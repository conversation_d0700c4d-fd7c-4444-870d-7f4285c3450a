package com.logistics.management.webapi.controller.biddingorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BiddingDemandDto {


    /**
     * 需求单id
     */
    @ApiModelProperty("需求单id")
    private String demandId = "";

    /**
     * 发货地址
     */
    @ApiModelProperty("发货地址")
    private String loadAddress = "";


    /**
     * 收货地址
     */
    @ApiModelProperty("收货地址")
    private String unloadAddress = "";


    /**
     * 需求单号
     */
    @ApiModelProperty("需求单号")
    private String demandOrderCode = "";


    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private String goodsCount = "";


    /**
     * 一口价
     */
    @ApiModelProperty("一口价")
    private String onePrice = "";

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private String unitPrice = "";





}
