package com.logistics.management.webapi.controller.settlestatement.tradition.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class CarrierTraditionStatementDetailListResponseDto {

    @ApiModelProperty("序号(导出结算使用)")
    @JsonIgnore
    private String serialNumber = "";

    @ApiModelProperty("对账单详情item id")
    private String settleStatementItemId = "";

    @ApiModelProperty("运单id")
    private String carrierOrderId = "";

    @ApiModelProperty("运单号")
    @ExcelProperty("运单号")
    private String carrierOrderCode = "";

    @ApiModelProperty("需求单id")
    private String demandOrderId = "";

    @ApiModelProperty("需求单号")
    @ExcelProperty("需求单号")
    private String demandOrderCode = "";

    @ApiModelProperty("调度单id")
    private String dispatchOrderId = "";

    @ApiModelProperty("调度单号")
    @ExcelProperty("调度单号")
    private String dispatchOrderCode = "";

    @ApiModelProperty("结算主体名")
    @ExcelProperty("结算主体")
    private String platformCompanyName = "";

    @ApiModelProperty("货物  多个货物/分割")
    @ExcelProperty("货物")
    private String goodsName = "";

    @ApiModelProperty("货物单位(导出结算使用)")
    @JsonIgnore
    private String goodsUnitLabel = "";

    @ApiModelProperty("车主id")
    private String companyCarrierId = "";

    @ApiModelProperty("车主  企业：企业名 个人：姓名+手机号 ")
    private String companyCarrierName = "";

    @ApiModelProperty("(导出) 车主  企业：企业名 个人：姓名+手机号")
    @ExcelProperty("车主")
    @JsonIgnore
    private String exportCompanyCarrierName = "";

    @ApiModelProperty("货主")
    @ExcelProperty("货主")
    private String companyEntrustName = "";

    @ApiModelProperty("车牌号")
    @ExcelProperty("车牌号")
    private String vehicleNo = "";

    @ApiModelProperty("司机(姓名 手机号)")
    private String driver = "";

    @ApiModelProperty("司机姓名(导出结算使用)")
    @JsonIgnore
    private String driverName = "";

    @ApiModelProperty("司机-导出使用")
    @ExcelProperty("司机")
    @JsonIgnore
    private String exportDriver = "";

    @ApiModelProperty("报价类型：1 单价  2 一口价")
    private String carrierPriceType = "";

    @ApiModelProperty("报价类型文本")
    @ExcelProperty("报价类型")
    private String carrierPriceTypeLabel = "";

    @ApiModelProperty("车主运费")
    @ExcelProperty("运费（元）")
    private String carrierFee = "";

    @ApiModelProperty("结算费用总额：运费")
    @ExcelProperty("结算费用总额（元）")
    private String settlementFee = "";

    @ApiModelProperty("预提数量")
    private String expectAmount = "";

    @ApiModelProperty("提货数量")
    @ExcelProperty("实提数量")
    private String loadAmount = "";

    @ApiModelProperty("卸货数量")
    @ExcelProperty("卸货数量")
    private String unloadAmount = "";

    @ApiModelProperty("结算数量")
    @ExcelProperty("结算数量")
    private String settlementAmount = "";

    @ApiModelProperty("结算数量不带单位(导出结算使用)")
    @JsonIgnore
    private String settlementAmountNoUnit = "";

    @ApiModelProperty("提货时间")
    @ExcelProperty("提货时间")
    private String loadTime = "";

    @ApiModelProperty("卸货时间")
    @ExcelProperty("卸货时间")
    private String unloadTime = "";

    @ApiModelProperty("签收时间")
    @ExcelProperty("签收时间")
    private String signTime = "";

    @ApiModelProperty("发货仓库")
    @ExcelProperty("发货仓库")
    private String loadWarehouse = "";

    @ApiModelProperty("发货详细地址")
    @ExcelProperty("发货详细地址")
    private String loadDetailAddress = "";

    @ApiModelProperty("提货地(导出结算使用)")
    @JsonIgnore
    private String loadAddress = "";

    @ApiModelProperty("收货仓库")
    @ExcelProperty("收货仓库")
    private String unloadWarehouse = "";

    @ApiModelProperty("收货详细地址")
    @ExcelProperty("收货详细地址")
    private String unloadDetailAddress = "";

    @ApiModelProperty("卸货地(导出结算使用)")
    @JsonIgnore
    private String unloadAddress = "";

    @ApiModelProperty("发货人")
    private String consignor = "";

    @ApiModelProperty("发货人-导出使用")
    @ExcelProperty("发货联系人")
    @JsonIgnore
    private String exportConsignor = "";

    @ApiModelProperty("收货人")
    private String receiver = "";

    @ApiModelProperty("收货人-导出使用")
    @ExcelProperty("收货联系人")
    @JsonIgnore
    private String exportReceiver = "";

    @ApiModelProperty("凭证数")
    @ExcelProperty("凭证数")
    private String ticketCount = "";

    @ApiModelProperty("调度人")
    @ExcelProperty("调度人")
    private String dispatchUserName = "";

    @ApiModelProperty("预计里程数")
    @ExcelProperty("预计里程数")
    private String expectMileage = "";
}
