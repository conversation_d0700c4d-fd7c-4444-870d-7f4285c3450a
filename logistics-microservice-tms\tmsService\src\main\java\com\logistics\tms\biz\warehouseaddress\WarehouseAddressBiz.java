package com.logistics.tms.biz.warehouseaddress;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.config.cache.CacheManager;
import com.logistics.tms.controller.warehouseaddress.request.*;
import com.logistics.tms.controller.warehouseaddress.response.SearchWarehouseAddressResponseModel;
import com.logistics.tms.controller.warehouseaddress.response.WarehouseAddressDetailResponseModel;
import com.logistics.tms.controller.warehouseaddress.response.WarehouseAddressListResponseModel;
import com.logistics.tms.entity.TWarehouseAddress;
import com.logistics.tms.mapper.TWarehouseAddressMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
public class WarehouseAddressBiz {

    @Resource
    private TWarehouseAddressMapper tWarehouseAddressMapper;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 列表
     * @param requestModel
     * @return
     */
    public PageInfo<WarehouseAddressListResponseModel> warehouseAddressList(WarehouseAddressListRequestModel requestModel) {
            requestModel.enablePaging();
            List<WarehouseAddressListResponseModel> warehouseAddressListResponseModels =  tWarehouseAddressMapper.warehouseAddressList(requestModel);
            return new PageInfo<>(warehouseAddressListResponseModels);
    }

    /**
     * 新增/编辑/删除
     * @param requestModel
     */
    @Transactional
    public void warehouseAddressAddOrModifyOrDel(AddWarehouseAddressRequestModel requestModel) {
        if(CommonConstant.INTEGER_ONE.equals(requestModel.getIfDelete())&&requestModel.getWarehouseAddressId()!=null){//删除
            deleteWarehouse(requestModel);
        }else if(requestModel.getWarehouseAddressId()==null){
            //check
            checkParams(requestModel);
            addWarehouse(requestModel);
        }else{
            //check
            checkParams(requestModel);
            updateWarehouse(requestModel);
        }
    }

    private void checkParams(AddWarehouseAddressRequestModel requestModel){
        if(StringUtils.isEmpty(requestModel.getWarehouse())){
            throw new BizException(CarrierDataExceptionEnum.WAREHOUSE_NAME_EMPTY);
        }
        if(requestModel.getProvinceId()==null||requestModel.getCityId()==null||requestModel.getAreaId()==null||StringUtils.isEmpty(requestModel.getProvinceName())||StringUtils.isEmpty(requestModel.getCityName())||StringUtils.isEmpty(requestModel.getAreaName())){
            throw new BizException(CarrierDataExceptionEnum.WAREHOUSE_ADDRESS_EMPTY);
        }

        if (requestModel.getCompanyEntrustId() == null || StringUtils.isEmpty(requestModel.getCompanyEntrustName())){
            throw new BizException(CarrierDataExceptionEnum.WAREHOUSE_ADDRESS_COMPANY_EMPTY);
        }
    }

  private void deleteWarehouse(AddWarehouseAddressRequestModel requestModel){
        TWarehouseAddress deleteWarehouse = new TWarehouseAddress();
        deleteWarehouse.setId(requestModel.getWarehouseAddressId());
        deleteWarehouse.setValid(CommonConstant.INTEGER_ZERO);
        commonBiz.setBaseEntityModify(deleteWarehouse, BaseContextHandler.getUserName());
        tWarehouseAddressMapper.updateByPrimaryKeySelective(deleteWarehouse);
    }
    private void addWarehouse(AddWarehouseAddressRequestModel requestModel){
        TWarehouseAddress tWarehouseAddress = tWarehouseAddressMapper.getWarehouseByName(requestModel.getCompanyEntrustId(), requestModel.getWarehouse().trim());
        if(tWarehouseAddress!=null){
            throw new BizException(CarrierDataExceptionEnum.WAREHOUSE_EXIST);
        }

        TWarehouseAddress insertAddress = new TWarehouseAddress();
        insertAddress.setProvinceId(requestModel.getProvinceId());
        insertAddress.setProvinceName(requestModel.getProvinceName());
        insertAddress.setCityId(requestModel.getCityId());
        insertAddress.setCityName(requestModel.getCityName());
        insertAddress.setAreaId(requestModel.getAreaId());
        insertAddress.setAreaName(requestModel.getAreaName());
        insertAddress.setWarehouse(requestModel.getWarehouse().trim());
        insertAddress.setCompanyEntrustId(requestModel.getCompanyEntrustId());
        insertAddress.setCompanyEntrustName(requestModel.getCompanyEntrustName());
        insertAddress.setEnabledTime(new Date());
        commonBiz.setBaseEntityAdd(insertAddress,BaseContextHandler.getUserName());
        tWarehouseAddressMapper.insertSelective(insertAddress);
    }
    private void updateWarehouse(AddWarehouseAddressRequestModel requestModel){
        TWarehouseAddress tWarehouseAddress = tWarehouseAddressMapper.getWarehouseByName(requestModel.getCompanyEntrustId(), requestModel.getWarehouse().trim());
        if (tWarehouseAddress == null){
            throw new BizException(CarrierDataExceptionEnum.WAREHOUSE_NOT_EXIST);
        }
        if(!tWarehouseAddress.getId().equals(requestModel.getWarehouseAddressId())){
            throw new BizException(CarrierDataExceptionEnum.WAREHOUSE_EXIST);
        }
        TWarehouseAddress upAddress = new TWarehouseAddress();
        upAddress.setId(requestModel.getWarehouseAddressId());
        upAddress.setProvinceId(requestModel.getProvinceId());
        upAddress.setProvinceName(requestModel.getProvinceName());
        upAddress.setCityId(requestModel.getCityId());
        upAddress.setCityName(requestModel.getCityName());
        upAddress.setAreaId(requestModel.getAreaId());
        upAddress.setAreaName(requestModel.getAreaName());
        upAddress.setWarehouse(requestModel.getWarehouse().trim());
        upAddress.setCompanyEntrustId(requestModel.getCompanyEntrustId());
        upAddress.setCompanyEntrustName(requestModel.getCompanyEntrustName());
        commonBiz.setBaseEntityModify(upAddress,BaseContextHandler.getUserName());
        tWarehouseAddressMapper.updateByPrimaryKeySelective(upAddress);

        //仓库启用中，修改了仓库名，删除缓存仓库
        if (tWarehouseAddress.getEnabled().equals(CommonConstant.INTEGER_ONE) && !tWarehouseAddress.getWarehouse().equals(upAddress.getWarehouse())){
            CacheManager.getInstance().removeExpiry(Arrays.asList(upAddress.getWarehouse()));
        }
    }

    /**
     * 详情
     * @param requestModel
     * @return
     */
    public WarehouseAddressDetailResponseModel warehouseAddressDetail(WarehouseAddressDetailRequestModel requestModel) {
        return tWarehouseAddressMapper.getDetail(requestModel.getWarehouseAddressId());
    }

    /**
     * 启用/禁用
     * @param requestModel
     */
    @Transactional
    public void enable(WarehouseAddressEnableRequestModel requestModel) {
        requestModel.setOperator(BaseContextHandler.getUserName());
        tWarehouseAddressMapper.enable(requestModel);
    }

    /**
     * 根据仓库名模糊搜索地址信息
     * @param requestModel
     * @return
     */
    public PageInfo<SearchWarehouseAddressResponseModel> searchWarehouseAddress(SearchWarehouseAddressRequestModel requestModel){
        requestModel.enablePaging();
        List<SearchWarehouseAddressResponseModel> list = tWarehouseAddressMapper.searchWarehouseAddress(requestModel);
        return new PageInfo<>(list);
    }

    /**
     * 定时任务：删除缓存的仓库
     */
    public void delCacheWarehouse(){
        List<String> warehouseList = tWarehouseAddressMapper.currentEnableWarehouse();
        CacheManager.getInstance().removeExpiry(warehouseList);
    }
}
