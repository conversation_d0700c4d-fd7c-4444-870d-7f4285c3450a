package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * @author: wjf
 * @date: 2019/6/13 15:28
 */
@Data
public class UpdateDriverFreightFeeRequestDto {
    @ApiModelProperty("运单Id")
    @NotBlank(message = "运单ID不能为空")
    private String carrierOrderId;
    @ApiModelProperty("司机运费")
    @Pattern(regexp = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,2})$)",message = "请填写司机运费，最多只能保留2位小数")
    private String driverFreightFee;
}
