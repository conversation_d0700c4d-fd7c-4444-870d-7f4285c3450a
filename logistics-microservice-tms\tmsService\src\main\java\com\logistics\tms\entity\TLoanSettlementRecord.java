package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TLoanSettlementRecord extends BaseEntity {
    /**
    * 贷款记录表id
    */
    @ApiModelProperty("贷款记录表id")
    private Long loanRecordsId;

    /**
    * 扣减月份
    */
    @ApiModelProperty("扣减月份")
    private String deductingMonth;

    /**
    * 结算日期
    */
    @ApiModelProperty("结算日期")
    private Date settlementDate;

    /**
    * 结算金额
    */
    @ApiModelProperty("结算金额")
    private BigDecimal settlementFee;

    /**
    * 剩余还款金额
    */
    @ApiModelProperty("剩余还款金额")
    private BigDecimal remainingRepaymentFee;

    /**
    * 总金额
    */
    @ApiModelProperty("总金额")
    private BigDecimal totalFee;
}