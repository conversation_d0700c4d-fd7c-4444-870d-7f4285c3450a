package com.logistics.tms.client;

import com.logistics.tms.client.feign.tray.order.customerinorder.CustomerInOrderServiceApi;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.request.GetCarrierOrderQRCodeRequestModel;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.request.GetCustomerInOrderStateRequestModel;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.request.RejectOrderLoadAmountRequestModel;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.response.GetCarrierOrderQRCodeResponseModel;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.response.GetCustomerInOrderStateResponseModel;
import com.logistics.tms.client.model.TmsLockReplenishDemandFeignRequest;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 云盘订单服务客户端
 */
@Service
public class TrayOrderServiceClient {

    @Resource
    private CustomerInOrderServiceApi customerInOrderServiceApi;

    /**
     * 批量获取运单入库状态
     *
     * @param carrierOrderList 运单号集合
     * @return 入库状态集合
     */
    public List<GetCustomerInOrderStateResponseModel> getCustomerStateByCarrierOrderCodes(List<String> carrierOrderList) {
        GetCustomerInOrderStateRequestModel requestModel = new GetCustomerInOrderStateRequestModel();
        requestModel.setCarrierOrderCodeList(carrierOrderList);
        Result<List<GetCustomerInOrderStateResponseModel>> result = customerInOrderServiceApi.getCustomerStateByCarrierOrderCodes(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 根据运单号查询二维码信息
     * @param carrierOrderCode
     * @return
     */
    public GetCarrierOrderQRCodeResponseModel getCarrierOrderQRCode(String carrierOrderCode){
        GetCarrierOrderQRCodeRequestModel requestModel = new GetCarrierOrderQRCodeRequestModel();
        requestModel.setCarrierOrderCode(carrierOrderCode);
        Result<GetCarrierOrderQRCodeResponseModel> result = customerInOrderServiceApi.getCarrierOrderQRCode(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 司机驳回提货数量
     */
    public void rejectOrderLoadAmount(RejectOrderLoadAmountRequestModel requestModel) {
        Result<Boolean> result = customerInOrderServiceApi.rejectOrderLoadAmount(requestModel);
        result.throwException();
    }

    /**
     * 司机驳回提货数量
     */
    public void tmsLockReplenishDemand(String demandCode) {
        TmsLockReplenishDemandFeignRequest tmsLockReplenishDemandFeignRequest = new TmsLockReplenishDemandFeignRequest();
        tmsLockReplenishDemandFeignRequest.setDemandCode(demandCode);
        Result<Void> result = customerInOrderServiceApi.tmsLockReplenishDemand(tmsLockReplenishDemandFeignRequest);
        result.throwException();
    }

}
