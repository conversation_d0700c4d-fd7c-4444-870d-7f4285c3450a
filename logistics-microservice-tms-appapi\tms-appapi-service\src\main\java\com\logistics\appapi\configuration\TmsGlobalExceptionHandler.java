package com.logistics.appapi.configuration;

import com.yelo.tray.core.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.tomcat.util.http.fileupload.FileUploadException;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartException;

@ResponseBody
@ControllerAdvice(basePackages = {"com.leyi", "com.logistics", "com.yelo"})
@Order(-1)
@Slf4j
public class TmsGlobalExceptionHandler {

    @ExceptionHandler({MultipartException.class})
    public Result<Void> multipartException(MultipartException ex) {
        log.info("图片上传异常:"+ ex.getMessage());
        return new Result<>(-19999, null, ex.getMessage());
    }


    @ExceptionHandler({FileUploadException.class})
    public Result<Void> fileUploadException(FileUploadException ex) {
        log.info("图片上传异常:"+ ex.getMessage());
        return new Result<>(-19999, null, ex.getMessage());
    }


}
