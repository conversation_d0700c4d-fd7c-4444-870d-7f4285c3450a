package com.logistics.appapi.controller.driversafemeeting.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/11/8 11:52
 */
@Data
public class SafeMeetingListRequestDto extends AbstractPageForm<SafeMeetingListRequestDto> {
    @ApiModelProperty("学习状态：空 全部，0未学习，1已学习")
    private String status;
}
