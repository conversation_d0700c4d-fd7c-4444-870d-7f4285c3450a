package com.logistics.tms.controller.attendance.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchAttendanceListRequestModel extends AbstractPageForm<SearchAttendanceListRequestModel> {

    //考勤用户
    @ApiModelProperty("考勤用户,姓名或手机号模糊搜索")
    private String attendanceUser;

    //人员机构
    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    //考勤查询开始时间
    @ApiModelProperty("考勤查询开始时间")
    private String attendanceStartTime;

    //考勤查询结束时间
    @ApiModelProperty("考勤查询结束时间")
    private String attendanceEndTime;

}
