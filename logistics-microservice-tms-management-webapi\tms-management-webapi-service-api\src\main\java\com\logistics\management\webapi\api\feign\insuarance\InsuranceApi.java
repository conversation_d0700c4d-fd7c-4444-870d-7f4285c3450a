package com.logistics.management.webapi.api.feign.insuarance;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.insuarance.dto.*;
import com.logistics.management.webapi.api.feign.insuarance.hystrix.InsuranceApiHystrix;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * @author: wjf
 * @date: 2019/6/4 17:38
 */
@Api(value = "API-InsuranceApi-保险管理")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = InsuranceApiHystrix.class)
public interface InsuranceApi {

    @ApiOperation(value = "保险管理列表v1.1.7")
    @PostMapping(value = "/api/insurance/searchInsuranceList")
    Result<PageInfo<SearchInsuranceListResponseDto>> searchInsuranceList(@RequestBody SearchInsuranceListRequestDto requestDto);

    @ApiOperation(value = "保险详情")
    @PostMapping(value = "/api/insurance/getInsuranceDetail")
    Result<GetInsuranceDetailResponseDto> getInsuranceDetail(@RequestBody @Valid InsuranceIdRequestDto requestDto);

    @ApiOperation(value = "新增/修改保险")
    @PostMapping(value = "/api/insurance/addOrModifyInsurance")
    Result addOrModifyInsurance(@RequestBody @Valid AddOrModifyInsuranceRequestDto requestDto);

    @ApiOperation(value = "作废保险")
    @PostMapping(value = "/api/insurance/cancelInsurance")
    Result cancelInsurance(@RequestBody @Valid CancelInsuranceRequestDto requestDto);

    @ApiOperation(value = "导出保险")
    @GetMapping(value = "/api/insurance/exportInsurance")
    void exportInsurance(SearchInsuranceListRequestDto requestDto, HttpServletResponse response);

    @ApiOperation(value = "导入保险")
    @PostMapping(value = "/api/insurance/importInsurance")
    Result<ImportInsuranceResponseDto> importInsurance(@RequestParam("file") MultipartFile file, HttpServletRequest request);

    @ApiOperation(value = "导入保险证件信息")
    @PostMapping(value = "/api/insurance/importInsuranceCertificateInfo")
    Result importInsuranceCertificateInfo(@RequestParam("file") MultipartFile file, HttpServletRequest request);

    @ApiOperation(value = "根据车辆id查询保险信息（车辆退保页面）")
    @PostMapping(value = "/api/insurance/getInsuranceInfoByVehicleId")
    Result<GetInsuranceInfoByVehicleIdResponseDto> getInsuranceInfoByVehicleId(@RequestBody @Valid GetInsuranceInfoByVehicleIdRequestDto requestDto);

    @ApiOperation(value = "确认退保")
    @PostMapping(value = "/api/insurance/confirmRefund")
    Result confirmRefund(@RequestBody @Valid ConfirmRefundRequestDto requestDto);
}
