package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2019/5/16 8:56
 */
@Data
public class DemandOrderGoodsBaseInfoResponseModel {
    private Long goodsId;
    @ApiModelProperty("货物品名")
    private String goodsName;
    @ApiModelProperty("规格")
    private String goodsSize;
    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;
    @ApiModelProperty("货物数量")
    private BigDecimal goodsAmount;
    private Integer length;
    private Integer width;
    private Integer height;
}
