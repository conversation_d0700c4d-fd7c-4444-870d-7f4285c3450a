package com.logistics.management.webapi.controller.warehouseaddress.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

@Data
public class AddWarehouseAddressRequestDto {

    @ApiModelProperty("ID")
    private String warehouseAddressId;
    @ApiModelProperty("仓库")
    @Size(min = 1,max = 20,message = "仓库名限制1-20个字符")
    private String warehouse ;
    @ApiModelProperty("省ID")
    private String provinceId;
    @ApiModelProperty("市ID")
    private String cityId;
    @ApiModelProperty("区ID")
    private String areaId;
    @ApiModelProperty("省")
    private String provinceName;
    @ApiModelProperty("市")
    private String cityName;
    @ApiModelProperty("区")
    private String areaName;
    @ApiModelProperty("删除标志位，1 删除")
    private String ifDelete;
    @ApiModelProperty("委托方公司id")
    private String companyEntrustId;
    @ApiModelProperty("委托方公司名称")
    private String companyEntrustName;

}
