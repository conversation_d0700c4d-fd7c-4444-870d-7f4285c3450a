package com.logistics.management.webapi.client.freightconfig;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.freightconfig.hystrix.CarrierFreightConfigHystrix;
import com.logistics.management.webapi.client.freightconfig.hystrix.ShippingFreigntRuleConfigHystrix;
import com.logistics.management.webapi.client.freightconfig.request.*;
import com.logistics.management.webapi.client.freightconfig.request.shipping.AddShippingFreightRuleReqModel;
import com.logistics.management.webapi.client.freightconfig.request.shipping.ListShippingFreightRuleConfigReqModel;
import com.logistics.management.webapi.client.freightconfig.response.GetConfigVechicleRespModel;
import com.logistics.management.webapi.client.freightconfig.response.shipping.ListShippingFreightRuleListRespModel;
import com.yelo.tray.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = FeignClientName.TMS_SERVICES, path = "/service/freight/rule", fallback = ShippingFreigntRuleConfigHystrix.class)
public interface ShippingFreightRuleConfigClient {


    /**
     * 配置运价规则列表  v2.42
     */
    @PostMapping(value = "/getList")
    public Result<PageInfo<ListShippingFreightRuleListRespModel>> getList(@RequestBody ListShippingFreightRuleConfigReqModel reqModel);


    /**
     * 配置运价规则列表导出 v2.42
     */
    @PostMapping(value = "/exportList")
    Result<List<ListShippingFreightRuleListRespModel>> exportList(@RequestBody ListShippingFreightRuleConfigReqModel reqModel);

    /**
     * 新增配置运价规则  v2.42
     */
    @PostMapping(value = "/add")
    public Result<Boolean> add(@RequestBody AddShippingFreightRuleReqModel reqModel);


    /**
     * 零担运价管理启用禁用 v2.42
     */
    @PostMapping(value = "/enableOrForbidOrDelete")
    public Result<Boolean> enableOrForbid(@RequestBody @Valid EnableOrForbidOrDeleteReqModel reqModel);

    /**
     * 配置车长运价/运价编辑 v2.42
     */
    @PostMapping(value = "/configVehicle")
    public Result<Boolean> configVehicle(@RequestBody @Valid List<ConfigVehicleReqModel> configVehicleReqModels);


    /**
     * 配置车长运价/运价编辑 v2.42
     */
    @PostMapping(value = "/editVehicle")
    public Result<Boolean> editVehicle(@RequestBody @Valid List<ConfigVehicleReqModel> configVehicleReqModels);

    /**
     * 车长查看 v2.42
     */
    @PostMapping(value = "/getConfigVehicle")
    public Result<GetConfigVechicleRespModel> getConfigVehicle(@RequestBody @Valid ShippingFreightRuleIdReqModel reqModel);


    /**
     * 配置串点运价/运价编辑 v2.42
     */
    @PostMapping(value = "/configStringPoint")
    public Result<Boolean> configStringPoint(@RequestBody @Valid List<ConfigStringPointReqModel> configVehicleReqModels);



}
