package com.logistics.management.webapi.base.enums;

/**
 * @author: wjf
 * @date: 2019/6/5 12:03
 */
public enum InsuranceCoverageEnum {
    DEFAULT(0,"","0"),
    COMMERCIAL_INSURANCE(1, "商业险","1"),
    COMPULSORY_INSURANCE(2, "交强险","2"),
    PERSONAL_INSURANCE(3, "个人意外险","3"),
    CARGO_INSURANCE(4, "货物险","4"),
    CARRIER_INSURANCE(5, "危货承运人险","5"),
    ;

    private Integer key;
    private String value;
    private String keyStr;

    InsuranceCoverageEnum(Integer key, String value, String keyStr) {
        this.key = key;
        this.value = value;
        this.keyStr = keyStr;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getKeyStr() {
        return keyStr;
    }

    public static InsuranceCoverageEnum getEnum(Integer key) {
        for (InsuranceCoverageEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return DEFAULT;
    }
}
