<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleBasicMapper">
    <resultMap id="getVehicleBasicDetailByCarrierVehicleIdMap" type="com.logistics.tms.controller.vehicleassetmanagement.response.VehicleAssetManagementDetailResponseModel">
        <id column="carrierVehicleId" property="carrierVehicleId" jdbcType="BIGINT"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="vehicleBasicId" property="vehicleBasicId" jdbcType="BIGINT"/>
        <result column="usage_property" property="usageProperty" jdbcType="INTEGER"/>
        <result column="if_install_gps" property="ifInstallGps" jdbcType="INTEGER"/>
        <result column="if_access_sinopec" property="ifAccessSinopec" jdbcType="INTEGER"/>
        <result column="vehicle_property" property="vehicleProperty" jdbcType="INTEGER"/>
        <result column="vehicle_owner" property="vehicleOwner" jdbcType="VARCHAR"/>
        <result column="connect_time" property="connectTime" jdbcType="TIMESTAMP"/>
        <result column="connect_way" property="connectWay" jdbcType="INTEGER"/>
        <result column="authentication_start_time" property="authenticationStartTime" jdbcType="TIMESTAMP"/>
        <result column="authentication_expire_time" property="authenticationExpireTime" jdbcType="TIMESTAMP"/>
        <result column="registration_certification_number" property="registrationCertificationNumber" jdbcType="VARCHAR"/>
        <result column="emission_standard_type" property="emissionStandardType" jdbcType="INTEGER"/>
        <result column="loading_capacity" property="loadingCapacity" jdbcType="INTEGER"/>
        <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="vehicle_type" property="vehicleType" jdbcType="VARCHAR"/>
        <result column="vehicle_type_id" property="vehicleTypeId" jdbcType="BIGINT"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="owner" property="owner" jdbcType="VARCHAR"/>
        <result column="brand" property="brand" jdbcType="VARCHAR"/>
        <result column="model" property="model" jdbcType="VARCHAR"/>
        <result column="vehicle_identification_number" property="vehicleIdentificationNumber" jdbcType="VARCHAR"/>
        <result column="engine_number" property="engineNumber" jdbcType="VARCHAR"/>
        <result column="certificationDep" property="certificationDepartment" jdbcType="VARCHAR"/>
        <result column="registration_date" property="registrationDate" jdbcType="TIMESTAMP"/>
        <result column="issueDa" property="issueDate" jdbcType="TIMESTAMP"/>
        <result column="filing_number" property="filingNumber" jdbcType="VARCHAR"/>
        <result column="authorized_carrying_capacity" property="authorizedCarryingCapacity" jdbcType="INTEGER"/>
        <result column="total_weight" jdbcType="DECIMAL" property="totalWeight" />
        <result column="curb_weight" jdbcType="DECIMAL" property="curbWeight" />
        <result column="traction_mass_weight"  property="tractionMassWeight" jdbcType="DECIMAL"  />
        <result column="approved_load_weight"  property="approvedLoadWeight" jdbcType="DECIMAL"  />
        <result column="length"  property="length" jdbcType="INTEGER" />
        <result column="width"  property="width"  jdbcType="INTEGER" />
        <result column="height"  property="height" jdbcType="INTEGER"  />
        <result column="obsolescence_date" property="obsolescenceDate" jdbcType="TIMESTAMP"  />
        <result column="axle_number" property="axleNumber" jdbcType="INTEGER"  />
        <result column="drive_shaft_number"  property="driveShaftNumber" jdbcType="INTEGER"  />
        <result column="tires_number" property="tiresNumber" jdbcType="INTEGER"  />
        <result column="plate_color"   property="plateColor" jdbcType="INTEGER" />
        <result column="body_color" property="bodyColor" jdbcType="VARCHAR"  />
        <result column="vehicleDrivingLicenseId" property="vehicleDrivingLicenseId" jdbcType="INTEGER"/>
        <result column="roadTransportId" property="vehicleRoadTransportCertificateId" jdbcType="INTEGER"  />
        <result column="certification_sign"  property="certificationSign" jdbcType="VARCHAR"  />
        <result column="business_license_number" property="businessLicenseNumber" jdbcType="VARCHAR"  />
        <result column="economic_type" property="economicType" jdbcType="VARCHAR"  />
        <result column="transport_tonnage"  property="transportTonnage" jdbcType="DECIMAL"  />
        <result column="business_scope" property="businessScope" jdbcType="VARCHAR"  />
        <result column="vrtcDepart"   property="roadCertificationDepartment" jdbcType="VARCHAR" />
        <result column="vrtcDate" property="roadIssueDate" jdbcType="TIMESTAMP"  />
        <result column="obtain_date"  property="obtainDate" jdbcType="TIMESTAMP"  />
        <result column="vrtcRemark" property="remark" jdbcType="VARCHAR"  />

        <collection property="otherDocumentsRecord" ofType="com.logistics.tms.controller.vehicleassetmanagement.response.CertificationPicturesRecordModel">
            <id column="fileId" property="fileId" />
            <result  column="file_type" property="fileType" />
            <result  column="object_type" property="objectType" />
            <result  column="absolute_file_path" property="absoluteFilePath" />
            <result  column="file_path" property="relativeFilepath" />
            <result  column="file_type_name" property="fileTypeName" />
            <result  column="cpBy" property="lastModifiedBy" />
            <result  column="cpTime" property="lastModifiedTime" />
        </collection>
    </resultMap>

    <select id="getVehicleBasicDetailByCarrierVehicleId" resultMap="getVehicleBasicDetailByCarrierVehicleIdMap">
        select
            tcdr.id as carrierVehicleId,
            tcdr.company_carrier_id,

            tqvb.id as vehicleBasicId,
            tqvb.vehicle_property ,
            tqvb.usage_property ,
            tqvb.if_install_gps ,
            tqvb.if_access_sinopec ,
            tqvb.vehicle_owner ,
            tqvb.connect_time ,
            tqvb.connect_way ,
            tqvb.authentication_start_time,
            tqvb.authentication_expire_time ,
            tqvb.registration_certification_number ,
            tqvb.emission_standard_type,
            tqvb.loading_capacity,


            tqvdl.id as vehicleDrivingLicenseId,
            tqvdl.vehicle_no ,
            tvt.vehicle_type,
            tqvdl.vehicle_type as vehicle_type_id,
            tqvdl.address,
            tqvdl.owner,
            tqvdl.brand,
            tqvdl.model,
            tqvdl.vehicle_identification_number,
            tqvdl.engine_number,
            tqvdl.certification_department as certificationDep,
            tqvdl.registration_date ,
            tqvdl.issue_date as issueDa,
            tqvdl.filing_number ,
            tqvdl.authorized_carrying_capacity ,
            tqvdl.total_weight ,
            tqvdl.curb_weight ,
            tqvdl.traction_mass_weight ,
            tqvdl.approved_load_weight ,
            tqvdl.length,
            tqvdl.width,
            tqvdl.height,
            tqvdl.obsolescence_date ,
            tqvdl.axle_number ,
            tqvdl.drive_shaft_number,
            tqvdl.tires_number ,
            tqvdl.plate_color ,
            tqvdl.body_color ,

            tqvrtc.id as roadTransportId,
            tqvrtc.certification_sign,
            tqvrtc.business_license_number,
            tqvrtc.economic_type,
            tqvrtc.transport_tonnage,
            tqvrtc.business_scope,
            tqvrtc.certification_department as vrtcDepart,
            tqvrtc.issue_date as vrtcDate,
            tqvrtc.obtain_date,
            tqvrtc.remark as vrtcRemark,

            tqcp.id as fileId,
            tqcp.file_type,
            tqcp.object_type,
            tqcp.file_type_name ,
            tqcp.last_modified_by as cpBy,
            tqcp.last_modified_time as cpTime,
            tqcp.file_path
        FROM t_carrier_vehicle_relation tcdr
        left join t_vehicle_basic tqvb on tqvb.id = tcdr.vehicle_id and tqvb.valid = 1
        LEFT JOIN  t_vehicle_driving_license  tqvdl on tqvb.id=tqvdl.vehicle_id and tqvdl.valid=1
        left join t_vehicle_type tvt on tvt.id  = tqvdl.vehicle_type and tvt.valid = 1
	    LEFT JOIN  t_vehicle_road_transport_certificate tqvrtc on tqvrtc.vehicle_id=tqvb.id and tqvrtc.valid=1
	    LEFT JOIN  t_certification_pictures  tqcp  on ((tqcp.object_type = 13 and tqcp.object_id = tqvdl.id)  or (tqcp.object_type = 14 and tqcp.object_id = tqvrtc.id) or (tqcp.object_type = 15 and tqcp.object_id = tqvb.id)) and tqcp.valid=1
        where tqvb.valid=1 and tcdr.id = #{carrierVehicleId,jdbcType=BIGINT}
    </select>

    <select id="getVehicleRelationInfoByVehicleNo" resultType="com.logistics.tms.biz.vehicleassetmanagement.model.VehicleRelationInfoModel">
        select
        tqvd.vehicle_no as vehicleNo,
        tqvb.id         as vehicleId,
        tqvd.id         as vehicleDrivingLicenseId,
        tqrd.id         as vehicleRoadTransportCertificateId
        from t_vehicle_basic tqvb
        left join t_vehicle_driving_license tqvd on tqvd.vehicle_id = tqvb.id and tqvd.valid = 1
        left join t_vehicle_road_transport_certificate tqrd on tqrd.vehicle_id = tqvb.id and tqrd.valid = 1
        where tqvb.valid = 1
          and tqvd.vehicle_no = #{vehicleNo,jdbcType = VARCHAR}
    </select>

    <select id="getVehicleRelationInfoByVehicleIds" resultType="com.logistics.tms.biz.vehicleassetmanagement.model.VehicleRelationInfoModel">
        select
        tqvd.vehicle_no as vehicleNo,
        tqvb.id         as vehicleId,
        tqvd.id         as vehicleDrivingLicenseId,
        tqrd.id         as vehicleRoadTransportCertificateId
        from t_vehicle_basic tqvb
        left join t_vehicle_driving_license tqvd on tqvd.vehicle_id = tqvb.id and tqvd.valid = 1
        left join t_vehicle_road_transport_certificate tqrd on tqrd.vehicle_id = tqvb.id and tqrd.valid = 1
        where tqvb.valid = 1
        <choose>
            <when test="vehicleIds != null and vehicleIds.size() != 0">
                and  tqvb.id in
                <foreach collection="vehicleIds" open="(" separator="," close=")" item="item">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </select>

    <resultMap id="vehicleBasicMap" type="com.logistics.tms.controller.vehicleassetmanagement.response.VehicleAssetManagementListResponseModel">
        <id column="id" property="vehicleBasicId" jdbcType="BIGINT"/>
        <result column="usage_property" property="usageProperty" jdbcType="INTEGER"/>
        <result column="vehicle_property" property="vehicleProperty" jdbcType="INTEGER"/>
        <result column="vehicle_type" property="vehicleType" jdbcType="VARCHAR"/>
        <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="vehicle_identification_number" property="vehicleIdentificationNumber" jdbcType="VARCHAR"/>
        <result column="engine_number" property="engineNumber" jdbcType="VARCHAR"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR"/>
        <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP"/>
        <result column="operating_state" property="operatingState" jdbcType="INTEGER" />
        <result column="outageInfo" property="outageInfo" jdbcType="VARCHAR"/>
        <result column="approved_load_weight" property="approvedLoadWeight" jdbcType="DECIMAL"/>
        <result column="total_weight" property="totalWeight" jdbcType="DECIMAL"/>
        <result column="vehicle_category" property="vehicleCategory" jdbcType="INTEGER"/>
        <result column="loading_capacity" property="loadingCapacity" jdbcType="INTEGER"/>
    </resultMap>

    <select id="getVehicleBasicByIds" resultMap="vehicleBasicMap" >
        SELECT
        tqvb.id,
        tqvb.usage_property,
        tqvb.vehicle_property,
        tqvb.created_by,
        tqvb.last_modified_by,
        tqvb.last_modified_time,
        tqvb.operating_state,
        tqvb.shut_down_reason as outageInfo,
        tqvb.loading_capacity,

        tqvdl.vehicle_no,
        tqvdl.vehicle_identification_number,
        tqvdl.engine_number,
        tqvdl.approved_load_weight,
        tqvdl.total_weight,

        tvt.vehicle_type,
        tvt.vehicle_category
        FROM t_vehicle_basic tqvb
        LEFT JOIN t_vehicle_driving_license tqvdl ON tqvb.id = tqvdl.vehicle_id and tqvdl.valid = 1
        left join t_vehicle_type tvt on tvt.id = tqvdl.vehicle_type and tvt.valid = 1
        where tqvb.valid = 1
          and tqvb.id in (${ids})
        group by tqvb.id
        order by tqvb.last_modified_time desc, tqvb.id desc
  </select>

    <select id="getVehicleBasicByVehicleNos" resultMap="vehicleBasicMap">
        SELECT
        tqvb.id,
        tqvb.usage_property,
        tqvb.vehicle_property,
        tqvb.created_by,
        tqvb.last_modified_by,
        tqvb.last_modified_time,
        tqvb.operating_state,
        tqvb.shut_down_reason as outageInfo,
        tqvb.loading_capacity,

        tqvdl.vehicle_no,
        tqvdl.vehicle_identification_number,
        tqvdl.engine_number,
        tqvdl.approved_load_weight,
        tqvdl.total_weight,

        tvt.vehicle_type,
        tvt.vehicle_category
        FROM t_vehicle_basic tqvb
        LEFT JOIN t_vehicle_driving_license tqvdl ON tqvb.id = tqvdl.vehicle_id and tqvdl.valid = 1
        left join t_vehicle_type tvt on tvt.id = tqvdl.vehicle_type and tvt.valid = 1
        where tqvb.valid = 1
        and tqvdl.vehicle_no in (${vehicleNos})
        group by tqvb.id
        order by tqvb.last_modified_time desc, tqvb.id desc
    </select>

    <select id="getVehicleLicenseComplete" resultType="com.logistics.tms.biz.vehicleassetmanagement.model.GetVehicleIfCompleteByIdsModel" >
        SELECT
        tqvb.id as vehicleId,
        if(count(t1.id)>0,1,0) as ifComplete
        FROM t_vehicle_basic tqvb
        LEFT JOIN t_vehicle_driving_license tqvdl ON tqvb.id=tqvdl.vehicle_id and tqvdl.valid=1
        left join t_vehicle_driving_license_annual_review t1 on t1.driving_license_id = tqvdl.id and t1.valid = 1
        where tqvb.valid = 1
        and tqvb.id in (${ids})
        group by tqvb.id
    </select>
    <select id="getVehicleTransportCertificateComplete" resultType="com.logistics.tms.biz.vehicleassetmanagement.model.GetVehicleIfCompleteByIdsModel" >
        SELECT
        tqvb.id  as vehicleId,
        if(count(t2.id)>0,1,0) as ifComplete
        FROM t_vehicle_basic tqvb
        left join t_vehicle_road_transport_certificate tqrtc on  tqrtc.vehicle_id = tqvb.id and tqrtc.valid = 1
        left join t_vehicle_road_transport_certificate_annual_review t2 on t2.road_transport_cetification_id = tqrtc.id and t2.valid = 1
        where tqvb.valid = 1
        and tqvb.id in (${ids})
        group by tqvb.id
    </select>
    <select id="getVehicleGradeEstimationComplete" resultType="com.logistics.tms.biz.vehicleassetmanagement.model.GetVehicleIfCompleteByIdsModel" >
        SELECT
        tqvb.id as vehicleId,
        if(count(t3.id)>0,1,0) as ifComplete
        FROM t_vehicle_basic tqvb
        left join t_vehicle_grade_estimation_record t3 on t3.vehicle_id = tqvb.id and t3.valid = 1
        where tqvb.valid = 1
        and tqvb.id in (${ids})
        group by tqvb.id
    </select>

    <select id="getVehicleStatics" resultType="com.logistics.tms.controller.vehicleassetmanagement.response.AssetsBoardResponseModel">
        SELECT
        ifnull(sum(if(tqvb.vehicle_property = 1 and tqvt.vehicle_category = 1, 1, 0)), 0)                                            as tractorCount,           -- 牵引车
        ifnull(sum(if(tqvb.vehicle_property = 1 and tqvt.vehicle_category = 2, 1, 0)), 0)                                            as trailerCount,           -- 挂车
        ifnull(sum(if(tqvb.vehicle_property = 1 and tqvt.vehicle_category = 3, 1, 0)), 0)                                            as oneVehicleCount,        -- 一体车
        ifnull(sum(if(tqvb.usage_property = 2 and tqvt.vehicle_category = 1, 1, 0)), 0)                                              as dangerousCarriageCount, -- 危货运输
        ifnull(sum(if(tqvb.usage_property = 1 and tqvt.vehicle_category = 1, 1, 0)), 0)                                              as commonCarriageCount,    -- 普货运输
        ifnull(sum(if(tqvb.vehicle_property = 1 and tqvt.vehicle_category in (1, 3), 1, 0)), 0)                                      as ownVehicleCount,        -- 自有车辆
        ifnull(sum(if(tqvb.vehicle_property = 2 and tqvt.vehicle_category in (1, 3), 1, 0)), 0)                                      as outVehicleCount,        -- 外部车辆
        ifnull(sum(if(tqvb.vehicle_property = 1 and tqvt.vehicle_category in (1, 3) and tqvb.emission_standard_type != 0, 1, 0)), 0) as vehicleTotalCount,      -- 排放标准车辆总数量
        ifnull(sum(if(tqvb.vehicle_property = 1 and tqvt.vehicle_category in (1, 3) and tqvb.emission_standard_type = 1, 1, 0)), 0)  as guoYiCount,             -- 排放标准国一车辆数量
        ifnull(sum(if(tqvb.vehicle_property = 1 and tqvt.vehicle_category in (1, 3) and tqvb.emission_standard_type = 2, 1, 0)), 0)  as guoErCount,             -- 排放标准国二车辆数量
        ifnull(sum(if(tqvb.vehicle_property = 1 and tqvt.vehicle_category in (1, 3) and tqvb.emission_standard_type = 3, 1, 0)), 0)  as guoSanCount,            -- 排放标准国三车辆数量
        ifnull(sum(if(tqvb.vehicle_property = 1 and tqvt.vehicle_category in (1, 3) and tqvb.emission_standard_type = 4, 1, 0)), 0)  as guoSiCount,             -- 排放标准国四车辆数量
        ifnull(sum(if(tqvb.vehicle_property = 1 and tqvt.vehicle_category in (1, 3) and tqvb.emission_standard_type = 5, 1, 0)), 0)  as guoWuCount,             -- 排放标准国五车辆数量
        ifnull(sum(if(tqvb.vehicle_property = 1 and tqvt.vehicle_category in (1, 3) and tqvb.emission_standard_type = 6, 1, 0)), 0)  as guoLiuCount,            -- 排放标准国六车辆数量
        ifnull(sum(if(tqvb.vehicle_property = 1 and tqvt.vehicle_category in (2, 3), tqvdi.approved_load_weight, 0)), 0)             as approvedLoadWeight,     -- 核定载质量
        ifnull(sum(if(tqvb.vehicle_property = 1 and tqvt.vehicle_category in (2, 3), tqvdi.total_weight, 0)), 0)                     as totalWeight             -- 总质量
        from t_carrier_vehicle_relation tcvr
        left join t_vehicle_basic tqvb on tqvb.valid =1 and tcvr.vehicle_id = tqvb.id
        left join t_vehicle_driving_license tqvdi on tqvdi.vehicle_id = tqvb.id and tqvdi.valid = 1
        left join t_vehicle_type tqvt on tqvt.id = tqvdi.vehicle_type and tqvt.valid = 1
        where tcvr.valid = 1
        and tcvr.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        and tqvb.operating_state = 1
  </select>

    <select id="getVehicleBasicModelByIds" resultMap="vehicleBasicModelMap">
        select
        vb.id               as vehicleBasicId,
        vb.vehicle_property as vehicleProperty,
        vgr.id              as vehicleGpsRecordId,
        vger.id             as vehicleGradeEstimationRecordId
        from t_vehicle_basic vb
        left join t_vehicle_gps_record vgr on vgr.vehicle_id = vb.id and vgr.valid = 1
        left join t_vehicle_grade_estimation_record vger on vger.vehicle_id = vb.id and vger.valid = 1
        where vb.valid = 1
        <if test="vehicleBasicIds != null">
            and vb.id in (${vehicleBasicIds})
        </if>
    </select>

    <select id="getVehicleDrivingLicenseModelByIds" resultMap="vehicleDrivingLicenseModelMap">
        select
        vb.id as vehicleBasicId,
        vdl.id as vehicleDrivingLicenseId,
        vdl.vehicle_no,
        vdlar.id as vehicleDrivingLicenseAnnualReviewId
        from t_vehicle_basic vb
        left join t_vehicle_driving_license vdl on vdl.vehicle_id = vb.id and vdl.valid =1
        left join t_vehicle_driving_license_annual_review vdlar on vdlar.driving_license_id = vdl.id and vdlar.valid = 1
        where vb.valid = 1
        <if test="vehicleBasicIds!=null">
            and vb.id in (${vehicleBasicIds})
        </if>
    </select>
    <select id="getVehicleRoadTransportCertificateModelByIds" resultMap="vehicleRoadTransportCertificateModelMap">
        select
        vb.id as vehicleBasicId,
        vrtc.id as vehicleRoadTransportCertificateId,
        vrtcar.id as vehicleRoadTransportCertificateAnnualReviewId
        from t_vehicle_basic vb
        left join t_vehicle_road_transport_certificate vrtc on vrtc.vehicle_id = vb.id and vrtc.valid = 1
        left join t_vehicle_road_transport_certificate_annual_review vrtcar on vrtcar.road_transport_cetification_id = vrtc.id and vrtcar.valid = 1
        where vb.valid = 1
        <if test="vehicleBasicIds!=null">
            and vb.id in (${vehicleBasicIds})
        </if>
    </select>
    <select id="getVehicleBasicModelById" resultMap="vehicleBasicModelMap">
        select
        vb.id as vehicleBasicId,
        vb.vehicle_property as vehicleProperty,
        vgr.id as vehicleGpsRecordId,
        vger.id as vehicleGradeEstimationRecordId
        from t_vehicle_basic vb
        left join t_vehicle_gps_record vgr on vgr.vehicle_id = vb.id and vgr.valid = 1
        left join t_vehicle_grade_estimation_record vger on vger.vehicle_id = vb.id and vger.valid = 1
        where vb.valid = 1
        <if test="vehicleBasicId!=null">
            and vb.id = #{vehicleBasicId,jdbcType = BIGINT}
        </if>
    </select>

    <resultMap id="vehicleBasicModelMap" type="com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicModel">
        <id column="vehicleBasicId" property="vehicleBasicId" jdbcType="BIGINT"/>
        <result column="vehicleProperty" property="vehicleProperty" jdbcType="INTEGER" />
        <result column="basicVehicleNo" property="basicVehicleNo" jdbcType="VARCHAR"/>
        <collection property="vehicleGpsRecordModelList" ofType="com.logistics.tms.biz.vehicleassetmanagement.model.VehicleGpsRecordModel">
            <id column="vehicleGpsRecordId" property="vehicleGpsRecordId" jdbcType="BIGINT"/>
        </collection>
        <collection property="vehicleGradeEstimationRecordModelList" ofType="com.logistics.tms.biz.vehicleassetmanagement.model.VehicleGradeEstimationRecordModel">
            <id column="vehicleGradeEstimationRecordId" property="vehicleGradeEstimationRecordId" jdbcType="BIGINT"/>
        </collection>
    </resultMap>
    <resultMap id="vehicleDrivingLicenseModelMap" type="com.logistics.tms.biz.vehicleassetmanagement.model.VehicleDrivingLicenseModel">
        <id column="vehicleDrivingLicenseId" property="vehicleDrivingLicenseId" jdbcType="BIGINT"/>
        <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="vehicleBasicId" property="vehicleBasicId" jdbcType="BIGINT"/>
        <collection property="vehicleDrivingLicenseAnnualReviewList" ofType="com.logistics.tms.biz.vehicleassetmanagement.model.VehicleDrivingLicenseAnnualReviewModel">
            <id column="vehicleDrivingLicenseAnnualReviewId" property="vehicleDrivingLicenseAnnualReviewId" jdbcType="BIGINT"/>
        </collection>
    </resultMap>
    <resultMap id="vehicleRoadTransportCertificateModelMap" type="com.logistics.tms.biz.vehicleassetmanagement.model.VehicleRoadTransportCertificateModel">
        <id column="vehicleRoadTransportCertificateId" property="vehicleRoadTransportCertificateId" jdbcType="BIGINT"/>
        <result column="vehicleBasicId" property="vehicleBasicId" jdbcType="BIGINT"/>
        <collection property="vehicleRoadTransportCertificateAnnualReviewList" ofType="com.logistics.tms.biz.vehicleassetmanagement.model.VehicleRoadTransportCertificateAnnualReviewModel">
            <id column="vehicleRoadTransportCertificateAnnualReviewId" property="vehicleRoadTransportCertificateAnnualReviewId" jdbcType="BIGINT"/>
        </collection>
    </resultMap>
    <select id="getVehicleDrivingLicenseModelById" resultMap="vehicleDrivingLicenseModelMap">
        select
        vdl.id as vehicleDrivingLicenseId,
        vdl.vehicle_no,
        vdlar.id as vehicleDrivingLicenseAnnualReviewId
        from t_vehicle_basic vb
        left join t_vehicle_driving_license vdl on vdl.vehicle_id = vb.id and vdl.valid =1
        left join t_vehicle_driving_license_annual_review vdlar on vdlar.driving_license_id = vdl.id and vdlar.valid = 1
        where vb.valid = 1
        <if test="vehicleBasicId!=null">
            and vb.id = #{vehicleBasicId,jdbcType = BIGINT}
        </if>
    </select>
    <select id="getVehicleRoadTransportCertificateModelById" resultMap="vehicleRoadTransportCertificateModelMap">
        select
        vrtc.id as vehicleRoadTransportCertificateId,
        vrtcar.id as vehicleRoadTransportCertificateAnnualReviewId
        from t_vehicle_basic vb
        left join t_vehicle_road_transport_certificate vrtc on vrtc.vehicle_id = vb.id and vrtc.valid = 1
        left join t_vehicle_road_transport_certificate_annual_review vrtcar on vrtcar.road_transport_cetification_id = vrtc.id and vrtcar.valid = 1
        where vb.valid = 1
        <if test="vehicleBasicId!=null">
            and vb.id = #{vehicleBasicId,jdbcType = BIGINT}
        </if>
    </select>

    <update id="updateByPrimaryKeySelectiveExt" parameterType="com.logistics.tms.entity.TVehicleBasic">
        update t_vehicle_basic
        <set>
            usage_property = #{params.usageProperty,jdbcType=INTEGER},
            if_install_gps = #{params.ifInstallGps,jdbcType=INTEGER},
            if_access_sinopec = #{params.ifAccessSinopec,jdbcType=INTEGER},
            vehicle_property = #{params.vehicleProperty,jdbcType=INTEGER},
            vehicle_owner = #{params.vehicleOwner,jdbcType=VARCHAR},
            connect_time = #{params.connectTime,jdbcType=TIMESTAMP},
            connect_way = #{params.connectWay,jdbcType=INTEGER},
            authentication_start_time = #{params.authenticationStartTime,jdbcType=TIMESTAMP},
            authentication_expire_time = #{params.authenticationExpireTime,jdbcType=TIMESTAMP},
            registration_certification_number = #{params.registrationCertificationNumber,jdbcType=VARCHAR},
            <if test="params.loadingCapacity!=null">
                loading_capacity = #{params.loadingCapacity,jdbcType=INTEGER},
            </if>
            <if test="params.emissionStandardType!=null">
                emission_standard_type = #{params.emissionStandardType,jdbcType=INTEGER},
            </if>
            <if test="params.source != null">
                source = #{params.source,jdbcType=INTEGER},
            </if>
            <if test="params.createdBy != null">
                created_by = #{params.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="params.createdTime != null">
                created_time = #{params.createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="params.lastModifiedBy != null">
                last_modified_by = #{params.lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="params.lastModifiedTime != null">
                last_modified_time = #{params.lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="params.valid != null">
                valid = #{params.valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{params.id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKeySelectiveExtTwo" parameterType="com.logistics.tms.entity.TVehicleBasic">
        update t_vehicle_basic
        <set>
            usage_property = #{params.usageProperty,jdbcType=INTEGER},
            if_install_gps = #{params.ifInstallGps,jdbcType=INTEGER},
            if_access_sinopec = #{params.ifAccessSinopec,jdbcType=INTEGER},
            vehicle_property = #{params.vehicleProperty,jdbcType=INTEGER},
            vehicle_owner = #{params.vehicleOwner,jdbcType=VARCHAR},
            connect_time = #{params.connectTime,jdbcType=TIMESTAMP},
            connect_way = #{params.connectWay,jdbcType=INTEGER},
            authentication_start_time = #{params.authenticationStartTime,jdbcType=TIMESTAMP},
            authentication_expire_time = #{params.authenticationExpireTime,jdbcType=TIMESTAMP},
            registration_certification_number = #{params.registrationCertificationNumber,jdbcType=VARCHAR},
            loading_capacity = #{params.loadingCapacity,jdbcType=INTEGER},
            <if test="params.emissionStandardType!=null">
                emission_standard_type = #{params.emissionStandardType,jdbcType=INTEGER},
            </if>
            <if test="params.source != null">
                source = #{params.source,jdbcType=INTEGER},
            </if>
            <if test="params.createdBy != null">
                created_by = #{params.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="params.createdTime != null">
                created_time = #{params.createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="params.lastModifiedBy != null">
                last_modified_by = #{params.lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="params.lastModifiedTime != null">
                last_modified_time = #{params.lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="params.valid != null">
                valid = #{params.valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{params.id,jdbcType=BIGINT}
    </update>

    <select id="getInfoByVehicleNo" resultMap="BaseResultMap">
        select
        tvb.*
        from t_vehicle_basic tvb
        left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = tvb.id and tvdl.valid = 1
        where tvb.valid = 1
        and tvdl.vehicle_no = #{vehicleNo,jdbcType = VARCHAR}
    </select>

    <update id="deleteVehicleBasicInfo" >
        update t_vehicle_basic t1
        left join t_vehicle_gps_record t2 on t1.id = t2.vehicle_id and t2.valid = 1
        left join t_vehicle_grade_estimation_record t3 on t1.id = t3.vehicle_id and t3.valid = 1
        left join t_certification_pictures t4 on ((t1.id = t4.object_id and t4.object_type = 15) or (t2.id = t4.object_id and t4.object_type = 10) or (t3.id = t4.object_id and t4.object_type = 11)) and t4.valid = 1
        set t1.valid = 0,t2.valid = 0,t3.valid = 0,t4.valid = 0,
        t1.last_modified_by = #{modifiedBy,jdbcType=VARCHAR},t1.last_modified_time = #{modifiedTime,jdbcType=TIMESTAMP},
        t2.last_modified_by = #{modifiedBy,jdbcType=VARCHAR},t2.last_modified_time = #{modifiedTime,jdbcType=TIMESTAMP},
        t3.last_modified_by = #{modifiedBy,jdbcType=VARCHAR},t3.last_modified_time = #{modifiedTime,jdbcType=TIMESTAMP},
        t4.last_modified_by = #{modifiedBy,jdbcType=VARCHAR},t4.last_modified_time = #{modifiedTime,jdbcType=TIMESTAMP}
        where t1.valid = 1 and t1.id in (${vehicleIds})
    </update>

    <select id="fuzzyQueryVehicleInfoByVehicleNo" resultType="com.logistics.tms.controller.vehicleassetmanagement.response.FuzzyQueryVehicleInfoResponseModel">
        select
        tvt.vehicle_category as vehicleCategory,
        tqvb.vehicle_property as vehicleProperty,
        tqvb.id as vehicleId,
        tqvd.vehicle_no as vehicleNo
        from t_vehicle_basic tqvb
        left join t_vehicle_driving_license tqvd on tqvd.vehicle_id = tqvb.id and tqvd.valid =1
        left join t_vehicle_type tvt on tqvd.vehicle_type = tvt.id and tvt.valid = 1
        where 1=1
        and tqvb.valid = 1
        and tqvb.operating_state = 1
        <if test="vehicleNo!=null and vehicleNo!=''">
            and instr(tqvd.vehicle_no,#{vehicleNo,jdbcType = VARCHAR })>0
        </if>
    </select>

    <select id="queryVehicleInfoByVehicleNos" resultType="com.logistics.tms.controller.vehicleassetmanagement.response.FuzzyQueryVehicleInfoResponseModel">
        select
        tqvb.id               as vehicleId,
        tqvb.vehicle_property as vehicleProperty,
        tqvb.loading_capacity as loadingCapacity,

        tqvd.id               as drivingLicenseId,
        tqvd.vehicle_no       as vehicleNo,
        tqvd.vehicle_type     as vehicleType,
        tqvb.operating_state  as operatingState
        from t_vehicle_basic tqvb
        left join t_vehicle_driving_license tqvd on tqvd.vehicle_id = tqvb.id and tqvd.valid = 1
        where tqvb.valid = 1
        and tqvd.vehicle_no in (${vehicleNos})
    </select>

    <select id="fuzzyQueryVehicleInfo" resultType="com.logistics.tms.controller.vehicleassetmanagement.response.FuzzyQueryVehicleInfoResponseModel">
        select distinct
        tqvb.id                            as vehicleId,
        tqvd.vehicle_no                    as vehicleNo,
        tqvd.brand                         as brand,
        tqvd.model                         as model,
        tqvd.vehicle_identification_number as vehicleIdentificationNumber,
        tqvd.engine_number                 as engineNumber,
        tqvd.body_color                    as bodyColor
        from t_carrier_vehicle_relation tcvr
        left join t_vehicle_basic tqvb on tqvb.valid = 1 and tqvb.id = tcvr.vehicle_id
        left join t_vehicle_driving_license tqvd on tqvd.vehicle_id = tqvb.id and tqvd.valid = 1
        left join t_vehicle_type tqvt on tqvt.id = tqvd.vehicle_type and tqvt.valid = 1

        <if test="params.source != null and params.source == 1">
            left join t_loan_records tlr on tlr.vehicle_no = tqvd.vehicle_no and tlr.valid = 1
        </if>

        where tcvr.valid = 1
        and tcvr.company_carrier_id = #{params.companyCarrierId,jdbcType=BIGINT}
        <if test="param1.operatingState != null">
            and tqvb.operating_state = #{params.operatingState,jdbcType=INTEGER}
        </if>
        <if test="params.type != null and params.type != ''">
            and tqvb.vehicle_property in (${params.type})
        </if>
        <if test="params.fuzzyVehicleField != null and params.fuzzyVehicleField != ''">
            and instr(tqvd.vehicle_no, #{params.fuzzyVehicleField,jdbcType = VARCHAR }) > 0
        </if>
        <if test="params.vehicleCategory != null and params.vehicleCategory != ''">
            and tqvt.vehicle_category in (${params.vehicleCategory})
        </if>
        <if test="params.source != null and params.source == 1">
            and tlr.id is null <!-- 查询车辆不存在贷款记录 -->
        </if>
        <if test="params.source != null and params.source == 2 and params.notInVehicleIds != null and params.notInVehicleIds.size() != 0">
            and  tqvb.id not in
            <foreach collection="params.notInVehicleIds" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="getExportGradeEstimationByVehicleIds" resultType="com.logistics.tms.biz.vehicleassetmanagement.model.ExportGradeEstimationModel">
      select
	    vger.vehicle_id as vehicleId,
        max(vger.estimation_date) as estimationDate
      from t_vehicle_grade_estimation_record vger
      where vger.valid = 1
      and vger.vehicle_id in (${vehicleIds})
      group by vger.vehicle_id;
    </select>

    <select id="getExportDrivingLicenseReviewByDrivingIds" resultType="com.logistics.tms.biz.vehicleassetmanagement.model.ExportDrivingLicenseReviewModel">
      select
        drev.driving_license_id as drivingLicenseId,
        max(drev.check_valid_date) as checkValidDate
      from t_vehicle_driving_license_annual_review drev
      where drev.valid =1
      and drev.driving_license_id in (${drivingLicenseIds})
      group by drev.driving_license_id;
    </select>

    <select id="getExportRoadAnnualByRoadIds" resultType="com.logistics.tms.biz.vehicleassetmanagement.model.ExportRoadAnnualModel">
      select
	    crev.road_transport_cetification_id as vehicleRoadId,
	    max(crev.check_valid_date) as checkValidDate
      from t_vehicle_road_transport_certificate_annual_review  crev
      where crev.valid = 1
      and crev.road_transport_cetification_id in (${roadIds})
      group by crev.road_transport_cetification_id;
    </select>

    <select id="findNewlyInstallTimeGpsRecord" resultType="com.logistics.tms.biz.vehicleassetmanagement.model.ExportGpsRecordModel">
        select
        ret.vehicleId,
        ret.simNumber,
        ret.installTime,
        ret.terminalType,
        ret.gpsServiceProvider
        from (
        select
        a.vehicle_id as  vehicleId,
        a.sim_number as simNumber,
        a.install_time as installTime,
        a.terminal_type as terminalType,
        a.gps_service_provider as gpsServiceProvider,
        (@i:=case when @key_i = a.vehicle_id then @i+1 else 1 end) as sortNum,
        (@key_i:=a.vehicle_id) as temp
        from  t_vehicle_gps_record a ,(select @i:=0,@key_i:=0) t2
        where a.valid =1
        and a.install_time is not null
        and a.vehicle_id in (${vehicleIds})
        order by a.vehicle_id,a.install_time desc ) ret
        where ret.sortNum = 1;
    </select>

    <select id="getVehicleBasicPropertyById" resultType="com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel">
        select
        tvb.id               as vehicleId,
        tvdl.vehicle_no      as vehicleNo,
        tvb.vehicle_property as vehicleProperty,
        tvb.operating_state  as operatingState,
        tvt.vehicle_category as vehicleCategory
        from t_vehicle_basic tvb
        left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = tvb.id and tvdl.valid = 1
        left join t_vehicle_type tvt on tvt.id = tvdl.vehicle_type and tvt.valid = 1
        where tvb.valid = 1
          and tvb.id = #{id,jdbcType=BIGINT}
        limit 1
    </select>

    <select id="getVehicleNoByIds" resultType="com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel">
        select
        tvb.id as vehicleId,
        tvdl.vehicle_no as vehicleNo,
        tvb.vehicle_property as vehicleProperty,
        tvb.operating_state as operatingState,
        tvt.vehicle_category as vehicleCategory
        from t_vehicle_basic tvb
        left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = tvb.id and tvdl.valid = 1
        left join t_vehicle_type tvt on tvt.id = tvdl.vehicle_type and tvt.valid = 1
        where tvb.valid = 1
        and tvb.id in (${ids})
    </select>
    <select id="getVehicleNoByNos" resultType="com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel">
        select
            tvb.id as vehicleId,
            tvdl.vehicle_no as vehicleNo,
            tvb.vehicle_property as vehicleProperty,
            tvb.operating_state as operatingState,
            tvt.vehicle_category as vehicleCategory
        from t_vehicle_basic tvb
                 left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = tvb.id and tvdl.valid = 1
                 left join t_vehicle_type tvt on tvt.id = tvdl.vehicle_type and tvt.valid = 1
        where tvb.valid = 1
          and tvdl.vehicle_no in (${vehicleNos})
    </select>
    <select id="getInternalTractorCount" resultType="java.lang.Integer">
        select count(*)
        from t_vehicle_basic tvb
        left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = tvb.id and tvdl.valid = 1
        left join t_vehicle_type tvt on tvt.id = tvdl.vehicle_type and tvt.valid = 1
        where tvb.valid = 1
        and tvb.vehicle_property in (1,3)
        and tvb.operating_state = 1
        and tvt.vehicle_category in (1,3)
    </select>

    <select id="getByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_vehicle_basic
        where valid = 1
        and id in (${ids})
    </select>

    <resultMap id="getVehicleScrapDetailMap" type="com.logistics.tms.controller.vehicleassetmanagement.response.VehicleAssertScrapDetailResponseModel">
        <result column="id" property="vehicleBasicId"/>
        <result column="vehicle_no" property="vehicleNo"/>
        <result column="operating_state" property="outageType"/>
        <result column="shut_down_reason" property="outageInfo"/>
        <collection property="pathList" ofType="java.lang.String" javaType="list">
            <result column="file_path"></result>
        </collection>
    </resultMap>
    <select id="getVehicleScrapDetail" resultMap="getVehicleScrapDetailMap">
        select
        tvb.id,
        tvb.operating_state,
        tvb.shut_down_reason,
        tvdl.vehicle_no,
        tcp.file_path
        from t_vehicle_basic tvb
        left join t_vehicle_driving_license tvdl on tvdl.vehicle_id=tvb.id and tvdl.valid=1
        left join t_certification_pictures tcp on tcp.object_type=15 and  tvb.id=tcp.object_id and tcp.file_type=10 and tcp.valid=1
        where tvb.id=#{vehicleId,jdbcType=BIGINT}
        and tvb.valid=1
    </select>

    <select id="searchVehicleForOurCompany" resultType="com.logistics.tms.controller.dispatch.response.VehicleSearchResponseModel">
        select
        tvb.id          as vehicleId,
        tvdl.vehicle_no as vehicleNo
        from t_vehicle_basic tvb
        left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = tvb.id and tvdl.valid = 1
        left join t_carrier_vehicle_relation tcvr on tcvr.valid = 1 and tvb.id = tcvr.vehicle_id
        where tvb.valid = 1
        and tvb.operating_state = 1
        and tcvr.company_carrier_id = #{params.companyCarrierId,jdbcType=BIGINT}
        <if test="params.vehicleProperty != null and params.vehicleProperty != ''">
            and tvb.vehicle_property in (${params.vehicleProperty})
        </if>
        <if test="params.vehicleNo != null and params.vehicleNo != ''">
            and instr(tvdl.vehicle_no, #{params.vehicleNo,jdbcType=VARCHAR})
        </if>
        <if test="params.vehicleTypeIds != null and params.vehicleTypeIds != ''">
            and tvdl.vehicle_type in (${params.vehicleTypeIds})
        </if>
    </select>

    <select id="searchVehicleByProperty" resultType="com.logistics.tms.controller.vehicleassetmanagement.response.SearchVehicleByPropertyResponseModel">
        select
        tvb.id               as vehicleId,
        tvb.vehicle_property as vehicleProperty,

        tvdl.vehicle_no      as vehicleNo,

        tvt.vehicle_category as vehicleCategory
        from t_vehicle_basic tvb
        left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = tvb.id and tvdl.valid = 1
        left join t_vehicle_type tvt on tvt.id = tvdl.vehicle_type and tvt.valid = 1
        where tvb.valid = 1
        and tvb.operating_state = 1
        <if test="vehicleNo != null and vehicleNo != ''">
            and instr(tvdl.vehicle_no, #{vehicleNo,jdbcType=VARCHAR})
        </if>
        <if test="vehicleProperty != null and vehicleProperty.size() != 0">
            and tvb.vehicle_property in
            <foreach collection="vehicleProperty" item="item" open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="vehicleCategory != null and vehicleCategory != ''">
            and tvt.vehicle_category in (${vehicleCategory})
        </if>
        order by tvb.id desc
    </select>

    <select id="searchVehicle" resultType="com.logistics.tms.controller.dispatch.response.VehicleSearchResponseModel">
        select
        tvb.id          as vehicleId,
        tvdl.vehicle_no as vehicleNo,
        tsvr.staff_id   as driverId
        from t_vehicle_basic tvb
        left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = tvb.id and tvdl.valid = 1
        left join t_staff_vehicle_relation tsvr ON tsvr.valid = 1
        and tsvr.vehicle_id = tvb.id
        and tsvr.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        where tvb.valid = 1
        and tvb.id in (${vehicleIds})
        <if test="vehicleNo != null and vehicleNo != ''">
            and (instr(tvdl.vehicle_no, #{vehicleNo,jdbcType=VARCHAR}))
        </if>
    </select>

    <select id="searchTrailerVehicle" resultType="com.logistics.tms.controller.vehicleassetmanagement.response.SearchTrailerVehicleResponseModel">
        SELECT
        tcvr.vehicle_id AS trailerVehicleId,
        tvdl.vehicle_no AS trailerVehicleNo
        FROM t_carrier_vehicle_relation tcvr
        LEFT JOIN t_vehicle_driving_license tvdl ON tvdl.vehicle_id = tcvr.vehicle_id AND tvdl.valid = 1
        LEFT JOIN t_vehicle_type tvt ON tvt.valid = 1 AND tvt.id = tvdl.vehicle_type
        WHERE tcvr.valid = 1
          and tcvr.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
          and instr(tvdl.vehicle_no, #{trailerVehicleNo,jdbcType=VARCHAR})
          and tvt.vehicle_category = 2
    </select>
</mapper>