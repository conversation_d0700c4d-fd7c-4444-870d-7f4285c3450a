package com.logistics.tms.controller.freightconfig.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigEditRequestModel extends CarrierFreightConfigRequestModel {

    @ApiModelProperty(value = "需求类型", required = true)
    private List<String> entrustTypes;
}
