package com.logistics.appapi.controller.driversafepromise.mapping;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.SafePromiseStatusEnum;
import com.logistics.appapi.client.driversafepromise.response.SearchSafePromiseAppletListResponseModel;
import com.logistics.appapi.controller.driversafepromise.response.SearchSafePromiseListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/11/25
 * @description:
 */
public class SafePromiseListMapping extends MapperMapping<SearchSafePromiseAppletListResponseModel, SearchSafePromiseListResponseDto> {


    @Override
    public void configure() {
        SearchSafePromiseAppletListResponseModel source = getSource();
        SearchSafePromiseListResponseDto destination = getDestination();
        //转换状态
        if (source.getStatus() != null) {
            destination.setStatusLabel(SafePromiseStatusEnum.getEnum(source.getStatus()).getValue());
        }
        //拼接标题
        if (StringUtils.isNotBlank(source.getPeriod())) {
            destination.setPeriodLabel(source.getPeriod() + CommonConstant.PROMISE_PERIOD);
        }
        //转换时间
        if (source.getPublishTime() != null) {
            destination.setPublishTime(DateUtils.dateToString(source.getPublishTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
    }
}
