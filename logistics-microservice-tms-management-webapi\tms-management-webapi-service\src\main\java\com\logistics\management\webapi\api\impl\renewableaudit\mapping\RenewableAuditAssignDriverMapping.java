package com.logistics.management.webapi.api.impl.renewableaudit.mapping;

import com.logistics.management.webapi.api.feign.renewableaudit.dto.RenewableAuditAssignDriverResponseDto;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.tms.api.feign.renewableaudit.model.RenewableAssignDriverDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.util.Optional;

public class RenewableAuditAssignDriverMapping extends MapperMapping<RenewableAssignDriverDetailResponseModel, RenewableAuditAssignDriverResponseDto> {
    @Override
    public void configure() {
        RenewableAssignDriverDetailResponseModel source = getSource();
        RenewableAuditAssignDriverResponseDto destination = getDestination();

        //发货地址转换
        destination.setLoadDetailAddress(source.getLoadProvinceName() + source.getLoadCityName() + source.getLoadAreaName());

        //货物数量保留两位小数
        destination.setGoodsAmountTotal(Optional.ofNullable(source.getGoodsAmountTotal()).orElse(BigDecimal.ZERO).setScale(3,BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());

        if (StringUtils.isNotBlank(source.getStaffName()) && StringUtils.isNotBlank(source.getStaffMobile())) {
            destination.setDriver(source.getStaffName() + " " + source.getStaffMobile());
        }

        //客户转换
        if(CompanyTypeEnum.COMPANY.getKey().equals(source.getBusinessType())){
            destination.setCustomerName(source.getCustomerName());
        }else if(CompanyTypeEnum.PERSONAL.getKey().equals(source.getBusinessType()))
            destination.setCustomerName(source.getCustomerUserName()+" "+source.getCustomerUserMobile());
    }
}
