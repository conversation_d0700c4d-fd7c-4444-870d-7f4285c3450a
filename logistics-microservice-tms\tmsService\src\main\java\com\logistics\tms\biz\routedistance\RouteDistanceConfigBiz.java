package com.logistics.tms.biz.routedistance;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.IfValidEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.client.BasicDataClient;
import com.logistics.tms.controller.routeconfig.request.*;
import com.logistics.tms.controller.routeconfig.response.RouteDistanceConfigDetailResponseModel;
import com.logistics.tms.controller.routeconfig.response.RouteDistanceConfigRecommendResponseModel;
import com.logistics.tms.controller.routeconfig.response.RouteDistanceConfigResponseModel;
import com.logistics.tms.entity.TRouteDistanceConfig;
import com.logistics.tms.mapper.TRouteDistanceConfigMapper;
import com.yelo.basicdata.api.feign.datamap.model.GetLonLatByMapIdResponseModel;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 *  路线距离配置service
 *
 * <AUTHOR>
 * @date 2023/7/3 11:01
 */
@Slf4j
@Service
public class RouteDistanceConfigBiz {


    @Resource
    private TRouteDistanceConfigMapper tRouteDistanceConfigMapper;

    @Resource
    private BasicDataClient basicDataClient;

    @Resource
    private CommonBiz commonBiz;


    /**
     * 查询路线距离配置列表
     * @param requestModel 检索条件 收货地 发货地
     * @return PageInfo<RouteDistanceConfigResponseModel> 路线距离配置列表
     */
    public PageInfo<RouteDistanceConfigResponseModel> searchList(RouteDistanceConfigListRequestModel requestModel) {
        requestModel.enablePaging();
        List<RouteDistanceConfigResponseModel> routeDistanceConfigResponseModels =
                tRouteDistanceConfigMapper.selectByArea(requestModel);
        return new PageInfo<>(routeDistanceConfigResponseModels);
    }

    /**
     * 查询路线距离配置详情
     *
     * @param requestModel 路线距离配置ID
     * @return RouteDistanceConfigDetailResponseModel 路线距离配置详情
     */
    public RouteDistanceConfigDetailResponseModel detail(RouteDistanceConfigRequestModel requestModel) {
        RouteDistanceConfigDetailResponseModel routeDistanceConfigDetailResponseModel =
                tRouteDistanceConfigMapper.selectById(requestModel.getRouteDistanceConfigId());
        if (Objects.isNull(routeDistanceConfigDetailResponseModel)) {
            throw new BizException(CarrierDataExceptionEnum.DISTANCE_CONFIG_NOT_EXIST);
        }
        return routeDistanceConfigDetailResponseModel;
    }


    /**
     * 新增路线距离配置
     * @param requestModel 路线距离配置
     * @return Boolean 是否创建成功
     */
    @Transactional
    public Boolean add(RouteDistanceConfigAddRequestModel requestModel) {

        // 添加正向距离配置
        TRouteDistanceConfig tRouteDistanceConfig = MapperUtils.mapper(requestModel, TRouteDistanceConfig.class);
        if (Objects.isNull(tRouteDistanceConfig)) {
            return false;
        }
        TRouteDistanceConfig configId = tRouteDistanceConfigMapper.selectByFromToArea(tRouteDistanceConfig);
        // 已经存在此发货地和收货地的配置
        if (Objects.nonNull(configId)) {
            throw new BizException(CarrierDataExceptionEnum.DISTANCE_CONFIG_HAS_EXIST);
        }
        commonBiz.setBaseEntityAdd(tRouteDistanceConfig, BaseContextHandler.getUserName());
        tRouteDistanceConfigMapper.insertSelective(tRouteDistanceConfig);

        // 添加反向距离配置
        tRouteDistanceConfig.setId(null);
        Long fromProvinceId = tRouteDistanceConfig.getFromProvinceId();
        String fromProvinceName = tRouteDistanceConfig.getFromProvinceName();
        Long fromCityId = tRouteDistanceConfig.getFromCityId();
        String fromCityName = tRouteDistanceConfig.getFromCityName();
        Long fromAreaId = tRouteDistanceConfig.getFromAreaId();
        String fromAreaName = tRouteDistanceConfig.getFromAreaName();
        // 交换收发货地
        tRouteDistanceConfig.setFromProvinceId(tRouteDistanceConfig.getToProvinceId());
        tRouteDistanceConfig.setFromProvinceName(tRouteDistanceConfig.getToProvinceName());
        tRouteDistanceConfig.setFromCityId(tRouteDistanceConfig.getToCityId());
        tRouteDistanceConfig.setFromCityName(tRouteDistanceConfig.getToCityName());
        tRouteDistanceConfig.setFromAreaId(tRouteDistanceConfig.getToAreaId());
        tRouteDistanceConfig.setFromAreaName(tRouteDistanceConfig.getToAreaName());

        tRouteDistanceConfig.setToProvinceId(fromProvinceId);
        tRouteDistanceConfig.setToProvinceName(fromProvinceName);
        tRouteDistanceConfig.setToCityId(fromCityId);
        tRouteDistanceConfig.setToCityName(fromCityName);
        tRouteDistanceConfig.setToAreaId(fromAreaId);
        tRouteDistanceConfig.setToAreaName(fromAreaName);
        TRouteDistanceConfig configIdReverse = tRouteDistanceConfigMapper.selectByFromToArea(tRouteDistanceConfig);
        // 已经存在此发货地和收货地的配置
        if (!Objects.nonNull(configIdReverse)) {
            tRouteDistanceConfigMapper.insertSelective(tRouteDistanceConfig);
        } else {
            TRouteDistanceConfig updateDistanceConfig = new TRouteDistanceConfig();
            updateDistanceConfig.setId(configIdReverse.getId());
            updateDistanceConfig.setBillingDistance(tRouteDistanceConfig.getBillingDistance());
            commonBiz.setBaseEntityModify(updateDistanceConfig, BaseContextHandler.getUserName());
            tRouteDistanceConfigMapper.updateByPrimaryKeySelective(updateDistanceConfig);
        }

        return true;
    }


    /**
     * 编辑路线距离配置
     * @param requestModel 根据id编辑计费距离配置
     * @return Boolean 是否更新成功
     */
    @Transactional
    public Boolean edit(@RequestBody RouteDistanceConfigEditRequestModel requestModel){
        TRouteDistanceConfig tRouteDistanceConfig =
                tRouteDistanceConfigMapper.selectByPrimaryKey(requestModel.getRouteDistanceConfigId());
        // 编辑时配置是否存在
        if(Objects.isNull(tRouteDistanceConfig)){
            throw new BizException(CarrierDataExceptionEnum.DISTANCE_CONFIG_NOT_EXIST);
        }
        TRouteDistanceConfig updateDistanceConfig = new TRouteDistanceConfig();
        updateDistanceConfig.setId(tRouteDistanceConfig.getId());
        updateDistanceConfig.setBillingDistance(requestModel.getBillingDistance());
        commonBiz.setBaseEntityModify(updateDistanceConfig, BaseContextHandler.getUserName());
        tRouteDistanceConfigMapper.updateByPrimaryKeySelective(updateDistanceConfig);
        return true;
    }


    /**
     * 删除路线距离配置
     * @param requestModel 根据id删除计费距离
     * @return Boolean 删除成功
     */
    @Transactional
    public Boolean delete(@RequestBody RouteDistanceConfigRequestModel requestModel){
        TRouteDistanceConfig tRouteDistanceConfig =
                tRouteDistanceConfigMapper.selectByPrimaryKey(requestModel.getRouteDistanceConfigId());
        // 编辑时配置是否存在
        if(Objects.isNull(tRouteDistanceConfig)){
            throw new BizException(CarrierDataExceptionEnum.DISTANCE_CONFIG_NOT_EXIST);
        }
        TRouteDistanceConfig updateDistanceConfig = new TRouteDistanceConfig();
        updateDistanceConfig.setId(tRouteDistanceConfig.getId());
        updateDistanceConfig.setValid(IfValidEnum.INVALID.getKey());
        commonBiz.setBaseEntityModify(updateDistanceConfig, BaseContextHandler.getUserName());
        tRouteDistanceConfigMapper.updateByPrimaryKeySelective(updateDistanceConfig);
        return true;
    }


    /**
     * 路线距离配置
     * @param requestModel 发货区id 收货区id
     * @return 高德推荐公里数 RouteDistanceConfigRecommendResponseModel
     */
    public RouteDistanceConfigRecommendResponseModel recommend(
            RouteDistanceConfigRecommendRequestModel requestModel){
        // 根据区id获取经纬度
        List<Long> mapIdList = new LinkedList<>();
        mapIdList.add(requestModel.getFromAreaId());
        mapIdList.add(requestModel.getToAreaId());
        List<GetLonLatByMapIdResponseModel> lonLatByMapIds = basicDataClient.getLonLatByMapIds(mapIdList);
        if (lonLatByMapIds.size() != 2) {
            throw new BizException(CarrierDataExceptionEnum.MAP_AREA_NOT_EXIST);
        }
        GetLonLatByMapIdResponseModel from = lonLatByMapIds.get(0);
        GetLonLatByMapIdResponseModel to = lonLatByMapIds.get(1);
        List<BigDecimal> mileageByLonAndLat;
        if (requestModel.getFromAreaId().equals(from.getMapId())) {
            mileageByLonAndLat =
                    commonBiz.getThreeMileageByLonAndLat(from.getLongitude()  + CommonConstant.COMMA + from.getLatitude(),
                            to.getLongitude()+ CommonConstant.COMMA + to.getLatitude());
        }else{
            mileageByLonAndLat =
                    commonBiz.getThreeMileageByLonAndLat(to.getLongitude() + CommonConstant.COMMA + to.getLatitude(),
                            from.getLongitude() + CommonConstant.COMMA + from.getLatitude());
        }
        Collections.sort(mileageByLonAndLat);
        RouteDistanceConfigRecommendResponseModel responseModel = new RouteDistanceConfigRecommendResponseModel();
        responseModel.setDistance(mileageByLonAndLat);
        return responseModel;
    }





}
