package com.logistics.appapi.controller.attendance.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;

/**
 * 修改考勤打卡历史请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/14
 */
@Data
public class UpdateAttendanceHistoryRequestDto {

	@ApiModelProperty(value = "考勤打卡ID", required = true)
	@NotBlank(message = "考勤信息不能为空")
	private String attendanceRecordId;

	@ApiModelProperty(value = "要变更的打卡类型 1: 上班 2:下班", required = true)
	@NotBlank(message = "请选择变更类型")
	@Range(min = 1, max = 2, message = "变更类型不存在")
	private String changeType;

	@ApiModelProperty(value = "要变更的打卡时间", required = true)
	@NotBlank(message = "请选择要变更的时间")
	private String changePunchTime;

	@ApiModelProperty(value = "变更原因")
	private String changeReason;
}
