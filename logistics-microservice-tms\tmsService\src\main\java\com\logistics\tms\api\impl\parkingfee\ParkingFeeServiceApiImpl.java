package com.logistics.tms.api.impl.parkingfee;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.parkingfee.ParkingFeeServiceApi;
import com.logistics.tms.api.feign.parkingfee.dto.*;
import com.logistics.tms.biz.parkingfee.ParkingFeeBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/10/9 16:16
 */
@RestController
public class ParkingFeeServiceApiImpl implements ParkingFeeServiceApi {

    @Autowired
    private ParkingFeeBiz parkingFeeBiz;

    /**
     * 列表查询
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<ParkingFeeListResponseModel>> searchList(@RequestBody ParkingFeeListRequestModel requestModel) {
        return Result.success(parkingFeeBiz.searchList(requestModel));
    }

    /**
     * 获取列表汇总
     * @param requestModel
     * @return
     */
    @Override
    public Result<SummaryParkingFeeResponseModel> getSummary(@RequestBody ParkingFeeListRequestModel requestModel) {
        return Result.success(parkingFeeBiz.getSummary(requestModel));
    }

    /**
     * 详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<ParkingFeeDetailResponseModel> getDetail(@RequestBody ParkingFeeDetailRequestModel requestModel) {
        return Result.success(parkingFeeBiz.getDetail(requestModel));
    }

    /**
     * 修改/保存
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> saveOrUpdate(@RequestBody SaveOrUpdateParkingFeeRequestModel requestModel) {
        parkingFeeBiz.saveOrUpdate(requestModel);
        return Result.success(true);
    }

    /**
     * 费用终止
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> terminateParkingFee(@RequestBody TerminateParkingFeeRequestModel requestModel) {
        parkingFeeBiz.terminateParkingFee(requestModel);
        return Result.success(true);
    }

    /**
     * 扣减历史列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<List<ParkingFeeDeductingHistoryResponseModel>> getDeductingHistoryList(@RequestBody ParkingFeeDeductingHistoryRequestModel requestModel) {
        return Result.success(parkingFeeBiz.getDeductingHistoryList(requestModel));
    }

    /**
     * 操作记录列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<List<ParkingFeeOperationRecordResponsesModel>> getOperationRecordList(@RequestBody ParkingFeeOperationRecordRequestModel requestModel) {
        return Result.success(parkingFeeBiz.getOperationRecordList(requestModel));
    }
}
