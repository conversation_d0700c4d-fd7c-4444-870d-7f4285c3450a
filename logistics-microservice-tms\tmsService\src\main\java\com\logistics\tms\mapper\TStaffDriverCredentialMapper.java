package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TStaffDriverCredential;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface TStaffDriverCredentialMapper extends BaseMapper<TStaffDriverCredential>{

    int batchUpdate(@Param("list") List<TStaffDriverCredential> list);

    Map<String,String> getDueDriverCredentialCount(@Param("companyCarrierId") Long companyCarrierId,@Param("remindDays") Integer remindDays);

    TStaffDriverCredential getDriverCredentialByStaffId(@Param("staffId") Long staffId);

    int updateByStaffIdSelective(TStaffDriverCredential tqStaffDriverCredential);

}