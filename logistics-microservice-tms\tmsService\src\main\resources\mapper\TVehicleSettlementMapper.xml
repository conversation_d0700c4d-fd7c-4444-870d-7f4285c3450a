<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TVehicleSettlementMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TVehicleSettlement">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="vehicle_property" jdbcType="INTEGER" property="vehicleProperty" />
    <result column="settlement_month" jdbcType="VARCHAR" property="settlementMonth" />
    <result column="actual_expenses_payable" jdbcType="DECIMAL" property="actualExpensesPayable" />
    <result column="carrier_order_count" jdbcType="INTEGER" property="carrierOrderCount" />
    <result column="carrier_freight" jdbcType="DECIMAL" property="carrierFreight" />
    <result column="tire_fee" jdbcType="DECIMAL" property="tireFee" />
    <result column="gps_fee" jdbcType="DECIMAL" property="gpsFee" />
    <result column="parking_fee" jdbcType="DECIMAL" property="parkingFee" />
    <result column="loan_fee" jdbcType="DECIMAL" property="loanFee" />
    <result column="oil_filled_fee" jdbcType="DECIMAL" property="oilFilledFee" />
    <result column="vehicle_claim_fee" jdbcType="DECIMAL" property="vehicleClaimFee" />
    <result column="insurance_fee" jdbcType="DECIMAL" property="insuranceFee" />
    <result column="deducting_fee" jdbcType="DECIMAL" property="deductingFee" />
    <result column="remaining_fee" jdbcType="DECIMAL" property="remainingFee" />
    <result column="accident_insurance_fee" jdbcType="DECIMAL" property="accidentInsuranceFee" />
    <result column="accident_insurance_expense_total" jdbcType="DECIMAL" property="accidentInsuranceExpenseTotal" />
    <result column="accident_insurance_claim_fee" jdbcType="DECIMAL" property="accidentInsuranceClaimFee" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
    <result column="oil_refund_fee" jdbcType="DECIMAL" property="oilRefundFee" />
    <result column="insurance_refund_fee" jdbcType="DECIMAL" property="insuranceRefundFee" />
    <result column="if_adjust_fee" jdbcType="INTEGER" property="ifAdjustFee" />
    <result column="adjust_fee" jdbcType="DECIMAL" property="adjustFee" />
    <result column="adjust_remark" jdbcType="VARCHAR" property="adjustRemark" />
    <result column="withdraw_remark" jdbcType="VARCHAR" property="withdrawRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    id, status, vehicle_id, vehicle_no, vehicle_property, settlement_month, actual_expenses_payable, 
    carrier_order_count, carrier_freight, tire_fee, gps_fee, parking_fee, loan_fee, oil_filled_fee, 
    vehicle_claim_fee, insurance_fee, deducting_fee, remaining_fee, accident_insurance_fee, 
    accident_insurance_expense_total, accident_insurance_claim_fee, remark, created_by, 
    created_time, last_modified_by, last_modified_time, valid, oil_refund_fee, insurance_refund_fee, 
    if_adjust_fee, adjust_fee, adjust_remark, withdraw_remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_vehicle_settlement
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_vehicle_settlement
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TVehicleSettlement">
    insert into t_vehicle_settlement (id, status, vehicle_id, 
      vehicle_no, vehicle_property, settlement_month, 
      actual_expenses_payable, carrier_order_count, 
      carrier_freight, tire_fee, gps_fee, 
      parking_fee, loan_fee, oil_filled_fee, 
      vehicle_claim_fee, insurance_fee, deducting_fee, 
      remaining_fee, accident_insurance_fee, accident_insurance_expense_total, 
      accident_insurance_claim_fee, remark, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid, oil_refund_fee, insurance_refund_fee, 
      if_adjust_fee, adjust_fee, adjust_remark, 
      withdraw_remark)
    values (#{id,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{vehicleId,jdbcType=BIGINT}, 
      #{vehicleNo,jdbcType=VARCHAR}, #{vehicleProperty,jdbcType=INTEGER}, #{settlementMonth,jdbcType=VARCHAR}, 
      #{actualExpensesPayable,jdbcType=DECIMAL}, #{carrierOrderCount,jdbcType=INTEGER}, 
      #{carrierFreight,jdbcType=DECIMAL}, #{tireFee,jdbcType=DECIMAL}, #{gpsFee,jdbcType=DECIMAL}, 
      #{parkingFee,jdbcType=DECIMAL}, #{loanFee,jdbcType=DECIMAL}, #{oilFilledFee,jdbcType=DECIMAL}, 
      #{vehicleClaimFee,jdbcType=DECIMAL}, #{insuranceFee,jdbcType=DECIMAL}, #{deductingFee,jdbcType=DECIMAL}, 
      #{remainingFee,jdbcType=DECIMAL}, #{accidentInsuranceFee,jdbcType=DECIMAL}, #{accidentInsuranceExpenseTotal,jdbcType=DECIMAL}, 
      #{accidentInsuranceClaimFee,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER}, #{oilRefundFee,jdbcType=DECIMAL}, #{insuranceRefundFee,jdbcType=DECIMAL}, 
      #{ifAdjustFee,jdbcType=INTEGER}, #{adjustFee,jdbcType=DECIMAL}, #{adjustRemark,jdbcType=VARCHAR}, 
      #{withdrawRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TVehicleSettlement" keyProperty="id" useGeneratedKeys="true">
    insert into t_vehicle_settlement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleNo != null">
        vehicle_no,
      </if>
      <if test="vehicleProperty != null">
        vehicle_property,
      </if>
      <if test="settlementMonth != null">
        settlement_month,
      </if>
      <if test="actualExpensesPayable != null">
        actual_expenses_payable,
      </if>
      <if test="carrierOrderCount != null">
        carrier_order_count,
      </if>
      <if test="carrierFreight != null">
        carrier_freight,
      </if>
      <if test="tireFee != null">
        tire_fee,
      </if>
      <if test="gpsFee != null">
        gps_fee,
      </if>
      <if test="parkingFee != null">
        parking_fee,
      </if>
      <if test="loanFee != null">
        loan_fee,
      </if>
      <if test="oilFilledFee != null">
        oil_filled_fee,
      </if>
      <if test="vehicleClaimFee != null">
        vehicle_claim_fee,
      </if>
      <if test="insuranceFee != null">
        insurance_fee,
      </if>
      <if test="deductingFee != null">
        deducting_fee,
      </if>
      <if test="remainingFee != null">
        remaining_fee,
      </if>
      <if test="accidentInsuranceFee != null">
        accident_insurance_fee,
      </if>
      <if test="accidentInsuranceExpenseTotal != null">
        accident_insurance_expense_total,
      </if>
      <if test="accidentInsuranceClaimFee != null">
        accident_insurance_claim_fee,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
      <if test="oilRefundFee != null">
        oil_refund_fee,
      </if>
      <if test="insuranceRefundFee != null">
        insurance_refund_fee,
      </if>
      <if test="ifAdjustFee != null">
        if_adjust_fee,
      </if>
      <if test="adjustFee != null">
        adjust_fee,
      </if>
      <if test="adjustRemark != null">
        adjust_remark,
      </if>
      <if test="withdrawRemark != null">
        withdraw_remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleProperty != null">
        #{vehicleProperty,jdbcType=INTEGER},
      </if>
      <if test="settlementMonth != null">
        #{settlementMonth,jdbcType=VARCHAR},
      </if>
      <if test="actualExpensesPayable != null">
        #{actualExpensesPayable,jdbcType=DECIMAL},
      </if>
      <if test="carrierOrderCount != null">
        #{carrierOrderCount,jdbcType=INTEGER},
      </if>
      <if test="carrierFreight != null">
        #{carrierFreight,jdbcType=DECIMAL},
      </if>
      <if test="tireFee != null">
        #{tireFee,jdbcType=DECIMAL},
      </if>
      <if test="gpsFee != null">
        #{gpsFee,jdbcType=DECIMAL},
      </if>
      <if test="parkingFee != null">
        #{parkingFee,jdbcType=DECIMAL},
      </if>
      <if test="loanFee != null">
        #{loanFee,jdbcType=DECIMAL},
      </if>
      <if test="oilFilledFee != null">
        #{oilFilledFee,jdbcType=DECIMAL},
      </if>
      <if test="vehicleClaimFee != null">
        #{vehicleClaimFee,jdbcType=DECIMAL},
      </if>
      <if test="insuranceFee != null">
        #{insuranceFee,jdbcType=DECIMAL},
      </if>
      <if test="deductingFee != null">
        #{deductingFee,jdbcType=DECIMAL},
      </if>
      <if test="remainingFee != null">
        #{remainingFee,jdbcType=DECIMAL},
      </if>
      <if test="accidentInsuranceFee != null">
        #{accidentInsuranceFee,jdbcType=DECIMAL},
      </if>
      <if test="accidentInsuranceExpenseTotal != null">
        #{accidentInsuranceExpenseTotal,jdbcType=DECIMAL},
      </if>
      <if test="accidentInsuranceClaimFee != null">
        #{accidentInsuranceClaimFee,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
      <if test="oilRefundFee != null">
        #{oilRefundFee,jdbcType=DECIMAL},
      </if>
      <if test="insuranceRefundFee != null">
        #{insuranceRefundFee,jdbcType=DECIMAL},
      </if>
      <if test="ifAdjustFee != null">
        #{ifAdjustFee,jdbcType=INTEGER},
      </if>
      <if test="adjustFee != null">
        #{adjustFee,jdbcType=DECIMAL},
      </if>
      <if test="adjustRemark != null">
        #{adjustRemark,jdbcType=VARCHAR},
      </if>
      <if test="withdrawRemark != null">
        #{withdrawRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TVehicleSettlement">
    update t_vehicle_settlement
    <set>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleProperty != null">
        vehicle_property = #{vehicleProperty,jdbcType=INTEGER},
      </if>
      <if test="settlementMonth != null">
        settlement_month = #{settlementMonth,jdbcType=VARCHAR},
      </if>
      <if test="actualExpensesPayable != null">
        actual_expenses_payable = #{actualExpensesPayable,jdbcType=DECIMAL},
      </if>
      <if test="carrierOrderCount != null">
        carrier_order_count = #{carrierOrderCount,jdbcType=INTEGER},
      </if>
      <if test="carrierFreight != null">
        carrier_freight = #{carrierFreight,jdbcType=DECIMAL},
      </if>
      <if test="tireFee != null">
        tire_fee = #{tireFee,jdbcType=DECIMAL},
      </if>
      <if test="gpsFee != null">
        gps_fee = #{gpsFee,jdbcType=DECIMAL},
      </if>
      <if test="parkingFee != null">
        parking_fee = #{parkingFee,jdbcType=DECIMAL},
      </if>
      <if test="loanFee != null">
        loan_fee = #{loanFee,jdbcType=DECIMAL},
      </if>
      <if test="oilFilledFee != null">
        oil_filled_fee = #{oilFilledFee,jdbcType=DECIMAL},
      </if>
      <if test="vehicleClaimFee != null">
        vehicle_claim_fee = #{vehicleClaimFee,jdbcType=DECIMAL},
      </if>
      <if test="insuranceFee != null">
        insurance_fee = #{insuranceFee,jdbcType=DECIMAL},
      </if>
      <if test="deductingFee != null">
        deducting_fee = #{deductingFee,jdbcType=DECIMAL},
      </if>
      <if test="remainingFee != null">
        remaining_fee = #{remainingFee,jdbcType=DECIMAL},
      </if>
      <if test="accidentInsuranceFee != null">
        accident_insurance_fee = #{accidentInsuranceFee,jdbcType=DECIMAL},
      </if>
      <if test="accidentInsuranceExpenseTotal != null">
        accident_insurance_expense_total = #{accidentInsuranceExpenseTotal,jdbcType=DECIMAL},
      </if>
      <if test="accidentInsuranceClaimFee != null">
        accident_insurance_claim_fee = #{accidentInsuranceClaimFee,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
      <if test="oilRefundFee != null">
        oil_refund_fee = #{oilRefundFee,jdbcType=DECIMAL},
      </if>
      <if test="insuranceRefundFee != null">
        insurance_refund_fee = #{insuranceRefundFee,jdbcType=DECIMAL},
      </if>
      <if test="ifAdjustFee != null">
        if_adjust_fee = #{ifAdjustFee,jdbcType=INTEGER},
      </if>
      <if test="adjustFee != null">
        adjust_fee = #{adjustFee,jdbcType=DECIMAL},
      </if>
      <if test="adjustRemark != null">
        adjust_remark = #{adjustRemark,jdbcType=VARCHAR},
      </if>
      <if test="withdrawRemark != null">
        withdraw_remark = #{withdrawRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TVehicleSettlement">
    update t_vehicle_settlement
    set status = #{status,jdbcType=INTEGER},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      vehicle_property = #{vehicleProperty,jdbcType=INTEGER},
      settlement_month = #{settlementMonth,jdbcType=VARCHAR},
      actual_expenses_payable = #{actualExpensesPayable,jdbcType=DECIMAL},
      carrier_order_count = #{carrierOrderCount,jdbcType=INTEGER},
      carrier_freight = #{carrierFreight,jdbcType=DECIMAL},
      tire_fee = #{tireFee,jdbcType=DECIMAL},
      gps_fee = #{gpsFee,jdbcType=DECIMAL},
      parking_fee = #{parkingFee,jdbcType=DECIMAL},
      loan_fee = #{loanFee,jdbcType=DECIMAL},
      oil_filled_fee = #{oilFilledFee,jdbcType=DECIMAL},
      vehicle_claim_fee = #{vehicleClaimFee,jdbcType=DECIMAL},
      insurance_fee = #{insuranceFee,jdbcType=DECIMAL},
      deducting_fee = #{deductingFee,jdbcType=DECIMAL},
      remaining_fee = #{remainingFee,jdbcType=DECIMAL},
      accident_insurance_fee = #{accidentInsuranceFee,jdbcType=DECIMAL},
      accident_insurance_expense_total = #{accidentInsuranceExpenseTotal,jdbcType=DECIMAL},
      accident_insurance_claim_fee = #{accidentInsuranceClaimFee,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER},
      oil_refund_fee = #{oilRefundFee,jdbcType=DECIMAL},
      insurance_refund_fee = #{insuranceRefundFee,jdbcType=DECIMAL},
      if_adjust_fee = #{ifAdjustFee,jdbcType=INTEGER},
      adjust_fee = #{adjustFee,jdbcType=DECIMAL},
      adjust_remark = #{adjustRemark,jdbcType=VARCHAR},
      withdraw_remark = #{withdrawRemark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>