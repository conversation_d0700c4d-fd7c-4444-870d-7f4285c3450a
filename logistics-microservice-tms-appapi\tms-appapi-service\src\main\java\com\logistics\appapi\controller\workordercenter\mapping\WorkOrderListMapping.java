package com.logistics.appapi.controller.workordercenter.mapping;

import com.logistics.appapi.base.enums.EncodeTypeEnum;
import com.logistics.appapi.base.enums.WorkOrderStatusEnum;
import com.logistics.appapi.base.utils.FrequentMethodUtils;
import com.logistics.appapi.client.workordercenter.response.WorkOrderListAppletResponseModel;
import com.logistics.appapi.controller.workordercenter.response.WorkOrderListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

public class WorkOrderListMapping extends MapperMapping<WorkOrderListAppletResponseModel, WorkOrderListResponseDto> {

    @Override
    public void configure() {
        WorkOrderListAppletResponseModel source = getSource();
        WorkOrderListResponseDto destination = getDestination();

        //状态
        destination.setStatusLabel(WorkOrderStatusEnum.getEnumByKey(source.getStatus()).getValue());

        //联系人
        destination.setLoadMobile(FrequentMethodUtils.encryptionData(source.getLoadMobile(), EncodeTypeEnum.MOBILE_PHONE));

        //提货地址
        String loadDetailAddress = source.getLoadProvinceName() + source.getLoadCityName() + source.getLoadAreaName()
                + (StringUtils.isNotBlank(source.getLoadWarehouse()) ? "【" + source.getLoadWarehouse() + "】" : "") + source.getLoadDetailAddress();
        destination.setLoadDetailAddress(loadDetailAddress);
    }
}
