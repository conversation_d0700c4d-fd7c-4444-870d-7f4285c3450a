package com.logistics.tms.biz.companyauthorization;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.companycarrierauthorization.model.request.*;
import com.logistics.tms.api.feign.companycarrierauthorization.model.response.CarrierAuthorizationDetailResponseModel;
import com.logistics.tms.api.feign.companycarrierauthorization.model.response.CarrierAuthorizationListResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TCertificationPictures;
import com.logistics.tms.entity.TCompanyAuthorization;
import com.logistics.tms.entity.TCompanyCarrier;
import com.logistics.tms.mapper.TCertificationPicturesMapper;
import com.logistics.tms.mapper.TCompanyAuthorizationMapper;
import com.logistics.tms.mapper.TCompanyCarrierMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class CompanyAuthorizationBiz {

    private final TCompanyAuthorizationMapper companyAuthorizationMapper;

    private final TCompanyCarrierMapper companyCarrierMapper;

    private final TCertificationPicturesMapper certificationPicturesMapper;

    private final CommonBiz commonBiz;

    private final TCompanyCarrierMapper tCompanyCarrierMapper;

    /**
     * 查询车主授权列表
     * @param requestModel
     * @return PageInfo<CarrierAuthorizationListResponseModel>
     */
    public PageInfo<CarrierAuthorizationListResponseModel> carrierAuthorizationList(CarrierAuthorizationListRequestModel requestModel) {
        requestModel.enablePaging();
        List<CarrierAuthorizationListResponseModel> carrierAuthorizationList = companyAuthorizationMapper.selectCarrierAuthorizationList(requestModel);
        return new PageInfo<>(carrierAuthorizationList);
    }

    /**
     * 车主授权详情
     * @param requestModel
     * @return CarrierAuthorizationDetailResponseModel
     */
    public CarrierAuthorizationDetailResponseModel carrierAuthorizationDetail(CarrierAuthorizationDetailRequestModel requestModel) {
        // 查询授权详情
        var carrierAuthorizationDetail = companyAuthorizationMapper.selectCarrierAuthorizationDetailById(requestModel.getCarrierAuthorizationId());
        if (Objects.isNull(carrierAuthorizationDetail)) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_AUTH_INFO_IS_NULL);
        }
        // 查询图片信息
        if (!CompanyCarrierAuthAuditStatusEnum.WAIT_AUTH.getKey().equals(carrierAuthorizationDetail.getAuthorizationStatus())) {
            List<String> imagePathList = certificationPicturesMapper.getByObjectIdType(carrierAuthorizationDetail.getCarrierAuthorizationId(),
                    CertificationPicturesObjectTypeEnum.T_COMPANY_CARRIER_AUTHORIZATION.getObjectType(),
                    CertificationPicturesFileTypeEnum.T_COMPANY_CARRIER_AUTHORIZATION_FILE.getFileType())
                    .stream()
                    .map(TCertificationPictures::getFilePath)
                    .collect(Collectors.toList());
            carrierAuthorizationDetail.setAuthorizationImageList(imagePathList);
        }
        //查询归档文件信息
        certificationPicturesMapper.getByObjectIdType(carrierAuthorizationDetail.getCarrierAuthorizationId(),
                        CertificationPicturesObjectTypeEnum.T_COMPANY_CARRIER_AUTHORIZATION.getObjectType(),
                        CertificationPicturesFileTypeEnum.T_ARCHIVED_PIC_FILE.getFileType())
                .stream()
                .map(TCertificationPictures::getFilePath).findFirst().ifPresent(carrierAuthorizationDetail::setArchivedFilePath);
        return carrierAuthorizationDetail;
    }

    /**
     * 新增车主授权信息
     * @param requestModel
     * @return Boolean
     */
    @Transactional
    public Boolean carrierAuthorizationAdd(CarrierAuthorizationAddRequestModel requestModel) {

        // 查询车主是否存在
        List<TCompanyCarrier> companyCarriers = companyCarrierMapper.getByIds(requestModel.getCompanyCarrierId().toString());
        TCompanyCarrier companyCarrier = companyCarriers.stream()
                .findFirst()
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY));
        Long companyId = companyCarrier.getCompanyId();

        // 查询车主授权信息
        TCompanyAuthorization companyAuthorization = companyAuthorizationMapper.selectOneByCompanyId(companyId);
        if (Objects.isNull(companyAuthorization)) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_AUTH_INFO_IS_NULL);
        }
        // 是否已提交审核
        if (!CompanyCarrierAuthAuditStatusEnum.WAIT_AUTH.getKey().equals(companyAuthorization.getAuditStatus())) {
            // 是否完成授权
            if (CompanyCarrierAuthAuditStatusEnum.BE_AUTH.getKey().equals(companyAuthorization.getAuditStatus())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ALREADY_AUTHORIZATION);
            }
            throw new BizException(CarrierDataExceptionEnum.CARRIER_AUTHORIZATION_REVIEW_SUBMIT);
        }

        // 新增授权信息
        Date currentDate = new Date();
        String userName = BaseContextHandler.getUserName();
        TCompanyAuthorization entity = new TCompanyAuthorization();
        entity.setId(companyAuthorization.getId());
        entity.setCompanyId(companyId);
        entity.setCompanyType(companyCarrier.getType());
        entity.setAuditStatus(CompanyCarrierAuthAuditStatusEnum.BE_AUTH.getKey());
        entity.setAuditorName(userName);
        entity.setAuditTime(currentDate);
        entity.setRemark(requestModel.getRemark());
        // 归档参数
        Integer archivedStatus = CompanyAuthorizationArchivedEnum.ARCHIVED.getKey();
        if (archivedStatus.equals(requestModel.getIsArchived())) {
            entity.setIsArchive(archivedStatus);
            entity.setArchivedName(userName);
            entity.setArchivedTime(currentDate);
        }
        commonBiz.setBaseEntityModify(entity, userName);
        companyAuthorizationMapper.updateCompanyAuthorization(entity);

        //更新车主授权状态
        TCompanyCarrier tCompanyCarrierUp = new TCompanyCarrier();
        tCompanyCarrierUp.setId(companyCarrier.getId());
        tCompanyCarrierUp.setAuthorizationStatus(CompanyCarrierAuthAuditStatusEnum.BE_AUTH.getKey());
        commonBiz.setBaseEntityModify(tCompanyCarrierUp, BaseContextHandler.getUserName());
        tCompanyCarrierMapper.updateByPrimaryKeySelective(tCompanyCarrierUp);

        // 上传授权书图片
        CertificationPicturesFileTypeEnum fileTypeEnum = CertificationPicturesFileTypeEnum.T_COMPANY_CARRIER_AUTHORIZATION_FILE;
        certificationPicturesMapper.delByObjectTypeId(fileTypeEnum.getObjectType().getObjectType(), entity.getId(), BaseContextHandler.getUserName());

        List<TCertificationPictures> pictures = requestModel.getAuthorizationImageList()
                .stream()
                .map(pic -> {
                    // 上传图片
                    String path = commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.COMPANY_CARRIER_AUTHORIZATION.getKey(),
                            "", pic, null);
                    // 封装票据
                    TCertificationPictures picture = new TCertificationPictures();
                    picture.setObjectId(entity.getId());
                    picture.setObjectType(fileTypeEnum.getObjectType().getObjectType());
                    picture.setFileType(fileTypeEnum.getFileType());
                    picture.setFileTypeName(fileTypeEnum.getFileName());
                    picture.setFileName(fileTypeEnum.getFileName());
                    picture.setFilePath(path);
                    picture.setUploadUserName(userName);
                    picture.setUploadTime(currentDate);
                    picture.setSuffix(pic.substring(pic.indexOf('.')));
                    commonBiz.setBaseEntityAdd(picture, BaseContextHandler.getUserName());
                    return picture;
                }).collect(Collectors.toList());
        // 保存图片路径
        certificationPicturesMapper.batchInsert(pictures);
        return true;
    }

    /**
     * 审核车主授权信息
     * @param requestModel
     * @return Boolean
     */
    @Transactional
    public Boolean carrierAuthorizationAudit(CarrierAuthorizationAuditRequestModel requestModel) {
        // 查询车主授权信息
        TCompanyAuthorization companyAuthorization = companyAuthorizationMapper.selectOneById(requestModel.getCarrierAuthorizationId());
        if (Objects.isNull(companyAuthorization)) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_AUTH_INFO_IS_NULL);
        }

        // 状态判断
        if (!CompanyCarrierAuthAuditStatusEnum.WAIT_AUDIT.getKey().equals(companyAuthorization.getAuditStatus())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_AUTHORIZATION_AUDIT_CHANGE);
        }

        Date date = new Date();
        String userName = BaseContextHandler.getUserName();
        TCompanyAuthorization entity = new TCompanyAuthorization();
        entity.setId(companyAuthorization.getId());
        // 审核状态转换
        Integer auditStatus = CommonConstant.INTEGER_ONE.equals(requestModel.getAuditModel()) ?
                CompanyCarrierAuthAuditStatusEnum.BE_AUTH.getKey() :
                CompanyCarrierAuthAuditStatusEnum.AUDIT_REJECT.getKey();
        entity.setAuditStatus(auditStatus);
        entity.setAuditorName(userName);
        entity.setAuditTime(date);
        entity.setRemark(requestModel.getRemark());

        // 归档处理
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getIsArchived())) {
            entity.setIsArchive(requestModel.getIsArchived());
            entity.setArchivedTime(date);
            entity.setArchivedName(userName);
        }

        commonBiz.setBaseEntityModify(entity, userName);
        companyAuthorizationMapper.updateCompanyAuthorization(entity);

        //更新车主表信息
        Long carrierId = Optional.ofNullable(tCompanyCarrierMapper.getByCompanyId(companyAuthorization.getCompanyId()))
                .map(TCompanyCarrier::getId)
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.CARRIER_ACCOUNT_NOT_EXIST));
        TCompanyCarrier tCompanyCarrierUp = new TCompanyCarrier();
        tCompanyCarrierUp.setId(carrierId);
        tCompanyCarrierUp.setAuthorizationStatus(auditStatus);
        commonBiz.setBaseEntityModify(tCompanyCarrierUp, userName);
        tCompanyCarrierMapper.updateByPrimaryKeySelective(tCompanyCarrierUp);
        return true;
    }

    /**
     * 车主授权信息归档
     * @param requestModel
     * @return Boolean
     */
    @Transactional
    public Boolean carrierAuthorizationArchived(CarrierAuthorizationArchivedRequestModel requestModel) {

        TCompanyAuthorization companyAuthorization = companyAuthorizationMapper.selectOneById(requestModel.getCarrierAuthorizationId());
        if (Objects.isNull(companyAuthorization)) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_AUTH_INFO_IS_NULL);
        }
        if (!CompanyCarrierAuthAuditStatusEnum.BE_AUTH.getKey().equals(companyAuthorization.getAuditStatus())) {
            throw new BizException(CarrierDataExceptionEnum.NOT_CARRIER_AUTHORIZATION_ARCHIVED);
        }
        if (!CompanyAuthorizationArchivedEnum.NOT_ARCHIVED.getKey().equals(companyAuthorization.getIsArchive())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_AUTHORIZATION_ARCHIVED_CHANGE);
        }

        // 修改归档状态
        TCompanyAuthorization entity = new TCompanyAuthorization();
        entity.setId(companyAuthorization.getId());
        entity.setIsArchive(CompanyAuthorizationArchivedEnum.ARCHIVED.getKey());
        entity.setArchivedName(BaseContextHandler.getUserName());
        entity.setArchivedTime(new Date());
        entity.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityModify(entity, BaseContextHandler.getUserName());
        int size = companyAuthorizationMapper.updateCompanyAuthorization(entity);
        if (!CommonConstant.INTEGER_ONE.equals(size)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_AUTHORIZATION_ARCHIVED_CHANGE);
        }
        //归档文件修改
        archivedFileModified(entity.getId(),requestModel.getArchivedFilePath());
        return true;
    }

    /**
     * 车主授权信息归档
     * @param requestModel
     * @return Boolean
     */
    @Transactional
    public Boolean carrierAuthorizationReArchive(CarrierAuthorizationReArchivedRequestModel requestModel) {

        TCompanyAuthorization companyAuthorization = companyAuthorizationMapper.selectOneById(requestModel.getCarrierAuthorizationId());
        if (Objects.isNull(companyAuthorization)) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_AUTH_INFO_IS_NULL);
        }
        if (!CompanyCarrierAuthAuditStatusEnum.BE_AUTH.getKey().equals(companyAuthorization.getAuditStatus())) {
            throw new BizException(CarrierDataExceptionEnum.NOT_CARRIER_AUTHORIZATION_ARCHIVED);
        }
        if (CompanyAuthorizationArchivedEnum.NOT_ARCHIVED.getKey().equals(companyAuthorization.getIsArchive())) {
            throw new BizException(CarrierDataExceptionEnum.NOT_ARCHIVED);
        }

        // 修改归档状态
        TCompanyAuthorization entity = new TCompanyAuthorization();
        entity.setArchivedName(BaseContextHandler.getUserName());
        entity.setArchivedTime(new Date());
        entity.setId(companyAuthorization.getId());
        entity.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityModify(entity, BaseContextHandler.getUserName());
        int size = companyAuthorizationMapper.updateCompanyAuthorization(entity);
        if (!CommonConstant.INTEGER_ONE.equals(size)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_AUTHORIZATION_ARCHIVED_CHANGE);
        }
        //归档文件修改
        archivedFileModified(entity.getId(),requestModel.getArchivedFilePath());
        return true;
    }
    public void archivedFileModified(Long objectId,String archivedFilePath){
        CertificationPicturesFileTypeEnum fileTypeEnum = CertificationPicturesFileTypeEnum.T_ARCHIVED_PIC_FILE;
        String path = commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.T_ARCHIVED_FILE.getKey(),
                "", archivedFilePath, null);
        Optional<TCertificationPictures> archivedFileList = certificationPicturesMapper.getByObjectIdType(objectId, CertificationPicturesObjectTypeEnum.T_COMPANY_CARRIER_AUTHORIZATION.getObjectType(), fileTypeEnum.getFileType()).stream().findFirst();
        archivedFileList.ifPresentOrElse(e->{
            if(StringUtils.isBlank(archivedFilePath)){
                log.debug("已存在归档文件->逻辑删除");
                commonBiz.setBaseEntityModify(e, BaseContextHandler.getUserName());
                certificationPicturesMapper.delByObjectTypeFileTypeId(e.getObjectType(), fileTypeEnum.getFileType(), e.getObjectId(), BaseContextHandler.getUserName());
            }else{
                log.debug("更新历史归档文件->更新");
                e.setFilePath(path);
                e.setSuffix(archivedFilePath.substring(archivedFilePath.indexOf(CommonConstant.POINT)));
                commonBiz.setBaseEntityModify(e, BaseContextHandler.getUserName());
                certificationPicturesMapper.updateByPrimaryKey(e);
            }
        },()->{
            if(StringUtils.isBlank(archivedFilePath)){
                log.debug("不存在归档文件");
            }else{
                log.debug("不存在归档文件->新增");
                TCertificationPictures archivedFile = archivedFileStructure(objectId,archivedFilePath,path,fileTypeEnum);
                commonBiz.setBaseEntityAdd(archivedFile, BaseContextHandler.getUserName());
                certificationPicturesMapper.insertSelective(archivedFile);
            }
        });
    }

    private TCertificationPictures archivedFileStructure(Long objectId, String archivedFilePath, String path, CertificationPicturesFileTypeEnum fileTypeEnum) {
        TCertificationPictures archivedFile = new TCertificationPictures();
        archivedFile.setObjectId(objectId);
        archivedFile.setObjectType(fileTypeEnum.getObjectType().getObjectType());
        archivedFile.setFileType(fileTypeEnum.getFileType());
        archivedFile.setFileTypeName(fileTypeEnum.getFileName());
        archivedFile.setFileName(fileTypeEnum.getFileName());
        archivedFile.setFilePath(path);
        archivedFile.setSuffix(archivedFilePath.substring(archivedFilePath.indexOf(CommonConstant.POINT)));
        return archivedFile;
    }
}
