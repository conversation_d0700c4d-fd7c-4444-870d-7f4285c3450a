package com.logistics.tms.controller.staff.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchStaffManagementListRequestModel extends AbstractPageForm<SearchStaffManagementListRequestModel> {
    @ApiModelProperty("人员机构  1 自主，2 外部，3 自营")
    private Integer staffProperty;
    @ApiModelProperty("人员类别 1 驾驶员 2 押运员 3 驾驶员&押运员,多种类型拼接用于看板跳转例如 “1,3”")
    private String staffType;
    @ApiModelProperty("1 启用 2 禁用")
    private Integer openStatus;
    @ApiModelProperty("从业资格证号")
    private String occupationalRequirementsCredentialNo;
    @ApiModelProperty("人员姓名")
    private String staffName;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("操作时间")
    private String lastModifiedTimeStart;
    @ApiModelProperty("操作时间")
    private String lastModifiedTimeEnd;

    @ApiModelProperty("人员基本信息表IDs（导出时选中数据的ids）")
    private String staffIds;
    @ApiModelProperty("看板跳转的IDS")
    private String ids;

    @ApiModelProperty("车主名称或个人姓名手机号")
    private String companyCarrierName;

    @ApiModelProperty("我司数据,1:我司")
    private Integer isOurCompany;

    @ApiModelProperty("实名认证(个人): 0 待实名 1 实名中 2 已实名")
    private Integer realNameAuthenticationStatus;

    @ApiModelProperty("云仓账号状态, 0:关 1:开")
    private Integer accountSwitchStatus;
}
