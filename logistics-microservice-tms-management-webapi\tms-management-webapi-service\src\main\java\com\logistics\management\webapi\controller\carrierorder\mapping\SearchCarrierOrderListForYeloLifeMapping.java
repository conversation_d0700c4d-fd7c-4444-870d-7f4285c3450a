package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.carrierorder.response.SearchCarrierOrderListForYeloLifeResponseModel;
import com.logistics.management.webapi.client.carrierorder.response.SearchCarrierOrderListGoodsInfoModel;
import com.logistics.management.webapi.controller.carrierorder.response.SearchCarrierOrderListForYeloLifeResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/19
 */
public class SearchCarrierOrderListForYeloLifeMapping extends MapperMapping<SearchCarrierOrderListForYeloLifeResponseModel, SearchCarrierOrderListForYeloLifeResponseDto> {

	@Override
	public void configure() {
		SearchCarrierOrderListForYeloLifeResponseModel source = getSource();
		SearchCarrierOrderListForYeloLifeResponseDto destination = getDestination();

		if (source != null) {
			//客户名称转换
			if(LifeBusinessTypeEnum.COMPANY.getKey().equals(source.getBusinessType())){
				destination.setCustomName(source.getCustomName());
				destination.setExportCustomerName(source.getCustomName());
			}else if (LifeBusinessTypeEnum.PERSONAGE.getKey().equals(source.getBusinessType())) {
				destination.setCustomName(source.getCustomerUserName() + " " + FrequentMethodUtils.encryptionData(source.getCustomerUserMobile(), EncodeTypeEnum.MOBILE_PHONE));
				destination.setExportCustomerName(source.getCustomerUserName() + " " + source.getCustomerUserMobile());
			}

			//个人车主展示姓名+手机号
			if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())){
				destination.setCompanyCarrierName(source.getCarrierContactName()+" "+source.getCarrierContactPhone());
			}

			//状态转换
			if (CommonConstant.INTEGER_ONE.equals(source.getIfCancel())) {
				destination.setStatus(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getKey().toString());
				destination.setStatusDesc(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getValue());
			}else{
				destination.setStatusDesc(CarrierOrderStatusEnum.getEnum(source.getStatus()).getValue());
			}

			destination.setGoodsUnit(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());
			//数量
			destination.setExpectAmount(source.getExpectAmount().stripTrailingZeros().toPlainString());
			destination.setLoadAmount(source.getLoadAmount().stripTrailingZeros().toPlainString());
			destination.setUnloadAmount(source.getUnloadAmount().stripTrailingZeros().toPlainString());
			destination.setSignAmount(source.getSignAmount().stripTrailingZeros().toPlainString());

			//收发货地址
			destination.setLoadAddress((StringUtils.isNotEmpty(source.getLoadWarehouse()) ? "【" + source.getLoadWarehouse() + "】" : "") + " " + source.getLoadCityName() + source.getLoadAreaName() + source.getLoadDetailAddress());
			destination.setUnloadAddress(source.getLoadProvinceName() + source.getUnloadCityName() + source.getUnloadAreaName() + source.getUnloadDetailAddress());

			//司机
			destination.setDriver(source.getDriverName() + " " + FrequentMethodUtils.encryptionData(source.getDriverMobile(), EncodeTypeEnum.MOBILE_PHONE));
			destination.setExportDriver(source.getDriverName() + " " + source.getDriverMobile());

			//拼接品名
			if (ListUtils.isNotEmpty(source.getGoodsInfoList())) {
				StringBuilder goodsName = new StringBuilder();
				for (int index = 0; index < source.getGoodsInfoList().size(); index++) {
					SearchCarrierOrderListGoodsInfoModel tmpGoodsModel = source.getGoodsInfoList().get(index);
					if (source.getGoodsUnit().equals(GoodsUnitEnum.BY_VOLUME.getKey())) {
						tmpGoodsModel.setGoodsSize(tmpGoodsModel.getGoodsSize() + " " + tmpGoodsModel.getLength() + "*" + tmpGoodsModel.getWidth() + "*" + tmpGoodsModel.getHeight() + "mm");
					}
					if (index != 0) {
						goodsName.append("/");
					}
					goodsName.append(tmpGoodsModel.getGoodsName());
				}
				destination.setGoodsName(goodsName.toString());
			}

			//运单来源
			destination.setCustomerOrderSource(YeloLifeCarrierOrderSourceEnum.getEnum(source.getCustomerOrderSource()).getValue());

			destination.setEntrustTypeDesc(EntrustTypeEnum.getEnum(source.getEntrustType()).getValue());

			if (EntrustTypeEnum.LIFE_SALE.getKey().equals(source.getEntrustType())) {
				destination.setOutStatusLabel(CarrierOrderOutStatusEnum.getEnum(source.getOutStatus()).getValue());
			}else{
				destination.setOutStatus(CommonConstant.BLANK_TEXT);
				destination.setOutStatusLabel(CommonConstant.BLANK_TEXT);
			}
		}
	}
}
