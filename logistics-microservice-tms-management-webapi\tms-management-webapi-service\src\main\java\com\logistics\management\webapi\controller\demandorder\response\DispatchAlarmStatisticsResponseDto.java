package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2021/10/18 16:21
 */
@Data
public class DispatchAlarmStatisticsResponseDto {
    @ApiModelProperty("发货省份")
    private String loadProvinceName="";
    @ApiModelProperty("发货市")
    private String loadCityName="";
    @ApiModelProperty("负责人")
    private String loadRegionContactName="";
    @ApiModelProperty("数量（需求单货物数量）")
    private String goodsAmount="";
    @ApiModelProperty("下单数（需求单数）")
    private String demandOrderCount="";
}
