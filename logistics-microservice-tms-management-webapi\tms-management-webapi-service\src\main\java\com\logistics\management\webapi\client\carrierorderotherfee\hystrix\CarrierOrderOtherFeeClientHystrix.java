package com.logistics.management.webapi.client.carrierorderotherfee.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.carrierorderotherfee.CarrierOrderOtherFeeClient;
import com.logistics.management.webapi.client.carrierorderotherfee.request.*;
import com.logistics.management.webapi.client.carrierorderotherfee.response.CarrierOrderOtherFeeDetailResponseModel;
import com.logistics.management.webapi.client.carrierorderotherfee.response.GetOtherFeeRecordResponseModel;
import com.logistics.management.webapi.client.carrierorderotherfee.response.SearchOtherFeeListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/19 13:28
 */
@Component
public class CarrierOrderOtherFeeClientHystrix implements CarrierOrderOtherFeeClient {
    @Override
    public Result<PageInfo<SearchOtherFeeListResponseModel>> searchList(SearchOtherFeeListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addCarrierOrderOtherFee(AddCarrierOrderOtherFeeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierOrderOtherFeeDetailResponseModel> getCarrierOrderOtherFeeDetail(CarrierOrderOtherFeeDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> commitCarrierOrderOtherFee(CommitCarrierOrderOtherFeeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> rejectOtherFee(RejectCarrierOrderOtherFeeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> cancelOtherFee(CarrierOrderOtherFeeDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetOtherFeeRecordResponseModel>> getOtherFeeRecord(CarrierOrderOtherFeeDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> rollbackOtherFee(CarrierOrderOtherFeeDetailRequestModel requestModel) {
        return Result.timeout();
    }
}
