package com.logistics.management.webapi.base.enums;

public enum EntrustTypeEnum {
    DEFAULT(0, "", "非云盘单子"),
    DELIVER(1, "发货", "发货"),
    RECYCLE_IN(2, "回收入库", "回收入库"),
    PROCUREMENT(3, "采购", "采购"),
    TRANSFERS(4, "调拨", "调拨"),
    LEYI_PUBLISH(5, "", "云盘发布托盘订单"),
    BOOKING(6, "预约", "预约"),
    RETURN_GOODS(7, "退货", "退货"),
    SUPPLIER_DIRECT_DISTRIBUTION(9, "供应商直配", "供应商直配"),
    RECYCLE_OUT(10, "回收出库", "回收出库"),
    RETURN_GOODS_DISTRIBUTION(11, "退货仓库配送", "退货仓库配送"),
    RETURN_GOODS_TRANSFERS(12, "退货调拨", "退货调拨"),
    LIFE_TRANSFER(100, "新生回收", "新生回收"),
    LIFE_SALE(101, "新生销售", "新生销售"),
    ;

    private Integer key;
    private String value;
    private String remark;

    EntrustTypeEnum(Integer key, String value, String remark) {
        this.key = key;
        this.value = value;
        this.remark = remark;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getRemark() {
        return remark;
    }

    public static EntrustTypeEnum getEnum(Integer key) {
        for (EntrustTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
