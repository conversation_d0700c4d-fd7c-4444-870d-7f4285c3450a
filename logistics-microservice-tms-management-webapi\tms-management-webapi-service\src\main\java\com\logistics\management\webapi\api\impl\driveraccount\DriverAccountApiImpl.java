package com.logistics.management.webapi.api.impl.driveraccount;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.driveraccount.DriverAccountApi;
import com.logistics.management.webapi.api.feign.driveraccount.dto.request.*;
import com.logistics.management.webapi.api.feign.driveraccount.dto.response.DriverAccountDetailResponseDto;
import com.logistics.management.webapi.api.feign.driveraccount.dto.response.DriverAccountImageResponseDto;
import com.logistics.management.webapi.api.feign.driveraccount.dto.response.DriverAccountOperateLogResponseDto;
import com.logistics.management.webapi.api.feign.driveraccount.dto.response.SearchDriverAccountResponseDto;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.driveraccount.mapping.DriverAccountGetAccountImageListMapping;
import com.logistics.management.webapi.api.impl.driveraccount.mapping.DriverAccountGetDetailMapping;
import com.logistics.management.webapi.api.impl.driveraccount.mapping.DriverAccountSearchListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.tms.api.feign.driveraccount.DriverAccountServiceApi;
import com.logistics.tms.api.feign.driveraccount.model.request.*;
import com.logistics.tms.api.feign.driveraccount.model.response.DriverAccountDetailResponseModel;
import com.logistics.tms.api.feign.driveraccount.model.response.DriverAccountImageResponseModel;
import com.logistics.tms.api.feign.driveraccount.model.response.DriverAccountOperateLogResponseModel;
import com.logistics.tms.api.feign.driveraccount.model.response.SearchDriverAccountResponseModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
public class DriverAccountApiImpl implements DriverAccountApi {

    @Autowired
    private DriverAccountServiceApi driverAccountServiceApi;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 新增/修改司机账户
     *
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addAccount(@RequestBody @Valid DriverAccountAddRequestDto requestDto) {
        Result<Boolean> result = driverAccountServiceApi.addAccount(MapperUtils.mapper(requestDto, DriverAccountAddRequestModel.class));
        result.throwException();
        return result;
    }

    /**
     * 查询司机账户列表
     *
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchDriverAccountResponseDto>> searchList(@RequestBody SearchDriverAccountRequestDto requestDto) {
        Result<PageInfo<SearchDriverAccountResponseModel>> result = driverAccountServiceApi.searchList(MapperUtils.mapper(requestDto, SearchDriverAccountRequestModel.class));
        result.throwException();
        PageInfo pageData = result.getData();
        pageData.setList(MapperUtils.mapper(pageData.getList(), SearchDriverAccountResponseDto.class, new DriverAccountSearchListMapping()));
        return Result.success(pageData);
    }

    /**
     * 查询司机账户详情
     *
     * @param requestDto
     * @return
     */
    @Override
    public Result<DriverAccountDetailResponseDto> getDetail(@RequestBody @Valid DriverAccountDetailRequestDto requestDto) {
        Result<DriverAccountDetailResponseModel> result = driverAccountServiceApi.getDetail(MapperUtils.mapper(requestDto, DriverAccountDetailRequestModel.class));
        result.throwException();
        Map<String, String> imageMap = new HashMap<>();
        if (result.getData() != null) {
            List<String> bankAccountImages = result.getData().getBankAccountImages();
            List<String> sourceSrcList = new ArrayList<>();
            if (ListUtils.isNotEmpty(bankAccountImages)) {
                sourceSrcList.addAll(bankAccountImages);
            }
            imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        }
        return Result.success(MapperUtils.mapper(result.getData(), DriverAccountDetailResponseDto.class, new DriverAccountGetDetailMapping(configKeyConstant, imageMap)));
    }

    /**
     * 查询司机账户收款证件
     *
     * @param requestDto
     * @return
     */
    @Override
    public Result<DriverAccountImageResponseDto> getAccountImageList(@RequestBody @Valid DriverAccountImageRequestDto requestDto) {
        Result<DriverAccountImageResponseModel> result = driverAccountServiceApi.getAccountImageList(MapperUtils.mapper(requestDto, DriverAccountImageRequestModel.class));
        result.throwException();
        List<String> bankAccountImages = result.getData().getImagePaths();
        List<String> sourceSrcList = new ArrayList<>();
        if (ListUtils.isNotEmpty(bankAccountImages)) {
            sourceSrcList.addAll(bankAccountImages);
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(), DriverAccountImageResponseDto.class, new DriverAccountGetAccountImageListMapping(configKeyConstant, imageMap)));
    }

    /**
     * 操作日志列表
     *
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<DriverAccountOperateLogResponseDto>> getOperateLogList(@RequestBody @Valid DriverAccountOperateLogRequestDto requestDto) {
        Result<List<DriverAccountOperateLogResponseModel>> result = driverAccountServiceApi.getOperateLogList(MapperUtils.mapper(requestDto, DriverAccountOperateLogRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), DriverAccountOperateLogResponseDto.class));
    }
}
