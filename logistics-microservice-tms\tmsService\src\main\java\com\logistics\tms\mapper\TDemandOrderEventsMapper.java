package com.logistics.tms.mapper;


import com.logistics.tms.controller.demandorder.response.DemandOrderEventResponseModel;
import com.logistics.tms.entity.TDemandOrderEvents;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TDemandOrderEventsMapper extends BaseMapper<TDemandOrderEvents> {

    int batchInsertSelective(@Param("list") List<TDemandOrderEvents> list);

    List<DemandOrderEventResponseModel> getDemandOrderEventsByDemandId(@Param("demandId") Long demandId, @Param("companyCarrierId") Long companyCarrierId);
}