package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DemandCarrierOrderListGoodsInfoModel {
    @ApiModelProperty("货物ID")
    private Long  goodsId;
    @ApiModelProperty("货物")
    private String  goodsName;
    @ApiModelProperty("规格")
    private Integer length;
    @ApiModelProperty("规格")
    private Integer width;
    @ApiModelProperty("规格")
    private Integer height;
    @ApiModelProperty("预提件数")
    private BigDecimal expectAmount;
    @ApiModelProperty("装货件数")
    private BigDecimal loadAmount;
    @ApiModelProperty("卸货件数")
    private BigDecimal unloadAmount;
    @ApiModelProperty("签收件数")
    private BigDecimal signAmount;
}
