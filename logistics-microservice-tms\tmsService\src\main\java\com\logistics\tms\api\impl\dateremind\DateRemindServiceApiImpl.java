package com.logistics.tms.api.impl.dateremind;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.dateremind.DateRemindServiceApi;
import com.logistics.tms.api.feign.dateremind.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.dateremind.DateRemindBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/5/31 8:51
 */
@RestController
public class DateRemindServiceApiImpl implements DateRemindServiceApi {

    @Autowired
    private DateRemindBiz dateRemindBiz;

    /**
     * 日期提醒列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<DateRemindListResponseModel>> searchDateRemindList(@RequestBody DateRemindListRequestModel requestModel) {
        return Result.success(dateRemindBiz.searchDateRemindList(requestModel));
    }

    /**
     * 日期提醒详情
     * @param responseModel
     * @return
     */
    @Override
    public Result<DateRemindDetailResponseModel> dateRemindDetail(@RequestBody DateRemindDetailRequestModel responseModel) {
        return Result.success(dateRemindBiz.getDateRemindDetail(responseModel));
    }

    /**
     * 日期提醒保存/修改
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> saveOrModifyDateRemind(@RequestBody SaveOrModifyDateRemindRequestModel requestModel) {
        dateRemindBiz.saveOrModifyDateRemind(requestModel);
        return Result.success(true);
    }

    /**
     * 日期统一提醒
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> unifiedDateRemind(@RequestBody UnifiedDateRemindRequestModel requestModel) {
        dateRemindBiz.unifiedDateRemind(requestModel);
        return Result.success(true);
    }

    /**
     * 导出
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<DateRemindListResponseModel>> export(@RequestBody DateRemindListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        PageInfo<DateRemindListResponseModel>  pageInfo = dateRemindBiz.searchDateRemindList(requestModel);
        return Result.success(pageInfo.getList());
    }
}
