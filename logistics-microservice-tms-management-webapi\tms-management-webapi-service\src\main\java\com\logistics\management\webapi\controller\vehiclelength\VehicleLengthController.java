package com.logistics.management.webapi.controller.vehiclelength;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.ConvertPageInfoUtil;
import com.logistics.management.webapi.client.vehiclelength.VehicleLengthClient;
import com.logistics.management.webapi.client.vehiclelength.request.AddOrUpdateVehicleLengthRequestModel;
import com.logistics.management.webapi.client.vehiclelength.request.SearchVehicleLengthListRequestModel;
import com.logistics.management.webapi.client.vehiclelength.request.VehicleLengthDetailRequestModel;
import com.logistics.management.webapi.client.vehiclelength.response.SearchVehicleLengthListResponseModel;
import com.logistics.management.webapi.client.vehiclelength.response.SelectVehicleLengthListResponseModel;
import com.logistics.management.webapi.client.vehiclelength.response.VehicleLengthDetailResponseModel;
import com.logistics.management.webapi.controller.vehiclelength.mapping.SearchVehicleLengthListMapping;
import com.logistics.management.webapi.controller.vehiclelength.mapping.SelectVehicleLengthListMapping;
import com.logistics.management.webapi.controller.vehiclelength.mapping.VehicleLengthDetailMapping;
import com.logistics.management.webapi.controller.vehiclelength.request.AddOrUpdateVehicleLengthRequestDto;
import com.logistics.management.webapi.controller.vehiclelength.request.SearchVehicleLengthListRequestDto;
import com.logistics.management.webapi.controller.vehiclelength.request.VehicleLengthDetailRequestDto;
import com.logistics.management.webapi.controller.vehiclelength.response.SearchVehicleLengthListResponseDto;
import com.logistics.management.webapi.controller.vehiclelength.response.SelectVehicleLengthListResponseDto;
import com.logistics.management.webapi.controller.vehiclelength.response.VehicleLengthDetailResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 车长配置管理
 * <AUTHOR>
 * @date:2024/4/26 9:20
 */
@Api(value = "API-vehicleLength-车长配置管理", tags = "车长配置管理")
@RestController
@RequestMapping(value = "/api/vehicleLength")
public class VehicleLengthController {

    @Resource
    private VehicleLengthClient vehicleLengthClient;


    /**
     * 分页搜索车长配置 v3.20.0
     * @param requestDto 请求参数
     * @return 车长配置
     */
    @PostMapping(value = "/searchVehicleLengthList")
    @ApiOperation(value = "分页搜索车长配置", tags = "3.20.0")
    public Result<PageInfo<SearchVehicleLengthListResponseDto>> searchVehicleLengthList(
            @RequestBody @Valid SearchVehicleLengthListRequestDto requestDto){
        SearchVehicleLengthListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchVehicleLengthListRequestModel.class);
        Result<PageInfo<SearchVehicleLengthListResponseModel>> result = vehicleLengthClient.searchVehicleLengthList(requestModel);
        result.throwException();
        PageInfo<SearchVehicleLengthListResponseDto> pageInfo = ConvertPageInfoUtil.convertPageInfo(result.getData(), SearchVehicleLengthListResponseDto.class, new SearchVehicleLengthListMapping());
        return Result.success(pageInfo);
    }


    /**
     * 车长配置详情 v3.20.0
     * @param requestDto 请求参数
     * @return 车长配置详情
     */
    @PostMapping(value = "/vehicleLengthDetail")
    @ApiOperation(value = "车长配置详情", tags = "3.20.0")
    public Result<VehicleLengthDetailResponseDto> vehicleLengthDetail(
            @RequestBody @Valid VehicleLengthDetailRequestDto requestDto){
        Result<VehicleLengthDetailResponseModel> result = vehicleLengthClient.vehicleLengthDetail(MapperUtils.mapper(requestDto, VehicleLengthDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), VehicleLengthDetailResponseDto.class, new VehicleLengthDetailMapping()));
    }

    /**
     * 新增编辑车长 v3.20.0
     * @param requestDto 请求参数
     * @return boolean
     */
    @PostMapping(value = "/addOrUpdateVehicleLength")
    @ApiOperation(value = "新增编辑车长", tags = "3.20.0")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addOrUpdateVehicleLength(
            @RequestBody @Valid AddOrUpdateVehicleLengthRequestDto requestDto){
        //承运范围校验
        if (ConverterUtils.toBigDecimal(requestDto.getCarriageScopeMax()).compareTo(ConverterUtils.toBigDecimal(requestDto.getCarriageScopeMin())) < CommonConstant.INTEGER_ZERO){
            throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
        }
        return vehicleLengthClient.addOrUpdateVehicleLength(MapperUtils.mapper(requestDto, AddOrUpdateVehicleLengthRequestModel.class));
    }

    /**
     * 删除车长 v3.20.0
     * @param requestDto 请求参数
     * @return boolean
     */
    @PostMapping(value = "/delVehicleLength")
    @ApiOperation(value = "删除车长", tags = "3.20.0")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> delVehicleLength(
            @RequestBody @Valid VehicleLengthDetailRequestDto requestDto){
        return vehicleLengthClient.delVehicleLength(MapperUtils.mapper(requestDto, VehicleLengthDetailRequestModel.class));
    }

    /**
     * 下拉选择车长 v3.20.0
     * @return 下拉选择车长
     */
    @PostMapping(value = "/selectVehicleLengthList")
    @ApiOperation(value = "下拉选择车长", tags = "3.20.0")
    public Result<List<SelectVehicleLengthListResponseDto>> selectVehicleLengthList(){
        Result<List<SelectVehicleLengthListResponseModel>> result = vehicleLengthClient.selectVehicleLengthList();
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SelectVehicleLengthListResponseDto.class, new SelectVehicleLengthListMapping()));
    }

}
