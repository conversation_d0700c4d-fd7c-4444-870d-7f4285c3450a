package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/7/22 9:34
 */
@Data
public class YeloLifeCarrierOrderStockOutModel {

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("出库状态：1 部分出库，2 已出库")
    private Integer outStatus;

    @ApiModelProperty("货物对应的数量")
    private List<YeloLifeCarrierOrderGoodsStockOutModel> goodsModels;


    @ApiModelProperty("操作人姓名")
    private String operatorName;
}
