package com.logistics.management.webapi.controller.reservebalance.mapping;

import com.logistics.management.webapi.controller.reservebalance.dto.response.DriverReserveBalanceListResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.enums.StaffPropertyEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.reservebalance.model.response.DriverReserveBalanceListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.math.BigDecimal;

public class DriverReserveBalanceListMapping extends MapperMapping<DriverReserveBalanceListResponseModel, DriverReserveBalanceListResponseDto> {

    private boolean isExport = false;

    public DriverReserveBalanceListMapping() {
    }

    public DriverReserveBalanceListMapping(boolean isExport) {
        this.isExport = isExport;
    }

    @Override
    public void configure() {

        DriverReserveBalanceListResponseModel source = getSource();
        DriverReserveBalanceListResponseDto destination = getDestination();

        // 机构
        destination.setDriverPropertyLabel(StaffPropertyEnum.getEnum(source.getDriverProperty()).getValue());

        // 司机名称
        String mobile = isExport ? source.getDriverMobile() : FrequentMethodUtils.encryptionData(source.getDriverMobile(), EncodeTypeEnum.MOBILE_PHONE);
        String driverName = String.format(CommonConstant.NAME_MOBILE_FORMAT, source.getDriverName(), mobile);
        destination.setDriverName(driverName);

        // 金额处理
        destination.setBalanceAmount(amountConversion(source.getBalanceAmount()));
        destination.setVerificationAmount(amountConversion(source.getVerificationAmount()));
        destination.setAwaitVerificationAmount(amountConversion(source.getAwaitVerificationAmount()));
    }

    private String amountConversion(BigDecimal amount) {
        return amount.stripTrailingZeros().toPlainString();
    }
}
