package com.logistics.tms.base.enums;

/**
 * @Author: sj
 * @Date: 2019/11/8 15:10
 */
public enum SafeCheckItemStatusEnum {
    NULL(-1,""),
    NOT_CHECK(0,"未检查"),
    QUALIFIED(1, "合格"),
    UNQUALIFIED(2,"不合格")
    ;

    private Integer key;
    private String value;

    SafeCheckItemStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
