<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TSinopecMessageLogMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TSinopecMessageLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="message_type" property="messageType" jdbcType="INTEGER" />
    <result column="message_action" property="messageAction" jdbcType="INTEGER" />
    <result column="message_body" property="messageBody" jdbcType="VARCHAR" />
    <result column="message_ip" property="messageIp" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, message_type, message_action, message_body, message_ip, created_by, created_time, 
    last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_sinopec_message_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_sinopec_message_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TSinopecMessageLog" >
    insert into t_sinopec_message_log (id, message_type, message_action, 
      message_body, message_ip, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{messageType,jdbcType=INTEGER}, #{messageAction,jdbcType=INTEGER}, 
      #{messageBody,jdbcType=VARCHAR}, #{messageIp,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TSinopecMessageLog" >
    insert into t_sinopec_message_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="messageType != null" >
        message_type,
      </if>
      <if test="messageAction != null" >
        message_action,
      </if>
      <if test="messageBody != null" >
        message_body,
      </if>
      <if test="messageIp != null" >
        message_ip,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="messageType != null" >
        #{messageType,jdbcType=INTEGER},
      </if>
      <if test="messageAction != null" >
        #{messageAction,jdbcType=INTEGER},
      </if>
      <if test="messageBody != null" >
        #{messageBody,jdbcType=VARCHAR},
      </if>
      <if test="messageIp != null" >
        #{messageIp,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TSinopecMessageLog" >
    update t_sinopec_message_log
    <set >
      <if test="messageType != null" >
        message_type = #{messageType,jdbcType=INTEGER},
      </if>
      <if test="messageAction != null" >
        message_action = #{messageAction,jdbcType=INTEGER},
      </if>
      <if test="messageBody != null" >
        message_body = #{messageBody,jdbcType=VARCHAR},
      </if>
      <if test="messageIp != null" >
        message_ip = #{messageIp,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TSinopecMessageLog" >
    update t_sinopec_message_log
    set message_type = #{messageType,jdbcType=INTEGER},
      message_action = #{messageAction,jdbcType=INTEGER},
      message_body = #{messageBody,jdbcType=VARCHAR},
      message_ip = #{messageIp,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>