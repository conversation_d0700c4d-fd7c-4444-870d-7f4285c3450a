package com.logistics.tms.mapper;

import com.logistics.tms.biz.settlestatement.model.CarrierSettleStatementDetailModel;
import com.logistics.tms.controller.invoicingmanagement.request.GetAddSettleStatementListRequestModel;
import com.logistics.tms.controller.invoicingmanagement.response.GetAddSettleStatementListResponseModel;
import com.logistics.tms.controller.settlestatement.packaging.request.CarrierSettleStatementListRequestModel;
import com.logistics.tms.controller.settlestatement.packaging.response.CarrierAdjustCostResponseModel;
import com.logistics.tms.controller.settlestatement.packaging.response.CarrierSettleStatementDetailTotalResponseModel;
import com.logistics.tms.controller.settlestatement.packaging.response.CarrierSettleStatementListResponseModel;
import com.logistics.tms.entity.TSettleStatement;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Mybatis Generator on 2022/11/10
 */
@Mapper
public interface TSettleStatementMapper extends BaseMapper<TSettleStatement> {

	List<Long> carrierSettleStatementIdList(@Param("params") CarrierSettleStatementListRequestModel requestModel, @Param("type") Integer type);

	List<CarrierSettleStatementListResponseModel> carrierSettleStatementList(@Param("ids") String ids);

	CarrierSettleStatementDetailTotalResponseModel selectDetailById(@Param("settleStatementId") Long settleStatementId,
																	@Param("companyCarrierId") Long companyCarrierId,
																	@Param("settleStatementType") Integer settleStatementType);

	CarrierAdjustCostResponseModel queryAdjustCost(@Param("settleStatementId") Long settleStatementId);

	List<CarrierSettleStatementDetailModel> getDetailByIds(@Param("ids") String ids, @Param("type") Integer type);

	List<Long> getAddSettleStatementIdList(@Param("params") GetAddSettleStatementListRequestModel requestModel, @Param("type") Integer type);
	List<GetAddSettleStatementListResponseModel> getAddSettleStatementList(@Param("ids") String ids);
}