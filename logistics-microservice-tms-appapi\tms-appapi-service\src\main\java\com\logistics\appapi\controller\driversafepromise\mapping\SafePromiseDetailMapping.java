package com.logistics.appapi.controller.driversafepromise.mapping;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.SafePromiseStatusEnum;
import com.logistics.appapi.client.driversafepromise.response.SafePromiseAppletDetailResponseModel;
import com.logistics.appapi.controller.driversafepromise.response.SafePromiseDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.Map;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/11/25
 * @description:
 */
public class SafePromiseDetailMapping extends MapperMapping<SafePromiseAppletDetailResponseModel, SafePromiseDetailResponseDto> {


    private String imagePrefix;
    private Map<String, String> imageMap;

    public SafePromiseDetailMapping(String imagePrefix,Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        SafePromiseAppletDetailResponseModel source = getSource();
        SafePromiseDetailResponseDto destination = getDestination();
        //转换状态
        //拼接标题
        if (StringUtils.isNotBlank(source.getPeriod())) {
            destination.setPeriodLabel(source.getPeriod() + CommonConstant.PROMISE_PERIOD);
        }
        if (source.getStatus() != null) {
            destination.setStatusLabel(SafePromiseStatusEnum.getEnum(source.getStatus()).getValue());
        }
        //转换时间
        if (source.getPublishTime() != null) {
            destination.setPublishTime(DateUtils.dateToString(source.getPublishTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if (StringUtils.isNotBlank(source.getHandPromiseUrl())) {
            destination.setAbsoluteHandPromiseUrl(imagePrefix + imageMap.get(source.getHandPromiseUrl()));
        }
        if (StringUtils.isNotBlank(source.getSignResponsibilityUrl())) {
            destination.setAbsoluteSignResponsibilityUrl(imagePrefix + imageMap.get(source.getSignResponsibilityUrl()));
        }
        if (StringUtils.isNotBlank(source.getAttachmentUrl())) {
            destination.setAbsoluteAttachmentUrl(imagePrefix + imageMap.get(source.getAttachmentUrl()));
        }
    }


}
