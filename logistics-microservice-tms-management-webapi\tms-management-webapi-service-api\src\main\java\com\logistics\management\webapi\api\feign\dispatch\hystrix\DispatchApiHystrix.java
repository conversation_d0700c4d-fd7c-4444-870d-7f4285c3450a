package com.logistics.management.webapi.api.feign.dispatch.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.dispatch.DispatchApi;
import com.logistics.management.webapi.api.feign.dispatch.dto.*;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DispatchApiHystrix implements DispatchApi {


    @Override
    public Result<List<DriverAndVehicleSearchResponseDto>> searchDriverAndVehicle(DriverAndVehicleSearchRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<List<DemandOrderDispatchResponseDto>> getDispatchDetail(DemandOrderDispatchRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> saveCompleteDispatch(CompleteDemandOrderRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<DriverSearchResponseDto>> searchDriver(DriverSearchRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<VehicleSearchResponseDto>> searchVehicle(VehicleSearchRequestDto requestDto) {
        return Result.timeout();
    }
}
