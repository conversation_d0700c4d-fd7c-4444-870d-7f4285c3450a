package com.logistics.management.webapi.api.feign.extvehiclesettlement.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2019/11/20 14:24
 */
@Data
public class ExtVehicleSettlementFallbackRequestDto {
    @ApiModelProperty("外部车辆结算ID")
    @NotBlank(message = "id不能为空")
    private String extVehicleSettlementId;
    @ApiModelProperty("回退理由")
    @NotBlank(message = "请填写回退理由")
    private String fallbackReason;
}
