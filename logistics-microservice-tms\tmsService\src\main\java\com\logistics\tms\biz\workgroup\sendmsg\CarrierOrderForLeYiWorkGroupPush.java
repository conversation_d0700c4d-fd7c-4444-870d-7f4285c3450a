package com.logistics.tms.biz.workgroup.sendmsg;

import com.google.common.collect.Maps;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.WorkGroupAmountTypeEnum;
import com.logistics.tms.base.enums.WorkGroupOrderTypeEnum;
import com.logistics.tms.biz.carrierorder.CarrierOrderForLeYiBiz;
import com.logistics.tms.biz.workgroup.sendmsg.model.WorkGroupCarrierOrderForLeYiBoModel;
import com.logistics.tms.biz.workgroup.sendmsg.model.mapping.WorkGroupCarrierOrderForLeYiMapping;
import com.logistics.tms.controller.carrierorder.response.SearchCarrierOrderListForLeYiResponseModel;
import com.logistics.tms.entity.TCarrierOrderAddress;
import com.logistics.tms.mapper.TCarrierOrderAddressMapper;
import com.logistics.tms.mapper.TCarrierOrderMapper;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
public class CarrierOrderForLeYiWorkGroupPush extends AbstractWorkGroupPush {

    @Resource
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Resource
    private TCarrierOrderAddressMapper tCarrierOrderAddressMapper;
    @Resource
    private CarrierOrderForLeYiBiz carrierOrderForLeYiBiz;


    @Override
    public WorkGroupOrderTypeEnum getOrderType() {
        return WorkGroupOrderTypeEnum.CARRIER_ORDER;
    }

    @Override
    protected WorkGroupAddress getOrderAddress(Long orderId) {
        TCarrierOrderAddress tCarrierOrderAddress = tCarrierOrderAddressMapper.getByCarrierOrderId(orderId);
        return new WorkGroupAddress()
                .setLoadProvinceId(tCarrierOrderAddress.getLoadProvinceId())
                .setLoadCityId(tCarrierOrderAddress.getLoadCityId())
                .setLoadWarehouse(tCarrierOrderAddress.getLoadWarehouse())
                .setUnloadProvinceId(tCarrierOrderAddress.getUnloadProvinceId())
                .setUnloadCityId(tCarrierOrderAddress.getUnloadCityId())
                .setUnloadWarehouse(tCarrierOrderAddress.getUnloadWarehouse());
    }

    @Override
    protected Map<Integer, BigDecimal> businessParamFilter(Long orderId) {
        return Optional.ofNullable(tCarrierOrderMapper.selectByPrimaryKeyDecrypt(orderId))
                .map(c -> {
                    Map<Integer, BigDecimal> typeAmountMap = Maps.newHashMap();
                    typeAmountMap.put(WorkGroupAmountTypeEnum.EXPECT_AMOUNT.getKey(), c.getExpectAmount());
                    typeAmountMap.put(WorkGroupAmountTypeEnum.LOAD_AMOUNT.getKey(), c.getLoadAmount());
                    typeAmountMap.put(WorkGroupAmountTypeEnum.DIFFERENCE_AMOUNT.getKey(), c.getExpectAmount().subtract(c.getLoadAmount()));
                    return typeAmountMap;
                })
                .orElse(new HashMap<>());
    }

    @Override
    protected WorkGroupCarrierOrderForLeYiBoModel getOrderDetail(Long orderId) {
        List<SearchCarrierOrderListForLeYiResponseModel> list =
                carrierOrderForLeYiBiz.searchCarrierOrderListForLeYiByIds(Collections.singletonList(orderId), new HashMap<>());
        if (ListUtils.isEmpty(list)) {
            return null;
        }
        // 转换
        SearchCarrierOrderListForLeYiResponseModel model = list.get(CommonConstant.INTEGER_ZERO);
        return MapperUtils.mapper(model, WorkGroupCarrierOrderForLeYiBoModel.class, new WorkGroupCarrierOrderForLeYiMapping());
    }
}
