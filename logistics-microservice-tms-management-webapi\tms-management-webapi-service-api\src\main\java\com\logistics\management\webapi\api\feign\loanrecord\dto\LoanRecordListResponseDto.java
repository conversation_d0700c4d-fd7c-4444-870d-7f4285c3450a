package com.logistics.management.webapi.api.feign.loanrecord.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @Author: sj
 * @Date: 2019/9/30 9:37
 */
@Data
public class LoanRecordListResponseDto {
    @ApiModelProperty("贷款记录ID")
    private String loanRecordId = "";
    @ApiModelProperty("贷款状态：0 待结算，1 部分结算，2 已结算")
    private String status = "";
    @ApiModelProperty("贷款状态文本")
    private String statusLabel = "";
    @ApiModelProperty("车辆ID")
    private String vehicleId = "";
    @ApiModelProperty("车牌号")
    private String vehicleNo = "";
    @ApiModelProperty("品牌")
    private String brand = "";
    @ApiModelProperty("型号")
    private String model = "";
    @ApiModelProperty("品牌型号")
    private String brandModel = "";
    @ApiModelProperty("司机id")
    private String staffId = "";
    @ApiModelProperty("司机姓名")
    private String name = "";
    @ApiModelProperty("手机号")
    private String mobile = "";
    @ApiModelProperty("司机文本：姓名+手机号拼接")
    private String driverLabel = "";
    @ApiModelProperty("购车总价")
    private String carPrice = "";
    @ApiModelProperty("总贷款费用")
    private String loanFee = "";
    @ApiModelProperty("贷款总期数")
    private String loanPeriods = "";
    @ApiModelProperty("贷款利率")
    private String loanRate = "";
    @ApiModelProperty("贷款利息")
    private String loanInterest = "";
    @ApiModelProperty("贷款手续费")
    private String loanCommission = "";
    @ApiModelProperty("剩余金额")
    private String remainingRepaymentFee = "";
    @ApiModelProperty("备注")
    private String remark = "";
    @ApiModelProperty("操作人")
    private String lastModifiedBy = "";
    @ApiModelProperty("操作时间")
    private String lastModifiedTime = "";
}
