package com.logistics.appapi.client.reserveapply.hystrix;

import com.logistics.appapi.client.reserveapply.DriverReserveApplyClient;
import com.logistics.appapi.client.reserveapply.request.ApplyReserveBalanceRequestModel;
import com.logistics.appapi.client.reserveapply.request.ReserveBalanceApplyCancelRequestModel;
import com.logistics.appapi.client.reserveapply.request.ReserveBalanceApplyDetailRequestModel;
import com.logistics.appapi.client.reserveapply.request.ReserveBalanceApplyListRequestModel;
import com.logistics.appapi.client.reserveapply.response.ReserveBalanceApplyDetailResponseModel;
import com.logistics.appapi.client.reserveapply.response.ReserveBalanceApplyListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

@Component
public class DriverReserveApplyClientHystrix implements DriverReserveApplyClient {

    @Override
    public Result<Boolean> applyReserveBalance(ApplyReserveBalanceRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ReserveBalanceApplyListResponseModel> reserveBalanceApplyList(ReserveBalanceApplyListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ReserveBalanceApplyDetailResponseModel> reserveBalanceApplyDetail(ReserveBalanceApplyDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> cancel(ReserveBalanceApplyCancelRequestModel requestModel) {
        return Result.timeout();
    }
}
