package com.logistics.tms.mapper;

import com.logistics.tms.controller.workordercenter.response.WorkOrderProcessResponseModel;
import com.logistics.tms.entity.TWorkOrderProcess;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2023/04/14
*/
@Mapper
public interface TWorkOrderProcessMapper extends BaseMapper<TWorkOrderProcess> {

	List<WorkOrderProcessResponseModel> searchProcessListByOrderId(@Param("workOrderId") Long workOrderId);
}