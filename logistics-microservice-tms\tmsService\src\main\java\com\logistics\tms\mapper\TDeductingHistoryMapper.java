package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TDeductingHistory;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TDeductingHistoryMapper extends BaseMapper<TDeductingHistory> {

    List<TDeductingHistory> getByObjectTypeId(@Param("objectType")Integer objectType,@Param("objectId")Long objectId);

    int batchInsert(@Param("list")List<TDeductingHistory> list);

    int batchUpdate(@Param("list")List<TDeductingHistory> list);

    TDeductingHistory getByTypeObjectIdMonth(@Param("objectType") Integer key, @Param("objectId") Long objectId, @Param("settleMonth") String settlementMonth);
}