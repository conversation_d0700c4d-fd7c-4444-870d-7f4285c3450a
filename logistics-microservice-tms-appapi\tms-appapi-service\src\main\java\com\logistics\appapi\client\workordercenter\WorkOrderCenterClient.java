package com.logistics.appapi.client.workordercenter;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.workordercenter.hystrix.WorkOrderCenterClientHystrix;
import com.logistics.appapi.client.workordercenter.request.*;
import com.logistics.appapi.client.workordercenter.response.WorkOrderDetailAppletResponseModel;
import com.logistics.appapi.client.workordercenter.response.WorkOrderListAppletResponseModel;
import com.logistics.appapi.client.workordercenter.response.WorkOrderProcessAppletResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/7 14:07
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        fallback = WorkOrderCenterClientHystrix.class)
public interface WorkOrderCenterClient {

    /**
     * 工单列表
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "工单列表")
    @PostMapping(value = "/service/applet/workOrderCenter/workOrderListForApplet")
    Result<PageInfo<WorkOrderListAppletResponseModel>> workOrderListForApplet(@RequestBody WorkOrderListAppletRequestModel requestModel);

    /**
     * 工单详情
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "工单详情")
    @PostMapping(value = "/service/applet/workOrderCenter/workOrderDetailForApplet")
    Result<WorkOrderDetailAppletResponseModel> workOrderDetailForApplet(@RequestBody WorkOrderDetailAppletRequestModel requestModel);

    /**
     * 工单处理过程列表
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "工单处理过程列表")
    @PostMapping(value = "/service/applet/workOrderCenter/workOrderProcessForApplet")
    Result<List<WorkOrderProcessAppletResponseModel>> workOrderProcessForApplet(@RequestBody WorkOrderDetailAppletRequestModel requestModel);

    /**
     * 撤销工单
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "撤销工单")
    @PostMapping(value = "/service/workOrderCenter/cancelWorkOrder")
    Result<Boolean> cancelWorkOrder(@RequestBody CancelWorkOrderRequestModel requestModel);

    /**
     * 上报任务中心异常工单
     *
     * @param requestModel 请求Model
     * @return boolean
     */
    @ApiOperation(value = "上报任务中心异常工单")
    @PostMapping(value = "/service/workOrderCenter/reportWorkException")
    Result<Boolean> reportWorkException(@RequestBody ReportWorkExceptionRequestModel requestModel);

    /**
     * 重新上报异常
     * @param requestModel 请求Model
     * @return boolean
     */
    @ApiOperation(value = "重新上报任务中心异常工单")
    @PostMapping(value = "/service/workOrderCenter/reReportWorkException")
    Result<Boolean> reReportWorkException(@RequestBody ReReportWorkExceptionRequestModel requestModel);
}
