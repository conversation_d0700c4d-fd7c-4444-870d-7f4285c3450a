package com.logistics.tms.biz.terminalcustomeraddress

import com.logistics.tms.api.feign.terminalcustomeraddress.model.AddOrModifyTerminalCustomerAddressRequestModel
import com.logistics.tms.api.feign.terminalcustomeraddress.model.GetTerminalCustomerAddressDetailRequestModel
import com.logistics.tms.api.feign.terminalcustomeraddress.model.GetTerminalCustomerAddressDetailResponseModel
import com.logistics.tms.api.feign.terminalcustomeraddress.model.SearchTerminalCustomerAddressListRequestModel
import com.logistics.tms.api.feign.terminalcustomeraddress.model.SearchTerminalCustomerAddressListResponseModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TTerminalCustomerAddress
import com.logistics.tms.mapper.TTerminalCustomerAddressMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class TerminalCustomerAddressBizTest extends Specification {
    @Mock
    TTerminalCustomerAddressMapper tTerminalCustomerAddressMapper
    @Mock
    CommonBiz commonBiz
    @InjectMocks
    TerminalCustomerAddressBiz terminalCustomerAddressBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Terminal Customer Address List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tTerminalCustomerAddressMapper.searchTerminalCustomerAddressList(any())).thenReturn([new SearchTerminalCustomerAddressListResponseModel()])

        expect:
        terminalCustomerAddressBiz.searchTerminalCustomerAddressList(requestModel) == expectedResult

        where:
        requestModel                                        || expectedResult
        new SearchTerminalCustomerAddressListRequestModel() || null
    }

    @Unroll
    def "add Or Modify Terminal Customer Address where requestModel=#requestModel"() {
        expect:
        terminalCustomerAddressBiz.addOrModifyTerminalCustomerAddress(requestModel)
        assert expectedResult == false

        where:
        requestModel                                         || expectedResult
        new AddOrModifyTerminalCustomerAddressRequestModel() || true
    }

    @Unroll
    def "get Terminal Customer Address Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tTerminalCustomerAddressMapper.getTerminalCustomerAddressDetail(anyLong())).thenReturn(new GetTerminalCustomerAddressDetailResponseModel())

        expect:
        terminalCustomerAddressBiz.getTerminalCustomerAddressDetail(requestModel) == expectedResult

        where:
        requestModel                                       || expectedResult
        new GetTerminalCustomerAddressDetailRequestModel() || new GetTerminalCustomerAddressDetailResponseModel()
    }

    @Unroll
    def "del Terminal Customer Address where requestModel=#requestModel"() {
        given:
        when(tTerminalCustomerAddressMapper.getByIds(anyString())).thenReturn([new TTerminalCustomerAddress()])

        expect:
        terminalCustomerAddressBiz.delTerminalCustomerAddress(requestModel)
        assert expectedResult == false

        where:
        requestModel                                                                                              || expectedResult
        new com.logistics.tms.api.feign.terminalcustomeraddress.model.DeleteTerminalCustomerAddressRequestModel() || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme