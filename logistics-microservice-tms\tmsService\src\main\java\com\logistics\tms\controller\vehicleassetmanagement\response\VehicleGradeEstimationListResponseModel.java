package com.logistics.tms.controller.vehicleassetmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class VehicleGradeEstimationListResponseModel {
    @ApiModelProperty("等级评定列表Id")
    private Long vehicleGradeEstimationId;
    @ApiModelProperty("等级评定检查日期")
    private Date estimationDate;
    @ApiModelProperty("等级 1 一级 2 耳机 3 三级")
    private Integer grade;
    @ApiModelProperty("凭证列表")
    private List<CertificationPicturesResponseModel> fileList;
    @ApiModelProperty("最后操作人")
    private String lastModifiedBy;
    @ApiModelProperty("最后操作时间")
    private Date lastModifiedTime;
    @ApiModelProperty("备注")
    private String remark;
}
