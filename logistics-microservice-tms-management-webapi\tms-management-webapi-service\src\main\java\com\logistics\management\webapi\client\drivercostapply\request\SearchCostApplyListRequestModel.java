package com.logistics.management.webapi.client.drivercostapply.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/3 13:42
 */
@Data
public class SearchCostApplyListRequestModel extends AbstractPageForm<SearchCostApplyListRequestModel> {

    @ApiModelProperty("审核状态：-1 待业务审核，0 待财务审核，1 已审核，2 已驳回，3 已撤销，4 已红冲")
    private Integer auditStatus;

    @ApiModelProperty("费用类型：100 住宿费，101 装卸费，102 其他费用，103 加班费，104 劳保费，105 电话费，106 交通费， 107 打印费，108 叉车费，109 盖雨布， 110破包赔偿，111 交通罚款；200 维修费，201 车辆保养费，202 过路过桥费，203 停车费，204 尿素费，205 加油费；300 核酸检测费，301 医疗防护用品；400 扣款")
    private Integer costType;

    @ApiModelProperty("司机")
    private String staffName;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    @ApiModelProperty("申请时间-起始")
    private String applyTimeStart;

    @ApiModelProperty("申请时间-结束")
    private String applyTimeEnd;

    @ApiModelProperty("审核人")
    private String auditorName;

    @ApiModelProperty("审核时间-开始")
    private String auditTimeStart;

    @ApiModelProperty("审核时间-结束")
    private String auditTimeEnd;

    @ApiModelProperty(value = "申请时间（yyyy-MM）")
    private String applyTime;

    @ApiModelProperty(value = "司机ID")
    private Long staffId;

    @ApiModelProperty("请求来源 1 后台，2 前台 3小程序")
    private Integer source;//1 后台，2 前台 3小程序

    @ApiModelProperty("导出用 费用申请id集合")
    private List<Long> driverCostApplyIds;

    @ApiModelProperty("备用金单号")
    private String reserveCode;

    @ApiModelProperty("发票: 0 无票, 1 有票; 1.3.6 新增")
    private String invoice;
}
