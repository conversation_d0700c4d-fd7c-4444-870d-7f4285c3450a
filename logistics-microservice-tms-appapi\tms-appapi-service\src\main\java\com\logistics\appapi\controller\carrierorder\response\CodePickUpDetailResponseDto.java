package com.logistics.appapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;



@Data
public class CodePickUpDetailResponseDto {


    @ApiModelProperty(value = "运单Id")
    private String carrierOrderId;

    @ApiModelProperty(value = "预计提货数量")
    private String loadAmountExpect;

    @ApiModelProperty(value = "实际扫码数")
    private String scanAmountActual;

    @ApiModelProperty(value = "编码有误数")
    private String codeErrorCount;

    @ApiModelProperty(value = "实际提货数")
    private String loadAmountActual;


}
