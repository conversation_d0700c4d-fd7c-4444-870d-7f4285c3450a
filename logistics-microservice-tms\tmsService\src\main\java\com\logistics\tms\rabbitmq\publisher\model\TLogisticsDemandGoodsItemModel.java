package com.logistics.tms.rabbitmq.publisher.model;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author:lei.zhu
 * @date:2022/1/4 15:28:29
 */
@Data
public class TLogisticsDemandGoodsItemModel {
    private Long goodsId;

    private BigDecimal fallbackCount;

    private BigDecimal arrangedAmount;

    private BigDecimal notArrangedAmount;

    private String lastModifiedBy;

    private Date lastModifiedTime;
}
