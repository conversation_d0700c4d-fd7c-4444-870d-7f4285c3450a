<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TRenewableAuditAddressMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TRenewableAuditAddress">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="renewable_order_id" jdbcType="BIGINT" property="renewableOrderId" />
    <result column="load_address_code" jdbcType="VARCHAR" property="loadAddressCode" />
    <result column="load_province_id" jdbcType="BIGINT" property="loadProvinceId" />
    <result column="load_province_name" jdbcType="VARCHAR" property="loadProvinceName" />
    <result column="load_city_id" jdbcType="BIGINT" property="loadCityId" />
    <result column="load_city_name" jdbcType="VARCHAR" property="loadCityName" />
    <result column="load_area_id" jdbcType="BIGINT" property="loadAreaId" />
    <result column="load_area_name" jdbcType="VARCHAR" property="loadAreaName" />
    <result column="load_detail_address" jdbcType="VARCHAR" property="loadDetailAddress" />
    <result column="load_warehouse" jdbcType="VARCHAR" property="loadWarehouse" />
    <result column="load_company" jdbcType="VARCHAR" property="loadCompany" />
    <result column="load_longitude" jdbcType="VARCHAR" property="loadLongitude" />
    <result column="load_latitude" jdbcType="VARCHAR" property="loadLatitude" />
    <result column="consignor_name" jdbcType="VARCHAR" property="consignorName" />
    <result column="consignor_mobile" jdbcType="VARCHAR" property="consignorMobile" />
    <result column="expected_load_time" jdbcType="TIMESTAMP" property="expectedLoadTime" />
    <result column="unload_address_code" jdbcType="VARCHAR" property="unloadAddressCode" />
    <result column="unload_province_id" jdbcType="BIGINT" property="unloadProvinceId" />
    <result column="unload_province_name" jdbcType="VARCHAR" property="unloadProvinceName" />
    <result column="unload_city_id" jdbcType="BIGINT" property="unloadCityId" />
    <result column="unload_city_name" jdbcType="VARCHAR" property="unloadCityName" />
    <result column="unload_area_id" jdbcType="BIGINT" property="unloadAreaId" />
    <result column="unload_area_name" jdbcType="VARCHAR" property="unloadAreaName" />
    <result column="unload_detail_address" jdbcType="VARCHAR" property="unloadDetailAddress" />
    <result column="unload_warehouse" jdbcType="VARCHAR" property="unloadWarehouse" />
    <result column="unload_company" jdbcType="VARCHAR" property="unloadCompany" />
    <result column="unload_longitude" jdbcType="VARCHAR" property="unloadLongitude" />
    <result column="unload_latitude" jdbcType="VARCHAR" property="unloadLatitude" />
    <result column="receiver_name" jdbcType="VARCHAR" property="receiverName" />
    <result column="receiver_mobile" jdbcType="VARCHAR" property="receiverMobile" />
    <result column="expected_unload_time" jdbcType="TIMESTAMP" property="expectedUnloadTime" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, renewable_order_id, load_address_code, load_province_id, load_province_name, 
    load_city_id, load_city_name, load_area_id, load_area_name, load_detail_address, 
    load_warehouse, load_company, load_longitude, load_latitude, consignor_name, consignor_mobile, 
    expected_load_time, unload_address_code, unload_province_id, unload_province_name, 
    unload_city_id, unload_city_name, unload_area_id, unload_area_name, unload_detail_address, 
    unload_warehouse, unload_company, unload_longitude, unload_latitude, receiver_name, 
    receiver_mobile, expected_unload_time, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_renewable_audit_address
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_renewable_audit_address
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TRenewableAuditAddress">
    insert into t_renewable_audit_address (id, renewable_order_id, load_address_code, 
      load_province_id, load_province_name, load_city_id, 
      load_city_name, load_area_id, load_area_name, 
      load_detail_address, load_warehouse, load_company, 
      load_longitude, load_latitude, consignor_name, 
      consignor_mobile, expected_load_time, unload_address_code, 
      unload_province_id, unload_province_name, unload_city_id, 
      unload_city_name, unload_area_id, unload_area_name, 
      unload_detail_address, unload_warehouse, unload_company, 
      unload_longitude, unload_latitude, receiver_name, 
      receiver_mobile, expected_unload_time, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{renewableOrderId,jdbcType=BIGINT}, #{loadAddressCode,jdbcType=VARCHAR}, 
      #{loadProvinceId,jdbcType=BIGINT}, #{loadProvinceName,jdbcType=VARCHAR}, #{loadCityId,jdbcType=BIGINT}, 
      #{loadCityName,jdbcType=VARCHAR}, #{loadAreaId,jdbcType=BIGINT}, #{loadAreaName,jdbcType=VARCHAR}, 
      #{loadDetailAddress,jdbcType=VARCHAR}, #{loadWarehouse,jdbcType=VARCHAR}, #{loadCompany,jdbcType=VARCHAR}, 
      #{loadLongitude,jdbcType=VARCHAR}, #{loadLatitude,jdbcType=VARCHAR}, #{consignorName,jdbcType=VARCHAR}, 
      #{consignorMobile,jdbcType=VARCHAR}, #{expectedLoadTime,jdbcType=TIMESTAMP}, #{unloadAddressCode,jdbcType=VARCHAR}, 
      #{unloadProvinceId,jdbcType=BIGINT}, #{unloadProvinceName,jdbcType=VARCHAR}, #{unloadCityId,jdbcType=BIGINT}, 
      #{unloadCityName,jdbcType=VARCHAR}, #{unloadAreaId,jdbcType=BIGINT}, #{unloadAreaName,jdbcType=VARCHAR}, 
      #{unloadDetailAddress,jdbcType=VARCHAR}, #{unloadWarehouse,jdbcType=VARCHAR}, #{unloadCompany,jdbcType=VARCHAR}, 
      #{unloadLongitude,jdbcType=VARCHAR}, #{unloadLatitude,jdbcType=VARCHAR}, #{receiverName,jdbcType=VARCHAR}, 
      #{receiverMobile,jdbcType=VARCHAR}, #{expectedUnloadTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TRenewableAuditAddress">
    insert into t_renewable_audit_address
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="renewableOrderId != null">
        renewable_order_id,
      </if>
      <if test="loadAddressCode != null">
        load_address_code,
      </if>
      <if test="loadProvinceId != null">
        load_province_id,
      </if>
      <if test="loadProvinceName != null">
        load_province_name,
      </if>
      <if test="loadCityId != null">
        load_city_id,
      </if>
      <if test="loadCityName != null">
        load_city_name,
      </if>
      <if test="loadAreaId != null">
        load_area_id,
      </if>
      <if test="loadAreaName != null">
        load_area_name,
      </if>
      <if test="loadDetailAddress != null">
        load_detail_address,
      </if>
      <if test="loadWarehouse != null">
        load_warehouse,
      </if>
      <if test="loadCompany != null">
        load_company,
      </if>
      <if test="loadLongitude != null">
        load_longitude,
      </if>
      <if test="loadLatitude != null">
        load_latitude,
      </if>
      <if test="consignorName != null">
        consignor_name,
      </if>
      <if test="consignorMobile != null">
        consignor_mobile,
      </if>
      <if test="expectedLoadTime != null">
        expected_load_time,
      </if>
      <if test="unloadAddressCode != null">
        unload_address_code,
      </if>
      <if test="unloadProvinceId != null">
        unload_province_id,
      </if>
      <if test="unloadProvinceName != null">
        unload_province_name,
      </if>
      <if test="unloadCityId != null">
        unload_city_id,
      </if>
      <if test="unloadCityName != null">
        unload_city_name,
      </if>
      <if test="unloadAreaId != null">
        unload_area_id,
      </if>
      <if test="unloadAreaName != null">
        unload_area_name,
      </if>
      <if test="unloadDetailAddress != null">
        unload_detail_address,
      </if>
      <if test="unloadWarehouse != null">
        unload_warehouse,
      </if>
      <if test="unloadCompany != null">
        unload_company,
      </if>
      <if test="unloadLongitude != null">
        unload_longitude,
      </if>
      <if test="unloadLatitude != null">
        unload_latitude,
      </if>
      <if test="receiverName != null">
        receiver_name,
      </if>
      <if test="receiverMobile != null">
        receiver_mobile,
      </if>
      <if test="expectedUnloadTime != null">
        expected_unload_time,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="renewableOrderId != null">
        #{renewableOrderId,jdbcType=BIGINT},
      </if>
      <if test="loadAddressCode != null">
        #{loadAddressCode,jdbcType=VARCHAR},
      </if>
      <if test="loadProvinceId != null">
        #{loadProvinceId,jdbcType=BIGINT},
      </if>
      <if test="loadProvinceName != null">
        #{loadProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="loadCityId != null">
        #{loadCityId,jdbcType=BIGINT},
      </if>
      <if test="loadCityName != null">
        #{loadCityName,jdbcType=VARCHAR},
      </if>
      <if test="loadAreaId != null">
        #{loadAreaId,jdbcType=BIGINT},
      </if>
      <if test="loadAreaName != null">
        #{loadAreaName,jdbcType=VARCHAR},
      </if>
      <if test="loadDetailAddress != null">
        #{loadDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="loadWarehouse != null">
        #{loadWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="loadCompany != null">
        #{loadCompany,jdbcType=VARCHAR},
      </if>
      <if test="loadLongitude != null">
        #{loadLongitude,jdbcType=VARCHAR},
      </if>
      <if test="loadLatitude != null">
        #{loadLatitude,jdbcType=VARCHAR},
      </if>
      <if test="consignorName != null">
        #{consignorName,jdbcType=VARCHAR},
      </if>
      <if test="consignorMobile != null">
        #{consignorMobile,jdbcType=VARCHAR},
      </if>
      <if test="expectedLoadTime != null">
        #{expectedLoadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="unloadAddressCode != null">
        #{unloadAddressCode,jdbcType=VARCHAR},
      </if>
      <if test="unloadProvinceId != null">
        #{unloadProvinceId,jdbcType=BIGINT},
      </if>
      <if test="unloadProvinceName != null">
        #{unloadProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="unloadCityId != null">
        #{unloadCityId,jdbcType=BIGINT},
      </if>
      <if test="unloadCityName != null">
        #{unloadCityName,jdbcType=VARCHAR},
      </if>
      <if test="unloadAreaId != null">
        #{unloadAreaId,jdbcType=BIGINT},
      </if>
      <if test="unloadAreaName != null">
        #{unloadAreaName,jdbcType=VARCHAR},
      </if>
      <if test="unloadDetailAddress != null">
        #{unloadDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="unloadWarehouse != null">
        #{unloadWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="unloadCompany != null">
        #{unloadCompany,jdbcType=VARCHAR},
      </if>
      <if test="unloadLongitude != null">
        #{unloadLongitude,jdbcType=VARCHAR},
      </if>
      <if test="unloadLatitude != null">
        #{unloadLatitude,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null">
        #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="receiverMobile != null">
        #{receiverMobile,jdbcType=VARCHAR},
      </if>
      <if test="expectedUnloadTime != null">
        #{expectedUnloadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TRenewableAuditAddress">
    update t_renewable_audit_address
    <set>
      <if test="renewableOrderId != null">
        renewable_order_id = #{renewableOrderId,jdbcType=BIGINT},
      </if>
      <if test="loadAddressCode != null">
        load_address_code = #{loadAddressCode,jdbcType=VARCHAR},
      </if>
      <if test="loadProvinceId != null">
        load_province_id = #{loadProvinceId,jdbcType=BIGINT},
      </if>
      <if test="loadProvinceName != null">
        load_province_name = #{loadProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="loadCityId != null">
        load_city_id = #{loadCityId,jdbcType=BIGINT},
      </if>
      <if test="loadCityName != null">
        load_city_name = #{loadCityName,jdbcType=VARCHAR},
      </if>
      <if test="loadAreaId != null">
        load_area_id = #{loadAreaId,jdbcType=BIGINT},
      </if>
      <if test="loadAreaName != null">
        load_area_name = #{loadAreaName,jdbcType=VARCHAR},
      </if>
      <if test="loadDetailAddress != null">
        load_detail_address = #{loadDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="loadWarehouse != null">
        load_warehouse = #{loadWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="loadCompany != null">
        load_company = #{loadCompany,jdbcType=VARCHAR},
      </if>
      <if test="loadLongitude != null">
        load_longitude = #{loadLongitude,jdbcType=VARCHAR},
      </if>
      <if test="loadLatitude != null">
        load_latitude = #{loadLatitude,jdbcType=VARCHAR},
      </if>
      <if test="consignorName != null">
        consignor_name = #{consignorName,jdbcType=VARCHAR},
      </if>
      <if test="consignorMobile != null">
        consignor_mobile = #{consignorMobile,jdbcType=VARCHAR},
      </if>
      <if test="expectedLoadTime != null">
        expected_load_time = #{expectedLoadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="unloadAddressCode != null">
        unload_address_code = #{unloadAddressCode,jdbcType=VARCHAR},
      </if>
      <if test="unloadProvinceId != null">
        unload_province_id = #{unloadProvinceId,jdbcType=BIGINT},
      </if>
      <if test="unloadProvinceName != null">
        unload_province_name = #{unloadProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="unloadCityId != null">
        unload_city_id = #{unloadCityId,jdbcType=BIGINT},
      </if>
      <if test="unloadCityName != null">
        unload_city_name = #{unloadCityName,jdbcType=VARCHAR},
      </if>
      <if test="unloadAreaId != null">
        unload_area_id = #{unloadAreaId,jdbcType=BIGINT},
      </if>
      <if test="unloadAreaName != null">
        unload_area_name = #{unloadAreaName,jdbcType=VARCHAR},
      </if>
      <if test="unloadDetailAddress != null">
        unload_detail_address = #{unloadDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="unloadWarehouse != null">
        unload_warehouse = #{unloadWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="unloadCompany != null">
        unload_company = #{unloadCompany,jdbcType=VARCHAR},
      </if>
      <if test="unloadLongitude != null">
        unload_longitude = #{unloadLongitude,jdbcType=VARCHAR},
      </if>
      <if test="unloadLatitude != null">
        unload_latitude = #{unloadLatitude,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null">
        receiver_name = #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="receiverMobile != null">
        receiver_mobile = #{receiverMobile,jdbcType=VARCHAR},
      </if>
      <if test="expectedUnloadTime != null">
        expected_unload_time = #{expectedUnloadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TRenewableAuditAddress">
    update t_renewable_audit_address
    set renewable_order_id = #{renewableOrderId,jdbcType=BIGINT},
      load_address_code = #{loadAddressCode,jdbcType=VARCHAR},
      load_province_id = #{loadProvinceId,jdbcType=BIGINT},
      load_province_name = #{loadProvinceName,jdbcType=VARCHAR},
      load_city_id = #{loadCityId,jdbcType=BIGINT},
      load_city_name = #{loadCityName,jdbcType=VARCHAR},
      load_area_id = #{loadAreaId,jdbcType=BIGINT},
      load_area_name = #{loadAreaName,jdbcType=VARCHAR},
      load_detail_address = #{loadDetailAddress,jdbcType=VARCHAR},
      load_warehouse = #{loadWarehouse,jdbcType=VARCHAR},
      load_company = #{loadCompany,jdbcType=VARCHAR},
      load_longitude = #{loadLongitude,jdbcType=VARCHAR},
      load_latitude = #{loadLatitude,jdbcType=VARCHAR},
      consignor_name = #{consignorName,jdbcType=VARCHAR},
      consignor_mobile = #{consignorMobile,jdbcType=VARCHAR},
      expected_load_time = #{expectedLoadTime,jdbcType=TIMESTAMP},
      unload_address_code = #{unloadAddressCode,jdbcType=VARCHAR},
      unload_province_id = #{unloadProvinceId,jdbcType=BIGINT},
      unload_province_name = #{unloadProvinceName,jdbcType=VARCHAR},
      unload_city_id = #{unloadCityId,jdbcType=BIGINT},
      unload_city_name = #{unloadCityName,jdbcType=VARCHAR},
      unload_area_id = #{unloadAreaId,jdbcType=BIGINT},
      unload_area_name = #{unloadAreaName,jdbcType=VARCHAR},
      unload_detail_address = #{unloadDetailAddress,jdbcType=VARCHAR},
      unload_warehouse = #{unloadWarehouse,jdbcType=VARCHAR},
      unload_company = #{unloadCompany,jdbcType=VARCHAR},
      unload_longitude = #{unloadLongitude,jdbcType=VARCHAR},
      unload_latitude = #{unloadLatitude,jdbcType=VARCHAR},
      receiver_name = #{receiverName,jdbcType=VARCHAR},
      receiver_mobile = #{receiverMobile,jdbcType=VARCHAR},
      expected_unload_time = #{expectedUnloadTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>