package com.logistics.management.webapi.controller.companycarrier;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.client.companycarrier.CompanyCarrierClient;
import com.logistics.management.webapi.client.companycarrier.request.*;
import com.logistics.management.webapi.client.companycarrier.response.CompanyCarrierDetailResponseModel;
import com.logistics.management.webapi.client.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.management.webapi.client.companycarrier.response.SearchCompanyCarrierListResponseModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.baidu.BaiDuClient;
import com.logistics.management.webapi.client.thirdparty.basicdata.baidu.request.CheckSensitiveWordRequestModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.baidu.response.CheckSensitiveWordResponseModel;
import com.logistics.management.webapi.controller.companycarrier.mapping.CompanyCarrierDetailMapping;
import com.logistics.management.webapi.controller.companycarrier.mapping.CompanyCarrierSearchListMapping;
import com.logistics.management.webapi.controller.companycarrier.mapping.FuzzyQueryMapping;
import com.logistics.management.webapi.controller.companycarrier.request.*;
import com.logistics.management.webapi.controller.companycarrier.response.CompanyCarrierDetailResponseDto;
import com.logistics.management.webapi.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseDto;
import com.logistics.management.webapi.controller.companycarrier.response.SearchCompanyCarrierListResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.IDCardValidator;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 车主管理
 * @Author: sj
 * @Date: 2019/9/27 17:35
 */
@Api(value = "API-CompanyCarrierApi-车主管理")
@RestController
public class CompanyCarrierController {
    @Resource
    private CompanyCarrierClient companyCarrierClient;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private CommonBiz commonBiz;

    @Resource
    private BaiDuClient baiDuClient;

    /**
     * 查询车主公司列表 3.27.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "查询车主公司列表")
    @PostMapping(value = "/api/companyCarrier/searchList")
    public Result<PageInfo<SearchCompanyCarrierListResponseDto>> searchList(@RequestBody SearchCompanyCarrierListRequestDto requestDto) {
        Result<PageInfo<SearchCompanyCarrierListResponseModel>> result = companyCarrierClient.searchList(MapperUtils.mapper(requestDto, SearchCompanyCarrierListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SearchCompanyCarrierListResponseDto> dtoList = MapperUtils.mapper(result.getData().getList(), SearchCompanyCarrierListResponseDto.class, new CompanyCarrierSearchListMapping());
        pageInfo.setList(dtoList == null ? new ArrayList() : dtoList);
        return Result.success(pageInfo);
    }

    /**
     * 公司详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "公司详情", tags = "1.2.4")
    @PostMapping(value = "/api/companyCarrier/detail")
    public Result<CompanyCarrierDetailResponseDto> getDetail(@RequestBody @Valid CompanyCarrierDetailRequestDto requestDto) {
        Result<CompanyCarrierDetailResponseModel> result = companyCarrierClient.getDetail(MapperUtils.mapper(requestDto, CompanyCarrierDetailRequestModel.class));
        result.throwException();
        //获取图片签名
        List<String> sourceSrcList=new ArrayList<>();
        CompanyCarrierDetailResponseModel detailResponseModel = result.getData();
        if (StringUtils.isNotBlank(detailResponseModel.getRoadTransportCertificateImage())){
            sourceSrcList.add(detailResponseModel.getRoadTransportCertificateImage());//道路许可证图片
        }
        if (StringUtils.isNotBlank(detailResponseModel.getTradingCertificateImage())){
            sourceSrcList.add(detailResponseModel.getTradingCertificateImage());//道路许可证图片
        }
        if (StringUtils.isNotBlank(detailResponseModel.getIdentityFaceFile())){
            sourceSrcList.add(detailResponseModel.getIdentityFaceFile());//身份人像面
        }
        if (StringUtils.isNotBlank(detailResponseModel.getIdentityNationalFile())){
            sourceSrcList.add(detailResponseModel.getIdentityNationalFile());//身份证国徽面
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(), CompanyCarrierDetailResponseDto.class, new CompanyCarrierDetailMapping(configKeyConstant.fileAccessAddress,imageMap)));
    }

    /**
     * 修改
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "编辑-个人/企业车主", tags = "1.2.4")
    @PostMapping(value = "/api/companyCarrier/saveOrUpdate")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> saveOrUpdate(@RequestBody @Valid SaveOrModifyCompanyCarrierRequestDto requestDto) {
        //入参校验
        //校验类型
        if (!CompanyTypeEnum.COMPANY.getKey().toString().equals(requestDto.getType()) && !CompanyTypeEnum.PERSONAL.getKey().toString().equals(requestDto.getType())){
            throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
        }
        //校验身份证号
        if(StringUtils.isNotBlank(requestDto.getIdentityNumber()) && !IDCardValidator.isValidatedAllIdcard(requestDto.getIdentityNumber())){
            throw new BizException(ManagementWebApiExceptionEnum.ID_CARD_FORMAT_ERROR);
        }
        //校验敏感词汇
        StringBuffer stringBuffer = new StringBuffer();
        if (StringUtils.isNotBlank(requestDto.getCompanyCarrierName())){
            stringBuffer.append(requestDto.getCompanyCarrierName());
        }
        if (StringUtils.isNotBlank(requestDto.getCompanyWaterMark())){
            stringBuffer.append(requestDto.getCompanyWaterMark());
        }
        if (StringUtils.isNotBlank(stringBuffer)){
            CheckSensitiveWordRequestModel checkSensitiveWordRequestModel = new CheckSensitiveWordRequestModel();
            checkSensitiveWordRequestModel.setContent(stringBuffer.toString());
            Result<CheckSensitiveWordResponseModel> checkResult = baiDuClient.checkSensitiveWord(checkSensitiveWordRequestModel);
            if (checkResult.getData() != null && CommonConstant.INTEGER_ONE.equals(checkResult.getData().getIfSensitive())) {
                throw new BizException(ManagementWebApiExceptionEnum.COMPANY_NAME_OR_WATR_FORBBDIEN_WORD);
            }
        }

        return companyCarrierClient.saveOrUpdate(MapperUtils.mapperNoDefault(requestDto, SaveOrModifyCompanyCarrierRequestModel.class));
    }

    /**
     * 模糊查询车主公司信息 3.22.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "模糊查询车主公司信息")
    @PostMapping(value = "/api/companyCarrier/getCompanyByName")
    public Result<List<FuzzySearchCompanyCarrierResponseDto>> fuzzyQuery(@RequestBody FuzzySearchCompanyCarrierRequestDto requestDto) {
        Result<List<FuzzySearchCompanyCarrierResponseModel>> result = companyCarrierClient.fuzzyQuery(MapperUtils.mapper(requestDto, FuzzySearchCompanyCarrierRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), FuzzySearchCompanyCarrierResponseDto.class,new FuzzyQueryMapping()));
    }

    /**
     * 开启/关闭黑名单 3.22.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "开启/关闭黑名单")
    @PostMapping(value = "/api/companyCarrier/openOrClose")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> openOrClose(@RequestBody @Valid OpenCloseBlacklistRequestDto requestDto) {
        return companyCarrierClient.openOrClose(MapperUtils.mapper(requestDto, OpenCloseBlacklistRequestModel.class));
    }

    /**
     * 开启/关闭零担模式 3.27.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/api/companyCarrier/openOrCloseLessThanTruckload")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> openOrCloseLessThanTruckload(@RequestBody @Valid OpenCloseLessThanTruckloadRequestDto requestDto) {
        return companyCarrierClient.openOrCloseLessThanTruckload(MapperUtils.mapper(requestDto, OpenCloseLessThanTruckloadRequestModel.class));
    }

}
