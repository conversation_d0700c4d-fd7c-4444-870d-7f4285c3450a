package com.logistics.tms.mapper;

import com.logistics.tms.entity.TExtDemandOrderRelation;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2025/07/22
*/
@Mapper
public interface TExtDemandOrderRelationMapper extends BaseMapper<TExtDemandOrderRelation> {


    TExtDemandOrderRelation selectByCarrierOrderCode(String carrierOrderCode);

    List<TExtDemandOrderRelation> selectByExtCode(@Param("extCode") String extCode);

    /**
     * 根据额外需求单ID查询运单关联信息
     */
    TExtDemandOrderRelation selectByExtDemandOrderId(Long extDemandOrderId);

}