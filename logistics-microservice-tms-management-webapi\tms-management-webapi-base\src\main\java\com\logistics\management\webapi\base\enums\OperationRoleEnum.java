package com.logistics.management.webapi.base.enums;

/**
 * @author: wjf
 * @date: 2019/7/12 15:56
 */
public enum OperationRoleEnum {
    DEFAULT(0,""),
    CARRIER(1,"车主"),
    DRIVER(2,"司机"),
    CARRIER_DRIVER(3,"车主、司机"),
    ;

    private Integer key;
    private String value;

    OperationRoleEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static OperationRoleEnum getEnum(Integer key) {
        for (OperationRoleEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
