package com.logistics.tms.mapper;

import com.logistics.tms.controller.carrierorder.response.GetCarrierOrderWeixinPushResponseModel;
import com.logistics.tms.entity.TCarrierOrderWx;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TCarrierOrderWxMapper extends BaseMapper<TCarrierOrderWx> {

    List<GetCarrierOrderWeixinPushResponseModel> selectCarrierOrderWeixinPushInfoByCarrierOrder(@Param("carrierOrderId") Long carrierOrderId);

    int batchUpdate(@Param("list") List<TCarrierOrderWx> tCarrierOrderWxes);

    int batchInsert(@Param("list") List<TCarrierOrderWx> tCarrierOrderWxes);

}