package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 新生运单提货
 *
 * <AUTHOR>
 * @date 2022/8/16 16:57
 */
@Data
public class LoadForYeloLifeRequestDto {

    @NotBlank(message = "运单id不能为空")
    @ApiModelProperty(value = "运单ID", required = true)
    private String carrierOrderId;

    @ApiModelProperty(value = "货物列表", required = true)
    @NotEmpty(message = "货物列表不能为空")
    @Valid
    private List<LoadGoodsForYeloLifeRequestDto> goodsList;

    @ApiModelProperty(value = "现场图片列表")
    private List<String> siteImgList;

    @ApiModelProperty(value = "出库单列表", required = true)
    @NotEmpty(message = "出库单不能为空")
    @Size(min = 1, max = 2, message = "出库单图片最多两张")
    private List<String> outImgList;
}
