package com.logistics.tms.controller.driversafemeeting.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/6/5 17:49
 */
@Data
public class ModifyDriverSafeMeetingRequestModel {
    @ApiModelProperty(value = "学习例会id")
    private Long safeMeetingId;
    @ApiModelProperty(value = "学习标题")
    private String title;
    @ApiModelProperty(value = "学习简介")
    private String introduction;
    @ApiModelProperty(value = "学习内容")
    private String content;
}
