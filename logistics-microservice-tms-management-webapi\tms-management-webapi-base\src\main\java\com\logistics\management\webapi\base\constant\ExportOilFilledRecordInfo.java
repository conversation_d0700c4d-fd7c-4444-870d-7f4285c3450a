package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;


public class ExportOilFilledRecordInfo {
    private ExportOilFilledRecordInfo() {

    }

    private static final Map<String, String> EXPORT_LOAN_RECORD_MAP;

    static {
        EXPORT_LOAN_RECORD_MAP = new LinkedHashMap<>();
        EXPORT_LOAN_RECORD_MAP.put("车牌号", "vehicleNo");
        EXPORT_LOAN_RECORD_MAP.put("充油金额 ", "oilFilledFee");
        EXPORT_LOAN_RECORD_MAP.put("充值时间", "oilFilledDate");
        EXPORT_LOAN_RECORD_MAP.put("充油方式", "oilFilledTypeLabel");
        EXPORT_LOAN_RECORD_MAP.put("升数", "liter");
        EXPORT_LOAN_RECORD_MAP.put("充值积分", "topUpIntegral");
        EXPORT_LOAN_RECORD_MAP.put("奖励积分", "rewardIntegral");
        EXPORT_LOAN_RECORD_MAP.put("副卡卡号", "subCardNumber");
        EXPORT_LOAN_RECORD_MAP.put("副卡所属人", "subCardOwner");
        EXPORT_LOAN_RECORD_MAP.put("操作人", "lastModifiedBy");
        EXPORT_LOAN_RECORD_MAP.put("操作时间", "lastModifiedTime");
    }

    public static Map<String, String> getExportOilFilledRecordMap() {
        return EXPORT_LOAN_RECORD_MAP;
    }
}
