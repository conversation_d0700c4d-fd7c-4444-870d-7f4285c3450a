package com.logistics.appapi.controller.baiscinfo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/16
 */
@Data
public class GetPersonAuthVerifyCodeRequestDto {

	@ApiModelProperty(value = "手机号码", required = true)
	@NotBlank(message = "手机号不能为空")
	private String mobile;

	@ApiModelProperty(value = "操作类型, 1:实名 2:修改手机号", required = true)
	@NotBlank(message = "操作类型不能为空")
	@Range(min = 1, max = 2, message = "操作类型错误")
	private String operationType;
}
