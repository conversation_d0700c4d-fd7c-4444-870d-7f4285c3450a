package com.logistics.tms.client.feign.warehouse.stock.reponse;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2023/12/4 16:06
 */
@Data
public class ListWarehouseByNameResponseModel {
    @ApiModelProperty("仓库ID")
    private Long id;
    @ApiModelProperty("仓库类型,1是自有,2是第三方")
    private Integer type;
    @ApiModelProperty("仓库中文名字")
    private String chineseName;
    @ApiModelProperty("仓库英文名字")
    private String englishName;
    @ApiModelProperty("省份ID")
    private Long provinceId;
    @ApiModelProperty("省份名字")
    private String provinceName;
    @ApiModelProperty("城市ID")
    private Long cityId;
    @ApiModelProperty("城市名字")
    private String cityName;
    @ApiModelProperty("县区id")
    private Long areaId;
    @ApiModelProperty("县区名字")
    private String areaName;
    @ApiModelProperty("详细地址")
    private String detailAddress;
    @ApiModelProperty("详细地址（英文）")
    private String abroadAddress;
    @ApiModelProperty("联系人名字")
    private String contactName;
    @ApiModelProperty("联系座机号码")
    private String contactTelephone;
    @ApiModelProperty("手机号")
    private String contactMobile;
    @ApiModelProperty("传真")
    private String contactFax;
    @ApiModelProperty("是否启用,1启用,0禁用")
    private Integer enabled;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("地址经度")
    private String longitude;
    @ApiModelProperty("地址纬度")
    private String latitude;
}
