package com.logistics.appapi.client.thirdparty.basicdata;

import com.logistics.appapi.client.thirdparty.basicdata.baidu.BaiDuServiceApi;
import com.logistics.appapi.client.thirdparty.basicdata.baidu.request.CheckSensitiveWordRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.baidu.response.CheckSensitiveWordResponseModel;
import com.logistics.appapi.client.thirdparty.basicdata.datamap.DataMapServiceApi;
import com.logistics.appapi.client.thirdparty.basicdata.datamap.request.GetMapListRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.datamap.response.GetMapListResponseModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.FileServiceApi;
import com.logistics.appapi.client.thirdparty.basicdata.file.request.BatchGetOSSFileUrlRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.request.GetFileByFilePathRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.request.GetOSSUrlRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.request.PdfWriteToImgRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.response.*;
import com.logistics.appapi.client.thirdparty.basicdata.ocr.OCRServiceApi;
import com.logistics.appapi.client.thirdparty.basicdata.ocr.response.OcrMultipleInvoiceResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/7 11:21
 */
@Service
public class BasicServiceClient {

    @Resource
    private OCRServiceApi OCRServiceApi;
    @Resource
    private BaiDuServiceApi baiDuServiceApi;
    @Resource
    private DataMapServiceApi dataMapServiceApi;
    @Resource
    private FileServiceApi fileServiceApi;

    /**
     * 发票数据ocr识别
     * @param file
     * @param type
     * @return
     */
    public OcrMultipleInvoiceResponseModel multipleInvoice(MultipartFile file, String type){
        Result<OcrMultipleInvoiceResponseModel> result = OCRServiceApi.multipleInvoice(file, type);
        result.throwException();
        return result.getData();
    }

    /**
     * 判断是否具有敏感词汇
     * @param requestModel
     * @return
     */
    public CheckSensitiveWordResponseModel checkSensitiveWord(CheckSensitiveWordRequestModel requestModel){
        Result<CheckSensitiveWordResponseModel> result = baiDuServiceApi.checkSensitiveWord(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 省市区-列表-查询(三级联动)
     * @param requestModel
     * @return
     */
    public List<GetMapListResponseModel> getMapList(GetMapListRequestModel requestModel){
        Result<List<GetMapListResponseModel>> result = dataMapServiceApi.getMapList(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 上传图片到oss并识别文字信息
     * @param file
     * @param picType
     * @param idCardSide
     * @return
     */
    public FileUploadForAIResponseModel uploadOSSFileForAI(MultipartFile file, String picType, String idCardSide){
        Result<FileUploadForAIResponseModel> result = fileServiceApi.uploadOSSFileForAI(file,picType,idCardSide);
        result.throwException();
        return result.getData();
    }

    /**
     * 上传文件到oss
     * @param file
     * @return
     */
    public UploadFileOSSResponseModel uploadMultiPartFileOSS(MultipartFile file){
        Result<UploadFileOSSResponseModel> result = fileServiceApi.uploadMultiPartFileOSS(file);
        result.throwException();
        return result.getData();
    }

    /**
     * 获取oss图片预览url
     */
    public GetOSSUrlResponseModel getOSSFileUrl(GetOSSUrlRequestModel requestModel){
        Result<GetOSSUrlResponseModel> result = fileServiceApi.getOSSFileUrl(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 批量获取oss图片预览url
     */
    public List<GetOSSUrlResponseModel> batchGetOSSFileUrl(BatchGetOSSFileUrlRequestModel requestModel){
        Result<List<GetOSSUrlResponseModel>> result = fileServiceApi.batchGetOSSFileUrl(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 获取oss File流
     */
    public GetFileByteOSSResponseModel getFileByteOSS(GetFileByFilePathRequestModel requestModel){
        Result<GetFileByteOSSResponseModel> result = fileServiceApi.getFileByteOSS(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * pdf转图片并落库OSS
     * @param requestModel
     * @return
     */
    public PdfWriteToImgResponseModel pdfWriteToImgOSS(PdfWriteToImgRequestModel requestModel){
        Result<PdfWriteToImgResponseModel> result = fileServiceApi.pdfWriteToImgOSS(requestModel);
        result.throwException();
        return result.getData();
    }
}
