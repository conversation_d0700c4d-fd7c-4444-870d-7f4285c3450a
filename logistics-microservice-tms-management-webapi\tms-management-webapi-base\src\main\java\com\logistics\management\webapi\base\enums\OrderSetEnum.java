package com.logistics.management.webapi.base.enums;

/**
 * <AUTHOR>
 * @createDate 2019-04-04 10:15
 */
public enum OrderSetEnum {
    NOT_SPECIFY(0, "不指定承运商接单"),
    SPECIFY(1, "指定承运商接单"),;

    private Integer key;
    private String value;

    OrderSetEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static OrderSetEnum getEnum(Integer key) {
        for (OrderSetEnum t : values()) {
            if (t.getKey().equals(key)){
                return t;
            }
        }
        return null;
    }
}
