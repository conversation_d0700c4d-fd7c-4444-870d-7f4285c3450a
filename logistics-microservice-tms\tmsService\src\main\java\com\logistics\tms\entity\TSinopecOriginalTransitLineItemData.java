package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/12/14
*/
@Data
public class TSinopecOriginalTransitLineItemData extends BaseEntity {
    /**
    * 中石化委托单原始数据表ID
    */
    @ApiModelProperty("中石化委托单原始数据表ID")
    private Long sinopecOriginalDataId;

    /**
    * 物流订单号
    */
    @ApiModelProperty("物流订单号")
    private String logisticsOrderNumber;

    /**
    * 商流订单号
    */
    @ApiModelProperty("商流订单号")
    private String businessOrderNumber;

    /**
    * 订单有效期
    */
    @ApiModelProperty("订单有效期")
    private Date businessOrderValidEnd;

    /**
    * 凭证日期
    */
    @ApiModelProperty("凭证日期")
    private Date certificateTime;

    /**
    * 物料形态, 1: 固体 2: 液体 3: 气体
    */
    @ApiModelProperty("物料形态, 1: 固体 2: 液体 3: 气体")
    private Integer existState;

    /**
    * 物料性质, 1: 危化品 2: 普通
    */
    @ApiModelProperty("物料性质, 1: 危化品 2: 普通")
    private Integer materialAttribute;

    /**
    * 是否易制毒, 0: 否，1: 是
    */
    @ApiModelProperty("是否易制毒, 0: 否，1: 是")
    private Integer makeDrug;

    /**
    * 是否支持提货身份验证, 0: 否，1: 是
    */
    @ApiModelProperty("是否支持提货身份验证, 0: 否，1: 是")
    private Integer supportAuthOnline;

    /**
    * 包装方式(非必填)
    */
    @ApiModelProperty("包装方式(非必填)")
    private String packingName;

    /**
    * 包装规格(非必填)
    */
    @ApiModelProperty("包装规格(非必填)")
    private String packingSpecification;

    /**
    * 生产企业类型, 1: 内部企业，0: 外部企业
    */
    @ApiModelProperty("生产企业类型, 1: 内部企业，0: 外部企业")
    private Integer productionEnterpriseType;

    /**
    * 生产企业名称
    */
    @ApiModelProperty("生产企业名称")
    private String productionEnterpriseName;

    /**
    * 生产企业编码
    */
    @ApiModelProperty("生产企业编码")
    private String productionEnterpriseCode;

    /**
    * 批次号(非必填)
    */
    @ApiModelProperty("批次号(非必填)")
    private String batch;

    /**
    * 物料大类
    */
    @ApiModelProperty("物料大类")
    private String materialMainCategory;

    /**
    * 物料大类编码
    */
    @ApiModelProperty("物料大类编码")
    private String materialMainCategoryCode;

    /**
    * 物料小类
    */
    @ApiModelProperty("物料小类")
    private String materialCategory;

    /**
    * 物料小类编码
    */
    @ApiModelProperty("物料小类编码")
    private String materialCategoryCode;

    /**
    * 物料名称
    */
    @ApiModelProperty("物料名称")
    private String materialName;

    /**
    * 物料编码
    */
    @ApiModelProperty("物料编码")
    private String materialCode;

    /**
    * 委托数量
    */
    @ApiModelProperty("委托数量")
    private BigDecimal associatedCount;

    /**
    * 单位ID
    */
    @ApiModelProperty("单位ID")
    private Integer unitId;

    /**
    * 单位名称
    */
    @ApiModelProperty("单位名称")
    private String unitName;

    /**
    * 客户编码(非必填)
    */
    @ApiModelProperty("客户编码(非必填)")
    private String clientCode;

    /**
    * 客户名称(非必填)
    */
    @ApiModelProperty("客户名称(非必填)")
    private String clientName;

    /**
    * 运输单价(非必填)
    */
    @ApiModelProperty("运输单价(非必填)")
    private BigDecimal transportPrice;

    /**
    * 预估运费(非必填)
    */
    @ApiModelProperty("预估运费(非必填)")
    private BigDecimal estimateTransportFee;

    /**
    * 装货人类型, 1: 生产企业 2: 仓库
    */
    @ApiModelProperty("装货人类型, 1: 生产企业 2: 仓库")
    private Integer freighterType;

    /**
    * 装货人编码
    */
    @ApiModelProperty("装货人编码")
    private String freighterCode;

    /**
    * 装货人
    */
    @ApiModelProperty("装货人")
    private String freighterName;

    /**
    * 起运地/港/站信息:位置类型(非必填), 1: 地 2: 港 3: 站
    */
    @ApiModelProperty("起运地/港/站信息:位置类型(非必填), 1: 地 2: 港 3: 站")
    private Integer originPortLocationType;

    /**
    * 起运地/港/站信息:位置编码(非必填)
    */
    @ApiModelProperty("起运地/港/站信息:位置编码(非必填)")
    private String originPortLocationCode;

    /**
    * 起运地/港/站信息:位置名称【发货仓库】(非必填)
    */
    @ApiModelProperty("起运地/港/站信息:位置名称【发货仓库】(非必填)")
    private String originPortLocationName;

    /**
    * 起运地/港/站信息:详细地址【发货详细地址】(非必填)
    */
    @ApiModelProperty("起运地/港/站信息:详细地址【发货详细地址】(非必填)")
    private String originPortDetailAddress;

    /**
    * 起运地/港/站信息:四级位置:省份编码(非必填)
    */
    @ApiModelProperty("起运地/港/站信息:四级位置:省份编码(非必填)")
    private String originPortProvinceCode;

    /**
    * 起运地/港/站信息:四级位置:省份名称(非必填)
    */
    @ApiModelProperty("起运地/港/站信息:四级位置:省份名称(非必填)")
    private String originPortProvinceName;

    /**
    * 起运地/港/站信息:四级位置:地级市编码(非必填)
    */
    @ApiModelProperty("起运地/港/站信息:四级位置:地级市编码(非必填)")
    private String originPortCityCode;

    /**
    * 起运地/港/站信息:四级位置:地级名称(非必填)
    */
    @ApiModelProperty("起运地/港/站信息:四级位置:地级名称(非必填)")
    private String originPortCityName;

    /**
    * 起运地/港/站信息:四级位置:区县编码(非必填)
    */
    @ApiModelProperty("起运地/港/站信息:四级位置:区县编码(非必填)")
    private String originPortCountyCode;

    /**
    * 起运地/港/站信息:四级位置:区县名称(非必填)
    */
    @ApiModelProperty("起运地/港/站信息:四级位置:区县名称(非必填)")
    private String originPortCountyName;

    /**
    * 起运地/港/站信息:四级位置:街道编码(非必填)
    */
    @ApiModelProperty("起运地/港/站信息:四级位置:街道编码(非必填)")
    private String originPortTownCode;

    /**
    * 起运地/港/站信息:四级位置:街道名称【可并入详细地址展示】(非必填)
    */
    @ApiModelProperty("起运地/港/站信息:四级位置:街道名称【可并入详细地址展示】(非必填)")
    private String originPortTownName;

    /**
    * 起运地/港/站信息:经纬度坐标:纬度(非必填)
    */
    @ApiModelProperty("起运地/港/站信息:经纬度坐标:纬度(非必填)")
    private String originPortLatitude;

    /**
    * 起运地/港/站信息:经纬度坐标:经度(非必填)
    */
    @ApiModelProperty("起运地/港/站信息:经纬度坐标:经度(非必填)")
    private String originPortLongitude;

    /**
    * 送达地/港/站信息:位置类型(非必填), 1: 地 2: 港 3: 站
    */
    @ApiModelProperty("送达地/港/站信息:位置类型(非必填), 1: 地 2: 港 3: 站")
    private Integer destinationPortLocationType;

    /**
    * 送达地/港/站信息:位置编码(非必填)
    */
    @ApiModelProperty("送达地/港/站信息:位置编码(非必填)")
    private String destinationPortLocationCode;

    /**
    * 送达地/港/站信息:位置名称【发货仓库】(非必填)
    */
    @ApiModelProperty("送达地/港/站信息:位置名称【发货仓库】(非必填)")
    private String destinationPortLocationName;

    /**
    * 送达地/港/站信息:详细地址【发货详细地址】(非必填)
    */
    @ApiModelProperty("送达地/港/站信息:详细地址【发货详细地址】(非必填)")
    private String destinationPortDetailAddress;

    /**
    * 送达地/港/站信息:四级位置:省份编码(非必填)
    */
    @ApiModelProperty("送达地/港/站信息:四级位置:省份编码(非必填)")
    private String destinationPortProvinceCode;

    /**
    * 送达地/港/站信息:四级位置:省份名称(非必填)
    */
    @ApiModelProperty("送达地/港/站信息:四级位置:省份名称(非必填)")
    private String destinationPortProvinceName;

    /**
    * 送达地/港/站信息:四级位置:地级市编码(非必填)
    */
    @ApiModelProperty("送达地/港/站信息:四级位置:地级市编码(非必填)")
    private String destinationPortCityCode;

    /**
    * 送达地/港/站信息:四级位置:地级名称(非必填)
    */
    @ApiModelProperty("送达地/港/站信息:四级位置:地级名称(非必填)")
    private String destinationPortCityName;

    /**
    * 送达地/港/站信息:四级位置:区县编码(非必填)
    */
    @ApiModelProperty("送达地/港/站信息:四级位置:区县编码(非必填)")
    private String destinationPortCountyCode;

    /**
    * 送达地/港/站信息:四级位置:区县名称(非必填)
    */
    @ApiModelProperty("送达地/港/站信息:四级位置:区县名称(非必填)")
    private String destinationPortCountyName;

    /**
    * 送达地/港/站信息:四级位置:街道编码(非必填)
    */
    @ApiModelProperty("送达地/港/站信息:四级位置:街道编码(非必填)")
    private String destinationPortTownCode;

    /**
    * 送达地/港/站信息:四级位置:街道名称【可并入详细地址展示】(非必填)
    */
    @ApiModelProperty("送达地/港/站信息:四级位置:街道名称【可并入详细地址展示】(非必填)")
    private String destinationPortTownName;

    /**
    * 送达地/港/站信息:经纬度坐标:纬度(非必填)
    */
    @ApiModelProperty("送达地/港/站信息:经纬度坐标:纬度(非必填)")
    private String destinationPortLatitude;

    /**
    * 送达地/港/站信息:经纬度坐标:经度(非必填)
    */
    @ApiModelProperty("送达地/港/站信息:经纬度坐标:经度(非必填)")
    private String destinationPortLongitude;

    /**
    * 收货人编码
    */
    @ApiModelProperty("收货人编码")
    private String receiverCode;

    /**
    * 收货人名称
    */
    @ApiModelProperty("收货人名称")
    private String receiverName;

    /**
    * 收货人类型, 仓库、客户
    */
    @ApiModelProperty("收货人类型, 仓库、客户")
    private Integer receiverType;

    /**
    * 收货联系方式(非必填)
    */
    @ApiModelProperty("收货联系方式(非必填)")
    private String receiverPhone;

    /**
    * 其他费用(非必填)
    */
    @ApiModelProperty("其他费用(非必填)")
    private BigDecimal otherfee;

    /**
    * 备用字段1(非必填)
    */
    @ApiModelProperty("备用字段1(非必填)")
    private String remark1;

    /**
    * 备用字段2(非必填)
    */
    @ApiModelProperty("备用字段2(非必填)")
    private String remark2;

    /**
    * 备用字段3(非必填)
    */
    @ApiModelProperty("备用字段3(非必填)")
    private String remark3;

    /**
    * 备用字段4(非必填)
    */
    @ApiModelProperty("备用字段4(非必填)")
    private String remark4;

    /**
    * 备用字段5(非必填)
    */
    @ApiModelProperty("备用字段5(非必填)")
    private String remark5;

    /**
    * 备用字段6(非必填)
    */
    @ApiModelProperty("备用字段6(非必填)")
    private String remark6;

    /**
    * 备用字段7(非必填)
    */
    @ApiModelProperty("备用字段7(非必填)")
    private String remark7;

    /**
    * 备用字段8(非必填)
    */
    @ApiModelProperty("备用字段8(非必填)")
    private String remark8;

    /**
    * 备用字段9(非必填)
    */
    @ApiModelProperty("备用字段9(非必填)")
    private String remark9;

    /**
    * 备用字段10(非必填)
    */
    @ApiModelProperty("备用字段10(非必填)")
    private String remark10;
}