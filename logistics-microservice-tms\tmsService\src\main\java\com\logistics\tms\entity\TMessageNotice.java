package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2024/06/12
*/
@Data
public class TMessageNotice extends BaseEntity {
    /**
    * 消息唯一ID
    */
    @ApiModelProperty("消息唯一ID")
    private String messageId;

    /**
    * 消息类型（与前端对应）：5000 竞价单详情
    */
    @ApiModelProperty("消息类型（与前端对应）：5000 竞价单详情")
    private Integer messageType;

    /**
    * 消息模块：1 后台，2 前台
    */
    @ApiModelProperty("消息模块：1 后台，2 前台")
    private Integer messageModule;

    /**
    * 类型：10 竞价单
    */
    @ApiModelProperty("类型：10 竞价单")
    private Integer objectType;

    /**
    * id：根据类型确定表id
    */
    @ApiModelProperty("id：根据类型确定表id")
    private Long objectId;

    /**
    * code：根据类型确定表code
    */
    @ApiModelProperty("code：根据类型确定表code")
    private String objectCode;

    /**
    * 消息体
    */
    @ApiModelProperty("消息体")
    private String messageBody;

    /**
    * 消息推送人
    */
    @ApiModelProperty("消息推送人")
    private String messagePusher;

    /**
    * 消息推送时间
    */
    @ApiModelProperty("消息推送时间")
    private Date messagePushTime;

    /**
    * 消息接收方（后台：空，前台：车主id）
    */
    @ApiModelProperty("消息接收方（后台：空，前台：车主id）")
    private Long messageReceiver;

    /**
    * 是否已读：0 否，1 是
    */
    @ApiModelProperty("是否已读：0 否，1 是")
    private Integer ifRead;

    /**
    * 已读操作人
    */
    @ApiModelProperty("已读操作人")
    private String readOperator;

    /**
    * 已读时间
    */
    @ApiModelProperty("已读时间")
    private Date readTime;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}