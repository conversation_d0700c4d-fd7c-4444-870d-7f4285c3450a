feign:
  sentinel:
    enabled: true

server:
  shutdown: graceful
  max-http-header-size: 1048576
  tomcat:
    max-http-form-post-size: 1048576

spring:
  lifecycle:
    timeout-per-shutdown-phase: 30s
  main: 
    allow-bean-definition-overriding: true
    allow-circular-references: true
  application:
    name: logistics-tms-services
    platform: logistics_tms
  cloud:
    nacos:
      password: ${NACOS-PASSWORD:nacos}
      username: ${NACOS-USERNAME:nacos}
      discovery:
        namespace: ${env}
        server-addr: ${NACOS-HOST:${env}.nacos.yelopack.com}:${NACOS-PORT:8848}
        group: ${REGISTRY_GROUP:DEFAULT_GROUP}
      config:
        username: ${spring.cloud.nacos.username}
        password: ${spring.cloud.nacos.password}
        namespace: ${env}
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        group: ${CONFIG_GROUP:LOGISTICS_GROUP}
  profiles:
    active: ${env}
  sleuth:
    web:
      client:
        enabled: true
    sampler:
      probability: 1.0 # 将采样比例设置为 1.0，也就是全部都需要。默认是 0.1

management:
  endpoint:
    health:
      sensitive: false
      show-details: always
    shutdown:
      enabled: true #启用shutdown
      sensitive: false #禁用密码验证
    loggers:
      enabled: true #启用loggers
      sensitive: false #禁用密码验证
    service-registry:
      enabled: true
  endpoints:
    web:
      exposure:
        include: shutdown,refresh,health,info,prometheus,service-registry,deregister

token:
  expire: 7200
  appExp: 259200

auth:
  serviceId: saas-auth-server
  client:
    id: logistics-tms-services
    token-header: token
    pub-key:
      path: client/pub.key


delayqueue:
  queueName: tms_queueName
