package com.logistics.appapi.client.baiscinfo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DriverBasicInfoResponseModel {

    @ApiModelProperty("司机ID")
    private Long driverId;

    @ApiModelProperty("司机姓名")
    private String driverName;

    @ApiModelProperty("司机手机号")
    private String driverMobile;

    @ApiModelProperty("司机身份证号")
    private String driverIdentityNumber;

    @ApiModelProperty(value = "认证类型,1:手机号认证 2:人脸识别")
    private Integer authModel;

    @ApiModelProperty("实名认证(个人): 0 待实名 1 实名中 2 已实名")
    private Integer realNameAuthenticationStatus;
}
