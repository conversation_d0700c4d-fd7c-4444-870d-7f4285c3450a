package com.logistics.management.webapi.api.feign.driversafemeeting.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/11/1 19:45
 */
@Data
public class DriverSafeMeetingKanBanResponseDto {
    @ApiModelProperty("年")
    private String meetingYear="";
    @ApiModelProperty("月")
    private String meetingMonth="";
    @ApiModelProperty("1:安全例会 2：紧急培训")
    private String type="";
    @ApiModelProperty("自主驾驶员人数")
    private String ownDriverCount="0";
    @ApiModelProperty("自营驾驶员人数")
    private String affiliationDriverCount="0";
    @ApiModelProperty("已学习驾驶员人数")
    private String learnDriverCount="0";
    @ApiModelProperty("线上学习率")
    private String onlineLearningRate="0.00";
    @ApiModelProperty("学习周期（年月，跳转学习详情）")
    private String period="";
    @ApiModelProperty("学习例会id（跳转学习内容）")
    private String safeMeetingId="";
    @ApiModelProperty("例会标题")
    private String title;
}
