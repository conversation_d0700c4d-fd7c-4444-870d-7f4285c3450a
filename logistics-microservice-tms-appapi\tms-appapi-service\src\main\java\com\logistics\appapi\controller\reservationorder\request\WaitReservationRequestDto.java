package com.logistics.appapi.controller.reservationorder.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * @author: wjf
 * @date: 2024/8/14 14:03
 */
@Data
public class WaitReservationRequestDto {

    /**
     * 预约类型：1 提货，2 卸货
     */
    @NotBlank(message = "预约类型不能为空")
    @Pattern(regexp = "^[12]$", message = "预约类型不能为空")
    private String reservationType;
}
