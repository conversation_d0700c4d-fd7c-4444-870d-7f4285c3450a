package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TCarrierOrderCorrect extends BaseEntity {
    /**
    * 运单id
    */
    @ApiModelProperty("运单id")
    private Long carrierOrderId;

    /**
    * 纠错状态：0 待纠错，1 已纠错，2 无需纠错
    */
    @ApiModelProperty("纠错状态：0 待纠错，1 已纠错，2 无需纠错")
    private Integer correctStatus;

    /**
    * 入库状态：2 部分入库，3 已入库
    */
    @ApiModelProperty("入库状态：2 部分入库，3 已入库")
    private Integer stockInState;

    /**
    * 入库数量
    */
    @ApiModelProperty("入库数量")
    private BigDecimal stockInCount;

    /**
    * 入库备注信息
    */
    @ApiModelProperty("入库备注信息")
    private String stockInRemark;

    /**
    * 纠错原因类型: 1 系统数量错误，2 实物损耗，3 提错托盘，4 仓库入错
    */
    @ApiModelProperty("纠错原因类型: 1 系统数量错误，2 实物损耗，3 提错托盘，4 仓库入错")
    private Integer correctType;

    /**
    * 纠错操作人
    */
    @ApiModelProperty("纠错操作人")
    private String correctUser;

    /**
    * 纠错操作时间
    */
    @ApiModelProperty("纠错操作时间")
    private Date correctTime;

    /**
    * 提错托盘数量
    */
    @ApiModelProperty("提错托盘数量")
    private BigDecimal loadErrorAmount;

    /**
    * 遗失托盘数量
    */
    @ApiModelProperty("遗失托盘数量")
    private BigDecimal loseErrorAmount;
}