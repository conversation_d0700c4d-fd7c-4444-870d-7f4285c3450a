<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderLocationMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCarrierOrderLocation" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="carrier_order_id" property="carrierOrderId" jdbcType="BIGINT" />
    <result column="location_type" property="locationType" jdbcType="INTEGER" />
    <result column="location" property="location" jdbcType="VARCHAR" />
    <result column="longitude" property="longitude" jdbcType="VARCHAR" />
    <result column="latitude" property="latitude" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, carrier_order_id, location_type, location, longitude, latitude, created_by, created_time, 
    last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_carrier_order_location
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_carrier_order_location
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCarrierOrderLocation" >
    insert into t_carrier_order_location (id, carrier_order_id, location_type, 
      location, longitude, latitude, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{carrierOrderId,jdbcType=BIGINT}, #{locationType,jdbcType=INTEGER}, 
      #{location,jdbcType=VARCHAR}, #{longitude,jdbcType=VARCHAR}, #{latitude,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCarrierOrderLocation" >
    insert into t_carrier_order_location
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="carrierOrderId != null" >
        carrier_order_id,
      </if>
      <if test="locationType != null" >
        location_type,
      </if>
      <if test="location != null" >
        location,
      </if>
      <if test="longitude != null" >
        longitude,
      </if>
      <if test="latitude != null" >
        latitude,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderId != null" >
        #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="locationType != null" >
        #{locationType,jdbcType=INTEGER},
      </if>
      <if test="location != null" >
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null" >
        #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null" >
        #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCarrierOrderLocation" >
    update t_carrier_order_location
    <set >
      <if test="carrierOrderId != null" >
        carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="locationType != null" >
        location_type = #{locationType,jdbcType=INTEGER},
      </if>
      <if test="location != null" >
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null" >
        longitude = #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null" >
        latitude = #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCarrierOrderLocation" >
    update t_carrier_order_location
    set carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      location_type = #{locationType,jdbcType=INTEGER},
      location = #{location,jdbcType=VARCHAR},
      longitude = #{longitude,jdbcType=VARCHAR},
      latitude = #{latitude,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>