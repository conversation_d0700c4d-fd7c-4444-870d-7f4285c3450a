package com.logistics.management.webapi.api.feign.leave.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.leave.LeaveApi;
import com.logistics.management.webapi.api.feign.leave.dto.*;
import com.yelo.tray.core.base.Result;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/15
 */
public class LeaveApiHystrix implements LeaveApi {
	@Override
	public Result<PageInfo<SearchLeaveListResponseDto>> searchLeaveApplyList(SearchLeaveListRequestDto requestDto) {
		return Result.timeout();
	}

	@Override
	public void exportLeaveApplyList(SearchLeaveListRequestDto requestDto, HttpServletResponse response) {
		Result.timeout();
	}

	@Override
	public Result<LeaveDetailResponseDto> leaveApplyDetail(LeaveDetailRequestDto requestDto) {
		return Result.timeout();
	}

	@Override
	public Result<Boolean> auditLeaveApply(AuditLeaveApplyRequestDto requestDto) {
		return Result.timeout();
	}

	@Override
	public Result<Boolean> cancelLeaveApply(CancelLeaveApplyRequestDto requestDto) {
		return Result.timeout();
	}
}
