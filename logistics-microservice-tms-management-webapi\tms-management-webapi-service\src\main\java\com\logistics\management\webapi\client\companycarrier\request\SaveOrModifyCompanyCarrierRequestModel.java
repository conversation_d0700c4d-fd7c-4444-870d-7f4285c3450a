package com.logistics.management.webapi.client.companycarrier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/9/27 17:42
 */
@Data
public class SaveOrModifyCompanyCarrierRequestModel {

    @ApiModelProperty("车主id")
    private Long companyCarrierId;

    @ApiModelProperty(value = "公司名字", required = true)
    private String companyCarrierName;

    @ApiModelProperty("公司水印")
    private String companyWaterMark;

    @ApiModelProperty(value = "类型: 1 企业 2 个人")
    private Integer type;

    @ApiModelProperty("营业执照路径相对")
    private String fileSrcPathTradingCertificateImage;

    @ApiModelProperty("营业执照有效期")
    private Date tradingCertificateValidityTime;

    @ApiModelProperty("营业执照是否永久: 0 否 1 是")
    private Integer tradingCertificateIsForever;

    @ApiModelProperty("营业执照是否后补: 0 否 1 是")
    private Integer tradingCertificateIsAmend;

    @ApiModelProperty("道路许可证号")
    private String roadTransportCertificateNumber;

    @ApiModelProperty("道路许可证号路径相对")
    private String fileSrcPathRoadTransportCertificateImage;

    @ApiModelProperty("道路许可证号有效期")
    private Date roadTransportCertificateValidityTime;

    @ApiModelProperty("道路许可证号是否永久: 0 否 1 是")
    private Integer roadTransportCertificateIsForever;

    @ApiModelProperty("道路许可证号是否后补: 0 否 1 是")
    private Integer roadTransportCertificateIsAmend;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("车主账号id")
    private Long carrierContactId;

    @ApiModelProperty("姓名")
    private String contactName;

    @ApiModelProperty("手机号")
    private String contactPhone;

    @ApiModelProperty("身份证人面像")
    private String identityFaceFile;

    @ApiModelProperty("身份证人面像图片是否后补 0否1是")
    private Integer identityFaceFileIsAmend;

    @ApiModelProperty("身份证国徽像")
    private String identityNationalFile;

    @ApiModelProperty("身份证国徽图片是否后补 0否1是")
    private Integer identityNationalFileIsAmend;

    @ApiModelProperty("身份证有效期")
    private Date identityValidity;

    @ApiModelProperty("身份证是否永久: 0 否 1 是")
    private Integer identityIsForever;

    @ApiModelProperty("身份证号码")
    private String identityNumber;

    @ApiModelProperty("省ID")
    private Long provinceId;

    @ApiModelProperty("省名字")
    private String provinceName;

    @ApiModelProperty("城市ID")
    private Long cityId;

    @ApiModelProperty("城市名字")
    private String cityName;

    @ApiModelProperty("区ID")
    private Long areaId;

    @ApiModelProperty("区名字")
    private String areaName;

    @ApiModelProperty("发证机关详情")
    private String certificationDepartmentDetail;

    @ApiModelProperty("临时费用提交：0 否，1 是")
    private Integer commitOtherFee;

    @ApiModelProperty(value = "临时费用费点,0<=数值范围<=20")
    private BigDecimal otherFeeTaxPoint;

    @ApiModelProperty(value = "运费费点,0<=数值范围<=20")
    private BigDecimal freightTaxPoint;
}
