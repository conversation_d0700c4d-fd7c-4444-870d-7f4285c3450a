package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleGradeEstimationRecord extends BaseEntity {
    /**
    * 车辆ID
    */
    @ApiModelProperty("车辆ID")
    private Long vehicleId;

    /**
    * 等级评定检查日期
    */
    @ApiModelProperty("等级评定检查日期")
    private Date estimationDate;

    /**
    * 等级 1 一级 2 耳机 3 三级
    */
    @ApiModelProperty("等级 1 一级 2 耳机 3 三级")
    private Integer grade;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
}