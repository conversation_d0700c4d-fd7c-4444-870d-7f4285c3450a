package com.logistics.tms.controller.dispatch.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class CompleteDemandOrderRequestModel {
    @ApiModelProperty("需求ids")
    private String demandOrderId;
    @ApiModelProperty("（云盘需求单必填）原因类型：10 数量问题，20 重报问题，30 联系问题，40 地址问题，50 装车问题，60 等待问题，70 其他问题")
    private Integer objectionType;
    @ApiModelProperty("（云盘需求单必填）描述")
    private String objectionReason;

    @ApiModelProperty("来源 1后台 2 前台")
    private Integer source;
}
