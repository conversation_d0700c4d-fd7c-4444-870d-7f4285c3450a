package com.logistics.tms.api.impl.carrierfreight;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.carrierfreight.CarrierFreightServiceApi;
import com.logistics.tms.api.feign.carrierfreight.model.CarrierFreightAddRequestModel;
import com.logistics.tms.api.feign.carrierfreight.model.CarrierFreightEnableRequestModel;
import com.logistics.tms.api.feign.carrierfreight.model.SearchCarrierFreightRequestModel;
import com.logistics.tms.api.feign.carrierfreight.model.SearchCarrierFreightResponseModel;
import com.logistics.tms.biz.carrierfreight.CarrierFreightBiz;
import com.yelo.tray.core.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * 车主运价
 *
 * <AUTHOR>
 * @date 2022/9/2 15:00
 */
@RestController
public class CarrierFreightServiceApiImpl implements CarrierFreightServiceApi {

    @Autowired
    private CarrierFreightBiz carrierFreightBiz;

    /**
     * 添加车主运价
     *
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> addCarrierFreight(CarrierFreightAddRequestModel requestModel) {
        return Result.success(carrierFreightBiz.addCarrierFreight(requestModel));
    }

    /**
     * 车主运价禁用启用
     *
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> enable(CarrierFreightEnableRequestModel requestModel) {
        return Result.success(carrierFreightBiz.enable(requestModel));
    }

    /**
     * 查询车主运价列表
     *
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<SearchCarrierFreightResponseModel>> searchList(SearchCarrierFreightRequestModel requestModel) {
        PageInfo<SearchCarrierFreightResponseModel> carrierFreightList = carrierFreightBiz.searchList(requestModel);
        return Result.success(carrierFreightList);
    }
}
