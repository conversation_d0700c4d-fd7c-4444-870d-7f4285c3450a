<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderMapper">
    <sql id="Base_Column_List_Decrypt">
    id, dispatch_order_id, dispatch_order_code, demand_order_id, demand_order_code, carrier_order_code, customer_order_code,
    status, status_update_time, if_cancel, cancel_reason, cancel_operator_name, cancel_time,
    business_type, customer_name, customer_user_name,
    AES_DECRYPT(UNHEX(customer_user_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as customer_user_mobile,
    customer_order_source, source, dispatch_user_id, dispatch_user_name, dispatch_time, company_carrier_type,
    company_carrier_id, company_carrier_name, carrier_contact_id, carrier_contact_name,
    AES_DECRYPT(UNHEX(carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrier_contact_phone,
    company_carrier_level, company_entrust_id, company_entrust_name, upstream_customer, load_time, unload_time,
    sign_time, expect_amount, load_amount_expect, load_amount, unload_amount_expect,
    unload_amount, sign_amount, expect_entrust_freight, expect_entrust_freight_type,
    entrust_freight_type, entrust_freight, sign_freight_fee, dispatch_freight_fee_type,
    dispatch_freight_fee, adjust_fee, markup_fee, goods_unit, demand_order_source, demand_order_entrust_type,
    settlement_tonnage, carrier_settlement, expect_mileage,config_distance, load_validity, abnormal_amount,
    if_empty, empty_time, remark,dispatch_remark, carrier_price_type, carrier_price, print_count, if_objection, out_status, publish_name,
    AES_DECRYPT(UNHEX(publish_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as publish_mobile, publish_time,
    publish_org_code, publish_org_name,if_urgent,available_on_weekends, loading_unloading_part, loading_unloading_charge, recycle_task_type, project_label,
    if_wait_audit_vehicle, correct_status, stock_in_state, carrier_settle_statement_status, qr_code_pic_path, delivery_method,
    order_mode, vehicle_length_id, vehicle_length, bargaining_mode,if_recycle_by_code, created_by, created_time, last_modified_by, last_modified_time, valid
    </sql>

    <select id="selectByPrimaryKeyDecrypt" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt" />
        from t_carrier_order
        where id = #{id,jdbcType=BIGINT}
        and valid = 1
    </select>

    <insert id="insertSelectiveEncrypt" parameterType="com.logistics.tms.entity.TCarrierOrder" keyProperty="id" useGeneratedKeys="true">
        insert into t_carrier_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="dispatchOrderId != null">
                dispatch_order_id,
            </if>
            <if test="dispatchOrderCode != null">
                dispatch_order_code,
            </if>
            <if test="demandOrderId != null">
                demand_order_id,
            </if>
            <if test="demandOrderCode != null">
                demand_order_code,
            </if>
            <if test="carrierOrderCode != null">
                carrier_order_code,
            </if>
            <if test="customerOrderCode != null">
                customer_order_code,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="statusUpdateTime != null">
                status_update_time,
            </if>
            <if test="ifCancel != null">
                if_cancel,
            </if>
            <if test="cancelReason != null">
                cancel_reason,
            </if>
            <if test="cancelOperatorName != null">
                cancel_operator_name,
            </if>
            <if test="cancelTime != null">
                cancel_time,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="customerName != null">
                customer_name,
            </if>
            <if test="customerUserName != null">
                customer_user_name,
            </if>
            <if test="customerUserMobile != null">
                customer_user_mobile,
            </if>
            <if test="customerOrderSource != null">
                customer_order_source,
            </if>
            <if test="source != null">
                source,
            </if>
            <if test="dispatchUserId != null">
                dispatch_user_id,
            </if>
            <if test="dispatchUserName != null">
                dispatch_user_name,
            </if>
            <if test="dispatchTime != null">
                dispatch_time,
            </if>
            <if test="companyCarrierType != null">
                company_carrier_type,
            </if>
            <if test="companyCarrierId != null">
                company_carrier_id,
            </if>
            <if test="companyCarrierName != null">
                company_carrier_name,
            </if>
            <if test="carrierContactId != null">
                carrier_contact_id,
            </if>
            <if test="carrierContactName != null">
                carrier_contact_name,
            </if>
            <if test="carrierContactPhone != null">
                carrier_contact_phone,
            </if>
            <if test="companyCarrierLevel != null">
                company_carrier_level,
            </if>
            <if test="companyEntrustId != null">
                company_entrust_id,
            </if>
            <if test="companyEntrustName != null">
                company_entrust_name,
            </if>
            <if test="upstreamCustomer != null">
                upstream_customer,
            </if>
            <if test="loadTime != null">
                load_time,
            </if>
            <if test="unloadTime != null">
                unload_time,
            </if>
            <if test="signTime != null">
                sign_time,
            </if>
            <if test="expectAmount != null">
                expect_amount,
            </if>
            <if test="loadAmountExpect != null">
                load_amount_expect,
            </if>
            <if test="loadAmount != null">
                load_amount,
            </if>
            <if test="unloadAmountExpect != null">
                unload_amount_expect,
            </if>
            <if test="unloadAmount != null">
                unload_amount,
            </if>
            <if test="signAmount != null">
                sign_amount,
            </if>
            <if test="expectEntrustFreight != null">
                expect_entrust_freight,
            </if>
            <if test="expectEntrustFreightType != null">
                expect_entrust_freight_type,
            </if>
            <if test="entrustFreightType != null">
                entrust_freight_type,
            </if>
            <if test="entrustFreight != null">
                entrust_freight,
            </if>
            <if test="signFreightFee != null">
                sign_freight_fee,
            </if>
            <if test="dispatchFreightFeeType != null">
                dispatch_freight_fee_type,
            </if>
            <if test="dispatchFreightFee != null">
                dispatch_freight_fee,
            </if>
            <if test="adjustFee != null">
                adjust_fee,
            </if>
            <if test="markupFee != null">
                markup_fee,
            </if>
            <if test="goodsUnit != null">
                goods_unit,
            </if>
            <if test="demandOrderSource != null">
                demand_order_source,
            </if>
            <if test="demandOrderEntrustType != null">
                demand_order_entrust_type,
            </if>
            <if test="settlementTonnage != null">
                settlement_tonnage,
            </if>
            <if test="carrierSettlement != null">
                carrier_settlement,
            </if>
            <if test="expectMileage != null">
                expect_mileage,
            </if>
            <if test="configDistance != null">
                config_distance,
            </if>
            <if test="loadValidity != null">
                load_validity,
            </if>
            <if test="abnormalAmount != null">
                abnormal_amount,
            </if>
            <if test="ifEmpty != null">
                if_empty,
            </if>
            <if test="emptyTime != null">
                empty_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="dispatchRemark != null">
                dispatch_remark,
            </if>
            <if test="carrierPriceType != null">
                carrier_price_type,
            </if>
            <if test="carrierPrice != null">
                carrier_price,
            </if>
            <if test="printCount != null">
                print_count,
            </if>
            <if test="ifObjection != null">
                if_objection,
            </if>
            <if test="outStatus != null">
                out_status,
            </if>
            <if test="publishName != null">
                publish_name,
            </if>
            <if test="publishMobile != null">
                publish_mobile,
            </if>
            <if test="publishTime != null">
                publish_time,
            </if>
            <if test="publishOrgCode != null">
                publish_org_code,
            </if>
            <if test="publishOrgName != null">
                publish_org_name,
            </if>
            <if test="ifUrgent != null">
                if_urgent,
            </if>
            <if test="availableOnWeekends != null">
                available_on_weekends,
            </if>
            <if test="loadingUnloadingPart != null">
                loading_unloading_part,
            </if>
            <if test="loadingUnloadingCharge != null">
                loading_unloading_charge,
            </if>
            <if test="recycleTaskType != null">
                recycle_task_type,
            </if>
            <if test="projectLabel != null">
                project_label,
            </if>
            <if test="ifWaitAuditVehicle != null">
                if_wait_audit_vehicle,
            </if>
            <if test="correctStatus != null">
                correct_status,
            </if>
            <if test="stockInState != null">
                stock_in_state,
            </if>
            <if test="carrierSettleStatementStatus != null">
                carrier_settle_statement_status,
            </if>
            <if test="qrCodePicPath != null">
                qr_code_pic_path,
            </if>
            <if test="deliveryMethod != null">
                delivery_method,
            </if>
            <if test="orderMode != null">
                order_mode,
            </if>
            <if test="vehicleLengthId != null">
                vehicle_length_id,
            </if>
            <if test="vehicleLength != null">
                vehicle_length,
            </if>
            <if test="bargainingMode != null">
                bargaining_mode,
            </if>
            <if test="ifRecycleByCode != null">
                if_recycle_by_code,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by,
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time,
            </if>
            <if test="valid != null">
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="dispatchOrderId != null">
                #{dispatchOrderId,jdbcType=BIGINT},
            </if>
            <if test="dispatchOrderCode != null">
                #{dispatchOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="demandOrderId != null">
                #{demandOrderId,jdbcType=BIGINT},
            </if>
            <if test="demandOrderCode != null">
                #{demandOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="carrierOrderCode != null">
                #{carrierOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="customerOrderCode != null">
                #{customerOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="statusUpdateTime != null">
                #{statusUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ifCancel != null">
                #{ifCancel,jdbcType=INTEGER},
            </if>
            <if test="cancelReason != null">
                #{cancelReason,jdbcType=VARCHAR},
            </if>
            <if test="cancelOperatorName != null">
                #{cancelOperatorName,jdbcType=VARCHAR},
            </if>
            <if test="cancelTime != null">
                #{cancelTime,jdbcType=TIMESTAMP},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=INTEGER},
            </if>
            <if test="customerName != null">
                #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="customerUserName != null">
                #{customerUserName,jdbcType=VARCHAR},
            </if>
            <if test="customerUserMobile != null">
                HEX(AES_ENCRYPT(#{customerUserMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="customerOrderSource != null">
                #{customerOrderSource,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                #{source,jdbcType=INTEGER},
            </if>
            <if test="dispatchUserId != null">
                #{dispatchUserId,jdbcType=BIGINT},
            </if>
            <if test="dispatchUserName != null">
                #{dispatchUserName,jdbcType=VARCHAR},
            </if>
            <if test="dispatchTime != null">
                #{dispatchTime,jdbcType=TIMESTAMP},
            </if>
            <if test="companyCarrierType != null">
                #{companyCarrierType,jdbcType=INTEGER},
            </if>
            <if test="companyCarrierId != null">
                #{companyCarrierId,jdbcType=BIGINT},
            </if>
            <if test="companyCarrierName != null">
                #{companyCarrierName,jdbcType=VARCHAR},
            </if>
            <if test="carrierContactId != null">
                #{carrierContactId,jdbcType=BIGINT},
            </if>
            <if test="carrierContactName != null">
                #{carrierContactName,jdbcType=VARCHAR},
            </if>
            <if test="carrierContactPhone != null">
                HEX(AES_ENCRYPT(#{carrierContactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="companyCarrierLevel != null">
                #{companyCarrierLevel,jdbcType=INTEGER},
            </if>
            <if test="companyEntrustId != null">
                #{companyEntrustId,jdbcType=BIGINT},
            </if>
            <if test="companyEntrustName != null">
                #{companyEntrustName,jdbcType=VARCHAR},
            </if>
            <if test="upstreamCustomer != null">
                #{upstreamCustomer,jdbcType=VARCHAR},
            </if>
            <if test="loadTime != null">
                #{loadTime,jdbcType=TIMESTAMP},
            </if>
            <if test="unloadTime != null">
                #{unloadTime,jdbcType=TIMESTAMP},
            </if>
            <if test="signTime != null">
                #{signTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expectAmount != null">
                #{expectAmount,jdbcType=DECIMAL},
            </if>
            <if test="loadAmountExpect != null">
                #{loadAmountExpect,jdbcType=DECIMAL},
            </if>
            <if test="loadAmount != null">
                #{loadAmount,jdbcType=DECIMAL},
            </if>
            <if test="unloadAmountExpect != null">
                #{unloadAmountExpect,jdbcType=DECIMAL},
            </if>
            <if test="unloadAmount != null">
                #{unloadAmount,jdbcType=DECIMAL},
            </if>
            <if test="signAmount != null">
                #{signAmount,jdbcType=DECIMAL},
            </if>
            <if test="expectEntrustFreight != null">
                #{expectEntrustFreight,jdbcType=DECIMAL},
            </if>
            <if test="expectEntrustFreightType != null">
                #{expectEntrustFreightType,jdbcType=INTEGER},
            </if>
            <if test="entrustFreightType != null">
                #{entrustFreightType,jdbcType=INTEGER},
            </if>
            <if test="entrustFreight != null">
                #{entrustFreight,jdbcType=DECIMAL},
            </if>
            <if test="signFreightFee != null">
                #{signFreightFee,jdbcType=DECIMAL},
            </if>
            <if test="dispatchFreightFeeType != null">
                #{dispatchFreightFeeType,jdbcType=INTEGER},
            </if>
            <if test="dispatchFreightFee != null">
                #{dispatchFreightFee,jdbcType=DECIMAL},
            </if>
            <if test="adjustFee != null">
                #{adjustFee,jdbcType=DECIMAL},
            </if>
            <if test="markupFee != null">
                #{markupFee,jdbcType=DECIMAL},
            </if>
            <if test="goodsUnit != null">
                #{goodsUnit,jdbcType=INTEGER},
            </if>
            <if test="demandOrderSource != null">
                #{demandOrderSource,jdbcType=INTEGER},
            </if>
            <if test="demandOrderEntrustType != null">
                #{demandOrderEntrustType,jdbcType=INTEGER},
            </if>
            <if test="settlementTonnage != null">
                #{settlementTonnage,jdbcType=INTEGER},
            </if>
            <if test="carrierSettlement != null">
                #{carrierSettlement,jdbcType=INTEGER},
            </if>
            <if test="expectMileage != null">
                #{expectMileage,jdbcType=DECIMAL},
            </if>
            <if test="configDistance != null">
                #{configDistance,jdbcType=DECIMAL},
            </if>
            <if test="loadValidity != null">
                #{loadValidity,jdbcType=INTEGER},
            </if>
            <if test="abnormalAmount != null">
                #{abnormalAmount,jdbcType=DECIMAL},
            </if>
            <if test="ifEmpty != null">
                #{ifEmpty,jdbcType=INTEGER},
            </if>
            <if test="emptyTime != null">
                #{emptyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="dispatchRemark != null">
                #{dispatchRemark,jdbcType=VARCHAR},
            </if>
            <if test="carrierPriceType != null">
                #{carrierPriceType,jdbcType=INTEGER},
            </if>
            <if test="carrierPrice != null">
                #{carrierPrice,jdbcType=DECIMAL},
            </if>
            <if test="printCount != null">
                #{printCount,jdbcType=INTEGER},
            </if>
            <if test="ifObjection != null">
                #{ifObjection,jdbcType=INTEGER},
            </if>
            <if test="outStatus != null">
                #{outStatus,jdbcType=INTEGER},
            </if>
            <if test="publishName != null">
                #{publishName,jdbcType=VARCHAR},
            </if>
            <if test="publishMobile != null">
                HEX(AES_ENCRYPT(#{publishMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="publishTime != null">
                #{publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="publishOrgCode != null">
                #{publishOrgCode,jdbcType=VARCHAR},
            </if>
            <if test="publishOrgName != null">
                #{publishOrgName,jdbcType=VARCHAR},
            </if>
            <if test="ifUrgent != null">
                #{ifUrgent,jdbcType=INTEGER},
            </if>
            <if test="availableOnWeekends != null">
                #{availableOnWeekends,jdbcType=INTEGER},
            </if>
            <if test="loadingUnloadingPart != null">
                #{loadingUnloadingPart,jdbcType=INTEGER},
            </if>
            <if test="loadingUnloadingCharge != null">
                #{loadingUnloadingCharge,jdbcType=DECIMAL},
            </if>
            <if test="recycleTaskType != null">
                #{recycleTaskType,jdbcType=INTEGER},
            </if>
            <if test="projectLabel != null">
                #{projectLabel,jdbcType=VARCHAR},
            </if>
            <if test="ifWaitAuditVehicle != null">
                #{ifWaitAuditVehicle,jdbcType=INTEGER},
            </if>
            <if test="correctStatus != null">
                #{correctStatus,jdbcType=INTEGER},
            </if>
            <if test="stockInState != null">
                #{stockInState,jdbcType=INTEGER},
            </if>
            <if test="carrierSettleStatementStatus != null">
                #{carrierSettleStatementStatus,jdbcType=INTEGER},
            </if>
            <if test="qrCodePicPath != null">
                #{qrCodePicPath,jdbcType=VARCHAR},
            </if>
            <if test="deliveryMethod != null">
                #{deliveryMethod,jdbcType=INTEGER},
            </if>
            <if test="orderMode != null">
                #{orderMode,jdbcType=INTEGER},
            </if>
            <if test="vehicleLengthId != null">
                #{vehicleLengthId,jdbcType=BIGINT},
            </if>
            <if test="vehicleLength != null">
                #{vehicleLength,jdbcType=DECIMAL},
            </if>
            <if test="bargainingMode != null">
                #{bargainingMode,jdbcType=INTEGER},
            </if>
            <if test="ifRecycleByCode != null">
                #{ifRecycleByCode,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelectiveEncrypt" parameterType="com.logistics.tms.entity.TCarrierOrder">
        update t_carrier_order
        <set>
            <if test="dispatchOrderId != null">
                dispatch_order_id = #{dispatchOrderId,jdbcType=BIGINT},
            </if>
            <if test="dispatchOrderCode != null">
                dispatch_order_code = #{dispatchOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="demandOrderId != null">
                demand_order_id = #{demandOrderId,jdbcType=BIGINT},
            </if>
            <if test="demandOrderCode != null">
                demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="carrierOrderCode != null">
                carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="customerOrderCode != null">
                customer_order_code = #{customerOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="statusUpdateTime != null">
                status_update_time = #{statusUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ifCancel != null">
                if_cancel = #{ifCancel,jdbcType=INTEGER},
            </if>
            <if test="cancelReason != null">
                cancel_reason = #{cancelReason,jdbcType=VARCHAR},
            </if>
            <if test="cancelOperatorName != null">
                cancel_operator_name = #{cancelOperatorName,jdbcType=VARCHAR},
            </if>
            <if test="cancelTime != null">
                cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
            </if>
            <if test="businessType != null">
                business_type = #{businessType,jdbcType=INTEGER},
            </if>
            <if test="customerName != null">
                customer_name = #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="customerUserName != null">
                customer_user_name = #{customerUserName,jdbcType=VARCHAR},
            </if>
            <if test="customerUserMobile != null">
                customer_user_mobile = HEX(AES_ENCRYPT(#{customerUserMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="customerOrderSource != null">
                customer_order_source = #{customerOrderSource,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                source = #{source,jdbcType=INTEGER},
            </if>
            <if test="dispatchUserId != null">
                dispatch_user_id = #{dispatchUserId,jdbcType=BIGINT},
            </if>
            <if test="dispatchUserName != null">
                dispatch_user_name = #{dispatchUserName,jdbcType=VARCHAR},
            </if>
            <if test="dispatchTime != null">
                dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
            </if>
            <if test="companyCarrierType != null">
                company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
            </if>
            <if test="companyCarrierId != null">
                company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
            </if>
            <if test="companyCarrierName != null">
                company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
            </if>
            <if test="carrierContactId != null">
                carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
            </if>
            <if test="carrierContactName != null">
                carrier_contact_name = #{carrierContactName,jdbcType=VARCHAR},
            </if>
            <if test="carrierContactPhone != null">
                carrier_contact_phone = HEX(AES_ENCRYPT(#{carrierContactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="companyCarrierLevel != null">
                company_carrier_level = #{companyCarrierLevel,jdbcType=INTEGER},
            </if>
            <if test="companyEntrustId != null">
                company_entrust_id = #{companyEntrustId,jdbcType=BIGINT},
            </if>
            <if test="companyEntrustName != null">
                company_entrust_name = #{companyEntrustName,jdbcType=VARCHAR},
            </if>
            <if test="upstreamCustomer != null">
                upstream_customer = #{upstreamCustomer,jdbcType=VARCHAR},
            </if>
            <if test="loadTime != null">
                load_time = #{loadTime,jdbcType=TIMESTAMP},
            </if>
            <if test="unloadTime != null">
                unload_time = #{unloadTime,jdbcType=TIMESTAMP},
            </if>
            <if test="signTime != null">
                sign_time = #{signTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expectAmount != null">
                expect_amount = #{expectAmount,jdbcType=DECIMAL},
            </if>
            <if test="loadAmountExpect != null">
                load_amount_expect = #{loadAmountExpect,jdbcType=DECIMAL},
            </if>
            <if test="loadAmount != null">
                load_amount = #{loadAmount,jdbcType=DECIMAL},
            </if>
            <if test="unloadAmountExpect != null">
                unload_amount_expect = #{unloadAmountExpect,jdbcType=DECIMAL},
            </if>
            <if test="unloadAmount != null">
                unload_amount = #{unloadAmount,jdbcType=DECIMAL},
            </if>
            <if test="signAmount != null">
                sign_amount = #{signAmount,jdbcType=DECIMAL},
            </if>
            <if test="expectEntrustFreight != null">
                expect_entrust_freight = #{expectEntrustFreight,jdbcType=DECIMAL},
            </if>
            <if test="expectEntrustFreightType != null">
                expect_entrust_freight_type = #{expectEntrustFreightType,jdbcType=INTEGER},
            </if>
            <if test="entrustFreightType != null">
                entrust_freight_type = #{entrustFreightType,jdbcType=INTEGER},
            </if>
            <if test="entrustFreight != null">
                entrust_freight = #{entrustFreight,jdbcType=DECIMAL},
            </if>
            <if test="signFreightFee != null">
                sign_freight_fee = #{signFreightFee,jdbcType=DECIMAL},
            </if>
            <if test="dispatchFreightFeeType != null">
                dispatch_freight_fee_type = #{dispatchFreightFeeType,jdbcType=INTEGER},
            </if>
            <if test="dispatchFreightFee != null">
                dispatch_freight_fee = #{dispatchFreightFee,jdbcType=DECIMAL},
            </if>
            <if test="adjustFee != null">
                adjust_fee = #{adjustFee,jdbcType=DECIMAL},
            </if>
            <if test="markupFee != null">
                markup_fee = #{markupFee,jdbcType=DECIMAL},
            </if>
            <if test="goodsUnit != null">
                goods_unit = #{goodsUnit,jdbcType=INTEGER},
            </if>
            <if test="demandOrderSource != null">
                demand_order_source = #{demandOrderSource,jdbcType=INTEGER},
            </if>
            <if test="demandOrderEntrustType != null">
                demand_order_entrust_type = #{demandOrderEntrustType,jdbcType=INTEGER},
            </if>
            <if test="settlementTonnage != null">
                settlement_tonnage = #{settlementTonnage,jdbcType=INTEGER},
            </if>
            <if test="carrierSettlement != null">
                carrier_settlement = #{carrierSettlement,jdbcType=INTEGER},
            </if>
            <if test="expectMileage != null">
                expect_mileage = #{expectMileage,jdbcType=DECIMAL},
            </if>
            <if test="configDistance != null">
                config_distance = #{configDistance,jdbcType=DECIMAL},
            </if>
            <if test="loadValidity != null">
                load_validity = #{loadValidity,jdbcType=INTEGER},
            </if>
            <if test="abnormalAmount != null">
                abnormal_amount = #{abnormalAmount,jdbcType=DECIMAL},
            </if>
            <if test="ifEmpty != null">
                if_empty = #{ifEmpty,jdbcType=INTEGER},
            </if>
            <if test="emptyTime != null">
                empty_time = #{emptyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="dispatchRemark != null">
                dispatch_remark = #{dispatchRemark,jdbcType=VARCHAR},
            </if>
            <if test="carrierPriceType != null">
                carrier_price_type = #{carrierPriceType,jdbcType=INTEGER},
            </if>
            <if test="carrierPrice != null">
                carrier_price = #{carrierPrice,jdbcType=DECIMAL},
            </if>
            <if test="printCount != null">
                print_count = #{printCount,jdbcType=INTEGER},
            </if>
            <if test="ifObjection != null">
                if_objection = #{ifObjection,jdbcType=INTEGER},
            </if>
            <if test="outStatus != null">
                out_status = #{outStatus,jdbcType=INTEGER},
            </if>
            <if test="publishName != null">
                publish_name = #{publishName,jdbcType=VARCHAR},
            </if>
            <if test="publishMobile != null">
                publish_mobile = HEX(AES_ENCRYPT(#{publishMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="publishTime != null">
                publish_time = #{publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="publishOrgCode != null">
                publish_org_code = #{publishOrgCode,jdbcType=VARCHAR},
            </if>
            <if test="publishOrgName != null">
                publish_org_name = #{publishOrgName,jdbcType=VARCHAR},
            </if>
            <if test="ifUrgent != null">
                if_urgent = #{ifUrgent,jdbcType=INTEGER},
            </if>
            <if test="availableOnWeekends != null">
                available_on_weekends = #{availableOnWeekends,jdbcType=INTEGER},
            </if>
            <if test="loadingUnloadingPart != null">
                loading_unloading_part = #{loadingUnloadingPart,jdbcType=INTEGER},
            </if>
            <if test="loadingUnloadingCharge != null">
                loading_unloading_charge = #{loadingUnloadingCharge,jdbcType=DECIMAL},
            </if>
            <if test="recycleTaskType != null">
                recycle_task_type = #{recycleTaskType,jdbcType=INTEGER},
            </if>
            <if test="projectLabel != null">
                project_label = #{projectLabel,jdbcType=VARCHAR},
            </if>
            <if test="ifWaitAuditVehicle != null">
                if_wait_audit_vehicle = #{ifWaitAuditVehicle,jdbcType=INTEGER},
            </if>
            <if test="correctStatus != null">
                correct_status = #{correctStatus,jdbcType=INTEGER},
            </if>
            <if test="stockInState != null">
                stock_in_state = #{stockInState,jdbcType=INTEGER},
            </if>
            <if test="carrierSettleStatementStatus != null">
                carrier_settle_statement_status = #{carrierSettleStatementStatus,jdbcType=INTEGER},
            </if>
            <if test="qrCodePicPath != null">
                qr_code_pic_path = #{qrCodePicPath,jdbcType=VARCHAR},
            </if>
            <if test="deliveryMethod != null">
                delivery_method = #{deliveryMethod,jdbcType=INTEGER},
            </if>
            <if test="orderMode != null">
                order_mode = #{orderMode,jdbcType=INTEGER},
            </if>
            <if test="vehicleLengthId != null">
                vehicle_length_id = #{vehicleLengthId,jdbcType=BIGINT},
            </if>
            <if test="vehicleLength != null">
                vehicle_length = #{vehicleLength,jdbcType=DECIMAL},
            </if>
            <if test="bargainingMode != null">
                bargaining_mode = #{bargainingMode,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <resultMap id="getCarrierOrderByDispatchOrderId_Map" type="com.logistics.tms.controller.dispatchorder.response.DispatchOrderCarrierChildResponseModel">
        <id column="carrierOrderId" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="demandOrderId" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="carrierOrderCode" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="customerOrderCode" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="ifCancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="ifEmpty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="dispatchFreightFeeType" property="dispatchFreightFeeType" jdbcType="INTEGER"/>
        <result column="dispatchFreightFee" property="dispatchFreightFee" jdbcType="DECIMAL"/>
        <result column="adjustFee" property="adjustFee" jdbcType="DECIMAL"/>
        <result column="markupFee" property="markupFee" jdbcType="DECIMAL"/>
        <result column="goodsUnit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="carrierExpectAmount" property="carrierExpectAmount" jdbcType="DECIMAL"/>
        <result column="carrierLoadAmount" property="carrierLoadAmount" jdbcType="DECIMAL"/>
        <result column="carrierUnloadAmount" property="carrierUnloadAmount" jdbcType="DECIMAL"/>
        <result column="carrierSignAmount" property="carrierSignAmount" jdbcType="DECIMAL"/>
        <result column="demandOrderSource" property="demandOrderSource" jdbcType="INTEGER"/>

        <result column="unloadProvinceName" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unloadCityName" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unloadAreaName" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unloadDetailAddress" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unloadWarehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <collection property="carrierOrderGoodsList" ofType="com.logistics.tms.controller.dispatchorder.response.CarrierOrderGoodsModel">
            <result column="goodsName" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goodsSize" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="expectAmount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="loadAmount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="unloadAmount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="signAmount" property="signAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>
    <select id="getCarrierOrderByDispatchOrderId" resultMap="getCarrierOrderByDispatchOrderId_Map">
        select
        tco.id as carrierOrderId,
        tco.demand_order_id as demandOrderId,
        tco.carrier_order_code as carrierOrderCode,
        tco.customer_order_code as customerOrderCode,
        tco.status as status,
        tco.if_cancel as ifCancel,
        tco.if_empty as ifEmpty,
        tco.dispatch_freight_fee_type as dispatchFreightFeeType,
        tco.dispatch_freight_fee as dispatchFreightFee,
        tco.adjust_fee AS adjustFee,
        tco.markup_fee AS markupFee,
        tco.goods_unit as goodsUnit,
        tco.expect_amount as carrierExpectAmount,
        tco.load_amount as carrierLoadAmount,
        tco.unload_amount as carrierUnloadAmount,
        tco.sign_amount as carrierSignAmount,
        tco.demand_order_source as demandOrderSource,

        tcoa.unload_province_name as unloadProvinceName,
        tcoa.unload_city_name as unloadCityName,
        tcoa.unload_area_name as unloadAreaName,
        tcoa.unload_detail_address as unloadDetailAddress,
        tcoa.unload_warehouse as unloadWarehouse,

        tcog.goods_name as goodsName,
        tcog.length as length,
        tcog.width as width,
        tcog.height as height,
        tcog.goods_size as goodsSize,
        tcog.expect_amount as expectAmount,
        tcog.load_amount as loadAmount,
        tcog.unload_amount as unloadAmount,
        tcog.sign_amount as signAmount
        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tcoa.carrier_order_id = tco.id and tcoa.valid = 1
        left join t_carrier_order_goods tcog on tcog.carrier_order_id = tco.id and tcog.valid = 1
        where tco.valid = 1
        <if test="params.dispatchOrderId != null">
            and tco.dispatch_order_id = #{params.dispatchOrderId,jdbcType=BIGINT}
        </if>
        order by tco.created_time desc,tco.id desc
    </select>
    <resultMap id="searchCarrierOrderForManagementMap"
               type="com.logistics.tms.controller.carrierorder.response.SearchCarrierOrderListResponseModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="dispatch_user_name" property="dispatchUserName" jdbcType="VARCHAR"/>
        <result column="dispatch_time" property="dispatchTime" jdbcType="TIMESTAMP"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="consignor_name" property="consignorName" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR"/>
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR"/>
        <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="driver_name" property="driverName" jdbcType="VARCHAR"/>
        <result column="driver_mobile" property="driverMobile" jdbcType="VARCHAR"/>
        <result column="expect_arrival_time" property="expectArrivalTime" jdbcType="TIMESTAMP"/>
        <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
        <result column="load_amount" property="loadAmount" jdbcType="DECIMAL"/>
        <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
        <result column="sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        <result column="load_time" property="loadTime" jdbcType="TIMESTAMP"/>
        <result column="unload_time" property="unloadTime" jdbcType="TIMESTAMP"/>
        <result column="sign_time" property="signTime" jdbcType="TIMESTAMP"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="dispatch_freight_fee_type" property="dispatchFreightFeeType" jdbcType="INTEGER"/>
        <result column="dispatch_freight_fee" property="dispatchFreightFee" jdbcType="DECIMAL"/>
        <result column="entrust_freight_type" property="entrustFreightType" jdbcType="INTEGER"/>
        <result column="entrust_freight" property="entrustFreight" jdbcType="DECIMAL"/>
        <result column="sign_freight_fee" property="signFreightFee" jdbcType="DECIMAL"/>
        <result column="carrier_price_type" property="carrierPriceType" jdbcType="INTEGER"/>
        <result column="carrier_price" property="carrierPrice" jdbcType="DECIMAL"/>
        <result column="adjust_fee" property="adjustFee" jdbcType="DECIMAL"/>
        <result column="markup_fee" property="markupFee" jdbcType="DECIMAL"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="settlement_tonnage" property="settlementTonnage" jdbcType="INTEGER"/>
        <result column="carrier_settlement" property="carrierSettlement" jdbcType="INTEGER"/>
        <result column="company_entrust_name" property="entrustCompany" jdbcType="VARCHAR"/>
        <result column="company_carrier_name" property="carrierCompany" jdbcType="VARCHAR"/>
        <result column="expect_mileage" property="expectMileage" jdbcType="DECIMAL"/>
        <result column="if_objection" property="ifObjection" jdbcType="INTEGER"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="carrier_contact_id" property="carrierContactId" jdbcType="BIGINT"/>
        <result column="company_carrier_type" property="companyCarrierType" jdbcType="INTEGER"/>
        <result column="carrier_contact_name" property="carrierContactName" jdbcType="VARCHAR"/>
        <result column="carrier_contact_phone" property="carrierContactMobile" jdbcType="VARCHAR"/>
        <result column="carrierPaymentPriceType" property="carrierPaymentPriceType" jdbcType="INTEGER"/>
        <result column="carrierSettlementAmount" property="carrierSettlementAmount" jdbcType="DECIMAL"/>
        <result column="carrierSettlementCostTotal" property="carrierSettlementCostTotal" jdbcType="DECIMAL"/>
        <result column="company_carrier_level" property="isOurCompany" jdbcType="INTEGER"/>
        <result column="demand_order_source" property="demandOrderSource" jdbcType="INTEGER"/>
        <collection property="goodsInfoList"
                    ofType="com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="g_expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="g_load_amount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="g_unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="g_sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>

    <select id="searchCarrierOrderForManagement" resultMap="searchCarrierOrderForManagementMap">
        select
        tco.id,
        tco.carrier_order_code,
        tco.customer_order_code,
        tco.status,
        tco.dispatch_time,
        tco.expect_amount,
        tco.load_amount,
        tco.unload_amount,
        tco.sign_amount,
        tco.load_time,
        tco.unload_time,
        tco.sign_time,
        tco.demand_order_id,
        tco.demand_order_code,
        tco.remark,
        tco.dispatch_freight_fee_type,
        tco.dispatch_freight_fee,
        tco.entrust_freight_type,
        tco.entrust_freight,
        tco.sign_freight_fee,
        tco.adjust_fee,
        tco.markup_fee,
        tco.if_cancel,
        tco.if_empty,
        tco.dispatch_user_name,
        tco.goods_unit,
        tco.company_carrier_id,
        tco.company_carrier_name,
        tco.carrier_contact_id,
        tco.company_carrier_type,
        tco.carrier_contact_name,
        AES_DECRYPT(UNHEX(tco.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrier_contact_phone,
        tco.company_entrust_name,
        tco.expect_mileage,
        tco.if_objection,
        tco.settlement_tonnage,
        tco.carrier_settlement,
        tco.carrier_price_type,
        tco.carrier_price,
        tco.company_carrier_level,
        tco.demand_order_source,

        tcoa.load_province_name,
        tcoa.load_city_name,
        tcoa.load_area_name,
        tcoa.load_detail_address,
        tcoa.load_warehouse,
        tcoa.unload_province_name,
        tcoa.unload_city_name,
        tcoa.unload_area_name,
        tcoa.unload_detail_address,
        tcoa.unload_warehouse,
        tcoa.consignor_name,
        tcoa.consignor_mobile,
        tcoa.receiver_name,
        tcoa.receiver_mobile,

        tcog.id as goodsId,
        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount AS g_expect_amount,
        tcog.load_amount AS g_load_amount,
        tcog.unload_amount AS g_unload_amount,
        tcog.sign_amount AS g_sign_amount

        from t_carrier_order tco
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        LEFT JOIN t_carrier_order_goods tcog on tcog.valid = 1 and tcog.carrier_order_id = tco.id
        where tco.valid= 1
        and tco.id in (${ids})
        order by tco.created_time desc,tco.id desc
    </select>

    <select id="searchCarrierOrderIdsForManagement" resultType="java.lang.Long">
        select
        DISTINCT tco.id
        from t_carrier_order tco
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        LEFT JOIN t_carrier_order_goods tcog on tcog.valid = 1 and tcog.carrier_order_id = tco.id
        where tco.valid= 1 and tco.demand_order_source in (2,3,4)
        <if test="condition.carrierOrderIds!=null and condition.carrierOrderIds!=''">
            and tco.id in (${condition.carrierOrderIds})
        </if>
        <if test="condition.status != null">
            <choose>
                <when test="condition.status==0">
                    and tco.if_cancel = 1
                </when>
                <otherwise>
                    and tco.status = #{condition.status,jdbcType=VARCHAR} and tco.if_cancel = 0 and tco.if_empty = 0
                </otherwise>
            </choose>
        </if>
        <if test="condition.ifWaitAudit==1">
            and tco.if_wait_audit_vehicle = 1
        </if>
        <if test="condition.carrierOrderCode !=null and condition.carrierOrderCode != ''">
            and (INSTR(tco.carrier_order_code,#{condition.carrierOrderCode,jdbcType=VARCHAR})>0
            or INSTR(REPLACE(tco.carrier_order_code,'-',''),#{condition.carrierOrderCode,jdbcType=VARCHAR})>0)
        </if>
        <if test="condition.customerOrderCode !=null and condition.customerOrderCode != ''">
            and instr(tco.customer_order_code,#{condition.customerOrderCode,jdbcType=VARCHAR})>0
        </if>
        <if test="condition.demandOrderCode !=null and condition.demandOrderCode != ''">
            and INSTR(tco.demand_order_code,#{condition.demandOrderCode,jdbcType=VARCHAR})>0
        </if>
        <if test="condition.loadAddress!=null and condition.loadAddress!=''">
            and instr(concat(tcoa.load_province_name,tcoa.load_city_name,tcoa.load_area_name,tcoa.load_detail_address,tcoa.load_warehouse),#{condition.loadAddress,jdbcType=VARCHAR})
        </if>
        <if test="condition.unloadAddress!=null and condition.unloadAddress!=''">
            and instr(concat(tcoa.unload_province_name,tcoa.unload_city_name,tcoa.unload_area_name,tcoa.unload_detail_address,tcoa.unload_warehouse),#{condition.unloadAddress,jdbcType=VARCHAR})
        </if>
        <if test="condition.consignorName!=null and condition.consignorName!=''">
            and (instr(tcoa.consignor_name,#{condition.consignorName,jdbcType=VARCHAR})>0
            or instr(tcoa.consignor_mobile,#{condition.consignorName,jdbcType=VARCHAR})>0)
        </if>
        <if test="condition.receiverName!=null and condition.receiverName!=''">
            and (instr(tcoa.receiver_name,#{condition.receiverName,jdbcType=VARCHAR})>0
            or instr(tcoa.receiver_mobile,#{condition.receiverName,jdbcType=VARCHAR})>0)
        </if>
        <if test="condition.dispatchUserName!=null and condition.dispatchUserName!=''">
            and instr(tco.dispatch_user_name,#{condition.dispatchUserName,jdbcType=VARCHAR})>0
        </if>
        <if test="condition.goodsName!=null and condition.goodsName!=''">
            and instr(tcog.goods_name,#{condition.goodsName,jdbcType=VARCHAR})>0
        </if>
        <if test="condition.size!=null and condition.size!=''">
            and (instr(concat(tcog.length,'*',tcog.width,'*',tcog.height),#{condition.size,jdbcType=VARCHAR})>0
            or instr(tcog.goods_size,#{condition.size,jdbcType=VARCHAR})>0)
        </if>
        <if test="condition.dispatchTimeFrom!=null and condition.dispatchTimeFrom!=''">
            and tco.dispatch_time &gt;= DATE_FORMAT(#{condition.dispatchTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="condition.dispatchTimeTo!=null and condition.dispatchTimeTo!=''">
            and tco.dispatch_time &lt;= DATE_FORMAT(#{condition.dispatchTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="condition.unloadTimeFrom!=null and condition.unloadTimeFrom!=''">
            and tco.unload_time &gt;= DATE_FORMAT(#{condition.unloadTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d
            %H:%i:%S')
        </if>
        <if test="condition.unloadTimeTo!=null and condition.unloadTimeTo!=''">
            and tco.unload_time &lt;= DATE_FORMAT(#{condition.unloadTimeTo,jdbcType=VARCHAR},'%Y-%m-%d
            23:59:59')
        </if>
        <if test="condition.companyEntrustIds!=null and condition.companyEntrustIds !=''">
            and tco.company_entrust_id in (${condition.companyEntrustIds})
        </if>
        <if test="condition.carrierCompany != null and condition.carrierCompany !=''">
            and ((tco.company_carrier_type = 1 and instr(tco.company_carrier_name,#{condition.carrierCompany,jdbcType=VARCHAR}))
            or (tco.company_carrier_type = 2 and (instr(tco.carrier_contact_name,#{condition.carrierCompany,jdbcType=VARCHAR}) or instr(AES_DECRYPT(UNHEX(tco.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') ,#{condition.carrierCompany,jdbcType=VARCHAR}))))
        </if>

        <!-- 需求单下单时间 -->
        <if test="condition.demandCreatedTimeFrom!=null and condition.demandCreatedTimeFrom!='' ">
            and tco.publish_time >= DATE_FORMAT(#{condition.demandCreatedTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="condition.demandCreatedTimeTo!=null and condition.demandCreatedTimeTo!='' ">
            and tco.publish_time &lt;= DATE_FORMAT(#{condition.demandCreatedTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="condition.ifObjection !=null and condition.ifObjection!='' and condition.ifObjection==1">
            and tco.if_objection = 1
        </if>
        <if test="condition.carrierOrderIdList !=null and condition.carrierOrderIdList.size>0">
            and tco.id in (
            <foreach collection="condition.carrierOrderIdList" item="item"  separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
            )
        </if>
        <if test="condition.customerOrderCodeList != null and condition.customerOrderCodeList.size > 0">
            and tco.customer_order_code in (
            <foreach collection="condition.customerOrderCodeList" item="item" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
        <if test="condition.demandOrderCodeList != null and condition.demandOrderCodeList.size > 0">
            and tco.demand_order_code in (
            <foreach collection="condition.demandOrderCodeList" item="item" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
        <if test="condition.ifRecycleByCode != null">
            and tco.if_recycle_by_code = #{condition.ifRecycleByCode,jdbcType=INTEGER}
            and tco.demand_order_entrust_type = 100
        </if>
        order by tco.created_time desc,tco.id desc
    </select>

    <resultMap id="searchCarrierOrderForLeYiManagementMap" type="com.logistics.tms.controller.carrierorder.response.SearchCarrierOrderListForLeYiResponseModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="dispatch_order_code" property="dispatchOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="dispatch_user_name" property="dispatchUserName" jdbcType="VARCHAR"/>
        <result column="dispatch_time" property="dispatchTime" jdbcType="TIMESTAMP"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="consignor_name" property="consignorName" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR"/>
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR"/>
        <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
        <result column="load_amount" property="loadAmount" jdbcType="DECIMAL"/>
        <result column="load_amount_expect" property="loadAmountExpect" jdbcType="DECIMAL"/>
        <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
        <result column="sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        <result column="abnormal_amount" property="abnormalAmount" jdbcType="DECIMAL"/>
        <result column="load_time" property="loadTime" jdbcType="TIMESTAMP"/>
        <result column="unload_time" property="unloadTime" jdbcType="TIMESTAMP"/>
        <result column="sign_time" property="signTime" jdbcType="TIMESTAMP"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="dispatch_freight_fee_type" property="dispatchFreightFeeType" jdbcType="INTEGER"/>
        <result column="dispatch_freight_fee" property="dispatchFreightFee" jdbcType="DECIMAL"/>
        <result column="sign_freight_fee" property="signFreightFee" jdbcType="DECIMAL"/>
        <result column="adjust_fee" property="adjustFee" jdbcType="DECIMAL"/>
        <result column="markup_fee" property="markupFee" jdbcType="DECIMAL"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="company_entrust_name" property="entrustCompany" jdbcType="VARCHAR"/>
        <result column="company_carrier_name" property="carrierCompany" jdbcType="VARCHAR"/>
        <result column="expect_mileage" property="expectMileage" jdbcType="DECIMAL"/>
        <result column="load_validity" property="loadValidity" jdbcType="INTEGER"/>
        <result column="load_region_name" property="loadRegionName" jdbcType="VARCHAR"/>
        <result column="load_region_contact_name" property="loadRegionContactName" jdbcType="VARCHAR"/>
        <result column="load_region_contact_phone" property="loadRegionContactPhone" jdbcType="VARCHAR"/>
        <result column="expected_load_time" property="expectedLoadTime" jdbcType="TIMESTAMP"/>
        <result column="expected_unload_time" property="expectedUnloadTime" jdbcType="TIMESTAMP"/>
        <result column="out_status" property="outStatus" jdbcType="INTEGER"/>
        <result column="entrust_freight_type" property="entrustFreightType" jdbcType="INTEGER"/>
        <result column="entrust_freight" property="entrustFreight" jdbcType="DECIMAL"/>
        <result column="carrier_price_type" property="carrierPriceType" jdbcType="INTEGER"/>
        <result column="carrier_price" property="carrierPrice" jdbcType="DECIMAL"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="carrier_contact_id" property="carrierContactId" jdbcType="BIGINT"/>
        <result column="company_carrier_type" property="companyCarrierType" jdbcType="INTEGER"/>
        <result column="carrier_contact_name" property="carrierContactName" jdbcType="VARCHAR"/>
        <result column="carrier_contact_phone" property="carrierContactMobile" jdbcType="VARCHAR"/>
        <result column="settlement_tonnage" property="settlementTonnage" jdbcType="INTEGER"/>
        <result column="carrier_settlement" property="carrierSettlement" jdbcType="INTEGER"/>
        <result column="demand_order_entrust_type" property="entrustType" jdbcType="INTEGER"/>
        <result column="if_urgent" property="ifUrgent" jdbcType="INTEGER"/>
        <result column="correct_status" property="correctStatus" jdbcType="INTEGER"/>
        <result column="company_carrier_level" property="isOurCompany" jdbcType="INTEGER"/>
        <result column="publish_org_name" property="publishOrgName" jdbcType="VARCHAR"/>
        <result column="recycle_task_type" property="recycleTaskType" jdbcType="INTEGER"/>
        <result column="config_distance" property="configDistance" jdbcType="VARCHAR"/>
        <result column="statement_other_fee_tax_point" property="statementOtherFeeTaxPoint" jdbcType="DECIMAL"/>
        <result column="statement_freight_tax_point" property="statementFreightTaxPoint" jdbcType="DECIMAL"/>
        <result column="if_ext_carrier_order" property="ifExtCarrierOrder" jdbcType="INTEGER"/>
        <result column="demand_order_entrust_type" property="demandOrderEntrustType" jdbcType="INTEGER"/>

        <collection property="goodsInfoList"
                    ofType="com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="g_expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="g_load_amount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="g_unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="g_sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>
    <select id="searchCarrierOrderForLeYiManagement" resultMap="searchCarrierOrderForLeYiManagementMap">
        select
        tco.id,
        tco.carrier_order_code,
        tco.dispatch_order_code,
        tco.customer_order_code,
        tco.status,
        tco.dispatch_time,
        tco.expect_amount,
        tco.load_amount,
        tco.load_amount_expect,
        tco.unload_amount,
        tco.sign_amount,
        tco.abnormal_amount,
        tco.load_time,
        tco.unload_time,
        tco.sign_time,
        tco.demand_order_id,
        tco.demand_order_code,
        tco.remark,
        tco.dispatch_freight_fee_type,
        tco.dispatch_freight_fee,
        tco.sign_freight_fee,
        tco.adjust_fee,
        tco.markup_fee,
        tco.if_cancel,
        tco.if_empty,
        tco.dispatch_user_name,
        tco.goods_unit,
        tco.company_carrier_id,
        tco.company_carrier_name,
        tco.carrier_contact_id,
        tco.company_carrier_type,
        tco.carrier_contact_name,
        AES_DECRYPT(UNHEX(tco.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrier_contact_phone,
        tco.company_entrust_name,
        tco.expect_mileage,
        tco.load_validity,
        tco.out_status,
        tco.entrust_freight,
        tco.entrust_freight_type,
        tco.carrier_price_type,
        tco.carrier_price,
        tco.settlement_tonnage,
        tco.carrier_settlement,
        tco.if_urgent,
        tco.demand_order_entrust_type,
        tco.correct_status,
        tco.company_carrier_level,
        tco.publish_org_name,
        tco.recycle_task_type,
        tco.config_distance,
        tco.statement_other_fee_tax_point,
        tco.statement_freight_tax_point,
        tco.if_ext_carrier_order,
        tco.demand_order_entrust_type,

        tcoa.load_province_name,
        tcoa.load_city_name,
        tcoa.load_area_name,
        tcoa.load_detail_address,
        tcoa.load_warehouse,
        tcoa.unload_province_name,
        tcoa.unload_city_name,
        tcoa.unload_area_name,
        tcoa.unload_detail_address,
        tcoa.unload_warehouse,
        tcoa.consignor_name,
        tcoa.consignor_mobile,
        tcoa.receiver_name,
        tcoa.receiver_mobile,
        tcoa.load_region_name,
        tcoa.load_region_contact_name,
        tcoa.load_region_contact_phone,
        tcoa.expected_load_time,
        tcoa.expected_unload_time,

        tcog.id as goodsId,
        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount AS g_expect_amount,
        tcog.load_amount AS g_load_amount,
        tcog.unload_amount AS g_unload_amount,
        tcog.sign_amount AS g_sign_amount

        from t_carrier_order tco
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        LEFT JOIN t_carrier_order_goods tcog on tcog.valid = 1 and tcog.carrier_order_id = tco.id
        where tco.valid= 1
        and tco.id in (${ids})
        order by tco.created_time desc,tco.id desc
    </select>

    <select id="searchCarrierOrderIdsForLeYiManagement" resultType="java.lang.Long">
        select
        DISTINCT tco.id
        from t_carrier_order tco
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        LEFT JOIN t_carrier_order_goods tcog on tcog.valid = 1 and tcog.carrier_order_id = tco.id
        where tco.valid= 1 and tco.demand_order_source = 1
        <choose>
            <when test="condition.carrierOrderIds!=null and condition.carrierOrderIds!=''">
                and tco.id in (${condition.carrierOrderIds})
            </when>
            <otherwise>
                <if test="condition.statusList != null and condition.statusList.size > 0">
                    and
                    <foreach collection="condition.statusList" item="status" separator="or" open="(" close=")">
                        <choose>
                            <when test="status==0">
                                tco.if_cancel = 1
                            </when>
                            <when test="status==2">
                                tco.if_empty = 1
                            </when>
                            <otherwise>
                                (tco.status = #{status,jdbcType=INTEGER}  and tco.if_cancel = 0 and tco.if_empty = 0)
                            </otherwise>
                        </choose>
                    </foreach>
                </if>
                <if test="condition.ifWaitAudit==1">
                    and tco.if_wait_audit_vehicle = 1
                </if>
                <if test="condition.correctStatus != null">
                    and tco.correct_status = #{condition.correctStatus,jdbcType=INTEGER}
                </if>
                <if test="condition.carrierOrderCode !=null and condition.carrierOrderCode != ''">
                    and (INSTR(tco.carrier_order_code,#{condition.carrierOrderCode,jdbcType=VARCHAR})>0
                    or INSTR(REPLACE(tco.carrier_order_code,'-',''),#{condition.carrierOrderCode,jdbcType=VARCHAR})>0)
                </if>
                <if test="condition.customerOrderCode !=null and condition.customerOrderCode != ''">
                    and instr(tco.customer_order_code,#{condition.customerOrderCode,jdbcType=VARCHAR})>0
                </if>
                <if test="condition.demandOrderCode !=null and condition.demandOrderCode != ''">
                    and INSTR(tco.demand_order_code,#{condition.demandOrderCode,jdbcType=VARCHAR})>0
                </if>
                <if test="condition.dispatchOrderCode != null and condition.dispatchOrderCode !=''">
                    and instr(tco.dispatch_order_code,#{condition.dispatchOrderCode,jdbcType=VARCHAR})
                </if>
                <if test="condition.dispatchUserName!=null and condition.dispatchUserName!=''">
                    and instr(tco.dispatch_user_name,#{condition.dispatchUserName,jdbcType=VARCHAR})>0
                </if>
                <if test="condition.dispatchTimeFrom!=null and condition.dispatchTimeFrom!=''">
                    and tco.dispatch_time &gt;= DATE_FORMAT(#{condition.dispatchTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
                </if>
                <if test="condition.dispatchTimeTo!=null and condition.dispatchTimeTo!=''">
                    and tco.dispatch_time &lt;= DATE_FORMAT(#{condition.dispatchTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
                </if>
                <if test="condition.loadTimeStart!=null and condition.loadTimeStart!=''">
                    and tco.load_time &gt;= DATE_FORMAT(#{condition.loadTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
                </if>
                <if test="condition.loadTimeEnd!=null and condition.loadTimeEnd!=''">
                    and tco.load_time &lt;= DATE_FORMAT(#{condition.loadTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
                </if>
                <if test="condition.signTimeStart!=null and condition.signTimeStart!=''">
                    and tco.sign_time &gt;= DATE_FORMAT(#{condition.signTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
                </if>
                <if test="condition.signTimeEnd!=null and condition.signTimeEnd!=''">
                    and tco.sign_time &lt;= DATE_FORMAT(#{condition.signTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
                </if>
                <if test="condition.carrierCompany != null and condition.carrierCompany !=''">
                    and ((tco.company_carrier_type = 1 and instr(tco.company_carrier_name,#{condition.carrierCompany,jdbcType=VARCHAR}))
                    or (tco.company_carrier_type = 2 and (instr(tco.carrier_contact_name,#{condition.carrierCompany,jdbcType=VARCHAR}) or instr(AES_DECRYPT(UNHEX(tco.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') ,#{condition.carrierCompany,jdbcType=VARCHAR}))))
                </if>
                <if test="condition.demandCreatedTimeFrom!=null and condition.demandCreatedTimeFrom!='' ">
                    and tco.publish_time >= DATE_FORMAT(#{condition.demandCreatedTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
                </if>
                <if test="condition.demandCreatedTimeTo!=null and condition.demandCreatedTimeTo!='' ">
                    and tco.publish_time &lt;= DATE_FORMAT(#{condition.demandCreatedTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
                </if>
                <if test="condition.entrustType!=null">
                    and tco.demand_order_entrust_type = #{condition.entrustType,jdbcType=INTEGER}
                </if>
                <if test="condition.ifUrgent!=null">
                    and tco.if_urgent=#{condition.ifUrgent,jdbcType=INTEGER}
                </if>
                <if test="condition.carrierOrderIdList !=null and condition.carrierOrderIdList.size>0">
                    and tco.id in (
                    <foreach collection="condition.carrierOrderIdList" item="item"  separator=",">
                        #{item,jdbcType=BIGINT}
                    </foreach>
                    )
                </if>
                <if test="condition.carrierOrderCodeList !=null and condition.carrierOrderCodeList.size>0">
                    and tco.carrier_order_code in (
                    <foreach collection="condition.carrierOrderCodeList" item="item"  separator=",">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                    )
                </if>
                <if test="condition.demandOrderCodeList!=null and condition.demandOrderCodeList.size>0">
                    and tco.demand_order_code in (
                    <foreach collection="condition.demandOrderCodeList" item="item"  separator=",">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                    )
                </if>
                <if test="condition.outStatus != null">
                    and tco.out_status = #{condition.outStatus,jdbcType=INTEGER}
                    and tco.demand_order_entrust_type in (1,3,4,7,9,11,12)
                </if>

                <if test="condition.goods !=null and condition.goods != ''">
                    and instr(tcog.goods_name,#{condition.goods,jdbcType=VARCHAR})>0
                </if>

                <if test="condition.loadAddress!=null and condition.loadAddress!=''">
                    and instr(concat(tcoa.load_province_name,tcoa.load_city_name,tcoa.load_area_name,tcoa.load_detail_address),#{condition.loadAddress,jdbcType=VARCHAR})
                </if>
                <if test="condition.unloadAddress!=null and condition.unloadAddress!=''">
                    and instr(concat(tcoa.unload_province_name,tcoa.unload_city_name,tcoa.unload_area_name,tcoa.unload_detail_address),#{condition.unloadAddress,jdbcType=VARCHAR})
                </if>
                <if test="condition.loadWarehouse!=null and condition.loadWarehouse!=''">
                    and instr(tcoa.load_warehouse,#{condition.loadWarehouse,jdbcType=VARCHAR})>0
                </if>
                <if test="condition.unloadWarehouse!=null and condition.unloadWarehouse!=''">
                    and instr(tcoa.unload_warehouse,#{condition.unloadWarehouse,jdbcType=VARCHAR})>0
                </if>
                <if test="condition.loadRegionName != null and condition.loadRegionName !=''">
                    and instr(tcoa.load_region_name,#{condition.loadRegionName,jdbcType=VARCHAR})
                </if>
                <if test="condition.consignor != null and condition.consignor !=''">
                    and (instr(tcoa.consignor_name,#{condition.consignor,jdbcType=VARCHAR}) or instr(tcoa.consignor_mobile,#{condition.consignor,jdbcType=VARCHAR}))
                </if>
                <if test="condition.receiver != null and condition.receiver !=''">
                    and (instr(tcoa.receiver_name,#{condition.receiver,jdbcType=VARCHAR}) or instr(tcoa.receiver_mobile,#{condition.receiver,jdbcType=VARCHAR}))
                </if>
                <if test="condition.orgCode!=null and condition.orgCode!=''">
                    <bind name="orgCodeLike" value="condition.orgCode + '%'"/>
                    and tco.publish_org_code like #{orgCodeLike}
                </if>
                <if test="condition.recycleTaskType != null">
                    and tco.recycle_task_type = #{condition.recycleTaskType,jdbcType=INTEGER}
                </if>
                <if test="condition.ifExtCarrierOrder != null and condition.ifExtCarrierOrder != ''">
                    and tco.if_ext_carrier_order = #{condition.ifExtCarrierOrder,jdbcType=VARCHAR}
                </if>
                <if test="condition.projectLabel != null">
                    and FIND_IN_SET(#{condition.projectLabel,jdbcType=INTEGER}, tco.project_label)
                </if>
                <if test="condition.excludeCarrierOrderIdList != null and condition.excludeCarrierOrderIdList.size() > 0">
                    and tco.id not in
                    <foreach collection="condition.excludeCarrierOrderIdList" item="item" separator="," open="(" close=")">
                        #{item,jdbcType=BIGINT}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        order by tco.created_time desc,tco.id desc
    </select>

    <select id="getWaitAuditVehicleCount" resultType="java.lang.Integer">
        select
        count(id)
        from t_carrier_order
        where valid = 1
        and if_wait_audit_vehicle = 1
        and demand_order_source in (2,3,4)
        and publish_time >= DATE_FORMAT(#{condition.demandCreatedTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        and publish_time &lt;= DATE_FORMAT(#{condition.demandCreatedTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
    </select>

    <select id="getWaitAuditVehicleCountForLeYi" resultType="java.lang.Integer">
        select
        count(id)
        from t_carrier_order
        where valid = 1
        and if_wait_audit_vehicle = 1
        and demand_order_source = 1
        and publish_time >= DATE_FORMAT(#{condition.demandCreatedTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        and publish_time &lt;= DATE_FORMAT(#{condition.demandCreatedTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
    </select>

    <resultMap id="getCarrierOrderInfoByDemandIdMap"
               type="com.logistics.tms.controller.carrierorder.response.DemandOrderCarrierDetailResponseModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="dispatch_time" property="dispatchTime" jdbcType="TIMESTAMP"/>
        <result column="dispatch_user_name" property="dispatchUserName" jdbcType="VARCHAR"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="vehicle_no" property="vehicleNumber" jdbcType="VARCHAR"/>
        <result column="driver_name" property="driverName" jdbcType="VARCHAR"/>
        <result column="driver_mobile" property="driverMobile" jdbcType="VARCHAR"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
        <result column="load_amount" property="loadAmount" jdbcType="DECIMAL"/>
        <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
        <result column="sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        <result column="sign_freight_fee" property="signFreightFee" jdbcType="DECIMAL"/>
        <result column="entrust_freight_type" property="entrustFreightType" jdbcType="INTEGER"/>
        <result column="entrust_freight" property="entrustFreight" jdbcType="DECIMAL"/>
        <result column="dispatch_freight_fee_type" property="dispatchFreightFeeType" jdbcType="INTEGER"/>
        <result column="dispatch_freight_fee" property="dispatchFreightFee" jdbcType="DECIMAL"/>
        <result column="adjust_fee" property="adjustFee" jdbcType="DECIMAL"/>
        <result column="markup_fee" property="markupFee" jdbcType="DECIMAL"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <collection property="goodsInfoList"
                    ofType="com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="expectAmount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="loadAmount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="unloadAmount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="signAmount" property="signAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>
    <select id="getCarrierOrderInfoByDemandId" resultMap="getCarrierOrderInfoByDemandIdMap">
    SELECT
	tco.id,
	tco.demand_order_id,
	tco.carrier_order_code,
    tco.dispatch_user_name,
    tco.dispatch_time,
    tco.if_cancel,
    tco.if_empty,
    tco.status,
    tco.goods_unit,
    tco.expect_amount,
    tco.load_amount,
    tco.unload_amount,
    tco.sign_amount,
    tco.entrust_freight,
    tco.entrust_freight_type,
    tco.sign_freight_fee,
    tco.dispatch_freight_fee_type,
    tco.dispatch_freight_fee,
    tco.adjust_fee,
    tco.markup_fee,
    tco.company_carrier_id,

    tcog.id as goodsId,
    tcog.goods_name,
    tcog.length,
    tcog.width,
    tcog.height,
    tcog.goods_size,
    tcog.expect_amount as expectAmount,
    tcog.load_amount as loadAmount,
    tcog.unload_amount as unloadAmount,
    tcog.sign_amount as signAmount,

    tcovh.driver_name,
    tcovh.driver_mobile,
    tcovh.vehicle_no
    FROM
	t_carrier_order tco
    LEFT JOIN t_carrier_order_goods tcog ON tco.id = tcog.carrier_order_id and tcog.valid= 1
    LEFT JOIN t_carrier_order_vehicle_history tcovh on tco.id = tcovh.carrier_order_id and tcovh.valid= 1 and tcovh.if_invalid=1
    WHERE tco.valid = 1
    AND tco.demand_order_id =#{demandId}
    order by tco.id desc
    </select>

    <select id="getEmptyCarrierOrderInfoByDemandIds" resultMap="getCarrierOrderInfoByDemandIdMap">
        SELECT
        tco.id,
        tco.demand_order_id,
        tco.carrier_order_code,
        tco.dispatch_user_name,
        tco.dispatch_time,
        tco.if_cancel,
        tco.if_empty,
        tco.status,
        tco.goods_unit,
        tco.expect_amount,
        tco.load_amount,
        tco.unload_amount,
        tco.sign_amount,
        tco.entrust_freight,
        tco.entrust_freight_type,
        tco.sign_freight_fee,
        tco.dispatch_freight_fee_type,
        tco.dispatch_freight_fee,
        tco.adjust_fee,
        tco.markup_fee,
        tco.company_carrier_id,

        tcog.id as goodsId,
        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount as expectAmount,
        tcog.load_amount as loadAmount,
        tcog.unload_amount as unloadAmount,
        tcog.sign_amount as signAmount,

        tcovh.driver_name,
        tcovh.driver_mobile,
        tcovh.vehicle_no
        FROM
        t_carrier_order tco
        LEFT JOIN t_carrier_order_goods tcog ON tco.id = tcog.carrier_order_id and tcog.valid= 1
        LEFT JOIN t_carrier_order_vehicle_history tcovh on tco.id = tcovh.carrier_order_id and tcovh.valid= 1 and tcovh.if_invalid=1
        WHERE tco.valid = 1
        AND tco.if_empty = 1
        AND tco.demand_order_id in (${demandOrderIds})
    </select>


    <resultMap id="carrierOrderDetailBasicInfoMap" type="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailBasicInfoModel">
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR" />
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR" />
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR" />
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR" />
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR" />
        <result column="consignor_name" property="consignorName" jdbcType="VARCHAR" />
        <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR" />
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR" />
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR" />
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR" />
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR" />
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR" />
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR" />
        <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR" />
        <result column="load_time" property="loadTime" jdbcType="TIMESTAMP" />
        <result column="unload_time" property="unloadTime" jdbcType="TIMESTAMP" />
    </resultMap>
    <resultMap id="carrierOrderDetailFreightFeeInfoMap" type="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailFreightFeeInfoModel">
        <result column="dispatch_freight_fee_type" property="dispatchFreightFeeType" jdbcType="INTEGER"/>
        <result column="dispatch_freight_fee" property="dispatchFreightFee" jdbcType="DECIMAL" />
        <result column="adjust_fee" property="adjustFee" jdbcType="DECIMAL" />
        <result column="markup_fee" property="markupFee" jdbcType="DECIMAL" />
    </resultMap>

    <resultMap id="getCarrierOrderDetailForManagementByIdMap" type="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailResponseModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="dispatch_user_name" property="dispatchUserName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="dispatch_time" property="dispatchTime" jdbcType="TIMESTAMP"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
        <result column="load_amount" property="loadAmount" jdbcType="DECIMAL"/>
        <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
        <result column="sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        <result column="company_entrust_name" property="entrustCompany" jdbcType="VARCHAR"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="demand_order_entrust_type" property="entrustType" jdbcType="INTEGER"/>
        <result column="settlement_tonnage" property="entrustSettlementTonnage" jdbcType="INTEGER"/>
        <result column="carrier_settlement" property="carrierSettlementTonnage" jdbcType="INTEGER"/>
        <result column="carrier_settle_statement_status" property="carrierSettleStatementStatus" jdbcType="INTEGER"/>
        <association property="carrierOrderDetailBasicInfo" resultMap="carrierOrderDetailBasicInfoForManagementMap"/>
        <association property="carrierOrderDetailFreightFeeInfo" resultMap="carrierOrderDetailFreightFeeInfoForManagementMap"/>
        <collection property="carrierOrderDetailGoodsInfo" ofType="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailGoodsInfoModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="g_expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="g_load_amount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="g_unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="g_sign_amount" property="signAmount" jdbcType="DECIMAL"/>

        </collection>
        <collection property="carrierOrderDetailVehicleDriverInfo" ofType="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailVehicleDriverInfoModel">
            <id column="vehicleHistoryId" property="vehicleHistoryId" jdbcType="BIGINT"/>
            <result column="audit_status" property="auditStatus" jdbcType="INTEGER"/>
            <result column="if_invalid" property="ifInvalid" jdbcType="INTEGER"/>
            <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
            <result column="driver_name" property="driverName" jdbcType="VARCHAR"/>
            <result column="driver_mobile" property="driverMobile" jdbcType="VARCHAR"/>
            <result column="reject_reason" property="rejectReason" jdbcType="VARCHAR"/>
            <result column="vehicleHistoryRemark" property="remark" jdbcType="VARCHAR"/>
            <result column="vehicleHistoryCreateTime" property="createdTime" jdbcType="TIMESTAMP"/>
        </collection>
        <collection property="carrierOrderDetailEvents" ofType="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailEventModel">
            <id column="eventId" property="eventId" jdbcType="BIGINT"/>
            <result column="event_desc" property="eventDesc" jdbcType="VARCHAR"/>
            <result column="event_time" property="eventTime" jdbcType="TIMESTAMP"/>
        </collection>
    </resultMap>
    <resultMap id="carrierOrderDetailBasicInfoForManagementMap" type="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailBasicInfoModel">
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR" />
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR" />
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR" />
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR" />
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR" />
        <result column="consignor_name" property="consignorName" jdbcType="VARCHAR" />
        <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR" />
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR" />
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR" />
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR" />
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR" />
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR" />
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR" />
        <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR" />
        <result column="load_time" property="loadTime" jdbcType="TIMESTAMP" />
        <result column="unload_time" property="unloadTime" jdbcType="TIMESTAMP" />
    </resultMap>
    <resultMap id="carrierOrderDetailFreightFeeInfoForManagementMap" type="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailFreightFeeInfoModel">
        <result column="dispatch_freight_fee_type" property="dispatchFreightFeeType" jdbcType="INTEGER"/>
        <result column="dispatch_freight_fee" property="dispatchFreightFee" jdbcType="DECIMAL" />
        <result column="sign_freight_fee" property="signFreightFee" jdbcType="DECIMAL" />
        <result column="adjust_fee" property="adjustFee" jdbcType="DECIMAL" />
        <result column="markup_fee" property="markupFee" jdbcType="DECIMAL" />
        <result column="entrust_freight_type" property="entrustFreightType" jdbcType="INTEGER"/>
        <result column="entrust_freight" property="entrustFreight" jdbcType="DECIMAL" />
        <result column="expect_entrust_freight_type" property="expectEntrustFreightType" jdbcType="INTEGER"/>
        <result column="expect_entrust_freight" property="expectEntrustFreight" jdbcType="DECIMAL" />
        <result column="carrier_price_type" property="carrierFreightType" jdbcType="INTEGER"/>
        <result column="carrier_price" property="carrierFreight" jdbcType="DECIMAL" />
    </resultMap>
    <select id="getCarrierOrderDetailForManagementById" resultMap="getCarrierOrderDetailForManagementByIdMap">
        select
        tco.id,
        tco.carrier_order_code,
        tco.customer_order_code,
        tco.demand_order_id,
        tco.demand_order_code,
        tco.status,
        tco.if_cancel,
        tco.if_empty,
        tco.dispatch_user_name,
        tco.dispatch_time,
        tco.load_time,
        tco.unload_time,
        tco.remark,
        tco.dispatch_freight_fee_type,
        tco.dispatch_freight_fee,
        tco.sign_freight_fee,
        tco.adjust_fee,
        tco.markup_fee,
        tco.expect_entrust_freight,
        tco.expect_entrust_freight_type,
        tco.entrust_freight_type,
        tco.entrust_freight,
        tco.carrier_price_type,
        tco.carrier_price,
        tco.goods_unit,
        tco.expect_amount,
        tco.load_amount,
        tco.unload_amount,
        tco.sign_amount,
        tco.company_carrier_id,
        tco.company_entrust_name,
        tco.settlement_tonnage,
        tco.carrier_settlement,
        tco.demand_order_entrust_type,
        tco.carrier_settle_statement_status,

        tcoa.load_province_name,
        tcoa.load_city_name,
        tcoa.load_area_name,
        tcoa.load_detail_address,
        tcoa.load_warehouse,
        tcoa.unload_province_name,
        tcoa.unload_city_name,
        tcoa.unload_area_name,
        tcoa.unload_detail_address,
        tcoa.unload_warehouse,
        tcoa.consignor_name,
        tcoa.consignor_mobile,
        tcoa.receiver_name,
        tcoa.receiver_mobile,

        tcog.id as goodsId,
        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount AS g_expect_amount,
        tcog.load_amount AS g_load_amount,
        tcog.unload_amount AS g_unload_amount,
        tcog.sign_amount AS g_sign_amount,

        tcovh.id as  vehicleHistoryId,
        tcovh.vehicle_no,
        tcovh.driver_name,
        tcovh.driver_mobile,
        tcovh.if_invalid,
        tcovh.audit_status,
        tcovh.remark as vehicleHistoryRemark,
        tcovh.reject_reason,
        tcovh.created_time as vehicleHistoryCreateTime

        from t_carrier_order tco
        LEFT JOIN t_carrier_order_goods tcog ON tco.id = tcog.carrier_order_id and tcog.valid= 1
        LEFT JOIN t_carrier_order_vehicle_history tcovh on tco.id = tcovh.carrier_order_id and tcovh.valid= 1
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        where tco.valid = 1 and tco.id = #{orderId,jdbcType=BIGINT}
    </select>
    <resultMap id="getCarrierOrderDetailForLeYiManagementByIdMap" type="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailForLeYiResponseModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="dispatch_user_name" property="dispatchUserName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="dispatch_time" property="dispatchTime" jdbcType="TIMESTAMP"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
        <result column="load_amount" property="loadAmount" jdbcType="DECIMAL"/>
        <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
        <result column="sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="company_entrust_name" property="entrustCompany" jdbcType="VARCHAR"/>
        <result column="dispatch_remark" property="dispatchRemark" jdbcType="VARCHAR"/>
        <result column="settlement_tonnage" property="entrustSettlementTonnage" jdbcType="INTEGER"/>
        <result column="carrier_settlement" property="carrierSettlementTonnage" jdbcType="INTEGER"/>
        <result column="demand_order_entrust_type" property="entrustType" jdbcType="INTEGER"/>
        <result column="carrier_settle_statement_status" property="carrierSettleStatementStatus" jdbcType="INTEGER"/>
        <result column="project_label" property="projectLabel" jdbcType="VARCHAR"/>
        <association property="carrierOrderDetailBasicInfo" javaType="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailBasicInfoForLeYiModel">
            <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR" />
            <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR" />
            <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR" />
            <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR" />
            <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR" />
            <result column="consignor_name" property="consignorName" jdbcType="VARCHAR" />
            <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR" />
            <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR" />
            <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR" />
            <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR" />
            <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR" />
            <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR" />
            <result column="receiver_name" property="receiverName" jdbcType="VARCHAR" />
            <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR" />
            <result column="load_time" property="loadTime" jdbcType="TIMESTAMP" />
            <result column="unload_time" property="unloadTime" jdbcType="TIMESTAMP" />
            <result column="unload_address_is_amend" property="unloadAddressIsAmend" jdbcType="INTEGER"/>
        </association>
        <association property="carrierOrderDetailFreightFeeInfo" javaType="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailFreightFeeInfoModel">
            <result column="dispatch_freight_fee_type" property="dispatchFreightFeeType" jdbcType="INTEGER"/>
            <result column="dispatch_freight_fee" property="dispatchFreightFee" jdbcType="DECIMAL" />
            <result column="sign_freight_fee" property="signFreightFee" jdbcType="DECIMAL" />
            <result column="adjust_fee" property="adjustFee" jdbcType="DECIMAL" />
            <result column="markup_fee" property="markupFee" jdbcType="DECIMAL" />
            <result column="entrust_freight_type" property="entrustFreightType" jdbcType="INTEGER"/>
            <result column="entrust_freight" property="entrustFreight" jdbcType="DECIMAL" />
            <result column="expect_entrust_freight_type" property="expectEntrustFreightType" jdbcType="INTEGER"/>
            <result column="expect_entrust_freight" property="expectEntrustFreight" jdbcType="DECIMAL" />
            <result column="carrier_price_type" property="carrierFreightType" jdbcType="INTEGER"/>
            <result column="carrier_price" property="carrierFreight" jdbcType="DECIMAL" />
        </association>
        <collection property="carrierOrderDetailGoodsInfo" ofType="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailGoodsInfoModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="g_expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="g_load_amount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="g_unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="g_sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        </collection>
        <collection property="carrierOrderDetailVehicleDriverInfo" ofType="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailVehicleDriverInfoModel">
            <id column="vehicleHistoryId" property="vehicleHistoryId" jdbcType="BIGINT"/>
            <result column="audit_status" property="auditStatus" jdbcType="INTEGER"/>
            <result column="if_invalid" property="ifInvalid" jdbcType="INTEGER"/>
            <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
            <result column="driver_name" property="driverName" jdbcType="VARCHAR"/>
            <result column="driver_mobile" property="driverMobile" jdbcType="VARCHAR"/>
            <result column="reject_reason" property="rejectReason" jdbcType="VARCHAR"/>
            <result column="vehicleHistoryRemark" property="remark" jdbcType="VARCHAR"/>
            <result column="vehicleHistoryCreateTime" property="createdTime" jdbcType="TIMESTAMP"/>
        </collection>
    </resultMap>
    <select id="getCarrierOrderDetailForLeYiManagementById" resultMap="getCarrierOrderDetailForLeYiManagementByIdMap">
        select
        tco.id,
        tco.carrier_order_code,
        tco.customer_order_code,
        tco.demand_order_id,
        tco.demand_order_code,
        tco.status,
        tco.if_cancel,
        tco.if_empty,
        tco.dispatch_user_name,
        tco.dispatch_time,
        tco.load_time,
        tco.unload_time,
        tco.remark,
        tco.dispatch_freight_fee_type,
        tco.dispatch_freight_fee,
        tco.sign_freight_fee,
        tco.adjust_fee,
        tco.markup_fee,
        tco.expect_entrust_freight,
        tco.expect_entrust_freight_type,
        tco.entrust_freight_type,
        tco.entrust_freight,
        tco.goods_unit,
        tco.expect_amount,
        tco.load_amount,
        tco.unload_amount,
        tco.sign_amount,
        tco.company_carrier_id,
        tco.company_entrust_name,
        tco.carrier_price_type,
        tco.carrier_price,
        tco.dispatch_remark,
        tco.demand_order_entrust_type,
        tco.settlement_tonnage,
        tco.carrier_settlement,
        tco.carrier_settle_statement_status,
        tco.project_label,

        tcoa.load_province_name,
        tcoa.load_city_name,
        tcoa.load_area_name,
        tcoa.load_detail_address,
        tcoa.load_warehouse,
        tcoa.unload_province_name,
        tcoa.unload_city_name,
        tcoa.unload_area_name,
        tcoa.unload_detail_address,
        tcoa.unload_warehouse,
        tcoa.consignor_name,
        tcoa.consignor_mobile,
        tcoa.receiver_name,
        tcoa.receiver_mobile,
        tcoa.unload_address_is_amend,

        tcog.id as goodsId,
        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount AS g_expect_amount,
        tcog.load_amount AS g_load_amount,
        tcog.unload_amount AS g_unload_amount,
        tcog.sign_amount AS g_sign_amount,

        tcovh.id as  vehicleHistoryId,
        tcovh.vehicle_no,
        tcovh.driver_name,
        tcovh.driver_mobile,
        tcovh.if_invalid,
        tcovh.audit_status,
        tcovh.remark as vehicleHistoryRemark,
        tcovh.reject_reason,
        tcovh.created_time as vehicleHistoryCreateTime
        from t_carrier_order tco
        LEFT JOIN t_carrier_order_goods tcog ON tco.id = tcog.carrier_order_id and tcog.valid= 1
        LEFT JOIN t_carrier_order_vehicle_history tcovh on tco.id = tcovh.carrier_order_id and tcovh.valid= 1
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        where tco.valid = 1 and tco.id = #{orderId,jdbcType=BIGINT}
    </select>

    <select id="selectCarrierOrdersByIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List_Decrypt"/>
        from t_carrier_order
        where valid = 1
        and id in (${ids})
    </select>


    <select id="selectYunPanCarrierOrdersByIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List_Decrypt"/>
        from t_carrier_order
        where valid = 1
        and demand_order_source  = 1
        <if test="ids!=null and ids!=''">
        and id in (${ids})
        </if>
    </select>



    <update id="batchUpdateCarrierOrders" parameterType="com.logistics.tms.entity.TCarrierOrder">
        <foreach collection="carrierOrders" separator=";" item="item">
            update t_carrier_order
            <set>
                <if test="item.dispatchOrderId != null">
                    dispatch_order_id = #{item.dispatchOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.dispatchOrderCode != null">
                    dispatch_order_code = #{item.dispatchOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.demandOrderId != null">
                    demand_order_id = #{item.demandOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.demandOrderCode != null">
                    demand_order_code = #{item.demandOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.carrierOrderCode != null">
                    carrier_order_code = #{item.carrierOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.customerOrderCode != null">
                    customer_order_code = #{item.customerOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.status != null">
                    status = #{item.status,jdbcType=INTEGER},
                </if>
                <if test="item.statusUpdateTime != null">
                    status_update_time = #{item.statusUpdateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.ifCancel != null">
                    if_cancel = #{item.ifCancel,jdbcType=INTEGER},
                </if>
                <if test="item.cancelReason != null">
                    cancel_reason = #{item.cancelReason,jdbcType=VARCHAR},
                </if>
                <if test="item.cancelOperatorName != null">
                    cancel_operator_name = #{item.cancelOperatorName,jdbcType=VARCHAR},
                </if>
                <if test="item.cancelTime != null">
                    cancel_time = #{item.cancelTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.businessType != null">
                    business_type = #{item.businessType,jdbcType=INTEGER},
                </if>
                <if test="item.customerName != null">
                    customer_name = #{item.customerName,jdbcType=VARCHAR},
                </if>
                <if test="item.customerUserName != null">
                    customer_user_name = #{item.customerUserName,jdbcType=VARCHAR},
                </if>
                <if test="item.customerUserMobile != null">
                    customer_user_mobile = HEX(AES_ENCRYPT(#{item.customerUserMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
                </if>
                <if test="item.customerOrderSource != null">
                    customer_order_source = #{item.customerOrderSource,jdbcType=INTEGER},
                </if>
                <if test="item.source != null">
                    source = #{item.source,jdbcType=INTEGER},
                </if>
                <if test="item.dispatchUserId != null">
                    dispatch_user_id = #{item.dispatchUserId,jdbcType=BIGINT},
                </if>
                <if test="item.dispatchUserName != null">
                    dispatch_user_name = #{item.dispatchUserName,jdbcType=VARCHAR},
                </if>
                <if test="item.dispatchTime != null">
                    dispatch_time = #{item.dispatchTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.companyCarrierType != null">
                    company_carrier_type = #{item.companyCarrierType,jdbcType=INTEGER},
                </if>
                <if test="item.companyCarrierId != null">
                    company_carrier_id = #{item.companyCarrierId,jdbcType=BIGINT},
                </if>
                <if test="item.companyCarrierName != null">
                    company_carrier_name = #{item.companyCarrierName,jdbcType=VARCHAR},
                </if>
                <if test="item.carrierContactId != null">
                    carrier_contact_id = #{item.carrierContactId,jdbcType=BIGINT},
                </if>
                <if test="item.carrierContactName != null">
                    carrier_contact_name = #{item.carrierContactName,jdbcType=VARCHAR},
                </if>
                <if test="item.carrierContactPhone != null">
                    carrier_contact_phone = HEX(AES_ENCRYPT(#{item.carrierContactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
                </if>
                <if test="item.companyCarrierLevel != null">
                    company_carrier_level = #{item.companyCarrierLevel,jdbcType=INTEGER},
                </if>
                <if test="item.companyEntrustId != null">
                    company_entrust_id = #{item.companyEntrustId,jdbcType=BIGINT},
                </if>
                <if test="item.companyEntrustName != null">
                    company_entrust_name = #{item.companyEntrustName,jdbcType=VARCHAR},
                </if>
                <if test="item.upstreamCustomer != null">
                    upstream_customer = #{item.upstreamCustomer,jdbcType=VARCHAR},
                </if>
                <if test="item.loadTime != null">
                    load_time = #{item.loadTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.unloadTime != null">
                    unload_time = #{item.unloadTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.signTime != null">
                    sign_time = #{item.signTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.expectAmount != null">
                    expect_amount = #{item.expectAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.loadAmountExpect != null">
                    load_amount_expect = #{item.loadAmountExpect,jdbcType=DECIMAL},
                </if>
                <if test="item.loadAmount != null">
                    load_amount = #{item.loadAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.unloadAmountExpect != null">
                    unload_amount_expect = #{item.unloadAmountExpect,jdbcType=DECIMAL},
                </if>
                <if test="item.unloadAmount != null">
                    unload_amount = #{item.unloadAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.signAmount != null">
                    sign_amount = #{item.signAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.expectEntrustFreight != null">
                    expect_entrust_freight = #{item.expectEntrustFreight,jdbcType=DECIMAL},
                </if>
                <if test="item.expectEntrustFreightType != null">
                    expect_entrust_freight_type = #{item.expectEntrustFreightType,jdbcType=INTEGER},
                </if>
                <if test="item.entrustFreightType != null">
                    entrust_freight_type = #{item.entrustFreightType,jdbcType=INTEGER},
                </if>
                <if test="item.entrustFreight != null">
                    entrust_freight = #{item.entrustFreight,jdbcType=DECIMAL},
                </if>
                <if test="item.signFreightFee != null">
                    sign_freight_fee = #{item.signFreightFee,jdbcType=DECIMAL},
                </if>
                <if test="item.dispatchFreightFeeType != null">
                    dispatch_freight_fee_type = #{item.dispatchFreightFeeType,jdbcType=INTEGER},
                </if>
                <if test="item.dispatchFreightFee != null">
                    dispatch_freight_fee = #{item.dispatchFreightFee,jdbcType=DECIMAL},
                </if>
                <if test="item.adjustFee != null">
                    adjust_fee = #{item.adjustFee,jdbcType=DECIMAL},
                </if>
                <if test="item.markupFee != null">
                    markup_fee = #{item.markupFee,jdbcType=DECIMAL},
                </if>
                <if test="item.goodsUnit != null">
                    goods_unit = #{item.goodsUnit,jdbcType=INTEGER},
                </if>
                <if test="item.demandOrderSource != null">
                    demand_order_source = #{item.demandOrderSource,jdbcType=INTEGER},
                </if>
                <if test="item.demandOrderEntrustType != null">
                    demand_order_entrust_type = #{item.demandOrderEntrustType,jdbcType=INTEGER},
                </if>
                <if test="item.settlementTonnage != null">
                    settlement_tonnage = #{item.settlementTonnage,jdbcType=INTEGER},
                </if>
                <if test="item.carrierSettlement != null">
                    carrier_settlement = #{item.carrierSettlement,jdbcType=INTEGER},
                </if>
                <if test="item.expectMileage != null">
                    expect_mileage = #{item.expectMileage,jdbcType=DECIMAL},
                </if>
                <if test="item.configDistance != null">
                    config_distance = #{item.configDistance,jdbcType=DECIMAL},
                </if>
                <if test="item.loadValidity != null">
                    load_validity = #{item.loadValidity,jdbcType=INTEGER},
                </if>
                <if test="item.abnormalAmount != null">
                    abnormal_amount = #{item.abnormalAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.ifEmpty != null">
                    if_empty = #{item.ifEmpty,jdbcType=INTEGER},
                </if>
                <if test="item.emptyTime != null">
                    empty_time = #{item.emptyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.dispatchRemark != null">
                    dispatch_remark = #{item.dispatchRemark,jdbcType=VARCHAR},
                </if>
                <if test="item.carrierPriceType != null">
                    carrier_price_type = #{item.carrierPriceType,jdbcType=INTEGER},
                </if>
                <if test="item.carrierPrice != null">
                    carrier_price = #{item.carrierPrice,jdbcType=DECIMAL},
                </if>
                <if test="item.printCount != null">
                    print_count = #{item.printCount,jdbcType=INTEGER},
                </if>
                <if test="item.ifObjection != null">
                    if_objection = #{item.ifObjection,jdbcType=INTEGER},
                </if>
                <if test="item.outStatus != null">
                    out_status = #{item.outStatus,jdbcType=INTEGER},
                </if>
                <if test="item.publishName != null">
                    publish_name = #{item.publishName,jdbcType=VARCHAR},
                </if>
                <if test="item.publishMobile != null">
                    publish_mobile = HEX(AES_ENCRYPT(#{item.publishMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
                </if>
                <if test="item.publishTime != null">
                    publish_time = #{item.publishTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.publishOrgCode != null">
                    publish_org_code = #{item.publishOrgCode,jdbcType=VARCHAR},
                </if>
                <if test="item.publishOrgName != null">
                    publish_org_name = #{item.publishOrgName,jdbcType=VARCHAR},
                </if>
                <if test="item.ifUrgent != null">
                    if_urgent = #{item.ifUrgent,jdbcType=INTEGER},
                </if>
                <if test="item.availableOnWeekends != null">
                    available_on_weekends = #{item.availableOnWeekends,jdbcType=INTEGER},
                </if>
                <if test="item.loadingUnloadingPart != null">
                    loading_unloading_part = #{item.loadingUnloadingPart,jdbcType=INTEGER},
                </if>
                <if test="item.loadingUnloadingCharge != null">
                    loading_unloading_charge = #{item.loadingUnloadingCharge,jdbcType=DECIMAL},
                </if>
                <if test="item.recycleTaskType != null">
                    recycle_task_type = #{item.recycleTaskType,jdbcType=INTEGER},
                </if>
                <if test="item.projectLabel != null">
                    project_label = #{item.projectLabel,jdbcType=VARCHAR},
                </if>
                <if test="item.ifWaitAuditVehicle != null">
                    if_wait_audit_vehicle = #{item.ifWaitAuditVehicle,jdbcType=INTEGER},
                </if>
                <if test="item.correctStatus != null">
                    correct_status = #{item.correctStatus,jdbcType=INTEGER},
                </if>
                <if test="item.stockInState != null">
                    stock_in_state = #{item.stockInState,jdbcType=INTEGER},
                </if>
                <if test="item.carrierSettleStatementStatus != null">
                    carrier_settle_statement_status = #{item.carrierSettleStatementStatus,jdbcType=INTEGER},
                </if>
                <if test="item.statementOtherFeeTaxPoint != null">
                    statement_other_fee_tax_point = #{item.statementOtherFeeTaxPoint,jdbcType=DECIMAL},
                </if>
                <if test="item.statementFreightTaxPoint != null">
                    statement_freight_tax_point = #{item.statementFreightTaxPoint,jdbcType=DECIMAL},
                </if>
                <if test="item.qrCodePicPath != null">
                    qr_code_pic_path = #{item.qrCodePicPath,jdbcType=VARCHAR},
                </if>
                <if test="item.deliveryMethod != null">
                    delivery_method = #{item.deliveryMethod,jdbcType=INTEGER},
                </if>
                <if test="item.orderMode != null">
                    order_mode = #{item.orderMode,jdbcType=INTEGER},
                </if>
                <if test="item.vehicleLengthId != null">
                    vehicle_length_id = #{item.vehicleLengthId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleLength != null">
                    vehicle_length = #{item.vehicleLength,jdbcType=DECIMAL},
                </if>
                <if test="item.bargainingMode != null">
                    bargaining_mode = #{item.bargainingMode,jdbcType=INTEGER},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <resultMap id="selectCarrierOrderSignDetailMap" type="com.logistics.tms.controller.carrierorder.response.CarrierOrderListBeforeSignUpResponseModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT" />
        <result column="demandOrderId" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="dispatch_order_id" property="dispatchOrderId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR" />
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR" />
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER" />
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER" />
        <result column="entrust_freight_type" property="entrustFreightType" jdbcType="INTEGER" />
        <result column="entrust_freight" property="entrustFreight" jdbcType="DECIMAL" />
        <result column="sign_freight_fee" property="signFreightFee" jdbcType="DECIMAL" />
        <result column="expectAmount" property="expectAmount" jdbcType="INTEGER" />
        <result column="loadAmount" property="loadAmount" jdbcType="INTEGER" />
        <result column="unloadAmount" property="unloadAmount" jdbcType="INTEGER" />
        <result column="signAmount" property="signAmount" jdbcType="VARCHAR"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="dispatch_freight_fee_type" property="dispatchFreightFeeType" jdbcType="INTEGER"/>
        <result column="dispatch_freight_fee" property="dispatchFreightFee" jdbcType="DECIMAL"/>
        <result column="adjust_fee" property="adjustFee" jdbcType="DECIMAL"/>
        <result column="markup_fee" property="markupFee" jdbcType="DECIMAL"/>
        <result column="settlement_tonnage" property="settlementTonnage" jdbcType="INTEGER"/>
        <result column="demand_order_entrust_type" property="entrustType" jdbcType="INTEGER"/>
        <result column="demand_order_source" property="source" jdbcType="INTEGER"/>
        <result column="out_status" property="outStatus" jdbcType="INTEGER"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="expect_mileage" property="expectMileage" jdbcType="DECIMAL"/>
        <result column="project_label" property="projectLabel" jdbcType="VARCHAR"/>
        <result column="bargaining_mode" property="bargainingMode" jdbcType="INTEGER"/>
        <result column="if_recycle_by_code" property="ifRecycleByCode" jdbcType="INTEGER"/>
        <collection property="goodsInfo" ofType="com.logistics.tms.controller.carrierorder.response.CarrierOrderListBeforeSignUpGoodsModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="demand_order_goods_id" property="demandOrderGoodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL" />
            <result column="load_amount" property="loadAmount" jdbcType="DECIMAL" />
            <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL" />
            <result column="sign_amount" property="signAmount" jdbcType="DECIMAL" />
        </collection>
    </resultMap>

    <select id="selectCarrierOrderSignDetail" resultMap="selectCarrierOrderSignDetailMap">
        select
        tco.id as id,
        tco.demand_order_id as demandOrderId,
        tco.dispatch_order_id,
        tco.carrier_order_code,
        tco.demand_order_code,
        tco.customer_order_code,
        tco.status,
        tco.if_cancel,
        tco.if_empty,
        tco.entrust_freight_type,
        tco.entrust_freight,
        tco.sign_freight_fee,
        tco.expect_amount as expectAmount,
        tco.load_amount as loadAmount,
        tco.unload_amount as unloadAmount,
        tco.sign_amount as signAmount,
        tco.goods_unit,
        tco.dispatch_freight_fee_type,
        tco.dispatch_freight_fee,
        tco.adjust_fee,
        tco.markup_fee,
        tco.out_status,
        tco.company_carrier_id,
        tco.settlement_tonnage,
        tco.demand_order_entrust_type,
        tco.demand_order_source,
        tco.expect_mileage,
        tco.project_label,
        tco.bargaining_mode,
        tco.if_recycle_by_code,

        tcog.id as goodsId,
        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount,
        tcog.load_amount,
        tcog.unload_amount,
        tcog.sign_amount,
        tcog.demand_order_goods_id

        from t_carrier_order tco
        left join t_carrier_order_goods tcog on tcog.valid = 1 and tcog.carrier_order_id = tco.id
        where tco.valid = 1
        <if test="carrierOrdersIds != null and carrierOrdersIds != ''">
            and tco.id in (${carrierOrdersIds})
        </if>
        <if test="demandOrderIds != null and demandOrderIds != ''">
            and tco.demand_order_id in (${demandOrderIds})
        </if>
        order by tco.created_time desc,tco.id desc
    </select>

    <resultMap id="downloadLadingBillMap"
               type="com.logistics.tms.controller.carrierorder.response.DownloadLadingBillResponseModel">
        <result column="carrierOrdrId" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
        <result column="load_amount" property="loadAmount" jdbcType="DECIMAL"/>
        <result column="sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        <result column="load_time" property="loadTime" jdbcType="TIMESTAMP"/>
        <result column="dispatch_time" property="dispatchTime" jdbcType="TIMESTAMP"/>
        <result column="load_company" property="loadCompany" jdbcType="VARCHAR"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="unload_company" property="unloadCompany" jdbcType="VARCHAR"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="consignor_name" property="consignorName" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR"/>
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR"/>
        <result column="expected_load_time" property="expectedLoadTime" jdbcType="TIMESTAMP"/>
        <result column="expected_unload_time" property="expectedUnloadTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="dispatch_remark" property="dispatchRemark" jdbcType="VARCHAR"/>
        <result column="company_entrust_name" property="companyEntrustName" jdbcType="VARCHAR"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="upstream_customer" property="upstreamCustomer" jdbcType="VARCHAR"/>
        <result column="company_carrier_name" property="companyCarrierName" jdbcType="VARCHAR"/>

        <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="driver_name" property="driverName" jdbcType="VARCHAR"/>
        <result column="driver_mobile" property="driverMobile" jdbcType="VARCHAR"/>
        <result column="driver_identity" property="driverIdentityNumber" jdbcType="VARCHAR"/>
        <result column="recycle_expect_load_time" property="recycleExpectedLoadTime" jdbcType="VARCHAR"/>

        <result column="demand_order_source" property="demandOrderSource" jdbcType="INTEGER"/>
        <result column="demand_order_entrust_type" property="entrustType" jdbcType="INTEGER"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="qr_code_pic_path" property="qrCodePicPath" jdbcType="VARCHAR"/>

        <result column="available_on_weekends" property="availableOnWeekends" jdbcType="INTEGER"/>
        <result column="loading_unloading_part" property="loadingUnloadingPart" jdbcType="INTEGER"/>
        <result column="publish_name" property="publishName" jdbcType="VARCHAR"/>
        <result column="publish_mobile" property="publishMobile" jdbcType="VARCHAR"/>

        <collection property="goodsInfoList"
                    ofType="com.logistics.tms.controller.carrierorder.response.DownloadLadingBillGoodsResponseModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="category_name" property="categoryName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="goodExpectAmount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="goodLoadAmount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="goodUnloadAmount" property="unloadAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>

    <select id="downloadLadingBill" resultMap="downloadLadingBillMap">
        select
        tco.id                                                                                                                  as carrierOrdrId,
        tco.carrier_order_code,
        tco.status,
        tco.expect_amount,
        tco.load_amount,
        tco.sign_amount,
        tco.goods_unit,
        tco.load_time,
        tco.dispatch_time,
        tco.qr_code_pic_path,
        tco.remark,
        tco.dispatch_remark,
        tco.demand_order_code,
        tco.demand_order_source,
        tco.demand_order_entrust_type,
        tco.publish_name,
        AES_DECRYPT(UNHEX(tco.publish_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as publish_mobile,
        tco.available_on_weekends,
        tco.loading_unloading_part,
        tco.company_entrust_name,
        tco.if_cancel,
        tco.if_empty,
        tco.upstream_customer,
        tco.company_carrier_name,


        tcovh.vehicle_no,
        tcovh.driver_name,
        tcovh.driver_mobile,
        tcovh.driver_identity,
        tcovh.expect_load_time                                                                                                     recycle_expect_load_time,

        tcoa.load_company,
        tcoa.load_province_name,
        tcoa.load_city_name,
        tcoa.load_area_name,
        tcoa.load_detail_address,
        tcoa.load_warehouse,
        tcoa.expected_load_time,
        tcoa.unload_company,
        tcoa.unload_province_name,
        tcoa.unload_city_name,
        tcoa.unload_area_name,
        tcoa.unload_detail_address,
        tcoa.unload_warehouse,
        tcoa.expected_unload_time,
        tcoa.consignor_name,
        tcoa.consignor_mobile,
        tcoa.receiver_name,
        tcoa.receiver_mobile,

        tcog.id                                                                                                                 as goodsId,
        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount                                                                                                      as goodExpectAmount,
        tcog.load_amount                                                                                                        as goodLoadAmount,
        tcog.unload_amount                                                                                                      as goodUnloadAmount,
        tcog.category_name

        from t_carrier_order tco
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        LEFT JOIN t_carrier_order_vehicle_history tcovh on tcovh.valid = 1 and tcovh.carrier_order_id = tco.id and tcovh.if_invalid = 1
        LEFT JOIN t_carrier_order_goods tcog on tcog.valid = 1 and tcog.carrier_order_id = tco.id
        where tco.valid = 1
          and tco.id = #{carrierOrderId,jdbcType=BIGINT}
    </select>

    <resultMap id="downloadLadingBillByCarrierCodeMap"
               type="com.logistics.tms.controller.carrierorder.response.DownloadLadingBillByCarrierCodeResponseModel">
        <result column="carrierOrdrId" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
        <result column="load_amount" property="loadAmount" jdbcType="DECIMAL"/>
        <result column="sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        <result column="load_time" property="loadTime" jdbcType="TIMESTAMP"/>
        <result column="dispatch_time" property="dispatchTime" jdbcType="TIMESTAMP"/>
        <result column="load_company" property="loadCompany" jdbcType="VARCHAR"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="unload_company" property="unloadCompany" jdbcType="VARCHAR"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="consignor_name" property="consignorName" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR"/>
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR"/>
        <result column="expected_load_time" property="expectedLoadTime" jdbcType="TIMESTAMP"/>
        <result column="expected_unload_time" property="expectedUnloadTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="dispatch_remark" property="dispatchRemark" jdbcType="VARCHAR"/>
        <result column="company_entrust_name" property="companyEntrustName" jdbcType="VARCHAR"/>

        <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="driver_name" property="driverName" jdbcType="VARCHAR"/>
        <result column="driver_mobile" property="driverMobile" jdbcType="VARCHAR"/>
        <result column="driver_identity" property="driverIdentityNumber" jdbcType="VARCHAR"/>
        <result column="recycle_expect_load_time" property="recycleExpectedLoadTime" jdbcType="VARCHAR"/>

        <result column="demand_order_source" property="demandOrderSource" jdbcType="INTEGER"/>
        <result column="demand_order_entrust_type" property="entrustType" jdbcType="INTEGER"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="qr_code_pic_path" property="qrCodePicPath" jdbcType="VARCHAR"/>

        <result column="available_on_weekends" property="availableOnWeekends" jdbcType="INTEGER"/>
        <result column="loading_unloading_part" property="loadingUnloadingPart" jdbcType="INTEGER"/>
        <result column="publish_name" property="publishName" jdbcType="VARCHAR"/>
        <result column="publish_mobile" property="publishMobile" jdbcType="VARCHAR"/>

        <collection property="goodsInfoList"
                    ofType="com.logistics.tms.controller.carrierorder.response.DownloadLadingBillGoodsByCarrierCodeResponseModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="category_name" property="categoryName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="goodExpectAmount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="goodLoadAmount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="goodUnloadAmount" property="unloadAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>


    <select id="downloadLadingBillByCarrierCode" resultMap="downloadLadingBillByCarrierCodeMap">
        select
        tco.id                                                                                                                  as carrierOrdrId,
        tco.carrier_order_code,
        tco.status,
        tco.expect_amount,
        tco.load_amount,
        tco.sign_amount,
        tco.goods_unit,
        tco.load_time,
        tco.dispatch_time,
        tco.qr_code_pic_path,
        tco.remark,
        tco.dispatch_remark,
        tco.demand_order_code,
        tco.demand_order_source,
        tco.demand_order_entrust_type,
        tco.publish_name,
        AES_DECRYPT(UNHEX(tco.publish_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as publish_mobile,
        tco.available_on_weekends,
        tco.loading_unloading_part,
        tco.company_entrust_name,

        tcovh.vehicle_no,
        tcovh.driver_name,
        tcovh.driver_mobile,
        tcovh.driver_identity,
        tcovh.expect_load_time                                                                                                     recycle_expect_load_time,

        tcoa.load_company,
        tcoa.load_province_name,
        tcoa.load_city_name,
        tcoa.load_area_name,
        tcoa.load_detail_address,
        tcoa.load_warehouse,
        tcoa.expected_load_time,
        tcoa.unload_company,
        tcoa.unload_province_name,
        tcoa.unload_city_name,
        tcoa.unload_area_name,
        tcoa.unload_detail_address,
        tcoa.unload_warehouse,
        tcoa.expected_unload_time,
        tcoa.consignor_name,
        tcoa.consignor_mobile,
        tcoa.receiver_name,
        tcoa.receiver_mobile,

        tcog.id                                                                                                                 as goodsId,
        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount                                                                                                      as goodExpectAmount,
        tcog.load_amount                                                                                                        as goodLoadAmount,
        tcog.unload_amount                                                                                                      as goodUnloadAmount,
        tcog.category_name

        from t_carrier_order tco
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        LEFT JOIN t_carrier_order_vehicle_history tcovh on tcovh.valid = 1 and tcovh.carrier_order_id = tco.id and tcovh.if_invalid = 1
        LEFT JOIN t_carrier_order_goods tcog on tcog.valid = 1 and tcog.carrier_order_id = tco.id
        where tco.valid = 1
          and tco.carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR}
    </select>


    <resultMap id="getCarrierOrderByDemandOrderIdsMap" type="com.logistics.tms.controller.carrierorder.response.GetCarrierOrderResponseModel">
        <id column="carrier_order_id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="entrust_freight_type" property="entrustFreightType" jdbcType="INTEGER"/>
        <result column="entrust_freight" property="entrustFreight" jdbcType="DECIMAL"/>
        <result column="sign_freight_fee" property="signFreightFee" jdbcType="DECIMAL"/>
        <result column="carrier_price_type" property="carrierPriceType" jdbcType="INTEGER"/>
        <result column="carrier_price" property="carrierPrice" jdbcType="DECIMAL"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="texpect_amount" property="expectAmount" jdbcType="DECIMAL"/>
        <result column="tload_amount" property="loadAmount" jdbcType="DECIMAL"/>
        <result column="tunload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
        <result column="tsign_amount" property="signAmount" jdbcType="DECIMAL"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="company_entrust_id" property="companyEntrustId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="load_time" property="loadTime" jdbcType="TIMESTAMP"/>
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="settlement_tonnage" property="settlementTonnage" jdbcType="INTEGER"/>
        <result column="carrier_settlement" property="carrierSettlement" jdbcType="INTEGER"/>
        <result column="demand_order_source" property="demandOrderSource" jdbcType="INTEGER"/>
        <collection property="goodsList" ofType="com.logistics.tms.controller.carrierorder.response.GetCarrierOrderGoodsResponseModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="load_amount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>
    <select id="getCarrierOrderByDemandOrderIds" resultMap="getCarrierOrderByDemandOrderIdsMap">
        SELECT
        tco.demand_order_id,
        tco.id as carrier_order_id,
        tco.carrier_order_code,
        tco.entrust_freight_type,
        tco.entrust_freight,
        tco.sign_freight_fee,
        tco.carrier_price_type,
        tco.carrier_price,
        tco.goods_unit,
        tco.status,
        tco.load_time,
        tco.expect_amount texpect_amount,
        tco.load_amount tload_amount,
        tco.sign_amount tsign_amount,
        tco.unload_amount tunload_amount,
        tco.company_entrust_id,
        tco.company_carrier_id,
        tco.if_empty,
        tco.settlement_tonnage,
        tco.carrier_settlement,
        tco.demand_order_source,

        goods.id as goodsId,
        goods.length,
        goods.width,
        goods.height,
        goods.expect_amount,
        goods.load_amount,
        goods.sign_amount,
        goods.unload_amount
        from t_carrier_order tco
        left join t_carrier_order_goods goods ON goods.carrier_order_id = tco.id and goods.valid = 1
        where tco.demand_order_id in (${demandOrderIds})
        and tco.valid = 1
        and tco.if_cancel = 0
        <if test="ifEmpty != null">
            and tco.if_empty = #{ifEmpty,jdbcType=INTEGER}
        </if>
    </select>

    <resultMap id="getByDemandOrderIds_Map" type="com.logistics.tms.controller.carrierorder.response.DemandCarrierOrderRecursiveModel">
        <id column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <collection property="carrierOrderList" ofType="com.logistics.tms.controller.carrierorder.response.DemandCarrierOrderModel">
            <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
            <result column="status" property="status" jdbcType="INTEGER"/>
            <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
            <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        </collection>
    </resultMap>
    <select id="getNotCancelByDemandOrderIds" resultMap="getByDemandOrderIds_Map">
        select
        id,
        demand_order_id,
        status,
        if_cancel,
        if_empty
        from t_carrier_order
        where valid = 1
        and demand_order_id in (${demandOrderIds})
        and if_cancel = 0
        and if_empty=0
    </select>

    <select id="getByDemandOrderIds" resultMap="getByDemandOrderIds_Map">
        select
        id,
        demand_order_id,
        status,
        if_cancel,
        if_empty
        from t_carrier_order
        where valid = 1
        and demand_order_id in (${demandOrderIds})
    </select>

    <select id="getNotCancelByDemandOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_carrier_order
        where valid = 1
        and demand_order_id = #{demandOrderId,jdbcType=BIGINT}
        and if_cancel = 0
        and if_empty = 0
    </select>

    <select id="getNotCancelEmptyByDemandOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_carrier_order
        where valid = 1
        and demand_order_id = #{demandOrderId,jdbcType=BIGINT}
        and if_cancel = 0
    </select>

    <select id="getCarrierStatus" resultType="com.logistics.tms.controller.carrierorder.response.CarrierOrderStatusModel">
        select
        tco.id as carrierOrderId,
        tco.demand_order_id as demandOrderId,
        tco.status,
        tco.if_cancel as ifCancel,
        tco.if_empty as ifEmpty,
        tco.goods_unit as goodsUnit,
        tco.dispatch_freight_fee_type as dispatchFreightFeeType,
        tco.dispatch_freight_fee as dispatchFreightFee,
        tco.company_carrier_id as companyCarrierId
        from t_carrier_order tco
        where tco.valid = 1
        and tco.id = #{carrierOrderId,jdbcType=BIGINT}
    </select>

    <select id="findOrderListByHistoryVehicleNo" resultType="java.lang.Long">
        select distinct
        tco.id
        from
        t_carrier_order_vehicle_history tcovh
        left join t_carrier_order tco on tcovh.carrier_order_id = tco.id
        and tco.valid = 1
        and tco.if_cancel = 0
        and tco.if_empty = 0
        and tco.status &lt; 60000
        where tco.id is not null
        and tcovh.valid = 1
        and tcovh.if_invalid = 1
        and tcovh.vehicle_no = #{vehicleNo,jdbcType = VARCHAR}
        and tco.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
    </select>

    <resultMap id="getCarrierOrderByVehicleId_Map" type="com.logistics.tms.controller.vehiclesettlement.response.GetCarrierOrderByVehicleIdResponseModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="dispatch_freight_fee_type" property="dispatchFreightFeeType" jdbcType="INTEGER"/>
        <result column="dispatch_freight_fee" property="dispatchFreightFee" jdbcType="DECIMAL"/>
        <result column="adjust_fee" property="adjustFee" jdbcType="DECIMAL"/>
        <result column="markup_fee" property="markupFee" jdbcType="DECIMAL"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="demand_order_source" property="demandOrderSource" jdbcType="INTEGER"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="driver_name" property="driverName" jdbcType="VARCHAR"/>
        <result column="driver_mobile" property="driverMobile" jdbcType="VARCHAR"/>
        <result column="dispatch_time" property="dispatchTime" jdbcType="TIMESTAMP"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <collection property="ticketList" ofType="java.lang.String">
            <result column="image_path"/>
        </collection>
    </resultMap>
    <select id="getCarrierOrderByIdsForSettlement" resultMap="getCarrierOrderByVehicleId_Map">
        select
        tco.id,
        tco.carrier_order_code,
        tco.customer_order_code,
        tco.status,
        tco.unload_amount,
        tco.dispatch_time,
        tco.dispatch_freight_fee_type,
        tco.dispatch_freight_fee,
        tco.adjust_fee,
        tco.markup_fee,
        tco.goods_unit,
        tco.remark,
        tco.demand_order_source,
        tco.company_carrier_id,

        tcoa.load_province_name,
        tcoa.load_city_name,
        tcoa.load_area_name,
        tcoa.load_detail_address,
        tcoa.load_warehouse,
        tcoa.unload_province_name,
        tcoa.unload_city_name,
        tcoa.unload_area_name,
        tcoa.unload_detail_address,
        tcoa.unload_warehouse,

        tcovh.vehicle_no,
        tcovh.driver_name,
        tcovh.driver_mobile,

        tcot.image_path
        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tcoa.carrier_order_id = tco.id and tcoa.valid = 1
        left join t_carrier_order_vehicle_history tcovh on tcovh.carrier_order_id = tco.id and tcovh.valid = 1 and tcovh.if_invalid = 1
        left join t_carrier_order_tickets tcot on tcot.carrier_order_id=tco.id and tcot.valid=1 and tcot.image_type = 3
        where tco.valid = 1 and tco.if_cancel = 0
        and tco.id in (${ids})
        order by tco.unload_time desc,tco.id desc
    </select>

    <select id="getDriverReconciliationCarrierOrder" resultType="com.logistics.tms.controller.vehiclesettlement.response.ReconciliationCarrierOrderListResponseModel">
        select
        tco.id as carrierOrderId,
        tco.carrier_order_code as carrierOrderCode,
        tco.customer_order_code as customerOrderCode,
        tco.unload_amount as unloadAmount,
        tco.dispatch_freight_fee_type as dispatchFreightFeeType,
        tco.dispatch_freight_fee as dispatchFreightFee,
        tco.adjust_fee as adjustFee,
        tco.markup_fee as markupFee,
        tco.goods_unit as goodsUnit,

        tcoa.load_province_name as loadProvinceName,
        tcoa.load_city_name as loadCityName,
        tcoa.load_area_name as loadAreaName,
        tcoa.load_detail_address as loadDetailAddress,
        tcoa.load_warehouse as loadWarehouse,
        tcoa.unload_province_name as unloadProvinceName,
        tcoa.unload_city_name as unloadCityName,
        tcoa.unload_area_name as unloadAreaName,
        tcoa.unload_detail_address as unloadDetailAddress,
        tcoa.unload_warehouse as unloadWarehouse
        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tcoa.carrier_order_id = tco.id and tcoa.valid = 1
        where tco.valid = 1 and tco.if_cancel = 0
        and tco.id in (${ids})
        order by tco.unload_time desc,tco.id desc
    </select>

    <select id="getVehicleBySettlementMonth" resultType="com.logistics.tms.controller.vehiclesettlement.response.GetVehicleBySettlementMonthModel">
        select
        tcovh.vehicle_id as vehicleId,
        tco.id           as objectId
        from t_carrier_order tco
        left join t_carrier_order_vehicle_history tcovh on tcovh.carrier_order_id = tco.id and tcovh.valid = 1 and tcovh.if_invalid = 1
        left join t_vehicle_basic tvb on tvb.id = tcovh.vehicle_id and tvb.valid = 1
        where tco.valid = 1
        and tco.if_cancel = 0
        and tvb.vehicle_property in (1, 3)
        and tco.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        and ((tco.demand_order_source in (1, 5) and date_format(tco.sign_time, '%Y-%m') = #{settlementMonth,jdbcType=VARCHAR})
            or (tco.demand_order_source in (2, 3, 4) and date_format(tco.dispatch_time, '%Y-%m') = #{settlementMonth,jdbcType=VARCHAR})
            or date_format(tco.empty_time, '%Y-%m') = #{settlementMonth,jdbcType=VARCHAR})
    </select>

    <!-- 司机运费 -->
    <select id="searchDriverFreightOrdersIds" resultType="java.lang.Long">
        select
        DISTINCT tco.id
        from t_carrier_order tco
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        LEFT JOIN t_carrier_order_vehicle_history tcovh on tcovh.valid = 1 and tcovh.carrier_order_id = tco.id and tcovh.if_invalid = 1
        LEFT JOIN t_vehicle_basic tv on tv.id = tcovh.vehicle_id and tv.valid = 1
        LEFT JOIN t_carrier_order_goods tcog on tcog.valid = 1 and tcog.carrier_order_id = tco.id
        where tco.valid= 1 and tco.if_cancel = 0
        <choose>
            <when test="condition.status == 50000">
                and tco.status = 50000
            </when>
            <when test="condition.status == 60000">
                and tco.status = 60000
            </when>
            <when test="condition.status == 2">
                and tco.if_empty = 1
            </when>
            <otherwise>
                and (tco.status in (50000,60000) or tco.if_empty = 1)
            </otherwise>
        </choose>
        <if test="condition.carrierOrderCode !=null and condition.carrierOrderCode != ''">
            and (INSTR(tco.carrier_order_code,#{condition.carrierOrderCode,jdbcType=VARCHAR})>0
            or INSTR(REPLACE(tco.carrier_order_code,'-',''),#{condition.carrierOrderCode,jdbcType=VARCHAR})>0)
        </if>
        <if test="condition.demandOrderCode !=null and condition.demandOrderCode != ''">
            and INSTR(tco.demand_order_code,#{condition.demandOrderCode,jdbcType=VARCHAR})>0
        </if>
        <if test="condition.loadAddress!=null and condition.loadAddress!=''">
            and instr(concat(tcoa.load_province_name,tcoa.load_city_name,tcoa.load_area_name,tcoa.load_detail_address,tcoa.load_warehouse),#{condition.loadAddress,jdbcType=VARCHAR})
        </if>
        <if test="condition.unloadAddress!=null and condition.unloadAddress!=''">
            and instr(concat(tcoa.unload_province_name,tcoa.unload_city_name,tcoa.unload_area_name,tcoa.unload_detail_address,tcoa.unload_warehouse),#{condition.unloadAddress,jdbcType=VARCHAR})
        </if>
        <if test="condition.vehicleNo!=null and condition.vehicleNo!=''">
            and instr(tcovh.vehicle_no,#{condition.vehicleNo,jdbcType=VARCHAR})>0
        </if>
        <if test="condition.vehicleProperty!=null and condition.vehicleProperty!=''">
            and tv.vehicle_property = #{condition.vehicleProperty,jdbcType=INTEGER}
        </if>
        <if test="condition.driver!=null and condition.driver!=''">
            and instr(concat(tcovh.driver_name,tcovh.driver_mobile),#{condition.driver,jdbcType=VARCHAR})
        </if>
        <if test="condition.dispatchUser!=null and condition.dispatchUser!=''">
            and instr(tco.dispatch_user_name,#{condition.dispatchUser,jdbcType=VARCHAR})>0
        </if>
        <if test="condition.goodsName!=null and condition.goodsName!=''">
            and instr(tcog.goods_name,#{condition.goodsName,jdbcType=VARCHAR})>0
        </if>
        <if test="condition.goodsSize!=null and condition.goodsSize!=''">
            and (instr(concat(tcog.length,'*',tcog.width,'*',tcog.height),#{condition.goodsSize,jdbcType=VARCHAR})>0
            or instr(tcog.goods_size,#{condition.goodsSize,jdbcType=VARCHAR})>0)
        </if>
        <if test="condition.dispatchTimeFrom!=null and condition.dispatchTimeFrom!=''">
            and tco.dispatch_time &gt;= DATE_FORMAT(#{condition.dispatchTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="condition.dispatchTimeTo!=null and condition.dispatchTimeTo!=''">
            and tco.dispatch_time &lt;= DATE_FORMAT(#{condition.dispatchTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="condition.companyEntrustIds!=null and condition.companyEntrustIds !=''">
            and tco.company_entrust_id in (${condition.companyEntrustIds})
        </if>
        order by tco.id desc
    </select>


    <resultMap id="searchDriverFreightOrdersMap" type="com.logistics.tms.api.feign.driverfreight.model.DriverFreightListSearchResponseModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="dispatch_user_name" property="dispatchUserName" jdbcType="VARCHAR"/>
        <result column="dispatch_time" property="dispatchTime" jdbcType="TIMESTAMP"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="vehicle_id" property="vehicleId" jdbcType="BIGINT"/>
        <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="driver_name" property="driverName" jdbcType="VARCHAR"/>
        <result column="driver_mobile" property="driverMobile" jdbcType="VARCHAR"/>
        <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
        <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
        <result column="sign_time" property="signTime" jdbcType="TIMESTAMP"/>
        <result column="dispatch_freight_fee_type" property="dispatchFreightFeeType" jdbcType="INTEGER"/>
        <result column="dispatch_freight_fee" property="dispatchFreightFee" jdbcType="DECIMAL"/>
        <result column="adjust_fee" property="adjustFee" jdbcType="DECIMAL"/>
        <result column="markup_fee" property="markupFee" jdbcType="DECIMAL"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="company_entrust_name" property="companyEntrustName" jdbcType="VARCHAR"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="VARCHAR"/>
        <result column="company_carrier_name" property="companyCarrierName" jdbcType="VARCHAR"/>
        <result column="company_carrier_type" property="companyCarrierType" jdbcType="INTEGER"/>
        <result column="carrier_contact_name" property="carrierContactName" jdbcType="VARCHAR"/>
        <result column="carrier_contact_phone" property="carrierContactMobile" jdbcType="VARCHAR"/>
        <result column="demand_order_source" property="demandOrderSource" jdbcType="INTEGER"/>
        <collection property="goodsInfoList"
                    ofType="com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="g_expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="g_load_amount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="g_unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="g_sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>
    <select id="searchDriverFreightOrdersByIds" resultMap="searchDriverFreightOrdersMap">
        select
        tco.id,
        tco.carrier_order_code,
        tco.customer_order_code,
        tco.status,
        tco.expect_amount,
        tco.unload_amount,
        tco.sign_time,
        tco.demand_order_id,
        tco.demand_order_code,
        tco.remark,
        tco.dispatch_freight_fee_type,
        tco.dispatch_freight_fee,
        tco.adjust_fee,
        tco.markup_fee,
        tco.if_empty,
        tco.dispatch_user_name,
        tco.dispatch_time,
        tco.goods_unit,
        tco.company_entrust_name,
        tco.company_carrier_id,
        tco.company_carrier_name,
        tco.company_carrier_type,
        tco.carrier_contact_name,
        AES_DECRYPT(UNHEX(tco.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrier_contact_phone,
        tco.demand_order_source,

        tcovh.vehicle_id,
        tcovh.vehicle_no,
        tcovh.driver_name,
        tcovh.driver_mobile,

        tcoa.load_province_name,
        tcoa.load_city_name,
        tcoa.load_area_name,
        tcoa.load_detail_address,
        tcoa.load_warehouse,
        tcoa.unload_province_name,
        tcoa.unload_city_name,
        tcoa.unload_area_name,
        tcoa.unload_detail_address,
        tcoa.unload_warehouse,

        tcog.id            as goodsId,
        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount AS g_expect_amount,
        tcog.load_amount   AS g_load_amount,
        tcog.unload_amount AS g_unload_amount,
        tcog.sign_amount   AS g_sign_amount
        from t_carrier_order tco
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        LEFT JOIN t_carrier_order_vehicle_history tcovh on tcovh.valid = 1 and tcovh.carrier_order_id = tco.id and tcovh.if_invalid = 1
        LEFT JOIN t_carrier_order_goods tcog on tcog.valid = 1 and tcog.carrier_order_id = tco.id
        where tco.valid = 1
        and tco.id in (${carrierOrderIds})
        order by tco.id desc
    </select>


    <!-- applet 相关-->
    <select id="searchCarrierOrderIdsForApp" resultType="java.lang.Long">
        select
        distinct tco.id
        from t_carrier_order tco
        left join t_carrier_order_vehicle_history tcovh on tcovh.carrier_order_id = tco.id and tcovh.if_invalid = 1 and tcovh.valid = 1
        where tco.valid = 1
        and tcovh.driver_id = #{params.driverId,jdbcType=BIGINT}
        <if test="params.carrierCompanyIds != null and params.carrierCompanyIds.size() != 0">
            and tco.company_carrier_id in
            <foreach collection="params.carrierCompanyIds" open="(" separator="," close=")" item="item">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="params.startTime != null and params.startTime != ''">
            and tco.created_time &gt;= DATE_FORMAT(#{params.startTime,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00')
        </if>
        <if test="params.endTime!= null and params.endTime != ''">
            and tco.created_time &lt;= DATE_FORMAT(#{params.endTime,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.status != null">
            <choose>
                <when test="params.status == 0">
                    and tco.if_cancel = 1
                </when>
                <when test="params.status == 20000"><!--待提货-->
                    and tco.status in (10000,20000) and tco.if_cancel = 0 and tco.if_empty = 0
                </when>
                <when test="params.status == 40000"><!--待卸货-->
                    and tco.status in (30000,40000) and tco.if_cancel = 0 and tco.if_empty = 0
                </when>
                <otherwise>
                    and tco.status = #{params.status,jdbcType=INTEGER} and tco.if_cancel = 0 and tco.if_empty = 0
                </otherwise>
            </choose>
        </if>
        <if test="params.ifYeloLife != null and params.ifYeloLife == 1">
            and tco.demand_order_source = 5
        </if>
        <if test="params.ifExtCarrierOrder != null and params.ifExtCarrierOrder != ''">
            and tco.if_ext_carrier_order = #{params.ifExtCarrierOrder,jdbcType=VARCHAR}
        </if>
        <if test="params.excludeCarrierOrderIdList != null and params.excludeCarrierOrderIdList.size() != 0">
            and tco.id not in
            <foreach collection="params.excludeCarrierOrderIdList" open="(" separator="," close=")" item="item">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        order by tco.id desc
    </select>
    <resultMap id="searchListByApp_Map" type="com.logistics.tms.controller.carrierorderapplet.response.SearchCarrierOrderListAppResponseModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="demand_order_source" property="demandOrderSource" jdbcType="INTEGER"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="load_longitude" property="loadLongitude" jdbcType="VARCHAR"/>
        <result column="load_latitude" property="loadLatitude" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="loadMobile" jdbcType="VARCHAR"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="unload_longitude" property="unloadLongitude" jdbcType="VARCHAR"/>
        <result column="unload_latitude" property="unloadLatitude" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="unloadMobile" jdbcType="VARCHAR"/>
        <result column="expected_load_time" property="expectArrivalTime" jdbcType="TIMESTAMP"/>
        <result column="expected_unload_time" property="expectedUnloadTime" jdbcType="TIMESTAMP"/>
        <result column="load_time" property="loadTime" jdbcType="TIMESTAMP"/>
        <result column="unload_time" property="unloadTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="business_type" property="businessType" jdbcType="INTEGER"/>
        <result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
        <result column="publish_name" property="publishName" jdbcType="VARCHAR"/>
        <result column="publish_mobile" property="publishMobile" jdbcType="VARCHAR"/>
        <result column="customer_user_name" property="customerUserName" jdbcType="VARCHAR"/>
        <result column="customer_user_mobile" property="customerUserMobile" jdbcType="VARCHAR"/>
        <result column="if_recycle_by_code" property="ifRecycleByCode" jdbcType="INTEGER"/>
        <result column="if_ext_carrier_order" property="ifExtCarrierOrder" jdbcType="INTEGER"/>
        <result column="demand_order_entrust_type" property="demandOrderEntrustType" jdbcType="INTEGER"/>

        <collection property="goodList" ofType="com.logistics.tms.controller.carrierorderapplet.response.CarrierOrderDetailGoodsInfoAppModel">
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="load_amount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>
    <select id="searchCarrierOrderForApp" resultMap="searchListByApp_Map">
        select
        tco.id,
        tco.demand_order_id,
        tco.carrier_order_code,
        tco.customer_order_code as customer_order_code,
        tco.status,
        tco.if_cancel,
        tco.if_empty,
        tco.load_time,
        tco.unload_time,
        tco.remark,
        tco.goods_unit,
        tco.business_type,
        tco.customer_name,
        tco.publish_name,
        AES_DECRYPT(UNHEX(tco.publish_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as publish_mobile,
        tco.demand_order_source,
        tco.customer_user_name,
        AES_DECRYPT(UNHEX(tco.customer_user_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as customer_user_mobile,
        tco.if_recycle_by_code,
        tco.if_ext_carrier_order,
        tco.demand_order_entrust_type,

        tcoa.load_province_name,
        tcoa.load_city_name,
        tcoa.load_area_name,
        tcoa.load_detail_address,
        tcoa.load_warehouse,
        tcoa.load_longitude,
        tcoa.load_latitude,
        tcoa.consignor_mobile,
        tcoa.expected_load_time,
        tcoa.unload_province_name,
        tcoa.unload_city_name,
        tcoa.unload_area_name,
        tcoa.unload_detail_address,
        tcoa.unload_warehouse,
        tcoa.unload_longitude,
        tcoa.unload_latitude,
        tcoa.receiver_mobile,
        tcoa.expected_unload_time,

        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount,
        tcog.load_amount,
        tcog.unload_amount,
        tcog.sign_amount
        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tcoa.carrier_order_id = tco.id and tcoa.valid = 1
        left join t_carrier_order_goods tcog on tcog.carrier_order_id = tco.id and tcog.valid = 1
        where tco.valid = 1
        and tco.id in (${ids})
        order by tco.id desc
    </select>

    <select id="searchListAccountByApp" resultType="com.logistics.tms.controller.carrierorderapplet.response.SearchCarrierOrderCountResponseModel">
        select
        ifnull(count(tco.id), 0)                                                                          as allCount,
        ifnull(sum(if(tco.status in (10000, 20000) and tco.if_cancel = 0 and tco.if_empty = 0, 1, 0)), 0) as waitLoadCount,
        ifnull(sum(if(tco.status in (30000, 40000) and tco.if_cancel = 0 and tco.if_empty = 0, 1, 0)), 0) as waitUnloadCount,
        ifnull(sum(if(tco.status in (50000) and tco.if_cancel = 0 and tco.if_empty = 0, 1, 0)), 0)        as waitSignUpCount
        from
        (
        select
        DISTINCT tco.id,
        tco.if_cancel,
        tco.if_empty,
        tco.status,
        tcovh.driver_id
        from t_carrier_order tco
        left join t_carrier_order_vehicle_history tcovh on tcovh.carrier_order_id = tco.id and tcovh.if_invalid = 1 and tcovh.valid = 1
        where tco.valid = 1
        and (
        <foreach collection="params.driverModel" item="item" open="(" close=")" separator="or">
            tco.company_carrier_id = #{item.companyCarrierId,jdbcType=BIGINT}
            and tcovh.driver_id = #{item.driverId,jdbcType=BIGINT}
        </foreach>
        )
        <if test="params.startTime != null and params.startTime != ''">
            and tco.created_time &gt;= DATE_FORMAT(#{params.startTime,jdbcType=VARCHAR}, '%Y-%m-%d 00:00:00')
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            and tco.created_time &lt;= DATE_FORMAT(#{params.endTime,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="params.ifYeloLife != null and params.ifYeloLife == 1">
            and tco.demand_order_source = 5
        </if>
        <if test="params.ifExtCarrierOrder != null and params.ifExtCarrierOrder != ''">
            and tco.if_ext_carrier_order = #{params.ifExtCarrierOrder,jdbcType=VARCHAR}
        </if>
        <if test="params.excludeCarrierOrderIdList != null and params.excludeCarrierOrderIdList.size() != 0">
            and tco.id not in
            <foreach collection="params.excludeCarrierOrderIdList" open="(" separator="," close=")" item="item">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        ) tco
    </select>

    <resultMap id="carrierOrderDetailByApp_Map" type="com.logistics.tms.controller.carrierorderapplet.response.CarrierOrderDetailAppResponseModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="cancel_reason" property="cancelReason" jdbcType="VARCHAR"/>
        <result column="cancel_operator_name" property="cancelOperatorName" jdbcType="VARCHAR" />
        <result column="cancel_time" property="cancelTime" jdbcType="TIMESTAMP" />
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="load_longitude" property="loadLongitude" jdbcType="VARCHAR"/>
        <result column="load_latitude" property="loadLatitude" jdbcType="VARCHAR"/>
        <result column="consignor_name" property="loadPerson" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="loadMobile" jdbcType="VARCHAR"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="unload_longitude" property="unloadLongitude" jdbcType="VARCHAR"/>
        <result column="unload_latitude" property="unloadLatitude" jdbcType="VARCHAR"/>
        <result column="receiver_name" property="unloadPerson" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="unloadMobile" jdbcType="VARCHAR"/>
        <result column="expected_load_time" property="expectArrivalTime" jdbcType="TIMESTAMP"/>
        <result column="expected_unload_time" property="expectedUnloadTime" jdbcType="TIMESTAMP"/>
        <result column="load_time" property="loadTime" jdbcType="TIMESTAMP"/>
        <result column="unload_time" property="unloadTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="source" property="source" jdbcType="INTEGER"/>
        <result column="dispatch_user_id" property="dispatchUserId" jdbcType="BIGINT"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="demand_order_source" property="demandOrderSource" jdbcType="INTEGER"/>
        <result column="business_type" property="businessType" jdbcType="INTEGER"/>
        <result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
        <result column="publish_name" property="publishName" jdbcType="VARCHAR"/>
        <result column="publish_mobile" property="publishMobile" jdbcType="VARCHAR"/>
        <result column="customer_user_name" property="customerUserName" jdbcType="VARCHAR"/>
        <result column="customer_user_mobile" property="customerUserMobile" jdbcType="VARCHAR"/>
        <result column="demand_order_entrust_type" property="entrustType" jdbcType="INTEGER"/>
        <result column="ifRecycleByCode" property="ifRecycleByCode" jdbcType="VARCHAR"/>
        <result column="project_label" property="projectLabel" jdbcType="VARCHAR"/>



        <collection property="carrierOrderDetailGoodsInfo" ofType="com.logistics.tms.controller.carrierorderapplet.response.CarrierOrderDetailGoodsInfoAppModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="demand_order_goods_id" property="demandOrderGoodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="load_amount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="sign_amount" property="signAmount" jdbcType="DECIMAL"/>

            <collection property="codeDtoList" ofType="com.logistics.tms.controller.carrierorderapplet.response.CarrierOrderDetailCodeModel">
                <result column="tcogcYeloCode" property="yeloCode" jdbcType="VARCHAR"/>
                <result column="tcogcLoadAmount" property="loadAmount" jdbcType="DECIMAL"/>
                <result column="tcogcUnloadAmount" property="unloadAmount" jdbcType="DECIMAL"/>
                <result column="tcogcSignAmount" property="signAmount" jdbcType="DECIMAL"/>
                <result column="tcogcUnit" property="unit" jdbcType="INTEGER"/>
            </collection>
        </collection>
    </resultMap>

    <select id="carrierOrderDetailByApp" resultMap="carrierOrderDetailByApp_Map">
        select
        tco.id,
        tco.demand_order_id,
        tco.carrier_order_code,
        tco.customer_order_code,
        tco.status,
        tco.if_cancel,
        tco.if_empty,
        tco.cancel_reason,
        tco.cancel_operator_name,
        tco.cancel_time,
        tco.load_time,
        tco.unload_time,
        tco.remark,
        tco.source,
        tco.dispatch_user_id,
        tco.goods_unit,
        tco.business_type,
        tco.customer_name,
        tco.publish_name,
        AES_DECRYPT(UNHEX(tco.publish_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as publish_mobile,
        tco.company_carrier_id,
        tco.demand_order_source,
        tco.customer_user_name,
        AES_DECRYPT(UNHEX(tco.customer_user_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as customer_user_mobile,
        tco.demand_order_entrust_type,
        tco.if_recycle_by_code ifRecycleByCode,
        tco.project_label,

        tcoa.load_province_name,
        tcoa.load_city_name,
        tcoa.load_area_name,
        tcoa.load_detail_address,
        tcoa.load_warehouse,
        tcoa.load_longitude,
        tcoa.load_latitude,
        tcoa.consignor_name,
        tcoa.consignor_mobile,
        tcoa.expected_load_time,
        tcoa.unload_province_name,
        tcoa.unload_city_name,
        tcoa.unload_area_name,
        tcoa.unload_detail_address,
        tcoa.unload_warehouse,
        tcoa.unload_longitude,
        tcoa.unload_latitude,
        tcoa.receiver_name,
        tcoa.receiver_mobile,
        tcoa.expected_unload_time,

        tcog.id as goodsId,
        tcog.demand_order_goods_id,
        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount,
        tcog.load_amount,
        tcog.unload_amount,
        tcog.sign_amount,

        tcogc.yelo_good_code tcogcYeloCode,
        tcogc.load_amount tcogcLoadAmount,
        tcogc.unload_amount tcogcUnloadAmount,
        tcogc.sign_amount tcogcSignAmount,
        tcogc.unit tcogcUnit

        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tcoa.carrier_order_id = tco.id and tcoa.valid = 1
        left join t_carrier_order_goods tcog on tcog.carrier_order_id = tco.id and tcog.valid = 1
        left join t_carrier_order_goods_code tcogc on tcogc.carrier_order_goods_id = tcog.id and tcogc.valid = 1
        where tco.valid = 1
        and tco.id = #{carrierOrderId,jdbcType=BIGINT}
    </select>

    <resultMap id="getExtVehicleCarrierOrder_Map" type="com.logistics.tms.api.feign.extvehiclesettlement.model.ExtVehicleCarrierOrderModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="dispatch_freight_fee_type" property="dispatchFreightFeeType" jdbcType="INTEGER"/>
        <result column="dispatch_freight_fee" property="dispatchFreightFee" jdbcType="DECIMAL"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="last_modified_by" property="userName" jdbcType="VARCHAR"/>
        <result column="vehicle_id" property="vehicleId" jdbcType="BIGINT"/>
        <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="driver_id" property="staffId" jdbcType="BIGINT"/>
        <result column="driver_name" property="staffName" jdbcType="VARCHAR"/>
        <result column="driver_mobile" property="staffMobile" jdbcType="VARCHAR"/>
        <collection property="goodsInfoList" ofType="com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="g_expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="g_load_amount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="g_unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="g_sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>
    <select id="getExtVehicleCarrierOrder" resultMap="getExtVehicleCarrierOrder_Map">
        select
        tco.id,
        tco.dispatch_freight_fee_type,
        tco.dispatch_freight_fee,
        tco.goods_unit,
        tco.last_modified_by,

        tcog.id            as goodsId,
        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount as g_expect_amount,
        tcog.load_amount   as g_load_amount,
        tcog.unload_amount as g_unload_amount,
        tcog.sign_amount   as g_sign_amount,

        tcovh.vehicle_id,
        tcovh.vehicle_no,
        tcovh.driver_id,
        tcovh.driver_name,
        tcovh.driver_mobile
        from t_carrier_order tco
        left join t_carrier_order_goods tcog on tcog.carrier_order_id = tco.id and tcog.valid = 1
        left join t_carrier_order_vehicle_history tcovh on tcovh.carrier_order_id = tco.id and tcovh.if_invalid = 1 and tcovh.valid = 1
        left join t_vehicle_basic tvb on tvb.id = tcovh.vehicle_id and tvb.valid = 1
        where tco.valid = 1
          and tco.id in (${ids})
          and tvb.vehicle_property = 2
    </select>
    <resultMap id="getLoadDetailByIdsMap" type="com.logistics.tms.controller.carrierorder.response.LoadDetailResponseModel">
        <result column="carrierOrderId" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="carrierOrderCode" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="companyEntrustId" property="companyEntrustId" jdbcType="BIGINT"/>
        <result column="companyEntrustName" property="companyEntrustName" jdbcType="VARCHAR"/>
        <result column="goodsUnit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="dispatchTime" property="dispatchTime" jdbcType="TIMESTAMP"/>
        <result column="vehicleNo" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="driverName" property="driverName" jdbcType="VARCHAR"/>
        <result column="driverMobile" property="driverMobile" jdbcType="VARCHAR"/>
        <result column="loadProvinceName" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="loadCityName" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="loadAreaName" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="loadDetailAddress" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="loadWarehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="unloadProvinceName" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unloadCityName" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unloadAreaName" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unloadDetailAddress" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unloadWarehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <collection property="goodsList" ofType="com.logistics.tms.controller.carrierorder.response.CarrierOrderGoodsResponseModel">
            <result column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="expectAmount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="loadAmount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="unLoadAmount" property="unLoadAmount" jdbcType="DECIMAL"/>
            <result column="goodsName" property="goodsName" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>
    <select id="getLoadDetailByIds" resultMap="getLoadDetailByIdsMap">
        select
        tco.id as carrierOrderId,
        tco.carrier_order_code as carrierOrderCode,
        tco.company_entrust_id as companyEntrustId,
        tco.company_entrust_name as companyEntrustName,
        tco.goods_unit as goodsUnit,
        tco.dispatch_time as dispatchTime,

        tcovh.vehicle_no as vehicleNo,
        tcovh.driver_name as driverName,
        tcovh.driver_mobile as driverMobile,

        tcoa.load_province_name as loadProvinceName,
        tcoa.load_city_name as loadCityName,
        tcoa.load_area_name as loadAreaName,
        tcoa.load_detail_address as loadDetailAddress,
        tcoa.load_warehouse as loadWarehouse,
        tcoa.unload_province_name as unloadProvinceName ,
        tcoa.unload_city_name as unloadCityName,
        tcoa.unload_area_name as unloadAreaName,
        tcoa.unload_detail_address as unloadDetailAddress,
        tcoa.unload_warehouse as unloadWarehouse,

        tcog.id as goodsId,
        tcog.expect_amount as expectAmount,
        tcog.load_amount as loadAmount,
        tcog.unload_amount as unLoadAmount,
        tcog.goods_name as goodsName

        from t_carrier_order tco
        left join t_carrier_order_vehicle_history tcovh on tco.id = tcovh.carrier_order_id and tcovh.if_invalid = 1 and tcovh.valid= 1
        left join t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        left join t_carrier_order_goods tcog on tcog.valid =1 and tcog.carrier_order_id = tco.id
        where tco.valid = 1 and tco.id in (${carrierOrderIds})
    </select>

    <select id="getByCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List_Decrypt"/>
        from t_carrier_order
        where valid = 1
        and carrier_order_code = #{carrierOrderCode,jdbcType = VARCHAR}
    </select>

    <select id="getCarrierOrderByCodes" resultMap="BaseResultMap">
        select <include refid="Base_Column_List_Decrypt"/>
        FROM t_carrier_order
        where valid =  1
        and carrier_order_code in
        <if test="carrierOrderCodeList != null and carrierOrderCodeList.size > 0">
            <foreach collection="carrierOrderCodeList" open="(" close=")" item="carrierOrderCode" separator=",">
                #{carrierOrderCode}
            </foreach>
        </if>
    </select>

    <resultMap id="exportCarrierOrderTickets_Map" type="com.logistics.tms.controller.carrierorder.response.ExportCarrierOrderTicketsModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
        <collection property="signTicketsList" ofType="string" javaType="list">
            <result column="image_path"/>
        </collection>
    </resultMap>
    <select id="exportCarrierOrderTickets" resultMap="exportCarrierOrderTickets_Map">
        select
        tco.id,
        tco.carrier_order_code,
        tco.customer_order_code,
        tcovh.vehicle_no,
        tcot.image_path
        from t_carrier_order tco
        left join t_carrier_order_vehicle_history tcovh on tcovh.carrier_order_id = tco.id and tcovh.if_invalid = 1 and tcovh.valid = 1
        left join t_carrier_order_tickets tcot on tcot.carrier_order_id = tco.id and tcot.valid = 1
        where tco.valid = 1
        and tco.id in (${carrierOrderIds})
        and tcot.image_type = 3
        order by tco.id desc, tcot.id desc
    </select>

    <select id="updateCarrierOrderUnloadAddressDetail" resultType="com.logistics.tms.controller.carrierorder.response.UpdateUnloadAddressDetailResponseModel">
        select
        tco.id                        as carrierOrderId,
        tco.carrier_order_code        as carrierOrderCode,
        tco.customer_order_code       as customerOrderCode,
        tco.stock_in_state            as stockInState,
        tco.demand_order_entrust_type as entrustType,
        tco.demand_order_source       as demandOrderSource,

        tcoa.id                       as carrierOrderAddressId,
        tcoa.unload_province_name     as unloadProvinceName,
        tcoa.unload_city_name         as unloadCityName,
        tcoa.unload_area_name         as unloadAreaName,
        tcoa.unload_detail_address    as unloadDetailAddress,
        tcoa.unload_warehouse         as unloadWarehouse
        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tcoa.carrier_order_id = tco.id and tcoa.valid = 1
        where tco.valid = 1
          and tco.id in (${carrierOrderIds})
          and tco.status &lt; 60000
          and tco.if_cancel = 0
          and tco.if_empty = 0
    </select>

    <select id="carrierOrderCorrectDetail" resultType="com.logistics.tms.controller.carrierorder.response.CarrierOrderCorrectDetailResponseModel">
        select
        tco.id                              as carrierOrderId,
        tco.carrier_order_code              as carrierOrderCode,
        tco.expect_amount                   as expectAmount,
        tco.load_amount_expect              as loadAmountExpect,
        tco.unload_amount_expect            as unloadAmountExpect,
        tco.load_amount                     as loadAmount,
        tco.unload_amount                   as unloadAmount,
        tco.goods_unit                      as goodsUnit,
        tco.company_carrier_id              as companyCarrierId,
        tco.carrier_price_type              as carrierFreightType,
        tco.carrier_price                   as carrierFreight,
        tco.demand_order_entrust_type       as entrustType,
        tco.unload_amount                   as goodsAmount,
        tco.abnormal_amount                 as abnormalAmount,
        tco.carrier_settle_statement_status as carrierSettleStatementStatus,
        tco.carrier_settlement              as carrierSettlement,
        tco.expect_mileage                  as expectMileage,

        tcoa.load_area_id                   as loadAreaId,
        tcoa.unload_area_id                    unloadAreaId,

        tcoc.correct_status                 as correctStatus,
        tcoc.stock_in_count                 as stockInCount,
        tcoc.stock_in_remark                as stockInRemark,
        tcoc.correct_type                   as correctType,
        tcoc.correct_user                   as correctUser,
        tcoc.correct_time                   as correctTime,
        tcoc.load_error_amount              as loadErrorAmount,
        tcoc.lose_error_amount              as loseErrorAmount
        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tcoa.valid = 1 and tco.id = tcoa.carrier_order_id
        left join t_carrier_order_correct tcoc on tcoc.carrier_order_id = tco.id and tcoc.valid = 1
        where tco.valid = 1
          and tco.id = #{carrierOrderId,jdbcType=BIGINT}
    </select>

    <resultMap id="getCarrierOrderInfoForAuto_Map" type="com.logistics.tms.controller.carrierorder.response.CarrierOrderInfoForAutoModel">
        <id column="carrierOrderId" property="carrierOrderId" jdbcType="BIGINT" />
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="expectAmount" property="expectAmount" jdbcType="DECIMAL" />
        <result column="loadAmount" property="loadAmount" jdbcType="DECIMAL" />
        <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="entrust_freight_type" property="entrustFreightType" jdbcType="INTEGER"/>
        <result column="entrust_freight" property="entrustFreight" jdbcType="DECIMAL"/>
        <result column="carrier_price_type" property="carrierPriceType" jdbcType="INTEGER"/>
        <result column="carrier_price" property="carrierPrice" jdbcType="DECIMAL"/>
        <result column="isOurCompany" property="isOurCompany" jdbcType="INTEGER"/>
        <result column="entrustType" property="entrustType" jdbcType="INTEGER"/>
        <result column="carrier_settlement" property="carrierSettlement" jdbcType="INTEGER"/>
        <result column="settlement_tonnage" property="settlementTonnage" jdbcType="INTEGER"/>
        <result column="carrier_settle_statement_status" property="carrierSettleStatementStatus" jdbcType="INTEGER"/>
        <result column="abnormal_amount" property="abnormalAmount" jdbcType="DECIMAL"/>
        <result column="expect_mileage" property="expectMileage" jdbcType="DECIMAL"/>
        <result column="bargaining_mode" property="bargainingMode" jdbcType="INTEGER"/>
        <result column="project_label" property="projectLabel" jdbcType="VARCHAR"/>
        <result column="demand_order_entrust_type" property="demandOrderEntrustType" jdbcType="INTEGER"/>
        <collection property="goodsList" ofType="com.logistics.tms.controller.carrierorder.response.CarrierOrderGoodsForAutoModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="load_amount" property="loadAmount" jdbcType="DECIMAL" />
            <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL" />
        </collection>
    </resultMap>

    <select id="getCarrierOrderInfoForAuto" resultMap="getCarrierOrderInfoForAuto_Map">
        select
        tco.id                        as carrierOrderId,
        tco.carrier_order_code,
        tco.status,
        tco.demand_order_id,
        tco.expect_amount             as expectAmount,
        tco.load_amount               as loadAmount,
        tco.goods_unit,
        tco.entrust_freight_type,
        tco.entrust_freight,
        tco.carrier_price_type,
        tco.carrier_price,
        tco.demand_order_entrust_type as entrustType,
        tco.unload_amount,
        tco.carrier_settlement,
        tco.settlement_tonnage,
        tco.company_carrier_id,
        tco.carrier_settle_statement_status,
        tco.abnormal_amount,
        tco.expect_mileage,
        tco.bargaining_mode,
        tco.project_label,
        tco.demand_order_entrust_type,

        tcc.level                     as isOurCompany,

        tcog.id                       as goodsId,
        tcog.load_amount,
        tcog.unload_amount
        from t_carrier_order tco
        left join t_company_carrier tcc on tcc.valid = 1 and tcc.id = tco.company_carrier_id
        left join t_carrier_order_goods tcog on tcog.valid = 1 and tcog.carrier_order_id = tco.id
        where tco.valid = 1
          and tco.id = #{carrierOrderId,jdbcType=BIGINT}
    </select>

    <select id="mapDataStatistics" resultType="com.logistics.tms.controller.demandorder.response.MapDataStatisticsResponseModel">
        select
        sum(if(tco.status in (10000, 20000), 1, 0)) as waitLoadCount,
        sum(if(tco.correct_status = 0, 1, 0))       as waitCorrectCount,
        tcoa.load_city_name                         as cityName,
        tcoa.load_city_id                           as loadCityId
        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tco.id = tcoa.carrier_order_id and tcoa.valid = 1
        where tco.valid = 1
        and tco.demand_order_source = 1
        and tco.if_cancel = 0
        and tco.if_empty = 0
        and tcoa.load_city_id > 0
        GROUP BY tcoa.load_city_id
        having waitCorrectCount > 0
            or waitLoadCount > 0
    </select>

    <resultMap id="logisticsCostStatistics_Map" type="com.logistics.tms.controller.carrierorder.response.LogisticsCostStatisticsModel">
        <result column="yearMonth" property="yearMonth" jdbcType="VARCHAR"/>
        <collection property="orderList" ofType="com.logistics.tms.controller.carrierorder.response.LogisticsCostStatisticsOrderModel">
            <result column="signAmount" property="signAmount" jdbcType="DECIMAL"/>
            <result column="dispatchFreightFee" property="dispatchFreightFee" jdbcType="DECIMAL"/>
            <result column="adjustFee" property="adjustFee" jdbcType="DECIMAL"/>
            <result column="markupFee" property="markupFee" jdbcType="DECIMAL"/>
            <result column="entrustType" property="entrustType" jdbcType="INTEGER"/>
        </collection>
    </resultMap>
    <select id="logisticsCostStatistics" resultMap="logisticsCostStatistics_Map">
        select
        DATE_FORMAT(tco.sign_time,'%Y-%m') as yearMonth,
        sum(tco.sign_amount) as signAmount,
        sum(CASE
                when tco.dispatch_freight_fee_type = 1 then tco.dispatch_freight_fee*tco.unload_amount
                else tco.dispatch_freight_fee
            end) as dispatchFreightFee,
        sum(tco.adjust_fee) as adjustFee,
        sum(tco.markup_fee) as markupFee,

        tco.demand_order_entrust_type as entrustType
        from t_carrier_order tco
        where tco.status = 60000
        and tco.valid = 1
        and tco.if_cancel = 0
        and tco.if_empty = 0
        and tco.demand_order_source = 1
        and tco.demand_order_entrust_type in (1,2,4,10,11,12)
        and DATE_FORMAT(tco.sign_time,'%Y-%m') >= #{signTimeStart,jdbcType=VARCHAR}
        and DATE_FORMAT(tco.sign_time,'%Y-%m') &lt;= #{signTimeEnd,jdbcType=VARCHAR}
        group by yearMonth,entrustType
    </select>

    <resultMap id="logisticsLoadValidityStatistics_Map" type="com.logistics.tms.controller.carrierorder.response.LogisticsLoadValidityStatisticsModel">
        <result column="yearMonth" property="yearMonth" jdbcType="VARCHAR"/>
        <collection property="orderList" ofType="com.logistics.tms.controller.carrierorder.response.LogisticsLoadValidityStatisticsOrderModel">
            <result column="loadValidity" property="loadValidity" jdbcType="DECIMAL"/>
            <result column="carrierOrderCount" property="carrierOrderCount" jdbcType="DECIMAL"/>
            <result column="entrustType" property="entrustType" jdbcType="INTEGER"/>
        </collection>
    </resultMap>
    <select id="logisticsLoadValidityStatistics" resultMap="logisticsLoadValidityStatistics_Map">
        select
        DATE_FORMAT(tco.load_time,'%Y-%m') as yearMonth,
        sum(tco.load_validity) as loadValidity,
        count(tco.id) as carrierOrderCount,

        tco.demand_order_entrust_type as entrustType
        from t_carrier_order tco
        where tco.status > 20000
        and tco.valid = 1
        and tco.if_cancel = 0
        and tco.if_empty = 0
        and tco.demand_order_source = 1
        and tco.demand_order_entrust_type in (1,2,4,10,11,12)
        and DATE_FORMAT(tco.load_time,'%Y-%m') >= #{signTimeStart,jdbcType=VARCHAR}
        and DATE_FORMAT(tco.load_time,'%Y-%m') &lt;= #{signTimeEnd,jdbcType=VARCHAR}
        group by yearMonth,entrustType
    </select>

    <select id="waitCorrectStatistics" resultType="com.logistics.tms.controller.carrierorder.response.WaitCorrectStatisticsModel">
        SELECT
        count(tco.id)                 AS carrierOrderCount,
        tcoa.load_province_name       AS loadProvinceName,
        tcoa.load_city_name           AS loadCityName,
        tcoa.load_region_contact_name AS loadRegionContactName,
        tcoa.load_city_id             as loadCityId
        FROM t_carrier_order tco
        LEFT JOIN t_carrier_order_address tcoa ON tcoa.carrier_order_id = tco.id and tcoa.valid = 1
        LEFT JOIN t_carrier_order_correct tcoc ON tcoc.carrier_order_id = tco.id and tcoc.valid = 1
        WHERE tcoc.correct_status = 0
        and tco.valid = 1
        AND tco.demand_order_entrust_type in (2,10)
        AND tco.demand_order_source = 1
        GROUP BY tcoa.load_province_id, tcoa.load_city_id
        order by carrierOrderCount desc, tcoa.load_city_id
    </select>
    <select id="waitLoadStatistics" resultType="com.logistics.tms.controller.carrierorder.response.WaitLoadStatisticsModel">
        select
        tco.expect_amount as expectAmount,
        date_format(tco.dispatch_time,'%Y-%m-%d') as dispatchTime
        FROM t_carrier_order tco
        WHERE tco.STATUS IN ( 10000, 20000 )
        and tco.demand_order_source = 1
        AND tco.valid = 1
        and tco.if_cancel = 0
        and tco.if_empty=0
    </select>
    <select id="selectCorrectCarrierOrderCount" resultType="com.logistics.tms.controller.demandorder.response.CorrectCarrierOrderDataStatisticsResponseModel">
        SELECT
        count( tco.id ) AS waitCorrectCount,
        sum(tco.expect_amount) AS waitCorrectAmount
        FROM t_carrier_order tco
        LEFT JOIN t_carrier_order_correct tcoc ON tcoc.carrier_order_id = tco.id and tcoc.valid = 1
        WHERE tcoc.correct_status = 0
        and tco.valid = 1
        AND tco.demand_order_entrust_type in (2,10)
        AND tco.demand_order_source = 1
    </select>

    <select id="selectCarrierOrdersCountVehicleByDemandOrderIds" resultType="com.logistics.tms.controller.demandorder.request.WebDemandOrderDispatchVehicleRequestModel">
        SELECT
        COUNT(*) as countVehicle ,
        demand_order_id as demandOrderId,
        sum(sign_amount) as signAmount
        FROM
        t_carrier_order
        WHERE valid = 1
        and if_cancel=0
        and if_empty = 0
        AND demand_order_id IN (${demandOrderIds})
        GROUP BY demand_order_id
    </select>

    <select id="searchCarrierOrderIdsForWeb" resultType="java.lang.Long">
        select
        distinct tco.id
        from t_carrier_order tco
        LEFT JOIN t_demand_order tdo on tdo.valid = 1 and tdo.id = tco.demand_order_id
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        LEFT JOIN t_carrier_order_vehicle_history tcovh on tcovh.valid = 1 and tcovh.carrier_order_id = tco.id and tcovh.if_invalid = 1
        LEFT JOIN t_carrier_order_goods tcog on tcog.valid = 1 and tcog.carrier_order_id = tco.id
        where tco.valid= 1
        and tco.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}

        <if test="condition.demandOrderCodeList != null and condition.demandOrderCodeList.size() > 0">
            and tco.demand_order_code in
            <foreach collection="condition.demandOrderCodeList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="condition.carrierOrderCodeList != null and condition.carrierOrderCodeList.size() > 0">
            and tco.carrier_order_code in
            <foreach collection="condition.carrierOrderCodeList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>

        <if test="condition.carrierOrderCode !=null and condition.carrierOrderCode != ''">
            and (INSTR(tco.carrier_order_code,#{condition.carrierOrderCode,jdbcType=VARCHAR})>0
            or INSTR(REPLACE(tco.carrier_order_code,'-',''),#{condition.carrierOrderCode,jdbcType=VARCHAR})>0)
        </if>
        <if test="condition.customerOrderCode != null and condition.customerOrderCode != ''">
            and if(tdo.order_type = 21, instr(tdo.sinopec_order_no, #{condition.customerOrderCode,jdbcType=VARCHAR}) > 0,
            instr(tco.customer_order_code, #{condition.customerOrderCode,jdbcType=VARCHAR}) > 0)
        </if>
        <if test="condition.status != null">
            <choose>
                <when test="condition.status==20000">
                    and tco.status in (10000,20000) and tco.if_cancel = 0 and tco.if_empty=0
                </when>
                <when test="condition.status==40000">
                    and tco.status in (30000,40000) and tco.if_cancel = 0
                </when>
                <when test="condition.status==0">
                    and tco.if_cancel = 1
                </when>
                <when test="condition.status==2">
                    and tco.if_empty = 1
                </when>
                <otherwise>
                    and tco.status = #{condition.status,jdbcType=VARCHAR} and tco.if_cancel = 0 and tco.if_empty = 0
                </otherwise>
            </choose>
        </if>
        <if test="condition.loadAddress!=null and condition.loadAddress!=''">
            and instr(concat(tcoa.load_province_name,tcoa.load_city_name,tcoa.load_area_name,tcoa.load_detail_address,tcoa.load_warehouse),#{condition.loadAddress,jdbcType=VARCHAR})
        </if>
        <if test="condition.unloadAddress!=null and condition.unloadAddress!=''">
            and instr(concat(tcoa.unload_province_name,tcoa.unload_city_name,tcoa.unload_area_name,tcoa.unload_detail_address,tcoa.unload_warehouse),#{condition.unloadAddress,jdbcType=VARCHAR})
        </if>
        <if test="condition.vehicleNo!=null and condition.vehicleNo!=''">
            and instr(tcovh.vehicle_no,#{condition.vehicleNo,jdbcType=VARCHAR})>0
        </if>
        <if test="condition.driver!=null and condition.driver!=''">
            and instr(concat(tcovh.driver_name, tcovh.driver_mobile),#{condition.driver,jdbcType=VARCHAR})
        </if>
        <if test="condition.size!=null and condition.size!=''">
            and (instr(concat(tcog.length,'*',tcog.width,'*',tcog.height),#{condition.size,jdbcType=VARCHAR})>0
            or instr(tcog.goods_name,#{condition.size,jdbcType=VARCHAR})>0
            or instr(tcog.goods_size,#{condition.size,jdbcType=VARCHAR})>0)
        </if>
        <if test="condition.dispatchTimeFrom!=null and condition.dispatchTimeFrom!=''">
            and tco.dispatch_time &gt;= DATE_FORMAT(#{condition.dispatchTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="condition.dispatchTimeTo!=null and condition.dispatchTimeTo!=''">
            and tco.dispatch_time &lt;= DATE_FORMAT(#{condition.dispatchTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="condition.expectArrivalTimeFrom!=null and condition.expectArrivalTimeFrom!=''">
            and tcovh.expect_arrival_time &gt;= DATE_FORMAT(#{condition.expectArrivalTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d
            %H:%i:%S')
        </if>
        <if test="condition.expectArrivalTimeTo!=null and condition.expectArrivalTimeTo!=''">
            and tcovh.expect_arrival_time &lt;= DATE_FORMAT(#{condition.expectArrivalTimeTo,jdbcType=VARCHAR},'%Y-%m-%d
            23:59:59')
        </if>
        <if test="condition.dispatchUserName != null and condition.dispatchUserName!=''">
            and instr(tco.dispatch_user_name,#{condition.dispatchUserName,jdbcType=VARCHAR})>0
        </if>
        <if test="condition.companyEntrustName != null and condition.companyEntrustName !=''">
            and instr(tco.company_entrust_name, #{condition.companyEntrustName,jdbcType=VARCHAR})
        </if>
        <if test="condition.recycleTaskType != null">
            and tco.recycle_task_type = #{condition.recycleTaskType,jdbcType=INTEGER}
        </if>
        <if test="condition.entrustType != null">
            and tco.demand_order_entrust_type = #{condition.entrustType,jdbcType=INTEGER}
        </if>
        <if test="condition.excludeCarrierOrderIdList != null and condition.excludeCarrierOrderIdList.size() > 0">
            and tco.id not in
            <foreach collection="condition.excludeCarrierOrderIdList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="condition.ifRecycleByCode != null">
            and tco.if_recycle_by_code = #{condition.ifRecycleByCode,jdbcType=INTEGER}
            and tco.demand_order_entrust_type = 100
        </if>
        <if test="condition.ifExtCarrierOrder != null and condition.ifExtCarrierOrder != ''">
            and tco.if_ext_carrier_order = #{condition.ifExtCarrierOrder,jdbcType=VARCHAR}
        </if>
        <if test="condition.sort !=null and condition.sort!=''">
            order by ${condition.sort} ${condition.order} ,tco.id desc
        </if>
    </select>

    <resultMap id="searchCarrierOrderResultMap"
               type="com.logistics.tms.controller.carrierorder.response.SearchCarrierOrderListForWebResponseModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="dispatch_time" property="dispatchTime" jdbcType="TIMESTAMP"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="consignor_name" property="consignorName" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR"/>
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR"/>
        <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="driver_name" property="driverName" jdbcType="VARCHAR"/>
        <result column="driver_mobile" property="driverMobile" jdbcType="VARCHAR"/>
        <result column="expect_arrival_time" property="expectArrivalTime" jdbcType="TIMESTAMP"/>
        <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
        <result column="load_amount" property="loadAmount" jdbcType="DECIMAL"/>
        <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
        <result column="sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        <result column="load_time" property="loadTime" jdbcType="TIMESTAMP"/>
        <result column="unload_time" property="unloadTime" jdbcType="TIMESTAMP"/>
        <result column="print_count" property="printCount" jdbcType="INTEGER"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="cancel_reason" property="cancelReason" jdbcType="VARCHAR"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="company_entrust_name" property="entrust" jdbcType="VARCHAR"/>
        <result column="demand_order_entrust_type" property="entrustType" jdbcType="INTEGER"/>
        <result column="recycle_task_type" property="recycleTaskType" jdbcType="INTEGER"/>
        <result column="if_recycle_by_code" property="ifRecycleByCode" jdbcType="INTEGER"/>
        <result column="if_ext_carrier_order" property="ifExtCarrierOrder" jdbcType="INTEGER"/>
        <result column="demand_order_source" property="demandOrderSource" jdbcType="INTEGER"/>
        <result column="demand_order_entrust_type" property="demandOrderEntrustType" jdbcType="INTEGER"/>

        <collection property="goodsInfoList"
                    ofType="com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="g_expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="g_load_amount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="g_unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="g_sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        </collection>

    </resultMap>

    <select id="searchCarrierOrderForWeb" resultMap="searchCarrierOrderResultMap">
        select
        tco.id,
        tco.demand_order_id,
        tco.carrier_order_code,
        tco.customer_order_code,
        tco.status,
        tco.dispatch_time,
        tco.expect_amount,
        tco.load_amount,
        tco.unload_amount,
        tco.sign_amount,
        tco.load_time,
        tco.unload_time,
        tco.print_count,
        tco.demand_order_id,
        tco.if_cancel,
        tco.if_empty,
        tco.cancel_reason,
        tco.goods_unit,
        tco.company_entrust_name,
        tco.demand_order_entrust_type,
        tco.recycle_task_type,
        tco.if_recycle_by_code,
        tco.if_ext_carrier_order,
        tco.demand_order_source,
        tco.demand_order_entrust_type,


        tcovh.vehicle_no,
        tcovh.driver_name,
        tcovh.expect_arrival_time,
        tcovh.driver_mobile   as driver_mobile,

        tcoa.load_province_name,
        tcoa.load_city_name,
        tcoa.load_area_name,
        tcoa.load_detail_address,
        tcoa.load_warehouse,
        tcoa.unload_province_name,
        tcoa.unload_city_name,
        tcoa.unload_area_name,
        tcoa.unload_detail_address,
        tcoa.unload_warehouse,
        tcoa.consignor_name,
        tcoa.receiver_name,
        tcoa.consignor_mobile as consignor_mobile,
        tcoa.receiver_mobile  as receiver_mobile,

        tcog.id               as goodsId,
        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount    AS g_expect_amount,
        tcog.load_amount      AS g_load_amount,
        tcog.unload_amount    AS g_unload_amount,
        tcog.sign_amount      AS g_sign_amount

        from t_carrier_order tco
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        LEFT JOIN t_carrier_order_vehicle_history tcovh on tcovh.valid = 1 and tcovh.carrier_order_id = tco.id and tcovh.if_invalid = 1
        LEFT JOIN t_carrier_order_goods tcog on tcog.valid = 1 and tcog.carrier_order_id = tco.id
        where tco.valid= 1 and tco.id in (${ids})
        <if test="condition.sort !=null and condition.sort!=''">
            order by ${condition.sort} ${condition.order} ,tco.id desc
        </if>
    </select>

    <select id="statistics" resultType="com.logistics.tms.controller.carrierorder.response.CarrierOrderStatisticsResponseModel">
        select
        ifnull(count(*), 0)                                                                   as allCount,
        ifnull(sum(if(status in (10000, 20000) and if_cancel = 0 and if_empty = 0, 1, 0)), 0) as waitPickCount,
        ifnull(sum(if(status in (30000, 40000) and if_cancel = 0 and if_empty = 0, 1, 0)), 0) as waitUnloadCount,
        ifnull(sum(if(status in (50000) and if_cancel = 0 and if_empty = 0, 1, 0)), 0)        as waitSignCount,
        ifnull(sum(if(status in (60000) and if_cancel = 0 and if_empty = 0, 1, 0)), 0)        as signCount,
        ifnull(sum(if(if_cancel = 1, 1, 0)), 0)                                               as cancelCount,
        ifnull(sum(if(if_empty = 1, 1, 0)), 0)                                                as emptyCount
        from t_carrier_order
        where valid = 1
        and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        <if test="carrierOrderIds != null and carrierOrderIds != ''">
            and id in (${carrierOrderIds})
        </if>
    </select>

    <resultMap id="getCarrierOrderDetailByIdMap" type="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailForWebResponseModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="cancel_reason" property="cancelReason" jdbcType="VARCHAR"/>
        <result column="dispatch_user_name" property="dispatchUserName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
        <result column="load_amount" property="loadAmount" jdbcType="DECIMAL"/>
        <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
        <result column="sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        <result column="company_entrust_name" property="entrustCompany" jdbcType="VARCHAR"/>
        <result column="carrier_settle_statement_status" property="carrierSettleStatementStatus" jdbcType="INTEGER"/>
        <association property="carrierOrderDetailBasicInfo" resultMap="carrierOrderDetailBasicInfoMap"/>
        <collection property="carrierOrderDetailGoodsInfo" ofType="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailGoodsInfoModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="g_expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="g_load_amount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="g_unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="g_sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        </collection>
        <collection property="carrierOrderDetailVehicleDriverInfo" ofType="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailVehicleDriverInfoModel">
            <id column="vehicleHistoryId" property="vehicleHistoryId" jdbcType="BIGINT"/>
            <result column="audit_status" property="auditStatus" jdbcType="INTEGER"/>
            <result column="if_invalid" property="ifInvalid" jdbcType="INTEGER"/>
            <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
            <result column="driver_name" property="driverName" jdbcType="VARCHAR"/>
            <result column="driver_mobile" property="driverMobile" jdbcType="VARCHAR"/>
            <result column="reject_reason" property="rejectReason" jdbcType="VARCHAR"/>
            <result column="vehicleHistoryRemark" property="remark" jdbcType="VARCHAR"/>
            <result column="vehicleHistoryCreateTime" property="createdTime" jdbcType="TIMESTAMP"/>
        </collection>
        <collection property="carrierOrderDetailEvents" ofType="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailEventModel">
            <id column="eventId" property="eventId" jdbcType="BIGINT"/>
            <result column="event" property="event" jdbcType="INTEGER"/>
            <result column="event_desc" property="eventDesc" jdbcType="VARCHAR"/>
            <result column="event_time" property="eventTime" jdbcType="TIMESTAMP"/>
        </collection>
    </resultMap>

    <select id="getCarrierOrderDetailForWebById" resultMap="getCarrierOrderDetailByIdMap">
        select
        tco.id,
        tco.carrier_order_code,
        tco.customer_order_code,
        tco.demand_order_id,
        tco.status,
        tco.if_cancel,
        tco.cancel_reason,
        tco.if_empty,
        tco.dispatch_user_name,
        tco.load_time,
        tco.unload_time,
        tco.remark,
        tco.goods_unit,
        tco.expect_amount,
        tco.load_amount,
        tco.unload_amount,
        tco.sign_amount,
        tco.company_entrust_name,
        tco.carrier_settle_statement_status,

        tcoa.load_province_name,
        tcoa.load_city_name,
        tcoa.load_area_name,
        tcoa.load_detail_address,
        tcoa.load_warehouse,
        tcoa.unload_province_name,
        tcoa.unload_city_name,
        tcoa.unload_area_name,
        tcoa.unload_detail_address,
        tcoa.unload_warehouse,
        tcoa.consignor_name,
        tcoa.receiver_name,
        tcoa.consignor_mobile                                                                                                      as consignor_mobile,
        tcoa.receiver_mobile                                                                                                       as receiver_mobile,

        tcog.id                                                                                                                    as goodsId,
        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount                                                                                                         AS g_expect_amount,
        tcog.load_amount                                                                                                           AS g_load_amount,
        tcog.unload_amount                                                                                                         AS g_unload_amount,
        tcog.sign_amount                                                                                                           AS g_sign_amount,

        tcovh.id                                                                                                                   as vehicleHistoryId,
        tcovh.vehicle_no,
        tcovh.driver_name,
        tcovh.if_invalid,
        tcovh.audit_status,
        tcovh.remark                                                                                                               as vehicleHistoryRemark,
        tcovh.reject_reason,
        tcovh.created_time                                                                                                         as vehicleHistoryCreateTime,
        tcovh.driver_mobile                                                                                                        as driver_mobile,

        tcoe.id                                                                                                                    as eventId,
        tcoe.event,
        tcoe.event_desc,
        tcoe.event_time
        from t_carrier_order tco
        LEFT JOIN t_carrier_order_goods tcog ON tco.id = tcog.carrier_order_id and tcog.valid = 1
        LEFT JOIN t_carrier_order_vehicle_history tcovh on tco.id = tcovh.carrier_order_id and tcovh.valid = 1
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        left join t_carrier_order_events tcoe on tcoe.valid = 1 and tcoe.carrier_order_id = tco.id
        where tco.valid = 1
        and tco.id = #{carrierOrderId,jdbcType=BIGINT}
        and tco.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
    </select>

    <select id="selectVehicleHistoryByCarrierOrderId" resultType="com.logistics.tms.controller.carrierorder.response.ViewCarrierOrderTicketsResponseModel">
        select
        tco.id                                                                                                                   as carrierOrderId,
        tco.carrier_order_code                                                                                                   as carrierOrderCode,
        tcovh.vehicle_no                                                                                                         as vehicleNo,
        tcovh.driver_name                                                                                                        as driverName,
        tcovh.driver_mobile                                                                                                      as driverMobile
        from t_carrier_order tco
        left join t_carrier_order_vehicle_history tcovh on tcovh.carrier_order_id = tco.id and tcovh.if_invalid = 1 and tcovh.valid = 1
        where tco.valid = 1
        and tco.id = #{carrierOrderId,jdbcType=BIGINT}
    </select>
    <select id="getCarrierOrderStatusByDemandOrderIds" resultMap="getByDemandOrderIds_Map">
        select
        id,
        demand_order_id,
        status,
        if_cancel,
        if_empty
        from t_carrier_order
        where valid = 1
        and demand_order_id in (${demandOrderIds})
    </select>

    <select id="getCountByDriverId" resultType="int">
        select count(*)
        from t_carrier_order tco
        left join t_carrier_order_vehicle_history tcovh on tcovh.carrier_order_id = tco.id and tcovh.if_invalid = 1 and tcovh.valid = 1
        left join t_carrier_driver_relation tcdr on tcdr.valid = 1 and tcdr.driver_id = tcovh.driver_id and tco.company_carrier_id = tcdr.company_carrier_id
        where tco.valid = 1
        and tcovh.driver_id = #{driverId,jdbcType = BIGINT}
        and tco.if_cancel = 0
        and tco.if_empty = 0
        and tcdr.enabled = 1
        and tco.company_carrier_id in (${companyCarrierIds})
        <if test="status != null and status.size() != 0">
            and tco.status in
            <foreach collection="status" item="item" open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="needExcludedOrderIds != null and needExcludedOrderIds.size() != 0">
            and tco.id not in
            <foreach collection="needExcludedOrderIds" open="(" separator="," close=")" item="item">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="status == null">
            and false
        </if>
    </select>

    <select id="searchCarrierOrderIdsForYeloLife" resultType="java.lang.Long">
        select
        DISTINCT tco.id
        from t_carrier_order tco
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        LEFT JOIN t_carrier_order_goods tcog on tcog.valid = 1 and tcog.carrier_order_id = tco.id
        where tco.valid= 1 and tco.demand_order_source = 5
        <if test="condition.entrustType!=null ">
            and tco.demand_order_entrust_type = #{condition.entrustType,jdbcType=INTEGER}
        </if>
        <if test="condition.outStatus != null">
            and tco.out_status = #{condition.outStatus,jdbcType=INTEGER}
            and tco.demand_order_entrust_type = 101
        </if>
        <if test="condition.carrierOrderIds!=null and condition.carrierOrderIds!=''">
            and tco.id in (${condition.carrierOrderIds})
        </if>
        <if test="condition.status != null">
            <choose>
                <when test="condition.status==0">
                    and tco.if_cancel = 1
                </when>
                <otherwise>
                    and tco.status = #{condition.status,jdbcType=VARCHAR} and tco.if_cancel = 0 and tco.if_empty = 0
                </otherwise>
            </choose>
        </if>
        <if test="condition.carrierOrderCode !=null and condition.carrierOrderCode != ''">
            and (INSTR(tco.carrier_order_code,#{condition.carrierOrderCode,jdbcType=VARCHAR})>0
            or INSTR(REPLACE(tco.carrier_order_code,'-',''),#{condition.carrierOrderCode,jdbcType=VARCHAR})>0)
        </if>
        <if test="condition.customerOrderCode !=null and condition.customerOrderCode != ''">
            and instr(tco.customer_order_code,#{condition.customerOrderCode,jdbcType=VARCHAR})>0
        </if>
        <if test="condition.customName != null and condition.customName != ''">
            and
            (IF(tco.business_type = 1, instr(tco.customer_name,#{condition.customName,jdbcType=VARCHAR}) > 0, (instr(tco.customer_user_name,#{condition.customName,jdbcType=VARCHAR}) > 0 or
            instr(AES_DECRYPT(UNHEX(tco.customer_user_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'),#{condition.customName,jdbcType=VARCHAR}) > 0 )))
        </if>
        <if test="condition.customerOrderSource!=null">
            and tco.customer_order_source = #{condition.customerOrderSource,jdbcType=INTEGER}
        </if>
        <if test="condition.loadAddress!=null and condition.loadAddress!=''">
            and instr(concat(tcoa.load_province_name,tcoa.load_city_name,tcoa.load_area_name,tcoa.load_detail_address,tcoa.load_warehouse),#{condition.loadAddress,jdbcType=VARCHAR})
        </if>
        <if test="condition.loadWareHouse!=null and condition.loadWareHouse!=''">
            and instr(tcoa.load_warehouse,#{condition.loadWareHouse,jdbcType=VARCHAR})
        </if>
        <if test="condition.unloadAddress!=null and condition.unloadAddress!=''">
            and instr(concat(tcoa.unload_province_name,tcoa.unload_city_name,tcoa.unload_area_name,tcoa.unload_detail_address,tcoa.unload_warehouse),#{condition.unloadAddress,jdbcType=VARCHAR})
        </if>
        <if test="condition.unloadWareHouse!=null and condition.unloadWareHouse!=''">
            and instr(tcoa.unload_warehouse,#{condition.unloadWareHouse,jdbcType=VARCHAR})
        </if>
        <if test="condition.consignorName!=null and condition.consignorName!=''">
            and (instr(tcoa.consignor_name,#{condition.consignorName,jdbcType=VARCHAR})>0
            or instr(tcoa.consignor_mobile,#{condition.consignorName,jdbcType=VARCHAR})>0)
        </if>
        <if test="condition.receiverName!=null and condition.receiverName!=''">
            and (instr(tcoa.receiver_name,#{condition.receiverName,jdbcType=VARCHAR})>0
            or instr(tcoa.receiver_mobile,#{condition.receiverName,jdbcType=VARCHAR})>0)
        </if>
        <if test="condition.goodsName!=null and condition.goodsName!=''">
            and instr(tcog.goods_name,#{condition.goodsName,jdbcType=VARCHAR})>0
        </if>
        <!-- 需求单下单时间 -->
        <if test="condition.demandCreatedTimeFrom!=null and condition.demandCreatedTimeFrom!='' ">
            and tco.publish_time >= DATE_FORMAT(#{condition.demandCreatedTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="condition.demandCreatedTimeTo!=null and condition.demandCreatedTimeTo!='' ">
            and tco.publish_time &lt;= DATE_FORMAT(#{condition.demandCreatedTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="condition.carrierOrderIdList !=null and condition.carrierOrderIdList.size>0">
            and tco.id in (
            <foreach collection="condition.carrierOrderIdList" item="item"  separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
            )
        </if>
        <if test="condition.carrierOrderCodeList !=null and condition.carrierOrderCodeList.size>0">
            and tco.carrier_order_code in (
            <foreach collection="condition.carrierOrderCodeList" item="item"  separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
        <if test="condition.demandOrderCodeList!=null and condition.demandOrderCodeList.size>0">
            and tco.demand_order_code in (
            <foreach collection="condition.demandOrderCodeList" item="item"  separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
        <if test="condition.ifRecycleByCode != null">
            and tco.if_recycle_by_code = #{condition.ifRecycleByCode,jdbcType=INTEGER}
            and tco.demand_order_entrust_type = 100
        </if>
        order by tco.created_time desc,tco.id desc
    </select>

    <resultMap id="searchCarrierOrderForYeloLife_Map"  type="com.logistics.tms.controller.carrierorder.response.SearchCarrierOrderListForYeloLifeResponseModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="dispatch_user_name" property="dispatchUserName" jdbcType="VARCHAR"/>
        <result column="dispatch_time" property="dispatchTime" jdbcType="TIMESTAMP"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="expected_load_time" property="expectedLoadTime" jdbcType="INTEGER"/>
        <result column="expect_amount" property="expectAmount" jdbcType="INTEGER"/>
        <result column="load_amount" property="loadAmount" jdbcType="DECIMAL"/>
        <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
        <result column="sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="customer_order_source" property="customerOrderSource" jdbcType="INTEGER"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="business_type" property="businessType" jdbcType="INTEGER"/>
        <result column="customer_name" property="customName" jdbcType="VARCHAR"/>
        <result column="publish_name" property="publishName" jdbcType="VARCHAR"/>
        <result column="publish_mobile" property="publishMobile" jdbcType="VARCHAR"/>
        <result column="customer_user_name" property="customerUserName" jdbcType="VARCHAR"/>
        <result column="customer_user_mobile" property="customerUserMobile" jdbcType="VARCHAR"/>
        <result column="demand_order_entrust_type" property="entrustType" jdbcType="INTEGER"/>
        <result column="out_status" property="outStatus" jdbcType="INTEGER"/>
        <result column="if_recycle_by_code" property="ifRecycleByCode" jdbcType="INTEGER"/>

        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="company_carrier_name" property="companyCarrierName" jdbcType="VARCHAR"/>
        <result column="company_carrier_type" property="companyCarrierType" jdbcType="INTEGER"/>
        <result column="carrier_contact_name" property="carrierContactName" jdbcType="VARCHAR"/>
        <result column="carrier_contact_phone" property="carrierContactPhone" jdbcType="VARCHAR"/>

        <collection property="goodsInfoList"
                    ofType="com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="g_expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="g_load_amount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="g_unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="g_sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>

    <select id="searchCarrierOrderForYeloLife" resultMap="searchCarrierOrderForYeloLife_Map" >
        select
        tco.id,
        tco.carrier_order_code,
        tco.customer_order_code,
        tco.status,
        tco.dispatch_time,
        tco.expect_amount,
        tco.load_amount,
        tco.unload_amount,
        tco.sign_amount,
        tco.demand_order_id,
        tco.demand_order_code,
        tco.remark,
        tco.dispatch_user_name,
        tco.customer_order_source,
        tco.if_cancel,
        tco.goods_unit,
        tco.business_type,
        tco.customer_name,
        tco.publish_name,
        AES_DECRYPT(UNHEX(tco.publish_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as publish_mobile,
        tco.customer_user_name,
        AES_DECRYPT(UNHEX(tco.customer_user_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as customer_user_mobile,
        tco.demand_order_entrust_type,
        tco.company_carrier_id,
        tco.company_carrier_name,
        tco.company_carrier_type,
        tco.carrier_contact_name,
        AES_DECRYPT(UNHEX(tco.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrier_contact_phone,
        tco.out_status,
        tco.if_recycle_by_code,


        tcoa.load_province_name,
        tcoa.load_city_name,
        tcoa.load_area_name,
        tcoa.load_detail_address,
        tcoa.load_warehouse,
        tcoa.expected_load_time,
        tcoa.unload_province_name,
        tcoa.unload_city_name,
        tcoa.unload_area_name,
        tcoa.unload_detail_address,
        tcoa.unload_warehouse,

        tcog.id as goodsId,
        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount AS g_expect_amount,
        tcog.load_amount AS g_load_amount,
        tcog.unload_amount AS g_unload_amount,
        tcog.sign_amount AS g_sign_amount

        from t_carrier_order tco
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        LEFT JOIN t_carrier_order_goods tcog on tcog.valid = 1 and tcog.carrier_order_id = tco.id
        where tco.valid= 1
        and tco.id in (${carrierOrderIds})
        order by tco.created_time desc,tco.id desc
    </select>

    <resultMap id="searchCarrierOrderIdsStatisticsForYeloLife_Map" type="com.logistics.tms.biz.carrierorder.model.SearchListStatisticsForYeloLifeModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
    </resultMap>

    <select id="searchCarrierOrderIdsStatisticsForYeloLife" resultMap="searchCarrierOrderIdsStatisticsForYeloLife_Map">
        select
        tco.id,
        tco.status,
        tco.if_cancel
        from t_carrier_order tco
        where tco.valid= 1 and tco.demand_order_source = 5
        <if test="carrierOrderIdList !=null and carrierOrderIdList.size>0">
            and tco.id in (
            <foreach collection="carrierOrderIdList" item="item"  separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
            )
        </if>
    </select>

    <resultMap id="getCarrierOrderDetailForYeloLifeById_Map" type="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailForYeloLifeResponseModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="dispatch_user_name" property="dispatchUserName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="dispatch_time" property="dispatchTime" jdbcType="TIMESTAMP"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="load_amount" property="loadAmount" jdbcType="DECIMAL"/>
        <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
        <result column="sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="company_carrier_name" property="companyCarrierName" jdbcType="VARCHAR"/>
        <result column="company_carrier_type" property="companyCarrierType" jdbcType="INTEGER"/>
        <result column="carrier_contact_name" property="carrierContactName" jdbcType="VARCHAR"/>
        <result column="carrier_contact_phone" property="carrierContactPhone" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="INTEGER"/>
        <result column="publish_name" property="publishName" jdbcType="VARCHAR"/>
        <result column="publish_mobile" property="publishMobile" jdbcType="VARCHAR"/>
        <result column="customer_user_name" property="customerUserName" jdbcType="VARCHAR"/>
        <result column="customer_user_mobile" property="customerUserMobile" jdbcType="VARCHAR"/>
        <result column="settlement_tonnage" property="entrustSettlementTonnage" jdbcType="INTEGER"/>
        <result column="carrier_settlement" property="carrierSettlementTonnage" jdbcType="INTEGER"/>
        <result column="carrier_settle_statement_status" property="carrierSettleStatementStatus" jdbcType="INTEGER"/>
        <association property="carrierOrderDetailBasicInfo" resultMap="carrierOrderDetailBasicInfoForManagementMap"/>

        <association property="carrierOrderDetailFreightFeeInfo" resultMap="carrierOrderDetailFreightFeeInfoForManagementMap"/>

        <collection property="carrierOrderDetailGoodsInfo" ofType="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailGoodsInfoModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="g_expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="g_load_amount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="g_unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="g_sign_amount" property="signAmount" jdbcType="DECIMAL"/>

        </collection>
        <collection property="carrierOrderDetailVehicleDriverInfo" ofType="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailVehicleDriverInfoModel">
            <id column="vehicleHistoryId" property="vehicleHistoryId" jdbcType="BIGINT"/>
            <result column="audit_status" property="auditStatus" jdbcType="INTEGER"/>
            <result column="if_invalid" property="ifInvalid" jdbcType="INTEGER"/>
            <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
            <result column="driver_name" property="driverName" jdbcType="VARCHAR"/>
            <result column="driver_mobile" property="driverMobile" jdbcType="VARCHAR"/>
            <result column="reject_reason" property="rejectReason" jdbcType="VARCHAR"/>
            <result column="vehicleHistoryRemark" property="remark" jdbcType="VARCHAR"/>
            <result column="vehicleHistoryCreateTime" property="createdTime" jdbcType="TIMESTAMP"/>
        </collection>
    </resultMap>

    <select id="getCarrierOrderDetailForYeloLifeById" resultMap="getCarrierOrderDetailForYeloLifeById_Map">
        select
        tco.id,
        tco.carrier_order_code,
        tco.customer_order_code,
        tco.demand_order_id,
        tco.demand_order_code,
        tco.customer_name,
        tco.status,
        tco.if_cancel,
        tco.dispatch_user_name,
        tco.dispatch_time,
        tco.load_time,
        tco.unload_time,
        tco.remark,
        tco.dispatch_freight_fee_type,
        tco.dispatch_freight_fee,
        tco.sign_freight_fee,
        tco.adjust_fee,
        tco.markup_fee,
        tco.expect_entrust_freight,
        tco.expect_entrust_freight_type,
        tco.entrust_freight_type,
        tco.entrust_freight,
        tco.carrier_price_type,
        tco.carrier_price,
        tco.goods_unit,
        tco.expect_amount,
        tco.load_amount,
        tco.unload_amount,
        tco.sign_amount,
        tco.business_type,
        tco.publish_name,
        AES_DECRYPT(UNHEX(tco.publish_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as publish_mobile,
        tco.customer_user_name,
        AES_DECRYPT(UNHEX(tco.customer_user_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as customer_user_mobile,
        tco.settlement_tonnage,
        tco.carrier_settlement,
        tco.company_carrier_id,
        tco.company_carrier_name,
        tco.company_carrier_type,
        tco.carrier_contact_name,
        AES_DECRYPT(UNHEX(tco.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrier_contact_phone,
        tco.carrier_settle_statement_status,

        tcoa.load_province_name,
        tcoa.load_city_name,
        tcoa.load_area_name,
        tcoa.load_detail_address,
        tcoa.load_warehouse,
        tcoa.unload_province_name,
        tcoa.unload_city_name,
        tcoa.unload_area_name,
        tcoa.unload_detail_address,
        tcoa.unload_warehouse,
        tcoa.consignor_name,
        tcoa.consignor_mobile,
        tcoa.receiver_name,
        tcoa.receiver_mobile,

        tcog.id as goodsId,
        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount AS g_expect_amount,
        tcog.load_amount AS g_load_amount,
        tcog.unload_amount AS g_unload_amount,
        tcog.sign_amount AS g_sign_amount,

        tcovh.id as  vehicleHistoryId,
        tcovh.vehicle_no,
        tcovh.driver_name,
        tcovh.driver_mobile,
        tcovh.if_invalid,
        tcovh.audit_status,
        tcovh.remark as vehicleHistoryRemark,
        tcovh.reject_reason,
        tcovh.created_time as vehicleHistoryCreateTime

        from t_carrier_order tco
        LEFT JOIN t_carrier_order_goods tcog ON tco.id = tcog.carrier_order_id and tcog.valid= 1
        LEFT JOIN t_carrier_order_vehicle_history tcovh on tco.id = tcovh.carrier_order_id and tcovh.valid= 1
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        where tco.valid = 1 and tco.id = #{carrierOrderId,jdbcType=BIGINT}
    </select>

    <resultMap id="getLoadDetailForYeloLifeByIds_Map" type="com.logistics.tms.controller.carrierorder.response.LoadDetailForYeloLifeResponseModel">
        <result column="carrierOrderId" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="carrierOrderCode" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="vehicleNo" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="driverName" property="driverName" jdbcType="VARCHAR"/>
        <result column="driverMobile" property="driverMobile" jdbcType="VARCHAR"/>
        <result column="loadProvinceName" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="loadCityName" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="loadAreaName" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="loadDetailAddress" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="loadWarehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="consignor_name" property="consignorName" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR"/>
        <result column="unloadProvinceName" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unloadCityName" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unloadAreaName" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unloadDetailAddress" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unloadWarehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="business_type" property="businessType" jdbcType="INTEGER"/>
        <collection property="goodsList" ofType="com.logistics.tms.controller.carrierorder.response.GetLoadDetailForYeloLifeGoodModel">
            <result column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="expectAmount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="loadAmount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="unLoadAmount" property="unLoadAmount" jdbcType="DECIMAL"/>
            <result column="goodsName" property="goodsName" jdbcType="VARCHAR"/>

            <collection property="codeDtoList" ofType="com.logistics.tms.controller.carrierorder.response.GetLoadDetailForYeliLifeCodeModel">
                <result column="carrierOrderGoodsCodeId" property="carrierOrderGoodsCodeId" jdbcType="BIGINT"/>
                <result column="yeloCode" property="yeloCode" jdbcType="VARCHAR"/>
                <result column="carrierOrderGoodsCodeLoadAmount" property="loadAmount" jdbcType="DECIMAL"/>
                <result column="carrierOrderGoodsCodeUnloadAmount" property="unloadAmount" jdbcType="DECIMAL"/>
                <result column="carrierOrderGoodsCodeUnit" property="unit" jdbcType="INTEGER"/>
            </collection>
        </collection>
    </resultMap>

    <select id="getLoadDetailForYeloLifeByIds"  resultMap="getLoadDetailForYeloLifeByIds_Map"  >
        select
        tco.id                     as carrierOrderId,
        tco.carrier_order_code     as carrierOrderCode,
        tco.goods_unit,
        tco.business_type,

        tcovh.vehicle_no           as vehicleNo,
        tcovh.driver_name          as driverName,
        tcovh.driver_mobile        as driverMobile,

        tcoa.load_province_name    as loadProvinceName,
        tcoa.load_city_name        as loadCityName,
        tcoa.load_area_name        as loadAreaName,
        tcoa.load_detail_address   as loadDetailAddress,
        tcoa.load_warehouse        as loadWarehouse,
        tcoa.consignor_name,
        tcoa.consignor_mobile,
        tcoa.unload_province_name  as unloadProvinceName,
        tcoa.unload_city_name      as unloadCityName,
        tcoa.unload_area_name      as unloadAreaName,
        tcoa.unload_detail_address as unloadDetailAddress,
        tcoa.unload_warehouse      as unloadWarehouse,
        tcoa.receiver_name,
        tcoa.receiver_mobile,

        tcog.id                    as goodsId,
        tcog.expect_amount         as expectAmount,
        tcog.load_amount           as loadAmount,
        tcog.unload_amount         as unLoadAmount,
        tcog.goods_name            as goodsName,


        tcogc.id as carrierOrderGoodsCodeId,
        tcogc.load_amount as carrierOrderGoodsCodeLoadAmount,
        tcogc.unload_amount as carrierOrderGoodsCodeUnloadAmount,
        tcogc.yelo_good_code as yeloCode,
        tcogc.unit as carrierOrderGoodsCodeUnit

        from t_carrier_order tco
        left join t_carrier_order_vehicle_history tcovh on tco.id = tcovh.carrier_order_id and tcovh.if_invalid = 1 and tcovh.valid = 1
        left join t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        left join t_carrier_order_goods tcog on tcog.valid = 1 and tcog.carrier_order_id = tco.id
        left join t_carrier_order_goods_code tcogc on tcogc.valid = 1 and tcogc.carrier_order_goods_id = tcog.id
        where tco.valid = 1
        and tco.id = #{carrierOrderId}
    </select>

    <select id="searchYeloLifeCarrierOrderId" resultType="java.lang.Long">
        select
        DISTINCT tco.id
        from t_carrier_order tco
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        where tco.valid= 1 and tco.demand_order_source = 5 and tco.business_type != 0
        <if test="condition.status != null">
            <choose>
                <when test="condition.status==0">
                    and tco.if_cancel = 1
                </when>
                <otherwise>
                    and tco.status = #{condition.status,jdbcType=VARCHAR} and tco.if_cancel = 0 and tco.if_empty = 0
                </otherwise>
            </choose>
        </if>
        <if test="condition.carrierOrderCode !=null and condition.carrierOrderCode != ''">
            and (INSTR(tco.carrier_order_code,#{condition.carrierOrderCode,jdbcType=VARCHAR})>0
            or INSTR(REPLACE(tco.carrier_order_code,'-',''),#{condition.carrierOrderCode,jdbcType=VARCHAR})>0)
        </if>
        <if test="condition.demandOrderCode !=null and condition.demandOrderCode != ''">
            and INSTR(tco.demand_order_code,#{condition.demandOrderCode,jdbcType=VARCHAR})>0
        </if>
        <if test="condition.recycleOrderCode !=null and condition.recycleOrderCode != ''">
            and INSTR(tco.customer_order_code,#{condition.recycleOrderCode,jdbcType=VARCHAR})>0
        </if>
        <if test="condition.loadAddress!=null and condition.loadAddress!=''">
            and instr(concat(tcoa.load_province_name,tcoa.load_city_name,tcoa.load_area_name,tcoa.load_detail_address,tcoa.load_warehouse),#{condition.loadAddress,jdbcType=VARCHAR})
        </if>
        <if test="condition.unloadWareHouse!=null and condition.unloadWareHouse!=''">
            and instr(tcoa.unload_warehouse,#{condition.unloadWareHouse,jdbcType=VARCHAR})>0
        </if>
        <if test="condition.loadTimeFrom!=null and condition.loadTimeFrom!='' ">
            and tco.load_time >= DATE_FORMAT(#{condition.loadTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="condition.loadTimeTo!=null and condition.loadTimeTo!='' ">
            and tco.load_time &lt;= DATE_FORMAT(#{condition.loadTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="condition.publishName != null and condition.publishName != ''">
            and (instr(tco.publish_name, #{condition.publishName,jdbcType=VARCHAR}) > 0 or
            instr(AES_DECRYPT(UNHEX(tco.publish_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'), #{condition.publishName,jdbcType=VARCHAR}) > 0)
        </if>
        <if test="condition.carrierOrderIdList !=null and condition.carrierOrderIdList.size>0">
            and tco.id in (
            <foreach collection="condition.carrierOrderIdList" item="item"  separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
            )
        </if>
        <if test="condition.provinceIdList != null and condition.provinceIdList.size() != 0">
            and tcoa.load_province_id in
            <foreach collection="condition.provinceIdList" item="provinceId" close=")" open="(" index="index" separator=",">
                #{provinceId,jdbcType=BIGINT}
            </foreach>
        </if>
        order by tco.created_time desc,tco.id desc
    </select>

    <resultMap id="searchYeloLifeCarrierOrder_map" type="com.logistics.tms.controller.carrierorder.response.SearchYeloLifeCarrierOrderResponseModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="business_type" property="businessType" jdbcType="INTEGER"/>
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="recycleOrderCode" jdbcType="VARCHAR"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="load_time" property="loadTime" jdbcType="TIMESTAMP"/>
        <result column="unload_time" property="unloadTime" jdbcType="TIMESTAMP"/>
        <result column="customer_name" property="customName" jdbcType="VARCHAR"/>
        <result column="publish_name" property="publishName" jdbcType="VARCHAR"/>
        <result column="publish_mobile" property="publishMobile" jdbcType="VARCHAR"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="customer_user_name" property="customerUserName" jdbcType="VARCHAR"/>
        <result column="customer_user_mobile" property="customerUserMobile" jdbcType="VARCHAR"/>
        <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
        <result column="load_amount" property="loadAmount" jdbcType="DECIMAL"/>
        <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
        <result column="sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        <result column="settlement_tonnage" property="entrustSettlementTonnage" jdbcType="INTEGER"/>
        <result column="entrust_freight_type" property="entrustFreightType" jdbcType="INTEGER"/>
        <result column="entrust_freight" property="entrustFreight" jdbcType="DECIMAL"/>
    </resultMap>

    <select id="searchYeloLifeCarrierOrder" resultMap="searchYeloLifeCarrierOrder_map">
        select
        tco.id,
        tco.carrier_order_code,
        tco.status,
        tco.load_time,
        tco.unload_time,
        tco.expect_amount,
        tco.load_amount,
        tco.unload_amount,
        tco.sign_amount,
        tco.entrust_freight_type,
        tco.entrust_freight,
        tco.settlement_tonnage,
        tco.demand_order_id,
        tco.demand_order_code,
        tco.customer_order_code,
        tco.if_cancel,
        tco.business_type,
        tco.publish_name,
        AES_DECRYPT(UNHEX(tco.publish_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as publish_mobile,
        tco.customer_name,
        tco.goods_unit,
        tco.customer_user_name,
        AES_DECRYPT(UNHEX(tco.customer_user_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as customer_user_mobile,

        tcoa.load_province_name,
        tcoa.load_city_name,
        tcoa.load_area_name,
        tcoa.load_detail_address,
        tcoa.load_warehouse,
        tcoa.unload_province_name,
        tcoa.unload_city_name,
        tcoa.unload_area_name,
        tcoa.unload_detail_address,
        tcoa.unload_warehouse

        from t_carrier_order tco
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        where tco.valid = 1
        and tco.id in (${carrierOrderIds})
        order by tco.created_time desc, tco.id desc
    </select>

    <resultMap id="searchYeloLifeCarrierOrderDetail_map" type="com.logistics.tms.controller.carrierorder.response.SearchYeloLifeCarrierOrderDetailResponseModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="business_type" property="businessType" jdbcType="INTEGER"/>
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="recycleOrderCode" jdbcType="VARCHAR"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="consignor_name" property="consignorName" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR"/>
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR"/>
        <result column="load_time" property="loadTime" jdbcType="TIMESTAMP"/>
        <result column="customer_name" property="customName" jdbcType="VARCHAR"/>
        <result column="publish_name" property="publishName" jdbcType="VARCHAR"/>
        <result column="publish_mobile" property="publishMobile" jdbcType="VARCHAR"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="customer_user_name" property="customerUserName" jdbcType="VARCHAR"/>
        <result column="customer_user_mobile" property="customerUserMobile" jdbcType="VARCHAR"/>
        <result column="if_recycle_by_code" property="ifRecycleByCode" jdbcType="INTEGER"/>


        <collection property="goodsList" ofType="com.logistics.tms.controller.carrierorder.response.SearchYeloLifeCarrierOrderGoodsModel">
            <id column="goodId" property="goodId" jdbcType="BIGINT"/>
            <result column="goods_name" property="skuName" jdbcType="VARCHAR"/>
            <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="load_amount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>

    <select id="searchYeloLifeCarrierOrderDetail" resultMap="searchYeloLifeCarrierOrderDetail_map">
        select
        tco.id,
        tco.carrier_order_code,
        tco.status,
        tco.load_time,
        tco.demand_order_id,
        tco.demand_order_code,
        tco.customer_order_code,
        tco.if_cancel,
        tco.business_type,
        tco.publish_name,
        AES_DECRYPT(UNHEX(tco.publish_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')    as publish_mobile,
        tco.customer_name,
        tco.remark,
        tco.goods_unit,
        tco.customer_user_name,
        tco.customer_user_name,
        tco.if_recycle_by_code,


        AES_DECRYPT(UNHEX(tco.customer_user_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as customer_user_mobile,

        tcoa.load_province_name,
        tcoa.load_city_name,
        tcoa.load_area_name,
        tcoa.load_detail_address,
        tcoa.load_warehouse,
        tcoa.consignor_name,
        tcoa.consignor_mobile,
        tcoa.unload_province_name,
        tcoa.unload_city_name,
        tcoa.unload_area_name,
        tcoa.unload_detail_address,
        tcoa.unload_warehouse,
        tcoa.receiver_name,
        tcoa.receiver_mobile,

        tcog.id as goodId,
        tcog.goods_name,
        tcog.expect_amount,
        tcog.load_amount,
        tcog.unload_amount,
        tcog.sign_amount
        from t_carrier_order tco
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        LEFT JOIN t_carrier_order_goods tcog on tcog.valid = 1 and tcog.carrier_order_id = tco.id
        where tco.valid = 1
        and tco.id = #{carrierOrderId,jdbcType=BIGINT}
    </select>

    <resultMap id="carrierOrderListBeforeSignUpForYeloLife_map" type="com.logistics.tms.controller.carrierorder.response.SignDetailForYeloLifeResponseModel">
        <id column="id" property="carrierOrderId" jdbcType="BIGINT" />
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER" />
        <result column="dispatch_freight_fee_type" property="dispatchFreightFeeType" jdbcType="INTEGER"/>
        <result column="dispatch_freight_fee" property="dispatchFreightFee" jdbcType="DECIMAL"/>
        <result column="business_type" property="businessType" jdbcType="INTEGER"/>
        <result column="expect_amount" property="expectAmount"/>
        <result column="load_amount" property="loadAmount"/>
        <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER" />
        <result column="carrier_settlement" property="carrierSettlement"/>
        <result column="settlement_tonnage" property="settlementTonnage"/>
        <result column="carrier_price_type" property="carrierPriceType"/>
        <result column="carrier_price" property="carrierPrice"/>
        <result column="entrust_freight_type" property="entrustFreightType"/>
        <result column="entrust_freight" property="entrustFreight"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT" />
        <result column="demand_order_entrust_type" property="entrustType"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="consignor_name" property="consignorName" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR"/>

        <collection property="goodsList" ofType="com.logistics.tms.controller.carrierorder.response.CarrierOrderGoodsResponseModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL" />
            <result column="load_amount" property="loadAmount" jdbcType="DECIMAL" />
            <result column="gUnloadAmount" property="unLoadAmount" jdbcType="DECIMAL" />

                <collection property="codeDtoList" ofType="com.logistics.tms.controller.carrierorder.response.CarrierOrderGoodsCodeResponseModel">
                    <result column="tcogcYeloGoodCode" property="yeloCode" jdbcType="VARCHAR"/>
                    <result column="tcogcUnloadAmount" property="weight" jdbcType="DECIMAL"/>
                    <result column="tcogcUnit" property="unit" jdbcType="INTEGER" />
                </collection>
        </collection>
    </resultMap>

    <select id="carrierOrderListBeforeSignUpForYeloLife" resultMap="carrierOrderListBeforeSignUpForYeloLife_map">
        select
        tco.id as id,
        tco.status,
        tco.carrier_order_code,
        tco.if_cancel,
        tco.dispatch_freight_fee_type,
        tco.dispatch_freight_fee,
        tco.business_type,
        tco.expect_amount,
        tco.load_amount,
        tco.unload_amount,
        tco.settlement_tonnage,
        tco.carrier_settlement,
        tco.carrier_price_type,
        tco.carrier_price,
        tco.entrust_freight_type,
        tco.entrust_freight,
        tco.goods_unit,
        tco.company_carrier_id,
        tco.demand_order_entrust_type,

        tcoa.load_province_name,
        tcoa.load_city_name,
        tcoa.load_area_name,
        tcoa.load_detail_address,
        tcoa.load_warehouse,
        tcoa.consignor_name,
        tcoa.consignor_mobile,
        tcoa.unload_province_name,
        tcoa.unload_city_name,
        tcoa.unload_area_name,
        tcoa.unload_detail_address,
        tcoa.unload_warehouse,
        tcoa.receiver_name,
        tcoa.receiver_mobile,

        tcog.id as goodsId,
        tcog.goods_name,
        tcog.expect_amount,
        tcog.load_amount,
        tcog.unload_amount as gUnloadAmount,

        tcogc.yelo_good_code tcogcYeloGoodCode,
        tcogc.unload_amount tcogcUnloadAmount,
        tcogc.unit tcogcUnit

        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        left join t_carrier_order_goods tcog on tcog.valid = 1 and tcog.carrier_order_id = tco.id
        left join t_carrier_order_goods_code tcogc on tcogc.valid = 1 and tcogc.carrier_order_goods_id = tcog.id
        where tco.valid = 1
        and tco.id = #{carrierOrderId,jdbcType=BIGINT}
    </select>

    <select id="searchCarrierOrderForParams" resultType="com.logistics.tms.controller.carrierorder.response.SearchCarrierOrderForParamsResponseModel">
        select
        tco.id as carrierOrderId,
        tco.carrier_order_code as carrierOrderCode,
        tco.company_carrier_type as companyCarrierType,
        tco.company_carrier_id as companyCarrierId,
        tco.company_carrier_name as companyCarrierName,
        tco.carrier_contact_name as carrierContactName,
        AES_DECRYPT(UNHEX(tco.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactMobile,
        tco.project_label as projectLabel,
        tco.carrier_settle_statement_status as carrierSettleStatementStatus,

        tcoa.load_city_name as loadCityName,
        tcoa.unload_city_name as unloadCityName,

        tcovh.vehicle_no as vehicleNo,
        tcovh.driver_name as driverName,
        tcovh.driver_mobile as driverMobile
        from t_carrier_order tco
        LEFT JOIN t_carrier_order_vehicle_history tcovh on tco.id = tcovh.carrier_order_id and tcovh.valid = 1
        LEFT JOIN t_carrier_order_address tcoa on tcoa.valid = 1 and tcoa.carrier_order_id = tco.id
        where tco.valid = 1
        <if test="params.carrierOrderIds != null and params.carrierOrderIds != ''">
            and tco.id in (${params.carrierOrderIds})
        </if>
        <if test="params.carrierOrderCode != null and params.carrierOrderCode != ''">
            and instr(tco.carrier_order_code, #{params.carrierOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="params.projectLabel != null">
            and FIND_IN_SET(#{params.projectLabel,jdbcType=INTEGER}, tco.project_label)
        </if>
        <if test="params.companyCarrierId != null">
            and tco.company_carrier_id = #{params.companyCarrierId,jdbcType=BIGINT}
        </if>
        <if test="params.companyCarrierName != null and params.companyCarrierName !=''">
            and ((tco.company_carrier_type = 1 and instr(tco.company_carrier_name,#{params.companyCarrierName,jdbcType=VARCHAR}))
            or (tco.company_carrier_type = 2 and (instr(tco.carrier_contact_name,#{params.companyCarrierName,jdbcType=VARCHAR}) or instr(AES_DECRYPT(UNHEX(tco.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') ,#{params.companyCarrierName,jdbcType=VARCHAR}))))
        </if>
        <if test="params.vehicleNo != null and params.vehicleNo != ''">
            and instr(tcovh.vehicle_no, #{params.vehicleNo,jdbcType=VARCHAR})
        </if>
        <if test="params.driver != null and params.driver != ''">
            and (instr(tcovh.driver_name, #{params.driver,jdbcType=VARCHAR}) or instr(tcovh.driver_mobile, #{params.driver,jdbcType=VARCHAR}))
        </if>
        <if test="params.loadTimeStart != null and params.loadTimeStart != ''">
            and tco.load_time &gt;= DATE_FORMAT(#{params.loadTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="params.loadTimeEnd != null and params.loadTimeEnd != ''">
            and tco.load_time &lt;= DATE_FORMAT(#{params.loadTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.loadAddress != null and params.loadAddress != ''">
            and(
            instr(tcoa.load_province_name, #{params.loadAddress,jdbcType=VARCHAR}) or
            instr(tcoa.load_city_name, #{params.loadAddress,jdbcType=VARCHAR}) or
            instr(tcoa.load_area_name, #{params.loadAddress,jdbcType=VARCHAR}) or
            instr(tcoa.load_detail_address, #{params.loadAddress,jdbcType=VARCHAR}))
        </if>
        <if test="params.unloadAddress != null and params.unloadAddress != ''">
            and(
            instr(tcoa.unload_province_name, #{params.unloadAddress,jdbcType=VARCHAR}) or
            instr(tcoa.unload_city_name, #{params.unloadAddress,jdbcType=VARCHAR}) or
            instr(tcoa.unload_area_name, #{params.unloadAddress,jdbcType=VARCHAR}) or
            instr(tcoa.unload_detail_address, #{params.unloadAddress,jdbcType=VARCHAR}))
        </if>
        <if test="params.createdTimeStart != null and params.createdTimeStart != ''">
            and tco.created_time &gt;= DATE_FORMAT(#{params.createdTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="params.createdTimeEnd != null and params.createdTimeEnd != ''">
            and tco.created_time &lt;= DATE_FORMAT(#{params.createdTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.demandOrderEntrustType != null">
            and tco.demand_order_entrust_type = #{params.demandOrderEntrustType,jdbcType=INTEGER}
        </if>
        order by tco.id desc
    </select>

    <resultMap id="selectCarrierOrderSignDetailForLeyiMap" type="com.logistics.tms.controller.carrierorder.response.CarrierOrderListBeforeSignUpForLeyiResponseModel">
        <id column="id" property="carrierOrderId"/>
        <result column="carrier_order_code" property="carrierOrderCode"/>
        <result column="demand_order_entrust_type" property="entrustType"/>
        <result column="company_carrier_id" property="companyCarrierId"/>
        <result column="company_carrier_name" property="companyCarrierName"/>
        <result column="company_carrier_type" property="companyCarrierType"/>
        <result column="carrier_contact_name" property="carrierContactName"/>
        <result column="carrier_contact_phone" property="carrierContactMobile"/>
        <result column="load_city_name" property="loadCityName"/>
        <result column="load_area_id" property="loadAreaId"/>
        <result column="unload_city_name" property="unloadCityName"/>
        <result column="unload_area_id" property="unloadAreaId"/>
        <result column="goods_unit" property="goodsUnit"/>
        <result column="expect_amount" property="expectAmount"/>
        <result column="load_amount" property="loadAmount"/>
        <result column="unload_amount" property="unloadAmount"/>
        <result column="dispatch_freight_fee_type" property="dispatchFreightFeeType"/>
        <result column="dispatch_freight_fee" property="dispatchFreightFee"/>
        <result column="adjust_fee" property="adjustFee"/>
        <result column="markup_fee" property="markupFee"/>
        <result column="status" property="status"/>
        <result column="if_cancel" property="ifCancel"/>
        <result column="if_empty" property="ifEmpty"/>
        <result column="demand_order_source" property="demandOrderSource"/>
        <result column="demandOrderId" property="demandOrderId"/>
        <result column="demand_order_code" property="demandOrderCode"/>
        <result column="settlement_tonnage" property="settlementTonnage"/>
        <result column="carrier_settlement" property="carrierSettlement"/>
        <result column="expect_mileage" property="expectMileage"/>
        <result column="carrier_price_type" property="carrierPriceType"/>
        <result column="carrier_price" property="carrierPrice"/>
        <result column="bargaining_mode" property="bargainingMode"/>
        <result column="if_ext_carrier_order" property="ifExtCarrierOrder"/>
    </resultMap>

    <select id="selectCarrierOrderSignDetailForLeyi" resultMap="selectCarrierOrderSignDetailForLeyiMap">
        select
        tco.id as id,
        tco.carrier_order_code,
        tco.demand_order_id as demandOrderId,
        tco.demand_order_code,
        tco.status,
        tco.if_cancel,
        tco.if_empty,
        tco.goods_unit,
        tco.expect_amount,
        tco.load_amount,
        tco.unload_amount,
        tco.dispatch_freight_fee_type,
        tco.dispatch_freight_fee,
        tco.adjust_fee,
        tco.markup_fee,
        tco.company_carrier_id,
        tco.company_carrier_name,
        tco.company_carrier_type,
        tco.carrier_contact_name,
        AES_DECRYPT(UNHEX(tco.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrier_contact_phone,
        tco.demand_order_source,
        tco.demand_order_entrust_type,
        tco.settlement_tonnage,
        tco.carrier_settlement,
        tco.expect_mileage,
        tco.carrier_price_type,
        tco.carrier_price,
        tco.bargaining_mode,
        tco.if_ext_carrier_order,

        tcoa.load_city_name,
        tcoa.load_area_id,
        tcoa.unload_city_name,
        tcoa.unload_area_id
        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tco.id = tcoa.carrier_order_id and tcoa.valid = 1
        where tco.valid = 1
        <if test="carrierOrderIds != null and carrierOrderIds != ''">
            and tco.id in (${carrierOrderIds})
        </if>
        order by tco.created_time desc,tco.id desc
    </select>

    <select id="selectCarrierOrderSignDetailForYeloLife" resultType="com.logistics.tms.controller.carrierorder.response.CarrierOrderBeforeSignUpForYeloLifeResponseModel">
        select
        tco.id as carrierOrderId,
        tco.carrier_order_code as carrierOrderCode,
        tco.status,
        tco.if_cancel as ifCancel,
        tco.goods_unit as goodsUnit,
        tco.expect_amount as expectAmount,
        tco.load_amount as loadAmount,
        tco.unload_amount as unloadAmount,
        tco.dispatch_freight_fee_type as dispatchFreightFeeType,
        tco.dispatch_freight_fee as dispatchFreightFee,
        tco.adjust_fee as adjustFee,
        tco.markup_fee as markupFee,
        tco.company_carrier_id as companyCarrierId,
        tco.company_carrier_name as companyCarrierName,
        tco.company_carrier_type as companyCarrierType,
        tco.carrier_contact_name as carrierContactName,
        AES_DECRYPT(UNHEX(tco.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactMobile,
        tco.demand_order_source as demandOrderSource,
        tco.demand_order_entrust_type as entrustType,
        tco.settlement_tonnage as settlementTonnage,
        tco.carrier_settlement as carrierSettlement,
        tco.carrier_price_type as carrierPriceType,
        tco.carrier_price as carrierPrice,
        tco.entrust_freight_type as entrustFreightType,
        tco.entrust_freight as entrustFreight,

        tcoa.load_city_name as loadCityName,
        tcoa.unload_city_name as unloadCityName
        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tco.id = tcoa.carrier_order_id and tcoa.valid = 1
        where tco.valid = 1
        <if test="carrierOrderIds != null and carrierOrderIds != ''">
            and tco.id in (${carrierOrderIds})
        </if>
        order by tco.created_time desc,tco.id desc
    </select>

    <select id="getValidCarrierOrder" resultType="com.logistics.tms.controller.carrierorder.response.GetValidCarrierOrderResponseModel">
        select
        tco.id as carrierOrderId,
        tco.carrier_order_code as carrierOrderCode,
        tco.status,
        tco.goods_unit as goodsUnit,
        tco.expect_amount as expectAmount,
        tco.load_amount as loadAmount,
        tco.unload_amount as unloadAmount,
        tco.sign_amount as signAmount,
        tco.company_carrier_type as companyCarrierType,
        tco.company_carrier_id as companyCarrierId,
        tco.company_carrier_name as companyCarrierName,
        tco.carrier_contact_name as carrierContactName,
        AES_DECRYPT(UNHEX(tco.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactMobile,
        tco.loading_unloading_part as loadingUnloadingPart,
        tco.loading_unloading_charge as loadingUnloadingCharge,

        tcoa.load_city_name as loadCityName,
        tcoa.unload_city_name as unloadCityName,

        tcovh.driver_name as driverName,
        tcovh.driver_mobile as driverMobile,
        tcovh.vehicle_no as vehicleNo
        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tco.id = tcoa.carrier_order_id and tcoa.valid = 1
        left join t_carrier_order_vehicle_history tcovh on tco.id = tcovh.carrier_order_id and tcovh.if_invalid = 1 and tcovh.valid = 1
        where tco.valid = 1
        and tco.demand_order_source = 1
        and tco.company_carrier_id = #{params.companyCarrierId,jdbcType=BIGINT}
        and tco.if_cancel = 0
        and tco.if_empty = 0
        <if test="params.carrierOrderCode != null and params.carrierOrderCode != ''">
            and instr(tco.carrier_order_code, #{params.carrierOrderCode,jdbcType=VARCHAR})
        </if>
        order by tco.id desc
    </select>

    <select id="searchCarrierOrderForById" resultType="com.logistics.tms.controller.carrierorder.response.GetValidCarrierOrderResponseModel">
        select
        tco.id as carrierOrderId,
        tco.carrier_order_code as carrierOrderCode,
        tco.status,
        tco.goods_unit as goodsUnit,
        tco.expect_amount as expectAmount,
        tco.load_amount as loadAmount,
        tco.unload_amount as unloadAmount,
        tco.sign_amount as signAmount,
        tco.company_carrier_type as companyCarrierType,
        tco.company_carrier_id as companyCarrierId,
        tco.company_carrier_name as companyCarrierName,
        tco.carrier_contact_name as carrierContactName,
        AES_DECRYPT(UNHEX(tco.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactMobile,
        tco.loading_unloading_part as loadingUnloadingPart,
        tco.loading_unloading_charge as loadingUnloadingCharge,

        tcoa.load_city_name as loadCityName,
        tcoa.unload_city_name as unloadCityName,

        tcovh.driver_name as driverName,
        tcovh.driver_mobile as driverMobile,
        tcovh.vehicle_no as vehicleNo
        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tco.id = tcoa.carrier_order_id and tcoa.valid = 1
        left join t_carrier_order_vehicle_history tcovh on tco.id = tcovh.carrier_order_id and tcovh.if_invalid = 1 and tcovh.valid = 1
        where tco.valid = 1
        and tco.id = #{carrierOrderId}
    </select>

    <select id="getSpecifiedStateCount" resultType="java.lang.Integer">
        select
        count(tco.id)
        from t_carrier_order tco
        left join t_carrier_order_vehicle_history his on his.carrier_order_id = tco.id and his.valid = 1
        where tco.valid =1
        and tco.company_carrier_id = #{companyCarrierId,jdbcType = BIGINT}
        and tco.status&lt;50000
        and tco.if_cancel = 0
        and tco.if_empty = 0
        and his.if_invalid = 1
        <if test="driverIds != null">
            and his.driver_id in (${driverIds})
        </if>
        <if test="vehicleIds != null">
            and his.vehicle_id in (${vehicleIds})
        </if>
    </select>

    <select id="selectCarrierOrderIdByCode" resultType="java.lang.Long">
        select
        tco.id
        from t_carrier_order tco
        left join  t_carrier_order_vehicle_history tcovh  on tcovh.valid =1 and tcovh.if_invalid =1 and tcovh.carrier_order_id = tco.id
        where tco.valid = 1
        and tcovh.driver_id = #{driverId,jdbcType=BIGINT}
        and carrier_order_code = #{carrierOrderCode,jdbcType = VARCHAR}
    </select>

    <select id="selectCarrierWaitSettleStatementListIds" resultType="java.lang.Long">
        select DISTINCT tco.id
        from t_carrier_order tco
        left join t_carrier_order_vehicle_history tcovh on tco.id = tcovh.carrier_order_id and tcovh.valid = 1 and tcovh.if_invalid = 1
        left join t_carrier_order_address tcod on tcod.carrier_order_id = tco.id and tcod.valid = 1
        where tco.valid = 1
        and (tco.status = 60000 or tco.if_empty = 1)
        and tco.demand_order_source = 1
        <choose>
            <when test="carrierSettleStatementStatus != null">
                and tco.carrier_settle_statement_status = #{carrierSettleStatementStatus,jdbcType=INTEGER}
            </when>
            <otherwise>
                and tco.carrier_settle_statement_status in (-2, -3)
            </otherwise>
        </choose>
        <if test="companyCarrierName != null and companyCarrierName != ''">
            and (
            (tco.company_carrier_type = 1 and instr(tco.company_carrier_name, #{companyCarrierName,jdbcType=VARCHAR}))
            or (tco.company_carrier_type = 2 and (instr(tco.carrier_contact_name, #{companyCarrierName,jdbcType=VARCHAR})
            or instr(AES_DECRYPT(UNHEX(tco.carrier_contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'), #{companyCarrierName,jdbcType=VARCHAR})))
            )
        </if>
        <if test="companyEntrustName != null and companyEntrustName != ''">
            and instr(tco.company_entrust_name, #{companyEntrustName,jdbcType=VARCHAR})
        </if>
        <if test="carrierOrderCode != null and carrierOrderCode != ''">
            and instr(tco.carrier_order_code, #{carrierOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="dispatchOrderCode != null and dispatchOrderCode != ''">
            and instr(tco.dispatch_order_code, #{dispatchOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="carrierPriceType != null and carrierPriceType != ''">
            and tco.carrier_price_type = #{carrierPriceType,jdbcType=INTEGER}
        </if>
        <if test="driver != null and driver != ''">
            and (instr(tcovh.driver_name, #{driver,jdbcType=VARCHAR}) or
            instr(tcovh.driver_mobile, #{driver,jdbcType=VARCHAR}))
        </if>
        <if test="vehicleNo != null and vehicleNo != ''">
            and instr(tcovh.vehicle_no, #{vehicleNo,jdbcType=VARCHAR})
        </if>
        <if test="demandOrderCode != null and demandOrderCode != ''">
            and instr(tco.demand_order_code, #{demandOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="entrustType != null">
            and tco.demand_order_entrust_type = #{entrustType,jdbcType=INTEGER}
        </if>
        <if test="loadAddress != null and loadAddress != ''">
            and(
            instr(tcod.load_province_name, #{loadAddress,jdbcType=VARCHAR}) or
            instr(tcod.load_city_name, #{loadAddress,jdbcType=VARCHAR}) or
            instr(tcod.load_area_name, #{loadAddress,jdbcType=VARCHAR}) or
            instr(tcod.load_detail_address, #{loadAddress,jdbcType=VARCHAR}))
        </if>
        <if test="unloadAddress != null and unloadAddress != ''">
            and(
            instr(tcod.unload_province_name, #{unloadAddress,jdbcType=VARCHAR}) or
            instr(tcod.unload_city_name, #{unloadAddress,jdbcType=VARCHAR}) or
            instr(tcod.unload_area_name, #{unloadAddress,jdbcType=VARCHAR}) or
            instr(tcod.unload_detail_address, #{unloadAddress,jdbcType=VARCHAR}))
        </if>
        <if test="loadWarehouse != null and loadWarehouse != ''">
            and instr(tcod.load_warehouse, #{loadWarehouse,jdbcType=VARCHAR})
        </if>
        <if test="unloadWarehouse != null and unloadWarehouse != ''">
            and instr(tcod.unload_warehouse, #{unloadWarehouse,jdbcType=VARCHAR})
        </if>
        <if test="loadTimeStart != null and loadTimeStart != ''">
            and tco.load_time &gt;= DATE_FORMAT(#{loadTimeStart,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%S')
        </if>
        <if test="loadTimeEnd != null and loadTimeEnd != ''">
            and tco.load_time &lt;= DATE_FORMAT(#{loadTimeEnd,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="unloadTimeStart != null and unloadTimeStart != ''">
            and tco.unload_time &gt;= DATE_FORMAT(#{unloadTimeStart,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%S')
        </if>
        <if test="unloadTimeEnd != null and unloadTimeEnd != ''">
            and tco.unload_time &lt;= DATE_FORMAT(#{unloadTimeEnd,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="signTimeStart != null and signTimeStart != ''">
            and tco.sign_time &gt;= DATE_FORMAT(#{signTimeStart,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%S')
        </if>
        <if test="signTimeEnd != null and signTimeEnd != ''">
            and tco.sign_time &lt;= DATE_FORMAT(#{signTimeEnd,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="projectLabel != null">
            and FIND_IN_SET(#{projectLabel,jdbcType=INTEGER}, tco.project_label)
        </if>
        <if test="carrierOrderCodeList != null and carrierOrderCodeList.size > 0">
            and tco.carrier_order_code in
            <foreach collection="carrierOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="demandOrderCodeList != null and demandOrderCodeList.size > 0">
            and tco.demand_order_code in
            <foreach collection="demandOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="carrierOrderIds != null and carrierOrderIds != ''">
            and tco.id in (${carrierOrderIds})
        </if>
        <if test="companyCarrierId != null">
            and tco.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        </if>
        order by tco.last_modified_time desc, tco.id desc
    </select>

    <resultMap id="selectCarrierWaitSettleStatementListMap" type="com.logistics.tms.controller.settlestatement.packaging.response.CarrierWaitSettleStatementListResponseModel">
        <result column="carrierOrderId" property="carrierOrderId"/>
        <result column="carrierOrderCode" property="carrierOrderCode"/>
        <result column="demandOrderId" property="demandOrderId"/>
        <result column="demandOrderCode" property="demandOrderCode"/>
        <result column="dispatchOrderId" property="dispatchOrderId"/>
        <result column="dispatchOrderCode" property="dispatchOrderCode"/>
        <result column="companyCarrierId" property="companyCarrierId"/>
        <result column="companyCarrierType" property="companyCarrierType"/>
        <result column="companyCarrierName" property="companyCarrierName"/>
        <result column="carrierContactName" property="carrierContactName"/>
        <result column="carrierContactMobile" property="carrierContactMobile"/>
        <result column="companyEntrustName" property="companyEntrustName"/>
        <result column="vehicle_no" property="vehicleNo"/>
        <result column="driver_name" property="driverName"/>
        <result column="driver_mobile" property="driverPhone"/>
        <result column="carrierPriceType" property="carrierPriceType"/>
        <result column="carrier_price" property="carrierPrice"/>
        <result column="expectAmount" property="expectAmount"/>
        <result column="loadAmount" property="loadAmount"/>
        <result column="unloadAmount" property="unloadAmount"/>
        <result column="signAmount" property="signAmount"/>
        <result column="goodsUnit" property="goodsUnit"/>
        <result column="loadTime" property="loadTime"/>
        <result column="unloadTime" property="unloadTime"/>
        <result column="signTime" property="signTime"/>
        <result column="loadWarehouse" property="loadWarehouse"/>
        <result column="loadProvinceName" property="loadProvinceName"/>
        <result column="loadCityName" property="loadCityName"/>
        <result column="loadAreaName" property="loadAreaName"/>
        <result column="loadDetailAddress" property="loadDetailAddress"/>
        <result column="consignorName" property="consignorName"/>
        <result column="consignorMobile" property="consignorMobile"/>
        <result column="unloadWarehouse" property="unloadWarehouse"/>
        <result column="unloadProvinceName" property="unloadProvinceName"/>
        <result column="unloadCityName" property="unloadCityName"/>
        <result column="unloadAreaName" property="unloadAreaName"/>
        <result column="unloadDetailAddress" property="unloadDetailAddress"/>
        <result column="receiverName" property="receiverName"/>
        <result column="receiverMobile" property="receiverMobile"/>
        <result column="dispatch_user_name" property="dispatchUserName"/>
        <result column="expectMileage" property="expectMileage"/>
        <result column="demandOrderEntrustType" property="entrustType"/>
        <result column="carrier_settlement" property="carrierSettlement"/>
        <result column="if_empty" property="ifEmpty"/>
        <result column="config_distance" property="configDistance"/>
        <result column="bargaining_mode" property="bargainingMode"/>
        <result column="carrier_settle_statement_status" property="carrierSettleStatementStatus"/>
    </resultMap>

    <select id="selectCarrierWaitSettleStatementList" resultMap="selectCarrierWaitSettleStatementListMap">
        select
        tco.id                                                                                                                         as carrierOrderId,
        tco.demand_order_id                                                                                                            as demandOrderId,
        tco.dispatch_order_id                                                                                                          as dispatchOrderId,
        tco.dispatch_order_code                                                                                                        as dispatchOrderCode,
        tco.carrier_order_code                                                                                                         as carrierOrderCode,
        tco.demand_order_code                                                                                                          as demandOrderCode,
        tco.company_entrust_name                                                                                                       as companyEntrustName,
        tco.company_carrier_id                                                                                                         as companyCarrierId,
        tco.company_carrier_name                                                                                                       as companyCarrierName,
        tco.company_carrier_type                                                                                                       as companyCarrierType,
        tco.carrier_contact_name                                                                                                       as carrierContactName,
        AES_DECRYPT(UNHEX(tco.carrier_contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactMobile,
        tco.load_time                                                                                                                  as loadTime,
        tco.unload_time                                                                                                                as unloadTime,
        tco.sign_time                                                                                                                  as signTime,
        tco.goods_unit                                                                                                                 as goodsUnit,
        tco.expect_amount                                                                                                              as expectAmount,
        tco.load_amount                                                                                                                as loadAmount,
        tco.unload_amount                                                                                                              as unloadAmount,
        tco.sign_amount                                                                                                                as signAmount,
        tco.carrier_price_type                                                                                                         as carrierPriceType,
        tco.carrier_price,
        tco.demand_order_entrust_type                                                                                                  as demandOrderEntrustType,
        tco.expect_mileage                                                                                                             as expectMileage,
        tco.dispatch_user_name,
        tco.carrier_settlement,
        tco.if_empty,
        tco.config_distance,
        tco.bargaining_mode,
        tco.carrier_settle_statement_status,

        tcoa.load_province_name                                                                                                        as loadProvinceName,
        tcoa.load_city_name                                                                                                            as loadCityName,
        tcoa.load_area_name                                                                                                            as loadAreaName,
        tcoa.load_detail_address                                                                                                       as loadDetailAddress,
        tcoa.load_warehouse                                                                                                            as loadWarehouse,
        tcoa.consignor_name                                                                                                            as consignorName,
        tcoa.consignor_mobile                                                                                                          as consignorMobile,
        tcoa.unload_province_name                                                                                                      as unloadProvinceName,
        tcoa.unload_city_name                                                                                                          as unloadCityName,
        tcoa.unload_area_name                                                                                                          as unloadAreaName,
        tcoa.unload_warehouse                                                                                                          as unloadWarehouse,
        tcoa.unload_detail_address                                                                                                     as unloadDetailAddress,
        tcoa.receiver_name                                                                                                             as receiverName,
        tcoa.receiver_mobile                                                                                                           as receiverMobile,

        tcovh.vehicle_no,
        tcovh.driver_name,
        tcovh.driver_mobile

        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tcoa.carrier_order_id = tco.id and tcoa.valid = 1
        left join t_carrier_order_vehicle_history tcovh on tcovh.valid = 1 and tcovh.if_invalid = 1 and tco.id = tcovh.carrier_order_id
        where tco.valid = 1
        and tco.id in (${ids})
        order by tco.last_modified_time desc, tco.id desc
    </select>

    <select id="selectCarrierWaitSettleStatementCarrierOrdersByIds" resultType="com.logistics.tms.biz.settlestatement.model.WaitSettleStatementCarrierOrderModel">
        select
        tco.company_carrier_id                                                                                                         as companyCarrierId,
        tco.company_carrier_name                                                                                                       as companyCarrierName,
        tco.carrier_contact_name                                                                                                       as carrierContactName,
        AES_DECRYPT(UNHEX(tco.carrier_contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactPhone,
        tco.company_carrier_type                                                                                                       as companyCarrierType,
        tco.carrier_order_code                                                                                                         as carrierOrderCode,
        tco.id                                                                                                                         as carrierOrderId,
        tco.load_time                                                                                                                  as loadTime,
        tco.sign_time                                                                                                                  as signTime,
        tco.goods_unit                                                                                                                 as goodsUnit,
        tco.company_entrust_id                                                                                                         as companyEntrustId,
        tco.company_entrust_name                                                                                                       as companyEntrustName,
        tco.expect_amount                                                                                                              as expectAmount,
        tco.load_amount                                                                                                                as loadAmount,
        tco.unload_amount                                                                                                              as unloadAmount,
        tco.sign_amount                                                                                                                as signAmount,
        tco.carrier_price_type                                                                                                         as carrierPriceType,
        tco.carrier_price                                                                                                              as carrierPrice,
        tco.carrier_settlement                                                                                                         as carrierSettlement,
        tco.if_empty                                                                                                                   as ifEmpty,
        tp.settlement_amount                                                                                                           as dbSettlementAmount,
        tp.settlement_cost_total                                                                                                       as dbSettlementCost,
        tp.price_type                                                                                                                  as dbCarrierPriceType
        from t_carrier_order tco
        left join t_payment tp on tp.valid = 1 and tco.id = tp.carrier_order_id
        where tco.valid = 1
        and (tco.status = 60000 or tco.if_empty = 1)
        and tco.carrier_settle_statement_status = -2
        and tco.id in (${ids})
        <if test="companyCarrierId != null">
            and tco.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        </if>
        limit 5000
    </select>

    <select id="selectSettleStatementCarrierOrder" resultType="com.logistics.tms.controller.settlestatement.packaging.response.CarrierSettleStatementDetailListResponseModel">
        select
        tco.id                                                                                                                         as carrierOrderId,
        tco.carrier_order_code                                                                                                         as carrierOrderCode,
        tco.demand_order_id                                                                                                            as demandOrderId,
        tco.demand_order_code                                                                                                          as demandOrderCode,
        tco.dispatch_order_id                                                                                                          as dispatchOrderId,
        tco.dispatch_order_code                                                                                                        as dispatchOrderCode,
        tco.demand_order_entrust_type                                                                                                  as entrustType,
        tco.company_carrier_id                                                                                                         as companyCarrierId,
        tco.company_carrier_type                                                                                                       as companyCarrierType,
        tco.company_carrier_name                                                                                                       as companyCarrierName,
        tco.carrier_contact_name                                                                                                       as carrierContactName,
        AES_DECRYPT(UNHEX(tco.carrier_contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactMobile,
        tco.company_entrust_name                                                                                                       as companyEntrustName,
        tco.carrier_price_type                                                                                                         as carrierPriceType,
        tco.carrier_price                                                                                                              as carrierPrice,
        tco.expect_amount                                                                                                              as expectAmount,
        tco.load_amount                                                                                                                as loadAmount,
        tco.unload_amount                                                                                                              as unloadAmount,
        tco.sign_amount                                                                                                                as signAmount,
        tco.load_time                                                                                                                  as loadTime,
        tco.unload_time                                                                                                                as unloadTime,
        tco.sign_time                                                                                                                  as signTime,
        tco.expect_mileage                                                                                                             as expectMileage,
        tco.dispatch_user_name                                                                                                         as dispatchUserName,
        tco.carrier_settlement                                                                                                         as carrierSettlement,
        tco.goods_unit                                                                                                                 as goodsUnit,
        tco.if_empty                                                                                                                   as ifEmpty,
        tco.config_distance                                                                                                            as configDistance,
        tco.bargaining_mode                                                                                                            as bargainingMode,

        tcoa.load_province_name                                                                                                        as loadProvinceName,
        tcoa.load_city_name                                                                                                            as loadCityName,
        tcoa.load_area_name                                                                                                            as loadAreaName,
        tcoa.load_detail_address                                                                                                       as loadDetailAddress,
        tcoa.load_warehouse                                                                                                            as loadWarehouse,
        tcoa.unload_province_name                                                                                                      as unloadProvinceName,
        tcoa.unload_city_name                                                                                                          as unloadCityName,
        tcoa.unload_area_name                                                                                                          as unloadAreaName,
        tcoa.unload_detail_address                                                                                                     as unloadDetailAddress,
        tcoa.unload_warehouse                                                                                                          as unloadWarehouse,
        tcoa.consignor_name                                                                                                            as consignorName,
        tcoa.consignor_mobile                                                                                                          as consignorMobile,
        tcoa.receiver_name                                                                                                             as receiverName,
        tcoa.receiver_mobile                                                                                                           as receiverMobile,

        tcovh.vehicle_no                                                                                                               as vehicleNo,
        tcovh.driver_name                                                                                                              as driverName,
        tcovh.driver_mobile                                                                                                            as driverPhone
        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tcoa.valid = 1 and tco.id = tcoa.carrier_order_id
        left join t_carrier_order_vehicle_history tcovh on tcovh.valid = 1 and tcovh.if_invalid = 1 and tco.id = tcovh.carrier_order_id
        where tco.valid = 1
        and tco.id in (${carrierOrderIdsStr})
        <if test="requestModel.carrierOrderCode != null and requestModel.carrierOrderCode != ''">
            and instr(tco.carrier_order_code,#{requestModel.carrierOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="requestModel.dispatchOrderCode != null and requestModel.dispatchOrderCode != ''">
            and instr(tco.dispatch_order_code,#{requestModel.dispatchOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="requestModel.demandOrderCode != null and requestModel.demandOrderCode != ''">
            and instr(tco.demand_order_code,#{requestModel.demandOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="requestModel.vehicleNumber != null and requestModel.vehicleNumber != ''">
            and instr(tcovh.vehicle_no,#{requestModel.vehicleNumber,jdbcType=VARCHAR})
        </if>
        <if test="requestModel.driverName!=null and requestModel.driverName!=''">
            and instr(concat(tcovh.driver_name,tcovh.driver_mobile),#{requestModel.driverName,jdbcType=VARCHAR})
        </if>
        <if test="requestModel.entrustType!=null ">
            and tco.demand_order_entrust_type = #{requestModel.entrustType,jdbcType=INTEGER}
        </if>
        <if test="requestModel.carrierPriceType!=null ">
            and tco.carrier_price_type = #{requestModel.carrierPriceType,jdbcType=INTEGER}
        </if>
        <if test="requestModel.loadAddress != null and requestModel.loadAddress != ''">
            and(
            instr(tcoa.load_province_name, #{requestModel.loadAddress,jdbcType=VARCHAR}) or
            instr(tcoa.load_city_name, #{requestModel.loadAddress,jdbcType=VARCHAR}) or
            instr(tcoa.load_area_name, #{requestModel.loadAddress,jdbcType=VARCHAR}) or
            instr(tcoa.load_detail_address, #{requestModel.loadAddress,jdbcType=VARCHAR}))
        </if>
        <if test="requestModel.unloadAddress != null and requestModel.unloadAddress != ''">
            and(
            instr(tcoa.unload_province_name, #{requestModel.unloadAddress,jdbcType=VARCHAR}) or
            instr(tcoa.unload_city_name, #{requestModel.unloadAddress,jdbcType=VARCHAR}) or
            instr(tcoa.unload_area_name, #{requestModel.unloadAddress,jdbcType=VARCHAR}) or
            instr(tcoa.unload_detail_address, #{requestModel.unloadAddress,jdbcType=VARCHAR}))
        </if>
        <if test="requestModel.loadWarehouse != null and requestModel.loadWarehouse != ''">
            and instr(tcoa.load_warehouse, #{requestModel.loadWarehouse,jdbcType=VARCHAR})
        </if>
        <if test="requestModel.unloadWareHouse != null and requestModel.unloadWareHouse != ''">
            and instr(tcoa.unload_warehouse, #{requestModel.unloadWareHouse,jdbcType=VARCHAR})
        </if>
        <if test="requestModel.loadTimeStart!=null and requestModel.loadTimeStart!=''">
            and tco.load_time &gt;= DATE_FORMAT(#{requestModel.loadTimeStart,jdbcType=VARCHAR},'%Y-%m-%d
            %H:%i:%S')
        </if>
        <if test="requestModel.loadTimeEnd!=null and requestModel.loadTimeEnd!=''">
            and tco.load_time &lt;= DATE_FORMAT(#{requestModel.loadTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d
            23:59:59')
        </if>
        <if test="requestModel.unloadTimeStart!=null and requestModel.unloadTimeStart!=''">
            and tco.unload_time &gt;= DATE_FORMAT(#{requestModel.unloadTimeStart,jdbcType=VARCHAR},'%Y-%m-%d
            %H:%i:%S')
        </if>
        <if test="requestModel.unloadTimeEnd!=null and requestModel.unloadTimeEnd!=''">
            and tco.unload_time &lt;= DATE_FORMAT(#{requestModel.unloadTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d
            23:59:59')
        </if>
        <if test="requestModel.signTimeStart!=null and requestModel.signTimeStart!=''">
            and tco.sign_time &gt;= DATE_FORMAT(#{requestModel.signTimeStart,jdbcType=VARCHAR},'%Y-%m-%d
            %H:%i:%S')
        </if>
        <if test="requestModel.signTimeEnd!=null and requestModel.signTimeEnd!=''">
            and tco.sign_time &lt;= DATE_FORMAT(#{requestModel.signTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d
            23:59:59')
        </if>
        <if test="requestModel.projectLabel != null">
            and FIND_IN_SET(#{requestModel.projectLabel,jdbcType=INTEGER}, tco.project_label)
        </if>
        <if test="requestModel.carrierOrderCodeList != null and requestModel.carrierOrderCodeList.size() > 0">
            and tco.carrier_order_code in
            <foreach collection="requestModel.carrierOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="requestModel.demandOrderCodeList != null and requestModel.demandOrderCodeList.size() > 0">
            and tco.demand_order_code in
            <foreach collection="requestModel.demandOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        order by tco.last_modified_time desc , tco.id desc
    </select>

    <select id="selectCarrierTraditionWaitSettleStatementListIds" resultType="java.lang.Long">
        select DISTINCT tco.id
        from t_carrier_order tco
        left join t_carrier_order_vehicle_history tcovh on tco.id = tcovh.carrier_order_id and tcovh.valid = 1 and tcovh.if_invalid = 1
        left join t_carrier_order_address tcod on tcod.carrier_order_id = tco.id and tcod.valid = 1
        where tco.valid = 1
        and tco.carrier_settle_statement_status = -2
        and tco.status = 60000
        and tco.demand_order_source in (2,3,4)
        <if test="companyCarrierName != null and companyCarrierName != ''">
            and (
            (tco.company_carrier_type = 1 and instr(tco.company_carrier_name, #{companyCarrierName,jdbcType=VARCHAR}))
            or (tco.company_carrier_type = 2 and (instr(tco.carrier_contact_name, #{companyCarrierName,jdbcType=VARCHAR})
            or instr(AES_DECRYPT(UNHEX(tco.carrier_contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'), #{companyCarrierName,jdbcType=VARCHAR})))
            )
        </if>
        <if test="companyEntrustName != null and companyEntrustName != ''">
            and instr(tco.company_entrust_name, #{companyEntrustName,jdbcType=VARCHAR})
        </if>
        <if test="carrierOrderCode != null and carrierOrderCode != ''">
            and instr(tco.carrier_order_code, #{carrierOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="dispatchOrderCode != null and dispatchOrderCode != ''">
            and instr(tco.dispatch_order_code, #{dispatchOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="carrierPriceType != null and carrierPriceType != ''">
            and tco.carrier_price_type = #{carrierPriceType,jdbcType=INTEGER}
        </if>
        <if test="driver != null and driver != ''">
            and (instr(tcovh.driver_name, #{driver,jdbcType=VARCHAR}) or
            instr(tcovh.driver_mobile, #{driver,jdbcType=VARCHAR}))
        </if>
        <if test="vehicleNo != null and vehicleNo != ''">
            and instr(tcovh.vehicle_no, #{vehicleNo,jdbcType=VARCHAR})
        </if>
        <if test="demandOrderCode != null and demandOrderCode != ''">
            and instr(tco.demand_order_code, #{demandOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="loadAddress != null and loadAddress != ''">
            and(
            instr(tcod.load_province_name, #{loadAddress,jdbcType=VARCHAR}) or
            instr(tcod.load_city_name, #{loadAddress,jdbcType=VARCHAR}) or
            instr(tcod.load_area_name, #{loadAddress,jdbcType=VARCHAR}) or
            instr(tcod.load_detail_address, #{loadAddress,jdbcType=VARCHAR}))
        </if>
        <if test="unloadAddress != null and unloadAddress != ''">
            and(
            instr(tcod.unload_province_name, #{unloadAddress,jdbcType=VARCHAR}) or
            instr(tcod.unload_city_name, #{unloadAddress,jdbcType=VARCHAR}) or
            instr(tcod.unload_area_name, #{unloadAddress,jdbcType=VARCHAR}) or
            instr(tcod.unload_detail_address, #{unloadAddress,jdbcType=VARCHAR}))
        </if>
        <if test="loadWarehouse != null and loadWarehouse != ''">
            and instr(tcod.load_warehouse, #{loadWarehouse,jdbcType=VARCHAR})
        </if>
        <if test="unloadWarehouse != null and unloadWarehouse != ''">
            and instr(tcod.unload_warehouse, #{unloadWarehouse,jdbcType=VARCHAR})
        </if>
        <if test="loadTimeStart != null and loadTimeStart != ''">
            and tco.load_time &gt;= DATE_FORMAT(#{loadTimeStart,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%S')
        </if>
        <if test="loadTimeEnd != null and loadTimeEnd != ''">
            and tco.load_time &lt;= DATE_FORMAT(#{loadTimeEnd,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="unloadTimeStart != null and unloadTimeStart != ''">
            and tco.unload_time &gt;= DATE_FORMAT(#{unloadTimeStart,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%S')
        </if>
        <if test="unloadTimeEnd != null and unloadTimeEnd != ''">
            and tco.unload_time &lt;= DATE_FORMAT(#{unloadTimeEnd,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="signTimeStart != null and signTimeStart != ''">
            and tco.sign_time &gt;= DATE_FORMAT(#{signTimeStart,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%S')
        </if>
        <if test="signTimeEnd != null and signTimeEnd != ''">
            and tco.sign_time &lt;= DATE_FORMAT(#{signTimeEnd,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="carrierOrderCodeList != null and carrierOrderCodeList.size > 0">
            and tco.carrier_order_code in
            <foreach collection="carrierOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="demandOrderCodeList != null and demandOrderCodeList.size > 0">
            and tco.demand_order_code in
            <foreach collection="demandOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="carrierOrderIds != null and carrierOrderIds != ''">
            and tco.id in (${carrierOrderIds})
        </if>
        <if test="companyCarrierId != null">
            and tco.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        </if>
        order by tco.last_modified_time desc, tco.id desc
    </select>

    <resultMap id="selectCarrierTraditionWaitSettleStatementListMap" type="com.logistics.tms.controller.settlestatement.tradition.response.TraditionWaitSettleStatementListResponseModel">
        <result column="carrierOrderId" property="carrierOrderId"/>
        <result column="carrierOrderCode" property="carrierOrderCode"/>
        <result column="demandOrderId" property="demandOrderId"/>
        <result column="demandOrderCode" property="demandOrderCode"/>
        <result column="dispatchOrderId" property="dispatchOrderId"/>
        <result column="dispatchOrderCode" property="dispatchOrderCode"/>
        <result column="companyCarrierId" property="companyCarrierId"/>
        <result column="companyCarrierType" property="companyCarrierType"/>
        <result column="companyCarrierName" property="companyCarrierName"/>
        <result column="carrierContactName" property="carrierContactName"/>
        <result column="carrierContactMobile" property="carrierContactMobile"/>
        <result column="companyEntrustName" property="companyEntrustName"/>
        <result column="vehicle_no" property="vehicleNo"/>
        <result column="driver_name" property="driverName"/>
        <result column="driver_mobile" property="driverPhone"/>
        <result column="carrierPriceType" property="carrierPriceType"/>
        <result column="carrier_price" property="carrierPrice"/>
        <result column="expectAmount" property="expectAmount"/>
        <result column="loadAmount" property="loadAmount"/>
        <result column="unloadAmount" property="unloadAmount"/>
        <result column="signAmount" property="signAmount"/>
        <result column="goodsUnit" property="goodsUnit"/>
        <result column="loadTime" property="loadTime"/>
        <result column="unloadTime" property="unloadTime"/>
        <result column="signTime" property="signTime"/>
        <result column="loadWarehouse" property="loadWarehouse"/>
        <result column="loadProvinceName" property="loadProvinceName"/>
        <result column="loadCityName" property="loadCityName"/>
        <result column="loadAreaName" property="loadAreaName"/>
        <result column="loadDetailAddress" property="loadDetailAddress"/>
        <result column="consignorName" property="consignorName"/>
        <result column="consignorMobile" property="consignorMobile"/>
        <result column="unloadWarehouse" property="unloadWarehouse"/>
        <result column="unloadProvinceName" property="unloadProvinceName"/>
        <result column="unloadCityName" property="unloadCityName"/>
        <result column="unloadAreaName" property="unloadAreaName"/>
        <result column="unloadDetailAddress" property="unloadDetailAddress"/>
        <result column="receiverName" property="receiverName"/>
        <result column="receiverMobile" property="receiverMobile"/>
        <result column="dispatch_user_name" property="dispatchUserName"/>
        <result column="expectMileage" property="expectMileage"/>
        <result column="carrier_settlement" property="carrierSettlement"/>
    </resultMap>

    <select id="selectCarrierTraditionWaitSettleStatementList" resultMap="selectCarrierTraditionWaitSettleStatementListMap">
        select
            tco.id                                                                                                                         as carrierOrderId,
            tco.demand_order_id                                                                                                            as demandOrderId,
            tco.dispatch_order_id                                                                                                          as dispatchOrderId,
            tco.dispatch_order_code                                                                                                        as dispatchOrderCode,
            tco.carrier_order_code                                                                                                         as carrierOrderCode,
            tco.demand_order_code                                                                                                          as demandOrderCode,
            tco.company_entrust_name                                                                                                       as companyEntrustName,
            tco.company_carrier_id                                                                                                         as companyCarrierId,
            tco.company_carrier_name                                                                                                       as companyCarrierName,
            tco.company_carrier_type                                                                                                       as companyCarrierType,
            tco.carrier_contact_name                                                                                                       as carrierContactName,
            AES_DECRYPT(UNHEX(tco.carrier_contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactMobile,
            tco.load_time                                                                                                                  as loadTime,
            tco.unload_time                                                                                                                as unloadTime,
            tco.sign_time                                                                                                                  as signTime,
            tco.goods_unit                                                                                                                 as goodsUnit,
            tco.expect_amount                                                                                                              as expectAmount,
            tco.load_amount                                                                                                                as loadAmount,
            tco.unload_amount                                                                                                              as unloadAmount,
            tco.sign_amount                                                                                                                as signAmount,
            tco.carrier_price_type                                                                                                         as carrierPriceType,
            tco.carrier_price,
            tco.expect_mileage                                                                                                             as expectMileage,
            tco.dispatch_user_name,
            tco.carrier_settlement,

            tcoa.load_province_name                                                                                                        as loadProvinceName,
            tcoa.load_city_name                                                                                                            as loadCityName,
            tcoa.load_area_name                                                                                                            as loadAreaName,
            tcoa.load_detail_address                                                                                                       as loadDetailAddress,
            tcoa.load_warehouse                                                                                                            as loadWarehouse,
            tcoa.consignor_name                                                                                                            as consignorName,
            tcoa.consignor_mobile                                                                                                          as consignorMobile,
            tcoa.unload_province_name                                                                                                      as unloadProvinceName,
            tcoa.unload_city_name                                                                                                          as unloadCityName,
            tcoa.unload_area_name                                                                                                          as unloadAreaName,
            tcoa.unload_warehouse                                                                                                          as unloadWarehouse,
            tcoa.unload_detail_address                                                                                                     as unloadDetailAddress,
            tcoa.receiver_name                                                                                                             as receiverName,
            tcoa.receiver_mobile                                                                                                           as receiverMobile,

            tcovh.vehicle_no,
            tcovh.driver_name,
            tcovh.driver_mobile

        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tcoa.carrier_order_id = tco.id and tcoa.valid = 1
        left join t_carrier_order_vehicle_history tcovh on tcovh.valid = 1 and tcovh.if_invalid = 1 and tco.id = tcovh.carrier_order_id
        where tco.valid = 1
        and tco.id in (${ids})
        order by tco.last_modified_time desc, tco.id desc
    </select>

    <update id="batchUpdateCarrierSettleStatus">
        update t_carrier_order
        set carrier_settle_statement_status = #{status,jdbcType=INTEGER},
        last_modified_by = #{operator,jdbcType=VARCHAR},
        <if test="updateWrapper.statementOtherFeeTaxPoint != null">
        statement_other_fee_tax_point = #{updateWrapper.statementOtherFeeTaxPoint,jdbcType=DECIMAL},
        </if>
        <if test="updateWrapper.statementFreightTaxPoint != null">
        statement_freight_tax_point = #{updateWrapper.statementFreightTaxPoint,jdbcType=DECIMAL},
        </if>
        last_modified_time = current_timestamp
        where valid = 1
        and id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="searchCarrierOrderIdsForLeYiReceiptAudit" resultType="java.lang.Long">
        select
        distinct tco.id
        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tcoa.carrier_order_id = tco.id and tcoa.valid = 1
        where tco.valid= 1
        and tco.demand_order_source = 1
        <if test="condition.carrierOrderCode !=null and condition.carrierOrderCode != ''">
            and (INSTR(tco.carrier_order_code,#{condition.carrierOrderCode})
            or INSTR(REPLACE(tco.carrier_order_code,'-',''),#{condition.carrierOrderCode}))
        </if>
        <if test="condition.entrustType != null">
            and tco.demand_order_entrust_type = #{condition.entrustType,jdbcType=INTEGER}
        </if>
        <if test="condition.unloadStartDate != null and condition.unloadStartDate !='' ">
            and tco.unload_time &gt;= DATE_FORMAT(#{condition.unloadStartDate},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="condition.unloadEndDate != null and condition.unloadEndDate !=''">
            and tco.unload_time &lt;= DATE_FORMAT(#{condition.unloadEndDate},'%Y-%m-%d 23:59:59')
        </if>
        <if test="condition.carrierOrderIdList != null and condition.carrierOrderIdList.size > 0">
            and tco.id in
            <foreach collection="condition.carrierOrderIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="condition.loadAddress!=null and condition.loadAddress!=''">
            and instr(concat(tcoa.load_province_name,tcoa.load_city_name,tcoa.load_area_name,tcoa.load_detail_address,tcoa.load_warehouse),#{condition.loadAddress,jdbcType=VARCHAR})
        </if>
        <if test="condition.unloadAddress!=null and condition.unloadAddress!=''">
            and instr(concat(tcoa.unload_province_name,tcoa.unload_city_name,tcoa.unload_area_name,tcoa.unload_detail_address,tcoa.unload_warehouse),#{condition.unloadAddress,jdbcType=VARCHAR})
        </if>
        order by tco.created_time desc,tco.id desc
    </select>

    <select id="searchCarrierOrderForLeYiReceiptAudit" resultType="com.logistics.tms.controller.carrierorder.response.SearchCarrierOrderListForLeYiResponseModel">
        SELECT tco.id carrierOrderId,
        tco.carrier_order_code carrierOrderCode,
        tco.unload_time unloadTime,
        tco.demand_order_entrust_type entrustType,

        tcoa.load_province_name loadProvinceName,
        tcoa.load_city_name loadCityName,
        tcoa.load_area_name loadAreaName,
        tcoa.load_detail_address loadDetailAddress,
        tcoa.load_warehouse loadWarehouse,
        tcoa.unload_province_name unloadProvinceName,
        tcoa.unload_city_name unloadCityName,
        tcoa.unload_area_name unloadAreaName,
        tcoa.unload_detail_address unloadDetailAddress,
        tcoa.unload_warehouse unloadWarehouse,

        tcovh.driver_name driverName,
        tcovh.driver_mobile driverMobile,
        tcovh.vehicle_no vehicleNo
        FROM t_carrier_order tco
        LEFT JOIN t_carrier_order_address tcoa ON tcoa.carrier_order_id = tco.id
        AND tcoa.valid = 1
        LEFT JOIN t_carrier_order_vehicle_history tcovh ON tco.id = tcovh.carrier_order_id
        AND tcovh.valid = 1
        AND tcovh.if_invalid = 1
        WHERE tco.valid = 1
        AND tco.id IN
        <foreach collection="carrierOrderIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="printBillDetail" resultType="com.logistics.tms.controller.carrierorderapplet.response.PrintBillDetailResponseModel">
        SELECT
        tco.carrier_order_code     carrierOrderCode,
        tco.expect_amount          expectAmount,
        tco.load_amount            loadAmount,
        tco.goods_unit             goodsUnit,
        tco.status,

        tcoa.load_warehouse        loadWarehouse,
        tcoa.consignor_name        loadPerson,
        tcoa.consignor_mobile      loadMobile,
        tcoa.receiver_name         unloadPerson,
        tcoa.receiver_mobile       unloadMobile,
        tcoa.unload_province_name  unloadProvinceName,
        tcoa.unload_city_name      unloadCityName,
        tcoa.unload_area_name      unloadAreaName,
        tcoa.unload_detail_address unloadDetailAddress,
        tcoa.unload_warehouse      unloadWarehouse,

        tcovh.driver_name driverName,
        tcovh.driver_mobile driverMobile,
        tcovh.vehicle_no vehicleNo
        FROM t_carrier_order tco
        LEFT JOIN t_carrier_order_address tcoa ON tcoa.carrier_order_id = tco.id AND tcoa.valid = 1
        LEFT JOIN t_carrier_order_vehicle_history tcovh ON tco.id = tcovh.carrier_order_id AND tcovh.if_invalid = 1 AND tcovh.valid = 1
        WHERE tco.valid = 1
        AND tco.id = #{carrierOrderId,jdbcType=BIGINT}
    </select>

    <select id="selectByDemandOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_carrier_order
        where valid = 1
        and demand_order_id = #{demandOrderId,jdbcType=BIGINT}
    </select>

    <select id="selectMinCarrierPrice" resultType="java.math.BigDecimal">
        select min(IF(tco.carrier_price_type=1,tco.carrier_price*tco.sign_amount,tco.carrier_price))
        from t_carrier_order tco
                 LEFT JOIN t_carrier_order_address tcoa ON tcoa.carrier_order_id = tco.id AND tcoa.valid = 1
        where tco.valid = 1
          and IF(tco.carrier_price_type=1,tco.carrier_price*tco.sign_amount,tco.carrier_price) &gt; 100
          and year(tco.sign_time) = #{lastYear}
          and tco.sign_amount &gt;= #{carriageScopeMin,jdbcType=DECIMAL}
          and tco.sign_amount &lt;= #{carriageScopeMax,jdbcType=DECIMAL}
          and tcoa.load_province_id = #{loadProvinceId,jdbcType=BIGINT}
          and tcoa.load_city_id = #{loadCityId,jdbcType=BIGINT}
          and tcoa.load_area_id = #{loadAreaId,jdbcType=BIGINT}
          and tcoa.unload_province_id = #{unloadProvinceId,jdbcType=BIGINT}
          and tcoa.unload_city_id = #{unloadCityId,jdbcType=BIGINT}
          and tcoa.unload_area_id = #{unloadAreaId,jdbcType=BIGINT}
    </select>

    <select id="selectByDemandOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_carrier_order
        where valid = 1
        and demand_order_id in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="loadWaitReservationCarrierOrder" resultType="com.logistics.tms.controller.reservationorder.response.WaitReservationCarrierOrderListResponseModel">
        select
        tco.id as carrierOrderId,
        tco.carrier_order_code as carrierOrderCode,
        tco.demand_order_entrust_type as entrustType,
        tco.status,
        tco.expect_amount as expectAmount,

        tcoa.load_province_id as provinceId,
        tcoa.load_province_name as provinceName,
        tcoa.load_city_id as cityId,
        tcoa.load_city_name as cityName,
        tcoa.load_area_id as areaId,
        tcoa.load_area_name as areaName,
        tcoa.load_detail_address as detailAddress,
        tcoa.load_warehouse as warehouse,
        tcoa.expected_load_time as expectedTime,
        tcoa.load_longitude as longitude,
        tcoa.load_latitude as latitude,

        tcovh.driver_id as driverId,
        tcovh.driver_name as driverName,
        tcovh.driver_mobile as driverMobile,
        tcovh.driver_identity as driverIdentity,
        tcovh.vehicle_id as vehicleId,
        tcovh.vehicle_no as vehicleNo
        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tcoa.carrier_order_id = tco.id and tcoa.valid = 1
        left join t_carrier_order_vehicle_history tcovh on tcovh.carrier_order_id = tco.id and tcovh.if_invalid = 1 and tcovh.valid = 1
        where tco.valid = 1
        <if test="driverModel != null and driverModel.size > 0">
            and (
            <foreach collection="driverModel" item="item" open="(" close=")" separator="or">
                tco.company_carrier_id = #{item.companyCarrierId,jdbcType=BIGINT}
                and tcovh.driver_id = #{item.driverId,jdbcType=BIGINT}
            </foreach>
            )
        </if>
        <if test="carrierOrderIdList != null and carrierOrderIdList.size > 0">
            and tco.id in
            <foreach collection="carrierOrderIdList" item="carrierOrderId" separator="," open="(" close=")">
                #{carrierOrderId,jdbcType=BIGINT}
            </foreach>
        </if>
        and tco.status in (10000, 20000)
        <if test="loadDemandOrderEntrustTypeList != null and loadDemandOrderEntrustTypeList.size > 0">
            and tco.demand_order_entrust_type in
            <foreach collection="loadDemandOrderEntrustTypeList" item="demandOrderEntrustType" separator="," open="(" close=")">
                #{demandOrderEntrustType,jdbcType=INTEGER}
            </foreach>
        </if>
        and tco.if_cancel = 0
        and tco.if_empty = 0
        and tco.id not in
        (select
        troi.carrier_order_id
        from
        t_reservation_order_item troi
        left join t_reservation_order tro on troi.reservation_order_id = tro.id and tro.valid = 1
        where
        troi.valid = 1
        and troi.enabled = 1
        and tro.reservation_type = 1
        <if test="driverModel != null and driverModel.size > 0">
            and (
            <foreach collection="driverModel" item="item" open="(" close=")" separator="or">
                tro.driver_id = #{item.driverId,jdbcType=BIGINT}
            </foreach>
            )
        </if>
        )

    </select>

    <select id="waitReservationOrderId" resultType="java.lang.Long">
        select
        DISTINCT tco.id
        from t_carrier_order tco
        left join t_carrier_order_vehicle_history tcovh on tcovh.carrier_order_id = tco.id and tcovh.if_invalid = 1 and tcovh.valid = 1
        where tco.valid = 1
        and (
        <foreach collection="driverModel" item="item" open="(" close=")" separator="or">
            tco.company_carrier_id = #{item.companyCarrierId,jdbcType=BIGINT}
            and tcovh.driver_id = #{item.driverId,jdbcType=BIGINT}
        </foreach>
        )
        <choose>
            <when test="reservationType == 1">
                and tco.status in (10000, 20000)
                and tco.demand_order_entrust_type in (1, 4)
            </when>
            <otherwise>
                and tco.status in (30000, 40000)
                and tco.demand_order_entrust_type in (2, 4, 12)
            </otherwise>
        </choose>
        and tco.if_cancel = 0
        and tco.if_empty = 0
        and tco.id not in
        (select
        troi.carrier_order_id
        from
        t_reservation_order_item troi
        left join t_reservation_order tro on troi.reservation_order_id = tro.id and tro.valid = 1
        where
        troi.valid = 1
        and troi.enabled = 1
        <choose>
            <when test="reservationType == 1">
                and tro.reservation_type  = 1
            </when>
            <otherwise>
                and tro.reservation_type  = 2
            </otherwise>
        </choose>
        and (
        <foreach collection="driverModel" item="item" open="(" close=")" separator="or">
            tro.driver_id = #{item.driverId,jdbcType=BIGINT}
        </foreach>
        )
        )
    </select>




    <select id="unloadWaitReservationCarrierOrder" resultType="com.logistics.tms.controller.reservationorder.response.WaitReservationCarrierOrderListResponseModel">

        select
        tco.id as carrierOrderId,
        tco.carrier_order_code as carrierOrderCode,
        tco.demand_order_entrust_type as entrustType,
        tco.status,
        tco.expect_amount as expectAmount,

        tcoa.unload_province_id as provinceId,
        tcoa.unload_province_name as provinceName,
        tcoa.unload_city_id as cityId,
        tcoa.unload_city_name as cityName,
        tcoa.unload_area_id as areaId,
        tcoa.unload_area_name as areaName,
        tcoa.unload_detail_address as detailAddress,
        tcoa.unload_warehouse as warehouse,
        tcoa.expected_unload_time as expectedTime,
        tcoa.unload_longitude as longitude,
        tcoa.unload_latitude as latitude,

        tcovh.driver_id as driverId,
        tcovh.driver_name as driverName,
        tcovh.driver_mobile as driverMobile,
        tcovh.driver_identity as driverIdentity,
        tcovh.vehicle_id as vehicleId,
        tcovh.vehicle_no as vehicleNo
        from t_carrier_order tco
        left join t_carrier_order_address tcoa on tcoa.carrier_order_id = tco.id and tcoa.valid = 1
        left join t_carrier_order_vehicle_history tcovh on tcovh.carrier_order_id = tco.id and tcovh.if_invalid = 1 and tcovh.valid = 1
        where tco.valid = 1
        <if test="driverModel != null and driverModel.size > 0">
        and (
        <foreach collection="driverModel" item="item" open="(" close=")" separator="or">
            tco.company_carrier_id = #{item.companyCarrierId,jdbcType=BIGINT}
            and tcovh.driver_id = #{item.driverId,jdbcType=BIGINT}
        </foreach>
        )
        </if>
        <if test="carrierOrderIdList != null and carrierOrderIdList.size > 0">
            and tco.id in
            <foreach collection="carrierOrderIdList" item="carrierOrderId" separator="," open="(" close=")">
                #{carrierOrderId,jdbcType=BIGINT}
            </foreach>
        </if>
        and tco.status in (30000, 40000)

        <if test="unLoadDemandOrderEntrustTypeList != null and unLoadDemandOrderEntrustTypeList.size > 0">
            and tco.demand_order_entrust_type in
            <foreach collection="unLoadDemandOrderEntrustTypeList" item="demandOrderEntrustType" separator="," open="(" close=")">
                #{demandOrderEntrustType,jdbcType=INTEGER}
            </foreach>
        </if>
        and tco.if_cancel = 0
        and tco.if_empty = 0
        and tco.id not in
        (select
        troi.carrier_order_id
        from
        t_reservation_order_item troi
        left join t_reservation_order tro on troi.reservation_order_id = tro.id and tro.valid = 1
        where
        troi.valid = 1
        and troi.enabled = 1
        and tro.reservation_type  = 2
        <if test="driverModel != null and driverModel.size > 0">
        and (
        <foreach collection="driverModel" item="item" open="(" close=")" separator="or">
            tro.driver_id = #{item.driverId,jdbcType=BIGINT}
        </foreach>
        )
        </if>
        )
    </select>


    <select id="selectCarrierOrderReservationInfoByCode" resultType="com.logistics.tms.controller.reservationorder.response.GetReservationInfo4H5RespModel">
        select
        tro.id as reservationOrderId,
        tco.id as carrierOrderId,
        tco.carrier_order_code as carrierOrderCode,
        tco.demand_order_entrust_type as orderEntrustType,
        CASE
		     WHEN tco.demand_order_entrust_type = 4 AND tco.STATUS IN ( 30000, 40000 ) AND tro.reservation_type = 1 AND tro.STATUS = 20 THEN 0
		     ELSE IFNULL( tro.STATUS, 0 )
		     END AS state,
	    CASE
			WHEN tco.demand_order_entrust_type = 4 AND tco.STATUS IN ( 30000, 40000 ) AND tro.reservation_type = 1 AND tro.STATUS = 20 THEN'可预约'
				WHEN tro.STATUS = 0 || tro.STATUS IS NULL THEN '可预约'
				WHEN tro.STATUS = 10 THEN '待签到'
				WHEN tro.STATUS = 20 THEN '已签到'
			END AS stateLabel,
        CONCAT(tcovh.driver_name, '', tcovh.driver_mobile) as driverName,
        tcovh.vehicle_no as vehicleNumber,
        tcoa.load_province_name as loadProvinceName,
        tcoa.load_city_name as loadCityName,
        tcoa.load_area_name as loadAreaName,
        tcoa.unload_province_name as unLoadProvinceName,
        tcoa.unload_city_name as unLoadCityName,
        tcoa.unload_area_name as unLoadAreaName,
        tcoa.unload_warehouse as unLoadWarehouse,
        tcoa.load_warehouse as loadWarehouse
        from
        t_carrier_order tco
        left join t_carrier_order_vehicle_history tcovh on tcovh.carrier_order_id = tco.id and tcovh.if_invalid = 1 and tcovh.valid = 1
        left join t_carrier_order_address tcoa on tcoa.carrier_order_id = tco.id and tcoa.valid = 1
        left join t_reservation_order_item troi  on tco.id = troi.carrier_order_id and troi.valid = 1 and troi.enabled = 1
        left join t_reservation_order tro  on troi.reservation_order_id = tro.id and tro.valid = 1  AND tro.status != 30
        where
        tco.carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR}
        order by tro.id desc
        limit 1
    </select>


    <select id="searchCarrierReservationOrder" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_carrier_order
        where valid = 1 AND if_cancel = 0 AND if_empty = 0  and demand_order_source  = 1
        and company_carrier_id = #{loginUserCompanyCarrierId,jdbcType=BIGINT}
        	AND id NOT IN (
        SELECT
        troi.carrier_order_id
        FROM
        t_reservation_order_item troi
        LEFT JOIN t_reservation_order tro ON troi.reservation_order_id = tro.id
        AND tro.valid = 1
        WHERE
        troi.valid = 1
        AND troi.enabled = 1
        <choose>
            <when test="requestModel.operateNode == 1">
                and tro.reservation_type = 1
            </when>
            <otherwise>
                and tro.reservation_type = 2
            </otherwise>
        </choose>
                )
        <choose>
            <when test="requestModel.operateNode == 1">
                and status in (10000, 20000)
<!--                and demand_order_entrust_type in (1, 4)-->
            </when>
            <otherwise>
                and status in (30000, 40000)
<!--                and demand_order_entrust_type in (2, 4, 12)-->
            </otherwise>
        </choose>

        <if test="carrierOrderIds != null and carrierOrderIds.size() != 0">
            and id in
                <foreach collection="carrierOrderIds" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
            </foreach>
         </if>

        order by id desc
    </select>


    <select id="selectExtCarrierOrderCountByDemandOrderCode">
        select
        count(*)
        t_carrier_order
        where
        valid = 1
        and if_cancel = 0
        and if_empty = 0
        and demand_order_code = #{demandOrderCode,jdbcType=VARCHAR}
        and if_ext_carrier_order = 1
    </select>







</mapper>