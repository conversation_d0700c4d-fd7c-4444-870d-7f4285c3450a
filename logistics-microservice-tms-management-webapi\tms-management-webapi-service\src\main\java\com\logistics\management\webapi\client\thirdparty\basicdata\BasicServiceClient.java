package com.logistics.management.webapi.client.thirdparty.basicdata;

import com.logistics.management.webapi.base.enums.EnabledEnum;
import com.logistics.management.webapi.client.thirdparty.basicdata.ocr.OCRClient;
import com.logistics.management.webapi.client.thirdparty.basicdata.ocr.response.OcrMultipleInvoiceResponseModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.user.UserClient;
import com.logistics.management.webapi.client.thirdparty.basicdata.user.request.GetUserInfoListRequestModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.user.response.GetUserInfoListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/22 15:15
 */
@Service
public class BasicServiceClient {

    @Resource
    private UserClient userClient;
    @Resource
    private OCRClient ocrClient;

    /**
     * 查询用户列表（用户名+手机号模糊）
     * @param requestModel
     * @return
     */
    public List<GetUserInfoListResponseModel> getUserInfoList(GetUserInfoListRequestModel requestModel){
        Result<List<GetUserInfoListResponseModel>> result = userClient.getUserInfoList(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 查询启用中的用户信息
     * @param userName 用户名+手机号模糊
     * @return
     */
    public List<GetUserInfoListResponseModel> searchEnableUserList(String userName){
        GetUserInfoListRequestModel requestModel = new GetUserInfoListRequestModel();
        requestModel.setAccount(userName);
        requestModel.setEnabled(EnabledEnum.ENABLED.getKey());
        return getUserInfoList(requestModel);
    }

    /**
     * 发票数据ocr识别
     * @param file
     * @param type
     * @return
     */
    public OcrMultipleInvoiceResponseModel multipleInvoice(MultipartFile file, String type){
        Result<OcrMultipleInvoiceResponseModel> result = ocrClient.multipleInvoice(file, type);
        result.throwException();
        return result.getData();
    }
}
