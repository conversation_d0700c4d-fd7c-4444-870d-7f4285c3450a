package com.logistics.tms.controller.freightconfig.request.region;

import com.logistics.tms.controller.freightconfig.request.CarrierFreightConfigRequestModel;
import com.logistics.tms.controller.freightconfig.request.ladder.CarrierFreightConfigLadderRequestModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigRegionAddRequestModel extends CarrierFreightConfigRequestModel {

    @ApiModelProperty(value = "新增方案集合")
    private List<CarrierFreightConfigRegionAddItemRequestModel> schemeList;

    @Data
    public static class CarrierFreightConfigRegionAddItemRequestModel {

        @ApiModelProperty(value = "运价方案类型; 100: 路线配置; 200:同区-跨区价格; 201: 同区价格; 202: 跨区价格; 301:系统计算预计距离; 302:系统配置距离", required = true)
        private Integer schemeType;

        @ApiModelProperty(value = "阶梯", required = true)
        private List<CarrierFreightConfigLadderRequestModel> ladderConfigList;
    }
}
