package com.logistics.tms.controller.freightconfig.response.shipping;

import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class ListShippingFreightRuleListRespModel {


    /**
     * 主键id
     */

    private Long shippingFreightRuleId ;


    /**
     * 1启用,0禁用
     */
    private Integer enabled ;
    /**
     * 1启用,0禁用
     */
    private String enabledStr = "";


//    /**
//     * 发货省name
//     */
//    private String fromProvinceName = "";

    /**
     * 发货市name
     */
    private String fromCityName = "";

    /**
     * 发货区name
     */
    private String fromAreaName = "";

//
//    /**
//     * 收货省name
//     */
//    private String toProvinceName = "";

    /**
     * 收货市name
     */
    private String toCityName = "";

    /**
     * 收货区name
     */
    private String toAreaName = "";


    /**
     * 运距
     */
    private String carrierDistance = "";


    /**
     * 零担数量
     */
    private String startEndCount = "";



    /**
     * 零担价格（含单位）
     */
    private String price = "";



    /**
     * 4.2车长报价
     */
    private String fourPointTwo = "";



    /**
     * 6.8车长报价
     */
    private String sixPointEight = "";


    /**
     * 9.6车长报价
     */
    private String ninePointSix = "";



    /**
     * 13.7车长报价
     */
    private String thirteenPointSevenFive = "";


    /**
     * 17.5车长报价
     */
    private String seventeenPointFive = "";


    /**
     * 操作人
     */
    private String lastModifiedBy = "";

    /**
     * 操作时间
     */
    private String lastModifiedTime = "";


}
