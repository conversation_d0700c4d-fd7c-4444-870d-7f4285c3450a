<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TSinopecOriginalTransitLineItemDataMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TSinopecOriginalTransitLineItemData" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="sinopec_original_data_id" property="sinopecOriginalDataId" jdbcType="BIGINT" />
    <result column="logistics_order_number" property="logisticsOrderNumber" jdbcType="VARCHAR" />
    <result column="business_order_number" property="businessOrderNumber" jdbcType="VARCHAR" />
    <result column="business_order_valid_end" property="businessOrderValidEnd" jdbcType="TIMESTAMP" />
    <result column="certificate_time" property="certificateTime" jdbcType="TIMESTAMP" />
    <result column="exist_state" property="existState" jdbcType="INTEGER" />
    <result column="material_attribute" property="materialAttribute" jdbcType="INTEGER" />
    <result column="make_drug" property="makeDrug" jdbcType="INTEGER" />
    <result column="support_auth_online" property="supportAuthOnline" jdbcType="INTEGER" />
    <result column="packing_name" property="packingName" jdbcType="VARCHAR" />
    <result column="packing_specification" property="packingSpecification" jdbcType="VARCHAR" />
    <result column="production_enterprise_type" property="productionEnterpriseType" jdbcType="INTEGER" />
    <result column="production_enterprise_name" property="productionEnterpriseName" jdbcType="VARCHAR" />
    <result column="production_enterprise_code" property="productionEnterpriseCode" jdbcType="VARCHAR" />
    <result column="batch" property="batch" jdbcType="VARCHAR" />
    <result column="material_main_category" property="materialMainCategory" jdbcType="VARCHAR" />
    <result column="material_main_category_code" property="materialMainCategoryCode" jdbcType="VARCHAR" />
    <result column="material_category" property="materialCategory" jdbcType="VARCHAR" />
    <result column="material_category_code" property="materialCategoryCode" jdbcType="VARCHAR" />
    <result column="material_name" property="materialName" jdbcType="VARCHAR" />
    <result column="material_code" property="materialCode" jdbcType="VARCHAR" />
    <result column="associated_count" property="associatedCount" jdbcType="DECIMAL" />
    <result column="unit_id" property="unitId" jdbcType="INTEGER" />
    <result column="unit_name" property="unitName" jdbcType="VARCHAR" />
    <result column="client_code" property="clientCode" jdbcType="VARCHAR" />
    <result column="client_name" property="clientName" jdbcType="VARCHAR" />
    <result column="transport_price" property="transportPrice" jdbcType="DECIMAL" />
    <result column="estimate_transport_fee" property="estimateTransportFee" jdbcType="DECIMAL" />
    <result column="freighter_type" property="freighterType" jdbcType="INTEGER" />
    <result column="freighter_code" property="freighterCode" jdbcType="VARCHAR" />
    <result column="freighter_name" property="freighterName" jdbcType="VARCHAR" />
    <result column="origin_port_location_type" property="originPortLocationType" jdbcType="INTEGER" />
    <result column="origin_port_location_code" property="originPortLocationCode" jdbcType="VARCHAR" />
    <result column="origin_port_location_name" property="originPortLocationName" jdbcType="VARCHAR" />
    <result column="origin_port_detail_address" property="originPortDetailAddress" jdbcType="VARCHAR" />
    <result column="origin_port_province_code" property="originPortProvinceCode" jdbcType="VARCHAR" />
    <result column="origin_port_province_name" property="originPortProvinceName" jdbcType="VARCHAR" />
    <result column="origin_port_city_code" property="originPortCityCode" jdbcType="VARCHAR" />
    <result column="origin_port_city_name" property="originPortCityName" jdbcType="VARCHAR" />
    <result column="origin_port_county_code" property="originPortCountyCode" jdbcType="VARCHAR" />
    <result column="origin_port_county_name" property="originPortCountyName" jdbcType="VARCHAR" />
    <result column="origin_port_town_code" property="originPortTownCode" jdbcType="VARCHAR" />
    <result column="origin_port_town_name" property="originPortTownName" jdbcType="VARCHAR" />
    <result column="origin_port_latitude" property="originPortLatitude" jdbcType="VARCHAR" />
    <result column="origin_port_longitude" property="originPortLongitude" jdbcType="VARCHAR" />
    <result column="destination_port_location_type" property="destinationPortLocationType" jdbcType="INTEGER" />
    <result column="destination_port_location_code" property="destinationPortLocationCode" jdbcType="VARCHAR" />
    <result column="destination_port_location_name" property="destinationPortLocationName" jdbcType="VARCHAR" />
    <result column="destination_port_detail_address" property="destinationPortDetailAddress" jdbcType="VARCHAR" />
    <result column="destination_port_province_code" property="destinationPortProvinceCode" jdbcType="VARCHAR" />
    <result column="destination_port_province_name" property="destinationPortProvinceName" jdbcType="VARCHAR" />
    <result column="destination_port_city_code" property="destinationPortCityCode" jdbcType="VARCHAR" />
    <result column="destination_port_city_name" property="destinationPortCityName" jdbcType="VARCHAR" />
    <result column="destination_port_county_code" property="destinationPortCountyCode" jdbcType="VARCHAR" />
    <result column="destination_port_county_name" property="destinationPortCountyName" jdbcType="VARCHAR" />
    <result column="destination_port_town_code" property="destinationPortTownCode" jdbcType="VARCHAR" />
    <result column="destination_port_town_name" property="destinationPortTownName" jdbcType="VARCHAR" />
    <result column="destination_port_latitude" property="destinationPortLatitude" jdbcType="VARCHAR" />
    <result column="destination_port_longitude" property="destinationPortLongitude" jdbcType="VARCHAR" />
    <result column="receiver_code" property="receiverCode" jdbcType="VARCHAR" />
    <result column="receiver_name" property="receiverName" jdbcType="VARCHAR" />
    <result column="receiver_type" property="receiverType" jdbcType="INTEGER" />
    <result column="receiver_phone" property="receiverPhone" jdbcType="VARCHAR" />
    <result column="otherFee" property="otherfee" jdbcType="DECIMAL" />
    <result column="remark1" property="remark1" jdbcType="VARCHAR" />
    <result column="remark2" property="remark2" jdbcType="VARCHAR" />
    <result column="remark3" property="remark3" jdbcType="VARCHAR" />
    <result column="remark4" property="remark4" jdbcType="VARCHAR" />
    <result column="remark5" property="remark5" jdbcType="VARCHAR" />
    <result column="remark6" property="remark6" jdbcType="VARCHAR" />
    <result column="remark7" property="remark7" jdbcType="VARCHAR" />
    <result column="remark8" property="remark8" jdbcType="VARCHAR" />
    <result column="remark9" property="remark9" jdbcType="VARCHAR" />
    <result column="remark10" property="remark10" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, sinopec_original_data_id, logistics_order_number, business_order_number, business_order_valid_end, 
    certificate_time, exist_state, material_attribute, make_drug, support_auth_online, 
    packing_name, packing_specification, production_enterprise_type, production_enterprise_name, 
    production_enterprise_code, batch, material_main_category, material_main_category_code, 
    material_category, material_category_code, material_name, material_code, associated_count, 
    unit_id, unit_name, client_code, client_name, transport_price, estimate_transport_fee, 
    freighter_type, freighter_code, freighter_name, origin_port_location_type, origin_port_location_code, 
    origin_port_location_name, origin_port_detail_address, origin_port_province_code, 
    origin_port_province_name, origin_port_city_code, origin_port_city_name, origin_port_county_code, 
    origin_port_county_name, origin_port_town_code, origin_port_town_name, origin_port_latitude, 
    origin_port_longitude, destination_port_location_type, destination_port_location_code, 
    destination_port_location_name, destination_port_detail_address, destination_port_province_code, 
    destination_port_province_name, destination_port_city_code, destination_port_city_name, 
    destination_port_county_code, destination_port_county_name, destination_port_town_code, 
    destination_port_town_name, destination_port_latitude, destination_port_longitude, 
    receiver_code, receiver_name, receiver_type, receiver_phone, otherFee, remark1, remark2, 
    remark3, remark4, remark5, remark6, remark7, remark8, remark9, remark10, created_by, 
    created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_sinopec_original_transit_line_item_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_sinopec_original_transit_line_item_data
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TSinopecOriginalTransitLineItemData" >
    insert into t_sinopec_original_transit_line_item_data (id, sinopec_original_data_id, logistics_order_number, 
      business_order_number, business_order_valid_end, 
      certificate_time, exist_state, material_attribute, 
      make_drug, support_auth_online, packing_name, 
      packing_specification, production_enterprise_type, 
      production_enterprise_name, production_enterprise_code, 
      batch, material_main_category, material_main_category_code, 
      material_category, material_category_code, 
      material_name, material_code, associated_count, 
      unit_id, unit_name, client_code, 
      client_name, transport_price, estimate_transport_fee, 
      freighter_type, freighter_code, freighter_name, 
      origin_port_location_type, origin_port_location_code, 
      origin_port_location_name, origin_port_detail_address, 
      origin_port_province_code, origin_port_province_name, 
      origin_port_city_code, origin_port_city_name, 
      origin_port_county_code, origin_port_county_name, 
      origin_port_town_code, origin_port_town_name, 
      origin_port_latitude, origin_port_longitude, 
      destination_port_location_type, destination_port_location_code, 
      destination_port_location_name, destination_port_detail_address, 
      destination_port_province_code, destination_port_province_name, 
      destination_port_city_code, destination_port_city_name, 
      destination_port_county_code, destination_port_county_name, 
      destination_port_town_code, destination_port_town_name, 
      destination_port_latitude, destination_port_longitude, 
      receiver_code, receiver_name, receiver_type, 
      receiver_phone, otherFee, remark1, 
      remark2, remark3, remark4, 
      remark5, remark6, remark7, 
      remark8, remark9, remark10, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{sinopecOriginalDataId,jdbcType=BIGINT}, #{logisticsOrderNumber,jdbcType=VARCHAR}, 
      #{businessOrderNumber,jdbcType=VARCHAR}, #{businessOrderValidEnd,jdbcType=TIMESTAMP}, 
      #{certificateTime,jdbcType=TIMESTAMP}, #{existState,jdbcType=INTEGER}, #{materialAttribute,jdbcType=INTEGER}, 
      #{makeDrug,jdbcType=INTEGER}, #{supportAuthOnline,jdbcType=INTEGER}, #{packingName,jdbcType=VARCHAR}, 
      #{packingSpecification,jdbcType=VARCHAR}, #{productionEnterpriseType,jdbcType=INTEGER}, 
      #{productionEnterpriseName,jdbcType=VARCHAR}, #{productionEnterpriseCode,jdbcType=VARCHAR}, 
      #{batch,jdbcType=VARCHAR}, #{materialMainCategory,jdbcType=VARCHAR}, #{materialMainCategoryCode,jdbcType=VARCHAR}, 
      #{materialCategory,jdbcType=VARCHAR}, #{materialCategoryCode,jdbcType=VARCHAR}, 
      #{materialName,jdbcType=VARCHAR}, #{materialCode,jdbcType=VARCHAR}, #{associatedCount,jdbcType=DECIMAL}, 
      #{unitId,jdbcType=INTEGER}, #{unitName,jdbcType=VARCHAR}, #{clientCode,jdbcType=VARCHAR}, 
      #{clientName,jdbcType=VARCHAR}, #{transportPrice,jdbcType=DECIMAL}, #{estimateTransportFee,jdbcType=DECIMAL}, 
      #{freighterType,jdbcType=INTEGER}, #{freighterCode,jdbcType=VARCHAR}, #{freighterName,jdbcType=VARCHAR}, 
      #{originPortLocationType,jdbcType=INTEGER}, #{originPortLocationCode,jdbcType=VARCHAR}, 
      #{originPortLocationName,jdbcType=VARCHAR}, #{originPortDetailAddress,jdbcType=VARCHAR}, 
      #{originPortProvinceCode,jdbcType=VARCHAR}, #{originPortProvinceName,jdbcType=VARCHAR}, 
      #{originPortCityCode,jdbcType=VARCHAR}, #{originPortCityName,jdbcType=VARCHAR}, 
      #{originPortCountyCode,jdbcType=VARCHAR}, #{originPortCountyName,jdbcType=VARCHAR}, 
      #{originPortTownCode,jdbcType=VARCHAR}, #{originPortTownName,jdbcType=VARCHAR}, 
      #{originPortLatitude,jdbcType=VARCHAR}, #{originPortLongitude,jdbcType=VARCHAR}, 
      #{destinationPortLocationType,jdbcType=INTEGER}, #{destinationPortLocationCode,jdbcType=VARCHAR}, 
      #{destinationPortLocationName,jdbcType=VARCHAR}, #{destinationPortDetailAddress,jdbcType=VARCHAR}, 
      #{destinationPortProvinceCode,jdbcType=VARCHAR}, #{destinationPortProvinceName,jdbcType=VARCHAR}, 
      #{destinationPortCityCode,jdbcType=VARCHAR}, #{destinationPortCityName,jdbcType=VARCHAR}, 
      #{destinationPortCountyCode,jdbcType=VARCHAR}, #{destinationPortCountyName,jdbcType=VARCHAR}, 
      #{destinationPortTownCode,jdbcType=VARCHAR}, #{destinationPortTownName,jdbcType=VARCHAR}, 
      #{destinationPortLatitude,jdbcType=VARCHAR}, #{destinationPortLongitude,jdbcType=VARCHAR}, 
      #{receiverCode,jdbcType=VARCHAR}, #{receiverName,jdbcType=VARCHAR}, #{receiverType,jdbcType=INTEGER}, 
      #{receiverPhone,jdbcType=VARCHAR}, #{otherfee,jdbcType=DECIMAL}, #{remark1,jdbcType=VARCHAR}, 
      #{remark2,jdbcType=VARCHAR}, #{remark3,jdbcType=VARCHAR}, #{remark4,jdbcType=VARCHAR}, 
      #{remark5,jdbcType=VARCHAR}, #{remark6,jdbcType=VARCHAR}, #{remark7,jdbcType=VARCHAR}, 
      #{remark8,jdbcType=VARCHAR}, #{remark9,jdbcType=VARCHAR}, #{remark10,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TSinopecOriginalTransitLineItemData" >
    insert into t_sinopec_original_transit_line_item_data
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="sinopecOriginalDataId != null" >
        sinopec_original_data_id,
      </if>
      <if test="logisticsOrderNumber != null" >
        logistics_order_number,
      </if>
      <if test="businessOrderNumber != null" >
        business_order_number,
      </if>
      <if test="businessOrderValidEnd != null" >
        business_order_valid_end,
      </if>
      <if test="certificateTime != null" >
        certificate_time,
      </if>
      <if test="existState != null" >
        exist_state,
      </if>
      <if test="materialAttribute != null" >
        material_attribute,
      </if>
      <if test="makeDrug != null" >
        make_drug,
      </if>
      <if test="supportAuthOnline != null" >
        support_auth_online,
      </if>
      <if test="packingName != null" >
        packing_name,
      </if>
      <if test="packingSpecification != null" >
        packing_specification,
      </if>
      <if test="productionEnterpriseType != null" >
        production_enterprise_type,
      </if>
      <if test="productionEnterpriseName != null" >
        production_enterprise_name,
      </if>
      <if test="productionEnterpriseCode != null" >
        production_enterprise_code,
      </if>
      <if test="batch != null" >
        batch,
      </if>
      <if test="materialMainCategory != null" >
        material_main_category,
      </if>
      <if test="materialMainCategoryCode != null" >
        material_main_category_code,
      </if>
      <if test="materialCategory != null" >
        material_category,
      </if>
      <if test="materialCategoryCode != null" >
        material_category_code,
      </if>
      <if test="materialName != null" >
        material_name,
      </if>
      <if test="materialCode != null" >
        material_code,
      </if>
      <if test="associatedCount != null" >
        associated_count,
      </if>
      <if test="unitId != null" >
        unit_id,
      </if>
      <if test="unitName != null" >
        unit_name,
      </if>
      <if test="clientCode != null" >
        client_code,
      </if>
      <if test="clientName != null" >
        client_name,
      </if>
      <if test="transportPrice != null" >
        transport_price,
      </if>
      <if test="estimateTransportFee != null" >
        estimate_transport_fee,
      </if>
      <if test="freighterType != null" >
        freighter_type,
      </if>
      <if test="freighterCode != null" >
        freighter_code,
      </if>
      <if test="freighterName != null" >
        freighter_name,
      </if>
      <if test="originPortLocationType != null" >
        origin_port_location_type,
      </if>
      <if test="originPortLocationCode != null" >
        origin_port_location_code,
      </if>
      <if test="originPortLocationName != null" >
        origin_port_location_name,
      </if>
      <if test="originPortDetailAddress != null" >
        origin_port_detail_address,
      </if>
      <if test="originPortProvinceCode != null" >
        origin_port_province_code,
      </if>
      <if test="originPortProvinceName != null" >
        origin_port_province_name,
      </if>
      <if test="originPortCityCode != null" >
        origin_port_city_code,
      </if>
      <if test="originPortCityName != null" >
        origin_port_city_name,
      </if>
      <if test="originPortCountyCode != null" >
        origin_port_county_code,
      </if>
      <if test="originPortCountyName != null" >
        origin_port_county_name,
      </if>
      <if test="originPortTownCode != null" >
        origin_port_town_code,
      </if>
      <if test="originPortTownName != null" >
        origin_port_town_name,
      </if>
      <if test="originPortLatitude != null" >
        origin_port_latitude,
      </if>
      <if test="originPortLongitude != null" >
        origin_port_longitude,
      </if>
      <if test="destinationPortLocationType != null" >
        destination_port_location_type,
      </if>
      <if test="destinationPortLocationCode != null" >
        destination_port_location_code,
      </if>
      <if test="destinationPortLocationName != null" >
        destination_port_location_name,
      </if>
      <if test="destinationPortDetailAddress != null" >
        destination_port_detail_address,
      </if>
      <if test="destinationPortProvinceCode != null" >
        destination_port_province_code,
      </if>
      <if test="destinationPortProvinceName != null" >
        destination_port_province_name,
      </if>
      <if test="destinationPortCityCode != null" >
        destination_port_city_code,
      </if>
      <if test="destinationPortCityName != null" >
        destination_port_city_name,
      </if>
      <if test="destinationPortCountyCode != null" >
        destination_port_county_code,
      </if>
      <if test="destinationPortCountyName != null" >
        destination_port_county_name,
      </if>
      <if test="destinationPortTownCode != null" >
        destination_port_town_code,
      </if>
      <if test="destinationPortTownName != null" >
        destination_port_town_name,
      </if>
      <if test="destinationPortLatitude != null" >
        destination_port_latitude,
      </if>
      <if test="destinationPortLongitude != null" >
        destination_port_longitude,
      </if>
      <if test="receiverCode != null" >
        receiver_code,
      </if>
      <if test="receiverName != null" >
        receiver_name,
      </if>
      <if test="receiverType != null" >
        receiver_type,
      </if>
      <if test="receiverPhone != null" >
        receiver_phone,
      </if>
      <if test="otherfee != null" >
        otherFee,
      </if>
      <if test="remark1 != null" >
        remark1,
      </if>
      <if test="remark2 != null" >
        remark2,
      </if>
      <if test="remark3 != null" >
        remark3,
      </if>
      <if test="remark4 != null" >
        remark4,
      </if>
      <if test="remark5 != null" >
        remark5,
      </if>
      <if test="remark6 != null" >
        remark6,
      </if>
      <if test="remark7 != null" >
        remark7,
      </if>
      <if test="remark8 != null" >
        remark8,
      </if>
      <if test="remark9 != null" >
        remark9,
      </if>
      <if test="remark10 != null" >
        remark10,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sinopecOriginalDataId != null" >
        #{sinopecOriginalDataId,jdbcType=BIGINT},
      </if>
      <if test="logisticsOrderNumber != null" >
        #{logisticsOrderNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessOrderNumber != null" >
        #{businessOrderNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessOrderValidEnd != null" >
        #{businessOrderValidEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="certificateTime != null" >
        #{certificateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="existState != null" >
        #{existState,jdbcType=INTEGER},
      </if>
      <if test="materialAttribute != null" >
        #{materialAttribute,jdbcType=INTEGER},
      </if>
      <if test="makeDrug != null" >
        #{makeDrug,jdbcType=INTEGER},
      </if>
      <if test="supportAuthOnline != null" >
        #{supportAuthOnline,jdbcType=INTEGER},
      </if>
      <if test="packingName != null" >
        #{packingName,jdbcType=VARCHAR},
      </if>
      <if test="packingSpecification != null" >
        #{packingSpecification,jdbcType=VARCHAR},
      </if>
      <if test="productionEnterpriseType != null" >
        #{productionEnterpriseType,jdbcType=INTEGER},
      </if>
      <if test="productionEnterpriseName != null" >
        #{productionEnterpriseName,jdbcType=VARCHAR},
      </if>
      <if test="productionEnterpriseCode != null" >
        #{productionEnterpriseCode,jdbcType=VARCHAR},
      </if>
      <if test="batch != null" >
        #{batch,jdbcType=VARCHAR},
      </if>
      <if test="materialMainCategory != null" >
        #{materialMainCategory,jdbcType=VARCHAR},
      </if>
      <if test="materialMainCategoryCode != null" >
        #{materialMainCategoryCode,jdbcType=VARCHAR},
      </if>
      <if test="materialCategory != null" >
        #{materialCategory,jdbcType=VARCHAR},
      </if>
      <if test="materialCategoryCode != null" >
        #{materialCategoryCode,jdbcType=VARCHAR},
      </if>
      <if test="materialName != null" >
        #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null" >
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="associatedCount != null" >
        #{associatedCount,jdbcType=DECIMAL},
      </if>
      <if test="unitId != null" >
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="unitName != null" >
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="clientCode != null" >
        #{clientCode,jdbcType=VARCHAR},
      </if>
      <if test="clientName != null" >
        #{clientName,jdbcType=VARCHAR},
      </if>
      <if test="transportPrice != null" >
        #{transportPrice,jdbcType=DECIMAL},
      </if>
      <if test="estimateTransportFee != null" >
        #{estimateTransportFee,jdbcType=DECIMAL},
      </if>
      <if test="freighterType != null" >
        #{freighterType,jdbcType=INTEGER},
      </if>
      <if test="freighterCode != null" >
        #{freighterCode,jdbcType=VARCHAR},
      </if>
      <if test="freighterName != null" >
        #{freighterName,jdbcType=VARCHAR},
      </if>
      <if test="originPortLocationType != null" >
        #{originPortLocationType,jdbcType=INTEGER},
      </if>
      <if test="originPortLocationCode != null" >
        #{originPortLocationCode,jdbcType=VARCHAR},
      </if>
      <if test="originPortLocationName != null" >
        #{originPortLocationName,jdbcType=VARCHAR},
      </if>
      <if test="originPortDetailAddress != null" >
        #{originPortDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="originPortProvinceCode != null" >
        #{originPortProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="originPortProvinceName != null" >
        #{originPortProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="originPortCityCode != null" >
        #{originPortCityCode,jdbcType=VARCHAR},
      </if>
      <if test="originPortCityName != null" >
        #{originPortCityName,jdbcType=VARCHAR},
      </if>
      <if test="originPortCountyCode != null" >
        #{originPortCountyCode,jdbcType=VARCHAR},
      </if>
      <if test="originPortCountyName != null" >
        #{originPortCountyName,jdbcType=VARCHAR},
      </if>
      <if test="originPortTownCode != null" >
        #{originPortTownCode,jdbcType=VARCHAR},
      </if>
      <if test="originPortTownName != null" >
        #{originPortTownName,jdbcType=VARCHAR},
      </if>
      <if test="originPortLatitude != null" >
        #{originPortLatitude,jdbcType=VARCHAR},
      </if>
      <if test="originPortLongitude != null" >
        #{originPortLongitude,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortLocationType != null" >
        #{destinationPortLocationType,jdbcType=INTEGER},
      </if>
      <if test="destinationPortLocationCode != null" >
        #{destinationPortLocationCode,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortLocationName != null" >
        #{destinationPortLocationName,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortDetailAddress != null" >
        #{destinationPortDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortProvinceCode != null" >
        #{destinationPortProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortProvinceName != null" >
        #{destinationPortProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortCityCode != null" >
        #{destinationPortCityCode,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortCityName != null" >
        #{destinationPortCityName,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortCountyCode != null" >
        #{destinationPortCountyCode,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortCountyName != null" >
        #{destinationPortCountyName,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortTownCode != null" >
        #{destinationPortTownCode,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortTownName != null" >
        #{destinationPortTownName,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortLatitude != null" >
        #{destinationPortLatitude,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortLongitude != null" >
        #{destinationPortLongitude,jdbcType=VARCHAR},
      </if>
      <if test="receiverCode != null" >
        #{receiverCode,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null" >
        #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="receiverType != null" >
        #{receiverType,jdbcType=INTEGER},
      </if>
      <if test="receiverPhone != null" >
        #{receiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="otherfee != null" >
        #{otherfee,jdbcType=DECIMAL},
      </if>
      <if test="remark1 != null" >
        #{remark1,jdbcType=VARCHAR},
      </if>
      <if test="remark2 != null" >
        #{remark2,jdbcType=VARCHAR},
      </if>
      <if test="remark3 != null" >
        #{remark3,jdbcType=VARCHAR},
      </if>
      <if test="remark4 != null" >
        #{remark4,jdbcType=VARCHAR},
      </if>
      <if test="remark5 != null" >
        #{remark5,jdbcType=VARCHAR},
      </if>
      <if test="remark6 != null" >
        #{remark6,jdbcType=VARCHAR},
      </if>
      <if test="remark7 != null" >
        #{remark7,jdbcType=VARCHAR},
      </if>
      <if test="remark8 != null" >
        #{remark8,jdbcType=VARCHAR},
      </if>
      <if test="remark9 != null" >
        #{remark9,jdbcType=VARCHAR},
      </if>
      <if test="remark10 != null" >
        #{remark10,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TSinopecOriginalTransitLineItemData" >
    update t_sinopec_original_transit_line_item_data
    <set >
      <if test="sinopecOriginalDataId != null" >
        sinopec_original_data_id = #{sinopecOriginalDataId,jdbcType=BIGINT},
      </if>
      <if test="logisticsOrderNumber != null" >
        logistics_order_number = #{logisticsOrderNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessOrderNumber != null" >
        business_order_number = #{businessOrderNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessOrderValidEnd != null" >
        business_order_valid_end = #{businessOrderValidEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="certificateTime != null" >
        certificate_time = #{certificateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="existState != null" >
        exist_state = #{existState,jdbcType=INTEGER},
      </if>
      <if test="materialAttribute != null" >
        material_attribute = #{materialAttribute,jdbcType=INTEGER},
      </if>
      <if test="makeDrug != null" >
        make_drug = #{makeDrug,jdbcType=INTEGER},
      </if>
      <if test="supportAuthOnline != null" >
        support_auth_online = #{supportAuthOnline,jdbcType=INTEGER},
      </if>
      <if test="packingName != null" >
        packing_name = #{packingName,jdbcType=VARCHAR},
      </if>
      <if test="packingSpecification != null" >
        packing_specification = #{packingSpecification,jdbcType=VARCHAR},
      </if>
      <if test="productionEnterpriseType != null" >
        production_enterprise_type = #{productionEnterpriseType,jdbcType=INTEGER},
      </if>
      <if test="productionEnterpriseName != null" >
        production_enterprise_name = #{productionEnterpriseName,jdbcType=VARCHAR},
      </if>
      <if test="productionEnterpriseCode != null" >
        production_enterprise_code = #{productionEnterpriseCode,jdbcType=VARCHAR},
      </if>
      <if test="batch != null" >
        batch = #{batch,jdbcType=VARCHAR},
      </if>
      <if test="materialMainCategory != null" >
        material_main_category = #{materialMainCategory,jdbcType=VARCHAR},
      </if>
      <if test="materialMainCategoryCode != null" >
        material_main_category_code = #{materialMainCategoryCode,jdbcType=VARCHAR},
      </if>
      <if test="materialCategory != null" >
        material_category = #{materialCategory,jdbcType=VARCHAR},
      </if>
      <if test="materialCategoryCode != null" >
        material_category_code = #{materialCategoryCode,jdbcType=VARCHAR},
      </if>
      <if test="materialName != null" >
        material_name = #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null" >
        material_code = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="associatedCount != null" >
        associated_count = #{associatedCount,jdbcType=DECIMAL},
      </if>
      <if test="unitId != null" >
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="unitName != null" >
        unit_name = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="clientCode != null" >
        client_code = #{clientCode,jdbcType=VARCHAR},
      </if>
      <if test="clientName != null" >
        client_name = #{clientName,jdbcType=VARCHAR},
      </if>
      <if test="transportPrice != null" >
        transport_price = #{transportPrice,jdbcType=DECIMAL},
      </if>
      <if test="estimateTransportFee != null" >
        estimate_transport_fee = #{estimateTransportFee,jdbcType=DECIMAL},
      </if>
      <if test="freighterType != null" >
        freighter_type = #{freighterType,jdbcType=INTEGER},
      </if>
      <if test="freighterCode != null" >
        freighter_code = #{freighterCode,jdbcType=VARCHAR},
      </if>
      <if test="freighterName != null" >
        freighter_name = #{freighterName,jdbcType=VARCHAR},
      </if>
      <if test="originPortLocationType != null" >
        origin_port_location_type = #{originPortLocationType,jdbcType=INTEGER},
      </if>
      <if test="originPortLocationCode != null" >
        origin_port_location_code = #{originPortLocationCode,jdbcType=VARCHAR},
      </if>
      <if test="originPortLocationName != null" >
        origin_port_location_name = #{originPortLocationName,jdbcType=VARCHAR},
      </if>
      <if test="originPortDetailAddress != null" >
        origin_port_detail_address = #{originPortDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="originPortProvinceCode != null" >
        origin_port_province_code = #{originPortProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="originPortProvinceName != null" >
        origin_port_province_name = #{originPortProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="originPortCityCode != null" >
        origin_port_city_code = #{originPortCityCode,jdbcType=VARCHAR},
      </if>
      <if test="originPortCityName != null" >
        origin_port_city_name = #{originPortCityName,jdbcType=VARCHAR},
      </if>
      <if test="originPortCountyCode != null" >
        origin_port_county_code = #{originPortCountyCode,jdbcType=VARCHAR},
      </if>
      <if test="originPortCountyName != null" >
        origin_port_county_name = #{originPortCountyName,jdbcType=VARCHAR},
      </if>
      <if test="originPortTownCode != null" >
        origin_port_town_code = #{originPortTownCode,jdbcType=VARCHAR},
      </if>
      <if test="originPortTownName != null" >
        origin_port_town_name = #{originPortTownName,jdbcType=VARCHAR},
      </if>
      <if test="originPortLatitude != null" >
        origin_port_latitude = #{originPortLatitude,jdbcType=VARCHAR},
      </if>
      <if test="originPortLongitude != null" >
        origin_port_longitude = #{originPortLongitude,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortLocationType != null" >
        destination_port_location_type = #{destinationPortLocationType,jdbcType=INTEGER},
      </if>
      <if test="destinationPortLocationCode != null" >
        destination_port_location_code = #{destinationPortLocationCode,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortLocationName != null" >
        destination_port_location_name = #{destinationPortLocationName,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortDetailAddress != null" >
        destination_port_detail_address = #{destinationPortDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortProvinceCode != null" >
        destination_port_province_code = #{destinationPortProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortProvinceName != null" >
        destination_port_province_name = #{destinationPortProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortCityCode != null" >
        destination_port_city_code = #{destinationPortCityCode,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortCityName != null" >
        destination_port_city_name = #{destinationPortCityName,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortCountyCode != null" >
        destination_port_county_code = #{destinationPortCountyCode,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortCountyName != null" >
        destination_port_county_name = #{destinationPortCountyName,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortTownCode != null" >
        destination_port_town_code = #{destinationPortTownCode,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortTownName != null" >
        destination_port_town_name = #{destinationPortTownName,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortLatitude != null" >
        destination_port_latitude = #{destinationPortLatitude,jdbcType=VARCHAR},
      </if>
      <if test="destinationPortLongitude != null" >
        destination_port_longitude = #{destinationPortLongitude,jdbcType=VARCHAR},
      </if>
      <if test="receiverCode != null" >
        receiver_code = #{receiverCode,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null" >
        receiver_name = #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="receiverType != null" >
        receiver_type = #{receiverType,jdbcType=INTEGER},
      </if>
      <if test="receiverPhone != null" >
        receiver_phone = #{receiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="otherfee != null" >
        otherFee = #{otherfee,jdbcType=DECIMAL},
      </if>
      <if test="remark1 != null" >
        remark1 = #{remark1,jdbcType=VARCHAR},
      </if>
      <if test="remark2 != null" >
        remark2 = #{remark2,jdbcType=VARCHAR},
      </if>
      <if test="remark3 != null" >
        remark3 = #{remark3,jdbcType=VARCHAR},
      </if>
      <if test="remark4 != null" >
        remark4 = #{remark4,jdbcType=VARCHAR},
      </if>
      <if test="remark5 != null" >
        remark5 = #{remark5,jdbcType=VARCHAR},
      </if>
      <if test="remark6 != null" >
        remark6 = #{remark6,jdbcType=VARCHAR},
      </if>
      <if test="remark7 != null" >
        remark7 = #{remark7,jdbcType=VARCHAR},
      </if>
      <if test="remark8 != null" >
        remark8 = #{remark8,jdbcType=VARCHAR},
      </if>
      <if test="remark9 != null" >
        remark9 = #{remark9,jdbcType=VARCHAR},
      </if>
      <if test="remark10 != null" >
        remark10 = #{remark10,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TSinopecOriginalTransitLineItemData" >
    update t_sinopec_original_transit_line_item_data
    set sinopec_original_data_id = #{sinopecOriginalDataId,jdbcType=BIGINT},
      logistics_order_number = #{logisticsOrderNumber,jdbcType=VARCHAR},
      business_order_number = #{businessOrderNumber,jdbcType=VARCHAR},
      business_order_valid_end = #{businessOrderValidEnd,jdbcType=TIMESTAMP},
      certificate_time = #{certificateTime,jdbcType=TIMESTAMP},
      exist_state = #{existState,jdbcType=INTEGER},
      material_attribute = #{materialAttribute,jdbcType=INTEGER},
      make_drug = #{makeDrug,jdbcType=INTEGER},
      support_auth_online = #{supportAuthOnline,jdbcType=INTEGER},
      packing_name = #{packingName,jdbcType=VARCHAR},
      packing_specification = #{packingSpecification,jdbcType=VARCHAR},
      production_enterprise_type = #{productionEnterpriseType,jdbcType=INTEGER},
      production_enterprise_name = #{productionEnterpriseName,jdbcType=VARCHAR},
      production_enterprise_code = #{productionEnterpriseCode,jdbcType=VARCHAR},
      batch = #{batch,jdbcType=VARCHAR},
      material_main_category = #{materialMainCategory,jdbcType=VARCHAR},
      material_main_category_code = #{materialMainCategoryCode,jdbcType=VARCHAR},
      material_category = #{materialCategory,jdbcType=VARCHAR},
      material_category_code = #{materialCategoryCode,jdbcType=VARCHAR},
      material_name = #{materialName,jdbcType=VARCHAR},
      material_code = #{materialCode,jdbcType=VARCHAR},
      associated_count = #{associatedCount,jdbcType=DECIMAL},
      unit_id = #{unitId,jdbcType=INTEGER},
      unit_name = #{unitName,jdbcType=VARCHAR},
      client_code = #{clientCode,jdbcType=VARCHAR},
      client_name = #{clientName,jdbcType=VARCHAR},
      transport_price = #{transportPrice,jdbcType=DECIMAL},
      estimate_transport_fee = #{estimateTransportFee,jdbcType=DECIMAL},
      freighter_type = #{freighterType,jdbcType=INTEGER},
      freighter_code = #{freighterCode,jdbcType=VARCHAR},
      freighter_name = #{freighterName,jdbcType=VARCHAR},
      origin_port_location_type = #{originPortLocationType,jdbcType=INTEGER},
      origin_port_location_code = #{originPortLocationCode,jdbcType=VARCHAR},
      origin_port_location_name = #{originPortLocationName,jdbcType=VARCHAR},
      origin_port_detail_address = #{originPortDetailAddress,jdbcType=VARCHAR},
      origin_port_province_code = #{originPortProvinceCode,jdbcType=VARCHAR},
      origin_port_province_name = #{originPortProvinceName,jdbcType=VARCHAR},
      origin_port_city_code = #{originPortCityCode,jdbcType=VARCHAR},
      origin_port_city_name = #{originPortCityName,jdbcType=VARCHAR},
      origin_port_county_code = #{originPortCountyCode,jdbcType=VARCHAR},
      origin_port_county_name = #{originPortCountyName,jdbcType=VARCHAR},
      origin_port_town_code = #{originPortTownCode,jdbcType=VARCHAR},
      origin_port_town_name = #{originPortTownName,jdbcType=VARCHAR},
      origin_port_latitude = #{originPortLatitude,jdbcType=VARCHAR},
      origin_port_longitude = #{originPortLongitude,jdbcType=VARCHAR},
      destination_port_location_type = #{destinationPortLocationType,jdbcType=INTEGER},
      destination_port_location_code = #{destinationPortLocationCode,jdbcType=VARCHAR},
      destination_port_location_name = #{destinationPortLocationName,jdbcType=VARCHAR},
      destination_port_detail_address = #{destinationPortDetailAddress,jdbcType=VARCHAR},
      destination_port_province_code = #{destinationPortProvinceCode,jdbcType=VARCHAR},
      destination_port_province_name = #{destinationPortProvinceName,jdbcType=VARCHAR},
      destination_port_city_code = #{destinationPortCityCode,jdbcType=VARCHAR},
      destination_port_city_name = #{destinationPortCityName,jdbcType=VARCHAR},
      destination_port_county_code = #{destinationPortCountyCode,jdbcType=VARCHAR},
      destination_port_county_name = #{destinationPortCountyName,jdbcType=VARCHAR},
      destination_port_town_code = #{destinationPortTownCode,jdbcType=VARCHAR},
      destination_port_town_name = #{destinationPortTownName,jdbcType=VARCHAR},
      destination_port_latitude = #{destinationPortLatitude,jdbcType=VARCHAR},
      destination_port_longitude = #{destinationPortLongitude,jdbcType=VARCHAR},
      receiver_code = #{receiverCode,jdbcType=VARCHAR},
      receiver_name = #{receiverName,jdbcType=VARCHAR},
      receiver_type = #{receiverType,jdbcType=INTEGER},
      receiver_phone = #{receiverPhone,jdbcType=VARCHAR},
      otherFee = #{otherfee,jdbcType=DECIMAL},
      remark1 = #{remark1,jdbcType=VARCHAR},
      remark2 = #{remark2,jdbcType=VARCHAR},
      remark3 = #{remark3,jdbcType=VARCHAR},
      remark4 = #{remark4,jdbcType=VARCHAR},
      remark5 = #{remark5,jdbcType=VARCHAR},
      remark6 = #{remark6,jdbcType=VARCHAR},
      remark7 = #{remark7,jdbcType=VARCHAR},
      remark8 = #{remark8,jdbcType=VARCHAR},
      remark9 = #{remark9,jdbcType=VARCHAR},
      remark10 = #{remark10,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>