package com.logistics.management.webapi.controller.reserveapply.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Data
@ExcelIgnoreUnannotated
public class ReserveApplyListResponseDto {

	@ApiModelProperty("申请记录id")
	private String applyId = "";

	@ApiModelProperty("状态：-1 已撤销，0 待业务审核，1 待财务审核，2 已驳回，3 待打款，4 已打款")
	private String status = "";

	@ApiModelProperty("状态展示文本")
	@ExcelProperty(value = "状态")
	private String statusLabel = "";

	@ApiModelProperty("类型: 1 充值 2 垫付; 1.3.6 新增")
	private String type;

	@ApiModelProperty("类型文本")
	@ExcelProperty(value = "类型")
	private String typeLabel;

	@ApiModelProperty("申请单号")
	@ExcelProperty(value = "申请单号")
	private String applyCode = "";

	@ApiModelProperty("司机")
	@ExcelProperty(value = "司机")
	private String driver = "";

	@ApiModelProperty("收款账号")
	@ExcelProperty(value = "收款账号")
	private String receiveAccount = "";

	@ApiModelProperty("申请金额")
	@ExcelProperty(value = "申请金额（元）")
	private String applyAmount = "";

	@ApiModelProperty("批准金额")
	@ExcelProperty(value = "批准金额（元）")
	private String approveAmount = "";

	@ApiModelProperty("备注")
	@ExcelProperty(value = "备注")
	private String remark = "";
}
