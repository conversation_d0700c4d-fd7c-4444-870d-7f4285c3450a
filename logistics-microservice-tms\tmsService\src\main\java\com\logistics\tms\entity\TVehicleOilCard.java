package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/08/04
*/
@Data
public class TVehicleOilCard extends BaseEntity {
    /**
    * 卡号
    */
    @ApiModelProperty("卡号")
    private String cardNumber;

    /**
    * 车辆id
    */
    @ApiModelProperty("车辆id")
    private Long vehicleId;

    /**
    * 车辆机构：1 自主，2 外部，3 自营
    */
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 绑定状态：0 未绑定，1 已绑定
    */
    @ApiModelProperty("绑定状态：0 未绑定，1 已绑定")
    private Integer status;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}