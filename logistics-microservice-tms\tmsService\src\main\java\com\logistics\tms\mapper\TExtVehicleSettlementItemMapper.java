package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TExtVehicleSettlementItem;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TExtVehicleSettlementItemMapper extends BaseMapper<TExtVehicleSettlementItem> {

    TExtVehicleSettlementItem getValidBySettlementId(@Param("settlementId")Long settlementId);

    List<TExtVehicleSettlementItem> getValidBySettlementIds(@Param("settlementIds")String settlementIds);
}