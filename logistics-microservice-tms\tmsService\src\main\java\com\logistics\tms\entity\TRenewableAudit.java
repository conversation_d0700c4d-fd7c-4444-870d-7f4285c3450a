package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/10/14
*/
@Data
public class TRenewableAudit extends BaseEntity {
    /**
    * 需求单ID
    */
    @ApiModelProperty("需求单ID")
    private Long demandOrderId;

    /**
    * 需求单号
    */
    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    /**
    * 新生订单单号
    */
    @ApiModelProperty("新生订单单号")
    private String renewableOrderCode;

    /**
    * 订单状态：0 待指派，1 待确认，2 待审核，3 已审核 4 已取消
    */
    @ApiModelProperty("订单状态：0 待指派，1 待确认，2 待审核，3 已审核 4 已取消")
    private Integer status;

    /**
    * 业务类型：1 公司，2 个人
    */
    @ApiModelProperty("业务类型：1 公司，2 个人")
    private Integer businessType;

    /**
    * 订单来源：1 新生同步，2 司机下单
    */
    @ApiModelProperty("订单来源：1 新生同步，2 司机下单")
    private Integer source;

    /**
    * 委托总数量
    */
    @ApiModelProperty("委托总数量")
    private BigDecimal goodsAmountTotal;

    /**
    * 已确认委托总数量(KG)
    */
    @ApiModelProperty("已确认委托总数量(KG)")
    private BigDecimal verifiedGoodsAmountTotal;

    /**
    * 车辆id
    */
    @ApiModelProperty("车辆id")
    private Long vehicleId;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 司机id
    */
    @ApiModelProperty("司机id")
    private Long staffId;

    /**
    * 人员机构：1 自主，2 外部，3 自营
    */
    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    /**
    * 司机姓名
    */
    @ApiModelProperty("司机姓名")
    private String staffName;

    /**
    * 手机号(加密)
    */
    @ApiModelProperty("手机号(加密)")
    private String staffMobile;

    /**
    * 下单时间
    */
    @ApiModelProperty("下单时间")
    private Date publishTime;

    /**
    * 乐橘新生客户名称（企业）
    */
    @ApiModelProperty("乐橘新生客户名称（企业）")
    private String customerName;

    /**
    * 乐橘新生客户姓名（个人）
    */
    @ApiModelProperty("乐橘新生客户姓名（个人）")
    private String customerUserName;

    /**
    * 乐橘新生客户手机号(加密)（个人）
    */
    @ApiModelProperty("乐橘新生客户手机号(加密)（个人）")
    private String customerUserMobile;

    /**
    * 下单人
    */
    @ApiModelProperty("下单人")
    private String publishUserName;

    /**
    * 下单人手机号(加密)
    */
    @ApiModelProperty("下单人手机号(加密)")
    private String publishUserMobile;

    /**
    * 审核时间
    */
    @ApiModelProperty("审核时间")
    private Date auditTime;

    /**
    * 审核人
    */
    @ApiModelProperty("审核人")
    private String auditorName;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}