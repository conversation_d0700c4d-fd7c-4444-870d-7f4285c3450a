package com.logistics.management.webapi.base.enums;

/**
 * @author: wjf
 * @date: 2019/11/4 16:56
 */
public enum NumberConversionEnum {
    DEFAULT_NULL("",""),
    ONE("01", "一"),
    TWO("02", "二"),
    THREE("03", "三"),
    FOUR("04", "四"),
    FIVE("05", "五"),
    SEX("06", "六"),
    SEVEN("07", "七"),
    EIGHT("08", "八"),
    NINE("09", "九"),
    TEN("10", "十"),
    ELVEN("11", "十一"),
    TWELVE("12", "十二"),
    ;

    private String key;
    private String value;

    NumberConversionEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static NumberConversionEnum getEnum(String key) {
        for (NumberConversionEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT_NULL;
    }
}
