package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DemandOrderResponseModel {
    @ApiModelProperty("需求单ID")
    private Long demandId;
    @ApiModelProperty("委托单状态：1000待调度 2000部分调度 3000调度完成 - 用于列表计算需求单实际货主费用使用")
    private Integer useStatus;
    @ApiModelProperty("委托单状态：500待发布 1000待调度 2000部分调度 3000调度完成 4000待签收 5000已签收")
    private Integer status;
    @ApiModelProperty("是否取消 1 是 0 否")
    private Integer ifCancel;
    @ApiModelProperty("是否放空 1 是 0 否")
    private Integer ifEmpty;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("需求生成人")
    private String publishName;
    @ApiModelProperty("下单时间")
    private Date publishTime;
    @ApiModelProperty("凭证日期")
    private Date ticketTime;

    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;
    @ApiModelProperty("合同价类型：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer contractPriceType;
    @ApiModelProperty("合同价")
    private BigDecimal contractPrice;
    @ApiModelProperty("预计合同价类型：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer exceptContractPriceType;
    @ApiModelProperty("预计合同价")
    private BigDecimal exceptContractPrice;
    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;
    @ApiModelProperty("委托数量")
    private BigDecimal goodsAmount;
    @ApiModelProperty("已安排数量")
    private BigDecimal arrangedAmount;
    @ApiModelProperty("未安排数量")
    private BigDecimal notArrangedAmount;
    @ApiModelProperty("退回数量")
    private BigDecimal backAmount;
    @ApiModelProperty("备注")
    private String remark;

    private String userName;
    private Long loadProvinceId;
    private String loadProvinceName;
    private Long loadCityId;
    private String loadCityName;
    private Long loadAreaId;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    private String consignorName;
    private String consignorMobile;
    private Date expectedLoadTime;
    private Long unloadProvinceId;
    private String unloadProvinceName;
    private Long unloadCityId;
    private String unloadCityName;
    private Long unloadAreaId;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;
    private String receiverName;
    private String receiverMobile;
    private Date expectedUnloadTime;

    //货主信息
    @ApiModelProperty("货主公司ID")
    private Long companyEntrustId;
    @ApiModelProperty("货主公司名称")
    private String companyEntrustName;
    @ApiModelProperty("上游客户")
    private String upstreamCustomer;

    //车主信息
    @ApiModelProperty("车主公司ID")
    private Long companyCarrierId;
    @ApiModelProperty("车主公司类型")
    private Integer companyCarrierType;
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName;
    @ApiModelProperty("车主联系人id")
    private Long carrierContactId;
    @ApiModelProperty("车主联系人")
    private String carrierContactName;
    @ApiModelProperty("车主联系人电话")
    private String carrierContactMobile;

    @ApiModelProperty("取消原因")
    private String cancelReason;
    @ApiModelProperty("委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件")
    private Integer source;

    @ApiModelProperty("预计货主费用")
    private BigDecimal expectEntrustFee;
    @ApiModelProperty("实际货主费用")
    private BigDecimal actualEntrustFee;
    @ApiModelProperty("结算类型 1 单价 2 一口价")
    private Integer priceType;

    @ApiModelProperty("是否加急：0 否，1 是")
    private Integer ifUrgent;

    @ApiModelProperty("生产企业")
    private String manufacturerName;
    @ApiModelProperty("物料运输组")
    private String itemTransGroupName;
    @ApiModelProperty("包装规格")
    private String itemPackSpecName;
    @ApiModelProperty("是否异常：0 否，1 是")
    private Integer ifObjection;
    @ApiModelProperty("是否异常（中石化推送单子）：0 否，1 是")
    private Integer ifObjectionSinopec;
    @ApiModelProperty("下单类型：10 发布，20 拉取，21 推送")
    private Integer orderType;
    @ApiModelProperty("中石化订单号")
    private String sinopecOrderNo;
    @ApiModelProperty("负责人姓名（调度员姓名）")
    private String dispatcherName;
    @ApiModelProperty("负责人电话（调度员电话）")
    private String dispatcherPhone;
    @ApiModelProperty("是否网货,  0: 否 1: 是")
    private Integer sinopecOnlineGoodsFlag;
    @ApiModelProperty("货主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer settlementTonnage;

    @ApiModelProperty("需求单货物列表")
    private List<DemandOrderGoodsResponseModel> goodsResponseModels;
}

