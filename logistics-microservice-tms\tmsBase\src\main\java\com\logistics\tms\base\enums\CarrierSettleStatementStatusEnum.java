/**
 * Created by yun.<PERSON><PERSON> on 2017/12/12.
 */
package com.logistics.tms.base.enums;

import java.util.Optional;
import java.util.stream.Stream;

public enum CarrierSettleStatementStatusEnum {

	WAIT_FINISH(-3, "待完结"),
	NOT_RELATED(-2, "未关联"),
	WAIT_SUBMITTED(-1, "待提交"),
	WAIT_BUSINESS_AUDIT(0, "待业务审核"),
	WAIT_FINANCIAL_AUDIT(1, "待财务审核"),
	ACCOUNT_CHECKED(2, "已对账"),
	REJECT(3, "已驳回"),
	;

	private final Integer key;
	private final String value;

	CarrierSettleStatementStatusEnum(Integer key, String value) {
		this.key = key;
		this.value = value;
	}

	public Integer getKey() {
		return key;
	}

	public String getValue() {
		return value;
	}

	public static Optional<CarrierSettleStatementStatusEnum> getEnumByKey(Integer key) {
		return Stream.of(values())
				.filter(f -> f.getKey().equals(key))
				.findFirst();
	}
}