package com.logistics.management.webapi.controller.region.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/10/18 9:57
 */
@Data
public class SearchRegionResponseDto {
    @ApiModelProperty("物流大区ID")
    private String regionId = "";
    @ApiModelProperty("是否禁用 1 启用  0 禁用")
    private String enabled = "";
    @ApiModelProperty("是否禁用 文本")
    private String enabledLabel = "";
    @ApiModelProperty("大区名称")
    private String regionName = "";
    @ApiModelProperty("大区负责人姓名+手机号")
    private String contactName = "";
    @ApiModelProperty("操作人")
    private String lastModifiedBy = "";
    @ApiModelProperty("操作时间")
    private String lastModifiedTime = "";
}
