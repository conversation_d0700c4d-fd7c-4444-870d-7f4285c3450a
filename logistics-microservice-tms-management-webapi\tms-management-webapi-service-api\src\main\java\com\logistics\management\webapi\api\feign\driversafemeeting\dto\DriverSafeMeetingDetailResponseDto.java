package com.logistics.management.webapi.api.feign.driversafemeeting.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/11/4 9:26
 */
@Data
public class DriverSafeMeetingDetailResponseDto {
    @ApiModelProperty("学习例会关系id")
    private String safeMeetingRelationId="";
    @ApiModelProperty("学习例会id")
    private String safeMeetingId="";
    @ApiModelProperty("学习状态：0未学习，1已学习")
    private String status="";
    private String statusDesc="";
    @ApiModelProperty(value = "学习月份")
    private String period="";
    @ApiModelProperty("驾驶员姓名")
    private String staffName="";
    @ApiModelProperty("驾驶员手机号")
    private String staffMobile="";
    @ApiModelProperty(value = "学习标题")
    private String title="";
    @ApiModelProperty(value = "学习时间")
    private String studyTime="";
    @ApiModelProperty(value = "签字时间")
    private String signTime="";
    @ApiModelProperty(value = "驾驶员图片")
    private String staffDriverImageUrl="";
    @ApiModelProperty(value = "签字图片")
    private String signImageUrl="";
    @ApiModelProperty("人员机构 1 自主 2外部 3 自营")
    private String staffProperty = "";
    @ApiModelProperty("人员机构展示文本")
    private String staffPropertyLabel = "";
}
