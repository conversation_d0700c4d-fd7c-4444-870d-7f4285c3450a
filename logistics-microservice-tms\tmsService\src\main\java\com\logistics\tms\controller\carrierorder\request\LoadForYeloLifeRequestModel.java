package com.logistics.tms.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/22
 */
@Data
public class LoadForYeloLifeRequestModel {

	@ApiModelProperty(value = "运单ID")
	private Long carrierOrderId;

	@ApiModelProperty(value = "货物列表")
	private List<LoadGoodsForYeloLifeRequestModel> goodsList;

	@ApiModelProperty(value = "现场图片列表")
	private List<String> siteImgList;

	@ApiModelProperty(value = "出库单列表")
	private List<String> outImgList;
}
