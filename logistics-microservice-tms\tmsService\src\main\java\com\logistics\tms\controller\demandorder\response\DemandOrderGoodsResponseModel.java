package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DemandOrderGoodsResponseModel {

    @ApiModelProperty("需求单货物id")
    private Long demandOrderGoodsId;

    @ApiModelProperty("需求单id")
    private Long demandOrderId;

    @ApiModelProperty("sku编号")
    private String skuCode;

    @ApiModelProperty("货物名")
    private String goodsName;

    @ApiModelProperty("大类")
    private String categoryName;

    @ApiModelProperty("货物尺寸")
    private String goodsSize;

    @ApiModelProperty("长")
    private Integer length;

    @ApiModelProperty("宽")
    private Integer width;

    @ApiModelProperty("高")
    private Integer height;

    @ApiModelProperty("货物数量")
    private BigDecimal goodsAmountNumber;

    @ApiModelProperty("货物体积")
    private Integer goodsAmountVolume;

    @ApiModelProperty("货物预计重量")
    private BigDecimal goodsAmountExpectWeight;

    @ApiModelProperty("已安排货物体积")
    private Integer arrangedAmountVolume;

    @ApiModelProperty("已安排货物重量")
    private BigDecimal arrangedAmountExpectWeight;

    @ApiModelProperty("已安排货物数量")
    private BigDecimal arrangedAmountNumber;

    @ApiModelProperty("未安排货物体积")
    private Integer notArrangedAmountVolume;

    @ApiModelProperty("未安排货物重量")
    private BigDecimal notArrangedAmountExpectWeight;

    @ApiModelProperty("未安排货物数量")
    private BigDecimal notArrangedAmountNumber;

    @ApiModelProperty("退回货物数量")
    private BigDecimal backAmountNumber;

    @ApiModelProperty("退回货物体积")
    private Integer backAmountVolume;

    @ApiModelProperty("退回货物预计数量")
    private BigDecimal backAmountExpectWeight;
}
