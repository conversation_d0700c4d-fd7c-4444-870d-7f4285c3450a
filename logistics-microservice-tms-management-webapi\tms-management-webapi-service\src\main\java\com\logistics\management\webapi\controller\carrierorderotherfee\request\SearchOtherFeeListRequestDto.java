package com.logistics.management.webapi.controller.carrierorderotherfee.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchOtherFeeListRequestDto extends AbstractPageForm<SearchOtherFeeListRequestDto> {

    @ApiModelProperty(value = "主表id 导出使用")
    private String carrierOrderOtherFeeIds;

    @ApiModelProperty(value = "（3.16.0修改）费用状态：1 待提交，2 待审核，3 已驳回，4 已撤销，5 已审核")
    private String auditStatus;

    @ApiModelProperty(value = "费用类型：1 短驳费，2 保管费，3 装卸费，4 压车费，5 质量处罚费，6 其他杂费")
    private String feeType;

    @ApiModelProperty(value = "车主")
    private String companyCarrierName;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;

    @ApiModelProperty(value = "司机")
    private String driver;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty(value = "最新操作人")
    private String lastModifiedBy;

    @ApiModelProperty(value = "操作开始时间")
    private String lastModifiedTimeStart;

    @ApiModelProperty(value = "操作结束时间")
    private String lastModifiedTimeEnd;

    @ApiModelProperty("1.3.7新增；项目标签：1 石化板块，2 轮胎板块，3 涂料板块，4 其他")
    private String projectLabel;

    /**
     *  提货地址 v2.43
     */
    @ApiModelProperty(value = "提货地址 v2.43")
    private String loadAddress;

    /**
     * 卸货地址 v2.43
     */
    @ApiModelProperty(value = "卸货地址 v2.43")
    private String unloadAddress;


    /**
     * 运单生成时间 v2.43
     */
    @ApiModelProperty(value = "运单生成时间 v2.43")
    private String createdTimeStart;

    /**
     * 运单生成时间 v2.43
     */
    @ApiModelProperty(value = "运单生成时间 v2.43")
    private String createdTimeEnd;

    /**
     * 委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨 v2.43
     */
    @ApiModelProperty(value = "委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨 v2.43")
    private String demandOrderEntrustType;
}
