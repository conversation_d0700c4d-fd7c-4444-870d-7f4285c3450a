package com.logistics.management.webapi.api.impl.vehicleassetmanagement.mapper;

import com.logistics.management.webapi.api.feign.vehicleassetmanagement.dto.ExportVehicleBasicInfoResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.tms.api.feign.vehicleassetmanagement.model.ExportVehicleBasicInfoResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * @Author: sj
 * @Date: 2019/7/11 19:59
 */
public class ExportVehicleBasicInfoListMapping extends MapperMapping<ExportVehicleBasicInfoResponseModel,ExportVehicleBasicInfoResponseDto> {
    @Override
    public void configure() {
        ExportVehicleBasicInfoResponseModel resource = this.getSource();
        ExportVehicleBasicInfoResponseDto destination = this.getDestination();
        if(resource!=null){
            destination.setCompanyCarrierName(CompanyTypeEnum.COMPANY.getKey().equals(resource.getCompanyCarrierType()) ?
                    resource.getCompanyCarrierName() :
                    resource.getCarrierContactName() + " " + resource.getCarrierContactPhone());

            destination.setUsagePropertyLabel(VehicleUsagePropertyEnum.getEnum(resource.getUsageProperty()).getValue());
            destination.setIfInstallGpsLabel(IfInstallGpsEnum.getEnum(resource.getIfInstallGps()).getValue());
            destination.setIfAccessSinopecLabel(IfAccessSinopecEnum.getEnum(resource.getIfAccessSinopec()).getValue());
            destination.setVehiclePropertyLabel(VehiclePropertyEnum.getEnum(resource.getVehicleProperty()).getValue());
            destination.setEmissionStandard(EmissionStandardTypeEnum.getEnum(resource.getEmissionStandardType()).getValue());

            //日期转换
            if(resource.getRegistrationDate()!=null){
                destination.setRegistrationDate(DateUtils.dateToString(resource.getRegistrationDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(resource.getDrivingIssueDate()!=null){
                destination.setDrivingIssueDate(DateUtils.dateToString(resource.getDrivingIssueDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(resource.getObsolescenceDate()!=null){
                destination.setObsolescenceDate(DateUtils.dateToString(resource.getObsolescenceDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(resource.getCheckVehicleValidDate()!=null){
                destination.setCheckVehicleValidDate(DateUtils.dateToString(resource.getCheckVehicleValidDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(resource.getIssueDate()!=null){
                destination.setIssueDate(DateUtils.dateToString(resource.getIssueDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(resource.getObtainDate()!=null){
                destination.setObtainDate(DateUtils.dateToString(resource.getObtainDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(resource.getCheckRoadValidDate()!=null){
                destination.setCheckRoadValidDate(DateUtils.dateToString(resource.getCheckRoadValidDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(resource.getAuthenticationStartTime()!=null){
                destination.setAuthenticationStartTime(DateUtils.dateToString(resource.getAuthenticationStartTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(resource.getAuthenticationExpireTime()!=null){
                destination.setAuthenticationExpireTime(DateUtils.dateToString(resource.getAuthenticationExpireTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(resource.getInstallTime()!=null){
                destination.setInstallTime(DateUtils.dateToString(resource.getInstallTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(resource.getEstimationDate()!=null){
                destination.setEstimationDate(DateUtils.dateToString(resource.getEstimationDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(resource.getPlateColor()!=null){
                destination.setPlateColor(PlateColorEnum.getEnum(resource.getPlateColor()).getValue());
            }
            destination.setTotalWeight(Optional.ofNullable(resource.getTotalWeight()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
            destination.setCurbWeight(Optional.ofNullable(resource.getCurbWeight()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
            destination.setTractionMassWeight(Optional.ofNullable(resource.getTractionMassWeight()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
            destination.setApprovedLoadWeight(Optional.ofNullable(resource.getApprovedLoadWeight()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
            destination.setTransportTonnage(Optional.ofNullable(resource.getTransportTonnage()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
            if(CommonConstant.ZERO.equals(destination.getTotalWeight())){
                destination.setTotalWeight(CommonConstant.BLANK_TEXT);
            }
            if(CommonConstant.ZERO.equals(destination.getCurbWeight())){
                destination.setCurbWeight(CommonConstant.BLANK_TEXT);
            }
            if(CommonConstant.ZERO.equals(destination.getTractionMassWeight())){
                destination.setTractionMassWeight(CommonConstant.BLANK_TEXT);
            }
            if(CommonConstant.ZERO.equals(destination.getApprovedLoadWeight())){
                destination.setApprovedLoadWeight(CommonConstant.BLANK_TEXT);
            }
            if(CommonConstant.ZERO.equals(destination.getTransportTonnage())){
                destination.setTransportTonnage(CommonConstant.BLANK_TEXT);
            }
            if(isNullForInteger(resource.getAuthorizedCarryingCapacity())){
                destination.setAuthorizedCarryingCapacity(CommonConstant.BLANK_TEXT);
            }
            if(isNullForInteger(resource.getLength())){
                destination.setLength(CommonConstant.BLANK_TEXT);
            }
            if(isNullForInteger(resource.getWidth())){
                destination.setWidth(CommonConstant.BLANK_TEXT);
            }
            if(isNullForInteger(resource.getHeight())){
                destination.setHeight(CommonConstant.BLANK_TEXT);
            }
            if(isNullForInteger(resource.getAxleNumber())){
                destination.setAxleNumber(CommonConstant.BLANK_TEXT);
            }
            if(isNullForInteger(resource.getDriveShaftNumber())){
                destination.setDriveShaftNumber(CommonConstant.BLANK_TEXT);
            }
            if(isNullForInteger(resource.getTiresNumber())){
                destination.setTiresNumber(CommonConstant.BLANK_TEXT);
            }

        }
    }

    private boolean isNullForInteger(Integer source){
        return source == null || CommonConstant.INTEGER_ZERO.equals(source);
    }
}
