<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TCompanyEntrustMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCompanyEntrust">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="company_short_name" jdbcType="VARCHAR" property="companyShortName" />
    <result column="settlement_tonnage" jdbcType="INTEGER" property="settlementTonnage" />
    <result column="if_audit" jdbcType="INTEGER" property="ifAudit" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="sign_mode" jdbcType="INTEGER" property="signMode" />
    <result column="sign_days" jdbcType="INTEGER" property="signDays" />
    <result column="enabled" jdbcType="INTEGER" property="enabled" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, company_short_name, settlement_tonnage, if_audit, type, sign_mode, 
    sign_days, enabled, source, remark, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_company_entrust
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_company_entrust
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCompanyEntrust">
    insert into t_company_entrust (id, company_id, company_short_name, 
      settlement_tonnage, if_audit, type, 
      sign_mode, sign_days, enabled, 
      source, remark, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT}, #{companyShortName,jdbcType=VARCHAR}, 
      #{settlementTonnage,jdbcType=INTEGER}, #{ifAudit,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, 
      #{signMode,jdbcType=INTEGER}, #{signDays,jdbcType=INTEGER}, #{enabled,jdbcType=INTEGER}, 
      #{source,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCompanyEntrust" keyProperty="id" useGeneratedKeys="true">
    insert into t_company_entrust
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyShortName != null">
        company_short_name,
      </if>
      <if test="settlementTonnage != null">
        settlement_tonnage,
      </if>
      <if test="ifAudit != null">
        if_audit,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="signMode != null">
        sign_mode,
      </if>
      <if test="signDays != null">
        sign_days,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyShortName != null">
        #{companyShortName,jdbcType=VARCHAR},
      </if>
      <if test="settlementTonnage != null">
        #{settlementTonnage,jdbcType=INTEGER},
      </if>
      <if test="ifAudit != null">
        #{ifAudit,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="signMode != null">
        #{signMode,jdbcType=INTEGER},
      </if>
      <if test="signDays != null">
        #{signDays,jdbcType=INTEGER},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCompanyEntrust">
    update t_company_entrust
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyShortName != null">
        company_short_name = #{companyShortName,jdbcType=VARCHAR},
      </if>
      <if test="settlementTonnage != null">
        settlement_tonnage = #{settlementTonnage,jdbcType=INTEGER},
      </if>
      <if test="ifAudit != null">
        if_audit = #{ifAudit,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="signMode != null">
        sign_mode = #{signMode,jdbcType=INTEGER},
      </if>
      <if test="signDays != null">
        sign_days = #{signDays,jdbcType=INTEGER},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCompanyEntrust">
    update t_company_entrust
    set company_id = #{companyId,jdbcType=BIGINT},
      company_short_name = #{companyShortName,jdbcType=VARCHAR},
      settlement_tonnage = #{settlementTonnage,jdbcType=INTEGER},
      if_audit = #{ifAudit,jdbcType=INTEGER},
      type = #{type,jdbcType=INTEGER},
      sign_mode = #{signMode,jdbcType=INTEGER},
      sign_days = #{signDays,jdbcType=INTEGER},
      enabled = #{enabled,jdbcType=INTEGER},
      source = #{source,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>