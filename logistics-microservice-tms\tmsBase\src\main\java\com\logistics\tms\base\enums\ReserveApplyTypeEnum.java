package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReserveApplyTypeEnum {
    RECHARGE_TYPE(1, "充值"),
    ADVANCE_TYPE(2, "垫付"),
    RED_CHARGE_REFUND_TYPE(3, "红冲退款"),
    ;
    private final Integer key;
    private final String value;

    public boolean isAdvance() {
        return this.equals(ADVANCE_TYPE);
    }
}
