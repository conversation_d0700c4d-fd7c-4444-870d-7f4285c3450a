package com.logistics.tms.controller.routeenquiry.response;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/7/9 13:49
 */
@Data
public class GetRouteEnquiryDetailForWebResponseModel {

    /**
     * 路线询价单表id
     */
    private Long routeEnquiryId;

    /**
     * 竞价单号
     */
    private String orderCode;

    /**
     * 竞价状态：1 待业务审核，2 待车主确认，3 待结算审核，4 完成竞价
     */
    private Integer status;

    /**
     * 是否取消：0 否，1 是
     */
    private Integer ifCancel;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 货物名称
     */
    private String goodsName;

    /**
     * 报价生效开始时间
     */
    private Date quoteStartTime;

    /**
     * 报价生效结束时间
     */
    private Date quoteEndTime;

    /**
     * 备注
     */
    private String remark;


    /**
     * 竞价详情-最新报价人
     */
    private String quoteOperator;

    /**
     * 竞价详情-最新报价时间
     */
    private Date quoteTime;

    /**
     * 竞价详情-地址列表
     */
    private List<GetRouteEnquiryDetailAddressListResponseModel> addressList;


    /**
     * 业务审核-审核时间
     */
    private Date auditTimeOne;

    /**
     * 业务审核-关联合同号
     */
    private String contractCode;

    /**
     * 业务审核-备注
     */
    private String auditRemarkOne;


    /**
     * 车主确认-上传人
     */
    private String uploadUserName;

    /**
     * 车主确认-上传时间
     */
    private Date uploadTime;

    /**
     * 车主确认-报价单附件
     */
    private List<String> quotationFileList;


    /**
     * 结算审核-审核时间
     */
    private Date auditTimeTwo;

    /**
     * 结算审核-备注
     */
    private String auditRemarkTwo;


    /**
     * 归档文件
     */
    private List<String> archivedFileList;
    
}
