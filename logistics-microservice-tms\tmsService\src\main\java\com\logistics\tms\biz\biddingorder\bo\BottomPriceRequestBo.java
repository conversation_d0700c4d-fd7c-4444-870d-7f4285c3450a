package com.logistics.tms.biz.biddingorder.bo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BottomPriceRequestBo {

    /**
     * 发货地省id
     */
    private Long loadProvinceId;
    /**
     * 发货地市id
     */
    private Long loadCityId;
    /**
     * 发货地区id
     */
    private Long loadAreaId;


    /**
     * 收货地省id
     */
    private Long unloadProvinceId;
    /**
     * 收货地市id
     */
    private Long unloadCityId;
    /**
     * 收货地区id
     */
    private Long unloadAreaId;

    /**
     * 承运范围（小）
     */
    private BigDecimal carriageScopeMin;

    /**
     * 承运范围（大）
     */
    private BigDecimal carriageScopeMax;

    /**
     * 去年年份
     */
    private Integer lastYear;




}
