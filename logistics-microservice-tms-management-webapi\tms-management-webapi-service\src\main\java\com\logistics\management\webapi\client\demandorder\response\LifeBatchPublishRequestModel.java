package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

@Data
public class LifeBatchPublishRequestModel {
    @ApiModelProperty(value = "需求单列表", required = true)
    @NotNull(message = "需求单信息不能为空")
    @Valid
    @Size(max = 10, message = "最多发布10条数据")
    private List<String> demandDtoList;


    @ApiModelProperty(value = "货主价格类型：1 单价(元/吨，元/件)，2 一口价(元),发布单个必填")
    private Integer contractPriceType;
    @ApiModelProperty("货主价格,发布单个可以是单价也可以是一口价,批量只能为单价")
    private BigDecimal contractPrice;

    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主", required = true)
    @NotBlank(message = "请选择接单车主类型")
    private Integer isOurCompany;
    @ApiModelProperty(value = "车主ID")
    private Long companyCarrierId;
    @ApiModelProperty(value = "车主价格：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer carrierPriceType;
    @ApiModelProperty(value = "车主价格,发布单个可以是单价也可以是一口价,批量为单价")
    private BigDecimal carrierPrice;

}
