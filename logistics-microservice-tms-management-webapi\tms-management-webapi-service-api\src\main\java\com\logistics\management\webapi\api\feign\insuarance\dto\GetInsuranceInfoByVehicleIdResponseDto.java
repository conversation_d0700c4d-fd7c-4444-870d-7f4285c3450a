package com.logistics.management.webapi.api.feign.insuarance.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/12/26 16:42
 */
@Data
public class GetInsuranceInfoByVehicleIdResponseDto {
    @ApiModelProperty(value = "商业险信息")
    private GetInsuranceInfoByVehicleIdDto commercialInsuranceInfo = new GetInsuranceInfoByVehicleIdDto();
    @ApiModelProperty(value = "交强险信息")
    private GetInsuranceInfoByVehicleIdDto compulsoryInsuranceInfo = new GetInsuranceInfoByVehicleIdDto();
    @ApiModelProperty(value = "货物险信息")
    private GetInsuranceInfoByVehicleIdDto cargoInsuranceInfo = new GetInsuranceInfoByVehicleIdDto();
}
