package com.logistics.management.webapi.controller.companyaccount.mapping;

import com.logistics.management.webapi.base.enums.EnabledEnum;
import com.logistics.management.webapi.client.companyaccount.response.SearchCompanyAccountResponseModel;
import com.logistics.management.webapi.controller.companyaccount.response.SearchCompanyAccountResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/6
 */
public class CompanyAccountSearchListMapping extends MapperMapping<SearchCompanyAccountResponseModel, SearchCompanyAccountResponseDto> {
	@Override
	public void configure() {
		SearchCompanyAccountResponseModel source = getSource();
		SearchCompanyAccountResponseDto destination = getDestination();

		//启用禁用
		destination.setEnabledLabel(EnabledEnum.getEnum(source.getEnabled()).getValue());
		//银行名+支行名
		destination.setBankAccountName(source.getBankAccountName() + ConverterUtils.toString(source.getBraBankName()));
	}
}
