package com.logistics.appapi.base.enums;

/**
 * 司机费用申请审核枚举
 * <p>
 * 审核状态：-1 待业务审核，0 待财务审核，1 已审核，2 已驳回，3 已撤销，4 已红冲
 */
public enum DriverCostAuditEnum {
    DEFAULT(-99, ""),
    WAIT_BUSINESS_AUDIT(-1, "待业务审核"),
    WAIT_FINANCIAL_AUDIT(0, "待财务审核"),
    AUDITED(1, "已审核"),
    REJECT(2, "已驳回"),
    REPEAL(3, "已撤销"),
    RED_CHARGE_REFUND(4, "已红冲"),
    ;

    private final Integer key;
    private final String value;

    DriverCostAuditEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static DriverCostAuditEnum getEnum(Integer key) {
        for (DriverCostAuditEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
