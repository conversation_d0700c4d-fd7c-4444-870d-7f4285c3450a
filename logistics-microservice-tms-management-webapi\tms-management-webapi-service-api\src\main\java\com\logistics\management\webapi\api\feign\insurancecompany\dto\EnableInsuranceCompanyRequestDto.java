package com.logistics.management.webapi.api.feign.insurancecompany.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: sj
 * @Date: 2019/5/29 14:30
 */
@Data
public class EnableInsuranceCompanyRequestDto implements Serializable{
    @ApiModelProperty("保险公司ID")
    private String insuranceCompanyId;
    @ApiModelProperty("禁用启用 1 启用 0 禁用")
    private String enabled;
}
