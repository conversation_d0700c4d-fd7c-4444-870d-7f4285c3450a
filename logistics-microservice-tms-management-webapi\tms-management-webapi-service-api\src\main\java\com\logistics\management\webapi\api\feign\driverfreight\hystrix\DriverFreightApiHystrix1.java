package com.logistics.management.webapi.api.feign.driverfreight.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.driverfreight.DriverFreightApi;
import com.logistics.management.webapi.api.feign.driverfreight.dto.DriverFreightListSearchRequestDto;
import com.logistics.management.webapi.api.feign.driverfreight.dto.DriverFreightListSearchResponseDto;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;


@Component
public class DriverFreightApiHystrix1 implements DriverFreightApi {


    @Override
    public Result<PageInfo<DriverFreightListSearchResponseDto>> driverFreightList(DriverFreightListSearchRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void exportDriverFreightList(DriverFreightListSearchRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }
}
