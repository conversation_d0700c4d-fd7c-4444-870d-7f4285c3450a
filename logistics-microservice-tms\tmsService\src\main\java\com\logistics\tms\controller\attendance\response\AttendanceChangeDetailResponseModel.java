package com.logistics.tms.controller.attendance.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class AttendanceChangeDetailResponseModel {

    @ApiModelProperty(value = "打卡变更申请ID")
    private Long attendanceChangeApplyId;

    @ApiModelProperty("考勤用户")
    private String staffName;

    @ApiModelProperty("考勤用户手机号")
    private String staffMobile;

    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    @ApiModelProperty("考勤日期")
    private Date attendanceDate;

    @ApiModelProperty("上班打卡时间")
    private Date onDutyPunchTime;

    @ApiModelProperty("上班打卡图片")
    private String onDutyPunchPic;

    @ApiModelProperty("下班打卡时间")
    private Date offDutyPunchTime;

    @ApiModelProperty("下班打卡图片")
    private String offDutyPunchPic;

    @ApiModelProperty("变更类型: 1 上班，2 下班")
    private Integer changeType;

    @ApiModelProperty("用户提交的变更时间")
    private Date changePunchTime;

    @ApiModelProperty("变更原因")
    private String changeReason;

    @ApiModelProperty("审核时间")
    private Date auditTime;

    @ApiModelProperty("审核状态: 1 已审核，2 已驳回")
    private Integer auditStatus;

    @ApiModelProperty("审核人")
    private String auditorName;

    @ApiModelProperty("审核备注")
    private String remark;
}
