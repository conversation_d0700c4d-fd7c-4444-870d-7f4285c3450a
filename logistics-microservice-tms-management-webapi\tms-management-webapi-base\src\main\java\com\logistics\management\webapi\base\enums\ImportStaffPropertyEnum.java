package com.logistics.management.webapi.base.enums;

/**
 * @Author: sj
 * @Date: 2019/10/12 14:34
 */
public enum ImportStaffPropertyEnum {
    INTERNAL (1,"自主","1"),
    OUTSIDE(2,"外部","2"),
    SELF(3,"自营","3"),
    ;

    private Integer key;
    private String value;
    private String strKey;

    ImportStaffPropertyEnum(Integer key, String value, String strKey) {
        this.key = key;
        this.value = value;
        this.strKey = strKey;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getStrKey(){ return strKey;}

    public static ImportStaffPropertyEnum getEnum(Integer key) {
        for (ImportStaffPropertyEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }

    public static ImportStaffPropertyEnum getEnumByValue(String value) {
        for (ImportStaffPropertyEnum t : values()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        return null;
    }
}
