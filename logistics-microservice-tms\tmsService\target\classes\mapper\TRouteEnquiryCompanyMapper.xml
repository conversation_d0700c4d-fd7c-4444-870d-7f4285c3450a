<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRouteEnquiryCompanyMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TRouteEnquiryCompany" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="route_enquiry_id" property="routeEnquiryId" jdbcType="BIGINT" />
    <result column="company_carrier_type" property="companyCarrierType" jdbcType="INTEGER" />
    <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT" />
    <result column="company_carrier_name" property="companyCarrierName" jdbcType="VARCHAR" />
    <result column="carrier_contact_id" property="carrierContactId" jdbcType="BIGINT" />
    <result column="carrier_contact_name" property="carrierContactName" jdbcType="VARCHAR" />
    <result column="carrier_contact_phone" property="carrierContactPhone" jdbcType="VARCHAR" />
    <result column="quote_status" property="quoteStatus" jdbcType="INTEGER" />
    <result column="quote_operator" property="quoteOperator" jdbcType="VARCHAR" />
    <result column="quote_operator_phone" property="quoteOperatorPhone" jdbcType="VARCHAR" />
    <result column="quote_time" property="quoteTime" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, route_enquiry_id, company_carrier_type, company_carrier_id, company_carrier_name, 
    carrier_contact_id, carrier_contact_name, carrier_contact_phone, quote_status, quote_operator, 
    quote_operator_phone, quote_time, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_route_enquiry_company
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_route_enquiry_company
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TRouteEnquiryCompany" >
    insert into t_route_enquiry_company (id, route_enquiry_id, company_carrier_type, 
      company_carrier_id, company_carrier_name, carrier_contact_id, 
      carrier_contact_name, carrier_contact_phone, 
      quote_status, quote_operator, quote_operator_phone, 
      quote_time, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{routeEnquiryId,jdbcType=BIGINT}, #{companyCarrierType,jdbcType=INTEGER}, 
      #{companyCarrierId,jdbcType=BIGINT}, #{companyCarrierName,jdbcType=VARCHAR}, #{carrierContactId,jdbcType=BIGINT}, 
      #{carrierContactName,jdbcType=VARCHAR}, #{carrierContactPhone,jdbcType=VARCHAR}, 
      #{quoteStatus,jdbcType=INTEGER}, #{quoteOperator,jdbcType=VARCHAR}, #{quoteOperatorPhone,jdbcType=VARCHAR}, 
      #{quoteTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TRouteEnquiryCompany" >
    insert into t_route_enquiry_company
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="routeEnquiryId != null" >
        route_enquiry_id,
      </if>
      <if test="companyCarrierType != null" >
        company_carrier_type,
      </if>
      <if test="companyCarrierId != null" >
        company_carrier_id,
      </if>
      <if test="companyCarrierName != null" >
        company_carrier_name,
      </if>
      <if test="carrierContactId != null" >
        carrier_contact_id,
      </if>
      <if test="carrierContactName != null" >
        carrier_contact_name,
      </if>
      <if test="carrierContactPhone != null" >
        carrier_contact_phone,
      </if>
      <if test="quoteStatus != null" >
        quote_status,
      </if>
      <if test="quoteOperator != null" >
        quote_operator,
      </if>
      <if test="quoteOperatorPhone != null" >
        quote_operator_phone,
      </if>
      <if test="quoteTime != null" >
        quote_time,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="routeEnquiryId != null" >
        #{routeEnquiryId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierType != null" >
        #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null" >
        #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null" >
        #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactId != null" >
        #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactName != null" >
        #{carrierContactName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactPhone != null" >
        #{carrierContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="quoteStatus != null" >
        #{quoteStatus,jdbcType=INTEGER},
      </if>
      <if test="quoteOperator != null" >
        #{quoteOperator,jdbcType=VARCHAR},
      </if>
      <if test="quoteOperatorPhone != null" >
        #{quoteOperatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="quoteTime != null" >
        #{quoteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TRouteEnquiryCompany" >
    update t_route_enquiry_company
    <set >
      <if test="routeEnquiryId != null" >
        route_enquiry_id = #{routeEnquiryId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierType != null" >
        company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null" >
        company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null" >
        company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactId != null" >
        carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactName != null" >
        carrier_contact_name = #{carrierContactName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactPhone != null" >
        carrier_contact_phone = #{carrierContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="quoteStatus != null" >
        quote_status = #{quoteStatus,jdbcType=INTEGER},
      </if>
      <if test="quoteOperator != null" >
        quote_operator = #{quoteOperator,jdbcType=VARCHAR},
      </if>
      <if test="quoteOperatorPhone != null" >
        quote_operator_phone = #{quoteOperatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="quoteTime != null" >
        quote_time = #{quoteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TRouteEnquiryCompany" >
    update t_route_enquiry_company
    set route_enquiry_id = #{routeEnquiryId,jdbcType=BIGINT},
      company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
      company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
      carrier_contact_name = #{carrierContactName,jdbcType=VARCHAR},
      carrier_contact_phone = #{carrierContactPhone,jdbcType=VARCHAR},
      quote_status = #{quoteStatus,jdbcType=INTEGER},
      quote_operator = #{quoteOperator,jdbcType=VARCHAR},
      quote_operator_phone = #{quoteOperatorPhone,jdbcType=VARCHAR},
      quote_time = #{quoteTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>