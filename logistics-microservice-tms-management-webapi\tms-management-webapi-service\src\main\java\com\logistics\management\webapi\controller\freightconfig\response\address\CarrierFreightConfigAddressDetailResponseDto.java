package com.logistics.management.webapi.controller.freightconfig.response.address;

import com.logistics.management.webapi.controller.freightconfig.response.ladder.CarrierFreightConfigPriceDesignResponseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierFreightConfigAddressDetailResponseDto {

    @ApiModelProperty(value = "路线计价配置Id")
    private String freightConfigAddressId = "";

    @ApiModelProperty(value = "发货省名字")
    private String fromProvinceName = "";

    @ApiModelProperty(value = "发货城市名字")
    private String fromCityName = "";

    @ApiModelProperty(value = "发货县区名字")
    private String fromAreaName = "";

    @ApiModelProperty(value = "发货地")
    private String fromAddress = "";

    @ApiModelProperty(value = "卸货省名字")
    private String toProvinceName = "";

    @ApiModelProperty(value = "卸货城市名字")
    private String toCityName = "";

    @ApiModelProperty(value = "卸货县区名字")
    private String toAreaName = "";

    @ApiModelProperty(value = "收货地")
    private String toAddress = "";

    @ApiModelProperty(value = "价格设计")
    private CarrierFreightConfigPriceDesignResponseDto priceDesign;
}
