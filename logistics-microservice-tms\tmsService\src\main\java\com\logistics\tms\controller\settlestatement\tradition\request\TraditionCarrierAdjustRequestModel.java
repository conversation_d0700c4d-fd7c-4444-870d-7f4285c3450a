package com.logistics.tms.controller.settlestatement.tradition.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class TraditionCarrierAdjustRequestModel {

    @ApiModelProperty(value = "对账单id")
    private Long settleStatementId;

    @ApiModelProperty(value = "调整费用符号：1 ‘+’， 2 ‘-’ ")
    private String adjustCostSymbol;

    @ApiModelProperty(value = "调整费用")
    private BigDecimal adjustCost;

    @ApiModelProperty(value = "调整理由1-100字符")
    private String adjustReason;
}
