package com.logistics.appapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/3/12 13:47
 */
@Data
public class CarrierOrderGoodsLoadUnloadRequestDto {

    @ApiModelProperty(value = "货物id",required = true)
    @NotBlank(message = "货物id不能为空")
    private String goodsId;

    @ApiModelProperty(value = "提货/卸货数量",required = true)
    @NotBlank(message = "数量不能为空")
    private String count;


    @ApiModelProperty(value = "货物的编码集合 v2.44", required = true)
    private List<LoadGoodsForYeloLifeRequestCodeDto> codeDtoList;


}
