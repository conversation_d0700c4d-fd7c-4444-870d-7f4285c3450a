/**
 * Created by yun<PERSON>zhou on 2017/12/12.
 */
package com.logistics.tms.base.enums;

public enum DateRemindTypeEnum {

    VEHICLE_LICENSE_DATE_VALIDATE(1, "行驶证检查有效期"),
    ROAD_TRANSPORT_DATE_VALIDATE(2, "车辆年审有效期"),
    GRADE_ESTIMATION_DATE_VALIDATE(3, "等级评定检查日期"),
    IDENTITY_NUMBER_VALID_DATE(4, "身份证期限"),
    DRIVING_LICENSE_DATE_VALIDATE(5, "驾照期限"),
    DRIVER_OCCUPATIONAL_DATE_VALIDATE(6, "从业资格证有效期"),
    INTEGRITY_EXAMINATION_DATE_VALIDATE(7, "诚信考核有效期"),
    CONTINUE_LEARNING_DATE_VALIDATE(8, "继续教育有效期"),
    INSURANCE_DATE_VALIDATE(9, "保险截止时间"),
    VEHICLE_OBSOLESCENSE_VALIDATE(10, "车辆强制报废期")


    ;

    private Integer key;
    private String value;

    DateRemindTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
