package com.logistics.tms.controller.vehicleassetmanagement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AddOrModifyVehicleGpsRecordRequestModel {
    @ApiModelProperty("车辆GPS安装记录Id")
    private Long vehicleGpsRecordId;
    @ApiModelProperty("GPS安装日期")
    private Date installTime;
    @ApiModelProperty("终端型号")
    private String terminalType;
    @ApiModelProperty("GPS终端SIM卡号")
    private String simNumber;
    @ApiModelProperty("GPS服务商名称")
    private String gpsServiceProvider;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("证件图片列表")
    private List<CertificationPicturesRequestModel> fileList;
}
