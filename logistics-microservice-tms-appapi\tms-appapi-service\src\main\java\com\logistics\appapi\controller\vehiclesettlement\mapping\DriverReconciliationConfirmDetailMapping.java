package com.logistics.appapi.controller.vehiclesettlement.mapping;

import com.logistics.appapi.client.vehiclesettlement.response.DriverReconciliationConfirmDetailResponseModel;
import com.logistics.appapi.controller.vehiclesettlement.response.DriverReconciliationConfirmDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

/**
 * @author：wjf
 * @date：2021/4/13 14:37
 */
public class DriverReconciliationConfirmDetailMapping extends MapperMapping<DriverReconciliationConfirmDetailResponseModel,DriverReconciliationConfirmDetailResponseDto> {

    private String imagePrefix;
    public DriverReconciliationConfirmDetailMapping(String imagePrefix ) {
        this.imagePrefix = imagePrefix;
    }

    @Override
    public void configure() {
        DriverReconciliationConfirmDetailResponseModel source = getSource();
        DriverReconciliationConfirmDetailResponseDto destination = getDestination();

        if (StringUtils.isNotBlank(source.getCommitImageUrl())){
            destination.setCommitImageUrl(imagePrefix + source.getCommitImageUrl());
        }
    }
}
