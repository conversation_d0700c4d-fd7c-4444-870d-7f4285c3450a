package com.logistics.tms.api.feign.driverfreight.model;
import com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class DriverFreightListSearchResponseModel {
    @ApiModelProperty("运单ID")
    private Long carrierOrderId;
    @ApiModelProperty("50000 待签收 60000 已签收")
    private Integer status ;
    @ApiModelProperty("是否放空 0 否 1 是")
    private Integer ifEmpty;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("需求单ID")
    private Long demandOrderId;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("车辆机构 1 自主，2 外部，3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("车辆id")
    private Long vehicleId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机名")
    private String driverName;
    @ApiModelProperty("司机手机")
    private String driverMobile;
    @ApiModelProperty
    private Integer dispatchFreightFeeType;
    @ApiModelProperty
    private BigDecimal dispatchFreightFee;
    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;
    @ApiModelProperty("多装多卸加价费用")
    private BigDecimal markupFee;
    @ApiModelProperty("预提")
    private BigDecimal expectAmount;
    @ApiModelProperty("实际结算数据")
    private BigDecimal unloadAmount;
    @ApiModelProperty("货物单位：1 件，2 吨，3方，4块")
    private Integer goodsUnit;
    @ApiModelProperty("实际签收时间")
    private Date signTime ;
    @ApiModelProperty("备注")
    private String remark ;
    @ApiModelProperty("调度人")
    private String dispatchUserName;
    @ApiModelProperty("运单生成时间")
    private Date dispatchTime ;

    @ApiModelProperty("货主公司类型")
    private Integer companyEntrustType;
    @ApiModelProperty("货主公司名称")
    private String companyEntrustName;
    @ApiModelProperty("货主联系人")
    private String entrustContactName;
    @ApiModelProperty("货主联系人电话")
    private String entrustContactMobile;
    @ApiModelProperty("车主id")
    private Long companyCarrierId;
    @ApiModelProperty("车主公司类型")
    private Integer companyCarrierType;
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName;
    @ApiModelProperty("车主联系人")
    private String carrierContactName;
    @ApiModelProperty("车主联系人电话")
    private String carrierContactMobile;

    @ApiModelProperty("委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件")
    private Integer demandOrderSource;

    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;

    @ApiModelProperty("司机临时费用")
    private BigDecimal driverOtherFee;

    private List<SearchCarrierOrderListGoodsInfoModel> goodsInfoList;

}
