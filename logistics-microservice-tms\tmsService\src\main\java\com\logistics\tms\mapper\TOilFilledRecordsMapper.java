package com.logistics.tms.mapper;

import com.logistics.tms.entity.TOilFilledRecords;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TOilFilledRecordsMapper extends BaseMapper<TOilFilledRecords> {

    List<TOilFilledRecords> listByOilFilledId(@Param("oilFilledId")Long oilFilledId);

    int batchInsert(@Param("list") List<TOilFilledRecords> list);
}