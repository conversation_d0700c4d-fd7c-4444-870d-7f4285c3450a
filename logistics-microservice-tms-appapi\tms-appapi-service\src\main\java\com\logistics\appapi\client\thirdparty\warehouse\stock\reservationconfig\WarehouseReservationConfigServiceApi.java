package com.logistics.appapi.client.thirdparty.warehouse.stock.reservationconfig;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.thirdparty.warehouse.stock.reservationconfig.hystrix.WarehouseReservationConfigServiceApiHystrix;
import com.logistics.appapi.client.thirdparty.warehouse.stock.reservationconfig.request.SearchReservationTimeByWarehouseRequestModel;
import com.logistics.appapi.client.thirdparty.warehouse.stock.reservationconfig.response.SearchReservationTimeByWarehouseResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2024/8/26 10:05
 */
@FeignClient(name = FeignClientName.WAREHOUSE_STOCK_SERVICES,
        fallback = WarehouseReservationConfigServiceApiHystrix.class)
public interface WarehouseReservationConfigServiceApi {

    /**
     * 查询仓库可用预约时间 (物流调用)
     */
    @PostMapping(value = "/service/reservationConfig/searchWarehouseReservation")
    Result<SearchReservationTimeByWarehouseResponseModel> searchReservationTimeByWarehouse(@RequestBody SearchReservationTimeByWarehouseRequestModel requestModel);
}
