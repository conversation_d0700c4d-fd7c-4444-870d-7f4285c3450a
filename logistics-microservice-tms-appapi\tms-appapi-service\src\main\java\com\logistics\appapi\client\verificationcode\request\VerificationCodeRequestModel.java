package com.logistics.appapi.client.verificationcode.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author：wjf
 * @date：2021/5/12 16:44
 */
@Data
public class VerificationCodeRequestModel {
    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("来源：1是来源运营平台,2客户前台,3是客户app")
    private Integer source;

    @ApiModelProperty("验证码类型：1平台人员登录,2客户登录,3平台人员找回密码，4客户注册，5上上签接口短信,6绑定手机号")
    private Integer verificationType;

    private String ip;
}
