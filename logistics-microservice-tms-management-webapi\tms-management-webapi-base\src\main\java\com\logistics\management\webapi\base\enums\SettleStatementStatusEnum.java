/**
 * Created by yun<PERSON>zhou on 2017/12/12.
 */
package com.logistics.management.webapi.base.enums;

/**
 * 对账单状态: -2 已撤销，-1 待提交，0 待业务审核，1 待财务审核，2 已对账，3 已驳回
 */
public enum SettleStatementStatusEnum {

	DEFAULT(-99, ""),
	CANCEL(-2, "已撤销"),
	WAIT_SUBMITTED(-1, "待提交"),
	WAIT_BUSINESS_AUDIT(0, "待业务审核"),
	WAIT_FINANCIAL_AUDIT(1, "待财务审核"),
	ACCOUNT_CHECKED(2, "已对账"),
	REJECT(3, "已驳回"),
	;

	private final Integer key;
	private final String value;

	SettleStatementStatusEnum(Integer key, String value) {
		this.key = key;
		this.value = value;
	}

	public static SettleStatementStatusEnum getEnum(Integer key) {
		for (SettleStatementStatusEnum t : values()) {
			if (t.getKey().equals(key)) {
				return t;
			}
		}
		return DEFAULT;
	}

	public Integer getKey() {
		return key;
	}

	public String getValue() {
		return value;
	}
}
