package com.logistics.management.webapi.controller.demandorder.mapping;

import com.logistics.management.webapi.base.enums.DemandOrderOrdersEnum;
import com.logistics.management.webapi.client.demandorder.response.DemandOrderOrderRelResponseModel;
import com.logistics.management.webapi.controller.demandorder.response.DemandOrderOrdersResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: fjh
 * @date: 2021/12/23
 */
public class DemandOrderOrdersMapping extends MapperMapping<DemandOrderOrderRelResponseModel, DemandOrderOrdersResponseDto> {
    @Override
    public void configure() {
        DemandOrderOrderRelResponseModel model = getSource();
        DemandOrderOrdersResponseDto dto = getDestination();

        if (model != null){
            dto.setTotalAmount(model.getTotalAmount().stripTrailingZeros().toPlainString());
            dto.setRelType(DemandOrderOrdersEnum.getEnum(model.getRelType()).getValue());
        }
    }
}
