package com.logistics.tms.controller.workordercenter.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2023/4/14 17:05
 */
@Data
public class WorkOrderListResponseModel {

	@ApiModelProperty("工单id")
	private Long workOrderId;

	@ApiModelProperty("状态：0 待处理，10 处理中，20 已处理，30 已关闭，40 已撤销")
	private Integer status;

	@ApiModelProperty("工单类型：10 需求单，20 运单")
	private Integer workOrderType;

	@ApiModelProperty("需求单号")
	private String demandOrderCode;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("异常问题一级")
	private Integer anomalyTypeOne;

	@ApiModelProperty("异常问题二级")
	private Integer anomalyTypeTwo;

	@ApiModelProperty("车主类型 1公司 2 个人")
	private Integer companyType;

	@ApiModelProperty("车主公司名称")
	private String companyName;

	@ApiModelProperty("车主联系人姓名")
	private String contactName;

	@ApiModelProperty("车主联系人手机号")
	private String contactPhone;

	@ApiModelProperty("提报人")
	private String reportUserName;

	@ApiModelProperty("提报时间")
	private Date reportTime;

	@ApiModelProperty("处理时间")
	private Date solveTime;

	@ApiModelProperty("提报来源：1 后台，2 前台，3 小程序")
	private Integer reportSource;
}
