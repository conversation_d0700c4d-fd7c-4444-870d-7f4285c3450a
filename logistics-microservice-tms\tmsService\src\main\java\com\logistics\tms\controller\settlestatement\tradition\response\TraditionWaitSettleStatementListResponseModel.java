package com.logistics.tms.controller.settlestatement.tradition.response;

import com.logistics.tms.controller.dispatchorder.response.CarrierOrderGoodsModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/11
 */
@Data
public class TraditionWaitSettleStatementListResponseModel {

	@ApiModelProperty("运单id")
	private Long carrierOrderId;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("需求单id")
	private String demandOrderId;

	@ApiModelProperty("需求单号")
	private String demandOrderCode;

	@ApiModelProperty("调度单id")
	private Long dispatchOrderId;

	@ApiModelProperty("调度单号")
	private String dispatchOrderCode;

	@ApiModelProperty("车主id")
	private Long companyCarrierId;

	@ApiModelProperty(value = "车主公司类型：1 公司，2 个人")
	private Integer companyCarrierType;

	@ApiModelProperty("车主公司名称")
	private String companyCarrierName;

	@ApiModelProperty("车主联系人")
	private String carrierContactName;

	@ApiModelProperty("车主联系人电话")
	private String carrierContactMobile;

	@ApiModelProperty("货主公司名称")
	private String companyEntrustName;

	@ApiModelProperty("车牌号")
	private String vehicleNo;

	@ApiModelProperty("司机")
	private String driverName;

	@ApiModelProperty("司机")
	private String driverPhone;

	@ApiModelProperty("报价类型：1 单价  2 一口价")
	private Integer carrierPriceType;

	@ApiModelProperty("车主价格")
	private BigDecimal carrierPrice;

	@ApiModelProperty("车主运费")
	private BigDecimal carrierFee;

	@ApiModelProperty("预提数量")
	private BigDecimal expectAmount;

	@ApiModelProperty("提货数量")
	private BigDecimal loadAmount;

	@ApiModelProperty("卸货数量")
	private BigDecimal unloadAmount;

	@ApiModelProperty("签收数量")
	private BigDecimal signAmount;

	@ApiModelProperty("结算数量")
	private BigDecimal settlementAmount;

	@ApiModelProperty("货物单位：1 件，2 吨")
	private Integer goodsUnit;

	@ApiModelProperty("提货时间")
	private Date loadTime;

	@ApiModelProperty("卸货时间")
	private Date unloadTime;

	@ApiModelProperty("签收时间")
	private Date signTime;

	//发货地址信息
	@ApiModelProperty("发货仓库")
	private String loadWarehouse;
	@ApiModelProperty("发货省")
	private String loadProvinceName;
	@ApiModelProperty("发货市")
	private String loadCityName;
	@ApiModelProperty("发货区")
	private String loadAreaName;
	@ApiModelProperty("发货地址详情")
	private String loadDetailAddress;
	@ApiModelProperty("发件人")
	private String consignorName;
	@ApiModelProperty("发件人手机号")
	private String consignorMobile;

	//收货地址信息
	@ApiModelProperty("收货仓库")
	private String unloadWarehouse;
	@ApiModelProperty("收货省")
	private String unloadProvinceName;
	@ApiModelProperty("收货市")
	private String unloadCityName;
	@ApiModelProperty("收货区")
	private String unloadAreaName;
	@ApiModelProperty("收货地址详情")
	private String unloadDetailAddress;
	@ApiModelProperty("收件人")
	private String receiverName;
	@ApiModelProperty("收件人手机号")
	private String receiverMobile;

	@ApiModelProperty("凭证数")
	private Integer ticketCount;

	@ApiModelProperty("调度人")
	private String dispatchUserName;

	@ApiModelProperty("预计里程数")
	private BigDecimal expectMileage;

	@ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
	private Integer carrierSettlement;

	@ApiModelProperty("运单货物")
	private List<CarrierOrderGoodsModel> goodsList;
}
