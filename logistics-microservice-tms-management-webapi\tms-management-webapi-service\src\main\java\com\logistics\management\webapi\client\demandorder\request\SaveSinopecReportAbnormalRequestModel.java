package com.logistics.management.webapi.client.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SaveSinopecReportAbnormalRequestModel {

	@ApiModelProperty("需求单id")
	private Long demandOrderId;

	@ApiModelProperty("发货省份id")
	private Long loadProvinceId;
	@ApiModelProperty("发货省份名字")
	private String loadProvinceName;
	@ApiModelProperty("发货城市id")
	private Long loadCityId;
	@ApiModelProperty("发货城市名字")
	private String loadCityName;
	@ApiModelProperty("发货县区id")
	private Long loadAreaId;
	@ApiModelProperty("发货县区名字")
	private String loadAreaName;
	@ApiModelProperty("发货详细地址")
	private String loadDetailAddress;

	@ApiModelProperty("收货省份id")
	private Long unloadProvinceId;
	@ApiModelProperty("收货省份名字")
	private String unloadProvinceName;
	@ApiModelProperty("收货城市id")
	private Long unloadCityId;
	@ApiModelProperty("收货城市名字")
	private String unloadCityName;
	@ApiModelProperty("收货县区id")
	private Long unloadAreaId;
	@ApiModelProperty("收货县区名字")
	private String unloadAreaName;
	@ApiModelProperty("收货详细地址")
	private String unloadDetailAddress;

	@ApiModelProperty(value = "调度人员姓名,2-20个字符")
	private String dispatcherName;
	@ApiModelProperty(value = "调度人员电话,1-50个字符")
	private String dispatcherPhone;
	@ApiModelProperty(value = "单价 范围1-1000,最多两位小数")
	private BigDecimal contractPrice;//固定是单价

	@ApiModelProperty(value = "异常类型：1 已报价，2 已取消")
	private Integer objectionType;

	@ApiModelProperty(value = "备注 最多100个字符")
	private String remark;
}