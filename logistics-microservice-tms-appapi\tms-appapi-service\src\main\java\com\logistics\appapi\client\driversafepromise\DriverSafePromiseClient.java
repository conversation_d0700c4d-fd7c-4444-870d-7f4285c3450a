package com.logistics.appapi.client.driversafepromise;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.driversafepromise.hystrix.DriverSafePromiseClientHystrix;
import com.logistics.appapi.client.driversafepromise.request.SafePromiseAppletDetailRequestModel;
import com.logistics.appapi.client.driversafepromise.request.SearchSafePromiseAppletListRequestModel;
import com.logistics.appapi.client.driversafepromise.request.UploadSafePromiseRequestModel;
import com.logistics.appapi.client.driversafepromise.response.SafePromiseAppletDetailResponseModel;
import com.logistics.appapi.client.driversafepromise.response.SearchSafePromiseAppletListResponseModel;
import com.logistics.appapi.client.driversafepromise.response.SummarySignSafePromiseAppletResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2024/3/14 14:09
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        fallback = DriverSafePromiseClientHystrix.class)
public interface DriverSafePromiseClient {

    @ApiOperation("小程序承诺书列表")
    @PostMapping(value = "/service/applet/safePromise/searchList")
    Result<PageInfo<SearchSafePromiseAppletListResponseModel>> searchAppletList(@RequestBody SearchSafePromiseAppletListRequestModel requestModel);

    @ApiOperation("小程序承诺书列表汇总")
    @PostMapping(value = "/service/applet/safePromise/searchAppletSummary")
    Result<SummarySignSafePromiseAppletResponseModel> searchAppletSummary(@RequestBody SearchSafePromiseAppletListRequestModel requestModel);

    @ApiOperation("小程序承诺书详情")
    @PostMapping(value = "/service/applet/safePromise/getDetail")
    Result<SafePromiseAppletDetailResponseModel> getAppletDetail(@RequestBody SafePromiseAppletDetailRequestModel requestModel);

    @ApiOperation("签订列表-上传/重新上传")
    @PostMapping(value = "/service/safePromise/uploadSafePromise")
    Result<Boolean> uploadSafePromise(@RequestBody UploadSafePromiseRequestModel requestModel);

}
