package com.logistics.management.webapi.api.feign.demandorderobjectionsinopec.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.demandorderobjectionsinopec.DemandOrderObjectionSinopecApi;
import com.logistics.management.webapi.api.feign.demandorderobjectionsinopec.dto.*;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2022/5/30 13:13
 */
@Component
public class DemandOrderObjectionSinopecApiHystrix implements DemandOrderObjectionSinopecApi {
    @Override
    public Result<PageInfo<SearchDemandOrderObjectionSinopecResponseDto>> searchSinopecObjection(SearchDemandOrderObjectionSinopecRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<GetSinopecObjectionDetailResponseDto> getSinopecObjectionDetail(GetSinopecObjectionDetailRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> sinopecObjectionAudit(SinopecObjectionAuditRequestDto requestDto) {
        return Result.timeout();
    }
}
