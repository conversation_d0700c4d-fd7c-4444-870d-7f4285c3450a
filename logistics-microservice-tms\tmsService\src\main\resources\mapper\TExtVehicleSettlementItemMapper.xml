<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TExtVehicleSettlementItemMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TExtVehicleSettlementItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ext_vehicle_settlement_id" jdbcType="BIGINT" property="extVehicleSettlementId" />
    <result column="payment_channel" jdbcType="VARCHAR" property="paymentChannel" />
    <result column="payment_no" jdbcType="VARCHAR" property="paymentNo" />
    <result column="payment_fee" jdbcType="DECIMAL" property="paymentFee" />
    <result column="reimburse_fee" jdbcType="DECIMAL" property="reimburseFee" />
    <result column="total_fee" jdbcType="DECIMAL" property="totalFee" />
    <result column="if_fallback" jdbcType="INTEGER" property="ifFallback" />
    <result column="fallback_reason" jdbcType="VARCHAR" property="fallbackReason" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, ext_vehicle_settlement_id, payment_channel, payment_no, payment_fee, reimburse_fee, 
    total_fee, if_fallback, fallback_reason, remark, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_ext_vehicle_settlement_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_ext_vehicle_settlement_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TExtVehicleSettlementItem">
    insert into t_ext_vehicle_settlement_item (id, ext_vehicle_settlement_id, payment_channel, 
      payment_no, payment_fee, reimburse_fee, 
      total_fee, if_fallback, fallback_reason, 
      remark, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{extVehicleSettlementId,jdbcType=BIGINT}, #{paymentChannel,jdbcType=VARCHAR}, 
      #{paymentNo,jdbcType=VARCHAR}, #{paymentFee,jdbcType=DECIMAL}, #{reimburseFee,jdbcType=DECIMAL}, 
      #{totalFee,jdbcType=DECIMAL}, #{ifFallback,jdbcType=INTEGER}, #{fallbackReason,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TExtVehicleSettlementItem">
    insert into t_ext_vehicle_settlement_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="extVehicleSettlementId != null">
        ext_vehicle_settlement_id,
      </if>
      <if test="paymentChannel != null">
        payment_channel,
      </if>
      <if test="paymentNo != null">
        payment_no,
      </if>
      <if test="paymentFee != null">
        payment_fee,
      </if>
      <if test="reimburseFee != null">
        reimburse_fee,
      </if>
      <if test="totalFee != null">
        total_fee,
      </if>
      <if test="ifFallback != null">
        if_fallback,
      </if>
      <if test="fallbackReason != null">
        fallback_reason,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="extVehicleSettlementId != null">
        #{extVehicleSettlementId,jdbcType=BIGINT},
      </if>
      <if test="paymentChannel != null">
        #{paymentChannel,jdbcType=VARCHAR},
      </if>
      <if test="paymentNo != null">
        #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="paymentFee != null">
        #{paymentFee,jdbcType=DECIMAL},
      </if>
      <if test="reimburseFee != null">
        #{reimburseFee,jdbcType=DECIMAL},
      </if>
      <if test="totalFee != null">
        #{totalFee,jdbcType=DECIMAL},
      </if>
      <if test="ifFallback != null">
        #{ifFallback,jdbcType=INTEGER},
      </if>
      <if test="fallbackReason != null">
        #{fallbackReason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TExtVehicleSettlementItem">
    update t_ext_vehicle_settlement_item
    <set>
      <if test="extVehicleSettlementId != null">
        ext_vehicle_settlement_id = #{extVehicleSettlementId,jdbcType=BIGINT},
      </if>
      <if test="paymentChannel != null">
        payment_channel = #{paymentChannel,jdbcType=VARCHAR},
      </if>
      <if test="paymentNo != null">
        payment_no = #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="paymentFee != null">
        payment_fee = #{paymentFee,jdbcType=DECIMAL},
      </if>
      <if test="reimburseFee != null">
        reimburse_fee = #{reimburseFee,jdbcType=DECIMAL},
      </if>
      <if test="totalFee != null">
        total_fee = #{totalFee,jdbcType=DECIMAL},
      </if>
      <if test="ifFallback != null">
        if_fallback = #{ifFallback,jdbcType=INTEGER},
      </if>
      <if test="fallbackReason != null">
        fallback_reason = #{fallbackReason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TExtVehicleSettlementItem">
    update t_ext_vehicle_settlement_item
    set ext_vehicle_settlement_id = #{extVehicleSettlementId,jdbcType=BIGINT},
      payment_channel = #{paymentChannel,jdbcType=VARCHAR},
      payment_no = #{paymentNo,jdbcType=VARCHAR},
      payment_fee = #{paymentFee,jdbcType=DECIMAL},
      reimburse_fee = #{reimburseFee,jdbcType=DECIMAL},
      total_fee = #{totalFee,jdbcType=DECIMAL},
      if_fallback = #{ifFallback,jdbcType=INTEGER},
      fallback_reason = #{fallbackReason,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>