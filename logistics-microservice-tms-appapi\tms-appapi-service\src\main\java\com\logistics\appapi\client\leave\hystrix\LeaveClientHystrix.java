package com.logistics.appapi.client.leave.hystrix;

import com.logistics.appapi.client.leave.LeaveClient;
import com.logistics.appapi.client.leave.request.*;
import com.logistics.appapi.client.leave.response.DriverLeaveApplyDetailResponseModel;
import com.logistics.appapi.client.leave.response.DriverLeaveApplyListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/3/14 13:58
 */
@Component
public class LeaveClientHystrix implements LeaveClient {
    @Override
    public Result<Boolean> driverLeaveApply(DriverLeaveApplyRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<DriverLeaveApplyListResponseModel> driverLeaveApplyList(DriverLeaveApplyListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<DriverLeaveApplyDetailResponseModel> driverLeaveApplyDetail(DriverLeaveApplyDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> driverResubmitLeaveApply(DriverLeaveApplyResubmitRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> driverCancelLeaveApply(DriverLeaveApplyCancelRequestModel requestModel) {
        return Result.timeout();
    }
}
