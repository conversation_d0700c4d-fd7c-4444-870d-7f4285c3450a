/**
 * Created by yun.zhou on 2017/12/12.
 */
package com.logistics.appapi.base.enums;

public enum AuditStatusEnum {
    INVALID(-2, "已作废"),
    NOT_AUDIT(-1, "无需审核"),
    WAIT_AUDIT(0, "待审核"),
    AUDIT_THROUGH(1, "已审核"),
    AUDIT_REJECT(2, "已驳回"),
    WAIT_SUBMIT(3, "待提交"),
    ;

    private Integer key;
    private String value;

    AuditStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static AuditStatusEnum getEnum(Integer key) {
        for (AuditStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
