package com.logistics.management.webapi.controller.reservebalance.dto.response;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchDriverReserveBalanceResponseDto {

    @ApiModelProperty(value = "合计充值金额")
    private String totalRechargeAmount = "0";

    @ApiModelProperty(value = "合计已冲销金额")
    private String totalVerificationAmount = "0";

    @ApiModelProperty(value = "合计待冲销金额")
    private String totalAwaitVerificationAmount = "0";

    @ApiModelProperty(value = "剩余金额")
    private String totalRemainingAmount = "0";

    @ApiModelProperty("司机备用金列表")
    private PageInfo<DriverReserveBalanceListResponseDto> driverReserveBalancePageInfo;
}
