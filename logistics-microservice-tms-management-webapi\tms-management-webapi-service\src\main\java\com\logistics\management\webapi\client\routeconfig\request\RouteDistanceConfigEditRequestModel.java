package com.logistics.management.webapi.client.routeconfig.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class RouteDistanceConfigEditRequestModel extends RouteDistanceConfigRequestModel {

    @ApiModelProperty(value = "计费距离", required = true)
    private BigDecimal billingDistance;
}
