package com.logistics.management.webapi.controller.reservebalance.dto.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Data
public class DriverReserveBalanceListRequestDto extends AbstractPageForm<DriverReserveBalanceListRequestDto> {

	@ApiModelProperty(value = "人员机构, 1 自主 2 外包 3 自营")
	private String staffProperty;

	@ApiModelProperty(value = "司机, 姓名 + 手机号")
	private String driverName;

	@ApiModelProperty(value = "余额排序, 0 升序 1 降序")
	private Integer balanceSort;

	@ApiModelProperty(value = "已冲销排序, 0 升序 1 降序")
	private Integer verificationSort;
}
