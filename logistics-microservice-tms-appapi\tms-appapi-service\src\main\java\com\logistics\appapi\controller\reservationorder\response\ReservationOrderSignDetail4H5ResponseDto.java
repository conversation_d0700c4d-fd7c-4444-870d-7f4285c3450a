package com.logistics.appapi.controller.reservationorder.response;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/14 14:52
 */
@Data
public class ReservationOrderSignDetail4H5ResponseDto {
    /**
     * 预约单id
     */
    private String reservationOrderId="";

    /**
     * 预约单号
     */
    private String reservationOrderCode="";

    /**
     * 预约类型：1 提货，2 卸货
     */
    private String reservationType="";

    /**
     * 预约类型
     */
    private String reservationTypeLabel="";

    /**
     * 发货地/收货地
     */
    private String address="";

    /**
     * 运单列表
     */
    private List<ReservationCarrierOrderList4H5ResponseDto> orderList=new ArrayList<>();

    /**
     * 预计里程数（公里）
     */
    private String expectMileage="";

    /**
     * 预约时间
     */
    private String reservationTime="";


    /**
     * 委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨；100 新生回收，101 新生销售
     */
    private String demandOrderEntrustType="";

    /**
     * 预约类型
     */
    private String demandOrderEntrustTypeLabel="";

    /**
     * 仓库名称
     */
    private String warehouse;
}
