package com.logistics.management.webapi.controller.drivercostapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @author: wjf
 * @date: 2022/8/3 13:54
 */
@Data
public class UndoDriverCostApplyRequestDto {
    @ApiModelProperty(value = "司机费用申请表id",required = true)
    @NotBlank(message = "id不能为空")
    private String driverCostApplyId;

    @ApiModelProperty(value = "撤销说明",required = true)
    @NotBlank(message = "请维护撤销说明，1-100字")
    @Size(min = 1, max = 100, message = "请维护撤销说明，1-100字")
    private String remark;
}
