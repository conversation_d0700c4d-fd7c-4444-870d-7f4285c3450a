package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TContractFile;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TContractFileMapper extends BaseMapper<TContractFile> {

   int updateContractFileByContractId(@Param("contractId") Long contractId, @Param("filePath") String filePath);

   int batchInsert(@Param("list") List<TContractFile> list);
}