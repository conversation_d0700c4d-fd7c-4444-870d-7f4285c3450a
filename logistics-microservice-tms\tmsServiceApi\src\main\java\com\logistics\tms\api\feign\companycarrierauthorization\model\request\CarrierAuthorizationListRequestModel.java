package com.logistics.tms.api.feign.companycarrierauthorization.model.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CarrierAuthorizationListRequestModel extends AbstractPageForm<CarrierAuthorizationListRequestModel> {

    @ApiModelProperty("车主名")
    private String companyCarrierName;

    @ApiModelProperty("授权状态, 0:待授权 1:待审核 2:已驳回 3:已授权")
    private Integer authorizationStatus;

    @ApiModelProperty("是否归档, 0:否 1:是")
    private Integer isArchived;

    @ApiModelProperty("归档人")
    private String archivedUsername;

    @ApiModelProperty("归档时间起")
    private Date archivedTimeStart;

    @ApiModelProperty("归档时间止")
    private Date archivedTimeEnd;
}
