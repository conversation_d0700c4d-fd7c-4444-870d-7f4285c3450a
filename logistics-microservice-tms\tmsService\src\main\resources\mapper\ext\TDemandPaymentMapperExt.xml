<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandPaymentMapper" >
  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TDemandPayment" >
    <foreach collection="list" item="item" separator=";">
      insert into t_demand_payment
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.demandOrderId != null" >
          demand_order_id,
        </if>
        <if test="item.priceType != null">
          price_type,
        </if>
        <if test="item.settlementAmount != null" >
          settlement_amount,
        </if>
        <if test="item.settlementCostTotal != null">
          settlement_cost_total,
        </if>
        <if test="item.settlementTime != null" >
          settlement_time,
        </if>
        <if test="item.status != null" >
          status,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.demandOrderId != null" >
          #{item.demandOrderId,jdbcType=BIGINT},
        </if>
        <if test="item.priceType != null">
          #{item.priceType,jdbcType=INTEGER},
        </if>
        <if test="item.settlementAmount != null" >
          #{item.settlementAmount,jdbcType=DECIMAL},
        </if>
        <if test="item.settlementCostTotal != null">
          #{item.settlementCostTotal,jdbcType=DECIMAL},
        </if>
        <if test="item.settlementTime != null" >
          #{item.settlementTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.status != null" >
          #{item.status,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

  <select id="getByDemandOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_demand_payment
    where valid = 1
    and demand_order_id = #{demandOrderId,jdbcType=BIGINT}
  </select>

  <select id="getByDemandOrderIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_demand_payment
    where valid = 1
    and demand_order_id in (${demandOrderIds})
  </select>

    <update id="batchUpdate">
        <foreach collection="list" separator=";" item="item">
            update t_demand_payment
            <set>
                <if test="item.demandOrderId != null">
                    demand_order_id = #{item.demandOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.priceType != null">
                    price_type = #{item.priceType,jdbcType=INTEGER},
                </if>
                <if test="item.settlementAmount != null">
                    settlement_amount = #{item.settlementAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.settlementCostTotal != null">
                    settlement_cost_total = #{item.settlementCostTotal,jdbcType=DECIMAL},
                </if>
                <if test="item.settlementTime != null">
                    settlement_time = #{item.settlementTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.status != null">
                    status = #{item.status,jdbcType=INTEGER},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateByDemandOrderId">
        update t_demand_payment
        <set>
            <if test="priceType != null">
                price_type = #{priceType,jdbcType=INTEGER},
            </if>
            <if test="settlementAmount != null">
                settlement_amount = #{settlementAmount,jdbcType=DECIMAL},
            </if>
            <if test="settlementCostTotal != null">
                settlement_cost_total = #{settlementCostTotal,jdbcType=DECIMAL},
            </if>
            <if test="settlementTime != null">
                settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=INTEGER},
            </if>
        </set>
        where demand_order_id = #{demandOrderId,jdbcType=BIGINT}
    </update>
</mapper>