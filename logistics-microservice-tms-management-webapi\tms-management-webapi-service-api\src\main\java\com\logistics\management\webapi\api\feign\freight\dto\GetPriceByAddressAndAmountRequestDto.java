package com.logistics.management.webapi.api.feign.freight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
@Data
public class GetPriceByAddressAndAmountRequestDto {
    @ApiModelProperty("委托方公司ID")
    private String companyEntrustId;
    @ApiModelProperty("发货省ID")
    private String fromProvinceId;
    @ApiModelProperty("发货市ID")
    private String fromCityId;
    @ApiModelProperty("发货区ID")
    private String fromAreaId;
    @ApiModelProperty("发货仓库")
    private String fromWarehouse;
    @ApiModelProperty("收货省ID")
    private String toProvinceId;
    @ApiModelProperty("收货市ID")
    private String toCityId;
    @ApiModelProperty("收货区ID")
    private String toAreaId;
    @ApiModelProperty("收货仓库")
    private String toWarehouse;
    @ApiModelProperty("收货仓库详情-包含一日游")
    private String toWarehouseDetail;
    @ApiModelProperty("货物数量")
    private String goodsAmount;
    @ApiModelProperty("货物单位 1件 2吨")
    private String goodsUnit;
    @ApiModelProperty("费用类型 1 单价 2 一口价")
    private String freightType;


}
