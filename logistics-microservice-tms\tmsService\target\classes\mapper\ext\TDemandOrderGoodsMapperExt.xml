<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandOrderGoodsMapper">
    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TDemandOrderGoods">
        <foreach collection="list" item="item" separator=";">
            insert into t_demand_order_goods
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.demandOrderId != null">
                    demand_order_id,
                </if>
                <if test="item.skuCode != null">
                    sku_code,
                </if>
                <if test="item.goodsName != null">
                    goods_name,
                </if>
                <if test="item.categoryName != null">
                    category_name,
                </if>
                <if test="item.length != null">
                    length,
                </if>
                <if test="item.width != null">
                    width,
                </if>
                <if test="item.height != null">
                    height,
                </if>
                <if test="item.goodsSize != null">
                    goods_size,
                </if>
                <if test="item.goodsAmount != null">
                    goods_amount,
                </if>
                <if test="item.arrangedAmount != null">
                    arranged_amount,
                </if>
                <if test="item.notArrangedAmount != null">
                    not_arranged_amount,
                </if>
                <if test="item.backAmount != null">
                    back_amount,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.demandOrderId != null">
                    #{item.demandOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.skuCode != null">
                    #{item.skuCode,jdbcType=VARCHAR},
                </if>
                <if test="item.goodsName != null">
                    #{item.goodsName,jdbcType=VARCHAR},
                </if>
                <if test="item.categoryName != null">
                    #{item.categoryName,jdbcType=VARCHAR},
                </if>
                <if test="item.length != null">
                    #{item.length,jdbcType=INTEGER},
                </if>
                <if test="item.width != null">
                    #{item.width,jdbcType=INTEGER},
                </if>
                <if test="item.height != null">
                    #{item.height,jdbcType=INTEGER},
                </if>
                <if test="item.goodsSize != null">
                    #{item.goodsSize,jdbcType=VARCHAR},
                </if>
                <if test="item.goodsAmount != null">
                    #{item.goodsAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.arrangedAmount != null">
                    #{item.arrangedAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.notArrangedAmount != null">
                    #{item.notArrangedAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.backAmount != null">
                    #{item.backAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <select id="getDemandOrderGoodsByGoodsIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_demand_order_goods
        where valid = 1
          and id in (${ids})
    </select>

    <select id="getDemandOrderGoodsByDemandOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_demand_order_goods
        where valid = 1
          and demand_order_id in (${demandOrderIds})
    </select>

    <select id="getInvalidByDemandOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_demand_order_goods
        where demand_order_id = #{demandOrderId,jdbcType=BIGINT}
    </select>

    <update id="batchUpdateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDemandOrderGoods">
        <foreach collection="list" item="item" separator=";">
            update t_demand_order_goods
            <set>
                <if test="item.demandOrderId != null">
                    demand_order_id = #{item.demandOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.skuCode != null">
                    sku_code = #{item.skuCode,jdbcType=VARCHAR},
                </if>
                <if test="item.goodsName != null">
                    goods_name = #{item.goodsName,jdbcType=VARCHAR},
                </if>
                <if test="item.categoryName != null">
                    category_name = #{item.categoryName,jdbcType=VARCHAR},
                </if>
                <if test="item.length != null">
                    length = #{item.length,jdbcType=INTEGER},
                </if>
                <if test="item.width != null">
                    width = #{item.width,jdbcType=INTEGER},
                </if>
                <if test="item.height != null">
                    height = #{item.height,jdbcType=INTEGER},
                </if>
                <if test="item.goodsSize != null">
                    goods_size = #{item.goodsSize,jdbcType=VARCHAR},
                </if>
                <if test="item.goodsAmount != null">
                    goods_amount = #{item.goodsAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.arrangedAmount != null">
                    arranged_amount = #{item.arrangedAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.notArrangedAmount != null">
                    not_arranged_amount = #{item.notArrangedAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.backAmount != null">
                    back_amount = #{item.backAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getTDemandOrderGoodsByDemandId" resultType="com.logistics.tms.controller.demandorder.response.DemandOrderGoodsResponseModel">
        SELECT
        id                  as demandOrderGoodsId,
        goods_name          as goodsName,
        demand_order_id     as demandOrderId,
        length,
        width,
        height,
        goods_amount        as goodsAmountNumber,
        arranged_amount     as arrangedAmountNumber,
        not_arranged_amount as notArrangedAmountNumber,
        goods_size          as goodsSize,
        back_amount         as backAmountNumber
        FROM t_demand_order_goods
        WHERE valid = 1
          and demand_order_id = #{demandId}
    </select>
</mapper>