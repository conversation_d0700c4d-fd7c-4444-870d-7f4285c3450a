package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新生运单入库状态model
 *
 * <AUTHOR>
 * @date ：Created in 2022/8/20
 */
@Data
public class YeloLifeCarrierOrderInStatusModel {

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("入库状态: 3:已入库")
	private Integer stockInState;

	@ApiModelProperty("入库操作人")
	private String operatorName;
}
