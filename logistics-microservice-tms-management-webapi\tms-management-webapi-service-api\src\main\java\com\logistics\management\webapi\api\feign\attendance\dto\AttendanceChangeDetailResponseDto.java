package com.logistics.management.webapi.api.feign.attendance.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 打卡变更申请详情响应dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/15
 */
@Data
public class AttendanceChangeDetailResponseDto {

	@ApiModelProperty(value = "打卡变更申请ID")
	private String attendanceChangeApplyId = "";

	@ApiModelProperty("考勤用户,【机构】姓名_手机号")
	private String attendanceUser = "";

	@ApiModelProperty("考勤日期")
	private String attendanceDate = "";

	@ApiModelProperty("上班打卡日期")
	private String onDutyPunchDate = "";

	@ApiModelProperty("上班打卡时间")
	private String onDutyPunchTime = "";

	@ApiModelProperty("上班打卡图片")
	private String onDutyPunchPic = "";

	@ApiModelProperty("下班打卡日期")
	private String offDutyPunchDate = "";

	@ApiModelProperty("下班打卡时间")
	private String offDutyPunchTime = "";

	@ApiModelProperty("下班打卡图片")
	private String offDutyPunchPic = "";

	@ApiModelProperty("变更类型: 1 上班，2 下班")
	private String changeType = "";

	@ApiModelProperty("变更类型Label")
	private String changeTypeLabel = "";

	@ApiModelProperty("用户提交的变更时间")
	private String changePunchTime = "";

	@ApiModelProperty("变更原因")
	private String changeReason = "";

	@ApiModelProperty("审核时间")
	private String auditTime = "";

	@ApiModelProperty("审核状态: 1 已审核，2 已驳回")
	private String auditStatus = "";

	@ApiModelProperty("审核状态Label")
	private String auditStatusLabel = "";

	@ApiModelProperty("审核人")
	private String auditorName = "";

	@ApiModelProperty("审核备注")
	private String remark = "";
}
