package com.logistics.appapi.controller.carrierorder;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.logistics.appapi.base.enums.*;
import com.logistics.appapi.base.utils.ConversionUtils;
import com.logistics.appapi.base.utils.MurmurHashUtils;
import com.logistics.appapi.client.auth.AuthTokenServiceApi;
import com.logistics.appapi.controller.common.CommonBiz;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.constant.ConfigKeyConstant;
import com.logistics.appapi.base.utils.RegExpValidatorUtil;
import com.logistics.appapi.base.utils.WordBarCodeUtils;
import com.logistics.appapi.client.carrierorder.CarrierOrderClient;
import com.logistics.appapi.client.carrierorder.request.*;
import com.logistics.appapi.client.carrierorder.response.*;
import com.logistics.appapi.client.thirdparty.basicdata.BasicServiceClient;
import com.logistics.appapi.client.thirdparty.basicdata.file.request.PdfWriteToImgRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.response.PdfWriteToImgResponseModel;
import com.logistics.appapi.controller.carrierorder.mapping.*;
import com.logistics.appapi.controller.carrierorder.request.*;
import com.logistics.appapi.controller.carrierorder.response.*;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.utils.RedisUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tools.utils.WordToPdfUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;
@Slf4j
@RestController
@Api(value = "APPLET-API - CarrierOrderAppApi-运单管理", tags = "运单管理")
public class CarrierOrderAppController {

    @Resource
    private CarrierOrderClient carrierOrderClient;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private BasicServiceClient basicServiceClient;
    @Resource
    private AuthTokenServiceApi authTokenServiceApi;
    @Resource
    private RedisUtils redisUtils;

    /**
     * 查询运单列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "查询运单列表 v2.6.8", tags = "1.1.2")
    @PostMapping(value = "/api/driverApplet/carrierOrder/searchList")
    public Result<PageInfo<SearchCarrierOrderListResponseDto>> searchList(@RequestBody SearchCarrierOrderListRequestDto requestDto) {
        if (requestDto != null && requestDto.getStartTime() != null && requestDto.getEndTime() != null && requestDto.getStartTime().compareTo(requestDto.getEndTime()) > 0){
            throw new BizException(AppApiExceptionEnum.START_TIME_END_TIME_ERROR);
        }
        Result<PageInfo<SearchCarrierOrderListAppResponseModel>> pageInfoResult = carrierOrderClient.searchList(MapperUtils.mapperNoDefault(requestDto, SearchCarrierOrderListAppRequestModel.class));
        if (pageInfoResult.isSuccess()) {
            List<SearchCarrierOrderListResponseDto> searchCarrierOrderListResponseDtos = MapperUtils.mapper(pageInfoResult.getData().getList(), SearchCarrierOrderListResponseDto.class, new SearchListMapping());
            PageInfo pageInfo = pageInfoResult.getData();
            pageInfo.setList(searchCarrierOrderListResponseDtos);
            return Result.success(pageInfo);
        } else {
            return (Result) pageInfoResult;
        }
    }

    /**
     * 查询运单列表每个状态的数量
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "查询运单列表每个状态的数量 v1.0.3")
    @PostMapping(value = "/api/driverApplet/carrierOrder/searchListAccount")
    public Result<SearchCarrierOrderCountResponseDto> searchListAccount(@RequestBody SearchCarrierOrderListRequestDto requestDto) {
        Result<SearchCarrierOrderCountResponseModel> result = carrierOrderClient.searchListAccount(MapperUtils.mapperNoDefault(requestDto,SearchCarrierOrderListAppRequestModel.class));
        if (!result.isSuccess()){
            throw new BizException(result.getErrcode(),result.getErrmsg());
        }
        SearchCarrierOrderCountResponseDto dto = MapperUtils.mapper(result.getData(),SearchCarrierOrderCountResponseDto.class);
        return Result.success(dto);
    }

    /**
     * 运单详情页
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "运单详情页 V2.6.8")
    @PostMapping(value = "/api/driverApplet/carrierOrder/carrierOrderDetail")
    public Result<CarrierOrderDetailResponseDto> carrierOrderDetail(@RequestBody @Valid CarrierOrderIdRequestDto requestDto) {
        Result<CarrierOrderDetailAppResponseModel> result = carrierOrderClient.carrierOrderDetail(MapperUtils.mapper(requestDto,CarrierOrderIdRequestModel.class));
        result.throwException();
        CarrierOrderDetailResponseDto dto = MapperUtils.mapper(result.getData(),CarrierOrderDetailResponseDto.class, new CarrierOrderDetailMapping());
        return Result.success(dto);
    }

    /**
     * 物流详情页
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "物流详情页")
    @PostMapping(value = "/api/driverApplet/carrierOrder/carrierOrderLogisticsDetail")
    public Result<CarrierOrderLogisticsDetailResponseDto> carrierOrderLogisticsDetail(@RequestBody @Valid CarrierOrderIdRequestDto requestDto) {
        Result<CarrierOrderLogisticsDetailResponseModel> result = carrierOrderClient.carrierOrderLogisticsDetail(MapperUtils.mapper(requestDto,CarrierOrderIdRequestModel.class));
        result.throwException();
        CarrierOrderLogisticsDetailResponseDto dto = MapperUtils.mapper(result.getData(),CarrierOrderLogisticsDetailResponseDto.class, new CarrierOrderLogisticsDetailMapping());
        return Result.success(dto);
    }

    @ApiOperation(value = "提货")
    @PostMapping(value = "/api/driverApplet/carrierOrder/pickUp")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result pickUp(@RequestBody @Valid CarrierOrderLoadRequestDto requestDto) {
        List<CarrierOrderTicketRequestDto> tickets = requestDto.getTickets();
        if (ListUtils.isNotEmpty(tickets)) {
            //现场图片数量
            int localeTicketCount = CommonConstant.INTEGER_ZERO;
            //出库单数量
            int outTicketCount = CommonConstant.INTEGER_ZERO;
            for (CarrierOrderTicketRequestDto ticket : tickets) {
                if (CommonConstant.TWO.equals(ticket.getTicketType())) {
                    outTicketCount++;
                    if (outTicketCount > CommonConstant.INTEGER_SIX) {
                        //出库单最多6张
                        throw new BizException(AppApiExceptionEnum.TICKET_COUNT_MAX);
                    }
                } else if (CommonConstant.EIGHT.equals(ticket.getTicketType())) {
                    localeTicketCount++;
                    if (localeTicketCount > CommonConstant.INTEGER_TWO) {
                        //现场图片最多2张
                        throw new BizException(AppApiExceptionEnum.LOCALE_TICKET_COUNT_MAX);
                    }
                } else {
                    throw new BizException(AppApiExceptionEnum.PARAMS_ERROR);
                }
            }
        }
        //校验触达带料共享托盘图片
        SiteOtherPalletsRequestDto siteOtherPalletsDto = requestDto.getSiteOtherPallets();
        if (siteOtherPalletsDto != null) {
            //空置数量
            String emptyTraysAmountStr = siteOtherPalletsDto.getEmptyTraysAmount();
            if (emptyTraysAmountStr != null) {
                int emptyTraysAmountInt = Integer.parseInt(emptyTraysAmountStr);
                if (emptyTraysAmountInt < CommonConstant.INTEGER_ONE || emptyTraysAmountInt > CommonConstant.INTEGER_TEN_THOUSAND) {
                    //1-10000范围内的数字
                    throw new BizException(AppApiExceptionEnum.TRAYS_AMOUNT_MAX);
                }
            }
            //带料占用数量
            String employTraysAmountStr = siteOtherPalletsDto.getEmployTraysAmount();
            if (employTraysAmountStr != null) {
                int employTraysAmountInt = Integer.parseInt(employTraysAmountStr);
                if (employTraysAmountInt < CommonConstant.INTEGER_ONE || employTraysAmountInt > CommonConstant.INTEGER_TEN_THOUSAND) {
                    //1-10000范围内的数字
                    throw new BizException(AppApiExceptionEnum.TRAYS_AMOUNT_MAX);
                }
                //托盘非空置时,需要带料托盘的图片
                if (ListUtils.isEmpty(siteOtherPalletsDto.getSharedTrayPics())) {
                    throw new BizException(AppApiExceptionEnum.REACH_TRY_PIC);
                }
            }
            if (ListUtils.isNotEmpty(siteOtherPalletsDto.getSharedTrayPics()) && siteOtherPalletsDto.getSharedTrayPics().size() > CommonConstant.INTEGER_THREE){
                throw new BizException(AppApiExceptionEnum.REACH_TRY_PIC);
            }
        }
        Result result = carrierOrderClient.pickUp(MapperUtils.mapper(requestDto, CarrierOrderLoadRequestModel.class));
        if ( result.getErrcode()==CommonConstant.CODE_NOT_EXIST){
            return new Result(CommonConstant.CODE_NOT_EXIST,result.getData(),result.getErrmsg());
        }else {
            result.throwException();
        }
        return result;
    }

    /**
     * 卸货
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "卸货 v2.44",tags = "3.10.0")
    @PostMapping(value = "/api/driverApplet/carrierOrder/unloading")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> unloading(@RequestBody @Valid CarrierOrderUnloadRequestDto requestDto) {
        List<CarrierOrderTicketRequestDto> tickets = requestDto.getTickets();
        if (ListUtils.isEmpty(tickets)){
            throw new BizException(AppApiExceptionEnum.TICKET_COUNT_EMPTY);
        }
        //现场图片数量
        int localeTicketCount = CommonConstant.INTEGER_ZERO;
        //签收单数量
        int ticketCount = CommonConstant.INTEGER_ZERO;
        for (CarrierOrderTicketRequestDto ticket : tickets) {
            if (CommonConstant.THREE.equals(ticket.getTicketType())) {
                ticketCount++;
                if (ticketCount > CommonConstant.INTEGER_SIX) {
                    //签收单最多6张
                    throw new BizException(AppApiExceptionEnum.TICKET_COUNT_MAX);
                }
            } else if (CommonConstant.NINE.equals(ticket.getTicketType())) {
                localeTicketCount++;
                if (localeTicketCount > CommonConstant.INTEGER_TWO) {
                    //现场图片最多2张
                    throw new BizException(AppApiExceptionEnum.LOCALE_TICKET_COUNT_MAX);
                }
            }else{
                throw new BizException(AppApiExceptionEnum.PARAMS_ERROR);
            }
        }
        CarrierOrderUnloadRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierOrderUnloadRequestModel.class);
        //给入参的货物数量赋值 如果入参存在货物编码数量
        for (CarrierOrderGoodsLoadUnloadRequestModel signGoodsForYeloLifeRequestModel : Optional.ofNullable(requestModel.getGoodsList()).orElse(new ArrayList<>())) {
            List<LoadGoodsForYeloLifeRequestCodeModel> codeDtoList = signGoodsForYeloLifeRequestModel.getCodeDtoList();
            if (CollectionUtil.isEmpty(codeDtoList)) {
                continue;
            }
            BigDecimal reduceTotalSignAmount = codeDtoList.stream().map(LoadGoodsForYeloLifeRequestCodeModel::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            signGoodsForYeloLifeRequestModel.setCount(reduceTotalSignAmount);
        }
        return carrierOrderClient.unloading(requestModel);
    }

    /**
     * 到达提货地
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "到达提货地", tags = "1.0.8")
    @PostMapping(value = "/api/driverApplet/carrierOrder/arrivePickUp")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> arrivePickUp(@RequestBody @Valid CarrierOrderArriveLoadUnloadRequestDto requestDto) {
        Result<Boolean> result = carrierOrderClient.arrivePickUp(MapperUtils.mapper(requestDto, CarrierOrderArriveLoadUnloadRequestModel.class));
        result.throwException();
        return result;
    }

    /**
     * 触达到达提货地
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "到达提货地v2", tags = "1.1.6")
    @PostMapping(value = "/api/driverApplet/carrierOrder/arrivePickUpV2")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> arrivePickUpV2(@RequestBody @Valid ArrivePickUpV2RequestDto requestDto) {
        if (StringUtils.isNotBlank(requestDto.getReachTelephone()) && !RegExpValidatorUtil.isMobileOrTel(requestDto.getReachTelephone())){
            throw new BizException(AppApiExceptionEnum.TELEPHONE_ERROR);
        }
        Result<Boolean> result = carrierOrderClient.arrivePickUpV2(MapperUtils.mapper(requestDto, ArrivePickUpV2RequestModel.class));
        result.throwException();
        return result;
    }

    /**
     * 到达卸货地
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "到达卸货地", tags = "1.0.8")
    @PostMapping(value = "/api/driverApplet/carrierOrder/arriveUnloading")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> arriveUnloading(@RequestBody @Valid CarrierOrderArriveLoadUnloadRequestDto requestDto) {
        Result<Boolean> result = carrierOrderClient.arriveUnloading(MapperUtils.mapper(requestDto, CarrierOrderArriveLoadUnloadRequestModel.class));
        result.throwException();
        return result;
    }


    /**
     * 查看运单签收单3
     *
     * @param requestDto 运单ID
     * @return 签收单路径
     */
    @ApiOperation(value = "查看运单签收单 v1.0.1")
    @PostMapping(value = "/api/driverApplet/carrierOrder/reviewSignTickets3")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<ReviewSignTicketsResponseDto> reviewSignTickets3(@RequestBody @Valid ReviewSignTicketsRequestDto requestDto,HttpServletRequest httpServletRequest) {
        GetPdfPathForBillDto getPdfPathForBillDto = getPdfPathForBill3(requestDto,httpServletRequest);
        String pdfPath = getPdfPathForBillDto.getPdfPath();
        String wordPath = getPdfPathForBillDto.getWordPath();
        List<String> signTicketsPath = new ArrayList<>();
        try (InputStream is = new FileInputStream(new File(pdfPath)); ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = is.read(buffer)) != -1){
                bos.write(buffer, 0, len);
            }
            PdfWriteToImgRequestModel pdfWriteToImgRequestModel = new PdfWriteToImgRequestModel();
            pdfWriteToImgRequestModel.setBytes(bos.toByteArray());
            pdfWriteToImgRequestModel.setPath(getPdfPathForBillDto.getCarrierOrderCode());
            pdfWriteToImgRequestModel.setDpi(150);//图片清晰度
            PdfWriteToImgResponseModel imgResponseModel = basicServiceClient.pdfWriteToImgOSS(pdfWriteToImgRequestModel);
            List<String> ticketsPathList = imgResponseModel.getImgPathList();
            for (String path : ticketsPathList) {
                signTicketsPath.add(configKeyConstant.fileAccessAddressTemp + commonBiz.getImageURL(path.substring(path.lastIndexOf("/"))));
            }
        } catch (Exception e) {
            log.info("downLoad pick up bill pdf File error", e);
        } finally {
            File wordFile = new File(wordPath);
            if (wordFile.isFile() && wordFile.exists()) {
                if (!wordFile.delete()) {
                    log.info("下载提货单 " + wordPath + " 删除失败 ");
                }
            }
            File pdfFile = new File(pdfPath);
            if (pdfFile.isFile() && pdfFile.exists()) {
                if (!pdfFile.delete()) {
                    log.info("下载提货单 " + pdfPath + " 删除失败 ");
                }
            }
        }
        ReviewSignTicketsResponseDto responseDto = new ReviewSignTicketsResponseDto();
        responseDto.setSignTicketsPath(signTicketsPath);
        return Result.success(responseDto);
    }

    //获取运单签收单pdf、word全路径（下载签收单、查看签收单）
    public GetPdfPathForBillDto getPdfPathForBill3(ReviewSignTicketsRequestDto requestDto, HttpServletRequest httpServletRequest) {
        Result<DownloadLadingBillResponseModel> result = carrierOrderClient.downloadLadingBill(MapperUtils.mapper(requestDto, CarrierOrderDetailRequestModel.class));
        result.throwException();
        DownloadLadingBillResponseDto responseDto = MapperUtils.mapper(result.getData(), DownloadLadingBillResponseDto.class, new DownloadLadingBillMapping());
        if (!(EntrustTypeEnum.RECYCLE_IN.getKey().toString().equals(responseDto.getEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().toString().equals(responseDto.getEntrustType()))) {
            throw new BizException(AppApiExceptionEnum.SIGN_UP_BILL_3_ONLY_FOR_YR);
        }

        String tmpFileName;
        List<String[]> tableItems = new ArrayList<>();
        Map<String, String> templateValues = new HashMap<>();
        Map<String, InputStream> picMap = new HashMap<>();
        //要填充的货物数据
        List<Map<String, List<String[]>>> tableInsertItems = new ArrayList<>();
        templateValues.put("carrierOrderCode", responseDto.getCarrierOrderCode());
        templateValues.put("expectedLoadTime", responseDto.getExpectedLoadTime());
        templateValues.put("companyCarrierName", responseDto.getCompanyCarrierName());
        templateValues.put("demandOrderCode", responseDto.getDemandOrderCode());
        templateValues.put("driverIdentityNumber", responseDto.getDriverIdentityNumber());
        templateValues.put("driverName", Optional.ofNullable(responseDto.getDriverName()).orElse("") + " " + Optional.ofNullable(responseDto.getDriverMobile()).orElse(""));
        templateValues.put("vehicleNo", responseDto.getVehicleNo());
        templateValues.put("remark", responseDto.getRemark());
        templateValues.put("upstreamCustomer", responseDto.getLoadWarehouse());
        templateValues.put("consignorName", Optional.ofNullable(responseDto.getConsignorName()).orElse("") + " " + Optional.ofNullable(responseDto.getConsignorMobile()).orElse(""));
        templateValues.put("loadDetailAddress", responseDto.getLoadDetailAddress());
        templateValues.put("expectAmount", responseDto.getExpectAmount());
        templateValues.put("unloadWarehouse", responseDto.getUnloadWarehouse());
        templateValues.put("receiverName",Optional.ofNullable(responseDto.getReceiverName()).orElse("") + " " + Optional.ofNullable(responseDto.getReceiverMobile()).orElse(""));
        templateValues.put("unloadDetailAddress", responseDto.getUnloadDetailAddress());
        templateValues.put("publisher", Optional.ofNullable(responseDto.getPublishName()).orElse(CommonConstant.BLANK_TEXT) + Optional.ofNullable(responseDto.getPublishMobile()).orElse(CommonConstant.BLANK_TEXT));
        //获取二维码图片
        //获取二维码图片
        byte[] qrCodePicByte = result.getData().getQrCodePicByte();
        //获取二维码图片流
        String qrCodePicPath = responseDto.getQrCodePicPath();
        if (qrCodePicByte == null && StringUtils.isNotBlank(qrCodePicPath)) {
            qrCodePicByte = commonBiz.getOssFileByte(configKeyConstant.imageUploadCatalog + qrCodePicPath);
        }
        ByteArrayInputStream qrCodeInputStream = null;
        if (qrCodePicByte != null) {
            qrCodeInputStream = new ByteArrayInputStream(qrCodePicByte);
        }
        //需要重新生成二维码
        if ((CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey().equals(responseDto.getStatus())
                || CarrierOrderStatusEnum.WAIT_LOAD.getKey().equals(responseDto.getStatus())
                || CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey().equals(responseDto.getStatus())
                || CarrierOrderStatusEnum.WAIT_UNLOAD.getKey().equals(responseDto.getStatus()))
                && "0".equals(responseDto.getIfCancel())
                && "0".equals(responseDto.getIfEmpty())) {
            //重新构建二维码
            //二维码参数
            Map<String, Object> qrCodeParamsMap = DriverAppletQrCodeJumpPageEnum.CARRIER_ORDER_DETAIL_PAGE.getParamsMap();
            qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_CODE.getKey(), responseDto.getCarrierOrderCode());
            qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_BILL_TYPE.getKey(), QrCodeParamsBillTypeEnum.RECYCLE.getKey());
            String key = (String) redisUtils.get(CommonConstant.KEY_TOKEN_REDIS + responseDto.getCarrierOrderCode());
            if (StringUtils.isEmpty(key)) {
                Long hashCodeUnsigned = MurmurHashUtils.hash128Unsigned(JSONObject.toJSONString(responseDto) + CommonConstant.SHORT_URL_HASH_COLLISION_SUFFIX);
                // 对哈希值进行62进制转换
                key = ConversionUtils.base62(hashCodeUnsigned);
                redisUtils.getRedisTemplate().opsForValue().set(CommonConstant.KEY_TOKEN_REDIS + responseDto.getCarrierOrderCode(), key);
            }
            qrCodeParamsMap.put("f", "1");
            qrCodeParamsMap.put("k", key);
            qrCodePicByte = commonBiz.createQrCode(configKeyConstant.qrcodeCommonPrefix, qrCodeParamsMap).getFileByte();
            qrCodeInputStream = new ByteArrayInputStream(qrCodePicByte);

        }
        //回收类型
        if (qrCodeInputStream != null) {
            picMap.put(CommonConstant.PIC_RID_R, qrCodeInputStream);
        }
        tmpFileName = "/template/sign_bill_3.docx";

        String pdfBasePath = "/tmp/htmlfont";//临时保存word和pdf的文件夹
        commonBiz.dirIfExist(pdfBasePath);
        String tempBasePath = pdfBasePath + "/";
        String fileName = "云途签收单3" + responseDto.getCarrierOrderCode();
        String filePath = tempBasePath + fileName;
        String wordPath = filePath + ".docx";//word文档全路径
        //替换word模板内容生成新word文档保存到指定的文件夹
        InputStream inputStream = CarrierOrderAppController.class.getResourceAsStream(tmpFileName);
        WordBarCodeUtils.fillWordTemplateValues(inputStream, wordPath, templateValues, picMap, tableItems, tableInsertItems);
        //将生成的word文档转换为pdf，保存在同一个路径下
        WordToPdfUtils.wordConverterToPdf(filePath);

        GetPdfPathForBillDto getPdfPathForBillDto = new GetPdfPathForBillDto();
        getPdfPathForBillDto.setCarrierOrderCode(responseDto.getCarrierOrderCode());
        getPdfPathForBillDto.setPdfPath(filePath + ".pdf");
        getPdfPathForBillDto.setWordPath(wordPath);
        getPdfPathForBillDto.setFileName(fileName);
        return getPdfPathForBillDto;
    }



    /**
     * 查看运单签收单
     *
     * @param requestDto 运单ID
     * @return 签收单路径
     */
    @ApiOperation(value = "查看运单签收单 v1.0.1")
    @PostMapping(value = "/api/driverApplet/carrierOrder/reviewSignTickets")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<ReviewSignTicketsResponseDto> reviewSignTickets(@RequestBody @Valid ReviewSignTicketsRequestDto requestDto,HttpServletRequest httpServletRequest) {
        GetPdfPathForBillDto getPdfPathForBillDto = getPdfPathForBill(requestDto,httpServletRequest);
        String pdfPath = getPdfPathForBillDto.getPdfPath();
        String wordPath = getPdfPathForBillDto.getWordPath();
        List<String> signTicketsPath = new ArrayList<>();
        if (StringUtils.isNotBlank(pdfPath) && StringUtils.isNotBlank(wordPath)){
            try (InputStream is = new FileInputStream(new File(pdfPath)); ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
                byte[] buffer = new byte[1024];
                int len = 0;
                while ((len = is.read(buffer)) != -1){
                    bos.write(buffer, 0, len);
                }
                PdfWriteToImgRequestModel pdfWriteToImgRequestModel = new PdfWriteToImgRequestModel();
                pdfWriteToImgRequestModel.setBytes(bos.toByteArray());
                pdfWriteToImgRequestModel.setPath(getPdfPathForBillDto.getCarrierOrderCode());
                pdfWriteToImgRequestModel.setDpi(150);//图片清晰度
                PdfWriteToImgResponseModel imgResponseModel = basicServiceClient.pdfWriteToImgOSS(pdfWriteToImgRequestModel);
                List<String> ticketsPathList = imgResponseModel.getImgPathList();
                for (String path : ticketsPathList) {
                    signTicketsPath.add(configKeyConstant.fileAccessAddressTemp + commonBiz.getImageURL(path.substring(path.lastIndexOf("/"))));
                }
            } catch (Exception e) {
                log.info("downLoad pick up bill pdf File error", e);
            } finally {
                File wordFile = new File(wordPath);
                if (wordFile.isFile() && wordFile.exists()) {
                    if (!wordFile.delete()) {
                        log.info("下载提货单 " + wordPath + " 删除失败 ");
                    }
                }
                File pdfFile = new File(pdfPath);
                if (pdfFile.isFile() && pdfFile.exists()) {
                    if (!pdfFile.delete()) {
                        log.info("下载提货单 " + pdfPath + " 删除失败 ");
                    }
                }
            }
        }
        ReviewSignTicketsResponseDto responseDto = new ReviewSignTicketsResponseDto();
        responseDto.setSignTicketsPath(signTicketsPath);
        return Result.success(responseDto);
    }

    /**
     * 根据运单号查询运单id
     *
     * @param requestDto 运单号
     * @return 运单id
     */
    @ApiOperation(value = "扫一扫根据运单号查询运单id v1.0.5")
    @PostMapping(value = "/api/driverApplet/carrierOrder/queryIdByCarrierOrderCode")
    public Result<QueryIdByCarrierOrderCodeResponseDto> queryIdByCarrierOrderCode(@RequestBody @Valid QueryIdByCarrierOrderCodeRequestDto requestDto) {
        Result<QueryIdByCarrierOrderCodeResponseModel> result = carrierOrderClient.queryIdByCarrierOrderCode(MapperUtils.mapper(requestDto, QueryIdByCarrierOrderCodeRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), QueryIdByCarrierOrderCodeResponseDto.class));
    }

    /**
     * 司机打印详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "司机打印详情", tags = "1.1.6")
    @PostMapping(value = "/api/driverApplet/carrierOrder/printBillDetail")
    public Result<PrintBillDetailResponseDto> printBillDetail(@RequestBody @Valid CarrierOrderIdRequestDto requestDto) {
        Result<PrintBillDetailResponseModel> result = carrierOrderClient.printBillDetail(MapperUtils.mapper(requestDto, CarrierOrderIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), PrintBillDetailResponseDto.class, new PrintBillDetailMapping()));
    }

    //获取运单签收单pdf、word全路径（下载签收单、查看签收单）
    public GetPdfPathForBillDto getPdfPathForBill(ReviewSignTicketsRequestDto requestDto, HttpServletRequest httpServletRequest) {
        Result<DownloadLadingBillResponseModel> result = carrierOrderClient.downloadLadingBill(MapperUtils.mapper(requestDto, CarrierOrderDetailRequestModel.class));
        result.throwException();
        DownloadLadingBillResponseDto responseDto = MapperUtils.mapper(result.getData(), DownloadLadingBillResponseDto.class, new DownloadLadingBillMapping());

        String tmpFileName;
        List<String[]> tableItems = new ArrayList<>();
        List<Map<String, List<String[]>>> tableInsertItems = new ArrayList<>();
        Map<String, String> templateValues = new HashMap<>();
        Map<String, InputStream> picMap = new HashMap<>();
        templateValues.put("carrierOrderCode", responseDto.getCarrierOrderCode());
        templateValues.put("vehicleNumber", responseDto.getVehicleNo());
        templateValues.put("driverName", responseDto.getDriverName());
        templateValues.put("driverIdCardNum", responseDto.getDriverIdentityNumber());
        templateValues.put("driverPhone", responseDto.getDriverMobile());
        templateValues.put("driver", Optional.ofNullable(responseDto.getDriverName()).orElse("") + " " + Optional.ofNullable(responseDto.getDriverMobile()).orElse(""));
        templateValues.put("pickUpWarehouseAddress", responseDto.getLoadDetailAddress());
        templateValues.put("pickUpWarehouse", responseDto.getLoadWarehouse());
        templateValues.put("receiptsWarehouseAddress", responseDto.getUnloadDetailAddress());
        templateValues.put("receiptsWarehouse", responseDto.getUnloadWarehouse());
        templateValues.put("pickUpExpectedTime", responseDto.getExpectedLoadTime());
        templateValues.put("receiptsExpectedTime", responseDto.getExpectedUnloadTime());
        templateValues.put("companyEntrustName", responseDto.getCompanyEntrustName());
        templateValues.put("dispatchTime", responseDto.getDispatchTime());

        if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(responseDto.getDemandOrderSource())) {
            templateValues.put("goodsName", responseDto.getGoodsName());
            templateValues.put("consignor", Optional.ofNullable(responseDto.getConsignorName()).orElse("") + " " + Optional.ofNullable(responseDto.getConsignorMobile()).orElse(""));
            templateValues.put("receiver", Optional.ofNullable(responseDto.getReceiverName()).orElse("") + " " + Optional.ofNullable(responseDto.getReceiverMobile()).orElse(""));
            templateValues.put("goodsUnit", responseDto.getGoodsUnit());
            templateValues.put("demandOrderCode", responseDto.getDemandOrderCode());
            templateValues.put("receiverMobile", responseDto.getReceiverMobile());
            if (responseDto.getStatus() > CarrierOrderStatusEnum.WAIT_LOAD.getKey() && responseDto.getStatus() < CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey()) {
                templateValues.put("loadAmount", responseDto.getLoadAmount());
                templateValues.put("amountDiff", responseDto.getAmountDiff());
            } else if (CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(responseDto.getStatus())) {
                templateValues.put("loadAmount", responseDto.getLoadAmount());
                templateValues.put("signAmount", responseDto.getSignAmount());
                templateValues.put("amountDiff", responseDto.getAmountDiff());
            }

            //获取二维码图片
            byte[] qrCodePicByte = result.getData().getQrCodePicByte();
            //获取二维码图片流
            String qrCodePicPath = responseDto.getQrCodePicPath();
            if (qrCodePicByte == null && StringUtils.isNotBlank(qrCodePicPath)){
                qrCodePicByte = commonBiz.getOssFileByte(configKeyConstant.imageUploadCatalog + qrCodePicPath);
            }
            ByteArrayInputStream qrCodeInputStream = null;
            if (qrCodePicByte != null) {
                qrCodeInputStream = new ByteArrayInputStream(qrCodePicByte);
            }

            //需要重新生成二维码
            if ((CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey().equals(responseDto.getStatus())
                    || CarrierOrderStatusEnum.WAIT_LOAD.getKey().equals(responseDto.getStatus())
                    || CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey().equals(responseDto.getStatus())
                    || CarrierOrderStatusEnum.WAIT_UNLOAD.getKey().equals(responseDto.getStatus()))
                    && "0".equals(responseDto.getIfCancel())
                    && "0".equals(responseDto.getIfEmpty())) {
                //重新构建二维码
                //二维码参数
                Map<String, Object> qrCodeParamsMap = DriverAppletQrCodeJumpPageEnum.CARRIER_ORDER_DETAIL_PAGE.getParamsMap();
                qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_CODE.getKey(), responseDto.getCarrierOrderCode());
                if (EntrustTypeEnum.RECYCLE_IN.getKey().toString().equals(responseDto.getEntrustType())
                        || EntrustTypeEnum.RECYCLE_OUT.getKey().toString().equals(responseDto.getEntrustType())) {
                    qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_BILL_TYPE.getKey(), QrCodeParamsBillTypeEnum.RECYCLE.getKey());
                } else {
                    qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_BILL_TYPE.getKey(), QrCodeParamsBillTypeEnum.OTHER.getKey());
                }
                String key = (String)redisUtils.get(CommonConstant.KEY_TOKEN_REDIS+responseDto.getCarrierOrderCode());
                if (StringUtils.isEmpty(key)) {
                    Long hashCodeUnsigned = MurmurHashUtils.hash128Unsigned(JSONObject.toJSONString(responseDto) + CommonConstant.SHORT_URL_HASH_COLLISION_SUFFIX);
                    // 对哈希值进行62进制转换
                    key = ConversionUtils.base62(hashCodeUnsigned);
                    redisUtils.getRedisTemplate().opsForValue().set(CommonConstant.KEY_TOKEN_REDIS + responseDto.getCarrierOrderCode(), key);
                }
                qrCodeParamsMap.put("f", "1");
                qrCodeParamsMap.put("k", key);
                qrCodePicByte = commonBiz.createQrCode(configKeyConstant.qrcodeCommonPrefix, qrCodeParamsMap).getFileByte();
                qrCodeInputStream = new ByteArrayInputStream(qrCodePicByte);

            }

            if (!EntrustTypeEnum.RECYCLE_IN.getKey().toString().equals(responseDto.getEntrustType())
                    && !EntrustTypeEnum.RECYCLE_OUT.getKey().toString().equals(responseDto.getEntrustType())) {
                //非回收类型
                if (qrCodeInputStream != null) {
                    picMap.put(CommonConstant.PIC_RID, qrCodeInputStream);
                }

                templateValues.put("unloadCompany", responseDto.getUnloadCompany());
                templateValues.put("entrustType", responseDto.getEntrustTypeLabel());
                templateValues.put("loadTime", responseDto.getLoadTime());
                templateValues.put("loadAmount", BigDecimal.ZERO.compareTo(new BigDecimal(responseDto.getLoadAmount())) < CommonConstant.INTEGER_ZERO ? responseDto.getLoadAmount() : CommonConstant.BLANK_TEXT);

                List<DownloadLadingBillGoodsResponseDto> goodsInfoList = responseDto.getGoodsInfoList();
                if (goodsInfoList == null) {
                    goodsInfoList = new ArrayList<>();
                }
                if (ListUtils.isNotEmpty(goodsInfoList)) {
                    for (int i = 0; i < goodsInfoList.size(); i++) {
                        DownloadLadingBillGoodsResponseDto dto = goodsInfoList.get(i);
                        String loadAmount = CommonConstant.BLANK_TEXT;
                        if (responseDto.getStatus() > CarrierOrderStatusEnum.WAIT_LOAD.getKey()) {
                            loadAmount = dto.getLoadAmount();
                        }
                        if (String.valueOf(EntrustTypeEnum.DELIVER.getKey()).equals(responseDto.getEntrustType())) {
                            tableItems.add(new String[]{ConverterUtils.toString(i + 1),
                                    StringUtils.isNotEmpty(dto.getCategoryName()) ? dto.getCategoryName() : dto.getGoodsName(),
                                    dto.getGoodsName(), dto.getExpectAmount(), loadAmount, ""});
                        } else {
                            tableItems.add(new String[]{ConverterUtils.toString(i + 1), dto.getGoodsName(), dto.getGoodsSize(), dto.getExpectAmount(), loadAmount, ""});
                        }
                    }
                }
                tmpFileName = "/template/stock_out_for_leyi_template.docx";
            }else {
                //回收类型
                if (qrCodeInputStream != null) {
                    picMap.put(CommonConstant.PIC_RID_R, qrCodeInputStream);
                }

                //提货货物
                Map<String, List<String[]>> tableValueLoadMap = new HashMap<>();
                List<String[]> tableCellValueLoadList = new ArrayList<>();
                tableValueLoadMap.put(CommonConstant.TABLE_KEYWORD_1, tableCellValueLoadList);

                //卸货货物
                Map<String, List<String[]>> tableValueUnloadMap = new HashMap<>();
                List<String[]> tableCellValueUnloadList = new ArrayList<>();
                tableValueUnloadMap.put(CommonConstant.TABLE_KEYWORD_2, tableCellValueUnloadList);

                tableInsertItems.add(tableValueLoadMap);
                tableInsertItems.add(tableValueUnloadMap);
                for (DownloadLadingBillGoodsResponseDto goodsDtoItem : responseDto.getGoodsInfoList()) {
                    tableCellValueLoadList.add(new String[]{CommonConstant.BLANK_TEXT, goodsDtoItem.getGoodsName(), goodsDtoItem.getExpectAmount(), goodsDtoItem.getLoadAmount(), goodsDtoItem.getLoadDiffAmount()});
                    tableCellValueUnloadList.add(new String[]{CommonConstant.BLANK_TEXT, goodsDtoItem.getGoodsName(), goodsDtoItem.getLoadAmount(), goodsDtoItem.getUnloadAmount(), goodsDtoItem.getUnloadDiffAmount()});
                }

                templateValues.put("remark", (StringUtils.isNotBlank(responseDto.getRemark()) ? responseDto.getRemark() + CommonConstant.SEMICOLON : CommonConstant.BLANK_TEXT) + responseDto.getOtherRequirements());
                templateValues.put("pickUpExpectedTime", responseDto.getRecycleExpectedLoadTime());
                templateValues.put("publisher", Optional.ofNullable(responseDto.getPublishName()).orElse(CommonConstant.BLANK_TEXT) + Optional.ofNullable(responseDto.getPublishMobile()).orElse(CommonConstant.BLANK_TEXT));

                tmpFileName = "/template/pick_up_goods_bill_for_leyi.docx";
            }
        }else {
            templateValues.put("pickUpPerson", responseDto.getConsignorName());
            templateValues.put("pickUpPhone", responseDto.getConsignorMobile());
            templateValues.put("receiptsPerson", responseDto.getReceiverName());
            templateValues.put("receiptsPhone", responseDto.getReceiverMobile());
            templateValues.put("totalCount", responseDto.getTotalCount());
            templateValues.put("volume", responseDto.getTotalVolume());

            List<DownloadLadingBillGoodsResponseDto> goodsInfoList = responseDto.getGoodsInfoList();
            if (ListUtils.isNotEmpty(goodsInfoList)) {
                for (int i = 0; i < goodsInfoList.size(); i++) {
                    DownloadLadingBillGoodsResponseDto dto = goodsInfoList.get(i);
                    tableItems.add(new String[]{ConverterUtils.toString(i + 1), dto.getGoodsName(), dto.getGoodsSize(), dto.getExpectAmount(), dto.getVolume()});
                }
            }
            tmpFileName = "/template/pick_up_goods_bill.docx";
        }
        String pdfBasePath = "/tmp/htmlfont";//临时保存word和pdf的文件夹
        commonBiz.dirIfExist(pdfBasePath);
        String tempBasePath = pdfBasePath + "/";
        String fileName = "云途签收单" + responseDto.getCarrierOrderCode();
        String filePath = tempBasePath + fileName;
        String wordPath = filePath + ".docx";//word文档全路径
        //替换word模板内容生成新word文档保存到指定的文件夹
        InputStream inputStream = CarrierOrderAppController.class.getResourceAsStream(tmpFileName);
        WordBarCodeUtils.fillWordTemplateValues(inputStream, wordPath, templateValues, picMap, tableItems, tableInsertItems);
        //将生成的word文档转换为pdf，保存在同一个路径下
        WordToPdfUtils.wordConverterToPdf(filePath);

        GetPdfPathForBillDto getPdfPathForBillDto = new GetPdfPathForBillDto();
        getPdfPathForBillDto.setCarrierOrderCode(responseDto.getCarrierOrderCode());
        getPdfPathForBillDto.setPdfPath(filePath + ".pdf");
        getPdfPathForBillDto.setWordPath(wordPath);
        getPdfPathForBillDto.setFileName(fileName);
        return getPdfPathForBillDto;
    }

    @ApiOperation(value = "司机申请修改提货数量-(拒绝)", tags = "3.7.0")
    @PostMapping(value = "/api/driverApplet/carrierOrder/denyLoadAmount")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> denyLoadAmount(@RequestBody @Valid CarrierOrderIdRequestDto requestDto) {
        Result<Boolean> result = carrierOrderClient.denyLoadAmount(MapperUtils.mapper(requestDto, CarrierOrderIdRequestModel.class));
        result.throwException();
        return result;
    }

    @ApiOperation(value = "(云盘)提货确认", tags = "3.7.0")
    @PostMapping(value = "/api/driverApplet/carrierOrder/pickupConfirm")
    public Result<LeyiPickupConfirmResponseDto> pickupConfirm(@RequestBody @Valid PickupConfirmRequestDto requestDto) {
        Result<LeyiPickupConfirmResponseModel> result =
                carrierOrderClient.pickupConfirm(MapperUtils.mapper(requestDto, PickupConfirmRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), LeyiPickupConfirmResponseDto.class,
                new MapperMapping<LeyiPickupConfirmResponseModel, LeyiPickupConfirmResponseDto>() {
                    @Override
                    public void configure() {
                        LeyiPickupConfirmResponseModel source = getSource();
                        LeyiPickupConfirmResponseDto destination = getDestination();
                        Optional.ofNullable(source.getLoadingCount())
                                        .ifPresent(f -> destination.setLoadingCount(f.stripTrailingZeros().toPlainString()));
                    }
                })
        );
    }

    @ApiOperation(value = "查询云盘二维码", tags = "3.7.0")
    @PostMapping(value = "/api/driverApplet/carrierOrder/getLeYiQrCode")
    public Result<GetLeYiQrCodeResponseDto> getLeYiQrCode(@RequestBody @Valid GetLeYiQrCodeRequestDto requestDto) {
        Result<GetLeYiQrCodeResponseModel> result = carrierOrderClient.getLeYiQrCode(MapperUtils.mapper(requestDto, GetLeYiQrCodeRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), GetLeYiQrCodeResponseDto.class));
    }


    @ApiOperation(value = "校验接口是否能满足多提接口 v2.6.8", tags = "2.6.8")
    @PostMapping(value = "/api/driverApplet/carrierOrder/verifyEnablePickUpMore")
    public Result<Boolean> verifyEnablePickUpMore(@RequestBody @Valid VerifyEnablePickUpMoreReqDto requestDto) {
        Result<Boolean> result = carrierOrderClient.verifyEnablePickUpMore(MapperUtils.mapper(requestDto, VerifyEnablePickUpMoreReqModel.class));
        result.throwException();
        return result;
    }

    @ApiOperation(value = "补单1-确认关联需求单信息 v2.6.8", tags = "2.6.8")
    @PostMapping(value = "/api/driverApplet/carrierOrder/commitExtDemandOrder")
    public Result<CommitExtDemandOrderRespDto> commitExtDemandOrder(@RequestBody @Valid CommitExtDemandOrderReqDto requestDto) {
        return null;
    }

    @ApiOperation(value = "补单2-关联调度单信息 v2.6.8", tags = "2.6.8")
    @PostMapping(value = "/api/driverApplet/carrierOrder/associateExtDemandOrder")
    public Result<AssociateExtDemandOrderRespDto> associateExtDemandOrder(@RequestBody @Valid AssociateExtDemandOrderReqDto requestDto) {
        AssociateExtDemandOrderReqModel mapper = MapperUtils.mapper(requestDto, AssociateExtDemandOrderReqModel.class);
        mapper.setSource(3);
        Result<AssociateExtDemandOrderRespModel> result = carrierOrderClient.associateExtDemandOrder(mapper);
        result.throwException();
        AssociateExtDemandOrderRespDto associateExtDemandOrderRespDto = new AssociateExtDemandOrderRespDto();
        associateExtDemandOrderRespDto.setCarrierOrderId(result.getData().getCarrierOrderId());
        return Result.success(associateExtDemandOrderRespDto);
    }



}
