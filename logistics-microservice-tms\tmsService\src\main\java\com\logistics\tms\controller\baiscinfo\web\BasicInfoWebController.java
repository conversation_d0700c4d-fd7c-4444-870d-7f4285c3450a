package com.logistics.tms.controller.baiscinfo.web;

import com.logistics.tms.controller.baiscinfo.applet.request.GetPersonAuthVerifyCodeRequestModel;
import com.logistics.tms.controller.baiscinfo.web.request.CarrierBasicInfoSubmitRequestModel;
import com.logistics.tms.controller.baiscinfo.web.request.UploadAuthorizationRequestModel;
import com.logistics.tms.controller.baiscinfo.web.request.VerifyPersonThreeElementsRequestModel;
import com.logistics.tms.controller.baiscinfo.web.request.VerifyPersonTwoElementsRequestModel;
import com.logistics.tms.controller.baiscinfo.web.response.CarrierBasicInfoResponseModel;
import com.logistics.tms.controller.baiscinfo.web.response.DownloadAuthTemplateResponseModel;
import com.logistics.tms.controller.baiscinfo.web.response.VerifyPersonThreeElementsResponseModel;
import com.logistics.tms.controller.baiscinfo.web.response.VerifyPersonTwoElementsResponseModel;
import com.logistics.tms.biz.basicinfo.web.BasicInfoWebBiz;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/9
 */
@Api(value = "API - BasicInfoWebApi-车主基础信息管理")
@RestController
@RequestMapping("/service/basicInfo")
public class BasicInfoWebController {

    @Autowired
    private BasicInfoWebBiz basicInfoWebBiz;

    /**
     * 个人三要素校验
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "个人三要素校验")
    @PostMapping(value = "/verifyPersonThreeElements")
    public Result<VerifyPersonThreeElementsResponseModel> verifyPersonThreeElements(@RequestBody VerifyPersonThreeElementsRequestModel requestModel) {
        return Result.success(basicInfoWebBiz.verifyPersonThreeElements(requestModel));
    }

    /**
     * 个人手机号认证-获取验证码
     *
     * @return
     */
    @ApiOperation(value = "个人手机号认证-获取验证码")
    @PostMapping(value = "/getPersonAuthVerifyCode")
    public Result<Boolean> getVerifyCode(@RequestBody GetPersonAuthVerifyCodeRequestModel requestModel) {
        basicInfoWebBiz.getVerifyCode(requestModel);
        return Result.success(true);
    }

    /**
     * 车主基础信息提交
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车主基础信息提交")
    @PostMapping(value = "/carrierBasicInfoSubmit")
    public Result<Boolean> carrierBasicInfoSubmit(@RequestBody CarrierBasicInfoSubmitRequestModel requestModel) {
        basicInfoWebBiz.carrierBasicInfoSubmit(requestModel);
        return Result.success(true);
    }

    /**
     * 车主前台提交授权书
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "上传车主授权书")
    @PostMapping(value = "/uploadAuthorization")
    public Result<Boolean> uploadAuthorization(@RequestBody UploadAuthorizationRequestModel requestModel) {
        basicInfoWebBiz.uploadAuthorization(requestModel);
        return Result.success(true);
    }

    /**
     * 下载车主授权书
     */
    @ApiOperation(value = "下载授权书模板")
    @GetMapping(value = "/downloadAuthTemplate")
    public Result<DownloadAuthTemplateResponseModel> downloadAuthTemplate() {
        return Result.success(basicInfoWebBiz.downloadAuthTemplate());
    }

    /**
     * 车主基础信息查询
     *
     * @return CarrierBasicInfoResponseModel
     */
    @ApiOperation(value = "查询车主基础信息")
    @PostMapping(value = "/carrierBasicInfo")
    public Result<CarrierBasicInfoResponseModel> carrierBasicInfo() {
        return Result.success(basicInfoWebBiz.carrierBasicInfo());
    }

    /**
     * 个人二要素校验
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "个人二要素校验")
    @PostMapping(value = "/verifyPersonTwoElements")
    public Result<VerifyPersonTwoElementsResponseModel> verifyPersonTwoElements(@RequestBody VerifyPersonTwoElementsRequestModel requestModel) {
        return Result.success(basicInfoWebBiz.verifyPersonTwoElements(requestModel));
    }
}
