<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TDateRemindMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDateRemind">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="date_name" jdbcType="VARCHAR" property="dateName" />
    <result column="if_remind" jdbcType="INTEGER" property="ifRemind" />
    <result column="remind_days" jdbcType="INTEGER" property="remindDays" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="add_user_id" jdbcType="BIGINT" property="addUserId" />
    <result column="add_user_name" jdbcType="VARCHAR" property="addUserName" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, date_name, if_remind, remind_days, remark, add_user_id, add_user_name, source, 
    created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_date_remind
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_date_remind
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDateRemind">
    insert into t_date_remind (id, date_name, if_remind,
      remind_days, remark, add_user_id, 
      add_user_name, source, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{dateName,jdbcType=VARCHAR}, #{ifRemind,jdbcType=INTEGER}, 
      #{remindDays,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{addUserId,jdbcType=BIGINT}, 
      #{addUserName,jdbcType=VARCHAR}, #{source,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDateRemind" keyProperty="id" useGeneratedKeys="true">
    insert into t_date_remind
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="dateName != null">
        date_name,
      </if>
      <if test="ifRemind != null">
        if_remind,
      </if>
      <if test="remindDays != null">
        remind_days,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="addUserId != null">
        add_user_id,
      </if>
      <if test="addUserName != null">
        add_user_name,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="dateName != null">
        #{dateName,jdbcType=VARCHAR},
      </if>
      <if test="ifRemind != null">
        #{ifRemind,jdbcType=INTEGER},
      </if>
      <if test="remindDays != null">
        #{remindDays,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addUserId != null">
        #{addUserId,jdbcType=BIGINT},
      </if>
      <if test="addUserName != null">
        #{addUserName,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDateRemind">
    update t_date_remind
    <set>
      <if test="dateName != null">
        date_name = #{dateName,jdbcType=VARCHAR},
      </if>
      <if test="ifRemind != null">
        if_remind = #{ifRemind,jdbcType=INTEGER},
      </if>
      <if test="remindDays != null">
        remind_days = #{remindDays,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addUserId != null">
        add_user_id = #{addUserId,jdbcType=BIGINT},
      </if>
      <if test="addUserName != null">
        add_user_name = #{addUserName,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDateRemind">
    update t_date_remind
    set date_name = #{dateName,jdbcType=VARCHAR},
      if_remind = #{ifRemind,jdbcType=INTEGER},
      remind_days = #{remindDays,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      add_user_id = #{addUserId,jdbcType=BIGINT},
      add_user_name = #{addUserName,jdbcType=VARCHAR},
      source = #{source,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>