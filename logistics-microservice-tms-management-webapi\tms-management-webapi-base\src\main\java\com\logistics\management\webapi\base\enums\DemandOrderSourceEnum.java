package com.logistics.management.webapi.base.enums;

public enum DemandOrderSourceEnum {
    LEYI_TRAY(1,"乐橘云盘委托单"),
    PUBLISH(2,"平台发单"),
    SINOPEC(3,"中石化系统"),
    YANG_BA(4,"扬巴邮件"),
    YELO_LIFE(5, "乐橘新生"),
    ;

    private Integer key;
    private String value;

    DemandOrderSourceEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
