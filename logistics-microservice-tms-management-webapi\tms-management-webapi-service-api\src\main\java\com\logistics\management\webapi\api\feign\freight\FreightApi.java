package com.logistics.management.webapi.api.feign.freight;
import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.freight.dto.*;
import com.logistics.management.webapi.api.feign.freight.hystrix.FreightApiHystrix;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/12/24 11:59
 */
@Api(value = "API-FreightApi-运价管理")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = FreightApiHystrix.class)
public interface FreightApi {

    @ApiOperation(value = "查询运价管理列表")
    @PostMapping(value = "/api/freight/searchList")
    Result<PageInfo<SearchFreightListResponseDto>> searchList(@RequestBody SearchFreightListRequestDto requestDto);

    @ApiOperation(value = "添加运价管理")
    @PostMapping(value = "/api/freight/addFreight")
    Result<Boolean> addFreight(@RequestBody AddFreightRequestDto requestDto);

    @ApiOperation(value = "启用/禁用")
    @PostMapping(value = "/api/freight/enableFreight")
    Result<Boolean> enableFreight(@RequestBody @Valid EnableFreightRequestDto requestDto);



    @ApiOperation(value = "运价地址列表")
    @PostMapping(value = "/api/freight/searchFreightAddressList")
    Result<PageInfo<SearchFreightAddressResponseDto>> searchFreightAddressList(@RequestBody SearchFreightAddressRequestDto requestDto);

    @ApiOperation(value = "添加/修改运价地址规则")
    @PostMapping(value = "/api/freight/addFreightAddressRule")
    Result<Boolean> addFreightAddressRule(@RequestBody @Valid AddOrModifyFreightAddressRuleRequestDto requestDto);

    @ApiOperation(value = "运价地址规则详情")
    @PostMapping(value = "/api/freight/getFreightRuleDetail")
    Result<FreightAddressRuleDetailResponseDto> getFreightRuleDetail(@RequestBody @Valid FreightAddressRuleDetailRequestDto responseDto);

    @ApiOperation(value = "删除运价地址规则")
    @PostMapping(value = "/api/freight/deleteFreightAddressRule")
    Result<Boolean> deleteFreightAddressRule(@RequestBody @Valid DeleteFreightAddressRequestDto requestDto);

    @ApiOperation(value = "统一加价/减价")
    @PostMapping(value = "/api/freight/modifyFreightPrice")
    Result<Boolean> modifyFreightPrice(@RequestBody @Valid ModifyFreightPriceRequestDto requestDto);

    @ApiOperation(value = "运价日志")
    @PostMapping(value = "/api/freight/freightLogs")
    Result<List<FreightLogsResponseDto>> freightLogs(@RequestBody @Valid FreightLogsRequestDto requestDto);

    @ApiOperation(value = "导出运价地址")
    @GetMapping(value = "/api/freight/exportFreightAddress")
    void exportFreightAddress(SearchFreightAddressRequestDto requestDto, HttpServletResponse response);

    @ApiOperation(value = "调度车辆-司机运价")
    @PostMapping(value = "/api/freight/getDriverFreight")
    Result<DriverFreightByDemandOrderIdsAndVehicleResponseDto>  getDriverFreight(@RequestBody DriverFreightByDemandOrderIdsAndVehicleRequestDto requestDto);

    @ApiOperation(value = "委托发布-根据委托方地址和数量查询价格")
    @PostMapping(value = "/api/freight/getPriceByAddressAndAmount")
    Result<GetPriceByAddressAndAmountResponseDto> getPriceByAddressAndAmount(@RequestBody GetPriceByAddressAndAmountRequestDto requestDto);
}
