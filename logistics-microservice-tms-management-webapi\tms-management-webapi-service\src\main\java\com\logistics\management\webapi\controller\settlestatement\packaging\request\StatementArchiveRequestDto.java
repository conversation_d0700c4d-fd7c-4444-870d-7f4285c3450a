package com.logistics.management.webapi.controller.settlestatement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/9
 */
@Data
public class StatementArchiveRequestDto {

	@ApiModelProperty(value = "对账单详情item id", required = true)
	@NotBlank(message = "请选择要归档的记录")
	private String settleStatementItemId;

	@ApiModelProperty(value = "归档原因", required = true)
	@NotBlank(message = "请填写归档原因")
	@Length(min = 1, max = 100, message = "请填写归档原因，1<字符长度<=100")
	private String archiveRemark;

	@ApiModelProperty(value = "归档图片", required = true)
	@NotEmpty(message = "请上传归档图片")
	@Size(max = 2, message = "请上传归档图片，最多2张")
	private List<String> archiveTicketList;

	@ApiModelProperty(value = "操作类型：1 归档，2 确认编辑", required = true)
	@NotBlank(message = "操作类型：1 归档，2 确认编辑不能为空")
	@Range(min = 1, max = 2, message = "归档操作标识错误")
	private String operateType;
}
