package com.logistics.tms.biz.customeraccount


import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.client.AuthClient
import com.logistics.tms.client.BasicDataClient
import com.logistics.tms.controller.customeraccount.request.AppletBindingOpenIdRequestModel
import com.logistics.tms.controller.customeraccount.request.CustomerLoginRequestModel
import com.logistics.tms.controller.customeraccount.request.DriverAppletDefaultLoginRequestModel
import com.logistics.tms.controller.customeraccount.request.ModifyPasswordRequestModel
import com.logistics.tms.controller.customeraccount.request.OpenAccountRequestModel
import com.logistics.tms.controller.customeraccount.request.UpdateAccountPasswordRequestModel
import com.logistics.tms.controller.customeraccount.request.UpdateTCustomerAccountRelationIfCloseRequestModel
import com.logistics.tms.controller.customeraccount.response.AccountInfoResponseModel
import com.logistics.tms.controller.customeraccount.response.CustomerLoginResponseModel
import com.logistics.tms.entity.TCustomerAccount
import com.logistics.tms.entity.TCustomerAccountRelation
import com.logistics.tms.mapper.TCustomerAccountMapper
import com.logistics.tms.mapper.TCustomerAccountRelationMapper
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class CustomerAccountBizTest extends Specification {
    @Mock
    TCustomerAccountMapper tCustomerAccountMapper
    @Mock
    TCustomerAccountRelationMapper tCustomerAccountRelationMapper
    @Mock
    AuthClient authClient
    @Mock
    BasicDataClient basicDataClient
    @Mock
    CommonBiz commonBiz
    @InjectMocks
    CustomerAccountBiz customerAccountBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "driver Applet Default Login where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCustomerAccountMapper.selectByMobileAndRole(anyString(), anyString(), anyInt())).thenReturn(new AccountInfoResponseModel())
        when(authClient.loginCreateToken(any())).thenReturn(null)

        expect:
        customerAccountBiz.driverAppletDefaultLogin(requestModel) == expectedResult

        where:
        requestModel                               || expectedResult
        new DriverAppletDefaultLoginRequestModel() || new CustomerLoginResponseModel()
    }

    @Unroll
    def "driver Applet Login where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCustomerAccountMapper.selectByMobileAndRole(anyString(), anyString(), anyInt())).thenReturn(new AccountInfoResponseModel())
        when(authClient.loginCreateToken(any())).thenReturn(null)

        expect:
        customerAccountBiz.driverAppletLogin(requestModel) == expectedResult

        where:
        requestModel                    || expectedResult
        new CustomerLoginRequestModel() || new CustomerLoginResponseModel()
    }

    @Unroll
    def "applet Binding Open Id where requestModel=#requestModel"() {
        given:
        when(tCustomerAccountMapper.selectByPrimaryKeyDecrypt(anyLong())).thenReturn(new TCustomerAccount(openId: "openId"))
        when(tCustomerAccountMapper.updateByPrimaryKeySelectiveEncrypt(any())).thenReturn(0)
        when(tCustomerAccountMapper.getByOpenId(anyString())).thenReturn(new TCustomerAccount(openId: "openId"))

        expect:
        customerAccountBiz.appletBindingOpenId(requestModel)
        assert expectedResult == false

        where:
        requestModel                          || expectedResult
        new AppletBindingOpenIdRequestModel() || true
    }

    @Unroll
    def "applet Login Out"() {
        given:
        when(tCustomerAccountMapper.selectByPrimaryKeyDecrypt(anyLong())).thenReturn(new TCustomerAccount(openId: "openId"))
        when(tCustomerAccountMapper.updateByPrimaryKeySelectiveEncrypt(any())).thenReturn(0)

        expect:
        customerAccountBiz.appletLoginOut()
        assert expectedResult == false

        where:
        expectedResult << true
    }

    @Unroll
    def "update Account Password where requestModel=#requestModel"() {
        given:
        when(tCustomerAccountMapper.updateByPrimaryKeySelectiveEncrypt(any())).thenReturn(0)
        when(tCustomerAccountMapper.selectByMobile(anyString())).thenReturn(new TCustomerAccount(userPassword: "userPassword"))

        expect:
        customerAccountBiz.updateAccountPassword(requestModel)
        assert expectedResult == false

        where:
        requestModel                            || expectedResult
        new UpdateAccountPasswordRequestModel() || true
    }

    @Unroll
    def "modify Password where requestModel=#requestModel"() {
        given:
        when(tCustomerAccountMapper.selectByPrimaryKeyDecrypt(anyLong())).thenReturn(new TCustomerAccount(userPassword: "userPassword", openId: "openId"))
        when(tCustomerAccountMapper.updateByPrimaryKeySelectiveEncrypt(any())).thenReturn(0)

        expect:
        customerAccountBiz.modifyPassword(requestModel)
        assert expectedResult == false

        where:
        requestModel                     || expectedResult
        new ModifyPasswordRequestModel() || true
    }

    @Unroll
    def "open Or Close Account where requestModel=#requestModel"() {
        given:
        when(tCustomerAccountMapper.selectByPrimaryKeyDecrypt(anyLong())).thenReturn(new TCustomerAccount(userCode: "userCode", userName: "userName", userAccount: "userAccount", userPassword: "userPassword"))
        when(tCustomerAccountMapper.insertSelectiveEncrypt(any())).thenReturn(0)
        when(tCustomerAccountMapper.updateByPrimaryKeySelectiveEncrypt(any())).thenReturn(0)
        when(tCustomerAccountMapper.selectByMobile(anyString())).thenReturn(new TCustomerAccount(userCode: "userCode", userName: "userName", userAccount: "userAccount", userPassword: "userPassword"))
        when(tCustomerAccountMapper.getAccountInfoBy(anyString(), anyLong(), anyInt())).thenReturn(new AccountInfoResponseModel())
        when(tCustomerAccountRelationMapper.getRelationByUserRoleAndUserId(anyInt(), anyLong())).thenReturn(new TCustomerAccountRelation(userRole: 0, accountId: 1l, userId: 1l, ifClose: 0, enabled: 0))

        expect:
        customerAccountBiz.openOrCloseAccount(requestModel)
        assert expectedResult == false

        where:
        requestModel                  || expectedResult
        new OpenAccountRequestModel() || true
    }

    @Unroll
    def "update T Customer Account Relation If Close where requestModel=#requestModel"() {
        given:
        when(tCustomerAccountRelationMapper.getTCustomerAccountRelationByIds(anyString(), anyInt())).thenReturn([new TCustomerAccountRelation(ifClose: 0, enabled: 0)])

        expect:
        customerAccountBiz.updateTCustomerAccountRelationIfClose(requestModel)
        assert expectedResult == false

        where:
        requestModel                                            || expectedResult
        new UpdateTCustomerAccountRelationIfCloseRequestModel() || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme