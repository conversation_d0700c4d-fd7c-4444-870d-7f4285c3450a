package com.logistics.tms.api.feign.freight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 统一加价/减价
 * @Author: sj
 * @Date: 2019/12/24 13:26
 */
@Data
public class ModifyFreightPriceRequestModel {
    @ApiModelProperty("运价规则地址ids")
    private String freightAddressIds;
    @ApiModelProperty("加价还是减价 1 加价 2 减价")
    private Integer calcType;
    @ApiModelProperty("加减价类型 1 百分比 2 元")
    private Integer unitType;
    @ApiModelProperty("加减价数量")
    private BigDecimal amount;
}
