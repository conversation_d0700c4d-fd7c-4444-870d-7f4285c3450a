package com.logistics.tms.client.feign.auth;

import com.logistics.tms.client.feign.FeignClientName;
import com.logistics.tms.client.feign.auth.hystrix.AuthTokenServiceApiHystrix;
import com.logistics.tms.client.feign.auth.request.CreateToken;
import com.logistics.tms.client.feign.auth.response.TokenModule;
import com.yelo.tray.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2023/12/25 13:36
 */
@FeignClient(name = FeignClientName.AUTH_SERVICES, fallback = AuthTokenServiceApiHystrix.class)
public interface AuthTokenServiceApi {

    @PostMapping({"/client/auth/createToken"})
    Result<TokenModule> createToken(@RequestBody CreateToken requestModel);
}
