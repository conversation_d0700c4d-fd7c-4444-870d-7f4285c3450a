package com.logistics.tms.controller.dispatch.response;


import lombok.Data;

import java.math.BigDecimal;

@Data
public class SearchSpecialDispatchIfMatchFreightRespModel {

    /**
     * 是否匹配零担运价规则 1匹配 0不匹配 (2.42)
     */
    private Integer ifMatchShippingFreightRule = 0;

    /**
     * 串点费用
     */
    private BigDecimal crossPointFee = BigDecimal.ZERO;

    /**
     * 整车运费
     */
    private BigDecimal carrierFreight = BigDecimal.ZERO;
}
