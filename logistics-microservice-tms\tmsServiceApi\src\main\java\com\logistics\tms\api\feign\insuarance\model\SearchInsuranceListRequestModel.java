package com.logistics.tms.api.feign.insuarance.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/6/4 19:15
 */
@Data
public class SearchInsuranceListRequestModel extends AbstractPageForm<SearchInsuranceListRequestModel> {
    @ApiModelProperty("保单状态：1 保障中，2 已过期，3 未开始，4 已作废，5 已退保")
    private Integer policyStatus;
    @ApiModelProperty("险种：1 商业险，2 交强险，3 个人意外险，4 货物险，5 危货承运人险")
    private Integer insuranceType;
    @ApiModelProperty("保险公司")
    private String insuranceCompanyName;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆机构 1 自主，2 外部，3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("司机")
    private String driverName;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("操作时间")
    private String lastModifiedTimeStart;
    @ApiModelProperty("操作时间")
    private String lastModifiedTimeEnd;

    @ApiModelProperty("看板跳转的IDS")
    private String ids;

    @ApiModelProperty(value = "是否导出,1导出，0不导出")
    private Integer ifExport;
}
