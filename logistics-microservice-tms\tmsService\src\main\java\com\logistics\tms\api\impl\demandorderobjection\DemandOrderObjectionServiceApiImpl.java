package com.logistics.tms.api.impl.demandorderobjection;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.demandorderobjection.DemandOrderObjectionServiceApi;
import com.logistics.tms.api.feign.demandorderobjection.model.SearchDemandOrderObjectionRequestModel;
import com.logistics.tms.api.feign.demandorderobjection.model.SearchDemandOrderObjectionResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.demandorderobjection.DemandOrderObjectionBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class DemandOrderObjectionServiceApiImpl implements DemandOrderObjectionServiceApi {

    @Autowired
    private DemandOrderObjectionBiz demandOrderObjectionBiz;

    /**
     * 云盘需求单异常列表
     *
     * @param  requestModel
     */
    @Override
    public Result<PageInfo<SearchDemandOrderObjectionResponseModel>> searchDemandOrderObjection(@RequestBody SearchDemandOrderObjectionRequestModel requestModel) {
        return Result.success(demandOrderObjectionBiz.searchDemandOrderObjection(requestModel));
    }

    /**
     * 导出云盘需求单异常列表
     *
     * @param  requestModel
     */
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    @Override
    public Result<PageInfo<SearchDemandOrderObjectionResponseModel>> exportDemandOrderObjection(@RequestBody SearchDemandOrderObjectionRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        return Result.success(demandOrderObjectionBiz.searchDemandOrderObjection(requestModel));
    }
}
