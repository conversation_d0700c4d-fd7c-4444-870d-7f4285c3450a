package com.logistics.management.webapi.controller.settlestatement.tradition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TraditionCarrierAssociationCarrierOrderItem {

	@ApiModelProperty("运单号")
	private String carrierOrderCode = "";

	@ApiModelProperty("车主公司  企业:公司名  个人:联系人姓名+手机号")
	private String companyCarrierName = "";

	@ApiModelProperty("货主公司")
	private String companyEntrustName = "";

	@ApiModelProperty("结算数量")
	private String settlementAmount = "";

	@ApiModelProperty("货物单位")
	private String goodsUnit = "";

	@ApiModelProperty("车主运费")
	private String setSettlementCost = "";
}
