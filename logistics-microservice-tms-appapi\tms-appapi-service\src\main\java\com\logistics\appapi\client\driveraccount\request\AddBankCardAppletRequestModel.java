package com.logistics.appapi.client.driveraccount.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/29
 */
@Data
public class AddBankCardAppletRequestModel {

	@ApiModelProperty(value = "司机账户ID,更改时必填")
	private Long driverAccountId;

	@ApiModelProperty(value = "开户银行名称 4-20位")
	private String bankAccountName;

	@ApiModelProperty(value = "银行账号 9-30位")
	private String bankAccount;

	@ApiModelProperty(value = "开户支行名称, 非必填 填写时检验长度0-20")
	private String braBankName;

	@ApiModelProperty(value = "收款证件图片")
	private List<String> bankAccountImage;

	@ApiModelProperty("验证码来源")
	private Integer codeSource;

	@ApiModelProperty("验证码类型")
	private Integer codeType;

	@ApiModelProperty(value = "短信验证码")
	private String verificationCode;
}
