package com.logistics.management.webapi.client.routeenquiry;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.routeenquiry.hystrix.RouteEnquiryClientHystrix;
import com.logistics.management.webapi.client.routeenquiry.request.*;
import com.logistics.management.webapi.client.routeenquiry.response.GetRouteEnquiryDetailResponseModel;
import com.logistics.management.webapi.client.routeenquiry.response.GetRouteEnquiryQuoteDetailResponseModel;
import com.logistics.management.webapi.client.routeenquiry.response.SearchRouteEnquiryListResponseModel;
import com.logistics.management.webapi.client.routeenquiry.response.SearchRouteEnquirySummaryListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/7/10 13:45
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/routeEnquiry",
        fallback = RouteEnquiryClientHystrix.class)
public interface RouteEnquiryClient {

    /**
     * 查询列表
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/searchList")
    Result<PageInfo<SearchRouteEnquiryListResponseModel>> searchList(@RequestBody SearchRouteEnquiryListRequestModel requestModel);

    /**
     * 创建
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/create")
    Result<Boolean> create(@RequestBody CreateRouteEnquiryRequestModel requestModel);

    /**
     * 查询详情
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/getDetail")
    Result<GetRouteEnquiryDetailResponseModel> getDetail(@RequestBody GetRouteEnquiryDetailRequestModel requestModel);

    /**
     * 取消报价
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/cancelQuote")
    Result<Boolean> cancelQuote(@RequestBody GetRouteEnquiryDetailRequestModel requestModel);

    /**
     * 查询车主报价详情
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/getQuoteDetail")
    Result<List<GetRouteEnquiryQuoteDetailResponseModel>> getQuoteDetail(@RequestBody GetRouteEnquiryQuoteDetailRequestModel requestModel);

    /**
     * 选择车主报价
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/selectCarrierQuote")
    Result<Boolean> selectCarrierQuote(@RequestBody RouteEnquirySelectCarrierQuoteRequestModel requestModel);

    /**
     * 结算审核
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/settleAudit")
    Result<Boolean> settleAudit(@RequestBody RouteEnquirySettleAuditRequestModel requestModel);

    /**
     * 归档
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/archive")
    Result<Boolean> archive(@RequestBody RouteEnquiryArchiveRequestModel requestModel);

    /**
     * 查询汇总列表
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/searchSummaryList")
    Result<PageInfo<SearchRouteEnquirySummaryListResponseModel>> searchSummaryList(@RequestBody SearchRouteEnquirySummaryListRequestModel requestModel);

    /**
     * 导出汇总列表
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/exportSummaryList")
    Result<List<SearchRouteEnquirySummaryListResponseModel>> exportSummaryList(@RequestBody SearchRouteEnquirySummaryListRequestModel requestModel);

}
