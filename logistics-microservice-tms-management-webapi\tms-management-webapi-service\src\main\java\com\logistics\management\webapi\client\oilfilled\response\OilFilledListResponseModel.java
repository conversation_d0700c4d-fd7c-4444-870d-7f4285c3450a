package com.logistics.management.webapi.client.oilfilled.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OilFilledListResponseModel {

    @ApiModelProperty(value = "充油记录ID")
    private Long oilFilledId;
    @ApiModelProperty(value = "结算状态0 待结算，1 已结算")
    private Integer status;
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆机构：1 自主 2 外部 3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty(value = "司机姓名")
    private String name;
    @ApiModelProperty(value = "司机手机号")
    private String mobile;
    @ApiModelProperty(value = "充油金额")
    private BigDecimal oilFilledFee;
    @ApiModelProperty(value = "费用来源：10 充油，20 退款")
    private Integer source;
    @ApiModelProperty(value = "充值时间")
    private Date oilFilledDate;
    @ApiModelProperty(value = "充油方式")
    private Integer oilFilledType;
    @ApiModelProperty(value = "升数")
    private Integer liter;
    @ApiModelProperty(value = "充值积分")
    private BigDecimal topUpIntegral;
    @ApiModelProperty(value = "奖励积分")
    private Integer rewardIntegral;
    @ApiModelProperty(value = "副卡卡号")
    private String subCardNumber;
    @ApiModelProperty(value = "副卡所属人")
    private String subCardOwner;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "操作人")
    private String lastModifiedBy;
    @ApiModelProperty(value = "操作时间")
    private Date lastModifiedTime;
    @ApiModelProperty(value = "退款原因类型：0 其他，10 丢失副卡，20 车辆过户，30 车辆报废，40 充油充错")
    private Integer refundReasonType;
    @ApiModelProperty(value = "退款原因")
    private String refundReason;
}
