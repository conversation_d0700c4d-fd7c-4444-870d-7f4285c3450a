package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2021/11/8 17:59
 */
public enum DemandOrderObjectionTypeEnum {

    DEFAULT(0,""),
    COMPANY(1,"我司原因"),
    CUSTOMER(2,"客户原因"),
    FORCE_MAJEURE(3, "不可抗力"),
    LOGISTICS(4, "物流原因"),
    COMMUNICATION(5, "平台问题"),
    AMOUNT(10,"数量问题"),
    REPEAT(20,"重报问题"),
    ASSOCIATION(30,"联系问题"),
    ADDRESS(40,"地址问题"),
    LOAD(50,"装车问题"),
    WAIT(60,"等待问题"),
    OTHER(70,"其他问题"),
    ;

    private Integer key;
    private String value;

    DemandOrderObjectionTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
    public static DemandOrderObjectionTypeEnum getEnum(Integer key) {
        for (DemandOrderObjectionTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
