package com.logistics.tms.controller.renewableaudit;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.renewableaudit.RenewableAuditBiz;
import com.logistics.tms.controller.renewableaudit.request.*;
import com.logistics.tms.controller.renewableaudit.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/14 14:39
 */
@Api(value = "新生审核")
@RestController
public class RenewableAuditController {

    @Resource
    private RenewableAuditBiz renewableAuditBiz;

    /**
     * 新生审核列表
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(("新生审核列表"))
    @PostMapping(value = "/service/renewableAudit/renewableAuditList")
    public Result<PageInfo<RenewableAuditResponseModel>> renewableAuditList(@RequestBody RenewableAuditRequestModel requestModel) {
        return Result.success(renewableAuditBiz.renewableAuditList(requestModel));
    }

    /**
     * 导出审核列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出审核列表")
    @PostMapping(value = "/service/renewableAudit/exportRenewableAuditList")
    public Result<List<RenewableAuditResponseModel>> exportRenewableAuditList(@RequestBody RenewableAuditRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        return Result.success(renewableAuditBiz.renewableAuditList(requestModel).getList());
    }

    /**
     * 详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "新生审核列表详情")
    @PostMapping(value = "/service/renewableAudit/getRenewableAuditDetail")
    public Result<RenewableAuditDetailResponseModel> getRenewableAuditDetail(@RequestBody RenewableAuditDetailRequestModel requestModel) {
        RenewableAuditDetailResponseModel renewableAuditDetail = renewableAuditBiz.getRenewableAuditDetail(requestModel);
        return Result.success(renewableAuditDetail);
    }

    /**
     * 操作日志
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "操作日志查询")
    @PostMapping(value = "/service/renewableAudit/getRenewableAuditLogs")
    public Result<List<RenewableAuditOperateLogsResponseModel>> getRenewableAuditLogs(@RequestBody RenewableAuditDetailRequestModel requestModel) {
        return Result.success(renewableAuditBiz.getRenewableAuditLogs(requestModel));
    }

    /**
     * 查看票据
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查询票据信息")
    @PostMapping(value = "/service/renewableAudit/getRenewableAuditTickets")
    public Result<List<RenewableAuditTicketsResponseModel>> getRenewableAuditTickets(@RequestBody RenewableAuditDetailRequestModel requestModel) {
        return Result.success(renewableAuditBiz.getRenewableAuditTickets(requestModel));
    }

    /**
     * 指派司机-详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "指派司机-详情")
    @PostMapping(value = "/service/renewableAudit/getAssignDriverDetail")
    public Result<List<RenewableAssignDriverDetailResponseModel>> getAssignDriverDetail(@RequestBody RenewableAssignDriverDetailRequestModel requestModel) {
        return Result.success(renewableAuditBiz.getAssignDriverDetail(requestModel));
    }

    /**
     * 指派司机-指派
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "指派司机-指派")
    @PostMapping(value = "/service/renewableAudit/assignDriver")
    public Result<Boolean> assignDriver(@RequestBody RenewableAssignDriverRequestModel requestModel) {
        return Result.success(renewableAuditBiz.assignDriver(requestModel));
    }

    /**
     * 修改指派
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "修改指派-提交")
    @PostMapping(value = "/service/renewableAudit/updateAssignDriver")
    public Result<Boolean> updateAssignDriver(@RequestBody RenewableAssignDriverRequestModel requestModel) {
        return Result.success(renewableAuditBiz.updateAssignDriver(requestModel));
    }
    /**
     * 确认信息-详情
     * @param requestModel
     * @return
     */
    @ApiOperation(("确认信息-详情"))
    @PostMapping(value = "/service/renewableAudit/confirmNewsDetail")
    public Result<RenewableConfirmGoodsResponseModel> confirmNewsDetail(@RequestBody RenewableAuditDetailRequestModel requestModel) {
        return Result.success(renewableAuditBiz.confirmNewsDetail(requestModel));
    }

    /**
     * 确认信息-确认
     * @param requestModel
     * @return
     */
    @ApiOperation(("确认信息-提交"))
    @PostMapping(value = "/service/renewableAudit/confirmNews")
    public Result<Boolean> confirmNews(@RequestBody RenewableConfirmGoodsRequestModel requestModel) {
        return Result.success(renewableAuditBiz.confirmNews(requestModel));
    }

    /**
     * 新生审核列表统计
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "新生审核列表统计")
    @PostMapping(value = "/service/renewableAudit/getRenewableAuditListStatistics")
    public Result<RenewableAuditListStatisticsResponseModel> getRenewableAuditListStatistics(@RequestBody RenewableAuditRequestModel requestModel) {
        return Result.success(renewableAuditBiz.getRenewableAuditListStatistics(requestModel));
    }

    /**
     * 取消回收单(新生调用),并同时取消审核记录
     *
     * @param requestModel 回收单号
     * @return 操作结果
     */
    @ApiOperation(value = "取消回收单(新生调用)")
    @PostMapping(value = "/service/renewableAudit/cancelRecycleOrder")
    public Result<Boolean> cancelRecycleOrder(@RequestBody CancelRecycleOrderRequestModel requestModel) {
        renewableAuditBiz.cancelRecycleOrder(requestModel);
        return Result.success(true);
    }

    /**
     * 订单tab汇总
     *
     * @return
     */
    @ApiOperation("订单tab汇总")
    @PostMapping(value = "/service/applet/yeloLife/renewableOrderListStatistic")
    public Result<RenewableOrderListStatisticResponseModel> renewableOrderListStatistic() {
        return Result.success(renewableAuditBiz.renewableOrderListStatistic());
    }

    /**
     * 新生订单确认列表
     *
     * @param requestModel
     * @return
     */
    @ApiOperation("新生订单确认列表")
    @PostMapping(value = "/service/applet/yeloLife/renewableOrderList")
    public Result<PageInfo<RenewableOrderListResponseModel>> renewableOrderList(@RequestBody RenewableOrderListRequestModel requestModel) {
        return Result.success(renewableAuditBiz.renewableOrderList(requestModel));
    }

    /**
     * 新生订单详情
     *
     * @param requestModel
     * @return
     */
    @ApiOperation("新生订单详情")
    @PostMapping(value = "/service/applet/yeloLife/renewableOrderDetail")
    public Result<RenewableOrderDetailResponseModel> renewableOrderDetail(@RequestBody RenewableOrderDetailRequestModel requestModel) {
        return Result.success(renewableAuditBiz.renewableOrderDetail(requestModel));
    }

    /**
     * 新生订单详情-确认提交货物
     *
     * @param requestModel
     * @return
     */
    @ApiOperation("新生订单详情-确认提交货物")
    @PostMapping(value = "/service/applet/yeloLife/submitGoods")
    public Result<Boolean> submitGoods(@RequestBody RenewableOrderSubmitGoodsRequestModel requestModel) {
        return Result.success(renewableAuditBiz.submitGoods(requestModel));
    }

    /**
     * 新生订单详情-确认提交单据
     *
     * @param requestModel
     * @return
     */
    @ApiOperation("新生订单详情-确认提交单据")
    @PostMapping(value = "/service/applet/yeloLife/submitTicket")
    public Result<Boolean> submitTicket(@RequestBody RenewableOrderSubmitTicketRequestModel requestModel) {
        return Result.success(renewableAuditBiz.submitTicket(requestModel));
    }

    /**
     * 新生订单详情-确认提交信息
     *
     * @param requestModel
     * @return
     */
    @ApiOperation("新生订单详情-确认提交信息")
    @PostMapping(value = "/service/applet/yeloLife/submitRenewableOrder")
    public Result<Boolean> submitRenewableOrder(@RequestBody SubmitRenewableOrderRequestModel requestModel) {
        return Result.success(renewableAuditBiz.submitRenewableOrder(requestModel));
    }

    /**
     * 新生订单详情-查询收货仓库(先查询大数据再查询云仓仓库地址)
     *
     * @param requestModel
     * @return
     */
    @ApiOperation("新生订单详情-查询收货仓库(先查询大数据再查询云仓仓库地址)")
    @PostMapping(value = "/service/applet/yeloLife/searchWarehouse")
    public Result<List<SearchWarehouseResponseModel>> searchWarehouse(@RequestBody SearchWarehouseRequestModel requestModel) {
        return Result.success(renewableAuditBiz.searchWarehouse(requestModel));
    }

    /**
     * 驾驶员下单-查询发货人(先查询大数据再查询新生客户地址)
     *
     * @param requestModel
     * @return
     */
    @ApiOperation("驾驶员下单-查询发货人(先查询大数据再查询新生客户地址)")
    @PostMapping(value = "/service/applet/yeloLife/searchConsignor")
    public Result<List<SearchConsignorResponseModel>> searchConsignor(@RequestBody SearchConsignorRequestModel requestModel) {
        return Result.success(renewableAuditBiz.searchConsignor(requestModel));
    }

    /**
     * 驾驶员下单
     *
     * @param requestModel
     * @return
     */
    @ApiOperation("驾驶员下单")
    @PostMapping(value = "/service/applet/yeloLife/publishRenewableOrder")
    public Result<PublishRenewableOrderResponseModel> publishRenewableOrder(@RequestBody PublishRenewableOrderRequestModel requestModel) {
        return Result.success(renewableAuditBiz.publishRenewableOrder(requestModel));
    }

    /**
     * 查询sku下拉列表
     *
     * @param requestModel
     * @return
     */
    @ApiOperation("查询sku下拉列表")
    @PostMapping(value = "/service/applet/yeloLife/searchLifeSku")
    public Result<List<SearchLifeSkuResponseModel>> searchLifeSku(@RequestBody SearchLifeSkuRequestModel requestModel) {
        return Result.success(renewableAuditBiz.searchLifeSku(requestModel));
    }

    /**
     * 查看sku示例内容
     *
     * @param requestModel
     * @return
     */
    @ApiOperation("查看sku示例内容")
    @PostMapping(value = "/service/applet/yeloLife/searchSkuDetail")
    public Result<SearchSkuDetailResponseModel> searchSkuDetail(@RequestBody SearchSkuDetailRequestModel requestModel) {
        return Result.success(renewableAuditBiz.searchSkuDetail(requestModel));
    }
}
