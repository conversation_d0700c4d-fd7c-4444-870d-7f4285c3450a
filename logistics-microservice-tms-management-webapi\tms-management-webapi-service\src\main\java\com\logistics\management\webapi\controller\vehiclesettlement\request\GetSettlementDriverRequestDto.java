package com.logistics.management.webapi.controller.vehiclesettlement.request;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class GetSettlementDriverRequestDto {

    @ApiModelProperty("结算Id")
    @NotBlank(message = "结算Id不能为空")
    private String vehicleSettlementId;

    @ApiModelProperty("司机姓名或手机号")
    private String driverName;

}
