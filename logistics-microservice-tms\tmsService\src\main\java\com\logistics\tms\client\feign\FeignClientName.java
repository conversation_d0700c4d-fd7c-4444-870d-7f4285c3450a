package com.logistics.tms.client.feign;

public interface FeignClientName {
    //公共-auth服务
    String AUTH_SERVICES = "auth-services";

    //公共-基础数据服务
    String BASIC_DATA_SERVICES = "yelo-basicData-services";

    //云盘订单服务
    String TRAY_ORDER_SERVICES = "order-services";

    /**
     *云盘基础数据
     */
    String LEYI_BASIC_DATA_SERVICES = "basicData-services";

    //云仓库存服务
    String WAREHOUSE_STOCK_SERVICES = "warehouse-stock-service";

    //云仓新生服务
    String WAREHOUSE_LIFE_SERVICES = "warehouse-life-service";
}
