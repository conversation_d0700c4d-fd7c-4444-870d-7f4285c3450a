package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TSafetyGroupMeetingAttachment;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TSafetyGroupMeetingAttachmentMapper extends BaseMapper<TSafetyGroupMeetingAttachment> {



    int batchInsertSelective(@Param("list") List<TSafetyGroupMeetingAttachment> tSafetyGroupMeetingAttachmentList);


}