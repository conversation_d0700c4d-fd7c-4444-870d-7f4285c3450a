package com.logistics.management.webapi.controller.biddingorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class SearchBiddingDemandRequestDto {


    /**
     * 发货地
     */
    @ApiModelProperty("发货地")
    @NotBlank(message = "发货地不能为空")
    private String loadAddress;

    /**
     * 卸货地
     */
    @ApiModelProperty("卸货地")
    @NotBlank(message = "卸货地不能为空")
    private String unloadAddress;

    /**
     * 货物数量-低档
     */
    @ApiModelProperty("货物数量-低档")
    @NotBlank(message = "货物数量不能为空")
    private String goodsCountLow;

    /**
     * 货物数量-高档
     */
    @ApiModelProperty("货物数量-高档")
    @NotBlank(message = "货物数量不能为空")
    private String goodsCountHigh;


}
