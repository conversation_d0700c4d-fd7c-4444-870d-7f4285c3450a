package com.logistics.management.webapi.controller.region.mapping;

import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.client.region.response.RegionCompanyResponseModel;
import com.logistics.management.webapi.controller.region.response.RegionCompanyResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2024/6/5 14:34
 */
public class GetRegionCompanyMapping extends MapperMapping<RegionCompanyResponseModel, RegionCompanyResponseDto> {
    @Override
    public void configure() {
        RegionCompanyResponseModel source = getSource();
        RegionCompanyResponseDto destination = getDestination();

        //个人类型 把联系人+手机号当作公司名
        if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyType())) {
            destination.setCompanyName(source.getContactName() + " " + source.getContactPhone());
        }
    }
}
