package com.logistics.management.webapi.controller.invoicingmanagement.packaging.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/3/19 17:10
 */
@Data
public class GetInvoiceListResponseDto {
    @ApiModelProperty("发票id")
    private String invoiceId="";

    @ApiModelProperty("发票类型")
    private String invoiceType="";

    @ApiModelProperty("发票类型文本")
    private String invoiceTypeLabel="";

    @ApiModelProperty("发票代码")
    private String invoiceCode="";

    @ApiModelProperty("发票号码")
    private String invoiceNum="";

    @ApiModelProperty("开票日期")
    private String invoiceDate="";

    @ApiModelProperty("发票金额")
    private String invoiceAmount="";

    @ApiModelProperty("税率")
    private String taxRate="";

    @ApiModelProperty("税额合计")
    private String totalTaxAndPrice="";

    @ApiModelProperty("发票图片")
    private String invoicePictureUrl="";

    @ApiModelProperty("操作人")
    private String lastModifiedBy="";

    @ApiModelProperty("操作时间")
    private String lastModifiedTime="";
}
