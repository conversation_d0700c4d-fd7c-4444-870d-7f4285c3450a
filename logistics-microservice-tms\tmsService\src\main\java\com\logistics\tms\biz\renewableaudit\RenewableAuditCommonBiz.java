package com.logistics.tms.biz.renewableaudit;


import com.alibaba.fastjson.JSON;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.demandorder.DemandOrderCommonBiz;
import com.logistics.tms.biz.renewableaudit.model.CreateDemandOrderForRenewableAuditModel;
import com.logistics.tms.biz.renewableaudit.model.CreateDemandOrderGoodsForRenewableAuditModel;
import com.logistics.tms.client.BasicDataClient;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.logistics.tms.rabbitmq.publisher.model.SyncLogisticsDemandOrderInfo;
import com.yelo.basicdata.api.feign.datamap.model.GetLonLatByMapIdRequestModel;
import com.yelo.basicdata.api.feign.datamap.model.GetLonLatByMapIdResponseModel;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class RenewableAuditCommonBiz {

    @Autowired
    private TRenewableAuditMapper tRenewableAuditMapper;
    @Autowired
    private TRenewableAuditAddressMapper tRenewableAuditAddressMapper;
    @Autowired
    private TDriverAppointMapper tDriverAppointMapper;
    @Autowired
    private TDriverAppointAddressMapper tDriverAppointAddressMapper;
    @Autowired
    private TDriverAppointGoodsMapper tDriverAppointGoodsMapper;
    @Autowired
    private BasicDataClient basicDataClient;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TCompanyEntrustMapper tCompanyEntrustMapper;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;

    //需求单相关
    @Autowired
    private TDemandOrderMapper tDemandOrderMapper;
    @Autowired
    private TDemandOrderAddressMapper tDemandOrderAddressMapper;
    @Autowired
    private TDemandOrderGoodsMapper tDemandOrderGoodsMapper;
    @Autowired
    private TDemandOrderCarrierMapper tDemandOrderCarrierMapper;
    @Autowired
    private TDemandOrderOperateLogsMapper tDemandOrderOperateLogsMapper;
    @Autowired
    private TDemandOrderEventsMapper tDemandOrderEventsMapper;
    @Autowired
    private DemandOrderCommonBiz demandOrderCommonBiz;

    @Autowired
    private RabbitMqPublishBiz rabbitMqPublishBiz;
    @Autowired
    private TCertificationPicturesMapper tCertificationPicturesMapper;

    /**
     * 更新新生订单地址经纬度
     *
     * @param address 新生订单地址
     */
    @Transactional
    public void updateRenewableAddressLonAndLat(TRenewableAuditAddress address) {
        log.info("updateRenewableAddressLonAndLat—>params:{}", JSON.toJSONString(address));
        //省份不存在不查询经纬度
        if (StringUtils.isBlank(address.getLoadProvinceName()) && StringUtils.isBlank(address.getUnloadProvinceName())) {
            return;
        }

        GetLonLatByMapIdRequestModel model;

        GetLonLatByMapIdResponseModel loadLonModel = null;
        if (StringUtils.isNotBlank(address.getLoadProvinceName())) {
            StringBuilder loadAddress = new StringBuilder();//提货省市区
            if (StringUtils.isNotBlank(address.getLoadDetailAddress())) {
                loadAddress.append(address.getLoadProvinceName()).append(address.getLoadCityName()).append(address.getLoadAreaName()).append(address.getLoadDetailAddress());
            }
            model = new GetLonLatByMapIdRequestModel();
            model.setAddress(loadAddress.toString());
            model.setMapId(address.getLoadAreaId());
            loadLonModel = basicDataClient.getLonLatByMapId(model);
        }

        GetLonLatByMapIdResponseModel unloadLonModel = null;
        if (StringUtils.isNotBlank(address.getUnloadProvinceName())) {
            StringBuilder unloadAddress = new StringBuilder();//卸货省市区
            if (StringUtils.isNotBlank(address.getUnloadDetailAddress())) {
                unloadAddress.append(address.getUnloadProvinceName()).append(address.getUnloadCityName()).append(address.getUnloadAreaName()).append(address.getUnloadDetailAddress());
            }
            model = new GetLonLatByMapIdRequestModel();
            model.setAddress(unloadAddress.toString());
            model.setMapId(address.getUnloadAreaId());
            unloadLonModel = basicDataClient.getLonLatByMapId(model);
        }
        if ((loadLonModel == null || StringUtils.isBlank(loadLonModel.getLongitude())) && (unloadLonModel == null || StringUtils.isBlank(unloadLonModel.getLongitude()))){
            return;
        }

        TRenewableAuditAddress upRenewableAuditAddress = new TRenewableAuditAddress();
        upRenewableAuditAddress.setId(address.getId());
        if (loadLonModel != null) {
            upRenewableAuditAddress.setLoadLongitude(loadLonModel.getLongitude());
            upRenewableAuditAddress.setLoadLatitude(loadLonModel.getLatitude());
        }
        if (unloadLonModel != null) {
            upRenewableAuditAddress.setUnloadLongitude(unloadLonModel.getLongitude());
            upRenewableAuditAddress.setUnloadLatitude(unloadLonModel.getLatitude());
        }

        log.info("updateRenewableAddressLonAndLat—>update->params:{}", JSON.toJSONString(address));
        tRenewableAuditAddressMapper.updateByPrimaryKeySelectiveEncrypt(upRenewableAuditAddress);
    }

    /**
     * 生成需求单、预约记录-司机下单（新生无账号）、新生订单已审核
     * @param demandOrderModel
     */
    @Transactional
    public void createDemandOrder(CreateDemandOrderForRenewableAuditModel demandOrderModel){
        //查询新生货物信息
        String yeloLifeCompanyName = commonBiz.getYeloLifeCompanyName();
        TCompanyEntrust dbCompanyEntrust = tCompanyEntrustMapper.getByName(yeloLifeCompanyName);
        if (dbCompanyEntrust == null) {
            throw new BizException(EntrustDataExceptionEnum.COMPANY_ENTRUST_EMPTY);
        }
        //查询我司公司信息
        String companyCarrierName = commonBiz.getQiyaCompanyName();
        if (StringUtils.isBlank(companyCarrierName)) {
            throw new BizException(EntrustDataExceptionEnum.QIYA_COMPANY_EMPTY);
        }
        TCompanyCarrier dbCompanyCarrier = tCompanyCarrierMapper.getByName(companyCarrierName);
        if (dbCompanyCarrier == null) {
            throw new BizException(EntrustDataExceptionEnum.QIYA_COMPANY_EMPTY);
        }

        String userName = demandOrderModel.getPublishName();
        Date now = demandOrderModel.getPublishTime();

        BigDecimal goodsAmount = BigDecimal.ZERO;
        BigDecimal goodsAmountTotal = CommonConstant.BIG_DECIMAL_ZERO;
        BigDecimal goodsPriceTotal = CommonConstant.BIG_DECIMAL_ZERO;
        //转换货物信息
        for (CreateDemandOrderGoodsForRenewableAuditModel model : demandOrderModel.getGoodsList()) {
            goodsAmountTotal = goodsAmountTotal.add(model.getGoodsAmount());
            goodsPriceTotal = goodsPriceTotal.add((model.getGoodsAmount().multiply(model.getGoodsPrice())).setScale(2, RoundingMode.HALF_UP));//每个货物合计价格四舍五入的值相加(兼容新生逻辑)

            goodsAmount = goodsAmount.add(model.getGoodsAmount().divide(CommonConstant.BIG_DECIMAL_ONE_THOUSAND_HUNDRED, 3, BigDecimal.ROUND_HALF_UP));
        }
        goodsAmount = goodsAmount.setScale(3, BigDecimal.ROUND_HALF_UP);


        //新增需求单
        TDemandOrder tDemandOrder = new TDemandOrder();
        tDemandOrder.setDemandOrderCode(commonBiz.getBusinessTypeCode(BusinessCodeTypeEnum.DEMAND_ORDER_CODE_YELOLIFE, "", userName));
        tDemandOrder.setCustomerOrderCode(demandOrderModel.getRenewableOrderCode());

        //不同的来源需求单状态不一样
        if (CommonConstant.ONE.equals(demandOrderModel.getRequestSource())){//司机下单（新生无账号）
            //待发布
            tDemandOrder.setStatus(DemandOrderStatusEnum.WAIT_PUBLISH.getKey());
            tDemandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_PUBLISH.getKey());
        }else{//新生订单已审核
            //待调度
            tDemandOrder.setStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
            tDemandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
        }

        tDemandOrder.setStatusUpdateTime(now);
        tDemandOrder.setSource(DemandOrderSourceEnum.YELO_LIFE.getKey());
        tDemandOrder.setBusinessType(demandOrderModel.getBusinessType());
        tDemandOrder.setCustomerName(demandOrderModel.getCustomerName());
        tDemandOrder.setCustomerUserName(demandOrderModel.getCustomerUserName());
        tDemandOrder.setCustomerUserMobile(demandOrderModel.getCustomerUserMobile());
        tDemandOrder.setCustomerOrderSource(demandOrderModel.getCustomerOrderSource());
        tDemandOrder.setPublishName(userName);
        tDemandOrder.setPublishMobile(demandOrderModel.getPublishMobile());
        tDemandOrder.setPublishTime(now);
        tDemandOrder.setSettlementTonnage(dbCompanyEntrust.getSettlementTonnage());
        tDemandOrder.setGoodsAmount(goodsAmount);
        tDemandOrder.setNotArrangedAmount(goodsAmount);
        tDemandOrder.setGoodsUnit(GoodsUnitEnum.BY_WEIGHT.getKey());
        tDemandOrder.setRemark(demandOrderModel.getRemark());

        //货主信息
        tDemandOrder.setCompanyEntrustId(dbCompanyEntrust.getId());
        tDemandOrder.setCompanyEntrustName(yeloLifeCompanyName);

        //车主信息
        tDemandOrder.setCompanyCarrierType(dbCompanyCarrier.getType());
        tDemandOrder.setCompanyCarrierId(dbCompanyCarrier.getId());
        tDemandOrder.setCompanyCarrierName(companyCarrierName);
        tDemandOrder.setCompanyCarrierLevel(dbCompanyCarrier.getLevel());

        commonBiz.setBaseEntityAdd(tDemandOrder, demandOrderModel.getPublishName());
        tDemandOrderMapper.insertSelectiveEncrypt(tDemandOrder);


        //新增需求单地址
        TDemandOrderAddress tDemandOrderAddress = MapperUtils.mapper(demandOrderModel.getAddressModel(), TDemandOrderAddress.class);
        tDemandOrderAddress.setId(null);
        tDemandOrderAddress.setDemandOrderId(tDemandOrder.getId());
        commonBiz.setBaseEntityAdd(tDemandOrderAddress, demandOrderModel.getPublishName());
        tDemandOrderAddressMapper.insertSelective(tDemandOrderAddress);


        //新增需求单货物
        TDemandOrderGoods demandOrderGoods;
        List<TDemandOrderGoods> demandOrderGoodsList = new ArrayList<>();
        for (CreateDemandOrderGoodsForRenewableAuditModel model : demandOrderModel.getGoodsList()) {
            //组装货物信息
            demandOrderGoods = new TDemandOrderGoods();
            demandOrderGoods.setDemandOrderId(tDemandOrder.getId());
            demandOrderGoods.setSkuCode(model.getSkuCode());
            demandOrderGoods.setGoodsAmount(model.getGoodsAmount().divide(CommonConstant.BIG_DECIMAL_ONE_THOUSAND_HUNDRED, 3, BigDecimal.ROUND_HALF_UP));
            demandOrderGoods.setNotArrangedAmount(demandOrderGoods.getGoodsAmount());
            demandOrderGoods.setGoodsName(model.getGoodsName());
            commonBiz.setBaseEntityAdd(demandOrderGoods, demandOrderModel.getPublishName());
            demandOrderGoodsList.add(demandOrderGoods);
        }
        if (ListUtils.isNotEmpty(demandOrderGoodsList)) {
            tDemandOrderGoodsMapper.batchInsert(demandOrderGoodsList);
        }

        //新增记录到需求单车主变更表
        TDemandOrderCarrier tDemandOrderCarrier = new TDemandOrderCarrier();
        tDemandOrderCarrier.setDemandOrderId(tDemandOrder.getId());
        tDemandOrderCarrier.setCompanyCarrierId(tDemandOrder.getCompanyCarrierId());
        tDemandOrderCarrier.setCarrierContactId(tDemandOrder.getCarrierContactId());
        tDemandOrderCarrier.setCarrierPrice(tDemandOrder.getCarrierPrice());
        tDemandOrderCarrier.setCarrierPriceType(tDemandOrder.getCarrierPriceType());
        commonBiz.setBaseEntityAdd(tDemandOrderCarrier, userName);
        tDemandOrderCarrierMapper.insertSelective(tDemandOrderCarrier);


        //司机下单（新生无账号）或司机下单（新生有账号）订单已审核，生成预约记录
        if (CommonConstant.ONE.equals(demandOrderModel.getRequestSource()) || RenewableSourceTypeEnum.DRIVER_PLACE_ORDER.getKey().equals(demandOrderModel.getCustomerOrderSource())) {
            //生成预约记录信息
            TDriverAppoint driverAppoint = new TDriverAppoint();
            driverAppoint.setDemandOrderId(tDemandOrder.getId());
            driverAppoint.setDemandOrderCode(tDemandOrder.getDemandOrderCode());
            driverAppoint.setCustomerName(tDemandOrder.getCustomerName());
            driverAppoint.setCustomerUserName(tDemandOrder.getCustomerUserName());
            driverAppoint.setCustomerUserMobile(tDemandOrder.getCustomerUserMobile());
            driverAppoint.setBusinessType(tDemandOrder.getBusinessType());
            driverAppoint.setStaffId(demandOrderModel.getStaffId());
            driverAppoint.setStaffProperty(demandOrderModel.getStaffProperty());
            driverAppoint.setStaffName(demandOrderModel.getStaffName());
            driverAppoint.setStaffMobile(demandOrderModel.getStaffMobile());
            driverAppoint.setGoodsAmountTotal(goodsAmountTotal.setScale(3, RoundingMode.HALF_UP));
            driverAppoint.setGoodsPriceTotal(goodsPriceTotal.setScale(2, RoundingMode.HALF_UP));
            driverAppoint.setPublishTime(now);
            driverAppoint.setPublishUserName(tDemandOrder.getPublishName());
            driverAppoint.setPublishUserMobile(tDemandOrder.getPublishMobile());
            if (CommonConstant.ONE.equals(demandOrderModel.getRequestSource())){//关联车辆状态
                driverAppoint.setIfAssociatedVehicle(CommonConstant.INTEGER_ZERO);
            }
            commonBiz.setBaseEntityAdd(driverAppoint, BaseContextHandler.getUserName());
            tDriverAppointMapper.insertSelectiveEncrypt(driverAppoint);

            //生成预约记录地址信息
            TDriverAppointAddress driverAppointAddress = MapperUtils.mapper(tDemandOrderAddress, TDriverAppointAddress.class);
            driverAppointAddress.setId(null);
            driverAppointAddress.setDriverAppointId(driverAppoint.getId());
            commonBiz.setBaseEntityAdd(driverAppointAddress, BaseContextHandler.getUserName());
            tDriverAppointAddressMapper.insertSelectiveEncrypt(driverAppointAddress);

            //生成预约记录货物信息
            TDriverAppointGoods driverAppointGoods;
            List<TDriverAppointGoods> goodsList = new ArrayList<>();
            for (CreateDemandOrderGoodsForRenewableAuditModel orderGood : demandOrderModel.getGoodsList()) {
                //创建预约货物
                driverAppointGoods = MapperUtils.mapper(orderGood, TDriverAppointGoods.class);
                driverAppointGoods.setId(null);
                driverAppointGoods.setDriverAppointId(driverAppoint.getId());
                driverAppointGoods.setRenewableSkuCode(orderGood.getSkuCode());
                driverAppointGoods.setGoodsUnit(YeloLifeGoodsUnitEnum.BY_WEIGHT.getKey());
                commonBiz.setBaseEntityAdd(driverAppointGoods, BaseContextHandler.getUserName());
                goodsList.add(driverAppointGoods);
            }
            tDriverAppointGoodsMapper.batchInsert(goodsList);

            //预约记录-上传现场图片和确认单据
            uploadPicture(demandOrderModel.getScenePictureList(), demandOrderModel.getConfirmPictureList(), driverAppoint.getId(), demandOrderModel.getRequestSource(), userName);
        }

        //新增需求单日志和事件
        AsyncProcessQueue.execute(() -> addDemandOrderLogs(tDemandOrder));

        //需求单地址经纬度为空，则更新需求单地址经纬度
        if (StringUtils.isBlank(tDemandOrderAddress.getLoadLongitude()) || StringUtils.isBlank(tDemandOrderAddress.getUnloadLongitude())){
            AsyncProcessQueue.execute(() -> demandOrderCommonBiz.updateAddressLonAndLat(Collections.singletonList(tDemandOrderAddress)));
        }


        //新生订单已审核
        if (CommonConstant.TWO.equals(demandOrderModel.getRequestSource())) {
            //回填新生订单审核表需求单信息
            TRenewableAudit tRenewableAudit = new TRenewableAudit();
            tRenewableAudit.setId(demandOrderModel.getRenewableOrderId());
            tRenewableAudit.setDemandOrderId(tDemandOrder.getId());
            tRenewableAudit.setDemandOrderCode(tDemandOrder.getDemandOrderCode());
            tRenewableAuditMapper.updateByPrimaryKeySelectiveEncrypt(tRenewableAudit);

            //将需求单与新生订单号关系同步新生系统
            SyncLogisticsDemandOrderInfo syncLogisticsDemandOrderInfo = new SyncLogisticsDemandOrderInfo();
            syncLogisticsDemandOrderInfo.setDemandOrderCode(tDemandOrder.getDemandOrderCode());
            syncLogisticsDemandOrderInfo.setRecycleOrderCode(demandOrderModel.getRenewableOrderCode());
            syncLogisticsDemandOrderInfo.setOperator(userName);
            rabbitMqPublishBiz.syncLogisticsDemandOrderInfo2YeloLife(syncLogisticsDemandOrderInfo);
        }
    }

    //新增需求单操作日志和事件
    public void addDemandOrderLogs(TDemandOrder tDemandOrder){
        Date now = tDemandOrder.getPublishTime();
        String userName = tDemandOrder.getPublishName();

        //生成需求单日志
        TDemandOrderOperateLogs demandOrderOperateLogs = getDemandOrderOperateLogs(tDemandOrder.getId(), DemandOrderOperateLogsEnum.CREATE_DEMAND_ORDER, userName, tDemandOrder.getRemark(), now, null);
        tDemandOrderOperateLogsMapper.insertSelective(demandOrderOperateLogs);

        //生成需求单事件
        TDemandOrderEvents demandOrderEvents = getDemandOrderEvent(tDemandOrder.getId(), tDemandOrder.getCompanyCarrierId(), DemandOrderEventsTypeEnum.ACCEPT_CARRIERORDER, userName, now);
        tDemandOrderEventsMapper.insertSelective(demandOrderEvents);
    }

    //创建需求单日志
    public TDemandOrderOperateLogs getDemandOrderOperateLogs(Long demandOrderId, DemandOrderOperateLogsEnum logsEnum, String userName, String remark, Date now, String operationContent) {
        TDemandOrderOperateLogs demandOrderOperateLogs = new TDemandOrderOperateLogs();
        demandOrderOperateLogs.setDemandOrderId(demandOrderId);
        demandOrderOperateLogs.setOperationType(logsEnum.getKey());
        if (StringUtils.isBlank(operationContent)) {
            demandOrderOperateLogs.setOperationContent(logsEnum.getValue());
        }else{
            demandOrderOperateLogs.setOperationContent(operationContent);
        }
        demandOrderOperateLogs.setRemark(remark);
        demandOrderOperateLogs.setOperatorName(userName);
        demandOrderOperateLogs.setOperateTime(now);
        commonBiz.setBaseEntityAdd(demandOrderOperateLogs, userName);
        return demandOrderOperateLogs;
    }

    //创建需求单事件
    public TDemandOrderEvents getDemandOrderEvent(Long demandOrderId, Long companyCarrierId, DemandOrderEventsTypeEnum typeEnum, String userName, Date now) {
        TDemandOrderEvents tDemandOrderEvents = new TDemandOrderEvents();
        tDemandOrderEvents.setDemandOrderId(demandOrderId);
        tDemandOrderEvents.setCompanyCarrierId(companyCarrierId);
        tDemandOrderEvents.setEvent(typeEnum.getKey());
        tDemandOrderEvents.setEventDesc(typeEnum.getValue());
        tDemandOrderEvents.setEventTime(now);
        tDemandOrderEvents.setOperatorName(userName);
        tDemandOrderEvents.setOperateTime(now);
        commonBiz.setBaseEntityAdd(tDemandOrderEvents, userName);
        return tDemandOrderEvents;
    }

    /**
     * 预约记录上传图片
     * @param scenePictureList 现场图片
     * @param confirmPictureList 确认票据
     * @param driverAppointId 预约记录表id
     * @param requestSource 请求来源：1 司机下单（新生无账号），2 新生订单已审核
     * @param userName 操作人
     */
    public void uploadPicture(List<String> scenePictureList, List<String> confirmPictureList, Long driverAppointId, String requestSource, String userName){
        if (ListUtils.isEmpty(scenePictureList) && ListUtils.isEmpty(confirmPictureList)){
            return;
        }

        //上传现场图片
        List<TCertificationPictures> picturesList = new ArrayList<>();
        if (ListUtils.isNotEmpty(scenePictureList)) {
            for (String picturePath : scenePictureList) {
                picturesList.add(buildCertificationPicture(driverAppointId,
                        picturePath,
                        CertificationPicturesFileTypeEnum.T_DRIVER_APPOINT_SCENE_PICTURE_FILE,
                        CopyFileTypeEnum.DRIVER_APPOINT_SITE_IMAGE.getKey(),
                        requestSource,
                        userName));
            }
        }

        //上传确认单据
        if (ListUtils.isNotEmpty(confirmPictureList)) {
            for (String picturePath : confirmPictureList) {
                picturesList.add(buildCertificationPicture(driverAppointId,
                        picturePath,
                        CertificationPicturesFileTypeEnum.T_DRIVER_APPOINT_CONFIRM_PICTURE_FILE,
                        CopyFileTypeEnum.DRIVER_APPOINT_CONFIRM_TICKET.getKey(),
                        requestSource,
                        userName));
            }
        }

        // 批量保存构建的图片信息
        if (ListUtils.isNotEmpty(picturesList)) {
            tCertificationPicturesMapper.batchInsert(picturesList);
        }
    }
    /**
     * 构建图片文件存储实体
     *
     * @param objectId 表id
     * @param picturePath 图片路径
     * @param typeEnum 图片类型枚举
     * @param copyFileType 拷贝图片类型
     * @param requestSource 请求来源：1 司机下单（新生无账号），2 新生订单已审核
     * @param userName 操作人
     * @return
     */
    private TCertificationPictures buildCertificationPicture(Long objectId,
                                                             String picturePath,
                                                             CertificationPicturesFileTypeEnum typeEnum,
                                                             Integer copyFileType,
                                                             String requestSource,
                                                             String userName) {
        //构建文件表实例
        TCertificationPictures certificationPictures = new TCertificationPictures();
        certificationPictures.setObjectId(objectId);
        certificationPictures.setObjectType(typeEnum.getObjectType().getObjectType());
        certificationPictures.setFileType(typeEnum.getFileType());
        certificationPictures.setFileTypeName(typeEnum.getFileName());
        certificationPictures.setFileName(typeEnum.getFileName());
        certificationPictures.setUploadTime(new Date());
        certificationPictures.setUploadUserName(userName);
        if (CommonConstant.ONE.equals(requestSource)) {//司机下单（新生无账号）
            certificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(copyFileType,
                    "",
                    picturePath,
                    null));
        }else{//新生订单已审核
            certificationPictures.setFilePath(picturePath);
        }
        certificationPictures.setSuffix(certificationPictures.getFilePath().substring(certificationPictures.getFilePath().lastIndexOf('.')));
        commonBiz.setBaseEntityAdd(certificationPictures, userName);
        return certificationPictures;
    }
}
