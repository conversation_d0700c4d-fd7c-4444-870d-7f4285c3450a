package com.logistics.management.webapi.api.impl.carriercontact.mapping;


import com.logistics.management.webapi.api.feign.carriercontact.dto.CarrierContactDetailResponseDto;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.tms.api.feign.carriercontact.dto.CarrierContactDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

public class CarrierContactDetailMapping extends MapperMapping<CarrierContactDetailResponseModel, CarrierContactDetailResponseDto> {


    @Override
    public void configure() {
        CarrierContactDetailResponseModel source = getSource();
        CarrierContactDetailResponseDto destination = getDestination();

        if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getType())){
            destination.setCompanyCarrierName(source.getContactName() + " " + source.getContactPhone());
        }
        destination.setTypeLabel(CompanyTypeEnum.getEnum(source.getType()).getValue());


    }
}
