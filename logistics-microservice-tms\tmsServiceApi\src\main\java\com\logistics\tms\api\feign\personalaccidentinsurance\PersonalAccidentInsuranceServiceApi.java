package com.logistics.tms.api.feign.personalaccidentinsurance;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.personalaccidentinsurance.hystrix.PersonalAccidentInsuranceServiceApiHystrix;
import com.logistics.tms.api.feign.personalaccidentinsurance.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/5/30 11:21
 */
@Api(value = "API-PersonalAccidentInsuranceServiceApi-个人意外险配置管理")
@FeignClient(name = "logistics-tms-services", fallback = PersonalAccidentInsuranceServiceApiHystrix.class)
public interface PersonalAccidentInsuranceServiceApi {

    @ApiOperation(value = "获取个人意外险列表")
    @PostMapping(value = "/service/personalAccidentInsurance/searchPersonalAccidentInsuranceList")
    Result<PageInfo<PersonalAccidentInsuranceListResponseModel>> searchPersonalAccidentInsuranceList(@RequestBody PersonalAccidentInsuranceListRequestModel requestModel);

    @ApiOperation(value = "查询个人意外险详情")
    @PostMapping(value = "/service/personalAccidentInsurance/getPersonalAccidentInsuranceDetail")
    Result<PersonalAccidentInsuranceDetailResponseModel> getPersonalAccidentInsuranceDetail(@RequestBody PersonalAccidentInsuranceIdRequestModel requestModel);

    @ApiOperation(value = "新增/修改个人意外险")
    @PostMapping(value = "/service/personalAccidentInsurance/addOrModifyPersonalAccidentInsurance")
    Result<Boolean> addOrModifyPersonalAccidentInsurance(@RequestBody AddOrModifyPersonalAccidentInsuranceRequestModel requestModel);

    @ApiOperation(value = "导出个人意外险")
    @PostMapping(value = "/service/personalAccidentInsurance/exportPersonalAccidentInsurance")
    Result<List<PersonalAccidentInsuranceListResponseModel>> exportPersonalAccidentInsurance(@RequestBody PersonalAccidentInsuranceListRequestModel requestModel);

    @ApiOperation(value = "根据保单号查询保单完整信息（新增个人意外险页面使用）")
    @PostMapping(value = "/service/personalAccidentInsurance/getInsuranceByPolicyNumber")
    Result<List<GetInsuranceByPolicyNumberResponseModel>> getInsuranceByPolicyNumber(@RequestBody GetInsuranceByPolicyNumberRequestModel requestModel);

    @ApiOperation(value = "根据保单号模糊保单号")
    @PostMapping(value = "/service/personalAccidentInsurance/searchInsuranceByPolicyNumber")
    Result<List<SearchInsuranceByPolicyNumberResponseModel>> searchInsuranceByPolicyNumber(@RequestBody SearchInsuranceByPolicyNumberRequestModel requestModel);

    @ApiOperation(value = "根据保单/批单号模糊查询保单号和金额（关联人数少于保单人数且保费大于，新增个人意外险页面使用0）")
    @PostMapping(value = "/service/personalAccidentInsurance/getPolicyNoPremiumByPolicyNo")
    Result<List<GetPolicyNoPremiumByPolicyNoResponseModel>> getPolicyNoPremiumByPolicyNo(@RequestBody SearchInsuranceByPolicyNumberRequestModel requestModel);

    @ApiOperation(value = "导入个人意外险")
    @PostMapping(value = "/service/personalAccidentInsurance/importPersonalAccidentInsurance")
    Result<ImportPersonalAccidentInsuranceResponseModel> importPersonalAccidentInsurance(@RequestBody ImportPersonalAccidentInsuranceRequestModel requestModel);
}
