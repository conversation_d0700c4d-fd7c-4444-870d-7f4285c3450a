package com.logistics.management.webapi.controller.warehouseaddress.mapping;

import com.logistics.management.webapi.base.enums.EnabledEnum;
import com.logistics.management.webapi.client.warehouseaddress.response.WarehouseAddressListResponseModel;
import com.logistics.management.webapi.controller.warehouseaddress.response.WarehouseAddressListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.util.Optional;


public class WarehouseAddressListMapping extends MapperMapping<WarehouseAddressListResponseModel,WarehouseAddressListResponseDto> {


    @Override
    public void configure() {
        WarehouseAddressListResponseModel source = this.getSource();
        WarehouseAddressListResponseDto destination = this.getDestination();
        if(source!=null){
            destination.setAddress(Optional.ofNullable(source.getProvinceName()).orElse("")+
                    Optional.ofNullable(source.getCityName()).orElse("")+
                    Optional.ofNullable(source.getAreaName()).orElse(""));
            destination.setEnabledLabel(EnabledEnum.getEnum(source.getEnabled()).getValue());
        }
    }
}
