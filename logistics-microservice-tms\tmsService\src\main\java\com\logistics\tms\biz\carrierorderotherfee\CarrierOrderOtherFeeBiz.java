package com.logistics.tms.biz.carrierorderotherfee;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.driverpayee.model.ViewLogResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.carrierorder.request.SearchCarrierOrderForParamsRequestModel;
import com.logistics.tms.controller.carrierorder.response.GetValidCarrierOrderResponseModel;
import com.logistics.tms.controller.carrierorder.response.SearchCarrierOrderForParamsResponseModel;
import com.logistics.tms.controller.carrierorderotherfee.request.*;
import com.logistics.tms.controller.carrierorderotherfee.response.*;
import com.logistics.tms.entity.TCarrierOrder;
import com.logistics.tms.entity.TCarrierOrderOtherFee;
import com.logistics.tms.entity.TCarrierOrderOtherFeeItem;
import com.logistics.tms.entity.TCertificationPictures;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CarrierOrderOtherFeeBiz {

    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TOperateLogsMapper tOperateLogsMapper;
    @Autowired
    private TCarrierOrderOtherFeeMapper tCarrierOrderOtherFeeMapper;
    @Autowired
    private TCarrierOrderOtherFeeItemMapper tCarrierOrderOtherFeeItemMapper;
    @Autowired
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Autowired
    private TCertificationPicturesMapper tCertificationPicturesMapper;
    @Autowired
    private CarrierOrderOtherFeeCommonBiz carrierOrderOtherFeeCommonBiz;

    /**
     * 临时费用列表
     * @param requestModel
     * @return
     */
    public PageInfo<SearchOtherFeeListResponseModel> searchList(SearchOtherFeeListRequestModel requestModel) {
        //运单信息
        Map<Long, SearchCarrierOrderForParamsResponseModel> carrierOrderMap = new HashMap<>();
        //后台列表是否有运单相关筛选条件
        boolean ifCarrierOrderParams = false;

        if (CommonConstant.TWO.equals(requestModel.getRequestSource())) {//前台
            //查询登录人所属车主id
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (companyCarrierId == null || companyCarrierId.equals(CommonConstant.LONG_ZERO)) {
                return new PageInfo<>(new ArrayList<>());
            }
            //赋值车主id
            requestModel.setCompanyCarrierId(companyCarrierId);
        }else {//后台
            //判断是否有运单相关筛选条件
            if (StringUtils.isNotBlank(requestModel.getCompanyCarrierName())
                    || StringUtils.isNotBlank(requestModel.getVehicleNo())
                    || StringUtils.isNotBlank(requestModel.getDriver())
                    || StringUtils.isNotBlank(requestModel.getCarrierOrderCode())
                    || requestModel.getProjectLabel() != null
                    || StringUtils.isNotBlank(requestModel.getLoadAddress())
                    || StringUtils.isNotBlank(requestModel.getUnloadAddress())
                    || StringUtils.isNotBlank(requestModel.getCreatedTimeStart())
                    || StringUtils.isNotBlank(requestModel.getCreatedTimeEnd())
                    || requestModel.getDemandOrderEntrustType() != null) {
                ifCarrierOrderParams = true;
            }
        }

        //后台有运单筛选条件才先去查运单（不然会查所有运单，效率太低）或前台
        if (ifCarrierOrderParams || CommonConstant.TWO.equals(requestModel.getRequestSource())) {
            //查询车主的运单信息（包括筛选条件）
            List<SearchCarrierOrderForParamsResponseModel> carrierOrderList = tCarrierOrderMapper.searchCarrierOrderForParams(MapperUtils.mapper(requestModel, SearchCarrierOrderForParamsRequestModel.class));
            if (ListUtils.isEmpty(carrierOrderList)) {
                return new PageInfo<>(new ArrayList<>());
            }

            //转换运单信息
            List<Long> carrierOrderIdList = new ArrayList<>();
            for (SearchCarrierOrderForParamsResponseModel model : carrierOrderList) {
                carrierOrderMap.put(model.getCarrierOrderId(), model);

                carrierOrderIdList.add(model.getCarrierOrderId());
            }
            //赋值运单Id
            requestModel.setCarrierOrderIds(StringUtils.listToString(carrierOrderIdList, ','));
        }

        //开启分页
        requestModel.enablePaging();
        //根据筛选条件分页查询临时费用id
        List<Long> idList = tCarrierOrderOtherFeeMapper.searchListId(MapperUtils.mapper(requestModel,SearchOtherFeeListRequestModel.class));
        PageInfo pageInfo = new PageInfo(idList);
        if (ListUtils.isNotEmpty(idList)){
            //根据id查询临时费用信息
            List<SearchOtherFeeListResponseModel> list = tCarrierOrderOtherFeeMapper.searchList(StringUtils.listToString(idList,','));
            List<SearchOtherFeeListResponseModel> modelList = MapperUtils.mapper(list, SearchOtherFeeListResponseModel.class);

            //后台如果不存在运单筛选条件，则根据运单id查询运单信息
            if (!ifCarrierOrderParams){
                List<Long> carrierOrderIdList = modelList.stream().map(SearchOtherFeeListResponseModel::getCarrierOrderId).distinct().collect(Collectors.toList());
                SearchCarrierOrderForParamsRequestModel searchCarrierOrderForParamsRequestModel = new SearchCarrierOrderForParamsRequestModel();
                searchCarrierOrderForParamsRequestModel.setCarrierOrderIds(StringUtils.listToString(carrierOrderIdList,','));
                List<SearchCarrierOrderForParamsResponseModel> carrierOrderList = tCarrierOrderMapper.searchCarrierOrderForParams(searchCarrierOrderForParamsRequestModel);
                for (SearchCarrierOrderForParamsResponseModel model : carrierOrderList) {
                    carrierOrderMap.put(model.getCarrierOrderId(), model);
                }
            }

            //拼接数据
            SearchCarrierOrderForParamsResponseModel carrierOrderModel;
            for (SearchOtherFeeListResponseModel model : modelList) {
                //拼接运单相关信息
                carrierOrderModel = carrierOrderMap.get(model.getCarrierOrderId());
                if (carrierOrderModel != null){
                    model.setDriverName(carrierOrderModel.getDriverName());
                    model.setDriverMobile(carrierOrderModel.getDriverMobile());
                    model.setVehicleNo(carrierOrderModel.getVehicleNo());
                    model.setLoadCityName(carrierOrderModel.getLoadCityName());
                    model.setUnloadCityName(carrierOrderModel.getUnloadCityName());
                    model.setCompanyCarrierType(carrierOrderModel.getCompanyCarrierType());
                    model.setCompanyCarrierName(carrierOrderModel.getCompanyCarrierName());
                    model.setCompanyCarrierId(carrierOrderModel.getCompanyCarrierId());
                    model.setCarrierContactName(carrierOrderModel.getCarrierContactName());
                    model.setCarrierContactMobile(carrierOrderModel.getCarrierContactMobile());
                    model.setProjectLabel(carrierOrderModel.getProjectLabel());
                    model.setCarrierSettleStatementStatus(carrierOrderModel.getCarrierSettleStatementStatus());
                }
            }
            pageInfo.setList(modelList);
        }
        return pageInfo;
    }

    /**
     * 新增临时费用
     * @param requestModel
     * @return
     */
    @Transactional
    public Boolean addCarrierOrderOtherFee(AddCarrierOrderOtherFeeRequestModel requestModel){
        //判断运单是否存在
        TCarrierOrder dbCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (dbCarrierOrder == null || IfValidEnum.INVALID.getKey().equals(dbCarrierOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //校验是否有新增权限
        carrierOrderOtherFeeCommonBiz.addOtherFeeCheck(dbCarrierOrder, requestModel.getRequestSource());

        List<AddCarrierOrderOtherFeeItemRequestModel> otherFeeList = requestModel.getOtherFeeList();
        //临时费用合计
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (AddCarrierOrderOtherFeeItemRequestModel itemModel : otherFeeList) {
            //如果费用符号为-，则将金额变为负数
            if (CommonConstant.TWO.equals(itemModel.getFeeAmountSymbol())){
                itemModel.setFeeAmount(itemModel.getFeeAmount().negate());
            }
            //计算费用合计
            totalAmount= totalAmount.add(itemModel.getFeeAmount());
        }

        //新增临时费用
        TCarrierOrderOtherFee tCarrierOrderOtherFee = new TCarrierOrderOtherFee();
        tCarrierOrderOtherFee.setCarrierOrderId(requestModel.getCarrierOrderId());
        tCarrierOrderOtherFee.setCarrierOrderCode(dbCarrierOrder.getCarrierOrderCode());
        tCarrierOrderOtherFee.setTotalAmount(totalAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        //运单已签收
        if (CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(dbCarrierOrder.getStatus())){
            //后台-已审核
            if (CommonConstant.ONE.equals(requestModel.getRequestSource())) {
                tCarrierOrderOtherFee.setAuditStatus(CarrierOrderOtherFeeStatusEnum.ALREADY_AUDIT.getKey());
                tCarrierOrderOtherFee.setAuditorName(BaseContextHandler.getUserName());
                tCarrierOrderOtherFee.setAuditTime(new Date());
            }
            //前台、小程序-待审核
            else {
                tCarrierOrderOtherFee.setAuditStatus(CarrierOrderOtherFeeStatusEnum.WAIT_AUDIT.getKey());
            }
        }
        //运单未签收-待提交
        else{
            tCarrierOrderOtherFee.setAuditStatus(CarrierOrderOtherFeeStatusEnum.WAIT_COMMIT.getKey());
        }
        commonBiz.setBaseEntityAdd(tCarrierOrderOtherFee, BaseContextHandler.getUserName());
        tCarrierOrderOtherFeeMapper.insertSelective(tCarrierOrderOtherFee);

        //新增临时费用明细
        addFeeItem(otherFeeList, tCarrierOrderOtherFee.getId(), tCarrierOrderOtherFee.getCarrierOrderCode());

        //记录日志
        tOperateLogsMapper.insertSelective(commonBiz.addOperateLogs(tCarrierOrderOtherFee.getId(), OperateLogsOperateTypeEnum.ADD_TEMPORARY_COST,"临时费用"+tCarrierOrderOtherFee.getTotalAmount()+"元", BaseContextHandler.getUserName()));
        return true;
    }

    //新增临时费用明细
    public void addFeeItem(List<AddCarrierOrderOtherFeeItemRequestModel> otherFeeList, Long carrierOrderOtherFeeId, String carrierOrderCode){
        //新增临时费用明细
        TCarrierOrderOtherFeeItem tCarrierOrderOtherFeeItem;
        TCertificationPictures newPicture;
        List<TCertificationPictures> picturesList= new ArrayList<>();
        Date now = new Date();
        for (AddCarrierOrderOtherFeeItemRequestModel addCarrierOrderOtherFeeItemRequestModel : otherFeeList) {
            //新增临时费用明细
            tCarrierOrderOtherFeeItem = new TCarrierOrderOtherFeeItem();
            tCarrierOrderOtherFeeItem.setCarrierOrderOtherFeeId(carrierOrderOtherFeeId);
            tCarrierOrderOtherFeeItem.setFeeAmount(addCarrierOrderOtherFeeItemRequestModel.getFeeAmount());
            tCarrierOrderOtherFeeItem.setFeeType(addCarrierOrderOtherFeeItemRequestModel.getFeeType());
            tCarrierOrderOtherFeeItem.setFeeSource(addCarrierOrderOtherFeeItemRequestModel.getFeeSource());
            commonBiz.setBaseEntityAdd(tCarrierOrderOtherFeeItem, BaseContextHandler.getUserName());
            tCarrierOrderOtherFeeItemMapper.insertSelective(tCarrierOrderOtherFeeItem);

            //临时费用单据
            List<String> billsPicture = addCarrierOrderOtherFeeItemRequestModel.getBillsPicture();
            for (String pic :billsPicture ){
                newPicture = new TCertificationPictures();
                newPicture.setObjectType(CertificationPicturesFileTypeEnum.CARRIER_ORDER_OTHER_FEE.getObjectType().getObjectType());
                newPicture.setObjectId(tCarrierOrderOtherFeeItem.getId());
                newPicture.setFileType(CertificationPicturesFileTypeEnum.CARRIER_ORDER_OTHER_FEE.getFileType());
                newPicture.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CARRIER_ORDER_OTHER_FEE.getKey(), carrierOrderCode, pic, null));
                newPicture.setUploadUserName(BaseContextHandler.getUserName());
                newPicture.setUploadTime(now);
                newPicture.setFileTypeName(CertificationPicturesFileTypeEnum.CARRIER_ORDER_OTHER_FEE.getFileName());
                newPicture.setFileName(CertificationPicturesFileTypeEnum.CARRIER_ORDER_OTHER_FEE.getFileName());
                newPicture.setSuffix(pic.substring(pic.indexOf('.')));
                commonBiz.setBaseEntityAdd(newPicture, BaseContextHandler.getUserName());
                picturesList.add(newPicture);

            }
        }
        //新增单据
        if (ListUtils.isNotEmpty(picturesList)) {
            tCertificationPicturesMapper.batchInsert(picturesList);
        }
    }

    /**
     * 审核/提交临时费用
     * @param requestModel
     * @return
     */
    @Transactional
    public Boolean commitCarrierOrderOtherFee(CommitCarrierOrderOtherFeeRequestModel requestModel){
        //临时费用
        TCarrierOrderOtherFee tCarrierOrderOtherFee;
        //审核
        if (CommonConstant.TWO.equals(requestModel.getOperateType())) {
            //判断是否有权限
            tCarrierOrderOtherFee = carrierOrderOtherFeeCommonBiz.auditCheck(requestModel.getCarrierOrderOtherFeeId());
        }
        //提交
        else{
            //校验能否提交
            tCarrierOrderOtherFee = carrierOrderOtherFeeCommonBiz.commitCheck(requestModel.getCarrierOrderOtherFeeId(), requestModel.getRequestSource());
        }

        //查询数据库中费用数据和单据信息
        List<TCarrierOrderOtherFeeItem> dbCarrierOrderOtherFeeItemList = tCarrierOrderOtherFeeItemMapper.getByCarrierOrderOtherFeeId(tCarrierOrderOtherFee.getId());
        List<Long> dbItemIdList = new ArrayList<>();
        Map<Long, TCarrierOrderOtherFeeItem> dbItemMap = new HashMap<>();
        for (TCarrierOrderOtherFeeItem otherFeeItem : dbCarrierOrderOtherFeeItemList) {
            dbItemIdList.add(otherFeeItem.getId());
            dbItemMap.put(otherFeeItem.getId(), otherFeeItem);
        }

        //处理入参费用明细
        List<CommitCarrierOrderOtherFeeItemRequestModel> otherFeeList = requestModel.getOtherFeeList();
        //临时费用合计
        BigDecimal totalAmount = BigDecimal.ZERO;
        //入参已存在于数据的费用明细id
        List<Long> existItemIdList = new ArrayList<>();
        //需要更新的费用明细
        List<CommitCarrierOrderOtherFeeItemRequestModel> updateItemList = new ArrayList<>();
        //需求新增的费用明细
        List<CommitCarrierOrderOtherFeeItemRequestModel> addItemList = new ArrayList<>();
        for (CommitCarrierOrderOtherFeeItemRequestModel itemModel : otherFeeList) {
            //如果费用符号为-，则将金额变为负数
            if (CommonConstant.TWO.equals(itemModel.getFeeAmountSymbol())){
                itemModel.setFeeAmount(itemModel.getFeeAmount().negate());
            }
            //计算费用合计
            totalAmount= totalAmount.add(itemModel.getFeeAmount());

            if (dbItemIdList.contains(itemModel.getCarrierOrderOtherFeeItemId())){
                updateItemList.add(itemModel);

                existItemIdList.add(itemModel.getCarrierOrderOtherFeeItemId());
            }else{
                addItemList.add(itemModel);
            }
        }

        //查询已存在的费用明细票据信息
        Map<Long, List<TCertificationPictures>> dbBillsPictureMap = new HashMap<>();
        if (ListUtils.isNotEmpty(existItemIdList)) {
            List<TCertificationPictures> dbCertificationPicturesList = tCertificationPicturesMapper.getImageByIdsAndType(StringUtils.listToString(existItemIdList, ','), CertificationPicturesObjectTypeEnum.T_CARRIER_ORDER_OTHER_FEE.getObjectType());
            dbBillsPictureMap = dbCertificationPicturesList.stream().collect(Collectors.groupingBy(TCertificationPictures::getObjectId));
        }

        //更新临时费用
        TCarrierOrderOtherFee tCarrierOrderOtherFeeAudit = new TCarrierOrderOtherFee();
        tCarrierOrderOtherFeeAudit.setId(requestModel.getCarrierOrderOtherFeeId());
        tCarrierOrderOtherFeeAudit.setTotalAmount(totalAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        //审核
        if (CommonConstant.TWO.equals(requestModel.getOperateType())) {
            tCarrierOrderOtherFeeAudit.setAuditStatus(CarrierOrderOtherFeeStatusEnum.ALREADY_AUDIT.getKey());
            tCarrierOrderOtherFeeAudit.setAuditorName(BaseContextHandler.getUserName());
            tCarrierOrderOtherFeeAudit.setAuditTime(new Date());
            tCarrierOrderOtherFeeAudit.setRemark(requestModel.getRemark());
        }
        //提交
        else {
            //小程序-只有【待提交】状态可以修改，且状态不变
            if (!CommonConstant.THREE.equals(requestModel.getRequestSource())) {
                tCarrierOrderOtherFeeAudit.setAuditStatus(CarrierOrderOtherFeeStatusEnum.WAIT_AUDIT.getKey());
                tCarrierOrderOtherFeeAudit.setRemark("");
            }
        }
        commonBiz.setBaseEntityModify(tCarrierOrderOtherFeeAudit, BaseContextHandler.getUserName());
        tCarrierOrderOtherFeeMapper.updateByPrimaryKeySelective(tCarrierOrderOtherFeeAudit);

        //临时费用明细存在，则编辑
        List<TCarrierOrderOtherFeeItem> upItemList = new ArrayList<>();
        List<TCertificationPictures> upPicturesLIst = new ArrayList<>();
        List<TCertificationPictures> inPicturesLIst = new ArrayList<>();
        if(ListUtils.isNotEmpty(updateItemList)){
            TCarrierOrderOtherFeeItem existCarrierOrderOtherFeeItem;
            TCarrierOrderOtherFeeItem upItem;
            TCertificationPictures upPictures;
            Date now = new Date();
            for (CommitCarrierOrderOtherFeeItemRequestModel model : updateItemList) {
                //修改费用明细
                existCarrierOrderOtherFeeItem = dbItemMap.get(model.getCarrierOrderOtherFeeItemId());
                if (existCarrierOrderOtherFeeItem != null
                        && (!model.getFeeType().equals(existCarrierOrderOtherFeeItem.getFeeType()) || model.getFeeAmount().compareTo(existCarrierOrderOtherFeeItem.getFeeAmount()) != CommonConstant.INTEGER_ZERO)) {
                    upItem = new TCarrierOrderOtherFeeItem();
                    upItem.setId(model.getCarrierOrderOtherFeeItemId());
                    upItem.setFeeType(model.getFeeType());
                    upItem.setFeeAmount(model.getFeeAmount());
                    upItem.setFeeSource(model.getFeeSource());
                    commonBiz.setBaseEntityModify(upItem, BaseContextHandler.getUserName());
                    upItemList.add(upItem);
                }else {
                    upItem = new TCarrierOrderOtherFeeItem();
                    upItem.setId(model.getCarrierOrderOtherFeeItemId());
                    upItem.setFeeSource(model.getFeeSource());
                    commonBiz.setBaseEntityModify(upItem, BaseContextHandler.getUserName());
                    upItemList.add(upItem);
                }

                //修改票据
                List<String> billsPicture = model.getBillsPicture();
                List<TCertificationPictures> tCertificationPictures = dbBillsPictureMap.get(model.getCarrierOrderOtherFeeItemId());
                List<String> paths = tCertificationPictures.stream().map(TCertificationPictures::getFilePath).collect(Collectors.toList());

                // 查出被前端删了的
                tCertificationPictures.forEach(e -> {
                    if (!billsPicture.contains(e.getFilePath())){
                        TCertificationPictures upPO = new TCertificationPictures();
                            upPO.setId(e.getId());
                            upPO.setValid(0);
                            commonBiz.setBaseEntityModify(upPO, BaseContextHandler.getUserName());
                            upPicturesLIst.add(upPO);

                    }
                });

                for (String pic : billsPicture){
                    if (ListUtils.isNotEmpty(paths) != null && !paths.contains(pic)){
                        upPictures = new TCertificationPictures();
                        upPictures.setObjectType(tCertificationPictures.get(0).getObjectType());
                        upPictures.setObjectId(tCertificationPictures.get(0).getObjectId());
                        upPictures.setFileType(CertificationPicturesFileTypeEnum.CARRIER_ORDER_OTHER_FEE.getFileType());
                        upPictures.setFileTypeName(CertificationPicturesFileTypeEnum.CARRIER_ORDER_OTHER_FEE.getFileName());
                        upPictures.setFileName(CertificationPicturesFileTypeEnum.CARRIER_ORDER_OTHER_FEE.getFileName());
                        upPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CARRIER_ORDER_OTHER_FEE.getKey(), tCarrierOrderOtherFee.getCarrierOrderCode(),pic, null));
                        upPictures.setUploadUserName(BaseContextHandler.getUserName());
                        upPictures.setUploadTime(now);
                        upPictures.setSuffix(pic.substring(pic.indexOf('.')));
                        commonBiz.setBaseEntityModify(upPictures, BaseContextHandler.getUserName());
                        inPicturesLIst.add(upPictures);
                    }
                }
            }
        }

        //数据库里存在的明细id，去除入参明细id后，还存在明细id，则删除
        dbItemIdList.removeAll(existItemIdList);
        if (ListUtils.isNotEmpty(dbItemIdList)){
            //删除费用明细
            TCarrierOrderOtherFeeItem delItem;
            for (Long aLong : dbItemIdList) {
                delItem = new TCarrierOrderOtherFeeItem();
                delItem.setId(aLong);
                delItem.setValid(IfValidEnum.INVALID.getKey());
                commonBiz.setBaseEntityModify(delItem, BaseContextHandler.getUserName());
                upItemList.add(delItem);
            }

            //删除票据
            CertificationPicturesFileTypeEnum fileTypeEnum = CertificationPicturesFileTypeEnum.CARRIER_ORDER_OTHER_FEE;
            tCertificationPicturesMapper.delByObjectTypeObjectIds(fileTypeEnum.getObjectType().getObjectType(), StringUtils.listToString(dbItemIdList,','), BaseContextHandler.getUserName());
        }

        //新增费用明细
        if (ListUtils.isNotEmpty(addItemList)){
            //新增临时费用明细
            addFeeItem(MapperUtils.mapper(addItemList, AddCarrierOrderOtherFeeItemRequestModel.class), tCarrierOrderOtherFee.getId(), tCarrierOrderOtherFee.getCarrierOrderCode());
        }

        //更新费用明细
        if (ListUtils.isNotEmpty(upItemList)){
            tCarrierOrderOtherFeeItemMapper.batchUpdateSelective(upItemList);
        }
        //更新费用明细票据
        if (ListUtils.isNotEmpty(upPicturesLIst)){
            tCertificationPicturesMapper.batchUpdate(upPicturesLIst);
        }

        if (ListUtils.isNotEmpty(inPicturesLIst)){
            tCertificationPicturesMapper.batchInsert(inPicturesLIst);
        }

        //记录日志
        //审核
        if (CommonConstant.TWO.equals(requestModel.getOperateType())) {
            tOperateLogsMapper.insertSelective(commonBiz.addOperateLogs(tCarrierOrderOtherFee.getId(), OperateLogsOperateTypeEnum.AUDIT_TEMPORARY_COST, "临时费用" + tCarrierOrderOtherFeeAudit.getTotalAmount() + "元", BaseContextHandler.getUserName()));
        }
        //提交
        else {
            tOperateLogsMapper.insertSelective(commonBiz.addOperateLogs(tCarrierOrderOtherFee.getId(), OperateLogsOperateTypeEnum.COMMIT_TEMPORARY_COST, "临时费用" + tCarrierOrderOtherFeeAudit.getTotalAmount() + "元", BaseContextHandler.getUserName()));
        }
        return true;
    }

    /**
     * 驳回临时费用
     * @param requestModel
     * @return
     */
    @Transactional
    public Boolean rejectOtherFee(RejectCarrierOrderOtherFeeRequestModel requestModel){
        //查看该费用记录是否存在
        TCarrierOrderOtherFee tCarrierOrderOtherFee = tCarrierOrderOtherFeeMapper.selectByPrimaryKey(requestModel.getCarrierOrderOtherFeeId());
        if (tCarrierOrderOtherFee == null || tCarrierOrderOtherFee.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_EXIST);
        }

        //判断该费用是否为待审核
        if (!CarrierOrderOtherFeeStatusEnum.WAIT_AUDIT.getKey().equals(tCarrierOrderOtherFee.getAuditStatus())) {
            throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_AUDIT);
        }

        //驳回
        TCarrierOrderOtherFee carrierOrderOtherFee = new TCarrierOrderOtherFee();
        carrierOrderOtherFee.setId(requestModel.getCarrierOrderOtherFeeId());
        carrierOrderOtherFee.setAuditStatus(CarrierOrderOtherFeeStatusEnum.AUDIT_REJECT.getKey());
        carrierOrderOtherFee.setAuditorName(BaseContextHandler.getUserName());
        carrierOrderOtherFee.setAuditTime(new Date());
        carrierOrderOtherFee.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityModify(carrierOrderOtherFee, BaseContextHandler.getUserName());
        tCarrierOrderOtherFeeMapper.updateByPrimaryKeySelective(carrierOrderOtherFee);

        //记录日志
        tOperateLogsMapper.insertSelective(commonBiz.addOperateLogs(tCarrierOrderOtherFee.getId(), OperateLogsOperateTypeEnum.REJECT_TEMPORARY_COST, requestModel.getRemark(), BaseContextHandler.getUserName()));
        return true;
    }

    /**
     * 临时费用详情
     * @param requestModel
     * @return
     */
    public CarrierOrderOtherFeeDetailResponseModel getCarrierOrderOtherFeeDetail(CarrierOrderOtherFeeDetailRequestModel requestModel){
        //查询临时费用相关信息
        CarrierOrderOtherFeeDetailResponseModel detail = tCarrierOrderOtherFeeMapper.getCarrierOrderOtherFeeDetail(requestModel.getCarrierOrderOtherFeeId());
        if (detail == null){
            throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_EXIST);
        }

        //查询运单信息
        GetValidCarrierOrderResponseModel carrierOrderResponseModel = tCarrierOrderMapper.searchCarrierOrderForById(detail.getCarrierOrderId());
        if (carrierOrderResponseModel == null){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //前台-判断是否是该车主的费用
        if (CommonConstant.TWO.equals(requestModel.getRequestSource())){
            //查询登录人所属车主id
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (!carrierOrderResponseModel.getCompanyCarrierId().equals(companyCarrierId)){
                throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_EXIST);
            }
        }

        //查询图片
        List<CarrierOrderOtherFeeItemDetailResponseModel> otherFeeList = detail.getOtherFeeList();
        if (ListUtils.isNotEmpty(otherFeeList)){
            List<Long> itemIds = otherFeeList.stream().map(CarrierOrderOtherFeeItemDetailResponseModel::getCarrierOrderOtherFeeItemId).collect(Collectors.toList());
            List<TCertificationPictures> dbCertificationPicturesList = tCertificationPicturesMapper.getImageByIdsAndType(StringUtils.listToString(itemIds, ','), CertificationPicturesObjectTypeEnum.T_CARRIER_ORDER_OTHER_FEE.getObjectType());
            for (CarrierOrderOtherFeeItemDetailResponseModel carrierOrderOtherFeeItemDetailResponseModel :otherFeeList){
              for (TCertificationPictures pic : dbCertificationPicturesList){
                  if (carrierOrderOtherFeeItemDetailResponseModel.getCarrierOrderOtherFeeItemId().equals(pic.getObjectId())){
                      List<String> billsPicture = carrierOrderOtherFeeItemDetailResponseModel.getBillsPicture();
                      billsPicture.add(pic.getFilePath());
                      carrierOrderOtherFeeItemDetailResponseModel.setBillsPicture(billsPicture);
                  }
              }
            }
        }
        detail.setOtherFeeList(otherFeeList);
        //将运单信息合并到详情
        MapperUtils.mapper(carrierOrderResponseModel, detail);
        return detail;
    }

    /**
     * 撤销
     * @param requestModel
     * @return
     */
    @Transactional
    public Boolean cancelOtherFee(CarrierOrderOtherFeeDetailRequestModel requestModel) {
        //查看该费用记录是否存在
        TCarrierOrderOtherFee tCarrierOrderOtherFee = tCarrierOrderOtherFeeMapper.selectByPrimaryKey(requestModel.getCarrierOrderOtherFeeId());
        if(tCarrierOrderOtherFee == null || tCarrierOrderOtherFee.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_EXIST);
        }

        //判断状态
        if (CommonConstant.TWO.equals(requestModel.getRequestSource())){//前台
            //查询登录人所属车主id
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (companyCarrierId == null || companyCarrierId.equals(CommonConstant.LONG_ZERO)){
                throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_EXIST);
            }
            //判断运单是否存在
            TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(tCarrierOrderOtherFee.getCarrierOrderId());
            if(tCarrierOrder == null || tCarrierOrder.getValid().equals(IfValidEnum.INVALID.getKey())){
                throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_EXIST);
            }
            //判断费用所属运单的车主是否与登录人一致
            if (!tCarrierOrder.getCompanyCarrierId().equals(companyCarrierId)){
                throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_EXIST);
            }
            //判断该费用是否为待提交、已驳回
            if (!CarrierOrderOtherFeeStatusEnum.WAIT_COMMIT.getKey().equals(tCarrierOrderOtherFee.getAuditStatus())
                    && !CarrierOrderOtherFeeStatusEnum.AUDIT_REJECT.getKey().equals(tCarrierOrderOtherFee.getAuditStatus())) {
                throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_REVOKE_FOR_WEB);
            }
        }else{//后台
            //判断该费用是否为待提交、、待审核已驳回
            if (!CarrierOrderOtherFeeStatusEnum.WAIT_COMMIT.getKey().equals(tCarrierOrderOtherFee.getAuditStatus())
                    && !CarrierOrderOtherFeeStatusEnum.WAIT_AUDIT.getKey().equals(tCarrierOrderOtherFee.getAuditStatus())
                    && !CarrierOrderOtherFeeStatusEnum.AUDIT_REJECT.getKey().equals(tCarrierOrderOtherFee.getAuditStatus())) {
                throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_REVOKE);
            }
        }

        //更新状态
        TCarrierOrderOtherFee carrierOrderOtherFee = new TCarrierOrderOtherFee();
        carrierOrderOtherFee.setId(tCarrierOrderOtherFee.getId());
        carrierOrderOtherFee.setAuditStatus(CarrierOrderOtherFeeStatusEnum.AUDIT_REVOKE.getKey());
        commonBiz.setBaseEntityModify(carrierOrderOtherFee, BaseContextHandler.getUserName());
        tCarrierOrderOtherFeeMapper.updateByPrimaryKeySelective(carrierOrderOtherFee);

        //记录日志
        tOperateLogsMapper.insertSelective(commonBiz.addOperateLogs(tCarrierOrderOtherFee.getId(), OperateLogsOperateTypeEnum.REVOKE_TEMPORARY_COST,null,BaseContextHandler.getUserName()));
        return true;
    }

    /**
     * 查看操作日志
     * @param requestModel
     * @return
     */
    public List<GetOtherFeeRecordResponseModel> getOtherFeeRecord(CarrierOrderOtherFeeDetailRequestModel requestModel) {
        //查看该费用记录是否存在
        TCarrierOrderOtherFee tCarrierOrderOtherFee = tCarrierOrderOtherFeeMapper.selectByPrimaryKey(requestModel.getCarrierOrderOtherFeeId());
        if(tCarrierOrderOtherFee == null || tCarrierOrderOtherFee.getValid().equals(IfValidEnum.INVALID.getKey())){
            return new ArrayList<>();
        }

        //前台操作
        if (CommonConstant.TWO.equals(requestModel.getRequestSource())){
            //查询登录人所属车主id
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (companyCarrierId == null || companyCarrierId.equals(CommonConstant.LONG_ZERO)){
                return new ArrayList<>();
            }
            //判断运单是否存在
            TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(tCarrierOrderOtherFee.getCarrierOrderId());
            if(tCarrierOrder == null || tCarrierOrder.getValid().equals(IfValidEnum.INVALID.getKey())){
                return new ArrayList<>();
            }
            //判断费用所属运单的车主是否与登录人一致
            if (!tCarrierOrder.getCompanyCarrierId().equals(companyCarrierId)){
                return new ArrayList<>();
            }
        }

        //查询操作日志
        List<ViewLogResponseModel> viewLogResponseModels = tOperateLogsMapper.selectLogsByCondition(OperateLogsObjectTypeEnum.TEMPORARY_COST.getKey(), requestModel.getCarrierOrderOtherFeeId(), null);
        return MapperUtils.mapper(viewLogResponseModels, GetOtherFeeRecordResponseModel.class);
    }

    /**
     * 小程序-根据运单id查询【待提交】状态最新一条临时费用详情
     * @param requestModel
     * @return
     */
    public GetOtherFeeByCarrierOrderIdResponseModel getOtherFeeByCarrierOrderId(GetOtherFeeByCarrierOrderIdRequestModel requestModel) {
        GetOtherFeeByCarrierOrderIdResponseModel responseModel = new GetOtherFeeByCarrierOrderIdResponseModel();
        //根据运单id查询【待提交】状态最新一条临时费用
        TCarrierOrderOtherFee dbCarrierOrderOtherFee = tCarrierOrderOtherFeeMapper.getTopWaitCommitByCarrierOrderId(requestModel.getCarrierOrderId());
        if (dbCarrierOrderOtherFee == null){
            return responseModel;
        }

        //查费用明细
        List<GetOtherFeeItemByCarrierOrderIdResponseModel> otherFeeList = tCarrierOrderOtherFeeItemMapper.getItemByCarrierOrderOtherFeeId(dbCarrierOrderOtherFee.getId());
        responseModel.setCarrierOrderOtherFeeId(dbCarrierOrderOtherFee.getId());
        responseModel.setTotalAmount(dbCarrierOrderOtherFee.getTotalAmount());
        if (ListUtils.isNotEmpty(otherFeeList)){
            List<Long> itemIds = otherFeeList.stream().map(GetOtherFeeItemByCarrierOrderIdResponseModel::getCarrierOrderOtherFeeItemId).collect(Collectors.toList());
            List<TCertificationPictures> dbCertificationPicturesList = tCertificationPicturesMapper.getImageByIdsAndType(StringUtils.listToString(itemIds, ','), CertificationPicturesObjectTypeEnum.T_CARRIER_ORDER_OTHER_FEE.getObjectType());
            for (GetOtherFeeItemByCarrierOrderIdResponseModel getOtherFeeItemByCarrierOrderIdResponseModel :otherFeeList){
                for (TCertificationPictures pic : dbCertificationPicturesList){
                    if (getOtherFeeItemByCarrierOrderIdResponseModel.getCarrierOrderOtherFeeItemId().equals(pic.getObjectId())){
                        List<String> billsPicture = getOtherFeeItemByCarrierOrderIdResponseModel.getBillsPicture();
                        billsPicture.add(pic.getFilePath());
                        getOtherFeeItemByCarrierOrderIdResponseModel.setBillsPicture(billsPicture);
                    }
                }
            }
        }
        responseModel.setOtherFeeList(otherFeeList);
        return responseModel;
    }

    /**
     * 回退
     * @param requestModel
     * @return
     */
    @Transactional
    public void rollbackOtherFee(CarrierOrderOtherFeeDetailRequestModel requestModel) {
        //查看该费用记录是否存在
        TCarrierOrderOtherFee tCarrierOrderOtherFee = tCarrierOrderOtherFeeMapper.selectByPrimaryKey(requestModel.getCarrierOrderOtherFeeId());
        if (tCarrierOrderOtherFee == null || tCarrierOrderOtherFee.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_EXIST);
        }

        //判断该费用是否为已审核
        if (!CarrierOrderOtherFeeStatusEnum.ALREADY_AUDIT.getKey().equals(tCarrierOrderOtherFee.getAuditStatus())) {
            throw new BizException(CarrierDataExceptionEnum.ONLY_AUDIT_OPERATED_ERROR);
        }

        //判断运单是否存在
        TCarrierOrder dbCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(tCarrierOrderOtherFee.getCarrierOrderId());
        if (dbCarrierOrder == null || IfValidEnum.INVALID.getKey().equals(dbCarrierOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //判断是否未关联对账单
        if (dbCarrierOrder.getCarrierSettleStatementStatus() > CarrierSettleStatementStatusEnum.NOT_RELATED.getKey()) {
            throw new BizException(CarrierDataExceptionEnum.ALREADY_RELEVANCY_RECONCILIATION);
        }

        //回退
        TCarrierOrderOtherFee carrierOrderOtherFee = new TCarrierOrderOtherFee();
        carrierOrderOtherFee.setId(requestModel.getCarrierOrderOtherFeeId());
        carrierOrderOtherFee.setAuditStatus(CarrierOrderOtherFeeStatusEnum.AUDIT_REJECT.getKey());
        carrierOrderOtherFee.setAuditorName(BaseContextHandler.getUserName());
        carrierOrderOtherFee.setAuditTime(new Date());
        commonBiz.setBaseEntityModify(carrierOrderOtherFee, BaseContextHandler.getUserName());
        tCarrierOrderOtherFeeMapper.updateByPrimaryKeySelective(carrierOrderOtherFee);
    }

}