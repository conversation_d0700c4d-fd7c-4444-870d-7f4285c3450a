package com.logistics.appapi.controller.reserve.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Data
public class ReserveBalanceApplyListItemDto {

	@ApiModelProperty("申请记录id")
	private String applyId = "";

	@ApiModelProperty("状态：-1 已撤销，0 待业务审核，1 待财务审核，2 已驳回，3 待打款，4 已打款")
	private String status = "";

	@ApiModelProperty("状态展示文本")
	private String statusLabel = "";

	@ApiModelProperty("申请日期")
	private String applyDate = "";

	@ApiModelProperty("申请人")
	private String proposer = "";

	@ApiModelProperty("申请金额")
	private String applyAmount = "";

	@ApiModelProperty("批准金额")
	private String approveAmount = "";
}
