package com.logistics.tms.biz.carrierorder.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class CarrierOrderSynchronizeModel {
    @ApiModelProperty(value = "需求类型  ")
    private Integer type;

    private String carrierOrderCode;

    //所属需求单id, 借用的临时字段,不参与json持久化
    @JsonIgnore
    private Long demandOrderId;

    private Integer status;

    private Integer ifInvalid;

    private String vehicleNumber;

    private String driverName;

    private String driverMobilePhone;

    private String identityCardNumber;

    private BigDecimal loadingCount;

    private BigDecimal unloadingCount;

    private Date receiptTime;

    private Date stockOutTime;

    private String userName;

    private String cancelReason;

    private Date expectArrivalTime;

    private List<OrderAmountModel> orderLoadAmount = new ArrayList<>();

    private List<OrderAmountModel> orderUnloadAmount = new ArrayList<>();

    private List<OrderAmountModel> orderSignAmount = new ArrayList<>();

    @ApiModelProperty("物流提货单据（提货）、物流签收回单（卸货）")
    private List<String> billPath=new ArrayList<>();

    @ApiModelProperty("是否纠错 1是 2否")
    private Integer ifSolveError=2;

    @ApiModelProperty(value = "SKU对应数量")
    private List<TypeAndCountModel> typeAndCountModel;

    @ApiModelProperty(value="卸货时间")
    private Date unloadTime;

    @ApiModelProperty(value="提货时间")
    private Date loadTime;


    /**
     * 是否需要同步发送给其他外部公司 0:否 1：是
     */
    private Integer sendOtherOutCompanyFlag = 0;
}
