<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderOtherFeeItemMapper">

    <select id="selectOtherFeeItemByCarrierIds" resultType="com.logistics.tms.controller.carrierorder.response.CarrierOrderOtherFeeItemModel">
        select
        tcoof.carrier_order_id as carrierOrderId,
        tcoofi.fee_type        as feeType,
        tcoofi.fee_amount      as feeAmount
        from t_carrier_order_other_fee tcoof
        left join t_carrier_order_other_fee_item tcoofi on tcoofi.valid = 1 and tcoof.id = tcoofi.carrier_order_other_fee_id
        where tcoof.valid = 1
        and audit_status = 5
        and tcoof.carrier_order_id in (${carrierOrderIds})
    </select>

    <select id="getByCarrierOrderOtherFeeId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_order_other_fee_item
        where valid = 1
        and carrier_order_other_fee_id = #{carrierOrderOtherFeeId, jdbcType=BIGINT}
    </select>

    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TCarrierOrderOtherFeeItem"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_carrier_order_other_fee_item (carrier_order_other_fee_id, fee_amount,
                                                    fee_type, created_by, created_time,
                                                    last_modified_by, last_modified_time, valid
        )
        values
        <foreach collection="recordList" item="item" separator=",">
            (#{item.carrierOrderOtherFeeId,jdbcType=BIGINT}, #{item.feeAmount,jdbcType=DECIMAL},
            #{item.feeType,jdbcType=INTEGER}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdTime,jdbcType=TIMESTAMP},
            #{item.lastModifiedBy,jdbcType=VARCHAR}, #{item.lastModifiedTime,jdbcType=TIMESTAMP}, #{item.valid,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <update id="batchUpdateSelective" parameterType="com.logistics.tms.entity.TCarrierOrderOtherFeeItem" >
        <foreach collection="recordList" item="item" separator=";">
            update t_carrier_order_other_fee_item
            <set >
                <if test="item.carrierOrderOtherFeeId != null" >
                    carrier_order_other_fee_id = #{item.carrierOrderOtherFeeId,jdbcType=BIGINT},
                </if>
                <if test="item.feeAmount != null" >
                    fee_amount = #{item.feeAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.feeType != null" >
                    fee_type = #{item.feeType,jdbcType=INTEGER},
                </if>
                <if test="item.feeSource != null" >
                    fee_source = #{item.feeSource,jdbcType=INTEGER},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <resultMap id="getItemByCarrierOrderOtherFeeId_Map" type="com.logistics.tms.controller.carrierorderotherfee.response.GetOtherFeeItemByCarrierOrderIdResponseModel" >
        <result column="carrier_order_other_fee_item_id" property="carrierOrderOtherFeeItemId" jdbcType="BIGINT"/>
        <result column="fee_type" property="feeType" jdbcType="INTEGER"/>
        <result column="fee_amount" property="feeAmount" jdbcType="DECIMAL"/>
    </resultMap>
    <select id="getItemByCarrierOrderOtherFeeId" resultMap="getItemByCarrierOrderOtherFeeId_Map">
        SELECT
        tcoofi.id AS carrier_order_other_fee_item_id,
        tcoofi.fee_type,
        tcoofi.fee_amount
        FROM t_carrier_order_other_fee_item tcoofi
        WHERE tcoofi.valid = 1
        AND tcoofi.carrier_order_other_fee_id = #{carrierOrderOtherFeeId,jdbcType=BIGINT}
    </select>
</mapper>