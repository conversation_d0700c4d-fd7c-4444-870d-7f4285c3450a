package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新生运单发货、收货信息
 *
 * <AUTHOR>
 * @date 2022/8/17 13:23
 */
@ApiModel("运单基本信息")
@Data
public class CarrierOrderDetailBasicInfoForYeloLifeDto {

    @ApiModelProperty("发货地址")
    private String loadAddress = "";

    @ApiModelProperty("发货人")
    private String consignor = "";

    @ApiModelProperty("收货地址")
    private String unloadAddress = "";

    @ApiModelProperty("收货人")
    private String receiver = "";
}
