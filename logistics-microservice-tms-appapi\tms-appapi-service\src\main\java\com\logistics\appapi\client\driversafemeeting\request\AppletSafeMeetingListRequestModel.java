package com.logistics.appapi.client.driversafemeeting.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/11/8 10:19
 */
@Data
public class AppletSafeMeetingListRequestModel extends AbstractPageForm<AppletSafeMeetingListRequestModel> {
    @ApiModelProperty("学习状态：空 全部，0未学习，1已学习")
    private Integer status;
}
