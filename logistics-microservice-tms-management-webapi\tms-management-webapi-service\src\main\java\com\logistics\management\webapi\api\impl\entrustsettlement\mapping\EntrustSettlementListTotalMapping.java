package com.logistics.management.webapi.api.impl.entrustsettlement.mapping;

import com.logistics.management.webapi.api.feign.entrustsettlement.dto.EntrustSettlementListResponseDto;
import com.logistics.tms.api.feign.entrustsettlement.model.EntrustSettlementListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2019/10/11 19:52
 */
public class EntrustSettlementListTotalMapping extends MapperMapping<EntrustSettlementListResponseModel,EntrustSettlementListResponseDto> {
    @Override
    public void configure() {
        EntrustSettlementListResponseModel source = getSource();
        EntrustSettlementListResponseDto destination = getDestination();
        if (source != null){
            destination.setTotalPackageSettlementAmount(source.getTotalPackageSettlementAmount().stripTrailingZeros().toPlainString());
            destination.setTotalWeightSettlementAmount(source.getTotalWeightSettlementAmount().stripTrailingZeros().toPlainString());
        }
    }
}
