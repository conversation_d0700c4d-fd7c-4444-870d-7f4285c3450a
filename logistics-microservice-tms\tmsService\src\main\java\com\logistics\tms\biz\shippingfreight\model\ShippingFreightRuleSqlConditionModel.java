package com.logistics.tms.biz.shippingfreight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ShippingFreightRuleSqlConditionModel {

    private List<Long> shippingFreightIds;
    private List<Long> ids;


    /**
     * 发货地
     */
    private String loadAddress = "";


    /**
     * 收货地
     */
    private String unloadAddress = "";


    /**
     * 是否启用,1启用,0禁用
     */
    private Integer enabled;

}
