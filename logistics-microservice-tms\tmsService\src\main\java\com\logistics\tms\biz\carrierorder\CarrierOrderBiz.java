package com.logistics.tms.biz.carrierorder;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.constant.ConfigKeyConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.base.utils.LocalStringUtil;
import com.logistics.tms.base.utils.RegExpValidatorUtil;
import com.logistics.tms.base.utils.StripTrailingZerosUtils;
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz;
import com.logistics.tms.biz.carrierorder.model.*;
import com.logistics.tms.biz.carrierorderloadcode.model.CarrierOrderLoadCodeListSqlConditionModel;
import com.logistics.tms.biz.carrierorderticketsaudit.CarrierOrderTicketsAuditBiz;
import com.logistics.tms.biz.carrierorderticketsaudit.model.CreateOrResetReceiptTicketAuditBoModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.demandorder.DemandOrderBiz;
import com.logistics.tms.biz.extvehiclesettlement.ExtVehicleSettlementBiz;
import com.logistics.tms.biz.reservationorder.ReservationOrderCommonBiz;
import com.logistics.tms.biz.shippingorder.ShippingOrderCommonBiz;
import com.logistics.tms.biz.staff.model.TStaffBasicModel;
import com.logistics.tms.biz.staffvehiclerelation.StaffVehicleBiz;
import com.logistics.tms.biz.staffvehiclerelation.model.TVehicleBasicModel;
import com.logistics.tms.biz.sysconfig.SysConfigBiz;
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel;
import com.logistics.tms.biz.workgroup.sendmsg.model.WorkGroupEventModel;
import com.logistics.tms.biz.workgroup.sendmsg.model.WorkGroupPushBoModel;
import com.logistics.tms.biz.workordercenter.WorkOrderBiz;
import com.logistics.tms.client.BasicDataClient;
//import com.logistics.tms.client.CarrierClient;
import com.logistics.tms.client.feign.yelolife.basicdata.YeloLifeBasicDataServiceApi;
import com.logistics.tms.controller.carrierorder.request.*;
import com.logistics.tms.controller.carrierorder.response.*;
import com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.tms.controller.demandorder.request.UpdateDemandOrderStatusByIdsRequestModel;
import com.logistics.tms.controller.demandorder.request.WebDemandOrderDispatchVehicleRequestModel;
import com.logistics.tms.controller.demandorder.response.CancelCarrierOrderOrderRelModel;
import com.logistics.tms.controller.demandorder.response.GetDemandOrderInfoByIdsModel;
import com.logistics.tms.controller.demandorder.response.LogisticsDemandStateSynchronizeModel;
import com.logistics.tms.controller.dispatch.response.DriverIdVehicleIdRelationIdModel;
import com.logistics.tms.controller.staffvehiclerelation.request.StaffAndVehicleSearchRequestModel;
import com.logistics.tms.controller.staffvehiclerelation.response.StaffAndVehicleSearchResponseModel;
import com.logistics.tms.controller.vehicleassetmanagement.response.FuzzyQueryVehicleInfoResponseModel;
import com.logistics.tms.controller.vehicleassetmanagement.response.VehicleAssetManagementListResponseModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.logistics.tms.rabbitmq.publisher.model.*;
import com.yelo.basicdata.api.feign.file.model.FileUploadRequestModel;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.redisdistributedlock.DistributedLock;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("carrierOrderBiz")
public class CarrierOrderBiz {


    @Autowired
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Autowired
    private TCarrierOrderVehicleHistoryMapper tCarrierOrderVehicleHistoryMapper;
    @Autowired
    private TCarrierOrderTicketsMapper tCarrierOrderTicketsMapper;
    @Autowired
    private TCarrierOrderGoodsMapper tCarrierOrderGoodsMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TCarrierOrderEventsMapper tCarrierOrderEventsMapper;
    @Autowired
    private TCarrierOrderOperateLogsMapper carrierOrderOperateLogsMapper;
    @Autowired
    private CarrierOrderCommonBiz carrierOrderCommonBiz;
    @Autowired
    private TDispatchOrderMapper dispatchOrderMapper;
    @Autowired
    private TCarrierOrderOrderRelMapper tCarrierOrderOrderRelMapper;
    @Autowired
    private TCarrierOrderWxMapper tCarrierOrderWxMapper;
    @Autowired
    private TVehicleGpsMapper tVehicleGpsMapper;
    @Autowired
    private TOperateLogsMapper operateLogsMapper;
    @Autowired
    private DemandOrderBiz demandOrderBiz;
    @Autowired
    private TDemandOrderMapper tDemandOrderMapper;
    @Autowired
    private TCarrierOrderVehicleHistoryMapper carrierOrderVehicleHistoryMapper;
    @Autowired
    private StaffVehicleBiz staffVehicleBiz;
    @Autowired
    private TVehicleBasicMapper tVehicleBasicMapper;
    @Autowired
    private TStaffVehicleRelationMapper tStaffVehicleRelationMapper;
    @Autowired
    private TCompanyEntrustMapper tCompanyEntrustMapper;
    @Autowired
    private ExtVehicleSettlementBiz extVehicleSettlementBiz;
    @Autowired
    private TCarrierVehicleRelationMapper tCarrierVehicleRelationMapper;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private BasicDataClient basicDataClient;
    @Autowired
    private TPaymentMapper tPaymentMapper;
    @Autowired
    private TReceivementMapper tReceivementMapper;
    @Autowired
    private RabbitMqPublishBiz rabbitMqPublishBiz;
    @Autowired
    private TCarrierDriverRelationMapper tCarrierDriverRelationMapper;
    @Autowired
    private TStaffBasicMapper tStaffBasicMapper;
//    @Autowired
//    private CarrierClient carrierClient;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private CarrierOrderTicketsAuditBiz carrierOrderTicketsAuditBiz;
    @Autowired
    private WorkOrderBiz workOrderBiz;
    @Resource
    private SysConfigBiz sysConfigBiz;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private ShippingOrderCommonBiz shippingOrderCommonBiz;
    @Resource
    private ReservationOrderCommonBiz reservationOrderCommonBiz;
    @Resource
    private TCarrierOrderGoodsCodeMapper tCarrierOrderGoodsCodeMapper;
    @Resource
    private YeloLifeBasicDataServiceApi yeloLifeBasicDataServiceApi;
    @Resource
    private TCarrierOrderAddressMapper tCarrierOrderAddressMapper;

    @Resource
    private TCarrierOrderLoadCodeMapper tCarrierOrderLoadCodeMapper;




    /**
     * 后台运单列表
     */
    public PageInfo searchCarrierOrderListManagement(SearchCarrierOrderListRequestModel requestModel) {
        //查询条件含有司机车辆信息
        Map<Long, TCarrierOrderVehicleHistory> carrierOrderVehicleHistoryMap = new HashMap<>();
        if (StringUtils.isBlank(requestModel.getCarrierOrderIds())
                && (StringUtils.isNotBlank(requestModel.getVehicleNo())
                || StringUtils.isNotBlank(requestModel.getDriver())
                || StringUtils.isNotBlank(requestModel.getExpectArrivalTimeFrom())
                || StringUtils.isNotBlank(requestModel.getExpectArrivalTimeTo()))){
            //查询地址信息
            SearchCarrierOrderVehicleHistoryModel vehicleHistoryModel = MapperUtils.mapper(requestModel, SearchCarrierOrderVehicleHistoryModel.class);
            List<TCarrierOrderVehicleHistory> vehicleHistoryList = tCarrierOrderVehicleHistoryMapper.searchListByCarrierOrderParams(vehicleHistoryModel);
            //不存在，直接返回
            if (ListUtils.isEmpty(vehicleHistoryList)) {
                return new PageInfo<>(new ArrayList<>());
            }
            //存在，数据聚合处理
            List<Long> vehicleCarrierOrderIdList = new ArrayList<>();
            for (TCarrierOrderVehicleHistory vehicleHistory : vehicleHistoryList) {
                carrierOrderVehicleHistoryMap.put(vehicleHistory.getCarrierOrderId(), vehicleHistory);
                if (!vehicleCarrierOrderIdList.contains(vehicleHistory.getCarrierOrderId())) {
                    vehicleCarrierOrderIdList.add(vehicleHistory.getCarrierOrderId());
                }
            }
            requestModel.setCarrierOrderIdList(vehicleCarrierOrderIdList);
        }

        //分页查询获取运单Id
        requestModel.enablePaging();
        List<Long> carrierOrderIdList = tCarrierOrderMapper.searchCarrierOrderIdsForManagement(requestModel);
        PageInfo carrierOrderList = new PageInfo(carrierOrderIdList);

        if (ListUtils.isNotEmpty(carrierOrderIdList)) {
            String carrierOrderIds = StringUtils.listToString(carrierOrderIdList, ',');
            //根据运单id查询运单信息
            List<SearchCarrierOrderListResponseModel> searchCarrierOrderListResponseModels = tCarrierOrderMapper.searchCarrierOrderForManagement(carrierOrderIds);

            List<Long> companyCarrierIdList = new ArrayList<>();
            for (SearchCarrierOrderListResponseModel responseModel : searchCarrierOrderListResponseModels) {
                if (!companyCarrierIdList.contains(responseModel.getCompanyCarrierId())){
                    companyCarrierIdList.add(responseModel.getCompanyCarrierId());
                }
            }

            //根据运单id查询票据信息
            List<GetTicketsAmountByCarrierOrderIdsModel> ticketsAmountList=tCarrierOrderTicketsMapper.getTicketsAmountByCarrierOrderIds(carrierOrderIds);
            Map<Long, Integer> carrierOrderTicketsAmountMap = ticketsAmountList.stream().collect(
                    Collectors.toMap(GetTicketsAmountByCarrierOrderIdsModel::getCarrierOrderId, GetTicketsAmountByCarrierOrderIdsModel::getAmount));

            //不存在车辆相关筛选条件，则根据运单id查询运单司机车辆信息
            if (com.yelo.tools.utils.MapUtils.isEmpty(carrierOrderVehicleHistoryMap)) {
                List<TCarrierOrderVehicleHistory> carrierOrderVehicleHistoryList = tCarrierOrderVehicleHistoryMapper.getValidByCarrierOrderIds(carrierOrderIds);
                carrierOrderVehicleHistoryList.forEach(item -> carrierOrderVehicleHistoryMap.put(item.getCarrierOrderId(), item));
            }

            //根据运单id查询车主结算信息
            List<TPayment> tPaymentList = tPaymentMapper.getByCarrierOrderIds(carrierOrderIds);
            Map<Long, TPayment> paymentMap = new HashMap<>();
            tPaymentList.forEach(item -> paymentMap.put(item.getCarrierOrderId(), item));

            //拼接数据
            TCarrierOrderVehicleHistory tCarrierOrderVehicleHistory;
            TPayment tPayment;
            for(SearchCarrierOrderListResponseModel responseModel:searchCarrierOrderListResponseModels){
                //票据数量
                responseModel.setCarrierOrderTicketsAmount(Optional.ofNullable(carrierOrderTicketsAmountMap.get(responseModel.getCarrierOrderId())).orElse(CommonConstant.INTEGER_ZERO));

                //车辆司机信息
                tCarrierOrderVehicleHistory = carrierOrderVehicleHistoryMap.get(responseModel.getCarrierOrderId());
                if (tCarrierOrderVehicleHistory != null){
                    responseModel.setVehicleNo(tCarrierOrderVehicleHistory.getVehicleNo());
                    responseModel.setDriverName(tCarrierOrderVehicleHistory.getDriverName());
                    responseModel.setDriverMobile(tCarrierOrderVehicleHistory.getDriverMobile());
                    responseModel.setExpectArrivalTime(tCarrierOrderVehicleHistory.getExpectArrivalTime());
                }

                //结算信息
                tPayment = paymentMap.get(responseModel.getCarrierOrderId());
                if (tPayment != null){
                    responseModel.setCarrierPaymentPriceType(tPayment.getPriceType());
                    responseModel.setCarrierSettlementAmount(tPayment.getSettlementAmount());
                    responseModel.setCarrierSettlementCostTotal(tPayment.getSettlementCostTotal());
                }
            }
            carrierOrderList.setList(searchCarrierOrderListResponseModels);
        }
        return carrierOrderList;
    }

    /**
     * 待审核车辆数
     * @param requestModel
     * @return
     */
    public WaitAuditVehicleInfoResponseModel waitAuditVehicleCountInfo(WaitAuditVehicleInfoRequestModel requestModel) {
        //如果下单开始时间跟下单结束时间为空的话，默认为当前-90
        if (StringUtils.isBlank(requestModel.getDemandCreatedTimeFrom()) || StringUtils.isBlank(requestModel.getDemandCreatedTimeTo())) {
            requestModel.setDemandCreatedTimeTo(DateUtils.dateToString(new Date(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            Calendar now = Calendar.getInstance();
            now.add(Calendar.DAY_OF_MONTH, -90);
            requestModel.setDemandCreatedTimeFrom(DateUtils.dateToString(now.getTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        WaitAuditVehicleInfoResponseModel responseModel = new WaitAuditVehicleInfoResponseModel();
        responseModel.setWaitAuditVehicleCount(tCarrierOrderMapper.getWaitAuditVehicleCount(requestModel));
        return responseModel;
    }


    /**
     * 运单详情
     * @param requestModel
     * @return
     */
    public CarrierOrderDetailResponseModel getCarrierOrderDetailById(CarrierOrderDetailRequestModel requestModel) {
        //查询运单详情
        CarrierOrderDetailResponseModel carrierOrderDetailResponseModel = tCarrierOrderMapper.getCarrierOrderDetailForManagementById(requestModel.getCarrierOrderId());
        if (carrierOrderDetailResponseModel == null) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //查询车主结算费用
        TPayment tPayment = tPaymentMapper.getByCarrierOrderId(carrierOrderDetailResponseModel.getCarrierOrderId());
        if (tPayment != null){
            carrierOrderDetailResponseModel.getCarrierOrderDetailFreightFeeInfo().setCarrierPriceType(tPayment.getPriceType());
            carrierOrderDetailResponseModel.getCarrierOrderDetailFreightFeeInfo().setCarrierSettlementCostTotal(tPayment.getSettlementCostTotal());
        }
        //查询货主结算费用
        TReceivement tReceivement = tReceivementMapper.getByCarrierOrderId(carrierOrderDetailResponseModel.getCarrierOrderId());
        if (tReceivement != null){
            carrierOrderDetailResponseModel.getCarrierOrderDetailFreightFeeInfo().setEntrustPriceType(tReceivement.getPriceType());
            carrierOrderDetailResponseModel.getCarrierOrderDetailFreightFeeInfo().setEntrustSettlementCostTotal(tReceivement.getSettlementCostTotal());
        }
        //查询车主信息
        TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(carrierOrderDetailResponseModel.getCompanyCarrierId());
        if (tCompanyCarrier != null && tCompanyCarrier.getValid().equals(IfValidEnum.VALID.getKey())){
            carrierOrderDetailResponseModel.setIfQiya(tCompanyCarrier.getLevel());
        }

        //车辆司机排序
        carrierOrderDetailResponseModel.getCarrierOrderDetailVehicleDriverInfo().sort((o1, o2) -> o2.getCreatedTime().compareTo(o1.getCreatedTime()));
        return carrierOrderDetailResponseModel;
    }

    /**
     * 票据信息列表
     *
     * @param carrierOrderId 运单id
     * @param imageType      图片类型
     * @return 票据信息
     */
    public List<GetTicketsResponseModel> getTicketsByCarrierOrderId(Long carrierOrderId, Integer imageType) {
        return tCarrierOrderTicketsMapper.getTicketsByCarrierOrderId(carrierOrderId, imageType);
    }

    /**
     * 上传票据
     * @param requestModel
     */
    @Transactional
    public void uploadTickets(UploadTicketsRequestModel requestModel) {
        if (requestModel == null || ListUtils.isEmpty(requestModel.getTmpUrl())) {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }
        TCarrierOrder order = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (order == null || IfValidEnum.INVALID.getKey().equals(order.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //运单取消不能操作
        if (order.getIfCancel().equals(CommonConstant.INTEGER_ONE)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL);
        }
        //运单放空不能操作
        if (order.getIfEmpty().equals(CommonConstant.INTEGER_ONE)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EMPTY);
        }

        //运单已关联对账不可修改
        if (order.getCarrierSettleStatementStatus() > CarrierSettleStatementStatusEnum.NOT_RELATED.getKey()) {
            throw new BizException(CarrierDataExceptionEnum.ALREADY_RELEVANCY_RECONCILIATION);
        }

        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(order.getId().toString());
        if (!workOrderMap.isEmpty()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }

        // 回单审核中不允许编辑
        if (CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey().equals(requestModel.getTicketType())) {
            carrierOrderTicketsAuditBiz.updateReceiptTicketCheck(order.getDemandOrderSource(), order.getId());
        }

        //车辆待审核不能操作
        DriverIdVehicleIdRelationIdModel model = new DriverIdVehicleIdRelationIdModel();
        model.setCarrierOrderId(requestModel.getCarrierOrderId());
        TCarrierOrderVehicleHistory history = tCarrierOrderVehicleHistoryMapper.getTopByCarrierOrderId(model);
        if (history == null) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        if (history.getAuditStatus().equals(AuditStatusEnum.WAIT_AUDIT.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_WAIT_AUDIT);
        }
        //运单票据的数量不能超过6张
        List<GetTicketsResponseModel> existTicketsCount = tCarrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(requestModel.getCarrierOrderId(), requestModel.getTicketType());
        if (requestModel.getTmpUrl().size() + existTicketsCount.size() > CommonConstant.INTEGER_SIX) {
            throw new BizException(CarrierDataExceptionEnum.PICTURES_OVER_COUNT);
        }

        CarrierOrderTicketsTypeEnum ticketType = CarrierOrderTicketsTypeEnum.getEnum(requestModel.getTicketType());

        //上传票据
        CopyFileTypeEnum copyType = getCopyFileTypeEnum(requestModel.getTicketType());
        List<TCarrierOrderTickets> list = new ArrayList<>();

        //同步给云盘的票据
        List<CarrierOrderBill> carrierOrderBillList=new ArrayList<>();
        CarrierOrderBill carrierOrderBill;

        for (String tmpUrl : requestModel.getTmpUrl()) {
            TCarrierOrderTickets tmp = new TCarrierOrderTickets();
            tmp.setCarrierOrderId(requestModel.getCarrierOrderId());
            tmp.setImageName(ticketType.getValue());
            tmp.setImagePath(commonBiz.copyFileToDirectoryOfType(copyType.getKey(), order.getCarrierOrderCode(), tmpUrl, null));
            tmp.setImageType(requestModel.getTicketType());
            tmp.setUploadTime(new Date());
            tmp.setUploadUserName(BaseContextHandler.getUserName());
            commonBiz.setBaseEntityAdd(tmp, BaseContextHandler.getUserName());
            list.add(tmp);

            //上传回收类型出库单，同步云盘
            if(EntrustTypeEnum.RECYCLE_IN.getKey().equals(order.getDemandOrderEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(order.getDemandOrderEntrustType())){//回收
                if (CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getKey().equals(ticketType.getKey())) {//出库单
                    carrierOrderBill = new CarrierOrderBill();
                    carrierOrderBill.setBillPath(tmp.getImagePath());
                    carrierOrderBill.setType(CommonConstant.INTEGER_TWO);
                    carrierOrderBillList.add(carrierOrderBill);
                }
            }
            //发货、回收入库、回收出库、供应商直配、退货仓库配送，签收单同步云盘
            if (CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey().equals(ticketType.getKey())) {//签收单
                List<Integer> entrustTypeListModel = Arrays.asList(EntrustTypeEnum.DELIVER.getKey(), EntrustTypeEnum.RECYCLE_IN.getKey(),
                        EntrustTypeEnum.RECYCLE_OUT.getKey(), EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey(), EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey());
                if (entrustTypeListModel.contains(order.getDemandOrderEntrustType())) {
                    carrierOrderBill = new CarrierOrderBill();
                    carrierOrderBill.setBillPath(tmp.getImagePath());
                    carrierOrderBill.setType(CommonConstant.INTEGER_FOUR);
                    carrierOrderBillList.add(carrierOrderBill);
                }
            }
        }
        tCarrierOrderTicketsMapper.batchInsertTickets(list);

        // 创建回单审核
        List<String> ticketsPathList = list
                .stream()
                .map(TCarrierOrderTickets::getImagePath)
                .collect(Collectors.toList());
        carrierOrderTicketsAuditBiz.createOrResetReceiptTicketAudit(new CreateOrResetReceiptTicketAuditBoModel()
                .setSource(order.getDemandOrderSource())
                .setTicketType(ticketType.getKey())
                .setCarrierOrderId(order.getId())
                .setCarrierOrderCode(order.getCarrierOrderCode())
                .setCarrierOrderStatus(order.getStatus())
                .setEntrustType(order.getDemandOrderEntrustType())
                .setTicketsPathList(ticketsPathList));

        //上传回收类型出库单或发货类型签收单，同步云盘
        if (ListUtils.isNotEmpty(carrierOrderBillList)) {
            UpdateCarrierOrderBillModel updateCarrierOrderBillModel = new UpdateCarrierOrderBillModel();
            updateCarrierOrderBillModel.setCarrierOrderCode(order.getCarrierOrderCode());
            updateCarrierOrderBillModel.setOperation(BaseContextHandler.getUserName());
            updateCarrierOrderBillModel.setType(CommonConstant.INTEGER_ONE);//新增
            updateCarrierOrderBillModel.setCarrierOrderBillList(carrierOrderBillList);
            rabbitMqPublishBiz.syncCarrierOrderOutStockTickets(Arrays.asList(updateCarrierOrderBillModel));
        }
    }

    /**
     * 前台编辑票据
     *
     * @param requestModel 票据信息
     */
    @Transactional
    public void uploadTicketsForWeb(UploadTicketsForWebRequestModel requestModel) {
        if (ListUtils.isEmpty(requestModel.getImageInfoList())) {
            throw new BizException(CarrierDataExceptionEnum.CERTIFICATION_PICTURES_NOT_EXIST);
        }
        //查询运单信息
        TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (tCarrierOrder == null || IfValidEnum.INVALID.getKey().equals(tCarrierOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //运单取消不能操作
        if (tCarrierOrder.getIfCancel().equals(CommonConstant.INTEGER_ONE)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL);
        }
        //运单放空不能操作
        if (tCarrierOrder.getIfEmpty().equals(CommonConstant.INTEGER_ONE)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EMPTY);
        }

        //未卸货不能编辑回单
        if (tCarrierOrder.getStatus() < CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_UPDATE_TICKET_WEB_ERROR);
        }
        //运单已关联对账不可修改
        if (tCarrierOrder.getCarrierSettleStatementStatus() > CarrierSettleStatementStatusEnum.NOT_RELATED.getKey()) {
            throw new BizException(CarrierDataExceptionEnum.ALREADY_RELEVANCY_RECONCILIATION);
        }

        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(tCarrierOrder.getId().toString());
        if (!workOrderMap.isEmpty()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }

        //回单审核中不允许编辑
        carrierOrderTicketsAuditBiz.updateReceiptTicketCheck(tCarrierOrder.getDemandOrderSource(), tCarrierOrder.getId());

        Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        //判断运单是不是当前车主的
        if (loginUserCompanyCarrierId == null ||
                CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId) ||
                !tCarrierOrder.getCompanyCarrierId().equals(loginUserCompanyCarrierId)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //车辆待审核不能操作
        DriverIdVehicleIdRelationIdModel model = new DriverIdVehicleIdRelationIdModel();
        model.setCarrierOrderId(requestModel.getCarrierOrderId());
        TCarrierOrderVehicleHistory history = tCarrierOrderVehicleHistoryMapper.getTopByCarrierOrderId(model);
        if (history == null) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        if (history.getAuditStatus().equals(AuditStatusEnum.WAIT_AUDIT.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_WAIT_AUDIT);
        }

        //上传票据
        List<TCarrierOrderTickets> addTicketLis = new ArrayList<>();//新增票据列表
        List<TCarrierOrderTickets> delTicketLis = new ArrayList<>();//删除票据列表
        //同步给云盘的票据
        List<CarrierOrderBill> carrierOrderAddBillList = new ArrayList<>();//新增
        List<CarrierOrderBill> carrierOrderDelBillList = new ArrayList<>();//删除
        //查询存在的票据
        List<GetTicketsResponseModel> ticketsList = tCarrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(requestModel.getCarrierOrderId(), requestModel.getImageType());
        if (ListUtils.isNotEmpty(ticketsList)) {
            /*存在票据信息*/

            //入参票据Map
            Map<Long, TicketsForWebModel> requestTicketsMap = new HashMap<>();
            List<TicketsForWebModel> requestAddTicketList = new ArrayList<>();
            for (TicketsForWebModel requestTicketModel : requestModel.getImageInfoList()) {
                requestTicketsMap.put(requestTicketModel.getImageId(), requestTicketModel);
                if (requestTicketModel.getImageId() == null) {
                    requestAddTicketList.add(requestTicketModel);
                }
            }
            //新增的票据
            for (TicketsForWebModel ticketsForWebModel : requestAddTicketList) {
                addTicketLis.add(addTicket(tCarrierOrder, requestModel.getImageType(), carrierOrderAddBillList, ticketsForWebModel.getImagePath()));
            }

            //运单票据类型
            CarrierOrderTicketsTypeEnum ticketType = CarrierOrderTicketsTypeEnum.getEnum(requestModel.getImageType());
            //删掉的票据
            for (GetTicketsResponseModel dbTicketModel : ticketsList) {
                TicketsForWebModel ticketsForWebModel = requestTicketsMap.get(dbTicketModel.getImageId());
                if (ticketsForWebModel == null) {
                    //删除的票据
                    TCarrierOrderTickets delTicket = new TCarrierOrderTickets();
                    delTicket.setId(dbTicketModel.getImageId());
                    delTicket.setValid(IfValidEnum.INVALID.getKey());
                    delTicket.setUploadUserName(BaseContextHandler.getUserName());
                    commonBiz.setBaseEntityModify(delTicket, BaseContextHandler.getUserName());
                    delTicketLis.add(delTicket);

                    //上传回收类型出库单，同步云盘
                    if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(tCarrierOrder.getDemandOrderEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())) {//回收
                        if (CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getKey().equals(ticketType.getKey())) {//出库单
                            CarrierOrderBill carrierOrderBill = new CarrierOrderBill();
                            carrierOrderBill.setBillPath(dbTicketModel.getImagePath());
                            carrierOrderBill.setType(CommonConstant.INTEGER_TWO);
                            carrierOrderDelBillList.add(carrierOrderBill);
                        }
                    }
                    //发货、回收入库、回收出库、供应商直配、退货仓库配送，签收单同步云盘
                    if (CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey().equals(ticketType.getKey())) {//签收单
                        List<Integer> entrustTypeListModel = Arrays.asList(EntrustTypeEnum.DELIVER.getKey(), EntrustTypeEnum.RECYCLE_IN.getKey(),
                                EntrustTypeEnum.RECYCLE_OUT.getKey(), EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey(), EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey());
                        if (entrustTypeListModel.contains(tCarrierOrder.getDemandOrderEntrustType())) {
                            CarrierOrderBill carrierOrderBill = new CarrierOrderBill();
                            carrierOrderBill.setBillPath(dbTicketModel.getImagePath());
                            carrierOrderBill.setType(CommonConstant.INTEGER_FOUR);
                            carrierOrderDelBillList.add(carrierOrderBill);
                        }
                    }
                }
            }
        } else {
            /*不存在票据信息*/

            //新增的票据
            for (TicketsForWebModel ticketsForWebModel : requestModel.getImageInfoList()) {
                addTicketLis.add(addTicket(tCarrierOrder, requestModel.getImageType(), carrierOrderAddBillList, ticketsForWebModel.getImagePath()));
            }
        }


        if (ListUtils.isNotEmpty(addTicketLis)) {
            tCarrierOrderTicketsMapper.batchInsertTickets(addTicketLis);
        }
        if (ListUtils.isNotEmpty(delTicketLis)) {
            tCarrierOrderTicketsMapper.batchUpdate(delTicketLis);
        }

        // 重置回单审核
        List<String> ticketsPathList = addTicketLis
                .stream()
                .map(TCarrierOrderTickets::getImagePath)
                .collect(Collectors.toList());
        carrierOrderTicketsAuditBiz.createOrResetReceiptTicketAudit(new CreateOrResetReceiptTicketAuditBoModel()
                .setSource(tCarrierOrder.getDemandOrderSource())
                .setEntrustType(tCarrierOrder.getDemandOrderEntrustType())
                .setTicketType(requestModel.getImageType())
                .setCarrierOrderId(tCarrierOrder.getId())
                .setCarrierOrderCode(tCarrierOrder.getCarrierOrderCode())
                .setCarrierOrderStatus(tCarrierOrder.getStatus())
                .setTicketsPathList(ticketsPathList));

        //上传回收类型出库单或发货类型签收单，同步云盘
        if (ListUtils.isNotEmpty(carrierOrderAddBillList) || ListUtils.isNotEmpty(carrierOrderDelBillList)) {
            UpdateCarrierOrderBillModel updateCarrierOrderBillModel = new UpdateCarrierOrderBillModel();
            updateCarrierOrderBillModel.setCarrierOrderCode(tCarrierOrder.getCarrierOrderCode());
            updateCarrierOrderBillModel.setOperation(BaseContextHandler.getUserName());
            //新增
            if (ListUtils.isNotEmpty(carrierOrderAddBillList)) {
                updateCarrierOrderBillModel.setType(CommonConstant.INTEGER_ONE);//新增
                updateCarrierOrderBillModel.setCarrierOrderBillList(carrierOrderAddBillList);
                rabbitMqPublishBiz.syncCarrierOrderOutStockTickets(List.of(updateCarrierOrderBillModel));
            }
            //删除
            if (ListUtils.isNotEmpty(carrierOrderDelBillList)) {
                updateCarrierOrderBillModel.setType(CommonConstant.INTEGER_TWO);//新增
                updateCarrierOrderBillModel.setCarrierOrderBillList(carrierOrderDelBillList);
                rabbitMqPublishBiz.syncCarrierOrderOutStockTickets(List.of(updateCarrierOrderBillModel));
            }
        }
    }

    /**
     * 前台编辑票据- 新增票据
     *
     * @param order                   运单
     * @param imageType               票据类型
     * @param carrierOrderAddBillList 同步云盘票据信息
     * @param imagePath               票据路径
     */
    private TCarrierOrderTickets addTicket(TCarrierOrder order, Integer imageType, List<CarrierOrderBill> carrierOrderAddBillList, String imagePath) {
        CopyFileTypeEnum copyType = getCopyFileTypeEnum(imageType);//拷贝oss图片类型
        CarrierOrderTicketsTypeEnum ticketType = CarrierOrderTicketsTypeEnum.getEnum(imageType);//运单票据类型

        TCarrierOrderTickets ticketModel = new TCarrierOrderTickets();
        ticketModel.setCarrierOrderId(order.getId());
        ticketModel.setImageName(ticketType.getValue());
        ticketModel.setImagePath(commonBiz.copyFileToDirectoryOfType(copyType.getKey(), order.getCarrierOrderCode(), imagePath, null));
        ticketModel.setImageType(imageType);
        ticketModel.setUploadTime(new Date());
        ticketModel.setUploadUserName(BaseContextHandler.getUserName());
        commonBiz.setBaseEntityAdd(ticketModel, BaseContextHandler.getUserName());

        //上传回收类型出库单，同步云盘
        if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(order.getDemandOrderEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(order.getDemandOrderEntrustType())) {//回收
            if (CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getKey().equals(ticketType.getKey())) {//出库单
                CarrierOrderBill carrierOrderBill = new CarrierOrderBill();
                carrierOrderBill.setBillPath(ticketModel.getImagePath());
                carrierOrderBill.setType(CommonConstant.INTEGER_TWO);
                carrierOrderAddBillList.add(carrierOrderBill);
            }
        }
        //发货、回收入库、回收出库、供应商直配、退货仓库配送，签收单同步云盘
        if (CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey().equals(ticketType.getKey())) {//签收单
            List<Integer> entrustTypeListModel = Arrays.asList(EntrustTypeEnum.DELIVER.getKey(), EntrustTypeEnum.RECYCLE_IN.getKey(),
                    EntrustTypeEnum.RECYCLE_OUT.getKey(), EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey(), EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey());
            if (entrustTypeListModel.contains(order.getDemandOrderEntrustType())) {
                CarrierOrderBill carrierOrderBill = new CarrierOrderBill();
                carrierOrderBill.setBillPath(ticketModel.getImagePath());
                carrierOrderBill.setType(CommonConstant.INTEGER_FOUR);
                carrierOrderAddBillList.add(carrierOrderBill);
            }
        }

        return ticketModel;
    }

    //根据图片类型获取拷贝类型
    private CopyFileTypeEnum getCopyFileTypeEnum(Integer type) {
        if (CarrierOrderTicketsTypeEnum.CARRIER_ORDER_LOAD_TICKETS.getKey().equals(type)) {
            return CopyFileTypeEnum.CARRIER_ORDER_LOAD_TICKETS;
        } else if (CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getKey().equals(type)) {
            return CopyFileTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS;
        } else if (CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey().equals(type)) {
            return CopyFileTypeEnum.CARRIER_ORDER_SIGN_TICKETS;
        } else if (CarrierOrderTicketsTypeEnum.CARRIER_ORDER_OTHER_TICKETS.getKey().equals(type)) {
            return CopyFileTypeEnum.CARRIER_ORDER_OTHER_TICKETS;
        } else
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
    }

    /**
     * 运单取消
     * @param requestModel
     */
    @Transactional
    public void cancelCarrierOrder(CancelCarrierOrderRequestModel requestModel) {
        String carrierOrderIds = requestModel.getCarrierOrderId();
        if (StringUtils.isBlank(carrierOrderIds)) {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }

        //前台请求,获取当前登录人的公司id
        Long companyCarrierId = null;
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (companyCarrierId == null || CommonConstant.LONG_ZERO.equals(companyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }
        }

        //查询运单
        List<Long> carrierOrderIdList = Arrays.stream(carrierOrderIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<CarrierOrderListBeforeSignUpResponseModel> carrierOrderList = tCarrierOrderMapper.selectCarrierOrderSignDetail(StringUtils.listToString(carrierOrderIdList, ','), null);
        if (ListUtils.isEmpty(carrierOrderList) || carrierOrderList.size() != carrierOrderIdList.size()) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //校验选中的运单是否可取消
        for (CarrierOrderListBeforeSignUpResponseModel carrierOrderInfo : carrierOrderList) {
            //运单取消不能操作
            if (CommonConstant.INTEGER_ONE.equals(carrierOrderInfo.getIfCancel())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL);
            }
            //运单放空不能操作
            if (CommonConstant.INTEGER_ONE.equals(carrierOrderInfo.getIfEmpty())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EMPTY);
            }

            //发货、调拨、退货、供应商直配、采购、退货仓库配送、退货调拨、新生销售, 已出库的不能取消
            List<Integer> entrustTypeListModel = Arrays.asList(EntrustTypeEnum.DELIVER.getKey(), EntrustTypeEnum.TRANSFERS.getKey(),
                    EntrustTypeEnum.RETURN_GOODS.getKey(), EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey(), EntrustTypeEnum.PROCUREMENT.getKey(),
                    EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey(), EntrustTypeEnum.RETURN_GOODS_TRANSFERS.getKey(), EntrustTypeEnum.LIFE_SALE.getKey());
            if (entrustTypeListModel.contains(carrierOrderInfo.getEntrustType())) {
                if (!CarrierOrderOutStatusEnum.WAIT_OUT.getKey().equals(carrierOrderInfo.getOutStatus())) {
                    throw new BizException(CarrierDataExceptionEnum.NOT_ALLOWED_CANCEL);
                }
            }

            //提货后不能取消
            if (carrierOrderInfo.getStatus() > CarrierOrderStatusEnum.WAIT_LOAD.getKey()) {
                throw new BizException(CarrierDataExceptionEnum.CANCEL_CARRIERORDER_ERROR);
            }

            //判断取消的单子是不是当前车主的
            if (companyCarrierId != null && !companyCarrierId.equals(carrierOrderInfo.getCompanyCarrierId())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
            }
        }

        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(carrierOrderIds);
        if (!workOrderMap.isEmpty()) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }

        //判断运单是否已经生成对账
        if (carrierOrderCommonBiz.existSettlementData(StringUtils.listToString(carrierOrderIdList, ','))) {
            throw new BizException(CarrierDataExceptionEnum.BILL_HAS_BEEN_GENERATED_CANNOT_OPERATION);
        }

        List<TCarrierOrder> list = new ArrayList<>();
        List<TCarrierOrderOperateLogs> operateLogs = new ArrayList<>();
        List<TCarrierOrderEvents> eventsList = new ArrayList<>();
        List<CarrierOrderSynchronizeModel> synchronizeModels = new ArrayList<>();
        Map<Long, BigDecimal> carrierOrderCountMap = new HashMap<>();
        Map<Long, BigDecimal> carrierGoodCountMap = new HashMap<>();
        Map<Long, CarrierOrderListBeforeSignUpResponseModel> orderMap = new HashMap<>();
        CarrierOrderCancelToYeloLifeModel carrierOrderCancelToYeloLifeModel;
        List<CarrierOrderCancelToYeloLifeModel> carrierOrderForLifeList = new ArrayList<>();
        CancelCarrierOrderImpactDemandOrderModel orderResponseModels = new CancelCarrierOrderImpactDemandOrderModel();
        List<WorkGroupPushBoModel> workGroupPushBoModels = Lists.newArrayList();
        if (ListUtils.isNotEmpty(carrierOrderList)) {
            for (CarrierOrderListBeforeSignUpResponseModel responseModel : carrierOrderList) {
                //更新运单
                BigDecimal integer = carrierOrderCountMap.get(responseModel.getDemandOrderId());
                if (integer != null) {
                    integer = integer.add(responseModel.getExpectAmount());
                    carrierOrderCountMap.put(responseModel.getDemandOrderId(), integer);
                } else {
                    carrierOrderCountMap.put(responseModel.getDemandOrderId(), responseModel.getExpectAmount());
                }
                TCarrierOrder upOrder = new TCarrierOrder();
                upOrder.setId(responseModel.getCarrierOrderId());
                upOrder.setCancelReason(requestModel.getCancelReason());
                upOrder.setCancelOperatorName(BaseContextHandler.getUserName());
                upOrder.setCancelTime(new Date());
                upOrder.setIfCancel(CommonConstant.INTEGER_ONE);
                commonBiz.setBaseEntityModify(upOrder, BaseContextHandler.getUserName());
                list.add(upOrder);

                //日志
                TCarrierOrderOperateLogs carrierOrderOperateLogs = new TCarrierOrderOperateLogs();
                carrierOrderOperateLogs.setCarrierOrderId(responseModel.getCarrierOrderId());
                carrierOrderOperateLogs.setOperationType(CarrierOrderOperateLogsTypeEnum.CANCEL_CARRIER_ORDER.getKey());
                carrierOrderOperateLogs.setOperationContent(CarrierOrderOperateLogsTypeEnum.CANCEL_CARRIER_ORDER.getValue());
                carrierOrderOperateLogs.setRemark(requestModel.getCancelReason());
                carrierOrderOperateLogs.setOperatorName(BaseContextHandler.getUserName());
                carrierOrderOperateLogs.setOperateTime(new Date());
                commonBiz.setBaseEntityAdd(carrierOrderOperateLogs, BaseContextHandler.getUserName());
                operateLogs.add(carrierOrderOperateLogs);

                //事件
                TCarrierOrderEvents newEvent = new TCarrierOrderEvents();
                newEvent.setEvent(CarrierOrderEventsTypeEnum.HAS_CANCELED.getKey());
                newEvent.setEventDesc(CarrierOrderEventsTypeEnum.HAS_CANCELED.getValue());
                newEvent.setRemark(CarrierOrderEventsTypeEnum.HAS_CANCELED.getFormat());
                newEvent.setEventTime(new Date());
                newEvent.setCarrierOrderId(responseModel.getCarrierOrderId());
                newEvent.setOperateTime(new Date());
                newEvent.setOperatorName(BaseContextHandler.getUserName());
                commonBiz.setBaseEntityAdd(newEvent, BaseContextHandler.getUserName());
                eventsList.add(newEvent);

                //托盘同步的单子
                if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(responseModel.getSource())) {
                    CarrierOrderSynchronizeModel syncModel = new CarrierOrderSynchronizeModel();
                    syncModel.setType(responseModel.getEntrustType());
                    syncModel.setCarrierOrderCode(responseModel.getCarrierOrderCode());
                    syncModel.setDemandOrderId(responseModel.getDemandOrderId());//借用的临时字段
                    syncModel.setIfInvalid(CommonConstant.INTEGER_ONE);
                    syncModel.setUserName(BaseContextHandler.getUserName());
                    syncModel.setCancelReason(requestModel.getCancelReason());
                    synchronizeModels.add(syncModel);
                }
                //新生单子
                else if (DemandOrderSourceEnum.YELO_LIFE.getKey().equals(responseModel.getSource())) {
                    carrierOrderCancelToYeloLifeModel = new CarrierOrderCancelToYeloLifeModel();
                    carrierOrderCancelToYeloLifeModel.setDemandOrderCode(responseModel.getDemandOrderCode());
                    carrierOrderCancelToYeloLifeModel.setCarrierOrderCode(responseModel.getCarrierOrderCode());
                    carrierOrderCancelToYeloLifeModel.setCustomerOrderCode(responseModel.getCustomerOrderCode());
                    carrierOrderCancelToYeloLifeModel.setUserName(BaseContextHandler.getUserName());
                    carrierOrderForLifeList.add(carrierOrderCancelToYeloLifeModel);
                }

                List<CarrierOrderListBeforeSignUpGoodsModel> goodsInfo = responseModel.getGoodsInfo();
                for (CarrierOrderListBeforeSignUpGoodsModel good : goodsInfo) {
                    BigDecimal goodExpectAmount = carrierGoodCountMap.get(good.getDemandOrderGoodsId());
                    if (goodExpectAmount != null) {
                        goodExpectAmount = goodExpectAmount.add(good.getExpectAmount());
                        carrierGoodCountMap.put(good.getDemandOrderGoodsId(), goodExpectAmount);
                    } else {
                        carrierGoodCountMap.put(good.getDemandOrderGoodsId(), good.getExpectAmount());
                    }
                }
                orderMap.put(responseModel.getCarrierOrderId(), responseModel);

                // 构建智能推送运单取消节点事件Model
                workGroupPushBoModels.add(new WorkGroupPushBoModel()
                        .setOrderId(responseModel.getCarrierOrderId())
                        .setOrderSource(responseModel.getSource())
                        .setEntrustTypeGroup(responseModel.getEntrustType())
                        .setProjectLabel(responseModel.getProjectLabel())
                        .setOrderType(WorkGroupOrderTypeEnum.CARRIER_ORDER)
                        .setOrderNode(WorkGroupOrderNodeEnum.CARRIER_ORDER_CANCEL));
            }
        }

        //查询s单数据信息
        List<TCarrierOrderOrderRel> carrierOrderOrderRels = tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(carrierOrderIds);
        if (ListUtils.isNotEmpty(carrierOrderOrderRels)) {
            List<CancelCarrierOrderOrderRelModel> relModels = new ArrayList<>();
            for (TCarrierOrderOrderRel rel : carrierOrderOrderRels) {
                CarrierOrderListBeforeSignUpResponseModel code = orderMap.get(rel.getCarrierOrderId());
                if (code != null) {
                    CancelCarrierOrderOrderRelModel relModel = new CancelCarrierOrderOrderRelModel();
                    relModel.setDemandOrderOrderId(rel.getDemandOrderOrderId());
                    relModel.setExpectAmount(rel.getExpectAmount());
                    relModel.setOrderId(rel.getOrderId());
                    relModel.setOrderCode(rel.getOrderCode());
                    relModel.setDemandOrdeId(code.getDemandOrderId());
                    relModels.add(relModel);
                }
            }
            orderResponseModels.setCarrierOrderOrderRelModels(relModels);
        }

        orderResponseModels.setCarrierGoodCountMap(carrierGoodCountMap);
        orderResponseModels.setCarrierOrderCountMap(carrierOrderCountMap);
        orderResponseModels.setCarrierOrderForLifeList(carrierOrderForLifeList);
        if (ListUtils.isNotEmpty(synchronizeModels)) {
            orderResponseModels.setSynchronizeModels(synchronizeModels);
        }
        if (ListUtils.isNotEmpty(list)) {
            tCarrierOrderMapper.batchUpdateCarrierOrders(list);
        }
        if (ListUtils.isNotEmpty(eventsList)) {
            tCarrierOrderEventsMapper.batchInsertSelective(eventsList);
        }
        if (ListUtils.isNotEmpty(operateLogs)) {
            carrierOrderOperateLogsMapper.batchInsertSelective(operateLogs);
        }

        //过滤零担运单 校验生成零担费用
        List<Long> lessThanTruckLoadCarrierOrderIds = carrierOrderList.stream().filter(carrierModel -> BargainingModeEnum.LESS_THAN_TRUCKLOAD_PRICING.getKey().equals(carrierModel.getBargainingMode()))
                .map(CarrierOrderListBeforeSignUpResponseModel::getCarrierOrderId).collect(Collectors.toList());
        shippingOrderCommonBiz.checkAndGenerateShippingFeeFromCarrierOrderAction(lessThanTruckLoadCarrierOrderIds);

        //物流系统取消运单-修改需求单的状态和数量
        demandOrderBiz.cancelCarrierOrderUpdateDemandOrder(orderResponseModels);

        //预约单解除关联运单
        reservationOrderCommonBiz.disassociateCarrierOrder(carrierOrderIdList);

        // 发布智能推送运单取消节点事件
        applicationContext.publishEvent(new WorkGroupEventModel()
                .setWorkGroupPushBoModels(workGroupPushBoModels));
    }

    /**
     * 签收详情
     * @param requestModel
     * @return
     */
    public List<CarrierOrderListBeforeSignUpResponseModel> carrierOrderListBeforeSignUp(CarrierOrderListBeforeSignUpRequestModel requestModel) {
        if (requestModel == null || ListUtils.isEmpty(requestModel.getCarrierOrderIds())) {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }
        if (requestModel.getCarrierOrderIds().size() > 30) {
            throw new BizException(CarrierDataExceptionEnum.PLEASE_MAX_THIRTY_SIGN);
        }
        List<Long> carrierOrderIdList = requestModel.getCarrierOrderIds();
        //判断车辆是否处于未审核状态
        carrierOrderCommonBiz.checkIfCarrierOrdersStatusCanChange(carrierOrderIdList, CarrierDataExceptionEnum.VEHICLE_NEED_AUDIT_TIP1);

        String carrierOrderIds = StringUtils.listToString(carrierOrderIdList, ',');
        List<CarrierOrderListBeforeSignUpResponseModel> carrierOrderList = tCarrierOrderMapper.selectCarrierOrderSignDetail(carrierOrderIds, null);

        List<Integer> goodsUnitList = new ArrayList<>();
        for (CarrierOrderListBeforeSignUpResponseModel tmp : carrierOrderList) {
            if (!CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey().equals(tmp.getStatus())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_NOT_ALLOW_SIGN);
            }
            //运单取消不能操作
            if (CommonConstant.INTEGER_ONE.equals(tmp.getIfCancel())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_NOT_ALLOW_SIGN);
            }
            if (!goodsUnitList.contains(tmp.getGoodsUnit())) {
                goodsUnitList.add(tmp.getGoodsUnit());
            }
        }
        //不同单位的不能一起操作
        if (ListUtils.isNotEmpty(goodsUnitList) && goodsUnitList.size() > CommonConstant.INTEGER_ONE){
            throw new BizException(CarrierDataExceptionEnum.GOODS_UNIT_DIFFERENT);
        }
        return carrierOrderList;
    }

    /**
     * 运单签收-条件判断
     * @param requestModel
     */
    @Transactional
    public void carrierOrderSignUp(CarrierOrderConfirmSignUpRequestModel requestModel) {
        if (requestModel == null || ListUtils.isEmpty(requestModel.getSignList())) {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }
        if (requestModel.getSignList().size() > 30) {
            throw new BizException(CarrierDataExceptionEnum.PLEASE_MAX_THIRTY_SIGN);
        }
        List<Long> carrierOrderIdList = new ArrayList<>();
        Map<Long, BigDecimal> signUpFeeMap = new HashMap<>();//每个运单上的签收费用
        for (CarrierOrderSignUpRequestModel tmp:requestModel.getSignList()) {
            carrierOrderIdList.add(tmp.getCarrierOrderId());
            signUpFeeMap.put(tmp.getCarrierOrderId(),tmp.getSignFreightFee());
        }

        CarrierOrderListBeforeSignUpRequestModel beforeSignUpRequestModel = new CarrierOrderListBeforeSignUpRequestModel();
        beforeSignUpRequestModel.setCarrierOrderIds(carrierOrderIdList);
        List<CarrierOrderListBeforeSignUpResponseModel> carrierOrderList = carrierOrderListBeforeSignUp(beforeSignUpRequestModel);
        signCarrierOrder(carrierOrderList,signUpFeeMap,requestModel.getSignTime());
    }

    /**
     * 运单签收-业务逻辑处理
     * @param carrierOrderList 待签收运单信息
     * @param signUpFeeMap 每个运单上的签收费用
     * @param requestSignTime 签收时间
     */
    private void signCarrierOrder(List<CarrierOrderListBeforeSignUpResponseModel> carrierOrderList,Map<Long, BigDecimal> signUpFeeMap,Date requestSignTime) {
        Date now = new Date();
        TCarrierOrder upCarrierOrder;
        List<TCarrierOrder> batchUpdateCarrierOrders = new ArrayList<>();
        TCarrierOrderGoods upGoods;
        List<TCarrierOrderGoods> batchUpdateCarrierGoods = new ArrayList<>();
        TCarrierOrderEvents carrierOrderEvents;
        List<TCarrierOrderEvents> insertEvents = new ArrayList<>();
        TCarrierOrderOperateLogs carrierOrderOperateLogs;
        List<TCarrierOrderOperateLogs> insertLogs = new ArrayList<>();
        String unit;
        Date signTime = now;
        if (requestSignTime != null) {
            signTime = requestSignTime;
        }
        //签收备注
        String signRemark = "";
        if (requestSignTime != null) {
            signRemark = "，修改签收时间为" + DateUtils.dateToString(requestSignTime, "yyyy年MM月dd日");
        }
        List<Long> carrierOrderIdList = new ArrayList<>();

        for (CarrierOrderListBeforeSignUpResponseModel carrierOrderSignInfo : carrierOrderList) {
            //更新货物
            for (CarrierOrderListBeforeSignUpGoodsModel goods : carrierOrderSignInfo.getGoodsInfo()) {
                upGoods = new TCarrierOrderGoods();
                upGoods.setId(goods.getGoodsId());
                upGoods.setSignAmount(goods.getUnloadAmount());
                commonBiz.setBaseEntityModify(upGoods, BaseContextHandler.getUserName());
                batchUpdateCarrierGoods.add(upGoods);
            }

            //更新运单
            upCarrierOrder = new TCarrierOrder();
            upCarrierOrder.setId(carrierOrderSignInfo.getCarrierOrderId());
            upCarrierOrder.setStatus(CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey());
            upCarrierOrder.setStatusUpdateTime(now);
            upCarrierOrder.setSignTime(signTime);
            upCarrierOrder.setSignAmount(carrierOrderSignInfo.getUnloadAmount());
            if (carrierOrderSignInfo.getEntrustType() > CommonConstant.INTEGER_ZERO) {//托盘的订单才记录该字段
                upCarrierOrder.setSignFreightFee(signUpFeeMap.get(carrierOrderSignInfo.getCarrierOrderId()));
                upCarrierOrder.setEntrustFreight(signUpFeeMap.get(carrierOrderSignInfo.getCarrierOrderId()));
                upCarrierOrder.setEntrustFreightType(FreightTypeEnum.FIXED_PRICE.getKey());
            }
            commonBiz.setBaseEntityModify(upCarrierOrder, BaseContextHandler.getUserName());
            batchUpdateCarrierOrders.add(upCarrierOrder);

            unit = GoodsUnitEnum.getEnum(carrierOrderSignInfo.getGoodsUnit()).getUnit();
            //生成运单事件
            carrierOrderEvents = new TCarrierOrderEvents();
            carrierOrderEvents.setCarrierOrderId(carrierOrderSignInfo.getCarrierOrderId());
            carrierOrderEvents.setEvent(CarrierOrderEventsTypeEnum.SIGN_IN.getKey());
            carrierOrderEvents.setEventDesc(CarrierOrderEventsTypeEnum.SIGN_IN.getValue());
            carrierOrderEvents.setRemark(CarrierOrderEventsTypeEnum.SIGN_IN.format(StripTrailingZerosUtils.stripTrailingZerosToString(upCarrierOrder.getSignAmount()) + unit));
            carrierOrderEvents.setEventTime(now);
            carrierOrderEvents.setOperatorName(BaseContextHandler.getUserName());
            carrierOrderEvents.setOperateTime(now);
            commonBiz.setBaseEntityAdd(carrierOrderEvents, BaseContextHandler.getUserName());
            insertEvents.add(carrierOrderEvents);
            //没有预计里程数,更新预计里程数
            if (!(carrierOrderSignInfo.getExpectMileage() != null
                    && carrierOrderSignInfo.getExpectMileage().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO)) {
                carrierOrderIdList.add(upCarrierOrder.getId());
            }

            //生成操作日志
            carrierOrderOperateLogs = new TCarrierOrderOperateLogs();
            carrierOrderOperateLogs.setCarrierOrderId(carrierOrderSignInfo.getCarrierOrderId());
            carrierOrderOperateLogs.setOperationType(CarrierOrderOperateLogsTypeEnum.SIGN_IN.getKey());
            carrierOrderOperateLogs.setOperationContent(CarrierOrderOperateLogsTypeEnum.SIGN_IN.getValue());
            carrierOrderOperateLogs.setOperatorName(BaseContextHandler.getUserName());
            carrierOrderOperateLogs.setOperateTime(now);
            carrierOrderOperateLogs.setRemark(CarrierOrderOperateLogsTypeEnum.SIGN_IN.format(StripTrailingZerosUtils.stripTrailingZerosToString(upCarrierOrder.getSignAmount()) + unit, ConverterUtils.toString(signUpFeeMap.get(carrierOrderSignInfo.getCarrierOrderId()))) + signRemark);
            commonBiz.setBaseEntityAdd(carrierOrderOperateLogs, BaseContextHandler.getUserName());
            insertLogs.add(carrierOrderOperateLogs);
        }
        if (ListUtils.isNotEmpty(batchUpdateCarrierOrders)) {
            tCarrierOrderMapper.batchUpdateCarrierOrders(batchUpdateCarrierOrders);
        }
        if (ListUtils.isNotEmpty(batchUpdateCarrierGoods)) {
            tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(batchUpdateCarrierGoods);
        }
        if (ListUtils.isNotEmpty(insertEvents)) {
            tCarrierOrderEventsMapper.batchInsertSelective(insertEvents);
        }
        if (ListUtils.isNotEmpty(insertLogs)) {
            carrierOrderOperateLogsMapper.batchInsertSelective(insertLogs);
        }

        //根据运单所属需求单Id查询需求单状态
        List<Long> demandOrderIdList = carrierOrderList.stream().map(CarrierOrderListBeforeSignUpResponseModel::getDemandOrderId).distinct().collect(Collectors.toList());
        List<GetDemandOrderInfoByIdsModel> demandOrderInfoList = tDemandOrderMapper.getDemandOrderInfoByIds(StringUtils.listToString(demandOrderIdList, ','));
        List<Long> demandOrderIds = new ArrayList<>();
        demandOrderInfoList.forEach(demandOrder -> {
            if (demandOrder.getEntrustStatus() < DemandOrderStatusEnum.SIGN_DISPATCH.getKey()) {
                demandOrderIds.add(demandOrder.getDemandOrderId());
            }
        });
        if (ListUtils.isNotEmpty(demandOrderIds)) {
            //如果需求单下所有运单（除已取消的）均为已签收状态，则判断是否将需求单置为已签收状态
            List<DemandCarrierOrderRecursiveModel> demandCarrierOrderList = tCarrierOrderMapper.getNotCancelByDemandOrderIds(StringUtils.listToString(demandOrderIds, ','));
            List<Long> demandIds = new ArrayList<>();
            demandCarrierOrderList.forEach(order -> {
                DemandCarrierOrderModel demandCarrierOrderModel = order.getCarrierOrderList().stream().filter(carrierOrder -> !CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(carrierOrder.getStatus())).findFirst().orElse(null);
                if (demandCarrierOrderModel == null) {
                    demandIds.add(order.getDemandOrderId());
                }
            });
            if (ListUtils.isNotEmpty(demandIds)) {
                //修改需求单状态
                UpdateDemandOrderStatusByIdsRequestModel updateDemandOrderStatusByIdsRequestModel = new UpdateDemandOrderStatusByIdsRequestModel();
                updateDemandOrderStatusByIdsRequestModel.setDemandOrderIdList(demandIds);
                updateDemandOrderStatusByIdsRequestModel.setEntrustStatus(DemandOrderStatusEnum.SIGN_DISPATCH.getKey());
                updateDemandOrderStatusByIdsRequestModel.setOperatorName(BaseContextHandler.getUserName());
                demandOrderBiz.updateDemandOrderStatusByIds(updateDemandOrderStatusByIdsRequestModel);
            }
        }
        if (ListUtils.isNotEmpty(carrierOrderIdList)) {
            AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.carrierOrderMileage(carrierOrderIdList, null));
        }
    }

    /**
     *
     * 修改车辆-审核/驳回车辆
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public void auditOrRejectVehicle(AuditOrRejectVehicleRequestModel requestModel) {
        //校验入参
        if (requestModel.getType().equals(CommonConstant.TWO) && (StringUtils.isBlank(requestModel.getRejectReason())
                || requestModel.getRejectReason().length() > CommonConstant.INTEGER_THREE_HUNDRED)) {//驳回
            throw new BizException(CarrierDataExceptionEnum.REJECT_REASON_EMPTY);
        }

        //查询运单是否存在
        TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (tCarrierOrder == null || IfValidEnum.INVALID.getKey().equals(tCarrierOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //查询审核的运单车辆记录是否存在
        TCarrierOrderVehicleHistory tCarrierOrderVehicleHistory = tCarrierOrderVehicleHistoryMapper.selectByPrimaryKey(requestModel.getVehicleHistoryId());
        if (null == tCarrierOrderVehicleHistory) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_VEHICLE_RELATION_EMPTY);
        }
        //判断审核的运单车辆记录状态
        if (AuditStatusEnum.NOT_NEED_AUDIT.getKey().equals(tCarrierOrderVehicleHistory.getAuditStatus())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_VEHICLE_WAIT_SUBMIT);
        } else if (AuditStatusEnum.AUDIT_THROUGH.getKey().equals(tCarrierOrderVehicleHistory.getAuditStatus())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_VEHICLE_AUDIT_THROUGH);
        } else if (AuditStatusEnum.AUDIT_REJECT.getKey().equals(tCarrierOrderVehicleHistory.getAuditStatus())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_VEHICLE_AUDIT_REJECT);
        }

        //记录审核/驳回日志
        List<CarrierOrderSynchronizeModel> synchronizeModels = new ArrayList<>();//云仓
        List<CarrierOrderSynchronizeModel> synchronizeToLeYiModels = new ArrayList<>();//云盘
        UpdateLifeCarrierOrderVehicleMessage updateLifeCarrierOrderVehicleMessage = null;//新生
        TCarrierOrderOperateLogs carrierOrderOperateLogs = new TCarrierOrderOperateLogs();
        carrierOrderOperateLogs.setCarrierOrderId(requestModel.getCarrierOrderId());
        TCarrierOrderVehicleHistory carrierOrderVehicleHistory;
        List<TCarrierOrderVehicleHistory> list = new ArrayList<>();
        Date now = new Date();

        if (requestModel.getType().equals(CommonConstant.ONE)) {//审核
            Integer driverEnabled = tCarrierOrderVehicleHistoryMapper.getCarrierDriverByHistoryId(requestModel.getVehicleHistoryId(), tCarrierOrder.getCompanyCarrierId());
            if (driverEnabled.equals(EnabledEnum.DISABLED.getKey())) {
                throw new BizException(CarrierDataExceptionEnum.DRIVER_ACCOUNT_DISABLED);
            }
            DriverAndVehicleResponseModel model = tCarrierOrderVehicleHistoryMapper.getDriverVehicleInfoByCarrierOrderId(requestModel.getCarrierOrderId());
            if (null == model) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_VEHICLE_RELATION_EMPTY);
            }

            List<FuzzyQueryVehicleInfoResponseModel> dbTVehicleBasicList = tVehicleBasicMapper.queryVehicleInfoByVehicleNos(LocalStringUtil.listTostring(Arrays.asList(model.getVehicleNo(), tCarrierOrderVehicleHistory.getVehicleNo()), ','));
            if (ListUtils.isEmpty(dbTVehicleBasicList)) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
            }

            Map<String, Integer> vehiclePropertyMap = new HashMap<>();
            dbTVehicleBasicList.forEach(temp -> vehiclePropertyMap.put(temp.getVehicleNo(), temp.getVehicleProperty()));

            //存在内部车辆,是否有月结算账单
            if (((VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehiclePropertyMap.get(model.getVehicleNo())) || VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehiclePropertyMap.get(model.getVehicleNo())))
                    || (VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehiclePropertyMap.get(tCarrierOrderVehicleHistory.getVehicleNo())) || VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehiclePropertyMap.get(tCarrierOrderVehicleHistory.getVehicleNo()))))
                    && carrierOrderCommonBiz.existSettlementData(ConverterUtils.toString(tCarrierOrder.getId()))) {
                throw new BizException(CarrierDataExceptionEnum.BILL_HAS_BEEN_GENERATED_CANNOT_OPERATION);
            }

            //非本月调度的外部车辆的运单,在当前月不能修改车辆成为内部车辆
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date cunMothDate = calendar.getTime();
            if(VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(vehiclePropertyMap.get(model.getVehicleNo()))
                    && (VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehiclePropertyMap.get(tCarrierOrderVehicleHistory.getVehicleNo())) || VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehiclePropertyMap.get(tCarrierOrderVehicleHistory.getVehicleNo())))
                    && tCarrierOrder.getDispatchTime().before(cunMothDate)){
                throw new BizException(CarrierDataExceptionEnum.LAST_MONTH_OWN_VEHICLE_STATEMENT_HAS_BEEN_GENERATED);
            }

            //将前一个车辆信息置为无效
            carrierOrderVehicleHistory = new TCarrierOrderVehicleHistory();
            carrierOrderVehicleHistory.setId(model.getVehicleHistoryId());
            carrierOrderVehicleHistory.setIfInvalid(IfValidEnum.INVALID.getKey());
            commonBiz.setBaseEntityModify(carrierOrderVehicleHistory, BaseContextHandler.getUserName());
            list.add(carrierOrderVehicleHistory);
            //将当期车辆信息置为已审核且有效
            carrierOrderVehicleHistory = new TCarrierOrderVehicleHistory();
            carrierOrderVehicleHistory.setId(requestModel.getVehicleHistoryId());
            carrierOrderVehicleHistory.setIfInvalid(IfValidEnum.VALID.getKey());
            carrierOrderVehicleHistory.setAuditStatus(AuditStatusEnum.AUDIT_THROUGH.getKey());
            carrierOrderVehicleHistory.setAuditTime(now);
            carrierOrderVehicleHistory.setAuditUserId(BaseContextHandler.getUserId());
            carrierOrderVehicleHistory.setAuditUserName(BaseContextHandler.getUserName());
            carrierOrderVehicleHistory.setAuditSource(requestModel.getAuditSource());
            commonBiz.setBaseEntityModify(carrierOrderVehicleHistory, BaseContextHandler.getUserName());
            list.add(carrierOrderVehicleHistory);

            //记录审核日志
            carrierOrderOperateLogs.setOperationType(CarrierOrderOperateLogsTypeEnum.AUDIT_VEHICLE.getKey());
            carrierOrderOperateLogs.setOperationContent(CarrierOrderOperateLogsTypeEnum.AUDIT_VEHICLE.getValue());

            TCarrierOrder order = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(tCarrierOrderVehicleHistory.getCarrierOrderId());
            if (order == null) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
            }
            //修改gps记录上的调度单信息
            TVehicleGps tVehicleGps = tVehicleGpsMapper.selectVehicleByVehicleId(tCarrierOrderVehicleHistory.getVehicleId());
            if (tVehicleGps != null) {
                TVehicleGps uptVehicleGps = new TVehicleGps();
                uptVehicleGps.setId(tVehicleGps.getId());
                uptVehicleGps.setDispatchOrderId(order.getDispatchOrderId());
                uptVehicleGps.setDispatchOrderCode(tCarrierOrder.getDispatchOrderCode());
                commonBiz.setBaseEntityModify(uptVehicleGps, BaseContextHandler.getUserName());
                tVehicleGpsMapper.updateByPrimaryKeySelective(uptVehicleGps);
            }

            //同步运单上车辆信息
            if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(tCarrierOrder.getDemandOrderSource())) {
                CarrierOrderSynchronizeModel syncModel = new CarrierOrderSynchronizeModel();
                syncModel.setType(order.getDemandOrderEntrustType());
                syncModel.setCarrierOrderCode(order.getCarrierOrderCode());
                syncModel.setVehicleNumber(tCarrierOrderVehicleHistory.getVehicleNo());
                syncModel.setDriverMobilePhone(tCarrierOrderVehicleHistory.getDriverMobile());
                syncModel.setIdentityCardNumber(tCarrierOrderVehicleHistory.getDriverIdentity());
                syncModel.setDriverName(tCarrierOrderVehicleHistory.getDriverName());
                syncModel.setUserName(BaseContextHandler.getUserName());
                syncModel.setExpectArrivalTime(tCarrierOrderVehicleHistory.getExpectArrivalTime());
                synchronizeModels.add(syncModel);

                //回收入库、回收出库、预约类型的
                if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())
                        || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())
                        || EntrustTypeEnum.BOOKING.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())
                        || EntrustTypeEnum.DELIVER.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())
                        || EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey().equals(tCarrierOrder.getDemandOrderEntrustType()))   {
                    if (EntrustTypeEnum.DELIVER.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())
                            || EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())){
                        syncModel.setSendOtherOutCompanyFlag(1);
                    }
                    synchronizeToLeYiModels.add(syncModel);
                }
            }else if (DemandOrderSourceEnum.YELO_LIFE.getKey().equals(tCarrierOrder.getDemandOrderSource())) {
                //新生运单修改车辆司机同步新生
                updateLifeCarrierOrderVehicleMessage = new UpdateLifeCarrierOrderVehicleMessage();
                updateLifeCarrierOrderVehicleMessage.setCarrierOrderCode(order.getCarrierOrderCode());
                updateLifeCarrierOrderVehicleMessage.setCustomerOrderCode(order.getCustomerOrderCode());
                updateLifeCarrierOrderVehicleMessage.setVehicleNumber(tCarrierOrderVehicleHistory.getVehicleNo());
                updateLifeCarrierOrderVehicleMessage.setDriverName(tCarrierOrderVehicleHistory.getDriverName());
                updateLifeCarrierOrderVehicleMessage.setDriverMobilePhone(tCarrierOrderVehicleHistory.getDriverMobile());
                updateLifeCarrierOrderVehicleMessage.setUserName(BaseContextHandler.getUserName());
            }

            //修改审核物流事件
            TCarrierOrderEvents auditEvent = new TCarrierOrderEvents();
            auditEvent.setCarrierOrderId(order.getId());
            auditEvent.setEvent(CarrierOrderEventsTypeEnum.AUDIT_VEHICLES.getKey());
            auditEvent.setEventDesc(CarrierOrderEventsTypeEnum.AUDIT_VEHICLES.getValue());
            auditEvent.setEventTime(now);
            auditEvent.setOperateTime(now);
            auditEvent.setOperatorName(BaseContextHandler.getUserName());
            commonBiz.setBaseEntityAdd(auditEvent, BaseContextHandler.getUserName());
            tCarrierOrderEventsMapper.insertSelective(auditEvent);
            TCarrierOrderEvents modifyEvent = tCarrierOrderEventsMapper.selectEventByEventTypeAndCarrieriOrderId(order.getId(), CarrierOrderEventsTypeEnum.UPDATE_VEHICLE_WAIT_AUDIT.getKey());
            if (modifyEvent != null) {
                TCarrierOrderEvents upModifyEvent = new TCarrierOrderEvents();
                upModifyEvent.setId(modifyEvent.getId());
                upModifyEvent.setEvent(CarrierOrderEventsTypeEnum.UPDATE_VEHICLE_AUDIT.getKey());
                upModifyEvent.setEventDesc(CarrierOrderEventsTypeEnum.UPDATE_VEHICLE_AUDIT.getValue());
                commonBiz.setBaseEntityModify(modifyEvent, BaseContextHandler.getUserName());
                tCarrierOrderEventsMapper.updateByPrimaryKeySelective(upModifyEvent);
            }
            //审核通过记录操作日志
            TOperateLogs operateLogs = new TOperateLogs();
            operateLogs.setObjectType(OperateLogsOperateTypeEnum.AUDIT_THROUGH_VEHICLE.getObjectType().getKey());
            operateLogs.setObjectId(requestModel.getVehicleHistoryId());
            operateLogs.setOperateType(OperateLogsOperateTypeEnum.AUDIT_THROUGH_VEHICLE.getOperateType());
            operateLogs.setOperateContents(OperateLogsOperateTypeEnum.AUDIT_THROUGH_VEHICLE.getOperateContents());
            operateLogs.setOperateUserName(BaseContextHandler.getUserName());
            operateLogs.setOperateTime(now);
            commonBiz.setBaseEntityAdd(operateLogs, BaseContextHandler.getUserName());
            operateLogsMapper.insertSelective(operateLogs);

            //修改调度单车辆司机信息
            TCarrierOrder carrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
            if (carrierOrder == null) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
            }
            TDispatchOrder dispatchOrder = new TDispatchOrder();
            dispatchOrder.setId(carrierOrder.getDispatchOrderId());
            dispatchOrder.setDriverId(tCarrierOrderVehicleHistory.getDriverId());
            dispatchOrder.setDriverName(tCarrierOrderVehicleHistory.getDriverName());
            dispatchOrder.setDriverMobile(tCarrierOrderVehicleHistory.getDriverMobile());
            dispatchOrder.setDriverIdentity(tCarrierOrderVehicleHistory.getDriverIdentity());
            dispatchOrder.setVehicleId(tCarrierOrderVehicleHistory.getVehicleId());
            dispatchOrder.setVehicleNo(tCarrierOrderVehicleHistory.getVehicleNo());
            commonBiz.setBaseEntityModify(dispatchOrder, BaseContextHandler.getUserName());
            dispatchOrderMapper.updateByPrimaryKeySelectiveEncrypt(dispatchOrder);

        } else {//驳回
            carrierOrderVehicleHistory = new TCarrierOrderVehicleHistory();
            carrierOrderVehicleHistory.setId(requestModel.getVehicleHistoryId());
            carrierOrderVehicleHistory.setAuditStatus(AuditStatusEnum.AUDIT_REJECT.getKey());
            carrierOrderVehicleHistory.setAuditTime(now);
            carrierOrderVehicleHistory.setAuditUserId(BaseContextHandler.getUserId());
            carrierOrderVehicleHistory.setAuditUserName(BaseContextHandler.getUserName());
            carrierOrderVehicleHistory.setAuditSource(requestModel.getAuditSource());
            carrierOrderVehicleHistory.setRemark(requestModel.getRejectReason());
            carrierOrderVehicleHistory.setRejectReason(requestModel.getRejectReason());
            commonBiz.setBaseEntityModify(carrierOrderVehicleHistory, BaseContextHandler.getUserName());
            list.add(carrierOrderVehicleHistory);

            //驳回车辆修改日志
            carrierOrderOperateLogs.setOperationType(CarrierOrderOperateLogsTypeEnum.REJECT_VEHICLE.getKey());
            carrierOrderOperateLogs.setOperationContent(CarrierOrderOperateLogsTypeEnum.REJECT_VEHICLE.getValue());
            carrierOrderOperateLogs.setRemark(requestModel.getRejectReason());
            //驳回修改物流事件
            TCarrierOrderEvents modifyEvent = tCarrierOrderEventsMapper.selectEventByEventTypeAndCarrieriOrderId(tCarrierOrderVehicleHistory.getCarrierOrderId(), CarrierOrderEventsTypeEnum.UPDATE_VEHICLE_WAIT_AUDIT.getKey());
            if (modifyEvent != null) {
                TCarrierOrderEvents upModifyEvent = new TCarrierOrderEvents();
                upModifyEvent.setId(modifyEvent.getId());
                upModifyEvent.setEvent(CarrierOrderEventsTypeEnum.UPDATE_VEHICLE_REJECT.getKey());
                upModifyEvent.setEventDesc(CarrierOrderEventsTypeEnum.UPDATE_VEHICLE_REJECT.getValue());
                commonBiz.setBaseEntityModify(modifyEvent, BaseContextHandler.getUserName());
                tCarrierOrderEventsMapper.updateByPrimaryKeySelective(upModifyEvent);
            }
            //驳回记录操作日志
            TOperateLogs operateLogs = new TOperateLogs();
            operateLogs.setObjectType(OperateLogsOperateTypeEnum.AUDIT_THROUGH_REJECT.getObjectType().getKey());
            operateLogs.setObjectId(requestModel.getVehicleHistoryId());
            operateLogs.setOperateType(OperateLogsOperateTypeEnum.AUDIT_THROUGH_REJECT.getOperateType());
            operateLogs.setOperateContents(OperateLogsOperateTypeEnum.AUDIT_THROUGH_REJECT.getOperateContents());
            operateLogs.setOperateUserName(BaseContextHandler.getUserName());
            operateLogs.setOperateTime(now);
            operateLogs.setRemark(requestModel.getRejectReason());
            commonBiz.setBaseEntityAdd(operateLogs, BaseContextHandler.getUserName());
            operateLogsMapper.insertSelective(operateLogs);

        }
        if (ListUtils.isNotEmpty(list)) {
            tCarrierOrderVehicleHistoryMapper.batchUpdateByPrimaryKeySelective(list);
        }
        //记录日志
        carrierOrderOperateLogs.setOperatorName(BaseContextHandler.getUserName());
        carrierOrderOperateLogs.setOperateTime(now);
        commonBiz.setBaseEntityAdd(carrierOrderOperateLogs, BaseContextHandler.getUserName());
        carrierOrderOperateLogsMapper.insertSelective(carrierOrderOperateLogs);


        //修改运单表是否存在待审核车辆字段
        TCarrierOrder upCarrierOrder = new TCarrierOrder();
        upCarrierOrder.setId(tCarrierOrder.getId());
        upCarrierOrder.setIfWaitAuditVehicle(CommonConstant.INTEGER_ZERO);
        commonBiz.setBaseEntityModify(upCarrierOrder, BaseContextHandler.getUserName());
        tCarrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(upCarrierOrder);

        //新生运单-修改车辆司机同步新生
        if (updateLifeCarrierOrderVehicleMessage != null) {
            log.info("运单修改车辆审核通过时同步新生：" + updateLifeCarrierOrderVehicleMessage);
            SyncCarrierOrderToYeloLifeModel<Object> syncCarrierOrderToYeloLifeModel = new SyncCarrierOrderToYeloLifeModel<>();
            syncCarrierOrderToYeloLifeModel.setType(SyncCarrierOrderToYeloLifeModel.SyncTypeEnum.UPDATE_VEHICLE.getKey());
            syncCarrierOrderToYeloLifeModel.setMsgData(Collections.singletonList(updateLifeCarrierOrderVehicleMessage));
            rabbitMqPublishBiz.syncCarrierOrderToYeloLife(syncCarrierOrderToYeloLifeModel);
        }

        //同步云仓
        if (requestModel.getType().equals(CommonConstant.ONE) && ListUtils.isNotEmpty(synchronizeModels)) {
            log.info("运单修改车辆审核通过同步云仓：" + synchronizeModels);
            CarrierOrderSynchronizeLeyiModel carrierOrderSynchronizeLeyiModel = new CarrierOrderSynchronizeLeyiModel();
            carrierOrderSynchronizeLeyiModel.setCarrierOrderSynchronizeModels(synchronizeModels);
            rabbitMqPublishBiz.carrierOrderSynchronizeToWarehouse(carrierOrderSynchronizeLeyiModel);
        }

        //同步云盘
        if (requestModel.getType().equals(CommonConstant.ONE) && ListUtils.isNotEmpty(synchronizeToLeYiModels)) {
            log.info("运单修改车辆审核通过同步云盘：" + synchronizeToLeYiModels);
            CarrierOrderSynchronizeLeyiModel carrierOrderSynchronizeLeyiModel = new CarrierOrderSynchronizeLeyiModel();
            carrierOrderSynchronizeLeyiModel.setCarrierOrderSynchronizeModels(synchronizeToLeYiModels);
            rabbitMqPublishBiz.carrierOrderSynchronizeToLeyi(carrierOrderSynchronizeLeyiModel);
        }
    }

    /**
     * 下载提货单
     *
     * @param requestModel
     * @return
     */
    public DownloadLadingBillResponseModel downloadLadingBill(CarrierOrderDetailRequestModel requestModel) {
        DownloadLadingBillResponseModel downloadLadingBillResponseModel = tCarrierOrderMapper.downloadLadingBill(requestModel.getCarrierOrderId());
        if (downloadLadingBillResponseModel == null){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //运单还没有生成条形码,生成条形码并上传到OSS
        if (StringUtils.isBlank(downloadLadingBillResponseModel.getQrCodePicPath())) {
            //二维码参数
            Map<String, Object> qrCodeParamsMap = DriverAppletQrCodeJumpPageEnum.CARRIER_ORDER_DETAIL_PAGE.getParamsMap();
            qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_CODE.getKey(), downloadLadingBillResponseModel.getCarrierOrderCode());
            if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(downloadLadingBillResponseModel.getEntrustType())
                    || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(downloadLadingBillResponseModel.getEntrustType())){
                qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_BILL_TYPE.getKey(), QrCodeParamsBillTypeEnum.RECYCLE.getKey());
            }else {
                qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_BILL_TYPE.getKey(), QrCodeParamsBillTypeEnum.OTHER.getKey());
            }
            byte[] qrCodePicByte = commonBiz.createQrCode(configKeyConstant.qrcodeCommonPrefix, qrCodeParamsMap).getFileByte();
            downloadLadingBillResponseModel.setQrCodePicByte(qrCodePicByte);

            //更新运单条形码图片路径
            if (qrCodePicByte != null) {
                //捕获异常目的：二维码是否上传成功不影响下载提货单，下次触发的时候再上传
                try {
                    //上传oss
                    String fileName = downloadLadingBillResponseModel.getCarrierOrderCode() + CommonConstant.POINT + CommonConstant.PIC_JPG;
                    FileUploadRequestModel fileUploadRequestModel = new FileUploadRequestModel();
                    fileUploadRequestModel.setFilePath(configKeyConstant.imageUploadCatalog + configKeyConstant.carrierOrderQrCode);
                    fileUploadRequestModel.setFileName(fileName);
                    fileUploadRequestModel.setFileByte(qrCodePicByte);
                    basicDataClient.uploadFileOSS(fileUploadRequestModel);
                    String qrCodePicPath = configKeyConstant.carrierOrderQrCode + "/" + fileName;

                    //更新运单条形码图片路径
                    TCarrierOrder tCarrierOrder = new TCarrierOrder();
                    tCarrierOrder.setId(downloadLadingBillResponseModel.getCarrierOrderId());
                    tCarrierOrder.setQrCodePicPath(qrCodePicPath);
                    commonBiz.setBaseEntityModify(tCarrierOrder, BaseContextHandler.getUserName());
                    tCarrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(tCarrierOrder);
                }catch (Exception e){
                    log.info("二维码图片上传失败：", e);
                }
            }
        }
        return downloadLadingBillResponseModel;
    }

    /**
     * 运单日志
     *
     * @param carrierOrderId
     * @return
     */
    public List<GetCarrierOrderLogsResponseModel> getCarrierOrderLogs(Long carrierOrderId) {
        return carrierOrderOperateLogsMapper.getLogsByCarrierOrderId(carrierOrderId);
    }

    /**
     * 删除票据
     *
     */
    @Transactional
    public void delTickets(DeleteTicketsRequestModel requestModel) {
        TCarrierOrder order = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (order == null || IfValidEnum.INVALID.getKey().equals(order.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //运单取消不能操作
        if (order.getIfCancel().equals(CommonConstant.INTEGER_ONE)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL);
        }
        //运单放空不能操作
        if (CommonConstant.INTEGER_ONE.equals(order.getIfEmpty())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EMPTY);
        }
        //运单已关联对账不可修改
        if (order.getCarrierSettleStatementStatus() > CarrierSettleStatementStatusEnum.NOT_RELATED.getKey()) {
            throw new BizException(CarrierDataExceptionEnum.ALREADY_RELEVANCY_RECONCILIATION);
        }
        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(order.getId().toString());
        if (!workOrderMap.isEmpty()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }

        //前台请求
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            //判断运单是不是当前车主的
            if (loginUserCompanyCarrierId == null ||
                    CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId) ||
                    !order.getCompanyCarrierId().equals(loginUserCompanyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
            }
        }

        //车辆不存在或待审核不能操作
        DriverIdVehicleIdRelationIdModel model = new DriverIdVehicleIdRelationIdModel();
        model.setCarrierOrderId(requestModel.getCarrierOrderId());
        TCarrierOrderVehicleHistory history = tCarrierOrderVehicleHistoryMapper.getTopByCarrierOrderId(model);
        if (history == null) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        if (history.getAuditStatus().equals(AuditStatusEnum.WAIT_AUDIT.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_WAIT_AUDIT);
        }

        //校验票据是否存在
        TCarrierOrderTickets dbCarrierOrderTickets = tCarrierOrderTicketsMapper.selectByPrimaryKey(requestModel.getImageId());
        if (dbCarrierOrderTickets == null || dbCarrierOrderTickets.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.CERTIFICATION_PICTURES_NOT_EXIST);
        }

        // 回单审核中不允许编辑
        if (CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey().equals(dbCarrierOrderTickets.getImageType())) {
            carrierOrderTicketsAuditBiz.updateReceiptTicketCheck(order.getDemandOrderSource(), order.getId());
        }

        List<CarrierOrderBill> carrierOrderBillList = new ArrayList<>();
        //删除回收入库、回收出库类型单子的出库单，同步云盘
        if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(order.getDemandOrderEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(order.getDemandOrderEntrustType())) {
            if (CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getKey().equals(dbCarrierOrderTickets.getImageType())){
                CarrierOrderBill bill = new CarrierOrderBill();
                bill.setType(CommonConstant.INTEGER_TWO);
                bill.setBillPath(dbCarrierOrderTickets.getImagePath());
                carrierOrderBillList.add(bill);
            }
        }

        //发货、回收入库、回收出库、供应商直配、退货仓库配送，签收单同步云盘
        if (CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey().equals(dbCarrierOrderTickets.getImageType())) {//签收单
            List<Integer> entrustTypeListModel = Arrays.asList(EntrustTypeEnum.DELIVER.getKey(), EntrustTypeEnum.RECYCLE_IN.getKey(),
                    EntrustTypeEnum.RECYCLE_OUT.getKey(), EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey(), EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey());
            if (entrustTypeListModel.contains(order.getDemandOrderEntrustType())) {
                CarrierOrderBill bill = new CarrierOrderBill();
                bill.setType(CommonConstant.INTEGER_FOUR);
                bill.setBillPath(dbCarrierOrderTickets.getImagePath());
                carrierOrderBillList.add(bill);
            }
        }

        //删除票据
        TCarrierOrderTickets tCarrierOrderTickets = new TCarrierOrderTickets();
        tCarrierOrderTickets.setId(requestModel.getImageId());
        tCarrierOrderTickets.setValid(IfValidEnum.INVALID.getKey());
        commonBiz.setBaseEntityModify(tCarrierOrderTickets, BaseContextHandler.getUserName());
        tCarrierOrderTicketsMapper.updateByPrimaryKeySelective(tCarrierOrderTickets);

        //删除回收类型出库单或发货类型签收单，同步云盘
        if (ListUtils.isNotEmpty(carrierOrderBillList)) {
            UpdateCarrierOrderBillModel updateCarrierOrderBillModel = new UpdateCarrierOrderBillModel();
            updateCarrierOrderBillModel.setCarrierOrderCode(order.getCarrierOrderCode());
            updateCarrierOrderBillModel.setOperation(BaseContextHandler.getUserName());
            updateCarrierOrderBillModel.setType(CommonConstant.INTEGER_TWO);//删除
            updateCarrierOrderBillModel.setCarrierOrderBillList(carrierOrderBillList);
            rabbitMqPublishBiz.syncCarrierOrderOutStockTickets(Arrays.asList(updateCarrierOrderBillModel));
        }
    }

    /**
     * 获取微信推送人
     * @param requestModel
     * @return
     */
    public List<GetCarrierOrderWeixinPushResponseModel> getCarrierOrderWeixinPushInfo(GetCarrierOrderWeixinPushRequestModel requestModel) {
        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(requestModel.getCarrierOrderId().toString());
        if (!workOrderMap.isEmpty()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }
        List<GetCarrierOrderWeixinPushResponseModel> getCarrierOrderWeixinPushResponseModels = tCarrierOrderWxMapper.selectCarrierOrderWeixinPushInfoByCarrierOrder(requestModel.getCarrierOrderId());
        if (ListUtils.isNotEmpty(getCarrierOrderWeixinPushResponseModels)) {

            String senderRoleName = WeixinPushInfoRoleEnum.SENDER.getValue();
            String receiverRoleName = WeixinPushInfoRoleEnum.RECEIVER.getValue();
            String entrustRoleName = WeixinPushInfoRoleEnum.ENTRUST.getValue();
            String otherRoleName = WeixinPushInfoRoleEnum.OTHER.getValue();

            Map<String, String> configMap = sysConfigBiz.getSysConfig(ConfigKeyEnum.ConfigGroupCodeEnum.WECHAT_SENDER_ROLE.getCode());

            if (!CollectionUtils.isEmpty(configMap)) {
                if (StringUtils.isNotBlank(configMap.get(ConfigKeyEnum.SENDER_ROLE_NAME.getValue()))) {
                    senderRoleName = configMap.get(ConfigKeyEnum.SENDER_ROLE_NAME.getValue());
                }
                if (StringUtils.isNotBlank(configMap.get(ConfigKeyEnum.RECEIVER_ROLE_NAME.getValue()))) {
                    receiverRoleName = configMap.get(ConfigKeyEnum.RECEIVER_ROLE_NAME.getValue());
                }
                if (StringUtils.isNotBlank(configMap.get(ConfigKeyEnum.ENTRUST_ROLE_NAME.getValue()))) {
                    entrustRoleName = configMap.get(ConfigKeyEnum.ENTRUST_ROLE_NAME.getValue());
                }
                if (StringUtils.isNotBlank(configMap.get(ConfigKeyEnum.OTHER_ROLE_NAME.getValue()))) {
                    otherRoleName = configMap.get(ConfigKeyEnum.OTHER_ROLE_NAME.getValue());
                }
            }
            for (GetCarrierOrderWeixinPushResponseModel getCarrierOrderWeixinPushResponseModel : getCarrierOrderWeixinPushResponseModels) {
                if (WeixinPushInfoRoleEnum.SENDER.getKey().equals(getCarrierOrderWeixinPushResponseModel.getRole())) {
                    getCarrierOrderWeixinPushResponseModel.setRoleName(senderRoleName);
                } else if (WeixinPushInfoRoleEnum.RECEIVER.getKey().equals(getCarrierOrderWeixinPushResponseModel.getRole())) {
                    getCarrierOrderWeixinPushResponseModel.setRoleName(receiverRoleName);
                } else if (WeixinPushInfoRoleEnum.ENTRUST.getKey().equals(getCarrierOrderWeixinPushResponseModel.getRole())) {
                    getCarrierOrderWeixinPushResponseModel.setRoleName(entrustRoleName);
                } else {
                    getCarrierOrderWeixinPushResponseModel.setRoleName(otherRoleName);
                }
            }
        }
        return getCarrierOrderWeixinPushResponseModels;
    }

    /**
     * 确定推送微信消息
     * @param requestModel
     */
    @Transactional
    public void confirmPushWeixin(ConfirmPushWeixinRequestModel requestModel) {
        if (ListUtils.isNotEmpty(requestModel.getPushContacts())) {
            //运单有未处理的异常工单，则不能操作
            Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(requestModel.getCarrierOrderId().toString());
            if (!workOrderMap.isEmpty()){
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
            }
            List<TCarrierOrderWx> insertList = new ArrayList<>();
            List<TCarrierOrderWx> updateList = new ArrayList<>();
            List<GetCarrierOrderWeixinPushResponseModel> getCarrierOrderWeixinPushResponseModels = tCarrierOrderWxMapper.selectCarrierOrderWeixinPushInfoByCarrierOrder(requestModel.getCarrierOrderId());
            List<Long> deleteLongList = new ArrayList<>();
            getCarrierOrderWeixinPushResponseModels.stream().forEach(tmp -> deleteLongList.add(tmp.getId()));
            for (ConfirmPushWeixinContactItemsRequestModel tmp : requestModel.getPushContacts()) {
                if (tmp.getCarrierOrderWxId() == null) {
                    TCarrierOrderWx insertOrderWx = new TCarrierOrderWx();
                    insertOrderWx.setCarrierOrderId(requestModel.getCarrierOrderId());
                    insertOrderWx.setRole(WeixinPushInfoRoleEnum.OTHER.getKey());
                    insertOrderWx.setIfPush(tmp.getIfPush());
                    insertOrderWx.setMobile(tmp.getMobile());
                    insertOrderWx.setName(tmp.getName());
                    commonBiz.setBaseEntityAdd(insertOrderWx, BaseContextHandler.getUserName());
                    insertList.add(insertOrderWx);
                } else {
                    TCarrierOrderWx upOrderWx = new TCarrierOrderWx();
                    upOrderWx.setId(tmp.getCarrierOrderWxId());
                    upOrderWx.setName(tmp.getName());
                    upOrderWx.setMobile(tmp.getMobile());
                    upOrderWx.setIfPush(tmp.getIfPush());
                    commonBiz.setBaseEntityModify(upOrderWx, BaseContextHandler.getUserName());
                    updateList.add(upOrderWx);
                    deleteLongList.remove(tmp.getCarrierOrderWxId());
                }
            }
            if (ListUtils.isNotEmpty(deleteLongList)) {
                deleteLongList.stream().forEach(tmp -> {
                    TCarrierOrderWx deleteWx = new TCarrierOrderWx();
                    deleteWx.setId(tmp);
                    deleteWx.setValid(CommonConstant.INTEGER_ZERO);
                    commonBiz.setBaseEntityModify(deleteWx, BaseContextHandler.getUserName());
                    updateList.add(deleteWx);
                });
            }
            if (ListUtils.isNotEmpty(updateList)) {
                tCarrierOrderWxMapper.batchUpdate(updateList);
            }
            if (ListUtils.isNotEmpty(insertList)) {
                tCarrierOrderWxMapper.batchInsert(insertList);
            }
        }
    }

    //创建运单事件
    public TCarrierOrderEvents getCarrierOrderEvent(Long carrierOrderId, CarrierOrderEventsTypeEnum eventTypeEnum, String userName, String remark) {
        TCarrierOrderEvents carrierOrderEvents = new TCarrierOrderEvents();
        carrierOrderEvents.setCarrierOrderId(carrierOrderId);
        carrierOrderEvents.setEvent(eventTypeEnum.getKey());
        carrierOrderEvents.setEventDesc(eventTypeEnum.getValue());
        carrierOrderEvents.setRemark(remark);
        carrierOrderEvents.setEventTime(new Date());
        carrierOrderEvents.setOperatorName(userName);
        carrierOrderEvents.setOperateTime(new Date());
        commonBiz.setBaseEntityAdd(carrierOrderEvents, userName);
        return carrierOrderEvents;
    }

    //创建运单日志
    public TCarrierOrderOperateLogs getCarrierOrderOperateLogs(Long carrierOrderId, CarrierOrderOperateLogsTypeEnum logTypeEnum, String userName, String remark) {
        TCarrierOrderOperateLogs carrierOrderOperateLogs = new TCarrierOrderOperateLogs();
        carrierOrderOperateLogs.setCarrierOrderId(carrierOrderId);
        carrierOrderOperateLogs.setOperationType(logTypeEnum.getKey());
        carrierOrderOperateLogs.setOperationContent(logTypeEnum.getValue());
        carrierOrderOperateLogs.setRemark(remark);
        carrierOrderOperateLogs.setOperatorName(userName);
        carrierOrderOperateLogs.setOperateTime(new Date());
        commonBiz.setBaseEntityAdd(carrierOrderOperateLogs, userName);
        return carrierOrderOperateLogs;
    }


    /**
     * 修改车辆
     *
     * @param requestModel
     * @return
     */
    @Transactional
    @DistributedLock(prefix = CommonConstant.TMS_ADD_MODIFY_DRIVER_LOCK,
            keys = "#requestModel.carrierOrderId",
            waitTime = 3)
    public void modifyVehicle(ModifyVehicleRequestModel requestModel) {
        //查询运单信息
        TCarrierOrder carrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (carrierOrder == null || carrierOrder.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //已取消不能操作
        if (CommonConstant.INTEGER_ONE.equals(carrierOrder.getIfCancel())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL);
        }
        //运单放空不能操作
        if (CommonConstant.INTEGER_ONE.equals(carrierOrder.getIfEmpty())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EMPTY);
        }
        //运单卸货后不能修改车辆
        if (carrierOrder.getStatus() > CarrierOrderStatusEnum.WAIT_UNLOAD.getKey()) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_UNLOAD);
        }
        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(carrierOrder.getId().toString());
        if (!workOrderMap.isEmpty()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }

        Long companyCarrierId = CommonConstant.LONG_ZERO;//车主ID
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            //如果是前台请求,则获取当前登录的车主
            companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (companyCarrierId == null || CommonConstant.LONG_ZERO.equals(companyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }
        } else if (CommonConstant.INTEGER_ONE.equals(requestModel.getSource())) {
            //如果是后台请求,使用运单上面的车主id
            companyCarrierId = carrierOrder.getCompanyCarrierId();
        }

        //查询车主信息
        FuzzySearchCompanyCarrierResponseModel companyCarrierInfo = tCompanyCarrierMapper.getCompanyCarrierInfoById(companyCarrierId);

        //查询运单车辆
        DriverIdVehicleIdRelationIdModel driverIdVehicleIdRelationIdModel = new DriverIdVehicleIdRelationIdModel();
        driverIdVehicleIdRelationIdModel.setCarrierOrderId(requestModel.getCarrierOrderId());
        TCarrierOrderVehicleHistory tCarrierOrderVehicleHistory = carrierOrderVehicleHistoryMapper.getTopByCarrierOrderId(driverIdVehicleIdRelationIdModel);

        if (tCarrierOrderVehicleHistory.getIfInvalid().equals(IfValidEnum.INVALID.getKey()) && tCarrierOrderVehicleHistory.getAuditStatus().equals(AuditStatusEnum.WAIT_AUDIT.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_VEHICLE_WAIT_AUDIT);
        }
        DriverAndVehicleResponseModel driverAndVehicleResponseModel = carrierOrderVehicleHistoryMapper.getDriverVehicleInfoByCarrierOrderId(requestModel.getCarrierOrderId());
        //修改前后是否一致
        if (driverAndVehicleResponseModel.getDriverPhone().equals(requestModel.getDriverMobile())
                && driverAndVehicleResponseModel.getVehicleNo().equals(requestModel.getVehicleNo())) {
            //挂车校验
            if (requestModel.getTrailerVehicleId() != null
                    || (driverAndVehicleResponseModel.getTrailerVehicleId() != null && driverAndVehicleResponseModel.getTrailerVehicleId() > CommonConstant.INTEGER_ZERO)) {
                if (driverAndVehicleResponseModel.getTrailerVehicleId().equals(requestModel.getTrailerVehicleId())) {
                    throw new BizException(CarrierDataExceptionEnum.VEHICLE_INFO_SAME_CANT_MODIFY);
                }
            } else {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_INFO_SAME_CANT_MODIFY);
            }
        }

        if (!driverAndVehicleResponseModel.getVehicleNo().equals(requestModel.getVehicleNo())) {
            List<FuzzyQueryVehicleInfoResponseModel> dbTVehicleBasicList = tVehicleBasicMapper.queryVehicleInfoByVehicleNos(LocalStringUtil.listTostring(Arrays.asList(driverAndVehicleResponseModel.getVehicleNo(), requestModel.getVehicleNo()), ','));
            if (ListUtils.isEmpty(dbTVehicleBasicList)) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
            }

            Map<String, Integer> vehiclePropertyMap = new HashMap<>();
            dbTVehicleBasicList.forEach(temp -> vehiclePropertyMap.put(temp.getVehicleNo(), temp.getVehicleProperty()));

            //存在内部车辆,是否有月结算账单
            if (((VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehiclePropertyMap.get(driverAndVehicleResponseModel.getVehicleNo())) || VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehiclePropertyMap.get(driverAndVehicleResponseModel.getVehicleNo())))
                    || (VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehiclePropertyMap.get(requestModel.getVehicleNo())) || VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehiclePropertyMap.get(requestModel.getVehicleNo()))))
                    && carrierOrderCommonBiz.existSettlementData(ConverterUtils.toString(carrierOrder.getId()))) {
                throw new BizException(CarrierDataExceptionEnum.BILL_HAS_BEEN_GENERATED_CANNOT_OPERATION);
            }

            //非本月调度的外部车辆的运单,在当前月不能修改车辆成为内部车辆
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date cunMothDate = calendar.getTime();
            if (VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(vehiclePropertyMap.get(driverAndVehicleResponseModel.getVehicleNo()))
                    && (VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehiclePropertyMap.get(requestModel.getVehicleNo())) || VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehiclePropertyMap.get(requestModel.getVehicleNo())))
                    && carrierOrder.getDispatchTime().before(cunMothDate)) {
                throw new BizException(CarrierDataExceptionEnum.LAST_MONTH_OWN_VEHICLE_STATEMENT_HAS_BEEN_GENERATED);
            }
        }

        //判断车辆司机是否存在
        List<VehicleAssetManagementListResponseModel> vehicleBasicInfoList = tVehicleBasicMapper.getVehicleBasicByVehicleNos(LocalStringUtil.listTostring(Collections.singletonList(requestModel.getVehicleNo()), ','));
        if (ListUtils.isEmpty(vehicleBasicInfoList)) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }
        VehicleAssetManagementListResponseModel vehicleBasicInfo = vehicleBasicInfoList.get(CommonConstant.INTEGER_ZERO);

        if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(companyCarrierInfo.getIsOurCompany())) {
            //我司车辆,校验调度的车辆是否已经停运
            if (!OperatingStateEnum.IN_OPERATION.getKey().equals(vehicleBasicInfo.getOperatingState())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_OUT_OF_SERVICE);
            }
        }

        //查询货主信息
        TCompanyEntrust tCompanyEntrust = tCompanyEntrustMapper.selectByPrimaryKey(carrierOrder.getCompanyEntrustId());
        if (tCompanyEntrust == null) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_ENTRUST_EMPTY);
        }

        //判断车辆司机是否存在
        TVehicleBasic vehicleBasic = tVehicleBasicMapper.getInfoByVehicleNo(requestModel.getVehicleNo());
        if (vehicleBasic==null){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }
        TStaffBasic staffBasic = tStaffBasicMapper.getByMobile(requestModel.getDriverMobile());

        if (staffBasic == null) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_NOT_EXIST);
        }
        if (!StaffTypeEnum.DRIVER.getKey().equals(staffBasic.getType())
                && !StaffTypeEnum.DRIVER_SUPERCARGO.getKey().equals(staffBasic.getType())){
            throw new BizException(CarrierDataExceptionEnum.ONLY_DRIVERS_OF_THE_DRIVER_TYPE_CAN_BE_DISPATCHED);
        }

        //判断车主和车辆关系
        TCarrierVehicleRelation vehicleRelation = tCarrierVehicleRelationMapper.getByCompanyCarrierIdAndVehicleId(companyCarrierId, vehicleBasic.getId());
        if (vehicleRelation == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_CARRIER_REL_EMPTY);
        }
        //判断车主和司机关系
        TCarrierDriverRelation driverRelation = tCarrierDriverRelationMapper.getByCompanyCarrierIdAndDriverId(companyCarrierId, staffBasic.getId(), EnabledEnum.ENABLED.getKey());
        if (driverRelation == null) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_CARRIER_REL_EMPTY);
        }

        //如果选择了挂车
        VehicleBasicPropertyModel trailerVehicleInfo = null;
        if (requestModel.getTrailerVehicleId() != null) {
            trailerVehicleInfo = tVehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getTrailerVehicleId());
            if (trailerVehicleInfo == null || !VehicleCategoryEnum.TRAILER.getKey().equals(trailerVehicleInfo.getVehicleCategory())) {
                throw new BizException(CarrierDataExceptionEnum.TRACTOR_VEHICLE_NOT_EXIST);
            }

            //判断车主和挂车车辆关系
            TCarrierVehicleRelation trailerVehicleRelation = tCarrierVehicleRelationMapper.getByCompanyCarrierIdAndVehicleId(companyCarrierId, trailerVehicleInfo.getVehicleId());
            if (trailerVehicleRelation == null) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_CARRIER_REL_EMPTY);
            }
        }

        //云盘我司运单车辆司机判断
        if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(carrierOrder.getDemandOrderSource()) && IsOurCompanyEnum.OUR_COMPANY.getKey().equals(companyCarrierInfo.getIsOurCompany())) {
            //车辆为自营自主非挂车
            if (VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(vehicleBasicInfo.getVehicleProperty()) ||
                    VehicleCategoryEnum.TRAILER.getKey().equals(vehicleBasicInfo.getVehicleCategory())) {
                throw new BizException(CarrierDataExceptionEnum.DISPATCH_VEHICLE_ERROR);
            }

            //司机自营自主启用
            if (StaffPropertyEnum.EXTERNAL_STAFF.getKey().equals(staffBasic.getStaffProperty()) ||
                    EnabledEnum.DISABLED.getKey().equals(driverRelation.getEnabled())) {
                throw new BizException(CarrierDataExceptionEnum.DISPATCH_DRIVER_ERROR);
            }
        }

        Long driverId = staffBasic.getId();//司机id
        Long vehicleId = vehicleBasic.getId();//车辆id

        //添加gps信息
        TVehicleBasicModel newGpsVehicle = new TVehicleBasicModel();
        newGpsVehicle.setVehicleBasicId(vehicleId);
        newGpsVehicle.setVehicleNo(requestModel.getVehicleNo());

        TStaffBasicModel carrierDriver = new TStaffBasicModel();
        carrierDriver.setMobile(requestModel.getDriverMobile());
        carrierDriver.setIdentityNumber(requestModel.getDriverIdentity());
        carrierDriver.setName(requestModel.getDriverName());
        carrierDriver.setStaffBasicId(driverId);
        staffVehicleBiz.addVehicleGpsInfo(newGpsVehicle, carrierDriver);

        Integer ifInvalid;
        Integer auditStatus;
        CarrierOrderEventsTypeEnum carrierOrderEventsTypeEnum;
        List<CarrierOrderSynchronizeModel> synchronizeModels = new ArrayList<>();
        List<CarrierOrderSynchronizeModel> synchronizeToLeYiModels = new ArrayList<>();
        UpdateLifeCarrierOrderVehicleMessage updateLifeCarrierOrderVehicleMessage = null;//新生
        if (CommonConstant.INTEGER_ONE.equals(tCompanyEntrust.getIfAudit())) {
            ifInvalid = IfValidEnum.INVALID.getKey();
            auditStatus = AuditStatusEnum.WAIT_AUDIT.getKey();
            carrierOrderEventsTypeEnum = CarrierOrderEventsTypeEnum.UPDATE_VEHICLE_WAIT_AUDIT;
        } else {//无须审核
            ifInvalid = IfValidEnum.VALID.getKey();
            auditStatus = AuditStatusEnum.NOT_NEED_AUDIT.getKey();
            carrierOrderEventsTypeEnum = CarrierOrderEventsTypeEnum.UPDATE_VEHICLE;

            //同步运单信息：货主修改车辆无需审核时，则需将修改后的车辆司机信息同步给托盘
            if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(carrierOrder.getDemandOrderSource())) {
                CarrierOrderSynchronizeModel syncModel = new CarrierOrderSynchronizeModel();
                syncModel.setType(carrierOrder.getDemandOrderEntrustType());
                syncModel.setCarrierOrderCode(carrierOrder.getCarrierOrderCode());
                syncModel.setVehicleNumber(requestModel.getVehicleNo());
                syncModel.setDriverMobilePhone(requestModel.getDriverMobile());
                syncModel.setIdentityCardNumber(requestModel.getDriverIdentity());
                syncModel.setDriverName(requestModel.getDriverName());
                syncModel.setUserName(BaseContextHandler.getUserName());
                syncModel.setExpectArrivalTime(requestModel.getExpectArrivalTime());
                synchronizeModels.add(syncModel);

                //回收入库、回收出库、预约类型的
                if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(carrierOrder.getDemandOrderEntrustType())
                        || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(carrierOrder.getDemandOrderEntrustType())
                        || EntrustTypeEnum.BOOKING.getKey().equals(carrierOrder.getDemandOrderEntrustType())
                        || EntrustTypeEnum.DELIVER.getKey().equals(carrierOrder.getDemandOrderEntrustType())
                        || EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey().equals(carrierOrder.getDemandOrderEntrustType()))   {
                    if (EntrustTypeEnum.DELIVER.getKey().equals(carrierOrder.getDemandOrderEntrustType())
                            || EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey().equals(carrierOrder.getDemandOrderEntrustType())){
                        syncModel.setSendOtherOutCompanyFlag(1);
                    }
                    synchronizeToLeYiModels.add(syncModel);
                }
            }else if (DemandOrderSourceEnum.YELO_LIFE.getKey().equals(carrierOrder.getDemandOrderSource())) {
                //新生运单修改车辆司机同步新生
                updateLifeCarrierOrderVehicleMessage = new UpdateLifeCarrierOrderVehicleMessage();
                updateLifeCarrierOrderVehicleMessage.setCarrierOrderCode(carrierOrder.getCarrierOrderCode());
                updateLifeCarrierOrderVehicleMessage.setCustomerOrderCode(carrierOrder.getCustomerOrderCode());
                updateLifeCarrierOrderVehicleMessage.setVehicleNumber(requestModel.getVehicleNo());
                updateLifeCarrierOrderVehicleMessage.setDriverName(requestModel.getDriverName());
                updateLifeCarrierOrderVehicleMessage.setDriverMobilePhone(requestModel.getDriverMobile());
                updateLifeCarrierOrderVehicleMessage.setUserName(BaseContextHandler.getUserName());
            }
        }

        //修改原车辆关联关系
        TCarrierOrderVehicleHistory carrierOrderVehicleHistory = new TCarrierOrderVehicleHistory();
        carrierOrderVehicleHistory.setId(driverAndVehicleResponseModel.getVehicleHistoryId());
        carrierOrderVehicleHistory.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityModify(carrierOrderVehicleHistory, BaseContextHandler.getUserName());
        carrierOrderVehicleHistory.setIfInvalid(CommonConstant.INTEGER_ZERO.equals(tCompanyEntrust.getIfAudit()) ? IfValidEnum.INVALID.getKey() : null);
        carrierOrderVehicleHistoryMapper.updateByPrimaryKeySelective(carrierOrderVehicleHistory);

        //新增修改运单车辆记录
        carrierOrderVehicleHistory = new TCarrierOrderVehicleHistory();
        carrierOrderVehicleHistory.setCarrierOrderId(requestModel.getCarrierOrderId());
        carrierOrderVehicleHistory.setVehicleId(vehicleId);
        carrierOrderVehicleHistory.setVehicleNo(requestModel.getVehicleNo());
        carrierOrderVehicleHistory.setDriverId(driverId);
        carrierOrderVehicleHistory.setDriverName(requestModel.getDriverName());
        carrierOrderVehicleHistory.setDriverMobile(requestModel.getDriverMobile());
        carrierOrderVehicleHistory.setDriverIdentity(requestModel.getDriverIdentity());
        //挂车存在
        if (trailerVehicleInfo != null) {
            carrierOrderVehicleHistory.setTrailerVehicleId(trailerVehicleInfo.getVehicleId());
            carrierOrderVehicleHistory.setTrailerVehicleNo(trailerVehicleInfo.getVehicleNo());
        }else{
            carrierOrderVehicleHistory.setTrailerVehicleId(CommonConstant.LONG_ZERO);
            carrierOrderVehicleHistory.setTrailerVehicleNo(CommonConstant.EMPTY_STRING);
        }
        if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(carrierOrder.getDemandOrderEntrustType())
                || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(carrierOrder.getDemandOrderEntrustType())){
            carrierOrderVehicleHistory.setExpectLoadTime(requestModel.getExpectArrivalTime());
        }else {
            carrierOrderVehicleHistory.setExpectArrivalTime(requestModel.getExpectArrivalTime());
        }
        carrierOrderVehicleHistory.setIfInvalid(ifInvalid);
        carrierOrderVehicleHistory.setAuditStatus(auditStatus);
        carrierOrderVehicleHistory.setVehicleHistoryId(driverAndVehicleResponseModel.getVehicleHistoryId());
        commonBiz.setBaseEntityAdd(carrierOrderVehicleHistory, BaseContextHandler.getUserName());
        carrierOrderVehicleHistoryMapper.insertSelective(carrierOrderVehicleHistory);

        //修改车辆记录日志
        String remark = requestModel.getRemark() + String.format(CarrierOrderOperateLogsTypeEnum.UPDATE_VEHICLE.getFormat(), requestModel.getVehicleNo(), requestModel.getDriverName() + " " + requestModel.getDriverMobile());
        TCarrierOrderOperateLogs carrierOrderOperateLogs = carrierOrderCommonBiz.getCarrierOrderOperateLogs(requestModel.getCarrierOrderId(), CarrierOrderOperateLogsTypeEnum.UPDATE_VEHICLE, BaseContextHandler.getUserName(), remark);
        carrierOrderOperateLogsMapper.insertSelective(carrierOrderOperateLogs);

        //修改车辆记录事件
        TCarrierOrderEvents carrierOrderEvents = carrierOrderCommonBiz.getCarrierOrderEvent(requestModel.getCarrierOrderId(), carrierOrderEventsTypeEnum, BaseContextHandler.getUserName(), "");
        tCarrierOrderEventsMapper.insertSelective(carrierOrderEvents);

        //修改车辆记录操作日志
        TOperateLogs operateLogs = new TOperateLogs();
        operateLogs.setObjectType(OperateLogsOperateTypeEnum.AUDIT_THROUGH_UPDATE.getObjectType().getKey());
        operateLogs.setObjectId(carrierOrderVehicleHistory.getId());
        operateLogs.setOperateType(OperateLogsOperateTypeEnum.AUDIT_THROUGH_UPDATE.getOperateType());
        operateLogs.setOperateContents(OperateLogsOperateTypeEnum.AUDIT_THROUGH_UPDATE.getOperateContents());
        operateLogs.setOperateUserName(BaseContextHandler.getUserName());
        operateLogs.setOperateTime(new Date());
        operateLogs.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityAdd(operateLogs, BaseContextHandler.getUserName());
        operateLogsMapper.insertSelective(operateLogs);

        //修改运单表是否存在待审核车辆字段
        if (AuditStatusEnum.WAIT_AUDIT.getKey().equals(auditStatus)) {//需要审核
            TCarrierOrder upCarrierOrder = new TCarrierOrder();
            upCarrierOrder.setId(carrierOrder.getId());
            upCarrierOrder.setIfWaitAuditVehicle(CommonConstant.INTEGER_ONE);
            commonBiz.setBaseEntityModify(upCarrierOrder, BaseContextHandler.getUserName());
            tCarrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(upCarrierOrder);
        }

        //新生运单-修改车辆司机同步新生
        if (updateLifeCarrierOrderVehicleMessage != null) {
            log.info("运单修改车辆无需审核时同步新生：" + updateLifeCarrierOrderVehicleMessage);
            SyncCarrierOrderToYeloLifeModel<Object> syncCarrierOrderToYeloLifeModel = new SyncCarrierOrderToYeloLifeModel<>();
            syncCarrierOrderToYeloLifeModel.setType(SyncCarrierOrderToYeloLifeModel.SyncTypeEnum.UPDATE_VEHICLE.getKey());
            syncCarrierOrderToYeloLifeModel.setMsgData(Collections.singletonList(updateLifeCarrierOrderVehicleMessage));
            rabbitMqPublishBiz.syncCarrierOrderToYeloLife(syncCarrierOrderToYeloLifeModel);
        }

        //云盘非回收、非预约类型运单-同步数据给云仓
        if (ListUtils.isNotEmpty(synchronizeModels)) {
            log.info("运单修改车辆无需审核时同步云仓：" + synchronizeModels);
            CarrierOrderSynchronizeLeyiModel carrierOrderSynchronizeLeyiModel = new CarrierOrderSynchronizeLeyiModel();
            carrierOrderSynchronizeLeyiModel.setCarrierOrderSynchronizeModels(synchronizeModels);
            rabbitMqPublishBiz.carrierOrderSynchronizeToWarehouse(carrierOrderSynchronizeLeyiModel);
        }

        //云盘回收、预约类型运单-同步数据给云盘
        if (ListUtils.isNotEmpty(synchronizeToLeYiModels)) {
            log.info("运单修改车辆无需审核时同步云盘：" + synchronizeToLeYiModels);
            CarrierOrderSynchronizeLeyiModel carrierOrderSynchronizeLeyiModel = new CarrierOrderSynchronizeLeyiModel();
            carrierOrderSynchronizeLeyiModel.setCarrierOrderSynchronizeModels(synchronizeToLeYiModels);
            rabbitMqPublishBiz.carrierOrderSynchronizeToLeyi(carrierOrderSynchronizeLeyiModel);
        }

        //预约单解除关联运单
        reservationOrderCommonBiz.disassociateCarrierOrder(new ArrayList<>(){{add(requestModel.getCarrierOrderId());}});


        // 发布智能推送运单修改车辆节点事件
        applicationContext.publishEvent(new WorkGroupEventModel()
                .setWorkGroupPushBoModels(carrierOrder.getId(),
                        carrierOrder.getDemandOrderSource(),
                        carrierOrder.getDemandOrderEntrustType(),
                        carrierOrder.getProjectLabel(),
                        WorkGroupOrderTypeEnum.CARRIER_ORDER,
                        WorkGroupOrderNodeEnum.CARRIER_ORDER_UPDATE_VEHICLE));
    }

    /**
     * 确定推送微信消息
     * @param requestModel
     * @return
     */
    public List<DriverAndVehicleResponseModel> getDriverAndVehicleByVehicleNumber(WebDemandOrderDriverRequestModel requestModel) {
        List<DriverAndVehicleResponseModel> retList;

        StaffAndVehicleSearchRequestModel requestRelationModel = MapperUtils.mapper(requestModel, StaffAndVehicleSearchRequestModel.class);
        List<StaffAndVehicleSearchResponseModel> relationList = tStaffVehicleRelationMapper.getDriverAndVehicleByVehicleNumber(requestRelationModel);
        List<DriverAndVehicleResponseModel> list = new ArrayList<>();
        if (ListUtils.isEmpty(relationList)) {
            List<FuzzyQueryVehicleInfoResponseModel> vehicleList = tVehicleBasicMapper.fuzzyQueryVehicleInfoByVehicleNo(requestModel.getVehicleNo());
            for (FuzzyQueryVehicleInfoResponseModel tempVehicle : vehicleList) {
                DriverAndVehicleResponseModel model = new DriverAndVehicleResponseModel();
                model.setVehicleId(tempVehicle.getVehicleId());
                model.setVehicleNo(tempVehicle.getVehicleNo());
                list.add(model);
            }
            return list;
        }
        retList = MapperUtils.mapper(relationList, DriverAndVehicleResponseModel.class);
        return retList == null ? retList : new ArrayList<>();
    }

    /**
     * 修改司机运费（运单详情页面）
     */
    @Transactional
    public void updateDriverFreightFee(UpdateDriverFreightFeeRequestModel requestModel) {
        CarrierOrderStatusModel tCarrierOrder = tCarrierOrderMapper.getCarrierStatus(requestModel.getCarrierOrderId());
        if (tCarrierOrder == null) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //查询车主信息
        TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(tCarrierOrder.getCompanyCarrierId());
        if (tCompanyCarrier!=null && CommonConstant.INTEGER_TWO.equals(tCompanyCarrier.getLevel())){
            throw new BizException(CarrierDataExceptionEnum.UNCHANGEABLE);
        }
        //判断运单状态
        if (tCarrierOrder.getIfCancel().equals(CommonConstant.INTEGER_ONE)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL);
        } else if (tCarrierOrder.getIfEmpty().equals(CommonConstant.INTEGER_ONE)){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EMPTY);
        }else if (tCarrierOrder.getStatus() > CarrierOrderStatusEnum.WAIT_UNLOAD.getKey()) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_UNLOAD);
        }
        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(requestModel.getCarrierOrderId().toString());
        if (!workOrderMap.isEmpty()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }
        //判断运单是否生成车辆结算数量
        if(carrierOrderCommonBiz.existSettlementData(ConverterUtils.toString(requestModel.getCarrierOrderId()))){
            throw new BizException(CarrierDataExceptionEnum.BILL_HAS_BEEN_GENERATED_CANNOT_OPERATION);
        }

        //更新运单司机费用
        TCarrierOrder carrierOrder = new TCarrierOrder();
        carrierOrder.setId(requestModel.getCarrierOrderId());
        carrierOrder.setDispatchFreightFee(requestModel.getDriverFreightFee());
        commonBiz.setBaseEntityModify(carrierOrder, BaseContextHandler.getUserName());
        tCarrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(carrierOrder);

        //记录操作日志
        String unit = "";
        if (FreightTypeEnum.UNIT_PRICE.getKey().equals(tCarrierOrder.getDispatchFreightFeeType())) {
            unit = GoodsUnitEnum.getEnum(tCarrierOrder.getGoodsUnit()).getPriceUnit();
        } else if (FreightTypeEnum.FIXED_PRICE.getKey().equals(tCarrierOrder.getDispatchFreightFeeType())) {
            unit = CommonConstant.YUAN;
        }
        String remark = CarrierOrderOperateLogsTypeEnum.UPDATE_DRIVER_FREIGHT_FEE.format(tCarrierOrder.getDispatchFreightFee() + unit, requestModel.getDriverFreightFee() + unit);
        TCarrierOrderOperateLogs carrierOrderOperateLogs = carrierOrderCommonBiz.getCarrierOrderOperateLogs(requestModel.getCarrierOrderId(), CarrierOrderOperateLogsTypeEnum.UPDATE_DRIVER_FREIGHT_FEE, BaseContextHandler.getUserName(), remark);
        if (carrierOrderOperateLogs != null) {
            carrierOrderOperateLogsMapper.insertSelective(carrierOrderOperateLogs);
        }
    }

    /**
     * 根据运单id查询司机车辆信息（后台修改车辆详情）
     *
     * @param requestModel
     * @return
     */
    public DriverAndVehicleResponseModel getDriverVehicleInfoByCarrierOrderId(CarrierOrderDetailRequestModel requestModel) {
        TCarrierOrder carrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (carrierOrder == null || carrierOrder.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(requestModel.getCarrierOrderId().toString());
        if (!workOrderMap.isEmpty()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }

        DriverAndVehicleResponseModel driverVehicleInfoByCarrierOrderId = carrierOrderVehicleHistoryMapper.getDriverVehicleInfoByCarrierOrderId(requestModel.getCarrierOrderId());

        //查询当前运单的车主是否我司
        TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(carrierOrder.getCompanyCarrierId());
        if (tCompanyCarrier != null && IfValidEnum.VALID.getKey().equals(tCompanyCarrier.getValid())) {
            driverVehicleInfoByCarrierOrderId.setIsOurCompany(tCompanyCarrier.getLevel());
        }
        driverVehicleInfoByCarrierOrderId.setCompanyCarrierId(carrierOrder.getCompanyCarrierId());
        return driverVehicleInfoByCarrierOrderId;
    }

    /**
     * 提货
     *
     * @param requestModel
     */
    @Transactional
    public Result load(CarrierOrderLoadRequestModel requestModel) {
        List<Long> carrierOrderIdList = new ArrayList<>();
        Map<Long, BigDecimal> pageLoadAmountMap = new HashMap<>();
        Map<Long, BigDecimal> pageLoadGoodsAmountMap = new HashMap<>();
        Map<Long, LoadRequestModel> pageLoadRequestMap = new HashMap<>();
        Map<Long,List<LoadGoodsForYeloLifeRequestCodeModel>> goodCodeMap = new HashMap<>();
        List<String> code = new ArrayList<>();
        //校验入参数量，并统计提货数量
        for (LoadRequestModel item : requestModel.getLoadCarrierOrderList()) {
            if (item.getCarrierOrderId() == null || ListUtils.isEmpty(item.getGoodsList())) {
                throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
            }

            BigDecimal pageLoadAmount = BigDecimal.ZERO;
            for (CarrierOrderNodeGoodsRequestModel itemGoods : item.getGoodsList()) {
                BigDecimal tempGoodsCount = Optional.ofNullable(itemGoods.getCount()).orElse(BigDecimal.ZERO);
                //不同的单位对数量最小值最大值校验
                if (GoodsUnitEnum.BY_WEIGHT.getKey().equals(item.getGoodsUnit())){
                    if (!RegExpValidatorUtil.isFloatNumber(ConverterUtils.toString(tempGoodsCount))
                            || tempGoodsCount.compareTo(BigDecimal.ZERO) < CommonConstant.INTEGER_ZERO
                            || tempGoodsCount.compareTo(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_THOUSAND) > CommonConstant.INTEGER_ZERO){
                        throw new BizException(CarrierDataExceptionEnum.TOTAL_EXPECT_AMOUNT_NOT_WEIGHT);
                    }
                }else{
                    if (!RegExpValidatorUtil.isNumber(ConverterUtils.toString(tempGoodsCount))
                            || tempGoodsCount.compareTo(BigDecimal.ZERO) < CommonConstant.INTEGER_ZERO
                            || tempGoodsCount.compareTo(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_THOUSAND) > CommonConstant.INTEGER_ZERO){
                        throw new BizException(CarrierDataExceptionEnum.TOTAL_EXPECT_AMOUNT_NOT_NUMBER);
                    }
                }
                pageLoadGoodsAmountMap.put(itemGoods.getGoodsId(), itemGoods.getCount());
                pageLoadAmount = pageLoadAmount.add(itemGoods.getCount());
            }
            item.getGoodsList().forEach(e->{
                if (ListUtils.isNotEmpty(e.getCodeDtoList())){
                    goodCodeMap.put(e.getGoodsId(),e.getCodeDtoList());
                    code.addAll(e.getCodeDtoList().stream().map(LoadGoodsForYeloLifeRequestCodeModel::getYeloCode).collect(Collectors.toList()));
                }
            });
            //总量校验： 0<提货数量<5000
            if (pageLoadAmount.compareTo(BigDecimal.ZERO) <= CommonConstant.INTEGER_ZERO){
                throw new BizException(CarrierDataExceptionEnum.TOTAL_EXPECT_LOAD_AMOUNT_ERROR);
            }
            carrierOrderIdList.add(item.getCarrierOrderId());
            pageLoadAmountMap.put(item.getCarrierOrderId(), pageLoadAmount);
            pageLoadRequestMap.put(item.getCarrierOrderId(), item);
        }
        if (CollectionUtil.isNotEmpty(code)){
            if (code.size() != new HashSet<>(code).size()) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_GOODS_CODE_CANNOT_DEP);
            }
            Result<List<String>> listResult = yeloLifeBasicDataServiceApi.verifyGoodsCodeList(code);
            listResult.throwException();
            List<String> codes = listResult.getData();
            if (code.size() != codes.size()) {
                code.removeAll(codes);
                return new Result(CommonConstant.CODE_NOT_EXIST,code,CarrierDataExceptionEnum.CARRIER_ORDER_GOODS_CODE_NOT_STANDARD.getMsg());
            }
        }
        String carrierOrderIds = StringUtils.listToString(carrierOrderIdList, ',');
        //做校验用
        List<CarrierOrderListBeforeSignUpResponseModel> carrierOrderList = tCarrierOrderMapper.selectCarrierOrderSignDetail(carrierOrderIds, null);
        if (carrierOrderList.size() != carrierOrderIdList.size()) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }



        //判断前台请求 运单所属
        boolean isWebRequest = false;
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            CarrierOrderListBeforeSignUpResponseModel tCarrierOrder = carrierOrderList.get(CommonConstant.INTEGER_ZERO);
            carrierOrderCommonBiz.checkCarrierOrderCompanyCarrierForWeb(tCarrierOrder.getCompanyCarrierId());
            isWebRequest = true;
        }

        carrierOrderCommonBiz.checkIfCarrierOrdersStatusCanChange(carrierOrderIdList, CarrierDataExceptionEnum.VEHICLE_NEED_AUDIT_TIP2);
        for (CarrierOrderListBeforeSignUpResponseModel order : carrierOrderList) {
            //不是待提货状态不能操作
            if (!CarrierOrderStatusEnum.WAIT_LOAD.getKey().equals(order.getStatus())) {
                throw new BizException(CarrierDataExceptionEnum.ONLY_WAIT_LOAD);
            }
            //已取消不能操作
            if (CommonConstant.INTEGER_ONE.equals(order.getIfCancel())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL);
            }
            //运单放空不能操作
            if (CommonConstant.INTEGER_ONE.equals(order.getIfEmpty())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EMPTY);
            }

            BigDecimal loadAmount = pageLoadAmountMap.get(order.getCarrierOrderId());

            //回收出库类型，实体数不能大于预提数
            if (EntrustTypeEnum.RECYCLE_OUT.getKey().equals(order.getEntrustType())){
                if (loadAmount.compareTo(order.getExpectAmount()) > CommonConstant.INTEGER_ZERO){
                    throw new BizException(CarrierDataExceptionEnum.RECYCLE_OUT_LOAD_AMOUNT_MAX);
                }
            }
            //非回收类型：运单提货数量有数值不能操作（云仓已出库会回填提货数量）
            if(!EntrustTypeEnum.RECYCLE_IN.getKey().equals(order.getEntrustType()) && !EntrustTypeEnum.RECYCLE_OUT.getKey().equals(order.getEntrustType())) {
                boolean amountLoadFlag = BigDecimal.ZERO.compareTo(order.getLoadAmount()) != CommonConstant.INTEGER_ZERO && order.getLoadAmount().compareTo(loadAmount) != CommonConstant.INTEGER_ZERO;
                if (amountLoadFlag) {
                    StringBuilder loadAmountNumber = new StringBuilder();
                    loadAmountNumber.append(order.getLoadAmount().stripTrailingZeros().toPlainString()).append(GoodsUnitEnum.getEnum(order.getGoodsUnit()).getUnit());
                    throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_LOAD_AMOUNT_ERROR.getCode(), MessageFormat.format(CarrierDataExceptionEnum.CARRIER_ORDER_LOAD_AMOUNT_ERROR.getMsg(), order.getCarrierOrderCode(), loadAmountNumber));
                }
            }

            //发货、调拨、退货、供应商直配、采购、退货仓库配送、退货调拨、新生销售
            List<Integer> entrustTypeListModel = Arrays.asList(EntrustTypeEnum.DELIVER.getKey(), EntrustTypeEnum.TRANSFERS.getKey(),
                    EntrustTypeEnum.RETURN_GOODS.getKey(), EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey(), EntrustTypeEnum.PROCUREMENT.getKey(),
                    EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey(), EntrustTypeEnum.RETURN_GOODS_TRANSFERS.getKey(), EntrustTypeEnum.LIFE_SALE.getKey());
            if (entrustTypeListModel.contains(order.getEntrustType())) {
                //货物完成出库才能提货
                if (!CarrierOrderOutStatusEnum.FINISH_OUT.getKey().equals(order.getOutStatus())) {
                    throw new BizException(CarrierDataExceptionEnum.NOT_ALLOWED_PICKUP);
                }

                //每个货物对应的数量必须跟出库数量一致
                for (CarrierOrderListBeforeSignUpGoodsModel good : order.getGoodsInfo()) {
                    if (good.getLoadAmount().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO
                            && good.getLoadAmount().compareTo(pageLoadGoodsAmountMap.get(good.getGoodsId())) != CommonConstant.INTEGER_ZERO){
                        throw new BizException(CarrierDataExceptionEnum.LOAD_AMOUNT_ERROR_FOR_LE_YI);
                    }
                }
            }
        }

        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(carrierOrderIds);
        if (!workOrderMap.isEmpty()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }

        //运单货物、销售单查询分组
        Map<Long, List<TCarrierOrderGoods>> dbTCarrierOrderGoodsMap = new HashMap<>();
        List<TCarrierOrderGoods> dbTCarrierOrderGoods = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(carrierOrderIds);
        if (ListUtils.isNotEmpty(dbTCarrierOrderGoods)) {
            dbTCarrierOrderGoodsMap = dbTCarrierOrderGoods.stream().collect(Collectors.groupingBy(TCarrierOrderGoods::getCarrierOrderId, Collectors.toList()));
        }

        Date now = new Date();
        TCarrierOrder upOrder;
        List<TCarrierOrder> upCarrierOrders = new ArrayList<>();
        TCarrierOrderTickets addTickets;
        List<TCarrierOrderTickets> addTicketsList = new ArrayList<>();
        TCarrierOrderGoods upOrderGood;
        List<TCarrierOrderGoods> upCarrierOrderGoods = new ArrayList<>();
        List<TCarrierOrderEvents> insertEvents = new ArrayList<>();
        List<TCarrierOrderOperateLogs> insertLogs = new ArrayList<>();
        Map<String,List<String>> outStockCarrierOrderTicketsMap=new HashMap<>();
        List<WorkGroupPushBoModel> workGroupPushBoModels = Lists.newArrayList();
        List<TCarrierOrderGoodsCode> insertGoodsCodeList = new ArrayList<>();

        String unit;
        LoadRequestModel loadRequestModel;
        List<TCarrierOrder> dbTCarrierOrderList = tCarrierOrderMapper.selectCarrierOrdersByIds(carrierOrderIds);
        for (TCarrierOrder order : dbTCarrierOrderList) {
            loadRequestModel = pageLoadRequestMap.get(order.getId());
            upOrder = new TCarrierOrder();
            upOrder.setId(order.getId());
            upOrder.setStatus(CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey());
            upOrder.setStatusUpdateTime(now);
            upOrder.setLoadAmount(pageLoadAmountMap.get(order.getId()));
            upOrder.setLoadAmountExpect(pageLoadAmountMap.get(order.getId()));
            upOrder.setLoadTime(isWebRequest ? new Date() : loadRequestModel.getLoadTime());
            if(StringUtils.isNotBlank(loadRequestModel.getRemark())){
                upOrder.setRemark(CommonConstant.LOAD_REMARK_PREFIX + loadRequestModel.getRemark() + order.getRemark());
            }
            upOrder.setDeliveryMethod(CarrierOrderDeliverMethodEnum.BILL_PICKUP.getKey());
            commonBiz.setBaseEntityModify(upOrder, BaseContextHandler.getUserName());

            order.setLoadAmount(upOrder.getLoadAmount());//托盘逻辑
            order.setLoadAmountExpect(upOrder.getLoadAmountExpect());
            order.setLoadTime(upOrder.getLoadTime());
            order.setRemark(upOrder.getRemark());

            if (ListUtils.isNotEmpty(loadRequestModel.getTmpUrl())) {
                int ticketCount = loadRequestModel.getTmpUrl().size();
                List<GetTicketsResponseModel> existTicketsCount = tCarrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(order.getId(), CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getKey());
                if (ticketCount + existTicketsCount.size() > CommonConstant.INTEGER_SIX) {
                    throw new BizException(CarrierDataExceptionEnum.PICTURES_OVER_COUNT);
                }
                List<String> outStockTickets = new ArrayList<>();
                for (String imageUrl : loadRequestModel.getTmpUrl()) {
                    addTickets = new TCarrierOrderTickets();
                    addTickets.setCarrierOrderId(order.getId());
                    addTickets.setImageName(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getValue());
                    addTickets.setImagePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getKey(), order.getCarrierOrderCode(), imageUrl, null));
                    addTickets.setImageType(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getKey());
                    addTickets.setUploadTime(now);
                    addTickets.setUploadUserName(BaseContextHandler.getUserName());
                    commonBiz.setBaseEntityAdd(addTickets, BaseContextHandler.getUserName());
                    addTicketsList.add(addTickets);

                    outStockTickets.add(addTickets.getImagePath());
                }

                if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(order.getDemandOrderEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(order.getDemandOrderEntrustType())) {
                    outStockCarrierOrderTicketsMap.put(order.getCarrierOrderCode(), outStockTickets);
                }
            }

            //更新货物表信息，并计算同步到entrust的总提货数(货物提货数大于预提数 取 预提数)
            // 特殊逻辑 如果是新生回收的话，并且是按编码回收，那么需要下落编码code表和 good做关联，并且good的所有数量都是code表的综合 ----jiang 2.44
            boolean ifYeloLifeRecycleAndByCode = order.getIfRecycleByCode().equals(CommonConstant.INTEGER_ONE) &&
                    EntrustTypeEnum.LIFE_TRANSFER.getKey().equals(order.getDemandOrderEntrustType());
            List<TCarrierOrderGoods> tCarrierOrderGoods = dbTCarrierOrderGoodsMap.get(order.getId());
            BigDecimal carrierOrderLoad = new BigDecimal("0.00");
            for (TCarrierOrderGoods good : tCarrierOrderGoods) {
                BigDecimal loadAmount = pageLoadGoodsAmountMap.get(good.getId());
                upOrderGood = new TCarrierOrderGoods();
                upOrderGood.setId(good.getId());
                upOrderGood.setLoadAmount(loadAmount);
                if (ifYeloLifeRecycleAndByCode){
                    loadAmount = new BigDecimal("0.00");
                    if (goodCodeMap.get(good.getId()) != null) {
                        for (LoadGoodsForYeloLifeRequestCodeModel  e : goodCodeMap.get(good.getId())){
                            TCarrierOrderGoodsCode tCarrierOrderGoodsCode = new TCarrierOrderGoodsCode();
                            tCarrierOrderGoodsCode.setCarrierOrderGoodsId(good.getId());
                            tCarrierOrderGoodsCode.setLoadAmount(e.getWeight());
                            tCarrierOrderGoodsCode.setYeloGoodCode(e.getYeloCode());
                            tCarrierOrderGoodsCode.setUnit(e.getUnit());
                            commonBiz.setBaseEntityAdd(tCarrierOrderGoodsCode, BaseContextHandler.getUserName());
                            insertGoodsCodeList.add(tCarrierOrderGoodsCode);
                            loadAmount = loadAmount.add(e.getWeight());
                        }
                    }
                    upOrderGood.setLoadAmount(loadAmount);
                    carrierOrderLoad = carrierOrderLoad.add(loadAmount);
                }
                commonBiz.setBaseEntityModify(upOrderGood, BaseContextHandler.getUserName());
                upCarrierOrderGoods.add(upOrderGood);
            }

            if(ifYeloLifeRecycleAndByCode){
                upOrder.setLoadAmount(carrierOrderLoad);
                upOrder.setLoadAmountExpect(carrierOrderLoad);
                order.setLoadAmount(upOrder.getLoadAmount());//托盘逻辑
                order.setLoadAmountExpect(upOrder.getLoadAmountExpect());
            }
            upCarrierOrders.add(upOrder);

            unit = GoodsUnitEnum.getEnum(order.getGoodsUnit()).getUnit();
            //生成运单事件
            TCarrierOrderEvents carrierOrderEvents = carrierOrderCommonBiz.getCarrierOrderEvent(order.getId(), CarrierOrderEventsTypeEnum.PICK_UP, BaseContextHandler.getUserName(), CarrierOrderEventsTypeEnum.PICK_UP.format(StripTrailingZerosUtils.stripTrailingZerosToString(upOrder.getLoadAmount()) + unit));
            insertEvents.add(carrierOrderEvents);

            //生成操作日志
            TCarrierOrderOperateLogs carrierOrderOperateLogs = carrierOrderCommonBiz.getCarrierOrderOperateLogs(order.getId(), CarrierOrderOperateLogsTypeEnum.PICK_UP, BaseContextHandler.getUserName(), CarrierOrderOperateLogsTypeEnum.PICK_UP.format(StripTrailingZerosUtils.stripTrailingZerosToString(upOrder.getLoadAmount()) + unit));
            insertLogs.add(carrierOrderOperateLogs);

            // 构建运单智能推送提货节点事件Model
            workGroupPushBoModels.add(new WorkGroupPushBoModel()
                    .setOrderId(order.getId())
                    .setOrderSource(order.getDemandOrderSource())
                    .setEntrustTypeGroup(order.getDemandOrderEntrustType())
                    .setProjectLabel(order.getProjectLabel())
                    .setOrderType(WorkGroupOrderTypeEnum.CARRIER_ORDER)
                    .setOrderNode(WorkGroupOrderNodeEnum.CARRIER_ORDER_LOAD));
        }

        if (ListUtils.isNotEmpty(upCarrierOrders)) {
            tCarrierOrderMapper.batchUpdateCarrierOrders(upCarrierOrders);
        }
        if (ListUtils.isNotEmpty(upCarrierOrderGoods)) {
            tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(upCarrierOrderGoods);
        }
        if (ListUtils.isNotEmpty(insertGoodsCodeList)) {
            tCarrierOrderGoodsCodeMapper.batchInsertSelective(insertGoodsCodeList);
        }
        if (ListUtils.isNotEmpty(insertEvents)) {
            tCarrierOrderEventsMapper.batchInsertSelective(insertEvents);
        }
        if (ListUtils.isNotEmpty(insertLogs)) {
            carrierOrderOperateLogsMapper.batchInsertSelective(insertLogs);
        }
        if (ListUtils.isNotEmpty(addTicketsList)){
            tCarrierOrderTicketsMapper.batchInsertTickets(addTicketsList);
        }

        //预约单解除关联运单
        reservationOrderCommonBiz.disassociateCarrierOrder(carrierOrderIdList);

        //提货同步托盘
        loadUnloadSync(CommonConstant.INTEGER_ONE, carrierOrderIds, dbTCarrierOrderList, pageLoadAmountMap, pageLoadGoodsAmountMap, outStockCarrierOrderTicketsMap);


        // 发布智能推送提货节点事件
        applicationContext.publishEvent(new WorkGroupEventModel()
                .setWorkGroupPushBoModels(workGroupPushBoModels));


        //异步方法放在最后执行
        //节点信息推送微信公众号
        AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.batchWxPush(upCarrierOrders));
        return Result.success(true);
    }

    /**
     * 卸货
     *
     * @param requestModel
     */
    @Transactional
    public void unload(CarrierOrderUnLoadRequestModel requestModel) {

        //入参整理
        for (UnloadRequestModel unloadRequestModel : requestModel.getUnloadCarrierOrderList()) {
            for (CarrierOrderNodeGoodsRequestModel carrierOrderNodeGoodsRequestModel : unloadRequestModel.getGoodsList()) {
                carrierOrderNodeGoodsRequestModel.setCodeDtoList(Optional.ofNullable(carrierOrderNodeGoodsRequestModel.getCodeDtoList()).orElse(new ArrayList()));
            }
        }

        List<Long> carrierOrderIdList = new ArrayList<>();
        Map<Long, BigDecimal> pageUnLoadAmountMap = new HashMap<>();
        Map<Long, BigDecimal> pageUnLoadGoodsAmountMap = new HashMap<>();
        Map<String, LoadGoodsForYeloLifeRequestCodeModel> pageUnLoadGoodsCodeAmountMap = new HashMap<>();
        Map<Long, String> pageRemarkMap = new HashMap<>();
        Map<Long, List<String>> carrierOrderSignTicketsList = new HashMap<>();

        //校验入参数量，并统计提货数量
        for (UnloadRequestModel item : requestModel.getUnloadCarrierOrderList()) {
            if (item.getCarrierOrderId() == null || ListUtils.isEmpty(item.getGoodsList())) {
                throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
            }
            BigDecimal pageUnLoadAmount = BigDecimal.ZERO;
            for (CarrierOrderNodeGoodsRequestModel itemGoods : item.getGoodsList()) {
                BigDecimal tempGoodsCount = Optional.ofNullable(itemGoods.getCount()).orElse(BigDecimal.ZERO);
                //不同的单位对数量最小值最大值校验
                if (GoodsUnitEnum.BY_WEIGHT.getKey().equals(item.getGoodsUnit())){
                    if (!RegExpValidatorUtil.isFloatNumber(ConverterUtils.toString(tempGoodsCount))
                            || tempGoodsCount.compareTo(BigDecimal.ZERO) < CommonConstant.INTEGER_ZERO
                            || tempGoodsCount.compareTo(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_THOUSAND) > CommonConstant.INTEGER_ZERO){
                        throw new BizException(CarrierDataExceptionEnum.TOTAL_EXPECT_AMOUNT_NOT_WEIGHT);
                    }
                }else{
                    if (ListUtils.isEmpty(itemGoods.getCodeDtoList())){
                        if (!RegExpValidatorUtil.isNumber(ConverterUtils.toString(tempGoodsCount))
                                || tempGoodsCount.compareTo(BigDecimal.ZERO) < CommonConstant.INTEGER_ZERO
                                || tempGoodsCount.compareTo(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_THOUSAND) > CommonConstant.INTEGER_ZERO){
                            throw new BizException(CarrierDataExceptionEnum.TOTAL_EXPECT_AMOUNT_NOT_NUMBER);
                        }
                    }
                }
                pageUnLoadGoodsAmountMap.put(itemGoods.getGoodsId(), itemGoods.getCount());
                pageUnLoadAmount = pageUnLoadAmount.add(itemGoods.getCount());
                for (LoadGoodsForYeloLifeRequestCodeModel codeModel : itemGoods.getCodeDtoList()) {
                    pageUnLoadGoodsCodeAmountMap.put(itemGoods.getGoodsId() + "-" + codeModel.getYeloCode(), codeModel);
                }
            }
            if (pageUnLoadAmount.compareTo(BigDecimal.ZERO) <= CommonConstant.INTEGER_ZERO) {
                throw new BizException(CarrierDataExceptionEnum.TOTAL_EXPECT_LOAD_AMOUNT_ERROR);
            }
            carrierOrderIdList.add(item.getCarrierOrderId());
            pageUnLoadAmountMap.put(item.getCarrierOrderId(), pageUnLoadAmount);
            //备注信息
            if (StringUtils.isNotBlank(item.getRemark())) {
                pageRemarkMap.put(item.getCarrierOrderId(), CommonConstant.UNLOAD_REMARK_PREFIX + item.getRemark());
            }

            //签收单
            carrierOrderSignTicketsList.put(item.getCarrierOrderId(), item.getTmpUrl());
        }

        //运单状态检查
        String carrierOrderIds = StringUtils.listToString(carrierOrderIdList, ',');
        List<TCarrierOrder> orders = tCarrierOrderMapper.selectCarrierOrdersByIds(carrierOrderIds);
        if (orders.size() != carrierOrderIdList.size()) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //判断前台请求 运单所属
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            TCarrierOrder tCarrierOrder = orders.get(CommonConstant.INTEGER_ZERO);
            carrierOrderCommonBiz.checkCarrierOrderCompanyCarrierForWeb(tCarrierOrder.getCompanyCarrierId());
        }
        //校验车辆审核状态
        carrierOrderCommonBiz.checkIfCarrierOrdersStatusCanChange(carrierOrderIdList, CarrierDataExceptionEnum.VEHICLE_NEED_AUDIT_TIP2);

        //运单货物、销售单查询分组
        Map<Long, List<TCarrierOrderGoods>> dbTCarrierOrderGoodsMap = new HashMap<>();
        List<TCarrierOrderGoods> dbTCarrierOrderGoods = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(carrierOrderIds);
        if (ListUtils.isNotEmpty(dbTCarrierOrderGoods)) {
            dbTCarrierOrderGoodsMap = dbTCarrierOrderGoods.stream().collect(Collectors.groupingBy(TCarrierOrderGoods::getCarrierOrderId, Collectors.toList()));
        }
        String carrierOrderGoodsJoin = dbTCarrierOrderGoods.stream().map(tCarrierOrderGoods -> tCarrierOrderGoods.getId().toString()).collect(Collectors.joining(","));

        //查询运单货物编码
        List<TCarrierOrderGoodsCode> tCarrierOrderGoodsCodes = tCarrierOrderGoodsCodeMapper.selectGoodsCodeByGoodsIds(carrierOrderGoodsJoin);
        Map<Long, List<TCarrierOrderGoodsCode>> tCarrierOrderGoodsCodesMap = tCarrierOrderGoodsCodes.stream().collect(Collectors.groupingBy(TCarrierOrderGoodsCode::getCarrierOrderGoodsId));

        for (TCarrierOrder order : orders) {
            //不是待卸货状态不能操作
            if (!CarrierOrderStatusEnum.WAIT_UNLOAD.getKey().equals(order.getStatus())) {
                throw new BizException(CarrierDataExceptionEnum.ONLY_WAIT_UNLOAD);
            }
            //已取消不能操作
            if (CommonConstant.INTEGER_ONE.equals(order.getIfCancel())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL);
            }
            //运单放空不能操作
            if (CommonConstant.INTEGER_ONE.equals(order.getIfEmpty())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EMPTY);
            }
            //运单放空不能操作
            if (CommonConstant.INTEGER_ONE.equals(order.getIfExtCarrierOrder())) {
                throw new BizException(EntrustDataExceptionEnum.SYSTEM_NOT_SUPPORT);
            }
            //卸货数量不能大于提货数量
            List<TCarrierOrderGoods> dbGoodsList = dbTCarrierOrderGoodsMap.get(order.getId());
            for (TCarrierOrderGoods goods : dbGoodsList) {
                if (pageUnLoadGoodsAmountMap.get(goods.getId()).compareTo(goods.getLoadAmount()) > 0) {
                    throw new BizException(CarrierDataExceptionEnum.UNLOADING_AMOUNT_CANT_GT_LOAD_AMOUNT);
                }
            }
            if (pageUnLoadAmountMap.get(order.getId()).compareTo(order.getLoadAmount()) > 0) {
                throw new BizException(CarrierDataExceptionEnum.UNLOADING_AMOUNT_CANT_GT_LOAD_AMOUNT);
            }
        }
        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(carrierOrderIds);
        if (!workOrderMap.isEmpty()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }

        Date now = new Date();
        List<TCarrierOrder> upCarrierOrders = new ArrayList<>();
        List<CreateOrResetReceiptTicketAuditBoModel> ticketsAuditList = new ArrayList<>();
        List<TCarrierOrderGoods> upCarrierOrderGoods = new ArrayList<>();
        List<TCarrierOrderEvents> insertEvents = new ArrayList<>();
        List<TCarrierOrderOperateLogs> insertLogs = new ArrayList<>();
        List<TCarrierOrderTickets> addTicketsList = new ArrayList<>();
        List<TCarrierOrderGoodsCode> updateCarrierOrderGoodsCodes = new ArrayList<>();
        Map<String, List<String>> carrierOrderTicketsMap = null;//map为：运单号-》卸货票据绝对路径
        List<WorkGroupPushBoModel> workGroupPushBoModels = Lists.newArrayList();

        String unit;
        TCarrierOrder upOrder;
        CreateOrResetReceiptTicketAuditBoModel ticketsAudit;
        for (TCarrierOrder order : orders) {
            upOrder = new TCarrierOrder();
            upOrder.setId(order.getId());
            upOrder.setStatus(CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey());
            upOrder.setStatusUpdateTime(now);
            upOrder.setUnloadTime(now);
            upOrder.setUnloadAmount(pageUnLoadAmountMap.get(order.getId()));
            upOrder.setUnloadAmountExpect(pageUnLoadAmountMap.get(order.getId()));
            if(StringUtils.isNotBlank(pageRemarkMap.get(order.getId()))){
                upOrder.setRemark(pageRemarkMap.get(order.getId()) + order.getRemark());
            }
            commonBiz.setBaseEntityModify(upOrder, BaseContextHandler.getUserName());
            upCarrierOrders.add(upOrder);
            // 创建回单审核参数
            ticketsAudit = new CreateOrResetReceiptTicketAuditBoModel()
                    .setCarrierOrderId(upOrder.getId())
                    .setCarrierOrderCode(order.getCarrierOrderCode())
                    .setCarrierOrderStatus(upOrder.getStatus())
                    .setSource(order.getDemandOrderSource())
                    .setEntrustType(order.getDemandOrderEntrustType())
                    .setTicketType(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey());

            order.setUnloadAmount(upOrder.getUnloadAmount());//托盘逻辑
            order.setUnloadAmountExpect(upOrder.getUnloadAmountExpect());
            order.setUnloadTime(upOrder.getUnloadTime());
            order.setRemark(upOrder.getRemark());
            order.setUnloadTime(upOrder.getUnloadTime());

            TCarrierOrderGoods upOrderGood;
            List<TCarrierOrderGoods> tCarrierOrderGoods = dbTCarrierOrderGoodsMap.get(order.getId());
            for (TCarrierOrderGoods good : tCarrierOrderGoods) {
                upOrderGood = new TCarrierOrderGoods();
                upOrderGood.setId(good.getId());
                upOrderGood.setUnloadAmount(pageUnLoadGoodsAmountMap.get(good.getId()));
                commonBiz.setBaseEntityModify(upOrderGood, BaseContextHandler.getUserName());
                upCarrierOrderGoods.add(upOrderGood);

                //如果是货物编码回收 还需要更新货物编码
                List<TCarrierOrderGoodsCode> carrierOrderGoodsCodes = tCarrierOrderGoodsCodesMap.getOrDefault(good.getId(), new ArrayList<>());
                for (TCarrierOrderGoodsCode carrierOrderGoodsCode : carrierOrderGoodsCodes) {
                    LoadGoodsForYeloLifeRequestCodeModel codeModel = pageUnLoadGoodsCodeAmountMap.get(good.getId() + "-" + carrierOrderGoodsCode.getYeloGoodCode());
                    if (codeModel == null) {
                        continue;
                    }

                    TCarrierOrderGoodsCode updateCarrierOrderGoodsCode = new TCarrierOrderGoodsCode();
                    updateCarrierOrderGoodsCode.setId(carrierOrderGoodsCode.getId());
                    updateCarrierOrderGoodsCode.setUnloadAmount(codeModel.getWeight());
                    updateCarrierOrderGoodsCode.setUnit(codeModel.getUnit());
                    updateCarrierOrderGoodsCodes.add(updateCarrierOrderGoodsCode);
                }
            }
            unit = GoodsUnitEnum.getEnum(order.getGoodsUnit()).getUnit();
            //生成运单事件
            TCarrierOrderEvents carrierOrderEvents = carrierOrderCommonBiz.getCarrierOrderEvent(upOrder.getId(), CarrierOrderEventsTypeEnum.UNLOADING, BaseContextHandler.getUserName(), CarrierOrderEventsTypeEnum.UNLOADING.format(StripTrailingZerosUtils.stripTrailingZerosToString(upOrder.getUnloadAmount()) + unit));
            insertEvents.add(carrierOrderEvents);
            //生成操作日志
            TCarrierOrderOperateLogs carrierOrderOperateLogs = carrierOrderCommonBiz.getCarrierOrderOperateLogs(upOrder.getId(), CarrierOrderOperateLogsTypeEnum.UNLOADING, BaseContextHandler.getUserName(), CarrierOrderOperateLogsTypeEnum.UNLOADING.format(StripTrailingZerosUtils.stripTrailingZerosToString(upOrder.getUnloadAmount()) + unit));
            insertLogs.add(carrierOrderOperateLogs);

            //保存签收单
            List<String> singTicketList = carrierOrderSignTicketsList.get(order.getId());
            if (ListUtils.isNotEmpty(singTicketList)) {
                int ticketCount = singTicketList.size();
                List<GetTicketsResponseModel> existTicketsCount = tCarrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(order.getId(), CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey());
                if (ticketCount + existTicketsCount.size() > CommonConstant.INTEGER_SIX) {
                    throw new BizException(CarrierDataExceptionEnum.PICTURES_OVER_COUNT);
                }
                TCarrierOrderTickets addTickets;
                List<String> signTicketsList = new ArrayList<>();
                for (String image : singTicketList) {
                    addTickets = new TCarrierOrderTickets();
                    addTickets.setCarrierOrderId(order.getId());
                    addTickets.setImageName(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getValue());
                    addTickets.setImagePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey(), order.getCarrierOrderCode(), image, null));
                    addTickets.setImageType(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey());
                    addTickets.setUploadTime(new Date());
                    addTickets.setUploadUserName(BaseContextHandler.getUserName());
                    commonBiz.setBaseEntityAdd(addTickets, BaseContextHandler.getUserName());
                    addTicketsList.add(addTickets);

                    signTicketsList.add(addTickets.getImagePath());
                }
                ticketsAudit.setTicketsPathList(signTicketsList);

                //如果是发货、回收入库、回收出库、供应商直配、退货仓库配送类型，将卸货票据同步给云盘
                List<Integer> entrustTypeListModel = Arrays.asList(EntrustTypeEnum.DELIVER.getKey(), EntrustTypeEnum.RECYCLE_IN.getKey(),
                        EntrustTypeEnum.RECYCLE_OUT.getKey(), EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey(), EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey());
                if (entrustTypeListModel.contains(order.getDemandOrderEntrustType()) && ListUtils.isNotEmpty(signTicketsList)) {
                    carrierOrderTicketsMap = new HashMap<>();
                    carrierOrderTicketsMap.put(order.getCarrierOrderCode(), signTicketsList);
                }
            }
            ticketsAuditList.add(ticketsAudit);

            // 构建运单智能推送卸货节点事件Model
            workGroupPushBoModels.add(new WorkGroupPushBoModel()
                    .setOrderId(order.getId())
                    .setOrderSource(order.getDemandOrderSource())
                    .setEntrustTypeGroup(order.getDemandOrderEntrustType())
                    .setProjectLabel(order.getProjectLabel())
                    .setOrderType(WorkGroupOrderTypeEnum.CARRIER_ORDER)
                    .setOrderNode(WorkGroupOrderNodeEnum.CARRIER_ORDER_UNLOAD));
        }
        if (ListUtils.isNotEmpty(upCarrierOrders)) {
            tCarrierOrderMapper.batchUpdateCarrierOrders(upCarrierOrders);
        }
        if (ListUtils.isNotEmpty(upCarrierOrderGoods)) {
            tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(upCarrierOrderGoods);
        }
        if (CollectionUtil.isNotEmpty(updateCarrierOrderGoodsCodes)) {
            tCarrierOrderGoodsCodeMapper.batchUpdate(updateCarrierOrderGoodsCodes);
        }
        if (ListUtils.isNotEmpty(insertEvents)) {
            tCarrierOrderEventsMapper.batchInsertSelective(insertEvents);
        }
        if (ListUtils.isNotEmpty(insertLogs)) {
            carrierOrderOperateLogsMapper.batchInsertSelective(insertLogs);
        }
        if (ListUtils.isNotEmpty(addTicketsList)) {
            tCarrierOrderTicketsMapper.batchInsertTickets(addTicketsList);
        }
        if (ListUtils.isNotEmpty(ticketsAuditList)) {
            // 创建回单审核
            carrierOrderTicketsAuditBiz.batchCreateReceiptTicketAudit(ticketsAuditList, !CommonConstant.INTEGER_TWO.equals(requestModel.getSource()));
        }

        //生成外部车辆结算数据
        extVehicleSettlementBiz.createExtVehicleSettlement(carrierOrderIdList, false, false);

        //预约单解除关联运单
        reservationOrderCommonBiz.disassociateCarrierOrder(carrierOrderIdList);

        //卸货同步托盘
        loadUnloadSync(CommonConstant.INTEGER_TWO, carrierOrderIds, orders, pageUnLoadAmountMap, pageUnLoadGoodsAmountMap, carrierOrderTicketsMap);

        // 发布智能推送卸货节点事件
        applicationContext.publishEvent(new WorkGroupEventModel()
                .setWorkGroupPushBoModels(workGroupPushBoModels));

        //异步方法放在最后执行
        //节点信息推送微信公众号
        AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.batchWxPush(upCarrierOrders));
    }

    /**
     * 提货卸货同步托盘
     *
     * @param type                     1 提货，2 卸货
     * @param carrierOrderIds          运单ids
     * @param orders                   运单信息（该节点操作未更新前的）
     * @param loadUnloadAmountMap      运单id-》运单货物总数量
     * @param loadUnloadGoodsAmountMap 运单货物id-》货物数量
     * @param carrierOrderTicketsMap   提货出库单或卸货签收单：运单号-》票据拷贝后路径
     */
    public void loadUnloadSync(Integer type, String carrierOrderIds, List<TCarrierOrder> orders, Map<Long, BigDecimal> loadUnloadAmountMap, Map<Long, BigDecimal> loadUnloadGoodsAmountMap, Map<String, List<String>> carrierOrderTicketsMap) {
        //卸货
        List<CarrierOrderSynchronizeModel> synchronizeModels = new ArrayList<>();//云仓
        List<CarrierOrderSynchronizeModel> synchronizeToLeYiModels = new ArrayList<>();//云盘
        //查询销售单数据,同步托盘
        Map<Long, List<TCarrierOrderOrderRel>> dbCarrierOrderRelMap = new HashMap<>();
        List<TCarrierOrderOrderRel> dbCarrierOrderRels = tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(carrierOrderIds);
        if (ListUtils.isNotEmpty(dbCarrierOrderRels)) {
            dbCarrierOrderRelMap = dbCarrierOrderRels.stream().collect(Collectors.groupingBy(TCarrierOrderOrderRel::getCarrierOrderId, Collectors.toList()));
        }

        List<LogisticsDemandStateSynchronizeModel> logisticsDemandStateSynchronizeModels = new ArrayList<>();
        Map<Long, List<TCarrierOrderGoods>> dbTCarrierOrderGoodsMap = new HashMap<>();
        List<CarrierOrderLoadAmountSyncModel> syncBackAmountModels = new ArrayList<>();

        List<TCarrierOrderGoods> dbTCarrierOrderGoods = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(carrierOrderIds);
        if (ListUtils.isNotEmpty(dbTCarrierOrderGoods)) {
            dbTCarrierOrderGoodsMap = dbTCarrierOrderGoods.stream().collect(Collectors.groupingBy(TCarrierOrderGoods::getCarrierOrderId, Collectors.toList()));
        }

        //查询运单车辆记录信息
        List<TCarrierOrderVehicleHistory> dbCarrierOrderVehicleHistoryList = tCarrierOrderVehicleHistoryMapper.getValidByCarrierOrderIds(carrierOrderIds);
        Map<Long, TCarrierOrderVehicleHistory> dbCarrierOrderVehicleHistoryMap = dbCarrierOrderVehicleHistoryList.stream().collect(Collectors.toMap(TCarrierOrderVehicleHistory::getCarrierOrderId, Function.identity()));

        CarrierOrderLoadToYeloLifeModel carrierOrderLoadToYeloLifeModel;
        List<CarrierOrderLoadToYeloLifeModel> carrierOrderLoadToYeloLifeModelList = new ArrayList<>();
        List<TCarrierOrderOrderRel> upCarrierOrderRels = new ArrayList<>();
        //提货退回
        CarrierOrderOrderRelSyncModel orderRelSyncModel;
        List<CarrierOrderOrderRelSyncModel> orderRelSyncList = new ArrayList<>();

        //卸货票据同步给云盘
        UpdateCarrierOrderBillModel updateCarrierOrderBillModel;
        List<UpdateCarrierOrderBillModel> updateCarrierOrderBillModelList = new ArrayList<>();

        //遍历运单信息
        for (TCarrierOrder order : orders) {
            List<OrderAmountModel> upOrderUnloadAmountModels = new ArrayList<>();
            List<OrderAmountModel> upOrderLoadAmountModels = new ArrayList<>();
            List<TCarrierOrderOrderRel> oldCarrierOrderRels = dbCarrierOrderRelMap.get(order.getId());
            OrderAmountModel orderUnloadAmountModel;
            OrderAmountModel orderLoadAmountModel;
            if (ListUtils.isNotEmpty(oldCarrierOrderRels)) {
                BigDecimal surplusAmount = loadUnloadAmountMap.get(order.getId());
                for (TCarrierOrderOrderRel tmp : oldCarrierOrderRels) {
                    TCarrierOrderOrderRel upRel = new TCarrierOrderOrderRel();
                    upRel.setId(tmp.getId());
                    commonBiz.setBaseEntityModify(upRel, BaseContextHandler.getUserName());
                    upCarrierOrderRels.add(upRel);

                    if (CommonConstant.INTEGER_TWO.equals(type)) {
                        orderUnloadAmountModel = new OrderAmountModel();
                        orderUnloadAmountModel.setOrderId(tmp.getOrderId());
                        orderUnloadAmountModel.setOrderCode(tmp.getOrderCode());
                        upOrderUnloadAmountModels.add(orderUnloadAmountModel);

                        upRel.setLoadAmount(tmp.getLoadAmount());

                        if (surplusAmount.subtract(upRel.getLoadAmount()).compareTo(BigDecimal.ZERO) > 0) {
                            upRel.setUnloadAmount(upRel.getLoadAmount());
                            surplusAmount = surplusAmount.subtract(upRel.getLoadAmount());
                        } else {
                            upRel.setUnloadAmount(surplusAmount);
                            surplusAmount = BigDecimal.ZERO;
                        }

                        orderUnloadAmountModel.setAmount(upRel.getUnloadAmount());
                    }else if(CommonConstant.INTEGER_ONE.equals(type)){
                        orderLoadAmountModel = new OrderAmountModel();
                        orderLoadAmountModel.setOrderId(tmp.getOrderId());
                        orderLoadAmountModel.setOrderCode(tmp.getOrderCode());
                        upOrderLoadAmountModels.add(orderLoadAmountModel);

                        if(surplusAmount.subtract(tmp.getExpectAmount()).compareTo(BigDecimal.ZERO) > 0){
                            upRel.setLoadAmount(tmp.getExpectAmount());
                            surplusAmount = surplusAmount.subtract(tmp.getExpectAmount());
                        }else{
                            upRel.setLoadAmount(surplusAmount);
                            surplusAmount = BigDecimal.ZERO;
                        }

                        orderLoadAmountModel.setAmount(upRel.getLoadAmount());

                        //对应S单退回数量
                        orderRelSyncModel = new CarrierOrderOrderRelSyncModel();
                        orderRelSyncModel.setOrderId(tmp.getOrderId());
                        orderRelSyncModel.setCount(tmp.getExpectAmount().subtract(upRel.getLoadAmount()));
                        orderRelSyncList.add(orderRelSyncModel);
                    }
                }
            }
            if(CommonConstant.INTEGER_ONE.equals(type)){
                //更新货物表信息，并计算同步到entrust的总提货数(货物提货数大于预提数 取 预提数)
                BigDecimal syncLoadAmount = BigDecimal.ZERO;
                List<TCarrierOrderGoods> tCarrierOrderGoods = dbTCarrierOrderGoodsMap.get(order.getId());
                for (TCarrierOrderGoods good : tCarrierOrderGoods) {
                    if(loadUnloadGoodsAmountMap.get(good.getId()).compareTo(good.getExpectAmount()) > 0){
                        syncLoadAmount = syncLoadAmount.add(good.getExpectAmount());
                    }else{
                        syncLoadAmount = syncLoadAmount.add(loadUnloadGoodsAmountMap.get(good.getId()));
                    }
                }

                //未完全提货,同步更新需求单退回数量
                if(order.getExpectAmount().compareTo(syncLoadAmount) > 0){
                    BigDecimal backAmount =  order.getExpectAmount().subtract(syncLoadAmount);

                    CarrierOrderLoadAmountSyncModel carrierOrderLoadModel = new CarrierOrderLoadAmountSyncModel();
                    carrierOrderLoadModel.setDemandOrderId(order.getDemandOrderId());
                    carrierOrderLoadModel.setBackAmount(backAmount);
                    CarrierOrderLoadGoodsAmountSyncModel carrierOrderLoadGoodsSyncModel;
                    List<CarrierOrderLoadGoodsAmountSyncModel> goodsList = new ArrayList<>();
                    BigDecimal goodsBackAmount;
                    for (TCarrierOrderGoods temp : tCarrierOrderGoods) {
                        goodsBackAmount = temp.getExpectAmount().subtract(loadUnloadGoodsAmountMap.get(temp.getId()));
                        if(goodsBackAmount.compareTo(BigDecimal.ZERO) <= CommonConstant.INTEGER_ZERO){
                            continue;
                        }
                        carrierOrderLoadGoodsSyncModel = new CarrierOrderLoadGoodsAmountSyncModel();
                        carrierOrderLoadGoodsSyncModel.setGoodsId(temp.getDemandOrderGoodsId());
                        carrierOrderLoadGoodsSyncModel.setBackAmount(goodsBackAmount);
                        goodsList.add(carrierOrderLoadGoodsSyncModel);
                    }
                    carrierOrderLoadModel.setGoodsList(goodsList);
                    carrierOrderLoadModel.setCarrierOrderRelList(orderRelSyncList);
                    syncBackAmountModels.add(carrierOrderLoadModel);
                }
            }

            //云盘单子
            if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(order.getDemandOrderSource())) {
                //待推送云盘数据
                CarrierOrderSynchronizeModel syncModel = new CarrierOrderSynchronizeModel();
                syncModel.setType(order.getDemandOrderEntrustType());
                syncModel.setCarrierOrderCode(order.getCarrierOrderCode());
                syncModel.setStatus(CommonConstant.INTEGER_ONE.equals(type) ? CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey() : CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey());
                syncModel.setUnloadingCount(order.getUnloadAmount());
                if(CommonConstant.INTEGER_TWO.equals(type)){
                    syncModel.setUnloadTime(order.getUnloadTime());
                }
                if(CommonConstant.INTEGER_ONE.equals(type)){
                    syncModel.setLoadTime(order.getLoadTime());
                }
                syncModel.setLoadingCount(order.getLoadAmount());
                syncModel.setStockOutTime(order.getLoadTime());
                syncModel.setUserName(BaseContextHandler.getUserName());
                syncModel.setOrderLoadAmount(upOrderLoadAmountModels);
                syncModel.setOrderUnloadAmount(upOrderUnloadAmountModels);
                synchronizeModels.add(syncModel);

                //提货-回收类型,卸货-发货、回收、供应商直配类型，票据同步运单
                if (MapUtils.isNotEmpty(carrierOrderTicketsMap)) {//运单操作节点票据不为空
                    List<String> tickets = carrierOrderTicketsMap.get(order.getCarrierOrderCode());
                    if (ListUtils.isNotEmpty(tickets)) {
                        //票据同步云盘
                        if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(order.getDemandOrderEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(order.getDemandOrderEntrustType())){
                            //回收类型：提货、卸货节点都有
                            syncModel.setBillPath(tickets);

                        }else if (EntrustTypeEnum.DELIVER.getKey().equals(order.getDemandOrderEntrustType())
                                || EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey().equals(order.getDemandOrderEntrustType())
                                || EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey().equals(order.getDemandOrderEntrustType())){

                            //发货、供应商直配、退货仓库配送类型：卸货
                            List<CarrierOrderBill> carrierOrderBillList = new ArrayList<>();
                            CarrierOrderBill carrierOrderBill;
                            for (String ticketPath : tickets) {
                                carrierOrderBill = new CarrierOrderBill();
                                carrierOrderBill.setBillPath(ticketPath);
                                carrierOrderBill.setType(CommonConstant.INTEGER_FOUR);
                                carrierOrderBillList.add(carrierOrderBill);
                            }
                            updateCarrierOrderBillModel = new UpdateCarrierOrderBillModel();
                            updateCarrierOrderBillModel.setCarrierOrderCode(order.getCarrierOrderCode());
                            updateCarrierOrderBillModel.setOperation(BaseContextHandler.getUserName());
                            updateCarrierOrderBillModel.setType(CommonConstant.INTEGER_ONE);
                            updateCarrierOrderBillModel.setCarrierOrderBillList(carrierOrderBillList);
                            updateCarrierOrderBillModelList.add(updateCarrierOrderBillModel);
                        }
                    }
                }

                //回收入库、回收出库、预约类型
                if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(order.getDemandOrderEntrustType())
                        || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(order.getDemandOrderEntrustType())
                        || EntrustTypeEnum.BOOKING.getKey().equals(order.getDemandOrderEntrustType())) {
                    synchronizeToLeYiModels.add(syncModel);
                }
                //运单sku数据
                List<TCarrierOrderGoods> carrierOrderGoodsList = dbTCarrierOrderGoodsMap.get(order.getId());
                List<TypeAndCountModel> typeAndCountModels = new ArrayList<>();
                for (TCarrierOrderGoods goods : carrierOrderGoodsList) {
                    TypeAndCountModel typeAndCountModel=new TypeAndCountModel();
                    typeAndCountModel.setProductTypeCode(goods.getSkuCode());
                    typeAndCountModel.setCategoryName(goods.getCategoryName());
                    typeAndCountModel.setHeight(ConverterUtils.toString(goods.getHeight()));
                    typeAndCountModel.setWidth(ConverterUtils.toString(goods.getWidth()));
                    typeAndCountModel.setLength(ConverterUtils.toString(goods.getLength()));
                    typeAndCountModel.setSortName(goods.getGoodsName());
                    typeAndCountModel.setLoadingCount(goods.getLoadAmount().intValue());
                    typeAndCountModel.setUnloadingCount(goods.getUnloadAmount().intValue());

                    typeAndCountModels.add(typeAndCountModel);
                }
                syncModel.setTypeAndCountModel(typeAndCountModels);
            }
            //新生单子
            else if (DemandOrderSourceEnum.YELO_LIFE.getKey().equals(order.getDemandOrderSource())) {
                //提货
                if (CommonConstant.INTEGER_ONE.equals(type)) {
                    CarrierOrderGoodsToYeloLifeModel carrierOrderGoodsToYeloLifeModel;
                    List<CarrierOrderGoodsToYeloLifeModel> carrierOrderGoodsToYeloLifeModelList = new ArrayList<>();
                    //货物信息
                    List<TCarrierOrderGoods> carrierOrderGoodsList = dbTCarrierOrderGoodsMap.get(order.getId());
                    boolean ifYeloLifeRecycleAndByCode = order.getIfRecycleByCode().equals(CommonConstant.INTEGER_ONE) &&
                            EntrustTypeEnum.LIFE_TRANSFER.getKey().equals(order.getDemandOrderEntrustType());
                    Map<Long,List<TCarrierOrderGoodsCode>> goodCodeMap = new HashMap<>();
                    if (ifYeloLifeRecycleAndByCode){
                        List<TCarrierOrderGoodsCode> tCarrierOrderGoodsCodes = tCarrierOrderGoodsCodeMapper.selectGoodsCodeByGoodsIds(StringUtils.listToString(carrierOrderGoodsList.stream().map(
                                TCarrierOrderGoods::getId
                        ).collect(Collectors.toList()), ','));
                        if (ListUtils.isNotEmpty(tCarrierOrderGoodsCodes)){
                            goodCodeMap = tCarrierOrderGoodsCodes.stream().collect(
                                    Collectors.groupingBy(TCarrierOrderGoodsCode::getCarrierOrderGoodsId));
                        }
                    }

                    for (TCarrierOrderGoods tCarrierOrderGoods : carrierOrderGoodsList) {
                        carrierOrderGoodsToYeloLifeModel = new CarrierOrderGoodsToYeloLifeModel();
                        carrierOrderGoodsToYeloLifeModel.setSkuCode(tCarrierOrderGoods.getSkuCode());
                        if (ifYeloLifeRecycleAndByCode){
                            if (goodCodeMap.get(tCarrierOrderGoods.getId())!= null){
                                for (TCarrierOrderGoodsCode  e : goodCodeMap.get(tCarrierOrderGoods.getId())){
                                    carrierOrderGoodsToYeloLifeModel = new CarrierOrderGoodsToYeloLifeModel();
                                    carrierOrderGoodsToYeloLifeModel.setSkuCode(tCarrierOrderGoods.getSkuCode());
                                    carrierOrderGoodsToYeloLifeModel.setCount(e.getLoadAmount());
                                    carrierOrderGoodsToYeloLifeModel.setRecycleBagCode(e.getYeloGoodCode());
                                    carrierOrderGoodsToYeloLifeModel.setUnit(e.getUnit());
                                    carrierOrderGoodsToYeloLifeModelList.add(carrierOrderGoodsToYeloLifeModel);
                                }
                            }
                        }else {
                            carrierOrderGoodsToYeloLifeModel.setSkuCode(tCarrierOrderGoods.getSkuCode());
                            Integer goodsUnit = order.getGoodsUnit();
                            if (GoodsUnitEnum.BY_PACKAGE.getKey().equals(goodsUnit)){
                                carrierOrderGoodsToYeloLifeModel.setUnit(CommonConstant.INTEGER_TWO);
                            }
                            if (GoodsUnitEnum.BY_WEIGHT.getKey().equals(goodsUnit)){
                                carrierOrderGoodsToYeloLifeModel.setUnit(CommonConstant.INTEGER_ONE);
                            }
                            carrierOrderGoodsToYeloLifeModel.setUnit(order.getGoodsUnit());
                            carrierOrderGoodsToYeloLifeModel.setCount(tCarrierOrderGoods.getLoadAmount());
                            carrierOrderGoodsToYeloLifeModelList.add(carrierOrderGoodsToYeloLifeModel);
                        }
                    }
                    //车辆信息
                    TCarrierOrderVehicleHistory dbCarrierOrderVehicleHistory = dbCarrierOrderVehicleHistoryMap.get(order.getId());
                    //同步云仓model
                    carrierOrderLoadToYeloLifeModel = new CarrierOrderLoadToYeloLifeModel();
                    carrierOrderLoadToYeloLifeModel.setDemandOrderCode(order.getDemandOrderCode());
                    carrierOrderLoadToYeloLifeModel.setCarrierOrderCode(order.getCarrierOrderCode());
                    carrierOrderLoadToYeloLifeModel.setCustomerOrderCode(order.getCustomerOrderCode());
                    carrierOrderLoadToYeloLifeModel.setVehicleNo(dbCarrierOrderVehicleHistory.getVehicleNo());
                    carrierOrderLoadToYeloLifeModel.setRemark(order.getRemark());
                    carrierOrderLoadToYeloLifeModel.setUserName(BaseContextHandler.getUserName());
                    carrierOrderLoadToYeloLifeModel.setLifeGoodsModels(carrierOrderGoodsToYeloLifeModelList);
                    carrierOrderLoadToYeloLifeModelList.add(carrierOrderLoadToYeloLifeModel);
                }
            }
        }

        //更新需求单退回数量信息
        if (ListUtils.isNotEmpty(syncBackAmountModels)) {
            logisticsDemandStateSynchronizeModels = demandOrderBiz.syncCarrierLoadBackAmount(syncBackAmountModels);
        }
        if (ListUtils.isNotEmpty(upCarrierOrderRels)) {
            tCarrierOrderOrderRelMapper.batchUpdateSelective(upCarrierOrderRels);
        }

        //新生运单提货后同步新生系统
        if (ListUtils.isNotEmpty(carrierOrderLoadToYeloLifeModelList)) {
            SyncCarrierOrderToYeloLifeModel<Object> syncCarrierOrderToYeloLifeModel = new SyncCarrierOrderToYeloLifeModel<>();
            syncCarrierOrderToYeloLifeModel.setType(SyncCarrierOrderToYeloLifeModel.SyncTypeEnum.LOAD.getKey());
            syncCarrierOrderToYeloLifeModel.setMsgData(carrierOrderLoadToYeloLifeModelList);
            rabbitMqPublishBiz.syncCarrierOrderToYeloLife(syncCarrierOrderToYeloLifeModel);
        }

        //推送数据给云仓
        if (ListUtils.isNotEmpty(synchronizeModels)) {
            CarrierOrderSynchronizeLeyiModel carrierOrderSynchronizeLeyiModel = new CarrierOrderSynchronizeLeyiModel();
            carrierOrderSynchronizeLeyiModel.setCarrierOrderSynchronizeModels(synchronizeModels);
            carrierOrderSynchronizeLeyiModel.setLogisticsDemandStateSynchronizeModels(logisticsDemandStateSynchronizeModels);
            log.info(CommonConstant.INTEGER_ONE.equals(type) ? "提货" : "卸货" + "-同步云仓信息：" + carrierOrderSynchronizeLeyiModel);
            rabbitMqPublishBiz.carrierOrderSynchronizeToWarehouse(carrierOrderSynchronizeLeyiModel);
        }

        //推送数据给云盘（回收或预约类型）
        if (ListUtils.isNotEmpty(synchronizeToLeYiModels)) {
            CarrierOrderSynchronizeLeyiModel carrierOrderSynchronizeLeyiModel = new CarrierOrderSynchronizeLeyiModel();
            carrierOrderSynchronizeLeyiModel.setCarrierOrderSynchronizeModels(synchronizeToLeYiModels);
            carrierOrderSynchronizeLeyiModel.setLogisticsDemandStateSynchronizeModels(logisticsDemandStateSynchronizeModels);
            log.info(CommonConstant.INTEGER_ONE.equals(type) ? "提货" : "卸货" + "-同步云盘信息：" + carrierOrderSynchronizeLeyiModel);
            rabbitMqPublishBiz.carrierOrderSynchronizeToLeyi(carrierOrderSynchronizeLeyiModel);
        }

        //发货、供应商直配类型卸货票同步给云盘（回收类型在"推送数据给云盘"mq同步）
        if (ListUtils.isNotEmpty(updateCarrierOrderBillModelList)) {
            rabbitMqPublishBiz.syncCarrierOrderOutStockTickets(updateCarrierOrderBillModelList);
        }
    }

    /**
     * 到达提货地
     *
     * @param requestModel 运单ID集合
     */
    @Transactional
    public void reachLoadAddress(ReachLoadAddressRequestModel requestModel) {
        if (ListUtils.isEmpty(requestModel.getCarrierOrderId())) {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }
        String carrierOrderIds = StringUtils.listToString(requestModel.getCarrierOrderId(), ',');

        List<TCarrierOrder> orders = tCarrierOrderMapper.selectCarrierOrdersByIds(carrierOrderIds);
        carrierOrderCommonBiz.checkIfCarrierOrdersStatusCanChange(requestModel.getCarrierOrderId(), CarrierDataExceptionEnum.VEHICLE_NEED_AUDIT_TIP2);

        if (orders.size() != requestModel.getCarrierOrderId().size()) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //判断前台请求 运单所属
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            TCarrierOrder tCarrierOrder = orders.get(CommonConstant.INTEGER_ZERO);
            carrierOrderCommonBiz.checkCarrierOrderCompanyCarrierForWeb(tCarrierOrder.getCompanyCarrierId());
        }

        //运单状态检查
        for (TCarrierOrder order : orders) {
            //不是待到达提货地状态不能操作
            if (!CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey().equals(order.getStatus())) {
                throw new BizException(CarrierDataExceptionEnum.CANT_NOT_REACHLOADADDRESS);
            }
            //已取消不能操作
            if (CommonConstant.INTEGER_ONE.equals(order.getIfCancel())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL);
            }
            //运单放空不能操作
            if (CommonConstant.INTEGER_ONE.equals(order.getIfEmpty())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EMPTY);
            }
        }
        //运单有未处理的异常工单，则不能操作
//        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(carrierOrderIds);
//        if (!workOrderMap.isEmpty()){
//            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
//        }

        Date now = new Date();
        List<TCarrierOrder> upCarrierOrders = new ArrayList<>();
        List<TCarrierOrderEvents> insertEvents = new ArrayList<>();
        List<TCarrierOrderOperateLogs> insertLogs = new ArrayList<>();
        List<CarrierOrderSynchronizeModel> synchronizeModels = new ArrayList<>();//同步云仓
        List<CarrierOrderSynchronizeModel> synchronizeToLeYiModels = new ArrayList<>();//同步云盘
        List<WorkGroupPushBoModel> workGroupPushBoModels = Lists.newArrayList();

        TCarrierOrder upOrder;
        TCarrierOrderEvents carrierOrderEvents;
        TCarrierOrderOperateLogs carrierOrderOperateLogs;
        for (TCarrierOrder order : orders) {
            upOrder = new TCarrierOrder();
            upOrder.setId(order.getId());
            upOrder.setStatus(CarrierOrderStatusEnum.WAIT_LOAD.getKey());
            upOrder.setStatusUpdateTime(now);
            commonBiz.setBaseEntityModify(upOrder, BaseContextHandler.getUserName());
            upCarrierOrders.add(upOrder);

            //生成运单事件
            carrierOrderEvents = carrierOrderCommonBiz.getCarrierOrderEvent(order.getId(), CarrierOrderEventsTypeEnum.ARRIVED_PICK_UP, BaseContextHandler.getUserName(), CarrierOrderEventsTypeEnum.ARRIVED_PICK_UP.getFormat());
            insertEvents.add(carrierOrderEvents);

            //生成操作日志
            carrierOrderOperateLogs = carrierOrderCommonBiz.getCarrierOrderOperateLogs(order.getId(), CarrierOrderOperateLogsTypeEnum.ARRIVED_PICK_UP, BaseContextHandler.getUserName(), "");
            insertLogs.add(carrierOrderOperateLogs);

            //同步托盘和新生运单
            if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(order.getDemandOrderSource())) {
                CarrierOrderSynchronizeModel syncModel = new CarrierOrderSynchronizeModel();
                syncModel.setType(order.getDemandOrderEntrustType());
                syncModel.setCarrierOrderCode(order.getCarrierOrderCode());
                syncModel.setStatus(CarrierOrderStatusEnum.WAIT_LOAD.getKey());
                syncModel.setUserName(BaseContextHandler.getUserName());
                synchronizeModels.add(syncModel);

                //仅云盘的回收入库、回收出库、预约类型才同步给云盘系统
                if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(order.getDemandOrderEntrustType())
                        || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(order.getDemandOrderEntrustType())
                        || EntrustTypeEnum.BOOKING.getKey().equals(order.getDemandOrderEntrustType())) {
                    synchronizeToLeYiModels.add(syncModel);
                }
            }

            // 构建运单智能推送到达提货地节点事件Model
            workGroupPushBoModels.add(new WorkGroupPushBoModel()
                    .setOrderId(order.getId())
                    .setOrderSource(order.getDemandOrderSource())
                    .setEntrustTypeGroup(order.getDemandOrderEntrustType())
                    .setProjectLabel(order.getProjectLabel())
                    .setOrderType(WorkGroupOrderTypeEnum.CARRIER_ORDER)
                    .setOrderNode(WorkGroupOrderNodeEnum.CARRIER_ORDER_REACH_LOAD_ADDRESS));
        }
        if (ListUtils.isNotEmpty(upCarrierOrders)) {
            tCarrierOrderMapper.batchUpdateCarrierOrders(upCarrierOrders);
        }
        if (ListUtils.isNotEmpty(insertEvents)) {
            tCarrierOrderEventsMapper.batchInsertSelective(insertEvents);
        }
        if (ListUtils.isNotEmpty(insertLogs)) {
            carrierOrderOperateLogsMapper.batchInsertSelective(insertLogs);
        }

        if (ListUtils.isNotEmpty(synchronizeModels)) {
            log.info("到达提货地-同步云仓运单状态信息:" + synchronizeModels);
            CarrierOrderSynchronizeLeyiModel carrierOrderSynchronizeLeyiModel = new CarrierOrderSynchronizeLeyiModel();
            carrierOrderSynchronizeLeyiModel.setCarrierOrderSynchronizeModels(synchronizeModels);
            rabbitMqPublishBiz.carrierOrderSynchronizeToWarehouse(carrierOrderSynchronizeLeyiModel);
        }

        if (ListUtils.isNotEmpty(synchronizeToLeYiModels)) {
            log.info("到达提货地-同步托盘运单状态信息给云盘:" + synchronizeToLeYiModels);
            CarrierOrderSynchronizeLeyiModel carrierOrderSynchronizeToLeyiModel = new CarrierOrderSynchronizeLeyiModel();
            carrierOrderSynchronizeToLeyiModel.setCarrierOrderSynchronizeModels(synchronizeToLeYiModels);
            rabbitMqPublishBiz.carrierOrderSynchronizeToLeyi(carrierOrderSynchronizeToLeyiModel);
        }

        // 发布智能推送到达提货地节点事件
        applicationContext.publishEvent(new WorkGroupEventModel()
                .setWorkGroupPushBoModels(workGroupPushBoModels));

        //异步方法放在最后执行
        //节点信息推送微信公众号
        AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.batchWxPush(upCarrierOrders));
    }

    /**
     * 到达卸货地
     * @param requestModel
     */
    @Transactional
    public void reachUnloadAddress(ReachUnloadAddressRequestModel requestModel) {
        if (ListUtils.isEmpty(requestModel.getCarrierOrderId())) {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }

        //运单状态检查
        String carrierOrderIds = StringUtils.listToString(requestModel.getCarrierOrderId(), ',');
        List<TCarrierOrder> orders = tCarrierOrderMapper.selectCarrierOrdersByIds(carrierOrderIds);
        carrierOrderCommonBiz.checkIfCarrierOrdersStatusCanChange(requestModel.getCarrierOrderId(), CarrierDataExceptionEnum.VEHICLE_NEED_AUDIT_TIP2);

        if (orders.size() != requestModel.getCarrierOrderId().size()) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //判断前台请求 运单所属
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            TCarrierOrder tCarrierOrder = orders.get(CommonConstant.INTEGER_ZERO);
            carrierOrderCommonBiz.checkCarrierOrderCompanyCarrierForWeb(tCarrierOrder.getCompanyCarrierId());
        }

        for (TCarrierOrder order : orders) {
            //不是待到达卸货地状态不能操作
            if (!CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey().equals(order.getStatus())) {
                throw new BizException(CarrierDataExceptionEnum.ONLY_REACHUNLOADADDRESS);
            }
            //已取消不能操作
            if (CommonConstant.INTEGER_ONE.equals(order.getIfCancel())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL);
            }
            //运单放空不能操作
            if (CommonConstant.INTEGER_ONE.equals(order.getIfEmpty())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EMPTY);
            }
            if (CommonConstant.INTEGER_ONE.equals(order.getIfExtCarrierOrder())) {
                throw new BizException(EntrustDataExceptionEnum.SYSTEM_NOT_SUPPORT);
            }
        }

        Map<Long, List<TCarrierOrderOrderRel>> dbCarrierOrderRelMap = new HashMap<>();
        List<TCarrierOrderOrderRel> dbCarrierOrderRels = tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(carrierOrderIds);
        if (ListUtils.isNotEmpty(dbCarrierOrderRels)) {
            dbCarrierOrderRelMap = dbCarrierOrderRels.stream().collect(Collectors.groupingBy(TCarrierOrderOrderRel::getCarrierOrderId, Collectors.toList()));
        }

        Date now = new Date();
        TCarrierOrder upOrder;
        List<TCarrierOrder> upCarrierOrders = new ArrayList<>();
        List<TCarrierOrderEvents> insertEvents = new ArrayList<>();
        List<TCarrierOrderOperateLogs> insertLogs = new ArrayList<>();
        List<TCarrierOrderOrderRel> upCarrierOrderRels = new ArrayList<>();
        List<CarrierOrderSynchronizeModel> synchronizeModels = new ArrayList<>();
        List<CarrierOrderSynchronizeModel> synchronizeToLeYiModels = new ArrayList<>();
        List<WorkGroupPushBoModel> workGroupPushBoModels = Lists.newArrayList();

        for (TCarrierOrder order : orders) {
            upOrder = new TCarrierOrder();
            upOrder.setId(order.getId());
            upOrder.setStatus(CarrierOrderStatusEnum.WAIT_UNLOAD.getKey());
            upOrder.setStatusUpdateTime(now);
            commonBiz.setBaseEntityModify(upOrder, BaseContextHandler.getUserName());
            upCarrierOrders.add(upOrder);

            //生成运单事件
            TCarrierOrderEvents carrierOrderEvents = carrierOrderCommonBiz.getCarrierOrderEvent(order.getId(), CarrierOrderEventsTypeEnum.ARRIVED_UNLOAD, BaseContextHandler.getUserName(), CarrierOrderEventsTypeEnum.ARRIVED_UNLOAD.getFormat());
            insertEvents.add(carrierOrderEvents);

            //生成操作日志
            TCarrierOrderOperateLogs carrierOrderOperateLogs = carrierOrderCommonBiz.getCarrierOrderOperateLogs(order.getId(), CarrierOrderOperateLogsTypeEnum.ARRIVED_UNLOAD, BaseContextHandler.getUserName(), "");
            insertLogs.add(carrierOrderOperateLogs);

            //运单关联S单,跨节点到达卸货地操作同步运单
            if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(order.getDemandOrderSource())) {
                CarrierOrderSynchronizeModel syncModel = new CarrierOrderSynchronizeModel();
                syncModel.setType(order.getDemandOrderEntrustType());
                syncModel.setCarrierOrderCode(order.getCarrierOrderCode());
                syncModel.setStatus(CarrierOrderStatusEnum.WAIT_UNLOAD.getKey());
                syncModel.setLoadingCount(upOrder.getLoadAmount());
                syncModel.setUserName(BaseContextHandler.getUserName());
                //查询销售单数据
                List<OrderAmountModel> upOrderLoadAmountModels = new ArrayList<>();
                if (upOrder.getLoadAmount() != null) {
                    List<TCarrierOrderOrderRel> oldCarrierOrderRels = dbCarrierOrderRelMap.get(order.getId());
                    if (ListUtils.isNotEmpty(oldCarrierOrderRels)) {
                        TCarrierOrderOrderRel upRel = null;
                        OrderAmountModel orderAmountModel = null;
                        for (TCarrierOrderOrderRel tmp : oldCarrierOrderRels) {
                            upRel = new TCarrierOrderOrderRel();
                            upRel.setId(tmp.getId());
                            upRel.setLoadAmount(tmp.getExpectAmount());
                            commonBiz.setBaseEntityModify(upRel, BaseContextHandler.getUserName());
                            upCarrierOrderRels.add(upRel);
                            orderAmountModel = new OrderAmountModel();
                            orderAmountModel.setAmount(upRel.getLoadAmount());
                            orderAmountModel.setOrderId(tmp.getOrderId());
                            orderAmountModel.setOrderCode(tmp.getOrderCode());
                            upOrderLoadAmountModels.add(orderAmountModel);
                        }
                    }
                    syncModel.setOrderLoadAmount(upOrderLoadAmountModels);
                }
                synchronizeModels.add(syncModel);

                //云盘回收入库、回收出库、预约类型的单子
                if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(order.getDemandOrderEntrustType())
                        || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(order.getDemandOrderEntrustType())
                        || EntrustTypeEnum.BOOKING.getKey().equals(order.getDemandOrderEntrustType())) {
                    synchronizeToLeYiModels.add(syncModel);
                }
            }

            // 构建运单智能推送到达卸货地节点事件Model
            workGroupPushBoModels.add(new WorkGroupPushBoModel()
                    .setOrderId(order.getId())
                    .setOrderSource(order.getDemandOrderSource())
                    .setEntrustTypeGroup(order.getDemandOrderEntrustType())
                    .setProjectLabel(order.getProjectLabel())
                    .setOrderType(WorkGroupOrderTypeEnum.CARRIER_ORDER)
                    .setOrderNode(WorkGroupOrderNodeEnum.CARRIER_ORDER_REACH_UNLOAD_ADDRESS));
        }

        if (ListUtils.isNotEmpty(upCarrierOrders)) {
            tCarrierOrderMapper.batchUpdateCarrierOrders(upCarrierOrders);
        }
        if (ListUtils.isNotEmpty(insertEvents)) {
            tCarrierOrderEventsMapper.batchInsertSelective(insertEvents);
        }
        if (ListUtils.isNotEmpty(insertLogs)) {
            carrierOrderOperateLogsMapper.batchInsertSelective(insertLogs);
        }
        if (ListUtils.isNotEmpty(upCarrierOrderRels)) {
            tCarrierOrderOrderRelMapper.batchUpdateSelective(upCarrierOrderRels);
        }
        if (ListUtils.isNotEmpty(synchronizeModels)) {
            log.info("到达卸货地-同步云仓运单状态信息:" + synchronizeModels);
            CarrierOrderSynchronizeLeyiModel carrierOrderSynchronizeLeyiModel = new CarrierOrderSynchronizeLeyiModel();
            carrierOrderSynchronizeLeyiModel.setCarrierOrderSynchronizeModels(synchronizeModels);
            rabbitMqPublishBiz.carrierOrderSynchronizeToWarehouse(carrierOrderSynchronizeLeyiModel);
        }
        if (ListUtils.isNotEmpty(synchronizeToLeYiModels)) {
            log.info("到达卸货地-同步云盘运单状态信息:" + synchronizeToLeYiModels);
            CarrierOrderSynchronizeLeyiModel carrierOrderSynchronizeLeyiModel = new CarrierOrderSynchronizeLeyiModel();
            carrierOrderSynchronizeLeyiModel.setCarrierOrderSynchronizeModels(synchronizeToLeYiModels);
            rabbitMqPublishBiz.carrierOrderSynchronizeToLeyi(carrierOrderSynchronizeLeyiModel);
        }

        // 发布智能推送到达卸货地节点事件
        applicationContext.publishEvent(new WorkGroupEventModel()
                .setWorkGroupPushBoModels(workGroupPushBoModels));

        //异步方法放在最后执行
        //节点信息推送微信公众号
        AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.batchWxPush(upCarrierOrders));
    }

    /**
     * 根据需求单ids查询运单（不包含取消的，但包含放空的）
     * @param requestModel
     * @return
     */
    public List<GetCarrierOrderResponseModel> getCarrierOrderByDemandOrderIds(DemandOrderIdsRequestModel requestModel) {
        List<GetCarrierOrderResponseModel> list = tCarrierOrderMapper.getCarrierOrderByDemandOrderIds(requestModel.getDemandOrderIds(), null);
        if (ListUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 根据委托单id查询全部运单信息（不包含取消的，需求单完成调度时调用）
     *
     * @param requestModel
     * @return
     */
    public List<DemandCarrierOrderRecursiveModel> getByDemandOrderIds(DemandOrderIdsRequestModel requestModel) {
        return tCarrierOrderMapper.getNotCancelByDemandOrderIds(requestModel.getDemandOrderIds());
    }

    /**
     * 提卸货详情接口
     * @param requestModel
     * @return
     */
    public List<LoadDetailResponseModel> getLoadDetail(LoadDetailRequestModel requestModel){
        if (ListUtils.isEmpty(requestModel.getCarrierOrderId())) {
            return new ArrayList<>();
        }
        //节点操作提示
        List<TCarrierOrder> dbTCarrierOrderList = carrierOrderCommonBiz.checkCarrierOrderIfExist(requestModel.getCarrierOrderId());
        for (TCarrierOrder dbTCarrierOrder : dbTCarrierOrderList) {
            if (CommonConstant.INTEGER_ONE.equals(requestModel.getNodeType()) && !dbTCarrierOrder.getStatus().equals(CarrierOrderStatusEnum.WAIT_LOAD.getKey())) {
                //已提货
                throw new BizException(CarrierDataExceptionEnum.ONLY_WAIT_LOAD);
            } else if (CommonConstant.INTEGER_TWO.equals(requestModel.getNodeType()) && !dbTCarrierOrder.getStatus().equals(CarrierOrderStatusEnum.WAIT_UNLOAD.getKey())) {
                //已卸货
                throw new BizException(CarrierDataExceptionEnum.ONLY_WAIT_UNLOAD);
            }
        }

        if (CommonConstant.INTEGER_ONE.equals(requestModel.getNodeType())) {
            for (TCarrierOrder tCarrierOrder : dbTCarrierOrderList) {
                Integer entrustType = tCarrierOrder.getDemandOrderEntrustType();
                //发货、调拨、退货、供应商直配、采购、退货仓库配送、退货调拨、新生销售, 货物完成出库才能提货
                List<Integer> entrustTypeListModel = Arrays.asList(EntrustTypeEnum.DELIVER.getKey(), EntrustTypeEnum.TRANSFERS.getKey(),
                        EntrustTypeEnum.RETURN_GOODS.getKey(), EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey(), EntrustTypeEnum.PROCUREMENT.getKey(),
                        EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey(), EntrustTypeEnum.RETURN_GOODS_TRANSFERS.getKey(), EntrustTypeEnum.LIFE_SALE.getKey());
                if (entrustTypeListModel.contains(entrustType)) {
                    if (!CarrierOrderOutStatusEnum.FINISH_OUT.getKey().equals(tCarrierOrder.getOutStatus())) {
                        throw new BizException(CarrierDataExceptionEnum.NOT_ALLOWED_PICKUP);
                    }
                }
            }
        }

        String carrierOrderIds = StringUtils.listToString(requestModel.getCarrierOrderId(),',');

        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(carrierOrderIds);
        if (!workOrderMap.isEmpty()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }

        Long leyiCompanyEntrustId = commonBiz.getLeyiCompanyEntrustId();
        List<LoadDetailResponseModel> loadDetailList = tCarrierOrderMapper.getLoadDetailByIds(carrierOrderIds);
        for (LoadDetailResponseModel temp : loadDetailList) {
            boolean crossNodeOperation = true;
            for (CarrierOrderGoodsResponseModel tempGoods : temp.getGoodsList()) {
                if (Optional.ofNullable(tempGoods.getLoadAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                    crossNodeOperation = false;
                    break;
                }
            }
            for (CarrierOrderGoodsResponseModel tempGoods : temp.getGoodsList()) {
                tempGoods.setLoadAmount(BigDecimal.ZERO.compareTo(tempGoods.getLoadAmount()) == 0 && crossNodeOperation ? tempGoods.getExpectAmount() : Optional.ofNullable(tempGoods.getLoadAmount()).orElse(BigDecimal.ZERO));
            }
            if (leyiCompanyEntrustId.equals(temp.getCompanyEntrustId())) {
                temp.setIfTray(CommonConstant.INTEGER_ONE);
            }
        }
        return loadDetailList;
    }


    /**
     * 托盘运单货物出库将数量同步物流系统
     * @param requestModel
     * @return
     */
    @Transactional
    public void synCarrierOrderLoadAmount(SyncCarrierOrderLoadAmountRequestModel requestModel){
        //入参校验-运单号不能为空、数量不能为0
        if (StringUtils.isEmpty(requestModel.getCarrierOrderCode()) || CommonConstant.BIG_DECIMAL_ZERO.equals(requestModel.getLoadAmount())){
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }
        //入参校验-货物信息不能为空
        if (ListUtils.isEmpty(requestModel.getTypeAndCountModel())) {
            throw new BizException(CarrierDataExceptionEnum.GOODS_LIST_ERROR);
        }

        //查询运单信息
        TCarrierOrder carrierOrderByCode = tCarrierOrderMapper.getByCode(requestModel.getCarrierOrderCode());
        //tms系统不存在这个运单，则查询网络货运
        if (carrierOrderByCode == null ){
            //查询网络货运
//            com.logistics.carrier.api.feign.carrierorder.management.model.SyncCarrierOrderLoadAmountRequestModel syncCarrierOrderLoadAmountRequestModel
//                    = new com.logistics.carrier.api.feign.carrierorder.management.model.SyncCarrierOrderLoadAmountRequestModel();
//            MapperUtils.mapper(requestModel,syncCarrierOrderLoadAmountRequestModel);
//            carrierClient.synCarrierOrderLoadAmount(syncCarrierOrderLoadAmountRequestModel);
//            return;
            throw new BizException("运单不存在");
        }
        // 运单状态判断
        if (carrierOrderByCode.getStatus() > CarrierOrderStatusEnum.WAIT_LOAD.getKey()){
            throw new BizException(CarrierDataExceptionEnum.CANT_NOT_LOAD);
        }
        //运单取消不能操作
        if(CommonConstant.INTEGER_ONE.equals(carrierOrderByCode.getIfCancel())){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL);
        }
        //运单放空不能操作
        if (CommonConstant.INTEGER_ONE.equals(carrierOrderByCode.getIfEmpty())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EMPTY);
        }

        //更新提货数量
        TCarrierOrder tCarrierOrder = new TCarrierOrder();
        tCarrierOrder.setId(carrierOrderByCode.getId());
        tCarrierOrder.setLastModifiedBy(requestModel.getOperateUserName());
        tCarrierOrder.setLastModifiedTime(requestModel.getCurrentDateTime());
        tCarrierOrder.setLoadAmount(requestModel.getLoadAmount());
        tCarrierOrder.setOutStatus(requestModel.getOutStatus());

        //查询运单货物的的信息
        List<TCarrierOrderGoods> tCarrierOrderGoods = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(carrierOrderByCode.getId().toString());

        //根据sku code更新货物信息
        TCarrierOrderGoods upTCarrierOrderGoods;
        List<TCarrierOrderGoods> upTCarrierOrderGoodsList = new ArrayList<>();
        for (TCarrierOrderGoods tCarrierOrderGood : tCarrierOrderGoods) {
            for (SyncCarrierOrderLoadAmountModel loadAmountModel : requestModel.getTypeAndCountModel()) {
                if (StringUtils.isNotBlank(loadAmountModel.getProductTypeCode()) && loadAmountModel.getProductTypeCode().equals(tCarrierOrderGood.getSkuCode())) {
                    upTCarrierOrderGoods = new TCarrierOrderGoods();
                    upTCarrierOrderGoods.setId(tCarrierOrderGood.getId());
                    upTCarrierOrderGoods.setLoadAmount(ConverterUtils.toBigDecimal(loadAmountModel.getLoadAmount()));
                    upTCarrierOrderGoods.setLastModifiedBy(requestModel.getOperateUserName());
                    upTCarrierOrderGoods.setLastModifiedTime(requestModel.getCurrentDateTime());
                    upTCarrierOrderGoodsList.add(upTCarrierOrderGoods);
                }
            }
        }
        //云盘会在需求单签收前更改sku信息，导致没有匹配到我们这边货物信息
        if (ListUtils.isEmpty(upTCarrierOrderGoodsList)){
            throw new BizException(CarrierDataExceptionEnum.GOODS_LIST_ERROR);
        }

        //更新运单
        tCarrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(tCarrierOrder);
        //更新货物
        tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(upTCarrierOrderGoodsList);
    }

    /**
     * 导出运单回单
     * @param requestModel
     * @return
     */
    public ExportCarrierOrderTicketsResponseModel exportCarrierOrderTickets(ExportCarrierOrderTicketsRequestModel requestModel) {
        List<ExportCarrierOrderTicketsModel> carrierOrderTicketsModelList = tCarrierOrderMapper.exportCarrierOrderTickets(StringUtils.listToString(requestModel.getCarrierOrderIdList(),','));
        if (ListUtils.isEmpty(carrierOrderTicketsModelList)){
            throw new BizException(CarrierDataExceptionEnum.EXPORT_CARRIER_ORDER_TICKETS_ERROR);
        }
        Map<String, List<String>> carrierOrderTicketsMap = new HashMap<>();
        List<String> filePathList;
        for (ExportCarrierOrderTicketsModel ticketsModel : carrierOrderTicketsModelList) {
            StringBuilder fileName = new StringBuilder();
            if (requestModel.getFileNameType().contains(CommonConstant.ONE)){
                fileName.append(ticketsModel.getCarrierOrderCode());
            }
            if (requestModel.getFileNameType().contains(CommonConstant.TWO) && StringUtils.isNotBlank(ticketsModel.getCustomerOrderCode())){
                if (StringUtils.isNotBlank(fileName)) {
                    fileName.append("+");
                }
                fileName.append(ticketsModel.getCustomerOrderCode());
            }
            if (requestModel.getFileNameType().contains(CommonConstant.THREE)){
                if (StringUtils.isNotBlank(fileName)) {
                    fileName.append("+");
                }
                fileName.append(ticketsModel.getVehicleNo());
            }
            if (StringUtils.isBlank(fileName)){
                fileName.append(ticketsModel.getCarrierOrderCode());
            }
            filePathList = carrierOrderTicketsMap.get(ConverterUtils.toString(fileName));
            if (ListUtils.isEmpty(filePathList)) {
                filePathList = ticketsModel.getSignTicketsList();
            }else{
                filePathList.addAll(ticketsModel.getSignTicketsList());
            }
            carrierOrderTicketsMap.put(ConverterUtils.toString(fileName), filePathList);
        }
        ExportCarrierOrderTicketsResponseModel responseModel = new ExportCarrierOrderTicketsResponseModel();
        responseModel.setFileByte(basicDataClient.compressAndDownloadFile(carrierOrderTicketsMap));
        return responseModel;
    }


    /**
     * 检测导出运单回单
     * @param requestModel
     * @return
     */
    public void checkExportCarrierOrderTickets(ExportCarrierOrderTicketsRequestModel requestModel) {
        List<ExportCarrierOrderTicketsModel> carrierOrderTicketsModelList = tCarrierOrderMapper.exportCarrierOrderTickets(StringUtils.listToString(requestModel.getCarrierOrderIdList(),','));
        if (ListUtils.isEmpty(carrierOrderTicketsModelList)){
            throw new BizException(CarrierDataExceptionEnum.EXPORT_CARRIER_ORDER_TICKETS_ERROR);
        }
    }

    /**
     * 查询需求单已调车辆
     * @param
     * @return
     */
    public Map<Long, WebDemandOrderDispatchVehicleRequestModel> countDispatchVehicleMap(List<Long> demandOrderIdList) {
        List<WebDemandOrderDispatchVehicleRequestModel> vehicleRequestModelList = new ArrayList<>();
        if (ListUtils.isNotEmpty(demandOrderIdList)){
            vehicleRequestModelList = tCarrierOrderMapper.selectCarrierOrdersCountVehicleByDemandOrderIds(StringUtils.listToString(demandOrderIdList,','));
        }
        Map<Long, WebDemandOrderDispatchVehicleRequestModel> map = new HashMap<>();
        if (ListUtils.isNotEmpty(vehicleRequestModelList)){
            for (WebDemandOrderDispatchVehicleRequestModel model : vehicleRequestModelList) {
                map.put(model.getDemandOrderId(), model);
            }
        }
        return map;
    }

    /**
     * 前台运单列表
     *
     * @param requestModel 筛选条件
     * @return 运单列表
     */
    public PageInfo<SearchCarrierOrderListForWebResponseModel> searchCarrierOrderListForWeb(SearchCarrierOrderListForWebRequestModel requestModel) {
        processSortParams(requestModel);
        //获取当前登录司机
        Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
            return new PageInfo<>(Collections.emptyList());
        }

        //设置要排除的运单号
        List<Long> needExcludedOrderIds = workOrderBiz.getNeedExcludedOrderIds(WorkOrderTypeEnum.CARRIER_ORDER_TYPE, CommonConstant.INTEGER_TWO, loginUserCompanyCarrierId, null);
        requestModel.setExcludeCarrierOrderIdList(needExcludedOrderIds);

        //查询运单信息
        requestModel.enablePaging();
        List<Long> carrierOrderIdList = tCarrierOrderMapper.searchCarrierOrderIdsForWeb(requestModel, loginUserCompanyCarrierId);
        PageInfo carrierOrderList = new PageInfo(carrierOrderIdList);
        carrierOrderList.setList(new ArrayList());
        if (ListUtils.isNotEmpty(carrierOrderIdList)) {
            String carrierOrderIds = StringUtils.listToString(carrierOrderIdList, ',');
            List<SearchCarrierOrderListForWebResponseModel> searchCarrierOrderListResponseModels = tCarrierOrderMapper.searchCarrierOrderForWeb(requestModel, carrierOrderIds);
            if (ListUtils.isNotEmpty(searchCarrierOrderListResponseModels)) {

                //查询需求单信息
                Map<Long, TDemandOrder> demandOrderMap = tDemandOrderMapper.getByIds(LocalStringUtil.listTostring(searchCarrierOrderListResponseModels.stream().map(SearchCarrierOrderListForWebResponseModel::getDemandOrderId).collect(Collectors.toList()), ','))
                        .stream().collect(Collectors.toMap(TDemandOrder::getId, tDemandOrder -> tDemandOrder));

                //拼接数据
                for (SearchCarrierOrderListForWebResponseModel item : searchCarrierOrderListResponseModels) {
                    TDemandOrder tDemandOrder = demandOrderMap.get(item.getDemandOrderId());
                    if (tDemandOrder != null) {
                        if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(tDemandOrder.getSource())) {
                            item.setCustomerOrderCode("");
                        }

                        item.setOrderType(tDemandOrder.getOrderType());
                        item.setSinopecOrderNo(tDemandOrder.getSinopecOrderNo());
                    }
                    item.setEnableExtCarrierOrder(CommonConstant.ZERO);
                    // 回收入
                    if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(item.getDemandOrderEntrustType())){
                        //运单状态：≠已取消/已放空；
                        Integer ifCancel = item.getIfCancel();
                        Integer ifEmpty = item.getIfEmpty();
                        if (!CommonConstant.INTEGER_ONE.equals(ifCancel) && !CommonConstant.INTEGER_ONE.equals(ifEmpty)){
                            // 非补单
                            if (!item.getIfExtCarrierOrder().equals(CommonConstant.INTEGER_ONE)){
                                //查询是否货物是共享托盘
                                List<TCarrierOrderGoods> tCarrierOrderGoods = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderId(item.getCarrierOrderId());
                                if (ListUtils.isNotEmpty(tCarrierOrderGoods)){
                                    item.setEnableExtCarrierOrder(CommonConstant.ONE);
                                }
                            }
                        }
                    }


                    //工单状态
                    item.setWhetherWorkOrder(needExcludedOrderIds.contains(item.getCarrierOrderId()) ? CommonConstant.INTEGER_ONE : CommonConstant.INTEGER_ZERO);
                }
                carrierOrderList.setList(searchCarrierOrderListResponseModels);
            }
        }
        return carrierOrderList;
    }

    /**
     * 处理前台运单列表排序
     *
     * @param requestModel 排序参数,规则
     */
    private void processSortParams(SearchCarrierOrderListForWebRequestModel requestModel) {
        String sort = requestModel.getSort();
        String order = requestModel.getOrder();
        if (!"desc".equals(order) && !"asc".equals(order)) {
            requestModel.setOrder("asc");
        }
        if ("expectArrivalTime".equals(sort)) {
            requestModel.setSort("tcovh.expect_arrival_time");
        } else {
            requestModel.setSort("tco.created_time");
            requestModel.setOrder("desc");
        }
    }

    /**
     * 前台运单列表tab各状态运单数量统计
     *
     * @param requestModel 筛选条件
     * @return 运单列表tab各状态运单数量统计
     */
    public CarrierOrderStatisticsResponseModel carrierOrderStatisticsForWeb(SearchCarrierOrderListForWebRequestModel requestModel) {
        Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        if (companyCarrierId == null || CommonConstant.LONG_ZERO.equals(companyCarrierId)) {
            return new CarrierOrderStatisticsResponseModel();
        }

        //设置要排除的运单号
        requestModel.setExcludeCarrierOrderIdList(workOrderBiz.getNeedExcludedOrderIds(WorkOrderTypeEnum.CARRIER_ORDER_TYPE, CommonConstant.INTEGER_TWO, companyCarrierId, null));

        requestModel.setOrder(null);
        requestModel.setSort(null);
        List<Long> carrierOrderIds = tCarrierOrderMapper.searchCarrierOrderIdsForWeb(requestModel, companyCarrierId);
        CarrierOrderStatisticsResponseModel model = new CarrierOrderStatisticsResponseModel();
        if (ListUtils.isNotEmpty(carrierOrderIds)) {
            model = tCarrierOrderMapper.statistics(StringUtils.listToString(carrierOrderIds, ','), companyCarrierId);
        }
        return model;
    }

    /**
     * 查看运单详情
     *
     * @param requestModel 运单ID
     * @return 运单详情
     */
    public CarrierOrderDetailForWebResponseModel carrierOrderDetailForWeb(CarrierOrderDetailRequestModel requestModel) {
        if(requestModel == null || requestModel.getCarrierOrderId() == null){
            throw  new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }


        //查询运单
        TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (tCarrierOrder == null) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //查询运单货物明细
        List<TCarrierOrderGoods> tCarrierOrderGoods = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(tCarrierOrder.getId().toString());
        String tCarrierOrderGoodIds = tCarrierOrderGoods.stream().map(orderGoods -> orderGoods.getId().toString()).collect(Collectors.joining(","));

        //查询运单货物编码明细
        List<TCarrierOrderGoodsCode> tCarrierOrderGoodsCodes = tCarrierOrderGoodsCodeMapper.selectGoodsCodeByGoodsIds(tCarrierOrderGoodIds);
        Map<Long, List<TCarrierOrderGoodsCode>> tCarrierOrderGoodsCodesMap = tCarrierOrderGoodsCodes.stream().collect(Collectors.groupingBy(TCarrierOrderGoodsCode::getCarrierOrderGoodsId));

        //查询运单车辆历史
        List<TCarrierOrderVehicleHistory> tCarrierOrderVehicleHistories = tCarrierOrderVehicleHistoryMapper.getAllByCarrierOrderIds(tCarrierOrder.getId().toString());

        //查询运单地址
        TCarrierOrderAddress tCarrierOrderAddress = tCarrierOrderAddressMapper.getByCarrierOrderId(tCarrierOrder.getId());

        //查询运单事件
        List<TCarrierOrderEvents> tCarrierOrderEvents = tCarrierOrderEventsMapper.selectByCarrierOrderId(tCarrierOrder.getId());



        CarrierOrderDetailForWebResponseModel carrierOrderDetailResponseModel = new CarrierOrderDetailForWebResponseModel();
        carrierOrderDetailResponseModel.setCarrierOrderId(tCarrierOrder.getId());
        carrierOrderDetailResponseModel.setDemandOrderId(tCarrierOrder.getDemandOrderId());
//        carrierOrderDetailResponseModel.setDemandOrderCode(tCarrierOrder.getDemandOrderCode());
        carrierOrderDetailResponseModel.setCarrierOrderCode(tCarrierOrder.getCarrierOrderCode());
        carrierOrderDetailResponseModel.setCustomerOrderCode(tCarrierOrder.getCustomerOrderCode());
        carrierOrderDetailResponseModel.setStatus(tCarrierOrder.getStatus());
        carrierOrderDetailResponseModel.setIfCancel(tCarrierOrder.getIfCancel());
        carrierOrderDetailResponseModel.setIfEmpty(tCarrierOrder.getIfEmpty());
        carrierOrderDetailResponseModel.setCancelReason(tCarrierOrder.getCancelReason());
        carrierOrderDetailResponseModel.setDispatchUserName(tCarrierOrder.getDispatchUserName());
        carrierOrderDetailResponseModel.setRemark(tCarrierOrder.getRemark());
//        carrierOrderDetailResponseModel.setDispatchTime(tCarrierOrder.getDispatchTime());
        carrierOrderDetailResponseModel.setGoodsUnit(tCarrierOrder.getGoodsUnit());
        carrierOrderDetailResponseModel.setExpectAmount(tCarrierOrder.getExpectAmount());
        carrierOrderDetailResponseModel.setLoadAmount(tCarrierOrder.getLoadAmount());
        carrierOrderDetailResponseModel.setUnloadAmount(tCarrierOrder.getUnloadAmount());
        carrierOrderDetailResponseModel.setSignAmount(tCarrierOrder.getSignAmount());
        carrierOrderDetailResponseModel.setEntrustCompany(tCarrierOrder.getCompanyEntrustName());
//        carrierOrderDetailResponseModel.setCompanyCarrierId(tCarrierOrder.getCompanyCarrierId());
//        carrierOrderDetailResponseModel.setEntrustType(tCarrierOrder.getDemandOrderEntrustType());
//        carrierOrderDetailResponseModel.setEntrustSettlementTonnage(tCarrierOrder.getSettlementTonnage());
//        carrierOrderDetailResponseModel.setCarrierSettlementTonnage(tCarrierOrder.getCarrierSettlement());
        carrierOrderDetailResponseModel.setCarrierSettleStatementStatus(tCarrierOrder.getCarrierSettleStatementStatus());
        carrierOrderDetailResponseModel.setIfRecycleByCode(tCarrierOrder.getIfRecycleByCode());

        //地址相关
        CarrierOrderDetailBasicInfoModel carrierOrderDetailBasicInfo = new CarrierOrderDetailBasicInfoModel();
        carrierOrderDetailBasicInfo.setLoadProvinceName(tCarrierOrderAddress.getLoadProvinceName());
        carrierOrderDetailBasicInfo.setLoadCityName(tCarrierOrderAddress.getLoadCityName());
        carrierOrderDetailBasicInfo.setLoadAreaName(tCarrierOrderAddress.getLoadAreaName());
        carrierOrderDetailBasicInfo.setLoadDetailAddress(tCarrierOrderAddress.getLoadDetailAddress());
        carrierOrderDetailBasicInfo.setLoadWarehouse(tCarrierOrderAddress.getLoadWarehouse());
        carrierOrderDetailBasicInfo.setConsignorName(tCarrierOrderAddress.getConsignorName());
        carrierOrderDetailBasicInfo.setConsignorMobile(tCarrierOrderAddress.getConsignorMobile());
        carrierOrderDetailBasicInfo.setUnloadProvinceName(tCarrierOrderAddress.getUnloadProvinceName());
        carrierOrderDetailBasicInfo.setUnloadCityName(tCarrierOrderAddress.getUnloadCityName());
        carrierOrderDetailBasicInfo.setUnloadAreaName(tCarrierOrderAddress.getUnloadAreaName());
        carrierOrderDetailBasicInfo.setUnloadDetailAddress(tCarrierOrderAddress.getUnloadDetailAddress());
        carrierOrderDetailBasicInfo.setUnloadWarehouse(tCarrierOrderAddress.getUnloadWarehouse());
        carrierOrderDetailBasicInfo.setReceiverName(tCarrierOrderAddress.getReceiverName());
        carrierOrderDetailBasicInfo.setReceiverMobile(tCarrierOrderAddress.getReceiverMobile());
        carrierOrderDetailBasicInfo.setLoadTime(tCarrierOrder.getLoadTime());
        carrierOrderDetailBasicInfo.setUnloadTime(tCarrierOrder.getUnloadTime());
        carrierOrderDetailResponseModel.setCarrierOrderDetailBasicInfo(carrierOrderDetailBasicInfo);

        //货物信息
        List<CarrierOrderDetailGoodsInfoModel> carrierOrderDetailGoodsInfo = new ArrayList<>();
        for (TCarrierOrderGoods tCarrierOrderGood : tCarrierOrderGoods) {

            if (CarrierOrderIfRecycleByCodeEnum.RECYCLE_BY_CODE.getCode().equals(tCarrierOrder.getIfRecycleByCode())
                    && CarrierOrderStatusEnum.WAIT_LOAD.getKey().compareTo(tCarrierOrder.getStatus()) < 0) {
                List<TCarrierOrderGoodsCode> carrierOrderGoodsCodes = tCarrierOrderGoodsCodesMap.getOrDefault(tCarrierOrderGood.getId(), new ArrayList<>());

                for (TCarrierOrderGoodsCode carrierOrderGoodsCode : carrierOrderGoodsCodes) {
                    CarrierOrderDetailGoodsInfoModel carrierOrderDetailGoodsInfoModel = new CarrierOrderDetailGoodsInfoModel();
                    carrierOrderDetailGoodsInfoModel.setYeloCode(carrierOrderGoodsCode.getYeloGoodCode());
                    carrierOrderDetailGoodsInfoModel.setGoodsId(tCarrierOrderGood.getId());
                    carrierOrderDetailGoodsInfoModel.setGoodsName(tCarrierOrderGood.getGoodsName());
                    carrierOrderDetailGoodsInfoModel.setLength(tCarrierOrderGood.getLength());
                    carrierOrderDetailGoodsInfoModel.setWidth(tCarrierOrderGood.getWidth());
                    carrierOrderDetailGoodsInfoModel.setHeight(tCarrierOrderGood.getHeight());
                    carrierOrderDetailGoodsInfoModel.setGoodsSize(tCarrierOrderGood.getGoodsSize());
                    carrierOrderDetailGoodsInfoModel.setExpectAmount(tCarrierOrderGood.getExpectAmount());
                    carrierOrderDetailGoodsInfoModel.setLoadAmount(carrierOrderGoodsCode.getLoadAmount());
                    carrierOrderDetailGoodsInfoModel.setUnloadAmount(carrierOrderGoodsCode.getUnloadAmount());
                    carrierOrderDetailGoodsInfoModel.setSignAmount(carrierOrderGoodsCode.getSignAmount());
                    carrierOrderDetailGoodsInfo.add(carrierOrderDetailGoodsInfoModel);
                }
            }else {
                CarrierOrderDetailGoodsInfoModel carrierOrderDetailGoodsInfoModel = new CarrierOrderDetailGoodsInfoModel();
                carrierOrderDetailGoodsInfoModel.setGoodsId(tCarrierOrderGood.getId());
                carrierOrderDetailGoodsInfoModel.setGoodsName(tCarrierOrderGood.getGoodsName());
                carrierOrderDetailGoodsInfoModel.setLength(tCarrierOrderGood.getLength());
                carrierOrderDetailGoodsInfoModel.setWidth(tCarrierOrderGood.getWidth());
                carrierOrderDetailGoodsInfoModel.setHeight(tCarrierOrderGood.getHeight());
                carrierOrderDetailGoodsInfoModel.setGoodsSize(tCarrierOrderGood.getGoodsSize());
                carrierOrderDetailGoodsInfoModel.setExpectAmount(tCarrierOrderGood.getExpectAmount());
                carrierOrderDetailGoodsInfoModel.setLoadAmount(tCarrierOrderGood.getLoadAmount());
                carrierOrderDetailGoodsInfoModel.setUnloadAmount(tCarrierOrderGood.getUnloadAmount());
                carrierOrderDetailGoodsInfoModel.setSignAmount(tCarrierOrderGood.getSignAmount());
                carrierOrderDetailGoodsInfo.add(carrierOrderDetailGoodsInfoModel);

            }
        }
        carrierOrderDetailResponseModel.setCarrierOrderDetailGoodsInfo(carrierOrderDetailGoodsInfo);

        //车辆信息
        List<CarrierOrderDetailVehicleDriverInfoModel> carrierOrderDetailVehicleDriverInfo = new ArrayList<>();
        for (TCarrierOrderVehicleHistory tCarrierOrderVehicleHistory : tCarrierOrderVehicleHistories) {
            CarrierOrderDetailVehicleDriverInfoModel driverInfoModel = new CarrierOrderDetailVehicleDriverInfoModel();
            driverInfoModel.setVehicleHistoryId(tCarrierOrderVehicleHistory.getId().toString());
            driverInfoModel.setAuditStatus(tCarrierOrderVehicleHistory.getAuditStatus());
            driverInfoModel.setIfInvalid(tCarrierOrderVehicleHistory.getIfInvalid());
            driverInfoModel.setVehicleNo(tCarrierOrderVehicleHistory.getVehicleNo());
            driverInfoModel.setDriverName(tCarrierOrderVehicleHistory.getDriverName());
            driverInfoModel.setDriverMobile(tCarrierOrderVehicleHistory.getDriverMobile());
            driverInfoModel.setRejectReason(tCarrierOrderVehicleHistory.getRejectReason());
            driverInfoModel.setRemark(tCarrierOrderVehicleHistory.getRemark());
            driverInfoModel.setCreatedTime(tCarrierOrderVehicleHistory.getCreatedTime());
            carrierOrderDetailVehicleDriverInfo.add(driverInfoModel);
        }
        carrierOrderDetailResponseModel.setCarrierOrderDetailVehicleDriverInfo(carrierOrderDetailVehicleDriverInfo);

        List<CarrierOrderDetailEventModel> carrierOrderDetailEvents = new ArrayList<>();
        for (TCarrierOrderEvents tCarrierOrderEvent : tCarrierOrderEvents) {
            CarrierOrderDetailEventModel carrierOrderDetailEventModel = new CarrierOrderDetailEventModel();
            carrierOrderDetailEventModel.setEventId(tCarrierOrderEvent.getId());
            carrierOrderDetailEventModel.setEvent(tCarrierOrderEvent.getEvent());
            carrierOrderDetailEventModel.setEventDesc(tCarrierOrderEvent.getEventDesc());
            carrierOrderDetailEventModel.setEventTime(tCarrierOrderEvent.getEventTime());
            carrierOrderDetailEvents.add(carrierOrderDetailEventModel);
        }
        carrierOrderDetailResponseModel.setCarrierOrderDetailEvents(carrierOrderDetailEvents);




//        CarrierOrderDetailForWebResponseModel carrierOrderDetailResponseModel = tCarrierOrderMapper.getCarrierOrderDetailForWebById(requestModel.getCarrierOrderId(),commonBiz.getLoginUserCompanyCarrierId());
//        if(carrierOrderDetailResponseModel!=null) {
//
//
//        }

        //车辆司机排序
        carrierOrderDetailResponseModel.getCarrierOrderDetailVehicleDriverInfo().sort((o1, o2) -> o2.getCreatedTime().compareTo(o1.getCreatedTime()));
        //运单事件排序
        carrierOrderDetailResponseModel.getCarrierOrderDetailEvents().sort(Comparator.comparing(CarrierOrderDetailEventModel::getEventTime));

        //查询对应的需求单
        TDemandOrder tDemandOrder = tDemandOrderMapper.selectByPrimaryKeyDecrypt(carrierOrderDetailResponseModel.getDemandOrderId());
        if (tDemandOrder != null && IfValidEnum.VALID.getKey().equals(tDemandOrder.getValid())) {
            if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(tDemandOrder.getSource())){
                carrierOrderDetailResponseModel.setCustomerOrderCode("");
            }

            carrierOrderDetailResponseModel.setOrderType(tDemandOrder.getOrderType());
            carrierOrderDetailResponseModel.setSinopecOrderNo(tDemandOrder.getSinopecOrderNo());
        }

        //查询票据信息
        carrierOrderDetailResponseModel.setCarrierOrderDetailTickets(tCarrierOrderTicketsMapper.getTicketsCountByCarrierOrderIdForWeb(carrierOrderDetailResponseModel.getCarrierOrderId()));
        // 查询回单审核状态
        Optional.ofNullable(tDemandOrder)
                .filter(f -> carrierOrderTicketsAuditBiz.isReceiptEntrustType(f.getSource()))
                .ifPresent(order -> {
                    TCarrierOrderTicketsAudit receiptAuditDetail = carrierOrderTicketsAuditBiz.getReceiptAuditByCarrierOrderId(carrierOrderDetailResponseModel.getCarrierOrderId());
                    if (Objects.nonNull(receiptAuditDetail)) {
                        carrierOrderDetailResponseModel.getCarrierOrderDetailTickets()
                                .setTicketsAuditStatus(receiptAuditDetail.getTicketsAuditStatus());
                    }
                });
        return carrierOrderDetailResponseModel;
    }

    /**
     * 查看运单详情单据查看
     *
     * @param requestModel 运单ID
     * @return 运单详情单据图片
     */
    public ViewCarrierOrderTicketsResponseModel viewCarrierOrderTickets(CarrierOrderDetailRequestModel requestModel) {
        ViewCarrierOrderTicketsResponseModel responseModel = tCarrierOrderMapper.selectVehicleHistoryByCarrierOrderId(requestModel.getCarrierOrderId());
        if (responseModel == null) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        responseModel.setTicketsList(tCarrierOrderTicketsMapper.getTicketsByCarrierOrderIdByWeb(requestModel.getCarrierOrderId()));
        return responseModel;
    }

    /**
     * 根据运单id查询司机车辆信息(修改车辆时)
     *
     * @param requestModel 运单ID
     * @return 运单车辆司机信息
     */
    public DriverVehicleInfoByCarrierOrderIdResponseModel getDriverVehicleInfoByCarrierOrderIdForWeb(CarrierOrderDetailRequestModel requestModel) {
        //查询运单并校验
        List<TCarrierOrder> tCarrierOrders = carrierOrderCommonBiz.checkCarrierOrderIfExist(Collections.singletonList(requestModel.getCarrierOrderId()));
        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(requestModel.getCarrierOrderId().toString());
        if (!workOrderMap.isEmpty()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }
        TCarrierOrder tCarrierOrder = tCarrierOrders.get(CommonConstant.INTEGER_ZERO);

        //拷贝属性
        DriverAndVehicleResponseModel driverVehicleInfoByCarrierOrderId = carrierOrderVehicleHistoryMapper.getDriverVehicleInfoByCarrierOrderId(requestModel.getCarrierOrderId());
        DriverVehicleInfoByCarrierOrderIdResponseModel responseModel = MapperUtils.mapper(driverVehicleInfoByCarrierOrderId, DriverVehicleInfoByCarrierOrderIdResponseModel.class);
        responseModel.setEntrustType(tCarrierOrder.getDemandOrderEntrustType());//委托类型
        responseModel.setCompanyCarrierId(tCarrierOrder.getCompanyCarrierId());//车主ID
        return responseModel;
    }


    /**
     * 打印提货单
     *
     * @param requestModel 运单ID
     */
    public void printLadingBill(DownloadLadingBillRequestModel requestModel) {
        TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (null == tCarrierOrder || IfValidEnum.INVALID.getKey().equals(tCarrierOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        commonBiz.checkAppCarrierPermission();
        TCarrierOrder carrierOrder = new TCarrierOrder();
        carrierOrder.setId(tCarrierOrder.getId());
        carrierOrder.setPrintCount(tCarrierOrder.getPrintCount() + 1);
        commonBiz.setBaseEntityModify(carrierOrder, BaseContextHandler.getUserName());
        tCarrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(carrierOrder);
    }

    /**
     * 统计司机运单数量
     *
     * @param driverId
     * @param status
     * @return
     */
    public int getCountByDriverId(Long driverId, String companyCarrierIds, List<Integer> status) {
        if (driverId == null || StringUtils.isBlank(companyCarrierIds)) {
            return CommonConstant.INTEGER_ZERO;
        }
        List<Long> needExcludedOrderIds = workOrderBiz.getNeedExcludedOrderIds(WorkOrderTypeEnum.CARRIER_ORDER_TYPE, CommonConstant.INTEGER_THREE, null, Collections.singletonList(driverId));
        return tCarrierOrderMapper.getCountByDriverId(driverId, companyCarrierIds, status, needExcludedOrderIds);
    }

    public DownloadLadingBillByCarrierCodeResponseModel downloadLadingBillByCarrierCode(CarrierOrderCodeRequestModel requestModel) {
        DownloadLadingBillByCarrierCodeResponseModel downloadLadingBillResponseModel = tCarrierOrderMapper.downloadLadingBillByCarrierCode(requestModel.getCarrierOrderCode());

        if(downloadLadingBillResponseModel == null){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //运单还没有生成条形码,生成条形码并上传到OSS
        if (StringUtils.isBlank(downloadLadingBillResponseModel.getQrCodePicPath())) {
            //二维码参数
            Map<String, Object> qrCodeParamsMap = DriverAppletQrCodeJumpPageEnum.CARRIER_ORDER_DETAIL_PAGE.getParamsMap();
            qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_CODE.getKey(), downloadLadingBillResponseModel.getCarrierOrderCode());
            if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(downloadLadingBillResponseModel.getEntrustType())
                    || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(downloadLadingBillResponseModel.getEntrustType())){
                qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_BILL_TYPE.getKey(), QrCodeParamsBillTypeEnum.RECYCLE.getKey());
            }else {
                qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_BILL_TYPE.getKey(), QrCodeParamsBillTypeEnum.OTHER.getKey());
            }
            byte[] qrCodePicByte = commonBiz.createQrCode(configKeyConstant.qrcodeCommonPrefix, qrCodeParamsMap).getFileByte();
            downloadLadingBillResponseModel.setQrCodePicByte(qrCodePicByte);

            //更新运单条形码图片路径
            if (qrCodePicByte != null) {
                //捕获异常目的：二维码是否上传成功不影响下载提货单，下次触发的时候再上传
                try {
                    //上传oss
                    String fileName = downloadLadingBillResponseModel.getCarrierOrderCode() + CommonConstant.POINT + CommonConstant.PIC_JPG;
                    FileUploadRequestModel fileUploadRequestModel = new FileUploadRequestModel();
                    fileUploadRequestModel.setFilePath(configKeyConstant.imageUploadCatalog + configKeyConstant.carrierOrderQrCode);
                    fileUploadRequestModel.setFileName(fileName);
                    fileUploadRequestModel.setFileByte(qrCodePicByte);
                    basicDataClient.uploadFileOSS(fileUploadRequestModel);
                    String qrCodePicPath = configKeyConstant.carrierOrderQrCode + "/" + fileName;

                    //更新运单条形码图片路径
                    TCarrierOrder tCarrierOrder = new TCarrierOrder();
                    tCarrierOrder.setId(downloadLadingBillResponseModel.getCarrierOrderId());
                    tCarrierOrder.setQrCodePicPath(qrCodePicPath);
                    commonBiz.setBaseEntityModify(tCarrierOrder, BaseContextHandler.getUserName());
                    tCarrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(tCarrierOrder);
                }catch (Exception e){
                    log.info("二维码图片上传失败：", e);
                }
            }
        }
        return downloadLadingBillResponseModel;
    }
}
