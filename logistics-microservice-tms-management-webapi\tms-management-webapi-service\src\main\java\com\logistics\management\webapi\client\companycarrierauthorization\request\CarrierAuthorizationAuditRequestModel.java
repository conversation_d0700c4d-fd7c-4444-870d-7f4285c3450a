package com.logistics.management.webapi.client.companycarrierauthorization.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierAuthorizationAuditRequestModel {

    @ApiModelProperty(value = "车主授权信息id", required = true)
    private Long carrierAuthorizationId;

    @ApiModelProperty(value = "审核类型: 1:通过 ,2:驳回", required = true)
    private Integer auditModel;

    @ApiModelProperty(value = "是否归档, 0:否 1:是", required = true)
    private Integer isArchived;

    @ApiModelProperty(value = "备注")
    private String remark;
}
