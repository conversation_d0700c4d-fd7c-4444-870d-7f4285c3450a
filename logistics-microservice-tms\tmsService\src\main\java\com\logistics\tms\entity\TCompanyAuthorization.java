package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2023/01/09
*/
@Data
public class TCompanyAuthorization extends BaseEntity {
    /**
    * 企业主表id
    */
    @ApiModelProperty("企业主表id")
    private Long companyId;

    /**
    * 企业类型 1 车主
    */
    @ApiModelProperty("企业类型 1 车主")
    private Integer companyType;

    /**
    * 审核状态: 0 待授权，1 待审核，2 已驳回，3 已授权
    */
    @ApiModelProperty("审核状态: 0 待授权，1 待审核，2 已驳回，3 已授权")
    private Integer auditStatus;

    /**
    * 是否归档：0 未归档，1 已归档
    */
    @ApiModelProperty("是否归档：0 未归档，1 已归档")
    private Integer isArchive;

    /**
    * 审核人
    */
    @ApiModelProperty("审核人")
    private String auditorName;

    /**
    * 审核时间
    */
    @ApiModelProperty("审核时间")
    private Date auditTime;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 归档人
    */
    @ApiModelProperty("归档人")
    private String archivedName;

    /**
    * 归档时间
    */
    @ApiModelProperty("归档时间")
    private Date archivedTime;
}