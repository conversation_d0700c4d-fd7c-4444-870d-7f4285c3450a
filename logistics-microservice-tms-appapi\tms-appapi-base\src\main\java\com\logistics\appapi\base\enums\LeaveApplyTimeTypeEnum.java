/**
 * Created by yun.zhou on 2017/12/12.
 */
package com.logistics.appapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum LeaveApplyTimeTypeEnum {
    DEFAULT(-1, ""),
    MORNING(1, "上午"),
    AFTERNOON(2, "下午"),
    ;

    private Integer key;
    private String value;

    public static LeaveApplyTimeTypeEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
