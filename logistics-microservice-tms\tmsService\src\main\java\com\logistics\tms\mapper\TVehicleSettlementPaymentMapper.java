package com.logistics.tms.mapper;

import com.logistics.tms.controller.vehiclesettlement.response.ReconciliationBillingRecordsResponseModel;
import com.logistics.tms.entity.TVehicleSettlementPayment;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TVehicleSettlementPaymentMapper extends BaseMapper<TVehicleSettlementPayment> {

    List<TVehicleSettlementPayment> getByVehicleSettlementId(@Param("vehicleSettlementId")Long vehicleSettlementId);

    List<ReconciliationBillingRecordsResponseModel> driverReconciliationBillingRecords(@Param("vehicleSettlementId")Long vehicleSettlementId, @Param("driverId") Long driverId);

}