package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ModifyCarrierDetailResponseModel {

    @ApiModelProperty("需求单ID")
    private Long demandOrderId;

    @ApiModelProperty(value = "车主ID")
    private Long companyCarrierId;

    @ApiModelProperty(value = "车主名称")
    private String companyCarrierName;

    @ApiModelProperty(value = "车主公司类型：1 公司，2 个人")
    private Integer companyCarrierType;

    @ApiModelProperty("车主账号名称")
    private String carrierContactName;

    @ApiModelProperty("车主账号手机号（原长度50）")
    private String carrierContactPhone;

    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;

    @ApiModelProperty(value = "车主报价")
    private BigDecimal carrierPrice;

    @ApiModelProperty(value = "价格类型 1 单价 2 一口价")
    private Integer carrierPriceType;

    @ApiModelProperty(value = "货物数量")
    private BigDecimal goodsAmount;
}
