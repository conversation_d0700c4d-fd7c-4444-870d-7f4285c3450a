package com.logistics.tms.controller.dispatchorder.request;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SearchSpecialDispatchIfMatchFreightDemandReqModel {

    /**
     * 需求单id
     */
    private Long demandId;


    /**
     * 调度数量
     */
    private BigDecimal count;




    /**
     * 发货地仓库 2.42
     */
    private String loadProvinceName;
    /**
     * 发货地市 2.42
     */
    private String loadCityName;
    /**
     * 发货地区 2.42
     */
    private String loadAreaName;
    /**
     * 发货地详细地址  2.42
     */
    private String loadDetailAddress;

    /**
     * 收货地省 2.42
     */
    private String unloadProvinceName;
    /**
     * 收货地市 2.42
     */
    private String unloadCityName;
    /**
     * 收货地区 2.42
     */
    private String unloadAreaName;
    /**
     * 收货地详细地址 2.42
     */
    private String unloadDetailAddress;

    /**
     * 排序
     */
    private Integer orderNum;
    /**
     * 到下个点位距离
     */
    private BigDecimal nextPointDistance ;

}
