package com.logistics.appapi.client.website.demand;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.website.demand.hystrix.DemandSourceClientHystrix;
import com.logistics.appapi.client.website.demand.request.AddDemandSourceRequestModel;
import com.logistics.appapi.client.website.demand.request.DemandSourceListRequestModel;
import com.logistics.appapi.client.website.demand.response.DemandSourceListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2024/3/15 11:03
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        fallback = DemandSourceClientHystrix.class)
public interface DemandSourceClient {

    @ApiOperation(value = "货源列表接口")
    @PostMapping(value = "/service/demandSource/searchList")
    Result<PageInfo<DemandSourceListResponseModel>> searchList(@RequestBody DemandSourceListRequestModel requestModel);

    @ApiOperation(value = "发布货源")
    @PostMapping(value = "/service/demandSource/add")
    Result<Boolean> add(@RequestBody AddDemandSourceRequestModel requestModel);
}
