package com.logistics.tms.controller.companyaccount;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.controller.companyaccount.request.CompanyAccountAddRequestModel;
import com.logistics.tms.controller.companyaccount.request.CompanyAccountEnabledRequestModel;
import com.logistics.tms.controller.companyaccount.request.CompanyAccountImageRequestModel;
import com.logistics.tms.controller.companyaccount.request.SearchCompanyAccountRequestModel;
import com.logistics.tms.controller.companyaccount.response.CompanyAccountImageResponseModel;
import com.logistics.tms.controller.companyaccount.response.SearchCompanyAccountResponseModel;
import com.logistics.tms.biz.companyaccount.CompanyAccountBiz;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/6
 */
@Api(value = "API - CompanyEntrustServiceApi-公司账户管理")
@RestController
public class CompanyAccountController{

	@Autowired
	private CompanyAccountBiz companyAccountBiz;

	/**
	 * 新增公司账户
	 *
	 * @param requestModel 公司账户信息
	 * @return 操作结果
	 */
	@ApiOperation(value = "新增公司账户")
	@PostMapping("/service/companyAccount/addAccount")
	public Result<Boolean> addAccount(@RequestBody CompanyAccountAddRequestModel requestModel) {
		companyAccountBiz.addAccount(requestModel);
		return Result.success(true);
	}

	/**
	 * 公司账户列表
	 *
	 * @param requestModel 筛选条件
	 * @return 公司账户列表
	 */
	@ApiOperation(value = "公司账户查询列表")
	@PostMapping("/service/companyAccount/searchList")
	public Result<PageInfo<SearchCompanyAccountResponseModel>> searchList(@RequestBody SearchCompanyAccountRequestModel requestModel) {
		return Result.success(companyAccountBiz.searchList(requestModel));
	}

	/**
	 * 查看银行账户图片
	 *
	 * @param requestModel 公司银行账户id
	 * @return 银行账户图片
	 */
	@ApiOperation(value = "公司账户图片查询")
	@PostMapping("/service/companyAccount/getAccountImageList")
	public Result<CompanyAccountImageResponseModel> getAccountImageList(@RequestBody @Valid CompanyAccountImageRequestModel requestModel) {
		return Result.success(companyAccountBiz.getAccountImageList(requestModel));
	}

	/**
	 * 公司账户启用/禁用
	 *
	 * @param requestModel 启用/禁用
	 * @return 操作结果
	 */
	@ApiOperation(value = "公司账户禁用/启用")
	@PostMapping("/service/companyAccount/enabled")
	public Result<Boolean> enabled(@RequestBody CompanyAccountEnabledRequestModel requestModel) {
		companyAccountBiz.enabled(requestModel);
		return Result.success(true);
	}
}
