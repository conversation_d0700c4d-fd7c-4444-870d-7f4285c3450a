package com.logistics.tms.controller.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class SearchWarehouseRequestModel {

	@ApiModelProperty("发货人地址-发货省")
	private String loadProvinceName;

	@ApiModelProperty("发货人地址-发货市")
	private String loadCityName;

	@ApiModelProperty("发货人地址-发货区")
	private String loadAreaName;

	@ApiModelProperty("发货人地址-发货详细地址")
	private String loadDetailAddress;

	@ApiModelProperty("发货人地址-发货仓库")
	private String loadWarehouse;

	@ApiModelProperty("搜索方式：1 距离优先，2 综合")
	private String searchType;

	@ApiModelProperty("仓库名称，仓库地址，仓库联系人 搜索条件")
	private String warehouseCondition;
}
