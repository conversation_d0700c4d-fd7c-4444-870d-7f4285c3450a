package com.logistics.tms.mapper;

import com.logistics.tms.controller.routeenquiry.response.GetRouteEnquiryQuoteDetailResponseModel;
import com.logistics.tms.entity.TRouteEnquiryAddressQuote;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/07/09
*/
@Mapper
public interface TRouteEnquiryAddressQuoteMapper extends BaseMapper<TRouteEnquiryAddressQuote> {

    List<TRouteEnquiryAddressQuote> getByCompanyId(@Param("routeEnquiryCompanyId")Long routeEnquiryCompanyId);

    List<GetRouteEnquiryQuoteDetailResponseModel> getQuoteDetail(@Param("routeEnquiryCompanyId")Long routeEnquiryCompanyId);

}