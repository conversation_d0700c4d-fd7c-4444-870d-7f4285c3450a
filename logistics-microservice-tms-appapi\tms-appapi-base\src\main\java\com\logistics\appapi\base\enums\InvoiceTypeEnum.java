/**
 * Created by yun.zhou on 2017/12/12.
 */
package com.logistics.appapi.base.enums;

public enum InvoiceTypeEnum {
    DEFAULT("-1", ""),
    VALUE_ADDED_TAX("1", "增值税发票"),
    TAXI("2", "出租车发票"),
    TRAIN("3", "火车票"),

    QUOTA("4", "定额发票"),

    ROLL_TICKET("5", "卷票"),
    MACHINE_PRINTING("6", "机打发票"),
    PASSING_BY("7", "过路过桥费发票"),
    ;

    private final String key;
    private final String value;

    InvoiceTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static InvoiceTypeEnum getEnum(String key) {
        for (InvoiceTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
