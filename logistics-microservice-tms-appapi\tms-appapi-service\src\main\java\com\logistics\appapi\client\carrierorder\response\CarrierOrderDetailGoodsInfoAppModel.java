package com.logistics.appapi.client.carrierorder.response;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CarrierOrderDetailGoodsInfoAppModel {

    @ApiModelProperty("货物id")
    private Long goodsId;
    @ApiModelProperty("需求单货物id")
    private Long demandOrderGoodsId;
    @ApiModelProperty("品名")
    private String goodsName;
    @ApiModelProperty("预提件数")
    private BigDecimal expectAmount;
    @ApiModelProperty("实提件数")
    private BigDecimal loadAmount;
    @ApiModelProperty("实卸件数")
    private BigDecimal unloadAmount;
    @ApiModelProperty("签收件数")
    private BigDecimal signAmount;
    @ApiModelProperty("长")
    private Integer length;
    @ApiModelProperty("宽")
    private Integer width;
    @ApiModelProperty("高")
    private Integer height;
    @ApiModelProperty("规格")
    private String goodsSize;

    @ApiModelProperty(value = "货物的编码集合 v2.44")
    private List<CarrierOrderDetailCodeModel> codeDtoList;
}
