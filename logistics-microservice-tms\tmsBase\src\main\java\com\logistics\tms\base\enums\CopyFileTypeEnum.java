package com.logistics.tms.base.enums;

public enum CopyFileTypeEnum {
    COMPANY_CARRIER_TRADING(1,"承运商营业执照"),
    COMPANY_CARRIER_AUTHORIZATION(2,"授权书"),
    COMPANY_CARRIER_ROAD_TRANSPORT (3,"道路许可证"),
    CUSTOMER_AUTHORIZATION(4,"承运商账号授权书"),
    CUSTOMER_CARRIER_IDENTITY_FACE(5,"身份证人面像"),
    CUSTOMER_CARRIER_IDENTITY_NATIONAL(6,"身份证国徽面"),
    CARRIER_ORDER_LOAD_TICKETS(7,"提货单"),
    CARRIER_ORDER_UNLOAD_TICKETS(8,"出库单"),
    CARRIER_ORDER_SIGN_TICKETS(9,"签收单"),
    CARRIER_ORDER_OTHER_TICKETS(10,"其他单据"),
    CARRIER_ORDER_ARRIVE_PICK_UP(11,"到达装货地凭证"),
    CARRIER_ORDER_ARRIVE_UNLOADING(12,"到达卸货地凭证"),
    VEHICLE_DRIVING_LICENSE(13,"车辆行驶证"),
    ANNOUNCEMENT_PICTURE(14,"公告图片"),
    PERSONAL_ACCIDENT_INSURANCE(15,"个人意外险保单"),
    VIOLATION_REGULATIONS(16,"违章事故凭证"),
    DRIVING_LICENSE_ANNUAL_REVIEW(17,"行驶证年审记录凭证"),
    OCCUPATIONAL_RECORD(18,"司机从业资格证"),
    INTEGRITY_EXAMINATION_RECORD(19,"司机诚信考试记录"),
    CONTINUE_LEARNING_RECORD(20,"司机继续教育记录"),
    STAFF_CERTIFICATE(21,"人员信息证件"),
    VEHICLE_BASIC_INFO(22,"车辆资产基础详情文件信息"),
    VEHICLE_GPS_RECORD(23,"车辆GPS终端记录"),
    VEHICLE_GRADE_ESTIMATION_RECORD(24,"车辆等级评定凭据"),
    VEHICLE_ROAD_TRANSPORT_CERTIFICATE_ANNUAL_REVIEW(25,"车辆道路运输证年审记录凭证"),
    VEHICLE_TIRE(26,"轮胎信息凭证"),
    APP_ADVERTISEMENT(27,"app首页图"),
    DRIVER_PAYEE(28,"司机收款账户证件"),
    FEEDBACK_TYPE(29,"APP反馈图片信息"),
    EXTERNAL_CARRIER_ORDER_UNLOAD_TICKETS(30,"外部运单出库单"),
    INSURANCE_TICKETS(31,"保险凭证"),
    COMPLAINT_IMAGE_FILE(32,"投诉凭证"),
    CARRIER_FEEDBACK_IMAGE_FILE(33,"车主反馈凭证"),
    COMPANY_CONTRACT_ORDER_FILE(34,"合同文件"),
    COMPANY_ENTRUST_TRADING(35,"委托方营业执照"),
    LOAN_RECORDS_FILE_TYPE(36,"贷款记录附件"),
    OIL_FILLED_ATTACHMENT_FILE_TYPE(37,"充油附件"),
    VEHICLE_SETTLEMENT_ATTACHMENT(38,"车辆结算附件"),
    INSURANCE_COSTS_ATTACHMENT_FILE_TYPE(39,"保险费用附件"),
    DRIVER_SAFE_PROMISE_FILE(40,"承诺书文件"),
    DRIVER_SAFE_MEETING_FILE(41,"安全例会"),
    DRIVER_SAFE_CHECK_FILE(42,"安全检查"),
    DRIVER_SAFE_CHECK_REFORM_FILE(43,"安全检查整改事项"),
    DRIVER_SAFE_CHECK_RESULT_FILE(44,"安全检查整改结果"),
    EXT_VEHICLE_SETTLEMENT_ATTACHMENT_FILE(45,"外部车辆结算附件"),
    SAFETY_GROUP_MEETING_ATTACHMENT_FILE(46, "安全领导小组会议附件"),
    APPLET_DRIVER_SETTLE_IMAGE(47, "小程序司机确认对账图片"),
    VEHICLE_OUTAGE(48, "车辆过户/报废证明图片"),
    DEMAND_ORDER_OBJECTION_SINOPEC(49, "中石化需求单异常审核图片"),
    DRIVER_COST_APPLY(50, "司机费用申请凭证"),
    RENEWABLE_ORDER_SITE_IMAGE(51, "新生订单现场图片"),
    RENEWABLE_ORDER_CONFIRM_TICKET(52, "新生订单确认票据"),
    DRIVER_APPOINT_SITE_IMAGE(53, "司机新生下单预约记录现场图片"),
    DRIVER_APPOINT_CONFIRM_TICKET(54, "司机新生下单预约记录确认票据"),
    CARRIER_ORDER_OTHER_FEE(55, "临时费用单据"),
    CARRIER_ORDER_SETTLE_STATEMENT_ARCHIVE_TICKET(56, "对账单归档单据"),
    ATTENDANCE_DUTY_PUNCH(57, "考勤打卡图片"),
    RESERVE_APPLY_REMIT_TICKET(58, "备用金申请确认打款票据"),
    COMPANY_ACCOUNT_BANK_CARD_PIC(59, "公司账户银行卡图片"),
    DRIVER_ACCOUNT_BANK_CARD_PIC(60, "司机账户银行卡图片"),
    DRIVER_REACH_ATTACHMENT(61, "司机触达附件"),
    WORK_ORDER_DRIVER_ARRIVE_SCENE(62, "上报工单司机到达现场图片"),
    T_ARCHIVED_FILE(63, "归档文件"),
    INVOICE_IMAGE(64, "发票图片"),
    ROUTE_ENQUIRY_ATTACHMENT(65, "路线询价单附件"),
    INVOICING_MANAGEMENT_ARCHIVED_FILE(66, "发票管理归档文件"),
    CARRIER_ORDER_CODE_FILE(67, "运单提货编码附件"),
    ;


    private Integer key;
    private String value;

    CopyFileTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
