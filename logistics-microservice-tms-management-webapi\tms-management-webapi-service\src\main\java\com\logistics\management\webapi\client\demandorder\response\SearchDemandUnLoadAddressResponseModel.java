package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2020 12/30
 * @desc 需求单收获地址dto
 */
@Data
public class SearchDemandUnLoadAddressResponseModel {
    @ApiModelProperty("云盘仓库外部id")
    private Long warehouseId;
    @ApiModelProperty("卸货省份id")
    private Long unloadProvinceId;
    @ApiModelProperty("卸货省份名字")
    private String unloadProvinceName;
    @ApiModelProperty("卸货城市id")
    private Long unloadCityId;
    @ApiModelProperty("卸货城市名字")
    private String unloadCityName;
    @ApiModelProperty("卸货县区id")
    private Long unloadAreaId;
    @ApiModelProperty("卸货县区名字")
    private String unloadAreaName;
    @ApiModelProperty("卸货详细地址")
    private String unloadDetailAddress;
    @ApiModelProperty("卸货仓库")
    private String unloadWarehouse;
    @ApiModelProperty("卸货人姓名")
    private String receiverName;
    @ApiModelProperty("卸货人手机号")
    private String receiverMobile;

}
