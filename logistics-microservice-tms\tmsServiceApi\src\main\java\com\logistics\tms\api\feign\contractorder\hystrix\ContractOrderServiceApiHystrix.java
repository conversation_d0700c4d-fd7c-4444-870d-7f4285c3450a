package com.logistics.tms.api.feign.contractorder.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.contractorder.ContractOrderServiceApi;
import com.logistics.tms.api.feign.contractorder.model.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("tmsContractOrderServiceApiHystrix")
public class ContractOrderServiceApiHystrix implements ContractOrderServiceApi {
    
    @Override
    public Result<PageInfo<ContractOrderSearchResponseModel>> searchContractOrderList(ContractOrderSearchRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ContractOrderDetailResponseModel> getDetail(ContractOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result saveContract(AddOrModifyContractOrderRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result terminateOrCancelContract(TerminateOrCancelContractRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<ContractOrderSearchResponseModel>> exportContractOrder(ContractOrderSearchRequestModel requestModel) {
        return Result.timeout();
    }
}
