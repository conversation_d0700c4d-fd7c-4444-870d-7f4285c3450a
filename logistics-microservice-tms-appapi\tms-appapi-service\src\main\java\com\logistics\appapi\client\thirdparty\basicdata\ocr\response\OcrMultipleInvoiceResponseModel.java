package com.logistics.appapi.client.thirdparty.basicdata.ocr.response;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OcrMultipleInvoiceResponseModel {

    @ApiModelProperty("图片全路径")
    private String src = "";

    @ApiModelProperty("图片相对路径")
    private String relativePath = "";

    @ApiModelProperty("ocr发票文字识别内容")
    private OcrMultipleInvoiceRes ocrInvoiceRes;

    private JSONObject response;

}