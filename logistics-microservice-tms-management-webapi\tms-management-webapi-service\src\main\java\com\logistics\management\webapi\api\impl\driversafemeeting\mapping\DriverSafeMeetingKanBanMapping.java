package com.logistics.management.webapi.api.impl.driversafemeeting.mapping;

import com.logistics.management.webapi.api.feign.driversafemeeting.dto.DriverSafeMeetingKanBanResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.NumberConversionEnum;
import com.logistics.management.webapi.base.enums.StaffPropertyEnum;
import com.logistics.management.webapi.base.enums.StudyStatusEnum;
import com.logistics.tms.api.feign.driversafemeeting.model.DriverSafeMeetingKanBanItemResponseModel;
import com.logistics.tms.api.feign.driversafemeeting.model.DriverSafeMeetingKanBanResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2019/11/4 16:46
 */
public class DriverSafeMeetingKanBanMapping extends MapperMapping<DriverSafeMeetingKanBanResponseModel,DriverSafeMeetingKanBanResponseDto> {
    @Override
    public void configure() {
        DriverSafeMeetingKanBanResponseModel source = getSource();
        DriverSafeMeetingKanBanResponseDto destination = getDestination();
        if (source != null){
            String period = source.getPeriod();
            destination.setMeetingYear(period.substring(CommonConstant.INTEGER_ZERO,period.length()-CommonConstant.INTEGER_THREE));
            destination.setMeetingMonth(NumberConversionEnum.getEnum(period.substring(period.length()-CommonConstant.INTEGER_TWO)).getValue());

            if (ListUtils.isNotEmpty(source.getItemList())){
                Integer driverCount = CommonConstant.INTEGER_ZERO;
                Integer ownDriverCount = CommonConstant.INTEGER_ZERO;
                Integer affiliationDriverCount = CommonConstant.INTEGER_ZERO;
                Integer learnDriverCount = CommonConstant.INTEGER_ZERO;
                for (DriverSafeMeetingKanBanItemResponseModel model : source.getItemList()) {
                    if (StaffPropertyEnum.OWNER.getKey().equals(model.getStaffProperty())){
                        ownDriverCount++;
                    }else if (StaffPropertyEnum.AFFILIATION.getKey().equals(model.getStaffProperty())){
                        affiliationDriverCount++;
                    }
                    driverCount = ownDriverCount + affiliationDriverCount;

                    if (StudyStatusEnum.STUDY.getKey().equals(model.getStatus())){
                        learnDriverCount++;
                    }
                }
                destination.setOwnDriverCount(ConverterUtils.toString(ownDriverCount));
                destination.setAffiliationDriverCount(ConverterUtils.toString(affiliationDriverCount));
                destination.setLearnDriverCount(ConverterUtils.toString(learnDriverCount));
                if (driverCount > CommonConstant.INTEGER_ZERO && learnDriverCount > CommonConstant.INTEGER_ZERO){
                    destination.setOnlineLearningRate(ConverterUtils.toString(ConverterUtils.toBigDecimal(learnDriverCount).multiply(CommonConstant.PERCENT).divide(ConverterUtils.toBigDecimal(driverCount),2,BigDecimal.ROUND_HALF_UP)));
                }
            }
        }
    }
}
