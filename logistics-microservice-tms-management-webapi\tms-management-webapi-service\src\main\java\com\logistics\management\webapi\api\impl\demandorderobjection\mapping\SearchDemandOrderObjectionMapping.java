package com.logistics.management.webapi.api.impl.demandorderobjection.mapping;

import com.logistics.management.webapi.api.feign.demandorderobjection.dto.SearchDemandOrderObjectionResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.DemandOrderObjectionTypeEnum;
import com.logistics.management.webapi.base.enums.DemandOrderStatusEnum;
import com.logistics.management.webapi.base.enums.EntrustTypeEnum;
import com.logistics.tms.api.feign.demandorderobjection.model.SearchDemandOrderObjectionResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author:lei.zhu
 * @date:2021/11/3 13:26:27
 */
public class SearchDemandOrderObjectionMapping extends MapperMapping<SearchDemandOrderObjectionResponseModel, SearchDemandOrderObjectionResponseDto> {
    @Override
    public void configure() {
        SearchDemandOrderObjectionResponseModel source = getSource();
        SearchDemandOrderObjectionResponseDto destination = getDestination();

        if (CommonConstant.INTEGER_ONE.equals(source.getIfCancel())) {
            destination.setStatus(DemandOrderStatusEnum.CANCEL_DISPATCH.getKey().toString());
            destination.setStatusDesc(DemandOrderStatusEnum.CANCEL_DISPATCH.getValue());
        }else if (CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())) {
            destination.setStatus(DemandOrderStatusEnum.ORDER_EMPTY.getKey().toString());
            destination.setStatusDesc(DemandOrderStatusEnum.ORDER_EMPTY.getValue());
        }else if (CommonConstant.INTEGER_ONE.equals(source.getIfRollback())) {
            destination.setStatus(DemandOrderStatusEnum.ROLLBACK.getKey().toString());
            destination.setStatusDesc(DemandOrderStatusEnum.ROLLBACK.getValue());
        }else{
            destination.setStatusDesc(DemandOrderStatusEnum.getEnum(source.getStatus()).getValue());
        }
        destination.setObjectionTypeDesc(DemandOrderObjectionTypeEnum.getEnum(source.getObjectionType()).getValue());
        destination.setEntrustType(EntrustTypeEnum.getEnum(source.getEntrustType()).getValue());
    }
}
