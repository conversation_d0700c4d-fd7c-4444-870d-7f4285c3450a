package com.logistics.tms.biz.renewableaudit.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/20 9:24
 */
@Data
public class CreateDemandOrderForRenewableAuditModel {

    //公共信息均为必传
    @ApiModelProperty("客户订单来源：1 乐橘新生客户，2 司机下单")
    private Integer customerOrderSource;

    @ApiModelProperty("需求生成人")
    private String publishName;

    @ApiModelProperty("下单人手机号")
    private String publishMobile;

    @ApiModelProperty("下单时间")
    private Date publishTime;

    @ApiModelProperty("司机id")
    private Long staffId;

    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    @ApiModelProperty("司机姓名")
    private String staffName;

    @ApiModelProperty("司机手机号")
    private String staffMobile;


    @ApiModelProperty("地址信息")
    private CreateDemandOrderAddressForRenewableAuditModel addressModel;

    @ApiModelProperty("货物信息")
    private List<CreateDemandOrderGoodsForRenewableAuditModel> goodsList;

    @ApiModelProperty("请求来源：1 司机下单（新生无账号），2 新生订单已审核")
    private String requestSource;

    //确认单据（requestSource=1时，图片为用户刚上传的；requestSource=2时，来源于新生订单的）
    @ApiModelProperty(value = "现场图片")
    private List<String> scenePictureList;

    @ApiModelProperty(value = "确认单据")
    private List<String> confirmPictureList;


    //以下信息requestSource=2时必传
    @ApiModelProperty("新生订单审核表id")
    private Long renewableOrderId;

    @ApiModelProperty("新生订单单号")
    private String renewableOrderCode;

    @ApiModelProperty("业务类型：1 公司，2 个人")
    private Integer businessType;

    @ApiModelProperty("乐橘新生客户名称（企业）")
    private String customerName;

    @ApiModelProperty("乐橘新生客户姓名（个人）")
    private String customerUserName;

    @ApiModelProperty("乐橘新生客户手机号（个人）")
    private String customerUserMobile;

    @ApiModelProperty("备注")
    private String remark;
    //以上信息requestSource=2时必传
}
