package com.logistics.tms.biz.carrierorderloadcode.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CarrierOrderLoadCodeListSqlConditionModel {

    @ApiModelProperty(value = "运单Id", required = true)
    private Long carrierOrderId;

    @ApiModelProperty("编码状态 1正确编码 2编码无法识别")
    private Integer state;


    @ApiModelProperty("编码codes")
    private List<String> codes;


    //批量查询
    private List<Long> carrierOrderIds;

}
