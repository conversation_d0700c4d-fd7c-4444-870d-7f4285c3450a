package com.logistics.appapi.client.customeraccount.hystrix;

import com.logistics.appapi.client.customeraccount.CustomerAccountClient;
import com.logistics.appapi.client.customeraccount.request.*;
import com.logistics.appapi.client.customeraccount.response.CustomerLoginResponseModel;
import com.logistics.appapi.client.customeraccount.response.UpdatePhoneByVerifyPhoneResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/3/7 16:54
 */
@Component
public class CustomerAccountClientHystrix implements CustomerAccountClient {
    @Override
    public Result<CustomerLoginResponseModel> driverAppletDefaultLogin(DriverAppletDefaultLoginRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CustomerLoginResponseModel> driverAppletLogin(CustomerLoginRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> appletBindingOpenId(AppletBindingOpenIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> appletLoginOut() {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> updateAccountPassword(UpdateAccountPasswordRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> modifyPassword(ModifyPasswordRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<UpdatePhoneByVerifyPhoneResponseModel> verifyPhone(UpdatePhoneByVerifyPhoneRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> updatePhone(UpdatePhoneRequestModel requestModel) {
        return Result.timeout();
    }
}
