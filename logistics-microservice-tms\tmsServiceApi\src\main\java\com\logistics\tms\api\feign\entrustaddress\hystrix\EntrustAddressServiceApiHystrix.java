package com.logistics.tms.api.feign.entrustaddress.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.entrustaddress.EntrustAddressServiceApi;
import com.logistics.tms.api.feign.entrustaddress.model.GetAddressByCompanyNameRequestModel;
import com.logistics.tms.api.feign.entrustaddress.model.GetAddressByCompanyNameResponseModel;
import com.logistics.tms.api.feign.entrustaddress.model.SearchEntrustAddressRequestModel;
import com.logistics.tms.api.feign.entrustaddress.model.SearchEntrustAddressResponseModel;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/9/19 11:49
 */
@Component("tmsEntrustAddressServiceApiHystrix")
public class EntrustAddressServiceApiHystrix implements EntrustAddressServiceApi {
    @Override
    public Result<PageInfo<GetAddressByCompanyNameResponseModel>> getAddressByCompanyNameOrWarehouse(GetAddressByCompanyNameRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<SearchEntrustAddressResponseModel>> searchList(SearchEntrustAddressRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchEntrustAddressResponseModel>> export(SearchEntrustAddressRequestModel requestModel) {
        return Result.timeout();
    }
}
