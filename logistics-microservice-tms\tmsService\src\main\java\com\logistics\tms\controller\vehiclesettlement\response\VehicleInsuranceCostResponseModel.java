package com.logistics.tms.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/6/8 11:33
 */
@Data
public class VehicleInsuranceCostResponseModel {
    @ApiModelProperty(value = "保单Id")
    private Long insuranceId;
    @ApiModelProperty(value = "险种：1 商业险，2 交强险，4 货物险，5 危货承运人险")
    private Integer insuranceType;
    private String insuranceTypeLabel;
    @ApiModelProperty(value = "保单号")
    private String policyNo;
    @ApiModelProperty(value = "保费合计")
    private BigDecimal totalCost;
    @ApiModelProperty(value = "剩余未扣除金额")
    private BigDecimal remainNotDeductionCost;
    @ApiModelProperty(value = "应扣减费用")
    private BigDecimal payCost;
    @ApiModelProperty(value = "未扣减费用")
    private BigDecimal unPaidCost;
}
