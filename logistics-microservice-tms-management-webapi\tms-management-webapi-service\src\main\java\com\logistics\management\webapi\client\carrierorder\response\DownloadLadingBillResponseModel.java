package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2018/9/30 11:48
 */
@Data
public class DownloadLadingBillResponseModel {

    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("需求类型")
    private Integer entrustType;

    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("运单状态 10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收")
    private Integer status;

    @ApiModelProperty("预计提货数量")
    private BigDecimal expectAmount;

    @ApiModelProperty("实际提货数量")
    private BigDecimal loadAmount;

    @ApiModelProperty("签收数量")
    private BigDecimal signAmount;

    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;

    @ApiModelProperty("车牌号码")
    private String vehicleNo;

    @ApiModelProperty("司机姓名")
    private String driverName;

    @ApiModelProperty("司机手机号")
    private String driverMobile;

    @ApiModelProperty("司机身份证号")
    private String driverIdentityNumber;

    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadCompany;
    private String loadDetailAddress;
    private String loadWarehouse;

    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadCompany;
    private String unloadDetailAddress;
    private String unloadWarehouse;

    @ApiModelProperty("提货人")
    private String consignorName;

    @ApiModelProperty("提货人电话")
    private String consignorMobile;

    @ApiModelProperty("卸货人")
    private String receiverName;

    @ApiModelProperty("卸货人电话")
    private String receiverMobile;

    @ApiModelProperty("预计提货时间")
    private Date expectedLoadTime;

    @ApiModelProperty("回收预计提货时间")
    private Date recycleExpectedLoadTime;

    @ApiModelProperty("预计卸货时间")
    private Date expectedUnloadTime;

    @ApiModelProperty("实提时间")
    private Date loadTime;

    @ApiModelProperty("调度时间")
    private Date dispatchTime;

    @ApiModelProperty("货物信息")
    private List<DownloadLadingBillGoodsResponseModel> goodsInfoList;

    @ApiModelProperty("委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件")
    private Integer demandOrderSource;

    @ApiModelProperty("运单二维码图片路径")
    private String qrCodePicPath;
    private byte[] qrCodePicByte;//二维码图片流(与二维码图片路径只有一个有值)

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("调度备注")
    private String dispatchRemark;

    @ApiModelProperty("周末是否可上门：0 空，1 是，2 否")
    private Integer availableOnWeekends;

    @ApiModelProperty("装卸方: 0 空，1 我司装卸，2 客户装卸")
    private Integer loadingUnloadingPart;

    @ApiModelProperty("下单人")
    private String publishName;

    @ApiModelProperty("下单人手机号")
    private String publishMobile;

    @ApiModelProperty("货主")
    private String companyEntrustName;


    @ApiModelProperty("是否取消 0 否 1 是")
    private Integer ifCancel;

    @ApiModelProperty("是否放空：0 否，1 是")
    private Integer ifEmpty;

    @ApiModelProperty("上游客户（YR）")
    private String upstreamCustomer;

    @ApiModelProperty("承运商")
    private String companyCarrierName;

}
