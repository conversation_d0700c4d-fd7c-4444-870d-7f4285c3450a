<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandOrderCustomerRelMapper" >
  <select id="getByDemandOrderIds" resultType="com.logistics.tms.rabbitmq.publisher.model.SyncSignDemandOrderListToGroundPushModel">
      select
      tdocr.tray_customer_company_id as trayCustomerCompanyId,
      tdocr.tray_customer_address_id as trayCustomerAddressId,
      tdocr.product_category_id as productCategoryId,

      tdo.demand_order_code as demandOrderCode,
      tdo.goods_amount as goodsAmount,
      tdo.arranged_amount as arrangedAmount
      from t_demand_order_customer_rel tdocr
      left join t_demand_order tdo on tdo.id = tdocr.demand_order_id and tdocr.valid = tdo.valid
      where tdocr.valid = 1
      and tdocr.demand_order_id in (${demandOrderIds})
  </select>
</mapper>