package com.logistics.tms.controller.customeraccount.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/7/13 14:28
 */
@Data
public class FindPasswordPrepareRequestModel {
    @ApiModelProperty(value = "用户账号")
    private String userAccount;
    @ApiModelProperty(value = "手机验证码")
    private String verificationCode;
    @ApiModelProperty(value = "验证码来源")
    private Integer codeSource;
    @ApiModelProperty(value = "验证码类型")
    private Integer codeType;
}
