package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleSafeCheck extends BaseEntity {
    /**
    * 周期、检查日期
    */
    @ApiModelProperty("周期、检查日期")
    private String period;

    /**
    * 同一批次ID
    */
    @ApiModelProperty("同一批次ID")
    private String batchUuid;

    /**
    * 车辆总数量
    */
    @ApiModelProperty("车辆总数量")
    private Integer vehicleCount;
}