package com.logistics.management.webapi.api.feign.demandorderobjectionsinopec.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @author: wjf
 * @date: 2022/5/30 13:55
 */
@Data
public class SinopecObjectionAuditRequestDto {
    @ApiModelProperty(value = "需求单异常id",required = true)
    @NotBlank(message = "异常id不能为空")
    private String demandOrderObjectionId;

    @ApiModelProperty(value = "审核结果：1 通过，2 驳回",required = true)
    @NotBlank(message = "请选择审核结果")
    private String auditStatus;
    @ApiModelProperty(value = "异常类型：1 已报价，2 已取消",required = true)
    @NotBlank(message = "请选择异常类型")
    private String auditObjectionType;
    @ApiModelProperty(value = "审核依据",required = true)
    @NotEmpty(message = "请上传审核依据")
    private List<String> auditTicketList;
    @ApiModelProperty("备注：最多100字")
    @Size(max = 100, message = "备注不能超过100字")
    private String auditRemark;
}
