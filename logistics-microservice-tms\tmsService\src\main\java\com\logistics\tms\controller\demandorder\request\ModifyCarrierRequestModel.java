package com.logistics.tms.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ModifyCarrierRequestModel {

    @ApiModelProperty(value="需求单ID")
    private Long demandOrderId;

    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;

    @ApiModelProperty(value="车主ID")
    private Long companyCarrierId;

    @ApiModelProperty(value="车主报价")
    private BigDecimal carrierPrice;

    @ApiModelProperty(value="车主报价类型")
    private Integer carrierPriceType;

    @ApiModelProperty(value="修改原因")
    private String modifyReason;
}
