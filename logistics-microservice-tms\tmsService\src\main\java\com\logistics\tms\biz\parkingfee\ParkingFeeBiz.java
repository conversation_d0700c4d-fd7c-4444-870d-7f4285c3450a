package com.logistics.tms.biz.parkingfee;

import com.github.pagehelper.PageInfo;
import com.yelo.tools.context.BaseContextHandler;
import com.logistics.tms.api.feign.parkingfee.dto.*;
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @Author: sj
 * @Date: 2019/10/9 16:17
 */
@Service
@Slf4j
public class ParkingFeeBiz {
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TParkingFeeMapper tParkingFeeMapper;
    @Autowired
    private TParkingFeeRecordsMapper tParkingFeeRecordsMapper;
    @Autowired
    private TVehicleBasicMapper tVehicleBasicMapper;
    @Autowired
    private TStaffBasicMapper tStaffBasicMapper;
    @Autowired
    private TDeductingHistoryMapper deductingHistoryMapper;
    @Autowired
    private TVehicleSettlementRelationMapper tVehicleSettlementRelationMapper;

    /**
     * 列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<ParkingFeeListResponseModel> searchList(ParkingFeeListRequestModel requestModel) {
        requestModel.enablePaging();
        List<ParkingFeeListResponseModel> retList = tParkingFeeMapper.getParkingFeeList(requestModel);
        return new PageInfo(retList);
    }

    /**
     * 汇总
     *
     * @param requestModel
     * @return
     */
    public SummaryParkingFeeResponseModel getSummary(ParkingFeeListRequestModel requestModel) {
        return tParkingFeeMapper.getSummary(requestModel);
    }

    /**
     * 详情
     *
     * @param requestModel
     * @return
     */
    public ParkingFeeDetailResponseModel getDetail(ParkingFeeDetailRequestModel requestModel) {
        ParkingFeeDetailResponseModel parkingFeeDetail = tParkingFeeMapper.getDetailById(requestModel.getParkingFeeId());
        if (parkingFeeDetail == null) {
            throw new BizException(CarrierDataExceptionEnum.PARKING_FEE_NOT_EXIST);
        }

        ParkingFeeDeductingHistoryRequestModel requestDeductingHistoryModel = new ParkingFeeDeductingHistoryRequestModel();
        requestDeductingHistoryModel.setParkingFeeId(requestModel.getParkingFeeId());
        List<ParkingFeeDeductingHistoryResponseModel> deductingHistoryList = this.getDeductingHistoryList(requestDeductingHistoryModel);
        parkingFeeDetail.setDeductingHistoryList(Optional.ofNullable(deductingHistoryList).orElse(new ArrayList<>()));

        //判断是有已经生成了结算数据
        List<TVehicleSettlementRelation> tVehicleSettlementRelationList = tVehicleSettlementRelationMapper.getByObjectIdAndType(VehicleSettlementTypeEnum.PARKING.getKey(), parkingFeeDetail.getParkingFeeId());
        if (ListUtils.isNotEmpty(tVehicleSettlementRelationList)) {
            parkingFeeDetail.setIfSettlement(CommonConstant.INTEGER_ONE);
        }

        return parkingFeeDetail;
    }

    /**
     * 修改/保存
     *
     * @param requestModel
     */
    @Transactional
    public void saveOrUpdate(SaveOrUpdateParkingFeeRequestModel requestModel) {

        TParkingFee tParkingFee = new TParkingFee();
        tParkingFee.setVehicleId(requestModel.getVehicleId());
        tParkingFee.setVehicleNo(requestModel.getVehicleNo());
        tParkingFee.setStaffId(requestModel.getStaffId());
        tParkingFee.setMobile(requestModel.getMobile());
        tParkingFee.setName(requestModel.getName());
        tParkingFee.setParkingFee(requestModel.getParkingFee());
        tParkingFee.setCooperationPeriod(requestModel.getCooperationPeriod());
        tParkingFee.setCooperationCompany(requestModel.getCooperationCompany());
        tParkingFee.setStartDate(requestModel.getStartDate());
        tParkingFee.setEndDate(requestModel.getEndDate());
        tParkingFee.setFinishDate(requestModel.getEndDate());
        tParkingFee.setRemark(requestModel.getRemark());
        tParkingFee.setCooperationStatus(getCooperationStatus(requestModel.getStartDate(), requestModel.getEndDate()));

        Date currentNow = DateUtils.stringToDate(DateUtils.dateToString(new Date(), DateUtils.DATE_TO_STRING_SHORT_PATTERN), DateUtils.DATE_TO_STRING_SHORT_PATTERN);
        //截止时间不能少于当前时间
        if ((requestModel.getParkingFeeId() == null || CommonConstant.LONG_ZERO.equals(requestModel.getParkingFeeId())) && requestModel.getEndDate().before(currentNow)) {
            throw new BizException(CarrierDataExceptionEnum.PARKING_FEE_END_TIME_NOT_ALLOW_BEFORE_NOW);
        }

        //查询车辆基础信息
        VehicleBasicPropertyModel dbVehicleBasic = tVehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getVehicleId());
        if (requestModel.getVehicleId() == null || dbVehicleBasic == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }

        //查询司机基础信息
        TStaffBasic dbStaffBasic = tStaffBasicMapper.selectByPrimaryKey(requestModel.getStaffId());
        if (requestModel.getStaffId() == null || dbStaffBasic == null || CommonConstant.INTEGER_ZERO.equals(dbStaffBasic.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_VEHICLE_RELATION_EMPTY);
        }

        tParkingFee.setVehicleNo(dbVehicleBasic.getVehicleNo());
        tParkingFee.setName(dbStaffBasic.getName());
        tParkingFee.setMobile(dbStaffBasic.getMobile());

        if (requestModel.getParkingFeeId() == null || requestModel.getParkingFeeId() <= CommonConstant.INTEGER_ZERO) {//新增
            List<TParkingFee> tParkingFeeList = tParkingFeeMapper.getByVehicleStartDate(requestModel.getVehicleId(), requestModel.getStartDate(), requestModel.getEndDate());
            if (ListUtils.isNotEmpty(tParkingFeeList)) {
                throw new BizException(CarrierDataExceptionEnum.PARKING_FEE_EXIST);
            }

            //是否内部司机
            if (!(VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(dbVehicleBasic.getVehicleProperty()) ||
                    VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(dbVehicleBasic.getVehicleProperty()))) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_PROPERTY_ERROR);
            }
            //是否运营中
            if (!VehicleOutageStatus.IN_OPERATION.getKey().equals(dbVehicleBasic.getOperatingState())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_OUTAGE_ERROR);
            }
            //是否牵引车或者一体车
            if (!VehicleCategoryEnum.TRACTOR.getKey().equals(dbVehicleBasic.getVehicleCategory()) && !VehicleCategoryEnum.WHOLE.getKey().equals(dbVehicleBasic.getVehicleCategory())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_CATEGORY_ERROR);
            }
            //是否内部司机
            if (!(StaffPropertyEnum.OWN_STAFF.getKey().equals(dbStaffBasic.getStaffProperty()) ||
                    StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(dbStaffBasic.getStaffProperty()))) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_PROPERTY_ERROR);
            }

            tParkingFee.setVehicleProperty(dbVehicleBasic.getVehicleProperty());
            commonBiz.setBaseEntityAdd(tParkingFee, BaseContextHandler.getUserName());
            tParkingFeeMapper.insertSelective(tParkingFee);
            //保存操作记录
            addParkingFeeOperatorRecord(tParkingFee);

        } else {//修改
            List<TParkingFee> dbOneParkingFeeList = tParkingFeeMapper.getByVehicleStartDate(requestModel.getVehicleId(), requestModel.getStartDate(), requestModel.getEndDate());
            TParkingFee dbTwoParkingFee = tParkingFeeMapper.selectByPrimaryKey(requestModel.getParkingFeeId());
            if (dbTwoParkingFee == null || IfValidEnum.INVALID.getKey().equals(dbTwoParkingFee.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.PARKING_FEE_NOT_EXIST);
            }
            if (ListUtils.isNotEmpty(dbOneParkingFeeList)) {
                TParkingFee dbThreeModel = dbOneParkingFeeList.stream().filter(o -> !o.getId().equals(requestModel.getParkingFeeId())).findFirst().orElse(null);
                if (dbThreeModel != null) {
                    throw new BizException(CarrierDataExceptionEnum.PARKING_FEE_EXIST);
                }
            }
            if (CooperationStatusEnum.TERMINAL.getKey().equals(dbTwoParkingFee.getCooperationStatus())) {
                throw new BizException(CarrierDataExceptionEnum.PARKING_FEE_TERMINATION);
            }
            if (!SettlementStatusEnum.WAIT.getKey().equals(dbTwoParkingFee.getStatus())) {
                throw new BizException(CarrierDataExceptionEnum.SETTLEMENT_STATUS_ERROR_NOT_UPDATE);
            }

            List<TDeductingHistory> historyList = deductingHistoryMapper.getByObjectTypeId(DeductingHistoryObjectTypeEnum.T_PARKING_FEE.getKey(), dbTwoParkingFee.getId());
            if (ListUtils.isNotEmpty(historyList) && requestModel.getStartDate().getTime() > new Date().getTime()) {
                throw new BizException(CarrierDataExceptionEnum.PARKING_FEE_HISTORY_EXIST);
            }

            //修改了车辆则判断车辆是否符合条件
            if (!dbTwoParkingFee.getVehicleId().equals(requestModel.getVehicleId())) {
                //自有
                if (!(VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(dbVehicleBasic.getVehicleProperty()) ||
                        VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(dbVehicleBasic.getVehicleProperty()))) {
                    throw new BizException(CarrierDataExceptionEnum.VEHICLE_PROPERTY_ERROR);
                }
                //营运中国
                if (!VehicleOutageStatus.IN_OPERATION.getKey().equals(dbVehicleBasic.getOperatingState())) {
                    throw new BizException(CarrierDataExceptionEnum.VEHICLE_OUTAGE_ERROR);
                }
                //牵引车或者一体车
                if (!VehicleCategoryEnum.TRACTOR.getKey().equals(dbVehicleBasic.getVehicleCategory()) && !VehicleCategoryEnum.WHOLE.getKey().equals(dbVehicleBasic.getVehicleCategory())) {
                    throw new BizException(CarrierDataExceptionEnum.VEHICLE_CATEGORY_ERROR);
                }
            }

            //修改了司机则判断司机是否符合条件
            if (!dbTwoParkingFee.getStaffId().equals(requestModel.getStaffId()) &&
                    !(StaffPropertyEnum.OWN_STAFF.getKey().equals(dbStaffBasic.getStaffProperty()) ||
                            StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(dbStaffBasic.getStaffProperty()))) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_PROPERTY_ERROR);
            }

            //判断当前停车费用是否存在账单数据
            List<TVehicleSettlementRelation> tVehicleSettlementRelationList = tVehicleSettlementRelationMapper.getByObjectIdAndType(VehicleSettlementTypeEnum.PARKING.getKey(), dbTwoParkingFee.getId());
            if (ListUtils.isEmpty(tVehicleSettlementRelationList) && requestModel.getEndDate().before(currentNow)) {
                throw new BizException(CarrierDataExceptionEnum.PARKING_FEE_END_TIME_NOT_ALLOW_BEFORE_NOW);
            }
            //如果存在车辆结算费用，则以下字段不允许修改
            if (ListUtils.isNotEmpty(tVehicleSettlementRelationList)) {
                tParkingFee.setVehicleId(null);
                tParkingFee.setVehicleNo(null);
                tParkingFee.setStartDate(null);
                tParkingFee.setCooperationPeriod(null);
                tParkingFee.setEndDate(null);
                tParkingFee.setParkingFee(null);
                tParkingFee.setFinishDate(null);
            } else {
                //修改为其他车辆时才更新车辆机构
                if (!dbTwoParkingFee.getVehicleId().equals(requestModel.getVehicleId())) {
                    tParkingFee.setVehicleProperty(dbVehicleBasic.getVehicleProperty());
                }
            }

            tParkingFee.setId(dbTwoParkingFee.getId());
            commonBiz.setBaseEntityModify(tParkingFee, BaseContextHandler.getUserName());
            tParkingFeeMapper.updateByPrimaryKeySelective(tParkingFee);
            //添加操作记录
            addParkingFeeOperatorRecord(tParkingFee);
        }
    }

    public Integer getCooperationStatus(Date startDate, Date endDate) {
        Date now = new Date();
        SimpleDateFormat nowSdf = new SimpleDateFormat(DateUtils.DATE_TO_STRING_SHORT_PATTERN);
        String nowStr = nowSdf.format(now);
        Date nowTime = null;
        try {
            nowTime = nowSdf.parse(nowStr);
        } catch (ParseException e) {
            log.info(e.getMessage(), e);
        }
        Integer cooperationStatus = null;
        if (nowTime != null) {
            if (startDate.getTime() > nowTime.getTime()) {
                cooperationStatus = CooperationStatusEnum.NO_START.getKey();
            } else if (nowTime.getTime() > endDate.getTime()) {
                cooperationStatus = CooperationStatusEnum.TERMINAL.getKey();
            } else {
                cooperationStatus = CooperationStatusEnum.VALID.getKey();
            }
        }
        return cooperationStatus;
    }

    /**
     * 保存停车费用操作记录
     *
     * @param tParkingFee
     */
    @Transactional
    public void addParkingFeeOperatorRecord(TParkingFee tParkingFee) {
        TParkingFeeRecords tParkingFeeRecords = new TParkingFeeRecords();
        tParkingFeeRecords.setParkingFeeId(tParkingFee.getId());
        tParkingFeeRecords.setCooperationCompany(tParkingFee.getCooperationCompany());
        tParkingFeeRecords.setCooperationPeriod(tParkingFee.getCooperationPeriod());
        tParkingFeeRecords.setParkingFee(tParkingFee.getParkingFee());
        tParkingFeeRecords.setStartDate(tParkingFee.getStartDate());
        tParkingFeeRecords.setEndDate(tParkingFee.getEndDate());
        tParkingFeeRecords.setFinishDate(tParkingFee.getFinishDate());
        tParkingFeeRecords.setRemark(tParkingFee.getRemark());
        commonBiz.setBaseEntityAdd(tParkingFeeRecords, BaseContextHandler.getUserName());
        tParkingFeeRecordsMapper.insertSelective(tParkingFeeRecords);
    }

    /**
     * 终止
     *
     * @param requestModel
     */
    @Transactional
    public void terminateParkingFee(TerminateParkingFeeRequestModel requestModel) {
        TParkingFee dbParkingFe = tParkingFeeMapper.selectByPrimaryKey(requestModel.getParkingFeeId());
        if (dbParkingFe == null || IfValidEnum.INVALID.getKey().equals(dbParkingFe.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.PARKING_FEE_NOT_EXIST);
        }
        if (CooperationStatusEnum.TERMINAL.getKey().equals(dbParkingFe.getCooperationStatus())) {
            throw new BizException(CarrierDataExceptionEnum.PARKING_FEE_TERMINATION);
        }

        if (CooperationStatusEnum.NO_START.getKey().equals(dbParkingFe.getCooperationStatus()) && SettlementStatusEnum.WAIT.getKey().equals(dbParkingFe.getStatus())) {
            throw new BizException(CarrierDataExceptionEnum.PARKING_FEE_NOT_TERMINATION);
        }

        if (requestModel.getFinishDate().getTime() > dbParkingFe.getEndDate().getTime() || requestModel.getFinishDate().getTime() < dbParkingFe.getStartDate().getTime()) {
            throw new BizException(CarrierDataExceptionEnum.PARKING_FEE_FINISH_DATE_ERROR);
        }

        TParkingFee upParkingFee = new TParkingFee();
        upParkingFee.setId(requestModel.getParkingFeeId());
        upParkingFee.setFinishDate(requestModel.getFinishDate());
        upParkingFee.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityAdd(upParkingFee, BaseContextHandler.getUserName());
        tParkingFeeMapper.updateByPrimaryKeySelective(upParkingFee);

        dbParkingFe.setFinishDate(requestModel.getFinishDate());
        dbParkingFe.setRemark(requestModel.getRemark());
        //添加操作记录
        this.addParkingFeeOperatorRecord(dbParkingFe);
    }

    /**
     * 扣减历史
     *
     * @param requestModel
     * @return
     */
    public List<ParkingFeeDeductingHistoryResponseModel> getDeductingHistoryList(ParkingFeeDeductingHistoryRequestModel requestModel) {
        TParkingFee dbParkingFee = tParkingFeeMapper.selectByPrimaryKey(requestModel.getParkingFeeId());
        if (dbParkingFee == null || IfValidEnum.INVALID.getKey().equals(dbParkingFee.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.PARKING_FEE_NOT_EXIST);
        }

        List<TDeductingHistory> historyList = deductingHistoryMapper.getByObjectTypeId(DeductingHistoryObjectTypeEnum.T_PARKING_FEE.getKey(), requestModel.getParkingFeeId());
        if (ListUtils.isEmpty(historyList)) {
            return new ArrayList<>();
        }

        List<ParkingFeeDeductingHistoryResponseModel> responseList = new ArrayList<>();
        ParkingFeeDeductingHistoryResponseModel deductingHistoryResponseModel;
        for (TDeductingHistory tempDeductingHistory : historyList) {
            deductingHistoryResponseModel = new ParkingFeeDeductingHistoryResponseModel();
            deductingHistoryResponseModel.setParkingFeeDeductingId(tempDeductingHistory.getId());
            deductingHistoryResponseModel.setParkingFeeId(dbParkingFee.getId());
            deductingHistoryResponseModel.setVehicleNo(dbParkingFee.getVehicleNo());
            deductingHistoryResponseModel.setDeductingMonth(tempDeductingHistory.getDeductingMonth());
            deductingHistoryResponseModel.setTotalFee(tempDeductingHistory.getTotalFee());
            deductingHistoryResponseModel.setDeductingFee(tempDeductingHistory.getDeductingFee());
            deductingHistoryResponseModel.setRemainingDeductingFee(tempDeductingHistory.getRemainingDeductingFee());
            deductingHistoryResponseModel.setLastModifiedTime(tempDeductingHistory.getLastModifiedTime());
            deductingHistoryResponseModel.setLastModifiedBy(tempDeductingHistory.getLastModifiedBy());
            responseList.add(deductingHistoryResponseModel);
        }

        return responseList;
    }

    /**
     * 操作记录
     *
     * @param requestModel
     * @return
     */
    public List<ParkingFeeOperationRecordResponsesModel> getOperationRecordList(ParkingFeeOperationRecordRequestModel requestModel) {
        TParkingFee dbParkingFee = tParkingFeeMapper.selectByPrimaryKey(requestModel.getParkingFeeId());
        if (dbParkingFee == null || IfValidEnum.INVALID.getKey().equals(dbParkingFee.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.PARKING_FEE_NOT_EXIST);
        }
        List<TParkingFeeRecords> parkingFeeRecordsList = tParkingFeeRecordsMapper.getByParkingFeeId(dbParkingFee.getId());
        if (ListUtils.isEmpty(parkingFeeRecordsList)) {
            return new ArrayList<>();
        }
        List<ParkingFeeOperationRecordResponsesModel> retList = new ArrayList<>();
        ParkingFeeOperationRecordResponsesModel parkingFeeOperationRecord;
        for (TParkingFeeRecords tempParkingFeeRecords : parkingFeeRecordsList) {
            parkingFeeOperationRecord = new ParkingFeeOperationRecordResponsesModel();
            parkingFeeOperationRecord.setVehicleId(dbParkingFee.getVehicleId());
            parkingFeeOperationRecord.setVehicleNo(dbParkingFee.getVehicleNo());
            parkingFeeOperationRecord.setParkingFeeId(tempParkingFeeRecords.getParkingFeeId());
            parkingFeeOperationRecord.setCooperationCompany(tempParkingFeeRecords.getCooperationCompany());
            parkingFeeOperationRecord.setStartDate(tempParkingFeeRecords.getStartDate());
            parkingFeeOperationRecord.setEndDate(tempParkingFeeRecords.getEndDate());
            parkingFeeOperationRecord.setFinishDate(tempParkingFeeRecords.getFinishDate());
            parkingFeeOperationRecord.setParkingFee(tempParkingFeeRecords.getParkingFee());
            parkingFeeOperationRecord.setCooperationPeriod(tempParkingFeeRecords.getCooperationPeriod());
            parkingFeeOperationRecord.setRemark(tempParkingFeeRecords.getRemark());
            parkingFeeOperationRecord.setLastModifiedBy(tempParkingFeeRecords.getLastModifiedBy());
            parkingFeeOperationRecord.setLastModifiedTime(tempParkingFeeRecords.getLastModifiedTime());
            retList.add(parkingFeeOperationRecord);
        }
        return retList;
    }
}
