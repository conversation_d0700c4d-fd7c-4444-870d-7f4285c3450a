package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Data
public class ModifyVehicleRequestDto {

    @ApiModelProperty(value = "运单id", required = true)
    @NotBlank(message = "运单id不能为空")
    private String carrierOrderId;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("挂车车辆ID,1.4.0新增")
    private String trailerVehicleId;

    @ApiModelProperty(value = "期望到达时间", required = true)
    @NotBlank(message = "预计到货时间不能为空")
    private String expectArrivalTime;

    @ApiModelProperty("司机姓名")
    @NotBlank(message = "司机姓名不能为空")
    private String driverName;

    @ApiModelProperty(value = "司机手机号", required = true)
    @Pattern(regexp = "^\\d{11}$", message = "司机手机号不能为空或格式不正确")
    private String driverMobile;

    @ApiModelProperty("司机身份证号码")
    private String driverIdentity;

    @ApiModelProperty("修改原因")
    @Size(min = 1, max = 30, message = "修改原因不能为空，且不超过30字")
    private String remark;
}
