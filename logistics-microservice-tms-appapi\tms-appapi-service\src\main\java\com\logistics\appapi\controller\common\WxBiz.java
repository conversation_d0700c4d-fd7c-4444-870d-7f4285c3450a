package com.logistics.appapi.controller.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.logistics.appapi.base.enums.AppApiExceptionEnum;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.URL;
import java.net.URLConnection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/10/23 11:24
 */
@Service
@Slf4j
public class WxBiz {

    @Value("${wechat.applet.url}")
    private String weChatUrl;
    @Value("${wechat.applet.appid}")
    private String appId;
    @Value("${wechat.applet.secret}")
    private String secret;

    /**
     * code换取openId
     * @param code
     * @return
     */
    public String getOpenId(String code) {
        Map<String, String> urlParam = new HashMap<>();
        urlParam.put("appid", appId);
        urlParam.put("secret", secret);
        urlParam.put("js_code", code);
        urlParam.put("grant_type", "authorization_code");
        JSONObject jsonObject = JSON.parseObject(getOpenIdJSON(weChatUrl, urlParam));
        if (jsonObject == null || StringUtils.isBlank(jsonObject.getString("openid"))) {
            throw new BizException(AppApiExceptionEnum.OPEN_ID_EMPTY);
        }
        return jsonObject.getString("openid");
    }
    //从腾讯获取openId
    public static String getOpenIdJSON(String url, Map<String, ?> paramMap) {
        StringBuilder result = new StringBuilder();
        StringBuilder params = new StringBuilder();
        Iterator<String> it = paramMap.keySet().iterator();
        while (it.hasNext()) {
            String key = it.next();
            params = params.append(key).append("=").append(paramMap.get(key)).append("&");
        }

        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("Accept-Charset", "utf-8");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            try (PrintWriter out = new PrintWriter(conn.getOutputStream())) {
                // 发送请求参数
                out.print(params.toString());
                // flush输出流的缓冲
                out.flush();
                // 定义BufferedReader输入流来读取URL的响应
                try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"))) {
                    String line;
                    while ((line = in.readLine()) != null) {
                        result = result.append(line);
                    }
                }
            }
        } catch (Exception e) {
            log.error("openConnection Wx Exception", e);
        }
        return result.toString();
    }
}
