package com.logistics.tms.base.enums;
/**
 * <AUTHOR>
 * @date 2020/3/18 15:15
 */
public enum CertApplyStatusEnum {
    SYSTEM_EXCEPTION(-2, "系统异常"),
    INVALID_APPLY(-1, "无效的申请（数据库无此值）"),
    TASK_ID_NO_EXIST_OR_OVERDUE(0, "taskId不存在或已过期"),
    NEW_APPLY(1, "新申请"),
    APPLY_HANDLE(2, "申请中"),
    APPLY_OVERTIME(3, "超时"),
    APPLY_FAIL(4, "申请失败"),
    APPLY_SUCCESS(5, "成功"),
   ;

    private Integer key;
    private String value;

    CertApplyStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
