package com.logistics.management.webapi.controller.shippingorder.mapping;

import cn.hutool.core.date.DateUtil;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.ShippingOrderStatusEnum;
import com.logistics.management.webapi.client.shippingorder.response.SearchShippingOrderListResponseModel;
import com.logistics.management.webapi.controller.shippingorder.response.SearchShippingOrderListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringHelper;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2024/7/10 13:53
 */
public class ShippingOrderSearchListMapping extends MapperMapping<SearchShippingOrderListResponseModel, SearchShippingOrderListResponseDto> {
    @Override
    public void configure() {
        SearchShippingOrderListResponseModel source = getSource();
        SearchShippingOrderListResponseDto destination = getDestination();


        destination.setStatusLabel(ShippingOrderStatusEnum.getEnum(source.getStatus()).getValue());
        destination.setExpectAmount(source.getExpectAmount().stripTrailingZeros().toPlainString());
        destination.setSignAmount(source.getSignAmount().stripTrailingZeros().toPlainString());
        destination.setDispatchTime(DateUtil.format(source.getDispatchTime(), CommonConstant.YYYY_MM_DD_SPACE_HH_MM_SS));

        destination.setExpectCarrierFreight(source.getCarrierFreight().add(source.getCrossPointFee()).toPlainString());
        destination.setVehicleLength(new BigDecimal("-1.00").equals(source.getVehicleLength()) ? "零担" : source.getVehicleLength().stripTrailingZeros().toPlainString());

    }
}
