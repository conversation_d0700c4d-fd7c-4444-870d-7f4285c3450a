package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/10/18
*/
@Data
public class TStaffVehicleRelation extends BaseEntity {
    /**
    * 车主id
    */
    @ApiModelProperty("车主id")
    private Long companyCarrierId;

    /**
    * 司机id
    */
    @ApiModelProperty("司机id")
    private Long staffId;

    /**
    * 车辆id
    */
    @ApiModelProperty("车辆id")
    private Long vehicleId;

    /**
    * 挂车ID
    */
    @ApiModelProperty("挂车ID")
    private Long trailerVehicleId;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}