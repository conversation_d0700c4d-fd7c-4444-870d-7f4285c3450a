package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetCarrierOrderWeixinPushResponseDto {
    @ApiModelProperty("ID")
    private String id;
    @ApiModelProperty("角色 1 发货方 2 收货方 3 委托方")
    private String role;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("是否推送 1 推送 0 不推")
    private String ifPush;
    @ApiModelProperty("角色 1 发货方 2 收货方 3 委托方 4 其他")
    private String roleName;
}
