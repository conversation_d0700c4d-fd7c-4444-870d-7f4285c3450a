package com.logistics.management.webapi.api.feign.insuarance.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2019/12/25 17:45
 */
@Data
public class InsuranceRefundDto {
    @ApiModelProperty("险种：1 商业险，2 交强险，3 个人意外险，4 货物险，5 危货承运人险")
    @NotBlank(message = "险种类别不能为空")
    private String insuranceType;
    @ApiModelProperty(value = "保单Id")
    @NotBlank(message = "保单Id不能为空")
    private String insuranceId;
    @ApiModelProperty(value = "是否退保：0 不退，1 退保")
    @NotBlank(message = "请选择是否退保")
    private String ifRefund;
    @ApiModelProperty(value = "退保金额")
    private String refundPremium;
    @ApiModelProperty("图片相对路径")
    private String filePath;
}
