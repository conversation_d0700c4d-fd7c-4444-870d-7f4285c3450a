package com.logistics.management.webapi.base.enums;

public enum  ContractNatureEnum {
    DEFAULT_VALUE(0,""),
    SUPPLY_CONTRACT(1, "货源合同"),
    CAR_CONTRACTS(2, "车源合同"),
    LEASE_CONTRACT(3,"租赁合同"),
    ;
    private Integer key;
    private String value;

    ContractNatureEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static ContractNatureEnum getEnum(Integer key) {
        for (ContractNatureEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
