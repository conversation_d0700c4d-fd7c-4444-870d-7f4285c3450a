package com.logistics.tms.controller.staff.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class OccupationalAddOrModifyRequestModel {

    @ApiModelProperty("从业资格记录Id")
    private Long occupationalRecordId;
    @ApiModelProperty("人员Id")
    private Long staffId;
    @ApiModelProperty("发证日期 2019-12-12 00:00:00")
    private Date issueDate;
    @ApiModelProperty("诚信考核有效期  2019-12-12 00:00:00")
    private Date validDate;
    @ApiModelProperty("凭证地址")
    private List<String> imagePaths;
    @ApiModelProperty("备注")
    private String remark;
}
