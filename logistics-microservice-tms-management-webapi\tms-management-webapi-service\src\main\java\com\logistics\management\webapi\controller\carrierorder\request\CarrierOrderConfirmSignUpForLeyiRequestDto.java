package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/1
 */
@Data
public class CarrierOrderConfirmSignUpForLeyiRequestDto {

	@ApiModelProperty("签收时间")
	private String signTime;

	@Valid
	@NotEmpty(message = "请填写签收信息")
	private List<CarrierOrderSignUpForLeyiRequestDto> signList;
}
