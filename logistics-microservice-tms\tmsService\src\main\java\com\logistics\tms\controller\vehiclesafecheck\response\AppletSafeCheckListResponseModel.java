package com.logistics.tms.controller.vehiclesafecheck.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/11/25 11:46
 */
@Data
public class AppletSafeCheckListResponseModel {
    @ApiModelProperty("安全检查id")
    private Long safeCheckVehicleId;
    @ApiModelProperty("车辆检查名称-年月+车辆安全检查")
    private String period;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("状态: 0未检查、10待确认、20待整改、30已整改、40检查完成")
    private Integer status;
    @ApiModelProperty("检查时间")
    private Date checkTime;
    @ApiModelProperty("整改项")
    private Integer reformItemCount;
}
