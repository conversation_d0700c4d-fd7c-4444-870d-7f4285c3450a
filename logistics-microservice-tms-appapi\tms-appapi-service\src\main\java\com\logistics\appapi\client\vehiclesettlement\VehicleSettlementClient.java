package com.logistics.appapi.client.vehiclesettlement;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.vehiclesettlement.hystrix.VehicleSettlementClientHystrix;
import com.logistics.appapi.client.vehiclesettlement.request.DriverReconciliationConfirmRequestModel;
import com.logistics.appapi.client.vehiclesettlement.request.SearchDriverReconciliationListRequestModel;
import com.logistics.appapi.client.vehiclesettlement.request.VehicleSettlementIdRequestModel;
import com.logistics.appapi.client.vehiclesettlement.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/2/22 10:36
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/vehicleSettlement",
        fallback = VehicleSettlementClientHystrix.class)
public interface VehicleSettlementClient {

    @ApiOperation(("司机账单列表"))
    @PostMapping(value = "/searchDriverReconciliationList")
    Result<PageInfo<SearchDriverReconciliationListResponseModel>> searchDriverReconciliationList(@RequestBody SearchDriverReconciliationListRequestModel requestModel);

    @ApiOperation(("司机账单列表数量统计"))
    @PostMapping(value = "/driverReconciliationListCount")
    Result<DriverReconciliationListCountResponseModel> driverReconciliationListCount(@RequestBody SearchDriverReconciliationListRequestModel requestModel);

    @ApiOperation(("司机账单详情"))
    @PostMapping(value = "/driverReconciliationDetail")
    Result<DriverReconciliationDetailResponseModel> driverReconciliationDetail(@RequestBody VehicleSettlementIdRequestModel requestModel);

    @ApiOperation(("司机确认账单"))
    @PostMapping(value = "/driverReconciliationConfirm")
    Result driverReconciliationConfirm(@RequestBody DriverReconciliationConfirmRequestModel requestModel);

    @ApiOperation(("司机确认详情"))
    @PostMapping(value = "/driverReconciliationConfirmDetail")
    Result<DriverReconciliationConfirmDetailResponseModel> driverReconciliationConfirmDetail(@RequestBody VehicleSettlementIdRequestModel requestModel);

    @ApiOperation(("司机账单运单列表"))
    @PostMapping(value = "/driverReconciliationCarrierOrder")
    Result<ReconciliationCarrierOrderDetailResponseModel> driverReconciliationCarrierOrder(@RequestBody VehicleSettlementIdRequestModel requestModel);

    @ApiOperation(("司机账单收款记录"))
    @PostMapping(value = "/driverReconciliationBillingRecords")
    Result<List<ReconciliationBillingRecordsResponseModel>> driverReconciliationBillingRecords(@RequestBody VehicleSettlementIdRequestModel requestModel);

    @ApiOperation(("司机账单轮胎费用列表"))
    @PostMapping(value = "/driverReconciliationTire")
    Result<ReconciliationTireDetailResponseModel> driverReconciliationTire(@RequestBody VehicleSettlementIdRequestModel requestModel);

    @ApiOperation(("司机账单充油费用列表"))
    @PostMapping(value = "/driverReconciliationOilFilled")
    Result<ReconciliationOilFilledDetailResponseModel> driverReconciliationOilFilled(@RequestBody VehicleSettlementIdRequestModel requestModel);

}
