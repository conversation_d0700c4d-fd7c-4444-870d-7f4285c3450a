package com.logistics.management.webapi.controller.workgroup.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.*;
import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/22 14:19
 */
@Data
public class AddEditWorkGroupNodeListRequestDto {

    @ApiModelProperty(value = "节点id,编辑时填写")
    private String workGroupNodeId;

    @ApiModelProperty(value = "业务单据, 10:需求单 20:运单", required = true)
    @Pattern(regexp = "^(10|20)$", message = "请选择单据类型")
    private String orderType;

    @ApiModelProperty(value = "(3.18.0)业务节点, 101:下单 102:调度 103:取消；201:提货 202:纠错 203:取消 204:生成运单 205:修改车辆 206:卸货 207:到达提货地 208:到达卸货地", required = true)
    @Pattern(regexp = "^(101|102|103|201|202|203|204|205|206|207|208)$", message = "请选择单据节点")
    private String orderNode;

    @ApiModelProperty(value = "时间要求,仅需求单&下单节点展示此字段并必填")
    @Range(min = 0, max = 7, message = "时间要求为0~7天")
    private String timeRequire;

    @ApiModelProperty(value = "取值要求, 101:委托数 102:已安排 103:未安排；201:预提数 202:实提数 203:差异数(预提-实提)", required = true)
    @Pattern(regexp = "^(101|102|103|201|202|203)$", message = "请选择取值要求类型")
    private String amountType;

    @ApiModelProperty(value = "取值要求符号, 1: >= 2: = 3: <=", required = true)
    @Pattern(regexp = "^([123])$", message = "请选择取值要求符号")
    private String amountSymbol;

    @ApiModelProperty(value = "取值要求,0~10000整数", required = true)
    @NotBlank(message = "请输入取值要求")
    @Max(value = 10000, message = "请输入正确的取值要求,为0~10000整数")
    @Digits(integer = 5, fraction = 0, message = "请输入正确的取值要求,为0~10000整数")
    private String amountRequire;

    @ApiModelProperty(value = "模板字段", required = true)
    @NotEmpty(message = "请选择模板字段")
    @Size(max = 15, message = "最多选择15个模板字段")
    private List<String> fieldList;
}
