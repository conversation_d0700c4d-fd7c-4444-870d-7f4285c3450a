package com.logistics.management.webapi.api.impl.safegroupmeeting.mapping;

import com.logistics.management.webapi.api.feign.safegroupmeeting.dto.SafeGroupMeetingDetailResponseDto;
import com.logistics.management.webapi.api.feign.safegroupmeeting.dto.SafetyGroupMeetingAttachmentDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.SafeGroupMeetingSeasonEnum;
import com.logistics.tms.api.feign.safegroupmeeting.model.SafeGroupMeetingDetailResponseModel;
import com.logistics.tms.api.feign.safegroupmeeting.model.SafetyGroupMeetingAttachmentModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * @author: wjf
 * @date: 2019/6/4 11:09
 */
public class SafeGroupMeetingDetailMapping extends MapperMapping<SafeGroupMeetingDetailResponseModel, SafeGroupMeetingDetailResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;
    public SafeGroupMeetingDetailMapping(String imagePrefix ,Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        SafeGroupMeetingDetailResponseModel source = getSource();
        SafeGroupMeetingDetailResponseDto destination = getDestination();
        if (source != null){
            destination.setMeetingSeasonLabel(SafeGroupMeetingSeasonEnum.getEnum(source.getMeetingSeason()).getValue());
            destination.setMeetingTime(DateUtils.dateToString(source.getMeetingTime(), CommonConstant.DATE_TO_STRING_YMD_PATTERN));
            List<SafetyGroupMeetingAttachmentModel> safetyGroupMeetingAttachmentList = source.getSafetyGroupMeetingAttachmentList();
            if (ListUtils.isNotEmpty(safetyGroupMeetingAttachmentList)){
                List<SafetyGroupMeetingAttachmentDto> safetyGroupMeetingAttachmentDtoList  = new ArrayList<>();
                for (SafetyGroupMeetingAttachmentModel safetyGroupMeetingAttachmentModel : safetyGroupMeetingAttachmentList){
                    SafetyGroupMeetingAttachmentDto safetyGroupMeetingAttachmentDto = new SafetyGroupMeetingAttachmentDto();
                    safetyGroupMeetingAttachmentDto.setMeetingImagePath(imagePrefix+imageMap.get(safetyGroupMeetingAttachmentModel.getMeetingImagePath()));
                    safetyGroupMeetingAttachmentDto.setType(safetyGroupMeetingAttachmentModel.getType().toString());
                    safetyGroupMeetingAttachmentDtoList.add(safetyGroupMeetingAttachmentDto);
                }

                destination.setSafetyGroupMeetingAttachmentList(safetyGroupMeetingAttachmentDtoList);
            }
        }
    }

}
