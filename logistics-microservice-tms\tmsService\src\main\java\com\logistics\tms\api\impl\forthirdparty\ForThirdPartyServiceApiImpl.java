package com.logistics.tms.api.impl.forthirdparty;

import com.logistics.tms.api.feign.forthirdparty.ForThirdPartyServiceApi;
import com.logistics.tms.api.feign.forthirdparty.model.request.WorkOrderProcessSyncRequestModel;
import com.logistics.tms.biz.workordercenter.WorkOrderBiz;
import com.yelo.tray.core.base.Result;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class ForThirdPartyServiceApiImpl implements ForThirdPartyServiceApi {

    @Resource
    private WorkOrderBiz workOrderBiz;

    /**
     * 工单处理流程同步 - 智慧运营(任务中心)调用
     * @param requestModel 请求Model
     * @return boolean
     */
    @Override
    public Result<Boolean> syncWorkOrderProcess(WorkOrderProcessSyncRequestModel requestModel) {
        return Result.success(workOrderBiz.syncWorkOrderProcess(requestModel));
    }
}
