package com.logistics.management.webapi.api.feign.insuarance.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/6/4 18:52
 */
@Data
public class AddOrModifyInsuranceRequestDto {
    @ApiModelProperty("保险id")
    private String insuranceId;
    @ApiModelProperty("险种：1 商业险，2 交强险，3 个人意外险，4 货物险，5 危货承运人险")
    @NotBlank(message = "请选择险种")
    private String insuranceType;
    @ApiModelProperty("车牌号")
    @NotBlank(message = "请维护车牌号")
    private String vehicleId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机")
    @NotBlank(message = "请维护司机")
    private String driverId;
    @ApiModelProperty("保险公司id")
    @NotBlank(message = "请维护保险公司")
    private String insuranceCompanyId;
    @ApiModelProperty("保单号")
    @NotBlank(message = "请维护保险单号")
    private String policyNumber;
    @ApiModelProperty("保费")
    @NotBlank(message = "请维护保费")
    @Pattern(regexp = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,2})$)",message = "请维护保费")
    private String premium;
    @ApiModelProperty("保险生效时间")
    @NotBlank(message = "请维护保险生效时间")
    @Pattern(regexp = "|[\\d]{4}-[\\d]{1,2}-[\\d]{1,2} [\\d]{1,2}:[\\d]{1,2}",message = "请维护正确的保险生效时间")
    private String startTime;
    @ApiModelProperty("保险截止时间")
    @NotBlank(message = "请维护保险截止时间")
    @Pattern(regexp = "|[\\d]{4}-[\\d]{1,2}-[\\d]{1,2} [\\d]{1,2}:[\\d]{1,2}",message = "请维护正确的保险截止时间")
    private String endTime;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("凭证")
    private List<InsuranceTicketsRequestDto> ticketList;

    @ApiModelProperty("代缴车船税")
    private String paymentOfVehicleAndVesselTax;

    @ApiModelProperty("个人意外险表id")
    private String personalAccidentInsuranceId;
    @ApiModelProperty("批单号")
    private String batchNumber;
    @ApiModelProperty("保单类型：1 保单，2 批单")
    private String policyType;
    @ApiModelProperty("保笔保金")
    private String singlePremium;
    @ApiModelProperty("关联扣费保单id")
    private String relatedPersonalAccidentInsuranceId;
}
