package com.logistics.management.webapi.controller.oilfilled.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/10/9
 * @description:
 */
@Data
public class OilFilledGetSummaryResponseDto {

    @ApiModelProperty(value = "待结算数量")
    private String waitSettleCount="";
    @ApiModelProperty(value = "已结算数量")
    private String haveSettleCount="";

}
