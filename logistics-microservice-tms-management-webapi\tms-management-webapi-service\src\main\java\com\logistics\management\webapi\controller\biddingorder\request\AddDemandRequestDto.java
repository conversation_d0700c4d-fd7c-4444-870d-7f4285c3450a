package com.logistics.management.webapi.controller.biddingorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class AddDemandRequestDto {

    /**
     * 竞价单id
     */
    @ApiModelProperty("竞价单id")
    @NotBlank(message = "竞价单id不能为空")
    private String biddingOrderId;

    /**
     * 需求单id
     */
    @ApiModelProperty("需求单id")
    @NotBlank(message = "需求单id不能为空")
    private String demandOrderId;


}
