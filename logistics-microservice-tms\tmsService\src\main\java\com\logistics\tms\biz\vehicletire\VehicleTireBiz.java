package com.logistics.tms.biz.vehicletire;

import com.github.pagehelper.PageInfo;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tray.core.exception.BizException;
import com.logistics.tms.controller.staffvehiclerelation.request.SearchStaffVehicleListRequestModel;
import com.logistics.tms.controller.staffvehiclerelation.response.SearchStaffVehicleListResponseModel;
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel;
import com.logistics.tms.api.feign.vehicletire.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VehicleTireBiz {

    @Autowired
    private TVehicleTireMapper tqVehicleTireMapper;
    @Autowired
    private TCertificationPicturesMapper tqCertificationPicturesMapper;
    @Autowired
    private TVehicleBasicMapper tqVehicleBasicMapper;
    @Autowired
    private TVehicleTireNoMapper tqVehicleTireNoMapper;
    @Autowired
    private TStaffBasicMapper tqStaffBasicMapper;
    @Autowired
    private TVehicleSettlementRelationMapper tVehicleSettlementRelationMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TStaffVehicleRelationMapper tStaffVehicleRelationMapper;
    @Autowired
    private TVehicleDrivingLicenseMapper tVehicleDrivingLicenseMapper;

    /**
     * 查询轮胎管理列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<VehicleTireListResponseModel> searchVehicleTireList(VehicleTireListRequestModel requestModel) {
        List<Long> vehicleTireIdsList = tqVehicleTireMapper.searchVehicleTireIdsList(requestModel);
        PageInfo pageInfo = new PageInfo(vehicleTireIdsList);
        if (ListUtils.isNotEmpty(vehicleTireIdsList)) {
            List<VehicleTireListResponseModel> vehicleTireList = tqVehicleTireMapper.searchVehicleTireList(StringUtils.listToString(vehicleTireIdsList, ','));
            pageInfo.setList(ListUtils.isEmpty(vehicleTireList) ? new ArrayList<>() : vehicleTireList);
        } else {
            pageInfo.setList(new ArrayList<>());
        }
        return pageInfo;
    }

    /**
     * 查看轮胎管理列表详情
     *
     * @param requestModel
     * @return
     */
    public VehicleTireDetailResponseModel getVehicleTireDetail(VehicleTireIdRequestModel requestModel) {
        VehicleTireDetailResponseModel responseModel = tqVehicleTireMapper.getVehicleTireDetailById(requestModel.getVehicleTireId());
        if (responseModel == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_TIRE_IS_EMPTY);
        }
        if(responseModel.getTrailerVehicleId() != null && responseModel.getTrailerVehicleId() > CommonConstant.LONG_ZERO){
            TVehicleDrivingLicense tVehicleDrivingLicense = tVehicleDrivingLicenseMapper.getByVehicleId(responseModel.getTrailerVehicleId());
            responseModel.setTrailerVehicleNo(tVehicleDrivingLicense.getVehicleNo());
        }
        //查询该轮胎记录是否关联了结算费用
        List<TVehicleSettlementRelation> tVehicleSettlementRelationList = tVehicleSettlementRelationMapper.getByObjectIdAndType(VehicleSettlementTypeEnum.TIRE.getKey(), responseModel.getVehicleTireId());
        if (ListUtils.isNotEmpty(tVehicleSettlementRelationList)) {
            responseModel.setIfSettlement(CommonConstant.INTEGER_ONE);
        }
        return responseModel;
    }

    /**
     * 新增/修改轮胎信息
     *
     * @param requestModel
     */
    @Transactional
    public void addOrModifyVehicleTire(AddOrModifyVehicleTireRequestModel requestModel) {
        TStaffBasic tqStaffBasic = tqStaffBasicMapper.selectByPrimaryKey(requestModel.getStaffId());
        if (tqStaffBasic == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }
        VehicleBasicPropertyModel tqVehicleBasic = tqVehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getVehicleId());
        if (tqVehicleBasic == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_NOT_EXIST);
        }

        SearchStaffVehicleListRequestModel model = new SearchStaffVehicleListRequestModel();
        model.setVehicleIds(ConverterUtils.toString(requestModel.getVehicleId()));
        List<SearchStaffVehicleListResponseModel> relModelList = tStaffVehicleRelationMapper.searchStaffVehicleList(model);
        SearchStaffVehicleListResponseModel relModel = new SearchStaffVehicleListResponseModel();
        if (ListUtils.isNotEmpty(relModelList)) {
            relModel = relModelList.get(CommonConstant.INTEGER_ZERO);
        }

        TVehicleTire vehicleTire = new TVehicleTire();
        vehicleTire.setStaffId(requestModel.getStaffId());
        vehicleTire.setVehicleId(requestModel.getVehicleId());
        if (relModel.getTrailerVehicleId() != null && relModel.getTrailerVehicleId() > CommonConstant.LONG_ZERO) {
            vehicleTire.setTrailerVehicleId(relModel.getTrailerVehicleId());
        }else{
            vehicleTire.setTrailerVehicleId(CommonConstant.LONG_ZERO);
        }
        vehicleTire.setReplaceDate(requestModel.getReplaceDate());
        vehicleTire.setTireCompany(requestModel.getTireCompany());
        vehicleTire.setRemark(requestModel.getRemark());
        GetTireByTireIdsModel tqVehicleTire = tqVehicleTireMapper.getByTireIdOrVehicleId(null, requestModel.getVehicleId(), requestModel.getReplaceDate());
        String userName = BaseContextHandler.getUserName();

        if (requestModel.getVehicleTireId() == null || requestModel.getVehicleTireId() <= CommonConstant.LONG_ZERO) {//新增
            if (tqVehicleTire != null) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_DATE_REPEAT);
            }
            //必须是内部车辆
            if (!(VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(tqVehicleBasic.getVehicleProperty()) ||
                    VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(tqVehicleBasic.getVehicleProperty()))) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_PROPERTY_ERROR);
            }

            //设置车辆机构
            vehicleTire.setVehicleProperty(tqVehicleBasic.getVehicleProperty());
            commonBiz.setBaseEntityAdd(vehicleTire, BaseContextHandler.getUserName());
            tqVehicleTireMapper.insertSelective(vehicleTire);
        } else {//修改
            //判断车辆机构,如果未修改车辆则不判断车辆机构
            if (!tqVehicleBasic.getVehicleId().equals(requestModel.getVehicleId()) &&
                    !(VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(tqVehicleBasic.getVehicleProperty()) || VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(tqVehicleBasic.getVehicleProperty()))) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_PROPERTY_ERROR);
            }

            TVehicleTire tVehicleTire = tqVehicleTireMapper.selectByPrimaryKey(requestModel.getVehicleTireId());
            if (tVehicleTire == null || CommonConstant.INTEGER_ZERO.equals(tVehicleTire.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_TIRE_IS_EMPTY);
            }
            if (tqVehicleTire != null && !tqVehicleTire.getVehicleTireId().equals(requestModel.getVehicleTireId())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_DATE_REPEAT);
            }
            if (OilFilledStatusEnum.HAVE_SETTLE.getKey().equals(tVehicleTire.getSettlementStatus())) {
                throw new BizException(CarrierDataExceptionEnum.SETTLEMENT_STATUS_ERROR_NOT_UPDATE);
            }
            //查询该轮胎费用是否关联了结算
            List<TVehicleSettlementRelation> tVehicleSettlementRelationList = tVehicleSettlementRelationMapper.getByObjectIdAndType(VehicleSettlementTypeEnum.TIRE.getKey(), requestModel.getVehicleTireId());
            //如果轮胎费用关联了结算数据，那么以下字段不允许赋值更新
            if (ListUtils.isNotEmpty(tVehicleSettlementRelationList)) {
                //处理牌号信息,如果已经关联了结算信息，不允许操作任何轮胎费用,所以置空
                requestModel.setVehicleTireNoList(null);
                vehicleTire.setVehicleId(null);
            }
            //更换了车辆才更新车辆机构
            if (!tVehicleTire.getVehicleId().equals(requestModel.getVehicleId())) {
                vehicleTire.setVehicleProperty(tqVehicleBasic.getVehicleProperty());
            }
            vehicleTire.setId(requestModel.getVehicleTireId());
            commonBiz.setBaseEntityModify(vehicleTire, BaseContextHandler.getUserName());
            tqVehicleTireMapper.updateByPrimaryKeySelective(vehicleTire);
            //处理牌号信息
            if (ListUtils.isNotEmpty(requestModel.getVehicleTireNoList())) {
                List<TVehicleTireNo> vehicleTireNoList = tqVehicleTireNoMapper.getByTireId(requestModel.getVehicleTireId());
                List<Long> tireNoIdList = vehicleTireNoList.stream().map(TVehicleTireNo::getId).collect(Collectors.toList());
                List<VehicleTireNoListRequestModel> addList = requestModel.getVehicleTireNoList().stream().filter(item -> item.getVehicleTrieNoId() == null || item.getVehicleTrieNoId() <= CommonConstant.LONG_ZERO).collect(Collectors.toList());
                List<VehicleTireNoListRequestModel> updateList = requestModel.getVehicleTireNoList().stream().filter(item -> item.getVehicleTrieNoId() != null && item.getVehicleTrieNoId() > CommonConstant.LONG_ZERO).collect(Collectors.toList());
                requestModel.setVehicleTireNoList(addList);
                List<Long> upTireNoIdList = new ArrayList<>();
                TVehicleTireNo upTire;
                List<TVehicleTireNo> upList = new ArrayList<>();
                if (ListUtils.isNotEmpty(updateList)) {
                    for (VehicleTireNoListRequestModel tire : updateList) {
                        upTire = new TVehicleTireNo();
                        upTire.setId(tire.getVehicleTrieNoId());
                        upTire.setTireBrand(tire.getTireBrand());
                        upTire.setAmount(tire.getAmount());
                        upTire.setUnitPrice(tire.getUnitPrice());
                        commonBiz.setBaseEntityModify(upTire, userName);
                        upList.add(upTire);
                        upTireNoIdList.add(tire.getVehicleTrieNoId());
                    }
                }
                if (ListUtils.isNotEmpty(upTireNoIdList)){
                    tireNoIdList.removeAll(upTireNoIdList);
                    if (ListUtils.isNotEmpty(tireNoIdList)){
                        for (Long id:tireNoIdList) {
                            upTire = new TVehicleTireNo();
                            upTire.setId(id);
                            upTire.setValid(CommonConstant.INTEGER_ZERO);
                            commonBiz.setBaseEntityModify(upTire,userName);
                            upList.add(upTire);
                        }
                    }
                }
                if (ListUtils.isNotEmpty(upList)) {
                    tqVehicleTireNoMapper.batchUpdate(upList);
                }
            }
            //处理凭证信息
            List<TCertificationPictures> certificationList = tqCertificationPicturesMapper.getByObjectIdType(requestModel.getVehicleTireId(), CertificationPicturesObjectTypeEnum.T_VEHICLE_TIRE.getObjectType(), CertificationPicturesFileTypeEnum.VEHICLE_TIRE.getFileType());
            List<String> vehicleTirePathList = certificationList.stream().map(TCertificationPictures::getFilePath).collect(Collectors.toList());
            List<Long> vehicleTireIdList = certificationList.stream().map(TCertificationPictures::getId).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(requestModel.getFileList())) {
                List<CertificationPicturesInfoRequestModel> ticketsListUpdate = requestModel.getFileList().stream().filter(item -> item.getFileId() != null && item.getFileId() > CommonConstant.LONG_ZERO).collect(Collectors.toList());
                List<CertificationPicturesInfoRequestModel> ticketsListRequest = requestModel.getFileList().stream().filter(item -> item.getFileId() == null || item.getFileId() <= CommonConstant.LONG_ZERO).collect(Collectors.toList());
                requestModel.setFileList(ticketsListRequest);
                TCertificationPictures certificationPictures;
                List<TCertificationPictures> upList = new ArrayList<>();
                List<Long> idList = new ArrayList<>();
                if (ListUtils.isNotEmpty(ticketsListUpdate)) {
                    for (CertificationPicturesInfoRequestModel ticket : ticketsListUpdate) {
                        if (!vehicleTirePathList.contains(ticket.getFilePath())) {
                            certificationPictures = new TCertificationPictures();
                            certificationPictures.setId(ticket.getFileId());
                            certificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.VEHICLE_TIRE.getKey(), "", ticket.getFilePath(), null));
                            commonBiz.setBaseEntityModify(certificationPictures, userName);
                            upList.add(certificationPictures);
                        }
                        idList.add(ticket.getFileId());
                    }
                    if (ListUtils.isNotEmpty(idList)) {
                        vehicleTireIdList.removeAll(idList);
                        if (ListUtils.isNotEmpty(vehicleTireIdList)) {
                            for (Long id : vehicleTireIdList) {
                                certificationPictures = new TCertificationPictures();
                                certificationPictures.setId(id);
                                certificationPictures.setValid(CommonConstant.INTEGER_ZERO);
                                commonBiz.setBaseEntityModify(certificationPictures, userName);
                                upList.add(certificationPictures);
                            }
                        }
                    }
                } else {
                    for (TCertificationPictures ticket : certificationList) {
                        certificationPictures = new TCertificationPictures();
                        certificationPictures.setId(ticket.getId());
                        certificationPictures.setValid(CommonConstant.INTEGER_ZERO);
                        commonBiz.setBaseEntityModify(certificationPictures, userName);
                        upList.add(certificationPictures);
                    }
                }
                if (ListUtils.isNotEmpty(upList)) {
                    tqCertificationPicturesMapper.batchUpdate(upList);
                }
            }
        }
        //批量插入轮胎牌号信息
        if (ListUtils.isNotEmpty(requestModel.getVehicleTireNoList())) {
            TVehicleTireNo vehicleTireNo;
            List<TVehicleTireNo> addList = new ArrayList<>();
            for (VehicleTireNoListRequestModel tmp : requestModel.getVehicleTireNoList()) {
                vehicleTireNo = new TVehicleTireNo();
                vehicleTireNo.setTireId(vehicleTire.getId());
                vehicleTireNo.setTireBrand(tmp.getTireBrand());
                vehicleTireNo.setAmount(tmp.getAmount());
                vehicleTireNo.setUnitPrice(tmp.getUnitPrice());
                commonBiz.setBaseEntityAdd(vehicleTireNo, userName);
                addList.add(vehicleTireNo);
            }
            if (ListUtils.isNotEmpty(addList)) {
                tqVehicleTireNoMapper.batchInsert(addList);
            }
        }
        //批量插入轮胎凭证信息
        if (ListUtils.isNotEmpty(requestModel.getFileList())) {
            List<TCertificationPictures> retList = new ArrayList<>();
            TCertificationPictures newPictures;
            Date now = new Date();
            for (CertificationPicturesInfoRequestModel tmp : requestModel.getFileList()) {
                newPictures = new TCertificationPictures();
                newPictures.setObjectId(vehicleTire.getId());
                newPictures.setObjectType(CertificationPicturesObjectTypeEnum.T_VEHICLE_TIRE.getObjectType());
                newPictures.setFileType(CertificationPicturesFileTypeEnum.VEHICLE_TIRE.getFileType());
                newPictures.setFileTypeName(CertificationPicturesFileTypeEnum.VEHICLE_TIRE.getFileName());
                newPictures.setUploadTime(now);
                newPictures.setUploadUserName(userName);
                newPictures.setSuffix(tmp.getFilePath().substring(tmp.getFilePath().lastIndexOf('.')));
                newPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.VEHICLE_TIRE.getKey(), "", tmp.getFilePath(), null));
                commonBiz.setBaseEntityAdd(newPictures, userName);
                retList.add(newPictures);
            }
            if (ListUtils.isNotEmpty(retList)) {
                tqCertificationPicturesMapper.batchInsert(retList);
            }
        }
    }

    /**
     * 删除轮胎管理信息
     *
     * @param requestModel
     */
    @Transactional
    public void deleteVehicleTire(VehicleTireIdRequestModel requestModel) {
        TVehicleTire tqVehicleTire = tqVehicleTireMapper.selectByPrimaryKey(requestModel.getVehicleTireId());
        if (tqVehicleTire == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_TIRE_IS_EMPTY);
        }
        List<TVehicleSettlementRelation> relation = tVehicleSettlementRelationMapper.getByObjectIdAndType(VehicleSettlementTypeEnum.TIRE.getKey(), tqVehicleTire.getId());
        if(ListUtils.isNotEmpty(relation)){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_TIRE_CANNOT_DEL);
        }

        GetTireByTireIdsModel getTireByTireIdsModel = tqVehicleTireMapper.getByTireIdOrVehicleId(requestModel.getVehicleTireId(), null, null);
        String userName = BaseContextHandler.getUserName();
        TVehicleTire delTire = new TVehicleTire();
        delTire.setId(getTireByTireIdsModel.getVehicleTireId());
        delTire.setValid(CommonConstant.INTEGER_ZERO);
        commonBiz.setBaseEntityModify(delTire, userName);
        tqVehicleTireMapper.updateByPrimaryKeySelective(delTire);

        TVehicleTireNo delTireNo;
        List<TVehicleTireNo> delTireNoList = new ArrayList<>();
        for (GetTireNoByTireIdsModel tireNo : getTireByTireIdsModel.getTireNoList()) {
            delTireNo = new TVehicleTireNo();
            delTireNo.setId(tireNo.getVehicleTrieNoId());
            delTireNo.setValid(CommonConstant.INTEGER_ZERO);
            commonBiz.setBaseEntityModify(delTireNo, userName);
            delTireNoList.add(delTireNo);
        }
        if (ListUtils.isNotEmpty(delTireNoList)) {
            tqVehicleTireNoMapper.batchUpdate(delTireNoList);
        }
    }

    /**
     * Excel导入轮胎管理信息
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public ImportVehicleTireInfoResponseModel importExcelInfoVehicleTireInfo(ImportVehicleTireInfoRequestModel requestModel) {
        ImportVehicleTireInfoResponseModel responseModel = new ImportVehicleTireInfoResponseModel();
        Integer numberFailures = requestModel.getNumberFailures();
        Integer numberSuccessful = 0;
        String userName = BaseContextHandler.getUserName();
        TStaffBasic tqStaffBasic;
        TVehicleBasic tqVehicleBasic;
        GetTireByTireIdsModel getTireByTireIdsModel;
        TVehicleTireNo vehicleTireNo;
        List<TVehicleTireNo> addTireNoList = new ArrayList<>();
        List<TVehicleTireNo> upTireNoList = new ArrayList<>();
        TVehicleTire vehicleTire;
        List<TVehicleTire> upTireList = new ArrayList<>();
        Date now = new Date();

        //轮胎导入排除外部车辆与司机，只允许维护内部信息
        for (ImportVehicleTireInfoListRequestModel model : requestModel.getImportList()) {
            //查询车辆
            tqVehicleBasic = tqVehicleBasicMapper.getInfoByVehicleNo(model.getVehicleNo());
            if (tqVehicleBasic == null
                    || OperatingStateEnum.OUT_OPERATION.getKey().equals(tqVehicleBasic.getOperatingState())) {
                numberFailures++;
                continue;
            }
            tqStaffBasic = tqStaffBasicMapper.getByMobile(model.getDriveMobile());
            if (tqStaffBasic == null
                    || CommonConstant.INTEGER_ZERO.equals(tqStaffBasic.getType())
                    || CommonConstant.INTEGER_TWO.equals(tqStaffBasic.getType())) {
                numberFailures++;
                continue;
            }

            //查询已存在的轮胎费用
            getTireByTireIdsModel = tqVehicleTireMapper.getByTireIdOrVehicleId(null, tqVehicleBasic.getId(), model.getReplaceDate());

            if (getTireByTireIdsModel != null) {//已存在，牌号有则更新，没有则新增

                //校验车辆机构,车辆未变更不校验
                if (!tqVehicleBasic.getId().equals(getTireByTireIdsModel.getVehicleId())) {
                    if (VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(tqVehicleBasic.getVehicleProperty())) {
                        numberFailures++;
                        continue;
                    }
                }

                //校验司机,司机未变更不校验
                if (!tqStaffBasic.getId().equals(getTireByTireIdsModel.getStaffId())) {
                    if (VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(tqVehicleBasic.getVehicleProperty())) {
                        numberFailures++;
                        continue;
                    }
                }

                vehicleTire = new TVehicleTire();
                vehicleTire.setId(getTireByTireIdsModel.getVehicleTireId());
                vehicleTire.setStaffId(tqStaffBasic.getId());
                vehicleTire.setTireCompany(model.getTireCompany());
                vehicleTire.setRemark(model.getRemark());
                commonBiz.setBaseEntityModify(vehicleTire, userName);
                upTireList.add(vehicleTire);

                List<String> brandList = new ArrayList<>();
                Map<String, Long> brandMap = new HashMap<>();
                Map<String, Date> brandTimeMap = new HashMap<>();
                getTireByTireIdsModel.getTireNoList().stream().forEach(item -> {
                    brandList.add(item.getTireBrand());
                    brandMap.put(item.getTireBrand(), item.getVehicleTrieNoId());
                    brandTimeMap.put(item.getTireBrand(), item.getCreatedTime());
                });
                for (ImportVehicleTireListRequestModel tire : model.getVehicleTireList()) {
                    vehicleTireNo = new TVehicleTireNo();
                    vehicleTireNo.setTireBrand(tire.getTireBrand());
                    vehicleTireNo.setAmount(tire.getAmount());
                    vehicleTireNo.setUnitPrice(tire.getUnitPrice());
                    if (brandList.contains(tire.getTireBrand())) {
                        vehicleTireNo.setId(brandMap.get(tire.getTireBrand()));
                        commonBiz.setBaseEntityModify(vehicleTireNo, userName);
                        upTireNoList.add(vehicleTireNo);
                    } else {
                        vehicleTireNo.setTireId(getTireByTireIdsModel.getVehicleTireId());
                        commonBiz.setBaseEntityAdd(vehicleTireNo, userName);
                        addTireNoList.add(vehicleTireNo);
                    }
                    brandTimeMap.put(tire.getTireBrand(), now);
                }
                if (brandTimeMap.size() > 5) {
                    Integer count = brandTimeMap.size();
                    Map<String, Date> result = new LinkedHashMap<>();
                    brandTimeMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).forEachOrdered(x -> result.put(x.getKey(), x.getValue()));
                    for (String brand : result.keySet()) {
                        vehicleTireNo = new TVehicleTireNo();
                        vehicleTireNo.setId(brandMap.get(brand));
                        vehicleTireNo.setValid(CommonConstant.INTEGER_ZERO);
                        commonBiz.setBaseEntityModify(vehicleTireNo, userName);
                        upTireNoList.add(vehicleTireNo);
                        count--;
                        if (count == 5) {
                            break;
                        }
                    }
                }
            } else {
                //非外部车辆
                if (VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(tqVehicleBasic.getVehicleProperty())) {
                    numberFailures++;
                    continue;
                }

                //非外部司机
                if (StaffPropertyEnum.EXTERNAL_STAFF.getKey().equals(tqStaffBasic.getStaffProperty())) {
                    numberFailures++;
                    continue;
                }

                vehicleTire = new TVehicleTire();
                vehicleTire.setStaffId(tqStaffBasic.getId());
                vehicleTire.setVehicleId(tqVehicleBasic.getId());
                vehicleTire.setVehicleProperty(tqVehicleBasic.getVehicleProperty());
                vehicleTire.setReplaceDate(model.getReplaceDate());
                vehicleTire.setTireCompany(model.getTireCompany());
                vehicleTire.setRemark(model.getRemark());
                commonBiz.setBaseEntityAdd(vehicleTire, userName);
                tqVehicleTireMapper.insertSelective(vehicleTire);
                for (ImportVehicleTireListRequestModel tire : model.getVehicleTireList()) {
                    vehicleTireNo = new TVehicleTireNo();
                    vehicleTireNo.setTireId(vehicleTire.getId());
                    vehicleTireNo.setTireBrand(tire.getTireBrand());
                    vehicleTireNo.setAmount(tire.getAmount());
                    vehicleTireNo.setUnitPrice(tire.getUnitPrice());
                    commonBiz.setBaseEntityAdd(vehicleTireNo, userName);
                    addTireNoList.add(vehicleTireNo);
                }
            }
            numberSuccessful++;
        }
        if (ListUtils.isNotEmpty(upTireList)) {
            tqVehicleTireMapper.batchUpdate(upTireList);
        }
        if (ListUtils.isNotEmpty(addTireNoList)) {
            tqVehicleTireNoMapper.batchInsert(addTireNoList);
        }
        if (ListUtils.isNotEmpty(upTireNoList)) {
            tqVehicleTireNoMapper.batchUpdate(upTireNoList);
        }
        responseModel.setNumberFailures(numberFailures);
        responseModel.setNumberSuccessful(numberSuccessful);
        return responseModel;
    }

    /**
     * 导入轮胎管理凭证
     */
    @Transactional
    public void importVehicleTireCertificateInfo(ImportTireCertificateRequestModel requestModel) {
        //判断车牌号是否存在
        TVehicleBasic tqVehicleBasic = tqVehicleBasicMapper.getInfoByVehicleNo(requestModel.getVehicleNo());
        if (tqVehicleBasic == null) {
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }
        GetTireByTireIdsModel tqVehicleTire = tqVehicleTireMapper.getByTireIdOrVehicleId(null, tqVehicleBasic.getId(), requestModel.getReplaceDate());
        if (tqVehicleTire == null) {
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }
        String userName = BaseContextHandler.getUserName();
        Integer objectType = CertificationPicturesObjectTypeEnum.T_VEHICLE_TIRE.getObjectType();
        CertificationPicturesFileTypeEnum fileTypeEnum = CertificationPicturesFileTypeEnum.VEHICLE_TIRE;
        List<TCertificationPictures> certificationList = tqCertificationPicturesMapper.getByObjectIdType(tqVehicleTire.getVehicleTireId(), objectType, fileTypeEnum.getFileType());
        if (ListUtils.isNotEmpty(certificationList) && certificationList.size() > 3) {
            TCertificationPictures del = new TCertificationPictures();
            del.setId(certificationList.get(0).getId());
            del.setValid(CommonConstant.INTEGER_ZERO);
            commonBiz.setBaseEntityModify(del, userName);
            tqCertificationPicturesMapper.updateByPrimaryKeySelective(del);
        }
        TCertificationPictures newPicture = new TCertificationPictures();
        newPicture.setFileType(fileTypeEnum.getFileType());
        newPicture.setFileTypeName(fileTypeEnum.getFileName());
        newPicture.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.VEHICLE_TIRE.getKey(), "", requestModel.getFilePath(), null));
        newPicture.setObjectType(objectType);
        newPicture.setObjectId(tqVehicleTire.getVehicleTireId());
        newPicture.setUploadUserName(userName);
        newPicture.setUploadTime(new Date());
        newPicture.setSuffix(requestModel.getFilePath().substring(requestModel.getFilePath().lastIndexOf('.')));
        commonBiz.setBaseEntityAdd(newPicture, userName);
        tqCertificationPicturesMapper.insertSelective(newPicture);
    }
}











