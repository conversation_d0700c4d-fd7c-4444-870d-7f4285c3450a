package com.logistics.tms.controller.settlestatement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/9
 */
@Data
public class StatementArchiveRequestModel {

	@ApiModelProperty(value = "对账单详情item id")
	private Long settleStatementItemId;

	@ApiModelProperty(value = "归档原因")
	private String archiveRemark;

	@ApiModelProperty(value = "归档图片")
	private List<String> archiveTicketList;

	@ApiModelProperty(value = "操作类型：1 归档，2 确认编辑")
	private Integer operateType;
}
