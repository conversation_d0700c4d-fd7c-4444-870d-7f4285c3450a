package com.logistics.tms.controller.carrierorderapplet.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2018/10/16 14:00
 */
@Data
public class CarrierOrderBillResponseModel {
    @ApiModelProperty("单据类型：（1 提货单，2 出库单，3 签收单，4 其他， 5 到达装货地凭证， 6 到达卸货地凭证）")
    private Integer ticketType;
    @ApiModelProperty("单据路径")
    private List<String> ticketPath;
}
