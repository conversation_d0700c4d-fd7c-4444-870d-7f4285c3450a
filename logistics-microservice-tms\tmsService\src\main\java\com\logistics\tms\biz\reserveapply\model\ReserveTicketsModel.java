package com.logistics.tms.biz.reserveapply.model;

import com.logistics.tms.base.enums.CertificationPicturesFileTypeEnum;
import com.logistics.tms.base.enums.CopyFileTypeEnum;
import com.yelo.tools.context.BaseContextHandler;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;

@Data
@Accessors(chain = true)
public class ReserveTicketsModel {

    private String userName;
    private Long objectId;
    private CertificationPicturesFileTypeEnum remitPicEnum;
    private CopyFileTypeEnum fileTypeEnum;
    private Collection<String> tickets;

    public String getUserName() {
        if (StringUtils.isBlank(userName)) {
            return BaseContextHandler.getUserName();
        }
        return userName;
    }
}
