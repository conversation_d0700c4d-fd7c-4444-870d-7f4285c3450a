package com.logistics.tms.api.feign.terminalreachmanagement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class GetReachManagementDetailResponseModel {

    @ApiModelProperty(value = "终端客户触达管理列表id")
    private Long reachManagementId;

    // ========= 发货数据 =========
    @ApiModelProperty(value = "运单号")
    private String carrierOrderCode;

    @ApiModelProperty(value = "上游客户")
    private String upstreamCustomer;

    @ApiModelProperty(value = "发货联系人")
    private String consignorName;
    @ApiModelProperty(value = "发货联系人")
    private String consignorMobile;

    @ApiModelProperty(value = "发货仓库")
    private String loadWarehouse;

    @ApiModelProperty(value = "发货详细地址（发货省市区+详细地址）")
    private String loadProvinceName;
    @ApiModelProperty(value = "发货详细地址（发货省市区+详细地址）")
    private String loadCityName;
    @ApiModelProperty(value = "发货详细地址（发货省市区+详细地址）")
    private String loadAreaName;
    @ApiModelProperty(value = "发货详细地址（发货省市区+详细地址）")
    private String loadDetailAddress;


    // ========= 触达现场数据 =========
    @ApiModelProperty(value = "触达人")
    private String reachDriverName;
    @ApiModelProperty(value = "触达人")
    private String reachDriverPhone;

    @ApiModelProperty(value = "触达时间")
    private Date reachTime;

    @ApiModelProperty(value = "联系人校验 0无误 1有误")
    private Integer checkReachContact;

    @ApiModelProperty(value = "联系人")
    private String reachContactor;
    @ApiModelProperty(value = "联系人")
    private String reachTelephone;

    @ApiModelProperty(value = "地址抬头")
    private String terminalHead;

    @ApiModelProperty(value = "触达地址")
    private String reachProvinceName;
    @ApiModelProperty(value = "触达地址")
    private String reachCityName;
    @ApiModelProperty(value = "触达地址")
    private String reachAreaName;
    @ApiModelProperty(value = "触达地址")
    private String reachAddressDetail;

    @ApiModelProperty(value = "地址偏离（km）")
    private Double distanceDeviation;

    @ApiModelProperty(value = "空置托盘数量")
    private Long emptyTraysAmount;

    @ApiModelProperty(value = "带料共享托盘数量")
    private Long employTraysAmount;

    @ApiModelProperty(value = "附件类型（1、托盘 2、终端门头）")
    private Map<String, List<String>> attachmentMap = new HashMap<>();

}
