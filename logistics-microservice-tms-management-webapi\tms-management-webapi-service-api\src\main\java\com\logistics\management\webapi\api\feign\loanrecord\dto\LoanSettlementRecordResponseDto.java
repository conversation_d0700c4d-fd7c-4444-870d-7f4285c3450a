package com.logistics.management.webapi.api.feign.loanrecord.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/9/30 10:24
 */
@Data
public class LoanSettlementRecordResponseDto {
    @ApiModelProperty("结算记录表ID")
    private String loanSettlementRecordId;
    @ApiModelProperty("贷款记录表ID")
    private String loanRecordsId;
    @ApiModelProperty("结算日期")
    private String settlementDate;
    @ApiModelProperty("结算金额")
    private String settlementFee;
    @ApiModelProperty("剩余还款金额")
    private String remainingRepaymentFee;
    @ApiModelProperty("总金额")
    private String totalFee;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("创建时间")
    private String createdTime;
}
