package com.logistics.tms.biz.demandorder.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2024/4/28 14:49
 */
@Data
public class BiddingOrderSelectCarrierDemandModel {

    /**
     * 需求单id
     */
    @ApiModelProperty("需求单id")
    private Long demandOrderId;

    /**
     * 竞价金额类型：1 单价，2 一口价
     */
    @ApiModelProperty("竞价金额类型：1 单价，2 一口价")
    private Integer biddingPriceType;

    /**
     * 竞价金额
     */
    @ApiModelProperty("竞价金额")
    private BigDecimal biddingPrice;
}
