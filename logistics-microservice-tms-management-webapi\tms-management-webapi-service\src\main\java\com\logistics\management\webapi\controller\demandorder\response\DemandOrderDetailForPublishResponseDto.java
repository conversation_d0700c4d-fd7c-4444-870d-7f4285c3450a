package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DemandOrderDetailForPublishResponseDto {
    @ApiModelProperty("需求单id")
    private String demandId="";
    @ApiModelProperty("发货地址")
    private String loadDetailAddress="";
    @ApiModelProperty("发货人")
    private String consignorName="";
    @ApiModelProperty("期望提货时间")
    private String expectedLoadTime="";
    @ApiModelProperty("收货地址")
    private String unloadDetailAddress="";
    @ApiModelProperty("收货人")
    private String receiverName="";
    @ApiModelProperty("期望卸货时间")
    private String expectedUnloadTime="";
    @ApiModelProperty("货物信息")
    private List<DemandOrderGoodsForPublishResponseDto> goodsResponseModel;
    @ApiModelProperty("备注")
    private String remark="";
}
