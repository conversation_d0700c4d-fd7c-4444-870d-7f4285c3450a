package com.logistics.tms.controller.freightconfig.request.address;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigAddressListRequestModel extends AbstractPageForm<CarrierFreightConfigAddressListRequestModel> {

    @ApiModelProperty(value = "运价配置Id")
    private Long freightConfigId;

    @ApiModelProperty(value = "运价状态; 0 禁用 1 启用")
    private Integer enabled;

    @ApiModelProperty(value = "发货地")
    private String loadAddress;

    @ApiModelProperty(value = "收货地")
    private String unloadAddress;

    @ApiModelProperty("选择导出ids")
    private String freightConfigAddressIds;

    private Long schemeId;
}
