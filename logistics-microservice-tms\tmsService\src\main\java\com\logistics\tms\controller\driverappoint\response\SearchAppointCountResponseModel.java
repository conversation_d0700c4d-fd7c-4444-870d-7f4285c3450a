package com.logistics.tms.controller.driverappoint.response;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SearchAppointCountResponseModel {

    @ApiModelProperty(value = "预约记录(次数)")
    private Integer appointCount;

    @ApiModelProperty(value = "下单数量(总吨数)")
    private BigDecimal appointAmountTotal;

    @ApiModelProperty(value = "申请记录列表")
    private PageInfo<SearchAppointResponseModel> modelList;
}
