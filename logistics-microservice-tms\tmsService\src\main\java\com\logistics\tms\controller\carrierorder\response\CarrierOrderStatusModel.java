package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2019/6/13 17:37
 */
@Data
public class CarrierOrderStatusModel {
    private Long carrierOrderId;
    private Long demandOrderId;
    private Integer status;
    private Integer ifCancel;
    private Integer ifEmpty;
    private Integer goodsUnit;
    private Integer dispatchFreightFeeType;
    private BigDecimal dispatchFreightFee;
    @ApiModelProperty("车主公司ID")
    private Long companyCarrierId;
}
