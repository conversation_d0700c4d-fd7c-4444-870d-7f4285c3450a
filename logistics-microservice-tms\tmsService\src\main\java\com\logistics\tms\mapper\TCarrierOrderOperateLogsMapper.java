package com.logistics.tms.mapper;

import com.logistics.tms.controller.carrierorder.response.GetCarrierOrderLogsResponseModel;
import com.logistics.tms.entity.TCarrierOrderOperateLogs;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TCarrierOrderOperateLogsMapper extends BaseMapper<TCarrierOrderOperateLogs>{

    int batchInsertSelective(@Param("list") List<TCarrierOrderOperateLogs> list);

    int batchUpdate(@Param("list") List<TCarrierOrderOperateLogs> list);

    List<GetCarrierOrderLogsResponseModel> getLogsByCarrierOrderId(@Param("carrierOrderId") Long carrierOrderId);
}