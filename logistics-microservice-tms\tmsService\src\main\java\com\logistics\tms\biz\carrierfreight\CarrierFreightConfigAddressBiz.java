package com.logistics.tms.biz.carrierfreight;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.biz.carrierfreight.model.AddressAreaExistRequestModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.freightconfig.request.address.*;
import com.logistics.tms.controller.freightconfig.request.ladder.CarrierFreightConfigLadderRequestModel;
import com.logistics.tms.controller.freightconfig.response.address.*;
import com.logistics.tms.controller.freightconfig.response.ladder.CarrierFreightConfigPriceDesignResponseModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.logistics.tms.base.constant.CommonConstant.CARRIER_FREIGHT_KEY;

/**
 * 车主运费配置-路线计价配置管理
 */
@Slf4j
@Service
public class CarrierFreightConfigAddressBiz {

    @Autowired
    private TCarrierFreightConfigMapper tCarrierFreightConfigMapper;
    @Autowired
    private TCarrierFreightConfigSchemeMapper tCarrierFreightConfigSchemeMapper;
    @Autowired
    private TCarrierFreightConfigAddressMapper tCarrierFreightConfigAddressMapper;
    @Autowired
    private TCarrierFreightConfigLadderMapper tCarrierFreightConfigLadderMapper;
    @Autowired
    private TOperateLogsMapper tOperateLogsMapper;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private CarrierFreightConfigLadderBiz carrierFreightConfigLadderBiz;
    @Autowired
    private CarrierFreightBiz carrierFreightBiz;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 路线计价配置列表
     *
     * @param requestModel 筛选条件
     * @return 路线计价配置列表
     */
    public CarrierFreightConfigAddressListResponseModel searchConfigAddressList(CarrierFreightConfigAddressListRequestModel requestModel) {
        CarrierFreightConfigAddressListResponseModel responseModel = new CarrierFreightConfigAddressListResponseModel();
        TCarrierFreightConfig tCarrierFreightConfig = tCarrierFreightConfigMapper.selectByPrimaryKey(requestModel.getFreightConfigId());
        if (tCarrierFreightConfig == null || IfValidEnum.INVALID.getKey().equals(tCarrierFreightConfig.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_FREIGHT_NOT_EXIST);
        }

        //查询运价方案
        TCarrierFreightConfigScheme tCarrierFreightConfigScheme =
                tCarrierFreightConfigSchemeMapper.selectByFreightConfigIdAndSchemetype(tCarrierFreightConfig.getId(), CarrierFreightConfigSchemeTypeEnum.ROUTE_CONFIG.getKey());
        if (tCarrierFreightConfigScheme == null) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_FREIGHT_NOT_EXIST);
        }
        requestModel.setSchemeId(tCarrierFreightConfigScheme.getId());
        requestModel.enablePaging();
        List<TCarrierFreightConfigAddress> carrierFreightAddressList = tCarrierFreightConfigAddressMapper.searchAddressRuleList(requestModel);
        PageInfo pageInfo = new PageInfo();
        if (ListUtils.isNotEmpty(carrierFreightAddressList)) {
            // 数据转换
            List<CarrierFreightConfigAddressItemResponseModel> responseModels = carrierFreightAddressList.stream()
                    .map(carrierFreightAddress -> {
                        CarrierFreightConfigAddressItemResponseModel model = MapperUtils.mapper(carrierFreightAddress, CarrierFreightConfigAddressItemResponseModel.class);
                        model.setFreightConfigAddressId(carrierFreightAddress.getId());
                        return model;
                    })
                    .collect(Collectors.toList());
            pageInfo = new PageInfo(carrierFreightAddressList);
            pageInfo.setList(responseModels);
        }
        responseModel.setFreightConfigSchemeId(tCarrierFreightConfigScheme.getId());
        responseModel.setSchemeType(tCarrierFreightConfigScheme.getSchemeType());
        responseModel.setAddressConfigPage(pageInfo);
        return responseModel;
    }

    /**
     * 路线计价配置查看
     *
     * @param requestModel
     * @return
     */
    public CarrierFreightConfigAddressDetailResponseModel configAddressDetail(CarrierFreightAddressDetailRequestModel requestModel) {
        // 校验运价细则是否存在
        TCarrierFreightConfigAddress tCarrierFreightConfigAddress = tCarrierFreightConfigAddressMapper.selectByPrimaryKey(requestModel.getFreightConfigAddressId());
        if (tCarrierFreightConfigAddress == null || IfValidEnum.INVALID.getKey().equals(tCarrierFreightConfigAddress.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.FREIGHT_ADDRESS_NOT_EXIST);
        }

        // 封装路线信息
        CarrierFreightConfigAddressDetailResponseModel responseModel = MapperUtils.mapper(tCarrierFreightConfigAddress, CarrierFreightConfigAddressDetailResponseModel.class);
        responseModel.setFreightConfigAddressId(tCarrierFreightConfigAddress.getId());

        // 查询运价规则与阶梯价格
        Map<Long, CarrierFreightConfigPriceDesignResponseModel> ladderDetail =
                carrierFreightConfigLadderBiz.getLadderDetail(CarrierFreightConfigLadderModeEnum.ROUTE_CONFIG.getKey(), Collections.singletonList(responseModel.getFreightConfigAddressId()));
        responseModel.setPriceDesign(ladderDetail.get(responseModel.getFreightConfigAddressId()));
        return responseModel;
    }

    /**
     * 启用禁用路线计价配置
     *
     * @param requestModel
     */
    @Transactional
    public void enableConfigAddress(CarrierFreightAddressEnableRequestModel requestModel) {
        // 批量操作前校验
        List<TCarrierFreightConfigAddress> freightAddressList = verifyBeforeBatchUpdateAddressRule(requestModel.getFreightConfigAddressIds(),
                true,
                requestModel.getEnabled());
        // 批量启用、禁用
        List<TCarrierFreightConfigAddress> updateFreightAddressList = new ArrayList<>();
        List<Long> addressIdList = new ArrayList<>();
        for (TCarrierFreightConfigAddress freightAddress : freightAddressList) {
            TCarrierFreightConfigAddress carrierFreightAddress = new TCarrierFreightConfigAddress();
            carrierFreightAddress.setId(freightAddress.getId());
            carrierFreightAddress.setEnabled(requestModel.getEnabled());
            commonBiz.setBaseEntityModify(carrierFreightAddress, BaseContextHandler.getUserName());

            updateFreightAddressList.add(carrierFreightAddress);
            addressIdList.add(freightAddress.getId());
        }
        tCarrierFreightConfigAddressMapper.batchUpdateSelective(updateFreightAddressList);

        // 新增日志
        this.batchInsertLogs(EnabledEnum.ENABLED.getKey().equals(requestModel.getEnabled())
                        ? OperateLogsOperateTypeEnum.CARRIER_FREIGHT_ADDRESS_RULE_ENABLE
                        : OperateLogsOperateTypeEnum.CARRIER_FREIGHT_ADDRESS_RULE_DISABLE,
                addressIdList,
                BaseContextHandler.getUserName());

    }

    /**
     * 批量修改运价地址前的校验
     *
     * @param freightAddressIds
     * @param isUpdate          是否修改操作
     * @param enabled           修改操作需要此参数
     * @return
     */
    private List<TCarrierFreightConfigAddress> verifyBeforeBatchUpdateAddressRule(List<Long> freightAddressIds, Boolean isUpdate, Integer enabled) {
        List<Long> updateFreightAddressIds = freightAddressIds.stream().distinct().collect(Collectors.toList());
        // 校验所有的车主运价规则是否存在
        List<TCarrierFreightConfigAddress> freightAddressList = tCarrierFreightConfigAddressMapper.searchFreightAddressListByIds(updateFreightAddressIds);
        if (ListUtils.isEmpty(freightAddressList)
                || updateFreightAddressIds.size() != freightAddressList.size()) {
            throw new BizException(CarrierDataExceptionEnum.FREIGHT_NOT_EXIST);
        }
        List<Long> carrierFreightIds = freightAddressList.stream().map(TCarrierFreightConfigAddress::getSchemeId).distinct().collect(Collectors.toList());
        if (carrierFreightIds.size() > 1) {
            // 多个车主下的运价规则，抛异常
            throw new BizException(CarrierDataExceptionEnum.SELECT_SAME_CARRIER_FREIGHT);
        }

        if (isUpdate) {
            // 校验启用、禁用操作
            for (TCarrierFreightConfigAddress freightAddress : freightAddressList) {
                if (freightAddress.getEnabled().equals(enabled)) {
                    throw new BizException(EnabledEnum.ENABLED.getKey().equals(enabled)
                            ? CarrierDataExceptionEnum.ONLY_DISABLED_CAN_ENABLED : CarrierDataExceptionEnum.ONLY_ENABLED_CAN_DISABLED);
                }
            }
        } else {
            // 仅支持删除禁用车主运价
            for (TCarrierFreightConfigAddress freightAddress : freightAddressList) {
                if (EnabledEnum.ENABLED.getKey().equals(freightAddress.getEnabled())) {
                    throw new BizException(CarrierDataExceptionEnum.ONLY_SUPPORT_DELETE_DISABLED_FREIGHT);
                }
            }
        }
        return freightAddressList;
    }


    /**
     * 批量添加操作日志
     *
     * @param type
     * @param objectIds
     * @param userName
     */
    public void batchInsertLogs(OperateLogsOperateTypeEnum type, List<Long> objectIds, String userName) {
        List<TOperateLogs> newLogs = new ArrayList<>();
        for (Long objectId : objectIds) {
            TOperateLogs newLog = new TOperateLogs();
            newLog.setObjectId(objectId);
            newLog.setObjectType(type.getObjectType().getKey());
            newLog.setOperateType(type.getOperateType());
            newLog.setOperateContents(type.getOperateContents());
            newLog.setOperateTime(new Date());
            newLog.setOperateUserName(userName);
            commonBiz.setBaseEntityAdd(newLog, userName);
            newLogs.add(newLog);
        }
        tOperateLogsMapper.batchInsert(newLogs);
    }

    /**
     * 删除路线计价配置
     *
     * @param requestModel
     */
    @Transactional
    public void deleteConfigAddress(CarrierFreightAddressDeleteRequestModel requestModel) {
        // 批量操作前校验
        List<TCarrierFreightConfigAddress> freightAddressList = verifyBeforeBatchUpdateAddressRule(requestModel.getFreightConfigAddressIds(),
                false,
                null);

        String userName = BaseContextHandler.getUserName();
        // 批量删除
        List<TCarrierFreightConfigAddress> updateFreightAddressList = new ArrayList<>();
        List<Long> addressIdList = new ArrayList<>();
        for (TCarrierFreightConfigAddress freightAddress : freightAddressList) {
            addressIdList.add(freightAddress.getId());

            TCarrierFreightConfigAddress carrierFreightAddress = new TCarrierFreightConfigAddress();
            carrierFreightAddress.setId(freightAddress.getId());
            carrierFreightAddress.setValid(IfValidEnum.INVALID.getKey());
            commonBiz.setBaseEntityModify(carrierFreightAddress, userName);

            updateFreightAddressList.add(carrierFreightAddress);
        }
        tCarrierFreightConfigAddressMapper.batchUpdateSelective(updateFreightAddressList);

        // 删除运价规则和价格表
        AsyncProcessQueue.execute(() -> {
            batchDeleteLadder(addressIdList, userName);
        });

        // 新增日志
        this.batchInsertLogs(OperateLogsOperateTypeEnum.CARRIER_FREIGHT_ADDRESS_RULE_DELETE, addressIdList, userName);
    }

    /**
     * 批量删除原价规则价格
     *
     * @param addressRuleIds
     */
    private void batchDeleteLadder(List<Long> addressRuleIds, String userName) {
        List<TCarrierFreightConfigLadder> ladderList = tCarrierFreightConfigLadderMapper.selectConfigLadderByLadderModeAndModeIds(CarrierFreightConfigLadderModeEnum.ROUTE_CONFIG.getKey(), addressRuleIds);
        if (ListUtils.isEmpty(ladderList)) {
            return;
        }
        List<TCarrierFreightConfigLadder> deleteRulePriceList = new ArrayList<>();
        for (TCarrierFreightConfigLadder rulePrice : ladderList) {
            TCarrierFreightConfigLadder ladder = new TCarrierFreightConfigLadder();
            ladder.setId(rulePrice.getId());
            ladder.setValid(IfValidEnum.INVALID.getKey());
            commonBiz.setBaseEntityModify(ladder, userName);
            deleteRulePriceList.add(ladder);
        }
        tCarrierFreightConfigLadderMapper.batchUpdateSelective(deleteRulePriceList);
    }

    /**
     * 新增路线计价配置
     *
     * @param requestModel
     */
    @Transactional
    public void addAddressConfig(CarrierFreightConfigAddressAddRequestModel requestModel) {
        long start = System.currentTimeMillis();

        StringBuilder redisKey = new StringBuilder(CARRIER_FREIGHT_KEY)
                .append(requestModel.getFreightConfigSchemeId()).append("_")
                .append(requestModel.getFromProvinceId()).append("_")
                .append(requestModel.getFromCityId()).append("_")
                .append(requestModel.getToProvinceId()).append("_")
                .append(requestModel.getToCityId());
        String distributedLock = commonBiz.getDistributedLock(redisKey.toString());
        try {
            // 保存车主运价规则前校验
            verifyBeforeSaveAddressRule(requestModel);
            log.info("step 1 耗时：{}ms", (System.currentTimeMillis() - start));

            // 构建并批量保存车主运价地址
            List<TCarrierFreightConfigAddress> tCarrierFreightConfigAddresses = buildAndAddAddress(requestModel);
            tCarrierFreightConfigAddressMapper.batchOneSqlInsert(tCarrierFreightConfigAddresses);
            //取出数据库id
            List<Long> addressIdList = tCarrierFreightConfigAddresses.stream().map(TCarrierFreightConfigAddress::getId).collect(Collectors.toList());
            log.info("step 2 耗时：{}ms", (System.currentTimeMillis() - start));

            // 保存运价规则
            List<TCarrierFreightConfigLadder> tCarrierFreightConfigLadderList = buildAndAddAddressRule(requestModel.getLadderConfigList(), addressIdList);
            tCarrierFreightConfigLadderMapper.batchInsert(tCarrierFreightConfigLadderList);
            log.info("step 3 耗时：{}ms", (System.currentTimeMillis() - start));

            // 异步新增日志
            String userName = BaseContextHandler.getUserName();
            AsyncProcessQueue.execute(() -> this.batchInsertLogs(OperateLogsOperateTypeEnum.CARRIER_FREIGHT_ADDRESS_RULE_ADD, addressIdList, userName));
        } finally {
            commonBiz.removeDistributedLock(redisKey.toString(), distributedLock);
        }
    }

    /**
     * 保存车主运价规则前校验
     *
     * @param requestModel
     */
    private void verifyBeforeSaveAddressRule(CarrierFreightConfigAddressAddRequestModel requestModel) {
        // 校验车主价格方案是否存在
        TCarrierFreightConfigScheme tCarrierFreightConfigScheme = tCarrierFreightConfigSchemeMapper.selectByPrimaryKey(requestModel.getFreightConfigSchemeId());
        if (tCarrierFreightConfigScheme == null || IfValidEnum.INVALID.getKey().equals(tCarrierFreightConfigScheme.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_FREIGHT_NOT_EXIST);
        }

        // 发货区重复性校验
        List<AddressAreaExistRequestModel> areaList = new ArrayList<>();
        for (CarrierFreightConfigAddressAddRequestModel.FromArea fromArea : requestModel.getFromAreaId()) {
            for (CarrierFreightConfigAddressAddRequestModel.FromArea toArea : requestModel.getToAreaId()) {
                AddressAreaExistRequestModel areaExistRequestModel = new AddressAreaExistRequestModel();
                areaExistRequestModel.setFromAreaId(fromArea.getFromAreaId());
                areaExistRequestModel.setToAreaId(toArea.getFromAreaId());

                areaList.add(areaExistRequestModel);
            }
        }
        List<TCarrierFreightConfigAddress> freightAddressList = tCarrierFreightConfigAddressMapper.searchAreaRouteExist(requestModel.getFreightConfigSchemeId(), areaList);
        if (ListUtils.isNotEmpty(freightAddressList)) {
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ALREADY_EXIST);
        }
    }

    /**
     * 构建车主运价地址
     *
     * @param requestModel
     * @return 车主运价地址id
     */
    private List<TCarrierFreightConfigAddress> buildAndAddAddress(CarrierFreightConfigAddressAddRequestModel requestModel) {
        List<TCarrierFreightConfigAddress> freightAddressList = new ArrayList<>();
        // 车主运价地址笛卡尔积。发货区数量 * 收货区数量
        for (CarrierFreightConfigAddressAddRequestModel.FromArea fromArea : requestModel.getFromAreaId()) {
            for (CarrierFreightConfigAddressAddRequestModel.FromArea toArea : requestModel.getToAreaId()) {
                TCarrierFreightConfigAddress carrierFreightAddress = new TCarrierFreightConfigAddress();
                carrierFreightAddress.setSchemeId(requestModel.getFreightConfigSchemeId());
                carrierFreightAddress.setFromProvinceId(requestModel.getFromProvinceId());
                carrierFreightAddress.setFromProvinceName(requestModel.getFromProvinceName());
                carrierFreightAddress.setFromCityId(requestModel.getFromCityId());
                carrierFreightAddress.setFromCityName(requestModel.getFromCityName());
                carrierFreightAddress.setFromAreaId(fromArea.getFromAreaId());
                carrierFreightAddress.setFromAreaName(fromArea.getFromAreaName());
                carrierFreightAddress.setToProvinceId(requestModel.getToProvinceId());
                carrierFreightAddress.setToProvinceName(requestModel.getToProvinceName());
                carrierFreightAddress.setToCityId(requestModel.getToCityId());
                carrierFreightAddress.setToCityName(requestModel.getToCityName());
                carrierFreightAddress.setToAreaId(toArea.getFromAreaId());
                carrierFreightAddress.setToAreaName(toArea.getFromAreaName());
                carrierFreightAddress.setEnabled(EnabledEnum.DISABLED.getKey());
                commonBiz.setBaseEntityAdd(carrierFreightAddress, BaseContextHandler.getUserName());
                freightAddressList.add(carrierFreightAddress);
            }
        }
        return freightAddressList;
    }

    /**
     * 保存运价规则
     *
     * @param addressIdList
     */
    private List<TCarrierFreightConfigLadder> buildAndAddAddressRule(List<CarrierFreightConfigLadderRequestModel> addressRuleList, List<Long> addressIdList) {
        List<TCarrierFreightConfigLadder> tCarrierFreightConfigLadderList = new ArrayList<>();
        for (Long addressId : addressIdList) {
            for (CarrierFreightConfigLadderRequestModel addressRule : addressRuleList) {
                // 构建运价阶梯配置
                TCarrierFreightConfigLadder tCarrierFreightConfigLadder = setCarrierFreightConfigLadder(addressId, addressRule);
                tCarrierFreightConfigLadderList.add(tCarrierFreightConfigLadder);
            }
        }
        return tCarrierFreightConfigLadderList;
    }

    private TCarrierFreightConfigLadder setCarrierFreightConfigLadder(Long addressId, CarrierFreightConfigLadderRequestModel addressRule) {
        TCarrierFreightConfigLadder tCarrierFreightConfigLadder = new TCarrierFreightConfigLadder();
        tCarrierFreightConfigLadder.setModeId(addressId);
        tCarrierFreightConfigLadder.setLadderLevel(addressRule.getLadderLevel());
        tCarrierFreightConfigLadder.setPriceMode(addressRule.getPriceMode());
        tCarrierFreightConfigLadder.setLadderMode(CarrierFreightConfigLadderModeEnum.ROUTE_CONFIG.getKey());
        tCarrierFreightConfigLadder.setLadderType(addressRule.getLadderType());
        tCarrierFreightConfigLadder.setLadderFrom(addressRule.getLadderFrom());
        tCarrierFreightConfigLadder.setLadderTo(addressRule.getLadderTo());
        tCarrierFreightConfigLadder.setLadderUnit(addressRule.getLadderUnit());
        tCarrierFreightConfigLadder.setUnitPrice(addressRule.getUnitPrice());
        return tCarrierFreightConfigLadder;
    }

    /**
     * 编辑路线计价配置
     *
     * @param requestModel
     */
    @Transactional
    public void editAddressConfig(CarrierFreightAddressEditEnableRequestModel requestModel) {

        // 校验车主价格方案是否存在
        TCarrierFreightConfigAddress freightAddress = tCarrierFreightConfigAddressMapper.selectByPrimaryKey(requestModel.getFreightConfigAddressId());
        if (freightAddress == null || IfValidEnum.INVALID.getKey().equals(freightAddress.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_FREIGHT_NOT_EXIST);
        }

        //查询阶梯数据
        Map<Long, TCarrierFreightConfigLadder> carrierFreightConfigLadderMap = tCarrierFreightConfigLadderMapper
                .selectConfigLadderByLadderModeAndModeIds(CarrierFreightConfigLadderModeEnum.ROUTE_CONFIG.getKey(), Collections.singletonList(freightAddress.getId()))
                .stream().collect(Collectors.toMap(TCarrierFreightConfigLadder::getId, Function.identity()));

        // 校验修改的数据是否存在
        for (CarrierFreightConfigLadderRequestModel addressRule : requestModel.getLadderConfigList()) {
            if (addressRule.getFreightConfigLadderId() != null
                    && !carrierFreightConfigLadderMap.containsKey(addressRule.getFreightConfigLadderId())) {
                throw new BizException(CarrierDataExceptionEnum.FREIGHT_ADDRESS_RULE_NOT_EXIST);
            }
        }

        // 根据请求参数分组 删除、保存、修改
        List<TCarrierFreightConfigLadder> updateLadderList = new ArrayList<>();
        // 运价规则存在，新增了阶梯，这时候已经知道了运价规则id，直接保存阶梯即可
        List<TCarrierFreightConfigLadder> insertLadderList = new ArrayList<>();
        // 构建运价地址规则
        for (CarrierFreightConfigLadderRequestModel addressRule : requestModel.getLadderConfigList()) {

            //数据库中包含一样的阶梯
            if (carrierFreightConfigLadderMap.get(addressRule.getFreightConfigLadderId()) != null) {
                carrierFreightConfigLadderMap.remove(addressRule.getFreightConfigLadderId());
            }

            TCarrierFreightConfigLadder tCarrierFreightConfigLadder = setCarrierFreightConfigLadder(freightAddress.getId(), addressRule);

            // 判断运价地址规则id是否为空，不为空将进行更新。并且删除查询DB的List中的数据
            if (addressRule.getFreightConfigLadderId() == null) {
                commonBiz.setBaseEntityAdd(tCarrierFreightConfigLadder, BaseContextHandler.getUserName());
                insertLadderList.add(tCarrierFreightConfigLadder);
            } else {
                tCarrierFreightConfigLadder.setId(addressRule.getFreightConfigLadderId());
                commonBiz.setBaseEntityModify(tCarrierFreightConfigLadder, BaseContextHandler.getUserName());
                updateLadderList.add(tCarrierFreightConfigLadder);
            }
        }

        //删除的阶梯价格
        Collection<TCarrierFreightConfigLadder> deleteLadderTempList = carrierFreightConfigLadderMap.values();
        for (TCarrierFreightConfigLadder tCarrierFreightConfigLadder : deleteLadderTempList) {
            tCarrierFreightConfigLadder.setValid(IfValidEnum.INVALID.getKey());
            commonBiz.setBaseEntityModify(tCarrierFreightConfigLadder, BaseContextHandler.getUserName());
            updateLadderList.add(tCarrierFreightConfigLadder);
        }

        // 修改车主运价地址表修改时间
        TCarrierFreightConfigAddress updateFreightAddress = new TCarrierFreightConfigAddress();
        updateFreightAddress.setId(freightAddress.getId());
        commonBiz.setBaseEntityModify(updateFreightAddress, BaseContextHandler.getUserName());
        tCarrierFreightConfigAddressMapper.updateByPrimaryKeySelective(updateFreightAddress);

        //插入或更新阶梯数据
        if (ListUtils.isNotEmpty(updateLadderList)) {
            tCarrierFreightConfigLadderMapper.batchUpdateSelective(updateLadderList);
        }
        if (ListUtils.isNotEmpty(insertLadderList)) {
            tCarrierFreightConfigLadderMapper.batchInsert(insertLadderList);
        }

        // 保存日志
        tOperateLogsMapper.insertSelective(commonBiz.insertLogs(OperateLogsOperateTypeEnum.CARRIER_FREIGHT_ADDRESS_RULE_MODIFY, freightAddress.getId(), null));
    }

    /**
     * 查询车主运价信息
     *
     * @param requestModel
     * @return
     */
    public SearchCarrierFreightConfigResponseModel getCarrierFreight(SearchCarrierFreightConfigRequestModel requestModel) {
        TCarrierFreight carrierFreightByScheme = carrierFreightBiz.getCarrierFreightByScheme(requestModel.getFreightConfigSchemeId());

        // 查询车主信息
        List<FuzzySearchCompanyCarrierResponseModel> companyCarrierList = tCompanyCarrierMapper.selectCompanyCarrierInfoByIds(Collections.singletonList(carrierFreightByScheme.getCompanyCarrierId()));
        if (ListUtils.isEmpty(companyCarrierList)) {
            throw new BizException(CarrierDataExceptionEnum.NO_COMPANY_CARRIER);
        }

        FuzzySearchCompanyCarrierResponseModel companyCarrier = companyCarrierList.get(CommonConstant.INTEGER_ZERO);
        SearchCarrierFreightConfigResponseModel responseModel = new SearchCarrierFreightConfigResponseModel();
        responseModel.setCarrierFreightId(carrierFreightByScheme.getId());
        responseModel.setEnabled(carrierFreightByScheme.getEnabled());
        responseModel.setCarrierContactPhone(companyCarrier.getContactPhone());
        responseModel.setCarrierContactName(companyCarrier.getContactName());
        responseModel.setCompanyCarrierName(companyCarrier.getCompanyName());
        responseModel.setCompanyCarrierType(companyCarrier.getCompanyType());
        return responseModel;
    }

    /**
     * 查询车主运价信息
     *
     * @param requestModel
     * @return
     */
    public List<CarrierFreightConfigAddressLogsResponseModel> getAddressRuleLogs(CarrierFreightAddressDetailRequestModel requestModel) {
        // 校验车主价格方案是否存在
        TCarrierFreightConfigAddress freightAddress = tCarrierFreightConfigAddressMapper.selectByPrimaryKey(requestModel.getFreightConfigAddressId());
        if (freightAddress == null || IfValidEnum.INVALID.getKey().equals(freightAddress.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_FREIGHT_NOT_EXIST);
        }

        return tOperateLogsMapper.selectLogsByCondition(OperateLogsOperateTypeEnum.CARRIER_FREIGHT_ADDRESS_RULE_ADD.getObjectType().getKey(),
                        requestModel.getFreightConfigAddressId(),
                        OperateLogsOperateTypeEnum.CARRIER_FREIGHT_ADDRESS_RULE_ADD.getOperateType())
                .stream()
                .map(log -> MapperUtils.mapper(log, CarrierFreightConfigAddressLogsResponseModel.class))
                .collect(Collectors.toList());
    }


    /**
     * 根据方案id查询地址
     * @param freightConfigSchemeId 方案id
     * @return 运价地址
     */
    public List<TCarrierFreightConfigAddress> searchAddressByConfigSchemeId(Long freightConfigSchemeId) {
        return tCarrierFreightConfigAddressMapper.searchAddressByConfigSchemeId(freightConfigSchemeId);
    }

    /**
     * 根据方案id删除地址
     * @param schemeId 方案id
     * @param userName 操作人
     */
    public void deleteBySchemeId(Long schemeId,String userName) {
        tCarrierFreightConfigAddressMapper.deleteBySchemeId(schemeId,userName);
    }


}
