package com.logistics.management.webapi.controller.freightconfig.response.ladder;

import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

@Data
public class CarrierFreightConfigLadderResponseDto {

    @ApiModelProperty(value = "阶梯ID")
    private String freightConfigLadderId = "";

    @ApiModelProperty(value = "阶梯层级; 0 为固定单价/总价")
    private String ladderLevel = "";

    @ApiModelProperty(value = "阶梯类型; 1: KM(公里); 2: 数量;")
    private String ladderType = "";

    @ApiModelProperty(value = "阶梯起始")
    private String ladderFrom = "";

    @ApiModelProperty(value = "阶梯终止（包含等于）")
    private String ladderTo = "";

    @ApiModelProperty(value = "单位; 1: 件; 2: 吨; 4: 块")
    private String ladderUnit = "";

    @ApiModelProperty(value = "单位文本")
    private String ladderUnitLabel = "";

    @ApiModelProperty(value = "价格模式; 1: 单价; 2: 总价;")
    private String priceMode = "";

    @ApiModelProperty(value = "价格(元)")
    private String unitPrice = "";
    
    @ApiModelProperty(value = "多级阶梯")
    private List<CarrierFreightConfigLadderResponseDto> ladderConfigList;

    public void setLadderUnit(String ladderUnit) {
        this.ladderUnit = ladderUnit;
        if (Objects.nonNull(ladderUnit)) {
            this.ladderUnitLabel = GoodsUnitEnum.getEnum(Integer.parseInt(ladderUnit)).getPriceUnit();
        }
    }
}
