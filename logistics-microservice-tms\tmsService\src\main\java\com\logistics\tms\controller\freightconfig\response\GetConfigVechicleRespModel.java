package com.logistics.tms.controller.freightconfig.response;

import com.logistics.tms.controller.freightconfig.request.ConfigVehicleItemModel;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


@Data
public class GetConfigVechicleRespModel {


//    /**
//     * 主键id
//     */
//    private Long shippingFreightId ;


    /**
     * 发货地
     */
    private String loadAddress = "";

    /**
     * 收货地
     */
    private String unloadAddress = "";



    private List<GetConfigVechicleDetailRespModel> detailRespDtos = new ArrayList<>();

}
