package com.logistics.management.webapi.controller.invoicingmanagement.packaging.mapping;

import com.logistics.management.webapi.client.invoicingmanagement.response.GetInvoicePicturesResponseModel;
import com.logistics.management.webapi.controller.invoicingmanagement.packaging.response.GetInvoicePicturesResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

import java.util.Map;

/**
 * @author: wjf
 * @date: 2024/3/21 14:27
 */
public class GetInvoicePicturesMapping extends MapperMapping<GetInvoicePicturesResponseModel, GetInvoicePicturesResponseDto> {

    private final String imagePrefix;
    private final Map<String, String> imageMap;
    public GetInvoicePicturesMapping(String imagePrefix , Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        GetInvoicePicturesResponseModel source = getSource();
        GetInvoicePicturesResponseDto destination = getDestination();

        if (StringUtils.isNotBlank(source.getInvoicePicture())){
            destination.setInvoicePictureUrl(imagePrefix + imageMap.get(source.getInvoicePicture()));
        }
    }
}
