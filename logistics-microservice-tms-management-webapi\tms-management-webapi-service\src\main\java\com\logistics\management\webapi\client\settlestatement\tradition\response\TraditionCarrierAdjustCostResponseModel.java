package com.logistics.management.webapi.client.settlestatement.tradition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class TraditionCarrierAdjustCostResponseModel {

    @ApiModelProperty("对账单id")
    private Long settleStatementId;

    @ApiModelProperty("车主运费")
    private BigDecimal carrierFreight;

    @ApiModelProperty("运费费点")
    private BigDecimal freightTaxPoint;

    @ApiModelProperty("调整费用")
    private BigDecimal adjustCost;
}
