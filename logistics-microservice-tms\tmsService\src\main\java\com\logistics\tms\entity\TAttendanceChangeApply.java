package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/11/10
*/
@Data
public class TAttendanceChangeApply extends BaseEntity {
    /**
    * 考勤记录表ID
    */
    @ApiModelProperty("考勤记录表ID")
    private Long attendanceRecordId;

    /**
    * 变更类型: 1 上班，2 下班
    */
    @ApiModelProperty("变更类型: 1 上班，2 下班")
    private Integer changeType;

    /**
    * 要变更的打卡时间
    */
    @ApiModelProperty("要变更的打卡时间")
    private Date changePunchTime;

    /**
    * 变更原因
    */
    @ApiModelProperty("变更原因")
    private String changeReason;

    /**
    * 审核状态: 0 待审核，1 已审核，2 已驳回，3 已撤销
    */
    @ApiModelProperty("审核状态: 0 待审核，1 已审核，2 已驳回，3 已撤销")
    private Integer auditStatus;

    /**
    * 审核人
    */
    @ApiModelProperty("审核人")
    private String auditorName;

    /**
    * 审核时间
    */
    @ApiModelProperty("审核时间")
    private Date auditTime;

    /**
    * 备注(审核,驳回,撤销)
    */
    @ApiModelProperty("备注(审核,驳回,撤销)")
    private String remark;

    /**
    * 是否有效：1 有效，0 无效
    */
    @ApiModelProperty("是否有效：1 有效，0 无效")
    private Integer valid;
}