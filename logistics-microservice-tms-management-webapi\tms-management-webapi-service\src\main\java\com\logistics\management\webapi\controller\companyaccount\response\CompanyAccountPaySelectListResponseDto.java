package com.logistics.management.webapi.controller.companyaccount.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CompanyAccountPaySelectListResponseDto {

    @ApiModelProperty(value = "银行账号")
    private String bankAccount = "";

    @ApiModelProperty(value = "银行名称")
    private String bankAccountName = "";

    @ApiModelProperty(value = "支行名称")
    private String braBankName = "";
}
