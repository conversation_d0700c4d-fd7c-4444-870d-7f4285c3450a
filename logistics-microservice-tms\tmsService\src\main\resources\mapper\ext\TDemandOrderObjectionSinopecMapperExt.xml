<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandOrderObjectionSinopecMapper" >
  <select id="searchSinopecObjection" resultType="com.logistics.tms.api.feign.demandorderobjectionsinopec.model.SearchDemandOrderObjectionSinopecResponseModel">
    select
    tdo.id as demandId,
    tdo.demand_order_code as demandOrderCode,
    tdo.customer_order_code as customerOrderCode,
    tdo.sinopec_order_no as sinopecOrderNo,
    tdo.company_entrust_name as companyEntrustName,

    tdoos.id as demandOrderObjectionId,
    tdoos.audit_status as auditStatus,
    tdoos.dispatcher_name as dispatcherName,
    tdoos.dispatcher_phone as dispatcher<PERSON>hone,
    tdoos.remark as remark,
    tdoos.auditor_name as auditor<PERSON>ame,
    tdoos.audit_time as auditTime,
    tdoos.audit_objection_type as auditObjectionType,
    tdoos.audit_remark as auditRemark
    from t_demand_order_objection_sinopec tdoos
    left join t_demand_order tdo on tdo.id = tdoos.demand_order_id and tdo.valid = 1
    where tdoos.valid = 1
    <if test="params.demandOrderCode != null and params.demandOrderCode != ''">
      and instr(tdo.demand_order_code, #{params.demandOrderCode,jdbcType=VARCHAR})
    </if>
    <if test="params.customerOrderCode != null and params.customerOrderCode != ''">
      and (instr(tdo.customer_order_code, #{params.customerOrderCode,jdbcType=VARCHAR}) or instr(tdo.sinopec_order_no, #{params.customerOrderCode,jdbcType=VARCHAR}))
    </if>
    <if test="params.auditObjectionType != null">
      and tdoos.audit_objection_type = #{params.auditObjectionType,jdbcType=INTEGER}
    </if>
    <if test="params.auditTimeStart!=null and params.auditTimeStart!=''">
      and tdoos.audit_time &gt;= DATE_FORMAT(#{params.auditTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %k:%i:%S')
    </if>
    <if test="params.auditTimeEnd!=null and params.auditTimeEnd!=''">
      and tdoos.audit_time &lt;= DATE_FORMAT(#{params.auditTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
    </if>
    order by tdoos.last_modified_time desc, tdoos.id desc
  </select>

  <select id="getSinopecObjectionDetail" resultType="com.logistics.tms.api.feign.demandorderobjectionsinopec.model.GetSinopecObjectionDetailResponseModel">
    select
    tdo.id as demandId,
    tdo.demand_order_code as demandOrderCode,
    tdo.customer_order_code as customerOrderCode,
    tdo.sinopec_order_no as sinopecOrderNo,
    tdo.company_entrust_name as companyEntrustName,

    tdoos.id as demandOrderObjectionId,
    tdoos.dispatcher_name as dispatcherName,
    tdoos.dispatcher_phone as dispatcherPhone,
    tdoos.objection_type as objectionType,
    tdoos.audit_objection_type as auditObjectionType,
    tdoos.audit_status as auditStatus,
    tdoos.audit_remark as auditRemark
    from t_demand_order_objection_sinopec tdoos
    left join t_demand_order tdo on tdo.id = tdoos.demand_order_id and tdo.valid = 1
    where tdoos.valid = 1
    and tdoos.id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByDemandOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_demand_order_objection_sinopec
    where valid = 1
    and audit_status = 0
    and demand_order_id = #{demandOrderId,jdbcType=BIGINT}
  </select>
</mapper>