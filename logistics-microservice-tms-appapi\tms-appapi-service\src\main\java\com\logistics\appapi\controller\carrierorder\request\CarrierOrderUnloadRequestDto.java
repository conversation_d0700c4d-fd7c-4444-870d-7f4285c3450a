package com.logistics.appapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: wjf
 * @date: 2023/3/23 13:28
 */
@Data
public class CarrierOrderUnloadRequestDto {
    @ApiModelProperty(value = "运单Id",required = true)
    @NotBlank(message = "运单ID不能为空")
    private String carrierOrderId;

    @ApiModelProperty(value = "卸货数量",required = true)
    @NotEmpty(message = "货物信息不能为空")
    @Valid
    private List<CarrierOrderGoodsLoadUnloadRequestDto> goodsList;

    @ApiModelProperty(value = "卸货单据")
    private List<CarrierOrderTicketRequestDto> tickets;

    @ApiModelProperty(value = "货物单位：1 件，2 吨", required = true)
    @NotBlank(message = "货物单位不能为空")
    private String goodsUnit;
}
