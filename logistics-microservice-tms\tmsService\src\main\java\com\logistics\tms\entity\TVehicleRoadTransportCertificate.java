package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleRoadTransportCertificate extends BaseEntity {
    /**
    * 车辆基础信息ID
    */
    @ApiModelProperty("车辆基础信息ID")
    private Long vehicleId;

    /**
    * 发证签
    */
    @ApiModelProperty("发证签")
    private String certificationSign;

    /**
    * 经营许可证号
    */
    @ApiModelProperty("经营许可证号")
    private String businessLicenseNumber;

    /**
    * 经济类型
    */
    @ApiModelProperty("经济类型")
    private String economicType;

    /**
    * 吨位
    */
    @ApiModelProperty("吨位")
    private BigDecimal transportTonnage;

    /**
    * 经营类型
    */
    @ApiModelProperty("经营类型")
    private String businessScope;

    /**
    * 发证部门
    */
    @ApiModelProperty("发证部门")
    private String certificationDepartment;

    /**
    * 发证日期
    */
    @ApiModelProperty("发证日期")
    private Date issueDate;

    /**
    * 初领日期
    */
    @ApiModelProperty("初领日期")
    private Date obtainDate;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}