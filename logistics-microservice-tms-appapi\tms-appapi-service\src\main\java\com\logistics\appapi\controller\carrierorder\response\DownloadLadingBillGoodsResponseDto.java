package com.logistics.appapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: wjf
 * @date: 2018/9/30 11:48
 */
@Data
public class DownloadLadingBillGoodsResponseDto implements Serializable {

    @ApiModelProperty("货物id")
    private String goodsId = "";

    @ApiModelProperty("品名")
    private String goodsName = "";

    @ApiModelProperty("大类名")
    private String categoryName = "";

    @ApiModelProperty("规格")
    private String goodsSize = "";

    @ApiModelProperty("体积")
    private String volume = "";

    @ApiModelProperty("配送数量")
    private String expectAmount = "";

    @ApiModelProperty("实提数量")
    private String loadAmount = "";

    @ApiModelProperty("实卸数量")
    private String unloadAmount = "";

    //提货差异
    private String loadDiffAmount;

    //卸货差异
    private String unloadDiffAmount;
}
