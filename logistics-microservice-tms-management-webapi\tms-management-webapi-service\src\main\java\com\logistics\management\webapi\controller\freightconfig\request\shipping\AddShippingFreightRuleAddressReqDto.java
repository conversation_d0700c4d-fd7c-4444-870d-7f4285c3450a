package com.logistics.management.webapi.controller.freightconfig.request.shipping;

import lombok.Data;


@Data
public class AddShippingFreightRuleAddressReqDto {


    /**
     * 发货省
     */
    private String fromProvinceId;


    /**
     * 收货省name
     */
    private String fromProvinceName;

    /**
     * 收货市id
     */
    private String fromCityId;

    /**
     * 收货市name
     */
    private String fromCityName;


    /**
     * 收货区id
     */
    private String fromAreaId;


    /**
     * 收货区name
     */
    private String fromAreaName;

    /**
     * 运距
     */
    private String carrierDistance;


}
