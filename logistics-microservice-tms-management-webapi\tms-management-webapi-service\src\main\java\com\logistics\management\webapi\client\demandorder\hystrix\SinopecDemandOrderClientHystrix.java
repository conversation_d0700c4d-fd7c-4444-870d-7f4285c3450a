package com.logistics.management.webapi.client.demandorder.hystrix;

import com.logistics.management.webapi.client.demandorder.SinopecDemandOrderClient;
import com.logistics.management.webapi.client.demandorder.request.*;
import com.logistics.management.webapi.client.demandorder.response.BatchPublishSinopecResponseModel;
import com.logistics.management.webapi.client.demandorder.response.PublishSinopecResponseModel;
import com.logistics.management.webapi.client.demandorder.response.SinopecReportAbnormalDetailResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/3/27 16:08
 */
@Component
public class SinopecDemandOrderClientHystrix implements SinopecDemandOrderClient {
    @Override
    public Result<Boolean> cancelSinopecDemandOrder(SinopecDemandOrderCancelRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PublishSinopecResponseModel> publishSinopecDetail(PublishSinopecDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<BatchPublishSinopecResponseModel> batchPublishSinopecDetail(BatchPublishSinopecDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> batchPublishSinopecDemandOrder(BatchPublishSinopecDemandRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> publishSinopecDemandOrder(PublishSinopecDemandRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SinopecReportAbnormalDetailResponseModel> sinopecReportAbnormalDetail(SinopecReportAbnormalDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> saveSinopecReportAbnormal(SaveSinopecReportAbnormalRequestModel requestModel) {
        return Result.timeout();
    }
}
