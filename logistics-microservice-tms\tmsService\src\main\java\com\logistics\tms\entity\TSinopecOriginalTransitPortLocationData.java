package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/12/14
*/
@Data
public class TSinopecOriginalTransitPortLocationData extends BaseEntity {
    /**
    * 中石化委托单原始数据表ID
    */
    @ApiModelProperty("中石化委托单原始数据表ID")
    private Long sinopecOriginalDataId;

    /**
    * 位置类型(非必填)
    */
    @ApiModelProperty("位置类型(非必填)")
    private Integer locationType;

    /**
    * 位置编码(非必填)
    */
    @ApiModelProperty("位置编码(非必填)")
    private String locationCode;

    /**
    * 位置名称(非必填)
    */
    @ApiModelProperty("位置名称(非必填)")
    private String locationName;

    /**
    * 省份编码(非必填)
    */
    @ApiModelProperty("省份编码(非必填)")
    private String provinceCode;

    /**
    * 省份名称(非必填)
    */
    @ApiModelProperty("省份名称(非必填)")
    private String provinceName;

    /**
    * 地级市编码(非必填)
    */
    @ApiModelProperty("地级市编码(非必填)")
    private String cityCode;

    /**
    * 地级名称(非必填)
    */
    @ApiModelProperty("地级名称(非必填)")
    private String cityName;

    /**
    * 区县编码(非必填)
    */
    @ApiModelProperty("区县编码(非必填)")
    private String countyCode;

    /**
    * 区县名称(非必填)
    */
    @ApiModelProperty("区县名称(非必填)")
    private String countyName;

    /**
    * 街道编码(非必填)
    */
    @ApiModelProperty("街道编码(非必填)")
    private String townCode;

    /**
    * 街道名称【可并入详细地址展示】
    */
    @ApiModelProperty("街道名称【可并入详细地址展示】")
    private String townName;

    /**
    * 详细地址(非必填)
    */
    @ApiModelProperty("详细地址(非必填)")
    private String detailAddress;

    /**
    * 纬度(非必填)
    */
    @ApiModelProperty("纬度(非必填)")
    private String latitude;

    /**
    * 经度(非必填)
    */
    @ApiModelProperty("经度(非必填)")
    private String longitude;
}