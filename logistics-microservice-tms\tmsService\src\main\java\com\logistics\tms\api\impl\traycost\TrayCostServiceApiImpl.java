package com.logistics.tms.api.impl.traycost;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.traycost.TrayCostServiceApi;
import com.logistics.tms.api.feign.traycost.model.*;
import com.logistics.tms.biz.traycost.TrayCostBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/20 19:21
 */
@RestController
public class TrayCostServiceApiImpl implements TrayCostServiceApi {

    @Autowired
    private TrayCostBiz trayCostBiz;

    /**
     * 托盘费用列表
     * @return
     */
    @Override
    public Result<List<SearchTrayCostListResponseModel>> searchTrayCostList() {
        return Result.success(trayCostBiz.searchTrayCostList());
    }

    /**
     * 托盘费用详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<TrayCostDetailResponseModel> getDetail(@RequestBody TrayCostIdRequestModel requestModel) {
        return Result.success(trayCostBiz.getDetail(requestModel));
    }

    /**
     * 修改费用
     * @param requestModel
     * @return
     */
    @Override
    public Result modifyPrice(@RequestBody ModifyPriceRequestModel requestModel) {
        trayCostBiz.modifyPrice(requestModel);
        return Result.success(true);
    }

    /**
     * 费用记录列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<SearchCostRecordsListResponseModel>> searchCostRecordsList(@RequestBody SearchCostRecordsListRequestModel requestModel) {
        return Result.success(trayCostBiz.searchCostRecordsList(requestModel));
    }
}
