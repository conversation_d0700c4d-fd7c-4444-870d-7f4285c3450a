package com.logistics.tms.mapper;

import com.logistics.tms.entity.TDriverAppointAddress;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2022/08/15
*/
@Mapper
public interface TDriverAppointAddressMapper extends BaseMapper<TDriverAppointAddress> {

	TDriverAppointAddress selectByPrimaryKeyDecrypt(Long id);

	int insertSelectiveEncrypt(TDriverAppointAddress tDriverAppoint);

	int updateByPrimaryKeySelectiveEncrypt(TDriverAppointAddress tDriverAppoint);

	/**
	 * 根据驾驶员预约注解批量查询驾驶员预约地址
	 *
	 * @param driverAppointIds
	 * @return
	 */
	List<TDriverAppointAddress> queryAddressListByDriverAppointIds(@Param("driverAppointIds") List<Long> driverAppointIds);
}