package com.logistics.tms.controller.demandorder.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2018/10/12 15:38
 */
@Data
public class UpdateDemandOrderStatusAndAmountModel {
    private List<Long> demandOrderIds;
    private List<Long> demandGoodIds;
    private List<Long> demandOrderRelIds;
    private Map<Long,BigDecimal> demandOrderArrangedAmountMap;
    private Map<Long,BigDecimal> demandGoodArrangedAmountMap;
    private Map<Long,BigDecimal> demandOrderRelArrangedAmountMap;
}
