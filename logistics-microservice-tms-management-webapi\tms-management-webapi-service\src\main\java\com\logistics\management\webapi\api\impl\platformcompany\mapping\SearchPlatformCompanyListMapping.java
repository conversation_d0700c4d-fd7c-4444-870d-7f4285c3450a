package com.logistics.management.webapi.api.impl.platformcompany.mapping;

import com.logistics.management.webapi.api.feign.platformcompany.dto.SearchPlatformCompanyListResponseDto;
import com.logistics.tms.api.feign.platformcompany.model.SearchPlatformCompanyListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @author: wjf
 * @date: 2022/11/11 17:57
 */
public class SearchPlatformCompanyListMapping extends MapperMapping<SearchPlatformCompanyListResponseModel, SearchPlatformCompanyListResponseDto> {
    @Override
    public void configure() {
        SearchPlatformCompanyListResponseModel source = getSource();
        SearchPlatformCompanyListResponseDto destination = getDestination();

        if (source.getCreatedTime() != null){
            destination.setCreatedTime(DateUtils.dateToString(source.getCreatedTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
    }
}
