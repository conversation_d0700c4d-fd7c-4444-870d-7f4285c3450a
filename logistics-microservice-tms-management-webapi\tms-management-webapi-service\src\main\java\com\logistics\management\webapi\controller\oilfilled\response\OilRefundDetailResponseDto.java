package com.logistics.management.webapi.controller.oilfilled.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/12/23 16:13
 */
@Data
public class OilRefundDetailResponseDto {
    @ApiModelProperty(value = "充油ID")
    private String oilFilledId="";
    @ApiModelProperty(value = "充油方式：1 充油卡，2 加油车")
    private String oilFilledType="";
    @ApiModelProperty(value = "充油方式文字")
    private String oilFilledTypeLabel= "";
    @ApiModelProperty(value = "车牌号Id")
    private String vehicleId="";
    @ApiModelProperty(value = "车牌号号码")
    private String vehicleNo="";
    @ApiModelProperty(value = "司机ID")
    private String staffId="";
    @ApiModelProperty(value = "司机")
    private String name="";
    @ApiModelProperty(value = "退款金额")
    private String oilFilledFee="";
    @ApiModelProperty(value = "退款时间")
    private String oilFilledDate="";
    @ApiModelProperty(value = "退款原因类型：0 其他，10 丢失副卡，20 车辆过户，30 车辆报废，40 充油充错")
    private String refundReasonType="";
    private String refundReasonTypeDesc="";
    @ApiModelProperty(value = "退款原因")
    private String refundReason="";
    @ApiModelProperty(value = "备注")
    private String remark="";
    @ApiModelProperty(value = "附件相对路径")
    private String refundFile="";
    @ApiModelProperty(value = "附件绝对路径")
    private String refundFilePath="";

    @ApiModelProperty(value = "是否有结算数据,1有，0没有")
    private String ifSettlement = "0";
}
