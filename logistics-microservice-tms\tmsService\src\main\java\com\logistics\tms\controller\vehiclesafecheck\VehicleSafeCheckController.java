package com.logistics.tms.controller.vehiclesafecheck;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.vehiclesafecheck.SafeCheckBiz;
import com.logistics.tms.controller.vehiclesafecheck.request.*;
import com.logistics.tms.controller.vehiclesafecheck.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/15 9:22
 */
@Api(value = "安全检查")
@RestController
public class VehicleSafeCheckController {

    @Resource
    private SafeCheckBiz safeCheckBiz;

    /**
     * 列表
     * @param requestModel
     * @return
     */
    @ApiOperation("列表")
    @PostMapping(value = "/service/safeCheck/searchList")
    public Result<PageInfo<SearchSafeCheckListResponseModel>> searchList(@RequestBody SearchSafeCheckListRequestModel requestModel) {
        return Result.success(safeCheckBiz.searchList(requestModel));
    }

    /**
     * 列表汇总
     * @param requestModel
     * @return
     */
    @ApiOperation("列表汇总")
    @PostMapping(value = "/service/safeCheck/getListSummary")
    public Result<SummarySafeCheckResponseModel> getListSummary(@RequestBody SearchSafeCheckListRequestModel requestModel) {
        return Result.success(safeCheckBiz.getListSummary(requestModel));
    }

    /**
     * 车辆检查-新增
     * @param requestModel
     * @return
     */
    @ApiOperation("车辆检查-新增")
    @PostMapping(value = "/service/safeCheck/addSafeCheck")
    public Result<Boolean> addSafeCheck(@RequestBody AddSafeCheckRequestModel requestModel) {
        safeCheckBiz.addSafeCheck(requestModel);
        return Result.success(true);
    }

    /**
     * 删除
     * @param requestModel
     * @return
     */
    @ApiOperation("删除")
    @PostMapping(value = "/service/safeCheck/delSafeCheck")
    public Result<Boolean> delSafeCheck(@RequestBody DelSafeCheckRequestModel requestModel) {
        safeCheckBiz.delSafeCheck(requestModel);
        return Result.success(true);
    }

    /**
     * 详情
     * @param requestModel
     * @return
     */
    @ApiOperation("详情")
    @PostMapping(value = "/service/safeCheck/getDetail")
    public Result<SafeCheckDetailResponseModel> getDetail(@RequestBody SafeCheckDetailRequestModel requestModel) {
        return Result.success(safeCheckBiz.getDetail(requestModel));
    }

    /**
     * 车辆检查-待检查/待整改-提交
     * @param requestModel
     * @return
     */
    @ApiOperation("车辆检查-待检查/待整改-提交")
    @PostMapping(value = "/service/safeCheck/addSafeCheckDetail")
    public Result<Boolean> addSafeCheckDetail(@RequestBody AddSafeCheckDetailRequestModel requestModel) {
        safeCheckBiz.addSafeCheckDetail(requestModel);
        return Result.success(true);
    }

    /**
     * 车辆检查-提交整改结果
     * @param requestModel
     * @return
     */
    @ApiOperation("车辆检查-提交整改结果")
    @PostMapping(value = "/service/safeCheck/addReformResult")
    public Result<Boolean> addReformResult(@RequestBody AddReformResultRequestModel requestModel) {
        safeCheckBiz.addReformResult(requestModel);
        return Result.success(true);
    }

    /**
     * 车辆检查-重新检查
     * @param requestModel
     * @return
     */
    @ApiOperation("车辆检查-重新检查")
    @PostMapping(value = "/service/safeCheck/reformCheck")
    public Result<Boolean> reformCheck(@RequestBody ReformCheckRequestModel requestModel) {
        safeCheckBiz.reformCheck(requestModel);
        return Result.success(true);
    }

    /**
     * 车辆检车看板（列表）
     * @param requestModel
     * @return
     */
    @ApiOperation(("车辆检车看板（列表）"))
    @PostMapping(value = "/service/safeCheck/getSafeCheckBoardInfo")
    public Result<List<SafeCheckBoardResponseModel>> getSafeCheckBoardInfo(@RequestBody SafeCheckBoardRequestModel requestModel) {
        return Result.success(safeCheckBiz.getSafeCheckBoardInfo(requestModel));
    }

    //小程序

    /**
     * 小程序-车辆检查列表
     * @param requestModel
     * @return
     */
    @ApiOperation("列表")
    @PostMapping(value = "/service/applet/safeCheck/searchAppletList")
    public Result<PageInfo<AppletSafeCheckListResponseModel>> searchAppletList(@RequestBody AppletSafeCheckListRequestModel requestModel) {
        return Result.success(safeCheckBiz.searchAppletList(requestModel));
    }

    /**
     * 小程序-车辆检查汇总
     * @param requestModel
     * @return
     */
    @ApiOperation("列表汇总")
    @PostMapping(value = "/service/applet/safeCheck/getAppletSummary")
    public Result<AppletSafeCheckSummaryResponseModel> getAppletSummary(@RequestBody AppletSafeCheckListRequestModel requestModel) {
        return Result.success(safeCheckBiz.getAppletSummary(requestModel));
    }

    /**
     * 小程序-提交待检查
     * @param requestModel
     * @return
     */
    @ApiOperation("待检查-提交")
    @PostMapping(value = "/service/applet/safeCheck/submitWaitCheck")
    public Result<Boolean> submitWaitCheck(@RequestBody AppletAddWaitCheckRequestModel requestModel) {
        safeCheckBiz.submitWaitCheck(requestModel);
        return Result.success(true);
    }

    /**
     * 小程序-提交待整改
     * @param requestModel
     * @return
     */
    @ApiOperation("待整改-提交")
    @PostMapping(value = "/service/applet/safeCheck/submitWaitReform")
    public Result<Boolean> submitWaitReform(@RequestBody AppletAddWaitReformRequestModel requestModel) {
        safeCheckBiz.submitWaitReform(requestModel);
        return Result.success(true);
    }
}
