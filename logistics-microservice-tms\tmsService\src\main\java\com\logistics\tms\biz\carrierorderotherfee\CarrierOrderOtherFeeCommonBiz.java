package com.logistics.tms.biz.carrierorderotherfee;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2022/9/9 10:38
 */
@Service
public class CarrierOrderOtherFeeCommonBiz {

    @Autowired
    private TCarrierOrderOtherFeeMapper tCarrierOrderOtherFeeMapper;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private TVehicleSettlementRelationMapper tVehicleSettlementRelationMapper;
    @Autowired
    private TExtVehicleSettlementMapper tExtVehicleSettlementMapper;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private CarrierOrderCommonBiz carrierOrderCommonBiz;
    @Resource
    private TCarrierOrderMapper tCarrierOrderMapper;

    /**
     * 查询运单已审核的临时费用
     * @param carrierOrderIds 运单id
     * @return 运单id对应的临时费用合计
     */
    public Map<Long, BigDecimal> getAuditFee(String carrierOrderIds){
        Map<Long, BigDecimal> feeMap = new HashMap<>();

        //根据运单id查询已审核的临时费用信息
        List<TCarrierOrderOtherFee> carrierOrderOtherFeeList = tCarrierOrderOtherFeeMapper.getAuditByCarrierOrderIds(carrierOrderIds);
        if (ListUtils.isNotEmpty(carrierOrderOtherFeeList)){
            for (TCarrierOrderOtherFee tCarrierOrderOtherFee : carrierOrderOtherFeeList) {
                feeMap.merge(tCarrierOrderOtherFee.getCarrierOrderId(), tCarrierOrderOtherFee.getTotalAmount(), BigDecimal::add);
            }
        }
        return feeMap;
    }

    /**
     * 新增临时费用校验
     * @param dbCarrierOrder 运单
     * @param requestSource 请求来源：1 后台，2 前台，3 小程序
     */
    public void addOtherFeeCheck(TCarrierOrder dbCarrierOrder, String requestSource){
        //判断是否有新增权限
        //后台
        if (CommonConstant.ONE.equals(requestSource)){
            //查询车主信息
            TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(dbCarrierOrder.getCompanyCarrierId());
            if(tCompanyCarrier==null || IfValidEnum.INVALID.getKey().equals(tCompanyCarrier.getValid())){
                throw new BizException(CarrierDataExceptionEnum.NO_COMPANY_CARRIER);
            }

            //判断当前车主是否有权限
            if(CarrierOrderOtherFeeIsSubmitEnum.NOT_SUBMIT.getCode().equals(tCompanyCarrier.getCommitOtherFee())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_COMPANY_NO_PERMISSION);
            }
        }
        //前台
        else if (CommonConstant.TWO.equals(requestSource)){
            //查询登录人所属车主id
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (companyCarrierId == null || companyCarrierId.equals(CommonConstant.LONG_ZERO)) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ACCOUNT_NOT_EXIST);
            }

            //判断运单是否是该车主的
            if (!dbCarrierOrder.getCompanyCarrierId().equals(companyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
            }

            //查询车主信息
            TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(dbCarrierOrder.getCompanyCarrierId());
            if(tCompanyCarrier==null || IfValidEnum.INVALID.getKey().equals(tCompanyCarrier.getValid())){
                throw new BizException(CarrierDataExceptionEnum.NO_COMPANY_CARRIER);
            }

            //判断当前车主是否有权限
            if(CarrierOrderOtherFeeIsSubmitEnum.NOT_SUBMIT.getCode().equals(tCompanyCarrier.getCommitOtherFee())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_COMPANY_NO_PERMISSION);
            }
        }
        //小程序
        else if (CommonConstant.THREE.equals(requestSource)){
            //校验运单是否属于当前登录司机
            carrierOrderCommonBiz.checkDriver4Node(dbCarrierOrder.getId(), dbCarrierOrder.getCompanyCarrierId());

            //只有我司才能新增
            //查询车主信息
            TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(dbCarrierOrder.getCompanyCarrierId());
            if(tCompanyCarrier==null || IfValidEnum.INVALID.getKey().equals(tCompanyCarrier.getValid())){
                throw new BizException(CarrierDataExceptionEnum.NO_COMPANY_CARRIER);
            }
            if (!IsOurCompanyEnum.OUR_COMPANY.getKey().equals(tCompanyCarrier.getLevel())){
                throw new BizException(CarrierDataExceptionEnum.NO_QIYA_ORDER);
            }

            //只有【待提货】【待卸货】才能新增
            if (!CarrierOrderStatusEnum.WAIT_LOAD.getKey().equals(dbCarrierOrder.getStatus()) &&
                    !CarrierOrderStatusEnum.WAIT_UNLOAD.getKey().equals(dbCarrierOrder.getStatus())){
                throw new BizException(CarrierDataExceptionEnum.COMMIT_CARRIER_ORDER_FEE_ORDER_STATUS_ERROR);
            }
        }else{
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }

        //非云盘运单不能新增
        if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(dbCarrierOrder.getDemandOrderSource())){
            throw new BizException(CarrierDataExceptionEnum.ADD_TEMPORARY_FEE_ERROR);
        }

        //运单已关联对账
        if (dbCarrierOrder.getCarrierSettleStatementStatus() > CarrierSettleStatementStatusEnum.NOT_RELATED.getKey()) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_ALREADY_RELEVANCY_RECONCILIATION);
        }

        //一个运单只能新增一条临时费用
        List<TCarrierOrderOtherFee> dbCarrierOrderOtherFeeList = tCarrierOrderOtherFeeMapper.getNoCancelByCarrierOrderId(dbCarrierOrder.getId());
        if (ListUtils.isNotEmpty(dbCarrierOrderOtherFeeList)){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_FEE_EXIST);
        }

        commonCheck(dbCarrierOrder.getId());
    }

    /**
     * 公共校验
     * @param carrierOrderId
     */
    public void commonCheck(Long carrierOrderId){
        //判断运单是否已关联自有车辆结算
        List<TVehicleSettlementRelation> vehicleSettlementRelations = tVehicleSettlementRelationMapper.getByObjectIdAndType(VehicleSettlementTypeEnum.CARRIER_ORDER.getKey(), carrierOrderId);
        if(ListUtils.isNotEmpty(vehicleSettlementRelations)){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_DATA_EXIST);
        }

        //判断运单是否已关联外部车辆结算并已付款
        List<TExtVehicleSettlement> extVehicleSettlementList = tExtVehicleSettlementMapper.getByCarrierOrderIds(carrierOrderId.toString());
        if (ListUtils.isNotEmpty(extVehicleSettlementList) && VehicleSettlementStatusEnum.PAYMENT.getKey().equals(extVehicleSettlementList.get(CommonConstant.INTEGER_ZERO).getStatus())){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_DATA_EXIST);
        }
    }

    /**
     * 提交校验
     *
     * @param carrierOrderOtherFeeId 临时费用id
     * @param requestSource 请求来源：1 后台，2 前台，3 小程序
     * @return
     */
    public TCarrierOrderOtherFee commitCheck(Long carrierOrderOtherFeeId, String requestSource) {
        //查看该费用记录是否存在
        TCarrierOrderOtherFee tCarrierOrderOtherFee = tCarrierOrderOtherFeeMapper.selectByPrimaryKey(carrierOrderOtherFeeId);
        if (tCarrierOrderOtherFee == null || tCarrierOrderOtherFee.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_EXIST);
        }

        TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(tCarrierOrderOtherFee.getCarrierOrderId());
        if (tCarrierOrder == null) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //后台
        if (CommonConstant.ONE.equals(requestSource)){
            //只有待提交、已驳回状态才能提交
            if (!CarrierOrderOtherFeeStatusEnum.WAIT_COMMIT.getKey().equals(tCarrierOrderOtherFee.getAuditStatus())
                    && !CarrierOrderOtherFeeStatusEnum.AUDIT_REJECT.getKey().equals(tCarrierOrderOtherFee.getAuditStatus())) {
                throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_COMMIT);
            }

            //运单非【已签收】状态，不能提交
            if (!CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(tCarrierOrder.getStatus())) {
                throw new BizException(CarrierDataExceptionEnum.COMMIT_CARRIER_ORDER_FEE_ORDER_STATUS_ERROR);
            }
        }
        //前台
        else if (CommonConstant.TWO.equals(requestSource)) {
            //查询登录人所属车主id
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            //判断是否是该车主的费用
            if (!tCarrierOrder.getCompanyCarrierId().equals(companyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_EXIST);
            }

            //只有待提交、已驳回状态才能提交
            if (!CarrierOrderOtherFeeStatusEnum.WAIT_COMMIT.getKey().equals(tCarrierOrderOtherFee.getAuditStatus())
                    && !CarrierOrderOtherFeeStatusEnum.AUDIT_REJECT.getKey().equals(tCarrierOrderOtherFee.getAuditStatus())) {
                throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_COMMIT);
            }

            //运单非【已签收】状态，不能提交
            if (!CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(tCarrierOrder.getStatus())) {
                throw new BizException(CarrierDataExceptionEnum.COMMIT_CARRIER_ORDER_FEE_ORDER_STATUS_ERROR);
            }
        }
        //小程序
        else if (CommonConstant.THREE.equals(requestSource)) {
            //校验运单是否属于当前登录司机
            carrierOrderCommonBiz.checkDriver4Node(tCarrierOrder.getId(), tCarrierOrder.getCompanyCarrierId());

            //只有待提交状态才能提交
            if (!CarrierOrderOtherFeeStatusEnum.WAIT_COMMIT.getKey().equals(tCarrierOrderOtherFee.getAuditStatus())) {
                throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_COMMIT);
            }

            //只有【待提货】【待卸货】才能提交
            if (!CarrierOrderStatusEnum.WAIT_LOAD.getKey().equals(tCarrierOrder.getStatus())
                    && !CarrierOrderStatusEnum.WAIT_UNLOAD.getKey().equals(tCarrierOrder.getStatus())){
                throw new BizException(CarrierDataExceptionEnum.COMMIT_CARRIER_ORDER_FEE_ORDER_STATUS_ERROR);
            }
        } else {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }

        //一个运单只能新增一条临时费用
        List<TCarrierOrderOtherFee> dbCarrierOrderOtherFeeList = tCarrierOrderOtherFeeMapper.getNoCancelByCarrierOrderId(tCarrierOrder.getId());
        if (ListUtils.isNotEmpty(dbCarrierOrderOtherFeeList)){
            //排除当前操作的费用外，如果还有费用，则不允许提交
            dbCarrierOrderOtherFeeList = dbCarrierOrderOtherFeeList.stream().filter(item -> !item.getId().equals(tCarrierOrderOtherFee.getId())).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(dbCarrierOrderOtherFeeList)) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_FEE_EXIST);
            }
        }
        return tCarrierOrderOtherFee;
    }

    /**
     * 审核通过校验
     *
     * @param carrierOrderOtherFeeId 临时费用id
     * @return
     */
    public TCarrierOrderOtherFee auditCheck(Long carrierOrderOtherFeeId) {
        //查看该费用记录是否存在
        TCarrierOrderOtherFee tCarrierOrderOtherFee = tCarrierOrderOtherFeeMapper.selectByPrimaryKey(carrierOrderOtherFeeId);
        if (tCarrierOrderOtherFee == null || tCarrierOrderOtherFee.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_EXIST);
        }

        TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(tCarrierOrderOtherFee.getCarrierOrderId());
        if (tCarrierOrder == null) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //判断该费用是否为待审核
        if (!CarrierOrderOtherFeeStatusEnum.WAIT_AUDIT.getKey().equals(tCarrierOrderOtherFee.getAuditStatus())) {
            throw new BizException(CarrierDataExceptionEnum.TEMPORARY_FEE_NOT_AUDIT);
        }

        //判断运单是否已关联对账
        if (tCarrierOrder.getCarrierSettleStatementStatus() > CarrierSettleStatementStatusEnum.NOT_RELATED.getKey()) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_ALREADY_RELEVANCY_RECONCILIATION);
        }

        commonCheck(tCarrierOrder.getId());

        return tCarrierOrderOtherFee;
    }
}
