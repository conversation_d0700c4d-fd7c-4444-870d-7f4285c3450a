package com.logistics.appapi.controller.reserve.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Data
public class ApplyReserveBalanceRequestDto {

	@ApiModelProperty("申请记录id, 重新提交必填")
	private String applyId;

	@ApiModelProperty(value = "申请金额,0<金额<=10000", required = true)
	@DecimalMax(value = "10000.00", message = "请输入正确的申请金额,0<金额<=10000")
	@DecimalMin(value = "0.00", inclusive = false, message = "请输入正确的申请金额,0<金额<=10000")
	@NotBlank(message = "请输入申请金额")
	private String applyAmount;

	@ApiModelProperty(value = "收款账户", required = true)
	@NotBlank(message = "请选择收款账户")
	private String receiveBankAccount;

	@ApiModelProperty("备注,最大100个字符")
	@Length(max = 100, message = "备注不能大于100个字符")
	private String remark;
}
