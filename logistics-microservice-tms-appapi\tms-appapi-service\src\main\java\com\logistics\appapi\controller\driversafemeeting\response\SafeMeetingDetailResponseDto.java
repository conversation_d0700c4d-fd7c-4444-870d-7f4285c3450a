package com.logistics.appapi.controller.driversafemeeting.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/11/8 11:57
 */
@Data
public class SafeMeetingDetailResponseDto {
    @ApiModelProperty("学习例会关系id")
    private String safeMeetingRelationId="";
    @ApiModelProperty("学习状态：0未学习，1已学习")
    private String status="";
    private String statusDesc="";
    @ApiModelProperty(value = "学习月份")
    private String period="";
    @ApiModelProperty(value = "创建人（组织人）")
    private String createdBy="";
    @ApiModelProperty(value = "创建时间（发布时间）")
    private String createdTime="";
    @ApiModelProperty(value = "学习标题")
    private String title="";
    @ApiModelProperty(value = "学习内容")
    private String content="";
    @ApiModelProperty(value = "驾驶员图片")
    private String staffDriverImageUrl="";
    @ApiModelProperty(value = "签字图片")
    private String signImageUrl="";
}
