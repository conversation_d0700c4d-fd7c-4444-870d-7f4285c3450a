package com.logistics.tms.biz.biddingorder.bo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/04/28
 */
@Data
public class SearchBiddingOrderListRespBo {
    /**
     * 竞价单id
     */
    private Long biddingOrderId;

    /**
     * 竞价单号
     */
    private String biddingOrderCode;

    /**
     * 竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价
     */
    private Integer biddingStatus;

    /**
     * 是否取消：0 否，1 是
     */
    private Integer ifCancel;

    /**
     * 装卸方式 1 一装一卸、2 多装一卸
     */
    private Integer handlingMode;

    /**
     * 关联单据
     */
    private String demandCount;

    /**
     * 途径点
     */
    private Integer pathwayCount;

    /**
     * 数量
     */
    private BigDecimal goodsCount;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 报价开始时间
     */
    private Date quoteStartTime;

    /**
     * 报价结束时间
     */
    private Date quoteEndTime;
}
