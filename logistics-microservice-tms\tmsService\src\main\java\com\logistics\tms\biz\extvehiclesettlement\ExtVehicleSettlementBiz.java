package com.logistics.tms.biz.extvehiclesettlement;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel;
import com.logistics.tms.api.feign.extvehiclesettlement.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.carrierorderotherfee.CarrierOrderOtherFeeCommonBiz;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.MapUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2019/11/20 14:30
 */
@Service
public class ExtVehicleSettlementBiz {

    @Autowired
    private TExtVehicleSettlementMapper extVehicleSettlementMapper;
    @Autowired
    private TExtVehicleSettlementItemMapper extVehicleSettlementItemMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TCertificationPicturesMapper certificationPicturesMapper;
    @Autowired
    private TCarrierOrderMapper carrierOrderMapper;
    @Autowired
    private TCarrierOrderOperateLogsMapper carrierOrderOperateLogsMapper;
    @Autowired
    private CarrierOrderOtherFeeCommonBiz carrierOrderOtherFeeCommonBiz;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;

    /**
     * 生成结算数据（物流系统同步过来、运单卸货、运单放空）
     *
     * @param carrierOrderIdList 运单id集合
     * @param ifEmpty            是否放空
     * @param ifCorrect          是否纠错
     */
    public void createExtVehicleSettlement(List<Long> carrierOrderIdList, boolean ifEmpty, boolean ifCorrect) {
        if (ListUtils.isEmpty(carrierOrderIdList)) {
            return;
        }

        List<TExtVehicleSettlement> upExtVehicleSettlementList = new ArrayList<>();
        //查询存在的结算数据
        List<TExtVehicleSettlement> existList = extVehicleSettlementMapper.getByCarrierOrderIds(StringUtils.listToString(carrierOrderIdList, ','));
        if (ListUtils.isNotEmpty(existList)) {
            //已存在结算数据的
            List<Long> existIdList = new ArrayList<>();
            for (TExtVehicleSettlement tExtVehicleSettlement : existList) {
                //是纠错并且未支付,删掉当前结算信息重新生成
                if (ifCorrect && carrierOrderIdList.contains(tExtVehicleSettlement.getCarrierOrderId())
                        && VehicleSettlementStatusEnum.NOT_PAYMENT.getKey().equals(tExtVehicleSettlement.getStatus())) {
                    TExtVehicleSettlement upExtVehicleSettlement = new TExtVehicleSettlement();
                    upExtVehicleSettlement.setId(tExtVehicleSettlement.getId());
                    upExtVehicleSettlement.setValid(IfValidEnum.INVALID.getKey());
                    commonBiz.setBaseEntityModify(upExtVehicleSettlement, BaseContextHandler.getUserName());
                    upExtVehicleSettlementList.add(upExtVehicleSettlement);
                } else {
                    existIdList.add(tExtVehicleSettlement.getCarrierOrderId());
                }
            }
            carrierOrderIdList.removeAll(existIdList);
        }
        if (ListUtils.isEmpty(carrierOrderIdList)) {
            return;
        }

        List<ExtVehicleCarrierOrderModel> carrierOrderList = carrierOrderMapper.getExtVehicleCarrierOrder(StringUtils.listToString(carrierOrderIdList, ','));
        if (ListUtils.isEmpty(carrierOrderList)) {
            return;
        }
        TExtVehicleSettlement extVehicleSettlement;
        List<TExtVehicleSettlement> addList = new ArrayList<>();
        for (ExtVehicleCarrierOrderModel model : carrierOrderList) {
            BigDecimal amount = BigDecimal.ZERO;
            for (SearchCarrierOrderListGoodsInfoModel goods : model.getGoodsInfoList()) {
                if (ifEmpty) {
                    amount = amount.add(goods.getExpectAmount());
                } else {
                    amount = amount.add(goods.getUnloadAmount());
                }

            }
            BigDecimal dispatchFreightFee;
            if (model.getDispatchFreightFeeType().equals(FreightTypeEnum.UNIT_PRICE.getKey()) && !ifEmpty) {
                dispatchFreightFee = model.getDispatchFreightFee().multiply(amount).setScale(2, RoundingMode.HALF_UP);
            } else {
                dispatchFreightFee = model.getDispatchFreightFee();
            }

            extVehicleSettlement = new TExtVehicleSettlement();
            extVehicleSettlement.setCarrierOrderId(model.getCarrierOrderId());
            extVehicleSettlement.setVehicleId(model.getVehicleId());
            extVehicleSettlement.setVehicleNo(model.getVehicleNo());
            extVehicleSettlement.setStaffId(model.getStaffId());
            extVehicleSettlement.setStaffName(model.getStaffName());
            extVehicleSettlement.setStaffMobile(model.getStaffMobile());
            extVehicleSettlement.setDriverTotalFee(dispatchFreightFee);
            extVehicleSettlement.setSettlementUnit(model.getGoodsUnit());
            extVehicleSettlement.setSettlementAmount(amount);
            commonBiz.setBaseEntityAdd(extVehicleSettlement, model.getUserName());
            addList.add(extVehicleSettlement);
        }

        if (ListUtils.isNotEmpty(upExtVehicleSettlementList)) {
            extVehicleSettlementMapper.batchUpdateSelective(upExtVehicleSettlementList);
        }

        extVehicleSettlementMapper.batchInsert(addList);
    }

    /**
     * 列表
     * @param requestModel
     * @return
     */
    public PageInfo<SearchExtVehicleSettlementListResponseModel> searchExtVehicleSettlementList(SearchExtVehicleSettlementListRequestModel requestModel) {
        if (StringUtils.isBlank(requestModel.getDispatchTimeFrom()) || StringUtils.isBlank(requestModel.getDispatchTimeTo())){
            Date now = new Date();
            Date last = DateUtils.add(now, Calendar.MONTH, CommonConstant.NEGATIVE_INTEGER_TWO);
            requestModel.setDispatchTimeFrom(DateUtils.dateToString(now,DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            requestModel.setDispatchTimeTo(DateUtils.dateToString(last,DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        //分页查询
        requestModel.enablePaging();
        List<Long> idList = extVehicleSettlementMapper.searchExtVehicleSettlementIdList(requestModel);
        PageInfo pageInfo = new PageInfo(idList);
        pageInfo.setList(new ArrayList());
        if (ListUtils.isNotEmpty(idList)){
            //查询列表信息
            List<SearchExtVehicleSettlementListResponseModel> list = extVehicleSettlementMapper.searchExtVehicleSettlementList(StringUtils.listToString(idList,','));

            //已支付的结算表id
            List<Long> paySettlementIdList=new ArrayList<>();
            //运单id
            List<Long> carrierOrderIdList=new ArrayList<>();
            //车主id
            List<Long> companyCarrierIdList = new ArrayList<>();

            for (SearchExtVehicleSettlementListResponseModel model : list) {
                if(VehicleSettlementStatusEnum.PAYMENT.getKey().equals(model.getPayStatus())){//已支付
                    paySettlementIdList.add(model.getExtVehicleSettlementId());
                }
                carrierOrderIdList.add(model.getCarrierOrderId());
                companyCarrierIdList.add(model.getCompanyCarrierId());
            }

            //查询已付款信息
            Map<Long, BigDecimal> totalFeeMap = new HashMap<>();
            if (ListUtils.isNotEmpty(paySettlementIdList)) {
                List<TExtVehicleSettlementItem> itemList = extVehicleSettlementItemMapper.getValidBySettlementIds(StringUtils.listToString(paySettlementIdList, ','));
                for (TExtVehicleSettlementItem settlementItem : itemList) {
                    totalFeeMap.put(settlementItem.getExtVehicleSettlementId(), settlementItem.getTotalFee());
                }
            }

            //查询车主信息
            Map<Long, Integer> companyLevelMap = tCompanyCarrierMapper.getByIds(StringUtils.listToString(companyCarrierIdList,',')).stream().collect(Collectors.toMap(TCompanyCarrier::getId, TCompanyCarrier::getLevel));

            //查询运单临时费用信息
            Map<Long, BigDecimal> driverOtherFeeMap = new HashMap<>();
            if (ListUtils.isNotEmpty(carrierOrderIdList)) {
                driverOtherFeeMap = carrierOrderOtherFeeCommonBiz.getAuditFee(StringUtils.listToString(carrierOrderIdList,','));
            }

            //拼接数据
            if (!MapUtils.isEmpty(totalFeeMap) || !MapUtils.isEmpty(driverOtherFeeMap)) {
                for (SearchExtVehicleSettlementListResponseModel responseModel : list) {
                    //赋值已付款的结算合计总费用
                    responseModel.setTotalFee(totalFeeMap.get(responseModel.getExtVehicleSettlementId()));

                    //赋值司机临时费用-我司时，司机才加临时费用
                    if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(companyLevelMap.get(responseModel.getCompanyCarrierId()))) {
                        responseModel.setDriverOtherFee(driverOtherFeeMap.get(responseModel.getCarrierOrderId()));
                    }
                }
            }

            pageInfo.setList(list);
        }
        return pageInfo;
    }

    /**
     * 付款详情
     * @param requestModel
     * @return
     */
    public ExtVehicleSettlementDetailResponseModel extVehicleSettlementDetail(ExtVehicleSettlementIdRequestModel requestModel) {
        //查询结算信息是否存在
        TExtVehicleSettlement tExtVehicleSettlement = extVehicleSettlementMapper.selectByPrimaryKey(requestModel.getExtVehicleSettlementId());
        if (tExtVehicleSettlement == null || tExtVehicleSettlement.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.EXT_VEHICLE_SETTLEMENT_EMPTY);
        }

        //不同的操作判断状态
        if (CommonConstant.INTEGER_THREE.equals(requestModel.getOperateType())){//回退
            //未支付状态不能回退
            if (tExtVehicleSettlement.getStatus().equals(VehicleSettlementStatusEnum.NOT_PAYMENT.getKey())){
                throw new BizException(CarrierDataExceptionEnum.EXT_VEHICLE_SETTLEMENT_NOT_PAYMENT);
            }
        }else if (CommonConstant.INTEGER_TWO.equals(requestModel.getOperateType())){//付款
            //已支付状态不能支付
            if (tExtVehicleSettlement.getStatus().equals(VehicleSettlementStatusEnum.PAYMENT.getKey())){
                throw new BizException(CarrierDataExceptionEnum.EXT_VEHICLE_SETTLEMENT_PAYMENT);
            }
            //查询运单信息
            TCarrierOrder dbCarrierOrder = carrierOrderMapper.selectByPrimaryKeyDecrypt(tExtVehicleSettlement.getCarrierOrderId());
            if (dbCarrierOrder == null || dbCarrierOrder.getValid().equals(IfValidEnum.INVALID.getKey())){
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
            }
            //运单已签收或已放空才能付款
            if (!dbCarrierOrder.getStatus().equals(CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey()) && !dbCarrierOrder.getIfEmpty().equals(CommonConstant.INTEGER_ONE)){
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_OPERATION_ERROR);
            }
        }else if (!CommonConstant.INTEGER_ONE.equals(requestModel.getOperateType())){//查询支付详情
            throw new BizException(CarrierDataExceptionEnum.EXT_VEHICLE_SETTLEMENT_EMPTY);
        }

        //查询详情信息
        ExtVehicleSettlementDetailResponseModel detail = extVehicleSettlementMapper.extVehicleSettlementDetail(requestModel.getExtVehicleSettlementId());
        if (detail == null){
            throw new BizException(CarrierDataExceptionEnum.EXT_VEHICLE_SETTLEMENT_EMPTY);
        }

        //查询车主信息
        Map<Long, Integer> companyLevelMap = tCompanyCarrierMapper.getByIds(detail.getCompanyCarrierId().toString()).stream().collect(Collectors.toMap(TCompanyCarrier::getId, TCompanyCarrier::getLevel));

        //赋值司机临时费用-我司时，司机才加临时费用
        if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(companyLevelMap.get(detail.getCompanyCarrierId()))) {
            //查询运单临时费用
            Map<Long, BigDecimal> driverOtherFeeMap = carrierOrderOtherFeeCommonBiz.getAuditFee(tExtVehicleSettlement.getCarrierOrderId().toString());
            detail.setDriverOtherFee(driverOtherFeeMap.get(tExtVehicleSettlement.getCarrierOrderId()));
        }

        //查询票据信息
        List<TCertificationPictures> tCertificationPicturesList = certificationPicturesMapper.getByObjectIdType(requestModel.getExtVehicleSettlementId(), CertificationPicturesObjectTypeEnum.T_EXT_VEHICLE_SETTLEMENT.getObjectType(), CertificationPicturesFileTypeEnum.EXT_VEHICLE_SETTLEMENT_ATTACHMENT_FILE.getFileType());
        detail.setAttachmentList(tCertificationPicturesList.stream().map(TCertificationPictures::getFilePath).collect(Collectors.toList()));
        return detail;
    }

    /**
     * 付款
     * @param requestModel
     */
    @Transactional
    public void extVehicleSettlementPayment(ExtVehicleSettlementPaymentRequestModel requestModel) {
        //查询结算信息是否存在
        TExtVehicleSettlement tExtVehicleSettlement = extVehicleSettlementMapper.selectByPrimaryKey(requestModel.getExtVehicleSettlementId());
        if (tExtVehicleSettlement == null || tExtVehicleSettlement.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.EXT_VEHICLE_SETTLEMENT_EMPTY);
        }
        //已支付状态不能支付
        if (tExtVehicleSettlement.getStatus().equals(VehicleSettlementStatusEnum.PAYMENT.getKey())){
            throw new BizException(CarrierDataExceptionEnum.EXT_VEHICLE_SETTLEMENT_PAYMENT);
        }
        //查询运单信息
        TCarrierOrder dbCarrierOrder = carrierOrderMapper.selectByPrimaryKeyDecrypt(tExtVehicleSettlement.getCarrierOrderId());
        if (dbCarrierOrder == null || dbCarrierOrder.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //运单已签收或已放空才能付款
        if (!dbCarrierOrder.getStatus().equals(CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey()) && !dbCarrierOrder.getIfEmpty().equals(CommonConstant.INTEGER_ONE)){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_OPERATION_ERROR);
        }
        String userName = BaseContextHandler.getUserName();

        //更新外部车辆结算状态
        TExtVehicleSettlement extVehicleSettlement = new TExtVehicleSettlement();
        extVehicleSettlement.setId(tExtVehicleSettlement.getId());
        extVehicleSettlement.setStatus(VehicleSettlementStatusEnum.PAYMENT.getKey());
        commonBiz.setBaseEntityModify(extVehicleSettlement, userName);
        extVehicleSettlementMapper.updateByPrimaryKeySelective(extVehicleSettlement);

        //新增外部车辆结算子表
        TExtVehicleSettlementItem extVehicleSettlementItem = new TExtVehicleSettlementItem();
        extVehicleSettlementItem.setExtVehicleSettlementId(tExtVehicleSettlement.getId());
        extVehicleSettlementItem.setPaymentChannel(requestModel.getPaymentChannel());
        extVehicleSettlementItem.setPaymentNo(requestModel.getPaymentNo());
        extVehicleSettlementItem.setPaymentFee(requestModel.getPaymentFee());
        extVehicleSettlementItem.setReimburseFee(requestModel.getReimburseFee());
        extVehicleSettlementItem.setTotalFee(tExtVehicleSettlement.getDriverTotalFee().add(requestModel.getReimburseFee()).subtract(requestModel.getPaymentFee()).setScale(2, BigDecimal.ROUND_HALF_UP));
        extVehicleSettlementItem.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityAdd(extVehicleSettlementItem,userName);
        extVehicleSettlementItemMapper.insertSelective(extVehicleSettlementItem);

        //新增票据
        if (ListUtils.isNotEmpty(requestModel.getAttachmentList())) {
            List<TCertificationPictures> addList = new ArrayList<>();
            TCertificationPictures certificationPictures;
            Date now = new Date();
            for (String path : requestModel.getAttachmentList()) {
                certificationPictures = new TCertificationPictures();
                certificationPictures.setObjectId(tExtVehicleSettlement.getId());
                certificationPictures.setObjectType(CertificationPicturesObjectTypeEnum.T_EXT_VEHICLE_SETTLEMENT.getObjectType());
                certificationPictures.setFileType(CertificationPicturesFileTypeEnum.EXT_VEHICLE_SETTLEMENT_ATTACHMENT_FILE.getFileType());
                certificationPictures.setFileName(CertificationPicturesFileTypeEnum.EXT_VEHICLE_SETTLEMENT_ATTACHMENT_FILE.getFileName());
                certificationPictures.setFileTypeName(CertificationPicturesFileTypeEnum.EXT_VEHICLE_SETTLEMENT_ATTACHMENT_FILE.getFileName());
                certificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.EXT_VEHICLE_SETTLEMENT_ATTACHMENT_FILE.getKey(), "", path, null));
                certificationPictures.setUploadTime(now);
                certificationPictures.setUploadUserName(userName);
                certificationPictures.setSuffix(path.substring(path.lastIndexOf(CommonConstant.POINT)));
                commonBiz.setBaseEntityAdd(certificationPictures, userName);
                addList.add(certificationPictures);
            }
            if (ListUtils.isNotEmpty(addList)) {
                certificationPicturesMapper.batchInsert(addList);
            }
        }
        //记录运单付款记录
        createCarrierLogs(tExtVehicleSettlement.getCarrierOrderId(),CarrierOrderOperateLogsTypeEnum.EXT_VEHICLE_SETTLEMENT_PAYMENT.getKey(), CarrierOrderOperateLogsTypeEnum.EXT_VEHICLE_SETTLEMENT_PAYMENT.format(extVehicleSettlementItem.getTotalFee().toString()),requestModel.getRemark());
    }

    /**
     * 回退
     * @param requestModel
     */
    @Transactional
    public void extVehicleSettlementFallback(ExtVehicleSettlementFallbackRequestModel requestModel) {
        //查询结算信息是否存在
        TExtVehicleSettlement tExtVehicleSettlement = extVehicleSettlementMapper.selectByPrimaryKey(requestModel.getExtVehicleSettlementId());
        if (tExtVehicleSettlement == null || tExtVehicleSettlement.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.EXT_VEHICLE_SETTLEMENT_EMPTY);
        }
        //待支付状态不能回退
        if (tExtVehicleSettlement.getStatus().equals(VehicleSettlementStatusEnum.NOT_PAYMENT.getKey())){
            throw new BizException(CarrierDataExceptionEnum.EXT_VEHICLE_SETTLEMENT_NOT_PAYMENT);
        }
        //查询结算子表信息是否存在
        TExtVehicleSettlementItem tExtVehicleSettlementItem = extVehicleSettlementItemMapper.getValidBySettlementId(tExtVehicleSettlement.getId());
        if (tExtVehicleSettlementItem == null || tExtVehicleSettlementItem.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.EXT_VEHICLE_SETTLEMENT_EMPTY);
        }
        String userName = BaseContextHandler.getUserName();

        //更新外部车辆结算状态
        TExtVehicleSettlement extVehicleSettlement = new TExtVehicleSettlement();
        extVehicleSettlement.setId(tExtVehicleSettlement.getId());
        extVehicleSettlement.setStatus(VehicleSettlementStatusEnum.NOT_PAYMENT.getKey());
        commonBiz.setBaseEntityModify(extVehicleSettlement, userName);
        extVehicleSettlementMapper.updateByPrimaryKeySelective(extVehicleSettlement);

        //更新外部车辆结算子表
        TExtVehicleSettlementItem extVehicleSettlementItem = new TExtVehicleSettlementItem();
        extVehicleSettlementItem.setId(tExtVehicleSettlementItem.getId());
        extVehicleSettlementItem.setIfFallback(CommonConstant.INTEGER_ONE);
        extVehicleSettlementItem.setFallbackReason(requestModel.getFallbackReason());
        commonBiz.setBaseEntityModify(extVehicleSettlementItem,userName);
        extVehicleSettlementItemMapper.updateByPrimaryKeySelective(extVehicleSettlementItem);

        //删除票据信息
        List<TCertificationPictures> tCertificationPicturesList = certificationPicturesMapper.getByObjectIdType(tExtVehicleSettlement.getId(), CertificationPicturesObjectTypeEnum.T_EXT_VEHICLE_SETTLEMENT.getObjectType(), CertificationPicturesFileTypeEnum.EXT_VEHICLE_SETTLEMENT_ATTACHMENT_FILE.getFileType());
        if (ListUtils.isNotEmpty(tCertificationPicturesList)){
            List<TCertificationPictures> delList = new ArrayList<>();
            TCertificationPictures certificationPictures;
            for (TCertificationPictures pic:tCertificationPicturesList) {
                certificationPictures = new TCertificationPictures();
                certificationPictures.setId(pic.getId());
                certificationPictures.setValid(IfValidEnum.INVALID.getKey());
                commonBiz.setBaseEntityModify(certificationPictures,userName);
                delList.add(certificationPictures);
            }
            if (ListUtils.isNotEmpty(delList)){
                certificationPicturesMapper.batchUpdate(delList);
            }
        }
        //记录运单退款记录
        createCarrierLogs(tExtVehicleSettlement.getCarrierOrderId(),CarrierOrderOperateLogsTypeEnum.EXT_VEHICLE_SETTLEMENT_FALL_BACK.getKey(), CarrierOrderOperateLogsTypeEnum.EXT_VEHICLE_SETTLEMENT_FALL_BACK.format(tExtVehicleSettlementItem.getTotalFee().toString()),requestModel.getFallbackReason());
    }

    //记录运单操作记录
    public void createCarrierLogs(Long carrierOrderId,Integer type,String content,String remark){
        TCarrierOrderOperateLogs carrierOrderOperateLogs = new TCarrierOrderOperateLogs();
        carrierOrderOperateLogs.setCarrierOrderId(carrierOrderId);
        carrierOrderOperateLogs.setOperationType(type);
        carrierOrderOperateLogs.setOperationContent(content);
        carrierOrderOperateLogs.setRemark(remark);
        carrierOrderOperateLogs.setOperatorName(BaseContextHandler.getUserName());
        carrierOrderOperateLogs.setOperateTime(new Date());
        commonBiz.setBaseEntityAdd(carrierOrderOperateLogs,BaseContextHandler.getUserName());
        carrierOrderOperateLogsMapper.insertSelective(carrierOrderOperateLogs);
    }

    /**
     * 更新司机费用-运单签收触发
     * @param carrierOrderIdList
     * @param driverFeeMap
     */
    @Transactional
    public void updateDriverFee(List<Long> carrierOrderIdList, Map<Long, BigDecimal> driverFeeMap){
        List<TExtVehicleSettlement> dbExtVehicleSettlementList = extVehicleSettlementMapper.getByCarrierOrderIds(StringUtils.listToString(carrierOrderIdList,','));
        if (ListUtils.isEmpty(dbExtVehicleSettlementList)){
            return;
        }

        //更新司机费用
        TExtVehicleSettlement upExtVehicleSettlement;
        List<TExtVehicleSettlement> upList = new ArrayList<>();
        for (TExtVehicleSettlement tExtVehicleSettlement : dbExtVehicleSettlementList) {
            upExtVehicleSettlement = new TExtVehicleSettlement();
            upExtVehicleSettlement.setId(tExtVehicleSettlement.getId());
            upExtVehicleSettlement.setDriverTotalFee(driverFeeMap.get(tExtVehicleSettlement.getCarrierOrderId()));
            commonBiz.setBaseEntityModify(upExtVehicleSettlement, BaseContextHandler.getUserName());
            upList.add(upExtVehicleSettlement);
        }

        if (ListUtils.isNotEmpty(upList)) {
            extVehicleSettlementMapper.batchUpdateSelective(upList);
        }
    }


    /**
     * 判断是否生成外部结算并且已经支付
     *
     * @param carrierOrderId 运单id
     */
    public boolean checkAlreadyPaid(Long carrierOrderId) {
        boolean flag = false;
        TExtVehicleSettlement tExtVehicleSettlement = extVehicleSettlementMapper.getByCarrierOrderId(carrierOrderId);
        if (tExtVehicleSettlement != null && VehicleSettlementStatusEnum.PAYMENT.getKey().equals(tExtVehicleSettlement.getStatus())) {
            flag = true;
        }
        return flag;
    }
}
