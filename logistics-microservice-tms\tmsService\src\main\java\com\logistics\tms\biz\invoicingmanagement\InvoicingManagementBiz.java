package com.logistics.tms.biz.invoicingmanagement;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.constant.ConfigKeyConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.invoicingmanagement.model.GetInvoiceAmountModel;
import com.logistics.tms.biz.invoicingmanagement.model.GetReconciliationFeeModel;
import com.logistics.tms.biz.settlestatement.model.CarrierSettleStatementDetailModel;
import com.logistics.tms.biz.settlestatement.model.CarrierSettleStatementOrderDetailModel;
import com.logistics.tms.controller.invoicingmanagement.request.*;
import com.logistics.tms.controller.invoicingmanagement.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2024/3/21 11:28
 */
@Service
public class InvoicingManagementBiz {

    @Resource
    private TInvoicingManagementMapper tInvoicingManagementMapper;
    @Resource
    private TInvoicingSettleStatementMapper tInvoicingSettleStatementMapper;
    @Resource
    private TInvoicingInvoiceMapper tInvoicingInvoiceMapper;
    @Resource
    private TSettleStatementMapper tSettleStatementMapper;
    @Resource
    private TInvoicingArchiveAttachmentMapper tInvoicingArchiveAttachmentMapper;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private ConfigKeyConstant configKeyConstant;

    /**
     * 发票管理-列表
     */
    public PageInfo<SearchInvoicingManagementListResponseModel> searchList(SearchInvoicingManagementListRequestModel requestModel){
        //分页查询列表
        requestModel.enablePaging();
        List<SearchInvoicingManagementListResponseModel> list = tInvoicingManagementMapper.searchList(requestModel, requestModel.getBusinessType());
        if (ListUtils.isNotEmpty(list)){
            List<Long> invoicingIdList = list.stream().map(SearchInvoicingManagementListResponseModel::getInvoicingId).collect(Collectors.toList());

            //查询发票金额
            Map<Long, GetInvoiceAmountModel> invoiceAmountMap = tInvoicingInvoiceMapper.getInvoiceAmountByInvoicingIds(invoicingIdList);

            //查询对账金额
            Map<Long, GetReconciliationFeeModel> reconciliationFeeMap = tInvoicingSettleStatementMapper.getReconciliationFeeByInvoicingIds(invoicingIdList);

            //拼接数据
            GetInvoiceAmountModel invoiceAmountModel;
            GetReconciliationFeeModel reconciliationFeeModel;
            for (SearchInvoicingManagementListResponseModel model : list) {
                //发票金额
                invoiceAmountModel = invoiceAmountMap.get(model.getInvoicingId());
                if (invoiceAmountModel != null){
                    model.setInvoiceAmount(invoiceAmountModel.getInvoiceAmount());
                }

                //对账金额
                reconciliationFeeModel = reconciliationFeeMap.get(model.getInvoicingId());
                if (reconciliationFeeModel != null){
                    model.setReconciliationFee(reconciliationFeeModel.getReconciliationFee());
                }
            }
        }
        return new PageInfo<>(list);
    }

    /**
     * 发票管理-查看发票
     */
    public List<GetInvoicePicturesResponseModel> getInvoicePictures(InvoicingManagementDetailRequestModel requestModel){
        //查询发票管理是否存在
        TInvoicingManagement tInvoicingManagement = getInvoicingByIdAndType(requestModel.getInvoicingId(), requestModel.getBusinessType());
        if (tInvoicingManagement == null) {
            return new ArrayList<>();
        }

        //查询发票管理下的发票信息
        List<TInvoicingInvoice> list = tInvoicingInvoiceMapper.getByInvoicingIds(Collections.singletonList(requestModel.getInvoicingId()));
        return MapperUtils.mapper(list, GetInvoicePicturesResponseModel.class);
    }

    /**
     * 发票管理-修改业务名称
     * @param requestModel
     */
    @Transactional
    public void updateBusinessName(UpdateBusinessNameRequestModel requestModel){
        //查询发票管理信息
        TInvoicingManagement dbInvoicingManagement = getInvoicingManagement(requestModel.getInvoicingId(), requestModel.getBusinessType());

        //更新业务名称
        if (!dbInvoicingManagement.getBusinessName().equals(requestModel.getBusinessName())) {
            TInvoicingManagement upInvoicingManagement = new TInvoicingManagement();
            upInvoicingManagement.setId(dbInvoicingManagement.getId());
            upInvoicingManagement.setBusinessName(requestModel.getBusinessName());
            commonBiz.setBaseEntityModify(upInvoicingManagement, BaseContextHandler.getUserName());
            tInvoicingManagementMapper.updateByPrimaryKeySelectiveEncrypt(upInvoicingManagement);
        }
    }

    /**
     * 发票管理-详情-头部信息
     */
    public InvoicingManagementDetailResponseModel getDetail(InvoicingManagementDetailRequestModel requestModel){
        return tInvoicingManagementMapper.getDetail(requestModel.getInvoicingId(), requestModel.getBusinessType());
    }

    /**
     * 发票管理-详情-发票列表
     */
    public List<GetInvoiceListResponseModel> getInvoiceList(InvoicingManagementDetailRequestModel requestModel) {
        //查询发票管理是否存在
        TInvoicingManagement tInvoicingManagement = getInvoicingByIdAndType(requestModel.getInvoicingId(), requestModel.getBusinessType());
        if (tInvoicingManagement == null) {
            return new ArrayList<>();
        }

        List<TInvoicingInvoice> existInvoices = tInvoicingInvoiceMapper.getByInvoicingIds(List.of(requestModel.getInvoicingId()));
        List<String> invoicePictures = existInvoices.stream().map(TInvoicingInvoice::getInvoicePicture).collect(Collectors.toList());
        Map<String, String> OSSFileUrlMap = commonBiz.batchGetOSSFileUrl(invoicePictures);

        List<GetInvoiceListResponseModel> result = new ArrayList<>();
        for (TInvoicingInvoice existInvoice : existInvoices) {
            GetInvoiceListResponseModel responseModel = MapperUtils.mapper(existInvoice, GetInvoiceListResponseModel.class);
            responseModel.setInvoiceId(existInvoice.getId());
            responseModel.setInvoiceTypeLabel(InvoiceTypeEnum.getEnum(existInvoice.getInvoiceType()).getValue());
            responseModel.setInvoicePictureUrl(configKeyConstant.fileAccessAddress + OSSFileUrlMap.get(existInvoice.getInvoicePicture()));
            result.add(responseModel);
        }
        return result.stream()
                .sorted(Comparator.comparing(GetInvoiceListResponseModel::getLastModifiedTime).reversed())
                .collect(Collectors.toList());
    }

    //获取发票管理信息
    private TInvoicingManagement getInvoicingManagement(Long invoicingId, Integer businessType){
        TInvoicingManagement dbInvoicingManagement = getInvoicingByIdAndType(invoicingId, businessType);
        if (dbInvoicingManagement == null){
            throw new BizException(CarrierDataExceptionEnum.INVOICING_MANAGEMENT_NOT_EXIST);
        }
        return dbInvoicingManagement;
    }
    //获取发票管理信息
    private TInvoicingManagement getInvoicingByIdAndType(Long invoicingId, Integer businessType){
        return tInvoicingManagementMapper.getByIdAndType(invoicingId, businessType);
    }

    /**
     * 发票管理-详情-发票列表-查看详情
     */
    public GetInvoiceDetailResponseModel getInvoiceDetail(GetInvoiceDetailRequestModel requestModel){
        //查询发票信息
        TInvoicingInvoice dbInvoicingInvoice = tInvoicingInvoiceMapper.selectByPrimaryKey(requestModel.getInvoiceId());
        if (dbInvoicingInvoice == null || IfValidEnum.INVALID.getKey().equals(dbInvoicingInvoice.getValid())) {
            return new GetInvoiceDetailResponseModel();
        }

        //校验发票管理信息是否存在
        getInvoicingManagement(dbInvoicingInvoice.getInvoicingId(), requestModel.getBusinessType());

        GetInvoiceDetailResponseModel responseModel = MapperUtils.mapper(dbInvoicingInvoice, GetInvoiceDetailResponseModel.class);
        responseModel.setInvoiceTypeLabel(InvoiceTypeEnum.getEnum(dbInvoicingInvoice.getInvoiceType()).getValue());
        Map<String, String> OSSFileUrlMap = commonBiz.batchGetOSSFileUrl(List.of(responseModel.getInvoicePicture()));
        responseModel.setInvoicePictureUrl(configKeyConstant.fileAccessAddress + OSSFileUrlMap.get(responseModel.getInvoicePicture()));
        return responseModel;
    }

    /**
     * 发票管理-详情-发票列表-新增/编辑发票
     */
    @Transactional
    public void addOrModifyInvoice(AddOrModifyInvoiceRequestModel requestModel) {
        //校验发票管理信息是否存在
        getInvoicingManagement(requestModel.getInvoicingId(), requestModel.getBusinessType());

        //唯一校验
        TInvoicingInvoice existInvoicingInvoice;
        if (StrUtil.isNotBlank(requestModel.getInvoiceCode()) || StrUtil.isNotBlank(requestModel.getInvoiceNum())) {
            existInvoicingInvoice = tInvoicingInvoiceMapper
                    .getOneByInvoiceCodeAndInvoiceNumAndInvoicingId(requestModel.getInvoiceCode(), requestModel.getInvoiceNum(), requestModel.getInvoicingId());
            if (existInvoicingInvoice != null && !existInvoicingInvoice.getId().equals(requestModel.getInvoiceId())) {
                throw new BizException(CarrierDataExceptionEnum.INVOICE_CODE_NUM_NOT_ONLY_ONE);
            }
        }
        if (null == requestModel.getInvoiceId()) {
            addInvoice(requestModel);
        } else {
            updateInvoice(requestModel);
        }
    }

    /**
     * 新增发票
     *
     * @param requestModel
     */
    private void addInvoice(AddOrModifyInvoiceRequestModel requestModel) {
        List<TInvoicingInvoice> existInvoices = tInvoicingInvoiceMapper.getByInvoicingIds(List.of(requestModel.getInvoicingId()));
        if(existInvoices.size()>= CommonConstant.INVOICING_MAX_SIZE){
            throw new BizException(CarrierDataExceptionEnum.INVOICING_ADD_MAX);
        }
        TInvoicingInvoice invoicingInvoice = MapperUtils.mapper(requestModel, TInvoicingInvoice.class);
        //上传图片
        invoicingInvoice.setInvoicePicture(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.INVOICE_IMAGE.getKey(),
                "", requestModel.getInvoicePicture(), null));
        commonBiz.setBaseEntityAdd(invoicingInvoice, BaseContextHandler.getUserName());
        tInvoicingInvoiceMapper.insertSelective(invoicingInvoice);
    }

    /**
     * 更新发票
     *
     * @param requestModel
     */
    private void updateInvoice(AddOrModifyInvoiceRequestModel requestModel) {
        TInvoicingInvoice invoicingInvoice = MapperUtils.mapper(requestModel, TInvoicingInvoice.class);
        //查询发票信息是否存在
        TInvoicingInvoice dbInvoicingInvoice = tInvoicingInvoiceMapper.selectByPrimaryKey(requestModel.getInvoiceId());
        if (dbInvoicingInvoice == null || IfValidEnum.INVALID.getKey().equals(dbInvoicingInvoice.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.INVOICE_NOT_EXIST);
        }
        if (!dbInvoicingInvoice.getInvoicePicture().equals(requestModel.getInvoicePicture())) {
            invoicingInvoice.setInvoicePicture(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.INVOICE_IMAGE.getKey(),
                    "", requestModel.getInvoicePicture(), null));
        }
        invoicingInvoice.setId(dbInvoicingInvoice.getId());
        tInvoicingInvoiceMapper.updateByPrimaryKeySelective(invoicingInvoice);
    }

    /**
     * 发票管理-详情-发票列表-删除发票
     */
    @Transactional
    public void delInvoice(GetInvoiceDetailRequestModel requestModel){
        //查询发票信息是否存在
        TInvoicingInvoice dbInvoicingInvoice = tInvoicingInvoiceMapper.selectByPrimaryKey(requestModel.getInvoiceId());
        if (dbInvoicingInvoice == null || IfValidEnum.INVALID.getKey().equals(dbInvoicingInvoice.getValid())){
            throw new BizException(CarrierDataExceptionEnum.INVOICE_NOT_EXIST);
        }

        //校验发票管理信息是否存在
        getInvoicingManagement(dbInvoicingInvoice.getInvoicingId(), requestModel.getBusinessType());

        //删除发票
        TInvoicingInvoice delInvoicingInvoice = new TInvoicingInvoice();
        delInvoicingInvoice.setId(dbInvoicingInvoice.getId());
        delInvoicingInvoice.setValid(IfValidEnum.INVALID.getKey());
        commonBiz.setBaseEntityModify(delInvoicingInvoice, BaseContextHandler.getUserName());
        tInvoicingInvoiceMapper.updateByPrimaryKeySelective(delInvoicingInvoice);
    }

    /**
     * 发票管理-详情-对账单列表
     */
    public PageInfo<GetSettleStatementListResponseModel> getSettleStatementList(GetSettleStatementListRequestModel requestModel){
        //查询发票管理信息
        getInvoicingManagement(requestModel.getInvoicingId(), requestModel.getBusinessType());

        //分页查询对账单信息
        requestModel.enablePaging();
        List<Long> invoicingSettleStatementIdList = tInvoicingSettleStatementMapper.getSettleStatementIdList(requestModel);
        PageInfo pageInfo = new PageInfo<>(invoicingSettleStatementIdList);
        if (ListUtils.isNotEmpty(invoicingSettleStatementIdList)){
            List<GetSettleStatementListResponseModel> settleStatementList = tInvoicingSettleStatementMapper.getSettleStatementList(invoicingSettleStatementIdList);
            pageInfo.setList(settleStatementList);
        }
        return pageInfo;
    }

    /**
     * 发票管理-详情-对账单-添加对账单列表
     */
    public PageInfo<GetAddSettleStatementListResponseModel> getAddSettleStatementList(GetAddSettleStatementListRequestModel requestModel){
        //分页查询对账单信息（同一车主、已对账、未关联发票的）
        requestModel.enablePaging();
        List<Long> settleStatementIdList = tSettleStatementMapper.getAddSettleStatementIdList(requestModel, requestModel.getBusinessType());
        PageInfo pageInfo = new PageInfo<>(settleStatementIdList);
        if (ListUtils.isNotEmpty(settleStatementIdList)){
            List<GetAddSettleStatementListResponseModel> settleStatementList = tSettleStatementMapper.getAddSettleStatementList(StringUtils.listToString(settleStatementIdList,','));
            pageInfo.setList(settleStatementList);
        }
        return pageInfo;
    }

    /**
     * 发票管理-详情-对账单-添加对账单
     */
    @Transactional
    public void addSettleStatement(AddInvoicingSettleStatementRequestModel requestModel){
        //查询发票管理信息
        TInvoicingManagement dbInvoicingManagement = getInvoicingManagement(requestModel.getInvoicingId(), requestModel.getBusinessType());

        //查询对账单信息
        List<CarrierSettleStatementDetailModel> settleStatementDetailList = tSettleStatementMapper.getDetailByIds(StringUtils.listToString(requestModel.getSettleStatementIdList(),','), dbInvoicingManagement.getBusinessType());
        if (ListUtils.isEmpty(settleStatementDetailList)){
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }

        //校验对账单是否满足条件
        for (CarrierSettleStatementDetailModel model : settleStatementDetailList) {
            //非已对账，不能操作
            if (!SettleStatementStatusEnum.ACCOUNT_CHECKED.getKey().equals(model.getSettleStatementStatus())) {
                throw new BizException(CarrierDataExceptionEnum.APPLY_INVOICING_STATE_ERROR);
            }
            //非未关联发票
            if (CommonConstant.INTEGER_ONE.equals(model.getIfInvoice())){
                throw new BizException(CarrierDataExceptionEnum.APPLY_INVOICING_STATE_ERROR);
            }

            //校验车主是否与发票管理车主一致
            CarrierSettleStatementOrderDetailModel orderDetailModel = model.getSettleStatementItemList().stream().filter(item -> !item.getCompanyCarrierId().equals(dbInvoicingManagement.getCompanyCarrierId())).findFirst().orElse(null);
            if (orderDetailModel != null){
                throw new BizException(CarrierDataExceptionEnum.APPLY_INVOICING_STATE_ERROR);
            }
        }

        //查询发票管理下已关联的对账单
        List<TInvoicingSettleStatement> dbInvoicingSettleStatementList = tInvoicingSettleStatementMapper.getByInvoicingIds(Collections.singletonList(dbInvoicingManagement.getId()));
        //对账单总数量不能超过50条
        if (dbInvoicingSettleStatementList.size() + settleStatementDetailList.size() > CommonConstant.INT_FIFTY){
            throw new BizException(CarrierDataExceptionEnum.INVOICING_ADD_MAX);
        }

        //组装对账单信息
        TInvoicingSettleStatement addInvoicingSettleStatement;
        List<TInvoicingSettleStatement> addInvoicingSettleStatementList = new ArrayList<>();
        TSettleStatement upSettleStatement;
        List<TSettleStatement> upSettleStatementList = new ArrayList<>();
        for (CarrierSettleStatementDetailModel model : settleStatementDetailList) {
            addInvoicingSettleStatement = new TInvoicingSettleStatement();
            addInvoicingSettleStatement.setInvoicingId(dbInvoicingManagement.getId());
            addInvoicingSettleStatement.setSettleStatementId(model.getSettleStatementId());
            commonBiz.setBaseEntityAdd(addInvoicingSettleStatement, BaseContextHandler.getUserName());
            addInvoicingSettleStatementList.add(addInvoicingSettleStatement);

            //更新对账单【关联开票】字段
            upSettleStatement = new TSettleStatement();
            upSettleStatement.setId(model.getSettleStatementId());
            upSettleStatement.setIfInvoice(CommonConstant.INTEGER_ONE);
            commonBiz.setBaseEntityModify(upSettleStatement, BaseContextHandler.getUserName());
            upSettleStatementList.add(upSettleStatement);
        }
        //新增发票管理-对账单信息
        if (ListUtils.isNotEmpty(addInvoicingSettleStatementList)){
            tInvoicingSettleStatementMapper.batchInsert(addInvoicingSettleStatementList);
        }
        //更新对账单【关联开票】字段
        if (ListUtils.isNotEmpty(upSettleStatementList)){
            tSettleStatementMapper.batchUpdateSelective(upSettleStatementList);
        }
    }

    /**
     * 发票管理-详情-对账单-移除对账单
     */
    @Transactional
    public void delSettleStatement(DelSettleStatementRequestModel requestModel){
        //查询对账单关联信息是否存在
        TInvoicingSettleStatement dbInvoicingSettleStatement = tInvoicingSettleStatementMapper.selectByPrimaryKey(requestModel.getInvoicingSettleStatementId());
        if (dbInvoicingSettleStatement == null || IfValidEnum.INVALID.getKey().equals(dbInvoicingSettleStatement.getValid())){
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }

        //校验发票管理信息是否存在
        getInvoicingManagement(dbInvoicingSettleStatement.getInvoicingId(), requestModel.getBusinessType());

        //删除对账单关联信息
        TInvoicingSettleStatement delInvoicingSettleStatement = new TInvoicingSettleStatement();
        delInvoicingSettleStatement.setId(dbInvoicingSettleStatement.getId());
        delInvoicingSettleStatement.setValid(IfValidEnum.INVALID.getKey());
        commonBiz.setBaseEntityModify(delInvoicingSettleStatement, BaseContextHandler.getUserName());
        tInvoicingSettleStatementMapper.updateByPrimaryKeySelective(delInvoicingSettleStatement);

        //更新对账单【关联开票】字段
        TSettleStatement upSettleStatement = new TSettleStatement();
        upSettleStatement.setId(dbInvoicingSettleStatement.getSettleStatementId());
        upSettleStatement.setIfInvoice(CommonConstant.INTEGER_ZERO);
        commonBiz.setBaseEntityModify(upSettleStatement, BaseContextHandler.getUserName());
        tSettleStatementMapper.updateByPrimaryKeySelective(upSettleStatement);
    }

    /**
     * 发票管理-详情-归档列表
     * @param requestModel
     * @return
     */
    public List<GetInvoicingArchiveListResponseModel> getInvoicingArchiveList(InvoicingManagementDetailRequestModel requestModel){
        //查询发票管理信息
        TInvoicingManagement dbInvoicingManagement = getInvoicingManagement(requestModel.getInvoicingId(), requestModel.getBusinessType());
        return tInvoicingArchiveAttachmentMapper.getInvoicingArchiveList(dbInvoicingManagement.getId());
    }

    /**
     * 发票管理-详情-确认归档
     * @param requestModel
     */
    @Transactional
    public void invoicingArchive(InvoicingArchiveRequestModel requestModel){
        //查询发票管理信息
        TInvoicingManagement dbInvoicingManagement = getInvoicingManagement(requestModel.getInvoicingId(), requestModel.getBusinessType());

        //查询已存在的归档文件
        List<GetInvoicingArchiveListResponseModel> existList = tInvoicingArchiveAttachmentMapper.getInvoicingArchiveList(dbInvoicingManagement.getId());
        if (existList.size() + requestModel.getTmpUrl().size() > CommonConstant.INTEGER_SIX){
            throw new BizException(CarrierDataExceptionEnum.INVOICING_MANAGEMENT_ARCHIVED_FILE_MAX);
        }

        //新增归档文件
        TInvoicingArchiveAttachment addInvoicingArchiveAttachment;
        List<TInvoicingArchiveAttachment> addInvoicingArchiveAttachmentList = new ArrayList<>();
        for (String path : requestModel.getTmpUrl()) {
            addInvoicingArchiveAttachment = new TInvoicingArchiveAttachment();
            addInvoicingArchiveAttachment.setInvoicingId(dbInvoicingManagement.getId());
            addInvoicingArchiveAttachment.setImagePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.INVOICING_MANAGEMENT_ARCHIVED_FILE.getKey(), "", path, null));
            commonBiz.setBaseEntityAdd(addInvoicingArchiveAttachment, BaseContextHandler.getUserName());
            addInvoicingArchiveAttachmentList.add(addInvoicingArchiveAttachment);
        }
        if (ListUtils.isNotEmpty(addInvoicingArchiveAttachmentList)){
            tInvoicingArchiveAttachmentMapper.batchInsert(addInvoicingArchiveAttachmentList);
        }
    }

}
