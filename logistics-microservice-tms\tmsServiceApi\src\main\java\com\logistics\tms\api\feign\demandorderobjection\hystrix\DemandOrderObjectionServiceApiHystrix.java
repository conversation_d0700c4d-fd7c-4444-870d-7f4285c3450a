package com.logistics.tms.api.feign.demandorderobjection.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.demandorderobjection.DemandOrderObjectionServiceApi;
import com.logistics.tms.api.feign.demandorderobjection.model.SearchDemandOrderObjectionRequestModel;
import com.logistics.tms.api.feign.demandorderobjection.model.SearchDemandOrderObjectionResponseModel;
import org.springframework.stereotype.Component;

@Component("tmsDemandOrderObjectionServiceApiHystrix")
public class DemandOrderObjectionServiceApiHystrix implements DemandOrderObjectionServiceApi {
    @Override
    public Result<PageInfo<SearchDemandOrderObjectionResponseModel>> searchDemandOrderObjection(SearchDemandOrderObjectionRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<SearchDemandOrderObjectionResponseModel>> exportDemandOrderObjection(SearchDemandOrderObjectionRequestModel requestModel) {
        return Result.timeout();
    }
}
