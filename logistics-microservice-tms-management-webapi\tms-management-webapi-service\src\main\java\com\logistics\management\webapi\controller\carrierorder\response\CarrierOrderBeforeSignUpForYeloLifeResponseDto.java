package com.logistics.management.webapi.controller.carrierorder.response;

import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/7/22 9:56
 */
@Data
public class CarrierOrderBeforeSignUpForYeloLifeResponseDto {

    /**
     * 运单ID
     */
    private String carrierOrderId = "";

    /**
     * 运单号
     */
    private String carrierOrderCode = "";

    /**
     * 委托类型：100 新生回收，101 新生销售
     */
    private String entrustType = "";

    /**
     * 车主
     */
    private String carrierCompany = "";

    /**
     * 提货地
     */
    private String loadCityName = "";

    /**
     * 卸货地
     */
    private String unloadCityName = "";

    /**
     * 货物单位
     */
    private String goodsUnit = "";

    /**
     * 数量
     */
    private String amountTotal = "";

    /**
     * 司机运费合计(元)
     */
    private String dispatchFreightFeeTotal = "";

    /**
     * 实际货主费用(元)
     */
    private String actualEntrustFee = "";

    /**
     * 车主运费金额(元)
     */
    private String carrierFreight = "";

    /**
     * 实际车主运费(元)
     */
    private String signCarrierFreight = "";

    /**
     * 是否我司: 1:我司 2:其他车主
     */
    private String isOurCompany = "";

}
