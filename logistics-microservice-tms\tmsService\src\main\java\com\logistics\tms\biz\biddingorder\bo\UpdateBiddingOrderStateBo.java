package com.logistics.tms.biz.biddingorder.bo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/05/06
 */
@Data
public class UpdateBiddingOrderStateBo {
    /**
     * 竞价单
     */
    private Long biddingOrderId;
    /**
     *更新状态
     */
    private Integer targetState;
    /**
     *原始状态
     */
    private List<Integer> sourceState;

    /**
     *更新人
     */
    private String lastModifiedBy;
    /**
     *更新时间
     */
    private Date lastModifiedTime;
}
