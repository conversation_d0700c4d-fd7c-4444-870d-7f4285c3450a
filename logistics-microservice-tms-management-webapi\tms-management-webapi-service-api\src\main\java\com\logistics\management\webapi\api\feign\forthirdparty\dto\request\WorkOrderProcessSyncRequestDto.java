package com.logistics.management.webapi.api.feign.forthirdparty.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class WorkOrderProcessSyncRequestDto {

    @ApiModelProperty(value = "工单编号")
    @NotBlank(message = "交互单号不允许为空")
    private String code;

    @ApiModelProperty(value = "状态; 10 处理中，20 已处理，30 已关闭")
    @NotBlank(message = "处理状态不允许为空")
    private String status;

    @ApiModelProperty(value = "处理人")
    @NotBlank(message = "处理人不允许为空")
    private String solveUserName;

    @ApiModelProperty(value = "处理时间")
    @NotBlank(message = "处理时间不允许为空")
    private String solveTime;

    @ApiModelProperty(value = "处理备注")
    private String solveRemark;

}
