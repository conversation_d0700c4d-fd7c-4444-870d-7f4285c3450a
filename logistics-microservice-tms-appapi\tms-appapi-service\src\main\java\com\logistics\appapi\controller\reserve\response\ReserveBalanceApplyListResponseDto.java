package com.logistics.appapi.controller.reserve.response;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.controller.reserve.response.ReserveBalanceApplyListItemDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Data
public class ReserveBalanceApplyListResponseDto {

	@ApiModelProperty("申请次数")
	private String applyCount = "";

	@ApiModelProperty("批准次数")
	private String approveCount = "";

	@ApiModelProperty("批准金额")
	private String approveAmount = "";

	@ApiModelProperty("申请记录")
	private PageInfo<ReserveBalanceApplyListItemDto> applyList;
}
