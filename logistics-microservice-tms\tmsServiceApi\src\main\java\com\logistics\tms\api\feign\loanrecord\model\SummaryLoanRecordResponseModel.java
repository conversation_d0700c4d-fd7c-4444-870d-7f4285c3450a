package com.logistics.tms.api.feign.loanrecord.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/9/30 9:38
 */
@Data
public class SummaryLoanRecordResponseModel {
    @ApiModelProperty("待结算")
    private Integer waitCount;
    @ApiModelProperty("部分结算")
    private Integer partCount;
    @ApiModelProperty("已结算")
    private Integer hasCount;
    @ApiModelProperty("所有数量")
    private Integer allCount;
}
