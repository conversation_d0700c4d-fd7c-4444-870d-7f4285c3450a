package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TSafetyGroupMeetingAttachment extends BaseEntity {
    /**
    * 安全会议表iD
    */
    @ApiModelProperty("安全会议表iD")
    private Long safetyGroupMeetingId;

    /**
    * 类型：1 现场图片，2 签到图片
    */
    @ApiModelProperty("类型：1 现场图片，2 签到图片")
    private Integer type;

    /**
    * 会议图片路径
    */
    @ApiModelProperty("会议图片路径")
    private String meetingImagePath;
}