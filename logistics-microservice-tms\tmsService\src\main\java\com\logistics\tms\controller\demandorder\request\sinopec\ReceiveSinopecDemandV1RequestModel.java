package com.logistics.tms.controller.demandorder.request.sinopec;

import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 * @date ：Created in 2022/12/12
 */
@Data
public class ReceiveSinopecDemandV1RequestModel {

	//去重用
	private Long originalDataId;

	@ApiModelProperty("租户ID 1034")
	private Long customerId;

	@ApiModelProperty("租户名称 中国石化化工销售有限公司")
	private String customerName;

	@ApiModelProperty("是否网货 0-否 1-是")
	private Integer onlineGoodsFlag;

	@ApiModelProperty("委托单号 YS2614840539AB")
	private String associatedNumber;

	@ApiModelProperty("托运人编码 HG01")
	private String consignorCode;

	@ApiModelProperty("托运人名称（货主） 化销华东分公司")
	private String consignorName;

	@ApiModelProperty("承运商编码")
	private String carrierCode;

	@ApiModelProperty("承运商名称（车主） ")
	private String carrierName;

	@ApiModelProperty("委托日期 ")
	private Long associatedDate;

	@ApiModelProperty("送达期限 ")
	private Long arrivalDeadline;

	@ApiModelProperty("短驳次数 0-不短驳，1，2")
	private Integer shortSplitCount;

	@ApiModelProperty("运输方式 1-铁路 2-公路3-管道 4-水路 5-航空")
	private Integer transportType;

	@ApiModelProperty("运输方式备注名称 ")
	private String transportTypeRemark;

	@ApiModelProperty("委托总量")
	private BigDecimal associatedCount;

	@ApiModelProperty("是否包含危化品 0-不包含1-包含")
	private Integer hasHazardousChemicals;

	@ApiModelProperty("委托单备注")
	private String associatedRemark;

	@ApiModelProperty("计划包装数,计划共享托盘数")
	private Integer planPackageQuantity;

	@ApiModelProperty("预估费用 ")
	private BigDecimal estimateCost;

	@ApiModelProperty("货运单元信息")
	private List<SinopecFreightUnitModel> freightUnit;

	@ApiModelProperty("中转港/站信息")
	private List<SinopecTransitPortLocationModel> transitPortLocation;

	@ApiModelProperty("审核人姓名")
	private String auditUser;

	@ApiModelProperty("是否是国外运输 0-否，国内运输 1-是，国外运输")
	private Integer isAbroad;

	@ApiModelProperty("最早装期")
	private Long earliestLoadTime;

	@ApiModelProperty("最晚装期")
	private Long latestLoadTime;

	@ApiModelProperty("唛头数量")
	private Integer marksQuantity;

	@ApiModelProperty("唛头金额")
	private BigDecimal marksAmount;

	@ApiModelProperty("打托数量")
	private Integer trayQuantity;

	@ApiModelProperty("打托金额")
	private BigDecimal trayAmount;

	@ApiModelProperty("附件信息")
	private SinopecAnnexInfoModel annexInfo;

	@ApiModelProperty("内外贸 1-內贸 2-外贸")
	private Integer tradePositionType;

	@ApiModelProperty("贸易类型 1-合同贸易 2-计划贸易")
	private String tradeType;

	@ApiModelProperty("交接方式")
	private String handoverName;

	@ApiModelProperty("起运国/地区")
	private String originCountry;

	@ApiModelProperty("起运国/地区编码")
	private String originCountryCode;

	@ApiModelProperty("送达国/地区")
	private String destinationCountry;

	@ApiModelProperty("送达国/地区编码")
	private String destinationCountryCode;

	@ApiModelProperty("申报口岸")
	private SinopecDeclarationPortModel declarationPort;

	@ApiModelProperty("预留字段")
	private String remark1;

	@ApiModelProperty("预留字段")
	private String remark2;

	@ApiModelProperty("预留字段")
	private String remark3;

	@ApiModelProperty("预留字段")
	private String remark4;

	@ApiModelProperty("预留字段")
	private String remark5;

	@ApiModelProperty("预留字段")
	private String remark6;

	@ApiModelProperty("预留字段")
	private String remark7;

	@ApiModelProperty("预留字段")
	private String remark8;

	@ApiModelProperty("预留字段")
	private String remark9;

	@ApiModelProperty("预留字段")
	private String remark10;

	@ApiModelProperty("行项目信息")
	private List<SinopecDemandListModel> list;

	/**
	 * 校验参数
	 *
	 * @return 校验结果
	 */
	public String check() {
		if (this.customerId == null) {
			return "租户id不能为空";
		}
		if (StringUtils.isBlank(this.customerName)) {
			return "租户名称不能为空";
		}
		if (this.onlineGoodsFlag == null) {
			return "是否网货不能为空";
		}
		if (StringUtils.isBlank(this.associatedNumber)) {
			return "委托单号不能为空";
		}
		if (StringUtils.isBlank(this.consignorCode)) {
			return "托运人编码不能为空";
		}
		if (StringUtils.isBlank(this.consignorName)) {
			return "托运人名称不能为空";
		}
		if (StringUtils.isBlank(this.carrierCode)) {
			return "承运商编码不能为空";
		}
		if (StringUtils.isBlank(this.carrierName)) {
			return "承运商名称不能为空";
		}
		if (this.associatedDate == null) {
			return "委托日期不能为空";
		}
		if (this.arrivalDeadline == null) {
			return "送达期限不能为空";
		}
		if (this.shortSplitCount == null) {
			return "短驳次数不能为空";
		}
		if (this.transportType == null) {
			return "运输方式不能为空";
		}
		if (this.associatedCount == null) {
			return "委托总量不能为空";
		}
		if (this.hasHazardousChemicals == null) {
			return "是否包含危化品不能为空";
		}
		if (StringUtils.isBlank(this.auditUser)) {
			return "审核人姓名不能为空";
		}
		if (this.tradePositionType == null) {
			return "内外贸不能为空";
		}
		if (StringUtils.isBlank(this.tradeType)) {
			return "贸易类型不能为空";
		}
		if (StringUtils.isBlank(this.handoverName)) {
			return "交接方式不能为空";
		}
		if (ListUtils.isEmpty(this.list)) {
			return "行项目信息不能为空";
		} else {
			List<SinopecDemandListModel> itemInfoList = this.list;
			//只校验第一条
			SinopecDemandListModel sinopecDemandListModel = itemInfoList.get(0);
			String checkMsg = sinopecDemandListModel.check();
			if (StringUtils.isNotBlank(checkMsg)) {
				return checkMsg;
			}
		}

		//长度校验
		if (this.customerName.length() > 255) {
			return "租户名称长度不能大于255个字符";
		}
		if (this.onlineGoodsFlag < 0 || this.onlineGoodsFlag > 1) {
			return "是否网货只能是1或0";
		}
		if (this.associatedNumber.length() > 64) {
			return "委托单号长度不能大于64个字符";
		}
		if (this.consignorCode.length() > 255) {
			return "托运人编码长度不能大于255个字符";
		}
		if (this.consignorName.length() > 255) {
			return "托运人名称长度不能大于255个字符";
		}
		if (this.carrierCode.length() > 64) {
			return "承运商编码长度不能大于64个字符";
		}
		if (this.carrierName.length() > 255) {
			return "承运商名称长度不能大于255个字符";
		}
		if (this.transportType < 1 || this.transportType > 5) {
			return "运输方式只能是1-5";
		}
		if (this.transportTypeRemark != null && this.transportTypeRemark.length() > 64) {
			return "运输方式备注名称不能大于64个字符";
		}
		if (this.hasHazardousChemicals < 0 || this.hasHazardousChemicals > 1) {
			return "是否包含危化品只能是0或1";
		}
		if (this.associatedRemark != null && this.associatedRemark.length() > 255) {
			return "委托单备注不能大于255个字符";
		}
		if (this.auditUser.length() > 16) {
			return "审核人姓名长度不能超过16个字符";
		}
		if (this.isAbroad != null && (this.isAbroad < 0 || this.isAbroad > 1)) {
			return "是否是国外运输只能是0或1";
		}
		if (this.tradePositionType < 1 || this.tradePositionType > 2) {
			return "内外贸只能是1或2";
		}
		if (this.tradeType.length() > 64) {
			return "贸易类型长度不能大于64个字符";
		}
		if (this.handoverName.length() > 64) {
			return "交接方式长度不能大于64个字符";
		}
		if (this.originCountry != null && this.originCountry.length() > 64) {
			return "起运国/地区长度不能大于64个字符";
		}
		if (this.originCountryCode != null && this.originCountryCode.length() > 64) {
			return "起运国/地区编码长度不能大于64个字符";
		}
		if (this.destinationCountry != null && this.destinationCountry.length() > 64) {
			return "送达国/地区长度不能大于64个字符";
		}
		if (this.destinationCountryCode != null && this.destinationCountryCode.length() > 64) {
			return "送达国/地区编码长度不能大于64个字符";
		}
		if (this.freightUnit != null) {
			for (SinopecFreightUnitModel sinopecFreightUnitModel : this.freightUnit) {
				if (sinopecFreightUnitModel.getName() != null && sinopecFreightUnitModel.getName().length() > 100) {
					return "货运单元名称长度不能大于100个字符";
				}
				if (sinopecFreightUnitModel.getType() != null && sinopecFreightUnitModel.getType().length() > 20) {
					return "货运单元编码长度不能大于20个字符";
				}
			}
		}
		if (this.transitPortLocation != null) {
			for (SinopecTransitPortLocationModel transitPortLocationModel : this.transitPortLocation) {
				if (transitPortLocationModel.getLocationCode() != null && transitPortLocationModel.getLocationCode().length() > 64) {
					return "中转港/站信息位置编码长度不能大于64个字符";
				}
				if (transitPortLocationModel.getLocationName() != null && transitPortLocationModel.getLocationName().length() > 200) {
					return "中转港/站信息位置名称长度不能大于200个字符";
				}
				if (transitPortLocationModel.getDetailAddress() != null && transitPortLocationModel.getDetailAddress().length() > 200) {
					return "中转港/站信息详细地址长度不能大于200个字符";
				}
			}
		}
		if (ListUtils.isEmpty(this.list)) {
			return "行项目信息不能为空";
		} else {
			for (SinopecDemandListModel sinopecDemandListModel : this.list) {
				String checkMsg = sinopecDemandListModel.check();
				if (StringUtils.isNotBlank(checkMsg)) {
					return checkMsg;
				}
			}
		}
		return null;
	}
}
