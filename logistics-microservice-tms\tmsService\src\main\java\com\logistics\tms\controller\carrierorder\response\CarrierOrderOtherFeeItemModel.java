package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/2
 */
@Data
public class CarrierOrderOtherFeeItemModel {

	@ApiModelProperty("运单ID")
	private Long carrierOrderId;

	@ApiModelProperty("费用类型：1 短驳费，2 保管费，3 装卸费，4 压车费，5 质量处罚费，6 其他杂费")
	private Integer feeType;

	@ApiModelProperty("费用金额")
	private BigDecimal feeAmount;
}
