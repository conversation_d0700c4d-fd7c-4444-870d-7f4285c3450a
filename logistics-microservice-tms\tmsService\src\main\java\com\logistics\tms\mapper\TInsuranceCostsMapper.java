package com.logistics.tms.mapper;

import com.logistics.tms.controller.vehiclesettlement.response.GetInsuranceCostsByVehicleIdResponseModel;
import com.logistics.tms.entity.TInsuranceCosts;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TInsuranceCostsMapper extends BaseMapper<TInsuranceCosts> {

    GetInsuranceCostsByVehicleIdResponseModel getByIdForSettlement(@Param("id") Long id);

    TInsuranceCosts getLastByVehicleId(@Param("vehicleId") Long vehicleId);

    int batchUpdate(@Param("list") List<TInsuranceCosts> list);

    int deleteInsuranceCostAndRelation(@Param("insuranceCostId") Long insuranceCostId, @Param("name")String name);
}