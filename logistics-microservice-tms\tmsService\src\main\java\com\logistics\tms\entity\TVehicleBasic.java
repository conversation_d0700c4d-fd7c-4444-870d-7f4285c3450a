package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2023/06/14
*/
@Data
public class TVehicleBasic extends BaseEntity {
    /**
    * 车辆使用性质 1普货 2危货
    */
    @ApiModelProperty("车辆使用性质 1普货 2危货")
    private Integer usageProperty;

    /**
    * 是否安装GPS 1 是 0 否
    */
    @ApiModelProperty("是否安装GPS 1 是 0 否")
    private Integer ifInstallGps;

    /**
    * 是否入网石化 1 是 0 否
    */
    @ApiModelProperty("是否入网石化 1 是 0 否")
    private Integer ifAccessSinopec;

    /**
    * 车辆机构：1 自主，2 外部，3 自营
    */
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;

    /**
    * 真实所属车主
    */
    @ApiModelProperty("真实所属车主")
    private String vehicleOwner;

    /**
    * 入网时间
    */
    @ApiModelProperty("入网时间")
    private Date connectTime;

    /**
    * 入网方式 1 转发 2 新装
    */
    @ApiModelProperty("入网方式 1 转发 2 新装")
    private Integer connectWay;

    /**
    * 认证期限开始时间
    */
    @ApiModelProperty("认证期限开始时间")
    private Date authenticationStartTime;

    /**
    * 认证期限到期时间
    */
    @ApiModelProperty("认证期限到期时间")
    private Date authenticationExpireTime;

    /**
    * 登记证书编号
    */
    @ApiModelProperty("登记证书编号")
    private String registrationCertificationNumber;

    /**
    * 排放标准类型：1 国一，2 国二，3 国三，4 国四，5 国五，6 国六
    */
    @ApiModelProperty("排放标准类型：1 国一，2 国二，3 国三，4 国四，5 国五，6 国六")
    private Integer emissionStandardType;

    /**
    * 车辆运营状态：1 运营中，2 已停运，3 过户，4 报废
    */
    @ApiModelProperty("车辆运营状态：1 运营中，2 已停运，3 过户，4 报废")
    private Integer operatingState;

    /**
    * 停运原因
    */
    @ApiModelProperty("停运原因")
    private String shutDownReason;

    /**
    * 装载量（可装载托盘数）
    */
    @ApiModelProperty("装载量（可装载托盘数）")
    private Integer loadingCapacity;

    /**
    * 车辆添加来源：1新增 2导入
    */
    @ApiModelProperty("车辆添加来源：1新增 2导入")
    private Integer source;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}