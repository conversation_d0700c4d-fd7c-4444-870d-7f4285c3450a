package com.logistics.management.webapi.controller.dispatch.response;

import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;

@Data
public class SearchCanJoinShippingOrderRespDto {

    /**
     * 零担id
     */
    private String shippingOrderId;

    /**
     * 零担单号
     */
    private String shippingOrderCode;

    /**
     * 车牌号
     */
    private String vehicleNo;


    /**
     * 串点费用
     */
    private String crossPointFee;

    /**
     * 整车运费
     */
    private String carrierFreight;

}
