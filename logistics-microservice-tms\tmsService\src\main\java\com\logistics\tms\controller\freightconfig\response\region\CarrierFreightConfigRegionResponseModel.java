package com.logistics.tms.controller.freightconfig.response.region;

import com.logistics.tms.controller.freightconfig.response.ladder.CarrierFreightConfigPriceDesignResponseModel;
import com.logistics.tms.controller.freightconfig.response.scheme.CarrierFreightConfigSchemeResponseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigRegionResponseModel extends CarrierFreightConfigSchemeResponseModel {

    @ApiModelProperty(value = "价格设计")
    private CarrierFreightConfigPriceDesignResponseModel priceDesign;
}
