package com.logistics.management.webapi.controller.shippingorder.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/8/6 10:04
 */
@Data
@ExcelIgnoreUnannotated
public class SearchShippingOrderListResponseDto {

    /**
     * 运输单id
     */
    private String shippingOrderId="";

    /**
     * 状态：0 待审核，1 已审核，2 已驳回
     */
    private String status="";
    /**
     * 状态
     */
    @ExcelProperty("状态")
    private String statusLabel="";

    /**
     * 运输单号
     */
    @ExcelProperty("零担单号")
    private String shippingOrderCode="";

    /**
     * 调度单id
     */
    private String dispatchOrderId="";
    /**
     * 调度单号
     */
    @ExcelProperty("调度单号")
    private String dispatchOrderCode="";

    /**
     * 多装
     */
    @ExcelProperty("多装")
    private String loadPointAmount="";

    /**
     * 发货省市区
     */
    @ExcelProperty("发货省市区")
    private String loadAddress="";

    /**
     * 收货省市区
     */
    @ExcelProperty("收货省市区")
    private String unloadAddress="";

    /**
     * 预提数
     */
    @ExcelProperty("预提数")
    private String expectAmount="";

    /**
     * 签收数
     */
    @ExcelProperty("签收数")
    private String signAmount="";

    /**
     * 车主
     */
    @ExcelProperty("车主")
    private String companyCarrierName="";

    /**
     * 车长
     */
    @ExcelProperty("车长")
    private String vehicleLength="";

    /**
     * 整车费用
     */
    @ExcelProperty("整车运费")
    private String carrierFreight="";

    /**
     * 串点费用
     */
    @ExcelProperty("串点费用")
    private String crossPointFee="";

    /**
     * 预计车主费用
     */
    @ExcelProperty("预计车主费用")
    private String expectCarrierFreight="";

    /**
     * 实际车主费用
     */
    @ExcelProperty("实际车主费用")
    private String actualCarrierFreight="";

    /**
     * 车牌号
     */
    @ExcelProperty("车牌号")
    private String vehicleNo="";

    /**
     * 司机
     */
    @ExcelProperty("司机")
    private String driver="";

    /**
     * 调度人
     */
    @ExcelProperty("调度人")
    private String dispatchUserName="";

    /**
     * 调度时间
     */
    @ExcelProperty("调度时间")
    private String dispatchTime="";

}
