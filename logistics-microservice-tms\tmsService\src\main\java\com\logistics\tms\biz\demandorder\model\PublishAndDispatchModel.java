package com.logistics.tms.biz.demandorder.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class PublishAndDispatchModel {

    @ApiModelProperty("需求单id")
    private Long demandId;

    @ApiModelProperty("业务类型：1 发货，2 回收，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配")
    private Integer entrustType;

    @ApiModelProperty("车辆id")
    private Long vehicleId;

    @ApiModelProperty("司机id")
    private Long driverId;

    @ApiModelProperty("预计提货时间")
    private Date expectedLoadTime;

    /**
     * 仓库信息
     */
    @ApiModelProperty(value = "仓库ID")
    private Long warehouseId;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "省份Id")
    private Long provinceId;

    @ApiModelProperty(value = "省份名字")
    private String provinceName;

    @ApiModelProperty(value = "城市Id")
    private Long cityId;

    @ApiModelProperty(value = "城市名字")
    private String cityName;

    @ApiModelProperty(value = "县区Id")
    private Long areaId;

    @ApiModelProperty(value = "县区名字")
    private String areaName;

    @ApiModelProperty(value = "详细地址")
    private String detailAddress;

    @ApiModelProperty(value = "联系人名字")
    private String contactName;

    @ApiModelProperty(value = "联系人联系方式")
    private String contactPhone;
}
