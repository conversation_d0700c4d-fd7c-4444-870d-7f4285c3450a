package com.logistics.tms.controller.driversafemeeting.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/11/4 9:13
 */
@Data
public class AddDriverSafeMeetingRequestModel {
    @ApiModelProperty(value = "驾驶员")
    private List<Long> driverIdList;
    @ApiModelProperty(value = "类型")
    private Integer type;
    @ApiModelProperty(value = "学习月份")
    private String period;
    @ApiModelProperty(value = "学习标题")
    private String title;
    @ApiModelProperty(value = "学习简介")
    private String introduction;
    @ApiModelProperty(value = "学习内容")
    private String content;
}
