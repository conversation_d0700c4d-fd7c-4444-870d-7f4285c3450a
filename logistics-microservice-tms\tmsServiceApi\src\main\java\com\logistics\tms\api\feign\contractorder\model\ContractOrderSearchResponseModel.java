package com.logistics.tms.api.feign.contractorder.model;

import lombok.Data;

import java.util.Date;

@Data
public class ContractOrderSearchResponseModel {

    private Integer contractId;

    private String contractNoInternal;

    private String contractNoExternal;

    private Integer contractStatus;

    private Integer contractType;

    private Integer contractNature;

    private String customerName;

    private Date contractStartTime;

    private Date contractEndTime;

    private String lastModifiedBy;

    private Date lastModifiedTime;
}
