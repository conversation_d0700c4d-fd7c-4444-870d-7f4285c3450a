package com.logistics.appapi.controller.carrierorderotherfee;

import com.logistics.appapi.controller.common.CommonBiz;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.constant.ConfigKeyConstant;
import com.logistics.appapi.base.enums.AppApiExceptionEnum;
import com.logistics.appapi.client.carrierorderotherfee.CarrierOrderOtherFeeClient;
import com.logistics.appapi.client.carrierorderotherfee.request.AddCarrierOrderOtherFeeRequestModel;
import com.logistics.appapi.client.carrierorderotherfee.request.CommitCarrierOrderOtherFeeRequestModel;
import com.logistics.appapi.client.carrierorderotherfee.request.GetOtherFeeByCarrierOrderIdRequestModel;
import com.logistics.appapi.client.carrierorderotherfee.response.GetOtherFeeByCarrierOrderIdResponseModel;
import com.logistics.appapi.controller.carrierorderotherfee.mapping.GetOtherFeeByCarrierOrderIdMapping;
import com.logistics.appapi.controller.carrierorderotherfee.request.*;
import com.logistics.appapi.controller.carrierorderotherfee.response.GetOtherFeeByCarrierOrderIdResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2024/1/18 11:01
 */
@Api(value = "临时费用管理", tags = "临时费用管理")
@RestController
@RequestMapping(value = "/api/carrierOrderOtherFee")
public class CarrierOrderOtherFeeController {

    @Resource
    private CarrierOrderOtherFeeClient carrierOrderOtherFeeClient;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private ConfigKeyConstant configKeyConstant;

    /**
     * 新增临时费用 v2.43
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "新增临时费用",tags = "3.10.0")
    @PostMapping(value = "/addCarrierOrderOtherFee")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addCarrierOrderOtherFee(@RequestBody @Valid AddCarrierOrderOtherFeeRequestDto requestDto) {
        //费用类型不能重复
        List<String> feeTypeList = requestDto.getOtherFeeList().stream().map(AddCarrierOrderOtherFeeItemRequestDto::getFeeType).distinct().collect(Collectors.toList());
        if (feeTypeList.size() != requestDto.getOtherFeeList().size()){
            throw new BizException(AppApiExceptionEnum.OTHER_FEE_NOT_DUPLICATE);
        }

        AddCarrierOrderOtherFeeRequestModel requestModel = MapperUtils.mapper(requestDto, AddCarrierOrderOtherFeeRequestModel.class);
        requestModel.setRequestSource(CommonConstant.THREE);
        return carrierOrderOtherFeeClient.addCarrierOrderOtherFee(requestModel);
    }

    /**
     * 根据运单id查询【待提交】状态最新一条临时费用详情  v2.43
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "根据运单id查询【待提交】状态最新一条临时费用详情",tags = "3.10.0")
    @PostMapping(value = "/getOtherFeeByCarrierOrderId")
    public Result<GetOtherFeeByCarrierOrderIdResponseDto> getOtherFeeByCarrierOrderId(@RequestBody @Valid GetOtherFeeByCarrierOrderIdRequestDto requestDto) {
        Result<GetOtherFeeByCarrierOrderIdResponseModel> result = carrierOrderOtherFeeClient.getOtherFeeByCarrierOrderId(MapperUtils.mapper(requestDto, GetOtherFeeByCarrierOrderIdRequestModel.class));
        result.throwException();
        GetOtherFeeByCarrierOrderIdResponseModel responseModel = result.getData();
        Map<String, String> imageMap = new HashMap<>();
        if (ListUtils.isNotEmpty(responseModel.getOtherFeeList())){
            List<String> sourceSrcList = new ArrayList<>();
            responseModel.getOtherFeeList().forEach(o-> sourceSrcList.addAll(o.getBillsPicture()));
            imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        }
        return Result.success(MapperUtils.mapper(responseModel, GetOtherFeeByCarrierOrderIdResponseDto.class, new GetOtherFeeByCarrierOrderIdMapping(configKeyConstant.fileAccessAddress, imageMap)));
    }

    /**
     * 提交临时费用 v2.43
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "提交临时费用",tags = "3.10.0")
    @PostMapping(value = "/commitCarrierOrderOtherFee")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> commitCarrierOrderOtherFee(@RequestBody @Valid CommitCarrierOrderOtherFeeRequestDto requestDto) {
        //费用类型不能重复
        List<String> feeTypeList = requestDto.getOtherFeeList().stream().map(CommitCarrierOrderOtherFeeItemRequestDto::getFeeType).distinct().collect(Collectors.toList());
        if (feeTypeList.size() != requestDto.getOtherFeeList().size()){
            throw new BizException(AppApiExceptionEnum.OTHER_FEE_NOT_DUPLICATE);
        }

        CommitCarrierOrderOtherFeeRequestModel requestModel = MapperUtils.mapper(requestDto, CommitCarrierOrderOtherFeeRequestModel.class);
        requestModel.setOperateType(CommonConstant.ONE);
        requestModel.setRequestSource(CommonConstant.THREE);
        return carrierOrderOtherFeeClient.commitCarrierOrderOtherFee(requestModel);
    }
}
