package com.logistics.tms.controller.driveraccount.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/29
 */
@Data
public class BankCardInfoResponseModel {

	@ApiModelProperty(value = "司机账户ID")
	private Long driverAccountId;

	@ApiModelProperty(value = "银行名称")
	private String bankAccountName;

	@ApiModelProperty(value = "银行账号")
	private String bankAccount;
}
