package com.logistics.management.webapi.client.carrierorder;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.carrierorder.hystrix.CarrierOrderForYeloLifeClientHystrix;
import com.logistics.management.webapi.client.carrierorder.request.*;
import com.logistics.management.webapi.client.carrierorder.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/26 14:06
 */
@FeignClient(name = FeignClientName.TMS_SERVICES, fallback = CarrierOrderForYeloLifeClientHystrix.class)
public interface CarrierOrderForYeloLifeClient {

    @ApiOperation(value = "查询运单列表")
    @PostMapping(value = "/service/carrierOrderManagement/searchCarrierOrderListForYeloLife")
    Result<PageInfo<SearchCarrierOrderListForYeloLifeResponseModel>> searchCarrierOrderListForYeloLife(@RequestBody SearchCarrierOrderListForYeloLifeRequestModel requestModel);

    @ApiOperation(value = "导出运单")
    @PostMapping(value = "/service/carrierOrderManagement/exportCarrierOrderForYeloLife")
    Result<List<SearchCarrierOrderListForYeloLifeResponseModel>> exportCarrierOrderForYeloLife(@RequestBody SearchCarrierOrderListForYeloLifeRequestModel requestModel);

    @ApiOperation(value = "运单列表统计")
    @PostMapping(value = "/service/carrierOrderManagement/searchListStatisticsForYeloLife")
    Result<SearchCarrierListStatisticsResponseModel> searchListStatisticsForYeloLife(@RequestBody SearchCarrierOrderListForYeloLifeRequestModel requestModel);

    @ApiOperation(value = "运单详情页")
    @PostMapping(value = "/service/carrierOrderManagement/carrierOrderDetailForYeloLife")
    Result<CarrierOrderDetailForYeloLifeResponseModel> carrierOrderDetailForYeloLife(@RequestBody @Valid CarrierOrderIdRequestModel requestModel);

    @ApiOperation(value = "查询票据")
    @PostMapping(value = "/service/carrierOrderManagement/getTicketsForYeloLife")
    Result<List<TicketsModel>> getTicketsForYeloLife(@RequestBody CarrierOrderIdRequestModel requestModel);

    @ApiOperation(value = "查询提卸货详情")
    @PostMapping(value = "/service/carrierOrderManagement/getLoadDetailForYeloLife")
    Result<LoadDetailForYeloLifeResponseModel> getLoadDetailForYeloLife(@RequestBody LoadDetailForYeloLifeRequestModel requestModel);

    @ApiOperation(value = "提货")
    @PostMapping(value = "/service/carrierOrderManagement/loadForYeloLife")
    Result loadForYeloLife(@RequestBody LoadForYeloLifeRequestModel requestModel);

    @ApiOperation(value = "卸货")
    @PostMapping(value = "/service/carrierOrderManagement/unloadForYeloLife")
    Result<Boolean> unloadForYeloLife(@RequestBody UnLoadForYeloLifeRequestModel requestModel);

    @ApiOperation(value = "查询签收确认详情")
    @PostMapping(value = "/service/carrierOrderManagement/carrierOrderListBeforeSignUpForYeloLife")
    Result<SignDetailForYeloLifeResponseModel> carrierOrderListBeforeSignUpForYeloLife(@RequestBody @Valid CarrierOrderIdRequestModel requestModel);

    @ApiOperation(value = "签收")
    @PostMapping(value = "/service/carrierOrderManagement/carrierOrderSignUpForYeloLife")
    Result<Boolean> carrierOrderSignUpForYeloLife(@RequestBody @Valid CarrierOrderSignUpForYeloLifeRequestModel requestModel);

    /**
     * 查询签收详情-销售类型
     *
     * @param requestModel 运单id
     * @return 签收详情
     */
    @PostMapping(value = "/service/carrierOrderManagement/carrierOrderBeforeSignUpForYeloLife")
    Result<List<CarrierOrderBeforeSignUpForYeloLifeResponseModel>> carrierOrderBeforeSignUpForYeloLife(@RequestBody CarrierOrderBeforeSignUpForYeloLifeRequestModel requestModel);

    /**
     * 运单签收-销售类型
     *
     * @param requestModel 运单id,签收信息
     * @return 操作结果
     */
    @PostMapping(value = "/service/carrierOrderManagement/carrierOrderConfirmSignUpForYeloLife")
    Result<Boolean> carrierOrderConfirmSignUpForYeloLife(@RequestBody CarrierOrderConfirmSignUpForYeloLifeRequestModel requestModel);

    @ApiOperation(value = "修改卸货地址-查询新生仓库")
    @PostMapping(value = "/service/carrierOrderManagement/getYeloLifeUnloadWarehouse")
    Result<List<GetYeloLifeUnloadWarehouseResponseModel>> getYeloLifeUnloadWarehouse(@RequestBody GetYeloLifeUnloadWarehouseRequestModel requestModel);

    @ApiOperation(value = "修改卸货地址详情")
    @PostMapping(value = "/service/carrierOrderManagement/getUnloadAddressDetailForYeloLife")
    Result<UpdateUnloadAddressDetailResponseModel> getUnloadAddressDetailForYeloLife(@RequestBody CarrierOrderIdRequestModel requestModel);

    @ApiOperation(value = "确认修改卸货地址")
    @PostMapping(value = "/service/carrierOrderManagement/updateUnloadAddressConfirmForYeloLife")
    Result<Boolean> updateUnloadAddressConfirmForYeloLife(@RequestBody UpdateCarrierOrderUnloadAddressForLifeRequestModel requestModel);

}
