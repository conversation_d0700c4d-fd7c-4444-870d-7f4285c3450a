package com.logistics.tms.mapper;

import com.logistics.tms.api.feign.carriercontact.dto.CarrierContactDetailResponseModel;
import com.logistics.tms.api.feign.carriercontact.dto.SearchCarrierContactRequestModel;
import com.logistics.tms.api.feign.carriercontact.dto.SearchCarrierContactResponseModel;
import com.logistics.tms.entity.TCarrierContact;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Mybatis Generator on 2022/07/11
 */
@Mapper
public interface TCarrierContactMapper extends BaseMapper<TCarrierContact> {

    TCarrierContact selectByContactPhone(@Param("contactPhone") String contactPhone);

    List<TCarrierContact> getByCompanyCarrierId(@Param("companyCarrierId") Long companyCarrierId);

    int updateByPrimaryKeySelectiveTime(TCarrierContact tCarrierContact);

    int insertSelectiveEncrypt(TCarrierContact tCarrierContact);

    int updateByPrimaryKeySelectiveEncrypt(TCarrierContact tCarrierContact);

    TCarrierContact selectByPrimaryKeyDecrypt(@Param("id") Long id);

    CarrierContactDetailResponseModel getCarrierContactDetailById(@Param("carrierContactId") Long carrierContactId);

    List<SearchCarrierContactResponseModel> selectCarrierContactList(@Param("params") SearchCarrierContactRequestModel requestModel);
}