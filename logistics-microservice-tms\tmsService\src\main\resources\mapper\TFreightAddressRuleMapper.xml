<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TFreightAddressRuleMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TFreightAddressRule" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="freight_address_id" property="freightAddressId" jdbcType="BIGINT" />
    <result column="rule_index" property="ruleIndex" jdbcType="INTEGER" />
    <result column="amount_from" property="amountFrom" jdbcType="DECIMAL" />
    <result column="from_symbol" property="fromSymbol" jdbcType="INTEGER" />
    <result column="amount_to" property="amountTo" jdbcType="DECIMAL" />
    <result column="to_symbol" property="toSymbol" jdbcType="INTEGER" />
    <result column="freight_type" property="freightType" jdbcType="INTEGER" />
    <result column="freight_fee" property="freightFee" jdbcType="DECIMAL" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, freight_address_id, rule_index, amount_from, from_symbol, amount_to, to_symbol, 
    freight_type, freight_fee, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_freight_address_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_freight_address_rule
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TFreightAddressRule" >
    insert into t_freight_address_rule (id, freight_address_id, rule_index, 
      amount_from, from_symbol, amount_to, 
      to_symbol, freight_type, freight_fee, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{freightAddressId,jdbcType=BIGINT}, #{ruleIndex,jdbcType=INTEGER}, 
      #{amountFrom,jdbcType=DECIMAL}, #{fromSymbol,jdbcType=INTEGER}, #{amountTo,jdbcType=DECIMAL}, 
      #{toSymbol,jdbcType=INTEGER}, #{freightType,jdbcType=INTEGER}, #{freightFee,jdbcType=DECIMAL}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TFreightAddressRule" keyProperty="id" useGeneratedKeys="true">
    insert into t_freight_address_rule
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="freightAddressId != null" >
        freight_address_id,
      </if>
      <if test="ruleIndex != null" >
        rule_index,
      </if>
      <if test="amountFrom != null" >
        amount_from,
      </if>
      <if test="fromSymbol != null" >
        from_symbol,
      </if>
      <if test="amountTo != null" >
        amount_to,
      </if>
      <if test="toSymbol != null" >
        to_symbol,
      </if>
      <if test="freightType != null" >
        freight_type,
      </if>
      <if test="freightFee != null" >
        freight_fee,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="freightAddressId != null" >
        #{freightAddressId,jdbcType=BIGINT},
      </if>
      <if test="ruleIndex != null" >
        #{ruleIndex,jdbcType=INTEGER},
      </if>
      <if test="amountFrom != null" >
        #{amountFrom,jdbcType=DECIMAL},
      </if>
      <if test="fromSymbol != null" >
        #{fromSymbol,jdbcType=INTEGER},
      </if>
      <if test="amountTo != null" >
        #{amountTo,jdbcType=DECIMAL},
      </if>
      <if test="toSymbol != null" >
        #{toSymbol,jdbcType=INTEGER},
      </if>
      <if test="freightType != null" >
        #{freightType,jdbcType=INTEGER},
      </if>
      <if test="freightFee != null" >
        #{freightFee,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TFreightAddressRule" >
    update t_freight_address_rule
    <set >
      <if test="freightAddressId != null" >
        freight_address_id = #{freightAddressId,jdbcType=BIGINT},
      </if>
      <if test="ruleIndex != null" >
        rule_index = #{ruleIndex,jdbcType=INTEGER},
      </if>
      <if test="amountFrom != null" >
        amount_from = #{amountFrom,jdbcType=DECIMAL},
      </if>
      <if test="fromSymbol != null" >
        from_symbol = #{fromSymbol,jdbcType=INTEGER},
      </if>
      <if test="amountTo != null" >
        amount_to = #{amountTo,jdbcType=DECIMAL},
      </if>
      <if test="toSymbol != null" >
        to_symbol = #{toSymbol,jdbcType=INTEGER},
      </if>
      <if test="freightType != null" >
        freight_type = #{freightType,jdbcType=INTEGER},
      </if>
      <if test="freightFee != null" >
        freight_fee = #{freightFee,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TFreightAddressRule" >
    update t_freight_address_rule
    set freight_address_id = #{freightAddressId,jdbcType=BIGINT},
      rule_index = #{ruleIndex,jdbcType=INTEGER},
      amount_from = #{amountFrom,jdbcType=DECIMAL},
      from_symbol = #{fromSymbol,jdbcType=INTEGER},
      amount_to = #{amountTo,jdbcType=DECIMAL},
      to_symbol = #{toSymbol,jdbcType=INTEGER},
      freight_type = #{freightType,jdbcType=INTEGER},
      freight_fee = #{freightFee,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>