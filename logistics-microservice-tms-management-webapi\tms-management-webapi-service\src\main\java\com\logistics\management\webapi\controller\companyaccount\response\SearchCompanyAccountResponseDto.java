package com.logistics.management.webapi.controller.companyaccount.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchCompanyAccountResponseDto {

    @ApiModelProperty(value = "公司账户ID")
    private String companyAccountId = "";

    @ApiModelProperty(value = "状态: 1 启用 0 禁用")
    private String enabled = "";

    @ApiModelProperty(value = "状态文本")
    private String enabledLabel = "";

    @ApiModelProperty(value = "银行账号")
    private String bankAccount = "";

    @ApiModelProperty(value = "行号")
    private String bankCode = "";

    @ApiModelProperty(value = "银行名称, 开户银行名称 + 开户支行名称")
    private String bankAccountName = "";

    @ApiModelProperty(value = "银行卡图片数量")
    private String bankImageNumber = "";

    @ApiModelProperty(value = "使用范围")
    private String remark = "";

    @ApiModelProperty(value = "新增人")
    private String createdBy = "";

    @ApiModelProperty(value = "操作人")
    private String operateBy = "";

    @ApiModelProperty(value = "操作时间, yyyy-MM-dd HH:mm:ss")
    private String operateDateTime = "";
}
