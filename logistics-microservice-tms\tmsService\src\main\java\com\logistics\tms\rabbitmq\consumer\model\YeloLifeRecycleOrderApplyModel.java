package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 新生回收单model
 *
 * <AUTHOR>
 * @date ：Created in 2022/8/19
 */
@Data
public class YeloLifeRecycleOrderApplyModel {

	@ApiModelProperty("回收申请单号")
	private String recycleOrderCode;

	@ApiModelProperty("业务类型：1 公司，2 个人")
	private Integer businessType;

	@ApiModelProperty("乐橘新生客户名称（企业）")
	private String customerName;

	@ApiModelProperty("乐橘新生客户姓名（个人）")
	private String customerUserName;

	@ApiModelProperty("乐橘新生客户手机号（个人）")
	private String customerUserMobile;

	@ApiModelProperty("下单人")
	private String publishUserName;

	@ApiModelProperty("下单人手机号")
	private String publishUserMobile;

	@ApiModelProperty("发货仓库code")
	private String loadWarehouseCode;

	@ApiModelProperty("发货仓库")
	private String loadWarehouse;

	@ApiModelProperty("省份ID(高德)")
	private Long loadProvinceId;

	@ApiModelProperty("省份名字")
	private String loadProvinceName;

	@ApiModelProperty("城市ID(高德)")
	private Long loadCityId;

	@ApiModelProperty("城市名字")
	private String loadCityName;

	@ApiModelProperty("县区id(高德)")
	private Long loadAreaId;

	@ApiModelProperty("县区名字")
	private String loadAreaName;

	@ApiModelProperty("详细地址")
	private String loadDetailAddress;

	@ApiModelProperty("发货人姓名")
	private String consignorName;

	@ApiModelProperty("发货人手机号")
	private String consignorMobile;

	@ApiModelProperty("回收单货物")
	private List<YeloLifeRecycleOrderGoodsModel> recycleOrderGoods;

	@ApiModelProperty("订单备注")
	private String remark;

	@ApiModelProperty("推送物流操作人")
	private String operatorName;
}
