package com.logistics.tms.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author：wjf
 * @date：2021/4/12 13:12
 */
@Data
public class DriverReconciliationListCountResponseModel {
    @ApiModelProperty("全部")
    private Integer allCount = 0;
    @ApiModelProperty("待确认")
    private Integer waitConfirmCount = 0;
    @ApiModelProperty("待结清（包含部分结清）")
    private Integer waitSettleCount = 0;
    @ApiModelProperty("已结清")
    private Integer completeSettleCount = 0;
    @ApiModelProperty("待处理")
    private Integer waitSolveCount = 0;
}
