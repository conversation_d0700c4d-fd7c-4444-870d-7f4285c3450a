/**
 * Created by yun<PERSON>zhou on 2017/12/12.
 */
package com.logistics.tms.base.enums;

public enum WeixinPushInfoRoleEnum {

    SENDER(1, "发货方"),
    RECEIVER(2, "收货方"),
    ENTRUST(3, "委托方"),
    OTHER(4, "其他"),;

    private Integer key;
    private String value;

    WeixinPushInfoRoleEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
