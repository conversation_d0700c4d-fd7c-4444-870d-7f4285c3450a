package com.logistics.tms.biz.freight

import com.logistics.tms.api.feign.driverpayee.model.ViewLogResponseModel
import com.logistics.tms.api.feign.freight.model.*
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.controller.demandorder.response.GetDemandOrderInfoByIdsModel
import com.logistics.tms.entity.TFreight
import com.logistics.tms.entity.TFreightAddress
import com.logistics.tms.entity.TFreightAddressRule
import com.logistics.tms.entity.TFreightAddressRuleMarkup
import com.logistics.tms.mapper.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class FreightBizTest extends Specification {
    @Mock
    CommonBiz commonBiz
    @Mock
    TFreightMapper tFreightMapper
    @Mock
    TFreightAddressMapper tFreightAddressMapper
    @Mock
    TFreightAddressRuleMapper tFreightAddressRuleMapper
    @Mock
    TFreightAddressRuleMarkupMapper tFreightAddressRuleMarkupMapper
    @Mock
    TCompanyCarrierMapper tCompanyCarrierMapper
    @Mock
    TCompanyEntrustMapper tCompanyEntrustMapper
    @Mock
    TOperateLogsMapper tOperateLogsMapper
    @Mock
    TDemandOrderMapper tDemandOrderMapper
    @Mock
    TCarrierVehicleRelationMapper tCarrierVehicleRelationMapper
    @Mock
    TWarehouseAddressMapper tWarehouseAddressMapper
    @InjectMocks
    FreightBiz freightBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tFreightMapper.searchEntrustFreightList(any())).thenReturn([new SearchFreightListResponseModel()])
        when(tFreightMapper.searchCarrierFreightList(any())).thenReturn([new SearchFreightListResponseModel()])

        expect:
        freightBiz.searchList(requestModel) == expectedResult

        where:
        requestModel                        || expectedResult
        new SearchFreightListRequestModel() || null
    }

    @Unroll
    def "get Freight Company Info where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tFreightMapper.getEntrustFreightCompanyInfo(anyLong())).thenReturn(new FreightCompanyInfoResponseModel())
        when(tFreightMapper.getCarrierFreightCompanyInfo(anyLong())).thenReturn(new FreightCompanyInfoResponseModel())

        expect:
        freightBiz.getFreightCompanyInfo(requestModel) == expectedResult

        where:
        requestModel                         || expectedResult
        new FreightCompanyInfoRequestModel() || new FreightCompanyInfoResponseModel()
    }

    @Unroll
    def "add Freight where requestModel=#requestModel"() {
        given:
        when(tFreightMapper.getByCompanyIdAndRoleType(anyLong(), anyInt())).thenReturn(new TFreight(objectId: 1l, objectType: 0))

        expect:
        freightBiz.addFreight(requestModel)
        assert expectedResult == false

        where:
        requestModel                 || expectedResult
        new AddFreightRequestModel() || true
    }

    @Unroll
    def "enable Freight where requestModel=#requestModel"() {
        expect:
        freightBiz.enableFreight(requestModel)
        assert expectedResult == false

        where:
        requestModel                    || expectedResult
        new EnableFreightRequestModel() || true
    }

    @Unroll
    def "search Freight Address List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tFreightAddressMapper.searchFreightAddressList(any())).thenReturn([new SearchFreightAddressResponseModel()])

        expect:
        freightBiz.searchFreightAddressList(requestModel) == expectedResult

        where:
        requestModel                           || expectedResult
        new SearchFreightAddressRequestModel() || null
    }

    @Unroll
    def "add Freight Address Rule where requestModel=#requestModel"() {
        given:
        when(tFreightAddressMapper.getListByCondition(any())).thenReturn([new TFreightAddress(freightId: 1l, warehouseId: 1l, fromProvinceId: 1l, fromProvinceName: "fromProvinceName", fromCityId: 1l, fromCityName: "fromCityName", fromAreaId: 1l, fromAreaName: "fromAreaName", toProvinceId: 1l, toProvinceName: "toProvinceName", toCityId: 1l, toCityName: "toCityName", toAreaId: 1l, toAreaName: "toAreaName", calcType: 0)])
        when(tFreightAddressRuleMapper.getListByFreightAddressIds(anyString())).thenReturn([new TFreightAddressRule(freightAddressId: 1l, ruleIndex: 0, amountFrom: 0 as BigDecimal, fromSymbol: 0, amountTo: 0 as BigDecimal, toSymbol: 0, freightType: 0, freightFee: 0 as BigDecimal)])
        when(tFreightAddressRuleMarkupMapper.batchInsert(any())).thenReturn(0)
        when(tFreightAddressRuleMarkupMapper.getByFreightAddressRuleIds(anyString())).thenReturn([new TFreightAddressRuleMarkup(freightAddressRuleId: 1l, markIndex: 0, loadAmount: 0, unloadAmount: 0, markupFreightFee: 0 as BigDecimal)])
        when(tFreightAddressRuleMarkupMapper.batchUpdate(any())).thenReturn(0)

        expect:
        freightBiz.addFreightAddressRule(requestModel)
        assert expectedResult == false

        where:
        requestModel                                    || expectedResult
        new AddOrModifyFreightAddressRuleRequestModel() || true
    }

    @Unroll
    def "get Freight Rule Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tFreightAddressMapper.getFreightAddressRuleDetail(anyLong())).thenReturn(new FreightAddressRuleDetailResponseModel())

        expect:
        freightBiz.getFreightRuleDetail(requestModel) == expectedResult

        where:
        requestModel                               || expectedResult
        new FreightAddressRuleDetailRequestModel() || new FreightAddressRuleDetailResponseModel()
    }

    @Unroll
    def "delete Freight Address Rule where requestModel=#requestModel"() {
        given:
        when(tFreightAddressMapper.deleteFreightAddress(anyString(), anyString(), any())).thenReturn(0)

        expect:
        freightBiz.deleteFreightAddressRule(requestModel)
        assert expectedResult == false

        where:
        requestModel                           || expectedResult
        new DeleteFreightAddressRequestModel() || true
    }

    @Unroll
    def "modify Freight Price where requestModel=#requestModel"() {
        given:
        when(tFreightAddressRuleMapper.getListByFreightAddressIds(anyString())).thenReturn([new TFreightAddressRule(freightFee: 0 as BigDecimal)])
        when(tOperateLogsMapper.batchInsert(any())).thenReturn(0)

        expect:
        freightBiz.modifyFreightPrice(requestModel)
        assert expectedResult == false

        where:
        requestModel                         || expectedResult
        new ModifyFreightPriceRequestModel() || true
    }

    @Unroll
    def "freight Logs where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tOperateLogsMapper.selectLogsByCondition(anyInt(), anyLong(), anyInt())).thenReturn([new ViewLogResponseModel()])

        expect:
        freightBiz.freightLogs(requestModel) == expectedResult

        where:
        requestModel                  || expectedResult
        new FreightLogsRequestModel() || [new FreightLogsResponseModel()]
    }

    @Unroll
    def "get Driver Freight where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tFreightMapper.getByCompanyIdAndRoleType(anyLong(), anyInt())).thenReturn(new TFreight(enabled: 0))
        when(tFreightMapper.getDriverFreightInfo(any())).thenReturn([new CompanyDriverFreightResponseModel()])
        when(tFreightAddressRuleMarkupMapper.getByRuleIdLoadAmount(anyLong(), anyInt(), anyInt())).thenReturn(new TFreightAddressRuleMarkup(markupFreightFee: 0 as BigDecimal))
        when(tDemandOrderMapper.getDemandOrderInfoByIds(anyString())).thenReturn([new GetDemandOrderInfoByIdsModel()])

        expect:
        freightBiz.getDriverFreight(requestModel) == expectedResult

        where:
        requestModel                                              || expectedResult
        new DriverFreightByDemandOrderIdsAndVehicleRequestModel() || new DriverFreightByDemandOrderIdsAndVehicleResponseModel()
    }

    @Unroll
    def "get Entrust Freight Info where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tFreightMapper.getByCompanyIdAndRoleType(anyLong(), anyInt())).thenReturn(new com.logistics.tms.entity.TFreight(enabled: 0))
        when(tFreightMapper.getEntrustFreightInfo(any())).thenReturn(new com.logistics.tms.api.feign.freight.model.GetFreightForEntrustResponseModel())
        when(tWarehouseAddressMapper.getWarehouseByName(anyLong(), anyString())).thenReturn(new com.logistics.tms.entity.TWarehouseAddress())

        expect:
        freightBiz.getEntrustFreightInfo(requestModel) == expectedResult

        where:
        requestModel                                                                           || expectedResult
        new com.logistics.tms.api.feign.freight.model.GetPriceByAddressAndAmountRequestModel() || new com.logistics.tms.api.feign.freight.model.GetPriceByAddressAndAmountResponseModel()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme