package com.logistics.tms.mapper;

import com.logistics.tms.biz.shippingfreight.model.GetCountCarrierByShippingFreightIdModel;
import com.logistics.tms.controller.baiscinfo.web.response.CarrierBasicInfoResponseModel;
import com.logistics.tms.biz.demandorder.model.CompanyCarrierByIdModel;
import com.logistics.tms.controller.companycarrier.request.FuzzySearchCompanyCarrierRequestModel;
import com.logistics.tms.controller.companycarrier.request.SearchCompanyCarrierListRequestModel;
import com.logistics.tms.controller.companycarrier.response.CompanyCarrierDetailResponseModel;
import com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.tms.controller.companycarrier.response.SearchCompanyCarrierListResponseModel;
import com.logistics.tms.controller.freightconfig.response.AssociateCarrierListRespModel;
import com.logistics.tms.controller.settlestatement.packaging.response.CarrierTaxPointResponseModel;
import com.logistics.tms.entity.TCompanyCarrier;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TCompanyCarrierMapper extends BaseMapper<TCompanyCarrier> {

    int updateByPrimaryKeySelectiveForTime(TCompanyCarrier tCompanyCarrier);

    List<SearchCompanyCarrierListResponseModel> getCompanyCarrierList(@Param("params") SearchCompanyCarrierListRequestModel requestModel);

    CompanyCarrierDetailResponseModel getCompanyCarrierDetailById(@Param("companyCarrierId") Long companyCarrierId);

    List<FuzzySearchCompanyCarrierResponseModel> fuzzyQueryCompanyCarrierInfo(@Param("params") FuzzySearchCompanyCarrierRequestModel requestModel);

    List<FuzzySearchCompanyCarrierResponseModel> selectCompanyCarrierInfoByIds(@Param("ids") List<Long> companyCarrierIds);

    FuzzySearchCompanyCarrierResponseModel getCompanyCarrierInfoById(@Param("id") Long companyCarrierId);

    TCompanyCarrier getByName(@Param("companyName") String companyName);

    //根据ID查询非我司未加入黑名单车主
    CompanyCarrierByIdModel selectNoOurCompanyCarrierById(@Param("companyCarrierId") Long companyCarrierId);

    CompanyCarrierByIdModel selectCarrierById(@Param("companyCarrierId") Long companyCarrierId);

    List<TCompanyCarrier> getByIds(@Param("ids") String ids);

    CarrierTaxPointResponseModel selectTaxPointById(@Param("companyCarrierId") Long companyCarrierId);

    CarrierBasicInfoResponseModel selectCarrierBasicInfo(@Param("companyCarrierId") Long companyCarrierId);

    FuzzySearchCompanyCarrierResponseModel selectPersonCarrierByMobile(@Param("mobile") String mobile);

    List<FuzzySearchCompanyCarrierResponseModel> selectPersonCarrierByMobiles(@Param("mobiles") List<String> mobiles);

    TCompanyCarrier getByCompanyId(@Param("companyId") Long companyId);

    List<GetCountCarrierByShippingFreightIdModel> getCountCarrierByShippingFreightIds(@Param("ids") List<Long> ids);

    int updateNoAuthCompanyAndMore31Day(Long carrierCompanyId);



}