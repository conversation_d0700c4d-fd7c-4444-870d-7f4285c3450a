package com.logistics.tms.biz.violationregulation

import com.logistics.tms.controller.vehicleassetmanagement.response.FuzzyQueryVehicleInfoResponseModel
import com.yelo.tray.core.exception.BizException
import com.logistics.tms.api.feign.violationregulation.model.*
import com.logistics.tms.base.constant.ConfigKeyConstant
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TCertificationPictures
import com.logistics.tms.entity.TStaffBasic
import com.logistics.tms.entity.TVehicleBasic
import com.logistics.tms.entity.TViolationRegulations
import com.logistics.tms.mapper.TCertificationPicturesMapper
import com.logistics.tms.mapper.TStaffBasicMapper
import com.logistics.tms.mapper.TVehicleBasicMapper
import com.logistics.tms.mapper.TViolationRegulationsMapper
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class ViolationRegulationBizTest extends Specification {
    @Mock
    TViolationRegulationsMapper tQViolationRegulationsMapper
    @Mock
    TCertificationPicturesMapper tQCertificationPicturesMapper
    @Mock
    TVehicleBasicMapper tQVehicleBasicMapper
    @Mock
    TStaffBasicMapper tQStaffBasicMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    ConfigKeyConstant configKeyConstant
    @InjectMocks
    ViolationRegulationBiz violationRegulationBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Violation Regulations Info where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tQViolationRegulationsMapper.getViolationRegulationSummaryData(any())).thenReturn(new SearchViolationRegulationListResponseModel())
        when(tQViolationRegulationsMapper.getViolationRegulationIds(any())).thenReturn([1l])
        when(tQViolationRegulationsMapper.getViolationRegulationListByIds(anyString())).thenReturn([new ViolationRegulationListResponseModel()])

        expect:
        violationRegulationBiz.searchViolationRegulationsInfo(requestModel) == expectedResult

        where:
        requestModel                                    || expectedResult
        new SearchViolationRegulationListRequestModel() || new SearchViolationRegulationListResponseModel()
    }

    @Unroll
    def "save Violation Regulation where requestModel=#requestModel"() {
        given:
        when(tQVehicleBasicMapper.selectByPrimaryKey(anyLong())).thenReturn(vehicleBasic)
        when(tQStaffBasicMapper.selectByPrimaryKey(anyLong())).thenReturn(staffBasic)
        when(tQViolationRegulationsMapper.selectRecordByVehicleIdAndDriverIdAndOccurTime(anyLong(), anyLong(), any())).thenReturn(new TViolationRegulations(vehicleId: 1l, driverId: 1l, deduction: 0, fine: 0 as BigDecimal, occuranceTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 50).getTime(), occuranceAddress: "occuranceAddress", remark: "remark", source: 0))
        when(tQCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 50).getTime(), suffix: "suffix")])
        when(tQCertificationPicturesMapper.batchInsert(any())).thenReturn(0)
        when(tQCertificationPicturesMapper.updateFilePath(any())).thenReturn(0)
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")

        when:
        violationRegulationBiz.saveViolationRegulation(requestModel)

        then: "校验验证"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where:
        requestModel                                                              || expectedResult || staffBasic        || vehicleBasic
        new AddOrModifyViolationRegulationRequestModel(driverId: 1)               || "司机信息不存在"      || null              || null
        new AddOrModifyViolationRegulationRequestModel(driverId: 1)               || "车辆信息不存在"      || new TStaffBasic() || null
        new AddOrModifyViolationRegulationRequestModel(driverId: 1, vehicleId: 1) || "违章信息已存在"      || new TStaffBasic() || new TVehicleBasic()
        new AddOrModifyViolationRegulationRequestModel(driverId: 1, vehicleId: 1) || "违章信息不存在"      || new TStaffBasic() || new TVehicleBasic()
    }

    @Unroll
    def "get Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tQViolationRegulationsMapper.getViolationRegulationListByIds(anyString())).thenReturn([new ViolationRegulationListResponseModel(violationRegulationId: 1)])
        when(tQViolationRegulationsMapper.selectByPrimaryKey(anyLong())).thenReturn(new TViolationRegulations())

        expect: "验证"
        with(violationRegulationBiz.getDetail(requestModel)) {
            violationRegulationId == expectedResult
        }

        where:
        requestModel                                                        || expectedResult
        new ViolationRegulationDetailRequestModel(violationRegulationId: 1) || 1
    }

    @Unroll
    def "delete Violation Regulation where requestModel=#requestModel"() {
        given:

        when:
        violationRegulationBiz.deleteViolationRegulation(requestModel)

        then:
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where:
        requestModel                                                         || expectedResult
        new DeleteViolationRegulationRequestModel(violationRegulationId: -1) || "违章信息不存在"
    }

    @Unroll
    def "import Violation Regulation where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tQViolationRegulationsMapper.batchInsert(any())).thenReturn(0)
        when(tQViolationRegulationsMapper.selectRecordByVehicleIdAndDriverIdAndOccurTime(anyLong(), anyLong(), any())).thenReturn(new TViolationRegulations(vehicleId: 1l, driverId: 1l, deduction: 0, fine: 0 as BigDecimal, occuranceTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 50).getTime(), occuranceAddress: "occuranceAddress", remark: "remark", source: 0))
        when(tQViolationRegulationsMapper.batchUpdate(any())).thenReturn(0)
        when(tQVehicleBasicMapper.fuzzyQueryVehicleInfoByVehicleNo(anyString())).thenReturn([new FuzzyQueryVehicleInfoResponseModel()])
        when(tQStaffBasicMapper.fuzzyQueryDriverInfo(anyString())).thenReturn([new FuzzyQueryDriverInfoResponseModel()])

        expect:
        violationRegulationBiz.importViolationRegulation(requestModel) == expectedResult

        where:
        requestModel                                                                                      || expectedResult
        new com.logistics.tms.api.feign.violationregulation.model.ImportViolationRegulationRequestModel() || new com.logistics.tms.api.feign.violationregulation.model.ImportViolationRegulationResponseModel()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme