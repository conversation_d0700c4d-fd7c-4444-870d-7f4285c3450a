package com.logistics.appapi.controller.uploadfile.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/5/8 17:29
 */
@Data
public class TrainTicketResponseDto {
    @ApiModelProperty("姓名")
    private String name="";
    @ApiModelProperty("车牌金额")
    private String ticketRates="";
    @ApiModelProperty("出发日期")
    private String date="";
    @ApiModelProperty("席别")
    private String seatCategory="";
    @ApiModelProperty("始发站")
    private String startingStation="";
    @ApiModelProperty("车票号")
    private String ticketNum="";
    @ApiModelProperty("车次号")
    private String trainNum="";
    @ApiModelProperty("到达站")
    private String destinationStation="";
}
