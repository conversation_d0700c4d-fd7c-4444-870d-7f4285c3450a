package com.logistics.management.webapi.controller.oilfilled.mapping;

import com.logistics.management.webapi.client.oilfilled.response.OilFilledDetailResponseModel;
import com.logistics.management.webapi.controller.oilfilled.response.OilFilledDetailResponseDto;
import com.logistics.management.webapi.controller.oilfilled.response.OilFilledFileDto;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.OilFilledStatusEnum;
import com.logistics.management.webapi.base.enums.OilFilledTypeEnum;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;

import java.util.Map;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/10/10
 * @description:
 */
public class OilFilledDetailMapping extends MapperMapping<OilFilledDetailResponseModel, OilFilledDetailResponseDto> {

    private ConfigKeyConstant configKeyConstant;

    private Map<String,String> imageMap;

    @Override
    public void configure() {
        OilFilledDetailResponseModel source = getSource();
        OilFilledDetailResponseDto destination = getDestination();
        if (source.getStatus() != null) {
            destination.setStatusLabel(OilFilledStatusEnum.getEnum(source.getStatus()).getValue());
        }
        if (source.getOilFilledType() != null) {
            destination.setOilFilledTypeLabel(OilFilledTypeEnum.getEnum(source.getOilFilledType()).getValue());
        }
        if (source.getOilFilledDate() != null) {
            destination.setOilFilledDate(DateUtils.dateToString(source.getOilFilledDate(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if (ListUtils.isNotEmpty(destination.getOilFilledFileList())) {
            for (OilFilledFileDto item : destination.getOilFilledFileList()) {
                item.setAbsoluteFilePath(configKeyConstant.fileAccessAddress + imageMap.get(item.getRelativeFilepath()));
            }
        }
        //拼接司机姓名跟手机号
        destination.setName(source.getName() + " " + source.getMobile());
    }

    public OilFilledDetailMapping() {
    }

    public OilFilledDetailMapping(ConfigKeyConstant configKeyConstant, Map<String,String> imageMap) {
        this.configKeyConstant = configKeyConstant;
        this.imageMap=imageMap;
    }
}
