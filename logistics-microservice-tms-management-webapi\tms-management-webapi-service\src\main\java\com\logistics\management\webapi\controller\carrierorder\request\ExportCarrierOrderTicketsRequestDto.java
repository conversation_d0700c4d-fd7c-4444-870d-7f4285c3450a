package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author：wjf
 * @date：2021/3/16 11:38
 */
@Data
public class ExportCarrierOrderTicketsRequestDto {
    @ApiModelProperty("运单ids")
    @NotEmpty(message = "请选择运单")
    private List<String> carrierOrderIdList;
    @ApiModelProperty("命名方式：1 运单号，2 客户单号，3 车牌号（多个用逗号分隔）")
    @NotBlank(message = "请选择命名方式")
    private String fileNameType;
}
