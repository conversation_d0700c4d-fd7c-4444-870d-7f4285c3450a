package com.logistics.appapi.controller.verificationcode.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class GetVerificationCodeRequestDto {

    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    @ApiModelProperty(value = "验证码类型：1:tms司机小程序登录 2:tms司机小程序找回密码 3:tms司机修改手机号 4:tms司机添加银行卡 5:访客预约 6:访客签到", required = true)
    @NotBlank(message = "验证码类型不能为空")
    private String verificationType;

    @ApiModelProperty("拼图验证码X轴")
    private String locationX;
    @ApiModelProperty("拼图验证码验证ID")
    private String verificationId;
}
