package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * @author: wjf
 * @date: 2024/4/26 13:08
 */
@Getter
@AllArgsConstructor
public enum OrderModeEnum {

    DEFAULT(0, ""),
    ASSIGN_CARRIER(1, "指定车主"),
    BIDDING_PRICE(2, "竞价抢单"),
    ;

    private final Integer key;
    private final String value;

    public static OrderModeEnum getEnumByStringKey(String key) {
        return Stream.of(values())
                .filter(f -> f.getKey().toString().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
