package com.logistics.management.webapi.api.feign.carrierfreight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询车主运价列表
 *
 * <AUTHOR>
 * @date 2022/9/1 15:00
 */
@Data
public class SearchCarrierFreightResponseDto {

    @ApiModelProperty("车主运价id")
    private String carrierFreightId = "";

    @ApiModelProperty("状态")
    private String enabled = "";

    @ApiModelProperty("状态描述")
    private String enableLabel = "";

    @ApiModelProperty("车主名称或个人姓名手机号")
    private String companyCarrierName = "";
}
