package com.logistics.management.webapi.api.feign.carrierfreight;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.carrierfreight.dto.CarrierFreightAddRequestDto;
import com.logistics.management.webapi.api.feign.carrierfreight.dto.CarrierFreightEnableRequestDto;
import com.logistics.management.webapi.api.feign.carrierfreight.dto.SearchCarrierFreightRequestDto;
import com.logistics.management.webapi.api.feign.carrierfreight.dto.SearchCarrierFreightResponseDto;
import com.logistics.management.webapi.api.feign.carrierfreight.hystrix.CarrierFreightApiHystrix;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 车主运价
 *
 * <AUTHOR>
 * @date 2022/9/1 14:32
 */
@Api(value = "API-CarrierFreightApi-车主运价")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = CarrierFreightApiHystrix.class)
public interface CarrierFreightApi {

    @ApiOperation(value = "添加车主运价 v1.2.2")
    @PostMapping(value = "/api/carrierFreight/addCarrierFreight")
    Result<Boolean> addCarrierFreight(@RequestBody @Valid CarrierFreightAddRequestDto requestDto);

    @ApiOperation(value = "禁用/启用车主运价 v1.2.2")
    @PostMapping(value = "/api/carrierFreight/enable")
    Result<Boolean> enable(@RequestBody @Valid CarrierFreightEnableRequestDto requestDto);

    @ApiOperation(value = "查询车主运价列表 v1.2.2")
    @PostMapping(value = "/api/carrierFreight/searchList")
    Result<PageInfo<SearchCarrierFreightResponseDto>> searchList(@RequestBody SearchCarrierFreightRequestDto requestDto);
}
