package com.logistics.tms.configuration;

import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.constant.BaseAppConstant;
import org.slf4j.MDC;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.CollectionUtils;

import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@EnableAsync
public class AsyncConfiguration {

    @Bean
    public TaskExecutor taskExecutor() {
        return this.getTaskExecutor("logistics-tms-");
    }

    //自动发布调度使用的线程池
    @Bean("demandPublishAndDispatchTask")
    public TaskExecutor demandPublishAndDispatchTask() {
        return this.getTaskExecutor("logistics-tms-publish-");
    }

    // 回单自动审核使用的线程池
    @Bean("receiptAutoAuditTask")
    public TaskExecutor receiptAutoAuditTask() {
        return this.getTaskExecutor("logistics-tms-receipt-auto-");
    }

    // 智能推送线程池
    @Bean("workGroupPushTaskExecutor")
    public TaskExecutor workGroupPushTaskExecutor() {
        return this.getTaskExecutor("work-group-");
    }

    private ThreadPoolTaskExecutor getTaskExecutor(String threadNamePrefix) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置核心线程数
        executor.setCorePoolSize(10);
        // 设置最大线程数
        executor.setMaxPoolSize(20);
        // 设置队列容量
        executor.setQueueCapacity(20);
        // 设置线程活跃时间（秒）
        executor.setKeepAliveSeconds(60);
        // 设置默认线程名称
        if (StringUtils.isNotBlank(threadNamePrefix)) {
            executor.setThreadNamePrefix(threadNamePrefix);
        }
        // 设置拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // MDC, BaseContextHandler 拓展
        executor.setTaskDecorator(new TaskDecorator() {
            @Override
            public Runnable decorate(Runnable runnable) {
                Map<String, String> mdcMap = MDC.getCopyOfContextMap();
                Map<String, Object> baseContextMap = BaseContextHandler.getAll();
                return () -> {
                    try {
                        this.copy(mdcMap, baseContextMap);
                        runnable.run();
                    } finally {
                        this.clear();
                    }
                };
            }

            /**
             * 子线程执行完成后将会移除ThreadLocal中的数据
             */
            private void clear() {
                MDC.clear();
                BaseAppConstant.removeAllInfoInThread();
                BaseContextHandler.remove();
            }

            /**
             * 复制主线程中threadlocal的数据
             *
             * @param context   MDC中的数据
             * @param objectMap 主要是一些乐橘后端服务用户登录的数据
             */
            private void copy(Map<String, String> context, Map<String, Object> objectMap) {
                if (CollectionUtils.isEmpty(context)) {
                    MDC.clear();
                } else {
                    MDC.setContextMap(context);
                }
                if (CollectionUtils.isEmpty(objectMap)) {
                    BaseContextHandler.remove();
                } else {
                    BaseContextHandler.setAll(objectMap);
                }
            }
        });

        return executor;
    }
}
