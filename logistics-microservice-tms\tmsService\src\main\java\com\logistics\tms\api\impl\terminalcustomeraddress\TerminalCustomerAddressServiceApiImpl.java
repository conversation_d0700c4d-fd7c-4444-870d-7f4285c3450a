package com.logistics.tms.api.impl.terminalcustomeraddress;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.terminalcustomeraddress.TerminalCustomerAddressServiceApi;
import com.logistics.tms.api.feign.terminalcustomeraddress.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.terminalcustomeraddress.TerminalCustomerAddressBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/1/4 16:31
 */
@RestController
public class TerminalCustomerAddressServiceApiImpl implements TerminalCustomerAddressServiceApi {
    
    @Autowired
    private TerminalCustomerAddressBiz terminalCustomerAddressBiz;

    /**
     * 终端客户地址列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<SearchTerminalCustomerAddressListResponseModel>> searchTerminalCustomerAddressList(@RequestBody SearchTerminalCustomerAddressListRequestModel requestModel) {
        return Result.success(terminalCustomerAddressBiz.searchTerminalCustomerAddressList(requestModel));
    }

    /**
     * 导出终端客户地址列表
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchTerminalCustomerAddressListResponseModel>> exportTerminalCustomerAddressList(@RequestBody SearchTerminalCustomerAddressListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        PageInfo<SearchTerminalCustomerAddressListResponseModel> pageInfo = terminalCustomerAddressBiz.searchTerminalCustomerAddressList(requestModel);
        return Result.success(pageInfo.getList());
    }

    /**
     * 新增/修改终端客户地址
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> addOrModifyTerminalCustomerAddress(@RequestBody AddOrModifyTerminalCustomerAddressRequestModel requestModel) {
        terminalCustomerAddressBiz.addOrModifyTerminalCustomerAddress(requestModel);
        return Result.success(true);
    }

    /**
     * 终端客户地址详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<GetTerminalCustomerAddressDetailResponseModel> getTerminalCustomerAddressDetail(@RequestBody GetTerminalCustomerAddressDetailRequestModel requestModel) {
        return Result.success(terminalCustomerAddressBiz.getTerminalCustomerAddressDetail(requestModel));
    }

    /**
     * 批量删除终端客户地址
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> delTerminalCustomerAddress(@RequestBody DeleteTerminalCustomerAddressRequestModel requestModel) {
        terminalCustomerAddressBiz.delTerminalCustomerAddress(requestModel);
        return Result.success(true);
    }
}
