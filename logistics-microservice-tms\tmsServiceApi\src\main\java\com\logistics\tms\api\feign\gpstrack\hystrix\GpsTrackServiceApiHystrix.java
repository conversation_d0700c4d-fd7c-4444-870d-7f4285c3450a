package com.logistics.tms.api.feign.gpstrack.hystrix;

import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.gpstrack.GpsTrackServiceApi;
import com.logistics.tms.api.feign.gpstrack.model.*;
import org.springframework.stereotype.Component;

/**
 * liang current user system login name
 * 2018/9/18 current system date
 */
@Component("tmsGpsTrackServiceApiHystrix")
public class GpsTrackServiceApiHystrix implements GpsTrackServiceApi {

    @Override
    public Result<AllVehicleTrackInfoResponseModel> getVehicleTrackInfoList(AllVehicleTrackInfoRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SearchCarrierOrderDestinationByVehicleNoResponseModel> getDestinationByVehicleNo(SearchCarrierOrderDestinationByVehicleNoRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<OpGpHisTrackResponseModel> getVehicleTrackHistory(OpGpHisTrackRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result refreshLocation() {
        return Result.timeout();
    }
}
