package com.logistics.tms.api.feign.terminalreachmanagement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SearchReachManagementListResponseModel {

    @ApiModelProperty(value = "终端客户触达管理id")
    private Long reachManagementId;

    @ApiModelProperty(value = "运单id")
    private Long carrierOrderId;

    @ApiModelProperty(value = "运单号")
    private String carrierOrderCode;

    @ApiModelProperty(value = "发货联系人")
    private String consignorName;
    @ApiModelProperty(value = "发货联系人")
    private String consignorMobile;

    @ApiModelProperty(value = "发货仓库")
    private String loadWarehouse;

    @ApiModelProperty(value = "发货详细地址（发货省市区+详细地址）")
    private String loadProvinceName;
    @ApiModelProperty(value = "发货详细地址（发货省市区+详细地址）")
    private String loadCityName;
    @ApiModelProperty(value = "发货详细地址（发货省市区+详细地址）")
    private String loadAreaName;
    @ApiModelProperty(value = "发货详细地址（发货省市区+详细地址）")
    private String loadDetailAddress;

    @ApiModelProperty(value = "触达司机")
    private String reachDriverName;
    @ApiModelProperty(value = "触达司机")
    private String reachDriverPhone;

    @ApiModelProperty(value = "上游客户")
    private String upstreamCustomer;

    @ApiModelProperty(value = "触达抬头")
    private String terminalHead;

    @ApiModelProperty(value = "触达地址")
    private String reachProvinceName;
    @ApiModelProperty(value = "触达地址")
    private String reachCityName;
    @ApiModelProperty(value = "触达地址")
    private String reachAreaName;
    @ApiModelProperty(value = "触达地址")
    private String reachAddressDetail;

    @ApiModelProperty(value = "距离偏差（km）")
    private Double distanceDeviation;

    @ApiModelProperty(value = "联系人校验 1无误 2有误")
    private Integer checkReachContact;

    @ApiModelProperty(value = "现场联系人")
    private String reachContactor;
    @ApiModelProperty(value = "现场联系人")
    private String reachTelephone;

    @ApiModelProperty(value = "空置托盘数量")
    private Long emptyTraysAmount;

    @ApiModelProperty(value = "带料共享托盘数量")
    private Long employTraysAmount;

    @ApiModelProperty(value = "触达时间")
    private Date reachTime;

    @ApiModelProperty(value = "操作人")
    private String lastModifiedBy;

    @ApiModelProperty(value = "操作时间")
    private Date lastModifiedTime;

}
