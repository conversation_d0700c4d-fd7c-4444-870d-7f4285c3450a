package com.logistics.appapi.client.homepage;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.homepage.hystrix.HomePageClientHystrix;
import com.logistics.appapi.client.homepage.response.HomeOrderCollectResponseModel;
import com.logistics.appapi.client.homepage.response.VerifyWarehousePermissionResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * @author: wjf
 * @date: 2024/3/7 14:32
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        fallback = HomePageClientHystrix.class)
public interface HomePageClient {

    /**
     * 首页单据汇总
     *
     * @return
     */
    @ApiOperation("首页单据汇总")
    @PostMapping(value = "/service/applet/homePage/homeOrderCollect")
    Result<HomeOrderCollectResponseModel> homeOrderCollect();

    /**
     * 验证是否可以跳转云仓小程序
     *
     * @return
     */
    @ApiOperation(value = "验证是否可以跳转云仓小程序")
    @PostMapping(value = "/service/applet/homePage/verifyWarehousePermission")
    Result<VerifyWarehousePermissionResponseModel> verifyWarehousePermission();
}
