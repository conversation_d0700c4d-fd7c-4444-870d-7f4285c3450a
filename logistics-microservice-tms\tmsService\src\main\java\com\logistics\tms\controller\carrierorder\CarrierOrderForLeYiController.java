package com.logistics.tms.controller.carrierorder;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.controller.carrierorder.response.UpdateUnloadAddressDetailResponseModel;
import com.logistics.tms.controller.carrierorderapplet.request.CarrierOrderIdRequestModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.carrierorder.CarrierOrderForLeYiBiz;
import com.logistics.tms.controller.carrierorder.request.*;
import com.logistics.tms.controller.carrierorder.response.*;
import com.logistics.tms.controller.carrierorderapplet.request.VerifyEnablePickUpMoreReqModel;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2023/9/26 13:41
 */
@RestController
public class CarrierOrderForLeYiController {

    @Resource
    private CarrierOrderForLeYiBiz carrierOrderForLeYiBiz;

    /**
     * 查询云盘运单列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查询云盘运单列表 ")
    @PostMapping(value = "/service/carrierOrderManagement/searchCarrierOrderListForLeYi")
    public Result<PageInfo<SearchCarrierOrderListForLeYiResponseModel>> searchCarrierOrderListForLeYi(@RequestBody SearchCarrierOrderListForLeYiRequestModel requestModel) {
        return Result.success(carrierOrderForLeYiBiz.searchCarrierOrderListForLeYi(requestModel));
    }

    /**
     * 导出云盘运单列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出云盘运单列表 ")
    @PostMapping(value = "/service/carrierOrderManagement/exportCarrierOrderListForLeYi")
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchCarrierOrderListForLeYiResponseModel>> exportCarrierOrderListForLeYi(@RequestBody SearchCarrierOrderListForLeYiRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        return Result.success(carrierOrderForLeYiBiz.searchCarrierOrderListForLeYi(requestModel).getList());
    }

    /**
     * 云盘待审核车辆数
     * @param requestModel
     * @return
     */
    @ApiOperation(value="云盘待审核车辆数")
    @PostMapping(value = "/service/management/carrierOrderManagement/getWaitAuditVehicleCountInfoForLeYi")
    public Result<WaitAuditVehicleInfoResponseModel> getWaitAuditVehicleCountInfoForLeYi(@RequestBody WaitAuditVehicleInfoRequestModel requestModel) {
        return Result.success(carrierOrderForLeYiBiz.getWaitAuditVehicleCountInfoForLeYi(requestModel));
    }

    /**
     * 云盘运单详情页
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "云盘运单详情页")
    @PostMapping(value = "/service/carrierOrderManagement/carrierOrderDetailForLeYi")
    public Result<CarrierOrderDetailForLeYiResponseModel> carrierOrderDetailForLeYi(@RequestBody CarrierOrderDetailRequestModel requestModel) {
        return Result.success(carrierOrderForLeYiBiz.carrierOrderDetailForLeYi(requestModel));
    }

    /**
     * 云盘运单修改地址详情
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "云盘运单修改地址详情")
    @PostMapping(value = "/service/management/carrierOrderManagement/updateCarrierOrderUnloadAddressDetail")
    public Result<List<UpdateUnloadAddressDetailResponseModel>> updateCarrierOrderUnloadAddressDetail(@RequestBody CarrierOrderUpUnloadAddrRequestModel requestModel) {
        return Result.success(carrierOrderForLeYiBiz.updateCarrierOrderUnloadAddressDetail(requestModel));
    }

    /**
     * 云盘运单确认修改地址
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "云盘运单确认修改地址")
    @PostMapping(value = "/service/management/carrierOrderManagement/updateCarrierOrderUnloadAddressConfirm")
    public Result<Boolean> updateCarrierOrderUnloadAddressConfirm(@RequestBody UpdateCarrierOrderUnloadAddressConfirmRequestModel requestModel) {
        carrierOrderForLeYiBiz.updateCarrierOrderUnloadAddressConfirm(requestModel);
        return Result.success(true);
    }

    /**
     * 云盘运单纠错详情
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "云盘运单纠错详情")
    @PostMapping(value = "/service/management/carrierOrderManagement/carrierOrderCorrectDetail")
    public Result<CarrierOrderCorrectDetailResponseModel> carrierOrderCorrectDetail(@RequestBody CarrierOrderCorrectDetailRequestModel requestModel) {
        return Result.success(carrierOrderForLeYiBiz.carrierOrderCorrectDetail(requestModel));
    }

    /**
     * 云盘运单获取客户单号详情
     *
     * @param requestModel 运单号
     * @return 客户单号详情
     */
    @ApiOperation(value = "云盘运单获取客户单号详情")
    @PostMapping(value = "/service/management/carrierOrderManagement/getCarrierOrderOrders")
    public Result<List<CarrierOrderOrdersResponseModel>> getCarrierOrderOrders(@RequestBody CarrierOrderIdRequestModel requestModel) {
        return Result.success(carrierOrderForLeYiBiz.getCarrierOrderOrders(requestModel));
    }

    /**
     * 云盘运单确认纠错
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "云盘运单确认纠错")
    @PostMapping(value = "/service/management/carrierOrderManagement/carrierOrderCorrectConfirm")
    public Result<Boolean> carrierOrderCorrectConfirm(@RequestBody CarrierOrderCorrectConfirmRequestModel requestModel) {
        carrierOrderForLeYiBiz.carrierOrderCorrectConfirm(requestModel);
        return Result.success(true);
    }

    /**
     * 运单放空详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "运单放空详情")
    @PostMapping(value = "/service/management/carrierOrderManagement/carrierOrderEmptyDetail")
    public Result<CarrierOrderEmptyDetailResponseModel> carrierOrderEmptyDetail(@RequestBody CarrierOrderDetailRequestModel requestModel) {
        return Result.success(carrierOrderForLeYiBiz.carrierOrderEmptyDetail(requestModel));
    }

    /**
     * 确认放空
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "确认放空")
    @PostMapping(value = "/service/management/carrierOrderManagement/confirmEmpty")
    public Result<Boolean> confirmEmpty(@RequestBody CarrierOrderEmptyRequestModel requestModel) {
        carrierOrderForLeYiBiz.confirmEmpty(requestModel);
        return Result.success(true);
    }

    /**
     * 复制运单信息
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "复制运单信息")
    @PostMapping(value = "/service/management/carrierOrderManagement/copyCarrierOrder")
    public Result<CopyCarrierOrderResponseModel> copyCarrierOrder(@RequestBody CarrierOrderIdRequestModel requestModel) {
        return Result.success(carrierOrderForLeYiBiz.copyCarrierOrder(requestModel));
    }

    /**
     * 云盘物流看板-平均物流费用
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "云盘物流看板-平均物流费用")
    @PostMapping(value = "/service/management/carrierOrderManagement/logisticsCostStatistics")
    public Result<List<LogisticsCostStatisticsResponseModel>> logisticsCostStatistics(@RequestBody LogisticsCostStatisticsRequestModel requestModel) {
        return Result.success(carrierOrderForLeYiBiz.logisticsCostStatistics(requestModel));
    }

    /**
     * 云盘物流看板-平均提货时效
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "云盘物流看板-平均提货时效")
    @PostMapping(value = "/service/management/carrierOrderManagement/logisticsLoadValidityStatistics")
    public Result<List<LogisticsLoadValidityStatisticsResponseModel>> logisticsLoadValidityStatistics(@RequestBody LogisticsLoadValidityStatisticsRequestModel requestModel) {
        return Result.success(carrierOrderForLeYiBiz.logisticsLoadValidityStatistics(requestModel));
    }

    /**
     * 云盘物流看板-待提货、待纠错
     *
     * @param
     */
    @ApiOperation(value = "云盘物流看板-待提货、待纠错")
    @PostMapping(value = "/service/management/waitCorrectStatistics")
    public Result<WaitCorrectStatisticsResponseModel> waitCorrectStatistics() {
        return Result.success(carrierOrderForLeYiBiz.waitCorrectStatistics());
    }

    /**
     * 云盘运单签收详情
     *
     * @param requestModel 运单id
     * @return 运单签收详情
     */
    @ApiOperation(value = "云盘运单签收详情 ")
    @PostMapping(value = "/service/carrierOrderManagement/carrierOrderListBeforeSignUpForLeyi")
    public Result<List<CarrierOrderListBeforeSignUpForLeyiResponseModel>> carrierOrderListBeforeSignUpForLeyi(@RequestBody CarrierOrderListBeforeSignUpRequestModel requestModel) {
        requestModel.setShowDetail(true);
        return Result.success(carrierOrderForLeYiBiz.carrierOrderListBeforeSignUpForLeyi(requestModel));
    }

    /**
     * 云盘运单签收
     *
     * @param requestModel 签收详情
     * @return 操作结果
     */
    @ApiOperation(value = "云盘运单签收 ")
    @PostMapping(value = "/service/carrierOrderManagement/carrierOrderSignUpForLeyi")
    public Result<Boolean> carrierOrderSignUpForLeyi(@RequestBody CarrierOrderConfirmSignUpForLeyiRequestModel requestModel) {
        carrierOrderForLeYiBiz.carrierOrderSignUpForLeyi(requestModel);
        return Result.success(true);
    }

    /**
     * 云盘运单详情编辑费用-详情查询
     *
     * @param requestModel 运单id,费用类型
     * @return 费用信息
     */
    @ApiOperation(value = "云盘运单详情编辑费用-详情查询")
    @PostMapping(value = "/service/carrierOrderManagement/editCarrierOrderCostDetail")
    public Result<EditCarrierOrderCostDetailResponseModel> editCarrierOrderCostDetail(@RequestBody EditCarrierOrderCostDetailRequestModel requestModel) {
        return Result.success(carrierOrderForLeYiBiz.editCarrierOrderCostDetail(requestModel));
    }

    /**
     * 云盘运单详情编辑费用
     *
     * @param requestModel 费用信息
     * @return 操作结果
     */
    @ApiOperation(value = "云盘运单详情编辑费用")
    @PostMapping(value = "/service/carrierOrderManagement/editCarrierOrderCost")
    public Result<Boolean> editCarrierOrderCost(@RequestBody EditCarrierOrderCostRequestModel requestModel) {
        carrierOrderForLeYiBiz.editCarrierOrderCost(requestModel);
        return Result.success(true);
    }

    /**
     * 根据车主id查询有效的云盘运单（排除已取消、已放空）
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "根据车主id查询有效的云盘运单（排除已取消、已放空）")
    @PostMapping(value = "/service/carrierOrderManagement/getValidCarrierOrder")
    public Result<PageInfo<GetValidCarrierOrderResponseModel>> getValidCarrierOrder(@RequestBody GetValidCarrierOrderRequestModel requestModel) {
        return Result.success(carrierOrderForLeYiBiz.getValidCarrierOrder(requestModel));
    }

    /**
     * 判断当前运单是否可以取消
     *
     * @param requestModel 运单号
     * @return 是否可取消
     */
    @ApiOperation(value = "是否可取消运单判断(云仓,云盘调用)")
    @PostMapping(value = "/service/carrierOrderManagement/externalCanCancelCarrierOrder")
    public Result<Boolean> externalCanCancelCarrierOrder(@RequestBody ExternalCancelCarrierOrderRequestModel requestModel) {
        carrierOrderForLeYiBiz.externalCanCancelCarrierOrder(requestModel);
        return Result.success(true);
    }

    /**
     * 云仓,云盘取消  发货-回收出库 的运单
     *
     * @param requestModel 运单号
     * @return 操作结果
     */
    @ApiOperation(value = "取消运单(云仓,云盘调用)")
    @PostMapping(value = "/service/carrierOrderManagement/externalCancelCarrierOrder")
    public Result<Boolean> externalCancelCarrierOrder(@RequestBody ExternalCancelCarrierOrderRequestModel requestModel) {
        carrierOrderForLeYiBiz.externalCancelCarrierOrder(requestModel);
        return Result.success(true);
    }

    /**
     * 根据运单号查询运单上车辆司机信息（云盘调用）
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/service/carrierOrderManagement/getDriverVehicleInfoByCode")
    public Result<List<GetDriverVehicleInfoByCodeResponseModel>> getDriverVehicleInfoByCode(@RequestBody GetDriverVehicleInfoByCodeRequestModel requestModel){
        return Result.success(carrierOrderForLeYiBiz.getDriverVehicleInfoByCode(requestModel));
    }

    @ApiOperation(value = "查询云盘二维码")
    @PostMapping(value = "/service/carrierOrderManagement/getLeYiQrCode")
    public Result<GetLeYiQrCodeResponseModel> getLeYiQrCode(@RequestBody GetLeYiQrCodeRequestModel requestModel){
        return Result.success(carrierOrderForLeYiBiz.getLeYiQrCode(requestModel));
    }

    @ApiOperation(value = "校验接口是否能满足多提接口")
    @PostMapping(value = "/service/carrierOrderManagement/verifyEnablePickUpMore")
    public Result<Boolean> verifyEnablePickUpMore(@RequestBody VerifyEnablePickUpMoreReqModel requestModel) {
        return Result.success(carrierOrderForLeYiBiz.verifyEnablePickUpMore(requestModel));
    }

}
