<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandOrderCustomerRelMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDemandOrderCustomerRel" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT" />
    <result column="tray_customer_company_id" property="trayCustomerCompanyId" jdbcType="BIGINT" />
    <result column="tray_customer_address_id" property="trayCustomerAddressId" jdbcType="BIGINT" />
    <result column="product_category_id" property="productCategoryId" jdbcType="BIGINT" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, demand_order_id, tray_customer_company_id, tray_customer_address_id, product_category_id, 
    created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_demand_order_customer_rel
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_demand_order_customer_rel
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDemandOrderCustomerRel" >
    insert into t_demand_order_customer_rel (id, demand_order_id, tray_customer_company_id, 
      tray_customer_address_id, product_category_id, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{demandOrderId,jdbcType=BIGINT}, #{trayCustomerCompanyId,jdbcType=BIGINT}, 
      #{trayCustomerAddressId,jdbcType=BIGINT}, #{productCategoryId,jdbcType=BIGINT}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDemandOrderCustomerRel" >
    insert into t_demand_order_customer_rel
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="demandOrderId != null" >
        demand_order_id,
      </if>
      <if test="trayCustomerCompanyId != null" >
        tray_customer_company_id,
      </if>
      <if test="trayCustomerAddressId != null" >
        tray_customer_address_id,
      </if>
      <if test="productCategoryId != null" >
        product_category_id,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="demandOrderId != null" >
        #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="trayCustomerCompanyId != null" >
        #{trayCustomerCompanyId,jdbcType=BIGINT},
      </if>
      <if test="trayCustomerAddressId != null" >
        #{trayCustomerAddressId,jdbcType=BIGINT},
      </if>
      <if test="productCategoryId != null" >
        #{productCategoryId,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDemandOrderCustomerRel" >
    update t_demand_order_customer_rel
    <set >
      <if test="demandOrderId != null" >
        demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="trayCustomerCompanyId != null" >
        tray_customer_company_id = #{trayCustomerCompanyId,jdbcType=BIGINT},
      </if>
      <if test="trayCustomerAddressId != null" >
        tray_customer_address_id = #{trayCustomerAddressId,jdbcType=BIGINT},
      </if>
      <if test="productCategoryId != null" >
        product_category_id = #{productCategoryId,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDemandOrderCustomerRel" >
    update t_demand_order_customer_rel
    set demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      tray_customer_company_id = #{trayCustomerCompanyId,jdbcType=BIGINT},
      tray_customer_address_id = #{trayCustomerAddressId,jdbcType=BIGINT},
      product_category_id = #{productCategoryId,jdbcType=BIGINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>