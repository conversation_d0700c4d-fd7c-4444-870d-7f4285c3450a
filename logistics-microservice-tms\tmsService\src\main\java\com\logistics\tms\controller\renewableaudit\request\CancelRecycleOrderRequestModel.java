package com.logistics.tms.controller.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/1
 */
@Data
public class CancelRecycleOrderRequestModel {

	@ApiModelProperty("回收申请单号")
	private String recycleOrderCode;

	@ApiModelProperty("操作类型: 1:取消")
	private Integer operationType;

	@ApiModelProperty("操作人")
	private String operator;

	@ApiModelProperty("操作时间")
	private Date operationTime;
}
