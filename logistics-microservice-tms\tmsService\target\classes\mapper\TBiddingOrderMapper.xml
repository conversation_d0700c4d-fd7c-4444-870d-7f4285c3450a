<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TBiddingOrderMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TBiddingOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bidding_order_code" jdbcType="VARCHAR" property="biddingOrderCode" />
    <result column="bidding_status" jdbcType="INTEGER" property="biddingStatus" />
    <result column="if_cancel" jdbcType="INTEGER" property="ifCancel" />
    <result column="company_carrier_range" jdbcType="INTEGER" property="companyCarrierRange" />
    <result column="quote_start_time" jdbcType="TIMESTAMP" property="quoteStartTime" />
    <result column="quote_duration" jdbcType="INTEGER" property="quoteDuration" />
    <result column="quote_end_time" jdbcType="TIMESTAMP" property="quoteEndTime" />
    <result column="expected_load_time" jdbcType="TIMESTAMP" property="expectedLoadTime" />
    <result column="expected_unload_time" jdbcType="TIMESTAMP" property="expectedUnloadTime" />
    <result column="vehicle_length_id" jdbcType="BIGINT" property="vehicleLengthId" />
    <result column="vehicle_length" jdbcType="DECIMAL" property="vehicleLength" />
    <result column="lowest_price" jdbcType="DECIMAL" property="lowestPrice" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, bidding_order_code, bidding_status, if_cancel, company_carrier_range, quote_start_time, 
    quote_duration, quote_end_time, expected_load_time, expected_unload_time, vehicle_length_id, 
    vehicle_length, lowest_price, remark, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_bidding_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_bidding_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TBiddingOrder">
    insert into t_bidding_order (id, bidding_order_code, bidding_status, 
      if_cancel, company_carrier_range, quote_start_time, 
      quote_duration, quote_end_time, expected_load_time, 
      expected_unload_time, vehicle_length_id, vehicle_length, 
      lowest_price, remark, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{biddingOrderCode,jdbcType=VARCHAR}, #{biddingStatus,jdbcType=INTEGER}, 
      #{ifCancel,jdbcType=INTEGER}, #{companyCarrierRange,jdbcType=INTEGER}, #{quoteStartTime,jdbcType=TIMESTAMP}, 
      #{quoteDuration,jdbcType=INTEGER}, #{quoteEndTime,jdbcType=TIMESTAMP}, #{expectedLoadTime,jdbcType=TIMESTAMP}, 
      #{expectedUnloadTime,jdbcType=TIMESTAMP}, #{vehicleLengthId,jdbcType=BIGINT}, #{vehicleLength,jdbcType=DECIMAL}, 
      #{lowestPrice,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TBiddingOrder">
    insert into t_bidding_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="biddingOrderCode != null">
        bidding_order_code,
      </if>
      <if test="biddingStatus != null">
        bidding_status,
      </if>
      <if test="ifCancel != null">
        if_cancel,
      </if>
      <if test="companyCarrierRange != null">
        company_carrier_range,
      </if>
      <if test="quoteStartTime != null">
        quote_start_time,
      </if>
      <if test="quoteDuration != null">
        quote_duration,
      </if>
      <if test="quoteEndTime != null">
        quote_end_time,
      </if>
      <if test="expectedLoadTime != null">
        expected_load_time,
      </if>
      <if test="expectedUnloadTime != null">
        expected_unload_time,
      </if>
      <if test="vehicleLengthId != null">
        vehicle_length_id,
      </if>
      <if test="vehicleLength != null">
        vehicle_length,
      </if>
      <if test="lowestPrice != null">
        lowest_price,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="biddingOrderCode != null">
        #{biddingOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="biddingStatus != null">
        #{biddingStatus,jdbcType=INTEGER},
      </if>
      <if test="ifCancel != null">
        #{ifCancel,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierRange != null">
        #{companyCarrierRange,jdbcType=INTEGER},
      </if>
      <if test="quoteStartTime != null">
        #{quoteStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="quoteDuration != null">
        #{quoteDuration,jdbcType=INTEGER},
      </if>
      <if test="quoteEndTime != null">
        #{quoteEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectedLoadTime != null">
        #{expectedLoadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectedUnloadTime != null">
        #{expectedUnloadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleLengthId != null">
        #{vehicleLengthId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLength != null">
        #{vehicleLength,jdbcType=DECIMAL},
      </if>
      <if test="lowestPrice != null">
        #{lowestPrice,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TBiddingOrder">
    update t_bidding_order
    <set>
      <if test="biddingOrderCode != null">
        bidding_order_code = #{biddingOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="biddingStatus != null">
        bidding_status = #{biddingStatus,jdbcType=INTEGER},
      </if>
      <if test="ifCancel != null">
        if_cancel = #{ifCancel,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierRange != null">
        company_carrier_range = #{companyCarrierRange,jdbcType=INTEGER},
      </if>
      <if test="quoteStartTime != null">
        quote_start_time = #{quoteStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="quoteDuration != null">
        quote_duration = #{quoteDuration,jdbcType=INTEGER},
      </if>
      <if test="quoteEndTime != null">
        quote_end_time = #{quoteEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectedLoadTime != null">
        expected_load_time = #{expectedLoadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectedUnloadTime != null">
        expected_unload_time = #{expectedUnloadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleLengthId != null">
        vehicle_length_id = #{vehicleLengthId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLength != null">
        vehicle_length = #{vehicleLength,jdbcType=DECIMAL},
      </if>
      <if test="lowestPrice != null">
        lowest_price = #{lowestPrice,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TBiddingOrder">
    update t_bidding_order
    set bidding_order_code = #{biddingOrderCode,jdbcType=VARCHAR},
      bidding_status = #{biddingStatus,jdbcType=INTEGER},
      if_cancel = #{ifCancel,jdbcType=INTEGER},
      company_carrier_range = #{companyCarrierRange,jdbcType=INTEGER},
      quote_start_time = #{quoteStartTime,jdbcType=TIMESTAMP},
      quote_duration = #{quoteDuration,jdbcType=INTEGER},
      quote_end_time = #{quoteEndTime,jdbcType=TIMESTAMP},
      expected_load_time = #{expectedLoadTime,jdbcType=TIMESTAMP},
      expected_unload_time = #{expectedUnloadTime,jdbcType=TIMESTAMP},
      vehicle_length_id = #{vehicleLengthId,jdbcType=BIGINT},
      vehicle_length = #{vehicleLength,jdbcType=DECIMAL},
      lowest_price = #{lowestPrice,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>