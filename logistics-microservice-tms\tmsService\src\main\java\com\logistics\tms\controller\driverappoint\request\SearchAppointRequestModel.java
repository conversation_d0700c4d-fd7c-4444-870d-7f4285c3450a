package com.logistics.tms.controller.driverappoint.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchAppointRequestModel extends AbstractPageForm<SearchAppointRequestModel> {

    @ApiModelProperty(value = "预约日期 yyyy-MM")
    private String appointDate;

    @ApiModelProperty(value = "关联车辆状态：0 未关联，1 已关联")
    private Integer associatedVehicleStatus;
}
