package com.logistics.tms.biz.workgroup.sendmsg.event;

import com.logistics.tms.base.enums.DemandOrderSourceEnum;
import com.logistics.tms.base.enums.WorkGroupOrderTypeEnum;
import com.logistics.tms.biz.workgroup.sendmsg.AbstractWorkGroupPush;
import com.logistics.tms.biz.workgroup.sendmsg.model.WorkGroupEventModel;
import com.logistics.tms.client.feign.basicdata.BasicServiceClient;
import com.logistics.tms.client.feign.basicdata.wechat.request.PushMessageRequestModel;
import com.yelo.tools.utils.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WorkGroupEventListener {

    @Resource
    private List<AbstractWorkGroupPush> abstractWorkGroupPushes;
    @Resource
    private TaskExecutor workGroupPushTaskExecutor;
    @Resource
    private BasicServiceClient basicServiceClient;

    @Async
    @TransactionalEventListener
    public void eventListener(WorkGroupEventModel event) {
        Map<WorkGroupOrderTypeEnum, AbstractWorkGroupPush> handlerMap = abstractWorkGroupPushes
                .stream()
                .collect(Collectors.toMap(AbstractWorkGroupPush::getOrderType, Function.identity()));
        // 构建消息
        List<PushMessageRequestModel> pushMessageRequestModels = Collections.synchronizedList(Lists.newArrayList());
        var futures = event.getWorkGroupPushBoModels()
                .stream()
                .filter(f -> DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(f.getOrderSource()))
                .filter(f -> handlerMap.containsKey(f.getOrderType()))
                .map(f -> CompletableFuture.supplyAsync(() -> handlerMap.get(f.getOrderType()).builderWorkGroupPushMessage(f),
                                workGroupPushTaskExecutor)
                        .handle((res, error) -> {
                            if (ListUtils.isNotEmpty(res)) {
                                pushMessageRequestModels.addAll(res);
                            } else if (error != null) {
                                log.error("【智能推送】 -------------------- {}ID【{}】【{}】节点",
                                        f.getOrderType().getValue(),
                                        f.getOrderId(),
                                        f.getOrderNode().getValue(),
                                        error);
                            }
                            return null;
                        }))
                .toArray(CompletableFuture[]::new);
        CompletableFuture.allOf(futures).join();
        log.info("【智能推送】发送消息："+pushMessageRequestModels);
        basicServiceClient.workGroupPushMessage(pushMessageRequestModels);
    }
}
