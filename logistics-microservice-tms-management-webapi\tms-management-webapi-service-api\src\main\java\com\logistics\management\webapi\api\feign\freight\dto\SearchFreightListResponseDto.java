package com.logistics.management.webapi.api.feign.freight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 运价管理
 * @Author: sj
 * @Date: 2019/12/24 13:04
 */
@Data
public class SearchFreightListResponseDto {
    @ApiModelProperty("运价ID")
    private String freightId;
    @ApiModelProperty("业务表公司ID")
    private String companyId;
    @ApiModelProperty("业务表公司名称")
    private String companyName;
    @ApiModelProperty("启用 1 禁用 0")
    private String enabled;
    @ApiModelProperty("启用/禁用文本")
    private String enabledLabel;
}
