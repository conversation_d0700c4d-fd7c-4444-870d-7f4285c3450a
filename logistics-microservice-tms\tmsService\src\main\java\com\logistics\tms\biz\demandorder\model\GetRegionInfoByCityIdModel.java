package com.logistics.tms.biz.demandorder.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author:lei.zhu
 * @date:2021/10/28 10:46:57
 */
@Data
public class GetRegionInfoByCityIdModel {
    @ApiModelProperty("大区id")
    private Long regionId;
    @ApiModelProperty("城市id")
    private Long cityId;
    @ApiModelProperty("大区名称")
    private String regionName;
    @ApiModelProperty("大区联系人名称")
    private String regionContactName;
    @ApiModelProperty("大区联系人手机号")
    private String regionContactPhone;
    @ApiModelProperty("需求单id")
    private Long demandId;
}
