package com.logistics.tms.mapper;

import com.logistics.tms.controller.workordercenter.request.WorkOrderDetailAppletRequestModel;
import com.logistics.tms.controller.workordercenter.request.WorkOrderDetailRequestModel;
import com.logistics.tms.controller.workordercenter.request.WorkOrderListAppletRequestModel;
import com.logistics.tms.controller.workordercenter.request.WorkOrderListRequestModel;
import com.logistics.tms.controller.workordercenter.response.WorkOrderDetailAppletResponseModel;
import com.logistics.tms.controller.workordercenter.response.WorkOrderDetailResponseModel;
import com.logistics.tms.controller.workordercenter.response.WorkOrderListAppletResponseModel;
import com.logistics.tms.controller.workordercenter.response.WorkOrderListResponseModel;
import com.logistics.tms.biz.demandorder.model.WorkOrderPendingOrderModel;
import com.logistics.tms.biz.workordercenter.model.WorkOrderExceptionModel;
import com.logistics.tms.entity.TWorkOrder;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2023/04/14
*/
@Mapper
public interface TWorkOrderMapper extends BaseMapper<TWorkOrder> {

	TWorkOrder selectByPrimaryKeyDecrypt(Long id);

	int insertSelectiveEncrypt(TWorkOrder tWorkOrder);

	int updateByPrimaryKeySelectiveEncrypt(TWorkOrder tWorkOrder);

	List<WorkOrderExceptionModel> checkExceptionExist(@Param("demandOrderIds") String demandOrderIds, @Param("carrierOrderIds") String carrierOrderIds);

	List<WorkOrderListResponseModel> searchWorkOrderList(WorkOrderListRequestModel requestModel);

	WorkOrderDetailResponseModel searchWorkOrderDetailById(WorkOrderDetailRequestModel requestModel);

	List<WorkOrderPendingOrderModel> selectPendingOrderIdByCarrierId(@Param("companyCarrierId") Long companyCarrierId);

	List<WorkOrderPendingOrderModel> selectAllPendingOrderIds();

	List<TWorkOrder> selectAllDecryptByOrderIdAndOrderType(@Param("orderId") Long orderId, @Param("orderType") Integer orderType);

	List<TWorkOrder> selectWorkOrderByCarrierIds(@Param("carrierOrderIdList") List<Long> carrierOrderIdList);

	List<TWorkOrder> selectWorkOrderByDemandIds(@Param("demandOrderIdList") List<Long> demandOrderIdList);

	Integer selectPendingOrderCountByDriverId(@Param("driverId") Long driverId);

	List<Long> selectPendingOrderIdByDriverIds(@Param("driverIdList") List<Long> driverIdList);

    List<WorkOrderListAppletResponseModel> searchWorkOrderListForApplet(WorkOrderListAppletRequestModel requestModel);

	WorkOrderDetailAppletResponseModel searchWorkOrderDetailForApplet(WorkOrderDetailAppletRequestModel requestModel);

	TWorkOrder selectOneByWorkOrderCode(@Param("workOrderCode") String workOrderCode);
}
