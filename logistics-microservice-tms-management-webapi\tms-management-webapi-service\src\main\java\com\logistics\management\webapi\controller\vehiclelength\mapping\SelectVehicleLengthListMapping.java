package com.logistics.management.webapi.controller.vehiclelength.mapping;

import com.logistics.management.webapi.client.vehiclelength.response.SelectVehicleLengthListResponseModel;
import com.logistics.management.webapi.controller.vehiclelength.response.SelectVehicleLengthListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2024/4/29 9:25
 */
public class SelectVehicleLengthListMapping extends MapperMapping<SelectVehicleLengthListResponseModel, SelectVehicleLengthListResponseDto> {
    @Override
    public void configure() {
        SelectVehicleLengthListResponseModel source = getSource();
        SelectVehicleLengthListResponseDto destination = getDestination();

        destination.setVehicleLength(source.getVehicleLength().stripTrailingZeros().toPlainString());
    }
}
