package com.logistics.tms.controller.vehiclesettlement;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.vehiclesettlement.VehicleSettlementBiz;
import com.logistics.tms.controller.vehiclesettlement.request.*;
import com.logistics.tms.controller.vehiclesettlement.response.*;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/2/21 14:10
 */
@RestController
@Api(value = "车辆结算管理")
@RequestMapping(value = "/service/vehicleSettlement")
public class VehicleSettlementController {

    @Resource
    private VehicleSettlementBiz vehicleSettlementBiz;

    /**
     * 车辆结算列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆结算列表")
    @PostMapping(value = "/searchVehicleSettlementList")
    public Result<PageInfo<SearchVehicleSettlementListResponseModel>> searchVehicleSettlementList(@RequestBody SearchVehicleSettlementListRequestModel requestModel) {
        List<SearchVehicleSettlementListResponseModel> list = vehicleSettlementBiz.searchVehicleSettlementList(requestModel.enablePaging());
        PageInfo pageInfo = new PageInfo(list);
        return Result.success(pageInfo);
    }

    /**
     * 车辆结算列表数量
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆结算列表数量")
    @PostMapping(value = "/searchVehicleSettlementListCount")
    public Result<SearchVehicleSettlementListCountResponseModel> searchVehicleSettlementListCount(@RequestBody SearchVehicleSettlementListRequestModel requestModel) {
        return Result.success(vehicleSettlementBiz.searchVehicleSettlementListCount(requestModel));
    }

    /**
     * 车辆结算详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆结算详情")
    @PostMapping(value = "/getVehicleSettlementDetail")
    public Result<GetVehicleSettlementDetailResponseModel> getVehicleSettlementDetail(@RequestBody VehicleSettlementDetailRequestModel requestModel) {
        return Result.success(vehicleSettlementBiz.getVehicleSettlementDetail(requestModel));
    }

    /**
     * 导出车辆结算列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出车辆结算列表")
    @PostMapping(value = "/exportVehicleSettlement")
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchVehicleSettlementListResponseModel>> exportVehicleSettlement(@RequestBody SearchVehicleSettlementListRequestModel requestModel) {
        return Result.success(vehicleSettlementBiz.searchVehicleSettlementList(requestModel));
    }

    /**
     * 确认结算
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "确认结算")
    @PostMapping(value = "/confirmSettlement")
    public Result confirmSettlement(@RequestBody ConfirmSettlementRequestModel requestModel) {
        vehicleSettlementBiz.confirmSettlement(requestModel);
        return Result.success(true);
    }

    /**
     * 车辆结算看板
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆结算看板")
    @PostMapping(value = "/vehicleSettlementKanBan")
    public Result<List<VehicleSettlementKanBanResponseModel>> vehicleSettlementKanBan(@RequestBody VehicleSettlementKanBanRequestModel requestModel) {
        return Result.success(vehicleSettlementBiz.vehicleSettlementKanBan(requestModel));
    }

    /**
     * 定时任务：生成车辆结算数据（贷款、停车费、GPS费、保险费未结清）
     * @return
     */
    @ApiOperation(value = "提供给测试同学触发：生成车辆结算数据（贷款、停车费、GPS费、保险费未结清）")
    @PostMapping(value = "/generateVehicleSettlement")
    public Result generateVehicleSettlement() {
        vehicleSettlementBiz.generateVehicleSettlement();
        return Result.success(true);
    }

    /**
     * 无需确认/撤回详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "无需确认/撤回详情")
    @PostMapping(value = "/cancelVehicleSettlementDetail")
    public Result<CancelVehicleSettlementDetailResponseModel> cancelVehicleSettlementDetail(@RequestBody CancelVehicleSettlementDetailRequestModel requestModel) {
        return Result.success(vehicleSettlementBiz.cancelVehicleSettlementDetail(requestModel));
    }

    /**
     * 无需确认/撤回
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "无需确认/撤回")
    @PostMapping(value = "/cancelVehicleSettlement")
    public Result<Boolean> cancelVehicleSettlement(@RequestBody CancelVehicleSettlementRequestModel requestModel) {
        vehicleSettlementBiz.cancelVehicleSettlement(requestModel);
        return Result.success(true);
    }

    /**
     * 车辆结算列表-结清详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆结算列表-结清详情")
    @PostMapping(value = "/settleFreightDetail")
    public Result<SettleFreightDetailResponseModel> settleFreightDetail(@RequestBody  VehicleSettlementIdRequestModel requestModel) {
        return Result.success(vehicleSettlementBiz.settleFreightDetail(requestModel));
    }

    /**
     * 车辆结算列表-结清
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆结算列表-结清")
    @PostMapping(value = "/settleFreight")
    public Result<Boolean> settleFreight(@RequestBody  SettleFreightRequestModel requestModel) {
        vehicleSettlementBiz.settleFreight(requestModel);
        return Result.success(true);
    }

    /**
     * 车辆结算-发送司机-查询司机
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆结算-发送司机-查询司机")
    @PostMapping(value = "/getDriver")
    public Result<List<GetSettlementDriverResponseModel>> getDriver(@RequestBody GetSettlementDriverRequestModel requestModel) {
        return Result.success(vehicleSettlementBiz.getDriver(requestModel));
    }

    /**
     * 发送司机-确认
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "发送司机-确认")
    @PostMapping(value = "/confirmSendToDriver")
    public Result<Boolean> confirmSendToDriver(@RequestBody ConfirmSendToDriverRequestModel requestModel) {
        vehicleSettlementBiz.confirmSendToDriver(requestModel);
        return Result.success(true);
    }

    /**
     * 车辆结算列表-账单记录-查看
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆结算列表-账单记录-查看")
    @PostMapping(value = "/settlementStatementRecord")
    public Result<SettlementStatementRecordResponseModel> settlementStatementRecord(@RequestBody VehicleSettlementIdRequestModel requestModel) {
        return Result.success(vehicleSettlementBiz.settlementStatementRecord(requestModel));
    }

    /**
     * 车辆结算列表-处理-详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆结算列表-处理-详情")
    @PostMapping(value = "/settlementStatementHandleDetail")
    public Result<SettlementStatementHandleDetailResponseModel> settlementStatementHandleDetail(@RequestBody VehicleSettlementIdRequestModel requestModel) {
        return Result.success(vehicleSettlementBiz.settlementStatementHandleDetail(requestModel));
    }

    /**
     * 车辆结算列表-处理
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆结算列表-处理")
    @PostMapping(value = "/settlementStatementHandle")
    public Result<Boolean> settlementStatementHandle(@RequestBody SettlementStatementHandleRequestModel requestModel) {
        vehicleSettlementBiz.settlementStatementHandle(requestModel);
        return Result.success(true);
    }

    /**
     * 车辆结算-发送司机-账单列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆结算-发送司机-账单列表")
    @PostMapping(value = "/sendDriverSettleStatementList")
    public Result<List<SendDriverSettleStatementListResponseModel>> sendDriverSettleStatementList(@RequestBody SendDriverSettleStatementListRequestModel requestModel) {
        return Result.success(vehicleSettlementBiz.sendDriverSettleStatementList(requestModel));
    }

    /**
     * 车辆结算详情-查询账单上该车辆未关联的轮胎费用
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆结算详情-查询账单上该车辆未关联的轮胎费用")
    @PostMapping(value = "/getVehicleTireByVehicleSettlementId")
    public Result<GetVehicleTireByVehicleSettlementIdResponseModel> getVehicleTireByVehicleSettlementId(@RequestBody VehicleSettlementIdRequestModel requestModel) {
        return Result.success(vehicleSettlementBiz.getVehicleTireByVehicleSettlementId(requestModel));
    }

    /**
     * 车辆结算详情-修改关联的轮胎（待对账状态操作）
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆结算详情-修改关联的轮胎（待对账状态操作）")
    @PostMapping(value = "/updateVehicleSettlementTire")
    public Result<Boolean> updateVehicleSettlementTire(@RequestBody  UpdateVehicleSettlementTireRequestModel requestModel) {
        vehicleSettlementBiz.updateVehicleSettlementTire(requestModel);
        return Result.success(true);
    }
}
