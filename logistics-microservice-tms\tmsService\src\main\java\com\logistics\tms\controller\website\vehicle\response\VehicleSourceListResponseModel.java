package com.logistics.tms.controller.website.vehicle.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/10/31 15:42
 */
@Data
public class VehicleSourceListResponseModel {
    @ApiModelProperty(value = "车源id")
    private Long vehicleSourceId;
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;
    @ApiModelProperty(value = "载重吨位")
    private String approvedLoadWeight;
    @ApiModelProperty(value = "车辆类型")
    private Integer type;
    @ApiModelProperty(value = "联系人姓名")
    private String contactName;
    @ApiModelProperty(value = "联系人手机号")
    private String contactMobile;
}
