<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRouteEnquiryMapper" >
  <insert id="insertSelectiveBackKey" parameterType="com.logistics.tms.entity.TRouteEnquiry" keyProperty="id" useGeneratedKeys="true" >
    insert into t_route_enquiry
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="orderCode != null" >
        order_code,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="ifCancel != null" >
        if_cancel,
      </if>
      <if test="goodsName != null" >
        goods_name,
      </if>
      <if test="quoteStartTime != null" >
        quote_start_time,
      </if>
      <if test="quoteEndTime != null" >
        quote_end_time,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="ifArchive != null" >
        if_archive,
      </if>
      <if test="contractCode != null" >
        contract_code,
      </if>
      <if test="auditorNameOne != null" >
        auditor_name_one,
      </if>
      <if test="auditTimeOne != null" >
        audit_time_one,
      </if>
      <if test="auditRemarkOne != null" >
        audit_remark_one,
      </if>
      <if test="auditorNameTwo != null" >
        auditor_name_two,
      </if>
      <if test="auditTimeTwo != null" >
        audit_time_two,
      </if>
      <if test="auditRemarkTwo != null" >
        audit_remark_two,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderCode != null" >
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="ifCancel != null" >
        #{ifCancel,jdbcType=INTEGER},
      </if>
      <if test="goodsName != null" >
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="quoteStartTime != null" >
        #{quoteStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="quoteEndTime != null" >
        #{quoteEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ifArchive != null" >
        #{ifArchive,jdbcType=INTEGER},
      </if>
      <if test="contractCode != null" >
        #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="auditorNameOne != null" >
        #{auditorNameOne,jdbcType=VARCHAR},
      </if>
      <if test="auditTimeOne != null" >
        #{auditTimeOne,jdbcType=TIMESTAMP},
      </if>
      <if test="auditRemarkOne != null" >
        #{auditRemarkOne,jdbcType=VARCHAR},
      </if>
      <if test="auditorNameTwo != null" >
        #{auditorNameTwo,jdbcType=VARCHAR},
      </if>
      <if test="auditTimeTwo != null" >
        #{auditTimeTwo,jdbcType=TIMESTAMP},
      </if>
      <if test="auditRemarkTwo != null" >
        #{auditRemarkTwo,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <select id="searchList" resultType="com.logistics.tms.controller.routeenquiry.response.SearchRouteEnquiryListResponseModel">
    select
    tre.id as routeEnquiryId,
    tre.order_code as orderCode,
    tre.status,
    tre.if_cancel as ifCancel,
    tre.if_archive as ifArchive,
    tre.quote_start_time as quoteStartTime,
    tre.quote_end_time as quoteEndTime,
    tre.contract_code as contractCode,
    tre.auditor_name_one as auditorNameOne,
    tre.audit_time_one as auditTimeOne,
    tre.auditor_name_two as auditorNameTwo,
    tre.audit_time_two as auditTimeTwo,

    trec.company_carrier_type as companyCarrierType,
    trec.company_carrier_name as companyCarrierName,
    trec.carrier_contact_name as carrierContactName,
    AES_DECRYPT(UNHEX(trec.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactPhone
    from t_route_enquiry tre
    left join t_route_enquiry_company trec on tre.id = trec.route_enquiry_id and trec.quote_status = 1 and trec.valid = 1
    where tre.valid = 1
    <if test="params.status != null">
      <choose>
        <when test="params.status == 5">
          and tre.if_cancel = 1
        </when>
        <otherwise>
          and tre.status = #{params.status,jdbcType=INTEGER} and tre.if_cancel = 0
        </otherwise>
      </choose>
    </if>
    <if test="params.ifArchive != null">
      and tre.if_archive = #{params.ifArchive,jdbcType=INTEGER}
    </if>
    <if test="params.createdBy != null and params.createdBy != ''">
      and instr(tre.created_by, #{params.createdBy,jdbcType=VARCHAR})
    </if>
    <if test="params.createdTimeStart != null and params.createdTimeStart != ''">
      and tre.created_time &gt;= DATE_FORMAT(#{params.createdTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
    </if>
    <if test="params.createdTimeEnd != null and params.createdTimeEnd != ''">
      and tre.created_time &lt;= DATE_FORMAT(#{params.createdTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
    </if>
    order by tre.id desc
  </select>


    <select id="searchListForWeb"  resultType="com.logistics.tms.controller.routeenquiry.response.SearchRouteEnquiryListForWebResponseModel">
        select
        tre.id routeEnquiryId,
        tre.order_code orderCode,
        tre.status,
        tre.if_cancel ifCancel,
        tre.if_archive ifArchive,
        tre.quote_start_time quoteStartTime,
        tre.quote_end_time quoteEndTime,
        tre.contract_code contractCode,

        trec.quote_operator quoteOperator,
        trec.quote_time quoteTime,
        trec.quote_status  quoteStatus

        from t_route_enquiry tre
        join t_route_enquiry_company trec on tre.id = trec.route_enquiry_id and trec.valid = 1
        where tre.valid = 1
        and trec.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        <if test="params.status != null">
            <choose>
                <when test="params.status == 1">
                    and (
                    tre.status = 1
                    and trec.quote_status in (-1,0)
                    and tre.if_cancel = 0
                    )
                </when>
                <when test="params.status == 5">
                    and (
                    tre.if_cancel = 1 or trec.quote_status = 2
                    or (trec.quote_status != 1  and tre.status in (2,3,4))
                    )
                </when>
                <otherwise>
                    and (
                    tre.status = #{params.status,jdbcType=INTEGER}
                    and trec.quote_status = 1
                    and tre.if_cancel = 0
                    )
                </otherwise>
            </choose>
        </if>
        <if test="params.quoteOperator != null and params.quoteOperator != ''">
            AND INSTR(trec.quote_operator, #{params.quoteOperator,jdbcType=VARCHAR})
        </if>
        <if test="params.quoteTimeStart != null and params.quoteTimeStart != ''">
            and trec.quote_time &gt;= DATE_FORMAT(#{params.quoteTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="params.quoteTimeEnd != null and params.quoteTimeEnd != ''">
            and trec.quote_time &lt;= DATE_FORMAT(#{params.quoteTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        order by tre.id desc
    </select>
</mapper>