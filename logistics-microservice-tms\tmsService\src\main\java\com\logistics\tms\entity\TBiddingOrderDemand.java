package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2024/04/26
*/
@Data
public class TBiddingOrderDemand extends BaseEntity {
    /**
    * 竞价单id
    */
    @ApiModelProperty("竞价单id")
    private Long biddingOrderId;

    /**
    * 需求单id
    */
    @ApiModelProperty("需求单id")
    private Long demandOrderId;

    /**
    * 竞价金额类型：1 单价，2 一口价
    */
    @ApiModelProperty("竞价金额类型：1 单价，2 一口价")
    private Integer biddingPriceType;

    /**
    * 竞价金额
    */
    @ApiModelProperty("竞价金额")
    private BigDecimal biddingPrice;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}