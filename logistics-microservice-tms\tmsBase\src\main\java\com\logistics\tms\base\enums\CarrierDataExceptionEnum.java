package com.logistics.tms.base.enums;

import com.yelo.tray.core.base.enums.BaseExceptionEnum;

/**
 * <AUTHOR>
 * @createDate 2018-08-13 20:36
 */
public enum CarrierDataExceptionEnum implements BaseExceptionEnum {

    OPERATION_EXECUTING_MESSAGE(-2,"操作执行中，请稍后再试"),
    HYSTRIX_ERROR_MESSAGE(-1,"请求超时，请稍候再试"),
    USER_NAME_PWD_ERROR(10000, "帐号或者密码错误"),
    SMS_VERIFICATION_CODE_TEMPLATE_NOT_EXIST(30001, "验证码短信模板不存在"),
    VEHICLE_NO_INVALID(30002, "请输入正确的车牌号"),
    VEHICLE_NO_NOT_NET_IN(30003, "该车牌号未入网"),
    COMPANY_CARRIER_REPEAT(30004, "公司名字已存在"),
    COMPANY_CARRIER_TRADING_AMEND(30005, "请上传营业执照"),
    COMPANY_CARRIER_AUTHORIZATION_AMEND(30006, "请上传授权书"),
    COMPANY_CARRIER_ROAD_AMEND(30007, "请上传道路许可证"),
    COMPANY_CARRIER_IDENTITYCARD_AMEND(30008, "请上传身份证"),
    CARRIER_ACCOUNT_PHONE_REPEAT(30009, "车主账号已存在"),
    COMPANY_CARRIER_IS_EMPTY(30010, "车主公司不存在"),
    CUSTOMER_IS_EMPTY(30011, "联系人信息不存在"),
    CUSTOMER_ACCOUNT_EMPTY(30012, "车主账号不存在"),
    VEHICLE_DRIVER_REPEAT(30013, "该车辆与司机您已经添加过了，请勿重复添加"),
    VEHICLE_DRIVER_WRITE_WRONG(30015, "司机手机号填写错误"),
    VEHICLE_NUMBER_WRITE_WRONG(30016, "车牌号/司机手机号不能为空"),
    VEHICLE_NUMBER_IS_EMPTY(30017, "车辆信息不存在"),
    VEHICLE_NUMBER_IS_EXIST(30018, "车牌号已存在"),
    REQUEST_PARAM_ERROR(30020, "请求参数错误"),
    CARRIER_ORDER_NOT_EXIST(30021, "运单不存在"),
    PICTURES_OVER_COUNT(30022, "最多上传6张图片"),
    DEMAND_ORDER_EMPTY(30023, "请选择需要调度的需求单"),
    DEMAND_ORDER_LOAD_ACCOUNT_ERROR(30024, "预提数量不能小于0"),
    CANT_NOT_LOAD(30025, "只有待到达提货地，待提货的数据进行该操作"),
    CANT_NOT_UNLOAD(30026, "只有待到达提货地，待提货，待到达卸货地，待卸货的数据进行该操作"),
    CARRIER_ORDER_CANCEL(30027, "运单已取消，不能进行该操作"),
    CANCEL_CARRIERORDER_ERROR(30028, "已提货运单不允许取消"),
    DEMAND_ORDER_COMPLETE(30001, "需求单已完成，不能进行调度操作"),
    DEMAND_ORDER_LOAD_ACCOUNT_ZERO(30029, "需求单下的预提数量必须大于0"),
    CARRIER_ORDER_STATUS_NOT_ALLOW_SIGN(30030, "选中了不符合签收条件的运单，请重新选择"),
    TOTAL_EXPECT_LOAD_AMOUNT_ERROR(30367, "0<总数量≤10000"),
    CARRIER_ORDER_VEHICLE_CONSISTENT(30032, "您当前修改的司机车辆信息与之前的信息一致"),
    CARRIER_ORDER_VEHICLE_WAIT_AUDIT(30033, "该运单下车辆未审核，不能再次修改车辆信息"),
    CARRIER_ORDER_VEHICLE_RELATION_EMPTY(30034, "车辆司机信息不存在"),
    CARRIER_ORDER_VEHICLE_WAIT_SUBMIT(30035, "该修改车辆信息无需审核"),
    CARRIER_ORDER_VEHICLE_AUDIT_THROUGH(30036, "该修改车辆信息已审核"),
    CARRIER_ORDER_VEHICLE_AUDIT_REJECT(30037, "该修改车辆信息已驳回"),
    REJECT_REASON_EMPTY(30038, "驳回原因不能为空，且不超过300字"),
    SIGN_UP_AMOUNT_CANT_GT_EXPECTED_AMOUNT(30039, "签收数量不能大于预提数量"),
    SIGN_UP_AMOUNT_CANT_BE_ZERO(30040, "签收数量不能为0"),
    CURRENT_CARRIER_ORDER_CANT_MODIFY_VEHICLE(30041, "该状态下的运单不允许修改车辆信息"),
    VEHICLE_INFO_SAME_CANT_MODIFY(30042, "您当前修改的车辆与之前的车辆信息一致"),
    EXPECTED_AMOUNT_CANT_GT_CARRIER_AMOUNT(30043, "预提数量不能大于待承运数"),
    EXPECTED_AMOUNT_CANT_BE_ZERO(30044, "预提数量不能小于0"),
    NO_VEHICLE_EXIST(30045, "无结果"),
    STARTIM_ENDTIME_ERROR(30046, "起始日期不能大于截止日期"),
    CARRIER_ORDER_STATUS_ERROR(30047, "该数据状态已更新，请刷新之后重新操作"),
    CARRIER_ORDER_GOODS_EMPTY(30048, "查询运单货物信息失败，请刷新重试"),
    CARRIER_ORDER_VEHICLE_WAIT_AUDIT_APP(30049, "运单调度已申请修改车辆司机，您暂时不能操作，如有疑问请联系调度"),
    PLEASE_MAX_THIRTY_SIGN(30050, "您最多批量签收30个订单"),
    SIGN_AMOUNT_CANT_BE_BULL(30051, "签收金额不能为空"),
    DRIVER_NOT_EXIST(30054, "司机信息不存在"),
    DRIVER_PHONE_USED(30055, "手机号已存在"),
    DRIVER_ACCOUNT_HAS_OPENED(30056, "司机账号已开通，无需重复开通"),
    DRIVER_ACCOUNT_HAS_CLOSED(30057, "司机账号已关闭，无需重复关闭"),
    DRIVER_ACCOUNT_WAIT_OPEN(30058, "该司机账号未开通，不能操作关闭"),
    DRIVER_EXIST_VEHICLE_RELATION(30059, "该司机存在与车辆的关联关系，不允许删除"),
    CARRIER_ACCOUNT_NOT_EXIST(30060, "车主账号不存在"),
    CANT_NOT_REACHLOADADDRESS(30061, "只有待到达提货地的数据进行该操作"),
    CANT_NOT_REACHUNLOADADDRESS(30062, "只有待到达提货地，待提货，待到达卸货地的数据进行该操作"),
    VEHICLE_ALREADY_EXIST_NOT_REPEAT(30063, "该车牌号已存在，请勿重复添加"),
    VEHICLE_NEED_AUDIT_TIP1(30064, " 运单车辆还未审核，请审核车辆之后再操作"),
    VEHICLE_NEED_AUDIT_TIP2(30065, " 运单车辆还未审核，请联系杨辉审核车辆之后再操作"),
    VEHICLE_NEED_AUDIT_TIP3(30066, " 运单调度已申请修改车辆司机，您暂时不能操作，如有疑问请联系调度"),
    CARRIER_DRIVER_DISABLED(30067, "司机账号被禁用，无法操作"),
    DRIVER_ACCOUNT_CLOSED(30068, "  司机账号被关闭，无法启用"),
    DRIVER_ACCOUNT_DISABLED(30069, "您选择的司机已被禁用，请至司机管理中启用之后再选择"),
    TOTAL_EXPECT_AMOUNT_NOT_NUMBER(30070, "数量要求大于0小于等于10000，不允许填写小数"),

    CARRIER_PHONE_USED(30071, "车主账号冲突"),

    COMPANY_CARRIER_TRADING_AMEND_DATE(30072, "请选择营业执照的有效期"),
    COMPANY_CARRIER_ROAD_AMEND_DATE(30073, "选择道路许可证有效期"),
    COMPANY_CARRIER_IDENTITYCARD_AMEND_DATE(30074, "选择身份证有效期"),
    CARRIER_ORDER_CONTACT_NOT_EXIST(30075, "运单联系人信息不存在"),
    MOBILE_EMPTY(30076, "手机号不能为空"),
    COMPANY_CARRIER_PERMISSIONS_EMPTY(30077, "权限不能为空"),

    MODIFY_CARRIER_ACCOUNT_ERROR(30078, "修改账号填写错误，重新填写"),
    AMOUNT_ERROR(30079, "数量异常"),
    COMPANY_CARRIER_ROAD_AMEND_NUMBER(30080, "请正确填写许可证号,为6-30字"),
    COMPANY_CARRIER_IDENTITYCARD_AMEND_NUMBER(30081, "请填写身份证号"),
    CARRIER_ACCOUNT_PHONE_OTHER_COMPANY(30082, "该车主账号已经挂靠在其他物流公司，不允许重复挂靠"),
    CARRIER_ACCOUNT_PHONE_EXIST_THIS_COMPANY(30083, "该车主账号已经挂靠在本公司，不允许重复挂靠"),
    DRIVER_ACCOUNT_REPEAT(30084, "司机账号冲突"),

    COMPANY_CARRIER_DIFFERENT(30085, "车主不同不允许批量调度车辆"),
    CARRIER_COMPANY_REPEAT_OR_NOT_EXIST(30086, "车主公司重复或不存在"),
    DEMAND_ORDER_CANCEL(30087, "需求单已取消，不能进行调度操作"),
    DEMAND_ORDER_WAIT_PUBLISH(30088, "需求单待发布，不能进行调度操作"),
    DEMAND_ORDER_BIDDING_NOT_OPERATION(30089,"需求单竞价中，不能进行该操作"),

    WEIXIN_API_FAILURE(30090, "微信接口调用失败"),
    HAS_ALREADY_BIND_MOBILE(30091, "已绑定手机号，无需重复绑定"),
    NOT_BIND_MOBILE(30092, "微信号未绑定手机号，请绑定"),
    CUSTOMER_ACCOUNT_REPEAT(30093, "手机号码已存在"),
    CARRIER_ORDER_WAIT_AUDIT(30094, "车辆未审核，不能进行该操作"),
    DRIVER_NOT_CLOSED(30095, "司机账号未关闭，无法删除"),
    DISPATCH_ORDER_NOT_EXIST(30096, "调度单不存在"),
    CARRIER_ORDER_NOT_WAIT_SIGN_STATUS(30097, "运单不是待签收状态，不能进行该操作"),
    EXPECT_AMOUNT_NOT_WEIGHT(30098, "数量要求大于等于0小于等于5000，最多填写3位小数"),
    TOTAL_EXPECT_AMOUNT_NOT_WEIGHT(30098, "数量要求大于0小于等于100000，最多填写3位小数"),
    CHOOSE_SAME_UNIT_DEMAND_ORDER_TO_DISPATCH(30099, "请选择相同单位的需求单进行调度"),
    EXPECT_AMOUNT_NOT_POSITIVE_NUMBER(30100, "预提数量只能输入0或正整数"),
    EXPECT_AMOUNT_NOT_FLOAT_NUMBER(300101, "预提数量只能输入0或不超过三位小数的数字"),
    DISPATCH_ORDER_WAIT_SUBMIT(30102, "该调度单无需审核"),
    DISPATCH_ORDER_AUDIT_THROUGH(30103, "该调度单已审核"),
    DISPATCH_ORDER_AUDIT_REJECT(30104, "该调度单已驳回"),
    DISPATCH_ORDER_INVALID(30105, "该调度单已作废"),
    CARRIER_ORDER_STATUS_NOT_ALLOW_SIGN_RECYCLE(30106, "Y单异常，请先纠错再签收"),
    DRIVER_FEE_EMPTY(30107, "司机运费不能为空"),
    CARRIER_ORDER_NOT_LE_YI(30108, "该运单不是乐医委托单无法进行后台签收"),
    CARRIER_FREIGHT_RULE_NOT_EXIST(30109, "车主运费规则不存在"),
    CARRIER_FREIGHT_ENABLE_DISABLE_ERROR(30110, "车主运费规则启用/禁用失败"),
    CARRIER_FREIGHT_RULE_EXIST(30111, "车主运费规则已存在"),
    CARRIER_FREIGHT_CALC_RULE_ERROR(30112, "请选择正确的计价规则"),
    CARRIER_FREIGHT_CALC_RULE_EMPTY(30113, "计价阶梯不能为空"),
    CARRIER_FREIGHT_RULE_PERIODS_FIRST_START_WITH_ZERO(30114, "计价规则阶梯首重必须已'0<单位'开始"),
    MAINTAIN_RIGHT_PERIODS(30115, "请维护正确的阶梯数据"),
    CARRIER_ORDER_NOT_AUDIT(30116, "运单处于待审核状态，不能进行该操作"),
    GOODS_LIST_ERROR(30117, "货物信息错误"),
    VEHICLE_PROPERTY_EMPTY(30118, "车辆机构不能为空"),
    DRIVER_FREIGHT_ALL_VEHICLE_EXIST(30119, "该起卸地已存在全部车辆运费规则"),
    DRIVER_FREIGHT_EXIST(30120, "该起卸地该车辆机构已存在"),
    DRIVER_FREIGHT_PERIOD_EMPTY(30121, "司机运费阶梯不能为空"),
    DRIVER_FREIGHT_NOT_EXIST(30122, "司机运费不存在"),
    DEMAND_ORDER_IDS_EMPTY(30123, "需求单ID不能为空"),
    CARRIER_FREIGHT_RULE_NOT_EXIST_OR_DISABLE(30124, "运费规则不存在或者被禁用"),
    CARRIER_FREIGHT_RULE_PERIOD_NOT_EXIST(30125, "运费规则阶梯不存在"),
    MARKUP_PRICE_MUST_OVER_ZERO(30126, "请维护多卸加价规则的加价金额"),
    DRIVER_FREIGHT_NEED_LEAST_ONE(30127, "请至少维护一条司机运费信息"),
    QUALIFICATION_AUDITED(30128, "资质已审核，不能进行该操作"),
    QUALIFICATION_WAIT_AUDIT(30129, "资质待审核，不能进行该操作"),
    QUALIFICATION_REJECT(30130, "资质已驳回，不能进行该操作"),
    QUALIFICATION_APPLY(30131, "还未申请资质审核，不能进行该操作"),
    CARRIER_SETTLEMENT_INFO_EMPTY(30132, "运单结算信息不存在"),
    DEMAND_ORDER_NOT_EXIST(30133, "需求单不存在"),
    DEMAND_ORDER_CANT_SEND(30134, "需求单不可发送"),
    DATA_LOSS(30135, "发送运单数据缺失"),
    ANNOUNCEMENT_NOT_EXIST(30136, "公告不存在或者被删除"),
    ANNOUNCEMENT_TIME_ERROR(30137, "不能选择当前时间之前的时间"),
    ANNOUNCEMENT_TIME_EMPTY(30139, "发送时间为空"),
    GOODS_LIST_EMPTY(30140, "货物信息不能为空"),
    REGISTER_CARRIER_ROLE_EXIST(30141, "您已注册车主，请勿重复注册"),
    CARRIER_ROUTE_EMPTY(30142, "线路不存在"),
    CARRIER_ROUTE_EXIST(30143, "该数据已存在，请勿重复添加"),
    COMPANY_CARRIER_IS_WAIT_AUDIT(30144, "车主信息待审核，不允许操作"),
    COMPANY_CARRIER_HAS_AUDITED(30145, "车主信息已审核，不允许操作"),
    COMPANY_CARRIER_HAS_REJECT(30146, "车主信息已驳回，不允许操作"),
    COMPANY_CARRIER_TO_BE_SUBMITTED(30147, "车主信息待提交，不允许操作"),
    COMPANY_CARRIER_REJECT_MESSAGE_EMPTY(30148, "驳回信息不能为空"),
    ACCOUNT_EMPTY(30149, "账号不存在"),
    NOT_CARRIER_ROLE(30150, "只有车主才能进行该操作"),
    OPERATION_TYPE_EMPTY(30151, "操作类型不能为空"),
    COMPANY_CARRIER_FAILED_TO_PASS_THE_AUDIT(30152, "该企业还未审核通过，无法建立关联关系"),
    COMPANY_CARRIER_ROLE_EXIST(30153, "您已是车主，无需进行升级操作，请刷新重试"),
    COMPANY_CARRIER_DISABLED(30154, "车主账号被禁用，无法操作"),
    VEHICLE_TYPE_IS_EMPTY(30155, "车辆类型不存在"),
    VEHICLE_TYPE_ENABLE_DISABLE_ERROR(30156, "启动/禁用失败"),
    INSURANCE_COMPANY_IS_EMPTY(30159, "保险公司不存在"),
    INSURANCE_COMPANY_HAS_EXIST(30160, "该保险公司已存在，请勿重复添加"),
    INSURANCE_COMPANY_ENABLE_DISABLE_ERROR(30161, "保险公司启用/禁用失败"),
    ENABLE_DISABLE_PARAMS_ERROR(30162, "启用/禁用参数有误"),
    PERSONAL_ACCIDENT_INSURANCE_EXIST(30163, "该保单已存在，请勿重复添加"),
    DATE_REMIND_NOT_EXIST(30164, "日期提醒信息不存在"),
    DATE_REMIND_HAS_EXIST(30165, "该日期提醒已维护，请勿重复维护！"),
    VIOLATION_REGULATION_IS_EMPTY(30166, "违章信息不存在"),
    CERTIFICATION_PICTURES_NOT_EXIST(30167, "图片不存在"),
    INSURANCE_EMPTY(30168, "保险不存在"),
    INSURANCE_CANCEL(30169, "保险已作废，不能进行该操作"),
    INSURANCE_OVERDUE(30170, "保险已过期，不能进行该操作"),
    START_TIME_DA_YU_END_TIME(30174, "保险生效时间不能大于保险截止时间"),
    INSURANCE_EXIST(30175, "该保险已存在，请勿重复添加"),
    STAFF_EXIST(30176, "该人员信息已存在"),
    DRIVER_LICENSE_DATE_ERROR(30177, "请维护正确的驾照期限"),
    FILE_TYPE_NOT_EXIST(30178, "图片类型不存在"),
    BASIC_INFORMATION_OF_VEHICLE_ASSETS_NOT_EXIST(30179, "车辆资产基础信息不存在"),
    STAFF_NOT_EXIST(30180, "人员信息不存在"),
    DRIVER_INSURANCE_VALID(30181, "该司机有保险正在执行中，不允许删除"),
    VEHICLE_NUMBER_HAS_EXIST(30182, "车辆信息已存在"),
    DRIVING_LICENSE_NOT_EXIST(30183, "行驶证信息不存在"),
    VEHICLE_ROAD_TRANSPORT_CERTIFICATE_NOT_EXIST(30184, "道路运输证信息不存在"),
    VEHICLE_BASIC_IS_EMPTY(30185, "车辆资产基本信息不存在"),
    INSURANCE_NOT_EXIST(30186, "保单不能为空"),
    CARRIER_ACCOUNT_ALREADY_REGISTERED(30187, "该公司已被其他人注册，请直接联系客服为您建立关联关系"),
    CARRIER_ORDER_UNLOAD(30189, "运单已卸货，不能进行该操作"),
    CARRIER_ORDER_UNLOADING_ADDRESS_WAYBILL_BE_AUDITED(30190, "运单卸货地址变更待审核,暂时不能进行该操作"),
    IMPORT_ERROR(30191, "导入失败"),
    VEHICLE_INSURANCE_VALID(30192, "该车辆有保险正在执行中，不允许删除"),
    PERSON_ACCIDENT_INSURANCE_COUNT_MAX(30193, "保单关联人数已满，请确认保单信息是否正确"),
    BANK_NAME_HAS_EXIST(30194, "银行名称已存在"),
    BANK_INFO_IS_EMPTY(30195, "银行信息不存在"),
    BANK_ENABLE_DISABLE_ERROR(30196, "银行信息启用/禁用失败"),
    VEHICLE_PAYEE_REL_EMPTY(30197, "车辆收款账户关联关系不存在"),
    VEHICLE_PAYEE_REL_EXIST(30198, "车辆收款账户关联关系已存在"),
    DRIVER_PAYEE_EMPTY(30199, "收款账户不存在"),
    DATA_EXIST(30200, "此数据已存在，请勿重复添加！"),
    DRIVER_PAYEE_NOT_EXIST(30201, "司机账户信息不存在"),
    DRIVER_PAYEE_IDENTITY_EXIST(30202, "该收款账户身份证已存在"),
    DRIVER_PAYEE_AUDIT_STATUS_CANT_MODIFY(30202, "非待审核/已驳回/已审核的数据无法修改"),
    DRIVER_PAYEE_CANT_AUDIT(30203, "非待审核/已驳回状态的数据无法审核"),
    DRIVER_PAYEE_CANT_REJECT(30204, "非待审核状态的数据无法驳回"),
    DRIVER_PAYEE_NOT_AUDIT(30205, "非已审核状态的数据无法作废"),
    DRIVER_PAYEE_RELATED(30206, "该收款账号关联车牌号X辆，请解除关联之后再作废"),
    FEEDBACK_INFO_EMPTY(30207, "反馈信息不存在"),
    STATUS_NOT_DIFFERENT_IN_BATCH_REPLY(30208, "批量回复数据状态不一致"),
    DRIVER_PAYEE_IDENTITY_IMAGE(30209, "收款人账号身份证图片信息异常"),
    QR_CODE_ERROR(30209, "非有效二维码，请重新扫描"),
    APP_ERROR(30210, "请在APP里进行扫码操作"),
    EXTERNAL_CARRIER_ORDER_LOAD(30211, "该外部运单已提货"),
    EXTERNAL_CARRIER_ORDER_EMPTY(30212, "该外部运单不存在"),
    EXTERNAL_CARRIER_ORDER_CANCEL(30213, "该外部运单已取消"),
    EXTERNAL_CARRIER_ORDER_SIGN_UP(30214, "该外部运单已签收"),
    CARRIER_ORDER_SWEEP_CODE_DETAIL(30215, "该运单不由您运输，请勿扫码操作"),
    CARRIER_ORDER_XQR_CODE_AND_DIFFERENCE(30216, "二维码对应的运单号与您选中的运单号不一致，请重新扫码"),
    VEHICLE_TIRE_IS_EMPTY(30217, "轮胎信息不存在"),
    VEHICLE_DATE_REPEAT(30218, "该车牌号和更换日期数据已存在"),
    WAREHOUSE_NOT_EXIST(30219, "仓库配置不存在"),
    APP_ADVERTISEMENT_TIME_OVERLAP(30220, "时间不能重叠"),
    VIOLATION_REGULATION_HAS_EXIST(30221, "违章信息已存在"),
    DEMAND_ORDER_CANT_SIGN(30222, "无法签收该运单"),
    DEMAND_ORDER_NEED_LOCATION(30223, "请开启定位"),
    ORDER_LIST_SCAN_WAIT_AUDIT(30224, "先审核通过再操作"),
    ORDER_LIST_SCAN_INVALID(30225, "非有效二维码，请重新扫描"),
    ORDER_LIST_SCAN_ERROR(30226, "扫码失败"),
    TRAILER_VEHICLE_NOT_EXIST(30227, "牵引车信息不存在"),
    TRACTOR_VEHICLE_NOT_EXIST(30228, "挂车信息不存在"),
    STAFF_Q_VEHICLE_RELATION_HAS_EXIST(30229, "牵引车，车辆司机信息已存在"),
    STAFF_G_VEHICLE_RELATION_HAS_EXIST(30230, "挂车，车辆司机信息已存在"),
    STAFF_VEHICLE_RELATION_HAS_EXIST(30231, "车辆司机关联信息已存在"),
    STAFF_VEHICLE_RELATION_NOT_EXIST(30232, "车辆司机关联信息不存在"),
    VEHICLES_ARE_OUT_OF_SERVICE(30233, "车辆已停运"),
    VEHICLES_OUT_OF_SERVICE_ERROR(30234, "车辆存在关联的运单或保险，请重新停运"),
    VEHICLE_OUT_OF_SERVICE(30235, "已停运车辆不能调度"),
    WAREHOUSE_EXIST(30236, "该仓库配置已存在，请勿重复添加"),
    WAREHOUSE_NAME_EMPTY(30237, "仓库名不能为空"),
    WAREHOUSE_ADDRESS_EMPTY(30238, "请选择省市区"),
    ENTRUST_ADDRESS_EMPTY(30239, "地址信息不存在"),
    ENTRUST_ADDRESS_COMPANY_NAME_EMPTY(30240, "公司不能为空"),
    CONTRACT_NOT_EXIST(30241, "合同信息不存在"),
    CONTRACT_NOT_MODIFY(30242, "合同信息不允许修改"),
    NOT_EXECUTING_NOT_ENDING(30243, "只有执行中的合同才能终止"),
    NOT_WAIT_EXECUTE_NOT_CANCEL(30244, "只有待执行的合同才能作废"),
    GOODS_UNIT_DIFFERENT(30245, "货物单位不同不允许进行该操作"),
    CONTACT_PHONE_EMPTY(30246, "请填写正确的联系方式"),
    ENTRUST_ADDRESS_EXIST(30247, "地址信息已存在"),
    DEMAND_ORDER_ID_EMPTY(30248, "需求单ID不能为空"),
    DEMAND_ORDER_GOODS_UNIT_DIFFERENT(30249, "委托货物请选择正确且相同的单位"),
    DEMANDORDER_IS_EMPTY(30250, "需求单不存在"),
    CONFIGURATION_ERROR(30251, "请配置相关参数"),
    CUSTOMER_ORDER_CODE_EXIST(30252, "客户单号已存在"),
    RECEIVER_ADDRESS_EMPTY(30253, "未找到正确的收货地址"),
    ENABLE_DISABLE_ERROR(30254, "启用/禁用失败"),
    VEHICLE_SOURCE_EXIST(30255, "该车牌号已经被注册，不可重复添加！"),
    ENTRUST_SOURCE_EXIST(30256, "您已经注册为平台货主，请勿重复注册"),
    ONE_VEHICLE_NOT_EXIST(30257, "一体车信息不存在"),
    STAFF_O_VEHICLE_RELATION_HAS_EXIST(30258, "一体车，车辆司机信息已存在"),
    REQUEST_PARAMS_INCOMPLETE(30259, "入参信息不完整"),
    PUBLISHER_HAS_EXIST_BLACK_LIST(30260, "信息黑名单已存在"),
    BLACK_LIST_INFO_NOT_EXIST(30261, "信息黑名单不存在"),
    KEFU_NOT_EXIST(30262, "客服信息不存在"),
    KEFU_REPLIED(30263, "客服信息已回复，无需重复回复"),
    DATA_STATUS_HAS_UPDATE(30264, "数据状态已更新，请刷新之后重新操作"),
    COMPLAINT_INTO_NOT_EXIST(30265, "投诉信息不存在"),
    DONT_BLACL_SELF_DEMANDORDER(30266, "非自己的需求单无法拉黑"),
    COMPANY_ENTRUST_EMPTY(30267, "货主公司不存在"),

    GPS_FEE_EXIST(30268, "gps费用已存在"),
    GPS_FEE_START_DATE_ERROR(30269, "请维护正确的起始时间、截止时间"),
    GPS_FEE_HISTORY_EXIST(30270, "gps费用已有扣减记录，合作开始时间不能小于当前时间"),
    CALCULATION_CAR_PRICE_MISTAKE(30271, "贷款总价计算有误"),
    CALCULATION_LOAN_FEE_MISTAKE(30272, "总贷款费用计算有误"),
    LOAN_RECORDS_NOT_EXIST(30273, "贷款记录不存在"),
    LOAN_RECORDS_HAS_EXIST(30274, "贷款记录已存在"),
    GPS_FEE_NOT_EXIST(30275, "gps费用不存在"),
    GPS_FEE_TERMINATION(30276, "该gps费用已终止"),
    GPS_FEE_FINISH_DATE_ERROR(30277, "请维护正确的终止时间（起始时间到截止时间之间）"),
    STAFF_PROPERTY_ERROR(30278, "该司机非内部人员"),
    VEHICLE_NUMBER_NOT_EXIST(30279, "车辆信息不存在"),
    VEHICLE_PROPERTY_ERROR(30280, "该车辆非内部车辆"),
    VEHICLE_CATEGORY_ERROR(30281, "该选择牵引车或一体车"),
    VEHICLE_OUTAGE_ERROR(30282, "该选择运营中的车辆"),
    OIL_FILLED_NOT_EXIST(30283, "充油不存在"),
    PARKING_FEE_EXIST(30284, "停车费用已存在"),
    PARKING_FEE_NOT_EXIST(30285, "停车费用不存在"),
    PARKING_FEE_TERMINATION(30286, "停车费用已终止"),
    PARKING_FEE_HISTORY_EXIST(30287, "停车费用已有扣减记录，合作开始时间不能小于当前时间"),
    PARKING_FEE_FINISH_DATE_ERROR(30288, "请维护正确的终止时间（起始时间到截止时间之间）"),
    INSURANCE_COST_NOT_EXIST(30289, "保险费用不存在"),
    SELECT_ITEMS_HAS_REMOVED(30291, "车辆司机关系已移除"),
    VEHICLE_SETTLEMENT_EMPTY(30292, "结算费用信息不存在"),
    INSURANCE_COST_ONLY_ONE_ON_CURRENT_SETTLE_MONTH(30293, "当月已经有保险记录"),
    DRIVER_SAFE_PROMISE_INFO_EXIST(30294, "当年已创建驾驶员承诺书"),
    PLEASE_SELECT_DRIVER(30295, "请添加承诺书人员"),
    DRIVER_SAFE_PROMISE_INFO_NOT_EXIST(30296, "承诺书不存在"),

    DRIVER_SAFE_MEETING_EXIST(30297, "安全例会已存在"),
    DRIVER_SAFE_MEETING_EMPTY(30298, "安全例会/领导会议不存在"),
    INTERNAL_DRIVER_ERROR(30299, "请选择内部司机"),
    DRIVER_SAFE_MEETING_REISSUE_ERROR(30300, "安全例会只有当月才能补发"),
    DRIVER_SAFE_MEETING_STUDY(30301, "已学习不能进行该操作"),

    VEHICLE_PROPERTY_NOT_EXIST(30302, "请选择车辆机构"),
    VEHICLE_CATEGORY_NOT_EXIST(30303, "请选择牵引车类型或一体车类型"),

    SPECIFY_PERIOD_SAFE_CHECK_INFO_EXIST(30304, "当月已创建安全车辆检查"),
    SELECT_VEHICLE_INFO_NOT_MATCHING(30305, "选择车辆信息与数据库不匹配,请重新选择"),
    INTERNAL_VEHICLE_ERROR(30306, "请选择内部车辆"),
    SAFE_CHECK_INFO_NOT_EXIST(30307, "车辆安全检查信息不存在"),
    CHECK_ITEM_INFO_ERROR(30308, "请检查合格项"),
    SAFE_CHECK_ITEM_INFO_NOT_EXIST(30309, "车辆安全检查合格项信息不存在"),
    SAFE_CHECK_STATUS_HAS_CHANGE(30310, "数据状态发生改变，请刷新数据"),
    SAFE_CHECK_REFORM_RESULT_FILE_IS_EMPTY(30311, "请上传整改项目相关附件"),


    COMMERCIAL_INSURANCE_COST_NOT_EMPTY(30312, "商业险剩余未扣除金额不能为空"),
    COMPULSORY_INSURANCE_COST_NOT_EMPTY(30313, "交强险剩余未扣除费用不能为空"),
    CARGO_INSURANCE_COST_NOT_EMPTY(30314, "货物险剩余未扣除费用不能为空"),
    CARRIER_INSURANCE_COST_NOT_EMPTY(30315, "承运人险剩余未扣除费用不能为空"),

    COMMERCIAL_INSURANCE_PAY_COST_NOT_EMPTY(30316, "商业险已扣除费用不能为空"),
    COMPULSORY_INSURANCE_PAY_COST_NOT_EMPTY(30317, "交强险已扣除费用不能为空"),
    CARGO_INSURANCE_PAY_COST_NOT_EMPTY(30318, "货物险已扣除费用不能为空"),
    CARRIER_INSURANCE_PAY_COST_NOT_EMPTY(30319, "承运人险已扣除费用不能为空"),
    CARRIER_SETTLEMENT_HAS_BEEN_PAYED(30320, "已付款的请勿重复操作付款动作"),
    CARRIER_SETTLEMENT_PRICE_TYPE_NULL(30321, "请勿选择报价类型为空的数据进行付款操作"),
    CARRIER_SETTLEMENT_HAS_NOT_BEEN_PAYED(30322, "未付款的请勿操作回退动作"),
    VEHICLE_HAS_SETTLEMENT(30323, "该费用已结算"),
    FIRST_NOT_SETTLEMENT(30324, "请先确认{0}月费用"),
    VEHICLE_PROPERTY_SELECT_ERROR(30325, "请选择外部车辆"),
    GPS_FEE_NOT_TERMINATION(30326, "该gps费用处于已预付且待结算状态，不能进行终止操作"),
    LOAN_DEDUCTING_FEE_MAX(30327, "贷款应扣费用不能大于未扣减合计费用"),
    PARKING_DEDUCTING_FEE_MAX(30328, "停车应扣费用不能大于未扣减合计费用"),
    GPS_DEDUCTING_FEE_MAX(30329, "GPS应扣费用不能大于未扣减合计费用"),
    PARKING_FEE_NOT_TERMINATION(30330, "该停车费用处于已预付且待结算状态，不能进行终止操作"),
    VEHICLE_NOT_HAVE_VALID_INSURANCE(30331, "当前车辆没有有效保险，请去保险管理中为该车辆添加保险费用！"),
    VEHICLE_CATEGORY_OR_PROPERTY_CONFLICT(30332, "该车修改的车辆类型或车辆机构，与已有车辆司机关联关系中不符！"),
    PLEASE_CREATE_VEHICLE_TYPE(30333, "调度车辆失败，重型半挂牵引车车辆类型不存在！"),
    STAFF_PROPERTY_CONFLICT(30334, "修改驾驶员的人员机构，与已有车辆司机关联关系中不符！"),
    CURRENT_MONTH_NOT_SETTLEMENT(30335, "当月不可结算"),
    SETTLEMENT_STATUS_ERROR_NOT_UPDATE(30336, "只有待结算状态才允许修改"),
    VEHICLE_TYPE_NOT_EDIT(30337, "重型半挂牵引车不可编辑"),
    INSURANCE_HAVE_COSTS_NOT_ALLOW_CANCEL(30338, "当前保险有待确认账单，请先完成对账"),
    INSURANCE_END_TIME_NOT_ALLOW_BEFORE_NOW(30339, "保险截止时间不能小于当前时间"),
    GPS_FEE_END_TIME_NOT_ALLOW_BEFORE_NOW(30340, "合作截止不能小于当前时间"),
    LOAN_RECORDS_START_DATE_ERROR(30341, "截至时间小于当前时间(贷款的截止时间=起始时间+期数（月数)"),
    PARKING_FEE_END_TIME_NOT_ALLOW_BEFORE_NOW(30342, "合作截止不能小于当前时间"),
    VEHICLE_UNFINISH_CHECK_NOT_ALLOW_OUTAGE(30343, "车辆未完成检查，暂时不能停运！"),
    SEARCH_EMPTY_BY_LON_LAT(30344, "根据经纬度查询地址信息：未查询到"),
    VEHICLE_RELATE_CARRIER_NOT_ALLOW_UPDATE(30345, "车辆已关联车主，请先解绑后再修改为自有车辆"),
    EXT_VEHICLE_SETTLEMENT_EMPTY(30346, "外部车辆结算数据不存在"),
    EXT_VEHICLE_SETTLEMENT_PAYMENT(30347, "已支付，不能进行该操作"),
    EXT_VEHICLE_SETTLEMENT_NOT_PAYMENT(30348, "未支付，不能进行该操作"),
    VEHICLES_ARE_IN_OF_SERVICE(30349, "车辆运营中"),
    VEHICLE_UNFINISH_CHECK_NOT_ALLOW_DEL_RELATION(30350, "车辆未完成检查，暂时删除车辆司机关联关系！"),
    REFUND_COUNT_MAX(30351, "已超出退款数量"),
    FREIGHT_RATE_ALREADY_EXISTS_IN_THE_COMPANY(30352, "该公司已存在运价，请勿重复操作"),
    COMPANY_FREIGHT_NOT_EXISTS(30353, "运价信息不存在"),
    INSURANCE_REFUND(30354, "保险已退保，不能进行该操作"),
    REFUND_INSURANCE_EMPTY(30355, "请选择退保保险"),
    VEHICLE_HAVE_WAIT_SETTLEMENT_INSURANCE_COST(30356, "有待结算保险费用，请先结算保险费用！"),
    REFUND_INSURANCE_FILE_EMPTY(30357, "请上传退保保险证明"),
    REFUND_PREMIUM_MAX(30358, "退保金额不能大于已扣减金额"),
    INSURANCE_NOT_NORMAL(30359, "已作废、已退保、非保障中的保险不能进行该操作"),
    PLEASE_CHECK_VEHICLE_TYPE(30360, "请检车辆是否是牵引车或一体车类型！"),
    BILL_HAS_BEEN_GENERATED_CANNOT_OPERATION(30361, "您的运单已经生成对账单，不可操作"),
    LAST_MONTH_OWN_VEHICLE_STATEMENT_HAS_BEEN_GENERATED(30362, "上月自有车辆对账单已经生成，请修改外部车辆承运！"),
    DRIVER_SAFE_MEETING_MODIFY_ERROR(30363, "安全例会只有当月才能重新编辑"),
    INSURANCE_CHANGE(30364, "当前车辆保险信息有变动，不能被结算，请刷新页面！"),
    UNLOADING_AMOUNT_CANT_GT_LOAD_AMOUNT(30365, "卸货数量不能大于提货数量"),
    DATA_SYNCHRONIZATION_NETWORK_FREIGHT_NOT_ALLOWED_OPERATION(30366, "数据已同步网络货运，不允许操作"),
    EXPECT_LOAD_AMOUNT_ERROR(30367, "0<实提数量≤5000"),
    CARRIER_ORDER_LOAD_AMOUNT_SYNC_ERROR(30368, "您的运单已提货，不可修改实提数"),
    CARRIER_ORDER_LOAD_AMOUNT_ERROR(30369, "运单号:{0},您已实提{1},请勿修改"),
    WAREHOUSE_ADDRESS_COMPANY_EMPTY(30371, "请选择货主"),
    EXPORT_CARRIER_ORDER_TICKETS_ERROR(30372, "没有选择满足导出条件的运单"),
    SAFETY_GROUP_MEETING_EXIST(30373, "每个季度仅能创建1次"),
    SEND_DRIVER_ERROR(30374, "车辆{0},对账单状态错误"),
    VEHICLE_SETTLE_STATEMENT_ERROR(30375, "对账单状态错误"),
    ONLY_WAIT_CONFIRM(30376, "只有待确认账单才能使用此功能"),
    PAY_MONEY_NOT_EMPTY(30377, "打款金额不能为空"),
    PAY_MONEY_ERROR(30378, "您输入的金额不能大于未付运费"),
    ADJUST_FEE_ERROR(30379, "调整费用金额错误"),
    PAYABLE_FEE_ERROR(30380, "应付费用不能小于等于0"),
    DRIVER_ERROR(30381, "司机不属于该对账单"),
    ONLY_WAIT_SETTLESTATEMENT(30382, "只有待对账单，才能进行对账"),
    SEND_DRIVER_VEHICLE_NUMBER_MONTH_ERROR(30383, "车辆:{0},账单月:{1}的账单不符合条件"),
    PAY_COMPANY_ERROR(30384, "打款公司不能为空且不能超过50字"),
    RECEIVE_NAME_ERROR(30385, "收款人不能为空且不能超过50字"),
    PAY_TIME_ERROR(30386, "打款时间不能为空"),
    NOT_APPLY_ACCOUNT_LOGIN(30387, "该账号未开通权限，如有问题请联系客服"),
    CUSTOMER_ACCOUNT_NOT_EXIST(30389, "账号不存在"),
    OLD_PASSWORD_NOT_TRUE(30390, "旧密码不正确，请重新输入"),
    OLE_PASSWORD_NOT_EQUALS_NEW_PASSWORD(30391, "新旧密码一致，请重新输入"),
    MOBILE_NOT_AUTHORIZED(30392, "该手机号没有被授予权限，请联系客服"),
    VEHICLE_OUTAGE_FILE_ERROR(30393, "请上传图片"),
    VEHICLES_ARE_TRANSFER(30394, "车辆已过户"),
    VEHICLES_ARE_SCRAP(30395, "车辆已停运"),
    VEHICLE_TIRE_CANNOT_DEL(30396, "轮胎费用已经关联对账单，不能删除"),
    ONLY_WAIT_LOAD(30397, "只有待提货的数据才能进行该操作"),
    ONLY_REACHUNLOADADDRESS(30398, "只有待到达卸货地状态的数据才能进行该操作"),
    ONLY_WAIT_UNLOAD(30399, "只有待卸货状态的数据才能进行该操作"),
    UPDATE_UNLOAD_ADDRESS_ERROR(30401, "当前业务类型不支持修改卸货地址"),
    CORRECT_STATUS_ERROR(30403, "仅待纠错运单允许纠错"),
    CARRIER_ORDER_CORRECT_INFO_EMPTY(30404, "运单纠错信息不存在"),
    CARRIER_ORDER_CORRECT_AMOUNT_ERROR(30405, "实提数量不能小于实卸数量"),
    SEARCH_CARRIER_ORDER_AMOUNT_MAX(30406, "查询数量最多25条"),
    STOCK_IN_AMOUNT_ERROR(30407, "入库数量异常"),
    SEARCH_MILEAGE_BY_LON_LAT(30408, "根据经纬度查询里程数信息：未查询到"),
    UPDATE_UNLOAD_ADDRESS_ERROR_FOR_STOCK_IN(30409, "仓库货物已经入库不能修改"),
    REGION_NAME_HAS_EXIST(30410, "大区名称已经存在"),
    REGION_INFO_IS_EMPTY(30411, "大区信息不存在"),
    CHOOSE_CITY_IS_OCCUPIED(30412, "当前选择城市已经被其他大区关联"),
    RECYCLE_ORDER_DISPATCH(30413, "回收类型需求单只能单独调度"),
    REGION_ENABLE_DISABLE_ERROR(30414, "物流大区启用/禁用失败"),
    REGION_DELETE_ERROR(30415, "大区启用状态不可删除"),
    CARRIER_ORDER_IS_EMPTY(30416, "运单已放空，不能进行该操作"),
    CARRIER_ORDER_EMPTY_STATUS_ERROR(30417, "仅待提货以及待到达提货地状态允许放空"),
    DEMAND_ORDER_NOT_LEYI(30418, "非云盘需求单无法操作"),
    CARRIER_ORDER_CANNOT_EMPTY(30420, "运单不符合放空状态"),
    LOAD_ERROR_AMOUNT_ERROR(30423, "提错托盘数量不能大于差异数"),
    LOSE_ERROR_AMOUNT_ERROR(30424, "遗失托盘数量不能大于差异数"),
    NOT_ALLOWED_PICKUP(30425, "货物未出库，不能提货!"),
    NOT_ALLOWED_CANCEL(30426, "货物出库状态已经更新，不能操作!"),
    LOAD_AMOUNT_ERROR_FOR_LE_YI(30427, "按照出库单货物种类【sku】数量填数量"),
    DEMAND_ORDER_DISPATCH_MAX(30428, "需求单最多被调度99次"),
    VEHICLE_DRIVER_ASSOCIATED_WITH_THE_SAME_INSTITUTION(30429, "车辆驾驶员必须归属同机构才能关联"),
    NOT_CONTACTNAME(30430, "请填写姓名,为2-50字"),
    NOT_CONTACTPHONE(30431, "请正确填写手机号"),
    NOT_CONTACTPROVICE(30432, "请选择省份"),
    NOT_CONTACTCITY(30433, "请选择城市"),
    NOT_CONTACTAREA(30434, "请选择区域"),
    NOT_CONTACTCERTIFICATION_DEPARTMENT_DETAIL(30435, "请填写发证机关详情"),
    DRIVER_CARRIER_REL_EMPTY(30436, "司机车主关联关系不存在"),
    VEHICLE_CARRIER_REL_EMPTY(30437, "车辆车主关联关系不存在"),
    PARAMS_COMPANY_NAME_EMPTY(30438, "请正确维护公司名称"),
    COMPANY_ERROR_CARRIER_ROAD_AMEND_DATE(30439, "选择正确的道路许可证有效期"),
    COMPANY_CARRIER_IDENTITYCARD_TIME_ERROR(30440,"请正确填写身份证有效期"),
    PARAMS_COMPANY_WATER_MARK_EMPTY(30441, "请正确维护水印名称"),
    COMPANY_CARRIER_TIME_ERROR(30442,"请正确填写公司有效期"),
    NOT_REMOVE_CARNUM_DATA(30443, "请选择添加的车牌号"),
    NO_COMPANY_CARRIER(30444, "车主不存在"),
    HIGHER_THAN_VEHICLENO_SIZE(30445, "车牌号数量大于50"),
    PASSWORD_UPDATE_FAIL(30447, "密码更新失败"),
    NO_COMPANYTYPE(30448, "非企业类型不能新增"),
    PHONE_UPDATE_FAIL(30449, "手机号更新失败"),
    IDENTITY_INFORMATION_INCOMPLETE(30450, "账号信息不全，需要补充身份证号"),
    OPERATION_NOT_ALLOWED_FOR_COMPANIES(30451,"非企业车主公司不允许此操作"),
    CANNOT_EDIT_SELF(30452,"不能对自身账号进行操作"),
    UNCHANGEABLE(30453,"不可修改"),
    ACCOUNT_IS_ENABLED(30454, "账号已启用，请勿重复操作!"),
    ACCOUNT_IS_DISABLED(30455, "账号已禁用，请勿重复操作!"),
    UPDATE_PHONE_ACCOUNT_EXIST(30456, "账号已经存在，请联系调度员"),
    ENABLE_IS_NOT_REMOVED(30457, "启用状态下,账号无法删除!"),
    ONLY_DRIVERS_OF_THE_DRIVER_TYPE_CAN_BE_DISPATCHED(30459,"只能选择驾驶员类型的司机"),
    CARRIER_ORDER_IS_STOCK_OUT(30460, "运单已出库完成"),
    BASIC_DATA_ERROR(30462,"当前数据有误，请联系调度员"),
    VEHICLE_OIL_CARD_CARD_VEHICLE_EXIST(30463, "一张油卡只能绑定一辆车"),
    VEHICLE_OIL_CARD_CARD_EXIST_VEHICLE_NOT_EXIST(30464, "油卡已存在，请直接绑定"),
    VEHICLE_OIL_CARD_VEHICLE_EXIST(30465, "一辆车只能绑定一张油卡"),
    VEHICLE_OIL_CARD_VEHICLE_ERROR(30466, "请选择自主/自营的牵引车或一体车"),
    VEHICLE_OIL_CARD_NOT_EXIST(30467, "车辆油卡信息不存在"),
    VEHICLE_OIL_CARD_STATUS_ERROR(30468, "车辆油卡绑定状态错误"),
    DRIVER_COST_APPLY_NOT_EXIST(30469, "费用申请记录不存在"),
    DRIVER_COST_APPLY_REPEAL(30470, "用户已经撤销，请刷新页面"),
    DRIVER_COST_APPLY_AUDIT_STATUS_ERROR(30471, "审核状态有误"),
    PERSON_COMPANY_ACCOUNT_NOT_DELETE(30472, "个人车主账号不能删除"),
    DATA_ERROR_AND_RE_LOGIN(30473, "当前数据有误,请重新登陆后重试!"),
    VEHICLE_OIL_CARD_IS_NULL(30474, "请联系调度员绑定油卡"),
    DRIVER_COST_APPLY_OPERATE_ERROR(30475, "仅已驳回才能操作"),
    ONLY_INTERIOR_STAFF(30476, "仅内部司机可以申请费用"),
    DRIVER_COST_APPLY_AUDITED(30477, "您的费用申请已经被批准，无法撤销"),
    DRIVER_COST_APPLY_CANCEL_ERROR(30478, "仅待审核或已驳回才能操作"),
    NOT_UNBIND_OLI_CARD(30479, "有待审核费用记录，不可解绑"),
    RENEWABLE_ORDER_NOT_EXIST(30480, "新生订单不存在"),
    PUBLISH_RENEWABLE_ORDER_PRICE_OVER(30481, "单笔不能超过3000元"),
    ORDER_STATUS_NOT_MATCH(30482, "订单状态不匹配"),
    UNCONFIRMED_GOODS(30483, "未确认货物信息"),
    NOT_UPLOAD_TICKETS(30484, "未上传单据"),
    ASSIGN_DRIVER(30485, "最多支持25条待指派的订单指派司机"),
    UPDATE_DRIVER(30486, "最多支持25条待确认的订单更换司机车辆"),
    PUBLISH_RENEWABLE_ORDER_LOAD_ADDRESS_CODE_ERROR(30487, "发货人已经存在地址，请返回发货人列表选择"),
    YELO_LIFE_CUSTOMER_ACCOUNT_DISABLED(30488, "客户账号被禁用，无法代客户下单"),
    CARRIER_ORDER_ALREADY_SIGN_UP(30489, "运单已签收,请勿重复操作"),
    UPDATE_UNLOAD_ADDRESS_ERROR_LIFE_ORDER(30490, "运单状态与操作不符"),
    UPDATE_UNLOAD_ADDRESS_ONLY_LIFE_ORDER(30491, "仅乐橘新生的运单才可以操作"),
    DRIVER_APPOINT_NOT_EXIST(30492, "预约记录不存在"),
    CARRIER_ORDER_IS_STOCK_IN(30493, "运单已入库完成"),
    DRIVER_APPOINT_ASSOCIATED_VEHICLE(30494, "预约记录已关联车辆"),
    RECYCLE_ORDER_IS_NULL(30495, "新生订单审核数据不存在"),
    RECYCLE_ORDER_CANCELED(30496, "新生订单审核数据已取消"),
    RECYCLE_ORDER_AUDIT_THROUGH(30497, "新生订单审核数据已审核"),
    OPERATION_TYPE_ERROR(30498, "操作类型错误"),
    OPERATION_RECYCLE_ORDER_ERROR(30499, "操作失败,请重试"),
    RENEWABLE_ORDER_INOPERABLE(30500, "仅支持对单个待确认的订单执行此操作"),
    LOAD_UNLOAD_AMOUNT_ERROR(30501, "超出货物数量范围"),
    TEMPORARY_FEE_NOT_EXIST(30502, "临时费用不存在"),
    TEMPORARY_FEE_NOT_REVOKE(30503, "非待提交/待审核/已驳回状态的数据无法撤销"),
    CARRIER_ORDER_ONLY_ALREADY_SIGN_UP(30504, "签收后才能操作"),
    CARRIER_FREIGHT_NOT_EXIST(30505, "车主不存在运价"),
    ENTRUST_SETTLEMENT_PAY(30506, "货主费用已结算,无法编辑"),
    CARRIER_FREIGHT_ALREADY_EXIST(30507, "车主已经存在运价"),
    ROUTE_ALREADY_EXIST(30508, "路线已经存在，请检查路线"),
    CARRIER_COMPANY_NO_PERMISSION(30509,"当前车主没有权限，请检查车主详情"),
    CARRIER_ORDER_DATA_EXIST(30510,"当前运单已经生成数据，不能申请临时费用"),
    TEMPORARY_FEE_NOT_REVOKE_FOR_WEB(30511, "非待审核/已驳回状态的数据无法撤销"),
    ONLY_SUPPORT_DELETE_DISABLED_FREIGHT(30512, "仅支持删除禁用运价"),
    FREIGHT_NOT_EXIST(30513, "车主运价不存在"),
    ONLY_ENABLED_CAN_DISABLED(30514, "仅启用车主运价可禁用"),
    ONLY_DISABLED_CAN_ENABLED(30515, "仅禁用车主运价可启用"),
    TEMPORARY_FEE_NOT_AUDIT(30516, "审核状态不正确，无法审核"),
    FREIGHT_ADDRESS_NOT_EXIST(30517, "车主运价细则不存在"),
    TEMPORARY_FEE_NOT_REJECT(30518, "审核状态不正确，无法驳回"),
    SELECT_SAME_CARRIER_FREIGHT(30519, "请选择同一车主运价进行修改"),
    FREIGHT_ADDRESS_RULE_NOT_EXIST(30520, "车主运价规则不存在"),
    FREIGHT_ADDRESS_RULE_PRICE_NOT_EXIST(30521, "车主运价价格不存在"),
    TEMPORARY_FEE_CARRIER_ORDER_NOT_RELATION(30522, "该运单未关联此临时费用"),
    CARRIER_ORDER_COMPANY_CARRIER_NOT_RELATION(30523, "该运单未关联此车主"),
    TEMPORARY_FEE_NOT_COMMIT(30524, "审核状态不正确，无法提交"),
    ONLY_OUR_COMPANY_EDIT_CARRIER_PRICE(30525, "运单车主为我司时无法编辑车主费用"),
    DISPATCH_VEHICLE_ERROR(30526, "当前调度的车辆信息有误"),
    DISPATCH_DRIVER_ERROR(30527, "当前调度的司机信息有误"),
    ADD_TEMPORARY_FEE_ERROR(30528, "仅限云盘业务运单提交临时费用"),
    SIGN_CARRIER_PRICE_ERROR(30529, "请输入正确的车主费用,为大于0小于100000"),
    CARRIER_ORDER_OPERATION_ERROR(30530, "当前状态不能操作"),
    GET_ACCESS_TOKEN_ERROR(30531, "获取access token失败"),
    GENERATE_SCHEME_ERROR(30532, "生成小程序码失败"),
    ONLY_ADD_OUR_COMPANY_DRIVER(30533, "只允许添加我司司机"),
    OTHER_COMPANY_DEL_PERMISSION(30534, "只有其他车主才能进行该操作"),
    THE_DRIVER_IS_CARRYING_GOODS(30535, "您要删除的司机正在承运货物，请先去更换司机"),
    THE_VEHICLE_IS_CARRYING_GOODS(30536, "您要删除的车辆正在承运货物，请先去更换车辆"),
    STAFF_ONLY_DISABLED(30537, "仅禁用状态才可以操作"),
    STAFF_HAVE_VEHICLE_RELATION(30538, "请先删除车辆关联关系"),
    ONLY_OUR_COMPANY_EDIT_STAFF_PROPERTY(30539, "驾驶员与其他承运商有关联关系！"),
    ONLY_OUR_COMPANY_EDIT_VEHICLE_PROPERTY(30540, "车辆与其他承运商有关联关系！"),
    ALREADY_RELEVANCY_RECONCILIATION(30541, "已关联对账,无法操作！"),
    CARRIER_ORDER_ALREADY_RELEVANCY_RECONCILIATION(30542, "当前运单已经生成数据，不能申请临时费用"),
    PLATFORM_COMPANY_EXIST(30543, "结算主体名称不可重复"),
    PLATFORM_COMPANY_NOT_EXIST(30544, "结算主体不存在"),
    CHOOSE_SETTLE_STATEMENT_CARRIER_ORDER(30545, "请选择申请对账的运单"),
    SETTLEMENT_STATEMENT_PLATFORM_OR_COMPANY_DIFFERENT(30546, "必须是同一车主才能生成对账单"),
    CREATE_SETTLE_APPLY_FEE_ZERO(30547, "当前对账单没有对账费用，无法对账"),
    CREATE_SETTLE_FAIL(30548, "生成对账单失败,请稍后重试!"),
    CARRIER_ORDER_IS_ARCHIVE(30549, "当前对账单内运单，仅允许归档一次"),
    SETTLE_STATEMENT_NOT_EXIST(30550, "对账单不存在"),
    SETTLE_STATEMENT_IS_NOT_ACCOUNT_CHECKED(30551, "对账后才能归档"),
    ARCHIVED_RECORD_NO_OPERATION_ALLOWED(30552, "当前归档记录无法编辑"),
    SETTLE_STATEMENT_CANNOT_EDIT_NAME(30553, "当前对账单状态不能编辑名称"),
    SETTLE_STATEMENT_CANNOT_EDIT_MONTH(30554, "当前对账单不能调整费用月份"),
    SETTLE_STATEMENT_CANNOT_EDIT_PLATFORM_NAME(30555, "当前对账单不能修改结算主体"),
    SETTLE_STATEMENT_CANNOT_EDIT_TAX_POINT(30556, "当前对账单状态不能编辑费点"),
    SETTLE_STATEMENT_STATE_ERROR(30557, "对账单状态错误"),
    SETTLE_STATEMENT_CANNOT_EDIT_ADJUST(30558, "仅审核时可调整差异"),
    SETTLE_STATEMENT_CARRIER_ORDER_UPDATE_ERROR(30559, "更新对账单运单状态错误"),
    APPLY_SETTLE_STATEMENT_MAX(30560, "超出对账单运单上限,最多5000条"),
    SETTLE_STATEMENT_CARRIER_ORDER_EMPTY(30561, "对账单必须关联一条运单！"),
    CARRIER_ORDER_UPDATE_TICKET_WEB_ERROR(30562, "请在运单卸货后，再上传回单！"),
    ATTENDANCE_CLOCK_REPEAT(30563,"上班打卡成功, 请勿重复打卡"),
    ATTENDANCE_CLOCK_COMPLETE(30564,"您已完成打卡"),
    ATTENDANCE_RECORD_NOT_EXISTS(30565,"考勤打卡记录不存在"),
    ATTENDANCE_CHANGE_APPLY_NOT_CURRENT_MONTH(30566,"只能申请修改本月考勤"),
    ATTENDANCE_CHANGE_APPLY_EXISTS(30567,"已申请变更, 请勿重复操作"),
    ATTENDANCE_CHANGE_APPLY_TIME_CROSS(30568,"下班不能早于上班时间"),
    ATTENDANCE_CHANGE_NOT_UNDO(30569,"您的申请状态已经变更，无法撤销"),
    ATTENDANCE_CHANGE_NOT_EXISTS(30570,"考勤申请变更不存在"),
    ATTENDANCE_CHANGE_HAD_WITHDRAWN(30571,"用户已经撤销，请刷新页面"),
    ATTENDANCE_CHANGE_HAD_REVIEWED(30572,"审核状态已经变更，请刷新页面"),
    ATTENDANCE_CHANGE_FAILURE(30573,"考勤变更申请更新失败"),
    LEAVE_APPLY_NOT_EXISTS(30574, "请假申请记录不存在"),
    LEAVE_APPLY_NOT_UNABLE_SUBMIT(30575, "您的申请状态已经变更，请刷新页面"),
    LEAVE_APPLY_TIME_TOO_LONG(30576, "请假时长不能遇大于30天"),
    LEAVE_APPLY_TIME_CROSS(30577,"结束时间不能早于开始时间"),
    LEAVE_APPLY_NOT_UNDO(30578, "您的申请状态已经变更，无法撤销"),
    LEAVE_APPLY_HAD_WITHDRAWN(30579,"用户已经撤销，请刷新页面"),
    LEAVE_APPLY_AUDIT_HAD_CHANGE(30580,"审核状态已经变更，请刷新页面"),
    LEAVE_APPLY_TIME_CONFLICT(30581,"您的请假时间发生冲突，请重新选择"),
    VEHICLE_NUMBER_CANNOT_CHANGE(30582, "车牌号不可修改"),
    RESERVE_APPLY_NOT_EXIST(30583, "备用金申请不存在"),
    RESERVE_APPLY_AUDIT_HAD_CHANGE(30584, "当前申请状态发生变化，请返回列表查看"),
    RESERVE_BALANCE_NOT_EXIST(30585, "余额账户不存在"),
    RESERVE_BALANCE_EXIST(30586, "余额已存在"),
    RESERVE_BALANCE_INSUFFICIENT(30587, "司机可用余额不足，请检查备用金"),
    COMPANY_ACCOUNT_IS_EXIST(30588, "公司账户已存在"),
    COMPANY_ACCOUNT_STATUS_ERROR(30589, "账号状态已经更新，请勿操作"),
    DRIVER_ACCOUNT_IS_EXIST(30590, "司机账户已存在"),
    DRIVER_ACCOUNT_NOT_EXIST(30591, "司机账户不存在"),
    RESERVE_APPLY_STATUS_CHANGE_NOT_UNDO(30592, "您的申请状态已经变更，无法重新提交"),
    DRIVER_ACCOUNT_STAFF_PROPERTY_ERROR(30593, "仅自主或自营人员可以操作"),
    RESERVE_APPLY_AUDIT_HAD_CHANGE_DRIVER(30594, "您的费用申请状态已经更改，无法撤销"),
    COMPANY_ACCOUNT_NOT_EXIST(30595, "公司账户不存在"),
    REVERSE_CHARGES_NOT_EMPTY(30596, "冲销费用不允许为空"),
    REVERSE_APPLY_CARD_UPDATES(30597, "银行卡已经更新，请重新选择银行卡"),
    REVERSE_BALANCE_AMOUNT_CHANGES(30598, "用户余额有变更，请刷新界面"),
    COMPANY_CARRIER_AUTH_INFO_IS_NULL(30599, "车主授权信息不存在"),
    COMPANY_CARRIER_AUTH_STATUS_ERROR(30600, "车主授权状态错误"),
    REQUEST_BEST_SIGN_SERVICE_ERROR(30601, "调用上上签服务失败"),
    VERIFICATION_CODE_ERROR(30602, "验证码错误或已失效"),
    REAL_NAME_AUTH_INFO_NOT_EXIST(30603, "实名认证信息不存在"),
    REAL_NAME_AUTH_INFO_EXIST(30604, "实名认证信息已存在"),
    CHECK_AUTHORIZATION(30605, "请先完成授权"),
    CHECK_REAL_NAME(30606, "请先完成实名认证"),
    FACE_AUTH_ERROR(30607, "人脸认证失败"),
    COMPANY_NOT_AUTH(30608, "请车主先完成实名认证/授权"),
    STAFF_ALREADY_REAL_NAME_AUTH(30609, "司机已经完成实名认证，请如实提供相关信息"),
    SYS_MAILING_ADDRESS_NOT_EXIST(30610, "系统邮寄信息配置不存在"),
    CARRIER_ALREADY_AUTHORIZATION(30611, "当前车主已经完成授权"),
    CARRIER_AUTHORIZATION_ARCHIVED_CHANGE(30612, "归档状态已变更，请刷新界面"),
    NOT_CARRIER_AUTHORIZATION_ARCHIVED(30613, "当前车主未授权，无法归档"),
    CARRIER_AUTHORIZATION_AUDIT_CHANGE(30614, "审核状态已变更，请刷新界面"),
    CARRIER_AUTHORIZATION_REVIEW_SUBMIT(30615, "当前车主已提交授权审核"),
    DRIVER_UPDATE_MOBILE_BY_AUTHORIZATION(30616, "请您完成实名认证之后，才能修改手机号!"),
    DRIVER_UPDATE_MOBILE_EXIST_BY_AUTHORIZATION(30617, "账号已经存在，请联系调度员"),
    CARRIER_CAN_NOT_CHANGE_PHONE(30618, "检测到您的手机号含有司机角色，请到乐橘云途小程序中修改账号"),
    CARRIER_CONTACT_FORBIDDEN_NOT_ALLOW_MODIFY(30619, "车主账号已禁用,无法编辑"),
    REAL_NAME_BASIC_INFO_ERROR(30620, "您的基础信息有误，请检查姓名以及身份证号"),
    CARRIER_ALREADY_REAL_NAME_AUTH(30621, "车主已经完成实名认证，请如实提供相关信息"),
    THREE_ELEMENTS_VERIFY_FAIL(30621, "实名信息校验失败，请确认您的手机号是实名购买"),
    DATA_STATUS_ERROR(30622, "当前记录状态已更新,请刷新后重试"),
    WAREHOUSE_SWITCH_CLOSE(30623, "您的权限已关闭，请联系业务人员"),
    ONLY_RECYCLE_LOAD(30624, "仅回收类型运单可进行当前操作"),

    REACH_INFO_NOT_EXISTS(30625, "数据不存在!"),
    RECEIPT_AUDIT_NOT_EXISTS(30626, "回单审核不存在"),
    RECEIPT_NOT_NOT_ALLOWED_EDIT(30627, "回单%s无法编辑"),
    RECEIPT_AUDIT_STATUS_CHANGE(30628, "回单状态已改变，请刷新网页"),
    RELEVANCY_RECONCILIATION_UNABLE_MODIFIABLE(30629, "运单已经加入对账单，不可修改回单"),
    RECEIPT_AUDIT_EXISTS(30630, "回单审核已存在"),

    CARRIER_ORDER_EXCEPTION(30631, "运单存在未处理的异常工单，不能进行当前操作"),
    WORK_ORDER_NOT_EXIST(30632, "工单不存在"),
    WORK_ORDER_ONLY_WAITING_TASK(30633, "待处理的工单才能执行此操作"),
    REPORT_ERROR_DEMAND_ORDER_ERROR_FOR_LEYI(30634, "仅回收类型且待调度允许上报异常"),
    REPORT_ERROR_CARRIER_ORDER_STATUS_ERROR(30635, "运单状态不符，请检查运单状态"),
    REPORT_DEMAND_WORK_ORDER_EXIST(30636, "需求单已经提交工单，请勿重复提交"),
    REPORT_CARRIER_WORK_ORDER_EXIST(30637, "运单已经提交工单，请勿重复提交"),
    WORK_ORDER_STATUS_CANNOT_SUBMITTED(30638, "工单状态不支持重新提交"),
    WORK_ORDER_STATUS_ABNORMAL_UNABLE_PROCESS(30639, "工单状态不匹配，无法处理"),

    RECEIPT_AUTO_AUDIT(30640, "回单已在自动审核，请稍后再试"),
    NO_PERMISSION(30641, "无此操作权限"),
    RECYCLE_OUT_LOAD_AMOUNT_MAX(30642, "提货数不允许大于预提数，请联系调度人员"),
    CARRIER_ORDER_CORRECT_LOAD_ERROR(30643, "实际实提数不能大于原实提数"),
    OUTER_CANCEL_ORDER_ENTRUST_TYPE_ERROR(30644, "仅发货、回收出库类型的才能取消"),
    OUTER_CANCEL_ORDER_ALREADY_SIGN_UP(30645, "物流已经签收，不允许直接取消"),
    OUTER_CANCEL_ORDER_NO_CANCEL(30645, "运单已经关联结算，不允许取消"),
    CARRIER_ORDER_NO_IN_STOCK(30646, "客户未入库"),
    CARRIER_ORDER_CORRECT_LOAD_EQUAL_UNLOAD(30647, "实际实提数要等于实际实卸数"),
    CARRIER_ORDER_CORRECT_TICKETS_IS_NULL(30648, "请上传回单,最多两张"),
    CARRIER_ORDER_CORRECT_CARRIER_PRICE_NO_CHANGE(30649, "当前运单已经加入对账单，运费回单信息不可修改"),
    CARRIER_FREIGHT_CONFIG_SCHEME_EXIST(30650, "区域计价方案配置已存在"),
    CARRIER_FREIGHT_CONFIG_DISTANCE_EXIST(30651, "里程数计价配置已存在"),
    CARRIER_FREIGHT_CONFIG_SCHEME_NOT_EXIST(30652, "区域计价方案配置不存在"),

    ENTRUST_TYPE_HAS_EXIST(30653,"该车主已经配置过此需求类型"),
    FREIGHT_CONFIG_NOT_EXIST(30654,"车主运价配置表数据不存在"),
    NOT_MATCH_ENTRUST_TYPE(30655,"没有匹配的需求类型"),
    NOT_MATCH_CONFIG_TYPE(30656,"没有匹配的价格模式"),

    DISTANCE_CONFIG_HAS_EXIST(30657,"已经存在此收、发货地路线距离配置"),

    DISTANCE_CONFIG_NOT_EXIST(30658,"不存在此距离配置，请刷新页面后重试"),
    MAP_AREA_NOT_EXIST(30659,"高德不存在当前区域，请手动输入配置距离"),
    NOT_ARCHIVED(30660,"未归档，不允许重新归档"),
    ONLY_AUDIT_OPERATED_ERROR(30661, "仅已审核才能操作"),
    ONLY_INTERIOR_STAFF_RED_CHARGE_REFUND(30662, "仅内部司机申请可以进行红冲退款"),
    INVOICE_CODE_AND_NUM_CANNOT_REPEAT(30663, "发票代码+发票号码已经存在"),
    NOT_ALLOWED_RED_CHARGE_REFUND_BY_COST_DEDUCTIONS(30664, "扣款类型申请不允许红冲"),
    CHOOSE_TRADITION_SETTLE_STATEMENT_CARRIER_ORDER(30665, "仅能关联当前车主承运且未关联其他对账单的运单"),
    CARRIER_ORDER_PICKUP_QRCODE_CONFIRM_STATE_ERROR(30666, "客户在复核提货数量，请稍等"),
    CARRIER_ORDER_PICKUP_BILL_CONFIRM_STATE_ERROR(30667, "您已选择二维码提货，请使用二维码提货"),
    CARRIER_ORDER_PICKUP_DELIVER_METHOD_ERROR(30668, "提货方式暂不支持"),
    CARRIER_ORDER_PICKUP_QRCODE_CONFIRM_COUNT_ERROR(30669, "您已选择二维码提货，提货数量不能修改"),

    WORK_GROUP_NOT_EXIST(30670, "智能推送配置不存在"),
    WORK_GROUP_DO_NOT_OPERATE(30671, "选择配置不支持操作"),
    WORK_GROUP_USER_NOT_EXIST(30672, "人员信息有误，请检查"),
    WORK_GROUP_COUNT_MAX(30673, "群名额已满，请注意"),
    WORK_GROUP_OWNER_REPETITION(30674, "请选择不同的人员"),
    WORK_GROUP_SOURCE_ERROR(30675, "推送设置不能更改"),
    NO_QIYA_ORDER(30676,"非我司运单，无权限"),
    INVOICING_ADD_MAX(30677, "不支持增加"),
    CARRIER_ORDER_FEE_EXIST(30678,"运单已存在临时费用"),
    COMMIT_CARRIER_ORDER_FEE_ORDER_STATUS_ERROR(30679,"当前运单不支持操作"),
    APPLY_INVOICING_STATE_ERROR(30680, "账单无法申请开票"),
    INVOICE_NOT_EXIST(30681, "发票信息不存在"),
    INVOICING_MANAGEMENT_NOT_EXIST(30682, "发票管理信息不存在"),
    INVOICE_CODE_NUM_NOT_ONLY_ONE(30683, "发票号码+发票代码不唯一"),


    BIND_ORDER_NOT_EXIST(30684, "竞价单不存在"),

    BIND_ORDER_STATUS_ERROR(30685, "竞价单状态不支持当前操作"),

    BIND_ORDER_DEMAND_NOT_EXIST(30686, "竞价单与需求单关联不存在"),

    LEAST_ONE_DEMAND(30687, "最少保留一个需求单"),

    BIND_ORDER_DEMAND_HAS_EXIST(30688, "竞价单中已存在该需求单"),

    CUSTOMER_BIND_ORDER_QUOTE_EXIST_MANY(30689, "同一竞价单,车主报价单存在多条"),

    BINDING_ORDER_CANNOT_OPERATE(30690, "当前竞价单不可操作"),
    VEHICLE_LENGTH_INFO_NOT_EXIST(30691, "车长信息不存在"),

    ORDER_QUOTE_NOT_EXIST(30692, "报价单不存在"),

    BIND_ORDER_STATE_CHANGED(30693, "竞价单状态发送变化,刷新后再试"),

    NOT_GOODS_COUNT_RANGE(30694, "没有货物数量的区间配置"),

    ORDER_QUOTE_STATUS_ERROR(30695, "报价单状态不支持当前操作"),

    ROUTE_ENQUIRY_NOT_EXIST(30696, "竞价单不存在"),
    ROUTE_ENQUIRY_NOT_OPERATE(30697, "竞价单不支持操作"),
    INVOICING_MANAGEMENT_ARCHIVED_FILE_MAX(30698, "归档文件最多6份"),

    SHIPPING_ORDER_NOT_EXIST(30699,"零担运输单不存在"),
    SHIPPING_ORDER_ITEM_NOT_EXIST(30700,"零担运输单明细不存在"),
    SHIPPING_ORDER_CANNOT_OPERATE(30701, "零担报价单不支持当前操作"),

    CARRIER_ORDER_ADDRESS_DIFFERENT(30702, "运单地址不一致"),
    RESERVATION_ORDER_NOT_EXIST(30703, "预约单不存在"),
    RESERVATION_ORDER_STATUS_ERROR(30704, "预约单状态错误"),
    RESERVATION_ORDER_SIGN_IN_ERROR(30705, "您距离目标地址较远，不支持签到"),
    RESERVATION_TIME_ERROR(30706, "预约时间小于当前时间,请刷新页面后重新选择"),


    SHIPPING_FREIGHT_RULE_NOT_EXIST(30707, "零担运价规则不存在"),

    SHIPPING_FREIGHT_NAME_EISXT(30708, "运价规则名称已存在"),
    SYSTEM_ONT_SUPPORT(30709, "系统不支持"),
    SHIPPING_FREIGHT_CARRIER_COMPANY_IDS_NULL(30710, "请选择取消关联得车主"),

    SHIPPING_FREIGHT_CARRIER_COMPANY_IS_EXIST(30711, "存在已关联的车主"),
    REPEAT_ADDRESS_EXIST(30712, "存在重复地址"),
    RESERVATION_ORDER_ITEM_NOT_EXIST(30713, "预约单明细不存在"),


    CARRIER_ORDER_GOODS_CODE_NOT_EXIST(30713, "卸货商品编码不存在"),
    CARRIER_ORDER_GOODS_CODE_CANNOT_DEP(30714, "商品编码不能重复"),

    CARRIER_ORDER_GOODS_CODE_NOT_STANDARD(30715, "编码不符合标准"),
    SYSTEM_ONT_SUPPORT_ACTION(30716, "系统不支持操作"),
    CARRIER_ORDER_ADDRESS_NOT_EXIST(30717, "运单地址不存在"),
    LOAD_RESERVATION_ORDER_CARRIER_STATUS_ERROR(30718, "只有待到达提货地，待提货状态的运单才能预约提货"),
    UNLOAD_RESERVATION_ORDER_CARRIER_STATUS_ERROR(30719, "只有待到达卸货地，待卸货状态的运单才能预约卸货"),

    CODE_RULE_GROUP_NOT_EXIST(30801, "托盘规则配置不存在"),

    TRAY_CODE_RULE_ERROR(30802, "编码规则不符合托盘编码"),
    PRODUCT_CODE_REPEAT(30803, "%s编码重复"),
    CARRIER_ORDER_LOAD_CODE_NOT_EXIST(30804, "运单提货编码不存在"),
    CARRIER_ORDER_IS_EXT_ORDER(30805, "多提的运单不能操作提货"),
    EXT_CARRIER_ORDER_CANNOT_MORE_PICK_UP(30806, "该运单不能多提"),
    EXT_CARRIER_ORDER_CONFIG_ERROR(30807, "补单配置不存在"),
    CREATE_EXT_CARRIER_ORDER_TYPE_ERROR(30808, "只有回收入库类型的需求单可以补单"),
    CREATE_EXT_CARRIER_ORDER_STATE_ERROR(30809, "该运单状态不符合多提"),
    CREATE_EXT_CARRIER_ORDER_PROJECT_ERROR(30810, "该运单不属于石化板块"),
    CREATE_EXT_CARRIER_ORDER_IS_EXIST(30811, "该运单已有补单"),
    CREATE_EXT_CARRIER_ORDER_GOODS_ERROR(30812, "该运单货物不是共享托盘"),
    CREATE_EXT_CARRIER_ORDER_COMPANY_ERROR(30813, "该运单发起客户不是C1"),
    CREATE_EXT_CARRIER_ORDER_COMPANY_VERSION_ERROR(30814, "该运单客户签约版本号小于2.3.0"),
    ;


    @Override
    public int getCode() {
        return key;
    }

    @Override
    public String getMsg() {
        return value;
    }

    private Integer key;
    private String value;

    CarrierDataExceptionEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
