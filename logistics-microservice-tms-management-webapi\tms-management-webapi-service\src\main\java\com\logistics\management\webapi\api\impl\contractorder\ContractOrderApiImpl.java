package com.logistics.management.webapi.api.impl.contractorder;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import com.logistics.management.webapi.api.feign.contractorder.ContractOrderApi;
import com.logistics.management.webapi.api.feign.contractorder.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.contractorder.mapping.ContractOrderDetailMapping;
import com.logistics.management.webapi.api.impl.contractorder.mapping.ListContractOrderMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.constant.ExportExcelContractOrder;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.tms.api.feign.contractorder.ContractOrderServiceApi;
import com.logistics.tms.api.feign.contractorder.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
public class ContractOrderApiImpl implements ContractOrderApi {

    @Autowired
    private ContractOrderServiceApi contractOrderServiceApi;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 获取合同列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<ContractOrderSearchResponseDto>> searchContractOrderList(@RequestBody ContractOrderSearchRequestDto requestDto) {
        Result<PageInfo<ContractOrderSearchResponseModel>> pageInfoResult = contractOrderServiceApi.searchContractOrderList(MapperUtils.mapperNoDefault(requestDto, ContractOrderSearchRequestModel.class));
        PageInfo<ContractOrderSearchResponseDto> pageInfo = new PageInfo<>();
        if (pageInfoResult.isSuccess()) {
            if (ListUtils.isNotEmpty(pageInfoResult.getData().getList())) {
                List<ContractOrderSearchResponseDto> responseDtos = MapperUtils.mapper(pageInfoResult.getData().getList(), ContractOrderSearchResponseDto.class, new ListContractOrderMapping());
                pageInfo = MapperUtils.mapper(pageInfoResult.getData(), PageInfo.class);
                pageInfo.setList(responseDtos);
            } else {
                pageInfo.setList(new ArrayList<>());
            }
            return Result.success(pageInfo);
        } else {
            return (Result)pageInfoResult;
        }
    }

    /**
     * 导出合同
     * @param requestDto
     * @param response
     */
    @Override
    public void exportContractOrder(ContractOrderSearchRequestDto requestDto, HttpServletResponse response) {
        Result<List<ContractOrderSearchResponseModel>> listResult = contractOrderServiceApi.exportContractOrder(MapperUtils.mapper(requestDto, ContractOrderSearchRequestModel.class));
        if (!listResult.isSuccess()) {
            throw new BizException(listResult.getErrcode(), listResult.getErrmsg());
        }
        List<ContractOrderSearchResponseDto> list = MapperUtils.mapper(listResult.getData(), ContractOrderSearchResponseDto.class, new ListContractOrderMapping());
        String fileName = "合同管理" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM);
        Map<String, String> exportMap = ExportExcelContractOrder.getExcelContractOrder();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() throws Exception {
                return list;
            }
        });
    }

    /**
     * 获取合同详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<ContractOrderDetailResponseDto> getDetail(@RequestBody @Valid ContractOrderDetailRequestDto requestDto) {
        Result<ContractOrderDetailResponseModel> result = contractOrderServiceApi.getDetail(MapperUtils.mapper(requestDto, ContractOrderDetailRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (ContractFileResponseModel fileResponseModel : result.getData().getContractFiles()) {
            sourceSrcList.add(fileResponseModel.getContractFilePath());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(),ContractOrderDetailResponseDto.class,new ContractOrderDetailMapping(configKeyConstant,imageMap)));
    }

    /**
     * 新增修改合同信息
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result saveContract(@RequestBody @Valid AddOrModifyContractOrderRequestDto requestDto) {
        if (requestDto.getContractFiles().size() > CommonConstant.INT_FIFTY){
            throw new BizException(ManagementWebApiExceptionEnum.CONTRACT_FILE_COUNT_MAX);
        }
        if(!CommonConstant.THREE.equals(requestDto.getContractNature()) && StringUtils.isBlank(requestDto.getContractObjectId())){
            throw new BizException(ManagementWebApiExceptionEnum.PLEASE_SELECT_CUSTOMER);
        }
        return Result.success(contractOrderServiceApi.saveContract(MapperUtils.mapperNoDefault(requestDto,AddOrModifyContractOrderRequestModel.class)));
    }

    /**
     * 作废或终止合同
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result terminateOrCancelContract(@RequestBody @Valid TerminateOrCancelContractRequestDto requestDto) {
        return contractOrderServiceApi.terminateOrCancelContract(MapperUtils.mapper(requestDto, TerminateOrCancelContractRequestModel.class));
    }
}
