package com.logistics.tms.controller.routeenquiry;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.routeenquiry.RouteEnquiryBiz;
import com.logistics.tms.controller.routeenquiry.request.*;
import com.logistics.tms.controller.routeenquiry.response.*;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 路线询价
 * @author: wjf
 * @date: 2024/7/9 17:16
 */
@Api(value = "路线询价",tags = "路线询价")
@RestController
@RequestMapping(value = "/service/routeEnquiry")
public class RouteEnquiryController {

    @Resource
    private RouteEnquiryBiz routeEnquiryBiz;

    /*
     * 后台
     */
    /**
     * 查询列表
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/searchList")
    public Result<PageInfo<SearchRouteEnquiryListResponseModel>> searchList(@RequestBody SearchRouteEnquiryListRequestModel requestModel){
        return Result.success(routeEnquiryBiz.searchList(requestModel));
    }

    /**
     * 创建
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/create")
    public Result<Boolean> create(@RequestBody CreateRouteEnquiryRequestModel requestModel){
        routeEnquiryBiz.create(requestModel);
        return Result.success(true);
    }

    /**
     * 查询详情
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/getDetail")
    public Result<GetRouteEnquiryDetailResponseModel> getDetail(@RequestBody GetRouteEnquiryDetailRequestModel requestModel){
        return Result.success(routeEnquiryBiz.getDetail(requestModel));
    }

    /**
     * 取消报价
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/cancelQuote")
    public Result<Boolean> cancelQuote(@RequestBody GetRouteEnquiryDetailRequestModel requestModel){
        routeEnquiryBiz.cancelQuote(requestModel);
        return Result.success(true);
    }

    /**
     * 查询车主报价详情
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/getQuoteDetail")
    public Result<List<GetRouteEnquiryQuoteDetailResponseModel>> getQuoteDetail(@RequestBody GetRouteEnquiryQuoteDetailRequestModel requestModel){
        return Result.success(routeEnquiryBiz.getQuoteDetail(requestModel));
    }

    /**
     * 选择车主报价
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/selectCarrierQuote")
    public Result<Boolean> selectCarrierQuote(@RequestBody RouteEnquirySelectCarrierQuoteRequestModel requestModel){
        routeEnquiryBiz.selectCarrierQuote(requestModel);
        return Result.success(true);
    }

    /**
     * 结算审核
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/settleAudit")
    public Result<Boolean> settleAudit(@RequestBody RouteEnquirySettleAuditRequestModel requestModel){
        routeEnquiryBiz.settleAudit(requestModel);
        return Result.success(true);
    }

    /**
     * 归档
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/archive")
    public Result<Boolean> archive(@RequestBody RouteEnquiryArchiveRequestModel requestModel){
        routeEnquiryBiz.archive(requestModel);
        return Result.success(true);
    }

    /**
     * 查询汇总列表
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/searchSummaryList")
    public Result<PageInfo<SearchRouteEnquirySummaryListResponseModel>> searchSummaryList(@RequestBody SearchRouteEnquirySummaryListRequestModel requestModel){
        return Result.success(routeEnquiryBiz.searchSummaryList(requestModel));
    }

    /**
     * 导出汇总列表
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/exportSummaryList")
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchRouteEnquirySummaryListResponseModel>> exportSummaryList(@RequestBody SearchRouteEnquirySummaryListRequestModel requestModel){
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        return Result.success(routeEnquiryBiz.searchSummaryList(requestModel).getList());
    }



    /*
     * 前台
     */
    /**
     * 前台-查询列表
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/searchListForWeb")
    public Result<PageInfo<SearchRouteEnquiryListForWebResponseModel>> searchListForWeb(@RequestBody SearchRouteEnquiryListForWebRequestModel requestModel){
        return Result.success(routeEnquiryBiz.searchListForWeb(requestModel));
    }
    
    /**
     * 前台-查询详情
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/getDetailForWeb")
    public Result<GetRouteEnquiryDetailForWebResponseModel> getDetailForWeb(@RequestBody GetRouteEnquiryDetailRequestModel requestModel){
        return Result.success(routeEnquiryBiz.getDetailForWeb(requestModel));
    }

    /**
     * 前台-报价
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/quote")
    public Result<Boolean> quote(@RequestBody RouteEnquiryQuoteRequestModel requestModel){
        routeEnquiryBiz.quote(requestModel);
        return Result.success(true);
    }

    /**
     * 前台-取消报价
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/cancelQuoteForWeb")
    public Result<Boolean> cancelQuoteForWeb(@RequestBody GetRouteEnquiryDetailRequestModel requestModel){
        routeEnquiryBiz.cancelQuoteForWeb(requestModel);
        return Result.success(true);
    }

    /**
     * 前台-查询临时报价单回填信息
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/createQuotation")
    public Result<GetCreateQuotationInfoResponseModel> createQuotation(@RequestBody GetRouteEnquiryDetailRequestModel requestModel){
        return Result.success(routeEnquiryBiz.createQuotation(requestModel));
    }

    /**
     * 前台-上传报价单
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/uploadQuotation")
    public Result<Boolean> uploadQuotation(@RequestBody UploadQuotationRequestModel requestModel){
        routeEnquiryBiz.uploadQuotation(requestModel);
        return Result.success(true);
    }

}
