package com.logistics.management.webapi.api.feign.attendance;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.attendance.dto.*;
import com.logistics.management.webapi.api.feign.attendance.hystrix.AttendanceApiHystrix;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;

/**
 * 考勤管理api
 */
@Api(value = "API - AttendanceApi - 考勤管理", tags = "考勤管理")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = AttendanceApiHystrix.class)
public interface AttendanceApi {

    @ApiOperation(value = "考勤打卡列表查询", tags = "1.1.8")
    @PostMapping(value = "/api/attendance/searchAttendanceList")
    Result<PageInfo<SearchAttendanceListResponseDto>> searchAttendanceList(@RequestBody SearchAttendanceListRequestDto requestDto);

    @ApiOperation(value = "导出考勤打卡列表", tags = "1.1.8")
    @PostMapping(value = "/api/attendance/exportAttendanceList")
    void exportAttendanceList(@RequestBody SearchAttendanceListRequestDto requestDto, HttpServletResponse response);

    @ApiOperation(value = "考勤打卡详情查询", tags = "1.1.8")
    @PostMapping(value = "/api/attendance/attendanceDetail")
    Result<AttendanceDetailResponseDto> attendanceDetail(@RequestBody AttendanceDetailRequestDto requestDto);

    @ApiOperation(value = "考勤打卡变更列表查询", tags = "1.1.8")
    @PostMapping(value = "/api/attendance/searchAttendanceChangeList")
    Result<PageInfo<SearchAttendanceChangeListResponseDto>> searchAttendanceChangeList(@RequestBody SearchAttendanceChangeListRequestDto requestDto);

    @ApiOperation(value = "导出考勤打卡变更列表", tags = "1.1.8")
    @PostMapping(value = "/api/attendance/exportAttendanceChangeList")
    void exportAttendanceChangeList(@RequestBody SearchAttendanceChangeListRequestDto requestDto, HttpServletResponse response);

    @ApiOperation(value = "考勤打卡变更详情查询", tags = "1.1.8")
    @PostMapping(value = "/api/attendance/attendanceChangeDetail")
    Result<AttendanceChangeDetailResponseDto> attendanceChangeDetail(@RequestBody AttendanceChangeDetailRequestDto requestDto);

    @ApiOperation(value = "审核考勤打卡变更申请", tags = "1.1.8")
    @PostMapping(value = "/api/attendance/auditAttendanceChangeApply")
    Result<Boolean> auditAttendanceChangeApply(@RequestBody AuditAttendanceChangeApplyRequestDto requestDto);

    @ApiOperation(value = "撤销考勤打卡变更申请", tags = "1.1.8")
    @PostMapping(value = "/api/attendance/cancelAttendanceChangeApply")
    Result<Boolean> cancelAttendanceChangeApply(@RequestBody CancelAttendanceChangeApplyRequestDto requestDto);
}
