<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TOilFilledMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TOilFilled">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_property" jdbcType="INTEGER" property="vehicleProperty" />
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="oil_filled_fee" jdbcType="DECIMAL" property="oilFilledFee" />
    <result column="oil_filled_date" jdbcType="TIMESTAMP" property="oilFilledDate" />
    <result column="oil_filled_type" jdbcType="INTEGER" property="oilFilledType" />
    <result column="liter" jdbcType="INTEGER" property="liter" />
    <result column="top_up_integral" jdbcType="DECIMAL" property="topUpIntegral" />
    <result column="reward_integral" jdbcType="INTEGER" property="rewardIntegral" />
    <result column="sub_card_number" jdbcType="VARCHAR" property="subCardNumber" />
    <result column="sub_card_owner" jdbcType="VARCHAR" property="subCardOwner" />
    <result column="cooperation_company" jdbcType="VARCHAR" property="cooperationCompany" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
    <result column="refund_reason_type" jdbcType="INTEGER" property="refundReasonType" />
    <result column="refund_reason" jdbcType="VARCHAR" property="refundReason" />
  </resultMap>
  <sql id="Base_Column_List">
    id, status, vehicle_id, vehicle_property, vehicle_no, staff_id, name, mobile, source, 
    oil_filled_fee, oil_filled_date, oil_filled_type, liter, top_up_integral, reward_integral, 
    sub_card_number, sub_card_owner, cooperation_company, remark, created_by, created_time, 
    last_modified_by, last_modified_time, valid, refund_reason_type, refund_reason
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_oil_filled
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_oil_filled
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TOilFilled">
    insert into t_oil_filled (id, status, vehicle_id, 
      vehicle_property, vehicle_no, staff_id, 
      name, mobile, source, 
      oil_filled_fee, oil_filled_date, oil_filled_type, 
      liter, top_up_integral, reward_integral, 
      sub_card_number, sub_card_owner, cooperation_company, 
      remark, created_by, created_time, 
      last_modified_by, last_modified_time, valid, 
      refund_reason_type, refund_reason)
    values (#{id,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{vehicleId,jdbcType=BIGINT}, 
      #{vehicleProperty,jdbcType=INTEGER}, #{vehicleNo,jdbcType=VARCHAR}, #{staffId,jdbcType=BIGINT}, 
      #{name,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{source,jdbcType=INTEGER}, 
      #{oilFilledFee,jdbcType=DECIMAL}, #{oilFilledDate,jdbcType=TIMESTAMP}, #{oilFilledType,jdbcType=INTEGER}, 
      #{liter,jdbcType=INTEGER}, #{topUpIntegral,jdbcType=DECIMAL}, #{rewardIntegral,jdbcType=INTEGER}, 
      #{subCardNumber,jdbcType=VARCHAR}, #{subCardOwner,jdbcType=VARCHAR}, #{cooperationCompany,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}, 
      #{refundReasonType,jdbcType=INTEGER}, #{refundReason,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TOilFilled" keyProperty="id" useGeneratedKeys="true">
    insert into t_oil_filled
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleProperty != null">
        vehicle_property,
      </if>
      <if test="vehicleNo != null">
        vehicle_no,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="oilFilledFee != null">
        oil_filled_fee,
      </if>
      <if test="oilFilledDate != null">
        oil_filled_date,
      </if>
      <if test="oilFilledType != null">
        oil_filled_type,
      </if>
      <if test="liter != null">
        liter,
      </if>
      <if test="topUpIntegral != null">
        top_up_integral,
      </if>
      <if test="rewardIntegral != null">
        reward_integral,
      </if>
      <if test="subCardNumber != null">
        sub_card_number,
      </if>
      <if test="subCardOwner != null">
        sub_card_owner,
      </if>
      <if test="cooperationCompany != null">
        cooperation_company,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
      <if test="refundReasonType != null">
        refund_reason_type,
      </if>
      <if test="refundReason != null">
        refund_reason,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleProperty != null">
        #{vehicleProperty,jdbcType=INTEGER},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="oilFilledFee != null">
        #{oilFilledFee,jdbcType=DECIMAL},
      </if>
      <if test="oilFilledDate != null">
        #{oilFilledDate,jdbcType=TIMESTAMP},
      </if>
      <if test="oilFilledType != null">
        #{oilFilledType,jdbcType=INTEGER},
      </if>
      <if test="liter != null">
        #{liter,jdbcType=INTEGER},
      </if>
      <if test="topUpIntegral != null">
        #{topUpIntegral,jdbcType=DECIMAL},
      </if>
      <if test="rewardIntegral != null">
        #{rewardIntegral,jdbcType=INTEGER},
      </if>
      <if test="subCardNumber != null">
        #{subCardNumber,jdbcType=VARCHAR},
      </if>
      <if test="subCardOwner != null">
        #{subCardOwner,jdbcType=VARCHAR},
      </if>
      <if test="cooperationCompany != null">
        #{cooperationCompany,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
      <if test="refundReasonType != null">
        #{refundReasonType,jdbcType=INTEGER},
      </if>
      <if test="refundReason != null">
        #{refundReason,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TOilFilled">
    update t_oil_filled
    <set>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleProperty != null">
        vehicle_property = #{vehicleProperty,jdbcType=INTEGER},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="oilFilledFee != null">
        oil_filled_fee = #{oilFilledFee,jdbcType=DECIMAL},
      </if>
      <if test="oilFilledDate != null">
        oil_filled_date = #{oilFilledDate,jdbcType=TIMESTAMP},
      </if>
      <if test="oilFilledType != null">
        oil_filled_type = #{oilFilledType,jdbcType=INTEGER},
      </if>
      <if test="liter != null">
        liter = #{liter,jdbcType=INTEGER},
      </if>
      <if test="topUpIntegral != null">
        top_up_integral = #{topUpIntegral,jdbcType=DECIMAL},
      </if>
      <if test="rewardIntegral != null">
        reward_integral = #{rewardIntegral,jdbcType=INTEGER},
      </if>
      <if test="subCardNumber != null">
        sub_card_number = #{subCardNumber,jdbcType=VARCHAR},
      </if>
      <if test="subCardOwner != null">
        sub_card_owner = #{subCardOwner,jdbcType=VARCHAR},
      </if>
      <if test="cooperationCompany != null">
        cooperation_company = #{cooperationCompany,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
      <if test="refundReasonType != null">
        refund_reason_type = #{refundReasonType,jdbcType=INTEGER},
      </if>
      <if test="refundReason != null">
        refund_reason = #{refundReason,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TOilFilled">
    update t_oil_filled
    set status = #{status,jdbcType=INTEGER},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_property = #{vehicleProperty,jdbcType=INTEGER},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      staff_id = #{staffId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      source = #{source,jdbcType=INTEGER},
      oil_filled_fee = #{oilFilledFee,jdbcType=DECIMAL},
      oil_filled_date = #{oilFilledDate,jdbcType=TIMESTAMP},
      oil_filled_type = #{oilFilledType,jdbcType=INTEGER},
      liter = #{liter,jdbcType=INTEGER},
      top_up_integral = #{topUpIntegral,jdbcType=DECIMAL},
      reward_integral = #{rewardIntegral,jdbcType=INTEGER},
      sub_card_number = #{subCardNumber,jdbcType=VARCHAR},
      sub_card_owner = #{subCardOwner,jdbcType=VARCHAR},
      cooperation_company = #{cooperationCompany,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER},
      refund_reason_type = #{refundReasonType,jdbcType=INTEGER},
      refund_reason = #{refundReason,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>