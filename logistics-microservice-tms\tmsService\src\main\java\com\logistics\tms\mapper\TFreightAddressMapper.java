package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.freight.model.GetAddressRuleListConditionModel;
import com.logistics.tms.api.feign.freight.model.FreightAddressRuleDetailResponseModel;
import com.logistics.tms.api.feign.freight.model.SearchFreightAddressRequestModel;
import com.logistics.tms.api.feign.freight.model.SearchFreightAddressResponseModel;
import com.logistics.tms.entity.TFreightAddress;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

@Mapper
public interface TFreightAddressMapper extends BaseMapper<TFreightAddress>{

    List<SearchFreightAddressResponseModel> searchFreightAddressList(@Param("params") SearchFreightAddressRequestModel requestModel);

    FreightAddressRuleDetailResponseModel getFreightAddressRuleDetail(@Param("freightAddressId") Long freightAddressId);

    List<TFreightAddress> getListByCondition(@Param("params") GetAddressRuleListConditionModel conditionModel);

    int deleteFreightAddress(@Param("freightAddressIds") String freightAddressIds,@Param("lastModifiedBy") String lastModifyBy,@Param("lastModifiedTime")Date lastModifiedTime);
}