package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TDriverSafeMeetingRelation extends BaseEntity {
    /**
    * 安全例会ID
    */
    @ApiModelProperty("安全例会ID")
    private Long safeMeetingId;

    /**
    * 驾驶员ID
    */
    @ApiModelProperty("驾驶员ID")
    private Long staffId;

    /**
    * 驾驶员姓名
    */
    @ApiModelProperty("驾驶员姓名")
    private String staffName;

    /**
    * 驾驶员手机号
    */
    @ApiModelProperty("驾驶员手机号")
    private String staffMobile;

    /**
    * 人员机构：1 自主，2 外部，3 自营
    */
    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    /**
    * 状态0未学习，1已学习
    */
    @ApiModelProperty("状态0未学习，1已学习")
    private Integer status;

    /**
    * 学习时间
    */
    @ApiModelProperty("学习时间")
    private Date studyTime;

    /**
    * 签字时间
    */
    @ApiModelProperty("签字时间")
    private Date signTime;

    /**
    * 签字时定位地址
    */
    @ApiModelProperty("签字时定位地址")
    private String signLocateAddress;

    /**
    * 驾驶员图片地址
    */
    @ApiModelProperty("驾驶员图片地址")
    private String staffDriverImageUrl;

    /**
    * 签字图片地址
    */
    @ApiModelProperty("签字图片地址")
    private String signImageUrl;
}