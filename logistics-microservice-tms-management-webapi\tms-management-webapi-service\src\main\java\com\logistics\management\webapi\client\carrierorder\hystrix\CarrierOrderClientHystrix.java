package com.logistics.management.webapi.client.carrierorder.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.carrierorder.CarrierOrderClient;
import com.logistics.management.webapi.client.carrierorder.request.*;
import com.logistics.management.webapi.client.carrierorder.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CarrierOrderClientHystrix implements CarrierOrderClient {

    @Override
    public Result<PageInfo<SearchCarrierOrderListResponseModel>> searchList(SearchCarrierOrderListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<WaitAuditVehicleInfoResponseModel> waitAuditVehicleCountInfo(WaitAuditVehicleInfoRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierOrderDetailResponseModel> carrierOrderDetail(CarrierOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetTicketsResponseModel>> getTickets(GetTicketsRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> uploadTickets(UploadTicketsRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> cancelCarrierOrder(CancelCarrierOrderRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierOrderBeforeSignUpResponseModel> carrierOrderListBeforeSignUp(CarrierOrderListBeforeSignUpRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> carrierOrderSignUp(CarrierOrderConfirmSignUpRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> auditOrRejectVehicle(AuditOrRejectVehicleRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<DownloadLadingBillResponseModel> downloadLadingBill(CarrierOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetCarrierOrderLogsResponseModel>> getCarrierOrderLogs(CarrierOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchCarrierOrderListResponseModel>> exportCarrierOrder(SearchCarrierOrderListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> delTickets(DeleteTicketsRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetCarrierOrderWeixinPushResponseModel>> getWeixinPushInfo(GetCarrierOrderWeixinPushRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> confirmPushWeixin(ConfirmPushWeixinRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> updateDriverFreightFee(UpdateDriverFreightFeeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> load(CarrierOrderLoadRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> unload(CarrierOrderUnLoadRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> reachLoadAddress(ReachLoadAddressRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> reachUnloadAddress(ReachUnloadAddressRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<LoadDetailResponseModel>> getLoadDetail(LoadDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> checkExportCarrierOrderTickets(ExportCarrierOrderTicketsRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ExportCarrierOrderTicketsResponseModel> exportCarrierOrderTickets(ExportCarrierOrderTicketsRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> modifyVehicle(ModifyVehicleRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<DriverAndVehicleResponseModel> getDriverVehicleInfoByCarrierOrderId(CarrierOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> verifyEnablePickUpMore(VerifyEnablePickUpMoreReqModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<AssociateExtDemandOrderRespModel> associateExtDemandOrder(AssociateExtDemandOrderReqModel requestModel) {
        return Result.timeout();
    }
}
