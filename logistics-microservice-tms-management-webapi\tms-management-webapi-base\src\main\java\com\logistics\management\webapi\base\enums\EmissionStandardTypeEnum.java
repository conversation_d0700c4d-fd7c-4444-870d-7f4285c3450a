package com.logistics.management.webapi.base.enums;

/**
 * @author: wjf
 * @date: 2021/7/22 13:02
 */
public enum EmissionStandardTypeEnum {
    DEFAULT(0,""),
    GUO_YI(1, "国一"),
    GUO_ER(2, "国二"),
    GUO_SAN(3, "国三"),
    GUO_SI(4, "国四"),
    GUO_WU(5, "国五"),
    GUO_LIU(6, "国六"),
    ;
    private Integer key;
    private String value;

    EmissionStandardTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static EmissionStandardTypeEnum getEnum(Integer key) {
        for (EmissionStandardTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }

    public static EmissionStandardTypeEnum getEnumByValue(String value) {
        for (EmissionStandardTypeEnum t : values()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
