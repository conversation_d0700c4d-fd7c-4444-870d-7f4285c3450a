package com.logistics.tms.controller.carrierorderticketsaudit;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.carrierorderticketsaudit.CarrierOrderTicketsAuditBiz;
import com.logistics.tms.controller.carrierorderticketsaudit.response.GetReceiptAuditDetailResponseModel;
import com.logistics.tms.controller.carrierorderticketsaudit.response.SearchReceiptAuditListResponseModel;
import com.logistics.tms.controller.carrierorderticketsaudit.request.GetReceiptAuditDetailRequestModel;
import com.logistics.tms.controller.carrierorderticketsaudit.request.ReceiptAgainAuditRequestModel;
import com.logistics.tms.controller.carrierorderticketsaudit.request.ReceiptAuditRequestModel;
import com.logistics.tms.controller.carrierorderticketsaudit.request.SearchReceiptAuditListRequestModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequiredArgsConstructor
public class ReceiptAuditController {

    @Resource
    private CarrierOrderTicketsAuditBiz carrierOrderTicketsAuditBiz;

    /**
     * 分页查询回单审核列表
     * @param requestModel 请求 Model
     * @return 回单审核分页列表
     */
    @ApiOperation(value = "回单审核列表", tags = "1.2.9")
    @PostMapping("/service/receiptReviewManagement/searchList")
    public Result<PageInfo<SearchReceiptAuditListResponseModel>> searchList(@RequestBody SearchReceiptAuditListRequestModel requestModel) {
        return Result.success(carrierOrderTicketsAuditBiz.searchList(requestModel));
    }

    /**
     * 查询回单审核详情
     * @param requestModel 请求 Model
     * @return 回单审核详情
     */
    @ApiOperation(value = "回单审核详情", tags = "1.2.9")
    @PostMapping("/service/receiptReviewManagement/getDetail")
    public Result<GetReceiptAuditDetailResponseModel> getDetail(@RequestBody GetReceiptAuditDetailRequestModel requestModel) {
        return Result.success(carrierOrderTicketsAuditBiz.getDetail(requestModel));
    }

    /**
     * 回单审核
     * @param requestModel 请求Model
     * @return boolean
     */
    @ApiOperation(value = "回单审核", tags = "1.2.9")
    @PostMapping("/service/receiptReviewManagement/audit")
    public Result<Boolean> audit(@RequestBody ReceiptAuditRequestModel requestModel) {
        return Result.success(carrierOrderTicketsAuditBiz.audit(requestModel));
    }

    /**
     * 回单重新审核
     * @param requestModel 请求Model
     * @return boolean
     */
    @ApiOperation(value = "回单重新审核", tags = "1.2.9")
    @PostMapping("/service/receiptReviewManagement/againAudit")
    public Result<Boolean> againAudit(@RequestBody ReceiptAgainAuditRequestModel requestModel) {
        return Result.success(carrierOrderTicketsAuditBiz.againAudit(requestModel));
    }
}
