package com.logistics.management.webapi.api.feign.carriercontact.dto;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/7/10 13:56
 */
@Data
public class SearchCarrierContactRequestDto extends AbstractPageForm<SearchCarrierContactRequestDto> {

    @ApiModelProperty("车主类型：1 企业 2 个人")
    private String type;

    @ApiModelProperty("承运商公司名字")
    private String companyCarrierName;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("账号状态 全部为空 禁用0 启用1")
    private String carrierContactStatus;


}
