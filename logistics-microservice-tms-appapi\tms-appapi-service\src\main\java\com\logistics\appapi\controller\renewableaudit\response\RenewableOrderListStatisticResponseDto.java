package com.logistics.appapi.controller.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class RenewableOrderListStatisticResponseDto {

	@ApiModelProperty("全部")
	private String allCount = "0";

	@ApiModelProperty("待确定")
	private String waitConfirm = "0";

	@ApiModelProperty("待审核")
	private String waitAudit = "0";

	@ApiModelProperty("已审核")
	private String audited = "0";
}
