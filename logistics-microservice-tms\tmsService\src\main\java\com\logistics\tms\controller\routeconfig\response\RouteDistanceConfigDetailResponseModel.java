package com.logistics.tms.controller.routeconfig.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RouteDistanceConfigDetailResponseModel {

    @ApiModelProperty(value = "路线距离配置Id")
    private Long routeDistanceConfigId;

    @ApiModelProperty(value = "发货区id", required = true)
    private Long fromAreaId;

    @ApiModelProperty(value = "收货区id", required = true)
    private Long toAreaId;

    @ApiModelProperty(value = "发货区; 省市区拼接")
    private String fromAreaName;

    @ApiModelProperty(value = "卸货区; 省市区拼接")
    private String toAreaName;

    @ApiModelProperty(value = "计费距离（KM）")
    private BigDecimal billingDistance;
}
