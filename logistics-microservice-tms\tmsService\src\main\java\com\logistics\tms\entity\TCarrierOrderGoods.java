package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2022/08/20
*/
@Data
public class TCarrierOrderGoods extends BaseEntity {
    /**
    * 运单ID
    */
    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    /**
    * 需求单货物ID
    */
    @ApiModelProperty("需求单货物ID")
    private Long demandOrderGoodsId;

    /**
    * sku编号
    */
    @ApiModelProperty("sku编号")
    private String skuCode;

    /**
    * 货物品名
    */
    @ApiModelProperty("货物品名")
    private String goodsName;

    /**
    * 大类名称
    */
    @ApiModelProperty("大类名称")
    private String categoryName;

    /**
    * 长
    */
    @ApiModelProperty("长")
    private Integer length;

    /**
    * 宽
    */
    @ApiModelProperty("宽")
    private Integer width;

    /**
    * 高
    */
    @ApiModelProperty("高")
    private Integer height;

    /**
    * 规格
    */
    @ApiModelProperty("规格")
    private String goodsSize;

    /**
    * 运单分配到的S单的预计数量
    */
    @ApiModelProperty("运单分配到的S单的预计数量")
    private BigDecimal expectAmount;

    /**
    * 装货数量/实提数量
    */
    @ApiModelProperty("装货数量/实提数量")
    private BigDecimal loadAmount;

    /**
    * 卸货数量/实卸数量
    */
    @ApiModelProperty("卸货数量/实卸数量")
    private BigDecimal unloadAmount;

    /**
    * 签收件数
    */
    @ApiModelProperty("签收件数")
    private BigDecimal signAmount;
}