package com.logistics.tms.base.enums;

/**
 * @author:lei.zhu
 * @date:2021/9/18 15:29:21
 */
public enum CorrectStatusEnum {
    DEFAULT(-1,""),
    WAIT_CORRECT(0, "待纠错"),
    CORRECT(1,"已纠错"),
    DISPENSE_WITH_CORRECT(2,"无需纠错"),
    ;


    private Integer key;
    private String value;

    CorrectStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static CorrectStatusEnum getEnum(Integer key) {
        for (CorrectStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
