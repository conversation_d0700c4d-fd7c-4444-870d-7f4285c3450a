package com.logistics.management.webapi.api.impl.organization;

import com.logistics.management.webapi.api.feign.organization.IOrganizationApi;
import com.logistics.management.webapi.api.feign.organization.dto.IOrganizationNameResponseDto;
import com.logistics.tms.api.feign.organization.IOrganizationServiceApi;
import com.logistics.tms.api.feign.organization.model.IOrganizationNameResponseModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.base.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class IOrganizationApiImpl implements IOrganizationApi {

    private final IOrganizationServiceApi organizationServiceApi;

    /**
     * 查询所有部门（列表筛选条件接口）
     * @return
     */
    @Override
    public Result<List<IOrganizationNameResponseDto>> getAllOrgForHierarchy() {
        Result<List<IOrganizationNameResponseModel>> result = organizationServiceApi.getAllOrgForHierarchy();
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), IOrganizationNameResponseDto.class));
    }
}
