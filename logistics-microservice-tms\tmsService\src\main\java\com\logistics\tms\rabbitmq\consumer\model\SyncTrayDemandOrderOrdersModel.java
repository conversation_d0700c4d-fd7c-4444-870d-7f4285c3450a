package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 托盘同步需求单到 TMS
 */
@Data
public class SyncTrayDemandOrderOrdersModel {

    @ApiModelProperty("销售单ID")
    private Long orderId;
    @ApiModelProperty("销售单号")
    private String orderCode;
    @ApiModelProperty("数量")
    private BigDecimal totalAmount;
    private Integer relType;
    private String remark;
    @ApiModelProperty("创建人")
    private String createdBy;
    @ApiModelProperty("创建时间")
    private Date createdTime;

}
