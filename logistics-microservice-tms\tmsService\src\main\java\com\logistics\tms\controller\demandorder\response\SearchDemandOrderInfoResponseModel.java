package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SearchDemandOrderInfoResponseModel {

    @ApiModelProperty("需求单状态：1000待调度 2000部分调度 3000调度完成 4000待签收 5000已签收 1取消 2放空 3回退")
    private Integer status;

    private String statusDesc;

    @ApiModelProperty("是否取消")
    private Integer ifCancel;

    @ApiModelProperty("是否放空")
    private Integer ifEmpty;

    @ApiModelProperty("是否回退")
    private Integer ifRollback;

    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("货物名称")
    private String goodsName;

    @ApiModelProperty("下单时间")
    private Date publishTime;

    @ApiModelProperty("发货人")
    private String consignorName;

    @ApiModelProperty("发货人联系方式")
    private String consignorMobile;

    @ApiModelProperty("单位：1 件 2 吨 3 方 4 块")
    private Integer goodsUnit;

    private String goodsUnitLabel;

    @ApiModelProperty("下单数量")
    private BigDecimal goodsAmount;

    @ApiModelProperty("客户名称")
    private String upstreamCustomer;

    @ApiModelProperty("提货省")
    private String loadProvinceName;

    @ApiModelProperty("提货市")
    private String loadCityName;

    @ApiModelProperty("提货区")
    private String loadAreaName;

    @ApiModelProperty("提货详细地址")
    private String loadDetailAddress;

    @ApiModelProperty("提货仓库")
    private String loadWarehouse;
}
