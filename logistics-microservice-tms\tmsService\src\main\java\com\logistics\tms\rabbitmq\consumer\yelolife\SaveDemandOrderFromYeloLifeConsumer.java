package com.logistics.tms.rabbitmq.consumer.yelolife;

import com.logistics.tms.biz.demandorder.DemandOrderForLifeBiz;
import com.logistics.tms.rabbitmq.consumer.model.SyncLifeDemandOrderMessage;
import com.rabbitmq.client.Channel;
import com.yelo.tools.rabbitmq.annocation.EnableMqErrorMessageCollect;
import com.yelo.tools.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * @author: wjf
 * @date: 2024/6/18 13:58
 */
@Component
@Slf4j
public class SaveDemandOrderFromYeloLifeConsumer {

    private ObjectMapper objectMapper = JacksonUtils.getInstance();
    @Resource
    private DemandOrderForLifeBiz demandOrderForLifeBiz;

    @EnableMqErrorMessageCollect
    @RabbitListener(bindings = {@QueueBinding(
            exchange = @Exchange(name = "logistics.qiyatms.topic", type = "topic", durable = "true"),
            value = @Queue(value = "logistics.lifeSyncTmsDemandOrder", durable = "true"),
            key = "lifeSyncTmsDemandOrder")}
            , concurrency = "3")
    public void process(@Payload String message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) throws IOException {
        log.info("新生同步需求单到物流系统：" + message);
        SyncLifeDemandOrderMessage parse = objectMapper.readValue(message, SyncLifeDemandOrderMessage.class);
        demandOrderForLifeBiz.SaveDemandOrderFromYeloLife(parse);
        channel.basicAck(deliveryTag, false);
    }

}
