<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TGpsFeeMapper">
  <select id="searchGpsFeeList" resultType="com.logistics.tms.api.feign.gpsfee.model.SearchGpsFeeListResponseModel">
    select
    id as gpsFeeId,
    status as status,
    vehicle_no as vehicleNo,
    vehicle_property as vehicleProperty,
    name as driver<PERSON>ame,
    mobile as driverPhone,
    gps_service_provider as gpsServiceProvider,
    terminal_type as terminalType,
    service_fee as serviceFee,
    cooperation_period as cooperationPeriod,
    sim_number as simNumber,
    start_date as startDate,
    end_date as endDate,
    finish_date as finishDate,
    cooperation_status as cooperationStatus,
    remark as remark,
    last_modified_by as lastModifiedBy,
    last_modified_time as lastModifiedTime
    from t_gps_fee
    where valid = 1
    <if test="params.status != null">
      <choose>
        <when test="params.status == -1">
          and cooperation_status = 3
        </when>
        <otherwise>
          and status = #{params.status,jdbcType=INTEGER} and cooperation_status != 3
        </otherwise>
      </choose>
    </if>
    <if test="params.vehicleNo != null and params.vehicleNo != ''">
      and instr(vehicle_no,#{params.vehicleNo,jdbcType=VARCHAR})
    </if>
    <if test="params.driverName != null and params.driverName != ''">
      and (instr(name,#{params.driverName,jdbcType=VARCHAR}) or instr(mobile,#{params.driverName,jdbcType=VARCHAR}))
    </if>
    <if test="params.gpsServiceProvider != null and params.gpsServiceProvider != ''">
      and instr(gps_service_provider,#{params.gpsServiceProvider,jdbcType=VARCHAR})
    </if>
    <if test="params.gpsFeeIds != null and params.gpsFeeIds != ''">
      and id in (${params.gpsFeeIds})
    </if>
    <if test="params.vehicleProperty != null">
      and vehicle_property = #{params.vehicleProperty,jdbcType=INTEGER}
    </if>
    order by created_time desc,id desc
  </select>

  <select id="getByVehicleStartDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_gps_fee
    where valid = 1
    and vehicle_id = #{vehicleId,jdbcType=BIGINT}
    and finish_date >= #{startDate,jdbcType=TIMESTAMP}
    and start_date &lt;= #{finishDate,jdbcType=TIMESTAMP}
  </select>

  <select id="searchGpsFeeListCount" resultType="com.logistics.tms.api.feign.gpsfee.model.SearchGpsFeeListCountResponseModel">
    select
    ifnull(count(0),0) as allSettlementCount,
    ifnull(sum(if(status = 0 and cooperation_status != 3,1,0)),0) as waitSettlementCount,
    ifnull(sum(if(status = 1 and cooperation_status != 3,1,0)),0) as partSettlementCount,
    ifnull(sum(if(status = 2 and cooperation_status != 3,1,0)),0) as completeSettlementCount,
    ifnull(sum(if(cooperation_status = 3,1,0)),0) as terminationCount
    from t_gps_fee
    where valid = 1
    <if test="params.vehicleNo != null and params.vehicleNo != ''">
      and instr(vehicle_no,#{params.vehicleNo,jdbcType=VARCHAR})
    </if>
    <if test="params.driverName != null and params.driverName != ''">
      and (instr(name,#{params.driverName,jdbcType=VARCHAR}) or instr(mobile,#{params.driverName,jdbcType=VARCHAR}))
    </if>
    <if test="params.gpsServiceProvider != null and params.gpsServiceProvider != ''">
      and instr(gps_service_provider,#{params.gpsServiceProvider,jdbcType=VARCHAR})
    </if>
    <if test="params.vehicleProperty != null">
      and vehicle_property = #{params.vehicleProperty,jdbcType=INTEGER}
    </if>
  </select>

  <select id="getGpsFeeDetail" resultType="com.logistics.tms.api.feign.gpsfee.model.GpsFeeDetailResponseModel">
    select
    id as gpsFeeId,
    status as status,
    vehicle_id as vehicleId,
    vehicle_no as vehicleNo,
    staff_id as staffId,
    name as driverName,
    mobile as driverPhone,
    gps_service_provider as gpsServiceProvider,
    terminal_type as terminalType,
    service_fee as serviceFee,
    cooperation_period as cooperationPeriod,
    sim_number as simNumber,
    install_time as installTime,
    start_date as startDate,
    end_date as endDate,
    finish_date as finishDate,
    remark as remark
    from t_gps_fee
    where valid = 1
    and id = #{gpsFeeId,jdbcType=BIGINT}
  </select>

  <select id="getCurrentDeductingByIdForSettlement" resultType="com.logistics.tms.controller.vehiclesettlement.response.GetGpsFeeByVehicleIdResponseModel">
    select
    tgf.id as gpsFeeId,
    tgf.status,
    tgf.service_fee as serviceFee,
    tgf.cooperation_period as cooperationPeriod,
    tgf.finish_date as finishDate,
    tdh.deducting_month as deductingMonth,
    tdh.deducting_fee as deductingFee,
    tdh.remaining_deducting_fee as remainingDeductingFee
    from t_gps_fee tgf
    left join (select object_id,deducting_month,deducting_fee,remaining_deducting_fee from t_deducting_history
    where valid = 1 and object_type = 1 and object_id = #{id,jdbcType=BIGINT} and deducting_month &lt;= #{deductingMonth,jdbcType=VARCHAR}
    order by deducting_month desc,id desc limit 1) tdh on tdh.object_id = tgf.id
    where tgf.valid = 1
    and tgf.id = #{id,jdbcType=BIGINT}
  </select>

  <select id="getNotTerminal" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_gps_fee
    where valid = 1
    and cooperation_status != 3
  </select>

  <update id="batchUpdate" parameterType="com.logistics.tms.entity.TGpsFee">
    <foreach collection="list" item="item" separator=";">
      update t_gps_fee
      <set>
        <if test="item.status != null">
          status = #{item.status,jdbcType=INTEGER},
        </if>
        <if test="item.vehicleId != null">
          vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
        </if>
        <if test="item.vehicleNo != null">
          vehicle_no = #{item.vehicleNo,jdbcType=VARCHAR},
        </if>
        <if test="item.vehicleProperty != null">
          vehicle_property = #{item.vehicleProperty,jdbcType=INTEGER},
        </if>
        <if test="item.staffId != null">
          staff_id = #{item.staffId,jdbcType=BIGINT},
        </if>
        <if test="item.name != null">
          name = #{item.name,jdbcType=VARCHAR},
        </if>
        <if test="item.mobile != null">
          mobile = #{item.mobile,jdbcType=VARCHAR},
        </if>
        <if test="item.terminalType != null">
          terminal_type = #{item.terminalType,jdbcType=VARCHAR},
        </if>
        <if test="item.gpsServiceProvider != null">
          gps_service_provider = #{item.gpsServiceProvider,jdbcType=VARCHAR},
        </if>
        <if test="item.simNumber != null">
          sim_number = #{item.simNumber,jdbcType=VARCHAR},
        </if>
        <if test="item.installTime != null">
          install_time = #{item.installTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.startDate != null">
          start_date = #{item.startDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.endDate != null">
          end_date = #{item.endDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.finishDate != null">
          finish_date = #{item.finishDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.serviceFee != null">
          service_fee = #{item.serviceFee,jdbcType=DECIMAL},
        </if>
        <if test="item.cooperationPeriod != null">
          cooperation_period = #{item.cooperationPeriod,jdbcType=INTEGER},
        </if>
        <if test="item.cooperationStatus != null">
          cooperation_status = #{item.cooperationStatus,jdbcType=INTEGER},
        </if>
        <if test="item.remark != null">
          remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null">
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

    <select id="getVehicleBySettlementMonth" resultType="com.logistics.tms.controller.vehiclesettlement.response.GetVehicleBySettlementMonthModel">
        select
        vehicle_id as vehicleId,
        max(id) as objectId
        from t_gps_fee
        where valid = 1
        and status != 2
        and date_format(start_date,'%Y-%m') &lt;= #{settlementMonth,jdbcType=VARCHAR}
        and date_format(finish_date,'%Y-%m') >= #{settlementMonth,jdbcType=VARCHAR}
        and date_format(created_time,'%Y-%m') &lt;= #{settlementMonth,jdbcType=VARCHAR}
        GROUP BY vehicle_id
    </select>
</mapper>