package com.logistics.tms.api.feign.insurancecompany.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.insurancecompany.InsuranceCompanyServiceApi;
import com.logistics.tms.api.feign.insurancecompany.model.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/5/29 14:14
 */
@Component("tmsInsuranceCompanyApiHystrix")
public class InsuranceCompanyApiHystrix implements InsuranceCompanyServiceApi {

    @Override
    public Result<PageInfo<InsuranceCompanyListResponseModel>> searchInsuranceCompanyList(InsuranceCompanyListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<InsuranceCompanyDetailResponseModel> getDetail(InsuranceCompanyDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> saveOrModifyInsuranceCompany(SaveOrModifyInsuranceCompanyRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> enableOrDisable(EnableInsuranceCompanyRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<InsuranceCompanyListResponseModel>> export(InsuranceCompanyListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ImportInsuranceCompanyResponseModel> importInsuranceCompany(ImportInsuranceCompanyRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<FuzzyQueryInsuranceCompanyListResponseModel>> fuzzyQueryInsuranceCompanyByName(FuzzyQueryInsuranceCompanyRequestModel requestModel) {
        return Result.timeout();
    }
}
