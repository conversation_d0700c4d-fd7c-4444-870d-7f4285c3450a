package com.logistics.management.webapi.base.enums;

/**
 * @Author: sj
 * @Date: 2019/5/31 13:59
 */
public enum IfRemindEnum {
    DEFAULT_NULL(-1,""),
    NO(0, "否"),
    YES(1, "是"),
    ;

    private Integer key;
    private String value;

    IfRemindEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static IfRemindEnum getEnum(Integer key) {
        for (IfRemindEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT_NULL;
    }
}

