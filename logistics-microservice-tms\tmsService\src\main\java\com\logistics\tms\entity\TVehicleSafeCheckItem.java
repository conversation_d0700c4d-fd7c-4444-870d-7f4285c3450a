package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleSafeCheckItem extends BaseEntity {
    /**
    * 安全检查ID
    */
    @ApiModelProperty("安全检查ID")
    private Long safeCheckVehicleId;

    /**
    * 检查项目。车辆安全性能：100转向系统，101制动系统，102灯光，103邮箱-轮胎；安全器材：200防护用品使用情况，201安全带，202消防器材，203各部门紧固；车辆其他性能：300传动-悬挂，301GPS设备使用情况
    */
    @ApiModelProperty("检查项目。车辆安全性能：100转向系统，101制动系统，102灯光，103邮箱-轮胎；安全器材：200防护用品使用情况，201安全带，202消防器材，203各部门紧固；车辆其他性能：300传动-悬挂，301GPS设备使用情况")
    private Integer itemType;

    /**
    * 检查状态，0待检查，1合格，2不合格
    */
    @ApiModelProperty("检查状态，0待检查，1合格，2不合格")
    private Integer status;
}