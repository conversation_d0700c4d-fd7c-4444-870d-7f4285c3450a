package com.logistics.appapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2018/10/16 14:34
 */
@Data
public class SearchCarrierOrderCountResponseModel {
    @ApiModelProperty("合计数量")
    private Integer allCount = 0;
    @ApiModelProperty("待提货数量")
    private Integer waitLoadCount = 0;
    @ApiModelProperty("待卸货数量")
    private Integer waitUnloadCount = 0;
    @ApiModelProperty("待签收数量")
    private Integer waitSignUpCount = 0;
}
