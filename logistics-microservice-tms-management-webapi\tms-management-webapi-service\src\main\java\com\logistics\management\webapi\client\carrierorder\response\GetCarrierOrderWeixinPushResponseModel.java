package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetCarrierOrderWeixinPushResponseModel {
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("角色 1 发货方 2 收货方 3 委托方 4 其他")
    private Integer role;
    @ApiModelProperty("角色 1 发货方 2 收货方 3 委托方 4 其他")
    private String roleName;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("是否推送 1 推送 0 不推")
    private Integer ifPush;

}
