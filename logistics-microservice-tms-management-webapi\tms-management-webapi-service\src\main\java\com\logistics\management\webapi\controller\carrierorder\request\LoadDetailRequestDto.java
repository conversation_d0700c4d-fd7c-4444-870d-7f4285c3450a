package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/24 14:04
 */
@Data
public class LoadDetailRequestDto {
    @ApiModelProperty(value = "运单ID",required = true)
    @NotEmpty(message = "请选择运单")
    private List<String> carrierOrderId;
    @ApiModelProperty("节点类型：1 已提货 2 已卸货")
    private String nodeType;
}
