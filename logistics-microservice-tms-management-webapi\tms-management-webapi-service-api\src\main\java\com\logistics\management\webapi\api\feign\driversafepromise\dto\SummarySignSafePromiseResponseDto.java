package com.logistics.management.webapi.api.feign.driversafepromise.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 签订列表 - 汇总
 * @Author: sj
 * @Date: 2019/11/4 14:48
 */
@Data
public class SummarySignSafePromiseResponseDto {
    @ApiModelProperty("全部")
    private String allCount;
    @ApiModelProperty("已经签订")
    private String hasSignCount;
    @ApiModelProperty("未签订")
    private String notSignCount;
}
