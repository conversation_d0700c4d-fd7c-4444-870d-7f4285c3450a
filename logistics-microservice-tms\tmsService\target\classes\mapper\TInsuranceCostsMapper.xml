<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TInsuranceCostsMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TInsuranceCosts">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="settlement_month" jdbcType="VARCHAR" property="settlementMonth" />
    <result column="commercial_insurance_cost" jdbcType="DECIMAL" property="commercialInsuranceCost" />
    <result column="compulsory_insurance_cost" jdbcType="DECIMAL" property="compulsoryInsuranceCost" />
    <result column="cargo_insurance_cost" jdbcType="DECIMAL" property="cargoInsuranceCost" />
    <result column="carrier_insurance_cost" jdbcType="DECIMAL" property="carrierInsuranceCost" />
    <result column="insurance_claims_cost" jdbcType="DECIMAL" property="insuranceClaimsCost" />
    <result column="insurance_claims_time" jdbcType="TIMESTAMP" property="insuranceClaimsTime" />
    <result column="pay_commercial_insurance_cost" jdbcType="DECIMAL" property="payCommercialInsuranceCost" />
    <result column="pay_compulsory_insurance_cost" jdbcType="DECIMAL" property="payCompulsoryInsuranceCost" />
    <result column="pay_cargo_insurance_cost" jdbcType="DECIMAL" property="payCargoInsuranceCost" />
    <result column="pay_carrier_insurance_cost" jdbcType="DECIMAL" property="payCarrierInsuranceCost" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, status, vehicle_id, vehicle_no, settlement_month, commercial_insurance_cost, 
    compulsory_insurance_cost, cargo_insurance_cost, carrier_insurance_cost, insurance_claims_cost, 
    insurance_claims_time, pay_commercial_insurance_cost, pay_compulsory_insurance_cost, 
    pay_cargo_insurance_cost, pay_carrier_insurance_cost, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_insurance_costs
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_insurance_costs
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TInsuranceCosts">
    insert into t_insurance_costs (id, status, vehicle_id, 
      vehicle_no, settlement_month, commercial_insurance_cost, 
      compulsory_insurance_cost, cargo_insurance_cost, 
      carrier_insurance_cost, insurance_claims_cost, 
      insurance_claims_time, pay_commercial_insurance_cost, 
      pay_compulsory_insurance_cost, pay_cargo_insurance_cost, 
      pay_carrier_insurance_cost, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{vehicleId,jdbcType=BIGINT}, 
      #{vehicleNo,jdbcType=VARCHAR}, #{settlementMonth,jdbcType=VARCHAR}, #{commercialInsuranceCost,jdbcType=DECIMAL}, 
      #{compulsoryInsuranceCost,jdbcType=DECIMAL}, #{cargoInsuranceCost,jdbcType=DECIMAL}, 
      #{carrierInsuranceCost,jdbcType=DECIMAL}, #{insuranceClaimsCost,jdbcType=DECIMAL}, 
      #{insuranceClaimsTime,jdbcType=TIMESTAMP}, #{payCommercialInsuranceCost,jdbcType=DECIMAL}, 
      #{payCompulsoryInsuranceCost,jdbcType=DECIMAL}, #{payCargoInsuranceCost,jdbcType=DECIMAL}, 
      #{payCarrierInsuranceCost,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TInsuranceCosts" keyProperty="id" useGeneratedKeys="true">
    insert into t_insurance_costs
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleNo != null">
        vehicle_no,
      </if>
      <if test="settlementMonth != null">
        settlement_month,
      </if>
      <if test="commercialInsuranceCost != null">
        commercial_insurance_cost,
      </if>
      <if test="compulsoryInsuranceCost != null">
        compulsory_insurance_cost,
      </if>
      <if test="cargoInsuranceCost != null">
        cargo_insurance_cost,
      </if>
      <if test="carrierInsuranceCost != null">
        carrier_insurance_cost,
      </if>
      <if test="insuranceClaimsCost != null">
        insurance_claims_cost,
      </if>
      <if test="insuranceClaimsTime != null">
        insurance_claims_time,
      </if>
      <if test="payCommercialInsuranceCost != null">
        pay_commercial_insurance_cost,
      </if>
      <if test="payCompulsoryInsuranceCost != null">
        pay_compulsory_insurance_cost,
      </if>
      <if test="payCargoInsuranceCost != null">
        pay_cargo_insurance_cost,
      </if>
      <if test="payCarrierInsuranceCost != null">
        pay_carrier_insurance_cost,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="settlementMonth != null">
        #{settlementMonth,jdbcType=VARCHAR},
      </if>
      <if test="commercialInsuranceCost != null">
        #{commercialInsuranceCost,jdbcType=DECIMAL},
      </if>
      <if test="compulsoryInsuranceCost != null">
        #{compulsoryInsuranceCost,jdbcType=DECIMAL},
      </if>
      <if test="cargoInsuranceCost != null">
        #{cargoInsuranceCost,jdbcType=DECIMAL},
      </if>
      <if test="carrierInsuranceCost != null">
        #{carrierInsuranceCost,jdbcType=DECIMAL},
      </if>
      <if test="insuranceClaimsCost != null">
        #{insuranceClaimsCost,jdbcType=DECIMAL},
      </if>
      <if test="insuranceClaimsTime != null">
        #{insuranceClaimsTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payCommercialInsuranceCost != null">
        #{payCommercialInsuranceCost,jdbcType=DECIMAL},
      </if>
      <if test="payCompulsoryInsuranceCost != null">
        #{payCompulsoryInsuranceCost,jdbcType=DECIMAL},
      </if>
      <if test="payCargoInsuranceCost != null">
        #{payCargoInsuranceCost,jdbcType=DECIMAL},
      </if>
      <if test="payCarrierInsuranceCost != null">
        #{payCarrierInsuranceCost,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TInsuranceCosts">
    update t_insurance_costs
    <set>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="settlementMonth != null">
        settlement_month = #{settlementMonth,jdbcType=VARCHAR},
      </if>
      <if test="commercialInsuranceCost != null">
        commercial_insurance_cost = #{commercialInsuranceCost,jdbcType=DECIMAL},
      </if>
      <if test="compulsoryInsuranceCost != null">
        compulsory_insurance_cost = #{compulsoryInsuranceCost,jdbcType=DECIMAL},
      </if>
      <if test="cargoInsuranceCost != null">
        cargo_insurance_cost = #{cargoInsuranceCost,jdbcType=DECIMAL},
      </if>
      <if test="carrierInsuranceCost != null">
        carrier_insurance_cost = #{carrierInsuranceCost,jdbcType=DECIMAL},
      </if>
      <if test="insuranceClaimsCost != null">
        insurance_claims_cost = #{insuranceClaimsCost,jdbcType=DECIMAL},
      </if>
      <if test="insuranceClaimsTime != null">
        insurance_claims_time = #{insuranceClaimsTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payCommercialInsuranceCost != null">
        pay_commercial_insurance_cost = #{payCommercialInsuranceCost,jdbcType=DECIMAL},
      </if>
      <if test="payCompulsoryInsuranceCost != null">
        pay_compulsory_insurance_cost = #{payCompulsoryInsuranceCost,jdbcType=DECIMAL},
      </if>
      <if test="payCargoInsuranceCost != null">
        pay_cargo_insurance_cost = #{payCargoInsuranceCost,jdbcType=DECIMAL},
      </if>
      <if test="payCarrierInsuranceCost != null">
        pay_carrier_insurance_cost = #{payCarrierInsuranceCost,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TInsuranceCosts">
    update t_insurance_costs
    set status = #{status,jdbcType=INTEGER},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      settlement_month = #{settlementMonth,jdbcType=VARCHAR},
      commercial_insurance_cost = #{commercialInsuranceCost,jdbcType=DECIMAL},
      compulsory_insurance_cost = #{compulsoryInsuranceCost,jdbcType=DECIMAL},
      cargo_insurance_cost = #{cargoInsuranceCost,jdbcType=DECIMAL},
      carrier_insurance_cost = #{carrierInsuranceCost,jdbcType=DECIMAL},
      insurance_claims_cost = #{insuranceClaimsCost,jdbcType=DECIMAL},
      insurance_claims_time = #{insuranceClaimsTime,jdbcType=TIMESTAMP},
      pay_commercial_insurance_cost = #{payCommercialInsuranceCost,jdbcType=DECIMAL},
      pay_compulsory_insurance_cost = #{payCompulsoryInsuranceCost,jdbcType=DECIMAL},
      pay_cargo_insurance_cost = #{payCargoInsuranceCost,jdbcType=DECIMAL},
      pay_carrier_insurance_cost = #{payCarrierInsuranceCost,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>