package com.logistics.management.webapi.controller.settlestatement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;

@Data
public class CarrierModifyTaxPointRequestDto {

    @ApiModelProperty(value = "对账单id", required = true)
    @NotBlank(message = "对账单id不能为空")
    private String settleStatementId;

    @ApiModelProperty(value = "临时费用费点",required = true)
    @NotBlank(message = "请维护临时费用费点，请输入0.00~20.00的数字")
    @DecimalMin(value = "0.00", message = "请维护临时费用费点，请输入0.00~20.00的数字")
    @DecimalMax(value = "20.00", message = "请维护临时费用费点，请输入0.00~20.00的数字")
    private String otherFeeTaxPoint;

    @ApiModelProperty(value = "运费费点",required = true)
    @NotBlank(message = "请维护运费费点，请输入0.00~20.00的数字")
    @DecimalMin(value = "0", message = "请维护运费费点，请输入0.00~20.00的数字")
    @DecimalMax(value = "20", message = "请维护运费费点，请输入0.00~20.00的数字")
    private String freightTaxPoint;
}
