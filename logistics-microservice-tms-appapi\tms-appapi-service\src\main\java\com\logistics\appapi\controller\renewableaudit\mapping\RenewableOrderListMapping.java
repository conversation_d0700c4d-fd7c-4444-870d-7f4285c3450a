package com.logistics.appapi.controller.renewableaudit.mapping;

import com.logistics.appapi.base.enums.RenewableAuditStatusEnum;
import com.logistics.appapi.base.enums.RenewableBusinessType;
import com.logistics.appapi.client.renewableaudit.response.RenewableOrderListResponseModel;
import com.logistics.appapi.controller.renewableaudit.response.RenewableOrderListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/9/13 11:21
 */
public class RenewableOrderListMapping extends MapperMapping<RenewableOrderListResponseModel, RenewableOrderListResponseDto> {

    @Override
    public void configure() {
        RenewableOrderListResponseModel source = getSource();
        RenewableOrderListResponseDto destination = getDestination();

        destination.setStatusLabel(RenewableAuditStatusEnum.getEnum(source.getStatus()).getValue());
        destination.setPublishTime(DateUtils.dateToString(source.getPublishTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        destination.setVerifiedGoodsAmountTotal(source.getVerifiedGoodsAmountTotal().stripTrailingZeros().toPlainString());

        //客户公司名称转换
        if (RenewableBusinessType.PERSON.getCode().equals(source.getBusinessType())) {
            destination.setCustomerName(Optional.ofNullable(source.getCustomerUserName()).orElse("") + " " + Optional.ofNullable(source.getCustomerUserMobile()).orElse(""));
        }
    }
}
