package com.logistics.management.webapi.client.demandorder.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.demandorder.DemandOrderClient;
import com.logistics.management.webapi.client.demandorder.request.*;
import com.logistics.management.webapi.client.demandorder.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/27 16:04
 */
@Component
public class DemandOrderClientHystrix implements DemandOrderClient {
    @Override
    public Result<Boolean> saveDemandOrder(SaveDemandOrderRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<DemandOrderResponseModel>> searchList(DemandOrderSearchRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SearchListStatisticsResponseModel> searchListStatistics(DemandOrderSearchRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> cancelDemandOrder(DemandOrderCancelRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<DemandOrderDetailResponseModel> getDetail(DemandOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetDemandOrderLogsResponseModel>> getDemandOrderLogs(DemandOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<DemandOrderResponseModel>> exportDemandOrder(DemandOrderSearchRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetPublishDemandOrderDetailResponseModel> getPublishDemandOrderDetail(DemandOrderIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> confirmSynNetworkFreight(DemandOrderIdListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ModifyCarrierDetailResponseModel> modifyCarrierDetail(ModifyCarrierDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> modifyCarrier(ModifyCarrierRequestModel requestModel) {
        return Result.timeout();
    }
}
