package com.logistics.tms.api.feign.driverpayee;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.common.SrcUrlModel;
import com.logistics.tms.api.feign.driverpayee.hystrix.DriverPayeeServiceApiHystrix;
import com.logistics.tms.api.feign.driverpayee.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Api(value = "API-DriverPayeeServiceApi-收款人账户管理")
@FeignClient(name = "logistics-tms-services", fallback = DriverPayeeServiceApiHystrix.class)
public interface DriverPayeeServiceApi {

    @PostMapping(value = "/service/driverPayee/driverPayeeList")
    @ApiOperation(value = "司机收款人账户列表")
    Result<PageInfo<DriverPayeeListResponseModel>> driverPayeeList(@RequestBody DriverPayeeListRequestModel requestModel);

    @PostMapping(value = "/service/driverPayee/exportDriverPayeeList")
    @ApiOperation(value = "导出司机收款人账户列表")
    Result<List<ExportDriverPayeeListResponseModel>> exportDriverPayeeList(@RequestBody DriverPayeeListRequestModel requestModel);

    @PostMapping(value = "/service/driverPayee/addOrModify")
    @ApiOperation(value = "司机收款人账户新增/修改")
    Result<Boolean> addOrModifyDriverPayee(@RequestBody AddOrModifyDriverPayeeRequestModel requestModel);

    @PostMapping(value = "/service/driverPayee/detail")
    @ApiOperation(value = "司机收款人账户详情")
    Result<DriverPayeeDetailResponseModel> driverPayeeDetail(@RequestBody DriverPayeeDetailRequestModel requestModel);

    @PostMapping(value = "/service/driverPayee/auditOrReject")
    @ApiOperation(value = "司机收款人审核驳回作废")
    Result<Boolean> auditOrReject(@RequestBody AuditRejectDriverPayeeRequestModel requestModel);

    @ApiOperation(value = "导入")
    @PostMapping(value = "/service/driverPayee/import")
    Result<ImportDriverPayeeResponseModel> importDriverPayee(@RequestBody ImportDriverPayeeRequestModel importDriverPayeeRequestModel);

    @ApiOperation(value = "导入证件信息")
    @PostMapping(value = "/service/driverPayee/importCertificateInfo")
    Result<Boolean> importDriverPayeeCertificate(@RequestBody SrcUrlModel srcUrlModel);

    @ApiOperation(value = "查看日志")
    @PostMapping(value = "/service/driverPayee/viewLogs")
    Result<List<ViewLogResponseModel>> driverPayeeLogs(@RequestBody DriverPayeeDetailRequestModel requestModel);

    @PostMapping(value = "/service/driverPayee/searchDriverPayees")
    @ApiOperation(value = "查询已审核的收款账户信息")
    Result<List<SearchDriverPayeesResponseModel>> searchDriverPayees(@RequestBody SearchDriverPayeesRequestModel requestDto);
}
