package com.logistics.tms.api.feign.vehicletire.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ImportVehicleTireInfoListRequestModel {
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机姓名")
    private String driveName;
    @ApiModelProperty("司机联系方式")
    private String driveMobile;
    @ApiModelProperty("更换日期")
    private Date replaceDate;
    @ApiModelProperty("轮胎企业")
    private String tireCompany;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("轮胎列表")
    private List<ImportVehicleTireListRequestModel> vehicleTireList;
}
