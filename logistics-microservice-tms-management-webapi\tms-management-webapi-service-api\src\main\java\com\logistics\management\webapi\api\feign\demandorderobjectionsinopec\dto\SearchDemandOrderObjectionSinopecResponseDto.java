package com.logistics.management.webapi.api.feign.demandorderobjectionsinopec.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/5/30 13:15
 */
@Data
public class SearchDemandOrderObjectionSinopecResponseDto {
    @ApiModelProperty("需求单异常id")
    private String demandOrderObjectionId="";

    @ApiModelProperty("需求单id")
    private String demandId= "";
    @ApiModelProperty("需求单号")
    private String demandOrderCode= "";
    @ApiModelProperty("客户单号")
    private String customerOrderCode= "";
    @ApiModelProperty("调度人员")
    private String dispatcher="";
    @ApiModelProperty("货主")
    private String companyEntrustName= "";
    @ApiModelProperty("上报备注")
    private String remark= "";

    //审核信息
    @ApiModelProperty("审核状态：0 待审核，1 已审核，２ 已驳回")
    private String auditStatus= "";
    @ApiModelProperty("审核状态")
    private String auditStatusLabel= "";
    @ApiModelProperty("审核人")
    private String auditorName= "";
    @ApiModelProperty("审核时间")
    private String auditTime= "";
    @ApiModelProperty("审核依据-图片数量")
    private String auditTicketCount="";
    @ApiModelProperty("审核依据-图片路径")
    private List<String> auditTicketList;
    @ApiModelProperty("审核异常类型")
    private String auditObjectionTypeLabel= "";
    @ApiModelProperty("备注")
    private String auditRemark= "";
}
