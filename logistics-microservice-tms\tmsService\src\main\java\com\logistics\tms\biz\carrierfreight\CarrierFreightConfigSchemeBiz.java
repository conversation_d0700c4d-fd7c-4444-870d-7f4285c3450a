package com.logistics.tms.biz.carrierfreight;

import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.CarrierFreightConfigSchemeTypeEnum;
import com.logistics.tms.entity.TCarrierFreightConfig;
import com.logistics.tms.entity.TCarrierFreightConfigScheme;
import com.logistics.tms.mapper.TCarrierFreightConfigSchemeMapper;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Service
public class CarrierFreightConfigSchemeBiz {

    @Resource
    private CarrierFreightConfigBiz carrierFreightConfigBiz;
    @Resource
    private TCarrierFreightConfigSchemeMapper carrierFreightConfigSchemeMapper;

    /**
     * 根据配置Id 查询方案Id
     * @param freightConfigId 配置Id
     * @return 方案信息列表
     */
    public List<TCarrierFreightConfigScheme> getConfigScheme(Long freightConfigId) {
        return carrierFreightConfigSchemeMapper.selectConfigSchemeByFreightConfigId(freightConfigId);
    }

    /**
     * 根据方案配置Id查询
     * @param freightConfigSchemeId 方案配置Id
     * @return 方案信息
     */
    public Optional<TCarrierFreightConfigScheme> getConfigSchemeById(Long freightConfigSchemeId) {
        return Optional.ofNullable(carrierFreightConfigSchemeMapper.selectByPrimaryKey(freightConfigSchemeId));
    }

    /**
     * 新增方案配置
     *
     * @param freightConfigId 运价配置ID
     * @param schemeType 方案类型
     * @return true | false
     */
    public TCarrierFreightConfigScheme addConfigScheme(Long freightConfigId, Integer schemeType) {

        // 查询配置
        TCarrierFreightConfig freightConfig = carrierFreightConfigBiz.getFreightConfigById(freightConfigId)
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.FREIGHT_CONFIG_NOT_EXIST));

        CarrierFreightConfigSchemeTypeEnum schemeTypeEnum = CarrierFreightConfigSchemeTypeEnum.getEnumByKey(schemeType);

        // 方案与配置是否匹配
        if (!schemeTypeEnum.getConfigType().getKey().equals(freightConfig.getConfigType())) {
            throw new BizException(CarrierDataExceptionEnum.NOT_MATCH_CONFIG_TYPE);
        }

        // 查询方案是否存在
        List<TCarrierFreightConfigScheme> configScheme = this.getConfigScheme(freightConfigId);

        // 方案校验
        if (!CarrierFreightConfigSchemeTypeEnum.ROUTE_CONFIG.equals(schemeTypeEnum)) {
            if (ListUtils.isNotEmpty(configScheme)) {
                if (CarrierFreightConfigSchemeTypeEnum.REGION_CONFIG.getKey().equals(schemeType)) {
                    throw new BizException(CarrierDataExceptionEnum.CARRIER_FREIGHT_CONFIG_SCHEME_EXIST);
                }
                switch (schemeTypeEnum) {
                    // 区域计价配置校验
                    case REGION_CONFIG:
                    case REGION_SAME_CONFIG:
                    case REGION_DIFFERENT_CONFIG:
                        if (configScheme.stream()
                                .anyMatch(a -> CarrierFreightConfigSchemeTypeEnum.REGION_CONFIG.getKey().equals(a.getSchemeType())
                                        || a.getSchemeType().equals(schemeType))) {
                            throw new BizException(CarrierDataExceptionEnum.CARRIER_FREIGHT_CONFIG_SCHEME_EXIST);
                        }
                        break;

                    // 里程记录计价校验
                    case CALCULATIONS_DISTANCE_CONFIG:
                    case DISTANCE_CONFIG:
                        throw new BizException(CarrierDataExceptionEnum.CARRIER_FREIGHT_CONFIG_DISTANCE_EXIST);
                }
            }
        }

        // 新增
        TCarrierFreightConfigScheme entity = new TCarrierFreightConfigScheme();
        entity.setFreightConfigId(freightConfigId);
        entity.setSchemeType(schemeType);
        carrierFreightConfigSchemeMapper.insertGeneratedKey(entity);
        return entity;
    }

    /**
     * 编辑方案类型
     *
     * @param schemeType 方案类型
     */
    public void editSchemeType(Long schemeId, Integer schemeType) {
        TCarrierFreightConfigScheme scheme = new TCarrierFreightConfigScheme();
        scheme.setId(schemeId);
        scheme.setSchemeType(schemeType);
        carrierFreightConfigSchemeMapper.updateByPrimaryKeySelective(scheme);
    }

    /**
     * 移除不存在的 Id
     * @param freightConfigId 配置Id
     * @param schemeTypes 方案类型集合
     * @param existsIds 已存在的Id集合
     */
    @Transactional
    public void removeSchemeNotExistsIdByIds(Long freightConfigId, List<Integer> schemeTypes, List<Long> existsIds,String userName) {
        carrierFreightConfigSchemeMapper.deleteNotExistBySchemeTypeAndIds(freightConfigId,
                schemeTypes,
                existsIds,
                userName
        );
    }
}
