package com.logistics.tms.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author：wjf
 * @date：2021/4/12 13:12
 */
@Data
public class ReconciliationTireListResponseModel {
    @ApiModelProperty(value = "轮胎牌号")
    private String tireBrand;
    @ApiModelProperty(value = "数量")
    private Integer amount;
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;
    @ApiModelProperty("更换日期")
    private Date replaceDate;
}
