package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/1
 */
@Data
public class CarrierOrderSignUpForLeyiRequestDto {

	@ApiModelProperty(value = "运单ID", required = true)
	@NotBlank(message = "id不能为空")
	private String carrierOrderId;

	@ApiModelProperty(value = "实际货主费用(元)", required = true)
	@NotBlank(message = "请填写实际货主费用")
	@DecimalMin(value = "0.01", message = "实际货主费用要大于0元")
	@DecimalMax(value = "100000", message = "实际货主费用要小于十万元")
	private String actualEntrustFee;

	@ApiModelProperty(value = "实际车主运费(元)")
	private String signCarrierFreight;
}
