package com.logistics.management.webapi.controller.shippingorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/8/6 10:03
 */
@Data
public class SearchShippingOrderListRequestDto extends AbstractPageForm<SearchShippingOrderListRequestDto> {

    /**
     * 运输单号
     */
    private String shippingOrderCode;

    /**
     * 状态：0 待审核，1 已审核，2 已驳回
     */
    private String status;

    /**
     * 车主
     */
    private String companyCarrierName;

    /**
     * 创建时间-开始
     */
    private String createdTimeStart;
    /**
     * 创建时间-结束
     */
    private String createdTimeEnd;

    /**
     * 发货地
     */
    private String loadAddress;

    /**
     * 收货地
     */
    private String unloadAddress;

    /**
     * 调度单号
     */
    private String dispatchOrderCode;

    /**
     * 需求单号
     */
    private String demandOrderCode;

    /**
     * 运单号
     */
    private String carrierOrderCode;

}
