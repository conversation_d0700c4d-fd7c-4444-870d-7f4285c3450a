package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2023/5/12 10:12
 */
public enum RecycleTaskTypeEnum {

    DEFAULT(0, ""),
    ORDINARY(1, "日常回收"),
    URGENT(2, "加急或节假日回收"),
    ;

    private Integer key;
    private String value;

    RecycleTaskTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static RecycleTaskTypeEnum getEnum(Integer key) {
        for (RecycleTaskTypeEnum t : values()) {
            if (t.getKey() .equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
