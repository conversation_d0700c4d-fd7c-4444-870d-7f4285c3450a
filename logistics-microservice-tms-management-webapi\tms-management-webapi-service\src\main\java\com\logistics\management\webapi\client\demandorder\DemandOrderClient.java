package com.logistics.management.webapi.client.demandorder;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.demandorder.hystrix.DemandOrderClientHystrix;
import com.logistics.management.webapi.client.demandorder.request.*;
import com.logistics.management.webapi.client.demandorder.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/27 16:03
 */
@FeignClient(name = FeignClientName.TMS_SERVICES, fallback = DemandOrderClientHystrix.class)
public interface DemandOrderClient {

    /**
     * 委托发布
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "委托发布")
    @PostMapping(value = "/service/demandOrder/saveDemandOrder")
    Result<Boolean> saveDemandOrder(@RequestBody SaveDemandOrderRequestModel requestModel);

    /**
     * 获取需求单列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取需求单列表")
    @PostMapping(value = "/service/demandOrder/searchList")
    Result<PageInfo<DemandOrderResponseModel>> searchList(@RequestBody DemandOrderSearchRequestModel requestModel);

    /**
     * 获取需求单列表统计
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取需求单列表统计")
    @PostMapping(value = "/service/demandOrder/searchListStatistics")
    Result<SearchListStatisticsResponseModel> searchListStatistics(@RequestBody DemandOrderSearchRequestModel requestModel);

    /**
     * 取消需求单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "取消需求单")
    @PostMapping(value = "/service/demandOrder/cancelDemandOrder")
    Result<Boolean> cancelDemandOrder(@RequestBody DemandOrderCancelRequestModel requestModel);

    /**
     * 获取需求单详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取需求单详情")
    @PostMapping(value = "/service/demandOrder/getDetail")
    Result<DemandOrderDetailResponseModel> getDetail(@RequestBody DemandOrderDetailRequestModel requestModel);

    /**
     * 需求单日志
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "需求单日志")
    @PostMapping(value = "/service/demandOrder/getDemandOrderLogs")
    Result<List<GetDemandOrderLogsResponseModel>> getDemandOrderLogs(@RequestBody DemandOrderDetailRequestModel requestModel);

    /**
     * 导出需求单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出需求单")
    @PostMapping(value = "/service/demandOrder/exportDemandOrder")
    Result<List<DemandOrderResponseModel>> exportDemandOrder(@RequestBody DemandOrderSearchRequestModel requestModel);

    /**
     * 复制发布详情接口
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "复制发布详情接口")
    @PostMapping(value = "/service/demandOrder/getPublishDemandOrderDetail")
    Result<GetPublishDemandOrderDetailResponseModel> getPublishDemandOrderDetail(@RequestBody DemandOrderIdRequestModel requestModel);

    /**
     * 确认同步网络货运平台
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "确认同步网络货运平台")
    @PostMapping(value = "/service/demandOrder/confirmSynNetworkFreight")
    Result<Boolean> confirmSynNetworkFreight(@RequestBody DemandOrderIdListRequestModel requestModel);

    /**
     * 修改车主详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "需求单修改车主详情查询v1.1.9")
    @PostMapping(value = "/service/demandOrder/modifyCarrierDetail")
    Result<ModifyCarrierDetailResponseModel> modifyCarrierDetail(@RequestBody ModifyCarrierDetailRequestModel requestModel);


    /**
     * 修改车主
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "修改车主")
    @PostMapping(value = "/service/demandOrder/modifyCarrier")
    Result<Boolean> modifyCarrier(@RequestBody ModifyCarrierRequestModel requestModel);

}
