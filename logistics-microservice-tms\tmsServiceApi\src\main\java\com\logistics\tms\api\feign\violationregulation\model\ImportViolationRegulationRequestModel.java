package com.logistics.tms.api.feign.violationregulation.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/6/3 13:18
 */
@Data
public class ImportViolationRegulationRequestModel  {
    @ApiModelProperty("导入List")
    private List<ImportViolationRegulationListRequestModel> importList;
    @ApiModelProperty("失败数量")
    private Integer numberFailures = 0;
}
