package com.logistics.appapi.controller.driverappoint;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.client.driverappoint.DriverAppointClient;
import com.logistics.appapi.client.driverappoint.request.DriverAppointAssociatedVehicleRequestModel;
import com.logistics.appapi.client.driverappoint.request.SearchAppointRequestModel;
import com.logistics.appapi.client.driverappoint.request.SearchDrierAppointDetailRequestModel;
import com.logistics.appapi.client.driverappoint.response.SearchAppointCountResponseModel;
import com.logistics.appapi.client.driverappoint.response.SearchDrierAppointDetailResponseModel;
import com.logistics.appapi.controller.driverappoint.mapping.AppletAppointmentRecordMapping;
import com.logistics.appapi.controller.driverappoint.mapping.SearchDrierAppointListMappping;
import com.logistics.appapi.controller.driverappoint.request.DriverAppointAssociatedVehicleRequestDto;
import com.logistics.appapi.controller.driverappoint.request.SearchDrierAppointDetailRequestDto;
import com.logistics.appapi.controller.driverappoint.request.SearchDrierAppointListRequestDto;
import com.logistics.appapi.controller.driverappoint.response.SearchDrierAppointCountResponseDto;
import com.logistics.appapi.controller.driverappoint.response.SearchDrierAppointDetailResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @author: wjf
 * @date: 2024/3/11 14:18
 */
@Api(value = "司机预约记录", tags = "司机预约记录")
@RestController
@RequestMapping(value = "/api/driverApplet/driverAppoint")
public class DriverAppointController {

    @Resource
    private DriverAppointClient driverAppointClient;

    /**
     * 小程序-预约记录-列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "小程序-预约记录-列表 v1.0.3")
    @PostMapping(value = "/searchDrierAppointList")
    public Result<SearchDrierAppointCountResponseDto> searchDrierAppointList(@RequestBody @Valid SearchDrierAppointListRequestDto requestDto) {
        Result<SearchAppointCountResponseModel> searchAppointCountResponseModelResult = driverAppointClient.searchAppointList(MapperUtils.mapper(requestDto, SearchAppointRequestModel.class));
        searchAppointCountResponseModelResult.throwException();
        return Result.success(MapperUtils.mapper(searchAppointCountResponseModelResult.getData(),SearchDrierAppointCountResponseDto.class,new SearchDrierAppointListMappping()));
    }

    /**
     * 小程序-预约记录-详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "小程序-预约记录-详情 v1.0.3")
    @PostMapping(value = "/searchDrierAppointDetail")
    public Result<SearchDrierAppointDetailResponseDto> searchDrierAppointDetail(@RequestBody @Valid SearchDrierAppointDetailRequestDto requestDto) {
        Result<SearchDrierAppointDetailResponseModel> appletAppointmentRecordResponseModelResult = driverAppointClient.searchDrierAppointDetail(MapperUtils.mapper(requestDto, SearchDrierAppointDetailRequestModel.class));
        appletAppointmentRecordResponseModelResult.throwException();
        return Result.success(MapperUtils.mapper(appletAppointmentRecordResponseModelResult.getData(),SearchDrierAppointDetailResponseDto.class,new AppletAppointmentRecordMapping()));
    }

    /**
     * 小程序-预约记录关联车辆（生成运单）
     * @param requestDto
     * @return
     */
    @ApiOperation("小程序-预约记录关联车辆（生成运单）v1.0.3")
    @PostMapping({"/associatedVehicle"})
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> associatedVehicle(@RequestBody @Valid DriverAppointAssociatedVehicleRequestDto requestDto) {
        return driverAppointClient.associatedVehicle(MapperUtils.mapper(requestDto, DriverAppointAssociatedVehicleRequestModel.class));
    }
}
