package com.logistics.management.webapi.controller.freightconfig.request.address;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigAddressEnableRequestDto extends CarrierFreightConfigAddressRequestDto {

    @ApiModelProperty(value = "启用禁用; 1 启用 0 禁用", required = true)
    @NotBlank(message = "请选择启用或禁用状态")
    @Range(min = 0, max = 1, message = "请选择启用或禁用状态")
    private String enable;
}
