package com.logistics.appapi.controller.vehiclesettlement.mapping;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.client.vehiclesettlement.response.DriverReconciliationDetailResponseModel;
import com.logistics.appapi.controller.vehiclesettlement.response.DriverReconciliationDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.math.BigDecimal;

/**
 * @author:lei.zhu
 * @date:2021/5/14 13:36
 */
public class DriverReconciliationDetailMapping extends MapperMapping<DriverReconciliationDetailResponseModel, DriverReconciliationDetailResponseDto> {

    @Override
    public void configure() {
        DriverReconciliationDetailResponseDto destination = getDestination();
        DriverReconciliationDetailResponseModel source = getSource();

        destination.setCarrierFreight(fixSymbol(source.getCarrierFreight()));
        destination.setAdjustFee(fixSymbol(source.getAdjustFee()));
        destination.setClaimFee(fixSymbol(source.getClaimFee()));

        destination.setTireFee(fixDeductingSymbol(source.getTireFee()));
        destination.setOilFilledFee(fixDeductingSymbol(source.getOilFilledFee()));
        destination.setGpsFee(fixDeductingSymbol(source.getGpsFee()));
        destination.setParkingFee(fixDeductingSymbol(source.getParkingFee()));
        destination.setInsuranceFee(fixDeductingSymbol(source.getInsuranceFee()));
        destination.setLoanFee(fixDeductingSymbol(source.getLoanFee()));


    }
    public String fixSymbol(BigDecimal bigDecimal){
        if(bigDecimal==null || bigDecimal.compareTo(BigDecimal.ZERO)== CommonConstant.INTEGER_ZERO){
            return BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP).toPlainString();
        }
        if(bigDecimal.compareTo(BigDecimal.ZERO)>CommonConstant.INTEGER_ZERO){
            return CommonConstant.PLUS+bigDecimal.stripTrailingZeros().setScale(2,BigDecimal.ROUND_HALF_UP).toPlainString();
        }
        return bigDecimal.stripTrailingZeros().setScale(2,BigDecimal.ROUND_HALF_UP).toPlainString();
    }
    public String fixDeductingSymbol(BigDecimal bigDecimal){
        if(bigDecimal==null || bigDecimal.compareTo(BigDecimal.ZERO)== CommonConstant.INTEGER_ZERO){
            return BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP).toPlainString();
        }
        if(bigDecimal.compareTo(BigDecimal.ZERO)>CommonConstant.INTEGER_ZERO){
            return CommonConstant.MINUS+bigDecimal.stripTrailingZeros().setScale(2,BigDecimal.ROUND_HALF_UP).toPlainString();
        }
        return CommonConstant.PLUS+bigDecimal.stripTrailingZeros().setScale(2,BigDecimal.ROUND_HALF_UP).toPlainString();
    }

}
