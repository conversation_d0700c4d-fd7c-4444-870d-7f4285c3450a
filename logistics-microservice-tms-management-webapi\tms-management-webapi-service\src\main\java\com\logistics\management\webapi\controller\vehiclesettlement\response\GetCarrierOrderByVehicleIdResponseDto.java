package com.logistics.management.webapi.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/14 9:19
 */
@Data
public class GetCarrierOrderByVehicleIdResponseDto {
    @ApiModelProperty("运单id")
    private String carrierOrderId="";
    @ApiModelProperty("运单号")
    private String carrierOrderCode="";
    @ApiModelProperty("客户单号")
    private String customerOrderCode="";
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty("调度时间")
    private String dispatchTime="";
    @ApiModelProperty("数量（带单位）")
    private String amount="";
    @ApiModelProperty("运费")
    private String dispatchFreightFee="";
    @ApiModelProperty("发货地址")
    private String loadAddress;
    @ApiModelProperty("收货地址")
    private String unloadAddress;

    @ApiModelProperty("备注")
    private String remark="";

    @ApiModelProperty("司机姓名")
    private String driverName="";
    @ApiModelProperty("手机号")
    private String driverMobile="";

    //导出pdf用
    @ApiModelProperty("发货地址")
    private String loadAddressPdf="";
    @ApiModelProperty("卸货地址")
    private String unloadAddressPdf="";
    @ApiModelProperty("运单回单图片路径 列表")
    private List<String> ticketList;

    @ApiModelProperty("委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件")
    private String demandOrderSource="";
}
