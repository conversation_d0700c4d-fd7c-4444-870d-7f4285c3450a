<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderGoodsCodeMapper" >


    <select id="selectGoodsCodeByGoodsIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_carrier_order_goods_code
        where
        valid = 1
        and carrier_order_goods_id in (${ids})
    </select>

      <insert id="batchInsertSelective" parameterType="com.logistics.tms.entity.TCarrierOrderGoodsCode" >
        <foreach collection="list" item="item" separator=";">
            insert into t_carrier_order_goods_code
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    id,
                </if>
                <if test="item.carrierOrderGoodsId != null" >
                    carrier_order_goods_id,
                </if>
                <if test="item.yeloGoodCode != null" >
                    yelo_good_code,
                </if>
                <if test="item.loadAmount != null" >
                    load_amount,
                </if>
                <if test="item.unloadAmount != null" >
                    unload_amount,
                </if>
                <if test="item.signAmount != null" >
                    sign_amount,
                </if>
                <if test="item.unit != null" >
                    unit,
                </if>
                <if test="item.createdBy != null" >
                    created_by,
                </if>
                <if test="item.createdTime != null" >
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="item.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.carrierOrderGoodsId != null" >
                    #{item.carrierOrderGoodsId,jdbcType=BIGINT},
                </if>
                <if test="item.yeloGoodCode != null" >
                    #{item.yeloGoodCode,jdbcType=VARCHAR},
                </if>
                <if test="item.loadAmount != null" >
                    #{item.loadAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.unloadAmount != null" >
                    #{item.unloadAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.signAmount != null" >
                    #{item.signAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.unit != null" >
                    #{item.unit,jdbcType=INTEGER},
                </if>
                <if test="item.createdBy != null" >
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>


    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            update t_carrier_order_goods_code
            <set>
                <if test="item.carrierOrderGoodsId != null">
                    carrier_order_goods_id = #{item.carrierOrderGoodsId,jdbcType=BIGINT},
                </if>
                <if test="item.yeloGoodCode != null">
                    yelo_good_code = #{item.yeloGoodCode,jdbcType=VARCHAR},
                </if>
                <if test="item.loadAmount != null">
                    load_amount = #{item.loadAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.unloadAmount != null">
                    unload_amount = #{item.unloadAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.signAmount != null">
                    sign_amount = #{item.signAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.unit != null">
                    unit = #{item.unit,jdbcType=INTEGER},
                </if>

                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>


</mapper>