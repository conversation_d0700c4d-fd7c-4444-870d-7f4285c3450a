package com.logistics.management.webapi.api.impl.driversafemeeting.mapping;

import com.logistics.management.webapi.api.feign.driversafemeeting.dto.DriverSafeMeetingContentDetailResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.tms.api.feign.driversafemeeting.model.DriverSafeMeetingContentDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/11/6 15:49
 */
public class DriverSafeMeetingContentDetailMapping extends MapperMapping<DriverSafeMeetingContentDetailResponseModel,DriverSafeMeetingContentDetailResponseDto> {
    @Override
    public void configure() {
        DriverSafeMeetingContentDetailResponseModel source = getSource();
        DriverSafeMeetingContentDetailResponseDto destination = getDestination();
        if (source != null){
            if (ListUtils.isNotEmpty(source.getDriverIdList())) {
                destination.setDriverCount(ConverterUtils.toString(source.getDriverIdList().size()));
            }
            if (source.getPeriod().equals(DateUtils.dateToString(new Date(), CommonConstant.DATE_TO_STRING_YM_PATTERN))){
                destination.setIfReissue(CommonConstant.ONE);
            }
        }
    }
}
