package com.logistics.management.webapi.controller.companyentrust.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.SignModeEnum;
import com.logistics.management.webapi.base.enums.UpdateVehicleAuditEnum;
import com.logistics.management.webapi.client.companyentrust.response.CompanyEntrustDetailResponseModel;
import com.logistics.management.webapi.controller.companyentrust.response.CompanyEntrustDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/9/28 13:28
 */
public class CompanyEntrustDetailMapping extends MapperMapping<CompanyEntrustDetailResponseModel, CompanyEntrustDetailResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;

    public CompanyEntrustDetailMapping(String imagePrefix, Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap = imageMap;
    }

    @Override
    public void configure() {
        CompanyEntrustDetailResponseModel model = getSource();
        CompanyEntrustDetailResponseDto dto = getDestination();
        if (model != null) {
            if (StringUtils.isNotBlank(model.getTradingCertificateImage())) {
                dto.setFileSrcPathTradingCertificateImage(model.getTradingCertificateImage());
                dto.setFileTargetPathTradingCertificateImage(imagePrefix + imageMap.get(model.getTradingCertificateImage()));
            }
            if (model.getTradingCertificateValidityTime() != null) {
                dto.setTradingCertificateValidityTime(DateUtils.dateToString(model.getTradingCertificateValidityTime(), CommonConstant.DATE_TO_STRING_YMD_PATTERN));
            }
            if (model.getIfAudit() != null) {
                dto.setIfAuditDesc(UpdateVehicleAuditEnum.getEnum(model.getIfAudit()).getValue());
            }
            if (SignModeEnum.MANUALLY_SIGN.getKey().equals(model.getSignMode())) {
                dto.setSignModeDesc(SignModeEnum.MANUALLY_SIGN.getDesc());
            } else if (SignModeEnum.AUTO_SIGN.getKey().equals(model.getSignMode())) {
                dto.setSignModeDesc(String.format(SignModeEnum.AUTO_SIGN.getDesc(), model.getSignDays()));
            }
            dto.setTypeLabel(CompanyTypeEnum.getEnum(model.getType()).getValue());
        }
    }
}
