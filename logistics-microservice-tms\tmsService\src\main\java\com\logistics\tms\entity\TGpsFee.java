package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TGpsFee extends BaseEntity {
    /**
    * 结算状态：0 待结算，1 部分结算，2 结算完成
    */
    @ApiModelProperty("结算状态：0 待结算，1 部分结算，2 结算完成")
    private Integer status;

    /**
    * 车辆id
    */
    @ApiModelProperty("车辆id")
    private Long vehicleId;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 车辆机构：1 自主，2 外部，3 自营
    */
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;

    /**
    * 司机id
    */
    @ApiModelProperty("司机id")
    private Long staffId;

    /**
    * 司机姓名
    */
    @ApiModelProperty("司机姓名")
    private String name;

    /**
    * 手机号
    */
    @ApiModelProperty("手机号")
    private String mobile;

    /**
    * gps终端型号
    */
    @ApiModelProperty("gps终端型号")
    private String terminalType;

    /**
    * gps服务商
    */
    @ApiModelProperty("gps服务商")
    private String gpsServiceProvider;

    /**
    * SIM卡号
    */
    @ApiModelProperty("SIM卡号")
    private String simNumber;

    /**
    * 安装日期
    */
    @ApiModelProperty("安装日期")
    private Date installTime;

    /**
    * 起始日期
    */
    @ApiModelProperty("起始日期")
    private Date startDate;

    /**
    * 截止时间
    */
    @ApiModelProperty("截止时间")
    private Date endDate;

    /**
    * 终止时间
    */
    @ApiModelProperty("终止时间")
    private Date finishDate;

    /**
    * 服务费
    */
    @ApiModelProperty("服务费")
    private BigDecimal serviceFee;

    /**
    * 合作周期（月）
    */
    @ApiModelProperty("合作周期（月）")
    private Integer cooperationPeriod;

    /**
    * 合作状态：1 已预付，2 进行中，3 已终止
    */
    @ApiModelProperty("合作状态：1 已预付，2 进行中，3 已终止")
    private Integer cooperationStatus;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}