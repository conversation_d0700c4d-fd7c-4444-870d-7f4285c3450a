package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TDateRemind extends BaseEntity {
    /**
    * 日期名称
    */
    @ApiModelProperty("日期名称")
    private String dateName;

    /**
    * 是否提醒：0 否，1 是
    */
    @ApiModelProperty("是否提醒：0 否，1 是")
    private Integer ifRemind;

    /**
    * 提醒天数
    */
    @ApiModelProperty("提醒天数")
    private Integer remindDays;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 添加人id
    */
    @ApiModelProperty("添加人id")
    private Long addUserId;

    /**
    * 添加人名字
    */
    @ApiModelProperty("添加人名字")
    private String addUserName;

    /**
    * 车辆添加来源：1新增 2导入
    */
    @ApiModelProperty("车辆添加来源：1新增 2导入")
    private Integer source;
}