<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TReservationOrderMapper" >
  <select id="waitSignInSummaryList" resultType="com.logistics.tms.controller.reservationorder.response.ReservationOrderSummaryListResponseModel">
    select
      tro.id as reservationOrderId,
      tro.reservation_order_code as reservationOrderCode,
      tro.reservation_type as reservationType,
      tro.vehicle_no as vehicleNo,
      tro.province_name as provinceName,
      tro.city_name as cityName,
      tro.area_name as areaName,
      tro.detail_address as detailAddress,
      tro.warehouse,
      tro.reservation_start_time as reservationStartTime,
      tro.reservation_end_time as reservationEndTime,

      sum(troi.expect_amount) as expectAmount
    from t_reservation_order tro
    left join t_reservation_order_item troi on tro.id = troi.reservation_order_id and troi.valid = 1 and troi.enabled = 1
    where tro.valid = 1
    and (
    <foreach collection="driverModel" item="item" open="(" close=")" separator="or">
       tro.driver_id = #{item.driverId,jdbcType=BIGINT}
    </foreach>
    )
    and tro.status = 10
    group by tro.id
    order by tro.reservation_start_time
  </select>

  <select id="getEnabledOrder" resultMap="BaseResultMap">
    select
    tro.*
    from t_reservation_order tro
    left join t_reservation_order_item troi on tro.id = troi.reservation_order_id and troi.valid = 1
    where tro.valid = 1
    and tro.id in
    <foreach collection="reservationOrderIdList" item="reservationOrderId" open="(" close=")" separator=",">
      #{reservationOrderId,jdbcType=BIGINT}
    </foreach>
    and troi.enabled = 1
  </select>

  <select id="getOrderByIds" resultMap="BaseResultMap">
    select
    tro.*
    from t_reservation_order tro
    where tro.valid = 1
    and tro.id in
    <foreach collection="reservationOrderIdList" item="reservationOrderId" open="(" close=")" separator=",">
      #{reservationOrderId,jdbcType=BIGINT}
    </foreach>
  </select>

  <update id="batchUpdateSelective" parameterType="com.logistics.tms.entity.TReservationOrder" >
    <foreach collection="recordList" item="item" separator=";">
      update t_reservation_order
      <set >
        <if test="item.reservationOrderCode != null" >
          reservation_order_code = #{item.reservationOrderCode,jdbcType=VARCHAR},
        </if>
        <if test="item.status != null" >
          status = #{item.status,jdbcType=INTEGER},
        </if>
        <if test="item.reservationType != null" >
          reservation_type = #{item.reservationType,jdbcType=INTEGER},
        </if>
        <if test="item.driverId != null" >
          driver_id = #{item.driverId,jdbcType=BIGINT},
        </if>
        <if test="item.driverName != null" >
          driver_name = #{item.driverName,jdbcType=VARCHAR},
        </if>
        <if test="item.driverMobile != null" >
          driver_mobile = #{item.driverMobile,jdbcType=VARCHAR},
        </if>
        <if test="item.driverIdentity != null" >
          driver_identity = #{item.driverIdentity,jdbcType=VARCHAR},
        </if>
        <if test="item.vehicleId != null" >
          vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
        </if>
        <if test="item.vehicleNo != null" >
          vehicle_no = #{item.vehicleNo,jdbcType=VARCHAR},
        </if>
        <if test="item.provinceId != null" >
          province_id = #{item.provinceId,jdbcType=BIGINT},
        </if>
        <if test="item.provinceName != null" >
          province_name = #{item.provinceName,jdbcType=VARCHAR},
        </if>
        <if test="item.cityId != null" >
          city_id = #{item.cityId,jdbcType=BIGINT},
        </if>
        <if test="item.cityName != null" >
          city_name = #{item.cityName,jdbcType=VARCHAR},
        </if>
        <if test="item.areaId != null" >
          area_id = #{item.areaId,jdbcType=BIGINT},
        </if>
        <if test="item.areaName != null" >
          area_name = #{item.areaName,jdbcType=VARCHAR},
        </if>
        <if test="item.detailAddress != null" >
          detail_address = #{item.detailAddress,jdbcType=VARCHAR},
        </if>
        <if test="item.warehouse != null" >
          warehouse = #{item.warehouse,jdbcType=VARCHAR},
        </if>
        <if test="item.longitude != null">
          longitude = #{item.longitude,jdbcType=VARCHAR},
        </if>
        <if test="item.latitude != null">
          latitude = #{item.latitude,jdbcType=VARCHAR},
        </if>
        <if test="item.reservationStartTime != null" >
          reservation_start_time = #{item.reservationStartTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.reservationEndTime != null" >
          reservation_end_time = #{item.reservationEndTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.signDate != null" >
          sign_date = #{item.signDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="getWaitSignInAndReservationTimeInvalid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_reservation_order
    where valid = 1
    and status = 10
    and reservation_end_time &lt;= now()
  </select>

    <select id="searchListForDriverWeb" resultType="com.logistics.tms.controller.reservationorder.response.ReservationOrderSearchListResponseModel">
        select
        troi.id,
        troi.carrier_order_code carrierOrderCode,
        troi.expect_amount reservationCount,
<!--        tro.driver_name carrierName,-->
        tro.reservation_order_code reservationCode,
        tro.reservation_type reservationType,
        CASE
        WHEN troi.enabled = 0  THEN 30
        ELSE tro.status
        END AS state,
        tro.vehicle_no vehicleNo,
        tro.warehouse warehouseName,
        concat(tro.province_name,tro.city_name,tro.area_name,tro.detail_address) warehouseAddress,
        tro.reservation_source reservationSource,
        tro.reservation_role reservationRole,
        tro.created_by driver,
        tro.driver_identity identityCardNumber,
        tro.reservation_start_time  reservationStartTime,
        tro.reservation_end_time  reservationEndTime,
        tro.sign_date signInTime

        from t_reservation_order tro
        join t_reservation_order_item troi on tro.id = troi.reservation_order_id and troi.valid = 1
        left join t_carrier_order tco on tco.id = troi.carrier_order_id and tco.valid = 1
        where tro.valid = 1
        and tco.company_carrier_id = #{param1.carrierCompanyId,jdbcType=BIGINT}
        <if test="param1.reservationCode != null and param1.reservationCode != ''">
            and instr(tro.reservation_order_code,#{param1.reservationCode,jdbcType=VARCHAR})>0
        </if>
        <if test="param1.state != null">
            <choose>
                <when test="param1.state == 10">
                    and tro.status = 10 and troi.enabled = 1
                </when>
                <when test="param1.state == 20">
                    and tro.status = 20 and troi.enabled = 1
                </when>
                <when test="param1.state == 30">
                    and (tro.status = 30 or troi.enabled = 0)
                </when>
                <otherwise>
                    and tro.status = #{param1.state,jdbcType=INTEGER}
                </otherwise>
            </choose>
        </if>
<!--        <if test="param1.carrierName != null and param1.carrierName != ''">-->
<!--            and instr(tro.driver_name,#{param1.carrierName,jdbcType=VARCHAR})>0-->
<!--        </if>-->

        <if test="param1.vehicleNo != null and param1.vehicleNo != ''">
            and instr(tro.vehicle_no,#{param1.vehicleNo,jdbcType=VARCHAR})>0
        </if>
        <if test="param1.reservationSource != null">
            and instr(tro.reservation_source,#{param1.reservationSource,jdbcType=VARCHAR})>0
        </if>
        <if test="param1.reservationRole != null">
            and tro.reservation_role = #{param1.reservationRole,jdbcType=INTEGER}
        </if>
        <if test="param1.warehouseName != null and param1.warehouseName != ''">
            and instr(tro.warehouse,#{param1.warehouseName,jdbcType=VARCHAR})>0
        </if>

        <if test="param1.reservationTimeStart != null and param1.reservationTimeStart != ''">
            and tro.reservation_start_time &gt;= DATE_FORMAT(#{param1.reservationTimeStart,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00')
        </if>

        <if test="param1.reservationTimeEnd != null and param1.reservationTimeEnd != ''">
            and tro.reservation_end_time &lt;= DATE_FORMAT(#{param1.reservationTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="param1.signInTimeStart != null and param1.signInTimeStart != ''">
            and tro.sign_date &gt;= DATE_FORMAT(#{param1.signInTimeStart,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00')
        </if>
        <if test="param1.signInTimeEnd != null and param1.signInTimeEnd != ''">
            and tro.sign_date &lt;= DATE_FORMAT(#{param1.signInTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="param1.carrierOrderCode != null and param1.carrierOrderCode != ''">
            and instr(troi.carrier_order_code,#{param1.carrierOrderCode,jdbcType=VARCHAR})>0
        </if>
        ORDER BY CASE WHEN tro.sign_date is null THEN -1 else 0 END desc ,tro.sign_date desc,tro.reservation_start_time desc
    </select>

    <select id="searchListForManagementWeb" resultType="com.logistics.tms.controller.reservationorder.response.ReservationOrderSearchListForManagementWebResModel">
        select
        troi.id,
        troi.carrier_order_code carrierOrderCode,
        troi.expect_amount reservationCount,
        tco.company_carrier_name companyCarrierName,
        tco.company_carrier_type companyCarrierType,
        tco.carrier_contact_name carrierContactName,
        AES_DECRYPT(UNHEX(tco.carrier_contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactMobile,
        tro.reservation_order_code reservationCode,
        tro.reservation_type reservationType,
        CASE
        WHEN troi.enabled = 0  THEN 30
        ELSE tro.status
        END AS state,
        tro.vehicle_no vehicleNo,
        tro.warehouse warehouseName,
        concat(tro.province_name,tro.city_name,tro.area_name,tro.detail_address) warehouseAddress,
        tro.reservation_source reservationSource,
        tro.reservation_role reservationRole,
        tro.created_by driver,
        tro.driver_identity identityCardNumber,
        tro.reservation_start_time  reservationStartTime,
        tro.reservation_end_time  reservationEndTime,
        tro.sign_date signInTime,
        tro.created_time createdTime
        from t_reservation_order tro
        left join t_reservation_order_item troi on tro.id = troi.reservation_order_id and troi.valid = 1
        left join t_carrier_order tco on troi.carrier_order_id = tco.id and tco.valid = 1
        where tro.valid = 1
        <if test="param1.carrierName != null and param1.carrierName != ''">
            and (
            (tco.company_carrier_type = 1 and instr(tco.company_carrier_name, #{param1.carrierName,jdbcType=VARCHAR}))
            or (tco.company_carrier_type = 2 and (instr(tco.carrier_contact_name, #{param1.carrierName,jdbcType=VARCHAR})
            or instr(AES_DECRYPT(UNHEX(tco.carrier_contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'), #{param1.carrierName,jdbcType=VARCHAR})))
            )
        </if>
        <if test="param1.carrierOrderCode != null and param1.carrierOrderCode != ''">
            and instr(tco.carrier_order_code,#{param1.carrierOrderCode,jdbcType=VARCHAR})>0
        </if>
        <if test="param1.reservationCode != null and param1.reservationCode != ''">
            and instr(tro.reservation_order_code,#{param1.reservationCode,jdbcType=VARCHAR})>0
        </if>
        <if test="param1.state != null">
            <choose>
                <when test="param1.state == 10">
                    and tro.status = 10 and troi.enabled = 1
                </when>
                <when test="param1.state == 20">
                    and tro.status = 20 and troi.enabled = 1
                </when>
                <when test="param1.state == 30">
                    and (tro.status = 30 or troi.enabled = 0)
                </when>
                <otherwise>
                    and tro.status = #{param1.state,jdbcType=INTEGER}
                </otherwise>
            </choose>
        </if>
        <if test="param1.vehicleNo != null and param1.vehicleNo != ''">
            and instr(tro.vehicle_no,#{param1.vehicleNo,jdbcType=VARCHAR})>0
        </if>
        <if test="param1.reservationSource != null">
            and instr(tro.reservation_source,#{param1.reservationSource,jdbcType=VARCHAR})>0
        </if>
        <if test="param1.reservationRole != null">
            and tro.reservation_role = #{param1.reservationRole,jdbcType=INTEGER}
        </if>
        <if test="param1.warehouseName != null and param1.warehouseName != ''">
            and instr(tro.warehouse,#{param1.warehouseName,jdbcType=VARCHAR})>0
        </if>

        <if test="param1.reservationTimeStart != null and param1.reservationTimeStart != ''">
            and tro.reservation_start_time &gt;= DATE_FORMAT(#{param1.reservationTimeStart,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00')
        </if>

        <if test="param1.reservationTimeEnd != null and param1.reservationTimeEnd != ''">
            and tro.reservation_end_time &lt;= DATE_FORMAT(#{param1.reservationTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="param1.signInTimeStart != null and param1.signInTimeStart != ''">
            and tro.sign_date &gt;= DATE_FORMAT(#{param1.signInTimeStart,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00')
        </if>
        <if test="param1.signInTimeEnd != null and param1.signInTimeEnd != ''">
            and tro.sign_date &lt;= DATE_FORMAT(#{param1.signInTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        ORDER BY CASE WHEN tro.sign_date is null THEN -1 else 0 END desc ,tro.sign_date desc,tro.reservation_start_time desc

    </select>


</mapper>