package com.logistics.management.webapi.api.feign.demandorderobjection.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.demandorderobjection.DemandOrderObjectionApi;
import com.logistics.management.webapi.api.feign.demandorderobjection.dto.SearchDemandOrderObjectionRequestDto;
import com.logistics.management.webapi.api.feign.demandorderobjection.dto.SearchDemandOrderObjectionResponseDto;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: wjf
 * @date: 2021/10/18 11:19
 */
@Component
public class DemandOrderObjectionApiHystrix implements DemandOrderObjectionApi {
    @Override
    public Result<PageInfo<SearchDemandOrderObjectionResponseDto>> searchDemandOrderObjection(SearchDemandOrderObjectionRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void exportDemandOrderObjection(SearchDemandOrderObjectionRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }
}
