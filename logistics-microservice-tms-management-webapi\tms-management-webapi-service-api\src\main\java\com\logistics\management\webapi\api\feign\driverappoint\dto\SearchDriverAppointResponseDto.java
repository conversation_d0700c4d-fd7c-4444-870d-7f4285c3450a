package com.logistics.management.webapi.api.feign.driverappoint.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 驾驶员预约列表
 *
 * <AUTHOR>
 * @date 2022/8/17 10:14
 */
@ApiModel("查询驾驶员预约列表信息")
@Data
public class SearchDriverAppointResponseDto {

    @ApiModelProperty("预约记录id")
    private String driverAppointId = "";
    @ApiModelProperty("司机")
    private String driver = "";
    private String exportDriver = "";
    @ApiModelProperty("司机机构：1 自主，2 外部，3 自营")
    private String staffProperty = "";
    private String staffPropertyLabel = "";
    @ApiModelProperty("需求单ID")
    private String demandOrderId = "";
    @ApiModelProperty("需求单号")
    private String demandOrderCode = "";
    @ApiModelProperty("车牌号")
    private String vehicleNo = "";
    @ApiModelProperty("业务类型：1 公司，2 个人")
    private String businessType = "";
    private String businessLabel = "";
    @ApiModelProperty("发货人")
    private String consignorName = "";
    private String exportConsignor= "";
    @ApiModelProperty("发货地址")
    private String loadAddress = "";//拼接后的数据，列表展示
    @ApiModelProperty("下单总数量(KG)")
    private String goodsAmountTotal = "";
    @ApiModelProperty("合计")
    private String goodsPriceTotal = "";
    @ApiModelProperty("下单时间")
    private String publishTime = "";

    @ApiModelProperty("乐橘新生客户名称")
    private String customerName;
    @ApiModelProperty("乐橘新生客户名称(导出使用)")
    private String exportCustomer;

}
