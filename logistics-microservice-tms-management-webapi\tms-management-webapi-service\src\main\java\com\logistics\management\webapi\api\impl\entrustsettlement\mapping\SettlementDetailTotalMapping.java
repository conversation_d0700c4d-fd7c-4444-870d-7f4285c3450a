package com.logistics.management.webapi.api.impl.entrustsettlement.mapping;

import com.logistics.management.webapi.api.feign.entrustsettlement.dto.GetSettlementDetailResponseDto;
import com.logistics.tms.api.feign.entrustsettlement.model.GetSettlementDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2019/10/11 19:52
 */
public class SettlementDetailTotalMapping extends MapperMapping<GetSettlementDetailResponseModel,GetSettlementDetailResponseDto> {
    @Override
    public void configure() {
        GetSettlementDetailResponseModel source = getSource();
        GetSettlementDetailResponseDto destination = getDestination();
        if (source != null){
            destination.setTotalPackageSettlementAmount(source.getTotalPackageSettlementAmount().stripTrailingZeros().toPlainString());
            destination.setTotalWeightSettlementAmount(source.getTotalWeightSettlementAmount().stripTrailingZeros().toPlainString());
        }
    }
}
