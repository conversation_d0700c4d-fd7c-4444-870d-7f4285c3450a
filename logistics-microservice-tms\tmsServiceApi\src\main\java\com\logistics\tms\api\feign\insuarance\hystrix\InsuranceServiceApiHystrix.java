package com.logistics.tms.api.feign.insuarance.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.insuarance.InsuranceServiceApi;
import com.logistics.tms.api.feign.insuarance.model.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/6/4 19:14
 */
@Component("tmsInsuranceServiceApiHystrix")
public class InsuranceServiceApiHystrix implements InsuranceServiceApi {

    @Override
    public Result<PageInfo<SearchInsuranceListResponseModel>> searchInsuranceList(SearchInsuranceListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetInsuranceDetailResponseModel> getInsuranceDetail(InsuranceIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result addOrModifyInsurance(AddOrModifyInsuranceRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result cancelInsurance(CancelInsuranceRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchInsuranceListResponseModel>> exportInsurance(SearchInsuranceListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ImportInsuranceResponseModel> importInsurance(ImportInsuranceRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result importInsuranceCertificateInfo(ImportInsuranceCertificateRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetInsuranceInfoByVehicleIdResponseModel> getInsuranceInfoByVehicleId(GetInsuranceInfoByVehicleIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result confirmRefund(ConfirmRefundRequestModel requestModel) {
        return Result.timeout();
    }
}
