package com.logistics.management.webapi.client.reserveapply;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.reserveapply.hystrix.DriverReserveApplyClientHystrix;
import com.logistics.management.webapi.client.reserveapply.request.*;
import com.logistics.management.webapi.client.reserveapply.response.ReserveApplyDetailResponseModel;
import com.logistics.management.webapi.client.reserveapply.response.ReserveApplyListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/DriverReserveApply",
        fallback = DriverReserveApplyClientHystrix.class)
public interface DriverReserveApplyClient {

    @ApiOperation(value = "备用金申请列表查询")
    @PostMapping("/reserveApplyList")
    Result<PageInfo<ReserveApplyListResponseModel>> reserveApplyList(@RequestBody ReserveApplyListRequestModel requestModel);

    @ApiOperation(value = "备用金申请列表导出查询")
    @PostMapping("/reserveApplyListExport")
    Result<List<ReserveApplyListResponseModel>> reserveApplyListExport(@RequestBody ReserveApplyListRequestModel requestModel);

    @ApiOperation(value = "备用金申请详情查询")
    @PostMapping("/reserveApplyDetail")
    Result<ReserveApplyDetailResponseModel> reserveApplyDetail(@RequestBody ReserveApplyDetailRequestModel requestModel);

    @ApiOperation(value = "备用金申请撤销")
    @PostMapping("/reserveApplyCancel")
    Result<Boolean> reserveApplyCancel(@RequestBody ReserveApplyCancelRequestModel requestModel);

    @ApiOperation(value = "备用金申请审核")
    @PostMapping("/reserveApplyAudit")
    Result<Boolean> reserveApplyAudit(@RequestBody ReserveApplyAuditRequestModel requestModel);

    @ApiOperation(value = "备用金打款")
    @PostMapping("/driverReserveRemit")
    Result<Boolean> driverReserveRemit(@RequestBody ReserveApplyRemitRequestModel requestModel);
}
