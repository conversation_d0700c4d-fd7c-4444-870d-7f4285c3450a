package com.logistics.tms.api.feign.freight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class GetPriceByAddressAndAmountRequestModel {

    @ApiModelProperty("委托方公司ID")
    private Long companyEntrustId;
    @ApiModelProperty("发货省ID")
    private Long fromProvinceId;
    @ApiModelProperty("发货市ID")
    private Long fromCityId;
    @ApiModelProperty("发货区ID")
    private Long fromAreaId;
    @ApiModelProperty("发货仓库")
    private String fromWarehouse;
    @ApiModelProperty("收货省ID")
    private Long toProvinceId;
    @ApiModelProperty("收货市ID")
    private Long toCityId;
    @ApiModelProperty("收货区ID")
    private Long toAreaId;
    @ApiModelProperty("收货仓库详细地址")
    private String toWarehouseDetail;
    @ApiModelProperty("货物数量")
    private BigDecimal goodsAmount;
    @ApiModelProperty("货物单位 1件 2吨")
    private Integer goodsUnit;
    @ApiModelProperty("费用类型 1 单价 2 一口价")
    private Integer freightType;

    @Override
    public String toString() {
        return "GetPriceByAddressAndAmountRequestModel{" +
                "companyEntrustId=" + companyEntrustId +
                ", fromProvinceId=" + fromProvinceId +
                ", fromCityId=" + fromCityId +
                ", fromAreaId=" + fromAreaId +
                ", fromWarehouse='" + fromWarehouse + '\'' +
                ", toProvinceId=" + toProvinceId +
                ", toCityId=" + toCityId +
                ", toAreaId=" + toAreaId +
                ", toWarehouseDetail='" + toWarehouseDetail + '\'' +
                ", goodsAmount=" + goodsAmount +
                ", goodsUnit=" + goodsUnit +
                ", freightType=" + freightType +
                '}';
    }
}
