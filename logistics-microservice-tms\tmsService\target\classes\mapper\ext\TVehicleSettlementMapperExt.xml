<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleSettlementMapper">
    <select id="getByVehicleIdAndMonth" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_vehicle_settlement
        where valid = 1
        and vehicle_id = #{vehicleId,jdbcType = BIGINT}
        and settlement_month = #{settlementMonth,jdbcType = VARCHAR}
    </select>

    <select id="searchVehicleSettlementList" resultType="com.logistics.tms.controller.vehiclesettlement.response.SearchVehicleSettlementListResponseModel">
        select
        tvs.id as vehicleSettlementId,
        tvs.status,
        tvs.vehicle_id as vehicleId,
        tvs.vehicle_no as vehicleNo,
        tvs.vehicle_property as vehicleProperty,
        tvs.settlement_month as settlementMonth,
        tvs.actual_expenses_payable as actualExpensesPayable,
        tvs.carrier_order_count as carrierOrderCount,
        tvs.carrier_freight as carrierFreight,
        tvs.adjust_fee as adjustFee,
        tvs.deducting_fee as deductingFeeTotal,
        tvs.remark,
        tvs.withdraw_remark as withdrawRemark,
        tvs.last_modified_by as lastModifiedBy,
        tvs.last_modified_time as lastModifiedTime,

        tvsdr.driver_name as drivername
        from t_vehicle_settlement tvs
        left join t_vehicle_settlement_driver_relation tvsdr on tvsdr.vehicle_settlement_id=tvs.id and tvsdr.valid=1
        where tvs.valid = 1
        <if test="params.status != null">
            and tvs.status = #{params.status,jdbcType=INTEGER}
        </if>
        <if test="params.vehicleNo != null and params.vehicleNo != ''">
            and instr(tvs.vehicle_no,#{params.vehicleNo,jdbcType=VARCHAR})
        </if>
        <if test="params.vehicleProperty != null">
            and tvs.vehicle_property = #{params.vehicleProperty,jdbcType=INTEGER}
        </if>
        <if test="params.driverName!=null and params.driverName!=''">
            and (
              instr(tvsdr.driver_name,#{params.driverName,jdbcType=VARCHAR})
              or instr(tvsdr.driver_mobile,#{params.driverName,jdbcType=VARCHAR})
            )
        </if>
        <if test="params.settlementMonth != null and params.settlementMonth != ''">
            and settlement_month = #{params.settlementMonth,jdbcType=VARCHAR}
        </if>
        <if test="params.settlementMonthStart != null and params.settlementMonthStart != ''">
            and tvs.settlement_month >= #{params.settlementMonthStart,jdbcType=VARCHAR}
        </if>
        <if test="params.settlementMonthEnd != null and params.settlementMonthEnd != ''">
            and tvs.settlement_month &lt;= #{params.settlementMonthEnd,jdbcType=VARCHAR}
        </if>

        <if test="params.vehicleSettlementIds != null and params.vehicleSettlementIds != ''">
            and tvs.id in (${params.vehicleSettlementIds})
        </if>
        order by tvs.last_modified_time desc,tvs.id desc
    </select>

    <select id="sendDriverSettleStatementList" resultType="com.logistics.tms.controller.vehiclesettlement.response.SendDriverSettleStatementListResponseModel">
        select
        tvs.id as vehicleSettlementId,
        tvs.vehicle_id as vehicleId,
        tvs.vehicle_no as vehicleNo,
        tvs.settlement_month as settlementMonth,
        tvs.actual_expenses_payable as actualExpensesPayable,
        tvs.carrier_order_count as carrierOrderCount
        from t_vehicle_settlement tvs
        where tvs.valid = 1
        and tvs.status = 1
        and tvs.id in (${vehicleSettlementIds})
        order by tvs.last_modified_time desc,tvs.id desc
    </select>

    <select id="searchVehicleSettlementListCount" resultType="com.logistics.tms.controller.vehiclesettlement.response.SearchVehicleSettlementListCountResponseModel">
        select
        ifnull(count(0),0) as allCount,
        ifnull(sum(if(status = 0,1,0)),0) as waitSettlementCount,
        ifnull(sum(if(status = 1,1,0)),0) as completeSettlementCount
        from t_vehicle_settlement
        where valid = 1
        <if test="params.vehicleNo != null and params.vehicleNo != ''">
            and instr(vehicle_no,#{params.vehicleNo,jdbcType=VARCHAR})
        </if>
        <if test="params.vehicleProperty != null">
            and vehicle_property = #{params.vehicleProperty,jdbcType=INTEGER}
        </if>
        <if test="params.settlementMonth != null and params.settlementMonth != ''">
            and settlement_month = #{params.settlementMonth,jdbcType=VARCHAR}
        </if>
        <if test="params.settlementMonthStart != null and params.settlementMonthStart != ''">
            and settlement_month >= #{params.settlementMonthStart,jdbcType=VARCHAR}
        </if>
        <if test="params.settlementMonthEnd != null and params.settlementMonthEnd != ''">
            and settlement_month &lt;= #{params.settlementMonthEnd,jdbcType=VARCHAR}
        </if>
    </select>
    <resultMap id="getVehicleSettlementDetail_Map" type="com.logistics.tms.controller.vehiclesettlement.response.GetVehicleSettlementDetailResponseModel">

        <result column="id" property="vehicleSettlementId"/>
        <result column="status" property="status"/>
        <result column="settlement_month" property="settlementMonth"/>
        <result column="vehicle_id" property="vehicleId"/>
        <result column="vehicle_no" property="vehicleNo"/>
        <result column="if_adjust_fee" property="ifAdjustFee"/>
        <result column="adjust_fee" property="adjustFee"/>
        <result column="adjust_remark" property="adjustRemark"/>
        <result column="vehicle_claim_fee" property="vehicleClaimFee"/>
        <result column="accident_insurance_expense_total" property="accidentInsuranceExpenseTotal"/>
        <result column="accident_insurance_fee" property="accidentInsuranceFee"/>
        <result column="accident_insurance_claim_fee" property="accidentInsuranceClaimFee"/>
        <result column="deductingFeeTotal" property="deductingFeeTotal"/>
        <result column="remainingDeductingFeeTotal" property="remainingDeductingFeeTotal"/>
        <result column="actual_expenses_payable" property="actualExpensesPayable"/>
        <result column="remark" property="remark"/>
    </resultMap>
    <select id="getVehicleSettlementDetail" resultMap="getVehicleSettlementDetail_Map">
        select
        tvs.id,
        tvs.status,
        tvs.vehicle_id ,
        tvs.vehicle_no ,
        tvs.settlement_month ,
        tvs.actual_expenses_payable ,
        tvs.vehicle_claim_fee ,
        tvs.deducting_fee as deductingFeeTotal,
        tvs.remaining_fee as remainingDeductingFeeTotal,
        tvs.accident_insurance_expense_total ,
        tvs.accident_insurance_fee  ,
        tvs.accident_insurance_claim_fee  ,
        tvs.if_adjust_fee ,
        tvs.adjust_fee ,
        tvs.adjust_remark ,
        tvs.remark,
        tcp.file_path as attachment
        from t_vehicle_settlement tvs
        left join t_certification_pictures tcp on tcp.object_type = 23 and object_id = tvs.id and tcp.valid = 1
        where tvs.valid = 1
        and tvs.id = #{id,jdbcType=BIGINT}
    </select>

    <resultMap id="vehicleSettlementKanBan_Map" type="com.logistics.tms.controller.vehiclesettlement.response.VehicleSettlementKanBanResponseModel">
        <result column="settlement_month" property="settlementMonth" jdbcType="VARCHAR"/>
        <collection property="vehicleSettlementList" ofType="com.logistics.tms.controller.vehiclesettlement.response.VehicleSettlementKanBanModel">
            <id column="id" property="vehicleSettlementId" jdbcType="BIGINT"/>
            <result column="status" property="status" jdbcType="INTEGER"/>
        </collection>
    </resultMap>
    <select id="vehicleSettlementKanBan" resultMap="vehicleSettlementKanBan_Map">
        select
        id,
        status,
        settlement_month
        from t_vehicle_settlement
        where valid = 1
        and instr(settlement_month,#{settlementYear,jdbcType=VARCHAR})
        group by vehicle_id,settlement_month
    </select>

    <select id="getFirstNotSettlementByVehicleId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_vehicle_settlement
        where valid = 1
        and status &lt; 4
        and vehicle_id = #{vehicleId,jdbcType=BIGINT}
        order by settlement_month asc
        limit 1
    </select>

    <select id="getVehicleByMonth" resultType="java.lang.Long">
        select
        vehicle_id
        from t_vehicle_settlement
        where valid = 1
        and settlement_month = #{settlementMonth,jdbcType=VARCHAR}
    </select>
    <select id="getCancelVehicleSettlementDetailById" resultType="com.logistics.tms.controller.vehiclesettlement.response.CancelVehicleSettlementDetailResponseModel">
        select
        tvs.id as vehicleSettlementId,
        tvs.vehicle_no as vehicleNo,
        tvs.settlement_month as  settlementMonth,
        tvs.actual_expenses_payable as actualExpensesPayable,
        tvs.remark as cancelReason,
        tvs.status,
        tvsdr.reason,
        tvsdr.driver_name as driverName,
        tvsdr.driver_mobile as driverPhone,
        tvsdr.commit_image_url as commitImageUrl,
        tvsdr.status as driverStatus

        from t_vehicle_settlement tvs
        left join t_vehicle_settlement_driver_relation tvsdr on tvsdr.vehicle_settlement_id=tvs.id and tvsdr.valid=1
        where tvs.valid=1
        and tvs.id = #{vehicleSettlementId,jdbcType=BIGINT}
    </select>

    <select id="settleFreightDetailById" resultType="com.logistics.tms.controller.vehiclesettlement.response.SettleFreightDetailResponseModel">
        select
        tvs.id as vehicleSettlementId,
        tvs.vehicle_no as vehicleNo,
        tvs.settlement_month as settlementMonth,
        tvs.status,
        tvs.actual_expenses_payable as actualExpensesPayable,
        sum(tvsp.pay_fee) as payMoneyTotal
        from t_vehicle_settlement tvs
        left join t_vehicle_settlement_payment tvsp on tvsp.vehicle_settlement_id=tvs.id and tvsp.valid=1
        where tvs.valid=1
        and tvs.id = #{vehicleSettlementId,jdbcType=BIGINT}
        group by tvs.id
    </select>


    <select id="getVehicleSettlementByIds" parameterType="String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_vehicle_settlement
        where valid = 1
        and id in (${ids})
        order by created_time desc,id desc
    </select>

    <update id="batchUpdateSelective" parameterType="com.logistics.tms.entity.TVehicleSettlement">
        <foreach collection="list" item="item" separator=";">
            update t_vehicle_settlement
            <set>
                <if test="item.status != null">
                    status = #{item.status,jdbcType=INTEGER},
                </if>
                <if test="item.vehicleId != null">
                    vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleNo != null">
                    vehicle_no = #{item.vehicleNo,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleProperty != null">
                    vehicle_property = #{item.vehicleProperty,jdbcType=INTEGER},
                </if>
                <if test="item.settlementMonth != null">
                    settlement_month = #{item.settlementMonth,jdbcType=VARCHAR},
                </if>
                <if test="item.actualExpensesPayable != null">
                    actual_expenses_payable = #{item.actualExpensesPayable,jdbcType=DECIMAL},
                </if>
                <if test="item.carrierOrderCount != null">
                    carrier_order_count = #{item.carrierOrderCount,jdbcType=INTEGER},
                </if>
                <if test="item.carrierFreight != null">
                    carrier_freight = #{item.carrierFreight,jdbcType=DECIMAL},
                </if>
                <if test="item.tireFee != null">
                    tire_fee = #{item.tireFee,jdbcType=DECIMAL},
                </if>
                <if test="item.gpsFee != null">
                    gps_fee = #{item.gpsFee,jdbcType=DECIMAL},
                </if>
                <if test="item.parkingFee != null">
                    parking_fee = #{item.parkingFee,jdbcType=DECIMAL},
                </if>
                <if test="item.loanFee != null">
                    loan_fee = #{item.loanFee,jdbcType=DECIMAL},
                </if>
                <if test="item.oilFilledFee != null">
                    oil_filled_fee = #{item.oilFilledFee,jdbcType=DECIMAL},
                </if>
                <if test="item.vehicleClaimFee != null">
                    vehicle_claim_fee = #{item.vehicleClaimFee,jdbcType=DECIMAL},
                </if>
                <if test="item.insuranceFee != null">
                    insurance_fee = #{item.insuranceFee,jdbcType=DECIMAL},
                </if>
                <if test="item.deductingFee != null">
                    deducting_fee = #{item.deductingFee,jdbcType=DECIMAL},
                </if>
                <if test="item.remainingFee != null">
                    remaining_fee = #{item.remainingFee,jdbcType=DECIMAL},
                </if>
                <if test="item.accidentInsuranceFee != null">
                    accident_insurance_fee = #{item.accidentInsuranceFee,jdbcType=DECIMAL},
                </if>
                <if test="item.accidentInsuranceExpenseTotal != null">
                    accident_insurance_expense_total = #{item.accidentInsuranceExpenseTotal,jdbcType=DECIMAL},
                </if>
                <if test="item.accidentInsuranceClaimFee != null">
                    accident_insurance_claim_fee = #{item.accidentInsuranceClaimFee,jdbcType=DECIMAL},
                </if>
                <if test="item.oilRefundFee != null">
                    oil_refund_fee = #{item.oilRefundFee,jdbcType=DECIMAL},
                </if>
                <if test="item.insuranceRefundFee != null">
                    insurance_refund_fee = #{item.insuranceRefundFee,jdbcType=DECIMAL},
                </if>
                <if test="item.ifAdjustFee != null">
                    if_adjust_fee = #{item.ifAdjustFee,jdbcType=INTEGER},
                </if>
                <if test="item.adjustFee != null">
                    adjust_fee = #{item.adjustFee,jdbcType=DECIMAL},
                </if>
                <if test="item.adjustRemark != null">
                    adjust_remark = #{item.adjustRemark,jdbcType=VARCHAR},
                </if>
                <if test="item.withdrawRemark != null" >
                    withdraw_remark = #{item.withdrawRemark,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="searchDriverReconciliationList" resultType="com.logistics.tms.controller.vehiclesettlement.response.SearchDriverReconciliationListResponseModel">
        select
        tvs.id as vehicleSettlementId,
        tvs.status,
        tvs.vehicle_no as vehicleNo,
        tvs.settlement_month as settlementMonth,
        tvs.carrier_order_count as carrierOrderCount,
        tvs.adjust_fee as adjustFee,
        tvs.actual_expenses_payable as actualExpensesPayable
        from t_vehicle_settlement tvs
        left join t_vehicle_settlement_driver_relation tvsdr on tvsdr.vehicle_settlement_id = tvs.id and tvsdr.valid = 1
        where tvs.valid = 1
        and tvsdr.driver_id = #{params.driverId,jdbcType=BIGINT}
        and tvs.status > 1
        <if test="params.status != null">
            <choose>
                <when test="params.status == 4">
                    and tvs.status in (4,5)
                </when>
                <otherwise>
                    and tvs.status = #{params.status,jdbcType=INTEGER}
                </otherwise>
            </choose>
        </if>
        <if test="params.settlementMonth != null and params.settlementMonth != ''">
            and tvs.settlement_month = #{params.settlementMonth,jdbcType=VARCHAR}
        </if>
        order by tvs.settlement_month desc, tvs.id desc
    </select>

    <select id="driverReconciliationListCount" resultType="com.logistics.tms.controller.vehiclesettlement.response.DriverReconciliationListCountResponseModel">
        select
        ifnull(count(tvs.id),0) as allCount,
        ifnull(sum(if(tvs.status = 2,1,0)),0) as waitConfirmCount,
        ifnull(sum(if(tvs.status in (4,5),1,0)),0) as waitSettleCount,
        ifnull(sum(if(tvs.status = 6,1,0)),0) as completeSettleCount,
        ifnull(sum(if(tvs.status = 3,1,0)),0) as waitSolveCount
        from t_vehicle_settlement tvs
        left join t_vehicle_settlement_driver_relation tvsdr on tvsdr.vehicle_settlement_id = tvs.id and tvsdr.valid = 1
        where tvs.valid = 1
        and tvsdr.driver_id = #{params.driverId,jdbcType=BIGINT}
        and tvs.status > 1
        <if test="params.settlementMonth != null and params.settlementMonth != ''">
            and tvs.settlement_month = #{params.settlementMonth,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="settlementStatementHandleDetail" resultType="com.logistics.tms.controller.vehiclesettlement.response.SettlementStatementHandleDetailResponseModel">
        select
        tvs.id as vehicleSettlementId,
        tvs.vehicle_no as vehicleNo,
        tvs.settlement_month as settlementMonth,
        tvs.actual_expenses_payable as actualExpensesPayable,
        tvs.adjust_remark as adjustRemark,
        tvs.adjust_fee as adjustFee,
        tvs.status,
        tvsdr.driver_name as driverName,
        tvsdr.driver_mobile as driverPhone,
        tvsdr.settlement_reason_remark as settlementReasonRemark
        from t_vehicle_settlement tvs
        left join t_vehicle_settlement_driver_relation tvsdr on tvsdr.vehicle_settlement_id=tvs.id and tvsdr.valid=1
        where tvs.valid=1
        and tvs.id = #{vehicleSettlementId,jdbcType=BIGINT}
    </select>


    <resultMap id="getSettlementStatementRecordList_Map" type="com.logistics.tms.controller.vehiclesettlement.response.SettlementStatementRecordResponseModel">
        <id column="id" property="vehicleSettlementId" jdbcType="BIGINT"/>
        <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="settlement_month" property="settlementMonth" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="driver_name" property="driverName" jdbcType="VARCHAR"/>
        <result column="driver_mobile" property="driverMobile" jdbcType="VARCHAR"/>
        <result column="actual_expenses_payable" property="actualExpensesPayable" jdbcType="DECIMAL"/>
        <collection property="itemList" ofType="com.logistics.tms.controller.vehiclesettlement.response.SettleStatementRecordItemModel">
            <result column="pay_company" property="payCompany" jdbcType="VARCHAR"/>
            <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
            <result column="pay_fee" property="payFee" jdbcType="DECIMAL"/>
            <result column="pay_time" property="payTime" jdbcType="TIMESTAMP"/>
        </collection>
    </resultMap>


    <select id="getSettlementStatementRecordList" parameterType="long" resultMap="getSettlementStatementRecordList_Map">
        SELECT
            tvs.id,
            tvs.vehicle_no,
            tvs.status,
            tvs.actual_expenses_payable,
            tvs.settlement_month,
            tvsdr.driver_name,
            tvsdr.driver_mobile,
            tvsp.pay_company,
            tvsp.receiver_name,
            tvsp.pay_fee,
            tvsp.pay_time
        FROM
            t_vehicle_settlement tvs
                LEFT JOIN t_vehicle_settlement_driver_relation tvsdr ON tvsdr.vehicle_settlement_id = tvs.id
                AND tvsdr.valid = 1
                LEFT JOIN t_vehicle_settlement_payment tvsp ON tvsp.vehicle_settlement_id = tvs.id
                AND tvsp.valid = 1
        WHERE
            tvs.valid = 1
          and tvs.id = #{vehicleSettlementId,jdbcType=BIGINT}
    </select>


    <select id="driverReconciliationDetail" resultType="com.logistics.tms.controller.vehiclesettlement.response.DriverReconciliationDetailResponseModel">
        select
        tvs.id as vehicleSettlementId,
        tvs.status,
        tvs.carrier_freight as carrierFreight,
        tvs.carrier_order_count as carrierOrderCount,
        tvs.adjust_fee as adjustFee,
        tvs.adjust_remark as adjustRemark,
        tvs.tire_fee as tireFee,
        tvs.oil_filled_fee as oilFilledFee,
        tvs.gps_fee as gpsFee,
        tvs.parking_fee as parkingFee,
        tvs.insurance_fee as insuranceFee,
        tvs.vehicle_claim_fee as vehicleClaimFee,
        tvs.accident_insurance_claim_fee as accidentInsuranceClaimFee,
        tvs.accident_insurance_fee as accidentInsuranceFee,
        tvs.loan_fee as loanFee,
        tvs.actual_expenses_payable as actualExpensesPayable,
        tvsdr.settlement_reason_remark as settlementReasonRemark
        from t_vehicle_settlement tvs
        left join t_vehicle_settlement_driver_relation tvsdr on tvsdr.vehicle_settlement_id = tvs.id and tvsdr.valid = 1
        where tvs.valid = 1
        and tvs.id = #{vehicleSettlementId,jdbcType=BIGINT}
        and tvsdr.driver_id = #{driverId,jdbcType=BIGINT}
        and tvs.status > 1
    </select>
    <select id="settlementStatementHandleInfo" resultType="com.logistics.tms.controller.vehiclesettlement.response.SettlementStatementHandleModel">
        select
        tvs.id as vehicleSettlementId,
        tvs.status as vehicleSettlementStatus,
        tvs.settlement_month as settlementMonth,
        tvs.actual_expenses_payable as actualExpensesPayable,
        tvs.adjust_fee as adjustFee,
        tvs.vehicle_id as vehicleId,
        tvsdr.id as vehicleSettlementDriverRelationId,
        tvsdr.status as vehicleSettlementDriverRelationStatus
        from t_vehicle_settlement tvs
        left join t_vehicle_settlement_driver_relation tvsdr on tvsdr.vehicle_settlement_id=tvs.id and tvsdr.valid=1
        where tvs.valid=1
        and tvs.id= #{vehicleSettlementId,jdbcType=BIGINT}

    </select>

    <select id="driverReconciliationConfirmDetail" resultType="com.logistics.tms.controller.vehiclesettlement.response.DriverReconciliationConfirmDetailResponseModel">
        select
        tvs.id as vehicleSettlementId,
        tvs.vehicle_no as vehicleNo,
        tvs.settlement_month as settlementMonth,
        tvs.actual_expenses_payable as actualExpensesPayable,

        tvsdr.driver_name as driverName,
        tvsdr.driver_mobile as driverMobile,
        tvsdr.status as confirmStatus,
        tvsdr.confirm_time as confirmTime,
        tvsdr.commit_image_url as commitImageUrl,
        tvsdr.reason
        from t_vehicle_settlement tvs
        left join t_vehicle_settlement_driver_relation tvsdr on tvsdr.vehicle_settlement_id = tvs.id and tvsdr.valid = 1
        where tvs.valid = 1
        and tvs.id = #{vehicleSettlementId,jdbcType=BIGINT}
        and tvsdr.driver_id = #{driverId,jdbcType=BIGINT}
        and tvs.status > 3
    </select>

    <resultMap id="driverReconciliationCarrierOrder_Map" type="com.logistics.tms.controller.vehiclesettlement.response.ReconciliationCarrierOrderDetailResponseModel">
        <result column="settlement_month" property="settlementMonth" jdbcType="VARCHAR"/>
        <result column="carrier_freight" property="carrierFreight" jdbcType="DECIMAL"/>
        <result column="adjust_fee" property="adjustFee" jdbcType="DECIMAL"/>
        <collection property="carrierOrderIdList" ofType="java.lang.Long" javaType="list">
            <result column="object_id"/>
        </collection>
    </resultMap>
    <select id="driverReconciliationCarrierOrder" resultMap="driverReconciliationCarrierOrder_Map">
        select
        tvs.settlement_month,
        tvs.carrier_freight,
        tvs.adjust_fee,

        tvsr.object_id
        from t_vehicle_settlement tvs
        left join t_vehicle_settlement_relation tvsr on tvsr.vehicle_settlement_id = tvs.id and tvsr.valid = 1
        left join t_vehicle_settlement_driver_relation tvsdr on tvsdr.vehicle_settlement_id = tvs.id and tvsdr.valid = 1
        where tvs.valid = 1
        and tvs.id = #{vehicleSettlementId,jdbcType=BIGINT}
        and tvsdr.driver_id = #{driverId,jdbcType=BIGINT}
        and tvsr.object_type = 70
    </select>

    <resultMap id="driverReconciliationTire_Map" type="com.logistics.tms.controller.vehiclesettlement.response.ReconciliationTireDetailResponseModel">
        <result column="settlement_month" property="settlementMonth" jdbcType="VARCHAR"/>
        <result column="tire_fee" property="tireFee" jdbcType="DECIMAL"/>
        <collection property="tireIdList" ofType="java.lang.Long" javaType="list">
            <result column="object_id"/>
        </collection>
    </resultMap>
    <select id="driverReconciliationTire" resultMap="driverReconciliationTire_Map">
        select
        tvs.settlement_month,
        tvs.tire_fee,

        tvsr.object_id
        from t_vehicle_settlement tvs
        left join t_vehicle_settlement_relation tvsr on tvsr.vehicle_settlement_id = tvs.id and tvsr.valid = 1
        left join t_vehicle_settlement_driver_relation tvsdr on tvsdr.vehicle_settlement_id = tvs.id and tvsdr.valid = 1
        where tvs.valid = 1
        and tvs.id = #{vehicleSettlementId,jdbcType=BIGINT}
        and tvsdr.driver_id = #{driverId,jdbcType=BIGINT}
        and tvsr.object_type = 40
    </select>

    <resultMap id="driverReconciliationOilFilled_Map" type="com.logistics.tms.controller.vehiclesettlement.response.ReconciliationOilFilledDetailResponseModel">
        <result column="settlement_month" property="settlementMonth" jdbcType="VARCHAR"/>
        <result column="oil_filled_fee" property="oilFilledFee" jdbcType="DECIMAL"/>
        <collection property="oilFilledIdList" ofType="java.lang.Long" javaType="list">
            <result column="object_id"/>
        </collection>
    </resultMap>
    <select id="driverReconciliationOilFilled" resultMap="driverReconciliationOilFilled_Map">
        select
        tvs.settlement_month,
        tvs.oil_filled_fee,
        tvsr.object_id
        from t_vehicle_settlement tvs
        left join t_vehicle_settlement_relation tvsr on tvsr.vehicle_settlement_id = tvs.id and tvsr.valid = 1
        left join t_vehicle_settlement_driver_relation tvsdr on tvsdr.vehicle_settlement_id = tvs.id and tvsdr.valid = 1
        where tvs.valid = 1
        and tvs.id = #{vehicleSettlementId,jdbcType=BIGINT}
        and tvsdr.driver_id = #{driverId,jdbcType=BIGINT}
        and tvsr.object_type = 50
    </select>


    <select id="getSettlementVehicleDriverInfoByIds" resultType="com.logistics.tms.controller.vehiclesettlement.response.GetSettlementDriverModel">
        select
        tvsr.vehicle_settlement_id  vehicleSettlementId,
        tsb.id driverId,
        tsb.name driverName,
        tsb.mobile driverMobile
        from t_carrier_order_vehicle_history tcovh
        left join t_vehicle_settlement_relation tvsr on tvsr.object_id = tcovh.carrier_order_id and tvsr.valid = 1 and tvsr.object_type = 70
        left join t_staff_basic tsb on tsb.id  =  tcovh.driver_id and tsb.valid = 1
        left join t_carrier_driver_relation tcdr on tcdr.driver_id = tsb.id and tcdr.valid = 1
        where tcovh.valid = 1
        and tsb.staff_property in (1, 3)
        and tcdr.enabled = 1
        and tvsr.vehicle_settlement_id in (${vehicleSettlementIds})
        GROUP BY tvsr.vehicle_settlement_id
    </select>

    <select id="getSettlementVehicleDriverInfoNoCarrier" resultType="com.logistics.tms.controller.vehiclesettlement.response.GetSettlementDriverModel">
        select
        tvs.id vehicleSettlementId,
        tsb.id driverId,
        tsb.name driverName,
        tsb.mobile driverMobile
        from t_vehicle_settlement tvs
        left join t_staff_vehicle_relation tsvr on tsvr.vehicle_id = tvs.vehicle_id and tsvr.valid = 1
        left join t_staff_basic tsb on tsb.id  =  tsvr.staff_id and tsb.valid = 1
        left join t_carrier_driver_relation tcdr on tcdr.driver_id = tsb.id and tcdr.valid = 1
        where tvs.valid = 1
        and tsb.staff_property in (1, 3)
        and tcdr.enabled = 1
        and tvs.id in (${vehicleSettlementIds})
        GROUP BY tvs.id
    </select>
</mapper>