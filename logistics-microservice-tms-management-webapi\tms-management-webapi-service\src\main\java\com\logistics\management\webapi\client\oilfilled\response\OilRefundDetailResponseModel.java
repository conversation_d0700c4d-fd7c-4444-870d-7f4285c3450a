package com.logistics.management.webapi.client.oilfilled.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/12/23 14:30
 */
@Data
public class OilRefundDetailResponseModel {
    @ApiModelProperty(value = "充油ID")
    private Long oilFilledId;
    @ApiModelProperty(value = "充油方式：1 充油卡，2 加油车")
    private Integer oilFilledType;
    @ApiModelProperty(value = "车牌号Id")
    private Long vehicleId;
    @ApiModelProperty(value = "车牌号号码")
    private String vehicleNo;
    @ApiModelProperty(value = "司机ID")
    private Long staffId;
    @ApiModelProperty(value = "司机")
    private String name;
    @ApiModelProperty(value = "手机号")
    private String mobile;
    @ApiModelProperty(value = "退款金额")
    private BigDecimal oilFilledFee;
    @ApiModelProperty(value = "退款时间")
    private Date oilFilledDate;
    @ApiModelProperty(value = "退款原因类型：0 其他，10 丢失副卡，20 车辆过户，30 车辆报废，40 充油充错")
    private Integer refundReasonType;
    @ApiModelProperty(value = "退款原因")
    private String refundReason;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "附件")
    private String refundFile;

    @ApiModelProperty(value = "是否有结算数据,1有，0没有")
    private Integer ifSettlement = 0;
}
