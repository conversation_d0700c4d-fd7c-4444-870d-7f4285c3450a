package com.logistics.appapi.client.carrierorderotherfee.hystrix;

import com.logistics.appapi.client.carrierorderotherfee.CarrierOrderOtherFeeClient;
import com.logistics.appapi.client.carrierorderotherfee.request.AddCarrierOrderOtherFeeRequestModel;
import com.logistics.appapi.client.carrierorderotherfee.request.CommitCarrierOrderOtherFeeRequestModel;
import com.logistics.appapi.client.carrierorderotherfee.request.GetOtherFeeByCarrierOrderIdRequestModel;
import com.logistics.appapi.client.carrierorderotherfee.response.GetOtherFeeByCarrierOrderIdResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2023/10/26 14:28
 */
@Component
public class CarrierOrderOtherFeeHystrix implements CarrierOrderOtherFeeClient {

    @Override
    public Result<Boolean> addCarrierOrderOtherFee(AddCarrierOrderOtherFeeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> commitCarrierOrderOtherFee(CommitCarrierOrderOtherFeeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetOtherFeeByCarrierOrderIdResponseModel> getOtherFeeByCarrierOrderId(GetOtherFeeByCarrierOrderIdRequestModel requestModel) {
        return Result.timeout();
    }
}
