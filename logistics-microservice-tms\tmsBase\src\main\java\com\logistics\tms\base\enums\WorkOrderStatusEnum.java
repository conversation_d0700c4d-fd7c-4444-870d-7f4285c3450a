package com.logistics.tms.base.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Stream;

/**
 * 状态：0 待处理，10 处理中，20 已处理，30 已关闭，40 已撤销
 */
@Getter
@AllArgsConstructor
public enum WorkOrderStatusEnum {

    DEFAULT(-1, ""),
    WAITING_TASK(0, "待处理"),
    BEING_PROCESSED(10, "处理中"),
    PROCESSED(20, "已处理"),
    CLOSED(30, "已关闭"),
    RESCINDED(40, "已撤销");

    private final Integer key;
    private final String value;

    public static WorkOrderStatusEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }

    /**
     * 校验状态是否可以变更转台
     * @param currentStatus 当前状态
     * @param changeStatus 变更状态
     * @return true 允许 | false 不允许
     */
    public static boolean checkNextStatusByCurrentStatus(Integer currentStatus, Integer changeStatus) {
        WorkOrderStatusEnum workOrderStatusEnum = getEnumByKey(currentStatus);
        List<Integer> statusList = Lists.newArrayList();
        switch (workOrderStatusEnum) {
            case WAITING_TASK:
                statusList.add(BEING_PROCESSED.getKey());
                statusList.add(RESCINDED.getKey());
                statusList.add(CLOSED.getKey());
                break;
            case BEING_PROCESSED:
                statusList.add(PROCESSED.getKey());
                statusList.add(CLOSED.getKey());
                break;
        }
        return statusList.contains(changeStatus);
    }

    /**
     * 校验状态是否不可以变更状态
     * @param currentStatus 当前状态
     * @param changeStatus 变更状态
     * @return true 允许 | false 不允许
     */
    public static boolean negateCheckNextStatusByCurrentStatus(Integer currentStatus, Integer changeStatus) {
        return !checkNextStatusByCurrentStatus(currentStatus, changeStatus);
    }
}
