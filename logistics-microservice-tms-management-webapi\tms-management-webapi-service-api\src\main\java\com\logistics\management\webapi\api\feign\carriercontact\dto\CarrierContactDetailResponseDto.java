package com.logistics.management.webapi.api.feign.carriercontact.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * liang current user system login name
 * 2018/9/18 current system date
 */
@Data
public class CarrierContactDetailResponseDto {

    @ApiModelProperty("车主账号id")
    private String carrierContactId = "";

    @ApiModelProperty("车主类型")
    private String type = "";

    @ApiModelProperty("车主类型文本")
    private String typeLabel = "";

    @ApiModelProperty("车主id")
    private String companyCarrierId = "";

    @ApiModelProperty("车主名称")
    private String companyCarrierName = "";

    @ApiModelProperty("姓名")
    private String contactName = "";

    @ApiModelProperty("手机号")
    private String contactPhone = "";

    @ApiModelProperty("身份证号")
    private String identityNumber = "";

}
