package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * @author: wjf
 * @date: 2024/8/23 9:27
 */
@AllArgsConstructor
@Getter
public enum ReservationOrderStatusEnum {
    DEFAULT(-99999, ""),
    WAIT_SIGN_IN(10, "待签到"),
    SIGN_IN(20, "已签到"),
    INVALID(30, "已失效"),
    ;

    private final Integer key;
    private final String value;

    public static ReservationOrderStatusEnum getEnum(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }

}
