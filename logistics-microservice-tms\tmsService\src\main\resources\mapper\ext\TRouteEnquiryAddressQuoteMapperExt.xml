<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRouteEnquiryAddressQuoteMapper" >
  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TRouteEnquiryAddressQuote" >
    <foreach collection="recordList" item="item" separator=";">
      insert into t_route_enquiry_address_quote
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.routeEnquiryAddressId != null" >
          route_enquiry_address_id,
        </if>
        <if test="item.routeEnquiryCompanyId != null" >
          route_enquiry_company_id,
        </if>
        <if test="item.distance != null" >
          distance,
        </if>
        <if test="item.quotePriceType != null" >
          quote_price_type,
        </if>
        <if test="item.quotePrice != null" >
          quote_price,
        </if>
        <if test="item.quoteRemark != null" >
          quote_remark,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.routeEnquiryAddressId != null" >
          #{item.routeEnquiryAddressId,jdbcType=BIGINT},
        </if>
        <if test="item.routeEnquiryCompanyId != null" >
          #{item.routeEnquiryCompanyId,jdbcType=BIGINT},
        </if>
        <if test="item.distance != null" >
          #{item.distance,jdbcType=DECIMAL},
        </if>
        <if test="item.quotePriceType != null" >
          #{item.quotePriceType,jdbcType=INTEGER},
        </if>
        <if test="item.quotePrice != null" >
          #{item.quotePrice,jdbcType=DECIMAL},
        </if>
        <if test="item.quoteRemark != null" >
          #{item.quoteRemark,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

  <update id="batchUpdateSelective" parameterType="com.logistics.tms.entity.TRouteEnquiryAddressQuote" >
    <foreach collection="recordList" item="item" separator=";">
      update t_route_enquiry_address_quote
      <set >
        <if test="item.routeEnquiryAddressId != null" >
          route_enquiry_address_id = #{item.routeEnquiryAddressId,jdbcType=BIGINT},
        </if>
        <if test="item.routeEnquiryCompanyId != null" >
          route_enquiry_company_id = #{item.routeEnquiryCompanyId,jdbcType=BIGINT},
        </if>
        <if test="item.distance != null" >
          distance = #{item.distance,jdbcType=DECIMAL},
        </if>
        <if test="item.quotePriceType != null" >
          quote_price_type = #{item.quotePriceType,jdbcType=INTEGER},
        </if>
        <if test="item.quotePrice != null" >
          quote_price = #{item.quotePrice,jdbcType=DECIMAL},
        </if>
        <if test="item.quoteRemark != null" >
          quote_remark = #{item.quoteRemark,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="getByCompanyId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_route_enquiry_address_quote
    where
    route_enquiry_company_id = #{routeEnquiryCompanyId,jdbcType=BIGINT}
    and valid = 1
  </select>

  <select id="getQuoteDetail" resultType="com.logistics.tms.controller.routeenquiry.response.GetRouteEnquiryQuoteDetailResponseModel">
    select
    treaq.id as routeEnquiryAddressQuoteId,
    treaq.distance as distance,
    treaq.quote_price as quotePrice,
    treaq.quote_price_type as quotePriceType,
    treaq.quote_remark as quoteRemark,


    trea.from_warehouse as fromWarehouse,
    trea.from_province_name as fromProvinceName,
    trea.from_city_name as fromCityName,
    trea.from_area_name as fromAreaName,
    trea.to_province_name as toProvinceName,
    trea.to_city_name as toCityName,
    trea.to_area_name as toAreaName
    from t_route_enquiry_address_quote treaq
    left join t_route_enquiry_address trea on treaq.route_enquiry_address_id = trea.id and trea.valid = 1
    where
    treaq.route_enquiry_company_id = #{routeEnquiryCompanyId,jdbcType=BIGINT}
    and treaq.valid = 1
    order by trea.id desc
  </select>
</mapper>