package com.logistics.appapi.client.attendance.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class UpdateAttendanceHistoryDetailResponseModel {

    @ApiModelProperty("考勤打卡ID")
    private Long attendanceRecordId;

    @ApiModelProperty("考勤日期,yyyy-MM-dd")
    private Date attendanceDate;

    @ApiModelProperty("上班打卡时间")
    private Date onDutyPunchTime;

    @ApiModelProperty("上班打卡地点")
    private String onDutyPunchLocation;

    @ApiModelProperty("下班打卡时间")
    private Date offDutyPunchTime;

    @ApiModelProperty("下班打卡地点")
    private String offDutyPunchLocation;

    @ApiModelProperty("工时")
    private BigDecimal manHour;

    @ApiModelProperty("变更的打卡类型 1: 上班 2:下班")
    private Integer changeType;

    @ApiModelProperty("变更申请ID")
    private Long attendanceChangeApplyId;

    @ApiModelProperty("变更的打卡时间")
    private Date changePunchTime;

    @ApiModelProperty("变更原因")
    private String changeReason;
}
