package com.logistics.tms.api.feign.extvehiclesettlement.model;

import com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/11/20 13:33
 */
@Data
public class SearchExtVehicleSettlementListResponseModel {
    @ApiModelProperty("外部车辆结算ID")
    private Long extVehicleSettlementId;
    @ApiModelProperty("支付状态：0 未支付，1 已支付")
    private Integer payStatus;
    @ApiModelProperty("运单ID")
    private Long carrierOrderId;
    @ApiModelProperty("运单状态：50000 待签收，60000 已签收 2已放空")
    private Integer status ;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;
    @ApiModelProperty("需求单ID")
    private Long demandOrderId;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机名")
    private String driverName;
    @ApiModelProperty("司机手机")
    private String driverMobile;
    @ApiModelProperty("司机合计费用")
    private BigDecimal driverTotalFee;
    @ApiModelProperty("结算数量")
    private BigDecimal settlementAmount;
    @ApiModelProperty("实际签收时间")
    private Date signTime ;
    @ApiModelProperty("发货地（市）")
    private String loadCityName;
    @ApiModelProperty("收货地（市）")
    private String unloadCityName;
    @ApiModelProperty("调度人")
    private String dispatchUserName;
    @ApiModelProperty("货主公司名称")
    private String companyEntrustName;
    @ApiModelProperty("车主id")
    private Long companyCarrierId;
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName;
    @ApiModelProperty("车主公司类型")
    private Integer companyCarrierType;
    @ApiModelProperty("车主联系人")
    private String carrierContactName;
    @ApiModelProperty("车主联系人电话")
    private String carrierContactPhone;
    @ApiModelProperty("运单生成时间")
    private Date dispatchTime;

    @ApiModelProperty("司机临时费用")
    private BigDecimal driverOtherFee;

    @ApiModelProperty("结算合计总费用")
    private BigDecimal totalFee;
    @ApiModelProperty("单位：1 件，2 吨，3 方，4 块")
    private Integer settlementUnit;
    @ApiModelProperty("是否放空 0否 1是")
    private Integer ifEmpty;
    private List<SearchCarrierOrderListGoodsInfoModel> goodsInfoList;

    @ApiModelProperty("委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件")
    private Integer demandOrderSource;
}
