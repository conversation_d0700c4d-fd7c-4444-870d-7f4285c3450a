package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;


public class ExportExcelPersonalAccidentInsurance {
    private ExportExcelPersonalAccidentInsurance() {
    }

    private static final Map<String, String> EXPORT_PERSONAL_ACCIDENT_INSURANCE;

    static {
        EXPORT_PERSONAL_ACCIDENT_INSURANCE = new LinkedHashMap<>();
        EXPORT_PERSONAL_ACCIDENT_INSURANCE.put("保单类型 ", "insuranceType");
        EXPORT_PERSONAL_ACCIDENT_INSURANCE.put("保险公司", "insuranceCompany");
        EXPORT_PERSONAL_ACCIDENT_INSURANCE.put("保单号", "policyNumber");
        EXPORT_PERSONAL_ACCIDENT_INSURANCE.put("批单号", "batchNumber");
        EXPORT_PERSONAL_ACCIDENT_INSURANCE.put("保费总额", "grossPremium");
        EXPORT_PERSONAL_ACCIDENT_INSURANCE.put("保单人数", "policyPersonCount");
        EXPORT_PERSONAL_ACCIDENT_INSURANCE.put("已关联人数", "associatedCount");
        EXPORT_PERSONAL_ACCIDENT_INSURANCE.put("单笔保金（元/人）", "singlePremium");
        EXPORT_PERSONAL_ACCIDENT_INSURANCE.put("保单凭证", "ticketsCount");
        EXPORT_PERSONAL_ACCIDENT_INSURANCE.put("保单生效时间", "startTime");
        EXPORT_PERSONAL_ACCIDENT_INSURANCE.put("保单截止时间", "endTime");
        EXPORT_PERSONAL_ACCIDENT_INSURANCE.put("操作人", "lastModifiedBy");
        EXPORT_PERSONAL_ACCIDENT_INSURANCE.put("操作时间", "lastModifiedTime");
    }

    public static Map<String, String> getExportPersonalAccidentInsurance() {
        return EXPORT_PERSONAL_ACCIDENT_INSURANCE;
    }
}
