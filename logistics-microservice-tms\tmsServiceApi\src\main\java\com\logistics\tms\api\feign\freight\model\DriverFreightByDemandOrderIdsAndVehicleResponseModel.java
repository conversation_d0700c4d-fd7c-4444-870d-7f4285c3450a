package com.logistics.tms.api.feign.freight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DriverFreightByDemandOrderIdsAndVehicleResponseModel {
    @ApiModelProperty("价格类型 1 单价 2 整车价")
    private Integer priceType = 0;
    @ApiModelProperty("价格")
    private BigDecimal price = BigDecimal.ZERO;
    @ApiModelProperty("多装多卸车加价")
    private BigDecimal multipleMarkupPrice = BigDecimal.ZERO;
}
