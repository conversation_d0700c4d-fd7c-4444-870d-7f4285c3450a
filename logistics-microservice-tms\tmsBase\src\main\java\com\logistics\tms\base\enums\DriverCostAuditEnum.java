package com.logistics.tms.base.enums;

import com.google.common.collect.Lists;

import java.util.Optional;

/**
 * 司机费用申请审核枚举
 * <p>
 * 审核状态：-1 待业务审核，0 待财务审核，1 已审核，2 已驳回，3 已撤销，4 已红冲
 */
public enum DriverCostAuditEnum {
    DEFAULT(-1, ""),
    WAIT_BUSINESS_AUDIT(-1, "待业务审核"),
    WAIT_FINANCIAL_AUDIT(0, "待财务审核"),
    AUDITED(1, "已审核"),
    REJECT(2, "已驳回"),
    REPEAL(3, "已撤销"),
    RED_CHARGE_REFUND(4, "已红冲"),
    ;

    private final Integer key;
    private final String value;

    DriverCostAuditEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static boolean isShowReserveApply(Integer auditStatus) {
        return Optional.ofNullable(auditStatus)
                .map(s -> Lists.newArrayList(AUDITED.getKey(), RED_CHARGE_REFUND.getKey()).contains(s))
                .orElse(Boolean.FALSE);
    }
}
