package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2021/9/18 9:52
 */
@Data
public class ReviewSignTicketsResponseDto {

    @ApiModelProperty("是否可进入H5进行预约标识  0:否 1：是  v2.45")
    private String enableReservationFlag = "";

    @ApiModelProperty("运单号（用于H5查 预约信息）  v2.45")
    private String carrierOrderId = "";

    @ApiModelProperty("accessToken 用于H5得交互，有效期 10分钟  v2.45")
    private String visitorToken = "";

    @ApiModelProperty("签收单全路径")
    private List<String> signTicketsPath;


}
