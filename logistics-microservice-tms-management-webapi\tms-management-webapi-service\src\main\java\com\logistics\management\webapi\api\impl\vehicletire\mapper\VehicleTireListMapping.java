package com.logistics.management.webapi.api.impl.vehicletire.mapper;

import com.logistics.management.webapi.api.feign.vehicletire.dto.VehicleTireListResponseDto;
import com.logistics.management.webapi.base.enums.VehiclePropertyEnum;
import com.logistics.management.webapi.base.enums.VehicleTireSettlementStatusEnum;
import com.logistics.tms.api.feign.vehicletire.model.VehicleTireListResponseModel;
import com.logistics.tms.api.feign.vehicletire.model.VehicleTireNoListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;

public class VehicleTireListMapping extends MapperMapping<VehicleTireListResponseModel, VehicleTireListResponseDto> {
    @Override
    public void configure() {
        VehicleTireListResponseModel source = this.getSource();
        VehicleTireListResponseDto destination = this.getDestination();
        if (source != null) {
            String driverName = StringUtils.isBlank(source.getDriverName())?"":source.getDriverName();
            String driverMobile = StringUtils.isBlank(source.getDriveMobile())?"":source.getDriveMobile();
            destination.setDriverLabel(driverName + " " + driverMobile);
            destination.setSettlementStatusLabel(VehicleTireSettlementStatusEnum.getEnum(source.getSettlementStatus()).getValue());
            destination.setVehiclePropertyLabel(VehiclePropertyEnum.getEnum(source.getVehicleProperty()).getValue());
            if (source.getReplaceDate() != null) {
                destination.setReplaceDate(DateUtils.dateToString(source.getReplaceDate(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(ListUtils.isNotEmpty(source.getVehicleTireNoList())){
                BigDecimal sum = BigDecimal.ZERO;
                for(VehicleTireNoListResponseModel tmp :source.getVehicleTireNoList()){
                    sum = sum.add(tmp.getUnitPrice().multiply(ConverterUtils.toBigDecimal(tmp.getAmount())).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
                destination.setTotalCost(ConverterUtils.toString(sum));
                destination.setTireNoCount(source.getVehicleTireNoList().size());
            }
        }
    }
}

