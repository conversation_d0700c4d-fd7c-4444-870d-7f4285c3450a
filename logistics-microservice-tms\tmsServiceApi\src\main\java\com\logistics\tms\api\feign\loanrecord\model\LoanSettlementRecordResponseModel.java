package com.logistics.tms.api.feign.loanrecord.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/9/30 10:24
 */
@Data
public class LoanSettlementRecordResponseModel {
    @ApiModelProperty("结算记录表ID")
    private Long loanSettlementRecordId;
    @ApiModelProperty("贷款记录表ID")
    private Long loanRecordsId;
    @ApiModelProperty("结算日期")
    private Date settlementDate;
    @ApiModelProperty("结算金额")
    private BigDecimal settlementFee;
    @ApiModelProperty("剩余还款金额")
    private BigDecimal remainingRepaymentFee;
    @ApiModelProperty("总金额")
    private BigDecimal totalFee;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("创建时间")
    private Date createdTime;
}
