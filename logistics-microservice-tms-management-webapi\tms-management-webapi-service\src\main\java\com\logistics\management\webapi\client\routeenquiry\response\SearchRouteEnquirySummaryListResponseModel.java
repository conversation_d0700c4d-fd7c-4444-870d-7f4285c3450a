package com.logistics.management.webapi.client.routeenquiry.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2024/7/9 17:16
 */
@Data
public class SearchRouteEnquirySummaryListResponseModel {

    /**
     * 路线询价单地址表id
     */
    private Long routeEnquiryAddressId;

    /**
     * 竞价单号
     */
    private String orderCode;

    //发货地
    /**
     * 发货地-仓库
     */
    private String fromWarehouse;

    /**
     * 发货地-省
     */
    private String fromProvinceName;

    /**
     * 发货地-市
     */
    private String fromCityName;

    /**
     * 发货地-区
     */
    private String fromAreaName;


    //收货地
    /**
     * 收货地-省
     */
    private String toProvinceName;

    /**
     * 收货地-市
     */
    private String toCityName;

    /**
     * 收货地-区
     */
    private String toAreaName;

    /**
     * 运距
     */
    private BigDecimal distance;

    /**
     * 货物名称
     */
    private String goodsName;

    /**
     * 结算价
     */
    private BigDecimal quotePrice;

    /**
     * 结算模式
     */
    private Integer quotePriceType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 中标承运商-车主公司类型：1 公司，2 个人
     */
    private Integer companyCarrierType;

    /**
     * 中标承运商-车主公司名称
     */
    private String companyCarrierName;

    /**
     * 中标承运商-车主账号名称
     */
    private String carrierContactName;

    /**
     * 中标承运商-车主账号手机号
     */
    private String carrierContactPhone;

    /**
     * 报价生效开始时间
     */
    private Date quoteStartTime;

    /**
     * 报价生效结束时间
     */
    private Date quoteEndTime;

    /**
     * 关联合同号
     */
    private String contractCode;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdTime;

}
