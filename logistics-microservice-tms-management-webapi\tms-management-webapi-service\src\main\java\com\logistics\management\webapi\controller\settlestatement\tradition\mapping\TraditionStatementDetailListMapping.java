package com.logistics.management.webapi.controller.settlestatement.tradition.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.settlestatement.tradition.response.CarrierTraditionStatementDetailListResponseModel;
import com.logistics.management.webapi.controller.settlestatement.tradition.response.CarrierTraditionStatementDetailListResponseDto;
import com.logistics.tms.api.feign.dispatchorder.model.CarrierOrderGoodsModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/17
 */
public class TraditionStatementDetailListMapping extends MapperMapping<CarrierTraditionStatementDetailListResponseModel, CarrierTraditionStatementDetailListResponseDto> {
	@Override
	public void configure() {
		CarrierTraditionStatementDetailListResponseModel source = getSource();
		CarrierTraditionStatementDetailListResponseDto destination = getDestination();

		//车主
		if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())) {
			//个人车主:姓名+手机号
			destination.setCompanyCarrierName(source.getCarrierContactName() + FrequentMethodUtils.encryptionData(source.getCarrierContactMobile(), EncodeTypeEnum.MOBILE_PHONE));
			destination.setExportCompanyCarrierName(source.getCarrierContactName() + source.getCarrierContactMobile());
		} else {
			destination.setCompanyCarrierName(source.getCompanyCarrierName());
			destination.setExportCompanyCarrierName(source.getCompanyCarrierName());
		}
		//司机信息处理
		destination.setDriver(source.getDriverName() + " " + FrequentMethodUtils.encryptionData(source.getDriverPhone(), EncodeTypeEnum.MOBILE_PHONE));
		destination.setExportDriver(source.getDriverName() + " " + source.getDriverPhone());
		//报价类型展示label
		destination.setCarrierPriceTypeLabel(PriceTypeEnum.getEnum(source.getCarrierPriceType()).getValue());
		//提货数量
		destination.setLoadAmount(source.getLoadAmount().stripTrailingZeros().toPlainString() + GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());
		//卸货数量
		destination.setUnloadAmount(source.getUnloadAmount().stripTrailingZeros().toPlainString() + GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());
		//预提数量
		destination.setExpectAmount(source.getExpectAmount().stripTrailingZeros().toPlainString() + GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());
		//车主费用
		destination.setCarrierFee(source.getCarrierFreight().setScale(2, RoundingMode.HALF_UP).toPlainString());
		//结算费用总额
		destination.setSettlementFee(Optional.ofNullable(source.getCarrierFreight()).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP).toPlainString());

		//拼接货物名
		StringBuilder goodsNameSB = new StringBuilder();
		List<CarrierOrderGoodsModel> goodsList = source.getGoodsList();
		for (int i = 0; i < goodsList.size(); i++) {
			if (i > CommonConstant.INTEGER_ZERO) {
				goodsNameSB.append(CommonConstant.SLASH);
			}
			goodsNameSB.append(goodsList.get(i).getGoodsName());
		}
		destination.setGoodsName(goodsNameSB.toString());

		//实际结算数量,有结算表数据就取结算表数据
		BigDecimal carrierSettlementAmount = BigDecimal.ZERO;
		if (source.getSettlementAmount() != null) {
			carrierSettlementAmount = source.getSettlementAmount();
		} else {

				if (SettlementTonnageEnum.LOAD.getKey().equals(source.getCarrierSettlement())) {
					carrierSettlementAmount = source.getLoadAmount();
				} else if (SettlementTonnageEnum.UNLOAD.getKey().equals(source.getCarrierSettlement())) {
					carrierSettlementAmount = source.getUnloadAmount();
				} else if (SettlementTonnageEnum.SIGN.getKey().equals(source.getCarrierSettlement())) {
					carrierSettlementAmount = source.getSignAmount();
				} else if (SettlementTonnageEnum.EXPECT.getKey().equals(source.getCarrierSettlement())) {
					carrierSettlementAmount = source.getExpectAmount();
				}

		}
		destination.setSettlementAmountNoUnit(carrierSettlementAmount.stripTrailingZeros().toPlainString());
		destination.setSettlementAmount(carrierSettlementAmount.stripTrailingZeros().toPlainString() + GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());

		//里程数
		destination.setExpectMileage(source.getExpectMileage().stripTrailingZeros().toPlainString() + "KM");

		//提货地址
		String loadAddress = Optional.ofNullable(source.getLoadProvinceName()).orElse("") +
				Optional.ofNullable(source.getLoadCityName()).orElse("") +
				Optional.ofNullable(source.getLoadAreaName()).orElse("") +
				"【" + Optional.ofNullable(source.getLoadWarehouse()).orElse("") + "】";
		destination.setLoadWarehouse(loadAddress);
		destination.setConsignor(source.getConsignorName() + " " + FrequentMethodUtils.encryptionData(source.getConsignorMobile(), EncodeTypeEnum.MOBILE_PHONE));
		destination.setExportConsignor(source.getConsignorName() + " " + source.getConsignorMobile());

		destination.setLoadAddress(loadAddress + source.getLoadDetailAddress());

		//卸货地址
		String unloadAddress = Optional.ofNullable(source.getUnloadProvinceName()).orElse("") +
				Optional.ofNullable(source.getUnloadCityName()).orElse("") +
				Optional.ofNullable(source.getUnloadAreaName()).orElse("") +
				"【" + Optional.ofNullable(source.getUnloadWarehouse()).orElse("") + "】";
		destination.setUnloadWarehouse(unloadAddress);
		destination.setReceiver(source.getReceiverName() + " " + FrequentMethodUtils.encryptionData(source.getReceiverMobile(), EncodeTypeEnum.MOBILE_PHONE));
		destination.setExportReceiver(source.getReceiverName() + " " + source.getReceiverMobile());

		destination.setUnloadAddress(unloadAddress + source.getUnloadDetailAddress());

		//货物单位
		destination.setGoodsUnitLabel(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());

		//提货时间
		destination.setLoadTime(source.getLoadTime() != null ? DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT.format(source.getLoadTime()) : CommonConstant.BLANK_TEXT);
		//卸货时间
		destination.setUnloadTime(source.getUnloadTime() != null ? DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT.format(source.getUnloadTime()) : CommonConstant.BLANK_TEXT);
		//签收时间
		destination.setSignTime(source.getSignTime() != null ? DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT.format(source.getSignTime()) : CommonConstant.BLANK_TEXT);
	}
}
