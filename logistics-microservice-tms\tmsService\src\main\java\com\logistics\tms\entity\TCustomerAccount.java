package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2023/06/30
*/
@Data
public class TCustomerAccount extends BaseEntity {
    /**
    * 用户编号
    */
    @ApiModelProperty("用户编号")
    private String userCode;

    /**
    * 用户名称
    */
    @ApiModelProperty("用户名称")
    private String userName;

    /**
    * 用户账号（原长度50）
    */
    @ApiModelProperty("用户账号（原长度50）")
    private String userAccount;

    /**
    * 邮箱
    */
    @ApiModelProperty("邮箱")
    private String email;

    /**
    * 用户密码
    */
    @ApiModelProperty("用户密码")
    private String userPassword;

    /**
    * 盐值
    */
    @ApiModelProperty("盐值")
    private String userSalt;

    /**
    * 头像路径（原长度100）
    */
    @ApiModelProperty("头像路径（原长度100）")
    private String userHeadPath;

    /**
    * 微信唯一标识
    */
    @ApiModelProperty("微信唯一标识")
    private String openId;

    /**
    * 启用 1 禁用 0
    */
    @ApiModelProperty("启用 1 禁用 0")
    private Integer enabled;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}