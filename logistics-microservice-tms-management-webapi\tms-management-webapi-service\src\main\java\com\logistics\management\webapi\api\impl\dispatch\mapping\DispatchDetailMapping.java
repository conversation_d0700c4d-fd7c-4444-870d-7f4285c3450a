package com.logistics.management.webapi.api.impl.dispatch.mapping;


import com.logistics.management.webapi.api.feign.dispatch.dto.DemandOrderDispatchResponseDto;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.tms.api.feign.dispatch.model.DemandOrderDispatchResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2018/9/27 14:10
 */
public class DispatchDetailMapping extends MapperMapping<DemandOrderDispatchResponseModel,DemandOrderDispatchResponseDto> {
    @Override
    public void configure() {
        DemandOrderDispatchResponseModel model = getSource();
        DemandOrderDispatchResponseDto dto = getDestination();
        if (model == null) {
            return;
        }
        dto.setLoadDetailAddress(model.getLoadCityName() + model.getLoadAreaName() + model.getLoadDetailAddress());
        dto.setUnloadDetailAddress(model.getUnloadCityName() + model.getUnloadAreaName() + model.getUnloadDetailAddress());
        dto.setGoodsSize(model.getGoodsSize());
        if (GoodsUnitEnum.BY_VOLUME.getKey().equals(model.getGoodsUnit())) {
            dto.setGoodsSize(dto.getGoodsSize() + " " + model.getLength() + "*" + model.getWidth() + "*" + model.getHeight() + "mm");
        }
        dto.setNotArrangedAmount(model.getNotArrangedAmount().stripTrailingZeros().toPlainString());

        //个人车主
        if (CompanyTypeEnum.PERSONAL.getKey().equals(model.getCarrierType())) {
            dto.setCompanyCarrierName(model.getCustomerCarrierName() + " " + model.getCustomerCarrierMobile());
        }
    }
}
