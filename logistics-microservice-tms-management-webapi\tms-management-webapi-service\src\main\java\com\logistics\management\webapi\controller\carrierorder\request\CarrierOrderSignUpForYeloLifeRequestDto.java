package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 新生运单签收
 *
 * <AUTHOR>
 * @date 2022/8/16 17:41
 */
@Data
public class CarrierOrderSignUpForYeloLifeRequestDto {

    @NotBlank(message = "运单id不能为空")
    @ApiModelProperty(value = "运单ID", required = true)
    private String carrierOrderId;

    @Valid
    @ApiModelProperty(value = "货物列表", required = true)
    @NotEmpty(message = "货物列表不能为空")
    private List<SignGoodsForYeloLifeRequestDto> goodsList;

    @ApiModelProperty(value = "货主实际运费 0<一口价<=50000", required = true)
    @NotBlank(message = "货主实际运费不能为空")
    @DecimalMin(value = "0.001", message = "货主实际运费要大于0")
    @DecimalMax(value = "50000", message = "货主实际运费要小于等于50000")
    private String entrustFreightFee;

    /**
     * (3.23.0)车主运费
     */
    @ApiModelProperty(value = "车主实际运费 0<一口价<=50000", required = true)
    @NotBlank(message = "车主实际运费不能为空")
    @DecimalMin(value = "0.01", message = "车主实际运费要大于0")
    @DecimalMax(value = "50000", message = "车主实际运费要小于等于50000")
    private String carrierFreight;

    /**
     * 司机实际运费（我司必填）
     */
    @ApiModelProperty(value = "司机实际运费 0<一口价<=50000")
    private String dispatchFreightFee;
}
