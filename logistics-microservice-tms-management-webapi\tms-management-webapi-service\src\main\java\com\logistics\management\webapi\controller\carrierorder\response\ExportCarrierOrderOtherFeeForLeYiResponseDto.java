package com.logistics.management.webapi.controller.carrierorder.response;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/9/16 13:47
 */
@Data
public class ExportCarrierOrderOtherFeeForLeYiResponseDto {

    @ExcelProperty("实际日期")
    private String signTime = "";

    @ExcelProperty("需求类型")
    private String entrustType="";

    @ExcelProperty("货物名称")
    private String goodsName = "";

    @ExcelProperty("需求单号")
    private String demandOrderCode = "";

    @ExcelProperty("调度单号")
    private String dispatchOrderCode = "";

    @ExcelProperty("运单号")
    private String carrierOrderCode = "";

    @ExcelProperty("车牌号")
    private String vehicleNo = "";

    @ExcelProperty("司机姓名")
    private String driver = "";

    @ExcelProperty("临时费用（元）")
    @ApiModelProperty("临时费用")
    private String temporaryFee = "";


    @ExcelProperty("临时费用费额（元）")
    @ApiModelProperty("临时费用费额")
    private String otherFeeFreight = "";

    @ExcelProperty("合计(元)")
    private String otherFeeTotal = "";

    @ExcelProperty("短驳费(元)")
    private String shortBargeFee = "";

    @ExcelProperty("保管费(元)")
    private String storageFee = "";

    @ExcelProperty("装卸费(元)")
    private String handlingChangesFee = "";

    @ExcelProperty("压车费(元)")
    private String crushingFee = "";

    @ExcelProperty("质量处罚费(元)")
    private String qualityPenaltyFee = "";

    @ExcelProperty("其他杂费(元)")
    private String otherFee = "";

    @ExcelProperty("车主")
    private String carrierCompany = "";

    @ExcelProperty("调度人")
    private String dispatchUserName = "";

    @ExcelProperty("大区")
    private String loadRegionName="";

    @ExcelProperty("委托客户")
    private String entrustCompany = "";

    @ExcelProperty("运单生成时间")
    private String dispatchTime = "";



}
