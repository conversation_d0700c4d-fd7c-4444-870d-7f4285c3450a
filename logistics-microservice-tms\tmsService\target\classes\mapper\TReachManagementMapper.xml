<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TReachManagementMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TReachManagement">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="carrier_order_id" jdbcType="BIGINT" property="carrierOrderId" />
    <result column="carrier_order_code" jdbcType="VARCHAR" property="carrierOrderCode" />
    <result column="reach_driver_name" jdbcType="VARCHAR" property="reachDriverName" />
    <result column="reach_driver_phone" jdbcType="VARCHAR" property="reachDriverPhone" />
    <result column="terminal_head" jdbcType="VARCHAR" property="terminalHead" />
    <result column="reach_province_id" jdbcType="BIGINT" property="reachProvinceId" />
    <result column="reach_province_name" jdbcType="VARCHAR" property="reachProvinceName" />
    <result column="reach_city_id" jdbcType="BIGINT" property="reachCityId" />
    <result column="reach_city_name" jdbcType="VARCHAR" property="reachCityName" />
    <result column="reach_area_id" jdbcType="BIGINT" property="reachAreaId" />
    <result column="reach_area_name" jdbcType="VARCHAR" property="reachAreaName" />
    <result column="reach_address_detail" jdbcType="VARCHAR" property="reachAddressDetail" />
    <result column="reach_address_remark" jdbcType="VARCHAR" property="reachAddressRemark" />
    <result column="reach_longitude" jdbcType="VARCHAR" property="reachLongitude" />
    <result column="reach_latitude" jdbcType="VARCHAR" property="reachLatitude" />
    <result column="distance_deviation" jdbcType="DECIMAL" property="distanceDeviation" />
    <result column="reach_contactor" jdbcType="VARCHAR" property="reachContactor" />
    <result column="reach_telephone" jdbcType="VARCHAR" property="reachTelephone" />
    <result column="check_reach_contact" jdbcType="INTEGER" property="checkReachContact" />
    <result column="reach_time" jdbcType="TIMESTAMP" property="reachTime" />
    <result column="empty_trays_amount" jdbcType="INTEGER" property="emptyTraysAmount" />
    <result column="employ_trays_amount" jdbcType="INTEGER" property="employTraysAmount" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, carrier_order_id, carrier_order_code, reach_driver_name, reach_driver_phone, 
    terminal_head, reach_province_id, reach_province_name, reach_city_id, reach_city_name, 
    reach_area_id, reach_area_name, reach_address_detail, reach_address_remark, reach_longitude, 
    reach_latitude, distance_deviation, reach_contactor, reach_telephone, check_reach_contact, 
    reach_time, empty_trays_amount, employ_trays_amount, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_reach_management
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_reach_management
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TReachManagement">
    insert into t_reach_management (id, carrier_order_id, carrier_order_code, 
      reach_driver_name, reach_driver_phone, terminal_head, 
      reach_province_id, reach_province_name, reach_city_id, 
      reach_city_name, reach_area_id, reach_area_name, 
      reach_address_detail, reach_address_remark, 
      reach_longitude, reach_latitude, distance_deviation, 
      reach_contactor, reach_telephone, check_reach_contact, 
      reach_time, empty_trays_amount, employ_trays_amount, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{carrierOrderId,jdbcType=BIGINT}, #{carrierOrderCode,jdbcType=VARCHAR}, 
      #{reachDriverName,jdbcType=VARCHAR}, #{reachDriverPhone,jdbcType=VARCHAR}, #{terminalHead,jdbcType=VARCHAR}, 
      #{reachProvinceId,jdbcType=BIGINT}, #{reachProvinceName,jdbcType=VARCHAR}, #{reachCityId,jdbcType=BIGINT}, 
      #{reachCityName,jdbcType=VARCHAR}, #{reachAreaId,jdbcType=BIGINT}, #{reachAreaName,jdbcType=VARCHAR}, 
      #{reachAddressDetail,jdbcType=VARCHAR}, #{reachAddressRemark,jdbcType=VARCHAR}, 
      #{reachLongitude,jdbcType=VARCHAR}, #{reachLatitude,jdbcType=VARCHAR}, #{distanceDeviation,jdbcType=DECIMAL}, 
      #{reachContactor,jdbcType=VARCHAR}, #{reachTelephone,jdbcType=VARCHAR}, #{checkReachContact,jdbcType=INTEGER}, 
      #{reachTime,jdbcType=TIMESTAMP}, #{emptyTraysAmount,jdbcType=INTEGER}, #{employTraysAmount,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TReachManagement">
    insert into t_reach_management
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="carrierOrderId != null">
        carrier_order_id,
      </if>
      <if test="carrierOrderCode != null">
        carrier_order_code,
      </if>
      <if test="reachDriverName != null">
        reach_driver_name,
      </if>
      <if test="reachDriverPhone != null">
        reach_driver_phone,
      </if>
      <if test="terminalHead != null">
        terminal_head,
      </if>
      <if test="reachProvinceId != null">
        reach_province_id,
      </if>
      <if test="reachProvinceName != null">
        reach_province_name,
      </if>
      <if test="reachCityId != null">
        reach_city_id,
      </if>
      <if test="reachCityName != null">
        reach_city_name,
      </if>
      <if test="reachAreaId != null">
        reach_area_id,
      </if>
      <if test="reachAreaName != null">
        reach_area_name,
      </if>
      <if test="reachAddressDetail != null">
        reach_address_detail,
      </if>
      <if test="reachAddressRemark != null">
        reach_address_remark,
      </if>
      <if test="reachLongitude != null">
        reach_longitude,
      </if>
      <if test="reachLatitude != null">
        reach_latitude,
      </if>
      <if test="distanceDeviation != null">
        distance_deviation,
      </if>
      <if test="reachContactor != null">
        reach_contactor,
      </if>
      <if test="reachTelephone != null">
        reach_telephone,
      </if>
      <if test="checkReachContact != null">
        check_reach_contact,
      </if>
      <if test="reachTime != null">
        reach_time,
      </if>
      <if test="emptyTraysAmount != null">
        empty_trays_amount,
      </if>
      <if test="employTraysAmount != null">
        employ_trays_amount,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderId != null">
        #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null">
        #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="reachDriverName != null">
        #{reachDriverName,jdbcType=VARCHAR},
      </if>
      <if test="reachDriverPhone != null">
        #{reachDriverPhone,jdbcType=VARCHAR},
      </if>
      <if test="terminalHead != null">
        #{terminalHead,jdbcType=VARCHAR},
      </if>
      <if test="reachProvinceId != null">
        #{reachProvinceId,jdbcType=BIGINT},
      </if>
      <if test="reachProvinceName != null">
        #{reachProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="reachCityId != null">
        #{reachCityId,jdbcType=BIGINT},
      </if>
      <if test="reachCityName != null">
        #{reachCityName,jdbcType=VARCHAR},
      </if>
      <if test="reachAreaId != null">
        #{reachAreaId,jdbcType=BIGINT},
      </if>
      <if test="reachAreaName != null">
        #{reachAreaName,jdbcType=VARCHAR},
      </if>
      <if test="reachAddressDetail != null">
        #{reachAddressDetail,jdbcType=VARCHAR},
      </if>
      <if test="reachAddressRemark != null">
        #{reachAddressRemark,jdbcType=VARCHAR},
      </if>
      <if test="reachLongitude != null">
        #{reachLongitude,jdbcType=VARCHAR},
      </if>
      <if test="reachLatitude != null">
        #{reachLatitude,jdbcType=VARCHAR},
      </if>
      <if test="distanceDeviation != null">
        #{distanceDeviation,jdbcType=DECIMAL},
      </if>
      <if test="reachContactor != null">
        #{reachContactor,jdbcType=VARCHAR},
      </if>
      <if test="reachTelephone != null">
        #{reachTelephone,jdbcType=VARCHAR},
      </if>
      <if test="checkReachContact != null">
        #{checkReachContact,jdbcType=INTEGER},
      </if>
      <if test="reachTime != null">
        #{reachTime,jdbcType=TIMESTAMP},
      </if>
      <if test="emptyTraysAmount != null">
        #{emptyTraysAmount,jdbcType=INTEGER},
      </if>
      <if test="employTraysAmount != null">
        #{employTraysAmount,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TReachManagement">
    update t_reach_management
    <set>
      <if test="carrierOrderId != null">
        carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null">
        carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="reachDriverName != null">
        reach_driver_name = #{reachDriverName,jdbcType=VARCHAR},
      </if>
      <if test="reachDriverPhone != null">
        reach_driver_phone = #{reachDriverPhone,jdbcType=VARCHAR},
      </if>
      <if test="terminalHead != null">
        terminal_head = #{terminalHead,jdbcType=VARCHAR},
      </if>
      <if test="reachProvinceId != null">
        reach_province_id = #{reachProvinceId,jdbcType=BIGINT},
      </if>
      <if test="reachProvinceName != null">
        reach_province_name = #{reachProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="reachCityId != null">
        reach_city_id = #{reachCityId,jdbcType=BIGINT},
      </if>
      <if test="reachCityName != null">
        reach_city_name = #{reachCityName,jdbcType=VARCHAR},
      </if>
      <if test="reachAreaId != null">
        reach_area_id = #{reachAreaId,jdbcType=BIGINT},
      </if>
      <if test="reachAreaName != null">
        reach_area_name = #{reachAreaName,jdbcType=VARCHAR},
      </if>
      <if test="reachAddressDetail != null">
        reach_address_detail = #{reachAddressDetail,jdbcType=VARCHAR},
      </if>
      <if test="reachAddressRemark != null">
        reach_address_remark = #{reachAddressRemark,jdbcType=VARCHAR},
      </if>
      <if test="reachLongitude != null">
        reach_longitude = #{reachLongitude,jdbcType=VARCHAR},
      </if>
      <if test="reachLatitude != null">
        reach_latitude = #{reachLatitude,jdbcType=VARCHAR},
      </if>
      <if test="distanceDeviation != null">
        distance_deviation = #{distanceDeviation,jdbcType=DECIMAL},
      </if>
      <if test="reachContactor != null">
        reach_contactor = #{reachContactor,jdbcType=VARCHAR},
      </if>
      <if test="reachTelephone != null">
        reach_telephone = #{reachTelephone,jdbcType=VARCHAR},
      </if>
      <if test="checkReachContact != null">
        check_reach_contact = #{checkReachContact,jdbcType=INTEGER},
      </if>
      <if test="reachTime != null">
        reach_time = #{reachTime,jdbcType=TIMESTAMP},
      </if>
      <if test="emptyTraysAmount != null">
        empty_trays_amount = #{emptyTraysAmount,jdbcType=INTEGER},
      </if>
      <if test="employTraysAmount != null">
        employ_trays_amount = #{employTraysAmount,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TReachManagement">
    update t_reach_management
    set carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      reach_driver_name = #{reachDriverName,jdbcType=VARCHAR},
      reach_driver_phone = #{reachDriverPhone,jdbcType=VARCHAR},
      terminal_head = #{terminalHead,jdbcType=VARCHAR},
      reach_province_id = #{reachProvinceId,jdbcType=BIGINT},
      reach_province_name = #{reachProvinceName,jdbcType=VARCHAR},
      reach_city_id = #{reachCityId,jdbcType=BIGINT},
      reach_city_name = #{reachCityName,jdbcType=VARCHAR},
      reach_area_id = #{reachAreaId,jdbcType=BIGINT},
      reach_area_name = #{reachAreaName,jdbcType=VARCHAR},
      reach_address_detail = #{reachAddressDetail,jdbcType=VARCHAR},
      reach_address_remark = #{reachAddressRemark,jdbcType=VARCHAR},
      reach_longitude = #{reachLongitude,jdbcType=VARCHAR},
      reach_latitude = #{reachLatitude,jdbcType=VARCHAR},
      distance_deviation = #{distanceDeviation,jdbcType=DECIMAL},
      reach_contactor = #{reachContactor,jdbcType=VARCHAR},
      reach_telephone = #{reachTelephone,jdbcType=VARCHAR},
      check_reach_contact = #{checkReachContact,jdbcType=INTEGER},
      reach_time = #{reachTime,jdbcType=TIMESTAMP},
      empty_trays_amount = #{emptyTraysAmount,jdbcType=INTEGER},
      employ_trays_amount = #{employTraysAmount,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>