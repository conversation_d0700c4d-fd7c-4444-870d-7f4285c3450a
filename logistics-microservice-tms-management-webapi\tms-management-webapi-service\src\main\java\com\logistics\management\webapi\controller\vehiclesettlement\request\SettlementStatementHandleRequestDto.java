package com.logistics.management.webapi.controller.vehiclesettlement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author:lei.zhu
 * @date:2021/4/9 13:53
 */
@Data
public class SettlementStatementHandleRequestDto {
    @ApiModelProperty(value = "车辆运费账单id",required = true)
    @NotBlank(message = "id不能为空")
    private String vehicleSettlementId;
    @ApiModelProperty(value = "操作类型 1 无需处理  2 修改调整费用  3 重新对账",required = true)
    @NotBlank(message = "操作类型不能为空")
    private String operatorType;


    @ApiModelProperty("无需处理-理由")
    private String reason;
    @ApiModelProperty("调整费用符号：1 ‘+’， 2 ‘-’ ")
    private String adjustCostSymbol;
    @ApiModelProperty("调整费用")
    private String adjustCost;
    @ApiModelProperty("调整费用-原因")
    private String adjustRemark;

}
