package com.logistics.tms.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/9/2 16:34
 */
@Data
public class SearchCarrierOrderForParamsRequestModel {
    @ApiModelProperty("运单id")
    private String carrierOrderIds;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("项目标签：1 石化板块，2 轮胎板块，3 涂料板块，4 其他")
    private Integer projectLabel;

    @ApiModelProperty("车主id")
    private Long companyCarrierId;
    @ApiModelProperty("车主名称")
    private String companyCarrierName;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("司机")
    private String driver;

    @ApiModelProperty("提货时间-起始")
    private String loadTimeStart;
    @ApiModelProperty("提货时间-结束")
    private String loadTimeEnd;

    @ApiModelProperty(value = "提货地址")
    private String loadAddress;

    @ApiModelProperty(value = "卸货地址")
    private String unloadAddress;

    @ApiModelProperty(value = "运单生成时间 v2.43")
    private String createdTimeStart;

    @ApiModelProperty(value = "运单生成时间 v2.43")
    private String createdTimeEnd;


    @ApiModelProperty(value = "委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨 v2.43")
    private Integer demandOrderEntrustType;
}
