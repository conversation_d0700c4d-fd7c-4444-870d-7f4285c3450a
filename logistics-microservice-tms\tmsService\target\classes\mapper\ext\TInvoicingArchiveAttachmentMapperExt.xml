<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TInvoicingArchiveAttachmentMapper" >
  <select id="getInvoicingArchiveList" resultType="com.logistics.tms.controller.invoicingmanagement.response.GetInvoicingArchiveListResponseModel">
    select 
    id as invoicingArchiveId,
    image_path as relativePath
    from t_invoicing_archive_attachment
    where valid = 1
    and invoicing_id = #{invoicingId,jdbcType=BIGINT}
  </select>

  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TInvoicingArchiveAttachment" >
    <foreach collection="recordList" item="item" separator=";">
      insert into t_invoicing_archive_attachment
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.invoicingId != null" >
          invoicing_id,
        </if>
        <if test="item.imagePath != null" >
          image_path,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.invoicingId != null" >
          #{item.invoicingId,jdbcType=BIGINT},
        </if>
        <if test="item.imagePath != null" >
          #{item.imagePath,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>
</mapper>