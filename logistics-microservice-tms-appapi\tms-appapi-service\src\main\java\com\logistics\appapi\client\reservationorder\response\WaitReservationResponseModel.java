package com.logistics.appapi.client.reservationorder.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/14 14:32
 */
@Data
public class WaitReservationResponseModel {

    /**
     * 预约类型：1 提货，2 卸货
     */
    private Integer reservationType;

    /**
     * 单子数量
     */
    private Integer orderCount;

    /**
     * 运单列表
     */
    private List<WaitReservationCarrierOrderListResponseModel> orderList=new ArrayList<>();
}
