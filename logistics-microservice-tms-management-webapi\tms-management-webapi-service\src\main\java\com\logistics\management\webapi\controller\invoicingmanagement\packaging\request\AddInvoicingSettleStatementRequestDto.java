package com.logistics.management.webapi.controller.invoicingmanagement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/20 9:19
 */
@Data
public class AddInvoicingSettleStatementRequestDto {
    @ApiModelProperty(value = "发票管理id",required = true)
    @NotBlank(message = "发票管理id不能为空")
    private String invoicingId;

    @ApiModelProperty(value = "对账单id",required = true)
    @NotEmpty(message = "请选择对账单")
    @Size(max = 50, message = "请选择对账单，最多50条")
    private List<String> settleStatementIdList;
}
