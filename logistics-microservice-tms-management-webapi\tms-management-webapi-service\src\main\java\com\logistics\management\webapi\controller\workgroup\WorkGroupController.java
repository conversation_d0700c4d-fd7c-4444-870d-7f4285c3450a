package com.logistics.management.webapi.controller.workgroup;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.client.workgroup.WorkGroupClient;
import com.logistics.management.webapi.client.workgroup.request.*;
import com.logistics.management.webapi.client.workgroup.response.SearchWorkGroupListResponseModel;
import com.logistics.management.webapi.client.workgroup.response.WorkGroupDetailResponseModel;
import com.logistics.management.webapi.client.workgroup.response.WorkGroupNodeResponseModel;
import com.logistics.management.webapi.controller.workgroup.mapping.GetNodeInfoMapping;
import com.logistics.management.webapi.controller.workgroup.mapping.WorkGroupDetailMapping;
import com.logistics.management.webapi.controller.workgroup.mapping.WorkGroupSearchListMapping;
import com.logistics.management.webapi.controller.workgroup.request.*;
import com.logistics.management.webapi.controller.workgroup.response.GetFieldListResponseDto;
import com.logistics.management.webapi.controller.workgroup.response.SearchWorkGroupListResponseDto;
import com.logistics.management.webapi.controller.workgroup.response.WorkGroupDetailResponseDto;
import com.logistics.management.webapi.controller.workgroup.response.WorkGroupNodeResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jodd.util.StringUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2023/12/21 14:25
 */
@RestController
@Api(value = "智能推送配置", tags = "智能推送配置")
@RequestMapping(value = "/api/workGroup")
public class WorkGroupController {

    @Resource
    private WorkGroupClient workGroupClient;

    @PostMapping(value = "/searchList")
    @ApiOperation(value = "列表", tags = "3.14.0")
    public Result<PageInfo<SearchWorkGroupListResponseDto>> searchList(@RequestBody SearchWorkGroupListRequestDto requestDto) {
        Result<PageInfo<SearchWorkGroupListResponseModel>> result = workGroupClient.searchList(MapperUtils.mapper(requestDto, SearchWorkGroupListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SearchWorkGroupListResponseDto> list = MapperUtils.mapper(pageInfo.getList(), SearchWorkGroupListResponseDto.class, new WorkGroupSearchListMapping());
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    @PostMapping(value = "/workGroupEnable")
    @ApiOperation(value = "列表-禁用/启用", tags = "3.14.0")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> workGroupEnable(@RequestBody @Valid WorkGroupEnableRequestDto requestDto) {
        return workGroupClient.workGroupEnable(MapperUtils.mapper(requestDto, WorkGroupEnableRequestModel.class));
    }

    @PostMapping(value = "/delWorkGroup")
    @ApiOperation(value = "列表-删除", tags = "3.14.0")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> delWorkGroup(@RequestBody @Valid WorkGroupIdRequestDto requestDto) {
        return workGroupClient.delWorkGroup(MapperUtils.mapper(requestDto, WorkGroupIdRequestModel.class));
    }

    @PostMapping(value = "/getDetail")
    @ApiOperation(value = "推送配置详情", tags = "3.15.2")
    public Result<WorkGroupDetailResponseDto> getDetail(@RequestBody @Valid WorkGroupIdRequestDto requestDto) {
        Result<WorkGroupDetailResponseModel> result = workGroupClient.getDetail(MapperUtils.mapper(requestDto, WorkGroupIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), WorkGroupDetailResponseDto.class, new WorkGroupDetailMapping()));
    }

    @PostMapping(value = "/addEditWorkGroup")
    @ApiOperation(value = "新增/编辑推送配置", tags = "3.15.2")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addEditWorkGroup(@RequestBody @Valid AddEditWorkGroupRequestDto requestDto) {
        //负责人与参与人不能是同一人
        if (requestDto.getGroupOwnerId().strip().equals(requestDto.getParticipantId().strip())) {
            throw new BizException(ManagementWebApiExceptionEnum.WORK_GROUP_USER_ERROR);
        }

        //已有群聊，则机器人编码必填
        if (WorkGroupSourceEnum.EXIST.getKey().toString().equals(requestDto.getWorkGroupSource())){
            if (StringUtils.isBlank(requestDto.getWorkGroupCode())
                    || requestDto.getWorkGroupCode().length() < CommonConstant.INTEGER_ONE
                    || requestDto.getWorkGroupCode().length() > CommonConstant.INTEGER_THREE_HUNDRED) {
                throw new BizException(ManagementWebApiExceptionEnum.WORK_GROUP_CODE_EMPTY);
            }
        }else if (WorkGroupSourceEnum.CREATE.getKey().toString().equals(requestDto.getWorkGroupSource())){
            requestDto.setWorkGroupCode(null);
        }else{
            throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
        }

        //区域
        if (MatchingFieldEnum.AREA.getKey().toString().equals(requestDto.getMatchingField())){
            //需维护配置区域
            if (ListUtils.isEmpty(requestDto.getDistrictList())){
                throw new BizException(ManagementWebApiExceptionEnum.DISTRICT_EMPTY);
            }
            requestDto.setConfigWarehouse("");
        }
        //仓库
        else if (MatchingFieldEnum.WAREHOUSE.getKey().toString().equals(requestDto.getMatchingField())){
            //需维护配置仓库
            if (StringUtils.isBlank(requestDto.getConfigWarehouse())
                    || requestDto.getConfigWarehouse().length() < CommonConstant.INTEGER_ONE
                    || requestDto.getConfigWarehouse().length() > CommonConstant.INTEGER_ONE_HUNDRED){
                throw new BizException(ManagementWebApiExceptionEnum.CONFIG_WAREHOUSE_EMPTY);
            }
            requestDto.setDistrictList(null);
        }else {
            throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
        }
        return workGroupClient.addEditWorkGroup(MapperUtils.mapper(requestDto, AddEditWorkGroupRequestModel.class));
    }

    @PostMapping(value = "/addEditNode")
    @ApiOperation(value = "新增/编辑配置节点信息 v3.18.0", tags = "3.18.0")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addEditNode(@RequestBody @Valid AddEditWorkGroupNodeRequestDto requestDto) {
        //入参校验
        WorkGroupOrderTypeEnum orderTypeEnum;
        WorkGroupOrderNodeEnum orderNodeEnum;
        WorkGroupAmountTypeEnum amountTypeEnum;
        List<String> demandOrderFieldList = Arrays.stream(WorkGroupDemandOrderFieldEnum.values()).map(WorkGroupDemandOrderFieldEnum::getField).collect(Collectors.toList());
        List<String> carrierOrderFieldList = Arrays.stream(WorkGroupCarrierOrderFieldEnum.values()).map(WorkGroupCarrierOrderFieldEnum::getField).collect(Collectors.toList());
        Set<String> reqSoleNodeKeySet = new HashSet<>();
        for (AddEditWorkGroupNodeListRequestDto nodeDto : requestDto.getNodeList()) {
            //校验业务单据
            orderTypeEnum = WorkGroupOrderTypeEnum.getEnumByKeyStr(nodeDto.getOrderType());
            if (WorkGroupOrderTypeEnum.DEFAULT.equals(orderTypeEnum)){
                throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
            }

            //校验业务节点
            orderNodeEnum = WorkGroupOrderNodeEnum.getEnumByKeyStr(nodeDto.getOrderNode());
            if (WorkGroupOrderNodeEnum.DEFAULT.equals(orderNodeEnum)){
                throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
            }
            //校验业务节点所属业务单据与选择的业务单据是否匹配
            if (!orderTypeEnum.equals(orderNodeEnum.getOrderTypeEnum())){
                throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
            }

            //校验取值要求
            amountTypeEnum = WorkGroupAmountTypeEnum.getEnumByKeyStr(nodeDto.getAmountType());
            if (WorkGroupAmountTypeEnum.DEFAULT.equals(amountTypeEnum)){
                throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
            }
            //校验取值要求所属业务单据与选择的业务单据是否匹配
            if (!orderTypeEnum.equals(amountTypeEnum.getOrderTypeEnum())){
                throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
            }

            //需求单
            if (WorkGroupOrderTypeEnum.DEMAND_ORDER.equals(orderTypeEnum)){
                //下单
                if (WorkGroupOrderNodeEnum.DEMAND_ORDER_CREATE.getKey().toString().equals(nodeDto.getOrderNode())) {
                    //时间要求必填
                    if (StringUtil.isBlank(nodeDto.getTimeRequire())) {
                        throw new BizException(ManagementWebApiExceptionEnum.WORK_GROUP_TIME_REQUIRE_IS_NULL);
                    }
                }
                //其他节点，无时间要求字段
                else{
                    nodeDto.setTimeRequire(null);
                }

                //校验信息模板字段
                if (nodeDto.getFieldList().stream().anyMatch(field -> !demandOrderFieldList.contains(field))) {
                    throw new BizException(ManagementWebApiExceptionEnum.WORK_GROUP_ORDER_FIELD_ERROR);
                }
            }
            //运单
            else{
                //运单，无时间要求字段
                nodeDto.setTimeRequire(null);

                //校验信息模板字段
                if (nodeDto.getFieldList().stream().anyMatch(field -> !carrierOrderFieldList.contains(field))) {
                    throw new BizException(ManagementWebApiExceptionEnum.WORK_GROUP_ORDER_FIELD_ERROR);
                }
            }

            //单据类型+单据节点为唯一
            reqSoleNodeKeySet.add(nodeDto.getOrderType() + CommonConstant.MINUS_SYMBOL + nodeDto.getOrderNode());
        }

        //节点重复数据校验
        if (reqSoleNodeKeySet.size() != requestDto.getNodeList().size()) {
            throw new BizException(ManagementWebApiExceptionEnum.WORK_GROUP_NODE_REPETITION);
        }

        return workGroupClient.addEditNode(MapperUtils.mapper(requestDto, AddEditWorkGroupNodeRequestModel.class));
    }

    @PostMapping(value = "/getNodeInfo")
    @ApiOperation(value = "配置节点信息 v3.18.0", tags = "3.18.0")
    public Result<WorkGroupNodeResponseDto> getNodeInfo(@RequestBody @Valid WorkGroupIdRequestDto requestDto) {
        Result<WorkGroupNodeResponseModel> result = workGroupClient.getNodeInfo(MapperUtils.mapper(requestDto, WorkGroupIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), WorkGroupNodeResponseDto.class, new GetNodeInfoMapping()));
    }

    @PostMapping(value = "/getFieldList")
    @ApiOperation(value = "信息模板列表", tags = "3.14.0")
    public Result<List<GetFieldListResponseDto>> getFieldList(@RequestBody @Valid GetFieldListRequestDto requestDto) {
        List<GetFieldListResponseDto> responseDtoList = new ArrayList<>();
        if (WorkGroupOrderTypeEnum.DEMAND_ORDER.getKey().toString().equals(requestDto.getOrderType())) {
            for (WorkGroupDemandOrderFieldEnum enumItem : WorkGroupDemandOrderFieldEnum.values()) {
                responseDtoList.add(new GetFieldListResponseDto()
                        .setField(enumItem.getField())
                        .setFieldLabel(enumItem.getFieldLabel())
                        .setFieldValue(enumItem.getFieldValue()));
            }
        } else if (WorkGroupOrderTypeEnum.CARRIER_ORDER.getKey().toString().equals(requestDto.getOrderType())) {
            for (WorkGroupCarrierOrderFieldEnum enumItem : WorkGroupCarrierOrderFieldEnum.values()) {
                responseDtoList.add(new GetFieldListResponseDto()
                        .setField(enumItem.getField())
                        .setFieldLabel(enumItem.getFieldLabel())
                        .setFieldValue(enumItem.getFieldValue()));
            }
        }
        return Result.success(responseDtoList);
    }
}
