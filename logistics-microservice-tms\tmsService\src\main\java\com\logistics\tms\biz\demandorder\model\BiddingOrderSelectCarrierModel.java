package com.logistics.tms.biz.demandorder.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/4/28 14:45
 */
@Data
public class BiddingOrderSelectCarrierModel {

    /**
     * 竞价单下需求单信息
     */
    @ApiModelProperty("竞价单下需求单信息")
    private List<BiddingOrderSelectCarrierDemandModel> demandOrderList;

    /**
     * 选择的车主id
     */
    @ApiModelProperty("选择的车主id")
    private Long companyCarrierId;

    /**
     * 选择的车主是否我司 1:我司,2:其他车主
     */
    @ApiModelProperty("选择的车主是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;

    /**
     * 车长id
     */
    @ApiModelProperty("车长id")
    private Long vehicleLengthId;

    /**
     * 车长（米）
     */
    @ApiModelProperty("车长（米）")
    private BigDecimal vehicleLength;


}
