package com.logistics.appapi.client.vehiclesettlement.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.vehiclesettlement.VehicleSettlementClient;
import com.logistics.appapi.client.vehiclesettlement.request.DriverReconciliationConfirmRequestModel;
import com.logistics.appapi.client.vehiclesettlement.request.SearchDriverReconciliationListRequestModel;
import com.logistics.appapi.client.vehiclesettlement.request.VehicleSettlementIdRequestModel;
import com.logistics.appapi.client.vehiclesettlement.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/2/22 10:36
 */
@Component
public class VehicleSettlementClientHystrix implements VehicleSettlementClient {
    @Override
    public Result<PageInfo<SearchDriverReconciliationListResponseModel>> searchDriverReconciliationList(SearchDriverReconciliationListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<DriverReconciliationListCountResponseModel> driverReconciliationListCount(SearchDriverReconciliationListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<DriverReconciliationDetailResponseModel> driverReconciliationDetail(VehicleSettlementIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result driverReconciliationConfirm(DriverReconciliationConfirmRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<DriverReconciliationConfirmDetailResponseModel> driverReconciliationConfirmDetail(VehicleSettlementIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ReconciliationCarrierOrderDetailResponseModel> driverReconciliationCarrierOrder(VehicleSettlementIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<ReconciliationBillingRecordsResponseModel>> driverReconciliationBillingRecords(VehicleSettlementIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ReconciliationTireDetailResponseModel> driverReconciliationTire(VehicleSettlementIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ReconciliationOilFilledDetailResponseModel> driverReconciliationOilFilled(VehicleSettlementIdRequestModel requestModel) {
        return Result.timeout();
    }
}
