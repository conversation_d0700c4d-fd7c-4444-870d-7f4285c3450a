<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TParkingFeeMapper" >
  <select id="getParkingFeeList" resultType="com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeListResponseModel">
    select
    tpf.id                  as parkingFeeId,
    tpf.status              as status,
    tpf.vehicle_id          as vehicleId,
    tpf.vehicle_no          as vehicleNo,
    tpf.vehicle_property    as vehicleProperty,
    tpf.staff_id            as staffId,
    tpf.name                as name,
    tpf.mobile              as mobile,
    tpf.cooperation_company as cooperationCompany,
    tpf.start_date          as startDate,
    tpf.end_date            as endDate,
    tpf.finish_date         as finishDate,
    tpf.parking_fee         as parkingFee,
    tpf.cooperation_period  as cooperationPeriod,
    tpf.cooperation_status  as cooperationStatus,
    tpf.remark              as remark,
    tpf.created_by          as createdBy,
    tpf.created_time        as createdTime,
    tpf.last_modified_by    as lastModifiedBy,
    tpf.last_modified_time  as lastModifiedTime
    from t_parking_fee tpf
    where tpf.valid = 1
    <if test="condition.status != null">
      <choose>
        <when test="condition.status == -1">
          and cooperation_status = 3
        </when>
        <otherwise>
          and status = #{condition.status,jdbcType=INTEGER} and cooperation_status != 3
        </otherwise>
      </choose>
    </if>

    <if test="condition.vehicleNo != null and condition.vehicleNo != ''">
        and instr(tpf.vehicle_no,#{condition.vehicleNo,jdbcType = VARCHAR})
    </if>

    <if test="condition.driverInfo!=null and condition.driverInfo!=''">
        and(instr(tpf.name,#{condition.driverInfo,jdbcType = VARCHAR}) or instr(tpf.mobile,#{condition.driverInfo,jdbcType = VARCHAR}))
    </if>

    <if test="condition.cooperationCompany!=null and condition.cooperationCompany!=''">
        and instr(tpf.cooperation_company,#{condition.cooperationCompany,jdbcType = VARCHAR})
    </if>
    <if test="condition.parkingFeeIds!=null and condition.parkingFeeIds!=''">
      and tpf.id in (${condition.parkingFeeIds})
    </if>
    <if test="condition.vehicleProperty != null">
      and tpf.vehicle_property = #{condition.vehicleProperty,jdbcType=INTEGER}
    </if>
    order by tpf.created_time desc,tpf.id desc
  </select>

  <select id="getSummary" resultType="com.logistics.tms.api.feign.parkingfee.dto.SummaryParkingFeeResponseModel">
    select
    ifnull(count(0),0) as allCount,
    ifnull(sum(if(tpf.status = 0 and tpf.cooperation_status != 3,1,0)),0) as waitCount,
    ifnull(sum(if(tpf.status = 1 and tpf.cooperation_status != 3,1,0)),0) as partCount,
    ifnull(sum(if(tpf.status = 2 and tpf.cooperation_status != 3,1,0)),0) as hasCount,
    ifnull(sum(if(tpf.cooperation_status = 3,1,0)),0) as terminateCount
    from t_parking_fee tpf
    where tpf.valid = 1
    <if test="condition.vehicleNo != null and condition.vehicleNo != ''">
      and instr(tpf.vehicle_no,#{condition.vehicleNo,jdbcType = VARCHAR})
    </if>

    <if test="condition.driverInfo!=null and condition.driverInfo!=''">
      and( instr(tpf.name,#{condition.driverInfo,jdbcType = VARCHAR}) or instr(tpf.mobile,#{condition.driverInfo,jdbcType = VARCHAR}))
    </if>

    <if test="condition.cooperationCompany!=null and condition.cooperationCompany!=''">
      and instr(tpf.cooperation_company,#{condition.cooperationCompany,jdbcType = VARCHAR})
    </if>
    <if test="condition.vehicleProperty != null">
      and tpf.vehicle_property = #{condition.vehicleProperty,jdbcType=INTEGER}
    </if>
  </select>

  <select id="getDetailById" resultType="com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeDetailResponseModel">
    select
    tpf.id as parkingFeeId,
    tpf.status as status,
    tpf.vehicle_id as vehicleId,
    tpf.vehicle_no as vehicleNo,
    tpf.staff_id as staffId,
    tpf.name as name,
    tpf.mobile as mobile,
    tpf.cooperation_company as cooperationCompany,
    tpf.start_date as startDate,
    tpf.end_date as endDate,
    tpf.finish_date as finishDate,
    tpf.parking_fee as parkingFee,
    tpf.cooperation_period as cooperationPeriod,
    tpf.cooperation_status as cooperationStatus,
    tpf.remark as remark,
    tpf.created_by as createdBy,
    tpf.created_time as createdTime,
    tpf.last_modified_by as lastModifiedBy,
    tpf.last_modified_time as lastModifiedTime
    from t_parking_fee tpf
    where valid = 1
    and tpf.id = #{parkingFeeId,jdbcType = BIGINT}
  </select>

  <select id="getByVehicleStartDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_parking_fee
    where valid = 1
    and vehicle_id = #{vehicleId,jdbcType=BIGINT}
    and finish_date >= #{startDate,jdbcType=TIMESTAMP}
    and start_date &lt;= #{finishDate,jdbcType=TIMESTAMP}
  </select>

    <select id="getCurrentDeductingByIdForSettlement" resultType="com.logistics.tms.controller.vehiclesettlement.response.GetParkingFeeByVehicleIdResponseModel">
        select
        tpf.id as parkingFeeId,
        tpf.status,
        tpf.parking_fee as parkingFee,
        tpf.cooperation_period as cooperationPeriod,
        tpf.finish_date as finishDate,
        tdh.deducting_month as deductingMonth,
        tdh.deducting_fee as deductingFee,
        tdh.remaining_deducting_fee as remainingDeductingFee
        from t_parking_fee tpf
        left join (select object_id,deducting_month,deducting_fee,remaining_deducting_fee from t_deducting_history
        where valid = 1 and object_type = 2 and object_id = #{id,jdbcType=BIGINT} and deducting_month &lt;= #{deductingMonth,jdbcType=VARCHAR}
        order by deducting_month desc,id desc limit 1) tdh on tdh.object_id = tpf.id
        where tpf.valid = 1
        and tpf.id = #{id,jdbcType=BIGINT}
    </select>

  <select id="getNotTerminal" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_parking_fee
    where valid = 1
    and cooperation_status != 3
  </select>

  <update id="batchUpdate" parameterType="com.logistics.tms.entity.TParkingFee" >
    <foreach collection="list" item="item" separator=";">
      update t_parking_fee
      <set >
        <if test="item.status != null" >
          status = #{item.status,jdbcType=INTEGER},
        </if>
        <if test="item.vehicleId != null" >
          vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
        </if>
        <if test="item.vehicleProperty != null" >
          vehicle_property = #{item.vehicleProperty,jdbcType=INTEGER},
        </if>
        <if test="item.vehicleNo != null" >
          vehicle_no = #{item.vehicleNo,jdbcType=VARCHAR},
        </if>
        <if test="item.staffId != null" >
          staff_id = #{item.staffId,jdbcType=BIGINT},
        </if>
        <if test="item.name != null" >
          name = #{item.name,jdbcType=VARCHAR},
        </if>
        <if test="item.mobile != null" >
          mobile = #{item.mobile,jdbcType=VARCHAR},
        </if>
        <if test="item.cooperationCompany != null" >
          cooperation_company = #{item.cooperationCompany,jdbcType=VARCHAR},
        </if>
        <if test="item.startDate != null" >
          start_date = #{item.startDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.endDate != null" >
          end_date = #{item.endDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.finishDate != null" >
          finish_date = #{item.finishDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.parkingFee != null" >
          parking_fee = #{item.parkingFee,jdbcType=DECIMAL},
        </if>
        <if test="item.cooperationPeriod != null" >
          cooperation_period = #{item.cooperationPeriod,jdbcType=INTEGER},
        </if>
        <if test="item.cooperationStatus != null" >
          cooperation_status = #{item.cooperationStatus,jdbcType=INTEGER},
        </if>
        <if test="item.remark != null" >
          remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="getVehicleBySettlementMonth" resultType="com.logistics.tms.controller.vehiclesettlement.response.GetVehicleBySettlementMonthModel">
    select
    vehicle_id as vehicleId,
    max(id) as objectId
    from t_parking_fee
    where valid = 1
    and status != 2
    and date_format(start_date,'%Y-%m') &lt;= #{settlementMonth,jdbcType=VARCHAR}
    and date_format(finish_date,'%Y-%m') >= #{settlementMonth,jdbcType=VARCHAR}
    and date_format(created_time,'%Y-%m') &lt;= #{settlementMonth,jdbcType=VARCHAR}
    GROUP BY vehicle_id
  </select>
  <select id="getById" resultType="com.logistics.tms.controller.vehiclesettlement.response.GetParkingFeeByVehicleIdResponseModel">
    select
      tpf.id as parkingFeeId,
      tpf.status,
      tpf.parking_fee as parkingFee,
      tpf.cooperation_period as cooperationPeriod,
      tpf.finish_date as finishDate,
      tdh.deducting_month as deductingMonth,
      tdh.deducting_fee as deductingFee,
      tdh.remaining_deducting_fee as remainingDeductingFee
    from t_parking_fee tpf
    left join t_deducting_history tdh on tdh.object_type = 2 and tdh.object_id = tpf.id  and tdh.valid = 1
    where tpf.valid = 1
    and tpf.id = #{id,jdbcType=BIGINT}
    order by tdh.created_time desc
  </select>
</mapper>