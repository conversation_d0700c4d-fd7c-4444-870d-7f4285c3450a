package com.logistics.management.webapi.controller.drivercostapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class DeductionsCostApplyRequestModel {

    @ApiModelProperty(value = "司机ID")
    private Long driverId;

    @ApiModelProperty(value = "备用金余额")
    private BigDecimal reserveBalance;

    @ApiModelProperty(value = "扣款金额")
    private BigDecimal deductionAmount;

    @ApiModelProperty(value = "发生时间")
    private Date occurrenceTime;

    @ApiModelProperty(value = "费用依据")
    private List<String> costBasisImagePaths;

    @ApiModelProperty(value = "申请说明")
    private String applyRemark;
}
