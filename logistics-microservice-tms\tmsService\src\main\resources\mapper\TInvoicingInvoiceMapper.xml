<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TInvoicingInvoiceMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TInvoicingInvoice" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="invoicing_id" property="invoicingId" jdbcType="BIGINT" />
    <result column="invoice_type" property="invoiceType" jdbcType="INTEGER" />
    <result column="invoice_code" property="invoiceCode" jdbcType="VARCHAR" />
    <result column="invoice_num" property="invoiceNum" jdbcType="VARCHAR" />
    <result column="invoice_date" property="invoiceDate" jdbcType="TIMESTAMP" />
    <result column="invoice_amount" property="invoiceAmount" jdbcType="DECIMAL" />
    <result column="tax_rate" property="taxRate" jdbcType="DECIMAL" />
    <result column="total_tax_and_price" property="totalTaxAndPrice" jdbcType="DECIMAL" />
    <result column="invoice_picture" property="invoicePicture" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, invoicing_id, invoice_type, invoice_code, invoice_num, invoice_date, invoice_amount, 
    tax_rate, total_tax_and_price, invoice_picture, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_invoicing_invoice
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_invoicing_invoice
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TInvoicingInvoice" >
    insert into t_invoicing_invoice (id, invoicing_id, invoice_type, 
      invoice_code, invoice_num, invoice_date, 
      invoice_amount, tax_rate, total_tax_and_price, 
      invoice_picture, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{invoicingId,jdbcType=BIGINT}, #{invoiceType,jdbcType=INTEGER}, 
      #{invoiceCode,jdbcType=VARCHAR}, #{invoiceNum,jdbcType=VARCHAR}, #{invoiceDate,jdbcType=TIMESTAMP}, 
      #{invoiceAmount,jdbcType=DECIMAL}, #{taxRate,jdbcType=DECIMAL}, #{totalTaxAndPrice,jdbcType=DECIMAL}, 
      #{invoicePicture,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TInvoicingInvoice" >
    insert into t_invoicing_invoice
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="invoicingId != null" >
        invoicing_id,
      </if>
      <if test="invoiceType != null" >
        invoice_type,
      </if>
      <if test="invoiceCode != null" >
        invoice_code,
      </if>
      <if test="invoiceNum != null" >
        invoice_num,
      </if>
      <if test="invoiceDate != null" >
        invoice_date,
      </if>
      <if test="invoiceAmount != null" >
        invoice_amount,
      </if>
      <if test="taxRate != null" >
        tax_rate,
      </if>
      <if test="totalTaxAndPrice != null" >
        total_tax_and_price,
      </if>
      <if test="invoicePicture != null" >
        invoice_picture,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="invoicingId != null" >
        #{invoicingId,jdbcType=BIGINT},
      </if>
      <if test="invoiceType != null" >
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="invoiceCode != null" >
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNum != null" >
        #{invoiceNum,jdbcType=VARCHAR},
      </if>
      <if test="invoiceDate != null" >
        #{invoiceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invoiceAmount != null" >
        #{invoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null" >
        #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="totalTaxAndPrice != null" >
        #{totalTaxAndPrice,jdbcType=DECIMAL},
      </if>
      <if test="invoicePicture != null" >
        #{invoicePicture,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TInvoicingInvoice" >
    update t_invoicing_invoice
    <set >
      <if test="invoicingId != null" >
        invoicing_id = #{invoicingId,jdbcType=BIGINT},
      </if>
      <if test="invoiceType != null" >
        invoice_type = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="invoiceCode != null" >
        invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNum != null" >
        invoice_num = #{invoiceNum,jdbcType=VARCHAR},
      </if>
      <if test="invoiceDate != null" >
        invoice_date = #{invoiceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invoiceAmount != null" >
        invoice_amount = #{invoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null" >
        tax_rate = #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="totalTaxAndPrice != null" >
        total_tax_and_price = #{totalTaxAndPrice,jdbcType=DECIMAL},
      </if>
      <if test="invoicePicture != null" >
        invoice_picture = #{invoicePicture,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TInvoicingInvoice" >
    update t_invoicing_invoice
    set invoicing_id = #{invoicingId,jdbcType=BIGINT},
      invoice_type = #{invoiceType,jdbcType=INTEGER},
      invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      invoice_num = #{invoiceNum,jdbcType=VARCHAR},
      invoice_date = #{invoiceDate,jdbcType=TIMESTAMP},
      invoice_amount = #{invoiceAmount,jdbcType=DECIMAL},
      tax_rate = #{taxRate,jdbcType=DECIMAL},
      total_tax_and_price = #{totalTaxAndPrice,jdbcType=DECIMAL},
      invoice_picture = #{invoicePicture,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>