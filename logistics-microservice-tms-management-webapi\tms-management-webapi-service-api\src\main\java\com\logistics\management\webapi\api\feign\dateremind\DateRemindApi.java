package com.logistics.management.webapi.api.feign.dateremind;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.dateremind.dto.*;
import com.logistics.management.webapi.api.feign.dateremind.hystrix.DateRemindApiHystrix;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * @Author: sj
 * @Date: 2019/5/31 8:46
 */
@Api(value = "API-DateRemindApi-配置中心-日期提醒设置")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = DateRemindApiHystrix.class)
public interface DateRemindApi {

    @ApiOperation(value = "日期提醒列表")
    @PostMapping(value = "/api/dateRemind/list")
    Result<PageInfo<DateRemindListResponseDto>> searchDateRemindList(@RequestBody DateRemindListRequestDto requestDto);

    @ApiOperation(value = "日期提醒详情")
    @PostMapping(value = "/api/dateRemind/detail")
    Result<DateRemindDetailResponseDto> dateRemindDetail(@RequestBody @Valid DateRemindDetailRequestDto responseDto);

    @ApiOperation(value = "日期提醒保存/修改")
    @PostMapping(value = "/api/dateRemind/saveOrModify")
    Result<Boolean> saveOrModifyDateRemind(@RequestBody @Valid SaveOrModifyDateRemindRequestDto requestDto);

    @ApiOperation(value = "日期统一提醒")
    @PostMapping(value = "/api/dateRemind/unifiedRemind")
    Result<Boolean> unifiedDateRemind(@RequestBody @Valid UnifiedDateRemindRequestDto remindRequestDto);

    @ApiOperation(value = "导出")
    @GetMapping(value = "/api/dateRemind/export")
    void export(DateRemindListRequestDto requestDto, HttpServletResponse response);
}
