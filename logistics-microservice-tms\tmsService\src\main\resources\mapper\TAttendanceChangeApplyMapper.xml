<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TAttendanceChangeApplyMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TAttendanceChangeApply" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="attendance_record_id" property="attendanceRecordId" jdbcType="BIGINT" />
    <result column="change_type" property="changeType" jdbcType="INTEGER" />
    <result column="change_punch_time" property="changePunchTime" jdbcType="TIMESTAMP" />
    <result column="change_reason" property="changeReason" jdbcType="VARCHAR" />
    <result column="audit_status" property="auditStatus" jdbcType="INTEGER" />
    <result column="auditor_name" property="auditorName" jdbcType="VARCHAR" />
    <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, attendance_record_id, change_type, change_punch_time, change_reason, audit_status, 
    auditor_name, audit_time, remark, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_attendance_change_apply
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_attendance_change_apply
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TAttendanceChangeApply" >
    insert into t_attendance_change_apply (id, attendance_record_id, change_type, 
      change_punch_time, change_reason, audit_status, 
      auditor_name, audit_time, remark, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{attendanceRecordId,jdbcType=BIGINT}, #{changeType,jdbcType=INTEGER}, 
      #{changePunchTime,jdbcType=TIMESTAMP}, #{changeReason,jdbcType=VARCHAR}, #{auditStatus,jdbcType=INTEGER}, 
      #{auditorName,jdbcType=VARCHAR}, #{auditTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TAttendanceChangeApply" >
    insert into t_attendance_change_apply
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="attendanceRecordId != null" >
        attendance_record_id,
      </if>
      <if test="changeType != null" >
        change_type,
      </if>
      <if test="changePunchTime != null" >
        change_punch_time,
      </if>
      <if test="changeReason != null" >
        change_reason,
      </if>
      <if test="auditStatus != null" >
        audit_status,
      </if>
      <if test="auditorName != null" >
        auditor_name,
      </if>
      <if test="auditTime != null" >
        audit_time,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="attendanceRecordId != null" >
        #{attendanceRecordId,jdbcType=BIGINT},
      </if>
      <if test="changeType != null" >
        #{changeType,jdbcType=INTEGER},
      </if>
      <if test="changePunchTime != null" >
        #{changePunchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="changeReason != null" >
        #{changeReason,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null" >
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditorName != null" >
        #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null" >
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TAttendanceChangeApply" >
    update t_attendance_change_apply
    <set >
      <if test="attendanceRecordId != null" >
        attendance_record_id = #{attendanceRecordId,jdbcType=BIGINT},
      </if>
      <if test="changeType != null" >
        change_type = #{changeType,jdbcType=INTEGER},
      </if>
      <if test="changePunchTime != null" >
        change_punch_time = #{changePunchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="changeReason != null" >
        change_reason = #{changeReason,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null" >
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditorName != null" >
        auditor_name = #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null" >
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TAttendanceChangeApply" >
    update t_attendance_change_apply
    set attendance_record_id = #{attendanceRecordId,jdbcType=BIGINT},
      change_type = #{changeType,jdbcType=INTEGER},
      change_punch_time = #{changePunchTime,jdbcType=TIMESTAMP},
      change_reason = #{changeReason,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      auditor_name = #{auditorName,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>