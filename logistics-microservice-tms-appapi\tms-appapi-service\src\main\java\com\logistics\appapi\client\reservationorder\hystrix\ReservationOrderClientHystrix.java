package com.logistics.appapi.client.reservationorder.hystrix;

import com.logistics.appapi.client.reservationorder.ReservationOrderClient;
import com.logistics.appapi.client.reservationorder.request.*;
import com.logistics.appapi.client.reservationorder.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import javax.validation.Valid;

/**
 * @author: wjf
 * @date: 2024/8/15 9:04
 */
@Component
public class ReservationOrderClientHystrix implements ReservationOrderClient {
    @Override
    public Result<ReservationOrderSummaryResponseModel> summary() {
        return Result.timeout();
    }

    @Override
    public Result<WaitReservationResponseModel> waitReservationList(WaitReservationRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<WaitReservationDetailResponseModel> waitReservationDetail(WaitReservationDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> confirmReservation(ConfirmReservationRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ReservationOrderSignDetailResponseModel> reservationOrderDetail(ReservationOrderSignDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> confirmSign(ReservationOrderConfirmSignRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> enterReservation(@Valid EnterReservationRequestModel requestDto) {
        return null;
    }

    @Override
    public Result<Boolean> enterSignUp(@Valid EnterSignUpRequestModel requestDto) {
        return null;
    }

    @Override
    public Result<GetReservationInfo4H5RespModel> getReservationInfo4H5(GetReservationInfo4H5ReqModel requestModel) {
        return Result.timeout();
    }
}
