package com.logistics.tms.controller.carrierorderapplet.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class LeyiPickupConfirmResponseModel {

    @ApiModelProperty(value = "提货确认状态, 0 未通过 1 通过")
    private Integer confirmStatus;

    @ApiModelProperty(value = "提货数量")
    private BigDecimal loadingCount;
}
