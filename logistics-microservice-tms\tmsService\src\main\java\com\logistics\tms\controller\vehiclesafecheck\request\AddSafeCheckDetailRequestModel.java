package com.logistics.tms.controller.vehiclesafecheck.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/11/6 13:54
 */
@Data
public class AddSafeCheckDetailRequestModel {
    @ApiModelProperty("车辆检查ID")
    private Long safeCheckVehicleId;
    @ApiModelProperty("检查项目")
    private List<SafeCheckItemRequestModel> itemList;
    @ApiModelProperty("车辆文件列表")
    private List<String> vehicleFileList;

    @ApiModelProperty("整改表ID")
    private Long checkReformId;
    @ApiModelProperty("整改数")
    private Integer reformCount;
    @ApiModelProperty("整改内容")
    private String reformContent;
    @ApiModelProperty("整改事项图片列表")
    private List<String> itemFileList;

    @ApiModelProperty("操作状态: 1 提交、2 整改")
    private Integer operatorType;
}
