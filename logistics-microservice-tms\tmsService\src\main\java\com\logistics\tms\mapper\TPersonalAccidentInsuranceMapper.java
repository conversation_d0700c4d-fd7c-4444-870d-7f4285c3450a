package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.personalaccidentinsurance.model.*;
import com.logistics.tms.entity.TPersonalAccidentInsurance;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TPersonalAccidentInsuranceMapper extends BaseMapper<TPersonalAccidentInsurance>{

    List<Long> searchPersonalAccidentInsuranceIds(@Param("params") PersonalAccidentInsuranceListRequestModel requestModel);

    List<PersonalAccidentInsuranceListResponseModel> searchPersonalAccidentInsuranceList(@Param("ids") String ids);

    PersonalAccidentInsuranceDetailResponseModel getPersonalAccidentInsuranceDetail(@Param("id") Long id);

    TPersonalAccidentInsurance getByTypePolicyNumber(@Param("type") Integer type, @Param("policyNumber") String policyNumber, @Param("batchNumber") String batchNumber);

    List<GetInsuranceByPolicyNumberResponseModel> getInsuranceByPolicyNumber(@Param("type") Integer type, @Param("policyNumber") String policyNumber);

    List<SearchInsuranceByPolicyNumberResponseModel> searchInsuranceByPolicyNumber(@Param("policyNumber") String policyNumber);

    List<GetPolicyNoPremiumByPolicyNoResponseModel> getPolicyNoPremiumByPolicyNo(@Param("policyNumber") String policyNumber);

    GetPersonInsurancePersonCountByIdResponseModel getPersonInsurancePersonCountById(@Param("id") Long id);

    List<TPersonalAccidentInsurance> getBatchInsuranceById(@Param("id") Long id);

    int batchUpdate(@Param("list") List<TPersonalAccidentInsurance> list);
}