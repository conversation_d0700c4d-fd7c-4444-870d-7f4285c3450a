package com.logistics.tms.controller.staffvehiclerelation;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.staffvehiclerelation.StaffVehicleBiz;
import com.logistics.tms.controller.staffvehiclerelation.request.*;
import com.logistics.tms.controller.staffvehiclerelation.response.*;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/7/26 15:50
 */
@Api(value = "API-StaffVehicleRelationServiceApi-司机车辆关联关系管理")
@RestController
@RequestMapping(value = "/service/staffVehicle")
public class StaffVehicleRelationController {

    @Autowired
    private StaffVehicleBiz staffVehicleBiz;

    /**
     * 列表
     * @param requestModel
     * @return
     */
    @ApiOperation("列表")
    @PostMapping(value = "/searchList")
    public Result<PageInfo<SearchStaffVehicleListResponseModel>> getStaffVehicleRelation(@RequestBody SearchStaffVehicleListRequestModel requestModel) {
        return Result.success(staffVehicleBiz.searchStaffVehicleList(requestModel));
    }

    /**
     * 详情
     * @param requestModel
     * @return
     */
    @ApiOperation("详情")
    @PostMapping(value = "/detail")
    public Result<StaffVehicleDetailResponseModel> getStaffVehicleDetail(@RequestBody StaffVehicleDetailRequestModel requestModel) {
        return Result.success(staffVehicleBiz.getDetail(requestModel));
    }

    /**
     * 修改/保存
     * @param requestModel
     * @return
     */
    @ApiOperation("修改/保存")
    @PostMapping(value = "/saveOrModify")
    public Result<Boolean> saveOrModifyStaffVehicleRelation(@RequestBody SaveOrModifyStaffVehicleRequestModel requestModel) {
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getIsOurCompany())) {
            /*我司车辆司机新增关系*/
            staffVehicleBiz.saveOrModifyStaffVehicle(requestModel);
        } else {
            /*其他车主车辆司机新增关系*/
            staffVehicleBiz.saveOrModifyOtherStaffVehicle(requestModel);
        }
        return Result.success(true);
    }

    /**
     * 删除车辆司机关联关系
     *
     * @param requestModel 关联id
     * @return 操作结果
     */
    @ApiOperation("删除")
    @PostMapping(value = "/delete")
    public Result<Boolean> deleteStaffVehicleRelation(@RequestBody DeleteStaffVehicleRequestModel requestModel) {
        staffVehicleBiz.deleteStaffVehicle(requestModel);
        return Result.success(true);
    }

    /**
     * 批量删除车辆司机关联关系(前台)
     *
     * @param requestModel 关联id
     * @return 操作结果
     */
    @ApiOperation("批量删除车主司机关联关系(目前车主前台使用)")
    @PostMapping(value = "/batchDeleteStaffVehicleRelation")
    public Result<Boolean> batchDeleteStaffVehicleRelation(@RequestBody BatchDeleteStaffVehicleRelationRequestModel requestModel) {
        staffVehicleBiz.batchDeleteStaffVehicleRelation(requestModel);
        return Result.success(true);
    }

    /**
     * 导出
     *
     * @param requestModel
     * @return
     */
    @ApiOperation("导出")
    @PostMapping(value = "/export")
    @IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchStaffVehicleListResponseModel>> exportStaffVehicleInfo(@RequestBody SearchStaffVehicleListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        PageInfo<SearchStaffVehicleListResponseModel> pageInfo = staffVehicleBiz.searchStaffVehicleList(requestModel);
        return Result.success(pageInfo.getList());
    }

    /**
     * 导入
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导入")
    @PostMapping(value = "/import")
    public Result<ImportStaffVehicleResponseModel> importStaffVehicleInfo(@RequestBody ImportStaffVehicleRequestModel requestModel) {
        return Result.success(staffVehicleBiz.importStaffVehicle(requestModel));
    }

    /**
     * 车辆检查-获取车辆司机列表
     * @param requestModel
     * @return
     */
    @ApiOperation("车辆检查-获取车辆司机列表")
    @PostMapping(value = "/getVehicleStaffRel")
    public Result<List<SafeCheckStaffRelResponseModel>> getVehicleStaffRel(@RequestBody SafeCheckStaffRelRequestModel requestModel) {
        return Result.success(staffVehicleBiz.getVehicleStaffList(requestModel));
    }
    /**
     * 模糊查询司机信息(车辆机构自营，搜索自主和自营司机)
     * @param
     * @return
     */
    @ApiOperation(value = "模糊查询司机信息(车辆机构自营，搜索自主和自营司机)")
    @PostMapping(value = "/fuzzyQueryDriverInfo")
    public Result<List<GetFuzzyQueryDriverInfoResponseModel>> fuzzyQueryDriverInfo(@RequestBody GetFuzzyQueryDriverInfoRequestModel requestModel) {
        return Result.success(staffVehicleBiz.fuzzyQueryDriverInfo(requestModel));
    }
}
