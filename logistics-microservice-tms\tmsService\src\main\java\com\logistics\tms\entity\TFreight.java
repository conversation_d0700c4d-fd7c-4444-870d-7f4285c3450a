package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TFreight extends BaseEntity {
    /**
    * 公司ID
    */
    @ApiModelProperty("公司ID")
    private Long objectId;

    /**
    * 公司类型 1 货主 2 车主
    */
    @ApiModelProperty("公司类型 1 货主 2 车主")
    private Integer objectType;

    /**
    * 启用 1 禁用 0
    */
    @ApiModelProperty("启用 1 禁用 0")
    private Integer enabled;
}