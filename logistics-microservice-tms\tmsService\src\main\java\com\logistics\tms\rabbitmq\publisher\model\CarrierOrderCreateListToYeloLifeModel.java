package com.logistics.tms.rabbitmq.publisher.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/6/28 10:04
 */
@Data
public class CarrierOrderCreateListToYeloLifeModel {

    //所属需求单id, 借用的临时字段,不参与json持久化
    @JsonIgnore
    private Long demandOrderId;

    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("客户单号")
    private String customerOrderCode;

    @ApiModelProperty("物流需求单完成调度状态 1.已完成调度")
    private String completeStatus;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty(value = "入库单sku明细")
    private List<CarrierOrderGoodsToYeloLifeModel> lifeGoodsModels;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNumber;
    @ApiModelProperty(value = "司机名称")
    private String driverName;
    @ApiModelProperty(value = "司机手机号")
    private String driverMobilePhone;

    @ApiModelProperty(value = "收货地code")
    private String toAddressCode;

    @ApiModelProperty(value = "发货地省")
    private String fromAddressProvince;
    @ApiModelProperty(value = "发货地市")
    private String fromAddressCity;
    @ApiModelProperty(value = "发货地区")
    private String fromAddressArea;
    @ApiModelProperty(value = "发货地详情")
    private String fromAddressDetail;
    @ApiModelProperty(value = "发货客户名称")
    private String fromCustomerName;
    @ApiModelProperty(value = "发货客户手机号")
    private String fromCustomerPhone;

    @ApiModelProperty(value = "操作人")
    private String userName;

    @ApiModelProperty(value = "备注")
    private String remark;

}
