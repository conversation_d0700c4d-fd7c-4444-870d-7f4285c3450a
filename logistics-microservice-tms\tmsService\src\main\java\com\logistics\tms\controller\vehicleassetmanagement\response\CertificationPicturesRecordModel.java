package com.logistics.tms.controller.vehicleassetmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CertificationPicturesRecordModel  {
    @ApiModelProperty("附件Id")
    private Long fileId;
    @ApiModelProperty("凭证类型")
    private Integer objectType;
    @ApiModelProperty("图片类型")
    private Integer fileType;
    @ApiModelProperty("图片位置")
    private Integer pageFileType;
    @ApiModelProperty("附件绝对路径")
    private String absoluteFilePath;
    @ApiModelProperty("附件相对路径")
    private String relativeFilepath;
    @ApiModelProperty("图片名称")
    private String fileTypeName;
    @ApiModelProperty("修改人")
    private String lastModifiedBy;
    @ApiModelProperty("最新的修改时间")
    private Date lastModifiedTime;
}
