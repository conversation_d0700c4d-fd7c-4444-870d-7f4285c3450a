package com.logistics.management.webapi.controller.settlestatement.tradition.mapping;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.management.webapi.client.settlestatement.tradition.response.TraditionAssociationCarrierOrderItemModel;
import com.logistics.management.webapi.client.settlestatement.tradition.response.TraditionAssociationCarrierOrderResponseModel;
import com.logistics.management.webapi.controller.settlestatement.tradition.response.TraditionAssociationCarrierOrderResponseDto;
import com.logistics.management.webapi.controller.settlestatement.tradition.response.TraditionCarrierAssociationCarrierOrderItem;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

public class TraditionAssociationCarrierOrderMapping extends MapperMapping<TraditionAssociationCarrierOrderResponseModel, TraditionAssociationCarrierOrderResponseDto> {
    @Override
    public void configure() {
        TraditionAssociationCarrierOrderResponseModel source = getSource();
        TraditionAssociationCarrierOrderResponseDto destination = getDestination();

        if (source != null) {
            //车主运费合计
            destination.setSettlementCost(ConverterUtils.toString(source.getEntrustFreight().setScale(2, RoundingMode.HALF_UP)));

            //数量去0
            destination.setTotalPackageSettlementAmount(source.getTotalPackageSettlementAmount().stripTrailingZeros().toPlainString());
            destination.setTotalWeightSettlementAmount(source.getTotalWeightSettlementAmount().stripTrailingZeros().toPlainString());
            destination.setTotalPieceSettlementAmount(source.getTotalPieceSettlementAmount().stripTrailingZeros().toPlainString());

            //运单数量转换
            List<TraditionCarrierAssociationCarrierOrderItem> carrierOrderItemList = new ArrayList<>();
            List<TraditionAssociationCarrierOrderItemModel> carrierOrderItemListModel = source.getCarrierOrderItemList().getList();
            if (ListUtils.isNotEmpty(carrierOrderItemListModel)) {
                TraditionCarrierAssociationCarrierOrderItem dtoItem;
                for (TraditionAssociationCarrierOrderItemModel model : carrierOrderItemListModel) {
                    //数据转换
                    dtoItem = MapperUtils.mapper(model, TraditionCarrierAssociationCarrierOrderItem.class);

                    //个人车主：姓名+手机号
                    if (CompanyTypeEnum.PERSONAL.getKey().equals(model.getCompanyCarrierType())) {
                        dtoItem.setCompanyCarrierName(model.getContactName() + "_" + model.getContactPhone());
                    }

                    //费用计算
                    dtoItem.setSetSettlementCost(ConverterUtils.toString(model.getEntrustFreight()));
                    //货物单位
                    String unit = GoodsUnitEnum.getEnum(model.getGoodsUnit()).getUnit();
                    //数量去0
                    dtoItem.setSettlementAmount(model.getSettlementAmount().stripTrailingZeros().toPlainString() + unit);
                    //单位转换
                    dtoItem.setGoodsUnit(unit);

                    carrierOrderItemList.add(dtoItem);
                }
            }
            PageInfo pageInfo = MapperUtils.mapper(source.getCarrierOrderItemList(), PageInfo.class);
            pageInfo.setList(carrierOrderItemList);
            destination.setCarrierOrderItemList(pageInfo);
        }
    }
}

