package com.logistics.management.webapi.controller.shippingorder.request;

import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2024/8/6 10:28
 */
@Data
public class ShippingOrderReQuoteRequestDto {

    /**
     * 运输单id
     */
    @NotBlank(message = "id不能为空")
    private String shippingOrderId;

    /**
     * 车长（选择“零担”时传-1，其他选择什么传什么）
     */
    @NotBlank(message = "请选择车长")
    private String vehicleLength;

    /**
     * 串点费用
     */
    @NotBlank(message = "请维护串点费用")
    @DecimalMin(value = "0", message = "请维护串点费用，0<=费用<=1000000元")
    @DecimalMax(value = "1000000", message = "请维护串点费用，0<=费用<=1000000元")
    private String crossPointFee;

    /**
     * 整车运费
     */
    @NotBlank(message = "请维护整车运费")
    @DecimalMin(value = "0.01", message = "请维护整车运费，0<费用<=1000000元")
    @DecimalMax(value = "1000000", message = "请维护整车运费，0<费用<=1000000元")
    private String carrierFreight;
}
