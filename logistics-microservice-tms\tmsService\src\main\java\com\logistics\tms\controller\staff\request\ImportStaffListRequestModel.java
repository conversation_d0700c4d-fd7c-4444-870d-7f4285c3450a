package com.logistics.tms.controller.staff.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ImportStaffListRequestModel{
    @ApiModelProperty("人员类型")
    private Integer type;
    @ApiModelProperty("性别")
    private Integer gender;
    @ApiModelProperty("人员姓名")
    private String name;
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("身份证号码")
    private String identityNumber;
    @ApiModelProperty("年龄")
    private Integer age;
    @ApiModelProperty("身份证期限")
    private Date identityValidity;
    @ApiModelProperty("身份证是否永久")
    private Integer identityIsForever;
    @ApiModelProperty("劳动合同编号")
    private String laborContractNo;
    @ApiModelProperty("劳动合同有效期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date laborContractValidDate;
    @ApiModelProperty("从业资格证证号")
    private String occupationalRequirementsCredentialNo;
    @ApiModelProperty("初次发证日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date initialIssuanceDate;
    @ApiModelProperty("机动车驾驶证号")
    private String driversLicenseNo;
    @ApiModelProperty("准驾车型")
    private String permittedType;
    @ApiModelProperty("驾照开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date driversLicenseDateFrom;
    @ApiModelProperty("驾照结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date driversLicenseDateTo;
    @ApiModelProperty("从业资格证发证期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date occupationalIssueDate;
    @ApiModelProperty("从业资格证有效期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date occupationalValidDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("诚信考核有效期")
    private Date examinationValidDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("继续教育有效期")
    private Date learningValidDate;
    @ApiModelProperty("人员机构 1 自主，2 外部，3 自营")
    private Integer staffProperty;
}
