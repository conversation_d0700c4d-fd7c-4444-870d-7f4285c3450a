package com.logistics.management.webapi.base.enums;


public enum DemandOrderSinopecObjectionTypeEnum {
    DEFAULT(0,""),
    QUOTE(1,"已报价"),
    CANCEL(2,"已取消"),
    ;

    private Integer key;
    private String value;

    DemandOrderSinopecObjectionTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
    public static DemandOrderSinopecObjectionTypeEnum getEnum(Integer key) {
        for (DemandOrderSinopecObjectionTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
