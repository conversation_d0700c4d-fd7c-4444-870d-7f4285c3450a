package com.logistics.management.webapi.api.impl.driveraccount.mapping;

import com.logistics.management.webapi.api.feign.driveraccount.dto.response.SearchDriverAccountResponseDto;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.enums.StaffPropertyEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.driveraccount.model.response.SearchDriverAccountResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/27
 */
public class DriverAccountSearchListMapping extends MapperMapping<SearchDriverAccountResponseModel, SearchDriverAccountResponseDto> {
	@Override
	public void configure() {
		SearchDriverAccountResponseModel source = getSource();
		SearchDriverAccountResponseDto destination = getDestination();

		//司机机构
		destination.setDriverPropertyLabel(StaffPropertyEnum.getEnum(source.getDriverProperty()).getValue());

		//司机姓名
		destination.setDriverName(source.getDriverName() + " " + FrequentMethodUtils.encryptionData(source.getDriverMobile(), EncodeTypeEnum.MOBILE_PHONE));

		//银行名称
		destination.setBankAccountName(source.getBankAccountName() + source.getBraBankName());
	}
}
