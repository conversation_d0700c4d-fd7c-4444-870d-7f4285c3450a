package com.logistics.appapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchCarrierOrderListResponseDto {

    @ApiModelProperty("运单ID")
    private String carrierOrderId = "";

    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";

    @ApiModelProperty("乐橘新生客户名称")
    private String customerName;

    @ApiModelProperty("客户单号")
    private String customerOrderCode = "";

    @ApiModelProperty("运单状态10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0 已取消 1待审核 2 已放空")
    private String status = "";

    private String statusLabel = "";

    @ApiModelProperty("起点")
    private String loadProvinceName = "";
    private String loadCityName = "";
    private String loadAreaName = "";
    private String loadDetailAddress = "";
    private String loadWarehouse = "";

    @ApiModelProperty("起点联系人 1.1.2")
    private String loadPerson = "";

    @ApiModelProperty("起点联系方式")
    private String loadMobile = "";

    @ApiModelProperty("提货经度")
    private String loadLongitude = "";

    @ApiModelProperty("提货纬度")
    private String loadLatitude = "";

    @ApiModelProperty("卸点")
    private String unloadProvinceName = "";
    private String unloadCityName = "";
    private String unloadAreaName = "";
    private String unloadDetailAddress = "";
    private String unloadWarehouse = "";

    @ApiModelProperty("卸点联系人 1.1.2")
    private String unloadPerson = "";

    @ApiModelProperty("卸点联系方式")
    private String unloadMobile = "";

    @ApiModelProperty("卸货经度")
    private String unloadLongitude = "";

    @ApiModelProperty("卸货纬度")
    private String unloadLatitude = "";

    @ApiModelProperty("货物")
    private String  goodsName = "";

    @ApiModelProperty("件数/吨位（包含单位）")
    private String amount = "";

    @ApiModelProperty("体积（为空不显示）")
    private String capacity = "";

    @ApiModelProperty("提货时间")
    private String expectArrivalTime = "";

    @ApiModelProperty("备注")
    private String remark = "";


    @ApiModelProperty("是否按编码回收 0:否 1:是   v2.44")
    private String ifRecycleByCode;

    @ApiModelProperty("是否是额外补的运单  0：否 1：是  V2.6.8")
    private String ifExtCarrierOrder;

    @ApiModelProperty("是否显示补单按钮  0：否 1：是  V2.6.8")
    private String enableExtCarrierOrder= "";
}
