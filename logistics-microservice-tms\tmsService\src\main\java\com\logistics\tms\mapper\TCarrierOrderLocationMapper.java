package com.logistics.tms.mapper;

import com.logistics.tms.entity.TCarrierOrderLocation;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2022/12/16
*/
@Mapper
public interface TCarrierOrderLocationMapper extends BaseMapper<TCarrierOrderLocation> {

    List<TCarrierOrderLocation> getByCarrierOrderIds(@Param("carrierOrderIds")String carrierOrderIds);

    TCarrierOrderLocation getByCarrierOrderIdType(@Param("carrierOrderId")Long carrierOrderId,@Param("locationType")Integer locationType);

}