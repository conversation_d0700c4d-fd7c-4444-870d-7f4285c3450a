package com.logistics.management.webapi.controller.freightconfig.request.address;

import com.logistics.management.webapi.controller.freightconfig.request.ladder.CarrierFreightConfigLadderRequestDto;
import com.logistics.management.webapi.controller.freightconfig.request.scheme.CarrierFreightConfigSchemeRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigAddressAddRequestDto extends CarrierFreightConfigSchemeRequestDto {

    @ApiModelProperty(value = "发货省ID", required = true)
    @NotBlank(message = "发货省ID不允许为空")
    private String fromProvinceId;

    @ApiModelProperty(value = "发货省名字", required = true)
    @NotBlank(message = "发货省名字不允许为空")
    private String fromProvinceName;

    @ApiModelProperty(value = "发货城市ID", required = true)
    @NotBlank(message = "发货省ID不允许为空")
    private String fromCityId;

    @ApiModelProperty(value = "发货城市名字", required = true)
    @NotBlank(message = "发货城市名字不允许为空")
    private String fromCityName;

    @ApiModelProperty(value = "发货县区", required = true)
    @NotEmpty(message = "发货县区不允许为空")
    private List<FromArea> fromAreaId;

    @ApiModelProperty(value = "卸货省ID", required = true)
    @NotBlank(message = "卸货省ID不允许为空")
    private String toProvinceId;

    @ApiModelProperty(value = "卸货省名字", required = true)
    @NotBlank(message = "卸货省名字不允许为空")
    private String toProvinceName;

    @ApiModelProperty(value = "卸货城市ID", required = true)
    @NotBlank(message = "发货省ID不允许为空")
    private String toCityId;

    @ApiModelProperty(value = "卸货城市名字", required = true)
    @NotBlank(message = "卸货城市名字不允许为空")
    private String toCityName;

    @ApiModelProperty(value = "卸货县区ID", required = true)
    @NotEmpty(message = "卸货县区不允许为空")
    private List<FromArea> toAreaId;

    @ApiModelProperty(value = "阶梯配置", required = true)
    @Size(min = 1, max = 10, message = "一条路线最多存在10条阶梯价格")
    private List<CarrierFreightConfigLadderRequestDto> ladderConfigList;

    @Data
    public static class FromArea {

        @ApiModelProperty(value = "发货县区ID", required = true)
        @NotBlank(message = "发货县区ID不允许为空")
        private String fromAreaId;

        @ApiModelProperty(value = "发货县区名称", required = true)
        @NotBlank(message = "发货县区名称不允许为空")
        private String fromAreaName;
    }
}
