package com.logistics.tms.biz.staffvehiclerelation


import com.logistics.tms.controller.staffvehiclerelation.request.DeleteStaffVehicleRequestModel
import com.logistics.tms.controller.staffvehiclerelation.request.GetFuzzyQueryDriverInfoRequestModel
import com.logistics.tms.controller.staffvehiclerelation.request.ImportStaffVehicleRequestModel
import com.logistics.tms.controller.staffvehiclerelation.request.SafeCheckStaffRelRequestModel
import com.logistics.tms.controller.staffvehiclerelation.request.SaveOrModifyStaffVehicleRequestModel
import com.logistics.tms.controller.staffvehiclerelation.request.SearchStaffVehicleListRequestModel
import com.logistics.tms.controller.staffvehiclerelation.request.StaffVehicleDetailRequestModel
import com.logistics.tms.controller.staffvehiclerelation.request.StaffVehicleRelationRequestModel
import com.logistics.tms.controller.staffvehiclerelation.response.ImportStaffVehicleResponseModel
import com.yelo.tray.core.exception.BizException
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.annotation.Rollback
import org.springframework.transaction.annotation.Transactional
import spock.lang.Specification
import spock.lang.Unroll
/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */

@SpringBootTest
class StaffVehicleBizTest extends Specification {

    @Autowired
    StaffVehicleBiz staffVehicleBiz

    def setup() {
    }

    @Unroll
    def "search Staff Vehicle List where requestModel=#requestModel then expect: #expectedResult"() {
        given:

        expect:
        with(staffVehicleBiz.searchStaffVehicleList(requestModel).getList().get(0)) {
            tractorVehicleNo == expectedResult
        }

        where:
        requestModel                                                 || expectedResult
        new SearchStaffVehicleListRequestModel(vehicleNo: "2321321") || "2321321"
    }

    @Unroll
    @Rollback
    @Transactional
    def "save Or Modify Staff Vehicle where requestModel=#requestModel"() {
        given: "准备数据"

        when: "执行方法"
        staffVehicleBiz.saveOrModifyStaffVehicle(requestModel)

        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where: "批量验证"
        requestModel                                                                                                                          || expectedResult
        new SaveOrModifyStaffVehicleRequestModel(staffVehicleRelationId: -1) || "请选择车辆机构"
        new SaveOrModifyStaffVehicleRequestModel(type: 1)                                                                                     || "请选择牵引车类型或一体车类型"
        new SaveOrModifyStaffVehicleRequestModel(staffVehicleRelationId: 1, type: 1, vehicleCategory: 1)                                      || "司机信息不存在"
        new SaveOrModifyStaffVehicleRequestModel(staffVehicleRelationId: 1, type: 1, vehicleCategory: 1, staffId: 1281)                       || "牵引车信息不存在"
        new SaveOrModifyStaffVehicleRequestModel(staffVehicleRelationId: 1, type: 1, vehicleCategory: 1, staffId: 1281, tractorVehicleId: 13) || "挂车信息不存在"
    }

    @Unroll
    def "get Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given: "准备数据"

        when: "执行方法"
        staffVehicleBiz.getDetail(requestModel)

        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where: "批量验证"
        requestModel                                           || mapperResult || expectedResult
        new StaffVehicleDetailRequestModel(staffVehicleId: -1) || null || "车辆司机关联信息不存在"
    }

    @Unroll
    @Transactional
    def "delete Staff Vehicle where requestModel=#requestModel"() {
        when:
        staffVehicleBiz.deleteStaffVehicle(requestModel)


        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }
        where:
        requestModel                         || expectedResult
        new DeleteStaffVehicleRequestModel(staffVehicleId:-1) || "车辆司机关联信息不存在"
    }

    @Unroll
    @Transactional
    def "import Staff Vehicle where requestModel=#requestModel then expect: #expectedResult"() {
        given:

        expect:
        staffVehicleBiz.importStaffVehicle(requestModel) == expectedResult

        where:
        requestModel                         || expectedResult
        new ImportStaffVehicleRequestModel(importList:null) || new ImportStaffVehicleResponseModel()
    }

    @Unroll
    @Transactional
    def "import Single Staff Vehicle Info where rel=#rel"() {
        given: "准备数据"

        when: "执行方法"
        staffVehicleBiz.importSingleStaffVehicleInfo(rel)

        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where: "批量验证"
        rel                                    || expectedResult
        new StaffVehicleRelationRequestModel() || "车辆司机关联信息不存在"
    }

    @Unroll
    def "get Vehicle Staff List where requestModel=#requestModel then expect: #expectedResult"() {
        given:

        expect:
        staffVehicleBiz.getVehicleStaffList(requestModel).size() == expectedResult

        where:
        requestModel                        || expectedResult
        new SafeCheckStaffRelRequestModel(vehicleNo: "e2132132" ) || 0
    }

    @Unroll
    def "fuzzy Query Driver Info where requestModel=#requestModel then expect: #expectedResult"() {
        given:

        expect:
        staffVehicleBiz.fuzzyQueryDriverInfo(requestModel).size() == expectedResult

        where:
        requestModel                              || expectedResult
        new GetFuzzyQueryDriverInfoRequestModel() || 1
    }
}
