package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.client.carrierorder.response.SearchCarrierOrderListGoodsInfoModel;
import com.logistics.management.webapi.client.carrierorder.response.SearchCarrierOrderListResponseModel;
import com.logistics.management.webapi.controller.carrierorder.response.SearchCarrierOrderResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;


public class CarrierOrderSearchListMapping extends MapperMapping<SearchCarrierOrderListResponseModel, SearchCarrierOrderResponseDto> {
    @Override
    public void configure() {
        SearchCarrierOrderListResponseModel source = getSource();
        SearchCarrierOrderResponseDto destination = getDestination();
        if(CommonConstant.INTEGER_ONE.equals(source.getIfCancel())){
            destination.setStatus(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getKey().toString());
            destination.setStatusDesc(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getValue());
        }else if(CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())){
            destination.setStatus(CarrierOrderStatusEnum.EMPTY.getKey().toString());
            destination.setStatusDesc(CarrierOrderStatusEnum.EMPTY.getValue());
        }else{
            destination.setStatusDesc(CarrierOrderStatusEnum.getEnum(source.getStatus()).getValue());
        }
        if (CommonConstant.INTEGER_ONE.equals(source.getIfObjection())){
            destination.setExportStatusDesc("(" + IfObjectionEnum.YES.getValue() + ")" + destination.getStatusDesc());
        }else{
            destination.setExportStatusDesc(destination.getStatusDesc());
        }

        //货主
        String companyEntrustName = source.getEntrustCompany();
        destination.setEntrustCompany(companyEntrustName);
        destination.setDriver(source.getDriverName() + " " + source.getDriverMobile());
        destination.setLoadAddress((StringUtils.isNotEmpty(source.getLoadWarehouse())?"【" + source.getLoadWarehouse() + "】":"") + " "+source.getLoadCityName()+source.getLoadAreaName() + source.getLoadDetailAddress());
        destination.setUnloadAddress((StringUtils.isNotEmpty(source.getUnloadWarehouse())?"【" + source.getUnloadWarehouse() + "】":"")  + " " +source.getUnloadCityName()+source.getUnloadAreaName()+ source.getUnloadDetailAddress());
        destination.setExportDriverName(source.getDriverName());
        destination.setExportDriverMobile(source.getDriverMobile());

        if(CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(source.getStatus())){
            BigDecimal expectMileage=BigDecimal.ZERO;
            if(source.getExpectMileage()!=null) {
                expectMileage=source.getExpectMileage();
            }
            destination.setExpectMileage(expectMileage.stripTrailingZeros().toPlainString()+"KM");
        }else{
            destination.setExpectMileage("");
        }

        //货主费用
        BigDecimal amount = BigDecimal.ZERO;
        if (SettlementTonnageEnum.LOAD.getKey().equals(source.getSettlementTonnage())){
            amount = source.getLoadAmount();
        }else if (SettlementTonnageEnum.UNLOAD.getKey().equals(source.getSettlementTonnage())){
            amount = source.getUnloadAmount();
        }else if (SettlementTonnageEnum.SIGN.getKey().equals(source.getSettlementTonnage())){
            amount = source.getSignAmount();
        }else if (SettlementTonnageEnum.EXPECT.getKey().equals(source.getSettlementTonnage())){
            amount = source.getExpectAmount();
        }
        if (source.getEntrustFreightType().equals(PriceTypeEnum.UNIT_PRICE.getKey())) {
            if (amount.compareTo(BigDecimal.ZERO) == CommonConstant.INTEGER_ZERO){
                amount = source.getExpectAmount();
            }
            destination.setFreightFee(ConverterUtils.toString(source.getEntrustFreight().multiply(amount).setScale(2, BigDecimal.ROUND_HALF_UP)));
        } else {
            destination.setFreightFee(ConverterUtils.toString(source.getEntrustFreight()));
        }

        //司机费用
        BigDecimal dispatchFreightFee = BigDecimal.ZERO;
        if(source.getStatus()<CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey()){
            if (source.getDispatchFreightFeeType().equals(PriceTypeEnum.UNIT_PRICE.getKey())){
                dispatchFreightFee = source.getDispatchFreightFee().multiply(source.getExpectAmount()).setScale(2, BigDecimal.ROUND_HALF_UP);
            }else if(source.getDispatchFreightFeeType().equals(PriceTypeEnum.FIXED_PRICE.getKey())) {
                dispatchFreightFee = source.getDispatchFreightFee();
            }
        }else{
            if (source.getDispatchFreightFeeType().equals(PriceTypeEnum.UNIT_PRICE.getKey())){
                dispatchFreightFee = source.getDispatchFreightFee().multiply(source.getUnloadAmount()).setScale(2, BigDecimal.ROUND_HALF_UP);
            }else if(source.getDispatchFreightFeeType().equals(PriceTypeEnum.FIXED_PRICE.getKey())) {
                dispatchFreightFee = source.getDispatchFreightFee();
            }
        }
        if(CommonConstant.INTEGER_ONE.equals(source.getIfCancel())){
            destination.setDispatchFreightFee(CommonConstant.ZERO_NET_ZERO);
        }else {
            destination.setDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee.add(source.getAdjustFee()).add(source.getMarkupFee())));
        }

        if (ListUtils.isNotEmpty(source.getGoodsInfoList())) {
            StringBuilder goodsName = new StringBuilder();
            StringBuilder goodsSize = new StringBuilder();
            for(int index = 0;index<source.getGoodsInfoList().size() ;index++){
                SearchCarrierOrderListGoodsInfoModel tmpGoodsModel = source.getGoodsInfoList().get(index);
                if (source.getGoodsUnit().equals(GoodsUnitEnum.BY_VOLUME.getKey())) {
                    tmpGoodsModel.setGoodsSize(tmpGoodsModel.getGoodsSize() + " " + tmpGoodsModel.getLength() + "*" + tmpGoodsModel.getWidth() + "*" + tmpGoodsModel.getHeight() + "mm");
                }
                if(index !=0){
                    goodsName.append("/");
                }
                goodsName.append(tmpGoodsModel.getGoodsName());

                if (StringUtils.isNotBlank(tmpGoodsModel.getGoodsSize())) {
                    if (goodsSize.length() > 0) {
                        goodsSize.append("/");
                    }
                    goodsSize.append(tmpGoodsModel.getGoodsSize());
                }
            }

            destination.setGoodsName(goodsName.toString());
            destination.setGoodsSize(goodsSize.toString());
        }

        if(source.getUnloadTime()!=null){
            destination.setUnloadTime(DateUtils.dateToString(source.getUnloadTime(),CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
        }
        if(source.getExpectArrivalTime()!=null){
            destination.setExpectArrivalTime(DateUtils.dateToString(source.getExpectArrivalTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if(source.getDispatchTime()!=null){
            destination.setDispatchTime(DateUtils.dateToString(source.getDispatchTime(),DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
        }

        destination.setGoodsUnit(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());
        destination.setExpectAmount(source.getExpectAmount().stripTrailingZeros().toPlainString());
        destination.setLoadAmount(source.getLoadAmount().stripTrailingZeros().toPlainString());
        destination.setUnloadAmount(source.getUnloadAmount().stripTrailingZeros().toPlainString());
        //展示车主名称
        if (CommonConstant.INTEGER_ONE.equals(source.getCompanyCarrierType())){
            destination.setCarrierCompany(source.getCarrierCompany());
        }else if (CommonConstant.INTEGER_TWO.equals(source.getCompanyCarrierType())){
            destination.setCarrierCompany(source.getCarrierContactName() + " " + source.getCarrierContactMobile());
        }

        //车主实际结算数量
        BigDecimal carrierSettlementAmount = BigDecimal.ZERO;
        if (SettlementTonnageEnum.LOAD.getKey().equals(source.getCarrierSettlement())) {
            carrierSettlementAmount = source.getLoadAmount();
        } else if (SettlementTonnageEnum.UNLOAD.getKey().equals(source.getCarrierSettlement())) {
            carrierSettlementAmount = source.getUnloadAmount();
        } else if (SettlementTonnageEnum.SIGN.getKey().equals(source.getCarrierSettlement())) {
            carrierSettlementAmount = source.getSignAmount();
        } else if (SettlementTonnageEnum.EXPECT.getKey().equals(source.getCarrierSettlement())) {
            carrierSettlementAmount = source.getExpectAmount();
        }
        //其他车主展示车主费用
        if (CommonConstant.INTEGER_TWO.equals(source.getIsOurCompany())){
            BigDecimal carrierFreightFee = source.getCarrierPrice();
            //预计车主费用
            if (PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getCarrierPriceType())){
                destination.setExpectedCarrierPriceTotal(ConverterUtils.toString(source.getExpectAmount().multiply(carrierFreightFee).setScale(2,BigDecimal.ROUND_HALF_UP)));
            }else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getCarrierPriceType())){
                destination.setExpectedCarrierPriceTotal(ConverterUtils.toString(carrierFreightFee));
            }

            //实际车主费用
            if(CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(source.getStatus())){
                if (PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getCarrierPriceType())) {
                    BigDecimal signCarrierFreightFeeTotal = carrierFreightFee.multiply(source.getSignAmount()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    destination.setCarrierPriceTotal(ConverterUtils.toString(signCarrierFreightFeeTotal));
                } else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getCarrierPriceType())) {
                    destination.setCarrierPriceTotal(ConverterUtils.toString(carrierFreightFee));
                }

                //有结算数据计算实际费用
                if(carrierSettlementAmount.compareTo(BigDecimal.ZERO) > 0){
                    if (PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getCarrierPriceType())) {
                        destination.setCarrierPriceTotal(ConverterUtils.toString(carrierFreightFee.multiply(carrierSettlementAmount).setScale(2, BigDecimal.ROUND_HALF_UP)));
                    } else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getCarrierPriceType())) {
                        destination.setCarrierPriceTotal(ConverterUtils.toString(carrierFreightFee));
                    }
                }

                //已生成结算数据
                if(source.getCarrierSettlementCostTotal() != null){
                    destination.setCarrierPriceTotal(ConverterUtils.toString(source.getCarrierSettlementCostTotal()));
                }
            }
        }else if (CommonConstant.INTEGER_ONE.equals(source.getIsOurCompany())){
            destination.setExpectedCarrierPriceTotal("");
            destination.setCarrierPriceTotal("");
        }
        destination.setIfTrayOrder(DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(source.getDemandOrderSource()) ? CommonConstant.ONE : CommonConstant.TWO);
    }
}
