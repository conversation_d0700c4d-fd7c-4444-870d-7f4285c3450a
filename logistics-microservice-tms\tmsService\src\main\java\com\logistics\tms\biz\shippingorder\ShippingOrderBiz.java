package com.logistics.tms.biz.shippingorder;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.driverpayee.model.ViewLogResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.dispatch.model.DemandOrderModel;
import com.logistics.tms.biz.dispatchorder.model.DispatchOrderItemSqlConditionModel;
import com.logistics.tms.biz.payment.PaymentBiz;
import com.logistics.tms.biz.shippingorder.model.ShippingOrderItemSqlConditionModel;
import com.logistics.tms.client.model.PathPlanResponseModel;
import com.logistics.tms.controller.companycarrier.response.UserCompanyCarrierInfoResponseModel;
import com.logistics.tms.controller.dispatch.request.SearchCanJoinShippingOrderRequestModel;
import com.logistics.tms.controller.dispatch.response.DemandOrderSpecialDispatchDetailListResponseModel;
import com.logistics.tms.controller.dispatch.response.DemandOrderSpecialDispatchDetailResponseModel;
import com.logistics.tms.controller.dispatch.response.SearchCanJoinShippingOrderRespModel;
import com.logistics.tms.controller.shippingorder.request.*;
import com.logistics.tms.controller.shippingorder.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2024/8/6 14:39
 */
@Service
public class ShippingOrderBiz {

    @Resource
    private TShippingOrderMapper tShippingOrderMapper;
    @Resource
    private TShippingOrderItemMapper tShippingOrderItemMapper;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private TOperateLogsMapper tOperateLogsMapper;
    @Resource
    private TDispatchOrderMapper tDispatchOrderMapper;
    @Resource
    private ShippingOrderCommonBiz shippingOrderCommonBiz;
    @Resource
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Resource
    private PaymentBiz paymentBiz;
    @Resource
    private TCarrierOrderGoodsMapper tCarrierOrderGoodsMapper;
    @Resource
    TPaymentMapper tPaymentMapper;
    @Resource
    TDemandOrderMapper tDemandOrderMapper;

    /**
     * 列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<SearchShippingOrderListResponseModel> searchList(SearchShippingOrderListRequestModel requestModel,Boolean ifExport) {

        //前台 只能查询登录车主的单子
        Long companyCarrierId = null;
        if (CommonConstant.TWO.equals(requestModel.getSource())){
            //判断车主是否存在
            companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (companyCarrierId == null || CommonConstant.LONG_ZERO.equals(companyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
            }
        }

        //调度单 条件查询
        Map<Long, TDispatchOrder> tDispatchOrdersMap = new HashMap<>();
        if (StringUtils.isNotBlank(requestModel.getCompanyCarrierName()) || companyCarrierId != null || StringUtils.isNotBlank(requestModel.getDispatchOrderCode())) {
            DispatchOrderItemSqlConditionModel dispatchOrderItemSqlConditionModel = new DispatchOrderItemSqlConditionModel();
            dispatchOrderItemSqlConditionModel.setCompanyCarrierName(requestModel.getCompanyCarrierName());
            dispatchOrderItemSqlConditionModel.setCompanyCarrierId(companyCarrierId);
            dispatchOrderItemSqlConditionModel.setDispatchOrderCode(requestModel.getDispatchOrderCode());
            List<TDispatchOrder> tDispatchOrders = tDispatchOrderMapper.selectByCondition(dispatchOrderItemSqlConditionModel);
            if (CollectionUtil.isEmpty(tDispatchOrders)) {
                return new PageInfo<>(new ArrayList<>());
            }
            tDispatchOrdersMap = tDispatchOrders.stream().collect(Collectors.toMap(TDispatchOrder::getId, Function.identity()));
        }

        //零担运输单明细 条件查询
        List<Long> conditionShippingOrderIds = new ArrayList<>();
        if (StringUtils.isNotBlank(requestModel.getLoadAddress()) || StringUtils.isNotBlank(requestModel.getUnloadAddress())
                || StringUtils.isNotBlank(requestModel.getDemandOrderCode()) || StringUtils.isNotBlank(requestModel.getCarrierOrderCode())) {
            ShippingOrderItemSqlConditionModel shippingOrderItemSqlConditionModel = new ShippingOrderItemSqlConditionModel();
            shippingOrderItemSqlConditionModel.setLoadAddress(requestModel.getLoadAddress());
            shippingOrderItemSqlConditionModel.setUnloadAddress(requestModel.getUnloadAddress());
            //如果根据地址查询 由于只展示第一条明细的地址 所以查询也只条件查询第一条明细
            if (StringUtils.isNotBlank(requestModel.getLoadAddress()) || StringUtils.isNotBlank(requestModel.getUnloadAddress())) {
                shippingOrderItemSqlConditionModel.setOrderNum(CommonConstant.INTEGER_ONE);
            }
            shippingOrderItemSqlConditionModel.setDemandOrderCode(requestModel.getDemandOrderCode());
            shippingOrderItemSqlConditionModel.setCarrierOrderCode(requestModel.getCarrierOrderCode());
            List<TShippingOrderItem> tShippingOrderItems = tShippingOrderItemMapper.selectByCondition(shippingOrderItemSqlConditionModel);
            if (CollectionUtil.isEmpty(tShippingOrderItems)) {
                return new PageInfo<>(new ArrayList<>());
            }
            conditionShippingOrderIds = tShippingOrderItems.stream().map(TShippingOrderItem::getShippingOrderId).distinct().collect(Collectors.toList());

        }


        //零担运输单 分页查询
        if (!ifExport) {
            requestModel.enablePaging();
        }
        List<TShippingOrder> tShippingOrders = tShippingOrderMapper.searchList(requestModel, tDispatchOrdersMap.keySet(), conditionShippingOrderIds);
        if (CollectionUtil.isEmpty(tShippingOrders)) {
            return new PageInfo<>(new ArrayList<>());
        }
        List<Long> dispatchOrderIds = tShippingOrders.stream().map(TShippingOrder::getDispatchOrderId).collect(Collectors.toList());
        List<Long> shippingOrderIds = tShippingOrders.stream().map(TShippingOrder::getId).collect(Collectors.toList());
        PageInfo pageInfo = new PageInfo<>(tShippingOrders);

        //查询调度单
        if (MapUtil.isEmpty(tDispatchOrdersMap)) {
            DispatchOrderItemSqlConditionModel dispatchOrderItemSqlConditionModel = new DispatchOrderItemSqlConditionModel();
            dispatchOrderItemSqlConditionModel.setDispatchOrderIds(dispatchOrderIds);
            List<TDispatchOrder> tDispatchOrders = tDispatchOrderMapper.selectByCondition(dispatchOrderItemSqlConditionModel);
            tDispatchOrdersMap = tDispatchOrders.stream().collect(Collectors.toMap(TDispatchOrder::getId, Function.identity()));
        }

        //查询零担运输单明细
        ShippingOrderItemSqlConditionModel sqlConditionModel = new ShippingOrderItemSqlConditionModel();
        sqlConditionModel.setShippingOrderIds(shippingOrderIds);
        List<TShippingOrderItem> tShippingOrderItems = tShippingOrderItemMapper.selectByCondition(sqlConditionModel);
        Map<Long, List<TShippingOrderItem>> tShippingOrderItemsMap = tShippingOrderItems.stream().collect(Collectors.groupingBy(TShippingOrderItem::getShippingOrderId));
        List<Long> carrierOrderIds = tShippingOrderItems.stream().map(TShippingOrderItem::getCarrierOrderId).collect(Collectors.toList());

        //查询运单
        List<TCarrierOrder> tCarrierOrders = tCarrierOrderMapper.selectCarrierOrdersByIds(StringUtils.listToString(carrierOrderIds, ','));
        Map<Long, TCarrierOrder> tCarrierOrdersMap = tCarrierOrders.stream().collect(Collectors.toMap(TCarrierOrder::getId, Function.identity()));

        //查询运单结算
        List<TPayment> tPayments = tPaymentMapper.getByCarrierOrderIds(StringUtils.listToString(carrierOrderIds, ','));
        Map<Long, TPayment> tPaymentsMap = tPayments.stream().collect(Collectors.toMap(TPayment::getCarrierOrderId, Function.identity()));

        //组装数据
        List<SearchShippingOrderListResponseModel> responseModels = new ArrayList<>();
        for (TShippingOrder tShippingOrder : tShippingOrders) {
            //下带零担运输单明细
            List<TShippingOrderItem> shippingOrderItems = tShippingOrderItemsMap.getOrDefault(tShippingOrder.getId(), new ArrayList<>());
            //拿到第一个地址 用于列表展示
            TShippingOrderItem firstShippingOrderItem = shippingOrderItems.stream().min(Comparator.comparing(TShippingOrderItem::getOrderNum)).orElse(null);
            //下带运单
            List<TCarrierOrder> carrierOrders = shippingOrderItems.stream().map(tShippingOrderItem -> tCarrierOrdersMap.get(tShippingOrderItem.getCarrierOrderId()))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            //调度单
            TDispatchOrder tDispatchOrder = tDispatchOrdersMap.get(tShippingOrder.getDispatchOrderId());
            //累加运单实际费用
            BigDecimal actualCarrierFreight = carrierOrders.stream().map(carrierOrder -> paymentBiz.calculateActualCarrierPriceLikeCarrierDetail(carrierOrder, tPaymentsMap.get(carrierOrder.getId())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            //累加运单签收数
            BigDecimal signAmount = carrierOrders.stream().map(TCarrierOrder::getSignAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            SearchShippingOrderListResponseModel responseModel = new SearchShippingOrderListResponseModel();
            responseModel.setActualCarrierFreight(actualCarrierFreight.setScale(2, RoundingMode.HALF_UP).toPlainString());
            responseModel.setCarrierFreight(tShippingOrder.getCarrierFreight());
            if (tDispatchOrder != null) {
                if (CompanyTypeEnum.PERSON.getKey().equals(tDispatchOrder.getCompanyCarrierType())) {
                    responseModel.setCompanyCarrierName(tDispatchOrder.getCarrierContactName() + " " + tDispatchOrder.getCarrierContactPhone());
                } else {
                    responseModel.setCompanyCarrierName(StringUtils.isBlank(tDispatchOrder.getCompanyCarrierName()) ? "" : tDispatchOrder.getCompanyCarrierName());
                }
                responseModel.setDispatchTime(tDispatchOrder.getDispatchTime());
                responseModel.setDispatchUserName(tDispatchOrder.getDispatchUserName());
                responseModel.setDriver(tDispatchOrder.getDriverName() + " " + tDispatchOrder.getDriverMobile());
                responseModel.setVehicleNo(tDispatchOrder.getVehicleNo());
                responseModel.setPointAmount(tDispatchOrder.getLoadPointAmount().toString() + "装" + tDispatchOrder.getUnloadPointAmount().toString() + "卸");
            }
            responseModel.setCrossPointFee(tShippingOrder.getCrossPointFee());
            responseModel.setDispatchOrderCode(tShippingOrder.getDispatchOrderCode());
            responseModel.setDispatchOrderId(tShippingOrder.getDispatchOrderId());
            responseModel.setExpectAmount(tShippingOrder.getExpectAmount());
            if (firstShippingOrderItem != null) {
                responseModel.setLoadAddress(firstShippingOrderItem.getLoadProvinceName() + "-" + firstShippingOrderItem.getLoadCityName() + "-" + firstShippingOrderItem.getLoadAreaName());
                responseModel.setUnloadAddress(firstShippingOrderItem.getUnloadProvinceName() + "-" + firstShippingOrderItem.getUnloadCityName() + "-" + firstShippingOrderItem.getUnloadAreaName());
            }
            responseModel.setLoadPointAmount(shippingOrderItems.size());
            responseModel.setShippingOrderCode(tShippingOrder.getShippingOrderCode());
            responseModel.setShippingOrderId(tShippingOrder.getId());
            responseModel.setSignAmount(signAmount);
            responseModel.setStatus(tShippingOrder.getStatus());
            responseModel.setVehicleLength(tShippingOrder.getVehicleLength());

            responseModels.add(responseModel);
        }
        pageInfo.setList(responseModels);
        return pageInfo;
    }


    /**
     * 查询详情
     * @param requestModel
     * @return
     */
    public GetShippingOrderDetailResponseModel getDetail(GetShippingOrderDetailRequestModel requestModel){
        //零担运输单
        TShippingOrder dbShippingOrder = tShippingOrderMapper.selectByPrimaryKey(requestModel.getShippingOrderId());
        if (dbShippingOrder == null || IfValidEnum.INVALID.getKey().equals(dbShippingOrder.getValid())){
            throw new BizException(CarrierDataExceptionEnum.SHIPPING_ORDER_NOT_EXIST);
        }
        //调度单
        TDispatchOrder dbDispatchOrder = tDispatchOrderMapper.selectByPrimaryKeyDecrypt(dbShippingOrder.getDispatchOrderId());
        if (dbDispatchOrder == null ){
            throw new BizException(CarrierDataExceptionEnum.DISPATCH_ORDER_NOT_EXIST);
        }

        //前台
        if (CommonConstant.TWO.equals(requestModel.getSource())){
            //判断车主是否存在
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (companyCarrierId == null || CommonConstant.LONG_ZERO.equals(companyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
            }
            //判断单子是否是该车主的
            if (!dbDispatchOrder.getCompanyCarrierId().equals(companyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.DISPATCH_ORDER_NOT_EXIST);
            }
        }

        //查询 零担运输单明细
        ShippingOrderItemSqlConditionModel shippingOrderItemSqlConditionModel = new ShippingOrderItemSqlConditionModel();
        shippingOrderItemSqlConditionModel.setShippingOrderIds(Arrays.asList(dbShippingOrder.getId()));
        List<TShippingOrderItem> tShippingOrderItems = tShippingOrderItemMapper.selectByCondition(shippingOrderItemSqlConditionModel);
        List<Long> carrierOrderIds = tShippingOrderItems.stream().map(TShippingOrderItem::getCarrierOrderId).collect(Collectors.toList());
        //拿到第一个地址 用于列表展示
        TShippingOrderItem firstShippingOrderItem = tShippingOrderItems.stream().min(Comparator.comparing(TShippingOrderItem::getOrderNum)).orElse(null);

        //运单goods
        List<TCarrierOrderGoods> tCarrierOrderGoods = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(StringUtils.listToString(carrierOrderIds, ','));
        Map<Long, List<TCarrierOrderGoods>> tCarrierOrderGoodsMap = tCarrierOrderGoods.stream().collect(Collectors.groupingBy(TCarrierOrderGoods::getCarrierOrderId));



        //查询 操作记录
        List<ViewLogResponseModel> viewLogResponseModels = tOperateLogsMapper.selectLogsByCondition(OperateLogsObjectTypeEnum.SHIPPING_ORDER.getKey(), dbShippingOrder.getId(), null);

        //组装数据返回
        List<GetShippingOrderOperateLogListResponseModel> operateModels = new ArrayList<>();
        for (ViewLogResponseModel viewLogResponseModel : viewLogResponseModels) {
            GetShippingOrderOperateLogListResponseModel operateLogModel = new GetShippingOrderOperateLogListResponseModel();
            operateLogModel.setOperateContents(viewLogResponseModel.getOperateContents());
            operateLogModel.setOperateTime(viewLogResponseModel.getOperateTime());
            operateLogModel.setOperateUserName(viewLogResponseModel.getOperateUserName());
            operateLogModel.setRemark(viewLogResponseModel.getRemark());
            operateModels.add(operateLogModel);
        }

        List<GetShippingOrderDetailOrderListResponseModel> detailItemList=new ArrayList<>();
        for (TShippingOrderItem tShippingOrderItem : tShippingOrderItems) {

            List<TCarrierOrderGoods> carrierOrderGoods = tCarrierOrderGoodsMap.getOrDefault(tShippingOrderItem.getCarrierOrderId(), new ArrayList<>());

            String goodsNameJson = carrierOrderGoods.stream().map(TCarrierOrderGoods::getGoodsName).collect(Collectors.joining(","));
            String goodsSizeJson = carrierOrderGoods.stream().map(tmp -> tmp.getLength() + "*" + tmp.getWidth() + "*" + tmp.getHeight() + " " + tmp.getGoodsSize()).collect(Collectors.joining(","));

            GetShippingOrderDetailOrderListResponseModel itemModel = new GetShippingOrderDetailOrderListResponseModel();
            itemModel.setDemandOrderCode(tShippingOrderItem.getDemandOrderCode());
            itemModel.setExpectAmount(tShippingOrderItem.getExpectAmount().stripTrailingZeros().toPlainString());
            itemModel.setGoodsName(goodsNameJson);
            itemModel.setGoodsSize(goodsSizeJson);
            itemModel.setLoadAddress(tShippingOrderItem.getLoadProvinceName() + "-" + tShippingOrderItem.getLoadCityName() + "-" + tShippingOrderItem.getLoadAreaName());
            itemModel.setUnloadAddress(tShippingOrderItem.getUnloadProvinceName() + "-" + tShippingOrderItem.getUnloadCityName() + "-" + tShippingOrderItem.getUnloadAreaName());
            detailItemList.add(itemModel);
        }

        //拼单列表
        GetShippingOrderDetailResponseModel responseModel = new GetShippingOrderDetailResponseModel();
        responseModel.setAuditRemark(dbShippingOrder.getAuditRemark());
        responseModel.setAuditTime(dbShippingOrder.getAuditTime());
        responseModel.setCarrierFreight(dbShippingOrder.getCarrierFreight());
        responseModel.setCreatedBy(dbShippingOrder.getCreatedBy());
        responseModel.setCreatedTime(dbShippingOrder.getCreatedTime());
        responseModel.setCrossPointFee(dbShippingOrder.getCrossPointFee());
        responseModel.setGoodsUnit(dbShippingOrder.getGoodsUnit());

        responseModel.setShippingOrderCode(dbShippingOrder.getShippingOrderCode());
        responseModel.setShippingOrderId(dbShippingOrder.getId());
        responseModel.setStatus(dbShippingOrder.getStatus());
        responseModel.setVehicleLength(dbShippingOrder.getVehicleLength());

        responseModel.setLoadPointAmount(dbDispatchOrder.getLoadPointAmount());
        responseModel.setOperateLogList(operateModels);
        responseModel.setOrderList(detailItemList);
        responseModel.setUnloadPointAmount(dbDispatchOrder.getUnloadPointAmount());

        if (CompanyTypeEnum.PERSON.getKey().equals(dbDispatchOrder.getCompanyCarrierType())) {
            responseModel.setCompanyCarrierName(dbDispatchOrder.getCarrierContactName() + " " + dbDispatchOrder.getCarrierContactPhone());
        } else {
            responseModel.setCompanyCarrierName(StringUtils.isBlank(dbDispatchOrder.getCompanyCarrierName()) ? "" : dbDispatchOrder.getCompanyCarrierName());
        }
        if (firstShippingOrderItem != null) {
            responseModel.setLoadAddress(firstShippingOrderItem.getLoadProvinceName() + "-" + firstShippingOrderItem.getLoadCityName() + "-" + firstShippingOrderItem.getLoadAreaName());
            responseModel.setUnloadAddress(firstShippingOrderItem.getUnloadProvinceName() + "-" + firstShippingOrderItem.getUnloadCityName() + "-" + firstShippingOrderItem.getUnloadAreaName());
        }

        responseModel.setPointAmount(dbDispatchOrder.getLoadPointAmount().toString() + "装" + dbDispatchOrder.getUnloadPointAmount().toString() + "卸");
        responseModel.setExpectAmount(dbShippingOrder.getExpectAmount());

        return responseModel;
    }

    /**
     * 审核
     * @param requestModel
     * @return
     */
    @Transactional
    public void audit(ShippingOrderAuditRequestModel requestModel){
        //查询运输单
        TShippingOrder dbShippingOrder = getTShippingOrder(requestModel.getShippingOrderId());

        //待审核状态才能操作

        if (! Arrays.asList(ShippingOrderStatusEnum.AUDIT_WAIT.getKey(),ShippingOrderStatusEnum.AUDIT_THROUGH.getKey()).contains(dbShippingOrder.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.SHIPPING_ORDER_CANNOT_OPERATE);
        }

        //如果是已审核驳回 下单运单不能加入对账
        if (ShippingOrderStatusEnum.AUDIT_THROUGH.getKey().equals(dbShippingOrder.getStatus()) && CommonConstant.TWO.equals(requestModel.getOperateType())) {
            //查询运输单明细
            ShippingOrderItemSqlConditionModel itemSqlConditionModel = new ShippingOrderItemSqlConditionModel();
            itemSqlConditionModel.setShippingOrderIds(Collections.singletonList(dbShippingOrder.getId()));
            List<TShippingOrderItem> tShippingOrderItems = tShippingOrderItemMapper.selectByCondition(itemSqlConditionModel);
            List<Long> carrierOrderIds = tShippingOrderItems.stream().map(TShippingOrderItem::getCarrierOrderId).collect(Collectors.toList());
            String carrierOrderIdsJoin = carrierOrderIds.stream().map(Object::toString).collect(Collectors.joining(","));

            //查询运单
            List<TCarrierOrder> tCarrierOrders = tCarrierOrderMapper.selectCarrierOrdersByIds(carrierOrderIdsJoin);
            boolean anyMatch = tCarrierOrders.stream().anyMatch(tCarrierOrder -> !Arrays.asList(CarrierSettleStatementStatusEnum.WAIT_FINISH.getKey(), CarrierSettleStatementStatusEnum.NOT_RELATED.getKey()).contains(tCarrierOrder.getCarrierSettleStatementStatus()));
            if (anyMatch) {
                throw new BizException(CarrierDataExceptionEnum.SYSTEM_ONT_SUPPORT);
            }

            //更新下带运单的对账状态为带完结
            List<TCarrierOrder> updateCarrierOrders = new ArrayList<>();
            for (TCarrierOrder tCarrierOrder : tCarrierOrders) {
                TCarrierOrder updateCarrierOrder = new TCarrierOrder();
                updateCarrierOrder.setId(tCarrierOrder.getId());
                updateCarrierOrder.setCarrierSettleStatementStatus(CarrierSettleStatementStatusEnum.WAIT_FINISH.getKey());
                updateCarrierOrders.add(updateCarrierOrder);
            }
            if (CollectionUtil.isNotEmpty(updateCarrierOrders)) {
                tCarrierOrderMapper.batchUpdateCarrierOrders(updateCarrierOrders);
            }

        }

        String userName = BaseContextHandler.getUserName();
        Date now = new Date();
        Integer status;
        String remark;
        //审核通过
        if (CommonConstant.ONE.equals(requestModel.getOperateType())){
            status = ShippingOrderStatusEnum.AUDIT_THROUGH.getKey();
            remark = "【通过】";
        }
        ////驳回
        else{
            status = ShippingOrderStatusEnum.AUDIT_REJECT.getKey();
            remark = "【驳回】";
        }
        if (StringUtils.isNotBlank(requestModel.getRemark())){
            remark = remark + requestModel.getRemark();
        }

        //更新状态
        TShippingOrder upShippingOrder = new TShippingOrder();
        upShippingOrder.setId(dbShippingOrder.getId());
        upShippingOrder.setStatus(status);
        upShippingOrder.setAuditorName(userName);
        upShippingOrder.setAuditTime(now);
        upShippingOrder.setAuditRemark(requestModel.getRemark());
        commonBiz.setBaseEntityModify(upShippingOrder, userName, now);
        tShippingOrderMapper.updateByPrimaryKeySelective(upShippingOrder);

        //记录操作日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(dbShippingOrder.getId(), OperateLogsOperateTypeEnum.SHIPPING_ORDER_AUDIT, remark, userName);
        tOperateLogsMapper.insertSelective(tOperateLogs);

        //审核通过
        if (CommonConstant.ONE.equals(requestModel.getOperateType())){
            //触发分摊费用
            shippingOrderCommonBiz.checkAndGenerateShippingFee(Collections.singletonList(dbShippingOrder.getId()), null);
        }
    }

    /**
     * 重新报价
     * @param requestModel
     * @return
     */
    @Transactional
    public void reQuote(ShippingOrderReQuoteRequestModel requestModel){
        //查询运输单
        TShippingOrder dbShippingOrder = getTShippingOrder(requestModel.getShippingOrderId());

        //前台
        if (CommonConstant.TWO.equals(requestModel.getSource())){
            //判断车主是否存在
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (companyCarrierId == null || CommonConstant.LONG_ZERO.equals(companyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
            }

            //判断单子是否是该车主的
            TDispatchOrder dbDispatchOrder = tDispatchOrderMapper.selectByPrimaryKeyDecrypt(dbShippingOrder.getDispatchOrderId());
            if (dbDispatchOrder == null || !dbDispatchOrder.getCompanyCarrierId().equals(companyCarrierId)){
                throw new BizException(CarrierDataExceptionEnum.SHIPPING_ORDER_NOT_EXIST);
            }
        }

        //已驳回状态才能操作
        if (!ShippingOrderStatusEnum.AUDIT_REJECT.getKey().equals(dbShippingOrder.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.SHIPPING_ORDER_CANNOT_OPERATE);
        }

        //拼接备注
        String remark = "提交前合计车主费用：" + dbShippingOrder.getCarrierFreight().add(dbShippingOrder.getCrossPointFee()) + "元";

        //更新状态
        TShippingOrder upShippingOrder = new TShippingOrder();
        upShippingOrder.setId(dbShippingOrder.getId());
        upShippingOrder.setStatus(ShippingOrderStatusEnum.AUDIT_WAIT.getKey());
        upShippingOrder.setVehicleLength(requestModel.getVehicleLength());
        upShippingOrder.setCarrierFreight(requestModel.getCarrierFreight());
        upShippingOrder.setCrossPointFee(requestModel.getCrossPointFee());
        commonBiz.setBaseEntityModify(upShippingOrder, BaseContextHandler.getUserName());
        tShippingOrderMapper.updateByPrimaryKeySelective(upShippingOrder);

        //记录操作日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(dbShippingOrder.getId(), OperateLogsOperateTypeEnum.SHIPPING_ORDER_RE_QUOTE, remark, BaseContextHandler.getUserName());
        tOperateLogsMapper.insertSelective(tOperateLogs);

    }

    private TShippingOrder getTShippingOrder(Long shippingOrderId){
        TShippingOrder dbShippingOrder = tShippingOrderMapper.selectByPrimaryKey(shippingOrderId);
        if (dbShippingOrder == null || IfValidEnum.INVALID.getKey().equals(dbShippingOrder.getValid())){
            throw new BizException(CarrierDataExceptionEnum.SHIPPING_ORDER_NOT_EXIST);
        }
        return dbShippingOrder;
    }

    /**
     * 查看路径规划
     * @param requestModel
     * @return
     */
    public List<GetShippingOrderRoutePlanResponseModel> getRoutePlan(GetShippingOrderDetailRequestModel requestModel){
        //查询运输单
        TShippingOrder dbShippingOrder = getTShippingOrder(requestModel.getShippingOrderId());

        //前台
        if (CommonConstant.TWO.equals(requestModel.getSource())){
            //判断车主是否存在
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (companyCarrierId == null || CommonConstant.LONG_ZERO.equals(companyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
            }

            //判断单子是否是该车主的
            TDispatchOrder dbDispatchOrder = tDispatchOrderMapper.selectByPrimaryKeyDecrypt(dbShippingOrder.getDispatchOrderId());
            if (dbDispatchOrder == null || !dbDispatchOrder.getCompanyCarrierId().equals(companyCarrierId)){
                throw new BizException(CarrierDataExceptionEnum.SHIPPING_ORDER_NOT_EXIST);
            }
        }

        List<GetShippingOrderRoutePlanResponseModel> list = new ArrayList<>();
        List<TShippingOrderItem> tShippingOrderItemList = tShippingOrderItemMapper.getRoutePlan(dbShippingOrder.getId());
        if (ListUtils.isNotEmpty(tShippingOrderItemList)){
            GetShippingOrderRoutePlanResponseModel responseModel;
            for (TShippingOrderItem tShippingOrderItem : tShippingOrderItemList) {
                responseModel = new GetShippingOrderRoutePlanResponseModel();
                responseModel.setNextPointDistance(tShippingOrderItem.getNextPointDistance().stripTrailingZeros().toPlainString());
                responseModel.setLongitude(tShippingOrderItem.getLoadLongitude());
                responseModel.setLatitude(tShippingOrderItem.getLoadLatitude());
                list.add(responseModel);
            }
            //将卸货地址的经纬度放在最后
            TShippingOrderItem tShippingOrderItem = tShippingOrderItemList.get(CommonConstant.INTEGER_ZERO);
            responseModel = new GetShippingOrderRoutePlanResponseModel();
            responseModel.setLongitude(tShippingOrderItem.getUnloadLongitude());
            responseModel.setLatitude(tShippingOrderItem.getUnloadLatitude());
            list.add(responseModel);
        }
        return list;
    }

    /**
     * 零担调度-查询可加入的零担
     */
    public List<SearchCanJoinShippingOrderRespModel> searchCanJoinShippingOrder(SearchCanJoinShippingOrderRequestModel requestModel) {
        Long requestDemandId = requestModel.getDemandIdList().get(0);

        List<SearchCanJoinShippingOrderRespModel> searchCanJoinShippingOrderRespModels = tShippingOrderMapper.searchCanJoinShippingOrder(requestModel);
        if (CollectionUtil.isEmpty(searchCanJoinShippingOrderRespModels)) {
            return new ArrayList<>();
        }

        List<Long> demandOrderIds = searchCanJoinShippingOrderRespModels.stream().map(SearchCanJoinShippingOrderRespModel::getDemandOrderId).distinct().collect(Collectors.toList());

        //查询 本次调度其中一个需求单和要加入的需求单 对应需求单详情
        demandOrderIds.add(requestDemandId);
        List<DemandOrderModel> demandOrderList = tDemandOrderMapper.getDemandOrderGoodsByDemandIds(StringUtils.listToString(demandOrderIds, ','));

        DemandOrderModel requestDemandOrderModel = demandOrderList.stream().filter(demandOrderModel -> demandOrderModel.getDemandOrderId().equals(requestDemandId)).findFirst().orElse(null);        Map<Long, DemandOrderModel> needJoinDemandOrderListMap = demandOrderList.stream().collect(Collectors.toMap(DemandOrderModel::getDemandOrderId, Function.identity()));
        Map<Long, DemandOrderModel> demandOrderListMap = demandOrderList.stream().collect(Collectors.toMap(DemandOrderModel::getDemandOrderId, Function.identity()));


        ArrayList<SearchCanJoinShippingOrderRespModel> respModels = new ArrayList<>();
        for (SearchCanJoinShippingOrderRespModel searchCanJoinShippingOrderRespModel : searchCanJoinShippingOrderRespModels) {
            DemandOrderModel needJoinDemandOrderModel = demandOrderListMap.get(searchCanJoinShippingOrderRespModel.getDemandOrderId());
            if (needJoinDemandOrderModel == null) {
                throw new BizException(CarrierDataExceptionEnum.DEMAND_ORDER_NOT_EXIST);
            }

            //回收类型不能与其他类型混合调度
            boolean recycle1 = Arrays.asList(EntrustTypeEnum.RECYCLE_IN.getKey(), EntrustTypeEnum.RECYCLE_OUT.getKey()).contains(requestDemandOrderModel.getEntrustType());
            boolean recycle2 = Arrays.asList(EntrustTypeEnum.RECYCLE_IN.getKey(), EntrustTypeEnum.RECYCLE_OUT.getKey()).contains(needJoinDemandOrderModel.getEntrustType());
            if (recycle1 && !recycle2) {
                continue;
            }
            if (!recycle1 && recycle2) {
                continue;
            }

            //卸货地不同 continue
            if (!requestModel.getUnloadProvinceName().equals(needJoinDemandOrderModel.getUnloadProvinceName())
                    || !requestModel.getUnloadCityName().equals(needJoinDemandOrderModel.getUnloadCityName())
                    || !requestModel.getUnloadAreaName().equals(needJoinDemandOrderModel.getUnloadAreaName())) {
                continue;
            }

            //不同单位的不能一起调度
            if (!requestDemandOrderModel.getGoodsUnit().equals(needJoinDemandOrderModel.getGoodsUnit())) {
                continue;
            }
            //不同车主，不能合并调度车辆
            if (!requestDemandOrderModel.getCompanyCarrierId().equals(needJoinDemandOrderModel.getCompanyCarrierId())) {
                continue;
            }
            respModels.add(searchCanJoinShippingOrderRespModel);
        }


        return respModels;

    }


}
