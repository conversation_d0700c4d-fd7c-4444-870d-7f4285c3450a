package com.logistics.tms.biz.workgroup.sendmsg;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.workgroup.sendmsg.model.WorkGroupFieldTemplate;
import com.logistics.tms.biz.workgroup.sendmsg.model.WorkGroupPushBoModel;
import com.logistics.tms.client.feign.basicdata.wechat.request.PushMessageRequestModel;
import com.logistics.tms.entity.TWorkGroup;
import com.logistics.tms.entity.TWorkGroupDistrict;
import com.logistics.tms.entity.TWorkGroupNode;
import com.logistics.tms.entity.TWorkGroupNodeField;
import com.logistics.tms.mapper.TWorkGroupDistrictMapper;
import com.logistics.tms.mapper.TWorkGroupMapper;
import com.logistics.tms.mapper.TWorkGroupNodeFieldMapper;
import com.logistics.tms.mapper.TWorkGroupNodeMapper;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.MapUtils;
import com.yelo.tools.utils.StringUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ReflectionUtils;

import javax.annotation.Resource;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractWorkGroupPush {

    // 兼容过滤时间要求
    protected static final Integer TIME_REQUIRE_KEY = -999999;

    @Data
    @Accessors(chain = true)
    protected static class WorkGroupAddress {
        private Long loadProvinceId;
        private Long loadCityId;
        private String loadWarehouse;

        private Long unloadProvinceId;
        private Long unloadCityId;
        private String unloadWarehouse;
    }

    @Resource
    private TWorkGroupMapper tWorkGroupMapper;
    @Resource
    private TWorkGroupNodeMapper tWorkGroupNodeMapper;
    @Resource
    private TWorkGroupDistrictMapper tWorkGroupDistrictMapper;
    @Resource
    private TWorkGroupNodeFieldMapper tWorkGroupNodeFieldMapper;

    /**
     * 智能推送 - 发送企业微信群消息
     */
    public List<PushMessageRequestModel> builderWorkGroupPushMessage(WorkGroupPushBoModel boModel) {
        // 匹配推送配置
        Map<TWorkGroup, Long> workGroupMap = matchingWorkGroup(boModel);
        if (MapUtils.isEmpty(workGroupMap)) {
            log.debug(logMsgFunction.apply(boModel, "未匹配到对应推送配置！"));
            return Collections.emptyList();
        }
        // 组装消息体,推送群消息
        return builderPushMessage(boModel, workGroupMap);
    }

    /**
     * 获取订单类型
     */
    public abstract WorkGroupOrderTypeEnum getOrderType();

    /**
     * 获取订单地址
     */
    protected abstract WorkGroupAddress getOrderAddress(Long orderId);

    /**
     * 业务参数过滤
     *
     * @return true 符合 false 未符合
     */
    protected abstract Map<Integer, BigDecimal> businessParamFilter(Long orderId);

    /**
     * 组装业务消息模板
     *
     * @return 消息内容
     */
    protected abstract Object getOrderDetail(Long orderId);

    /**
     * 匹配推送配置
     *
     * @return key -> 配置信息, value -> 节点集合
     */
    private Map<TWorkGroup, Long> matchingWorkGroup(WorkGroupPushBoModel boModel) {

        Long orderId = boModel.getOrderId();
        Integer entrustTypeGroup = boModel.getEntrustTypeGroup().getKey();
        Map<TWorkGroup, Long> matchingWorkGroupMap = Maps.newHashMap();

        // 查询配置信息，通过需求类型、项目标签过滤
        Map<Long, TWorkGroup> workGroupMap = tWorkGroupMapper.selectAllByEntrustTypeGroupDecrypt(entrustTypeGroup)
                .stream()
                .filter(tWorkGroup -> {
                    if (StringUtils.isBlank(boModel.getProjectLabel())){
                        return tWorkGroup.getProjectLabel().contains(CommonConstant.ZERO);
                    }else{
                        List<String> projectLabelList = Arrays.stream(boModel.getProjectLabel().split(CommonConstant.COMMA)).filter(item ->
                                tWorkGroup.getProjectLabel().contains(item)
                        ).collect(Collectors.toList());
                        return ListUtils.isNotEmpty(projectLabelList);
                    }
                })
                .collect(Collectors.toMap(TWorkGroup::getId, Function.identity()));

        // 继续根据条件过滤
        if (!MapUtils.isEmpty(workGroupMap)) {
            //通过配置地点、配置区域过滤，得到最终的推送配置ids
            List<Long> workGroupIds = matchingWorkGroup(workGroupMap, orderId);

            // 查询配置节点信息,业务参数过滤
            if (ListUtils.isNotEmpty(workGroupIds)) {
                //根据workGroupIds、单据类型、单据节点查询配置节点信息
                List<TWorkGroupNode> workGroupNodes = tWorkGroupNodeMapper.selectWorkGroupNodeByWorkGroupIdIn(
                        workGroupIds, boModel.getOrderType().getKey(), boModel.getOrderNode().getKey());

                //业务参数过滤
                if (ListUtils.isNotEmpty(workGroupNodes)) {
                    Map<Integer, BigDecimal> typeAmountMap = businessParamFilter(orderId);
                    workGroupNodes
                            .stream()
                            .filter(f -> typeAmountMap.containsKey(f.getAmountType()))
                            .filter(f -> {
                                //需求单-下单，判断时间要求
                                if (WorkGroupOrderTypeEnum.DEMAND_ORDER.getKey().equals(boModel.getOrderType().getKey())
                                        && WorkGroupOrderNodeEnum.DEMAND_ORDER_CREATE.getKey().equals(boModel.getOrderNode().getKey())) {
                                    return AmountSymbolEnum.LESS_THAN_OR_EQUAL
                                            .compareTo(typeAmountMap.get(TIME_REQUIRE_KEY), new BigDecimal(f.getTimeRequire()));
                                }
                                return true;
                            })
                            .filter(f -> AmountSymbolEnum.getEnum(f.getAmountSymbol())
                                    .compareTo(typeAmountMap.get(f.getAmountType()), f.getAmountRequire()))
                            .forEach(f -> matchingWorkGroupMap.merge(workGroupMap.get(f.getWorkGroupId()), f.getId(),
                                    (v1, v2) -> v2));
                }
            }
        }
        return matchingWorkGroupMap;
    }
    //通过配置地点、配置区域过滤，得到最终的推送配置ids
    private List<Long> matchingWorkGroup(Map<Long, TWorkGroup> workGroupMap, Long orderId){
        List<Long> workGroupIds = Lists.newArrayList(workGroupMap.keySet());
        // 获取订单省/市
        WorkGroupAddress orderAddress = getOrderAddress(orderId);
        // 查询配置省市区
        Map<Long, List<TWorkGroupDistrict>> dbWorkGroupDistrictMap = tWorkGroupDistrictMapper.selectAllByWorkGroupIdInAndAddress(workGroupIds)
                .stream()
                .collect(Collectors.groupingBy(TWorkGroupDistrict::getWorkGroupId));

        //通过配置地点、配置区域过滤，得到最终的推送配置ids
        workGroupIds = workGroupMap.values().stream().filter(tWorkGroup -> {
            //匹配地点：发货信息&收货信息
            if (tWorkGroup.getMatchingLocation().contains(MatchingLocationEnum.LOAD.getKey().toString())
                    && tWorkGroup.getMatchingLocation().contains(MatchingLocationEnum.UNLOAD.getKey().toString())){
                //区域
                if (MatchingFieldEnum.AREA.getKey().equals(tWorkGroup.getMatchingField())){
                    List<TWorkGroupDistrict> dbWorkGroupDistrictList = dbWorkGroupDistrictMap.get(tWorkGroup.getId());
                    if (ListUtils.isEmpty(dbWorkGroupDistrictList)){
                        return false;
                    }
                    dbWorkGroupDistrictList = dbWorkGroupDistrictList.stream().filter(tWorkGroupDistrict ->
                            (tWorkGroupDistrict.getProvinceId().equals(orderAddress.getLoadProvinceId()) && tWorkGroupDistrict.getCityId().equals(orderAddress.getLoadCityId()))
                                    || (tWorkGroupDistrict.getProvinceId().equals(orderAddress.getUnloadProvinceId()) && tWorkGroupDistrict.getCityId().equals(orderAddress.getUnloadCityId()))
                    ).collect(Collectors.toList());
                    return !ListUtils.isEmpty(dbWorkGroupDistrictList);
                }
                //仓库
                else{
                    return tWorkGroup.getConfigWarehouse().equals(orderAddress.getLoadWarehouse()) || tWorkGroup.getConfigWarehouse().equals(orderAddress.getUnloadWarehouse());
                }
            }
            //匹配地点：发货信息
            else if (tWorkGroup.getMatchingLocation().contains(MatchingLocationEnum.LOAD.getKey().toString())){
                //区域
                if (MatchingFieldEnum.AREA.getKey().equals(tWorkGroup.getMatchingField())){
                    List<TWorkGroupDistrict> dbWorkGroupDistrictList = dbWorkGroupDistrictMap.get(tWorkGroup.getId());
                    if (ListUtils.isEmpty(dbWorkGroupDistrictList)){
                        return false;
                    }
                    dbWorkGroupDistrictList = dbWorkGroupDistrictList.stream().filter(tWorkGroupDistrict ->
                            tWorkGroupDistrict.getProvinceId().equals(orderAddress.getLoadProvinceId()) && tWorkGroupDistrict.getCityId().equals(orderAddress.getLoadCityId())
                    ).collect(Collectors.toList());
                    return !ListUtils.isEmpty(dbWorkGroupDistrictList);
                }
                //仓库
                else{
                    return tWorkGroup.getConfigWarehouse().equals(orderAddress.getLoadWarehouse());
                }
            }
            //匹配地点：收货信息
            else{
                //区域
                if (MatchingFieldEnum.AREA.getKey().equals(tWorkGroup.getMatchingField())){
                    List<TWorkGroupDistrict> dbWorkGroupDistrictList = dbWorkGroupDistrictMap.get(tWorkGroup.getId());
                    if (ListUtils.isEmpty(dbWorkGroupDistrictList)){
                        return false;
                    }
                    dbWorkGroupDistrictList = dbWorkGroupDistrictList.stream().filter(tWorkGroupDistrict ->
                            tWorkGroupDistrict.getProvinceId().equals(orderAddress.getUnloadProvinceId()) && tWorkGroupDistrict.getCityId().equals(orderAddress.getUnloadCityId())
                    ).collect(Collectors.toList());
                    return !ListUtils.isEmpty(dbWorkGroupDistrictList);
                }
                //仓库
                else{
                    return tWorkGroup.getConfigWarehouse().equals(orderAddress.getUnloadWarehouse());
                }
            }
        }).map(TWorkGroup::getId).collect(Collectors.toList());
        return workGroupIds;
    }

    /**
     * 构建消息
     *
     * @param boModel      业务参数
     * @param workGroupMap 推送配置 key -> 配置信息, value -> 节点集合
     */
    private List<PushMessageRequestModel> builderPushMessage(WorkGroupPushBoModel boModel, Map<TWorkGroup, Long> workGroupMap) {

        Collection<Long> workGroupNodeIds = workGroupMap.values();
        ArrayListMultimap<Long, TWorkGroupNodeField> workGroupNodeFieldMap = ArrayListMultimap.create();
        Map<String, String> filedMap = WorkGroupFieldTemplate.getFiledByOrderType(boModel.getOrderType());
        String msgFormat = "【%s】: %s \n";

        // 查询消息字段表
        tWorkGroupNodeFieldMapper.selectAllByWorkGroupNodeIdIn(workGroupNodeIds)
                .stream()
                .filter(f -> filedMap.containsKey(f.getField()))
                .forEach(f -> workGroupNodeFieldMap.put(f.getWorkGroupNodeId(), f));
        if (workGroupNodeFieldMap.isEmpty()) {
            log.error(logMsgFunction.apply(boModel, "消息推送异常，未获取到节点模板字段！"));
            return Collections.emptyList();
        }

        // 获取订单详情
        Object orderDetail = getOrderDetail(boModel.getOrderId());
        if (Objects.isNull(orderDetail)) {
            log.error(logMsgFunction.apply(boModel, "消息推送异常，未获取到订单信息！"));
            return Collections.emptyList();
        }
        Class<?> aClass = orderDetail.getClass();
        List<PushMessageRequestModel> wechatPushMessageRequests = Lists.newArrayList();
        PushMessageRequestModel pushMessageRequestModel;

        for (Map.Entry<TWorkGroup, Long> entry : workGroupMap.entrySet()) {
            TWorkGroup workGroup = entry.getKey();
            Long workGroupNodeId = entry.getValue();
            List<TWorkGroupNodeField> workGroupNodeFields = workGroupNodeFieldMap.get(workGroupNodeId);
            if (ListUtils.isEmpty(workGroupNodeFields)) {
                log.error(logMsgFunction.apply(boModel, "消息推送异常，未获取到节点模板字段！"));
                continue;
            }

            StringBuilder stringBuilder = new StringBuilder(String.format(msgFormat, boModel.getOrderType().getValue(),
                    boModel.getOrderNode().getValue()));
            int fieldErrorSize = 0;
            for (TWorkGroupNodeField workGroupNodeField : workGroupNodeFields) {
                String field = workGroupNodeField.getField();
                String fieldName = filedMap.get(field);
                PropertyDescriptor pd;
                Object fieldValue;
                try {
                    pd = new PropertyDescriptor(field, aClass);
                } catch (IntrospectionException e) {
                    log.warn("【智能推送】 -------------------- {}ID【{}】【{}】节点，填充模板【{}】属性获取失败！",
                            boModel.getEntrustTypeGroup().getValue(),
                            boModel.getOrderId(),
                            boModel.getOrderNode().getValue(),
                            workGroupNodeField.getField());
                    stringBuilder.append(String.format(msgFormat, fieldName, ""));
                    fieldErrorSize++;
                    continue;
                }
                Method method = pd.getReadMethod();
                fieldValue = ReflectionUtils.invokeMethod(method, orderDetail);
                stringBuilder.append(String.format(msgFormat, fieldName, Objects.isNull(fieldValue) ? "" : fieldValue));
            }
            if (fieldErrorSize == workGroupNodeFields.size()) {
                log.error(logMsgFunction.apply(boModel, "填充模板属性失败！"));
                continue;
            }
            // 移除换行符
            String message = stringBuilder.deleteCharAt(stringBuilder.length() - 1).toString();

            //构建消息
            pushMessageRequestModel = new PushMessageRequestModel();
            pushMessageRequestModel.setMsgType(PushMessageRequestModel.PushMessageMsgType.text.name());
            pushMessageRequestModel.setMessage(message);
            //新建的群聊：通过群id发送消息
            if (WorkGroupSourceEnum.CREATE.getKey().equals(workGroup.getWorkGroupSource())) {
                pushMessageRequestModel.setChatId(workGroup.getWorkGroupCode());
            }
            //已有群聊：通过机器人发送消息
            else{
                pushMessageRequestModel.setRbId(workGroup.getWorkGroupCode());
            }
            wechatPushMessageRequests.add(pushMessageRequestModel);
        }
        return wechatPushMessageRequests;
    }

    private final BiFunction<WorkGroupPushBoModel, String, String> logMsgFunction = (event, s) ->
            String.format("【智能推送】 -------------------- %sID【%s】【%s】节点，%s",
                    event.getOrderType().getValue(),
                    event.getOrderId(),
                    event.getOrderNode().getValue(),
                    s);
}
