package com.logistics.tms.biz.reservationorder;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.EnabledEnum;
import com.logistics.tms.base.enums.ReservationOrderStatusEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.reservationorder.response.ReservationCarrierOrderListResponseModel;
import com.logistics.tms.entity.TReservationOrder;
import com.logistics.tms.entity.TReservationOrderItem;
import com.logistics.tms.mapper.TReservationOrderItemMapper;
import com.logistics.tms.mapper.TReservationOrderMapper;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.logistics.tms.rabbitmq.publisher.model.SyncReservationCarrierOrderInvalidToWarehouseItem;
import com.logistics.tms.rabbitmq.publisher.model.SyncReservationCarrierOrderInvalidToWarehouseModel;
import com.logistics.tms.rabbitmq.publisher.model.SyncReservationOrderInvalidToWarehouseModel;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2024/8/23 10:10
 */
@Service
public class ReservationOrderCommonBiz {

    @Resource
    private TReservationOrderMapper tReservationOrderMapper;
    @Resource
    private TReservationOrderItemMapper tReservationOrderItemMapper;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private RabbitMqPublishBiz rabbitMqPublishBiz;

    /**
     * 预约单解除关联运单
     * @param carrierOrderIdList
     */
    @Transactional
    public void disassociateCarrierOrder(List<Long> carrierOrderIdList){
        if (ListUtils.isEmpty(carrierOrderIdList)){
            return;
        }
        //查询运单是否绑定了【待签到】的预约单
        List<TReservationOrderItem> tReservationOrderItemList = tReservationOrderItemMapper.getWaitSignIn(carrierOrderIdList);
        if (ListUtils.isEmpty(tReservationOrderItemList)){
            return;
        }
        String userName = BaseContextHandler.getUserName();
        Date now = new Date();

        //将预约单明细置为禁用
        TReservationOrderItem upReservationOrderItem;
        List<TReservationOrderItem> upReservationOrderItemList = new ArrayList<>();
        List<String> carrierOrderCodes = new ArrayList<>();
        List<Long> reservationOrderIdList = new ArrayList<>();
        for (TReservationOrderItem tReservationOrderItem : tReservationOrderItemList) {
            upReservationOrderItem = new TReservationOrderItem();
            upReservationOrderItem.setId(tReservationOrderItem.getId());
            upReservationOrderItem.setEnabled(EnabledEnum.DISABLED.getKey());
            commonBiz.setBaseEntityModify(upReservationOrderItem, userName, now);
            upReservationOrderItemList.add(upReservationOrderItem);

            if (!reservationOrderIdList.contains(tReservationOrderItem.getReservationOrderId())){
                reservationOrderIdList.add(tReservationOrderItem.getReservationOrderId());
            }

            carrierOrderCodes.add(tReservationOrderItem.getCarrierOrderCode());
        }
        tReservationOrderItemMapper.batchUpdateSelective(upReservationOrderItemList);

        //查询预约单下明细全部启用的预约单
        List<TReservationOrder> tReservationOrderList = tReservationOrderMapper.getEnabledOrder(reservationOrderIdList);
        List<TReservationOrder> upReservationOrderList = new ArrayList<>();
        if (ListUtils.isEmpty(tReservationOrderList)){
            for (Long id : reservationOrderIdList) {
                TReservationOrder upReservationOrder = new TReservationOrder();
                upReservationOrder.setId(id);
                upReservationOrder.setStatus(ReservationOrderStatusEnum.INVALID.getKey());
                commonBiz.setBaseEntityModify(upReservationOrder, userName, now);
                upReservationOrderList.add(upReservationOrder);
            }
            tReservationOrderMapper.batchUpdateSelective(upReservationOrderList);
            //预约单失效同步云仓
            SyncReservationOrderInvalidToWarehouseModel warehouseModel = new SyncReservationOrderInvalidToWarehouseModel();

            warehouseModel.setReservationCodes(
                    tReservationOrderMapper.getOrderByIds(reservationOrderIdList).
                            stream()
                            .map(TReservationOrder::getReservationOrderCode).collect(Collectors.toList()));
            warehouseModel.setUserName(userName);
            rabbitMqPublishBiz.syncReservationOrderInvalidToWarehouse(warehouseModel);
            return;
        }else {
            List<Long> noValidOrders = tReservationOrderList.stream().map(TReservationOrder::getId).collect(Collectors.toList());
            reservationOrderIdList.removeAll(noValidOrders);
            if(ListUtils.isNotEmpty(reservationOrderIdList)){
                for (Long id : reservationOrderIdList) {
                    TReservationOrder upReservationOrder = new TReservationOrder();
                    upReservationOrder.setId(id);
                    upReservationOrder.setStatus(ReservationOrderStatusEnum.INVALID.getKey());
                    commonBiz.setBaseEntityModify(upReservationOrder, userName, now);
                    upReservationOrderList.add(upReservationOrder);
                }
                tReservationOrderMapper.batchUpdateSelective(upReservationOrderList);
                //预约单失效同步云仓
                SyncReservationOrderInvalidToWarehouseModel warehouseModel = new SyncReservationOrderInvalidToWarehouseModel();

                warehouseModel.setReservationCodes(
                        tReservationOrderMapper.getOrderByIds(reservationOrderIdList).
                                stream()
                                .map(TReservationOrder::getReservationOrderCode).collect(Collectors.toList()));
                warehouseModel.setUserName(userName);
                rabbitMqPublishBiz.syncReservationOrderInvalidToWarehouse(warehouseModel);
                return;
            }else {
                //预约单下运单失效同步云仓
                SyncReservationCarrierOrderInvalidToWarehouseModel warehouseModel = new SyncReservationCarrierOrderInvalidToWarehouseModel();
                List<SyncReservationCarrierOrderInvalidToWarehouseItem> items = new ArrayList<>();
                tReservationOrderItemList.stream().forEach(e->{
                    SyncReservationCarrierOrderInvalidToWarehouseItem invalidToWarehouseItem = new SyncReservationCarrierOrderInvalidToWarehouseItem();
                    invalidToWarehouseItem.setReservationOrderCode(tReservationOrderMapper.selectByPrimaryKey(e.getReservationOrderId()).getReservationOrderCode());
                    invalidToWarehouseItem.setCarrierOrderCode(e.getCarrierOrderCode());
                    items.add(invalidToWarehouseItem);
                });
                warehouseModel.setItems(items);
                warehouseModel.setUserName(userName);
                rabbitMqPublishBiz.syncReservationCarrierOrderInvalidToWarehouse(warehouseModel);
            }
        }
    }

    /**
     * 预约单到期未签到后自动失效
     */
    public void reservationOrderInvalid(){
        //查询预约时间已过但未签到的预约单
        List<TReservationOrder> dbReservationOrderList = tReservationOrderMapper.getWaitSignInAndReservationTimeInvalid();
        if (ListUtils.isEmpty(dbReservationOrderList)){
            return;
        }
        String userName = CommonConstant.SYSTEM;
        Date now = new Date();

        //将预约单置为失效
        TReservationOrder upReservationOrder;
        List<TReservationOrder> upReservationOrderList = new ArrayList<>();
        List<String> reservationCodes = new ArrayList<>();
        List<Long> reservationIds = new ArrayList<>();
        for (TReservationOrder tReservationOrder : dbReservationOrderList) {
            reservationIds.add(tReservationOrder.getId());
            upReservationOrder = new TReservationOrder();
            upReservationOrder.setId(tReservationOrder.getId());
            upReservationOrder.setStatus(ReservationOrderStatusEnum.INVALID.getKey());
            commonBiz.setBaseEntityModify(upReservationOrder, userName, now);
            upReservationOrderList.add(upReservationOrder);

            reservationCodes.add(tReservationOrder.getReservationOrderCode());
        }

        List<TReservationOrderItem> orderByReservationOrderIds = tReservationOrderItemMapper.getOrderByReservationOrderIds(reservationIds);
        if (ListUtils.isNotEmpty(orderByReservationOrderIds)){
            List<TReservationOrderItem> updateItems = new ArrayList<>();
            orderByReservationOrderIds.forEach(e->{
                TReservationOrderItem tReservationOrderItem = new TReservationOrderItem();
                tReservationOrderItem.setId(e.getId());
                tReservationOrderItem.setEnabled(EnabledEnum.DISABLED.getKey());
                commonBiz.setBaseEntityModify(tReservationOrderItem, userName, now);
                updateItems.add(tReservationOrderItem);
            });

            tReservationOrderItemMapper.batchUpdateSelective(updateItems);
        }
        if (ListUtils.isNotEmpty(upReservationOrderList)) {
            tReservationOrderMapper.batchUpdateSelective(upReservationOrderList);
        }
        //预约单失效同步云仓
        SyncReservationOrderInvalidToWarehouseModel warehouseModel = new SyncReservationOrderInvalidToWarehouseModel();
        warehouseModel.setReservationCodes(reservationCodes);
        warehouseModel.setUserName(userName);
        rabbitMqPublishBiz.syncReservationOrderInvalidToWarehouse(warehouseModel);

    }
}
