package com.logistics.management.webapi.api.impl.entrustsettlement.mapping;

import com.logistics.management.webapi.api.feign.entrustsettlement.dto.GetSettlementDetailRowDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.tms.api.feign.entrustsettlement.model.GetSettlementDetailRowModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2019/10/11 19:52
 */
public class SettlementDetailMapping extends MapperMapping<GetSettlementDetailRowModel,GetSettlementDetailRowDto> {
    @Override
    public void configure() {
        GetSettlementDetailRowModel source = getSource();
        GetSettlementDetailRowDto destination = getDestination();
        if (source != null){
            destination.setSettlementAmount(source.getSettlementAmount().stripTrailingZeros().toPlainString()+ GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());
            destination.setSettlementCostTotal(source.getSettlementCostTotal().setScale(2, BigDecimal.ROUND_HALF_UP)+ CommonConstant.YUAN);
        }
    }
}
