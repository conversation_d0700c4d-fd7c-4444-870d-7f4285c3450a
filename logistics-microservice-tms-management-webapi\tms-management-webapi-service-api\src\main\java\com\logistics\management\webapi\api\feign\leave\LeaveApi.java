package com.logistics.management.webapi.api.feign.leave;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.leave.dto.*;
import com.logistics.management.webapi.api.feign.leave.hystrix.LeaveApiHystrix;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;

/**
 * 请假管理api
 */
@Api(value = "API - LeaveApi - 请假管理", tags = "请假管理")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = LeaveApiHystrix.class)
public interface LeaveApi {

    @ApiOperation(value = "查询请假记录列表", tags = "1.1.8")
    @PostMapping(value = "/api/leave/searchLeaveApplyList")
    Result<PageInfo<SearchLeaveListResponseDto>> searchLeaveApplyList(@RequestBody SearchLeaveListRequestDto requestDto);

    @ApiOperation(value = "导出请假记录列表", tags = "1.1.8")
    @PostMapping(value = "/api/leave/exportLeaveApplyList")
    void exportLeaveApplyList(@RequestBody SearchLeaveListRequestDto requestDto, HttpServletResponse response);

    @ApiOperation(value = "查询请假记录详情", tags = "1.1.8")
    @PostMapping(value = "/api/leave/leaveApplyDetail")
    Result<LeaveDetailResponseDto> leaveApplyDetail(@RequestBody LeaveDetailRequestDto requestDto);

    @ApiOperation(value = "审核请假申请", tags = "1.1.8")
    @PostMapping(value = "/api/leave/auditLeaveApply")
    Result<Boolean> auditLeaveApply(@RequestBody AuditLeaveApplyRequestDto requestDto);

    @ApiOperation(value = "撤销请假申请", tags = "1.1.8")
    @PostMapping(value = "/api/leave/cancelLeaveApply")
    Result<Boolean> cancelLeaveApply(@RequestBody CancelLeaveApplyRequestDto requestDto);
}
