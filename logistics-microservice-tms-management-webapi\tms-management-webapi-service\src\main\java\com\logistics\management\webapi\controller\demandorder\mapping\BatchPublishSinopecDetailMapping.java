package com.logistics.management.webapi.controller.demandorder.mapping;

import com.logistics.management.webapi.client.demandorder.response.BatchPublishSinopecDetailResponseModel;
import com.logistics.management.webapi.client.demandorder.response.BatchPublishSinopecResponseModel;
import com.logistics.management.webapi.controller.demandorder.response.BatchPublishSinopecDetailResponseDto;
import com.logistics.management.webapi.controller.demandorder.response.BatchPublishSinopecResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;

import java.util.List;

/**
 * @author: wei.wang
 * @date: 2021/12/5
 */
public class BatchPublishSinopecDetailMapping extends MapperMapping<BatchPublishSinopecResponseModel, BatchPublishSinopecResponseDto> {
	@Override
	public void configure() {
		BatchPublishSinopecResponseModel source = getSource();
		BatchPublishSinopecResponseDto destination = getDestination();

		//去掉BigDecimal后面无效的0
		destination.setGoodsAmountCount(source.getGoodsAmountCount().stripTrailingZeros().toPlainString());

		List<BatchPublishSinopecDetailResponseModel> sinopecDemands = source.getSinopecDemands();
		destination.setSinopecDemands(MapperUtils.mapper(sinopecDemands, BatchPublishSinopecDetailResponseDto.class, new MapperMapping<BatchPublishSinopecDetailResponseModel, BatchPublishSinopecDetailResponseDto>() {
			@Override
			public void configure() {
				BatchPublishSinopecDetailResponseModel source1 = getSource();
				BatchPublishSinopecDetailResponseDto destination1 = getDestination();
				destination1.setGoodsAmount(source1.getGoodsAmount().stripTrailingZeros().toPlainString());
			}
		}));
	}
}
