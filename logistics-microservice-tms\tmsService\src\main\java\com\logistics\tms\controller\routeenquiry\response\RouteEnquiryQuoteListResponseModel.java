package com.logistics.tms.controller.routeenquiry.response;

import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2024/7/9 17:16
 */
@Data
public class RouteEnquiryQuoteListResponseModel {

    /**
     * 路线询价单车主表id
     */
    private Long routeEnquiryCompanyId;

    /**
     * 车主公司类型：1 公司，2 个人
     */
    private Integer companyCarrierType;

    /**
     * 车主公司名称
     */
    private String companyCarrierName;

    /**
     * 车主账号名称
     */
    private String carrierContactName;

    /**
     * 车主账号手机号
     */
    private String carrierContactPhone;

    /**
     * 报价时间
     */
    private Date quoteTime;

    /**
     * 报价状态：0 未选择，1 已选择，2 已取消
     */
    private Integer quoteStatus;

}
