package com.logistics.management.webapi.controller.freightconfig.request.address;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CarrierFreightAddressDetailRequestDto {

    @ApiModelProperty(value = "路线计价配置ID", required = true)
    @NotBlank(message = "路线计价配置ID不允许为空")
    private String freightConfigAddressId;
}
