package com.logistics.tms.controller.drivercostapply;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.violationregulation.model.FuzzyQueryDriverInfoRequestModel;
import com.logistics.tms.api.feign.violationregulation.model.FuzzyQueryDriverInfoResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.StaffTypeEnum;
import com.logistics.tms.biz.drivercostapply.DriverCostApplyBiz;
import com.logistics.tms.biz.reservebalance.ReserveBalanceBiz;
import com.logistics.tms.biz.staff.StaffManagementBiz;
import com.logistics.tms.controller.drivercostapply.request.*;
import com.logistics.tms.controller.drivercostapply.response.*;
import com.logistics.tms.entity.TReserveBalance;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/5
 */
@RestController
@RequestMapping(value = "/service/driverCostApply")
public class DriverCostApplyController {

	@Resource
	private DriverCostApplyBiz driverCostApplyBiz;
	@Resource
	private StaffManagementBiz staffManagementBiz;

	@Resource
	private ReserveBalanceBiz reserveBalanceBiz;

	/**
	 * 费用申请列表
	 *
	 * @param requestModel 筛选条件
	 * @return 费用申请列表
	 */
    @PostMapping(value = "/searchCostApplyList")
	public Result<PageInfo<SearchCostApplyListResponseModel>> searchCostApplyList(@RequestBody SearchCostApplyListRequestModel requestModel) {
		return Result.success(driverCostApplyBiz.searchCostApplyList(requestModel));
	}

	/**
	 * 申请记录详情
	 *
	 * @param requestModel 司机费用申请表id
	 * @return 申请记录详情
	 */
    @PostMapping(value = "/driverCostApplyDetail")
	public Result<DriverCostApplyDetailResponseModel> driverCostApplyDetail(@RequestBody DriverCostApplyDetailRequestModel requestModel) {
		return Result.success(driverCostApplyBiz.driverCostApplyDetail(requestModel));
	}

	/**
	 * 撤销费用申请
	 *
	 * @param requestModel 司机费用申请表id
	 * @return 操作结果
	 */
    @PostMapping(value = "/undoDriverCostApply")
	public Result<Boolean> undoDriverCostApply(@RequestBody UndoDriverCostApplyRequestModel requestModel) {
		driverCostApplyBiz.undoDriverCostApply(requestModel);
		return Result.success(true);
	}

	/**
	 * 审核/驳回费用申请
	 *
	 * @param requestModel 司机费用申请表id
	 * @return 操作结果
	 */
    @PostMapping(value = "/auditOrRejectCostApply")
	public Result<Boolean> auditOrRejectCostApply(@RequestBody AuditOrRejectCostApplyRequestModel requestModel) {
		driverCostApplyBiz.auditOrRejectCostApply(requestModel);
		return Result.success(true);
	}

	/**
	 * 费用申请汇总列表
	 *
	 * @param requestModel 筛选条件
	 * @return 费用申请汇总列表
	 */
    @PostMapping(value = "/searchCostApplySummary")
	public Result<PageInfo<SearchCostApplySummaryResponseModel>> searchCostApplySummary(@RequestBody SearchCostApplySummaryRequestModel requestModel) {
		return Result.success(driverCostApplyBiz.searchCostApplySummary(requestModel));
	}

	/**
	 * 小程序费用申请列表
	 *
	 * @param requestModel 申请时间
	 * @return 司机费用费用申请列表
	 */
    @PostMapping(value = "/searchCostApplyListForApplet")
	public Result<SearchCostApplyListCountForAppletResponseModel> searchCostApplyListForApplet(@RequestBody SearchCostApplyListForAppletRequestModel requestModel) {
		return Result.success(driverCostApplyBiz.searchCostApplyListForApplet(requestModel));
	}

	/**
	 * 小程序提交（重新提交）费用申请
	 *
	 * @param requestModel 费用申请信息
	 * @return 操作结果
	 */
    @PostMapping(value = "/addDriverCostApply")
	public Result<Boolean> addDriverCostApply(@RequestBody AddDriverCostApplyRequestModel requestModel) {
		driverCostApplyBiz.addDriverCostApply(requestModel);
		return Result.success(true);
	}

	/**
	 * 司机扣款费用申请
	 *
	 * @param requestModel 请求Model
	 * @return boolean
	 */
	@PostMapping(value = "/deductionsCostApply")
	public Result<Boolean> deductionsCostApply(@RequestBody DeductionsCostApplyRequestModel requestModel) {
		driverCostApplyBiz.deductionsCostApply(requestModel);
		return Result.success(Boolean.TRUE);
	}

	@ApiOperation(value = "查询司机扣款余额", tags = "1.3.6")
	@PostMapping(value = "/getDeductionsCostBalance")
	public Result<List<GetDeductionsCostBalanceResponseModel>> getDeductionsCostBalance(@RequestBody GetDeductionsCostBalanceRequestModel requestDto) {
		FuzzyQueryDriverInfoRequestModel fuzzyQueryDriverInfoRequestModel = new FuzzyQueryDriverInfoRequestModel();
		fuzzyQueryDriverInfoRequestModel.setType(StaffTypeEnum.DRIVER.getKey() + "," + StaffTypeEnum.DRIVER_SUPERCARGO.getKey());
		fuzzyQueryDriverInfoRequestModel.setFuzzyDriverField(requestDto.getFuzzyDriverField());
		fuzzyQueryDriverInfoRequestModel.setPageNum(CommonConstant.INTEGER_ZERO);
		fuzzyQueryDriverInfoRequestModel.setPageSize(CommonConstant.INTEGER_ZERO);
		PageInfo<FuzzyQueryDriverInfoResponseModel> fuzzyQueryDriverInfoResponseModelPageInfo = staffManagementBiz.fuzzyQueryDriverInfo(fuzzyQueryDriverInfoRequestModel);
		List<FuzzyQueryDriverInfoResponseModel> fuzzyQueryDriverInfoResponseModels = fuzzyQueryDriverInfoResponseModelPageInfo.getList();
		if(ListUtils.isNotEmpty(fuzzyQueryDriverInfoResponseModels)){
			List<Long> ids =
					fuzzyQueryDriverInfoResponseModels.stream()
							.map(FuzzyQueryDriverInfoResponseModel::getDriverId).collect(Collectors.toList());
			Map<Long, TReserveBalance> mapByDriverId =
					reserveBalanceBiz.getBalanceByDriverIds(ids)
							.stream().collect(Collectors.toMap(TReserveBalance::getDriverId, e -> e));
			List<GetDeductionsCostBalanceResponseModel> responseModels = fuzzyQueryDriverInfoResponseModels.stream().map(e -> {
				GetDeductionsCostBalanceResponseModel responseModel = new GetDeductionsCostBalanceResponseModel();
				responseModel.setDriverId(e.getDriverId());
				responseModel.setDriverName(e.getDriverName());
				responseModel.setDriverPhone(e.getDriverPhone());
				TReserveBalance tReserveBalance = mapByDriverId.get(responseModel.getDriverId());
				if(Objects.nonNull(tReserveBalance)){
					responseModel.setBalance(tReserveBalance.getBalanceAmount());
				}

				return responseModel;
			}).collect(Collectors.toList());
			return Result.success(responseModels);
		}
		return Result.success(new ArrayList<>());
	}

	/**
	 * 费用申请红冲退款
	 *
	 * @param requestModel 请求Model
	 * @return 操作结果
	 */
	@PostMapping(value = "/redChargeRefund")
	public Result<Boolean> redChargeRefund(@RequestBody RedChargeRefundCostApplyRequestModel requestModel) {
		driverCostApplyBiz.redChargeRefund(requestModel);
		return Result.success(true);
	}
}
