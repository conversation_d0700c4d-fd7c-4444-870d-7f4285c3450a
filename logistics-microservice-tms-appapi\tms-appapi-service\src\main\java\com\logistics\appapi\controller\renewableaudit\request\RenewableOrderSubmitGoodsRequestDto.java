package com.logistics.appapi.controller.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class RenewableOrderSubmitGoodsRequestDto {

	@ApiModelProperty(value = "乐橘新生订单审核表id", required = true)
	@NotBlank(message = "新生订单id不能为空")
	private String renewableAuditId;

	@Valid
	@ApiModelProperty(value = "新生订单货物信息", required = true)
	@NotEmpty(message = "货物不能为空，每单最多5种货物")
	@Size(min = 1, max = 5, message = "货物不能为空，每单最多5种货物")
	private List<RenewableOrderGoodRequestDto> renewableOrderGoods;
}
