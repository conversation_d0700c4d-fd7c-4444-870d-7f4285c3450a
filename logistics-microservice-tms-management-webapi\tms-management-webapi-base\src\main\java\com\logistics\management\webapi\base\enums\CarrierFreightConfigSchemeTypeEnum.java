package com.logistics.management.webapi.base.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum CarrierFreightConfigSchemeTypeEnum {
    DEFAULT(-1, ""),
    ROUTE_CONFIG(100, "路线配置"),
    REGION_CONFIG(200, "同区-跨区价格"),
    REGION_SAME_CONFIG(201, "同区价格"),
    REGION_DIFFERENT_CONFIG(202, "跨区价格"),
    CALCULATIONS_DISTANCE_CONFIG(301, "系统计算预计距离"),
    DISTANCE_CONFIG(302, "系统配置距离"),
    ;
    private final Integer key;

    private final String value;

    public static CarrierFreightConfigSchemeTypeEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }

    public static List<Integer> getRegionType() {
        return Lists.newArrayList(REGION_CONFIG.key, REGION_SAME_CONFIG.key, REGION_DIFFERENT_CONFIG.key);
    }
}
