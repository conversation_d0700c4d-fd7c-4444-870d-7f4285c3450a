package com.logistics.tms.controller.driveraccount;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.driveraccount.DriverAccountBiz;
import com.logistics.tms.controller.driveraccount.request.*;
import com.logistics.tms.controller.driveraccount.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/8 9:17
 */
@Api(value = "司机账户管理")
@RestController
public class DriverAccountController {

    @Resource
    private DriverAccountBiz driverAccountBiz;

    /**
     * 新增/修改司机账户
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "新增/修改司机账户")
    @PostMapping("/service/driverAccount/addAccount")
    public Result<Boolean> addAccount(@RequestBody DriverAccountAddRequestModel requestModel) {
        driverAccountBiz.addAccount(requestModel);
        return Result.success(true);
    }

    /**
     * 查询司机账户列表
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "司机账户列表")
    @PostMapping("/service/driverAccount/searchList")
    public Result<PageInfo<SearchDriverAccountResponseModel>> searchList(@RequestBody SearchDriverAccountRequestModel requestModel) {
        return Result.success(driverAccountBiz.searchList(requestModel));
    }

    /**
     * 司机账户详情
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "司机账户详情")
    @PostMapping("/service/driverAccount/getDetail")
    public Result<DriverAccountDetailResponseModel> getDetail(@RequestBody DriverAccountDetailRequestModel requestModel) {
        return Result.success(driverAccountBiz.getDetail(requestModel));
    }

    /**
     * 查询司机账户收款证件
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查询司机账户收款证件")
    @PostMapping("/service/driverAccount/getAccountImageList")
    public Result<DriverAccountImageResponseModel> getAccountImageList(@RequestBody DriverAccountImageRequestModel requestModel) {
        return Result.success(driverAccountBiz.getAccountImageList(requestModel));
    }

    /**
     * 操作日志列表
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "操作日志列表")
    @PostMapping("/service/driverAccount/getOperateLogList")
    public Result<List<DriverAccountOperateLogResponseModel>> getOperateLogList(@RequestBody DriverAccountOperateLogRequestModel requestModel) {
        return Result.success(driverAccountBiz.getOperateLogList(requestModel));
    }

    /**
     * 小程序司机添加银行卡
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "小程序添加/修改银行卡")
    @PostMapping(value = "/service/driverApplet/bankCard/addBankCard")
    public Result<Boolean> addBankCardForApplet(@RequestBody AddBankCardAppletRequestModel requestModel) {
        driverAccountBiz.addBankCardForApplet(requestModel);
        return Result.success(true);
    }

    /**
     * 小程序获取当前登录司机绑定的银行卡信息
     *
     * @return
     */
    @ApiOperation(value = "当前绑定银行卡信息查询")
    @PostMapping(value = "/service/driverApplet/bankCard/currBankCardInfo")
    public Result<BankCardInfoResponseModel> currBankCardInfo() {
        return Result.success(driverAccountBiz.currBankCardInfo());
    }
}
