package com.logistics.management.webapi.controller.workgroup.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class AddEditWorkGroupNodeRequestDto {

    @ApiModelProperty(value = "智能推送配置表id", required = true)
    @NotBlank(message = "请选择配置")
    private String workGroupId;

    @Valid
    @NotEmpty(message = "请选择需求类型")
    @ApiModelProperty(value = "(3.15.1)需求类型：1.回收业务(回收入库、回收出库) 2.采购业务（供应商直配、采购) 3.仓库业务（发货、调拨、退货、退货仓库送、退货调拨、其他）", required = true)
    @Size(max = 3, message = "请选正确的需求类型")
    private List<@Pattern(regexp = "^([123])$", message = "请选择正确的需求类型") String> entrustTypeList;

    @Valid
    @ApiModelProperty(value = "(3.15.1)项目标签：0 标签为空，1 石化板块，2 轮胎板块，3 涂料板块，4 其他板块", required = true)
    @NotEmpty(message = "请选择项目标签")
    @Size(max = 5, message = "请选正确的项目标签")
    private List<@Pattern(regexp = "^([01234])$", message = "请选择正确的项目标签") String> projectLabelList;

    @Valid
    @NotEmpty(message = "请添加节点")
    @ApiModelProperty(value = "节点信息", required = true)
    private List<AddEditWorkGroupNodeListRequestDto> nodeList;
}
