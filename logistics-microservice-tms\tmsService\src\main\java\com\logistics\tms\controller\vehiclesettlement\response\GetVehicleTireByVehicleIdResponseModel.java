package com.logistics.tms.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/10/10 19:32
 */
@Data
public class GetVehicleTireByVehicleIdResponseModel {
    private Long vehicleTireId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机姓名")
    private String driverName;
    private String driverMobile;
    @ApiModelProperty("更换日期")
    private Date replaceDate;
    @ApiModelProperty("轮胎企业")
    private String tireCompany;
    @ApiModelProperty("轮胎牌号")
    private String tireBrand;
    @ApiModelProperty("数量")
    private Integer amount;
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;
}
