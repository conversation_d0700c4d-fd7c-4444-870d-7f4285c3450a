package com.logistics.management.webapi.client.freightconfig;

import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.freightconfig.hystrix.CarrierFreightConfigMileageClientHystrix;
import com.logistics.management.webapi.client.freightconfig.request.mileage.CarrierFreightConfigMileageAddRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.mileage.CarrierFreightConfigMileageEditRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.mileage.CarrierFreightConfigMileageRequestModel;
import com.logistics.management.webapi.client.freightconfig.response.mileage.CarrierFreightConfigMileageResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/freight/config/mileage",
        fallback = CarrierFreightConfigMileageClientHystrix.class)
public interface CarrierFreightConfigMileageClient {

    @PostMapping(value = "/detail")
    @ApiOperation(value = "里程数计价配置查看", tags = "1.3.5")
    Result<CarrierFreightConfigMileageResponseModel> detail(@RequestBody CarrierFreightConfigMileageRequestModel requestModel);

    @PostMapping(value = "/add")
    @ApiOperation(value = "里程数计价配置新增", tags = "1.3.5")
    Result<Boolean> add(@RequestBody CarrierFreightConfigMileageAddRequestModel requestModel);

    @PostMapping(value = "/edit")
    @ApiOperation(value = "里程数计价配置编辑", tags = "1.3.5")
    Result<Boolean> edit(@RequestBody CarrierFreightConfigMileageEditRequestModel requestModel);
}
