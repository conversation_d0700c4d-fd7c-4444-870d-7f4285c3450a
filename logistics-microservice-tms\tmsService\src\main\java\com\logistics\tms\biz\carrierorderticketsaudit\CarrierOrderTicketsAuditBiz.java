package com.logistics.tms.biz.carrierorderticketsaudit;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.carrierorder.CarrierOrderForLeYiBiz;
import com.logistics.tms.biz.carrierorderticketsaudit.event.CarrierOrderTicketsAuditEvent;
import com.logistics.tms.biz.carrierorderticketsaudit.model.CreateOrResetReceiptTicketAuditBoModel;
import com.logistics.tms.biz.carrierorderticketsaudit.model.SearchCarrierOrderIdsForLeYiReceiptAuditBoModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.client.BasicDataClient;
import com.logistics.tms.client.feign.basicdata.ocr.response.OCRCustomizationIdentifyResponseDto;
import com.logistics.tms.controller.carrierorder.response.SearchCarrierOrderListForLeYiResponseModel;
import com.logistics.tms.controller.carrierorderticketsaudit.enums.CarrierOrderTicketsAuditStatusEnum;
import com.logistics.tms.controller.carrierorderticketsaudit.response.GetReceiptAuditDetailResponseModel;
import com.logistics.tms.controller.carrierorderticketsaudit.response.SearchReceiptAuditListResponseModel;
import com.logistics.tms.controller.carrierorderticketsaudit.request.GetReceiptAuditDetailRequestModel;
import com.logistics.tms.controller.carrierorderticketsaudit.request.ReceiptAgainAuditRequestModel;
import com.logistics.tms.controller.carrierorderticketsaudit.request.ReceiptAuditRequestModel;
import com.logistics.tms.controller.carrierorderticketsaudit.request.SearchReceiptAuditListRequestModel;
import com.logistics.tms.entity.TCarrierOrder;
import com.logistics.tms.entity.TCarrierOrderTickets;
import com.logistics.tms.entity.TCarrierOrderTicketsAudit;
import com.logistics.tms.mapper.TCarrierOrderMapper;
import com.logistics.tms.mapper.TCarrierOrderTicketsAuditMapper;
import com.logistics.tms.mapper.TCarrierOrderTicketsMapper;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.logistics.tms.rabbitmq.publisher.model.CarrierOrderBill;
import com.logistics.tms.rabbitmq.publisher.model.UpdateCarrierOrderBillModel;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.utils.RedisUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MultiValueMap;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CarrierOrderTicketsAuditBiz {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private TCarrierOrderTicketsAuditMapper carrierOrderTicketsAuditMapper;
    @Resource
    private TCarrierOrderTicketsMapper carrierOrderTicketsMapper;
    @Resource
    private TCarrierOrderMapper carrierOrderMapper;

    @Resource
    private CarrierOrderForLeYiBiz carrierOrderForLeYiBiz;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private RabbitMqPublishBiz rabbitMqPublishBiz;
    @Resource
    private BasicDataClient basicDataClient;


    /**
     * 分页查询回单审核列表
     *
     * @param requestModel 请求 Model
     * @return 回单审核分页列表
     */
    public PageInfo<SearchReceiptAuditListResponseModel> searchList(SearchReceiptAuditListRequestModel requestModel) {

        // 根据运单筛选条件查询
        SearchCarrierOrderIdsForLeYiReceiptAuditBoModel requestBoModel = MapperUtils.mapper(requestModel, SearchCarrierOrderIdsForLeYiReceiptAuditBoModel.class);
        if (!new SearchCarrierOrderIdsForLeYiReceiptAuditBoModel().equals(requestBoModel)) {
            List<Long> carrierOrderIds = carrierOrderForLeYiBiz.getReceiptAuditCarrierOrderIds(requestBoModel);
            if (ListUtils.isEmpty(carrierOrderIds)) {
                return new PageInfo<>(Collections.emptyList());
            }
            requestModel.setCarrierOrderIds(carrierOrderIds);
        }

        // 查询回单审核列表
        requestModel.enablePaging();
        List<SearchReceiptAuditListResponseModel> carrierOrderTicketsAuditList = carrierOrderTicketsAuditMapper.searchList(requestModel);
        if (ListUtils.isEmpty(carrierOrderTicketsAuditList)) {
            return new PageInfo<>(Collections.emptyList());
        }
        PageInfo<SearchReceiptAuditListResponseModel> pageInfo = new PageInfo<>(carrierOrderTicketsAuditList);

        List<Long> carrierOrderIds = carrierOrderTicketsAuditList
                .stream()
                .map(SearchReceiptAuditListResponseModel::getCarrierOrderId)
                .collect(Collectors.toList());

        // 查询签收单信息
        Map<Long, Date> ticketUploadTimeMap = this.getReceiptTicketsByCarrierOrderIds(carrierOrderIds)
                .stream()
                .collect(Collectors.toMap(TCarrierOrderTickets::getCarrierOrderId, TCarrierOrderTickets::getUploadTime,
                        (v1, v2) -> v2));

        // 根据Id查询运单信息
        List<SearchCarrierOrderListForLeYiResponseModel> carrierOrderInfoList =
                carrierOrderForLeYiBiz.getReceiptAuditCarrierOrderInfoList(carrierOrderIds);
        Map<Long, SearchCarrierOrderListForLeYiResponseModel> carrierOrderInfoMap = carrierOrderInfoList.stream()
                .collect(Collectors.toMap(SearchCarrierOrderListForLeYiResponseModel::getCarrierOrderId, Function.identity()));

        // 填充数据
        carrierOrderTicketsAuditList.forEach(f -> {
            Optional.ofNullable(carrierOrderInfoMap.get(f.getCarrierOrderId()))
                    .ifPresent(c -> {
                        f.setCarrierOrderCode(c.getCarrierOrderCode())
                                .setEntrustType(c.getEntrustType())
                                .setDriverName(c.getDriverName())
                                .setDriverMobile(c.getDriverMobile())
                                .setVehicleNo(c.getVehicleNo())
                                .setLoadAddress(c.getLoadProvinceName()
                                        + c.getLoadCityName()
                                        + c.getLoadAreaName())
                                .setLoadWarehouse(c.getLoadWarehouse())
                                .setUnloadAddress(c.getUnloadProvinceName()
                                        + c.getUnloadCityName()
                                        + c.getUnloadAreaName())
                                .setUnloadWarehouse(c.getUnloadWarehouse())
                                .setUnloadTime(c.getUnloadTime())
                                .setTicketUploadTime(ticketUploadTimeMap.get(f.getCarrierOrderId()))
                        ;
                    });
        });
        return pageInfo;
    }

    /**
     * 回单审核详情
     *
     * @param requestModel 请求 Model
     * @return 审核详情
     */
    public GetReceiptAuditDetailResponseModel getDetail(GetReceiptAuditDetailRequestModel requestModel) {

        // 查询审核信息
        TCarrierOrderTicketsAudit receiptTicketsAuditDetail = this.getReceiptAuditById(requestModel.getReceiptAuditId());

        // 根据运单Id查询签收单图片
        List<Long> carrierOrderIds = Collections.singletonList(receiptTicketsAuditDetail.getCarrierOrderId());
        List<String> ticketPaths = this.getReceiptTicketsByCarrierOrderIds(carrierOrderIds)
                .stream()
                .map(TCarrierOrderTickets::getImagePath)
                .collect(Collectors.toList());
        return new GetReceiptAuditDetailResponseModel()
                .setAuditorName(receiptTicketsAuditDetail.getTicketsAuditorName())
                .setAuditTime(receiptTicketsAuditDetail.getTicketsAuditTime())
                .setAuditStatus(receiptTicketsAuditDetail.getTicketsAuditStatus())
                .setRemark(receiptTicketsAuditDetail.getRemark())
                .setTicketImages(ticketPaths);
    }

    /**
     * 根据运单 Id 查询审核信息
     *
     * @param carrierOrderId 运单Id
     * @return 审核信息
     */
    public TCarrierOrderTicketsAudit getReceiptAuditByCarrierOrderId(Long carrierOrderId) {
        return carrierOrderTicketsAuditMapper.selectOneByCarrierOrderId(carrierOrderId);
    }

    /**
     * 是否符合回单审核的类型
     *
     * @param source      委托来源
     * @return true 符合 | false 不符合 （入参为空时返回 false）
     */
    public boolean isReceiptEntrustType(Integer source) {
        return DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(source);
    }

    /**
     * 修改回单信息，根据运单Id校验
     *
     * @param source 委托来源
     * @param carrierOrderId 运单Id
     */
    public void updateReceiptTicketCheck(Integer source, Long carrierOrderId) {
        TCarrierOrderTicketsAudit receiptAuditDetail = this.getReceiptAuditByCarrierOrderId(carrierOrderId);
        if (this.isReceiptEntrustType(source) && Objects.nonNull(receiptAuditDetail)) {
            this.updateReceiptTicketCheck(receiptAuditDetail.getTicketsAuditStatus());
        }
    }

    /**
     * 回单审核
     *
     * @param requestModel 请求Model
     * @return boolean
     */
    @Transactional
    public boolean audit(ReceiptAuditRequestModel requestModel) {

        // 查询审核信息
        TCarrierOrderTicketsAudit receiptAudit = this.getReceiptAuditById(requestModel.getReceiptAuditId());

        TCarrierOrderTicketsAudit auditEntity = this.builderAuditEntity(receiptAudit,
                requestModel.getAuditStatus(),
                requestModel.getRemark(),
                (status) -> CarrierOrderTicketsAuditStatusEnum.WAIT_AUDIT.getKey().equals(status)
                        || CarrierOrderTicketsAuditStatusEnum.REJECT_AUDIT.getKey().equals(status));

        // 更新审核状态
        int size = carrierOrderTicketsAuditMapper.updateAudit(auditEntity, receiptAudit.getTicketsAuditStatus());
        if (size < CommonConstant.INTEGER_ONE) {
            throw new BizException(CarrierDataExceptionEnum.RECEIPT_AUDIT_STATUS_CHANGE);
        }
        return true;
    }

    /**
     * 回单重新审核
     *
     * @param requestModel 请求Model
     * @return boolean
     */
    @Transactional
    public boolean againAudit(ReceiptAgainAuditRequestModel requestModel) {

        // 查询审核信息
        TCarrierOrderTicketsAudit receiptAudit = this.getReceiptAuditById(requestModel.getReceiptAuditId());

        // 运单校验
        TCarrierOrder order = carrierOrderMapper.selectByPrimaryKeyDecrypt(receiptAudit.getCarrierOrderId());
        if (order == null || IfValidEnum.INVALID.getKey().equals(order.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //运单已关联对账不可修改
        if (order.getCarrierSettleStatementStatus() > CarrierSettleStatementStatusEnum.NOT_RELATED.getKey()) {
            throw new BizException(CarrierDataExceptionEnum.RELEVANCY_RECONCILIATION_UNABLE_MODIFIABLE);
        }

        TCarrierOrderTicketsAudit auditEntity = this.builderAuditEntity(receiptAudit,
                requestModel.getAuditStatus(),
                requestModel.getRemark(),
                (status) -> CarrierOrderTicketsAuditStatusEnum.THROUGH_AUDIT.getKey().equals(status));
        // 更新审核状态
        int size = carrierOrderTicketsAuditMapper.updateAudit(auditEntity, CarrierOrderTicketsAuditStatusEnum.THROUGH_AUDIT.getKey());
        if (size < CommonConstant.INTEGER_ONE) {
            throw new BizException(CarrierDataExceptionEnum.RECEIPT_AUDIT_STATUS_CHANGE);
        }

        CarrierOrderTicketsTypeEnum signTicketsEnum = CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS;

        // 删除回单处理逻辑
        // 删除已存在回单，同步云盘
        List<CarrierOrderBill> carrierOrderDelBillList = Lists.newArrayList();
        List<TCarrierOrderTickets> delTicketsList = Lists.newArrayList();
        carrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(order.getId(), signTicketsEnum.getKey())
                .forEach(ticket -> {
                    TCarrierOrderTickets delTicket = new TCarrierOrderTickets();
                    delTicket.setId(ticket.getImageId());
                    delTicket.setValid(IfValidEnum.INVALID.getKey());
                    delTicket.setUploadUserName(BaseContextHandler.getUserName());
                    commonBiz.setBaseEntityModify(delTicket, BaseContextHandler.getUserName());
                    delTicketsList.add(delTicket);

                    CarrierOrderBill carrierOrderBill = new CarrierOrderBill();
                    carrierOrderBill.setBillPath(ticket.getImagePath());
                    carrierOrderBill.setType(CommonConstant.INTEGER_FOUR);
                    carrierOrderDelBillList.add(carrierOrderBill);
                });
        if (ListUtils.isNotEmpty(delTicketsList)) {
            carrierOrderTicketsMapper.batchUpdate(delTicketsList);
        }

        // 新增回单处理逻辑
        // 上传回单
        List<TCarrierOrderTickets> tickets = Lists.newArrayList();
        // 新增签收单同步云盘
        List<CarrierOrderBill> carrierOrderAddBillList = Lists.newArrayList();
        requestModel.getTicketImages()
                .forEach(imageUrl -> {
                    String imagePath = commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey(),
                            order.getCarrierOrderCode(),
                            imageUrl,
                            null);
                    TCarrierOrderTickets tmp = new TCarrierOrderTickets();
                    tmp.setCarrierOrderId(order.getId());
                    tmp.setImageName(signTicketsEnum.getValue());
                    tmp.setImagePath(imagePath);
                    tmp.setImageType(signTicketsEnum.getKey());
                    tmp.setUploadTime(receiptAudit.getTicketsAuditTime());
                    tmp.setUploadUserName(BaseContextHandler.getUserName());
                    commonBiz.setBaseEntityAdd(tmp, BaseContextHandler.getUserName());
                    tickets.add(tmp);

                    CarrierOrderBill carrierOrderBill = new CarrierOrderBill();
                    carrierOrderBill.setBillPath(tmp.getImagePath());
                    carrierOrderBill.setType(CommonConstant.INTEGER_FOUR);
                    carrierOrderAddBillList.add(carrierOrderBill);
                });
        if (ListUtils.isNotEmpty(tickets)) {
            carrierOrderTicketsMapper.batchInsertTickets(tickets);
        }

        // 同步云盘
        if (ListUtils.isNotEmpty(carrierOrderAddBillList) || ListUtils.isNotEmpty(carrierOrderDelBillList)) {
            UpdateCarrierOrderBillModel updateCarrierOrderBillModel = new UpdateCarrierOrderBillModel();
            updateCarrierOrderBillModel.setCarrierOrderCode(order.getCarrierOrderCode());
            updateCarrierOrderBillModel.setOperation(BaseContextHandler.getUserName());
            //新增
            if (ListUtils.isNotEmpty(carrierOrderAddBillList)) {
                updateCarrierOrderBillModel.setType(CommonConstant.INTEGER_ONE);
                updateCarrierOrderBillModel.setCarrierOrderBillList(carrierOrderAddBillList);
                rabbitMqPublishBiz.syncCarrierOrderOutStockTickets(Collections.singletonList(updateCarrierOrderBillModel));
            }
            //删除
            if (ListUtils.isNotEmpty(carrierOrderDelBillList)) {
                updateCarrierOrderBillModel.setType(CommonConstant.INTEGER_TWO);
                updateCarrierOrderBillModel.setCarrierOrderBillList(carrierOrderDelBillList);
                rabbitMqPublishBiz.syncCarrierOrderOutStockTickets(Collections.singletonList(updateCarrierOrderBillModel));
            }
        }
        return true;
    }

    /**
     * 创建|重置回单审核
     *
     * @param boModel 业务 Model
     */
    public void createOrResetReceiptTicketAudit(CreateOrResetReceiptTicketAuditBoModel boModel) {

        // 委托类型校验
        if (!this.createOrResetReceiptTicketAuditCheck(boModel)) {
            return;
        }
        // 校验回单是否正在自动审核
        String lockKey = String.format(CommonConstant.RECEIPT_TICKETS_AUDIT_KEY, boModel.getCarrierOrderCode());
        if (redisUtils.hasKey(lockKey)) {
            throw new BizException(CarrierDataExceptionEnum.RECEIPT_AUTO_AUDIT);
        }

        CarrierOrderTicketsAuditEvent ticketsAuditEvent = CarrierOrderTicketsAuditEvent
                .builder()
                .boModels(Collections.singletonList(boModel))
                .build();
        applicationContext.publishEvent(ticketsAuditEvent);
    }

    /**
     * 批量创建回单审核
     *
     * @param boModels 业务 Model
     * @param checkTicketExists 是否检查回单是否存在
     */
    public void batchCreateReceiptTicketAudit(List<CreateOrResetReceiptTicketAuditBoModel> boModels, boolean checkTicketExists) {

        // 数据筛选过滤
        boModels = boModels.stream()
                .filter(this::createOrResetReceiptTicketAuditCheck)
                .collect(Collectors.toList());
        if (ListUtils.isEmpty(boModels)) {
            return;
        }

        List<Long> carrierOrderIds = boModels
                .stream()
                .map(CreateOrResetReceiptTicketAuditBoModel::getCarrierOrderId)
                .collect(Collectors.toList());

        // 过滤没有签收单的数据
        List<Long> existsTicketCarrierOrderIds = Lists.newArrayList();
        Map<Long, List<String>> existsTicketCarrierOrderMap = Maps.newHashMap();
        if (checkTicketExists) {
            List<TCarrierOrderTickets> ticketsList = this.getReceiptTicketsByCarrierOrderIds(carrierOrderIds);
            existsTicketCarrierOrderMap.putAll(ticketsList
                    .stream()
                    .collect(Collectors.groupingBy(TCarrierOrderTickets::getCarrierOrderId,
                            Collectors.mapping(TCarrierOrderTickets::getImagePath, Collectors.toList()))));
            existsTicketCarrierOrderIds.addAll(existsTicketCarrierOrderMap.keySet());
            if (ListUtils.isEmpty(existsTicketCarrierOrderIds)) {
                return;
            }
        }

        List<TCarrierOrderTicketsAudit> receiptAuditDetailList = carrierOrderTicketsAuditMapper.selectAllByCarrierOrderIds(carrierOrderIds);
        List<Long> existsCarrierOrderIds = receiptAuditDetailList
                .stream()
                .map(TCarrierOrderTicketsAudit::getCarrierOrderId)
                .collect(Collectors.toList());

        List<CreateOrResetReceiptTicketAuditBoModel> insertBoModels = boModels.stream()
                .filter(f -> !existsCarrierOrderIds.contains(f.getCarrierOrderId()))
                .filter(f -> !checkTicketExists || existsTicketCarrierOrderIds.contains(f.getCarrierOrderId()))
                .peek(s -> {
                    Optional.ofNullable(existsTicketCarrierOrderMap.get(s.getCarrierOrderId()))
                            .ifPresent(s::setTicketsPathList);
                })
                .collect(Collectors.toList());
        if (ListUtils.isNotEmpty(insertBoModels)) {
            CarrierOrderTicketsAuditEvent ticketsAuditEvent = CarrierOrderTicketsAuditEvent
                    .builder()
                    .boModels(insertBoModels)
                    .build();
            applicationContext.publishEvent(ticketsAuditEvent);
        }
    }

    /**
     * 创建 | 重置回单审核记录
     *
     * @param boModel 业务 Model
     */
    @Async("receiptAutoAuditTask")
    public void asyncCreateReceiptTicketAudit(CreateOrResetReceiptTicketAuditBoModel boModel) {

        String lockKey = String.format(CommonConstant.RECEIPT_TICKETS_AUDIT_KEY, boModel.getCarrierOrderCode());
        RLock lock = null;
        boolean hasLock = false;
        try {
            lock = redissonClient.getLock(lockKey);
            hasLock = lock.tryLock();
            if (hasLock) {
                // 自动审核
                TCarrierOrderTicketsAudit entity = this.receiptTicketAutoAudit(boModel);
                if (Objects.isNull(entity)) {
                    return;
                }
                // 查询审核信息
                TCarrierOrderTicketsAudit resetEntity = this.getReceiptAuditByCarrierOrderId(boModel.getCarrierOrderId());
                // 重置回单审核
                if (Objects.nonNull(resetEntity)) {
                    // 校验
                    this.updateReceiptTicketCheck(resetEntity.getTicketsAuditStatus());
                    // 重置回单审核状态
                    resetEntity.setTicketsAuditStatus(entity.getTicketsAuditStatus());
                    resetEntity.setIfAutomaticAudit(entity.getIfAutomaticAudit());
                    resetEntity.setTicketsAuditTime(entity.getTicketsAuditTime());
                    resetEntity.setTicketsAuditorName(entity.getTicketsAuditorName());
                    resetEntity.setRemark("");
                    commonBiz.setBaseEntityModify(resetEntity, BaseContextHandler.getUserName());
                    carrierOrderTicketsAuditMapper.updateByPrimaryKey(resetEntity);
                    return;
                }

                // 创建回单审核
                entity.setCarrierOrderId(boModel.getCarrierOrderId());
                commonBiz.setBaseEntityAdd(entity, BaseContextHandler.getUserName());
                carrierOrderTicketsAuditMapper.insertSelective(entity);
            }
        }
        catch (BizException biz) {
            log.warn("创建回单审核失败, 运单号:【{}】, message:【{}】", boModel.getCarrierOrderCode(), biz.getMessage());
        }
        catch (Exception e) {
            log.error("创建回单审核异常, 运单号:【{}】, exception:【{}】", boModel.getCarrierOrderCode(), e.getMessage());
        } finally {
            if (hasLock) {
                lock.unlock();
            }
        }
    }

    /**
     * 回单自动审核
     *
     * @param boModel 业务 Model
     * @return 持久层 Entity
     */
    private TCarrierOrderTicketsAudit receiptTicketAutoAudit(CreateOrResetReceiptTicketAuditBoModel boModel) {

        log.info("回单自动审核, 运单号:【{}】-》回单自动审核 开始", boModel.getCarrierOrderCode());
        boolean isAutoAuditPass = false;
        if (ListUtils.isNotEmpty(boModel.getTicketsPathList())) {
            String filePath = boModel.getTicketsPathList().get(CommonConstant.INTEGER_ZERO);
            // 回单识别
            isAutoAuditPass = this.identifyCarrierOrderCodeWhetherMatch(filePath, boModel.getEntrustType(), boModel.getCarrierOrderCode());
        }
        log.info("回单自动审核, 运单号:【{}】-》回单自动审核 结束, 自动审核结果:【运单号{}】", boModel.getCarrierOrderCode(), isAutoAuditPass ? "匹配" : "未匹配");

        // 回收类型未识别 不产生回单审核记录
        if (!isAutoAuditPass && (EntrustTypeEnum.RECYCLE_IN.getKey().equals(boModel.getEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(boModel.getEntrustType()))) {
            return null;
        }

        // 已识别
        TCarrierOrderTicketsAudit entity = new TCarrierOrderTicketsAudit();
        int auditStatus = CarrierOrderTicketsAuditStatusEnum.WAIT_AUDIT.getKey();
        int ifAutomaticAudit = CommonConstant.INTEGER_ZERO;
        Date auditTime = null;
        String auditorName = "";
        if (isAutoAuditPass) {
            auditStatus = CarrierOrderTicketsAuditStatusEnum.THROUGH_AUDIT.getKey();
            ifAutomaticAudit = CommonConstant.INTEGER_ONE;
            auditTime = new Date();
            auditorName = CommonConstant.CARRIER_ORDER_TICKETS_AUTO_AUDIT;
        }
        entity.setTicketsAuditStatus(auditStatus);
        entity.setIfAutomaticAudit(ifAutomaticAudit);
        entity.setTicketsAuditTime(auditTime);
        entity.setTicketsAuditorName(auditorName);
        return entity;
    }

    /**
     * 创建回单审核校验
     *
     * @param boModel 业务参数Model
     * @return true 创建 | false 不创建
     */
    private boolean createOrResetReceiptTicketAuditCheck(CreateOrResetReceiptTicketAuditBoModel boModel) {

        // 委托类型校验
        if (!this.isReceiptEntrustType(boModel.getSource())) {
            return false;
        }
        // 票据类型校验
        if (!CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey().equals(boModel.getTicketType())) {
            return false;
        }
        // 运单状态校验
        List<Integer> checkStatusList = Lists.newArrayList(CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey(),
                CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey());
        return checkStatusList.contains(boModel.getCarrierOrderStatus());
    }

    /**
     * 回单编辑校验
     *
     * @param ticketAuditStatus 回单审核状态
     */
    private void updateReceiptTicketCheck(Integer ticketAuditStatus) {
        // 校验
        List<Integer> checkStatus = Lists.newArrayList(CarrierOrderTicketsAuditStatusEnum.WAIT_AUDIT.getKey(),
                CarrierOrderTicketsAuditStatusEnum.THROUGH_AUDIT.getKey());
        if (checkStatus.contains(ticketAuditStatus)) {
            int code = CarrierDataExceptionEnum.RECEIPT_NOT_NOT_ALLOWED_EDIT.getCode();
            String message = String.format(CarrierDataExceptionEnum.RECEIPT_NOT_NOT_ALLOWED_EDIT.getMsg(),
                    CarrierOrderTicketsAuditStatusEnum.getEnumByKey(ticketAuditStatus).getValue());
            throw new BizException(code, message);
        }
    }

    /**
     * 构建审核Entity
     *
     * @param receiptAudit 回单审核 Entity
     * @param auditStatus 审核状态
     * @param remark 备注
     * @param dbAuditStatusPredicate 状态校验函数
     * @return 审核 Entity
     */
    private TCarrierOrderTicketsAudit builderAuditEntity(TCarrierOrderTicketsAudit receiptAudit,
                                                         Integer auditStatus,
                                                         String remark,
                                                         Predicate<Integer> dbAuditStatusPredicate) {
        // 校验审核状态
        if (dbAuditStatusPredicate.negate().test(receiptAudit.getTicketsAuditStatus())) {
            throw new BizException(CarrierDataExceptionEnum.RECEIPT_AUDIT_STATUS_CHANGE);
        }
        // 构建审核Entity
        TCarrierOrderTicketsAudit auditEntity = new TCarrierOrderTicketsAudit();
        auditEntity.setId(receiptAudit.getId());
        auditEntity.setTicketsAuditStatus(auditStatus);
        auditEntity.setTicketsAuditorName(BaseContextHandler.getUserName());
        auditEntity.setTicketsAuditTime(new Date());
        auditEntity.setRemark(remark);
        return auditEntity;
    }

    /**
     * 根据回单审核Id查询审核信息
     *
     * @param receiptAuditId 回单审核ID
     * @return 回单审核信息
     */
    private TCarrierOrderTicketsAudit getReceiptAuditById(Long receiptAuditId) {
        return Optional.ofNullable(carrierOrderTicketsAuditMapper.selectOneById(receiptAuditId))
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.RECEIPT_AUDIT_NOT_EXISTS));
    }

    /**
     * 查询签收单
     *
     * @param carrierOrderIds 运单ID集合
     * @return 签收单集合
     */
    private List<TCarrierOrderTickets> getReceiptTicketsByCarrierOrderIds(List<Long> carrierOrderIds) {
        return carrierOrderTicketsMapper.selectTicketsByCarrierOrderIdsAndType(StringUtils.listToString(carrierOrderIds, ','),
                CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey());
    }

    /**
     * 识别运单号是否匹配
     *
     * @param filePath 图片相对路径
     * @param entrustType 委托类型
     * @param carrierOrderCode 运单号
     * @return true 匹配 | false 不匹配
     */
    private boolean identifyCarrierOrderCodeWhetherMatch(String filePath, Integer entrustType, String carrierOrderCode) {
        try {
            log.info("回单自动审核, 运单号:【{}】-》FilePath:【{}】", carrierOrderCode, filePath);
            // 获取二维码参数
            MultiValueMap<String, String> multiValueMap = commonBiz.parseQrCodeParametersByFilePath(filePath);
            if (Objects.equals(multiValueMap.getFirst(QrCodeParamsEnum.CARRIER_ORDER_CODE.getKey()), carrierOrderCode)) {
                return true;
            }
        } catch (Exception e) {
            log.warn("回单自动审核, 运单号:【{}】-》识别二维码异常, Exception:【{}】", carrierOrderCode, e.getMessage());
        }

        try {
            // 获取OCR模板Id
            String ocrTemplateId = (EntrustTypeEnum.RECYCLE_IN.getKey().equals(entrustType) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(entrustType)) ?
                    OCRTemplateEnum.LEYI_RECYCLING_TYPE_TEMPLATE_ID.getOcrTemplateId() :
                    OCRTemplateEnum.LEYI_NON_RECYCLING_TYPE_TEMPLATE_ID.getOcrTemplateId();

            OCRCustomizationIdentifyResponseDto responseDto = basicDataClient.ocrCustomizationTemplateIdentify(filePath, ocrTemplateId);
            log.info("回单自动审核, 运单号:【{}】-》OCR识别内容:【{}】", carrierOrderCode, JSONObject.toJSONString(responseDto));
            return Optional.ofNullable(responseDto)
                    .filter(f -> ListUtils.isNotEmpty(f.getIOcrData()))
                    .map(o -> {
                        return o.getIOcrData()
                                .stream()
                                .filter(f -> OCRIdentifyWordNameEnum.CARRIER_ORDER_CODE.getWordName().equals(f.getWordName()))
                                .anyMatch(a -> carrierOrderCode.equals(a.getWord()));
                    })
                    .orElse(false);
        } catch (Exception e) {
            log.warn("回单自动审核, 运单号:【{}】-》OCR识别异常, Exception:【{}】", carrierOrderCode, e.getMessage());
            return false;
        }
    }
}
