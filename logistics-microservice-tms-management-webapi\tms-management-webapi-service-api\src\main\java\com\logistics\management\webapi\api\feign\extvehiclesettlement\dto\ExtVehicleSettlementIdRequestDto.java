package com.logistics.management.webapi.api.feign.extvehiclesettlement.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2019/11/20 14:00
 */
@Data
public class ExtVehicleSettlementIdRequestDto {
    @ApiModelProperty("外部车辆结算ID")
    @NotBlank(message = "id不能为空")
    private String extVehicleSettlementId;
    @ApiModelProperty("操作类型：1 详情，2 付款，3 回退")
    @NotBlank(message = "操作类型不能为空")
    private String operateType;
}
