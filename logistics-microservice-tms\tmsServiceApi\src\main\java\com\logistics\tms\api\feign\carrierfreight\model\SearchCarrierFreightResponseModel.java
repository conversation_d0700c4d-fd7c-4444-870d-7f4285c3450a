package com.logistics.tms.api.feign.carrierfreight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询车主运价列表
 *
 * <AUTHOR>
 * @date 2022/9/2 16:15
 */
@Data
public class SearchCarrierFreightResponseModel {

    @ApiModelProperty("车主运价id")
    private Long carrierFreightId;

    @ApiModelProperty("状态")
    private Integer enabled;

    @ApiModelProperty(value = "车主名称")
    private String companyCarrierName;

    @ApiModelProperty(value = "车主公司类型：1 公司，2 个人")
    private Integer companyCarrierType;

    @ApiModelProperty("车主账号名称")
    private String carrierContactName;

    @ApiModelProperty("车主账号手机号（原长度50）")
    private String carrierContactPhone;
}
