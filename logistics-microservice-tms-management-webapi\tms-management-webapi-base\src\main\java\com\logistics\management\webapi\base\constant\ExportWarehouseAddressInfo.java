package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: jzh
 * @Date: 2019/12/30 19:35
 */
public class ExportWarehouseAddressInfo {
    private ExportWarehouseAddressInfo() {
    }
    private static final Map<String,String> WAREHOUSE_ADDRESS;
    static{
        WAREHOUSE_ADDRESS=new LinkedHashMap<>();
        WAREHOUSE_ADDRESS.put("状态","enabled");
        WAREHOUSE_ADDRESS.put("仓库","warehouse");
        WAREHOUSE_ADDRESS.put("货主","companyEntrustName");
        WAREHOUSE_ADDRESS.put("省市区","address");
        WAREHOUSE_ADDRESS.put("操作人","operateUserName");
        WAREHOUSE_ADDRESS.put("操作时间","operateTime");
    }


    public static Map<String, String> getWarehouseAddressInfo() {
        return WAREHOUSE_ADDRESS;
    }

}
