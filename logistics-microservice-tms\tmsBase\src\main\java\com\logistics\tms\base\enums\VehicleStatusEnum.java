package com.logistics.tms.base.enums;

import com.yelo.tray.core.base.enums.BaseExceptionEnum;

/**
 * <AUTHOR>
 * @createDate 2018-08-13 20:36
 */
public enum VehicleStatusEnum implements BaseExceptionEnum {

    STOP(0,"停驶"),
    DRIVING(1,"行驶中"),
    ;

    @Override
    public int getCode() {
        return key;
    }

    @Override
    public String getMsg() {
        return value;
    }

    private Integer key;
    private String value;

    VehicleStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
