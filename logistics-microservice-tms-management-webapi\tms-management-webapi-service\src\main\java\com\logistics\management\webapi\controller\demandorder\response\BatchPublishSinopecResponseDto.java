package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 中石化批量发布详情响应实体类
 *
 * @author: wei.wang
 * @date: 2021/12/3
 */
@Data
public class BatchPublishSinopecResponseDto {
	@ApiModelProperty("货主公司名称")
	private String companyEntrustName = "";
	@ApiModelProperty("需求单数量")
	private String demandOrderCount = "";
	@ApiModelProperty("货物总计数量")
	private String goodsAmountCount = "";

	@ApiModelProperty("需求单详情列表")
	private List<BatchPublishSinopecDetailResponseDto> sinopecDemands;
}
