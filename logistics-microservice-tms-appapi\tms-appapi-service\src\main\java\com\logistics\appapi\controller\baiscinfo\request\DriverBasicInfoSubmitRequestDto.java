package com.logistics.appapi.controller.baiscinfo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/4
 */
@Data
public class DriverBasicInfoSubmitRequestDto {

	@ApiModelProperty(value = "姓名,2-50个汉字", required = true)
	@NotBlank(message = "请输入姓名,2-50个汉字")
	@Pattern(regexp = "^[\\u4E00-\\u9FA5]{2,50}$", message = "请输入姓名,2-50个汉字")
	private String name;

	@ApiModelProperty(value = "身份证号,18-21位", required = true)
	@NotBlank(message = "请输入身份证号,18-21位")
	@Pattern(regexp = "^[0-9X]{18,21}$", message = "请输入身份证号,18-21位")
	private String identityNumber;

	@ApiModelProperty(value = "身份证头像面")
	private String identityFaceFile;

	@ApiModelProperty(value = "身份证国徽面")
	private String identityNationalFile;

	@ApiModelProperty(value = "认证类型,1:手机号认证 2:人脸识别", required = true)
	@NotBlank(message = "请选择认证类型")
	@Range(min = 1, max = 2, message = "请选择认证类型")
	private String authModel;

	@ApiModelProperty("人脸识别传入唯一字符串(人脸识别认证传)")
	private String orderNo;

	@ApiModelProperty(value = "短信验证码")
	private String verificationCode;
}
