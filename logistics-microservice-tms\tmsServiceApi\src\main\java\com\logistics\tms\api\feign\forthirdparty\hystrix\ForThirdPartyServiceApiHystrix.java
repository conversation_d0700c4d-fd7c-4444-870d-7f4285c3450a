package com.logistics.tms.api.feign.forthirdparty.hystrix;

import com.logistics.tms.api.feign.forthirdparty.ForThirdPartyServiceApi;
import com.logistics.tms.api.feign.forthirdparty.model.request.WorkOrderProcessSyncRequestModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

@Component
public class ForThirdPartyServiceApiHystrix implements ForThirdPartyServiceApi {

    @Override
    public Result<Boolean> syncWorkOrderProcess(WorkOrderProcessSyncRequestModel requestModel) {
        return Result.timeout();
    }
}
