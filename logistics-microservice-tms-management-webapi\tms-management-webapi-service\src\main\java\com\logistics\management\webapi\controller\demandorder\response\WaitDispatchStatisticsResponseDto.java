package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2021/10/18 16:30
 */
@Data
public class WaitDispatchStatisticsResponseDto {
    @ApiModelProperty("待发布总单数")
    private String waitPublishTotal = "0";
    @ApiModelProperty("待发布")
    private List<WaitDispatchStatisticsDto> waitPublishList;
    @ApiModelProperty("待调度总单数")
    private String waitDispatchTotal = "0";
    @ApiModelProperty("待调度")
    private List<WaitDispatchStatisticsDto> waitDispatchList;

}
