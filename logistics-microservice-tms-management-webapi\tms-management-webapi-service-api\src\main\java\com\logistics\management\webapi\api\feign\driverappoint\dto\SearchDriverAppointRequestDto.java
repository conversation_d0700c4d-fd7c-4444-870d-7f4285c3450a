package com.logistics.management.webapi.api.feign.driverappoint.dto;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询驾驶员预约列表
 *
 * <AUTHOR>
 * @date 2022/8/17 10:14
 */
@ApiModel("查询驾驶员预约列表")
@Data
public class SearchDriverAppointRequestDto extends AbstractPageForm<SearchDriverAppointRequestDto> {

    @ApiModelProperty("司机")
    private String driver;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("下单时间from")
    private String publishTimeFrom;
    @ApiModelProperty("下单时间to")
    private String publishTimeTo;
    @ApiModelProperty("选择导出的ids")
    private String driverAppointIds;
    @ApiModelProperty(value = "客户")
    private String customerName;
}
