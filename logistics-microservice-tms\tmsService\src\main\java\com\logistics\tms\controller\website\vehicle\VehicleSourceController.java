package com.logistics.tms.controller.website.vehicle;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.website.vehicle.VehicleSourceBiz;
import com.logistics.tms.controller.website.vehicle.request.PublishVehicleSourceRequestModel;
import com.logistics.tms.controller.website.vehicle.request.VehicleSourceListRequestModel;
import com.logistics.tms.controller.website.vehicle.response.VehicleSourceListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/15 10:52
 */
@Api(value = "云途官网-车源")
@RestController
public class VehicleSourceController {

    @Resource
    private VehicleSourceBiz vehicleSourceBiz;

    /**
     * 发布车源
     * @param requestModel
     * @return
     */
    @ApiOperation("发布车源")
    @PostMapping("/service/vehicleSource/publishVehicleSource")
    public Result publishVehicleSource(@RequestBody PublishVehicleSourceRequestModel requestModel) {
        vehicleSourceBiz.publishVehicleSource(requestModel);
        return Result.success(true);
    }

    /**
     * 车源列表
     * @param requestModel
     * @return
     */
    @ApiOperation("车源列表")
    @PostMapping("/service/vehicleSource/vehicleSourceList")
    public Result<PageInfo<VehicleSourceListResponseModel>> vehicleSourceList(@RequestBody VehicleSourceListRequestModel requestModel) {
        List<VehicleSourceListResponseModel> list = vehicleSourceBiz.vehicleSourceList(requestModel);
        return Result.success(new PageInfo<>(list));
    }
}
