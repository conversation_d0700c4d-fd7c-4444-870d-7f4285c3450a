package com.logistics.management.webapi.api.feign.entrustsettlement.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetDetailResponseDto {
    @ApiModelProperty("结算ID")
    private String settlementId="";
    @ApiModelProperty("需求单号")
    private String demandOrderCode="";
    @ApiModelProperty("客户单号")
    private String customerOrderCode="";
    @ApiModelProperty("报价类型")
    private String contractPriceType="";
    @ApiModelProperty("结算费用")
    private String settlementCostTotal="";
}
