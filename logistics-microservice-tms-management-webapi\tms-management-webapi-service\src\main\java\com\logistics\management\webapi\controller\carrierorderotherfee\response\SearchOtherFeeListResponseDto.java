package com.logistics.management.webapi.controller.carrierorderotherfee.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchOtherFeeListResponseDto {

    @ApiModelProperty(value = "主表id")
    private String carrierOrderOtherFeeId = "";

    @ApiModelProperty(value = "（3.16.0修改）审核状态：1 待提交，2 待审核，3 已驳回，4 已撤销，5 已审核")
    private String auditStatus = "";
    @ApiModelProperty(value = "审核状态文本")
    private String auditStatusDesc = "";

    @ApiModelProperty(value = "运单id")
    private String carrierOrderId = "";
    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";

    @ApiModelProperty(value = "车主")
    private String companyCarrierName = "";

    @ApiModelProperty(value = "司机")
    private String driver = "";

    @ApiModelProperty(value = "车牌号")
    private String vehicleNo = "";

    @ApiModelProperty(value = "路线")
    private String route = "";

    @ApiModelProperty(value = "合计费用")
    private String totalAmount = "";

    @ApiModelProperty(value = "新增人")
    private String createdBy = "";

    @ApiModelProperty(value = "新增时间")
    private String createdTime = "";

    @ApiModelProperty(value = "最新操作人")
    private String lastModifiedBy = "";

    @ApiModelProperty(value = "最新操作时间")
    private String lastModifiedTime = "";

    @ApiModelProperty("1.3.7新增；项目标签")
    private String projectLabel="";


    @ApiModelProperty(value = "车主(导出使用)")
    private String exportCompanyCarrierName = "";
    @ApiModelProperty(value = "司机(导出使用)")
    private String driverForExport = "";

    @ApiModelProperty("车主对账单状态, -2:未关联对账")
    private String carrierSettleStatementStatus = "";

}
