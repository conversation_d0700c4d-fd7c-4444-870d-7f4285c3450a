package com.logistics.tms.api.feign.carriercontact.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class SearchCarrierContactResponseModel {

    @ApiModelProperty("车主类型：1 企业 2 个人")
    private Integer type;

    @ApiModelProperty("账号id")
    private Long carrierContactId;

    @ApiModelProperty("联系人名字")
    private String contactName;

    @ApiModelProperty("联系方式")
    private String contactPhone;

    @ApiModelProperty("公司名称")
    private String companyCarrierName;

    @ApiModelProperty("账号状态 禁用0 启用1")
    private Integer carrierContactStatus;

    private Long companyId;//主公司id


}
