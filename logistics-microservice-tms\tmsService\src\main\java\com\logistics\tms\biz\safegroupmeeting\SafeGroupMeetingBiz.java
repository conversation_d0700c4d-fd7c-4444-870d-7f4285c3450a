package com.logistics.tms.biz.safegroupmeeting;

import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tray.core.exception.BizException;
import com.logistics.tms.api.feign.safegroupmeeting.model.*;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.CopyFileTypeEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TSafetyGroupMeeting;
import com.logistics.tms.entity.TSafetyGroupMeetingAttachment;
import com.logistics.tms.entity.TSafetyGroupMeetingRelation;
import com.logistics.tms.mapper.TSafetyGroupMeetingAttachmentMapper;
import com.logistics.tms.mapper.TSafetyGroupMeetingMapper;
import com.logistics.tms.mapper.TSafetyGroupMeetingRelationMapper;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


/**
 * @author: MIkasa
 * @date: 2021/04/09
 */
@Service
public class SafeGroupMeetingBiz {


    @Autowired
    private TSafetyGroupMeetingMapper tSafetyGroupMeetingMapper;
    @Autowired
    private TSafetyGroupMeetingAttachmentMapper tSafetyGroupMeetingAttachmentMapper;
    @Autowired
    private TSafetyGroupMeetingRelationMapper tSafetyGroupMeetingRelationMapper;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 新建安全小组领导会议
     *
     * @param requestModel
     */
    @Transactional
    public void addSafeGroupMeeting(AddSafeGroupMeetingRequestModel requestModel) {
        TSafetyGroupMeeting safetyGroupMeetingByYearAndSeason = tSafetyGroupMeetingMapper.getSafetyGroupMeetingByYearAndSeason(requestModel.getMeetingYear(), requestModel.getMeetingSeason());
        if (safetyGroupMeetingByYearAndSeason != null) {
            throw new BizException(CarrierDataExceptionEnum.SAFETY_GROUP_MEETING_EXIST);
        }
        TSafetyGroupMeeting insertSafetyGroupMeeting = new TSafetyGroupMeeting();
        MapperUtils.mapper(requestModel, insertSafetyGroupMeeting);
        insertSafetyGroupMeeting.setContent(commonBiz.processReplaceTempPicture(requestModel.getContent(),CopyFileTypeEnum.SAFETY_GROUP_MEETING_ATTACHMENT_FILE,requestModel.getMeetingYear()));

        commonBiz.setBaseEntityAdd(insertSafetyGroupMeeting, BaseContextHandler.getUserName());
        tSafetyGroupMeetingMapper.insertSelective(insertSafetyGroupMeeting);

        List<TSafetyGroupMeetingRelation> insertRelationList = new ArrayList<>();
        List<TSafetyGroupMeetingAttachment> insertAttachmentList = new ArrayList<>();
        List<SafeGroupMeetingRelationModel> safeGroupMeetingRelationList = requestModel.getSafeGroupMeetingRelationList();
        List<SafetyGroupMeetingAttachmentModel> safetyGroupMeetingAttachmentList = requestModel.getSafetyGroupMeetingAttachmentList();

        if (ListUtils.isNotEmpty(safeGroupMeetingRelationList)) {
            safeGroupMeetingRelationList.forEach(e -> {
                TSafetyGroupMeetingRelation tSafetyGroupMeetingRelation = new TSafetyGroupMeetingRelation();
                tSafetyGroupMeetingRelation.setSafetyGroupMeetingId(insertSafetyGroupMeeting.getId());
                tSafetyGroupMeetingRelation.setPosition(e.getPosition());
                tSafetyGroupMeetingRelation.setParticipatePerson(e.getParticipatePerson());
                commonBiz.setBaseEntityAdd(tSafetyGroupMeetingRelation, BaseContextHandler.getUserName());
                insertRelationList.add(tSafetyGroupMeetingRelation);
            });
        }

        if (ListUtils.isNotEmpty(safetyGroupMeetingAttachmentList)) {
            safetyGroupMeetingAttachmentList.forEach(e -> {
                TSafetyGroupMeetingAttachment tSafetyGroupMeetingAttachment = new TSafetyGroupMeetingAttachment();
                tSafetyGroupMeetingAttachment.setSafetyGroupMeetingId(insertSafetyGroupMeeting.getId());
                tSafetyGroupMeetingAttachment.setType(e.getType());
                tSafetyGroupMeetingAttachment.setMeetingImagePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.SAFETY_GROUP_MEETING_ATTACHMENT_FILE.getKey(), "", e.getMeetingImagePath(), null));
                commonBiz.setBaseEntityAdd(tSafetyGroupMeetingAttachment, BaseContextHandler.getUserName());
                insertAttachmentList.add(tSafetyGroupMeetingAttachment);
            });
        }

        if (ListUtils.isNotEmpty(insertRelationList)) {
            tSafetyGroupMeetingRelationMapper.batchInsertSelective(insertRelationList);
        }
        if (ListUtils.isNotEmpty(insertAttachmentList)) {
            tSafetyGroupMeetingAttachmentMapper.batchInsertSelective(insertAttachmentList);
        }
    }


    /**
     * 安全小组领导会议记录列表
     * @param requestModel
     * @return
     */
    public List<SafeGroupMeetingResponseModel> safeGroupMeetingData(SafeGroupMeetingRequestModel requestModel){
        return tSafetyGroupMeetingMapper.getSafeGroupMeetingData(requestModel.getMeetingYear());
    }

    /**
     * 安全小组领导会议详情
     * @param requestModel
     * @return
     */
    public SafeGroupMeetingDetailResponseModel safeGroupMeetingDetail(SafeGroupMeetingDetailRequestModel requestModel){
        SafeGroupMeetingDetailResponseModel detail = tSafetyGroupMeetingMapper.getSafeGroupMeetingDetail(requestModel.getSafetyGroupMeetingId());
        if (detail != null){
            detail.setContent(commonBiz.addRealPath(detail.getContent()));
        }
        return detail;
    }
}
