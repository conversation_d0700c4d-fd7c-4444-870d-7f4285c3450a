<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TOilFilledRecordsMapper">
    <select id="listByOilFilledId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_oil_filled_records
        where valid=1
        and oil_filled_id = #{oilFilledId,jdbcType=BIGINT}
        order by created_time desc
    </select>
    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TOilFilledRecords">
        <foreach collection="list" item="item" index="index" separator=";">
            insert into t_oil_filled_records
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.oilFilledId != null">
                    oil_filled_id,
                </if>
                <if test="item.vehicleNo != null">
                    vehicle_no,
                </if>
                <if test="item.oilFilledFee != null">
                    oil_filled_fee,
                </if>
                <if test="item.oilFilledDate != null">
                    oil_filled_date,
                </if>
                <if test="item.oilFilledType != null">
                    oil_filled_type,
                </if>
                <if test="item.liter != null">
                    liter,
                </if>
                <if test="item.topUpIntegral != null">
                    top_up_integral,
                </if>
                <if test="item.rewardIntegral != null">
                    reward_integral,
                </if>
                <if test="item.subCardNumber != null">
                    sub_card_number,
                </if>
                <if test="item.subCardOwner != null">
                    sub_card_owner,
                </if>
                <if test="item.refundReasonType != null">
                    refund_reason_type,
                </if>
                <if test="item.refundReason != null">
                    refund_reason,
                </if>
                <if test="item.remark != null">
                    remark,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.oilFilledId != null">
                    #{item.oilFilledId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleNo != null">
                    #{item.vehicleNo,jdbcType=VARCHAR},
                </if>
                <if test="item.oilFilledFee != null">
                    #{item.oilFilledFee,jdbcType=DECIMAL},
                </if>
                <if test="item.oilFilledDate != null">
                    #{item.oilFilledDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.oilFilledType != null">
                    #{item.oilFilledType,jdbcType=INTEGER},
                </if>
                <if test="item.liter != null">
                    #{item.liter,jdbcType=INTEGER},
                </if>
                <if test="item.topUpIntegral != null">
                    #{item.topUpIntegral,jdbcType=DECIMAL},
                </if>
                <if test="item.rewardIntegral != null">
                    #{item.rewardIntegral,jdbcType=INTEGER},
                </if>
                <if test="item.subCardNumber != null">
                    #{item.subCardNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.subCardOwner != null">
                    #{item.subCardOwner,jdbcType=VARCHAR},
                </if>
                <if test="item.refundReasonType != null">
                    #{item.refundReasonType,jdbcType=INTEGER},
                </if>
                <if test="item.refundReason != null">
                    #{item.refundReason,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null">
                    #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>
</mapper>