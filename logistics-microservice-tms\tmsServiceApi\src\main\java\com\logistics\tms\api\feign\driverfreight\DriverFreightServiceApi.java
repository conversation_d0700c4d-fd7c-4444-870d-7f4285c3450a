package com.logistics.tms.api.feign.driverfreight;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.driverfreight.hystrix.DriverFreightServiceApiHystrix;
import com.logistics.tms.api.feign.driverfreight.model.DriverFreightListSearchRequestModel;
import com.logistics.tms.api.feign.driverfreight.model.DriverFreightListSearchResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Api(value = "API-司机运费管理")
@FeignClient(name = "logistics-tms-services", fallback = DriverFreightServiceApiHystrix.class)
public interface DriverFreightServiceApi {

    @PostMapping(value = "/service/driverFreight/searchList")
    @ApiOperation(value = "司机运费列表", notes = "")
    Result<PageInfo<DriverFreightListSearchResponseModel>> driverFreightList(@RequestBody DriverFreightListSearchRequestModel requestDto);

}
