package com.logistics.management.webapi.controller.settlestatement.packaging;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.client.settlestatement.packaging.CarrierSettleStatementClient;
import com.logistics.management.webapi.client.settlestatement.packaging.request.*;
import com.logistics.management.webapi.client.settlestatement.packaging.response.*;
import com.logistics.management.webapi.controller.settlestatement.packaging.mapping.*;
import com.logistics.management.webapi.controller.settlestatement.packaging.request.*;
import com.logistics.management.webapi.controller.settlestatement.packaging.response.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 包装业务车主对账管理
 */
@Slf4j
@RestController
@Api(value = "包装业务车主对账管理")
@RequestMapping(value = "/api/carrierStatementManage")
public class CarrierSettleStatementController {

    @Resource
    private CarrierSettleStatementClient carrierSettleStatementClient;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 查询待对账运单列表 3.27.0
     *
     * @param requestDto 筛选条件
     * @return 待对账运单列表
     */
    @ApiOperation(value = "待对账运单列表", tags = "1.3.7")
    @PostMapping(value = "/waitSettleStatementList")
    public Result<PageInfo<CarrierWaitSettleStatementListResponseDto>> waitSettleStatementList(@RequestBody CarrierWaitSettleStatementListRequestDto requestDto) {
        Result<PageInfo<CarrierWaitSettleStatementListResponseModel>> result = carrierSettleStatementClient.waitSettleStatementList(MapperUtils.mapper(requestDto, CarrierWaitSettleStatementListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        pageInfo.setList(MapperUtils.mapper(pageInfo.getList(), CarrierWaitSettleStatementListResponseDto.class, new WaitSettleStatementListMapping()));
        return Result.success(pageInfo);
    }

    /**
     * 待对账运单导出
     */
    @ApiOperation(value = "待对账运单列表-导出", tags = "1.3.7")
    @PostMapping(value = "/exportWaitSettleStatementList")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportWaitSettleStatementList(@RequestBody CarrierWaitSettleStatementListRequestDto requestDto, HttpServletResponse response) {
        CarrierWaitSettleStatementListRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierWaitSettleStatementListRequestModel.class);
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<CarrierWaitSettleStatementListResponseModel>> result = carrierSettleStatementClient.exportWaitSettleStatementList(requestModel);
        result.throwException();
        List<CarrierWaitSettleStatementListResponseDto> list = MapperUtils.mapper(result.getData().getList(), CarrierWaitSettleStatementListResponseDto.class, new WaitSettleStatementListMapping());
        String fileName = "车主包装业务待对账运单" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM);
        try {
            YeloExcelUtils.doExportMoreThreadByOneSheet(response, list, false, CarrierWaitSettleStatementListResponseDto.class, fileName);
        } catch (Exception e) {
            return;
        }
    }

    /**
     * 生成对账单
     *
     * @param requestDto 对账单信息
     * @return 操作结果
     */
    @ApiOperation(value = "生成对账单", tags = "1.3.3")
    @PostMapping(value = "/createSettleStatement")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> createSettleStatement(@RequestBody @Valid CarrierCreateSettleStatementRequestDto requestDto) {
        CarrierCreateSettleStatementRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierCreateSettleStatementRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        Result<Boolean> result = carrierSettleStatementClient.createSettleStatement(requestModel);
        result.throwException();
        return result;
    }

    /**
     * 查询税点
     *
     * @param requestDto 车主id
     * @return 税点信息
     */
    @ApiOperation(value = "查询车主税点", tags = "1.2.4")
    @PostMapping(value = "/queryTaxPoint")
    public Result<CarrierTaxPointResponseDto> queryTaxPoint(@RequestBody @Valid CarrierTaxPointRequestDto requestDto) {
        Result<CarrierTaxPointResponseModel> result = carrierSettleStatementClient.queryTaxPoint(MapperUtils.mapper(requestDto, CarrierTaxPointRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), CarrierTaxPointResponseDto.class));
    }

    /**
     * 对账单列表 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "对账单列表")
    @PostMapping(value = "/settleStatementList")
    public Result<PageInfo<CarrierSettleStatementListResponseDto>> settleStatementList(@RequestBody CarrierSettleStatementListRequestDto requestDto) {
        CarrierSettleStatementListRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierSettleStatementListRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        Result<PageInfo<CarrierSettleStatementListResponseModel>> result = carrierSettleStatementClient.settleStatementList(requestModel);
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<CarrierSettleStatementListResponseDto> list = MapperUtils.mapper(pageInfo.getList(), CarrierSettleStatementListResponseDto.class, new CarrierSettleStatementListMapping());
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    /**
     * 对账单列表-导出
     * @param requestDto
     * @param response
     */
    @ApiOperation(value = "对账单列表-导出", tags = "1.2.4")
    @GetMapping(value = "/exportSettleStatementList")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportSettleStatementList(CarrierSettleStatementListRequestDto requestDto, HttpServletResponse response) {
        CarrierSettleStatementListRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierSettleStatementListRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<CarrierSettleStatementListResponseModel>> result = carrierSettleStatementClient.settleStatementList(requestModel);
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<CarrierSettleStatementListResponseDto> responseDtoList = MapperUtils.mapper(pageInfo.getList(), CarrierSettleStatementListResponseDto.class, new CarrierSettleStatementListMapping());
        String fileName = "对账单列表" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        try {
            YeloExcelUtils.doExportMoreThreadByOneSheet(response, responseDtoList, false, CarrierSettleStatementListResponseDto.class, fileName);
        } catch (Exception e) {
            return;
        }
    }

    /**
     * 关联运单号
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "关联运单号", tags = "1.2.4")
    @PostMapping(value = "/associationCarrierOrder")
    public Result<CarrierAssociationCarrierOrderResponseDto> associationCarrierOrder(@RequestBody @Valid CarrierAssociationCarrierOrderRequestDto requestDto) {
        Result<CarrierAssociationCarrierOrderResponseModel> result = carrierSettleStatementClient.associationCarrierOrder(MapperUtils.mapper(requestDto, CarrierAssociationCarrierOrderRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), CarrierAssociationCarrierOrderResponseDto.class, new CarrierAssociationCarrierOrderMapping()));
    }

    /**
     * 编辑对账月份
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "编辑对账月份", tags = "1.2.4")
    @PostMapping(value = "/modifySettleStatementMonth")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> modifySettleStatementMonth(@RequestBody @Valid ModifySettleStatementMonthRequestDto requestDto) {
        ModifySettleStatementMonthRequestModel requestModel = MapperUtils.mapper(requestDto, ModifySettleStatementMonthRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        return carrierSettleStatementClient.modifySettleStatementMonth(requestModel);
    }

    /**
     * 编辑结算主体
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "编辑结算主体", tags = "1.2.4")
    @PostMapping(value = "/modifyPlatformCompany")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> modifyPlatformCompany(@RequestBody @Valid ModifyPlatformCompanyRequestDto requestDto) {
        if (StringUtils.isNotBlank(requestDto.getContractCode()) && requestDto.getContractCode().length() > CommonConstant.INT_THIRTY){
            throw new BizException(ManagementWebApiExceptionEnum.CARRIER_SETTLE_STATEMENT_CONTRACT_CODE_ERROR);
        }
        return carrierSettleStatementClient.modifyPlatformCompany(MapperUtils.mapper(requestDto, ModifyPlatformCompanyRequestModel.class));
    }

    /**
     * 修改对账单费点
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "修改对账单费点", tags = "1.2.4")
    @PostMapping(value = "/modifyTaxPoint")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> modifyTaxPoint(@RequestBody @Valid CarrierModifyTaxPointRequestDto requestDto) {
        return carrierSettleStatementClient.modifyTaxPoint(MapperUtils.mapper(requestDto, CarrierModifyTaxPointRequestModel.class));
    }

    /**
     * 差异调整-回显
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "差异调整-回显", tags = "1.2.4")
    @PostMapping(value = "/queryAdjustCost")
    public Result<CarrierAdjustCostResponseDto> queryAdjustCost(@RequestBody @Valid CarrierQueryAdjustCostRequestDto requestDto) {
        Result<CarrierAdjustCostResponseModel> result = carrierSettleStatementClient.queryAdjustCost(MapperUtils.mapper(requestDto, CarrierQueryAdjustCostRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), CarrierAdjustCostResponseDto.class, new CarrierQueryAdjustCostMapping()));
    }

    /**
     * 差异调整-发起
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "差异调整-发起", tags = "1.2.4")
    @PostMapping(value = "/AdjustCost")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> adjustCost(@RequestBody @Valid CarrierAdjustRequestDto requestDto) {
        return carrierSettleStatementClient.adjustCost(MapperUtils.mapper(requestDto, CarrierAdjustRequestModel.class));
    }

    /**
     * 申请开票v3.18.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "申请开票 v3.18.0")
    @PostMapping(value = "/applyInvoicing")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> applyInvoicing(@RequestBody @Valid SettleStatementApplyInvoicingRequestDto requestDto) {
        return carrierSettleStatementClient.applyInvoicing(MapperUtils.mapper(requestDto, SettleStatementApplyInvoicingRequestModel.class));
    }

    /**
     * 修改对账单名
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "修改对账单名", tags = "1.2.4")
    @PostMapping(value = "/renameStatement")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> renameStatement(@RequestBody @Valid CarrierEditSettleStatementNameRequestDto requestDto) {
        CarrierEditSettleStatementNameRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierEditSettleStatementNameRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        return carrierSettleStatementClient.renameStatement(requestModel);
    }

    /**
     * 撤销对账单
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "撤销对账单", tags = "1.2.4")
    @PostMapping(value = "/cancel")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> cancel(@RequestBody @Valid CarrierCancelRequestDto requestDto) {
        CarrierCancelRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierCancelRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        return carrierSettleStatementClient.cancel(requestModel);
    }

    /**
     * 对账单归档列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "对账单归档列表", tags = "1.2.4")
    @PostMapping(value = "/statementArchiveList")
    public Result<PageInfo<StatementArchiveListResponseDto>> statementArchiveList(@RequestBody @Valid StatementArchiveListRequestDto requestDto) {
        Result<PageInfo<StatementArchiveListResponseModel>> result = carrierSettleStatementClient.statementArchiveList(MapperUtils.mapper(requestDto, StatementArchiveListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<StatementArchiveListResponseDto> list = MapperUtils.mapper(pageInfo.getList(), StatementArchiveListResponseDto.class);
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    /**
     * 对账单归档/编辑
     *
     * @param requestDto 归档原因和凭证
     * @return 操作结果
     */
    @ApiOperation(value = "对账单归档/编辑", tags = "1.2.4")
    @PostMapping(value = "/statementArchive")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> statementArchive(@RequestBody @Valid StatementArchiveRequestDto requestDto) {
        Result<Boolean> result = carrierSettleStatementClient.statementArchive(MapperUtils.mapper(requestDto, StatementArchiveRequestModel.class));
        result.throwException();
        return result;
    }

    /**
     * 查看归档图片
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "查看归档图片", tags = "1.2.4")
    @PostMapping(value = "/archiveTicketList")
    public Result<List<String>> archiveTicketList(@RequestBody @Valid StatementArchiveTicketListRequestDto requestDto) {
        Result<List<String>> result = carrierSettleStatementClient.archiveTicketList(MapperUtils.mapper(requestDto, StatementArchiveTicketListRequestModel.class));
        result.throwException();
        List<String> list = new ArrayList<>();
        if (ListUtils.isNotEmpty(result.getData())){
            Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(result.getData());
            for (String path : result.getData()) {
                list.add(configKeyConstant.fileAccessAddress + imageMap.get(path));
            }
        }
        return Result.success(list);
    }

    /**
     * 查看归档详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "查看归档详情", tags = "1.2.4")
    @PostMapping(value = "/statementArchiveDetail")
    public Result<StatementArchiveDetailResponseDto> statementArchiveDetail(@RequestBody @Valid StatementArchiveDetailRequestDto requestDto) {
        Result<StatementArchiveDetailResponseModel> result = carrierSettleStatementClient.statementArchiveDetail(MapperUtils.mapper(requestDto, StatementArchiveDetailRequestModel.class));
        result.throwException();
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(result.getData().getArchiveTicketList());
        return Result.success(MapperUtils.mapper(result.getData(), StatementArchiveDetailResponseDto.class, new StatementArchiveDetailMapping(configKeyConstant.fileAccessAddress, imageMap)));
    }

    /**
     * 查询对账单下待归档运单
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "查询对账单下待归档运单", tags = "1.2.4")
    @PostMapping(value = "/statementWaitArchiveList")
    public Result<List<StatementWaitArchiveListResponseDto>> statementWaitArchiveList(@RequestBody @Valid StatementWaitArchiveListRequestDto requestDto) {
        Result<List<StatementWaitArchiveListResponseModel>> result = carrierSettleStatementClient.statementWaitArchiveList(MapperUtils.mapper(requestDto, StatementWaitArchiveListRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), StatementWaitArchiveListResponseDto.class));
    }

    /**
     * 对账单详情-提交对账单
     *
     * @param requestDto 对账单id
     * @return 操作结果
     */
    @ApiOperation(value = "对账单详情-提交", tags = "1.2.4")
    @PostMapping(value = "/applicationCheck")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> submitSettleStatement(@RequestBody @Valid CarrierSettleStatementIdRequestDto requestDto) {
        Result<Boolean> result = carrierSettleStatementClient.submitSettleStatement(MapperUtils.mapper(requestDto, CarrierSettleStatementIdRequestModel.class));
        result.throwException();
        return result;
    }

    /**
     * 对账单详情-审核/驳回
     *
     * @param requestDto 对账单id
     * @return 操作结果
     */
    @ApiOperation(value = "对账单详情-审核/驳回", tags = "1.2.4")
    @PostMapping(value = "/auditOrReject")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> auditOrReject(@RequestBody @Valid CarrierChangeSettleStatementStatsRequestDto requestDto) {
        if (CommonConstant.TWO.equalsIgnoreCase(requestDto.getSettleStatementStatus()) && StringUtils.isEmpty(requestDto.getRemark())) {
            //驳回时备注不能为空
            throw new BizException(ManagementWebApiExceptionEnum.REMARK_EMPTY);
        }
        Result<Boolean> result = carrierSettleStatementClient.auditOrReject(MapperUtils.mapper(requestDto, CarrierChangeSettleStatementStatsRequestModel.class));
        result.throwException();
        return result;
    }

    /**
     * 对账单详情合计数据
     *
     * @param requestDto 对账单id
     * @return 合计数据
     */
    @ApiOperation(value = "对账单详情-合计", tags = "1.2.4")
    @PostMapping(value = "/settleStatementDetailTotal")
    public Result<CarrierSettleStatementDetailTotalResponseDto> settleStatementDetailTotal(@RequestBody @Valid CarrierSettleStatementIdRequestDto requestDto) {
        Result<CarrierSettleStatementDetailTotalResponseModel> result = carrierSettleStatementClient.settleStatementDetailTotal(MapperUtils.mapper(requestDto, CarrierSettleStatementIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), CarrierSettleStatementDetailTotalResponseDto.class, new SettleStatementDetailTotalMapping()));
    }

    /**
     * 对账单详情-对账单运单条目信息 3.21.0
     *
     * @param requestDto 对账单id,筛选条件
     * @return 运单条目信息
     */
    @ApiOperation(value = "对账单详情-列表 v2.44(2) ", tags = "1.3.7")
    @PostMapping(value = "/settleStatementDetailList")
    public Result<PageInfo<CarrierSettleStatementDetailListResponseDto>> settleStatementDetailList(@RequestBody @Valid CarrierSettleStatementDetailListRequestDto requestDto) {
        Result<PageInfo<CarrierSettleStatementDetailListResponseModel>> result = carrierSettleStatementClient.settleStatementDetailList(MapperUtils.mapper(requestDto, CarrierSettleStatementDetailListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        pageInfo.setList(MapperUtils.mapper(pageInfo.getList(), CarrierSettleStatementDetailListResponseDto.class, new SettleStatementDetailListMapping()));
        return Result.success(pageInfo);
    }

    /**
     * 对账单详情-导出对账单运单条目信息
     *
     * @param requestDto 对账单id,筛选条件
     */
    @ApiOperation(value = "对账单详情-导出对账单明细", tags = "1.3.7")
    @PostMapping(value = "/exportSettleStatementDetailList")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportSettleStatementDetailList(@RequestBody @Valid CarrierSettleStatementDetailListRequestDto requestDto, HttpServletResponse response) {
        CarrierSettleStatementDetailListRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierSettleStatementDetailListRequestModel.class);
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<CarrierSettleStatementDetailListResponseModel>> result = carrierSettleStatementClient.exportSettleStatementDetailList(requestModel);
        result.throwException();
        PageInfo pageInfo = result.getData();
        List list = MapperUtils.mapper(pageInfo.getList(), CarrierSettleStatementDetailListResponseDto.class, new SettleStatementDetailListMapping());
        String fileName = "对账单运单明细" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        try {
            YeloExcelUtils.doExportMoreThreadByOneSheet(response, list, false, CarrierSettleStatementDetailListResponseDto.class, fileName);
        } catch (Exception e) {
            return;
        }
    }

    /**
     * 对账单详情-导出对计算数据
     *
     * @param requestDto 对账单id,筛选条件
     */
    @ApiOperation(value = "对账单详情-导出结算数据", tags = "1.2.4")
    @PostMapping(value = "/exportSettleStatementDetail")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportSettleStatementDetail(@RequestBody @Valid ExportSettleStatementDetailRequestDto requestDto, HttpServletResponse response, HttpServletRequest request) {
        //合计
        Result<CarrierSettleStatementDetailTotalResponseModel> result = carrierSettleStatementClient.settleStatementDetailTotal(MapperUtils.mapper(requestDto, CarrierSettleStatementIdRequestModel.class));
        result.throwException();
        CarrierSettleStatementDetailTotalResponseDto detail = MapperUtils.mapper(result.getData(), CarrierSettleStatementDetailTotalResponseDto.class, new SettleStatementDetailTotalMapping());
        //运单列表
        CarrierSettleStatementDetailListRequestModel requestModel = new CarrierSettleStatementDetailListRequestModel();
        requestModel.setSettleStatementId(ConverterUtils.toLong(requestDto.getSettleStatementId()));
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<CarrierSettleStatementDetailListResponseModel>> carrierOrderList = carrierSettleStatementClient.settleStatementDetailList(requestModel);
        result.throwException();
        PageInfo pageInfo = carrierOrderList.getData();
        List<CarrierSettleStatementDetailListResponseDto> carrierOrderDtoList = MapperUtils.mapper(pageInfo.getList(), CarrierSettleStatementDetailListResponseDto.class, new SettleStatementDetailListMapping());

        String responseFileName = URLEncoder.encode("车主结算数据", StandardCharsets.UTF_8) + ".xlsx";
        String templateFileName = "/template/carrier_settlement_for_packaging_template.xlsx";
        try (InputStream inputStream = CarrierSettleStatementController.class.getResourceAsStream(templateFileName);
             ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            //读取excel模板
            ExcelWriter excelWriter = EasyExcel.write(bos).withTemplate(inputStream).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            // 填充list
            excelWriter.fill(carrierOrderDtoList, fillConfig, writeSheet);

            //填充头
            Map<String, Object> map = new HashMap<>();
            map.put("companyCarrierName", detail.getCompanyCarrierName());
            map.put("settleStatementCode", detail.getSettleStatementCode());
            map.put("date", detail.getCreatedTime());
            map.put("settleStatementMonth", detail.getSettleStatementMonth());
            map.put("settlementAmountTotal", detail.getSettlementAmountTotal());
            map.put("platformCompanyName", detail.getPlatformCompanyName());
            map.put("carrierFreightTotal", detail.getCarrierFreightTotal());
            map.put("otherFeeTotal", detail.getOtherFeeTotal());
            map.put("adjustFee", detail.getAdjustFee());
            map.put("reconciliationFee", detail.getReconciliationFee());
            map.put("contractCode", detail.getContractCode());
            excelWriter.fill(map, writeSheet);
            excelWriter.finish();

            //写出文件
            DownloadUtils.downloadFile(bos.toByteArray(), responseFileName, response,request);
        } catch (Exception e) {
            log.warn("导出对账单结算数据失败: ", e);
        }
    }

    /**
     * 对账单详情-查询添加运单列表
     *
     * @param requestDto 筛选条件
     * @return 运单信息
     */
    @ApiOperation(value = "对账单详情-查询添加运单列表", tags = "1.2.4")
    @PostMapping(value = "/addCarrierOrderList")
    public Result<PageInfo<CarrierAddCarrierOrderListResponseDto>> addCarrierOrderList(@RequestBody @Valid CarrierAddCarrierOrderListRequestDto requestDto) {
        Result<PageInfo<CarrierWaitSettleStatementListResponseModel>> result = carrierSettleStatementClient.addCarrierOrderList(MapperUtils.mapper(requestDto, CarrierAddCarrierOrderListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        pageInfo.setList(MapperUtils.mapper(pageInfo.getList(), CarrierAddCarrierOrderListResponseDto.class, new AddCarrierOrderListMapping()));
        return Result.success(pageInfo);
    }

    /**
     * 对账单详情-添加运单
     *
     * @param requestDto 对账单id,运单筛选条件
     * @return 操作结果
     */
    @ApiOperation(value = "对账单详情-添加运单", tags = "1.2.4")
    @PostMapping(value = "/addCarrierOrder")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addCarrierOrderConfirm(@RequestBody @Valid CarrierAddCarrierOrderConfirmRequestDto requestDto) {
        Result<Boolean> result = carrierSettleStatementClient.addCarrierOrderConfirm(MapperUtils.mapper(requestDto, CarrierAddCarrierOrderConfirmRequestModel.class));
        result.throwException();
        return result;
    }

    /**
     * 对账单详情-撤销运单
     *
     * @param requestDto 对账单运单id
     * @return 操作结果
     */
    @ApiOperation(value = "对账单详情-撤销运单", tags = "1.2.4")
    @PostMapping(value = "/cancelCarrierOrder")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> cancelCarrierOrder(@RequestBody @Valid CarrierUndoCarrierOrderRequestDto requestDto) {
        Result<Boolean> result = carrierSettleStatementClient.cancelCarrierOrder(MapperUtils.mapper(requestDto, CarrierUndoCarrierOrderRequestModel.class));
        result.throwException();
        return result;
    }

    /**
     * 对账单详情-已对账驳回 3.24.0
     *
     * @param requestDto 对账单id
     * @return 操作结果
     */
    @ApiOperation(value = "对账单详情-已对账驳回")
    @PostMapping(value = "/rejectCompleteSettle")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> rejectCompleteSettle(@RequestBody @Valid CarrierSettleStatementIdRequestDto requestDto) {
        return carrierSettleStatementClient.rejectCompleteSettle(MapperUtils.mapper(requestDto, CarrierSettleStatementIdRequestModel.class));
    }
}
