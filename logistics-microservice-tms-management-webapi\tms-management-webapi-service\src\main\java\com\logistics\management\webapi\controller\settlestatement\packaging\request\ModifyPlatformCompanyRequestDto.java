package com.logistics.management.webapi.controller.settlestatement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/9
 */
@Data
public class ModifyPlatformCompanyRequestDto {

	@ApiModelProperty(value = "对账单id", required = true)
	@NotBlank(message = "id不能为空")
	private String settleStatementId;

	@ApiModelProperty(value = "结算主体", required = true)
	@NotBlank(message = "请选择结算主体")
	private String platformCompanyId;

	@ApiModelProperty(value = "合同号")
	private String contractCode;
}
