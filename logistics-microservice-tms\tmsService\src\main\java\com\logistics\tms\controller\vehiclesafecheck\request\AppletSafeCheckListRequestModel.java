package com.logistics.tms.controller.vehiclesafecheck.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/11/25 11:46
 */
@Data
public class AppletSafeCheckListRequestModel extends AbstractPageForm<AppletSafeCheckListRequestModel>{
    @ApiModelProperty("状态 0未检查、10待确认、20待整改、30已整改、40检查完成")
    private Integer status;
    @ApiModelProperty("司机ID")
    private Long staffId;
    @ApiModelProperty("车辆id拼接，例如 “123,456”")
    private String vehicleIds;
}
