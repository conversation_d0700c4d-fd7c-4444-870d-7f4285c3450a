package com.logistics.appapi.base.enums;

/**
 * @author: wjf
 * @date: 2022/8/19 9:35
 */
public enum VerificationCodeTypeEnum {

    TMS_DRIVER_APPLET_LOGIN(1, "tms司机小程序登录"),
    TMS_DRIVER_APPLET_FIND_PASS(2, "tms司机小程序找回密码"),
    TMS_DRIVER_APPLET_MODIFY_PHONE(3, "tms司机小程序修改手机号"),
    TMS_DRIVER_APPLET_MODIFY_BANKCARD(4, "tms司机小程序添加银行卡"),
    H5_VISITOR_RESERVATION( 5, "访客预约"),
    H5_VISITOR_SIGN_IN( 6, "访客签到"),

    ;

    private Integer key;
    private String value;

    VerificationCodeTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
