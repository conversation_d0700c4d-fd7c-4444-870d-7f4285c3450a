package com.logistics.management.webapi.api.impl.carrierfreight.mapping;

import com.logistics.management.webapi.api.feign.carrierfreight.dto.SearchCarrierFreightResponseDto;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.EnabledEnum;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.carrierfreight.model.SearchCarrierFreightResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * <AUTHOR>
 * @date 2022/9/2 18:00
 */
public class CarrierFreightSelectListMapping extends MapperMapping<SearchCarrierFreightResponseModel, SearchCarrierFreightResponseDto> {
    @Override
    public void configure() {
        SearchCarrierFreightResponseModel source = getSource();
        SearchCarrierFreightResponseDto destination = getDestination();

        destination.setEnableLabel(EnabledEnum.getEnum(source.getEnabled()).getValue());
        destination.setCompanyCarrierName(CompanyTypeEnum.COMPANY.getKey().equals(source.getCompanyCarrierType()) ?
                    source.getCompanyCarrierName() :
                    source.getCarrierContactName() + " "
                    + FrequentMethodUtils.encryptionData(source.getCarrierContactPhone(), EncodeTypeEnum.MOBILE_PHONE));
    }
}
