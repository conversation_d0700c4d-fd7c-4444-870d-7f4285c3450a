package com.logistics.tms.client;

import com.alibaba.fastjson.JSONObject;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.constant.ConfigKeyConstant;
import com.logistics.tms.base.enums.ProductAreaEnum;
import com.logistics.tms.client.feign.basicdata.ocr.IOCRServiceApi;
import com.logistics.tms.client.feign.basicdata.ocr.request.OCRCustomizationIdentifyRequestDto;
import com.logistics.tms.client.feign.basicdata.ocr.request.OCRIdentifyQRCodeRequestDto;
import com.logistics.tms.client.feign.basicdata.ocr.response.OCRCustomizationIdentifyResponseDto;
import com.logistics.tms.client.model.OrgForHierarchyModel;
import com.logistics.tms.controller.baiscinfo.applet.request.FaceRecognitionRequestModel;
import com.logistics.tms.controller.baiscinfo.applet.response.FaceRecognitionResponseModel;
import com.yelo.basicdata.api.feign.datamap.DataMapServiceApi;
import com.yelo.basicdata.api.feign.datamap.model.*;
import com.yelo.basicdata.api.feign.facerecognition.FaceRecognitionServiceApi;
import com.yelo.basicdata.api.feign.facerecognition.model.BestSignFaceRecognitionRequestModel;
import com.yelo.basicdata.api.feign.facerecognition.model.BestSignFaceRecognitionResponseModel;
import com.yelo.basicdata.api.feign.file.FileServiceApi;
import com.yelo.basicdata.api.feign.file.model.*;
import com.yelo.basicdata.api.feign.gaodemap.GaoDeMapServiceApi;
import com.yelo.basicdata.api.feign.gaodemap.model.DirectionDrivingRequestModel;
import com.yelo.basicdata.api.feign.gaodemap.model.GeoCodeForGeoRequestModel;
import com.yelo.basicdata.api.feign.gaodemap.model.GeoCodeForReGeoRequestModel;
import com.yelo.basicdata.api.feign.organization.OrganizationServiceApi;
import com.yelo.basicdata.api.feign.organization.model.GetOrgCodeAndNameResponseModel;
import com.yelo.basicdata.api.feign.organization.model.OrganizationNameResponseModel;
import com.yelo.basicdata.api.feign.thirdpartclient.BestSignClient;
import com.yelo.basicdata.api.feign.verifycode.VerifyCodeServiceApi;
import com.yelo.basicdata.api.feign.verifycode.model.CheckVerificationCodeRequestModel;
import com.yelo.basicdata.api.feign.verifycode.model.VerifyCodeRequestModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author：wjf
 * @date：2021/3/16 15:17
 */
@Slf4j
@Service
public class BasicDataClient {

    @Autowired
    private FileServiceApi fileServiceApi;
    @Autowired
    private VerifyCodeServiceApi verifyCodeServiceApi;
    @Autowired
    private DataMapServiceApi dataMapServiceApi;
    @Autowired
    private GaoDeMapServiceApi gaoDeMapServiceApi;
    @Autowired
    private FaceRecognitionServiceApi faceRecognitionServiceApi;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private BestSignClient bestSignClient;
    @Autowired
    private OrganizationServiceApi organizationServiceApi;
    @Resource
    private IOCRServiceApi iOCRServiceApi;

    /**
     * 拷贝图片
     *
     * @param requestModel
     */
    public void copyFile(FileCopyRequestModel requestModel) {
        fileServiceApi.copyFileOSS(requestModel).throwException();
    }

    /**
     * 获取图片访问路径
     *
     * @param  fileSrc
     */
    public String getImageURL(String fileSrc){
        GetOSSUrlRequestModel requestModel = new GetOSSUrlRequestModel();
        requestModel.setFileSrc(fileSrc);
        Result<GetOSSUrlResponseModel> result = fileServiceApi.getOSSFileUrl(requestModel);
        result.throwException();
        return result.getData().getFileSrc();
    }

    /**
     * 批量获取oss图片预览url
     * @return
     */
    public List<GetOSSUrlResponseModel> batchGetOSSFileUrl(List<String> fileSrcList){
        BatchGetOSSFileUrlRequestModel requestModel = new BatchGetOSSFileUrlRequestModel();
        requestModel.setFileSrcList(fileSrcList);
        Result<List<GetOSSUrlResponseModel>> result = fileServiceApi.batchGetOSSFileUrl(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 发送短信验证码
     * @param requestModel
     */
    public void sendSms(VerifyCodeRequestModel requestModel){
        requestModel.setProductArea(ProductAreaEnum.QIYA.getKey());
        verifyCodeServiceApi.sendSms(requestModel);
    }

    /**
     *  校验验证码
     *
     * @param  source 来源
     * @param  type 验证码类型
     * @param  mobile 手机号
     * @param  smsCode 验证码
     */
    public void checkVerificationCode(Integer source,Integer type,String mobile,String smsCode){
        CheckVerificationCodeRequestModel verificationCodeRequestModel = new CheckVerificationCodeRequestModel();
        verificationCodeRequestModel.setSource(source);
        verificationCodeRequestModel.setVerificationType(type);
        verificationCodeRequestModel.setMobile(mobile);
        verificationCodeRequestModel.setSmsCode(smsCode);
        verifyCodeServiceApi.checkVerificationCode(verificationCodeRequestModel).throwException();
    }

    /**
     * 根据地址查询省市区：调用高德搜索接口查询区code，再查库获取省市区信息
     * @param keywords
     * @return
     */
    public GetProvinceCityAreaByKeywordsNewResponseModel getProvinceCityAreaByKeywordsNew(String keywords){
        GetProvinceCityAreaByKeywordsNewRequestModel requestModel = new GetProvinceCityAreaByKeywordsNewRequestModel();
        requestModel.setKeywords(keywords);
        Result<GetProvinceCityAreaByKeywordsNewResponseModel> result = dataMapServiceApi.getProvinceCityAreaByKeywordsNew(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 根据地址查询经纬度（高德地理编码接口）：返回的是geocodes下的集合，经纬度取Map里的location参数值
     * @param requestModel
     * @return
     */
    public List<Map<String, Object>> geoCodeForGeo(GeoCodeForGeoRequestModel requestModel){
        Result<List<Map<String, Object>>> result = gaoDeMapServiceApi.geoCodeForGeo(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 根据经纬度查询地址信息（高德逆地理编码接口）：批量查询故返回的是regeocodes下的对象集合，详细地址取Map里的formatted_address参数值
     * @param requestModel
     * @return
     */
    public List<Map<String, Object>> geoCodeForReGeo(GeoCodeForReGeoRequestModel requestModel){
        Result<List<Map<String, Object>>> result = gaoDeMapServiceApi.geoCodeForReGeo(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 高德路径规划2.0接口：返回的是paths下的集合
     * @param requestModel
     * @return
     */
    public List<Map<String, Object>> directionDriving(DirectionDrivingRequestModel requestModel){
        Result<List<Map<String, Object>>> result = gaoDeMapServiceApi.directionDriving(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 先根据地址调高德地理接口查询经纬度，没查到再根据地图id查询行政区的经纬度
     *
     * @param requestModel
     * @return
     */
    public GetLonLatByMapIdResponseModel getLonLatByMapId(GetLonLatByMapIdRequestModel requestModel) {
        Result<GetLonLatByMapIdResponseModel> result = dataMapServiceApi.getLonLatByMapId(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 打包压缩图片
     *
     * @param fileMap key为图片名称，value为图片相对路径
     * @return
     */
    public byte[] compressAndDownloadFile(Map<String, List<String>> fileMap) {
        CompressAndDownloadFileRequestModel fileModel = new CompressAndDownloadFileRequestModel();
        fileModel.setFileMap(fileMap);
        Result<CompressAndDownloadFileResponseModel> result = fileServiceApi.compressAndDownloadFileOSS(fileModel);
        result.throwException();
        if (result.getData() == null) {
            return new byte[0];
        }
        return result.getData().getFileByte();
    }

    /**
     * 根据地图ids查询行政区的经纬度
     *
     * @param mapIdList 如市id集合
     * @return 返回市id和市经纬度
     */
    public List<GetLonLatByMapIdResponseModel> getLonLatByMapIds(List<Long> mapIdList) {
        GetLonLatByMapIdsRequestModel requestModel = new GetLonLatByMapIdsRequestModel();
        requestModel.setMapIdList(mapIdList);
        Result<List<GetLonLatByMapIdResponseModel>> result = dataMapServiceApi.getLonLatByMapIds(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 上传图片到oss
     *
     * @param uploadModel
     */
    public void uploadFileOSS(FileUploadRequestModel uploadModel) {
        Result<Boolean> result = fileServiceApi.uploadFileOSS(uploadModel);
        result.throwException();
    }

    /**
     * 获取刷脸认证签名
     *
     * @param requestModel
     * @return
     */
    public FaceRecognitionResponseModel faceRecognition(FaceRecognitionRequestModel requestModel) {
        BestSignFaceRecognitionRequestModel recognitionRequestModel = new BestSignFaceRecognitionRequestModel();
        recognitionRequestModel.setAccount(requestModel.getAccount());
        recognitionRequestModel.setName(requestModel.getName());
        recognitionRequestModel.setIdentity(requestModel.getIdNo());
        recognitionRequestModel.setReturnUrl(configKeyConstant.returnUrl);
        Result<BestSignFaceRecognitionResponseModel> result = faceRecognitionServiceApi.faceRecognition(recognitionRequestModel);
        result.throwException();
        return MapperUtils.mapper(result.getData(), FaceRecognitionResponseModel.class);
    }

    /**
     * 个人三要素验证（一致性验证）
     *
     * @param requestBody
     * @return
     */
    public JSONObject personalIdentity3Mobile(JSONObject requestBody) {
        Result<JSONObject> result = bestSignClient.personalIdentity3Mobile(requestBody);
        result.throwException();
        return result.getData();
    }

    /**
     * 注册个人/企业用户并申请证书
     *
     * @param requestBody
     */
    public JSONObject registerUser(JSONObject requestBody) {
        Result<JSONObject> result = bestSignClient.userReg(requestBody);
        result.throwException();
        return result.getData();
    }

    /**
     * 人脸识别验证结果
     *
     * @param requestBody
     * @return
     */
    public JSONObject verifyFaceRecognitionResult(JSONObject requestBody) {
        Result<JSONObject> result = bestSignClient.webankGetFaceAuthResult(requestBody);
        result.throwException();
        return result.getData();
    }

    /**
     * 个人手机号认证-获取验证码
     *
     * @param requestBody
     */
    public JSONObject getVerifyCode(JSONObject requestBody) {
        Result<JSONObject> result = bestSignClient.identity3VcodeSender(requestBody);
        result.throwException();
        return result.getData();
    }

    /**
     * 校验验证码
     *
     * @param requestBody 验证码
     */
    public JSONObject checkVerifyCode(JSONObject requestBody) {
        Result<JSONObject> result = bestSignClient.identity3VcodeVerify(requestBody);
        result.throwException();
        return result.getData();
    }

    /**
     * 异步申请状态查询
     *
     * @param requestBody 用户唯一标识：企业类型为companyCode，个人类型为手机号
     */
    public JSONObject asyncApplyCertStatus(JSONObject requestBody) {
        Result<JSONObject> result = bestSignClient.asyncApplyCertStatus(requestBody);
        result.throwException();
        return result.getData();
    }

    /**
     * 查询证书编号
     *
     * @param requestBody
     * @return
     */
    public JSONObject getCert(JSONObject requestBody) {
        Result<JSONObject> result = bestSignClient.userGetCert(requestBody);
        result.throwException();
        return result.getData();
    }

    /**
     * 获取证书详细信息
     *
     * @param requestBody
     * @return
     */
    public JSONObject getCertInfo(JSONObject requestBody) {
        Result<JSONObject> result = bestSignClient.userCertInfo(requestBody);
        result.throwException();
        return result.getData();
    }

    /**
     * 个人二要素校验
     *
     * @param requestBody
     * @return
     */
    public JSONObject verifyPersonTwoElements(JSONObject requestBody) {
        Result<JSONObject> result = bestSignClient.personalIdentity2(requestBody);
        result.throwException();
        return result.getData();
    }

    /**
     * 查询所有部门（列表筛选条件接口）
     * @return
     */
    public List<OrganizationNameResponseModel> getAllOrgForHierarchy() {
        var result = organizationServiceApi.getAllOrgForHierarchy();
        result.throwException();
        return result.getData();
    }

    /**
     * 根据用户姓名查询部门名称（多层级显示部门名称）
     * @param userName  用户姓名
     * @return          用户信息&部门信息
     */
    public OrgForHierarchyModel getOrgForHierarchyByUserName(String userName) {
        GetOrgCodeAndNameResponseModel orgCodeAndName = getOrgCodeAndName(userName);
        if (ListUtils.isEmpty(orgCodeAndName.getOrgFullNameList())) {
            return new OrgForHierarchyModel();
        }

        String orgName = orgCodeAndName.getOrgFullNameList().get(orgCodeAndName.getOrgFullNameList().size() - CommonConstant.INTEGER_ONE);
        return orgCodeAndName.getOrgInfo().entrySet()
                .stream()
                .filter(f -> f.getValue().equals(orgName))
                .map(Map.Entry::getKey)
                .findFirst()
                .filter(StringUtils::isNotBlank)
                .map(key -> {
                    return new OrgForHierarchyModel()
                            .setOrgCode(key)
                            .setOrgName(orgCodeAndName.getOrgFullNameList().stream().collect(Collectors.joining("-")));
                })
                .orElse(new OrgForHierarchyModel());
    }

    /**
     * 根据用户姓名查询部门orgCode和姓名
     * @param userName  用户姓名
     * @return          用户信息&部门信息
     */
    public GetOrgCodeAndNameResponseModel getOrgCodeAndName(String userName) {
        Result<GetOrgCodeAndNameResponseModel> result = organizationServiceApi.getOrgCodeAndName(userName);
        result.throwException();
        return result.getData();
    }

    /**
     * 识别二维码
     * @param filePath 文件的相对路径
     * @return 识别内容
     */
    public String qrCodeIdentify(String filePath) {
        Result<String> result = iOCRServiceApi.qrCodeOSSPath(new OCRIdentifyQRCodeRequestDto().setFilePath(filePath));
        result.throwException();
        log.info("======================== 解析二维码内容: 【{}】 ========================", result.getData());
        return result.getData();
    }

    /**
     * 自定义模板识别运单号
     * @param filePath 文件的相对路径
     * @param templateId 图片OCR模板ID
     * @return OCR 识别Model
     */
    public OCRCustomizationIdentifyResponseDto ocrCustomizationTemplateIdentify(String filePath, String templateId) {
        Result<OCRCustomizationIdentifyResponseDto> result = iOCRServiceApi.ocrPicture(new OCRCustomizationIdentifyRequestDto()
                .setFilePath(filePath)
                .setTemplateId(templateId));
        result.throwException();
        return result.getData();
    }
}
