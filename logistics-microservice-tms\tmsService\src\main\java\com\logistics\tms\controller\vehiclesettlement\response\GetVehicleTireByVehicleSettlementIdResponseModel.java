package com.logistics.tms.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2021/7/20 16:02
 */
@Data
public class GetVehicleTireByVehicleSettlementIdResponseModel {
    @ApiModelProperty("车辆结算表id")
    private Long vehicleSettlementId;
    @ApiModelProperty("轮胎费用（未关联任何账单+只关联查询账单）")
    private List<GetVehicleTireListByVehicleSettlementIdResponseModel> vehicleTireList;
    @ApiModelProperty("查询账单已关联的轮胎费用id")
    private List<Long> selectVehicleTireIdList;
}
