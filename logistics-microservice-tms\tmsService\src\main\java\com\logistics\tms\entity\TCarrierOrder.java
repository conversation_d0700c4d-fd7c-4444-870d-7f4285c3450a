package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2024/08/06
*/
@Data
public class TCarrierOrder extends BaseEntity {
    /**
    * 调度单ID
    */
    @ApiModelProperty("调度单ID")
    private Long dispatchOrderId;

    /**
    * 调度单号
    */
    @ApiModelProperty("调度单号")
    private String dispatchOrderCode;

    /**
    * 需求单ID
    */
    @ApiModelProperty("需求单ID")
    private Long demandOrderId;

    /**
    * 需求单号
    */
    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    /**
    * 运单号
    */
    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    /**
    * 客户单号
    */
    @ApiModelProperty("客户单号")
    private String customerOrderCode;

    /**
    * 运单状态 10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收
    */
    @ApiModelProperty("运单状态 10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收")
    private Integer status;

    /**
    * 运单状态更新时间
    */
    @ApiModelProperty("运单状态更新时间")
    private Date statusUpdateTime;

    /**
    * 是否取消 0 否 1 是
    */
    @ApiModelProperty("是否取消 0 否 1 是")
    private Integer ifCancel;

    /**
    * 取消原因
    */
    @ApiModelProperty("取消原因")
    private String cancelReason;

    /**
    * 取消操作人
    */
    @ApiModelProperty("取消操作人")
    private String cancelOperatorName;

    /**
    * 取消时间
    */
    @ApiModelProperty("取消时间")
    private Date cancelTime;

    /**
    * 业务类型：1 公司，2 个人
    */
    @ApiModelProperty("业务类型：1 公司，2 个人")
    private Integer businessType;

    /**
    * 乐橘新生客户名称（企业）
    */
    @ApiModelProperty("乐橘新生客户名称（企业）")
    private String customerName;

    /**
    * 乐橘新生客户姓名（个人）
    */
    @ApiModelProperty("乐橘新生客户姓名（个人）")
    private String customerUserName;

    /**
    * 乐橘新生客户手机号(加密)（个人）
    */
    @ApiModelProperty("乐橘新生客户手机号(加密)（个人）")
    private String customerUserMobile;

    /**
    * 客户订单来源：1 乐橘新生客户，2 司机
    */
    @ApiModelProperty("客户订单来源：1 乐橘新生客户，2 司机")
    private Integer customerOrderSource;

    /**
    * 来源：1 后台 2 前台 3 app
    */
    @ApiModelProperty("来源：1 后台 2 前台 3 app")
    private Integer source;

    /**
    * 调度人ID
    */
    @ApiModelProperty("调度人ID")
    private Long dispatchUserId;

    /**
    * 调度人姓名
    */
    @ApiModelProperty("调度人姓名")
    private String dispatchUserName;

    /**
    * 调度时间/运单生成时间
    */
    @ApiModelProperty("调度时间/运单生成时间")
    private Date dispatchTime;

    /**
    * 车主公司类型：1 公司，2 个人
    */
    @ApiModelProperty("车主公司类型：1 公司，2 个人")
    private Integer companyCarrierType;

    /**
    * 车主公司ID
    */
    @ApiModelProperty("车主公司ID")
    private Long companyCarrierId;

    /**
    * 车主公司名称
    */
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName;

    /**
    * 车主联系人/账号id
    */
    @ApiModelProperty("车主联系人/账号id")
    private Long carrierContactId;

    /**
    * 车主账号名称
    */
    @ApiModelProperty("车主账号名称")
    private String carrierContactName;

    /**
    * 车主账号手机号（原长度50）
    */
    @ApiModelProperty("车主账号手机号（原长度50）")
    private String carrierContactPhone;

    /**
    * 车主公司级别：1 云途，2 二级承运商
    */
    @ApiModelProperty("车主公司级别：1 云途，2 二级承运商")
    private Integer companyCarrierLevel;

    /**
    * 委托方公司id
    */
    @ApiModelProperty("委托方公司id")
    private Long companyEntrustId;

    /**
    * 委托方公司名称
    */
    @ApiModelProperty("委托方公司名称")
    private String companyEntrustName;

    /**
    * 上游客户
    */
    @ApiModelProperty("上游客户")
    private String upstreamCustomer;

    /**
    * 装货时间
    */
    @ApiModelProperty("装货时间")
    private Date loadTime;

    /**
    * 卸货时间
    */
    @ApiModelProperty("卸货时间")
    private Date unloadTime;

    /**
    * 签收时间
    */
    @ApiModelProperty("签收时间")
    private Date signTime;

    /**
    * 运单分配到的S单的预计数量
    */
    @ApiModelProperty("运单分配到的S单的预计数量")
    private BigDecimal expectAmount;

    /**
    * 提货数量（预计）
    */
    @ApiModelProperty("提货数量（预计）")
    private BigDecimal loadAmountExpect;

    /**
    * 提货数量（实际）
    */
    @ApiModelProperty("提货数量（实际）")
    private BigDecimal loadAmount;

    /**
    * 卸货数量（预计）
    */
    @ApiModelProperty("卸货数量（预计）")
    private BigDecimal unloadAmountExpect;

    /**
    * 卸货数量（实际）
    */
    @ApiModelProperty("卸货数量（实际）")
    private BigDecimal unloadAmount;

    /**
    * 签收件数
    */
    @ApiModelProperty("签收件数")
    private BigDecimal signAmount;

    /**
    *  预计委托费用
    */
    @ApiModelProperty(" 预计委托费用")
    private BigDecimal expectEntrustFreight;

    /**
    * 预计委托费用类型 1 单价 2 整车价 
    */
    @ApiModelProperty("预计委托费用类型 1 单价 2 整车价 ")
    private Integer expectEntrustFreightType;

    /**
    * 委托费用类型 1 单价 2 整车价 
    */
    @ApiModelProperty("委托费用类型 1 单价 2 整车价 ")
    private Integer entrustFreightType;

    /**
    * 委托费用 
    */
    @ApiModelProperty("委托费用 ")
    private BigDecimal entrustFreight;

    /**
    * 签收运费
    */
    @ApiModelProperty("签收运费")
    private BigDecimal signFreightFee;

    /**
    * 司机运费价格类型 1 单价 2 一口价
    */
    @ApiModelProperty("司机运费价格类型 1 单价 2 一口价")
    private Integer dispatchFreightFeeType;

    /**
    * 调度运费
    */
    @ApiModelProperty("调度运费")
    private BigDecimal dispatchFreightFee;

    /**
    * 调整费用 可以为负数
    */
    @ApiModelProperty("调整费用 可以为负数")
    private BigDecimal adjustFee;

    /**
    * 多装多卸加价费用
    */
    @ApiModelProperty("多装多卸加价费用")
    private BigDecimal markupFee;

    /**
    * 货物单位：1 件，2 吨，3 方，4 块
    */
    @ApiModelProperty("货物单位：1 件，2 吨，3 方，4 块")
    private Integer goodsUnit;

    /**
    * 委托单来源：1 乐医托盘委托单，2 委托方发单，3 中石化系统，4 扬巴邮件，5 乐橘新生
    */
    @ApiModelProperty("委托单来源：1 乐医托盘委托单，2 委托方发单，3 中石化系统，4 扬巴邮件，5 乐橘新生")
    private Integer demandOrderSource;

    /**
    * 委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨；100 新生回收，101 新生销售
    */
    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨；100 新生回收，101 新生销售")
    private Integer demandOrderEntrustType;

    /**
    * 货主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位
    */
    @ApiModelProperty("货主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer settlementTonnage;

    /**
    * 车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位
    */
    @ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer carrierSettlement;

    /**
    * 预计里程数（公里）
    */
    @ApiModelProperty("预计里程数（公里）")
    private BigDecimal expectMileage;

    /**
    * 配置距离（公里）
    */
    @ApiModelProperty("配置距离（公里）")
    private BigDecimal configDistance;

    /**
    * 提货时效（天）
    */
    @ApiModelProperty("提货时效（天）")
    private Integer loadValidity;

    /**
    * 云仓异常数量
    */
    @ApiModelProperty("云仓异常数量")
    private BigDecimal abnormalAmount;

    /**
    * 是否放空：0 否，1 是
    */
    @ApiModelProperty("是否放空：0 否，1 是")
    private Integer ifEmpty;

    /**
    * 放空时间
    */
    @ApiModelProperty("放空时间")
    private Date emptyTime;

    /**
    * 下单备注
    */
    @ApiModelProperty("下单备注")
    private String remark;

    /**
    * 调度备注
    */
    @ApiModelProperty("调度备注")
    private String dispatchRemark;

    /**
    * 车主价格：1 单价(元/吨，元/件)，2 一口价(元)
    */
    @ApiModelProperty("车主价格：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer carrierPriceType;

    /**
    * 车主价格
    */
    @ApiModelProperty("车主价格")
    private BigDecimal carrierPrice;

    /**
    * 打印提货单次数
    */
    @ApiModelProperty("打印提货单次数")
    private Integer printCount;

    /**
    * 是否异常：0 否，1 是
    */
    @ApiModelProperty("是否异常：0 否，1 是")
    private Integer ifObjection;

    /**
    * 出库状态：0 待出库，1 部分出库，2 已出库
    */
    @ApiModelProperty("出库状态：0 待出库，1 部分出库，2 已出库")
    private Integer outStatus;

    /**
    * 下单人
    */
    @ApiModelProperty("下单人")
    private String publishName;

    /**
    * 下单人手机号(加密)
    */
    @ApiModelProperty("下单人手机号(加密)")
    private String publishMobile;

    /**
    * 需求单下单时间
    */
    @ApiModelProperty("需求单下单时间")
    private Date publishTime;

    /**
    * 下单人部门code
    */
    @ApiModelProperty("下单人部门code")
    private String publishOrgCode;

    /**
    * 下单人部门名称
    */
    @ApiModelProperty("下单人部门名称")
    private String publishOrgName;

    /**
    * 是否加急：0 否，1 是
    */
    @ApiModelProperty("是否加急：0 否，1 是")
    private Integer ifUrgent;

    /**
    * 周末是否可上门：0 空，1 是，2 否
    */
    @ApiModelProperty("周末是否可上门：0 空，1 是，2 否")
    private Integer availableOnWeekends;

    /**
    * 装卸方: 0 空，1 我司装卸，2 客户装卸
    */
    @ApiModelProperty("装卸方: 0 空，1 我司装卸，2 客户装卸")
    private Integer loadingUnloadingPart;

    /**
    * 装卸费用
    */
    @ApiModelProperty("装卸费用")
    private BigDecimal loadingUnloadingCharge;

    /**
    * 回收任务类型：1 日常回收，2 加急或节假日回收
    */
    @ApiModelProperty("回收任务类型：1 日常回收，2 加急或节假日回收")
    private Integer recycleTaskType;

    /**
    * 项目标签（多个标签,拼接）：1 石化板块，2 轮胎板块，3 涂料板块，4 其他
    */
    @ApiModelProperty("项目标签（多个标签,拼接）：1 石化板块，2 轮胎板块，3 涂料板块，4 其他")
    private String projectLabel;

    /**
    * 是否存在待审核车辆：0 否，1 是
    */
    @ApiModelProperty("是否存在待审核车辆：0 否，1 是")
    private Integer ifWaitAuditVehicle;

    /**
    * 纠错状态：0 待纠错，1 已纠错，2 无需纠错
    */
    @ApiModelProperty("纠错状态：0 待纠错，1 已纠错，2 无需纠错")
    private Integer correctStatus;

    /**
    * 入库状态：0 未入库，3 已入库
    */
    @ApiModelProperty("入库状态：0 未入库，3 已入库")
    private Integer stockInState;

    /**
    * 车主对账单状态: -3 待完结，-2 未关联，-1 待提交，0 待业务审核，1 待财务审核，2 已对账，3 已驳回
    */
    @ApiModelProperty("车主对账单状态: -3 待完结，-2 未关联，-1 待提交，0 待业务审核，1 待财务审核，2 已对账，3 已驳回")
    private Integer carrierSettleStatementStatus;

    /**
    * 运单二维码图片路径
    */
    @ApiModelProperty("运单二维码图片路径")
    private String qrCodePicPath;

    /**
    * 提货方式：1 票据提货，2 二维码提货
    */
    @ApiModelProperty("提货方式：1 票据提货，2 二维码提货 3编码提货")
    private Integer deliveryMethod;

    /**
    * 接单模式：1 指定车主，2 竞价抢单
    */
    @ApiModelProperty("接单模式：1 指定车主，2 竞价抢单")
    private Integer orderMode;

    /**
    * 车长id
    */
    @ApiModelProperty("车长id")
    private Long vehicleLengthId;

    /**
    * 车长（米）
    */
    @ApiModelProperty("车长（米）")
    private BigDecimal vehicleLength;

    /**
    * 议价模式：1 指定车主，2 竞价抢单，3 临时定价，4 零担定价
    */
    @ApiModelProperty("议价模式：1 指定车主，2 竞价抢单，3 临时定价，4 零担定价")
    private Integer bargainingMode;


    /**
     * 是否按码回收 0：否 1：是
     */
    @ApiModelProperty("是否按码回收 0：否 1：是")
    private Integer ifRecycleByCode;


    /**
     * 是否是额外补的运单0：否1：是
     */
    @ApiModelProperty("是否是额外补的运单0：否1：是")
    private Integer ifExtCarrierOrder;


    /**
     * 对账单上的临时费用费点
     */
    @ApiModelProperty("对账单上的临时费用费点")
    private BigDecimal statementOtherFeeTaxPoint;



    /**
     * 对账单上的运费费点
     */
    @ApiModelProperty("对账单上的运费费点")
    private BigDecimal statementFreightTaxPoint ;




    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}