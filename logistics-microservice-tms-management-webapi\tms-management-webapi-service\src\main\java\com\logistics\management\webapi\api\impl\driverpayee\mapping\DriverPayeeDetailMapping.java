package com.logistics.management.webapi.api.impl.driverpayee.mapping;

import com.logistics.management.webapi.api.feign.common.dto.CertificatePictureDto;
import com.logistics.management.webapi.api.feign.driverpayee.dto.DriverPayeeDetailResponseDto;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.tms.api.feign.driverpayee.model.DriverPayeeDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/3/28 12:35
 */
public class DriverPayeeDetailMapping extends MapperMapping<DriverPayeeDetailResponseModel,DriverPayeeDetailResponseDto> {
    private ConfigKeyConstant configKeyConstant;
    private Map<String,String> imageMap;

    public DriverPayeeDetailMapping() {
    }

    public DriverPayeeDetailMapping(ConfigKeyConstant configKeyConstant, Map<String,String> imageMap) {
        this.configKeyConstant = configKeyConstant;
        this.imageMap = imageMap;
    }

    @Override
    public void configure() {
        DriverPayeeDetailResponseModel model = getSource();
        DriverPayeeDetailResponseDto dto = getDestination();
        if(StringUtils.isNotBlank(model.getIdentityFront())){
            dto.setIdentityFrontSrc(configKeyConstant.fileAccessAddress+imageMap.get(model.getIdentityFront()));
        }
        if(StringUtils.isNotBlank(model.getIdentityBack())){
            dto.setIdentityBackSrc(configKeyConstant.fileAccessAddress+imageMap.get(model.getIdentityBack()));
        }
        if(ListUtils.isNotEmpty(dto.getImageList())){
            for(CertificatePictureDto certificatePictureDto : dto.getImageList()){
                if(StringUtils.isNotBlank(certificatePictureDto.getFilePath())){
                    certificatePictureDto.setFilePathSrc(configKeyConstant.fileAccessAddress+imageMap.get(certificatePictureDto.getFilePath()));
                }
            }
        }
    }
}
