package com.logistics.tms.biz.biddingorder;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.driverpayee.model.ViewLogResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.biz.biddingorder.bo.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.demandorder.DemandOrderForLeYiBiz;
import com.logistics.tms.biz.demandorder.model.BiddingOrderSelectCarrierDemandModel;
import com.logistics.tms.biz.demandorder.model.BiddingOrderSelectCarrierModel;
import com.logistics.tms.biz.demandorder.model.CompanyCarrierByIdModel;
import com.logistics.tms.biz.messagenotice.MessageNoticeCommonBiz;
import com.logistics.tms.biz.messagenotice.model.AddMessageNoticeModel;
import com.logistics.tms.biz.sysconfig.SysConfigBiz;
import com.logistics.tms.controller.biddingorder.request.*;
import com.logistics.tms.controller.biddingorder.response.*;
import com.logistics.tms.delayqueue.DelayQueueBizTypeEnum;
import com.logistics.tms.delayqueue.biddingorder.msg.BiddingOrderDelayMsg;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 竞价单业务处理类
 * <AUTHOR>
 * @date 2024/04/28
 */
@Slf4j
@Service
public class BiddingOrderBiz {
    @Resource
    private TOperateLogsMapper tOperateLogsMapper;
    @Resource
    private TDemandOrderMapper tDemandOrderMapper;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private TBiddingOrderMapper tBiddingOrderMapper;
    @Resource
    private TBiddingOrderDemandMapper tBiddingOrderDemandMapper;
    @Resource
    private TVehicleLengthMapper tVehicleLengthMapper;
    @Resource
    private TBiddingOrderQuoteMapper biddingOrderQuoteMapper;
    @Resource
    private TBiddingOrderCompanyMapper tBiddingOrderCompanyMapper;
    @Resource
    private TCompanyCarrierMapper  tCompanyCarrierMapper;
    @Resource
    private TCarrierOrderMapper  tCarrierOrderMapper;
    @Resource
    private DemandOrderForLeYiBiz demandOrderForLeYiBiz;
    @Resource
    private MessageNoticeCommonBiz messageNoticeCommonBiz;
    @Resource
    private SysConfigBiz sysConfigBiz;

    /**
     * 后台查询竞价单
     *
     * @param requestModel
     * @return {@link PageInfo}<{@link SearchBiddingOrderListResponseModel}>
     */
    public PageInfo<SearchBiddingOrderListResponseModel> searchBiddingOrderListByManager(SearchBiddingOrderListRequestModel requestModel) {
        SearchBiddingOrderListReqBo searchBiddingOrderListReqBo = MapperUtils.mapper(requestModel, SearchBiddingOrderListReqBo.class);
        //竞价单第一个需求单地址过滤
        Map<Long, DemandInfoBo> firstDemandInfoMap = new HashMap<>();
        if (StrUtil.isNotBlank(requestModel.getLoadAddress()) || StrUtil.isNotBlank(requestModel.getUnloadAddress())) {
            firstDemandInfoMap = selectFirstDemandInfo(requestModel.getLoadAddress(), requestModel.getUnloadAddress(), null);
            if (firstDemandInfoMap.isEmpty()){
                return new PageInfo<>(new ArrayList<>());
            }
            searchBiddingOrderListReqBo.setBiddingOrderIdsByAddress(new ArrayList<>(firstDemandInfoMap.keySet()));
        }
        //查询完成车长过滤
        Map<Long, VehicleLengthBo> completeVehicleLengthMap = new HashMap<>();
        if (null != requestModel.getVehicleLength()) {
            completeVehicleLengthMap = selectEndCompleteVehicleLength(requestModel.getVehicleLength(), null);
            if (completeVehicleLengthMap.isEmpty()){
                return new PageInfo<>(new ArrayList<>());
            }
            searchBiddingOrderListReqBo.setBiddingOrderIdsByVehicleLength(new ArrayList<>(completeVehicleLengthMap.keySet()));
        }
        requestModel.enablePaging();
        List<SearchBiddingOrderListRespBo> result = tBiddingOrderMapper.searchBiddingOrderListByManager(searchBiddingOrderListReqBo);
        if (CollUtil.isEmpty(result)) {
            return new PageInfo<>(new ArrayList<>());
        }
        PageInfo pageInfo = new PageInfo(result);
        List<Long> biddingOrderIds = result.stream().map(SearchBiddingOrderListRespBo::getBiddingOrderId).collect(Collectors.toList());
        if (CollUtil.isEmpty(firstDemandInfoMap)) {
            firstDemandInfoMap = selectFirstDemandInfo(null, null, biddingOrderIds);
        }
        if (CollUtil.isEmpty(completeVehicleLengthMap)) {
            completeVehicleLengthMap = selectEndCompleteVehicleLength(null, biddingOrderIds);
        }
        //查询完成状态总价
        Map<Long, QuotePriceBo> completeQuotePriceMap = selectEndCompleteQuotePrice(biddingOrderIds);
        //查询已经竞价过的车主数量
        Map<Long, List<TBiddingOrderQuote>> carrierCountMap = selectQuote(biddingOrderIds);
        List<SearchBiddingOrderListResponseModel> responseModelList=new ArrayList<>();
        for (SearchBiddingOrderListRespBo searchBiddingOrderListRespBo : result) {
            SearchBiddingOrderListResponseModel responseModel = MapperUtils.mapper(searchBiddingOrderListRespBo, SearchBiddingOrderListResponseModel.class);
            responseModel.setBiddingStatus(getBiddingOrderStatusEnum(searchBiddingOrderListRespBo.getBiddingStatus(),searchBiddingOrderListRespBo.getIfCancel()).getKey());
            responseModel.setBiddingStatusLabel(BiddingOrderStatusEnum.getEnum(responseModel.getBiddingStatus()).getValue());
            responseModel.setHandlingModeLabel(HandlingModeEnum.getEnum(searchBiddingOrderListRespBo.getHandlingMode()).getValue());
            //发货地 收货地
            DemandInfoBo demandInfoBo = firstDemandInfoMap.getOrDefault(searchBiddingOrderListRespBo.getBiddingOrderId(), new DemandInfoBo());
            responseModel.setLoadAddress(demandInfoBo.getLoadAddress());
            responseModel.setUnloadAddress(demandInfoBo.getUnloadAddress());
            responseModel.setGoodsUnit(GoodsUnitEnum.getEnum(demandInfoBo.getGoodsUnit()).getUnit());
            responseModel.setGoodsCount(searchBiddingOrderListRespBo.getGoodsCount().stripTrailingZeros());
            //完成车长
            VehicleLengthBo vehicleLengthBo = completeVehicleLengthMap.getOrDefault(searchBiddingOrderListRespBo.getBiddingOrderId(), new VehicleLengthBo());
            responseModel.setVehicleLength(vehicleLengthBo.getVehicleLength());
            //完成总价
            QuotePriceBo quotePriceBo = completeQuotePriceMap.getOrDefault(searchBiddingOrderListRespBo.getBiddingOrderId(),new QuotePriceBo());
            responseModel.setQuotePrice(this.getTotalQuotePrice(quotePriceBo.getQuotePriceType(),quotePriceBo.getQuotePrice(),searchBiddingOrderListRespBo.getGoodsCount()));
            //已经参加竞价数量
            responseModel.setCarrierCount(carrierCountMap.getOrDefault(searchBiddingOrderListRespBo.getBiddingOrderId(),new ArrayList<>()).size());
            responseModelList.add(responseModel);
        }
        pageInfo.setList(responseModelList);
        return pageInfo;
    }

    /**
     * 获取竞价单状态
     * @param biddingStatus
     * @param ifCancel
     * @return {@link BiddingOrderStatusEnum}
     */
    private BiddingOrderStatusEnum getBiddingOrderStatusEnum(Integer biddingStatus, Integer ifCancel){
        if(BiddingOrderIfCancelEnum.YES.getKey().equals(ifCancel)){
            return BiddingOrderStatusEnum.QUOTATION_CANCEL;
        }
        return BiddingOrderStatusEnum.getEnum(biddingStatus);
    }

    /**
     * 查询竞价单第一条需求单
     *
     * @param loadAddress
     * @param unloadAddress
     * @return {@link List}
     */
    private Map<Long, DemandInfoBo> selectFirstDemandInfo(String loadAddress, String unloadAddress, List<Long> biddingOrderIds) {
        List<DemandInfoBo> result = tBiddingOrderDemandMapper.selectFirstDemandInfo(loadAddress, unloadAddress, biddingOrderIds);
        return result.stream().collect(Collectors.toMap(DemandInfoBo::getBiddingOrderId, Function.identity()));
    }

    /**
     * 获取报价完成车长
     *
     * @param vehicleLengthId
     * @param biddingOrderIds
     * @return {@link Map}<{@link Long}, {@link VehicleLengthBo}>
     */
    private Map<Long, VehicleLengthBo> selectEndCompleteVehicleLength(Long vehicleLengthId, List<Long> biddingOrderIds) {
        List<VehicleLengthBo> result = biddingOrderQuoteMapper.selectEndCompleteVehicleLength(vehicleLengthId, biddingOrderIds);
        return result.stream().collect(Collectors.toMap(VehicleLengthBo::getBiddingOrderId, Function.identity()));
    }

    /**
     * 获取完成报价
     * @return {@link Map}<{@link Long}, {@link BigDecimal}>
     */
    private Map<Long, QuotePriceBo> selectEndCompleteQuotePrice(List<Long> biddingOrderIds){
        List<QuotePriceBo> result=biddingOrderQuoteMapper.selectEndCompleteQuotePrice(biddingOrderIds);
        return result.stream().collect(Collectors.toMap(QuotePriceBo::getBiddingOrderId,Function.identity()));
    }

    /**
     * 查询竞价单车主竞价数量
     * @param biddingOrderIds
     * @return {@link Map}<{@link Long}, {@link List}<{@link TBiddingOrderQuote}>>
     */
    private Map<Long, List<TBiddingOrderQuote>> selectQuote(List<Long> biddingOrderIds){
        if(CollUtil.isEmpty(biddingOrderIds)){
            return new HashMap<>();
        }
        SelectBiddingOrderQuoteByConditionBo selectBiddingOrderQuoteByConditionBo=new SelectBiddingOrderQuoteByConditionBo();
        selectBiddingOrderQuoteByConditionBo.setBiddingOrderIds(biddingOrderIds);
        List<TBiddingOrderQuote> biddingOrderQuotes = biddingOrderQuoteMapper.selectBiddingOrderQuoteByCondition(selectBiddingOrderQuoteByConditionBo);
        return biddingOrderQuotes.stream().collect(Collectors.groupingBy(TBiddingOrderQuote::getBiddingOrderId));
    }


    /**
     * 前台列表查询
     * @param requestModel
     * @return {@link PageInfo}<{@link SearchBiddingOrderListResponseModel}>
     */
    public PageInfo<SearchBiddingOrderListResponseModel> searchBiddingOrderListByCustomer(SearchBiddingOrderListRequestModel requestModel) {
        Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        //查询当前车主是否定向
        TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(companyCarrierId);
        if(tCompanyCarrier==null|| IfValidEnum.INVALID.getKey().equals(tCompanyCarrier.getValid())){
            return new PageInfo<>(new ArrayList<>());
        }

        //组装列表查询参数
        SearchBiddingOrderListByCustomerReqBo reqBo=MapperUtils.mapper(requestModel,SearchBiddingOrderListByCustomerReqBo.class);
        reqBo.setCompanyCarrierId(companyCarrierId);
        reqBo.setCompanyCarrierIfAddBlacklist(tCompanyCarrier.getIfAddBlacklist());

        //竞价单第一个需求单
        Map<Long, DemandInfoBo> firstDemandInfoMap = new HashMap<>();
        if (StrUtil.isNotBlank(requestModel.getLoadAddress()) || StrUtil.isNotBlank(requestModel.getUnloadAddress())) {
            firstDemandInfoMap = selectFirstDemandInfo(requestModel.getLoadAddress(), requestModel.getUnloadAddress(), null);
            if (firstDemandInfoMap.isEmpty()){
                return new PageInfo<>(new ArrayList<>());
            }
            reqBo.setBiddingOrderIdsByAddress(new ArrayList<>(firstDemandInfoMap.keySet()));
        }

        //查询主表
        requestModel.enablePaging();
        List<SearchBiddingOrderListByCustomerRespBo> result=tBiddingOrderMapper.searchBiddingOrderListByCustomer(reqBo);
        PageInfo pageInfo=new PageInfo(result);
        if (CollUtil.isEmpty(result)) {
            return new PageInfo<>(new ArrayList<>());
        }
        List<Long> biddingOrderIds = result.stream().map(SearchBiddingOrderListByCustomerRespBo::getBiddingOrderId).collect(Collectors.toList());
        if (CollUtil.isEmpty(firstDemandInfoMap)) {
            firstDemandInfoMap = selectFirstDemandInfo(null, null, biddingOrderIds);
        }
        List<SearchBiddingOrderListResponseModel> responseModelList=new ArrayList<>();
        for (SearchBiddingOrderListByCustomerRespBo searchBiddingOrderListRespBo : result) {
            SearchBiddingOrderListResponseModel responseModel = MapperUtils.mapper(searchBiddingOrderListRespBo, SearchBiddingOrderListResponseModel.class);
            responseModel.setBiddingStatus(getBiddingOrderStatusEnumByCustomer(searchBiddingOrderListRespBo.getBiddingStatus()
                    ,searchBiddingOrderListRespBo.getIfCancel(),searchBiddingOrderListRespBo.getQuoteStatus()).getKey());
            responseModel.setBiddingStatusLabel(BiddingOrderStatusEnum.getEnum(responseModel.getBiddingStatus()).getValue());
            responseModel.setHandlingModeLabel(HandlingModeEnum.getEnum(searchBiddingOrderListRespBo.getHandlingMode()).getValue());
            responseModel.setVehicleLength(searchBiddingOrderListRespBo.getVehicleLength());
            //发货地 收货地
            DemandInfoBo demandInfoBo = firstDemandInfoMap.getOrDefault(searchBiddingOrderListRespBo.getBiddingOrderId(), new DemandInfoBo());
            responseModel.setLoadAddress(demandInfoBo.getLoadAddress());
            responseModel.setUnloadAddress(demandInfoBo.getUnloadAddress());
            responseModel.setGoodsUnit(GoodsUnitEnum.getEnum(demandInfoBo.getGoodsUnit()).getUnit());
            responseModel.setGoodsCount(searchBiddingOrderListRespBo.getGoodsCount().stripTrailingZeros());
            responseModel.setQuotePrice(this.getTotalQuotePrice(searchBiddingOrderListRespBo.getQuotePriceType()
                    ,searchBiddingOrderListRespBo.getQuotePrice(),searchBiddingOrderListRespBo.getGoodsCount()));
            responseModelList.add(responseModel);
        }
        pageInfo.setList(responseModelList);
        return pageInfo;
    }

    /**
     * 前台状态
     * @param biddingStatus
     * @param ifCancel
     * @param quoteStatus
     * @return {@link BiddingOrderStatusEnum}
     */
    private BiddingOrderStatusEnum getBiddingOrderStatusEnumByCustomer(Integer biddingStatus, Integer ifCancel, Integer quoteStatus){
        if(BiddingOrderIfCancelEnum.YES.getKey().equals(ifCancel)){
            return BiddingOrderStatusEnum.QUOTATION_CANCEL;
        }
        if(QuoteStatusEnum.CANCELLED.getKey().equals(quoteStatus)){
            return BiddingOrderStatusEnum.QUOTATION_CANCEL;
        }
        if(BiddingOrderStatusEnum.COMPLETE_QUOTATION.getKey().equals(biddingStatus)
                &&(null==quoteStatus ||QuoteStatusEnum.UNSELECTED.getKey().equals(quoteStatus))){
            return BiddingOrderStatusEnum.QUOTATION_CANCEL;
        }
        return BiddingOrderStatusEnum.getEnum(biddingStatus);
    }

    /**
     * 获取最低价 v3.20.0
     * @param requestModel 请求参数
     * @return 最低价
     */
    public BottomPriceResponseModel bottomPrice(BottomPriceRequestModel requestModel) {
        // 竞价单
        TBiddingOrder tBiddingOrder = tBiddingOrderMapper.selectByPrimaryKey(requestModel.getBiddingOrderId());
        if (tBiddingOrder == null || IfValidEnum.INVALID.getKey().equals(tBiddingOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
        }
        BottomPriceResponseModel bottomPriceResponseModel = new BottomPriceResponseModel();
        List<TVehicleLength> tVehicleLengths = tVehicleLengthMapper.selectRangeGoodsCount(requestModel.getGoodsCount());
        if (ListUtils.isEmpty(tVehicleLengths)) {
            throw new BizException(CarrierDataExceptionEnum.NOT_GOODS_COUNT_RANGE);
        }
        TVehicleLength tVehicleLength = tVehicleLengths.get(CommonConstant.INTEGER_ZERO);
        LocalDate currentDate = LocalDate.now();
        int lastYear = currentDate.getYear() - 1;
        BottomPriceRequestBo bottomPriceRequestBo = new BottomPriceRequestBo();
        bottomPriceRequestBo.setLoadProvinceId(requestModel.getLoadProvinceId());
        bottomPriceRequestBo.setLoadCityId(requestModel.getLoadCityId());
        bottomPriceRequestBo.setLoadAreaId(requestModel.getLoadAreaId());
        bottomPriceRequestBo.setUnloadProvinceId(requestModel.getUnloadProvinceId());
        bottomPriceRequestBo.setUnloadCityId(requestModel.getUnloadCityId());
        bottomPriceRequestBo.setUnloadAreaId(requestModel.getUnloadAreaId());
        bottomPriceRequestBo.setCarriageScopeMax(tVehicleLength.getCarriageScopeMax());
        bottomPriceRequestBo.setCarriageScopeMin(tVehicleLength.getCarriageScopeMin());
        bottomPriceRequestBo.setLastYear(lastYear);
        BigDecimal minCarrierPrice = tCarrierOrderMapper.selectMinCarrierPrice(bottomPriceRequestBo);
        bottomPriceResponseModel.setBottomPrice(minCarrierPrice);
        if (minCarrierPrice != null) {
            TBiddingOrder updateBiddingOrder = new TBiddingOrder();
            updateBiddingOrder.setId(tBiddingOrder.getId());
            updateBiddingOrder.setLowestPrice(minCarrierPrice);
            commonBiz.setBaseEntityModify(updateBiddingOrder, BaseContextHandler.getUserName());
            tBiddingOrderMapper.updateByPrimaryKeySelective(updateBiddingOrder);
        }
        return bottomPriceResponseModel;
    }

    /**
     * 竞价查询需求单 v3.20.0
     * @param requestModel 请求参数
     * @return SearchBiddingDemandResponseModel
     */
    public List<SearchBiddingDemandResponseModel> searchBiddingDemand(SearchBiddingDemandRequestModel requestModel){
        return tDemandOrderMapper.searchBiddingDemand(requestModel);
    }

    /**
     * 车主报价详情 v3.20.0
     * @param requestModel 请求参数
     * @return 车主报价详情
     */
    public BiddingOrderQuoteDetailResponseModel biddingOrderQuoteDetail(BiddingOrderQuoteDetailRequestModel requestModel){
        // 报价单
        TBiddingOrderQuote tBiddingOrderQuote = biddingOrderQuoteMapper.selectByPrimaryKeyDecrypt(requestModel.getBiddingOrderQuoteId());
        if(tBiddingOrderQuote == null || IfValidEnum.INVALID.getKey().equals(tBiddingOrderQuote.getValid())){
            throw new BizException(CarrierDataExceptionEnum.ORDER_QUOTE_NOT_EXIST);
        }
        List<TBiddingOrderDemand> tBiddingOrderDemands =
                tBiddingOrderDemandMapper.selectByBiddingOrderId(tBiddingOrderQuote.getBiddingOrderId());
        List<Long> demandIds =
                tBiddingOrderDemands.stream().map(TBiddingOrderDemand::getDemandOrderId).collect(Collectors.toList());
        List<BiddingQuoteDemandModel> biddingQuoteDemandModels = tDemandOrderMapper.selectQuoteDemand(demandIds);
        List<TDemandOrder> byIds = tDemandOrderMapper.getByIds(StringUtils.listToString(demandIds, ','));
        Map<Long, TDemandOrder> demandOrderMap = byIds.stream().collect(Collectors.toMap(TDemandOrder::getId, e -> e));
        BigDecimal goodsAmount = BigDecimal.ZERO;
        for (TDemandOrder tDemandOrder : byIds) {
            goodsAmount = goodsAmount.add(tDemandOrder.getGoodsAmount());
        }
        BigDecimal rawQuotePrice = tBiddingOrderQuote.getQuotePrice();
        if(PriceTypeEnum.UNIT_PRICE.getKey().equals(tBiddingOrderQuote.getQuotePriceType())){
            tBiddingOrderQuote.setQuotePrice(tBiddingOrderQuote.getQuotePrice().multiply(goodsAmount));
        }
        if (ListUtils.isNotEmpty(biddingQuoteDemandModels)) {
            for (BiddingQuoteDemandModel demandModel: biddingQuoteDemandModels){
                demandModel.setOnePrice(tBiddingOrderQuote.getQuotePrice().divide(
                        new BigDecimal(tBiddingOrderDemands.size()), RoundingMode.HALF_UP));
                demandModel.setUnitPrice(tBiddingOrderQuote.getQuotePrice().divide(
                        goodsAmount, RoundingMode.HALF_UP).multiply(demandOrderMap.get(demandModel.getDemandId()).getGoodsAmount()));
            }
        }
        BiddingOrderQuoteDetailResponseModel responseModel = new BiddingOrderQuoteDetailResponseModel();
        responseModel.setBiddingOrderQuoteId(tBiddingOrderQuote.getId());
        responseModel.setQuotePrice(tBiddingOrderQuote.getQuotePrice().stripTrailingZeros());
        if(PriceTypeEnum.UNIT_PRICE.getKey().equals(tBiddingOrderQuote.getQuotePriceType())){
            responseModel.setUnitPrice(rawQuotePrice.stripTrailingZeros());
        }
        responseModel.setVehicleLengthId(tBiddingOrderQuote.getVehicleLengthId());
        responseModel.setVehicleLength(tBiddingOrderQuote.getVehicleLength());
        responseModel.setCarrierContactName(tBiddingOrderQuote.getCompanyCarrierName());
        if(CompanyTypeEnum.PERSON.getKey().equals(tBiddingOrderQuote.getCompanyCarrierType())){
            responseModel.setCarrierContactName(tBiddingOrderQuote.getCarrierContactName()+" "+tBiddingOrderQuote.getCarrierContactPhone());
        }
        responseModel.setQuotePriceType(tBiddingOrderQuote.getQuotePriceType());
        responseModel.setDemandDtoList(biddingQuoteDemandModels);
        return responseModel;
    }


    /**
     * 修改报价 v3.20.0
     * @param requestModel 请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void modifyQuote(ModifyQuoteRequestModel requestModel){
        // 报价单
        TBiddingOrderQuote tBiddingOrderQuote = biddingOrderQuoteMapper.selectByPrimaryKey(requestModel.getBiddingOrderQuoteId());
        if(tBiddingOrderQuote == null || IfValidEnum.INVALID.getKey().equals(tBiddingOrderQuote.getValid())){
            throw new BizException(CarrierDataExceptionEnum.ORDER_QUOTE_NOT_EXIST);
        }
        // 竞价单
        TBiddingOrder tBiddingOrder = tBiddingOrderMapper.selectByPrimaryKey(tBiddingOrderQuote.getBiddingOrderId());
        if (tBiddingOrder == null || IfValidEnum.INVALID.getKey().equals(tBiddingOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
        }
        // 【报价中】、【待选择】状态可操作
        if ((!BiddingOrderStatusEnum.IN_QUOTATION.getKey().equals(tBiddingOrder.getBiddingStatus())
                && !BiddingOrderStatusEnum.WAIT_SELECTED.getKey().equals(tBiddingOrder.getBiddingStatus()))
                || BiddingOrderIfCancelEnum.YES.getKey().equals(tBiddingOrder.getIfCancel())) {
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_STATUS_ERROR);
        }
        if (!QuoteStatusEnum.UNSELECTED.getKey().equals(tBiddingOrderQuote.getQuoteStatus())){
            throw new BizException(CarrierDataExceptionEnum.ORDER_QUOTE_STATUS_ERROR);
        }
        TBiddingOrderQuote updateOrderQuote = new TBiddingOrderQuote();
        updateOrderQuote.setId(tBiddingOrderQuote.getId());
        updateOrderQuote.setQuotePriceType(requestModel.getBiddingPriceType());
        updateOrderQuote.setQuotePrice(requestModel.getBiddingPrice());
        updateOrderQuote.setVehicleLengthId(requestModel.getVehicleLengthId());
        updateOrderQuote.setVehicleLength(requestModel.getVehicleLength());
        updateOrderQuote.setQuoteOperator(BaseContextHandler.getUserName());
        updateOrderQuote.setQuoteTime(new Date());
        commonBiz.setBaseEntityModify(updateOrderQuote, BaseContextHandler.getUserName());
        biddingOrderQuoteMapper.updateByPrimaryKeySelective(updateOrderQuote);
        BigDecimal remarkPrice = tBiddingOrderQuote.getQuotePrice();
        if (PriceTypeEnum.UNIT_PRICE.getKey().equals(tBiddingOrderQuote.getQuotePriceType())){
            // 报价单需求单关联
            List<TBiddingOrderDemand> tBiddingOrderDemands =
                    tBiddingOrderDemandMapper.selectByBiddingOrderId(tBiddingOrderQuote.getBiddingOrderId());
            List<Long> demandIds =
                    tBiddingOrderDemands.stream().map(TBiddingOrderDemand::getDemandOrderId).collect(Collectors.toList());
            List<TDemandOrder> byIds = tDemandOrderMapper.getByIds(StringUtils.listToString(demandIds, ','));
            BigDecimal goodsAmount = BigDecimal.ZERO;
            for (TDemandOrder tDemandOrder : byIds) {
                goodsAmount = goodsAmount.add(tDemandOrder.getGoodsAmount());
            }
            remarkPrice = remarkPrice.multiply(goodsAmount);
        }


        // 修改报价日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(
                tBiddingOrder.getId(),
                OperateLogsOperateTypeEnum.MODIFY_QUOTE,
                "修改前报价为：车长："+tBiddingOrderQuote.getVehicleLength().stripTrailingZeros()+"-总价："+remarkPrice.stripTrailingZeros().toPlainString(),
                BaseContextHandler.getUserName());

        // 新增修改报价日志
        tOperateLogsMapper.insertSelective(tOperateLogs);
    }


    /**
     * 选择车主报价 v3.20.0
     * @param requestModel 请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void confirmQuote(ConfirmQuoteRequestModel requestModel){
        // 报价单
        TBiddingOrderQuote tBiddingOrderQuote = biddingOrderQuoteMapper.selectByPrimaryKey(requestModel.getBiddingOrderQuoteId());
        if(tBiddingOrderQuote == null || IfValidEnum.INVALID.getKey().equals(tBiddingOrderQuote.getValid())){
            throw new BizException(CarrierDataExceptionEnum.ORDER_QUOTE_NOT_EXIST);
        }
        if (!QuoteStatusEnum.UNSELECTED.getKey().equals(tBiddingOrderQuote.getQuoteStatus())){
            throw new BizException(CarrierDataExceptionEnum.ORDER_QUOTE_STATUS_ERROR);
        }
        // 竞价单
        TBiddingOrder tBiddingOrder = tBiddingOrderMapper.selectByPrimaryKey(tBiddingOrderQuote.getBiddingOrderId());
        if (tBiddingOrder == null || IfValidEnum.INVALID.getKey().equals(tBiddingOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
        }
        if ((!BiddingOrderStatusEnum.WAIT_SELECTED.getKey().equals(tBiddingOrder.getBiddingStatus()) &&
                !BiddingOrderStatusEnum.IN_QUOTATION.getKey().equals(tBiddingOrder.getBiddingStatus()))
                || BiddingOrderIfCancelEnum.YES.getKey().equals(tBiddingOrder.getIfCancel())) {
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_STATUS_ERROR);
        }
        List<TCompanyCarrier> companyCarriers = tCompanyCarrierMapper.getByIds(String.valueOf(tBiddingOrderQuote.getCompanyCarrierId()));
        if(ListUtils.isEmpty(companyCarriers)){
            throw new BizException(CarrierDataExceptionEnum.NO_COMPANY_CARRIER);
        }
        // 报价单需求单关联
        List<TBiddingOrderDemand> tBiddingOrderDemands =
                tBiddingOrderDemandMapper.selectByBiddingOrderId(tBiddingOrderQuote.getBiddingOrderId());
        List<Long> demandIds =
                tBiddingOrderDemands.stream().map(TBiddingOrderDemand::getDemandOrderId).collect(Collectors.toList());
        List<TDemandOrder> byIds = tDemandOrderMapper.getByIds(StringUtils.listToString(demandIds, ','));
        BigDecimal goodsAmount = BigDecimal.ZERO;
        for (TDemandOrder tDemandOrder : byIds) {
            goodsAmount = goodsAmount.add(tDemandOrder.getGoodsAmount());
        }
        List<TBiddingOrderDemand> updateOrderDemandList = new ArrayList<>();
        for(TBiddingOrderDemand tBiddingOrderDemand:tBiddingOrderDemands){
            TBiddingOrderDemand updateOrderDemand = new TBiddingOrderDemand();
            updateOrderDemand.setDemandOrderId(tBiddingOrderDemand.getDemandOrderId());
            updateOrderDemand.setId(tBiddingOrderDemand.getId());
            updateOrderDemand.setBiddingPriceType(requestModel.getBiddingPriceType());
            if (PriceTypeEnum.UNIT_PRICE.getKey().equals(requestModel.getBiddingPriceType())
                    && PriceTypeEnum.FIXED_PRICE.getKey().equals(tBiddingOrderQuote.getQuotePriceType())) {
                updateOrderDemand.setBiddingPrice(tBiddingOrderQuote.getQuotePrice().divide(goodsAmount, RoundingMode.HALF_UP));
            }
            if (PriceTypeEnum.UNIT_PRICE.getKey().equals(requestModel.getBiddingPriceType())
                    && PriceTypeEnum.UNIT_PRICE.getKey().equals(tBiddingOrderQuote.getQuotePriceType())) {
                updateOrderDemand.setBiddingPrice(tBiddingOrderQuote.getQuotePrice());
            }
            if (PriceTypeEnum.FIXED_PRICE.getKey().equals(requestModel.getBiddingPriceType())
                    && PriceTypeEnum.UNIT_PRICE.getKey().equals(tBiddingOrderQuote.getQuotePriceType())) {
                updateOrderDemand.setBiddingPrice(tBiddingOrderQuote.getQuotePrice().multiply(goodsAmount).divide(
                        new BigDecimal(tBiddingOrderDemands.size()), RoundingMode.HALF_UP));
            }
            if (PriceTypeEnum.FIXED_PRICE.getKey().equals(requestModel.getBiddingPriceType())
                    && PriceTypeEnum.FIXED_PRICE.getKey().equals(tBiddingOrderQuote.getQuotePriceType())) {
                updateOrderDemand.setBiddingPrice(tBiddingOrderQuote.getQuotePrice().divide(
                        new BigDecimal(tBiddingOrderDemands.size()), RoundingMode.HALF_UP));
            }
            commonBiz.setBaseEntityModify(updateOrderDemand, BaseContextHandler.getUserName());
            updateOrderDemandList.add(updateOrderDemand);
        }
        // 批量更新报价单需求单关联
        tBiddingOrderDemandMapper.batchUpdateSelective(updateOrderDemandList);

        // 更新报价单状态
        TBiddingOrderQuote updateBiddingOrderQuote = new TBiddingOrderQuote();
        updateBiddingOrderQuote.setId(tBiddingOrderQuote.getId());
        updateBiddingOrderQuote.setQuoteStatus(QuoteStatusEnum.SELECTED.getKey());
        commonBiz.setBaseEntityModify(updateBiddingOrderQuote, BaseContextHandler.getUserName());
        biddingOrderQuoteMapper.updateByPrimaryKeySelective(updateBiddingOrderQuote);

        // 更新报价单状态
        TBiddingOrder updateBiddingOrder = new TBiddingOrder();
        updateBiddingOrder.setId(tBiddingOrder.getId());
        updateBiddingOrder.setBiddingStatus(BiddingOrderStatusEnum.COMPLETE_QUOTATION.getKey());
        updateBiddingOrder.setQuoteEndTime(new Date());
        commonBiz.setBaseEntityModify(updateBiddingOrder, BaseContextHandler.getUserName());
        tBiddingOrderMapper.updateByPrimaryKeySelective(updateBiddingOrder);

        // 发布需求单至待调度
        List<BiddingOrderSelectCarrierDemandModel> demandModelList = updateOrderDemandList.stream().map(e -> {
            BiddingOrderSelectCarrierDemandModel demandModel = new BiddingOrderSelectCarrierDemandModel();
            demandModel.setDemandOrderId(e.getDemandOrderId());
            demandModel.setBiddingPrice(e.getBiddingPrice());
            demandModel.setBiddingPriceType(e.getBiddingPriceType());
            return demandModel;
        }).collect(Collectors.toList());
        BiddingOrderSelectCarrierModel biddingOrderSelectCarrierModel = new BiddingOrderSelectCarrierModel();
        biddingOrderSelectCarrierModel.setDemandOrderList(demandModelList);
        biddingOrderSelectCarrierModel.setCompanyCarrierId(tBiddingOrderQuote.getCompanyCarrierId());
        biddingOrderSelectCarrierModel.setIsOurCompany(companyCarriers.get(CommonConstant.INTEGER_ZERO).getLevel());
        biddingOrderSelectCarrierModel.setVehicleLengthId(tBiddingOrderQuote.getVehicleLengthId());
        biddingOrderSelectCarrierModel.setVehicleLength(tBiddingOrderQuote.getVehicleLength());
        demandOrderForLeYiBiz.biddingOrderSelectCarrier(biddingOrderSelectCarrierModel);

        //新增选择车主日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(
                tBiddingOrder.getId(),
                OperateLogsOperateTypeEnum.CHOOSE_CARRIER,
                requestModel.getRemark(),
                BaseContextHandler.getUserName());
        tOperateLogsMapper.insertSelective(tOperateLogs);

        //删除原竞价队列
        BiddingOrderDelayMsg biddingOrderDelayMsg = new BiddingOrderDelayMsg();
        biddingOrderDelayMsg.setBiddingOrderId(tBiddingOrder.getId());
        commonBiz.removeDelayQueue(DelayQueueBizTypeEnum.BIDDING_ORDER.name(),biddingOrderDelayMsg);


        //给车主发消息弹窗
        MessageNoticeEnum messageNoticeEnum = MessageNoticeEnum.BIDDING_ORDER_SELECT_CARRIER;
        //获取消息体文本
        ConfigKeyEnum configKeyEnum = messageNoticeEnum.getConfigKeyEnum();
        String socketTemplateMessage = sysConfigBiz.getSysConfig(configKeyEnum.getGroupCode(), configKeyEnum.getValue()).orElse(null);
        if (StringUtils.isNotBlank(socketTemplateMessage)){
            String messageBody = MessageFormat.format(socketTemplateMessage, tBiddingOrder.getBiddingOrderCode());
            //组装消息
            AddMessageNoticeModel addMessageNoticeModel = new AddMessageNoticeModel();
            addMessageNoticeModel.setMessageNoticeEnum(messageNoticeEnum);
            addMessageNoticeModel.setObjectId(tBiddingOrder.getId());
            addMessageNoticeModel.setObjectCode(tBiddingOrder.getBiddingOrderCode());
            addMessageNoticeModel.setMessageBody(messageBody);
            addMessageNoticeModel.setMessageReceiverList(List.of(tBiddingOrderQuote.getCompanyCarrierId()));
            addMessageNoticeModel.setMessagePusher(BaseContextHandler.getUserName());
            AsyncProcessQueue.execute(() -> messageNoticeCommonBiz.addMessageNotice(addMessageNoticeModel));
        }
    }


    /**
     * 重新报价
     * @param requestModel 请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void rebiddingQuote(RebiddingRequestModel requestModel){
        TBiddingOrder tBiddingOrder = tBiddingOrderMapper.selectByPrimaryKey(requestModel.getBiddingOrderId());
        if (tBiddingOrder == null || IfValidEnum.INVALID.getKey().equals(tBiddingOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
        }
        if (!BiddingOrderStatusEnum.SUSPEND_QUOTATION.getKey().equals(tBiddingOrder.getBiddingStatus())
             || BiddingOrderIfCancelEnum.YES.getKey().equals(tBiddingOrder.getIfCancel())) {
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_STATUS_ERROR);
        }
        long demandCount = tBiddingOrderDemandMapper.selectCountByBiddingOrderId(requestModel.getBiddingOrderId());
        if (demandCount < CommonConstant.LONG_ONE) {
            throw new BizException(CarrierDataExceptionEnum.BINDING_ORDER_CANNOT_OPERATE);
        }
        // 清空所有车主报价
        biddingOrderQuoteMapper.emptyOrderQuote(requestModel.getBiddingOrderId());
        // 更改报价单状态和开始时间
        TBiddingOrder updateBiddingOrder = new TBiddingOrder();
        updateBiddingOrder.setId(tBiddingOrder.getId());
        updateBiddingOrder.setQuoteStartTime(new Date());
        updateBiddingOrder.setBiddingStatus(BiddingOrderStatusEnum.IN_QUOTATION.getKey());
        commonBiz.setBaseEntityModify(updateBiddingOrder, BaseContextHandler.getUserName());
        tBiddingOrderMapper.updateByPrimaryKeySelective(updateBiddingOrder);
        // 重新报价日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(
                tBiddingOrder.getId(),
                OperateLogsOperateTypeEnum.REBIDDING_QUOTE,
                null,
                BaseContextHandler.getUserName());

        //新增重新报价日志
        tOperateLogsMapper.insertSelective(tOperateLogs);
        BiddingOrderDelayMsg biddingOrderDelayMsg = new BiddingOrderDelayMsg();
        biddingOrderDelayMsg.setBiddingOrderId(tBiddingOrder.getId());
        //删除原竞价队列
        commonBiz.removeDelayQueue(DelayQueueBizTypeEnum.BIDDING_ORDER.name(),biddingOrderDelayMsg);

        //将竞价单添加到延迟队列
        commonBiz.addDelayQueue(DelayQueueBizTypeEnum.BIDDING_ORDER.name(), biddingOrderDelayMsg, BiddingOrderQuoteDurationEnum.getEnum(tBiddingOrder.getQuoteDuration()).getValue(), TimeUnit.MINUTES);
    }



    /**
     * 新增需求单
     * @param requestModel 请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void addDemand(AddDemandRequestModel requestModel){
        TBiddingOrder tBiddingOrder = tBiddingOrderMapper.selectByPrimaryKey(requestModel.getBiddingOrderId());
        if (tBiddingOrder == null || IfValidEnum.INVALID.getKey().equals(tBiddingOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
        }
        if (!BiddingOrderStatusEnum.SUSPEND_QUOTATION.getKey().equals(tBiddingOrder.getBiddingStatus())
                || BiddingOrderIfCancelEnum.YES.getKey().equals(tBiddingOrder.getIfCancel())) {
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_STATUS_ERROR);
        }
        TBiddingOrderDemand tBiddingOrderDemand =
                tBiddingOrderDemandMapper.selectByBiddingOrderIdAndDemandId(
                        requestModel.getBiddingOrderId(), requestModel.getDemandOrderId());
        if(tBiddingOrderDemand!=null){
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_DEMAND_HAS_EXIST);
        }
        TDemandOrder demandOrder = tDemandOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getDemandOrderId());
        if (demandOrder == null || IfValidEnum.INVALID.getKey().equals(demandOrder.getValid())
                || !DemandOrderStatusEnum.WAIT_PUBLISH.getKey().equals(demandOrder.getEntrustStatus())
                || DemandOrderIfCancelEnum.YES.getKey().equals(demandOrder.getIfCancel())) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }

        //新增竞价单-需求单关系
        TBiddingOrderDemand insertBindingOrderDemand = new TBiddingOrderDemand();
        insertBindingOrderDemand.setDemandOrderId(requestModel.getDemandOrderId());
        insertBindingOrderDemand.setBiddingOrderId(requestModel.getBiddingOrderId());
        commonBiz.setBaseEntityAdd(insertBindingOrderDemand, BaseContextHandler.getUserName());
        tBiddingOrderDemandMapper.insertSelective(insertBindingOrderDemand);

        //更新需求单状态
        TDemandOrder updateDemandOrder = new TDemandOrder();
        updateDemandOrder.setId(demandOrder.getId());
        updateDemandOrder.setStatus(DemandOrderStatusEnum.BIDDING.getKey());
        updateDemandOrder.setEntrustStatus(DemandOrderStatusEnum.BIDDING.getKey());
        updateDemandOrder.setStatusUpdateTime(new Date());
        updateDemandOrder.setOrderMode(OrderModeEnum.BIDDING_PRICE.getKey());
        commonBiz.setBaseEntityModify(updateDemandOrder, BaseContextHandler.getUserName());
        tDemandOrderMapper.updateByPrimaryKeySelective(updateDemandOrder);

        //竞价单添加需求单日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(
                tBiddingOrder.getId(),
                OperateLogsOperateTypeEnum.BIDDING_ORDER_DEMAND_ADD,
                "新增需求单:"+demandOrder.getDemandOrderCode(),
                BaseContextHandler.getUserName());

        //新增竞价单日志
        tOperateLogsMapper.insertSelective(tOperateLogs);
    }

    /**
     * 删除需求单
     * @param requestModel 请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void delDemand(DelDemandRequestModel requestModel){
        TBiddingOrder tBiddingOrder = tBiddingOrderMapper.selectByPrimaryKey(requestModel.getBiddingOrderId());
        if (tBiddingOrder == null || IfValidEnum.INVALID.getKey().equals(tBiddingOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
        }
        if (!BiddingOrderStatusEnum.SUSPEND_QUOTATION.getKey().equals(tBiddingOrder.getBiddingStatus())
                || BiddingOrderIfCancelEnum.YES.getKey().equals(tBiddingOrder.getIfCancel())) {
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_STATUS_ERROR);
        }
        TBiddingOrderDemand tBiddingOrderDemand =
                tBiddingOrderDemandMapper.selectByBiddingOrderIdAndDemandId(
                        requestModel.getBiddingOrderId(), requestModel.getDemandOrderId());
        if(tBiddingOrderDemand == null ||IfValidEnum.INVALID.getKey().equals(tBiddingOrderDemand.getValid())){
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_DEMAND_NOT_EXIST);
        }
        long demandCount = tBiddingOrderDemandMapper.selectCountByBiddingOrderId(requestModel.getBiddingOrderId());
        if (demandCount < CommonConstant.LONG_TWO) {
            throw new BizException(CarrierDataExceptionEnum.LEAST_ONE_DEMAND);
        }
        TDemandOrder demandOrder = tDemandOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getDemandOrderId());
        if (demandOrder == null || IfValidEnum.INVALID.getKey().equals(demandOrder.getValid())
                || !DemandOrderStatusEnum.BIDDING.getKey().equals(demandOrder.getEntrustStatus())) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }

        //删除竞价单-需求单关系
        TBiddingOrderDemand updateBiddingOrderDemand = new TBiddingOrderDemand();
        updateBiddingOrderDemand.setId(tBiddingOrderDemand.getId());
        updateBiddingOrderDemand.setValid(IfValidEnum.INVALID.getKey());
        commonBiz.setBaseEntityModify(updateBiddingOrderDemand, BaseContextHandler.getUserName());
        tBiddingOrderDemandMapper.updateByPrimaryKeySelective(updateBiddingOrderDemand);

        //更新需求单状态
        TDemandOrder updateDemandOrder = new TDemandOrder();
        updateDemandOrder.setId(demandOrder.getId());
        updateDemandOrder.setStatus(DemandOrderStatusEnum.WAIT_PUBLISH.getKey());
        updateDemandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_PUBLISH.getKey());
        updateDemandOrder.setStatusUpdateTime(new Date());
        updateDemandOrder.setOrderMode(OrderModeEnum.DEFAULT.getKey());
        commonBiz.setBaseEntityModify(updateDemandOrder, BaseContextHandler.getUserName());
        tDemandOrderMapper.updateByPrimaryKeySelective(updateDemandOrder);
    }

    /**
     * 前台展示 详情
     * @param requestModel
     * @return {@link BiddingOrderDetailResponseModel}
     */
    public BiddingOrderDetailResponseModel biddingOrderDetailByCustomer(BiddingOrderDetailRequestModel requestModel) {
        Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(companyCarrierId);
        if (tCompanyCarrier == null || IfValidEnum.INVALID.getKey().equals(tCompanyCarrier.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
        }
        //获取竞价单
        TBiddingOrder tBiddingOrder = tBiddingOrderMapper.selectByPrimaryKey(requestModel.getBiddingOrderId());
        if(null==tBiddingOrder||IfValidEnum.INVALID.getKey().equals(tBiddingOrder.getValid())){
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
        }

        //获取车主报价单
        SelectBiddingOrderQuoteByConditionBo selectBiddingOrderQuoteByConditionBo=new SelectBiddingOrderQuoteByConditionBo();
        selectBiddingOrderQuoteByConditionBo.setCompanyCarrierId(companyCarrierId);
        selectBiddingOrderQuoteByConditionBo.setBiddingOrderId(tBiddingOrder.getId());
        List<TBiddingOrderQuote> biddingOrderQuotes = biddingOrderQuoteMapper.selectBiddingOrderQuoteByCondition(selectBiddingOrderQuoteByConditionBo);
        TBiddingOrderQuote tBiddingOrderQuote=new TBiddingOrderQuote();
        if(biddingOrderQuotes.size()>1){
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_BIND_ORDER_QUOTE_EXIST_MANY);
        }else if(biddingOrderQuotes.size()==1){
            tBiddingOrderQuote= biddingOrderQuotes.get(0);
        }

        //竞价单车主范围是全部
        if (CompanyCarrierRangeEnum.ALL.getKey().equals(tBiddingOrder.getCompanyCarrierRange())){
            //如果车主加入了黑名单，但车主未报价，则不能看到此单
            if (CommonConstant.INTEGER_ONE.equals(tCompanyCarrier.getIfAddBlacklist()) && tBiddingOrderQuote.getId() == null){
                throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
            }
        }
        //竞价单车主范围是定向选择
        else if (CompanyCarrierRangeEnum.DIRECTIONAL.getKey().equals(tBiddingOrder.getCompanyCarrierRange())){
            //判断单子是否属于车主
            List<TBiddingOrderCompany> tBiddingOrderCompanyList = tBiddingOrderCompanyMapper.getByCompanyCarrierIdBiddingOrderId(companyCarrierId, tBiddingOrder.getId());
            if (ListUtils.isEmpty(tBiddingOrderCompanyList)){
                throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
            }
        }

        //查询关联需求单信息
        SelectDemandByConditionReqBo selectDemandByConditionReqBo=new SelectDemandByConditionReqBo();
        selectDemandByConditionReqBo.setBiddingOrderId(tBiddingOrder.getId());
        List<DemandInfoBo> demandInfoBoList= tBiddingOrderDemandMapper.selectDemandByCondition(selectDemandByConditionReqBo);
        List<BiddingDemandModel> biddingDemandModels = MapperUtils.mapper(demandInfoBoList, BiddingDemandModel.class);
        BigDecimal sumCount = biddingDemandModels.stream().map(BiddingDemandModel::getGoodsCount).reduce(BigDecimal.ZERO, BigDecimal::add);

        //组装返参
        BiddingOrderDetailResponseModel responseModel=MapperUtils.mapper(tBiddingOrder,BiddingOrderDetailResponseModel.class);
        responseModel.setBiddingOrderId(tBiddingOrder.getId());
        if (tBiddingOrder.getVehicleLengthId() > CommonConstant.LONG_ZERO) {
            responseModel.setVehicleLength(tBiddingOrder.getVehicleLength());
        }else{
            responseModel.setVehicleLength(null);
        }
        responseModel.setDemandDtoList(biddingDemandModels);

        //赋值前台车主报价记录
        if (tBiddingOrderQuote.getId()==null){
            responseModel.setBiddingOrderQuoteList(new ArrayList<>());
        }else {
            BiddingOrderQuoteListResponseModel quoteListResponseModel = MapperUtils.mapper(tBiddingOrderQuote, BiddingOrderQuoteListResponseModel.class);
            quoteListResponseModel.setBiddingOrderQuoteId(tBiddingOrderQuote.getId());
            quoteListResponseModel.setCarrierContactName(tBiddingOrderQuote.getQuoteOperator());
            quoteListResponseModel.setQuotePrice(this.getTotalQuotePrice(tBiddingOrderQuote.getQuotePriceType(), tBiddingOrderQuote.getQuotePrice(), sumCount));
            responseModel.setBiddingOrderQuoteList(List.of(quoteListResponseModel));
        }
        //赋值前台状态
        BiddingOrderStatusEnum biddingOrderStatusEnumByCustomer = getBiddingOrderStatusEnumByCustomer(tBiddingOrder.getBiddingStatus(), tBiddingOrder.getIfCancel(), tBiddingOrderQuote.getQuoteStatus());
        responseModel.setBiddingStatus(biddingOrderStatusEnumByCustomer.getKey());
        Long countDownTime = this.getCountDownTime(tBiddingOrder.getQuoteStartTime(), tBiddingOrder.getQuoteDuration());
        responseModel.setCountDownTime(BiddingOrderStatusEnum.IN_QUOTATION.equals(biddingOrderStatusEnumByCustomer)?countDownTime:0L);
        responseModel.setBiddingStatusLabel(biddingOrderStatusEnumByCustomer.getValue());
        responseModel.setHandlingMode(biddingDemandModels.size()>1?HandlingModeEnum.ONE_MORE_LOAD.getKey():HandlingModeEnum.LOADING_AND_UNLOADING.getKey());
        responseModel.setHandlingModeLabel(HandlingModeEnum.getEnum(responseModel.getHandlingMode()).getValue());
        return responseModel;
    }

    /**
     * 获取车主信息
     *
     * @return {@link String}
     */
    private String getCarrierContactName(TBiddingOrderQuote tBiddingOrderQuote) {
        if (null == tBiddingOrderQuote) {
            return "";
        }
        return CompanyTypeEnum.COMPANY.getKey().equals(tBiddingOrderQuote.getCompanyCarrierType()) ?
                tBiddingOrderQuote.getCompanyCarrierName() : String.format("%s%s", tBiddingOrderQuote.getCarrierContactName(), tBiddingOrderQuote.getCarrierContactPhone());

    }

    /**
     * 获取倒计时秒数
     *
     * @param quoteStartTime
     * @param quoteDuration
     * @return {@link Integer}
     */
    private Long getCountDownTime(Date quoteStartTime,Integer quoteDuration){
        if(null==quoteStartTime || null==quoteDuration){
            return 0L;
        }
        DateTime endTime = DateUtil.offset(quoteStartTime, DateField.MINUTE
                , BiddingOrderQuoteDurationEnum.getEnum(quoteDuration).getValue());
        Date now = new Date();
        return endTime.isAfter(now)?endTime.between(now, DateUnit.SECOND):0L;
    }

    /**
     * 后台详情展示
     * @param requestModel
     * @return {@link BiddingOrderDetailByManagerResponseModel}
     */
    public BiddingOrderDetailByManagerResponseModel biddingOrderDetailByManager(BiddingOrderDetailRequestModel requestModel) {
        //获取竞价单
        TBiddingOrder tBiddingOrder = tBiddingOrderMapper.selectByPrimaryKey(requestModel.getBiddingOrderId());
        if(null==tBiddingOrder||IfValidEnum.INVALID.getKey().equals(tBiddingOrder.getValid())){
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
        }
        BiddingOrderDetailByManagerResponseModel responseModel=MapperUtils.mapper(tBiddingOrder,BiddingOrderDetailByManagerResponseModel.class);
        responseModel.setBiddingOrderId(tBiddingOrder.getId());
        if (tBiddingOrder.getVehicleLengthId() > CommonConstant.LONG_ZERO) {
            responseModel.setVehicleLength(tBiddingOrder.getVehicleLength());
        }else{
            responseModel.setVehicleLength(null);
        }
        //查询关联需求单信息
        SelectDemandByConditionReqBo selectDemandByConditionReqBo=new SelectDemandByConditionReqBo();
        selectDemandByConditionReqBo.setBiddingOrderId(tBiddingOrder.getId());
        List<DemandInfoBo> demandInfoBoList= tBiddingOrderDemandMapper.selectDemandByCondition(selectDemandByConditionReqBo);
        List<BiddingDemandModel> biddingDemandModels = MapperUtils.mapper(demandInfoBoList, BiddingDemandModel.class);
        BigDecimal sumCount = biddingDemandModels.stream().map(BiddingDemandModel::getGoodsCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        responseModel.setDemandDtoList(biddingDemandModels);

        //获取车主报价单,后台过滤掉车主自己取消的记录
        SelectBiddingOrderQuoteByConditionBo selectBiddingOrderQuoteByConditionBo=new SelectBiddingOrderQuoteByConditionBo();
        selectBiddingOrderQuoteByConditionBo.setBiddingOrderId(tBiddingOrder.getId());
        List<TBiddingOrderQuote> biddingOrderQuotes = biddingOrderQuoteMapper.selectBiddingOrderQuoteByCondition(selectBiddingOrderQuoteByConditionBo);

        //赋值前台车主报价记录
        List<BiddingOrderQuoteListResponseModel> biddingOrderQuoteList=new ArrayList<>();
        for (TBiddingOrderQuote biddingOrderQuote : biddingOrderQuotes) {
            BiddingOrderQuoteListResponseModel quoteListResponseModel = MapperUtils.mapper(biddingOrderQuote, BiddingOrderQuoteListResponseModel.class);
            quoteListResponseModel.setBiddingOrderQuoteId(biddingOrderQuote.getId());
            quoteListResponseModel.setQuoteStatusLabel(QuoteStatusEnum.getEnum(biddingOrderQuote.getQuoteStatus()).getValue());
            quoteListResponseModel.setCarrierContactName(this.getCarrierContactName(biddingOrderQuote));
            quoteListResponseModel.setQuotePrice(this.getTotalQuotePrice(biddingOrderQuote.getQuotePriceType(),biddingOrderQuote.getQuotePrice(),sumCount));
            biddingOrderQuoteList.add(quoteListResponseModel);
        }
        responseModel.setBiddingOrderQuoteList(biddingOrderQuoteList);
        //赋值状态
        BiddingOrderStatusEnum biddingOrderStatusEnum = getBiddingOrderStatusEnum(tBiddingOrder.getBiddingStatus(), tBiddingOrder.getIfCancel());
        responseModel.setBiddingStatus(biddingOrderStatusEnum.getKey());
        responseModel.setBiddingStatusLabel(biddingOrderStatusEnum.getValue());
        Long countDownTime = this.getCountDownTime(tBiddingOrder.getQuoteStartTime(), tBiddingOrder.getQuoteDuration());
        responseModel.setCountDownTime(BiddingOrderStatusEnum.IN_QUOTATION.equals(biddingOrderStatusEnum)?countDownTime:0L);

        //装卸方式
        responseModel.setHandlingMode(biddingDemandModels.size()>1?HandlingModeEnum.ONE_MORE_LOAD.getKey():HandlingModeEnum.LOADING_AND_UNLOADING.getKey());
        responseModel.setHandlingModeLabel(HandlingModeEnum.getEnum(responseModel.getHandlingMode()).getValue());
        //操作日志
        List<ViewLogResponseModel> viewLogResponseModels = tOperateLogsMapper
                .selectLogsByCondition(OperateLogsObjectTypeEnum.BIDDING_ORDER.getKey(), tBiddingOrder.getId(), null);
        responseModel.setOperateLogList(MapperUtils.mapper(viewLogResponseModels,OperateLogListResponseModel.class));
        return responseModel;
    }

    /**
     * 获取竞价价格
     * @param quotePriceType
     * @param quotePrice
     * @param sumCount
     * @return {@link BigDecimal}
     */
    private BigDecimal getTotalQuotePrice(Integer quotePriceType,BigDecimal quotePrice,BigDecimal sumCount){
        if(quotePriceType==null||quotePrice==null||sumCount==null){
            return null;
        }
        if(PriceTypeEnum.FIXED_PRICE.getKey().equals(quotePriceType)){
            return quotePrice;
        }
        return quotePrice.multiply(sumCount).setScale(2);
    }

    /**
     * 前台报价
     *
     * @param requestModel
     */
    @Transactional
    public void quote(QuoteRequestModel requestModel) {
        Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(companyCarrierId);
        if (tCompanyCarrier == null || IfValidEnum.INVALID.getKey().equals(tCompanyCarrier.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
        }
        //获取竞价单
        TBiddingOrder tBiddingOrder = tBiddingOrderMapper.selectByPrimaryKey(requestModel.getBiddingOrderId());
        if (null == tBiddingOrder || IfValidEnum.INVALID.getKey().equals(tBiddingOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
        }

        //查询是否竞价过
        SelectBiddingOrderQuoteByConditionBo selectBiddingOrderQuoteByConditionBo = new SelectBiddingOrderQuoteByConditionBo();
        selectBiddingOrderQuoteByConditionBo.setBiddingOrderId(tBiddingOrder.getId());
        selectBiddingOrderQuoteByConditionBo.setCompanyCarrierId(companyCarrierId);
        List<TBiddingOrderQuote> biddingOrderQuotes = biddingOrderQuoteMapper.selectBiddingOrderQuoteByCondition(selectBiddingOrderQuoteByConditionBo);
        TBiddingOrderQuote tBiddingOrderQuote = new TBiddingOrderQuote();
        if (biddingOrderQuotes.size() > 1) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_BIND_ORDER_QUOTE_EXIST_MANY);
        } else if (biddingOrderQuotes.size() == 1) {
            tBiddingOrderQuote = biddingOrderQuotes.get(0);
        }

        //竞价单车主范围是全部
        if (CompanyCarrierRangeEnum.ALL.getKey().equals(tBiddingOrder.getCompanyCarrierRange())){
            //如果车主加入了黑名单，但车主未报价，则不能看到此单
            if (CommonConstant.INTEGER_ONE.equals(tCompanyCarrier.getIfAddBlacklist()) && tBiddingOrderQuote.getId() == null){
                throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
            }
        }
        //竞价单车主范围是定向选择
        else if (CompanyCarrierRangeEnum.DIRECTIONAL.getKey().equals(tBiddingOrder.getCompanyCarrierRange())){
            //判断单子是否属于车主
            List<TBiddingOrderCompany> tBiddingOrderCompanyList = tBiddingOrderCompanyMapper.getByCompanyCarrierIdBiddingOrderId(companyCarrierId, tBiddingOrder.getId());
            if (ListUtils.isEmpty(tBiddingOrderCompanyList)){
                throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
            }
        }

        //非【报价中】状态不可操作
        BiddingOrderStatusEnum biddingOrderStatusEnum = getBiddingOrderStatusEnumByCustomer(tBiddingOrder.getBiddingStatus()
                , tBiddingOrder.getIfCancel(), tBiddingOrderQuote.getQuoteStatus());
        if (!BiddingOrderStatusEnum.IN_QUOTATION.equals(biddingOrderStatusEnum)) {
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_STATUS_ERROR);
        }

        TBiddingOrderQuote biddingOrderQuote = MapperUtils.mapper(requestModel,TBiddingOrderQuote.class);
        //新增赋值
        if(biddingOrderQuotes.isEmpty()){
            biddingOrderQuote.setBiddingOrderId(requestModel.getBiddingOrderId());
            CompanyCarrierByIdModel selectNoOurCompanyCarrierById = tCompanyCarrierMapper.selectCarrierById(companyCarrierId);
            CompanyCarrierByIdModel companyCarrierByIdModel = Optional.ofNullable(selectNoOurCompanyCarrierById).orElse(new CompanyCarrierByIdModel());
            biddingOrderQuote.setQuoteStatus(QuoteStatusEnum.UNSELECTED.getKey());
            biddingOrderQuote.setCompanyCarrierType(companyCarrierByIdModel.getCompanyCarrierType());
            biddingOrderQuote.setCompanyCarrierId(companyCarrierByIdModel.getCompanyCarrierId());
            biddingOrderQuote.setCompanyCarrierName(companyCarrierByIdModel.getCompanyCarrierName());
            biddingOrderQuote.setCarrierContactId(companyCarrierByIdModel.getCarrierContactId());
            biddingOrderQuote.setCarrierContactName(companyCarrierByIdModel.getCarrierContactName());
            biddingOrderQuote.setCarrierContactPhone(companyCarrierByIdModel.getCarrierContactPhone());
        }
        biddingOrderQuote.setQuoteOperator(BaseContextHandler.getUserName());
        biddingOrderQuote.setQuotePrice(requestModel.getBiddingPrice());
        biddingOrderQuote.setQuotePriceType(requestModel.getBiddingPriceType());
        biddingOrderQuote.setQuoteTime(new Date());
        biddingOrderQuote.setId(tBiddingOrderQuote.getId());
        if (biddingOrderQuotes.isEmpty()) {
            biddingOrderQuoteMapper.insertSelectiveEncrypt(biddingOrderQuote);
        } else {
            biddingOrderQuoteMapper.updateByPrimaryKeySelectiveEncrypt(biddingOrderQuote);
        }


        //给后台发消息弹窗
        MessageNoticeEnum messageNoticeEnum = MessageNoticeEnum.BIDDING_ORDER_CARRIER_QUOTE;
        //获取消息体文本
        ConfigKeyEnum configKeyEnum = messageNoticeEnum.getConfigKeyEnum();
        String socketTemplateMessage = sysConfigBiz.getSysConfig(configKeyEnum.getGroupCode(), configKeyEnum.getValue()).orElse(null);
        if (StringUtils.isNotBlank(socketTemplateMessage)){
            String messageBody = MessageFormat.format(socketTemplateMessage, tBiddingOrder.getBiddingOrderCode());
            //组装消息
            AddMessageNoticeModel addMessageNoticeModel = new AddMessageNoticeModel();
            addMessageNoticeModel.setMessageNoticeEnum(messageNoticeEnum);
            addMessageNoticeModel.setObjectId(tBiddingOrder.getId());
            addMessageNoticeModel.setObjectCode(tBiddingOrder.getBiddingOrderCode());
            addMessageNoticeModel.setMessageBody(messageBody);
            addMessageNoticeModel.setMessagePusher(BaseContextHandler.getUserName());
            AsyncProcessQueue.execute(() -> messageNoticeCommonBiz.addMessageNotice(addMessageNoticeModel));
        }
    }

    /**
     * 前台取消报价
     * @param requestModel
     */
    @Transactional
    public void cancelBidding(CancelBiddingRequestModel requestModel) {
        Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(companyCarrierId);
        if (tCompanyCarrier == null || IfValidEnum.INVALID.getKey().equals(tCompanyCarrier.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
        }
        //获取竞价单
        TBiddingOrder tBiddingOrder = tBiddingOrderMapper.selectByPrimaryKey(requestModel.getBiddingOrderId());
        if (null == tBiddingOrder || IfValidEnum.INVALID.getKey().equals(tBiddingOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
        }

        //查询是否竞价过
        SelectBiddingOrderQuoteByConditionBo selectBiddingOrderQuoteByConditionBo = new SelectBiddingOrderQuoteByConditionBo();
        selectBiddingOrderQuoteByConditionBo.setBiddingOrderId(tBiddingOrder.getId());
        selectBiddingOrderQuoteByConditionBo.setCompanyCarrierId(companyCarrierId);
        List<TBiddingOrderQuote> biddingOrderQuotes = biddingOrderQuoteMapper.selectBiddingOrderQuoteByCondition(selectBiddingOrderQuoteByConditionBo);
        TBiddingOrderQuote tBiddingOrderQuote = new TBiddingOrderQuote();
        if (biddingOrderQuotes.size() > 1) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_BIND_ORDER_QUOTE_EXIST_MANY);
        } else if (biddingOrderQuotes.size() == 1) {
            tBiddingOrderQuote = biddingOrderQuotes.get(0);
        }

        //竞价单车主范围是全部
        if (CompanyCarrierRangeEnum.ALL.getKey().equals(tBiddingOrder.getCompanyCarrierRange())){
            //如果车主加入了黑名单，但车主未报价，则不能看到此单
            if (CommonConstant.INTEGER_ONE.equals(tCompanyCarrier.getIfAddBlacklist()) && tBiddingOrderQuote.getId() == null){
                throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
            }
        }
        //竞价单车主范围是定向选择
        else if (CompanyCarrierRangeEnum.DIRECTIONAL.getKey().equals(tBiddingOrder.getCompanyCarrierRange())){
            //判断单子是否属于车主
            List<TBiddingOrderCompany> tBiddingOrderCompanyList = tBiddingOrderCompanyMapper.getByCompanyCarrierIdBiddingOrderId(companyCarrierId, tBiddingOrder.getId());
            if (ListUtils.isEmpty(tBiddingOrderCompanyList)){
                throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
            }
        }

        //非【报价中】状态不可操作
        BiddingOrderStatusEnum biddingOrderStatusEnum = getBiddingOrderStatusEnumByCustomer(tBiddingOrder.getBiddingStatus()
                , tBiddingOrder.getIfCancel(), tBiddingOrderQuote.getQuoteStatus());
        if (!BiddingOrderStatusEnum.IN_QUOTATION.equals(biddingOrderStatusEnum)) {
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_STATUS_ERROR);
        }

        TBiddingOrderQuote biddingOrderQuote = new TBiddingOrderQuote();
        //新增赋值
        if(biddingOrderQuotes.isEmpty()){
            biddingOrderQuote.setBiddingOrderId(requestModel.getBiddingOrderId());
            CompanyCarrierByIdModel selectNoOurCompanyCarrierById = tCompanyCarrierMapper.selectCarrierById(companyCarrierId);
            CompanyCarrierByIdModel companyCarrierByIdModel = Optional.ofNullable(selectNoOurCompanyCarrierById).orElse(new CompanyCarrierByIdModel());
            biddingOrderQuote.setCompanyCarrierType(companyCarrierByIdModel.getCompanyCarrierType());
            biddingOrderQuote.setCompanyCarrierId(companyCarrierByIdModel.getCompanyCarrierId());
            biddingOrderQuote.setCompanyCarrierName(companyCarrierByIdModel.getCompanyCarrierName());
            biddingOrderQuote.setCarrierContactId(companyCarrierByIdModel.getCarrierContactId());
            biddingOrderQuote.setCarrierContactName(companyCarrierByIdModel.getCarrierContactName());
            biddingOrderQuote.setCarrierContactPhone(companyCarrierByIdModel.getCarrierContactPhone());
        }
        biddingOrderQuote.setQuoteOperator(BaseContextHandler.getUserName());
        biddingOrderQuote.setQuoteStatus(QuoteStatusEnum.CANCELLED.getKey());
        biddingOrderQuote.setQuoteTime(new Date());
        biddingOrderQuote.setId(tBiddingOrderQuote.getId());
        if (biddingOrderQuotes.isEmpty()) {
            biddingOrderQuoteMapper.insertSelectiveEncrypt(biddingOrderQuote);
        } else {
            biddingOrderQuoteMapper.updateByPrimaryKeySelectiveEncrypt(biddingOrderQuote);
        }
    }

    /**
     * 后台暂停报价单
     * @param requestModel
     */
    @Transactional
    public void stopBiddingByManager(StopBiddingRequestModel requestModel) {
        //获取竞价单
        TBiddingOrder tBiddingOrder = tBiddingOrderMapper.selectByPrimaryKey(requestModel.getBiddingOrderId());
        if (null == tBiddingOrder || IfValidEnum.INVALID.getKey().equals(tBiddingOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
        }
        BiddingOrderStatusEnum biddingOrderStatusEnum = getBiddingOrderStatusEnum(tBiddingOrder.getBiddingStatus(), tBiddingOrder.getIfCancel());
        if(!BiddingOrderStatusEnum.IN_QUOTATION.equals(biddingOrderStatusEnum)){
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_STATUS_ERROR);
        }

//        TBiddingOrder updateBiddingOrder=new TBiddingOrder();
//        updateBiddingOrder.setId(tBiddingOrder.getId());
//        updateBiddingOrder.setBiddingStatus();
        //tBiddingOrderMapper.updateByPrimaryKeySelective(updateBiddingOrder);
        this.updateBiddingOrderStateByCas(tBiddingOrder.getId(),BiddingOrderStatusEnum.SUSPEND_QUOTATION.getKey(),List.of(BiddingOrderStatusEnum.IN_QUOTATION.getKey()));

        //竞价单日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(
                tBiddingOrder.getId(),
                OperateLogsOperateTypeEnum.STOP_BIDDING_QUOTE,
                "",
                BaseContextHandler.getUserName());

        //竞价单日志
        tOperateLogsMapper.insertSelective(tOperateLogs);

        BiddingOrderDelayMsg biddingOrderDelayMsg = new BiddingOrderDelayMsg();
        biddingOrderDelayMsg.setBiddingOrderId(tBiddingOrder.getId());
        //删除原竞价队列
        commonBiz.removeDelayQueue(DelayQueueBizTypeEnum.BIDDING_ORDER.name(),biddingOrderDelayMsg);
    }

    /**
     * 取消报价后台
     * @param requestModel
     */
    @Transactional
    public void cancelBiddingByManager(CancelBiddingRequestModel requestModel) {
        //获取竞价单
        TBiddingOrder tBiddingOrder = tBiddingOrderMapper.selectByPrimaryKey(requestModel.getBiddingOrderId());
        if (null == tBiddingOrder || IfValidEnum.INVALID.getKey().equals(tBiddingOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_NOT_EXIST);
        }
        BiddingOrderStatusEnum biddingOrderStatusEnum = getBiddingOrderStatusEnum(tBiddingOrder.getBiddingStatus(), tBiddingOrder.getIfCancel());
        if(!(BiddingOrderStatusEnum.SUSPEND_QUOTATION.equals(biddingOrderStatusEnum)||BiddingOrderStatusEnum.WAIT_SELECTED.equals(biddingOrderStatusEnum))){
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_STATUS_ERROR);
        }
        this.updateBiddingOrderStateByCas(tBiddingOrder.getId(), BiddingOrderStatusEnum.QUOTATION_CANCEL.getKey(),
                List.of(BiddingOrderStatusEnum.SUSPEND_QUOTATION.getKey(), BiddingOrderStatusEnum.WAIT_SELECTED.getKey()));
        //修改竞价单结束时间
        TBiddingOrder updateBiddingOrderEndTime=new TBiddingOrder();
        updateBiddingOrderEndTime.setId(tBiddingOrder.getId());
        updateBiddingOrderEndTime.setQuoteEndTime(new Date());
        tBiddingOrderMapper.updateByPrimaryKeySelective(updateBiddingOrderEndTime);
        //修改需求单状态
        List<TBiddingOrderDemand> tBiddingOrderDemandList = tBiddingOrderDemandMapper.selectByBiddingOrderId(tBiddingOrder.getId());
        List<TDemandOrder> updateTDemandOrderList=new ArrayList<>();
        for (TBiddingOrderDemand tBiddingOrderDemand : tBiddingOrderDemandList) {
            TDemandOrder updateDemandOrder = new TDemandOrder();
            updateDemandOrder.setId(tBiddingOrderDemand.getDemandOrderId());
            updateDemandOrder.setStatus(DemandOrderStatusEnum.WAIT_PUBLISH.getKey());
            updateDemandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_PUBLISH.getKey());
            updateDemandOrder.setStatusUpdateTime(new Date());
            updateDemandOrder.setOrderMode(OrderModeEnum.DEFAULT.getKey());
            updateTDemandOrderList.add(updateDemandOrder);
        }
        if(CollUtil.isNotEmpty(updateTDemandOrderList)){
            tDemandOrderMapper.batchUpdateByPrimaryKeySelective(updateTDemandOrderList);
        }

        //竞价单日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(
                tBiddingOrder.getId(),
                OperateLogsOperateTypeEnum.CANCEL_BIDDING_QUOTE,
                "",
                BaseContextHandler.getUserName());

        //竞价单日志
        tOperateLogsMapper.insertSelective(tOperateLogs);
    }

    /**
     * 处理延迟消息
     * @param biddingOrderDelayMsg
     */
    @Transactional
    public void excDelayMsg(BiddingOrderDelayMsg biddingOrderDelayMsg) {
        BaseContextHandler.setUserName(CommonConstant.TIMING_TASK);
        //获取竞价单
        TBiddingOrder tBiddingOrder = tBiddingOrderMapper.selectByPrimaryKey(biddingOrderDelayMsg.getBiddingOrderId());
        if (null == tBiddingOrder || IfValidEnum.INVALID.getKey().equals(tBiddingOrder.getValid())) {
            log.error("竞价单延迟消息处理失败,竞价单不存在,id:{}",biddingOrderDelayMsg.getBiddingOrderId());
            return;
        }
        //非竞价中单子跳过
        BiddingOrderStatusEnum biddingOrderStatusEnum = getBiddingOrderStatusEnum(tBiddingOrder.getBiddingStatus(), tBiddingOrder.getIfCancel());
        if(!BiddingOrderStatusEnum.IN_QUOTATION.equals(biddingOrderStatusEnum)){
            log.info("竞价单id:{},状态为：{} 非报价中,跳过",biddingOrderDelayMsg.getBiddingOrderId(),biddingOrderStatusEnum.getValue());
            return;
        }
        //查询当前单子报价单
        SelectBiddingOrderQuoteByConditionBo selectBiddingOrderQuoteByConditionBo=new SelectBiddingOrderQuoteByConditionBo();
        selectBiddingOrderQuoteByConditionBo.setBiddingOrderId(tBiddingOrder.getId());
        selectBiddingOrderQuoteByConditionBo.setBiddingStatuses(List.of(QuoteStatusEnum.UNSELECTED.getKey()));
        List<TBiddingOrderQuote> biddingOrderQuotes = biddingOrderQuoteMapper.selectBiddingOrderQuoteByCondition(selectBiddingOrderQuoteByConditionBo);

        //更新竞价单状态
        TBiddingOrder updateBiddingOrder=new TBiddingOrder();
        updateBiddingOrder.setId(tBiddingOrder.getId());
        if(biddingOrderQuotes.isEmpty()){
            updateBiddingOrder.setBiddingStatus(BiddingOrderStatusEnum.QUOTATION_CANCEL.getKey());
        }else{
            updateBiddingOrder.setBiddingStatus(BiddingOrderStatusEnum.WAIT_SELECTED.getKey());
        }
        try {
            this.updateBiddingOrderStateByCas(updateBiddingOrder.getId(),updateBiddingOrder.getBiddingStatus(),List.of(BiddingOrderStatusEnum.IN_QUOTATION.getKey()));
        } catch (BizException e) {
            log.info("竞价单id:{},状态为：{} 非报价中,跳过",biddingOrderDelayMsg.getBiddingOrderId(),biddingOrderStatusEnum.getValue());
            return;
        } catch (Exception e){
            log.error("竞价单延迟消息处理失败id:{}",biddingOrderDelayMsg.getBiddingOrderId(),e);
            return;
        }
        //修改竞价单结束时间
        if(biddingOrderQuotes.isEmpty()){
            TBiddingOrder updateBiddingOrderEndTime=new TBiddingOrder();
            updateBiddingOrderEndTime.setId(tBiddingOrder.getId());
            updateBiddingOrderEndTime.setQuoteEndTime(new Date());
            tBiddingOrderMapper.updateByPrimaryKeySelective(updateBiddingOrderEndTime);
        }
        //更新需求单信息
        if(biddingOrderQuotes.isEmpty()){
            List<TBiddingOrderDemand> tBiddingOrderDemandList = tBiddingOrderDemandMapper.selectByBiddingOrderId(tBiddingOrder.getId());
            List<TDemandOrder> updateTDemandOrderList=new ArrayList<>();
            for (TBiddingOrderDemand tBiddingOrderDemand : tBiddingOrderDemandList) {
                TDemandOrder updateDemandOrder = new TDemandOrder();
                updateDemandOrder.setId(tBiddingOrderDemand.getDemandOrderId());
                updateDemandOrder.setStatus(DemandOrderStatusEnum.WAIT_PUBLISH.getKey());
                updateDemandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_PUBLISH.getKey());
                updateDemandOrder.setStatusUpdateTime(new Date());
                updateDemandOrder.setOrderMode(OrderModeEnum.DEFAULT.getKey());
                updateTDemandOrderList.add(updateDemandOrder);
            }
            if(CollUtil.isNotEmpty(updateTDemandOrderList)){
                tDemandOrderMapper.batchUpdateByPrimaryKeySelective(updateTDemandOrderList);
            }
        }
        //操作记录
        if(biddingOrderQuotes.isEmpty()){
            TOperateLogs tOperateLogs = commonBiz.addOperateLogs(
                    tBiddingOrder.getId(),
                    OperateLogsOperateTypeEnum.CANCEL_BIDDING_QUOTE,
                    "",
                    BaseContextHandler.getUserName());

            //竞价单日志
            tOperateLogsMapper.insertSelective(tOperateLogs);
        }


    }

    /**
     * 更新状态通过状态校验
     *
     * @param biddingOrderId 竞价单id
     * @param targetState 需要更新状态
     * @param sourceState 原始状态
     */
    private void  updateBiddingOrderStateByCas(Long biddingOrderId,Integer targetState,List<Integer> sourceState){
        if(biddingOrderId==null || targetState==null ||sourceState==null){
            return;
        }
        UpdateBiddingOrderStateBo updateBiddingOrderStateBo=new UpdateBiddingOrderStateBo();
        updateBiddingOrderStateBo.setBiddingOrderId(biddingOrderId);
        updateBiddingOrderStateBo.setTargetState(targetState);
        updateBiddingOrderStateBo.setSourceState(sourceState);
        int count= tBiddingOrderMapper.updateBiddingOrderStateByCAS(updateBiddingOrderStateBo);
        if(count!=1){
            throw new BizException(CarrierDataExceptionEnum.BIND_ORDER_STATE_CHANGED);
        }
    }
}
