package com.logistics.management.webapi.controller.region.mapping;

import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.client.region.response.GetCompanyCarrierByRegionResponseModel;
import com.logistics.management.webapi.controller.region.response.GetCompanyCarrierByRegionResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2024/6/5 9:44
 */
public class GetCompanyByRegionMapping extends MapperMapping<GetCompanyCarrierByRegionResponseModel, GetCompanyCarrierByRegionResponseDto> {
    @Override
    public void configure() {
        GetCompanyCarrierByRegionResponseModel source = getSource();
        GetCompanyCarrierByRegionResponseDto destination = getDestination();

        //个人类型 把联系人+手机号当作公司名
        if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyType())) {
            destination.setCompanyName(source.getContactName() + " " + source.getContactPhone());
        }
    }
}
