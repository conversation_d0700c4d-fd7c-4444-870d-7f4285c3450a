package com.logistics.appapi.controller.attendance.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 修改考勤打卡详情响应dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/14
 */
@Data
public class UpdateAttendanceHistoryDetailResponseDto {

	@ApiModelProperty("考勤打卡ID")
	private String attendanceRecordId = "";

	@ApiModelProperty("考勤日期, 今日 or MM月dd日")
	private String attendanceDate = "";

	@ApiModelProperty("上班打卡时间, pattern: yyyy-MM-dd HH:mm:ss")
	private String onDutyPunchDateTime = "";

	@ApiModelProperty("上班打卡时间")
	private String onDutyPunchTime = "";

	@ApiModelProperty("上班打卡地点")
	private String onDutyPunchLocation = "";

	@ApiModelProperty("下班打卡时间, pattern: yyyy-MM-dd HH:mm:ss")
	private String offDutyPunchDateTime = "";

	@ApiModelProperty("下班打卡时间")
	private String offDutyPunchTime = "";

	@ApiModelProperty("下班打卡地点")
	private String offDutyPunchLocation = "";

	@ApiModelProperty("工时")
	private String manHour = "";

	@ApiModelProperty("变更申请ID")
	private String attendanceChangeApplyId = "";

	@ApiModelProperty("要变更的打卡类型 1: 上班 2:下班")
	private String changeType = "";

	@ApiModelProperty("要变更的打卡类型Label")
	private String changeTypeLabel = "";

	@ApiModelProperty("要变更的打卡时间")
	private String changePunchTime = "";

	@ApiModelProperty("变更原因")
	private String changeReason = "";

}
