package com.logistics.tms.controller.dispatch.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class DriverAndVehicleSearchResponseModel {

    @ApiModelProperty("车辆司机关系ID")
    private Long vehicleStaffRelId;
    @ApiModelProperty("司机id")
    private Long driverId;
    @ApiModelProperty("司机名字")
    private String driverName;
    @ApiModelProperty("司机手机号")
    private String driverPhone;
    @ApiModelProperty("司机身份证")
    private String driverIdentityNumber;
    @ApiModelProperty("车辆id")
    private Long vehicleId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆使用性质：1 自有(内部) 2外部")
    private Integer vehicleProperty;

}
