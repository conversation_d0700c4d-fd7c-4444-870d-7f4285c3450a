package com.logistics.management.webapi.api.feign.entrustsettlement.hystrix;

import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.entrustsettlement.EntrustSettlementApi;
import com.logistics.management.webapi.api.feign.entrustsettlement.dto.*;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: wjf
 * @date: 2019/10/11 19:52
 */
@Component("tmsEntrustSettlementApiHystrix")
public class EntrustSettlementApiHystrix implements EntrustSettlementApi {
    @Override
    public Result<EntrustSettlementListResponseDto> entrustSettlementList(EntrustSettlementListRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void exportEntrustSettlementList(EntrustSettlementListRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }

    @Override
    public Result modifyCost(ModifyCostRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<GetSettlementDetailResponseDto> getSettlementDetail(GetSettlementDetailRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<GetDetailResponseDto> getDetail(GetDetailRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result receiveMoney(ReceiveMoneyRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result refund(RefundRequestDto requestDto) {
        return Result.timeout();
    }
}
