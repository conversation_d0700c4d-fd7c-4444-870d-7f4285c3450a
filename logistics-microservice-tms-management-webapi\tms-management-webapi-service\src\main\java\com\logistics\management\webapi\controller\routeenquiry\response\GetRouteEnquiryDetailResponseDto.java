package com.logistics.management.webapi.controller.routeenquiry.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/7/8 15:31
 */
@Data
public class GetRouteEnquiryDetailResponseDto {

    /**
     * 路线询价单表id
     */
    private String routeEnquiryId="";

    /**
     * 竞价单号
     */
    private String orderCode="";

    /**
     * 竞价状态：1 待业务审核，2 待车主确认，3 待结算审核，4 完成竞价，5 竞价取消
     */
    private String status="";
    /**
     * 竞价状态
     */
    private String statusLabel="";

    /**
     * 归档：0 否，1 是
     */
    private String ifArchive="";

    /**
     * 创建人
     */
    private String createdBy="";

    /**
     * 创建时间
     */
    private String createdTime="";

    /**
     * 货物名称
     */
    private String goodsName="";

    /**
     * 报价生效开始时间
     */
    private String quoteStartTime="";

    /**
     * 报价生效结束时间
     */
    private String quoteEndTime="";

    /**
     * 备注
     */
    private String remark="";


    /**
     * 竞价详情-地址列表
     */
    private List<GetRouteEnquiryDetailAddressListResponseDto> addressList=new ArrayList<>();


    /**
     * 车主报价记录
     */
    private List<RouteEnquiryQuoteListResponseDto> quoteList=new ArrayList<>();


    /**
     * 操作记录
     */
    private List<RouteEnquiryOperateLogListResponseDto> operateLogList=new ArrayList<>();


    /**
     * 上传文件列表
     */
    private List<RouteEnquiryFileListResponseDto> fileList=new ArrayList<>();

}
