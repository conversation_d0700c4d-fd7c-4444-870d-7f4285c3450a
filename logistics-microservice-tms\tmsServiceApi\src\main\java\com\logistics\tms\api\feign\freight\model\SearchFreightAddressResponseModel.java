package com.logistics.tms.api.feign.freight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 运价地址
 * @Author: sj
 * @Date: 2019/12/24 13:11
 */
@Data
public class SearchFreightAddressResponseModel {
    @ApiModelProperty("运价地址ID")
    private Long freightAddressId;
    @ApiModelProperty("公司运价ID")
    private Long freightId;
    @ApiModelProperty("计价类型: 计价类型 1 基价 2 一日游")
    private Integer calcType;
    @ApiModelProperty("仓库ID")
    private Long warehouseId;
    @ApiModelProperty("仓库名称")
    private String warehouseName;
    @ApiModelProperty("发货省ID")
    private Long fromProvinceId;
    @ApiModelProperty("发货省名称")
    private String fromProvinceName;
    @ApiModelProperty("发货市ID")
    private Long fromCityId;
    @ApiModelProperty("发货市名称")
    private String fromCityName;
    @ApiModelProperty("发货区ID")
    private Long fromAreaId;
    @ApiModelProperty("发货区名称")
    private String fromAreaName;
    @ApiModelProperty("卸货省ID")
    private Long toProvinceId;
    @ApiModelProperty("卸货省名称")
    private String toProvinceName;
    @ApiModelProperty("卸货市ID")
    private Long toCityId;
    @ApiModelProperty("卸货市名称")
    private String toCityName;
    @ApiModelProperty("卸货区ID")
    private Long toAreaId;
    @ApiModelProperty("卸货区名称")
    private String toAreaName;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;
}
