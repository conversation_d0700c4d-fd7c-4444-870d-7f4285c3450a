package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @deprecated
 */
@Deprecated
public class ExportDriverCostApplyInfo {

    private ExportDriverCostApplyInfo() {
    }

    private static final Map<String, String> DRIVER_COST_APPLY_INFO;

    static {
        DRIVER_COST_APPLY_INFO = new LinkedHashMap<>();
        DRIVER_COST_APPLY_INFO.put("审核状态", "auditStatusLabel");
        DRIVER_COST_APPLY_INFO.put("司机", "exportStaffName");
        DRIVER_COST_APPLY_INFO.put("司机机构", "staffPropertyLabel");
        DRIVER_COST_APPLY_INFO.put("车牌号", "vehicleNo");
        DRIVER_COST_APPLY_INFO.put("费用类型", "costTypeLabel");
        DRIVER_COST_APPLY_INFO.put("申请费用（元）", "applyCost");
        DRIVER_COST_APPLY_INFO.put("发生时间", "occurrenceTime");
        DRIVER_COST_APPLY_INFO.put("申请时间", "applyTime");
        DRIVER_COST_APPLY_INFO.put("关联油卡", "associatedOilCard");
        DRIVER_COST_APPLY_INFO.put("审核时间", "auditTime");
        DRIVER_COST_APPLY_INFO.put("审核人", "auditorName");

    }
    @Deprecated
    public static Map<String, String> getDriverCostApplyInfo() {
        return DRIVER_COST_APPLY_INFO;
    }
}

