package com.logistics.appapi.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author：wjf
 * @date：2021/4/9 14:15
 */
@Data
public class DriverReconciliationListCountResponseDto {
    @ApiModelProperty("全部")
    private String allCount="";
    @ApiModelProperty("待确认")
    private String waitConfirmCount="";
    @ApiModelProperty("待结清（包含部分结清）")
    private String waitSettleCount="";
    @ApiModelProperty("已结清")
    private String completeSettleCount="";
    @ApiModelProperty("待处理")
    private String waitSolveCount="";
}
