package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/29
 */
@Data
public class RenewableDemandListStatisticsResponseModel {

	@ApiModelProperty("全部")
	private Integer allCount;

	@ApiModelProperty("待发布")
	private Integer waitPublishCount;

	@ApiModelProperty("待调度")
	private Integer waitDispatchCount;

	@ApiModelProperty("部分调度")
	private Integer partDispatchCount;

	@ApiModelProperty("调度完成")
	private Integer dispatchCompleteCount;

	@ApiModelProperty("待签收")
	private Integer waitSignedAccount;

	@ApiModelProperty("已签收")
	private Integer signedAccount;

	@ApiModelProperty("取消")
	private Integer cancelCount;
}
