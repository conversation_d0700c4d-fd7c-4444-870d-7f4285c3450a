package com.logistics.tms.api.feign.terminalreachmanagement.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.terminalreachmanagement.ReachManagementServiceApi;
import com.logistics.tms.api.feign.terminalreachmanagement.model.GetReachManagementDetailRequestModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.GetReachManagementDetailResponseModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.SearchReachManagementListRequestModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.SearchReachManagementListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

@Component
public class ReachManagementServiceApiHystrix implements ReachManagementServiceApi {

    @Override
    public Result<PageInfo<SearchReachManagementListResponseModel>> searchReachManagementList(SearchReachManagementListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetReachManagementDetailResponseModel> getReachManagementDetail(GetReachManagementDetailRequestModel requestModel) {
        return Result.timeout();
    }

}
