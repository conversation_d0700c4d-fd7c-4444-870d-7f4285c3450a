package com.logistics.tms.mapper;

import com.logistics.tms.biz.biddingorder.bo.DemandInfoBo;
import com.logistics.tms.biz.biddingorder.bo.SelectDemandByConditionReqBo;
import com.logistics.tms.entity.TBiddingOrderDemand;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/04/26
*/
@Mapper
public interface TBiddingOrderDemandMapper extends BaseMapper<TBiddingOrderDemand> {

    TBiddingOrderDemand selectByBiddingOrderIdAndDemandId(@Param("biddingOrderId") Long bindingOrderId,@Param("demandId") Long demandId);

    /**
     * @param bindingOrderId
     * @return {@link List}<{@link TBiddingOrderDemand}>
     */
    List<TBiddingOrderDemand> selectByBiddingOrderId(@Param("bindingOrderId") Long bindingOrderId);

    long selectCountByBiddingOrderId(@Param("biddingOrderId")Long bindingOrderId);




    /**
     * 查询关联的第一个需求单信息
     *
     * @param loadAddress
     * @param unloadAddress
     * @param biddingOrderIds
     * @return {@link List}<{@link DemandInfoBo}>
     */
    List<DemandInfoBo> selectFirstDemandInfo(@Param("loadAddress") String loadAddress,@Param("unloadAddress") String unloadAddress,@Param("biddingOrderIds") List<Long> biddingOrderIds);

    /**
     * 查询关联需求单信息
     * @param selectDemandByConditionReqBo
     * @return {@link List}<{@link DemandInfoBo}>
     */
    List<DemandInfoBo> selectDemandByCondition(SelectDemandByConditionReqBo selectDemandByConditionReqBo);


}