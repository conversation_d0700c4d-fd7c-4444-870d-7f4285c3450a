package com.logistics.tms.controller.reservationorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2024/8/19 09:48
 */
@Data
public class ReservationOrderConfirmSignRequestModel {

    /**
     * 预约单id
     */
    private Long reservationOrderId;

    /**
     * 定位-经度
     */
    private String longitude;
    /**
     * 定位-纬度
     */
    private String latitude;

    /**
     * 来源 0。小程序司机 1.h5访客越野
     */
    private Integer source;

    /**
     * H5验证过的手机号
     */
    @ApiModelProperty("H5验证过的手机号")
    private String mobilePhone;

}
