package com.logistics.management.webapi.controller.reserveapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Data
public class ReserveApplyRemitRequestDto {

	@ApiModelProperty(value = "申请记录id", required = true)
	@NotBlank(message = "请选择要查看的记录")
	private String applyId;

	@ApiModelProperty(value = "打款编号0<=长度<=50")
	@Length(max = 50, message = "打款编号最多50个字符")
	private String remitCode;

	@ApiModelProperty(value = "打款时间")
	@NotBlank(message = "请选择打款时间")
	private String payTime;

	@ApiModelProperty(value = "打款凭据,1-2张图片")
	private List<String> tickets;

	@ApiModelProperty(value = "备注 长度<=100")
	@Length(max = 100, message = "备注最多100个字符")
	private String remark;
}
