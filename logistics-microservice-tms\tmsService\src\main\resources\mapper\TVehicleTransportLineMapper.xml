<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TVehicleTransportLineMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TVehicleTransportLine">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="load_province_id" jdbcType="BIGINT" property="loadProvinceId" />
    <result column="load_province_name" jdbcType="VARCHAR" property="loadProvinceName" />
    <result column="load_city_id" jdbcType="BIGINT" property="loadCityId" />
    <result column="load_city_name" jdbcType="VARCHAR" property="loadCityName" />
    <result column="load_area_id" jdbcType="BIGINT" property="loadAreaId" />
    <result column="load_area_name" jdbcType="VARCHAR" property="loadAreaName" />
    <result column="load_detail_address" jdbcType="VARCHAR" property="loadDetailAddress" />
    <result column="load_warehouse" jdbcType="VARCHAR" property="loadWarehouse" />
    <result column="unload_province_id" jdbcType="BIGINT" property="unloadProvinceId" />
    <result column="unload_province_name" jdbcType="VARCHAR" property="unloadProvinceName" />
    <result column="unload_city_id" jdbcType="BIGINT" property="unloadCityId" />
    <result column="unload_city_name" jdbcType="VARCHAR" property="unloadCityName" />
    <result column="unload_area_id" jdbcType="BIGINT" property="unloadAreaId" />
    <result column="unload_area_name" jdbcType="VARCHAR" property="unloadAreaName" />
    <result column="unload_detail_address" jdbcType="VARCHAR" property="unloadDetailAddress" />
    <result column="unload_warehouse" jdbcType="VARCHAR" property="unloadWarehouse" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, vehicle_id, vehicle_no, load_province_id, load_province_name, load_city_id, load_city_name, 
    load_area_id, load_area_name, load_detail_address, load_warehouse, unload_province_id, 
    unload_province_name, unload_city_id, unload_city_name, unload_area_id, unload_area_name, 
    unload_detail_address, unload_warehouse, created_time, last_modified_by, last_modified_time, 
    created_by, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_vehicle_transport_line
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_vehicle_transport_line
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TVehicleTransportLine">
    insert into t_vehicle_transport_line (id, vehicle_id, vehicle_no, 
      load_province_id, load_province_name, load_city_id, 
      load_city_name, load_area_id, load_area_name, 
      load_detail_address, load_warehouse, unload_province_id, 
      unload_province_name, unload_city_id, unload_city_name, 
      unload_area_id, unload_area_name, unload_detail_address, 
      unload_warehouse, created_time, last_modified_by, 
      last_modified_time, created_by, valid
      )
    values (#{id,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, #{vehicleNo,jdbcType=VARCHAR}, 
      #{loadProvinceId,jdbcType=BIGINT}, #{loadProvinceName,jdbcType=VARCHAR}, #{loadCityId,jdbcType=BIGINT}, 
      #{loadCityName,jdbcType=VARCHAR}, #{loadAreaId,jdbcType=BIGINT}, #{loadAreaName,jdbcType=VARCHAR}, 
      #{loadDetailAddress,jdbcType=VARCHAR}, #{loadWarehouse,jdbcType=VARCHAR}, #{unloadProvinceId,jdbcType=BIGINT}, 
      #{unloadProvinceName,jdbcType=VARCHAR}, #{unloadCityId,jdbcType=BIGINT}, #{unloadCityName,jdbcType=VARCHAR}, 
      #{unloadAreaId,jdbcType=BIGINT}, #{unloadAreaName,jdbcType=VARCHAR}, #{unloadDetailAddress,jdbcType=VARCHAR}, 
      #{unloadWarehouse,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TVehicleTransportLine">
    insert into t_vehicle_transport_line
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleNo != null">
        vehicle_no,
      </if>
      <if test="loadProvinceId != null">
        load_province_id,
      </if>
      <if test="loadProvinceName != null">
        load_province_name,
      </if>
      <if test="loadCityId != null">
        load_city_id,
      </if>
      <if test="loadCityName != null">
        load_city_name,
      </if>
      <if test="loadAreaId != null">
        load_area_id,
      </if>
      <if test="loadAreaName != null">
        load_area_name,
      </if>
      <if test="loadDetailAddress != null">
        load_detail_address,
      </if>
      <if test="loadWarehouse != null">
        load_warehouse,
      </if>
      <if test="unloadProvinceId != null">
        unload_province_id,
      </if>
      <if test="unloadProvinceName != null">
        unload_province_name,
      </if>
      <if test="unloadCityId != null">
        unload_city_id,
      </if>
      <if test="unloadCityName != null">
        unload_city_name,
      </if>
      <if test="unloadAreaId != null">
        unload_area_id,
      </if>
      <if test="unloadAreaName != null">
        unload_area_name,
      </if>
      <if test="unloadDetailAddress != null">
        unload_detail_address,
      </if>
      <if test="unloadWarehouse != null">
        unload_warehouse,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="loadProvinceId != null">
        #{loadProvinceId,jdbcType=BIGINT},
      </if>
      <if test="loadProvinceName != null">
        #{loadProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="loadCityId != null">
        #{loadCityId,jdbcType=BIGINT},
      </if>
      <if test="loadCityName != null">
        #{loadCityName,jdbcType=VARCHAR},
      </if>
      <if test="loadAreaId != null">
        #{loadAreaId,jdbcType=BIGINT},
      </if>
      <if test="loadAreaName != null">
        #{loadAreaName,jdbcType=VARCHAR},
      </if>
      <if test="loadDetailAddress != null">
        #{loadDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="loadWarehouse != null">
        #{loadWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="unloadProvinceId != null">
        #{unloadProvinceId,jdbcType=BIGINT},
      </if>
      <if test="unloadProvinceName != null">
        #{unloadProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="unloadCityId != null">
        #{unloadCityId,jdbcType=BIGINT},
      </if>
      <if test="unloadCityName != null">
        #{unloadCityName,jdbcType=VARCHAR},
      </if>
      <if test="unloadAreaId != null">
        #{unloadAreaId,jdbcType=BIGINT},
      </if>
      <if test="unloadAreaName != null">
        #{unloadAreaName,jdbcType=VARCHAR},
      </if>
      <if test="unloadDetailAddress != null">
        #{unloadDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="unloadWarehouse != null">
        #{unloadWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TVehicleTransportLine">
    update t_vehicle_transport_line
    <set>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="loadProvinceId != null">
        load_province_id = #{loadProvinceId,jdbcType=BIGINT},
      </if>
      <if test="loadProvinceName != null">
        load_province_name = #{loadProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="loadCityId != null">
        load_city_id = #{loadCityId,jdbcType=BIGINT},
      </if>
      <if test="loadCityName != null">
        load_city_name = #{loadCityName,jdbcType=VARCHAR},
      </if>
      <if test="loadAreaId != null">
        load_area_id = #{loadAreaId,jdbcType=BIGINT},
      </if>
      <if test="loadAreaName != null">
        load_area_name = #{loadAreaName,jdbcType=VARCHAR},
      </if>
      <if test="loadDetailAddress != null">
        load_detail_address = #{loadDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="loadWarehouse != null">
        load_warehouse = #{loadWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="unloadProvinceId != null">
        unload_province_id = #{unloadProvinceId,jdbcType=BIGINT},
      </if>
      <if test="unloadProvinceName != null">
        unload_province_name = #{unloadProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="unloadCityId != null">
        unload_city_id = #{unloadCityId,jdbcType=BIGINT},
      </if>
      <if test="unloadCityName != null">
        unload_city_name = #{unloadCityName,jdbcType=VARCHAR},
      </if>
      <if test="unloadAreaId != null">
        unload_area_id = #{unloadAreaId,jdbcType=BIGINT},
      </if>
      <if test="unloadAreaName != null">
        unload_area_name = #{unloadAreaName,jdbcType=VARCHAR},
      </if>
      <if test="unloadDetailAddress != null">
        unload_detail_address = #{unloadDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="unloadWarehouse != null">
        unload_warehouse = #{unloadWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TVehicleTransportLine">
    update t_vehicle_transport_line
    set vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      load_province_id = #{loadProvinceId,jdbcType=BIGINT},
      load_province_name = #{loadProvinceName,jdbcType=VARCHAR},
      load_city_id = #{loadCityId,jdbcType=BIGINT},
      load_city_name = #{loadCityName,jdbcType=VARCHAR},
      load_area_id = #{loadAreaId,jdbcType=BIGINT},
      load_area_name = #{loadAreaName,jdbcType=VARCHAR},
      load_detail_address = #{loadDetailAddress,jdbcType=VARCHAR},
      load_warehouse = #{loadWarehouse,jdbcType=VARCHAR},
      unload_province_id = #{unloadProvinceId,jdbcType=BIGINT},
      unload_province_name = #{unloadProvinceName,jdbcType=VARCHAR},
      unload_city_id = #{unloadCityId,jdbcType=BIGINT},
      unload_city_name = #{unloadCityName,jdbcType=VARCHAR},
      unload_area_id = #{unloadAreaId,jdbcType=BIGINT},
      unload_area_name = #{unloadAreaName,jdbcType=VARCHAR},
      unload_detail_address = #{unloadDetailAddress,jdbcType=VARCHAR},
      unload_warehouse = #{unloadWarehouse,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>