package com.logistics.tms.controller.staffvehiclerelation.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/7/26 13:56
 */
@Data
public class StaffVehicleRelationRequestModel {
    @ApiModelProperty("类型 1 自主，2 外部，3 自营")
    private Integer type;
    @ApiModelProperty("司机名称")
    private String staffName;
    @ApiModelProperty("司机电话")
    private String staffMobile;
    @ApiModelProperty("牵引车车牌号")
    private String tractorVehicleNo;
    @ApiModelProperty("挂车车牌号")
    private String trailerVehicleNo;
    @ApiModelProperty("车辆类别: 1 牵引车 2 挂车 3 一体车")
    private Integer vehicleCategory;
    @ApiModelProperty("备注")
    private String remark;
}
