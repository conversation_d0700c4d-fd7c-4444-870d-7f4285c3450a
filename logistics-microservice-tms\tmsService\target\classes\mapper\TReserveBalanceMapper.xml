<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TReserveBalanceMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TReserveBalance" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="driver_id" property="driverId" jdbcType="BIGINT" />
    <result column="balance_amount" property="balanceAmount" jdbcType="DECIMAL" />
    <result column="recharge_amount" property="rechargeAmount" jdbcType="DECIMAL" />
    <result column="verification_amount" property="verificationAmount" jdbcType="DECIMAL" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, driver_id, balance_amount, recharge_amount, verification_amount, created_by, 
    created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_reserve_balance
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_reserve_balance
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TReserveBalance" >
    insert into t_reserve_balance (id, driver_id, balance_amount, 
      recharge_amount, verification_amount, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{driverId,jdbcType=BIGINT}, #{balanceAmount,jdbcType=DECIMAL}, 
      #{rechargeAmount,jdbcType=DECIMAL}, #{verificationAmount,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TReserveBalance" >
    insert into t_reserve_balance
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="driverId != null" >
        driver_id,
      </if>
      <if test="balanceAmount != null" >
        balance_amount,
      </if>
      <if test="rechargeAmount != null" >
        recharge_amount,
      </if>
      <if test="verificationAmount != null" >
        verification_amount,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="driverId != null" >
        #{driverId,jdbcType=BIGINT},
      </if>
      <if test="balanceAmount != null" >
        #{balanceAmount,jdbcType=DECIMAL},
      </if>
      <if test="rechargeAmount != null" >
        #{rechargeAmount,jdbcType=DECIMAL},
      </if>
      <if test="verificationAmount != null" >
        #{verificationAmount,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TReserveBalance" >
    update t_reserve_balance
    <set >
      <if test="driverId != null" >
        driver_id = #{driverId,jdbcType=BIGINT},
      </if>
      <if test="balanceAmount != null" >
        balance_amount = #{balanceAmount,jdbcType=DECIMAL},
      </if>
      <if test="rechargeAmount != null" >
        recharge_amount = #{rechargeAmount,jdbcType=DECIMAL},
      </if>
      <if test="verificationAmount != null" >
        verification_amount = #{verificationAmount,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TReserveBalance" >
    update t_reserve_balance
    set driver_id = #{driverId,jdbcType=BIGINT},
      balance_amount = #{balanceAmount,jdbcType=DECIMAL},
      recharge_amount = #{rechargeAmount,jdbcType=DECIMAL},
      verification_amount = #{verificationAmount,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>