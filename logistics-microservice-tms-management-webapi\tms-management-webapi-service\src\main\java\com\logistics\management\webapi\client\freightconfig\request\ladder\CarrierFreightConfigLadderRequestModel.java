package com.logistics.management.webapi.client.freightconfig.request.ladder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CarrierFreightConfigLadderRequestModel {

    @ApiModelProperty(value = "阶梯配置Id, 编辑时使用")
    private Long freightConfigLadderId;

    @ApiModelProperty(value = "阶梯层级; 0 为固定单价/总价", required = true)
    private Integer ladderLevel;

    @ApiModelProperty(value = "阶梯类型; 1: KM(公里); 2: 数量;", required = true)
    private Integer ladderType;

    @ApiModelProperty(value = "阶梯起始", required = true)
    private BigDecimal ladderFrom;

    @ApiModelProperty(value = "阶梯终止（包含等于）", required = true)
    private BigDecimal ladderTo;

    @ApiModelProperty(value = "单位; 1: 件; 2: 吨; 4: 块", required = true)
    private Integer ladderUnit;

    @ApiModelProperty(value = "价格模式; 1: 单价; 2: 总价", required = true)
    private Integer priceMode;

    @ApiModelProperty(value = "价格(元)", required = true)
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "多级阶梯")
    private List<CarrierFreightConfigLadderRequestModel> ladderConfigList;
}
