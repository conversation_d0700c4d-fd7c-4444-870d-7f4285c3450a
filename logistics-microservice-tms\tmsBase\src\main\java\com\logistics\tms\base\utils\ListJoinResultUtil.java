package com.logistics.tms.base.utils;

import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import com.yelo.tools.utils.ListUtils;

import java.util.List;

public class ListJoinResultUtil {

    private ListJoinResultUtil() {
    }

    public static <S, D> void join(List<S> sourceList, Result<List<D>> destList, MatchRuleInter<S, D> matchRule, MatchOperateInter<S, D> matchOper) {
        if (!destList.isSuccess()) {
            throw new BizException(destList.getErrcode(), destList.getErrmsg());
        }
        joinList(sourceList, destList.getData(), matchRule, matchOper);
    }

    public static <S, D> void joinNoThrow(List<S> sourceList, Result<List<D>> destList, MatchRuleInter<S, D> matchRule, MatchOperateInter<S, D> matchOper) {
        if (destList.isSuccess()) {
            joinList(sourceList, destList.getData(), matchRule, matchOper);
        }
    }

    public static <S, D> void joinList(List<S> sourceList, List<D> destList, MatchRuleInter<S, D> matchRule, MatchOperateInter<S, D> matchOper) {
        if (!(ListUtils.isEmpty(sourceList) || destList == null || ListUtils.isEmpty(destList))) {
            sourceList.stream().forEach(source -> {
                D matched = destList.stream().filter(dest -> source != null && dest != null && matchRule.match(source, dest)).findFirst().orElse(null);
                if (matched != null) {
                    matchOper.match(source, matched);
                }
            });
        }
    }
}
