package com.logistics.management.webapi.controller.routeconfig.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class RouteDistanceConfigEditRequestDto extends RouteDistanceConfigRequestDto {

    @ApiModelProperty(value = "计费距离", required = true)
    @NotBlank(message = "计费距离不允许为空")
    @Min(value = 1, message = "计费距离不允许为 0 ")
    private String billingDistance;
}
