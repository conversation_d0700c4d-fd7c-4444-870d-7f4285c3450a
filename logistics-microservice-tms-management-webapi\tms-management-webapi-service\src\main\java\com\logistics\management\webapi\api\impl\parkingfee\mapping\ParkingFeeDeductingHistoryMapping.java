package com.logistics.management.webapi.api.impl.parkingfee.mapping;

import com.logistics.management.webapi.api.feign.parkingfee.dto.ParkingFeeDeductingHistoryResponseDto;
import com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeDeductingHistoryResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;

/**
 * @Author: sj
 * @Date: 2019/10/9 16:32
 */
public class ParkingFeeDeductingHistoryMapping extends MapperMapping<ParkingFeeDeductingHistoryResponseModel,ParkingFeeDeductingHistoryResponseDto> {
    @Override
    public void configure() {
        ParkingFeeDeductingHistoryResponseModel source = this.getSource();
        ParkingFeeDeductingHistoryResponseDto target = this.getDestination();

        if(source !=null){
            target.setRemainingDeductingFeeTotal(ConverterUtils.toString(source.getDeductingFee().add(source.getRemainingDeductingFee())));
        }
    }
}
