package com.logistics.tms.base.enums;

/**
 * 车主授权状态枚举
 */
public enum CompanyCarrierAuthAuditStatusEnum {
    WAIT_AUTH(0, "待授权"),
    WAIT_AUDIT(1, "待审核"),
    AUDIT_REJECT(2, "已驳回"),
    BE_AUTH(3, "已授权"),
    ;
    private final Integer key;
    private final String value;

    CompanyCarrierAuthAuditStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
