package com.logistics.tms.controller.warehouseaddress.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class WarehouseAddressListResponseModel {

    @ApiModelProperty("状态 0 禁用 1启用")
    private Integer enabled;
    @ApiModelProperty("Id")
    private Long warehouseAddressId;
    @ApiModelProperty("仓库")
    private String warehouse;
    @ApiModelProperty("货主")
    private String companyEntrustName;
    @ApiModelProperty("省ID")
    private Long provinceId;
    @ApiModelProperty("市ID")
    private Long cityId;
    @ApiModelProperty("区ID")
    private Long areaId;
    @ApiModelProperty("省")
    private String provinceName;
    @ApiModelProperty("市")
    private String cityName;
    @ApiModelProperty("区")
    private String areaName;
    @ApiModelProperty("操作人")
    private String operateUserName;
    @ApiModelProperty("操作时间")
    private Date operateTime;

}
