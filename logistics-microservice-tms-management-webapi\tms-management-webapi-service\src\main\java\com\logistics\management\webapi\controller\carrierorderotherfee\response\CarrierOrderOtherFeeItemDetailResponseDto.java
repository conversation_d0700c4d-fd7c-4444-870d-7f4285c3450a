package com.logistics.management.webapi.controller.carrierorderotherfee.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class CarrierOrderOtherFeeItemDetailResponseDto {

    @ApiModelProperty(value = "主表id")
    private String carrierOrderOtherFeeItemId = "";

    @ApiModelProperty(value = "费用名称：1 短驳费，2 保管费，3 装卸费，4 压车费，5 质量处罚费，6 其他杂费")
    private String feeType = "";
    @ApiModelProperty("费用名称")
    private String feeTypeLabel="";

    @ApiModelProperty("调整费用符号：1 ‘+’， 2 ‘-’")
    private String feeAmountSymbol="";
    @ApiModelProperty(value = "金额 :0<费用<=10000元")
    private String feeAmount = "";

    /**
     * 单据-绝对路径 v2.43
     */
    @ApiModelProperty("单据-绝对路径 v2.43")
    private List<BillPicDto> picList= new ArrayList<>();


    @ApiModelProperty(value = "费用需求 v2.44(2) 1:我司2.客户 v2.44(2)" )
    private String feeSource;


    @ApiModelProperty(value = "费用需求文本  v2.44(2)" )
    private String feeSourceLabel;

}
