package com.logistics.tms.api.feign.safegroupmeeting;

import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.safegroupmeeting.hystrix.SafeGroupMeetingServiceApiHystrix;
import com.logistics.tms.api.feign.safegroupmeeting.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * @author: <PERSON><PERSON><PERSON>
 * @date:  2021/4/9 09:00
 */
@Api(value = "API-SafeGroupMeetingApi-安全小组领导例会")
@FeignClient(name = "logistics-tms-services", fallback = SafeGroupMeetingServiceApiHystrix.class)
public interface SafeGroupMeetingServiceApi {

    @ApiOperation(("新建安全小组领导会议"))
    @PostMapping(value = "/service/safeGroupMeeting/addSafeGroupMeeting")
    Result<Boolean> addSafeGroupMeeting(@RequestBody AddSafeGroupMeetingRequestModel requestModel);


    @ApiOperation(("安全小组领导会议记录列表"))
    @PostMapping(value = "/service/safeGroupMeeting/safeGroupMeetingData")
    Result<List<SafeGroupMeetingResponseModel>> safeGroupMeetingData(@RequestBody SafeGroupMeetingRequestModel requestModel);


    @ApiOperation(("安全小组领导会议详情"))
    @PostMapping(value = "/service/safeGroupMeeting/safeGroupMeetingDetail")
    Result<SafeGroupMeetingDetailResponseModel> safeGroupMeetingDetail(@RequestBody SafeGroupMeetingDetailRequestModel requestModel);



}
