package com.logistics.tms.biz.carrierdriverrel;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.basicinfo.web.BasicInfoWebBiz;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.customeraccount.CustomerAccountBiz;
import com.logistics.tms.biz.staff.StaffManagementBiz;
import com.logistics.tms.controller.carrierdriverrel.request.AddOrModifyDriverRequestModel;
import com.logistics.tms.controller.carrierdriverrel.request.DriveDetailRequestModel;
import com.logistics.tms.controller.carrierdriverrel.request.SearchCarrierDriverListRequestModel;
import com.logistics.tms.controller.carrierdriverrel.response.DriveDetailResponseModel;
import com.logistics.tms.controller.carrierdriverrel.response.SearchCarrierDriverListResponseModel;
import com.logistics.tms.controller.customeraccount.request.OpenAccountRequestModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.redis.redisdistributedlock.DistributedLock;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service("carrierDriverBiz")
public class CarrierDriverBiz {

    @Autowired
    private TCarrierDriverRelationMapper tCarrierDriverRelationMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TOperateLogsMapper tOperateLogsMapper;
    @Autowired
    private TStaffBasicMapper tStaffBasicMapper;
    @Autowired
    private TStaffDriverCredentialMapper tStaffDriverCredentialMapper;
    @Autowired
    private CustomerAccountBiz customerAccountBiz;
    @Autowired
    private StaffManagementBiz staffManagementBiz;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private TRealNameAuthenticationMapper realNameAuthenticationMapper;
    @Autowired
    private BasicInfoWebBiz basicInfoWebBiz;


    /**
     * 司机列表(前台)
     *
     * @param requestModel
     * @return
     */
    public List<SearchCarrierDriverListResponseModel> searchCarrierDriverList(SearchCarrierDriverListRequestModel requestModel) {
        requestModel.setCompanyCarrierId(commonBiz.getLoginUserCompanyCarrierId());
        requestModel.enablePaging();
        return tCarrierDriverRelationMapper.searchCarrierDriverList(requestModel);
    }

    /**
     * 前台车主获取司机详情
     * @param requestModel 司机ID
     * @return 司机详情
     */
    public DriveDetailResponseModel getDetail(DriveDetailRequestModel requestModel) {
        //获取当前登录的车主ID
        Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }
        //查询司机详情
        return tCarrierDriverRelationMapper.getCompanyDriverDetailByDriverId(requestModel.getCarrierDriverId(), loginUserCompanyCarrierId);
    }

    /**
     * 前台车主新增/编辑司机
     *
     * @param requestModel 司机信息
     */
    @Transactional
    @DistributedLock(prefix = CommonConstant.TMS_ADD_STAFF_LOCK,
            keys = "#requestModel.mobile",
            waitTime = 3)
    public void addOrModifyStaff(AddOrModifyDriverRequestModel requestModel) {
        // 校验车主是否存在
        Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        TCompanyCarrier companyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(companyCarrierId);
        if (companyCarrier == null || IfValidEnum.INVALID.getKey().equals(companyCarrier.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }

        //只有其他车主才能新增
        if (!IsOurCompanyEnum.OTHER_COMPANY.getKey().equals(companyCarrier.getLevel())) {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }

        // 车主司机关联id
        Long carrierDriverId = requestModel.getCarrierDriverId();
        TStaffBasic tStaffBasicUp = null;
        //根据手机号查询人员资产
        TStaffBasic tStaffBasic = tStaffBasicMapper.getStaffByMobile(requestModel.getMobile());

        //判断是新增还是编辑
        if (carrierDriverId == null) { //新增
            Long relId;
            // 实名认证校验
            basicInfoWebBiz.checkCarrierRealNameOrAuthorization(companyCarrierId);
            //人员信息存在
            if (tStaffBasic != null && IfValidEnum.VALID.getKey().equals(tStaffBasic.getValid())) {
                //判断人员的机构是内部(自主,自营)还是外部
                //外部
                if (StaffPropertyEnum.EXTERNAL_STAFF.getKey().equals(tStaffBasic.getStaffProperty())) {
                    //查询当前车主和司机的关联关系
                    TCarrierDriverRelation carrierDriverRelation = tCarrierDriverRelationMapper.getByCompanyCarrierIdAndDriverId(companyCarrierId, tStaffBasic.getId(), null);
                    //不存在关联关系则新增关联关系和操作记录
                    if (carrierDriverRelation == null) {
                        //更新人员信息
                        tStaffBasicUp = new TStaffBasic();
                        tStaffBasicUp.setId(tStaffBasic.getId());
                        tStaffBasicUp.setIdentityNumber(requestModel.getIdentityNumber());
                        //添加车主司机关联关系和操作记录
                        relId = staffManagementBiz.addCarrierDriverRelAndLog(tStaffBasic, companyCarrierId);
                    } else {
                        //已存在就提示
                        throw new BizException(CarrierDataExceptionEnum.STAFF_EXIST);
                    }

                    //当前司机姓名是否和输入的一致,并且是驾驶员
                    if (!tStaffBasic.getName().equals(requestModel.getName()) ||
                            !(StaffTypeEnum.DRIVER.getKey().equals(tStaffBasic.getType()) || StaffTypeEnum.DRIVER_SUPERCARGO.getKey().equals(tStaffBasic.getType()))) {
                        throw new BizException(CarrierDataExceptionEnum.BASIC_DATA_ERROR);
                    }
                } else {
                    //不能添加内部人员
                    throw new BizException(CarrierDataExceptionEnum.BASIC_DATA_ERROR);
                }
            } else {
                //人员信息不存在

                Integer realNameAuthenticationStatus = null;
                //根据手机号查询实名信息,如果新增的司机和已存在的实名信息不一致则不允许新增
                TRealNameAuthentication tRealNameAuthentication = realNameAuthenticationMapper.selectRealNameAuthenticationByMobile(requestModel.getMobile());
                if (tRealNameAuthentication != null && CommonConstant.INTEGER_TWO.equals(tRealNameAuthentication.getCertificationStatus())) {
                    if (!tRealNameAuthentication.getName().equals(requestModel.getName())
                            || !tRealNameAuthentication.getIdentityNumber().equals(requestModel.getIdentityNumber())) {
                        throw new BizException(CarrierDataExceptionEnum.STAFF_ALREADY_REAL_NAME_AUTH);
                    }
                    //已经有实名信息了,
                    realNameAuthenticationStatus = RealNameAuthenticationStatusEnum.REAL_NAME.getKey();
                }

                //新增人员信息
                TStaffBasic tStaffBasicAdd = new TStaffBasic();
                tStaffBasicAdd.setStaffProperty(StaffPropertyEnum.EXTERNAL_STAFF.getKey());
                tStaffBasicAdd.setType(StaffTypeEnum.DRIVER.getKey());
                tStaffBasicAdd.setMobile(requestModel.getMobile());
                tStaffBasicAdd.setName(requestModel.getName());
                tStaffBasicAdd.setIdentityNumber(requestModel.getIdentityNumber());
                tStaffBasicAdd.setOpenStatus(OpenStatusEnum.OPEN.getKey());
                tStaffBasicAdd.setRealNameAuthenticationStatus(realNameAuthenticationStatus);
                commonBiz.setBaseEntityAdd(tStaffBasicAdd, BaseContextHandler.getUserName());
                tStaffBasicMapper.insertSelective(tStaffBasicAdd);

                //新增司机证件信息
                TStaffDriverCredential tStaffDriverCredential = new TStaffDriverCredential();
                tStaffDriverCredential.setStaffId(tStaffBasicAdd.getId());
                commonBiz.setBaseEntityAdd(tStaffDriverCredential, BaseContextHandler.getUserName());
                tStaffDriverCredentialMapper.insertSelective(tStaffDriverCredential);

                //添加车主司机关联记录
                relId = staffManagementBiz.addCarrierDriverRelAndLog(tStaffBasicAdd, companyCarrierId);
            }

            if (relId != null) {
                //新增司机账号信息并开通
                OpenAccountRequestModel openAccountRequestModel = new OpenAccountRequestModel();
                openAccountRequestModel.setType(CommonConstant.INTEGER_ONE);
                openAccountRequestModel.setMobile(requestModel.getMobile());
                openAccountRequestModel.setUserId(relId);
                openAccountRequestModel.setUserRole(AccountUserRoleTypeEnum.DRIVER_APPLET.getKey());
                openAccountRequestModel.setUserName(requestModel.getName());
                openAccountRequestModel.setIfClose(CommonConstant.INTEGER_ZERO);
                customerAccountBiz.openOrCloseAccount(openAccountRequestModel);
            }
        } else {
            //编辑时手机号不能修改

            //查询车主司机关联信息
            TCarrierDriverRelation driverRelation = tCarrierDriverRelationMapper.selectByPrimaryKey(requestModel.getCarrierDriverId());
            if (driverRelation == null || IfValidEnum.INVALID.getKey().equals(driverRelation.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
            }

            if (tStaffBasic == null || IfValidEnum.INVALID.getKey().equals(tStaffBasic.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
            }

            //更新人员信息
            tStaffBasicUp = new TStaffBasic();
            tStaffBasicUp.setId(tStaffBasic.getId());
            tStaffBasicUp.setIdentityNumber(requestModel.getIdentityNumber());
        }

        //更新人员信息
        if (tStaffBasicUp != null) {
            //如果司机已经实名,禁止修改姓名和身份证号
            if (RealNameAuthenticationStatusEnum.REAL_NAME.getKey().equals(tStaffBasic.getRealNameAuthenticationStatus())) {
                if (!tStaffBasic.getName().equals(requestModel.getName())
                        || !tStaffBasic.getIdentityNumber().equals(requestModel.getIdentityNumber())) {
                    throw new BizException(CarrierDataExceptionEnum.STAFF_ALREADY_REAL_NAME_AUTH);
                }
            }
            commonBiz.setBaseEntityModify(tStaffBasicUp, BaseContextHandler.getUserName());
            tStaffBasicMapper.updateByPrimaryKeySelective(tStaffBasicUp);
        }
    }

    /**
     * 批量添加车主司机关联关系并记录日志
     *
     * @param tStaffBasicList  要添加关联关系的司机集合
     * @param companyCarrierId 车主公司id
     */
    @Transactional
    public void addCarrierDriverRelAndLog(List<TStaffBasic> tStaffBasicList, Long companyCarrierId) {
        //车主司机关联list
        List<TCarrierDriverRelation> carrierDriverRelationList = new ArrayList<>();
        //操作日志list
        List<TOperateLogs> tOperateLogsList = new ArrayList<>();
        TCarrierDriverRelation tCarrierDriverRelation;
        //遍历人员基本信息
        for (TStaffBasic tStaffBasic : tStaffBasicList) {
            //创建关联记录
            tCarrierDriverRelation = new TCarrierDriverRelation();
            tCarrierDriverRelation.setDriverId(tStaffBasic.getId());
            tCarrierDriverRelation.setCompanyCarrierId(companyCarrierId);
            commonBiz.setBaseEntityAdd(tCarrierDriverRelation, BaseContextHandler.getUserName());
            carrierDriverRelationList.add(tCarrierDriverRelation);

            //添加操作日志
            tOperateLogsList.add(commonBiz.addOperateLogs(companyCarrierId, OperateLogsOperateTypeEnum.CARRIER_DRIVER_ADD, tStaffBasic.getName() + " " + tStaffBasic.getMobile(), BaseContextHandler.getUserName()));
        }

        //添加关联关系
        if (ListUtils.isNotEmpty(carrierDriverRelationList)) {
            tCarrierDriverRelationMapper.batchInsert(carrierDriverRelationList);
        }

        //添加操作日志
        if (ListUtils.isNotEmpty(tOperateLogsList)) {
            tOperateLogsMapper.batchInsert(tOperateLogsList);
        }
    }
}

