package com.logistics.tms.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/14 18:40
 */
@Data
public class GetInsuranceCostsByVehicleIdResponseModel {
    private Long insuranceCostsId;
    @ApiModelProperty("商业险剩余未扣除费用")
    private BigDecimal commercialInsuranceCost;
    @ApiModelProperty("交强险剩余未扣除费用")
    private BigDecimal compulsoryInsuranceCost;
    @ApiModelProperty("货物险剩余未扣除费用")
    private BigDecimal cargoInsuranceCost;
    @ApiModelProperty("承运人险剩余未扣除费用")
    private BigDecimal carrierInsuranceCost;
    @ApiModelProperty("已支付商业险费用")
    private BigDecimal payCommercialInsuranceCost;
    @ApiModelProperty("已支付交强险内容")
    private BigDecimal payCompulsoryInsuranceCost;
    @ApiModelProperty("已支持货物险")
    private BigDecimal payCargoInsuranceCost;
    @ApiModelProperty("已支付承运人险")
    private BigDecimal payCarrierInsuranceCost;
    @ApiModelProperty("保险理赔")
    private BigDecimal insuranceClaimsCost;
    @ApiModelProperty("关联的保险")
    private List<Long> insuranceIdList;
}
