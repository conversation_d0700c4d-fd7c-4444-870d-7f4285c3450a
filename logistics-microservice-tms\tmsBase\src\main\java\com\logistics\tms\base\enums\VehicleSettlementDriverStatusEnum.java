package com.logistics.tms.base.enums;

public enum VehicleSettlementDriverStatusEnum {
    DEFAULT(-1,""),
    DO_NOT_NEED_TO_CONFIRM(0,"无需确认"),
    CONFIRM(1,"确认"),
    REJECT(2,"驳回"),
    ;

    private Integer key;
    private String value;
    VehicleSettlementDriverStatusEnum(Integer key , String value){
        this.key=key;
        this.value=value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
