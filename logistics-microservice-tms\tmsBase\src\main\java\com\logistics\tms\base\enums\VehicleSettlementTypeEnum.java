package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2019/11/11 9:29
 */
public enum VehicleSettlementTypeEnum {
    DEFAULT(0,""),
    GPS(10, "gps"),
    PARKING(20, "停车"),
    LOAN(30, "贷款"),
    TIRE(40, "轮胎"),
    OIL(50, "充油"),
    OIL_REFUND(51,"油费退款"),
    INSURANCE(60, "保险"),
    INSURANCE_REFUND(61, "退保"),
    CARRIER_ORDER(70, "运费"),
    ;

    private Integer key;
    private String value;

    VehicleSettlementTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
