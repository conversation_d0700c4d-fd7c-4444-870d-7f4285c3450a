package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/11/21 14:04
 */
public class ExportExtVehicleSettlement {
    private ExportExtVehicleSettlement(){}

    private static final Map<String, String> EXPORT_EXT_VEHICLE_SETTLEMENT;

    static {
        EXPORT_EXT_VEHICLE_SETTLEMENT = new LinkedHashMap<>();
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("支付状态", "payStatusDesc");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("运单号", "carrierOrderCode");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("需求单", "demandOrderCode");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("状态 ", "statusDesc");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("车辆机构", "vehicleProperty");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("车牌号", "vehicleNo");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("司机", "driverName");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("司机费用合计（元）", "driverTotalFee");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("实际结算数据", "settlementAmount");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("付款信息", "totalFee");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("实际签收时间", "signTime");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("发货地", "loadCityName");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("收货地 ", "unloadCityName");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("品名", "goodsName");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("规格", "goodsSize");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("调度", "dispatchUserName");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("车主", "companyCarrierName");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("货主", "companyEntrustName");
        EXPORT_EXT_VEHICLE_SETTLEMENT.put("运单生成时间", "dispatchTime");
    }

    public static Map<String, String> getExportExtVehicleSettlement() {
        return EXPORT_EXT_VEHICLE_SETTLEMENT;
    }
}
