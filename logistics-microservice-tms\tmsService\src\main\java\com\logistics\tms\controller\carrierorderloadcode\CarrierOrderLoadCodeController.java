package com.logistics.tms.controller.carrierorderloadcode;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.carrierorderloadcode.CarrierOrderLoadCodeBiz;
import com.logistics.tms.controller.carrierorderapplet.request.CarrierOrderIdRequestModel;
import com.logistics.tms.controller.carrierorderloadcode.request.*;
import com.logistics.tms.controller.carrierorderloadcode.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/*
  前台运单管理
 */
@RestController
public class CarrierOrderLoadCodeController {

    @Autowired
    private CarrierOrderLoadCodeBiz carrierOrderLoadCodeBiz;



    @ApiOperation(value = "提货-正确临时编码列表 (v2.46)")
    @PostMapping(value = "/service/carrierOrderLoadCode/correctLoadCodeList")
    public Result<PageInfo<CorrectLoadCodeListResponseModel>> correctLoadCodeList(@RequestBody @Valid CorrectLoadCodeListRequestModel requestModel) {
        return Result.success(carrierOrderLoadCodeBiz.correctLoadCodeList(requestModel));
    }

    @ApiOperation(value = "提货-有误临时编码列表 (v2.46)")
    @PostMapping(value = "/service/carrierOrderLoadCode/errorLoadCodeList")
    public Result<PageInfo<ErrorLoadCodeListResponseModel>> errorLoadCodeList(@RequestBody @Valid ErrorLoadCodeListRequestModel requestModel) {
        return Result.success(carrierOrderLoadCodeBiz.errorLoadCodeList(requestModel));
    }


    @ApiOperation(value = "提货-保存正确的临时编码 (v2.46)")
    @PostMapping(value = "/service/carrierOrderLoadCode/saveCorrectLoadCode")
    public Result<SaveCorrectLoadCodeResponseModel> saveCorrectLoadCode(@RequestBody @Valid SaveCorrectLoadCodeRequestModel requestModel) {
        return Result.success(carrierOrderLoadCodeBiz.saveCorrectLoadCode(requestModel));
    }


    @ApiOperation(value = "提货-保存错误的临时编码附件 (v2.46)")
    @PostMapping(value = "/service/carrierOrderLoadCode/saveErrorLoadCodeFile")
    public Result<SaveErrorLoadCodeFileResponseModel> saveErrorLoadCodeFile(@RequestBody @Valid SaveErrorLoadCodeFileRequestModel requestModel) {
        return Result.success(carrierOrderLoadCodeBiz.saveErrorLoadCodeFile(requestModel));
    }

    @ApiOperation(value = "提货-删除临时编码 (v2.46)")
    @PostMapping(value = "/service/carrierOrderLoadCode/deleteCarrierOrderLoadCode")
    public Result<Boolean> deleteCarrierOrderLoadCode(@RequestBody @Valid CarrierOrderLoadCodeIdRequestModel requestModel) {
        carrierOrderLoadCodeBiz.deleteCarrierOrderLoadCode(requestModel);
        return Result.success(true);
    }


    @ApiOperation(value = "提货详情(编码提货) (v2.46)")
    @PostMapping(value = "/service/carrierOrderLoadCode/codePickUpDetail")
    Result<CodePickUpDetailResponseModel> codePickUpDetail(@RequestBody @Valid CarrierOrderIdRequestModel requestModel){
        return Result.success(carrierOrderLoadCodeBiz.codePickUpDetail(requestModel));
    }


    @ApiOperation(value = "提货-有误临时编码查看 (v2.46)")
    @PostMapping(value = "/service/carrierOrderLoadCode/errorLoadCodeGetDetail")
    public Result<ErrorLoadCodeGetDetailResponseModel> errorLoadCodeGetDetail(@RequestBody @Valid ErrorLoadCodeGetDetailRequestModel requestModel){
        return Result.success(carrierOrderLoadCodeBiz.errorLoadCodeGetDetail(requestModel));
    }


}
