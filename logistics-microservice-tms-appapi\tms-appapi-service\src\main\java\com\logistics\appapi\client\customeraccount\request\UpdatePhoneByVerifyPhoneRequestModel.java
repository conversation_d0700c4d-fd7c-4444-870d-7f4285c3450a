package com.logistics.appapi.client.customeraccount.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/7/13 15:52
 */
@Data
public class UpdatePhoneByVerifyPhoneRequestModel {
    @ApiModelProperty(value = "用户账号")
    private String userAccount;
    @ApiModelProperty(value = "手机验证码")
    private String verificationCode;
    @ApiModelProperty(value = "验证码来源")
    private Integer codeSource;
    @ApiModelProperty(value = "验证码类型")
    private Integer codeType;

    @ApiModelProperty(value = "请求来源：1 司机（小程序），2 车主（前台）")
    private Integer requestSource = 2;
}
