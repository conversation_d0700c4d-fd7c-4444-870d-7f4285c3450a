package com.logistics.tms.biz.staff;

import com.logistics.tms.controller.staff.request.ImportStaffListRequestModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.customeraccount.CustomerAccountBiz;
import com.logistics.tms.controller.customeraccount.request.OpenAccountRequestModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
public class StaffTransBiz {
    @Autowired
    private TStaffBasicMapper tqStaffBasicMapper;
    @Autowired
    private TStaffDriverOccupationalRecordMapper tqStaffDriverOccupationalRecordMapper;
    @Autowired
    private TStaffDriverContinueLearningRecordMapper tqStaffDriverContinueLearningRecordMapper;
    @Autowired
    private TStaffDriverIntegrityExaminationRecordMapper tqStaffDriverIntegrityExaminationRecordMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TStaffDriverCredentialMapper staffDriverCredentialMapper;
    @Autowired
    private CustomerAccountBiz customerAccountBiz;
    @Autowired
    private TCarrierDriverRelationMapper tCarrierDriverRelationMapper;

    @Transactional
    public void importAddStaff(TStaffBasic tqStaffBasic, Long qiyaCompanyCarrierId, ImportStaffListRequestModel requestModel) {
        Long staffId;
        boolean ifOpenAccount = true;
        TStaffBasic staffBasic = null;
        if (tqStaffBasic == null) {
            staffBasic = new TStaffBasic();
            staffBasic.setType(requestModel.getType());
            staffBasic.setStaffProperty(requestModel.getStaffProperty());
            staffBasic.setGender(requestModel.getGender());
            staffBasic.setName(requestModel.getName());
            staffBasic.setMobile(requestModel.getMobile());
            staffBasic.setAge(requestModel.getAge());
            staffBasic.setIdentityNumber(requestModel.getIdentityNumber());
            staffBasic.setIdentityValidity(requestModel.getIdentityValidity());
            staffBasic.setIdentityIsForever(requestModel.getIdentityIsForever());
            staffBasic.setLaborContractNo(requestModel.getLaborContractNo());
            staffBasic.setLaborContractValidDate(requestModel.getLaborContractValidDate());
            //新增人员基本信息
            staffBasic.setOpenStatus(StaffBasicOpenStatusEnum.OPEN.getKey());
            staffBasic.setSource(CommonConstant.INTEGER_ONE);
            commonBiz.setBaseEntityAdd(staffBasic, BaseContextHandler.getUserName());
            tqStaffBasicMapper.insertSelective(staffBasic);
            staffId = staffBasic.getId();
            //新增账号
            if (StaffTypeEnum.SUPERCARGO.getKey().equals(requestModel.getType())) {
                ifOpenAccount = false;
            }
            TStaffDriverCredential staffDriverCredential = new TStaffDriverCredential();
            staffDriverCredential.setOccupationalRequirementsCredentialNo(requestModel.getOccupationalRequirementsCredentialNo());
            staffDriverCredential.setInitialIssuanceDate(requestModel.getInitialIssuanceDate());
            staffDriverCredential.setDriversLicenseNo(requestModel.getDriversLicenseNo());
            staffDriverCredential.setPermittedType(requestModel.getPermittedType());
            staffDriverCredential.setDriversLicenseDateFrom(requestModel.getDriversLicenseDateFrom());
            staffDriverCredential.setDriversLicenseDateTo(requestModel.getDriversLicenseDateTo());
            //新增司机证件信息
            staffDriverCredential.setStaffId(staffBasic.getId());
            commonBiz.setBaseEntityAdd(staffDriverCredential, BaseContextHandler.getUserName());
            staffDriverCredentialMapper.insertSelective(staffDriverCredential);

            if (requestModel.getOccupationalIssueDate() != null || requestModel.getOccupationalValidDate() != null) {
                TStaffDriverOccupationalRecord newOccupationalRecord = new TStaffDriverOccupationalRecord();
                newOccupationalRecord.setIssueDate(requestModel.getOccupationalIssueDate());
                newOccupationalRecord.setStaffId(staffBasic.getId());
                newOccupationalRecord.setValidDate(requestModel.getOccupationalValidDate());
                commonBiz.setBaseEntityAdd(newOccupationalRecord, BaseContextHandler.getUserName());
                tqStaffDriverOccupationalRecordMapper.insertSelective(newOccupationalRecord);
            }
            if (requestModel.getExaminationValidDate() != null) {
                TStaffDriverIntegrityExaminationRecord newExaminationRecord = new TStaffDriverIntegrityExaminationRecord();
                newExaminationRecord.setStaffId(staffBasic.getId());
                newExaminationRecord.setValidDate(requestModel.getExaminationValidDate());
                commonBiz.setBaseEntityAdd(newExaminationRecord, BaseContextHandler.getUserName());
                tqStaffDriverIntegrityExaminationRecordMapper.insertSelective(newExaminationRecord);
            }
            if (requestModel.getLearningValidDate() != null) {
                TStaffDriverContinueLearningRecord continueLearningRecord = new TStaffDriverContinueLearningRecord();
                continueLearningRecord.setStaffId(staffBasic.getId());
                continueLearningRecord.setValidDate(requestModel.getLearningValidDate());
                commonBiz.setBaseEntityAdd(continueLearningRecord, BaseContextHandler.getUserName());
                tqStaffDriverContinueLearningRecordMapper.insertSelective(continueLearningRecord);
            }
        } else {
            //判断车辆是否已被其他车主添加
            if (!StaffPropertyEnum.EXTERNAL_STAFF.getKey().equals(requestModel.getStaffProperty())) {
                //查询当前司机是否关联其他车主
                List<TCarrierDriverRelation> tCarrierDriverRelations = tCarrierDriverRelationMapper.queryByDriverIds(Collections.singletonList(tqStaffBasic.getId()));
                if (ListUtils.isNotEmpty(tCarrierDriverRelations)) {
                    throw new BizException(CarrierDataExceptionEnum.ONLY_OUR_COMPANY_EDIT_STAFF_PROPERTY);
                }
            }
            staffId = tqStaffBasic.getId();
        }
        //新增关联关系
        TCarrierDriverRelation tCarrierDriverRelation = new TCarrierDriverRelation();
        tCarrierDriverRelation.setCompanyCarrierId(qiyaCompanyCarrierId);
        tCarrierDriverRelation.setDriverId(staffId);
        tCarrierDriverRelation.setEnabled(EnabledEnum.ENABLED.getKey());
        commonBiz.setBaseEntityModify(tCarrierDriverRelation, BaseContextHandler.getUserName());
        tCarrierDriverRelationMapper.insertSelective(tCarrierDriverRelation);

        //如果身份是司机或则司机&&押运员，则需要给司机开通账号
        if (ifOpenAccount) {
            OpenAccountRequestModel openAccountRequestModel = new OpenAccountRequestModel();
            openAccountRequestModel.setType(CommonConstant.INTEGER_ONE);
            openAccountRequestModel.setMobile(requestModel.getMobile());
            openAccountRequestModel.setUserId(tCarrierDriverRelation.getId());
            openAccountRequestModel.setUserRole(AccountUserRoleTypeEnum.DRIVER_APPLET.getKey());
            openAccountRequestModel.setUserName(requestModel.getName());
            if (tqStaffBasic != null) {
                if (StaffBasicOpenStatusEnum.CLOSE.getKey().equals(tqStaffBasic.getOpenStatus())) {
                    openAccountRequestModel.setIfClose(CommonConstant.INTEGER_ONE);
                }
            } else {
                if (StaffBasicOpenStatusEnum.CLOSE.getKey().equals(staffBasic.getOpenStatus())) {
                    openAccountRequestModel.setIfClose(CommonConstant.INTEGER_ONE);
                }
            }
            customerAccountBiz.openOrCloseAccount(openAccountRequestModel);
        }
    }

    @Transactional
    public void importModifyStaff(TCarrierDriverRelation carrierDriverRelation, TStaffBasic tqStaffBasic, ImportStaffListRequestModel requestModel) {
        Long staffId = tqStaffBasic.getId();
        boolean ifOpenAccount = false;
        boolean ifDeleteAccount = false;
        TStaffBasic staffBasic = new TStaffBasic();
        staffBasic.setId(tqStaffBasic.getId());
        staffBasic.setStaffProperty(requestModel.getStaffProperty());
        staffBasic.setType(requestModel.getType());
        staffBasic.setGender(requestModel.getGender());
        staffBasic.setName(requestModel.getName());
        staffBasic.setMobile(requestModel.getMobile());
        staffBasic.setAge(requestModel.getAge());
        staffBasic.setIdentityNumber(requestModel.getIdentityNumber());
        staffBasic.setIdentityValidity(requestModel.getIdentityValidity());
        staffBasic.setIdentityIsForever(requestModel.getIdentityIsForever());
        staffBasic.setLaborContractNo(requestModel.getLaborContractNo());
        staffBasic.setLaborContractValidDate(requestModel.getLaborContractValidDate());
        //新增人员基本信息
        staffBasic.setSource(CommonConstant.INTEGER_ONE);
        commonBiz.setBaseEntityModify(staffBasic, BaseContextHandler.getUserName());
        tqStaffBasicMapper.updateByPrimaryKeySelective(staffBasic);

        //外部修改为自主/自营
        if (StaffPropertyEnum.EXTERNAL_STAFF.getKey().equals(tqStaffBasic.getStaffProperty()) && !StaffPropertyEnum.EXTERNAL_STAFF.getKey().equals(requestModel.getStaffProperty())) {
            //查询当前司机是否关联其他车主
            List<TCarrierDriverRelation> tCarrierDriverRelations = tCarrierDriverRelationMapper.queryByDriverIds(Collections.singletonList(tqStaffBasic.getId()));
            if (tCarrierDriverRelations.size() > CommonConstant.INTEGER_ONE) {
                throw new BizException(CarrierDataExceptionEnum.ONLY_OUR_COMPANY_EDIT_STAFF_PROPERTY);
            }
        }

        if (StaffTypeEnum.SUPERCARGO.getKey().equals(tqStaffBasic.getType()) || StaffTypeEnum.DEFAULT.getKey().equals(tqStaffBasic.getType())) {//原来是押运员或无类型
            if (StaffTypeEnum.DRIVER.getKey().equals(requestModel.getType()) || StaffTypeEnum.DRIVER_SUPERCARGO.getKey().equals(requestModel.getType())) {//修改为司机或司机&押运员，则为手机号开通登录账号
                ifOpenAccount = true;
            }
        } else if (StaffTypeEnum.DRIVER.getKey().equals(tqStaffBasic.getType()) || StaffTypeEnum.DRIVER_SUPERCARGO.getKey().equals(tqStaffBasic.getType())) {//原来是司机或司机&押运员
            if (StaffTypeEnum.SUPERCARGO.getKey().equals(requestModel.getType())) {//修改为押运员后，需删除账号
                ifDeleteAccount = true;
            } else {//类型没变
                if (!tqStaffBasic.getName().equals(requestModel.getName())) {//修改了名字，同步修改账号名字
                    ifOpenAccount = true;
                }
                if (!tqStaffBasic.getMobile().equals(requestModel.getMobile())) {//手机号改了，删除原手机号登录账号，为新手机号开通登录账号
                    ifDeleteAccount = true;
                    ifOpenAccount = true;
                }
            }
        }

        TStaffDriverCredential staffDriverCredential = new TStaffDriverCredential();
        staffDriverCredential.setOccupationalRequirementsCredentialNo(requestModel.getOccupationalRequirementsCredentialNo());
        staffDriverCredential.setInitialIssuanceDate(requestModel.getInitialIssuanceDate());
        staffDriverCredential.setDriversLicenseNo(requestModel.getDriversLicenseNo());
        staffDriverCredential.setPermittedType(requestModel.getPermittedType());
        staffDriverCredential.setDriversLicenseDateFrom(requestModel.getDriversLicenseDateFrom());
        staffDriverCredential.setDriversLicenseDateTo(requestModel.getDriversLicenseDateTo());
        //新增司机证件信息
        staffDriverCredential.setStaffId(staffBasic.getId());
        commonBiz.setBaseEntityModify(staffDriverCredential, BaseContextHandler.getUserName());
        staffDriverCredentialMapper.updateByStaffIdSelective(staffDriverCredential);
        if ((requestModel.getOccupationalIssueDate() != null || requestModel.getOccupationalValidDate() != null) && tqStaffDriverOccupationalRecordMapper.selectRecordByStaffIdAndValidDate(staffId, requestModel.getOccupationalValidDate(), requestModel.getOccupationalIssueDate()) == 0) {
            TStaffDriverOccupationalRecord newOccupationalRecord = new TStaffDriverOccupationalRecord();
            newOccupationalRecord.setIssueDate(requestModel.getOccupationalIssueDate());
            newOccupationalRecord.setStaffId(staffId);
            newOccupationalRecord.setValidDate(requestModel.getOccupationalValidDate());
            commonBiz.setBaseEntityAdd(newOccupationalRecord, BaseContextHandler.getUserName());
            tqStaffDriverOccupationalRecordMapper.insertSelective(newOccupationalRecord);
        }
        if (requestModel.getExaminationValidDate() != null && tqStaffDriverIntegrityExaminationRecordMapper.selectRecordByStaffIdAndValidDate(staffId, requestModel.getExaminationValidDate()) == 0) {
            TStaffDriverIntegrityExaminationRecord newExaminationRecord = new TStaffDriverIntegrityExaminationRecord();
            newExaminationRecord.setStaffId(staffId);
            newExaminationRecord.setValidDate(requestModel.getExaminationValidDate());
            commonBiz.setBaseEntityAdd(newExaminationRecord, BaseContextHandler.getUserName());
            tqStaffDriverIntegrityExaminationRecordMapper.insertSelective(newExaminationRecord);
        }
        if (requestModel.getLearningValidDate() != null && tqStaffDriverContinueLearningRecordMapper.selectRecordByStaffIdAndValidDate(staffId, requestModel.getLearningValidDate()) == 0) {
            TStaffDriverContinueLearningRecord continueLearningRecord = new TStaffDriverContinueLearningRecord();
            continueLearningRecord.setStaffId(staffId);
            continueLearningRecord.setValidDate(requestModel.getLearningValidDate());
            commonBiz.setBaseEntityAdd(continueLearningRecord, BaseContextHandler.getUserName());
            tqStaffDriverContinueLearningRecordMapper.insertSelective(continueLearningRecord);
        }

        //对司机登录账号进行处理
        if (ifDeleteAccount) {
            OpenAccountRequestModel openAccountRequestModel = new OpenAccountRequestModel();
            openAccountRequestModel.setType(CommonConstant.INTEGER_THREE);
            openAccountRequestModel.setUserId(carrierDriverRelation.getId());
            openAccountRequestModel.setUserRole(AccountUserRoleTypeEnum.DRIVER_APPLET.getKey());
            customerAccountBiz.openOrCloseAccount(openAccountRequestModel);
        }
        if (ifOpenAccount) {
            OpenAccountRequestModel openAccountRequestModel = new OpenAccountRequestModel();
            openAccountRequestModel.setType(CommonConstant.INTEGER_ONE);
            openAccountRequestModel.setMobile(requestModel.getMobile());
            openAccountRequestModel.setUserId(carrierDriverRelation.getId());
            openAccountRequestModel.setUserRole(AccountUserRoleTypeEnum.DRIVER_APPLET.getKey());
            openAccountRequestModel.setUserName(requestModel.getName());
            if(StaffBasicOpenStatusEnum.CLOSE.getKey().equals(tqStaffBasic.getOpenStatus())){
                openAccountRequestModel.setIfClose(CommonConstant.INTEGER_ONE);
            }
            customerAccountBiz.openOrCloseAccount(openAccountRequestModel);
        }
    }
}
