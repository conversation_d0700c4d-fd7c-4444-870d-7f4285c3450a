package com.logistics.management.webapi.client.thirdparty.basicdata.user.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2023/12/22 15:20
 */
@Data
public class GetUserInfoListResponseModel {
    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("用户登录账户")
    private String userAccount;

    @ApiModelProperty("1启用 0禁用")
    private Integer enabled;

    @ApiModelProperty("手机号")
    private String mobilePhone;
}
