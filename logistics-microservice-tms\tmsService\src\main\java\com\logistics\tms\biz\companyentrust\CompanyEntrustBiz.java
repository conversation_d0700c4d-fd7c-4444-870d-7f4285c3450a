package com.logistics.tms.biz.companyentrust;

import com.logistics.tms.controller.companyentrust.request.AddOrModifyCompanyEntrustRequestModel;
import com.logistics.tms.controller.companyentrust.request.CompanyEntrustIdRequestModel;
import com.logistics.tms.controller.companyentrust.request.SearchCompanyEntrustByNameRequestModel;
import com.logistics.tms.controller.companyentrust.request.SearchCompanyEntrustRequestModel;
import com.logistics.tms.controller.companyentrust.response.CompanyEntrustDetailResponseModel;
import com.logistics.tms.controller.companyentrust.response.SearchCompanyEntrustByNameResponseModel;
import com.logistics.tms.controller.companyentrust.response.SearchCompanyEntrustResponseModel;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tray.core.exception.BizException;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.CopyFileTypeEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TCompany;
import com.logistics.tms.entity.TCompanyEntrust;
import com.logistics.tms.mapper.TCompanyEntrustMapper;
import com.logistics.tms.mapper.TCompanyMapper;
import com.yelo.tools.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/9/19 10:28
 */
@Service
public class CompanyEntrustBiz {

    @Autowired
    private TCompanyEntrustMapper companyEntrustMapper;
    @Autowired
    private TCompanyMapper companyMapper;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 获取委托方公司列表
     * @param requestModel
     * @return
     */
    public List<SearchCompanyEntrustResponseModel> searchList(SearchCompanyEntrustRequestModel requestModel) {
        return companyEntrustMapper.searchList(requestModel);
    }

    /**
     * 查看详情
     * @param requestModel
     * @return
     */
    public CompanyEntrustDetailResponseModel getDetail(CompanyEntrustIdRequestModel requestModel) {
        return companyEntrustMapper.getDetail(requestModel.getCompanyEntrustId());
    }

    /**
     * 添加/修改公司
     * @param requestModel
     */
    @Transactional
    public void saveCompany(AddOrModifyCompanyEntrustRequestModel requestModel) {
        TCompanyEntrust companyEntrust = new TCompanyEntrust();
        companyEntrust.setSettlementTonnage(requestModel.getSettlementTonnage());
        companyEntrust.setCompanyShortName(requestModel.getCompanyShortName());
        companyEntrust.setIfAudit(requestModel.getIfAudit());
        companyEntrust.setType(requestModel.getType());
        companyEntrust.setSignMode(requestModel.getSignMode());
        companyEntrust.setRemark(requestModel.getRemark());

        TCompany company = new TCompany();
        company.setCompanyName(requestModel.getCompanyName());
        company.setTradingCertificateIsAmend(requestModel.getTradingCertificateIsAmend());
        company.setTradingCertificateIsForever(requestModel.getTradingCertificateIsForever());
        company.setTradingCertificateValidityTime(requestModel.getTradingCertificateValidityTime());

        Long companyId;
        TCompany tCompany = companyMapper.getByName(requestModel.getCompanyName());
        if (tCompany != null){
            if (StringUtils.isNotBlank(requestModel.getFileSrcPathTradingCertificateImage())){
                if (!requestModel.getFileSrcPathTradingCertificateImage().equals(tCompany.getTradingCertificateImage())){
                    company.setTradingCertificateImage(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.COMPANY_ENTRUST_TRADING.getKey(),"",requestModel.getFileSrcPathTradingCertificateImage(),null));
                }
            }else{
                company.setTradingCertificateImage("");
            }
            company.setId(tCompany.getId());
            commonBiz.setBaseEntityModify(company,BaseContextHandler.getUserName());
            companyMapper.updateByPrimaryKeySelectiveForTime(company);
            companyId = tCompany.getId();
        }else{
            if (StringUtils.isNotBlank(requestModel.getFileSrcPathTradingCertificateImage())){
                company.setTradingCertificateImage(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.COMPANY_ENTRUST_TRADING.getKey(),"",requestModel.getFileSrcPathTradingCertificateImage(),null));
            }
            commonBiz.setBaseEntityAdd(company, BaseContextHandler.getUserName());
            companyMapper.insertSelective(company);
            companyId = company.getId();
        }
        companyEntrust.setCompanyId(companyId);

        if (requestModel.getCompanyEntrustId() != null && requestModel.getCompanyEntrustId() > CommonConstant.LONG_ZERO){//修改

            TCompanyEntrust dbTCompanyEntrust = companyEntrustMapper.selectByPrimaryKey(requestModel.getCompanyEntrustId());
            if(dbTCompanyEntrust == null){
                throw new BizException(CarrierDataExceptionEnum.COMPANY_ENTRUST_EMPTY);
            }
            if(tCompany!=null && !dbTCompanyEntrust.getCompanyId().equals(tCompany.getId())){
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_REPEAT);
            }

            companyEntrust.setId(requestModel.getCompanyEntrustId());
            commonBiz.setBaseEntityModify(companyEntrust,BaseContextHandler.getUserName());
            companyEntrustMapper.updateByPrimaryKeySelective(companyEntrust);
        }else{//新增
            if(tCompany != null){
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_REPEAT);
            }
            commonBiz.setBaseEntityAdd(companyEntrust,BaseContextHandler.getUserName());
            companyEntrustMapper.insertSelective(companyEntrust);
        }
    }

    /**
     * 根据公司名称模糊查询委托方公司
     * @param requestModel
     * @return
     */
    public List<SearchCompanyEntrustByNameResponseModel> searchCompanyEntrustByName(SearchCompanyEntrustByNameRequestModel requestModel){
        return companyEntrustMapper.searchCompanyEntrustByName(requestModel.getCompanyName());
    }
}
