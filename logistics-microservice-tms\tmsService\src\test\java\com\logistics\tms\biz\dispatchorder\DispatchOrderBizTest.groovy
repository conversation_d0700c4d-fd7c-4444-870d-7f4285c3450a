package com.logistics.tms.biz.dispatchorder


import com.logistics.tms.controller.dispatchorder.request.DispatchOrderCarrierRequestModel
import com.logistics.tms.controller.dispatchorder.request.DispatchOrderSearchRequestModel
import com.logistics.tms.controller.dispatchorder.response.CarrierOrderGoodsResponseModel
import com.logistics.tms.controller.dispatchorder.response.DispatchOrderCarrierChildResponseModel
import com.logistics.tms.controller.dispatchorder.response.DispatchOrderDetailResponseModel
import com.logistics.tms.controller.dispatchorder.response.DispatchOrderSearchResponseModel
import com.logistics.tms.mapper.TCarrierOrderGoodsMapper
import com.logistics.tms.mapper.TCarrierOrderMapper
import com.logistics.tms.mapper.TDispatchOrderMapper
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class DispatchOrderBizTest extends Specification {
    @Mock
    TDispatchOrderMapper dispatchOrderMapper
    @Mock
    TCarrierOrderMapper carrierOrderMapper
    @Mock
    TCarrierOrderGoodsMapper carrierOrderGoodsMapper
    @Mock
    Logger log
    @InjectMocks
    DispatchOrderBiz dispatchOrderBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(dispatchOrderMapper.searchIdList(any())).thenReturn([1l])
        when(dispatchOrderMapper.searchList(anyString())).thenReturn([new DispatchOrderSearchResponseModel()])

        expect:
        dispatchOrderBiz.searchList(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new DispatchOrderSearchRequestModel() || null
    }

    @Unroll
    def "get Carrier Order where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(carrierOrderMapper.getCarrierOrderByDispatchOrderId(any())).thenReturn([new DispatchOrderCarrierChildResponseModel()])

        expect:
        dispatchOrderBiz.getCarrierOrder(requestModel) == expectedResult

        where:
        requestModel                           || expectedResult
        new DispatchOrderCarrierRequestModel() || [new DispatchOrderCarrierChildResponseModel()]
    }

    @Unroll
    def "get Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(dispatchOrderMapper.getById(anyLong())).thenReturn(new DispatchOrderDetailResponseModel())
        when(carrierOrderGoodsMapper.getByDispatchOrderId(anyLong())).thenReturn([new CarrierOrderGoodsResponseModel()])

        expect:
        dispatchOrderBiz.getDetail(requestModel) == expectedResult

        where:
        requestModel                           || expectedResult
        new DispatchOrderCarrierRequestModel() || new DispatchOrderDetailResponseModel()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme