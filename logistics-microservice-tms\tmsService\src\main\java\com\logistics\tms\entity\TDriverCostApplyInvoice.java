package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2023/07/31
*/
@Data
public class TDriverCostApplyInvoice extends BaseEntity {
    /**
    * 司机费用申请ID
    */
    @ApiModelProperty("司机费用申请ID")
    private Long driverCostApplyId;

    /**
    * 1 增值税，2 出租车，3 火车票，4 定额，5 卷票，6 机打，7 过路
    */
    @ApiModelProperty("1 增值税，2 出租车，3 火车票，4 定额，5 卷票，6 机打，7 过路")
    private Integer type;

    /**
    * 发票名称
    */
    @ApiModelProperty("发票名称")
    private String invoiceName;

    /**
    * 票据类型
    */
    @ApiModelProperty("票据类型")
    private String invoiceType;

    /**
    * 发票代码
    */
    @ApiModelProperty("发票代码")
    private String invoiceCode;

    /**
    * 发票号码
    */
    @ApiModelProperty("发票号码")
    private String invoiceNum;

    /**
    * 合计金额 
    */
    @ApiModelProperty("合计金额 ")
    private BigDecimal totalPrice;

    /**
    * 合计税额 
    */
    @ApiModelProperty("合计税额 ")
    private BigDecimal totalTax;

    /**
    * 价税合计
    */
    @ApiModelProperty("价税合计")
    private BigDecimal totalTaxAndPrice;
}