package com.logistics.tms.controller.vehicletype.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ImportVehicleTypeResponseModel {
    @ApiModelProperty("成功数量")
    private Integer successNumber = 0;
    @ApiModelProperty("失败数量")
    private Integer failuresNumber = 0;

    public void addSuccessful() {
        this.successNumber++;
    }
    public void addFailures() {
        this.failuresNumber++;
    }

    public void initNumber( Integer initFailuresNumber) {
        if (initFailuresNumber != null) {
            this.failuresNumber = this.failuresNumber + initFailuresNumber;
        }
    }
}
