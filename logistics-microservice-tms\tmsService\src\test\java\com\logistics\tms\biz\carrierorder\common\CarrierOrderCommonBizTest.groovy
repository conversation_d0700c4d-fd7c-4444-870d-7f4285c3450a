package com.logistics.tms.biz.carrierorder.common

import com.logistics.tms.controller.carrierorder.response.TicketsModel
import com.logistics.tms.api.feign.entrustsettlement.model.ModifyCarrierSettlementCostModel
import com.logistics.tms.biz.carrierorder.model.GetSettlementByDemandOrderIdModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.controller.carrierorder.request.GetCarrierOrdersByDemandIdRequestModel
import com.logistics.tms.controller.carrierorder.response.CarrierOrderListBeforeSignUpResponseModel
import com.logistics.tms.controller.carrierorder.response.DemandOrderCarrierDetailResponseModel
import com.logistics.tms.controller.carrierorder.response.GetCarrierOrderWeixinPushResponseModel
import com.logistics.tms.controller.demandorder.response.CreateSettlementForCarrierConsumerModel
import com.logistics.tms.entity.*
import com.logistics.tms.mapper.*
import com.yelo.tools.rabbitmq.producer.RabbitMqSender
import com.yelo.tray.core.exception.BizException
import org.codehaus.jackson.map.ObjectMapper
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class CarrierOrderCommonBizTest extends Specification {
    @Mock
    TCarrierOrderVehicleHistoryMapper tCarrierOrderVehicleHistoryMapper
    @Mock
    RabbitMqSender rabbitMqSender
    @Mock
    ObjectMapper jsonWriter
    @Mock
    TCarrierOrderGoodsMapper tCarrierOrderGoodsMapper
    @Mock
    TCarrierOrderWxMapper tCarrierOrderWxMapper
    @Mock
    TCarrierOrderMapper tCarrierOrderMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TPaymentMapper paymentMapper
    @Mock
    TReceivementMapper receivementMapper
    @Mock
    TCarrierOrderOperateLogsMapper carrierOrderOperateLogsMapper
    @Mock
    TVehicleSettlementRelationMapper tVehicleSettlementRelationMapper
    @Mock
    TCarrierOrderTicketsMapper tCarrierOrderTicketsMapper
    @Mock
    TCarrierOrderAddressMapper tCarrierOrderAddressMapper
    @Mock
    Logger log
    @InjectMocks
    CarrierOrderCommonBiz carrierOrderCommonBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "exist Settlement Data where carrierOrderIds=#carrierOrderIds then expect: #expectedResult"() {
        given:
        when(tVehicleSettlementRelationMapper.getByObjectIdsAndType(anyInt(), anyString())).thenReturn([new TVehicleSettlementRelation()])

        expect:
        carrierOrderCommonBiz.existSettlementData(carrierOrderIds) == expectedResult

        where:
        carrierOrderIds   || expectedResult
        "carrierOrderIds" || true
    }

    @Unroll
    def "check If Carrier Orders Status Can Change where carrierOrderIdList=#carrierOrderIdList and exceptionEnum=#exceptionEnum"() {
        given:
        when(tCarrierOrderVehicleHistoryMapper.selectWaitAuditCarrierOrders(any())).thenReturn(null)

        expect:
        carrierOrderCommonBiz.checkIfCarrierOrdersStatusCanChange(carrierOrderIdList, null)
        assert expectedResult == true

        where:
        carrierOrderIdList || expectedResult
        null              || true
    }

    @Unroll
    def "wx Push where orderParam=#orderParam"() {
        given:
        when(tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderGoods(goodsName: "goodsName", length: 0, width: 0, height: 0, goodsSize: "goodsSize")])
        when(tCarrierOrderWxMapper.selectCarrierOrderWeixinPushInfoByCarrierOrder(anyLong())).thenReturn([new GetCarrierOrderWeixinPushResponseModel()])

        expect:
        carrierOrderCommonBiz.wxPush(orderParam)
        assert expectedResult == true

        where:
        orderParam                                                                                                                                   || expectedResult
        null || true
    }

    @Unroll
    def "batch Wx Push where tCarrierOrders=#tCarrierOrders"() {
        given:
        when(tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderGoods(goodsName: "goodsName", length: 0, width: 0, height: 0, goodsSize: "goodsSize")])
        when(tCarrierOrderWxMapper.selectCarrierOrderWeixinPushInfoByCarrierOrder(anyLong())).thenReturn([new GetCarrierOrderWeixinPushResponseModel()])

        expect:
        carrierOrderCommonBiz.batchWxPush(tCarrierOrders)
        assert expectedResult == true

        where:
        tCarrierOrders                                                                                                                                 || expectedResult
        null|| true
    }

    @Unroll
    def "single Wx Push where tCarrierOrder=#tCarrierOrder"() {
        given:
        when(tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderGoods(goodsName: "goodsName", length: 0, width: 0, height: 0, goodsSize: "goodsSize")])
        when(tCarrierOrderWxMapper.selectCarrierOrderWeixinPushInfoByCarrierOrder(anyLong())).thenReturn([new GetCarrierOrderWeixinPushResponseModel()])

        expect:
        carrierOrderCommonBiz.singleWxPush(tCarrierOrder)
        assert expectedResult == true

        where:
        tCarrierOrder || expectedResult
        null          || true
    }

    @Unroll
    def "check Carrier Order If Exist where carrierOrderId=#carrierOrderId then expect: #expectedResult"() {
        given:
        when(tCarrierOrderMapper.selectCarrierOrdersByIds(anyString())).thenReturn([new TCarrierOrder()])

        expect:
        carrierOrderCommonBiz.checkCarrierOrderIfExist(carrierOrderId).size() == expectedResult

        where:
        carrierOrderId || expectedResult
        [1l]           || 1
    }

    @Unroll
    def "create Settlement where settlementList=#settlementList"() {
        given:
        when(paymentMapper.batchInsert(any())).thenReturn(0)
        when(receivementMapper.batchInsert(any())).thenReturn(0)

        expect:
        carrierOrderCommonBiz.createSettlement(settlementList)
        assert expectedResult == true

        where:
        settlementList                                  || expectedResult
        [new CreateSettlementForCarrierConsumerModel()] || true
    }

    @Unroll
    def "modify Settlement Cost where model=#model"() {
        given:
        when(tCarrierOrderMapper.selectCarrierOrderSignDetail(anyString(), anyString())).thenReturn([new CarrierOrderListBeforeSignUpResponseModel()])
        when(paymentMapper.batchUpdate(any())).thenReturn(0)
        when(paymentMapper.getSettlementByDemandOrderId(anyLong())).thenReturn([new GetSettlementByDemandOrderIdModel()])
        when(receivementMapper.batchUpdate(any())).thenReturn(0)
        when(receivementMapper.getSettlementByDemandOrderId(anyLong())).thenReturn([new GetSettlementByDemandOrderIdModel()])
        when(carrierOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)

        when:
        carrierOrderCommonBiz.modifySettlementCost(model)
        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }
        where:
        model                                  || expectedResult
        new ModifyCarrierSettlementCostModel() || "运单结算信息不存在"
    }

    @Unroll
    def "get Carrier Order Info By Demand Id where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCarrierOrderVehicleHistoryMapper.getAllByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderVehicleHistory(carrierOrderId: 1l, auditStatus: 0)])
        when(tCarrierOrderMapper.getCarrierOrderInfoByDemandId(anyLong())).thenReturn([new DemandOrderCarrierDetailResponseModel()])
        when(tCarrierOrderTicketsMapper.getTicketsByCarrierOrderIds(anyString())).thenReturn([new TicketsModel()])
        when(tCarrierOrderAddressMapper.getByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderAddress(carrierOrderId: 1l, unloadProvinceName: "unloadProvinceName", unloadCityName: "unloadCityName", unloadAreaName: "unloadAreaName", unloadDetailAddress: "unloadDetailAddress", unloadWarehouse: "unloadWarehouse", unloadAddressIsAmend: 0, receiverName: "receiverName", receiverMobile: "receiverMobile")])

        expect:
        carrierOrderCommonBiz.getCarrierOrderInfoByDemandId(requestModel).size()  == expectedResult

        where:
        requestModel                                 || expectedResult
        new GetCarrierOrdersByDemandIdRequestModel() || 0
    }

    @Unroll
    def "get Volume where amount=#amount and length=#length and width=#width and height=#height then expect: #expectedResult"() {
        expect:
        carrierOrderCommonBiz.getVolume(amount, length, width, height) == expectedResult

        where:
        amount          | length | width | height || expectedResult
        0 as BigDecimal | 0      | 0     | 0      || 0 as BigDecimal
    }

    @Unroll
    def "carrier Order Mileage where carrierOrderIds=#carrierOrderIds"() {
        given:
        when(tCarrierOrderMapper.batchUpdateCarrierOrders(any())).thenReturn(0)
        when(commonBiz.getMileageByLonAndLat(anyString(), anyString())).thenReturn(0 as BigDecimal)
        when(tCarrierOrderAddressMapper.getByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderAddress(carrierOrderId: 1l, loadLongitude: "loadLongitude", loadLatitude: "loadLatitude", unloadLongitude: "unloadLongitude", unloadLatitude: "unloadLatitude")])

        expect:
        carrierOrderCommonBiz.carrierOrderMileage(carrierOrderIds)
        assert expectedResult == true

        where:
        carrierOrderIds || expectedResult
        [1l]            || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme