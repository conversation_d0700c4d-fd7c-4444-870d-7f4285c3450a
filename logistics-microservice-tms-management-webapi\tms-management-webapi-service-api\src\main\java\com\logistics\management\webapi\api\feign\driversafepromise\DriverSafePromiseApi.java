package com.logistics.management.webapi.api.feign.driversafepromise;
import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.driversafepromise.dto.*;
import com.logistics.management.webapi.api.feign.driversafepromise.hystrix.DriverSafePromiseApiHystrix;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import javax.validation.Valid;
/**
 * @Author: sj
 * @Date: 2019/11/4 10:04
 */
@Api(value = "API-DriverSafePromiseApi-司机安全承诺书")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = DriverSafePromiseApiHystrix.class)
public interface DriverSafePromiseApi {

    @ApiOperation("列表")
    @PostMapping(value = "/api/safePromise/searchList")
    Result<PageInfo<SearchSafePromiseListResponseDto>> searchList(@RequestBody SearchSafePromiseListRequestDto requestDto);

    @ApiOperation("新增")
    @PostMapping(value = "/api/safePromise/addSafePromise")
    Result<Boolean> addSafePromise(@RequestBody @Valid AddSafePromiseRequestDto requestDto);

    @ApiOperation("详情")
    @PostMapping(value = "/api/safePromise/getDetail")
    Result<SafePromiseDetailResponseDto> getDetail(@RequestBody @Valid SafePromiseDetailRequestDto requestDto);

    @ApiOperation("删除")
    @PostMapping(value = "/api/safePromise/delSafePromise")
    Result<Boolean> delSafePromise(@RequestBody @Valid DeleteSafePromiseRequestDto requestDto);

    @ApiOperation("补发")
    @PostMapping(value = "/api/safePromise/reissueSafePromise")
    Result<Boolean> reissueSafePromise(@RequestBody @Valid ReissueSavePromiseRequestDto requestDto);

    @ApiOperation("签订列表v1.1.7")
    @PostMapping(value = "/api/safePromise/searchSignList")
    Result<PageInfo<SearchSignSafePromiseListResponseDto>> searchSignList(@RequestBody SearchSignSafePromiseListRequestDto requestDto);

    @ApiOperation("签订详情")
    @PostMapping(value = "/api/safePromise/getSignDetail")
    Result<SignSafePromiseDetailResponseDto> getSignDetail(@RequestBody SignSafePromiseDetailRequestDto requestDto);

    @ApiOperation("签订列表汇总v1.1.7")
    @PostMapping(value = "/api/safePromise/getSignSummary")
    Result<SummarySignSafePromiseResponseDto> getSignSummary(@RequestBody SearchSignSafePromiseListRequestDto responseDto);

    @ApiOperation("签订列表-上传/重新上传")
    @PostMapping(value = "/api/safePromise/uploadSafePromise")
    Result<Boolean> uploadSafePromise(@RequestBody @Valid UploadSafePromiseRequestDto requestDto);
}
