package com.logistics.tms.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author:lei.zhu
 * @date:2021/4/9 13:40
 */
@Data
public class SettleStatementRecordItemModel {
    @ApiModelProperty("打款公司")
    private String payCompany;
    @ApiModelProperty("收款人")
    private String receiverName;
    @ApiModelProperty("打款金额")
    private BigDecimal payFee;
    @ApiModelProperty("打款时间")
    private Date payTime;
}
