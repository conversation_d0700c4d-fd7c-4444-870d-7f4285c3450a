package com.logistics.appapi.client.carrierorderotherfee.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/1/19 9:54
 */
@Data
public class GetOtherFeeByCarrierOrderIdResponseModel {
    @ApiModelProperty("临时费用id")
    private Long carrierOrderOtherFeeId;

    @ApiModelProperty("临时费用")
    private List<GetOtherFeeItemByCarrierOrderIdResponseModel> otherFeeList;

    @ApiModelProperty(value = "合计临时费用")
    private BigDecimal totalAmount;
}
