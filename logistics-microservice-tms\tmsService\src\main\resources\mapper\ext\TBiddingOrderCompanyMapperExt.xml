<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TBiddingOrderCompanyMapper" >
  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TBiddingOrderCompany" >
    <foreach collection="recordList" item="item" separator=";">
      insert into t_bidding_order_company
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.biddingOrderId != null" >
          bidding_order_id,
        </if>
        <if test="item.companyCarrierId != null" >
          company_carrier_id,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.biddingOrderId != null" >
          #{item.biddingOrderId,jdbcType=BIGINT},
        </if>
        <if test="item.companyCarrierId != null" >
          #{item.companyCarrierId,jdbcType=BIGINT},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

  <select id="getByCompanyCarrierIdBiddingOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_bidding_order_company
    where valid = 1
    <if test="companyCarrierId != null">
      and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
    </if>
    <if test="biddingOrderId != null">
      and bidding_order_id = #{biddingOrderId,jdbcType=BIGINT}
    </if>
  </select>
</mapper>