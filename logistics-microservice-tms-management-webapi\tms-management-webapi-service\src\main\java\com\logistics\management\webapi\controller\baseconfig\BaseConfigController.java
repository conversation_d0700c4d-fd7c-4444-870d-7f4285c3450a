package com.logistics.management.webapi.controller.baseconfig;

import com.logistics.management.webapi.base.enums.ConfigKeyEnum;
import com.logistics.management.webapi.client.sysconfig.SysConfigClient;
import com.logistics.management.webapi.client.sysconfig.request.SysConfigRequestModel;
import com.logistics.management.webapi.controller.baseconfig.response.GetOwnCompanyNameResponseDto;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/10/29
 * @description:
 */
@Api(value = "API - BASE-CONFIG-基础配合")
@RestController
public class BaseConfigController{

    @Resource
    private SysConfigClient sysConfigClient;

    /**
     * 获取自有公司名字
     * @return
     */
    @ApiOperation(value = "获取自有公司名字")
    @PostMapping(value = "/api/baseConfig/getOwnCompanyName")
    public Result<GetOwnCompanyNameResponseDto> getOwnCompanyName() {

        ConfigKeyEnum qiyaCompanyNameEnum = ConfigKeyEnum.QIYA_COMPANY_NAME;

        SysConfigRequestModel requestModel = new SysConfigRequestModel();
        requestModel.setGroupCode(qiyaCompanyNameEnum.getGroupCode());
        requestModel.setConfigKey(qiyaCompanyNameEnum.getValue());
        Result<String> result = sysConfigClient.getSysConfig(requestModel);
        result.throwException();
        GetOwnCompanyNameResponseDto responseDto = new GetOwnCompanyNameResponseDto();
        if (StringUtils.isNotBlank(result.getData())) {
            responseDto.setCompanyName(result.getData());
        }
        return Result.success(responseDto);
    }
}
