<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TReserveApplyMapper">
    <sql id="Base_Column_List_Decrypt">
        id, reserve_apply_code, status, type, staff_id, staff_name, staff_mobile, staff_property,
        AES_DECRYPT(UNHEX(receive_bank_account), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') receive_bank_account,
        receive_bank_account_name, receive_bra_bank_name, apply_amount,
        apply_time, apply_remark, approved_amount, auditor_name_one, audit_time_one, audit_remark_one,
        auditor_name_two, audit_time_two, audit_remark_two, pay_time, running_code, payment_remark,
        reject_remark, cancel_remark, balance_amount, created_by, created_time, last_modified_by,
        last_modified_time, valid
    </sql>

    <select id="selectReserveApplyList" resultType="com.logistics.tms.controller.reserveapply.response.ReserveApplyListResponseModel">
        SELECT id applyId,
        reserve_apply_code applyCode,
        status ,
        type,
        staff_name driverName,
        staff_mobile driverMobile,
        AES_DECRYPT(UNHEX(receive_bank_account), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') receiveAccount,
        receive_bank_account_name receiveBankAccountName,
        receive_bra_bank_name receiveBraBankName,
        apply_amount applyAmount,
        approved_amount approveAmount,
        (CASE status
        WHEN -1 THEN cancel_remark
        WHEN 0 THEN apply_remark
        WHEN 1 THEN audit_remark_one
        WHEN 2 THEN reject_remark
        WHEN 3 THEN audit_remark_two
        WHEN 4 THEN payment_remark
        ELSE '' END ) remark
        FROM t_reserve_apply
        WHERE valid = 1
        <if test="params.status != null ">
            AND status = #{params.status}
        </if>
        <if test="params.applyCode != null and params.applyCode != ''">
            AND INSTR(reserve_apply_code, #{params.applyCode})
        </if>
        <if test="params.driver != null and params.driver != ''">
            AND (INSTR(staff_name, #{params.driver}) or
            INSTR(staff_mobile, #{params.driver}))
        </if>
        <if test="params.staffProperty != null">
            AND staff_property = #{params.staffProperty}
        </if>
        <if test="params.applyTimeStart != null and params.applyTimeStart != ''">
            <if test="params.applyTimeEnd != null and params.applyTimeEnd != ''">
                AND (apply_time >= DATE_FORMAT(#{params.applyTimeStart}, '%Y-%m-%d 00:00:00' )
                AND apply_time &lt;= DATE_FORMAT(#{params.applyTimeEnd}, '%Y-%m-%d 23:59:59' ))
            </if>
        </if>
        <if test="params.financialAuditTimeStart != null and params.financialAuditTimeStart != ''">
            <if test="params.financialAuditTimeEnd != null and params.financialAuditTimeEnd != ''">
                AND (audit_time_two >= DATE_FORMAT(#{params.financialAuditTimeStart}, '%Y-%m-%d 00:00:00' )
                AND audit_time_two &lt;= DATE_FORMAT(#{params.financialAuditTimeEnd}, '%Y-%m-%d 23:59:59' ))
            </if>
        </if>
        <if test="params.payTimeStart != null and params.payTimeStart != ''">
            <if test="params.payTimeEnd != null and params.payTimeEnd != ''">
                AND (pay_time >= DATE_FORMAT(#{params.payTimeStart}, '%Y-%m-%d 00:00:00' )
                AND pay_time &lt;= DATE_FORMAT(#{params.payTimeEnd}, '%Y-%m-%d 23:59:59' ))
            </if>
        </if>
        <if test="params.type != null">
            AND type = #{params.type}
        </if>
        ORDER BY last_modified_time DESC, id DESC
    </select>

    <select id="selectReserveApplyDetailById"
            resultType="com.logistics.tms.controller.reserveapply.response.ReserveApplyDetailResponseModel">
        SELECT id                        applyId,
               reserve_apply_code        applyCode,
               `status`,
               staff_id                  driverId,
               staff_name                driverName,
               staff_mobile              driverMobile,
               AES_DECRYPT(UNHEX(receive_bank_account), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') receiveAccount,
               receive_bank_account_name receiveAccountName,
               receive_bra_bank_name     receiveBraBankName,
               apply_amount              applyAmount,
               apply_remark              remark,
               approved_amount           approveAmount,
               auditor_name_one          businessAuditor,
               audit_time_one            businessAuditTime,
               audit_remark_one          businessRemark,
               auditor_name_two          financeAuditor,
               audit_time_two            financeAuditTime,
               audit_remark_two          financeRemark,
               pay_time                  remitDate,
               running_code              remitCode,
               payment_remark            remitRemark,
               last_modified_time        remitOperateTime
        FROM t_reserve_apply
        WHERE valid = 1
          AND id = #{id}
    </select>

    <select id="selectOneById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List_Decrypt"/>
        FROM t_reserve_apply
        WHERE valid = 1 AND id = #{id}
    </select>

    <select id="selectOneByIdAndDriverId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List_Decrypt"/>
        FROM t_reserve_apply
        WHERE valid = 1
        AND id = #{id}
        AND staff_id = #{driverId}
    </select>

    <update id="updateAudit">
        UPDATE t_reserve_apply
        <set>
            <if test="apply.status != null">
                status = #{apply.status},
            </if>
            <if test="apply.approvedAmount != null">
                approved_amount = #{apply.approvedAmount},
            </if>
            <if test="apply.auditorNameOne != null and apply.auditorNameOne != ''">
                auditor_name_one = #{apply.auditorNameOne},
            </if>
            <if test="apply.auditTimeOne != null">
                audit_time_one = #{apply.auditTimeOne},
            </if>
            <if test="apply.auditRemarkOne != null and apply.auditRemarkOne != ''">
                audit_remark_one = #{apply.auditRemarkOne},
            </if>
            <if test="apply.auditorNameTwo != null and apply.auditorNameTwo != ''">
                auditor_name_two = #{apply.auditorNameTwo},
            </if>
            <if test="apply.auditTimeTwo != null">
                audit_time_two = #{apply.auditTimeTwo},
            </if>
            <if test="apply.auditRemarkTwo != null and apply.auditRemarkTwo != '' ">
                audit_remark_two = #{apply.auditRemarkTwo},
            </if>
            <if test="apply.payTime != null">
                pay_time = #{apply.payTime},
            </if>
            <if test="apply.runningCode != null and apply.runningCode != '' ">
                running_code = #{apply.runningCode},
            </if>
            <if test="apply.paymentRemark != null and apply.paymentRemark != '' ">
                payment_remark = #{apply.paymentRemark},
            </if>
            <if test="apply.rejectRemark != null and apply.rejectRemark != ''">
                reject_remark = #{apply.rejectRemark},
            </if>
            <if test="apply.cancelRemark != null and apply.cancelRemark != ''">
                cancel_remark = #{apply.cancelRemark},
            </if>
            <if test="apply.balanceAmount != null">
                balance_amount = #{apply.balanceAmount},
            </if>
            <if test="apply.lastModifiedBy != null and apply.lastModifiedBy != ''">
                last_modified_by = #{apply.lastModifiedBy},
            </if>
            <if test="apply.lastModifiedTime != null">
                last_modified_time = #{apply.lastModifiedTime},
            </if>
        </set>
        WHERE valid = 1
        AND id = #{apply.id}
        AND status = #{currentStatus}
    </update>

    <select id="selectReserveApplyUsedInCostDeductions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List_Decrypt"/>
        FROM t_reserve_apply
        WHERE valid = 1
        AND staff_id = #{driverId}
        AND status = 4
        AND balance_amount > 0
        ORDER BY pay_time , id
    </select>

    <update id="batchUpdateReserveApplyBalanceDeduction">
        <foreach collection="updateModelList" item="updateModel" separator=";">
            UPDATE t_reserve_apply
            <set>
                <if test="updateModel.updatedBalance != null">
                    balance_amount = balance_amount - #{updateModel.updatedBalance},
                </if>
                <if test="updateModel.lastModifiedBy != null and updateModel.lastModifiedBy != ''">
                    last_modified_by = #{updateModel.lastModifiedBy},
                </if>
                <if test="updateModel.lastModifiedTime != null">
                    last_modified_time = #{updateModel.lastModifiedTime},
                </if>
            </set>
            WHERE valid = 1
            AND status = 4
            <if test="updateModel.reserveApplyCode != null and updateModel.reserveApplyCode != ''">
                AND reserve_apply_code = #{updateModel.reserveApplyCode}
            </if>
            <if test="updateModel.balanceAmount != null">
                AND balance_amount = #{updateModel.balanceAmount}
            </if>
        </foreach>
    </update>

    <select id="selectReserveBalanceApplyList" resultType="com.logistics.tms.controller.reserveapply.response.ReserveBalanceApplyListItemModel">
        SELECT
        id              applyId,
        status,
        staff_name      staffName,
        staff_mobile    staffMobile,
        apply_amount    applyAmount,
        apply_time      applyDate,
        approved_amount approveAmount
        FROM t_reserve_apply
        WHERE valid = 1
          AND staff_id = #{driverId}
          AND DATE_FORMAT(apply_time, '%Y-%m') = #{applyMonth}
          and type = 1
        ORDER BY apply_time DESC, id DESC
    </select>

    <insert id="insertReserveApply" parameterType="com.logistics.tms.entity.TReserveApply" useGeneratedKeys="true" keyProperty="id">
        insert into t_reserve_apply
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="reserveApplyCode != null" >
                reserve_apply_code,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="staffId != null" >
                staff_id,
            </if>
            <if test="staffName != null" >
                staff_name,
            </if>
            <if test="staffMobile != null" >
                staff_mobile,
            </if>
            <if test="staffProperty != null" >
                staff_property,
            </if>
            <if test="receiveBankAccount != null" >
                receive_bank_account,
            </if>
            <if test="receiveBankAccountName != null" >
                receive_bank_account_name,
            </if>
            <if test="receiveBraBankName != null" >
                receive_bra_bank_name,
            </if>
            <if test="applyAmount != null" >
                apply_amount,
            </if>
            <if test="applyTime != null" >
                apply_time,
            </if>
            <if test="applyRemark != null" >
                apply_remark,
            </if>
            <if test="approvedAmount != null" >
                approved_amount,
            </if>
            <if test="auditorNameOne != null" >
                auditor_name_one,
            </if>
            <if test="auditTimeOne != null" >
                audit_time_one,
            </if>
            <if test="auditRemarkOne != null" >
                audit_remark_one,
            </if>
            <if test="auditorNameTwo != null" >
                auditor_name_two,
            </if>
            <if test="auditTimeTwo != null" >
                audit_time_two,
            </if>
            <if test="auditRemarkTwo != null" >
                audit_remark_two,
            </if>
            <if test="payTime != null" >
                pay_time,
            </if>
            <if test="runningCode != null" >
                running_code,
            </if>
            <if test="paymentRemark != null" >
                payment_remark,
            </if>
            <if test="rejectRemark != null" >
                reject_remark,
            </if>
            <if test="cancelRemark != null" >
                cancel_remark,
            </if>
            <if test="balanceAmount != null" >
                balance_amount,
            </if>
            <if test="createdBy != null" >
                created_by,
            </if>
            <if test="createdTime != null" >
                created_time,
            </if>
            <if test="lastModifiedBy != null" >
                last_modified_by,
            </if>
            <if test="lastModifiedTime != null" >
                last_modified_time,
            </if>
            <if test="valid != null" >
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="reserveApplyCode != null" >
                #{reserveApplyCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="staffId != null" >
                #{staffId,jdbcType=BIGINT},
            </if>
            <if test="staffName != null" >
                #{staffName,jdbcType=VARCHAR},
            </if>
            <if test="staffMobile != null" >
                #{staffMobile,jdbcType=VARCHAR},
            </if>
            <if test="staffProperty != null" >
                #{staffProperty,jdbcType=INTEGER},
            </if>
            <if test="receiveBankAccount != null" >
                HEX(AES_ENCRYPT(#{receiveBankAccount,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="receiveBankAccountName != null" >
                #{receiveBankAccountName,jdbcType=VARCHAR},
            </if>
            <if test="receiveBraBankName != null" >
                #{receiveBraBankName,jdbcType=VARCHAR},
            </if>
            <if test="applyAmount != null" >
                #{applyAmount,jdbcType=DECIMAL},
            </if>
            <if test="applyTime != null" >
                #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="applyRemark != null" >
                #{applyRemark,jdbcType=VARCHAR},
            </if>
            <if test="approvedAmount != null" >
                #{approvedAmount,jdbcType=DECIMAL},
            </if>
            <if test="auditorNameOne != null" >
                #{auditorNameOne,jdbcType=VARCHAR},
            </if>
            <if test="auditTimeOne != null" >
                #{auditTimeOne,jdbcType=TIMESTAMP},
            </if>
            <if test="auditRemarkOne != null" >
                #{auditRemarkOne,jdbcType=VARCHAR},
            </if>
            <if test="auditorNameTwo != null" >
                #{auditorNameTwo,jdbcType=VARCHAR},
            </if>
            <if test="auditTimeTwo != null" >
                #{auditTimeTwo,jdbcType=TIMESTAMP},
            </if>
            <if test="auditRemarkTwo != null" >
                #{auditRemarkTwo,jdbcType=VARCHAR},
            </if>
            <if test="payTime != null" >
                #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="runningCode != null" >
                #{runningCode,jdbcType=VARCHAR},
            </if>
            <if test="paymentRemark != null" >
                #{paymentRemark,jdbcType=VARCHAR},
            </if>
            <if test="rejectRemark != null" >
                #{rejectRemark,jdbcType=VARCHAR},
            </if>
            <if test="cancelRemark != null" >
                #{cancelRemark,jdbcType=VARCHAR},
            </if>
            <if test="balanceAmount != null" >
                #{balanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="createdBy != null" >
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null" >
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null" >
                #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null" >
                #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null" >
                #{valid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <select id="statisticsReserveApplyList" resultType="com.logistics.tms.biz.reserveapply.model.ReserveApplyListStatisticsModel">
        SELECT
        count(*)                                                                                       applyCount,
        ifnull(sum(CASE STATUS WHEN 3 THEN 1 WHEN 4 THEN 1 ELSE 0 END), 0)                             approveCount,
        ifnull(sum(CASE STATUS WHEN 3 THEN approved_amount WHEN 4 THEN approved_amount ELSE 0 END), 0) approveAmount
        FROM t_reserve_apply
        WHERE valid = 1
          and staff_id = #{driverId}
          AND DATE_FORMAT(apply_time, '%Y-%m') = #{applyMonth}
    </select>

    <update id="updateReserveApply" parameterType="com.logistics.tms.entity.TReserveApply" >
        update t_reserve_apply
        <set >
            <if test="reserveApplyCode != null" >
                reserve_apply_code = #{reserveApplyCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="staffId != null" >
                staff_id = #{staffId,jdbcType=BIGINT},
            </if>
            <if test="staffName != null" >
                staff_name = #{staffName,jdbcType=VARCHAR},
            </if>
            <if test="staffMobile != null" >
                staff_mobile = #{staffMobile,jdbcType=VARCHAR},
            </if>
            <if test="staffProperty != null" >
                staff_property = #{staffProperty,jdbcType=INTEGER},
            </if>
            <if test="receiveBankAccount != null" >
                receive_bank_account = HEX(AES_ENCRYPT(#{receiveBankAccount,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="receiveBankAccountName != null" >
                receive_bank_account_name = #{receiveBankAccountName,jdbcType=VARCHAR},
            </if>
            <if test="receiveBraBankName != null" >
                receive_bra_bank_name = #{receiveBraBankName,jdbcType=VARCHAR},
            </if>
            <if test="applyAmount != null" >
                apply_amount = #{applyAmount,jdbcType=DECIMAL},
            </if>
            <if test="applyTime != null" >
                apply_time = #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="applyRemark != null" >
                apply_remark = #{applyRemark,jdbcType=VARCHAR},
            </if>
            <if test="approvedAmount != null" >
                approved_amount = #{approvedAmount,jdbcType=DECIMAL},
            </if>
            <if test="auditorNameOne != null" >
                auditor_name_one = #{auditorNameOne,jdbcType=VARCHAR},
            </if>
            <if test="auditTimeOne != null" >
                audit_time_one = #{auditTimeOne,jdbcType=TIMESTAMP},
            </if>
            <if test="auditRemarkOne != null" >
                audit_remark_one = #{auditRemarkOne,jdbcType=VARCHAR},
            </if>
            <if test="auditorNameTwo != null" >
                auditor_name_two = #{auditorNameTwo,jdbcType=VARCHAR},
            </if>
            <if test="auditTimeTwo != null" >
                audit_time_two = #{auditTimeTwo,jdbcType=TIMESTAMP},
            </if>
            <if test="auditRemarkTwo != null" >
                audit_remark_two = #{auditRemarkTwo,jdbcType=VARCHAR},
            </if>
            <if test="payTime != null" >
                pay_time = #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="runningCode != null" >
                running_code = #{runningCode,jdbcType=VARCHAR},
            </if>
            <if test="paymentRemark != null" >
                payment_remark = #{paymentRemark,jdbcType=VARCHAR},
            </if>
            <if test="rejectRemark != null" >
                reject_remark = #{rejectRemark,jdbcType=VARCHAR},
            </if>
            <if test="cancelRemark != null" >
                cancel_remark = #{cancelRemark,jdbcType=VARCHAR},
            </if>
            <if test="balanceAmount != null" >
                balance_amount = #{balanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="createdBy != null" >
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null" >
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null" >
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null" >
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null" >
                valid = #{valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectAllByReserveApplyCodeIn" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_reserve_apply
        where valid = 1
        and status = 4
        and reserve_apply_code IN
        <foreach collection="reserveApplyCodeList" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
</mapper>