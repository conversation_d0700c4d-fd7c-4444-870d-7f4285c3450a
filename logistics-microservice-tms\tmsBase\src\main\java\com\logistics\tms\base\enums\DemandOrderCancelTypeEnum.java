package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: wjf
 * @date: 2018/11/13 9:13
 */
public enum DemandOrderCancelTypeEnum {
    NULL(-99,""),
    COMPANY(1,"我司原因"),
    CUSTOMER(2,"客户原因"),
    FORCE_MAJEURE(3, "不可抗力"),
    LOGISTICS(4, "物流原因"),
    COMMUNICATION(5, "平台问题"),
    OTHER_REASON(6, "其他原因"),

    ;

    private Integer key;
    private String value;

    DemandOrderCancelTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static DemandOrderCancelTypeEnum getEnum(Integer key){
        for (DemandOrderCancelTypeEnum t:values()) {
            if (t.getKey().equals(key)){
                return t;
            }
        }
        return NULL;
    }

    @Getter
    @AllArgsConstructor
    public enum DemandOrderCancelTypeTwoEnum {
        NULL(DemandOrderCancelTypeEnum.NULL, -99,""),

        REPEAT_REPORT(DemandOrderCancelTypeEnum.CUSTOMER, 201, "重复上报"),
        CHANGE_LETTERHEAD(DemandOrderCancelTypeEnum.CUSTOMER, 202, "更换签收单抬头"),
        RE_ORDER(DemandOrderCancelTypeEnum.CUSTOMER, 203, "数据报错，重新下单"),
        ADDRESS_CAUSE(DemandOrderCancelTypeEnum.CUSTOMER, 204, "地址原因"),
        WAITING_CAUSE(DemandOrderCancelTypeEnum.CUSTOMER, 205, "等待问题"),
        PALLET_OCCUPANCY(DemandOrderCancelTypeEnum.CUSTOMER, 206, "托盘占用"),
        FLOW_DIRECTION_CHECK(DemandOrderCancelTypeEnum.CUSTOMER, 207, "流向核对"),
        NOT_COMPATIBLE_WITH_LOADING(DemandOrderCancelTypeEnum.CUSTOMER, 208, "不配合装车"),
        CUSTOMER_NO_TIME(DemandOrderCancelTypeEnum.CUSTOMER, 209, "客户临时有事"),
        PHONE_UNAVAILABLE(DemandOrderCancelTypeEnum.CUSTOMER, 210, "现场电话联系不上"),


        INCLEMENT_WEATHER(DemandOrderCancelTypeEnum.FORCE_MAJEURE, 301, "恶劣天气"),
        GOV_REGULATION(DemandOrderCancelTypeEnum.FORCE_MAJEURE, 302, "政府管制"),
        REPAIR_ROAD(DemandOrderCancelTypeEnum.FORCE_MAJEURE, 303, "修路"),
        FLOOD(DemandOrderCancelTypeEnum.FORCE_MAJEURE, 304, "洪涝"),

        RECOVERY_NOT_TIMELY(DemandOrderCancelTypeEnum.LOGISTICS, 401, "回收不及时"),
        VEHICLE_FULLY_LOADED(DemandOrderCancelTypeEnum.LOGISTICS, 402, "车辆已满载"),

        REPEAT_ORDER(DemandOrderCancelTypeEnum.COMMUNICATION, 501, "重复下单"),
        IRREGULAR_OPERATION(DemandOrderCancelTypeEnum.COMMUNICATION, 502, "操作不规范"),

        PICK_UP_COMBINED_BILL(DemandOrderCancelTypeEnum.OTHER_REASON, 601, "物流提货现场并单"),

        ;
        private DemandOrderCancelTypeEnum typeEnum;
        private Integer key;
        private String value;

        public static DemandOrderCancelTypeTwoEnum getEnum(Integer key){
            for (DemandOrderCancelTypeTwoEnum t:values()) {
                if (t.getKey().equals(key)){
                    return t;
                }
            }
            return NULL;
        }
    }
}
