package com.logistics.management.webapi.controller.freightconfig.request.mileage;

import com.logistics.management.webapi.controller.freightconfig.request.ladder.CarrierFreightConfigLadderRequestDto;
import com.logistics.management.webapi.controller.freightconfig.request.scheme.CarrierFreightConfigSchemeAddRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigMileageAddRequestDto extends CarrierFreightConfigSchemeAddRequestDto {

    @Valid
    @ApiModelProperty(value = "阶梯", required = true)
    @Size(min = 1, max = 10, message = "请维护 1 ~ 10 条价格配置")
    private List<CarrierFreightConfigLadderRequestDto> ladderConfigList;
}
