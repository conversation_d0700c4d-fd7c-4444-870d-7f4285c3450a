package com.logistics.tms.controller.driversafemeeting.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/11/4 14:07
 */
@Data
public class ReplacementDriverSafeMeetingRequestModel {
    @ApiModelProperty(value = "学习例会id")
    private Long safeMeetingId;
    @ApiModelProperty(value = "驾驶员")
    private List<Long> driverIdList;
}
