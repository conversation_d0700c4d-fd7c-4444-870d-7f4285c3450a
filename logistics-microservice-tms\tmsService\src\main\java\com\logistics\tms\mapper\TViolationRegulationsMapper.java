package com.logistics.tms.mapper;

import com.logistics.tms.api.feign.violationregulation.model.SearchViolationRegulationListRequestModel;
import com.logistics.tms.api.feign.violationregulation.model.SearchViolationRegulationListResponseModel;
import com.logistics.tms.api.feign.violationregulation.model.ViolationRegulationListResponseModel;
import com.logistics.tms.entity.TViolationRegulations;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface TViolationRegulationsMapper extends BaseMapper<TViolationRegulations> {

    SearchViolationRegulationListResponseModel getViolationRegulationSummaryData(@Param("params") SearchViolationRegulationListRequestModel requestModel);

    List<Long> getViolationRegulationIds(@Param("params") SearchViolationRegulationListRequestModel requestModel);

    List<ViolationRegulationListResponseModel> getViolationRegulationListByIds(@Param("ids") String violationRegulationIds);

    int batchInsert(@Param("list") List<TViolationRegulations> violationRegulations);

    List<TViolationRegulations> getViolationRegulations(@Param("vehicleIds") String vehicleIds);

    TViolationRegulations selectRecordByVehicleIdAndDriverIdAndOccurTime(@Param("vehicleId") Long vehicleId, @Param("driverId") Long driverId, @Param("date") Date occuranceTime);

    int batchUpdate(@Param("list") List<TViolationRegulations> violationRegulations);



}