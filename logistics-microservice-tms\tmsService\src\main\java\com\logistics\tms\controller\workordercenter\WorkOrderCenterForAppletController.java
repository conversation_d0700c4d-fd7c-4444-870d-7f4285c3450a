package com.logistics.tms.controller.workordercenter;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.workordercenter.WorkOrderForAppletBiz;
import com.logistics.tms.controller.workordercenter.request.WorkOrderDetailAppletRequestModel;
import com.logistics.tms.controller.workordercenter.request.WorkOrderListAppletRequestModel;
import com.logistics.tms.controller.workordercenter.response.WorkOrderDetailAppletResponseModel;
import com.logistics.tms.controller.workordercenter.response.WorkOrderListAppletResponseModel;
import com.logistics.tms.controller.workordercenter.response.WorkOrderProcessAppletResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(value = "API - WorkOrderCenterForAppletServiceApi- 小程序工单中心", tags = "小程序工单中心")
@RestController
@RequestMapping("/service/applet/workOrderCenter")
public class WorkOrderCenterForAppletController{

    @Autowired
    private WorkOrderForAppletBiz workOrderForAppletBiz;

    /**
     * 工单列表
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "工单列表")
    @PostMapping(value = "/workOrderListForApplet")
    public Result<PageInfo<WorkOrderListAppletResponseModel>> workOrderListForApplet(@RequestBody WorkOrderListAppletRequestModel requestModel) {
        return Result.success(workOrderForAppletBiz.workOrderListForApplet(requestModel));
    }

    /**
     * 工单详情
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "工单详情")
    @PostMapping(value = "/workOrderDetailForApplet")
    public Result<WorkOrderDetailAppletResponseModel> workOrderDetailForApplet(@RequestBody WorkOrderDetailAppletRequestModel requestModel) {
        return Result.success(workOrderForAppletBiz.workOrderDetail(requestModel));
    }

    /**
     * 工单处理过程列表
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "工单处理过程列表")
    @PostMapping(value = "/workOrderProcessForApplet")
    public Result<List<WorkOrderProcessAppletResponseModel>> workOrderProcessForApplet(@RequestBody WorkOrderDetailAppletRequestModel requestModel) {
        return Result.success(workOrderForAppletBiz.workOrderProcess(requestModel));
    }
}
