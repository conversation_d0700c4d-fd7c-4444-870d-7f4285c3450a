package com.logistics.management.webapi.client.dispatch.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/7 9:10
 */
@Data
public class DemandOrderSpecialDispatchDetailResponseModel {

    /**
     * 需求单货物
     */
    private List<DemandOrderSpecialDispatchDetailListResponseModel> goodsList;

    /**
     * 车主
     */
    @ApiModelProperty("车主类型 1 公司 2 个人")
    private Integer companyCarrierType;
    @ApiModelProperty("车主id")
    private Long companyCarrierId;
    @ApiModelProperty("车主公司名")
    private String companyCarrierName;
    @ApiModelProperty("车主联系人")
    private String carrierContactName;
    @ApiModelProperty("车主联系方式")
    private String carrierContactPhone;
    @ApiModelProperty("是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;

    /**
     * 货物单位 1 件 2 吨
     */
    private Integer goodsUnit;

    /**
     * 装卸数-装
     */
    private Integer loadPointAmount;

    /**
     * 装卸数-卸
     */
    private Integer unloadPointAmount;

    /**
     * 串点距离
     */
    private BigDecimal crossPointDistance;

}
