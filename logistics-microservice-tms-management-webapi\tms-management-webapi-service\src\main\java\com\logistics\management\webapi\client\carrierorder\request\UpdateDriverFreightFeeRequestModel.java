package com.logistics.management.webapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2019/6/13 17:23
 */
@Data
public class UpdateDriverFreightFeeRequestModel {
    @ApiModelProperty("运单Id")
    private Long carrierOrderId;
    @ApiModelProperty("司机运费")
    private BigDecimal driverFreightFee;

}
