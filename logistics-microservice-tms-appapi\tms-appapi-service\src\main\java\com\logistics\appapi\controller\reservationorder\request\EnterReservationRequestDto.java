package com.logistics.appapi.controller.reservationorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


@Data
public class EnterReservationRequestDto {

	@ApiModelProperty(value = "手机号",required = true)
//	@NotBlank(message = "手机号不能为空")
	private String userAccount;

	@ApiModelProperty(value = "短信验证码",required = true)
//	@NotBlank(message = "短信验证码不能为空")
	private String verificationCode;

	@ApiModelProperty(value = "运单号",required = true)
	@NotBlank(message = "运单号不能为空")
	private String carrierOrderCode;

	@ApiModelProperty(value = "预约类型：1 提货，2 卸货 ",required = true)
	@NotBlank(message = "预约类型不能为空")
	private String reservationType;

	@ApiModelProperty("0 否 1：是")
	private String ifNeedVerifyDriver;
}
