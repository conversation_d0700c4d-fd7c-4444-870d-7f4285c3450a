package com.logistics.appapi.controller.baiscinfo.mapping;

import com.logistics.appapi.base.enums.AuthModelEnum;
import com.logistics.appapi.base.enums.RealNameAuthStatusEnum;
import com.logistics.appapi.client.baiscinfo.response.DriverBasicInfoResponseModel;
import com.logistics.appapi.controller.baiscinfo.response.DriverBasicInfoResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

public class DriverBasicInfoMapping extends MapperMapping<DriverBasicInfoResponseModel, DriverBasicInfoResponseDto> {

    @Override
    public void configure() {
        DriverBasicInfoResponseModel source = getSource();
        DriverBasicInfoResponseDto destination = getDestination();

        // 认证类型文本
        destination.setAuthModelLabel(AuthModelEnum.getEnumByKey(source.getAuthModel()).getValue());
        // 实名文本
        destination.setRealNameAuthenticationStatusLabel(RealNameAuthStatusEnum.getEnumByKey(source.getRealNameAuthenticationStatus()).getValue());
    }
}
