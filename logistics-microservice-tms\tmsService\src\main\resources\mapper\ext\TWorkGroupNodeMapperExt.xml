<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TWorkGroupNodeMapper" >
  <select id="getByWorkGroupId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_work_group_node
    where valid = 1
    and work_group_id  = #{workGroupId,jdbcType=BIGINT}
  </select>

  <select id="selectWorkGroupNodeByWorkGroupIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_work_group_node
    where valid = 1
    and work_group_id IN
    <foreach collection="workGroupIds" item="workGroupId" open="(" separator="," close=")">
      #{workGroupId}
    </foreach>
    and order_type = #{orderType}
    and order_node = #{orderNode}
  </select>

  <update id="batchUpdateSelective" parameterType="com.logistics.tms.entity.TWorkGroupNode">
    <foreach collection="recordList" item="item" separator=";">
      update t_work_group_node
      <set>
        <if test="item.workGroupId != null">
          work_group_id = #{item.workGroupId,jdbcType=BIGINT},
        </if>
        <if test="item.orderType != null">
          order_type = #{item.orderType,jdbcType=INTEGER},
        </if>
        <if test="item.orderNode != null">
          order_node = #{item.orderNode,jdbcType=INTEGER},
        </if>
        <if test="item.timeRequire != null">
          time_require = #{item.timeRequire,jdbcType=INTEGER},
        </if>
        <if test="item.amountType != null">
          amount_type = #{item.amountType,jdbcType=INTEGER},
        </if>
        <if test="item.amountSymbol != null">
          amount_symbol = #{item.amountSymbol,jdbcType=INTEGER},
        </if>
        <if test="item.amountRequire != null">
          amount_require = #{item.amountRequire,jdbcType=DECIMAL},
        </if>
        <if test="item.createdBy != null">
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <insert id="batchInsertAndReturnId" useGeneratedKeys="true" keyProperty="id">
    insert into t_work_group_node (id, work_group_id, order_type,
                                   order_node, time_require, amount_type,
                                   amount_symbol, amount_require, created_by,
                                   created_time, last_modified_by, last_modified_time,
                                   valid)
    values
    <foreach collection="recordList" item="item" separator=",">
      (
      <choose>
        <when test="item.id != null">
          #{item.id,jdbcType=BIGINT},
        </when>
        <otherwise>
          default,
        </otherwise>
      </choose>
      <choose>
        <when test="item.workGroupId != null">
          #{item.workGroupId,jdbcType=BIGINT},
        </when>
        <otherwise>
          default,
        </otherwise>
      </choose>
      <choose>
        <when test="item.orderType != null">
          #{item.orderType,jdbcType=INTEGER},
        </when>
        <otherwise>
          default,
        </otherwise>
      </choose>
      <choose>
        <when test="item.orderNode != null">
          #{item.orderNode,jdbcType=INTEGER},
        </when>
        <otherwise>
          default,
        </otherwise>
      </choose>
      <choose>
        <when test="item.timeRequire != null">
          #{item.timeRequire,jdbcType=INTEGER},
        </when>
        <otherwise>
          default,
        </otherwise>
      </choose>
      <choose>
        <when test="item.amountType != null">
          #{item.amountType,jdbcType=INTEGER},
        </when>
        <otherwise>
          default,
        </otherwise>
      </choose>
      <choose>
        <when test="item.amountSymbol != null">
          #{item.amountSymbol,jdbcType=INTEGER},
        </when>
        <otherwise>
          default,
        </otherwise>
      </choose>
      <choose>
        <when test="item.amountRequire != null">
          #{item.amountRequire,jdbcType=DECIMAL},
        </when>
        <otherwise>
          default,
        </otherwise>
      </choose>
      <choose>
        <when test="item.createdBy != null">
          #{item.createdBy,jdbcType=VARCHAR},
        </when>
        <otherwise>
          default,
        </otherwise>
      </choose>
      <choose>
        <when test="item.createdTime != null">
          #{item.createdTime,jdbcType=TIMESTAMP},
        </when>
        <otherwise>
          default,
        </otherwise>
      </choose>
      <choose>
        <when test="item.lastModifiedBy != null">
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </when>
        <otherwise>
          default,
        </otherwise>
      </choose>
      <choose>
        <when test="item.lastModifiedTime != null">
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </when>
        <otherwise>
          default,
        </otherwise>
      </choose>
      <choose>
        <when test="item.valid != null">
          #{item.valid,jdbcType=INTEGER}
        </when>
        <otherwise>
                    default
        </otherwise>
      </choose>
      )
    </foreach>
  </insert>

  <resultMap id="selectNodeListByWorkGroupIdMap" type="com.logistics.tms.controller.workgroup.response.WorkGroupNodeListResponseModel">
    <id column="workGroupNodeId" property="workGroupNodeId"/>
    <result column="order_type" property="orderType"/>
    <result column="order_node" property="orderNode"/>
    <result column="time_require" property="timeRequire"/>
    <result column="amount_type" property="amountType"/>
    <result column="amount_symbol" property="amountSymbol"/>
    <result column="amount_require" property="amountRequire"/>
    <collection property="fieldList" ofType="java.lang.String">
      <result column="field" javaType="java.lang.String"/>
    </collection>
  </resultMap>

  <select id="selectNodeListByWorkGroupId" resultMap="selectNodeListByWorkGroupIdMap">
    select
    twgn.id workGroupNodeId,
    twgn.order_type,
    twgn.order_node,
    twgn.time_require,
    twgn.amount_type,
    twgn.amount_symbol,
    twgn.amount_require,

    twgnf.field
    from t_work_group_node twgn
    left join t_work_group_node_field twgnf on twgnf.work_group_node_id = twgn.id and twgnf.valid = 1
    where twgn.valid = 1
    and twgn.work_group_id = #{workGroupId,jdbcType=BIGINT}
    order by twgn.created_time desc, twgn.id desc
  </select>
</mapper>