package com.logistics.appapi.client.attendance.hystrix;

import com.logistics.appapi.client.attendance.AttendanceClient;
import com.logistics.appapi.client.attendance.request.*;
import com.logistics.appapi.client.attendance.response.AttendanceClockDetailResponseModel;
import com.logistics.appapi.client.attendance.response.AttendanceHistoryListResponseModel;
import com.logistics.appapi.client.attendance.response.QueryPathByLonAndLatResponseModel;
import com.logistics.appapi.client.attendance.response.UpdateAttendanceHistoryDetailResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/3/7 14:50
 */
@Component
public class AttendanceClientHystrix implements AttendanceClient {
    @Override
    public Result<AttendanceClockDetailResponseModel> attendanceClockDetail() {
        return Result.timeout();
    }

    @Override
    public Result<QueryPathByLonAndLatResponseModel> queryPathByLonAndLat(QueryPathByLonAndLatRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> attendanceClock(AttendanceClockRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<AttendanceHistoryListResponseModel> attendanceHistoryList(AttendanceHistoryListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> updateAttendanceHistory(UpdateAttendanceHistoryRequestModel requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> cancelUpdateAttendanceHistory(CancelUpdateAttendanceHistoryRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<UpdateAttendanceHistoryDetailResponseModel> updateAttendanceHistoryDetail(UpdateAttendanceHistoryDetailRequestModel requestModel) {
        return Result.timeout();
    }
}
