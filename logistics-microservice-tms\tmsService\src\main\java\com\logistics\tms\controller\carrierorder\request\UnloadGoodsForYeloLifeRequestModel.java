package com.logistics.tms.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/22
 */
@Data
public class UnloadGoodsForYeloLifeRequestModel {

	@ApiModelProperty(value = "货物id")
	private Long goodsId;

	@ApiModelProperty(value = "货物数量")
	private BigDecimal unloadAmount;


	@ApiModelProperty(value = "货物的编码集合 v2.44", required = true)
	private List<LoadGoodsForYeloLifeRequestCodeModel> codeDtoList;
}
