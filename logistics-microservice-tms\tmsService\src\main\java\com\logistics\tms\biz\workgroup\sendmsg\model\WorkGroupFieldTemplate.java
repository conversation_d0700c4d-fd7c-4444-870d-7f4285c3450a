package com.logistics.tms.biz.workgroup.sendmsg.model;

import com.google.common.collect.Maps;
import com.logistics.tms.base.enums.WorkGroupOrderTypeEnum;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * 后台api层有对应枚举,修改时需同步修改 WorkGroupDemandOrderFieldEnum WorkGroupCarrierOrderFieldEnum
 */
@NoArgsConstructor
public class WorkGroupFieldTemplate {

    private static final Map<String, String> CARRIER_ORDER_FIELD_MAP;

    static {
        CARRIER_ORDER_FIELD_MAP = Maps.newLinkedHashMap();
        CARRIER_ORDER_FIELD_MAP.put("statusDesc", "状态");
        CARRIER_ORDER_FIELD_MAP.put("carrierOrderCode", "运单号");
        CARRIER_ORDER_FIELD_MAP.put("correctStatusDesc", "纠错状态");
        CARRIER_ORDER_FIELD_MAP.put("outStatusLabel", "出库状态");
        CARRIER_ORDER_FIELD_MAP.put("demandOrderCode", "需求单号");
        CARRIER_ORDER_FIELD_MAP.put("vehicleNo", "车牌");
        CARRIER_ORDER_FIELD_MAP.put("driver", "司机");
        CARRIER_ORDER_FIELD_MAP.put("driverIdentity", "司机身份证号");
        CARRIER_ORDER_FIELD_MAP.put("recycleTaskTypeLabel", "时效要求");
        CARRIER_ORDER_FIELD_MAP.put("dispatchFreightFee", "司机运费合计(元)");
        CARRIER_ORDER_FIELD_MAP.put("loadAddress", "发货地址");
        CARRIER_ORDER_FIELD_MAP.put("consignor", "发货人");
        CARRIER_ORDER_FIELD_MAP.put("expectAmount", "预计承运");
        CARRIER_ORDER_FIELD_MAP.put("otherFee", "临时费用（元）");
        CARRIER_ORDER_FIELD_MAP.put("entrustFreight", "实际货主费用（元）");
        CARRIER_ORDER_FIELD_MAP.put("carrierPriceTotal", "车主运费金额(元)");
        CARRIER_ORDER_FIELD_MAP.put("actualCarrierPriceTotal", "实际车主费用(元)");
        CARRIER_ORDER_FIELD_MAP.put("loadAmount", "实际提货");
        CARRIER_ORDER_FIELD_MAP.put("unloadAmount", "实际卸货");
        CARRIER_ORDER_FIELD_MAP.put("signAmount", "实际签收");
        CARRIER_ORDER_FIELD_MAP.put("backAmount", "回退数");
        CARRIER_ORDER_FIELD_MAP.put("differenceAmount", "差异数");
        CARRIER_ORDER_FIELD_MAP.put("abnormalAmount", "云仓异常数");
        CARRIER_ORDER_FIELD_MAP.put("expectLoadTime", "预计提货时间");
        CARRIER_ORDER_FIELD_MAP.put("expectArrivalTime", "预计到货时间");
        CARRIER_ORDER_FIELD_MAP.put("unloadAddress", "收货地址");
        CARRIER_ORDER_FIELD_MAP.put("receiver", "收货人");
        CARRIER_ORDER_FIELD_MAP.put("expectMileage", "预计里程数KM");
        CARRIER_ORDER_FIELD_MAP.put("loadTime", "实际提货时间");
        CARRIER_ORDER_FIELD_MAP.put("unloadTime", "实际到货时间");
        CARRIER_ORDER_FIELD_MAP.put("signTime", "实际签收时间");
        CARRIER_ORDER_FIELD_MAP.put("goodsName", "品名");
        CARRIER_ORDER_FIELD_MAP.put("remark", "备注");
        CARRIER_ORDER_FIELD_MAP.put("entrustType", "需求类型");
        CARRIER_ORDER_FIELD_MAP.put("publishOrgName", "下单部门");
        CARRIER_ORDER_FIELD_MAP.put("dispatchUserName", "调度人");
        CARRIER_ORDER_FIELD_MAP.put("carrierCompany", "车主");
        CARRIER_ORDER_FIELD_MAP.put("entrustCompany", "委托客户");
        CARRIER_ORDER_FIELD_MAP.put("customerOrderCode", "客户单号");
        CARRIER_ORDER_FIELD_MAP.put("dispatchTime", "运单生成时间");
        CARRIER_ORDER_FIELD_MAP.put("dispatchOrderCode", "调度单号");
        CARRIER_ORDER_FIELD_MAP.put("loadRegionName", "大区");
        CARRIER_ORDER_FIELD_MAP.put("loadRegionContactName", "负责人");
        CARRIER_ORDER_FIELD_MAP.put("loadValidity", "提货时效");
        CARRIER_ORDER_FIELD_MAP.put("carrierOrderTicketsAmount", "回单数");
        CARRIER_ORDER_FIELD_MAP.put("configDistance", "配置距离KM");
        CARRIER_ORDER_FIELD_MAP.put("expectedLoadTime", "期望提货时间");
        CARRIER_ORDER_FIELD_MAP.put("expectedUnloadTime", "期望到货时间");
    }

    private static final Map<String, String> DEMAND_ORDER_FIELD_MAP;

    static {
        DEMAND_ORDER_FIELD_MAP = Maps.newLinkedHashMap();
        DEMAND_ORDER_FIELD_MAP.put("statusDesc", "状态");
        DEMAND_ORDER_FIELD_MAP.put("demandOrderCode", "需求单号");
        DEMAND_ORDER_FIELD_MAP.put("customerOrderCode", "客户单号");
        DEMAND_ORDER_FIELD_MAP.put("publishTime", "下单时间");
        DEMAND_ORDER_FIELD_MAP.put("loadingUnloadingPartLabel", "装卸方式");
        DEMAND_ORDER_FIELD_MAP.put("recycleTaskTypeLabel", "时效要求");
        DEMAND_ORDER_FIELD_MAP.put("loadingUnloadingCharge", "装卸费用");
        DEMAND_ORDER_FIELD_MAP.put("otherRequirements", "其他要求");
        DEMAND_ORDER_FIELD_MAP.put("companyEntrustName", "货主");
        DEMAND_ORDER_FIELD_MAP.put("upstreamCustomer", "上游客户");
        DEMAND_ORDER_FIELD_MAP.put("loadWarehouse", "发货仓库");
        DEMAND_ORDER_FIELD_MAP.put("loadAddress", "发货省市区");
        DEMAND_ORDER_FIELD_MAP.put("loadDetailAddress", "发货详细地址");
        DEMAND_ORDER_FIELD_MAP.put("consignor", "发货联系人");
        DEMAND_ORDER_FIELD_MAP.put("unloadWarehouse", "收货仓库");
        DEMAND_ORDER_FIELD_MAP.put("unloadAddress", "收货省市区");
        DEMAND_ORDER_FIELD_MAP.put("unloadDetailAddress", "收货详细地址");
        DEMAND_ORDER_FIELD_MAP.put("receiver", "收货联系人");
        DEMAND_ORDER_FIELD_MAP.put("expectedUnloadTime", "期望到货时间");
        DEMAND_ORDER_FIELD_MAP.put("goodsAmount", "委托");
        DEMAND_ORDER_FIELD_MAP.put("arrangedAmount", "已安排");
        DEMAND_ORDER_FIELD_MAP.put("backAmount", "已退回");
        DEMAND_ORDER_FIELD_MAP.put("notArrangedAmount", "未安排");
        DEMAND_ORDER_FIELD_MAP.put("differenceAmount", "差异数");
        DEMAND_ORDER_FIELD_MAP.put("abnormalAmount", "云仓异常数");
        DEMAND_ORDER_FIELD_MAP.put("expectedLoadTime", "期望提货时间");
        DEMAND_ORDER_FIELD_MAP.put("remark", "备注");
        DEMAND_ORDER_FIELD_MAP.put("dispatchValidity", "调度时效");
        DEMAND_ORDER_FIELD_MAP.put("loadRegionName", "大区");
        DEMAND_ORDER_FIELD_MAP.put("loadRegionContactName", "负责人");
        DEMAND_ORDER_FIELD_MAP.put("goodsName", "品名");
        DEMAND_ORDER_FIELD_MAP.put("goodsSize", "规格(mm)");
        DEMAND_ORDER_FIELD_MAP.put("entrustType", "需求类型");
        DEMAND_ORDER_FIELD_MAP.put("publishOrgName", "下单部门");
        DEMAND_ORDER_FIELD_MAP.put("companyCarrierName", "车主");
        DEMAND_ORDER_FIELD_MAP.put("publishName", "委托人");
        DEMAND_ORDER_FIELD_MAP.put("rollbackRemark", "回退原因");
    }

    public static Map<String, String> getFiledByOrderType(WorkGroupOrderTypeEnum orderTypeEnum) {
        switch (orderTypeEnum) {
            case DEMAND_ORDER:
                return DEMAND_ORDER_FIELD_MAP;
            case CARRIER_ORDER:
                return CARRIER_ORDER_FIELD_MAP;
            default:
                return new HashMap<>();
        }
    }
}
