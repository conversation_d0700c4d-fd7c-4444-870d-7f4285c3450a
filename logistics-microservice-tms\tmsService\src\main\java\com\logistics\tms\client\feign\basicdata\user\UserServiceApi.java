package com.logistics.tms.client.feign.basicdata.user;

import com.logistics.tms.client.feign.FeignClientName;
import com.logistics.tms.client.feign.basicdata.user.hystrix.BasicUserServiceApiHystrix;
import com.logistics.tms.client.feign.basicdata.user.request.GetUserByIdsRequestModel;
import com.logistics.tms.client.feign.basicdata.user.request.GetUserInfoModelRequestModel;
import com.logistics.tms.client.feign.basicdata.user.request.UserIdRequestModel;
import com.logistics.tms.client.feign.basicdata.user.response.GetUserByIdsResponseModel;
import com.logistics.tms.client.feign.basicdata.user.response.UserDetailResponseModel;
import com.logistics.tms.client.feign.basicdata.user.response.UserInfoModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/25 13:19
 */
@FeignClient(name = FeignClientName.BASIC_DATA_SERVICES, fallback = BasicUserServiceApiHystrix.class)
public interface UserServiceApi {

    @ApiOperation("员工信息详情")
    @PostMapping({"/service/user/getUserInfo"})
    Result<UserDetailResponseModel> getUserInfo(@RequestBody UserIdRequestModel requestModel);

    @ApiOperation("根据账号/手机号/密码查询用户登录信息")
    @PostMapping({"/service/user/getUserInfoModel"})
    Result<UserInfoModel> getUserInfoModel(@RequestBody GetUserInfoModelRequestModel requestModel);

    @ApiOperation("根据用户ids查询用户信息")
    @PostMapping({"/service/user/getUserByIds"})
    Result<List<GetUserByIdsResponseModel>> getUserByIds(@RequestBody GetUserByIdsRequestModel requestModel);
}
