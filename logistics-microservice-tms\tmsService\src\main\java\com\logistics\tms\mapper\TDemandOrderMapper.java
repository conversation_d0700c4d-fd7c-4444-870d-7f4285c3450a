package com.logistics.tms.mapper;

import com.logistics.tms.biz.demandorder.model.AutoPublishRecycleDemandOrderModel;
import com.logistics.tms.biz.demandorder.model.AutoPublishWaitDispatchDemandModel;
import com.logistics.tms.biz.demandorder.model.FixedDemandOrderModel;
import com.logistics.tms.biz.demandorder.model.SyncTMSDemandOrderModel;
import com.logistics.tms.biz.dispatch.model.DemandOrderModel;
import com.logistics.tms.controller.biddingorder.request.SearchBiddingDemandRequestModel;
import com.logistics.tms.controller.biddingorder.response.BiddingQuoteDemandModel;
import com.logistics.tms.controller.biddingorder.response.SearchBiddingDemandResponseModel;
import com.logistics.tms.controller.demandorder.request.*;
import com.logistics.tms.controller.demandorder.response.*;
import com.logistics.tms.controller.homepage.response.DemandOrderStatisticsResponseModel;
import com.logistics.tms.entity.TDemandOrder;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface TDemandOrderMapper extends BaseMapper<TDemandOrder> {

    TDemandOrder selectByPrimaryKeyDecrypt(Long id);

    int insertSelectiveEncrypt(TDemandOrder tDemandOrder);

    int updateByPrimaryKeySelectiveEncrypt(TDemandOrder tDemandOrder);

    int batchUpdateByPrimaryKeySelective(@Param("list") List<TDemandOrder> list);

    List<TDemandOrder> getByIds(@Param("demandOrderIds") String demandOrderIds);

    TDemandOrder getByCode(@Param("demandOrderCode") String demandOrderCode);

    List<TDemandOrder> getByCodeList(@Param("demandOrderCodeList") List<String> demandOrderCodeList);

    List<DemandOrderSearchByIdsResponseModel> getDemandOrderGoodsAndRelByIds(@Param("params") DemandOrderSearchByIdsRequestModel requestModel);

    List<DemandOrderSearchByIdsResponseModel> getDemandOrderAddressEventByIds(@Param("params") DemandOrderSearchByIdsRequestModel requestModel);

    List<GetDemandOrderInfoByIdsModel> getDemandOrderInfoByIds(@Param("ids") String demandOrderIds);

    List<DemandOrderResponseModel> searchListManageAddressGoodsDemand(@Param("params") DemandOrderSearchRequestModel requestModel, @Param("demandOrderIds") String demandOrderIds);

    List<DemandOrderForLeYiResponseModel> searchListForLeYiManageAddressGoodsDemand(@Param("params") DemandOrderSearchForLeYiRequestModel requestModel, @Param("demandOrderIds") String demandOrderIds);

    List<Long> searchListManageIds(@Param("params") DemandOrderSearchRequestModel requestModel);

    List<Long> searchListForLeYiManageIds(@Param("params") DemandOrderSearchForLeYiRequestModel requestModel);

    SearchListStatisticsResponseModel searchListStatistics(@Param("params") DemandOrderSearchRequestModel requestModel);

    DemandOrderDetailResponseModel getDemandOrderDetail(@Param("params") DemandOrderDetailRequestModel requestModel);

    List<TDemandOrder> selectDemandOrdersBySourceAndPubDate(@Param("source") Integer source, @Param("fromDate") Date fromDate, @Param("endDate") Date endDate);

    List<String> getExistCodesByCustomerCodes(@Param("list") List<String> list);

    GetPublishDemandOrderDetailResponseModel getPublishDemandOrderDetail(@Param("demandOrderId") Long demandOrderId);

    List<SyncTMSDemandOrderModel> getSyncTMSDemandOrderModelById(@Param("demandOrderIds") String demandOrderIds);

    TDemandOrder getInvalidTopByDemandOrderCode(@Param("demandOrderCode") String demandOrderCode);

    List<DemandOrderModel> getDemandOrderGoodsByDemandIds(@Param("ids")String ids);

    void delDemandOrderAddressGoodsLogsByDemandOrderIds(@Param("demandOrderIds") String demandOrderIds, @Param("lastModifiedBy") String lastModifiedBy, @Param("lastModifiedTime") Date lastModifiedTime);

    List<BatchPublishDetailResponseModel> publishDetail(@Param("demandOrderIds") String demandOrderIds);

    DemandOrderDetailForLeYiResponseModel getDemandOrderDetailForLeYi(@Param("params") DemandOrderDetailRequestModel requestModel);

    List<DispatchAlarmStatisticsResponseModel> dispatchAlarmStatistics();

    List<WaitDispatchStatisticsModel> waitDispatchStatistics();

    List<MapDataStatisticsResponseModel> mapDataStatistics();

    //根据客户单号查询有效需求单
    TDemandOrder getValidSinopecOrderByOrderNo(@Param("orderNo") String orderNo);

    List<TDemandOrder> getValidSinopecOrderByOrderNos(@Param("orderNos") List<String> orderNos);

    void rollBackSinopecDemandOrderByCancel(@Param("demandOrderIds") String demandOrderIds);

    List<PublishSinopecResponseModel> sinopecPublishDetail(@Param("demandOrderIds") List<Long> demandOrderIds);

    void rollBackSinopecDemandOrderByPublish(@Param("list") List<PublishSinopecResponseModel> list);

    List<DemandOrderDataStatisticsResponseModel> selectDemandOrders();

    SinopecReportAbnormalDetailResponseModel sinopecReportAbnormalDetail(@Param("demandOrderId") Long demandOrderId);

    List<Long> searchDemandOrderListIdsForWebCarrier(@Param("params") WebDemandOrderSearchRequestModel requestModel, @Param("companyCarrierId") Long companyCarrierId);

    List<WebDemandOrderResponseModel> searchDemandOrderListForWebCarrier(@Param("params") WebDemandOrderSearchRequestModel requestModel, @Param("demandOrderIds") String demandOrderIds);

    WebDemandOrderSearchAccountResponseModel searchDemandOrderListForWebCarrierStatistics(@Param("demandOrderIds") String demandOrderIds, @Param("companyCarrierId") Long companyCarrierId);

    SearchDemandListForStatisticsChangeCarrierModel searchDemandListForStatisticsChangeCarrier(@Param("demandOrderIds") String demandOrderIds, @Param("companyCarrierId") Long companyCarrierId);

    List<Long> searchYeloLifeDemandOrderListIds(@Param("params") RenewableDemandOrderRequestModel requestModel);

    List<RenewableDemandOrderResponseModel> searchYeloLifeDemandOrderList(@Param("params") RenewableDemandOrderRequestModel requestModel, @Param("demandOrderIds") String demandOrderIds);

    RenewableDemandListStatisticsResponseModel searchYeloLifeDemandOrderStatisticsListIds(@Param("params") RenewableDemandOrderRequestModel requestModel);

    RenewableDemandOrderDetailResponseModel renewableDemandOrderDetail(@Param("params") DemandOrderDetailRequestModel requestModel);

    List<FixedDemandOrderModel> selectFixedDemandOrder();

    /**
     * 查询需要自动调度发布的回收入库类型的需求单
     *
     * @return 回收入库需求单
     */
    List<AutoPublishRecycleDemandOrderModel> selectAutoPublishRecycleDemandOrder();

    List<AutoPublishWaitDispatchDemandModel> selectAutoPublishWaitDispatchDemandOrders(@Param("ids") List<Long> ids, @Param("status") Integer status);

    DemandOrderStatisticsResponseModel demandOrderStatistics(@Param("excludeIds") List<Long> needExcludedOrderIds);

    List<SearchDemandOrderInfoResponseModel> searchDemandOrderInfo(SearchDemandOrderInfoRequestModel requestModel);


    List<SearchBiddingDemandResponseModel> searchBiddingDemand(@Param("params") SearchBiddingDemandRequestModel requestModel);


    List<BiddingQuoteDemandModel> selectQuoteDemand(@Param("demandIds") List<Long> demandIds);
}