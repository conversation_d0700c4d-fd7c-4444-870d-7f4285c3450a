package com.logistics.appapi.controller.workordercenter.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/14
 */
@Data
public class WorkOrderProcessResponseDto {

	@ApiModelProperty("状态：0 待处理，10 处理中，20 已处理，30 已关闭，40 已撤销")
	private String status = "";

	@ApiModelProperty("状态展示label")
	private String statusLabel = "";

	@ApiModelProperty("处理方人名展示label,")
	private String solveSourceLabel = "";//处理来源：1 后台，2 前台，3 小程序，4 任务中心

	@ApiModelProperty("处理时间")
	private String solveTime = "";

	@ApiModelProperty("处理备注")
	private String solveRemark = "";
}
