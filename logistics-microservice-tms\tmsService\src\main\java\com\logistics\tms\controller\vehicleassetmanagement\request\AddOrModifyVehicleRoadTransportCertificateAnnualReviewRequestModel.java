package com.logistics.tms.controller.vehicleassetmanagement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AddOrModifyVehicleRoadTransportCertificateAnnualReviewRequestModel {
    @ApiModelProperty("道路运输证年审记录Id")
    private Long vehicleRoadTransportCertificateAnnualReviewId;
    @ApiModelProperty("行驶证检查有效期")
    private Date checkValidDate;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("证件图片列表")
    private List<CertificationPicturesRequestModel> fileList;
}
