<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRegionCompanyMapper" >
  <select id="getCompanyByRegion" resultType="java.lang.Long">
    select
    trc.company_carrier_id
    from t_region_company trc
    left join t_region_item tri on tri.region_id = trc.region_id and tri.valid = 1
    where trc.valid = 1
    and tri.province_id = #{provinceId,jdbcType=BIGINT}
    and tri.city_id = #{cityId,jdbcType=BIGINT}
  </select>

  <select id="getCompanyByRegionId" resultType="java.lang.Long">
    select
    company_carrier_id
    from t_region_company
    where valid = 1
    and region_id = #{regionId,jdbcType=BIGINT}
  </select>

  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TRegionCompany" >
    <foreach collection="list" separator=";" item="item">
      insert into t_region_company
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.regionId != null" >
          region_id,
        </if>
        <if test="item.companyCarrierId != null" >
          company_carrier_id,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.regionId != null" >
          #{item.regionId,jdbcType=BIGINT},
        </if>
        <if test="item.companyCarrierId != null" >
          #{item.companyCarrierId,jdbcType=BIGINT},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="com.logistics.tms.entity.TRegionCompany" >
    <foreach collection="list" separator=";" item="item">
      update t_region_company
      <set >
        <if test="item.regionId != null" >
          region_id = #{item.regionId,jdbcType=BIGINT},
        </if>
        <if test="item.companyCarrierId != null" >
          company_carrier_id = #{item.companyCarrierId,jdbcType=BIGINT},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <update id="delByRegionId">
    update t_region_company
    set
    valid = 0,
    last_modified_by = #{userName,jdbcType=VARCHAR},
    last_modified_time = #{updateTime,jdbcType=TIMESTAMP}
    where valid = 1
    and region_id = #{regionId,jdbcType=BIGINT}
  </update>
</mapper>