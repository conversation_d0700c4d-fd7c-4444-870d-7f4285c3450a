package com.logistics.tms.mapper;

import com.logistics.tms.controller.reservationorder.request.ReservationOrderSearchListForManagementWebReqModel;
import com.logistics.tms.controller.reservationorder.request.ReservationOrderSearchListRequestModel;
import com.logistics.tms.controller.reservationorder.response.ReservationCarrierOrderListResponseModel;
import com.logistics.tms.entity.TReservationOrderItem;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/08/19
*/
@Mapper
public interface TReservationOrderItemMapper extends BaseMapper<TReservationOrderItem> {

    List<TReservationOrderItem> getWaitSignIn(@Param("carrierOrderIdList") List<Long> carrierOrderIdList);

    List<ReservationCarrierOrderListResponseModel> getOrderByReservationOrderId(@Param("reservationOrderId") Long reservationOrderId);

    List<TReservationOrderItem> getOrderByReservationOrderIds(@Param("list") List<Long> reservationOrderIds);


    List<TReservationOrderItem> listByCarrierOrderIds(@Param("carrierOrderIds") List<Long> carrierOrderIds);

    List<TReservationOrderItem> searchListForDriverWeb(@Param("tReservationOrderIds") List<Long> tReservationOrderIds, @Param("requestModel") ReservationOrderSearchListRequestModel requestModel);

    List<TReservationOrderItem> searchListForManagementWeb(@Param("tReservationOrderIds") List<Long> tReservationOrderIds, @Param("requestModel") ReservationOrderSearchListForManagementWebReqModel requestModel);

}