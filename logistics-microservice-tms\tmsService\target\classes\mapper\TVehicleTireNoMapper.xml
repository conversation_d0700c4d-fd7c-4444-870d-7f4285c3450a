<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleTireNoMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TVehicleTireNo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="tire_id" property="tireId" jdbcType="BIGINT" />
    <result column="amount" property="amount" jdbcType="INTEGER" />
    <result column="unit_price" property="unitPrice" jdbcType="DECIMAL" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
    <result column="tire_brand" property="tireBrand" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, tire_id, amount, unit_price, created_by, created_time, last_modified_by, last_modified_time, 
    valid, tire_brand
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_vehicle_tire_no
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_vehicle_tire_no
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TVehicleTireNo" >
    insert into t_vehicle_tire_no (id, tire_id, amount,
      unit_price, created_by, created_time, 
      last_modified_by, last_modified_time, valid, 
      tire_brand)
    values (#{id,jdbcType=BIGINT}, #{tireId,jdbcType=BIGINT}, #{amount,jdbcType=INTEGER}, 
      #{unitPrice,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}, 
      #{tireBrand,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TVehicleTireNo" >
    insert into t_vehicle_tire_no
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="tireId != null" >
        tire_id,
      </if>
      <if test="amount != null" >
        amount,
      </if>
      <if test="unitPrice != null" >
        unit_price,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
      <if test="tireBrand != null" >
        tire_brand,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tireId != null" >
        #{tireId,jdbcType=BIGINT},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="unitPrice != null" >
        #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
      <if test="tireBrand != null" >
        #{tireBrand,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TVehicleTireNo" >
    update t_vehicle_tire_no
    <set >
      <if test="tireId != null" >
        tire_id = #{tireId,jdbcType=BIGINT},
      </if>
      <if test="amount != null" >
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="unitPrice != null" >
        unit_price = #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
      <if test="tireBrand != null" >
        tire_brand = #{tireBrand,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TVehicleTireNo" >
    update t_vehicle_tire_no
    set tire_id = #{tireId,jdbcType=BIGINT},
      amount = #{amount,jdbcType=INTEGER},
      unit_price = #{unitPrice,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER},
      tire_brand = #{tireBrand,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>