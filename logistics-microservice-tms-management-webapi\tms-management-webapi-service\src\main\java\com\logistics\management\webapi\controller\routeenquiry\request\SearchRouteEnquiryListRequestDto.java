package com.logistics.management.webapi.controller.routeenquiry.request;

import com.yelo.tray.core.page.AbstractPageForm;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/7/8 15:18
 */
@Data
public class SearchRouteEnquiryListRequestDto extends AbstractPageForm<SearchRouteEnquiryListRequestDto> {

    /**
     * 竞价状态：1 待业务审核，2 待车主确认，3 待结算审核，4 完成竞价，5 竞价取消
     */
    private String status;

    /**
     * 是否归档：0 否，1 是
     */
    private String ifArchive;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间-开始
     */
    private String createdTimeStart;
    /**
     * 创建时间-结束
     */
    private String createdTimeEnd;
}
