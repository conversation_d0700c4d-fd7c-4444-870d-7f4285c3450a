package com.logistics.tms.biz.sinopec;

import com.alibaba.fastjson.JSONObject;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.constant.ConfigKeyConstant;
import com.logistics.tms.base.enums.SinopecMethodEnum;
import com.logistics.tms.biz.sinopec.model.SinopecOrderQuotationRequestModel;
import com.logistics.tms.biz.sinopec.model.SinopecRefuseConsignOrderRequestModel;
import com.pcitc.paas.signature.SignatureClient;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.HttpClientUtils;
import com.yelo.tools.utils.UUIDGenerateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2021/12/5 10:10
 */
@Service
@Slf4j
public class SinopecBiz {

    @Autowired
    private ConfigKeyConstant configKeyConstant;

    /**
     * 接单报价
     * @param requestModel
     * @return
     */
    public String orderQuotation(SinopecOrderQuotationRequestModel requestModel){
        JSONObject params = getParams();
        params.put(SinopecCommon.RECEIVE_IFID, SinopecCommon.ORDER_QUOTATION_DEFAULT);
        params.put(SinopecCommon.RECEIVE_METHOD, SinopecCommon.ORDER_QUOTATION_DEFAULT);
        params.put(SinopecCommon.SEND_OPERATOR, requestModel.getOperator());
        params.put(SinopecCommon.ORDER_NO, requestModel.getOrderNo());
        params.put(SinopecCommon.SN, requestModel.getSn());
        params.put(SinopecCommon.TRANS_UNIT_PRICE, requestModel.getTransUnitPrice());
        params.put(SinopecCommon.DISPATCHER, requestModel.getDispatcher());
        params.put(SinopecCommon.DISPATCHER_PHONE_NO, requestModel.getDispatcherPhoneNo());
        return requestSinopecMethod(params, SinopecMethodEnum.ORDER_QUOTATION.getKey());
    }

    /**
     * 接单报价V1
     * @param requestModel
     * @return
     */
    public String orderQuotationV1(SinopecOrderQuotationRequestModel requestModel){
        JSONObject params = new JSONObject();
        params.put(SinopecCommon.CUSTOMER_ID, requestModel.getCustomerId());
        params.put(SinopecCommon.CUSTOMER_NAME, requestModel.getCustomerName());
        params.put(SinopecCommon.BUSINESS_ORDER_NUMBER, requestModel.getOrderNo());
        params.put(SinopecCommon.ASSOCIATED_NUMBER, requestModel.getSn());
        params.put(SinopecCommon.TRANS_UNIT_PRICE, requestModel.getTransUnitPrice());
        params.put(SinopecCommon.DISPATCHER, requestModel.getDispatcher());
        params.put(SinopecCommon.DISPATCHER_PHONE_NO, requestModel.getDispatcherPhoneNo());
        return requestSinopecMethod(params, SinopecMethodEnum.ORDER_QUOTATION_V1.getKey());
    }

    /**
     * 拒绝委托
     * @param requestModel
     * @return
     */
    public String refuseConsignOrder(SinopecRefuseConsignOrderRequestModel requestModel){
        JSONObject params = getParams();
        params.put(SinopecCommon.RECEIVE_IFID, SinopecCommon.REFUSE_CONSIGN_ORDER_DEFAULT);
        params.put(SinopecCommon.RECEIVE_METHOD, SinopecCommon.REFUSE_CONSIGN_ORDER_DEFAULT);
        params.put(SinopecCommon.SEND_OPERATOR, requestModel.getOperator());
        params.put(SinopecCommon.ORDER_NO, requestModel.getOrderNo());
        params.put(SinopecCommon.SN, requestModel.getSn());
        params.put(SinopecCommon.REASONS, requestModel.getReasons());
        params.put(SinopecCommon.CONTACT, requestModel.getContact());
        params.put(SinopecCommon.PHONE_NO, requestModel.getPhoneNo());
        return requestSinopecMethod(params, SinopecMethodEnum.REFUSE_CONSIGN_ORDER.getKey());
    }

    /**
     * 拒绝委托V1
     * @param requestModel
     * @return
     */
    public String refuseConsignOrderV1(SinopecRefuseConsignOrderRequestModel requestModel){
        JSONObject params = new JSONObject();
        params.put(SinopecCommon.CUSTOMER_ID, requestModel.getCustomerId());
        params.put(SinopecCommon.CUSTOMER_NAME, requestModel.getCustomerName());
        params.put(SinopecCommon.ASSOCIATED_NUMBER, requestModel.getSn());
        params.put(SinopecCommon.REFUSE_REASON, requestModel.getReasons());
        params.put(SinopecCommon.REFUSE_REASON_TYPE, requestModel.getRefuseReasonType());
        params.put(SinopecCommon.CONTACT, requestModel.getContact());
        params.put(SinopecCommon.PHONE_NO, requestModel.getPhoneNo());
        return requestSinopecMethod(params, SinopecMethodEnum.REFUSE_CONSIGN_ORDER_V1.getKey());
    }

    //获取固定参数
    private JSONObject getParams(){
        JSONObject params = new JSONObject();
        params.put(SinopecCommon.IM_GUID, UUIDGenerateUtil.generateUUID().toUpperCase());
        params.put(SinopecCommon.SEND_TIME, DateUtils.dateToString(new Date(), CommonConstant.YYYYMMDDHHMMSS));
        params.put(SinopecCommon.SENDER, SinopecCommon.SENDER_DEFAULT);
        params.put(SinopecCommon.RECEIVER, SinopecCommon.RECEIVER_DEFAULT);
        return params;
    }

    /**
     * 请求中石化接口
     * @param paramsJson 入参
     * @param method 请求url
     * @return 返回中石化请求结果
     */
    public String requestSinopecMethod(JSONObject paramsJson, String method){
        String requestParams = paramsJson.toJSONString();
        String url = configKeyConstant.sinopecBaseUrl + method;
        String result = null;
        try {
            log.info("调用中石化接口，url："+url+"，参数："+requestParams);
            //中石化小订单 SignatureClient.hmacSignature  ->  SignatureClient.hmacSignatureSha256
            Map<String, String> signatureMap = SignatureClient.hmacSignatureSha256(configKeyConstant.sinopecAppkey, configKeyConstant.sinopecSecretkey, "");
            signatureMap.remove("Content-Type");
            log.info("调用中石化接口，签名：" + signatureMap.toString());
            result = HttpClientUtils.requestWithJsonWithHeadersUsingPost(url, (HashMap<String, String>) signatureMap, requestParams);
            log.info("调用中石化接口，url："+url+"，返回结果："+result);
        }catch (Exception e){
            log.info("调用中石化接口失败！");
        }
        return result;
    }

}
