package com.logistics.tms.client.feign.tray.basicdata.commonaddress.hystrix;

import com.logistics.tms.client.feign.tray.basicdata.commonaddress.LeyiCommonAddressServiceApi;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.request.BatchQueryCustomerInfoRequest;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.request.UpdateAccessibilityVerifyRequestModel;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.response.AddressInfoResponseModel;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.response.BatchQueryCustomerInfoResponse;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/5/17 9:21
 */
@Component
public class LeyiCommonAddressServiceApiHystrix implements LeyiCommonAddressServiceApi {

    @Override
    public Result<Boolean> updateAccessibilityVerifyFromGroundPush(UpdateAccessibilityVerifyRequestModel requestModel) {
        return Result.timeout();
    }


    @Override
    public Result<List<BatchQueryCustomerInfoResponse>> batchQueryCustomerInfo(@Valid BatchQueryCustomerInfoRequest request) {
        return Result.timeout();
    }
}
