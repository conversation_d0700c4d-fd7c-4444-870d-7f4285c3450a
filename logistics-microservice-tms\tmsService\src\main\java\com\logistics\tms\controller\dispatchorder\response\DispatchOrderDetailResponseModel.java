package com.logistics.tms.controller.dispatchorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by yuhong.lin on 2019/1/18
 */
@Data
public class DispatchOrderDetailResponseModel {
    @ApiModelProperty("调度单ID")
    private Long dispatchOrderId;
    @ApiModelProperty("装载数")
    private Integer loadPointAmount;
    @ApiModelProperty("卸载数")
    private Integer unloadPointAmount;
    @ApiModelProperty("期待到达时间")
    private Date expectArrivalTime;
    @ApiModelProperty("车辆ID")
    private Long vehicleId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆机构 1 自主，2 外部，3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("司机ID")
    private Long driverId;
    @ApiModelProperty("司机名称")
    private String driverName;
    @ApiModelProperty("司机电话")
    private String driverMobile;
    @ApiModelProperty("司机身份证")
    private String driverIdentity;
    @ApiModelProperty("调度费用类型")
    private Integer dispatchFreightFeeType;
    @ApiModelProperty("调度费用")
    private BigDecimal dispatchFreightFee;
    @ApiModelProperty("多装多卸价")
    private BigDecimal markupFee;
    @ApiModelProperty("调整费用类型")
    private Integer adjustFeeType;
    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;
    @ApiModelProperty("调整说明")
    private String adjustRemark;
    @ApiModelProperty("是否调整费用0 否 1 是：")
    private Integer ifAdjust;
    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;
    @ApiModelProperty("备注")
    private String remark="";
    @ApiModelProperty("车主名")
    private String companyCarrierName;


    private List<CarrierOrderGoodsResponseModel> carrierOrderGoodsList;
    private List<TOperateLogsResponseModel> operateLogsList;
}
