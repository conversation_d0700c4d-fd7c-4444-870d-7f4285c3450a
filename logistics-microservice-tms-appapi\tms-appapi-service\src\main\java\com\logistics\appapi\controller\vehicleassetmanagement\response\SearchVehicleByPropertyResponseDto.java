package com.logistics.appapi.controller.vehicleassetmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/8/3 9:55
 */
@Data
public class SearchVehicleByPropertyResponseDto {

    @ApiModelProperty("车辆id")
    private String vehicleId="";

    @ApiModelProperty("车牌号")
    private String vehicleNo="";

    @ApiModelProperty("车辆机构：车辆机构：1 自主，2 外部，3 自营")
    private String vehicleProperty="";

    @ApiModelProperty("车辆类别 1 牵引车 2 挂车 3 一体车")
    private String vehicleCategory = "";
}
