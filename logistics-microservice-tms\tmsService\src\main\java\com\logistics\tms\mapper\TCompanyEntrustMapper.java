package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.controller.companyentrust.response.CompanyEntrustDetailResponseModel;
import com.logistics.tms.controller.companyentrust.response.SearchCompanyEntrustByNameResponseModel;
import com.logistics.tms.controller.companyentrust.request.SearchCompanyEntrustRequestModel;
import com.logistics.tms.controller.companyentrust.response.SearchCompanyEntrustResponseModel;
import com.logistics.tms.entity.TCompanyEntrust;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TCompanyEntrustMapper extends BaseMapper<TCompanyEntrust> {

    List<SearchCompanyEntrustResponseModel> searchList(@Param("params") SearchCompanyEntrustRequestModel requestModel);

    CompanyEntrustDetailResponseModel getDetail(@Param("companyEntrustId")Long companyEntrustId);

    List<SearchCompanyEntrustByNameResponseModel> searchCompanyEntrustByName(@Param("companyName")String companyName);

    TCompanyEntrust getByName(@Param("companyName")String companyName);
}