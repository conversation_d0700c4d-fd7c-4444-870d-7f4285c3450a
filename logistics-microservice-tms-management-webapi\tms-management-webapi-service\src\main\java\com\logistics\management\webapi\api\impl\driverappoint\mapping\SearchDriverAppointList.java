package com.logistics.management.webapi.api.impl.driverappoint.mapping;

import com.logistics.management.webapi.api.feign.driverappoint.dto.SearchDriverAppointResponseDto;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.enums.LifeBusinessTypeEnum;
import com.logistics.management.webapi.base.enums.StaffPropertyEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.driverappoint.model.SearchDriverAppointResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.util.Optional;


/**
 * <AUTHOR>
 * @date 2022/8/25 16:41
 */
public class SearchDriverAppointList extends MapperMapping<SearchDriverAppointResponseModel, SearchDriverAppointResponseDto> {


    @Override
    public void configure() {
        SearchDriverAppointResponseModel source = getSource();
        SearchDriverAppointResponseDto destination = getDestination();

        // 司机信息
        destination.setDriver(Optional.ofNullable(source.getStaffName()).orElse("") + " "
                + FrequentMethodUtils.encryptionData(source.getStaffMobile(), EncodeTypeEnum.MOBILE_PHONE));
        destination.setExportDriver(Optional.ofNullable(source.getStaffName()).orElse("")+" "+Optional.ofNullable(source.getStaffMobile()).orElse(""));
        // 司机机构
        destination.setStaffPropertyLabel(StaffPropertyEnum.getEnum(source.getStaffProperty()).getValue());
        // 业务类型
        destination.setBusinessLabel(CompanyTypeEnum.getEnum(source.getBusinessType()).getValue());
        // 发货地址
        destination.setLoadAddress(Optional.ofNullable(source.getLoadProvinceName()).orElse("")
                + Optional.ofNullable(source.getLoadCityName()).orElse("")
                + Optional.ofNullable(source.getLoadAreaName()).orElse("")
                + Optional.ofNullable(source.getLoadDetailAddress()).orElse("")
                + Optional.ofNullable(source.getLoadWarehouse()).orElse(""));
        // 发货人
        destination.setConsignorName(Optional.ofNullable(source.getConsignorName()).orElse("")
                + " "
                + Optional.of(FrequentMethodUtils.encryptionData(source.getConsignorMobile(), EncodeTypeEnum.MOBILE_PHONE)).orElse(""));
        destination.setExportConsignor(Optional.ofNullable(source.getConsignorName()).orElse("") + " "
                + Optional.ofNullable(source.getConsignorMobile()).orElse(""));

        //数量转换
        destination.setGoodsAmountTotal(source.getGoodsAmountTotal().stripTrailingZeros().toPlainString());

        //转换客户
        if(LifeBusinessTypeEnum.COMPANY.getKey().equals(source.getBusinessType())){
            destination.setCustomerName(source.getCustomerName());
            destination.setExportCustomer(source.getCustomerName());
        }else if(LifeBusinessTypeEnum.PERSONAGE.getKey().equals(source.getBusinessType())){
            destination.setCustomerName(Optional.of(source.getCustomerUserName()).orElse("")+" "+FrequentMethodUtils.encryptionData(source.getCustomerUserMobile(), EncodeTypeEnum.MOBILE_PHONE));
            destination.setExportCustomer(Optional.of(source.getCustomerUserName()).orElse("")+" "+Optional.of(source.getCustomerUserMobile()).orElse(""));
        }
    }
}
