package com.logistics.tms.mapper;

import com.logistics.tms.biz.shippingorder.model.ShippingOrderItemSqlConditionModel;
import com.logistics.tms.entity.TShippingOrderItem;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/08/06
*/
@Mapper
public interface TShippingOrderItemMapper extends BaseMapper<TShippingOrderItem> {

    List<TShippingOrderItem> selectByCondition(@Param("param1") ShippingOrderItemSqlConditionModel shippingOrderItemSqlConditionModel);

    List<TShippingOrderItem> getRoutePlan(@Param("shippingOrderId") Long shippingOrderId);

}