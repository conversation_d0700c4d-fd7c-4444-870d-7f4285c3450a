package com.logistics.tms.controller.vehicleassetmanagement.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date:2019/6/3 10:00
 */

@Data
public class VehicleAssetManagementListRequestModel extends AbstractPageForm<VehicleAssetManagementListRequestModel> {

    @ApiModelProperty("车辆类别 1 牵引车 2 挂车 3 一体车 4 牵引车或一体车")
    private Integer vehicleCategory;

    @ApiModelProperty("类型 1 自主，2 外部，3 自营")
    private Integer vehicleProperty;

    @ApiModelProperty("车辆使用性质：1 普货 2 危货")
    private Integer usageProperty;

    @ApiModelProperty("车辆类型")
    private String vehicleType;

    @ApiModelProperty("车辆类型")
    private List<String> vehicleTypeList;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("发动机号码")
    private String engineNumber;

    @ApiModelProperty("车辆识别号")
    private String vehicleIdentificationNumber;

    @ApiModelProperty("操作人")
    private String lastModifiedBy;

    @ApiModelProperty("操作时间")
    private String lastModifiedTimeFrom;

    @ApiModelProperty("操作时间")
    private String lastModifiedTimeTo;

    @ApiModelProperty("批量车辆基本信息Ids")
    private String vehicleBasicIds;

    @ApiModelProperty("停运状态 1 营运中 2 已停运")
    private Integer operatingState;

    @ApiModelProperty("车主名称或个人姓名手机号")
    private String companyCarrierName;

    @ApiModelProperty("看板跳转的IDS")
    private String ids;

    //车主id 内部条件筛选使用
    private Long companyCarrierId;

    @ApiModelProperty("我司数据,1:我司")
    private Integer isOurCompany;
}
