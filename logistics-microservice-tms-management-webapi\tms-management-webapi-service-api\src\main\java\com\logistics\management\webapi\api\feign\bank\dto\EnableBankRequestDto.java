package com.logistics.management.webapi.api.feign.bank.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: sj
 * @Date: 2019/7/10 14:12
 */
@Data
public class EnableBankRequestDto {
    @ApiModelProperty("银行ID")
    @NotBlank(message = "银行名称ID为空")
    private String bankId;
    @ApiModelProperty("是否禁用 1 启用  0 禁用")
    private String enable;
}
