<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCompanyAuthorizationMapper">
    <select id="selectByCompanyIdAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_company_authorization
        where valid = 1
        and company_id = #{companyId}
        and company_type = #{companyType,jdbcType=INTEGER}
    </select>

    <select id="selectCarrierAuthorizationList" resultType="com.logistics.tms.api.feign.companycarrierauthorization.model.response.CarrierAuthorizationListResponseModel">
        SELECT tc.company_name   as companyCarrierName,
               tca.id            as carrierAuthorizationId,
               tca.is_archive    as isArchived,
               tca.audit_status  as authorizationStatus,
               tca.created_by    as createdBy,
               tca.created_time  as createdTime,
               tca.archived_name as archivedUsername,
               tca.archived_time as archivedTime,
               tcc.type          as companyCarrierType
        FROM t_company_authorization tca
        INNER JOIN t_company_carrier tcc ON tcc.company_id = tca.company_id
            AND tcc.valid = 1
        INNER JOIN t_company tc ON tc.id = tcc.company_id
            AND tc.valid = 1
        WHERE tca.valid = 1
        <if test="params.companyCarrierName != null and params.companyCarrierName != ''">
            AND INSTR(tc.company_name, #{params.companyCarrierName})
        </if>
        <if test="params.authorizationStatus != null">
            AND tca.audit_status = #{params.authorizationStatus}
        </if>
        <if test="params.isArchived != null">
            AND tca.is_archive = #{params.isArchived}
        </if>
        <if test="params.archivedUsername != null and params.archivedUsername != ''">
            AND INSTR(tca.archived_name , #{params.archivedUsername})
        </if>
        <if test="params.archivedTimeStart != null">
            AND tca.archived_time >= DATE_FORMAT(#{params.archivedTimeStart}, '%Y-%m-%d 00:00:00')
        </if>
        <if  test="params.archivedTimeEnd != null">
            AND tca.archived_time &lt;= DATE_FORMAT(#{params.archivedTimeEnd}, '%Y-%m-%d 23:59:59')
        </if>
        ORDER BY tca.last_modified_time DESC, tca.id DESC
    </select>

    <select id="selectOneByCompanyId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_company_authorization
        WHERE valid = 1
        AND company_id = #{companyId}
    </select>

    <select id="selectOneById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_company_authorization
        WHERE valid = 1
        AND id = #{id}
    </select>

    <insert id="addCompanyAuthorization" parameterType="com.logistics.tms.entity.TCompanyAuthorization" useGeneratedKeys="true" keyProperty="id">
        insert into t_company_authorization
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="companyId != null">
                company_id,
            </if>
            <if test="companyType != null">
                company_type,
            </if>
            <if test="auditStatus != null">
                audit_status,
            </if>
            <if test="isArchive != null">
                is_archive,
            </if>
            <if test="auditorName != null">
                auditor_name,
            </if>
            <if test="auditTime != null">
                audit_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="archivedName != null">
                archived_name,
            </if>
            <if test="archivedTime != null">
                archived_time,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by,
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time,
            </if>
            <if test="valid != null">
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="companyType != null">
                #{companyType,jdbcType=INTEGER},
            </if>
            <if test="auditStatus != null">
                #{auditStatus,jdbcType=INTEGER},
            </if>
            <if test="isArchive != null">
                #{isArchive,jdbcType=INTEGER},
            </if>
            <if test="auditorName != null">
                #{auditorName,jdbcType=VARCHAR},
            </if>
            <if test="auditTime != null">
                #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="archivedName != null">
                #{archivedName,jdbcType=VARCHAR},
            </if>
            <if test="archivedTime != null">
                #{archivedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <select id="selectCarrierAuthorizationDetailById" resultType="com.logistics.tms.api.feign.companycarrierauthorization.model.response.CarrierAuthorizationDetailResponseModel">
        SELECT tc.company_name   as companyCarrierName,
               tca.id            as carrierAuthorizationId,
               tca.is_archive    as isArchived,
               tca.audit_status  as authorizationStatus,
               tca.created_by    as createdBy,
               tca.created_time  as createdTime,
               tca.auditor_name  as auditorName,
               tca.audit_time    as auditTime,
               tca.archived_name as archivedUsername,
               tca.archived_time as archivedTime,
               tca.remark        as remark,
               tcc.type          as companyCarrierType
        FROM t_company_authorization tca
        INNER JOIN t_company_carrier tcc ON tcc.company_id = tca.company_id AND tcc.valid = 1
        INNER JOIN t_company tc ON tc.id = tcc.company_id AND tc.valid = 1
        WHERE tca.valid = 1
            AND tca.id = #{carrierAuthorizationId}
    </select>

    <update id="updateCompanyAuthorization">
        update t_company_authorization
        <set>
            <if test="auditStatus != null">
                audit_status = #{auditStatus,jdbcType=INTEGER},
            </if>
            <if test="isArchive != null">
                is_archive = #{isArchive,jdbcType=INTEGER},
            </if>
            <if test="auditorName != null">
                auditor_name = #{auditorName,jdbcType=VARCHAR},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="archivedName != null">
                archived_name = #{archivedName,jdbcType=VARCHAR},
            </if>
            <if test="archivedTime != null">
                archived_time = #{archivedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>