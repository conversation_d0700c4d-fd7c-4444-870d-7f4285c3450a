package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.vehiclepayeerel.model.SearchVehiclePayeeRelListRequestModel;
import com.logistics.tms.api.feign.vehiclepayeerel.model.SearchVehiclePayeeRelListResponseModel;
import com.logistics.tms.api.feign.vehiclepayeerel.model.VehiclePayeeRelDetailResponseModel;
import com.logistics.tms.entity.TVehiclePayeeRel;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TVehiclePayeeRelMapper extends BaseMapper<TVehiclePayeeRel> {

    List<SearchVehiclePayeeRelListResponseModel> searchVehiclePayeeRelList(@Param("params") SearchVehiclePayeeRelListRequestModel requestModel);

    VehiclePayeeRelDetailResponseModel getVehiclePayeeRelDetail(@Param("id") Long id);

    List<TVehiclePayeeRel> getByIds(@Param("ids") String ids);

    TVehiclePayeeRel getByVehicleId(@Param("vehicleId") Long vehicleId);

    int batchInsert(@Param("list") List<TVehiclePayeeRel> list);

    int batchUpdate(@Param("list") List<TVehiclePayeeRel> list);

    List<TVehiclePayeeRel> getByDriverPayeeId(@Param("driverPayeeIds") String driverPayeeIds);

}