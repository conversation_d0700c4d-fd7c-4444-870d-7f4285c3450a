package com.logistics.tms.biz.demandorder.model;

import com.logistics.tms.rabbitmq.consumer.model.SyncTrayDemandOrderOrdersModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class SyncTMSDemandOrderModel {

    private String demandOrderCode;
    private String customerOrderCode;
    private String publishName;
    private Date publishTime;
    private BigDecimal goodsAmount;
    private Integer goodsUnit;
    private Integer entrustType;
    private String remark;
    private String companyEntrustName;
    private Integer contractPriceType;
    private BigDecimal contractPrice;
    private Integer ifUrgent;

    private Long loadProvinceId;
    private String loadProvinceName;
    private Long loadCityId;
    private String loadCityName;
    private Long loadAreaId;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    private String loadCompany;
    private String loadLongitude;
    private String loadLatitude;
    private String consignorName;
    private String consignorMobile;
    private Date expectedLoadTime;

    private Long unloadProvinceId;
    private String unloadProvinceName;
    private Long unloadCityId;
    private String unloadCityName;
    private Long unloadAreaId;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;
    @ApiModelProperty("收货公司")
    private String unloadCompany;
    private String unloadLongitude;
    private String unloadLatitude;
    private Integer unloadAddressIsAmend;
    private String receiverName;
    private String receiverMobile;
    private Date expectedUnloadTime;

    @ApiModelProperty("上游客户")
    private String upstreamCustomer;
    private List<SyncTMSDemandOrderGoodsModel> goodsModels;

    @ApiModelProperty("销售单的关联表")
    private List<SyncTrayDemandOrderOrdersModel> ordersModels;
}
