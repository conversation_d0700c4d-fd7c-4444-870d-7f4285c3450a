package com.logistics.management.webapi.controller.biddingorder.response;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/04/29
 */
@Data
public class BiddingOrderDetailDemandDto {
    /**
     * 发货地址
     */
    private String loadAddress = "";


    /**
     * 收货地址
     */
    private String unloadAddress = "";


    /**
     * 需求单号
     */
    private String demandOrderCode = "";

    /**
     * 需求单号
     */
    private String demandOrderId = "";


    /**
     * 数量
     */
    private String goodsCount = "";
}
