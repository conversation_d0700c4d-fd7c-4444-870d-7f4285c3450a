package com.logistics.management.webapi.client.biddingorder;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.biddingorder.hystrix.BiddingOrderClientHystrix;
import com.logistics.management.webapi.client.biddingorder.request.*;
import com.logistics.management.webapi.client.biddingorder.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 竞价管理
 *
 * <AUTHOR>
 * @date 2024/04/26
 */
@FeignClient(name = FeignClientName.TMS_SERVICES, path = "/service/biddingOrder",fallback = BiddingOrderClientHystrix.class)
public interface BiddingOrderClient {

    /**
     * 后台查询列表
     *
     * @param requestModel 请求参数
     * @return {@link Result}<{@link PageInfo}<{@link SearchBiddingOrderListResponseModel}>>
     */
    @PostMapping(value = "/searchBiddingOrderListByManager")
    Result<PageInfo<SearchBiddingOrderListResponseModel>> searchBiddingOrderListByManager(
            @RequestBody @Valid SearchBiddingOrderListRequestModel requestModel);

    /**
     * 竞价单详情
     * @param requestModel 请求参数
     * @return 竞价单详情
     */
    @PostMapping(value = "/biddingOrderDetailByManager")
    Result<BiddingOrderDetailByManagerResponseModel> biddingOrderDetailByManager(
            @RequestBody @Valid BiddingOrderDetailRequestModel requestModel);

    /**
     * 暂停报价
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/stopBiddingByManager")
    Result<Boolean> stopBiddingByManager(
            @RequestBody @Valid StopBiddingRequestModel requestModel);

    /**
     * 取消报价
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/cancelBiddingByManager")
    Result<Boolean> cancelBiddingByManager(
            @RequestBody @Valid CancelBiddingRequestModel requestModel);

    /**
     * 获取最低价 v3.20.0
     * @param requestModel 请求参数
     * @return 最低价
     */
    @PostMapping(value = "/bottomPrice")
    Result<BottomPriceResponseModel> bottomPrice(
            @RequestBody BottomPriceRequestModel requestModel);

    /**
     * 选择车主报价 v3.20.0
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/confirmQuote")
    Result<Boolean> confirmQuote(@RequestBody ConfirmQuoteRequestModel requestModel);

    /**
     * 修改报价 v3.20.0
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/modifyQuote")
    Result<Boolean> modifyQuote(@RequestBody ModifyQuoteRequestModel requestModel);

    /**
     * 重新报价 v3.20.0
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/rebiddingQuote")
    Result<Boolean> rebiddingQuote(@RequestBody RebiddingRequestModel requestModel);


    /**
     * 删除需求单 v3.20.0
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/delDemand")
    Result<Boolean> delDemand(
            @RequestBody DelDemandRequestModel requestModel);

    /**
     * 竞价查询需求单 v3.20.0
     * @param requestModel 请求参数
     * @return SearchBiddingDemandResponseModel
     */
    @PostMapping(value = "/searchBiddingDemand")
    Result<List<SearchBiddingDemandResponseModel>> searchBiddingDemand(
            @RequestBody SearchBiddingDemandRequestModel requestModel);


    /**
     * 车主报价详情 v3.20.0
     * @param requestModel 请求参数
     * @return 车主报价详情
     */
    @PostMapping(value = "/biddingOrderQuoteDetail")
    Result<BiddingOrderQuoteDetailResponseModel> biddingOrderQuoteDetail(
            @RequestBody BiddingOrderQuoteDetailRequestModel requestModel);

    /**
     * 新增需求单 v3.20.0
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/addDemand")
    Result<Boolean> addDemand(@RequestBody @Valid AddDemandRequestModel requestModel);


}
