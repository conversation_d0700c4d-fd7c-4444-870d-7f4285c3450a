package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-06-10 11:32
 */
@Data
public class CarrierOrderLoadAmountSyncModel {
    @ApiModelProperty("需求单ID")
    private Long demandOrderId;
    @ApiModelProperty("回退数量")
    private BigDecimal backAmount;
    @ApiModelProperty("货物信息")
    private List<CarrierOrderLoadGoodsAmountSyncModel> goodsList;
    @ApiModelProperty("对应S单退回数量信息")
    private List<CarrierOrderOrderRelSyncModel> carrierOrderRelList;
}
