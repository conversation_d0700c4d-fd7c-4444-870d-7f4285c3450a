package com.logistics.tms.mapper;

import com.logistics.tms.api.feign.loanrecord.model.LoanRecordListRequestModel;
import com.logistics.tms.api.feign.loanrecord.model.LoanRecordListResponseModel;
import com.logistics.tms.api.feign.loanrecord.model.SummaryLoanRecordResponseModel;
import com.logistics.tms.controller.vehiclesettlement.response.GetLoanFeeByVehicleIdResponseModel;
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleBySettlementMonthModel;
import com.logistics.tms.entity.TLoanRecords;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TLoanRecordsMapper extends BaseMapper<TLoanRecords> {
    TLoanRecords getLoanRecordsByVehicleId(@Param("vehicleId") Long vehicleId);

    List<LoanRecordListResponseModel> getLoanRecordList(@Param("condition") LoanRecordListRequestModel requestModel);

    SummaryLoanRecordResponseModel getSummaryLoanRecordInfo(@Param("condition") LoanRecordListRequestModel requestModel);

    GetLoanFeeByVehicleIdResponseModel getSettlementRecordsByIdForSettlement(@Param("id") Long id,@Param("deductingMonth")String deductingMonth);

    List<GetVehicleBySettlementMonthModel> getVehicleBySettlementMonth(@Param("settlementMonth") String settlementMonth);

    List<GetLoanFeeByVehicleIdResponseModel> getById(@Param("id")Long id);
}