package com.logistics.tms.rabbitmq.consumer.tms;

import com.logistics.tms.biz.demandorder.DemandOrderForLeYiBiz;
import com.logistics.tms.rabbitmq.consumer.model.SyncTrayDemandOrderMessage;
import com.rabbitmq.client.Channel;
import com.yelo.tools.rabbitmq.annocation.EnableMqErrorMessageCollect;
import com.yelo.tools.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;


@Component
@Slf4j
public class SaveDemandOrderFromLeyiTrayConsumer {

    private ObjectMapper objectMapper = JacksonUtils.getInstance();

    @Autowired
    private DemandOrderForLeYiBiz demandOrderForLeYiBiz;

    @EnableMqErrorMessageCollect
    @RabbitListener(bindings = {@QueueBinding(
            exchange = @Exchange(name = "logistics.topic", type = "topic", durable = "true"),
            value = @Queue(value = "logistics.syncTrayStockPlanToLogistics", durable = "true"),
            key = "syncTrayStockPlanToLogistics")}
            , concurrency = "3")
    public void process(@Payload String message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) throws IOException {
        log.info("云盘/云仓同步需求单到物流系统：" + message);
        SyncTrayDemandOrderMessage parse = objectMapper.readValue(message, SyncTrayDemandOrderMessage.class);
        demandOrderForLeYiBiz.saveSyncTrayStockPlanDemandOrderToTms(parse);
        channel.basicAck(deliveryTag, false);
    }

}
