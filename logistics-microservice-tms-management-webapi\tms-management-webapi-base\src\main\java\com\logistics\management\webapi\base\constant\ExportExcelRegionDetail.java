package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2021/10/25 10:18
 */
public class ExportExcelRegionDetail {
    public ExportExcelRegionDetail() {
    }

    private static final Map<String, String> EXPORT_REGION_DETAIL;

    static {
        EXPORT_REGION_DETAIL = new LinkedHashMap<>();
        EXPORT_REGION_DETAIL.put("大区名称", "regionName");
        EXPORT_REGION_DETAIL.put("省", "provinceName");
        EXPORT_REGION_DETAIL.put("市", "cityName");
    }

    public static Map<String, String> getExportExcelRegionDetail() {
        return EXPORT_REGION_DETAIL;
    }
}
