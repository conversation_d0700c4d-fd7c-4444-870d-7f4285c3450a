package com.logistics.tms.biz.basicinfo.common;

import com.alibaba.fastjson.JSONObject;
import com.logistics.tms.controller.baiscinfo.web.request.VerifyPersonThreeElementsRequestModel;
import com.logistics.tms.controller.baiscinfo.web.response.VerifyPersonThreeElementsResponseModel;
import com.logistics.tms.base.constant.BestSignCommon;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.authentication.AuthenticationBiz;
import com.logistics.tms.biz.basicinfo.common.model.BestSignUserRegModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.client.model.GetVerifyCodeRequestModel;
import com.logistics.tms.client.model.PersonalCredentialInfoModel;
import com.logistics.tms.client.model.RegisterUserRequestModel;
import com.logistics.tms.client.model.RegisterUserResponseModel;
import com.logistics.tms.entity.TCustomerBestsignCredentialInfo;
import com.logistics.tms.entity.TCustomerBestsignTaskInfo;
import com.logistics.tms.entity.TCustomerSmsAuthInfo;
import com.logistics.tms.entity.TRealNameAuthentication;
import com.logistics.tms.mapper.TCustomerBestsignCredentialInfoMapper;
import com.logistics.tms.mapper.TCustomerBestsignTaskInfoMapper;
import com.logistics.tms.mapper.TCustomerSmsAuthInfoMapper;
import com.logistics.tms.mapper.TRealNameAuthenticationMapper;
import com.yelo.life.basicdata.api.base.enums.bestsign.BestSignResultEnum;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tools.utils.UUIDGenerateUtil;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/11
 */
@Slf4j
@Service
public class BasicInfoCommonBiz {

	@Autowired
	private TRealNameAuthenticationMapper tRealNameAuthenticationMapper;
	@Autowired
	private TCustomerSmsAuthInfoMapper tCustomerSmsAuthInfoMapper;
	@Autowired
	private CommonBiz commonBiz;
	@Autowired
	private AuthenticationBiz authenticationBiz;
	@Autowired
	private TCustomerBestsignTaskInfoMapper tCustomerBestsignTaskInfoMapper;
	@Autowired
	private TCustomerBestsignCredentialInfoMapper tCustomerBestsignCredentialInfoMapper;

	/**
	 * 上上签获取验证码
	 *
	 * @param requestModel
	 */
	@Transactional
	public void bestSignGetVerifyCode(GetVerifyCodeRequestModel requestModel) {
		//三要素校验用户输入的手机号
		VerifyPersonThreeElementsRequestModel threeElementsRequestModel = new VerifyPersonThreeElementsRequestModel();
		threeElementsRequestModel.setName(requestModel.getName());
		threeElementsRequestModel.setIdentity(requestModel.getIdentity());
		threeElementsRequestModel.setMobile(requestModel.getMobile());
		VerifyPersonThreeElementsResponseModel threeElementsResponseModel = authenticationBiz.verifyPersonalThreeElements(threeElementsRequestModel);
		if (!CommonConstant.ONE.equals(threeElementsResponseModel.getResult())) {
			throw new BizException(CarrierDataExceptionEnum.THREE_ELEMENTS_VERIFY_FAIL);
		}
		//获取验证码
		JSONObject responseBody = authenticationBiz.getVerifyCode(requestModel);
		if (responseBody != null) {
			if (!BestSignResultEnum.SUCCESS.getKey().equals(responseBody.getString(BestSignCommon.RESULT))) {
				throw new BizException(CarrierDataExceptionEnum.VERIFICATION_CODE_ERROR.getCode(), responseBody.getString(BestSignCommon.MSG));
			}
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(new Date());
			calendar.set(Calendar.MINUTE, calendar.get(Calendar.MINUTE) + Integer.parseInt(BestSignCommon.IDENTITY_KEY_EXPIRED_TIME));
			TCustomerSmsAuthInfo customerSmsAuthInfo = new TCustomerSmsAuthInfo();
			customerSmsAuthInfo.setAccountId(BaseContextHandler.getUserId());
			customerSmsAuthInfo.setPersonalIdentityKey(responseBody.getString(BestSignCommon.PERSONAL_IDENTITY_KEY));
			customerSmsAuthInfo.setExpireTime(calendar.getTime());
			commonBiz.setBaseEntityAdd(customerSmsAuthInfo, BaseContextHandler.getUserName());
			tCustomerSmsAuthInfoMapper.insertSelective(customerSmsAuthInfo);
		}
	}

	/**
	 * 上上签用户注册
	 */
	@Transactional
	public void bestSignUserReg(BestSignUserRegModel model) {
		String redisKey = CommonConstant.REAL_NAME_AUTHENTICATION_KEY + model.getMobile();
		String distributedLock = commonBiz.getDistributedLock(redisKey);
		try {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(new Date());
			calendar.add(Calendar.DAY_OF_MONTH, 1);//+1今天的时间加一天
			//上上签注册model组装
			RegisterUserRequestModel registerUserRequestModel = new RegisterUserRequestModel();
			registerUserRequestModel.setUserType(CommonConstant.ONE);
			registerUserRequestModel.setName(model.getName());
			PersonalCredentialInfoModel personalCredentialInfoModel = new PersonalCredentialInfoModel();
			personalCredentialInfoModel.setIdentity(model.getIdentityNumber());
			registerUserRequestModel.setPersonalCredential(personalCredentialInfoModel);

			//查询是否已经存在实名认证记录
			TRealNameAuthentication tRealNameAuthentication = tRealNameAuthenticationMapper.selectRealNameAuthenticationByMobile(model.getMobile());
			if (tRealNameAuthentication == null) {
				//上上签注册
				String uuid = UUIDGenerateUtil.generateUUID();
				registerUserRequestModel.setAccount(uuid);
				RegisterUserResponseModel responseModel = authenticationBiz.registerUser(registerUserRequestModel);
				//新增实名认证记录
				TRealNameAuthentication tRealNameAuthenticationUp = new TRealNameAuthentication();
				tRealNameAuthenticationUp.setBestsignAccount(uuid);
				tRealNameAuthenticationUp.setAuthenticationType(model.getAuthenticationType());
				tRealNameAuthenticationUp.setMobile(model.getMobile());
				tRealNameAuthenticationUp.setName(model.getName());
				tRealNameAuthenticationUp.setIdentityNumber(model.getIdentityNumber());
				tRealNameAuthenticationUp.setAuthMode(model.getAuthMode());
				tRealNameAuthenticationUp.setCertificationStatus(CertificationStatusEnum.WAIT_AUTH.getKey());
				commonBiz.setBaseEntityAdd(tRealNameAuthenticationUp, BaseContextHandler.getUserName());
				tRealNameAuthenticationMapper.insertSelectiveEncrypt(tRealNameAuthenticationUp);

				//新增证书拉取任务
				TCustomerBestsignTaskInfo tCustomerBestsignTaskInfoNew = new TCustomerBestsignTaskInfo();
				tCustomerBestsignTaskInfoNew.setTaskId(responseModel.getTaskId());
				tCustomerBestsignTaskInfoNew.setTaskExpireTime(calendar.getTime());
				tCustomerBestsignTaskInfoNew.setCertApplyStatus(CertApplyStatusEnum.NEW_APPLY.getKey());
				tCustomerBestsignTaskInfoNew.setStatus(BestSignTaskInfoStatusEnum.WAIT_HANDLE.getKey());
				tCustomerBestsignTaskInfoNew.setRealNameAuthId(tRealNameAuthenticationUp.getId());
				tCustomerBestsignTaskInfoNew.setBestsignAccount(uuid);
				commonBiz.setBaseEntityAdd(tCustomerBestsignTaskInfoNew, BaseContextHandler.getUserName());
				tCustomerBestsignTaskInfoMapper.insertSelective(tCustomerBestsignTaskInfoNew);
			} else {
				TRealNameAuthentication tRealNameAuthenticationUp = new TRealNameAuthentication();
				tRealNameAuthenticationUp.setId(tRealNameAuthentication.getId());
				//如果是司机先认证,个人车主后认证,修改认证类型
				if (CommonConstant.INTEGER_ONE.equals(tRealNameAuthentication.getAuthenticationType())
						&& !model.getRegType().equals(tRealNameAuthentication.getAuthenticationType())) {
					tRealNameAuthenticationUp.setAuthenticationType(CommonConstant.INTEGER_TWO);
				}

				//未通过实名前更新认证信息
				if (!CommonConstant.INTEGER_TWO.equals(tRealNameAuthentication.getCertificationStatus())) {
					boolean isReRegister = false;//手机号变更不需要重新注册
					if (StringUtils.isNotBlank(model.getName()) && !model.getName().equals(tRealNameAuthentication.getName())) {
						tRealNameAuthenticationUp.setName(model.getName());
						isReRegister = true;
					}
					if (StringUtils.isNotBlank(model.getIdentityNumber()) && !model.getIdentityNumber().equals(tRealNameAuthentication.getIdentityNumber())) {
						tRealNameAuthenticationUp.setIdentityNumber(model.getIdentityNumber());
						isReRegister = true;
					}
					if (StringUtils.isNotBlank(model.getMobile()) && !model.getMobile().equals(tRealNameAuthentication.getMobile())) {
						tRealNameAuthenticationUp.setMobile(model.getMobile());
					}

					//重新注册上上签
					if (isReRegister) {
						//注册上上签
						registerUserRequestModel.setAccount(tRealNameAuthentication.getBestsignAccount());
						RegisterUserResponseModel responseModel = authenticationBiz.registerUser(registerUserRequestModel);
						//查询证书拉取任务信息
						List<TCustomerBestsignTaskInfo> tCustomerBestsignTaskInfos = tCustomerBestsignTaskInfoMapper.selectByRealNameIds(Collections.singletonList(tRealNameAuthentication.getId()));
						if (ListUtils.isNotEmpty(tCustomerBestsignTaskInfos)) {
							TCustomerBestsignTaskInfo tCustomerBestsignTaskInfo = tCustomerBestsignTaskInfos.get(CommonConstant.INTEGER_ZERO);
							//更新证书拉取任务
							TCustomerBestsignTaskInfo tCustomerBestsignTaskInfoUp = new TCustomerBestsignTaskInfo();
							tCustomerBestsignTaskInfoUp.setId(tCustomerBestsignTaskInfo.getId());
							tCustomerBestsignTaskInfoUp.setTaskId(responseModel.getTaskId());
							tCustomerBestsignTaskInfoUp.setTaskExpireTime(calendar.getTime());
							tCustomerBestsignTaskInfoUp.setCertApplyStatus(CertApplyStatusEnum.NEW_APPLY.getKey());
							tCustomerBestsignTaskInfoUp.setStatus(BestSignTaskInfoStatusEnum.WAIT_HANDLE.getKey());
							tCustomerBestsignTaskInfoUp.setBestsignAccount(tRealNameAuthentication.getBestsignAccount());
							commonBiz.setBaseEntityModify(tCustomerBestsignTaskInfoUp, BaseContextHandler.getUserName());
							tCustomerBestsignTaskInfoMapper.updateByPrimaryKeySelective(tCustomerBestsignTaskInfoUp);
						}
					}
				}
				commonBiz.setBaseEntityModify(tRealNameAuthenticationUp, BaseContextHandler.getUserName());
				tRealNameAuthenticationMapper.updateByPrimaryKeySelectiveEncrypt(tRealNameAuthenticationUp);
			}
		} catch (Exception e) {
			throw e;
		} finally {
			commonBiz.removeDistributedLock(redisKey.toString(), distributedLock);
		}
	}

	/**
	 * 每1分钟去上上签检查CA认证进度
	 */
	@Transactional
	public void checkAuthenticationProgressFromBestSign() {
		// 组装请求参数
		//只查询申请状态为新申请和申请中的数据
		//只查询申请状态为新申请和申请中的数据
		List<TCustomerBestsignTaskInfo> waitHandleList = tCustomerBestsignTaskInfoMapper.getCustomerBestSignTaskList(CertApplyStatusEnum.NEW_APPLY.getKey() + "," + CertApplyStatusEnum.APPLY_HANDLE.getKey(), null);
		if (ListUtils.isNotEmpty(waitHandleList)) {
			List<TCustomerBestsignTaskInfo> updateCertApplyStatusList = new ArrayList<>();
			List<Long> needToReRegisterIdList = new ArrayList<>();
			TCustomerBestsignTaskInfo tCustomerBestsignTaskInfo;
			for (TCustomerBestsignTaskInfo item : waitHandleList) {
				//根据用户唯一识别码和对应的taskId再去获取申请状态
				JSONObject jsonObject = authenticationBiz.asyncApplyCertStatus(item.getBestsignAccount(), item.getTaskId());
				if (jsonObject != null) {
					tCustomerBestsignTaskInfo = new TCustomerBestsignTaskInfo();
					tCustomerBestsignTaskInfo.setId(item.getId());
					switch (jsonObject.getString(BestSignCommon.STATUS)) {
						case "-2":
							tCustomerBestsignTaskInfo.setCertApplyStatus(CertApplyStatusEnum.SYSTEM_EXCEPTION.getKey());
							tCustomerBestsignTaskInfo.setStatus(BestSignTaskInfoStatusEnum.FINISH_HANDLE.getKey());
							break;
						case "-1":
							tCustomerBestsignTaskInfo.setCertApplyStatus(CertApplyStatusEnum.INVALID_APPLY.getKey());
							tCustomerBestsignTaskInfo.setStatus(BestSignTaskInfoStatusEnum.FINISH_HANDLE.getKey());
							break;
						case "0":
							tCustomerBestsignTaskInfo.setCertApplyStatus(CertApplyStatusEnum.TASK_ID_NO_EXIST_OR_OVERDUE.getKey());
							tCustomerBestsignTaskInfo.setStatus(BestSignTaskInfoStatusEnum.FINISH_HANDLE.getKey());
							break;
						case "1":
							tCustomerBestsignTaskInfo.setCertApplyStatus(CertApplyStatusEnum.NEW_APPLY.getKey());
							break;
						case "2":
							tCustomerBestsignTaskInfo.setCertApplyStatus(CertApplyStatusEnum.APPLY_HANDLE.getKey());
							break;
						case "3":
							tCustomerBestsignTaskInfo.setCertApplyStatus(CertApplyStatusEnum.APPLY_OVERTIME.getKey());
							break;
						case "4":
							log.info("【申请注册CA失败】=======【customerBestsignTaskInfoId=" + tCustomerBestsignTaskInfo.getId() + "】");
							tCustomerBestsignTaskInfo.setCertApplyStatus(CertApplyStatusEnum.APPLY_FAIL.getKey());
							tCustomerBestsignTaskInfo.setStatus(BestSignTaskInfoStatusEnum.FINISH_HANDLE.getKey());
							break;
						case "5":
							tCustomerBestsignTaskInfo.setCertApplyStatus(CertApplyStatusEnum.APPLY_SUCCESS.getKey());
							tCustomerBestsignTaskInfo.setStatus(BestSignTaskInfoStatusEnum.FINISH_HANDLE.getKey());
							break;
						default:
							break;
					}
					//申请失败，记录申请错误次数
					if (!CertApplyStatusEnum.NEW_APPLY.getKey().equals(tCustomerBestsignTaskInfo.getCertApplyStatus())
							&& !CertApplyStatusEnum.APPLY_HANDLE.getKey().equals(tCustomerBestsignTaskInfo.getCertApplyStatus())
							&& !CertApplyStatusEnum.APPLY_SUCCESS.getKey().equals(tCustomerBestsignTaskInfo.getCertApplyStatus())) {
						if (item.getApplyErrorCount() < CommonConstant.INTEGER_THREE) {//申请失败次数小于3次，则重新申请
							tCustomerBestsignTaskInfo.setCertApplyStatus(null);
							tCustomerBestsignTaskInfo.setStatus(null);
							tCustomerBestsignTaskInfo.setApplyErrorCount(item.getApplyErrorCount() + CommonConstant.INTEGER_ONE);
							//需要重新申请的客户
							needToReRegisterIdList.add(item.getRealNameAuthId());
						} else {//申请失败次数大于3次
							//删除该条注册信息
							tCustomerBestsignTaskInfo.setValid(IfValidEnum.INVALID.getKey());
						}
					}
					updateCertApplyStatusList.add(tCustomerBestsignTaskInfo);
				}
			}
			if (ListUtils.isNotEmpty(updateCertApplyStatusList)) {
				tCustomerBestsignTaskInfoMapper.batchUpdate(updateCertApplyStatusList);
			}
			if (ListUtils.isNotEmpty(needToReRegisterIdList)) {
				List<TRealNameAuthentication> tRealNameAuthenticationList = tRealNameAuthenticationMapper.selectByIds(needToReRegisterIdList);
				List<TCustomerBestsignTaskInfo> taskInfoList = tCustomerBestsignTaskInfoMapper.selectByRealNameIds(needToReRegisterIdList);
				Map<Long, Long> taskInfoIdMap = new HashMap<>();
				taskInfoList.forEach(item -> taskInfoIdMap.put(item.getRealNameAuthId(), item.getId()));

				if (ListUtils.isNotEmpty(tRealNameAuthenticationList)) {
					List<TCustomerBestsignTaskInfo> updateTaskIdList = new ArrayList<>();
					RegisterUserRequestModel registerUserRequestModel;
					TCustomerBestsignTaskInfo tCustomerBestsignTaskInfoUp;
					for (TRealNameAuthentication tRealNameAuthentication : tRealNameAuthenticationList) {
						registerUserRequestModel = new RegisterUserRequestModel();
						registerUserRequestModel.setAccount(tRealNameAuthentication.getBestsignAccount());
						registerUserRequestModel.setUserType(CommonConstant.ONE);
						registerUserRequestModel.setName(tRealNameAuthentication.getName());
						PersonalCredentialInfoModel personalCredentialInfoModel = new PersonalCredentialInfoModel();
						personalCredentialInfoModel.setIdentity(tRealNameAuthentication.getIdentityNumber());
						registerUserRequestModel.setPersonalCredential(personalCredentialInfoModel);
						//重新注册
						RegisterUserResponseModel responseModel = authenticationBiz.registerUser(registerUserRequestModel);
						//重新注册后更新taskId和过期时间
						Calendar calendar = Calendar.getInstance();
						calendar.setTime(new Date());
						calendar.add(Calendar.DAY_OF_MONTH, CommonConstant.INTEGER_ONE);//taskId时效24小时
						tCustomerBestsignTaskInfoUp = new TCustomerBestsignTaskInfo();
						tCustomerBestsignTaskInfoUp.setId(taskInfoIdMap.get(tRealNameAuthentication.getId()));
						tCustomerBestsignTaskInfoUp.setTaskId(responseModel.getTaskId());
						tCustomerBestsignTaskInfoUp.setTaskExpireTime(calendar.getTime());
						tCustomerBestsignTaskInfoUp.setCertApplyStatus(CertApplyStatusEnum.NEW_APPLY.getKey());
						tCustomerBestsignTaskInfoUp.setStatus(BestSignTaskInfoStatusEnum.WAIT_HANDLE.getKey());
						commonBiz.setBaseEntityModify(tCustomerBestsignTaskInfoUp, CommonConstant.SYSTEM_ADMIN);
						updateTaskIdList.add(tCustomerBestsignTaskInfoUp);
					}
					if (ListUtils.isNotEmpty(updateTaskIdList)) {
						tCustomerBestsignTaskInfoMapper.batchUpdate(updateTaskIdList);
					}
				}
			}
		}
	}

	/**
	 * 每1分钟去上上签拉取CA证书信息
	 */
	@Transactional
	public void fetchCACertificateFromBestSign() {
		//只查询申请状态为成功和处理完成的数据
		List<TCustomerBestsignTaskInfo> needHandleList = tCustomerBestsignTaskInfoMapper.getCustomerBestSignTaskList(ConverterUtils.toString(CertApplyStatusEnum.APPLY_SUCCESS.getKey()), BestSignTaskInfoStatusEnum.FINISH_HANDLE.getKey());
		if (ListUtils.isNotEmpty(needHandleList)) {
			List<TCustomerBestsignCredentialInfo> tCustomerBestsignCredentialInfoList = new ArrayList<>();
			TCustomerBestsignCredentialInfo tCustomerBestsignCredentialInfo;
			List<TCustomerBestsignTaskInfo> tCustomerBestsignTaskInfoList = new ArrayList<>();
			TCustomerBestsignTaskInfo tCustomerBestsignTaskInfo;
			for (TCustomerBestsignTaskInfo item : needHandleList) {
				//查询证书编号
				JSONObject responseBody = authenticationBiz.getCert(item.getBestsignAccount());
				if (responseBody != null) {
					tCustomerBestsignCredentialInfo = new TCustomerBestsignCredentialInfo();
					tCustomerBestsignCredentialInfo.setRealNameAuthId(item.getRealNameAuthId());
					tCustomerBestsignCredentialInfo.setBestsignAccount(item.getBestsignAccount());
					tCustomerBestsignCredentialInfo.setCertId(responseBody.getString(BestSignCommon.CERT_ID));
					commonBiz.setBaseEntityAdd(tCustomerBestsignCredentialInfo, CommonConstant.SYSTEM_ADMIN);
					tCustomerBestsignCredentialInfoList.add(tCustomerBestsignCredentialInfo);
					//修改证书的回填状态
					tCustomerBestsignTaskInfo = new TCustomerBestsignTaskInfo();
					tCustomerBestsignTaskInfo.setId(item.getId());
					tCustomerBestsignTaskInfo.setStatus(BestSignTaskInfoStatusEnum.CERT_BACKFILL.getKey());
					commonBiz.setBaseEntityModify(tCustomerBestsignTaskInfo, CommonConstant.SYSTEM_ADMIN);
					tCustomerBestsignTaskInfoList.add(tCustomerBestsignTaskInfo);
				}
			}
			SimpleDateFormat sdf = new SimpleDateFormat(CommonConstant.YYYY_MM_DD_HH_MM_SS);
			for (TCustomerBestsignCredentialInfo item : tCustomerBestsignCredentialInfoList) {
				//获取证书详细信息
				try {
					JSONObject responseBody = authenticationBiz.getCertInfo(item.getBestsignAccount(), item.getCertId());
					if (responseBody != null) {
						item.setSerialNumber(responseBody.getString(BestSignCommon.SERIAL_NUMBER));
						item.setIssuerDn(responseBody.getString(BestSignCommon.ISSUER_DN));
						if (StringUtils.isNotBlank(responseBody.getString(BestSignCommon.REVOKED_TIME))) {
							item.setRevokedTime(sdf.parse(responseBody.getString(BestSignCommon.REVOKED_TIME)));
						}
						if (StringUtils.isNotBlank(responseBody.getString(BestSignCommon.START_TIME))) {
							item.setStartTime(sdf.parse(responseBody.getString(BestSignCommon.START_TIME)));
						}
						if (StringUtils.isNotBlank(responseBody.getString(BestSignCommon.STOP_TIME))) {
							item.setStopTime(sdf.parse(responseBody.getString(BestSignCommon.STOP_TIME)));
						}
						item.setRevokedReason(responseBody.getString(BestSignCommon.REVOKED_REASON));
						item.setSubjectDn(responseBody.getString(BestSignCommon.SUBJECT_DN));
						item.setStatus(ConverterUtils.toInt(responseBody.getString(BestSignCommon.STATUS)));
					}
				} catch (Exception e) {
					log.info("fetchCACertificateFromBestSign error: ", e);
				}
			}

			if (ListUtils.isNotEmpty(tCustomerBestsignTaskInfoList)) {
				tCustomerBestsignTaskInfoMapper.batchUpdate(tCustomerBestsignTaskInfoList);
			}
			if (ListUtils.isNotEmpty(tCustomerBestsignCredentialInfoList)) {
				tCustomerBestsignCredentialInfoMapper.batchInsertSelective(tCustomerBestsignCredentialInfoList);
			}
		}
	}

	/**
	 * 上上签校验验证码
	 *
	 * @param verificationCode 验证码
	 */
	@Transactional
	public void bestsignVerificationCodeAuth(String verificationCode) {
		TCustomerSmsAuthInfo tCustomerSmsAuthInfo = tCustomerSmsAuthInfoMapper.verificationCode(BaseContextHandler.getUserId());
		if (tCustomerSmsAuthInfo == null) {
			throw new BizException(CarrierDataExceptionEnum.VERIFICATION_CODE_ERROR);
		}
		JSONObject responseBody = authenticationBiz.checkVerifyCode(verificationCode, tCustomerSmsAuthInfo.getPersonalIdentityKey());
		if (responseBody == null) {
			throw new BizException(CarrierDataExceptionEnum.VERIFICATION_CODE_ERROR);
		}
		if (!CommonConstant.ONE.equals(responseBody.getString(BestSignCommon.RESULT))) {
			throw new BizException(CarrierDataExceptionEnum.VERIFICATION_CODE_ERROR);
		}
		TCustomerSmsAuthInfo customerSmsAuthInfo = new TCustomerSmsAuthInfo();
		customerSmsAuthInfo.setId(tCustomerSmsAuthInfo.getId());
		customerSmsAuthInfo.setWhetherUse(CommonConstant.INTEGER_ONE);
		customerSmsAuthInfo.setRemark(CommonConstant.VERIFY_CODE_USER);
		commonBiz.setBaseEntityModify(customerSmsAuthInfo, BaseContextHandler.getUserName());
		tCustomerSmsAuthInfoMapper.updateByPrimaryKeySelective(customerSmsAuthInfo);
	}

	/**
	 * 查询上上签人脸识别结果
	 *
	 * @param orderNo 流水号
	 */
	public void bestsignFaceAuth(String orderNo) {
		JSONObject responseBody = authenticationBiz.verifyFaceRecognitionResult(orderNo);
		if (responseBody == null) {
			throw new BizException(CarrierDataExceptionEnum.FACE_AUTH_ERROR);
		}
		if (!CommonConstant.ONE.equals(responseBody.getString(BestSignCommon.VERIFY_RESULT))) {
			throw new BizException(CarrierDataExceptionEnum.FACE_AUTH_ERROR);
		}
	}
}
