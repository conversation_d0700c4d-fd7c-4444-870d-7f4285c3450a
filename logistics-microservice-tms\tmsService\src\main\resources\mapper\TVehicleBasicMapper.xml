<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TVehicleBasicMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TVehicleBasic">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="usage_property" jdbcType="INTEGER" property="usageProperty" />
    <result column="if_install_gps" jdbcType="INTEGER" property="ifInstallGps" />
    <result column="if_access_sinopec" jdbcType="INTEGER" property="ifAccessSinopec" />
    <result column="vehicle_property" jdbcType="INTEGER" property="vehicleProperty" />
    <result column="vehicle_owner" jdbcType="VARCHAR" property="vehicleOwner" />
    <result column="connect_time" jdbcType="TIMESTAMP" property="connectTime" />
    <result column="connect_way" jdbcType="INTEGER" property="connectWay" />
    <result column="authentication_start_time" jdbcType="TIMESTAMP" property="authenticationStartTime" />
    <result column="authentication_expire_time" jdbcType="TIMESTAMP" property="authenticationExpireTime" />
    <result column="registration_certification_number" jdbcType="VARCHAR" property="registrationCertificationNumber" />
    <result column="emission_standard_type" jdbcType="INTEGER" property="emissionStandardType" />
    <result column="operating_state" jdbcType="INTEGER" property="operatingState" />
    <result column="shut_down_reason" jdbcType="VARCHAR" property="shutDownReason" />
    <result column="loading_capacity" jdbcType="INTEGER" property="loadingCapacity" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, usage_property, if_install_gps, if_access_sinopec, vehicle_property, vehicle_owner, 
    connect_time, connect_way, authentication_start_time, authentication_expire_time, 
    registration_certification_number, emission_standard_type, operating_state, shut_down_reason, 
    loading_capacity, source, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_vehicle_basic
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_vehicle_basic
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TVehicleBasic">
    insert into t_vehicle_basic (id, usage_property, if_install_gps, 
      if_access_sinopec, vehicle_property, vehicle_owner, 
      connect_time, connect_way, authentication_start_time, 
      authentication_expire_time, registration_certification_number, 
      emission_standard_type, operating_state, shut_down_reason, 
      loading_capacity, source, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{usageProperty,jdbcType=INTEGER}, #{ifInstallGps,jdbcType=INTEGER}, 
      #{ifAccessSinopec,jdbcType=INTEGER}, #{vehicleProperty,jdbcType=INTEGER}, #{vehicleOwner,jdbcType=VARCHAR}, 
      #{connectTime,jdbcType=TIMESTAMP}, #{connectWay,jdbcType=INTEGER}, #{authenticationStartTime,jdbcType=TIMESTAMP}, 
      #{authenticationExpireTime,jdbcType=TIMESTAMP}, #{registrationCertificationNumber,jdbcType=VARCHAR}, 
      #{emissionStandardType,jdbcType=INTEGER}, #{operatingState,jdbcType=INTEGER}, #{shutDownReason,jdbcType=VARCHAR}, 
      #{loadingCapacity,jdbcType=INTEGER}, #{source,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TVehicleBasic" keyProperty="id" useGeneratedKeys="true">
    insert into t_vehicle_basic
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="usageProperty != null">
        usage_property,
      </if>
      <if test="ifInstallGps != null">
        if_install_gps,
      </if>
      <if test="ifAccessSinopec != null">
        if_access_sinopec,
      </if>
      <if test="vehicleProperty != null">
        vehicle_property,
      </if>
      <if test="vehicleOwner != null">
        vehicle_owner,
      </if>
      <if test="connectTime != null">
        connect_time,
      </if>
      <if test="connectWay != null">
        connect_way,
      </if>
      <if test="authenticationStartTime != null">
        authentication_start_time,
      </if>
      <if test="authenticationExpireTime != null">
        authentication_expire_time,
      </if>
      <if test="registrationCertificationNumber != null">
        registration_certification_number,
      </if>
      <if test="emissionStandardType != null">
        emission_standard_type,
      </if>
      <if test="operatingState != null">
        operating_state,
      </if>
      <if test="shutDownReason != null">
        shut_down_reason,
      </if>
      <if test="loadingCapacity != null">
        loading_capacity,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="usageProperty != null">
        #{usageProperty,jdbcType=INTEGER},
      </if>
      <if test="ifInstallGps != null">
        #{ifInstallGps,jdbcType=INTEGER},
      </if>
      <if test="ifAccessSinopec != null">
        #{ifAccessSinopec,jdbcType=INTEGER},
      </if>
      <if test="vehicleProperty != null">
        #{vehicleProperty,jdbcType=INTEGER},
      </if>
      <if test="vehicleOwner != null">
        #{vehicleOwner,jdbcType=VARCHAR},
      </if>
      <if test="connectTime != null">
        #{connectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="connectWay != null">
        #{connectWay,jdbcType=INTEGER},
      </if>
      <if test="authenticationStartTime != null">
        #{authenticationStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="authenticationExpireTime != null">
        #{authenticationExpireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="registrationCertificationNumber != null">
        #{registrationCertificationNumber,jdbcType=VARCHAR},
      </if>
      <if test="emissionStandardType != null">
        #{emissionStandardType,jdbcType=INTEGER},
      </if>
      <if test="operatingState != null">
        #{operatingState,jdbcType=INTEGER},
      </if>
      <if test="shutDownReason != null">
        #{shutDownReason,jdbcType=VARCHAR},
      </if>
      <if test="loadingCapacity != null">
        #{loadingCapacity,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TVehicleBasic">
    update t_vehicle_basic
    <set>
      <if test="usageProperty != null">
        usage_property = #{usageProperty,jdbcType=INTEGER},
      </if>
      <if test="ifInstallGps != null">
        if_install_gps = #{ifInstallGps,jdbcType=INTEGER},
      </if>
      <if test="ifAccessSinopec != null">
        if_access_sinopec = #{ifAccessSinopec,jdbcType=INTEGER},
      </if>
      <if test="vehicleProperty != null">
        vehicle_property = #{vehicleProperty,jdbcType=INTEGER},
      </if>
      <if test="vehicleOwner != null">
        vehicle_owner = #{vehicleOwner,jdbcType=VARCHAR},
      </if>
      <if test="connectTime != null">
        connect_time = #{connectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="connectWay != null">
        connect_way = #{connectWay,jdbcType=INTEGER},
      </if>
      <if test="authenticationStartTime != null">
        authentication_start_time = #{authenticationStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="authenticationExpireTime != null">
        authentication_expire_time = #{authenticationExpireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="registrationCertificationNumber != null">
        registration_certification_number = #{registrationCertificationNumber,jdbcType=VARCHAR},
      </if>
      <if test="emissionStandardType != null">
        emission_standard_type = #{emissionStandardType,jdbcType=INTEGER},
      </if>
      <if test="operatingState != null">
        operating_state = #{operatingState,jdbcType=INTEGER},
      </if>
      <if test="shutDownReason != null">
        shut_down_reason = #{shutDownReason,jdbcType=VARCHAR},
      </if>
      <if test="loadingCapacity != null">
        loading_capacity = #{loadingCapacity,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TVehicleBasic">
    update t_vehicle_basic
    set usage_property = #{usageProperty,jdbcType=INTEGER},
      if_install_gps = #{ifInstallGps,jdbcType=INTEGER},
      if_access_sinopec = #{ifAccessSinopec,jdbcType=INTEGER},
      vehicle_property = #{vehicleProperty,jdbcType=INTEGER},
      vehicle_owner = #{vehicleOwner,jdbcType=VARCHAR},
      connect_time = #{connectTime,jdbcType=TIMESTAMP},
      connect_way = #{connectWay,jdbcType=INTEGER},
      authentication_start_time = #{authenticationStartTime,jdbcType=TIMESTAMP},
      authentication_expire_time = #{authenticationExpireTime,jdbcType=TIMESTAMP},
      registration_certification_number = #{registrationCertificationNumber,jdbcType=VARCHAR},
      emission_standard_type = #{emissionStandardType,jdbcType=INTEGER},
      operating_state = #{operatingState,jdbcType=INTEGER},
      shut_down_reason = #{shutDownReason,jdbcType=VARCHAR},
      loading_capacity = #{loadingCapacity,jdbcType=INTEGER},
      source = #{source,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>