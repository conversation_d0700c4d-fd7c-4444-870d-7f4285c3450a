package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/1
 */
@Data
public class EditCarrierOrderCostDetailRequestDto {

	@ApiModelProperty(value = "运单ID", required = true)
	@NotBlank(message = "运单ID不能为空")
	private String carrierOrderId;

	@ApiModelProperty(value = "费用类型 1:货主费用 2:车主费用", required = true)
	@NotBlank(message = "请选择要编辑的费用类型")
	private String costType;
}
