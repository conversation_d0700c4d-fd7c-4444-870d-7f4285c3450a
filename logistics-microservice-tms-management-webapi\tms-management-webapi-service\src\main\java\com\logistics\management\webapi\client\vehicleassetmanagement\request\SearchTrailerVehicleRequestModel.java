package com.logistics.management.webapi.client.vehicleassetmanagement.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SearchTrailerVehicleRequestModel extends AbstractPageForm<SearchTrailerVehicleRequestModel> {

    @ApiModelProperty("挂车车牌号")
    private String trailerVehicleNo;

    @ApiModelProperty("车主id")
    private Long companyCarrierId;

    @ApiModelProperty("来源：1 后台，2 前台")
    private Integer requestSource;
}
