<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TAttendanceAskLeaveMapper">
    <!--auto generated by <PERSON><PERSON><PERSON>CodeHelper on 2022-11-22-->
    <update id="updateByIdAndAuditStatus">
        update t_attendance_ask_leave
        <set>
            <if test="updated.id != null">
                id = #{updated.id,jdbcType=BIGINT},
            </if>
            <if test="updated.staffId != null">
                staff_id = #{updated.staffId,jdbcType=BIGINT},
            </if>
            <if test="updated.staffName != null">
                staff_name = #{updated.staffName,jdbcType=VARCHAR},
            </if>
            <if test="updated.staffMobile != null">
                staff_mobile = #{updated.staffMobile,jdbcType=VARCHAR},
            </if>
            <if test="updated.staffProperty != null">
                staff_property = #{updated.staffProperty,jdbcType=INTEGER},
            </if>
            <if test="updated.leaveType != null">
                leave_type = #{updated.leaveType,jdbcType=INTEGER},
            </if>
            <if test="updated.leaveStartTime != null">
                leave_start_time = #{updated.leaveStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.leaveStartTimeType != null">
                leave_start_time_type = #{updated.leaveStartTimeType,jdbcType=INTEGER},
            </if>
            <if test="updated.leaveEndTime != null">
                leave_end_time = #{updated.leaveEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.leaveEndTimeType != null">
                leave_end_time_type = #{updated.leaveEndTimeType,jdbcType=INTEGER},
            </if>
            <if test="updated.leaveDuration != null">
                leave_duration = #{updated.leaveDuration,jdbcType=DECIMAL},
            </if>
            <if test="updated.leaveReason != null">
                leave_reason = #{updated.leaveReason,jdbcType=VARCHAR},
            </if>
            <if test="updated.applyTime != null">
                apply_time = #{updated.applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.auditStatus != null">
                audit_status = #{updated.auditStatus,jdbcType=INTEGER},
            </if>
            <if test="updated.auditorName != null">
                auditor_name = #{updated.auditorName,jdbcType=VARCHAR},
            </if>
            <if test="updated.auditTime != null">
                audit_time = #{updated.auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.remark != null">
                remark = #{updated.remark,jdbcType=VARCHAR},
            </if>
            <if test="updated.createdBy != null">
                created_by = #{updated.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="updated.createdTime != null">
                created_time = #{updated.createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.lastModifiedBy != null">
                last_modified_by = #{updated.lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="updated.lastModifiedTime != null">
                last_modified_time = #{updated.lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.valid != null">
                valid = #{updated.valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
          and audit_status = #{auditStatus,jdbcType=INTEGER}
    </update>

    <!--auto generated by MybatisCodeHelper on 2022-11-22-->
    <select id="selectOneById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_attendance_ask_leave
        where id = #{id,jdbcType=BIGINT}
          and `valid` = 1
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-11-22-->
    <select id="selectLeaveApplyList"
            resultType="com.logistics.tms.controller.leave.response.DriverLeaveApplyListItemModel">
        select id                    leaveApplyId,
               staff_name            staffName,
               staff_mobile          staffMobile,
               leave_type            leaveType,
               leave_start_time      leaveStartTime,
               leave_start_time_type leaveStartTimeType,
               leave_end_time        leaveEndTime,
               leave_end_time_type   leaveEndTimeType,
               audit_status          leaveAuditStatus
        from t_attendance_ask_leave
        where valid = 1
          and date_format(apply_time, '%Y-%m') = #{applyTime}
          and staff_id = #{staffId}
        order by apply_time desc, id desc
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-11-22-->
    <select id="selectLeaveApplyListStatistical" resultType="java.lang.Long">
        select count(1)
        from t_attendance_ask_leave
        where valid = 1
          and staff_id = #{staffId,jdbcType=BIGINT}
          and audit_status in (0, 1)
          and date_format(apply_time, '%Y-%m') = #{applyTime}
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-11-23-->
    <select id="selectDriverLeaveApplyDetail"
            resultType="com.logistics.tms.controller.leave.response.DriverLeaveApplyDetailResponseModel">
        select id                    leaveApplyId,
               staff_name            staffName,
               staff_mobile          staffMobile,
               leave_type            leaveType,
               leave_start_time      leaveStartTime,
               leave_start_time_type leaveStartTimeType,
               leave_end_time        leaveEndTime,
               leave_end_time_type   leaveEndTimeType,
               audit_status          leaveAuditStatus,
               leave_duration        leaveDuration,
               leave_reason          leaveReason,
               audit_time            auditTime,
               remark
        from t_attendance_ask_leave
        where valid = 1
          and id = #{id,jdbcType=BIGINT}
          and staff_id = #{staffId,jdbcType=BIGINT}
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-11-24-->
    <select id="searchLeaveApplyList"
            resultType="com.logistics.tms.controller.leave.response.LeaveApplySearchListResponseModel">
        select id                    leaveApplyId,
               staff_name            staffName,
               staff_mobile          staffMobile,
               staff_property        staffProperty,
               leave_type            leaveType,
               leave_start_time      leaveStartTime,
               leave_start_time_type leaveStartTimeType,
               leave_end_time        leaveEndTime,
               leave_end_time_type   leaveEndTimeType,
               audit_status          leaveAuditStatus,
               leave_duration        leaveDuration,
               leave_reason          leaveReason,
               auditor_name          auditorName,
               audit_time            auditTime
        from t_attendance_ask_leave
        where `valid` = 1
        <if test="params.leaveAuditStatus != null">
            and audit_status = #{params.leaveAuditStatus}
        </if>
        <if test="params.staffProperty != null">
            and staff_property = #{params.staffProperty}
        </if>
        <if test="params.leaveApplyStartTime != '' and params.leaveApplyStartTime != null">
            <if test="params.leaveApplyEndTime != '' and params.leaveApplyEndTime != null">
                and date(apply_time) between #{params.leaveApplyStartTime} and #{params.leaveApplyEndTime}
            </if>
        </if>
        <if test="params.leaveApplyUser != null and params.leaveApplyUser != ''">
            and (instr(staff_name, #{params.leaveApplyUser}) or
                 instr(staff_mobile, #{params.leaveApplyUser}))
        </if>
        order by last_modified_time desc, id desc
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-11-25-->
    <select id="selectOneByIdAndStaffId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_attendance_ask_leave
        where `valid` = 1
          and id = #{id,jdbcType=BIGINT}
          and staff_id = #{staffId,jdbcType=BIGINT}
    </select>

    <select id="selectLeaveApplyDetail"
            resultType="com.logistics.tms.controller.leave.response.LeaveApplyDetailResponseModel">
        select id                    leaveApplyId,
               staff_name            staffName,
               staff_mobile          staffMobile,
               leave_type            leaveType,
               leave_start_time      leaveStartTime,
               leave_start_time_type leaveStartTimeType,
               leave_end_time        leaveEndTime,
               leave_end_time_type   leaveEndTimeType,
               audit_status          leaveAuditStatus,
               leave_duration        leaveDuration,
               leave_reason          leaveReason,
               audit_time            auditTime,
               auditor_name          auditorName,
               remark
        from t_attendance_ask_leave
        where valid = 1
          and id = #{id,jdbcType=BIGINT}
    </select>

<!--auto generated by MybatisCodeHelper on 2022-11-28-->
    <select id="selectAllByAuditStatusIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_attendance_ask_leave
        where
        staff_id = #{staffId}
        and valid = 1
        and audit_status in
        <foreach item="item" index="index" collection="auditStatusCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>
</mapper>