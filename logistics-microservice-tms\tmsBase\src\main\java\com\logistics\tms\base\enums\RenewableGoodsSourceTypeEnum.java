package com.logistics.tms.base.enums;

/**
 * 新生商品货物来源枚举
 */
public enum RenewableGoodsSourceTypeEnum {

    YELOLIFE_SYNC(1,"新生同步"),
    DRIVER_CONFIRM(2,"司机确认");

    private Integer key;
    private String value;

    RenewableGoodsSourceTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
