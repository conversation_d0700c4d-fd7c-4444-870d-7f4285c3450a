package com.logistics.tms.biz.messagenotice.model;

import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2024/6/13 9:50
 */
@Data
public class ReadMessageNoticeModel {

    /**
     * 消息id（不传则全部置为已读）
     */
    private String messageId;

    /**
     * 消息推送人（后台为空）
     */
    private Long messageReceiver;

    /**
     * 操作人
     */
    private String userName;

    /**
     * 操作时间
     */
    private Date updateTime;

    /**
     * 消息模块：1 后台，2 前台
     */
    private Integer messageModule;
}
