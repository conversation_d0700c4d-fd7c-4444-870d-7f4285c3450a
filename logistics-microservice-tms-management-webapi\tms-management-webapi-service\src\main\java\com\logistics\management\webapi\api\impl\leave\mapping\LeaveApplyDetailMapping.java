package com.logistics.management.webapi.api.impl.leave.mapping;

import com.logistics.management.webapi.api.feign.leave.dto.LeaveDetailResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.LeaveApplyAuditStatusEnum;
import com.logistics.management.webapi.base.enums.LeaveApplyTimeTypeEnum;
import com.logistics.management.webapi.base.enums.LeaveApplyTypeEnum;
import com.logistics.tms.api.feign.leave.model.LeaveApplyDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

import java.util.Date;

public class LeaveApplyDetailMapping extends MapperMapping<LeaveApplyDetailResponseModel, LeaveDetailResponseDto> {

    @Override
    public void configure() {

        LeaveApplyDetailResponseModel source = getSource();
        LeaveDetailResponseDto destination = getDestination();

        // 名字转换
        String leaveApplyStaff = String.format(CommonConstant.NAME_MOBILE_FORMAT, source.getStaffName(), source.getStaffMobile());
        destination.setLeaveApplyStaff(leaveApplyStaff);

        // 审核状态转换
        destination.setLeaveAuditStatusLabel(LeaveApplyAuditStatusEnum.getEnumByKey(source.getLeaveAuditStatus()).getValue());

        // 请假类型转换
        destination.setLeaveTypeLabel(LeaveApplyTypeEnum.getEnumByKey(source.getLeaveType()).getValue());

        // 请假时间转换
        destination.setLeaveStartTime(leaveTimeConversion(source.getLeaveStartTime(), source.getLeaveStartTimeType()));
        destination.setLeaveEndTime(leaveTimeConversion(source.getLeaveEndTime(), source.getLeaveEndTimeType()));

        // 请假时长转换
        String duration = source.getLeaveDuration().stripTrailingZeros().toPlainString();
        destination.setLeaveDuration(duration.concat(CommonConstant.DAY_TEXT));
    }

    private String leaveTimeConversion(Date leaveDate, Integer leaveDateType) {
        String leaveDateLabel = DateUtils.dateToString(leaveDate, DateUtils.DATE_TO_STRING_SHORT_PATTERN);
        String leaveDateTypeLabel = LeaveApplyTimeTypeEnum.getEnumByKey(leaveDateType).getValue();
        return String.join(" ", leaveDateLabel, leaveDateTypeLabel);
    }
}
