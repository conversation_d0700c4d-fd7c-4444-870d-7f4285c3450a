package com.logistics.tms.api.feign.insuarance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/11/1
 * @description:
 */
@Data
public class InsuranceSettlementCostsRelationModel {

    @ApiModelProperty(value = "保险费用Id")
    private Long insuranceCostsId;
    @ApiModelProperty(value = "保单类型")
    private Integer insuranceType;
    @ApiModelProperty(value = "结算月份")
    private String insuranceMonth;


}
