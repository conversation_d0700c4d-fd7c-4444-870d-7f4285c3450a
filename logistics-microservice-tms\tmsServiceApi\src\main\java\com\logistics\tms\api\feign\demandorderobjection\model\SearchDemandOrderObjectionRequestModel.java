package com.logistics.tms.api.feign.demandorderobjection.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchDemandOrderObjectionRequestModel extends AbstractPageForm<SearchDemandOrderObjectionRequestModel> {
    @ApiModelProperty("需求单状态：空 全部  3000调度完成 1取消 2放空 3回退")
    private Integer demandStatus;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("下单时间开始")
    private String publishTimeStart;
    @ApiModelProperty("下单时间结束")
    private String publishTimeEnd;
    @ApiModelProperty("地区（提货大区）")
    private String loadRegionName;
    @ApiModelProperty("上报时间开始")
    private String reportTimeStart;
    @ApiModelProperty("上报时间结束")
    private String reportTimeEnd;
}
