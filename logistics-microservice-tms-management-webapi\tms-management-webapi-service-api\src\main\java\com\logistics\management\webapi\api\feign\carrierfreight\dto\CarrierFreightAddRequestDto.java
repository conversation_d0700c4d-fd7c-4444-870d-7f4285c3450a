package com.logistics.management.webapi.api.feign.carrierfreight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 添加车主运价
 *
 * <AUTHOR>
 * @date 2022/9/1 14:40
 */
@Data
public class CarrierFreightAddRequestDto {

    @ApiModelProperty(value = "车主id", required = true)
    @NotNull(message = "车主不能为空")
    private Long companyCarrierId;
}
