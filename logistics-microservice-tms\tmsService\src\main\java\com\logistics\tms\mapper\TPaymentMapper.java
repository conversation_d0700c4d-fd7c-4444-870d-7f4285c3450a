package com.logistics.tms.mapper;

import com.logistics.tms.biz.carrierorder.model.GetSettlementByDemandOrderIdModel;
import com.logistics.tms.entity.TPayment;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TPaymentMapper extends BaseMapper<TPayment> {

    int batchInsert(@Param("list") List<TPayment> list);

    int batchUpdate(@Param("list") List<TPayment> list);

    List<GetSettlementByDemandOrderIdModel> getSettlementByDemandOrderId(@Param("demandOrderId")Long demandOrderId);

    TPayment getByCarrierOrderId(@Param("carrierOrderId")Long carrierOrderId);

    List<TPayment> getByCarrierOrderIds(@Param("carrierOrderIds")String carrierOrderIds);

    void batchUpdateByCarrierOrderIds(@Param("list") List<TPayment> tPaymentUpList);
}