package com.logistics.management.webapi.api.feign.demandorderobjection;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.demandorderobjection.dto.SearchDemandOrderObjectionRequestDto;
import com.logistics.management.webapi.api.feign.demandorderobjection.dto.SearchDemandOrderObjectionResponseDto;
import com.logistics.management.webapi.api.feign.demandorderobjection.hystrix.DemandOrderObjectionApiHystrix;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: wjf
 * @date: 2021/10/18 11:18
 */
@Api(value = "API - DemandOrderObjectionApi-云盘需求单异常管理", tags = "云盘需求单异常管理")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = DemandOrderObjectionApiHystrix.class)
public interface DemandOrderObjectionApi {

    @ApiOperation(value = "云盘需求单异常列表", tags = "1.2.7")
    @PostMapping(value = "/api/demandOrderObjection/searchDemandOrderObjection")
    Result<PageInfo<SearchDemandOrderObjectionResponseDto>> searchDemandOrderObjection(@RequestBody SearchDemandOrderObjectionRequestDto requestDto);

    @ApiOperation(value = "导出云盘需求单异常列表 v1.1.1")
    @GetMapping(value = "/api/demandOrderObjection/exportDemandOrderObjection")
    void exportDemandOrderObjection(SearchDemandOrderObjectionRequestDto requestDto, HttpServletResponse response);

}
