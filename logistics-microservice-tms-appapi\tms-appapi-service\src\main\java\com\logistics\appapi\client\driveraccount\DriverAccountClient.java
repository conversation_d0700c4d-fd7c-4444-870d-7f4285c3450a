package com.logistics.appapi.client.driveraccount;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.driveraccount.hystrix.DriverAccountClientHystrix;
import com.logistics.appapi.client.driveraccount.request.AddBankCardAppletRequestModel;
import com.logistics.appapi.client.driveraccount.request.DriverAccountDetailRequestModel;
import com.logistics.appapi.client.driveraccount.response.BankCardInfoResponseModel;
import com.logistics.appapi.client.driveraccount.response.DriverAccountDetailResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2024/3/11 9:35
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        fallback = DriverAccountClientHystrix.class)
public interface DriverAccountClient {

    @ApiOperation("小程序添加/修改银行卡")
    @PostMapping({"/service/driverApplet/bankCard/addBankCard"})
    Result<Boolean> addBankCardForApplet(@RequestBody AddBankCardAppletRequestModel requestModel);

    @ApiOperation("当前绑定银行卡信息查询")
    @PostMapping({"/service/driverApplet/bankCard/currBankCardInfo"})
    Result<BankCardInfoResponseModel> currBankCardInfo();

    @ApiOperation("司机账户详情")
    @PostMapping({"/service/driverAccount/getDetail"})
    Result<DriverAccountDetailResponseModel> getDetail(@RequestBody DriverAccountDetailRequestModel requestModel);

}
