package com.logistics.management.webapi.controller.oilfilled;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.*;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.enums.OilRefundReasonTypeEnum;
import com.logistics.management.webapi.client.oilfilled.OilFilledServiceClient;
import com.logistics.management.webapi.client.oilfilled.request.*;
import com.logistics.management.webapi.client.oilfilled.response.*;
import com.logistics.management.webapi.controller.oilfilled.mapping.*;
import com.logistics.management.webapi.controller.oilfilled.request.*;
import com.logistics.management.webapi.controller.oilfilled.response.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

@Api(value = "充油",tags = "充油")
@RestController
@RequestMapping(value = "/api/oilFilled")
@Slf4j
public class OilFilledController {

    @Resource
    private OilFilledServiceClient oilFilledServiceClient;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    OilFilledCheckMapping oilFilledCheckMapping;

    /**
     * 充油列表
     *
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/searchList")
    @ApiOperation(value = "充油列表v1.1.7")
    public Result<PageInfo<OilFilledListResponseDto>> searchList(@RequestBody OilFilledListRequestDto requestDto) {
        Result<PageInfo<OilFilledListResponseModel>> pageInfoResult = oilFilledServiceClient.searchList(MapperUtils.mapper(requestDto, OilFilledListRequestModel.class));
        pageInfoResult.throwException();
        PageInfo pageInfo = pageInfoResult.getData();
        if (pageInfo != null) {
            List<OilFilledListResponseDto> responseDtoList = MapperUtils.mapper(pageInfo.getList(), OilFilledListResponseDto.class, new OilFilledListMapping());
            pageInfo.setList(responseDtoList);
        }
        return Result.success(pageInfo);
    }

    /**
     * 充油列表导出
     * @param requestDto
     * @param response
     */
    @GetMapping(value = "/exportOilFilledList")
    @ApiOperation(value = "充油列表导出")
    public void exportOilFilledList(OilFilledListRequestDto requestDto, HttpServletResponse response) {
        OilFilledListRequestModel requestModel = MapperUtils.mapper(requestDto, OilFilledListRequestModel.class);
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<OilFilledListResponseModel>> pageInfoResult = oilFilledServiceClient.searchList(requestModel);
        pageInfoResult.throwException();
        PageInfo<OilFilledListResponseModel> pageInfoResultData = pageInfoResult.getData();
        if (pageInfoResultData != null) {
            List<OilFilledListResponseDto> dtoList = MapperUtils.mapper(pageInfoResultData.getList(), OilFilledListResponseDto.class, new OilFilledListMapping());
            String fileName = "充油费用" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
            Map<String, String> exportMap = ExportOilFilledListInfo.getExportOilFilledListMap();
            ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
                @Override
                public List load() {
                    return dtoList;
                }
            });
        }
    }

    /**
     * 新增/修改充油
     *
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/addOrModify")
    @ApiOperation(value = "新增/修改充油")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addOrModify(@RequestBody @Valid AddOrModifyOilFilledRequestDto requestDto) {
        return oilFilledServiceClient.addOrModify(MapperUtils.mapperNoDefault(requestDto, AddOrModifyOilFilledRequestModel.class));
    }

    /**
     * 充油详情
     *
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/getDetail")
    @ApiOperation(value = "充油详情")
    public Result<OilFilledDetailResponseDto> getDetail(@RequestBody @Valid OilFilledDetailRequestDto requestDto) {
        Result<OilFilledDetailResponseModel> detailResult = oilFilledServiceClient.getDetail(MapperUtils.mapper(requestDto, OilFilledDetailRequestModel.class));
        detailResult.throwException();
        List<String> sourceSrcList = new ArrayList<>();
        for (OilFilledFileModel ticketsModel : detailResult.getData().getOilFilledFileList()) {
            sourceSrcList.add(ticketsModel.getRelativeFilepath());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(detailResult.getData(), OilFilledDetailResponseDto.class, new OilFilledDetailMapping(configKeyConstant, imageMap)));
    }

    /**
     * 充油列表汇总
     *
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/getSummary")
    @ApiOperation(value = "充油列表汇总v1.1.7")
    public Result<OilFilledGetSummaryResponseDto> getSummary(@RequestBody OilFilledListRequestDto requestDto) {
        Result<OilFilledGetSummaryResponseModel> summaryResult = oilFilledServiceClient.getSummary(MapperUtils.mapper(requestDto, OilFilledListRequestModel.class));
        summaryResult.throwException();
        return Result.success(MapperUtils.mapper(summaryResult.getData(), OilFilledGetSummaryResponseDto.class));
    }

    /**
     * 充油操作记录
     *
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/getOperationRecord")
    @ApiOperation(value = "充油操作记录")
    public Result<List<OilFilledOperationRecordResponseDto>> getOperationRecord(@RequestBody @Valid OilFilledOperationRecordRequestDto requestDto) {
        Result<List<OilFilledOperationRecordResponseModel>> operationRecordResult = oilFilledServiceClient.getOperationRecord(MapperUtils.mapper(requestDto, OilFilledOperationRecordRequestModel.class));
        operationRecordResult.throwException();
        return Result.success(MapperUtils.mapper(operationRecordResult.getData(), OilFilledOperationRecordResponseDto.class, new OilFilledRecordMapping()));
    }

    /**
     * 导出充油及操作记录
     *
     * @param requestDto
     * @param response
     */
    @ApiModelProperty(value = "导出充油及操作记录")
    @GetMapping(value = "/exportOperationRecord")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportOperationRecord(OilFilledOperationRecordRequestDto requestDto, HttpServletResponse response) {
        Result<List<OilFilledOperationRecordResponseModel>> operationRecordResult = oilFilledServiceClient.getOperationRecord(MapperUtils.mapper(requestDto, OilFilledOperationRecordRequestModel.class));
        operationRecordResult.throwException();
        List<OilFilledOperationRecordResponseDto> dtoList = MapperUtils.mapper(operationRecordResult.getData(), OilFilledOperationRecordResponseDto.class, new OilFilledRecordMapping());
        if (ListUtils.isNotEmpty(dtoList)) {
            OilFilledOperationRecordResponseDto oilFilledOperationRecordResponseDto = dtoList.get(CommonConstant.INTEGER_ZERO);
            String fileName = oilFilledOperationRecordResponseDto.getVehicleNo() + "充油操作记录" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
            Map<String, String> exportMap = ExportOilFilledRecordInfo.getExportOilFilledRecordMap();
            ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
                @Override
                public List load() {
                    return dtoList;
                }
            });
        }
    }

    /**
     * 新增/修改油费退款
     *
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/addOrModifyRefund")
    @ApiOperation(value = "新增/修改油费退款")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addOrModifyRefund(@RequestBody @Valid AddOrModifyOilRefundRequestDto requestDto) {
        if (OilRefundReasonTypeEnum.SYB_CARD_LOST.getKey().toString().equals(requestDto.getRefundReasonType())) {
            if (StringUtils.isBlank(requestDto.getRefundReason())) {
                throw new BizException(ManagementWebApiExceptionEnum.SUB_CARD_NUMBER_EMPTY);
            }
        } else if (OilRefundReasonTypeEnum.OIL_FILLED.getKey().toString().equals(requestDto.getRefundReasonType())) {
            if (StringUtils.isBlank(requestDto.getRefundReason()) || requestDto.getRefundReason().length() > CommonConstant.INT_TEN) {
                throw new BizException(ManagementWebApiExceptionEnum.REFUND_REASON_EMPTY);
            }
        }
        return oilFilledServiceClient.addOrModifyRefund(MapperUtils.mapperNoDefault(requestDto, AddOrModifyOilRefundRequestModel.class));
    }

    /**
     * 油费退款详情
     *
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/getOilRefundDetail")
    @ApiOperation(value = "油费退款详情")
    public Result<OilRefundDetailResponseDto> getOilRefundDetail(@RequestBody @Valid OilFilledDetailRequestDto requestDto) {
        Result<OilRefundDetailResponseModel> result = oilFilledServiceClient.getOilRefundDetail(MapperUtils.mapper(requestDto, OilFilledDetailRequestModel.class));
        result.throwException();
        Map<String, String> picOSSPathMap = new HashMap<>();
        picOSSPathMap.put(result.getData().getRefundFile(), commonBiz.getImageURL(result.getData().getRefundFile()));
        return Result.success(MapperUtils.mapper(result.getData(), OilRefundDetailResponseDto.class, new OilRefundDetailMapping(configKeyConstant.fileAccessAddress, picOSSPathMap)));
    }

    /**
     * 油费退款操作记录
     *
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/getOilRefundRecord")
    @ApiOperation(value = "油费退款操作记录")
    public Result<List<OilRefundRecordResponseDto>> getOilRefundRecord(@RequestBody @Valid OilFilledOperationRecordRequestDto requestDto) {
        Result<List<OilFilledOperationRecordResponseModel>> result = oilFilledServiceClient.getOperationRecord(MapperUtils.mapper(requestDto, OilFilledOperationRecordRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), OilRefundRecordResponseDto.class, new OilRefundRecordMapping()));
    }

    /**
     * 导出油费退款操作记录
     *
     * @param requestDto
     * @param response
     */
    @ApiModelProperty(value = "导出油费退款操作记录")
    @GetMapping(value = "/exportOilRefundRecord")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportOilRefundRecord(OilFilledOperationRecordRequestDto requestDto, HttpServletResponse response) {
        Result<List<OilFilledOperationRecordResponseModel>> result = oilFilledServiceClient.getOperationRecord(MapperUtils.mapper(requestDto, OilFilledOperationRecordRequestModel.class));
        result.throwException();
        List<OilRefundRecordResponseDto> list = MapperUtils.mapper(result.getData(), OilRefundRecordResponseDto.class, new OilRefundRecordMapping());
        String vehicleNo = "";
        if (ListUtils.isNotEmpty(list)) {
            vehicleNo = list.get(CommonConstant.INTEGER_ZERO).getVehicleNo();
        }
        String fileName = vehicleNo + "退款操作记录" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String, String> exportMap = ExportOilRefundRecord.getExportOilRefundRecordMap();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return list;
            }
        });
    }

    @ApiOperation(value = "导入加油车", tags = "1.3.6")
    @PostMapping(value = "/importRefuelCar")
    public Result<ImportOilFilledResponseDto> importRefuelCar(@RequestParam(value = "file") MultipartFile file, HttpServletRequest request) {
        if (null == file) {
            throw new BizException(ManagementWebApiExceptionEnum.REFUEL_CARD_TIRE_FILE_IS_EMPTY);
        }
        InputStream in;
        try {
            in = file.getInputStream();
        } catch (IOException e) {
            log.error("导入加油车失败，", e);
            throw new BizException(ManagementWebApiExceptionEnum.REFUEL_CARD_TIRE_FILE_IS_EMPTY);
        }

        List<List<Object>> excelList = new ImportExcelUtil().getDataListByExcel(in, file.getOriginalFilename(), ImportExcelHeaders.getImportImportRefuelCarType());
        ImportOilFilledCarInfoRequestDto requestDto = oilFilledCheckMapping.initImportRepeatDataOfCar(excelList);
        ImportOilFilledCarInfoRequestModel importRequestModel = MapperUtils.mapper(requestDto,ImportOilFilledCarInfoRequestModel.class);
        if(ListUtils.isNotEmpty(importRequestModel.getImportList())){
            importRequestModel.getImportList().forEach(t->
                    commonBiz.convertObjectFieldToNullIfIsEmpty(t)
            );
        }
        Result<ImportOilFilledResponseModel> result = oilFilledServiceClient.importRefuelCar(importRequestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), ImportOilFilledResponseDto.class));
    }

    @ApiOperation(value = "导入充油卡", tags = "1.3.6")
    @PostMapping(value = "/importRefuelCard")
    public Result<ImportOilFilledResponseDto> importRefuelCard(@RequestParam(value = "file") MultipartFile file, HttpServletRequest request) {
        if (null == file) {
            throw new BizException(ManagementWebApiExceptionEnum.REFUEL_CARD_TIRE_FILE_IS_EMPTY);
        }
        InputStream in;
        try {
            in = file.getInputStream();
        } catch (IOException e) {
            log.error("导入充油卡失败，", e);
            throw new BizException(ManagementWebApiExceptionEnum.REFUEL_CARD_TIRE_FILE_IS_EMPTY);
        }
        List<List<Object>> excelList = new ImportExcelUtil().getDataListByExcel(in, file.getOriginalFilename(), ImportExcelHeaders.getImportImportRefuelCardType());
        ImportOilFilledCardInfoRequestDto requestDto = oilFilledCheckMapping.initImportRepeatDataOfCard(excelList);
        ImportOilFilledCardInfoRequestModel importRequestModel = MapperUtils.mapper(requestDto,ImportOilFilledCardInfoRequestModel.class);
        if(ListUtils.isNotEmpty(importRequestModel.getImportList())){
            importRequestModel.getImportList().forEach(t->
                    commonBiz.convertObjectFieldToNullIfIsEmpty(t)
            );
        }
        Result<ImportOilFilledResponseModel> result = oilFilledServiceClient.importRefuelCard(importRequestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), ImportOilFilledResponseDto.class));
    }
}
