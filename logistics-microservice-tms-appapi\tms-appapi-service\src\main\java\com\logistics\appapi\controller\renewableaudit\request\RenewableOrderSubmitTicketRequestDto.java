package com.logistics.appapi.controller.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class RenewableOrderSubmitTicketRequestDto {

	@ApiModelProperty(value = "乐橘新生订单审核表id",required = true)
	@NotBlank(message = "新生订单id不能为空")
	private String renewableAuditId;

	@ApiModelProperty(value = "现场图片",required = true)
	@NotEmpty(message = "现场图片不能为空")
	@Size(min = 1, max = 6, message = "现场图片最多上传6张")
	private List<String> scenePictureList;

	@ApiModelProperty(value = "确认单据",required = true)
	@NotEmpty(message = "确认单据不能为空")
	@Size(min = 1, max = 6, message = "确认单据最多上传6张")
	private List<String> confirmPictureList;
}
