package com.logistics.management.webapi.controller.messagenotice;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.messagenotice.MessageNoticeClient;
import com.logistics.management.webapi.client.messagenotice.request.ReadMessageNoticeRequestModel;
import com.logistics.management.webapi.client.messagenotice.request.SearchMessageNoticeListRequestModel;
import com.logistics.management.webapi.client.messagenotice.response.SearchMessageNoticeListResponseModel;
import com.logistics.management.webapi.controller.messagenotice.mapping.SearchMessageNoticeListMapping;
import com.logistics.management.webapi.controller.messagenotice.request.ReadMessageNoticeRequestDto;
import com.logistics.management.webapi.controller.messagenotice.request.SearchMessageNoticeListRequestDto;
import com.logistics.management.webapi.controller.messagenotice.response.SearchMessageNoticeListResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 消息通知
 * @author: wjf
 * @date: 2024/6/4 13:18
 */
@Api(value = "消息通知",tags = "消息通知")
@RestController
@RequestMapping(value = "/api/messageNotice")
public class MessageNoticeController {

    @Resource
    private MessageNoticeClient messageNoticeClient;

    /**
     * 消息列表 3.22.0
     *
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/searchList")
    @ApiOperation(value = "消息列表")
    public Result<PageInfo<SearchMessageNoticeListResponseDto>> searchList(@RequestBody SearchMessageNoticeListRequestDto requestDto) {
        SearchMessageNoticeListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchMessageNoticeListRequestModel.class);
        requestModel.setSource(CommonConstant.ONE);
        Result<PageInfo<SearchMessageNoticeListResponseModel>> result = messageNoticeClient.searchList(requestModel);
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SearchMessageNoticeListResponseDto> list = MapperUtils.mapper(pageInfo.getList(), SearchMessageNoticeListResponseDto.class, new SearchMessageNoticeListMapping());
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    /**
     * 置为已读 3.22.0
     *
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/readMessageNotice")
    @ApiOperation(value = "置为已读")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> readMessageNotice(@RequestBody ReadMessageNoticeRequestDto requestDto) {
        ReadMessageNoticeRequestModel requestModel = MapperUtils.mapper(requestDto, ReadMessageNoticeRequestModel.class);
        requestModel.setSource(CommonConstant.ONE);
        return messageNoticeClient.readMessageNotice(requestModel);
    }
}
