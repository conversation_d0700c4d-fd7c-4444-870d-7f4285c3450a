package com.logistics.tms.biz.personalaccidentinsurance

import com.logistics.tms.api.feign.personalaccidentinsurance.model.AddOrModifyPersonalAccidentInsuranceRequestModel
import com.logistics.tms.api.feign.personalaccidentinsurance.model.GetInsuranceByPolicyNumberRequestModel
import com.logistics.tms.api.feign.personalaccidentinsurance.model.GetInsuranceByPolicyNumberResponseModel
import com.logistics.tms.api.feign.personalaccidentinsurance.model.GetPolicyNoPremiumByPolicyNoResponseModel
import com.logistics.tms.api.feign.personalaccidentinsurance.model.ImportPersonalAccidentInsuranceRequestModel
import com.logistics.tms.api.feign.personalaccidentinsurance.model.ImportPersonalAccidentInsuranceResponseModel
import com.logistics.tms.api.feign.personalaccidentinsurance.model.PersonalAccidentInsuranceDetailResponseModel
import com.logistics.tms.api.feign.personalaccidentinsurance.model.PersonalAccidentInsuranceIdRequestModel
import com.logistics.tms.api.feign.personalaccidentinsurance.model.PersonalAccidentInsuranceListRequestModel
import com.logistics.tms.api.feign.personalaccidentinsurance.model.PersonalAccidentInsuranceListResponseModel
import com.logistics.tms.api.feign.personalaccidentinsurance.model.SearchInsuranceByPolicyNumberRequestModel
import com.logistics.tms.api.feign.personalaccidentinsurance.model.SearchInsuranceByPolicyNumberResponseModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TCertificationPictures
import com.logistics.tms.entity.TInsuranceCompany
import com.logistics.tms.entity.TPersonalAccidentInsurance
import com.logistics.tms.mapper.TCertificationPicturesMapper
import com.logistics.tms.mapper.TInsuranceCompanyMapper
import com.logistics.tms.mapper.TPersonalAccidentInsuranceMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class PersonalAccidentInsuranceBizTest extends Specification {
    @Mock
    TPersonalAccidentInsuranceMapper personalAccidentInsuranceMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TCertificationPicturesMapper certificationPicturesMapper
    @Mock
    TInsuranceCompanyMapper insuranceCompanyMapper
    @InjectMocks
    PersonalAccidentInsuranceBiz personalAccidentInsuranceBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Personal Accident Insurance List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(personalAccidentInsuranceMapper.searchPersonalAccidentInsuranceIds(any())).thenReturn([1l])
        when(personalAccidentInsuranceMapper.searchPersonalAccidentInsuranceList(anyString())).thenReturn([new PersonalAccidentInsuranceListResponseModel()])

        expect:
        personalAccidentInsuranceBiz.searchPersonalAccidentInsuranceList(requestModel) == expectedResult

        where:
        requestModel                                    || expectedResult
        new PersonalAccidentInsuranceListRequestModel() || null
    }

    @Unroll
    def "get Personal Accident Insurance Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(personalAccidentInsuranceMapper.getPersonalAccidentInsuranceDetail(anyLong())).thenReturn(new PersonalAccidentInsuranceDetailResponseModel())

        expect:
        personalAccidentInsuranceBiz.getPersonalAccidentInsuranceDetail(requestModel) == expectedResult

        where:
        requestModel                                  || expectedResult
        new PersonalAccidentInsuranceIdRequestModel() || new PersonalAccidentInsuranceDetailResponseModel()
    }

    @Unroll
    def "add Or Modify Personal Accident Insurance where requestModel=#requestModel"() {
        given:
        when(personalAccidentInsuranceMapper.getByTypePolicyNumber(anyInt(), anyString(), anyString())).thenReturn(new TPersonalAccidentInsurance(insuranceCompanyId: 1l, personalAccidentInsuranceId: 1l, type: 0, policyNumber: "policyNumber", batchNumber: "batchNumber", grossPremium: 0 as BigDecimal, policyPersonCount: 0, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 39).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 39).getTime(), addUserId: 1l, addUserName: "addUserName", source: 0))
        when(personalAccidentInsuranceMapper.getBatchInsuranceById(anyLong())).thenReturn([new TPersonalAccidentInsurance(insuranceCompanyId: 1l, personalAccidentInsuranceId: 1l, type: 0, policyNumber: "policyNumber", batchNumber: "batchNumber", grossPremium: 0 as BigDecimal, policyPersonCount: 0, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 39).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 39).getTime(), addUserId: 1l, addUserName: "addUserName", source: 0)])
        when(personalAccidentInsuranceMapper.batchUpdate(any())).thenReturn(0)
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(certificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", fileName: "fileName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 39).getTime(), suffix: "suffix")])
        when(certificationPicturesMapper.batchInsert(any())).thenReturn(0)
        when(certificationPicturesMapper.batchUpdate(any())).thenReturn(0)

        expect:
        personalAccidentInsuranceBiz.addOrModifyPersonalAccidentInsurance(requestModel)
        assert expectedResult == false

        where:
        requestModel                                           || expectedResult
        new AddOrModifyPersonalAccidentInsuranceRequestModel() || true
    }

    @Unroll
    def "get Insurance By Policy Number where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(personalAccidentInsuranceMapper.getInsuranceByPolicyNumber(anyInt(), anyString())).thenReturn([new GetInsuranceByPolicyNumberResponseModel()])

        expect:
        personalAccidentInsuranceBiz.getInsuranceByPolicyNumber(requestModel) == expectedResult

        where:
        requestModel                                 || expectedResult
        new GetInsuranceByPolicyNumberRequestModel() || [new GetInsuranceByPolicyNumberResponseModel()]
    }

    @Unroll
    def "search Insurance By Policy Number where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(personalAccidentInsuranceMapper.searchInsuranceByPolicyNumber(anyString())).thenReturn([new SearchInsuranceByPolicyNumberResponseModel()])

        expect:
        personalAccidentInsuranceBiz.searchInsuranceByPolicyNumber(requestModel) == expectedResult

        where:
        requestModel                                    || expectedResult
        new SearchInsuranceByPolicyNumberRequestModel() || [new SearchInsuranceByPolicyNumberResponseModel()]
    }

    @Unroll
    def "get Policy No Premium By Policy No where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(personalAccidentInsuranceMapper.getPolicyNoPremiumByPolicyNo(anyString())).thenReturn([new GetPolicyNoPremiumByPolicyNoResponseModel()])

        expect:
        personalAccidentInsuranceBiz.getPolicyNoPremiumByPolicyNo(requestModel) == expectedResult

        where:
        requestModel                                    || expectedResult
        new SearchInsuranceByPolicyNumberRequestModel() || [new GetPolicyNoPremiumByPolicyNoResponseModel()]
    }

    @Unroll
    def "import Personal Accident Insurance where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(personalAccidentInsuranceMapper.getByTypePolicyNumber(anyInt(), anyString(), anyString())).thenReturn(new TPersonalAccidentInsurance(insuranceCompanyId: 1l, personalAccidentInsuranceId: 1l, type: 0, policyNumber: "policyNumber", batchNumber: "batchNumber", grossPremium: 0 as BigDecimal, policyPersonCount: 0, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 39).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 39).getTime(), addUserId: 1l, addUserName: "addUserName", source: 0))
        when(personalAccidentInsuranceMapper.getBatchInsuranceById(anyLong())).thenReturn([new TPersonalAccidentInsurance(insuranceCompanyId: 1l, personalAccidentInsuranceId: 1l, type: 0, policyNumber: "policyNumber", batchNumber: "batchNumber", grossPremium: 0 as BigDecimal, policyPersonCount: 0, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 39).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 39).getTime(), addUserId: 1l, addUserName: "addUserName", source: 0)])
        when(personalAccidentInsuranceMapper.batchUpdate(any())).thenReturn(0)
        when(insuranceCompanyMapper.findInsuranceCompanyByName(anyString())).thenReturn(new TInsuranceCompany(companyName: "companyName"))

        expect:
        personalAccidentInsuranceBiz.importPersonalAccidentInsurance(requestModel) == expectedResult

        where:
        requestModel                                      || expectedResult
        new ImportPersonalAccidentInsuranceRequestModel() || new ImportPersonalAccidentInsuranceResponseModel()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme