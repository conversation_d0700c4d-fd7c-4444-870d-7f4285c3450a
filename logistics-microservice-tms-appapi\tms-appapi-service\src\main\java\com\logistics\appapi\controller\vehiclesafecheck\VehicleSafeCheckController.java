package com.logistics.appapi.controller.vehiclesafecheck;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.controller.common.CommonBiz;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.constant.ConfigKeyConstant;
import com.logistics.appapi.client.vehiclesafecheck.VehicleSafeCheckClient;
import com.logistics.appapi.client.vehiclesafecheck.request.AppletAddWaitCheckRequestModel;
import com.logistics.appapi.client.vehiclesafecheck.request.AppletAddWaitReformRequestModel;
import com.logistics.appapi.client.vehiclesafecheck.request.AppletSafeCheckListRequestModel;
import com.logistics.appapi.client.vehiclesafecheck.request.SafeCheckDetailRequestModel;
import com.logistics.appapi.client.vehiclesafecheck.response.*;
import com.logistics.appapi.controller.vehiclesafecheck.mapping.SafeCheckDetailMapping;
import com.logistics.appapi.controller.vehiclesafecheck.mapping.SearchSafeCheckListMapping;
import com.logistics.appapi.controller.vehiclesafecheck.request.AddWaitCheckRequestDto;
import com.logistics.appapi.controller.vehiclesafecheck.request.AddWaitReformRequestDto;
import com.logistics.appapi.controller.vehiclesafecheck.request.SafeCheckDetailRequestDto;
import com.logistics.appapi.controller.vehiclesafecheck.request.SafeCheckListRequestDto;
import com.logistics.appapi.controller.vehiclesafecheck.response.SafeCheckDetailResponseDto;
import com.logistics.appapi.controller.vehiclesafecheck.response.SafeCheckListResponseDto;
import com.logistics.appapi.controller.vehiclesafecheck.response.SafeCheckSummaryResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2024/3/15 9:14
 */
@Api(value = "车辆安全检查")
@RestController
public class VehicleSafeCheckController {

    @Resource
    private VehicleSafeCheckClient vehicleSafeCheckClient;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 列表
     * @param requestDto
     * @return
     */
    @ApiOperation("列表")
    @PostMapping(value = "/api/applet/safeCheck/searchList")
    public Result<PageInfo<SafeCheckListResponseDto>> searchList(@RequestBody SafeCheckListRequestDto requestDto) {
        Result<PageInfo<AppletSafeCheckListResponseModel>> result = vehicleSafeCheckClient.searchAppletList(MapperUtils.mapper(requestDto, AppletSafeCheckListRequestModel.class));
        result.throwException();

        PageInfo pageInfo = result.getData();
        List<SafeCheckListResponseDto> dtoList = MapperUtils.mapper(result.getData().getList(),SafeCheckListResponseDto.class,new SearchSafeCheckListMapping());
        pageInfo.setList(dtoList == null ? new ArrayList() : dtoList);
        return Result.success(pageInfo);
    }

    /**
     * 列表汇总
     * @param requestDto
     * @return
     */
    @ApiOperation("列表汇总")
    @PostMapping(value = "/api/applet/safeCheck/getSummary")
    public Result<SafeCheckSummaryResponseDto> getSummary(@RequestBody SafeCheckListRequestDto requestDto) {
        Result<AppletSafeCheckSummaryResponseModel> result = vehicleSafeCheckClient.getAppletSummary(MapperUtils.mapper(requestDto,AppletSafeCheckListRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SafeCheckSummaryResponseDto.class));
    }

    /**
     * 详情
     * @param requestDto
     * @return
     */
    @ApiOperation("详情")
    @PostMapping(value = "/api/applet/safeCheck/getDetail")
    public Result<SafeCheckDetailResponseDto> getDetail(@RequestBody @Valid SafeCheckDetailRequestDto requestDto) {
        Result<SafeCheckDetailResponseModel> result = vehicleSafeCheckClient.getDetail(MapperUtils.mapper(requestDto, SafeCheckDetailRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (SafeCheckFileResponseModel model : result.getData().getFileList()) {
            sourceSrcList.add(model.getRelativeFilepath());
        }
        SafeCheckReformResponseModel checkReformInfo = result.getData().getCheckReformInfo();
        if(checkReformInfo!=null){
            if(ListUtils.isNotEmpty(checkReformInfo.getItemFileList())){
                checkReformInfo.getItemFileList().forEach(item->sourceSrcList.add(item.getRelativeFilepath()));
            }
            if(ListUtils.isNotEmpty(checkReformInfo.getResultFileList())){
                checkReformInfo.getResultFileList().forEach(item->sourceSrcList.add(item.getRelativeFilepath()));
            }
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(),SafeCheckDetailResponseDto.class,new SafeCheckDetailMapping(configKeyConstant,imageMap)));
    }

    /**
     * 待检查 - 提交
     * @param requestDto
     * @return
     */
    @ApiOperation("待检查 - 提交")
    @PostMapping(value = "/api/applet/safeCheck/submitWaitCheck")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> submitWaitCheck(@RequestBody @Valid AddWaitCheckRequestDto requestDto) {
        Result<Boolean> result = vehicleSafeCheckClient.submitWaitCheck(MapperUtils.mapper(requestDto,AppletAddWaitCheckRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 待整改 - 提交
     * @param requestDto
     * @return
     */
    @ApiOperation("待整改 - 提交")
    @PostMapping(value = "/api/applet/safeCheck/submitWaitReform")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> submitWaitReform(@RequestBody @Valid AddWaitReformRequestDto requestDto) {
        Result<Boolean> result = vehicleSafeCheckClient.submitWaitReform(MapperUtils.mapper(requestDto,AppletAddWaitReformRequestModel.class));
        result.throwException();
        return Result.success(true);
    }
}
