package com.logistics.management.webapi.client.thirdparty.basicdata.baidu;

import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.thirdparty.basicdata.baidu.hystrix.BaiDuClientHystrix;
import com.logistics.management.webapi.client.thirdparty.basicdata.baidu.request.CheckSensitiveWordRequestModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.baidu.response.CheckSensitiveWordResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * @author: wjf
 * @date: 2019/12/17 14:46
 */
@FeignClient(name = FeignClientName.BASIC_DATA_SERVICES,fallback = BaiDuClientHystrix.class)
public interface BaiDuClient {

    @ApiOperation(value = "判断是否具有敏感词汇")
    @PostMapping(name = "判断是否具有敏感词汇", value = "/service/baiDu/checkSensitiveWord")
    Result<CheckSensitiveWordResponseModel> checkSensitiveWord(@RequestBody CheckSensitiveWordRequestModel requestModel);

}
