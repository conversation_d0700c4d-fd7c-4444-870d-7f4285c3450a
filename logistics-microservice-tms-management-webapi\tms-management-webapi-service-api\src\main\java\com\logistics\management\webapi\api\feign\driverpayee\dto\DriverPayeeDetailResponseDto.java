package com.logistics.management.webapi.api.feign.driverpayee.dto;

import com.logistics.management.webapi.api.feign.common.dto.CertificatePictureDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DriverPayeeDetailResponseDto {

    @ApiModelProperty("收款人账户ID")
    private String driverPayeeId = "";
    @ApiModelProperty("收款人姓名")
    private String name = "";
    @ApiModelProperty("收款人联系方式")
    private String mobile = "";
    @ApiModelProperty("收款人身份证号码")
    private String identityNo = "";
    @ApiModelProperty("银行Id")
    private String bankId = "";
    @ApiModelProperty("银行名称")
    private String bankName = "";
    @ApiModelProperty("银行卡号")
    private String bankCardNo = "";
    @ApiModelProperty("身份证正面")
    private String identityFront = "";
    @ApiModelProperty("身份证正面")
    private String identityFrontSrc = "";
    @ApiModelProperty("身份证背面")
    private String identityBack = "";
    @ApiModelProperty("身份证背面")
    private String identityBackSrc = "";
    @ApiModelProperty("收款证件列表,相对路径")
    private List<CertificatePictureDto> imageList;
    @ApiModelProperty("备注")
    private String remark = "";


}
