package com.logistics.tms.base.enums;

/**
 * @Author: sj
 * @Date: 2019/5/6 17:21
 */
public enum CompanyEntrustTypeEnum {
    DEFAULT(0,""),
    COMPANY(1, "企业"),
    PERSONAL(2, "个人"),
            ;
    private Integer key;
    private String value;

    CompanyEntrustTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static CompanyEntrustTypeEnum getEnum(Integer key) {
        for (CompanyEntrustTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
