package com.logistics.tms.biz.carriervehiclerel;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.controller.vehicleassetmanagement.response.FuzzyQueryVehicleInfoResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.OperateLogsOperateTypeEnum;
import com.logistics.tms.base.enums.VehicleOutageStatus;
import com.logistics.tms.base.enums.VehiclePropertyEnum;
import com.logistics.tms.base.utils.LocalStringUtil;
import com.logistics.tms.biz.basicinfo.web.BasicInfoWebBiz;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.carriervehiclerel.request.SaveOrModifyCarrierVehicleRequestModel;
import com.logistics.tms.controller.carriervehiclerel.request.SearchCarrierVehicleListRequestModel;
import com.logistics.tms.controller.carriervehiclerel.request.VehicleAssetDetailRequestModel;
import com.logistics.tms.controller.carriervehiclerel.response.GetCarrierVehicleDetailResponseModel;
import com.logistics.tms.controller.carriervehiclerel.response.SearchCarrierVehicleListResponseModel;
import com.logistics.tms.entity.TCarrierVehicleRelation;
import com.logistics.tms.entity.TVehicleBasic;
import com.logistics.tms.entity.TVehicleDrivingLicense;
import com.logistics.tms.entity.TVehicleRoadTransportCertificate;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.redis.redisdistributedlock.DistributedLock;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/10/14 14:20
 */
@Service
public class CarrierVehicleBiz {
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TCarrierVehicleRelationMapper tCarrierVehicleRelationMapper;
    @Autowired
    private TOperateLogsMapper tOperateLogsMapper;
    @Autowired
    private TVehicleBasicMapper tVehicleBasicMapper;
    @Autowired
    private TVehicleDrivingLicenseMapper tVehicleDrivingLicenseMapper;
    @Autowired
    private TVehicleRoadTransportCertificateMapper tVehicleRoadTransportCertificateMapper;

    @Autowired
    private BasicInfoWebBiz basicInfoWebBiz;

    /**
     * 列表(前台)
     * @param requestModel
     * @return
     */
    public PageInfo<SearchCarrierVehicleListResponseModel> searchList(SearchCarrierVehicleListRequestModel requestModel) {
        requestModel.setCompanyCarrierId(commonBiz.getLoginUserCompanyCarrierId());
        //查询车主下的车辆
        requestModel.enablePaging();
        List<SearchCarrierVehicleListResponseModel> relList = tCarrierVehicleRelationMapper.getList(requestModel);
        return new PageInfo<>(relList == null ? new ArrayList<>() : relList);
    }

    /**
     * 新增车辆信息(前台)
     *
     * @param requestModel 车辆信息
     */
    @Transactional
    @DistributedLock(prefix = CommonConstant.TMS_ADD_VEHICLE_LOCK,
            keys = "#requestModel.vehicleNo",
            waitTime = 3)
    public void saveOrModify(SaveOrModifyCarrierVehicleRequestModel requestModel) {
        //获取当前车主ID
        Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }
        // 实名认证校验
        basicInfoWebBiz.checkCarrierRealNameOrAuthorization();

        //根据车牌号查询车辆信息
        List<FuzzyQueryVehicleInfoResponseModel> vehicleInfoByVehicleNo = tVehicleBasicMapper.queryVehicleInfoByVehicleNos(LocalStringUtil.listTostring(Collections.singletonList(requestModel.getVehicleNo()), ','));
        if (ListUtils.isNotEmpty(vehicleInfoByVehicleNo)) {//车辆信息已存在
            //车牌号不能重复,所以只有一条车辆记录
            FuzzyQueryVehicleInfoResponseModel vehicleInfo = vehicleInfoByVehicleNo.get(CommonConstant.INTEGER_ZERO);

            //判断车辆的机构是内部(自主,自营)还是外部
            if (VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(vehicleInfo.getVehicleProperty())) {//外部
                //判断车辆类型是否和已存在的一致 ,并且判断是否在运营中
                if ((requestModel.getVehicleType() != null && !vehicleInfo.getVehicleType().equals(requestModel.getVehicleType())) ||
                        !VehicleOutageStatus.IN_OPERATION.getKey().equals(vehicleInfo.getOperatingState())) {
                    throw new BizException(CarrierDataExceptionEnum.BASIC_DATA_ERROR);
                }
                //查询当前车主和车辆的关联关系
                TCarrierVehicleRelation carrierVehicleRelation = tCarrierVehicleRelationMapper.getByCompanyCarrierIdAndVehicleId(loginUserCompanyCarrierId, vehicleInfo.getVehicleId());
                if (carrierVehicleRelation != null) {//此车主已经存在这辆车了
                    throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_HAS_EXIST);
                }

                //装载量（可装载托盘数）不同，则更新
                if (requestModel.getLoadingCapacity() != null && !requestModel.getLoadingCapacity().equals(vehicleInfo.getLoadingCapacity())){
                    //更新车辆信息
                    TVehicleBasic tVehicleBasic = new TVehicleBasic();
                    tVehicleBasic.setId(vehicleInfo.getVehicleId());
                    tVehicleBasic.setLoadingCapacity(requestModel.getLoadingCapacity());
                    commonBiz.setBaseEntityModify(tVehicleBasic, BaseContextHandler.getUserName());
                    tVehicleBasicMapper.updateByPrimaryKeySelective(tVehicleBasic);
                }

                //车辆类型不同，则更新
                if (requestModel.getVehicleType() != null && !requestModel.getVehicleType().equals(vehicleInfo.getVehicleType())) {
                    TVehicleDrivingLicense upVehicleDrivingLicense = new TVehicleDrivingLicense();
                    upVehicleDrivingLicense.setId(vehicleInfo.getDrivingLicenseId());
                    upVehicleDrivingLicense.setVehicleType(requestModel.getVehicleType());
                    commonBiz.setBaseEntityModify(upVehicleDrivingLicense, BaseContextHandler.getUserName());
                    tVehicleDrivingLicenseMapper.updateByPrimaryKeySelective(upVehicleDrivingLicense);
                }

                //不存在关联关系则新增关联关系和操作记录
                addCarrierVehicleRelation(loginUserCompanyCarrierId, vehicleInfo.getVehicleId(), vehicleInfo.getVehicleNo());
            } else {//不能添加内部车辆
                throw new BizException(CarrierDataExceptionEnum.BASIC_DATA_ERROR);
            }
        } else {//车辆信息不存在
            //新增车辆信息
            TVehicleBasic tVehicleBasic = new TVehicleBasic();
            tVehicleBasic.setVehicleProperty(VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey());
            tVehicleBasic.setLoadingCapacity(requestModel.getLoadingCapacity());
            commonBiz.setBaseEntityAdd(tVehicleBasic, BaseContextHandler.getUserName());
            tVehicleBasicMapper.insertSelective(tVehicleBasic);

            //车辆行驶证信息
            TVehicleDrivingLicense tVehicleDrivingLicense = new TVehicleDrivingLicense();
            tVehicleDrivingLicense.setVehicleNo(requestModel.getVehicleNo());
            tVehicleDrivingLicense.setVehicleId(tVehicleBasic.getId());
            tVehicleDrivingLicense.setVehicleType(requestModel.getVehicleType());
            commonBiz.setBaseEntityAdd(tVehicleDrivingLicense, BaseContextHandler.getUserName());
            tVehicleDrivingLicenseMapper.insertSelective(tVehicleDrivingLicense);

            //新增道路运输证表信息
            TVehicleRoadTransportCertificate vehicleRoadTransportCertificate = new TVehicleRoadTransportCertificate();
            vehicleRoadTransportCertificate.setVehicleId(tVehicleBasic.getId());
            commonBiz.setBaseEntityAdd(vehicleRoadTransportCertificate, BaseContextHandler.getUserName());
            tVehicleRoadTransportCertificateMapper.insertSelective(vehicleRoadTransportCertificate);

            //添加车主车辆关联记录并记录日志
            addCarrierVehicleRelation(loginUserCompanyCarrierId, tVehicleBasic.getId(), requestModel.getVehicleNo());
        }
    }

    /**
     * 添加车辆车主关联记录并记录日志
     *
     * @param companyCarrierId 车主id
     * @param vehicleId        车辆id
     * @param vehicleNo        车辆车牌号
     */
    @Transactional
    public void addCarrierVehicleRelation(Long companyCarrierId, Long vehicleId, String vehicleNo) {
        //新增车主车辆关联关系
        TCarrierVehicleRelation tCarrierVehicleRelationAdd = new TCarrierVehicleRelation();
        tCarrierVehicleRelationAdd.setCompanyCarrierId(companyCarrierId);
        tCarrierVehicleRelationAdd.setVehicleId(vehicleId);
        commonBiz.setBaseEntityAdd(tCarrierVehicleRelationAdd, BaseContextHandler.getUserName());
        tCarrierVehicleRelationMapper.insertSelective(tCarrierVehicleRelationAdd);

        //添加操作日志
        tOperateLogsMapper.insertSelective(commonBiz.addOperateLogs(companyCarrierId, OperateLogsOperateTypeEnum.CARRIER_VEHICLE_ADD, vehicleNo, BaseContextHandler.getUserName()));
    }

    /**
     * 车主车辆详情(前台)
     *
     * @param requestModel 车辆id
     * @return 车主车辆详情
     */
    public GetCarrierVehicleDetailResponseModel getCarrierVehicleDetail(VehicleAssetDetailRequestModel requestModel) {
        //获取当前车主ID
        Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }
        return tCarrierVehicleRelationMapper.getCarrierVehicleDetail(loginUserCompanyCarrierId, requestModel.getCarrierVehicleId());
    }
}
