package com.logistics.tms.controller.attendance.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SearchAttendanceListResponseModel {

    @ApiModelProperty("考勤打卡ID")
    private String attendanceRecordId;

    @ApiModelProperty("考勤用户姓名")
    private String staffName;

    @ApiModelProperty("考勤用户手机号")
    private String staffMobile;

    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    @ApiModelProperty("考勤日期")
    private Date attendanceDate;

    @ApiModelProperty("上班打卡时间")
    private Date onDutyPunchTime;

    @ApiModelProperty("上班打卡地点")
    private String onDutyPunchLocation;

    @ApiModelProperty("下班打卡时间")
    private Date offDutyPunchTime;

    @ApiModelProperty("下班打卡地点")
    private String offDutyPunchLocation;

    @ApiModelProperty("工时")
    private BigDecimal manHour;
}
