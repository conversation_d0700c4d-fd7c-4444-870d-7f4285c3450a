package com.logistics.management.webapi.controller.drivercostapply.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.DriverCostApplyTypeEnum;
import com.logistics.management.webapi.base.enums.DriverCostAuditEnum;
import com.logistics.management.webapi.base.enums.InvoiceTypeEnum;
import com.logistics.management.webapi.base.enums.StaffPropertyEnum;
import com.logistics.management.webapi.client.drivercostapply.response.DriverCostApplyDetailResponseModel;
import com.logistics.management.webapi.client.drivercostapply.response.DriverCostApplyInvoiceResponseModel;
import com.logistics.management.webapi.client.drivercostapply.response.DriverCostApplyTicketModel;
import com.logistics.management.webapi.client.drivercostapply.response.DriverCostDeductionResponseModel;
import com.logistics.management.webapi.controller.drivercostapply.response.DriverCostApplyDetailResponseDto;
import com.logistics.management.webapi.controller.drivercostapply.response.DriverCostApplyInvoiceResponseDto;
import com.logistics.management.webapi.controller.drivercostapply.response.ReserveItemDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/5
 */
public class DriverCostApplyDetailMapping extends MapperMapping<DriverCostApplyDetailResponseModel, DriverCostApplyDetailResponseDto> {

	private final Map<String, String> imageMap;
	private final ConfigKeyConstant configKeyConstant;

	public DriverCostApplyDetailMapping(ConfigKeyConstant configKeyConstant, Map<String, String> imageMap) {
		this.imageMap = imageMap;
		this.configKeyConstant = configKeyConstant;
	}

	@Override
	public void configure() {
		DriverCostApplyDetailResponseModel source = getSource();
		DriverCostApplyDetailResponseDto destination = getDestination();

		if (source != null) {
			//申请人
			destination.setStaffName("【" + StaffPropertyEnum.getEnum(source.getStaffProperty()).getValue() + "】" + source.getStaffName() + "_" + source.getStaffMobile());

			//审核状态
			destination.setAuditStatusLabel(DriverCostAuditEnum.getEnum(source.getAuditStatus()).getValue());
			//费用类型
			destination.setCostTypeLabel(DriverCostApplyTypeEnum.getEnum(source.getCostType()).getValue());
			//发生时间 天维度
			destination.setOccurrenceTime(source.getOccurrenceTime() != null ? DateFormatUtils.format(source.getOccurrenceTime(), CommonConstant.DATE_TO_STRING_YMD_PATTERN) : "");

			//处理凭证图片
			List<DriverCostApplyDetailResponseDto.DriverCostApplyTickDetail> srcUrlDtoList = new ArrayList<>();
			List<DriverCostApplyTicketModel> ticketList = source.getTicketList();
			if(ListUtils.isNotEmpty(ticketList)){
				ticketList.stream().collect(Collectors.groupingBy(DriverCostApplyTicketModel::getFileType)).forEach((key, value) -> {
					DriverCostApplyDetailResponseDto.DriverCostApplyTickDetail tickDetail = new DriverCostApplyDetailResponseDto.DriverCostApplyTickDetail();
					tickDetail.setType(key);
					tickDetail.setImagePathList(value.stream().map(e -> configKeyConstant.fileAccessAddress + imageMap.get(e.getFilePath())).collect(Collectors.toList()));
					srcUrlDtoList.add(tickDetail);
				});
			}
			destination.setTicketList(srcUrlDtoList);

			// 费用申请详情冲销费用列表
			List<DriverCostDeductionResponseModel> reserveList = source.getReserveList();
			List<ReserveItemDto> dtoList = MapperUtils.mapper(reserveList, ReserveItemDto.class, new DriverCostDeductionMapping());
			destination.setReserveList(dtoList);

			// 垫付
			String advanceCosts = Optional.ofNullable(source.getAdvanceCosts())
					.map(s -> s.stripTrailingZeros().toPlainString())
					.orElse("");
			destination.setAdvanceCosts(advanceCosts);

			//处理发票图片
			List<DriverCostApplyInvoiceResponseDto> invoiceDtoList = new ArrayList<>();
			List<DriverCostApplyInvoiceResponseModel> invoiceInfoList = source.getInvoiceInfoList();
			if (ListUtils.isNotEmpty(invoiceInfoList)) {
				for (DriverCostApplyInvoiceResponseModel invoiceModel : invoiceInfoList) {
					DriverCostApplyInvoiceResponseDto invoiceDto = MapperUtils.mapper(invoiceModel, DriverCostApplyInvoiceResponseDto.class);
					if (invoiceModel.getType() != null) {
						invoiceDto.setInvoiceType(invoiceModel.getType().toString());
						String typeLabel = InvoiceTypeEnum.getEnum(invoiceModel.getType()).getValue();
						String invoiceTypeLabel = StringUtils.isBlank(invoiceModel.getInvoiceType()) ? typeLabel : String.join("-", typeLabel, invoiceModel.getInvoiceType());
						invoiceDto.setInvoiceTypeLabel(invoiceTypeLabel);
					}
					invoiceDto.setInvoicePicPath(configKeyConstant.fileAccessAddress + imageMap.get(invoiceModel.getImagePath()));
					invoiceDtoList.add(invoiceDto);
				}
			}
			destination.setInvoiceInfoList(invoiceDtoList);
		}
	}
}
