package com.logistics.tms.controller.settlestatement.packaging.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/11/15 11:46
 */
@Data
public class StatementArchiveDetailResponseModel {
    @ApiModelProperty("对账单详情item id")
    private Long settleStatementItemId;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("归档原因")
    private String archiveRemark;

    @ApiModelProperty("归档图片")
    private List<String> archiveTicketList;
}
