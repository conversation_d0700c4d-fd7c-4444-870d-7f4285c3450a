package com.logistics.tms.controller.dispatchorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DispatchOrderCarrierChildResponseModel {
    @ApiModelProperty("运单ID")
    private Long carrierOrderId;
    @ApiModelProperty("调度单ID")
    private Long demandOrderId;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("运单状态 10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收")
    private Integer status;
    @ApiModelProperty("是否取消 0 否 1 是")
    private Integer ifCancel;
    @ApiModelProperty("是否放空 0 否 1 是")
    private Integer ifEmpty;
    @ApiModelProperty("卸货省")
    private String unloadProvinceName;
    @ApiModelProperty("卸货市")
    private String unloadCityName;
    @ApiModelProperty("卸货区")
    private String unloadAreaName;
    @ApiModelProperty("详细地址")
    private String unloadDetailAddress;
    @ApiModelProperty("仓库")
    private String unloadWarehouse;
    private List<CarrierOrderGoodsModel> carrierOrderGoodsList;

    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;
    @ApiModelProperty("司机运费价格类型 1 单价 2 一口价")
    private Integer dispatchFreightFeeType;
    @ApiModelProperty("预计司机运费")
    private BigDecimal dispatchFreightFee;
    @ApiModelProperty("调整价")
    private BigDecimal adjustFee;
    @ApiModelProperty("多装多卸价")
    private BigDecimal markupFee;
    @ApiModelProperty("预计数量")
    private BigDecimal carrierExpectAmount;
    @ApiModelProperty("提货数量")
    private BigDecimal carrierLoadAmount;
    @ApiModelProperty("卸货数量")
    private BigDecimal carrierUnloadAmount;
    @ApiModelProperty("签收数量")
    private BigDecimal carrierSignAmount;
    @ApiModelProperty("委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件")
    private Integer demandOrderSource;

}
