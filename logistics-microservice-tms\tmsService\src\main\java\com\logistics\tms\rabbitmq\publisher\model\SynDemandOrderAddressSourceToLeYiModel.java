package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2021/9/28 10:02
 */
@Data
public class SynDemandOrderAddressSourceToLeYiModel {
    @ApiModelProperty("卸货地址")
    private List<SynDemandOrderAddressToLeYiModel> demandOrderAddressList;

    @ApiModelProperty("操作人姓名")
    private String operateUserName;

    @ApiModelProperty("操作来源：1 网络货运系统，2 TMS系统")
    private String operateSource="2";
}
