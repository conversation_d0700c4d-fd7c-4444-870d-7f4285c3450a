package com.logistics.appapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SiteOtherPalletsRequestModel {

    @ApiModelProperty("空置数量")
    private Integer emptyTraysAmount;

    @ApiModelProperty("带料占用数量")
    private Integer employTraysAmount;

    @ApiModelProperty("共享托盘图片")
    private List<String> sharedTrayPics;
}
