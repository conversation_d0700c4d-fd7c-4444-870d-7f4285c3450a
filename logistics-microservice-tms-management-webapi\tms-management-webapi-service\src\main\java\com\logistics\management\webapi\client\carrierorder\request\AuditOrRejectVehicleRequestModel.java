package com.logistics.management.webapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AuditOrRejectVehicleRequestModel {

    @ApiModelProperty("运单id")
    private Long carrierOrderId;
    @ApiModelProperty("车辆历史纪录ID")
    private Long vehicleHistoryId;
    @ApiModelProperty("操作类型 1 审核 2 驳回")
    private String type;
    @ApiModelProperty("驳回原因")
    private String rejectReason;
    @ApiModelProperty("审核来源：1 后台，2 前台")
    private Integer auditSource;

}
