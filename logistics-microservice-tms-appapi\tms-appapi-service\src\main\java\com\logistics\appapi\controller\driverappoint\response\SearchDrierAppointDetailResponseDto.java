package com.logistics.appapi.controller.driverappoint.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/17
 */
@Data
public class SearchDrierAppointDetailResponseDto {

	@ApiModelProperty("乐橘新生客户名,个人展示手机号 姓名")
	private String customerName = "";

	@ApiModelProperty("需求单号")
	private String demandOrderCode = "";

	@ApiModelProperty(value = "发货人,展示姓名 手机号")
	private String consignor = "";

	@ApiModelProperty("发货详细地址,【仓库】省市区详情地址")
	private String loadDetailAddress = "";

	@ApiModelProperty("下单时间 yyyy-MM-dd HH:mm:ss")
	private String publishTime = "";

	@ApiModelProperty("货物数量(单位吨)")
	private String goodsAmountTotal = "";

	@ApiModelProperty("费用合计(元)")
	private String goodsPriceTotal = "";

	@ApiModelProperty(value = "车牌号")
	private String vehicleNo = "";

	@ApiModelProperty(value = "是否已关联车辆：0 否，1 是")
	private String ifAssociatedVehicle;

	@ApiModelProperty("审核进度")
	private List<AuditProcessDto> auditProcess = new ArrayList<>();
}
