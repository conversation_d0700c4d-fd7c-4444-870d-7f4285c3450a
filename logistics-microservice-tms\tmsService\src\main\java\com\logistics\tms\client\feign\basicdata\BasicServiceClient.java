package com.logistics.tms.client.feign.basicdata;

import com.logistics.tms.client.feign.basicdata.user.UserServiceApi;
import com.logistics.tms.client.feign.basicdata.user.request.GetUserByIdsRequestModel;
import com.logistics.tms.client.feign.basicdata.user.request.GetUserInfoModelRequestModel;
import com.logistics.tms.client.feign.basicdata.user.request.UserIdRequestModel;
import com.logistics.tms.client.feign.basicdata.user.response.GetUserByIdsResponseModel;
import com.logistics.tms.client.feign.basicdata.user.response.UserDetailResponseModel;
import com.logistics.tms.client.feign.basicdata.user.response.UserInfoModel;
import com.logistics.tms.client.feign.basicdata.wechat.BasicWechatServiceApi;
import com.logistics.tms.client.feign.basicdata.wechat.request.CreateGroupChatRequestModel;
import com.logistics.tms.client.feign.basicdata.wechat.request.PushMessageRequestModel;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/22 15:15
 */
@Service
public class BasicServiceClient {

    @Resource
    private UserServiceApi userServiceApi;
    @Resource
    private BasicWechatServiceApi basicWechatServiceApi;

    /**
     * 根据手机号查询用户信息
     *
     * @param mobilePhone 手机号
     * @return 用户信息
     */
    public UserInfoModel getUserInfoByMobilePhone(String mobilePhone) {
        GetUserInfoModelRequestModel requestModel = new GetUserInfoModelRequestModel();
        requestModel.setMobilePhone(mobilePhone);
        Result<UserInfoModel> result = userServiceApi.getUserInfoModel(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 员工信息详情
     *
     * @param userId
     * @return
     */
    public Result<UserDetailResponseModel> getUserInfoResult(Long userId) {
        UserIdRequestModel requestModel = new UserIdRequestModel();
        requestModel.setUserId(userId);
        return userServiceApi.getUserInfo(requestModel);
    }

    /**
     * 员工信息详情
     *
     * @param userId
     * @return
     */
    public UserDetailResponseModel getUserInfo(Long userId) {
        Result<UserDetailResponseModel> result = getUserInfoResult(userId);
        result.throwException();
        return result.getData();
    }

    /**
     * 根据ids查询账户信息
     *
     * @param idList
     * @return
     */
    public List<GetUserByIdsResponseModel> getUserByIds(@RequestBody List<Long> idList) {
        GetUserByIdsRequestModel requestModel = new GetUserByIdsRequestModel();
        requestModel.setUserIds(idList);
        Result<List<GetUserByIdsResponseModel>> result = userServiceApi.getUserByIds(requestModel);
        result.throwException();
        return result.getData();
    }

    public void createGroupChat(@RequestBody CreateGroupChatRequestModel requestModel) {
        Result<Boolean> result = basicWechatServiceApi.createGroupChat(requestModel);
        result.throwException();
    }

    /**
     * 发送消息
     */
    public void workGroupPushMessage(List<PushMessageRequestModel> pushMessageRequestModels) {
        if (ListUtils.isEmpty(pushMessageRequestModels)) {
            return;
        }
        Result<Boolean> result = basicWechatServiceApi.pushMessage(pushMessageRequestModels);
        result.throwException();
    }
}
