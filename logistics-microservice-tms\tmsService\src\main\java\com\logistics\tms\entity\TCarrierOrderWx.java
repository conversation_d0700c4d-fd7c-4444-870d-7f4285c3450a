package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TCarrierOrderWx extends BaseEntity {
    /**
     * 运单ID
     */
    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    /**
    * 角色 1 发货人 2 收货人 3 委托方 4 其他
    */
    @ApiModelProperty("角色 1 发货人 2 收货人 3 委托方 4 其他")
    private Integer role;

    /**
    * 姓名
    */
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String mobile;

    /**
    * 是否推送 1 是 0 否
    */
    @ApiModelProperty("是否推送 1 是 0 否")
    private Integer ifPush;
}