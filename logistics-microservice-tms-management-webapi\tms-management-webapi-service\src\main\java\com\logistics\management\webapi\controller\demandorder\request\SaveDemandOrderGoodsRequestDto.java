package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2019/5/16 8:56
 */
@Data
public class SaveDemandOrderGoodsRequestDto {

    @ApiModelProperty("货物品名")
    @NotBlank(message = "货物品名不能为空")
    private String goodsName;

    @ApiModelProperty("规格")
    private String goodsSize;

    @ApiModelProperty("货物单位：1 件，2 吨")
    @NotBlank(message = "请选择货物单位")
    private String goodsUnit;

    @ApiModelProperty("货物数量")
    @NotBlank(message = "请维护货物数量")
    private String goodsAmount;
}
