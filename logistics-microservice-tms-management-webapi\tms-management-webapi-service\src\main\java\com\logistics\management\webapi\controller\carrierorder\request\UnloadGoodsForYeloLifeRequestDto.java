package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 新生卸货商品
 *
 * <AUTHOR>
 * @date 2022/8/16 17:00
 */
@Data
public class UnloadGoodsForYeloLifeRequestDto {

    @ApiModelProperty(value = "货物id", required = true)
    @NotBlank(message = "货物id不能为空")
    private String goodsId;
    @ApiModelProperty(value = "货物数量", required = true)
    @NotBlank(message = "货物数量不能为空")
    private String unloadAmount;

    @ApiModelProperty(value = "货物的编码集合 v2.44", required = true)
    private List<LoadGoodsForYeloLifeRequestCodeDto> codeDtoList;

}
