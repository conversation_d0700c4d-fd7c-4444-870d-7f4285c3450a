<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TDriverCostApplyMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDriverCostApply">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cost_apply_code" jdbcType="VARCHAR" property="costApplyCode" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
    <result column="staff_mobile" jdbcType="VARCHAR" property="staffMobile" />
    <result column="staff_property" jdbcType="INTEGER" property="staffProperty" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="cost_type" jdbcType="INTEGER" property="costType" />
    <result column="apply_cost" jdbcType="DECIMAL" property="applyCost" />
    <result column="occurrence_time" jdbcType="TIMESTAMP" property="occurrenceTime" />
    <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime" />
    <result column="apply_remark" jdbcType="VARCHAR" property="applyRemark" />
    <result column="associated_oil_card" jdbcType="VARCHAR" property="associatedOilCard" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="auditor_name" jdbcType="VARCHAR" property="auditorName" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, cost_apply_code, staff_id, staff_name, staff_mobile, staff_property, vehicle_id, 
    vehicle_no, cost_type, apply_cost, occurrence_time, apply_time, apply_remark, associated_oil_card, 
    audit_status, auditor_name, audit_time, remark, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_driver_cost_apply
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_driver_cost_apply
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDriverCostApply">
    insert into t_driver_cost_apply (id, cost_apply_code, staff_id, 
      staff_name, staff_mobile, staff_property, 
      vehicle_id, vehicle_no, cost_type, 
      apply_cost, occurrence_time, apply_time, 
      apply_remark, associated_oil_card, audit_status, 
      auditor_name, audit_time, remark, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{costApplyCode,jdbcType=VARCHAR}, #{staffId,jdbcType=BIGINT}, 
      #{staffName,jdbcType=VARCHAR}, #{staffMobile,jdbcType=VARCHAR}, #{staffProperty,jdbcType=INTEGER}, 
      #{vehicleId,jdbcType=BIGINT}, #{vehicleNo,jdbcType=VARCHAR}, #{costType,jdbcType=INTEGER}, 
      #{applyCost,jdbcType=DECIMAL}, #{occurrenceTime,jdbcType=TIMESTAMP}, #{applyTime,jdbcType=TIMESTAMP}, 
      #{applyRemark,jdbcType=VARCHAR}, #{associatedOilCard,jdbcType=VARCHAR}, #{auditStatus,jdbcType=INTEGER}, 
      #{auditorName,jdbcType=VARCHAR}, #{auditTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDriverCostApply">
    insert into t_driver_cost_apply
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="costApplyCode != null">
        cost_apply_code,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="staffName != null">
        staff_name,
      </if>
      <if test="staffMobile != null">
        staff_mobile,
      </if>
      <if test="staffProperty != null">
        staff_property,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleNo != null">
        vehicle_no,
      </if>
      <if test="costType != null">
        cost_type,
      </if>
      <if test="applyCost != null">
        apply_cost,
      </if>
      <if test="occurrenceTime != null">
        occurrence_time,
      </if>
      <if test="applyTime != null">
        apply_time,
      </if>
      <if test="applyRemark != null">
        apply_remark,
      </if>
      <if test="associatedOilCard != null">
        associated_oil_card,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="auditorName != null">
        auditor_name,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="costApplyCode != null">
        #{costApplyCode,jdbcType=VARCHAR},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffName != null">
        #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null">
        #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="staffProperty != null">
        #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="costType != null">
        #{costType,jdbcType=INTEGER},
      </if>
      <if test="applyCost != null">
        #{applyCost,jdbcType=DECIMAL},
      </if>
      <if test="occurrenceTime != null">
        #{occurrenceTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyTime != null">
        #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyRemark != null">
        #{applyRemark,jdbcType=VARCHAR},
      </if>
      <if test="associatedOilCard != null">
        #{associatedOilCard,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditorName != null">
        #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDriverCostApply">
    update t_driver_cost_apply
    <set>
      <if test="costApplyCode != null">
        cost_apply_code = #{costApplyCode,jdbcType=VARCHAR},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffName != null">
        staff_name = #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null">
        staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="staffProperty != null">
        staff_property = #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="costType != null">
        cost_type = #{costType,jdbcType=INTEGER},
      </if>
      <if test="applyCost != null">
        apply_cost = #{applyCost,jdbcType=DECIMAL},
      </if>
      <if test="occurrenceTime != null">
        occurrence_time = #{occurrenceTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyTime != null">
        apply_time = #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyRemark != null">
        apply_remark = #{applyRemark,jdbcType=VARCHAR},
      </if>
      <if test="associatedOilCard != null">
        associated_oil_card = #{associatedOilCard,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditorName != null">
        auditor_name = #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDriverCostApply">
    update t_driver_cost_apply
    set cost_apply_code = #{costApplyCode,jdbcType=VARCHAR},
      staff_id = #{staffId,jdbcType=BIGINT},
      staff_name = #{staffName,jdbcType=VARCHAR},
      staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      staff_property = #{staffProperty,jdbcType=INTEGER},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      cost_type = #{costType,jdbcType=INTEGER},
      apply_cost = #{applyCost,jdbcType=DECIMAL},
      occurrence_time = #{occurrenceTime,jdbcType=TIMESTAMP},
      apply_time = #{applyTime,jdbcType=TIMESTAMP},
      apply_remark = #{applyRemark,jdbcType=VARCHAR},
      associated_oil_card = #{associatedOilCard,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      auditor_name = #{auditorName,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>