package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2024/7/22 9:35
 */
@Data
public class YeloLifeCarrierOrderGoodsStockOutModel {

    @ApiModelProperty("skuCode")
    private String skuCode;

    @ApiModelProperty(value = "实提数量（出库数量）")
    private BigDecimal loadAmount;

}
