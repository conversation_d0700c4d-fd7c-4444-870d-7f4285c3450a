package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2024/05/17
*/
@Data
public class TCarrierOrderAddress extends BaseEntity {
    /**
    * 运单ID
    */
    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    /**
    * 基础地址code
    */
    @ApiModelProperty("基础地址code")
    private String loadYeloAddressCode;

    /**
    * 提货地址code
    */
    @ApiModelProperty("提货地址code")
    private String loadAddressCode;

    /**
    * 省份ID
    */
    @ApiModelProperty("省份ID")
    private Long loadProvinceId;

    /**
    * 省份名字
    */
    @ApiModelProperty("省份名字")
    private String loadProvinceName;

    /**
    * 城市ID
    */
    @ApiModelProperty("城市ID")
    private Long loadCityId;

    /**
    * 城市名字
    */
    @ApiModelProperty("城市名字")
    private String loadCityName;

    /**
    * 县区id
    */
    @ApiModelProperty("县区id")
    private Long loadAreaId;

    /**
    * 县区名字
    */
    @ApiModelProperty("县区名字")
    private String loadAreaName;

    /**
    * 详细地址
    */
    @ApiModelProperty("详细地址")
    private String loadDetailAddress;

    /**
    * 发货仓库
    */
    @ApiModelProperty("发货仓库")
    private String loadWarehouse;

    /**
    * 装货公司
    */
    @ApiModelProperty("装货公司")
    private String loadCompany;

    /**
    * 提货经度
    */
    @ApiModelProperty("提货经度")
    private String loadLongitude;

    /**
    * 提货纬度
    */
    @ApiModelProperty("提货纬度")
    private String loadLatitude;

    /**
    * 发货人姓名
    */
    @ApiModelProperty("发货人姓名")
    private String consignorName;

    /**
    * 发货人手机号
    */
    @ApiModelProperty("发货人手机号")
    private String consignorMobile;

    /**
    * 期望提货时间
    */
    @ApiModelProperty("期望提货时间")
    private Date expectedLoadTime;

    /**
    * 提货大区id
    */
    @ApiModelProperty("提货大区id")
    private Long loadRegionId;

    /**
    * 提货大区名称
    */
    @ApiModelProperty("提货大区名称")
    private String loadRegionName;

    /**
    * 提货大区负责人姓名
    */
    @ApiModelProperty("提货大区负责人姓名")
    private String loadRegionContactName;

    /**
    * 提货大区负责人联系方式
    */
    @ApiModelProperty("提货大区负责人联系方式")
    private String loadRegionContactPhone;

    /**
    * 卸货地址code
    */
    @ApiModelProperty("卸货地址code")
    private String unloadAddressCode;

    /**
    * 省份ID
    */
    @ApiModelProperty("省份ID")
    private Long unloadProvinceId;

    /**
    * 省份名字
    */
    @ApiModelProperty("省份名字")
    private String unloadProvinceName;

    /**
    * 城市ID
    */
    @ApiModelProperty("城市ID")
    private Long unloadCityId;

    /**
    * 城市名字
    */
    @ApiModelProperty("城市名字")
    private String unloadCityName;

    /**
    * 县区id
    */
    @ApiModelProperty("县区id")
    private Long unloadAreaId;

    /**
    * 县区名字
    */
    @ApiModelProperty("县区名字")
    private String unloadAreaName;

    /**
    * 详细地址
    */
    @ApiModelProperty("详细地址")
    private String unloadDetailAddress;

    /**
    * 收货仓库
    */
    @ApiModelProperty("收货仓库")
    private String unloadWarehouse;

    /**
    * 卸货公司
    */
    @ApiModelProperty("卸货公司")
    private String unloadCompany;

    /**
    * 卸货经度
    */
    @ApiModelProperty("卸货经度")
    private String unloadLongitude;

    /**
    * 卸货纬度
    */
    @ApiModelProperty("卸货纬度")
    private String unloadLatitude;

    /**
    * 卸货地址是否后补：0 否，1 是
    */
    @ApiModelProperty("卸货地址是否后补：0 否，1 是")
    private Integer unloadAddressIsAmend;

    /**
    * 卸货地址修改原因类型：1 联系问题，2 地址问题，3 装车问题，4 等待问题
    */
    @ApiModelProperty("卸货地址修改原因类型：1 联系问题，2 地址问题，3 装车问题，4 等待问题")
    private Integer unloadAddressUpdateType;

    /**
    * 收货人姓名
    */
    @ApiModelProperty("收货人姓名")
    private String receiverName;

    /**
    * 收货人手机号
    */
    @ApiModelProperty("收货人手机号")
    private String receiverMobile;

    /**
    * 期望卸货时间
    */
    @ApiModelProperty("期望卸货时间")
    private Date expectedUnloadTime;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}