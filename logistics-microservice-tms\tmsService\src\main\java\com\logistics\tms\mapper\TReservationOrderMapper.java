package com.logistics.tms.mapper;

import com.logistics.tms.controller.carrierorderapplet.request.CarrierDriverRelationForAppletModel;
import com.logistics.tms.controller.reservationorder.request.ReservationOrderSearchListForManagementWebReqModel;
import com.logistics.tms.controller.reservationorder.request.ReservationOrderSearchListRequestModel;
import com.logistics.tms.controller.reservationorder.response.ReservationOrderSearchListForManagementWebResModel;
import com.logistics.tms.controller.reservationorder.response.ReservationOrderSearchListResponseModel;
import com.logistics.tms.controller.reservationorder.response.ReservationOrderSummaryListResponseModel;
import com.logistics.tms.entity.TReservationOrder;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/08/19
*/
@Mapper
public interface TReservationOrderMapper extends BaseMapper<TReservationOrder> {

    List<ReservationOrderSummaryListResponseModel> waitSignInSummaryList(@Param("driverModel") List<CarrierDriverRelationForAppletModel> driverModel);

    List<TReservationOrder> getEnabledOrder(@Param("reservationOrderIdList")List<Long> reservationOrderIdList);

    List<TReservationOrder> getWaitSignInAndReservationTimeInvalid();


    List<TReservationOrder> getOrderByIds(@Param("reservationOrderIdList")List<Long> reservationOrderIdList);

    List<ReservationOrderSearchListResponseModel> searchListForDriverWeb(@Param("param1") ReservationOrderSearchListRequestModel requestModel);


    List<ReservationOrderSearchListForManagementWebResModel> searchListForManagementWeb(@Param("param1") ReservationOrderSearchListForManagementWebReqModel requestModel);
}