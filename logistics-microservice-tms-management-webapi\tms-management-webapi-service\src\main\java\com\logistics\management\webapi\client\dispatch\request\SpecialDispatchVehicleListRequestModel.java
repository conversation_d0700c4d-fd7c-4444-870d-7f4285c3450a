package com.logistics.management.webapi.client.dispatch.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2024/8/7 9:16
 */
@Data
public class SpecialDispatchVehicleListRequestModel {

    /**
     * 需求单货物id
     */
    private Long demandOrderGoodsId;

    /**
     * 需求单id
     */
    private Long demandOrderId;

    /**
     * 预提数量
     */
    private BigDecimal loadAmount;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 到下个点位距离
     */
    private BigDecimal nextPointDistance;

}
