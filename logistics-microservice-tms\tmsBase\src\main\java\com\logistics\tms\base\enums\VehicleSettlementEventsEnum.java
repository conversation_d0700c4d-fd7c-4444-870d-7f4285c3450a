package com.logistics.tms.base.enums;

public enum VehicleSettlementEventsEnum {
    SETTLEMENT_STATEMENT(1,"对账"),
    CONFIRM(2,"确认"),
    SETTLED(3,"结清")
    ;
    private Integer key;
    private String value;
    VehicleSettlementEventsEnum(Integer key,String value){
        this.key=key;
        this.value=value;
    }

    public Integer getKey() {
        return key;
    }
    public String getValue() {
        return value;
    }

}
