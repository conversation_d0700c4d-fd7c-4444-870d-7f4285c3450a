package com.logistics.appapi.base.enums;

/**
 * 单据上传状态: 0:待上传 1:已上传
 *
 * <AUTHOR>
 */
public enum RenewableTicketUploadStatusEnum {

    WAIT_UPLOAD(0,"待上传"),
    HAS_UPLOAD(1,"已上传");

    private Integer key;
    private String value;

    RenewableTicketUploadStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static RenewableTicketUploadStatusEnum getEnum(Integer key) {
        for (RenewableTicketUploadStatusEnum t : values()) {
            if (t.getKey() .equals(key)) {
                return t;
            }
        }
        return WAIT_UPLOAD;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
