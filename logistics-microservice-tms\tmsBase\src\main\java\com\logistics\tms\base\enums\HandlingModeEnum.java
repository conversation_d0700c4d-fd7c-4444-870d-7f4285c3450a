package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/04/28
 */
@AllArgsConstructor
@Getter
public enum  HandlingModeEnum {
    DEFAULT(0, ""),
    LOADING_AND_UNLOADING(1, " 一装一卸"),
    ONE_MORE_LOAD(2, "多装一卸"),
    ;

    private final Integer key;
    private final String value;

    public static HandlingModeEnum getEnum(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
