package com.logistics.management.webapi.api.impl.entrustaddress;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import com.logistics.management.webapi.api.feign.entrustaddress.EntrustAddressApi;
import com.logistics.management.webapi.api.feign.entrustaddress.dto.GetAddressByCompanyNameOrWarehouseRequestDto;
import com.logistics.management.webapi.api.feign.entrustaddress.dto.GetAddressByCompanyNameOrWarehouseResponseDto;
import com.logistics.management.webapi.api.feign.entrustaddress.dto.SearchEntrustAddressRequestDto;
import com.logistics.management.webapi.api.feign.entrustaddress.dto.SearchEntrustAddressResponseDto;
import com.logistics.management.webapi.api.impl.entrustaddress.mapping.ListEntrustAddressMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportExcelEntrustAddress;
import com.logistics.management.webapi.base.constant.ExportExcelEntrustReceivingAddress;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.tms.api.feign.entrustaddress.EntrustAddressServiceApi;
import com.logistics.tms.api.feign.entrustaddress.model.GetAddressByCompanyNameRequestModel;
import com.logistics.tms.api.feign.entrustaddress.model.GetAddressByCompanyNameResponseModel;
import com.logistics.tms.api.feign.entrustaddress.model.SearchEntrustAddressRequestModel;
import com.logistics.tms.api.feign.entrustaddress.model.SearchEntrustAddressResponseModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ExcelUtils;
import com.yelo.tools.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/9/19 14:56
 */
@RestController
public class EntrustAddressApiImpl implements EntrustAddressApi {

    @Autowired
    private EntrustAddressServiceApi entrustAddressServiceApi;

    /**
     * 发布需求单时，根据公司名或者仓库模糊搜索带出地址
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<GetAddressByCompanyNameOrWarehouseResponseDto>> getAddressByCompanyNameOrWarehouse(@RequestBody GetAddressByCompanyNameOrWarehouseRequestDto requestDto) {
        if (StringUtils.isBlank(requestDto.getCompanyEntrustId())) {
            throw new BizException(ManagementWebApiExceptionEnum.COMPANY_ENTRUST_EMPTY);
        }
        Result<PageInfo<GetAddressByCompanyNameResponseModel>> result = entrustAddressServiceApi.getAddressByCompanyNameOrWarehouse(MapperUtils.mapper(requestDto, GetAddressByCompanyNameRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        pageInfo.setList(MapperUtils.mapper(result.getData().getList(), GetAddressByCompanyNameOrWarehouseResponseDto.class));
        return Result.success(pageInfo);
    }

    /**
     * 获取委托方地址列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchEntrustAddressResponseDto>> searchList(@RequestBody@Valid SearchEntrustAddressRequestDto requestDto) {
        SearchEntrustAddressRequestModel requestModel = MapperUtils.mapper(requestDto, SearchEntrustAddressRequestModel.class);
        requestModel.setSource(CommonConstant.ONE);
        Result<PageInfo<SearchEntrustAddressResponseModel>> pageInfoResult = entrustAddressServiceApi.searchList(requestModel);
        pageInfoResult.throwException();
        PageInfo pageInfo = pageInfoResult.getData();
        if(pageInfo!=null){
            List<SearchEntrustAddressResponseDto> dtoList = MapperUtils.mapper(pageInfo.getList(),SearchEntrustAddressResponseDto.class,new ListEntrustAddressMapping());
            pageInfo.setList(dtoList);
        }
        return Result.success(pageInfo);
    }

    /**
     * 导出
     * @param requestDto
     * @param response
     */
    @Override
    public void export(SearchEntrustAddressRequestDto requestDto, HttpServletResponse response) {
        SearchEntrustAddressRequestModel model = MapperUtils.mapper(requestDto, SearchEntrustAddressRequestModel.class);
        model.setSource(CommonConstant.ONE);
        Result<List<SearchEntrustAddressResponseModel>> listResult = entrustAddressServiceApi.export(model);
        listResult.throwException();
        List<SearchEntrustAddressResponseDto> dtoList = MapperUtils.mapper(listResult.getData(),SearchEntrustAddressResponseDto.class,new ListEntrustAddressMapping());
        String fileName = "委托方发货信息";
        Map<String, String> exportTypeMap = ExportExcelEntrustAddress.getExportEntrustAddress();
        if (requestDto.getAddressType().equals(CommonConstant.TWO)){
            fileName = "委托方收货信息";
            exportTypeMap = ExportExcelEntrustReceivingAddress.getExportEntrustReceivingAddress();
        }
        fileName = fileName + DateUtils.dateToString(new Date(), CommonConstant.DATE_TO_STRING_YMD_PATTERN);
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportTypeMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return dtoList;
            }
        });

    }
}
