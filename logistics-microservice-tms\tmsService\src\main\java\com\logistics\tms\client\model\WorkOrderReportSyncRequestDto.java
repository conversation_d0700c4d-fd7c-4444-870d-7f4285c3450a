package com.logistics.tms.client.model;

import com.logistics.tms.base.enums.WorkOrderAnomalyTypeOneEnum;
import com.logistics.tms.base.enums.WorkOrderAnomalyTypeTwoEnum;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.workordercenter.model.WorkOrderReportBoModel;
import com.logistics.tms.entity.TWorkOrder;
import com.yelo.tools.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Objects;

@Data
@Accessors(chain = true)
public class WorkOrderReportSyncRequestDto {


    @ApiModelProperty(value = "业务应用key")
    private String busAppKey = ReportConfigKey.TMS_KEY;

    @ApiModelProperty(value = "任务来源")
    private String taskResource = ReportConfigKey.TMS_KEY;

    @ApiModelProperty(value = "任务类型 1.回收无盘、2.客户失联、3.回收装车、4.客户签收、5.出库少货、6.C客户回访、7.Cn客户回访、8.物流异常")
    private String taskType = ReportConfigKey.TASK_TYPE;

    @ApiModelProperty(value = "任务简述")
    private String taskResume;

    @ApiModelProperty(value = "详细内容")
    private String taskExplain;

    @ApiModelProperty(value = "优先级 1、2、3、4、5 (数字越低，优先级越高)")
    private String level;

    @ApiModelProperty(value = "提报人")
    private String reportBy;

    @ApiModelProperty(value = "提报人联系方式")
    private String reportByPhone;

    @ApiModelProperty(value = "交互单号")
    private String code;

    @ApiModelProperty(value = "客户名称")
    private String customer;

    @ApiModelProperty(value = "客户联系方式")
    private String customerPhone;

    @ApiModelProperty(value = "客户联系人")
    private String customerContacts;

    @ApiModelProperty(value = "业务类型 1.履约、2.回收、3.Cn回访、4.仓作业")
    private String busType = ReportConfigKey.BUS_TYPE;

    @ApiModelProperty(value = "司机是否在现场 1在现场 2不在现场")
    private String isDriverOnSite;

    @ApiModelProperty(value = "操作人")
    private String userBy;

    @ApiModelProperty(value = "业务单号")
    private String busCode;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNumber;

    @ApiModelProperty(value = "司机名称")
    private String driverName;

    @ApiModelProperty(value = "委托人")
    private String client;

    @ApiModelProperty(value = "委托人id")
    private String clientId;

    public WorkOrderReportSyncRequestDto setTaskResume(Integer anomalyTypeOne, Integer anomalyTypeTwo) {
        this.taskResume = this.getTaskExplain(anomalyTypeOne, anomalyTypeTwo);
        return this;
    }

    public WorkOrderReportSyncRequestDto setTaskExplain(Integer anomalyTypeOne, Integer anomalyTypeTwo, String remark) {
        this.taskExplain = this.getTaskExplain(anomalyTypeOne, anomalyTypeTwo) + (StringUtils.isBlank(remark) ? "" : "," + remark);
        return this;
    }

    private String getTaskExplain(Integer anomalyTypeOne, Integer anomalyTypeTwo) {
        return WorkOrderAnomalyTypeOneEnum.getEnumByKey(anomalyTypeOne).getValue()
                + "/"
                + WorkOrderAnomalyTypeTwoEnum.getEnumByKey(anomalyTypeTwo).getValue();
    }

    public WorkOrderReportSyncRequestDto of(WorkOrderReportBoModel boModel) {
        WorkOrderReportSyncRequestDto requestDto = new WorkOrderReportSyncRequestDto();
        TWorkOrder entity = boModel.getEntity();
        if (Objects.nonNull(entity)) {
            requestDto
                    .setTaskResume(entity.getAnomalyTypeOne(), entity.getAnomalyTypeTwo())
                    .setTaskExplain(entity.getAnomalyTypeOne(), entity.getAnomalyTypeTwo(), entity.getRemark())
                    .setLevel(entity.getWorkOrderPriority().toString())
                    .setReportBy(entity.getReportUserName())
                    .setReportByPhone(boModel.getReportByPhone())
                    .setCode(entity.getWorkOrderCode())
                    .setCustomer(boModel.getCustomer())
                    .setCustomerPhone(entity.getContactTelephone())
                    .setCustomerContacts(entity.getContactName())
                    .setIsDriverOnSite(Objects.equals(entity.getIsArriveScene(), CommonConstant.INTEGER_ONE) ?
                            CommonConstant.INTEGER_ONE.toString() : CommonConstant.INTEGER_TWO.toString())
                    .setUserBy(entity.getReportUserName())
                    .setBusCode(boModel.getBusCode())
                    .setVehicleNumber(boModel.getVehicleNumber())
                    .setDriverName(entity.getDriverName())
                    .setClient(boModel.getClient())
            ;
        }
        return requestDto;
    }

    private interface ReportConfigKey {
        String TMS_KEY = "tms";
        String TASK_TYPE = "8";
        String BUS_TYPE = "2";
    }
}
