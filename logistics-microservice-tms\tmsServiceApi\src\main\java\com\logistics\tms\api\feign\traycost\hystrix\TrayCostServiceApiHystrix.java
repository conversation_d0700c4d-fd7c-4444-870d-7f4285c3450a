package com.logistics.tms.api.feign.traycost.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.traycost.TrayCostServiceApi;
import com.logistics.tms.api.feign.traycost.model.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/20 19:15
 */
@Component
public class TrayCostServiceApiHystrix implements TrayCostServiceApi {
    @Override
    public Result<List<SearchTrayCostListResponseModel>> searchTrayCostList() {
        return Result.timeout();
    }

    @Override
    public Result<TrayCostDetailResponseModel> getDetail(TrayCostIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result modifyPrice(ModifyPriceRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<SearchCostRecordsListResponseModel>> searchCostRecordsList(SearchCostRecordsListRequestModel requestModel) {
        return Result.timeout();
    }
}
