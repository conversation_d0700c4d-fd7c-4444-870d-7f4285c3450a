package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/10/9
 */
@Data
public class UpdateCarrierOrderUnloadAddressDetailRequestDto {

	@ApiModelProperty(value = "运单ID", required = true)
	@NotEmpty(message = "id不能为空")
	private List<String> carrierOrderId;
}
