package com.logistics.management.webapi.api.impl.dispatchorder;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import com.logistics.management.webapi.api.feign.dispatchorder.DispatchOrderApi;
import com.logistics.management.webapi.api.feign.dispatchorder.dto.*;
import com.logistics.management.webapi.api.impl.dispatchorder.mapper.DispatchOrderDetailMapping;
import com.logistics.management.webapi.api.impl.dispatchorder.mapper.GetCarrierOrderByDispatchOrderIdMapping;
import com.logistics.management.webapi.api.impl.dispatchorder.mapper.SearchDispatchOrderListMapping;
import com.logistics.tms.api.feign.dispatchorder.DispatchOrderServiceApi;
import com.logistics.tms.api.feign.dispatchorder.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@RestController
public class DispatchOrderApiImpl implements DispatchOrderApi {

    @Autowired
    private DispatchOrderServiceApi dispatchOrderServiceApi;

    /**
     * 调度单列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<DispatchOrderSearchResponseDto>> searchList(@RequestBody DispatchOrderSearchRequestDto requestDto) {
        DispatchOrderSearchRequestModel model = MapperUtils.mapper(requestDto, DispatchOrderSearchRequestModel.class);
        Result<PageInfo<DispatchOrderSearchResponseModel>> pageInfoResult = dispatchOrderServiceApi.searchList(model);
        pageInfoResult.throwException();
        PageInfo pageInfo = pageInfoResult.getData();
        List<DispatchOrderSearchResponseDto> dtoList = MapperUtils.mapper(pageInfo.getList(),DispatchOrderSearchResponseDto.class, new SearchDispatchOrderListMapping());
        pageInfo.setList(dtoList);
        return Result.success(pageInfo);
    }

    /**
     * 根据调度单id查询运单列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<DispatchOrderCarrierChildResponseDto>> getCarrierOrder(@RequestBody @Valid DispatchOrderCarrierRequestDto requestDto) {
        Result<List<DispatchOrderCarrierChildResponseModel>> listResult = dispatchOrderServiceApi.getCarrierOrder(MapperUtils.mapper(requestDto, DispatchOrderCarrierRequestModel.class));
        if (!listResult.isSuccess()){
            throw new BizException(listResult.getErrcode(),listResult.getErrmsg());
        }
        List<DispatchOrderCarrierChildResponseDto> dtoList = new ArrayList<>();
        if (ListUtils.isNotEmpty(listResult.getData())){
            List<DispatchOrderCarrierChildResponseModel> modelList = listResult.getData();
            dtoList = MapperUtils.mapper(modelList,DispatchOrderCarrierChildResponseDto.class, new GetCarrierOrderByDispatchOrderIdMapping());
        }
        return Result.success(dtoList);
    }

    /**
     * 调度单详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<DispatchOrderDetailResponseDto> getDetail(@RequestBody @Valid DispatchOrderCarrierRequestDto requestDto) {
        Result<DispatchOrderDetailResponseModel> responseResult = dispatchOrderServiceApi.getDetail(MapperUtils.mapper(requestDto, DispatchOrderCarrierRequestModel.class));
        responseResult.throwException();
        DispatchOrderDetailResponseDto responseDto=new DispatchOrderDetailResponseDto();
        if (null != responseResult.getData()){
            responseDto = MapperUtils.mapper(responseResult.getData(), DispatchOrderDetailResponseDto.class, new DispatchOrderDetailMapping());
        }
        return Result.success(responseDto);
    }


}
