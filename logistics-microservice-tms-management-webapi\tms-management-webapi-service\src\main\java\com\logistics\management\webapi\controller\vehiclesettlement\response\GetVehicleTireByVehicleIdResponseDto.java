package com.logistics.management.webapi.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/10/14 9:27
 */
@Data
public class GetVehicleTireByVehicleIdResponseDto {
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty("司机姓名")
    private String driverName="";
    @ApiModelProperty("手机号")
    private String driverMobile="";
    @ApiModelProperty(value = "轮胎牌号")
    private String tireBrand="";
    @ApiModelProperty(value = "数量")
    private String amount="";
    @ApiModelProperty("单价")
    private String unitPrice="";
    @ApiModelProperty("费用")
    private String tireCost="";
    @ApiModelProperty("轮胎企业")
    private String tireCompany="";
    @ApiModelProperty("更换日期")
    private String replaceDate="";
}
