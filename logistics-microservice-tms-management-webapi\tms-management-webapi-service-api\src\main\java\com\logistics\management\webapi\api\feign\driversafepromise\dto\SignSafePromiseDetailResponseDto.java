package com.logistics.management.webapi.api.feign.driversafepromise.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/11/6 16:52
 */
@Data
public class SignSafePromiseDetailResponseDto {
    @ApiModelProperty("承诺书ID")
    private String safePromiseId = "";
    @ApiModelProperty("签订承诺书ID")
    private String signSafePromiseId = "";
    @ApiModelProperty("司机ID")
    private String staffId;
    @ApiModelProperty("司机名称")
    private String staffName = "";
    @ApiModelProperty("车牌号")
    private String vehicleNo = "";

    @ApiModelProperty("手持承诺书图片地址-相对路径")
    private String handPromiseUrl = "";
    @ApiModelProperty("手持承诺书图片地址-绝对路径")
    private String absoluteHandPromiseUrl = "";

    @ApiModelProperty("签字责任书图片地址-相对路径")
    private String signResponsibilityUrl = "";
    @ApiModelProperty("签字责任书图片地址-绝对路径")
    private String absoluteSignResponsibilityUrl = "";
}
