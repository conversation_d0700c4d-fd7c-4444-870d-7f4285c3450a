package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/12/13 15:32
 */
@Data
public class CarrierOrderBeforeSignUpResponseDto {
    @ApiModelProperty("运单信息")
    private List<CarrierOrderListBeforeSignUpResponseDto> carrierOrderList;
    @ApiModelProperty("货物单位：1 件，2 吨，3 件（方），4 块")
    private String goodsUnit="";
    @ApiModelProperty("数量")
    private String amountTotal="";
    @ApiModelProperty("体积")
    private String capacityTotal="";
    @ApiModelProperty("司机运费合计")
    private String dispatchFreightFeeTotal="";
}
