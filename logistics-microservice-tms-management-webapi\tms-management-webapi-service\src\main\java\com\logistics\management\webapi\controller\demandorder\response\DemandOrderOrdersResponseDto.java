package com.logistics.management.webapi.controller.demandorder.response;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DemandOrderOrdersResponseDto {

    @ApiModelProperty("客户单号")
    private String orderCode= "";
    @ApiModelProperty("数量")
    private String totalAmount= "";
    @ApiModelProperty("类型")
    private String relType= "";
    @ApiModelProperty("备注")
    private String remark= "";
    @ApiModelProperty("下单人")
    private String createdBy= "";
    @ApiModelProperty("同步时间")
    private String createdTime= "";
}
