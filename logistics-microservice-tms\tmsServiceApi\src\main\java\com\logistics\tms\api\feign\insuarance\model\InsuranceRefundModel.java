package com.logistics.tms.api.feign.insuarance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2019/12/25 17:45
 */
@Data
public class InsuranceRefundModel {
    @ApiModelProperty("险种：1 商业险，2 交强险，3 个人意外险，4 货物险，5 危货承运人险")
    private Integer insuranceType;
    @ApiModelProperty(value = "保单Id")
    private Long insuranceId;
    @ApiModelProperty(value = "是否退保：0 不退，1 退保")
    private Integer ifRefund;
    @ApiModelProperty(value = "退保金额")
    private BigDecimal refundPremium;
    @ApiModelProperty("图片相对路径")
    private String filePath;
}
