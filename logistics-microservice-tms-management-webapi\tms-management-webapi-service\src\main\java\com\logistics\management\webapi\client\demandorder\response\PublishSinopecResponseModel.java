package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 获取需求单单个发布详情(中石化) - 响应model
 *
 * @author: wei.wang
 * @date: 2021/12/4
 */
@Data
public class PublishSinopecResponseModel {

	@ApiModelProperty("是否网货 0: 否 1: 是")
	private Integer sinopecOnlineGoodsFlag;
	@ApiModelProperty("需求单id")
	private Long demandId;
	@ApiModelProperty("需求单号")
	private String demandOrderCode;
	@ApiModelProperty("货主公司id")
	private Long companyEntrustId;
	@ApiModelProperty("货主公司名称")
	private String companyEntrustName;
	@ApiModelProperty("生产企业")
	private String manufacturerName;

	@ApiModelProperty("发货省份id")
	private Long loadProvinceId;
	@ApiModelProperty("发货省份名字")
	private String loadProvinceName;
	@ApiModelProperty("发货城市id")
	private Long loadCityId;
	@ApiModelProperty("发货城市名字")
	private String loadCityName;
	@ApiModelProperty("发货县区id")
	private Long loadAreaId;
	@ApiModelProperty("发货县区名字")
	private String loadAreaName;
	@ApiModelProperty("发货详细地址")
	private String loadDetailAddress;
	@ApiModelProperty("发货仓库")
	private String loadWarehouse;

	@ApiModelProperty("收货省份id")
	private Long unloadProvinceId;
	@ApiModelProperty("收货省份名字")
	private String unloadProvinceName;
	@ApiModelProperty("收货城市id")
	private Long unloadCityId;
	@ApiModelProperty("收货城市名字")
	private String unloadCityName;
	@ApiModelProperty("收货县区id")
	private Long unloadAreaId;
	@ApiModelProperty("收货县区名字")
	private String unloadAreaName;
	@ApiModelProperty("收货详细地址")
	private String unloadDetailAddress;

	@ApiModelProperty("委托数量")
	private BigDecimal goodsAmount;
	@ApiModelProperty("货物单位：1 件，2 吨，3 方，4 块")
	private Integer goodsUnit;
	@ApiModelProperty("备注")
	private String remark;

	private Long demandOrderAddressId;//地址表ID
	private String customerOrderCode;
	private String sinopecOrderNo;
	private Integer entrustStatus;
	private Integer ifCancel;
	private Integer ifEmpty;
	private Integer source;
	private Integer orderType;
	private Long sinopecCustomerId;
	private String sinopecCustomerName;

	@ApiModelProperty("是否异常（中石化推送单子）：0 否，1 是")
	private Integer ifObjectionSinopec;
}
