package com.logistics.management.webapi.controller.demandorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.client.demandorder.response.ModifyCarrierDetailResponseModel;
import com.logistics.management.webapi.controller.demandorder.response.ModifyCarrierDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

public class ModifyCarrierDetailMapping extends MapperMapping<ModifyCarrierDetailResponseModel, ModifyCarrierDetailResponseDto> {
    @Override
    public void configure() {
        ModifyCarrierDetailResponseModel source = getSource();
        ModifyCarrierDetailResponseDto destination = getDestination();

        //个人车主展示姓名+手机号
        if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())) {
            destination.setCompanyCarrierName(source.getCarrierContactName() + " " + source.getCarrierContactPhone());
        }

        //我司情况下
        if (CommonConstant.INTEGER_ONE.equals(source.getIsOurCompany())) {
            destination.setCarrierPrice("");
            destination.setCarrierPriceType("");
            destination.setCompanyCarrierId("");
            destination.setCompanyCarrierName("");
        }
    }
}
