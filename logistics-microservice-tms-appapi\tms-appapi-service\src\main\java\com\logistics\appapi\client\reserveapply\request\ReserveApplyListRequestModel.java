package com.logistics.appapi.client.reserveapply.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReserveApplyListRequestModel extends AbstractPageForm<ReserveApplyListRequestModel> {

    @ApiModelProperty("状态：-1 已撤销，0 待业务审核，1 待财务审核，2 已驳回，3 待打款，4 已打款")
    private Integer status;

    @ApiModelProperty("申请单号")
    private String applyCode;

    @ApiModelProperty("司机")
    private String driver;

    @ApiModelProperty("机构:1:自主 2:外部 3:自营")
    private Integer staffProperty;

    @ApiModelProperty("申请时间起")
    private String applyTimeStart;

    @ApiModelProperty("申请时间起")
    private String applyTimeEnd;

    @ApiModelProperty("财务审核时间起")
    private String financialAuditTimeStart;

    @ApiModelProperty("审核时间止")
    private String financialAuditTimeEnd;

    @ApiModelProperty("打款时间起")
    private String payTimeStart;

    @ApiModelProperty("打款时间止")
    private String payTimeEnd;

}
