package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TDriverPayee extends BaseEntity {
    /**
    * 收款人姓名
    */
    @ApiModelProperty("收款人姓名")
    private String name;

    /**
    * 收款人联系方式
    */
    @ApiModelProperty("收款人联系方式")
    private String mobile;

    /**
    * 身份证号
    */
    @ApiModelProperty("身份证号")
    private String identityNo;

    /**
    * 银行ID
    */
    @ApiModelProperty("银行ID")
    private Long bankId;

    /**
    * 银行卡号
    */
    @ApiModelProperty("银行卡号")
    private String bankCardNo;

    /**
    * 审核状态  -2 已作废 0 待审核 1 已审核 2 已驳回
    */
    @ApiModelProperty("审核状态  -2 已作废 0 待审核 1 已审核 2 已驳回")
    private Integer auditStatus;

    /**
     * 审核时间
     */
    @ApiModelProperty("审核时间")
    private Date auditTime;

    /**
    * 审核人ID
    */
    @ApiModelProperty("审核人ID")
    private Long auditorId;

    /**
    * 审核人姓名
    */
    @ApiModelProperty("审核人姓名")
    private String auditorName;

    /**
    * 驳回原因
    */
    @ApiModelProperty("驳回原因")
    private String auditReason;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
}