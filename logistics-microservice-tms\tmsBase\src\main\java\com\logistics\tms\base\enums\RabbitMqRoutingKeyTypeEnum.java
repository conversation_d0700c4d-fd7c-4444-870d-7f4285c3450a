package com.logistics.tms.base.enums;

public enum RabbitMqRoutingKeyTypeEnum {
    CANCEL_DEMAND_ORDER("cancelDemandOrder", "取消需求单routingKey"),
    CARRIER_ORDER_SYNCHRONIZED_ROUTINGKEY("carrier.order.synchronized.routingkey", "运单信息同步routingKey"),
    COPY_CARRIER_ORDER("copyCarrierOrder", "复制运单信息到乐医服务routingKey"),
    SEND_WEIXIN_TEMPLATE_MESSAGE("logisticsSendWeixinTemplateMessage","发送微信模板信息routingKey"),
    COMPLETE_DEMAND_ORDER("completeDemandOrder", "完成需求单需求单的状态影响routingKey"),
    CREATE_LOGISTICS_DEMAND_SETTLE("createLogisticsDemandSettle","将托盘的需求单货主结算信息同步给托盘 routingKey"),
    CANCEL_CARRIER_ORDER("cancelCarrierOrderChangeDemandStateByLogistics","取消运单改变需求单状态routingKey"),
    SYNC_DEMAND_ORDER("saveSyncTMSDemandOrderToLogistics","同步委托单到网络货运routingKey"),
    SYNC_DEMAND_ORDER_UNLOAD_ADDRESS("syncDemandOrderUnLoadAddress","将回收类需求单卸货地址同步给托盘 routingKey"),
    CARRIER_ORDER_CHANG_UNLOAD_WAREHOUSE("carrierOrderChangeUnloadWarehouse","将修改后的运单卸货地址同步给云仓 routingKey"),
    UPDATE_CARRIER_ORDER_BILL("updateCarrierOrderBill","同步出库单给云盘 routingKey"),
    SYNC_ADDITIONAL_ORDER("syncAdditionalOrder","网络货运 同步补单、追加 routingKey"),
    ORDER_CANCELDEMANDORDER("cancelDemandOrder","取消需求单同步云盘 routingKey"),
    ORDER_SYNCHRONIZED_CARRIER_ORDER("synchronizedCarrierOrder","物流各节点信息同步云盘 routingKey"),
    DISPATCH_COMPLETE("dispatchComplete","完成调度同步云盘 routingKey"),
    ORDER_UPDATE_CARRIERORDER_ADDRESS("updateCarrierOrderAddress","修改卸货地址同步云盘 routingKey"),
    ORDER_DEMAND_BACK_COUNT("demandBackCount","需求单回退数量给云盘 routingKey"),
    ORDER_CANCEL_CARRIER_ORDER("cancelCarrierOrderChangeDemandStateByLogistics","取消运单同步云盘 routingKey"),
    SYNC_LOGISTICS_LIFE_ORDER_INFO("lifeRecycleOrderMessage","同步新生回收单信息 routingKey"),
    SYNC_BILL_ORDER_TO_YELO_LIFE("lifeBillOrderMessage","推送给新生账单 routingKey"),
    ROLLBACK_DEMAND_ORDER_TO_LEYI("backDemandOrder","回收类型需求单回退，将回退信息同步给云盘 routingKey"),
    SYNC_SIGN_DEMAND_ORDER_TO_GROUND_PUSH("receiveSignDemandOrderFromTms","需求单（HR单）签收后同步给地推 routingKey"),
    SYNC_UPDATE_WECHAT_USER_ID("updateWechatUserId","更新t_user表绑定的企微id routingKey"),
    SEND_WEB_SOCKET_MESSAGE("sendWebSocketMessage","发送webSocket routingKey"),
    TMS_SYNC_LIFE_DEMAND_ORDER("tmsSyncLifeDemandOrder","需求单节点操作同步新生"),
    TMS_SYNC_LIFE_CARRIER_ORDER("tmsSyncLifeCarrierOrder","运单节点操作同步新生"),
    RESERVATION_ORDER_CREATE_TO_WAREHOUSE("reservationOrderCreate", "创建预约单同步云仓"),
    RESERVATION_ORDER_INVALID_TO_WAREHOUSE("reservationOrderInvalid", "预约单失效同步云仓"),
    RESERVATION_CARRIER_ORDER_INVALID_TO_WAREHOUSE("reservationOrderError", "预约单下运单失效同步云仓"),
    RESERVATION_ORDER_SIGN_IN_TO_WAREHOUSE("reservationOrderUpdateSignTime", "预约单签到同步云仓"),
    DEAL_REPLENISH_ORDER("dealReplenishOrder", "延时处理后补需求单"),
//    SYNC_RECYCLE_OUT_PRODUCT_CODE_TO_WAREHOUSE("syncRecycleOutProductCodeToWarehouse", "同步云仓回收出的编码"),
    DELAY_MESSAGE_CONSUMER("delayMessageConsumer","发送延时队列")


    ;

    private String key;
    private String value;

    RabbitMqRoutingKeyTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
