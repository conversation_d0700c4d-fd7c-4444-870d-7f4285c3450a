package com.logistics.appapi.controller.baiscinfo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/3
 */
@Data
public class DriverBasicInfoResponseDto {

	@ApiModelProperty("司机姓名")
	private String driverName = "";

	@ApiModelProperty("司机手机号")
	private String driverMobile = "";

	@ApiModelProperty("司机身份证号")
	private String driverIdentityNumber = "";

	@ApiModelProperty(value = "认证类型,1:手机号认证 2:人脸识别")
	private String authModel = "";

	@ApiModelProperty(value = "认证类型展示文本")
	private String authModelLabel = "";

	@ApiModelProperty("实名状态, 0:待实名 1:实名中 2:已实名")
	private String realNameAuthenticationStatus = "";

	@ApiModelProperty("实名状态展示文本")
	private String realNameAuthenticationStatusLabel = "";
}
