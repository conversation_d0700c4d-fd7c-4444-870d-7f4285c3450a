package com.logistics.tms.api.feign.personalaccidentinsurance.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.personalaccidentinsurance.PersonalAccidentInsuranceServiceApi;
import com.logistics.tms.api.feign.personalaccidentinsurance.model.*;
import com.logistics.tms.api.feign.personalaccidentinsurance.model.ImportPersonalAccidentInsuranceResponseModel;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/5/30 11:21
 */
@Component("tmsPersonalAccidentInsuranceServiceApiHystrix")
public class PersonalAccidentInsuranceServiceApiHystrix implements PersonalAccidentInsuranceServiceApi {

    @Override
    public Result<PageInfo<PersonalAccidentInsuranceListResponseModel>> searchPersonalAccidentInsuranceList(PersonalAccidentInsuranceListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PersonalAccidentInsuranceDetailResponseModel> getPersonalAccidentInsuranceDetail(PersonalAccidentInsuranceIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addOrModifyPersonalAccidentInsurance(AddOrModifyPersonalAccidentInsuranceRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<PersonalAccidentInsuranceListResponseModel>> exportPersonalAccidentInsurance(PersonalAccidentInsuranceListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetInsuranceByPolicyNumberResponseModel>> getInsuranceByPolicyNumber(GetInsuranceByPolicyNumberRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchInsuranceByPolicyNumberResponseModel>> searchInsuranceByPolicyNumber(SearchInsuranceByPolicyNumberRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetPolicyNoPremiumByPolicyNoResponseModel>> getPolicyNoPremiumByPolicyNo(SearchInsuranceByPolicyNumberRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ImportPersonalAccidentInsuranceResponseModel> importPersonalAccidentInsurance(ImportPersonalAccidentInsuranceRequestModel requestModel) {
        return Result.timeout();
    }
}
