package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2021/10/18 17:09
 */
@Data
public class LogisticsLoadValidityStatisticsResponseDto {
    @ApiModelProperty("统计年月")
    private String yearMonth="";
    @ApiModelProperty("发货类型：单/天（提货运单：提货时效/提货数量）")
    private String deliverCount = "";
    @ApiModelProperty("回收类型：单/天（提货运单：提货时效/提货数量）")
    private String recycleCount ="";
    @ApiModelProperty("调拨类型：单/天（提货运单：提货时效/提货数量）")
    private String transfersCount = "";
}
