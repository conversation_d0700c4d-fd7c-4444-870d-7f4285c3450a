package com.logistics.management.webapi.client.reserveapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ApplyReserveBalanceRequestModel {

    @ApiModelProperty("申请记录id, 重新提交必填")
    private Long applyId;

    @ApiModelProperty(value = "申请金额,0<金额<=10000", required = true)
    private BigDecimal applyAmount;

    @ApiModelProperty(value = "收款账户", required = true)
    private String receiveBankAccount;

    @ApiModelProperty("备注,最大100个字符")
    private String remark;
}
