package com.logistics.appapi.controller.drivercostapply.response;

import com.logistics.appapi.controller.uploadfile.response.SrcUrlDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/8/3 9:28
 */
@Data
public class DriverCostApplyInvoiceResponseDto {

    @ApiModelProperty(value = "发票id")
    private String invoiceId;

    @ApiModelProperty(value = "发票类型;")
    private String type = "";

    @ApiModelProperty(value = "发票类型;")
    private String typeLabel = "";

    @ApiModelProperty(value = "发票名称;")
    private String invoiceName = "";

    @ApiModelProperty(value = "票据类型;")
    private String invoiceType = "";

    @ApiModelProperty(value = "发票代码;")
    private String invoiceCode = "";

    @ApiModelProperty(value = "发票号码;")
    private String invoiceNum = "";

    @ApiModelProperty(value = "合计金额;")
    private String totalPrice = "";

    @ApiModelProperty(value = "合计税额;")
    private String totalTax = "";

    @ApiModelProperty(value = "价税合计;")
    private String totalTaxAndPrice = "";

    @ApiModelProperty(value = "图片依据路径")
    private SrcUrlDto imagePathList;
}
