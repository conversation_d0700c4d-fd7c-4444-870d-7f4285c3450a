package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.freight.model.*;
import com.logistics.tms.entity.TFreight;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TFreightMapper extends BaseMapper<TFreight> {
    TFreight getByCompanyIdAndRoleType(@Param("companyId") Long companyId,@Param("roleType") Integer roleType);

    List<SearchFreightListResponseModel> searchEntrustFreightList(@Param("params") SearchFreightListRequestModel requestModel);

    List<SearchFreightListResponseModel> searchCarrierFreightList(@Param("params") SearchFreightListRequestModel requestModel);

    FreightCompanyInfoResponseModel getEntrustFreightCompanyInfo(@Param("freightId") Long freightId);

    FreightCompanyInfoResponseModel getCarrierFreightCompanyInfo(@Param("freightId") Long freightId);

    List<CompanyDriverFreightResponseModel> getDriverFreightInfo(@Param("list") List<CompanyDriverFreightRequestModel> list);

    GetFreightForEntrustResponseModel getEntrustFreightInfo(@Param("params") GetFreightForEntrustRequestModel requestModel);

}