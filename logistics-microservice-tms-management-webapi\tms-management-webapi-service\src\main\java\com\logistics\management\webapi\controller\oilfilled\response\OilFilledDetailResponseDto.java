package com.logistics.management.webapi.controller.oilfilled.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class OilFilledDetailResponseDto {

    @ApiModelProperty(value = "充油ID,修改时候传入，新增不传")
    private String oilFilledId = "";
    @ApiModelProperty(value = "充油方式：1 充油卡，2 加油车")
    private String oilFilledType= "";
    @ApiModelProperty(value = "充油方式文字")
    private String oilFilledTypeLabel= "";
    @ApiModelProperty(value = "状态")
    private String status= "";
    @ApiModelProperty(value = "状态文字")
    private String statusLabel= "";
    @ApiModelProperty(value = "车牌号Id")
    private String vehicleId= "";
    @ApiModelProperty(value = "车牌号号码")
    private String vehicleNo= "";
    @ApiModelProperty(value = "司机ID")
    private String staffId= "";
    @ApiModelProperty(value = "司机")
    private String name= "";
    @ApiModelProperty(value = "副卡卡号，当充值方式为充油卡时候传")
    private String subCardNumber= "";
    @ApiModelProperty(value = "副卡所属人，当充值方式为充油卡时候传")
    private String subCardOwner= "";
    @ApiModelProperty(value = "充值金额/总金额")
    private String oilFilledFee= "";
    @ApiModelProperty(value = "充值积分")
    private String topUpIntegral= "";
    @ApiModelProperty(value = "奖励积分")
    private String rewardIntegral= "";
    @ApiModelProperty(value = "充值时间/加油时间")
    private String oilFilledDate= "";
    @ApiModelProperty(value = "合作公司,当充值方式为加油车时候传")
    private String cooperationCompany= "";
    @ApiModelProperty(value = "升数,当充值方式为加油车时候传")
    private String liter= "";
    @ApiModelProperty(value = "备注")
    private String remark= "";

    @ApiModelProperty(value = "附件集合")
    private List<OilFilledFileDto> oilFilledFileList ;

    @ApiModelProperty(value = "是有有结算数据,1有，0没有")
    private String ifSettlement = "0";

}
