package com.logistics.appapi.controller.attendance.mappping;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.AttendanceChangeTypeEnum;
import com.logistics.appapi.client.attendance.response.UpdateAttendanceHistoryDetailResponseModel;
import com.logistics.appapi.controller.attendance.response.UpdateAttendanceHistoryDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ObjectUtils;

import java.time.LocalDate;
import java.time.ZoneId;

public class UpdateAttendanceHistoryDetailMapping extends MapperMapping<UpdateAttendanceHistoryDetailResponseModel, UpdateAttendanceHistoryDetailResponseDto> {

    @Override
    public void configure() {
        UpdateAttendanceHistoryDetailResponseModel source = getSource();
        UpdateAttendanceHistoryDetailResponseDto destination = getDestination();

        // enum处理
        destination.setChangeTypeLabel(AttendanceChangeTypeEnum.getEnumByKey(source.getChangeType()).getValue());

        // 工时处理
        destination.setManHour(source.getManHour().stripTrailingZeros().toPlainString());

        // 考勤日期时间格式转换
        LocalDate attendanceLocalDate = LocalDate.ofInstant(source.getAttendanceDate().toInstant(), ZoneId.systemDefault());
        String attendanceDate = LocalDate.now().isEqual(attendanceLocalDate) ?
                CommonConstant.DATE_TODAY : DateUtils.dateToString(source.getAttendanceDate(), CommonConstant.DATE_TO_STRING_MD_PATTERN_TEXT);
        destination.setAttendanceDate(attendanceDate);

        // 上班时间格式转换
        String onDutyPunchDateTime = DateUtils.dateToString(source.getOnDutyPunchTime(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN);
        String onDutyPunchTime = DateUtils.dateToString(source.getOnDutyPunchTime(), CommonConstant.DATE_TO_STRING_HM_PATTERN);
        destination.setOnDutyPunchDateTime(onDutyPunchDateTime);
        destination.setOnDutyPunchTime(onDutyPunchTime);

        // 下班时间格式转换
        if (ObjectUtils.isNotEmpty(source.getOffDutyPunchTime())) {
            String offDutyPunchDateTime = DateUtils.dateToString(source.getOffDutyPunchTime(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN);
            String offDutyPunchTime = DateUtils.dateToString(source.getOffDutyPunchTime(), CommonConstant.DATE_TO_STRING_HM_PATTERN);
            destination.setOffDutyPunchDateTime(offDutyPunchDateTime);
            destination.setOffDutyPunchTime(offDutyPunchTime);
        }

        // 变更打卡时间转换
        if (ObjectUtils.isNotEmpty(source.getChangePunchTime())) {
            String changePunchTime = DateUtils.dateToString(source.getChangePunchTime(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES);
            destination.setChangePunchTime(changePunchTime);
        }
    }
}
