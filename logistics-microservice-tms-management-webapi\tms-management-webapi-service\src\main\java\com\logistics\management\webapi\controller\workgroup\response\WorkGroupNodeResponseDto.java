package com.logistics.management.webapi.controller.workgroup.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class WorkGroupNodeResponseDto {

    @ApiModelProperty("智能推送配置表id")
    private String workGroupId = "";

    @ApiModelProperty(value = "(3.15.1)需求类型：1.回收业务(回收入库、回收出库) 2.采购业务（供应商直配、采购) 3.仓库业务（发货、调拨、退货、退货仓库送、退货调拨、其他）")
    private List<String> entrustTypeList;

    @ApiModelProperty(value = "(3.15.1)项目标签：0 标签为空，1 石化板块，2 轮胎板块，3 涂料板块，4 其他板块")
    private List<String> projectLabelList;

    @ApiModelProperty(value = "节点信息")
    private List<WorkGroupNodeListResponseDto> nodeList;
}
