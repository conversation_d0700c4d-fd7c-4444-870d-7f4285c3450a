package com.logistics.tms.client.feign.yelolife.basicdata.hystrix;

import com.logistics.tms.client.feign.yelolife.basicdata.YeloLifeBasicDataServiceApi;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class YeloLifeBasicDataServiceApiHystrix implements YeloLifeBasicDataServiceApi {


    @Override
    public Result<List<String>> verifyGoodsCodeList(List<String> goodsCodes) {
        return Result.timeout();
    }
}
