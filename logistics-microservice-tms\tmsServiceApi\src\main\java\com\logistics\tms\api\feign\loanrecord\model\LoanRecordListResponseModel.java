package com.logistics.tms.api.feign.loanrecord.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/9/30 9:37
 */
@Data
public class LoanRecordListResponseModel {
    @ApiModelProperty("贷款记录ID")
    private Long loanRecordId;
    @ApiModelProperty("贷款状态：0 待结算，1 部分结算，2 已结算")
    private Integer status;
    @ApiModelProperty("车辆ID")
    private Long vehicleId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("品牌")
    private String brand;
    @ApiModelProperty("型号")
    private String model;
    @ApiModelProperty("车辆识别号")
    private String vehicleIdentificationNumber;
    @ApiModelProperty("车辆引擎")
    private String engineNumber;
    @ApiModelProperty("车身颜色")
    private String bodyColor;
    @ApiModelProperty("生产地")
    private String producer;
    @ApiModelProperty("生产厂商")
    private String manufacturers;
    @ApiModelProperty("生产日期")
    private Date productionDate;
    @ApiModelProperty("司机id")
    private Long staffId;
    @ApiModelProperty("司机姓名")
    private String name;
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("身份证号")
    private String identityNumber;
    @ApiModelProperty("裸车价")
    private BigDecimal nakedCarPrice;
    @ApiModelProperty("保险费用")
    private BigDecimal insurancePremium;
    @ApiModelProperty("购置税")
    private BigDecimal purchaseTax;
    @ApiModelProperty("购车总价")
    private BigDecimal carPrice;
    @ApiModelProperty("司机已承担费用")
    private BigDecimal driverExpense;
    @ApiModelProperty("总贷款费用")
    private BigDecimal loanFee;
    @ApiModelProperty("贷款总期数")
    private Integer loanPeriods;
    @ApiModelProperty("贷款开始时间")
    private Date loanStartTime;
    @ApiModelProperty("贷款利率")
    private BigDecimal loanRate;
    @ApiModelProperty("贷款利息")
    private BigDecimal loanInterest;
    @ApiModelProperty("贷款手续费")
    private BigDecimal loanCommission;
    @ApiModelProperty("剩余金额")
    private BigDecimal remainingRepaymentFee;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("创建人")
    private String createdBy;
    @ApiModelProperty("创建时间")
    private Date createdTime;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;
}
