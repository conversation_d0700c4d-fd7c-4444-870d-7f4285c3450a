package com.logistics.management.webapi.api.feign.driversafepromise.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 详情
 * @Author: sj
 * @Date: 2019/11/4 10:21
 */
@Data
public class SafePromiseDetailResponseDto {
    @ApiModelProperty("承诺书ID")
    private String safePromiseId = "";
    @ApiModelProperty("周期（年份）")
    private String period = "";
    @ApiModelProperty("经办人")
    private String agent = "";
    @ApiModelProperty("上传时间")
    private String uploadTime ="";
    @ApiModelProperty("已签订人数")
    private String hasSignCount = "";
    @ApiModelProperty("标题")
    private String title = "";
    @ApiModelProperty("内容")
    private String content = "";

    @ApiModelProperty("附件相对路径")
    private String attachmentUrl = "";
    @ApiModelProperty("附件绝对路径")
    private String absoluteAttachmentUrl = "";

    @ApiModelProperty("已签订承诺司机列表")
    private List<String> driverList;
    @ApiModelProperty("所有关联司机ID列表")
    private List<String> allDriverList;
}
