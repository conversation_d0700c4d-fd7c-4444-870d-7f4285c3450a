package com.logistics.management.webapi.api.feign.dispatch;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.dispatch.dto.*;
import com.logistics.management.webapi.api.feign.dispatch.hystrix.DispatchApiHystrix;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

@Api(value = "API-DispatchServiceApi-调度管理")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = DispatchApiHystrix.class)
public interface DispatchApi {


    @ApiOperation(value = "调度车辆-搜索司机&车牌")
    @PostMapping(value = "/api/dispatch/searchDriverAndVehicle")
    Result<List<DriverAndVehicleSearchResponseDto>> searchDriverAndVehicle(@RequestBody DriverAndVehicleSearchRequestDto requestDto);

    @ApiOperation(value = "调度车辆-调度车辆完成调度查看详情v1.1.9")
    @PostMapping(value = "/api/dispatch/getDispatchDetail")
    Result<List<DemandOrderDispatchResponseDto>> getDispatchDetail(@RequestBody DemandOrderDispatchRequestDto requestDto);

    @ApiOperation(value = "调度车辆-完成调度 V1.1.1")
    @PostMapping(value = "/api/dispatch/saveCompleteDispatch")
    Result<Boolean> saveCompleteDispatch(@RequestBody @Valid CompleteDemandOrderRequestDto requestDto);

    @ApiOperation(value = "调度车辆-分页搜索司机v1.1.9")
    @PostMapping(value = "/api/dispatch/searchDriver")
    Result<PageInfo<DriverSearchResponseDto>> searchDriver(@RequestBody @Valid DriverSearchRequestDto requestDto);

    @ApiOperation(value = "调度车辆-分页搜索车辆 v1.2.3")
    @PostMapping(value = "/api/dispatch/searchVehicle")
    Result<PageInfo<VehicleSearchResponseDto>> searchVehicle(@RequestBody @Valid VehicleSearchRequestDto requestDto);
}