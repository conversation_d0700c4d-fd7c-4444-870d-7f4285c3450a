package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

public class ExportExcelHeaderDemandOrderObjection {
    private ExportExcelHeaderDemandOrderObjection() {

    }

    private static final Map<String, String> EXCEL_DEMAND_ORDER_OBJECTION;

    static {
        EXCEL_DEMAND_ORDER_OBJECTION = new LinkedHashMap<>();
        EXCEL_DEMAND_ORDER_OBJECTION.put("需求单状态", "statusDesc");
        EXCEL_DEMAND_ORDER_OBJECTION.put("需求单号", "demandOrderCode");
        EXCEL_DEMAND_ORDER_OBJECTION.put("上报时间", "reportTime");
        EXCEL_DEMAND_ORDER_OBJECTION.put("上报人", "reportContactName");
        EXCEL_DEMAND_ORDER_OBJECTION.put("客户名称", "customerName");
        EXCEL_DEMAND_ORDER_OBJECTION.put("区域", "loadRegionName");
        EXCEL_DEMAND_ORDER_OBJECTION.put("问题类型","objectionTypeDesc");
        EXCEL_DEMAND_ORDER_OBJECTION.put("问题描述","objectionReason");
        EXCEL_DEMAND_ORDER_OBJECTION.put("需求类型", "entrustType");
    }

    public static Map<String, String> getExcelDemandOrderObjection() {
        return EXCEL_DEMAND_ORDER_OBJECTION;
    }
}
