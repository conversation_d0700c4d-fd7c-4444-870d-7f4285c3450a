package com.logistics.appapi.controller.login.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/10/22
 * @description:
 */
@Data
public class UserInfoResponseDto {

    @ApiModelProperty(value = "用户姓名")
    private String userName = "";

    @ApiModelProperty(value = "用户账号")
    private String userAccount = "";

    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private String staffProperty = "";

    @ApiModelProperty("实名状态, 0:待实名 1:实名中 2:已实名")
    private String realNameAuthenticationStatus = "";

    @ApiModelProperty("实名状态展示文本")
    private String realNameAuthenticationStatusLabel = "";

    @ApiModelProperty("是否可跳转云仓小程序: 0 不可跳转 1 可跳转")
    private String warehouseSwitch = "";
}
