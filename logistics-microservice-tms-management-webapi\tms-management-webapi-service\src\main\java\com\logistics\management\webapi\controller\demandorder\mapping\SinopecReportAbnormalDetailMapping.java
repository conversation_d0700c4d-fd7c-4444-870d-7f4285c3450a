package com.logistics.management.webapi.controller.demandorder.mapping;

import com.logistics.management.webapi.client.demandorder.response.SinopecReportAbnormalDetailResponseModel;
import com.logistics.management.webapi.controller.demandorder.response.SinopecReportAbnormalDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

public class SinopecReportAbnormalDetailMapping extends MapperMapping<SinopecReportAbnormalDetailResponseModel, SinopecReportAbnormalDetailResponseDto> {
	@Override
	public void configure() {
		SinopecReportAbnormalDetailResponseModel source = getSource();
		SinopecReportAbnormalDetailResponseDto target = getDestination();

		//去掉BigDecimal后面无效的0
		target.setGoodsAmount(source.getGoodsAmount().stripTrailingZeros().toPlainString());
	}
}
