<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TVehicleSafeCheckReformMapper">
    <select id="getBySafeCheckVehicleId" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/>
      from t_vehicle_safe_check_reform
      where valid = 1
      and safe_check_vehicle_id = #{safeCheckVehicleId,jdbcType = BIGINT}
    </select>
</mapper>