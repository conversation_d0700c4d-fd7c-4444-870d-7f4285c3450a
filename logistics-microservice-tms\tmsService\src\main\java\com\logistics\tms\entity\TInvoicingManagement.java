package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2024/03/20
*/
@Data
public class TInvoicingManagement extends BaseEntity {
    /**
    * 业务名称
    */
    @ApiModelProperty("业务名称")
    private String businessName;

    /**
    * 开票月份
    */
    @ApiModelProperty("开票月份")
    private String invoicingMonth;

    /**
    * 业务类型：1 包装业务，2 自营业务
    */
    @ApiModelProperty("业务类型：1 包装业务，2 自营业务")
    private Integer businessType;

    /**
    * 车主公司id
    */
    @ApiModelProperty("车主公司id")
    private Long companyCarrierId;

    /**
    * 车主名称
    */
    @ApiModelProperty("车主名称")
    private String companyCarrierName;

    /**
    * 手机号（原长度50）
    */
    @ApiModelProperty("手机号（原长度50）")
    private String contactPhone;

    /**
    * 联系人姓名
    */
    @ApiModelProperty("联系人姓名")
    private String contactName;

    /**
    * 车主类型：1 公司，2 个人
    */
    @ApiModelProperty("车主类型：1 公司，2 个人")
    private Integer companyCarrierType;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}