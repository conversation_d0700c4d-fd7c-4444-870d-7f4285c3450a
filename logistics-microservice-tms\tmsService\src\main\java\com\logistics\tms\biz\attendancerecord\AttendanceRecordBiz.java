package com.logistics.tms.biz.attendancerecord;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.common.model.WaterMarkModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.attendancerecord.handle.AttendancePunchHandle;
import com.logistics.tms.biz.attendancerecord.handle.AttendancePunchHandleFactory;
import com.logistics.tms.biz.attendancerecord.model.AttendanceClockModel;
import com.logistics.tms.biz.attendancerecord.model.AttendanceStatisticalModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.attendance.request.*;
import com.logistics.tms.controller.attendance.response.*;
import com.logistics.tms.entity.TAttendanceChangeApply;
import com.logistics.tms.entity.TAttendanceRecord;
import com.logistics.tms.mapper.TAttendanceChangeApplyMapper;
import com.logistics.tms.mapper.TAttendanceRecordMapper;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.ObjectUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AttendanceRecordBiz {

    @Resource
    private CommonBiz commonBiz;
    @Resource
    private AttendancePunchHandleFactory punchHandleFactory;
    @Resource
    private TAttendanceRecordMapper attendanceRecordMapper;
    @Resource
    private TAttendanceChangeApplyMapper attendanceChangeApplyMapper;

    /**
     * 根据token查询员工当日打卡状态
     *
     * @return AttendanceClockDetailResponseModel
     */
    public AttendanceClockDetailResponseModel attendanceClockDetail() {

        AttendanceClockDetailResponseModel attendanceClockDetailResponseModel = new AttendanceClockDetailResponseModel()
                .setIsOverClockInType(AttendancePunchTypeEnum.NOT_CLOCK_IN.getKey());

        // 获取当前登录员工
        Long loginDriverAppletUserId = getLoginDriverAppletUserId();

        // 获取员工当日考勤记录
        var todayTAttendanceRecord = getTodayAttendanceRecord(loginDriverAppletUserId);

        // 考勤记录处理
        todayTAttendanceRecord.ifPresent(record -> {
            AttendancePunchTypeEnum punchTypeEnum = getPunchType(todayTAttendanceRecord);
            attendanceClockDetailResponseModel
                    .setIsOverClockInType(punchTypeEnum.getKey())
                    .setOnDutyPunchTime(record.getOnDutyPunchTime())
                    .setOffDutyPunchTime(record.getOffDutyPunchTime());
        });

        return attendanceClockDetailResponseModel;
    }

    // 处理当日打卡状态
    private AttendancePunchTypeEnum getPunchType(Optional<TAttendanceRecord> lastTAttendanceRecord) {
        return lastTAttendanceRecord
                .filter(f -> ObjectUtils.isNotEmpty(f.getOnDutyPunchTime()))
                .filter(f -> {
                    LocalDate latOnDutyPunchLocalDate = LocalDate.ofInstant(f.getOnDutyPunchTime().toInstant(), ZoneId.systemDefault());
                    return LocalDate.now().isEqual(latOnDutyPunchLocalDate);
                })
                .map(record -> {
                    // 打卡类型区分
                    return Objects.isNull(record.getOffDutyPunchTime()) ?
                            AttendancePunchTypeEnum.ON_DUTY_CLOCK_IN : AttendancePunchTypeEnum.OFF_DUTY_CLOCK_IN;
                })
                .orElse(AttendancePunchTypeEnum.NOT_CLOCK_IN);
    }

    // 获取员工id
    private Long getLoginDriverAppletUserId() {
        // 获取员工id
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        if (CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.DATA_ERROR_AND_RE_LOGIN);
        }
        return loginDriverAppletUserId;
    }

    // 获取员工当日打卡记录
    private Optional<TAttendanceRecord> getTodayAttendanceRecord(Long loginDriverAppletUserId) {
        // 获取员工最近考勤记录
        Date today = DateUtils.localDate2Date(LocalDate.now());
        return Optional.ofNullable(attendanceRecordMapper.selectByStaffIdAndDate(loginDriverAppletUserId, today));
    }

    /**
     * 根据经纬度查询地址
     *
     * @param requestModel
     * @return QueryPathByLonAndLatResponseModel
     */
    public QueryPathByLonAndLatResponseModel queryPathByLonAndLat(QueryPathByLonAndLatRequestModel requestModel) {
        String lonLatSplicing = String.join(",", requestModel.getLon(), requestModel.getLat());
        String location = commonBiz.getAddressByLonAndLat(lonLatSplicing);
        return new QueryPathByLonAndLatResponseModel().setLocation(location);
    }

    /**
     * 考勤打卡
     *
     * @param requestModel
     */
    @Transactional
    public boolean attendanceClock(AttendanceClockRequestModel requestModel) {

        // 过滤打卡Handel
        AttendancePunchHandle punchHandle = punchHandleFactory.getHandle(requestModel.getPunchType());

        // 获取登录员工id
        Long loginDriverAppletUserId = getLoginDriverAppletUserId();

        // 获取员工当日考勤记录
        var todayAttendanceRecord = getTodayAttendanceRecord(loginDriverAppletUserId);

        // 获取当日打卡状态
        AttendancePunchTypeEnum todayPunchType = getPunchType(todayAttendanceRecord);

        // 上传图片及水印
        String uploadPic = uploadAttendancePunchImage(requestModel.getPunchPic(), requestModel.getPunchLocation());

        // 构建BO
        var attendanceClockBoModel = new AttendanceClockModel()
                .setStaffId(loginDriverAppletUserId)
                .setPunchType(requestModel.getPunchType())
                .setToDayPunchType(todayPunchType.getKey())
                .setDutyPunchLocation(requestModel.getPunchLocation())
                .setDutyPunchPic(uploadPic);
        todayAttendanceRecord.ifPresent(r -> {
            attendanceClockBoModel.setRecodeId(r.getId());
            attendanceClockBoModel.setOnDutyPunchTime(r.getOnDutyPunchTime());
        });

        // 打卡逻辑处理
        return punchHandle.handle(attendanceClockBoModel);
    }

    // 上传打卡图片及水印
    public String uploadAttendancePunchImage(String dutyPunchPic, String dutyPunchLocation) {
        Date now = new Date();
        WaterMarkModel waterMarkModel = new WaterMarkModel();
        waterMarkModel.setWaterMark(DateUtils.dateToString(now,DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
        waterMarkModel.setWaterMarkTwo(dutyPunchLocation);
        return commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.ATTENDANCE_DUTY_PUNCH.getKey(),"", dutyPunchPic, waterMarkModel);
    }

    /**
     * 考勤历史（按月份展示）
     *
     * @param requestModel
     * @return AttendanceHistoryListResponseModel
     */
    public AttendanceHistoryListResponseModel attendanceHistoryList(AttendanceHistoryListRequestModel requestModel) {

        AttendanceHistoryListResponseModel responseModel = new AttendanceHistoryListResponseModel();

        // 获取登录员工id
        Long loginDriverAppletUserId = getLoginDriverAppletUserId();

        // 按月份查询考勤历史
        requestModel.enablePaging();
        List<AttendanceHistoryItemResponseModel> historyItemModelList = attendanceRecordMapper.selectHistoryByStaffAndAttendanceDate(loginDriverAppletUserId,
                requestModel);
        PageInfo<AttendanceHistoryItemResponseModel> pageHistory = new PageInfo<>(historyItemModelList);
        if (CollectionUtil.isEmpty(historyItemModelList)) {
            responseModel.setClockInCount(CommonConstant.INTEGER_ZERO);
            responseModel.setManHourSum(BigDecimal.ZERO);
            responseModel.setAttendanceHistoryItem(pageHistory);
            return responseModel;
        }

        // 考勤申请处理
        historyRecordApplyHandel(requestModel.getAttendanceDate(), pageHistory);

        // 统计工时以及考勤次数
        AttendanceStatisticalModel attendanceStatisticalModel = attendanceRecordMapper.selectStatistical(loginDriverAppletUserId,
                requestModel.getAttendanceDate());
        responseModel.setManHourSum(attendanceStatisticalModel.getTotalManHour());
        // 上下班打卡各算一次，一天就是两次
        responseModel.setClockInCount(attendanceStatisticalModel.getTotalOnDutyPunch() + attendanceStatisticalModel.getTotalOffDutyPunch());
        responseModel.setAttendanceHistoryItem(pageHistory);
        return responseModel;
    }

    // 考勤申请处理
    private void historyRecordApplyHandel(String requestAttendanceDate, PageInfo<AttendanceHistoryItemResponseModel> pageHistory) {

        // 查询待审核考勤申请
        List<Long> historyRecordIds = pageHistory.getList().stream()
                .map(AttendanceHistoryItemResponseModel::getAttendanceRecordId)
                .collect(Collectors.toList());
        List<TAttendanceChangeApply> applyList = attendanceChangeApplyMapper.selectByAttendanceRecordIdIn(historyRecordIds);

        if (ListUtils.isNotEmpty(applyList)) {

            // 申请修改状态只有当月有效
            String currentMonth = LocalDate.now().format(DateTimeFormatter.ofPattern(CommonConstant.DATE_TO_STRING_YM_PATTERN));
            AttendanceChangeApplyStatusEnum applyStatusEnum = currentMonth.equals(requestAttendanceDate) ?
                    AttendanceChangeApplyStatusEnum.PERMISSION_APPLY : AttendanceChangeApplyStatusEnum.NOT_PERMISSION_APPLY;

            // 统计考勤对应 待审核 | 已驳回 变更申请数量
            Map<Long, Map<Integer, Long>> recordByApplyCountMap = applyList.stream()
                    .filter(f -> {
                        return AttendanceChangeAuditStatusEnum.AUDIT_WAIT.getKey().equals(f.getAuditStatus()) ||
                        AttendanceChangeAuditStatusEnum.AUDIT_REJECT.getKey().equals(f.getAuditStatus());
                    })
                    .collect(Collectors.groupingBy(TAttendanceChangeApply::getAttendanceRecordId,
                            Collectors.groupingBy(TAttendanceChangeApply::getChangeType, Collectors.counting())));

            // 填充申请状态
            for (AttendanceHistoryItemResponseModel record : pageHistory.getList()) {

                Integer onDutyPunchApply = applyStatusEnum.getKey();
                Integer offDutyPunchApply = applyStatusEnum.getKey();
                Long recordId = record.getAttendanceRecordId();

                Map<Integer, Long> changeTypeMap = recordByApplyCountMap.get(recordId);
                if (CollectionUtil.isNotEmpty(changeTypeMap)) {
                    Long onDutyWorkCount = changeTypeMap.get(AttendanceChangeTypeEnum.ON_DUTY_WORK_APPLY.getKey());
                    Long offDutyWorkCount = changeTypeMap.get(AttendanceChangeTypeEnum.OFF_DUTY_WORK_APPLY.getKey());
                    if (onDutyWorkCount != null && onDutyWorkCount > CommonConstant.INTEGER_ZERO) {
                        onDutyPunchApply = AttendanceChangeApplyStatusEnum.HAVE_APPLY.getKey();
                    }
                    if (offDutyWorkCount != null && offDutyWorkCount > CommonConstant.INTEGER_ZERO) {
                        offDutyPunchApply = AttendanceChangeApplyStatusEnum.HAVE_APPLY.getKey();
                    }
                }

                record.setOnDutyPunchApply(onDutyPunchApply);
                record.setOffDutyPunchApply(offDutyPunchApply);
            }
        }
    }

    /**
     * 考勤打卡列表查询
     * @param requestModel
     * @return PageInfo<SearchAttendanceListResponseModel>
     */
    public PageInfo<SearchAttendanceListResponseModel> searchAttendanceList(SearchAttendanceListRequestModel requestModel) {

        // 查询考勤列表
        requestModel.enablePaging();
        List<SearchAttendanceListResponseModel> responseModelList = attendanceRecordMapper.selectSearchAttendanceList(requestModel);
        if (ListUtils.isEmpty(responseModelList)) {
            return new PageInfo<>(Collections.emptyList());
        }
        return new PageInfo(responseModelList);
    }

    /**
     * 考勤打卡详情
     * @param requestModel
     * @return AttendanceDetailResponseModel
     */
    public AttendanceDetailResponseModel attendanceDetail(AttendanceDetailRequestModel requestModel) {
        return attendanceRecordMapper.selectOneDetailById(requestModel.getAttendanceRecordId());
    }
}
