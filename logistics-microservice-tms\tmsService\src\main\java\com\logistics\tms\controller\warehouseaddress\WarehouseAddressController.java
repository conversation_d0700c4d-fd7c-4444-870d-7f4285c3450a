package com.logistics.tms.controller.warehouseaddress;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.warehouseaddress.WarehouseAddressBiz;
import com.logistics.tms.controller.warehouseaddress.request.*;
import com.logistics.tms.controller.warehouseaddress.response.SearchWarehouseAddressResponseModel;
import com.logistics.tms.controller.warehouseaddress.response.WarehouseAddressDetailResponseModel;
import com.logistics.tms.controller.warehouseaddress.response.WarehouseAddressListResponseModel;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 仓库地址管理
 * @author: wjf
 * @date: 2024/7/15 9:08
 */
@Api(value = "仓库地址管理")
@RequestMapping("/service/warehouseaddress")
@RestController
public class WarehouseAddressController {

    @Resource
    private WarehouseAddressBiz warehouseAddressBiz;

    /**
     * 列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "列表")
    @PostMapping(value = "/list")
    public Result<PageInfo<WarehouseAddressListResponseModel>> warehouseAddressList(@RequestBody WarehouseAddressListRequestModel requestModel) {
        return Result.success(warehouseAddressBiz.warehouseAddressList(requestModel));
    }

    /**
     * 新增/编辑/删除
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "新增/编辑/删除")
    @PostMapping(value = "/addOrModifyOrDel")
    public Result<Boolean> warehouseAddressAddOrModifyOrDel(@RequestBody AddWarehouseAddressRequestModel requestModel) {
        warehouseAddressBiz.warehouseAddressAddOrModifyOrDel(requestModel);
        return Result.success(true);
    }

    /**
     * 详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "详情")
    @PostMapping(value = "/detail")
    public Result<WarehouseAddressDetailResponseModel> warehouseAddressDetail(@RequestBody WarehouseAddressDetailRequestModel requestModel) {
        return Result.success(warehouseAddressBiz.warehouseAddressDetail(requestModel));
    }

    /**
     * 导出
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出")
    @PostMapping(value = "/export")
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<WarehouseAddressListResponseModel>> export(@RequestBody WarehouseAddressListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        PageInfo<WarehouseAddressListResponseModel> pageInfo = warehouseAddressBiz.warehouseAddressList(requestModel);
        return Result.success(pageInfo.getList());
    }

    /**
     * 启用/禁用
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "启用/禁用")
    @PostMapping(value = "/enable")
    public Result<Boolean> enable(@RequestBody WarehouseAddressEnableRequestModel requestModel) {
        warehouseAddressBiz.enable(requestModel);
        return Result.success(true);
    }

    /**
     * 根据仓库名模糊搜索地址信息
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/searchWarehouseAddress")
    public Result<PageInfo<SearchWarehouseAddressResponseModel>> searchWarehouseAddress(@RequestBody SearchWarehouseAddressRequestModel requestModel){
        return Result.success(warehouseAddressBiz.searchWarehouseAddress(requestModel));
    }

}
