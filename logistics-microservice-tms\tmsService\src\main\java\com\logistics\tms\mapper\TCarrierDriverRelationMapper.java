package com.logistics.tms.mapper;

import com.logistics.tms.controller.carrierdriverrel.response.DriveDetailResponseModel;
import com.logistics.tms.controller.carrierdriverrel.request.SearchCarrierDriverListRequestModel;
import com.logistics.tms.controller.carrierdriverrel.response.SearchCarrierDriverListResponseModel;
import com.logistics.tms.controller.staff.request.SearchStaffManagementListRequestModel;
import com.logistics.tms.controller.staff.response.SearchStaffManagementListResponseModel;
import com.logistics.tms.controller.dispatch.request.DriverSearchRequestModel;
import com.logistics.tms.controller.dispatch.response.DriverSearchResponseModel;
import com.logistics.tms.controller.dispatchorder.response.DriverByNameAndPhoneResponseModel;
import com.logistics.tms.entity.TCarrierDriverRelation;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.Nullable;

import java.util.List;

/**
 * Created by Mybatis Generator on 2022/07/08
 */
@Mapper
public interface TCarrierDriverRelationMapper extends BaseMapper<TCarrierDriverRelation> {

    int batchInsert(@Param("list") List<TCarrierDriverRelation> list);

    int batchUpdate(@Param("list") List<TCarrierDriverRelation> list);

    TCarrierDriverRelation getByCompanyCarrierIdAndDriverId(@Param("companyCarrierId") Long companyCarrierId, @Param("driverId") Long driverId, @Param("isEnable") Integer isEnable);

    List<DriverSearchResponseModel> searchDriver(@Param("params") DriverSearchRequestModel requestModel);

    List<DriverByNameAndPhoneResponseModel> getDriverByNameAndPhone(@Param("companyCarrierId") Long companyCarrierId, @Param("driverNameAndPhone") String driverNameAndPhone);

    List<SearchCarrierDriverListResponseModel> searchCarrierDriverList(@Param("requestModel") SearchCarrierDriverListRequestModel requestModel);

    DriveDetailResponseModel getCompanyDriverDetailByDriverId(@Param("staffId") Long staffId, @Param("companyCarrierId") Long companyCarrierId);

    List<TCarrierDriverRelation> getByRelationIds(@Param("relationIds") String relationIds);

    /**
     * 查询车主关联的司机
     * @param companyIds
     * @return
     */
    List<Long> queryDriverIdsByCompanyIds(@Param("companyIds") List<Long> companyIds);

    /**
     * 查询司机关联的车主
     * @param driverIds
     * @return
     */
    List<TCarrierDriverRelation> queryByDriverIds(@Param("driverIds") List<Long> driverIds);

    /**
     * 查询司机车主关联关系
     * @param requestModel
     * @param companyIds
     * @return
     */
    List<SearchStaffManagementListResponseModel> queryDriverList(@Param("params") SearchStaffManagementListRequestModel requestModel, @Nullable @Param("companyIds") List<Long> companyIds);

    List<TCarrierDriverRelation> selectByCarrierIdAndDriverIds(@Param("companyCarrierId") Long loginUserCompanyCarrierId, @Param("driverIds") String driverIds);
}