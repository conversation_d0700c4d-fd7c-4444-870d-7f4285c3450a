<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TViolationRegulationsMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TViolationRegulations">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="driver_id" jdbcType="BIGINT" property="driverId" />
    <result column="vehicle_property" jdbcType="INTEGER" property="vehicleProperty" />
    <result column="deduction" jdbcType="INTEGER" property="deduction" />
    <result column="fine" jdbcType="DECIMAL" property="fine" />
    <result column="occurance_time" jdbcType="TIMESTAMP" property="occuranceTime" />
    <result column="occurance_address" jdbcType="VARCHAR" property="occuranceAddress" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, vehicle_id, driver_id, vehicle_property, deduction, fine, occurance_time, occurance_address, 
    remark, source, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_violation_regulations
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_violation_regulations
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TViolationRegulations">
    insert into t_violation_regulations (id, vehicle_id, driver_id, 
      vehicle_property, deduction, fine, 
      occurance_time, occurance_address, remark, 
      source, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, #{driverId,jdbcType=BIGINT}, 
      #{vehicleProperty,jdbcType=INTEGER}, #{deduction,jdbcType=INTEGER}, #{fine,jdbcType=DECIMAL}, 
      #{occuranceTime,jdbcType=TIMESTAMP}, #{occuranceAddress,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{source,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TViolationRegulations" keyProperty="id" useGeneratedKeys="true">
    insert into t_violation_regulations
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="vehicleProperty != null">
        vehicle_property,
      </if>
      <if test="deduction != null">
        deduction,
      </if>
      <if test="fine != null">
        fine,
      </if>
      <if test="occuranceTime != null">
        occurance_time,
      </if>
      <if test="occuranceAddress != null">
        occurance_address,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=BIGINT},
      </if>
      <if test="vehicleProperty != null">
        #{vehicleProperty,jdbcType=INTEGER},
      </if>
      <if test="deduction != null">
        #{deduction,jdbcType=INTEGER},
      </if>
      <if test="fine != null">
        #{fine,jdbcType=DECIMAL},
      </if>
      <if test="occuranceTime != null">
        #{occuranceTime,jdbcType=TIMESTAMP},
      </if>
      <if test="occuranceAddress != null">
        #{occuranceAddress,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TViolationRegulations">
    update t_violation_regulations
    <set>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=BIGINT},
      </if>
      <if test="vehicleProperty != null">
        vehicle_property = #{vehicleProperty,jdbcType=INTEGER},
      </if>
      <if test="deduction != null">
        deduction = #{deduction,jdbcType=INTEGER},
      </if>
      <if test="fine != null">
        fine = #{fine,jdbcType=DECIMAL},
      </if>
      <if test="occuranceTime != null">
        occurance_time = #{occuranceTime,jdbcType=TIMESTAMP},
      </if>
      <if test="occuranceAddress != null">
        occurance_address = #{occuranceAddress,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TViolationRegulations">
    update t_violation_regulations
    set vehicle_id = #{vehicleId,jdbcType=BIGINT},
      driver_id = #{driverId,jdbcType=BIGINT},
      vehicle_property = #{vehicleProperty,jdbcType=INTEGER},
      deduction = #{deduction,jdbcType=INTEGER},
      fine = #{fine,jdbcType=DECIMAL},
      occurance_time = #{occuranceTime,jdbcType=TIMESTAMP},
      occurance_address = #{occuranceAddress,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      source = #{source,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>