<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TWorkGroupDistrictMapper">
    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TWorkGroupDistrict">
        <foreach collection="recordList" item="item" separator=";">
            insert into t_work_group_district
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.workGroupId != null">
                    work_group_id,
                </if>
                <if test="item.provinceId != null">
                    province_id,
                </if>
                <if test="item.provinceName != null">
                    province_name,
                </if>
                <if test="item.cityId != null">
                    city_id,
                </if>
                <if test="item.cityName != null">
                    city_name,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.workGroupId != null">
                    #{item.workGroupId,jdbcType=BIGINT},
                </if>
                <if test="item.provinceId != null">
                    #{item.provinceId,jdbcType=BIGINT},
                </if>
                <if test="item.provinceName != null">
                    #{item.provinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.cityId != null">
                    #{item.cityId,jdbcType=BIGINT},
                </if>
                <if test="item.cityName != null">
                    #{item.cityName,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <update id="batchUpdateSelective" parameterType="com.logistics.tms.entity.TWorkGroupDistrict">
        <foreach collection="recordList" item="item" separator=";">
            update t_work_group_district
            <set>
                <if test="item.workGroupId != null">
                    work_group_id = #{item.workGroupId,jdbcType=BIGINT},
                </if>
                <if test="item.provinceId != null">
                    province_id = #{item.provinceId,jdbcType=BIGINT},
                </if>
                <if test="item.provinceName != null">
                    province_name = #{item.provinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.cityId != null">
                    city_id = #{item.cityId,jdbcType=BIGINT},
                </if>
                <if test="item.cityName != null">
                    city_name = #{item.cityName,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectByWorkGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_work_group_district
        where work_group_id = #{workGroupId,jdbcType=BIGINT}
        and valid = 1
    </select>

    <select id="selectAllByWorkGroupIdInAndAddress" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_work_group_district
        where work_group_id in
        <foreach collection="workGroupIds" item="workGroupId" open="(" separator="," close=")">
            #{workGroupId}
        </foreach>
        and valid = 1
    </select>

    <select id="getWorkGroupDistrictDetail" resultType="com.logistics.tms.controller.workgroup.response.WorkGroupDistrictResponseModel">
        select
        id as workGroupDistrictId,
        province_id as provinceId,
        province_name as provinceName,
        city_id as cityId,
        city_name as cityName
        from t_work_group_district
        where work_group_id = #{workGroupId,jdbcType=BIGINT}
        and valid = 1
    </select>
</mapper>