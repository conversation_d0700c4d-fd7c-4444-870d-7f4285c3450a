package com.logistics.tms.api.feign.personalaccidentinsurance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/5/31 9:35
 */
@Data
public class ImportPersonalAccidentInsuranceListRequestModel {
    @ApiModelProperty("保单类型：1 保单，2 批单")
    private Integer insuranceType;
    @ApiModelProperty("保险公司名称")
    private String insuranceCompany;
    @ApiModelProperty("保单号")
    private String policyNumber;
    @ApiModelProperty("批单号")
    private String batchNumber;
    @ApiModelProperty("保单总额")
    private BigDecimal grossPremium;
    @ApiModelProperty("保单人数")
    private Integer policyPersonCount;
    @ApiModelProperty("保险生效时间")
    private Date startTime;
    @ApiModelProperty("保险截止时间")
    private Date endTime;
}
