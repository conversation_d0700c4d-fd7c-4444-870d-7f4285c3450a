package com.logistics.tms.biz.renewableaudit.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/8/20 9:31
 */
@Data
public class CreateDemandOrderAddressForRenewableAuditModel {

    //发货地址
    @ApiModelProperty("发货地址code（新生客户地址code）")
    private String loadAddressCode;

    @ApiModelProperty("省份ID")
    private Long loadProvinceId;

    @ApiModelProperty("省份名字")
    private String loadProvinceName;

    @ApiModelProperty("城市ID")
    private Long loadCityId;

    @ApiModelProperty("城市名字")
    private String loadCityName;

    @ApiModelProperty("县区id")
    private Long loadAreaId;

    @ApiModelProperty("县区名字")
    private String loadAreaName;

    @ApiModelProperty("详细地址")
    private String loadDetailAddress;

    @ApiModelProperty("发货仓库")
    private String loadWarehouse;

    @ApiModelProperty("提货经度")
    private String loadLongitude;

    @ApiModelProperty("提货纬度")
    private String loadLatitude;

    @ApiModelProperty("发货人姓名")
    private String consignorName;

    @ApiModelProperty("发货人手机号")
    private String consignorMobile;



    //收货地址
    @ApiModelProperty("收货地址code（云仓仓库地址code）")
    private String unloadAddressCode;

    @ApiModelProperty("省份ID")
    private Long unloadProvinceId;

    @ApiModelProperty("省份名字")
    private String unloadProvinceName;

    @ApiModelProperty("城市ID")
    private Long unloadCityId;

    @ApiModelProperty("城市名字")
    private String unloadCityName;

    @ApiModelProperty("县区id")
    private Long unloadAreaId;

    @ApiModelProperty("县区名字")
    private String unloadAreaName;

    @ApiModelProperty("详细地址")
    private String unloadDetailAddress;

    @ApiModelProperty("收货仓库")
    private String unloadWarehouse;

    @ApiModelProperty("卸货经度")
    private String unloadLongitude;

    @ApiModelProperty("卸货纬度")
    private String unloadLatitude;

    @ApiModelProperty("收货人姓名")
    private String receiverName;

    @ApiModelProperty("收货人手机号")
    private String receiverMobile;
}
