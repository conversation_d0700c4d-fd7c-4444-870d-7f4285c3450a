package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @author: wjf
 * @date: 2023/1/9 17:14
 */
@Data
public class RollbackDemandOrderRequestDto {
    @ApiModelProperty(value = "需求单id",required = true)
    @NotBlank(message = "请选中数据")
    private String demandId;

    @ApiModelProperty(value = "(3.15.0)回退原因类型一级：2 客户原因，3 不可抗力，4 物流原因，5 平台问题，6 其他原因",required = true)
    @NotBlank(message = "请选择回退原因一级")
    private String rollbackCauseType;
    @ApiModelProperty(value = "(3.15.0)回退原因类型二级：201 重复上报，202 更换签收单抬头，203 数据报错，重新下单，204 地址原因，205 等待问题，206 托盘占用，207 流向核对，208 不配合装车，209 客户临时有事，210 现场电话联系不上；301 恶劣天气，302 政府管制，303 修路，304 洪涝；401 回收不及时，402 车辆已满载；501 重复下单，502 操作不规范；601 物流提货现场并单",required = true)
    @NotBlank(message = "请选择回退原因二级")
    private String rollbackCauseTypeTwo;

    @ApiModelProperty(value = "回退备注",required = true)
    @Size(min = 1,max = 200,message = "备注不能为空，且不超过200字")
    private String rollbackRemark;
}
