package com.logistics.management.webapi.client.vehicleassetmanagement;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.vehicleassetmanagement.hystrix.VehicleAssetManagementClientHystrix;
import com.logistics.management.webapi.client.vehicleassetmanagement.request.FuzzyQueryVehicleInfoRequestModel;
import com.logistics.management.webapi.client.vehicleassetmanagement.request.SearchTrailerVehicleRequestModel;
import com.logistics.management.webapi.client.vehicleassetmanagement.response.FuzzyQueryVehicleInfoResponseModel;
import com.logistics.management.webapi.client.vehicleassetmanagement.response.SearchTrailerVehicleResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * <AUTHOR>
 * @date:2019/6/3 9:39
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        fallback = VehicleAssetManagementClientHystrix.class)
public interface VehicleAssetManagementClient {

    @ApiOperation(value = "分页搜索挂车")
    @PostMapping(value = "/service/vehicleAssetManagement/searchTrailerVehicle")
    Result<PageInfo<SearchTrailerVehicleResponseModel>> searchTrailerVehicle(@RequestBody SearchTrailerVehicleRequestModel requestModel);

    @ApiOperation("模糊查询车辆信息")
    @PostMapping({"/service/VehicleAssetManagement/VehicleInfo"})
    Result<List<FuzzyQueryVehicleInfoResponseModel>> fuzzyQueryVehicleInfo(@RequestBody FuzzyQueryVehicleInfoRequestModel requestModel);
}
