package com.logistics.appapi.controller.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class RenewableOrderGoodDto {

	@ApiModelProperty("货物id")
	private String goodsId = "";

	@ApiModelProperty("sku编码")
	private String skuCode = "";

	@ApiModelProperty("货物名")
	private String goodsName = "";

	@ApiModelProperty("确认货物数量")
	private String goodsAmount = "";

	@ApiModelProperty("货物单位（如：件或公斤）")
	private String goodsUnit = "";

	@ApiModelProperty("建议单价")
	private String suggestGoodsPrice = "";

	@ApiModelProperty("收货单价")
	private String goodsPrice = "";
}
