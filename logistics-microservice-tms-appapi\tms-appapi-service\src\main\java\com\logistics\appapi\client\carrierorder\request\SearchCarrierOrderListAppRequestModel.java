package com.logistics.appapi.client.carrierorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class SearchCarrierOrderListAppRequestModel extends AbstractPageForm<SearchCarrierOrderListAppRequestModel> {

    @ApiModelProperty("状态：20000 待提货 40000 待卸货 50000 待签收 0 已取消")
    private Integer status;

    @ApiModelProperty("搜索开始时间")
    private String startTime;

    @ApiModelProperty("搜索结束时间")
    private String endTime;

    @ApiModelProperty("是否新生单子：1 是（传1只查新生的单子）")
    private Integer ifYeloLife;

    @ApiModelProperty("司机账号查数据")
    private List<CarrierDriverRelationForAppletModel> driverModel;

    private List<Long> excludeCarrierOrderIdList;
}
