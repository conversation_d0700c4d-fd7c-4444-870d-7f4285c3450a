package com.logistics.tms.config.rdelayqueue.core;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 延迟队列
 *
 * <AUTHOR>
 * @date 2024/04/25
 */
@Slf4j
public class RDelayQueue {

    private RBlockingQueue<String> rBlockingQueue;

    private RDelayedQueue<String> rDelayedQueue;

    public RDelayQueue(RedissonClient redissonClient, String queueName) {
        Objects.requireNonNull(redissonClient,"Spring 上下文中 RedissonClient 不存在！");
        Objects.requireNonNull(queueName,"延迟队列 queueName 不存在！");
        rBlockingQueue = redissonClient.getBlockingQueue(queueName);
        rDelayedQueue = redissonClient.getDelayedQueue(rBlockingQueue);
        log.info("延迟队列{},创建成功", queueName);
    }

    /**
     * 获取包装对象，内部使用
     * @return {@link DelayMsg}
     * @throws InterruptedException
     */
    protected DelayMsg take() throws InterruptedException {
        String take = rBlockingQueue.take();
        log.info("弹出到期消息:{}", take);
        return JSONUtil.toBean(take,DelayMsg.class);
    }

    /**
     * 新增
     * @param delayMsg 包装对象
     * @param time 超时时间
     * @param timeUnit 时间单位
     */
    public void add(DelayMsg delayMsg, long time, TimeUnit timeUnit) {
        rDelayedQueue.offer(JSONUtil.toJsonStr(delayMsg), time, timeUnit);
    }

    /**
     * 新增
     * @param delayMsgType 消息类型
     * @param object 消息内容
     * @param time 超时时间
     * @param timeUnit 时间单位
     */
    public void add(String delayMsgType,Object object ,long time, TimeUnit timeUnit) {
        add(new DelayMsg(delayMsgType,JSONUtil.toJsonStr(object)), time, timeUnit);
    }

    /**
     * 删除
     * @param delayMsg 包装对象
     * @return boolean
     */
    public boolean remove(DelayMsg delayMsg) {
        return rDelayedQueue.remove(JSONUtil.toJsonStr(delayMsg));
    }

    /**
     * @param delayMsgType 消息类型
     * @param object 消息内容
     * @return boolean
     */
    public boolean remove(String delayMsgType,Object object) {
        return remove(new DelayMsg(delayMsgType,JSONUtil.toJsonStr(object)));
    }

}
