package com.logistics.tms.mapper;

import com.logistics.tms.entity.TCarrierFreight;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Mybatis Generator on 2022/09/02
 */
@Mapper
public interface TCarrierFreightMapper extends BaseMapper<TCarrierFreight> {

    /**
     * 根据车主id查询车主运价
     *
     * @param carrierCompanyIds 车主表id
     * @return
     */
    List<TCarrierFreight> selectFreightByCarrierCompanyIds(@Param("ids") List<Long> carrierCompanyIds);

    /**
     * 查询车主运价
     *
     * @param carrierFreight
     * @return
     */
    List<TCarrierFreight> selectFreightList(@Param("params") TCarrierFreight carrierFreight);

    /**
     * 根据车主id查询车主运价
     *
     * @param carrierCompanyId
     * @return
     */
    TCarrierFreight getFreightByCarrierCompanyId(@Param("carrierCompanyId") Long carrierCompanyId);

    TCarrierFreight selectCarrierFreightBySchemeId(Long schemeId);
}