package com.logistics.management.webapi.controller.demandorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DemandOrderSearchForLeYiRequestDto extends AbstractPageForm<DemandOrderSearchForLeYiRequestDto> {
    @ApiModelProperty("(3.15.0)需求单状态：空 全部 500待发布 1000待调度 2000部分调度 3000调度完成 4000待签收 5000已签收 1取消 2放空 3回退")
    private List<String> demandStatusList;
    @ApiModelProperty("委托方,逗号分隔")
    private String companyEntrustIds;
    @ApiModelProperty("委托单号")
    private String demandOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("下单时间开始")
    private String publishTimeStart;
    @ApiModelProperty("下单时间结束")
    private String publishTimeEnd;
    @ApiModelProperty("发货仓库")
    private String loadWarehouse;
    @ApiModelProperty("发货地址")
    private String loadDetailAddress;
    @ApiModelProperty("发货人")
    private String consignorMobile;
    @ApiModelProperty("收货仓库")
    private String unloadWarehouse;
    @ApiModelProperty("收货地址")
    private String unloadDetailAddress;
    @ApiModelProperty("收货人")
    private String receiverName;
    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨 1.3.3")
    private String entrustType;
    @ApiModelProperty("显示已取消勾选 是否取消 1 是 0 否")
    private String showIfCancelStatus;
    @ApiModelProperty("是否加急：0 否，1 是")
    private String ifUrgent;
    @ApiModelProperty("排序字段")
    private String sort;
    @ApiModelProperty("顺序 asc 升序 desc 倒序")
    private String order;
    @ApiModelProperty("区域（提货大区）")
    private String loadRegionName;
    @ApiModelProperty("是否逾期：0 否，1 是")
    private String ifOverdue;

    @ApiModelProperty("拼单助手需求单号")
    private List<String> demandOrderCodeList;
    @ApiModelProperty("拼单助手客户单号")
    private List<String> customerOrderCodeList;

    @ApiModelProperty(value = "选择性导出，传入选择的ids,多个逗号分隔")
    private String demandIds;

    @ApiModelProperty("车主名")
    private String companyCarrierName;

    @ApiModelProperty(value = "货物：对品名及规格模糊查询")
    private String goodsName;

    @ApiModelProperty(value = "部门Code")
    private String orgCode;

    @ApiModelProperty("1.3.2新增；时效要求：1 日常回收，2 加急或节假日回收")
    private String recycleTaskType;

    @ApiModelProperty("1.3.7新增；项目标签：1 石化板块，2 轮胎板块，3 涂料板块，4 其他")
    private String projectLabel;

    @ApiModelProperty("是否是额外补的需求单0：否1：是 v2.6.8")
    private String ifExtDemandOrder;

}
