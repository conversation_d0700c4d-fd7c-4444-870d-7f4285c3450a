package com.logistics.tms.api.feign.gpsfee.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/10/8 10:29
 */
@Data
public class SearchGpsFeeListRequestModel extends AbstractPageForm<SearchGpsFeeListRequestModel> {
    @ApiModelProperty("结算状态：空：全部，0 待结算，1 部分结算，2 结算完成，-1 已终止")
    private Integer status;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("司机")
    private String driverName;
    @ApiModelProperty("服务商")
    private String gpsServiceProvider;
    @ApiModelProperty("服务商：多选导出使用")
    private String gpsFeeIds;
}
