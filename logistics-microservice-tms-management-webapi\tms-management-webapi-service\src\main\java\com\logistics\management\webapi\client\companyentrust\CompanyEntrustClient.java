package com.logistics.management.webapi.client.companyentrust;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.companyentrust.hystrix.CompanyEntrustClientHystrix;
import com.logistics.management.webapi.client.companyentrust.request.AddOrModifyCompanyEntrustRequestModel;
import com.logistics.management.webapi.client.companyentrust.request.CompanyEntrustIdRequestModel;
import com.logistics.management.webapi.client.companyentrust.request.SearchCompanyEntrustByNameRequestModel;
import com.logistics.management.webapi.client.companyentrust.request.SearchCompanyEntrustRequestModel;
import com.logistics.management.webapi.client.companyentrust.response.CompanyEntrustDetailResponseModel;
import com.logistics.management.webapi.client.companyentrust.response.SearchCompanyEntrustByNameResponseModel;
import com.logistics.management.webapi.client.companyentrust.response.SearchCompanyEntrustResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/9/18 19:43
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,fallback = CompanyEntrustClientHystrix.class)
public interface CompanyEntrustClient {

    @ApiOperation(value = "获取委托方公司列表")
    @PostMapping(value = "/service/companyEntrust/searchList")
    Result<PageInfo<SearchCompanyEntrustResponseModel>> searchList(@RequestBody SearchCompanyEntrustRequestModel requestModel);

    @ApiOperation(value = "查看详情")
    @PostMapping(value = "/service/companyEntrust/getDetail")
    Result<CompanyEntrustDetailResponseModel> getDetail(@RequestBody CompanyEntrustIdRequestModel requestModel);

    @ApiOperation(value = "添加/修改公司")
    @PostMapping(value = "/service/companyEntrust/saveCompany")
    Result saveCompany(@RequestBody AddOrModifyCompanyEntrustRequestModel requestModel);

    @ApiOperation(value = "根据公司名称模糊查询委托方公司")
    @PostMapping(value = "/service/companyEntrust/searchCompanyEntrustByName")
    Result<List<SearchCompanyEntrustByNameResponseModel>> searchCompanyEntrustByName(@RequestBody SearchCompanyEntrustByNameRequestModel requestModel);
}
