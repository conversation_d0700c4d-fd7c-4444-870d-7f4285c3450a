package com.logistics.tms.biz.dateremind;

import com.github.pagehelper.PageInfo;
import com.yelo.tools.context.BaseContextHandler;
import com.logistics.tms.api.feign.dateremind.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TDateRemind;
import com.logistics.tms.mapper.TDateRemindMapper;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/5/31 10:05
 */
@Service("dateRemindBiz")
public class DateRemindBiz {

    @Autowired
    private TDateRemindMapper tDateRemindMapper;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 列表
     * @param requestModel
     * @return
     */
    public PageInfo<DateRemindListResponseModel> searchDateRemindList(DateRemindListRequestModel requestModel){
        requestModel.enablePaging();
        List<DateRemindListResponseModel> dateRemindList = tDateRemindMapper.searchDateRemindList(requestModel);
        return new PageInfo(dateRemindList);
    }

    /**
     * 详情
     * @param requestModel
     * @return
     */
    public DateRemindDetailResponseModel getDateRemindDetail(DateRemindDetailRequestModel requestModel){
        TDateRemind tQDateRemind = tDateRemindMapper.selectByPrimaryKey(requestModel.getDateRemindId());
        if(tQDateRemind == null){
            throw new BizException(CarrierDataExceptionEnum.DATE_REMIND_NOT_EXIST);
        }
        DateRemindDetailResponseModel retModel = new DateRemindDetailResponseModel();
        MapperUtils.mapper(tQDateRemind,retModel);
        retModel.setDateRemindId(tQDateRemind.getId());
        return retModel;
    }

    /**
     * 新增/修改
     * @param requestModel
     */
    @Transactional
    public void saveOrModifyDateRemind(SaveOrModifyDateRemindRequestModel requestModel){
        TDateRemind tQDateRemind = new TDateRemind();
        tQDateRemind.setDateName(requestModel.getDateName());
        tQDateRemind.setRemindDays(requestModel.getRemindDays());
        tQDateRemind.setIfRemind(requestModel.getIfRemind());
        tQDateRemind.setRemark(requestModel.getRemark());
        TDateRemind dbOneDateRemind = tDateRemindMapper.getDateRemindByName(requestModel.getDateName());
        if(requestModel.getDateRemindId()!=null && requestModel.getDateRemindId()> CommonConstant.LONG_ZERO){ //修改
            TDateRemind dbTwoDateRemind = tDateRemindMapper.selectByPrimaryKey(requestModel.getDateRemindId());
            if(dbTwoDateRemind == null){
                throw new BizException(CarrierDataExceptionEnum.DATE_REMIND_NOT_EXIST);
            }
            if(dbOneDateRemind!=null && !dbOneDateRemind.getId().equals(requestModel.getDateRemindId())){
               throw new BizException(CarrierDataExceptionEnum.DATE_REMIND_HAS_EXIST);
            }
            tQDateRemind.setId(requestModel.getDateRemindId());
            commonBiz.setBaseEntityModify(tQDateRemind, BaseContextHandler.getUserName());
            tDateRemindMapper.updateByPrimaryKeySelectiveExt(tQDateRemind);
        }else{ //新增
            if(dbOneDateRemind!=null){
               throw new BizException(CarrierDataExceptionEnum.DATE_REMIND_HAS_EXIST);
            }
            tQDateRemind.setAddUserId(BaseContextHandler.getUserId());
            tQDateRemind.setAddUserName(BaseContextHandler.getUserName());
            commonBiz.setBaseEntityAdd(tQDateRemind,BaseContextHandler.getUserName());
            tDateRemindMapper.insertSelective(tQDateRemind);
        }
    }

    /**
     * 统一提醒
     * @param requestModel
     */
    @Transactional
    public void unifiedDateRemind(UnifiedDateRemindRequestModel requestModel){
        if(ListUtils.isEmpty(requestModel.getDateRemindIds())){
            return;
        }
        List<TDateRemind> dateRemindList = tDateRemindMapper.getDateRemindByIds(StringUtils.listToString(requestModel.getDateRemindIds(), ','));
        List<TDateRemind> upDateRemindList = new ArrayList<>();
        if(ListUtils.isNotEmpty(dateRemindList)){
            TDateRemind upDateRemind;
            for (TDateRemind tempDateRemind: dateRemindList) {
                upDateRemind =new TDateRemind();
                upDateRemind.setId(tempDateRemind.getId());
                upDateRemind.setIfRemind(requestModel.getIfRemind());
                upDateRemind.setRemark(requestModel.getRemark());
                if(CommonConstant.INTEGER_ONE.equals(requestModel.getIfRemind())){
                    upDateRemind.setRemindDays(requestModel.getRemindDays());
                }else{
                    upDateRemind.setRemindDays(null);
                }
                commonBiz.setBaseEntityModify(upDateRemind,BaseContextHandler.getUserName());
                upDateRemindList.add(upDateRemind);
            }
            if (ListUtils.isNotEmpty(upDateRemindList)) {
                tDateRemindMapper.batchModifyDateRemind(upDateRemindList);
            }
        }
    }
}
