<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandOrderCarrierMapper">
    <select id="getDemandCarrierByDemandOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_demand_order_carrier
        where valid = 1
          and demand_order_id in (${demandOrderIds})
          and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        order by id
    </select>

    <insert id="batchInsertSelective">
        <foreach collection="list" item="item" separator=";">
            insert into t_demand_order_carrier
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.demandOrderId != null">
                    demand_order_id,
                </if>
                <if test="item.companyCarrierId != null">
                    company_carrier_id,
                </if>
                <if test="item.carrierContactId != null">
                    carrier_contact_id,
                </if>
                <if test="item.carrierPriceType != null">
                    carrier_price_type,
                </if>
                <if test="item.carrierPrice != null">
                    carrier_price,
                </if>
                <if test="item.cancelReason != null">
                    cancel_reason,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.demandOrderId != null">
                    #{item.demandOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.companyCarrierId != null">
                    #{item.companyCarrierId,jdbcType=BIGINT},
                </if>
                <if test="item.carrierContactId != null">
                    #{item.carrierContactId,jdbcType=BIGINT},
                </if>
                <if test="item.carrierPriceType != null">
                    #{item.carrierPriceType,jdbcType=INTEGER},
                </if>
                <if test="item.carrierPrice != null">
                    #{item.carrierPrice,jdbcType=DECIMAL},
                </if>
                <if test="item.cancelReason != null">
                    #{item.cancelReason,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <select id="getNewestDemandCarrierByDemandOrderIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from t_demand_order_carrier
        where valid = 1
          and demand_order_id in (${demandOrderIds})
        <if test="companyCarrierIds != null and companyCarrierIds != ''">
            and company_carrier_id in (${companyCarrierIds})
        </if>
        order by created_time desc
    </select>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            update t_demand_order_carrier
            <set>
                <if test="item.demandOrderId != null">
                    demand_order_id = #{item.demandOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.companyCarrierId != null">
                    company_carrier_id = #{item.companyCarrierId,jdbcType=BIGINT},
                </if>
                <if test="item.carrierContactId != null">
                    carrier_contact_id = #{item.carrierContactId,jdbcType=BIGINT},
                </if>
                <if test="item.carrierPriceType != null">
                    carrier_price_type = #{item.carrierPriceType,jdbcType=INTEGER},
                </if>
                <if test="item.carrierPrice != null">
                    carrier_price = #{item.carrierPrice,jdbcType=DECIMAL},
                </if>
                <if test="item.cancelReason != null">
                    cancel_reason = #{item.cancelReason,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>