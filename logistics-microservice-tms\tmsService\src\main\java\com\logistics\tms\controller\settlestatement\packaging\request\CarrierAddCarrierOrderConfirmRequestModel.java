package com.logistics.tms.controller.settlestatement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/18
 */
@Data
public class CarrierAddCarrierOrderConfirmRequestModel {

	@ApiModelProperty(value = "对账单id")
	private Long settleStatementId;

	@ApiModelProperty("货主")
	private String companyEntrustName;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("需求单号")
	private String demandOrderCode;

	@ApiModelProperty("车牌号")
	private String vehicleNumber;

	@ApiModelProperty("司机名,司机姓名+手机号")
	private String driverName;

	@ApiModelProperty("签收时间起")
	private String signTimeStart;

	@ApiModelProperty("签收时间止")
	private String signTimeEnd;

	@ApiModelProperty("拼单助手运单号")
	private List<String> carrierOrderCodeList;

	@ApiModelProperty("拼单助手客户单号")
	private List<String> demandOrderCodeList;

	@ApiModelProperty("仅操作为确定时 运单ids  字符串拼接,','拼接")
	private String carrierOrderIds;

	private Long companyCarrierId;

	@ApiModelProperty("请求来源:1 后台，2 前台")
	private Integer source;//1 后台，2 前台
}
