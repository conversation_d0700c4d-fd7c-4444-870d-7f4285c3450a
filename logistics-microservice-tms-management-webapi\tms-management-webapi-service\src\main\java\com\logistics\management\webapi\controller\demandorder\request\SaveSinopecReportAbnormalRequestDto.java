package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;

/**
 * 石化需求单异常上报 请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/5/30
 */
@Data
public class SaveSinopecReportAbnormalRequestDto {

	@ApiModelProperty("需求单id")
	@NotBlank(message = "需求单id不能为空")
	private String demandOrderId;

	@ApiModelProperty("发货省份id")
	@NotBlank(message = "发货省份id不能为空")
	private String loadProvinceId;
	@ApiModelProperty("发货省份名字")
	@NotBlank(message = "发货省份名字不能为空")
	private String loadProvinceName;
	@ApiModelProperty("发货城市id")
	@NotBlank(message = "发货城市id不能为空")
	private String loadCityId;
	@ApiModelProperty("发货城市名字")
	@NotBlank(message = "发货城市名字不能为空")
	private String loadCityName;
	@ApiModelProperty("发货县区id")
	@NotBlank(message = "发货县区id不能为空")
	private String loadAreaId;
	@ApiModelProperty("发货县区名字")
	@NotBlank(message = "发货县区名字不能为空")
	private String loadAreaName;
	@ApiModelProperty("发货详细地址")
	private String loadDetailAddress;

	@ApiModelProperty("收货省份id")
	@NotBlank(message = "收货省份id不能为空")
	private String unloadProvinceId;
	@ApiModelProperty("收货省份名字")
	@NotBlank(message = "收货省份名字不能为空")
	private String unloadProvinceName;
	@ApiModelProperty("收货城市id")
	@NotBlank(message = "收货城市id不能为空")
	private String unloadCityId;
	@ApiModelProperty("收货城市名字")
	@NotBlank(message = "收货城市名字不能为空")
	private String unloadCityName;
	@ApiModelProperty("收货县区id")
	@NotBlank(message = "收货县区id不能为空")
	private String unloadAreaId;
	@ApiModelProperty("收货县区名字")
	@NotBlank(message = "收货县区名字不能为空")
	private String unloadAreaName;
	@ApiModelProperty("收货详细地址")
	@NotBlank(message = "收货详细地址不能为空")
	private String unloadDetailAddress;

	@ApiModelProperty(value = "调度人员姓名,2-20个字符", required = true)
	@Pattern(regexp = "^[\\u4e00-\\u9fa5]{2,20}$", message = "调度人员姓名不许为空且必须是汉字并且长度控制在2~20字内")
	private String dispatcherName;
	@ApiModelProperty(value = "调度人员电话,1-50个字符", required = true)
	@Size(min = 1, max = 50, message = "调度人员电话不许为空且长度控制在1~50个字符内")
	private String dispatcherPhone;
	@ApiModelProperty(value = "单价 范围1-1000,最多两位小数", required = true)
	@DecimalMin(value = "1", message = "单价不能小于1")
	@DecimalMax(value = "1000", message = "单价不能大于1000")
	@Digits(integer = 4, fraction = 2, message = "单价最多允许两位小数")
	private String contractPrice;//固定是单价

	@ApiModelProperty(value = "异常类型：1 已报价，2 已取消", required = true)
	@NotBlank(message = "请选择异常类型")
	private String objectionType;

	@ApiModelProperty(value = "备注 最多100个字符")
	@Length(max = 100, message = "备注最多100个字符")
	private String remark;
}
