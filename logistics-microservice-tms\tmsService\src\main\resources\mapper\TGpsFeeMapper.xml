<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TGpsFeeMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TGpsFee">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="vehicle_property" jdbcType="INTEGER" property="vehicleProperty" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="terminal_type" jdbcType="VARCHAR" property="terminalType" />
    <result column="gps_service_provider" jdbcType="VARCHAR" property="gpsServiceProvider" />
    <result column="sim_number" jdbcType="VARCHAR" property="simNumber" />
    <result column="install_time" jdbcType="TIMESTAMP" property="installTime" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="finish_date" jdbcType="TIMESTAMP" property="finishDate" />
    <result column="service_fee" jdbcType="DECIMAL" property="serviceFee" />
    <result column="cooperation_period" jdbcType="INTEGER" property="cooperationPeriod" />
    <result column="cooperation_status" jdbcType="INTEGER" property="cooperationStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, status, vehicle_id, vehicle_no, vehicle_property, staff_id, name, mobile, terminal_type, 
    gps_service_provider, sim_number, install_time, start_date, end_date, finish_date, 
    service_fee, cooperation_period, cooperation_status, remark, created_by, created_time, 
    last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_gps_fee
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_gps_fee
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TGpsFee">
    insert into t_gps_fee (id, status, vehicle_id, 
      vehicle_no, vehicle_property, staff_id, 
      name, mobile, terminal_type, 
      gps_service_provider, sim_number, install_time, 
      start_date, end_date, finish_date, 
      service_fee, cooperation_period, cooperation_status, 
      remark, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{vehicleId,jdbcType=BIGINT}, 
      #{vehicleNo,jdbcType=VARCHAR}, #{vehicleProperty,jdbcType=INTEGER}, #{staffId,jdbcType=BIGINT}, 
      #{name,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{terminalType,jdbcType=VARCHAR}, 
      #{gpsServiceProvider,jdbcType=VARCHAR}, #{simNumber,jdbcType=VARCHAR}, #{installTime,jdbcType=TIMESTAMP}, 
      #{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP}, #{finishDate,jdbcType=TIMESTAMP}, 
      #{serviceFee,jdbcType=DECIMAL}, #{cooperationPeriod,jdbcType=INTEGER}, #{cooperationStatus,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TGpsFee" keyProperty="id" useGeneratedKeys="true">
    insert into t_gps_fee
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleNo != null">
        vehicle_no,
      </if>
      <if test="vehicleProperty != null">
        vehicle_property,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="terminalType != null">
        terminal_type,
      </if>
      <if test="gpsServiceProvider != null">
        gps_service_provider,
      </if>
      <if test="simNumber != null">
        sim_number,
      </if>
      <if test="installTime != null">
        install_time,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="finishDate != null">
        finish_date,
      </if>
      <if test="serviceFee != null">
        service_fee,
      </if>
      <if test="cooperationPeriod != null">
        cooperation_period,
      </if>
      <if test="cooperationStatus != null">
        cooperation_status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleProperty != null">
        #{vehicleProperty,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="terminalType != null">
        #{terminalType,jdbcType=VARCHAR},
      </if>
      <if test="gpsServiceProvider != null">
        #{gpsServiceProvider,jdbcType=VARCHAR},
      </if>
      <if test="simNumber != null">
        #{simNumber,jdbcType=VARCHAR},
      </if>
      <if test="installTime != null">
        #{installTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="finishDate != null">
        #{finishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="serviceFee != null">
        #{serviceFee,jdbcType=DECIMAL},
      </if>
      <if test="cooperationPeriod != null">
        #{cooperationPeriod,jdbcType=INTEGER},
      </if>
      <if test="cooperationStatus != null">
        #{cooperationStatus,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TGpsFee">
    update t_gps_fee
    <set>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleProperty != null">
        vehicle_property = #{vehicleProperty,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="terminalType != null">
        terminal_type = #{terminalType,jdbcType=VARCHAR},
      </if>
      <if test="gpsServiceProvider != null">
        gps_service_provider = #{gpsServiceProvider,jdbcType=VARCHAR},
      </if>
      <if test="simNumber != null">
        sim_number = #{simNumber,jdbcType=VARCHAR},
      </if>
      <if test="installTime != null">
        install_time = #{installTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="finishDate != null">
        finish_date = #{finishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="serviceFee != null">
        service_fee = #{serviceFee,jdbcType=DECIMAL},
      </if>
      <if test="cooperationPeriod != null">
        cooperation_period = #{cooperationPeriod,jdbcType=INTEGER},
      </if>
      <if test="cooperationStatus != null">
        cooperation_status = #{cooperationStatus,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TGpsFee">
    update t_gps_fee
    set status = #{status,jdbcType=INTEGER},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      vehicle_property = #{vehicleProperty,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      terminal_type = #{terminalType,jdbcType=VARCHAR},
      gps_service_provider = #{gpsServiceProvider,jdbcType=VARCHAR},
      sim_number = #{simNumber,jdbcType=VARCHAR},
      install_time = #{installTime,jdbcType=TIMESTAMP},
      start_date = #{startDate,jdbcType=TIMESTAMP},
      end_date = #{endDate,jdbcType=TIMESTAMP},
      finish_date = #{finishDate,jdbcType=TIMESTAMP},
      service_fee = #{serviceFee,jdbcType=DECIMAL},
      cooperation_period = #{cooperationPeriod,jdbcType=INTEGER},
      cooperation_status = #{cooperationStatus,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>