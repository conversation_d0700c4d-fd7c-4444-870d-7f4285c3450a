package com.logistics.management.webapi.api.feign.insurancecompany.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: sj
 * @Date: 2019/5/29 15:14
 */
@Data
public class InsuranceCompanyDetailResponseDto implements Serializable{
    @ApiModelProperty("保险公司ID")
    private String insuranceCompanyId;
    @ApiModelProperty("保险公司名称")
    private String companyName = "";
    @ApiModelProperty("备注")
    private String remark;
}
