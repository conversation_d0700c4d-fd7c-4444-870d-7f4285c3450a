<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TReserveApplyMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TReserveApply">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="reserve_apply_code" jdbcType="VARCHAR" property="reserveApplyCode" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
    <result column="staff_mobile" jdbcType="VARCHAR" property="staffMobile" />
    <result column="staff_property" jdbcType="INTEGER" property="staffProperty" />
    <result column="receive_bank_account" jdbcType="VARCHAR" property="receiveBankAccount" />
    <result column="receive_bank_account_name" jdbcType="VARCHAR" property="receiveBankAccountName" />
    <result column="receive_bra_bank_name" jdbcType="VARCHAR" property="receiveBraBankName" />
    <result column="apply_amount" jdbcType="DECIMAL" property="applyAmount" />
    <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime" />
    <result column="apply_remark" jdbcType="VARCHAR" property="applyRemark" />
    <result column="approved_amount" jdbcType="DECIMAL" property="approvedAmount" />
    <result column="auditor_name_one" jdbcType="VARCHAR" property="auditorNameOne" />
    <result column="audit_time_one" jdbcType="TIMESTAMP" property="auditTimeOne" />
    <result column="audit_remark_one" jdbcType="VARCHAR" property="auditRemarkOne" />
    <result column="auditor_name_two" jdbcType="VARCHAR" property="auditorNameTwo" />
    <result column="audit_time_two" jdbcType="TIMESTAMP" property="auditTimeTwo" />
    <result column="audit_remark_two" jdbcType="VARCHAR" property="auditRemarkTwo" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="running_code" jdbcType="VARCHAR" property="runningCode" />
    <result column="payment_remark" jdbcType="VARCHAR" property="paymentRemark" />
    <result column="reject_remark" jdbcType="VARCHAR" property="rejectRemark" />
    <result column="cancel_remark" jdbcType="VARCHAR" property="cancelRemark" />
    <result column="balance_amount" jdbcType="DECIMAL" property="balanceAmount" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, reserve_apply_code, status, type, staff_id, staff_name, staff_mobile, staff_property, 
    receive_bank_account, receive_bank_account_name, receive_bra_bank_name, apply_amount, 
    apply_time, apply_remark, approved_amount, auditor_name_one, audit_time_one, audit_remark_one, 
    auditor_name_two, audit_time_two, audit_remark_two, pay_time, running_code, payment_remark, 
    reject_remark, cancel_remark, balance_amount, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_reserve_apply
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_reserve_apply
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TReserveApply">
    insert into t_reserve_apply (id, reserve_apply_code, status, 
      type, staff_id, staff_name, 
      staff_mobile, staff_property, receive_bank_account, 
      receive_bank_account_name, receive_bra_bank_name, 
      apply_amount, apply_time, apply_remark, 
      approved_amount, auditor_name_one, audit_time_one, 
      audit_remark_one, auditor_name_two, audit_time_two, 
      audit_remark_two, pay_time, running_code, 
      payment_remark, reject_remark, cancel_remark, 
      balance_amount, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{reserveApplyCode,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{type,jdbcType=INTEGER}, #{staffId,jdbcType=BIGINT}, #{staffName,jdbcType=VARCHAR}, 
      #{staffMobile,jdbcType=VARCHAR}, #{staffProperty,jdbcType=INTEGER}, #{receiveBankAccount,jdbcType=VARCHAR}, 
      #{receiveBankAccountName,jdbcType=VARCHAR}, #{receiveBraBankName,jdbcType=VARCHAR}, 
      #{applyAmount,jdbcType=DECIMAL}, #{applyTime,jdbcType=TIMESTAMP}, #{applyRemark,jdbcType=VARCHAR}, 
      #{approvedAmount,jdbcType=DECIMAL}, #{auditorNameOne,jdbcType=VARCHAR}, #{auditTimeOne,jdbcType=TIMESTAMP}, 
      #{auditRemarkOne,jdbcType=VARCHAR}, #{auditorNameTwo,jdbcType=VARCHAR}, #{auditTimeTwo,jdbcType=TIMESTAMP}, 
      #{auditRemarkTwo,jdbcType=VARCHAR}, #{payTime,jdbcType=TIMESTAMP}, #{runningCode,jdbcType=VARCHAR}, 
      #{paymentRemark,jdbcType=VARCHAR}, #{rejectRemark,jdbcType=VARCHAR}, #{cancelRemark,jdbcType=VARCHAR}, 
      #{balanceAmount,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TReserveApply" useGeneratedKeys="true" keyProperty="id">
    insert into t_reserve_apply
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reserveApplyCode != null">
        reserve_apply_code,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="staffName != null">
        staff_name,
      </if>
      <if test="staffMobile != null">
        staff_mobile,
      </if>
      <if test="staffProperty != null">
        staff_property,
      </if>
      <if test="receiveBankAccount != null">
        receive_bank_account,
      </if>
      <if test="receiveBankAccountName != null">
        receive_bank_account_name,
      </if>
      <if test="receiveBraBankName != null">
        receive_bra_bank_name,
      </if>
      <if test="applyAmount != null">
        apply_amount,
      </if>
      <if test="applyTime != null">
        apply_time,
      </if>
      <if test="applyRemark != null">
        apply_remark,
      </if>
      <if test="approvedAmount != null">
        approved_amount,
      </if>
      <if test="auditorNameOne != null">
        auditor_name_one,
      </if>
      <if test="auditTimeOne != null">
        audit_time_one,
      </if>
      <if test="auditRemarkOne != null">
        audit_remark_one,
      </if>
      <if test="auditorNameTwo != null">
        auditor_name_two,
      </if>
      <if test="auditTimeTwo != null">
        audit_time_two,
      </if>
      <if test="auditRemarkTwo != null">
        audit_remark_two,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="runningCode != null">
        running_code,
      </if>
      <if test="paymentRemark != null">
        payment_remark,
      </if>
      <if test="rejectRemark != null">
        reject_remark,
      </if>
      <if test="cancelRemark != null">
        cancel_remark,
      </if>
      <if test="balanceAmount != null">
        balance_amount,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reserveApplyCode != null">
        #{reserveApplyCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffName != null">
        #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null">
        #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="staffProperty != null">
        #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="receiveBankAccount != null">
        #{receiveBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="receiveBankAccountName != null">
        #{receiveBankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="receiveBraBankName != null">
        #{receiveBraBankName,jdbcType=VARCHAR},
      </if>
      <if test="applyAmount != null">
        #{applyAmount,jdbcType=DECIMAL},
      </if>
      <if test="applyTime != null">
        #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyRemark != null">
        #{applyRemark,jdbcType=VARCHAR},
      </if>
      <if test="approvedAmount != null">
        #{approvedAmount,jdbcType=DECIMAL},
      </if>
      <if test="auditorNameOne != null">
        #{auditorNameOne,jdbcType=VARCHAR},
      </if>
      <if test="auditTimeOne != null">
        #{auditTimeOne,jdbcType=TIMESTAMP},
      </if>
      <if test="auditRemarkOne != null">
        #{auditRemarkOne,jdbcType=VARCHAR},
      </if>
      <if test="auditorNameTwo != null">
        #{auditorNameTwo,jdbcType=VARCHAR},
      </if>
      <if test="auditTimeTwo != null">
        #{auditTimeTwo,jdbcType=TIMESTAMP},
      </if>
      <if test="auditRemarkTwo != null">
        #{auditRemarkTwo,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="runningCode != null">
        #{runningCode,jdbcType=VARCHAR},
      </if>
      <if test="paymentRemark != null">
        #{paymentRemark,jdbcType=VARCHAR},
      </if>
      <if test="rejectRemark != null">
        #{rejectRemark,jdbcType=VARCHAR},
      </if>
      <if test="cancelRemark != null">
        #{cancelRemark,jdbcType=VARCHAR},
      </if>
      <if test="balanceAmount != null">
        #{balanceAmount,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TReserveApply">
    update t_reserve_apply
    <set>
      <if test="reserveApplyCode != null">
        reserve_apply_code = #{reserveApplyCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffName != null">
        staff_name = #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null">
        staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="staffProperty != null">
        staff_property = #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="receiveBankAccount != null">
        receive_bank_account = #{receiveBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="receiveBankAccountName != null">
        receive_bank_account_name = #{receiveBankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="receiveBraBankName != null">
        receive_bra_bank_name = #{receiveBraBankName,jdbcType=VARCHAR},
      </if>
      <if test="applyAmount != null">
        apply_amount = #{applyAmount,jdbcType=DECIMAL},
      </if>
      <if test="applyTime != null">
        apply_time = #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyRemark != null">
        apply_remark = #{applyRemark,jdbcType=VARCHAR},
      </if>
      <if test="approvedAmount != null">
        approved_amount = #{approvedAmount,jdbcType=DECIMAL},
      </if>
      <if test="auditorNameOne != null">
        auditor_name_one = #{auditorNameOne,jdbcType=VARCHAR},
      </if>
      <if test="auditTimeOne != null">
        audit_time_one = #{auditTimeOne,jdbcType=TIMESTAMP},
      </if>
      <if test="auditRemarkOne != null">
        audit_remark_one = #{auditRemarkOne,jdbcType=VARCHAR},
      </if>
      <if test="auditorNameTwo != null">
        auditor_name_two = #{auditorNameTwo,jdbcType=VARCHAR},
      </if>
      <if test="auditTimeTwo != null">
        audit_time_two = #{auditTimeTwo,jdbcType=TIMESTAMP},
      </if>
      <if test="auditRemarkTwo != null">
        audit_remark_two = #{auditRemarkTwo,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="runningCode != null">
        running_code = #{runningCode,jdbcType=VARCHAR},
      </if>
      <if test="paymentRemark != null">
        payment_remark = #{paymentRemark,jdbcType=VARCHAR},
      </if>
      <if test="rejectRemark != null">
        reject_remark = #{rejectRemark,jdbcType=VARCHAR},
      </if>
      <if test="cancelRemark != null">
        cancel_remark = #{cancelRemark,jdbcType=VARCHAR},
      </if>
      <if test="balanceAmount != null">
        balance_amount = #{balanceAmount,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TReserveApply">
    update t_reserve_apply
    set reserve_apply_code = #{reserveApplyCode,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      type = #{type,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=BIGINT},
      staff_name = #{staffName,jdbcType=VARCHAR},
      staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      staff_property = #{staffProperty,jdbcType=INTEGER},
      receive_bank_account = #{receiveBankAccount,jdbcType=VARCHAR},
      receive_bank_account_name = #{receiveBankAccountName,jdbcType=VARCHAR},
      receive_bra_bank_name = #{receiveBraBankName,jdbcType=VARCHAR},
      apply_amount = #{applyAmount,jdbcType=DECIMAL},
      apply_time = #{applyTime,jdbcType=TIMESTAMP},
      apply_remark = #{applyRemark,jdbcType=VARCHAR},
      approved_amount = #{approvedAmount,jdbcType=DECIMAL},
      auditor_name_one = #{auditorNameOne,jdbcType=VARCHAR},
      audit_time_one = #{auditTimeOne,jdbcType=TIMESTAMP},
      audit_remark_one = #{auditRemarkOne,jdbcType=VARCHAR},
      auditor_name_two = #{auditorNameTwo,jdbcType=VARCHAR},
      audit_time_two = #{auditTimeTwo,jdbcType=TIMESTAMP},
      audit_remark_two = #{auditRemarkTwo,jdbcType=VARCHAR},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      running_code = #{runningCode,jdbcType=VARCHAR},
      payment_remark = #{paymentRemark,jdbcType=VARCHAR},
      reject_remark = #{rejectRemark,jdbcType=VARCHAR},
      cancel_remark = #{cancelRemark,jdbcType=VARCHAR},
      balance_amount = #{balanceAmount,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>