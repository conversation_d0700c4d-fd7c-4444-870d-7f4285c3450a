package com.logistics.management.webapi.api.feign.insurancecompany.dto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/5/29 18:53
 */
@Data
public class ImportInsuranceCompanyRequestDto implements Serializable{
    @ApiModelProperty("导入List")
    private List<InsuranceCompanyRequestDto> importList;
    @ApiModelProperty("失败数量")
    private String numberFailures="0";
    @ApiModelProperty("成功数量")
    private String numberSuccessful="0";
}
