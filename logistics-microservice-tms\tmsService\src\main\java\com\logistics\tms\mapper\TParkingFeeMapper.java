package com.logistics.tms.mapper;

import com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeDetailResponseModel;
import com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeListRequestModel;
import com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeListResponseModel;
import com.logistics.tms.api.feign.parkingfee.dto.SummaryParkingFeeResponseModel;
import com.logistics.tms.controller.vehiclesettlement.response.GetParkingFeeByVehicleIdResponseModel;
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleBySettlementMonthModel;
import com.logistics.tms.entity.TParkingFee;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface TParkingFeeMapper extends BaseMapper<TParkingFee> {

    List<ParkingFeeListResponseModel> getParkingFeeList(@Param("condition") ParkingFeeListRequestModel requestModel);

    SummaryParkingFeeResponseModel getSummary(@Param("condition") ParkingFeeListRequestModel requestModel);

    ParkingFeeDetailResponseModel getDetailById(@Param("parkingFeeId") Long parkingFeeId);

    List<TParkingFee> getByVehicleStartDate(@Param("vehicleId")Long vehicleId, @Param("startDate")Date startDate, @Param("finishDate")Date finishDate);

    GetParkingFeeByVehicleIdResponseModel getCurrentDeductingByIdForSettlement(@Param("id") Long id,@Param("deductingMonth")String deductingMonth);

    List<TParkingFee> getNotTerminal();

    int batchUpdate(@Param("list")List<TParkingFee> list);

    List<GetVehicleBySettlementMonthModel> getVehicleBySettlementMonth(@Param("settlementMonth") String settlementMonth);

    List<GetParkingFeeByVehicleIdResponseModel> getById(@Param("id")Long id);
}