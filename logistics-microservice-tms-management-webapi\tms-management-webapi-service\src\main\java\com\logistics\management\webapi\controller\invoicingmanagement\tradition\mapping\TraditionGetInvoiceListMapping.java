package com.logistics.management.webapi.controller.invoicingmanagement.tradition.mapping;

import com.logistics.management.webapi.client.invoicingmanagement.response.GetInvoiceListResponseModel;
import com.logistics.management.webapi.controller.invoicingmanagement.tradition.response.TraditionGetInvoiceListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * <AUTHOR>
 * @date 2024/04/03
 */
public class TraditionGetInvoiceListMapping extends MapperMapping<GetInvoiceListResponseModel, TraditionGetInvoiceListResponseDto> {
    @Override
    public void configure() {
        TraditionGetInvoiceListResponseDto destination = getDestination();
        GetInvoiceListResponseModel source = getSource();
        if(source!=null&&null!=source.getInvoiceDate()){
            destination.setInvoiceDate(DateUtils.dateToString(source.getInvoiceDate(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
    }
}
