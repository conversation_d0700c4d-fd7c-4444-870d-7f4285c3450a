package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: wjf
 * @date: 2024/6/5 10:56
 */
@Getter
@AllArgsConstructor
public enum MessageNoticeEnum {

    DEMAND_ORDER_BIDDING_PUBLISH("需求单竞价发布",
            ConfigKeyEnum.WEBSOCKET_DEMAND_ORDER_BIDDING_PUBLISH,
            WebSocketModuleTypeEnum.TMS_WEB,
            MessageNoticeModuleEnum.WEB,
            MessageNoticeObjectTypeEnum.BIDDING,
            MessageNoticeMessageTypeEnum.BIDDING_DETAIL),

    BIDDING_ORDER_SELECT_CARRIER("竞价单选择车主",
            ConfigKeyEnum.WEBSOCKET_BIDDING_ORDER_SELECT_CARRIER,
            WebSocketModuleTypeEnum.TMS_WEB,
            MessageNoticeModuleEnum.WEB,
            MessageNoticeObjectTypeEnum.BIDDING,
            MessageNoticeMessageTypeEnum.BIDDING_DETAIL),

    BIDDING_ORDER_CARRIER_QUOTE("竞价单车主报价",
            ConfigKeyEnum.WEBSOCKET_BIDDING_ORDER_CARRIER_QUOTE,
            WebSocketModuleTypeEnum.TMS_MANAGEMENT,
            MessageNoticeModuleEnum.MANAGEMENT,
            MessageNoticeObjectTypeEnum.BIDDING,
            MessageNoticeMessageTypeEnum.BIDDING_DETAIL),

    ;

    private final String remark;
    private final ConfigKeyEnum configKeyEnum;
    private final WebSocketModuleTypeEnum webSocketModuleTypeEnum;
    private final MessageNoticeModuleEnum messageNoticeModuleEnum;
    private final MessageNoticeObjectTypeEnum messageNoticeObjectTypeEnum;
    private final MessageNoticeMessageTypeEnum messageNoticeMessageTypeEnum;

}
