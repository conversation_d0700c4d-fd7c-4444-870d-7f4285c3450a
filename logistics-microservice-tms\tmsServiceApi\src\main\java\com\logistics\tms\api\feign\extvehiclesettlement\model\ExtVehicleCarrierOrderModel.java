package com.logistics.tms.api.feign.extvehiclesettlement.model;

import com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/11/20 18:49
 */
@Data
public class ExtVehicleCarrierOrderModel {
    @ApiModelProperty("运单id")
    private Long carrierOrderId;
    @ApiModelProperty("车辆")
    private Long vehicleId;
    private String vehicleNo;
    @ApiModelProperty("司机")
    private Long staffId;
    private String staffName;
    private String staffMobile;
    @ApiModelProperty("单位：1 件，2 吨，3 方，4 块")
    private Integer goodsUnit;
    @ApiModelProperty("司机费用")
    private Integer dispatchFreightFeeType;
    private BigDecimal dispatchFreightFee;
    @ApiModelProperty("操作人")
    private String userName;
    private List<SearchCarrierOrderListGoodsInfoModel> goodsInfoList;
}
