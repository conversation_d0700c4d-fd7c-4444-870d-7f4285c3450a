package com.logistics.tms.controller.driversafepromise.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 签订列表 - 汇总
 * @Author: sj
 * @Date: 2019/11/4 14:48
 */
@Data
public class SummarySignSafePromiseResponseModel {
    @ApiModelProperty("全部")
    private Integer allCount;
    @ApiModelProperty("已经签订")
    private Integer hasSignCount;
    @ApiModelProperty("未签订")
    private Integer notSignCount;
}
