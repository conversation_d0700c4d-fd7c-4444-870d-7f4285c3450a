<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TPersonalAccidentInsuranceMapper">
  <sql id="Base_Column_List">
    id, insurance_company_id, personal_accident_insurance_id, type, policy_number, batch_number,
    gross_premium, policy_person_count, start_time, end_time, add_user_id, add_user_name,
    source, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TPersonalAccidentInsurance">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="insurance_company_id" jdbcType="BIGINT" property="insuranceCompanyId" />
    <result column="personal_accident_insurance_id" jdbcType="BIGINT" property="personalAccidentInsuranceId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="policy_number" jdbcType="VARCHAR" property="policyNumber" />
    <result column="batch_number" jdbcType="VARCHAR" property="batchNumber" />
    <result column="gross_premium" jdbcType="DECIMAL" property="grossPremium" />
    <result column="policy_person_count" jdbcType="INTEGER" property="policyPersonCount" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="add_user_id" jdbcType="BIGINT" property="addUserId" />
    <result column="add_user_name" jdbcType="VARCHAR" property="addUserName" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_personal_accident_insurance
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_personal_accident_insurance
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TPersonalAccidentInsurance">
    insert into t_personal_accident_insurance (id, insurance_company_id, personal_accident_insurance_id,
      type, policy_number, batch_number, 
      gross_premium, policy_person_count, start_time, 
      end_time, add_user_id, add_user_name, 
      source, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{insuranceCompanyId,jdbcType=BIGINT}, #{personalAccidentInsuranceId,jdbcType=BIGINT}, 
      #{type,jdbcType=INTEGER}, #{policyNumber,jdbcType=VARCHAR}, #{batchNumber,jdbcType=VARCHAR}, 
      #{grossPremium,jdbcType=DECIMAL}, #{policyPersonCount,jdbcType=INTEGER}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{addUserId,jdbcType=BIGINT}, #{addUserName,jdbcType=VARCHAR}, 
      #{source,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TPersonalAccidentInsurance" keyProperty="id" useGeneratedKeys="true">
    insert into t_personal_accident_insurance
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="insuranceCompanyId != null">
        insurance_company_id,
      </if>
      <if test="personalAccidentInsuranceId != null">
        personal_accident_insurance_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="policyNumber != null">
        policy_number,
      </if>
      <if test="batchNumber != null">
        batch_number,
      </if>
      <if test="grossPremium != null">
        gross_premium,
      </if>
      <if test="policyPersonCount != null">
        policy_person_count,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="addUserId != null">
        add_user_id,
      </if>
      <if test="addUserName != null">
        add_user_name,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="insuranceCompanyId != null">
        #{insuranceCompanyId,jdbcType=BIGINT},
      </if>
      <if test="personalAccidentInsuranceId != null">
        #{personalAccidentInsuranceId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="policyNumber != null">
        #{policyNumber,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="grossPremium != null">
        #{grossPremium,jdbcType=DECIMAL},
      </if>
      <if test="policyPersonCount != null">
        #{policyPersonCount,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addUserId != null">
        #{addUserId,jdbcType=BIGINT},
      </if>
      <if test="addUserName != null">
        #{addUserName,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TPersonalAccidentInsurance">
    update t_personal_accident_insurance
    <set>
      <if test="insuranceCompanyId != null">
        insurance_company_id = #{insuranceCompanyId,jdbcType=BIGINT},
      </if>
      <if test="personalAccidentInsuranceId != null">
        personal_accident_insurance_id = #{personalAccidentInsuranceId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="policyNumber != null">
        policy_number = #{policyNumber,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        batch_number = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="grossPremium != null">
        gross_premium = #{grossPremium,jdbcType=DECIMAL},
      </if>
      <if test="policyPersonCount != null">
        policy_person_count = #{policyPersonCount,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addUserId != null">
        add_user_id = #{addUserId,jdbcType=BIGINT},
      </if>
      <if test="addUserName != null">
        add_user_name = #{addUserName,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TPersonalAccidentInsurance">
    update t_personal_accident_insurance
    set insurance_company_id = #{insuranceCompanyId,jdbcType=BIGINT},
      personal_accident_insurance_id = #{personalAccidentInsuranceId,jdbcType=BIGINT},
      type = #{type,jdbcType=INTEGER},
      policy_number = #{policyNumber,jdbcType=VARCHAR},
      batch_number = #{batchNumber,jdbcType=VARCHAR},
      gross_premium = #{grossPremium,jdbcType=DECIMAL},
      policy_person_count = #{policyPersonCount,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      add_user_id = #{addUserId,jdbcType=BIGINT},
      add_user_name = #{addUserName,jdbcType=VARCHAR},
      source = #{source,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>