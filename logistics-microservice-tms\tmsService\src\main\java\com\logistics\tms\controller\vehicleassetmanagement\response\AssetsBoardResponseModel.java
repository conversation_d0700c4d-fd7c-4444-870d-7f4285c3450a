package com.logistics.tms.controller.vehicleassetmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class AssetsBoardResponseModel {

    //车辆管理
    @ApiModelProperty("一体车数量")
    private Integer oneVehicleCount = 0;
    @ApiModelProperty("牵引车数量")
    private Integer tractorCount = 0;
    @ApiModelProperty("挂车数量")
    private Integer trailerCount = 0;
    @ApiModelProperty("危货运输数量")
    private Integer dangerousCarriageCount = 0;
    @ApiModelProperty("普货运输数量")
    private Integer commonCarriageCount = 0;
    @ApiModelProperty("自有车辆数量")
    private Integer ownVehicleCount = 0;
    @ApiModelProperty("外包车辆数量")
    private Integer outVehicleCount = 0;
    //司机管理
    @ApiModelProperty("驾驶员数量")
    private Integer driverCount = 0;
    @ApiModelProperty("押运员数量")
    private Integer superCargoCount = 0;
    @ApiModelProperty("押运员数量")
    private Integer driverAndSuperCargoCount = 0;
    @ApiModelProperty("外部驾驶员数量")
    private Integer extDriverCount = 0;
    //自有车辆环保标准
    @ApiModelProperty("车辆总数量")
    private Integer vehicleTotalCount = 0;
    @ApiModelProperty("国一车辆数量")
    private Integer guoYiCount = 0;
    @ApiModelProperty("国二车辆数量")
    private Integer guoErCount = 0;
    @ApiModelProperty("国三车辆数量")
    private Integer guoSanCount = 0;
    @ApiModelProperty("国四车辆数量")
    private Integer guoSiCount = 0;
    @ApiModelProperty("国五车辆数量")
    private Integer guoWuCount = 0;
    @ApiModelProperty("国六车辆数量")
    private Integer guoLiuCount = 0;
    //运力统计
    @ApiModelProperty("核定载质量（吨）")
    private BigDecimal approvedLoadWeight;
    @ApiModelProperty("总质量（吨）")
    private BigDecimal totalWeight;
    //风险预警
    @ApiModelProperty("车辆证件风险")
    private List<AssetsBoardRiskRecordResponseModel> vehicleCertificateList = new ArrayList<>();
    @ApiModelProperty("人员证件风险")
    private List<AssetsBoardRiskRecordResponseModel> staffCertificateList = new ArrayList<>();
    @ApiModelProperty("保险到期风险")
    private List<AssetsBoardRiskRecordResponseModel> insuranceDueList = new ArrayList<>();

}

