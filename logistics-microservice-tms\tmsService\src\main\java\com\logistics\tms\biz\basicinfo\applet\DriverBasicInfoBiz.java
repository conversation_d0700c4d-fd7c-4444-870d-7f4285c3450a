package com.logistics.tms.biz.basicinfo.applet;

import com.alibaba.fastjson.JSONObject;
import com.logistics.tms.controller.baiscinfo.applet.request.DriverBasicInfoSubmitRequestModel;
import com.logistics.tms.controller.baiscinfo.applet.request.FaceRecognitionRequestModel;
import com.logistics.tms.controller.baiscinfo.applet.request.FaceRecognitionResultRequestModel;
import com.logistics.tms.controller.baiscinfo.applet.request.GetPersonAuthVerifyCodeRequestModel;
import com.logistics.tms.controller.baiscinfo.applet.response.DriverBasicInfoResponseModel;
import com.logistics.tms.controller.baiscinfo.applet.response.FaceRecognitionResponseModel;
import com.logistics.tms.controller.baiscinfo.applet.response.FaceRecognitionResultResponseModel;
import com.logistics.tms.controller.baiscinfo.web.request.VerifyPersonThreeElementsRequestModel;
import com.logistics.tms.controller.baiscinfo.web.request.VerifyPersonTwoElementsRequestModel;
import com.logistics.tms.controller.baiscinfo.web.response.VerifyPersonThreeElementsResponseModel;
import com.logistics.tms.controller.baiscinfo.web.response.VerifyPersonTwoElementsResponseModel;
import com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.tms.base.constant.BestSignCommon;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.authentication.AuthenticationBiz;
import com.logistics.tms.biz.basicinfo.common.BasicInfoCommonBiz;
import com.logistics.tms.biz.basicinfo.common.model.BestSignUserRegModel;
import com.logistics.tms.biz.basicinfo.web.BasicInfoWebBiz;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.client.model.GetVerifyCodeRequestModel;
import com.logistics.tms.entity.TCertificationPictures;
import com.logistics.tms.entity.TCustomerAccount;
import com.logistics.tms.entity.TRealNameAuthentication;
import com.logistics.tms.entity.TStaffBasic;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class DriverBasicInfoBiz {

    private final TStaffBasicMapper staffBasicMapper;
    private final CommonBiz commonBiz;
    @Autowired
    private BasicInfoCommonBiz basicInfoCommonBiz;
    @Autowired
    private AuthenticationBiz authenticationBiz;
    @Autowired
    private TRealNameAuthenticationMapper tRealNameAuthenticationMapper;
    @Autowired
    private TStaffBasicMapper tStaffBasicMapper;
    @Autowired
    private TCertificationPicturesMapper tCertificationPicturesMapper;
    @Autowired
    private TCustomerAccountMapper tCustomerAccountMapper;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private BasicInfoWebBiz basicInfoWebBiz;

    // 获取员工Id
    private Long loginDriverAppletUserId() {
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        if (CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }
        return loginDriverAppletUserId;
    }

    /**
     * 司机实名认证校验
     */
    public void checkRealNameAuth() {
        checkRealNameAuth(loginDriverAppletUserId());
    }

    /**
     * 司机实名认证校验
     */
    public void checkRealNameAuth(Long driverId) {
        Optional.ofNullable(driverBasicInfo(driverId))
                .filter(f -> RealNameAuthenticationStatusEnum.REAL_NAME.getKey().equals(f.getRealNameAuthenticationStatus()))
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.CHECK_REAL_NAME));
    }

    public DriverBasicInfoResponseModel driverBasicInfo() {
        return driverBasicInfo(loginDriverAppletUserId());
    }

    /**
     * 查询司机基础信息
     *
     * @return
     */
    public DriverBasicInfoResponseModel driverBasicInfo(Long driverId) {
        return staffBasicMapper.selectDriverBasicInfo(driverId);
    }

    /**
     * 司机基础信息提交
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public void driverBasicInfoSubmit(DriverBasicInfoSubmitRequestModel requestModel) {
        //获取当前登录的司机的基础信息并校验实名状态
        TStaffBasic tStaffBasic = getStaffBasinInfo();
        if (RealNameAuthenticationStatusEnum.REAL_NAME.getKey().equals(tStaffBasic.getRealNameAuthenticationStatus())){
            throw new BizException(CarrierDataExceptionEnum.REAL_NAME_AUTH_INFO_EXIST);
        }

        //查询实名认证信息
        TRealNameAuthentication tRealNameAuthentication = tRealNameAuthenticationMapper.selectRealNameAuthenticationByMobile(tStaffBasic.getMobile());
        if (tRealNameAuthentication == null) {
            throw new BizException(CarrierDataExceptionEnum.REAL_NAME_AUTH_INFO_NOT_EXIST);
        }
        //判断认证模式
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getAuthModel())) {
            /*短信认证*/
            basicInfoCommonBiz.bestsignVerificationCodeAuth(requestModel.getVerificationCode());
        } else {
            /*人脸识别*/
            basicInfoCommonBiz.bestsignFaceAuth(requestModel.getOrderNo());
        }

        //实名认证的信息如果和当前司机的信息不一致那就更新司机信息
        //更新司机个人信息
        updateStaffBasicInfo(tStaffBasic, requestModel.getIdentityFaceFile(), requestModel.getIdentityNationalFile(), tRealNameAuthentication);

        //查询当前手机号是否有个人车主,如果有那么将个人车主实名状态也修改为已实名并修改姓名和身份证号
        FuzzySearchCompanyCarrierResponseModel personCarrierInfo = tCompanyCarrierMapper.selectPersonCarrierByMobile(tStaffBasic.getMobile());
        if (personCarrierInfo != null) {
            basicInfoWebBiz.updatePersonCarrierInfo(personCarrierInfo, requestModel.getIdentityFaceFile(), requestModel.getIdentityNationalFile(), tRealNameAuthentication);
        }

        //更新司机账号名
        TCustomerAccount tCustomerAccount = tCustomerAccountMapper.selectByMobile(tStaffBasic.getMobile());
        if (tCustomerAccount != null) {
            TCustomerAccount tCustomerAccountUp = new TCustomerAccount();
            tCustomerAccountUp.setId(tCustomerAccount.getId());
            tCustomerAccountUp.setUserName(tRealNameAuthentication.getName());
            commonBiz.setBaseEntityModify(tCustomerAccountUp, BaseContextHandler.getUserName());
            tCustomerAccountMapper.updateByPrimaryKeySelective(tCustomerAccountUp);
        }

        //更新实名认证状态
        TRealNameAuthentication tRealNameAuthenticationUp = new TRealNameAuthentication();
        tRealNameAuthenticationUp.setId(tRealNameAuthentication.getId());
        tRealNameAuthenticationUp.setAuthMode(requestModel.getAuthModel());
        tRealNameAuthenticationUp.setCertificationStatus(CommonConstant.INTEGER_TWO);
        commonBiz.setBaseEntityModify(tRealNameAuthenticationUp, BaseContextHandler.getUserName());
        int size = tRealNameAuthenticationMapper.updateCertificationStatus(tRealNameAuthenticationUp, RealNameAuthenticationStatusEnum.NOT_REAL_NAME.getKey());
        if (size < CommonConstant.INTEGER_ONE) {
            throw new BizException(CarrierDataExceptionEnum.REAL_NAME_AUTH_INFO_EXIST);
        }
    }

    /**
     * 实名-修改司机基础信息
     *
     * @param tStaffBasic             司机信息
     * @param identityFaceFile        身份证人面图片路径
     * @param identityNationalFile    身份证国徽面图片路径
     * @param tRealNameAuthentication 实名认证信息
     */
    @Transactional
    public void updateStaffBasicInfo(TStaffBasic tStaffBasic, String identityFaceFile, String identityNationalFile, TRealNameAuthentication tRealNameAuthentication) {
        TStaffBasic tStaffBasicUp = new TStaffBasic();
        if (!tStaffBasic.getName().equals(tRealNameAuthentication.getName())) {
            tStaffBasicUp.setName(tRealNameAuthentication.getName());
        }
        if (!tStaffBasic.getIdentityNumber().equals(tRealNameAuthentication.getIdentityNumber())) {
            tStaffBasicUp.setIdentityNumber(tRealNameAuthentication.getIdentityNumber());
        }

        //更新身份证图片
        List<TCertificationPictures> tCertificationPicturesUpList = new ArrayList<>();
        List<TCertificationPictures> tCertificationPicturesAddList = new ArrayList<>();
        CertificationPicturesObjectTypeEnum objectType = CertificationPicturesObjectTypeEnum.T_STAFF_BASIC;
        CertificationPicturesFileTypeEnum identityFront = CertificationPicturesFileTypeEnum.STAFF_IDENTITY_FRONT;
        CertificationPicturesFileTypeEnum identityBack = CertificationPicturesFileTypeEnum.STAFF_IDENTITY_BACK;

        //人像面
        if (StringUtils.isBlank(identityFaceFile)) {
            // 移除身份证关联
            tCertificationPicturesMapper.delByObjectTypeFileTypeId(objectType.getObjectType(), identityFront.getFileType(), tStaffBasic.getId(), BaseContextHandler.getUserName());
        } else {
            String filePath = commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CUSTOMER_CARRIER_IDENTITY_NATIONAL.getKey(), "", identityFaceFile, null);
            List<TCertificationPictures> identityFrontList = tCertificationPicturesMapper.getByObjectIdType(tStaffBasic.getId(), objectType.getObjectType(), identityFront.getFileType());
            if (ListUtils.isNotEmpty(identityFrontList)) {
                TCertificationPictures identityFrontEntity = identityFrontList.get(CommonConstant.INTEGER_ZERO);
                identityFrontEntity.setFilePath(filePath);
                commonBiz.setBaseEntityModify(identityFrontEntity, BaseContextHandler.getUserName());
                tCertificationPicturesUpList.add(identityFrontEntity);
            } else {
                tCertificationPicturesAddList.add(commonBiz.getCertificationPictures(tStaffBasic.getId(), objectType, identityFront, filePath));
            }
        }

        //国徽面
        if (StringUtils.isBlank(identityNationalFile)) {
            // 移除身份证关联
            tCertificationPicturesMapper.delByObjectTypeFileTypeId(objectType.getObjectType(), identityBack.getFileType(), tStaffBasic.getId(), BaseContextHandler.getUserName());
        } else {
            String filePath = commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CUSTOMER_CARRIER_IDENTITY_NATIONAL.getKey(), "", identityNationalFile, null);
            List<TCertificationPictures> identityBackList = tCertificationPicturesMapper.getByObjectIdType(tStaffBasic.getId(), objectType.getObjectType(), identityBack.getFileType());
            if (ListUtils.isNotEmpty(identityBackList)) {
                //国徽面
                TCertificationPictures identityBackEntity = identityBackList.get(CommonConstant.INTEGER_ZERO);
                identityBackEntity.setFilePath(filePath);
                commonBiz.setBaseEntityModify(identityBackEntity, BaseContextHandler.getUserName());
                tCertificationPicturesUpList.add(identityBackEntity);
            } else {
                tCertificationPicturesAddList.add(commonBiz.getCertificationPictures(tStaffBasic.getId(), objectType, identityBack, filePath));
            }
        }

        if (ListUtils.isNotEmpty(tCertificationPicturesUpList)) {
            tCertificationPicturesMapper.batchUpdate(tCertificationPicturesUpList);
        }
        if (ListUtils.isNotEmpty(tCertificationPicturesAddList)) {
            tCertificationPicturesMapper.batchInsert(tCertificationPicturesAddList);
        }

        tStaffBasicUp.setId(tStaffBasic.getId());
        tStaffBasicUp.setRealNameAuthenticationStatus(RealNameAuthenticationStatusEnum.REAL_NAME.getKey());
        commonBiz.setBaseEntityModify(tStaffBasicUp, BaseContextHandler.getUserName());
        tStaffBasicMapper.updateByPrimaryKeySelective(tStaffBasicUp);
    }

    /**
     * 个人手机号认证-获取验证码
     *
     * @return
     */
    public void getVerifyCode(GetPersonAuthVerifyCodeRequestModel requestModel) {
        //获取当前登录的司机的基础信息并校验实名状态
        TStaffBasic tStaffBasic = getStaffBasinInfo();
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getOperationType()) &&
                !RealNameAuthenticationStatusEnum.REAL_NAME.getKey().equals(tStaffBasic.getRealNameAuthenticationStatus())) {
            throw new BizException(CarrierDataExceptionEnum.CHECK_REAL_NAME);
        }
        //查询实名认证信息
        TRealNameAuthentication tRealNameAuthentication = tRealNameAuthenticationMapper.selectRealNameAuthenticationByMobile(tStaffBasic.getMobile());
        if (tRealNameAuthentication == null) {
            throw new BizException(CarrierDataExceptionEnum.REAL_NAME_AUTH_INFO_NOT_EXIST);
        }
        //获取验证码
        GetVerifyCodeRequestModel getVerifyCodeRequestModel = new GetVerifyCodeRequestModel();
        getVerifyCodeRequestModel.setAccount(tRealNameAuthentication.getBestsignAccount());
        getVerifyCodeRequestModel.setName(tRealNameAuthentication.getName());
        getVerifyCodeRequestModel.setMobile(requestModel.getMobile());
        getVerifyCodeRequestModel.setIdentity(tRealNameAuthentication.getIdentityNumber());
        basicInfoCommonBiz.bestSignGetVerifyCode(getVerifyCodeRequestModel);
    }

    /**
     * 个人二要素校验
     *
     * @param requestModel
     * @return
     */
    public VerifyPersonTwoElementsResponseModel verifyPersonTwoElements(VerifyPersonTwoElementsRequestModel requestModel) {
        VerifyPersonTwoElementsResponseModel responseModel = authenticationBiz.verifyPersonTwoElements(requestModel);
        //认证通过,注册上上签账户
        if (CommonConstant.ONE.equals(responseModel.getResult())) {
            //获取当前登录的司机的基础信息并校验实名状态
            TStaffBasic tStaffBasic = getStaffBasinInfo();

            //注册上上签账户
            BestSignUserRegModel model = new BestSignUserRegModel();
            model.setAuthenticationType(CommonConstant.INTEGER_ONE);
            model.setName(requestModel.getName());
            model.setMobile(tStaffBasic.getMobile());
            model.setIdentityNumber(requestModel.getIdentity());
            model.setRegType(CommonConstant.INTEGER_ONE);
            basicInfoCommonBiz.bestSignUserReg(model);
        }else {
            throw new BizException(CarrierDataExceptionEnum.REAL_NAME_BASIC_INFO_ERROR);
        }
        return responseModel;
    }

    /**
     * 个人三要素校验
     *
     * @param requestModel
     * @return
     */
    public VerifyPersonThreeElementsResponseModel verifyPersonThreeElements(VerifyPersonThreeElementsRequestModel requestModel) {
        VerifyPersonThreeElementsResponseModel responseModel = authenticationBiz.verifyPersonalThreeElements(requestModel);
        if (!CommonConstant.ONE.equals(responseModel.getResult())) {
            throw new BizException(CarrierDataExceptionEnum.REAL_NAME_BASIC_INFO_ERROR);
        }
        return responseModel;
    }

    /**
     * 获取刷脸认证签名
     *
     * @param requestModel
     * @return
     */
    public FaceRecognitionResponseModel faceRecognition(FaceRecognitionRequestModel requestModel) {
        //获取当前登录的司机的基础信息并校验实名状态
        TStaffBasic tStaffBasic = getStaffBasinInfo();
        //查询实名认证信息
        TRealNameAuthentication tRealNameAuthentication = tRealNameAuthenticationMapper.selectRealNameAuthenticationByMobile(tStaffBasic.getMobile());
        if (tRealNameAuthentication == null) {
            throw new BizException(CarrierDataExceptionEnum.REAL_NAME_AUTH_INFO_NOT_EXIST);
        }
        requestModel.setAccount(tRealNameAuthentication.getBestsignAccount());
        requestModel.setName(tRealNameAuthentication.getName());
        requestModel.setIdNo(tRealNameAuthentication.getIdentityNumber());
        //获取刷脸认证签名
        return authenticationBiz.faceRecognition(requestModel);
    }

    /**
     * 获取当前登录的司机的基础信息并校验实名状态
     *
     * @return
     */
    private TStaffBasic getStaffBasinInfo() {
        //司机id
        Long userId = loginDriverAppletUserId();
        //查询司机基础信息
        TStaffBasic tStaffBasic = staffBasicMapper.selectByPrimaryKey(userId);
        if (tStaffBasic == null || IfValidEnum.INVALID.getKey().equals(tStaffBasic.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }
        return tStaffBasic;
    }

    /**
     * 获取刷脸认证结果
     *
     * @param requestModel
     * @return
     */
    public FaceRecognitionResultResponseModel faceRecognitionResult(FaceRecognitionResultRequestModel requestModel) {
        JSONObject responseBody = authenticationBiz.verifyFaceRecognitionResult(requestModel.getOrderNo());
        if (responseBody == null) {
            throw new BizException(CarrierDataExceptionEnum.FACE_AUTH_ERROR);
        }

        FaceRecognitionResultResponseModel faceRecognitionResultResponseModel = new FaceRecognitionResultResponseModel();
        faceRecognitionResultResponseModel.setVerifyResult(responseBody.getString(BestSignCommon.VERIFY_RESULT));
        return faceRecognitionResultResponseModel;
    }
}
