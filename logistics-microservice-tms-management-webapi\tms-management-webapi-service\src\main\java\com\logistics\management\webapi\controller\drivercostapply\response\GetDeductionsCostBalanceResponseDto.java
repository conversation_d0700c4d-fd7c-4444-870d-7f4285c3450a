package com.logistics.management.webapi.controller.drivercostapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetDeductionsCostBalanceResponseDto {

    @ApiModelProperty("司机ID")
    private String driverId = "";

    @ApiModelProperty("司机姓名")
    private String driverName = "";

    @ApiModelProperty("司机电话")
    private String driverPhone = "";

    @ApiModelProperty("备用金余额")
    private String balance = "";
}
