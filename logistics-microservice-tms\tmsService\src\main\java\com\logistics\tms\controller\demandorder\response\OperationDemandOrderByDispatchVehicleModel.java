package com.logistics.tms.controller.demandorder.response;

import com.logistics.tms.controller.demandorder.request.UpdateDispatchVehicleCountRequestModel;
import com.logistics.tms.rabbitmq.publisher.model.CarrierOrderCreateListToYeloLifeModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2018/11/15 10:37
 */
@Data
public class OperationDemandOrderByDispatchVehicleModel {

    @ApiModelProperty("生成运单编号后修改需求单表已调车数")
    private List<UpdateDispatchVehicleCountRequestModel> updateCountList;
    @ApiModelProperty("需求单id对应运单号")
    private Map<Long,String> demandOrderCarrierMap;
    @ApiModelProperty("调度车辆生成运单同时修改需求单状态")
    private UpdateDemandOrderStatusAndAmountModel statusAmountModel;

    @ApiModelProperty("新生运单List")
    private List<CarrierOrderCreateListToYeloLifeModel> carrierOrderForLifeList;
}
