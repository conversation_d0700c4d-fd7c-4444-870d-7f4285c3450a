package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/19
 */
@Data
public class SearchCarrierOrderListForYeloLifeResponseModel {

	@ApiModelProperty("运单ID")
	private Long carrierOrderId;

	@ApiModelProperty("10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0 已取消")
	private Integer status;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("客户单号")
	private String customerOrderCode;

	@ApiModelProperty("需求单ID")
	private Long demandOrderId;

	@ApiModelProperty("需求单号")
	private String demandOrderCode;

	@ApiModelProperty("客户")
	private String customName;

	@ApiModelProperty("个人客户姓名")
	private String customerUserName;

	@ApiModelProperty("个人客户手机号")
	private String customerUserMobile;

	@ApiModelProperty("车牌号")
	private String vehicleNo;

	@ApiModelProperty("司机姓名")
	private String driverName;

	@ApiModelProperty("司机手机号")
	private String driverMobile;

	//发货地址信息
	@ApiModelProperty("发货仓库")
	private String loadWarehouse;
	private String loadProvinceName;
	private String loadCityName;
	private String loadAreaName;
	private String loadDetailAddress;

	//收货地址信息
	@ApiModelProperty("收货仓库")
	private String unloadWarehouse;
	private String unloadProvinceName;
	private String unloadCityName;
	private String unloadAreaName;
	private String unloadDetailAddress;

	@ApiModelProperty("备注")
	private String remark;

	@ApiModelProperty("期望提货时间")
	private Date expectedLoadTime;

	@ApiModelProperty("预计承运数量")
	private BigDecimal expectAmount;

	@ApiModelProperty("实际提货数量")
	private BigDecimal loadAmount;

	@ApiModelProperty("实际卸货数量")
	private BigDecimal unloadAmount;

	@ApiModelProperty("签收数量")
	private BigDecimal signAmount;

	@ApiModelProperty("回单数")
	private Integer carrierOrderTicketsAmount;

	@ApiModelProperty("调度人")
	private String dispatchUserName;

	@ApiModelProperty("运单生成时间")
	private Date dispatchTime;

	@ApiModelProperty("来源")
	private Integer customerOrderSource;

	@ApiModelProperty("是否取消")
	private Integer ifCancel;

	@ApiModelProperty("货物单位")
	private Integer goodsUnit;

	@ApiModelProperty("业务类型")
	private Integer businessType;

	@ApiModelProperty("发布人姓名")
	private String publishName;

	@ApiModelProperty("发布人手机号")
	private String publishMobile;

	@ApiModelProperty("车主id")
	private Long companyCarrierId;

	@ApiModelProperty("车主")
	private String companyCarrierName;

	@ApiModelProperty("车主公司类型")
	private Integer companyCarrierType;

	@ApiModelProperty("车主联系人")
	private String carrierContactName;

	@ApiModelProperty("车主联系人电话")
	private String carrierContactPhone;

	@ApiModelProperty("货物信息")
	private List<SearchCarrierOrderListGoodsInfoModel> goodsInfoList;

	/**
	 * 需求类型 100 新生回收，101新生销售
	 */
	private Integer entrustType;

	/**
	 * 出库状态：0 待出库，2 已出库
	 */
	@ApiModelProperty("出库状态：0 待出库，2 已出库")
	private Integer outStatus;

	@ApiModelProperty("是否按编码回收 0:否 1:是   v2.44")
	private Integer ifRecycleByCode;

}
