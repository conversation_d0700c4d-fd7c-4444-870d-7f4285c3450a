package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.controller.driversafepromise.request.SearchSafePromiseAppletListRequestModel;
import com.logistics.tms.controller.driversafepromise.response.SearchSafePromiseAppletListResponseModel;
import com.logistics.tms.controller.driversafepromise.request.SearchSafePromiseListRequestModel;
import com.logistics.tms.controller.driversafepromise.response.SearchSafePromiseListResponseModel;
import com.logistics.tms.entity.TDriverSafePromise;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

@Mapper
public interface TDriverSafePromiseMapper extends BaseMapper<TDriverSafePromise> {

    int getCountByPeriod(@Param("period") String period);

    int getCountById(@Param("safePromiseId") Long safePromiseId);

    List<SearchSafePromiseListResponseModel> searchList(@Param("params") SearchSafePromiseListRequestModel requestModel);

    int deleteSafePromiseById(@Param("safePromiseId") Long safePromiseId,@Param("modifiedBy") String modifiedBy,@Param("modifiedTime") Date modifiedTime);

    List<SearchSafePromiseAppletListResponseModel> searchAppletList(@Param("params") SearchSafePromiseAppletListRequestModel requestModel);
}