package com.logistics.management.webapi.api.feign.dispatchorder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DispatchOrderSearchResponseDto implements Serializable {
    @ApiModelProperty("调度单id")
    private String dispatchOrderId="";
    @ApiModelProperty("调度单号")
    private String dispatchOrderCode="";
    @ApiModelProperty("运单数")
    private Integer carrierOrderCount;
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty("司机")
    private String driverName="";
    @ApiModelProperty("调度时间")
    private String dispatchTime="";
    @ApiModelProperty("调度人")
    private String dispatchUserName="";

    @ApiModelProperty("装卸数")
    private String pointAmount="";
    @ApiModelProperty("调度合计费用")
    private String dispatchSumAmount = "";
    @ApiModelProperty("预计司机运费")
    private String dispatchFreightFee="";
    @ApiModelProperty("实际司机运费合计")
    private String signDispatchFreightFee="";
    @ApiModelProperty("实际调整价")
    private String adjustFee="";
    @ApiModelProperty("实际多装多卸价")
    private String markupFee="";

    private String companyCarrierName = "";
}
