package com.logistics.tms.biz.carrierfreight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierFreightCarrierModel {

    @ApiModelProperty("车主ID")
    private Long companyCarrierId;

    /**
     * 车主运价ID
     */
    @ApiModelProperty("车主运价ID")
    private Long carrierFreightConfigId;

    /**
     * 车主运价ID
     */
    @ApiModelProperty("车主运价配置ID")
    private Long carrierFreightId;

    /**
     * 配置类型; 1: 固定路线; 2: 区域设置; 3: 距离阶梯
     */
    @ApiModelProperty("配置类型; 1: 固定路线; 2: 区域设置; 3: 距离阶梯")
    private Integer configType;

    /**
     * 需求类型，多种类型‘,’拼接;
     */
    @ApiModelProperty("需求类型，多种类型‘,’拼接;")
    private String entrustType;
}
