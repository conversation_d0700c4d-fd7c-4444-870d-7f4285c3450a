package com.logistics.tms.controller.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class RenewableConfirmGoodsResponseModel {

    @ApiModelProperty(value = "新生订单审核列表id")
    private Long renewableOrderId;

    @ApiModelProperty(value = "单号")
    private String renewableOrderCode;

    @ApiModelProperty(value = "指派司机")
    private String  staffName;

    @ApiModelProperty(value = "车牌号")
    private String  vehicleNo;

    @ApiModelProperty(value = "确认货物信息")
    private List<RenewableAuditGoodsResponseModel> confirmGoodsList;

    @ApiModelProperty(value = "图片url")
    private List<RenewableTicketsUrlResponseModel> photosList;
}
