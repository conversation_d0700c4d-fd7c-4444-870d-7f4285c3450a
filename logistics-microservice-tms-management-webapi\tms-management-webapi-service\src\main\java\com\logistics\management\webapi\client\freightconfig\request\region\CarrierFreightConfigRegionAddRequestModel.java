package com.logistics.management.webapi.client.freightconfig.request.region;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CarrierFreightConfigSchemeTypeEnum;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.client.freightconfig.request.CarrierFreightConfigRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.ladder.CarrierFreightConfigLadderRequestCheck;
import com.logistics.management.webapi.client.freightconfig.request.ladder.CarrierFreightConfigLadderRequestModel;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigRegionAddRequestModel extends CarrierFreightConfigRequestModel {

    @ApiModelProperty(value = "新增方案集合")
    private List<CarrierFreightConfigRegionAddItemRequestModel> schemeList;

    @Data
    public static class CarrierFreightConfigRegionAddItemRequestModel {

        @ApiModelProperty(value = "运价方案类型; 100: 路线配置; 200:同区-跨区价格; 201: 同区价格; 202: 跨区价格; 301:系统计算预计距离; 302:系统配置距离", required = true)
        private Integer schemeType;

        @ApiModelProperty(value = "阶梯", required = true)
        private List<CarrierFreightConfigLadderRequestModel> ladderConfigList;
    }

    // 校验
    public void check() {
        // 方案校验
        Map<Integer, Integer> schemeMap = schemeList
                .stream()
                .collect(Collectors.toMap(CarrierFreightConfigRegionAddItemRequestModel::getSchemeType,
                        v -> CommonConstant.INTEGER_ZERO,
                        (v1, v2) -> {
                            throw new BizException(ManagementWebApiExceptionEnum.FREIGHT_REGION_SCHEME_CONFIG_REPEAT);
                        }));
        if (schemeMap.containsKey(CarrierFreightConfigSchemeTypeEnum.REGION_CONFIG.getKey())
                && schemeMap.size() == CommonConstant.INTEGER_TWO) {
            throw new BizException(ManagementWebApiExceptionEnum.FREIGHT_REGION_SCHEME_CONFIG_REPEAT);
        }
        schemeList.forEach(f -> {
            CarrierFreightConfigLadderRequestCheck.check(f.getLadderConfigList());
        });
    }
}
