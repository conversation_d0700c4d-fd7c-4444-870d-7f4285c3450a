package com.logistics.management.webapi.controller.carrierorderticketsaudit.mapping;

import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.enums.EntrustTypeEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.carrierorderticketsaudit.enums.CarrierOrderTicketsAuditStatusEnum;
import com.logistics.management.webapi.client.carrierorderticketsaudit.response.SearchReceiptAuditListResponseModel;
import com.logistics.management.webapi.controller.carrierorderticketsaudit.response.SearchReceiptAuditListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

import java.util.function.Function;

public class SearchReceiptAuditListMapping extends MapperMapping<SearchReceiptAuditListResponseModel, SearchReceiptAuditListResponseDto> {

    @Override
    public void configure() {
        SearchReceiptAuditListResponseModel source = getSource();
        SearchReceiptAuditListResponseDto destination = getDestination();

        // 名称转换
        destination.setAuditStatusLabel(CarrierOrderTicketsAuditStatusEnum.getEnumByKey(source.getAuditStatus()).getValue());

        //需求类型
        destination.setEntrustTypeLabel(EntrustTypeEnum.getEnum(source.getEntrustType()).getValue());

        // 手机号加密
        destination.setDriverName(source.getDriverName() + " " + FrequentMethodUtils.encryptionData(source.getDriverMobile(), EncodeTypeEnum.MOBILE_PHONE));

        // 详细地址 + 【仓库名称】
        Function<String, String> warehouseJoinFun = (warehouse) -> {
            return StringUtils.isBlank(warehouse) ? "" : "【" + warehouse + "】";
        };
        destination.setLoadAddress(source.getLoadAddress() + warehouseJoinFun.apply(source.getLoadWarehouse()));
        destination.setUnloadAddress(source.getUnloadAddress() + warehouseJoinFun.apply(source.getUnloadWarehouse()));
    }
}
