package com.logistics.appapi.controller.carrierorderotherfee.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2022/9/2 11:18
 */
@Data
public class GetOtherFeeByCarrierOrderIdRequestDto {
    @ApiModelProperty(value = "运单id",required = true)
    @NotBlank(message = "id不能为空")
    private String carrierOrderId;
}
