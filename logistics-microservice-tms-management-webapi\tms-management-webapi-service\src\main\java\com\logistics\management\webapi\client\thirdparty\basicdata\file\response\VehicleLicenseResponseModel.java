package com.logistics.management.webapi.client.thirdparty.basicdata.file.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/5/8 17:19
 */
@Data
public class VehicleLicenseResponseModel {
    @ApiModelProperty("车辆识别代号")
    private String vin="";
    @ApiModelProperty("住址")
    private String address="";
    @ApiModelProperty("品牌")
    private String brandModel="";
    @ApiModelProperty("型号")
    private String model="";
    @ApiModelProperty("发证日期")
    private String issueDate="";
    @ApiModelProperty("车辆类型")
    private String vehicleType="";
    @ApiModelProperty("所有人")
    private String owner="";
    @ApiModelProperty("使用性质")
    private String usingNature="";
    @ApiModelProperty("发动机号码")
    private String engineNumber="";
    @ApiModelProperty("号牌号码")
    private String vehicleNumber="";
    @ApiModelProperty("注册日期")
    private String registeredDate="";
}
