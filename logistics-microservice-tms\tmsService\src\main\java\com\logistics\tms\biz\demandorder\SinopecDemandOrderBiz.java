package com.logistics.tms.biz.demandorder;

import cn.org.secmid.phoenix.util.encoders.Base64;
import cn.org.secmid.security.SecurityEngineDeal;
import cn.org.sniopec.fades.UcspInterface;
import com.alibaba.fastjson.JSONObject;
import com.logistics.tms.controller.demandorder.request.sinopec.CancelSinopecDemandV1RequestModel;
import com.logistics.tms.api.feign.freight.model.GetPriceByAddressAndAmountRequestModel;
import com.logistics.tms.api.feign.freight.model.GetPriceByAddressAndAmountResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.constant.ConfigKeyConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.base.utils.RegExpValidatorUtil;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.demandorder.model.CompanyCarrierByIdModel;
import com.logistics.tms.biz.email.EmailConstant;
import com.logistics.tms.biz.email.model.SinopecEntrustOrderModel;
import com.logistics.tms.biz.email.model.SinopecOrderResultModel;
import com.logistics.tms.biz.freight.FreightBiz;
import com.logistics.tms.biz.sinopec.SinopecBiz;
import com.logistics.tms.biz.sinopec.model.SinopecOrderQuotationRequestModel;
import com.logistics.tms.biz.sinopec.model.SinopecRefuseConsignOrderRequestModel;
import com.logistics.tms.biz.sinopec.model.SinopecResultModel;
import com.logistics.tms.biz.sysconfig.SysConfigBiz;
import com.logistics.tms.controller.demandorder.request.*;
import com.logistics.tms.controller.demandorder.request.sinopec.*;
import com.logistics.tms.controller.demandorder.response.BatchPublishSinopecDetailResponseModel;
import com.logistics.tms.controller.demandorder.response.BatchPublishSinopecResponseModel;
import com.logistics.tms.controller.demandorder.response.PublishSinopecResponseModel;
import com.logistics.tms.controller.demandorder.response.SinopecReportAbnormalDetailResponseModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.utils.RedisUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2019/11/1 14:32
 */
@Service
@Slf4j
public class SinopecDemandOrderBiz {

    @Autowired
    private TDemandOrderMapper demandOrderMapper;
    @Autowired
    private TDemandOrderAddressMapper demandOrderAddressMapper;
    @Autowired
    private TDemandOrderGoodsMapper demandOrderGoodsMapper;
    @Autowired
    private TDemandOrderEventsMapper demandOrderEventsMapper;
    @Autowired
    private TDemandOrderOperateLogsMapper demandOrderOperateLogsMapper;
    @Autowired
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Autowired
    private TCarrierOrderOperateLogsMapper tCarrierOrderOperateLogsMapper;
    @Autowired
    private TCarrierOrderEventsMapper tCarrierOrderEventsMapper;
    @Autowired
    private TCompanyEntrustMapper companyEntrustMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TCompanyEntrustMapper tCompanyEntrustMapper;
    @Autowired
    private TCompanyMapper companyMapper;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private TWarehouseAddressMapper tWarehouseAddressMapper;
    private ObjectMapper objectMapper = JacksonUtils.getInstance();
    @Autowired
    private FreightBiz freightBiz;
    @Autowired
    private DemandOrderCommonBiz demandOrderCommonBiz;
    @Autowired
    private TSinopecOriginalDataMapper tSinopecOriginalDataMapper;
    @Autowired
    private TSinopecMessageLogMapper tSinopecMessageLogMapper;
    @Autowired
    private SinopecBiz sinopecBiz;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private TDemandOrderObjectionSinopecMapper tDemandOrderObjectionSinopecMapper;
    @Autowired
    private TDemandOrderCarrierMapper tDemandOrderCarrierMapper;
    @Autowired
    private TSinopecOriginalFreightUnitDataMapper tSinopecOriginalFreightUnitDataMapper;
    @Autowired
    private TSinopecOriginalTransitPortLocationDataMapper tSinopecOriginalTransitPortLocationDataMapper;
    @Autowired
    private TSinopecOriginalAnnexDataMapper tSinopecOriginalAnnexDataMapper;
    @Autowired
    private TSinopecOriginalTransitLineItemDataMapper tSinopecOriginalTransitLineItemDataMapper;
    @Autowired
    private UcspInterface ucspInterface;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private SysConfigBiz configBiz;

    /**
     * 从中石化官网爬取数据并生成需求单
     *
     * @param tmpEntrustOrder
     * @param configResult
     * @param addDemandOrderAddressList
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveDemandOrder(SinopecEntrustOrderModel tmpEntrustOrder, Map<String, String> configResult, List<TDemandOrderAddress> addDemandOrderAddressList) {
        //获取公司信息
        String companyEntrustName = "中国石化化工销售有限公司" + tmpEntrustOrder.getConsignerName();
        TCompanyEntrust companyEntrust = companyEntrustMapper.getByName(companyEntrustName);
        if (companyEntrust == null) {
            companyEntrust = addEntrustCompany(companyEntrustName);
        }
        String ticketTimeStr = null;
        Date ticketTime = null;
        if (tmpEntrustOrder.getExpectExeDateTime() != null) {
            ticketTime = new Date(tmpEntrustOrder.getExpectExeDateTime());
            ticketTimeStr = DateUtils.dateToString(ticketTime, DateUtils.DATE_TO_STRING_SHORT_PATTERN);
        }

        //查询公司仓库是否存在
        TWarehouseAddress tWarehouseAddress = tWarehouseAddressMapper.getValidWarehouseByNameForSinoper(companyEntrustName, tmpEntrustOrder.getOutWarehouseName(), ticketTimeStr);
        if (tWarehouseAddress == null){
            return;
        }

        Date now = new Date();
        Map<String, String> addressMap = new HashMap<>();

        BigDecimal goodsAmount = tmpEntrustOrder.getQty();
        try{
            addressMap = commonBiz.getProvinceCityArea(getAddress(tmpEntrustOrder.getRemark()));
            if(MapUtils.isEmpty(addressMap)){
                addressMap = commonBiz.getProvinceCityArea(tmpEntrustOrder.getRecvZoneName());
            }
        } catch (Exception e){
            log.info(e.getMessage(),e);
        }

        Long companyCarrierId = CommonConstant.LONG_ZERO;
        String companyCarrierName = configResult.get(ConfigKeyEnum.QIYA_COMPANY_NAME.getValue());
        Integer companyCarrierLevel = null;
        TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.getByName(companyCarrierName);
        if (tCompanyCarrier != null){
            companyCarrierId = tCompanyCarrier.getId();
            companyCarrierLevel = tCompanyCarrier.getLevel();
        }
        String checkDetailUrl = configResult.get(ConfigKeyEnum.KEY_API_CHECK_ORDER_DETAIL_URL.getValue());
        BigDecimal unitPrice = null;
        if(StringUtils.isNotBlank(checkDetailUrl)){
            checkDetailUrl = checkDetailUrl+ tmpEntrustOrder.getId();
            //获取需求单单价
            try {
                String result = HttpClientUtils.requestUsingPost(checkDetailUrl, null);
                if(StringUtils.isNotBlank(result)){
                    SinopecOrderResultModel sinopecEntrustOrderModelResult = objectMapper.readValue(result, SinopecOrderResultModel.class);
                    if(sinopecEntrustOrderModelResult!=null&&sinopecEntrustOrderModelResult.isSuccess()&&sinopecEntrustOrderModelResult.getData()!=null){
                        unitPrice = sinopecEntrustOrderModelResult.getData().getTransUnitPrice();
                    }
                }
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
        //生成需求单
        TDemandOrder demandOrder = new TDemandOrder();
        demandOrder.setDemandOrderCode(commonBiz.getBusinessTypeCode(BusinessCodeTypeEnum.DEMAND_ORDER_CODE, "", tmpEntrustOrder.getOperatorName()));
        demandOrder.setCustomerOrderCode(tmpEntrustOrder.getSapOrderNo());
        demandOrder.setPublishName(tmpEntrustOrder.getOperatorName());
        if (tmpEntrustOrder.getDispatchDate() != null) {
            demandOrder.setPublishTime(DateUtils.stringToDate(tmpEntrustOrder.getDispatchDate(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }else{
            demandOrder.setPublishTime(new Date());
        }
        demandOrder.setTicketTime(ticketTime);
        demandOrder.setCompanyEntrustId(companyEntrust.getId());
        demandOrder.setCompanyEntrustName(companyEntrustName);
        demandOrder.setCompanyCarrierId(companyCarrierId);
        demandOrder.setCompanyCarrierName(companyCarrierName);
        demandOrder.setCompanyCarrierLevel(companyCarrierLevel);
        demandOrder.setSettlementTonnage(companyEntrust.getSettlementTonnage());
        demandOrder.setGoodsAmount(goodsAmount);
        demandOrder.setNotArrangedAmount(goodsAmount);
        demandOrder.setGoodsUnit(GoodsUnitEnum.BY_WEIGHT.getKey());
        demandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
        demandOrder.setStatusUpdateTime(now);
        demandOrder.setStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
        if(unitPrice!=null){
            demandOrder.setExpectContractPrice(unitPrice);
            demandOrder.setExpectContractPriceType(ContractPriceTypeEnum.UNIT_PRICE.getKey());
        }
        demandOrder.setRemark(tmpEntrustOrder.getRemark());
        demandOrder.setSource(DemandOrderSourceEnum.SINOPEC_SYSTEM.getKey());
        demandOrder.setOrderType(DemandOrderOrderTypeEnum.PULL.getKey());
        commonBiz.setBaseEntityAdd(demandOrder, tmpEntrustOrder.getOperatorName());
        demandOrderMapper.insertSelectiveEncrypt(demandOrder);

        //生成需求单地址
        TDemandOrderAddress demandOrderAddress = new TDemandOrderAddress();
        demandOrderAddress.setDemandOrderId(demandOrder.getId());
        demandOrderAddress.setLoadProvinceId(tWarehouseAddress.getProvinceId());
        demandOrderAddress.setLoadProvinceName(tWarehouseAddress.getProvinceName());
        demandOrderAddress.setLoadCityId(tWarehouseAddress.getCityId());
        demandOrderAddress.setLoadCityName(tWarehouseAddress.getCityName());
        demandOrderAddress.setLoadAreaId(tWarehouseAddress.getAreaId());
        demandOrderAddress.setLoadAreaName(tWarehouseAddress.getAreaName());
        demandOrderAddress.setLoadWarehouse(tmpEntrustOrder.getOutWarehouseName());
        demandOrderAddress.setConsignorName("");
        demandOrderAddress.setConsignorMobile("");
        demandOrderAddress.setUnloadProvinceId(ConverterUtils.toLong(addressMap.get(EmailConstant.UNLOAD_PROVINCE_ID),0));
        demandOrderAddress.setUnloadProvinceName(addressMap.get(EmailConstant.UNLOAD_PROVINCE_NAME));
        demandOrderAddress.setUnloadCityId(ConverterUtils.toLong(addressMap.get(EmailConstant.UNLOAD_CITY_ID),0));
        demandOrderAddress.setUnloadCityName(addressMap.get(EmailConstant.UNLOAD_CITY_NAME));
        demandOrderAddress.setUnloadAreaId(ConverterUtils.toLong(addressMap.get(EmailConstant.UNLOAD_AREA_ID),0));
        demandOrderAddress.setUnloadAreaName(addressMap.get(EmailConstant.UNLOAD_AREA_NAME));
        demandOrderAddress.setUnloadDetailAddress(tmpEntrustOrder.getRemark());
        if(StringUtils.isNotBlank(tmpEntrustOrder.getRemark())){
            demandOrderAddress.setReceiverName(getContactName(tmpEntrustOrder.getRemark()));
            demandOrderAddress.setReceiverMobile(getMobile(tmpEntrustOrder.getRemark()));
        }
        demandOrderAddress.setExpectedUnloadTime(new Date(tmpEntrustOrder.getExpectArriveDate()));
        commonBiz.setBaseEntityAdd(demandOrderAddress, tmpEntrustOrder.getOperatorName());
        demandOrderAddressMapper.insertSelective(demandOrderAddress);
        addDemandOrderAddressList.add(demandOrderAddress);

        //生成货物
        TDemandOrderGoods demandOrderGoods = new TDemandOrderGoods();
        demandOrderGoods.setDemandOrderId(demandOrder.getId());
        demandOrderGoods.setGoodsName(tmpEntrustOrder.getMaterialName());
        demandOrderGoods.setGoodsSize("");
        demandOrderGoods.setGoodsAmount(goodsAmount);
        demandOrderGoods.setNotArrangedAmount(goodsAmount);
        commonBiz.setBaseEntityAdd(demandOrderGoods, tmpEntrustOrder.getOperatorName());

        //生成需求单事件
        TDemandOrderEvents demandOrderEvents = new TDemandOrderEvents();
        demandOrderEvents.setDemandOrderId(demandOrder.getId());
        demandOrderEvents.setCompanyCarrierId(demandOrder.getCompanyCarrierId());
        demandOrderEvents.setEvent(DemandOrderEventsTypeEnum.ACCEPT_CARRIERORDER.getKey());
        demandOrderEvents.setEventDesc(DemandOrderEventsTypeEnum.ACCEPT_CARRIERORDER.getValue());
        demandOrderEvents.setEventTime(now);
        demandOrderEvents.setOperatorName(tmpEntrustOrder.getOperatorName());
        demandOrderEvents.setOperateTime(now);
        commonBiz.setBaseEntityAdd(demandOrderEvents, tmpEntrustOrder.getOperatorName());

        //生成需求单日志
        TDemandOrderOperateLogs demandOrderOperateLogs = new TDemandOrderOperateLogs();
        demandOrderOperateLogs.setDemandOrderId(demandOrder.getId());
        demandOrderOperateLogs.setOperationType(DemandOrderOperateLogsEnum.CREATE_DEMAND_ORDER.getKey());
        demandOrderOperateLogs.setOperationContent(DemandOrderOperateLogsEnum.CREATE_DEMAND_ORDER.getValue());
        demandOrderOperateLogs.setOperatorName(tmpEntrustOrder.getOperatorName());
        demandOrderOperateLogs.setOperateTime(now);
        commonBiz.setBaseEntityAdd(demandOrderOperateLogs, tmpEntrustOrder.getOperatorName());

        demandOrderGoodsMapper.insertSelective(demandOrderGoods);
        demandOrderEventsMapper.insertSelective(demandOrderEvents);
        demandOrderOperateLogsMapper.insertSelective(demandOrderOperateLogs);

        //新增记录到需求单车主变更表
        TDemandOrderCarrier tDemandOrderCarrier = new TDemandOrderCarrier();
        tDemandOrderCarrier.setDemandOrderId(demandOrder.getId());
        tDemandOrderCarrier.setCompanyCarrierId(demandOrder.getCompanyCarrierId());
        tDemandOrderCarrier.setCarrierContactId(demandOrder.getCarrierContactId());
        tDemandOrderCarrier.setCarrierPrice(demandOrder.getCarrierPrice());
        tDemandOrderCarrier.setCarrierPriceType(demandOrder.getCarrierPriceType());
        commonBiz.setBaseEntityAdd(tDemandOrderCarrier, BaseContextHandler.getUserName());
        tDemandOrderCarrierMapper.insertSelective(tDemandOrderCarrier);

        //为需求单设置运价信息
        GetPriceByAddressAndAmountRequestModel requestFreightModel = new GetPriceByAddressAndAmountRequestModel();

        requestFreightModel.setFromAreaId(demandOrderAddress.getLoadAreaId());
        requestFreightModel.setFromCityId(demandOrderAddress.getLoadCityId());
        requestFreightModel.setFromProvinceId(demandOrderAddress.getLoadProvinceId());
        requestFreightModel.setFromWarehouse(demandOrderAddress.getLoadWarehouse());

        requestFreightModel.setToAreaId(demandOrderAddress.getUnloadAreaId());
        requestFreightModel.setToCityId(demandOrderAddress.getUnloadCityId());
        requestFreightModel.setToProvinceId(demandOrderAddress.getUnloadProvinceId());
        requestFreightModel.setToWarehouseDetail(demandOrderAddress.getUnloadDetailAddress());

        requestFreightModel.setGoodsAmount(demandOrder.getGoodsAmount());
        requestFreightModel.setGoodsUnit(demandOrder.getGoodsUnit());
        requestFreightModel.setCompanyEntrustId(demandOrder.getCompanyEntrustId());
        log.info("pull demand order input params：" +  requestFreightModel.toString());
        GetPriceByAddressAndAmountResponseModel dbEntrustInfo = freightBiz.getEntrustFreightInfo(requestFreightModel);
        if(dbEntrustInfo != null && dbEntrustInfo.getFreightFee()!=null){
            demandOrder.setContractPrice(dbEntrustInfo.getFreightFee());
            demandOrder.setContractPriceType(dbEntrustInfo.getFreightType());

            TDemandOrder upDemandOrder = new TDemandOrder();
            upDemandOrder.setId(demandOrder.getId());
            upDemandOrder.setContractPriceType(dbEntrustInfo.getFreightType());
            upDemandOrder.setContractPrice(dbEntrustInfo.getFreightFee());
            demandOrderMapper.updateByPrimaryKeySelectiveEncrypt(upDemandOrder);
        }
    }

    /**
     * 中石化推送需求单
     *
     * @param  requestModel
     */
    public void receiveDemand(ReceiveSinopecDemandRequestModel requestModel) {
        Object sn = redisUtils.get(CommonConstant.LOGISTICS_SINOPEC_SN + requestModel.getSn());
        if(sn!=null){
            log.error("=========中石化同步需求单========"+requestModel.getSn()+"单号已存在======");
            throw new BizException(EntrustDataExceptionEnum.SINOPEC_DEMANDORDER_EXIST);
        }
        TDemandOrder tDemandOrder = demandOrderMapper.getValidSinopecOrderByOrderNo(requestModel.getSn());
        if(tDemandOrder!=null){
            log.error("=========中石化同步需求单========"+requestModel.getSn()+"单号已存在======");
            throw new BizException(EntrustDataExceptionEnum.SINOPEC_DEMANDORDER_EXIST);
        }
        String operateName = requestModel.getSendOperator();
        if (StringUtils.isBlank(operateName)){
            operateName = CommonConstant.SINOPEC_OPERATOR;
        }
        //新增原始数据
        TSinopecOriginalData originalData = MapperUtils.mapperNoDefault(requestModel, TSinopecOriginalData.class);
        commonBiz.setBaseEntityAdd(originalData,operateName);
        tSinopecOriginalDataMapper.insertSelective(originalData);
        //新增数据日志
        addSinopecMessageLog(SinopecMessageLogTypeEnum.DEMANDORDER,SinopecMessageLogActionEnum.ADD, JSONObject.toJSONString(requestModel),requestModel.getIp(),operateName);

        //异步生成需求单
        if(StringUtils.isBlank(originalData.getSendOperator())){
            originalData.setSendOperator(CommonConstant.SINOPEC_OPERATOR);
        }
        AsyncProcessQueue.execute(() -> createDemandOrderFromOriginalData(originalData));
        redisUtils.set(CommonConstant.LOGISTICS_SINOPEC_SN + originalData.getSn(), originalData.getSn(),60*5L);
    }
    /**
     * 从原始数据中尝试创建需求单
     *
     * @param  originalData
     */
    @Transactional
    public void createDemandOrderFromOriginalData(TSinopecOriginalData originalData) {
        Date now=new Date();
        String operatorName=originalData.getSendOperator();
        //获取公司信息
        String companyEntrustName = "中国石化化工销售有限公司"+originalData.getConsignName();
        TCompanyEntrust companyEntrust  = companyEntrustMapper.getByName(companyEntrustName);
        if (companyEntrust == null) {
            BaseContextHandler.setUserName(operatorName);
            companyEntrust = addEntrustCompany(companyEntrustName);
        }
        String ticketTimeStr = null;
        Date ticketTime = originalData.getExpectExeDate();
        if(ticketTime!=null){
            ticketTimeStr = DateUtils.dateToString(ticketTime, DateUtils.DATE_TO_STRING_SHORT_PATTERN);
        }
        //查询公司仓库
        TWarehouseAddress tWarehouseAddress = tWarehouseAddressMapper.getValidWarehouseByNameForSinoper(companyEntrustName, originalData.getOutWarehouseName(), ticketTimeStr);

        //货物数量
        BigDecimal goodsAmount = originalData.getQty();

        //从remark中解析地址信息
        Map<String, String> addressMap = new HashMap<>();
        try{
            addressMap = commonBiz.getProvinceCityArea(getAddress(originalData.getRemark()));
            if(MapUtils.isEmpty(addressMap)){
                addressMap = commonBiz.getProvinceCityArea(originalData.getRecvDistrictName());
            }
        } catch (Exception e){
            log.info(e.getMessage(),e);
        }
        //生成中石化需求单
        TDemandOrder demandOrder = new TDemandOrder();
        demandOrder.setDemandOrderCode(commonBiz.getBusinessTypeCode(BusinessCodeTypeEnum.DEMAND_ORDER_CODE, "", operatorName));
        demandOrder.setCustomerOrderCode(originalData.getSn());
        demandOrder.setPublishName(operatorName);
        demandOrder.setPublishTime(now);
        demandOrder.setTicketTime(ticketTime);
        demandOrder.setCompanyEntrustId(companyEntrust.getId());
        demandOrder.setCompanyEntrustName(companyEntrustName);
        demandOrder.setSettlementTonnage(companyEntrust.getSettlementTonnage());
        demandOrder.setGoodsAmount(goodsAmount);
        demandOrder.setNotArrangedAmount(goodsAmount);
        demandOrder.setGoodsUnit(GoodsUnitEnum.BY_WEIGHT.getKey());
        demandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_PUBLISH.getKey());
        demandOrder.setStatusUpdateTime(now);
        demandOrder.setStatus(DemandOrderStatusEnum.WAIT_PUBLISH.getKey());
        demandOrder.setRemark(originalData.getRemark());
        demandOrder.setSource(DemandOrderSourceEnum.SINOPEC_SYSTEM.getKey());
        demandOrder.setOrderType(DemandOrderOrderTypeEnum.PUSH.getKey());
        demandOrder.setSinopecOrderNo(originalData.getOrderNo());
        demandOrder.setManufacturerName(originalData.getManufacturerName());
        demandOrder.setItemTransGroupName(originalData.getItemTransGroupName());
        demandOrder.setItemPackSpecName(originalData.getItemPackSpecName());
        commonBiz.setBaseEntityAdd(demandOrder, operatorName);


        //生成中石化需求单地址
        TDemandOrderAddress demandOrderAddress = new TDemandOrderAddress();
        demandOrderAddress.setDemandOrderId(demandOrder.getId());
        if(tWarehouseAddress!=null) {
            demandOrderAddress.setLoadProvinceId(tWarehouseAddress.getProvinceId());
            demandOrderAddress.setLoadProvinceName(tWarehouseAddress.getProvinceName());
            demandOrderAddress.setLoadCityId(tWarehouseAddress.getCityId());
            demandOrderAddress.setLoadCityName(tWarehouseAddress.getCityName());
            demandOrderAddress.setLoadAreaId(tWarehouseAddress.getAreaId());
            demandOrderAddress.setLoadAreaName(tWarehouseAddress.getAreaName());
        }
        demandOrderAddress.setLoadWarehouse(originalData.getOutWarehouseName());
        demandOrderAddress.setUnloadProvinceId(ConverterUtils.toLong(addressMap.get(EmailConstant.UNLOAD_PROVINCE_ID),0));
        demandOrderAddress.setUnloadProvinceName(addressMap.get(EmailConstant.UNLOAD_PROVINCE_NAME));
        demandOrderAddress.setUnloadCityId(ConverterUtils.toLong(addressMap.get(EmailConstant.UNLOAD_CITY_ID),0));
        demandOrderAddress.setUnloadCityName(addressMap.get(EmailConstant.UNLOAD_CITY_NAME));
        demandOrderAddress.setUnloadAreaId(ConverterUtils.toLong(addressMap.get(EmailConstant.UNLOAD_AREA_ID),0));
        demandOrderAddress.setUnloadAreaName(addressMap.get(EmailConstant.UNLOAD_AREA_NAME));
        demandOrderAddress.setUnloadDetailAddress(originalData.getRemark());
        if(StringUtils.isNotBlank(originalData.getRemark())){
            demandOrderAddress.setReceiverName(getContactName(originalData.getRemark()));
            demandOrderAddress.setReceiverMobile(getMobile(originalData.getRemark()));
        }
        demandOrderAddress.setExpectedUnloadTime(null);
        demandOrderAddress.setExpectedLoadTime(originalData.getInvalidatingStartDate()); //必须提货时间
        commonBiz.setBaseEntityAdd(demandOrderAddress, operatorName);

        //生成货物
        TDemandOrderGoods demandOrderGoods = new TDemandOrderGoods();
        //货物名称： 大类+小类+产品名
        demandOrderGoods.setGoodsName(originalData.getItemCategory1Name()+originalData.getItemCategoryName()+originalData.getItemName());
        demandOrderGoods.setGoodsAmount(goodsAmount);
        demandOrderGoods.setNotArrangedAmount(goodsAmount);
        commonBiz.setBaseEntityAdd(demandOrderGoods, operatorName);

        //生成需求单日志
        TDemandOrderOperateLogs demandOrderOperateLogs = demandOrderCommonBiz.getDemandOrderOperateLogs(null, DemandOrderOperateLogsEnum.CREATE_DEMAND_ORDER, operatorName, "");


        demandOrderMapper.insertSelectiveEncrypt(demandOrder);
        //回填数据id
        demandOrderAddress.setDemandOrderId(demandOrder.getId());
        demandOrderGoods.setDemandOrderId(demandOrder.getId());
        demandOrderOperateLogs.setDemandOrderId(demandOrder.getId());

        demandOrderAddressMapper.insertSelective(demandOrderAddress);
        demandOrderGoodsMapper.insertSelective(demandOrderGoods);
        demandOrderOperateLogsMapper.insertSelective(demandOrderOperateLogs);

        //生成需求单成功->更新源数据
        log.info("========中石化推送需求单--生成需求单成功=========客户单号"+demandOrder.getCustomerOrderCode());
        TSinopecOriginalData upSinopecOriginalData=new TSinopecOriginalData();
        upSinopecOriginalData.setId(originalData.getId());
        upSinopecOriginalData.setIfHasDemandOrder(CommonConstant.INTEGER_ONE);
        commonBiz.setBaseEntityModify(upSinopecOriginalData,operatorName);
        tSinopecOriginalDataMapper.updateByPrimaryKeySelective(upSinopecOriginalData);
    }

    /**
     *
     * 中石化主动取消需求单
     *
     * @param  requestModel
     */
    public void cancelDemand(CancelSinopecDemandRequestModel requestModel) {
        if(StringUtils.isBlank(requestModel.getSendOperator())){
            requestModel.setSendOperator(CommonConstant.SINOPEC_OPERATOR);
        }
        TDemandOrder tDemandOrder = demandOrderMapper.getValidSinopecOrderByOrderNo(requestModel.getSn());
        if(tDemandOrder==null){
            log.error("==============中石化取消需求单失败:需求单不存在或已失效-客户单号:"+requestModel.getSn()+"==============");
            throw new BizException(EntrustDataExceptionEnum.SINOPEC_DEMANDORDER_NOT_EXIST);
        }
        if (CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfObjectionSinopec())){
            log.error("==============中石化取消需求单失败:当前需求单异常，不能取消-客户单号:"+requestModel.getSn()+"==============");
            throw new BizException(EntrustDataExceptionEnum.SINOPEC_CANCEL_DEMAND_ORDER_IS_OBJECTION);
        }
        redisUtils.delete(CommonConstant.LOGISTICS_SINOPEC_SN + requestModel.getSn());
        //插入日志
        addSinopecMessageLog(SinopecMessageLogTypeEnum.DEMANDORDER,SinopecMessageLogActionEnum.CANCLE,JSONObject.toJSONString(requestModel),requestModel.getIp(),requestModel.getSendOperator());
        //异步取消需求单
        AsyncProcessQueue.execute(() -> sinopecCancelDemand(requestModel,tDemandOrder));
    }
    /**
     * 中石化主动取消需求单
     *
     * @param  requestModel
     */
    @Transactional
    public void sinopecCancelDemand(CancelSinopecDemandRequestModel requestModel,TDemandOrder tDemandOrder) {
        Date now=new Date();
        //需求单状态变更
        TDemandOrder upDemandOrder=new TDemandOrder();
        upDemandOrder.setId(tDemandOrder.getId());
        upDemandOrder.setIfCancel(CommonConstant.INTEGER_ONE);
        upDemandOrder.setCancelType(CommonConstant.INTEGER_ONE);
        upDemandOrder.setCancelTime(now);
        commonBiz.setBaseEntityModify(upDemandOrder,requestModel.getSendOperator());

        //新增取消需求单操作日志
        TDemandOrderOperateLogs logs = demandOrderCommonBiz.getDemandOrderOperateLogs(tDemandOrder.getId(), DemandOrderOperateLogsEnum.CANCEL_DEMAND_ORDER, requestModel.getSendOperator(), DemandOrderCancelTypeEnum.getEnum(upDemandOrder.getCancelType()).getValue());

        log.info("================中石化取消需求单,客户单号:"+requestModel.getSn()+"，需求单号:"+tDemandOrder.getDemandOrderCode()+"===================");
        //新增取消需求单事件
        TDemandOrderEvents event = demandOrderCommonBiz.generateEvent(tDemandOrder.getId(), tDemandOrder.getCompanyCarrierId(), DemandOrderEventsTypeEnum.CANCEL_DEMANDORDER, requestModel.getSendOperator());
        demandOrderMapper.updateByPrimaryKeySelectiveEncrypt(upDemandOrder);
        demandOrderOperateLogsMapper.insertSelective(logs);
        demandOrderEventsMapper.insertSelective(event);
        //已调度需求单，则取消需求单下的运单,并根据运单状态设置需求单是否异常
        if(tDemandOrder.getEntrustStatus()>DemandOrderStatusEnum.WAIT_DISPATCH.getKey()){
            cancelSinopecCarrierOrder(tDemandOrder.getId(),requestModel.getSendOperator(),upDemandOrder);
        }
        demandOrderMapper.updateByPrimaryKeySelectiveEncrypt(upDemandOrder);

    }

    /**
     * 取消运单
     *
     * @param  demandOrderId 需求单id
  * @param  sendOperator 操作人
     */
    private void cancelSinopecCarrierOrder(Long demandOrderId, String sendOperator,TDemandOrder upDemandOrder) {
        List<TCarrierOrder> carrierOrders = tCarrierOrderMapper.getNotCancelByDemandOrderId(demandOrderId);
        if(ListUtils.isNotEmpty(carrierOrders)){
            List<TCarrierOrder> upCarrierOrderList=new ArrayList<>();
            List<TCarrierOrderOperateLogs> logs=new ArrayList<>();
            List<TCarrierOrderEvents> events=new ArrayList<>();
            Date now = new Date();
            boolean ifObjection=false;
            for (TCarrierOrder carrierOrder : carrierOrders) {
                //取消运单
                TCarrierOrder upOrder = new TCarrierOrder();
                upOrder.setId(carrierOrder.getId());
                upOrder.setCancelOperatorName(sendOperator);
                upOrder.setCancelTime(now);
                if(carrierOrder.getStatus()>CarrierOrderStatusEnum.WAIT_LOAD.getKey()){
                    ifObjection=true;
                }
                upOrder.setIfCancel(CommonConstant.INTEGER_ONE);
                commonBiz.setBaseEntityModify(upOrder, sendOperator);
                upCarrierOrderList.add(upOrder);
                //插入取消日志
                TCarrierOrderOperateLogs carrierOrderOperateLogs = new TCarrierOrderOperateLogs();
                carrierOrderOperateLogs.setCarrierOrderId(carrierOrder.getId());
                carrierOrderOperateLogs.setOperationType(CarrierOrderOperateLogsTypeEnum.CANCEL_CARRIER_ORDER.getKey());
                carrierOrderOperateLogs.setOperationContent(CarrierOrderOperateLogsTypeEnum.CANCEL_CARRIER_ORDER.getValue());
                carrierOrderOperateLogs.setOperatorName(sendOperator);
                carrierOrderOperateLogs.setOperateTime(now);
                commonBiz.setBaseEntityAdd(carrierOrderOperateLogs, sendOperator);
                logs.add(carrierOrderOperateLogs);
                //插入取消事件
                TCarrierOrderEvents newEvent = new TCarrierOrderEvents();
                newEvent.setEvent(CarrierOrderEventsTypeEnum.HAS_CANCELED.getKey());
                newEvent.setEventDesc(CarrierOrderEventsTypeEnum.HAS_CANCELED.getValue());
                newEvent.setRemark(CarrierOrderEventsTypeEnum.HAS_CANCELED.getFormat());
                newEvent.setEventTime(now);
                newEvent.setCarrierOrderId(carrierOrder.getId());
                newEvent.setOperateTime(now);
                newEvent.setOperatorName(sendOperator);
                commonBiz.setBaseEntityAdd(newEvent, sendOperator);
                events.add(newEvent);
            }
            //存在已提货运单，设置异常
            if(ifObjection){
                upCarrierOrderList.stream().forEach(item->item.setIfObjection(CommonConstant.INTEGER_ONE));
                upDemandOrder.setIfObjection(CommonConstant.INTEGER_ONE);
            }

            log.info("================中石化取消需求单--取消运单，需求单id:"+demandOrderId+",运单信息:"+upCarrierOrderList+"===================");
            tCarrierOrderMapper.batchUpdateCarrierOrders(upCarrierOrderList);
            tCarrierOrderOperateLogsMapper.batchInsertSelective(logs);
            tCarrierOrderEventsMapper.batchInsertSelective(events);
        }
    }

    /**
     * 新增中石化接口日志
     *
     * @param  typeEnum  类型枚举
    * @param  actionEnum  动作枚举
    * @param  messageBody  原始入参信息
    * @param  ip   ip
     */
    private void addSinopecMessageLog(SinopecMessageLogTypeEnum typeEnum, SinopecMessageLogActionEnum actionEnum, String messageBody,String ip,String operatorName){
        TSinopecMessageLog log=new TSinopecMessageLog();
        log.setMessageType(typeEnum.getKey());
        log.setMessageAction(actionEnum.getKey());
        log.setMessageBody(messageBody);
        log.setMessageIp(ip);
        commonBiz.setBaseEntityAdd(log,operatorName);
        tSinopecMessageLogMapper.insertSelective(log);
    }

    public String getAddress(String remark){
        if(StringUtils.isBlank(remark)){
            return "";
        }
        StringBuilder address = new StringBuilder();
        String[] strs = remark.split("[\\pP\\s]");
        for(String tmp:strs){
            if(tmp.contains("省")
                    ||tmp.contains("市")
                    ||tmp.contains("区")
                    ||tmp.contains("县")
                    ||tmp.contains("大道")
                    ||tmp.contains("镇")
                    ||tmp.contains("村")
                    ||tmp.contains("路")
                    ||tmp.contains("经济开发区")
                    ||tmp.contains("号")){
                address.append(tmp);
            }
        }
        return address.toString();
    }

    public String getContactName(String remark) {
        String contactName = "";
        String regex = "(联系人|姓名|电话|联系电话|收货人)[^\\u4E00-\\u9FA5]+([\\u4E00-\\u9FA5]*)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(remark);
        while (matcher.find()) {
            contactName = matcher.group(matcher.groupCount());
            if (StringUtils.isNotBlank(contactName))
                break;
        }
        if (StringUtils.isBlank(contactName)) {
            String regexPos = "[\\u4E00-\\u9FA5]{1}(总|经理|老板|小姐|先生|主任)";
            Matcher matcherPos = Pattern.compile(regexPos).matcher(remark);
            while (matcherPos.find()) {
                contactName = matcherPos.group();
                if (StringUtils.isNotBlank(contactName))
                    break;
            }
        }
        if (StringUtils.isBlank(contactName)) {
            String nameRegex = "[^\\u4E00-\\u9FA5]+([王|刑|李|张|刘|陈|杨|黄|赵|吴|周|徐|孙|马|朱|胡|郭|何|高|林|罗|郑|梁|谢|宋|唐|许|韩|冯|邓|曹|彭|曾|肖|田|董|袁|潘|于|蒋|蔡|余|杜|叶|程|苏|魏|吕|丁|任|沈|姚|卢|姜|崔|钟|谭|陆|汪|范|金|石|廖|贾|夏|韦|傅|方|白|邹|孟|熊|秦|邱|江|尹|薛|闫|段|雷|侯|龙|史|黎|贺|顾|毛|郝|龚|邵|万|钱|覃|武|戴|孔|汤|庞|樊|兰|殷|施|陶|洪|翟|安|颜|倪|严|牛|温|芦|季|俞|章|鲁|葛|伍|申|尤|毕|聂|柴|焦|向|柳|邢|岳|齐|沿|梅|莫|庄|辛|管|祝|左|涂|谷|祁|时|舒|耿|牟|卜|路|詹|关|苗|凌|费|纪|靳|盛|童|欧|甄|项|曲|成|游|阳|裴|席|卫|查|屈|鲍|位|覃|霍|翁|隋|植|甘|景|薄|单|包|司|柏|宁|柯|阮|桂|闵|欧阳|解|强|丛|华|车|冉|房|边|辜|吉|饶|刁|瞿|戚|丘|古|米|池|滕|晋|苑|邬|臧|畅|宫|来|嵺|苟|全|褚|廉|简|娄|盖|符|奚|木|穆|党|燕|郎|邸|冀|谈|姬|屠|连|郜|晏|栾|郁|商|蒙|计|喻|揭|窦|迟|宇|敖|糜|鄢|冷|卓|花|艾|蓝|都|巩|稽|井|练|仲|乐|虞|卞|封|竺|冼|原|官|衣|楚|佟|栗|匡|宗|应|台|巫|鞠|僧|桑|荆|谌|银|扬|明|沙|薄|伏|岑|习|胥|保|和|蔺|水|云|昌|凤|酆|常|皮|康|元|平|萧|湛|禹|无|贝|茅|麻|危|骆|支|咎|经|裘|缪|干|宣|贲|杭|诸|钮|嵇|滑|荣|荀|羊|於|惠|家|芮|羿|储|汲|邴|松|富|乌|巴|弓|牧|隗|山|宓|蓬|郗|班|仰|秋|伊|仇|暴|钭|厉|戎|祖|束|幸|韶|蓟|印|宿|怀|蒲|鄂|索|咸|籍|赖|乔|阴|能|苍|双|闻|莘|贡|逢|扶|堵|宰|郦|雍|却|璩|濮|寿|通|扈|郏|浦|尚|农|别|阎|充|慕|茹|宦|鱼|容|易|慎|戈|庚|终|暨|居|衡|步|满|弘|国|文|寇|广|禄|阙|东|殴|殳|沃|利|蔚|越|夔|隆|师|厍|晃|勾|融|訾|阚|那|空|毋|乜|养|须|丰|巢|蒯|相|后|红|权逯|盖益|桓|公|万俟|司马|上官|夏侯|诸葛|闻人|东方|赫连|皇甫|尉迟|公羊|澹台|公冶|宗政|濮阳|淳于|单于|太叔|申屠|公孙|仲孙|轩辕|令狐|钟离|宇文|长孙|慕容|鲜于|闾丘|司徒|司空|亓官|司寇|仉|督|子车|颛孙|端木|巫马|公西|漆雕|乐正|壤驷|公良|拓跋|夹谷|宰父|谷粱|法|汝|钦|段干|百里|东郭|南门|呼延|归海|羊舌|微生|帅|缑|亢|况|郈|琴|梁丘|左丘|东门|西门|佘|佴|伯|赏|南宫|墨|哈|谯|笪|年|爱|仝|代][\\u4E00-\\u9FA5]{0,2})[^\\u4E00-\\u9FA5]+";
            Matcher matcherPos = Pattern.compile(nameRegex).matcher(remark);
            while (matcherPos.find()) {
                contactName = matcherPos.group(matcherPos.groupCount());
                if (StringUtils.isNotBlank(contactName))
                    break;
            }
        }
        return contactName;
    }

    public TCompanyEntrust addEntrustCompany(String entrustCompanyName){
        TCompany company = new TCompany();
        company.setCompanyName(entrustCompanyName);
        company.setTradingCertificateIsAmend(CommonConstant.INTEGER_ONE);
        commonBiz.setBaseEntityAdd(company, BaseContextHandler.getUserName());
        companyMapper.insertSelective(company);
        TCompanyEntrust newCompanyEntrust = new TCompanyEntrust();
        newCompanyEntrust.setSettlementTonnage(SettlementTonnageEnum.SIGN.getKey());
        newCompanyEntrust.setType(CompanyQualifiedTypeEnum.COMPANY.getKey());
        newCompanyEntrust.setCompanyId(company.getId());
        commonBiz.setBaseEntityAdd(newCompanyEntrust,BaseContextHandler.getUserName());
        tCompanyEntrustMapper.insertSelective(newCompanyEntrust);
        return tCompanyEntrustMapper.selectByPrimaryKey(newCompanyEntrust.getId());
    }
    public String getMobile(String remark) {
        String contactPhone = RegExpValidatorUtil.matchedStr(RegExpValidatorUtil.REG_MOBILE, remark);
        if (StringUtils.isBlank(contactPhone)) {
            contactPhone = RegExpValidatorUtil.matchedStr(RegExpValidatorUtil.REG_TEL, remark);
        }
        return contactPhone;
    }

    /**
     * 取消中石化需求单
     * @param requestModel
     */
    @Transactional
    public void cancelSinopecDemandOrder(SinopecDemandOrderCancelRequestModel requestModel) {
        List<TDemandOrder> dbDemandOrderList = demandOrderMapper.getByIds(requestModel.getDemandIds());
        if (ListUtils.isEmpty(dbDemandOrderList)){
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }

        Date now = new Date();
        TDemandOrder upDemandOrder;
        List<TDemandOrder> upDemandOrderList = new ArrayList<>();
        TDemandOrderOperateLogs demandOrderOperateLogs;
        List<TDemandOrderOperateLogs> logList = new ArrayList<>();
        TDemandOrderEvents demandOrderEvents;
        List<TDemandOrderEvents> eventsList = new ArrayList<>();
        SinopecRefuseConsignOrderRequestModel refuseConsignOrderModel;
        List<SinopecRefuseConsignOrderRequestModel> refuseConsignOrderModelList = new ArrayList<>();
        Map<String, Long> snOrderIdMap = new HashMap<>();//中石化委托单号-》需求单id
        for (TDemandOrder tDemandOrder : dbDemandOrderList) {
            //非中石化推送的需求单不走此取消方法
            if (!DemandOrderSourceEnum.SINOPEC_SYSTEM.getKey().equals(tDemandOrder.getSource()) || !DemandOrderOrderTypeEnum.PUSH.getKey().equals(tDemandOrder.getOrderType())) {
                throw new BizException(EntrustDataExceptionEnum.REQUEST_PARAM_ERROR);
            }
            //判断需求单状态
            if (CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfCancel())) {
                throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_CANCEL.getCode(), tDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMANDORDER_IS_CANCEL.getMsg());
            }
            //已放空
            if (CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfEmpty())) {
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getCode(), tDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getMsg());
            }
            //已经调度完了
            if (tDemandOrder.getEntrustStatus() > DemandOrderStatusEnum.WAIT_DISPATCH.getKey()) {
                throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_NOT_CANCEL.getCode(), tDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMANDORDER_NOT_CANCEL.getMsg());
            }
            //有异常
            if (CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfObjectionSinopec())) {
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_OBJECTION_SINOPEC.getCode(), tDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_OBJECTION_SINOPEC.getMsg());
            }

            //更新需求单
            upDemandOrder = new TDemandOrder();
            upDemandOrder.setId(tDemandOrder.getId());
            upDemandOrder.setIfCancel(CommonConstant.INTEGER_ONE);
            upDemandOrder.setCancelReason(requestModel.getCancelReason());
            upDemandOrder.setCancelTime(now);
            upDemandOrder.setDispatcherName(requestModel.getDispatcherName());
            upDemandOrder.setDispatcherPhone(requestModel.getDispatcherPhone());
            commonBiz.setBaseEntityModify(upDemandOrder, BaseContextHandler.getUserName());
            upDemandOrderList.add(upDemandOrder);

            //生成需求单事件
            demandOrderEvents = new TDemandOrderEvents();
            demandOrderEvents.setDemandOrderId(tDemandOrder.getId());
            demandOrderEvents.setCompanyCarrierId(tDemandOrder.getCompanyCarrierId());
            demandOrderEvents.setEvent(DemandOrderEventsTypeEnum.CANCEL_DEMANDORDER.getKey());
            demandOrderEvents.setEventDesc(DemandOrderEventsTypeEnum.CANCEL_DEMANDORDER.getValue());
            demandOrderEvents.setEventTime(now);
            demandOrderEvents.setOperatorName(BaseContextHandler.getUserName());
            demandOrderEvents.setOperateTime(now);
            commonBiz.setBaseEntityAdd(demandOrderEvents, BaseContextHandler.getUserName());
            eventsList.add(demandOrderEvents);

            //生成需求单日志
            demandOrderOperateLogs = new TDemandOrderOperateLogs();
            demandOrderOperateLogs.setDemandOrderId(tDemandOrder.getId());
            demandOrderOperateLogs.setOperationType(DemandOrderOperateLogsEnum.CANCEL_DEMAND_ORDER.getKey());
            demandOrderOperateLogs.setOperationContent(DemandOrderOperateLogsEnum.CANCEL_DEMAND_ORDER.getValue());
            // 获取原因类型
            String reasonType = SinopecRefuseReasonType.getValueByKey(requestModel.getCancelReasonType());
            demandOrderOperateLogs.setRemark("【"+requestModel.getDispatcherName()+"&"+requestModel.getDispatcherPhone()+"】"+ reasonType + " : " + requestModel.getCancelReason());
            demandOrderOperateLogs.setOperatorName(BaseContextHandler.getUserName());
            demandOrderOperateLogs.setOperateTime(now);
            commonBiz.setBaseEntityAdd(demandOrderOperateLogs, BaseContextHandler.getUserName());
            logList.add(demandOrderOperateLogs);

            //同步撤销中石化委托单
            refuseConsignOrderModel = new SinopecRefuseConsignOrderRequestModel();
            refuseConsignOrderModel.setOrderNo(tDemandOrder.getSinopecOrderNo());
            refuseConsignOrderModel.setSn(tDemandOrder.getCustomerOrderCode());
            refuseConsignOrderModel.setReasons(requestModel.getCancelReason());
            refuseConsignOrderModel.setContact(requestModel.getDispatcherName());
            refuseConsignOrderModel.setPhoneNo(requestModel.getDispatcherPhone());
            refuseConsignOrderModel.setOperator(BaseContextHandler.getUserName());

            // 填充租户信息和拒绝类型
            refuseConsignOrderModel.setRefuseReasonType(requestModel.getCancelReasonType());
            refuseConsignOrderModel.setCustomerId(tDemandOrder.getSinopecCustomerId());
            refuseConsignOrderModel.setCustomerName(tDemandOrder.getSinopecCustomerName());
            refuseConsignOrderModelList.add(refuseConsignOrderModel);

            snOrderIdMap.put(tDemandOrder.getCustomerOrderCode(), tDemandOrder.getId());
        }
        if (ListUtils.isNotEmpty(upDemandOrderList)) {
            demandOrderMapper.batchUpdateByPrimaryKeySelective(upDemandOrderList);
        }
        if (ListUtils.isNotEmpty(logList)) {
            demandOrderOperateLogsMapper.batchInsertSelective(logList);
        }
        if (ListUtils.isNotEmpty(eventsList)) {
            demandOrderEventsMapper.batchInsertSelective(eventsList);
        }

        //异步撤销中石化委托单
        if (ListUtils.isNotEmpty(refuseConsignOrderModelList)) {
            AsyncProcessQueue.execute(() -> this.synSinopecRefuseConsignOrder(refuseConsignOrderModelList, snOrderIdMap));
        }
    }

    //异步撤销中石化委托单
    private void synSinopecRefuseConsignOrder(List<SinopecRefuseConsignOrderRequestModel> refuseConsignOrderModelList, Map<String, Long> snOrderIdMap){
        //判断调用中石化接口开关是否打开
        String sysConfig = configBiz.getSysConfig(ConfigKeyEnum.SINOPEC_METHOD_SWITCH).orElse(CommonConstant.ZERO);
        if (CommonConstant.ZERO.equals(sysConfig)) {
            log.info("调用中石化接口，无开关配置或开关关闭无需调用！");
            return;
        }
        List<Long> rollbackDemandIdList = new ArrayList<>();//需回滚取消操作的需求单id
        for (SinopecRefuseConsignOrderRequestModel model : refuseConsignOrderModelList) {
            boolean ifCancelError = false;
            String msg = null;
            try {
                //请求中石化接口V1
                String result = sinopecBiz.refuseConsignOrderV1(model);
                if (StringUtils.isBlank(result)){
                    ifCancelError = true;
                }else{
                    SinopecResultModel resultModel = objectMapper.readValue(result, SinopecResultModel.class);
                    if (resultModel == null || !CommonConstant.ZERO.equals(resultModel.getType())){
                        ifCancelError = true;
                        if (resultModel != null){
                            msg = resultModel.getMessage();
                        }
                    }
                }
            }catch (Exception e){
                ifCancelError = true;
                msg = e.getMessage();
            }
            //取消中石化失败，则回滚
            if (ifCancelError){
                log.warn("取消中石化需求单-》同步取消中石化系统失败。中石化委托单号（客户单号）："+model.getSn()+"，"+msg);
                rollbackDemandIdList.add(snOrderIdMap.get(model.getSn()));
            }
        }
        //取消中石化委托单失败，则需求单回退
        if (ListUtils.isNotEmpty(rollbackDemandIdList)){
            demandOrderMapper.rollBackSinopecDemandOrderByCancel(StringUtils.listToString(rollbackDemandIdList,','));
        }
    }

    /**
     * 获取中石化需求发布详情
     *
     * @param requestModel 单个需求单id
     * @return 需求单发布详情
     */
    public PublishSinopecResponseModel publishSinopecDetail(PublishSinopecDetailRequestModel requestModel) {
        return publishSinopecBaseCheck(requestModel.getDemandId());
    }

    /**
     * 批量获取中石化需求单发布详情
     *
     * @param requestModel 多个需求单id
     * @return 需求单发布详情
     */
    public BatchPublishSinopecResponseModel batchPublishSinopecDetail(BatchPublishSinopecDetailRequestModel requestModel) {
        List<PublishSinopecResponseModel> publishDetails = searchSinopecDemandOrderDetail(requestModel.getDemandIds());
        //批量查询到的需求单进行二次筛选
        BatchPublishSinopecResponseModel responseModel = new BatchPublishSinopecResponseModel();

        //总需求单数量
        Integer demandOrderCount = CommonConstant.INTEGER_ZERO;
        //总货物数量
        BigDecimal goodsAmountCount = CommonConstant.BIG_DECIMAL_ZERO;
        //需求单发布详情列表集合
        List<BatchPublishSinopecDetailResponseModel> demandOrderList = new ArrayList<>();
        //拼接数据
        for (PublishSinopecResponseModel publishDetail : publishDetails) {

            demandOrderCount++;
            goodsAmountCount = goodsAmountCount.add(publishDetail.getGoodsAmount());
            demandOrderList.add(MapperUtils.mapper(publishDetail, BatchPublishSinopecDetailResponseModel.class));
        }

        //设置响应内容
        return responseModel
                .setCompanyEntrustName(publishDetails.get(CommonConstant.INTEGER_ZERO).getCompanyEntrustName())
                .setDemandOrderCount(demandOrderCount)
                .setGoodsAmountCount(goodsAmountCount)
                .setSinopecDemands(demandOrderList);
    }

    /**
     * 中石化需求单发布 需求单状态基础校验
     *
     * @param demandOrderId 需求单id
     * @return 需求单发布详情
     */
    private PublishSinopecResponseModel publishSinopecBaseCheck(Long demandOrderId) {
        List<PublishSinopecResponseModel> detailList = demandOrderMapper.sinopecPublishDetail(Collections.singletonList(demandOrderId));

        if (ListUtils.isEmpty(detailList)) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }

        if (detailList.size() > CommonConstant.INTEGER_ONE) {
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_ONLY_ONE);
        }

        PublishSinopecResponseModel responseModel = detailList.get(CommonConstant.INTEGER_ZERO);

        //只能是中石化推送的单子
        if (!DemandOrderSourceEnum.SINOPEC_SYSTEM.getKey().equals(responseModel.getSource()) || !DemandOrderOrderTypeEnum.PUSH.getKey().equals(responseModel.getOrderType())) {
            throw new BizException(EntrustDataExceptionEnum.OPERATION_DEMAND_ORDER_ONLY_SINOPEC.getCode(), responseModel.getDemandOrderCode() + EntrustDataExceptionEnum.OPERATION_DEMAND_ORDER_ONLY_SINOPEC.getMsg());

            //只能是待发布的
        } else if (!DemandOrderStatusEnum.WAIT_PUBLISH.getKey().equals(responseModel.getEntrustStatus())) {
            throw new BizException(EntrustDataExceptionEnum.COMPANY_CARRIER_PUBLISH_STATUS_ERROR.getCode(), responseModel.getDemandOrderCode() + EntrustDataExceptionEnum.COMPANY_CARRIER_PUBLISH_STATUS_ERROR.getMsg());

            //只能是未取消的
        } else if (CommonConstant.INTEGER_ONE.equals(responseModel.getIfCancel())) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_CANCEL.getCode(), responseModel.getDemandOrderCode() + EntrustDataExceptionEnum.DEMANDORDER_CANCEL.getMsg());

            //只能是未放空的
        } else if (CommonConstant.INTEGER_ONE.equals(responseModel.getIfEmpty())) {
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getCode(), responseModel.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getMsg());

            //有异常的
        } else if (CommonConstant.INTEGER_ONE.equals(responseModel.getIfObjectionSinopec())) {
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_OBJECTION_SINOPEC.getCode(), responseModel.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_OBJECTION_SINOPEC.getMsg());

            //非网货的
        } else if (!CommonConstant.INTEGER_ONE.equals(responseModel.getSinopecOnlineGoodsFlag())) {
            throw new BizException(EntrustDataExceptionEnum.OPERATION_DEMAND_ORDER_ONLY_SINOPEC_ONLINE.getCode(), responseModel.getDemandOrderCode() + EntrustDataExceptionEnum.OPERATION_DEMAND_ORDER_ONLY_SINOPEC_ONLINE.getMsg());
        }

        return responseModel;
    }

    /**
     * 单个发布中石化需求单
     *
     * @param requestModel 要发布的需求单信息
     */
    @Transactional
    public void publishSinopecDemandOrder(PublishSinopecDemandRequestModel requestModel) {

        //校验限制条件并获取需求单发布详情信息
        PublishSinopecResponseModel publishDetailModel = publishSinopecBaseCheck(requestModel.getDemandOrderId());

        //查询车主信息
        CompanyCarrierByIdModel companyCarrierByIdModel = demandOrderCommonBiz.companyCarrierInfoByIsOurCompany(requestModel.getIsOurCompany(), requestModel.getCompanyCarrierId());

        Date systemDate = new Date();
        //当前登陆人name
        String userName = BaseContextHandler.getUserName();

        //更新需求单表信息
        TDemandOrder tDemandOrder = new TDemandOrder();
        tDemandOrder.setId(publishDetailModel.getDemandId());
        tDemandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
        tDemandOrder.setStatusUpdateTime(systemDate);
        tDemandOrder.setStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
        tDemandOrder.setExpectContractPriceType(requestModel.getContractPriceType());
        tDemandOrder.setExpectContractPrice(requestModel.getContractPrice());

        //货主价格
        tDemandOrder.setContractPriceType(requestModel.getContractPriceType());
        tDemandOrder.setContractPrice(requestModel.getContractPrice());
        //车主价格
        tDemandOrder.setCarrierPriceType(requestModel.getCarrierPriceType());
        tDemandOrder.setCarrierPrice(requestModel.getCarrierPrice());

        //车主信息
        tDemandOrder.setCompanyCarrierType(companyCarrierByIdModel.getCompanyCarrierType());
        tDemandOrder.setCompanyCarrierId(companyCarrierByIdModel.getCompanyCarrierId());
        tDemandOrder.setCompanyCarrierName(companyCarrierByIdModel.getCompanyCarrierName());
        tDemandOrder.setCarrierContactId(companyCarrierByIdModel.getCarrierContactId());
        tDemandOrder.setCarrierContactName(companyCarrierByIdModel.getCarrierContactName());
        tDemandOrder.setCarrierContactPhone(companyCarrierByIdModel.getCarrierContactPhone());
        tDemandOrder.setCompanyCarrierLevel(companyCarrierByIdModel.getCompanyCarrierLevel());

        tDemandOrder.setDispatcherName(requestModel.getDispatcherName());
        tDemandOrder.setDispatcherPhone(requestModel.getDispatcherPhone());
        commonBiz.setBaseEntityModify(tDemandOrder, userName);
        demandOrderMapper.updateByPrimaryKeySelectiveEncrypt(tDemandOrder);

        //更新需求单地址表信息
        TDemandOrderAddress tDemandOrderAddress = MapperUtils.mapper(requestModel, TDemandOrderAddress.class);
        tDemandOrderAddress.setId(publishDetailModel.getDemandOrderAddressId());
        commonBiz.setBaseEntityModify(tDemandOrderAddress, userName);
        demandOrderAddressMapper.updateByPrimaryKeySelective(tDemandOrderAddress);

        //更新需求单操作log
        TDemandOrderOperateLogs demandOrderOperateLogs = new TDemandOrderOperateLogs();
        demandOrderOperateLogs.setDemandOrderId(publishDetailModel.getDemandId());
        demandOrderOperateLogs.setOperationType(DemandOrderOperateLogsEnum.PUBLISH_DEMAND_ORDER.getKey());
        demandOrderOperateLogs.setOperationContent(DemandOrderOperateLogsEnum.PUBLISH_DEMAND_ORDER.getValue());
        demandOrderOperateLogs.setRemark("【" + requestModel.getDispatcherName() + "&" + requestModel.getDispatcherPhone() + "】：" + requestModel.getContractPrice());
        demandOrderOperateLogs.setOperatorName(userName);
        demandOrderOperateLogs.setOperateTime(systemDate);
        commonBiz.setBaseEntityAdd(demandOrderOperateLogs, userName);
        demandOrderOperateLogsMapper.insertSelective(demandOrderOperateLogs);

        //新增记录到需求单车主变更表
        TDemandOrderCarrier tDemandOrderCarrier = new TDemandOrderCarrier();
        tDemandOrderCarrier.setDemandOrderId(tDemandOrder.getId());
        tDemandOrderCarrier.setCompanyCarrierId(tDemandOrder.getCompanyCarrierId());
        tDemandOrderCarrier.setCarrierContactId(tDemandOrder.getCarrierContactId());
        tDemandOrderCarrier.setCarrierPrice(tDemandOrder.getCarrierPrice());
        tDemandOrderCarrier.setCarrierPriceType(tDemandOrder.getCarrierPriceType());
        commonBiz.setBaseEntityAdd(tDemandOrderCarrier, userName);
        tDemandOrderCarrierMapper.insertSelective(tDemandOrderCarrier);

        //异步查询地址经纬度
        AsyncProcessQueue.execute(() -> demandOrderCommonBiz.updateAddressLonAndLat(Collections.singletonList(tDemandOrderAddress)));

        //同步报价信息到中石化的系统
        Map<String, PublishSinopecResponseModel> snOrderIdMap = new HashMap<>();
        snOrderIdMap.put(publishDetailModel.getCustomerOrderCode(), publishDetailModel);
        //需要同步给中石化系统的报价信息
        SinopecOrderQuotationRequestModel syncSinopecDemandOrderPublishModel = new SinopecOrderQuotationRequestModel()
                .setOrderNo(publishDetailModel.getSinopecOrderNo())
                .setSn(publishDetailModel.getCustomerOrderCode())
                .setTransUnitPrice(requestModel.getContractPrice())
                .setDispatcher(requestModel.getDispatcherName())
                .setDispatcherPhoneNo(requestModel.getDispatcherPhone())
                .setOperator(userName)
                // 添加租户信息
                .setCustomerId(publishDetailModel.getSinopecCustomerId())
                .setCustomerName(publishDetailModel.getSinopecCustomerName());
        //异步同步报价信息
        AsyncProcessQueue.execute(() -> this.syncSinopecDemandOrderPublish(Collections.singletonList(syncSinopecDemandOrderPublishModel), snOrderIdMap));
    }

    /**
     * 批量发布中石化需求单
     *
     * @param requestModel 要发布的需求单信息
     */
    @Transactional
    public void batchPublishSinopecDemandOrder(BatchPublishSinopecDemandRequestModel requestModel) {

        //要发布的需求单集合
        List<Long> demandOrderIds = requestModel.getSinopecDemands().stream().map(BatchPublishSinopecDemandDetailModel::getDemandOrderId).collect(Collectors.toList());
        //过滤后的需求列表
        List<PublishSinopecResponseModel> publishDetails = searchSinopecDemandOrderDetail(StringUtils.listToString(demandOrderIds, ','));

        //查询车主信息
        CompanyCarrierByIdModel companyCarrierByIdModel = demandOrderCommonBiz.companyCarrierInfoByIsOurCompany(requestModel.getIsOurCompany(), requestModel.getCompanyCarrierId());

        Date systemDate = new Date();
        //当前登陆人name
        String userName = BaseContextHandler.getUserName();

        //需求单实体
        TDemandOrder tDemandOrder;
        List<TDemandOrder> tDemandOrderList = new ArrayList<>();
        //需求单地址实体
        TDemandOrderAddress tDemandOrderAddress;
        List<TDemandOrderAddress> tDemandOrderAddressList = new ArrayList<>();
        //需求单操作日志实体
        TDemandOrderOperateLogs demandOrderOperateLogs;
        List<TDemandOrderOperateLogs> demandOrderOperateLogsList = new ArrayList<>();
        //需求单车主信息
        TDemandOrderCarrier tDemandOrderCarrier;
        List<TDemandOrderCarrier> tDemandOrderCarrierAddList = new ArrayList<>();
        //需要同步给中石化系统的报价信息
        List<SinopecOrderQuotationRequestModel> syncSinopecDemandOrderPublishModelList = new ArrayList<>();
        //客户单号需求单映射
        Map<String, PublishSinopecResponseModel> snOrderIdMap = new HashMap<>();
        //前端传入的需求单列表
        Map<Long, BatchPublishSinopecDemandDetailModel> requestOrderMap = requestModel.getSinopecDemands().stream().collect(Collectors.toMap(BatchPublishSinopecDemandDetailModel::getDemandOrderId, batchPublishSinopecDemandDetailModel -> batchPublishSinopecDemandDetailModel));
        //批量处理
        for (PublishSinopecResponseModel publishDetail : publishDetails) {

            //更新需求单表信息
            tDemandOrder = new TDemandOrder();
            tDemandOrder.setId(publishDetail.getDemandId());
            tDemandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
            tDemandOrder.setStatusUpdateTime(systemDate);
            tDemandOrder.setStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
            tDemandOrder.setExpectContractPriceType(ContractPriceTypeEnum.UNIT_PRICE.getKey());
            tDemandOrder.setExpectContractPrice(requestModel.getContractPrice());

            //货主价格
            tDemandOrder.setContractPriceType(ContractPriceTypeEnum.UNIT_PRICE.getKey());
            tDemandOrder.setContractPrice(requestModel.getContractPrice());
            //车主价格
            tDemandOrder.setCarrierPriceType(ContractPriceTypeEnum.UNIT_PRICE.getKey());
            tDemandOrder.setCarrierPrice(requestModel.getCarrierPrice());

            //车主信息
            tDemandOrder.setCompanyCarrierType(companyCarrierByIdModel.getCompanyCarrierType());
            tDemandOrder.setCompanyCarrierId(companyCarrierByIdModel.getCompanyCarrierId());
            tDemandOrder.setCompanyCarrierName(companyCarrierByIdModel.getCompanyCarrierName());
            tDemandOrder.setCarrierContactId(companyCarrierByIdModel.getCarrierContactId());
            tDemandOrder.setCarrierContactName(companyCarrierByIdModel.getCarrierContactName());
            tDemandOrder.setCarrierContactPhone(companyCarrierByIdModel.getCarrierContactPhone());
            tDemandOrder.setCompanyCarrierLevel(companyCarrierByIdModel.getCompanyCarrierLevel());

            tDemandOrder.setDispatcherName(requestModel.getDispatcherName());
            tDemandOrder.setDispatcherPhone(requestModel.getDispatcherPhone());
            commonBiz.setBaseEntityModify(tDemandOrder, userName);
            tDemandOrderList.add(tDemandOrder);

            //更新需求单地址表信息
            BatchPublishSinopecDemandDetailModel batchPublishSinopecDemandDetailModel = requestOrderMap.get(publishDetail.getDemandId());
            tDemandOrderAddress = MapperUtils.mapper(batchPublishSinopecDemandDetailModel, TDemandOrderAddress.class);
            tDemandOrderAddress.setId(publishDetail.getDemandOrderAddressId());
            commonBiz.setBaseEntityModify(tDemandOrderAddress, userName);
            tDemandOrderAddressList.add(tDemandOrderAddress);

            //更新需求单操作log
            demandOrderOperateLogs = new TDemandOrderOperateLogs();
            demandOrderOperateLogs.setDemandOrderId(publishDetail.getDemandId());
            demandOrderOperateLogs.setOperationType(DemandOrderOperateLogsEnum.PUBLISH_DEMAND_ORDER.getKey());
            demandOrderOperateLogs.setOperationContent(DemandOrderOperateLogsEnum.PUBLISH_DEMAND_ORDER.getValue());
            demandOrderOperateLogs.setRemark("【" + requestModel.getDispatcherName() + "&" + requestModel.getDispatcherPhone() + "】：" + requestModel.getContractPrice());
            demandOrderOperateLogs.setOperatorName(userName);
            demandOrderOperateLogs.setOperateTime(systemDate);
            commonBiz.setBaseEntityAdd(demandOrderOperateLogs, userName);
            demandOrderOperateLogsList.add(demandOrderOperateLogs);

            //需求单车主信息
            tDemandOrderCarrier = new TDemandOrderCarrier();
            tDemandOrderCarrier.setDemandOrderId(tDemandOrder.getId());
            tDemandOrderCarrier.setCompanyCarrierId(tDemandOrder.getCompanyCarrierId());
            tDemandOrderCarrier.setCarrierContactId(tDemandOrder.getCarrierContactId());
            tDemandOrderCarrier.setCarrierPrice(tDemandOrder.getCarrierPrice());
            tDemandOrderCarrier.setCarrierPriceType(tDemandOrder.getCarrierPriceType());
            commonBiz.setBaseEntityAdd(tDemandOrderCarrier, userName);
            tDemandOrderCarrierAddList.add(tDemandOrderCarrier);

            //同步报价信息到中石化的系统
            snOrderIdMap.put(publishDetail.getCustomerOrderCode(), publishDetail);
            syncSinopecDemandOrderPublishModelList.add(new SinopecOrderQuotationRequestModel()
                    .setOrderNo(publishDetail.getSinopecOrderNo())
                    .setSn(publishDetail.getCustomerOrderCode())
                    .setTransUnitPrice(requestModel.getContractPrice())
                    .setDispatcher(requestModel.getDispatcherName())
                    .setDispatcherPhoneNo(requestModel.getDispatcherPhone())
                    .setOperator(userName)
                    // 填充租户信息
                    .setCustomerId(publishDetail.getSinopecCustomerId())
                    .setCustomerName(publishDetail.getSinopecCustomerName())
            );
        }

        //更新需求单
        if (ListUtils.isNotEmpty(tDemandOrderList)) {
            demandOrderMapper.batchUpdateByPrimaryKeySelective(tDemandOrderList);
        }

        //更新需求单地址
        if (ListUtils.isNotEmpty(tDemandOrderAddressList)) {
            demandOrderAddressMapper.batchUpdateSelective(tDemandOrderAddressList);
            //异步查询地址经纬度
            AsyncProcessQueue.execute(() -> demandOrderCommonBiz.updateAddressLonAndLat(tDemandOrderAddressList));
        }

        //新增需求单操作日志
        if (ListUtils.isNotEmpty(demandOrderOperateLogsList)) {
            demandOrderOperateLogsMapper.batchInsertSelective(demandOrderOperateLogsList);
        }

        //新增需求单车主信息
        if(ListUtils.isNotEmpty(tDemandOrderCarrierAddList)){
            tDemandOrderCarrierMapper.batchInsertSelective(tDemandOrderCarrierAddList);
        }

        //异步同步报价信息
        if (ListUtils.isNotEmpty(syncSinopecDemandOrderPublishModelList)) {
            AsyncProcessQueue.execute(() -> this.syncSinopecDemandOrderPublish(syncSinopecDemandOrderPublishModelList, snOrderIdMap));
        }
    }

    /**
     * 发布中石化的需求单后同步报价信息到中石化的系统
     *
     * @param sinopecDemandOrderPublishList 报价信息
     * @param snDemandOrderMap              委托单号-需求单发布详情映射
     */
    private void syncSinopecDemandOrderPublish(List<SinopecOrderQuotationRequestModel> sinopecDemandOrderPublishList, Map<String, PublishSinopecResponseModel> snDemandOrderMap) {

        //判断调用中石化接口开关是否打开
        String sysConfig = configBiz.getSysConfig(ConfigKeyEnum.SINOPEC_METHOD_SWITCH).orElse(CommonConstant.ZERO);
        if (CommonConstant.ZERO.equals(sysConfig)) {
            log.info("调用中石化接口，无开关配置或开关关闭无需调用！");
            return;
        }

        List<PublishSinopecResponseModel> rollbackDemandOrderList = new ArrayList<>();//需要回退数据的需求单
        for (SinopecOrderQuotationRequestModel item : sinopecDemandOrderPublishList) {
            boolean ifError = false;
            String msg = null;
            try {
                //请求中石化接口
                String result = sinopecBiz.orderQuotationV1(item);
                if (StringUtils.isBlank(result)) {
                    ifError = true;
                } else {
                    SinopecResultModel resultModel = objectMapper.readValue(result, SinopecResultModel.class);
                    if (resultModel == null || !CommonConstant.ZERO.equals(resultModel.getType())) {
                        ifError = true;
                        if (resultModel != null) {
                            msg = resultModel.getMessage();
                        }
                    }
                }
            } catch (Exception e) {
                ifError = true;
                msg = e.getMessage();
            }
            //同步中石化失败
            if (ifError) {
                log.warn("发布中石化需求单-》同步报价信息到中石化系统失败。中石化委托单号（客户单号）：" + item.getSn() + "，" + msg);
                rollbackDemandOrderList.add(snDemandOrderMap.get(item.getSn()));
            }
        }
        //报价信息同步失败,把表数据恢复成发布前
        if (ListUtils.isNotEmpty(rollbackDemandOrderList)) {
            demandOrderMapper.rollBackSinopecDemandOrderByPublish(rollbackDemandOrderList);
        }
    }
    /**
     * 中石化需求单上报异常详情查询
     *
     * @param requestModel 需求单id
     * @return 需求单详情列表
     */
    public SinopecReportAbnormalDetailResponseModel sinopecReportAbnormalDetail(SinopecReportAbnormalDetailRequestModel requestModel) {
        SinopecReportAbnormalDetailResponseModel responseModel = demandOrderMapper.sinopecReportAbnormalDetail(requestModel.getDemandId());
        //只能操作网货的单子
        if (!CommonConstant.INTEGER_ONE.equals(responseModel.getSinopecOnlineGoodsFlag())) {
            throw new BizException(EntrustDataExceptionEnum.OPERATION_DEMAND_ORDER_ONLY_SINOPEC_ONLINE);
        }
        return responseModel;
    }

    /**
     * 中石化需求单异常上报
     *
     * @param requestModel 异常上报信息
     * @return 操作结果
     */
    public void saveSinopecReportAbnormal(SaveSinopecReportAbnormalRequestModel requestModel) {
        //查询异常数据
        TDemandOrderObjectionSinopec dbDemandOrderObjectionSinopec = tDemandOrderObjectionSinopecMapper.selectByDemandOrderId(requestModel.getDemandOrderId());
        if (dbDemandOrderObjectionSinopec != null && dbDemandOrderObjectionSinopec.getValid().equals(IfValidEnum.VALID.getKey()) && dbDemandOrderObjectionSinopec.getAuditStatus().equals(AuditStatusEnum.WAIT_AUDIT.getKey())){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_OBJECTION_SINOPEC);
        }

        //查询需求单
        TDemandOrder dbDemandOrder = demandOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getDemandOrderId());
        if (dbDemandOrder == null || dbDemandOrder.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }
        //只能是中石化推送的单子
        if (!DemandOrderSourceEnum.SINOPEC_SYSTEM.getKey().equals(dbDemandOrder.getSource())  || !DemandOrderOrderTypeEnum.PUSH.getKey().equals(dbDemandOrder.getOrderType())) {
            throw new BizException(EntrustDataExceptionEnum.OPERATION_DEMAND_ORDER_ONLY_SINOPEC.getCode(), dbDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.OPERATION_DEMAND_ORDER_ONLY_SINOPEC.getMsg());
            //只能是待发布/待调度的
        } else if (!DemandOrderStatusEnum.WAIT_PUBLISH.getKey().equals(dbDemandOrder.getEntrustStatus()) && !DemandOrderStatusEnum.WAIT_DISPATCH.getKey().equals(dbDemandOrder.getEntrustStatus())) {
            throw new BizException(EntrustDataExceptionEnum.THE_CURRENT_STATE_CANNOT_REPORT_EXCEPTIONS.getCode(), dbDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.THE_CURRENT_STATE_CANNOT_REPORT_EXCEPTIONS.getMsg());
            //已取消的需求单不能上报异常
        } else if (CommonConstant.INTEGER_ONE.equals(dbDemandOrder.getIfCancel())) {
            throw new BizException(EntrustDataExceptionEnum.THE_CURRENT_STATE_CANNOT_REPORT_EXCEPTIONS.getCode(), dbDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.THE_CURRENT_STATE_CANNOT_REPORT_EXCEPTIONS.getMsg());
            //待调度需求单不能选择已报价
        } else if (DemandOrderStatusEnum.WAIT_DISPATCH.getKey().equals(dbDemandOrder.getEntrustStatus()) && CommonConstant.INTEGER_ONE.equals(requestModel.getObjectionType())) {
            throw new BizException(EntrustDataExceptionEnum.QUOTATION_CANNOT_BE_SELECTED_FOR_THE_DEMAND_LIST_TO_BE_SCHEDULED.getCode(), dbDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.QUOTATION_CANNOT_BE_SELECTED_FOR_THE_DEMAND_LIST_TO_BE_SCHEDULED.getMsg());
            //只能操作网货的单子
        } else if (!CommonConstant.INTEGER_ONE.equals(dbDemandOrder.getSinopecOnlineGoodsFlag())) {
            throw new BizException(EntrustDataExceptionEnum.OPERATION_DEMAND_ORDER_ONLY_SINOPEC_ONLINE);
        }
        //新增中石化异常数据
        TDemandOrderObjectionSinopec tDemandOrderObjectionSinopec = new TDemandOrderObjectionSinopec();
        MapperUtils.mapper(requestModel, tDemandOrderObjectionSinopec);
        commonBiz.setBaseEntityAdd(tDemandOrderObjectionSinopec, BaseContextHandler.getUserName());
        tDemandOrderObjectionSinopecMapper.insertSelective(tDemandOrderObjectionSinopec);
        //修改中石化需求单异常字段
        TDemandOrder tDemandOrder = new TDemandOrder();
        tDemandOrder.setId(requestModel.getDemandOrderId());
        tDemandOrder.setIfObjectionSinopec(CommonConstant.INTEGER_ONE);
        commonBiz.setBaseEntityModify(tDemandOrder, BaseContextHandler.getUserName());
        demandOrderMapper.updateByPrimaryKeySelectiveEncrypt(tDemandOrder);
    }

    /**
     * 查询中石化需求单发布详情
     *
     * @param demandOrderIds 需求单id
     * @return 筛选后的中石化需求单发布详情集合
     */
    private List<PublishSinopecResponseModel> searchSinopecDemandOrderDetail(String demandOrderIds) {

        //防sql注入并过滤无效参数
        List<Long> demandOrderIdReq = Arrays.stream(demandOrderIds.split(",")).map(splitStr -> StringUtils.isNumeric(splitStr) ? Long.parseLong(splitStr) : CommonConstant.LONG_ZERO).distinct().collect(Collectors.toList());
        List<PublishSinopecResponseModel> detailList = demandOrderMapper.sinopecPublishDetail(demandOrderIdReq);

        if (ListUtils.isEmpty(detailList)) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }

        //货主id
        Set<Long> companyEntrustIdSet = new HashSet<>();
        Iterator<PublishSinopecResponseModel> iterator = detailList.iterator();
        //批量查询过滤不符合条件的记录
        while (iterator.hasNext()) {
            PublishSinopecResponseModel publishDetail = iterator.next();
            //只能是中石化推送的单子
            if (!DemandOrderSourceEnum.SINOPEC_SYSTEM.getKey().equals(publishDetail.getSource()) || !DemandOrderOrderTypeEnum.PUSH.getKey().equals(publishDetail.getOrderType())) {
                iterator.remove();

                //只能是待发布的
            } else if (!DemandOrderStatusEnum.WAIT_PUBLISH.getKey().equals(publishDetail.getEntrustStatus())) {
                iterator.remove();

                //只能是未取消的
            } else if (CommonConstant.INTEGER_ONE.equals(publishDetail.getIfCancel())) {
                iterator.remove();

                //只能是未放空的
            } else if (CommonConstant.INTEGER_ONE.equals(publishDetail.getIfEmpty())) {
                iterator.remove();

                //有异常的
            } else if (CommonConstant.INTEGER_ONE.equals(publishDetail.getIfObjectionSinopec())) {
                iterator.remove();

                //非网货的
            } else if (!CommonConstant.INTEGER_ONE.equals(publishDetail.getSinopecOnlineGoodsFlag())) {
                iterator.remove();
            }

            companyEntrustIdSet.add(publishDetail.getCompanyEntrustId());
        }

        //不同货主的需求单不能一起操作
        if (companyEntrustIdSet.size() > CommonConstant.INTEGER_ONE) {
            throw new BizException(EntrustDataExceptionEnum.PUBLISH_DEMAND_ORDER_ENTRUST_ERROR);
        }

        //批量只允许最多十条
        if (detailList.size() > CommonConstant.INTEGER_TEN) {
            throw new BizException(EntrustDataExceptionEnum.PUBLISH_DEMAND_ORDER_MAX);
        }

        //没有一条记录是符合的情况下 抛出错误
        if (ListUtils.isEmpty(detailList)) {
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_NOT_PUBLISH.getCode(), EntrustDataExceptionEnum.DEMAND_ORDER_NOT_PUBLISH.getMsg());
        }
        return detailList;
    }

    /**
     * 接收中石化委托单(新)
     *
     * @param requestModel
     */
    @Transactional
    public void receiveDemandV1(List<ReceiveSinopecDemandV1RequestModel> requestModel, List<String> associatedNumberList, String requestIp) {
        //redis校验,是否短时间内重复请求
        checkSinopecDemandRedisKey(associatedNumberList);

        //查询委托单是否已经生成需求单
        List<TDemandOrder> demandOrderList = new ArrayList<>();
        if (ListUtils.isNotEmpty(associatedNumberList)) {
            demandOrderList = demandOrderMapper.getValidSinopecOrderByOrderNos(associatedNumberList);
        }
        //用于过滤已经存在于系统内的委托单
        Map<String, Long> demandOrderAssociatedNumberMap = new HashMap<>();
        //校验委托单是否存在系统中
        checkSinopecDemandAlreadyExist(associatedNumberList, demandOrderList, demandOrderAssociatedNumberMap);

        int paramsCheckErrorCount = 0; //参数校验错误次数
        String requestOperateName = null;
        //委托单号 - 数量 映射map,用于防止有重复的委托单号 生成重复的需求单
        Map<String, Integer> originalDataCountMap = new HashMap<>();
        for (ReceiveSinopecDemandV1RequestModel requestItem : requestModel) {
            //参数校验
            String checkMsg = requestItem.check();
            //只有一条,直接抛出错误 ,多条过滤
            if (StringUtils.isNotBlank(checkMsg)) {
                String errorMsg = EntrustDataExceptionEnum.SINOPEC_DEMANDORDER_CHECK_ERROR.getMsg() + Optional.ofNullable(requestItem.getAssociatedNumber()).orElse("") + " " + checkMsg;
                if (requestModel.size() == CommonConstant.INTEGER_ONE) {
                    throw new BizException(EntrustDataExceptionEnum.SINOPEC_DEMANDORDER_CHECK_ERROR.getCode(), errorMsg);
                } else {
                    //多条的过滤
                    log.warn("接收中石化委托单参数校验错误: " + errorMsg);
                    paramsCheckErrorCount++;
                    continue;
                }
            }

            //如果委托单号存在于redis，则不新增（去重）
            Object associatedNumberV = redisUtils.get(CommonConstant.LOGISTICS_SINOPEC_ASSOCIATED + requestItem.getAssociatedNumber());
            if (associatedNumberV != null) {
                continue;
            }
            //委托单号设置到redis中
            redisUtils.set(CommonConstant.LOGISTICS_SINOPEC_ASSOCIATED + requestItem.getAssociatedNumber(), requestItem.getAssociatedNumber(), 60 * 5L);

            //操作人
            String operateName = requestItem.getAuditUser();
            if (StringUtils.isBlank(operateName)) {
                operateName = CommonConstant.SINOPEC_OPERATOR;
            }
            if (requestOperateName == null) {
                requestOperateName = operateName;
            }

            //新增原始数据
            List<SinopecDemandListModel> sinopecDemandList = requestItem.getList();//行项目信息
            List<SinopecFreightUnitModel> freightUnit = requestItem.getFreightUnit();//货运单元信息
            List<SinopecTransitPortLocationModel> transitPortLocation = requestItem.getTransitPortLocation();//中转港信息
            SinopecAnnexInfoModel annexInfo = requestItem.getAnnexInfo();//附件信息
            SinopecDeclarationPortModel declarationPort = requestItem.getDeclarationPort();//申报口岸

            //原始数据落库
            TSinopecOriginalData originalData = MapperUtils.mapperNoDefault(requestItem, TSinopecOriginalData.class);
            //委托单号
            originalData.setSn(requestItem.getAssociatedNumber());
            originalData.setAssociatedDate(new Date(requestItem.getAssociatedDate()));
            originalData.setArrivalDeadline(new Date(requestItem.getArrivalDeadline()));
            //申报口岸
            if (declarationPort != null) {
                MapperUtils.mapperNoDefault(declarationPort, originalData);
            }
            commonBiz.setBaseEntityAdd(originalData, operateName);
            tSinopecOriginalDataMapper.insertSelective(originalData);

            //货运单元
            List<TSinopecOriginalFreightUnitData> tSinopecOriginalFreightUnitDataList = packageFreightUnitData(operateName, freightUnit, originalData.getId());
            if (ListUtils.isNotEmpty(tSinopecOriginalFreightUnitDataList)) {
                tSinopecOriginalFreightUnitDataMapper.batchInsertSelective(tSinopecOriginalFreightUnitDataList);
            }

            //中转港信息
            List<TSinopecOriginalTransitPortLocationData> tSinopecOriginalTransitPortLocationDataList = packageTransitPortLocationData(operateName, transitPortLocation, originalData.getId());
            if (ListUtils.isNotEmpty(tSinopecOriginalTransitPortLocationDataList)) {
                tSinopecOriginalTransitPortLocationDataMapper.batchInsertSelective(tSinopecOriginalTransitPortLocationDataList);
            }

            //附件信息
            if (annexInfo != null) {

                //oms附件
                List<TSinopecOriginalAnnexData> omsAttachmentList = packageAnnexData(operateName, originalData.getId(), annexInfo.getOmsAttachment(), CommonConstant.INTEGER_ONE);

                //危化品托运清单附件
                List<TSinopecOriginalAnnexData> hazardousChemicalsConsignList = packageAnnexData(operateName, originalData.getId(), annexInfo.getHazardousChemicalsConsignList(), CommonConstant.INTEGER_ZERO);
                omsAttachmentList.addAll(hazardousChemicalsConsignList);

                if (ListUtils.isNotEmpty(omsAttachmentList)) {
                    tSinopecOriginalAnnexDataMapper.batchInsertSelective(omsAttachmentList);
                }
            }

            //行项目信息
            List<TSinopecOriginalTransitLineItemData> tSinopecOriginalTransitLineItemDataList = packageSinopecDemandItem(operateName, sinopecDemandList, originalData.getId());
            if (ListUtils.isNotEmpty(tSinopecOriginalTransitLineItemDataList)) {
                tSinopecOriginalTransitLineItemDataMapper.batchInsertSelective(tSinopecOriginalTransitLineItemDataList);
            }

            //不存在于系统内的并且本次也没有生成过的才会生成需求单
            if (demandOrderAssociatedNumberMap.get(requestItem.getAssociatedNumber()) == null
                    && originalDataCountMap.get(requestItem.getAssociatedNumber()) == null) {
                originalDataCountMap.put(requestItem.getAssociatedNumber(), CommonConstant.INTEGER_ONE);
                requestItem.setOriginalDataId(originalData.getId());
            }
        }

        //请求的所有条目都没通过校验
        if (requestModel.size() == paramsCheckErrorCount) {
            throw new BizException(EntrustDataExceptionEnum.SINOPEC_DEMANDORDER_CHECK_ALL_ERROR);
        }

        //新增数据日志
        addSinopecMessageLog(SinopecMessageLogTypeEnum.DEMANDORDER, SinopecMessageLogActionEnum.ADD, JSONObject.toJSONString(requestModel), requestIp, requestOperateName);
        //异步生成需求单
        AsyncProcessQueue.execute(() -> createDemandOrderFromOriginalDataV1(requestModel));
    }

    /**
     * 组装行信息列表
     *
     * @param operateName       操作人
     * @param sinopecDemandList 原始数据行信息列表
     * @param originalDataId    基础数据表id
     * @return 组装后的行信息列表
     */
    private List<TSinopecOriginalTransitLineItemData> packageSinopecDemandItem(String operateName, List<SinopecDemandListModel> sinopecDemandList, Long originalDataId) {
        List<TSinopecOriginalTransitLineItemData> tSinopecOriginalTransitLineItemDataList = new ArrayList<>();
        TSinopecOriginalTransitLineItemData tSinopecOriginalTransitLineItemData;
        for (SinopecDemandListModel sinopecDemandItemModel : sinopecDemandList) {
            tSinopecOriginalTransitLineItemData = MapperUtils.mapperNoDefault(sinopecDemandItemModel, TSinopecOriginalTransitLineItemData.class);
            tSinopecOriginalTransitLineItemData.setSinopecOriginalDataId(originalDataId);
            tSinopecOriginalTransitLineItemData.setBusinessOrderValidEnd(new Date(sinopecDemandItemModel.getBusinessOrderValidEnd()));
            tSinopecOriginalTransitLineItemData.setCertificateTime(new Date(sinopecDemandItemModel.getCertificateTime()));
            //生产企业
            SinopecManufacturerModel manufacturer = sinopecDemandItemModel.getManufacturer();
            if (manufacturer != null) {
                MapperUtils.mapperNoDefault(manufacturer, tSinopecOriginalTransitLineItemData);
            }
            //起运地
            SinopecLocationModel originPortLocation = sinopecDemandItemModel.getOriginPortLocation();
            if (originPortLocation != null) {
                tSinopecOriginalTransitLineItemData.setOriginPortLocationCode(originPortLocation.getLocationCode());
                tSinopecOriginalTransitLineItemData.setOriginPortLocationName(originPortLocation.getLocationName());
                tSinopecOriginalTransitLineItemData.setOriginPortLocationType(originPortLocation.getLocationType());
                tSinopecOriginalTransitLineItemData.setOriginPortDetailAddress(originPortLocation.getDetailAddress());
                SinopecFourLevelAddressModel fourLevelAddress = originPortLocation.getFourLevelAddress();
                if (fourLevelAddress != null) {
                    tSinopecOriginalTransitLineItemData.setOriginPortProvinceCode(fourLevelAddress.getProvinceCode());
                    tSinopecOriginalTransitLineItemData.setOriginPortProvinceName(fourLevelAddress.getProvinceName());
                    tSinopecOriginalTransitLineItemData.setOriginPortCityCode(fourLevelAddress.getCityName());
                    tSinopecOriginalTransitLineItemData.setOriginPortCityName(fourLevelAddress.getCityCode());
                    tSinopecOriginalTransitLineItemData.setOriginPortCountyCode(fourLevelAddress.getCountyCode());
                    tSinopecOriginalTransitLineItemData.setOriginPortCountyName(fourLevelAddress.getCountyName());
                    tSinopecOriginalTransitLineItemData.setOriginPortTownCode(fourLevelAddress.getTownCode());
                    tSinopecOriginalTransitLineItemData.setOriginPortTownName(fourLevelAddress.getTownName());
                }
                SinopecCoordinatesModel coordinates = originPortLocation.getCoordinates();
                if (coordinates != null) {
                    tSinopecOriginalTransitLineItemData.setOriginPortLatitude(coordinates.getLatitude());
                    tSinopecOriginalTransitLineItemData.setOriginPortLongitude(coordinates.getLongitude());
                }
            }
            //送达地
            SinopecLocationModel destinationPortLocation = sinopecDemandItemModel.getDestinationPortLocation();
            if (destinationPortLocation != null) {
                tSinopecOriginalTransitLineItemData.setDestinationPortLocationCode(destinationPortLocation.getLocationCode());
                tSinopecOriginalTransitLineItemData.setDestinationPortLocationName(destinationPortLocation.getLocationName());
                tSinopecOriginalTransitLineItemData.setDestinationPortLocationType(destinationPortLocation.getLocationType());
                tSinopecOriginalTransitLineItemData.setDestinationPortDetailAddress(destinationPortLocation.getDetailAddress());
                SinopecFourLevelAddressModel fourLevelAddress = destinationPortLocation.getFourLevelAddress();
                if (fourLevelAddress != null) {
                    tSinopecOriginalTransitLineItemData.setDestinationPortProvinceCode(fourLevelAddress.getProvinceCode());
                    tSinopecOriginalTransitLineItemData.setDestinationPortProvinceName(fourLevelAddress.getProvinceName());
                    tSinopecOriginalTransitLineItemData.setDestinationPortCityCode(fourLevelAddress.getCityName());
                    tSinopecOriginalTransitLineItemData.setDestinationPortCityName(fourLevelAddress.getCityCode());
                    tSinopecOriginalTransitLineItemData.setDestinationPortCountyCode(fourLevelAddress.getCountyCode());
                    tSinopecOriginalTransitLineItemData.setDestinationPortCountyName(fourLevelAddress.getCountyName());
                    tSinopecOriginalTransitLineItemData.setDestinationPortTownCode(fourLevelAddress.getTownCode());
                    tSinopecOriginalTransitLineItemData.setDestinationPortTownName(fourLevelAddress.getTownName());
                }
                SinopecCoordinatesModel coordinates = destinationPortLocation.getCoordinates();
                if (coordinates != null) {
                    tSinopecOriginalTransitLineItemData.setDestinationPortLatitude(coordinates.getLatitude());
                    tSinopecOriginalTransitLineItemData.setDestinationPortLongitude(coordinates.getLongitude());
                }
            }
            commonBiz.setBaseEntityAdd(tSinopecOriginalTransitLineItemData, operateName);
            tSinopecOriginalTransitLineItemDataList.add(tSinopecOriginalTransitLineItemData);
        }
        return tSinopecOriginalTransitLineItemDataList;
    }

    /**
     * 组装附件信息
     *
     * @param operateName                操作人
     * @param originalDataId             原始数据表id
     * @param sinopecAttachmentModelList 原始附件
     * @param annexType                  0: 危化品托运清单(hazardousChemicalsConsignList); 1: OMS上传附件(omsAttachment)
     * @return 组装后的附件entity
     */
    private List<TSinopecOriginalAnnexData> packageAnnexData(String operateName, Long originalDataId, List<SinopecAttachmentModel> sinopecAttachmentModelList, Integer annexType) {
        List<TSinopecOriginalAnnexData> tSinopecOriginalAnnexDataList = new ArrayList<>();
        TSinopecOriginalAnnexData tSinopecOriginalAnnexData;
        if (ListUtils.isNotEmpty(sinopecAttachmentModelList)) {
            for (SinopecAttachmentModel sinopecAttachmentModel : sinopecAttachmentModelList) {
                tSinopecOriginalAnnexData = MapperUtils.mapperNoDefault(sinopecAttachmentModel, TSinopecOriginalAnnexData.class);
                tSinopecOriginalAnnexData.setAnnexType(annexType);
                tSinopecOriginalAnnexData.setSinopecOriginalDataId(originalDataId);
                commonBiz.setBaseEntityAdd(tSinopecOriginalAnnexData, operateName);
                tSinopecOriginalAnnexDataList.add(tSinopecOriginalAnnexData);
            }
        }
        return tSinopecOriginalAnnexDataList;
    }

    /**
     * 组装港口信息
     *
     * @param operateName         操作人
     * @param transitPortLocation 中转港信息
     * @param originalDataId      原始数据表id
     * @return 组装后的中转港信息
     */
    private List<TSinopecOriginalTransitPortLocationData> packageTransitPortLocationData(String operateName, List<SinopecTransitPortLocationModel> transitPortLocation, Long originalDataId) {
        List<TSinopecOriginalTransitPortLocationData> tSinopecOriginalTransitPortLocationDataList = new ArrayList<>();
        if (ListUtils.isNotEmpty(transitPortLocation)) {
            TSinopecOriginalTransitPortLocationData tSinopecOriginalTransitPortLocationData;
            for (SinopecTransitPortLocationModel sinopecTransitPortLocationModel : transitPortLocation) {
                tSinopecOriginalTransitPortLocationData = MapperUtils.mapperNoDefault(sinopecTransitPortLocationModel, TSinopecOriginalTransitPortLocationData.class);
                tSinopecOriginalTransitPortLocationData.setSinopecOriginalDataId(originalDataId);
                //四级地址
                SinopecFourLevelAddressModel fourLevelAddress = sinopecTransitPortLocationModel.getFourLevelAddress();
                if (fourLevelAddress != null) {
                    MapperUtils.mapperNoDefault(fourLevelAddress, tSinopecOriginalTransitPortLocationData);
                }
                //经纬度
                SinopecCoordinatesModel coordinates = sinopecTransitPortLocationModel.getCoordinates();
                if (coordinates != null) {
                    tSinopecOriginalTransitPortLocationData.setLatitude(coordinates.getLatitude());
                    tSinopecOriginalTransitPortLocationData.setLongitude(coordinates.getLongitude());
                }
                commonBiz.setBaseEntityAdd(tSinopecOriginalTransitPortLocationData, operateName);
                tSinopecOriginalTransitPortLocationDataList.add(tSinopecOriginalTransitPortLocationData);
            }
        }
        return tSinopecOriginalTransitPortLocationDataList;
    }

    /**
     * 组装货运单元信息
     *
     * @param operateName    操作人
     * @param freightUnit    货运单元信息
     * @param originalDataId 原始数据表id
     * @return 组装后的货运单元信息
     */
    private List<TSinopecOriginalFreightUnitData> packageFreightUnitData(String operateName, List<SinopecFreightUnitModel> freightUnit, Long originalDataId) {
        List<TSinopecOriginalFreightUnitData> tSinopecOriginalFreightUnitDataList = new ArrayList<>();
        if (ListUtils.isNotEmpty(freightUnit)) {
            TSinopecOriginalFreightUnitData tSinopecOriginalFreightUnitData;

            for (SinopecFreightUnitModel sinopecFreightUnitModel : freightUnit) {
                tSinopecOriginalFreightUnitData = MapperUtils.mapperNoDefault(sinopecFreightUnitModel, TSinopecOriginalFreightUnitData.class);
                tSinopecOriginalFreightUnitData.setSinopecOriginalDataId(originalDataId);
                commonBiz.setBaseEntityAdd(tSinopecOriginalFreightUnitData, operateName);
                tSinopecOriginalFreightUnitDataList.add(tSinopecOriginalFreightUnitData);
            }
        }
        return tSinopecOriginalFreightUnitDataList;
    }

    /**
     * 中石化小订单 - 从中石化同步的原始数据创建需求单
     *
     * @param requestModel
     */
    @Transactional
    public void createDemandOrderFromOriginalDataV1(List<ReceiveSinopecDemandV1RequestModel> requestModel) {
        for (ReceiveSinopecDemandV1RequestModel requestItem : requestModel) {
            if (requestItem.getOriginalDataId() != null) {
                //只取第一条
                SinopecDemandListModel sinopecDemandListModel = requestItem.getList().get(CommonConstant.INTEGER_ZERO);
                SinopecManufacturerModel manufacturer = sinopecDemandListModel.getManufacturer();//生产企业
                SinopecLocationModel originPortLocation = sinopecDemandListModel.getOriginPortLocation();//起运地
                SinopecLocationModel destinationPortLocation = sinopecDemandListModel.getDestinationPortLocation();//送达地

                Date now = new Date();
                String operatorName = requestItem.getAuditUser();
                //获取公司信息
                String companyEntrustName = "中国石化化工销售有限公司" + requestItem.getConsignorName();
                TCompanyEntrust companyEntrust = companyEntrustMapper.getByName(companyEntrustName);
                if (companyEntrust == null) {
                    BaseContextHandler.setUserName(operatorName);
                    companyEntrust = addEntrustCompany(companyEntrustName);
                }
                String ticketTimeStr = null;
                Date ticketTime = new Date(sinopecDemandListModel.getCertificateTime());//凭证日期
                if (ticketTime != null) {
                    ticketTimeStr = DateUtils.dateToString(ticketTime, DateUtils.DATE_TO_STRING_SHORT_PATTERN);
                }

                //拼接省市区详情地址用高德查询地址信息
                Map<String, String> loadAddressMap = new HashMap<>();
                Map<String, String> unloadAddressMap = new HashMap<>();
                //查询公司仓库
                TWarehouseAddress tWarehouseAddress = null;
                if (originPortLocation != null) {
                    tWarehouseAddress = tWarehouseAddressMapper.getValidWarehouseByNameForSinoper(companyEntrustName, originPortLocation.getLocationName(), ticketTimeStr);
                    //查不到仓库,使用传过来的地址
                    if (tWarehouseAddress == null) {
                        try {
                            //查询发货省市区
                            SinopecFourLevelAddressModel loadFourLevelAddress = originPortLocation.getFourLevelAddress();
                            if (loadFourLevelAddress != null) {
                                String searchKeyword = loadFourLevelAddress.getProvinceName() + loadFourLevelAddress.getCityName() + loadFourLevelAddress.getCountyName();
                                loadAddressMap = commonBiz.getProvinceCityArea(searchKeyword);
                            }
                        } catch (Exception e) {
                            log.info(e.getMessage(), e);
                        }
                    }
                }
                if (destinationPortLocation != null) {
                    try {
                        //查询收货省市区
                        SinopecFourLevelAddressModel unloadFourLevelAddress = destinationPortLocation.getFourLevelAddress();
                        if (unloadFourLevelAddress != null) {
                            String searchKeyword = unloadFourLevelAddress.getProvinceName() + unloadFourLevelAddress.getCityName() + unloadFourLevelAddress.getCountyName();
                            unloadAddressMap = commonBiz.getProvinceCityArea(searchKeyword);
                        }
                    } catch (Exception e) {
                        log.info(e.getMessage(), e);
                    }
                }

                //货物数量
                BigDecimal goodsAmount = sinopecDemandListModel.getAssociatedCount();
                //生成中石化需求单
                TDemandOrder demandOrder = new TDemandOrder();
                demandOrder.setDemandOrderCode(commonBiz.getBusinessTypeCode(BusinessCodeTypeEnum.DEMAND_ORDER_CODE, "", operatorName));
                demandOrder.setCustomerOrderCode(requestItem.getAssociatedNumber());
                demandOrder.setPublishName(operatorName);
                demandOrder.setPublishTime(now);
                demandOrder.setTicketTime(ticketTime);
                demandOrder.setCompanyEntrustId(companyEntrust.getId());
                demandOrder.setCompanyEntrustName(companyEntrustName);
                demandOrder.setSettlementTonnage(companyEntrust.getSettlementTonnage());
                demandOrder.setGoodsAmount(goodsAmount);
                demandOrder.setNotArrangedAmount(goodsAmount);
                demandOrder.setGoodsUnit(GoodsUnitEnum.BY_WEIGHT.getKey());
                demandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_PUBLISH.getKey());
                demandOrder.setStatusUpdateTime(now);
                demandOrder.setStatus(DemandOrderStatusEnum.WAIT_PUBLISH.getKey());
                demandOrder.setRemark(requestItem.getAssociatedRemark());
                demandOrder.setSource(DemandOrderSourceEnum.SINOPEC_SYSTEM.getKey());
                demandOrder.setOrderType(DemandOrderOrderTypeEnum.PUSH.getKey());
                demandOrder.setSinopecOrderNo(sinopecDemandListModel.getBusinessOrderNumber());
                demandOrder.setManufacturerName(manufacturer.getProductionEnterpriseName());
                demandOrder.setItemTransGroupName(sinopecDemandListModel.getPackingName());
                demandOrder.setItemPackSpecName(sinopecDemandListModel.getPackingSpecification());
                demandOrder.setSinopecCustomerId(requestItem.getCustomerId());
                demandOrder.setSinopecCustomerName(requestItem.getCustomerName());
                demandOrder.setSinopecOnlineGoodsFlag(requestItem.getOnlineGoodsFlag());
                commonBiz.setBaseEntityAdd(demandOrder, operatorName);

                //生成中石化需求单地址
                TDemandOrderAddress demandOrderAddress = new TDemandOrderAddress();
                //提货地址
                if (tWarehouseAddress != null) {
                    demandOrderAddress.setLoadProvinceId(tWarehouseAddress.getProvinceId());
                    demandOrderAddress.setLoadProvinceName(tWarehouseAddress.getProvinceName());
                    demandOrderAddress.setLoadCityId(tWarehouseAddress.getCityId());
                    demandOrderAddress.setLoadCityName(tWarehouseAddress.getCityName());
                    demandOrderAddress.setLoadAreaId(tWarehouseAddress.getAreaId());
                    demandOrderAddress.setLoadAreaName(tWarehouseAddress.getAreaName());
                } else {
                    //查不到仓库时使用高德查询的省市区
                    demandOrderAddress.setLoadProvinceId(ConverterUtils.toLong(loadAddressMap.get(EmailConstant.UNLOAD_PROVINCE_ID), 0));
                    demandOrderAddress.setLoadProvinceName(loadAddressMap.get(EmailConstant.UNLOAD_PROVINCE_NAME));
                    demandOrderAddress.setLoadCityId(ConverterUtils.toLong(loadAddressMap.get(EmailConstant.UNLOAD_CITY_ID), 0));
                    demandOrderAddress.setLoadCityName(loadAddressMap.get(EmailConstant.UNLOAD_CITY_NAME));
                    demandOrderAddress.setLoadAreaId(ConverterUtils.toLong(loadAddressMap.get(EmailConstant.UNLOAD_AREA_ID), 0));
                    demandOrderAddress.setLoadAreaName(loadAddressMap.get(EmailConstant.UNLOAD_AREA_NAME));
                }
                if (originPortLocation != null) {
                    demandOrderAddress.setLoadWarehouse(originPortLocation.getLocationName());
                    demandOrderAddress.setLoadDetailAddress(originPortLocation.getDetailAddress());
                }
                //卸货地址
                demandOrderAddress.setUnloadProvinceId(ConverterUtils.toLong(unloadAddressMap.get(EmailConstant.UNLOAD_PROVINCE_ID), 0));
                demandOrderAddress.setUnloadProvinceName(unloadAddressMap.get(EmailConstant.UNLOAD_PROVINCE_NAME));
                demandOrderAddress.setUnloadCityId(ConverterUtils.toLong(unloadAddressMap.get(EmailConstant.UNLOAD_CITY_ID), 0));
                demandOrderAddress.setUnloadCityName(unloadAddressMap.get(EmailConstant.UNLOAD_CITY_NAME));
                demandOrderAddress.setUnloadAreaId(ConverterUtils.toLong(unloadAddressMap.get(EmailConstant.UNLOAD_AREA_ID), 0));
                demandOrderAddress.setUnloadAreaName(unloadAddressMap.get(EmailConstant.UNLOAD_AREA_NAME));
                if (destinationPortLocation != null) {
                    demandOrderAddress.setUnloadWarehouse(destinationPortLocation.getLocationName());
                    demandOrderAddress.setUnloadDetailAddress(destinationPortLocation.getDetailAddress());
                }
                //收货人
                demandOrderAddress.setReceiverName(sinopecDemandListModel.getReceiverName());
                demandOrderAddress.setReceiverMobile(sinopecDemandListModel.getReceiverPhone());

                commonBiz.setBaseEntityAdd(demandOrderAddress, operatorName);

                //生成货物
                TDemandOrderGoods demandOrderGoods = new TDemandOrderGoods();
                //货物名称： 大类+小类+产品名
                demandOrderGoods.setGoodsName(sinopecDemandListModel.getMaterialMainCategory() + sinopecDemandListModel.getMaterialCategory() + sinopecDemandListModel.getMaterialName());
                demandOrderGoods.setGoodsAmount(goodsAmount);
                demandOrderGoods.setNotArrangedAmount(goodsAmount);
                commonBiz.setBaseEntityAdd(demandOrderGoods, operatorName);

                //生成需求单日志
                TDemandOrderOperateLogs demandOrderOperateLogs = demandOrderCommonBiz.getDemandOrderOperateLogs(null, DemandOrderOperateLogsEnum.CREATE_DEMAND_ORDER, operatorName, "");

                demandOrderMapper.insertSelectiveEncrypt(demandOrder);
                //回填数据id
                demandOrderAddress.setDemandOrderId(demandOrder.getId());
                demandOrderGoods.setDemandOrderId(demandOrder.getId());
                demandOrderOperateLogs.setDemandOrderId(demandOrder.getId());

                demandOrderAddressMapper.insertSelective(demandOrderAddress);
                demandOrderGoodsMapper.insertSelective(demandOrderGoods);
                demandOrderOperateLogsMapper.insertSelective(demandOrderOperateLogs);

                //生成需求单成功->更新源数据
                log.info("========中石化推送需求单--生成需求单成功=========客户单号" + demandOrder.getCustomerOrderCode());
                TSinopecOriginalData upSinopecOriginalData = new TSinopecOriginalData();
                upSinopecOriginalData.setId(requestItem.getOriginalDataId());
                upSinopecOriginalData.setIfHasDemandOrder(CommonConstant.INTEGER_ONE);
                commonBiz.setBaseEntityModify(upSinopecOriginalData, operatorName);
                tSinopecOriginalDataMapper.updateByPrimaryKeySelective(upSinopecOriginalData);
            }
        }
    }

    /**
     * 校验委托单号是否短时间内重复请求
     *
     * @param associatedNumberList 委托单号集合
     */
    private void checkSinopecDemandRedisKey(List<String> associatedNumberList) {
        //从redis获取上次请求设置的委托单号 防止短时间内重复请求
        if (associatedNumberList.size() == CommonConstant.INTEGER_ONE) {
            String associatedNumberScope = associatedNumberList.get(CommonConstant.INTEGER_ZERO);
            Object associatedNumberV = redisUtils.get(CommonConstant.LOGISTICS_SINOPEC_ASSOCIATED + associatedNumberScope);
            if (associatedNumberV != null) {
                log.error("=========中石化同步需求单========" + associatedNumberScope + "单号已存在======");
                throw new BizException(EntrustDataExceptionEnum.SINOPEC_DEMANDORDER_EXIST);
            }
        } else {
            List<String> associatedNumberScopeList = new ArrayList<>();
            //遍历获取redis内设置的key
            for (String associatedNumber : associatedNumberList) {
                String associatedNumberV = String.valueOf(redisUtils.get(CommonConstant.LOGISTICS_SINOPEC_ASSOCIATED + associatedNumber));
                if (StringUtils.isNotBlank(associatedNumberV)
                        && !CommonConstant.NULL_TEXT.equals(associatedNumberV)) {
                    associatedNumberScopeList.add(associatedNumberV);
                }
            }
            //这次所有的委托单号都在短时间内被请求过
            if (associatedNumberScopeList.size() == associatedNumberList.size()) {
                log.error("=========中石化同步需求单========" + StringUtils.join(associatedNumberList, ',') + "单号已存在======");
                throw new BizException(EntrustDataExceptionEnum.SINOPEC_DEMANDORDER_EXIST);
            }
        }
    }

    /**
     * 校验委托单是否存在于系统内
     *
     * @param associatedNumberList           请求的委托单号列表
     * @param demandOrderList                系统内需求单
     * @param demandOrderAssociatedNumberMap 存放已经存在于系统内的需求单
     */
    private static void checkSinopecDemandAlreadyExist(List<String> associatedNumberList, List<TDemandOrder> demandOrderList, Map<String, Long> demandOrderAssociatedNumberMap) {
        if (associatedNumberList.size() == CommonConstant.INTEGER_ONE) {
            if (ListUtils.isNotEmpty(demandOrderList)) {
                log.error("=========中石化同步需求单========" + associatedNumberList.get(CommonConstant.INTEGER_ZERO) + "单号已存在======");
                throw new BizException(EntrustDataExceptionEnum.SINOPEC_DEMANDORDER_EXIST);
            }
        } else {
            if (ListUtils.isNotEmpty(demandOrderList)) {
                List<String> associatedNumberScopeList = new ArrayList<>();
                for (TDemandOrder tDemandOrder : demandOrderList) {
                    associatedNumberScopeList.add(tDemandOrder.getCustomerOrderCode());
                    demandOrderAssociatedNumberMap.put(tDemandOrder.getCustomerOrderCode(), tDemandOrder.getId());
                }
                //这次所有的委托单号都在短时间内被请求过
                if (associatedNumberScopeList.size() == associatedNumberList.size()) {
                    log.error("=========中石化同步需求单========" + StringUtils.join(associatedNumberList, ',') + "单号已存在======");
                    throw new BizException(EntrustDataExceptionEnum.SINOPEC_DEMANDORDER_EXIST);
                }
            }
        }
    }

    /**
     * 中石化主动取消需求单(新)
     *
     * @param requestModel 委托单号
     */
    @Transactional
    public void cancelDemandV1(CancelSinopecDemandV1RequestModel requestModel) {
        //只能单个取消
        String associatedNo = requestModel.getAssociatedNoList().get(CommonConstant.INTEGER_ZERO);
        String operator = CommonConstant.SINOPEC_OPERATOR;

        TDemandOrder tDemandOrder = demandOrderMapper.getValidSinopecOrderByOrderNo(associatedNo);
        if (tDemandOrder == null) {
            log.error("==============中石化取消需求单失败:需求单不存在或已失效-客户单号:" + associatedNo + "==============");
            throw new BizException(EntrustDataExceptionEnum.SINOPEC_DEMANDORDER_NOT_EXIST);
        }
        if (CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfObjectionSinopec())) {
            log.error("==============中石化取消需求单失败:当前需求单异常，不能取消-客户单号:" + associatedNo + "==============");
            throw new BizException(EntrustDataExceptionEnum.SINOPEC_CANCEL_DEMAND_ORDER_IS_OBJECTION);
        }

        //插入日志
        addSinopecMessageLog(SinopecMessageLogTypeEnum.DEMANDORDER, SinopecMessageLogActionEnum.CANCLE, JSONObject.toJSONString(requestModel), requestModel.getIp(), operator);
        //异步取消需求单
        AsyncProcessQueue.execute(() -> sinopecCancelDemandV1(tDemandOrder, operator));
        //清除当前委托单号在redis中的key
        redisUtils.delete(CommonConstant.LOGISTICS_SINOPEC_ASSOCIATED + associatedNo);
    }

    /**
     * 中石化主动取消需求单(新)
     *
     * @param tDemandOrder 需求单
     * @param operator     操作人
     */
    @Transactional
    public void sinopecCancelDemandV1(TDemandOrder tDemandOrder, String operator) {
        //需求单状态变更
        TDemandOrder upDemandOrder = new TDemandOrder();
        upDemandOrder.setId(tDemandOrder.getId());
        upDemandOrder.setIfCancel(CommonConstant.INTEGER_ONE);
        upDemandOrder.setCancelType(CommonConstant.INTEGER_ONE);
        upDemandOrder.setCancelTime(new Date());
        commonBiz.setBaseEntityModify(upDemandOrder, operator);

        //新增取消需求单操作日志
        TDemandOrderOperateLogs logs = demandOrderCommonBiz.getDemandOrderOperateLogs(tDemandOrder.getId(), DemandOrderOperateLogsEnum.CANCEL_DEMAND_ORDER, operator, DemandOrderCancelTypeEnum.getEnum(upDemandOrder.getCancelType()).getValue());

        log.info("================中石化取消需求单,客户单号:" + tDemandOrder.getCustomerOrderCode() + "，需求单号:" + tDemandOrder.getDemandOrderCode() + "===================");
        //新增取消需求单事件
        TDemandOrderEvents event = demandOrderCommonBiz.generateEvent(tDemandOrder.getId(), tDemandOrder.getCompanyCarrierId(), DemandOrderEventsTypeEnum.CANCEL_DEMANDORDER, operator);
        demandOrderMapper.updateByPrimaryKeySelectiveEncrypt(upDemandOrder);
        demandOrderOperateLogsMapper.insertSelective(logs);
        demandOrderEventsMapper.insertSelective(event);
        //已调度需求单，则取消需求单下的运单,并根据运单状态设置需求单是否异常
        if (tDemandOrder.getEntrustStatus() > DemandOrderStatusEnum.WAIT_DISPATCH.getKey()) {
            cancelSinopecCarrierOrder(tDemandOrder.getId(), operator, upDemandOrder);
        }
        demandOrderMapper.updateByPrimaryKeySelectiveEncrypt(upDemandOrder);
    }

    /**
     * 中石化委托单验签
     *
     * @param content 需要验证的数据
     * @param sign    需要验证的签名
     * @return true:验签成功 false:验签失败
     */
    public boolean checkSinopecSign(String content, String sign) {
        try {
            ClassPathResource configPropertiesResource = new ClassPathResource(configKeyConstant.sinopecConfigProperties);
            ClassPathResource sinopecSecxConfigResource1 = new ClassPathResource(configKeyConstant.sinopecSecxConfig);
            InputStream configPropertiesResourceInputStream = configPropertiesResource.getInputStream();
            InputStream sinopecSecxConfigResource1InputStream = sinopecSecxConfigResource1.getInputStream();
            File file = new File("/tmp");
            if (!file.exists()) {
                file.mkdirs();
            }
            File tempFile = new File(file, "sdk.properties");
            File tempFile1 = new File(file, "secxConfig.json");
            if (!tempFile.exists()) {
                FileOutputStream fos = new FileOutputStream(tempFile);
                IOUtils.copy(configPropertiesResourceInputStream, fos);
            }
            if (!tempFile1.exists()) {
                FileOutputStream fos1 = new FileOutputStream(tempFile1);
                IOUtils.copy(sinopecSecxConfigResource1InputStream, fos1);
            }

            SecurityEngineDeal securityEngineDeal = ucspInterface.getInstance(tempFile.getAbsolutePath());
            boolean outData = ucspInterface.verifyPkcs7Sign(securityEngineDeal, Base64.decode(sign), content.getBytes(StandardCharsets.UTF_8));
            log.info("PKCS7验签= " + outData);
            return outData;
        } catch (Exception e) {
            log.warn("中石化委托单请求解签失败: ", e);
            return false;
        }
    }
}
