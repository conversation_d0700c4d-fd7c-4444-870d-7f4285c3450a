package com.logistics.tms.controller.carrierorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/21
 */
@Data
public class SearchCarrierOrderListForWebRequestModel extends AbstractPageForm<SearchCarrierOrderListForWebRequestModel> {

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("客户单号")
	private String customerOrderCode;

	@ApiModelProperty("状态：空 全部 20000 待提货 40000 待卸货 50000 待签收 60000 已签收 0 已取消 2 已放空")
	private Integer status;

	@ApiModelProperty("起点")
	private String loadAddress;

	@ApiModelProperty("卸点")
	private String unloadAddress;

	@ApiModelProperty("车牌号")
	private String vehicleNo;

	@ApiModelProperty("司机姓名")
	private String driver;

	@ApiModelProperty("规格")
	private String size;

	@ApiModelProperty("派车时间")
	private String dispatchTimeFrom;

	@ApiModelProperty("派车时间")
	private String dispatchTimeTo;

	@ApiModelProperty("预计到货时间")
	private String expectArrivalTimeFrom;

	@ApiModelProperty("预计到货时间")
	private String expectArrivalTimeTo;

	@ApiModelProperty("调度人员姓名")
	private String dispatchUserName;

	@ApiModelProperty("委托方公司")
	private String companyEntrustName;

	@ApiModelProperty("回收任务类型：1 日常回收，2 加急或节假日回收")
	private Integer recycleTaskType;

	@ApiModelProperty("需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
	private Integer entrustType;

	@ApiModelProperty("排序字段")
	private String sort;

	@ApiModelProperty("顺序 asc 升序 desc 倒序")
	private String order;

	private List<Long> excludeCarrierOrderIdList;


	@ApiModelProperty("是否按编码回收 0:否 1:是   v2.44")
	private Integer ifRecycleByCode;

	@ApiModelProperty("是否是额外补的运单  0：否 1：是  V2.6.8")
	private String ifExtCarrierOrder;


	@ApiModelProperty("需求单集合 v2.45")
	private List<String> demandOrderCodeList;

	@ApiModelProperty("运单集合 v2.45")
	private List<String> carrierOrderCodeList;
}
