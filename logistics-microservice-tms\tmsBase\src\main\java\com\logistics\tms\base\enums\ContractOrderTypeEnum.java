package com.logistics.tms.base.enums;

/**
 * 合同类型枚举
 * @Author: sj
 * @Date: 2019/4/8 9:13
 */
public enum ContractOrderTypeEnum {
    FRAME_CONTRACT(1, "框架合同"),
    SINGLE_CONTRACT(2, "单次合同"),
    ;

    private Integer key;
    private String value;

    ContractOrderTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
