package com.logistics.tms.biz.workgroup.sendmsg;

import com.google.common.collect.Maps;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.WorkGroupAmountTypeEnum;
import com.logistics.tms.base.enums.WorkGroupOrderTypeEnum;
import com.logistics.tms.biz.demandorder.DemandOrderForLeYiBiz;
import com.logistics.tms.biz.workgroup.sendmsg.model.WorkGroupDemandOrderForLeYiBoModel;
import com.logistics.tms.biz.workgroup.sendmsg.model.mapping.WorkGroupDemandOrderForLeYiMapping;
import com.logistics.tms.controller.demandorder.request.DemandOrderSearchForLeYiRequestModel;
import com.logistics.tms.controller.demandorder.response.DemandOrderForLeYiResponseModel;
import com.logistics.tms.entity.TDemandOrderAddress;
import com.logistics.tms.mapper.TDemandOrderAddressMapper;
import com.logistics.tms.mapper.TDemandOrderMapper;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Component
public class DemandOrderForLeYiWorkGroupPush extends AbstractWorkGroupPush {

    @Resource
    private TDemandOrderMapper tDemandOrderMapper;
    @Resource
    private TDemandOrderAddressMapper tDemandOrderAddressMapper;
    @Resource
    private DemandOrderForLeYiBiz demandOrderForLeYiBiz;

    @Override
    public WorkGroupOrderTypeEnum getOrderType() {
        return WorkGroupOrderTypeEnum.DEMAND_ORDER;
    }

    @Override
    protected WorkGroupAddress getOrderAddress(Long orderId) {
        TDemandOrderAddress demandOrderAddress = tDemandOrderAddressMapper.getByDemandOrderId(orderId);
        return new WorkGroupAddress()
                .setLoadProvinceId(demandOrderAddress.getLoadProvinceId())
                .setLoadCityId(demandOrderAddress.getLoadCityId())
                .setLoadWarehouse(demandOrderAddress.getLoadWarehouse())
                .setUnloadProvinceId(demandOrderAddress.getUnloadProvinceId())
                .setUnloadCityId(demandOrderAddress.getUnloadCityId())
                .setUnloadWarehouse(demandOrderAddress.getUnloadWarehouse());
    }

    @Override
    protected Map<Integer, BigDecimal> businessParamFilter(Long orderId) {
        return Optional.ofNullable(tDemandOrderMapper.selectByPrimaryKeyDecrypt(orderId))
                .map(c -> {
                    Map<Integer, BigDecimal> typeAmountMap = Maps.newHashMap();
                    typeAmountMap.put(WorkGroupAmountTypeEnum.ENTRUST_AMOUNT.getKey(), c.getGoodsAmount());
                    typeAmountMap.put(WorkGroupAmountTypeEnum.ARRANGED_AMOUNT.getKey(), c.getArrangedAmount());
                    typeAmountMap.put(WorkGroupAmountTypeEnum.NOT_ARRANGED_AMOUNT.getKey(), c.getNotArrangedAmount());
                    TDemandOrderAddress demandOrderAddress = tDemandOrderAddressMapper.getByDemandOrderId(orderId);
                    // 兼容时间要求条件
                    Optional.ofNullable(demandOrderAddress)
                            .filter(f -> Objects.nonNull(f.getExpectedLoadTime()))
                            .ifPresent(t -> {
                                LocalDate expectedLoadDay = DateUtils.date2LocalDate(t.getExpectedLoadTime());
                                LocalDate publishDay = DateUtils.date2LocalDate(c.getPublishTime());
                                BigDecimal duration = BigDecimal.valueOf(publishDay.until(expectedLoadDay, ChronoUnit.DAYS));
                                typeAmountMap.put(TIME_REQUIRE_KEY, duration);
                            });
                    return typeAmountMap;
                })
                .orElse(new HashMap<>());
    }

    @Override
    protected WorkGroupDemandOrderForLeYiBoModel getOrderDetail(Long orderId) {
        List<DemandOrderForLeYiResponseModel> list =
                demandOrderForLeYiBiz.searchListForLeYiByIds(new DemandOrderSearchForLeYiRequestModel(), Collections.singletonList(orderId));
        if (ListUtils.isEmpty(list)) {
            return null;
        }
        DemandOrderForLeYiResponseModel model = list.get(CommonConstant.INTEGER_ZERO);
        return MapperUtils.mapper(model, WorkGroupDemandOrderForLeYiBoModel.class, new WorkGroupDemandOrderForLeYiMapping());
    }
}
