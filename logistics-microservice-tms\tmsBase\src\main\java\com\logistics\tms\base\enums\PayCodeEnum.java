package com.logistics.tms.base.enums;
/**
 * <AUTHOR>
 * @date 2020/3/16 14:48
 */
public enum PayCodeEnum {
    SUCCESS(0, "成功"),
    UNKNOWN_ERROR(249998, "未知原因"),
	SERVICE_PAUSE(249999, "服务终止"),
	SYSTEM_BUSY(240003, "系统忙"),
	SYSTEM_EXCUTE_WRONG(240005, "系统执行错误"),
	SYSTEM_PARAM_WRONG(240006, "系统参数错误"),
	NOT_EXISTS(240007, "不存在"),
	FORBIDDEN(240009, "拒绝访问"),
	SYSTEM_ONT_SUPPORT(240010, "系统不支持"),
	EXPIRED(240011, "已过期"),
	SIGN_WRONG(240012, "签署错误"),
	DEVELOPER_IS_NOT_EXISTS(241107, "开发者不存在"),
	USER_IS_NOT_EXISTS(241207, "用户不存在"),
	USER_IS_ALREADY_EXISTS(241208, "用户已经存在"),
	USER_IMAGE_IS_NOT_EXISTS(241217, "用户图片不存在"),
	USER_CREDENTIAL_IS_NOT_EXISTS(241227, "用户凭证不存在"),
	CERTIFICATE_IS_NOT_EXISTS(241307, "证书不存在"),
	CERTIFICATE_IS_ALREADY_EXISTS(241308, "证书已存在"),
	APPLY_CERTIFICATE_FAILUR(241317, "申请证书失败"),
	CONTRACT_NOT_EXISTS(241407, "合同不存在"),
	CONTRACT_CAN_NOT_BE_REFUSED(241227, "合同不能被拒绝"),
	CONTRACT_IS_NOT_OPERATED(241415, "合同不能被操作"),
	CREATE_CONTRACT_FAILED(241418, "合同创建失败"),
	SIGN_CONTRACT_FAILED(241420, "合同签署失败"),
	NOT_THE_SIGNER_FOR_THE_CONTRACT(241421, "不是合同的签署者"),
	MICRO_SERVICE_ERROR(241501, "服务异常"),
	PERSONAL_REAL_NAME_AUTHENTICATION_ERROR(241601, "个人实名认证出错"),
	ENTERPRISE_REAL_NAME_AUTHENTICATION_ERROR(241602, "企业实名认证出错"),
	EXCEEDING_NUMBER(241603, "对公打款超过次数"),
	PUBLIC_PAYMENT_FAIL(241604, "对公打款失败"),
	RECORD_DOES_NOT_EXIST(241605, "对公打款记录不存在"),
	RECORD_EXPIRED(241606, "对公打款记录已经过期"),
	VALIDATION_EXCEEDED(241607, "对公打款金额验证超出次数"),
	GENERATE_URL_FAIL(241701, "生成企业版url出错"),
	DEFAULT(-999999," ");


    private Integer key;
    private String value;

    PayCodeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static PayCodeEnum getEnum(Integer key) {
        for (PayCodeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
