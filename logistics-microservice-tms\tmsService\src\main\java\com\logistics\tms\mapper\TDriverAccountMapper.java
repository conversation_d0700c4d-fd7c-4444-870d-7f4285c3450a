package com.logistics.tms.mapper;

import com.logistics.tms.controller.driveraccount.request.SearchDriverAccountRequestModel;
import com.logistics.tms.controller.driveraccount.response.BankCardInfoResponseModel;
import com.logistics.tms.controller.driveraccount.response.DriverAccountDetailResponseModel;
import com.logistics.tms.controller.driveraccount.response.SearchDriverAccountResponseModel;
import com.logistics.tms.entity.TDriverAccount;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Mybatis Generator on 2022/12/06
 */
@Mapper
public interface TDriverAccountMapper extends BaseMapper<TDriverAccount> {

	TDriverAccount selectByPrimaryKeyDecrypt(Long id);

	int insertSelectiveEncrypt(TDriverAccount tDriverAccount);

	int updateByPrimaryKeySelectiveEncrypt(TDriverAccount tDriverAccount);

	TDriverAccount selectByBankAccountDriverId(@Param("bankAccount") String bankAccount, @Param("driverId") Long driverId);

	List<SearchDriverAccountResponseModel> searchList(SearchDriverAccountRequestModel requestModel);

	DriverAccountDetailResponseModel getDetail(Long driverAccountId);

	TDriverAccount selectOneByDriverId(@Param("driverId") Long driverId);

	TDriverAccount selectOneByDriverAccount(@Param("bankAccount") String bankAccount);

	BankCardInfoResponseModel selectDetailByDriverId(@Param("driverId") Long driverId);
}