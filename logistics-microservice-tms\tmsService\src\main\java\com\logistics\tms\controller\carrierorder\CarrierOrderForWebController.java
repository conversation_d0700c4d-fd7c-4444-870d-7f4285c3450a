package com.logistics.tms.controller.carrierorder;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.carrierorder.CarrierOrderBiz;
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz;
import com.logistics.tms.controller.carrierorder.request.CarrierOrderDetailRequestModel;
import com.logistics.tms.controller.carrierorder.request.DownloadLadingBillRequestModel;
import com.logistics.tms.controller.carrierorder.request.GetCarrierOrderNodeAmountRequestModel;
import com.logistics.tms.controller.carrierorder.request.SearchCarrierOrderListForWebRequestModel;
import com.logistics.tms.controller.carrierorder.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/*
  前台运单管理
 */
@RestController
@RequestMapping(value = "/service/web/carrierOrderManagement")
public class CarrierOrderForWebController {

    @Autowired
    private CarrierOrderBiz carrierOrderBiz;
    @Autowired
    private CarrierOrderCommonBiz carrierOrderCommonBiz;

    /**
     * 前台运单列表
     *
     * @param requestModel 筛选条件
     * @return 运单列表
     */
    @ApiOperation(value = "查询运单列表")
    @PostMapping(value = "/searchList")
    public Result<PageInfo<SearchCarrierOrderListForWebResponseModel>> searchList(@RequestBody SearchCarrierOrderListForWebRequestModel requestModel) {
        return Result.success(carrierOrderBiz.searchCarrierOrderListForWeb(requestModel));
    }

    /**
     * 前台运单列表tab各状态运单数量统计
     *
     * @param requestModel 筛选条件
     * @return 运单列表tab各状态运单数量统计
     */
    @ApiOperation("运单列表tab统计")
    @PostMapping({"/statistics"})
    public Result<CarrierOrderStatisticsResponseModel> statistics(@RequestBody SearchCarrierOrderListForWebRequestModel requestModel) {
        return Result.success(carrierOrderBiz.carrierOrderStatisticsForWeb(requestModel));
    }

    /**
     * 查看运单详情
     *
     * @param requestModel 运单ID
     * @return 运单详情
     */
    @ApiOperation(value = "运单详情")
    @PostMapping(value = "/detail")
    public Result<CarrierOrderDetailForWebResponseModel> carrierOrderDetail(@RequestBody CarrierOrderDetailRequestModel requestModel) {
        return Result.success(carrierOrderBiz.carrierOrderDetailForWeb(requestModel));
    }

    /**
     * 查看运单详情单据查看
     *
     * @param requestModel 运单ID
     * @return 运单详情单据图片
     */
    @ApiOperation(value = "运单详情页-查看回单")
    @PostMapping(value = "/viewCarrierOrderTickets")
    public Result<ViewCarrierOrderTicketsResponseModel> viewCarrierOrderTickets(@RequestBody CarrierOrderDetailRequestModel requestModel) {
        return Result.success(carrierOrderBiz.viewCarrierOrderTickets(requestModel));
    }

    /**
     * 打印提货单
     *
     * @param requestModel 运单ID
     * @return 操作结果
     */
    @ApiOperation(value = "打印提货单")
    @PostMapping(value = "/printLadingBill")
    public Result<Boolean> printLadingBill(@RequestBody DownloadLadingBillRequestModel requestModel) {
        carrierOrderBiz.printLadingBill(requestModel);
        return Result.success(true);
    }

    /**
     * 运单各操作节点状态校验
     *
     * @param requestModel 运单ID 操作节点
     * @return 运单货物信息
     */
    @ApiOperation(value = "操作节点提货与卸货数量-并校验单据是否异常与节点操作")
    @PostMapping(value = "/getCarrierOrderNodeAmount")
    public Result<GetCarrierOrderNodeAmountResponseModel> getCarrierOrderNodeAmount(@RequestBody GetCarrierOrderNodeAmountRequestModel requestModel) {
        return Result.success(carrierOrderCommonBiz.getCarrierOrderNodeAmount(requestModel));
    }

    @ApiOperation(value = "根据运单id查询司机车辆信息(车主前台修改车辆时)")
    @PostMapping(value = "/getDriverVehicleInfoByCarrierOrderId")
    public Result<DriverVehicleInfoByCarrierOrderIdResponseModel> getDriverVehicleInfoByCarrierOrderId(@RequestBody CarrierOrderDetailRequestModel requestModel) {
        return Result.success(carrierOrderBiz.getDriverVehicleInfoByCarrierOrderIdForWeb(requestModel));
    }
}
