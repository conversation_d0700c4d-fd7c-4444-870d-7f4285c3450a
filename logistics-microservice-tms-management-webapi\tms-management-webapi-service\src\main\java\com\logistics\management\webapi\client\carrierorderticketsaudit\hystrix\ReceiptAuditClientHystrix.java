package com.logistics.management.webapi.client.carrierorderticketsaudit.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.carrierorderticketsaudit.ReceiptAuditClient;
import com.logistics.management.webapi.client.carrierorderticketsaudit.response.GetReceiptAuditDetailResponseModel;
import com.logistics.management.webapi.client.carrierorderticketsaudit.response.SearchReceiptAuditListResponseModel;
import com.logistics.management.webapi.client.carrierorderticketsaudit.request.GetReceiptAuditDetailRequestModel;
import com.logistics.management.webapi.client.carrierorderticketsaudit.request.ReceiptAgainAuditRequestModel;
import com.logistics.management.webapi.client.carrierorderticketsaudit.request.ReceiptAuditRequestModel;
import com.logistics.management.webapi.client.carrierorderticketsaudit.request.SearchReceiptAuditListRequestModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/03/27
 */
@Component
public class ReceiptAuditClientHystrix implements ReceiptAuditClient {

    @Override
    public Result<PageInfo<SearchReceiptAuditListResponseModel>> searchList(SearchReceiptAuditListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetReceiptAuditDetailResponseModel> getDetail(GetReceiptAuditDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> audit(ReceiptAuditRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> againAudit(ReceiptAgainAuditRequestModel requestModel) {
        return Result.timeout();
    }
}
