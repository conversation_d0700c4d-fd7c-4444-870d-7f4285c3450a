package com.logistics.management.webapi.base.utils;

import com.github.pagehelper.PageInfo;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;

import java.util.ArrayList;
import java.util.List;

public class ConvertPageInfoUtil {
    private ConvertPageInfoUtil() {
    }

    public static <D> PageInfo<D> convertPageInfo(PageInfo source, Class<D> destinationType, MapperMapping mapperMapping) {
        List sourceList = source.getList();
        if (ListUtils.isNotEmpty(sourceList)) {
            List<D> destList = MapperUtils.mapper(sourceList, destinationType, mapperMapping);
            PageInfo destPageInfo = MapperUtils.mapper(source, PageInfo.class);
            destPageInfo.setList(destList);
            return destPageInfo;
        } else {
            return new PageInfo(new ArrayList());
        }
    }

    public static <D> PageInfo<D> convertPageInfo(PageInfo source, Class<D> destinationType) {
        List sourceList = source.getList();
        if (ListUtils.isNotEmpty(sourceList)) {
            List<D> destList = MapperUtils.mapper(sourceList, destinationType);
            PageInfo destPageInfo = MapperUtils.mapper(source, PageInfo.class);
            destPageInfo.setList(destList);
            return destPageInfo;
        } else {
            return new PageInfo(new ArrayList());
        }
    }
}