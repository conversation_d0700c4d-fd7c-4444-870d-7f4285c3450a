package com.logistics.management.webapi.client.reserveapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ReserveBalanceApplyListItemModel {

    @ApiModelProperty("申请记录id")
    private Long applyId;

    @ApiModelProperty("状态：-1 已撤销，0 待业务审核，1 待财务审核，2 已驳回，3 待打款，4 已打款")
    private Integer status;

    @ApiModelProperty("申请日期")
    private Date applyDate;

    @ApiModelProperty("申请人姓名")
    private String staffName;

    @ApiModelProperty("申请人手机号")
    private String staffMobile;

    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("批准金额")
    private BigDecimal approveAmount;
}
