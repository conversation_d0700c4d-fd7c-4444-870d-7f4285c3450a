package com.logistics.tms.biz.region;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.EnabledEnum;
import com.logistics.tms.base.enums.IfValidEnum;
import com.logistics.tms.base.enums.YesOrNoEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.tms.controller.region.request.*;
import com.logistics.tms.controller.region.response.*;
import com.logistics.tms.entity.TRegion;
import com.logistics.tms.entity.TRegionCompany;
import com.logistics.tms.entity.TRegionItem;
import com.logistics.tms.mapper.TCompanyCarrierMapper;
import com.logistics.tms.mapper.TRegionCompanyMapper;
import com.logistics.tms.mapper.TRegionItemMapper;
import com.logistics.tms.mapper.TRegionMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.MapUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/10/18 18:28
 */
@Slf4j
@Service
public class RegionBiz {

    @Resource
    private CommonBiz commonBiz;
    @Resource
    private TRegionMapper tRegionMapper;
    @Resource
    private TRegionItemMapper tRegionItemMapper;
    @Resource
    private TRegionCompanyMapper tRegionCompanyMapper;
    @Resource
    private TCompanyCarrierMapper tCompanyCarrierMapper;

    /**
     * 物流大区新增修改
     *
     * @param requestModel
     */
    @Transactional
    public void saveOrModifyRegion(SaveOrModifyRegionRequestModel requestModel) {
        //新增或修改数据
        TRegion tRegion = new TRegion();
        tRegion.setRegionName(requestModel.getRegionName());
        tRegion.setContactName(requestModel.getRegionContactName());
        tRegion.setContactPhone(requestModel.getRegionContactPhone());

        Long regionId = requestModel.getRegionId();
        TRegion bdRegionOne = tRegionMapper.findByName(tRegion.getRegionName());
        if (requestModel.getRegionId() == null || requestModel.getRegionId() <= CommonConstant.LONG_ZERO) {//新增逻辑
            //大区名称唯一判断
            if (bdRegionOne != null) {
                throw new BizException(CarrierDataExceptionEnum.REGION_NAME_HAS_EXIST);
            }
            //判断省市是否被别的大区占用
            checkProvince(requestModel);

            //更新数据
            tRegion.setEnabled(EnabledEnum.DISABLED.getKey());
            commonBiz.setBaseEntityAdd(tRegion, BaseContextHandler.getUserName());
            tRegionMapper.insertSelective(tRegion);

            regionId = tRegion.getId();
        } else {
            //修改逻辑
            TRegion bdRegionTwo = tRegionMapper.selectByPrimaryKey(requestModel.getRegionId());
            if (bdRegionTwo == null) { //不存在该大区
                throw new BizException(CarrierDataExceptionEnum.REGION_INFO_IS_EMPTY);
            }
            if (bdRegionOne != null && !bdRegionOne.getId().equals(requestModel.getRegionId())) {
                throw new BizException(CarrierDataExceptionEnum.REGION_NAME_HAS_EXIST);
            }
            //判断省市是否被别的大区占用
            checkProvince(requestModel);

            //更新数据
            tRegion.setId(bdRegionTwo.getId());
            commonBiz.setBaseEntityModify(tRegion, BaseContextHandler.getUserName());
            tRegionMapper.updateByPrimaryKeySelective(tRegion);

            //处理省市信息
            List<Long> oldCityIdList = new ArrayList<>();//旧的市id
            Map<Long, Long> oldCityIdItemIdMap = new HashMap<>();//旧的市id-》明细表id
            List<TRegionItem> itemOldList = tRegionItemMapper.selectByRegionId(bdRegionTwo.getId());
            for (TRegionItem item : itemOldList) {
                oldCityIdList.add(item.getCityId());
                oldCityIdItemIdMap.put(item.getCityId(), item.getId());
            }
            List<ProvinceRequestModel> addItemModel = new ArrayList<>();
            for (ProvinceRequestModel model : requestModel.getProvinceDtoList()) {
                if (oldCityIdList.contains(model.getCityId())){
                    //市id存在，不作处理，从map里移除
                    oldCityIdItemIdMap.remove(model.getCityId());
                }else{
                    //市id不存在，则新增
                    addItemModel.add(model);
                }
            }
            //map还有数据，则是需要删除的
            TRegionItem delRegionItem;
            List<TRegionItem> delItemList = new ArrayList<>();
            if (!MapUtils.isEmpty(oldCityIdItemIdMap)){
                for (Map.Entry<Long, Long> entry : oldCityIdItemIdMap.entrySet()) {
                    delRegionItem = new TRegionItem();
                    delRegionItem.setId(entry.getValue());
                    delRegionItem.setValid(IfValidEnum.INVALID.getKey());
                    commonBiz.setBaseEntityModify(delRegionItem, BaseContextHandler.getUserName());
                    delItemList.add(delRegionItem);
                }
                tRegionItemMapper.batchUpdate(delItemList);
            }
            //需要新增的
            requestModel.setProvinceDtoList(addItemModel);

            //删除车主关系
            tRegionCompanyMapper.delByRegionId(bdRegionTwo.getId(), BaseContextHandler.getUserName(), new Date());
        }
        //新增省市
        if (ListUtils.isNotEmpty(requestModel.getProvinceDtoList())){
            TRegionItem regionItem;
            List<TRegionItem> itemList = new ArrayList<>();
            for (ProvinceRequestModel model : requestModel.getProvinceDtoList()) {
                regionItem = new TRegionItem();
                regionItem.setRegionId(regionId);
                regionItem.setProvinceId(model.getProvinceId());
                regionItem.setProvinceName(model.getProvinceName());
                regionItem.setCityId(model.getCityId());
                regionItem.setCityName(model.getCityName());
                commonBiz.setBaseEntityAdd(regionItem, BaseContextHandler.getUserName());
                itemList.add(regionItem);
            }
            tRegionItemMapper.batchInsert(itemList);
        }
        //新增车主关系
        if (ListUtils.isNotEmpty(requestModel.getCompanyCarrierIdList())){
            TRegionCompany regionCompany;
            List<TRegionCompany> regionCompanyList = new ArrayList<>();
            for (Long companyCarrierId : requestModel.getCompanyCarrierIdList()) {
                regionCompany = new TRegionCompany();
                regionCompany.setRegionId(regionId);
                regionCompany.setCompanyCarrierId(companyCarrierId);
                commonBiz.setBaseEntityAdd(regionCompany, BaseContextHandler.getUserName());
                regionCompanyList.add(regionCompany);
            }
            tRegionCompanyMapper.batchInsert(regionCompanyList);
        }
    }

    //判断省市是否被别的大区占用
    private void checkProvince(SaveOrModifyRegionRequestModel requestModel) {
        List<TRegionItem> bdRegionItemList = tRegionItemMapper.getByNotRegionId(requestModel.getRegionId());
        if (ListUtils.isNotEmpty(bdRegionItemList)) {
            //全部有效城市集合
            List<Long> cityIdList = bdRegionItemList.stream().map(TRegionItem::getCityId).collect(Collectors.toList());
            for (ProvinceRequestModel provinceRequestModel : requestModel.getProvinceDtoList()) {
                if (cityIdList.contains(provinceRequestModel.getCityId())) {
                    throw new BizException(CarrierDataExceptionEnum.CHOOSE_CITY_IS_OCCUPIED);
                }
            }
        }
    }

    /**
     * 编辑详情
     *
     * @param requestModel
     * @return
     */
    public RegionDetailResponseModel getDetail(RegionDetailRequestModel requestModel) {
        RegionDetailResponseModel detail = tRegionMapper.getDetail(requestModel.getRegionId());
        if (detail == null){
            throw new BizException(CarrierDataExceptionEnum.REGION_INFO_IS_EMPTY);
        }
        detail.setCompanyCarrierList(this.getRegionCompany(requestModel));
       return detail;
    }

    /**
     * 列表查询
     *
     * @param requestModel
     * @return
     */
    public PageInfo<SearchRegionResponseModel> searchList(SearchRegionRequestModel requestModel) {
        requestModel.enablePaging();
        List<Long> idList = tRegionMapper.searchListIds(requestModel);
        PageInfo pageInfo = new PageInfo(idList);
        if (ListUtils.isNotEmpty(idList)) {
            List<SearchRegionResponseModel> responseModelList = tRegionMapper.searchList(StringUtils.listToString(idList, ','));
            pageInfo.setList(responseModelList);
        }
        return pageInfo;
    }

    /**
     * 获取物流大区下车主列表
     * @param requestModel
     * @return
     */
    public List<RegionCompanyResponseModel> getRegionCompany(RegionDetailRequestModel requestModel){
        List<Long> companyCarrierIdList = tRegionCompanyMapper.getCompanyByRegionId(requestModel.getRegionId());
        if (ListUtils.isEmpty(companyCarrierIdList)){
            return new ArrayList<>();
        }

        //根据车主id查询车主信息
        List<FuzzySearchCompanyCarrierResponseModel> companyCarrierList = tCompanyCarrierMapper.selectCompanyCarrierInfoByIds(companyCarrierIdList);
        if (ListUtils.isEmpty(companyCarrierList)){
            return new ArrayList<>();
        }

        return MapperUtils.mapper(companyCarrierList, RegionCompanyResponseModel.class);
    }

    /**
     * 启用/禁用大区
     *
     * @param requestModel
     */
    @Transactional
    public void enableOrDisable(EnableRegionRequestModel requestModel) {
        //查找数据库中的该大区信息
        List<TRegion> dbRegionList = tRegionMapper.getByIds(requestModel.getRegionIds());
        if (ListUtils.isEmpty(dbRegionList)){
            throw new BizException(CarrierDataExceptionEnum.REGION_INFO_IS_EMPTY);
        }

        //更新状态
        TRegion upRegion;
        List<TRegion> upList = new ArrayList<>();
        for (TRegion tRegion : dbRegionList) {
            if (!tRegion.getEnabled().equals(requestModel.getEnabled())) {
                upRegion = new TRegion();
                upRegion.setId(tRegion.getId());
                upRegion.setEnabled(requestModel.getEnabled());
                commonBiz.setBaseEntityModify(upRegion, BaseContextHandler.getUserName());
                upList.add(upRegion);
            }
        }
        if (ListUtils.isNotEmpty(upList)) {
            tRegionMapper.batchUpdate(upList);
        }
    }

    /**
     * 移除大区
     *
     * @param requestModel
     */
    @Transactional
    public void removeRegion(RemoveRegionRequestModel requestModel) {
        TRegion dbRegion = tRegionMapper.selectByPrimaryKey(requestModel.getRegionId());
        if (dbRegion == null || IfValidEnum.INVALID.getKey().equals(dbRegion.getValid())){
            throw new BizException(CarrierDataExceptionEnum.REGION_INFO_IS_EMPTY);
        }
        if (dbRegion.getEnabled().equals(EnabledEnum.ENABLED.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.REGION_DELETE_ERROR);
        }

        //删除
        tRegionMapper.delRegionById(dbRegion.getId(), BaseContextHandler.getUserName(), new Date());
    }

    /**
     * 大区详情列表
     * @param requestModel
     * @return
     */
    public PageInfo<SearchRegionDetailResponseModel> searchDetailList(SearchRegionDetailRequestModel requestModel) {
        requestModel.enablePaging();
        List<SearchRegionDetailResponseModel> list = tRegionItemMapper.searchDetailList(requestModel);
        return new PageInfo<>(list);
    }

    /**
     * 根据省市查询区域下的车主（排除加入黑名单的车主）
     * @param requestModel
     * @return
     */
    public List<GetCompanyCarrierByRegionResponseModel> getCompanyByRegion(GetCompanyCarrierByRegionRequestModel requestModel){
        if (requestModel.getProvinceId() == null || requestModel.getCityId() == null){
            return new ArrayList<>();
        }

        //获取区域配置下的车主id
        List<Long> companyCarrierIdList = tRegionCompanyMapper.getCompanyByRegion(requestModel.getProvinceId(), requestModel.getCityId());
        if (ListUtils.isEmpty(companyCarrierIdList)){
            return new ArrayList<>();
        }

        //根据车主id查询车主信息
        List<FuzzySearchCompanyCarrierResponseModel> companyCarrierList = tCompanyCarrierMapper.selectCompanyCarrierInfoByIds(companyCarrierIdList);
        if (ListUtils.isEmpty(companyCarrierList)){
            return new ArrayList<>();
        }

        List<GetCompanyCarrierByRegionResponseModel> responseModelList = new ArrayList<>();
        GetCompanyCarrierByRegionResponseModel responseModel;
        for (FuzzySearchCompanyCarrierResponseModel model : companyCarrierList) {
            if (YesOrNoEnum.NO.getKey().equals(model.getIfAddBlacklist())){
                responseModel = MapperUtils.mapper(model, GetCompanyCarrierByRegionResponseModel.class);
                responseModelList.add(responseModel);
            }
        }
        return responseModelList;
    }
}
