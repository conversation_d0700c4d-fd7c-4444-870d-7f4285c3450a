<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCustomerAccountMapper" >
  <sql id="Base_Column_List_Decrypt">
    id, user_code, user_name, AES_DECRYPT(UNHEX(user_account),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as user_account,
    AES_DECRYPT(UNHEX(email),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as email, user_password, user_salt,
    AES_DECRYPT(UNHEX(user_head_path),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as user_head_path, open_id,
    created_by, created_time, last_modified_by, last_modified_time, valid, enabled, remark
  </sql>
  <select id="selectByPrimaryKeyDecrypt" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List_Decrypt" />
    from t_customer_account
    where id = #{id,jdbcType=BIGINT}
    and valid = 1
  </select>
  <insert id="insertSelectiveEncrypt" parameterType="com.logistics.tms.entity.TCustomerAccount" keyProperty="id" useGeneratedKeys="true">
    insert into t_customer_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userCode != null">
        user_code,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="userAccount != null">
        user_account,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="userPassword != null">
        user_password,
      </if>
      <if test="userSalt != null">
        user_salt,
      </if>
      <if test="userHeadPath != null">
        user_head_path,
      </if>
      <if test="openId != null">
        open_id,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userCode != null">
        #{userCode,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userAccount != null">
        HEX(AES_ENCRYPT(#{userAccount,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="email != null">
        HEX(AES_ENCRYPT(#{email,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="userPassword != null">
        #{userPassword,jdbcType=VARCHAR},
      </if>
      <if test="userSalt != null">
        #{userSalt,jdbcType=VARCHAR},
      </if>
      <if test="userHeadPath != null">
        HEX(AES_ENCRYPT(#{userHeadPath,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="openId != null">
        #{openId,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelectiveEncrypt" parameterType="com.logistics.tms.entity.TCustomerAccount">
    update t_customer_account
    <set>
      <if test="userCode != null">
        user_code = #{userCode,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userAccount != null">
        user_account = HEX(AES_ENCRYPT(#{userAccount,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="email != null">
        email = HEX(AES_ENCRYPT(#{email,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="userPassword != null">
        user_password = #{userPassword,jdbcType=VARCHAR},
      </if>
      <if test="userSalt != null">
        user_salt = #{userSalt,jdbcType=VARCHAR},
      </if>
      <if test="userHeadPath != null">
        user_head_path = HEX(AES_ENCRYPT(#{userHeadPath,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="openId != null">
        open_id = #{openId,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getByOpenId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List_Decrypt"/>
    from t_customer_account
    where valid = 1
    and open_id = #{openId,jdbcType=VARCHAR}
  </select>

  <select id="selectByMobile" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List_Decrypt"/>
    from t_customer_account
    where valid = 1
    and AES_DECRYPT(UNHEX(user_account),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') = #{mobile,jdbcType=VARCHAR}
  </select>

  <select id="selectByMobileAndRole" resultMap="getAccountAndRoleInfo_Map">
    select
    <include refid="Get_Account_And_Role_Info"/>
    from t_customer_account tca
    left join t_customer_account_relation tcar on tcar.account_id = tca.id and tcar.valid = 1
    where tca.valid = 1
    <if test="mobile != null and mobile != ''">
      and AES_DECRYPT(UNHEX(tca.user_account),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') = #{mobile,jdbcType=VARCHAR}
    </if>
    <if test="openId != null and openId != ''">
      and tca.open_id = #{openId,jdbcType=VARCHAR}
    </if>
    <if test="userRole != null">
      and tcar.user_role = #{userRole,jdbcType=INTEGER}
    </if>
  </select>

  <sql id="Get_Account_And_Role_Info">
    tca.id,
    tca.user_code,
    tca.user_name,
    AES_DECRYPT(UNHEX(tca.user_account),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as user_account,
    tca.user_password,
    tca.user_salt,

    tcar.id as relationId,
    tcar.user_role,
    tcar.account_id,
    tcar.user_id,
    tcar.if_close,
    tcar.enabled
  </sql>
  <resultMap id="getAccountAndRoleInfo_Map" type="com.logistics.tms.controller.customeraccount.response.AccountInfoResponseModel">
    <id column="id" property="accountId" jdbcType="BIGINT"/>
    <result column="user_code" property="userCode" jdbcType="VARCHAR"/>
    <result column="user_name" property="userName" jdbcType="VARCHAR"/>
    <result column="user_account" property="userAccount" jdbcType="VARCHAR"/>
    <result column="user_password" property="userPassword" jdbcType="VARCHAR"/>
    <result column="user_salt" property="userSalt" jdbcType="VARCHAR"/>
    <collection property="roles" ofType="com.logistics.tms.controller.customeraccount.response.AccountRolesModel">
      <id column="relationId" property="accountRoleRelationId" jdbcType="BIGINT"/>
      <result column="user_role" property="userRole" jdbcType="INTEGER"/>
      <result column="account_id" property="accountId" jdbcType="BIGINT"/>
      <result column="user_id" property="userId" jdbcType="BIGINT"/>
      <result column="if_close" property="ifClose" jdbcType="INTEGER"/>
      <result column="enabled" property="enabled" jdbcType="INTEGER"/>
    </collection>
  </resultMap>

  <select id="getAccountInfoBy" resultMap="getAccountAndRoleInfo_Map">
    select
    <include refid="Get_Account_And_Role_Info"/>
    from t_customer_account tca
    left join t_customer_account_relation tcar on tcar.valid = 1 and tcar.account_id = tca.id
    where tca.valid = 1
    <if test="mobile != null and mobile != ''">
      and AES_DECRYPT(UNHEX(tca.user_account),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') = #{mobile,jdbcType=VARCHAR}
    </if>
    <if test="accountId != null">
      and tca.id = #{accountId,jdbcType=BIGINT}
    </if>
    <if test="userRole != null">
      and tcar.user_role = #{userRole,jdbcType=INTEGER}
    </if>
  </select>
</mapper>