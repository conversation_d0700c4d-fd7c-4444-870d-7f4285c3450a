package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 中石化单个需求单发布
 *
 * @author: wei.wang
 * @date: 2021/12/3
 */
@Data
public class PublishSinopecDemandRequestDto {

	@ApiModelProperty("需求单id")
	@NotBlank(message = "需求单id不能为空")
	private String demandOrderId;

	@ApiModelProperty("发货省份id")
	@NotBlank(message = "发货省份id不能为空")
	private String loadProvinceId;
	@ApiModelProperty("发货省份名字")
	@NotBlank(message = "发货省份名字不能为空")
	private String loadProvinceName;
	@ApiModelProperty("发货城市id")
	@NotBlank(message = "发货城市id不能为空")
	private String loadCityId;
	@ApiModelProperty("发货城市名字")
	@NotBlank(message = "发货城市名字不能为空")
	private String loadCityName;
	@ApiModelProperty("发货县区id")
	@NotBlank(message = "发货县区id不能为空")
	private String loadAreaId;
	@ApiModelProperty("发货县区名字")
	@NotBlank(message = "发货县区名字不能为空")
	private String loadAreaName;
	@ApiModelProperty("发货详细地址")
	private String loadDetailAddress;

	@ApiModelProperty("收货省份id")
	@NotBlank(message = "收货省份id不能为空")
	private String unloadProvinceId;
	@ApiModelProperty("收货省份名字")
	@NotBlank(message = "收货省份名字不能为空")
	private String unloadProvinceName;
	@ApiModelProperty("收货城市id")
	@NotBlank(message = "收货城市id不能为空")
	private String unloadCityId;
	@ApiModelProperty("收货城市名字")
	@NotBlank(message = "收货城市名字不能为空")
	private String unloadCityName;
	@ApiModelProperty("收货县区id")
	@NotBlank(message = "收货县区id不能为空")
	private String unloadAreaId;
	@ApiModelProperty("收货县区名字")
	@NotBlank(message = "收货县区名字不能为空")
	private String unloadAreaName;
	@ApiModelProperty("收货详细地址")
	@NotBlank(message = "收货详细地址不能为空")
	private String unloadDetailAddress;

	@ApiModelProperty("调度人员姓名")
	@Pattern(regexp = "^[\\u4e00-\\u9fa5]{2,20}$", message = "调度人员姓名不许为空且必须是汉字并且长度控制在2~20字内")
	private String dispatcherName;
	@ApiModelProperty("调度人员电话")
	@Size(min = 1, max = 50, message = "调度人员电话不许为空且长度控制在1~50个字符内")
	private String dispatcherPhone;

	@ApiModelProperty(value = "货主价格类型：1 单价(元/吨，元/件)，2 一口价(元)",required = true)
	@NotBlank(message = "请选货主价格类型")
	private String contractPriceType;
	@ApiModelProperty(value = "货主价格",required = true)
	@NotBlank(message = "请填写货主价格")
	private String contractPrice;

	@ApiModelProperty(value = "是否我司 1:我司,2:其他车主", required = true)
	@NotBlank(message = "请选择接单车主类型")
	private String isOurCompany;
	@ApiModelProperty(value = "车主ID")
	private String companyCarrierId;
	@ApiModelProperty(value = "车主价格：1 单价(元/吨，元/件)，2 一口价(元)")
	private String carrierPriceType;
	@ApiModelProperty(value = "车主价格")
	private String carrierPrice;
}
