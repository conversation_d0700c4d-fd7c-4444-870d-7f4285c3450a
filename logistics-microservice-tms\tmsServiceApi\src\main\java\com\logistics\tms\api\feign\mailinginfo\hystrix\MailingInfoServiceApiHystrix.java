package com.logistics.tms.api.feign.mailinginfo.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.mailinginfo.MailingInfoServiceApi;
import com.logistics.tms.api.feign.mailinginfo.model.request.MailingInfoDetailRequestModel;
import com.logistics.tms.api.feign.mailinginfo.model.request.MailingInfoListRequestModel;
import com.logistics.tms.api.feign.mailinginfo.model.request.MailingInfoModifyRequestModel;
import com.logistics.tms.api.feign.mailinginfo.model.response.MailingInfoDetailResponseModel;
import com.logistics.tms.api.feign.mailinginfo.model.response.MailingInfoListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

@Component
public class MailingInfoServiceApiHystrix implements MailingInfoServiceApi {

    @Override
    public Result<PageInfo<MailingInfoListResponseModel>> mailingInfoList(MailingInfoListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<MailingInfoDetailResponseModel> mailingInfoDetail(MailingInfoDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> mailingInfoModify(MailingInfoModifyRequestModel requestModel) {
        return Result.timeout();
    }
}
