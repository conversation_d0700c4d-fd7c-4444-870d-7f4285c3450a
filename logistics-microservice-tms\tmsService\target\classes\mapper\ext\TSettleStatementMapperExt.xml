<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TSettleStatementMapper">
    <select id="carrierSettleStatementIdList" resultType="java.lang.Long">
        select distinct tss.id
        from t_settle_statement tss
        left join t_settle_statement_item tssi on tssi.settle_statement_id = tss.id and tssi.valid = 1
        where tss.valid = 1
          and tss.company_role = 2
          and tss.settle_statement_type = #{type,jdbcType=INTEGER}
        <if test="params.settleStatementStatus != null">
            and tss.settle_statement_status = #{params.settleStatementStatus,jdbcType=INTEGER}
        </if>
        <if test="type == 1">
            <choose>
                <when test="params.ifSelectCancel == 1">
                    and tss.settle_statement_status = -2
                </when>
                <otherwise>
                    and tss.settle_statement_status != -2
                </otherwise>
            </choose>
        </if>
        <if test="params.companyCarrierName != null and params.companyCarrierName != ''">
            and (
                    (tssi.type = 1 and instr(tssi.company_carrier_name, #{params.companyCarrierName,jdbcType=VARCHAR}))
                    or (tssi.type = 2 and (instr(tssi.contact_name, #{params.companyCarrierName,jdbcType=VARCHAR})
                    or instr(AES_DECRYPT(UNHEX(tssi.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'),
                             #{params.companyCarrierName,jdbcType=VARCHAR})))
                )
        </if>
        <if test="params.settleStatementCode != null and params.settleStatementCode != ''">
            and instr(tss.settle_statement_code, #{params.settleStatementCode,jdbcType=VARCHAR})
        </if>
        <if test="params.carrierOrderCode != null and params.carrierOrderCode != ''">
            and instr(tssi.carrier_order_code, #{params.carrierOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="params.settleStatementName != null and params.settleStatementName != ''">
            and instr(tss.settle_statement_name, #{params.settleStatementName,jdbcType=VARCHAR})
        </if>
        <if test="params.ifInvoice != null">
            and tss.if_invoice = #{params.ifInvoice,jdbcType=INTEGER}
        </if>
        <if test="params.settleStatementMonthStart != null and params.settleStatementMonthStart != ''">
            and tss.settle_statement_month &gt;= #{params.settleStatementMonthStart,jdbcType=VARCHAR}
        </if>
        <if test="params.settleStatementMonthEnd != null and params.settleStatementMonthEnd != ''">
            and tss.settle_statement_month &lt;= #{params.settleStatementMonthEnd,jdbcType=VARCHAR}
        </if>
        <if test="params.lastModifiedTimeStart != null and params.lastModifiedTimeStart != ''">
            and tss.last_modified_time &gt;= DATE_FORMAT(#{params.lastModifiedTimeStart,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%S')
        </if>
        <if test="params.lastModifiedTimeEnd != null and params.lastModifiedTimeEnd != ''">
            and tss.last_modified_time &lt;= DATE_FORMAT(#{params.lastModifiedTimeEnd,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="params.settleStatementIds != null and params.settleStatementIds != ''">
            and tss.id in (${params.settleStatementIds})
        </if>
        <if test="params.companyCarrierId != null">
            and tssi.company_carrier_id = #{params.companyCarrierId,jdbcType=BIGINT}
        </if>
        order by tss.last_modified_time desc, tss.id desc
    </select>

    <select id="carrierSettleStatementList" resultType="com.logistics.tms.controller.settlestatement.packaging.response.CarrierSettleStatementListResponseModel">
        select
        tss.id                      as settleStatementId,
        tss.settle_statement_name   as settleStatementName,
        tss.settle_statement_month  as settleStatementMonth,
        tss.settle_statement_code   as settleStatementCode,
        tss.settle_statement_status as settleStatementStatus,
        tss.platform_company_id     as platformCompanyId,
        tss.platform_company_name   as platformCompanyName,
        tss.freight_tax_point       as freightTaxPoint,
        tss.other_fee_tax_point     as otherFeeTaxPoint,
        tss.adjust_fee              as adjustFee,
        tss.adjust_remark           as adjustRemark,
        tss.contract_code           as contractCode,
        tss.if_invoice              as ifInvoice,
        tss.remark,
        tss.created_time            as createdTime,
        tss.last_modified_by        as lastModifiedBy,
        tss.last_modified_time      as lastModifiedTime
        from t_settle_statement tss
        WHERE tss.valid = 1
        and tss.id in (${ids})
        order by tss.last_modified_time desc, tss.id desc
    </select>

    <resultMap id="selectDetailById_map" type="com.logistics.tms.controller.settlestatement.packaging.response.CarrierSettleStatementDetailTotalResponseModel">
        <id column="id" property="settleStatementId"/>
        <result column="settleStatementStatus" property="settleStatementStatus"/>
        <result column="settleStatementName" property="settleStatementName"/>
        <result column="settleStatementMonth" property="settleStatementMonth"/>
        <result column="freightTaxPoint" property="freightTaxPoint"/>
        <result column="otherFeeTaxPoint" property="otherFeeTaxPoint"/>
        <result column="adjustFee" property="adjustFee"/>
        <result column="settleStatementCode" property="settleStatementCode"/>
        <result column="platformCompanyName" property="platformCompanyName"/>
        <result column="remark" property="remark"/>
        <result column="contractCode" property="contractCode"/>
        <result column="createdTime" property="createdTime"/>
        <collection property="settleStatementItemList" ofType="com.logistics.tms.controller.settlestatement.packaging.response.CarrierSettleStatementItemModel">
            <id column="settleStatementItemId" property="settleStatementItemId"/>
            <result column="settleStatementId" property="settleStatementId"/>
            <result column="carrier_order_id" property="carrierOrderId"/>
            <result column="settlement_amount" property="settlementAmount"/>
            <result column="goods_unit" property="goodsUnit"/>
            <result column="entrust_freight" property="entrustFreight"/>
            <result column="other_fees" property="otherFees"/>
            <result column="payable_fee" property="payableFee"/>
            <result column="company_carrier_id" property="companyCarrierId"/>
            <result column="company_carrier_name" property="companyCarrierName"/>
            <result column="type" property="companyCarrierType"/>
            <result column="contact_name" property="carrierContactName"/>
            <result column="carrierContactMobile" property="carrierContactMobile"/>
        </collection>
    </resultMap>

    <select id="selectDetailById" resultMap="selectDetailById_map">
        select
        tss.id,
        tss.settle_statement_status                                                                                             as settleStatementStatus,
        tss.settle_statement_name                                                                                               as settleStatementName,
        tss.settle_statement_month                                                                                              as settleStatementMonth,
        tss.settle_statement_code                                                                                               as settleStatementCode,
        tss.platform_company_name                                                                                               as platformCompanyName,
        tss.freight_tax_point                                                                                                   as freightTaxPoint,
        tss.other_fee_tax_point                                                                                                 as otherFeeTaxPoint,
        tss.adjust_fee                                                                                                          as adjustFee,
        tss.apply_total_fee                                                                                                     as carrierFreightTotal,
        tss.remark,
        tss.contract_code                                                                                                       as contractCode,
        tss.created_time                                                                                                        as createdTime,

        tssi.id                                                                                                                 as settleStatementItemId,
        tssi.settle_statement_id                                                                                                as settleStatementId,
        tssi.carrier_order_id,
        tssi.settlement_amount,
        tssi.goods_unit,
        tssi.entrust_freight,
        tssi.other_fees,
        tssi.payable_fee,
        tssi.company_carrier_id,
        tssi.company_carrier_name,
        tssi.type,
        tssi.contact_name,
        AES_DECRYPT(UNHEX(tssi.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactMobile
        from t_settle_statement tss
        left join t_settle_statement_item tssi on tssi.valid = 1 and tssi.settle_statement_id = tss.id
        WHERE tss.valid = 1
        and tss.id = #{settleStatementId,jdbcType=BIGINT}
        <if test="companyCarrierId != null">
            and tssi.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        </if>
        <if test="settleStatementType != null">
            and tss.settle_statement_type = #{settleStatementType}
        </if>
    </select>

    <select id="queryAdjustCost" resultType="com.logistics.tms.controller.settlestatement.packaging.response.CarrierAdjustCostResponseModel">
        select
        tss.id                    as settleStatementId,
        tss.freight_tax_point     as freightTaxPoint,
        tss.other_fee_tax_point   as otherFeeTaxPoint,
        tss.adjust_fee            as adjustCost,

        sum(tssi.entrust_freight) as carrierFreight,
        sum(tssi.other_fees)      as otherFee
        from t_settle_statement tss
        left join t_settle_statement_item tssi on tssi.valid = 1 and tssi.settle_statement_id = tss.id
        WHERE tss.valid = 1
          and tss.id = #{settleStatementId,jdbcType=BIGINT}
        group by tss.id
    </select>

    <resultMap id="getDetailByIds_map" type="com.logistics.tms.biz.settlestatement.model.CarrierSettleStatementDetailModel">
        <id column="id" property="settleStatementId"/>
        <result column="settleStatementStatus" property="settleStatementStatus"/>
        <result column="ifInvoice" property="ifInvoice"/>
        <collection property="settleStatementItemList" ofType="com.logistics.tms.biz.settlestatement.model.CarrierSettleStatementOrderDetailModel">
            <id column="settleStatementItemId" property="settleStatementItemId"/>
            <result column="carrier_order_id" property="carrierOrderId"/>
            <result column="company_carrier_id" property="companyCarrierId"/>
            <result column="company_carrier_name" property="companyCarrierName"/>
            <result column="type" property="companyCarrierType"/>
            <result column="contact_name" property="carrierContactName"/>
            <result column="carrierContactMobile" property="carrierContactMobile"/>
        </collection>
    </resultMap>
    <select id="getDetailByIds" resultMap="getDetailByIds_map">
        select
        tss.id,
        tss.settle_statement_status as settleStatementStatus,
        tss.if_invoice as ifInvoice,

        tssi.id as settleStatementItemId,
        tssi.carrier_order_id,
        tssi.company_carrier_id,
        tssi.company_carrier_name,
        tssi.type,
        tssi.contact_name,
        AES_DECRYPT(UNHEX(tssi.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactMobile
        from t_settle_statement tss
        left join t_settle_statement_item tssi on tssi.valid = 1 and tssi.settle_statement_id = tss.id
        WHERE tss.valid = 1
        and tss.id in (${ids})
        and tss.settle_statement_type = #{type,jdbcType=INTEGER}
    </select>

    <update id="batchUpdateSelective" parameterType="com.logistics.tms.entity.TSettleStatement">
        <foreach collection="recordList" item="item" separator=";">
            update t_settle_statement
            <set>
                <if test="item.settleStatementCode != null">
                    settle_statement_code = #{item.settleStatementCode,jdbcType=VARCHAR},
                </if>
                <if test="item.settleStatementType != null">
                    settle_statement_type = #{item.settleStatementType,jdbcType=INTEGER},
                </if>
                <if test="item.settleStatementName != null">
                    settle_statement_name = #{item.settleStatementName,jdbcType=VARCHAR},
                </if>
                <if test="item.settleStatementMonth != null">
                    settle_statement_month = #{item.settleStatementMonth,jdbcType=VARCHAR},
                </if>
                <if test="item.companyRole != null">
                    company_role = #{item.companyRole,jdbcType=INTEGER},
                </if>
                <if test="item.otherFeeTaxPoint != null">
                    other_fee_tax_point = #{item.otherFeeTaxPoint,jdbcType=DECIMAL},
                </if>
                <if test="item.freightTaxPoint != null">
                    freight_tax_point = #{item.freightTaxPoint,jdbcType=DECIMAL},
                </if>
                <if test="item.applyTotalFee != null">
                    apply_total_fee = #{item.applyTotalFee,jdbcType=DECIMAL},
                </if>
                <if test="item.adjustFee != null">
                    adjust_fee = #{item.adjustFee,jdbcType=DECIMAL},
                </if>
                <if test="item.adjustRemark != null">
                    adjust_remark = #{item.adjustRemark,jdbcType=VARCHAR},
                </if>
                <if test="item.settleStatementStatus != null">
                    settle_statement_status = #{item.settleStatementStatus,jdbcType=INTEGER},
                </if>
                <if test="item.auditName != null">
                    audit_name = #{item.auditName,jdbcType=VARCHAR},
                </if>
                <if test="item.auditTime != null">
                    audit_time = #{item.auditTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.platformCompanyId != null">
                    platform_company_id = #{item.platformCompanyId,jdbcType=BIGINT},
                </if>
                <if test="item.platformCompanyName != null">
                    platform_company_name = #{item.platformCompanyName,jdbcType=VARCHAR},
                </if>
                <if test="item.contractCode != null">
                    contract_code = #{item.contractCode,jdbcType=VARCHAR},
                </if>
                <if test="item.ifInvoice != null">
                    if_invoice = #{item.ifInvoice,jdbcType=INTEGER},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getAddSettleStatementIdList" resultType="java.lang.Long">
        select distinct tss.id
        from t_settle_statement tss
        left join t_settle_statement_item tssi on tssi.settle_statement_id = tss.id and tssi.valid = 1
        where tss.valid = 1
        and tss.company_role = 2
        and tss.settle_statement_type = #{type,jdbcType=INTEGER}
        and tssi.company_carrier_id = #{params.companyCarrierId,jdbcType=BIGINT}
        and tss.settle_statement_status = 2
        and tss.if_invoice = 0
        <if test="params.settleStatementMonthStart != null and params.settleStatementMonthStart != ''">
            and tss.settle_statement_month &gt;= #{params.settleStatementMonthStart,jdbcType=VARCHAR}
        </if>
        <if test="params.settleStatementMonthEnd != null and params.settleStatementMonthEnd != ''">
            and tss.settle_statement_month &lt;= #{params.settleStatementMonthEnd,jdbcType=VARCHAR}
        </if>
        order by tss.settle_statement_code desc
    </select>
    <select id="getAddSettleStatementList" resultType="com.logistics.tms.controller.invoicingmanagement.response.GetAddSettleStatementListResponseModel">
        select
        tss.id as settleStatementId,
        tss.settle_statement_name as settleStatementName,
        tss.settle_statement_month as settleStatementMonth,
        tss.settle_statement_code as settleStatementCode,
        tss.apply_total_fee as reconciliationFee
        from t_settle_statement tss
        where tss.valid = 1
        and tss.id in (${ids})
        order by tss.settle_statement_code desc
    </select>
</mapper>