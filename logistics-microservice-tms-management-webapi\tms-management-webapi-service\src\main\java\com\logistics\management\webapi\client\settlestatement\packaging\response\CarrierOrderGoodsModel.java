package com.logistics.management.webapi.client.settlestatement.packaging.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2019/2/22 10:48
 */
@Data
public class CarrierOrderGoodsModel {

    private String goodsName;

    private Integer length;

    private Integer width;

    private Integer height;

    private String goodsSize;

    private BigDecimal expectAmount;

    private BigDecimal loadAmount;

    private BigDecimal unloadAmount;

    private BigDecimal signAmount;
}
