package com.logistics.tms.base.constant;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class ConfigKeyConstant {
    //临时文件访问地址
    @Value("${images.fileAccessAddressTemp}")
    public String fileAccessAddressTemp;
    //正式文件访问地址
    @Value("${images.fileAccessAddress}")
    public String fileAccessAddress;
    //文件服务器域名
    @Value("${images.fileServerDomainName}")
    public String fileServerDomainName;
    //临时文件目录
    @Value("${images.tempImageUploadCatalog}")
    public String tempImageUploadCatalog;
    //正式文件目录
    @Value("${images.imageUploadCatalog}")
    public String imageUploadCatalog;
    //客户凭证保存目录
    @Value("${images.carrierCompanyImageCatalog}")
    public String customerCompanyImageCatalog;
    //文件来源
    @Value("${images.imageSource}")
    public String imageSource;

    //token有效期
    @Value("${token.expire}")
    public String tokenExp;
    @Value("${token.appExp}")
    public String tokenAppExp;

    //提货单
    @Value("${images.carrierOrder.loadTicketsCatalog}")
    public String carrierOrderLoadTicketsCatalog;
    //出库单
    @Value("${images.carrierOrder.unloadTicketsCatalog}")
    public String carrierOrderUnloadTicketsCatalog;
    //签收单
    @Value("${images.carrierOrder.signTicketsCatalog}")
    public String carrierOrderSignTicketsCatalog;
    //入库单
    @Value("${images.carrierOrder.stockInTicketsCatalog}")
    public String carrierOrderStockInTicketsCatalog;
    //其他单据
    @Value("${images.carrierOrder.otherTicketsCatalog}")
    public String carrierOrderOtherTicketsCatalog;
    //到达装货地凭证
    @Value("${images.carrierOrder.arriveLoadTicketsCatalog}")
    public String carrierOrderArriveLoadTicketsCatalog;
    //到达卸货地凭证
    @Value("${images.carrierOrder.arriveUnloadTicketsCatalog}")
    public String carrierOrderArriveUnloadTicketsCatalog;
    //公告通知url
    @Value("${images.webAnnouncementDetailUrl}")
    public String webAnnouncementDetailUrl;
    //公告图片保存目录
    @Value("${images.announcementContextCatlog}")
    public String announcementContextCatlog;
    //个人意外险保单保存目录
    @Value("${images.personalAccidentInsuranceCatalog}")
    public String personalAccidentInsuranceCatalog;
    //违章事故凭证
    @Value("${images.violationRegulationsCatalog}")
    public String violationRegulationsCatalog;
    //保险凭证保存目录
    @Value("${images.insuranceTicketsCatalog}")
    public String insuranceTicketsCatalog;
    //司机从业资格证
    @Value("${images.occupationalRecordCatalog}")
    public String occupationalRecordCatalog;
    //司机诚信考核
    @Value("${images.integrityExaminationRecordCatalog}")
    public String integrityExaminationRecordCatalog;
    //司机继续教育
    @Value("${images.continueLearningRecordCatalog}")
    public String continueLearningRecordCatalog;
    //人员信息证件
    @Value("${images.staffCertificateCatalog}")
    public String staffCertificateCatalog;
    //车辆资产信息
    @Value("${images.vehicleBasicInfoCatalog}")
    public String vehicleBasicInfoCatalog;
    //车辆资产信息
    @Value("${images.driverPayeeCatalog}")
    public String driverPayeeCatalog;
    //反馈信息
    @Value("${images.feedbackInfoCatalog}")
    public String feedbackInfoCatalog;
    //外部运单出库单
    @Value("${images.externalCarrierOrderCatalog}")
    public String externalCarrierOrderCatalog;
    //轮胎信息凭证
    @Value("${images.vehicleTireCatalog}")
    public String vehicleTireCatalog;
    //app首页图
    @Value("${images.appAdvertisementCatalog}")
    public String appAdvertisementCatalog;
    //合同附件目录
    @Value("${images.contractImageCatalog}")
    public String contractImageCatalog;
    //贷款记录附件
    @Value("${images.loanRecordImageCatalog}")
    public String loanRecordImageCatalog;
    //充油附件
    @Value("${images.oilFilledImageCatalog}")
    public String oilFilledImageCatalog;
    //车辆结算附件
    @Value("${images.vehicleSettlementAttachmentCatalog}")
    public String vehicleSettlementAttachmentCatalog;
    //保险费用附件
    @Value("${images.insuranceCostsCatalog}")
    public String insuranceCostsCatalog;

    //驾驶员安全承诺书
    @Value("${images.safePromiseCatalog}")
    public String safePromiseCatalog;
    //驾驶员安全例会
    @Value("${images.safeMeetingCatalog}")
    public String safeMeetingCatalog;
    //车辆安全检查
    @Value("${images.safeCheckCatalog}")
    public String safeCheckCatalog;
    //车辆安全检查-整改
    @Value("${images.safeCheckReformCatalog}")
    public String safeCheckReformCatalog;
    //外部车辆结算附件
    @Value("${images.extVehicleSettlementCatalog}")
    public String extVehicleSettlementCatalog;

    //安全领导小组会议附件
    @Value("${images.safetyGroupMeetingCatalog}")
    public String safetyGroupMeetingCatalog;
    //小程序司机确认对账图片
    @Value("${images.appletDriverSettleCatalog}")
    public String appletDriverSettleCatalog;
    //小程序司机确认对账图片
    @Value("${images.vehicleOutageCatalog}")
    public String vehicleOutageCatalog;
    //中石化需求单异常审核图片
    @Value("${images.demandOrderObjectionCatalog}")
    public String demandOrderObjectionCatalog;

    //中石化接口
    @Value("${sinopec.base_url}")
    public String sinopecBaseUrl;
    @Value("${sinopec.appkey}")
    public String sinopecAppkey;
    @Value("${sinopec.secretkey}")
    public String sinopecSecretkey;

    @Value("${sinopec.configProperties}")
    public String sinopecConfigProperties;

    @Value("${sinopec.sinopecSecxConfig}")
    public String sinopecSecxConfig;

    //大数据提供接口地址
    @Value("${intelligentLogistics.bigDataUrl}")
    public String bigDataUrl;

    //固定发运接口域名
    @Value("${intelligentLogistics.bigDataScheduleUrl}")
    public String bigDataScheduleUrl;
    //固定发运需求token
    @Value("${intelligentLogistics.bigDataScheduleToken}")
    public String bigDataScheduleToken;

    //司机费用申请凭证
    @Value("${images.driverCostApply}")
    public String driverCostApply;

    //临时费用单据
    @Value("${images.carrierOrderOtherFeeCatalog}")
    public String carrierOrderOtherFeeCatalog;

    //新生订单现场图片
    @Value("${images.renewableOrder.scenePicture}")
    public String renewableOrderScenePicture;
    //新生订单确认票据
    @Value("${images.renewableOrder.confirmPicture}")
    public String renewableOrderConfirmPicture;

    //司机新生下单预约记录现场图片
    @Value("${images.driverappoint.scenePicture}")
    public String driverAppointScenePicture;
    //司机新生下单预约记录确认票据
    @Value("${images.driverappoint.confirmPicture}")
    public String driverAppointConfirmPicture;

    //运单条形码
    @Value("${images.carrierOrderQrCode}")
    public String carrierOrderQrCode;

    //对账单归档单据
    @Value("${images.settleStatementArchivePic}")
    public String settleStatementArchivePic;

    // 小程序 - 考勤打卡图片
    @Value("${images.attendanceDutyPunchPicture}")
    public String attendanceDutyPunchPicture;

    // 备用金 - 确认打款票据
    @Value("${images.reserveApplyRemitPicture}")
    public String reserveApplyRemitPicture;

    //公司账户图片
    @Value("${images.companyAccountBankCardPic}")
    public String companyAccountBankCardPic;

    //司机账户图片
    @Value("${images.driverAccountBankCardPic}")
    public String driverAccountBankCardPic;

    //司机触达附件
    @Value("${images.driverReachAttachment}")
    public String driverReachAttachment;

    // 工单上报司机到达现场图片
    @Value("${images.workOrderDriverArriveScenePicture}")
    public String workOrderDriverArriveScenePicture;

    //私钥
    @Value("${faceAuthentication.returnUrl}") //人脸认证后的返回地址
    public String returnUrl;

    // 任务中心Host
    @Value("${client.host.workOrderCenter}")
    public String workOrderCenterHost;

    //归档文件
    @Value("${images.archivedFile}")
    public String archivedFile;

    // 二维码跳转通用前缀
    @Value("${qrcode.commonPrefix}")
    public String qrcodeCommonPrefix;

    //发票管理-发票图片
    @Value("${images.invoiceImage}")
    public String invoiceImage;

    //路线询价单附件
    @Value("${images.routeEnquiryAttachment}")
    public String routeEnquiryAttachment;

    //发票管理-归档文件
    @Value("${images.invoicingManagementArchiveFile}")
    public String invoicingManagementArchiveFile;

    //发票管理-归档文件
    @Value("${images.carrierOrderCodeFile}")
    public String carrierOrderCodeFile;

}
