package com.logistics.tms.controller.driversafemeeting.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/6/15 10:38
 */
@Data
public class DriverSafeMeetingKanBanItemResponseModel {
    @ApiModelProperty("安全例会ID")
    private Long safeMeetingId;
    @ApiModelProperty("驾驶员ID")
    private Long staffId;
    @ApiModelProperty("学习状态：0未学习，1已学习")
    private Integer status;
    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;
}
