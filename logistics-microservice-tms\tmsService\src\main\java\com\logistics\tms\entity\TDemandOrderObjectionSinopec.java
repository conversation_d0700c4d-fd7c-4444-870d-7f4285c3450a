package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TDemandOrderObjectionSinopec extends BaseEntity {
    /**
    * 需求单id
    */
    @ApiModelProperty("需求单id")
    private Long demandOrderId;

    /**
    * 发货省份ID
    */
    @ApiModelProperty("发货省份ID")
    private Long loadProvinceId;

    /**
    * 发货省份名字
    */
    @ApiModelProperty("发货省份名字")
    private String loadProvinceName;

    /**
    * 发货城市ID
    */
    @ApiModelProperty("发货城市ID")
    private Long loadCityId;

    /**
    * 发货城市名字
    */
    @ApiModelProperty("发货城市名字")
    private String loadCityName;

    /**
    * 发货县区id
    */
    @ApiModelProperty("发货县区id")
    private Long loadAreaId;

    /**
    * 发货县区名字
    */
    @ApiModelProperty("发货县区名字")
    private String loadAreaName;

    /**
    * 发货详细地址
    */
    @ApiModelProperty("发货详细地址")
    private String loadDetailAddress;

    /**
    * 收货省份ID
    */
    @ApiModelProperty("收货省份ID")
    private Long unloadProvinceId;

    /**
    * 收货省份名字
    */
    @ApiModelProperty("收货省份名字")
    private String unloadProvinceName;

    /**
    * 收货城市ID
    */
    @ApiModelProperty("收货城市ID")
    private Long unloadCityId;

    /**
    * 收货城市名字
    */
    @ApiModelProperty("收货城市名字")
    private String unloadCityName;

    /**
    * 收货县区id
    */
    @ApiModelProperty("收货县区id")
    private Long unloadAreaId;

    /**
    * 收货县区名字
    */
    @ApiModelProperty("收货县区名字")
    private String unloadAreaName;

    /**
    * 收货详细地址
    */
    @ApiModelProperty("收货详细地址")
    private String unloadDetailAddress;

    /**
    * 货主运费（单价）
    */
    @ApiModelProperty("货主运费（单价）")
    private BigDecimal contractPrice;

    /**
    * 调度员姓名
    */
    @ApiModelProperty("调度员姓名")
    private String dispatcherName;

    /**
    * 调度员电话
    */
    @ApiModelProperty("调度员电话")
    private String dispatcherPhone;

    /**
    * 上报备注
    */
    @ApiModelProperty("上报备注")
    private String remark;

    /**
    * 异常类型：1 已报价，2 已取消
    */
    @ApiModelProperty("异常类型：1 已报价，2 已取消")
    private Integer objectionType;

    /**
    * 审核状态：0 待审核，1 已审核，２ 已驳回
    */
    @ApiModelProperty("审核状态：0 待审核，1 已审核，２ 已驳回")
    private Integer auditStatus;

    /**
    * 审核人
    */
    @ApiModelProperty("审核人")
    private String auditorName;

    /**
    * 审核时间
    */
    @ApiModelProperty("审核时间")
    private Date auditTime;

    /**
    * 审核异常类型：1 已报价，2 已取消
    */
    @ApiModelProperty("审核异常类型：1 已报价，2 已取消")
    private Integer auditObjectionType;

    /**
    * 审核备注
    */
    @ApiModelProperty("审核备注")
    private String auditRemark;
}