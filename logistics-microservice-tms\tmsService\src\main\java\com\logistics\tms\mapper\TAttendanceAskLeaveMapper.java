package com.logistics.tms.mapper;
import java.util.Collection;

import com.logistics.tms.controller.leave.request.LeaveApplySearchListRequestModel;
import com.logistics.tms.controller.leave.response.DriverLeaveApplyDetailResponseModel;
import com.logistics.tms.controller.leave.response.DriverLeaveApplyListItemModel;
import com.logistics.tms.controller.leave.response.LeaveApplyDetailResponseModel;
import com.logistics.tms.controller.leave.response.LeaveApplySearchListResponseModel;
import com.logistics.tms.entity.TAttendanceAskLeave;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2022/11/10
*/
@Mapper
public interface TAttendanceAskLeaveMapper extends BaseMapper<TAttendanceAskLeave> {

    int updateByIdAndAuditStatus(@Param("updated")TAttendanceAskLeave updated,@Param("id")Long id,@Param("auditStatus")Integer auditStatus);

    TAttendanceAskLeave selectOneById(@Param("id")Long id);

    List<DriverLeaveApplyListItemModel> selectLeaveApplyList(@Param("staffId")Long staffId, @Param("applyTime")String applyTime);

    Long selectLeaveApplyListStatistical(@Param("staffId")Long staffId,@Param("applyTime")String applyTime);

    DriverLeaveApplyDetailResponseModel selectDriverLeaveApplyDetail(@Param("id")Long id, @Param("staffId")Long staffId);

    List<LeaveApplySearchListResponseModel> searchLeaveApplyList(@Param("params") LeaveApplySearchListRequestModel params);

    TAttendanceAskLeave selectOneByIdAndStaffId(@Param("id")Long id,@Param("staffId")Long staffId);

    LeaveApplyDetailResponseModel selectLeaveApplyDetail(@Param("id")Long id);

    List<TAttendanceAskLeave> selectAllByAuditStatusIn(@Param("staffId") Long staffId, @Param("auditStatusCollection")Collection<Integer> auditStatusCollection);
}