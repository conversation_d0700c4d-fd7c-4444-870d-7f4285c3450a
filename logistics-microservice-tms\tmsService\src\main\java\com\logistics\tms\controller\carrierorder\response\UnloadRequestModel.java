package com.logistics.tms.controller.carrierorder.response;

import com.logistics.tms.controller.carrierorder.request.CarrierOrderNodeGoodsRequestModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class UnloadRequestModel {
    @ApiModelProperty(value = "运单ID")
    private Long carrierOrderId;

    @ApiModelProperty("签收单")
    private List<String> tmpUrl;

    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("货物信息")
    private List<CarrierOrderNodeGoodsRequestModel> goodsList;
}
