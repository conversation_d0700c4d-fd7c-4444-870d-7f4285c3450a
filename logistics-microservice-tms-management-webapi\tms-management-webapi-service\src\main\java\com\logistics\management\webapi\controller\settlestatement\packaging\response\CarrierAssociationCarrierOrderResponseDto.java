package com.logistics.management.webapi.controller.settlestatement.packaging.response;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierAssociationCarrierOrderResponseDto {

	@ApiModelProperty("运单数量合计")
	private String carrierOrderAmount = "";

	@ApiModelProperty("结算费用")
	private String settlementCost = "";

	@ApiModelProperty("总吨数")
	private String totalWeightSettlementAmount = "";

	@ApiModelProperty("总件数")
	private String totalPackageSettlementAmount = "";

	@ApiModelProperty("总块数")
	private String totalPieceSettlementAmount = "";

	@ApiModelProperty("运单列表")
	private PageInfo<CarrierAssociationCarrierOrderItem> carrierOrderItemList;
}
