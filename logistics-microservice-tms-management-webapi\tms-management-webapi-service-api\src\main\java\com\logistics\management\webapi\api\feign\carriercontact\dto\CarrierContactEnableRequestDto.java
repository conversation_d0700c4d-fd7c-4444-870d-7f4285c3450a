package com.logistics.management.webapi.api.feign.carriercontact.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * liang current user system login name
 * 2018/9/18 current system date
 */
@Data
public class CarrierContactEnableRequestDto {

   @ApiModelProperty(value = "车主账号id",required = true)
   @NotBlank(message = "请选择账号")
   private String carrierContactId;

   @ApiModelProperty(value = "禁用启用类型 禁用0 启用1",required = true)
   @NotBlank(message = "选择操作动作：禁用0 启用1")
   private String updateEnable;
}
