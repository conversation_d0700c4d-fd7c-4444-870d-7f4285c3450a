package com.logistics.tms.mapper;

import com.logistics.tms.biz.shippingfreight.model.ShippingFreightRuleSqlConditionModel;
import com.logistics.tms.entity.TShippingFreightAddress;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

/**
* Created by Mybatis Generator on 2024/09/19
*/
@Mapper
public interface TShippingFreightAddressMapper extends BaseMapper<TShippingFreightAddress> {
    int deleteByPrimaryKey(Long id);

    int insert(TShippingFreightAddress record);

    int insertSelective(TShippingFreightAddress record);

    TShippingFreightAddress selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TShippingFreightAddress record);

    int updateByPrimaryKey(TShippingFreightAddress record);

    List<TShippingFreightAddress> listByCondition(@Param("param1") ShippingFreightRuleSqlConditionModel sqlConditionModel);

    int batchInsertSelective(@Param("list") List<TShippingFreightAddress> list);

    int batchUpdate(ArrayList<TShippingFreightAddress> updateShippingFreightAddresses);

    List<TShippingFreightAddress> selectByIds(@Param("param1") List<Long> shippingFreightRuleIds);

    List<TShippingFreightAddress> selectByShippingFreightId(@Param("shippingFreightId")Long shippingFreightId);
}