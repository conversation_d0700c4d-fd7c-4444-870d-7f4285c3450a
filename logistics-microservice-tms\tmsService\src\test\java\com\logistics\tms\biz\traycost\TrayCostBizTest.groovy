package com.logistics.tms.biz.traycost

import com.logistics.tms.api.feign.traycost.model.*
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TTrayCost
import com.logistics.tms.mapper.TTrayCostMapper
import com.yelo.tools.redis.utils.RedisUtils
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class TrayCostBizTest extends Specification {
    @Mock
    TTrayCostMapper tTrayCostMapper
    @Mock
    RedisUtils redisUtils
    @Mock
    CommonBiz commonBiz
    @InjectMocks
    TrayCostBiz trayCostBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Tray Cost List"() {
        given:
        when(tTrayCostMapper.searchTrayCostList()).thenReturn([new SearchTrayCostListResponseModel()])

        expect:
        trayCostBiz.searchTrayCostList() == expectedResult

        where:
        expectedResult << [new SearchTrayCostListResponseModel()]
    }

    @Unroll
    def "get Detail where requestModel=#requestModel then expect: #expectedResult"() {
        expect:
        trayCostBiz.getDetail(requestModel) == expectedResult

        where:
        requestModel                 || expectedResult
        new TrayCostIdRequestModel() || new TrayCostDetailResponseModel()
    }

    @Unroll
    def "modify Price where requestModel=#requestModel"() {
        given:
        when(tTrayCostMapper.getTopByEntrustTypeGoodsUnit(anyInt(), anyInt())).thenReturn(new TTrayCost(entrustType: 0, goodsUnit: 0, unitPrice: 0 as BigDecimal, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 33).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 33).getTime()))

        expect:
        trayCostBiz.modifyPrice(requestModel)
        assert expectedResult == false

        where:
        requestModel                  || expectedResult
        new ModifyPriceRequestModel() || true
    }

    @Unroll
    def "get Next Month Noe Day"() {
        expect:
        TrayCostBiz.getNextMonthNoeDay() == expectedResult

        where:
        expectedResult << 1l
    }

    @Unroll
    def "search Cost Records List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tTrayCostMapper.searchCostRecordsList(any())).thenReturn([new SearchCostRecordsListResponseModel()])

        expect:
        trayCostBiz.searchCostRecordsList(requestModel) == expectedResult

        where:
        requestModel                            || expectedResult
        new SearchCostRecordsListRequestModel() || null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme