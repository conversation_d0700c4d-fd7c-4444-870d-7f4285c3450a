package com.logistics.management.webapi.api.impl.vehicleassetmanagement.mapper;

import com.logistics.management.webapi.api.feign.vehicleassetmanagement.dto.VehicleAssetManagementListResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.enums.VehiclePropertyEnum;
import com.logistics.management.webapi.base.enums.VehicleUsagePropertyEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.vehicleassetmanagement.model.VehicleAssetManagementListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.math.BigDecimal;
import java.util.Optional;

public class SearchVehicleBasicListMapping extends MapperMapping<VehicleAssetManagementListResponseModel, VehicleAssetManagementListResponseDto> {
    @Override
    public void configure() {
        VehicleAssetManagementListResponseModel source = this.getSource();
        VehicleAssetManagementListResponseDto destination = this.getDestination();
        if (source != null) {
            destination.setVehiclePropertyLabel(VehiclePropertyEnum.getEnum(source.getVehicleProperty()).getValue());
            destination.setUsageProperty(VehicleUsagePropertyEnum.getEnum(source.getUsageProperty()).getValue());
            destination.setDataIntegrity(Optional.ofNullable(source.getIfComplete()).orElse(CommonConstant.INTEGER_ZERO).toString());
            destination.setTotalWeight(Optional.ofNullable(source.getTotalWeight()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
            destination.setApprovedLoadWeight(Optional.ofNullable(source.getApprovedLoadWeight()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());

            //车主名
            destination.setCompanyCarrierName(CompanyTypeEnum.COMPANY.getKey().equals(source.getCompanyCarrierType()) ?
                    source.getCompanyCarrierName() :
                    source.getCarrierContactName() + " "
                            + FrequentMethodUtils.encryptionData(source.getCarrierContactPhone(), EncodeTypeEnum.MOBILE_PHONE));

            //我司车辆才有停运原因
            if (!CommonConstant.INTEGER_ONE.equals(source.getIsOurCompany())) {
                destination.setOutageInfo("");
            }
        }
    }
}
