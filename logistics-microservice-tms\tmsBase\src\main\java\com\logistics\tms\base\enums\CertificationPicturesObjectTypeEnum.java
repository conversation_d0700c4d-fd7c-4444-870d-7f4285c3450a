package com.logistics.tms.base.enums;

public enum CertificationPicturesObjectTypeEnum {
    OBJECT_TYPE_DEFAULT_ENUM(0,""),
    T_PERSONAL_ACCIDENT_INSURANCE(1,"个人意外险表"),
    T_STAFF_DRIVER_OCCUPATIONAL_RECORD(2,"司机从业资格证记录表"),
    T_STAFF_DRIVER_CONTINUE_LEARNING_RECORD(3,"司机继续学习记录表"),
    T_STAFF_DRIVER_INTEGRITY_EXAMINATION_RECORD(4,"司机诚信考核记录表"),
    T_INSURANCE(5,"保险信息表"),
    T_VIOLATION_REGULATIONS(6,"车辆违章信息表"),
    T_VEHICLE_DRIVING_LICENSE_ANNUAL_REVIEW(7,"车辆行驶证检查记录表"),
    T_STAFF_BASIC(8,"人员基本信息表"),
    T_STAFF_DRIVER_CREDENTIAL(9,"司机证件表"),
    T_VEHICLE_GPS_RECORD(10,"车辆GPS安装记录表"),
    T_VEHICLE_GRADE_ESTIMATION_RECORD(11,"车辆等级评定表"),
    T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE_ANNUAL_REVIEW(12,"车辆道路运输证年审记录表"),
    T_VEHICLE_DRIVING_LICENSE(13,"车辆行驶证表"),
    T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE(14,"道路运输证表"),
    T_VEHICLE_BASIC(15,"车辆资产基础信息表"),
    T_VEHICLE_TIRE(16,"轮胎管理基础信息表"),
    T_DRIVER_PAYEE(17,"司机收款账号表"),
    T_FEEDBACK(18,"反馈表"),
    T_CARRIER_COMPLAINT(19,"车主投诉表"),
    T_CARRIER_FEEDBACK(20,"车主反馈表"),
    T_LOAN_RECORDS(21,"贷款记录表"),
    T_OIL_FILLED(22,"充油表"),
    T_VEHICLE_SETTLEMENT(23, "车辆结算表"),
    T_INSURANCE_COSTS(24, "保险费用表"),
    T_VEHICLE_SAFE_CHECK_VEHICLE(25, "车辆检查"),
    T_VEHICLE_SAFE_CHECK_REFORM(26, "车辆检查整改"),
    T_EXT_VEHICLE_SETTLEMENT(27, "外部车辆结算主表"),
    T_DEMAND_ORDER_OBJECTION_SINOPEC(28, "中石化需求单异常表"),
    T_DRIVER_COST(29, "司机费用申请"),
    T_RENEWABLE_AUDIT(30, "新生订单审核表"),
    T_DRIVER_APPOINT(31, "司机新生下单预约表"),
    T_CARRIER_ORDER_OTHER_FEE(32, "临时费用单据"),
    T_COMPANY_ACCOUNT_BANK_CARD_PIC(33, "公司账户银行卡图片"),
    T_RESERVE_APPLY_PIC(34, "备用金申请"),
    T_DRIVER_ACCOUNT_BANK_CARD_PIC(35, "司机账户银行卡图片"),
    T_COMPANY_CARRIER_AUTHORIZATION(36, "车主授权书"),
    ;
    private Integer objectType;
    private String note;

    CertificationPicturesObjectTypeEnum(Integer objectType, String note) {
        this.objectType = objectType;
        this.note = note;

    }

    public Integer getObjectType() {
        return objectType;
    }

    public String getNote() {
        return note;
    }

    public static CertificationPicturesObjectTypeEnum getEnum(Integer objectType) {
        for (CertificationPicturesObjectTypeEnum t : values()) {
            if (t.getObjectType().equals(objectType)) {
                return t;
            }
        }
        return OBJECT_TYPE_DEFAULT_ENUM;
    }
}
