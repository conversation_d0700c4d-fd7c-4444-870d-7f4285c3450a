package com.logistics.appapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;

@Data
public class PickupConfirmRequestDto {

    @ApiModelProperty(value = "运单Id", required = true)
    @NotBlank(message = "运单id不能为空")
    private String carrierOrderId;

    @ApiModelProperty(value = "提货方式 1 票据提货，2 二维码提货")
    @NotBlank(message = "请选择提货方式")
    @Range(min = 1, max = 2, message = "请选择正确的提货方式")
    public String deliveryMethod;
}
