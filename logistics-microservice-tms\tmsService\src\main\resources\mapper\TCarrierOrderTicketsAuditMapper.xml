<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderTicketsAuditMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCarrierOrderTicketsAudit" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="carrier_order_id" property="carrierOrderId" jdbcType="BIGINT" />
    <result column="tickets_audit_status" property="ticketsAuditStatus" jdbcType="INTEGER" />
    <result column="tickets_auditor_name" property="ticketsAuditorName" jdbcType="VARCHAR" />
    <result column="tickets_audit_time" property="ticketsAuditTime" jdbcType="TIMESTAMP" />
    <result column="if_automatic_audit" property="ifAutomaticAudit" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, carrier_order_id, tickets_audit_status, tickets_auditor_name, tickets_audit_time, 
    if_automatic_audit, remark, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_carrier_order_tickets_audit
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_carrier_order_tickets_audit
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCarrierOrderTicketsAudit" >
    insert into t_carrier_order_tickets_audit (id, carrier_order_id, tickets_audit_status, 
      tickets_auditor_name, tickets_audit_time, 
      if_automatic_audit, remark, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{carrierOrderId,jdbcType=BIGINT}, #{ticketsAuditStatus,jdbcType=INTEGER}, 
      #{ticketsAuditorName,jdbcType=VARCHAR}, #{ticketsAuditTime,jdbcType=TIMESTAMP}, 
      #{ifAutomaticAudit,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCarrierOrderTicketsAudit" >
    insert into t_carrier_order_tickets_audit
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="carrierOrderId != null" >
        carrier_order_id,
      </if>
      <if test="ticketsAuditStatus != null" >
        tickets_audit_status,
      </if>
      <if test="ticketsAuditorName != null" >
        tickets_auditor_name,
      </if>
      <if test="ticketsAuditTime != null" >
        tickets_audit_time,
      </if>
      <if test="ifAutomaticAudit != null" >
        if_automatic_audit,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderId != null" >
        #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="ticketsAuditStatus != null" >
        #{ticketsAuditStatus,jdbcType=INTEGER},
      </if>
      <if test="ticketsAuditorName != null" >
        #{ticketsAuditorName,jdbcType=VARCHAR},
      </if>
      <if test="ticketsAuditTime != null" >
        #{ticketsAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ifAutomaticAudit != null" >
        #{ifAutomaticAudit,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCarrierOrderTicketsAudit" >
    update t_carrier_order_tickets_audit
    <set >
      <if test="carrierOrderId != null" >
        carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="ticketsAuditStatus != null" >
        tickets_audit_status = #{ticketsAuditStatus,jdbcType=INTEGER},
      </if>
      <if test="ticketsAuditorName != null" >
        tickets_auditor_name = #{ticketsAuditorName,jdbcType=VARCHAR},
      </if>
      <if test="ticketsAuditTime != null" >
        tickets_audit_time = #{ticketsAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ifAutomaticAudit != null" >
        if_automatic_audit = #{ifAutomaticAudit,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCarrierOrderTicketsAudit" >
    update t_carrier_order_tickets_audit
    set carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      tickets_audit_status = #{ticketsAuditStatus,jdbcType=INTEGER},
      tickets_auditor_name = #{ticketsAuditorName,jdbcType=VARCHAR},
      tickets_audit_time = #{ticketsAuditTime,jdbcType=TIMESTAMP},
      if_automatic_audit = #{ifAutomaticAudit,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>