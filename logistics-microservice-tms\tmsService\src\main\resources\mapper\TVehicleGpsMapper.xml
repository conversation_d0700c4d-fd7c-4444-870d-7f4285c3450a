<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleGpsMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TVehicleGps" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="vehicle_id" property="vehicleId" jdbcType="BIGINT" />
    <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR" />
    <result column="driver_id" property="driverId" jdbcType="BIGINT" />
    <result column="driver_name" property="driverName" jdbcType="VARCHAR" />
    <result column="driver_mobile" property="driverMobile" jdbcType="VARCHAR" />
    <result column="driver_identity" property="driverIdentity" jdbcType="VARCHAR" />
    <result column="carrier_order_status" property="carrierOrderStatus" jdbcType="INTEGER" />
    <result column="vehicle_status" property="vehicleStatus" jdbcType="INTEGER" />
    <result column="upload_time" property="uploadTime" jdbcType="TIMESTAMP" />
    <result column="current_location" property="currentLocation" jdbcType="VARCHAR" />
    <result column="dispatch_order_id" property="dispatchOrderId" jdbcType="BIGINT" />
    <result column="dispatch_order_code" property="dispatchOrderCode" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, vehicle_id, vehicle_no, driver_id, driver_name, driver_mobile, driver_identity, 
    carrier_order_status, vehicle_status, upload_time, current_location, dispatch_order_id, 
    dispatch_order_code, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_vehicle_gps
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_vehicle_gps
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TVehicleGps" >
    insert into t_vehicle_gps (id, vehicle_id, vehicle_no, 
      driver_id, driver_name, driver_mobile, 
      driver_identity, carrier_order_status, vehicle_status, 
      upload_time, current_location, dispatch_order_id, 
      dispatch_order_code, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, #{vehicleNo,jdbcType=VARCHAR}, 
      #{driverId,jdbcType=BIGINT}, #{driverName,jdbcType=VARCHAR}, #{driverMobile,jdbcType=VARCHAR}, 
      #{driverIdentity,jdbcType=VARCHAR}, #{carrierOrderStatus,jdbcType=INTEGER}, #{vehicleStatus,jdbcType=INTEGER}, 
      #{uploadTime,jdbcType=TIMESTAMP}, #{currentLocation,jdbcType=VARCHAR}, #{dispatchOrderId,jdbcType=BIGINT}, 
      #{dispatchOrderCode,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TVehicleGps" >
    insert into t_vehicle_gps
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="vehicleId != null" >
        vehicle_id,
      </if>
      <if test="vehicleNo != null" >
        vehicle_no,
      </if>
      <if test="driverId != null" >
        driver_id,
      </if>
      <if test="driverName != null" >
        driver_name,
      </if>
      <if test="driverMobile != null" >
        driver_mobile,
      </if>
      <if test="driverIdentity != null" >
        driver_identity,
      </if>
      <if test="carrierOrderStatus != null" >
        carrier_order_status,
      </if>
      <if test="vehicleStatus != null" >
        vehicle_status,
      </if>
      <if test="uploadTime != null" >
        upload_time,
      </if>
      <if test="currentLocation != null" >
        current_location,
      </if>
      <if test="dispatchOrderId != null" >
        dispatch_order_id,
      </if>
      <if test="dispatchOrderCode != null" >
        dispatch_order_code,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null" >
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null" >
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null" >
        #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null" >
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null" >
        #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="driverIdentity != null" >
        #{driverIdentity,jdbcType=VARCHAR},
      </if>
      <if test="carrierOrderStatus != null" >
        #{carrierOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="vehicleStatus != null" >
        #{vehicleStatus,jdbcType=INTEGER},
      </if>
      <if test="uploadTime != null" >
        #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="currentLocation != null" >
        #{currentLocation,jdbcType=VARCHAR},
      </if>
      <if test="dispatchOrderId != null" >
        #{dispatchOrderId,jdbcType=BIGINT},
      </if>
      <if test="dispatchOrderCode != null" >
        #{dispatchOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TVehicleGps" >
    update t_vehicle_gps
    <set >
      <if test="vehicleId != null" >
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null" >
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null" >
        driver_id = #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null" >
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null" >
        driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="driverIdentity != null" >
        driver_identity = #{driverIdentity,jdbcType=VARCHAR},
      </if>
      <if test="carrierOrderStatus != null" >
        carrier_order_status = #{carrierOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="vehicleStatus != null" >
        vehicle_status = #{vehicleStatus,jdbcType=INTEGER},
      </if>
      <if test="uploadTime != null" >
        upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="currentLocation != null" >
        current_location = #{currentLocation,jdbcType=VARCHAR},
      </if>
      <if test="dispatchOrderId != null" >
        dispatch_order_id = #{dispatchOrderId,jdbcType=BIGINT},
      </if>
      <if test="dispatchOrderCode != null" >
        dispatch_order_code = #{dispatchOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TVehicleGps" >
    update t_vehicle_gps
    set vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      driver_id = #{driverId,jdbcType=BIGINT},
      driver_name = #{driverName,jdbcType=VARCHAR},
      driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      driver_identity = #{driverIdentity,jdbcType=VARCHAR},
      carrier_order_status = #{carrierOrderStatus,jdbcType=INTEGER},
      vehicle_status = #{vehicleStatus,jdbcType=INTEGER},
      upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      current_location = #{currentLocation,jdbcType=VARCHAR},
      dispatch_order_id = #{dispatchOrderId,jdbcType=BIGINT},
      dispatch_order_code = #{dispatchOrderCode,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>