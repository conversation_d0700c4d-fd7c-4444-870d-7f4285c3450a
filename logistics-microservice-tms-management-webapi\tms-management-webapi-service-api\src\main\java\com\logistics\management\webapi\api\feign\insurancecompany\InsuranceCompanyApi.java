package com.logistics.management.webapi.api.feign.insurancecompany;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.insurancecompany.dto.*;
import com.logistics.management.webapi.api.feign.insurancecompany.hystrix.InsuranceCompanyApiHystrix;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/5/29 14:13
 */
@Api(value = "API-InsuranceCompanyApi-配置中心-保险公司设置")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = InsuranceCompanyApiHystrix.class)
public interface InsuranceCompanyApi {

    @ApiOperation(value = "保险公司列表")
    @PostMapping(value = "/api/insuranceCompany/insuranceCompanyList")
    Result<PageInfo<InsuranceCompanyListResponseDto>> searchInsuranceCompanyList(@RequestBody InsuranceCompanyListRequestDto requestDto);

    @ApiOperation(value = "保险公司新增修改")
    @PostMapping(value = "/api/insuranceCompany/saveInsuranceCompany")
    Result<Boolean> saveOrModifyInsuranceCompany(@RequestBody SaveOrModifyInsuranceCompanyRequestDto requestDto);

    @ApiOperation(value = "查看详情")
    @PostMapping(value = "/api/insuranceCompany/getDetail")
    Result<InsuranceCompanyDetailResponseDto> getDetail(@RequestBody @Valid InsuranceCompanyDetailRequestDto requestDto);

    @ApiOperation(value = "启用/禁用保险公司")
    @PostMapping(value = "/api/insuranceCompany/enable")
    Result<Boolean> enableOrDisable(@RequestBody @Valid EnableInsuranceCompanyRequestDto requestDto);

    @ApiOperation(value = "导出")
    @GetMapping(value = "/api/insuranceCompany/export")
    void export(InsuranceCompanyListRequestDto requestDto, HttpServletResponse response);

    @ApiOperation(value = "导入")
    @PostMapping(value = "/api/insuranceCompany/import")
    Result<ImportInsuranceCompanyResponseDto> importInsuranceCompany(@RequestParam("file") MultipartFile file, HttpServletRequest request);

    @ApiOperation(value = "根据名称模糊匹配保险公司")
    @PostMapping(value = "/api/insuranceCompany/fuzzyQuery")
    Result<List<FuzzyQueryInsuranceCompanyListResponseDto>> fuzzyQueryInsuranceCompanyByName(@RequestBody FuzzyQueryInsuranceCompanyRequestDto requestDto);
}
