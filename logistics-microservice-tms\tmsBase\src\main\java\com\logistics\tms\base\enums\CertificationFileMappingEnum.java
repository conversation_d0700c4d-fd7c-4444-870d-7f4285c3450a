package com.logistics.tms.base.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/6/12 17:38
 */
public enum CertificationFileMappingEnum {

    CERTIFICATION_FILE_MAPPING_ENUM_NULL(null,-1,-1,""),
    VEHICLE_DRIVING_PERMIT_FRONT(CertificationPicturesObjectTypeEnum.T_VEHICLE_DRIVING_LICENSE,1,1,"机动车行驶证正面"),
    VEHICLE_DRIVING_PERMIT_BACK(CertificationPicturesObjectTypeEnum.T_VEHICLE_DRIVING_LICENSE,2,2,"机动车行驶证反面"),
    ROAD_TRANSPORT_CERTIFICATE_CARD_FRONT(CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE,3,1,"道路运输证(卡片)正面"),
    ROAD_TRANSPORT_CERTIFICATE_CARD_BACK(CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE,4,2,"道路运输证(卡片)反面"),
    ROAD_TRANSPORT_CERTIFICATE_PAPER_FRONT(CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE,5,3,"道路运输证(纸质)正面"),
    ROAD_TRANSPORT_CERTIFICATE_PAPER_BACK(CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE,6,4,"道路运输证(纸质)反面"),
    VEHICLE_BASIC_INSPECTION_PHOTOS(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,7,1,"车辆检查照片"),
    VEHICLE_BASIC_ACCESS_CHECKLIST(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,8,2,"危化品运输车辆准入检查表"),
    VEHICLE_BASIC_REGISTRATION_FRONT(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,9,3,"登记证正面"),
    VEHICLE_BASIC_REGISTRATION_BACK(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,10,4,"登记证反面"),
    VEHICLE_BASIC_QUALITY_CERTIFICATE(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,11,5,"车辆合格证"),
    VEHICLE_BASIC_PURCHASE_INVOICE(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,12,6,"车辆购置发票"),
    VEHICLE_BASIC_PURCHASE_TAX_INVOICE(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,13,7,"车辆购置税发票"),
    VEHICLE_BASIC_VESSEL_TAX_INVOICE(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,14,8,"挂车车船税发票"),
    VEHICLE_BASIC_VESSEL_OTHER(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,15,9,"凭证其他"),
    DRIVING_LICENSE_ANNUAL_REVIEW(CertificationPicturesObjectTypeEnum.T_VEHICLE_DRIVING_LICENSE_ANNUAL_REVIEW,16,1,"车辆行驶证检查记录凭证"),
    VEHICLE_ROAD_TRANSPORT_CERTIFICATE_ANNUAL_REVIEW(CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE_ANNUAL_REVIEW,17,1,"车辆道路运输证年审记录凭证"),
    VEHICLE_GPS_RECORD(CertificationPicturesObjectTypeEnum.T_VEHICLE_GPS_RECORD,18,1,"车辆GPS安装记录凭证"),
    VEHICLE_GRADE_ESTIMATION_RECORD(CertificationPicturesObjectTypeEnum.T_VEHICLE_GRADE_ESTIMATION_RECORD,19,1,"车辆等级评定凭证"),
    ;
    private CertificationPicturesObjectTypeEnum objectType;
    private Integer pageFileType;
    private Integer dbFileType;
    private String fileName;

    CertificationFileMappingEnum(CertificationPicturesObjectTypeEnum objectType, Integer pageFileType, Integer dbFileType, String fileName) {
        this.objectType = objectType;
        this.pageFileType = pageFileType;
        this.dbFileType = dbFileType;
        this.fileName = fileName;
    }

    public CertificationPicturesObjectTypeEnum getObjectType() {
        return objectType;
    }
    public Integer getPageFileType() {
        return pageFileType;
    }
    public Integer getDbFileType() {
        return dbFileType;
    }
    public String getFileName() {
        return fileName;
    }

    public static CertificationFileMappingEnum getEnumByPageType(Integer pageFileType) {
        for (CertificationFileMappingEnum t : values()) {
            if (t.getPageFileType().equals(pageFileType)) {
                return t;
            }
        }
        return CERTIFICATION_FILE_MAPPING_ENUM_NULL;
    }

    public static CertificationFileMappingEnum getEnumByDbType(Object obj,Integer dbFileType) {
        for (CertificationFileMappingEnum t : values()) {
            if (t.getObjectType() == obj && t.getDbFileType().equals(dbFileType)) {
                return t;
            }
        }
        return CERTIFICATION_FILE_MAPPING_ENUM_NULL;
    }

    public static List<CertificationFileMappingEnum> getFileTypeEnumList(Object obj){
        List<CertificationFileMappingEnum> fileTypeEnumList = new ArrayList<>();
        for (CertificationFileMappingEnum t : values()) {
            if (t.getObjectType() == obj) {
                fileTypeEnumList.add(t);
            }
        }
        return fileTypeEnumList;
    }
}
