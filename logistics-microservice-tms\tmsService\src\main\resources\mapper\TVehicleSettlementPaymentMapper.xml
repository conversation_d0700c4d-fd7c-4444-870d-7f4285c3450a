<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleSettlementPaymentMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TVehicleSettlementPayment" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="vehicle_settlement_id" property="vehicleSettlementId" jdbcType="BIGINT" />
    <result column="pay_company" property="payCompany" jdbcType="VARCHAR" />
    <result column="receiver_name" property="receiverName" jdbcType="VARCHAR" />
    <result column="pay_time" property="payTime" jdbcType="TIMESTAMP" />
    <result column="pay_fee" property="payFee" jdbcType="DECIMAL" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, vehicle_settlement_id, pay_company, receiver_name, pay_time, pay_fee, created_by, 
    created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_vehicle_settlement_payment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_vehicle_settlement_payment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TVehicleSettlementPayment" >
    insert into t_vehicle_settlement_payment (id, vehicle_settlement_id, pay_company, 
      receiver_name, pay_time, pay_fee, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{vehicleSettlementId,jdbcType=BIGINT}, #{payCompany,jdbcType=VARCHAR}, 
      #{receiverName,jdbcType=VARCHAR}, #{payTime,jdbcType=TIMESTAMP}, #{payFee,jdbcType=DECIMAL}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TVehicleSettlementPayment" >
    insert into t_vehicle_settlement_payment
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="vehicleSettlementId != null" >
        vehicle_settlement_id,
      </if>
      <if test="payCompany != null" >
        pay_company,
      </if>
      <if test="receiverName != null" >
        receiver_name,
      </if>
      <if test="payTime != null" >
        pay_time,
      </if>
      <if test="payFee != null" >
        pay_fee,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vehicleSettlementId != null" >
        #{vehicleSettlementId,jdbcType=BIGINT},
      </if>
      <if test="payCompany != null" >
        #{payCompany,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null" >
        #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null" >
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payFee != null" >
        #{payFee,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TVehicleSettlementPayment" >
    update t_vehicle_settlement_payment
    <set >
      <if test="vehicleSettlementId != null" >
        vehicle_settlement_id = #{vehicleSettlementId,jdbcType=BIGINT},
      </if>
      <if test="payCompany != null" >
        pay_company = #{payCompany,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null" >
        receiver_name = #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null" >
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payFee != null" >
        pay_fee = #{payFee,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TVehicleSettlementPayment" >
    update t_vehicle_settlement_payment
    set vehicle_settlement_id = #{vehicleSettlementId,jdbcType=BIGINT},
      pay_company = #{payCompany,jdbcType=VARCHAR},
      receiver_name = #{receiverName,jdbcType=VARCHAR},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      pay_fee = #{payFee,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>