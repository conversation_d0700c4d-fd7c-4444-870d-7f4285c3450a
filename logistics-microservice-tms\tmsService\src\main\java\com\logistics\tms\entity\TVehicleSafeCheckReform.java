package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleSafeCheckReform extends BaseEntity {
    /**
    * 车辆检查ID
    */
    @ApiModelProperty("车辆检查ID")
    private Long safeCheckVehicleId;

    /**
    * 整改数量
    */
    @ApiModelProperty("整改数量")
    private Integer reformCount;

    /**
    * 整改内容
    */
    @ApiModelProperty("整改内容")
    private String reformContent;

    /**
    * 添加整改时间
    */
    @ApiModelProperty("添加整改时间")
    private Date addReformTime;

    /**
    * 整改结果
    */
    @ApiModelProperty("整改结果")
    private String reformResult;

    /**
    * 添加整改时间
    */
    @ApiModelProperty("添加整改时间")
    private Date addReformResultTime;
}