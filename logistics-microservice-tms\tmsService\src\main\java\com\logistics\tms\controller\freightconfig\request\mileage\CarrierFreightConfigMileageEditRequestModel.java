package com.logistics.tms.controller.freightconfig.request.mileage;

import com.logistics.tms.controller.freightconfig.request.ladder.CarrierFreightConfigLadderRequestModel;
import com.logistics.tms.controller.freightconfig.request.scheme.CarrierFreightConfigSchemeEditRequestModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigMileageEditRequestModel extends CarrierFreightConfigSchemeEditRequestModel {

    @ApiModelProperty(value = "阶梯")
    private List<CarrierFreightConfigLadderRequestModel> ladderConfigList;
}
