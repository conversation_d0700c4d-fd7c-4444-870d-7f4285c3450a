package com.logistics.tms.controller.freightconfig;

import com.logistics.tms.biz.carrierfreight.service.CarrierFreightConfigRegionService;
import com.logistics.tms.controller.freightconfig.request.region.CarrierFreightConfigRegionAddRequestModel;
import com.logistics.tms.controller.freightconfig.request.region.CarrierFreightConfigRegionEditRequestModel;
import com.logistics.tms.controller.freightconfig.request.region.CarrierFreightConfigRegionRequestModel;
import com.logistics.tms.controller.freightconfig.response.region.CarrierFreightConfigRegionResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "区域计价配置管理")
@RequestMapping(value = "/service/freight/config/region")
public class CarrierFreightConfigRegionController {

    @Resource
    private CarrierFreightConfigRegionService carrierFreightConfigRegionService;


    @PostMapping(value = "/detail")
    @ApiOperation(value = "区域计价配置查看", tags = "1.3.5")
    Result<List<CarrierFreightConfigRegionResponseModel>> detail(@RequestBody CarrierFreightConfigRegionRequestModel requestModel) {
        return Result.success(carrierFreightConfigRegionService.detail(requestModel));
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "区域计价配置新增", tags = "1.3.5")
    Result<Boolean> add(@RequestBody CarrierFreightConfigRegionAddRequestModel requestModel) {
        return Result.success(carrierFreightConfigRegionService.add(requestModel));
    }

    @PostMapping(value = "/edit")
    @ApiOperation(value = "区域计价配置编辑", tags = "1.3.5")
    Result<Boolean> edit(@RequestBody CarrierFreightConfigRegionEditRequestModel requestModel) {
        return Result.success(carrierFreightConfigRegionService.edit(requestModel));
    }
}
