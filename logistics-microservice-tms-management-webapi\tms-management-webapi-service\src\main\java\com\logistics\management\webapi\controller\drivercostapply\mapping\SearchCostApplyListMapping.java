package com.logistics.management.webapi.controller.drivercostapply.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.DriverCostApplyTypeEnum;
import com.logistics.management.webapi.base.enums.DriverCostAuditEnum;
import com.logistics.management.webapi.base.enums.OilFilledInvoiceLEnum;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.enums.StaffPropertyEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.drivercostapply.response.SearchCostApplyListResponseModel;
import com.logistics.management.webapi.controller.drivercostapply.response.SearchCostApplyListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/5
 */
public class SearchCostApplyListMapping extends MapperMapping<SearchCostApplyListResponseModel, SearchCostApplyListResponseDto> {

	@Override
	public void configure() {
		SearchCostApplyListResponseModel source = getSource();
		SearchCostApplyListResponseDto destination = getDestination();

		if (source != null) {
			//司机展示名称
			destination.setStaffName(source.getStaffName() + "_" + (FrequentMethodUtils.encryptionData(source.getStaffMobile(), EncodeTypeEnum.MOBILE_PHONE)));
			//司机机构
			destination.setStaffPropertyLabel(StaffPropertyEnum.getEnum(source.getStaffProperty()).getValue());
			//审核状态
			destination.setAuditStatusLabel(DriverCostAuditEnum.getEnum(source.getAuditStatus()).getValue());
			//费用类型
			destination.setCostTypeLabel(DriverCostApplyTypeEnum.getEnum(source.getCostType()).getValue());
			//发生时间 天维度
			destination.setOccurrenceTime(DateFormatUtils.format(source.getOccurrenceTime(), CommonConstant.DATE_TO_STRING_YMD_PATTERN));

			//导出司机名
			destination.setExportStaffName(source.getStaffName() + "_" + source.getStaffMobile());

			//待审核状态不展示审核时间与审核人
			if (DriverCostAuditEnum.WAIT_BUSINESS_AUDIT.getKey().equals(source.getAuditStatus())) {
				destination.setAuditTime("");
				destination.setAuditorName("");
			}

			// 备用金单号处理
			List<String> reserveCodeList = source.getReserveCodeList();
			if (ListUtils.isNotEmpty(reserveCodeList)) {
				destination.setReserveCode(String.join(",", reserveCodeList));
			}
			// 垫付金额
			String advanceCostsString = Optional.ofNullable(source.getAdvanceCosts())
					.map(s -> s.stripTrailingZeros().toPlainString())
					.orElse(CommonConstant.ZERO);
			destination.setAdvanceCosts(advanceCostsString);
			destination.setInvoiceLabel(OilFilledInvoiceLEnum.getEnum(destination.getInvoice()).getValue());
		}
	}
}
