<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TInsuranceRefundMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TInsuranceRefund">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="settlement_month" jdbcType="VARCHAR" property="settlementMonth" />
    <result column="insurance_id" jdbcType="BIGINT" property="insuranceId" />
    <result column="insurance_type" jdbcType="INTEGER" property="insuranceType" />
    <result column="refund_cost" jdbcType="DECIMAL" property="refundCost" />
    <result column="refund_path" jdbcType="VARCHAR" property="refundPath" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, status, vehicle_id, vehicle_no, settlement_month, insurance_id, insurance_type, 
    refund_cost, refund_path, remark, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_insurance_refund
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_insurance_refund
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TInsuranceRefund">
    insert into t_insurance_refund (id, status, vehicle_id, 
      vehicle_no, settlement_month, insurance_id, 
      insurance_type, refund_cost, refund_path, 
      remark, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{vehicleId,jdbcType=BIGINT}, 
      #{vehicleNo,jdbcType=VARCHAR}, #{settlementMonth,jdbcType=VARCHAR}, #{insuranceId,jdbcType=BIGINT}, 
      #{insuranceType,jdbcType=INTEGER}, #{refundCost,jdbcType=DECIMAL}, #{refundPath,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TInsuranceRefund">
    insert into t_insurance_refund
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleNo != null">
        vehicle_no,
      </if>
      <if test="settlementMonth != null">
        settlement_month,
      </if>
      <if test="insuranceId != null">
        insurance_id,
      </if>
      <if test="insuranceType != null">
        insurance_type,
      </if>
      <if test="refundCost != null">
        refund_cost,
      </if>
      <if test="refundPath != null">
        refund_path,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="settlementMonth != null">
        #{settlementMonth,jdbcType=VARCHAR},
      </if>
      <if test="insuranceId != null">
        #{insuranceId,jdbcType=BIGINT},
      </if>
      <if test="insuranceType != null">
        #{insuranceType,jdbcType=INTEGER},
      </if>
      <if test="refundCost != null">
        #{refundCost,jdbcType=DECIMAL},
      </if>
      <if test="refundPath != null">
        #{refundPath,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TInsuranceRefund">
    update t_insurance_refund
    <set>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="settlementMonth != null">
        settlement_month = #{settlementMonth,jdbcType=VARCHAR},
      </if>
      <if test="insuranceId != null">
        insurance_id = #{insuranceId,jdbcType=BIGINT},
      </if>
      <if test="insuranceType != null">
        insurance_type = #{insuranceType,jdbcType=INTEGER},
      </if>
      <if test="refundCost != null">
        refund_cost = #{refundCost,jdbcType=DECIMAL},
      </if>
      <if test="refundPath != null">
        refund_path = #{refundPath,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TInsuranceRefund">
    update t_insurance_refund
    set status = #{status,jdbcType=INTEGER},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      settlement_month = #{settlementMonth,jdbcType=VARCHAR},
      insurance_id = #{insuranceId,jdbcType=BIGINT},
      insurance_type = #{insuranceType,jdbcType=INTEGER},
      refund_cost = #{refundCost,jdbcType=DECIMAL},
      refund_path = #{refundPath,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>