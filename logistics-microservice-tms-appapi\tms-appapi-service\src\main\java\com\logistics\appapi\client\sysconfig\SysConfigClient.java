package com.logistics.appapi.client.sysconfig;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.sysconfig.hystrix.SysConfigClientHystrix;
import com.logistics.appapi.client.sysconfig.request.SysConfigRequestModel;
import com.logistics.appapi.client.sysconfig.response.SysConfigResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/7 17:17
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/sysConfig",
        fallback = SysConfigClientHystrix.class)
public interface SysConfigClient {

    @ApiOperation(value = "批量获取系统配置", tags = "1.2.6")
    @PostMapping(value = "/batchGet")
    Result<List<SysConfigResponseModel>> batchGetSysConfig(@RequestBody Collection<SysConfigRequestModel> requestModel);
}
