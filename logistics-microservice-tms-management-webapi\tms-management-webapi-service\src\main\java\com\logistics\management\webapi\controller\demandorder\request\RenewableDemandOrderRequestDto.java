package com.logistics.management.webapi.controller.demandorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class RenewableDemandOrderRequestDto extends AbstractPageForm<RenewableDemandOrderRequestDto> {

    @ApiModelProperty("需求单状态：空 全部 500待发布 1000待调度 2000部分调度 3000调度完成 4000待签收 5000已签收 1取消")
    private String demandStatus;

    @ApiModelProperty(value = "需求单号")
    private String demandOrderCode;

    @ApiModelProperty(value = "客户")
    private String customerName;

    @ApiModelProperty(value = "发货地址")
    private String loadDetailAddress;

    @ApiModelProperty(value = "发货人")
    private String consignor;

    @ApiModelProperty(value = "收货仓库")
    private String unloadWarehouse;

    @ApiModelProperty(value = "收货地址")
    private String unloadDetailAddress;

    @ApiModelProperty(value = "收货人")
    private String receiver;

    @ApiModelProperty(value = "客户单号")
    private String customerNo;

    @ApiModelProperty(value = "下单开始时间")
    private String publishStartTime;

    @ApiModelProperty(value = "下单结束时间")
    private String publishEndTime;

    @ApiModelProperty("客户订单来源：1 乐橘新生客户，2 司机")
    private String customerOrderSource;

    /**
     * (3.23.0)委托类型：100 新生回收，101 新生销售
     */
    @ApiModelProperty("委托类型：100 新生回收，101 新生销售")
    private String entrustType;

    @ApiModelProperty(value = "品名")
    private String goodsName;

    @ApiModelProperty("拼单助手需求单号")
    private List<String> demandOrderCodeList;

    @ApiModelProperty("拼单助手客户单号")
    private List<String> customerOrderCodeList;

    @ApiModelProperty(value = "选择性导出，传入选择的ids,多个逗号分隔")
    private String demandIds;

    @ApiModelProperty("排序字段")
    private String sort;

    @ApiModelProperty("顺序 asc 升序 desc 倒序")
    private String order;
}
