package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2023/12/21
*/
@Data
public class TWorkGroupNode extends BaseEntity {
    /**
    * 智能推送配置表id
    */
    @ApiModelProperty("智能推送配置表id")
    private Long workGroupId;

    /**
    * 单据类型, 10:需求单 20:运单
    */
    @ApiModelProperty("单据类型, 10:需求单 20:运单")
    private Integer orderType;

    /**
    * 单据节点, 101:下单 102:调度 103:取消；201:提货 202:纠错 203:取消 204:生成运单 205:修改车辆 206:卸货 207:到达提货地 208:到达卸货地
    */
    @ApiModelProperty("单据节点, 101:下单 102:调度 103:取消；201:提货 202:纠错 203:取消 204:生成运单 205:修改车辆 206:卸货 207:到达提货地 208:到达卸货地")
    private Integer orderNode;

    /**
    * 时间要求(<= 天)
    */
    @ApiModelProperty("时间要求(<= 天)")
    private Integer timeRequire;

    /**
    * 取值要求, 101:委托数 102:已安排 103:未安排；201:预提数 202:实提数 203:差异数(预提-实提)
    */
    @ApiModelProperty("取值要求, 101:委托数 102:已安排 103:未安排；201:预提数 202:实提数 203:差异数(预提-实提)")
    private Integer amountType;

    /**
    * 取值要求符号, 1: >= 2: = 3: <=
    */
    @ApiModelProperty("取值要求符号, 1: >= 2: = 3: <=")
    private Integer amountSymbol;

    /**
    * 取值要求
    */
    @ApiModelProperty("取值要求")
    private BigDecimal amountRequire;
}