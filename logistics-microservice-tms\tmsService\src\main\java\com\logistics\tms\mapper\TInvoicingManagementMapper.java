package com.logistics.tms.mapper;

import com.logistics.tms.controller.invoicingmanagement.request.SearchInvoicingManagementListRequestModel;
import com.logistics.tms.controller.invoicingmanagement.response.InvoicingManagementDetailResponseModel;
import com.logistics.tms.controller.invoicingmanagement.response.SearchInvoicingManagementListResponseModel;
import com.logistics.tms.entity.TInvoicingManagement;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/03/20
*/
@Mapper
public interface TInvoicingManagementMapper extends BaseMapper<TInvoicingManagement> {

    TInvoicingManagement selectByPrimaryKeyDecrypt(@Param("id") Long id);

    int insertSelectiveEncrypt(TInvoicingManagement tInvoicingManagement);

    int updateByPrimaryKeySelectiveEncrypt(TInvoicingManagement tInvoicingManagement);

    List<SearchInvoicingManagementListResponseModel> searchList(@Param("params") SearchInvoicingManagementListRequestModel requestModel, @Param("businessType") Integer businessType);

    InvoicingManagementDetailResponseModel getDetail(@Param("id") Long id, @Param("businessType") Integer businessType);

    TInvoicingManagement getByIdAndType(@Param("id") Long id, @Param("businessType") Integer businessType);

}