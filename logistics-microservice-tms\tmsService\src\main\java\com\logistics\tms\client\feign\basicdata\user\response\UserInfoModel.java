package com.logistics.tms.client.feign.basicdata.user.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2023/12/25 13:41
 */
@Data
public class UserInfoModel {
    @ApiModelProperty("用户id")
    private Long userId;
    @ApiModelProperty("用户名")
    private String userName;
    @ApiModelProperty("用户登录账户")
    private String userAccount;
    @ApiModelProperty("1启用 0禁用")
    private Integer enabled;
    @ApiModelProperty("手机号")
    private String mobilePhone;
    @ApiModelProperty("组织结构编码")
    private String orgCode;
}
