package com.logistics.tms.controller.oilfilled;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.oilfilled.OilFilledBiz;
import com.logistics.tms.controller.oilfilled.request.*;
import com.logistics.tms.controller.oilfilled.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2023/8/8 15:08
 */
@RestController
@RequestMapping(value = "/service/oilFilled")
public class OilFilledController {

    @Resource
    private OilFilledBiz oilFilledBiz;

    /**
     * 充油列表
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/searchList")
    public Result<PageInfo<OilFilledListResponseModel>> searchList(@RequestBody OilFilledListRequestModel requestModel){
        return Result.success(oilFilledBiz.searchList(requestModel));
    }

    /**
     * 新增/修改充油
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/addOrModify")
    public Result<Boolean> addOrModify(@RequestBody AddOrModifyOilFilledRequestModel requestModel){
        oilFilledBiz.addOrModify(requestModel);
        return Result.success(true);
    }

    /**
     * 充油列表汇总
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/getSummary")
    @ApiOperation(value = "充油列表汇总")
    public Result<OilFilledGetSummaryResponseModel> getSummary(@RequestBody OilFilledListRequestModel requestModel){
        return Result.success(oilFilledBiz.getSummary(requestModel));
    }

    /**
     * 充油详情接口
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/getDetail")
    public Result<OilFilledDetailResponseModel> getDetail(@RequestBody OilFilledDetailRequestModel requestModel){
        return Result.success(oilFilledBiz.getDetail(requestModel));
    }

    /**
     * 充油操作记录
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/getOperationRecord")
    public Result<List<OilFilledOperationRecordResponseModel>> getOperationRecord(@RequestBody OilFilledOperationRecordRequestModel requestModel){
        return Result.success(oilFilledBiz.getOperationRecord(requestModel));
    }

    /**
     * 新增/修改油费退款
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/addOrModifyRefund")
    public Result<Boolean> addOrModifyRefund(@RequestBody AddOrModifyOilRefundRequestModel requestModel){
        oilFilledBiz.addOrModifyRefund(requestModel);
        return Result.success(true);
    }

    /**
     * 油费退款详情
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/getOilRefundDetail")
    public Result<OilRefundDetailResponseModel> getOilRefundDetail(@RequestBody OilFilledDetailRequestModel requestModel){
        return Result.success(oilFilledBiz.getOilRefundDetail(requestModel));
    }

    /**
     * 导入充油卡
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/importRefuelCard")
    public Result<ImportOilFilledResponseModel> importRefuelCard(@RequestBody ImportOilFilledCardInfoRequestModel requestModel){
        return Result.success(oilFilledBiz.importRefuelCard(requestModel));
    }

    /**
     * 导入加油车
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/importRefuelCar")
    public Result<ImportOilFilledResponseModel> importRefuelCar(@RequestBody ImportOilFilledCarInfoRequestModel requestModel){
        return Result.success(oilFilledBiz.importRefuelCar(requestModel));
    }

}
