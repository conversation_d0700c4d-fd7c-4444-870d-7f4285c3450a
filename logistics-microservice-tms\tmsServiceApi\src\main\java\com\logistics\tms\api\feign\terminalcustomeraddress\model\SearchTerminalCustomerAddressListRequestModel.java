package com.logistics.tms.api.feign.terminalcustomeraddress.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/1/4 13:09
 */
@Data
public class SearchTerminalCustomerAddressListRequestModel extends AbstractPageForm<SearchTerminalCustomerAddressListRequestModel> {
    @ApiModelProperty("发货地址")
    private String loadAddress;

    @ApiModelProperty("联系人")
    private String collectContactName;

    @ApiModelProperty("终端客户地址ids 导出用")
    private String terminalCustomerAddressIds;
}
