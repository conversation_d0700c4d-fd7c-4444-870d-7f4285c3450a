package com.logistics.tms.controller.companycarrier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/7/13 15:59
 */
@Data
public class UserCompanyCarrierInfoResponseModel {
    @ApiModelProperty("承运商公司id")
    private Long companyCarrierId;
    @ApiModelProperty("承运商公司名称")
    private String companyCarrierName;
    @ApiModelProperty("联系人")
    private String carrierContactName;
    @ApiModelProperty("联系方式")
    private String carrierContactPhone;
    @ApiModelProperty("类型：1 公司，2 个人")
    private Integer type;
}
