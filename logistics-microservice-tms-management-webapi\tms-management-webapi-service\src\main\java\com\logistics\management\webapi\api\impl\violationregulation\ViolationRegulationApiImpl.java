package com.logistics.management.webapi.api.impl.violationregulation;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.violationregulation.ViolationRegulationApi;
import com.logistics.management.webapi.api.feign.violationregulation.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.violationregulation.mapper.ViolationRegulationDetailMapping;
import com.logistics.management.webapi.api.impl.violationregulation.mapper.ViolationRegulationListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.constant.ExportExcelViolationRegulations;
import com.logistics.management.webapi.base.constant.ImportExcelHeaders;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.violationregulation.ViolationRegulationServiceApi;
import com.logistics.tms.api.feign.violationregulation.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: sj
 * @Date: 2019/6/3 11:26
 */
@Slf4j
@RestController
public class ViolationRegulationApiImpl implements ViolationRegulationApi {
    @Autowired
    private ConfigKeyConstant configKeyConstant;

    @Autowired
    private ViolationRegulationServiceApi violationRegulationService;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<SearchViolationRegulationListResponseDto> searchViolationRegulationList(@RequestBody SearchViolationRegulationListRequestDto requestDto) {
        Result<SearchViolationRegulationListResponseModel> result = violationRegulationService.searchViolationRegulationList(MapperUtils.mapperNoDefault(requestDto, SearchViolationRegulationListRequestModel.class));
        result.throwException();
        SearchViolationRegulationListResponseDto resultDto = MapperUtils.mapper(result.getData(),SearchViolationRegulationListResponseDto.class);
        if(result.getData().getPageInfo() != null){
            PageInfo<ViolationRegulationListResponseModel> pageInfoModel = result.getData().getPageInfo();
            List<String> sourceSrcList=new ArrayList<>();
            for (ViolationRegulationListResponseModel responseModel : pageInfoModel.getList()) {
                for (CertificationPicturesResponseModel picturesResponseModel : responseModel.getFileList()) {
                    sourceSrcList.add(picturesResponseModel.getRelativeFilepath());
                }
            }
            Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
            List<ViolationRegulationListResponseDto> violationRegulationListDto = MapperUtils.mapper(pageInfoModel.getList(),ViolationRegulationListResponseDto.class,new ViolationRegulationListMapping(configKeyConstant,imageMap));
            PageInfo<ViolationRegulationListResponseDto> pageInfoDto = MapperUtils.mapper(pageInfoModel,PageInfo.class);
            pageInfoDto.setList(violationRegulationListDto == null ? new ArrayList<>() : violationRegulationListDto);
            resultDto.setPageInfo(pageInfoDto);
        }
        return Result.success(resultDto);
    }

    /**
     * 详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<ViolationRegulationDetailResponseDto> getViolationRegulationDetail(@RequestBody @Valid ViolationRegulationDetailRequestDto requestDto) {
        Result<ViolationRegulationDetailResponseModel> result =  violationRegulationService.getViolationRegulationDetail(MapperUtils.mapper(requestDto,ViolationRegulationDetailRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (CertificationPicturesResponseModel ticketsModel : result.getData().getFileList()) {
            sourceSrcList.add(ticketsModel.getRelativeFilepath());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(),ViolationRegulationDetailResponseDto.class,new ViolationRegulationDetailMapping(configKeyConstant,imageMap)));
    }

    /**
     * 新增/修改
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> saveOrModifyViolationRegulation(@RequestBody @Valid AddOrModifyViolationRegulationRequestDto requestDto) {
        if(StringUtils.isNotBlank(requestDto.getDeduction()) && (ConverterUtils.toInt(requestDto.getDeduction()) < CommonConstant.INTEGER_ZERO
                || ConverterUtils.toInt(requestDto.getDeduction()) > CommonConstant.INT_TWELVE)){
            throw new BizException(ManagementWebApiExceptionEnum.DEDUCTION_RANGE_OF_ILLEGAL_POINTS);
        }
        if(StringUtils.isNotBlank(requestDto.getFine()) && (ConverterUtils.toInt(requestDto.getFine()) < CommonConstant.INTEGER_ZERO
                || ConverterUtils.toInt(requestDto.getFine()) > CommonConstant.INTEGER_TEN_THOUSAND )){
            throw new BizException(ManagementWebApiExceptionEnum.FINE_RANGE_OF_ILLEGAL_POINTS);
        }

        Result<Boolean> result = violationRegulationService.saveOrModifyViolationRegulation(MapperUtils.mapperNoDefault(requestDto,AddOrModifyViolationRegulationRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 删除
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> deleteViolationRegulation(@RequestBody @Valid DeleteViolationRegulationRequestDto requestDto) {
        Result<Boolean> result = violationRegulationService.deleteViolationRegulation(MapperUtils.mapper(requestDto,DeleteViolationRegulationRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 导出
     * @param requestDto
     * @param response
     */
    @Override
    public void export(SearchViolationRegulationListRequestDto requestDto, HttpServletResponse response) {
        Result<List<ViolationRegulationListResponseModel>> result = violationRegulationService.export(MapperUtils.mapperNoDefault(requestDto,SearchViolationRegulationListRequestModel.class));
        result.throwException();
        String fileName = "违章事故信息" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        List<String> sourceSrcList=new ArrayList<>();
        for (ViolationRegulationListResponseModel responseModel : result.getData()) {
            for (CertificationPicturesResponseModel picturesResponseModel : responseModel.getFileList()) {
                sourceSrcList.add(picturesResponseModel.getRelativeFilepath());
            }
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        List<ViolationRegulationListResponseDto> resultList = MapperUtils.mapper(result.getData(),ViolationRegulationListResponseDto.class,new ViolationRegulationListMapping(configKeyConstant,imageMap));
        ExcelUtils.exportExcelForServlet(fileName, fileName, ExportExcelViolationRegulations.getViolationRegulationsInfo(), response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return resultList;
            }
        });
    }

    /**
     * 导入
     * @param file
     * @param request
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<ImportViolationRegulationResponseDto> importViolationRegulations(MultipartFile file, HttpServletRequest request) {
        if (null == file) {
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_VIOLATION_REGULATIONS_FILE_IS_EMPTY);
        }
        InputStream in;
        try {
            in = file.getInputStream();
        } catch (IOException e) {
            log.error("导入违章事故档案记录失败，",e);
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_VIOLATION_REGULATIONS_FILE_IS_EMPTY);
        }

        List<List<Object>> excelList = new ImportExcelUtil().getDataListByExcel(in, file.getOriginalFilename(), ImportExcelHeaders.getImportViolationRegulationType());
        ImportViolationRegulationRequestDto requestDto = this.initImportRepeatData(excelList);
        Result<ImportViolationRegulationResponseModel> result = violationRegulationService.importViolationRegulations(MapperUtils.mapperNoDefault(requestDto,ImportViolationRegulationRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),ImportViolationRegulationResponseDto.class));
    }
    //入参校验及转换
    public ImportViolationRegulationRequestDto initImportRepeatData(List<List<Object>> excelList){
        ImportViolationRegulationRequestDto requestDto = new ImportViolationRegulationRequestDto();
        if (ListUtils.isEmpty(excelList)) {
            return requestDto;
        }

        List<ImportViolationRegulationListRequestDto> importList = new ArrayList<>();
        Set<ImportViolationRegulationListRequestDto> setContains = new HashSet<>();
        ImportViolationRegulationListRequestDto violationRegulationDto;
        for (int i = 0; i < excelList.size(); i++) {
            List<Object> objects = excelList.get(i);
            if(objects!=null){
                violationRegulationDto = new ImportViolationRegulationListRequestDto();
                //车牌号
                violationRegulationDto.setVehicleNo(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ZERO)));
                if(!FrequentMethodUtils.validateVehicleFormat(violationRegulationDto.getVehicleNo())){
                    continue;
                }
                //司机姓名
                violationRegulationDto.setDriverName(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ONE)));
                if(StringUtils.isBlank(violationRegulationDto.getDriverName())){
                    continue;
                }
                //司机联系方式
                violationRegulationDto.setDriverPhone(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_TWO)));
                if(StringUtils.isBlank(violationRegulationDto.getDriverPhone()) && FrequentMethodUtils.validateTelFormat(violationRegulationDto.getDriverPhone())){
                    continue;
                }
                //扣分
                String deduction = this.objToString(objects.get(CommonConstant.INTEGER_THREE));
                violationRegulationDto.setDeduction(deduction);
                if(StringUtils.isNotBlank(deduction) && (ConverterUtils.toInt(deduction)<0 || ConverterUtils.toInt(deduction) > 12) ){
                    continue;
                }

                //罚款
                violationRegulationDto.setFine(this.objToString(objects.get(CommonConstant.INTEGER_FOUR)));
                if(StringUtils.isNotBlank(violationRegulationDto.getFine())){
                    BigDecimal cellFine = new BigDecimal(this.objToString(objects.get(CommonConstant.INTEGER_FOUR))).setScale(2, BigDecimal.ROUND_HALF_UP);
                    violationRegulationDto.setFine(cellFine.stripTrailingZeros().toPlainString());
                }

                //违章时间
                String occuranceTime = this.objToDate(objects.get(CommonConstant.INTEGER_FIVE));
                if(StringUtils.isBlank(occuranceTime)){
                    continue;
                }
                violationRegulationDto.setOccuranceTime(occuranceTime);

                //违章地点
                violationRegulationDto.setOccuranceAddress(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_SIX)));
                if(StringUtils.isBlank(violationRegulationDto.getOccuranceAddress())){
                    continue;
                }
                //备注
                violationRegulationDto.setRemark(ConverterUtils.toString(objects.get(objects.size()-1)));
                if(StringUtils.isNotBlank(violationRegulationDto.getRemark()) && violationRegulationDto.getRemark().length() > CommonConstant.INTEGER_THREE_HUNDRED){
                    continue;
                }
                setContains.add(violationRegulationDto);
            }
        }

        if(!setContains.isEmpty()){
            for (ImportViolationRegulationListRequestDto tempDto: setContains) {
                importList.add(tempDto);
            }
        }
        requestDto.setImportList(importList);
        requestDto.setNumberFailures(ConverterUtils.toString(excelList.size() - setContains.size()));
        return requestDto;
    }

    //object转String
    private String objToString(Object obj){
        if(obj == null){
            return "";
        }

        if(obj instanceof BigDecimal){
            BigDecimal decimal = (BigDecimal)obj;
            return  ConverterUtils.toString(decimal.stripTrailingZeros().toPlainString());
        }else{
            return ConverterUtils.toString(obj);
        }

    }

    /**
     * 格式化成 "2019/10/10" -> "2019-10-10"
     * @return
     */
    private String objToDate(Object obj){
        Date date = MapperUtils.mapperNoDefault(obj,Date.class);
        return date==null?null:DateUtils.dateToString(date,DateUtils.DATE_TO_STRING_DETAIAL_PATTERN);
    }

}
