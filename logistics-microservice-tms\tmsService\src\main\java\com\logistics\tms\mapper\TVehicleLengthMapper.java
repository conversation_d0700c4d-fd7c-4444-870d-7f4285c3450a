package com.logistics.tms.mapper;

import com.logistics.tms.controller.vehiclelength.response.SearchVehicleLengthListResponseModel;
import com.logistics.tms.controller.vehiclelength.response.SelectVehicleLengthListResponseModel;
import com.logistics.tms.controller.vehiclelength.response.VehicleLengthDetailResponseModel;
import com.logistics.tms.entity.TVehicleLength;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
* Created by Mybatis Generator on 2024/04/26
*/
@Mapper
public interface TVehicleLengthMapper extends BaseMapper<TVehicleLength> {

    List<SearchVehicleLengthListResponseModel> searchVehicleLengthList();

    VehicleLengthDetailResponseModel vehicleLengthDetail(@Param("vehicleLengthId")Long vehicleLengthId);

    List<SelectVehicleLengthListResponseModel> selectVehicleLengthList();

    List<TVehicleLength> selectRangeGoodsCount(@Param("goodsCount") BigDecimal goodsCount);

}