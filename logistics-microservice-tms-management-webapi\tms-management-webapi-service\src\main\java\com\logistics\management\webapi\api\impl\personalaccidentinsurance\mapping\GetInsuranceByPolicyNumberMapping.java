package com.logistics.management.webapi.api.impl.personalaccidentinsurance.mapping;

import com.logistics.management.webapi.api.feign.personalaccidentinsurance.dto.GetInsuranceByBatchNumberResponseDto;
import com.logistics.management.webapi.api.feign.personalaccidentinsurance.dto.GetInsuranceByPolicyNumberResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.tms.api.feign.personalaccidentinsurance.model.GetInsuranceByBatchNumberResponseModel;
import com.logistics.tms.api.feign.personalaccidentinsurance.model.GetInsuranceByPolicyNumberResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/6/4 11:09
 */
public class GetInsuranceByPolicyNumberMapping extends MapperMapping<GetInsuranceByPolicyNumberResponseModel,GetInsuranceByPolicyNumberResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;
    public GetInsuranceByPolicyNumberMapping(String imagePrefix,Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        GetInsuranceByPolicyNumberResponseModel source = getSource();
        GetInsuranceByPolicyNumberResponseDto destination = getDestination();
        if (source != null){
            destination.setSinglePremium(ConverterUtils.toString(source.getGrossPremium().divide(ConverterUtils.toBigDecimal(source.getPolicyPersonCount()),2, BigDecimal.ROUND_HALF_UP)));
            if (ListUtils.isNotEmpty(source.getTicketsList())){
                destination.getTicketsList().stream().forEach(tickets ->
                    tickets.setFilePathSrc(imagePrefix+imageMap.get(tickets.getFilePath()))
                );
            }
            if(source.getStartTime()!=null){
                destination.setStartTime(DateUtils.dateToString(source.getStartTime(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
            }
            if(source.getEndTime()!=null){
                destination.setEndTime(DateUtils.dateToString(source.getEndTime(),CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
            }
            if (ListUtils.isNotEmpty(source.getBatchNumberList())){
                for (GetInsuranceByBatchNumberResponseModel model:source.getBatchNumberList()) {
                    for (GetInsuranceByBatchNumberResponseDto dto:destination.getBatchNumberList()) {
                        if (model.getPersonalAccidentInsuranceId().toString().equals(dto.getPersonalAccidentInsuranceId())) {
                            if(model.getStartTime()!=null){
                                dto.setStartTime(DateUtils.dateToString(model.getStartTime(),CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
                            }
                            if(model.getEndTime()!=null){
                                dto.setEndTime(DateUtils.dateToString(model.getEndTime(),CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
                            }
                            dto.setSinglePremium(ConverterUtils.toString(model.getGrossPremium().divide(ConverterUtils.toBigDecimal(model.getPolicyPersonCount()), 2, BigDecimal.ROUND_HALF_UP)));
                            if (ListUtils.isNotEmpty(dto.getTicketsList())) {
                                dto.getTicketsList().stream().forEach(tickets ->
                                    tickets.setFilePathSrc(imagePrefix + imageMap.get(tickets.getFilePath()))
                                );
                            }
                            break;
                        }
                    }
                }
            }
        }
    }
}
