package com.logistics.tms.controller.routeenquiry.response;

import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2024/7/9 13:51
 */
@Data
public class SearchRouteEnquiryListForWebResponseModel {

    /**
     * 路线询价单表id
     */
    private Long routeEnquiryId;

    /**
     * 竞价单号
     */
    private String orderCode;

    /**
     * 竞价状态：1 待业务审核，2 待车主确认，3 待结算审核，4 完成竞价
     */
    private Integer status;

    /**
     * 是否取消：0 否，1 是
     */
    private Integer ifCancel;

    /**
     * 是否归档：0 否，1 是
     */
    private Integer ifArchive;

    /**
     * 路线数
     */
    private Integer addressCount;

    /**
     * 报价生效开始时间
     */
    private Date quoteStartTime;

    /**
     * 报价生效结束时间
     */
    private Date quoteEndTime;

    /**
     * 关联合同号
     */
    private String contractCode;

    /**
     * 报价人
     */
    private String quoteOperator;

    /**
     * 报价时间
     */
    private Date quoteTime;


    /**
     * 报价状态：-1 未报价，0 未选择，1 已选择，2 已取消
     */
    private Integer quoteStatus;
}
