package com.logistics.management.webapi.base.enums;

public enum CertificationPicturesObjectTypeEnum {
    OBJECT_TYPE_DEFAULT_ENUM(0,""),
    T_Q_PERSONAL_ACCIDENT_INSURANCE(1,"个人意外险表"),
    T_Q_STAFF_DRIVER_OCCUPATIONAL_RECORD(2,"司机从业资格证记录表"),
    T_Q_STAFF_DRIVER_CONTINUE_LEARNING_RECORD(3,"司机继续学习记录表"),
    T_Q_STAFF_DRIVER_INTEGRITY_EXAMINATION_RECORD(4,"司机诚信考核记录表"),
    T_Q_INSURANCE(5,"保险信息表"),
    T_Q_VIOLATION_REGULATIONS(6,"车辆违章信息表"),
    T_Q_VEHICLE_DRIVING_LICENSE_ANNUAL_REVIEW(7,"车辆行驶证检查记录表"),
    T_Q_STAFF_BASIC(8,"人员基本信息表"),
    T_Q_STAFF_DRIVER_CREDENTIAL(9,"司机证件表"),
    T_Q_VEHICLE_GPS_RECORD(10,"车辆GPS安装记录表"),
    T_Q_VEHICLE_GRADE_ESTIMATION_RECORD(11,"车辆等级评定表"),
    T_Q_VEHICLE_ROAD_TRANSPORT_CERTIFICATE_ANNUAL_REVIEW(12,"车辆道路运输证年审记录表"),
    T_Q_VEHICLE_DRIVING_LICENSE(13,"车辆行驶证表"),
    T_Q_VEHICLE_ROAD_TRANSPORT_CERTIFICATE(14,"道路运输证表"),
    T_Q_VEHICLE_BASIC(15,"奇亚车辆资产基础信息表"),
    ;
    private Integer objectType;
    private String note;

    CertificationPicturesObjectTypeEnum(Integer objectType,String note) {
        this.objectType = objectType;
        this.note = note;

    }

    public Integer getObjectType() {
        return objectType;
    }

    public String getNote() {
        return note;
    }

    public static CertificationPicturesObjectTypeEnum getEnum(Integer objectType) {
        for (CertificationPicturesObjectTypeEnum t : values()) {
            if (t.getObjectType().equals(objectType)) {
                return t;
            }
        }
        return OBJECT_TYPE_DEFAULT_ENUM;
    }
}
