package com.logistics.management.webapi.controller.routeenquiry.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.management.webapi.base.enums.PriceTypeEnum;
import com.logistics.management.webapi.client.routeenquiry.response.SearchRouteEnquirySummaryListResponseModel;
import com.logistics.management.webapi.controller.routeenquiry.response.SearchRouteEnquirySummaryListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;

/**
 * @author: wjf
 * @date: 2024/7/15 11:00
 */
public class RouteEnquirySearchSummaryListMapping extends MapperMapping<SearchRouteEnquirySummaryListResponseModel, SearchRouteEnquirySummaryListResponseDto> {
    @Override
    public void configure() {
        SearchRouteEnquirySummaryListResponseModel source = getSource();
        SearchRouteEnquirySummaryListResponseDto destination = getDestination();

        //中标承运商
        if (CompanyTypeEnum.COMPANY.getKey().equals(source.getCompanyCarrierType())) {
            destination.setCompanyCarrierName(source.getCompanyCarrierName());
        } else if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())) {
            destination.setCompanyCarrierName(source.getCarrierContactName() + " " + source.getCarrierContactPhone());
        }

        //拼接地址
        destination.setLoadAddress((StringUtils.isBlank(source.getFromWarehouse()) ? CommonConstant.BLANK_TEXT : ("【"+source.getFromWarehouse()+"】")) + source.getFromProvinceName() + source.getFromCityName() + source.getFromAreaName());
        destination.setUnloadAddress(source.getToProvinceName() + source.getToCityName() + source.getToAreaName());

        destination.setDistance(source.getDistance().stripTrailingZeros().toPlainString());
        destination.setQuotePriceTypeLabel(PriceTypeEnum.getEnum(source.getQuotePriceType()).getValue());
        if (PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getQuotePriceType())){
            destination.setQuotePrice(ConverterUtils.toString(source.getQuotePrice()) + GoodsUnitEnum.BY_WEIGHT.getPriceUnit());
        }else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getQuotePriceType())){
            destination.setQuotePrice(ConverterUtils.toString(source.getQuotePrice()) + CommonConstant.YUAN);
        }

        //报价生效期限
        if (source.getQuoteStartTime() != null) {
            destination.setQuoteStartTime(DateUtils.dateToString(source.getQuoteStartTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if (source.getQuoteEndTime() != null) {
            destination.setQuoteEndTime(DateUtils.dateToString(source.getQuoteEndTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
    }
}
