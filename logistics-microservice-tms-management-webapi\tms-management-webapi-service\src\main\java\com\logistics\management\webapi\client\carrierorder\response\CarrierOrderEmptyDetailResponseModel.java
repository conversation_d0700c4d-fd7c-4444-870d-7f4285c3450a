package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2021/10/27 10:58
 */
@Data
public class CarrierOrderEmptyDetailResponseModel {

    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("预提数量")
    private BigDecimal expectAmount;
}
