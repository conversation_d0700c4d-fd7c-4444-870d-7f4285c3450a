package com.logistics.tms.base.enums;
/**
 * @Author: sj
 * @Date: 2019/11/8 9:24
 */
public enum SafeCheckItemEnum {
    SAFETY_PRF_STEERING(SafeCheckClassEnum.SAFETY_PRF,100,"转向系统"),
    SAFETY_PRF_BRAKING(SafeCheckClassEnum.SAFETY_PRF,101,"制动系统"),
    SAFETY_PRF_LIGHTING(SafeCheckClassEnum.SAFETY_PRF,102,"灯光"),
    SAFETY_PRF_EMAIL_TYRE(SafeCheckClassEnum.SAFETY_PRF,103,"油箱轮胎"),

    SAFETY_EQP_PROTECTIVE(SafeCheckClassEnum.SAFETY_EQP,200,"防护用品使用情况"),
    SAFETY_EQP_BELT(SafeCheckClassEnum.SAFETY_EQP,201,"安全带"),
    SAFETY_EQP_FIRE(SafeCheckClassEnum.SAFETY_EQP,202,"消防器材"),
    SAFETY_EQP_FASTENING(SafeCheckClassEnum.SAFETY_EQP,203,"各部门紧固"),

    SAFETY_OTHER_TRANS_SSP(SafeCheckClassEnum.SAFETY_OTHER,300,"传动-悬挂"),
    SAFETY_OTHER_GPS(SafeCheckClassEnum.SAFETY_OTHER,301,"GPS设备使用情况"),
    ;
    private SafeCheckClassEnum checkClassType;
    private Integer itemType;
    private String itemName;

    SafeCheckItemEnum(SafeCheckClassEnum checkClassType, Integer itemType, String itemName){
      this.checkClassType = checkClassType;
      this.itemType = itemType;
      this.itemName = itemName;
    }

    public SafeCheckClassEnum getCheckClassType() { return checkClassType; }
    public Integer getItemType() { return itemType; }
    public String getItemName() { return itemName; }

}
