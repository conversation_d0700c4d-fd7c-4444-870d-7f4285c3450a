package com.logistics.appapi.client.staff.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/6/10 8:54
 */
@Data
public class GetStaffDetailByAccountIdResponseModel {
    @ApiModelProperty("人员基本信息表ID")
    private Long staffId;
    @ApiModelProperty("人员机构 1 自主，2 外部，3 自营")
    private Integer staffProperty;
    @ApiModelProperty("人员姓名")
    private String name;
    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("是否可跳转云仓小程序: 0 不可跳转 1 可跳转")
    private Integer warehouseSwitch;
}
