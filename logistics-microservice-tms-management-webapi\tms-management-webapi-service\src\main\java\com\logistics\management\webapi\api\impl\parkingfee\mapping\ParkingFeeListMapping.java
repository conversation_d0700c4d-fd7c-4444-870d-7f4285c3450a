package com.logistics.management.webapi.api.impl.parkingfee.mapping;

import com.logistics.management.webapi.api.feign.parkingfee.dto.ParkingFeeListResponseDto;
import com.logistics.management.webapi.base.enums.CooperationStatusEnum;
import com.logistics.management.webapi.base.enums.SettlementStatusEnum;
import com.logistics.management.webapi.base.enums.VehiclePropertyEnum;
import com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import java.util.Optional;

/**
 * @Author: sj
 * @Date: 2019/10/9 16:31
 */
public class ParkingFeeListMapping extends MapperMapping<ParkingFeeListResponseModel,ParkingFeeListResponseDto> {
    @Override
    public void configure() {
        ParkingFeeListResponseModel source = this.getSource();
        ParkingFeeListResponseDto target = this.getDestination();
        if(source!=null){
            target.setStatusLabel(SettlementStatusEnum.getEnum(source.getStatus()).getValue());
            target.setCooperationStatusLabel(CooperationStatusEnum.getEnum(source.getCooperationStatus()).getValue());
            if(source.getParkingFee()!=null && source.getCooperationPeriod()!=null){
                target.setParkingFeeLabel(source.getParkingFee()+"*"+source.getCooperationPeriod());
            }
            if(source.getStartDate()!=null){
                target.setStartDate(DateUtils.dateToString(source.getStartDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getEndDate()!=null){
                target.setEndDate(DateUtils.dateToString(source.getEndDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getFinishDate()!=null){
                target.setFinishDate(DateUtils.dateToString(source.getFinishDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getLastModifiedTime()!=null){
                target.setLastModifiedTime(DateUtils.dateToString(source.getLastModifiedTime(),DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
            }
            target.setDriverLabel(Optional.ofNullable(target.getName()).orElse("")+" "+ Optional.ofNullable(target.getMobile()).orElse("") );
            //设置车辆机构类型展示文本
            target.setVehiclePropertyLabel(VehiclePropertyEnum.getEnum(source.getVehicleProperty()).getValue());
        }
    }
}
