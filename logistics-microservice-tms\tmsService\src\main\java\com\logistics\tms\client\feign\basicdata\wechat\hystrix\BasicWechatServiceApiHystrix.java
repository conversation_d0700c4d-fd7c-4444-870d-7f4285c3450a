package com.logistics.tms.client.feign.basicdata.wechat.hystrix;

import com.logistics.tms.client.feign.basicdata.wechat.BasicWechatServiceApi;
import com.logistics.tms.client.feign.basicdata.wechat.request.CreateGroupChatRequestModel;
import com.logistics.tms.client.feign.basicdata.wechat.request.PushMessageRequestModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.Collection;

@Component
public class BasicWechatServiceApiHystrix implements BasicWechatServiceApi {

    @Override
    public Result<Boolean> pushMessage(Collection<PushMessageRequestModel> requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> createGroupChat(CreateGroupChatRequestModel createGroupChatRequestModel) {
        return Result.timeout();
    }
}
