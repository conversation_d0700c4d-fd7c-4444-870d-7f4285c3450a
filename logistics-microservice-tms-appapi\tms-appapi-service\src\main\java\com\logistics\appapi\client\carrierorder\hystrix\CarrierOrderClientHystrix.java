package com.logistics.appapi.client.carrierorder.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.carrierorder.CarrierOrderClient;
import com.logistics.appapi.client.carrierorder.request.*;
import com.logistics.appapi.client.carrierorder.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import javax.validation.Valid;

/**
 * @author: wjf
 * @date: 2023/11/13 13:21
 */
@Component
public class CarrierOrderClientHystrix implements CarrierOrderClient {

    @Override
    public Result<PageInfo<SearchCarrierOrderListAppResponseModel>> searchList(SearchCarrierOrderListAppRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SearchCarrierOrderCountResponseModel> searchListAccount(SearchCarrierOrderListAppRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierOrderDetailAppResponseModel> carrierOrderDetail(CarrierOrderIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierOrderLogisticsDetailResponseModel> carrierOrderLogisticsDetail(CarrierOrderIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result pickUp(CarrierOrderLoadRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> arrivePickUpV2(ArrivePickUpV2RequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> unloading(CarrierOrderUnloadRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> arrivePickUp(CarrierOrderArriveLoadUnloadRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> arriveUnloading(CarrierOrderArriveLoadUnloadRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<QueryIdByCarrierOrderCodeResponseModel> queryIdByCarrierOrderCode(QueryIdByCarrierOrderCodeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PrintBillDetailResponseModel> printBillDetail(CarrierOrderIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> denyLoadAmount(CarrierOrderIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<LeyiPickupConfirmResponseModel> pickupConfirm(PickupConfirmRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetLeYiQrCodeResponseModel> getLeYiQrCode(GetLeYiQrCodeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<DownloadLadingBillResponseModel> downloadLadingBill(CarrierOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> verifyEnablePickUpMore(VerifyEnablePickUpMoreReqModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<AssociateExtDemandOrderRespModel> associateExtDemandOrder(AssociateExtDemandOrderReqModel requestModel) {
        return Result.timeout();
    }
}
