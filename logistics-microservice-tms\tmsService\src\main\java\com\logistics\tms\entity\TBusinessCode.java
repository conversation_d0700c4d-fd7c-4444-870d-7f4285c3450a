package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TBusinessCode extends BaseEntity {
    /**
    * 业务类型1 需求单
    */
    @ApiModelProperty("业务类型1 需求单")
    private Integer businessType;

    /**
    * 编号长度
    */
    @ApiModelProperty("编号长度")
    private Integer businessCodeLength;

    /**
    * 编号前缀
    */
    @ApiModelProperty("编号前缀")
    private String businessCodePrefix;

    /**
    * 业务编码序列号，从1开始使用；在编码总长度基础上去掉编码前缀和时间戳，不足前边补零
    */
    @ApiModelProperty("业务编码序列号，从1开始使用；在编码总长度基础上去掉编码前缀和时间戳，不足前边补零")
    private Integer businessCodeSequence;

    /**
    * 日期格式
    */
    @ApiModelProperty("日期格式")
    private String businessCodeDateFormat;

    /**
    * 启用 1 禁用 0
    */
    @ApiModelProperty("启用 1 禁用 0")
    private Integer enabled;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
}