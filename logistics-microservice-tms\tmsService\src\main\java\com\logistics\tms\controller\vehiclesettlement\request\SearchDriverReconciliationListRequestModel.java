package com.logistics.tms.controller.vehiclesettlement.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author：wjf
 * @date：2021/4/12 13:12
 */
@Data
public class SearchDriverReconciliationListRequestModel extends AbstractPageForm<SearchDriverReconciliationListRequestModel> {
    @ApiModelProperty("结算状态：空 全部，2 待确认，3 待处理，4 待结清（包含部分结清），6 已结清")
    private Integer status;
    @ApiModelProperty("月份（yyyy-MM）")
    private String settlementMonth;

    private Long driverId;//登录司机id
}
