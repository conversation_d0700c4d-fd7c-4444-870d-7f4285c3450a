package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/10/14 15:01
 */
@Data
public class ShareCarrierOrderModel {

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("发货地址")
    private String loadProvinceName ;

    private String loadCityName ;

    private String loadAreaName ;

    private String loadWarehouse ;

    @ApiModelProperty("收货地址")
    private String unloadProvinceName;

    private String unloadCityName ;

    private String unloadAreaName ;

    private String unloadWarehouse ;

    @ApiModelProperty("实际提货数量")
    private BigDecimal loadAmount ;

}
