package com.logistics.appapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SiteOtherPalletsRequestDto {

    @ApiModelProperty("空置数量")
    private String emptyTraysAmount;

    @ApiModelProperty("带料占用数量")
    private String employTraysAmount;

    @ApiModelProperty("共享托盘图片")
    private List<String> sharedTrayPics;
}
