<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TDriverSafePromiseRelationMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDriverSafePromiseRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="safe_promise_id" jdbcType="BIGINT" property="safePromiseId" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="staff_property" jdbcType="INTEGER" property="staffProperty" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
    <result column="staff_mobile" jdbcType="VARCHAR" property="staffMobile" />
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="sign_time" jdbcType="TIMESTAMP" property="signTime" />
    <result column="hand_promise_url" jdbcType="VARCHAR" property="handPromiseUrl" />
    <result column="sign_responsibility_url" jdbcType="VARCHAR" property="signResponsibilityUrl" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, safe_promise_id, staff_id, staff_property, staff_name, staff_mobile, vehicle_no, 
    status, sign_time, hand_promise_url, sign_responsibility_url, created_by, created_time, 
    last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_driver_safe_promise_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_driver_safe_promise_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDriverSafePromiseRelation">
    insert into t_driver_safe_promise_relation (id, safe_promise_id, staff_id, 
      staff_property, staff_name, staff_mobile, 
      vehicle_no, status, sign_time, 
      hand_promise_url, sign_responsibility_url, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{safePromiseId,jdbcType=BIGINT}, #{staffId,jdbcType=BIGINT}, 
      #{staffProperty,jdbcType=INTEGER}, #{staffName,jdbcType=VARCHAR}, #{staffMobile,jdbcType=VARCHAR}, 
      #{vehicleNo,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{signTime,jdbcType=TIMESTAMP}, 
      #{handPromiseUrl,jdbcType=VARCHAR}, #{signResponsibilityUrl,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDriverSafePromiseRelation">
    insert into t_driver_safe_promise_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="safePromiseId != null">
        safe_promise_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="staffProperty != null">
        staff_property,
      </if>
      <if test="staffName != null">
        staff_name,
      </if>
      <if test="staffMobile != null">
        staff_mobile,
      </if>
      <if test="vehicleNo != null">
        vehicle_no,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="signTime != null">
        sign_time,
      </if>
      <if test="handPromiseUrl != null">
        hand_promise_url,
      </if>
      <if test="signResponsibilityUrl != null">
        sign_responsibility_url,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="safePromiseId != null">
        #{safePromiseId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffProperty != null">
        #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="staffName != null">
        #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null">
        #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="signTime != null">
        #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="handPromiseUrl != null">
        #{handPromiseUrl,jdbcType=VARCHAR},
      </if>
      <if test="signResponsibilityUrl != null">
        #{signResponsibilityUrl,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDriverSafePromiseRelation">
    update t_driver_safe_promise_relation
    <set>
      <if test="safePromiseId != null">
        safe_promise_id = #{safePromiseId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffProperty != null">
        staff_property = #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="staffName != null">
        staff_name = #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null">
        staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="signTime != null">
        sign_time = #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="handPromiseUrl != null">
        hand_promise_url = #{handPromiseUrl,jdbcType=VARCHAR},
      </if>
      <if test="signResponsibilityUrl != null">
        sign_responsibility_url = #{signResponsibilityUrl,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDriverSafePromiseRelation">
    update t_driver_safe_promise_relation
    set safe_promise_id = #{safePromiseId,jdbcType=BIGINT},
      staff_id = #{staffId,jdbcType=BIGINT},
      staff_property = #{staffProperty,jdbcType=INTEGER},
      staff_name = #{staffName,jdbcType=VARCHAR},
      staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      sign_time = #{signTime,jdbcType=TIMESTAMP},
      hand_promise_url = #{handPromiseUrl,jdbcType=VARCHAR},
      sign_responsibility_url = #{signResponsibilityUrl,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>