package com.logistics.management.webapi.controller.carrierorder;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.ApiParamsValidatorUtil;
import com.logistics.management.webapi.client.carrierorder.CarrierOrderForYeloLifeClient;
import com.logistics.management.webapi.client.carrierorder.request.*;
import com.logistics.management.webapi.client.carrierorder.response.*;
import com.logistics.management.webapi.controller.carrierorder.mapping.*;
import com.logistics.management.webapi.controller.carrierorder.request.*;
import com.logistics.management.webapi.controller.carrierorder.response.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tools.utils.YeloExcelUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;

/**
 * 新生运单管理
 * @author: wjf
 * @date: 2024/3/26 13:14
 */
@Api(value = "新生运单管理")
@RestController
public class CarrierOrderForYeloLifeController {

    @Resource
    private CarrierOrderForYeloLifeClient carrierOrderForYeloLifeClient;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private ConfigKeyConstant configKeyConstant;

    /**
     * 查询运单列表 3.26.0
     *
     * @param requestDto 筛选条件
     * @return 运单列表
     */
    @ApiOperation(value = "查询运单列表 v2.44")
    @PostMapping(value = "/api/carrierOrderManagement/searchCarrierOrderListForYeloLife")
    public Result<PageInfo<SearchCarrierOrderListForYeloLifeResponseDto>> searchCarrierOrderListForYeloLife(@RequestBody SearchCarrierOrderListForYeloLifeRequestDto requestDto) {
        Result<PageInfo<SearchCarrierOrderListForYeloLifeResponseModel>> result = carrierOrderForYeloLifeClient.searchCarrierOrderListForYeloLife(MapperUtils.mapper(requestDto, SearchCarrierOrderListForYeloLifeRequestModel.class));
        result.throwException();
        PageInfo pageData = result.getData();
        List<SearchCarrierOrderListForYeloLifeResponseModel> data = pageData.getList();
        pageData.setList(MapperUtils.mapper(data, SearchCarrierOrderListForYeloLifeResponseDto.class, new SearchCarrierOrderListForYeloLifeMapping()));
        return Result.success(pageData);
    }

    /**
     * 导出运单
     */
    @ApiOperation(value = "导出运单 v1.2.1")
    @GetMapping(value = "/api/carrierOrderManagement/exportCarrierOrderForYeloLife")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportCarrierOrderForYeloLife(SearchCarrierOrderListForYeloLifeRequestDto requestDto, HttpServletResponse response) {
        Result<List<SearchCarrierOrderListForYeloLifeResponseModel>> result = carrierOrderForYeloLifeClient.exportCarrierOrderForYeloLife(MapperUtils.mapper(requestDto, SearchCarrierOrderListForYeloLifeRequestModel.class));
        result.throwException();

        List<SearchCarrierOrderListForYeloLifeResponseDto> list = MapperUtils.mapper(result.getData(), SearchCarrierOrderListForYeloLifeResponseDto.class, new SearchCarrierOrderListForYeloLifeMapping());
        String fileName = "运单列表" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        try {
            YeloExcelUtils.doExportMoreThreadByOneSheet(response, list, false, SearchCarrierOrderListForYeloLifeResponseDto.class, fileName);
        } catch (Exception e) {
            return;
        }
    }

    /**
     * 运单列表统计 TAB
     *
     * @param requestDto 筛选条件
     * @return 统计列表
     */
    @ApiOperation(value = "运单列表统计 v1.2.1")
    @PostMapping(value = "/api/carrierOrderManagement/searchListStatisticsForYeloLife")
    public Result<SearchCarrierListStatisticsResponseDto> searchListStatisticsForYeloLife(@RequestBody SearchCarrierOrderListForYeloLifeRequestDto requestDto) {
        Result<SearchCarrierListStatisticsResponseModel> result = carrierOrderForYeloLifeClient.searchListStatisticsForYeloLife(MapperUtils.mapper(requestDto, SearchCarrierOrderListForYeloLifeRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SearchCarrierListStatisticsResponseDto.class));
    }

    /**
     * 运单详情页 3.23.0
     *
     * @param requestDto 运单ID
     * @return 运单详情
     */
    @ApiOperation(value = "运单详情页 v2.44")
    @PostMapping(value = "/api/carrierOrderManagement/carrierOrderDetailForYeloLife")
    public Result<CarrierOrderDetailForYeloLifeResponseDto> carrierOrderDetailForYeloLife(@RequestBody @Valid CarrierOrderIdRequestDto requestDto) {
        Result<CarrierOrderDetailForYeloLifeResponseModel> result = carrierOrderForYeloLifeClient.carrierOrderDetailForYeloLife(MapperUtils.mapper(requestDto, CarrierOrderIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), CarrierOrderDetailForYeloLifeResponseDto.class, new CarrierOrderDetailForYeloLifeMapping()));
    }

    /**
     * 查看运单票据
     *
     * @param requestDto 运单id
     * @return 票据信息
     */
    @ApiOperation(value = "查询票据 v1.2.1")
    @PostMapping(value = "/api/carrierOrderManagement/getTicketsForYeloLife")
    public Result<List<TicketsForYeloLifeDto>> getTicketsForYeloLife(@RequestBody @Valid CarrierOrderIdRequestDto requestDto) {
        Result<List<TicketsModel>> result = carrierOrderForYeloLifeClient.getTicketsForYeloLife(MapperUtils.mapper(requestDto, CarrierOrderIdRequestModel.class));
        result.throwException();
        List<String> sourceSrcList = new ArrayList<>();
        for (TicketsModel model : result.getData()) {
            sourceSrcList.add(model.getImagePath());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(), TicketsForYeloLifeDto.class, new GetTicketsForYeloLifeMapping(configKeyConstant.fileAccessAddress, imageMap)));
    }

    /**
     * 查询提卸货详情
     *
     * @param requestDto 运单id
     * @return 提卸货详情
     */
    @ApiOperation(value = "查询提卸货详情  v2.44")
    @PostMapping(value = "/api/carrierOrderManagement/getLoadDetailForYeloLife")
    public Result<LoadDetailForYeloLifeResponseDto> getLoadDetailForYeloLife(@RequestBody @Valid LoadDetailForYeloLifeRequestDto requestDto) {
        Result<LoadDetailForYeloLifeResponseModel> result = carrierOrderForYeloLifeClient.getLoadDetailForYeloLife(MapperUtils.mapper(requestDto, LoadDetailForYeloLifeRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), LoadDetailForYeloLifeResponseDto.class, new GetLoadDetailForYeloLifeMapping()));
    }

    /**
     * 运单提货
     *
     * @param requestDto 运单id,提货信息
     * @return 操作结果
     */
    @ApiOperation(value = "已提货 v2.44")
    @PostMapping(value = "/api/carrierOrderManagement/loadForYeloLife")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result loadForYeloLife(@RequestBody @Valid LoadForYeloLifeRequestDto requestDto) {
        Result result = carrierOrderForYeloLifeClient.loadForYeloLife(MapperUtils.mapper(requestDto, LoadForYeloLifeRequestModel.class));
        if (result.getErrcode()==CommonConstant.CODE_NOT_EXIST){
            return new Result(CommonConstant.CODE_NOT_EXIST,result.getData(),result.getErrmsg());
        }else {
            result.throwException();
        }
        return result;
    }


    /**
     * 运单卸货
     *
     * @param requestDto 运单id,卸货信息
     * @return 操作结果
     */
    @ApiOperation(value = "已卸货 v2.44")
    @PostMapping(value = "/api/carrierOrderManagement/unloadForYeloLife")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> unloadForYeloLife(@RequestBody @Valid UnLoadForYeloLifeRequestDto requestDto) {
        UnLoadForYeloLifeRequestModel requestModel = MapperUtils.mapper(requestDto, UnLoadForYeloLifeRequestModel.class);

        //给入参的货物数量赋值 如果入参存在货物编码数量
        for (UnloadGoodsForYeloLifeRequestModel signGoodsForYeloLifeRequestModel : Optional.ofNullable(requestModel.getGoodsList()).orElse(new ArrayList<>())) {
            List<LoadGoodsForYeloLifeRequestCodeModel> codeDtoList = signGoodsForYeloLifeRequestModel.getCodeDtoList();
            if (CollectionUtil.isEmpty(codeDtoList)) {
                continue;
            }
            BigDecimal reduceTotalSignAmount = codeDtoList.stream().map(LoadGoodsForYeloLifeRequestCodeModel::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            signGoodsForYeloLifeRequestModel.setUnloadAmount(reduceTotalSignAmount);
        }
        Result<Boolean> result = carrierOrderForYeloLifeClient.unloadForYeloLife(requestModel);
        result.throwException();
        return Result.success(true);
    }

    /**
     * 查询签收确认详情-回收类型 3.23.0
     *
     * @param requestDto 运单id
     * @return 签收详情
     */
    @ApiOperation(value = "查询签收确认详情-回收类型 v2.44")
    @PostMapping(value = "/api/carrierOrderManagement/carrierOrderListBeforeSignUpForYeloLife")
    public Result<SignDetailForYeloLifeResponseDto> carrierOrderListBeforeSignUpForYeloLife(@RequestBody @Valid CarrierOrderIdRequestDto requestDto) {
        Result<SignDetailForYeloLifeResponseModel> result = carrierOrderForYeloLifeClient.carrierOrderListBeforeSignUpForYeloLife(MapperUtils.mapper(requestDto, CarrierOrderIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SignDetailForYeloLifeResponseDto.class, new CarrierOrderSignUpDetailForYeloLifeMapping()));
    }

    /**
     * 运单签收-回收类型 3.23.0
     *
     * @param requestDto 运单id,签收信息
     * @return 操作结果
     */
    @ApiOperation(value = "运单签收-回收类型 v2.44")
    @PostMapping(value = "/api/carrierOrderManagement/carrierOrderSignUpForYeloLife")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> carrierOrderSignUpForYeloLife(@RequestBody @Valid CarrierOrderSignUpForYeloLifeRequestDto requestDto) {
        if (StringUtils.isNotBlank(requestDto.getDispatchFreightFee()) && !ApiParamsValidatorUtil.verifyFixedPrice(requestDto.getDispatchFreightFee())){
            throw new BizException(ManagementWebApiExceptionEnum.DRIVER_FEE_ERROR);
        }
        CarrierOrderSignUpForYeloLifeRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierOrderSignUpForYeloLifeRequestModel.class);
        //给入参的货物数量赋值 如果入参存在货物编码数量
        for (SignGoodsForYeloLifeRequestModel signGoodsForYeloLifeRequestModel : Optional.ofNullable(requestModel.getGoodsList()).orElse(new ArrayList<>())) {
            List<CarrierOrderSignUpForYeloLifeCodeModel> codeDtoList = signGoodsForYeloLifeRequestModel.getCodeDtoList();
            if (CollectionUtil.isEmpty(codeDtoList)) {
                continue;
            }
            BigDecimal reduceTotalSignAmount = codeDtoList.stream().map(CarrierOrderSignUpForYeloLifeCodeModel::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            signGoodsForYeloLifeRequestModel.setSignAmount(reduceTotalSignAmount);
        }
        Result<Boolean> result = carrierOrderForYeloLifeClient.carrierOrderSignUpForYeloLife(requestModel);
        result.throwException();
        return Result.success(true);
    }

    /**
     * 查询签收详情-销售类型 3.26.0
     *
     * @param requestDto 运单id
     * @return 签收详情
     */
    @PostMapping(value = "/api/carrierOrderManagement/carrierOrderBeforeSignUpForYeloLife")
    public Result<List<CarrierOrderBeforeSignUpForYeloLifeResponseDto>> carrierOrderBeforeSignUpForYeloLife(@RequestBody @Valid CarrierOrderBeforeSignUpForYeloLifeRequestDto requestDto) {
        Result<List<CarrierOrderBeforeSignUpForYeloLifeResponseModel>> result = carrierOrderForYeloLifeClient.carrierOrderBeforeSignUpForYeloLife(MapperUtils.mapper(requestDto, CarrierOrderBeforeSignUpForYeloLifeRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), CarrierOrderBeforeSignUpForYeloLifeResponseDto.class, new CarrierOrderBeforeSignUpForYeloLifeMapping()));
    }

    /**
     * 运单签收-销售类型 3.26.0
     *
     * @param requestDto 运单id,签收信息
     * @return 操作结果
     */
    @PostMapping(value = "/api/carrierOrderManagement/carrierOrderConfirmSignUpForYeloLife")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> carrierOrderConfirmSignUpForYeloLife(@RequestBody @Valid CarrierOrderConfirmSignUpForYeloLifeRequestDto requestDto) {
        return carrierOrderForYeloLifeClient.carrierOrderConfirmSignUpForYeloLife(MapperUtils.mapper(requestDto, CarrierOrderConfirmSignUpForYeloLifeRequestModel.class));
    }

    /**
     * 修改卸货地址-查询新生仓库
     *
     * @param requestDto 筛选条件
     * @return 新生仓库列表
     */
    @ApiOperation(value = "修改卸货地址-查询新生仓库 v1.2.1")
    @PostMapping(value = "/api/carrierOrderManagement/getYeloLifeUnloadWarehouse")
    public Result<List<GetYeloLifeUnloadWarehouseResponseDto>> getYeloLifeUnloadWarehouse(@RequestBody @Valid GetYeloLifeUnloadWarehouseRequestDto requestDto) {
        Result<List<GetYeloLifeUnloadWarehouseResponseModel>> result = carrierOrderForYeloLifeClient.getYeloLifeUnloadWarehouse(MapperUtils.mapper(requestDto, GetYeloLifeUnloadWarehouseRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), GetYeloLifeUnloadWarehouseResponseDto.class, new GetYeloLifeUnloadWarehouseMapping()));
    }

    /**
     * 修改卸货地址详情
     *
     * @param requestDto 运单id
     * @return 卸货地址详情
     */
    @ApiOperation(value = "修改卸货地址详情 v1.2.1")
    @PostMapping(value = "/api/carrierOrderManagement/getUnloadAddressDetailForYeloLife")
    public Result<UpdateCarrierOrderUnloadAddressDetailYeloLifeResponseDto> getUnloadAddressDetailForYeloLife(@RequestBody @Valid CarrierOrderIdRequestDto requestDto) {
        Result<UpdateUnloadAddressDetailResponseModel> result = carrierOrderForYeloLifeClient.getUnloadAddressDetailForYeloLife(MapperUtils.mapper(requestDto, CarrierOrderIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), UpdateCarrierOrderUnloadAddressDetailYeloLifeResponseDto.class, new GetUnloadAddressDetailForYeloLifeMapping()));
    }

    /**
     * 修改卸货地址
     *
     * @param requestDto 运单id,卸货地址信息
     * @return 操作结果
     */
    @ApiOperation(value = "确认修改卸货地址 v1.2.1")
    @PostMapping(value = "/api/carrierOrderManagement/updateUnloadAddressConfirmForYeloLife")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> updateUnloadAddressConfirmForYeloLife(@RequestBody @Valid UpdateCarrierOrderUnloadAddressForLifeRequestDto requestDto) {
        Result<Boolean> result = carrierOrderForYeloLifeClient.updateUnloadAddressConfirmForYeloLife(MapperUtils.mapper(requestDto, UpdateCarrierOrderUnloadAddressForLifeRequestModel.class));
        result.throwException();
        return Result.success(true);
    }
}
