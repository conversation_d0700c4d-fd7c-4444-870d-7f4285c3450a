package com.logistics.tms.controller.driverappoint.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SearchAppointResponseModel {

    @ApiModelProperty("司机预约记录id")
    private Long driverAppointId;

    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("乐橘新生客户名称（企业）")
    private String customerName;
    @ApiModelProperty("乐橘新生客户姓名（个人）")
    private String customerUserName;
    @ApiModelProperty("乐橘新生客户手机号（个人）")
    private String customerUserMobile;

    @ApiModelProperty("下单时间 yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    @ApiModelProperty("货物数量(单位吨)")
    private BigDecimal goodsAmountTotal;

    @ApiModelProperty(value = "业务类型：1 公司，2 个人")
    private Integer businessType;
}
