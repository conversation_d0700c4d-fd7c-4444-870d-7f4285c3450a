package com.logistics.management.webapi.client.carrierorderticketsaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class GetReceiptAuditDetailResponseModel {

    @ApiModelProperty(value = "审核人")
    private String auditorName;

    @ApiModelProperty(value = "审核时间")
    private Date auditTime;

    @ApiModelProperty(value = "审核状态; 0 待审核，1 已审核，2 已驳回")
    private Integer auditStatus;

    @ApiModelProperty(value = "回单图片")
    private List<String> ticketImages;

    @ApiModelProperty(value = "备注")
    private String remark;
}
