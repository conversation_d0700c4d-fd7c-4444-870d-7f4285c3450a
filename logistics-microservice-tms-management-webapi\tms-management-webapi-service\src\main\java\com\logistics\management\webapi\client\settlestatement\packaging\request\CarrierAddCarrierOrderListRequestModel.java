package com.logistics.management.webapi.client.settlestatement.packaging.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/17
 */
@Data
public class CarrierAddCarrierOrderListRequestModel extends AbstractPageForm<CarrierAddCarrierOrderListRequestModel> {

	@ApiModelProperty(value = "对账单id")
	private Long settleStatementId;

	@ApiModelProperty(value = "车主id")
	private Long companyCarrierId;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("需求单号")
	private String demandOrderCode;

	@ApiModelProperty("货主名称")
	private String companyEntrustName;

	@ApiModelProperty("车牌号")
	private String vehicleNumber;

	@ApiModelProperty("司机")
	private String driver;

	@ApiModelProperty("签收时间起")
	private String signTimeStart;

	@ApiModelProperty("签收时间止")
	private String signTimeEnd;

	@ApiModelProperty("请求来源:1 后台，2 前台")
	private Integer source;//1 后台，2 前台
}
