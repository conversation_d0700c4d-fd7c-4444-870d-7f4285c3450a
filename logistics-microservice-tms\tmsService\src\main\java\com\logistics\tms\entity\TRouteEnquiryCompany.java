package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2024/07/09
*/
@Data
public class TRouteEnquiryCompany extends BaseEntity {
    /**
    * 路线询价单表id
    */
    @ApiModelProperty("路线询价单表id")
    private Long routeEnquiryId;

    /**
    * 车主公司类型：1 公司，2 个人
    */
    @ApiModelProperty("车主公司类型：1 公司，2 个人")
    private Integer companyCarrierType;

    /**
    * 车主ID
    */
    @ApiModelProperty("车主ID")
    private Long companyCarrierId;

    /**
    * 车主公司名称
    */
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName;

    /**
    * 车主联系人id
    */
    @ApiModelProperty("车主联系人id")
    private Long carrierContactId;

    /**
    * 车主账号名称
    */
    @ApiModelProperty("车主账号名称")
    private String carrierContactName;

    /**
    * 车主账号手机号（原长度50）
    */
    @ApiModelProperty("车主账号手机号（原长度50）")
    private String carrierContactPhone;

    /**
    * 报价状态：-1 未报价，0 未选择，1 已选择，2 已取消
    */
    @ApiModelProperty("报价状态：-1 未报价，0 未选择，1 已选择，2 已取消")
    private Integer quoteStatus;

    /**
    * 报价人
    */
    @ApiModelProperty("报价人")
    private String quoteOperator;

    /**
    * 报价人手机号（原长度50）
    */
    @ApiModelProperty("报价人手机号（原长度50）")
    private String quoteOperatorPhone;

    /**
    * 报价时间
    */
    @ApiModelProperty("报价时间")
    private Date quoteTime;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}