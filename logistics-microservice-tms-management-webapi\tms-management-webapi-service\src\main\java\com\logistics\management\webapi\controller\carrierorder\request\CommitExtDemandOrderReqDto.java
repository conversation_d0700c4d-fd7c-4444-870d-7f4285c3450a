package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CommitExtDemandOrderReqDto {

    @ApiModelProperty("当前的运单id")
    private String carrierOrderId;

    @ApiModelProperty("微信定位的详细地址")
    private String loadAddress;


    /**
     * 定位-经度
     */
    @NotBlank(message = "请获取定位")
    private String longitude;
    /**
     * 定位-纬度
     */
    @NotBlank(message = "请获取定位")
    private String latitude;

    @ApiModelProperty("发货联系人")
    private String consignorName;

    @ApiModelProperty("发货联系方式")
    private String consignorMobile;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("采购单号")
    private String purchaseOrderCode;

    @ApiModelProperty("上游联系人姓名")
    private String upstreamContactPerson;

    @ApiModelProperty("上游联系方式")
    private String upstreamContactPhone;

    @ApiModelProperty("上游企业名称")
    private String upstreamCompanyName;

    @ApiModelProperty("提货数量")
    private String loadAmount;

}
