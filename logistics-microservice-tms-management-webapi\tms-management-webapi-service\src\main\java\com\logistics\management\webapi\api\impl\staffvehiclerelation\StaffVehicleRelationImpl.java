package com.logistics.management.webapi.api.impl.staffvehiclerelation;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.staffvehiclerelation.StaffVehicleRelationApi;
import com.logistics.management.webapi.api.feign.staffvehiclerelation.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.staffvehiclerelation.mapping.SearchStaffVehicleRelationMapping;
import com.logistics.management.webapi.api.impl.staffvehiclerelation.mapping.StaffVehicleDetailMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportStaffVehicleRelation;
import com.logistics.management.webapi.base.constant.ImportExcelHeaders;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.enums.StaffPropertyEnum;
import com.logistics.management.webapi.base.enums.VehicleCategoryEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.staffvehiclerelation.StaffVehicleRelationServiceApi;
import com.logistics.tms.api.feign.staffvehiclerelation.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * @Author: sj
 * @Date: 2019/7/26 14:03
 */
@Slf4j
@RestController
public class StaffVehicleRelationImpl implements StaffVehicleRelationApi {
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private StaffVehicleRelationServiceApi staffVehicleRelationServiceApi;

    /**
     * 列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchStaffVehicleListResponseDto>> getStaffVehicleRelation(@RequestBody SearchStaffVehicleListRequestDto requestDto) {

        Result<PageInfo<SearchStaffVehicleListResponseModel>> result = staffVehicleRelationServiceApi.getStaffVehicleRelation(MapperUtils.mapper(requestDto, SearchStaffVehicleListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        pageInfo.setList(MapperUtils.mapper(result.getData().getList(),SearchStaffVehicleListResponseDto.class,new SearchStaffVehicleRelationMapping()));
        return Result.success(pageInfo);
    }

    /**
     * 详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<StaffVehicleDetailResponseDto> getStaffVehicleDetail(@RequestBody @Valid StaffVehicleDetailRequestDto requestDto) {
        Result<StaffVehicleDetailResponseModel> result = staffVehicleRelationServiceApi.getStaffVehicleDetail(MapperUtils.mapper(requestDto, StaffVehicleDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),StaffVehicleDetailResponseDto.class,new StaffVehicleDetailMapping()));
    }

    /**
     * 修改/保存
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> saveOrModifyStaffVehicleRelation(@RequestBody @Valid SaveOrModifyStaffVehicleRequestDto requestDto) {
        //数据校验
        if (CommonConstant.ONE.equals(requestDto.getIsOurCompany())) {
            //我司
            requestDto.setCompanyCarrierId(null);
            requestDto.setVehicleId(null);
        } else {
            //其他车主
            if (StringUtils.isBlank(requestDto.getCompanyCarrierId())) {
                throw new BizException(ManagementWebApiExceptionEnum.COMPANY_CARRIER_ID_IS_NULL);
            }
            if (StringUtils.isBlank(requestDto.getVehicleId())) {
                throw new BizException(ManagementWebApiExceptionEnum.CHOOSE_CARDATA);
            }
        }
        Result<Boolean> result = staffVehicleRelationServiceApi.saveOrModifyStaffVehicleRelation(MapperUtils.mapperNoDefault(requestDto, SaveOrModifyStaffVehicleRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 删除
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> deleteStaffVehicleRelation(@RequestBody @Valid DeleteStaffVehicleRequestDto requestDto) {
        Result<Boolean> result = staffVehicleRelationServiceApi.deleteStaffVehicleRelation(MapperUtils.mapper(requestDto,DeleteStaffVehicleRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 导出
     * @param requestDto
     * @param response
     */
    @Override
    public void exportStaffVehicleInfo(SearchStaffVehicleListRequestDto requestDto, HttpServletResponse response) {
        Result<List<SearchStaffVehicleListResponseModel>> result = staffVehicleRelationServiceApi.exportStaffVehicleInfo(MapperUtils.mapper(requestDto,SearchStaffVehicleListRequestModel.class));
        result.throwException();
        String fileName = "车辆司机管理数据" + DateUtils.dateToString(new Date(), DateUtils.FORMAT_LONG);
        List<SearchStaffVehicleListResponseDto> resultList = MapperUtils.mapper(result.getData(),SearchStaffVehicleListResponseDto.class,new SearchStaffVehicleRelationMapping());
        ExcelUtils.exportExcelForServlet(fileName, fileName, ExportStaffVehicleRelation.getStaffVehicleRelation(), response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return resultList;
            }
        });
    }

    /**
     * 导入
     * @param file
     * @param request
     * @return
     */
    @Override
    public Result<ImportStaffVehicleResponseDto> importStaffVehicleInfo(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        if (null == file) {
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_STAFF_VEHICLE_FILE_IS_EMPTY);
        }
        InputStream in;
        try {
            in = file.getInputStream();
        } catch (IOException e) {
            log.error("导入司机车辆关联关系失败，",e);
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_STAFF_VEHICLE_FILE_IS_EMPTY);
        }
        List<List<Object>> excelList = new ImportExcelUtil().getDataListByExcel(in, file.getOriginalFilename(), ImportExcelHeaders.getImportStaffVehicleType());
        ImportStaffVehicleRequestDto importRelationRequestDto = this.initImportData(excelList);
        ImportStaffVehicleRequestModel importRelationRequestModel = MapperUtils.mapper(importRelationRequestDto,ImportStaffVehicleRequestModel.class);

        if(ListUtils.isNotEmpty(importRelationRequestModel.getImportList())){
            importRelationRequestModel.getImportList().stream().forEach(t->
                commonBiz.convertObjectFieldToNullIfIsEmpty(t)
            );
        }
        Result<ImportStaffVehicleResponseModel> result = staffVehicleRelationServiceApi.importStaffVehicleInfo(importRelationRequestModel);
        result.throwException();

        return Result.success(MapperUtils.mapper(result.getData(),ImportStaffVehicleResponseDto.class));
    }
    //入参校验及转换
    private ImportStaffVehicleRequestDto initImportData(List<List<Object>> excelList){
        ImportStaffVehicleRequestDto requestDto = new ImportStaffVehicleRequestDto();
        if(ListUtils.isEmpty(excelList)){
            return requestDto;
        }
        Integer numberFailures = CommonConstant.INTEGER_ZERO;
        List<StaffVehicleRelationRequestDto> importList = new ArrayList<>();
        Set<StaffVehicleRelationRequestDto> setContains = new HashSet<>();
        StaffVehicleRelationRequestDto singleRelationDto;

        for (int i = 0; i < excelList.size(); i++) {
            List<Object> objects = excelList.get(i);
            if(objects!=null){
                //关联车辆机构
                String typeLabel = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ZERO));
                if (StringUtils.isBlank(typeLabel) || (!StaffPropertyEnum.OWNER.getValue().equals(typeLabel.trim()) && !StaffPropertyEnum.AFFILIATION.getValue().equals(typeLabel.trim()))) {
                    numberFailures++;
                    continue;
                }

                String type = ConverterUtils.toString(StaffPropertyEnum.getEnumByValue(typeLabel.trim()).getKey());
                if (!type.equals(ConverterUtils.toString(StaffPropertyEnum.OWNER.getKey())) && !type.equals(ConverterUtils.toString(StaffPropertyEnum.AFFILIATION.getKey()))) {
                    numberFailures++;
                    continue;
                }

                //关联车辆类别
                String  vehicleCategoryLabel =ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ONE));
                if(StringUtils.isBlank(vehicleCategoryLabel) ||(!VehicleCategoryEnum.TRACTOR.getValue().equals(vehicleCategoryLabel.trim()) && !VehicleCategoryEnum.WHOLE.getValue().equals(vehicleCategoryLabel.trim())) ){
                    numberFailures ++;
                    continue;
                }
                String vehicleCategoryType = ConverterUtils.toString(VehicleCategoryEnum.getKeyByValue(vehicleCategoryLabel.trim()));
                if(!vehicleCategoryType.equals(ConverterUtils.toString(VehicleCategoryEnum.TRACTOR.getKey())) && !vehicleCategoryType.equals(ConverterUtils.toString(VehicleCategoryEnum.WHOLE.getKey()))){
                    numberFailures ++;
                    continue;
                }


               //牵引车或一体车车牌号
                String tractorVehicleNo = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_TWO));
                if(StringUtils.isBlank(tractorVehicleNo) || !FrequentMethodUtils.validateVehicleFormat(tractorVehicleNo)){
                    numberFailures ++;
                    continue;
                }

               //挂车车牌号
                String trailerVehicleNo = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_THREE));
                if(StaffPropertyEnum.OWNER.getKey().toString().equals(type) && VehicleCategoryEnum.TRACTOR.getKey().toString().equals(vehicleCategoryType)){
                    if(StringUtils.isBlank(trailerVehicleNo) || !FrequentMethodUtils.validateVehicleFormat(trailerVehicleNo)){
                        numberFailures ++;
                        continue;
                    }
                }else{
                    if(StringUtils.isNotBlank(trailerVehicleNo) && !FrequentMethodUtils.validateVehicleFormat(trailerVehicleNo)){
                        numberFailures ++;
                        continue;
                    }
                }

                // 司机姓名
                String staffName = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_FOUR));
                if(StringUtils.isBlank(staffName)){
                    numberFailures ++;
                    continue;
                }

                //司机电话
                String staffMobile = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_FIVE));
                if(StringUtils.isBlank(staffMobile) || !FrequentMethodUtils.validateTelFormat(staffMobile)){
                    numberFailures ++;
                    continue;
                }
                //备注
                String remark = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_SIX));

                singleRelationDto = new StaffVehicleRelationRequestDto();
                singleRelationDto.setType(type);
                singleRelationDto.setStaffName(staffName);
                singleRelationDto.setStaffMobile(staffMobile);
                singleRelationDto.setVehicleCategory(vehicleCategoryType);
                singleRelationDto.setTractorVehicleNo(tractorVehicleNo);
                singleRelationDto.setTrailerVehicleNo(trailerVehicleNo);
                singleRelationDto.setRemark(remark);
                setContains.add(singleRelationDto);
            }
        } 

        if(!setContains.isEmpty()){
            importList.addAll(setContains);
        }
        requestDto.setImportList(importList);
        requestDto.setNumberFailures(ConverterUtils.toString(numberFailures));
        return requestDto;
    }

    /**
     * 车辆检查使用-获取车辆司机列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<SafeCheckStaffRelResponseDto>> getVehicleStaffRel(@RequestBody  SafeCheckStaffRelRequestDto requestDto) {
        Result<List<SafeCheckStaffRelResponseModel>> result = staffVehicleRelationServiceApi.getVehicleStaffRel(MapperUtils.mapper(requestDto,SafeCheckStaffRelRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),SafeCheckStaffRelResponseDto.class));
    }


    /**
     * 模糊查询司机信息(车辆机构自营，搜索自主和自营司机)
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<GetFuzzyQueryDriverInfoResponseDto>> fuzzyQueryDriverInfo(@RequestBody GetFuzzyQueryDriverInfoRequestDto requestDto) {
        if (CommonConstant.TWO.equals(requestDto.getIsOurCompany())){
            if (StringUtils.isBlank(requestDto.getCompanyCarrierId())) {
                return Result.success(new ArrayList<>());
            }
            requestDto.setType("");
        }
        Result<List<GetFuzzyQueryDriverInfoResponseModel>> result = staffVehicleRelationServiceApi.fuzzyQueryDriverInfo(MapperUtils.mapper(requestDto, GetFuzzyQueryDriverInfoRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), GetFuzzyQueryDriverInfoResponseDto.class));
    }
}
