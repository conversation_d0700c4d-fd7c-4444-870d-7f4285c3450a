package com.logistics.tms.controller.reservationorder.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2024/8/19 09:48
 */
@Data
public class ReservationOrderSummaryListResponseModel {

    /**
     * 预约单id
     */
    private Long reservationOrderId;

    /**
     * 预约单号
     */
    private String reservationOrderCode;

    /**
     * 预约类型：1 提货，2 卸货
     */
    private Integer reservationType;

    /**
     * 预约开始时间
     */
    private Date reservationStartTime;
    /**
     * 预约结束时间
     */
    private Date reservationEndTime;

    /**
     * 省
     */
    private String provinceName;
    /**
     * 市
     */
    private String cityName;
    /**
     * 区
     */
    private String areaName;
    /**
     * 详细地址
     */
    private String detailAddress;
    /**
     * 仓库名称
     */
    private String warehouse;

    /**
     * 数量
     */
    private BigDecimal expectAmount;

    /**
     * 车牌号
     */
    private String vehicleNo;
}
