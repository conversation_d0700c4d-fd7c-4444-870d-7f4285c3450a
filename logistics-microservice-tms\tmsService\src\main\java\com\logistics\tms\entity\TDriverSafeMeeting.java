package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TDriverSafeMeeting extends BaseEntity {
    /**
    * 安全例会类型：1 安全例会，2 紧急培训
    */
    @ApiModelProperty("安全例会类型：1 安全例会，2 紧急培训")
    private Integer type;

    /**
    * 标题
    */
    @ApiModelProperty("标题")
    private String title;

    /**
    * 周期（学习月份）
    */
    @ApiModelProperty("周期（学习月份）")
    private String period;

    /**
    * 简介
    */
    @ApiModelProperty("简介")
    private String introduction;

    /**
    * 驾驶员总数量
    */
    @ApiModelProperty("驾驶员总数量")
    private Integer staffCount;

    /**
    * 内容
    */
    @ApiModelProperty("内容")
    private String content;
}