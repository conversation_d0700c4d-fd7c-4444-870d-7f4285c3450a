<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TSinopecOriginalDataMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TSinopecOriginalData">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="im_guid" jdbcType="VARCHAR" property="imGuid" />
    <result column="send_time" jdbcType="VARCHAR" property="sendTime" />
    <result column="sender" jdbcType="VARCHAR" property="sender" />
    <result column="receiver" jdbcType="VARCHAR" property="receiver" />
    <result column="receive_ifid" jdbcType="VARCHAR" property="receiveIfid" />
    <result column="receive_method" jdbcType="VARCHAR" property="receiveMethod" />
    <result column="send_operator" jdbcType="VARCHAR" property="sendOperator" />
    <result column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="online_goods_flag" jdbcType="INTEGER" property="onlineGoodsFlag" />
    <result column="consignor_code" jdbcType="VARCHAR" property="consignorCode" />
    <result column="consignor_name" jdbcType="VARCHAR" property="consignorName" />
    <result column="carrier_code" jdbcType="VARCHAR" property="carrierCode" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="associated_date" jdbcType="DATE" property="associatedDate" />
    <result column="arrival_deadline" jdbcType="DATE" property="arrivalDeadline" />
    <result column="short_split_count" jdbcType="INTEGER" property="shortSplitCount" />
    <result column="transport_type" jdbcType="INTEGER" property="transportType" />
    <result column="transport_type_remark" jdbcType="VARCHAR" property="transportTypeRemark" />
    <result column="associated_count" jdbcType="DECIMAL" property="associatedCount" />
    <result column="has_hazardous_chemicals" jdbcType="INTEGER" property="hasHazardousChemicals" />
    <result column="associated_remark" jdbcType="VARCHAR" property="associatedRemark" />
    <result column="plan_package_quantity" jdbcType="INTEGER" property="planPackageQuantity" />
    <result column="estimate_cost" jdbcType="DECIMAL" property="estimateCost" />
    <result column="audit_user" jdbcType="VARCHAR" property="auditUser" />
    <result column="is_abroad" jdbcType="INTEGER" property="isAbroad" />
    <result column="earliest_load_time" jdbcType="BIGINT" property="earliestLoadTime" />
    <result column="latest_load_time" jdbcType="BIGINT" property="latestLoadTime" />
    <result column="marks_quantity" jdbcType="INTEGER" property="marksQuantity" />
    <result column="marks_amount" jdbcType="DECIMAL" property="marksAmount" />
    <result column="tray_quantity" jdbcType="INTEGER" property="trayQuantity" />
    <result column="tray_amount" jdbcType="DECIMAL" property="trayAmount" />
    <result column="trade_position_type" jdbcType="INTEGER" property="tradePositionType" />
    <result column="trade_type" jdbcType="VARCHAR" property="tradeType" />
    <result column="handover_name" jdbcType="VARCHAR" property="handoverName" />
    <result column="origin_country" jdbcType="VARCHAR" property="originCountry" />
    <result column="origin_country_code" jdbcType="VARCHAR" property="originCountryCode" />
    <result column="destination_country" jdbcType="VARCHAR" property="destinationCountry" />
    <result column="destination_country_code" jdbcType="VARCHAR" property="destinationCountryCode" />
    <result column="port" jdbcType="VARCHAR" property="port" />
    <result column="port_name" jdbcType="VARCHAR" property="portName" />
    <result column="string_station" jdbcType="VARCHAR" property="stringStation" />
    <result column="station_name" jdbcType="VARCHAR" property="stationName" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="consign_name" jdbcType="VARCHAR" property="consignName" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="manufacturer_name" jdbcType="VARCHAR" property="manufacturerName" />
    <result column="expect_exe_date" jdbcType="TIMESTAMP" property="expectExeDate" />
    <result column="trans_mode" jdbcType="VARCHAR" property="transMode" />
    <result column="item_category1_name" jdbcType="VARCHAR" property="itemCategory1Name" />
    <result column="item_category_name" jdbcType="VARCHAR" property="itemCategoryName" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="item_trans_group_name" jdbcType="VARCHAR" property="itemTransGroupName" />
    <result column="item_pack_spec_name" jdbcType="VARCHAR" property="itemPackSpecName" />
    <result column="qty" jdbcType="DECIMAL" property="qty" />
    <result column="uom_name" jdbcType="VARCHAR" property="uomName" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="out_warehouse_name" jdbcType="VARCHAR" property="outWarehouseName" />
    <result column="load_zone_name" jdbcType="VARCHAR" property="loadZoneName" />
    <result column="load_zone_code" jdbcType="VARCHAR" property="loadZoneCode" />
    <result column="recv_district_code" jdbcType="VARCHAR" property="recvDistrictCode" />
    <result column="recv_district_name" jdbcType="VARCHAR" property="recvDistrictName" />
    <result column="invalidating_start_date" jdbcType="TIMESTAMP" property="invalidatingStartDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="remark1" jdbcType="VARCHAR" property="remark1" />
    <result column="remark2" jdbcType="VARCHAR" property="remark2" />
    <result column="remark3" jdbcType="VARCHAR" property="remark3" />
    <result column="remark4" jdbcType="VARCHAR" property="remark4" />
    <result column="remark5" jdbcType="VARCHAR" property="remark5" />
    <result column="remark6" jdbcType="VARCHAR" property="remark6" />
    <result column="remark7" jdbcType="VARCHAR" property="remark7" />
    <result column="remark8" jdbcType="VARCHAR" property="remark8" />
    <result column="remark9" jdbcType="VARCHAR" property="remark9" />
    <result column="remark10" jdbcType="VARCHAR" property="remark10" />
    <result column="if_has_demand_order" jdbcType="INTEGER" property="ifHasDemandOrder" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, im_guid, send_time, sender, receiver, receive_ifid, receive_method, send_operator, 
    customer_id, customer_name, online_goods_flag, consignor_code, consignor_name, carrier_code, 
    carrier_name, associated_date, arrival_deadline, short_split_count, transport_type, 
    transport_type_remark, associated_count, has_hazardous_chemicals, associated_remark, 
    plan_package_quantity, estimate_cost, audit_user, is_abroad, earliest_load_time, 
    latest_load_time, marks_quantity, marks_amount, tray_quantity, tray_amount, trade_position_type, 
    trade_type, handover_name, origin_country, origin_country_code, destination_country, 
    destination_country_code, port, port_name, string_station, station_name, province_name, 
    province, city_name, city, type, consign_name, order_no, manufacturer_name, expect_exe_date, 
    trans_mode, item_category1_name, item_category_name, item_code, item_name, item_trans_group_name, 
    item_pack_spec_name, qty, uom_name, sn, out_warehouse_name, load_zone_name, load_zone_code, 
    recv_district_code, recv_district_name, invalidating_start_date, remark, remark1, 
    remark2, remark3, remark4, remark5, remark6, remark7, remark8, remark9, remark10, 
    if_has_demand_order, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_sinopec_original_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_sinopec_original_data
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TSinopecOriginalData">
    insert into t_sinopec_original_data (id, im_guid, send_time, 
      sender, receiver, receive_ifid, 
      receive_method, send_operator, customer_id, 
      customer_name, online_goods_flag, consignor_code, 
      consignor_name, carrier_code, carrier_name, 
      associated_date, arrival_deadline, short_split_count, 
      transport_type, transport_type_remark, associated_count, 
      has_hazardous_chemicals, associated_remark, 
      plan_package_quantity, estimate_cost, audit_user, 
      is_abroad, earliest_load_time, latest_load_time, 
      marks_quantity, marks_amount, tray_quantity, 
      tray_amount, trade_position_type, trade_type, 
      handover_name, origin_country, origin_country_code, 
      destination_country, destination_country_code, 
      port, port_name, string_station, 
      station_name, province_name, province, 
      city_name, city, type, 
      consign_name, order_no, manufacturer_name, 
      expect_exe_date, trans_mode, item_category1_name, 
      item_category_name, item_code, item_name, 
      item_trans_group_name, item_pack_spec_name, qty, 
      uom_name, sn, out_warehouse_name, 
      load_zone_name, load_zone_code, recv_district_code, 
      recv_district_name, invalidating_start_date, 
      remark, remark1, remark2, 
      remark3, remark4, remark5, 
      remark6, remark7, remark8, 
      remark9, remark10, if_has_demand_order, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{imGuid,jdbcType=VARCHAR}, #{sendTime,jdbcType=VARCHAR}, 
      #{sender,jdbcType=VARCHAR}, #{receiver,jdbcType=VARCHAR}, #{receiveIfid,jdbcType=VARCHAR}, 
      #{receiveMethod,jdbcType=VARCHAR}, #{sendOperator,jdbcType=VARCHAR}, #{customerId,jdbcType=BIGINT}, 
      #{customerName,jdbcType=VARCHAR}, #{onlineGoodsFlag,jdbcType=INTEGER}, #{consignorCode,jdbcType=VARCHAR}, 
      #{consignorName,jdbcType=VARCHAR}, #{carrierCode,jdbcType=VARCHAR}, #{carrierName,jdbcType=VARCHAR}, 
      #{associatedDate,jdbcType=DATE}, #{arrivalDeadline,jdbcType=DATE}, #{shortSplitCount,jdbcType=INTEGER}, 
      #{transportType,jdbcType=INTEGER}, #{transportTypeRemark,jdbcType=VARCHAR}, #{associatedCount,jdbcType=DECIMAL}, 
      #{hasHazardousChemicals,jdbcType=INTEGER}, #{associatedRemark,jdbcType=VARCHAR}, 
      #{planPackageQuantity,jdbcType=INTEGER}, #{estimateCost,jdbcType=DECIMAL}, #{auditUser,jdbcType=VARCHAR}, 
      #{isAbroad,jdbcType=INTEGER}, #{earliestLoadTime,jdbcType=BIGINT}, #{latestLoadTime,jdbcType=BIGINT}, 
      #{marksQuantity,jdbcType=INTEGER}, #{marksAmount,jdbcType=DECIMAL}, #{trayQuantity,jdbcType=INTEGER}, 
      #{trayAmount,jdbcType=DECIMAL}, #{tradePositionType,jdbcType=INTEGER}, #{tradeType,jdbcType=VARCHAR}, 
      #{handoverName,jdbcType=VARCHAR}, #{originCountry,jdbcType=VARCHAR}, #{originCountryCode,jdbcType=VARCHAR}, 
      #{destinationCountry,jdbcType=VARCHAR}, #{destinationCountryCode,jdbcType=VARCHAR}, 
      #{port,jdbcType=VARCHAR}, #{portName,jdbcType=VARCHAR}, #{stringStation,jdbcType=VARCHAR}, 
      #{stationName,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, 
      #{cityName,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{consignName,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{manufacturerName,jdbcType=VARCHAR}, 
      #{expectExeDate,jdbcType=TIMESTAMP}, #{transMode,jdbcType=VARCHAR}, #{itemCategory1Name,jdbcType=VARCHAR}, 
      #{itemCategoryName,jdbcType=VARCHAR}, #{itemCode,jdbcType=VARCHAR}, #{itemName,jdbcType=VARCHAR}, 
      #{itemTransGroupName,jdbcType=VARCHAR}, #{itemPackSpecName,jdbcType=VARCHAR}, #{qty,jdbcType=DECIMAL}, 
      #{uomName,jdbcType=VARCHAR}, #{sn,jdbcType=VARCHAR}, #{outWarehouseName,jdbcType=VARCHAR}, 
      #{loadZoneName,jdbcType=VARCHAR}, #{loadZoneCode,jdbcType=VARCHAR}, #{recvDistrictCode,jdbcType=VARCHAR}, 
      #{recvDistrictName,jdbcType=VARCHAR}, #{invalidatingStartDate,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=VARCHAR}, #{remark1,jdbcType=VARCHAR}, #{remark2,jdbcType=VARCHAR}, 
      #{remark3,jdbcType=VARCHAR}, #{remark4,jdbcType=VARCHAR}, #{remark5,jdbcType=VARCHAR}, 
      #{remark6,jdbcType=VARCHAR}, #{remark7,jdbcType=VARCHAR}, #{remark8,jdbcType=VARCHAR}, 
      #{remark9,jdbcType=VARCHAR}, #{remark10,jdbcType=VARCHAR}, #{ifHasDemandOrder,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TSinopecOriginalData" keyProperty="id" useGeneratedKeys="true">
    insert into t_sinopec_original_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="imGuid != null">
        im_guid,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="sender != null">
        sender,
      </if>
      <if test="receiver != null">
        receiver,
      </if>
      <if test="receiveIfid != null">
        receive_ifid,
      </if>
      <if test="receiveMethod != null">
        receive_method,
      </if>
      <if test="sendOperator != null">
        send_operator,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="onlineGoodsFlag != null">
        online_goods_flag,
      </if>
      <if test="consignorCode != null">
        consignor_code,
      </if>
      <if test="consignorName != null">
        consignor_name,
      </if>
      <if test="carrierCode != null">
        carrier_code,
      </if>
      <if test="carrierName != null">
        carrier_name,
      </if>
      <if test="associatedDate != null">
        associated_date,
      </if>
      <if test="arrivalDeadline != null">
        arrival_deadline,
      </if>
      <if test="shortSplitCount != null">
        short_split_count,
      </if>
      <if test="transportType != null">
        transport_type,
      </if>
      <if test="transportTypeRemark != null">
        transport_type_remark,
      </if>
      <if test="associatedCount != null">
        associated_count,
      </if>
      <if test="hasHazardousChemicals != null">
        has_hazardous_chemicals,
      </if>
      <if test="associatedRemark != null">
        associated_remark,
      </if>
      <if test="planPackageQuantity != null">
        plan_package_quantity,
      </if>
      <if test="estimateCost != null">
        estimate_cost,
      </if>
      <if test="auditUser != null">
        audit_user,
      </if>
      <if test="isAbroad != null">
        is_abroad,
      </if>
      <if test="earliestLoadTime != null">
        earliest_load_time,
      </if>
      <if test="latestLoadTime != null">
        latest_load_time,
      </if>
      <if test="marksQuantity != null">
        marks_quantity,
      </if>
      <if test="marksAmount != null">
        marks_amount,
      </if>
      <if test="trayQuantity != null">
        tray_quantity,
      </if>
      <if test="trayAmount != null">
        tray_amount,
      </if>
      <if test="tradePositionType != null">
        trade_position_type,
      </if>
      <if test="tradeType != null">
        trade_type,
      </if>
      <if test="handoverName != null">
        handover_name,
      </if>
      <if test="originCountry != null">
        origin_country,
      </if>
      <if test="originCountryCode != null">
        origin_country_code,
      </if>
      <if test="destinationCountry != null">
        destination_country,
      </if>
      <if test="destinationCountryCode != null">
        destination_country_code,
      </if>
      <if test="port != null">
        port,
      </if>
      <if test="portName != null">
        port_name,
      </if>
      <if test="stringStation != null">
        string_station,
      </if>
      <if test="stationName != null">
        station_name,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="consignName != null">
        consign_name,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="manufacturerName != null">
        manufacturer_name,
      </if>
      <if test="expectExeDate != null">
        expect_exe_date,
      </if>
      <if test="transMode != null">
        trans_mode,
      </if>
      <if test="itemCategory1Name != null">
        item_category1_name,
      </if>
      <if test="itemCategoryName != null">
        item_category_name,
      </if>
      <if test="itemCode != null">
        item_code,
      </if>
      <if test="itemName != null">
        item_name,
      </if>
      <if test="itemTransGroupName != null">
        item_trans_group_name,
      </if>
      <if test="itemPackSpecName != null">
        item_pack_spec_name,
      </if>
      <if test="qty != null">
        qty,
      </if>
      <if test="uomName != null">
        uom_name,
      </if>
      <if test="sn != null">
        sn,
      </if>
      <if test="outWarehouseName != null">
        out_warehouse_name,
      </if>
      <if test="loadZoneName != null">
        load_zone_name,
      </if>
      <if test="loadZoneCode != null">
        load_zone_code,
      </if>
      <if test="recvDistrictCode != null">
        recv_district_code,
      </if>
      <if test="recvDistrictName != null">
        recv_district_name,
      </if>
      <if test="invalidatingStartDate != null">
        invalidating_start_date,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="remark1 != null">
        remark1,
      </if>
      <if test="remark2 != null">
        remark2,
      </if>
      <if test="remark3 != null">
        remark3,
      </if>
      <if test="remark4 != null">
        remark4,
      </if>
      <if test="remark5 != null">
        remark5,
      </if>
      <if test="remark6 != null">
        remark6,
      </if>
      <if test="remark7 != null">
        remark7,
      </if>
      <if test="remark8 != null">
        remark8,
      </if>
      <if test="remark9 != null">
        remark9,
      </if>
      <if test="remark10 != null">
        remark10,
      </if>
      <if test="ifHasDemandOrder != null">
        if_has_demand_order,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="imGuid != null">
        #{imGuid,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=VARCHAR},
      </if>
      <if test="sender != null">
        #{sender,jdbcType=VARCHAR},
      </if>
      <if test="receiver != null">
        #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="receiveIfid != null">
        #{receiveIfid,jdbcType=VARCHAR},
      </if>
      <if test="receiveMethod != null">
        #{receiveMethod,jdbcType=VARCHAR},
      </if>
      <if test="sendOperator != null">
        #{sendOperator,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="onlineGoodsFlag != null">
        #{onlineGoodsFlag,jdbcType=INTEGER},
      </if>
      <if test="consignorCode != null">
        #{consignorCode,jdbcType=VARCHAR},
      </if>
      <if test="consignorName != null">
        #{consignorName,jdbcType=VARCHAR},
      </if>
      <if test="carrierCode != null">
        #{carrierCode,jdbcType=VARCHAR},
      </if>
      <if test="carrierName != null">
        #{carrierName,jdbcType=VARCHAR},
      </if>
      <if test="associatedDate != null">
        #{associatedDate,jdbcType=DATE},
      </if>
      <if test="arrivalDeadline != null">
        #{arrivalDeadline,jdbcType=DATE},
      </if>
      <if test="shortSplitCount != null">
        #{shortSplitCount,jdbcType=INTEGER},
      </if>
      <if test="transportType != null">
        #{transportType,jdbcType=INTEGER},
      </if>
      <if test="transportTypeRemark != null">
        #{transportTypeRemark,jdbcType=VARCHAR},
      </if>
      <if test="associatedCount != null">
        #{associatedCount,jdbcType=DECIMAL},
      </if>
      <if test="hasHazardousChemicals != null">
        #{hasHazardousChemicals,jdbcType=INTEGER},
      </if>
      <if test="associatedRemark != null">
        #{associatedRemark,jdbcType=VARCHAR},
      </if>
      <if test="planPackageQuantity != null">
        #{planPackageQuantity,jdbcType=INTEGER},
      </if>
      <if test="estimateCost != null">
        #{estimateCost,jdbcType=DECIMAL},
      </if>
      <if test="auditUser != null">
        #{auditUser,jdbcType=VARCHAR},
      </if>
      <if test="isAbroad != null">
        #{isAbroad,jdbcType=INTEGER},
      </if>
      <if test="earliestLoadTime != null">
        #{earliestLoadTime,jdbcType=BIGINT},
      </if>
      <if test="latestLoadTime != null">
        #{latestLoadTime,jdbcType=BIGINT},
      </if>
      <if test="marksQuantity != null">
        #{marksQuantity,jdbcType=INTEGER},
      </if>
      <if test="marksAmount != null">
        #{marksAmount,jdbcType=DECIMAL},
      </if>
      <if test="trayQuantity != null">
        #{trayQuantity,jdbcType=INTEGER},
      </if>
      <if test="trayAmount != null">
        #{trayAmount,jdbcType=DECIMAL},
      </if>
      <if test="tradePositionType != null">
        #{tradePositionType,jdbcType=INTEGER},
      </if>
      <if test="tradeType != null">
        #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="handoverName != null">
        #{handoverName,jdbcType=VARCHAR},
      </if>
      <if test="originCountry != null">
        #{originCountry,jdbcType=VARCHAR},
      </if>
      <if test="originCountryCode != null">
        #{originCountryCode,jdbcType=VARCHAR},
      </if>
      <if test="destinationCountry != null">
        #{destinationCountry,jdbcType=VARCHAR},
      </if>
      <if test="destinationCountryCode != null">
        #{destinationCountryCode,jdbcType=VARCHAR},
      </if>
      <if test="port != null">
        #{port,jdbcType=VARCHAR},
      </if>
      <if test="portName != null">
        #{portName,jdbcType=VARCHAR},
      </if>
      <if test="stringStation != null">
        #{stringStation,jdbcType=VARCHAR},
      </if>
      <if test="stationName != null">
        #{stationName,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="consignName != null">
        #{consignName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerName != null">
        #{manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="expectExeDate != null">
        #{expectExeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="transMode != null">
        #{transMode,jdbcType=VARCHAR},
      </if>
      <if test="itemCategory1Name != null">
        #{itemCategory1Name,jdbcType=VARCHAR},
      </if>
      <if test="itemCategoryName != null">
        #{itemCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemTransGroupName != null">
        #{itemTransGroupName,jdbcType=VARCHAR},
      </if>
      <if test="itemPackSpecName != null">
        #{itemPackSpecName,jdbcType=VARCHAR},
      </if>
      <if test="qty != null">
        #{qty,jdbcType=DECIMAL},
      </if>
      <if test="uomName != null">
        #{uomName,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="outWarehouseName != null">
        #{outWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="loadZoneName != null">
        #{loadZoneName,jdbcType=VARCHAR},
      </if>
      <if test="loadZoneCode != null">
        #{loadZoneCode,jdbcType=VARCHAR},
      </if>
      <if test="recvDistrictCode != null">
        #{recvDistrictCode,jdbcType=VARCHAR},
      </if>
      <if test="recvDistrictName != null">
        #{recvDistrictName,jdbcType=VARCHAR},
      </if>
      <if test="invalidatingStartDate != null">
        #{invalidatingStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="remark1 != null">
        #{remark1,jdbcType=VARCHAR},
      </if>
      <if test="remark2 != null">
        #{remark2,jdbcType=VARCHAR},
      </if>
      <if test="remark3 != null">
        #{remark3,jdbcType=VARCHAR},
      </if>
      <if test="remark4 != null">
        #{remark4,jdbcType=VARCHAR},
      </if>
      <if test="remark5 != null">
        #{remark5,jdbcType=VARCHAR},
      </if>
      <if test="remark6 != null">
        #{remark6,jdbcType=VARCHAR},
      </if>
      <if test="remark7 != null">
        #{remark7,jdbcType=VARCHAR},
      </if>
      <if test="remark8 != null">
        #{remark8,jdbcType=VARCHAR},
      </if>
      <if test="remark9 != null">
        #{remark9,jdbcType=VARCHAR},
      </if>
      <if test="remark10 != null">
        #{remark10,jdbcType=VARCHAR},
      </if>
      <if test="ifHasDemandOrder != null">
        #{ifHasDemandOrder,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TSinopecOriginalData">
    update t_sinopec_original_data
    <set>
      <if test="imGuid != null">
        im_guid = #{imGuid,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=VARCHAR},
      </if>
      <if test="sender != null">
        sender = #{sender,jdbcType=VARCHAR},
      </if>
      <if test="receiver != null">
        receiver = #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="receiveIfid != null">
        receive_ifid = #{receiveIfid,jdbcType=VARCHAR},
      </if>
      <if test="receiveMethod != null">
        receive_method = #{receiveMethod,jdbcType=VARCHAR},
      </if>
      <if test="sendOperator != null">
        send_operator = #{sendOperator,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=BIGINT},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="onlineGoodsFlag != null">
        online_goods_flag = #{onlineGoodsFlag,jdbcType=INTEGER},
      </if>
      <if test="consignorCode != null">
        consignor_code = #{consignorCode,jdbcType=VARCHAR},
      </if>
      <if test="consignorName != null">
        consignor_name = #{consignorName,jdbcType=VARCHAR},
      </if>
      <if test="carrierCode != null">
        carrier_code = #{carrierCode,jdbcType=VARCHAR},
      </if>
      <if test="carrierName != null">
        carrier_name = #{carrierName,jdbcType=VARCHAR},
      </if>
      <if test="associatedDate != null">
        associated_date = #{associatedDate,jdbcType=DATE},
      </if>
      <if test="arrivalDeadline != null">
        arrival_deadline = #{arrivalDeadline,jdbcType=DATE},
      </if>
      <if test="shortSplitCount != null">
        short_split_count = #{shortSplitCount,jdbcType=INTEGER},
      </if>
      <if test="transportType != null">
        transport_type = #{transportType,jdbcType=INTEGER},
      </if>
      <if test="transportTypeRemark != null">
        transport_type_remark = #{transportTypeRemark,jdbcType=VARCHAR},
      </if>
      <if test="associatedCount != null">
        associated_count = #{associatedCount,jdbcType=DECIMAL},
      </if>
      <if test="hasHazardousChemicals != null">
        has_hazardous_chemicals = #{hasHazardousChemicals,jdbcType=INTEGER},
      </if>
      <if test="associatedRemark != null">
        associated_remark = #{associatedRemark,jdbcType=VARCHAR},
      </if>
      <if test="planPackageQuantity != null">
        plan_package_quantity = #{planPackageQuantity,jdbcType=INTEGER},
      </if>
      <if test="estimateCost != null">
        estimate_cost = #{estimateCost,jdbcType=DECIMAL},
      </if>
      <if test="auditUser != null">
        audit_user = #{auditUser,jdbcType=VARCHAR},
      </if>
      <if test="isAbroad != null">
        is_abroad = #{isAbroad,jdbcType=INTEGER},
      </if>
      <if test="earliestLoadTime != null">
        earliest_load_time = #{earliestLoadTime,jdbcType=BIGINT},
      </if>
      <if test="latestLoadTime != null">
        latest_load_time = #{latestLoadTime,jdbcType=BIGINT},
      </if>
      <if test="marksQuantity != null">
        marks_quantity = #{marksQuantity,jdbcType=INTEGER},
      </if>
      <if test="marksAmount != null">
        marks_amount = #{marksAmount,jdbcType=DECIMAL},
      </if>
      <if test="trayQuantity != null">
        tray_quantity = #{trayQuantity,jdbcType=INTEGER},
      </if>
      <if test="trayAmount != null">
        tray_amount = #{trayAmount,jdbcType=DECIMAL},
      </if>
      <if test="tradePositionType != null">
        trade_position_type = #{tradePositionType,jdbcType=INTEGER},
      </if>
      <if test="tradeType != null">
        trade_type = #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="handoverName != null">
        handover_name = #{handoverName,jdbcType=VARCHAR},
      </if>
      <if test="originCountry != null">
        origin_country = #{originCountry,jdbcType=VARCHAR},
      </if>
      <if test="originCountryCode != null">
        origin_country_code = #{originCountryCode,jdbcType=VARCHAR},
      </if>
      <if test="destinationCountry != null">
        destination_country = #{destinationCountry,jdbcType=VARCHAR},
      </if>
      <if test="destinationCountryCode != null">
        destination_country_code = #{destinationCountryCode,jdbcType=VARCHAR},
      </if>
      <if test="port != null">
        port = #{port,jdbcType=VARCHAR},
      </if>
      <if test="portName != null">
        port_name = #{portName,jdbcType=VARCHAR},
      </if>
      <if test="stringStation != null">
        string_station = #{stringStation,jdbcType=VARCHAR},
      </if>
      <if test="stationName != null">
        station_name = #{stationName,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="consignName != null">
        consign_name = #{consignName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerName != null">
        manufacturer_name = #{manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="expectExeDate != null">
        expect_exe_date = #{expectExeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="transMode != null">
        trans_mode = #{transMode,jdbcType=VARCHAR},
      </if>
      <if test="itemCategory1Name != null">
        item_category1_name = #{itemCategory1Name,jdbcType=VARCHAR},
      </if>
      <if test="itemCategoryName != null">
        item_category_name = #{itemCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        item_code = #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        item_name = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemTransGroupName != null">
        item_trans_group_name = #{itemTransGroupName,jdbcType=VARCHAR},
      </if>
      <if test="itemPackSpecName != null">
        item_pack_spec_name = #{itemPackSpecName,jdbcType=VARCHAR},
      </if>
      <if test="qty != null">
        qty = #{qty,jdbcType=DECIMAL},
      </if>
      <if test="uomName != null">
        uom_name = #{uomName,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        sn = #{sn,jdbcType=VARCHAR},
      </if>
      <if test="outWarehouseName != null">
        out_warehouse_name = #{outWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="loadZoneName != null">
        load_zone_name = #{loadZoneName,jdbcType=VARCHAR},
      </if>
      <if test="loadZoneCode != null">
        load_zone_code = #{loadZoneCode,jdbcType=VARCHAR},
      </if>
      <if test="recvDistrictCode != null">
        recv_district_code = #{recvDistrictCode,jdbcType=VARCHAR},
      </if>
      <if test="recvDistrictName != null">
        recv_district_name = #{recvDistrictName,jdbcType=VARCHAR},
      </if>
      <if test="invalidatingStartDate != null">
        invalidating_start_date = #{invalidatingStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="remark1 != null">
        remark1 = #{remark1,jdbcType=VARCHAR},
      </if>
      <if test="remark2 != null">
        remark2 = #{remark2,jdbcType=VARCHAR},
      </if>
      <if test="remark3 != null">
        remark3 = #{remark3,jdbcType=VARCHAR},
      </if>
      <if test="remark4 != null">
        remark4 = #{remark4,jdbcType=VARCHAR},
      </if>
      <if test="remark5 != null">
        remark5 = #{remark5,jdbcType=VARCHAR},
      </if>
      <if test="remark6 != null">
        remark6 = #{remark6,jdbcType=VARCHAR},
      </if>
      <if test="remark7 != null">
        remark7 = #{remark7,jdbcType=VARCHAR},
      </if>
      <if test="remark8 != null">
        remark8 = #{remark8,jdbcType=VARCHAR},
      </if>
      <if test="remark9 != null">
        remark9 = #{remark9,jdbcType=VARCHAR},
      </if>
      <if test="remark10 != null">
        remark10 = #{remark10,jdbcType=VARCHAR},
      </if>
      <if test="ifHasDemandOrder != null">
        if_has_demand_order = #{ifHasDemandOrder,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TSinopecOriginalData">
    update t_sinopec_original_data
    set im_guid = #{imGuid,jdbcType=VARCHAR},
      send_time = #{sendTime,jdbcType=VARCHAR},
      sender = #{sender,jdbcType=VARCHAR},
      receiver = #{receiver,jdbcType=VARCHAR},
      receive_ifid = #{receiveIfid,jdbcType=VARCHAR},
      receive_method = #{receiveMethod,jdbcType=VARCHAR},
      send_operator = #{sendOperator,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=BIGINT},
      customer_name = #{customerName,jdbcType=VARCHAR},
      online_goods_flag = #{onlineGoodsFlag,jdbcType=INTEGER},
      consignor_code = #{consignorCode,jdbcType=VARCHAR},
      consignor_name = #{consignorName,jdbcType=VARCHAR},
      carrier_code = #{carrierCode,jdbcType=VARCHAR},
      carrier_name = #{carrierName,jdbcType=VARCHAR},
      associated_date = #{associatedDate,jdbcType=DATE},
      arrival_deadline = #{arrivalDeadline,jdbcType=DATE},
      short_split_count = #{shortSplitCount,jdbcType=INTEGER},
      transport_type = #{transportType,jdbcType=INTEGER},
      transport_type_remark = #{transportTypeRemark,jdbcType=VARCHAR},
      associated_count = #{associatedCount,jdbcType=DECIMAL},
      has_hazardous_chemicals = #{hasHazardousChemicals,jdbcType=INTEGER},
      associated_remark = #{associatedRemark,jdbcType=VARCHAR},
      plan_package_quantity = #{planPackageQuantity,jdbcType=INTEGER},
      estimate_cost = #{estimateCost,jdbcType=DECIMAL},
      audit_user = #{auditUser,jdbcType=VARCHAR},
      is_abroad = #{isAbroad,jdbcType=INTEGER},
      earliest_load_time = #{earliestLoadTime,jdbcType=BIGINT},
      latest_load_time = #{latestLoadTime,jdbcType=BIGINT},
      marks_quantity = #{marksQuantity,jdbcType=INTEGER},
      marks_amount = #{marksAmount,jdbcType=DECIMAL},
      tray_quantity = #{trayQuantity,jdbcType=INTEGER},
      tray_amount = #{trayAmount,jdbcType=DECIMAL},
      trade_position_type = #{tradePositionType,jdbcType=INTEGER},
      trade_type = #{tradeType,jdbcType=VARCHAR},
      handover_name = #{handoverName,jdbcType=VARCHAR},
      origin_country = #{originCountry,jdbcType=VARCHAR},
      origin_country_code = #{originCountryCode,jdbcType=VARCHAR},
      destination_country = #{destinationCountry,jdbcType=VARCHAR},
      destination_country_code = #{destinationCountryCode,jdbcType=VARCHAR},
      port = #{port,jdbcType=VARCHAR},
      port_name = #{portName,jdbcType=VARCHAR},
      string_station = #{stringStation,jdbcType=VARCHAR},
      station_name = #{stationName,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      consign_name = #{consignName,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      manufacturer_name = #{manufacturerName,jdbcType=VARCHAR},
      expect_exe_date = #{expectExeDate,jdbcType=TIMESTAMP},
      trans_mode = #{transMode,jdbcType=VARCHAR},
      item_category1_name = #{itemCategory1Name,jdbcType=VARCHAR},
      item_category_name = #{itemCategoryName,jdbcType=VARCHAR},
      item_code = #{itemCode,jdbcType=VARCHAR},
      item_name = #{itemName,jdbcType=VARCHAR},
      item_trans_group_name = #{itemTransGroupName,jdbcType=VARCHAR},
      item_pack_spec_name = #{itemPackSpecName,jdbcType=VARCHAR},
      qty = #{qty,jdbcType=DECIMAL},
      uom_name = #{uomName,jdbcType=VARCHAR},
      sn = #{sn,jdbcType=VARCHAR},
      out_warehouse_name = #{outWarehouseName,jdbcType=VARCHAR},
      load_zone_name = #{loadZoneName,jdbcType=VARCHAR},
      load_zone_code = #{loadZoneCode,jdbcType=VARCHAR},
      recv_district_code = #{recvDistrictCode,jdbcType=VARCHAR},
      recv_district_name = #{recvDistrictName,jdbcType=VARCHAR},
      invalidating_start_date = #{invalidatingStartDate,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      remark1 = #{remark1,jdbcType=VARCHAR},
      remark2 = #{remark2,jdbcType=VARCHAR},
      remark3 = #{remark3,jdbcType=VARCHAR},
      remark4 = #{remark4,jdbcType=VARCHAR},
      remark5 = #{remark5,jdbcType=VARCHAR},
      remark6 = #{remark6,jdbcType=VARCHAR},
      remark7 = #{remark7,jdbcType=VARCHAR},
      remark8 = #{remark8,jdbcType=VARCHAR},
      remark9 = #{remark9,jdbcType=VARCHAR},
      remark10 = #{remark10,jdbcType=VARCHAR},
      if_has_demand_order = #{ifHasDemandOrder,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>