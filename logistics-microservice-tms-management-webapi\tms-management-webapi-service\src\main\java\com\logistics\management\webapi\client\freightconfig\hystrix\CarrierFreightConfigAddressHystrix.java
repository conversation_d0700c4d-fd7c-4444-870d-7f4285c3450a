package com.logistics.management.webapi.client.freightconfig.hystrix;

import com.logistics.management.webapi.client.freightconfig.CarrierFreightConfigAddressClient;
import com.logistics.management.webapi.client.freightconfig.request.address.*;
import com.logistics.management.webapi.client.freightconfig.response.address.CarrierFreightConfigAddressDetailResponseModel;
import com.logistics.management.webapi.client.freightconfig.response.address.CarrierFreightConfigAddressListResponseModel;
import com.logistics.management.webapi.client.freightconfig.response.address.CarrierFreightConfigAddressLogsResponseModel;
import com.logistics.management.webapi.client.freightconfig.response.address.SearchCarrierFreightConfigResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CarrierFreightConfigAddressHystrix implements CarrierFreightConfigAddressClient {

    @Override
    public Result<CarrierFreightConfigAddressListResponseModel> searchList(CarrierFreightConfigAddressListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierFreightConfigAddressDetailResponseModel> detail(CarrierFreightAddressDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> add(CarrierFreightConfigAddressAddRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> edit(CarrierFreightAddressEditEnableRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> delete(CarrierFreightAddressDeleteRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> enable(CarrierFreightAddressEnableRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierFreightConfigAddressListResponseModel> export(CarrierFreightConfigAddressListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SearchCarrierFreightConfigResponseModel> getCarrierFreight(SearchCarrierFreightConfigRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<CarrierFreightConfigAddressLogsResponseModel>> getAddressRuleLogs(CarrierFreightAddressDetailRequestModel requestDto) {
        return Result.timeout();
    }
}
