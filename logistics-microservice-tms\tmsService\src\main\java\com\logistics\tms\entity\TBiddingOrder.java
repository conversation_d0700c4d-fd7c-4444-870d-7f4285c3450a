package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2024/04/29
*/
@Data
public class TBiddingOrder extends BaseEntity {
    /**
    * 竞价单号
    */
    @ApiModelProperty("竞价单号")
    private String biddingOrderCode;

    /**
    * 竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价
    */
    @ApiModelProperty("竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价")
    private Integer biddingStatus;

    /**
    * 是否取消：0 否，1 是
    */
    @ApiModelProperty("是否取消：0 否，1 是")
    private Integer ifCancel;

    /**
    * 车主范围：1 全部，2 定向选择
    */
    @ApiModelProperty("车主范围：1 全部，2 定向选择")
    private Integer companyCarrierRange;

    /**
    * 报价开始时间
    */
    @ApiModelProperty("报价开始时间")
    private Date quoteStartTime;

    /**
    * 报价时长：1 30分钟，2 60分钟，3 90分钟
    */
    @ApiModelProperty("报价时长：1 30分钟，2 60分钟，3 90分钟")
    private Integer quoteDuration;

    /**
    * 报价结束时间
    */
    @ApiModelProperty("报价结束时间")
    private Date quoteEndTime;

    /**
    * 期望提货时间
    */
    @ApiModelProperty("期望提货时间")
    private Date expectedLoadTime;

    /**
    * 期望卸货时间
    */
    @ApiModelProperty("期望卸货时间")
    private Date expectedUnloadTime;

    /**
    * 车长id
    */
    @ApiModelProperty("车长id")
    private Long vehicleLengthId;

    /**
    * 车长（米）
    */
    @ApiModelProperty("车长（米）")
    private BigDecimal vehicleLength;

    /**
    * 历史最低价
    */
    @ApiModelProperty("历史最低价")
    private BigDecimal lowestPrice;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}