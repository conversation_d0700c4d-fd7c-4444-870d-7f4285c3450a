package com.logistics.management.webapi.controller.freightconfig.request.shipping;

import lombok.Data;

import java.util.List;

@Data
public class AddShippingFreightRuleReqDto {


    /**
     * 运价规则id
     */
    private String shippingFreightId;


    /**
     * 收货省
     */
    private String toProvinceId;


    /**
     * 收货省name
     */
    private String toProvinceName;

    /**
     * 收货市id
     */
    private String toCityId;

    /**
     * 收货市name
     */
    private String toCityName;


    /**
     * 收货区id
     */
    private String toAreaId;


    /**
     * 收货区name
     */
    private String toAreaName;


    /**
     * 发货地集合
     */
    private List<AddShippingFreightRuleAddressReqDto> fromAddressItems;




}
