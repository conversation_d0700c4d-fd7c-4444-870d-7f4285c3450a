package com.logistics.management.webapi.client.invoicingmanagement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/7/2 9:10
 */
@Data
public class InvoicingArchiveRequestModel {

    /**
     * 发票管理id
     */
    @ApiModelProperty(value = "发票管理id")
    private Long invoicingId;

    /**
     * 归档文件url
     */
    @ApiModelProperty(value = "归档文件url")
    private List<String> tmpUrl;

    @ApiModelProperty("业务类型：1 包装业务，2 自营业务")
    private Integer businessType;

}
