package com.logistics.tms.controller.settlestatement.tradition;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.settlestatement.TraditionCarrierSettleStatementBiz;
import com.logistics.tms.controller.settlestatement.tradition.request.*;
import com.logistics.tms.controller.settlestatement.tradition.response.*;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 自营业务车主对账管理
 */
@RestController
@RequestMapping(value = "/service/carrierStatementManage/tradition")
public class CarrierTraditionStatementController {

	@Autowired
	private TraditionCarrierSettleStatementBiz traditionCarrierSettleStatementBiz;

	@ApiOperation(value = "待对账运单列表")
	@PostMapping(value = "/waitSettleStatementList")
	public Result<PageInfo<TraditionWaitSettleStatementListResponseModel>> waitSettleStatementList(@RequestBody TraditionWaitSettleStatementListRequestModel requestModel) {
		PageInfo<TraditionWaitSettleStatementListResponseModel> result = traditionCarrierSettleStatementBiz.waitSettleStatementList(requestModel);
		return Result.success(result);
	}

	@ApiOperation(value = "导出待对账运单列表")
	@PostMapping(value = "/exportWaitSettleStatementList")
	@IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.RESPONSE)
	public Result<PageInfo<TraditionWaitSettleStatementListResponseModel>> exportWaitSettleStatementList(@RequestBody TraditionWaitSettleStatementListRequestModel requestModel) {
		PageInfo<TraditionWaitSettleStatementListResponseModel> result = traditionCarrierSettleStatementBiz.waitSettleStatementList(requestModel);
		return Result.success(result);
	}

	@ApiOperation(value = "生成对账单")
	@PostMapping(value = "/createSettleStatement")
	public Result<Boolean> createSettleStatement(@RequestBody TraditionCreateSettleStatementRequestModel requestModel) {
		traditionCarrierSettleStatementBiz.createSettleStatement(requestModel);
		return Result.success(true);
	}

	@ApiOperation(value = "查询车主税点")
	@PostMapping(value = "/queryTaxPoint")
	public Result<TraditionCarrierTaxPointResponseModel> queryTaxPoint(@RequestBody TraditionCarrierTaxPointRequestModel requestModel) {
		return Result.success(traditionCarrierSettleStatementBiz.queryTaxPoint(requestModel));
	}

	/**
	 * 对账单功能
	 */
	@ApiOperation(value = "对账单列表")
	@PostMapping(value = "/settleStatementList")
	public Result<PageInfo<CarrierTraditionStatementListResponseModel>> settleStatementList(@RequestBody CarrierTraditionStatementListRequestModel requestModel) {
		return Result.success(traditionCarrierSettleStatementBiz.settleStatementList(requestModel));
	}

	@ApiOperation(value = "关联运单号")
	@PostMapping(value = "/associationCarrierOrder")
	public Result<TraditionAssociationCarrierOrderResponseModel> associationCarrierOrder(@RequestBody TraditionAssociationCarrierOrderRequestModel requestModel) {
		return Result.success(traditionCarrierSettleStatementBiz.associationCarrierOrder(requestModel));
	}

	@ApiOperation(value = "编辑对账月份")
	@PostMapping(value = "/modifySettleStatementMonth")
	public Result<Boolean> modifySettleStatementMonth(@RequestBody ModifyTraditionStatementMonthRequestModel requestModel) {
		traditionCarrierSettleStatementBiz.modifySettleStatementMonth(requestModel);
		return Result.success(true);
	}

	@ApiOperation(value = "编辑结算主体")
	@PostMapping(value = "/modifyPlatformCompany")
	public Result<Boolean> modifyPlatformCompany(@RequestBody TraditionModifyPlatformCompanyRequestModel requestModel) {
		traditionCarrierSettleStatementBiz.modifyPlatformCompany(requestModel);
		return Result.success(true);
	}

	@ApiOperation(value = "修改对账单费点")
	@PostMapping(value = "/modifyTaxPoint")
	public Result<Boolean> modifyTaxPoint(@RequestBody TraditionCarrierModifyTaxPointRequestModel requestModel) {
		traditionCarrierSettleStatementBiz.modifyTaxPoint(requestModel);
		return Result.success(true);
	}

	@ApiOperation(value = "修改对账单名称")
	@PostMapping(value = "/renameStatement")
	public Result<Boolean> renameStatement(@RequestBody EditTraditionStatementNameRequestModel requestModel) {
		traditionCarrierSettleStatementBiz.renameStatement(requestModel);
		return Result.success(true);
	}

	@ApiOperation(value = "差异调整-回显")
	@PostMapping(value = "/queryAdjustCost")
	public Result<TraditionCarrierAdjustCostResponseModel> queryAdjustCost(@RequestBody TraditionCarrierQueryAdjustCostRequestModel requestModel) {
		return Result.success(traditionCarrierSettleStatementBiz.queryAdjustCost(requestModel));
	}

	@ApiOperation(value = "差异调整-发起")
	@PostMapping(value = "/AdjustCost")
	public Result<Boolean> adjustCost(@RequestBody TraditionCarrierAdjustRequestModel requestModel) {
		traditionCarrierSettleStatementBiz.adjustCost(requestModel);
		return Result.success(true);
	}

	/**
	 * 申请开票
	 * @param requestModel
	 * @return
	 */
	@ApiOperation(value = "申请开票")
	@PostMapping(value = "/applyInvoicing")
	public Result<Boolean> applyInvoicing(@RequestBody TraditionSettleStatementApplyInvoicingRequestModel requestModel) {
		traditionCarrierSettleStatementBiz.applyInvoicing(requestModel);
		return Result.success(true);
	}

	@ApiOperation(value = "撤销对账单")
	@PostMapping(value = "/cancel")
	public Result<Boolean> cancel(@RequestBody TraditionStatementCancelRequestModel requestModel) {
		traditionCarrierSettleStatementBiz.cancel(requestModel);
		return Result.success(true);
	}

	/**
	 * 对账单下运单归档
	 */
	@ApiOperation(value = "对账单归档列表")
	@PostMapping(value = "/statementArchiveList")
	public Result<PageInfo<TraditionStatementArchiveListResponseModel>> statementArchiveList(@RequestBody TraditionStatementArchiveListRequestModel requestModel) {
		return Result.success(traditionCarrierSettleStatementBiz.statementArchiveList(requestModel));
	}

	@ApiOperation(value = "对账单归档/编辑")
	@PostMapping(value = "/statementArchive")
	public Result<Boolean> statementArchive(@RequestBody TraditionStatementArchiveRequestModel requestModel) {
		traditionCarrierSettleStatementBiz.statementArchive(requestModel);
		return Result.success(true);
	}

	@ApiOperation(value = "查看归档图片")
	@PostMapping(value = "/archiveTicketList")
	public Result<List<String>> archiveTicketList(@RequestBody TraditionStatementArchiveTicketListRequestModel requestModel) {
		return Result.success(traditionCarrierSettleStatementBiz.archiveTicketList(requestModel));
	}

	@ApiOperation(value = "查看归档详情")
	@PostMapping(value = "/statementArchiveDetail")
	public Result<TraditionStatementArchiveDetailResponseModel> statementArchiveDetail(@RequestBody TraditionStatementArchiveDetailRequestModel requestModel) {
		return Result.success(traditionCarrierSettleStatementBiz.statementArchiveDetail(requestModel));
	}

	@ApiOperation(value = "查询对账单下待归档运单")
	@PostMapping(value = "/statementWaitArchiveList")
	public Result<List<TraditionStatementWaitArchiveListResponseModel>> statementWaitArchiveList(@RequestBody TraditionStatementWaitArchiveListRequestModel requestModel) {
		return Result.success(traditionCarrierSettleStatementBiz.statementWaitArchiveList(requestModel));
	}

	/*对账单详情*/
	@ApiOperation(value = "对账单详情-提交")
	@PostMapping(value = "/applicationCheck")
	public Result<Boolean> submitSettleStatement(@RequestBody CarrierTraditionStatementIdRequestModel requestModel) {
		traditionCarrierSettleStatementBiz.submitSettleStatement(requestModel);
		return Result.success(Boolean.TRUE);
	}

	@ApiOperation(value = "对账单详情-审核/驳回")
	@PostMapping(value = "/auditOrReject")
	public Result<Boolean> auditOrReject(@RequestBody ChangeTraditionStatementStatsRequestModel requestModel) {
		traditionCarrierSettleStatementBiz.auditOrReject(requestModel);
		return Result.success(true);
	}

	@ApiOperation(value = "对账单详情-合计")
	@PostMapping(value = "/settleStatementDetailTotal")
	public Result<CarrierTraditionStatementDetailTotalResponseModel> settleStatementDetailTotal(@RequestBody CarrierTraditionStatementIdRequestModel requestModel) {
		return Result.success(traditionCarrierSettleStatementBiz.settleStatementDetailTotal(requestModel));
	}

	@ApiOperation(value = "对账单详情-对账单运单列表")
	@PostMapping(value = "/settleStatementDetailList")
	public Result<PageInfo<CarrierTraditionStatementDetailListResponseModel>> settleStatementDetailList(@RequestBody CarrierTraditionStatementDetailListRequestModel requestModel) {
		return Result.success(traditionCarrierSettleStatementBiz.settleStatementDetailList(requestModel, false));
	}

	@ApiOperation(value = "导出对账单详情-对账单运单列表")
	@PostMapping(value = "/exportSettleStatementDetailList")
	@IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.RESPONSE)
	public Result<PageInfo<CarrierTraditionStatementDetailListResponseModel>> exportSettleStatementDetailList(@RequestBody CarrierTraditionStatementDetailListRequestModel requestModel) {
		return Result.success(traditionCarrierSettleStatementBiz.settleStatementDetailList(requestModel, true));
	}

	@ApiOperation(value = "对账单详情-查询添加运单列表")
	@PostMapping(value = "/addCarrierOrderList")
	public Result<PageInfo<TraditionWaitSettleStatementListResponseModel>> addCarrierOrderList(@RequestBody TraditionAddCarrierOrderListRequestModel requestModel) {
		return Result.success(traditionCarrierSettleStatementBiz.addCarrierOrderList(requestModel));
	}

	@ApiOperation(value = "对账单详情-添加运单")
	@PostMapping(value = "/addCarrierOrder")
	public Result<Boolean> addCarrierOrderConfirm(@RequestBody TraditionAddCarrierOrderConfirmRequestModel requestModel) {
		traditionCarrierSettleStatementBiz.addCarrierOrderConfirm(requestModel);
		return Result.success(Boolean.TRUE);
	}

	@ApiOperation(value = "对账单详情-撤销运单")
	@PostMapping(value = "/cancelCarrierOrder")
	public Result<Boolean> cancelCarrierOrder(@RequestBody TraditionUndoCarrierOrderRequestModel requestModel) {
		traditionCarrierSettleStatementBiz.cancelCarrierOrder(requestModel);
		return Result.success(Boolean.TRUE);
	}
}
