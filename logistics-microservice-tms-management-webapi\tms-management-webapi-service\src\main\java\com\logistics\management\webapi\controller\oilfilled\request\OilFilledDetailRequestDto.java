package com.logistics.management.webapi.controller.oilfilled.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/10/9
 * @description:
 */
@Data
public class OilFilledDetailRequestDto {

    @ApiModelProperty(value = "充油ID",required = true)
    @NotBlank(message = "充油ID不能为空")
    private String oilFilledId;

}
