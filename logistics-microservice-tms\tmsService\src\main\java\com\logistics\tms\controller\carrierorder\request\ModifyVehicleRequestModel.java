package com.logistics.tms.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ModifyVehicleRequestModel {

    @ApiModelProperty("运单号")
    private Long carrierOrderId;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("挂车车辆ID")
    private Long trailerVehicleId;

    @ApiModelProperty("期望到达时间")
    private Date expectArrivalTime;

    @ApiModelProperty("司机姓名")
    private String driverName;

    @ApiModelProperty("司机手机号")
    private String driverMobile;

    @ApiModelProperty("司机身份证号码")
    private String driverIdentity;

    @ApiModelProperty("修改原因")
    private String remark;

    @ApiModelProperty("来源：1 后台 2 前台 3 app")
    private Integer source;
}
