package com.logistics.management.webapi.client.vehicleassetmanagement.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.vehicleassetmanagement.VehicleAssetManagementClient;
import com.logistics.management.webapi.client.vehicleassetmanagement.request.FuzzyQueryVehicleInfoRequestModel;
import com.logistics.management.webapi.client.vehicleassetmanagement.request.SearchTrailerVehicleRequestModel;
import com.logistics.management.webapi.client.vehicleassetmanagement.response.FuzzyQueryVehicleInfoResponseModel;
import com.logistics.management.webapi.client.vehicleassetmanagement.response.SearchTrailerVehicleResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class VehicleAssetManagementClientHystrix implements VehicleAssetManagementClient {

    @Override
    public Result<PageInfo<SearchTrailerVehicleResponseModel>> searchTrailerVehicle(SearchTrailerVehicleRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<FuzzyQueryVehicleInfoResponseModel>> fuzzyQueryVehicleInfo(FuzzyQueryVehicleInfoRequestModel requestModel) {
        return Result.timeout();
    }
}
