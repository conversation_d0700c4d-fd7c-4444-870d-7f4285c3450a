package com.logistics.tms.biz.email.model;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SinopecEntrustOrderModel {

    //订单号 "2606291578"
    private String sapOrderNo;

    //产品名称
    private String materialName;

    //委托吨位
    private BigDecimal qty;

    //货主名称 中国石化化工销售有限公司+"华东分公司" 委托方
    private String consignerName;

    //发货仓库 "扬子树脂驻厂办"
    private String outWarehouseName;

    //送达期限 1551024000000 期望收货时间
    private Long expectArriveDate;

    //收货方 "上海溢品佳化工有限公司",
    private String recvAddressName;

    //目的城市,
    private String recvZoneName ;

    //备注 含详细收获地址 收货人 收货人电话信息
    private String remark;

    //下达日期 "2019-02-27"  下单时间
    private String dispatchDate;
    //下达人
    private String operatorName;
    //委托日期
    private Long consignDate;
    //凭证日期 1563897600000
    private Long expectExeDateTime;

    //订单ID
    private Long id;

    //运费单价
    private BigDecimal transUnitPrice;

}
