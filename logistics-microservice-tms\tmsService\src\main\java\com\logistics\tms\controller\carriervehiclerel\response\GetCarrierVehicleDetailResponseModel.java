package com.logistics.tms.controller.carriervehiclerel.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/15
 */
@Data
public class GetCarrierVehicleDetailResponseModel {

	@ApiModelProperty("车主车辆关联Id")
	private Long carrierVehicleId;

	@ApiModelProperty("车牌号")
	private String vehicleNo;

	@ApiModelProperty("车辆类型")
	private Long vehicleType;

	@ApiModelProperty("装载量（可装载托盘数）")
	private Integer loadingCapacity;
}
