package com.logistics.management.webapi.api.feign.leave.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 请假申请详情响应dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/15
 */
@Data
public class LeaveDetailResponseDto {

	@ApiModelProperty("请假申请ID")
	private String leaveApplyId = "";

	@ApiModelProperty("申请人,姓名_手机号")
	private String leaveApplyStaff = "";

	@ApiModelProperty("请假类型")
	private String leaveType = "";

	@ApiModelProperty("请假类型Label")
	private String leaveTypeLabel = "";

	@ApiModelProperty("请假申请开始时间 (年-月-日 上午/下午)")
	private String leaveStartTime = "";

	@ApiModelProperty("请假申请结束时间 (年-月-日 上午/下午)")
	private String leaveEndTime = "";

	@ApiModelProperty("请假时长")
	private String leaveDuration = "";

	@ApiModelProperty("请假原由")
	private String leaveReason = "";

	@ApiModelProperty("请假申请审核状态,审核状态: 1 已审核，2 已驳回，3 已撤销")
	private String leaveAuditStatus = "";

	@ApiModelProperty("请假申请审核状态Label")
	private String leaveAuditStatusLabel = "";

	@ApiModelProperty("审核人")
	private String auditorName = "";

	@ApiModelProperty("审核时间")
	private String auditTime = "";

	@ApiModelProperty("审核备注/撤销备注")
	private String remark = "";
}
