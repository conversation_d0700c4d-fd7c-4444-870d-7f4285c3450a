package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RenewableDemandOrderCarrierResponseDto {

    @ApiModelProperty("运单id")
    private String carrierOrderId="";

    @ApiModelProperty("运单号")
    private String carrierOrderCode="";

    @ApiModelProperty("状态 运单状态 10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0取消 2放空")
    private String status="";
    private String statusLabel="";

    @ApiModelProperty("车牌号")
    private String vehicleNumber = "";

    @ApiModelProperty("司机")
    private String driverName = "";

    @ApiModelProperty("承运数量")
    private String expectAmount = "";

    @ApiModelProperty("运输价格")
    private String carrierPrice = "";

    @ApiModelProperty("调度人")
    private String dispatchUserName = "";

    @ApiModelProperty("调度时间")
    private String dispatchTime = "";
}
