package com.logistics.management.webapi.controller.thirdparty.user;

import com.logistics.management.webapi.client.thirdparty.basicdata.BasicServiceClient;
import com.logistics.management.webapi.client.thirdparty.basicdata.user.response.GetUserInfoListResponseModel;
import com.logistics.management.webapi.controller.thirdparty.user.request.SearchEnableUserListRequestDto;
import com.logistics.management.webapi.controller.thirdparty.user.response.SearchEnableUserListResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/22 14:59
 */
@RestController
@Api(value = "后台用户管理", tags = "后台用户管理")
@RequestMapping(value = "/api/user")
public class UserController {

    @Resource
    private BasicServiceClient basicServiceClient;

    @PostMapping(value = "/searchEnableUserList")
    @ApiOperation(value = "查询启用中的后台员工信息", tags = "3.14.0")
    public Result<List<SearchEnableUserListResponseDto>> searchEnableUserList(@RequestBody SearchEnableUserListRequestDto requestDto) {
        List<GetUserInfoListResponseModel> list = basicServiceClient.searchEnableUserList(requestDto.getUserName());
        return Result.success(MapperUtils.mapper(list, SearchEnableUserListResponseDto.class));
    }
}
