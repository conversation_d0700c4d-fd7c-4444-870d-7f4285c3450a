package com.logistics.tms.api.impl.insuarance;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.insuarance.InsuranceServiceApi;
import com.logistics.tms.api.feign.insuarance.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.insuarance.InsuranceBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/6/4 19:41
 */
@RestController
public class InsuranceServiceApiImpl implements InsuranceServiceApi {

    @Autowired
    private InsuranceBiz insuranceBiz;

    /**
     * 保险管理列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<SearchInsuranceListResponseModel>> searchInsuranceList(@RequestBody SearchInsuranceListRequestModel requestModel) {
        return Result.success(insuranceBiz.searchInsuranceList(requestModel));
    }

    /**
     * 保险详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<GetInsuranceDetailResponseModel> getInsuranceDetail(@RequestBody InsuranceIdRequestModel requestModel) {
        return Result.success(insuranceBiz.getInsuranceDetail(requestModel));
    }

    /**
     * 新增/修改保险
     * @param requestModel
     * @return
     */
    @Override
    public Result addOrModifyInsurance(@RequestBody AddOrModifyInsuranceRequestModel requestModel) {
        insuranceBiz.addOrModifyInsurance(requestModel);
        return Result.success(true);
    }

    /**
     * 作废保险
     * @param requestModel
     * @return
     */
    @Override
    public Result cancelInsurance(@RequestBody CancelInsuranceRequestModel requestModel) {
        insuranceBiz.cancelInsurance(requestModel);
        return Result.success(true);
    }

    /**
     * 导出保险
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchInsuranceListResponseModel>> exportInsurance(@RequestBody SearchInsuranceListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        requestModel.setIfExport(CommonConstant.INTEGER_ONE);
        PageInfo pageInfo = insuranceBiz.searchInsuranceList(requestModel);
        return Result.success(pageInfo.getList());
    }

    /**
     * 导入保险
     * @param requestModel
     * @return
     */
    @Override
    public Result<ImportInsuranceResponseModel> importInsurance(@RequestBody ImportInsuranceRequestModel requestModel) {
        return Result.success(insuranceBiz.importInsurance(requestModel));
    }

    /**
     * 导入保险证件信息
     * @param requestModel
     * @return
     */
    @Override
    public Result importInsuranceCertificateInfo(@RequestBody ImportInsuranceCertificateRequestModel requestModel) {
        insuranceBiz.importInsuranceCertificateInfo(requestModel);
        return Result.success(true);
    }

    /**
     * 根据车辆id查询保险信息（车辆退保页面）
     * @param requestModel
     * @return
     */
    @Override
    public Result<GetInsuranceInfoByVehicleIdResponseModel> getInsuranceInfoByVehicleId(@RequestBody GetInsuranceInfoByVehicleIdRequestModel requestModel) {
        return Result.success(insuranceBiz.getInsuranceInfoByVehicleId(requestModel));
    }

    /**
     * 确认退保
     * @param requestModel
     * @return
     */
    @Override
    public Result confirmRefund(@RequestBody ConfirmRefundRequestModel requestModel) {
        insuranceBiz.confirmRefund(requestModel);
        return Result.success(true);
    }
}
