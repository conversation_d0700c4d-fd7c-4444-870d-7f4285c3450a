package com.logistics.management.webapi.controller.dispatch.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * @author: wjf
 * @date: 2024/8/6 10:13
 */
@Data
public class SpecialDispatchVehicleListRequestDto {

    /**
     * 需求单货物id
     */
    @NotBlank(message = "需求单货物id不能为空")
    private String demandOrderGoodsId;

    /**
     * 需求单id
     */
    @NotBlank(message = "需求单id不能为空")
    private String demandOrderId;

    /**
     * 预提数量
     */
    @NotBlank(message = "预提件数不能为空")
    @Pattern(regexp = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,3})$)",message = "请维护正确的预提数量")
    private String loadAmount;

    /**
     * 排序
     */
    private String orderNum;

    /**
     * 到下个点位距离
     */
    private String nextPointDistance;

}
