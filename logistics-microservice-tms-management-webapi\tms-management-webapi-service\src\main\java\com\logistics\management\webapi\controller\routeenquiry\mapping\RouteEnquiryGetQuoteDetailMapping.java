package com.logistics.management.webapi.controller.routeenquiry.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.management.webapi.base.enums.PriceTypeEnum;
import com.logistics.management.webapi.client.routeenquiry.response.GetRouteEnquiryQuoteDetailResponseModel;
import com.logistics.management.webapi.controller.routeenquiry.response.GetRouteEnquiryQuoteDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.StringUtils;

/**
 * @author: wjf
 * @date: 2024/7/11 18:10
 */
public class RouteEnquiryGetQuoteDetailMapping extends MapperMapping<GetRouteEnquiryQuoteDetailResponseModel, GetRouteEnquiryQuoteDetailResponseDto> {
    @Override
    public void configure() {
        GetRouteEnquiryQuoteDetailResponseModel source = getSource();
        GetRouteEnquiryQuoteDetailResponseDto destination = getDestination();

        //拼接地址
        destination.setLoadAddress((StringUtils.isBlank(source.getFromWarehouse()) ? CommonConstant.BLANK_TEXT : ("【"+source.getFromWarehouse()+"】")) + source.getFromProvinceName() + source.getFromCityName() + source.getFromAreaName());
        destination.setUnloadAddress(source.getToProvinceName() + source.getToCityName() + source.getToAreaName());

        destination.setDistance(source.getDistance().stripTrailingZeros().toPlainString());
        destination.setQuotePriceTypeLabel(PriceTypeEnum.getEnum(source.getQuotePriceType()).getValue());
        if (PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getQuotePriceType())){
            destination.setQuotePrice(ConverterUtils.toString(source.getQuotePrice()) + GoodsUnitEnum.BY_WEIGHT.getPriceUnit());
        }else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getQuotePriceType())){
            destination.setQuotePrice(ConverterUtils.toString(source.getQuotePrice()) + CommonConstant.YUAN);
        }
    }
}
