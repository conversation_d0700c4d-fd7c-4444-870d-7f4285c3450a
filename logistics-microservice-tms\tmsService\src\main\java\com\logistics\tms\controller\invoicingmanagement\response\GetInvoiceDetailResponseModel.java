package com.logistics.tms.controller.invoicingmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2024/3/19 17:21
 */
@Data
public class GetInvoiceDetailResponseModel {

    @ApiModelProperty("发票id")
    private Long invoiceId;

    @ApiModelProperty("发票类型")
    private Integer invoiceType;

    @ApiModelProperty("发票类型文本")
    private String invoiceTypeLabel="";

    @ApiModelProperty("发票代码")
    private String invoiceCode;

    @ApiModelProperty("发票号码")
    private String invoiceNum;

    @ApiModelProperty("开票日期")
    private Date invoiceDate;

    @ApiModelProperty("发票金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    @ApiModelProperty("税额合计")
    private BigDecimal totalTaxAndPrice;

    @ApiModelProperty("发票图片")
    private String invoicePicture;

    @ApiModelProperty("发票图片")
    private String invoicePictureUrl;
}
