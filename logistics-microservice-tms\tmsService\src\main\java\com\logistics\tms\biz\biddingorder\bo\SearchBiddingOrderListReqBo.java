package com.logistics.tms.biz.biddingorder.bo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/04/28
 */
@Data
public class SearchBiddingOrderListReqBo {

    /**
     *竞价单ids 地址过滤竞价单
     */
    private List<Long> biddingOrderIdsByAddress;

    /**
     *竞价单ids 车长过滤竞价单
     */
    private List<Long> biddingOrderIdsByVehicleLength;

    /**
     * 竞价状态
     */
    private Integer biddingStatus;

    /**
     * 装卸方式 1一装一卸、2多装一卸
     */
    private Integer handlingMode;
}
