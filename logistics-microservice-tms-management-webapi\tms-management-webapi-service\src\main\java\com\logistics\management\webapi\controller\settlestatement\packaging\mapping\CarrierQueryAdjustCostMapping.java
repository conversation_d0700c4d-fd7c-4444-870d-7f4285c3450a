package com.logistics.management.webapi.controller.settlestatement.packaging.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.settlestatement.packaging.response.CarrierAdjustCostResponseModel;
import com.logistics.management.webapi.controller.settlestatement.packaging.response.CarrierAdjustCostResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2022/11/17 18:14
 */
public class CarrierQueryAdjustCostMapping extends MapperMapping<CarrierAdjustCostResponseModel, CarrierAdjustCostResponseDto> {
    @Override
    public void configure() {
        CarrierAdjustCostResponseModel source = getSource();
        CarrierAdjustCostResponseDto destination = getDestination();

        //车主费用
        BigDecimal carrierFreight = source.getCarrierFreight().setScale(2, BigDecimal.ROUND_HALF_UP);
        destination.setCarrierFreight(ConverterUtils.toString(carrierFreight));

        //费额合计
        BigDecimal carrierFreightTotal = carrierFreight.add(carrierFreight.multiply(source.getFreightTaxPoint()).divide(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_NET_ZERO, 2, BigDecimal.ROUND_HALF_UP)).setScale(2, BigDecimal.ROUND_HALF_UP);
        destination.setCarrierFreightTotal(ConverterUtils.toString(carrierFreightTotal));

        //临时费用
        BigDecimal otherFees = source.getOtherFee().setScale(2, BigDecimal.ROUND_HALF_UP);
        if (otherFees.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO){
            destination.setOtherFee(CommonConstant.PLUS_SYMBOL + ConverterUtils.toString(otherFees));
        }else {
            destination.setOtherFee(ConverterUtils.toString(otherFees));
        }

        //临时费用合计
        BigDecimal otherFeesTotal;
        if (otherFees.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO && source.getOtherFeeTaxPoint().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO){
            otherFeesTotal = otherFees.add(otherFees.multiply(source.getOtherFeeTaxPoint()).divide(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_NET_ZERO, 2, BigDecimal.ROUND_HALF_UP)).setScale(2, BigDecimal.ROUND_HALF_UP);
        }else{
            otherFeesTotal = otherFees;
        }
        destination.setOtherFeeTotal(ConverterUtils.toString(otherFeesTotal));

        //申请费用总额
        BigDecimal applyFeeTotal = carrierFreightTotal.add(otherFeesTotal).setScale(2, BigDecimal.ROUND_HALF_UP);
        destination.setApplyFeeTotal(ConverterUtils.toString(applyFeeTotal));

        //对账费用
        destination.setReconciliationFee(ConverterUtils.toString(applyFeeTotal.add(source.getAdjustCost()).setScale(2, BigDecimal.ROUND_HALF_UP)));

        //调整费用
        if (source.getAdjustCost().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO){
            destination.setAdjustCostSymbol(CommonConstant.ONE);
        }else if (source.getAdjustCost().compareTo(BigDecimal.ZERO) < CommonConstant.INTEGER_ZERO){
            destination.setAdjustCostSymbol(CommonConstant.TWO);
            destination.setAdjustCost(ConverterUtils.toString(source.getAdjustCost().negate()));
        }else {
            destination.setAdjustCostSymbol(CommonConstant.BLANK_TEXT);
        }
    }
}
