package com.logistics.management.webapi.client.freightconfig;

import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.freightconfig.hystrix.CarrierFreightConfigRegionHystrix;
import com.logistics.management.webapi.client.freightconfig.request.region.CarrierFreightConfigRegionAddRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.region.CarrierFreightConfigRegionEditRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.region.CarrierFreightConfigRegionRequestModel;
import com.logistics.management.webapi.client.freightconfig.response.region.CarrierFreightConfigRegionResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/freight/config/region",
        fallback = CarrierFreightConfigRegionHystrix.class)
public interface CarrierFreightConfigRegionClient {

    @PostMapping(value = "/detail")
    @ApiOperation(value = "区域计价配置查看", tags = "1.3.5")
    Result<List<CarrierFreightConfigRegionResponseModel>> detail(@RequestBody CarrierFreightConfigRegionRequestModel requestModel);

    @PostMapping(value = "/add")
    @ApiOperation(value = "区域计价配置新增", tags = "1.3.5")
    Result<Boolean> add(@RequestBody CarrierFreightConfigRegionAddRequestModel requestModel);

    @PostMapping(value = "/edit")
    @ApiOperation(value = "区域计价配置编辑", tags = "1.3.5")
    Result<Boolean> edit(@RequestBody CarrierFreightConfigRegionEditRequestModel requestModel);
}
