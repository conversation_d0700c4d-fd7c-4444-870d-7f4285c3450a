package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TRegionItem extends BaseEntity {
    /**
    * 大区id
    */
    @ApiModelProperty("大区id")
    private Long regionId;

    /**
    * 省份id
    */
    @ApiModelProperty("省份id")
    private Long provinceId;

    /**
    * 省份名称
    */
    @ApiModelProperty("省份名称")
    private String provinceName;

    /**
    * 城市id
    */
    @ApiModelProperty("城市id")
    private Long cityId;

    /**
    * 城市名称
    */
    @ApiModelProperty("城市名称")
    private String cityName;
}