package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/19
 */
@Data
public class CarrierOrderDetailForYeloLifeResponseModel {

	@ApiModelProperty("运单ID")
	private Long carrierOrderId;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("客户单号")
	private String customerOrderCode;

	@ApiModelProperty("运单状态：10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0 已取消 2已放空")
	private Integer status;

	@ApiModelProperty("客户")
	private String customerName;

	@ApiModelProperty("个人客户姓名")
	private String customerUserName;

	@ApiModelProperty("个人客户手机号")
	private String customerUserMobile;

	@ApiModelProperty("需求单ID")
	private Long demandOrderId;

	@ApiModelProperty("需求单号")
	private String demandOrderCode;

	@ApiModelProperty("调度人")
	private String dispatchUserName;

	@ApiModelProperty("运单生成时间")
	private Date dispatchTime;

	@ApiModelProperty("运单基本信息")
	private CarrierOrderDetailBasicInfoModel carrierOrderDetailBasicInfo;

	@ApiModelProperty("运单运费信息")
	private CarrierOrderDetailFreightFeeInfoModel carrierOrderDetailFreightFeeInfo;

	@ApiModelProperty("运单货物信息")
	private List<CarrierOrderDetailGoodsForYeloLifeModel> carrierOrderDetailGoodsInfo;

	@ApiModelProperty("运单车辆司机信息")
	private List<CarrierOrderDetailVehicleDriverInfoModel> carrierOrderDetailVehicleDriverInfo;

	@ApiModelProperty("备注")
	private String remark;

	@ApiModelProperty("实际提货件数")
	private BigDecimal loadAmount;

	@ApiModelProperty("实际卸货件数")
	private BigDecimal unloadAmount;

	@ApiModelProperty("签收件数")
	private BigDecimal signAmount;

	@ApiModelProperty("货主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
	private Integer entrustSettlementTonnage;

	@ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
	private Integer carrierSettlementTonnage;

	@ApiModelProperty("预计承运件数")
	private BigDecimal expectAmount;

	@ApiModelProperty("单位")
	private Integer goodsUnit;

	@ApiModelProperty("是否取消 ")
	private Integer ifCancel;

	@ApiModelProperty("业务类型")
	private Integer businessType;

	@ApiModelProperty("发布人姓名")
	private String publishName;

	@ApiModelProperty("发布人手机号")
	private String publishMobile;

	@ApiModelProperty("车主id")
	private Long companyCarrierId;

	@ApiModelProperty("车主")
	private String companyCarrierName;

	@ApiModelProperty("车主公司类型")
	private Integer companyCarrierType;

	@ApiModelProperty("车主联系人")
	private String carrierContactName;

	@ApiModelProperty("车主联系人电话")
	private String carrierContactPhone;

	@ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
	private Integer isOurCompany;

	@ApiModelProperty("车主对账单状态, -2:未关联对账")
	private Integer carrierSettleStatementStatus;
}
