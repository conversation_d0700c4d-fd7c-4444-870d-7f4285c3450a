package com.logistics.management.webapi.controller.companycarrier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/9/27 17:40
 */
@Data
public class CompanyCarrierDetailResponseDto {

	@ApiModelProperty("公司id")
	private String companyCarrierId = "";

	@ApiModelProperty("公司水印")
	private String companyWaterMark = "";

	@ApiModelProperty("车主类型：1 企业 2 个人")
	private String type = "";

	@ApiModelProperty("车主类型文本")
	private String typeLabel = "";

	@ApiModelProperty("道路许可证号")
	private String roadTransportCertificateNumber = "";

	@ApiModelProperty("道路许可证号图片相对路径")
	private String fileRoadTransportCertificateImage = "";

	@ApiModelProperty("道路许可证号图片绝对路径")
	private String fileTargetPathRoadTransportCertificateImage = "";

	@ApiModelProperty("道路许可证号有效期")
	private String roadTransportCertificateValidityTime = "";

	@ApiModelProperty("道路许可证号永久 0 否 1 是")
	private String roadTransportCertificateIsForever = "";

	@ApiModelProperty("道路许可证号后补 0 否 1 是")
	private String roadTransportCertificateIsAmend = "";

	@ApiModelProperty("备注")
	private String remark = "";

	@ApiModelProperty("公司名字")
	private String companyCarrierName = "";

	@ApiModelProperty("营业执照图片相对路径")
	private String fileTradingCertificateImage = "";

	@ApiModelProperty("营业执照图片绝对路径")
	private String fileTargetPathTradingCertificateImage = "";

	@ApiModelProperty("营业执照有效期")
	private String tradingCertificateValidityTime = "";

	@ApiModelProperty("营业执照是否永久 0 否 1 是")
	private String tradingCertificateIsForever = "";

	@ApiModelProperty("营业执照是否后补 0 否 1 是")
	private String tradingCertificateIsAmend = "";

	@ApiModelProperty("车主账号id")
	private String carrierContactId = "";

	@ApiModelProperty("姓名")
	private String contactName = "";

	@ApiModelProperty("手机号")
	private String contactPhone = "";

	@ApiModelProperty("相对身份证人面像")
	private String identityFaceFile = "";

	@ApiModelProperty("绝对身份证人面像")
	private String fileAboIdentityFaceFile = "";

	@ApiModelProperty("身份证人面像图片是否后补 0否1是")
	private String identityFaceFileIsAmend = "";

	@ApiModelProperty("相对身份证国徽像")
	private String identityNationalFile = "";

	@ApiModelProperty("绝对相对身份证国徽像")
	private String fileAboIdentityNationalFile = "";

	@ApiModelProperty("身份证国徽图片是否后补 0否1是")
	private String identityNationalFileIsAmend = "";

	@ApiModelProperty("身份证有效期")
	private String identityValidity = "";

	@ApiModelProperty("身份证是否永久: 0 否 1 是")
	private String identityIsForever = "";

	@ApiModelProperty("身份证号码")
	private String identityNumber = "";

	@ApiModelProperty("省ID")
	private String provinceId = "";

	@ApiModelProperty("省名字")
	private String provinceName = "";

	@ApiModelProperty("城市ID")
	private String cityId = "";

	@ApiModelProperty("城市名字")
	private String cityName = "";

	@ApiModelProperty("区ID")
	private String areaId = "";

	@ApiModelProperty("区名字")
	private String areaName = "";

	@ApiModelProperty("发证机关详情")
	private String certificationDepartmentDetail = "";

	@ApiModelProperty("临时费用提交：0 否，1 是")
	private String commitOtherFee = "";

	@ApiModelProperty("临时费用费点")
	private String otherFeeTaxPoint = "";

	@ApiModelProperty("运费费点")
	private String freightTaxPoint = "";
}
