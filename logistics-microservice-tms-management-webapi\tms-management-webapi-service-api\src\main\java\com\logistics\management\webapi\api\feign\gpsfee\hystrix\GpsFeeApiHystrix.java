package com.logistics.management.webapi.api.feign.gpsfee.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.gpsfee.GpsFeeApi;
import com.logistics.management.webapi.api.feign.gpsfee.dto.*;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/8 14:29
 */
@Component
public class GpsFeeApiHystrix implements GpsFeeApi {
    @Override
    public Result<PageInfo<SearchGpsFeeListResponseDto>> searchGpsFeeList(SearchGpsFeeListRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<SearchGpsFeeListCountResponseDto> searchGpsFeeListCount(SearchGpsFeeListRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<GpsFeeDetailResponseDto> getGpsFeeDetail(GpsFeeIdRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result addOrModifyGpsFee(AddOrModifyGpsFeeRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void exportGpsFee(SearchGpsFeeListRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }

    @Override
    public Result terminationGpsFee(TerminationGpsFeeRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<List<GpsFeeRecordsListResponseDto>> getGpsFeeRecords(GpsFeeIdRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void exportGpsFeeRecords(GpsFeeIdRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }

    @Override
    public Result<List<GetDeductingHistoryResponseDto>> getGpsFeeDeductingHistory(GpsFeeIdRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void exportGpsFeeDeductingHistory(GpsFeeIdRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }
}
