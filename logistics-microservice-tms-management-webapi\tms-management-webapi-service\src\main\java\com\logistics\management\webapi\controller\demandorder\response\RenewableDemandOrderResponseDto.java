package com.logistics.management.webapi.controller.demandorder.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class RenewableDemandOrderResponseDto {

    @ApiModelProperty("需求单ID")
    private String demandId= "";

    @ApiModelProperty("需求单状态：500待发布 1000待调度 2000部分调度 3000调度完成 4000待签收 5000已签收 1取消")
    private String status= "";
    @ExcelProperty(value = "状态")
    private String statusDesc = "";

    @ApiModelProperty("需求单号")
    @ExcelProperty(value = "需求单号")
    private String demandOrderCode= "";

    @ApiModelProperty("客户单号")
    @ExcelProperty(value = "客户单号")
    private String customerOrderCode= "";

    @ApiModelProperty(value = "客户")
    private String customerName = "";
    @ApiModelProperty(value = "客户(导出使用)")
    @ExcelProperty(value = "客户")
    private String exportCustomerName = "";

    @ApiModelProperty("车主公司ID")
    private String companyCarrierId= "";
    @ExcelProperty("车主")
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName= "";

    @ApiModelProperty("发货详细地址")
    @ExcelProperty(value = "发货地址")
    private String loadDetailAddress= "";

    @ApiModelProperty("收货仓库")
    @ExcelProperty(value = "收货仓库")
    private String unloadWarehouse= "";

    @ApiModelProperty("收货地址详细")
    @ExcelProperty(value = "收货地址")
    private String unloadDetailAddress= "";

    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注")
    private String remark="";

    @ApiModelProperty("品名")
    @ExcelProperty(value = "品名")
    private String goodsName= "";

    @ExcelProperty("单位")
    @ApiModelProperty("货物单位 1 件 2 吨 3 方 4 块")
    private String goodsUnit= "";

    @ApiModelProperty("委托件数")
    @ExcelProperty("委托")
    private String goodsAmount= "";

    @ApiModelProperty("已安排数量")
    @ExcelProperty(value = "已安排")
    private String arrangedAmount= "";

    @ApiModelProperty("已退回数量")
    @ExcelProperty(value = "已退回")
    private String backAmount= "";

    @ApiModelProperty("未安排数量")
    @ExcelProperty(value = "未安排")
    private String notArrangedAmount= "";

    @ApiModelProperty("客户订单来源：1 乐橘新生客户，2 司机")
    @ExcelProperty(value = "来源")
    private String customerOrderSource;



    /**
     * 需求类型 100 新生回收，101新生销售    3.23.0
     */
    private String entrustType = "";
    /**
     * 需求类型描述    3.23.0
     */
    private String entrustTypeDesc = "";

}
