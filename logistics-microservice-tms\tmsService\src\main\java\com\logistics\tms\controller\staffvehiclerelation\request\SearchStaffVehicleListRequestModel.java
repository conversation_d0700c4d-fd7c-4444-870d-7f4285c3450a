package com.logistics.tms.controller.staffvehiclerelation.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/7/26 13:18
 */
@Data
public class SearchStaffVehicleListRequestModel extends AbstractPageForm<SearchStaffVehicleListRequestModel> {

    @ApiModelProperty("类型 1 自主，2 外部，3 自营")
    private Integer type;

    @ApiModelProperty("车辆类别: 1 牵引车 2 挂车 3 一体车")
    private Integer vehicleCategory;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("司机")
    private String staffName;

    @ApiModelProperty("牵引车或一体车ID拼接,例“123,456,789”")
    private String vehicleIds;

    @ApiModelProperty("批量导出ID入参,例“123,456,789”")
    private String staffVehicleIds;

    @ApiModelProperty("车主")
    private String companyCarrierName;

    @ApiModelProperty("我司数据,1:勾选")
    private Integer isOurCompany;

    @ApiModelProperty("内部字段,用于筛选")
    private String carrierVehicleIds;

    @ApiModelProperty("内部字段,用于筛选")
    private String companyCarrierIds;

    @ApiModelProperty("来源 1后台 2 前台")
    private Integer source;
}
