package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author:lei.zhu
 * @date:2021/12/29 13:21:16
 */
@Data
public class WithdrawDemandRelModel {
    @ApiModelProperty("销售单ID")
    private Long orderId;
    @ApiModelProperty("销售单号")
    private String orderCode;
    @ApiModelProperty("数量")
    private BigDecimal totalAmount;
    @ApiModelProperty("下单类型")
    private Integer relType;
    @ApiModelProperty("下单备注")
    private String remark;
    @ApiModelProperty("下单人")
    private String createdBy;
    @ApiModelProperty("下单时间")
    private Date createdTime;
}
