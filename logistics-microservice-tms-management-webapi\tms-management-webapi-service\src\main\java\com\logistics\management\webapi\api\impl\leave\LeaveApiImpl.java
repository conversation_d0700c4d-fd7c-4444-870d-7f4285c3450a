package com.logistics.management.webapi.api.impl.leave;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.leave.LeaveApi;
import com.logistics.management.webapi.api.feign.leave.dto.*;
import com.logistics.management.webapi.api.impl.leave.mapping.LeaveApplyDetailMapping;
import com.logistics.management.webapi.api.impl.leave.mapping.SearchLeaveListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.LeaveApplyAuditStatusEnum;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.ExportUtils;
import com.logistics.tms.api.feign.leave.LeaveServiceApi;
import com.logistics.tms.api.feign.leave.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 请假管理
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/15
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class LeaveApiImpl implements LeaveApi {

	private final LeaveServiceApi leaveServiceApi;

	/**
	 * 查询请假申请列表
	 * @param requestDto
	 * @return PageInfo<SearchLeaveListResponseDto>
	 */
	@Override
	public Result<PageInfo<SearchLeaveListResponseDto>> searchLeaveApplyList(@RequestBody SearchLeaveListRequestDto requestDto) {
		LeaveApplySearchListRequestModel requestModel = MapperUtils.mapper(requestDto, LeaveApplySearchListRequestModel.class);
		Result<PageInfo<LeaveApplySearchListResponseModel>> pageInfoResult = leaveServiceApi.searchLeaveApplyList(requestModel);
		pageInfoResult.throwException();

		PageInfo pageInfo = pageInfoResult.getData();
		List<LeaveApplySearchListResponseModel> leaveApplySearchModelList = pageInfo.getList();
		if (ListUtils.isNotEmpty(leaveApplySearchModelList)) {
			List<SearchLeaveListResponseDto> responseDtoList = MapperUtils.mapper(leaveApplySearchModelList, SearchLeaveListResponseDto.class,
					new SearchLeaveListMapping());
			pageInfo.setList(responseDtoList);
		}
		return Result.success(pageInfo);
	}

	/**
	 * 导出请假申请列表
	 * @param requestDto
	 * @param response
	 */
	@Override
	@IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
	public void exportLeaveApplyList(SearchLeaveListRequestDto requestDto, HttpServletResponse response) {
		LeaveApplySearchListRequestModel requestModel = MapperUtils.mapper(requestDto, LeaveApplySearchListRequestModel.class);
		Result<List<LeaveApplySearchListResponseModel>> responseResult = leaveServiceApi.exportLeaveApplyList(requestModel);
		responseResult.throwException();

		String fileName = "司机请假管理".concat(DateUtils.dateToString(new Date(), DateUtils.FORMAT_LONG));
		List<SearchLeaveListResponseDto> responseDtoList = MapperUtils.mapper(responseResult.getData(), SearchLeaveListResponseDto.class,
				new SearchLeaveListMapping());
		ExportUtils.exportByYeloExcel(response, responseDtoList, SearchLeaveListResponseDto.class, fileName);
	}

	/**
	 * 请假详情
	 * @param requestDto
	 * @return LeaveDetailResponseDto
	 */
	@Override
	public Result<LeaveDetailResponseDto> leaveApplyDetail(@RequestBody @Valid LeaveDetailRequestDto requestDto) {
		LeaveApplyDetailRequestModel requestModel = MapperUtils.mapper(requestDto, LeaveApplyDetailRequestModel.class);
		Result<LeaveApplyDetailResponseModel> responseModelResult = leaveServiceApi.leaveApplyDetail(requestModel);
		responseModelResult.throwException();

		LeaveDetailResponseDto responseDto = MapperUtils.mapper(responseModelResult.getData(), LeaveDetailResponseDto.class,
				new LeaveApplyDetailMapping());
		return Result.success(responseDto);
	}

	/**
	 * 请假申请审核
	 * @param requestDto
	 * @return boolean
	 */
	@Override
	@IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
	public Result<Boolean> auditLeaveApply(@RequestBody @Valid AuditLeaveApplyRequestDto requestDto) {
		Integer auditType = Integer.valueOf(requestDto.getAuditType());
		if (LeaveApplyAuditStatusEnum.AUDIT_REJECT.getKey().equals(auditType)) {
			if (StringUtils.isBlank(requestDto.getRemark()) || requestDto.getRemark().length() > CommonConstant.INTEGER_ONE_HUNDRED) {
				throw new BizException(ManagementWebApiExceptionEnum.REMARKS_VERIFICATION_MESSAGE);
			}
		}
		LeaveApplyAuditRequestModel requestModel = MapperUtils.mapper(requestDto, LeaveApplyAuditRequestModel.class);
		return leaveServiceApi.auditLeaveApply(requestModel);
	}

	/**
	 * 撤销请假申请
	 * @param requestDto
	 * @return boolean
	 */
	@Override
	@IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
	public Result<Boolean> cancelLeaveApply(@RequestBody @Valid CancelLeaveApplyRequestDto requestDto) {
		LeaveApplyCancelRequestModel requestModel = MapperUtils.mapper(requestDto, LeaveApplyCancelRequestModel.class);
		return leaveServiceApi.cancelLeaveApply(requestModel);
	}
}
