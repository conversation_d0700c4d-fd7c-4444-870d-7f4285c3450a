package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/09/02
*/
@Data
public class TCarrierOrderOtherFee extends BaseEntity {
    /**
    * 运单主表ID
    */
    @ApiModelProperty("运单主表ID")
    private Long carrierOrderId;

    /**
    * 运单号
    */
    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    /**
    * 状态：1 待提交，2 待审核，3 已驳回，4 已撤销，5 已审核
    */
    @ApiModelProperty("状态：1 待提交，2 待审核，3 已驳回，4 已撤销，5 已审核")
    private Integer auditStatus;

    /**
    * 审核人
    */
    @ApiModelProperty("审核人")
    private String auditorName;

    /**
    * 审核时间
    */
    @ApiModelProperty("审核时间")
    private Date auditTime;

    /**
    * 费用总计
    */
    @ApiModelProperty("费用总计")
    private BigDecimal totalAmount;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}