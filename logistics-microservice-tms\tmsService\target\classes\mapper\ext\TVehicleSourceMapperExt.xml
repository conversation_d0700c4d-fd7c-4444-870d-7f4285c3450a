<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleSourceMapper" >
    <select id="getByVehicleNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_vehicle_source
        where valid = 1
        and vehicle_no = #{vehicleNo,jdbcType=VARCHAR}
    </select>
    <select id="vehicleSourceList" resultType="com.logistics.tms.controller.website.vehicle.response.VehicleSourceListResponseModel">
        select
        id as vehicleSourceId,
        vehicle_no as vehicleNo,
        approved_load_weight as approvedLoadWeight,
        type as type,
        contact_name as contactName,
        contact_mobile as contactMobile
        from t_vehicle_source
        where valid = 1
        order by id desc
    </select>
</mapper>