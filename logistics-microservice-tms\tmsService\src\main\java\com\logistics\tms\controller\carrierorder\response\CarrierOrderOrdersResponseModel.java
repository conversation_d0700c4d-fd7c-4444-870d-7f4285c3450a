package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wei.wang
 * @date: 2021/12/24
 */
@Data

public class CarrierOrderOrdersResponseModel {

	@ApiModelProperty("客户单号")
	private String orderCode;
	@ApiModelProperty("数量")
	private BigDecimal totalAmount;
	@ApiModelProperty("类型")
	private Integer relType;
	@ApiModelProperty("备注")
	private String remark;
	@ApiModelProperty("下单人")
	private String createdBy;
	@ApiModelProperty("同步时间")
	private Date createdTime;
}
