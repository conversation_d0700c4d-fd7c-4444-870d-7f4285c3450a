package com.logistics.tms.controller.companycarrier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * liang current user system login name
 * 2018/11/7 current system date
 */
@Data
public class CompanyCarrierSearchByNameResponseModel {

    @ApiModelProperty("车主公司表id")
    private Long companyCarrierId;
    @ApiModelProperty("公司表id")
    private Long companyId;
    @ApiModelProperty("车主类型 1公司 2 个人")
    private Integer companyType;
    @ApiModelProperty("公司名称")
    private String companyName;
    @ApiModelProperty("车主账号联系人id")
    private Long carrierContactId;
    @ApiModelProperty("车主联系人")
    private String contactName;
    @ApiModelProperty("车主联系人电话")
    private String contactPhone;

}
