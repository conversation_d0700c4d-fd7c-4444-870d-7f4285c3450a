package com.logistics.management.webapi.controller.demandorder;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.DemandOrderCancelTypeEnum;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.enums.OrderModeEnum;
import com.logistics.management.webapi.base.enums.QuoteDurationEnum;
import com.logistics.management.webapi.client.demandorder.DemandOrderForLeYiClient;
import com.logistics.management.webapi.client.demandorder.request.*;
import com.logistics.management.webapi.client.demandorder.response.*;
import com.logistics.management.webapi.controller.demandorder.mapping.*;
import com.logistics.management.webapi.controller.demandorder.request.*;
import com.logistics.management.webapi.controller.demandorder.response.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tools.utils.YeloExcelUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 云盘需求单管理
 * @author: wjf
 * @date: 2024/1/9 9:38
 */
@Api(value = "云盘需求单管理", tags = "云盘需求单管理")
@RestController
public class DemandOrderForLeYiController {

    @Resource
    private DemandOrderForLeYiClient demandOrderForLeYiClient;

    @ApiOperation(value = "获取云盘需求单列表 v2.6.8", tags = "v2.6.8")
    @PostMapping(value = "/api/demandOrder/searchListForLeYi")
    public Result<PageInfo<DemandOrderForLeYiResponseDto>> searchListForLeYi(@RequestBody DemandOrderSearchForLeYiRequestDto requestDto) {
        //判断入参
        if (ListUtils.isNotEmpty(requestDto.getCustomerOrderCodeList()) && requestDto.getCustomerOrderCodeList().size() > CommonConstant.INTEGER_ONE_THOUSAND_HUNDRED) {
            throw new BizException(ManagementWebApiExceptionEnum.ORDER_BATCH_SEARCH_MAX_ONE_THOUSAND);
        }
        if (ListUtils.isNotEmpty(requestDto.getDemandOrderCodeList()) && requestDto.getDemandOrderCodeList().size() > CommonConstant.INTEGER_ONE_THOUSAND_HUNDRED) {
            throw new BizException(ManagementWebApiExceptionEnum.ORDER_BATCH_SEARCH_MAX_ONE_THOUSAND);
        }

        DemandOrderSearchForLeYiRequestModel requestModel = MapperUtils.mapper(requestDto, DemandOrderSearchForLeYiRequestModel.class);
        requestModel.setRequestSource(CommonConstant.ONE);
        Result<PageInfo<DemandOrderForLeYiResponseModel>> pageInfoResult = demandOrderForLeYiClient.searchListForLeYi(requestModel);
        pageInfoResult.throwException();
        PageInfo pageInfo = pageInfoResult.getData();
        if (pageInfoResult.getData() != null && ListUtils.isNotEmpty(pageInfoResult.getData().getList())) {
            List<DemandOrderForLeYiResponseDto> demandOrderListResponseDtos = MapperUtils.mapper(pageInfoResult.getData().getList(), DemandOrderForLeYiResponseDto.class, new ListDemandOrderForLeYiMapping());
            pageInfo.setList(demandOrderListResponseDtos);
        }
        return Result.success(pageInfo);
    }

    @ApiOperation(value = "导出需求单", tags = "3.15.0")
    @PostMapping(value = "/api/demandOrder/exportDemandOrderForLeYi")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportDemandOrderForLeYi(@RequestBody DemandOrderSearchForLeYiRequestDto requestDto, HttpServletResponse response) {
        DemandOrderSearchForLeYiRequestModel requestModel = MapperUtils.mapper(requestDto, DemandOrderSearchForLeYiRequestModel.class);
        requestModel.setRequestSource(CommonConstant.ONE);
        Result<PageInfo<DemandOrderForLeYiResponseModel>> listResult = demandOrderForLeYiClient.exportDemandOrderForLeYi(requestModel);
        listResult.throwException();
        List<DemandOrderForLeYiResponseDto> list = MapperUtils.mapper(listResult.getData().getList(),DemandOrderForLeYiResponseDto.class, new ListDemandOrderForLeYiMapping());
        String fileName = "需求单列表" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM);
        try {
            YeloExcelUtils.doExportMoreThreadByOneSheet(response, list, false, DemandOrderForLeYiResponseDto.class, fileName);
        } catch (Exception e) {
            return;
        }
    }

    @ApiOperation(value = "云盘需求单回退", tags = "3.15.0")
    @PostMapping(value = "/api/demandOrder/rollbackDemandOrder")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> rollbackDemandOrder(@RequestBody @Valid RollbackDemandOrderRequestDto requestDto) {
        //一级原因
        DemandOrderCancelTypeEnum typeEnum = DemandOrderCancelTypeEnum.getEnumByStringKey(requestDto.getRollbackCauseType());
        if (DemandOrderCancelTypeEnum.NULL.equals(typeEnum)){
            throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
        }
        //二级原因
        DemandOrderCancelTypeEnum.DemandOrderCancelTypeTwoEnum typeTwoEnum = DemandOrderCancelTypeEnum.DemandOrderCancelTypeTwoEnum.getEnumByStringKey(requestDto.getRollbackCauseTypeTwo());
        if (DemandOrderCancelTypeEnum.DemandOrderCancelTypeTwoEnum.NULL.equals(typeTwoEnum) || !typeTwoEnum.getTypeEnum().equals(typeEnum)){
            throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
        }

        return demandOrderForLeYiClient.rollbackDemandOrder(MapperUtils.mapper(requestDto, RollbackDemandOrderRequestModel.class));
    }

    /**
     * 获取需求单详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "获取需求单详情", tags = "1.3.7")
    @PostMapping(value = "/api/demandOrder/getDetailForLeYi")
    public Result<DemandOrderDetailForLeYiResponseDto> getDetailForLeYi(@RequestBody @Valid DemandOrderDetailRequestDto requestDto) {
        Result<DemandOrderDetailForLeYiResponseModel> result = demandOrderForLeYiClient.getDetailForLeYi(MapperUtils.mapper(requestDto, DemandOrderDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), DemandOrderDetailForLeYiResponseDto.class, new DemandOrderDetailForLeYiMapping())) ;
    }
    /**
     * 获取需求单号详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "获取客户单号详情v1.1.3")
    @PostMapping(value = "/api/demandOrder/getDemandOrderOrders")
    public Result<List<DemandOrderOrdersResponseDto>> getDemandOrderOrders(@RequestBody @Valid DemandOrderDetailRequestDto requestDto) {
        Result<List<DemandOrderOrderRelResponseModel>> result = demandOrderForLeYiClient.getDemandOrderOrders(MapperUtils.mapper(requestDto, DemandOrderDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), DemandOrderOrdersResponseDto.class , new DemandOrderOrdersMapping())) ;
    }

    /**
     * 云盘需求单批量发布详情 3.22.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "云盘需求单批量发布详情")
    @PostMapping(value = "/api/demandOrder/publishDetail")
    public Result<List<BatchPublishDetailResponseDto>> publishDetail(@RequestBody @Valid BatchPublishDetailRequestDto requestDto) {
        Result<List<BatchPublishDetailResponseModel>> result = demandOrderForLeYiClient.publishDetail(MapperUtils.mapper(requestDto, BatchPublishDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), BatchPublishDetailResponseDto.class, new PublishDetailMapping()));
    }

    /**
     * 云盘需求单批量发布 3.22.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "云盘需求单批量发布")
    @PostMapping(value = "/api/demandOrder/confirmPublish")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> confirmPublish(@RequestBody @Valid BatchPublishRequestDto requestDto) {
        //入参校验
        //需求单为空
        if (ListUtils.isEmpty(requestDto.getDemandDtoList())){
            throw new BizException(ManagementWebApiExceptionEnum.DEMADNLIST_ISNOTNULL);
        }
        if (requestDto.getDemandDtoList().size() > CommonConstant.INT_TEN){
            throw new BizException(ManagementWebApiExceptionEnum.PUBLISH_DEMAND_ORDER_MAX);
        }

        //接单模式
        OrderModeEnum orderModeEnum = OrderModeEnum.getEnumByStringKey(requestDto.getOrderMode());
        //指定车主
        if (OrderModeEnum.ASSIGN_CARRIER.equals(orderModeEnum)){
            //是否我司
            if (!CommonConstant.ONE.equals(requestDto.getIsOurCompany()) && !CommonConstant.TWO.equals(requestDto.getIsOurCompany())){
                throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
            }
            if (CommonConstant.TWO.equals(requestDto.getIsOurCompany())) {//其他车主
                //车主ID校验
                if (StringUtils.isBlank(requestDto.getCompanyCarrierId())) {
                    throw new BizException(ManagementWebApiExceptionEnum.COMPANY_CARRIER_ID_IS_NULL);
                }
            }else{
                requestDto.setCompanyCarrierId(null);
            }

            //竞价抢单字段置空
            requestDto.setQuoteDuration(null);
            requestDto.setCompanyCarrierRange(null);
            requestDto.setVehicleLengthNeed(null);
            requestDto.setVehicleLengthId(null);
            requestDto.setExpectedLoadTime(null);
            requestDto.setExpectedUnloadTime(null);
            requestDto.setRemark(null);
        }
        //竞价抢单
        else if (OrderModeEnum.BIDDING_PRICE.equals(orderModeEnum)){
            //报价时长
            QuoteDurationEnum quoteDurationEnum = QuoteDurationEnum.getEnumByStringKey(requestDto.getQuoteDuration());
            if (QuoteDurationEnum.DEFAULT.equals(quoteDurationEnum)){
                throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
            }
            //车主范围
            if (CommonConstant.ONE.equals(requestDto.getCompanyCarrierRange())){
                requestDto.setCompanyCarrierIdList(null);
            } else if (CommonConstant.TWO.equals(requestDto.getCompanyCarrierRange())){
                //定向选择，需选择车主
                if (ListUtils.isEmpty(requestDto.getCompanyCarrierIdList())){
                    throw new BizException(ManagementWebApiExceptionEnum.COMPANY_CARRIER_ID_IS_NULL);
                }
            }else{
                throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
            }
            //车长要求
            if (CommonConstant.ONE.equals(requestDto.getVehicleLengthNeed())){
                //无车长要求
                requestDto.setVehicleLengthId(null);
            }else if (CommonConstant.TWO.equals(requestDto.getVehicleLengthNeed())){
                //指定车长
                if (StringUtils.isBlank(requestDto.getVehicleLengthId())){
                    throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
                }
            }else{
                throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
            }

            //期望提货时间
            if (StringUtils.isBlank(requestDto.getExpectedLoadTime())){
                throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
            }
            //期望卸货时间
            if (StringUtils.isBlank(requestDto.getExpectedUnloadTime())){
                throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
            }

            //指定车主字段置空
            requestDto.setIsOurCompany(null);
            requestDto.setCompanyCarrierId(null);
        }
        else{
            throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
        }

        return demandOrderForLeYiClient.confirmPublish(MapperUtils.mapper(requestDto, BatchPublishRequestModel.class));
    }

    /**
     * 模糊搜索仓库地址(发布页面调用云盘仓库接口)
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "模糊搜索仓库地址(发布页面调用云盘仓库接口) V1.1.0")
    @PostMapping(value = "/api/demandOrder/searchYPWarehouse")
    public Result<List<SearchDemandUnLoadAddressResponseDto>> searchYPWarehouse(@RequestBody @Valid SearchDemandUnLoadAddressRequestDto requestDto) {
        Result<List<SearchDemandUnLoadAddressResponseModel>> result = demandOrderForLeYiClient.searchYPWarehouse(MapperUtils.mapper(requestDto, SearchDemandUnLoadAddressRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SearchDemandUnLoadAddressResponseDto.class));
    }

    /**
     * 确认放空
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "确认放空 V1.1.1")
    @PostMapping(value = "/api/demandOrder/confirmEmpty")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> confirmEmpty(@RequestBody @Valid DemandOrderEmptyRequestDto requestDto) {
        return demandOrderForLeYiClient.confirmEmpty(MapperUtils.mapper(requestDto, DemandOrderEmptyRequestModel.class));
    }

    /**
     * 云盘物流看板-调度报警
     *
     * @return 调度报警数据
     */
    @ApiOperation(value = "云盘物流看板-调度报警 v1.1.4")
    @PostMapping(value = "/api/demandOrder/dispatchAlarmStatistics")
    public Result<List<DispatchAlarmStatisticsResponseDto>> dispatchAlarmStatistics() {
        Result<List<DispatchAlarmStatisticsResponseModel>> result = demandOrderForLeYiClient.dispatchAlarmStatistics();
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), DispatchAlarmStatisticsResponseDto.class, new DispatchAlarmStatisticsMapping()));
    }

    /**
     * 云盘物流看板-待调度、待发布
     *
     * @return 待发布, 待调度数据
     */
    @ApiOperation(value = "云盘物流看板-待调度、待发布 v1.1.4")
    @PostMapping(value = "/api/demandOrder/waitDispatchStatistics")
    public Result<WaitDispatchStatisticsResponseDto> waitDispatchStatistics() {
        Result<WaitDispatchStatisticsResponseModel> result = demandOrderForLeYiClient.waitDispatchStatistics();
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), WaitDispatchStatisticsResponseDto.class,new WaitDispatchStatisticsMapping()));
    }

    /**
     * 云盘物流看板-合计数据
     * @return
     */
    @ApiOperation(value = "云盘物流看板-合计数据 v1.1.4")
    @PostMapping(value = "/api/demandOrder/aggregateDataStatistics")
    public Result<AggregateDataStatisticsResponseDto> aggregateDataStatistics() {
        Result<AggregateDataStatisticsResponseModel> result = demandOrderForLeYiClient.aggregateDataStatistics();
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), AggregateDataStatisticsResponseDto.class, new AggregateDataStatisticsMapping()));
    }


    /**
     * 云盘物流看板-地图数据
     * @return
     */
    @ApiOperation(value = "云盘物流看板-地图数据 v1.1.4")
    @PostMapping(value = "/api/demandOrder/mapDataStatistics")
    public Result<List<MapDataStatisticsResponseDto>> mapDataStatistics() {
        Result<List<MapDataStatisticsResponseModel>> result = demandOrderForLeYiClient.mapDataStatistics();
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), MapDataStatisticsResponseDto.class));
    }

    /**
     * 云盘需求单-智能拼单
     *
     * @return
     */
    @ApiOperation(value = "云盘需求单-智能拼单v1.1.9")
    @PostMapping(value = "/api/demandOrder/smartSpellList")
    public Result<SmartSpellListResponseDto> smartSpellList() {
        Result<SmartSpellListResponseModel> result = demandOrderForLeYiClient.smartSpellList();
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SmartSpellListResponseDto.class));
    }

    /**
     * 云盘需求单修改车主
     *
     * @param requestDto 修改车主信息
     * @return 操作结果
     */
    @ApiOperation(value = "云盘需求单修改车主v1.2.3")
    @PostMapping(value = "/api/demandOrder/modifyCarrierForLeyi")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> modifyCarrierForLeyi(@RequestBody @Valid ModifyCarrierForLeyiRequestDto requestDto) {
        Result<Boolean> result = demandOrderForLeYiClient.modifyCarrierForLeyi(MapperUtils.mapper(requestDto, ModifyCarrierForLeyiRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 云盘需求单修改车主详情查询
     *
     * @param requestDto 需求单id集合
     * @return 详情信息
     */
    @ApiOperation(value = "云盘需求单修改车主详情查询v1.2.3")
    @PostMapping(value = "/api/demandOrder/modifyCarrierDetailForLeyi")
    public Result<List<ModifyCarrierDetailForLeyiResponseDto>> modifyCarrierDetailForLeyi(@RequestBody @Valid ModifyCarrierDetailForLeyiRequestDto requestDto) {
        Result<List<ModifyCarrierDetailForLeyiResponseModel>> result = demandOrderForLeYiClient.modifyCarrierDetailForLeyi(MapperUtils.mapper(requestDto, ModifyCarrierDetailForLeyiRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), ModifyCarrierDetailForLeyiResponseDto.class));
    }
}
