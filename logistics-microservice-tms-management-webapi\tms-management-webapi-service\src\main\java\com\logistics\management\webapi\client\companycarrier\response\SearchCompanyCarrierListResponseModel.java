package com.logistics.management.webapi.client.companycarrier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/9/27 17:39
 */
@Data
public class SearchCompanyCarrierListResponseModel {

    @ApiModelProperty("承运商公司id")
    private Long companyCarrierId;

    @ApiModelProperty("道路许可证是否后补: 0 否 1 是")
    private Integer roadTransportCertificateIsAmend;

    @ApiModelProperty("车主类型: 1 企业 2 个人")
    private Integer type;

    @ApiModelProperty("来源 1后台添加 2 web注册 3 app注册 4 车主升级")
    private Integer source;

    @ApiModelProperty("创建时间")
    private Date createdTime;

    @ApiModelProperty("最后修改人")
    private String lastModifiedBy;

    @ApiModelProperty("最后操作时间")
    private Date lastModifiedTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否加入黑名单：0 否，1 是")
    private Integer ifAddBlacklist;

    /**
     * 是否零担模式：0 否，1 是
     */
    private Integer ifLessThanTruckload;

    /**
     * 车主账号名称
     */
    @ApiModelProperty("车主账号名称")
    private String carrierContactName;

    /**
     * 车主账号手机号
     */
    @ApiModelProperty("车主账号手机号")
    private String carrierContactPhone;

    @ApiModelProperty("公司名字")
    private String companyCarrierName;

    @ApiModelProperty("营业执照是否后补 0否1是")
    private Integer tradingCertificateIsAmend;

    @ApiModelProperty("授权状态, 0:待授权 1:待审核 2:已驳回 3:已授权")
    private Integer authorizationStatus;

    @ApiModelProperty("实名认证(个人): 0 待实名 1 实名中 2 已实名")
    private Integer realNameAuthenticationStatus;
}
