package com.logistics.appapi.controller.driversafemeeting.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/11/8 11:53
 */
@Data
public class SafeMeetingListResponseDto {
    @ApiModelProperty("学习例会关系id")
    private String safeMeetingRelationId="";
    @ApiModelProperty("学习状态：0未学习，1已学习")
    private String status="";
    private String statusDesc="";
    @ApiModelProperty(value = "学习月份")
    private String period="";
    @ApiModelProperty("例会标题")
    private String title="";
    @ApiModelProperty(value = "创建人（组织人）")
    private String createdBy="";
    @ApiModelProperty(value = "创建时间（发布时间）")
    private String createdTime="";
    @ApiModelProperty(value = "安全例会类型：1 安全例会，2 紧急培训")
    private String type = "";
}
