package com.logistics.tms.api.feign.entrustaddress.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2018/12/26 16:53
 */
@Data
public class SearchEntrustAddressRequestModel extends AbstractPageForm<SearchEntrustAddressRequestModel> {

    @ApiModelProperty("地址类型：1 发货，2 收货")
    private Integer addressType;
    @ApiModelProperty("来源：1 后台，2 前台")
    private String source = "";
}
