package com.logistics.appapi.controller.reservationorder.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2024/8/14 15:04
 */
@Data
public class ReservationOrderConfirmSignRequestDto {

    /**
     * 预约单id
     */
    @NotBlank(message = "预约单id不能为空")
    private String reservationOrderId;

    /**
     * 定位-经度
     */
    @NotBlank(message = "请获取定位")
    private String longitude;
    /**
     * 定位-纬度
     */
    @NotBlank(message = "请获取定位")
    private String latitude;

}
