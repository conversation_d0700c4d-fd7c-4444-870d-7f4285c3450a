package com.logistics.tms.base.enums;

/**
 * liang current user system login name
 * 2018/9/26 current system date
 */
public enum EntrustTypeEnum {
    DEFAULT(0,""),
    DELIVER(1,"发货"),
    RECYCLE_IN(2,"回收入库"),
    PROCUREMENT(3,"采购"),
    TRANSFERS(4,"调拨"),
    LEYI_PUBLISH(5,"委托发布"),
    BOOKING(6,"预约"),
    RETURN_GOODS(7, "退货"),
    SUPPLIER_DIRECT_DISTRIBUTION(9, "供应商直配"),
    RECYCLE_OUT(10, "回收出库"),
    RETURN_GOODS_DISTRIBUTION(11, "退货仓库配送"),
    RETURN_GOODS_TRANSFERS(12, "退货调拨"),
    LIFE_TRANSFER(100, "新生回收"),
    LIFE_SALE(101, "新生销售"),
    OTHER(-99,"其他"),
    ;

    private Integer key;
    private String value;

    EntrustTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static EntrustTypeEnum getEnum(Integer key) {
        for (EntrustTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return EntrustTypeEnum.DEFAULT;
    }
}
