package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-07-29 15:22
 */
@Data
public class GetCarrierOrderNodeAmountGoodsModel {
    @ApiModelProperty("货物id")
    private Long goodsId;
    @ApiModelProperty("货物名称")
    private String goodsName;
    @ApiModelProperty("预提")
    private BigDecimal expectAmount;
    @ApiModelProperty("提货")
    private BigDecimal loadAmount;
    @ApiModelProperty("卸货")
    private BigDecimal unLoadAmount;

    @ApiModelProperty(value = "货物的编码集合 v2.44", required = true)
    private List<CarrierOrderNodeCodeModel> codeDtoList;
}
