package com.logistics.management.webapi.controller.vehiclesettlement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @author: wjf
 * @date: 2021/7/20 16:19
 */
@Data
public class UpdateVehicleSettlementTireRequestDto {
    @ApiModelProperty("车辆结算表id")
    @NotBlank(message = "id不能为空")
    private String vehicleSettlementId;
    @ApiModelProperty("勾选的轮胎费用id")
    private List<String> selectVehicleTireIdList;
}
