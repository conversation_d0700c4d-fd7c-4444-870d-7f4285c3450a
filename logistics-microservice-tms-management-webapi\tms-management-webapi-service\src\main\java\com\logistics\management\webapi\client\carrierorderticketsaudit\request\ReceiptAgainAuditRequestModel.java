package com.logistics.management.webapi.client.carrierorderticketsaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReceiptAgainAuditRequestModel {

    @ApiModelProperty(value = "回单审核Id")
    private Long receiptAuditId;

    @ApiModelProperty(value = "审核结果; 1 审核通过，2 驳回")
    private Integer auditStatus;

    @ApiModelProperty(value = "回单图片")
    private List<String> ticketImages;

    @ApiModelProperty(value = "备注")
    private String remark;
}
