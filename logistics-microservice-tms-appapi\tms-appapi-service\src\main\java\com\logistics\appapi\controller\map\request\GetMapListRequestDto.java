package com.logistics.appapi.controller.map.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/30
 */
@Data
public class GetMapListRequestDto {

	@ApiModelProperty(value = "行政区划id（父级id）")
	private String mapId;

	@ApiModelProperty(value = "级别：0 国家，1 省，2 市，3 区",required = true)
	@NotBlank(message = "省市区级别不能为空")
	private String level;
}
