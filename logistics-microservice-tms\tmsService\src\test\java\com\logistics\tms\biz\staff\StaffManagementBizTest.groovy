package com.logistics.tms.biz.staff

import com.logistics.tms.biz.staff.model.GetStaffIfCompleteByIdsModel
import com.logistics.tms.biz.staff.model.StaffIdDriverCredentialIdModel
import com.logistics.tms.controller.staff.request.AddOrModifyStaffTicketsRequestModel
import com.logistics.tms.controller.staff.request.ContinueLearningAddOrModifyRequestModel
import com.logistics.tms.controller.staff.request.DeleteStaffRequestModel
import com.logistics.tms.controller.staff.request.EnableStaffRequestModel
import com.logistics.tms.controller.staff.request.ImportStaffRequestModel
import com.logistics.tms.controller.staff.request.IntegrityExaminationAddOrModifyRequestModel
import com.logistics.tms.controller.staff.request.OccupationalAddOrModifyRequestModel
import com.logistics.tms.controller.staff.request.SearchStaffManagementListRequestModel
import com.logistics.tms.controller.staff.request.StaffIdRequestModel
import com.logistics.tms.controller.staff.response.ContinueLearningListResponseModel
import com.logistics.tms.controller.staff.response.ExportStaffManagementListResponseModel
import com.logistics.tms.controller.staff.response.GetStaffDetailResponseModel
import com.logistics.tms.controller.staff.response.ImportStaffResponseModel
import com.logistics.tms.controller.staff.response.IntegrityExaminationListResponseModel
import com.logistics.tms.controller.staff.response.OccupationalListResponseModel
import com.logistics.tms.controller.staff.response.SearchStaffManagementListResponseModel
import com.yelo.tray.core.exception.BizException
import com.logistics.tms.api.feign.common.SrcUrlModel
import com.logistics.tms.api.feign.common.model.CertificatePictureModel
import com.logistics.tms.biz.staffvehiclerelation.model.TStaffVehicleRelationModel
import com.logistics.tms.api.feign.violationregulation.model.FuzzyQueryDriverInfoRequestModel
import com.logistics.tms.api.feign.violationregulation.model.FuzzyQueryDriverInfoResponseModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.biz.customeraccount.CustomerAccountBiz
import com.logistics.tms.entity.*
import com.logistics.tms.mapper.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class StaffManagementBizTest extends Specification {
    @Mock
    TStaffBasicMapper tqStaffBasicMapper
    @Mock
    TStaffDriverOccupationalRecordMapper tqStaffDriverOccupationalRecordMapper
    @Mock
    TStaffDriverContinueLearningRecordMapper tqStaffDriverContinueLearningRecordMapper
    @Mock
    TStaffDriverIntegrityExaminationRecordMapper tqStaffDriverIntegrityExaminationRecordMapper
    @Mock
    TCertificationPicturesMapper tqCertificationPicturesMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TStaffDriverCredentialMapper staffDriverCredentialMapper
    @Mock
    TInsuranceMapper insuranceMapper
    @Mock
    StaffTransBiz staffTransBiz
    @Mock
    TStaffVehicleRelationMapper tStaffVehicleRelationMapper
    @Mock
    CustomerAccountBiz customerAccountBiz
    @Mock
    Logger log
    @InjectMocks
    StaffManagementBiz staffManagementBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Staff Management List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqStaffBasicMapper.searchStaffRelIdList(any())).thenReturn([1l])
        when(tqStaffBasicMapper.searchStaffList(anyString())).thenReturn([new SearchStaffManagementListResponseModel()])
        when(tqStaffBasicMapper.getStaffContinueLearningComplete(anyString())).thenReturn([new GetStaffIfCompleteByIdsModel()])
        when(tqStaffBasicMapper.getStaffIntegrityExaminationComplete(anyString())).thenReturn([new GetStaffIfCompleteByIdsModel()])
        when(tqStaffBasicMapper.getStaffOccupationalComplete(anyString())).thenReturn([new GetStaffIfCompleteByIdsModel()])

        expect:
        staffManagementBiz.searchStaffManagementList(requestModel).getList().size() == expectedResult

        where:
        requestModel                                || expectedResult
        new SearchStaffManagementListRequestModel() || 1
    }

    @Unroll
    def "export Staff Management List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqStaffBasicMapper.searchStaffRelIdList(any())).thenReturn([1l])
        when(tqStaffBasicMapper.exportStaffList(anyString())).thenReturn([new ExportStaffManagementListResponseModel()])
        when(tqStaffDriverOccupationalRecordMapper.getOccupationalRecordsByStaffId(anyString())).thenReturn([new TStaffDriverOccupationalRecord(staffId: 1l, issueDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 14).getTime(), validDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 14).getTime())])
        when(tqStaffDriverContinueLearningRecordMapper.getTopContinueLearningByStaffId(anyString())).thenReturn([new TStaffDriverContinueLearningRecord(staffId: 1l, validDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 14).getTime())])
        when(tqStaffDriverIntegrityExaminationRecordMapper.getTopDriverIntegrityExaminationByStaffId(anyString())).thenReturn([new TStaffDriverIntegrityExaminationRecord(staffId: 1l, validDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 14).getTime())])

        expect:
        staffManagementBiz.exportStaffManagementList(requestModel).size() == expectedResult

        where:
        requestModel                                || expectedResult
        new SearchStaffManagementListRequestModel() || 1
    }

    @Unroll
    def "add Or Modify Staff where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqStaffBasicMapper.getByMobile(anyString())).thenReturn(new TStaffBasic(type: 0, staffProperty: 0, gender: 0, name: "name", mobile: "mobile", identityNumber: "identityNumber", age: 0, identityValidity: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 14).getTime(), identityIsForever: 0, laborContractNo: "laborContractNo", laborContractValidDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 14).getTime(), openStatus: 0, enabled: 0, source: 0, remark: "remark"))
        when(tqStaffDriverOccupationalRecordMapper.selectOccupationalList(anyLong())).thenReturn([new OccupationalListResponseModel()])
        when(tqStaffDriverOccupationalRecordMapper.batchUpdate(any())).thenReturn(0)
        when(tqStaffDriverContinueLearningRecordMapper.continueLearningList(anyLong())).thenReturn([new ContinueLearningListResponseModel()])
        when(tqStaffDriverContinueLearningRecordMapper.batchUpdate(any())).thenReturn(0)
        when(tqStaffDriverIntegrityExaminationRecordMapper.integrityExaminationList(anyLong())).thenReturn([new IntegrityExaminationListResponseModel()])
        when(tqStaffDriverIntegrityExaminationRecordMapper.batchUpdate(any())).thenReturn(0)
        when(tqCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", fileName: "fileName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 14).getTime(), suffix: "suffix")])
        when(tqCertificationPicturesMapper.batchInsert(any())).thenReturn(0)
        when(tqCertificationPicturesMapper.batchUpdate(any())).thenReturn(0)
        when(tqCertificationPicturesMapper.getTPicsByIds(anyString(), anyString())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", fileName: "fileName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 14).getTime(), suffix: "suffix")])
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(commonBiz.getLastDayOfMonth(any())).thenReturn(new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 14).getTime())
        when(tStaffVehicleRelationMapper.getRelByStaffId(anyLong())).thenReturn([new TStaffVehicleRelationModel()])

        expect:
        staffManagementBiz.addOrModifyStaff(requestModel) == expectedResult

        where:
        requestModel                              || expectedResult
        new AddOrModifyStaffTicketsRequestModel() || null
    }

    @Unroll
    def "get Staff Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqStaffBasicMapper.getStaffDetail(anyLong())).thenReturn(new GetStaffDetailResponseModel())

        expect:
        staffManagementBiz.getStaffDetail(requestModel) == expectedResult

        where:
        requestModel              || expectedResult
        new StaffIdRequestModel() || null
    }

    @Unroll
    def "delete Staff where requestModel=#requestModel"() {
        given: "准备数据"
        when(tqStaffBasicMapper.getByIds(anyString())).thenReturn([new StaffIdDriverCredentialIdModel()])
        when(tqStaffBasicMapper.batchUpdate(any())).thenReturn(0)
        when(staffDriverCredentialMapper.batchUpdate(any())).thenReturn(0)
        when(insuranceMapper.getByDriverIds(anyString())).thenReturn([new TInsurance(statusType: 0, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 14).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 14).getTime())])

        when: "执行方法"
        staffManagementBiz.deleteStaff(requestModel)

        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where: "批量验证"
        requestModel                  || expectedResult
        new DeleteStaffRequestModel() || "人员信息不存在"
    }

    @Unroll
    def "occupational List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqStaffDriverOccupationalRecordMapper.selectOccupationalList(anyLong())).thenReturn([new OccupationalListResponseModel()])
        when(tqCertificationPicturesMapper.getPicsByIdsAndType(anyString(), anyInt(), anyInt())).thenReturn([new CertificatePictureModel()])

        expect:
        staffManagementBiz.occupationalList(requestModel).size() == expectedResult

        where:
        requestModel              || expectedResult
        new StaffIdRequestModel() || 0
    }

    @Unroll
    def "occupational Add Or Modify where requestModelList=#requestModelList and staffId=#staffId"() {
        given:
        when(tqStaffDriverOccupationalRecordMapper.selectOccupationalList(anyLong())).thenReturn([new OccupationalListResponseModel()])
        when(tqStaffDriverOccupationalRecordMapper.batchUpdate(any())).thenReturn(0)
        when(tqCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", fileName: "fileName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 14).getTime(), suffix: "suffix")])
        when(tqCertificationPicturesMapper.batchInsert(any())).thenReturn(0)
        when(tqCertificationPicturesMapper.batchUpdate(any())).thenReturn(0)
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")

        expect:
        staffManagementBiz.occupationalAddOrModify(requestModelList, staffId)
        assert expectedResult == true

        where:
        requestModelList                            | staffId || expectedResult
        [new OccupationalAddOrModifyRequestModel()] | 1l || true
    }

    @Unroll
    def "continue Learning List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqStaffDriverContinueLearningRecordMapper.continueLearningList(anyLong())).thenReturn([new ContinueLearningListResponseModel()])
        when(tqCertificationPicturesMapper.getPicsByIdsAndType(anyString(), anyInt(), anyInt())).thenReturn([new CertificatePictureModel()])

        expect:
        staffManagementBiz.continueLearningList(requestModel).size() == expectedResult

        where:
        requestModel              || expectedResult
        new StaffIdRequestModel() || 0
    }

    @Unroll
    def "continue Learning Add Or Modify where requestModelList=#requestModelList and staffId=#staffId"() {
        given:
        when(tqStaffDriverContinueLearningRecordMapper.continueLearningList(anyLong())).thenReturn([new ContinueLearningListResponseModel()])
        when(tqStaffDriverContinueLearningRecordMapper.batchUpdate(any())).thenReturn(0)
        when(tqCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", fileName: "fileName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 14).getTime(), suffix: "suffix")])
        when(tqCertificationPicturesMapper.batchInsert(any())).thenReturn(0)
        when(tqCertificationPicturesMapper.batchUpdate(any())).thenReturn(0)
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")

        expect:
        staffManagementBiz.continueLearningAddOrModify(requestModelList, staffId)
        assert expectedResult == true

        where:
        requestModelList                                | staffId || expectedResult
        [new ContinueLearningAddOrModifyRequestModel()] | 1l || true
    }

    @Unroll
    def "integrity Examination List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqStaffDriverIntegrityExaminationRecordMapper.integrityExaminationList(anyLong())).thenReturn([new IntegrityExaminationListResponseModel()])
        when(tqCertificationPicturesMapper.getPicsByIdsAndType(anyString(), anyInt(), anyInt())).thenReturn([new CertificatePictureModel()])

        expect:
        staffManagementBiz.integrityExaminationList(requestModel).size() == expectedResult

        where:
        requestModel              || expectedResult
        new StaffIdRequestModel() || 0
    }

    @Unroll
    def "integrity Examination Add Or Modify where integrityExaminationAddOrModifyRequestModels=#integrityExaminationAddOrModifyRequestModels and staffId=#staffId"() {
        given:
        when(tqStaffDriverIntegrityExaminationRecordMapper.integrityExaminationList(anyLong())).thenReturn([new IntegrityExaminationListResponseModel()])
        when(tqStaffDriverIntegrityExaminationRecordMapper.batchUpdate(any())).thenReturn(0)
        when(tqCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", fileName: "fileName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 14).getTime(), suffix: "suffix")])
        when(tqCertificationPicturesMapper.batchInsert(any())).thenReturn(0)
        when(tqCertificationPicturesMapper.batchUpdate(any())).thenReturn(0)
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(commonBiz.getLastDayOfMonth(any())).thenReturn(new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 14).getTime())

        expect:
        staffManagementBiz.integrityExaminationAddOrModify(integrityExaminationAddOrModifyRequestModels, staffId)
        assert expectedResult == true

        where:
        integrityExaminationAddOrModifyRequestModels        | staffId || expectedResult
        [new IntegrityExaminationAddOrModifyRequestModel()] | 1l || true
    }

    @Unroll
    def "import Staff where importStaffRequestModel=#importStaffRequestModel then expect: #expectedResult"() {
        given:
        when(tqStaffBasicMapper.getByMobile(anyString())).thenReturn(new TStaffBasic())

        expect:
        staffManagementBiz.importStaff(importStaffRequestModel) == expectedResult

        where:
        importStaffRequestModel       || expectedResult
        new ImportStaffRequestModel() || new ImportStaffResponseModel()
    }

    @Unroll
    def "import Staff Certificate where srcUrlModel=#srcUrlModel"() {
        given: "准备数据"
        when(tqStaffBasicMapper.getByName(anyString())).thenReturn(new TStaffBasic())
        when(tqStaffBasicMapper.getCountByName(anyString())).thenReturn(0)
        when(tqCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", fileName: "fileName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 14).getTime(), suffix: "suffix")])
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(staffDriverCredentialMapper.getDriverCredentialByStaffId(anyLong())).thenReturn(new TStaffDriverCredential())

        when: "执行方法"
        staffManagementBiz.importStaffCertificate(srcUrlModel)

        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where: "批量验证"
        srcUrlModel       || expectedResult
        new SrcUrlModel() || "导入失败"
    }

    @Unroll
    def "fuzzy Query Driver Info where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqStaffBasicMapper.fuzzyQueryDriverMessage(any())).thenReturn([new FuzzyQueryDriverInfoResponseModel()])

        expect:
        staffManagementBiz.fuzzyQueryDriverInfo(requestModel).size() == expectedResult

        where:
        requestModel                           || expectedResult
        new FuzzyQueryDriverInfoRequestModel() || 1
    }

    @Unroll
    def "enable Or Disable where requestModel=#requestModel"() {
        given: "准备数据"
        when(tqStaffBasicMapper.batchUpdate(any())).thenReturn(0)
        when(tqStaffBasicMapper.getStaffByIds(anyString())).thenReturn([new TStaffBasic(type: 0, openStatus: 0, enabled: 0)])


        when: "执行方法"
        staffManagementBiz.enableOrDisable(requestModel)

        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where: "批量验证"
        requestModel                  || expectedResult
        new EnableStaffRequestModel() || "人员信息不存在"
    }

    @Unroll
    def "get Staff Detail By Account Id"() {
        given:
        when(tqStaffBasicMapper.getStaffDetail(anyLong())).thenReturn(new GetStaffDetailResponseModel())
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)

        expect:
        staffManagementBiz.getStaffDetailByAccountId()


    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme