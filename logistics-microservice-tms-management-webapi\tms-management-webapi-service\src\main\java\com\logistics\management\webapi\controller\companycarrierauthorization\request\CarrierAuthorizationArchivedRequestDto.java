package com.logistics.management.webapi.controller.companycarrierauthorization.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/3
 */
@Data
public class CarrierAuthorizationArchivedRequestDto {

	@ApiModelProperty(value = "车主授权信息id", required = true)
	@NotBlank(message = "车主授权信息id不能为空")
	private String carrierAuthorizationId;

	@ApiModelProperty(value = "归档文件; 为空时清空文件", required = true)
	private String archivedFilePath;

	@ApiModelProperty(value = "备注")
	@Length(max = 100, message = "备注内容为最多 100 字符")
	private String remark;
}
