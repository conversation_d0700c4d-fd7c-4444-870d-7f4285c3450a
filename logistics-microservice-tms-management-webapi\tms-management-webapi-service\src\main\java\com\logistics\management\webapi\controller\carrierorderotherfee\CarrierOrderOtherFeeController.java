package com.logistics.management.webapi.controller.carrierorderotherfee;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.constant.ExportCarrierOrderOtherFee;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.client.carrierorderotherfee.CarrierOrderOtherFeeClient;
import com.logistics.management.webapi.client.carrierorderotherfee.request.*;
import com.logistics.management.webapi.client.carrierorderotherfee.response.CarrierOrderOtherFeeDetailResponseModel;
import com.logistics.management.webapi.client.carrierorderotherfee.response.CarrierOrderOtherFeeItemDetailResponseModel;
import com.logistics.management.webapi.client.carrierorderotherfee.response.GetOtherFeeRecordResponseModel;
import com.logistics.management.webapi.client.carrierorderotherfee.response.SearchOtherFeeListResponseModel;
import com.logistics.management.webapi.controller.carrierorderotherfee.mapping.CarrierOrderOtherFeeDetailMapping;
import com.logistics.management.webapi.controller.carrierorderotherfee.mapping.CarrierOrderOtherFeeListMapping;
import com.logistics.management.webapi.controller.carrierorderotherfee.request.*;
import com.logistics.management.webapi.controller.carrierorderotherfee.response.CarrierOrderOtherFeeDetailResponseDto;
import com.logistics.management.webapi.controller.carrierorderotherfee.response.GetOtherFeeRecordResponseDto;
import com.logistics.management.webapi.controller.carrierorderotherfee.response.SearchOtherFeeListResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.ExcelUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2024/3/19 13:09
 */
@Api(value = "临时费用管理", tags = "临时费用管理")
@RestController
public class CarrierOrderOtherFeeController {

    @Resource
    private CarrierOrderOtherFeeClient carrierOrderOtherFeeClient;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private ConfigKeyConstant configKeyConstant;

    /**
     * 临时费用列表
     * @param requestDto  v2.43
     * @return
     */
    @ApiOperation(value = "临时费用列表", tags = "3.16.0")
    @PostMapping(value = "/api/carrierOrderOtherFee/searchList")
    public Result<PageInfo<SearchOtherFeeListResponseDto>> searchList(@RequestBody SearchOtherFeeListRequestDto requestDto) {
        SearchOtherFeeListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchOtherFeeListRequestModel.class);
        requestModel.setRequestSource(CommonConstant.ONE);
        Result<PageInfo<SearchOtherFeeListResponseModel>> pageInfoResult = carrierOrderOtherFeeClient.searchList(requestModel);
        pageInfoResult.throwException();
        PageInfo pageInfo = pageInfoResult.getData();
        List list = MapperUtils.mapper(pageInfo.getList(), SearchOtherFeeListResponseDto.class, new CarrierOrderOtherFeeListMapping());
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    /**
     * 导出临时费用
     * @param requestDto
     * @param response
     * @return
     */
    @ApiOperation(value = "临时费用列表导出", tags = "3.16.0")
    @GetMapping(value = "/api/carrierOrderOtherFee/exportCarrierOrderOtherFeeList")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportCarrierOrderOtherFeeList(SearchOtherFeeListRequestDto requestDto, HttpServletResponse response) {
        SearchOtherFeeListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchOtherFeeListRequestModel.class);
        requestModel.setRequestSource(CommonConstant.ONE);
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<SearchOtherFeeListResponseModel>> pageInfoResult = carrierOrderOtherFeeClient.searchList(requestModel);
        pageInfoResult.throwException();
        List<SearchOtherFeeListResponseDto> list = MapperUtils.mapper(pageInfoResult.getData().getList(), SearchOtherFeeListResponseDto.class, new CarrierOrderOtherFeeListMapping());
        String fileName = "TMS云盘临时费用列表";
        Map<String,String> exportMap = ExportCarrierOrderOtherFee.getExcelCarrierOrderOtherFee();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return list;
            }
        });
    }

    /**
     * 新增临时费用 v2.43
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "新增临时费用 v1.2.2")
    @PostMapping(value = "/api/carrierOrderOtherFee/addCarrierOrderOtherFee")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addCarrierOrderOtherFee(@RequestBody @Valid AddCarrierOrderOtherFeeRequestDto requestDto) {
        List<String> feeTypeList = requestDto.getOtherFeeList().stream().map(AddCarrierOrderOtherFeeItemRequestDto::getFeeType).distinct().collect(Collectors.toList());
        if (feeTypeList.size() != requestDto.getOtherFeeList().size()){
            throw new BizException(ManagementWebApiExceptionEnum.CARRIER_ORDER_OTHER_FEE_TYPE_REPEAT);
        }
        AddCarrierOrderOtherFeeRequestModel requestModel = MapperUtils.mapper(requestDto, AddCarrierOrderOtherFeeRequestModel.class);
        requestModel.setRequestSource(CommonConstant.ONE);
        return carrierOrderOtherFeeClient.addCarrierOrderOtherFee(requestModel);
    }

    /**
     * 临时费用详情 v 2.43
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "临时费用详情  v2.44(2)", tags = "2.43")
    @PostMapping(value = "/api/carrierOrderOtherFee/getCarrierOrderOtherFeeDetail")
    public Result<CarrierOrderOtherFeeDetailResponseDto> getCarrierOrderOtherFeeDetail(@RequestBody @Valid CarrierOrderOtherFeeDetailRequestDto requestDto) {
        CarrierOrderOtherFeeDetailRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierOrderOtherFeeDetailRequestModel.class);
        requestModel.setRequestSource(CommonConstant.ONE);
        Result<CarrierOrderOtherFeeDetailResponseModel> carrierOrderOtherFeeDetailResponseModelResult = carrierOrderOtherFeeClient.getCarrierOrderOtherFeeDetail(requestModel);
        carrierOrderOtherFeeDetailResponseModelResult.throwException();
        List<CarrierOrderOtherFeeItemDetailResponseModel> otherFeeList = carrierOrderOtherFeeDetailResponseModelResult.getData().getOtherFeeList();
        List<String> sourceSrcList=new ArrayList<>();
        otherFeeList.forEach(o-> sourceSrcList.addAll(o.getBillsPicture()));
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(carrierOrderOtherFeeDetailResponseModelResult.getData(),CarrierOrderOtherFeeDetailResponseDto.class,new CarrierOrderOtherFeeDetailMapping(configKeyConstant.fileAccessAddress,imageMap)));
    }

    /**
     * 提交/审核通过临时费用  v2.43
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "提交/审核通过临时费用 v2.44(2)")
    @PostMapping(value = "/api/carrierOrderOtherFee/commitCarrierOrderOtherFee")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> commitCarrierOrderOtherFee(@RequestBody @Valid CommitCarrierOrderOtherFeeRequestDto requestDto) {
        //费用类型不能重复
        List<String> feeTypeList = requestDto.getOtherFeeList().stream().map(CommitCarrierOrderOtherFeeItemRequestDto::getFeeType).distinct().collect(Collectors.toList());
        if (feeTypeList.size() != requestDto.getOtherFeeList().size()){
            throw new BizException(ManagementWebApiExceptionEnum.CARRIER_ORDER_OTHER_FEE_TYPE_REPEAT);
        }

        CommitCarrierOrderOtherFeeRequestModel requestModel = MapperUtils.mapper(requestDto, CommitCarrierOrderOtherFeeRequestModel.class);
        requestModel.setRequestSource(CommonConstant.ONE);
        Result<Boolean> booleanResult = carrierOrderOtherFeeClient.commitCarrierOrderOtherFee(requestModel);
        booleanResult.throwException();
        return booleanResult;
    }

    /**
     * 审核驳回临时费用
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "审核驳回临时费用 v1.2.2")
    @PostMapping(value = "/api/carrierOrderOtherFee/rejectOtherFee")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> rejectOtherFee(@RequestBody @Valid RejectCarrierOrderOtherFeeRequestDto requestDto) {
        RejectCarrierOrderOtherFeeRequestModel requestModel = MapperUtils.mapper(requestDto, RejectCarrierOrderOtherFeeRequestModel.class);
        return carrierOrderOtherFeeClient.rejectOtherFee(requestModel);
    }

    /**
     * 撤销
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "撤销 v1.2.2")
    @PostMapping(value = "/api/carrierOrderOtherFee/cancelOtherFee")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> cancelOtherFee(@RequestBody @Valid CarrierOrderOtherFeeDetailRequestDto requestDto) {
        CarrierOrderOtherFeeDetailRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierOrderOtherFeeDetailRequestModel.class);
        requestModel.setRequestSource(CommonConstant.ONE);
        return carrierOrderOtherFeeClient.cancelOtherFee(requestModel);
    }

    /**
     * 查看操作日志
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "操作记录 v1.2.2")
    @PostMapping(value = "/api/carrierOrderOtherFee/getOtherFeeRecord")
    public Result<List<GetOtherFeeRecordResponseDto>> getOtherFeeRecord(@RequestBody @Valid CarrierOrderOtherFeeDetailRequestDto requestDto) {
        CarrierOrderOtherFeeDetailRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierOrderOtherFeeDetailRequestModel.class);
        requestModel.setRequestSource(CommonConstant.ONE);
        Result<List<GetOtherFeeRecordResponseModel>> otherFeeLogsResult = carrierOrderOtherFeeClient.getOtherFeeRecord(requestModel);
        otherFeeLogsResult.throwException();
        return Result.success(MapperUtils.mapper(otherFeeLogsResult.getData(), GetOtherFeeRecordResponseDto.class));
    }

    /**
     * 回退v3.18.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "回退 v3.18.0")
    @PostMapping(value = "/api/carrierOrderOtherFee/rollbackOtherFee")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> rollbackOtherFee(@RequestBody @Valid CarrierOrderOtherFeeDetailRequestDto requestDto){
        return carrierOrderOtherFeeClient.rollbackOtherFee(MapperUtils.mapper(requestDto, CarrierOrderOtherFeeDetailRequestModel.class));
    }
}
