package com.logistics.management.webapi.controller.settlestatement.tradition.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class TraditionStatementCancelRequestDto {

    @ApiModelProperty(value = "对账单id", required = true)
    @NotBlank(message = "id不能为空")
    private String settleStatementId;

    @ApiModelProperty(value = "撤销对账原因,仅撤销时用",required = true)
    @Size(min = 1, max = 50, message = "撤销对账原因不能为空，1~50字")
    @NotBlank(message = "请填写取消原因")
    private String remark;
}
