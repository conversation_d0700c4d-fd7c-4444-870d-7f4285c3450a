package com.logistics.management.webapi.controller.demandorder.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
@ExcelIgnoreUnannotated
public class DemandOrderResponseDto {

    @ApiModelProperty("标识id")
    private String demandId= "";
    @ApiModelProperty("需求单状态：500待发布 1000待调度 2000部分调度 3000调度完成 4000待签收 5000已签收 1取消")
    private String status= "";
    @ExcelProperty(value = "状态", order = 1)
    private String statusDesc = "";
    @ApiModelProperty("委托单号")
    @ExcelProperty(value = "需求单号", order = 2)
    private String demandOrderCode= "";
    @ApiModelProperty("客户单号")
    @ExcelProperty(value = "客户单号", order = 4)
    private String customerOrderCode= "";
    @ApiModelProperty("委托人")
    @ExcelProperty(value = "委托人", order = 39)
    private String publishName= "";
    @ApiModelProperty("下单时间")
    @ExcelProperty(value = "下单时间", order = 6)
    private String publishTime= "";
    @ApiModelProperty("货主公司ID")
    private String companyEntrustId= "";
    @ApiModelProperty("货主公司类型")
    private String companyEntrustType= "";
    @ApiModelProperty("货主公司名称")
    @ExcelProperty(value = "货主", order = 9)
    private String companyEntrustName= "";
    @ApiModelProperty("货主联系人")
    private String entrustContactName= "";
    @ApiModelProperty("货主联系人电话")
    private String entrustContactMobile= "";
    @ApiModelProperty("车主公司ID")
    private String companyCarrierId= "";
    @ApiModelProperty("车主公司类型")
    private String companyCarrierType= "";
    @ApiModelProperty("车主公司名称")
    @ExcelProperty(value = "车主", order = 37)
    private String companyCarrierName= "";
    @ApiModelProperty("车主联系人")
    private String carrierContactName= "";
    @ApiModelProperty("车主联系人电话")
    private String carrierContactMobile= "";
    @ApiModelProperty("已调车数(包含已取消)")
    private String dispatchVehicleCount= "";
    @ApiModelProperty("发货仓库")
    @ExcelProperty(value = "发货仓库", order = 11)
    private String loadWarehouse= "";
    @ApiModelProperty("发货省市区")
    @ExcelProperty(value = "发货省", order = 12)
    private String loadProvinceName = "";
    @ExcelProperty(value = "发货市", order = 13)
    private String loadCityName = "";
    @ExcelProperty(value = "发货区", order = 14)
    private String loadAreaName = "";
    private String loadAddress= "";//拼接后的数据，列表展示
    @ApiModelProperty("发货详细地址")
    @ExcelProperty(value = "发货详细地址", order = 15)
    private String loadDetailAddress= "";
    @ApiModelProperty("发货人")
    @ExcelProperty(value = "发货人姓名", order = 16)
    private String consignorName= "";
    @ExcelProperty(value = "发货人联系方式", order = 17)
    private String consignorMobile="";
    @ApiModelProperty("期望提货时间")
    @ExcelProperty(value = "期望提货时间", order = 31)
    private String expectedLoadTime= "";
    @ApiModelProperty("收货仓库")
    @ExcelProperty(value = "收货仓库", order = 18)
    private String unloadWarehouse= "";
    @ApiModelProperty("收货省市区")
    @ExcelProperty(value = "收货省", order = 19)
    private String unloadProvinceName = "";
    @ExcelProperty(value = "收货市", order = 20)
    private String unloadCityName = "";
    @ExcelProperty(value = "收货区", order = 21)
    private String unloadAreaName = "";
    private String unloadAddress= "";//拼接后的数据，列表展示
    @ApiModelProperty("收货地址详细")
    @ExcelProperty(value = "收货详细地址", order = 22)
    private String unloadDetailAddress= "";
    @ApiModelProperty("收货人")
    @ExcelProperty(value = "收货人姓名", order = 23)
    private String receiverName= "";
    @ExcelProperty(value = "收货人联系方式", order = 24)
    private String receiverMobile="";
    @ApiModelProperty("期望到货时间")
    @ExcelProperty(value = "期望到货时间", order = 25)
    private String expectedUnloadTime= "";
    @ApiModelProperty("品名")
    @ExcelProperty(value = "品名", order = 33)
    private String goodsName= "";
    @ApiModelProperty("规格")
    @ExcelProperty(value = "规格", order = 36)
    private String goodsSize= "";
    @ApiModelProperty("货物单位 1 件 2 吨 3 方 4 块")
    @ExcelProperty(value = "单位", order = 26)
    private String goodsUnit= "";
    @ApiModelProperty("委托件数")
    @ExcelProperty(value = "委托", order = 27)
    private String goodsAmount= "";
    @ApiModelProperty("已安排数量")
    @ExcelProperty(value = "已安排", order = 28)
    private String arrangedAmount= "";
    @ApiModelProperty("未安排数量")
    @ExcelProperty(value = "未安排", order = 30)
    private String notArrangedAmount= "";
    @ApiModelProperty("退回件数")
    @ExcelProperty(value = "已退回", order = 29)
    private String backAmount= "";
    @ApiModelProperty("凭证日期")
    @ExcelProperty(value = "凭证日期", order = 5)
    private String ticketDate= "";

    @ApiModelProperty("预计货主费用")
    @ExcelProperty(value = "预计货主费用", order = 7)
    private String expectEntrustFee = "";
    @ApiModelProperty("实际货主费用")
    @ExcelProperty(value = "实际货主费用", order = 8)
    private String actualEntrustFee = "";
    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注", order = 32)
    private String remark="";

    @ApiModelProperty("生产企业")
    @ExcelProperty(value = "生产企业" ,order = 10)
    private String manufacturerName="";
    @ApiModelProperty("物料运输组")
    @ExcelProperty(value = "物料运输组", order = 34)
    private String itemTransGroupName="";
    @ApiModelProperty("包装规格")
    @ExcelProperty(value = "包装规格", order = 35)
    private String itemPackSpecName="";
    @ApiModelProperty("是否异常：0 否，1 是")
    private String ifObjection="";
    @ApiModelProperty("是否异常（中石化推送单子）：0 否，1 是")
    private String ifObjectionSinopec="";
    @ApiModelProperty("下单类型：10 发布，20 拉取，21 推送")
    private String orderType="";
    @ApiModelProperty("下单类型描述")
    private String orderTypeDesc="";
    @ApiModelProperty("是否网货,  0: 否 1: 是")
    private String sinopecOnlineGoodsFlag="";
    @ApiModelProperty("是否网货")
    @ExcelProperty(value = "是否网货", order = 3)
    private String sinopecOnlineGoodsFlagLabel="";
    @ApiModelProperty("委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件")
    private String source="";
    @ApiModelProperty("需求单状态导出用")
    private String exportStatusDesc="";
    @ApiModelProperty("负责人（调度员）")
    @ExcelProperty(value = "负责人", order = 38)
    private String dispatcher="";
}
