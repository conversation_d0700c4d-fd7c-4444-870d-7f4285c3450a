package com.logistics.appapi.controller.common;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.constant.ConfigKeyConstant;
import com.logistics.appapi.base.enums.AppApiExceptionEnum;
import com.logistics.appapi.client.thirdparty.basicdata.BasicServiceClient;
import com.logistics.appapi.client.thirdparty.basicdata.file.request.BatchGetOSSFileUrlRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.request.GetFileByFilePathRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.request.GetOSSUrlRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.response.GetFileByteOSSResponseModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.response.GetOSSUrlResponseModel;
import com.logistics.appapi.controller.carrierorder.response.CreateQrCodeModel;
import com.yelo.tools.redis.utils.GenRedisImgLocationUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author:lei.zhu
 * @date:2021/11/1 18:04:04
 */
@Slf4j
@Service
public class CommonBiz {

    @Resource
    private BasicServiceClient basicServiceClient;
    @Resource
    private ConfigKeyConstant configKeyConstant;

    /**
     * 获取图片访问路径
     *
     * @param  fileSrc
     */
    public String getImageURL(String fileSrc){
        GetOSSUrlRequestModel requestModel = new GetOSSUrlRequestModel();
        requestModel.setFileSrc(fileSrc);
        GetOSSUrlResponseModel getOSSUrlResponseModel = basicServiceClient.getOSSFileUrl(requestModel);
        return getOSSUrlResponseModel.getFileSrc();
    }

    /**
     * 批量获取图片访问路径
     *
     * @param  fileSrc
     */
    public Map<String,String> batchGetOSSFileUrl(List<String> fileSrc){
        if(ListUtils.isEmpty(fileSrc)){
            return new HashMap<>();
        }
        BatchGetOSSFileUrlRequestModel requestModel = new BatchGetOSSFileUrlRequestModel();
        requestModel.setFileSrcList(fileSrc);
        List<GetOSSUrlResponseModel> list = basicServiceClient.batchGetOSSFileUrl(requestModel);
        if(ListUtils.isNotEmpty(list)){
            return list.stream().collect(Collectors.toMap(GetOSSUrlResponseModel::getSourceFileSrc, GetOSSUrlResponseModel::getFileSrc,(key1, key2)->key2));
        }else{
            return new HashMap<>();
        }
    }

    //判断文件夹是否存在
    public boolean dirIfExist(String saveFile) {
        boolean existFlag = true;
        File file = new File(saveFile);
        if (!file.exists()) {//创建目录
            file.mkdirs();
        } else {
            existFlag = false;
        }
        return existFlag;
    }

    //对入参进行解密
    public String decode(String encryptionPassword) {
        log.info("解密开始："+encryptionPassword);
        String password = "";
        try {
            byte[] passwordBytes = RSAUtils.decryptByPrivateKey(Base64Utils.decode(encryptionPassword), configKeyConstant.loginPrivateKey);
            password = new String(passwordBytes);

        } catch (Exception e) {
            log.warn("解密失败---" + e);
            throw new BizException(AppApiExceptionEnum.DECRYPTION_FAILURE);

        }
        log.info("解密成功："+password);
        return password;
    }

    //验证滑块
    public void validationPuzzlePicture(String locationX, String verificationId){
        //入参不能为空
        if (StringUtils.isBlank(locationX) || StringUtils.isBlank(verificationId)) {
            throw new BizException(AppApiExceptionEnum.IMAGE_VERIFICATION_AUTH_DEFEATED);
        }
        //校验拼图是否正确
        boolean isSuccess = GenRedisImgLocationUtils.validationPuzzlePicture(locationX, verificationId);
        if (!isSuccess) {
            throw new BizException(AppApiExceptionEnum.IMAGE_VERIFICATION_AUTH_DEFEATED);
        }
    }

    /**
     * 获取oss文件字节数组
     *
     * @param ossPath
     */
    public byte[] getOssFileByte(String ossPath) {
        if (StringUtils.isBlank(ossPath)) {
            return null;
        }

        GetFileByFilePathRequestModel requestModel = new GetFileByFilePathRequestModel();
        requestModel.setFilePath(ossPath);
        GetFileByteOSSResponseModel getFileByteOSSResponseModel = basicServiceClient.getFileByteOSS(requestModel);
        return getFileByteOSSResponseModel.getFileByte();
    }


    /**
     * 创建二维码
     *
     * @param linkPrefix 链接前缀
     * @param qrCodeParamsMap  业务参数
     * @return 二维码图片地址
     */
    public CreateQrCodeModel createQrCode(String linkPrefix,
                                          Map<String, Object> qrCodeParamsMap) {

        CreateQrCodeModel createQrCodeModel = new CreateQrCodeModel();
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            String qrCodeContent = getQrCodeContent(linkPrefix, qrCodeParamsMap);
            if (StringUtils.isNotBlank(qrCodeContent)) {
                //生成二维码
                BufferedImage bufferedImage = QRCodeUtils.createImage(qrCodeContent, null, false);
                ImageIO.write(bufferedImage, "jpg", bos);
                createQrCodeModel.setFileByte(bos.toByteArray());
            }
        } catch (Exception e) {
            log.error("生成二维码失败", e);
        }
        return createQrCodeModel;
    }

    /**
     * 拼接二维码内容
     * @param linkPrefix 链接前缀
     * @param qrCodeParamsMap 业务参数
     * @return 跳转路径拼接业务参数的字符串
     */
    public String getQrCodeContent(String linkPrefix,
                                   Map<String, Object> qrCodeParamsMap){
        return CommonConstant.linkParamsJoinFunction.apply(linkPrefix, qrCodeParamsMap);
    }
}
