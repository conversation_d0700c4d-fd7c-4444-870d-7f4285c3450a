package com.logistics.management.webapi.client.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2021/9/27 15:27
 */
@Data
public class BatchPublishRequestModel {

    @ApiModelProperty("需求单列表")
    private List<BatchPublishDemandModel> demandDtoList;

    /**
     * 接单模式：1 指定车主，2 竞价抢单
     */
    @ApiModelProperty(value = "接单模式：1 指定车主，2 竞价抢单")
    private Integer orderMode = 1;


    //指定车主时需要的字段
    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;

    @ApiModelProperty(value = "车主ID")
    private Long companyCarrierId;


    //竞价抢单时需要的字段
    /**
     * 报价时长：1 30分钟，2 60分钟，3 90分钟
     */
    @ApiModelProperty(value = "报价时长：1 30分钟，2 60分钟，3 90分钟")
    private Integer quoteDuration;
    
    /**
     * 车主范围：1 全部，2 定向选择
     */
    @ApiModelProperty(value = "车主范围：1 全部，2 定向选择")
    private Integer companyCarrierRange;

    /**
     * 车主ID（车主范围为【定向选择】时必填）
     */
    @ApiModelProperty(value = "车主ID（车主范围为【定向选择】时必填）")
    private List<Long> companyCarrierIdList;

    /**
     * 车长要求：1 无，2 指定车长
     */
    @ApiModelProperty(value = "车长要求：1 无，2 指定车长")
    private Integer vehicleLengthNeed;

    /**
     * 车长id
     */
    @ApiModelProperty(value = "车长id")
    private Long vehicleLengthId;

    /**
     * 期望提货时间
     */
    @ApiModelProperty(value = "期望提货时间")
    private Date expectedLoadTime;

    /**
     * 期望卸货时间
     */
    @ApiModelProperty(value = "期望卸货时间")
    private Date expectedUnloadTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
