package com.logistics.appapi.controller.vehiclesafecheck.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/11/6 15:01
 */
@Data
public class SafeCheckReformResponseDto {
    @ApiModelProperty("整改表ID")
    private String checkReformId = "";
    @ApiModelProperty("整改数")
    private String reformCount = "";
    @ApiModelProperty("整改事项描述")
    private String reformContent = "";
    @ApiModelProperty("整改事项汇总，譬如：部分合格 xx项整改")
    private String reformItemSummary = "";
    @ApiModelProperty("整改项目不合格项拼接，譬如 XXX;XXX")
    private String reformItems = "";
    @ApiModelProperty("整改结果")
    private String reformResult = "";
    @ApiModelProperty("添加整改结果时间")
    private String addReformResultTime = "";
    @ApiModelProperty("整改事项图片列表")
    private List<SafeCheckFileResponseDto> itemFileList = new ArrayList<>();
    @ApiModelProperty("整改结果图片列表")
    private List<SafeCheckFileResponseDto> resultFileList = new ArrayList<>();
}
