package com.logistics.tms.biz.oilfilled

import com.logistics.tms.controller.oilfilled.request.AddOrModifyOilFilledRequestModel
import com.logistics.tms.controller.oilfilled.request.AddOrModifyOilRefundRequestModel
import com.logistics.tms.controller.oilfilled.request.OilFilledDetailRequestModel
import com.logistics.tms.controller.oilfilled.request.OilFilledListRequestModel
import com.logistics.tms.controller.oilfilled.request.OilFilledOperationRecordRequestModel
import com.logistics.tms.controller.oilfilled.response.OilFilledDetailResponseModel
import com.logistics.tms.controller.oilfilled.response.OilFilledGetSummaryResponseModel
import com.logistics.tms.controller.oilfilled.response.OilFilledOperationRecordResponseModel
import com.logistics.tms.controller.oilfilled.response.OilRefundDetailResponseModel
import com.yelo.tray.core.exception.BizException
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.annotation.Rollback
import org.springframework.transaction.annotation.Transactional
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
@SpringBootTest
class OilFilledBizTest extends Specification {

    @Autowired
    OilFilledBiz oilFilledBiz

    def setup() {
    }

    @Unroll
    def "search List where requestModel=#requestModel then expect: #expectedResult"() {
        given:

        expect:
        with(oilFilledBiz.searchList(requestModel)) {
            def data = list
            with(data) {
                data.get(0).getVehicleNo() == expectedResult
            }
        }

        where:
        requestModel                                        || expectedResult
        new OilFilledListRequestModel(vehicleNo: "浙A09890") || "浙A09890"
    }

    @Unroll
    @Rollback
    @Transactional
    def "add Or Modify where requestModel=#requestModel"() {
        given:

        when:
        oilFilledBiz.addOrModify(requestModel)

        then:
        notThrown(BizException)

        where:
        requestModel                                                                                                                                                                                                                                                                                                                                                                                      || expectedResult
        new AddOrModifyOilFilledRequestModel(cooperationCompany: "", liter: null, oilFilledDate: new GregorianCalendar(2022, 5, 28).getTime(), oilFilledFee: new BigDecimal("11111"), oilFilledFileList: null, oilFilledId: null, oilFilledType: 1, remark: "1231", rewardIntegral: null, staffId: 138, subCardNumber: "1111111111111111111", subCardOwner: "1231", topUpIntegral: null, vehicleId: 3393) || true
    }

    @Unroll
    def "get Summary where requestModel=#requestModel then expect: #expectedResult"() {
        given:

        expect:
        oilFilledBiz.getSummary(requestModel) == expectedResult

        where:
        requestModel                    || expectedResult
        new OilFilledListRequestModel() || new OilFilledGetSummaryResponseModel()
    }

    @Unroll
    def "get Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:

        expect:
        oilFilledBiz.getDetail(requestModel) == expectedResult

        where:
        requestModel                      || expectedResult
        new OilFilledDetailRequestModel() || new OilFilledDetailResponseModel()
    }

    @Unroll
    def "get Operation Record where requestModel=#requestModel then expect: #expectedResult"() {
        given:

        expect:
        oilFilledBiz.getOperationRecord(requestModel) == expectedResult

        where:
        requestModel                               || expectedResult
        new OilFilledOperationRecordRequestModel() || [new OilFilledOperationRecordResponseModel()]
    }

    @Unroll
    def "add Or Modify Refund where requestModel=#requestModel"() {
        given:

        expect:
        oilFilledBiz.addOrModifyRefund(requestModel)
        assert expectedResult == false

        where:
        requestModel                           || expectedResult
        new AddOrModifyOilRefundRequestModel() || true
    }

    @Unroll
    def "get Oil Refund Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:

        expect:
        oilFilledBiz.getOilRefundDetail(requestModel) == expectedResult

        where:
        requestModel                      || expectedResult
        new OilFilledDetailRequestModel() || new OilRefundDetailResponseModel()
    }
}
