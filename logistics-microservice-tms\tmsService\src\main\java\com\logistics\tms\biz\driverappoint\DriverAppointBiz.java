package com.logistics.tms.biz.driverappoint;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.logistics.tms.api.feign.driverpayee.model.ViewLogResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.driverappoint.model.CreateCarrierOrderByAssociatedVehicleModel;
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel;
import com.logistics.tms.controller.driverappoint.request.DriverAppointAssociatedVehicleRequestModel;
import com.logistics.tms.controller.driverappoint.request.SearchAppointRequestModel;
import com.logistics.tms.controller.driverappoint.request.SearchDrierAppointDetailRequestModel;
import com.logistics.tms.controller.driverappoint.request.SearchDriverAppointRequestModel;
import com.logistics.tms.controller.driverappoint.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/25 13:03
 */
@Service
public class DriverAppointBiz {

    @Resource
    private TDriverAppointMapper tDriverAppointMapper;
    @Resource
    private TDriverAppointAddressMapper tDriverAppointAddressMapper;
    @Resource
    private TRenewableAuditMapper tRenewableAuditMapper;
    @Resource
    private TOperateLogsMapper tOperateLogsMapper;
    @Resource
    private TStaffBasicMapper tStaffBasicMapper;
    @Resource
    private TVehicleBasicMapper tVehicleBasicMapper;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private DriverAppointCommonBiz driverAppointCommonBiz;
    @Resource
    private TCertificationPicturesMapper tCertificationPicturesMapper;

    /**
     * 查询驾驶员预约列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<SearchDriverAppointResponseModel> searchDriverAppointList(SearchDriverAppointRequestModel requestModel) {
        requestModel.enablePaging();
        List<TDriverAppoint> driverAppointList = tDriverAppointMapper.queryDriverAppointList(requestModel);
        PageInfo pageInfo = new PageInfo(driverAppointList);
        if (ListUtils.isEmpty(driverAppointList)) {
            return pageInfo;
        }
        List<Long> driverAppointIds = driverAppointList.stream().map(TDriverAppoint::getId).collect(Collectors.toList());
        // 查询地址信息
        Map<Long, TDriverAppointAddress> addressMap = tDriverAppointAddressMapper.queryAddressListByDriverAppointIds(driverAppointIds)
                .stream()
                .collect(Collectors.toMap(TDriverAppointAddress::getDriverAppointId, Function.identity()));
        List<SearchDriverAppointResponseModel> responseModels = driverAppointList.stream().map(driverAppoint -> {
            SearchDriverAppointResponseModel responseModel = MapperUtils.mapper(driverAppoint, SearchDriverAppointResponseModel.class);
            responseModel.setDriverAppointId(driverAppoint.getId());
            if (addressMap.containsKey(responseModel.getDriverAppointId())) {
                TDriverAppointAddress appointAddress = addressMap.get(responseModel.getDriverAppointId());
                // 封装地址信息
                responseModel.setLoadProvinceName(appointAddress.getLoadProvinceName());
                responseModel.setLoadCityName(appointAddress.getLoadCityName());
                responseModel.setLoadAreaName(appointAddress.getLoadAreaName());
                responseModel.setLoadDetailAddress(appointAddress.getLoadDetailAddress());
                responseModel.setLoadWarehouse(appointAddress.getLoadWarehouse());
                responseModel.setConsignorName(appointAddress.getConsignorName());
                responseModel.setConsignorMobile(appointAddress.getConsignorMobile());
            }
            return responseModel;
        }).collect(Collectors.toList());
        pageInfo.setList(responseModels);
        return pageInfo;
    }

    /**
     * 小程序-预约记录-详情
     * @param requestModel
     * @return
     */
    public SearchDrierAppointDetailResponseModel searchDrierAppointDetail(SearchDrierAppointDetailRequestModel requestModel){
        //获取当前登录人id
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        if (loginDriverAppletUserId == null || CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.DATA_ERROR_AND_RE_LOGIN);
        }
        //查询司机基础信息
        TStaffBasic tStaffBasic = tStaffBasicMapper.selectByPrimaryKey(loginDriverAppletUserId);
        if (tStaffBasic == null || IfValidEnum.INVALID.getKey().equals(tStaffBasic.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }

        List<AuditProcessResponseModel> auditProcess = new ArrayList<>();
        //预约记录查询
        SearchDrierAppointDetailResponseModel responseModel = tDriverAppointMapper.searchDrierAppointDetail(requestModel);
        if(responseModel==null || responseModel.getDemandOrderId()==null){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_APPOINT_NOT_EXIST);
        }
        //判断新生订单是否存在
        TRenewableAudit tRenewableAudit = tRenewableAuditMapper.selectByDemandOrderId(responseModel.getDemandOrderId());
        if(tRenewableAudit!=null){
            ArrayList<Integer> list = Lists.newArrayList(OperateLogsOperateTypeEnum.RENEWABLE_AUDIT_ORDER_ORDER.getOperateType(),OperateLogsOperateTypeEnum.RENEWABLE_AUDIT_ORDER_AUDIT.getOperateType(),OperateLogsOperateTypeEnum.RENEWABLE_AUDIT_ORDER_REJECT.getOperateType());
            List<ViewLogResponseModel> viewLogResponseModels = tOperateLogsMapper.selectLogsByOperateType(OperateLogsObjectTypeEnum.RENEWABLE_AUDIT_ORDER.getKey(), tRenewableAudit.getId(), StringUtils.listToString(list,','));
            viewLogResponseModels.forEach(o->{
                AuditProcessResponseModel model = new AuditProcessResponseModel();
                model.setProcessName(o.getOperateContents());
                model.setOccurrenceTime(o.getOperateTime());
                auditProcess.add(model);
            });
            responseModel.setAuditProcess(auditProcess);
        }
        return responseModel;
    }

    /**
     * 小程序-预约记录关联车辆（生成运单）
     * @param requestModel
     */
    @Transactional
    public void associatedVehicle(DriverAppointAssociatedVehicleRequestModel requestModel) {
        //获取当前登录的司机id
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        if (loginDriverAppletUserId == null || CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.DATA_ERROR_AND_RE_LOGIN);
        }

        //查询预约记录是否存在
        TDriverAppoint dbDriverAppoint = tDriverAppointMapper.selectByPrimaryKeyDecrypt(requestModel.getDriverAppointId());
        if (dbDriverAppoint == null || dbDriverAppoint.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_APPOINT_NOT_EXIST);
        }
        //判断是否可以关联车辆
        if (CommonConstant.INTEGER_ONE.equals(dbDriverAppoint.getIfAssociatedVehicle())){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_APPOINT_ASSOCIATED_VEHICLE);
        }

        //查询司机基础信息
        TStaffBasic tStaffBasic = tStaffBasicMapper.selectByPrimaryKey(loginDriverAppletUserId);
        if (tStaffBasic == null || IfValidEnum.INVALID.getKey().equals(tStaffBasic.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }

        //查询车辆基本信息
        VehicleBasicPropertyModel vehicleBasicPropertyModel = tVehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getVehicleId());
        if (vehicleBasicPropertyModel == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }
        //判断车辆是否是自主或自营的
        if (!VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehicleBasicPropertyModel.getVehicleProperty())
                && !VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehicleBasicPropertyModel.getVehicleProperty())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_VEHICLE_ERROR);
        }
        //判断车辆是否是牵引车或一体车
        if (!VehicleCategoryEnum.TRACTOR.getKey().equals(vehicleBasicPropertyModel.getVehicleCategory())
                && !VehicleCategoryEnum.WHOLE.getKey().equals(vehicleBasicPropertyModel.getVehicleCategory())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_VEHICLE_ERROR);
        }
        //运营中的车辆
        if (!OperatingStateEnum.IN_OPERATION.getKey().equals(vehicleBasicPropertyModel.getOperatingState())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLES_ARE_SCRAP);
        }
        //查询确认单据
        CertificationPicturesFileTypeEnum fileTypeEnum = CertificationPicturesFileTypeEnum.T_DRIVER_APPOINT_CONFIRM_PICTURE_FILE;
        List<TCertificationPictures> tCertificationPicturesList = tCertificationPicturesMapper.getByObjectIdType(dbDriverAppoint.getId(), fileTypeEnum.getObjectType().getObjectType(), fileTypeEnum.getFileType());
        List<String> confirmPictureList = tCertificationPicturesList.stream().map(TCertificationPictures::getFilePath).collect(Collectors.toList());

        //关联车辆并更新状态
        TDriverAppoint driverAppoint = new TDriverAppoint();
        driverAppoint.setId(dbDriverAppoint.getId());
        driverAppoint.setVehicleId(vehicleBasicPropertyModel.getVehicleId());
        driverAppoint.setVehicleNo(vehicleBasicPropertyModel.getVehicleNo());
        driverAppoint.setIfAssociatedVehicle(CommonConstant.INTEGER_ONE);
        commonBiz.setBaseEntityModify(driverAppoint, BaseContextHandler.getUserName());
        tDriverAppointMapper.updateByPrimaryKeySelectiveEncrypt(driverAppoint);

        //生成运单（更新需求单状态）
        CreateCarrierOrderByAssociatedVehicleModel createCarrierOrderModel = MapperUtils.mapper(dbDriverAppoint, CreateCarrierOrderByAssociatedVehicleModel.class);
        createCarrierOrderModel.setDriverIdentity(tStaffBasic.getIdentityNumber());
        createCarrierOrderModel.setVehicleId(vehicleBasicPropertyModel.getVehicleId());
        createCarrierOrderModel.setVehicleNo(vehicleBasicPropertyModel.getVehicleNo());
        createCarrierOrderModel.setConfirmPictureList(confirmPictureList);
        driverAppointCommonBiz.createCarrierOrder(createCarrierOrderModel);
    }

    /**
     * 小程序-预约记录-列表
     * @param requestModel
     * @return
     */
    public SearchAppointCountResponseModel searchAppointList(SearchAppointRequestModel requestModel) {
        //获取当前登录的司机id
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        if (loginDriverAppletUserId == null || CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.DATA_ERROR_AND_RE_LOGIN);
        }
        SearchAppointCountResponseModel searchAppointCountResponseModel = tDriverAppointMapper.searchAppointCount(loginDriverAppletUserId,requestModel);
        requestModel.enablePaging();
        List<SearchAppointResponseModel> searchAppointResponseModels = tDriverAppointMapper.searchAppointList(loginDriverAppletUserId,requestModel);
        searchAppointCountResponseModel.setModelList(new PageInfo<>(searchAppointResponseModels));
        return searchAppointCountResponseModel;
    }
}
