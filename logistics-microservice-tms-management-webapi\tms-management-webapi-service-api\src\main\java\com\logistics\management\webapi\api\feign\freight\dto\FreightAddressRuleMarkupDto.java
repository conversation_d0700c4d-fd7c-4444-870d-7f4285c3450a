package com.logistics.management.webapi.api.feign.freight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 多装多卸
 * @Author: sj
 * @Date: 2019/12/24 13:15
 */
@Data
public class FreightAddressRuleMarkupDto {
    @ApiModelProperty("运价地址多装多卸加价ID")
    private String freightAddressRuleMarkupId;
    @ApiModelProperty("序列")
    private String markIndex;
    @ApiModelProperty("阶梯序列")
    private String ruleIndex;
    @ApiModelProperty("装货点数")
    private String loadAmount;
    @ApiModelProperty("卸货点数")
    private String unloadAmount;
    @ApiModelProperty("加价金额")
    private String markupFreightFee;
}
