package com.logistics.appapi.controller.website.entrust.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * @author: wjf
 * @date: 2019/10/31 16:06
 */
@Data
public class AddEntrustSourceRequestDto {

    @ApiModelProperty(value = "公司名称",required = true)
    @Pattern(regexp = "[\\u4E00-\\u9FA5A-Z]{2,20}",message = "请输入公司名称，2≤字符长度≤20")
    private String companyName;

    @ApiModelProperty(value = "联系人姓名",required = true)
    @Pattern(regexp = "[\\u4E00-\\u9FA5a-zA-Z]{2,20}",message = "请输入联系人姓名，2≤字符长度≤20")
    private String contactName;

    @ApiModelProperty(value = "联系人手机号",required = true)
    @Pattern(regexp = "[1]\\d{10}",message = "请输入联系人手机号")
    @Size(min = 11,max = 11,message = "请输入联系人手机号")
    private String contactMobile;

    @ApiModelProperty(value = "发货备注",required = true)
    @Length(min = 2, max = 90, message = "请输入发货备注，2≤字符长度≤90")
    private String remark;
}
