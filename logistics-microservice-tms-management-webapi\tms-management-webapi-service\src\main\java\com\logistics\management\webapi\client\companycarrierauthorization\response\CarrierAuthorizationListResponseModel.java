package com.logistics.management.webapi.client.companycarrierauthorization.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CarrierAuthorizationListResponseModel {

    @ApiModelProperty("车主授权信息id")
    private Long carrierAuthorizationId;

    @ApiModelProperty("车主名")
    private String companyCarrierName;

    @ApiModelProperty("车主公司类型, 1:企业 2:个人")
    private Integer companyCarrierType;

    @ApiModelProperty("是否归档, 0:否 1:是")
    private Integer isArchived;

    @ApiModelProperty("授权状态, 0:待授权 1:待审核 2:已驳回 3:已授权")
    private Integer authorizationStatus;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("创建时间")
    private Date createdTime;

    @ApiModelProperty("归档人")
    private String archivedUsername;

    @ApiModelProperty("归档时间")
    private Date archivedTime;
}
