package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleSettlementDriverRelation extends BaseEntity {
    /**
    * 车辆结算id
    */
    @ApiModelProperty("车辆结算id")
    private Long vehicleSettlementId;

    /**
    * 司机确认状态：-1 未操作，0 无需确认，1 确认，2 驳回
    */
    @ApiModelProperty("司机确认状态：-1 未操作，0 无需确认，1 确认，2 驳回")
    private Integer status;

    /**
    * 司机确认图片路径
    */
    @ApiModelProperty("司机确认图片路径")
    private String commitImageUrl;

    /**
    * 司机对账问题备注
    */
    @ApiModelProperty("司机对账问题备注")
    private String settlementReasonRemark;

    /**
    * 无需确认理由
    */
    @ApiModelProperty("无需确认理由")
    private String reason;

    /**
    * 确认时间
    */
    @ApiModelProperty("确认时间")
    private Date confirmTime;

    /**
    * 司机Id
    */
    @ApiModelProperty("司机Id")
    private Long driverId;

    /**
    * 司机名称
    */
    @ApiModelProperty("司机名称")
    private String driverName;

    /**
    * 司机手机号
    */
    @ApiModelProperty("司机手机号")
    private String driverMobile;
}