package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/1/9 14:07
 */
@Data
public class RejectRollbackDemandOrderResponseModel {
    @ApiModelProperty("需求单号")
    private String demandCode;

    @ApiModelProperty("失败原因")
    private String failReason="";

    @ApiModelProperty("是否成功：0 否，1 是")
    private Integer ifSuccess;

    @ApiModelProperty("委托单状态：500待发布 1000待调度 2000部分调度 3000调度完成 4000待签收 5000已签收 1取消 2放空 3回退")
    private Integer demandOrderStatus;
}
