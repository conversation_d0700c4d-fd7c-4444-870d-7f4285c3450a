<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TInvoicingSettleStatementMapper" >
  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TInvoicingSettleStatement" >
    <foreach collection="recordList" item="item" separator=";">
      insert into t_invoicing_settle_statement
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.invoicingId != null" >
          invoicing_id,
        </if>
        <if test="item.settleStatementId != null" >
          settle_statement_id,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.invoicingId != null" >
          #{item.invoicingId,jdbcType=BIGINT},
        </if>
        <if test="item.settleStatementId != null" >
          #{item.settleStatementId,jdbcType=BIGINT},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

  <select id="getReconciliationFeeByInvoicingIds" resultType="com.logistics.tms.biz.invoicingmanagement.model.GetReconciliationFeeModel">
    select
    tiss.invoicing_id as invoicingId,
    sum(tss.apply_total_fee) as reconciliationFee
    from t_invoicing_settle_statement tiss
    left join t_settle_statement tss on tss.id = tiss.settle_statement_id and tss.valid = 1
    where tiss.valid = 1
    and tiss.invoicing_id in
    <foreach collection="invoicingIds" item="invoicingId" separator="," open="(" close=")">
      #{invoicingId}
    </foreach>
    group by tiss.invoicing_id
  </select>

  <select id="getByInvoicingIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_invoicing_settle_statement
    where valid = 1
    and invoicing_id in
    <foreach collection="invoicingIds" item="invoicingId" separator="," open="(" close=")">
      #{invoicingId}
    </foreach>
  </select>

  <select id="getSettleStatementIdList" resultType="java.lang.Long">
    select distinct
    tiss.id
    from t_invoicing_settle_statement tiss
    left join t_settle_statement tss on tss.id = tiss.settle_statement_id and tss.valid = 1
    where tiss.valid = 1
    and tiss.invoicing_id = #{params.invoicingId,jdbcType=BIGINT}
    order by tss.settle_statement_code desc
  </select>

  <resultMap id="getSettleStatementList_Map" type="com.logistics.tms.controller.invoicingmanagement.response.GetSettleStatementListResponseModel">
    <id column="id" property="invoicingSettleStatementId" jdbcType="BIGINT"/>
    <result column="settleStatementId" property="settleStatementId" jdbcType="BIGINT"/>
    <result column="settle_statement_code" property="settleStatementCode" jdbcType="VARCHAR"/>
    <result column="settle_statement_month" property="settleStatementMonth" jdbcType="VARCHAR"/>
    <result column="settle_statement_name" property="settleStatementName" jdbcType="VARCHAR"/>
    <result column="freight_tax_point" property="freightTaxPoint" jdbcType="DECIMAL"/>
    <result column="other_fee_tax_point" property="otherFeeTaxPoint" jdbcType="DECIMAL"/>
    <result column="adjust_fee" property="adjustFee" jdbcType="DECIMAL"/>
    <result column="apply_total_fee" property="reconciliationFee" jdbcType="DECIMAL"/>
    <result column="contract_code" property="contractCode" jdbcType="VARCHAR"/>

    <collection property="itemList" ofType="com.logistics.tms.controller.invoicingmanagement.response.GetSettleStatementListItemResponseModel">
      <result column="settleStatementItemId" property="settleStatementItemId" jdbcType="BIGINT"/>
      <result column="entrust_freight" property="entrustFreight" jdbcType="DECIMAL"/>
      <result column="other_fees" property="otherFees" jdbcType="DECIMAL"/>
    </collection>
  </resultMap>
  <select id="getSettleStatementList" resultMap="getSettleStatementList_Map">
    select
    tiss.id,

    tss.id as settleStatementId,
    tss.settle_statement_code,
    tss.settle_statement_month,
    tss.settle_statement_name,
    tss.freight_tax_point,
    tss.other_fee_tax_point,
    tss.adjust_fee,
    tss.apply_total_fee,
    tss.contract_code,

    tssi.id as settleStatementItemId,
    tssi.entrust_freight,
    tssi.other_fees
    from t_invoicing_settle_statement tiss
    left join t_settle_statement tss on tss.id = tiss.settle_statement_id and tss.valid = 1
    left join t_settle_statement_item tssi on tiss.settle_statement_id = tssi.settle_statement_id and tssi.valid = 1
    where tiss.valid = 1
    and tiss.id in
    <foreach collection="ids" item="id" separator="," open="(" close=")">
      #{id}
    </foreach>
    order by tss.settle_statement_code desc
  </select>
</mapper>