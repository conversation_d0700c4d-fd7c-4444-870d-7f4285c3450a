package com.logistics.appapi.controller.carrierorderotherfee.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/9/2 11:21
 */
@Data
public class GetOtherFeeByCarrierOrderIdResponseDto {
    @ApiModelProperty("临时费用id")
    private String carrierOrderOtherFeeId="";

    @ApiModelProperty("临时费用")
    private List<GetOtherFeeItemByCarrierOrderIdResponseDto> otherFeeList;

    @ApiModelProperty("合计临时费用")
    private String totalAmount="";
}
