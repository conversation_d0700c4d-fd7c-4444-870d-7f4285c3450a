package com.logistics.management.webapi.api.feign.driversafemeeting.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/11/4 17:07
 */
@Data
public class ReplacementDriverSafeMeetingRequestDto {
    @ApiModelProperty(value = "学习例会id",required = true)
    @NotBlank(message = "id不能为空")
    private String safeMeetingId;
    @ApiModelProperty(value = "驾驶员",required = true)
    @NotEmpty(message = "请选择驾驶员")
    private List<String> driverIdList;
}
