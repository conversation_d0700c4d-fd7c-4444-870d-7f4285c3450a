package com.logistics.management.webapi.api.impl.insuarance.mapping;

import com.logistics.management.webapi.api.feign.insuarance.dto.SearchInsuranceListResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.tms.api.feign.insuarance.model.InsuranceSettlementCostsRelationModel;
import com.logistics.tms.api.feign.insuarance.model.SearchInsuranceListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.util.*;


/**
 * @author: wjf
 * @date: 2019/6/5 11:50
 */
public class SearchInsuranceListMapping extends MapperMapping<SearchInsuranceListResponseModel, SearchInsuranceListResponseDto> {

    @Override
    public void configure() {
        SearchInsuranceListResponseModel source = getSource();
        SearchInsuranceListResponseDto destination = getDestination();
        if (source != null) {
            Date now = new Date();
            destination.setVehiclePropertyLabel(VehiclePropertyEnum.getEnum(source.getVehicleProperty()).getValue());
            destination.setInsuranceTypeDesc(InsuranceCoverageEnum.getEnum(source.getInsuranceType()).getValue());
            destination.setPolicyNumberExport(source.getPolicyNumber());
            destination.setDriverNamePhone(source.getDriverName() + " " + source.getDriverPhone());
            destination.setSettlementStatusDesc(SettlementStatusEnum.getEnum(source.getSettlementStatus()).getValue());
            if (InsuranceCoverageEnum.PERSONAL_INSURANCE.getKey().equals(source.getInsuranceType())) {
                destination.setSettlementStatus(SettlementStatusEnum.NULL.getKey().toString());
                destination.setSettlementStatusDesc(SettlementStatusEnum.NULL.getValue());
                destination.setInsuranceCompanyName(source.getInsuranceCompanyNamePerson());
                destination.setPolicyNumberExport(source.getPolicyNumberPerson());
                if (StringUtils.isNotBlank(source.getBatchNumberPerson())) {
                    destination.setPolicyNumber(source.getBatchNumberPerson());
                    destination.setBatchNumberExport(source.getBatchNumberPerson());
                } else {
                    destination.setPolicyNumber(source.getPolicyNumberPerson());
                }
                if (source.getStartTimePerson() != null) {
                    destination.setStartTime(DateUtils.dateToString(source.getStartTimePerson(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
                }
                if (source.getEndTimePerson() != null) {
                    destination.setEndTime(DateUtils.dateToString(source.getEndTimePerson(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
                }
                if (source.getGrossPremium() != null && source.getGrossPremium().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                    destination.setPremium(ConverterUtils.toString(source.getGrossPremium().divide(ConverterUtils.toBigDecimal(source.getPolicyPersonCount()), 2, BigDecimal.ROUND_HALF_UP)));
                } else {
                    destination.setPremium(ConverterUtils.toString(source.getGrossPremiumPerson().divide(ConverterUtils.toBigDecimal(source.getPolicyPersonCountPerson()), 2, BigDecimal.ROUND_HALF_UP)));
                }
                if (source.getStatusType().equals(InsuranceStatusTypeEnum.CANCEL.getKey())) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.CANCEL.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.CANCEL.getValue());
                    destination.setRemark(source.getCancelReason());
                }else if (source.getStatusType().equals(InsuranceStatusTypeEnum.REFUND.getKey())) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.REFUND.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.REFUND.getValue());
                    destination.setRemark(source.getCancelReason());
                } else if (source.getStartTimePerson().getTime() > now.getTime()) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.NO_START.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.NO_START.getValue());
                } else if (source.getEndTimePerson().getTime() < now.getTime()) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.OVERDUE.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.OVERDUE.getValue());
                } else if (source.getStartTimePerson().getTime() <= now.getTime() && source.getEndTimePerson().getTime() >= now.getTime()) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.VALID.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.VALID.getValue());
                }
            } else {
                //修改保险状态
                if (source.getStartTime() != null && now.before(source.getStartTime())) {
                    destination.setSettlementStatus(SettlementStatusEnum.NULL.getKey().toString());
                    destination.setSettlementStatusDesc(SettlementStatusEnum.NULL.getValue());
                }
                if (source.getStatusType().equals(InsuranceStatusTypeEnum.CANCEL.getKey())) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.CANCEL.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.CANCEL.getValue());
                    destination.setRemark(source.getCancelReason());
                }else if (source.getStatusType().equals(InsuranceStatusTypeEnum.REFUND.getKey())) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.REFUND.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.REFUND.getValue());
                    destination.setRemark(source.getCancelReason());
                } else if (source.getStartTime().getTime() > now.getTime()) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.NO_START.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.NO_START.getValue());
                } else if (source.getEndTime().getTime() < now.getTime()) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.OVERDUE.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.OVERDUE.getValue());
                } else if (source.getStartTime().getTime() <= now.getTime() && source.getEndTime().getTime() >= now.getTime()) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.VALID.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.VALID.getValue());
                }
                if (source.getStartTime() != null) {
                    destination.setStartTime(DateUtils.dateToString(source.getStartTime(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
                }
                if (source.getEndTime() != null) {
                    destination.setEndTime(DateUtils.dateToString(source.getEndTime(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
                }
                //判断是否确认弹窗
                List<InsuranceSettlementCostsRelationModel> insuranceSettlementCostsList = source.getInsuranceSettlementCostsList();
                //只有部分结算，并且关联了保单才需要二次确认
                if (ListUtils.isNotEmpty(insuranceSettlementCostsList) && InsuranceSettlementStatusEnum.WAIT.getKey().equals(source.getSettlementStatus())) {
                    destination.setIfNeedConfirm(CommonConstant.ONE);
                    Set<String> settleMonths = new HashSet<>();
                    Set<Integer> insuranceTypes = new HashSet<>();
                    for (InsuranceSettlementCostsRelationModel item : insuranceSettlementCostsList) {
                        settleMonths.add(item.getInsuranceMonth());
                        insuranceTypes.add(item.getInsuranceType());
                    }
                    if (!settleMonths.isEmpty()) {
                        List<String> settleMonthsList = new ArrayList(settleMonths);
                        Collections.sort(settleMonthsList);
                        Collections.reverse(settleMonthsList);
                        destination.setInsuranceSettlementMonths(StringUtils.listToString(settleMonthsList, '，'));
                    }
                    if (!insuranceTypes.isEmpty()) {
                        List<String> insuranceTypeStr = new ArrayList<>();
                        for (Integer insuranceType : insuranceTypes) {
                            insuranceTypeStr.add(InsuranceEnum.getEnum(insuranceType).getValue());
                        }
                        destination.setInsuranceSettlementTypes(StringUtils.listToString(insuranceTypeStr,'，'));
                    }
                }

                //交强险合计费用 = 保费 + 车船税
                if(InsuranceCoverageEnum.COMPULSORY_INSURANCE.getKey().equals(source.getInsuranceType())){
                    BigDecimal premium =Optional.ofNullable(source.getPremium()).orElse(BigDecimal.ZERO);
                    BigDecimal vehicleAndVesselTax = Optional.ofNullable(source.getPaymentOfVehicleAndVesselTax()).orElse(BigDecimal.ZERO);
                    destination.setPremium(premium.add(vehicleAndVesselTax).toPlainString());
                }
            }
        }
    }
}
