package com.logistics.tms.biz.payment;



import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierOrderStatusEnum;
import com.logistics.tms.base.enums.PriceTypeEnum;
import com.logistics.tms.base.enums.SettlementTonnageEnum;
import com.logistics.tms.entity.TCarrierOrder;
import com.logistics.tms.entity.TPayment;
import com.logistics.tms.mapper.TPaymentMapper;
import com.yelo.tools.utils.ConverterUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date ：Created in 2023/2/23
 */
@Slf4j
@Service
public class PaymentBiz {


	public BigDecimal calculateCarrierOrderCarrierPrice(TCarrierOrder tCarrierOrder){
		if (PriceTypeEnum.FIXED_PRICE.getKey().equals(tCarrierOrder.getCarrierPriceType())) {
			return tCarrierOrder.getCarrierPrice();
		}else {
			BigDecimal resultAmount = BigDecimal.ZERO;
			if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfEmpty())){
				resultAmount = tCarrierOrder.getExpectAmount();
			}else{
				if (SettlementTonnageEnum.LOAD.getKey().equals(tCarrierOrder.getCarrierSettlement())) {
					resultAmount = tCarrierOrder.getLoadAmount();
				} else if (SettlementTonnageEnum.UNLOAD.getKey().equals(tCarrierOrder.getCarrierSettlement())) {
					resultAmount = tCarrierOrder.getUnloadAmount();
				} else if (SettlementTonnageEnum.SIGN.getKey().equals(tCarrierOrder.getCarrierSettlement())) {
					resultAmount = tCarrierOrder.getSignAmount();
				} else if (SettlementTonnageEnum.EXPECT.getKey().equals(tCarrierOrder.getCarrierSettlement())) {
					resultAmount = tCarrierOrder.getExpectAmount();
				}
			}
			return tCarrierOrder.getCarrierPrice().multiply(resultAmount);
		}
	}


//	public BigDecimal calculateCarrierPrice(TCarrierOrder tCarrierOrder, TPayment tPayment){
//
//		int carrierPriceType = tPayment == null ? tCarrierOrder.getCarrierPriceType() : tPayment.getPriceType();
//		BigDecimal carrierPrice = tPayment == null ? tCarrierOrder.getCarrierPrice() : tPayment.getSettlementCostTotal();
//
//
//		if (PriceTypeEnum.FIXED_PRICE.getKey().equals(carrierPriceType)) {
//			return carrierPrice;
//		}else {
//			BigDecimal resultAmount = BigDecimal.ZERO;
//			if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfEmpty())){
//				resultAmount = tCarrierOrder.getExpectAmount();
//			}else{
//				if (SettlementTonnageEnum.LOAD.getKey().equals(tCarrierOrder.getCarrierSettlement())) {
//					resultAmount = tCarrierOrder.getLoadAmount();
//				} else if (SettlementTonnageEnum.UNLOAD.getKey().equals(tCarrierOrder.getCarrierSettlement())) {
//					resultAmount = tCarrierOrder.getUnloadAmount();
//				} else if (SettlementTonnageEnum.SIGN.getKey().equals(tCarrierOrder.getCarrierSettlement())) {
//					resultAmount = tCarrierOrder.getSignAmount();
//				} else if (SettlementTonnageEnum.EXPECT.getKey().equals(tCarrierOrder.getCarrierSettlement())) {
//					resultAmount = tCarrierOrder.getExpectAmount();
//				}
//			}
//			return carrierPrice.multiply(resultAmount);
//		}
//	}

	/**
	 * 用运单详情的计算运单实际费用的逻辑 去计算运单的实际费用
	 */
	public BigDecimal calculateActualCarrierPriceLikeCarrierDetail(TCarrierOrder tCarrierOrder, TPayment tPayment) {
		//不是已签收和放空 就是0
		if (!CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(tCarrierOrder.getStatus()) && !CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfEmpty())) {
			return BigDecimal.ZERO;
		}

		//如果settlementCostTotal有值 就直接用
		if (tPayment != null && tPayment.getPriceType() != null && tPayment.getSettlementCostTotal() != null) {
			return tPayment.getSettlementCostTotal();
		}


		//有结算数据计算实际费用
		if (PriceTypeEnum.UNIT_PRICE.getKey().equals(tCarrierOrder.getCarrierPriceType())) {
			BigDecimal carrierSettlementAmount = BigDecimal.ZERO;
			if (SettlementTonnageEnum.LOAD.getKey().equals(tCarrierOrder.getCarrierSettlement())) {
				carrierSettlementAmount = tCarrierOrder.getLoadAmount();
			} else if (SettlementTonnageEnum.UNLOAD.getKey().equals(tCarrierOrder.getCarrierSettlement())) {
				carrierSettlementAmount = tCarrierOrder.getUnloadAmount();
			} else if (SettlementTonnageEnum.SIGN.getKey().equals(tCarrierOrder.getCarrierSettlement())) {
				carrierSettlementAmount = tCarrierOrder.getSignAmount();
			} else if (SettlementTonnageEnum.EXPECT.getKey().equals(tCarrierOrder.getCarrierSettlement())) {
				carrierSettlementAmount = tCarrierOrder.getExpectAmount();
			}
			//如果放空 就用预计数量
			if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfEmpty())) {
				carrierSettlementAmount = tCarrierOrder.getExpectAmount();
			}
			return tCarrierOrder.getCarrierPrice().multiply(carrierSettlementAmount);

		} else {
			return tCarrierOrder.getCarrierPrice();
		}

	}

}
