package com.logistics.tms.controller.website.entrust;

import com.logistics.tms.biz.website.entrust.EntrustSourceBiz;
import com.logistics.tms.controller.website.entrust.request.AddEntrustSourceRequestModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: wjf
 * @date: 2024/3/15 10:00
 */
@Api(value = "云途官网-货主")
@RestController
public class EntrustSourceController {

    @Resource
    private EntrustSourceBiz entrustSourceBiz;

    /**
     * 成为货主
     * @param requestModel
     * @return
     */
    @ApiOperation("成为货主")
    @PostMapping("/service/entrustSource/addEntrustSource")
    public Result addEntrustSource(@RequestBody AddEntrustSourceRequestModel requestModel) {
        entrustSourceBiz.addEntrustSource(requestModel);
        return Result.success(true);
    }
}
