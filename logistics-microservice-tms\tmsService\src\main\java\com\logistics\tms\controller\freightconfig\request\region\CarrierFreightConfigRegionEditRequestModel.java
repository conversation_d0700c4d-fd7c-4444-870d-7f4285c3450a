package com.logistics.tms.controller.freightconfig.request.region;

import com.logistics.tms.controller.freightconfig.request.CarrierFreightConfigRequestModel;
import com.logistics.tms.controller.freightconfig.request.ladder.CarrierFreightConfigLadderRequestModel;
import com.logistics.tms.controller.freightconfig.request.scheme.CarrierFreightConfigSchemeEditRequestModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigRegionEditRequestModel extends CarrierFreightConfigRequestModel {

    @ApiModelProperty(value = "新增方案集合")
    private List<CarrierFreightConfigRegionEditItemRequestModel> schemeList;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class CarrierFreightConfigRegionEditItemRequestModel extends CarrierFreightConfigSchemeEditRequestModel {

        @ApiModelProperty(value = "阶梯", required = true)
        private List<CarrierFreightConfigLadderRequestModel> ladderConfigList;
    }
}
