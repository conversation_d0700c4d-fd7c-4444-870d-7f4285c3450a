package com.logistics.management.webapi.controller.settlestatement.tradition;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.ConvertPageInfoUtil;
import com.logistics.management.webapi.client.settlestatement.tradition.CarrierTraditionStatementClient;
import com.logistics.management.webapi.client.settlestatement.tradition.request.*;
import com.logistics.management.webapi.client.settlestatement.tradition.response.*;
import com.logistics.management.webapi.controller.settlestatement.tradition.mapping.*;
import com.logistics.management.webapi.controller.settlestatement.tradition.request.*;
import com.logistics.management.webapi.controller.settlestatement.tradition.response.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 自营业务车主对账管理
 */
@Slf4j
@RestController
@Api(value = "自营业务车主对账管理", tags = "自营业务车主对账管理")
@RequestMapping(value = "/api/carrierStatementManage/tradition")
public class CarrierTraditionStatementController {

    @Resource
    private CarrierTraditionStatementClient carrierTraditionStatementClient;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private ConfigKeyConstant configKeyConstant;

    /*
    待对账单
    * */

    /**
     * 待对账运单列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "待对账运单列表", tags = "1.3.9")
    @PostMapping(value = "/waitSettleStatementList")
    public Result<PageInfo<TraditionWaitSettleStatementListResponseDto>> waitSettleStatementList(@RequestBody TraditionWaitSettleStatementListRequestDto requestDto) {
        TraditionWaitSettleStatementListRequestModel requestModel = MapperUtils.mapper(requestDto, TraditionWaitSettleStatementListRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        Result<PageInfo<TraditionWaitSettleStatementListResponseModel>> result = carrierTraditionStatementClient.waitSettleStatementList(requestModel);
        result.throwException();
        PageInfo pageInfo = result.getData();
        pageInfo.setList(MapperUtils.mapper(pageInfo.getList(), TraditionWaitSettleStatementListResponseDto.class, new TraditionWaitSettleStatementListMapping()));
        return Result.success(pageInfo);
    }

    /**
     * 待对账运单列表-导出
     * @param requestDto
     * @param response
     */
    @ApiOperation(value = "待对账运单列表-导出", tags = "1.3.9")
    @PostMapping(value = "/exportWaitSettleStatementList")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportWaitSettleStatementList(@RequestBody TraditionWaitSettleStatementListRequestDto requestDto, HttpServletResponse response) {
        TraditionWaitSettleStatementListRequestModel requestModel = MapperUtils.mapper(requestDto, TraditionWaitSettleStatementListRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<TraditionWaitSettleStatementListResponseModel>> result = carrierTraditionStatementClient.exportWaitSettleStatementList(requestModel);
        result.throwException();
        List<TraditionWaitSettleStatementListResponseDto> list = MapperUtils.mapper(result.getData().getList(), TraditionWaitSettleStatementListResponseDto.class, new TraditionWaitSettleStatementListMapping());
        String fileName = "车主自营业务待对账运单" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        try {
            YeloExcelUtils.doExportMoreThreadByOneSheet(response, list, false, TraditionWaitSettleStatementListResponseDto.class, fileName);
        } catch (Exception e) {
            return;
        }
    }

    /**
     * 生成对账单
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "生成对账单", tags = "1.3.9")
    @PostMapping(value = "/createSettleStatement")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> createSettleStatement(@RequestBody @Valid TraditionCreateSettleStatementRequestDto requestDto) {
        TraditionCreateSettleStatementRequestModel requestModel = MapperUtils.mapper(requestDto, TraditionCreateSettleStatementRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        return carrierTraditionStatementClient.createSettleStatement(requestModel);
    }

    /**
     * 查询车主税点
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "查询车主税点", tags = "1.3.9")
    @PostMapping(value = "/queryTaxPoint")
    public Result<TraditionCarrierTaxPointResponseDto> queryTaxPoint(@RequestBody @Valid TraditionCarrierTaxPointRequestDto requestDto) {
        TraditionCarrierTaxPointRequestModel requestModel = MapperUtils.mapper(requestDto, TraditionCarrierTaxPointRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        Result<TraditionCarrierTaxPointResponseModel> result = carrierTraditionStatementClient.queryTaxPoint(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), TraditionCarrierTaxPointResponseDto.class));
    }


    /*
    对账单管理
    * */
    /**
     * 对账单列表 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "对账单列表", tags = "1.3.9")
    @PostMapping(value = "/settleStatementList")
    public Result<PageInfo<CarrierTraditionStatementListResponseDto>> settleStatementList(@RequestBody CarrierTraditionStatementListRequestDto requestDto) {
        CarrierTraditionStatementListRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierTraditionStatementListRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        Result<PageInfo<CarrierTraditionStatementListResponseModel>> result = carrierTraditionStatementClient.settleStatementList(requestModel);
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<CarrierTraditionStatementListResponseDto> list = MapperUtils.mapper(pageInfo.getList(), CarrierTraditionStatementListResponseDto.class, new CarrierTraditionStatementListMapping());
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    /**
     * 对账单列表-导出
     * @param requestDto
     * @param response
     */
    @ApiOperation(value = "对账单列表-导出", tags = "1.3.9")
    @GetMapping(value = "/exportSettleStatementList")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportSettleStatementList(CarrierTraditionStatementListRequestDto requestDto, HttpServletResponse response) {
        CarrierTraditionStatementListRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierTraditionStatementListRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<CarrierTraditionStatementListResponseModel>> result = carrierTraditionStatementClient.settleStatementList(requestModel);
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<CarrierTraditionStatementListResponseDto> list = MapperUtils.mapper(pageInfo.getList(), CarrierTraditionStatementListResponseDto.class, new CarrierTraditionStatementListMapping());
        String fileName = "对账单列表" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        try {
            YeloExcelUtils.doExportMoreThreadByOneSheet(response, list, false, CarrierTraditionStatementListResponseDto.class, fileName);
        } catch (Exception e) {
            return;
        }
    }

    /**
     * 关联运单号
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "关联运单号", tags = "1.3.9")
    @PostMapping(value = "/associationCarrierOrder")
    public Result<TraditionAssociationCarrierOrderResponseDto> associationCarrierOrder(@RequestBody @Valid TraditionAssociationCarrierOrderRequestDto requestDto) {
        Result<TraditionAssociationCarrierOrderResponseModel> result = carrierTraditionStatementClient.associationCarrierOrder(MapperUtils.mapper(requestDto, TraditionAssociationCarrierOrderRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), TraditionAssociationCarrierOrderResponseDto.class, new TraditionAssociationCarrierOrderMapping()));
    }

    /**
     * 编辑对账月份
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "编辑对账月份", tags = "1.3.9")
    @PostMapping(value = "/modifySettleStatementMonth")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> modifySettleStatementMonth(@RequestBody @Valid ModifyTraditionStatementMonthRequestDto requestDto) {
        ModifyTraditionStatementMonthRequestModel requestModel = MapperUtils.mapper(requestDto, ModifyTraditionStatementMonthRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        return carrierTraditionStatementClient.modifySettleStatementMonth(requestModel);
    }

    /**
     * 编辑结算主体
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "编辑结算主体", tags = "1.3.9")
    @PostMapping(value = "/modifyPlatformCompany")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> modifyPlatformCompany(@RequestBody @Valid TraditionModifyPlatformCompanyRequestDto requestDto) {
        return carrierTraditionStatementClient.modifyPlatformCompany(MapperUtils.mapper(requestDto, TraditionModifyPlatformCompanyRequestModel.class));
    }

    /**
     * 修改对账单费点
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "修改对账单费点", tags = "1.3.9")
    @PostMapping(value = "/modifyTaxPoint")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> modifyTaxPoint(@RequestBody @Valid TraditionCarrierModifyTaxPointRequestDto requestDto) {
        return carrierTraditionStatementClient.modifyTaxPoint(MapperUtils.mapper(requestDto, TraditionCarrierModifyTaxPointRequestModel.class));
    }

    /**
     * 差异调整-回显
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "差异调整-回显", tags = "1.3.9")
    @PostMapping(value = "/queryAdjustCost")
    public Result<TraditionCarrierAdjustCostResponseDto> queryAdjustCost(@RequestBody @Valid TraditionCarrierQueryAdjustCostRequestDto requestDto) {
        Result<TraditionCarrierAdjustCostResponseModel> result = carrierTraditionStatementClient.queryAdjustCost(MapperUtils.mapper(requestDto, TraditionCarrierQueryAdjustCostRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), TraditionCarrierAdjustCostResponseDto.class, new TraditionCarrierQueryAdjustCostMapping()));
    }

    /**
     * 差异调整-发起
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "差异调整-发起", tags = "1.3.9")
    @PostMapping(value = "/AdjustCost")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> adjustCost(@RequestBody @Valid TraditionCarrierAdjustRequestDto requestDto) {
        Result<Boolean> result = carrierTraditionStatementClient.adjustCost(MapperUtils.mapper(requestDto, TraditionCarrierAdjustRequestModel.class));
        result.throwException();
        return result;
    }

    /**
     * 申请开票 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "申请开票")
    @PostMapping(value = "/applyInvoicing")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> applyInvoicing(@RequestBody @Valid TraditionSettleStatementApplyInvoicingRequestDto requestDto) {
        return carrierTraditionStatementClient.applyInvoicing(MapperUtils.mapper(requestDto, TraditionSettleStatementApplyInvoicingRequestModel.class));
    }

    /**
     * 修改对账单名
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "修改对账单名", tags = "1.3.9")
    @PostMapping(value = "/renameStatement")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> renameStatement(@RequestBody @Valid EditTraditionStatementNameRequestDto requestDto) {
        EditTraditionStatementNameRequestModel requestModel = MapperUtils.mapper(requestDto, EditTraditionStatementNameRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        Result<Boolean> result = carrierTraditionStatementClient.renameStatement(requestModel);
        result.throwException();
        return result;
    }

    /**
     * 撤销对账单
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "撤销对账单", tags = "1.3.9")
    @PostMapping(value = "/cancel")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> cancel(@RequestBody @Valid TraditionStatementCancelRequestDto requestDto) {
        TraditionStatementCancelRequestModel requestModel = MapperUtils.mapper(requestDto, TraditionStatementCancelRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        Result<Boolean> result = carrierTraditionStatementClient.cancel(requestModel);
        result.throwException();
        return result;
    }

    /**
     * 对账单归档列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "对账单归档列表", tags = "1.3.9")
    @PostMapping(value = "/statementArchiveList")
    public Result<PageInfo<TraditionStatementArchiveListResponseDto>> statementArchiveList(@RequestBody @Valid TraditionStatementArchiveListRequestDto requestDto) {
        Result<PageInfo<TraditionStatementArchiveListResponseModel>> result = carrierTraditionStatementClient.statementArchiveList(MapperUtils.mapper(requestDto, TraditionStatementArchiveListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<TraditionStatementArchiveListResponseDto> list = MapperUtils.mapper(pageInfo.getList(), TraditionStatementArchiveListResponseDto.class);
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    /**
     * 对账单归档/编辑
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "对账单归档/编辑", tags = "1.3.9")
    @PostMapping(value = "/statementArchive")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> statementArchive(@RequestBody @Valid TraditionStatementArchiveRequestDto requestDto) {
        Result<Boolean> result = carrierTraditionStatementClient.statementArchive(MapperUtils.mapper(requestDto, TraditionStatementArchiveRequestModel.class));
        result.throwException();
        return result;
    }

    /**
     * 查看归档图片
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "查看归档图片", tags = "1.3.9")
    @PostMapping(value = "/archiveTicketList")
    public Result<List<String>> archiveTicketList(@RequestBody @Valid TraditionStatementArchiveTicketListRequestDto requestDto) {
        Result<List<String>> result = carrierTraditionStatementClient.archiveTicketList(MapperUtils.mapper(requestDto, TraditionStatementArchiveTicketListRequestModel.class));
        result.throwException();
        List<String> list = new ArrayList<>();
        if (ListUtils.isNotEmpty(result.getData())) {
            Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(result.getData());
            for (String path : result.getData()) {
                list.add(configKeyConstant.fileAccessAddress + imageMap.get(path));
            }
        }
        return Result.success(list);
    }

    /**
     * 查看归档详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "查看归档详情", tags = "1.3.9")
    @PostMapping(value = "/statementArchiveDetail")
    public Result<TraditionStatementArchiveDetailResponseDto> statementArchiveDetail(@RequestBody @Valid TraditionStatementArchiveDetailRequestDto requestDto) {
        Result<TraditionStatementArchiveDetailResponseModel> result = carrierTraditionStatementClient.statementArchiveDetail(MapperUtils.mapper(requestDto, TraditionStatementArchiveDetailRequestModel.class));
        result.throwException();
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(result.getData().getArchiveTicketList());
        return Result.success(MapperUtils.mapper(result.getData(), TraditionStatementArchiveDetailResponseDto.class, new TraditionStatementArchiveDetailMapping(configKeyConstant.fileAccessAddress, imageMap)));
    }

    /**
     * 查询对账单下待归档运单
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "查询对账单下待归档运单", tags = "1.3.9")
    @PostMapping(value = "/statementWaitArchiveList")
    public Result<List<TraditionStatementWaitArchiveListResponseDto>> statementWaitArchiveList(@RequestBody @Valid TraditionStatementWaitArchiveListRequestDto requestDto) {
        Result<List<TraditionStatementWaitArchiveListResponseModel>> result = carrierTraditionStatementClient.statementWaitArchiveList(MapperUtils.mapper(requestDto, TraditionStatementWaitArchiveListRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), TraditionStatementWaitArchiveListResponseDto.class));
    }

    /**
     * 对账单详情-提交
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "对账单详情-提交", tags = "1.3.9")
    @PostMapping(value = "/applicationCheck")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> submitSettleStatement(@RequestBody @Valid CarrierTraditionStatementIdRequestDto requestDto) {
        CarrierTraditionStatementIdRequestModel requestModel =
                MapperUtils.mapper(requestDto, CarrierTraditionStatementIdRequestModel.class);
        return carrierTraditionStatementClient.submitSettleStatement(requestModel);
    }

    /**
     * 对账单详情-审核/驳回
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "对账单详情-审核/驳回", tags = "1.3.9")
    @PostMapping(value = "/auditOrReject")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> auditOrReject(@RequestBody @Valid ChangeTraditionStatementStatsRequestDto requestDto) {
        if (CommonConstant.TWO.equalsIgnoreCase(requestDto.getSettleStatementStatus()) && StringUtils.isEmpty(requestDto.getRemark())) {
            //驳回时备注不能为空
            throw new BizException(ManagementWebApiExceptionEnum.REMARK_EMPTY);
        }
        return carrierTraditionStatementClient.auditOrReject(MapperUtils.mapper(requestDto, ChangeTraditionStatementStatsRequestModel.class));
    }

    /**
     * 对账单详情-合计
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "对账单详情-合计", tags = "1.3.9")
    @PostMapping(value = "/settleStatementDetailTotal")
    public Result<CarrierTraditionStatementDetailTotalResponseDto> settleStatementDetailTotal(@RequestBody @Valid CarrierTraditionStatementIdRequestDto requestDto) {
        CarrierTraditionStatementIdRequestModel requestModel =
                MapperUtils.mapper(requestDto, CarrierTraditionStatementIdRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);

        Result<CarrierTraditionStatementDetailTotalResponseModel> result =
                carrierTraditionStatementClient.settleStatementDetailTotal(requestModel);
        result.throwException();

        return Result.success(MapperUtils.mapper(result.getData(), CarrierTraditionStatementDetailTotalResponseDto.class,
                new CarrierTraditionStatementDetailTotalMapping()));
    }

    /**
     * 对账单详情-列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "对账单详情-列表", tags = "1.3.9")
    @PostMapping(value = "/settleStatementDetailList")
    public Result<PageInfo<CarrierTraditionStatementDetailListResponseDto>> settleStatementDetailList(@RequestBody @Valid CarrierTraditionStatementDetailListRequestDto requestDto) {
        CarrierTraditionStatementDetailListRequestModel requestModel =
                MapperUtils.mapper(requestDto, CarrierTraditionStatementDetailListRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);

        Result<PageInfo<CarrierTraditionStatementDetailListResponseModel>> result =
                carrierTraditionStatementClient.settleStatementDetailList(requestModel);
        result.throwException();

        PageInfo<CarrierTraditionStatementDetailListResponseDto> pageInfo =
                ConvertPageInfoUtil.convertPageInfo(result.getData(), CarrierTraditionStatementDetailListResponseDto.class, new TraditionStatementDetailListMapping());
        return Result.success(pageInfo);
    }

    /**
     * 对账单详情-导出对账单明细
     * @param requestDto
     * @param response
     */
    @ApiOperation(value = "对账单详情-导出对账单明细", tags = "1.3.9")
    @PostMapping(value = "/exportSettleStatementDetailList")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportSettleStatementDetailList(@RequestBody CarrierTraditionStatementDetailListRequestDto requestDto, HttpServletResponse response) {
        CarrierTraditionStatementDetailListRequestModel requestModel =
                MapperUtils.mapper(requestDto, CarrierTraditionStatementDetailListRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);

        Result<PageInfo<CarrierTraditionStatementDetailListResponseModel>> result =
                carrierTraditionStatementClient.exportSettleStatementDetailList(requestModel);
        result.throwException();

        List<CarrierTraditionStatementDetailListResponseModel> list = Optional.ofNullable(result.getData())
                .map(PageInfo::getList)
                .orElse(Collections.emptyList());
        List<CarrierTraditionStatementDetailListResponseDto> exportList =
                MapperUtils.mapper(list, CarrierTraditionStatementDetailListResponseDto.class, new TraditionStatementDetailListMapping());
        String fileName = "对账单运单明细" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        try {
            YeloExcelUtils.doExportMoreThreadByOneSheet(response, exportList, false, CarrierTraditionStatementDetailListResponseDto.class, fileName);
        } catch (Exception e) {
            return;
        }
    }

    /**
     * 对账单详情-导出结算数据
     * @param requestDto
     * @param response
     * @param request
     */
    @ApiOperation(value = "对账单详情-导出结算数据", tags = "1.3.9")
    @PostMapping(value = "/exportSettleStatementDetail")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportSettleStatementDetail(@RequestBody @Valid ExportTraditionStatementDetailRequestDto requestDto, HttpServletResponse response, HttpServletRequest request) {
        //合计
        CarrierTraditionStatementIdRequestModel detailRequestModel =
                MapperUtils.mapper(requestDto, CarrierTraditionStatementIdRequestModel.class);
        detailRequestModel.setSource(CommonConstant.INTEGER_ONE);

        Result<CarrierTraditionStatementDetailTotalResponseModel> detailResult =
                carrierTraditionStatementClient.settleStatementDetailTotal(detailRequestModel);
        detailResult.throwException();

        CarrierTraditionStatementDetailTotalResponseDto detail =
                MapperUtils.mapper(detailResult.getData(), CarrierTraditionStatementDetailTotalResponseDto.class,
                new CarrierTraditionStatementDetailTotalMapping());

        //运单列表
        CarrierTraditionStatementDetailListRequestModel detailListRequestModel = new CarrierTraditionStatementDetailListRequestModel();
        detailListRequestModel.setSettleStatementId(ConverterUtils.toLong(requestDto.getSettleStatementId()));
        detailListRequestModel.setSource(CommonConstant.INTEGER_ONE);

        Result<PageInfo<CarrierTraditionStatementDetailListResponseModel>> carrierOrderListResult =
                carrierTraditionStatementClient.exportSettleStatementDetailList(detailListRequestModel);
        carrierOrderListResult.throwException();

        PageInfo<CarrierTraditionStatementDetailListResponseModel> pageInfo = carrierOrderListResult.getData();
        List<CarrierTraditionStatementDetailListResponseDto> carrierOrderDtoList =
                MapperUtils.mapper(pageInfo.getList(), CarrierTraditionStatementDetailListResponseDto.class, new TraditionStatementDetailListMapping());

        String responseFileName = URLEncoder.encode("车主结算数据", StandardCharsets.UTF_8) + ".xlsx";
        String templateFileName = "/template/carrier_settlement_for_traditional_template.xlsx";
        try (InputStream inputStream = CarrierTraditionStatementController.class.getResourceAsStream(templateFileName);
             ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            //读取excel模板
            ExcelWriter excelWriter = EasyExcel.write(bos).withTemplate(inputStream).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            // 填充list
            excelWriter.fill(carrierOrderDtoList, fillConfig, writeSheet);

            //填充头
            Map<String, Object> map = new HashMap<>();
            map.put("companyCarrierName", detail.getCompanyCarrierName());
            map.put("settleStatementCode", detail.getSettleStatementCode());
            map.put("date", detail.getCreatedTime());
            map.put("settleStatementMonth", detail.getSettleStatementMonth());
            map.put("settlementAmountTotal", detail.getSettlementAmountTotal());
            map.put("platformCompanyName", detail.getPlatformCompanyName());
            map.put("carrierFreightTotal", detail.getCarrierFreightTotal());
            map.put("otherFeeTotal", CommonConstant.BIG_DECIMAL_ZERO_NET_ZERO.toPlainString());
            map.put("adjustFee", detail.getAdjustFee());
            map.put("reconciliationFee", detail.getReconciliationFee());
            map.put("contractCode", detail.getContractCode());
            excelWriter.fill(map, writeSheet);
            excelWriter.finish();

            //写出文件
            DownloadUtils.downloadFile(bos.toByteArray(), responseFileName, response,request);
        } catch (Exception e) {
            log.warn("导出对账单结算数据失败: ", e);
        }
    }

    /**
     * 对账单详情-查询添加运单列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "对账单详情-查询添加运单列表", tags = "1.3.9")
    @PostMapping(value = "/addCarrierOrderList")
    public Result<PageInfo<TraditionAddCarrierOrderListResponseDto>> addCarrierOrderList(@RequestBody TraditionAddCarrierOrderListRequestDto requestDto) {
        TraditionAddCarrierOrderListRequestModel requestModel =
                MapperUtils.mapper(requestDto, TraditionAddCarrierOrderListRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        Result<PageInfo<TraditionWaitSettleStatementListResponseModel>> result = carrierTraditionStatementClient.addCarrierOrderList(requestModel);
        result.throwException();
        PageInfo<TraditionAddCarrierOrderListResponseDto> pageInfo =
                ConvertPageInfoUtil.convertPageInfo(result.getData(), TraditionAddCarrierOrderListResponseDto.class, new TraditionAddCarrierOrderListMapping());
        return Result.success(pageInfo);
    }

    /**
     * 对账单详情-添加运单
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "对账单详情-添加运单", tags = "1.3.9")
    @PostMapping(value = "/addCarrierOrder")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addCarrierOrderConfirm(@RequestBody @Valid TraditionAddCarrierOrderConfirmRequestDto requestDto) {
        TraditionAddCarrierOrderConfirmRequestModel requestModel =
                MapperUtils.mapper(requestDto, TraditionAddCarrierOrderConfirmRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        return carrierTraditionStatementClient.addCarrierOrderConfirm(requestModel);
    }

    /**
     * 对账单详情-撤销运单
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "对账单详情-撤销运单", tags = "1.3.9")
    @PostMapping(value = "/cancelCarrierOrder")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> cancelCarrierOrder(@RequestBody @Valid TraditionUndoCarrierOrderRequestDto requestDto) {
        TraditionUndoCarrierOrderRequestModel requestModel =
                MapperUtils.mapper(requestDto, TraditionUndoCarrierOrderRequestModel.class);
        return carrierTraditionStatementClient.cancelCarrierOrder(requestModel);
    }
}
