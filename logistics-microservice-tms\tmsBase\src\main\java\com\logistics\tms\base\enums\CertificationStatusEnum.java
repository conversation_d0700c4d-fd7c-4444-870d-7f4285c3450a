package com.logistics.tms.base.enums;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/10
 */
public enum CertificationStatusEnum {

	WAIT_AUTH(0, "待认证"),
	IN_AUTH(1, "认证中"),
	AUTH_SUCCESS(2, "认证成功"),
	AUTH_FAIL(3, "认证失败"),
	;

	private final Integer key;
	private final String value;

	CertificationStatusEnum(Integer key, String value) {
		this.key = key;
		this.value = value;
	}

	public Integer getKey() {
		return key;
	}

	public String getValue() {
		return value;
	}
}
