package com.logistics.tms.biz.insuarance;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.insuarance.model.*;
import com.logistics.tms.api.feign.personalaccidentinsurance.model.GetPersonInsurancePersonCountByIdResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel;
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleSettlementRelationByInsuranceIdsBaseModel;
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleSettlementRelationByInsuranceIdsModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2019/6/4 19:42
 */
@Service
public class InsuranceBiz {

    @Autowired
    private TInsuranceMapper insuranceMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TCertificationPicturesMapper certificationPicturesMapper;
    @Autowired
    private TInsuranceCompanyMapper insuranceCompanyMapper;
    @Autowired
    private TStaffBasicMapper staffBasicMapper;
    @Autowired
    private TVehicleBasicMapper vehicleBasicMapper;
    @Autowired
    private TPersonalAccidentInsuranceMapper personalAccidentInsuranceMapper;
    @Autowired
    private TInsuranceCostsRelationMapper insuranceCostsRelationMapper;
    @Autowired
    private TInsuranceCostsMapper insuranceCostsMapper;
    @Autowired
    private TVehicleSettlementMapper tVehicleSettlementMapper;
    @Autowired
    private TVehicleSettlementRelationMapper tVehicleSettlementRelationMapper;
    @Autowired
    private TInsuranceRefundMapper insuranceRefundMapper;

    /**
     * 保险管理列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo searchInsuranceList(SearchInsuranceListRequestModel requestModel) {
        requestModel.enablePaging();
        List<SearchInsuranceListResponseModel> list = insuranceMapper.searchInsuranceList(requestModel);
        if (ListUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        //列表需要查询关联的保险费用,导出不需要
        if (!CommonConstant.INTEGER_ONE.equals(requestModel.getIfExport())) {
            List<Long> waitSettleInsuranceIds = new ArrayList<>();
            for (SearchInsuranceListResponseModel item : list) {
                if (!InsuranceEnum.PERSONAL.getKey().equals(item.getInsuranceType())) {
                    waitSettleInsuranceIds.add(item.getInsuranceId());
                }
            }
            if (ListUtils.isNotEmpty(waitSettleInsuranceIds)) {
                List<InsuranceSettlementCostsRelationListModel> settlementCostsRelation = insuranceMapper.getSettlementCostsRelation(StringUtils.listToString(waitSettleInsuranceIds, ','));
                if (ListUtils.isNotEmpty(settlementCostsRelation)) {
                    Map<Long, InsuranceSettlementCostsRelationListModel> insuranceSettlementCostsRelationListModelMap = new HashMap<>();
                    for (InsuranceSettlementCostsRelationListModel item : settlementCostsRelation) {
                        insuranceSettlementCostsRelationListModelMap.put(item.getInsuranceId(), item);
                    }
                    //查询保险是否关联了保险费用
                    Map<Long, List<GetVehicleSettlementRelationByInsuranceIdsBaseModel>> insuranceIdsModelMap = new HashMap<>();
                    List<GetVehicleSettlementRelationByInsuranceIdsModel> tVehicleSettlementRelationMapperByInsuranceIds = tVehicleSettlementRelationMapper.getByInsuranceIds(StringUtils.listToString(waitSettleInsuranceIds, ','));
                    if (ListUtils.isNotEmpty(tVehicleSettlementRelationMapperByInsuranceIds)) {
                        for (GetVehicleSettlementRelationByInsuranceIdsModel insuranceIdsModel : tVehicleSettlementRelationMapperByInsuranceIds) {
                            insuranceIdsModelMap.put(insuranceIdsModel.getInsuranceId(), insuranceIdsModel.getVehicleSettlementRelationList());
                        }
                    }
                    for (SearchInsuranceListResponseModel item : list) {
                        InsuranceSettlementCostsRelationListModel insuranceSettlementCostsRelationListModel = insuranceSettlementCostsRelationListModelMap.get(item.getInsuranceId());
                        List<GetVehicleSettlementRelationByInsuranceIdsBaseModel> getVehicleSettlementRelationByInsuranceIdsBaseModels = insuranceIdsModelMap.get(item.getInsuranceId());
                        //自由当前保险关联的保险费用，但是没有没有产生结算数据，才弹窗显示
                        if (insuranceSettlementCostsRelationListModel != null && ListUtils.isEmpty(getVehicleSettlementRelationByInsuranceIdsBaseModels)) {
                            item.setInsuranceSettlementCostsList(insuranceSettlementCostsRelationListModel.getInsuranceSettlementCostsRelationList());
                        }
                    }
                }
            }
        }
        PageInfo pageInfo = new PageInfo(list);
        pageInfo.setSize(insuranceMapper.getAllCount());
        return pageInfo;
    }

    /**
     * 保险详情
     *
     * @param requestModel
     * @return
     */
    public GetInsuranceDetailResponseModel getInsuranceDetail(InsuranceIdRequestModel requestModel) {
        GetInsuranceDetailResponseModel responseModel = insuranceMapper.getInsuranceDetail(requestModel.getInsuranceId());
        if (responseModel == null) {
            throw new BizException(CarrierDataExceptionEnum.INSURANCE_EMPTY);
        }
        List<GetVehicleSettlementRelationByInsuranceIdsModel> vehicleSettlementRelations = tVehicleSettlementRelationMapper.getByInsuranceIds(responseModel.getInsuranceId().toString());
        if (ListUtils.isNotEmpty(vehicleSettlementRelations)) {
            responseModel.setIfSettlement(CommonConstant.INTEGER_ONE);
        }
        if (InsuranceStatusTypeEnum.REFUND.getKey().equals(responseModel.getStatusType())){
            TInsuranceRefund tInsuranceRefund = insuranceRefundMapper.getByInsuranceIdType(responseModel.getInsuranceId(),responseModel.getInsuranceType());
            if (tInsuranceRefund != null){
                responseModel.setRefundPath(tInsuranceRefund.getRefundPath());
            }
        }
        return responseModel;
    }

    /**
     * 新增/修改保险
     *
     * @param requestModel
     */
    @Transactional
    public void addOrModifyInsurance(AddOrModifyInsuranceRequestModel requestModel) {
        if (!requestModel.getInsuranceType().equals(CertificationPicturesFileTypeEnum.PERSONAL_INSURANCE.getFileType()) && requestModel.getStartTime().getTime() > requestModel.getEndTime().getTime()) {
            throw new BizException(CarrierDataExceptionEnum.START_TIME_DA_YU_END_TIME);
        }
        Date now = new Date();
        //截止日期不能少于当前时间
        if (requestModel.getEndTime() != null && requestModel.getEndTime().before(now)) {
            throw new BizException(CarrierDataExceptionEnum.INSURANCE_END_TIME_NOT_ALLOW_BEFORE_NOW);
        }
        //根据车辆ID查询车辆信息
        VehicleBasicPropertyModel vehicleBasic = vehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getVehicleId());
        if (vehicleBasic == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }

        TInsurance insurance = new TInsurance();
        insurance.setDriverId(requestModel.getDriverId());
        insurance.setInsuranceCompanyId(requestModel.getInsuranceCompanyId());
        insurance.setRemark(requestModel.getRemark());
        if (requestModel.getPaymentOfVehicleAndVesselTax() == null) {
            insurance.setPaymentOfVehicleAndVesselTax(CommonConstant.BIG_DECIMAL_ZERO);
        } else {
            insurance.setPaymentOfVehicleAndVesselTax(requestModel.getPaymentOfVehicleAndVesselTax());
        }
        insurance.setPersonalAccidentInsuranceId(requestModel.getPersonalAccidentInsuranceId());
        if (requestModel.getRelatedPersonalAccidentInsuranceId() == null || requestModel.getRelatedPersonalAccidentInsuranceId().equals(CommonConstant.LONG_ZERO)) {
            insurance.setRelatedPersonalAccidentInsuranceId(CommonConstant.LONG_ZERO);
        } else {
            insurance.setRelatedPersonalAccidentInsuranceId(requestModel.getRelatedPersonalAccidentInsuranceId());
        }
        String userName = BaseContextHandler.getUserName();
        Long insuranceId = requestModel.getInsuranceId();
        List<TInsurance> insuranceList;
        GetPersonInsurancePersonCountByIdResponseModel getPersonInsurancePersonCountByIdOne = null;
        GetPersonInsurancePersonCountByIdResponseModel getPersonInsurancePersonCountByIdTwo = null;
        if (requestModel.getInsuranceType().equals(CertificationPicturesFileTypeEnum.PERSONAL_INSURANCE.getFileType())) {
            VerifyInsuranceUniquenessRequestModel verifyInsuranceUniquenessRequestModel = new VerifyInsuranceUniquenessRequestModel();
            verifyInsuranceUniquenessRequestModel.setInsuranceType(requestModel.getInsuranceType());
            verifyInsuranceUniquenessRequestModel.setDriverId(requestModel.getDriverId());
            verifyInsuranceUniquenessRequestModel.setPersonalAccidentInsuranceId(requestModel.getPersonalAccidentInsuranceId());
            verifyInsuranceUniquenessRequestModel.setRelatedPersonalAccidentInsuranceId(requestModel.getRelatedPersonalAccidentInsuranceId());
            insuranceList = insuranceMapper.getByTypeVehicleIdOrDriverId(verifyInsuranceUniquenessRequestModel);
            getPersonInsurancePersonCountByIdOne = personalAccidentInsuranceMapper.getPersonInsurancePersonCountById(requestModel.getPersonalAccidentInsuranceId());
            if (requestModel.getRelatedPersonalAccidentInsuranceId() != null && !requestModel.getRelatedPersonalAccidentInsuranceId().equals(CommonConstant.LONG_ZERO)) {
                getPersonInsurancePersonCountByIdTwo = personalAccidentInsuranceMapper.getPersonInsurancePersonCountById(requestModel.getRelatedPersonalAccidentInsuranceId());
            }
        } else {
            VerifyInsuranceUniquenessRequestModel verifyInsuranceUniquenessRequestModel = new VerifyInsuranceUniquenessRequestModel();
            verifyInsuranceUniquenessRequestModel.setInsuranceType(requestModel.getInsuranceType());
            verifyInsuranceUniquenessRequestModel.setVehicleId(requestModel.getVehicleId());
            verifyInsuranceUniquenessRequestModel.setStartTime(DateUtils.dateToString(requestModel.getStartTime(), CommonConstant.DATE_TO_STRING_DETAIAL_PATTERN));
            verifyInsuranceUniquenessRequestModel.setEndTime(DateUtils.dateToString(requestModel.getEndTime(), CommonConstant.DATE_TO_STRING_DETAIAL_PATTERN));
            insuranceList = insuranceMapper.getByTypeVehicleIdOrDriverId(verifyInsuranceUniquenessRequestModel);
        }

        //修改保险
        if (requestModel.getInsuranceId() != null && requestModel.getInsuranceId() > CommonConstant.LONG_ZERO) {
            //判断车辆机构,如果未修改车辆则不判断车辆机构
            if (!vehicleBasic.getVehicleId().equals(requestModel.getVehicleId()) &&
                    !(VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehicleBasic.getVehicleProperty()) || VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehicleBasic.getVehicleProperty()))) {
                throw new BizException(CarrierDataExceptionEnum.INTERNAL_VEHICLE_ERROR);
            }

            if (ListUtils.isNotEmpty(insuranceList) && (ListUtils.isNotEmpty(insuranceList) && (insuranceList.size() > 1 || !insuranceList.get(0).getId().equals(requestModel.getInsuranceId())))) {
                throw new BizException(CarrierDataExceptionEnum.INSURANCE_EXIST);
            }
            TInsurance tqInsurance = insuranceMapper.selectByPrimaryKey(requestModel.getInsuranceId());
            if (tqInsurance == null || IfValidEnum.INVALID.getKey().equals(tqInsurance.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.INSURANCE_EMPTY);
            }
            if (!SettlementStatusEnum.WAIT.getKey().equals(tqInsurance.getSettlementStatus())) {
                throw new BizException(CarrierDataExceptionEnum.SETTLEMENT_STATUS_ERROR_NOT_UPDATE);
            }
            String policyNumber = "";
            if (getPersonInsurancePersonCountByIdOne != null && !getPersonInsurancePersonCountByIdOne.getPersonalAccidentInsuranceId().equals(tqInsurance.getPersonalAccidentInsuranceId())
                    && getPersonInsurancePersonCountByIdOne.getPolicyPersonCount().equals(getPersonInsurancePersonCountByIdOne.getAssociatedCount())) {
                if (StringUtils.isNotBlank(getPersonInsurancePersonCountByIdOne.getBatchNumber())) {
                    policyNumber = getPersonInsurancePersonCountByIdOne.getBatchNumber();
                } else {
                    policyNumber = getPersonInsurancePersonCountByIdOne.getPolicyNumber();
                }
                throw new BizException(CarrierDataExceptionEnum.PERSON_ACCIDENT_INSURANCE_COUNT_MAX.getCode(), policyNumber + CarrierDataExceptionEnum.PERSON_ACCIDENT_INSURANCE_COUNT_MAX.getMsg());
            }
            if (getPersonInsurancePersonCountByIdTwo != null && !getPersonInsurancePersonCountByIdTwo.getPersonalAccidentInsuranceId().equals(tqInsurance.getRelatedPersonalAccidentInsuranceId())
                    && getPersonInsurancePersonCountByIdTwo.getPolicyPersonCount().equals(getPersonInsurancePersonCountByIdTwo.getAssociatedCount())) {
                if (StringUtils.isNotBlank(getPersonInsurancePersonCountByIdTwo.getBatchNumber())) {
                    policyNumber = getPersonInsurancePersonCountByIdTwo.getBatchNumber();
                } else {
                    policyNumber = getPersonInsurancePersonCountByIdTwo.getPolicyNumber();
                }
                throw new BizException(CarrierDataExceptionEnum.PERSON_ACCIDENT_INSURANCE_COUNT_MAX.getCode(), policyNumber + CarrierDataExceptionEnum.PERSON_ACCIDENT_INSURANCE_COUNT_MAX.getMsg());
            }
            boolean ifSettlement = false;
            List<TInsuranceCostsRelation> tInsuranceCostsRelationList = insuranceCostsRelationMapper.getByInsuranceId(tqInsurance.getId());
            if (ListUtils.isNotEmpty(tInsuranceCostsRelationList)) {
                Set<Long> insuranceCostsIds = new HashSet<>();
                for (TInsuranceCostsRelation tInsuranceCostsRelation : tInsuranceCostsRelationList) {
                    insuranceCostsIds.add(tInsuranceCostsRelation.getInsuranceCostsId());
                }
                //判断保险关联的保险费用是否产生的账单数据
                List<TVehicleSettlementRelation> tVehicleSettlementRelationList = tVehicleSettlementRelationMapper.getByObjectIdsAndType(VehicleSettlementTypeEnum.INSURANCE.getKey(), StringUtils.listToString(new ArrayList(insuranceCostsIds), ','));
                if (ListUtils.isNotEmpty(tVehicleSettlementRelationList)) {
                    ifSettlement = true;
                }
            }
            //只有是待结算才能改保单类型、保单号、费用、开始、截止时间
            if (InsuranceEnum.PERSONAL.getKey().equals(tqInsurance.getInsuranceType())
                    || (!InsuranceEnum.PERSONAL.getKey().equals(tqInsurance.getInsuranceType()) && InsuranceSettlementStatusEnum.WAIT.getKey().equals(tqInsurance.getSettlementStatus()) && !ifSettlement)) {
                //查询当前车辆的保险费用是否有待结算的数据，如果有也不修改
                insurance.setInsuranceType(requestModel.getInsuranceType());
                insurance.setPolicyNo(requestModel.getPolicyNumber());
                insurance.setPremium(requestModel.getPremium());
                insurance.setStartTime(requestModel.getStartTime());
                insurance.setEndTime(requestModel.getEndTime());
                //修改为与原来不同的车辆时才会更新车辆信息
                if (!tqInsurance.getVehicleId().equals(requestModel.getVehicleId())) {
                    insurance.setVehicleId(requestModel.getVehicleId());
                    insurance.setVehicleProperty(vehicleBasic.getVehicleProperty());
                }
                //交强险，未还保费 = 保费 + 车船税
                if (InsuranceEnum.COMPULSORY.getKey().equals(requestModel.getInsuranceType())) {
                    insurance.setUnpaidPremium(requestModel.getPremium().add(requestModel.getPaymentOfVehicleAndVesselTax()));
                } else {
                    insurance.setUnpaidPremium(requestModel.getPremium());
                }

                //判断当前保险是否关联了保险费用，如果是，并且没有关联结算数据，则把当前的保险费用删掉
                if (ListUtils.isNotEmpty(tInsuranceCostsRelationList) && !ifSettlement) {
                    TInsuranceCosts tInsuranceCosts;
                    List<TInsuranceCosts> tInsuranceCostsUpdateList = new ArrayList<>();
                    Set<Long> insuranceCostsIds = new HashSet<>();
                    for (TInsuranceCostsRelation insuranceCostsRelation : tInsuranceCostsRelationList) {
                        tInsuranceCosts = new TInsuranceCosts();
                        tInsuranceCosts.setId(insuranceCostsRelation.getInsuranceCostsId());
                        tInsuranceCosts.setValid(IfValidEnum.INVALID.getKey());
                        commonBiz.setBaseEntityModify(tInsuranceCosts, userName);
                        tInsuranceCostsUpdateList.add(tInsuranceCosts);
                        insuranceCostsIds.add(insuranceCostsRelation.getInsuranceCostsId());
                    }
                    if (ListUtils.isNotEmpty(tInsuranceCostsUpdateList)) {
                        insuranceCostsMapper.batchUpdate(tInsuranceCostsUpdateList);
                    }
                    if (!insuranceCostsIds.isEmpty()) {
                        insuranceCostsRelationMapper.batchUpdateValidByInsuranceCostsIds(StringUtils.listToString(new ArrayList(insuranceCostsIds), ','));
                    }
                }
            }
            insurance.setId(requestModel.getInsuranceId());
            commonBiz.setBaseEntityModify(insurance, userName);
            insuranceMapper.updateByPrimaryKeySelective(insurance);

            //删除或修改保单票据
            boolean updatePicFlag = false;
            Integer fileType = null;
            String upSource = "";
            if (tqInsurance.getInsuranceType().equals(requestModel.getInsuranceType())) {//险种未改变
                if (!requestModel.getInsuranceType().equals(CertificationPicturesFileTypeEnum.PERSONAL_INSURANCE.getFileType())) {
                    fileType = requestModel.getInsuranceType();
                    updatePicFlag = true;
                    upSource = CommonConstant.ONE;
                }
            } else {//险种改变
                fileType = tqInsurance.getInsuranceType();
                updatePicFlag = true;
                upSource = CommonConstant.TWO;
            }
            if (updatePicFlag) {
                List<TCertificationPictures> ticketList = certificationPicturesMapper.getByObjectIdType(requestModel.getInsuranceId(), CertificationPicturesObjectTypeEnum.T_INSURANCE.getObjectType(), fileType);
                List<InsuranceTicketsRequestModel> ticketsListUpdate = requestModel.getTicketList().stream().filter(item -> item.getTicketId() != null && item.getTicketId() > CommonConstant.LONG_ZERO).collect(Collectors.toList());
                List<InsuranceTicketsRequestModel> ticketsListRequest = requestModel.getTicketList().stream().filter(item -> item.getTicketId() == null || item.getTicketId().equals(CommonConstant.LONG_ZERO)).collect(Collectors.toList());
                requestModel.setTicketList(ticketsListRequest);
                List<String> ticketPathList = ticketList.stream().map(TCertificationPictures::getFilePath).collect(Collectors.toList());
                List<Long> ticketIdList = ticketList.stream().map(TCertificationPictures::getId).collect(Collectors.toList());
                TCertificationPictures certificationPictures;
                List<TCertificationPictures> upList = new ArrayList<>();
                List<Long> idList = new ArrayList<>();
                if (ListUtils.isNotEmpty(ticketsListUpdate)) {
                    for (InsuranceTicketsRequestModel ticket : ticketsListUpdate) {
                        if (!ticketPathList.contains(ticket.getFilePath())) {
                            if (upSource.equals(CommonConstant.ONE)) {
                                certificationPictures = new TCertificationPictures();
                                certificationPictures.setId(ticket.getTicketId());
                                certificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.PERSONAL_ACCIDENT_INSURANCE.getKey(), "", ticket.getFilePath(), null));
                                commonBiz.setBaseEntityModify(certificationPictures, userName);
                                upList.add(certificationPictures);
                            }
                        } else {
                            if (upSource.equals(CommonConstant.TWO)) {
                                certificationPictures = new TCertificationPictures();
                                certificationPictures.setId(ticket.getTicketId());
                                certificationPictures.setFileType(requestModel.getInsuranceType());
                                commonBiz.setBaseEntityModify(certificationPictures, userName);
                                upList.add(certificationPictures);
                            }
                        }
                        idList.add(ticket.getTicketId());
                    }
                    if (ListUtils.isNotEmpty(idList)) {
                        ticketIdList.removeAll(idList);
                        if (ListUtils.isNotEmpty(ticketIdList)) {
                            for (Long id : ticketIdList) {
                                certificationPictures = new TCertificationPictures();
                                certificationPictures.setId(id);
                                certificationPictures.setValid(CommonConstant.INTEGER_ZERO);
                                commonBiz.setBaseEntityModify(certificationPictures, userName);
                                upList.add(certificationPictures);
                            }
                        }
                    }
                } else {
                    for (TCertificationPictures ticket : ticketList) {
                        certificationPictures = new TCertificationPictures();
                        certificationPictures.setId(ticket.getId());
                        certificationPictures.setValid(CommonConstant.INTEGER_ZERO);
                        commonBiz.setBaseEntityModify(certificationPictures, userName);
                        upList.add(certificationPictures);
                    }
                }
                if (ListUtils.isNotEmpty(upList)) {
                    certificationPicturesMapper.batchUpdate(upList);
                }
            }
        } else {//新增保险
            //判断是否为内部车辆
            if (!(VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehicleBasic.getVehicleProperty()) ||
                    VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehicleBasic.getVehicleProperty()))) {
                throw new BizException(CarrierDataExceptionEnum.INTERNAL_VEHICLE_ERROR);
            }

            if (ListUtils.isNotEmpty(insuranceList)) {
                throw new BizException(CarrierDataExceptionEnum.INSURANCE_EXIST);
            }
            String policyNumber = "";
            if (getPersonInsurancePersonCountByIdOne != null && getPersonInsurancePersonCountByIdOne.getPolicyPersonCount().equals(getPersonInsurancePersonCountByIdOne.getAssociatedCount())) {
                if (StringUtils.isNotBlank(getPersonInsurancePersonCountByIdOne.getBatchNumber())) {
                    policyNumber = getPersonInsurancePersonCountByIdOne.getBatchNumber();
                } else {
                    policyNumber = getPersonInsurancePersonCountByIdOne.getPolicyNumber();
                }
                throw new BizException(CarrierDataExceptionEnum.PERSON_ACCIDENT_INSURANCE_COUNT_MAX.getCode(), policyNumber + CarrierDataExceptionEnum.PERSON_ACCIDENT_INSURANCE_COUNT_MAX.getMsg());
            }
            if (getPersonInsurancePersonCountByIdTwo != null && getPersonInsurancePersonCountByIdTwo.getPolicyPersonCount().equals(getPersonInsurancePersonCountByIdTwo.getAssociatedCount())) {
                if (StringUtils.isNotBlank(getPersonInsurancePersonCountByIdTwo.getBatchNumber())) {
                    policyNumber = getPersonInsurancePersonCountByIdTwo.getBatchNumber();
                } else {
                    policyNumber = getPersonInsurancePersonCountByIdTwo.getPolicyNumber();
                }
                throw new BizException(CarrierDataExceptionEnum.PERSON_ACCIDENT_INSURANCE_COUNT_MAX.getCode(), policyNumber + CarrierDataExceptionEnum.PERSON_ACCIDENT_INSURANCE_COUNT_MAX.getMsg());
            }
            insurance.setVehicleId(requestModel.getVehicleId());
            insurance.setVehicleProperty(vehicleBasic.getVehicleProperty());
            insurance.setInsuranceType(requestModel.getInsuranceType());
            insurance.setPolicyNo(requestModel.getPolicyNumber());
            insurance.setPremium(requestModel.getPremium());
            insurance.setStartTime(requestModel.getStartTime());
            insurance.setEndTime(requestModel.getEndTime());
            insurance.setSource(CommonConstant.INTEGER_ONE);
            //交强险，未还保费 = 保费 + 车船税
            if(InsuranceEnum.COMPULSORY.getKey().equals(requestModel.getInsuranceType())){
                insurance.setUnpaidPremium(requestModel.getPremium().add(requestModel.getPaymentOfVehicleAndVesselTax()));
            }else{
                insurance.setUnpaidPremium(requestModel.getPremium());
            }

            commonBiz.setBaseEntityAdd(insurance, userName);
            insuranceMapper.insertSelective(insurance);
            insuranceId = insurance.getId();
        }
        //新增凭证
        if (!requestModel.getInsuranceType().equals(CertificationPicturesFileTypeEnum.PERSONAL_INSURANCE.getFileType()) && ListUtils.isNotEmpty(requestModel.getTicketList())) {
            CertificationPicturesFileTypeEnum fileTypeEnum = CertificationPicturesFileTypeEnum.COMMERCIAL_INSURANCE;
            if (requestModel.getInsuranceType().equals(CertificationPicturesFileTypeEnum.COMMERCIAL_INSURANCE.getFileType())) {
                fileTypeEnum = CertificationPicturesFileTypeEnum.COMMERCIAL_INSURANCE;
            } else if (requestModel.getInsuranceType().equals(CertificationPicturesFileTypeEnum.COMPULSORY_INSURANCE.getFileType())) {
                fileTypeEnum = CertificationPicturesFileTypeEnum.COMPULSORY_INSURANCE;
            } else if (requestModel.getInsuranceType().equals(CertificationPicturesFileTypeEnum.PERSONAL_INSURANCE.getFileType())) {
                fileTypeEnum = CertificationPicturesFileTypeEnum.PERSONAL_INSURANCE;
            } else if (requestModel.getInsuranceType().equals(CertificationPicturesFileTypeEnum.CARGO_INSURANCE.getFileType())) {
                fileTypeEnum = CertificationPicturesFileTypeEnum.CARGO_INSURANCE;
            } else if (requestModel.getInsuranceType().equals(CertificationPicturesFileTypeEnum.CARRIER_INSURANCE.getFileType())) {
                fileTypeEnum = CertificationPicturesFileTypeEnum.CARRIER_INSURANCE;
            }
            TCertificationPictures certificationPictures;
            List<TCertificationPictures> addList = new ArrayList<>();
            for (InsuranceTicketsRequestModel ticket : requestModel.getTicketList()) {
                certificationPictures = new TCertificationPictures();
                certificationPictures.setObjectType(CertificationPicturesObjectTypeEnum.T_INSURANCE.getObjectType());
                certificationPictures.setObjectId(insuranceId);
                certificationPictures.setFileType(fileTypeEnum.getFileType());
                certificationPictures.setFileTypeName(fileTypeEnum.getFileName());
                certificationPictures.setFileName(fileTypeEnum.getFileName());
                certificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.INSURANCE_TICKETS.getKey(), "", ticket.getFilePath(), null));
                certificationPictures.setUploadUserName(userName);
                certificationPictures.setUploadTime(now);
                certificationPictures.setSuffix(ticket.getFilePath().substring(ticket.getFilePath().lastIndexOf('.')));
                commonBiz.setBaseEntityAdd(certificationPictures, userName);
                addList.add(certificationPictures);
            }
            if (ListUtils.isNotEmpty(addList)) {
                certificationPicturesMapper.batchInsert(addList);
            }
        }
    }

    /**
     * 作废保险
     *
     * @param requestModel
     */
    @Transactional
    public void cancelInsurance(CancelInsuranceRequestModel requestModel) {
        GetInsuranceDetailResponseModel tqInsurance = insuranceMapper.getInsuranceDetail(requestModel.getInsuranceId());
        if (tqInsurance == null) {
            throw new BizException(CarrierDataExceptionEnum.INSURANCE_EMPTY);
        }
        Date now = new Date();
        if (tqInsurance.getStatusType().equals(InsuranceStatusTypeEnum.CANCEL.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.INSURANCE_CANCEL);
        } else if (tqInsurance.getStatusType().equals(InsuranceStatusTypeEnum.REFUND.getKey())){
            throw new BizException(CarrierDataExceptionEnum.INSURANCE_REFUND);
        }else {
            if (tqInsurance.getInsuranceType().equals(CertificationPicturesFileTypeEnum.PERSONAL_INSURANCE.getFileType())) {
                if (tqInsurance.getEndTimePerson().getTime() < now.getTime()) {
                    throw new BizException(CarrierDataExceptionEnum.INSURANCE_OVERDUE);
                }
            } else {
                if (tqInsurance.getEndTime().getTime() < now.getTime()) {
                    throw new BizException(CarrierDataExceptionEnum.INSURANCE_OVERDUE);
                }
            }
        }
        //判断有保险费用所关联的车辆结算是否未确认,有则不允许作废
        List<GetVehicleSettlementRelationByInsuranceIdsModel> tInsuranceCostsList = tVehicleSettlementRelationMapper.getByInsuranceIds(ConverterUtils.toString(tqInsurance.getInsuranceId()));
        if (ListUtils.isNotEmpty(tInsuranceCostsList)) {
            List<GetVehicleSettlementRelationByInsuranceIdsBaseModel> relationByInsuranceIdsList = tInsuranceCostsList.get(CommonConstant.INTEGER_ZERO).getVehicleSettlementRelationList();
            if (ListUtils.isNotEmpty(relationByInsuranceIdsList)) {
                List<Long> vehicleSettlementIdList = relationByInsuranceIdsList.stream().map(GetVehicleSettlementRelationByInsuranceIdsBaseModel::getVehicleSettlementId).distinct().collect(Collectors.toList());
                if (ListUtils.isNotEmpty(vehicleSettlementIdList)) {
                    List<TVehicleSettlement> vehicleSettlementList = tVehicleSettlementMapper.getVehicleSettlementByIds(StringUtils.listToString(vehicleSettlementIdList,','));
                    TVehicleSettlement tVehicleSettlement = vehicleSettlementList.stream().filter(item -> item.getStatus() < VehicleSettlementStatementStatusEnum.WAIT_SETTLE.getKey()).findFirst().orElse(null);
                    if (tVehicleSettlement != null) {
                        throw new BizException(CarrierDataExceptionEnum.INSURANCE_HAVE_COSTS_NOT_ALLOW_CANCEL);
                    }
                }
            }
        }

        TInsurance insurance = new TInsurance();
        insurance.setId(requestModel.getInsuranceId());
        insurance.setStatusType(CommonConstant.INTEGER_ONE);
        insurance.setCancelReason(requestModel.getCancelReason());
        commonBiz.setBaseEntityModify(insurance, BaseContextHandler.getUserName());
        insuranceMapper.updateByPrimaryKeySelective(insurance);
    }

    /**
     * 导入保险
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public ImportInsuranceResponseModel importInsurance(ImportInsuranceRequestModel requestModel) {
        ImportInsuranceResponseModel responseModel = new ImportInsuranceResponseModel();
        Integer numberFailures = requestModel.getErrorNumber();
        Integer numberSuccessful = 0;
        String userName = BaseContextHandler.getUserName();
        List<TInsurance> insuranceList;
        TStaffBasic tqStaffBasic;
        TVehicleBasic vehicleBasic;
        TInsurance insurance;
        List<TInsurance> addList = new ArrayList<>();
        VerifyInsuranceUniquenessRequestModel verifyInsuranceUniquenessRequestModel;
        TInsuranceCompany tqInsuranceCompany;
        TPersonalAccidentInsurance personalAccidentInsurance;
        Long personalAccidentInsuranceId = 0L;
        for (ImportInsuranceListRequestModel model : requestModel.getImportList()) {
            vehicleBasic = vehicleBasicMapper.getInfoByVehicleNo(model.getVehicleNo());
            if (vehicleBasic == null) {
                numberFailures++;
                continue;
            }
            tqStaffBasic = staffBasicMapper.getByMobile(model.getDriverPhone());
            if (tqStaffBasic == null
                    || CommonConstant.INTEGER_ZERO.equals(tqStaffBasic.getType())
                    || CommonConstant.INTEGER_TWO.equals(tqStaffBasic.getType())) {
                numberFailures++;
                continue;
            }
            if (model.getInsuranceType().equals(CertificationPicturesFileTypeEnum.PERSONAL_INSURANCE.getFileType())) {
                personalAccidentInsurance = personalAccidentInsuranceMapper.getByTypePolicyNumber(null, model.getPolicyNumber(), model.getBatchNumber());
                if (personalAccidentInsurance == null) {
                    numberFailures++;
                    continue;
                }
                personalAccidentInsuranceId = personalAccidentInsurance.getId();
            }
            GetPersonInsurancePersonCountByIdResponseModel getPersonInsurancePersonCountByIdResponseModel = null;
            if (model.getInsuranceType().equals(CertificationPicturesFileTypeEnum.PERSONAL_INSURANCE.getFileType())) {
                verifyInsuranceUniquenessRequestModel = new VerifyInsuranceUniquenessRequestModel();
                verifyInsuranceUniquenessRequestModel.setDriverId(tqStaffBasic.getId());
                verifyInsuranceUniquenessRequestModel.setPersonalAccidentInsuranceId(personalAccidentInsuranceId);
                getPersonInsurancePersonCountByIdResponseModel = personalAccidentInsuranceMapper.getPersonInsurancePersonCountById(personalAccidentInsuranceId);
            } else {
                verifyInsuranceUniquenessRequestModel = new VerifyInsuranceUniquenessRequestModel();
                verifyInsuranceUniquenessRequestModel.setInsuranceType(model.getInsuranceType());
                verifyInsuranceUniquenessRequestModel.setVehicleId(vehicleBasic.getId());
                verifyInsuranceUniquenessRequestModel.setStartTime(DateUtils.dateToString(model.getStartTime(), CommonConstant.DATE_TO_STRING_DETAIAL_PATTERN));
                verifyInsuranceUniquenessRequestModel.setEndTime(DateUtils.dateToString(model.getEndTime(), CommonConstant.DATE_TO_STRING_DETAIAL_PATTERN));
            }
            insuranceList = insuranceMapper.getByTypeVehicleIdOrDriverId(verifyInsuranceUniquenessRequestModel);
            if (ListUtils.isNotEmpty(insuranceList) || (getPersonInsurancePersonCountByIdResponseModel != null && getPersonInsurancePersonCountByIdResponseModel.getPolicyPersonCount().equals(getPersonInsurancePersonCountByIdResponseModel.getAssociatedCount()))) {
                numberFailures++;
                continue;
            }
            tqInsuranceCompany = insuranceCompanyMapper.findInsuranceCompanyByName(model.getInsuranceCompanyName());
            if (tqInsuranceCompany == null) {
                numberFailures++;
                continue;
            }
            insurance = new TInsurance();
            insurance.setInsuranceType(model.getInsuranceType());
            insurance.setVehicleId(vehicleBasic.getId());
            insurance.setVehicleProperty(vehicleBasic.getVehicleProperty());
            insurance.setDriverId(tqStaffBasic.getId());
            insurance.setInsuranceCompanyId(tqInsuranceCompany.getId());
            insurance.setPolicyNo(model.getPolicyNumber());
            insurance.setPremium(model.getPremium());
            insurance.setUnpaidPremium(model.getPremium());
            insurance.setStartTime(model.getStartTime());
            insurance.setEndTime(model.getEndTime());
            insurance.setPersonalAccidentInsuranceId(personalAccidentInsuranceId);
            insurance.setPaymentOfVehicleAndVesselTax(model.getPaymentOfVehicleAndVesselTax());
            insurance.setSource(CommonConstant.INTEGER_TWO);
            commonBiz.setBaseEntityAdd(insurance, userName);
            addList.add(insurance);
            numberSuccessful++;
        }
        if (ListUtils.isNotEmpty(addList)) {
            insuranceMapper.batchInsert(addList);
        }
        responseModel.setNumberFailures(numberFailures);
        responseModel.setNumberSuccessful(numberSuccessful);
        return responseModel;
    }

    /**
     * 导入保险证件信息
     *
     * @param requestModel
     */
    @Transactional
    public void importInsuranceCertificateInfo(ImportInsuranceCertificateRequestModel requestModel) {
        //判断车牌号是否存在
        TVehicleBasic tqVehicleBasic = vehicleBasicMapper.getInfoByVehicleNo(requestModel.getVehicleNo());
        if (tqVehicleBasic == null) {
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }
        //判断该车在该年限是否存在保险
        List<TInsurance> tqInsuranceList = insuranceMapper.getByVehicleIdAndTime(requestModel.getInsuranceType(), tqVehicleBasic.getId(), requestModel.getStartTime(), requestModel.getEndTime());
        if (ListUtils.isEmpty(tqInsuranceList) || tqInsuranceList.size() > 1) {//该车在同一险种同一年份内有2份保险，则图片不挂在任一保险上
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }
        TInsurance tqInsurance = tqInsuranceList.get(0);
        CertificationPicturesFileTypeEnum fileTypeEnum = null;
        if (requestModel.getInsuranceType().equals(CertificationPicturesFileTypeEnum.COMMERCIAL_INSURANCE.getFileType())) {
            fileTypeEnum = CertificationPicturesFileTypeEnum.COMMERCIAL_INSURANCE;
        } else if (requestModel.getInsuranceType().equals(CertificationPicturesFileTypeEnum.COMPULSORY_INSURANCE.getFileType())) {
            fileTypeEnum = CertificationPicturesFileTypeEnum.COMPULSORY_INSURANCE;
        } else if (requestModel.getInsuranceType().equals(CertificationPicturesFileTypeEnum.PERSONAL_INSURANCE.getFileType())) {
            fileTypeEnum = CertificationPicturesFileTypeEnum.PERSONAL_INSURANCE;
        } else if (requestModel.getInsuranceType().equals(CertificationPicturesFileTypeEnum.CARGO_INSURANCE.getFileType())) {
            fileTypeEnum = CertificationPicturesFileTypeEnum.CARGO_INSURANCE;
        } else if (requestModel.getInsuranceType().equals(CertificationPicturesFileTypeEnum.CARRIER_INSURANCE.getFileType())) {
            fileTypeEnum = CertificationPicturesFileTypeEnum.CARRIER_INSURANCE;
        }
        if (fileTypeEnum == null) {
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }
        String userName = BaseContextHandler.getUserName();
        List<TCertificationPictures> insuranceTicketsList = certificationPicturesMapper.getByObjectIdType(tqInsurance.getId(), CertificationPicturesObjectTypeEnum.T_INSURANCE.getObjectType(), fileTypeEnum.getFileType());
        if (ListUtils.isNotEmpty(insuranceTicketsList) && insuranceTicketsList.size() > 3) {
            TCertificationPictures del = new TCertificationPictures();
            del.setId(insuranceTicketsList.get(0).getId());
            del.setValid(CommonConstant.INTEGER_ZERO);
            commonBiz.setBaseEntityModify(del, userName);
            certificationPicturesMapper.updateByPrimaryKeySelective(del);
        }
        TCertificationPictures newPicture = new TCertificationPictures();
        newPicture.setFileType(fileTypeEnum.getFileType());
        newPicture.setFileTypeName(fileTypeEnum.getFileName());
        newPicture.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.INSURANCE_TICKETS.getKey(), "", requestModel.getFilePath(), null));
        newPicture.setObjectType(CertificationPicturesObjectTypeEnum.T_INSURANCE.getObjectType());
        newPicture.setObjectId(tqInsurance.getId());
        newPicture.setUploadUserName(userName);
        newPicture.setUploadTime(new Date());
        newPicture.setSuffix(requestModel.getFilePath().substring(requestModel.getFilePath().lastIndexOf('.')));
        commonBiz.setBaseEntityAdd(newPicture, userName);
        certificationPicturesMapper.insertSelective(newPicture);
    }

    /**
     * 根据车辆id查询保险信息（车辆退保页面）
     * @param requestModel
     * @return
     */
    public GetInsuranceInfoByVehicleIdResponseModel getInsuranceInfoByVehicleId(GetInsuranceInfoByVehicleIdRequestModel requestModel) {
        GetInsuranceInfoByVehicleIdResponseModel responseModel = new GetInsuranceInfoByVehicleIdResponseModel();
        //获取该车辆在当前周期内的有效保险
        List<TInsurance> insuranceList = insuranceMapper.getByVehicleIdAndPeriod(requestModel.getVehicleId(), new Date());
        if (ListUtils.isEmpty(insuranceList)){
            return responseModel;
        }

        GetInsuranceInfoByVehicleIdModel commercialInsuranceInfo = new GetInsuranceInfoByVehicleIdModel();
        GetInsuranceInfoByVehicleIdModel compulsoryInsuranceInfo = new GetInsuranceInfoByVehicleIdModel();
        GetInsuranceInfoByVehicleIdModel cargoInsuranceInfo = new GetInsuranceInfoByVehicleIdModel();
        for (TInsurance item : insuranceList) {
            if (item.getInsuranceType().equals(InsuranceEnum.COMMERCIAL.getKey())){
                commercialInsuranceInfo.setInsuranceId(item.getId());
                commercialInsuranceInfo.setPolicyNo(item.getPolicyNo());
                commercialInsuranceInfo.setInsuranceType(item.getInsuranceType());
                commercialInsuranceInfo.setTotalCost(item.getPremium());
                commercialInsuranceInfo.setPayCost(item.getPremium().subtract(item.getUnpaidPremium()));
            }else if (item.getInsuranceType().equals(InsuranceEnum.COMPULSORY.getKey())){
                compulsoryInsuranceInfo.setInsuranceId(item.getId());
                compulsoryInsuranceInfo.setPolicyNo(item.getPolicyNo());
                compulsoryInsuranceInfo.setInsuranceType(item.getInsuranceType());
                compulsoryInsuranceInfo.setTotalCost(item.getPremium().add(item.getPaymentOfVehicleAndVesselTax()));
                compulsoryInsuranceInfo.setPayCost(item.getPremium().subtract(item.getUnpaidPremium()).add(item.getPaymentOfVehicleAndVesselTax()));
            }else if (item.getInsuranceType().equals(InsuranceEnum.CARGO.getKey())){
                cargoInsuranceInfo.setInsuranceId(item.getId());
                cargoInsuranceInfo.setPolicyNo(item.getPolicyNo());
                cargoInsuranceInfo.setInsuranceType(item.getInsuranceType());
                cargoInsuranceInfo.setTotalCost(item.getPremium());
                cargoInsuranceInfo.setPayCost(item.getPremium().subtract(item.getUnpaidPremium()));
            }
        }
        responseModel.setCommercialInsuranceInfo(commercialInsuranceInfo);
        responseModel.setCompulsoryInsuranceInfo(compulsoryInsuranceInfo);
        responseModel.setCargoInsuranceInfo(cargoInsuranceInfo);
        return responseModel;
    }

    /**
     * 确认退保
     * @param requestModel
     */
    @Transactional
    public void confirmRefund(ConfirmRefundRequestModel requestModel) {
        //验证入参
        List<InsuranceRefundModel> insuranceRefundList = new ArrayList<>();//退保的保险
        boolean fileEmptyFlag = false;
        for (InsuranceRefundModel model:requestModel.getInsuranceRefundList()) {
            if (CommonConstant.INTEGER_ONE.equals(model.getIfRefund())){
                insuranceRefundList.add(model);
                if (model.getInsuranceId() != null && StringUtils.isBlank(model.getFilePath())){
                    fileEmptyFlag = true;
                }
            }
        }
        if (ListUtils.isEmpty(insuranceRefundList)) {
            throw new BizException(CarrierDataExceptionEnum.REFUND_INSURANCE_EMPTY);
        }
        if (fileEmptyFlag) {
            throw new BizException(CarrierDataExceptionEnum.REFUND_INSURANCE_FILE_EMPTY);
        }

        //判断车辆
        VehicleBasicPropertyModel vehicleBasic = vehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getVehicleId());
        if (vehicleBasic == null) {
            throw new BizException(CarrierDataExceptionEnum.DRIVING_LICENSE_NOT_EXIST);
        }
        //判断是否是内部车辆
        if (!(VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehicleBasic.getVehicleProperty()) ||
                VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehicleBasic.getVehicleProperty()))) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_PROPERTY_ERROR);
        }
        //判断是否是牵引车/一体车
        if (!VehicleCategoryEnum.TRACTOR.getKey().equals(vehicleBasic.getVehicleCategory()) && !VehicleCategoryEnum.WHOLE.getKey().equals(vehicleBasic.getVehicleCategory())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_CATEGORY_ERROR);
        }

        List<Long> insuranceIdList = new ArrayList<>();
        Map<Long, BigDecimal> refundPremiumMap = new HashMap<>();
        for (InsuranceRefundModel model : insuranceRefundList) {
            insuranceIdList.add(model.getInsuranceId());
            refundPremiumMap.put(model.getInsuranceId(), model.getRefundPremium());
        }
        String insuranceIds = StringUtils.listToString(insuranceIdList, ',');
        //判断退保金额和保险状态
        List<TInsurance> insuranceList = insuranceMapper.getByIds(insuranceIds);
        if (ListUtils.isEmpty(insuranceList)) {
            throw new BizException(CarrierDataExceptionEnum.INSURANCE_EMPTY);
        }
        boolean invalidFlag = false;
        Date now = new Date();
        for (TInsurance tInsurance:insuranceList) {
            if (refundPremiumMap.get(tInsurance.getId()).compareTo(tInsurance.getPremium().add(tInsurance.getPaymentOfVehicleAndVesselTax()).subtract(tInsurance.getUnpaidPremium())) > CommonConstant.INTEGER_ZERO){
                throw new BizException(CarrierDataExceptionEnum.REFUND_PREMIUM_MAX);
            }
            if (!InsuranceStatusTypeEnum.NORMAL.getKey().equals(tInsurance.getStatusType()) || now.getTime() < tInsurance.getStartTime().getTime() || now.getTime() > tInsurance.getEndTime().getTime()){
                invalidFlag = true;
            }
        }
        if (invalidFlag){//已作废、已退保、非保障中的保险不能退保
            throw new BizException(CarrierDataExceptionEnum.INSURANCE_NOT_NORMAL);
        }
        //判断退保的保险有没有处于待结算的
        List<TInsuranceCostsRelation> relationList = insuranceCostsRelationMapper.getWaitSettlementByInsuranceIds(insuranceIds);
        if (ListUtils.isNotEmpty(relationList)){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_HAVE_WAIT_SETTLEMENT_INSURANCE_COST.getCode(),vehicleBasic.getVehicleNo() + CarrierDataExceptionEnum.VEHICLE_HAVE_WAIT_SETTLEMENT_INSURANCE_COST.getMsg());
        }

        //新增保险退款、修改保单状态和退款金额、新增保险退保附件
        TInsuranceRefund insuranceRefund;
        List<TInsuranceRefund> addInsuranceRefundList = new ArrayList<>();
        TInsurance insurance;
        List<TInsurance> upInsuranceList = new ArrayList<>();
        for (InsuranceRefundModel model:insuranceRefundList) {
            insuranceRefund = new TInsuranceRefund();
            insuranceRefund.setVehicleId(vehicleBasic.getVehicleId());
            insuranceRefund.setVehicleNo(vehicleBasic.getVehicleNo());
            insuranceRefund.setSettlementMonth(DateUtils.dateToString(new Date(), CommonConstant.DATE_TO_STRING_YM_PATTERN));
            insuranceRefund.setInsuranceId(model.getInsuranceId());
            insuranceRefund.setInsuranceType(model.getInsuranceType());
            insuranceRefund.setRefundCost(model.getRefundPremium());
            insuranceRefund.setRefundPath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.INSURANCE_TICKETS.getKey(), "", model.getFilePath(), null));
            insuranceRefund.setRemark(requestModel.getRemark());
            commonBiz.setBaseEntityAdd(insuranceRefund,BaseContextHandler.getUserName());
            addInsuranceRefundList.add(insuranceRefund);

            insurance = new TInsurance();
            insurance.setId(model.getInsuranceId());
            insurance.setStatusType(InsuranceStatusTypeEnum.REFUND.getKey());
            insurance.setCancelReason(requestModel.getRemark());
            insurance.setRefundPremium(model.getRefundPremium());
            if (StringUtils.isNotBlank(requestModel.getRemark())){
                insurance.setRemark(requestModel.getRemark());
            }
            commonBiz.setBaseEntityModify(insurance,BaseContextHandler.getUserName());
            upInsuranceList.add(insurance);
        }
        if (ListUtils.isNotEmpty(upInsuranceList)){
            insuranceMapper.batchUpdate(upInsuranceList);
        }
        if (ListUtils.isNotEmpty(addInsuranceRefundList)){
            insuranceRefundMapper.batchInsert(addInsuranceRefundList);
        }
    }
}
