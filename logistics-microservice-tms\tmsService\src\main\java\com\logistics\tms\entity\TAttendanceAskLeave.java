package com.logistics.tms.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/11/10
*/
@Data
public class TAttendanceAskLeave extends BaseEntity {
    /**
    * 驾驶员ID
    */
    @ApiModelProperty("驾驶员ID")
    private Long staffId;

    /**
    * 驾驶员姓名
    */
    @ApiModelProperty("驾驶员姓名")
    private String staffName;

    /**
    * 驾驶员手机号
    */
    @ApiModelProperty("驾驶员手机号")
    private String staffMobile;

    /**
    * 人员机构：1 自主，2 外部，3 自营
    */
    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    /**
    * 请假类型: 1 事假
    */
    @ApiModelProperty("请假类型: 1 事假")
    private Integer leaveType;

    /**
    * 开始时间(年月日)
    */
    @ApiModelProperty("开始时间(年月日)")
    private Date leaveStartTime;

    /**
    * 开始时间类型：1 上午，2 下午
    */
    @ApiModelProperty("开始时间类型：1 上午，2 下午")
    private Integer leaveStartTimeType;

    /**
    * 结束时间(年月日)
    */
    @ApiModelProperty("结束时间(年月日)")
    private Date leaveEndTime;

    /**
    * 结束时间类型：1 上午，2 下午
    */
    @ApiModelProperty("结束时间类型：1 上午，2 下午")
    private Integer leaveEndTimeType;

    /**
    * 请假时长(天)
    */
    @ApiModelProperty("请假时长(天)")
    private BigDecimal leaveDuration;

    /**
    * 请假事由
    */
    @ApiModelProperty("请假事由")
    private String leaveReason;

    /**
    * 申请时间
    */
    @ApiModelProperty("申请时间")
    private Date applyTime;

    /**
    * 审核状态: 0 待审核，1 已审核，2 已驳回，3 已撤销
    */
    @ApiModelProperty("审核状态: 0 待审核，1 已审核，2 已驳回，3 已撤销")
    private Integer auditStatus;

    /**
    * 审核人
    */
    @ApiModelProperty("审核人")
    private String auditorName;

    /**
    * 审核时间
    */
    @ApiModelProperty("审核时间")
    private Date auditTime;

    /**
    * 备注(审核,驳回,撤销)
    */
    @ApiModelProperty("备注(审核,驳回,撤销)")
    private String remark;

    /**
    * 是否有效：1 有效，0 无效
    */
    @ApiModelProperty("是否有效：1 有效，0 无效")
    @TableLogic(value = "1", delval = "0")
    private Integer valid;
}