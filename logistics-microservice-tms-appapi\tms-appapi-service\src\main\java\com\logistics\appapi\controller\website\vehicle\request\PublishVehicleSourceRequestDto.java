package com.logistics.appapi.controller.website.vehicle.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * @author: wjf
 * @date: 2019/10/31 15:21
 */
@Data
public class PublishVehicleSourceRequestDto {
    @ApiModelProperty(value = "车牌号",required = true)
    @NotBlank(message = "请输入车牌号")
    private String vehicleNo;

    @ApiModelProperty(value = "载重吨位",required = true)
    @Min(value = 20,message = "请输入载重吨位，20≤吨位≤20000")
    @Max(value = 20000,message = "请输入载重吨位，20≤吨位≤20000")
    private String approvedLoadWeight;

    @ApiModelProperty(value = "车辆类型",required = true)
    @NotBlank(message = "请选择车辆类型")
    private String type;

    @ApiModelProperty(value = "联系人姓名",required = true)
    @Pattern(regexp = "[\\u4E00-\\u9FA5a-zA-Z]{2,20}",message = "请输入联系人姓名，2≤字符长度≤20")
    private String contactName;

    @ApiModelProperty(value = "联系人手机号",required = true)
    @Pattern(regexp = "[1]\\d{10}",message = "请输入联系人手机号")
    @Size(min = 11,max = 11,message = "请输入联系人手机号")
    private String contactMobile;

    @ApiModelProperty(value = "uuid",required = true)
    @NotBlank(message = "请输入图片验证码")
    private String uuid;
    @ApiModelProperty(value = "图片验证码",required = true)
    @NotBlank(message = "请输入图片验证码")
    private String pictureVerificationCode;
}
