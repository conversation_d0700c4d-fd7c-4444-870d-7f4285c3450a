<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TSafetyGroupMeetingRelationMapper" >
  <insert id="batchInsertSelective" parameterType="com.logistics.tms.entity.TSafetyGroupMeetingRelation">
    <foreach collection="list" item="item" separator=";">
      insert into t_safety_group_meeting_relation
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.safetyGroupMeetingId != null" >
          safety_group_meeting_id,
        </if>
        <if test="item.position != null" >
          position,
        </if>
        <if test="item.participatePerson != null" >
          participate_person,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.safetyGroupMeetingId != null" >
          #{item.safetyGroupMeetingId,jdbcType=BIGINT},
        </if>
        <if test="item.position != null" >
          #{item.position,jdbcType=VARCHAR},
        </if>
        <if test="item.participatePerson != null" >
          #{item.participatePerson,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>


</mapper>