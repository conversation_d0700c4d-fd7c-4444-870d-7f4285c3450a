package com.logistics.appapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2018/10/17 16:22
 */
@Data
public class CarrierOrderArriveLoadUnloadRequestModel {

    @ApiModelProperty("运单Id")
    private Long carrierOrderId;

    @ApiModelProperty("经度")
    private String longitude;
    @ApiModelProperty("纬度")
    private String latitude;

    @ApiModelProperty("图片路径")
    private List<String> tmpUrl;
}
