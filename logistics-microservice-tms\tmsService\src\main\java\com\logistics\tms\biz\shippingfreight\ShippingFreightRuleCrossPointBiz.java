package com.logistics.tms.biz.shippingfreight;

import cn.hutool.core.collection.CollectionUtil;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.EnabledEnum;
import com.logistics.tms.base.enums.IfValidEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.shippingfreight.model.ShippingFreightRuleVehicleLengthSqlConditionModel;
import com.logistics.tms.controller.freightconfig.request.*;
import com.logistics.tms.controller.freightconfig.response.GetConfigVechicleRespModel;
import com.logistics.tms.entity.TShippingFreightAddress;
import com.logistics.tms.entity.TShippingFreightRuleCrossPoint;
import com.logistics.tms.entity.TShippingFreightRuleVehicleLength;
import com.logistics.tms.mapper.TShippingFreightAddressMapper;
import com.logistics.tms.mapper.TShippingFreightRuleCrossPointMapper;
import com.logistics.tms.mapper.TShippingFreightRuleVehicleLengthMapper;
import com.yelo.life.basicdata.api.base.enums.ValidTypeEnum;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ShippingFreightRuleCrossPointBiz {

    @Resource
    TShippingFreightRuleCrossPointMapper tShippingFreightRuleCrossPointMapper;

    @Resource
    CommonBiz commonBiz;


    @Transactional
    public void configStringPoint(List<ConfigStringPointReqModel> configStringPointReqModels){
        List<Long> ruleIds = configStringPointReqModels.stream().map(ConfigStringPointReqModel::getShippingFreightRuleId).collect(Collectors.toList());

        //全部删除
        List<TShippingFreightRuleCrossPoint> delList = new ArrayList<>();
        for (Long ruleId : ruleIds){
            TShippingFreightRuleCrossPoint delPO = new TShippingFreightRuleCrossPoint();
            delPO.setShippingFreightAddressId(ruleId);
            delPO.setValid(IfValidEnum.INVALID.getKey());
            commonBiz.setBaseEntityModify(delPO,BaseContextHandler.getUserName());
            delList.add(delPO);
        }
        tShippingFreightRuleCrossPointMapper.batchUpdateByAddressId(delList);

        List<TShippingFreightRuleCrossPoint> insertList = new ArrayList<>();
        configStringPointReqModels.forEach(e->{
            e.getItemReqDtos().forEach(item->{
                TShippingFreightRuleCrossPoint insertPO = new TShippingFreightRuleCrossPoint();
                insertPO.setShippingFreightAddressId(e.getShippingFreightRuleId());
                insertPO.setCountStart(item.getCountStart());
                insertPO.setCountEnd(item.getCountEnd());
                insertPO.setSort(item.getSort());
                insertPO.setPrice(item.getPrice());
                commonBiz.setBaseEntityAdd(insertPO,BaseContextHandler.getUserName());
                insertList.add(insertPO);
            });
        });
        tShippingFreightRuleCrossPointMapper.batchInsert(insertList);
    }




}
