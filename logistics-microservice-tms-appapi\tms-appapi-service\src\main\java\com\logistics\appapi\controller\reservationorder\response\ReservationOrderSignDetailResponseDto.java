package com.logistics.appapi.controller.reservationorder.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/14 14:52
 */
@Data
public class ReservationOrderSignDetailResponseDto {
    /**
     * 预约单id
     */
    private String reservationOrderId="";

    /**
     * 预约单号
     */
    private String reservationOrderCode="";

    /**
     * 预约类型：1 提货，2 卸货
     */
    private String reservationType="";

    /**
     * 预约类型
     */
    private String reservationTypeLabel="";

    /**
     * 发货地/收货地
     */
    private String address="";

    /**
     * 运单列表
     */
    private List<ReservationCarrierOrderListResponseDto> orderList=new ArrayList<>();

    /**
     * 预计里程数（公里）
     */
    private String expectMileage="";

    /**
     * 预约时间
     */
    private String reservationTime="";

}
