package com.logistics.tms.biz.insurancecompany;

import com.github.pagehelper.PageInfo;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tray.core.exception.BizException;
import com.logistics.tms.api.feign.insurancecompany.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.EnabledEnum;
import com.logistics.tms.base.enums.IfValidEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TInsuranceCompany;
import com.logistics.tms.mapper.TInsuranceCompanyMapper;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/5/29 15:53
 */
@Service
@Slf4j
public class InsuranceCompanyBiz {

    @Autowired
    private TInsuranceCompanyMapper tInsuranceCompanyMapper;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 查询保险公司列表信息
     * @param requestModel
     * @return
     */
    public PageInfo<InsuranceCompanyListResponseModel> searchInsuranceCompanyList(InsuranceCompanyListRequestModel requestModel){
        requestModel.enablePaging();
        List<InsuranceCompanyListResponseModel>  insuranceCompanyList = tInsuranceCompanyMapper.searchInsuranceCompanyList(requestModel);
        return new PageInfo( ListUtils.isNotEmpty(insuranceCompanyList) ? insuranceCompanyList : new ArrayList());
    }

    /**
     * 新增/修改保险公司
     * @param requestModel
     */
    @Transactional
    public void saveOrModifyInsuranceCompany(SaveOrModifyInsuranceCompanyRequestModel requestModel){
        TInsuranceCompany insuranceCompany = new TInsuranceCompany();
        insuranceCompany.setCompanyName(requestModel.getCompanyName());
        insuranceCompany.setRemark(requestModel.getRemark());

        TInsuranceCompany dbOneInsuranceCompany = tInsuranceCompanyMapper.findInsuranceCompanyByName(requestModel.getCompanyName());
        if(requestModel.getInsuranceCompanyId()!= null&&requestModel.getInsuranceCompanyId()>CommonConstant.INTEGER_ZERO){//修改
            TInsuranceCompany dbTwoInsuranceCompany =  tInsuranceCompanyMapper.selectByPrimaryKey(requestModel.getInsuranceCompanyId());
            if(dbTwoInsuranceCompany == null){
                throw new BizException(CarrierDataExceptionEnum.INSURANCE_COMPANY_IS_EMPTY);
            }
            if(dbOneInsuranceCompany!=null && !dbOneInsuranceCompany.getId().equals(dbTwoInsuranceCompany.getId())){
                throw new BizException(CarrierDataExceptionEnum.INSURANCE_COMPANY_HAS_EXIST);
            }
            insuranceCompany.setId(requestModel.getInsuranceCompanyId());
            commonBiz.setBaseEntityModify(insuranceCompany,BaseContextHandler.getUserName());
            tInsuranceCompanyMapper.updateByPrimaryKeySelective(insuranceCompany);
        }else{//新增
            if(dbOneInsuranceCompany!=null){
                throw new BizException(CarrierDataExceptionEnum.INSURANCE_COMPANY_HAS_EXIST);
            }
            insuranceCompany.setAddUserId(BaseContextHandler.getUserId());
            insuranceCompany.setAddUserName(BaseContextHandler.getUserName());
            insuranceCompany.setValid(IfValidEnum.VALID.getKey());
            insuranceCompany.setEnabled(EnabledEnum.ENABLED.getKey());
            insuranceCompany.setSource(CommonConstant.INTEGER_ONE);
            commonBiz.setBaseEntityAdd(insuranceCompany,BaseContextHandler.getUserName());
            tInsuranceCompanyMapper.insertSelective(insuranceCompany);
        }
    }

    /**
     * 导入-批量新增保险公司信息
     * @param requestModel
     */
    @Transactional
    public ImportInsuranceCompanyResponseModel batchImportInsuranceCompany(ImportInsuranceCompanyRequestModel requestModel){
        ImportInsuranceCompanyResponseModel responseModel = new ImportInsuranceCompanyResponseModel();
        responseModel.initNumber(requestModel.getNumberFailures());

        List<InsuranceCompanyRequestModel> importList = requestModel.getImportList();
        if(ListUtils.isEmpty(importList)){
            return responseModel;
        }
        List<InsuranceCompanyListResponseModel> dbInsuranceCompanyList = tInsuranceCompanyMapper.searchInsuranceCompanyList(new InsuranceCompanyListRequestModel());
        Map<String,InsuranceCompanyListResponseModel> checkMap = new HashMap<>();
        if(ListUtils.isNotEmpty(dbInsuranceCompanyList)){
            for (InsuranceCompanyListResponseModel tempInsuranceCompanyModel : dbInsuranceCompanyList) {
                checkMap.put(tempInsuranceCompanyModel.getCompanyName(),tempInsuranceCompanyModel);
            }
        }
        TInsuranceCompany insuranceCompany;
        List<TInsuranceCompany> insuranceCompanyList = new ArrayList<>();
        List<TInsuranceCompany> upInsuranceCompanyList = new ArrayList<>();
        for (InsuranceCompanyRequestModel tempInsuranceCompanyModel: importList) {
            responseModel.addSuccessful();
            insuranceCompany = new TInsuranceCompany();
            insuranceCompany.setCompanyName(tempInsuranceCompanyModel.getCompanyName());
            if(StringUtils.isNotBlank(tempInsuranceCompanyModel.getRemark())){
                insuranceCompany.setRemark(tempInsuranceCompanyModel.getRemark());
            }
            insuranceCompany.setEnabled(EnabledEnum.ENABLED.getKey());
            insuranceCompany.setSource(CommonConstant.INTEGER_TWO);
            if(checkMap.get(tempInsuranceCompanyModel.getCompanyName()) != null){
                InsuranceCompanyListResponseModel  insuranceCompanyModel = checkMap.get(tempInsuranceCompanyModel.getCompanyName());
                insuranceCompany.setId(insuranceCompanyModel.getInsuranceCompanyId());
                commonBiz.setBaseEntityModify(insuranceCompany,BaseContextHandler.getUserName());
                upInsuranceCompanyList.add(insuranceCompany);
            }else{
                insuranceCompany.setAddUserId(BaseContextHandler.getUserId());
                insuranceCompany.setAddUserName(BaseContextHandler.getUserName());
                commonBiz.setBaseEntityAdd(insuranceCompany,BaseContextHandler.getUserName());
                insuranceCompanyList.add(insuranceCompany);
            }
        }
        if(ListUtils.isNotEmpty(insuranceCompanyList)){
            tInsuranceCompanyMapper.batchInsert(insuranceCompanyList);
        }
        if(ListUtils.isNotEmpty(upInsuranceCompanyList)){
            tInsuranceCompanyMapper.batchUpdate(upInsuranceCompanyList);
        }
        return responseModel;
    }

    /**
     * 获取保险公司详情
     * @param requestModel
     * @return
     */
    public InsuranceCompanyDetailResponseModel getDetail(InsuranceCompanyDetailRequestModel requestModel){
        InsuranceCompanyDetailResponseModel responseModel = new InsuranceCompanyDetailResponseModel();
        TInsuranceCompany tQInsuranceCompany =  tInsuranceCompanyMapper.selectByPrimaryKey(requestModel.getInsuranceCompanyId());
        if(tQInsuranceCompany == null){
            throw new BizException(CarrierDataExceptionEnum.INSURANCE_COMPANY_IS_EMPTY);
        }
        responseModel.setInsuranceCompanyId(tQInsuranceCompany.getId());
        responseModel.setCompanyName(tQInsuranceCompany.getCompanyName());
        responseModel.setRemark(tQInsuranceCompany.getRemark());
        return responseModel;
    }

    /**
     * 启用/禁用保险公司
     * @param requestModel
     */
    @Transactional
    public void enableOrDisable(EnableInsuranceCompanyRequestModel requestModel){
        TInsuranceCompany tQInsuranceCompany =  tInsuranceCompanyMapper.selectByPrimaryKey(requestModel.getInsuranceCompanyId());
        if(tQInsuranceCompany == null){
            throw new BizException(CarrierDataExceptionEnum.INSURANCE_COMPANY_IS_EMPTY);
        }

        TInsuranceCompany upInsuranceCompany = new TInsuranceCompany();
        upInsuranceCompany.setId(tQInsuranceCompany.getId());
        if (EnabledEnum.ENABLED.getKey().equals(requestModel.getEnabled())) {
            if (EnabledEnum.ENABLED.getKey().equals(tQInsuranceCompany.getEnabled())) {
                throw new BizException(CarrierDataExceptionEnum.INSURANCE_COMPANY_ENABLE_DISABLE_ERROR);
            }
        } else if (EnabledEnum.DISABLED.getKey().equals(requestModel.getEnabled())) {
            if (EnabledEnum.DISABLED.getKey().equals(tQInsuranceCompany.getEnabled())) {
                throw new BizException(CarrierDataExceptionEnum.INSURANCE_COMPANY_ENABLE_DISABLE_ERROR);
            }
        }else{
            throw new BizException(CarrierDataExceptionEnum.ENABLE_DISABLE_PARAMS_ERROR);
        }
        upInsuranceCompany.setEnabled(requestModel.getEnabled());
        commonBiz.setBaseEntityModify(upInsuranceCompany, BaseContextHandler.getUserName());
        tInsuranceCompanyMapper.updateByPrimaryKeySelective(upInsuranceCompany);
    }

    /**
     * 根据公司名称模糊查询
     * @param requestModel
     * @return
     */
    public List<FuzzyQueryInsuranceCompanyListResponseModel> fuzzyQueryInsuranceCompany(FuzzyQueryInsuranceCompanyRequestModel requestModel){
        return tInsuranceCompanyMapper.queryInsuranceCompanyByName(requestModel);
    }
}
