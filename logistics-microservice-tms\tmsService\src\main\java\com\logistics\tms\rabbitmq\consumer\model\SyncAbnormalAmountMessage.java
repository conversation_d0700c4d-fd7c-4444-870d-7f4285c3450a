package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 同步云盘异常数
 *
 * @author: wei.wang
 * @date: 2021/12/23
 */
@Data
public class SyncAbnormalAmountMessage {

	@ApiModelProperty("运单号")
	private String carrierOrderCode;
	@ApiModelProperty("异常数量")
	private BigDecimal abnormalAmount;
	@ApiModelProperty("操作人")
	private String operatorName;
}
