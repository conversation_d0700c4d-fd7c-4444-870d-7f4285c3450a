package com.logistics.management.webapi.client.workgroup.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/22 15:44
 */
@Data
public class AddEditWorkGroupRequestModel {

    @ApiModelProperty("智能推送配置表id,编辑时必填")
    private Long workGroupId;

    @ApiModelProperty(value = "群名")
    private String groupName;

    @ApiModelProperty(value = "负责人id")
    private Long groupOwnerId;

    @ApiModelProperty(value = "参与人id")
    private Long participantId;

    @ApiModelProperty(value = "推送设置：1 新建群聊，2 已有群聊")
    private Integer workGroupSource;

    @ApiModelProperty(value = "机器人编码")
    private String workGroupCode;

    @ApiModelProperty(value = "匹配地点：1 发货信息，2 收货信息")
    private List<Integer> matchingLocationList;

    @ApiModelProperty(value = "匹配字段：1 区域，2 仓库")
    private Integer matchingField;

    @ApiModelProperty(value = "配置仓库")
    private String configWarehouse;

    @ApiModelProperty(value = "配置区域")
    private List<AddEditWorkGroupDistrictRequestModel> districtList;

    @ApiModelProperty(value = "群简介")
    private String groupDesc;
}
