package com.logistics.tms.mapper;

import com.logistics.tms.controller.routeenquiry.request.SearchRouteEnquiryListForWebRequestModel;
import com.logistics.tms.controller.routeenquiry.request.SearchRouteEnquiryListRequestModel;
import com.logistics.tms.controller.routeenquiry.response.SearchRouteEnquiryListForWebResponseModel;
import com.logistics.tms.controller.routeenquiry.response.SearchRouteEnquiryListResponseModel;
import com.logistics.tms.entity.TRouteEnquiry;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/07/09
*/
@Mapper
public interface TRouteEnquiryMapper extends BaseMapper<TRouteEnquiry> {

    int insertSelectiveBackKey(TRouteEnquiry tRouteEnquiry);

    List<SearchRouteEnquiryListResponseModel> searchList(@Param("params") SearchRouteEnquiryListRequestModel requestModel);

    List<SearchRouteEnquiryListForWebResponseModel> searchListForWeb(@Param("params") SearchRouteEnquiryListForWebRequestModel requestModel,@Param("companyCarrierId") Long companyCarrierId);

}