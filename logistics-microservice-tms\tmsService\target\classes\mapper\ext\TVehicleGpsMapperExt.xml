<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleGpsMapper" >
    <select id="getVehicleInfoByCondition" resultType="com.logistics.tms.api.feign.gpstrack.model.VehicleTrackInfoItem">
        select
        gps.vehicle_id as vehicleId,
        gps.vehicle_no as vehicleNo,
        gps.driver_id as driverId,
        gps.driver_name as  driverName,
        gps.driver_mobile as driverMobile,
        if(count(tcovh.id)=0,0,1) as carrierOrderStatus,
        gps.vehicle_status as vehicleStatus,
        gps.upload_time as uploadTime,
        gps.current_location as currentLocation
        from t_vehicle_gps gps
        left join t_vehicle_basic vehicle on vehicle.valid = 1 and vehicle.id = gps.vehicle_id
        left join t_carrier_order tco on gps.dispatch_order_id = tco.dispatch_order_id and tco.valid = 1 and tco.if_cancel = 0 and tco.if_empty = 0 and tco.status &lt; 60000
        left join t_carrier_order_vehicle_history tcovh on tcovh.carrier_order_id  = tco.id and tcovh.valid = 1 and tcovh.if_invalid = 1 and gps.vehicle_id = tcovh.vehicle_id
        where gps.valid = 1
        <if test="condition.vehicleStatus!=null">
            and gps.vehicle_status = #{condition.vehicleStatus,jdbcType=INTEGER}
        </if>
        <if test="condition.vehicleNo!=null and condition.vehicleNo!=''">
            and instr(gps.vehicle_no,#{condition.vehicleNo,jdbcType=VARCHAR})>0
        </if>
        <if test="condition.driver!=null and condition.driver!=''">
            and (instr(gps.driver_name,#{condition.driver,jdbcType=VARCHAR})>0
                or instr(gps.driver_mobile,#{condition.driver,jdbcType=VARCHAR})>0)
        </if>
        <if test="condition.currentPosition!=null and condition.currentPosition!=''">
            and  instr(gps.current_location,#{condition.currentPosition,jdbcType=VARCHAR})>0
        </if>
        <if test="condition.ifHasCarrierOrders == 1">
            and tcovh.id > 0
        </if>
        <if test="condition.ifHasCarrierOrders == 0">
            and tcovh.id is null
        </if>
        group by gps.id desc
    </select>
    <resultMap id="getCarrierOrdersByVehicleNoMap" type="com.logistics.tms.api.feign.gpstrack.model.SearchCarrierOrderDestinationByVehicleNoResponseModel">
        <id column="gpsId" property="gpsId" jdbcType="BIGINT" />
        <result column="dispatchOrderId" property="dispatchOrderId" jdbcType="BIGINT" />
        <result column="dispatchOrderCode" property="dispatchOrderCode" jdbcType="VARCHAR" />
        <result column="gpsVehicleNo" property="gpsVehicleNo" jdbcType="VARCHAR" />
        <result column="vehicleId" property="vehicleId" jdbcType="BIGINT" />
        <collection property="carrierOrderDestinations" ofType="com.logistics.tms.api.feign.gpstrack.model.CarrierOrderDestinationModel">
            <id column="carrierOrderId" property="carrierOrderId" jdbcType="BIGINT" />
            <result column="carrierOrderCode" property="carrierOrderCode" jdbcType="VARCHAR" />
            <result column="provinceName" property="provinceName" jdbcType="VARCHAR" />
            <result column="cityName" property="cityName" jdbcType="VARCHAR" />
            <result column="areaName" property="areaName" jdbcType="VARCHAR" />
            <result column="detailAddress" property="detailAddress" jdbcType="VARCHAR" />
            <result column="warehouse" property="warehouse" jdbcType="VARCHAR" />
            <result column="carrierOrderVehicleNo" property="carrierOrderVehicleNo" jdbcType="VARCHAR" />
            <result column="carrierOrderVehicleId" property="carrierOrderVehicleId" jdbcType="BIGINT" />
        </collection>
    </resultMap>

    <select id="getCarrierOrdersByVehicleNo" resultMap="getCarrierOrdersByVehicleNoMap">
        select
        gps.id as gpsId,
        gps.dispatch_order_id as dispatchOrderId,
        gps.dispatch_order_code as dispatchOrderCode,
        tco.id as carrierOrderId,
        tco.carrier_order_code as carrierOrderCode,
        tcovh.vehicle_no as carrierOrderVehicleNo,
        tcovh.vehicle_id as carrierOrderVehicleId,
        gps.vehicle_no as gpsVehicleNo,
        gps.vehicle_id as vehicleId,
        tcoa.unload_province_name as provinceName,
        tcoa.unload_city_name as cityName,
        tcoa.unload_area_name as areaName,
        tcoa.unload_detail_address as detailAddress,
        tcoa.unload_warehouse as warehouse
        from t_vehicle_gps gps
        left join t_carrier_order tco on gps.dispatch_order_id = tco.dispatch_order_id and tco.valid = 1 and tco.if_cancel = 0 and tco.if_empty = 0 and tco.status &lt; 60000
        left join t_carrier_order_vehicle_history tcovh on tcovh.carrier_order_id  = tco.id and tcovh.valid = 1 and tcovh.if_invalid = 1
        left join t_carrier_order_address tcoa on tcoa.carrier_order_id = tco.id and tcoa.valid = 1
        where gps.valid = 1 and  gps.vehicle_no = #{vehicleNo,jdbcType=VARCHAR}
        order by tco.created_time desc
    </select>

    <select id="selectAllVehicles" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_vehicle_gps gps
        where gps.valid = 1
    </select>

    <select id="selectVehicleGpsByVehicleNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_vehicle_gps
        where valid = 1 and vehicle_no = #{vehicleNo,jdbcType=VARCHAR}
    </select>

    <select id="selectVehicleByVehicleId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_vehicle_gps
        where vehicle_id = #{id,jdbcType=BIGINT} and valid = 1 limit 1
    </select>

    <update id="batchUpdateVehicleGps">
        <foreach collection="list"  index="index" item="item">
            update t_vehicle_gps
            <set >
                <if test="item.vehicleId != null" >
                    vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleNo != null" >
                    vehicle_no = #{item.vehicleNo,jdbcType=VARCHAR},
                </if>
                <if test="item.driverId != null" >
                    driver_id = #{item.driverId,jdbcType=BIGINT},
                </if>
                <if test="item.driverName != null" >
                    driver_name = #{item.driverName,jdbcType=VARCHAR},
                </if>
                <if test="item.driverMobile != null" >
                    driver_mobile = #{item.driverMobile,jdbcType=VARCHAR},
                </if>
                <if test="item.driverIdentity != null" >
                    driver_identity = #{item.driverIdentity,jdbcType=VARCHAR},
                </if>
                <if test="item.carrierOrderStatus != null" >
                    carrier_order_status = #{item.carrierOrderStatus,jdbcType=INTEGER},
                </if>
                <if test="item.vehicleStatus != null" >
                    vehicle_status = #{item.vehicleStatus,jdbcType=INTEGER},
                </if>
                <if test="item.uploadTime != null" >
                    upload_time = #{item.uploadTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.currentLocation != null" >
                    current_location = #{item.currentLocation,jdbcType=VARCHAR},
                </if>
                <if test="item.dispatchOrderId != null" >
                    dispatch_order_id = #{item.dispatchOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.dispatchOrderCode != null" >
                    dispatch_order_code = #{item.dispatchOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where vehicle_no = #{item.vehicleNo,jdbcType=BIGINT} and valid = 1;
        </foreach>
    </update>

    <select id="getVehicleGpsStatistics" resultType="com.logistics.tms.api.feign.gpstrack.model.AllVehicleTrackInfoResponseModel">
        select
        count(0) as allCount,
        ifnull(sum(vehicle_status),0) as drivingCount,
        ifnull(sum(1-vehicle_status),0) as stopCount
        from t_vehicle_gps gps
        where gps.valid = 1
    </select>

    <update id="batchUpdateVehicleGpsByDriverId">
        <foreach collection="vehicleList" item="item" separator=";">
            update t_vehicle_gps
            <set >
                <if test="item.vehicleId != null" >
                    vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleNo != null" >
                    vehicle_no = #{item.vehicleNo,jdbcType=VARCHAR},
                </if>
                <if test="item.driverId != null" >
                    driver_id = #{item.driverId,jdbcType=BIGINT},
                </if>
                <if test="item.driverName != null" >
                    driver_name = #{item.driverName,jdbcType=VARCHAR},
                </if>
                <if test="item.driverMobile != null" >
                    driver_mobile = #{item.driverMobile,jdbcType=VARCHAR},
                </if>
                <if test="item.driverIdentity != null" >
                    driver_identity = #{item.driverIdentity,jdbcType=VARCHAR},
                </if>
                <if test="item.carrierOrderStatus != null" >
                    carrier_order_status = #{item.carrierOrderStatus,jdbcType=INTEGER},
                </if>
                <if test="item.vehicleStatus != null" >
                    vehicle_status = #{item.vehicleStatus,jdbcType=INTEGER},
                </if>
                <if test="item.uploadTime != null" >
                    upload_time = #{item.uploadTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.currentLocation != null" >
                    current_location = #{item.currentLocation,jdbcType=VARCHAR},
                </if>
                <if test="item.dispatchOrderId != null" >
                    dispatch_order_id = #{item.dispatchOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.dispatchOrderCode != null" >
                    dispatch_order_code = #{item.dispatchOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where driver_id = #{item.id,jdbcType=BIGINT} and valid = 1
        </foreach>
    </update>

    <select id="getVehicleGpsByVehicleIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_vehicle_gps
        where valid = 1 and vehicle_id in (#{vehicleIds,jdbcType=VARCHAR})
    </select>

    <insert id="batchInsertByVehicle" >
        <foreach collection="list" item="item" separator=";">
        insert into t_vehicle_gps
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="item.id != null" >
                id,
            </if>
            <if test="item.vehicleId != null" >
                vehicle_id,
            </if>
            <if test="item.vehicleNo != null" >
                vehicle_no,
            </if>
            <if test="item.driverId != null" >
                driver_id,
            </if>
            <if test="item.driverName != null" >
                driver_name,
            </if>
            <if test="item.driverMobile != null" >
                driver_mobile,
            </if>
            <if test="item.driverIdentity != null" >
                driver_identity,
            </if>
            <if test="item.carrierOrderStatus != null" >
                carrier_order_status,
            </if>
            <if test="item.vehicleStatus != null" >
                vehicle_status,
            </if>
            <if test="item.uploadTime != null" >
                upload_time,
            </if>
            <if test="item.currentLocation != null" >
                current_location,
            </if>
            <if test="item.dispatchOrderId != null" >
                dispatch_order_id,
            </if>
            <if test="item.dispatchOrderCode != null" >
                dispatch_order_code,
            </if>
            <if test="item.createdBy != null" >
                created_by,
            </if>
            <if test="item.createdTime != null" >
                created_time,
            </if>
            <if test="item.lastModifiedBy != null" >
                last_modified_by,
            </if>
            <if test="item.lastModifiedTime != null" >
                last_modified_time,
            </if>
            <if test="item.valid != null" >
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="item.id != null" >
                #{item.id,jdbcType=BIGINT},
            </if>
            <if test="item.vehicleId != null" >
                #{item.vehicleId,jdbcType=BIGINT},
            </if>
            <if test="item.vehicleNo != null" >
                #{item.vehicleNo,jdbcType=VARCHAR},
            </if>
            <if test="item.driverId != null" >
                #{item.driverId,jdbcType=BIGINT},
            </if>
            <if test="item.driverName != null" >
                #{item.driverName,jdbcType=VARCHAR},
            </if>
            <if test="item.driverMobile != null" >
                #{item.driverMobile,jdbcType=VARCHAR},
            </if>
            <if test="item.driverIdentity != null" >
                #{item.driverIdentity,jdbcType=VARCHAR},
            </if>
            <if test="item.carrierOrderStatus != null" >
                #{item.carrierOrderStatus,jdbcType=INTEGER},
            </if>
            <if test="item.vehicleStatus != null" >
                #{item.vehicleStatus,jdbcType=INTEGER},
            </if>
            <if test="item.uploadTime != null" >
                #{item.uploadTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.currentLocation != null" >
                #{item.currentLocation,jdbcType=VARCHAR},
            </if>
            <if test="item.dispatchOrderId != null" >
                #{item.dispatchOrderId,jdbcType=BIGINT},
            </if>
            <if test="item.dispatchOrderCode != null" >
                #{item.dispatchOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="item.createdBy != null" >
                #{item.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="item.createdTime != null" >
                #{item.createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastModifiedBy != null" >
                #{item.lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="item.lastModifiedTime != null" >
                #{item.lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.valid != null" >
                #{item.valid,jdbcType=INTEGER},
            </if>
        </trim>
        </foreach>
    </insert>
</mapper>