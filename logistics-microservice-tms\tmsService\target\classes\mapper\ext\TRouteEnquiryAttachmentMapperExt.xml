<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRouteEnquiryAttachmentMapper" >
  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TRouteEnquiryAttachment" >
    <foreach collection="recordList" item="item" separator=";">
      insert into t_route_enquiry_attachment
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.routeEnquiryId != null" >
          route_enquiry_id,
        </if>
        <if test="item.attachmentType != null" >
          attachment_type,
        </if>
        <if test="item.attachmentPath != null" >
          attachment_path,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.routeEnquiryId != null" >
          #{item.routeEnquiryId,jdbcType=BIGINT},
        </if>
        <if test="item.attachmentType != null" >
          #{item.attachmentType,jdbcType=INTEGER},
        </if>
        <if test="item.attachmentPath != null" >
          #{item.attachmentPath,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

  <resultMap id="getFileGroupTypeByRouteEnquiryId_Map" type="com.logistics.tms.controller.routeenquiry.response.RouteEnquiryFileListResponseModel">
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP"/>
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR"/>
    <result column="attachment_type" property="attachmentType" jdbcType="INTEGER"/>

    <collection property="fileList" ofType="java.lang.String">
      <id column="attachment_path" jdbcType="VARCHAR"/>
    </collection>
  </resultMap>
  <select id="getFileGroupTypeByRouteEnquiryId" resultMap="getFileGroupTypeByRouteEnquiryId_Map">
    select
    attachment_type,
    attachment_path,
    last_modified_by,
    last_modified_time
    from t_route_enquiry_attachment
    where route_enquiry_id = #{routeEnquiryId,jdbcType=BIGINT}
    and valid = 1
    order by last_modified_time desc
  </select>
</mapper>