package com.logistics.tms.biz.demandorder

import com.logistics.tms.client.BasicDataClient
import com.logistics.tms.entity.TDemandOrderAddress
import com.logistics.tms.mapper.TDemandOrderAddressMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class DemandOrderCommonBizTest extends Specification {
    @Mock
    TDemandOrderAddressMapper tDemandOrderAddressMapper
    @Mock
    BasicDataClient basicDataClient
    @InjectMocks
    DemandOrderCommonBiz demandOrderCommonBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "update Address Lon And Lat where dbAddressList=#dbAddressList"() {
        given:
        when(tDemandOrderAddressMapper.batchUpdateSelective(any())).thenReturn(0)
        when(basicDataClient.getLonLatByMapId(any())).thenReturn(null)

        expect:
        demandOrderCommonBiz.updateAddressLonAndLat(dbAddressList)
        assert expectedResult == false

        where:
        dbAddressList                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       || expectedResult
        [new TDemandOrderAddress(loadProvinceName: "loadProvinceName", loadCityName: "loadCityName", loadAreaId: 1l, loadAreaName: "loadAreaName", loadDetailAddress: "loadDetailAddress", loadLongitude: "loadLongitude", loadLatitude: "loadLatitude", unloadProvinceName: "unloadProvinceName", unloadCityName: "unloadCityName", unloadAreaId: 1l, unloadAreaName: "unloadAreaName", unloadDetailAddress: "unloadDetailAddress", unloadLongitude: "unloadLongitude", unloadLatitude: "unloadLatitude")] || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme