package com.logistics.appapi.controller.drivercostapply.response;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/8/2 18:17
 */
@Data
public class SearchCostApplyListCountResponseDto {
    @ApiModelProperty("申请次数")
    private String applyCount="";
    @ApiModelProperty("批准次数")
    private String approvalCount="";
    @ApiModelProperty("批准费用")
    private String approvalFee="";

    @ApiModelProperty("列表数据")
    private PageInfo<SearchCostApplyListResponseDto> pageList;
}
