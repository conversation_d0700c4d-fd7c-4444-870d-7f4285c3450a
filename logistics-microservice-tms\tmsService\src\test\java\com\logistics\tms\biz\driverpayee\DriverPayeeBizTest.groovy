package com.logistics.tms.biz.driverpayee

import com.logistics.tms.api.feign.common.SrcUrlModel
import com.logistics.tms.api.feign.driverpayee.model.AddOrModifyDriverPayeeRequestModel
import com.logistics.tms.api.feign.driverpayee.model.AuditRejectDriverPayeeRequestModel
import com.logistics.tms.api.feign.driverpayee.model.DriverPayeeDetailRequestModel
import com.logistics.tms.api.feign.driverpayee.model.DriverPayeeDetailResponseModel
import com.logistics.tms.api.feign.driverpayee.model.DriverPayeeListRequestModel
import com.logistics.tms.api.feign.driverpayee.model.DriverPayeeListResponseModel
import com.logistics.tms.api.feign.driverpayee.model.ExportDriverPayeeListResponseModel
import com.logistics.tms.api.feign.driverpayee.model.ImportDriverPayeeRequestModel
import com.logistics.tms.api.feign.driverpayee.model.ImportDriverPayeeResponseModel
import com.logistics.tms.api.feign.driverpayee.model.SearchDriverPayeesRequestModel
import com.logistics.tms.api.feign.driverpayee.model.SearchDriverPayeesResponseModel
import com.logistics.tms.api.feign.driverpayee.model.ViewLogResponseModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TBank
import com.logistics.tms.entity.TCertificationPictures
import com.logistics.tms.entity.TDriverPayee
import com.logistics.tms.entity.TOperateLogs
import com.logistics.tms.entity.TVehiclePayeeRel
import com.logistics.tms.mapper.TBankMapper
import com.logistics.tms.mapper.TCertificationPicturesMapper
import com.logistics.tms.mapper.TDriverPayeeMapper
import com.logistics.tms.mapper.TOperateLogsMapper
import com.logistics.tms.mapper.TVehiclePayeeRelMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class DriverPayeeBizTest extends Specification {
    @Mock
    TDriverPayeeMapper tqDriverPayeeMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TCertificationPicturesMapper tqCertificationPicturesMapper
    @Mock
    TVehiclePayeeRelMapper tqVehiclePayeeRelMapper
    @Mock
    TOperateLogsMapper tOperateLogsMapper
    @Mock
    TBankMapper tqBankMapper
    @InjectMocks
    DriverPayeeBiz driverPayeeBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "driver Payee List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqDriverPayeeMapper.selectDriverPayeeListIds(any())).thenReturn([1l])
        when(tqDriverPayeeMapper.selectDriverPayeeListByIds(anyString())).thenReturn([new DriverPayeeListResponseModel()])

        expect:
        driverPayeeBiz.driverPayeeList(requestModel) == expectedResult

        where:
        requestModel                      || expectedResult
        new DriverPayeeListRequestModel() || null
    }

    @Unroll
    def "export Driver Payee List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqDriverPayeeMapper.exportDriverPayeeList(any())).thenReturn([new ExportDriverPayeeListResponseModel()])

        expect:
        driverPayeeBiz.exportDriverPayeeList(requestModel) == expectedResult

        where:
        requestModel                      || expectedResult
        new DriverPayeeListRequestModel() || [new ExportDriverPayeeListResponseModel()]
    }

    @Unroll
    def "add Or Modify Driver Payee where requestModel=#requestModel"() {
        given:
        when(tqDriverPayeeMapper.getByIdentity(anyString())).thenReturn(new TDriverPayee(name: "name", mobile: "mobile", identityNo: "identityNo", bankId: 1l, bankCardNo: "bankCardNo", auditStatus: 0, remark: "remark"))
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(commonBiz.insertLogs(any(), anyLong(), anyString())).thenReturn(new TOperateLogs())
        when(tqCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", fileName: "fileName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 22).getTime(), suffix: "suffix")])
        when(tqCertificationPicturesMapper.batchInsert(any())).thenReturn(0)
        when(tqCertificationPicturesMapper.batchUpdate(any())).thenReturn(0)

        expect:
        driverPayeeBiz.addOrModifyDriverPayee(requestModel)
        assert expectedResult == false

        where:
        requestModel                             || expectedResult
        new AddOrModifyDriverPayeeRequestModel() || true
    }

    @Unroll
    def "driver Payee Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqDriverPayeeMapper.getDriverPayeeDetail(anyLong())).thenReturn(new DriverPayeeDetailResponseModel())

        expect:
        driverPayeeBiz.driverPayeeDetail(requestModel) == expectedResult

        where:
        requestModel                        || expectedResult
        new DriverPayeeDetailRequestModel() || new DriverPayeeDetailResponseModel()
    }

    @Unroll
    def "audit Or Reject where requestModel=#requestModel"() {
        given:
        when(tqDriverPayeeMapper.selectDriverPayeeByIds(anyString())).thenReturn([new TDriverPayee(auditStatus: 0, auditTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 22).getTime(), auditorId: 1l, auditorName: "auditorName")])
        when(tqDriverPayeeMapper.batchUpdate(any())).thenReturn(0)
        when(commonBiz.insertLogs(any(), anyLong(), anyString())).thenReturn(new TOperateLogs())
        when(tqVehiclePayeeRelMapper.getByDriverPayeeId(anyString())).thenReturn([new TVehiclePayeeRel()])
        when(tOperateLogsMapper.batchInsert(any())).thenReturn(0)

        expect:
        driverPayeeBiz.auditOrReject(requestModel)
        assert expectedResult == false

        where:
        requestModel                             || expectedResult
        new AuditRejectDriverPayeeRequestModel() || true
    }

    @Unroll
    def "import Driver Payee where importDriverPayeeRequestModel=#importDriverPayeeRequestModel then expect: #expectedResult"() {
        given:
        when(tqDriverPayeeMapper.getByIdentity(anyString())).thenReturn(new TDriverPayee(name: "name", mobile: "mobile", identityNo: "identityNo", bankId: 1l, bankCardNo: "bankCardNo", auditStatus: 0, remark: "remark"))
        when(commonBiz.insertLogs(any(), anyLong(), anyString())).thenReturn(new TOperateLogs())
        when(tqBankMapper.findBankByName(anyString())).thenReturn(new TBank())

        expect:
        driverPayeeBiz.importDriverPayee(importDriverPayeeRequestModel) == expectedResult

        where:
        importDriverPayeeRequestModel       || expectedResult
        new ImportDriverPayeeRequestModel() || new ImportDriverPayeeResponseModel()
    }

    @Unroll
    def "import Driver Payee Certificate where srcUrlModel=#srcUrlModel"() {
        given:
        when(tqDriverPayeeMapper.findByName(anyString())).thenReturn([new TDriverPayee()])
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(commonBiz.insertLogs(any(), anyLong(), anyString())).thenReturn(new TOperateLogs())
        when(tqCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", fileName: "fileName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 22).getTime())])
        when(tqCertificationPicturesMapper.batchUpdate(any())).thenReturn(0)

        expect:
        driverPayeeBiz.importDriverPayeeCertificate(srcUrlModel)
        assert expectedResult == false

        where:
        srcUrlModel       || expectedResult
        new SrcUrlModel() || true
    }

    @Unroll
    def "driver Payee Logs where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tOperateLogsMapper.selectLogsByCondition(anyInt(), anyLong(), anyInt())).thenReturn([new ViewLogResponseModel()])

        expect:
        driverPayeeBiz.driverPayeeLogs(requestModel) == expectedResult

        where:
        requestModel                        || expectedResult
        new DriverPayeeDetailRequestModel() || [new ViewLogResponseModel()]
    }

    @Unroll
    def "search Driver Payees where requestDto=#requestDto then expect: #expectedResult"() {
        given:
        when(tqDriverPayeeMapper.searchAuditedDriverPayees(anyString())).thenReturn([new SearchDriverPayeesResponseModel()])

        expect:
        driverPayeeBiz.searchDriverPayees(requestDto) == expectedResult

        where:
        requestDto                           || expectedResult
        new SearchDriverPayeesRequestModel() || [new com.logistics.tms.api.feign.driverpayee.model.SearchDriverPayeesResponseModel()]
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme