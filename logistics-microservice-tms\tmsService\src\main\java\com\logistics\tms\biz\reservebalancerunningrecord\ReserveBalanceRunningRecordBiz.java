package com.logistics.tms.biz.reservebalancerunningrecord;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.controller.reservebalance.request.ReserveBalanceDetailRequestModel;
import com.logistics.tms.controller.reservebalance.response.ReserveBalanceDetailResponseModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TReserveBalanceRunningRecord;
import com.logistics.tms.mapper.TReserveBalanceRunningRecordMapper;
import com.yelo.tools.context.BaseContextHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ReserveBalanceRunningRecordBiz {

    private final CommonBiz commonBiz;
    private final TReserveBalanceRunningRecordMapper reserveBalanceRunningRecordMapper;

    /**
     * 备用金余额台账明细查询
     * @param requestModel
     * @return
     */
    public PageInfo<ReserveBalanceDetailResponseModel> reserveBalanceDetail(ReserveBalanceDetailRequestModel requestModel) {
        // 查询明细
        requestModel.enablePaging();
        List<ReserveBalanceDetailResponseModel> reserveBalanceDetailList = reserveBalanceRunningRecordMapper.reserveBalanceDetailGroupMonth(requestModel);
        return new PageInfo<>(reserveBalanceDetailList);
    }

    /**
     * 新增余额流水记录
     * @param balanceId
     * @param runningType
     * @param amount
     * @return id
     */
    public int addBalanceRunningRecord(Long balanceId, Integer runningType, BigDecimal amount) {
        TReserveBalanceRunningRecord balanceRunningRecord = new TReserveBalanceRunningRecord();
        balanceRunningRecord.setReserveBalanceId(balanceId);
        balanceRunningRecord.setRunningDate(new Date());
        balanceRunningRecord.setRunningType(runningType);
        balanceRunningRecord.setAmount(amount);
        commonBiz.setBaseEntityAdd(balanceRunningRecord, BaseContextHandler.getUserName());
        return reserveBalanceRunningRecordMapper.insert(balanceRunningRecord);
    }
}
