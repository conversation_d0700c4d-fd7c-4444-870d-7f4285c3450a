<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TLoanRecordsMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TLoanRecords">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="vehicle_identification_number" jdbcType="VARCHAR" property="vehicleIdentificationNumber" />
    <result column="engine_number" jdbcType="VARCHAR" property="engineNumber" />
    <result column="body_color" jdbcType="VARCHAR" property="bodyColor" />
    <result column="producer" jdbcType="VARCHAR" property="producer" />
    <result column="manufacturers" jdbcType="VARCHAR" property="manufacturers" />
    <result column="production_date" jdbcType="TIMESTAMP" property="productionDate" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="identity_number" jdbcType="VARCHAR" property="identityNumber" />
    <result column="naked_car_price" jdbcType="DECIMAL" property="nakedCarPrice" />
    <result column="insurance_premium" jdbcType="DECIMAL" property="insurancePremium" />
    <result column="purchase_tax" jdbcType="DECIMAL" property="purchaseTax" />
    <result column="car_price" jdbcType="DECIMAL" property="carPrice" />
    <result column="driver_expense" jdbcType="DECIMAL" property="driverExpense" />
    <result column="loan_fee" jdbcType="DECIMAL" property="loanFee" />
    <result column="loan_periods" jdbcType="INTEGER" property="loanPeriods" />
    <result column="loan_start_time" jdbcType="TIMESTAMP" property="loanStartTime" />
    <result column="load_finish_time" jdbcType="TIMESTAMP" property="loadFinishTime" />
    <result column="loan_rate" jdbcType="DECIMAL" property="loanRate" />
    <result column="loan_commission" jdbcType="DECIMAL" property="loanCommission" />
    <result column="loan_interest" jdbcType="DECIMAL" property="loanInterest" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, status, vehicle_id, vehicle_no, brand, model, vehicle_identification_number, 
    engine_number, body_color, producer, manufacturers, production_date, staff_id, name, 
    mobile, identity_number, naked_car_price, insurance_premium, purchase_tax, car_price, 
    driver_expense, loan_fee, loan_periods, loan_start_time, load_finish_time, loan_rate,
    loan_commission, loan_interest, remark, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_loan_records
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_loan_records
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TLoanRecords">
    insert into t_loan_records (id, status, vehicle_id, 
      vehicle_no, brand, model, 
      vehicle_identification_number, engine_number, 
      body_color, producer, manufacturers, 
      production_date, staff_id, name, 
      mobile, identity_number, naked_car_price, 
      insurance_premium, purchase_tax, car_price, 
      driver_expense, loan_fee, loan_periods, 
      loan_start_time, load_finish_time, loan_rate,
      loan_commission, loan_interest, remark, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{vehicleId,jdbcType=BIGINT}, 
      #{vehicleNo,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, 
      #{vehicleIdentificationNumber,jdbcType=VARCHAR}, #{engineNumber,jdbcType=VARCHAR}, 
      #{bodyColor,jdbcType=VARCHAR}, #{producer,jdbcType=VARCHAR}, #{manufacturers,jdbcType=VARCHAR}, 
      #{productionDate,jdbcType=TIMESTAMP}, #{staffId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{mobile,jdbcType=VARCHAR}, #{identityNumber,jdbcType=VARCHAR}, #{nakedCarPrice,jdbcType=DECIMAL}, 
      #{insurancePremium,jdbcType=DECIMAL}, #{purchaseTax,jdbcType=DECIMAL}, #{carPrice,jdbcType=DECIMAL}, 
      #{driverExpense,jdbcType=DECIMAL}, #{loanFee,jdbcType=DECIMAL}, #{loanPeriods,jdbcType=INTEGER}, 
      #{loanStartTime,jdbcType=TIMESTAMP}, #{loadFinishTime,jdbcType=TIMESTAMP}, #{loanRate,jdbcType=DECIMAL},
      #{loanCommission,jdbcType=DECIMAL}, #{loanInterest,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TLoanRecords" keyProperty="id" useGeneratedKeys="true">
    insert into t_loan_records
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleNo != null">
        vehicle_no,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="vehicleIdentificationNumber != null">
        vehicle_identification_number,
      </if>
      <if test="engineNumber != null">
        engine_number,
      </if>
      <if test="bodyColor != null">
        body_color,
      </if>
      <if test="producer != null">
        producer,
      </if>
      <if test="manufacturers != null">
        manufacturers,
      </if>
      <if test="productionDate != null">
        production_date,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="identityNumber != null">
        identity_number,
      </if>
      <if test="nakedCarPrice != null">
        naked_car_price,
      </if>
      <if test="insurancePremium != null">
        insurance_premium,
      </if>
      <if test="purchaseTax != null">
        purchase_tax,
      </if>
      <if test="carPrice != null">
        car_price,
      </if>
      <if test="driverExpense != null">
        driver_expense,
      </if>
      <if test="loanFee != null">
        loan_fee,
      </if>
      <if test="loanPeriods != null">
        loan_periods,
      </if>
      <if test="loanStartTime != null">
        loan_start_time,
      </if>
      <if test="loadFinishTime != null">
        load_finish_time,
      </if>
      <if test="loanRate != null">
        loan_rate,
      </if>
      <if test="loanCommission != null">
        loan_commission,
      </if>
      <if test="loanInterest != null">
        loan_interest,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="vehicleIdentificationNumber != null">
        #{vehicleIdentificationNumber,jdbcType=VARCHAR},
      </if>
      <if test="engineNumber != null">
        #{engineNumber,jdbcType=VARCHAR},
      </if>
      <if test="bodyColor != null">
        #{bodyColor,jdbcType=VARCHAR},
      </if>
      <if test="producer != null">
        #{producer,jdbcType=VARCHAR},
      </if>
      <if test="manufacturers != null">
        #{manufacturers,jdbcType=VARCHAR},
      </if>
      <if test="productionDate != null">
        #{productionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="identityNumber != null">
        #{identityNumber,jdbcType=VARCHAR},
      </if>
      <if test="nakedCarPrice != null">
        #{nakedCarPrice,jdbcType=DECIMAL},
      </if>
      <if test="insurancePremium != null">
        #{insurancePremium,jdbcType=DECIMAL},
      </if>
      <if test="purchaseTax != null">
        #{purchaseTax,jdbcType=DECIMAL},
      </if>
      <if test="carPrice != null">
        #{carPrice,jdbcType=DECIMAL},
      </if>
      <if test="driverExpense != null">
        #{driverExpense,jdbcType=DECIMAL},
      </if>
      <if test="loanFee != null">
        #{loanFee,jdbcType=DECIMAL},
      </if>
      <if test="loanPeriods != null">
        #{loanPeriods,jdbcType=INTEGER},
      </if>
      <if test="loanStartTime != null">
        #{loanStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loadFinishTime != null">
        #{loadFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loanRate != null">
        #{loanRate,jdbcType=DECIMAL},
      </if>
      <if test="loanCommission != null">
        #{loanCommission,jdbcType=DECIMAL},
      </if>
      <if test="loanInterest != null">
        #{loanInterest,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TLoanRecords">
    update t_loan_records
    <set>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="vehicleIdentificationNumber != null">
        vehicle_identification_number = #{vehicleIdentificationNumber,jdbcType=VARCHAR},
      </if>
      <if test="engineNumber != null">
        engine_number = #{engineNumber,jdbcType=VARCHAR},
      </if>
      <if test="bodyColor != null">
        body_color = #{bodyColor,jdbcType=VARCHAR},
      </if>
      <if test="producer != null">
        producer = #{producer,jdbcType=VARCHAR},
      </if>
      <if test="manufacturers != null">
        manufacturers = #{manufacturers,jdbcType=VARCHAR},
      </if>
      <if test="productionDate != null">
        production_date = #{productionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="identityNumber != null">
        identity_number = #{identityNumber,jdbcType=VARCHAR},
      </if>
      <if test="nakedCarPrice != null">
        naked_car_price = #{nakedCarPrice,jdbcType=DECIMAL},
      </if>
      <if test="insurancePremium != null">
        insurance_premium = #{insurancePremium,jdbcType=DECIMAL},
      </if>
      <if test="purchaseTax != null">
        purchase_tax = #{purchaseTax,jdbcType=DECIMAL},
      </if>
      <if test="carPrice != null">
        car_price = #{carPrice,jdbcType=DECIMAL},
      </if>
      <if test="driverExpense != null">
        driver_expense = #{driverExpense,jdbcType=DECIMAL},
      </if>
      <if test="loanFee != null">
        loan_fee = #{loanFee,jdbcType=DECIMAL},
      </if>
      <if test="loanPeriods != null">
        loan_periods = #{loanPeriods,jdbcType=INTEGER},
      </if>
      <if test="loanStartTime != null">
        loan_start_time = #{loanStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loadFinishTime != null">
        load_finish_time = #{loadFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loanRate != null">
        loan_rate = #{loanRate,jdbcType=DECIMAL},
      </if>
      <if test="loanCommission != null">
        loan_commission = #{loanCommission,jdbcType=DECIMAL},
      </if>
      <if test="loanInterest != null">
        loan_interest = #{loanInterest,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TLoanRecords">
    update t_loan_records
    set status = #{status,jdbcType=INTEGER},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      vehicle_identification_number = #{vehicleIdentificationNumber,jdbcType=VARCHAR},
      engine_number = #{engineNumber,jdbcType=VARCHAR},
      body_color = #{bodyColor,jdbcType=VARCHAR},
      producer = #{producer,jdbcType=VARCHAR},
      manufacturers = #{manufacturers,jdbcType=VARCHAR},
      production_date = #{productionDate,jdbcType=TIMESTAMP},
      staff_id = #{staffId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      identity_number = #{identityNumber,jdbcType=VARCHAR},
      naked_car_price = #{nakedCarPrice,jdbcType=DECIMAL},
      insurance_premium = #{insurancePremium,jdbcType=DECIMAL},
      purchase_tax = #{purchaseTax,jdbcType=DECIMAL},
      car_price = #{carPrice,jdbcType=DECIMAL},
      driver_expense = #{driverExpense,jdbcType=DECIMAL},
      loan_fee = #{loanFee,jdbcType=DECIMAL},
      loan_periods = #{loanPeriods,jdbcType=INTEGER},
      loan_start_time = #{loanStartTime,jdbcType=TIMESTAMP},
      load_finish_time = #{loadFinishTime,jdbcType=TIMESTAMP},
      loan_rate = #{loanRate,jdbcType=DECIMAL},
      loan_commission = #{loanCommission,jdbcType=DECIMAL},
      loan_interest = #{loanInterest,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>