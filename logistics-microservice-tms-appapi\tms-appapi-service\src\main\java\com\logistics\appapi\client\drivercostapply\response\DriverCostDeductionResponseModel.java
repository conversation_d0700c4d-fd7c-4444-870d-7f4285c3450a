package com.logistics.appapi.client.drivercostapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DriverCostDeductionResponseModel {

    @ApiModelProperty("备用金单号")
    private String reserveCode;

    @ApiModelProperty("余额")
    private BigDecimal balance;

    @ApiModelProperty("冲销金额")
    private BigDecimal writeOffAmount;

    @ApiModelProperty("备用金类型; 1 冲销 2 垫付")
    private Integer reserveType;
}
