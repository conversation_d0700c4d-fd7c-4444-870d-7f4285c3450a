package com.logistics.appapi.controller.bankcard.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Data
public class BankCardDetailResponseDto {

	@ApiModelProperty(value = "司机账户ID")
	private String driverAccountId = "";

	@ApiModelProperty(value = "开户银行名称")
	private String bankAccountName = "";

	@ApiModelProperty(value = "银行账号")
	private String bankAccount = "";

	@ApiModelProperty(value = "开户支行名称")
	private String braBankName = "";

	@ApiModelProperty(value = "收款证件图片")
	private List<String> bankAccountImage;
}
