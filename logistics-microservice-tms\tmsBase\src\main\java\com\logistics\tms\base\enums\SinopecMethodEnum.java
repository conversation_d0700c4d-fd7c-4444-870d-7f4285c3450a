package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2021/12/5 9:42
 */
public enum SinopecMethodEnum {
    ORDER_QUOTATION("lisbs/networkFreight/api/receiveResults", "接单报价"),
    ORDER_QUOTATION_V1("portal/receive/tms/OrderReceiv", "接单信息集成接口"),
    REFUSE_CONSIGN_ORDER("lisbs/networkFreight/api/refuseConsignOrder", "撤销委托单接口"),
    REFUSE_CONSIGN_ORDER_V1("portal/receive/tms/refusedEntrust", "拒绝委托集成接口"),
    ;

    private String key;
    private String value;

    SinopecMethodEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
