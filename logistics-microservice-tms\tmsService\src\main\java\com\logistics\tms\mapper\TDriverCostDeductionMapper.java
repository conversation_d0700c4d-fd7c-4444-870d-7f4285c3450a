package com.logistics.tms.mapper;

import com.logistics.tms.biz.drivercostdeduction.model.DriverCostDeductionQueryModel;
import com.logistics.tms.entity.TDriverCostDeduction;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2022/12/06
*/
@Mapper
public interface TDriverCostDeductionMapper extends BaseMapper<TDriverCostDeduction> {

    List<TDriverCostDeduction> selectAll(@Param("queryModel") DriverCostDeductionQueryModel queryModel);

    int batchInsert(@Param("costDeductionList") List<TDriverCostDeduction> costDeductionList);
}