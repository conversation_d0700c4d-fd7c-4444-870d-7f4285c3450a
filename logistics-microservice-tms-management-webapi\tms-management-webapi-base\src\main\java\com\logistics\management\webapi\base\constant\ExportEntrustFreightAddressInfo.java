package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/12/30 19:32
 */
public class ExportEntrustFreightAddressInfo {
    private ExportEntrustFreightAddressInfo() {
    }
    private static final Map<String,String> ENTRUST_FREIGHT_ADDRESS_INFO;
    static{
        ENTRUST_FREIGHT_ADDRESS_INFO=new LinkedHashMap<>();
        ENTRUST_FREIGHT_ADDRESS_INFO.put("价格类型","calcTypeLabel");
        ENTRUST_FREIGHT_ADDRESS_INFO.put("仓库","warehouseName");
        ENTRUST_FREIGHT_ADDRESS_INFO.put("发货市","fromProvinceAndCityLabel");
        ENTRUST_FREIGHT_ADDRESS_INFO.put("发货区","fromAreaName");
        ENTRUST_FREIGHT_ADDRESS_INFO.put("卸货市","toProvinceAndCityLabel");
        ENTRUST_FREIGHT_ADDRESS_INFO.put("卸货区","toAreaName");
        ENTRUST_FREIGHT_ADDRESS_INFO.put("操作人","lastModifiedBy");
        ENTRUST_FREIGHT_ADDRESS_INFO.put("操作时间","lastModifiedTime");

    }
    public static Map<String, String> getEntrustFreightAddressInfo() {
        return ENTRUST_FREIGHT_ADDRESS_INFO;
    }
}
