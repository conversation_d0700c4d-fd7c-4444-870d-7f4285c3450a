package com.logistics.management.webapi.controller.reserveapply.mapping;

import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.enums.ReserveApplyAuditStatusEnum;
import com.logistics.management.webapi.base.enums.ReserveApplyTypeEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.reserveapply.response.ReserveApplyListResponseModel;
import com.logistics.management.webapi.controller.reserveapply.response.ReserveApplyListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.math.BigDecimal;

public class ReserveApplyListMapping extends MapperMapping<ReserveApplyListResponseModel, ReserveApplyListResponseDto> {

    private boolean isExport = false;

    public ReserveApplyListMapping() {
    }

    public ReserveApplyListMapping(boolean isExport) {
        this.isExport = isExport;
    }

    @Override
    public void configure() {
        ReserveApplyListResponseModel source = getSource();
        ReserveApplyListResponseDto destination = getDestination();

        // 审核状态
        destination.setStatusLabel(ReserveApplyAuditStatusEnum.getEnumByKey(source.getStatus()).getValue());

        // 司机
        String mobile = isExport ? source.getDriverMobile() : FrequentMethodUtils.encryptionData(source.getDriverMobile(), EncodeTypeEnum.MOBILE_PHONE);

        destination.setDriver(source.getDriverName() + " " + mobile);

        // 收款账户
        String receiveAccount = String.format("%s（%s%s）", source.getReceiveAccount(),
                source.getReceiveBankAccountName(), source.getReceiveBraBankName());
        destination.setReceiveAccount(receiveAccount);

        // 金额
        destination.setApplyAmount(amountConversion(source.getApplyAmount()));
        destination.setApproveAmount(amountConversion(source.getApproveAmount()));

        // 备用金类型
        destination.setTypeLabel(ReserveApplyTypeEnum.getEnumByKey(source.getType()).getValue());
    }

    private String amountConversion(BigDecimal amount) {
        return amount.stripTrailingZeros().toPlainString();
    }
}
