package com.logistics.tms.controller.carrierorderotherfee.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class GetOtherFeeRecordResponseModel {

    @ApiModelProperty("操作人")
    private String operateUserName;

    @ApiModelProperty("操作时间")
    private Date operateTime;

    @ApiModelProperty("操作类型")
    private Integer operateType;

    @ApiModelProperty("说明")
    private String operateContents;

    @ApiModelProperty("备注")
    private String remark;
}