package com.logistics.management.webapi.api.impl.driverappoint;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.driverappoint.DriverAppointApi;
import com.logistics.management.webapi.api.feign.driverappoint.dto.SearchDriverAppointRequestDto;
import com.logistics.management.webapi.api.feign.driverappoint.dto.SearchDriverAppointResponseDto;
import com.logistics.management.webapi.api.impl.driverappoint.mapping.SearchDriverAppointList;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportExcelHeaderDriverAppoint;
import com.logistics.tms.api.feign.driverappoint.DriverAppointServiceApi;
import com.logistics.tms.api.feign.driverappoint.model.SearchDriverAppointRequestModel;
import com.logistics.tms.api.feign.driverappoint.model.SearchDriverAppointResponseModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ExcelUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 新生驾驶员预约列表
 *
 * <AUTHOR>
 * @date 2022/8/17 10:43
 */
@RestController
public class DriverAppointApiImpl implements DriverAppointApi {

    @Autowired
    private DriverAppointServiceApi driverAppointServiceApi;

    /**
     * 查询驾驶员预约列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchDriverAppointResponseDto>> searchDriverAppointList(@RequestBody SearchDriverAppointRequestDto requestDto) {
        Result<PageInfo<SearchDriverAppointResponseModel>> pageInfoResult = driverAppointServiceApi.searchDriverAppointList(
                MapperUtils.mapper(requestDto, SearchDriverAppointRequestModel.class));
        pageInfoResult.throwException();
        PageInfo pageInfo = pageInfoResult.getData();
        List<SearchDriverAppointResponseDto> responseDtoList = MapperUtils.mapper(pageInfo.getList(),
                SearchDriverAppointResponseDto.class,
                new SearchDriverAppointList());
        pageInfo.setList(responseDtoList);
        return Result.success(pageInfo);
    }

    /**
     * 导出驾驶员预约列表
     * @param requestDto
     * @param response
     */
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @Override
    public void exportDriverAppoint(SearchDriverAppointRequestDto requestDto, HttpServletResponse response) {
        SearchDriverAppointRequestModel requestModel = MapperUtils.mapper(requestDto, SearchDriverAppointRequestModel.class);
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<SearchDriverAppointResponseModel>> pageInfoResult = driverAppointServiceApi.searchDriverAppointList(requestModel);
        pageInfoResult.throwException();
        List<SearchDriverAppointResponseDto> responseDtoList = MapperUtils.mapper(pageInfoResult.getData().getList(),
                SearchDriverAppointResponseDto.class,
                new SearchDriverAppointList());
        String fileName = "驾驶员预约记录" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM);
        Map<String, String> exportMap = ExportExcelHeaderDriverAppoint.getExcelDriverAppoint();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return responseDtoList;
            }
        });
    }
}
