package com.logistics.tms.base.enums;

/**
 * @Author: sj
 * @Date: 2019/12/27 18:57
 */
public enum FreightCalcTypeEnum {
    NULL(-1,""),
    ADD_PRICE(1, "统一加价"),
    REDUCE_PRICE(2, "统一减价"),
    ;

    private Integer key;
    private String value;

    FreightCalcTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static FreightCalcTypeEnum getEnum(Integer key) {
        for (FreightCalcTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
