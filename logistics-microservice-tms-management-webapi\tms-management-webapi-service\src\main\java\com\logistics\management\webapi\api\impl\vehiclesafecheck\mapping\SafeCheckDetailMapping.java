package com.logistics.management.webapi.api.impl.vehiclesafecheck.mapping;

import com.logistics.management.webapi.api.feign.vehiclesafecheck.dto.SafeCheckDetailResponseDto;
import com.logistics.management.webapi.api.feign.vehiclesafecheck.dto.SafeCheckFileResponseDto;
import com.logistics.management.webapi.api.feign.vehiclesafecheck.dto.SafeCheckReformResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.tms.api.feign.vehiclesafecheck.model.SafeCheckDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.Map;
import java.util.Optional;

/**
 * @Author: sj
 * @Date: 2019/11/13 11:02
 */
public class SafeCheckDetailMapping extends MapperMapping<SafeCheckDetailResponseModel,SafeCheckDetailResponseDto> {
    private ConfigKeyConstant configKeyConstant;
    private Map<String, String> imageMap;
    public SafeCheckDetailMapping(ConfigKeyConstant configKeyConstant, Map<String, String> imageMap){
        this.configKeyConstant = configKeyConstant;
        this.imageMap=imageMap;
    }
    @Override
    public void configure() {
        SafeCheckDetailResponseModel source = this.getSource();
        SafeCheckDetailResponseDto dto = this.getDestination();
        if(source!=null){
            //其他图片信息
            if(ListUtils.isNotEmpty(dto.getFileList())){
                for (SafeCheckFileResponseDto tempFile: dto.getFileList()) {
                    tempFile.setAbsoluteFilePath(configKeyConstant.fileAccessAddress+imageMap.get(tempFile.getRelativeFilepath()));
                }
            }

            //检查人信息 姓名+电话
            dto.setCheckUserLabel(Optional.ofNullable(dto.getCheckUserName()).orElse("")+" "+Optional.ofNullable(dto.getCheckUserMobile()).orElse(""));

            //整改附件信息
            SafeCheckReformResponseDto checkReformInfo = dto.getCheckReformInfo();
            if(checkReformInfo == null){
                checkReformInfo = new SafeCheckReformResponseDto();
                dto.setCheckReformInfo(checkReformInfo);
            }

            if(ListUtils.isNotEmpty(checkReformInfo.getItemFileList())){
                for (SafeCheckFileResponseDto tempFile: checkReformInfo.getItemFileList()) {
                    tempFile.setAbsoluteFilePath(configKeyConstant.fileAccessAddress+imageMap.get(tempFile.getRelativeFilepath()));
                }
            }
            if(ListUtils.isNotEmpty(checkReformInfo.getResultFileList())){
                for (SafeCheckFileResponseDto tempFile : checkReformInfo.getResultFileList()) {
                    tempFile.setAbsoluteFilePath(configKeyConstant.fileAccessAddress+imageMap.get(tempFile.getRelativeFilepath()));
                }
            }

            if(StringUtils.isBlank(checkReformInfo.getReformCount())){
                checkReformInfo.setReformCount(CommonConstant.ZERO);
            }
            if(StringUtils.isBlank(checkReformInfo.getReformResult())){
                checkReformInfo.setReformResult(CommonConstant.ZERO);
            }

        }
    }
}
