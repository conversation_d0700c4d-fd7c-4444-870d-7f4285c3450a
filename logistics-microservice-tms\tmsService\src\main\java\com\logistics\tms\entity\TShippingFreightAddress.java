package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2024/09/19
*/
@Data
public class TShippingFreightAddress extends BaseEntity {
    /**
    * 零担运价管理id
    */
    @ApiModelProperty("零担运价管理id")
    private Long shippingFreightId;


    /**
    * 发货省id
    */
    @ApiModelProperty("发货省id")
    private Long fromProvinceId;

    /**
    * 发货省name
    */
    @ApiModelProperty("发货省name")
    private String fromProvinceName;

    /**
    * 发货市id
    */
    @ApiModelProperty("发货市id")
    private Long fromCityId;

    /**
    * 发货市name
    */
    @ApiModelProperty("发货市name")
    private String fromCityName;

    /**
    * 发货区id
    */
    @ApiModelProperty("发货区id")
    private Long fromAreaId;

    /**
    * 发货区name
    */
    @ApiModelProperty("发货区name")
    private String fromAreaName;

    /**
    * 收货省id
    */
    @ApiModelProperty("收货省id")
    private Long toProvinceId;

    /**
    * 收货省name
    */
    @ApiModelProperty("收货省name")
    private String toProvinceName;

    /**
    * 收货市id
    */
    @ApiModelProperty("收货市id")
    private Long toCityId;

    /**
    * 收货市name
    */
    @ApiModelProperty("收货市name")
    private String toCityName;

    /**
    * 收货区id
    */
    @ApiModelProperty("收货区id")
    private Long toAreaId;

    /**
    * 收货区name
    */
    @ApiModelProperty("收货区name")
    private String toAreaName;

    /**
    * 运距
    */
    @ApiModelProperty("运距")
    private BigDecimal carrierDistance;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 是否启用,1启用,0禁用
    */
    @ApiModelProperty("是否启用,1启用,0禁用")
    private Integer enabled;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}