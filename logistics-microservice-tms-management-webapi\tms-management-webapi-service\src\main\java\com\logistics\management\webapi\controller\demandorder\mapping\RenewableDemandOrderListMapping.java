package com.logistics.management.webapi.controller.demandorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.demandorder.response.DemandOrderGoodsResponseModel;
import com.logistics.management.webapi.client.demandorder.response.RenewableDemandOrderResponseModel;
import com.logistics.management.webapi.controller.demandorder.response.RenewableDemandOrderResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/29
 */
public class RenewableDemandOrderListMapping extends MapperMapping<RenewableDemandOrderResponseModel, RenewableDemandOrderResponseDto> {

	@Override
	public void configure() {
		RenewableDemandOrderResponseModel source = getSource();
		RenewableDemandOrderResponseDto destination = getDestination();

		if (source != null) {
			//转换客户
			if(LifeBusinessTypeEnum.COMPANY.getKey().equals(source.getBusinessType())){
				destination.setCustomerName(source.getCustomerName());
				destination.setExportCustomerName(source.getCustomerName());
			}else if (LifeBusinessTypeEnum.PERSONAGE.getKey().equals(source.getBusinessType())) {
				destination.setCustomerName(source.getCustomerUserName() + " " + FrequentMethodUtils.encryptionData(source.getCustomerUserMobile(), EncodeTypeEnum.MOBILE_PHONE));
				destination.setExportCustomerName(source.getCustomerUserName() + " " + source.getCustomerUserMobile());
			}
			destination.setCustomerOrderSource(YeloLifeCarrierOrderSourceEnum.getEnum(source.getCustomerOrderSource()).getValue());

			//个人车主展示姓名+手机号
			if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())){
				destination.setCompanyCarrierName(source.getCarrierContactName()+" "+source.getCarrierContactPhone());
			}

			//拼接货物名和规格
			List<DemandOrderGoodsResponseModel> goodsResponseModels = source.getGoodsResponseModels();
			if (ListUtils.isNotEmpty(goodsResponseModels)) {
				BigDecimal amount = BigDecimal.ZERO;
				StringBuilder goodsName = new StringBuilder();
				StringBuilder goodsSize = new StringBuilder();
				for (int index = 0; index < goodsResponseModels.size(); index++) {
					DemandOrderGoodsResponseModel tmpGoodsModel = goodsResponseModels.get(index);
					if (index != 0) {
						goodsName.append("/");
					}
					if (StringUtils.isNotBlank(goodsSize.toString()) && StringUtils.isNotBlank(tmpGoodsModel.getGoodsSize())) {
						goodsSize.append("/");
					}
					goodsName.append(tmpGoodsModel.getGoodsName());
					if (StringUtils.isNotBlank(tmpGoodsModel.getGoodsSize())) {
						goodsSize.append(tmpGoodsModel.getGoodsSize());
					}
					amount = amount.add(tmpGoodsModel.getGoodsAmountNumber() == null ? BigDecimal.ZERO : tmpGoodsModel.getGoodsAmountNumber());
				}
				destination.setGoodsName(goodsName.toString());
			}

			// 发货省市区
			StringBuilder loadAddress = new StringBuilder();
			loadAddress.append(Optional.ofNullable(source.getLoadProvinceName()).orElse("")).
					append(Optional.ofNullable(source.getLoadCityName()).orElse("")).
					append(Optional.ofNullable(source.getLoadAreaName()).orElse(""));
			destination.setLoadDetailAddress(loadAddress.toString());

			// 收货省市区
			StringBuilder unLoadAddress = new StringBuilder();
			unLoadAddress.append(Optional.ofNullable(source.getUnloadProvinceName()).orElse("")).
					append(Optional.ofNullable(source.getUnloadCityName()).orElse("")).
					append(Optional.ofNullable(source.getUnloadAreaName()).orElse(""));
			destination.setUnloadDetailAddress(unLoadAddress.toString());

			destination.setGoodsUnit(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());
			//数量
			destination.setNotArrangedAmount(Optional.ofNullable(source.getNotArrangedAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
			destination.setGoodsAmount(Optional.ofNullable(source.getGoodsAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
			destination.setArrangedAmount(Optional.ofNullable(source.getArrangedAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
			destination.setBackAmount(Optional.ofNullable(source.getBackAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());

			//转换需求单状态
			if (CommonConstant.INTEGER_ONE.equals(source.getIfCancel())) {
				destination.setStatus(DemandOrderStatusEnum.CANCEL_DISPATCH.getKey().toString());
				destination.setStatusDesc(DemandOrderStatusEnum.CANCEL_DISPATCH.getValue());
			} else {
				destination.setStatusDesc(DemandOrderStatusEnum.getEnum(source.getStatus()).getValue());
			}


			destination.setEntrustTypeDesc(EntrustTypeEnum.getEnum(source.getEntrustType()).getValue());
		}
	}
}
