<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCustomerSmsAuthInfoMapper">
    <select id="verificationCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_customer_sms_auth_info
        where valid = 1
        and account_id = #{accountId,jdbcType=BIGINT}
        and whether_use = 0
        and expire_time >= now()
        order by expire_time desc
        limit 1
    </select>
</mapper>