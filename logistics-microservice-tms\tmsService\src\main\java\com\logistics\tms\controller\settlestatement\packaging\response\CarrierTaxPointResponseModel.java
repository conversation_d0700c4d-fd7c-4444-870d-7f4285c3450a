package com.logistics.tms.controller.settlestatement.packaging.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/15
 */
@Data
public class CarrierTaxPointResponseModel {

	@ApiModelProperty("临时费用费点")
	private BigDecimal otherFeeTaxPoint;

	@ApiModelProperty("运费费点")
	private BigDecimal freightTaxPoint;
}
