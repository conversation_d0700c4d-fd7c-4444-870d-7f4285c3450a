package com.logistics.tms.biz.demandorderobjectionsinopec

import com.logistics.tms.api.feign.demandorderobjectionsinopec.model.GetSinopecObjectionDetailRequestModel
import com.logistics.tms.api.feign.demandorderobjectionsinopec.model.GetSinopecObjectionDetailResponseModel
import com.logistics.tms.api.feign.demandorderobjectionsinopec.model.SearchDemandOrderObjectionSinopecRequestModel
import com.logistics.tms.api.feign.demandorderobjectionsinopec.model.SearchDemandOrderObjectionSinopecResponseModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.biz.demandorder.SinopecDemandOrderBiz
import com.logistics.tms.entity.TCertificationPictures
import com.logistics.tms.mapper.TCertificationPicturesMapper
import com.logistics.tms.mapper.TDemandOrderMapper
import com.logistics.tms.mapper.TDemandOrderObjectionSinopecMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class DemandOrderObjectionSinopecBizTest extends Specification {
    @Mock
    TDemandOrderObjectionSinopecMapper tDemandOrderObjectionSinopecMapper
    @Mock
    TDemandOrderMapper tDemandOrderMapper
    @Mock
    TCertificationPicturesMapper tCertificationPicturesMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    SinopecDemandOrderBiz sinopecDemandOrderBiz
    @InjectMocks
    DemandOrderObjectionSinopecBiz demandOrderObjectionSinopecBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Sinopec Objection where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tDemandOrderObjectionSinopecMapper.searchSinopecObjection(any())).thenReturn([new SearchDemandOrderObjectionSinopecResponseModel()])
        when(tCertificationPicturesMapper.getTPicsByIds(anyString(), anyString())).thenReturn([new TCertificationPictures(objectId: 1l, filePath: "filePath")])

        expect:
        demandOrderObjectionSinopecBiz.searchSinopecObjection(requestModel) == expectedResult

        where:
        requestModel                                        || expectedResult
        new SearchDemandOrderObjectionSinopecRequestModel() || null
    }

    @Unroll
    def "get Sinopec Objection Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tDemandOrderObjectionSinopecMapper.getSinopecObjectionDetail(anyLong())).thenReturn(new GetSinopecObjectionDetailResponseModel())
        when(tCertificationPicturesMapper.getTPicsByIds(anyString(), anyString())).thenReturn([new TCertificationPictures(filePath: "filePath")])

        expect:
        demandOrderObjectionSinopecBiz.getSinopecObjectionDetail(requestModel) == expectedResult

        where:
        requestModel                                || expectedResult
        new GetSinopecObjectionDetailRequestModel() || new GetSinopecObjectionDetailResponseModel()
    }

    @Unroll
    def "sinopec Objection Audit where requestModel=#requestModel"() {
        given:
        when(tCertificationPicturesMapper.batchInsert(any())).thenReturn(0)
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")

        expect:
        demandOrderObjectionSinopecBiz.sinopecObjectionAudit(requestModel)
        assert expectedResult == false

        where:
        requestModel                                                                                          || expectedResult
        new com.logistics.tms.api.feign.demandorderobjectionsinopec.model.SinopecObjectionAuditRequestModel() || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme