package com.logistics.appapi.client.reservationorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/8/14 15:04
 */
@Data
public class ReservationOrderConfirmSignRequestModel {

    /**
     * 预约单id
     */
    private Long reservationOrderId;

    /**
     * 定位-经度
     */
    private String longitude;
    /**
     * 定位-纬度
     */
    private String latitude;

    /**
     * 来源 0。小程序司机 1.h5访客越野
     */
    private Integer source;

    @ApiModelProperty("H5验证过的手机号")
//    @NotBlank(message = "H5验证过的手机号不能为空")
    private String mobilePhone;
}
