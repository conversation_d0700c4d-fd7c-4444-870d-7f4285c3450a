package com.logistics.management.webapi.api.feign.insuarance.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/6/4 17:47
 */
@Data
public class SearchInsuranceListResponseDto {
    @ApiModelProperty("保险id")
    private String insuranceId = "";
    @ApiModelProperty("保单状态：1 保障中，2 已过期，3 未开始，4 已作废，5 已退保")
    private String policyStatus = "";
    private String policyStatusDesc = "";
    @ApiModelProperty("车辆机构：1 自主 2 外部 3 自营")
    private String vehicleProperty = "";
    @ApiModelProperty("车辆机构展示文本")
    private String vehiclePropertyLabel = "";
    @ApiModelProperty("结算状态：结算状态：-1 未开始，0 待结算，1 部分结算，2 已结算")
    private String settlementStatus = "";
    private String settlementStatusDesc = "";
    @ApiModelProperty("车牌号")
    private String vehicleNo = "";
    @ApiModelProperty("司机")
    private String driverName = "";
    @ApiModelProperty("司机电话")
    private String driverPhone = "";
    @ApiModelProperty("险种")
    private String insuranceType = "";
    @ApiModelProperty("险种")
    private String insuranceTypeDesc = "";
    @ApiModelProperty("保险公司")
    private String insuranceCompanyName = "";
    @ApiModelProperty("保单号")
    private String policyNumber = "";
    @ApiModelProperty("保费合计")
    private String premium = "";
    @ApiModelProperty("未还保险费")
    private String outstandingPremium = "";
    @ApiModelProperty("退保金额")
    private String refundPremium="";
    @ApiModelProperty("保险生效时间")
    private String startTime = "";
    @ApiModelProperty("保险截止时间")
    private String endTime = "";
    @ApiModelProperty("备注")
    private String remark = "";
    @ApiModelProperty("新增人")
    private String addUserName = "";
    @ApiModelProperty("操作人")
    private String lastModifiedBy = "";
    @ApiModelProperty("操作时间")
    private String lastModifiedTime = "";

    @ApiModelProperty("导出保单号")
    private String policyNumberExport = "";
    @ApiModelProperty("导出批单号")
    private String batchNumberExport = "";
    @ApiModelProperty("导出代缴车船税")
    private String paymentOfVehicleAndVesselTax = "";
    @ApiModelProperty("导出司机")
    private String driverNamePhone = "";

    @ApiModelProperty(value = "是否需要确认,1需要，0不需要")
    private String ifNeedConfirm = "0";

    @ApiModelProperty(value = "保险结算月份")
    private String insuranceSettlementMonths;

    @ApiModelProperty(value = "险种,保险结算类型")
    private String insuranceSettlementTypes;

}
