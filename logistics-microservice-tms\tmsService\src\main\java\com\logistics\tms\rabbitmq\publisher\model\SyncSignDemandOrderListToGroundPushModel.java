package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2023/9/22 9:38
 */
@Data
public class SyncSignDemandOrderListToGroundPushModel {

    @ApiModelProperty("云盘客户id")
    private Long trayCustomerCompanyId;
    @ApiModelProperty("云盘地址id")
    private Long trayCustomerAddressId;
    @ApiModelProperty("大类id")
    private Long productCategoryId;

    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("委托数量")
    private BigDecimal goodsAmount;

    @ApiModelProperty("已安排数量")
    private BigDecimal arrangedAmount;
}
