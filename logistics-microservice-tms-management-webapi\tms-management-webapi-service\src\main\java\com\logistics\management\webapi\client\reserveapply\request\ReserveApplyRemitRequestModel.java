package com.logistics.management.webapi.client.reserveapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ReserveApplyRemitRequestModel {

    @ApiModelProperty(value = "申请记录id", required = true)
    private Long applyId;

    @ApiModelProperty(value = "打款日期", required = true)
    private Date payTime;

    @ApiModelProperty(value = "打款编号0<=长度<=50")
    private String remitCode;

    @ApiModelProperty(value = "打款凭据,1-2张图片", required = true)
    private List<String> tickets;

    @ApiModelProperty(value = "备注 长度<=100")
    private String remark;
}
