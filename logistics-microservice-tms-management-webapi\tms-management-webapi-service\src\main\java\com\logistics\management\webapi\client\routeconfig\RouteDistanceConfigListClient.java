package com.logistics.management.webapi.client.routeconfig;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.routeconfig.hystrix.RouteDistanceConfigListHystrix;
import com.logistics.management.webapi.client.routeconfig.request.*;
import com.logistics.management.webapi.client.routeconfig.response.RouteDistanceConfigDetailResponseModel;
import com.logistics.management.webapi.client.routeconfig.response.RouteDistanceConfigRecommendResponseModel;
import com.logistics.management.webapi.client.routeconfig.response.RouteDistanceConfigResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = FeignClientName.TMS_SERVICES, fallback = RouteDistanceConfigListHystrix.class)
public interface RouteDistanceConfigListClient {

    @PostMapping(value = "/service/route/distance/config/searchList")
    @ApiOperation(value = "路线距离配置列表", tags = "1.3.5")
    Result<PageInfo<RouteDistanceConfigResponseModel>> searchList(@RequestBody RouteDistanceConfigListRequestModel requestModel);

    @PostMapping(value = "/service/route/distance/config/detail")
    @ApiOperation(value = "路线距离配置详情", tags = "1.3.5")
    Result<RouteDistanceConfigDetailResponseModel> detail(@RequestBody RouteDistanceConfigRequestModel requestModel);

    @PostMapping(value = "/service/route/distance/config/add")
    @ApiOperation(value = "路线距离配置新增", tags = "1.3.5")
    Result<Boolean> add(@RequestBody RouteDistanceConfigAddRequestModel requestModel);

    @PostMapping(value = "/service/route/distance/config/edit")
    @ApiOperation(value = "路线距离配置编辑", tags = "1.3.5")
    Result<Boolean> edit(@RequestBody RouteDistanceConfigEditRequestModel requestModel);

    @PostMapping(value = "/service/route/distance/config/delete")
    @ApiOperation(value = "路线距离配置删除", tags = "1.3.5")
    Result<Boolean> delete(@RequestBody RouteDistanceConfigRequestModel requestModel);

    @PostMapping(value = "/service/route/distance/config/recommend")
    @ApiOperation(value = "路线距离配置推荐", tags = "1.3.5")
    Result<RouteDistanceConfigRecommendResponseModel> recommend(@RequestBody RouteDistanceConfigRecommendRequestModel requestModel) ;
}
