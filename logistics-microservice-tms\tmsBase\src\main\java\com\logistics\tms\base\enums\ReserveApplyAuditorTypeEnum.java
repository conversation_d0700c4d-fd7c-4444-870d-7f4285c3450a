package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum ReserveApplyAuditorTypeEnum {

    DEFAULT(0, ""),
    AUDIT_BUSINESS_TYPE(1, "业务审核"),
    AUDIT_FINANCIAL_TYPE(2, "财务审核"),
    ;

    private Integer key;
    private String value;

    public static ReserveApplyAuditorTypeEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
