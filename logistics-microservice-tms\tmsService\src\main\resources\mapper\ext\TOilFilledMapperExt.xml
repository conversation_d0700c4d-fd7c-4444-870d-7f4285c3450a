<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TOilFilledMapper">
    <select id="getOilFilledList" resultType="com.logistics.tms.controller.oilfilled.response.OilFilledListResponseModel">
        select
        id                  as oilFilledId,
        status              as status,
        vehicle_no          as vehicleNo,
        vehicle_property    as vehicleProperty,
        name                as name,
        mobile              as mobile,
        source,
        oil_filled_fee      as oilFilledFee,
        oil_filled_date     as oilFilledDate,
        oil_filled_type     as oilFilledType,
        liter               as liter,
        top_up_integral     as topUpIntegral,
        reward_integral     as rewardIntegral,
        sub_card_number     as subCardNumber,
        sub_card_owner      as subCardOwner,
        cooperation_company as cooperationCompany,
        refund_reason_type  as refundReasonType,
        refund_reason       as refundReason,
        remark              as remark,
        last_modified_by    as lastModifiedBy,
        last_modified_time  as lastModifiedTime
        from t_oil_filled
        where valid = 1
        <if test="params.status != null">
            and status = #{params.status,jdbcType=INTEGER}
        </if>
        <if test="params.oilFilledType != null">
            and oil_filled_type = #{params.oilFilledType,jdbcType=INTEGER}
        </if>
        <if test="params.vehicleNo != null and params.vehicleNo != ''">
            and instr(vehicle_no, #{params.vehicleNo,jdbcType=VARCHAR}) &gt; 0
        </if>
        <if test="params.name != null and params.name != ''">
            and (instr(name, #{params.name,jdbcType=VARCHAR}) &gt; 0 or instr(mobile, #{params.name,jdbcType=VARCHAR}) &gt; 0)
        </if>
        <if test="params.source != null">
            and source = #{params.source,jdbcType=INTEGER}
        </if>
        <if test="params.subCardNumber != null and params.subCardNumber != ''">
            and instr(sub_card_number, #{params.subCardNumber,jdbcType=VARCHAR}) &gt; 0
        </if>
        <if test="params.subCardOwner != null and params.subCardOwner != ''">
            and instr(sub_card_owner, #{params.subCardOwner,jdbcType=VARCHAR}) &gt; 0
        </if>
        <if test="params.oilFilledDateStart != null and params.oilFilledDateStart != ''">
            and oil_filled_date &gt;= DATE_FORMAT(#{params.oilFilledDateStart,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%S')
        </if>
        <if test="params.oilFilledDateEnd != null and params.oilFilledDateEnd != ''">
            and oil_filled_date &lt;= DATE_FORMAT(#{params.oilFilledDateEnd,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="params.oilFilledIds != null and params.oilFilledIds != ''">
            and id in (${params.oilFilledIds})
        </if>
        <if test="params.vehicleProperty != null">
            and vehicle_property = #{params.vehicleProperty,jdbcType=INTEGER}
        </if>
        order by created_time desc, id desc
    </select>
    <select id="getSummary" resultType="com.logistics.tms.controller.oilfilled.response.OilFilledGetSummaryResponseModel">
        select
        ifnull(sum(if(status=0,1,0)),0) as waitSettleCount,
        ifnull(sum(if(status=1,1,0)),0) as haveSettleCount
        from t_oil_filled
        where valid=1
        <if test="params.oilFilledType!=null">
            and oil_filled_type = #{params.oilFilledType,jdbcType=INTEGER}
        </if>
        <if test="params.vehicleNo!=null and params.vehicleNo!=''">
            and instr(vehicle_no,#{params.vehicleNo,jdbcType=VARCHAR}) &gt;0
        </if>
        <if test="params.name!=null and params.name!=''">
            and instr(name,#{params.name,jdbcType=VARCHAR}) &gt;0
        </if>
        <if test="params.source != null">
            and source = #{params.source,jdbcType=INTEGER}
        </if>
        <if test="params.subCardNumber!=null and params.subCardNumber!=''">
            and instr(sub_card_number,#{params.subCardNumber,jdbcType=VARCHAR}) &gt;0
        </if>
        <if test="params.subCardOwner!=null and params.subCardOwner!=''">
            and instr(sub_card_owner,#{params.subCardOwner,jdbcType=VARCHAR}) &gt;0
        </if>
        <if test="params.oilFilledDateStart != null and params.oilFilledDateStart != ''">
            and oil_filled_date &gt;= DATE_FORMAT(#{params.oilFilledDateStart,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="params.oilFilledDateEnd != null and params.oilFilledDateEnd != ''">
            and oil_filled_date &lt;= DATE_FORMAT(#{params.oilFilledDateEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.oilFilledIds!=null and params.oilFilledIds!=''">
            and id in (${params.oilFilledIds})
        </if>
        <if test="params.vehicleProperty != null">
            and vehicle_property = #{params.vehicleProperty,jdbcType=INTEGER}
        </if>
    </select>

    <select id="getOilFilledByIdsForSettlement" resultType="com.logistics.tms.controller.vehiclesettlement.response.GetOilFilledByVehicleIdResponseModel">
        select
        id as oilFilledId,
        vehicle_no as vehicleNo,
        name as driverName,
        mobile as driverMobile,
        source,
        oil_filled_fee as oilFilledFee,
        oil_filled_date as oilFilledDate,
        oil_filled_type as oilFilledType,
        liter as liter,
        top_up_integral as topUpIntegral,
        reward_integral as rewardIntegral,
        sub_card_number as subCardNumber,
        sub_card_owner as subCardOwner,
        cooperation_company as cooperationCompany,
        refund_reason_type as refundReasonType,
        last_modified_by as lastModifiedBy,
        last_modified_time as lastModifiedTime
        from t_oil_filled
        where valid = 1
        and id in (${ids})
        order by created_time desc,id desc
    </select>

    <update id="settlementOilFilledByIds">
        update t_oil_filled set
        status = 1,
        last_modified_by = #{userName,jdbcType=VARCHAR},
        last_modified_time = now()
        where valid = 1
        and id in (${ids})
    </update>
    <update id="rollbackSettlementOilFilledByIds">
        update t_oil_filled set
        status = 0,
        last_modified_by = #{userName,jdbcType=VARCHAR},
        last_modified_time = now()
        where valid = 1
        and id in (${ids})
    </update>

    <select id="getVehicleBySettlementMonth" resultType="com.logistics.tms.controller.vehiclesettlement.response.GetVehicleBySettlementMonthModel">
        select
        vehicle_id as vehicleId,
        id as objectId
        from t_oil_filled
        where valid = 1
        and date_format(oil_filled_date,'%Y-%m') = #{settlementMonth,jdbcType=VARCHAR}
        and source = 10
    </select>

    <select id="getRefundCountByVehicleId" resultType="java.lang.Integer">
        select
        count(0)
        from t_oil_filled
        where valid = 1
        and source = 20
        and vehicle_id = #{vehicleId,jdbcType=BIGINT}
        and date_format(created_time,'%Y-%m') = date_format(now(),'%Y-%m')
    </select>
</mapper>