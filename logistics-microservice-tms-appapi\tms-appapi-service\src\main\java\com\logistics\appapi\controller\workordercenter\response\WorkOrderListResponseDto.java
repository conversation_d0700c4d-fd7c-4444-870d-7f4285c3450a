package com.logistics.appapi.controller.workordercenter.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/14
 */
@Data
public class WorkOrderListResponseDto {

	@ApiModelProperty("工单id")
	private String workOrderId = "";

	@ApiModelProperty("状态：0 待处理，10 处理中，20 已处理，30 已关闭，40 已撤销")
	private String status = "";

	@ApiModelProperty("状态展示label")
	private String statusLabel = "";

	@ApiModelProperty("运单号")
	private String carrierOrderCode = "";

	@ApiModelProperty("提货地址")
	private String loadDetailAddress = "";

	@ApiModelProperty("提货地址联系人")
	private String loadPerson = "";

	@ApiModelProperty("提货地址联系人联系方式")
	private String loadMobile = "";
}
