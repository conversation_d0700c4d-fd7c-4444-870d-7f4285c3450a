package com.logistics.tms.mapper;

import com.logistics.tms.controller.reservebalance.request.ReserveBalanceDetailRequestModel;
import com.logistics.tms.controller.reservebalance.response.ReserveBalanceDetailResponseModel;
import com.logistics.tms.entity.TReserveBalanceRunningRecord;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2022/12/06
*/
@Mapper
public interface TReserveBalanceRunningRecordMapper extends BaseMapper<TReserveBalanceRunningRecord> {

    List<ReserveBalanceDetailResponseModel> reserveBalanceDetailGroupMonth(@Param("params")ReserveBalanceDetailRequestModel params);
}