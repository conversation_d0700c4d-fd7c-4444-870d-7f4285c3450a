package com.logistics.management.webapi.client.dispatch;

import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.dispatch.hystrix.DispatchClientHystrix;
import com.logistics.management.webapi.client.dispatch.request.*;
import com.logistics.management.webapi.client.dispatch.response.DemandOrderSpecialDispatchDetailResponseModel;
import com.logistics.management.webapi.client.dispatch.response.SearchCanJoinShippingOrderRespModel;
import com.logistics.management.webapi.client.dispatch.response.SearchSpecialDispatchIfMatchFreightRespModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * @Author: dongya.li
 * @Date: 2019/9/12 13:25
 * @Description:
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/dispatch",
        fallback = DispatchClientHystrix.class)
public interface DispatchClient {

    @ApiOperation(value = "调度车辆-确认调度")
    @PostMapping(value = "/dispatchVehicle")
    Result<Boolean> dispatchVehicle(@RequestBody DispatchRequestModel requestModel);

    /**
     * 零担调度详情
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/specialDispatchDetail")
    Result<DemandOrderSpecialDispatchDetailResponseModel> specialDispatchDetail(@RequestBody DemandOrderSpecialDispatchDetailRequestModel requestModel);

    /**
     * 零担调度车辆
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/specialDispatchVehicle")
    Result<Boolean> specialDispatchVehicle(@RequestBody SpecialDispatchVehicleRequestModel requestModel);

    /**
     * 零担调度查询是否有匹配的串点和车长费用 2.42
     *
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/searchSpecialDispatchIfMatchFreight")
    public Result<SearchSpecialDispatchIfMatchFreightRespModel> searchSpecialDispatchIfMatchFreight(@RequestBody @Valid SearchSpecialDispatchIfMatchFreightReqModel requestDto);


    /**
     * 零担调度 根据车牌号查询可加入的零担 2.42
     */
    @PostMapping(value = "/searchCanJoinShippingOrder")
    public Result<List<SearchCanJoinShippingOrderRespModel>> searchCanJoinShippingOrder(@RequestBody @Valid SearchCanJoinShippingOrderRequestModel requestModel);

}
