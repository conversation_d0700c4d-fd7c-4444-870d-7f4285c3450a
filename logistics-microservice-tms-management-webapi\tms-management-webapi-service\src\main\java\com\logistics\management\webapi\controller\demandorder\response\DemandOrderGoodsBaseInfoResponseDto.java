package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/5/16 8:56
 */
@Data
public class DemandOrderGoodsBaseInfoResponseDto {
    @ApiModelProperty("货物品名")
    private String goodsName;
    @ApiModelProperty("规格")
    private String goodsSize;
    @ApiModelProperty("货物单位：1 件，2 吨,3方,4块")
    private String goodsUnit;
    @ApiModelProperty("货物数量")
    private String goodsAmount;
    private String length;
    private String width;
    private String height;

}
