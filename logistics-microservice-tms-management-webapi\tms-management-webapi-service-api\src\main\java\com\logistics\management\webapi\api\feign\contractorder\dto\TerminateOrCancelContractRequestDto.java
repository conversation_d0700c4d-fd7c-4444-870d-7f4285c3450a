package com.logistics.management.webapi.api.feign.contractorder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class TerminateOrCancelContractRequestDto {
    @ApiModelProperty("合同Id")
    @NotBlank(message = "合同id不能为空")
    private String contractId;
    @ApiModelProperty("操作类型 1终止 2作废")
    @NotBlank(message = "操作类型不能为空")
    private String type;
    @ApiModelProperty("终止或作废原因")
    @NotBlank(message = "原因不能为空")
    private String remark;
}
