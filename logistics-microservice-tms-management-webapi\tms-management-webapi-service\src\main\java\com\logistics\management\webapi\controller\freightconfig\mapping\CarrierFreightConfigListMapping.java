package com.logistics.management.webapi.controller.freightconfig.mapping;

import com.alibaba.excel.util.DateUtils;
import com.logistics.management.webapi.base.enums.ConfigLabelEnum;
import com.logistics.management.webapi.base.enums.EntrustTypeEnum;
import com.logistics.management.webapi.client.freightconfig.response.CarrierFreightConfigListResponseModel;
import com.logistics.management.webapi.controller.freightconfig.response.CarrierFreightConfigListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: sj
 * @Date: 2019/9/29 9:24
 */
public class CarrierFreightConfigListMapping extends MapperMapping<CarrierFreightConfigListResponseModel, CarrierFreightConfigListResponseDto> {
    @Override
    public void configure() {
        CarrierFreightConfigListResponseModel source = this.getSource();
        CarrierFreightConfigListResponseDto destination = this.getDestination();
        if(source!=null){
            if(Objects.nonNull(source.getConfigType())){
                destination.setConfigTypeLabel(ConfigLabelEnum.getEnum(source.getConfigType()).getValue());
            }
            if(Objects.nonNull(source.getCreatedTime())){
                destination.setCreatedTime(DateUtils.format(source.getCreatedTime()));
            }
            if(Objects.nonNull(source.getLastModifiedTime())){
                destination.setLastModifiedTime(DateUtils.format(source.getLastModifiedTime()));
            }
            if(Objects.nonNull(source.getEntrustType())){
                String entryType = Arrays.stream(source.getEntrustType().split(","))
                        .map(s -> EntrustTypeEnum.getEnum(Integer.valueOf(s))
                                .getValue()).collect(Collectors.joining("、"));
                destination.setEntrustTypesLabel(entryType);
            }

        }
    }
}
