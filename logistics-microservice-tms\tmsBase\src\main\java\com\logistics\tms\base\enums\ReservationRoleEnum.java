package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * @author: wjf
 * @date: 2024/8/22 11:01
 */
@AllArgsConstructor
@Getter
public enum ReservationRoleEnum {

    DEFAULT(-99999, ""),
    DRIVER(0, "司机"),
    VISITOR(1, "访客"),
    CARRIER(2, "车主"),
    ;

    private final Integer key;
    private final String value;


    public static ReservationRoleEnum getEnum(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }

}
