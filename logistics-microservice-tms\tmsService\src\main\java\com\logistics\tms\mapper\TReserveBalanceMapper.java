package com.logistics.tms.mapper;

import com.logistics.tms.controller.reservebalance.request.DriverReserveBalanceListRequestModel;
import com.logistics.tms.controller.reservebalance.response.DriverReserveBalanceListResponseModel;
import com.logistics.tms.biz.reservebalance.model.StatisticsReserveModel;
import com.logistics.tms.biz.reservebalance.model.UpdateBalanceModel;
import com.logistics.tms.entity.TReserveBalance;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2022/12/06
*/
@Mapper
public interface TReserveBalanceMapper extends BaseMapper<TReserveBalance> {

    StatisticsReserveModel statisticsReserve();

    List<DriverReserveBalanceListResponseModel> selectSearchList(@Param("params")DriverReserveBalanceListRequestModel params);

    String selectDriverNameById(@Param("id")Long id);

    TReserveBalance selectOneByDriverId(@Param("driverId") Long driverId);

    List<TReserveBalance> selectBalanceByDriverIds(@Param("driverIds") List<Long> driverIds);

    int updateBalanceById(@Param("updateModel")UpdateBalanceModel updateModel);

    void createBalance(TReserveBalance reserveBalance);
}