package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WorkGroupAmountTypeEnum {

    DEFAULT(0, ""),

    ENTRUST_AMOUNT(101, "委托数"),
    ARRANGED_AMOUNT(102, "已安排"),
    NOT_ARRANGED_AMOUNT(103, "未安排"),

    EXPECT_AMOUNT(201, "预提数"),
    LOAD_AMOUNT(202, "实提数"),
    DIFFERENCE_AMOUNT(203, "差异数(预提-实提)"),
    ;

    private final Integer key;
    private final String value;

    public static WorkGroupAmountTypeEnum getEnum(Integer key) {
        for (WorkGroupAmountTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
