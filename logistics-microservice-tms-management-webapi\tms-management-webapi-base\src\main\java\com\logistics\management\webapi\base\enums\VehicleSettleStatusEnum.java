package com.logistics.management.webapi.base.enums;

/**
 * @Author: wjf
 * @Date: 2019/11/20 15:21
 */
public enum VehicleSettleStatusEnum {
    DEFAULT(-99,""),
    NOT_PAYMENT(0, "未支付"),
    PAYMENT(1, "已支付"),
    ;
    private Integer key;
    private String value;

    VehicleSettleStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static VehicleSettleStatusEnum getEnum(Integer key) {
        for (VehicleSettleStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
