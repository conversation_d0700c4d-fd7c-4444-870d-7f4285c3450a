<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandOrderAddressMapper">
    <insert id="batchInsertSelective" parameterType="com.logistics.tms.entity.TDemandOrderAddress">
        <foreach collection="list" separator=";" item="item">
            insert into t_demand_order_address
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.demandOrderId != null">
                    demand_order_id,
                </if>
                <if test="item.loadYeloAddressCode != null">
                    load_yelo_address_code,
                </if>
                <if test="item.loadAddressCode != null">
                    load_address_code,
                </if>
                <if test="item.loadProvinceId != null">
                    load_province_id,
                </if>
                <if test="item.loadProvinceName != null">
                    load_province_name,
                </if>
                <if test="item.loadCityId != null">
                    load_city_id,
                </if>
                <if test="item.loadCityName != null">
                    load_city_name,
                </if>
                <if test="item.loadAreaId != null">
                    load_area_id,
                </if>
                <if test="item.loadAreaName != null">
                    load_area_name,
                </if>
                <if test="item.loadDetailAddress != null">
                    load_detail_address,
                </if>
                <if test="item.loadWarehouse != null">
                    load_warehouse,
                </if>
                <if test="item.loadCompany != null">
                    load_company,
                </if>
                <if test="item.loadLongitude != null">
                    load_longitude,
                </if>
                <if test="item.loadLatitude != null">
                    load_latitude,
                </if>
                <if test="item.consignorName != null">
                    consignor_name,
                </if>
                <if test="item.consignorMobile != null">
                    consignor_mobile,
                </if>
                <if test="item.expectedLoadTime != null">
                    expected_load_time,
                </if>
                <if test="item.loadRegionId != null">
                    load_region_id,
                </if>
                <if test="item.loadRegionName != null">
                    load_region_name,
                </if>
                <if test="item.loadRegionContactName != null">
                    load_region_contact_name,
                </if>
                <if test="item.loadRegionContactPhone != null">
                    load_region_contact_phone,
                </if>
                <if test="item.unloadAddressCode != null">
                    unload_address_code,
                </if>
                <if test="item.unloadProvinceId != null">
                    unload_province_id,
                </if>
                <if test="item.unloadProvinceName != null">
                    unload_province_name,
                </if>
                <if test="item.unloadCityId != null">
                    unload_city_id,
                </if>
                <if test="item.unloadCityName != null">
                    unload_city_name,
                </if>
                <if test="item.unloadAreaId != null">
                    unload_area_id,
                </if>
                <if test="item.unloadAreaName != null">
                    unload_area_name,
                </if>
                <if test="item.unloadDetailAddress != null">
                    unload_detail_address,
                </if>
                <if test="item.unloadWarehouse != null">
                    unload_warehouse,
                </if>
                <if test="item.unloadCompany != null">
                    unload_company,
                </if>
                <if test="item.unloadLongitude != null">
                    unload_longitude,
                </if>
                <if test="item.unloadLatitude != null">
                    unload_latitude,
                </if>
                <if test="item.unloadAddressIsAmend != null">
                    unload_address_is_amend,
                </if>
                <if test="item.receiverName != null">
                    receiver_name,
                </if>
                <if test="item.receiverMobile != null">
                    receiver_mobile,
                </if>
                <if test="item.expectedUnloadTime != null">
                    expected_unload_time,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.demandOrderId != null">
                    #{item.demandOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.loadYeloAddressCode != null">
                    #{item.loadYeloAddressCode,jdbcType=VARCHAR},
                </if>
                <if test="item.loadAddressCode != null">
                    #{item.loadAddressCode,jdbcType=VARCHAR},
                </if>
                <if test="item.loadProvinceId != null">
                    #{item.loadProvinceId,jdbcType=BIGINT},
                </if>
                <if test="item.loadProvinceName != null">
                    #{item.loadProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.loadCityId != null">
                    #{item.loadCityId,jdbcType=BIGINT},
                </if>
                <if test="item.loadCityName != null">
                    #{item.loadCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.loadAreaId != null">
                    #{item.loadAreaId,jdbcType=BIGINT},
                </if>
                <if test="item.loadAreaName != null">
                    #{item.loadAreaName,jdbcType=VARCHAR},
                </if>
                <if test="item.loadDetailAddress != null">
                    #{item.loadDetailAddress,jdbcType=VARCHAR},
                </if>
                <if test="item.loadWarehouse != null">
                    #{item.loadWarehouse,jdbcType=VARCHAR},
                </if>
                <if test="item.loadCompany != null">
                    #{item.loadCompany,jdbcType=VARCHAR},
                </if>
                <if test="item.loadLongitude != null">
                    #{item.loadLongitude,jdbcType=VARCHAR},
                </if>
                <if test="item.loadLatitude != null">
                    #{item.loadLatitude,jdbcType=VARCHAR},
                </if>
                <if test="item.consignorName != null">
                    #{item.consignorName,jdbcType=VARCHAR},
                </if>
                <if test="item.consignorMobile != null">
                    #{item.consignorMobile,jdbcType=VARCHAR},
                </if>
                <if test="item.expectedLoadTime != null">
                    #{item.expectedLoadTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.loadRegionId != null">
                    #{item.loadRegionId,jdbcType=BIGINT},
                </if>
                <if test="item.loadRegionName != null">
                    #{item.loadRegionName,jdbcType=VARCHAR},
                </if>
                <if test="item.loadRegionContactName != null">
                    #{item.loadRegionContactName,jdbcType=VARCHAR},
                </if>
                <if test="item.loadRegionContactPhone != null">
                    #{item.loadRegionContactPhone,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadAddressCode != null">
                    #{item.unloadAddressCode,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadProvinceId != null">
                    #{item.unloadProvinceId,jdbcType=BIGINT},
                </if>
                <if test="item.unloadProvinceName != null">
                    #{item.unloadProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadCityId != null">
                    #{item.unloadCityId,jdbcType=BIGINT},
                </if>
                <if test="item.unloadCityName != null">
                    #{item.unloadCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadAreaId != null">
                    #{item.unloadAreaId,jdbcType=BIGINT},
                </if>
                <if test="item.unloadAreaName != null">
                    #{item.unloadAreaName,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadDetailAddress != null">
                    #{item.unloadDetailAddress,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadWarehouse != null">
                    #{item.unloadWarehouse,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadCompany != null">
                    #{item.unloadCompany,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadLongitude != null">
                    #{item.unloadLongitude,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadLatitude != null">
                    #{item.unloadLatitude,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadAddressIsAmend != null">
                    #{item.unloadAddressIsAmend,jdbcType=INTEGER},
                </if>
                <if test="item.receiverName != null">
                    #{item.receiverName,jdbcType=VARCHAR},
                </if>
                <if test="item.receiverMobile != null">
                    #{item.receiverMobile,jdbcType=VARCHAR},
                </if>
                <if test="item.expectedUnloadTime != null">
                    #{item.expectedUnloadTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <select id="getByDemandOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_demand_order_address
        where valid = 1
          and demand_order_id = #{demandOrderId,jdbcType = BIGINT}
    </select>

    <select id="getInvalidByDemandOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_demand_order_address
        where demand_order_id = #{demandOrderId,jdbcType = BIGINT}
    </select>

    <select id="getByDemandOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_demand_order_address
        where valid = 1
          and demand_order_id in (${demandOrderIds})
    </select>

    <update id="batchUpdateSelective" parameterType="com.logistics.tms.entity.TDemandOrderAddress">
        <foreach collection="list" separator=";" item="item">
            update t_demand_order_address
            <set>
                <if test="item.demandOrderId != null">
                    demand_order_id = #{item.demandOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.loadYeloAddressCode != null">
                    load_yelo_address_code = #{item.loadYeloAddressCode,jdbcType=VARCHAR},
                </if>
                <if test="item.loadAddressCode != null">
                    load_address_code = #{item.loadAddressCode,jdbcType=VARCHAR},
                </if>
                <if test="item.loadProvinceId != null">
                    load_province_id = #{item.loadProvinceId,jdbcType=BIGINT},
                </if>
                <if test="item.loadProvinceName != null">
                    load_province_name = #{item.loadProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.loadCityId != null">
                    load_city_id = #{item.loadCityId,jdbcType=BIGINT},
                </if>
                <if test="item.loadCityName != null">
                    load_city_name = #{item.loadCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.loadAreaId != null">
                    load_area_id = #{item.loadAreaId,jdbcType=BIGINT},
                </if>
                <if test="item.loadAreaName != null">
                    load_area_name = #{item.loadAreaName,jdbcType=VARCHAR},
                </if>
                <if test="item.loadDetailAddress != null">
                    load_detail_address = #{item.loadDetailAddress,jdbcType=VARCHAR},
                </if>
                <if test="item.loadWarehouse != null">
                    load_warehouse = #{item.loadWarehouse,jdbcType=VARCHAR},
                </if>
                <if test="item.loadCompany != null">
                    load_company = #{item.loadCompany,jdbcType=VARCHAR},
                </if>
                <if test="item.loadLongitude != null">
                    load_longitude = #{item.loadLongitude,jdbcType=VARCHAR},
                </if>
                <if test="item.loadLatitude != null">
                    load_latitude = #{item.loadLatitude,jdbcType=VARCHAR},
                </if>
                <if test="item.consignorName != null">
                    consignor_name = #{item.consignorName,jdbcType=VARCHAR},
                </if>
                <if test="item.consignorMobile != null">
                    consignor_mobile = #{item.consignorMobile,jdbcType=VARCHAR},
                </if>
                <if test="item.expectedLoadTime != null">
                    expected_load_time = #{item.expectedLoadTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.loadRegionId != null">
                    load_region_id = #{item.loadRegionId,jdbcType=BIGINT},
                </if>
                <if test="item.loadRegionName != null">
                    load_region_name = #{item.loadRegionName,jdbcType=VARCHAR},
                </if>
                <if test="item.loadRegionContactName != null">
                    load_region_contact_name = #{item.loadRegionContactName,jdbcType=VARCHAR},
                </if>
                <if test="item.loadRegionContactPhone != null">
                    load_region_contact_phone = #{item.loadRegionContactPhone,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadAddressCode != null">
                    unload_address_code = #{item.unloadAddressCode,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadProvinceId != null">
                    unload_province_id = #{item.unloadProvinceId,jdbcType=BIGINT},
                </if>
                <if test="item.unloadProvinceName != null">
                    unload_province_name = #{item.unloadProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadCityId != null">
                    unload_city_id = #{item.unloadCityId,jdbcType=BIGINT},
                </if>
                <if test="item.unloadCityName != null">
                    unload_city_name = #{item.unloadCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadAreaId != null">
                    unload_area_id = #{item.unloadAreaId,jdbcType=BIGINT},
                </if>
                <if test="item.unloadAreaName != null">
                    unload_area_name = #{item.unloadAreaName,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadDetailAddress != null">
                    unload_detail_address = #{item.unloadDetailAddress,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadWarehouse != null">
                    unload_warehouse = #{item.unloadWarehouse,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadCompany != null">
                    unload_company = #{item.unloadCompany,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadLongitude != null">
                    unload_longitude = #{item.unloadLongitude,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadLatitude != null">
                    unload_latitude = #{item.unloadLatitude,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadAddressIsAmend != null">
                    unload_address_is_amend = #{item.unloadAddressIsAmend,jdbcType=INTEGER},
                </if>
                <if test="item.receiverName != null">
                    receiver_name = #{item.receiverName,jdbcType=VARCHAR},
                </if>
                <if test="item.receiverMobile != null">
                    receiver_mobile = #{item.receiverMobile,jdbcType=VARCHAR},
                </if>
                <if test="item.expectedUnloadTime != null">
                    expected_unload_time = #{item.expectedUnloadTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="dispatchAlarmStatisticsAddress" resultType="java.lang.Long">
        select max(id)
        from t_demand_order_address
        where valid = 1
          and load_city_id in
        <foreach collection="loadCityIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and load_region_contact_name != ''
        group by load_city_id
    </select>

    <select id="getByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_demand_order_address
        where valid = 1
          and id in (${ids})
    </select>
</mapper>