package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TDemandSource extends BaseEntity {
    /**
    * 货物名称
    */
    @ApiModelProperty("货物名称")
    private String goodsName;

    /**
    * 货物数量
    */
    @ApiModelProperty("货物数量")
    private Integer goodsAmount;

    /**
    * 货物单价
    */
    @ApiModelProperty("货物单价")
    private Integer goodsPrice;

    /**
    * 装货地址
    */
    @ApiModelProperty("装货地址")
    private String loadAddress;

    /**
    * 卸货地址
    */
    @ApiModelProperty("卸货地址")
    private String unloadAddress;

    /**
    * 联系人姓名
    */
    @ApiModelProperty("联系人姓名")
    private String contactName;

    /**
    * 联系人手机
    */
    @ApiModelProperty("联系人手机")
    private String contactMobile;
}