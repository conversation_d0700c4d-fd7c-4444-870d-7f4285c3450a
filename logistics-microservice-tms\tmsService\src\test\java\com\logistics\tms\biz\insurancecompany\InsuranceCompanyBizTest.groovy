package com.logistics.tms.biz.insurancecompany

import com.logistics.tms.api.feign.insurancecompany.model.EnableInsuranceCompanyRequestModel
import com.logistics.tms.api.feign.insurancecompany.model.FuzzyQueryInsuranceCompanyListResponseModel
import com.logistics.tms.api.feign.insurancecompany.model.ImportInsuranceCompanyRequestModel
import com.logistics.tms.api.feign.insurancecompany.model.ImportInsuranceCompanyResponseModel
import com.logistics.tms.api.feign.insurancecompany.model.InsuranceCompanyDetailRequestModel
import com.logistics.tms.api.feign.insurancecompany.model.InsuranceCompanyDetailResponseModel
import com.logistics.tms.api.feign.insurancecompany.model.InsuranceCompanyListRequestModel
import com.logistics.tms.api.feign.insurancecompany.model.InsuranceCompanyListResponseModel
import com.logistics.tms.api.feign.insurancecompany.model.SaveOrModifyInsuranceCompanyRequestModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TInsuranceCompany
import com.logistics.tms.mapper.TInsuranceCompanyMapper
import org.slf4j.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class InsuranceCompanyBizTest extends Specification {
    @Mock
    TInsuranceCompanyMapper tInsuranceCompanyMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    Logger log
    @InjectMocks
    InsuranceCompanyBiz insuranceCompanyBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Insurance Company List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tInsuranceCompanyMapper.searchInsuranceCompanyList(any())).thenReturn([new InsuranceCompanyListResponseModel()])

        expect:
        insuranceCompanyBiz.searchInsuranceCompanyList(requestModel) == expectedResult

        where:
        requestModel                           || expectedResult
        new InsuranceCompanyListRequestModel() || null
    }

    @Unroll
    def "save Or Modify Insurance Company where requestModel=#requestModel"() {
        given:
        when(tInsuranceCompanyMapper.findInsuranceCompanyByName(anyString())).thenReturn(new TInsuranceCompany(companyName: "companyName", remark: "remark", addUserId: 1l, addUserName: "addUserName", source: 0, enabled: 0))

        expect:
        insuranceCompanyBiz.saveOrModifyInsuranceCompany(requestModel)
        assert expectedResult == false

        where:
        requestModel                                   || expectedResult
        new SaveOrModifyInsuranceCompanyRequestModel() || true
    }

    @Unroll
    def "batch Import Insurance Company where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tInsuranceCompanyMapper.searchInsuranceCompanyList(any())).thenReturn([new InsuranceCompanyListResponseModel()])
        when(tInsuranceCompanyMapper.batchInsert(any())).thenReturn(0)
        when(tInsuranceCompanyMapper.batchUpdate(any())).thenReturn(0)

        expect:
        insuranceCompanyBiz.batchImportInsuranceCompany(requestModel) == expectedResult

        where:
        requestModel                             || expectedResult
        new ImportInsuranceCompanyRequestModel() || new ImportInsuranceCompanyResponseModel()
    }

    @Unroll
    def "get Detail where requestModel=#requestModel then expect: #expectedResult"() {
        expect:
        insuranceCompanyBiz.getDetail(requestModel) == expectedResult

        where:
        requestModel                             || expectedResult
        new InsuranceCompanyDetailRequestModel() || new InsuranceCompanyDetailResponseModel()
    }

    @Unroll
    def "enable Or Disable where requestModel=#requestModel"() {
        expect:
        insuranceCompanyBiz.enableOrDisable(requestModel)
        assert expectedResult == false

        where:
        requestModel                             || expectedResult
        new EnableInsuranceCompanyRequestModel() || true
    }

    @Unroll
    def "fuzzy Query Insurance Company where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tInsuranceCompanyMapper.queryInsuranceCompanyByName(any())).thenReturn([new FuzzyQueryInsuranceCompanyListResponseModel()])

        expect:
        insuranceCompanyBiz.fuzzyQueryInsuranceCompany(requestModel) == expectedResult

        where:
        requestModel                                                                                    || expectedResult
        new com.logistics.tms.api.feign.insurancecompany.model.FuzzyQueryInsuranceCompanyRequestModel() || [new com.logistics.tms.api.feign.insurancecompany.model.FuzzyQueryInsuranceCompanyListResponseModel()]
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme