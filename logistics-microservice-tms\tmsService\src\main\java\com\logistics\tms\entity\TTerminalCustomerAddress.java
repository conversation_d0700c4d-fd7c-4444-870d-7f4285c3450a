package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TTerminalCustomerAddress extends BaseEntity {
    /**
    * 仓库
    */
    @ApiModelProperty("仓库")
    private String collectWarehouse;

    /**
    * 省份ID
    */
    @ApiModelProperty("省份ID")
    private Long collectProvinceId;

    /**
    * 省份名字
    */
    @ApiModelProperty("省份名字")
    private String collectProvinceName;

    /**
    * 城市ID
    */
    @ApiModelProperty("城市ID")
    private Long collectCityId;

    /**
    * 城市名字
    */
    @ApiModelProperty("城市名字")
    private String collectCityName;

    /**
    * 县区id
    */
    @ApiModelProperty("县区id")
    private Long collectAreaId;

    /**
    * 县区名字
    */
    @ApiModelProperty("县区名字")
    private String collectAreaName;

    /**
    * 详细地址
    */
    @ApiModelProperty("详细地址")
    private String collectDetailAddress;

    /**
    * 联系人姓名
    */
    @ApiModelProperty("联系人姓名")
    private String collectContactName;

    /**
    * 联系方式
    */
    @ApiModelProperty("联系方式")
    private String collectContactMobile;

    /**
    * 地图链接
    */
    @ApiModelProperty("地图链接")
    private String mapLinkPath;

    /**
    * 客户情况
    */
    @ApiModelProperty("客户情况")
    private String customerSituation;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}