package com.logistics.management.webapi.client.thirdparty.basicdata.file.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/5/8 16:58
 */
@Data
public class IDCardFrontResponseModel {
    @ApiModelProperty("姓名")
    private String name="";
    @ApiModelProperty("性别")
    private String sex="";
    @ApiModelProperty("民族")
    private String national="";
    @ApiModelProperty("出生")
    private String birthday="";
    @ApiModelProperty("住址")
    private String address="";
    @ApiModelProperty("公民身份号码")
    private String number="";
}
