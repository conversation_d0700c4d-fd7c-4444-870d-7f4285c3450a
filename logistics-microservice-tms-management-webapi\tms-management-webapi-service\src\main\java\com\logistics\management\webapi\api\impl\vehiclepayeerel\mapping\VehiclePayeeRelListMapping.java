package com.logistics.management.webapi.api.impl.vehiclepayeerel.mapping;

import com.logistics.management.webapi.api.feign.vehiclepayeerel.dto.SearchVehiclePayeeRelListResponseDto;
import com.logistics.management.webapi.base.enums.VehiclePropertyEnum;
import com.logistics.tms.api.feign.vehiclepayeerel.model.SearchVehiclePayeeRelListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2019/7/11 16:08
 */
public class VehiclePayeeRelListMapping extends MapperMapping<SearchVehiclePayeeRelListResponseModel,SearchVehiclePayeeRelListResponseDto> {
    @Override
    public void configure() {
        SearchVehiclePayeeRelListResponseModel source = getSource();
        SearchVehiclePayeeRelListResponseDto destination = getDestination();
        if (source != null){
            destination.setVehicleProperty(VehiclePropertyEnum.getEnum(source.getVehicleProperty()).getValue());
        }
    }
}
