package com.logistics.tms.biz.demandorder


import com.logistics.tms.api.feign.freight.model.GetPriceByAddressAndAmountResponseModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.biz.email.model.SinopecEntrustOrderModel
import com.logistics.tms.biz.freight.FreightBiz
import com.logistics.tms.biz.sinopec.SinopecBiz
import com.logistics.tms.controller.demandorder.request.BatchPublishSinopecDemandRequestModel
import com.logistics.tms.controller.demandorder.request.BatchPublishSinopecDetailRequestModel
import com.logistics.tms.controller.demandorder.request.CancelSinopecDemandRequestModel
import com.logistics.tms.controller.demandorder.request.PublishSinopecDemandRequestModel
import com.logistics.tms.controller.demandorder.request.PublishSinopecDetailRequestModel
import com.logistics.tms.controller.demandorder.request.ReceiveSinopecDemandRequestModel
import com.logistics.tms.controller.demandorder.request.SaveSinopecReportAbnormalRequestModel
import com.logistics.tms.controller.demandorder.request.SinopecDemandOrderCancelRequestModel
import com.logistics.tms.controller.demandorder.request.SinopecReportAbnormalDetailRequestModel
import com.logistics.tms.controller.demandorder.response.BatchPublishSinopecResponseModel
import com.logistics.tms.controller.demandorder.response.PublishSinopecResponseModel
import com.logistics.tms.controller.demandorder.response.SinopecReportAbnormalDetailResponseModel
import com.logistics.tms.entity.*
import com.logistics.tms.mapper.*
import com.yelo.tools.redis.utils.RedisUtils
import org.codehaus.jackson.map.ObjectMapper
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class SinopecDemandOrderBizTest extends Specification {
    @Mock
    TDemandOrderMapper demandOrderMapper
    @Mock
    TDemandOrderAddressMapper demandOrderAddressMapper
    @Mock
    TDemandOrderGoodsMapper demandOrderGoodsMapper
    @Mock
    TDemandOrderEventsMapper demandOrderEventsMapper
    @Mock
    TDemandOrderOperateLogsMapper demandOrderOperateLogsMapper
    @Mock
    TCarrierOrderMapper tCarrierOrderMapper
    @Mock
    TCarrierOrderOperateLogsMapper tCarrierOrderOperateLogsMapper
    @Mock
    TCarrierOrderEventsMapper tCarrierOrderEventsMapper
    @Mock
    TCompanyEntrustMapper companyEntrustMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TCompanyEntrustMapper tCompanyEntrustMapper
    @Mock
    TCompanyMapper companyMapper
    @Mock
    TCompanyCarrierMapper companyCarrierMapper
    @Mock
    TWarehouseAddressMapper tWarehouseAddressMapper
    @Mock
    ObjectMapper objectMapper
    @Mock
    FreightBiz freightBiz
    @Mock
    DemandOrderBiz demandOrderBiz
    @Mock
    DemandOrderCommonBiz demandOrderCommonBiz
    @Mock
    TSinopecOriginalDataMapper tSinopecOriginalDataMapper
    @Mock
    TSinopecMessageLogMapper tSinopecMessageLogMapper
    @Mock
    SinopecBiz sinopecBiz
    @Mock
    RedisUtils redisUtils
    @Mock
    TDemandOrderObjectionSinopecMapper tDemandOrderObjectionSinopecMapper
    @Mock
    Logger log
    @InjectMocks
    SinopecDemandOrderBiz sinopecDemandOrderBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "save Demand Order where configResult=#configResult and tmpEntrustOrder=#tmpEntrustOrder and addDemandOrderAddressList=#addDemandOrderAddressList"() {
        given:
        when(companyEntrustMapper.getByName(anyString())).thenReturn(new TCompanyEntrust(companyId: 1l, settlementTonnage: 0, type: 0))
        when(commonBiz.getBusinessTypeCode(any(), anyString(), anyString())).thenReturn("getBusinessTypeCodeResponse")
        when(commonBiz.getProvinceCityArea(anyString())).thenReturn(["String": "String"])
        when(tCompanyEntrustMapper.getByName(anyString())).thenReturn(new TCompanyEntrust(companyId: 1l, settlementTonnage: 0, type: 0))
        when(companyCarrierMapper.getByName(anyString())).thenReturn(new TCompanyCarrier())
        when(tWarehouseAddressMapper.getValidWarehouseByNameForSinoper(anyString(), anyString(), anyString())).thenReturn(new TWarehouseAddress(provinceId: 1l, provinceName: "provinceName", cityId: 1l, cityName: "cityName", areaId: 1l, areaName: "areaName"))
        when(freightBiz.getEntrustFreightInfo(any())).thenReturn(new GetPriceByAddressAndAmountResponseModel())

        expect:
        sinopecDemandOrderBiz.saveDemandOrder(tmpEntrustOrder, configResult, addDemandOrderAddressList)
        assert expectedResult == false

        where:
        configResult         | tmpEntrustOrder                | addDemandOrderAddressList                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        || expectedResult
        ["String": "String"] | new SinopecEntrustOrderModel() | [new TDemandOrderAddress(demandOrderId: 1l, loadProvinceId: 1l, loadProvinceName: "loadProvinceName", loadCityId: 1l, loadCityName: "loadCityName", loadAreaId: 1l, loadAreaName: "loadAreaName", loadWarehouse: "loadWarehouse", consignorName: "consignorName", consignorMobile: "consignorMobile", unloadProvinceId: 1l, unloadProvinceName: "unloadProvinceName", unloadCityId: 1l, unloadCityName: "unloadCityName", unloadAreaId: 1l, unloadAreaName: "unloadAreaName", unloadDetailAddress: "unloadDetailAddress", receiverName: "receiverName", receiverMobile: "receiverMobile", expectedUnloadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 14).getTime())] || true
    }

    @Unroll
    def "receive Demand where requestModel=#requestModel"() {
        given:
        when(demandOrderMapper.getValidSinopecOrderByOrderNo(anyString())).thenReturn(new TDemandOrder(entrustStatus: 0, statusUpdateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 14).getTime(), status: 0, source: 0, demandOrderCode: "demandOrderCode", customerOrderCode: "customerOrderCode", publishName: "publishName", publishTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 14).getTime(), ticketTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 14).getTime(), settlementTonnage: 0, goodsAmount: 0 as BigDecimal, notArrangedAmount: 0 as BigDecimal, goodsUnit: 0, companyEntrustId: 1l, companyEntrustName: "companyEntrustName", remark: "remark", orderType: 0, sinopecOrderNo: "sinopecOrderNo", manufacturerName: "manufacturerName", itemTransGroupName: "itemTransGroupName", itemPackSpecName: "itemPackSpecName"))
        when(companyEntrustMapper.getByName(anyString())).thenReturn(new TCompanyEntrust(companyId: 1l, settlementTonnage: 0, type: 0))
        when(commonBiz.getBusinessTypeCode(any(), anyString(), anyString())).thenReturn("getBusinessTypeCodeResponse")
        when(commonBiz.getProvinceCityArea(anyString())).thenReturn(["String": "String"])
        when(tCompanyEntrustMapper.getByName(anyString())).thenReturn(new TCompanyEntrust(companyId: 1l, settlementTonnage: 0, type: 0))
        when(tWarehouseAddressMapper.getValidWarehouseByNameForSinoper(anyString(), anyString(), anyString())).thenReturn(new TWarehouseAddress(provinceId: 1l, provinceName: "provinceName", cityId: 1l, cityName: "cityName", areaId: 1l, areaName: "areaName"))
        when(demandOrderBiz.getDemandOrderOperateLogs(anyLong(), any(), anyString(), anyString())).thenReturn(new TDemandOrderOperateLogs(demandOrderId: 1l))

        expect:
        sinopecDemandOrderBiz.receiveDemand(requestModel)
        assert expectedResult == false

        where:
        requestModel                           || expectedResult
        new ReceiveSinopecDemandRequestModel() || true
    }

    @Unroll
    def "create Demand Order From Original Data where originalData=#originalData"() {
        given:
        when(companyEntrustMapper.getByName(anyString())).thenReturn(new TCompanyEntrust(companyId: 1l, settlementTonnage: 0, type: 0))
        when(commonBiz.getBusinessTypeCode(any(), anyString(), anyString())).thenReturn("getBusinessTypeCodeResponse")
        when(commonBiz.getProvinceCityArea(anyString())).thenReturn(["String": "String"])
        when(tCompanyEntrustMapper.getByName(anyString())).thenReturn(new TCompanyEntrust(companyId: 1l, settlementTonnage: 0, type: 0))
        when(tWarehouseAddressMapper.getValidWarehouseByNameForSinoper(anyString(), anyString(), anyString())).thenReturn(new TWarehouseAddress(provinceId: 1l, provinceName: "provinceName", cityId: 1l, cityName: "cityName", areaId: 1l, areaName: "areaName"))
        when(demandOrderBiz.getDemandOrderOperateLogs(anyLong(), any(), anyString(), anyString())).thenReturn(new TDemandOrderOperateLogs(demandOrderId: 1l))

        expect:
        sinopecDemandOrderBiz.createDemandOrderFromOriginalData(originalData)
        assert expectedResult == false

        where:
        originalData                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  || expectedResult
        new TSinopecOriginalData(sendOperator: "sendOperator", consignName: "consignName", orderNo: "orderNo", manufacturerName: "manufacturerName", expectExeDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 14).getTime(), itemCategory1Name: "itemCategory1Name", itemCategoryName: "itemCategoryName", itemName: "itemName", itemTransGroupName: "itemTransGroupName", itemPackSpecName: "itemPackSpecName", qty: 0 as BigDecimal, sn: "sn", outWarehouseName: "outWarehouseName", recvDistrictName: "recvDistrictName", invalidatingStartDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 14).getTime(), remark: "remark", ifHasDemandOrder: 0) || true
    }

    @Unroll
    def "cancel Demand where requestModel=#requestModel"() {
        given:
        when(demandOrderMapper.getValidSinopecOrderByOrderNo(anyString())).thenReturn(new TDemandOrder(entrustStatus: 0, ifCancel: 0, cancelType: 0, cancelTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 14).getTime(), demandOrderCode: "demandOrderCode", ifObjection: 0, ifObjectionSinopec: 0))
        when(tCarrierOrderMapper.batchUpdateCarrierOrders(any())).thenReturn(0)
        when(tCarrierOrderMapper.getNotCancelByDemandOrderId(anyLong())).thenReturn([new TCarrierOrder(status: 0, ifCancel: 0, cancelOperatorName: "cancelOperatorName", cancelTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 14).getTime(), ifObjection: 0)])
        when(tCarrierOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(tCarrierOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)
        when(demandOrderBiz.getDemandOrderOperateLogs(anyLong(), any(), anyString(), anyString())).thenReturn(new TDemandOrderOperateLogs())
        when(demandOrderBiz.getDemandOrderEvent(anyLong(), any(), anyString())).thenReturn(new TDemandOrderEvents())

        expect:
        sinopecDemandOrderBiz.cancelDemand(requestModel)
        assert expectedResult == false

        where:
        requestModel                          || expectedResult
        new CancelSinopecDemandRequestModel() || true
    }

    @Unroll
    def "sinopec Cancel Demand where tDemandOrder=#tDemandOrder and requestModel=#requestModel"() {
        given:
        when(tCarrierOrderMapper.batchUpdateCarrierOrders(any())).thenReturn(0)
        when(tCarrierOrderMapper.getNotCancelByDemandOrderId(anyLong())).thenReturn([new TCarrierOrder(status: 0, ifCancel: 0, cancelOperatorName: "cancelOperatorName", cancelTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 14).getTime(), ifObjection: 0)])
        when(tCarrierOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(tCarrierOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)
        when(demandOrderBiz.getDemandOrderOperateLogs(anyLong(), any(), anyString(), anyString())).thenReturn(new TDemandOrderOperateLogs())
        when(demandOrderBiz.getDemandOrderEvent(anyLong(), any(), anyString())).thenReturn(new TDemandOrderEvents())

        expect:
        sinopecDemandOrderBiz.sinopecCancelDemand(requestModel, tDemandOrder)
        assert expectedResult == false

        where:
        tDemandOrder                                                                                                                                                                                     | requestModel                          || expectedResult
        new TDemandOrder(entrustStatus: 0, ifCancel: 0, cancelType: 0, cancelTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 14).getTime(), demandOrderCode: "demandOrderCode", ifObjection: 0) | new CancelSinopecDemandRequestModel() || true
    }

    @Unroll
    def "get Address where remark=#remark then expect: #expectedResult"() {
        expect:
        sinopecDemandOrderBiz.getAddress(remark) == expectedResult

        where:
        remark   || expectedResult
        "remark" || "expectedResult"
    }

    @Unroll
    def "get Contact Name where remark=#remark then expect: #expectedResult"() {
        expect:
        sinopecDemandOrderBiz.getContactName(remark) == expectedResult

        where:
        remark   || expectedResult
        "remark" || "expectedResult"
    }

    @Unroll
    def "add Entrust Company where entrustCompanyName=#entrustCompanyName then expect: #expectedResult"() {
        expect:
        sinopecDemandOrderBiz.addEntrustCompany(entrustCompanyName) == expectedResult

        where:
        entrustCompanyName   || expectedResult
        "entrustCompanyName" || new TCompanyEntrust(companyId: 1l, settlementTonnage: 0, type: 0)
    }

    @Unroll
    def "get Mobile where remark=#remark then expect: #expectedResult"() {
        expect:
        sinopecDemandOrderBiz.getMobile(remark) == expectedResult

        where:
        remark   || expectedResult
        "remark" || "expectedResult"
    }

    @Unroll
    def "cancel Sinopec Demand Order where requestModel=#requestModel"() {
        given:
        when(demandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(entrustStatus: 0, ifCancel: 0, cancelTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 14).getTime(), cancelReason: "cancelReason", source: 0, demandOrderCode: "demandOrderCode", customerOrderCode: "customerOrderCode", ifEmpty: 0, orderType: 0, sinopecOrderNo: "sinopecOrderNo", dispatcherName: "dispatcherName", dispatcherPhone: "dispatcherPhone", ifObjectionSinopec: 0)])
        when(demandOrderMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(demandOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)
        when(demandOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(commonBiz.getBaseConfigMap(any())).thenReturn(["String": "String"])
        when(sinopecBiz.refuseConsignOrder(any())).thenReturn("refuseConsignOrderResponse")

        expect:
        sinopecDemandOrderBiz.cancelSinopecDemandOrder(requestModel)
        assert expectedResult == false

        where:
        requestModel                               || expectedResult
        new SinopecDemandOrderCancelRequestModel() || true
    }

    @Unroll
    def "publish Sinopec Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(demandOrderMapper.sinopecPublishDetail(any())).thenReturn([new PublishSinopecResponseModel()])

        expect:
        sinopecDemandOrderBiz.publishSinopecDetail(requestModel) == expectedResult

        where:
        requestModel                           || expectedResult
        new PublishSinopecDetailRequestModel() || new PublishSinopecResponseModel()
    }

    @Unroll
    def "batch Publish Sinopec Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(demandOrderMapper.sinopecPublishDetail(any())).thenReturn([new PublishSinopecResponseModel()])

        expect:
        sinopecDemandOrderBiz.batchPublishSinopecDetail(requestModel) == expectedResult

        where:
        requestModel                                || expectedResult
        new BatchPublishSinopecDetailRequestModel() || new BatchPublishSinopecResponseModel()
    }

    @Unroll
    def "publish Sinopec Demand Order where requestModel=#requestModel"() {
        given:
        when(demandOrderMapper.sinopecPublishDetail(any())).thenReturn([new PublishSinopecResponseModel()])
        when(commonBiz.getBaseConfigMap(any())).thenReturn(["String": "String"])
        when(companyCarrierMapper.getByName(anyString())).thenReturn(new TCompanyCarrier())
        when(sinopecBiz.orderQuotation(any())).thenReturn("orderQuotationResponse")

        expect:
        sinopecDemandOrderBiz.publishSinopecDemandOrder(requestModel)
        assert expectedResult == false

        where:
        requestModel                           || expectedResult
        new PublishSinopecDemandRequestModel() || true
    }

    @Unroll
    def "batch Publish Sinopec Demand Order where requestModel=#requestModel"() {
        given:
        when(demandOrderMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(demandOrderMapper.sinopecPublishDetail(any())).thenReturn([new PublishSinopecResponseModel()])
        when(demandOrderAddressMapper.batchUpdateSelective(any())).thenReturn(0)
        when(demandOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(commonBiz.getBaseConfigMap(any())).thenReturn(["String": "String"])
        when(companyCarrierMapper.getByName(anyString())).thenReturn(new TCompanyCarrier())
        when(sinopecBiz.orderQuotation(any())).thenReturn("orderQuotationResponse")

        expect:
        sinopecDemandOrderBiz.batchPublishSinopecDemandOrder(requestModel)
        assert expectedResult == false

        where:
        requestModel                                || expectedResult
        new BatchPublishSinopecDemandRequestModel() || true
    }

    @Unroll
    def "sinopec Report Abnormal Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(demandOrderMapper.sinopecReportAbnormalDetail(anyLong())).thenReturn(new SinopecReportAbnormalDetailResponseModel())

        expect:
        sinopecDemandOrderBiz.sinopecReportAbnormalDetail(requestModel) == expectedResult

        where:
        requestModel                                  || expectedResult
        new SinopecReportAbnormalDetailRequestModel() || new SinopecReportAbnormalDetailResponseModel()
    }

    @Unroll
    def "save Sinopec Report Abnormal where requestModel=#requestModel"() {
        given:
        when(tDemandOrderObjectionSinopecMapper.selectByDemandOrderId(anyLong())).thenReturn(new TDemandOrderObjectionSinopec(auditStatus: 0))

        expect:
        sinopecDemandOrderBiz.saveSinopecReportAbnormal(requestModel)
        assert expectedResult == false

        where:
        requestModel                                                                              || expectedResult
        new SaveSinopecReportAbnormalRequestModel() || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme