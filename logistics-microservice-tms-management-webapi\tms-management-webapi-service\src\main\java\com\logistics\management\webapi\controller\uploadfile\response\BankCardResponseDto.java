package com.logistics.management.webapi.controller.uploadfile.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/5/8 17:22
 */
@Data
public class BankCardResponseDto {
    @ApiModelProperty("银行名称")
    private String bankName="";
    @ApiModelProperty("银行卡类型，0:不能识别; 1: 借记卡; 2: 信用卡")
    private String bankCardType="";
    @ApiModelProperty("银行卡号")
    private String bankCardNumber="";
    @ApiModelProperty("有效期")
    private String validDate="";
}
