package com.logistics.management.webapi.client.thirdparty.basicdata.file.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/6/21 10:55
 */
@Data
public class VehicleLicenseBackResponseModel {
    @ApiModelProperty("整备质量(KG)")
    private String curbWeight;
    @ApiModelProperty("号牌号码")
    private String vehicleNo;
    @ApiModelProperty("准牵引总质量(KG)")
    private String tractionMassWeight;
    @ApiModelProperty("核定载人数")
    private String authorizedCarryingCapacity;
    @ApiModelProperty("外廓尺寸(mm)")
    private String length;
    private String width;
    private String height;
    @ApiModelProperty("总质量(KG)")
    private String totalWeight;
    @ApiModelProperty("档案编号")
    private String filingNumber;
    @ApiModelProperty("检验记录")
    private String inspectionRecords;
    @ApiModelProperty("燃油类型")
    private String fuelType;
    @ApiModelProperty("核定载质量(KG)")
    private String approvedLoadWeight;
    @ApiModelProperty("备注")
    private String remark;
}
