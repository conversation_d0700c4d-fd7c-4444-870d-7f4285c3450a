package com.logistics.tms.biz.violationregulation;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.controller.vehicleassetmanagement.response.FuzzyQueryVehicleInfoResponseModel;
import com.yelo.tools.context.BaseContextHandler;
import com.logistics.tms.api.feign.violationregulation.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.constant.ConfigKeyConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.LocalStringUtil;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TCertificationPictures;
import com.logistics.tms.entity.TStaffBasic;
import com.logistics.tms.entity.TVehicleBasic;
import com.logistics.tms.entity.TViolationRegulations;
import com.logistics.tms.mapper.TCertificationPicturesMapper;
import com.logistics.tms.mapper.TStaffBasicMapper;
import com.logistics.tms.mapper.TVehicleBasicMapper;
import com.logistics.tms.mapper.TViolationRegulationsMapper;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tools.utils.UUIDGenerateUtil;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @Author: sj
 * @Date: 2019/6/3 14:30
 */
@Service
public class ViolationRegulationBiz {
    @Autowired
    private TViolationRegulationsMapper tQViolationRegulationsMapper;
    @Autowired
    private TCertificationPicturesMapper tQCertificationPicturesMapper;
    @Autowired
    private TVehicleBasicMapper tQVehicleBasicMapper;
    @Autowired
    private TStaffBasicMapper tQStaffBasicMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private ConfigKeyConstant configKeyConstant;

    /**
     * 列表
     * @param requestModel
     * @return
     */
    public SearchViolationRegulationListResponseModel searchViolationRegulationsInfo(SearchViolationRegulationListRequestModel requestModel){
        SearchViolationRegulationListResponseModel responseModel = new SearchViolationRegulationListResponseModel();
        requestModel.enablePaging();
        List<Long> violationRegulationIdList = tQViolationRegulationsMapper.getViolationRegulationIds(requestModel);
        PageInfo<ViolationRegulationListResponseModel> pageInfo = new PageInfo(violationRegulationIdList);

        if(ListUtils.isNotEmpty(violationRegulationIdList)) {
            List<ViolationRegulationListResponseModel> violationRegulationList = tQViolationRegulationsMapper.getViolationRegulationListByIds(StringUtils.listToString(violationRegulationIdList,','));
            pageInfo.setList(violationRegulationList == null ? new ArrayList<>() : violationRegulationList);
            responseModel = tQViolationRegulationsMapper.getViolationRegulationSummaryData(requestModel);
        }else{
            pageInfo.setList(new ArrayList<>());
        }
        responseModel.setPageInfo(pageInfo);
        return responseModel;
    }

    /**
     * 新增/修改
     *
     * @param requestModel
     */
    @Transactional
    public void saveViolationRegulation(AddOrModifyViolationRegulationRequestModel requestModel) {
        TStaffBasic dbStaffBasic = tQStaffBasicMapper.selectByPrimaryKey(requestModel.getDriverId());
        if (dbStaffBasic == null) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_NOT_EXIST);
        }
        TVehicleBasic dbVehicleBasic = tQVehicleBasicMapper.selectByPrimaryKey(requestModel.getVehicleId());
        if (dbVehicleBasic == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }

        TViolationRegulations violationRegulations = new TViolationRegulations();
        violationRegulations.setDriverId(requestModel.getDriverId());
        violationRegulations.setDeduction(Optional.ofNullable(requestModel.getDeduction()).orElse(CommonConstant.INTEGER_ZERO));
        violationRegulations.setFine(Optional.ofNullable(requestModel.getFine()).orElse(CommonConstant.BIG_DECIMAL_ZERO));
        violationRegulations.setOccuranceTime(requestModel.getOccuranceTime());
        violationRegulations.setOccuranceAddress(requestModel.getOccuranceAddress());
        violationRegulations.setRemark(requestModel.getRemark());

        // vehicleId + driverId + OccuranceTime 重性校验
        TViolationRegulations tqViolationRegulations = tQViolationRegulationsMapper.selectRecordByVehicleIdAndDriverIdAndOccurTime(requestModel.getVehicleId(), requestModel.getDriverId(), requestModel.getOccuranceTime());

        if (requestModel.getViolationRegulationId() == null || requestModel.getViolationRegulationId() <= CommonConstant.INTEGER_ZERO) {//新增
            if (tqViolationRegulations != null) {
                throw new BizException(CarrierDataExceptionEnum.VIOLATION_REGULATION_HAS_EXIST);
            }

            //必须是内部车辆
            if (!(VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(dbVehicleBasic.getVehicleProperty()) ||
                    VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(dbVehicleBasic.getVehicleProperty()))) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_PROPERTY_ERROR);
            }

            //设置车辆信息
            violationRegulations.setVehicleId(dbVehicleBasic.getId());
            violationRegulations.setVehicleProperty(dbVehicleBasic.getVehicleProperty());
            violationRegulations.setSource(CommonConstant.INTEGER_ONE);
            commonBiz.setBaseEntityAdd(violationRegulations, BaseContextHandler.getUserName());
            tQViolationRegulationsMapper.insertSelective(violationRegulations);
        } else {//修改
            //必须是内部车辆
            //判断车辆机构,如果未修改车辆则不判断车辆机构
            if (!dbVehicleBasic.getId().equals(requestModel.getVehicleId()) &&
                    !(VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(dbVehicleBasic.getVehicleProperty()) || VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(dbVehicleBasic.getVehicleProperty()))) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_PROPERTY_ERROR);
            }

            TViolationRegulations tViolationRegulations = tQViolationRegulationsMapper.selectByPrimaryKey(requestModel.getViolationRegulationId());
            if (tViolationRegulations == null) {
                throw new BizException(CarrierDataExceptionEnum.VIOLATION_REGULATION_IS_EMPTY);
            }

            if (tqViolationRegulations != null && !requestModel.getViolationRegulationId().equals(tqViolationRegulations.getId())) {
                throw new BizException(CarrierDataExceptionEnum.VIOLATION_REGULATION_HAS_EXIST);
            }

            //设置车辆信息,如果有变动才修改
            if (!tViolationRegulations.getVehicleId().equals(requestModel.getVehicleId())) {
                violationRegulations.setVehicleId(dbVehicleBasic.getId());
                violationRegulations.setVehicleProperty(dbVehicleBasic.getVehicleProperty());
            }
            violationRegulations.setId(requestModel.getViolationRegulationId());
            commonBiz.setBaseEntityModify(violationRegulations, BaseContextHandler.getUserName());
            tQViolationRegulationsMapper.updateByPrimaryKeySelective(violationRegulations);

            List<TCertificationPictures> certificationList = tQCertificationPicturesMapper.getByObjectIdType(requestModel.getViolationRegulationId(), CertificationPicturesObjectTypeEnum.T_VIOLATION_REGULATIONS.getObjectType(), CertificationPicturesFileTypeEnum.VIOLATION_REGULATIONS.getFileType());
            if (ListUtils.isNotEmpty(certificationList)) {
                UpdateCertificationPicturesModel updateModel = new UpdateCertificationPicturesModel();
                updateModel.setObjectId(violationRegulations.getId());
                updateModel.setObjectType(CertificationPicturesObjectTypeEnum.T_VIOLATION_REGULATIONS.getObjectType());
                updateModel.setFileType(CertificationPicturesFileTypeEnum.VIOLATION_REGULATIONS.getFileType());
                updateModel.setFilePaths(LocalStringUtil.listTostring(requestModel.getFileList(), ','));
                tQCertificationPicturesMapper.updateFilePath(updateModel);
            }
            if (ListUtils.isNotEmpty(requestModel.getFileList())) {
                List<String> batchInsertList = new ArrayList<>();
                for (String filePath : requestModel.getFileList()) {
                    if (!filePath.contains(configKeyConstant.violationRegulationsCatalog)) {
                        batchInsertList.add(filePath);
                    }
                }
                requestModel.setFileList(batchInsertList);
            }
        }

        //批量插入图片信息
        if(ListUtils.isNotEmpty(requestModel.getFileList())){
            List<TCertificationPictures> batchInsertList = new ArrayList<>();
            TCertificationPictures pageCertificationPictures;
            for (String picturesPath : requestModel.getFileList() ) {
                pageCertificationPictures = new TCertificationPictures();
                pageCertificationPictures.setObjectId(violationRegulations.getId());
                pageCertificationPictures.setObjectType(CertificationPicturesObjectTypeEnum.T_VIOLATION_REGULATIONS.getObjectType());
                pageCertificationPictures.setFileType(CertificationPicturesFileTypeEnum.VIOLATION_REGULATIONS.getFileType());
                pageCertificationPictures.setFileTypeName(CertificationPicturesFileTypeEnum.VIOLATION_REGULATIONS.getFileName());
                pageCertificationPictures.setUploadTime(new Date());
                pageCertificationPictures.setUploadUserName(BaseContextHandler.getUserName());
                pageCertificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.VIOLATION_REGULATIONS.getKey(),UUIDGenerateUtil.generateUUID(),picturesPath,null));
                pageCertificationPictures.setSuffix(pageCertificationPictures.getFilePath().substring(pageCertificationPictures.getFilePath().lastIndexOf('.')));
                commonBiz.setBaseEntityAdd(pageCertificationPictures,BaseContextHandler.getUserName());
                batchInsertList.add(pageCertificationPictures);
            }
            tQCertificationPicturesMapper.batchInsert(batchInsertList);
        }
    }

    /**
     * 详情
     * @param requestModel
     * @return
     */
    public ViolationRegulationDetailResponseModel getDetail(ViolationRegulationDetailRequestModel requestModel) {
        TViolationRegulations dbViolationRegulations = tQViolationRegulationsMapper.selectByPrimaryKey(requestModel.getViolationRegulationId());
        if(dbViolationRegulations == null){
            throw new BizException(CarrierDataExceptionEnum.VIOLATION_REGULATION_IS_EMPTY);
        }
        ViolationRegulationDetailResponseModel responseModel = new ViolationRegulationDetailResponseModel();
        List<ViolationRegulationListResponseModel> violationRegulationList = tQViolationRegulationsMapper.getViolationRegulationListByIds(ConverterUtils.toString(requestModel.getViolationRegulationId()));
        if(ListUtils.isNotEmpty(violationRegulationList) ){
            MapperUtils.mapper(violationRegulationList.get(CommonConstant.INTEGER_ZERO), responseModel);
        }else{
            responseModel.setFileList(new ArrayList<>());
        }
        return responseModel;
    }

    /**
     * 删除
     */
    @Transactional
    public void deleteViolationRegulation(DeleteViolationRegulationRequestModel requestModel){
        TViolationRegulations dbViolationRegulations = tQViolationRegulationsMapper.selectByPrimaryKey(requestModel.getViolationRegulationId());
        if(dbViolationRegulations == null){
            throw new BizException(CarrierDataExceptionEnum.VIOLATION_REGULATION_IS_EMPTY);
        }

        TViolationRegulations upViolationRegulation = new TViolationRegulations();
        upViolationRegulation.setId(requestModel.getViolationRegulationId());
        upViolationRegulation.setValid(CommonConstant.INTEGER_ZERO);
        commonBiz.setBaseEntityModify(upViolationRegulation,BaseContextHandler.getUserName());
        tQViolationRegulationsMapper.updateByPrimaryKeySelective(upViolationRegulation);
    }

    /**
     * 导入
     * @param requestModel
     * @return
     */
    @Transactional
    public ImportViolationRegulationResponseModel importViolationRegulation(ImportViolationRegulationRequestModel requestModel){
        ImportViolationRegulationResponseModel responseModel = new ImportViolationRegulationResponseModel();
        responseModel.initNumber(requestModel.getNumberFailures());

        List<ImportViolationRegulationListRequestModel> importList = requestModel.getImportList();
        if(ListUtils.isEmpty(importList)){
            return responseModel;
        }

        List<FuzzyQueryDriverInfoResponseModel> driverModelList = tQStaffBasicMapper.fuzzyQueryDriverInfo("");
        List<FuzzyQueryVehicleInfoResponseModel> vehicleModelList = tQVehicleBasicMapper.fuzzyQueryVehicleInfoByVehicleNo("");

        if (ListUtils.isEmpty(driverModelList) || ListUtils.isEmpty(vehicleModelList)) {
            responseModel.addFailures(importList.size());
            return responseModel;
        }

        Map<String, FuzzyQueryDriverInfoResponseModel> driverInfoMap = new HashMap<>();
        for (FuzzyQueryDriverInfoResponseModel tempDriverModel : driverModelList) {
            driverInfoMap.put(tempDriverModel.getDriverName() + "|" + tempDriverModel.getDriverPhone(), tempDriverModel);
        }
        Map<String, FuzzyQueryVehicleInfoResponseModel> vehicleInfoMap = new HashMap<>();
        for (FuzzyQueryVehicleInfoResponseModel vehicleModel : vehicleModelList) {
            vehicleInfoMap.put(vehicleModel.getVehicleNo(), vehicleModel);
        }

        TViolationRegulations violationRegulations;
        List<TViolationRegulations> addList = new ArrayList<>();
        List<TViolationRegulations> upList = new ArrayList<>();
        for (ImportViolationRegulationListRequestModel tempImportModel : requestModel.getImportList()) {
            String driverKey = tempImportModel.getDriverName() + "|" + tempImportModel.getDriverPhone();
            if (driverInfoMap.get(driverKey) == null || vehicleInfoMap.get(tempImportModel.getVehicleNo()) == null) {
                responseModel.addFailures();
            } else {
                FuzzyQueryVehicleInfoResponseModel vehicleInfo = vehicleInfoMap.get(tempImportModel.getVehicleNo());
                if (vehicleInfo != null) {
                    FuzzyQueryDriverInfoResponseModel driverInfo = driverInfoMap.get(driverKey);
                    TViolationRegulations tqViolationRegulations = tQViolationRegulationsMapper.selectRecordByVehicleIdAndDriverIdAndOccurTime(vehicleInfo.getVehicleId(), driverInfo.getDriverId(), tempImportModel.getOccuranceTime());
                    violationRegulations = new TViolationRegulations();
                    violationRegulations.setDriverId(driverInfo.getDriverId());
                    violationRegulations.setVehicleId(vehicleInfo.getVehicleId());
                    violationRegulations.setRemark(StringUtils.isBlank(tempImportModel.getRemark()) ? null : tempImportModel.getRemark());
                    violationRegulations.setOccuranceTime(tempImportModel.getOccuranceTime());
                    violationRegulations.setFine(tempImportModel.getFine());
                    violationRegulations.setDeduction(tempImportModel.getDeduction());
                    violationRegulations.setOccuranceAddress(StringUtils.isBlank(tempImportModel.getOccuranceAddress()) ? null : tempImportModel.getOccuranceAddress());
                    violationRegulations.setSource(CommonConstant.INTEGER_TWO);
                    if (tqViolationRegulations == null) {
                        //新增
                        //不能新增外部车辆
                        if (VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(vehicleInfo.getVehicleProperty())) {
                            responseModel.addFailures();
                            continue;
                        }
                        violationRegulations.setVehicleProperty(vehicleInfo.getVehicleProperty());
                        commonBiz.setBaseEntityAdd(violationRegulations, BaseContextHandler.getUserName());
                        addList.add(violationRegulations);
                    } else {
                        //修改
                        //车辆相同不修改车辆机构
                        if (!vehicleInfo.getVehicleId().equals(tqViolationRegulations.getVehicleId())) {
                            violationRegulations.setVehicleProperty(vehicleInfo.getVehicleProperty());

                            //不能新增外部车辆
                            if (VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(vehicleInfo.getVehicleProperty())) {
                                responseModel.addFailures();
                                continue;
                            }
                        }
                        violationRegulations.setId(tqViolationRegulations.getId());
                        commonBiz.setBaseEntityModify(violationRegulations, BaseContextHandler.getUserName());
                        upList.add(violationRegulations);
                    }
                    responseModel.addSuccessful();
                } else {
                    responseModel.addFailures();
                }
            }
        }
        if(ListUtils.isNotEmpty(addList)){
            tQViolationRegulationsMapper.batchInsert(addList);
        }
        if(ListUtils.isNotEmpty(upList)){
            tQViolationRegulationsMapper.batchUpdate(upList);
        }
        return responseModel;
    }

}
