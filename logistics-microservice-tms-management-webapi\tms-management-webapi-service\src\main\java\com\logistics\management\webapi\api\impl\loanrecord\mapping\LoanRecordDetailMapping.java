package com.logistics.management.webapi.api.impl.loanrecord.mapping;

import com.logistics.management.webapi.api.feign.loanrecord.dto.LoanRecordDetailResponseDto;
import com.logistics.management.webapi.api.feign.loanrecord.dto.LoanRecordFileResponseDto;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.LoanStatusEnum;
import com.logistics.tms.api.feign.loanrecord.model.LoanRecordDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: sj
 * @Date: 2019/9/30 10:00
 */
public class LoanRecordDetailMapping extends MapperMapping<LoanRecordDetailResponseModel,LoanRecordDetailResponseDto> {
    private ConfigKeyConstant configKeyConstant;
    private Map<String,String> imageMap;
    public LoanRecordDetailMapping(){

    }
    public LoanRecordDetailMapping(ConfigKeyConstant configKeyConstant, Map<String,String> imageMap){
       this.configKeyConstant = configKeyConstant;
       this.imageMap=imageMap;
    }
    @Override
    public void configure() {
        LoanRecordDetailResponseModel source = this.getSource();
        LoanRecordDetailResponseDto target = this.getDestination();

        if(source != null){
            if(source.getLastModifiedTime() != null){
                target.setLastModifiedTime(DateUtils.dateToString(source.getLastModifiedTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getLoanStartTime() != null){
                target.setLoanStartTime(DateUtils.dateToString(source.getLoanStartTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getProductionDate()!=null){
                target.setProductionDate(DateUtils.dateToString(source.getProductionDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));

            }
            target.setStatusLabel(LoanStatusEnum.getEnum(source.getStatus()).getValue());

            List<LoanRecordFileResponseDto> fileList = target.getFileList();
            if(ListUtils.isNotEmpty(fileList)){
                for (LoanRecordFileResponseDto tempDto : fileList) {
                    tempDto.setAbsoluteFilePath(configKeyConstant.fileAccessAddress+imageMap.get(tempDto.getRelativeFilepath()));
                }
            }
            target.setDriverLabel(Optional.ofNullable(target.getName()).orElse("")+" "+ Optional.ofNullable(target.getMobile()).orElse("") );
        }
    }
}
