package com.logistics.tms.api.feign.freight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @Author: sj
 * @Date: 2019/12/26 9:33
 */
@Data
public class GetAddressRuleListConditionModel {
    @ApiModelProperty("运价ID")
    private Long freightId;
    @ApiModelProperty("仓库ID")
    private Long warehouseId;
    @ApiModelProperty("发货省ID")
    private Long fromProvinceId;
    @ApiModelProperty("发货市ID")
    private Long fromCityId;
    @ApiModelProperty("发货区ID")
    private Long fromAreaId;
    @ApiModelProperty("卸货省ID")
    private Long toProvinceId;
    @ApiModelProperty("卸货市ID")
    private Long toCityId;
    @ApiModelProperty("卸货区ID")
    private Long toAreaId;
    @ApiModelProperty("货主计价类型: 计价类型 1 基价 2 一日游")
    private Integer calcType;
}
