package com.logistics.management.webapi.controller.dispatch.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/6 10:13
 */
@Data
public class DemandOrderSpecialDispatchDetailRequestDto {

    /**
     * 需求单id
     */
    @NotEmpty(message = "请选择需求单")
    @Size(min = 1, max = 15, message = "最多支持15个需求单一起调度")
    private List<String> demandIdList;




}
