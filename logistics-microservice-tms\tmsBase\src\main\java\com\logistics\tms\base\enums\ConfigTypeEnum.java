package com.logistics.tms.base.enums;

/**
 * 价格类型; 1: 固定路线; 2: 区域设置; 3: 距离阶梯
 * @Author: xuanjia.liang
 * @Date: 2023/6/30 19:26
 */
public enum ConfigTypeEnum {
    DEFAULT_VALUE(-1,""),
    FIXED_ROUTE(1,"固定路线"),
    AREA_CONFIG(2,"区域设置"),
    DISTANCE_CONFIG(3,"距离阶梯"),;

    private final Integer key;
    private final String value;

    ConfigTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }
    public String getValue() {
        return value;
    }

    public static ConfigTypeEnum getEnum(Integer key) {
        for (ConfigTypeEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return DEFAULT_VALUE;
    }

}
