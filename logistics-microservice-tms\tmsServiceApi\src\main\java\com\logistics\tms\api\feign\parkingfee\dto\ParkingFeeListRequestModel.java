package com.logistics.tms.api.feign.parkingfee.dto;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/10/9 15:32
 */
@Data
public class ParkingFeeListRequestModel extends AbstractPageForm<ParkingFeeListRequestModel>{
    @ApiModelProperty("结算状态：空：全部，0 待结算，1 部分结算，2 结算完成，-1 已终止")
    private Integer status;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("司机 支持姓名或号码模糊匹配")
    private String driverInfo;
    @ApiModelProperty("合作公司")
    private String cooperationCompany;
    @ApiModelProperty("停车费用ID拼接，譬如:'123,456,789'")
    private String parkingFeeIds;
}
