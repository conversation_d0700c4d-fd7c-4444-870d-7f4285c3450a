package com.logistics.management.webapi.client.drivercostapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/3 14:51
 */
@Data
public class AuditOrRejectCostApplyRequestModel {

    @ApiModelProperty(value = "司机费用申请表id")
    private Long driverCostApplyId;

    @ApiModelProperty(value = "操作人员类型 1:业务审核 2:财务审核")
    private Integer auditorType;

    @ApiModelProperty(value = "操作类型：1 审核通过，2 驳回")
    private Integer operateType;

    @ApiModelProperty("撤销说明")
    private String remark;

    @ApiModelProperty("冲销费用列表")
    private List<DriverCostDeductionRequestModel> reserveList;

    @ApiModelProperty("垫付费用（元）; 1.3.6 新增")
    private String advanceCosts = "";
}
