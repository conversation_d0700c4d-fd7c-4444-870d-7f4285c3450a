/**
 * Created by yun.<PERSON><PERSON> on 2017/12/12.
 */
package com.logistics.tms.base.enums;

public enum OperateLogsOperateTypeEnum {

    AUDIT_THROUGH(OperateLogsObjectTypeEnum.DISPATCH_ORDER,1, "审核通过"),
    AUDIT_REJECT(OperateLogsObjectTypeEnum.DISPATCH_ORDER,2, "审核驳回"),
    UPDATE(OperateLogsObjectTypeEnum.DISPATCH_ORDER,3,"修改"),
    INVALID(OperateLogsObjectTypeEnum.DISPATCH_ORDER,4,"作废"),

    CARRIER_FREIGHT_RULE_ADD(OperateLogsObjectTypeEnum.CARRIER_RULE,0,"建立规则"),
    CARRIER_FREIGHT_RULE_ENABLE(OperateLogsObjectTypeEnum.CARRIER_RULE,0,"启用规则"),
    CARRIER_FREIGHT_RULE_DISABLE(OperateLogsObjectTypeEnum.CARRIER_RULE,0,"禁用规则"),
    CARRIER_FREIGHT_RULE_MODIFY_ADDRESS(OperateLogsObjectTypeEnum.CARRIER_RULE,0,"修改发货地"),
    CARRIER_FREIGHT_RULE_MODIFY_LADDER(OperateLogsObjectTypeEnum.CARRIER_RULE,0,"修改阶梯规则"),
    DRIVER_FREIGHT_ADD(OperateLogsObjectTypeEnum.CARRIER_DRIVER_FREIGHT,0,"建立司机运费"),
    DRIVER_FREIGHT_MODIFY(OperateLogsObjectTypeEnum.CARRIER_DRIVER_FREIGHT,0,"修改司机运费"),
    DRIVER_FREIGHT_UNIFY_ADD(OperateLogsObjectTypeEnum.CARRIER_DRIVER_FREIGHT,1,"统一加价"),
    DRIVER_FREIGHT_UNIFY_SUBTRACT(OperateLogsObjectTypeEnum.CARRIER_DRIVER_FREIGHT,1,"统一减价"),

    ENTRUST_FREIGHT_RULE_ADD(OperateLogsObjectTypeEnum.ENTRUST_FREIGHT,0,"建立规则"),
    ENTRUST_FREIGHT_RULE_ENABLE(OperateLogsObjectTypeEnum.ENTRUST_FREIGHT,0,"启用规则"),
    ENTRUST_FREIGHT_RULE_DISABLE(OperateLogsObjectTypeEnum.ENTRUST_FREIGHT,0,"禁用规则"),
    ENTRUST_FREIGHT_RULE_MODIFY_ADDRESS(OperateLogsObjectTypeEnum.ENTRUST_FREIGHT,0,"修改发货地"),
    ENTRUST_FREIGHT_RULE_MODIFY_LADDER(OperateLogsObjectTypeEnum.ENTRUST_FREIGHT,0,"修改阶梯规则"),
    ENTRUST_BASE_PRICE_ADD(OperateLogsObjectTypeEnum.ENTRUST_BASE_PRICE,0,"建立基价"),
    ENTRUST_BASE_PRICE_MODIFY(OperateLogsObjectTypeEnum.ENTRUST_BASE_PRICE,0,"修改基价"),
    ENTRUST_BASE_PRICE_UNIFY_ADD(OperateLogsObjectTypeEnum.ENTRUST_BASE_PRICE,1,"统一加价"),
    ENTRUST_BASE_PRICE_UNIFY_SUBTRACT(OperateLogsObjectTypeEnum.ENTRUST_BASE_PRICE,1,"统一减价"),


    FREIGHT_RULE_ENABLE(OperateLogsObjectTypeEnum.FREIGHT,0,"启用规则"),
    FREIGHT_RULE_DISABLE(OperateLogsObjectTypeEnum.FREIGHT,0,"禁用规则"),
    FREIGHT_RULE_ADD(OperateLogsObjectTypeEnum.FREIGHT,0,"新增规则"),
    FREIGHT_RULE_MODIFY(OperateLogsObjectTypeEnum.FREIGHT,0,"修改规则"),
    FREIGHT_RULE_DELETE(OperateLogsObjectTypeEnum.FREIGHT,0,"删除规则"),
    PRICE_UNIFY_ADD(OperateLogsObjectTypeEnum.FREIGHT,0,"统一加价"),
    PRICE_UNIFY_SUBTRACT(OperateLogsObjectTypeEnum.FREIGHT,0,"统一减价"),

    CARRIER_FREIGHT_ADDRESS_RULE_ENABLE(OperateLogsObjectTypeEnum.CARRIER_FREIGHT,0,"启用规则"),
    CARRIER_FREIGHT_ADDRESS_RULE_DISABLE(OperateLogsObjectTypeEnum.CARRIER_FREIGHT,0,"禁用规则"),
    CARRIER_FREIGHT_ADDRESS_RULE_ADD(OperateLogsObjectTypeEnum.CARRIER_FREIGHT,0,"新增规则"),
    CARRIER_FREIGHT_ADDRESS_RULE_MODIFY(OperateLogsObjectTypeEnum.CARRIER_FREIGHT,0,"修改规则"),
    CARRIER_FREIGHT_ADDRESS_RULE_DELETE(OperateLogsObjectTypeEnum.CARRIER_FREIGHT,0,"删除规则"),


    AUDIT_THROUGH_VEHICLE(OperateLogsObjectTypeEnum.UPDATE_AUDIT_VEHICLE,1, "审核通过"),
    AUDIT_THROUGH_REJECT(OperateLogsObjectTypeEnum.UPDATE_AUDIT_VEHICLE,2, "驳回"),
    AUDIT_THROUGH_UPDATE(OperateLogsObjectTypeEnum.UPDATE_AUDIT_VEHICLE,3,"修改车辆"),

    AUDIT_THROUGH_DRIVER_PAYEE(OperateLogsObjectTypeEnum.DRIVER_PAYEE,1, "审核通过"),
    REJECT_DRIVER_PAYEE(OperateLogsObjectTypeEnum.DRIVER_PAYEE,2, "驳回"),
    INVALID_DRIVER_PAYEE(OperateLogsObjectTypeEnum.DRIVER_PAYEE,3,"作废"),
    ADD_DRIVER_PAYEE(OperateLogsObjectTypeEnum.DRIVER_PAYEE,4,"新增收款人"),
    UPDATE_DRIVER_PAYEE(OperateLogsObjectTypeEnum.DRIVER_PAYEE,5,"修改"),
    IMPORT_ADD_DRIVER_PAYEE(OperateLogsObjectTypeEnum.DRIVER_PAYEE,6,"导入新增"),
    IMPORT_MODIFY_DRIVER_PAYEE(OperateLogsObjectTypeEnum.DRIVER_PAYEE,7,"导入修改"),
    IMPORT_MODIFY_DRIVER_PAYEE_CERTIFICATE(OperateLogsObjectTypeEnum.DRIVER_PAYEE,8,"导入证件"),

    ADD_CONTRACT(OperateLogsObjectTypeEnum.CONTRACT, 1, "新增合同"),
    MODIFY_CONTRACT(OperateLogsObjectTypeEnum.CONTRACT, 2, "修改合同"),
    TERMINATE_CONTRACT(OperateLogsObjectTypeEnum.CONTRACT, 3, "终止合同"),
    CANCEL_CONTRACT(OperateLogsObjectTypeEnum.CONTRACT, 4, "作废合同"),

    LOAN_RECORDS_ADD(OperateLogsObjectTypeEnum.LOAN_RECORDS,1,"新增贷款记录"),
    LOAN_RECORDS_UPDATE(OperateLogsObjectTypeEnum.LOAN_RECORDS,2,"修改贷款记录"),

    CARRIER_VEHICLE_ADD(OperateLogsObjectTypeEnum.COMPANY_CARRIER_VEHICLE,1,"添加"),

    CARRIER_DRIVER_ADD(OperateLogsObjectTypeEnum.COMPANY_CARRIER_DRIVER,1,"添加司机"),

    VEHICLE_OIL_CARD_ADD(OperateLogsObjectTypeEnum.VEHICLE_OIL_CARD,1,"新增"),
    VEHICLE_OIL_CARD_UNBINDING(OperateLogsObjectTypeEnum.VEHICLE_OIL_CARD,2,"解绑"),
    VEHICLE_OIL_CARD_BINDING(OperateLogsObjectTypeEnum.VEHICLE_OIL_CARD,3,"绑定"),

    ADD_TEMPORARY_COST(OperateLogsObjectTypeEnum.TEMPORARY_COST, 1, "新增"),
    AUDIT_TEMPORARY_COST(OperateLogsObjectTypeEnum.TEMPORARY_COST, 2, "审核"),
    REJECT_TEMPORARY_COST(OperateLogsObjectTypeEnum.TEMPORARY_COST, 3, "驳回"),
    COMMIT_TEMPORARY_COST(OperateLogsObjectTypeEnum.TEMPORARY_COST, 4, "提交"),
    PLATFORM_AUDIT_TEMPORARY_COST(OperateLogsObjectTypeEnum.TEMPORARY_COST, 5, "平台人员审核"),//此类型业务上已去掉
    REVOKE_TEMPORARY_COST(OperateLogsObjectTypeEnum.TEMPORARY_COST, 6, "撤销"),

    RENEWABLE_AUDIT_ORDER_ORDER(OperateLogsObjectTypeEnum.RENEWABLE_AUDIT_ORDER, 1, "下单"),
    RENEWABLE_AUDIT_ORDER_ASSIGN(OperateLogsObjectTypeEnum.RENEWABLE_AUDIT_ORDER, 2, "指派"),
    RENEWABLE_AUDIT_ORDER_UPDATE_ASSIGN(OperateLogsObjectTypeEnum.RENEWABLE_AUDIT_ORDER, 3, "修改指派"),
    RENEWABLE_AUDIT_ORDER_CONFIRM(OperateLogsObjectTypeEnum.RENEWABLE_AUDIT_ORDER, 4, "确认"),
    RENEWABLE_AUDIT_ORDER_AUDIT(OperateLogsObjectTypeEnum.RENEWABLE_AUDIT_ORDER, 5, "审核通过"),
    RENEWABLE_AUDIT_ORDER_REJECT(OperateLogsObjectTypeEnum.RENEWABLE_AUDIT_ORDER, 6, "审核驳回"),
    RENEWABLE_AUDIT_ORDER_CANCEL(OperateLogsObjectTypeEnum.RENEWABLE_AUDIT_ORDER, 7, "取消"),


    RESERVE_APPLY_AUDIT(OperateLogsObjectTypeEnum.RESERVE_APPLY_AUDIT, 1, "驾驶员提交"),
    RESERVE_APPLY_AUDIT_RESUBMIT(OperateLogsObjectTypeEnum.RESERVE_APPLY_AUDIT, 2, "驾驶员提交"),
    RESERVE_APPLY_AUDIT_BUSINESS(OperateLogsObjectTypeEnum.RESERVE_APPLY_AUDIT, 3, "业务审核通过"),
    RESERVE_APPLY_AUDIT_FINANCIAL(OperateLogsObjectTypeEnum.RESERVE_APPLY_AUDIT, 4, "财务审核通过"),
    RESERVE_APPLY_AUDIT_BUSINESS_REJECT(OperateLogsObjectTypeEnum.RESERVE_APPLY_AUDIT, 5, "业务审核驳回"),
    RESERVE_APPLY_AUDIT_FINANCIAL_REJECT(OperateLogsObjectTypeEnum.RESERVE_APPLY_AUDIT, 6, "财务审核驳回"),
    RESERVE_APPLY_AUDIT_REMIT(OperateLogsObjectTypeEnum.RESERVE_APPLY_AUDIT, 7, "已打款"),
    RESERVE_APPLY_ADVANCE_AUTO_CREATED_BY_SYSTEM(OperateLogsObjectTypeEnum.RESERVE_APPLY_AUDIT, 8, "由系统默认创建垫付备用金申请"),
    RESERVE_APPLY_RED_CHARGE_REFUND_AUTO_CREATED_BY_SYSTEM(OperateLogsObjectTypeEnum.RESERVE_APPLY_AUDIT, 9, "由系统默认创建红冲退款备用金申请"),


    DRIVER_ACCOUNT_ADD(OperateLogsObjectTypeEnum.DRIVER_ACCOUNT_ADD_EDIT, 1, "新增"),
    DRIVER_ACCOUNT_EDIT(OperateLogsObjectTypeEnum.DRIVER_ACCOUNT_ADD_EDIT, 2, "更换"),

    DRIVER_COST_APPLY_BUSINESS_AUDIT(OperateLogsObjectTypeEnum.DRIVER_COST_APPLY, 1, "业务审核"),
    DRIVER_COST_APPLY_FINANCIAL_AUDIT(OperateLogsObjectTypeEnum.DRIVER_COST_APPLY, 2, "财务审核"),
    DRIVER_COST_APPLY_RED_CHARGE_REFUND(OperateLogsObjectTypeEnum.DRIVER_COST_APPLY, 3, "红冲"),

    BIDDING_ORDER_ADD(OperateLogsObjectTypeEnum.BIDDING_ORDER, 1, "生成竞价单"),
    BIDDING_ORDER_DEMAND_ADD(OperateLogsObjectTypeEnum.BIDDING_ORDER, 2, "新增需求单"),
    REBIDDING_QUOTE(OperateLogsObjectTypeEnum.BIDDING_ORDER, 3, "重新报价"),
    STOP_BIDDING_QUOTE(OperateLogsObjectTypeEnum.BIDDING_ORDER, 4, "暂停报价"),
    CANCEL_BIDDING_QUOTE(OperateLogsObjectTypeEnum.BIDDING_ORDER, 5, "取消报价"),
    CHOOSE_CARRIER(OperateLogsObjectTypeEnum.BIDDING_ORDER, 6, "选择车主"),
    MODIFY_QUOTE(OperateLogsObjectTypeEnum.BIDDING_ORDER, 7, "修改报价"),

    ROUTE_ENQUIRY_ADD(OperateLogsObjectTypeEnum.ROUTE_ENQUIRY, 1, "创建竞价单"),
    ROUTE_ENQUIRY_CANCEL(OperateLogsObjectTypeEnum.ROUTE_ENQUIRY, 2, "竞价取消"),
    ROUTE_ENQUIRY_QUOTE(OperateLogsObjectTypeEnum.ROUTE_ENQUIRY, 3, "报价"),
    ROUTE_ENQUIRY_BUSINESS_AUDIT(OperateLogsObjectTypeEnum.ROUTE_ENQUIRY, 4, "业务审核"),
    ROUTE_ENQUIRY_SETTLE_AUDIT(OperateLogsObjectTypeEnum.ROUTE_ENQUIRY, 5, "结算审核"),
    ROUTE_ENQUIRY_ARCHIVE(OperateLogsObjectTypeEnum.ROUTE_ENQUIRY, 6, "归档"),

    SHIPPING_ORDER_AUDIT(OperateLogsObjectTypeEnum.SHIPPING_ORDER, 1, "审核"),
    SHIPPING_ORDER_RE_QUOTE(OperateLogsObjectTypeEnum.SHIPPING_ORDER, 2, "重新提交"),

    ;

    private OperateLogsObjectTypeEnum objectType;
    private Integer operateType;
    private String operateContents;

    OperateLogsOperateTypeEnum(OperateLogsObjectTypeEnum objectType, Integer key, String value) {
        this.objectType = objectType;
        this.operateType = key;
        this.operateContents = value;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public String getOperateContents() {
        return operateContents;
    }

    public OperateLogsObjectTypeEnum getObjectType() {
        return objectType;
    }

}
