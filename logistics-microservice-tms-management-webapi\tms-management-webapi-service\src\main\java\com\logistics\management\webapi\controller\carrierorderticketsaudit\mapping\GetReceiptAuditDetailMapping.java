package com.logistics.management.webapi.controller.carrierorderticketsaudit.mapping;

import com.logistics.management.webapi.client.carrierorderticketsaudit.enums.CarrierOrderTicketsAuditStatusEnum;
import com.logistics.management.webapi.client.carrierorderticketsaudit.response.GetReceiptAuditDetailResponseModel;
import com.logistics.management.webapi.controller.carrierorderticketsaudit.response.GetReceiptAuditDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;
import lombok.AllArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@AllArgsConstructor
public class GetReceiptAuditDetailMapping extends MapperMapping<GetReceiptAuditDetailResponseModel, GetReceiptAuditDetailResponseDto> {

    private String imagePrefix;

    private Map<String, String> imageMap;

    @Override
    public void configure() {

        GetReceiptAuditDetailResponseModel source = getSource();
        GetReceiptAuditDetailResponseDto destination = getDestination();

        // 填充审核状态文本
        destination.setAuditStatusLabel(CarrierOrderTicketsAuditStatusEnum.getEnumByKey(source.getAuditStatus()).getValue());
        destination.setTicketImages(picConversion(source.getTicketImages()));
    }

    // 图片访问路径转换
    private List<String> picConversion(List<String> tickets) {
        if (ListUtils.isEmpty(tickets)) {
            return Collections.emptyList();
        }
        return tickets.stream()
                .map(s -> imageMap.get(s))
                .filter(Objects::nonNull)
                .map(p -> imagePrefix.concat(p))
                .collect(Collectors.toList());
    }
}
