package com.logistics.management.webapi.api.feign.freight.dto;
import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 运价地址
 * @Author: sj
 * @Date: 2019/12/24 13:11
 */
@Data
public class SearchFreightAddressRequestDto extends AbstractPageForm<SearchFreightAddressRequestDto>{
    @NotBlank
    @ApiModelProperty("运价Id")
    private String freightId;
    @ApiModelProperty("仓库名称")
    private String warehouseName;
    @ApiModelProperty("发货地址")
    private String fromAddress;
    @ApiModelProperty("卸货")
    private String toAddress;
    @ApiModelProperty("计价类型:1 基价 2 一日游 ")
    private String calcType = "1";
    @ApiModelProperty("角色: 1 货主 2 车主")
    private String roleType;
    @ApiModelProperty("选择性导出id拼接")
    private String freightAddressIds;
}
