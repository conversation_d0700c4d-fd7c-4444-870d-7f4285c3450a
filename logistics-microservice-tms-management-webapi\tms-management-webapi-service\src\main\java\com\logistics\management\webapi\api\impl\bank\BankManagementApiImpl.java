package com.logistics.management.webapi.api.impl.bank;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.bank.BankManagementApi;
import com.logistics.management.webapi.api.feign.bank.dto.*;
import com.logistics.management.webapi.api.impl.bank.mapping.BankDetailMapping;
import com.logistics.management.webapi.api.impl.bank.mapping.ExportBankListMapping;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportExcelBankInfo;
import com.logistics.management.webapi.base.constant.ImportExcelHeaders;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.tms.api.feign.bank.BankServiceApi;
import com.logistics.tms.api.feign.bank.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * @Author: sj
 * @Date: 2019/7/10 14:25
 */

@RestController
@Slf4j
public class BankManagementApiImpl implements BankManagementApi {

    @Autowired
    private BankServiceApi bankServiceApi;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 银行列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchBankResponseDto>> searchBankList(@RequestBody SearchBankRequestDto requestDto) {

        Result<PageInfo<SearchBankResponseModel>> result = bankServiceApi.searchBankList(MapperUtils.mapper(requestDto, SearchBankRequestModel.class));
        result.throwException();

        PageInfo<SearchBankResponseDto> retPageInfo = new PageInfo<>(new ArrayList<>());
        if(result.getData()!=null){
            retPageInfo = MapperUtils.mapper(result.getData(),PageInfo.class);
            List<SearchBankResponseModel> bankModelList = result.getData().getList();
            List<SearchBankResponseDto> bankDtoList = MapperUtils.mapper(bankModelList,SearchBankResponseDto.class,new ExportBankListMapping());
            retPageInfo.setList(bankDtoList == null ? new ArrayList<>() : bankDtoList);
        }
        return Result.success(retPageInfo);
    }

    /**
     * 银行新增修改
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> saveOrModifyBank(@RequestBody @Valid SaveOrModifyBankRequestDto requestDto) {
        Result<Boolean> result = bankServiceApi.saveOrModifyBank(MapperUtils.mapper(requestDto, SaveOrModifyBankRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 查看详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<BankDetailResponseDto> getDetail(@RequestBody @Valid BankDetailRequestDto requestDto) {
        Result<BankDetailResponseModel> result =  bankServiceApi.getDetail(MapperUtils.mapper(requestDto, BankDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),BankDetailResponseDto.class,new BankDetailMapping()));
    }

    /**
     * 启用/禁用银行信息
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> enableOrDisable(@RequestBody @Valid EnableBankRequestDto requestDto) {
        Result<Boolean> result = bankServiceApi.enableOrDisable(MapperUtils.mapper(requestDto,EnableBankRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 导出
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void export(SearchBankRequestDto requestDto, HttpServletResponse response) {
        Result<List<SearchBankResponseModel>> result = bankServiceApi.export(MapperUtils.mapper(requestDto,SearchBankRequestModel.class));
        result.throwException();
        String fileName = "银行名称基础数据" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        List<SearchBankResponseDto> resultList = MapperUtils.mapper(result.getData(),SearchBankResponseDto.class,new ExportBankListMapping());
        Map<String, String> exportTypeMap = ExportExcelBankInfo.getExportBankInfo();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportTypeMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return resultList;
            }
        });
    }

    /**
     * 导入
     * @param file
     * @param request
     * @return
     */
    @Override
    public Result<ImportBankResponseDto> importBank(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        if (null == file) {
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_BANK_CONFIG_FILE_IS_EMPTY);
        }
        InputStream in;
        try {
            in = file.getInputStream();
        } catch (IOException e) {
            log.error("导入银行名称失败，",e);
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_BANK_CONFIG_FILE_IS_EMPTY);
        }

        List<List<Object>> excelList = new ImportExcelUtil().getDataListByExcel(in, file.getOriginalFilename(), ImportExcelHeaders.getImportBankType());//JSON处理类
        ImportBankRequestDto requestDto = this.initImportRepeatData(excelList);
        ImportBankRequestModel importBankRequestModel = MapperUtils.mapper(requestDto,ImportBankRequestModel.class);
        if(ListUtils.isNotEmpty(importBankRequestModel.getImportList())){
            importBankRequestModel.getImportList().forEach(t->
                commonBiz.convertObjectFieldToNullIfIsEmpty(t)
            );
        }
        Result<ImportBankResponseModel> result = bankServiceApi.importBank(importBankRequestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),ImportBankResponseDto.class));
    }

    /**
     * 根据名称模糊匹配银行信息
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<FuzzyQueryBankListResponseDto>> fuzzyQueryBank(@RequestBody FuzzyQueryBankRequestDto requestDto) {
        Result<List<FuzzyQueryBankListResponseModel>> result = bankServiceApi.fuzzyQueryBank(MapperUtils.mapper(requestDto,FuzzyQueryBankRequestModel.class));
        result.throwException();
        List<FuzzyQueryBankListResponseDto> responseBankList = MapperUtils.mapper(result.getData(),FuzzyQueryBankListResponseDto.class);
        return Result.success(responseBankList);
    }

    /**
     * 初始化请求数据信息
     * @return
     */
    private ImportBankRequestDto initImportRepeatData(List<List<Object>> excelList){
        ImportBankRequestDto requestDto = new ImportBankRequestDto();
        if (ListUtils.isEmpty(excelList)) {
            return requestDto;
        }
        Integer numberFailures = CommonConstant.INTEGER_ZERO;
        List<ImportBankListRequestDto> importList = new ArrayList<>();
        Set<ImportBankListRequestDto> setContains = new HashSet<>();
        ImportBankListRequestDto singleBankDto;
        for (int i = 0; i < excelList.size(); i++) {
            List<Object> objects = excelList.get(i);
            if(objects!=null) {
                String bankName = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ZERO));
                String branchName = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ONE));
                String remark = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_TWO));
                if (StringUtils.isBlank(bankName)
                        && StringUtils.isBlank(branchName)
                        &&  StringUtils.isBlank(remark)) {
                    continue;
                }
                if(StringUtils.isBlank(bankName)
                        || (bankName.length() < CommonConstant.INTEGER_FOUR || bankName.length() > CommonConstant.INT_TWENTY)){
                    numberFailures ++;
                    continue;
                }
                if(StringUtils.isBlank(branchName)
                        || (branchName.length() < CommonConstant.INTEGER_FOUR || branchName.length()>CommonConstant.INT_THIRTY)){
                    numberFailures ++;
                    continue;
                }
                singleBankDto = new ImportBankListRequestDto();
                singleBankDto.setBankName(bankName);
                singleBankDto.setBranchName(branchName);
                singleBankDto.setRemark(remark);
                setContains.add(singleBankDto);
            }
        }
        if(!setContains.isEmpty()){
            importList.addAll(setContains);
        }
        requestDto.setImportList(importList);
        requestDto.setNumberFailures(ConverterUtils.toString(numberFailures));
        return requestDto;
    }
}
