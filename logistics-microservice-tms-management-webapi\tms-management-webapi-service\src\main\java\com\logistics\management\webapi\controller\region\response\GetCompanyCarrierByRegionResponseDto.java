package com.logistics.management.webapi.controller.region.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/6/4 11:33
 */
@Data
public class GetCompanyCarrierByRegionResponseDto {
    /**
     * 车主类型 1公司 2 个人
     */
    @ApiModelProperty("车主类型 1公司 2 个人")
    private String companyType="";

    /**
     * 车主公司名称
     */
    @ApiModelProperty("车主公司名称")
    private String companyName="";

    /**
     * 车主公司ID
     */
    @ApiModelProperty("车主公司ID")
    private String companyId="";
}
