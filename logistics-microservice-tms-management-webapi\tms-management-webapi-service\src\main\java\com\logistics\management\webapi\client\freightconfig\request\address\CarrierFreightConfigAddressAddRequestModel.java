package com.logistics.management.webapi.client.freightconfig.request.address;

import com.logistics.management.webapi.client.freightconfig.request.ladder.CarrierFreightConfigLadderRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.scheme.CarrierFreightConfigSchemeRequestModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigAddressAddRequestModel extends CarrierFreightConfigSchemeRequestModel {

    @ApiModelProperty(value = "发货省ID")
    private Long fromProvinceId;

    @ApiModelProperty(value = "发货省名字")
    private String fromProvinceName;

    @ApiModelProperty(value = "发货城市ID")
    private Long fromCityId;

    @ApiModelProperty(value = "发货城市名字")
    private String fromCityName;

    @ApiModelProperty(value = "发货县区")
    private List<FromArea> fromAreaId;

    @ApiModelProperty(value = "卸货省ID")
    private Long toProvinceId;

    @ApiModelProperty(value = "卸货省名字")
    private String toProvinceName;

    @ApiModelProperty(value = "卸货城市ID")
    private Long toCityId;

    @ApiModelProperty(value = "卸货城市名字")
    private String toCityName;

    @ApiModelProperty(value = "卸货县区ID")
    private List<FromArea> toAreaId;

    @ApiModelProperty(value = "阶梯配置")
    private List<CarrierFreightConfigLadderRequestModel> ladderConfigList;

    @Data
    public static class FromArea {

        @ApiModelProperty(value = "发货县区ID")
        private Long fromAreaId;

        @ApiModelProperty(value = "发货县区名称")
        private String fromAreaName;
    }
}
