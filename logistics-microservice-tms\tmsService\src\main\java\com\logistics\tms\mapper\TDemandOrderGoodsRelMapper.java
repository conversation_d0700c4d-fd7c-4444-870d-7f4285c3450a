package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TDemandOrderGoodsRel;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TDemandOrderGoodsRelMapper extends BaseMapper<TDemandOrderGoodsRel> {

    int batchInsert(@Param("list") List<TDemandOrderGoodsRel> list);

    List<TDemandOrderGoodsRel> getByDemandOrderIds(@Param("demandOrderIds")String demandOrderIds);

    List<TDemandOrderGoodsRel> getInvalidByDemandOrderId(@Param("demandOrderId")Long demandOrderId);
}