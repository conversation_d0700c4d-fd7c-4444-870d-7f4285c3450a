package com.logistics.management.webapi.api.feign.insuarance.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.insuarance.InsuranceApi;
import com.logistics.management.webapi.api.feign.insuarance.dto.*;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @author: wjf
 * @date: 2019/6/4 17:38
 */
@Component
public class InsuranceApiHystrix implements InsuranceApi {

    @Override
    public Result<PageInfo<SearchInsuranceListResponseDto>> searchInsuranceList(SearchInsuranceListRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<GetInsuranceDetailResponseDto> getInsuranceDetail(InsuranceIdRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result addOrModifyInsurance(AddOrModifyInsuranceRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result cancelInsurance(CancelInsuranceRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void exportInsurance(SearchInsuranceListRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }

    @Override
    public Result<ImportInsuranceResponseDto> importInsurance(MultipartFile file, HttpServletRequest request) {
        return Result.timeout();
    }

    @Override
    public Result importInsuranceCertificateInfo(MultipartFile file, HttpServletRequest request) {
        return Result.timeout();
    }

    @Override
    public Result<GetInsuranceInfoByVehicleIdResponseDto> getInsuranceInfoByVehicleId(GetInsuranceInfoByVehicleIdRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result confirmRefund(ConfirmRefundRequestDto requestDto) {
        return Result.timeout();
    }
}
