package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DemandOrderDetailResponseModel {

    private Long demandId;
    private String demandOrderCode;
    private String customerOrderCode;
    private Integer entrustStatus;
    private Integer status;
    private Integer ifCancel;
    private Integer ifEmpty;
    private Integer ifRollback;//是否回退
    private String rollbackRemark;
    private String cancelReason;
    private String customerCompanyName;
    private String customerCompanyPhone;

    private String publishName;
    private Date publishTime;
    private String remark;
    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    private String consignorName;
    private String consignorMobile;
    private Date expectedLoadTime;
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;
    private String receiverName;
    private String receiverMobile;
    private Date expectedUnloadTime;
    private Long companyEntrustId;
    private String companyEntrustName;
    private Integer goodsUnit;
    private String upstreamCustomer;

    private Integer settlementTonnage;
    private BigDecimal goodsAmount;
    private Integer source;//委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件
    private Integer entrustType;

    private Integer contractPriceType;
    private BigDecimal contractPrice;

    private Integer exceptContractPriceType;
    private BigDecimal exceptContractPrice;

    private Integer carrierPriceType;
    private BigDecimal carrierPrice;

    private BigDecimal settlementCostTotal;
    private BigDecimal settlementAmount;
    private Integer priceType;
    @ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer carrierSettlement;
    @ApiModelProperty("车主结算费用合计")
    private BigDecimal carrierSettlementCostTotal;
    @ApiModelProperty("车主结算数量")
    private BigDecimal carrierSettlementAmount;
    @ApiModelProperty("车主报价类型 1 单价 2 整车价")
    private Integer carrierPaymentPriceType;
    private Integer orderMode;

    private Date ticketTime;

    @ApiModelProperty("下单类型：10 发布，20 拉取，21 推送")
    private Integer orderType;
    @ApiModelProperty("中石化订单号")
    private String sinopecOrderNo;

    private List<DemandOrderGoodsResponseModel> goodsResponseModel;
    private List<DemandOrderCarrierResponseModel> carrierResponseModel;
    private List<DemandOrderEventResponseModel> eventResponseModel;
    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;


    private Long companyCarrierId;
    private Integer companyCarrierType;
    private String companyCarrierName;
    private Long carrierContactId;
    private String carrierContactName;
    private String carrierContactMobile;

    //修改车主后-修改原因、价格
    private String modifyCarrierReason;
    private Integer modifyCarrierPriceType;
    private BigDecimal modifyCarrierPrice;

    private Long myCompanyCarrierId;
}
