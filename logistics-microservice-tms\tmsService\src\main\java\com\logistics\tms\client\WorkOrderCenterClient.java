package com.logistics.tms.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.logistics.tms.base.constant.ConfigKeyConstant;
import com.logistics.tms.base.enums.EntrustDataExceptionEnum;
import com.logistics.tms.base.enums.WorkOrderCenterRequestUrlEnum;
import com.logistics.tms.client.model.WorkOrderCancelSyncRequestDto;
import com.logistics.tms.client.model.WorkOrderReportSyncRequestDto;
import com.yelo.tools.utils.HttpClientUtils;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class WorkOrderCenterClient {

    @Resource
    private ConfigKeyConstant configKeyConstant;




    /**
     * 上报异常同步
     * @param requestDto 请求DTO
     */
    public void reportWorkOrderSync(WorkOrderReportSyncRequestDto requestDto) {
        this.requestTaskCenterClient(WorkOrderCenterRequestUrlEnum.CREATE_RECEIVE_TASK, requestDto, new TypeReference<>() {
        });
    }

    /**
     * 撤销异常工单同步
     * @param requestDto 请求DTO
     */
    public void cancelWorkOrderSync(WorkOrderCancelSyncRequestDto requestDto) {
        this.requestTaskCenterClient(WorkOrderCenterRequestUrlEnum.CLOSE_RECEIVE_TASK, requestDto, new TypeReference<>() {
        });
    }

    private <T> T requestTaskCenterClient(WorkOrderCenterRequestUrlEnum urlEnum, Object param, TypeReference<Result<T>> valueTypeRef) {
        Result<T> result = null;
        String requestUrl = configKeyConstant.workOrderCenterHost + urlEnum.getUrl();
        try {
            log.info("调用任务中心【" + urlEnum.getValue() + requestUrl + "】接口 入参：" + JSONObject.toJSONString(param));
            String request = HttpClientUtils.requestWithJsonWithHeadersUsingPost(requestUrl, null,    JSON.toJSONString(param));
            JSONObject resultJsonObject  =JSON.parseObject(JSON.toJSONString(request));
            log.info("调用任务中心【" + urlEnum.getValue() + requestUrl + "】接口 返参：" + resultJsonObject.toJSONString());
            result = resultJsonObject.toJavaObject(valueTypeRef);
        } catch (Exception e) {
            log.warn("调用任务中心【" + urlEnum.getValue() + requestUrl + "】接口失败：" + e.getMessage());
        }
        if (result == null) {
            throw new BizException(EntrustDataExceptionEnum.NETWORK_CONNECTION_FAILURE);
        }
        result.throwException();
        return result.getData();
    }
}