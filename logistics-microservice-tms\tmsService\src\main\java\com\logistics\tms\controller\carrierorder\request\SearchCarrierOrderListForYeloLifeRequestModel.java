package com.logistics.tms.controller.carrierorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/19
 */
@Data
public class SearchCarrierOrderListForYeloLifeRequestModel extends AbstractPageForm<SearchCarrierOrderListForYeloLifeRequestModel> {

	@ApiModelProperty("运单状态 10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0 已取消")
	private Integer status;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("客户")
	private String customName;

	@ApiModelProperty("车牌号")
	private String vehicleNo;

	@ApiModelProperty("司机")
	private String driver;

	@ApiModelProperty("发货仓库")
	private String loadWareHouse;

	@ApiModelProperty("发货地址")
	private String loadAddress;

	@ApiModelProperty("发货人")
	private String consignorName;

	@ApiModelProperty("收货仓库")
	private String unloadWareHouse;

	@ApiModelProperty("收货地址")
	private String unloadAddress;

	@ApiModelProperty("收货人")
	private String receiverName;

	@ApiModelProperty("客户单号")
	private String customerOrderCode;

	@ApiModelProperty("下单时间from")
	private String demandCreatedTimeFrom;

	@ApiModelProperty("下单时间to")
	private String demandCreatedTimeTo;

	@ApiModelProperty("来源")
	private Integer customerOrderSource;

	@ApiModelProperty("品名")
	private String goodsName;

	@ApiModelProperty("导出选中的id")
	private String carrierOrderIds;

	@ApiModelProperty("批量查询-需求单号")
	private List<String> demandOrderCodeList;

	@ApiModelProperty("批量查询-运单号")
	private List<String> carrierOrderCodeList;


	private List<Long> carrierOrderIdList;//运单筛选条件借用字段

	/**
	 * 委托类型：100 新生回收，101 新生销售
	 */
	@ApiModelProperty("委托类型：100 新生回收，101 新生销售")
	private Integer entrustType;

	/**
	 * 出库状态：0 待出库，2 已出库
	 */
	@ApiModelProperty("出库状态：0 待出库，2 已出库")
	private Integer outStatus;


	@ApiModelProperty("是否按编码回收 0:否 1:是   v2.44")
	private Integer ifRecycleByCode;
}
