package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.demandorderobjection.model.SearchDemandOrderObjectionRequestModel;
import com.logistics.tms.api.feign.demandorderobjection.model.SearchDemandOrderObjectionResponseModel;
import com.logistics.tms.entity.TDemandOrderObjection;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TDemandOrderObjectionMapper extends BaseMapper<TDemandOrderObjection> {

    int batchInsert(@Param("list") List<TDemandOrderObjection> list);

    int batchUpdate(@Param("list") List<TDemandOrderObjection> list);

    List<Long> searchDemandOrderObjectionIds(@Param("condition") SearchDemandOrderObjectionRequestModel requestModel);

    List<SearchDemandOrderObjectionResponseModel> searchDemandOrderObjection(@Param("ids") String ids);

    List<TDemandOrderObjection> getByDemandOrderIds(@Param("demandOrderIds")String demandOrderIds);
}