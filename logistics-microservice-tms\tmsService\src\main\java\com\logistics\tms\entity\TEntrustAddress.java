package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TEntrustAddress extends BaseEntity {
    /**
    * 公司id
    */
    @ApiModelProperty("公司id")
    private Long companyEntrustId;

    /**
     * 购货公司名称
     */
    @ApiModelProperty("购货公司名称")
    private String companyName;

    /**
    * 地址类型：1 发货，2 收货
    */
    @ApiModelProperty("地址类型：1 发货，2 收货")
    private Integer addressType;

    /**
    * 省份ID
    */
    @ApiModelProperty("省份ID")
    private Long provinceId;

    /**
    * 省份名字
    */
    @ApiModelProperty("省份名字")
    private String provinceName;

    /**
    * 城市ID
    */
    @ApiModelProperty("城市ID")
    private Long cityId;

    /**
    * 城市名字
    */
    @ApiModelProperty("城市名字")
    private String cityName;

    /**
    * 县区id
    */
    @ApiModelProperty("县区id")
    private Long areaId;

    /**
    * 县区名字
    */
    @ApiModelProperty("县区名字")
    private String areaName;

    /**
    * 详细地址
    */
    @ApiModelProperty("详细地址")
    private String detailAddress;

    /**
    * 仓库名
    */
    @ApiModelProperty("仓库名")
    private String warehouse;

    /**
    * 联系人
    */
    @ApiModelProperty("联系人")
    private String contactName;

    /**
    * 联系电话
    */
    @ApiModelProperty("联系电话")
    private String contactMobile;

    /**
    * 添加人id（委托方账号id）
    */
    @ApiModelProperty("添加人id（委托方账号id）")
    private Long entrustContactId;

    /**
    * 启用禁用状态: 0 禁用 1 启用
    */
    @ApiModelProperty("启用禁用状态: 0 禁用 1 启用")
    private Integer enabled;
}