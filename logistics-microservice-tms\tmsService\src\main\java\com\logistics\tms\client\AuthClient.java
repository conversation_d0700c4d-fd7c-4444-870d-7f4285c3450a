package com.logistics.tms.client;

import com.logistics.tms.client.feign.auth.AuthTokenServiceApi;
import com.logistics.tms.client.feign.auth.request.CreateToken;
import com.logistics.tms.client.feign.auth.response.TokenModule;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author：wjf
 * @date：2021/5/12 15:55
 */
@Service
public class AuthClient {

    @Resource
    private AuthTokenServiceApi authTokenServiceApi;

    /**
     * 登录生成token
     * @param requestModel
     * @return
     */
    public TokenModule loginCreateToken(CreateToken requestModel){
        Result<TokenModule> result = authTokenServiceApi.createToken(requestModel);
        result.throwException();
        return result.getData();
    }
}
