package com.logistics.management.webapi.controller.workgroup.mapping;

import com.logistics.management.webapi.base.enums.AmountSymbolEnum;
import com.logistics.management.webapi.base.enums.WorkGroupAmountTypeEnum;
import com.logistics.management.webapi.base.enums.WorkGroupOrderNodeEnum;
import com.logistics.management.webapi.base.enums.WorkGroupOrderTypeEnum;
import com.logistics.management.webapi.client.workgroup.response.WorkGroupNodeListResponseModel;
import com.logistics.management.webapi.client.workgroup.response.WorkGroupNodeResponseModel;
import com.logistics.management.webapi.controller.workgroup.response.WorkGroupNodeListResponseDto;
import com.logistics.management.webapi.controller.workgroup.response.WorkGroupNodeResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;

public class GetNodeInfoMapping extends MapperMapping<WorkGroupNodeResponseModel, WorkGroupNodeResponseDto> {
    @Override
    public void configure() {
        if (ListUtils.isNotEmpty(getSource().getNodeList())) {
            getDestination().setNodeList(MapperUtils.mapper(getSource().getNodeList(), WorkGroupNodeListResponseDto.class, new MapperMapping<>() {
                @Override
                public void configure() {
                    WorkGroupNodeListResponseModel source = getSource();
                    WorkGroupNodeListResponseDto destination = getDestination();

                    destination.setOrderTypeLabel(WorkGroupOrderTypeEnum.getEnum(source.getOrderType()).getValue());
                    destination.setOrderNodeLabel(WorkGroupOrderNodeEnum.getEnum(source.getOrderNode()).getValue());
                    destination.setAmountRequire(source.getAmountRequire().stripTrailingZeros().toPlainString());

                    String amountRequireLabel = WorkGroupAmountTypeEnum.getEnum(source.getAmountType()).getValue()
                            + AmountSymbolEnum.getEnum(source.getAmountSymbol()).getValue()
                            + source.getAmountRequire().stripTrailingZeros().toPlainString();
                    destination.setAmountRequireLabel(amountRequireLabel);
                }
            }));
        }
    }
}
