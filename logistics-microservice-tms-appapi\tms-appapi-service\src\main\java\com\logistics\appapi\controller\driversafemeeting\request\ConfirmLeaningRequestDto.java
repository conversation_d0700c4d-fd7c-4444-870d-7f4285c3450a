package com.logistics.appapi.controller.driversafemeeting.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2019/11/8 13:27
 */
@Data
public class ConfirmLeaningRequestDto {
    @ApiModelProperty("学习例会关系id")
    @NotBlank(message = "id不能为空")
    private String safeMeetingRelationId;
    @ApiModelProperty(value = "驾驶员图片")
    @NotBlank(message = "请采集照片")
    private String staffDriverImageUrl;
    @ApiModelProperty(value = "签字图片")
    @NotBlank(message = "请签字完成学习例会")
    private String signImageUrl;
    @ApiModelProperty(value = "经度")
    private String longitude;
    @ApiModelProperty(value = "纬度")
    private String latitude;
}
