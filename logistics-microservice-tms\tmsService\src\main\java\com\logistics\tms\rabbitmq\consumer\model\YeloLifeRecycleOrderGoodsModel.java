package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 新生回收单货物
 *
 * <AUTHOR>
 * @date ：Created in 2022/8/20
 */
@Data
public class YeloLifeRecycleOrderGoodsModel {

	@ApiModelProperty("skuCode")
	private String skuCode;

	@ApiModelProperty("货物名")
	private String goodsName;

	@ApiModelProperty("货物数量")
	private BigDecimal goodsAmount;

	@ApiModelProperty("再生料-定价类型(1.重量 2.数量)")
	private Integer goodsUnit;

	@ApiModelProperty("货物单价")
	private BigDecimal goodsPrice;
}
