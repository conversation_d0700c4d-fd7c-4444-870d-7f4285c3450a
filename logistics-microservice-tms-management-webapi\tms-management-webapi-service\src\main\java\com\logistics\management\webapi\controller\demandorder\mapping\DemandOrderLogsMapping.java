package com.logistics.management.webapi.controller.demandorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.demandorder.response.GetDemandOrderLogsResponseModel;
import com.logistics.management.webapi.controller.demandorder.response.GetDemandOrderLogsResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @author: wjf
 * @date: 2018/11/6 10:34
 */
public class DemandOrderLogsMapping extends MapperMapping<GetDemandOrderLogsResponseModel,GetDemandOrderLogsResponseDto> {
    @Override
    public void configure() {
        GetDemandOrderLogsResponseModel model = getSource();
        GetDemandOrderLogsResponseDto dto = getDestination();
        if (model != null){
            dto.setOperateTime(DateUtils.dateToString(model.getOperateTime(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
        }
    }
}
