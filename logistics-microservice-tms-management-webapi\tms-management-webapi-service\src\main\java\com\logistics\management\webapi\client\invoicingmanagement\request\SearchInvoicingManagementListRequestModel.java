package com.logistics.management.webapi.client.invoicingmanagement.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/3/19 16:19
 */
@Data
public class SearchInvoicingManagementListRequestModel extends AbstractPageForm<SearchInvoicingManagementListRequestModel> {

    @ApiModelProperty("业务名称")
    private String businessName;

    @ApiModelProperty("开票月份-开始")
    private String invoicingMonthStart;
    @ApiModelProperty("开票月份-结束")
    private String invoicingMonthEnd;

    @ApiModelProperty("承运商名称")
    private String companyCarrierName;

    @ApiModelProperty("业务类型：1 包装业务，2 自营业务")
    private Integer businessType;
}
