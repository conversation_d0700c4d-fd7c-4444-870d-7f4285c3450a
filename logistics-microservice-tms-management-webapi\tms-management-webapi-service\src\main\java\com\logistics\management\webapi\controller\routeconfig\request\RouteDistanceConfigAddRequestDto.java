package com.logistics.management.webapi.controller.routeconfig.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
public class RouteDistanceConfigAddRequestDto {

    @ApiModelProperty(value = "发货省份ID", required = true)
    @NotBlank(message = "发货省份ID不允许为空")
    private String fromProvinceId;

    @ApiModelProperty(value = "发货省份名字", required = true)
    @NotBlank(message = "发货省份名字不允许为空")
    private String fromProvinceName;

    @ApiModelProperty(value = "发货城市ID", required = true)
    @NotBlank(message = "发货城市ID不允许为空")
    private String fromCityId;

    @ApiModelProperty(value = "发货城市名字", required = true)
    @NotBlank(message = "发货城市名字不允许为空")
    private String fromCityName;

    @ApiModelProperty(value = "发货县区ID", required = true)
    @NotBlank(message = "发货县区ID不允许为空")
    private String fromAreaId;

    @ApiModelProperty(value = "发货县区名字", required = true)
    @NotBlank(message = "发货县区名字不允许为空")
    private String fromAreaName;

    @ApiModelProperty(value = "卸货省份ID", required = true)
    @NotBlank(message = "卸货省份ID不允许为空")
    private String toProvinceId;

    @ApiModelProperty(value = "卸货省份名字", required = true)
    @NotBlank(message = "卸货省份名字不允许为空")
    private String toProvinceName;

    @ApiModelProperty(value = "卸货城市ID", required = true)
    @NotBlank(message = "卸货城市ID不允许为空")
    private String toCityId;

    @ApiModelProperty(value = "卸货城市名字", required = true)
    @NotBlank(message = "卸货城市名字不允许为空")
    private String toCityName;

    @ApiModelProperty(value = "卸货县区ID", required = true)
    @NotBlank(message = "卸货县区ID不允许为空")
    private String toAreaId;

    @ApiModelProperty(value = "卸货县区名字", required = true)
    @NotBlank(message = "卸货县区名字不允许为空")
    private String toAreaName;

    @ApiModelProperty(value = "计费距离", required = true)
    @NotBlank(message = "计费距离不允许为空")
    @Min(value = 1, message = "计费距离不允许为 0 ")
    @Max(value = 10000, message = "计费距离不允许超过10000")
    private String billingDistance;
}
