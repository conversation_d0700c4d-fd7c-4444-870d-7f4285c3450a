<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TLoanSettlementRecordMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TLoanSettlementRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="loan_records_id" jdbcType="BIGINT" property="loanRecordsId" />
    <result column="deducting_month" jdbcType="VARCHAR" property="deductingMonth" />
    <result column="settlement_date" jdbcType="TIMESTAMP" property="settlementDate" />
    <result column="settlement_fee" jdbcType="DECIMAL" property="settlementFee" />
    <result column="remaining_repayment_fee" jdbcType="DECIMAL" property="remainingRepaymentFee" />
    <result column="total_fee" jdbcType="DECIMAL" property="totalFee" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, loan_records_id, deducting_month, settlement_date, settlement_fee, remaining_repayment_fee, 
    total_fee, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_loan_settlement_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_loan_settlement_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TLoanSettlementRecord">
    insert into t_loan_settlement_record (id, loan_records_id, deducting_month, 
      settlement_date, settlement_fee, remaining_repayment_fee, 
      total_fee, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{loanRecordsId,jdbcType=BIGINT}, #{deductingMonth,jdbcType=VARCHAR}, 
      #{settlementDate,jdbcType=TIMESTAMP}, #{settlementFee,jdbcType=DECIMAL}, #{remainingRepaymentFee,jdbcType=DECIMAL}, 
      #{totalFee,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TLoanSettlementRecord">
    insert into t_loan_settlement_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="loanRecordsId != null">
        loan_records_id,
      </if>
      <if test="deductingMonth != null">
        deducting_month,
      </if>
      <if test="settlementDate != null">
        settlement_date,
      </if>
      <if test="settlementFee != null">
        settlement_fee,
      </if>
      <if test="remainingRepaymentFee != null">
        remaining_repayment_fee,
      </if>
      <if test="totalFee != null">
        total_fee,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="loanRecordsId != null">
        #{loanRecordsId,jdbcType=BIGINT},
      </if>
      <if test="deductingMonth != null">
        #{deductingMonth,jdbcType=VARCHAR},
      </if>
      <if test="settlementDate != null">
        #{settlementDate,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementFee != null">
        #{settlementFee,jdbcType=DECIMAL},
      </if>
      <if test="remainingRepaymentFee != null">
        #{remainingRepaymentFee,jdbcType=DECIMAL},
      </if>
      <if test="totalFee != null">
        #{totalFee,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TLoanSettlementRecord">
    update t_loan_settlement_record
    <set>
      <if test="loanRecordsId != null">
        loan_records_id = #{loanRecordsId,jdbcType=BIGINT},
      </if>
      <if test="deductingMonth != null">
        deducting_month = #{deductingMonth,jdbcType=VARCHAR},
      </if>
      <if test="settlementDate != null">
        settlement_date = #{settlementDate,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementFee != null">
        settlement_fee = #{settlementFee,jdbcType=DECIMAL},
      </if>
      <if test="remainingRepaymentFee != null">
        remaining_repayment_fee = #{remainingRepaymentFee,jdbcType=DECIMAL},
      </if>
      <if test="totalFee != null">
        total_fee = #{totalFee,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TLoanSettlementRecord">
    update t_loan_settlement_record
    set loan_records_id = #{loanRecordsId,jdbcType=BIGINT},
      deducting_month = #{deductingMonth,jdbcType=VARCHAR},
      settlement_date = #{settlementDate,jdbcType=TIMESTAMP},
      settlement_fee = #{settlementFee,jdbcType=DECIMAL},
      remaining_repayment_fee = #{remainingRepaymentFee,jdbcType=DECIMAL},
      total_fee = #{totalFee,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>