package com.logistics.management.webapi.api.feign.driversafemeeting.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/11/4 9:13
 */
@Data
public class AddDriverSafeMeetingRequestDto {
    @ApiModelProperty(value = "驾驶员",required = true)
    @NotEmpty(message = "请选择驾驶员")
    private List<String> driverIdList;
    @ApiModelProperty(value = "类型",required = true)
    @NotBlank(message = "安全例会类型 1:安全例会 2：紧急培训")
    private String type;
    @ApiModelProperty(value = "学习月份",required = true)
    @NotBlank(message = "请选择学习的月份")
    private String period;
    @ApiModelProperty(value = "学习标题",required = true)
    @Size(min = 1, max = 255, message = "请填写标题，1-255字")
    private String title;
    @ApiModelProperty(value = "学习简介",required = true)
    @Size(min = 1, max = 255, message = "请填写简介，1-255字")
    private String introduction;
    @ApiModelProperty(value = "学习内容",required = true)
    @Size(min = 1, max = 20000, message = "请填写内容，1-10000字")
    private String content;
}
