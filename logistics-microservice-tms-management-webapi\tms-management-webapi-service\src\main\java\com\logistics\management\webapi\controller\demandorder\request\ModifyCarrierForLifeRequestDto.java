package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class ModifyCarrierForLifeRequestDto {

    /**
     * 需求单id
     */
    @ApiModelProperty(value = "需求单ID", required = true)
    @NotEmpty(message = "需求单ID不能为空")
    private List<String> demandOrderIds;

    /**
     * 是否我司 1:我司,2:其他车主
     */
    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主", required = true)
    @NotBlank(message = "请选择车主类型")
    @Pattern(regexp = "^[12]$", message = "请选择车主类型")
    private String isOurCompany;

    /**
     * 车主ID（其他车主时必填）
     */
    @ApiModelProperty(value = "车主ID（其他车主时必填）")
    private String companyCarrierId;

    /**
     * 修改原因
     */
    @ApiModelProperty(value = "修改原因", required = true)
    @NotBlank(message = "修改原因不能为空")
    @Size(min = 1, max = 300, message = "修改原因不能大于300")
    private String modifyReason;
}
