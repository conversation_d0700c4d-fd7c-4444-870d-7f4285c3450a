package com.logistics.tms.controller.freightconfig.request.address;

import com.logistics.tms.controller.freightconfig.request.ladder.CarrierFreightConfigLadderRequestModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightAddressEditEnableRequestModel extends CarrierFreightAddressDetailRequestModel {

    @ApiModelProperty(value = "编辑、新增阶梯配置")
    @Size(max = 10, message = "一条路线最多存在10条阶梯价格")
    private List<CarrierFreightConfigLadderRequestModel> ladderConfigList;

    @ApiModelProperty(value = "移除阶梯配置Id集合; 父级阶梯删除只传父级阶梯Id")
    private List<String> removeLadderConfigIds;
}
