package com.logistics.management.webapi.controller.companycarrierauthorization.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/3
 */
@Data
public class CarrierAuthorizationDetailRequestDto {

	@ApiModelProperty(value = "车主授权信息id", required = true)
	@NotBlank(message = "请选择要看的记录")
	private String carrierAuthorizationId;
}
