package com.logistics.management.webapi.base.enums;


public enum VehicleHistoryAuditStatusEnum {
    NOT_NEED_AUDIT(-1,"无需审核"),
    WAIT_AUDIT(0,"待审核"),
    AUDIT(1,"已审核"),
    REJECT(2,"已驳回"),
    ;

    private Integer key;
    private String value;

    VehicleHistoryAuditStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static VehicleHistoryAuditStatusEnum getEnum(Integer key) {
        for (VehicleHistoryAuditStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
