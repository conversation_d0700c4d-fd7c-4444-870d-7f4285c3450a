package com.logistics.tms.base.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/5/30 14:11
 */
public enum CertificationPicturesFileTypeEnum {
    CERTIFICATION_PICTURES_NULL_ENUM(CertificationPicturesObjectTypeEnum.OBJECT_TYPE_DEFAULT_ENUM,0,""),
    PERSONAL_ACCIDENT_INSURANCE(CertificationPicturesObjectTypeEnum.T_PERSONAL_ACCIDENT_INSURANCE,1,"个人意外险保单"),
    OCCUPATIONAL_RECORD(CertificationPicturesObjectTypeEnum.T_STAFF_DRIVER_OCCUPATIONAL_RECORD,1,"司机从业资格证"),
    INTEGRITY_EXAMINATION_RECORD(CertificationPicturesObjectTypeEnum.T_STAFF_DRIVER_INTEGRITY_EXAMINATION_RECORD,1,"诚信考核记录"),
    CONTINUE_LEARNING_RECORD(CertificationPicturesObjectTypeEnum.T_STAFF_DRIVER_CONTINUE_LEARNING_RECORD,1,"继续教育记录"),
    COMMERCIAL_INSURANCE(CertificationPicturesObjectTypeEnum.T_INSURANCE,1,"商业险保单"),
    COMPULSORY_INSURANCE(CertificationPicturesObjectTypeEnum.T_INSURANCE,2,"交强险保单"),
    PERSONAL_INSURANCE(CertificationPicturesObjectTypeEnum.T_INSURANCE,3,"个人意外险保单"),
    CARGO_INSURANCE(CertificationPicturesObjectTypeEnum.T_INSURANCE,4,"货物险保单"),
    CARRIER_INSURANCE(CertificationPicturesObjectTypeEnum.T_INSURANCE,5,"危货承运人险保单"),
    VIOLATION_REGULATIONS(CertificationPicturesObjectTypeEnum.T_VIOLATION_REGULATIONS,1,"违章事故凭证"),
    DRIVING_LICENSE_ANNUAL_REVIEW(CertificationPicturesObjectTypeEnum.T_VEHICLE_DRIVING_LICENSE_ANNUAL_REVIEW,1,"车辆行驶证检查记录凭证"),
    STAFF_IDENTITY_FRONT(CertificationPicturesObjectTypeEnum.T_STAFF_BASIC,1,"身份证人像面"),
    STAFF_IDENTITY_BACK(CertificationPicturesObjectTypeEnum.T_STAFF_BASIC,2,"身份证国徽面"),
    DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_FRONT(CertificationPicturesObjectTypeEnum.T_STAFF_DRIVER_CREDENTIAL,1,"从业资格证（卡片）正面"),
    DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_BACK(CertificationPicturesObjectTypeEnum.T_STAFF_DRIVER_CREDENTIAL,2,"从业资格证（卡片）反面"),
    DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_FRONT(CertificationPicturesObjectTypeEnum.T_STAFF_DRIVER_CREDENTIAL,3,"从业资格证（纸质）正面"),
    DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_BACK(CertificationPicturesObjectTypeEnum.T_STAFF_DRIVER_CREDENTIAL,4,"从业资格证（纸质）反面"),
    DRIVER_LICENSE_FRONT(CertificationPicturesObjectTypeEnum.T_STAFF_DRIVER_CREDENTIAL,5,"机动车驾驶证正面"),

    VEHICLE_GPS_RECORD(CertificationPicturesObjectTypeEnum.T_VEHICLE_GPS_RECORD,1,"车辆GPS安装记录凭证"),
    VEHICLE_GRADE_ESTIMATION_RECORD(CertificationPicturesObjectTypeEnum.T_VEHICLE_GRADE_ESTIMATION_RECORD,1,"车辆等级评定凭证"),
    VEHICLE_ROAD_TRANSPORT_CERTIFICATE_ANNUAL_REVIEW(CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE_ANNUAL_REVIEW,1,"车辆道路运输证年审记录凭证"),
    VEHICLE_TIRE(CertificationPicturesObjectTypeEnum.T_VEHICLE_TIRE,1,"轮胎凭证"),

    VEHICLE_DRIVING_PERMIT_FRONT(CertificationPicturesObjectTypeEnum.T_VEHICLE_DRIVING_LICENSE,1,"机动车行驶证正面"),
    VEHICLE_DRIVING_PERMIT_BACK(CertificationPicturesObjectTypeEnum.T_VEHICLE_DRIVING_LICENSE,2,"机动车行驶证反面"),
    ROAD_TRANSPORT_CERTIFICATE_CARD_FRONT(CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE,1,"道路运输证(卡片)正面"),
    ROAD_TRANSPORT_CERTIFICATE_CARD_BACK(CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE,2,"道路运输证(卡片)反面"),
    ROAD_TRANSPORT_CERTIFICATE_PAPER_FRONT(CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE,3,"道路运输证(纸质)正面"),
    ROAD_TRANSPORT_CERTIFICATE_PAPER_BACK(CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE,4,"道路运输证(纸质)反面"),
    VEHICLE_BASIC_INSPECTION_PHOTOS(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,1,"车辆检查照片"),
    VEHICLE_BASIC_ACCESS_CHECKLIST(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,2,"危化品运输车辆准入检查表"),
    VEHICLE_BASIC_REGISTRATION_FRONT(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,3,"登记证正面"),
    VEHICLE_BASIC_REGISTRATION_BACK(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,4,"登记证反面"),
    VEHICLE_BASIC_QUALITY_CERTIFICATE(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,5,"车辆合格证"),
    VEHICLE_BASIC_PURCHASE_INVOICE(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,6,"车辆购置发票"),
    VEHICLE_BASIC_PURCHASE_TAX_INVOICE(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,7,"车辆购置税发票"),
    VEHICLE_BASIC_VESSEL_TAX_INVOICE(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,8,"挂车车船税发票"),
    VEHICLE_BASIC_VESSEL_ELSE_FILE(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,9,"其他凭证"),
    VEHICLE_BASIC_OUTAGE_FILE(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC,10,"车辆报废停运图片"),



    FEEDBACK_FILE(CertificationPicturesObjectTypeEnum.T_FEEDBACK,1,"反馈图片信息"),

    DRIVER_PAYEE_IMAGE_FILE(CertificationPicturesObjectTypeEnum.T_DRIVER_PAYEE,1,"收款证明"),
    DRIVER_PAYEE_ID_FRONT_IMAGE_FILE(CertificationPicturesObjectTypeEnum.T_DRIVER_PAYEE,2,"身份证正面"),
    DRIVER_PAYEE_ID_BACK_IMAGE_FILE(CertificationPicturesObjectTypeEnum.T_DRIVER_PAYEE,3,"身份证反面"),

    CARRIER_COMPLAINT_IMAGE_FILE(CertificationPicturesObjectTypeEnum.T_CARRIER_COMPLAINT,1,"投诉凭证"),

    CARRIER_FEEDBACK_IMAGE_FILE(CertificationPicturesObjectTypeEnum.T_CARRIER_FEEDBACK,1,"车主反馈凭证"),

    LOAN_RECORDS_FILE(CertificationPicturesObjectTypeEnum.T_LOAN_RECORDS,1,"贷款记录附件"),
    OIL_FILLED_ATTACHMENT_FILE(CertificationPicturesObjectTypeEnum.T_OIL_FILLED,1,"充油附件"),
    OIL_REFUND_ATTACHMENT_FILE(CertificationPicturesObjectTypeEnum.T_OIL_FILLED,2,"油费退款附件"),
    VEHICLE_SETTLEMENT_ATTACHMENT(CertificationPicturesObjectTypeEnum.T_VEHICLE_SETTLEMENT,1,"车辆结算附件"),
    INSURANCE_COSTS_ATTACHMENT_FILE(CertificationPicturesObjectTypeEnum.T_INSURANCE_COSTS,1,"保险费用附件"),//保险费用模块已删除
    INSURANCE_COSTS_REFUND_ATTACHMENT_FILE(CertificationPicturesObjectTypeEnum.T_INSURANCE_COSTS,2,"保险费用退款附件"),

    SAFE_CHECK_VEHICLE_IMAGE_FILE(CertificationPicturesObjectTypeEnum.T_VEHICLE_SAFE_CHECK_VEHICLE,1,"车辆安全检查"),
    SAFE_CHECK_ITEM_REFORM_IMAGE_FILE(CertificationPicturesObjectTypeEnum.T_VEHICLE_SAFE_CHECK_REFORM,1,"整改项目图片"),
    SAFE_CHECK_REFORM_IMAGE_FILE(CertificationPicturesObjectTypeEnum.T_VEHICLE_SAFE_CHECK_REFORM,2,"整改结果图片"),

    EXT_VEHICLE_SETTLEMENT_ATTACHMENT_FILE(CertificationPicturesObjectTypeEnum.T_EXT_VEHICLE_SETTLEMENT,1,"外部车辆结算附件"),

    T_DEMAND_ORDER_OBJECTION_SINOPEC_IMAGE_FILE(CertificationPicturesObjectTypeEnum.T_DEMAND_ORDER_OBJECTION_SINOPEC,1,"中石化需求单异常审核图片"),

    T_DRIVER_COST_SCENE_FILE(CertificationPicturesObjectTypeEnum.T_DRIVER_COST, 1, "现场图片"),
    T_DRIVER_COST_PAYMENT_FILE(CertificationPicturesObjectTypeEnum.T_DRIVER_COST, 2, "支付图片"),
    T_DRIVER_COST_INVOICE_FILE(CertificationPicturesObjectTypeEnum.T_DRIVER_COST, 3, "发票图片"),

    CARRIER_ORDER_OTHER_FEE(CertificationPicturesObjectTypeEnum.T_CARRIER_ORDER_OTHER_FEE, 1, "临时费用单据"),

    T_RENEWABLE_AUDIT_SCENE_PICTURE_FILE(CertificationPicturesObjectTypeEnum.T_RENEWABLE_AUDIT, 1, "现场图片"),
    T_RENEWABLE_AUDIT_CONFIRM_PICTURE_FILE(CertificationPicturesObjectTypeEnum.T_RENEWABLE_AUDIT, 2, "确认单据"),

    T_DRIVER_APPOINT_SCENE_PICTURE_FILE(CertificationPicturesObjectTypeEnum.T_DRIVER_APPOINT, 1, "现场图片"),
    T_DRIVER_APPOINT_CONFIRM_PICTURE_FILE(CertificationPicturesObjectTypeEnum.T_DRIVER_APPOINT, 2, "确认单据"),

    T_COMPANY_ACCOUNT_BANK_CARD_PIC_FILE(CertificationPicturesObjectTypeEnum.T_COMPANY_ACCOUNT_BANK_CARD_PIC, 1, "公司账户银行卡图片"),
    T_RESERVE_APPLY_REMIT_PIC_FILE(CertificationPicturesObjectTypeEnum.T_RESERVE_APPLY_PIC, 1, "备用金申请打款票据"),
    T_DRIVER_ACCOUNT_BANK_CARD_PIC_FILE(CertificationPicturesObjectTypeEnum.T_DRIVER_ACCOUNT_BANK_CARD_PIC, 1, "司机账户银行卡图片"),
    T_COMPANY_CARRIER_AUTHORIZATION_FILE(CertificationPicturesObjectTypeEnum.T_COMPANY_CARRIER_AUTHORIZATION, 1, "车主授权书"),
    T_ARCHIVED_PIC_FILE(CertificationPicturesObjectTypeEnum.T_COMPANY_CARRIER_AUTHORIZATION, 2, "归档文件"),
    ;
    private CertificationPicturesObjectTypeEnum objectType;
    private Integer fileType;
    private String fileName;

    CertificationPicturesFileTypeEnum(CertificationPicturesObjectTypeEnum objectType, Integer fileType, String fileName) {
        this.objectType = objectType;
        this.fileType = fileType;
        this.fileName = fileName;
    }

    public CertificationPicturesObjectTypeEnum getObjectType() {
        return objectType;
    }

    public Integer getFileType() {
        return fileType;
    }

    public String getFileName() {
        return fileName;
    }


    public static CertificationPicturesFileTypeEnum getEnum(Object obj,Integer fileType) {
        for (CertificationPicturesFileTypeEnum t : values()) {
            if (t.getObjectType().equals(obj) && t.getFileType().equals(fileType)) {
                return t;
            }
        }
        return CERTIFICATION_PICTURES_NULL_ENUM;
    }

    public static List<CertificationPicturesFileTypeEnum> getFileTypeEnumList(Object obj){
        List<CertificationPicturesFileTypeEnum> fileTypeEnumList = new ArrayList<>();
        for (CertificationPicturesFileTypeEnum t : values()) {
            if (t.getObjectType() == obj) {
                fileTypeEnumList.add(t);
            }
        }
        return fileTypeEnumList;
    }
}
