package com.logistics.management.webapi.base.enums;

/**
 * @Author: sj
 * @Date: 2019/10/9 10:39
 */
public enum LoanStatusEnum {
    DEFAULT(-1,""),
    WAIT(0,"待结算"),
    PART(1,"部分结算"),
    HAS(2,"已结算"),
    ;

    private Integer key;
    private String value;

    LoanStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static LoanStatusEnum getEnum(Integer key) {
        for (LoanStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
