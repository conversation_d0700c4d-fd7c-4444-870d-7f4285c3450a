package com.logistics.tms.rabbitmq.publisher;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.RabbitMqExChangeTypeEnum;
import com.logistics.tms.base.enums.RabbitMqRoutingKeyTypeEnum;
import com.logistics.tms.biz.carrierorder.model.CancelCarrierOrderChangeDemandAndCarrierStataSynchronizeModel;
import com.logistics.tms.biz.carrierorder.model.CarrierOrderSynchronizeLeyiModel;
import com.logistics.tms.biz.demandorder.model.CreateEntrustSettlementForLeYiConsumerModel;
import com.logistics.tms.biz.demandorder.model.RollbackDemandOrderToLeYiModel;
import com.logistics.tms.biz.demandorder.model.SyncTMSDemandOrderModel;
import com.logistics.tms.controller.carrierorder.response.CopyCarrierOrderByDispatchVehicleMessage;
import com.logistics.tms.controller.carrierorder.response.CopyCarrierRecycleOrderByDispatchVehicleMessage;
import com.logistics.tms.controller.carrierorder.response.SendTemplateMessageModel;
import com.logistics.tms.controller.demandorder.response.CancelDemandByDemandOrderMessage;
import com.logistics.tms.controller.demandorder.response.CancelDemandByDemandRecoverOrderMessage;
import com.logistics.tms.controller.demandorder.response.LogisticsDemandStateSynchronizeModel;
import com.logistics.tms.rabbitmq.consumer.model.CommonDelayMsg;
import com.logistics.tms.rabbitmq.consumer.model.TmsSyncAdditionalOrderModel;
import com.logistics.tms.rabbitmq.publisher.model.*;
import com.yelo.tools.rabbitmq.producer.RabbitMqSender;
import com.yelo.tools.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

/**
 * @author: wjf
 * @date: 2021/9/28 10:01
 */
@Service
@Slf4j
public class RabbitMqPublishBiz {

    @Autowired
    private RabbitMqSender rabbitMqSender;
    private static final ObjectMapper JSON_WRITER = JacksonUtils.getInstance();

    /**
     * 同步需求单卸货地址、需求单来源于TMS给云仓系统
     * @param model
     */
    public void synDemandOrderAddressSourceToWarehouse(SynDemandOrderAddressSourceToLeYiModel model) {
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("同步需求单卸货地址、需求单来源于TMS给云仓系统：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.LOGISTICS_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.SYNC_DEMAND_ORDER_UNLOAD_ADDRESS.getKey(), message);
        } catch (IOException e) {
            log.error("同步需求单卸货地址、需求单来源于TMS给云仓系统 error", e);
        }
    }

    /**
     * 同步需求单卸货地址、需求单来源于TMS给云盘系统
     * @param model
     */
    public void synDemandOrderAddressSourceToLeYi(SynDemandOrderAddressSourceToLeYiModel model) {
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("同步需求单卸货地址、需求单来源于TMS给云盘系统：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.ORDER_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.SYNC_DEMAND_ORDER_UNLOAD_ADDRESS.getKey(), message);
        } catch (IOException e) {
            log.error("同步需求单卸货地址、需求单来源于TMS给云盘系统 error", e);
        }
    }

    /**
     * 运单各节点操作同步到云仓
     * @param model
     */
    public void carrierOrderSynchronizeToWarehouse(CarrierOrderSynchronizeLeyiModel model) {
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("操作运单各节点同步给云仓：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.CARRIER_ORDER_EXCHANGE.getKey(), RabbitMqRoutingKeyTypeEnum.CARRIER_ORDER_SYNCHRONIZED_ROUTINGKEY.getKey(), message);
        } catch (IOException e) {
            log.error("操作运单各节点同步给云仓 error", e);
        }
    }


    /**
     * 同运单各节点操作同步到云盘
     *
     * @param model 运单信息
     */
    public void carrierOrderSynchronizeToLeyi(CarrierOrderSynchronizeLeyiModel model) {
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("操作运单各节点同步给云盘：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.ORDER_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.ORDER_SYNCHRONIZED_CARRIER_ORDER.getKey(), message);
        } catch (IOException e) {
            log.error("操作运单各节点同步给云盘 error", e);
        }
    }

    /**
     * 将修改后的运单卸货地址同步给云仓
     *
     * @param model 修改地址后的运单信息
     */
    public void updateCarrierOrderUnloadAddressSynToWarehouse(UpdateCarrierOrderUnloadAddressSynWarehouseModel model) {
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("将修改后的运单卸货地址同步给云仓系统：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.WAREHOUSE_STOCK_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.CARRIER_ORDER_CHANG_UNLOAD_WAREHOUSE.getKey(), message);
        } catch (IOException e) {
            log.error("将修改后的运单卸货地址同步给云仓系统 error", e);
        }
    }

    /**
     * 将修改后的运单卸货地址同步给云盘
     *
     * @param model 修改地址后的运单信息
     */
    public void updateCarrierOrderUnloadAddressSynToLeYi(UpdateCarrierOrderUnloadAddressSynLeYiModel model) {
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("将修改后的运单卸货地址同步给云盘系统：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.ORDER_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.ORDER_UPDATE_CARRIERORDER_ADDRESS.getKey(), message);
        } catch (IOException e) {
            log.error("将修改后的运单卸货地址同步给云盘系统 error", e);
        }
    }


    /**
     * 将回退数同步给云盘
     *
     * @param model 云仓异常信息
     */
    public void syncBackAmountToLeYi(DemandBackCountMessage model) {
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("将回退数同步给云盘：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.ORDER_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.ORDER_DEMAND_BACK_COUNT.getKey(), message);
        } catch (IOException e) {
            log.error("将回退数同步给云盘 error", e);
        }
    }

    /**
     * 将出库单、回单同步给云盘
     */
    public void syncCarrierOrderOutStockTickets(List<UpdateCarrierOrderBillModel> model) {
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("新增/编辑运单票据同步给云盘：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.ORDER_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.UPDATE_CARRIER_ORDER_BILL.getKey(), message);
        } catch (IOException e) {
            log.error("新增/编辑运单票据同步给云盘 error", e);
        }
    }

    /**
     * 推送给新生货物
     *
     * @param model
     */
    public void syncRenewableConfirmGoods2YeloLife(LifePushSkuAndAddressInfoMessage model) {
        try {
            LifeSyncPackMessage<LifePushSkuAndAddressInfoMessage> lifeSyncPackMessage = new LifeSyncPackMessage<>();
            lifeSyncPackMessage.setType(CommonConstant.INTEGER_ONE);
            lifeSyncPackMessage.setMsgData(model);
            String message = JSON_WRITER.writeValueAsString(lifeSyncPackMessage);
            log.info("推送给新生货物：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.LIFE_ORDER_TOPIC.getKey(),
                    RabbitMqRoutingKeyTypeEnum.SYNC_LOGISTICS_LIFE_ORDER_INFO.getKey(),
                    message);
        } catch (IOException e) {
            log.error("推送给新生货物 error", e);
        }
    }

    /**
     * 生成需求单后，同步需求单与新生订单号关系给新生系统（新生订单已审核）
     *
     * @param model
     */
    public void syncLogisticsDemandOrderInfo2YeloLife(SyncLogisticsDemandOrderInfo model) {
        try {
            LifeSyncPackMessage<SyncLogisticsDemandOrderInfo> lifeSyncPackMessage = new LifeSyncPackMessage<>();
            lifeSyncPackMessage.setType(CommonConstant.INTEGER_TWO);
            lifeSyncPackMessage.setMsgData(model);
            String message = JSON_WRITER.writeValueAsString(lifeSyncPackMessage);
            log.info("推送给新生需求单号：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.LIFE_ORDER_TOPIC.getKey(),
                    RabbitMqRoutingKeyTypeEnum.SYNC_LOGISTICS_LIFE_ORDER_INFO.getKey(),
                    message);
        } catch (IOException e) {
            log.error("推送给新生需求单号 error", e);
        }
    }

    /**
     * 司机（无账号）下单生成运单，同步新生生成回收单与需求单关系、账单
     *
     * @param model
     */
    public void insertBillOrderLogisticsMessage2YeloLife(InsertBillOrderLogisticsMessage model) {
        try {
            LifeSyncPackMessage<InsertBillOrderLogisticsMessage> lifeSyncPackMessage = new LifeSyncPackMessage<>();
            lifeSyncPackMessage.setType(CommonConstant.INTEGER_ONE);
            lifeSyncPackMessage.setMsgData(model);
            String message = JSON_WRITER.writeValueAsString(lifeSyncPackMessage);
            log.info("司机（无账号）下单生成运单，同步新生生成回收单与需求单关系、账单：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.LIFE_FINANCIAL_TOPIC.getKey(),
                    RabbitMqRoutingKeyTypeEnum.SYNC_BILL_ORDER_TO_YELO_LIFE.getKey(),
                    message);
        } catch (IOException e) {
            log.error("司机（无账号）下单生成运单，同步新生生成回收单与需求单关系、账单 error", e);
        }
    }

    /**
     * 回收类型需求单回退，将回退信息同步给云盘
     * @param model
     */
    public void rollbackDemandOrderToLeYi(RollbackDemandOrderToLeYiModel model) {
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("回收类型需求单回退，将回退信息同步给云盘系统：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.ORDER_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.ROLLBACK_DEMAND_ORDER_TO_LEYI.getKey(), message);
        } catch (IOException e) {
            log.error("回收类型需求单回退，将回退信息同步给云盘系统 error", e);
        }
    }

//    /**
//     * 向微信公众号发送运单节点信息
//     * @param model
//     */
//    public void wxPush(SendTemplateMessageModel model){
//        try {
//            String message = JSON_WRITER.writeValueAsString(model);
//            log.info("向微信公众号发送运单节点信息：" + message);
//            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.LOGISTICS_CARRIER_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.SEND_WEIXIN_TEMPLATE_MESSAGE.getKey(), message);
//        } catch (IOException e) {
//            log.error("向微信公众号发送运单节点信息 error：", e);
//        }
//    }

    /**
     * 完成调度同步云仓
     * @param model
     * @param source 1 调度车辆自动完成调度，2 手动完成调度
     */
    public void completeDemandOrderSyn(List<LogisticsDemandStateSynchronizeModel> model, String source) {
        String requestSource = "";
        if (CommonConstant.ONE.equals(source)){
            requestSource = "调度车辆自动完成调度";
        }else if (CommonConstant.TWO.equals(source)){
            requestSource = "手动完成调度";
        }
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info(requestSource + "同步云仓系统：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.LOGISTICS_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.COMPLETE_DEMAND_ORDER.getKey(), message);
        } catch (IOException e) {
            log.error(requestSource + "同步云仓系统 error：", e);
        }
    }

    /**
     * 完成调度同步云盘
     * @param model
     * @param source 1 调度车辆自动完成调度，2 手动完成调度
     */
    public void completeDemandOrderSynToLeYi(DispatchCompleteModelMessage model, String source) {
        String requestSource = "";
        if (CommonConstant.ONE.equals(source)){
            requestSource = "调度车辆自动完成调度";
        }else if (CommonConstant.TWO.equals(source)){
            requestSource = "手动完成调度";
        }
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info(requestSource + "同步云盘系统：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.ORDER_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.DISPATCH_COMPLETE.getKey(), message);
        } catch (IOException e) {
            log.error(requestSource + "同步云盘系统 error：", e);
        }
    }

    /**
     * 取消运单同步云仓
     * @param model
     * @param source 1 物流系统取消，2 云盘云仓系统取消
     */
    public void cancelCarrierOrderSyn(CancelCarrierOrderChangeDemandAndCarrierStataSynchronizeModel model, String source) {
        String requestSource = "";
        if (CommonConstant.ONE.equals(source)){
            requestSource = "物流系统操作";
        }else if (CommonConstant.TWO.equals(source)){
            requestSource = "云盘云仓系统操作";
        }
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info(requestSource + "取消运单同步云仓系统：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.LOGISTICS_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.CANCEL_CARRIER_ORDER.getKey(), message);
        } catch (IOException e) {
            log.error(requestSource + "取消运单同步云仓系统 error：", e);
        }
    }

    /**
     * 取消运单同步云盘
     * @param model
     * @param source 1 物流系统取消，2 云盘云仓系统取消
     */
    public void cancelCarrierOrderSynToLeYi(CancelCarrierOrderChangeDemandAndCarrierStataSynchronizeModel model, String source) {
        String requestSource = "";
        if (CommonConstant.ONE.equals(source)){
            requestSource = "物流系统操作";
        }else if (CommonConstant.TWO.equals(source)){
            requestSource = "云盘云仓系统操作";
        }
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info(requestSource + "取消运单同步云盘系统：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.ORDER_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.ORDER_CANCEL_CARRIER_ORDER.getKey(), message);
        } catch (IOException e) {
            log.error(requestSource + "取消运单同步云盘系统 error：", e);
        }
    }

    /**
     * 生成需求单结算，同步云盘对账数据
     * @param model
     */
    public void createDemandSettleSynToLeYi(List<CreateEntrustSettlementForLeYiConsumerModel> model){
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("生成需求单结算，同步云盘对账数据：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.FINANCIAL_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.CREATE_LOGISTICS_DEMAND_SETTLE.getKey(), message);
        } catch (IOException e) {
            log.error("生成需求单结算，同步云盘对账数据 error：", e);
        }
    }

    /**
     * 取消需求单同步云仓
     * @param model
     */
    public void cancelDemandOrderSyn(CancelDemandByDemandOrderMessage model){
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("取消需求单同步云仓：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.LOGISTICS_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.CANCEL_DEMAND_ORDER.getKey(), message);
        } catch (IOException e) {
            log.error("取消需求单同步云仓 error：", e);
        }
    }

    /**
     * 取消需求单同步云盘
     * @param model
     */
    public void cancelDemandOrderSynToLeYi(CancelDemandByDemandRecoverOrderMessage model){
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("取消需求单同步云盘：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.ORDER_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.ORDER_CANCELDEMANDORDER.getKey(), message);
        } catch (IOException e) {
            log.error("取消需求单同步云盘 error：", e);
        }
    }

    /**
     * 向网络货运系统同步委托单信息
     * @param model
     */
    public void confirmSynNetworkFreight(List<SyncTMSDemandOrderModel> model){
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("向网络货运系统同步委托单信息：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.LOGISTICS_QIYA_TMS_TOP.getKey(), RabbitMqRoutingKeyTypeEnum.SYNC_DEMAND_ORDER.getKey(), message);
        } catch (IOException e) {
            log.error("向网络货运系统同步委托单信息 error：", e);
        }
    }

    /**
     * 同步网络货运追加、补单信息
     * @param model
     */
    public void syncAdditionalOrderToNetworkFreight(TmsSyncAdditionalOrderModel model){
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("同步网络货运追加、补单信息：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.LOGISTICS_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.SYNC_ADDITIONAL_ORDER.getKey(), message);
        } catch (IOException e) {
            log.error("同步网络货运追加、补单信息 error：", e);
        }
    }

    /**
     * 调度车辆生成运单同步给云仓
     * @param model
     */
    public void createCarrierOrderSyn(CopyCarrierOrderByDispatchVehicleMessage model){
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("调度车辆生成运单同步给云仓：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.LOGISTICS_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.COPY_CARRIER_ORDER.getKey(), message);
        } catch (IOException e) {
            log.error("调度车辆生成运单同步给云仓 error：", e);
        }
    }

    /**
     * 调度车辆生成运单同步给云盘
     * @param model
     */
    public void createCarrierOrderSynToLeYi(CopyCarrierRecycleOrderByDispatchVehicleMessage model){
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("调度车辆生成运单同步给云盘：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.ORDER_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.COPY_CARRIER_ORDER.getKey(), message);
        } catch (IOException e) {
            log.error("调度车辆生成运单同步给云盘 error：", e);
        }
    }

    /**
     * 需求单（HR单）签收后同步给地推
     * @param model
     */
    public void syncSignDemandOrderToGroundPush(SyncSignDemandOrderToGroundPushModel model){
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("需求单（HR单）签收后同步给地推：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.GROUND_PUSH_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.SYNC_SIGN_DEMAND_ORDER_TO_GROUND_PUSH.getKey(), message);
        } catch (IOException e) {
            log.error("需求单（HR单）签收后同步给地推 error：", e);
        }
    }

    /**
     * 更新t_user表绑定的企微id
     * @param model
     */
    public void syncUpdateWechatUserId(UpdateWechatUserIdMessage model){
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("更新t_user表绑定的企微id：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.BASIC_DATA_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.SYNC_UPDATE_WECHAT_USER_ID.getKey(), message);
        } catch (IOException e) {
            log.error("更新t_user表绑定的企微id error：", e);
        }
    }

    /**
     * 发送 socket 弹窗信息
     *
     * @param socketSendMessage
     */
    public void sendSocketMessage(WebSocketSendMessage socketSendMessage) {
        try {
            String message = JSON_WRITER.writeValueAsString(socketSendMessage);
            log.info("send socket message：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.BASIC_DATA_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.SEND_WEB_SOCKET_MESSAGE.getKey(), message);
        } catch (IOException e) {
            log.error("send socket json parse error：", e);
        }
    }

    /**
     * 需求单节点操作同步新生
     * @param model
     */
    public void syncDemandOrderToYeloLife(SyncDemandOrderToYeloLifeModel<Object> model) {
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("需求单节点操作同步新生：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.LIFE_ORDER_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.TMS_SYNC_LIFE_DEMAND_ORDER.getKey(), message);
        } catch (IOException e) {
            log.error("需求单节点操作同步新生 error：", e);
        }
    }

    /**
     * 运单节点操作同步新生
     * @param model
     */
    public void syncCarrierOrderToYeloLife(SyncCarrierOrderToYeloLifeModel<Object> model) {
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("运单节点操作同步新生：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.LIFE_ORDER_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.TMS_SYNC_LIFE_CARRIER_ORDER.getKey(), message);
        } catch (IOException e) {
            log.error("运单节点操作同步新生 error：", e);
        }
    }

    /**
     * 创建预约单同步云仓
     * @param model
     */
    public void syncReservationOrderCreateToWarehouse(SyncReservationOrderCreateToWarehouseModel model) {
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("创建预约单同步云仓：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.WAREHOUSE_STOCK_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.RESERVATION_ORDER_CREATE_TO_WAREHOUSE.getKey(), message);
        } catch (IOException e) {
            log.error("创建预约单同步云仓 error：", e);
        }
    }

    /**
     * 预约单失效同步云仓
     * @param model
     */
    public void syncReservationOrderInvalidToWarehouse(SyncReservationOrderInvalidToWarehouseModel model) {
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("预约单失效同步云仓：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.WAREHOUSE_STOCK_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.RESERVATION_ORDER_INVALID_TO_WAREHOUSE.getKey(), message);
        } catch (IOException e) {
            log.error("预约单失效同步云仓 error：", e);
        }
    }

    /**
     * 预约单下运单失效同步云仓
     * @param model
     */
    public void syncReservationCarrierOrderInvalidToWarehouse(SyncReservationCarrierOrderInvalidToWarehouseModel model) {
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("预约单下运单失效同步云仓：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.WAREHOUSE_STOCK_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.RESERVATION_CARRIER_ORDER_INVALID_TO_WAREHOUSE.getKey(), message);
        } catch (IOException e) {
            log.error("预约单下运单失效同步云仓 error：", e);
        }
    }

    /**
     * 预约单签到同步云仓
     * @param model
     */
    public void syncReservationOrderSignInToWarehouse(SyncReservationOrderSignInToWarehouseModel model) {
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("预约单签到同步云仓：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.WAREHOUSE_STOCK_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.RESERVATION_ORDER_SIGN_IN_TO_WAREHOUSE.getKey(), message);
        } catch (IOException e) {
            log.error("预约单签到同步云仓 error：", e);
        }
    }

    public void dealReplenishOrder(DelayMsg message) {
        try {
            String messageStr = JSON_WRITER.writeValueAsString(message);
            log.info("pushCodeVerification message: {}", messageStr);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.DELAY_QUEUE_TOPIC.getKey(),
                    RabbitMqRoutingKeyTypeEnum.DEAL_REPLENISH_ORDER.getKey(),
                    messageStr);
        } catch (IOException e) {
            log.info("pushCodeVerification message error", e);
        }
    }


//    /**
//     * 回收出同步云仓编码
//     * @param model
//     */
//    public void syncRecycleOutProductCodeToWarehouse(SyncRecycleOutProductCodeToWarehouseModel model) {
//        try {
//            String message = JSON_WRITER.writeValueAsString(model);
//            log.info("回收出编码同步云仓：" + message);
//            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.WAREHOUSE_STOCK_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.SYNC_RECYCLE_OUT_PRODUCT_CODE_TO_WAREHOUSE.getKey(), message);
//        } catch (IOException e) {
//            log.error("回收出编码同步云仓 error：", e);
//        }
//    }



    /**
     * 发送延时队列
     * @param model
     */
    public void dealReplenishOrder(CommonDelayMsg model) {
        try {
            String message = JSON_WRITER.writeValueAsString(model);
            log.info("发送延时队列：" + message);
            rabbitMqSender.sendMessage(RabbitMqExChangeTypeEnum.QUEUE_TOPIC.getKey(), RabbitMqRoutingKeyTypeEnum.DELAY_MESSAGE_CONSUMER.getKey(), message);
        } catch (IOException e) {
            log.error("发送延时队列 error：", e);
        }
    }



}
