<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.logistics</groupId>
    <artifactId>tms-appapi</artifactId>
    <version>3.13.0-RELEASE</version>

    <packaging>pom</packaging>
    <name>logistics-microservice-tms-appapi</name>
    <description>TMS司机小程序api服务</description>

    <modules>
        <module>tms-appapi-service</module>
        <module>tms-appapi-base</module>
    </modules>

    <parent>
        <groupId>com.yelo</groupId>
        <artifactId>yelo-starter-parent</artifactId>
        <version>********-RELEASE</version>
    </parent>

    <properties>
        <auth.service.version>2.0.5-RELEASE</auth.service.version>

        <projects.version>3.13.0-RELEASE</projects.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.leyi.tray</groupId>
                <artifactId>microService-auth-service-client</artifactId>
                <version>${auth.service.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>