package com.logistics.tms.mapper;
import com.logistics.tms.biz.carrierorderloadcode.model.CarrierOrderLoadCodeFileListSqlConditionModel;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.logistics.tms.entity.TCarrierOrderLoadCodeFile;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* Created by Mybatis Generator on 2025/01/03
*/
@Mapper
public interface TCarrierOrderLoadCodeFileMapper extends BaseMapper<TCarrierOrderLoadCodeFile> {

    int insertList(@Param("list")List<TCarrierOrderLoadCodeFile> list);


    List<TCarrierOrderLoadCodeFile> listByCarrierOrderLoadCodeByCondition(@Param("param") CarrierOrderLoadCodeFileListSqlConditionModel fileListSqlConditionModel);
}