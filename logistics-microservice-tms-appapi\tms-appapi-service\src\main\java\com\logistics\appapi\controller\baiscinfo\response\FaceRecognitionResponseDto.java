package com.logistics.appapi.controller.baiscinfo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/4
 */
@Data
public class FaceRecognitionResponseDto {

	@ApiModelProperty("随机字符串 用于查询人脸识别结果")
	private String orderNo;

	@ApiModelProperty("用于认证的url")
	private String url;

	@ApiModelProperty("流水号")
	private String sid;

	@ApiModelProperty("流程id")
	private String flowId;
}
