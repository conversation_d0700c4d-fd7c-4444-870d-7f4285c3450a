package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

public class ExportCarrierOrderOtherFee {

    private ExportCarrierOrderOtherFee() {
    }

    private static final Map<String, String> EXCEL_CARRIER_ORDER;

    static {
        EXCEL_CARRIER_ORDER = new LinkedHashMap<>();
        EXCEL_CARRIER_ORDER.put("审核状态", "auditStatusDesc");
        EXCEL_CARRIER_ORDER.put("运单号", "carrierOrderCode");
        EXCEL_CARRIER_ORDER.put("车主", "exportCompanyCarrierName");
        EXCEL_CARRIER_ORDER.put("司机", "driverForExport");
        EXCEL_CARRIER_ORDER.put("车牌号", "vehicleNo");
        EXCEL_CARRIER_ORDER.put("路线", "route");
        EXCEL_CARRIER_ORDER.put("合计费用", "totalAmount");
        EXCEL_CARRIER_ORDER.put("项目标签", "projectLabel");
        EXCEL_CARRIER_ORDER.put("新增人", "createdBy");
        EXCEL_CARRIER_ORDER.put("新增时间", "createdTime");
        EXCEL_CARRIER_ORDER.put("最新操作人", "lastModifiedBy");
        EXCEL_CARRIER_ORDER.put("最新操作时间", "lastModifiedTime");
    }

    public static Map<String, String> getExcelCarrierOrderOtherFee() {
        return EXCEL_CARRIER_ORDER;
    }
}
