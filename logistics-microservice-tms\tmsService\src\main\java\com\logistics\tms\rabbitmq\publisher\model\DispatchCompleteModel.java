package com.logistics.tms.rabbitmq.publisher.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author:lei.zhu
 * @date:2022/1/4 15:27:59
 */
@Data
public class DispatchCompleteModel {
    @ApiModelProperty("需求单号")
    private String demandCode;
    @ApiModelProperty("需求单状态：1000待调度 2000部分调度 3000调度完成")
    private Integer demandState;
    @ApiModelProperty("回退数量")
    private Integer completeBackAmount;
    @ApiModelProperty("调度时间")
    private Date dispatchTime;


    private List<TLogisticsDemandGoodsItemModel> tLogisticsDemandGoodsItemModels;

    @JsonIgnore
    private Long demandOrderId;
    @ApiModelProperty("差异数量")
    @JsonIgnore
    private BigDecimal differenceAmount;
    @JsonIgnore
    private Boolean ifSign=false;
}
