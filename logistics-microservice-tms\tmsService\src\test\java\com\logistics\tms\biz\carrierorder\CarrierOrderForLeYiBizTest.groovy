package com.logistics.tms.biz.carrierorder

import com.logistics.tms.controller.carrierorderapplet.request.CarrierOrderIdRequestModel
import com.logistics.tms.base.enums.CarrierOrderEventsTypeEnum
import com.logistics.tms.base.enums.CarrierOrderOperateLogsTypeEnum
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz
import com.logistics.tms.biz.carrierorder.model.GetTicketsAmountByCarrierOrderIdsModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.biz.demandorder.DemandOrderBiz
import com.logistics.tms.biz.extvehiclesettlement.ExtVehicleSettlementBiz
import com.logistics.tms.client.BasicDataClient
import com.logistics.tms.controller.carrierorder.request.*
import com.logistics.tms.controller.carrierorder.response.*
import com.logistics.tms.controller.demandorder.response.GetDemandOrderInfoByIdsModel
import com.logistics.tms.entity.*
import com.logistics.tms.mapper.*
import com.logistics.tms.rabbitmq.consumer.model.SyncAbnormalAmountMessage
import com.logistics.tms.rabbitmq.consumer.model.SyncStockInfoMessage
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class CarrierOrderForLeYiBizTest extends Specification {
    @Mock
    TCarrierOrderMapper tCarrierOrderMapper
    @Mock
    TCarrierOrderTicketsMapper tCarrierOrderTicketsMapper
    @Mock
    TCarrierOrderVehicleHistoryMapper tCarrierOrderVehicleHistoryMapper
    @Mock
    TDemandOrderMapper tDemandOrderMapper
    @Mock
    TCarrierOrderCorrectMapper tCarrierOrderCorrectMapper
    @Mock
    TCarrierOrderAddressMapper tCarrierOrderAddressMapper
    @Mock
    TCarrierOrderOperateLogsMapper tCarrierOrderOperateLogsMapper
    @Mock
    TCarrierOrderEventsMapper tCarrierOrderEventsMapper
    @Mock
    TCarrierOrderOrderRelMapper tCarrierOrderOrderRelMapper
    @Mock
    TCarrierOrderGoodsMapper tCarrierOrderGoodsMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TPaymentMapper tPaymentMapper
    @Mock
    TReceivementMapper tReceivementMapper
    @Mock
    ExtVehicleSettlementBiz extVehicleSettlementBiz
    @Mock
    DemandOrderBiz demandOrderBiz
    @Mock
    RabbitMqPublishBiz rabbitMqPublishBiz
    @Mock
    CarrierOrderCommonBiz carrierOrderCommonBiz
    @Mock
    BasicDataClient basicDataClient
    @Mock
    TDispatchOrderMapper tDispatchOrderMapper
    @Mock
    Logger log
    @InjectMocks
    CarrierOrderForLeYiBiz carrierOrderForLeYiBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Carrier Order List For Le Yi where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCarrierOrderMapper.searchCarrierOrderIdsForLeYiManagement(any())).thenReturn([1l])
        when(tCarrierOrderMapper.searchCarrierOrderForLeYiManagement(anyString())).thenReturn([new SearchCarrierOrderListForLeYiResponseModel()])
        when(tCarrierOrderTicketsMapper.getTicketsAmountByCarrierOrderIds(anyString())).thenReturn([new GetTicketsAmountByCarrierOrderIdsModel()])
        when(tCarrierOrderVehicleHistoryMapper.getValidByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderVehicleHistory(carrierOrderId: 1l, vehicleNo: "vehicleNo", driverName: "driverName", driverMobile: "driverMobile", expectLoadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime(), expectArrivalTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime())])
        when(tDemandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(settlementTonnage: 0, entrustType: 0, ifUrgent: 0)])
        when(tCarrierOrderCorrectMapper.getByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderCorrect(carrierOrderId: 1l, correctStatus: 0)])

        expect:
        carrierOrderForLeYiBiz.searchCarrierOrderListForLeYi(requestModel) == expectedResult

        where:
        requestModel                                    || expectedResult
        new SearchCarrierOrderListForLeYiRequestModel() || null
    }

    @Unroll
    def "carrier Order Detail For Le Yi where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCarrierOrderMapper.getCarrierOrderDetailForLeYiManagementById(anyLong())).thenReturn(new CarrierOrderDetailForLeYiResponseModel())
        when(tDemandOrderMapper.getDemandOrderInfoByIds(anyString())).thenReturn([new GetDemandOrderInfoByIdsModel()])
        when(tPaymentMapper.getByCarrierOrderId(anyLong())).thenReturn(new TPayment(priceType: 0, settlementCostTotal: 0 as BigDecimal))
        when(tReceivementMapper.getByCarrierOrderId(anyLong())).thenReturn(new TReceivement(priceType: 0, settlementCostTotal: 0 as BigDecimal))

        expect:
        carrierOrderForLeYiBiz.carrierOrderDetailForLeYi(requestModel) == expectedResult

        where:
        requestModel                         || expectedResult
        new CarrierOrderDetailRequestModel() || new CarrierOrderDetailForLeYiResponseModel()
    }

    @Unroll
    def "update Carrier Order Unload Address Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:


        expect:
        carrierOrderForLeYiBiz.updateCarrierOrderUnloadAddressDetail(requestModel) == expectedResult

        where:
        requestModel                         || expectedResult
        new CarrierOrderDetailRequestModel() || null
    }

    @Unroll
    def "update Carrier Order Unload Address Confirm where requestModel=#requestModel"() {
        given:

        expect:
        carrierOrderForLeYiBiz.updateCarrierOrderUnloadAddressConfirm(requestModel)
        assert expectedResult == false

        where:
        requestModel                                             || expectedResult
        new UpdateCarrierOrderUnloadAddressConfirmRequestModel() || true
    }

    @Unroll
    def "get Carrier Order Operate Logs where logTypeEnum=#logTypeEnum and carrierOrderId=#carrierOrderId and remark=#remark and userName=#userName then expect: #expectedResult"() {
        expect:
        carrierOrderForLeYiBiz.getCarrierOrderOperateLogs(carrierOrderId, logTypeEnum, userName, remark) == expectedResult

        where:
        logTypeEnum                                         | carrierOrderId | remark   | userName   || expectedResult
        CarrierOrderOperateLogsTypeEnum.CREATE_DEMAND_ORDER | 1l             | "remark" | "userName" || new TCarrierOrderOperateLogs(carrierOrderId: 1l, operationType: 0, operationContent: "operationContent", remark: "remark", operatorName: "operatorName", operateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime())
    }

    @Unroll
    def "get Carrier Order Event where eventTypeEnum=#eventTypeEnum and carrierOrderId=#carrierOrderId and remark=#remark and userName=#userName then expect: #expectedResult"() {
        expect:
        carrierOrderForLeYiBiz.getCarrierOrderEvent(carrierOrderId, eventTypeEnum, userName, remark) == expectedResult

        where:
        eventTypeEnum                       | carrierOrderId | remark   | userName   || expectedResult
        CarrierOrderEventsTypeEnum.BY_ORDER | 1l             | "remark" | "userName" || new TCarrierOrderEvents(carrierOrderId: 1l, event: 0, eventDesc: "eventDesc", remark: "remark", eventTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime(), operatorName: "operatorName", operateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime())
    }

    @Unroll
    def "update Carrier Order Address Lon And Lat where tCarrierOrderAddress=#tCarrierOrderAddress"() {
        given:
        when(basicDataClient.getLonLatByMapId(any())).thenReturn(null)

        expect:
        carrierOrderForLeYiBiz.updateCarrierOrderAddressLonAndLat(tCarrierOrderAddress)
        assert expectedResult == false

        where:
        tCarrierOrderAddress                                                                                                                                                                                                                                                       || expectedResult
        new TCarrierOrderAddress(unloadProvinceName: "unloadProvinceName", unloadCityName: "unloadCityName", unloadAreaId: 1l, unloadAreaName: "unloadAreaName", unloadDetailAddress: "unloadDetailAddress", unloadLongitude: "unloadLongitude", unloadLatitude: "unloadLatitude") || true
    }

    @Unroll
    def "carrier Order Correct Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCarrierOrderMapper.carrierOrderCorrectDetail(anyLong())).thenReturn(new CarrierOrderCorrectDetailResponseModel())
        when(tCarrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(anyLong(), anyInt())).thenReturn([new GetTicketsResponseModel()])

        expect:
        carrierOrderForLeYiBiz.carrierOrderCorrectDetail(requestModel) == expectedResult

        where:
        requestModel                                || expectedResult
        new CarrierOrderCorrectDetailRequestModel() || new CarrierOrderCorrectDetailResponseModel()
    }

    @Unroll
    def "carrier Order Correct Confirm where requestModel=#requestModel"() {
        given:
        when(tCarrierOrderMapper.getNotCancelByDemandOrderId(anyLong())).thenReturn([new TCarrierOrder(carrierOrderCode: "carrierOrderCode", status: 0, statusUpdateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime(), unloadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime(), signTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime(), expectAmount: 0 as BigDecimal, loadAmount: 0 as BigDecimal, unloadAmountExpect: 0 as BigDecimal, unloadAmount: 0 as BigDecimal, signAmount: 0 as BigDecimal, entrustFreightType: 0, entrustFreight: 0 as BigDecimal, signFreightFee: 0 as BigDecimal, dispatchFreightFeeType: 0, dispatchFreightFee: 0 as BigDecimal)])
        when(tCarrierOrderMapper.getCarrierOrderInfoForAuto(anyLong())).thenReturn(new CarrierOrderInfoForAutoModel())
        when(tCarrierOrderTicketsMapper.batchInsertTickets(any())).thenReturn(0)
        when(tCarrierOrderTicketsMapper.batchUpdate(any())).thenReturn(0)
        when(tCarrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(anyLong(), anyInt())).thenReturn([new GetTicketsResponseModel()])
        when(tCarrierOrderCorrectMapper.getByCarrierOrderId(anyLong())).thenReturn(new TCarrierOrderCorrect(carrierOrderId: 1l, correctStatus: 0, stockInCount: 0 as BigDecimal, correctType: 0, correctUser: "correctUser", correctTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime(), loadErrorAmount: 0 as BigDecimal, loseErrorAmount: 0 as BigDecimal))
        when(tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderOrderRel(orderId: 1l, orderCode: "orderCode", loadAmount: 0 as BigDecimal, unloadAmount: 0 as BigDecimal, signAmount: 0 as BigDecimal)])
        when(tCarrierOrderOrderRelMapper.batchUpdateSelective(any())).thenReturn(0)
        when(tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderGoods(goodsName: "goodsName", categoryName: "categoryName", length: 0, width: 0, height: 0, loadAmount: 0 as BigDecimal, unloadAmount: 0 as BigDecimal, signAmount: 0 as BigDecimal)])
        when(tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(any())).thenReturn(0)
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")

        expect:
        carrierOrderForLeYiBiz.carrierOrderCorrectConfirm(requestModel)
        assert expectedResult == false

        where:
        requestModel                                 || expectedResult
        new CarrierOrderCorrectConfirmRequestModel() || true
    }

    @Unroll
    def "update Sign Tickets For Correct where carrierOrderCode=#carrierOrderCode and requestList=#requestList and carrierOrderId=#carrierOrderId"() {
        given:
        when(tCarrierOrderTicketsMapper.batchInsertTickets(any())).thenReturn(0)
        when(tCarrierOrderTicketsMapper.batchUpdate(any())).thenReturn(0)
        when(tCarrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(anyLong(), anyInt())).thenReturn([new GetTicketsResponseModel()])
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")

        expect:
        carrierOrderForLeYiBiz.updateSignTicketsForCorrect(carrierOrderId, carrierOrderCode, requestList)
        assert expectedResult == false

        where:
        carrierOrderCode   | requestList | carrierOrderId || expectedResult
        "carrierOrderCode" | ["String"]  | 1l             || true
    }

    @Unroll
    def "correct Auto Sign where requestModel=#requestModel and now=#now and tCarrierOrderCorrect=#tCarrierOrderCorrect and userName=#userName and ifAutoCorrect=#ifAutoCorrect"() {
        given:
        when(tCarrierOrderMapper.getNotCancelByDemandOrderId(anyLong())).thenReturn([new TCarrierOrder(carrierOrderCode: "carrierOrderCode", status: 0, statusUpdateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime(), unloadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime(), signTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime(), loadAmount: 0 as BigDecimal, unloadAmountExpect: 0 as BigDecimal, unloadAmount: 0 as BigDecimal, signAmount: 0 as BigDecimal, entrustFreightType: 0, entrustFreight: 0 as BigDecimal, signFreightFee: 0 as BigDecimal, dispatchFreightFeeType: 0, dispatchFreightFee: 0 as BigDecimal)])
        when(tCarrierOrderMapper.getCarrierOrderInfoForAuto(anyLong())).thenReturn(new CarrierOrderInfoForAutoModel())
        when(tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderOrderRel(orderId: 1l, orderCode: "orderCode", loadAmount: 0 as BigDecimal, unloadAmount: 0 as BigDecimal, signAmount: 0 as BigDecimal)])
        when(tCarrierOrderOrderRelMapper.batchUpdateSelective(any())).thenReturn(0)
        when(tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderGoods(goodsName: "goodsName", categoryName: "categoryName", length: 0, width: 0, height: 0, loadAmount: 0 as BigDecimal, unloadAmount: 0 as BigDecimal, signAmount: 0 as BigDecimal)])
        when(tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(any())).thenReturn(0)

        expect:
        carrierOrderForLeYiBiz.correctAutoSign(tCarrierOrderCorrect, now, requestModel, userName, ifAutoCorrect)
        assert expectedResult == false

        where:
        requestModel                                 | now                                                             | tCarrierOrderCorrect                                                        | userName   | ifAutoCorrect || expectedResult
        new CarrierOrderCorrectConfirmRequestModel() | new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime() | new TCarrierOrderCorrect(carrierOrderId: 1l, stockInCount: 0 as BigDecimal) | "userName" | true          || true
    }

    @Unroll
    def "sync Stock Info From Leyi Stock In where message=#message"() {
        given:
        when(tCarrierOrderMapper.getNotCancelByDemandOrderId(anyLong())).thenReturn([new TCarrierOrder(demandOrderId: 1l, carrierOrderCode: "carrierOrderCode", status: 0, statusUpdateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime(), ifCancel: 0, unloadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime(), signTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime(), loadAmount: 0 as BigDecimal, unloadAmountExpect: 0 as BigDecimal, unloadAmount: 0 as BigDecimal, signAmount: 0 as BigDecimal, entrustFreightType: 0, entrustFreight: 0 as BigDecimal, signFreightFee: 0 as BigDecimal, dispatchFreightFeeType: 0, dispatchFreightFee: 0 as BigDecimal, ifEmpty: 0)])
        when(tCarrierOrderMapper.getByCode(anyString())).thenReturn(new TCarrierOrder(demandOrderId: 1l, carrierOrderCode: "carrierOrderCode", status: 0, statusUpdateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime(), ifCancel: 0, unloadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime(), signTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 7).getTime(), loadAmount: 0 as BigDecimal, unloadAmountExpect: 0 as BigDecimal, unloadAmount: 0 as BigDecimal, signAmount: 0 as BigDecimal, entrustFreightType: 0, entrustFreight: 0 as BigDecimal, signFreightFee: 0 as BigDecimal, dispatchFreightFeeType: 0, dispatchFreightFee: 0 as BigDecimal, ifEmpty: 0))
        when(tCarrierOrderMapper.getCarrierOrderInfoForAuto(anyLong())).thenReturn(new CarrierOrderInfoForAutoModel())
        when(tCarrierOrderTicketsMapper.batchInsertTickets(any())).thenReturn(0)
        when(tCarrierOrderCorrectMapper.getByCarrierOrderId(anyLong())).thenReturn(new TCarrierOrderCorrect(carrierOrderId: 1l, correctStatus: 0, stockInState: 0, stockInCount: 0 as BigDecimal, stockInRemark: "stockInRemark"))
        when(tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderOrderRel(orderId: 1l, orderCode: "orderCode", loadAmount: 0 as BigDecimal, unloadAmount: 0 as BigDecimal, signAmount: 0 as BigDecimal)])
        when(tCarrierOrderOrderRelMapper.batchUpdateSelective(any())).thenReturn(0)
        when(tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderGoods(goodsName: "goodsName", categoryName: "categoryName", length: 0, width: 0, height: 0, loadAmount: 0 as BigDecimal, unloadAmount: 0 as BigDecimal, signAmount: 0 as BigDecimal)])
        when(tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(any())).thenReturn(0)

        expect:
        carrierOrderForLeYiBiz.syncStockInfoFromLeyiStockIn(message)
        assert expectedResult == false

        where:
        message                    || expectedResult
        new SyncStockInfoMessage() || true
    }

    @Unroll
    def "get Wait Audit Vehicle Count Info For Le Yi where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCarrierOrderMapper.searchCarrierOrderIdsForLeYiManagement(any())).thenReturn([1l])

        expect:
        carrierOrderForLeYiBiz.getWaitAuditVehicleCountInfoForLeYi(requestModel) == expectedResult

        where:
        requestModel                           || expectedResult
        new WaitAuditVehicleInfoRequestModel() || new WaitAuditVehicleInfoResponseModel()
    }

    @Unroll
    def "carrier Order Empty Detail where requestModel=#requestModel then expect: #expectedResult"() {
        expect:
        carrierOrderForLeYiBiz.carrierOrderEmptyDetail(requestModel) == expectedResult

        where:
        requestModel                         || expectedResult
        new CarrierOrderDetailRequestModel() || new CarrierOrderEmptyDetailResponseModel()
    }

    @Unroll
    def "confirm Empty where requestModel=#requestModel"() {
        given:
        when(tCarrierOrderMapper.selectCarrierOrderSignDetail(anyString(), anyString())).thenReturn([new CarrierOrderListBeforeSignUpResponseModel()])
        when(tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderOrderRel(demandOrderOrderId: 1l, orderId: 1l, orderCode: "orderCode", expectAmount: 0 as BigDecimal)])

        expect:
        carrierOrderForLeYiBiz.confirmEmpty(requestModel)
        assert expectedResult == false

        where:
        requestModel                        || expectedResult
        new CarrierOrderEmptyRequestModel() || true
    }

    @Unroll
    def "logistics Cost Statistics where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCarrierOrderMapper.logisticsCostStatistics(anyString(), anyString())).thenReturn([new LogisticsCostStatisticsModel()])

        expect:
        carrierOrderForLeYiBiz.logisticsCostStatistics(requestModel) == expectedResult

        where:
        requestModel                              || expectedResult
        new LogisticsCostStatisticsRequestModel() || [new LogisticsCostStatisticsResponseModel()]
    }

    @Unroll
    def "logistics Load Validity Statistics where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCarrierOrderMapper.logisticsLoadValidityStatistics(anyString(), anyString())).thenReturn([new LogisticsLoadValidityStatisticsModel()])

        expect:
        carrierOrderForLeYiBiz.logisticsLoadValidityStatistics(requestModel) == expectedResult

        where:
        requestModel                                      || expectedResult
        new LogisticsLoadValidityStatisticsRequestModel() || [new LogisticsLoadValidityStatisticsResponseModel()]
    }

    @Unroll
    def "sync Abnormal Amount where abnormalAmountMessage=#abnormalAmountMessage"() {
        given:
        when(tCarrierOrderMapper.getByCode(anyString())).thenReturn(new TCarrierOrder(demandOrderId: 1l, demandOrderCode: "demandOrderCode", carrierOrderCode: "carrierOrderCode", status: 0, abnormalAmount: 0 as BigDecimal))

        expect:
        carrierOrderForLeYiBiz.syncAbnormalAmount(abnormalAmountMessage)
        assert expectedResult == false

        where:
        abnormalAmountMessage           || expectedResult
        new SyncAbnormalAmountMessage() || true
    }

    @Unroll
    def "get Carrier Order Orders where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCarrierOrderOrderRelMapper.getCarrierOrderOrdersByCarrierCode(anyLong())).thenReturn([new CarrierOrderOrdersResponseModel()])

        expect:
        carrierOrderForLeYiBiz.getCarrierOrderOrders(requestModel) == expectedResult

        where:
        requestModel                     || expectedResult
        new CarrierOrderIdRequestModel() || [new CarrierOrderOrdersResponseModel()]
    }

    @Unroll
    def "wait Correct Statistics"() {
        given:
        when(tCarrierOrderMapper.waitCorrectStatistics()).thenReturn([new WaitCorrectStatisticsModel()])
        when(tCarrierOrderMapper.waitLoadStatistics()).thenReturn([new WaitLoadStatisticsModel()])
        when(tCarrierOrderAddressMapper.waitCorrectStatisticsAddress(any())).thenReturn([1l])
        when(tCarrierOrderAddressMapper.getByIds(anyString())).thenReturn([new TCarrierOrderAddress(loadCityId: 1l, loadRegionContactName: "loadRegionContactName")])

        expect:
        carrierOrderForLeYiBiz.waitCorrectStatistics() == expectedResult

        where:
        expectedResult << new WaitCorrectStatisticsResponseModel()
    }

    @Unroll
    def "syn Complete Warehouse Status where model=#model"() {
        given:
        when(tCarrierOrderMapper.batchUpdateCarrierOrders(any())).thenReturn(0)
        when(tCarrierOrderMapper.getCarrierOrderByCodes(any())).thenReturn([new TCarrierOrder(carrierOrderCode: "carrierOrderCode", outStatus: 0)])

        expect:
        carrierOrderForLeYiBiz.synCompleteWarehouseStatus(model)
        assert expectedResult == false

        where:
        model                                                                  || expectedResult
        new com.logistics.tms.rabbitmq.consumer.model.CompleteWarehouseModel() || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme