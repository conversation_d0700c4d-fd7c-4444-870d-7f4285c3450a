package com.logistics.appapi.controller.attendance.response;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 考勤打卡历史响应dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/14
 */
@Data
public class AttendanceHistoryListResponseDto {

	@ApiModelProperty("打卡次数")
	private String clockInCount = "0";

	@ApiModelProperty("工时")
	private String manHourSum = "0";

	@ApiModelProperty("考勤打卡历史")
	private PageInfo<AttendanceHistoryItemDto> attendanceHistoryItem;
}
