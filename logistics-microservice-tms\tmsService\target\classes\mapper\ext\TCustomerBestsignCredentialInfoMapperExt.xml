<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCustomerBestsignCredentialInfoMapper">

<insert id="batchInsertSelective">
    <foreach collection="list" item="item" separator=";">
        insert into t_customer_bestsign_credential_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="item.id != null">
                id,
            </if>
            <if test="item.realNameAuthId != null">
                real_name_auth_id,
            </if>
            <if test="item.bestsignAccount != null">
                bestsign_account,
            </if>
            <if test="item.certId != null">
                cert_id,
            </if>
            <if test="item.serialNumber != null">
                serial_number,
            </if>
            <if test="item.subjectDn != null">
                subject_DN,
            </if>
            <if test="item.issuerDn != null">
                issuer_DN,
            </if>
            <if test="item.startTime != null">
                start_time,
            </if>
            <if test="item.stopTime != null">
                stop_time,
            </if>
            <if test="item.revokedTime != null">
                revoked_time,
            </if>
            <if test="item.revokedReason != null">
                revoked_reason,
            </if>
            <if test="item.status != null">
                status,
            </if>
            <if test="item.remark != null">
                remark,
            </if>
            <if test="item.createdBy != null">
                created_by,
            </if>
            <if test="item.createdTime != null">
                created_time,
            </if>
            <if test="item.lastModifiedBy != null">
                last_modified_by,
            </if>
            <if test="item.lastModifiedTime != null">
                last_modified_time,
            </if>
            <if test="item.valid != null">
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="item.id != null">
                #{item.id,jdbcType=BIGINT},
            </if>
            <if test="item.realNameAuthId != null">
                #{item.realNameAuthId,jdbcType=BIGINT},
            </if>
            <if test="item.bestsignAccount != null">
                #{item.bestsignAccount,jdbcType=VARCHAR},
            </if>
            <if test="item.certId != null">
                #{item.certId,jdbcType=VARCHAR},
            </if>
            <if test="item.serialNumber != null">
                #{item.serialNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.subjectDn != null">
                #{item.subjectDn,jdbcType=VARCHAR},
            </if>
            <if test="item.issuerDn != null">
                #{item.issuerDn,jdbcType=VARCHAR},
            </if>
            <if test="item.startTime != null">
                #{item.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.stopTime != null">
                #{item.stopTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.revokedTime != null">
                #{item.revokedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.revokedReason != null">
                #{item.revokedReason,jdbcType=VARCHAR},
            </if>
            <if test="item.status != null">
                #{item.status,jdbcType=INTEGER},
            </if>
            <if test="item.remark != null">
                #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.createdBy != null">
                #{item.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="item.createdTime != null">
                #{item.createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastModifiedBy != null">
                #{item.lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="item.lastModifiedTime != null">
                #{item.lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.valid != null">
                #{item.valid,jdbcType=INTEGER},
            </if>
        </trim>
    </foreach>
</insert>
</mapper>