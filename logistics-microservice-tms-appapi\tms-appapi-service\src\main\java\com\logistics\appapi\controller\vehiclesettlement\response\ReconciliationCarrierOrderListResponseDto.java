package com.logistics.appapi.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author：wjf
 * @date：2021/4/9 15:52
 */
@Data
public class ReconciliationCarrierOrderListResponseDto {
    @ApiModelProperty("运单id")
    private String carrierOrderId="";
    @ApiModelProperty("运单号")
    private String carrierOrderCode="";
    @ApiModelProperty("客户单号")
    private String customerOrderCode="";
    @ApiModelProperty("提货地")
    private String loadAddress="";
    @ApiModelProperty("卸货地")
    private String unloadAddress="";
    @ApiModelProperty("数量（带单位）")
    private String amount="";
    @ApiModelProperty("运费")
    private String dispatchFreightFee="";
}
