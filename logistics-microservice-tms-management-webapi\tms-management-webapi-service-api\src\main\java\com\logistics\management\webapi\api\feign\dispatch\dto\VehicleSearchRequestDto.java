package com.logistics.management.webapi.api.feign.dispatch.dto;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class VehicleSearchRequestDto  extends AbstractPageForm<VehicleSearchRequestDto> {

    @ApiModelProperty("车辆机构 1 自主，2 外部，3 自营")
    private String vehicleProperty;

    @ApiModelProperty(value = "车主ID",required = true)
    @NotBlank(message = "车主id不能为空")
    private String companyCarrierId;

    @ApiModelProperty("是否我司: 1:我司 2:其他车主")
    private String isOurCompany;

    @ApiModelProperty("车牌号")
    private String vehicleNo;
}
