package com.logistics.management.webapi.controller.workgroup.mapping;

import com.logistics.management.webapi.base.enums.EnabledEnum;
import com.logistics.management.webapi.client.workgroup.response.SearchWorkGroupListResponseModel;
import com.logistics.management.webapi.controller.workgroup.response.SearchWorkGroupListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2023/12/25 10:40
 */
public class WorkGroupSearchListMapping extends MapperMapping<SearchWorkGroupListResponseModel, SearchWorkGroupListResponseDto> {
    @Override
    public void configure() {
        SearchWorkGroupListResponseModel source = getSource();
        SearchWorkGroupListResponseDto destination = getDestination();

        destination.setEnabledLabel(EnabledEnum.getEnum(source.getEnabled()).getValue());
        destination.setGroupOwnerUser(source.getGroupOwnerUsername());
    }
}
