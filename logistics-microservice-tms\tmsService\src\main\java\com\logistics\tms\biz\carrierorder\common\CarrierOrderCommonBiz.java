package com.logistics.tms.biz.carrierorder.common;

import cn.hutool.core.collection.CollectionUtil;
import com.logistics.tms.controller.carrierorder.request.LoadGoodsForYeloLifeRequestCodeModel;
import com.logistics.tms.controller.carrierorder.response.CarrierOrderGoodsResponseModel;
import com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel;
import com.logistics.tms.controller.carrierorder.response.TicketsModel;
import com.logistics.tms.api.feign.entrustsettlement.model.ModifyCarrierSettlementCostModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.LocalStringUtil;
import com.logistics.tms.biz.carrierfreight.CarrierFreightConfigLadderBiz;
import com.logistics.tms.biz.carrierfreight.model.AddressAreaExistRequestModel;
import com.logistics.tms.biz.carrierfreight.model.AddressConfigLadderModel;
import com.logistics.tms.biz.carrierfreight.model.AddressConfigModel;
import com.logistics.tms.biz.carrierfreight.model.CarrierFreightCarrierModel;
import com.logistics.tms.biz.carrierorder.model.GetCarrierPriceModel;
import com.logistics.tms.biz.carrierorder.model.GetSettlementByDemandOrderIdModel;
import com.logistics.tms.biz.carrierorder.model.MatchCarrierPriceResultModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.staff.model.CarrierDriverRelationModel;
import com.logistics.tms.biz.workordercenter.WorkOrderBiz;
import com.logistics.tms.client.BasicDataClient;
import com.logistics.tms.controller.carrierorder.request.GetCarrierOrderNodeAmountRequestModel;
import com.logistics.tms.controller.carrierorder.request.GetCarrierOrdersByDemandIdRequestModel;
import com.logistics.tms.controller.carrierorder.response.*;
import com.logistics.tms.controller.demandorder.response.CreateSettlementForCarrierConsumerModel;
import com.logistics.tms.controller.freightconfig.response.ladder.CarrierFreightConfigLadderResponseModel;
import com.logistics.tms.controller.freightconfig.response.ladder.CarrierFreightConfigPriceDesignResponseModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.yelo.basicdata.api.feign.datamap.model.GetLonLatByMapIdRequestModel;
import com.yelo.basicdata.api.feign.datamap.model.GetLonLatByMapIdResponseModel;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CarrierOrderCommonBiz {
    @Autowired
    private TCarrierOrderVehicleHistoryMapper tCarrierOrderVehicleHistoryMapper;
    @Autowired
    private TCarrierOrderGoodsMapper tCarrierOrderGoodsMapper;
    @Autowired
    private TCarrierOrderWxMapper tCarrierOrderWxMapper;
    @Autowired
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Autowired
    private TPaymentMapper paymentMapper;
    @Autowired
    private TReceivementMapper receivementMapper;
    @Autowired
    private TCarrierOrderOperateLogsMapper carrierOrderOperateLogsMapper;
    @Autowired
    private TVehicleSettlementRelationMapper tVehicleSettlementRelationMapper;
    @Autowired
    private TCarrierOrderTicketsMapper tCarrierOrderTicketsMapper;
    @Autowired
    private TCarrierOrderAddressMapper tCarrierOrderAddressMapper;
    @Autowired
    private TCarrierFreightConfigAddressMapper tCarrierFreightConfigAddressMapper;
    @Autowired
    private TCarrierFreightConfigMapper tCarrierFreightConfigMapper;
    @Autowired
    private TRouteDistanceConfigMapper tRouteDistanceConfigMapper;
    @Autowired
    private TCarrierFreightConfigSchemeMapper tCarrierFreightConfigSchemeMapper;
    @Autowired
    private TDemandOrderMapper tDemandOrderMapper;
    @Autowired
    private TTrayCostMapper tTrayCostMapper;
    @Autowired
    private BasicDataClient basicDataClient;
    @Autowired
    private SqlSessionFactory sqlSessionFactory;
    @Autowired
    private WorkOrderBiz workOrderBiz;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private CarrierFreightConfigLadderBiz carrierFreightConfigLadderBiz;
    @Autowired
    private RabbitMqPublishBiz rabbitMqPublishBiz;
    @Autowired
    private TCarrierOrderGoodsCodeMapper tCarrierOrderGoodsCodeMapper;

    /**
     * 判断运单是否存在账单数据
     *
     * @param carrierOrderIds
     * @return true 是 false 否
     */
    public boolean existSettlementData(String carrierOrderIds) {
        List<TVehicleSettlementRelation> relList = tVehicleSettlementRelationMapper.getByObjectIdsAndType(VehicleSettlementTypeEnum.CARRIER_ORDER.getKey(), carrierOrderIds);
        return ListUtils.isNotEmpty(relList);
    }

    //判断车辆是否处于未审核状态
    public void checkIfCarrierOrdersStatusCanChange(List<Long> carrierOrderIdList,CarrierDataExceptionEnum exceptionEnum){
        if (ListUtils.isNotEmpty(carrierOrderIdList)) {
            List<String> carrierOrderCode = tCarrierOrderVehicleHistoryMapper.selectWaitAuditCarrierOrders(carrierOrderIdList);
            StringBuilder sb = new StringBuilder();
            for (String tmp : carrierOrderCode) {
                sb.append(tmp + ",");
            }
            if (sb.length() > 0) {
                throw new BizException(exceptionEnum.getKey(), sb.deleteCharAt(sb.length() - 1).toString() + exceptionEnum.getValue());
            }
        }
    }

    /**
     * 前台请求校验运单是否当前车主的
     *
     * @param carrierOrderCarrierId 运单上的车主ID
     */
    public void checkCarrierOrderCompanyCarrierForWeb(Long carrierOrderCarrierId) {
        //获取当前登陆人公司id
        Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }

        if (!loginUserCompanyCarrierId.equals(carrierOrderCarrierId)) {
            //判断当前运单是否当前车主的
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
    }

    /**
     * 小程序-校验运单上司机信息与登录人信息是否一致
     *
     * @param carrierOrderId
     */
    public void checkDriver4Node(Long carrierOrderId, Long companyCarrierId) {
        //校验运单上司机信息与登录人信息是否一致
        CarrierDriverRelationModel carrierDriverRelationModel = checkDriver(carrierOrderId, companyCarrierId);
        //司机账号被禁，没有操作运单的权限（只有查看权限）
        if(CommonConstant.INTEGER_ZERO.equals(carrierDriverRelationModel.getEnabled())){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_DRIVER_DISABLED);
        }
    }

    /**
     * 小程序-校验运单上司机信息与登录人信息是否一致
     *
     * @param carrierOrderId
     */
    public CarrierDriverRelationModel checkDriver(Long carrierOrderId, Long companyCarrierId) {
        //查询登录司机与车主关系信息
        List<CarrierDriverRelationModel> relationModelList = commonBiz.getLoginUserDriver();
        if (ListUtils.isEmpty(relationModelList)){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_NOT_EXIST);
        }

        //查询运单上司机信息
        TCarrierOrderVehicleHistory carrierOrderVehicleHistory = tCarrierOrderVehicleHistoryMapper.getValidTopByCarrierOrderId(carrierOrderId);
        if (carrierOrderVehicleHistory == null){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        if (IfValidEnum.INVALID.getKey().equals(carrierOrderVehicleHistory.getIfInvalid())
                && AuditStatusEnum.WAIT_AUDIT.getKey().equals(carrierOrderVehicleHistory.getAuditStatus())){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_VEHICLE_WAIT_AUDIT_APP);
        }

        //匹配运单上的车主与登录司机关联的车主是否有匹配的
        CarrierDriverRelationModel carrierDriverRelationModel = relationModelList.stream().filter(item -> item.getCompanyCarrierId().equals(companyCarrierId) && carrierOrderVehicleHistory.getDriverId().equals(item.getDriverId())).findFirst().orElse(null);
        //没有，则说明此运单不属于该司机
        if (carrierDriverRelationModel == null){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        return carrierDriverRelationModel;
    }

    public void wxPush(TCarrierOrder orderParam) {
        if (orderParam == null) {
            return;
        }
        TCarrierOrder order = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(orderParam.getId());
        String unit = GoodsUnitEnum.getEnum(order.getGoodsUnit()).getUnit();
        String carrierOrderStatus = "";
        if (CarrierOrderStatusEnum.WAIT_LOAD.getKey().equals(order.getStatus())) {
            carrierOrderStatus = "司机已到达提货地，请知晓；";
        } else if (CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey().equals(order.getStatus())) {
            carrierOrderStatus = "司机已提" + order.getLoadAmount().stripTrailingZeros().toPlainString() + unit + ",开始出发送货，请知晓；";
        } else if (CarrierOrderStatusEnum.WAIT_UNLOAD.getKey().equals(order.getStatus())) {
            carrierOrderStatus = "司机已到达卸货地，请知晓；";
        } else if (CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey().equals(order.getStatus())) {
            carrierOrderStatus = "司机已卸" + order.getUnloadAmount().stripTrailingZeros().toPlainString() + unit+"，请知晓；";
        } else {
            return;
        }
        List<TCarrierOrderGoods> tCarrierOrderGoodsList = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(ConverterUtils.toString(order.getId()));
        StringBuilder goodsInfo = new StringBuilder();
        if(ListUtils.isNotEmpty(tCarrierOrderGoodsList)){
            TCarrierOrderGoods firstGoods = tCarrierOrderGoodsList.get(0);
            goodsInfo.append(firstGoods.getGoodsName()).append(" ").append(firstGoods.getGoodsSize());
            if(GoodsUnitEnum.BY_VOLUME.getKey().equals(order.getGoodsUnit())){
                goodsInfo.append(" ").append(tCarrierOrderGoodsList.get(0).getLength() + "*" + tCarrierOrderGoodsList.get(0).getWidth() + "*" + tCarrierOrderGoodsList.get(0).getHeight()).append("mm");
            }
            if (tCarrierOrderGoodsList.size() > 1) {
                goodsInfo.append(" 等");
            }
        }
        List<String> mobileList = new ArrayList<>();
        List<GetCarrierOrderWeixinPushResponseModel> getCarrierOrderWeixinPushResponseModels = tCarrierOrderWxMapper.selectCarrierOrderWeixinPushInfoByCarrierOrder(order.getId());
        if(ListUtils.isNotEmpty(getCarrierOrderWeixinPushResponseModels)){
            for(GetCarrierOrderWeixinPushResponseModel getCarrierOrderWeixinPushResponseModel : getCarrierOrderWeixinPushResponseModels){
                if(CommonConstant.INTEGER_ONE.equals(getCarrierOrderWeixinPushResponseModel.getIfPush())){
                    mobileList.add(getCarrierOrderWeixinPushResponseModel.getMobile());
                }
            }
        }
        if(ListUtils.isEmpty(mobileList)){
            return ;
        }
        SendTemplateMessageModel sendTemplateMessageModel = new SendTemplateMessageModel();
        sendTemplateMessageModel.setMobile(mobileList.toArray(new String[0]));
        List<TemplateMessageDataModel> templateMessageDataModels = new ArrayList<>();
        TemplateMessageDataModel templateMessageDataModel = new TemplateMessageDataModel();
        templateMessageDataModel.setValue("乐橘云途提醒您");
        templateMessageDataModel.setColor("#000000");
        templateMessageDataModels.add(templateMessageDataModel);

        templateMessageDataModel = new TemplateMessageDataModel();
        templateMessageDataModel.setValue(order.getCarrierOrderCode());
        templateMessageDataModels.add(templateMessageDataModel);

        templateMessageDataModel = new TemplateMessageDataModel();
        templateMessageDataModel.setValue(goodsInfo.toString());
        templateMessageDataModels.add(templateMessageDataModel);

        templateMessageDataModel = new TemplateMessageDataModel();
        templateMessageDataModel.setValue(carrierOrderStatus);
        templateMessageDataModels.add(templateMessageDataModel);

        templateMessageDataModel = new TemplateMessageDataModel();
        templateMessageDataModel.setValue("感谢您对乐橘云途一如既往的支持与信任！");
        templateMessageDataModel.setColor("#000000");
        templateMessageDataModels.add(templateMessageDataModel);
        sendTemplateMessageModel.setData(templateMessageDataModels.toArray(new TemplateMessageDataModel[0]));
        // 网络货运下线
//        rabbitMqPublishBiz.wxPush(sendTemplateMessageModel);
    }


    public void batchWxPush(List<TCarrierOrder> tCarrierOrders){
        if(ListUtils.isNotEmpty(tCarrierOrders)){
            for(TCarrierOrder tmp:tCarrierOrders){
                try {
                    wxPush(tmp);
                } catch (Exception e) {
                    log.warn(e.getMessage());
                }
            }
        }
    }

    public void singleWxPush(TCarrierOrder tCarrierOrder) {
        if (null != tCarrierOrder) {
            try {
                wxPush(tCarrierOrder);
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
    }

    /**
     * 校验项：
     * @param carrierOrderId
     * @return
     */
    public List<TCarrierOrder> checkCarrierOrderIfExist(List<Long> carrierOrderId){
        String carrierOrderIds = StringUtils.listToString(carrierOrderId,',');
        List<TCarrierOrder> orders = tCarrierOrderMapper.selectCarrierOrdersByIds(carrierOrderIds);
        if(orders.size()!=carrierOrderId.size()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        return orders;
    }

    //生成运单结算信息
    @Transactional
    public void createSettlement(List<CreateSettlementForCarrierConsumerModel> settlementList, String ifUpdateCarrierSettlement) {
        if (ListUtils.isNotEmpty(settlementList)) {
            List<TPayment> paymentList = new ArrayList<>();
            List<TReceivement> receivementList = new ArrayList<>();
            TPayment payment;
            TReceivement receivement;
            for (CreateSettlementForCarrierConsumerModel model : settlementList) {
                if (CommonConstant.ONE.equals(ifUpdateCarrierSettlement)) {
                    payment = new TPayment();
                    payment.setCarrierOrderId(model.getCarrierOrderId());
                    payment.setPriceType(model.getPayPriceType());
                    payment.setSettlementAmount(model.getPaySettlementAmount());
                    payment.setSettlementCostTotal(model.getPaySettlementCost());
                    commonBiz.setBaseEntityAdd(payment, model.getUserName());
                    paymentList.add(payment);
                }

                receivement = new TReceivement();
                receivement.setCarrierOrderId(model.getCarrierOrderId());
                receivement.setPriceType(model.getPriceType());
                receivement.setSettlementAmount(model.getSettlementAmount());
                receivement.setSettlementCostTotal(model.getSettlementCost());
                commonBiz.setBaseEntityAdd(receivement, model.getUserName());
                receivementList.add(receivement);
            }

            //新增
            if (ListUtils.isNotEmpty(paymentList)) {
                paymentMapper.batchInsert(paymentList);
            }
            if (ListUtils.isNotEmpty(receivementList)) {
                receivementMapper.batchInsert(receivementList);
            }
        }
    }


    //委托方结算费用修改同步修改运单结算费用
    @Transactional
    public void modifySettlementCost(ModifyCarrierSettlementCostModel model){
        //查询车主和货主结算信息、运单信息
        List<GetSettlementByDemandOrderIdModel> paymentList = paymentMapper.getSettlementByDemandOrderId(model.getDemandOrderId());
        List<GetSettlementByDemandOrderIdModel> receivementList = receivementMapper.getSettlementByDemandOrderId(model.getDemandOrderId());
        if (ListUtils.isEmpty(paymentList) || ListUtils.isEmpty(receivementList)){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_SETTLEMENT_INFO_EMPTY);
        }
        List<CarrierOrderListBeforeSignUpResponseModel> carrierOrderList = tCarrierOrderMapper.selectCarrierOrderSignDetail(null,model.getDemandOrderId().toString());
        if (ListUtils.isEmpty(carrierOrderList)){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //查询车主、货主结算数据（根据各自结算吨位计算）
        BigDecimal amount;
        BigDecimal volume;
        BigDecimal amountTotal = BigDecimal.ZERO;
        BigDecimal volumeTotal = BigDecimal.ZERO;
        BigDecimal carrierAmount;
        BigDecimal carrierVolume;
        BigDecimal carrierAmountTotal = BigDecimal.ZERO;
        BigDecimal carrierVolumeTotal = BigDecimal.ZERO;
        for (CarrierOrderListBeforeSignUpResponseModel carrier : carrierOrderList) {
            volume = BigDecimal.ZERO;
            carrierVolume = BigDecimal.ZERO;
            for (CarrierOrderListBeforeSignUpGoodsModel goods : carrier.getGoodsInfo()) {
                BigDecimal capacity = ConverterUtils.toBigDecimal(goods.getLength()).multiply(ConverterUtils.toBigDecimal(goods.getWidth())).multiply(ConverterUtils.toBigDecimal(goods.getHeight()));
                //根据货主结算吨位取货主结算数量
                if (SettlementTonnageEnum.LOAD.getKey().equals(model.getSettlementTonnage())) {
                    amount = goods.getLoadAmount();
                } else if (SettlementTonnageEnum.UNLOAD.getKey().equals(model.getSettlementTonnage())) {
                    amount = goods.getUnloadAmount();
                } else if (SettlementTonnageEnum.SIGN.getKey().equals(model.getSettlementTonnage())) {
                    amount = goods.getSignAmount();
                } else if (SettlementTonnageEnum.EXPECT.getKey().equals(model.getSettlementTonnage())) {
                    amount = goods.getExpectAmount();
                } else {
                    amount = BigDecimal.ZERO;
                }
                if (CommonConstant.INTEGER_ONE.equals(carrier.getIfEmpty())) {
                    //已放空，取预提
                    amount = goods.getExpectAmount();
                }
                volume = volume.add(amount.multiply(capacity).divide(new BigDecimal(1000).pow(3)).setScale(3, BigDecimal.ROUND_HALF_UP));
                amountTotal = amountTotal.add(amount);
                //根据车主结算吨位取车主结算数量
                if (SettlementTonnageEnum.LOAD.getKey().equals(model.getCarrierSettlementTonnage())) {
                    carrierAmount = goods.getLoadAmount();
                } else if (SettlementTonnageEnum.UNLOAD.getKey().equals(model.getCarrierSettlementTonnage())) {
                    carrierAmount = goods.getUnloadAmount();
                } else if (SettlementTonnageEnum.SIGN.getKey().equals(model.getCarrierSettlementTonnage())) {
                    carrierAmount = goods.getSignAmount();
                } else if (SettlementTonnageEnum.EXPECT.getKey().equals(model.getCarrierSettlementTonnage())) {
                    carrierAmount = goods.getExpectAmount();
                } else {
                    carrierAmount = BigDecimal.ZERO;
                }
                if (CommonConstant.INTEGER_ONE.equals(carrier.getIfEmpty())) {
                    carrierAmount = goods.getExpectAmount();
                }
                carrierVolume = carrierVolume.add(carrierAmount.multiply(capacity).divide(new BigDecimal(1000).pow(3)).setScale(3, BigDecimal.ROUND_HALF_UP));
                carrierAmountTotal = carrierAmountTotal.add(carrierAmount);
            }
            volumeTotal = volumeTotal.add(volume);
            carrierVolumeTotal = carrierVolumeTotal.add(carrierVolume);
        }
        //修改车主结算费用
        TPayment payment;
        List<TPayment> upPaymentList = new ArrayList<>();
        TCarrierOrderOperateLogs carrierOrderOperateLogs;
        List<TCarrierOrderOperateLogs> logsList = new ArrayList<>();
        Date now = new Date();
        BigDecimal paymentCostSum = BigDecimal.ZERO;
        int paymentIndex = 0;
        for (GetSettlementByDemandOrderIdModel settlement:paymentList) {
            payment = new TPayment();
            payment.setId(settlement.getSettlementId());
            payment.setPriceType(model.getContractPriceType());
            BigDecimal cost = BigDecimal.ZERO;
            if (carrierAmountTotal.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO){
                cost = settlement.getSettlementAmount().multiply(model.getSettlementCostTotal()).divide(carrierAmountTotal,2, BigDecimal.ROUND_HALF_UP);
            }
            if (paymentList.size() > 1){
                if (paymentIndex == paymentList.size() - 1){
                    cost = model.getSettlementCostTotal().subtract(paymentCostSum);
                }else{
                    paymentCostSum = paymentCostSum.add(cost);
                }
            }
            payment.setSettlementCostTotal(cost);
            commonBiz.setBaseEntityModify(payment, model.getUserName());
            upPaymentList.add(payment);

            paymentIndex++;

            String priceType;
            if (settlement.getPriceType() == null || settlement.getPriceType().equals(CommonConstant.INTEGER_ZERO)){
                priceType = "无";
            }else{
                priceType = FreightTypeEnum.getEnum(settlement.getPriceType()).getValue();
            }
            carrierOrderOperateLogs = new TCarrierOrderOperateLogs();
            carrierOrderOperateLogs.setCarrierOrderId(settlement.getCarrierOrderId());
            carrierOrderOperateLogs.setOperationType(CarrierOrderOperateLogsTypeEnum.CARRIER_MODIFY_COST.getKey());
            carrierOrderOperateLogs.setOperationContent(CarrierOrderOperateLogsTypeEnum.CARRIER_MODIFY_COST.getValue());
            carrierOrderOperateLogs.setRemark(CarrierOrderOperateLogsTypeEnum.CARRIER_MODIFY_COST.format(priceType,FreightTypeEnum.getEnum(model.getContractPriceType()).getValue(),settlement.getSettlementCostTotal().stripTrailingZeros().toPlainString(),cost.stripTrailingZeros().toPlainString()));
            carrierOrderOperateLogs.setOperatorName(model.getUserName());
            carrierOrderOperateLogs.setOperateTime(now);
            commonBiz.setBaseEntityAdd(carrierOrderOperateLogs,model.getUserName());
            logsList.add(carrierOrderOperateLogs);
        }
        //修改货主结算费用
        TReceivement receivement;
        List<TReceivement> upReceivementList = new ArrayList<>();
        BigDecimal reCostSum = BigDecimal.ZERO;
        int reIndex = 0;
        for (GetSettlementByDemandOrderIdModel settlement:receivementList) {
            receivement = new TReceivement();
            receivement.setId(settlement.getSettlementId());
            receivement.setPriceType(model.getContractPriceType());
            BigDecimal cost = BigDecimal.ZERO;
            if (amountTotal.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO){
                cost = settlement.getSettlementAmount().multiply(model.getSettlementCostTotal()).divide(amountTotal,2, BigDecimal.ROUND_HALF_UP);
            }
            if (receivementList.size() > 1){
                if (reIndex == receivementList.size() - 1){
                    cost = model.getSettlementCostTotal().subtract(reCostSum);
                }else{
                    reCostSum = reCostSum.add(cost);
                }
            }
            receivement.setSettlementCostTotal(cost);
            commonBiz.setBaseEntityModify(receivement,model.getUserName());
            upReceivementList.add(receivement);

            reIndex++;
        }

        if (ListUtils.isNotEmpty(upPaymentList)){
            paymentMapper.batchUpdate(upPaymentList);
        }
        if (ListUtils.isNotEmpty(upReceivementList)){
            receivementMapper.batchUpdate(upReceivementList);
        }
        if (ListUtils.isNotEmpty(logsList)){
            carrierOrderOperateLogsMapper.batchInsertSelective(logsList);
        }
    }


    /**
     * 根据需求单id查询运单-需求单详情
     *
     * @param requestModel
     * @return
     */
    public List<DemandOrderCarrierDetailResponseModel> getCarrierOrderInfoByDemandId(GetCarrierOrdersByDemandIdRequestModel requestModel) {
        List<DemandOrderCarrierDetailResponseModel> carrierOrderInfoByDemandId = tCarrierOrderMapper.getCarrierOrderInfoByDemandId(requestModel.getDemandId());
        if(ListUtils.isEmpty(carrierOrderInfoByDemandId)){
            return carrierOrderInfoByDemandId;
        }
        List<Long> carrierOrderIdList = new ArrayList<>();
        for (DemandOrderCarrierDetailResponseModel model : carrierOrderInfoByDemandId) {
            carrierOrderIdList.add(model.getCarrierOrderId());
        }
        String carrierOrderIds = StringUtils.listToString(carrierOrderIdList,',');

        List<TCarrierOrderVehicleHistory> historyList = tCarrierOrderVehicleHistoryMapper.getAllByCarrierOrderIds(carrierOrderIds);
        Map<Long, List<TCarrierOrderVehicleHistory>> historyMap = historyList.stream().collect(Collectors.groupingBy(TCarrierOrderVehicleHistory::getCarrierOrderId, Collectors.toList()));

        List<TicketsModel> ticketsModelList = tCarrierOrderTicketsMapper.getTicketsByCarrierOrderIds(carrierOrderIds);
        Map<Long, List<TicketsModel>> ticketsMap = ticketsModelList.stream().collect(Collectors.groupingBy(TicketsModel::getCarrierOrderId, Collectors.toList()));

        List<TCarrierOrderAddress> tCarrierOrderAddressList=tCarrierOrderAddressMapper.getByCarrierOrderIds(carrierOrderIds);
        Map<Long,TCarrierOrderAddress> addressMap=new HashMap<>();
        tCarrierOrderAddressList.forEach(item->addressMap.put(item.getCarrierOrderId(),item));

        List<TCarrierOrderVehicleHistory> vehicleHistoryList;
        TCarrierOrderVehicleHistory tCarrierOrderVehicleHistory;
        TCarrierOrderAddress address;
        for (DemandOrderCarrierDetailResponseModel model : carrierOrderInfoByDemandId) {
            List<SearchCarrierOrderListGoodsInfoModel> goodsInfoList = model.getGoodsInfoList();
            BigDecimal volume = BigDecimal.ZERO;
            for (SearchCarrierOrderListGoodsInfoModel info : goodsInfoList) {
                BigDecimal volumeVolume = getVolume(info.getExpectAmount(), info.getLength(), info.getWidth(), info.getHeight());
                volume = volume.add(volumeVolume);
            }
            model.setAmountVolume(volume.setScale(3, BigDecimal.ROUND_HALF_UP).toString());

            model.setTickets(ticketsMap.get(model.getCarrierOrderId()));

            vehicleHistoryList = historyMap.get(model.getCarrierOrderId());
            tCarrierOrderVehicleHistory = vehicleHistoryList.stream().filter(item -> item.getAuditStatus().equals(AuditStatusEnum.WAIT_AUDIT.getKey())).findFirst().orElse(null);
            if (tCarrierOrderVehicleHistory != null) {
                model.setIfWaitAudit(CommonConstant.INTEGER_ONE);
            } else {
                model.setIfWaitAudit(CommonConstant.INTEGER_ZERO);
            }
            address = addressMap.get(model.getCarrierOrderId());
            if(address!=null){
                model.setUnloadProvinceName(address.getUnloadProvinceName());
                model.setUnloadCityName(address.getUnloadCityName());
                model.setUnloadAreaName(address.getUnloadAreaName());
                model.setUnloadDetailAddress(address.getUnloadDetailAddress());
                model.setUnloadWarehouse(address.getUnloadWarehouse());
                model.setReceiverName(address.getReceiverName());
                model.setReceiverMobile(address.getReceiverMobile());
                model.setUnloadAddressIsAmend(address.getUnloadAddressIsAmend());
            }
        }
        return carrierOrderInfoByDemandId;
    }

    public BigDecimal getVolume(BigDecimal amount, Integer length, Integer width, Integer height) {
        amount = Optional.ofNullable(amount).orElse(BigDecimal.ZERO);
        length = Optional.ofNullable(length).orElse(CommonConstant.INTEGER_ZERO);
        width = Optional.ofNullable(width).orElse(CommonConstant.INTEGER_ZERO);
        height = Optional.ofNullable(height).orElse(CommonConstant.INTEGER_ZERO);
        BigDecimal multiply = amount.multiply(BigDecimal.valueOf(length).multiply(BigDecimal.valueOf(width))).multiply(BigDecimal.valueOf(height));
        return multiply.divide(new BigDecimal(1000).pow(3));
    }

    /**
     * 更新运单里程数
     *
     * @param carrierOrderIds 运单ids
     */
    public void carrierOrderMileage(List<Long> carrierOrderIds, List<Long> carrierAddressIds) {
        List<TCarrierOrderAddress> addressList;
        if (ListUtils.isNotEmpty(carrierOrderIds)) {
            addressList = tCarrierOrderAddressMapper.getByCarrierOrderIds(StringUtils.listToString(carrierOrderIds, ','));
        } else if (ListUtils.isNotEmpty(carrierAddressIds)) {
            addressList = tCarrierOrderAddressMapper.getByIds(StringUtils.listToString(carrierAddressIds, ','));
        } else {
            return;
        }
        List<TCarrierOrder> upCarrierOrderList = new ArrayList<>();
        TCarrierOrder upCarrierOrder;
        String loadLongitude;//经度
        String loadLatitude;//纬度
        String unloadLongitude;//经度
        String unloadLatitude;//纬度
        BigDecimal mileage;
        for (TCarrierOrderAddress address : addressList) {
            loadLongitude = address.getLoadLongitude();
            loadLatitude = address.getLoadLatitude();
            unloadLongitude = address.getUnloadLongitude();
            unloadLatitude = address.getUnloadLatitude();
            if (StringUtils.isHasEmpty(loadLongitude, loadLatitude, unloadLongitude, unloadLatitude)) {
                continue;
            }
            mileage = commonBiz.getMileageByLonAndLat(loadLongitude + "," + loadLatitude, unloadLongitude + "," + unloadLatitude);
            if (BigDecimal.ZERO.equals(mileage)) {
                continue;
            }
            upCarrierOrder = new TCarrierOrder();
            upCarrierOrder.setId(address.getCarrierOrderId());
            upCarrierOrder.setExpectMileage(mileage);
            upCarrierOrderList.add(upCarrierOrder);
        }
        if (ListUtils.isNotEmpty(upCarrierOrderList)) {
            tCarrierOrderMapper.batchUpdateCarrierOrders(upCarrierOrderList);
        }
    }

    /**
     * 前台运单各操作节点状态校验
     *
     * @param requestModel 运单ID 操作节点
     * @return 运单货物信息
     */
    public GetCarrierOrderNodeAmountResponseModel getCarrierOrderNodeAmount(GetCarrierOrderNodeAmountRequestModel requestModel) {
        GetCarrierOrderNodeAmountResponseModel retModel = new GetCarrierOrderNodeAmountResponseModel();

        //4个节点都是单个运单操作
        TCarrierOrder dbTCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (dbTCarrierOrder == null || IfValidEnum.INVALID.getKey().equals(dbTCarrierOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.DATA_STATUS_HAS_UPDATE);
        }

        //节点操作提示
        if (CommonConstant.INTEGER_ZERO.equals(requestModel.getNodeType())) {//到达提货地
            if (!dbTCarrierOrder.getStatus().equals(CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey())) {
                throw new BizException(CarrierDataExceptionEnum.CANT_NOT_REACHLOADADDRESS);
            }
        } else if (CommonConstant.INTEGER_ONE.equals(requestModel.getNodeType())) {//已提货
            if (!dbTCarrierOrder.getStatus().equals(CarrierOrderStatusEnum.WAIT_LOAD.getKey())) {
                throw new BizException(CarrierDataExceptionEnum.ONLY_WAIT_LOAD);
            }
        } else if (CommonConstant.INTEGER_TWO.equals(requestModel.getNodeType())) {//到达卸货地
            if (!dbTCarrierOrder.getStatus().equals(CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey())) {
                throw new BizException(CarrierDataExceptionEnum.ONLY_REACHUNLOADADDRESS);
            }
        } else if (CommonConstant.INTEGER_THREE.equals(requestModel.getNodeType())) {//已卸货
            if (!dbTCarrierOrder.getStatus().equals(CarrierOrderStatusEnum.WAIT_UNLOAD.getKey())) {
                throw new BizException(CarrierDataExceptionEnum.ONLY_WAIT_UNLOAD);
            }
        }

        //查询需求单
        TDemandOrder tDemandOrder = tDemandOrderMapper.selectByPrimaryKeyDecrypt(dbTCarrierOrder.getDemandOrderId());
        if (tDemandOrder == null || IfValidEnum.INVALID.getKey().equals(tDemandOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.DATA_STATUS_HAS_UPDATE);
        }

        //提货判断运单的出库状态
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getNodeType())) {
            //发货、调拨、退货、供应商直配、采购、退货仓库配送、退货调拨、新生销售, 货物完成出库才能提货
            List<Integer> entrustTypeListModel = Arrays.asList(EntrustTypeEnum.DELIVER.getKey(), EntrustTypeEnum.TRANSFERS.getKey(),
                    EntrustTypeEnum.RETURN_GOODS.getKey(), EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey(), EntrustTypeEnum.PROCUREMENT.getKey(),
                    EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey(), EntrustTypeEnum.RETURN_GOODS_TRANSFERS.getKey(), EntrustTypeEnum.LIFE_SALE.getKey());
            if (entrustTypeListModel.contains(tDemandOrder.getEntrustType())) {
                if (!CarrierOrderOutStatusEnum.FINISH_OUT.getKey().equals(dbTCarrierOrder.getOutStatus())) {
                    throw new BizException(CarrierDataExceptionEnum.NOT_ALLOWED_PICKUP);
                }
            }
        }

        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(dbTCarrierOrder.getId().toString());
        if (!workOrderMap.isEmpty()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }

        //货物
        if (!CommonConstant.INTEGER_ZERO.equals(requestModel.getNodeType()) && !CommonConstant.INTEGER_TWO.equals(requestModel.getNodeType())) {
            List<TCarrierOrderGoods> goodsList = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderId(dbTCarrierOrder.getId());
            List<Long> goodsListIds = goodsList.stream().map(TCarrierOrderGoods::getId).collect(Collectors.toList());
            Map<Long, List<TCarrierOrderGoodsCode>> tCarrierOrderGoodsCodesMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(goodsListIds)) {
                List<TCarrierOrderGoodsCode> tCarrierOrderGoodsCodes = tCarrierOrderGoodsCodeMapper.selectGoodsCodeByGoodsIds(goodsListIds.stream().map(Objects::toString).collect(Collectors.joining(",")));
                tCarrierOrderGoodsCodesMap = tCarrierOrderGoodsCodes.stream().collect(Collectors.groupingBy(TCarrierOrderGoodsCode::getCarrierOrderGoodsId));
            }



            List<GetCarrierOrderNodeAmountGoodsModel> retGoodsList = new ArrayList<>();
            for (TCarrierOrderGoods tempModel : goodsList) {
                //货物编码明细
                List<TCarrierOrderGoodsCode> tCarrierOrderGoodsCodes = tCarrierOrderGoodsCodesMap.getOrDefault(tempModel.getId(), new ArrayList<>());

                List<CarrierOrderNodeCodeModel> codeDtoList = new ArrayList<>();
                for (TCarrierOrderGoodsCode tCarrierOrderGoodsCode : tCarrierOrderGoodsCodes) {
                    CarrierOrderNodeCodeModel codeModel = new CarrierOrderNodeCodeModel();
                    codeModel.setUnit(tCarrierOrderGoodsCode.getUnit());
                    codeModel.setLoadAmount(tCarrierOrderGoodsCode.getLoadAmount());
                    codeModel.setUnloadAmount(tCarrierOrderGoodsCode.getUnloadAmount());
                    codeModel.setYeloCode(tCarrierOrderGoodsCode.getYeloGoodCode());
                    codeDtoList.add(codeModel);
                }

                GetCarrierOrderNodeAmountGoodsModel retGoods = new GetCarrierOrderNodeAmountGoodsModel();
                retGoods.setGoodsId(tempModel.getId());
                retGoods.setExpectAmount(tempModel.getExpectAmount());
                retGoods.setLoadAmount(tempModel.getLoadAmount());
                retGoods.setGoodsName(tempModel.getGoodsName());
                retGoods.setCodeDtoList(codeDtoList);
                retGoodsList.add(retGoods);

            }
            retModel.setGoodsList(retGoodsList);
            retModel.setGoodsUnit(dbTCarrierOrder.getGoodsUnit());
        }
        retModel.setCarrierOrderId(dbTCarrierOrder.getId());
        return retModel;
    }

    //创建运单事件
    public TCarrierOrderEvents getCarrierOrderEvent(Long carrierOrderId, CarrierOrderEventsTypeEnum eventTypeEnum, String userName, String remark) {
        TCarrierOrderEvents carrierOrderEvents = new TCarrierOrderEvents();
        carrierOrderEvents.setCarrierOrderId(carrierOrderId);
        carrierOrderEvents.setEvent(eventTypeEnum.getKey());
        carrierOrderEvents.setEventDesc(eventTypeEnum.getValue());
        carrierOrderEvents.setRemark(remark);
        carrierOrderEvents.setEventTime(new Date());
        carrierOrderEvents.setOperatorName(userName);
        carrierOrderEvents.setOperateTime(new Date());
        commonBiz.setBaseEntityAdd(carrierOrderEvents, userName);
        return carrierOrderEvents;
    }

    //创建运单日志
    public TCarrierOrderOperateLogs getCarrierOrderOperateLogs(Long carrierOrderId, CarrierOrderOperateLogsTypeEnum logTypeEnum, String userName, String remark) {
        TCarrierOrderOperateLogs carrierOrderOperateLogs = new TCarrierOrderOperateLogs();
        carrierOrderOperateLogs.setCarrierOrderId(carrierOrderId);
        carrierOrderOperateLogs.setOperationType(logTypeEnum.getKey());
        carrierOrderOperateLogs.setOperationContent(logTypeEnum.getValue());
        carrierOrderOperateLogs.setRemark(remark);
        carrierOrderOperateLogs.setOperatorName(userName);
        carrierOrderOperateLogs.setOperateTime(new Date());
        commonBiz.setBaseEntityAdd(carrierOrderOperateLogs, userName);
        return carrierOrderOperateLogs;
    }

    //异步批量修改运单地址经纬度
    @Transactional
    public void updateCarrierOrderAddressLonAndLat(List<TCarrierOrderAddress> tCarrierOrderAddressList, boolean updateExpectMileage) {
        if (ListUtils.isNotEmpty(tCarrierOrderAddressList)) {
            TCarrierOrderAddress carrierOrderAddressUp;
            List<TCarrierOrderAddress> carrierOrderAddressUpList = new ArrayList<>();

            List<Long> carrierAddressIds = new ArrayList<>();
            for (TCarrierOrderAddress tCarrierOrderAddress : tCarrierOrderAddressList) {
                carrierAddressIds.add(tCarrierOrderAddress.getId());
                StringBuilder address = new StringBuilder();
                if (StringUtils.isNotBlank(tCarrierOrderAddress.getUnloadDetailAddress())) {
                    address.append(tCarrierOrderAddress.getUnloadProvinceName()).append(tCarrierOrderAddress.getUnloadCityName()).append(tCarrierOrderAddress.getUnloadAreaName()).append(tCarrierOrderAddress.getUnloadDetailAddress());
                }
                GetLonLatByMapIdRequestModel model = new GetLonLatByMapIdRequestModel();
                model.setAddress(address.toString());
                model.setMapId(tCarrierOrderAddress.getUnloadAreaId());
                GetLonLatByMapIdResponseModel lonLatModel = basicDataClient.getLonLatByMapId(model);

                carrierOrderAddressUp = new TCarrierOrderAddress();
                carrierOrderAddressUp.setId(tCarrierOrderAddress.getId());
                carrierOrderAddressUp.setUnloadLongitude(lonLatModel.getLongitude());
                carrierOrderAddressUp.setUnloadLatitude(lonLatModel.getLatitude());
                carrierOrderAddressUpList.add(carrierOrderAddressUp);
            }
            tCarrierOrderAddressMapper.batchUpdate(carrierOrderAddressUpList);

            if (updateExpectMileage
                    && ListUtils.isNotEmpty(carrierAddressIds)) {
                //更新运单预计里程数
                carrierOrderMileage(null, carrierAddressIds);
            }
        }
    }

    /**
     * 匹配车主价格预准备的数据
     *
     * @param companyCarrierIdList 车主id集合
     * @param areaModelList        运单提卸货区id集合
     * @return result
     */
    public MatchCarrierPriceResultModel matchCarrierPriceStepOneResult(List<Long> companyCarrierIdList, List<AddressAreaExistRequestModel> areaModelList) {
        //车主运价id
        List<Long> carrierFreightConfigIdList = new ArrayList<>();
        //查询车主运价配置
        List<CarrierFreightCarrierModel> carrierFreightCarrierModelList = tCarrierFreightConfigMapper.selectCarrierFreightByCompanyCarrier(companyCarrierIdList);
        //车主运价根据需求类型进行分组
        Map<Long, Map<Integer, CarrierFreightCarrierModel>> entrustCarrierFreightMap = new HashMap<>();

        if (ListUtils.isNotEmpty(carrierFreightCarrierModelList)) {
            Map<Long, List<CarrierFreightCarrierModel>> companyCarrierFreightMap = carrierFreightCarrierModelList.stream().collect(Collectors.groupingBy(CarrierFreightCarrierModel::getCompanyCarrierId));
            for (Map.Entry<Long, List<CarrierFreightCarrierModel>> entry : companyCarrierFreightMap.entrySet()) {
                Long companyCarrierId = entry.getKey();
                List<CarrierFreightCarrierModel> carrierFreightCarrierModels = entry.getValue();
                Map<Integer, CarrierFreightCarrierModel> entrustFreightMap = new HashMap<>();
                for (CarrierFreightCarrierModel carrierFreightCarrierModel : carrierFreightCarrierModels) {
                    //运价ID
                    carrierFreightConfigIdList.add(carrierFreightCarrierModel.getCarrierFreightConfigId());
                    String entrustTypeStComma = carrierFreightCarrierModel.getEntrustType();
                    if (StringUtils.isNotBlank(entrustTypeStComma)) {
                        String[] entrustArray = entrustTypeStComma.split(CommonConstant.COMMA);
                        for (String entrustTypeStr : entrustArray) {
                            //一种需求类型只会对应一种运价配置
                            Integer entrustType = Integer.valueOf(entrustTypeStr);
                            entrustFreightMap.putIfAbsent(entrustType, carrierFreightCarrierModel);
                        }
                    }
                }
                entrustCarrierFreightMap.putIfAbsent(companyCarrierId, entrustFreightMap);
            }
        }

        //查询系统配置距离
        List<TRouteDistanceConfig> tRouteDistanceConfigs = tRouteDistanceConfigMapper.selectByFromAreaAndToArea(areaModelList);

        //查询运价方案
        List<TCarrierFreightConfigScheme> tCarrierFreightConfigSchemes = tCarrierFreightConfigSchemeMapper.selectByCarrierFreightIds(carrierFreightConfigIdList);
        Map<Long, List<TCarrierFreightConfigScheme>> carrierFreightConfigSchemeMap = new HashMap<>();
        if (ListUtils.isNotEmpty(tCarrierFreightConfigSchemes)) {
            carrierFreightConfigSchemeMap = tCarrierFreightConfigSchemes.stream().collect(Collectors.groupingBy(TCarrierFreightConfigScheme::getFreightConfigId));
        }
        return new MatchCarrierPriceResultModel(entrustCarrierFreightMap, tRouteDistanceConfigs, carrierFreightConfigSchemeMap);
    }

    /**
     * 匹配车主价格
     *
     * @param tRouteDistanceConfigs         运价阶梯配置
     * @param carrierFreightConfigSchemeMap 车主费用方案配置
     * @param getCarrierPriceModel          运单信息
     * @param carrierFreightCarrierModel    车主运价信息
     */
    public void matchCarrierPrice(List<TRouteDistanceConfig> tRouteDistanceConfigs, Map<Long, List<TCarrierFreightConfigScheme>> carrierFreightConfigSchemeMap, GetCarrierPriceModel getCarrierPriceModel, CarrierFreightCarrierModel carrierFreightCarrierModel) {
        Integer carrierPriceType = null;
        BigDecimal carrierPrice = null;
        //查询运价方案
        List<TCarrierFreightConfigScheme> carrierFreightConfigScheme = carrierFreightConfigSchemeMap.get(carrierFreightCarrierModel.getCarrierFreightConfigId());
        if (ListUtils.isEmpty(carrierFreightConfigScheme)) {
            return;
        }
        if (ConfigTypeEnum.FIXED_ROUTE.getKey().equals(carrierFreightCarrierModel.getConfigType())) {
            //固定路线
            //路线类型只有一条方案
            TCarrierFreightConfigScheme tCarrierFreightConfigScheme = carrierFreightConfigScheme.get(CommonConstant.INTEGER_ZERO);

            //查询路线信息
            List<AddressConfigModel> addressConfigModels = tCarrierFreightConfigAddressMapper.selectAddressConfigAndLadderBySchemeId(tCarrierFreightConfigScheme.getId(), EnabledEnum.ENABLED.getKey());

            Map<Long, List<AddressConfigLadderModel>> addressConfigIdLadderMap = new HashMap<>();
            Long addressConfigId = null;
            for (AddressConfigModel addressConfigModel : addressConfigModels) {
                addressConfigIdLadderMap.put(addressConfigModel.getAddressConfigId(), addressConfigModel.getLadderConfigList());
                //比对收货发货区
                if (getCarrierPriceModel.getLoadAreaId().equals(addressConfigModel.getFromAreaId()) &&
                        getCarrierPriceModel.getUnloadAreaId().equals(addressConfigModel.getToAreaId())) {
                    addressConfigId = addressConfigModel.getAddressConfigId();
                    break;
                }
            }
            if (addressConfigId == null) {
                return;
            }
            List<AddressConfigLadderModel> ladderModels = addressConfigIdLadderMap.get(addressConfigId);
            if (ListUtils.isEmpty(ladderModels)) {
                return;
            }
            //阶梯最大截至数量
            BigDecimal maxAmountTo = null;
            AddressConfigLadderModel maxLadderModel = null;
            for (AddressConfigLadderModel ladderModel : ladderModels) {
                //空值处理
                if (ladderModel.getLadderFrom() == null) {
                    ladderModel.setLadderFrom(BigDecimal.ZERO);
                }
                if (ladderModel.getLadderTo() == null) {
                    ladderModel.setLadderTo(BigDecimal.ZERO);
                }
                if (maxAmountTo == null) {
                    maxAmountTo = ladderModel.getLadderTo();
                    maxLadderModel = ladderModel;
                } else {
                    //更新最大截至数量和最大截至数量金额
                    if (ladderModel.getLadderTo().compareTo(maxAmountTo) > CommonConstant.INTEGER_ZERO) {
                        maxAmountTo = ladderModel.getLadderTo();
                        maxLadderModel = ladderModel;
                    }
                }
                //阶梯起始数量<运单货物数量<=阶梯截至数量
                if (getCarrierPriceModel.getGoodsAmount().compareTo(ladderModel.getLadderFrom()) > CommonConstant.INTEGER_ZERO &&
                        getCarrierPriceModel.getGoodsAmount().compareTo(ladderModel.getLadderTo()) <= CommonConstant.INTEGER_ZERO &&
                        matchPrice(getCarrierPriceModel.getGoodsUnit(), ladderModel.getLadderUnit())) {
                    carrierPriceType = ladderModel.getPriceMode();
                    carrierPrice = ladderModel.getUnitPrice();
                    break;
                }
            }

            //未匹配到阶梯,取最大阶梯
            if (carrierPrice == null
                    && maxLadderModel != null
                    && matchPrice(getCarrierPriceModel.getGoodsUnit(), maxLadderModel.getLadderUnit())) {
                carrierPriceType = maxLadderModel.getPriceMode();
                carrierPrice = maxLadderModel.getUnitPrice();
            }


        } else if (ConfigTypeEnum.AREA_CONFIG.getKey().equals(carrierFreightCarrierModel.getConfigType())) {
            //区域设置
            List<Long> schemeIdLit = carrierFreightConfigScheme.stream().map(TCarrierFreightConfigScheme::getId).collect(Collectors.toList());
            //查询阶梯信息
            Map<Long, CarrierFreightConfigPriceDesignResponseModel> ladderDetailMap =
                    carrierFreightConfigLadderBiz.getLadderDetail(CarrierFreightConfigLadderModeEnum.SCHEME_CONFIG.getKey(), schemeIdLit);

            //遍历方案
            for (TCarrierFreightConfigScheme tCarrierFreightConfigScheme : carrierFreightConfigScheme) {
                //判断运单当前方案
                CarrierFreightConfigSchemeTypeEnum schemeTypeEnum;
                if (getCarrierPriceModel.getLoadAreaId().equals(getCarrierPriceModel.getUnloadAreaId())) {
                    //同区
                    schemeTypeEnum = CarrierFreightConfigSchemeTypeEnum.REGION_SAME_CONFIG;
                } else {
                    //跨区
                    schemeTypeEnum = CarrierFreightConfigSchemeTypeEnum.REGION_DIFFERENT_CONFIG;
                }

                //如果当前方案类型包含同区跨区或者和运单区域类型一致
                if (CarrierFreightConfigSchemeTypeEnum.REGION_CONFIG.getKey().equals(tCarrierFreightConfigScheme.getSchemeType()) || schemeTypeEnum.getKey().equals(tCarrierFreightConfigScheme.getSchemeType())) {
                    //获取每个方案下的阶梯
                    CarrierFreightConfigPriceDesignResponseModel priceModel = ladderDetailMap.get(tCarrierFreightConfigScheme.getId());
                    //获取阶梯价格
                    List<CarrierFreightConfigLadderResponseModel> ladderModelList = priceModel.getLadderConfigList();
                    BigDecimal maxAmountTo = null;
                    CarrierFreightConfigLadderResponseModel maxLadderModel = null;
                    for (CarrierFreightConfigLadderResponseModel ladderModel : ladderModelList) {
                        //空值处理
                        if (ladderModel.getLadderFrom() == null) {
                            ladderModel.setLadderFrom(BigDecimal.ZERO);
                        }
                        if (ladderModel.getLadderTo() == null) {
                            ladderModel.setLadderTo(BigDecimal.ZERO);
                        }
                        if (maxAmountTo == null) {
                            maxAmountTo = ladderModel.getLadderTo();
                            maxLadderModel = ladderModel;
                        } else {
                            //更新最大截至数量和最大截至数量金额
                            if (ladderModel.getLadderTo().compareTo(maxAmountTo) > CommonConstant.INTEGER_ZERO) {
                                maxAmountTo = ladderModel.getLadderTo();
                                maxLadderModel = ladderModel;
                            }
                        }
                        //阶梯起始数量<运单货物数量<=阶梯截至数量
                        if (getCarrierPriceModel.getGoodsAmount().compareTo(ladderModel.getLadderFrom()) > CommonConstant.INTEGER_ZERO &&
                                getCarrierPriceModel.getGoodsAmount().compareTo(ladderModel.getLadderTo()) <= CommonConstant.INTEGER_ZERO &&
                                matchPrice(getCarrierPriceModel.getGoodsUnit(), ladderModel.getLadderUnit())) {
                            carrierPriceType = ladderModel.getPriceMode();
                            carrierPrice = ladderModel.getUnitPrice();
                            break;
                        }
                    }
                    //未匹配到阶梯,取最大阶梯
                    if (carrierPrice == null
                            && maxLadderModel != null
                            && matchPrice(getCarrierPriceModel.getGoodsUnit(), maxLadderModel.getLadderUnit())) {
                        carrierPriceType = maxLadderModel.getPriceMode();
                        carrierPrice = maxLadderModel.getUnitPrice();
                    }

                }
            }
        } else if (ConfigTypeEnum.DISTANCE_CONFIG.getKey().equals(carrierFreightCarrierModel.getConfigType())) {
            //距离阶梯

            //获取当前运单的配置的计费距离
            BigDecimal billingDistance = BigDecimal.ZERO;
            if (ListUtils.isNotEmpty(tRouteDistanceConfigs)) {
                TRouteDistanceConfig tRouteDistanceConfig = tRouteDistanceConfigs
                        .stream()
                        .filter(tRouteDistanceConfig1 -> tRouteDistanceConfig1.getFromAreaId().equals(getCarrierPriceModel.getLoadAreaId()) && tRouteDistanceConfig1.getToAreaId().equals(getCarrierPriceModel.getUnloadAreaId()))
                        .findFirst().orElse(null);
                if (tRouteDistanceConfig != null){
                    billingDistance = tRouteDistanceConfig.getBillingDistance();
                }
            }
            //预计距离
            BigDecimal expectMileage = getCarrierPriceModel.getExpectMileage();

            //区域设置
            List<Long> schemeIdLit = carrierFreightConfigScheme.stream().map(TCarrierFreightConfigScheme::getId).collect(Collectors.toList());
            //查询阶梯信息
            Map<Long, CarrierFreightConfigPriceDesignResponseModel> ladderDetailMap =
                    carrierFreightConfigLadderBiz.getLadderDetail(CarrierFreightConfigLadderModeEnum.SCHEME_CONFIG.getKey(), schemeIdLit);

            //遍历方案
            for (TCarrierFreightConfigScheme tCarrierFreightConfigScheme : carrierFreightConfigScheme) {
                //获取方案下的阶梯
                CarrierFreightConfigPriceDesignResponseModel carrierFreightConfigPriceDesignResponseModel = ladderDetailMap.get(tCarrierFreightConfigScheme.getId());
                BigDecimal matchMileage = null;
                if (CarrierFreightConfigSchemeTypeEnum.CALCULATIONS_DISTANCE_CONFIG.getKey().equals(tCarrierFreightConfigScheme.getSchemeType())) {
                    //系统计算预计距离
                    matchMileage = expectMileage;
                } else if (CarrierFreightConfigSchemeTypeEnum.DISTANCE_CONFIG.getKey().equals(tCarrierFreightConfigScheme.getSchemeType())) {
                    //系统配置距离
                    matchMileage = billingDistance;
                }

                if (matchMileage != null && matchMileage.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                    //匹配阶梯
                    List<CarrierFreightConfigLadderResponseModel> ladderConfigList = carrierFreightConfigPriceDesignResponseModel.getLadderConfigList();
                    BigDecimal maxAmountTo = null;
                    CarrierFreightConfigLadderResponseModel maxLadderModel = null;
                    for (CarrierFreightConfigLadderResponseModel ladderModel : ladderConfigList) {
                            /*
                            阶梯中不会同时出现 多级阶梯和非多级阶梯
                            * */
                        //空值处理
                        if (ladderModel.getLadderFrom() == null) {
                            ladderModel.setLadderFrom(BigDecimal.ZERO);
                        }
                        if (ladderModel.getLadderTo() == null) {
                            ladderModel.setLadderTo(BigDecimal.ZERO);
                        }
                        //是否多级阶梯
                        if (ListUtils.isEmpty(ladderModel.getLadderConfigList())) {
                            if (maxAmountTo == null) {
                                maxAmountTo = ladderModel.getLadderTo();
                                maxLadderModel = ladderModel;
                            } else {
                                //更新最大截至数量和最大截至数量金额
                                if (ladderModel.getLadderTo().compareTo(maxAmountTo) > CommonConstant.INTEGER_ZERO) {
                                    maxAmountTo = ladderModel.getLadderTo();
                                    maxLadderModel = ladderModel;
                                }
                            }
                            //阶梯起始<运单里程<=阶梯截至
                            if (matchMileage.compareTo(ladderModel.getLadderFrom()) > CommonConstant.INTEGER_ZERO &&
                                    matchMileage.compareTo(ladderModel.getLadderTo()) <= CommonConstant.INTEGER_ZERO) {
                                carrierPriceType = ladderModel.getPriceMode();
                                carrierPrice = ladderModel.getUnitPrice();
                                break;
                            }
                        } else {
                            if (maxLadderModel == null) {
                                maxLadderModel = ladderModel;
                            } else {
                                if (ladderModel.getLadderTo().compareTo(maxLadderModel.getLadderTo()) > CommonConstant.INTEGER_ZERO) {
                                    maxLadderModel = ladderModel;
                                }
                            }

                            //匹配当前多级阶梯里程
                            if (matchMileage.compareTo(ladderModel.getLadderFrom()) > CommonConstant.INTEGER_ZERO &&
                                    matchMileage.compareTo(ladderModel.getLadderTo()) <= CommonConstant.INTEGER_ZERO) {
                                //遍历阶梯子集
                                List<CarrierFreightConfigLadderResponseModel> ladderSubsetList = ladderModel.getLadderConfigList();
                                BigDecimal subsetMaxAmountTo = null;
                                CarrierFreightConfigLadderResponseModel subsetMaxLadderModel = null;
                                for (CarrierFreightConfigLadderResponseModel ladderSubsetModel : ladderSubsetList) {
                                    //空值处理
                                    if (ladderSubsetModel.getLadderFrom() == null) {
                                        ladderSubsetModel.setLadderFrom(BigDecimal.ZERO);
                                    }
                                    if (ladderSubsetModel.getLadderTo() == null) {
                                        ladderSubsetModel.setLadderTo(BigDecimal.ZERO);
                                    }
                                    if (subsetMaxAmountTo == null) {
                                        subsetMaxAmountTo = ladderSubsetModel.getLadderTo();
                                        subsetMaxLadderModel = ladderSubsetModel;
                                    } else {
                                        //更新最大截至数量和最大截至数量金额
                                        if (ladderSubsetModel.getLadderTo().compareTo(subsetMaxAmountTo) > CommonConstant.INTEGER_ZERO) {
                                            subsetMaxAmountTo = ladderSubsetModel.getLadderTo();
                                            subsetMaxLadderModel = ladderSubsetModel;
                                        }
                                    }
                                    //阶梯起始数量<运单货物数量<=阶梯截至数量
                                    if (getCarrierPriceModel.getGoodsAmount().compareTo(ladderSubsetModel.getLadderFrom()) > CommonConstant.INTEGER_ZERO &&
                                            getCarrierPriceModel.getGoodsAmount().compareTo(ladderSubsetModel.getLadderTo()) <= CommonConstant.INTEGER_ZERO &&
                                            matchPrice(getCarrierPriceModel.getGoodsUnit(), ladderSubsetModel.getLadderUnit())) {
                                        carrierPriceType = ladderSubsetModel.getPriceMode();
                                        carrierPrice = ladderSubsetModel.getUnitPrice();
                                        break;
                                    }
                                }
                                //未匹配到阶梯,取最大阶梯
                                if (carrierPrice == null
                                        && subsetMaxLadderModel != null
                                        && matchPrice(getCarrierPriceModel.getGoodsUnit(), subsetMaxLadderModel.getLadderUnit())) {
                                    carrierPriceType = subsetMaxLadderModel.getPriceMode();
                                    carrierPrice = subsetMaxLadderModel.getUnitPrice();
                                }
                            }
                        }
                    }

                    //未匹配到阶梯,取最大阶梯
                    if (carrierPrice == null
                            && maxLadderModel != null
                            && matchPrice(getCarrierPriceModel.getGoodsUnit(), maxLadderModel.getLadderUnit())) {
                        if (ListUtils.isNotEmpty(maxLadderModel.getLadderConfigList())) {
                            //多级阶梯
                            BigDecimal subsetMaxAmountTo = null;
                            CarrierFreightConfigLadderResponseModel subsetMaxLadderModel = null;
                            for (CarrierFreightConfigLadderResponseModel ladderSubsetModel : maxLadderModel.getLadderConfigList()) {
                                //空值处理
                                if (ladderSubsetModel.getLadderFrom() == null) {
                                    ladderSubsetModel.setLadderFrom(BigDecimal.ZERO);
                                }
                                if (ladderSubsetModel.getLadderTo() == null) {
                                    ladderSubsetModel.setLadderTo(BigDecimal.ZERO);
                                }
                                if (subsetMaxAmountTo == null) {
                                    subsetMaxAmountTo = ladderSubsetModel.getLadderTo();
                                    subsetMaxLadderModel = ladderSubsetModel;
                                } else {
                                    //更新最大截至数量和最大截至数量金额
                                    if (ladderSubsetModel.getLadderTo().compareTo(subsetMaxAmountTo) > CommonConstant.INTEGER_ZERO) {
                                        subsetMaxAmountTo = ladderSubsetModel.getLadderTo();
                                        subsetMaxLadderModel = ladderSubsetModel;
                                    }
                                }
                                //阶梯起始数量<运单货物数量<=阶梯截至数量
                                if (getCarrierPriceModel.getGoodsAmount().compareTo(ladderSubsetModel.getLadderFrom()) > CommonConstant.INTEGER_ZERO &&
                                        getCarrierPriceModel.getGoodsAmount().compareTo(ladderSubsetModel.getLadderTo()) <= CommonConstant.INTEGER_ZERO &&
                                        matchPrice(getCarrierPriceModel.getGoodsUnit(), ladderSubsetModel.getLadderUnit())) {
                                    carrierPriceType = ladderSubsetModel.getPriceMode();
                                    carrierPrice = ladderSubsetModel.getUnitPrice();
                                    break;
                                }
                            }
                            //未匹配到阶梯,取最大阶梯
                            if (carrierPrice == null
                                    && subsetMaxLadderModel != null
                                    && matchPrice(getCarrierPriceModel.getGoodsUnit(), subsetMaxLadderModel.getLadderUnit())) {
                                carrierPriceType = subsetMaxLadderModel.getPriceMode();
                                carrierPrice = subsetMaxLadderModel.getUnitPrice();
                            }
                        } else {
                            carrierPriceType = maxLadderModel.getPriceMode();
                            carrierPrice = maxLadderModel.getUnitPrice();
                        }
                    }
                }
            }
        }
        //车主费用
        getCarrierPriceModel.setCarrierPriceType(carrierPriceType);
        getCarrierPriceModel.setCarrierPrice(carrierPrice);
    }

    /**
     * 价格是否匹配
     *
     * @param carrierOrderGoodsUnit 运单货物单位
     * @param ladderUnit            阶梯货物单位
     * @return flag
     */
    private boolean matchPrice(Integer carrierOrderGoodsUnit, Integer ladderUnit) {
        boolean isMatch = false;
        if (carrierOrderGoodsUnit.equals(ladderUnit)) {
            isMatch = true;
        } else if (GoodsUnitEnum.BY_PACKAGE.getKey().equals(ladderUnit) && GoodsUnitEnum.BY_VOLUME.getKey().equals(carrierOrderGoodsUnit)) {
            //运价维护的是件, 那么运单货物单位如果是件和方都要匹配价格
            isMatch = true;
        }
        return isMatch;
    }

    /**
     * 匹配云盘货主单价
     *
     * @param entrustTypeList
     * @param goodsUnitList
     * @return key:类型+单位，value:单价
     */
    public Map<String, BigDecimal> getEntrustPrice(List<Integer> entrustTypeList, List<Integer> goodsUnitList) {
        Map<String, BigDecimal> map = new HashMap<>();
        if (ListUtils.isEmpty(entrustTypeList) || ListUtils.isEmpty(goodsUnitList)) {
            return map;
        }
        List<TTrayCost> trayCostList = tTrayCostMapper.getTopByEntrustTypesAndGoodsUnits(LocalStringUtil.listTostring(entrustTypeList, ','), LocalStringUtil.listTostring(goodsUnitList, ','));
        if (ListUtils.isNotEmpty(trayCostList)) {
            for (TTrayCost tTrayCost : trayCostList) {
                map.put(tTrayCost.getEntrustType().toString()+tTrayCost.getGoodsUnit().toString(), tTrayCost.getUnitPrice());
            }
        }
        return map;
    }

    /**
     * 更新运单对账单状态
     *
     * @param carrierOrderIdList 运单id集合
     * @param statusEnum         运单对账单状态
     */
    @Transactional
    public void updateSettleStatementCarrierOrderStatus(List<Long> carrierOrderIdList, CarrierSettleStatementStatusEnum statusEnum,Object...obj) {
        if (ListUtils.isEmpty(carrierOrderIdList)) {
            return;
        }
        TCarrierOrder updateWrapper = new TCarrierOrder();

        try {
            if (obj != null) {
                BigDecimal freight = (BigDecimal) obj[1];
                if (freight != null && freight.compareTo(new BigDecimal("0.00")) != 0) {
                    updateWrapper.setStatementFreightTaxPoint(freight.divide(new BigDecimal("100")));
                }
                BigDecimal other = (BigDecimal) obj[0];
                if (other != null && other.compareTo(new BigDecimal("0.00")) != 0) {
                    updateWrapper.setStatementOtherFeeTaxPoint(other.divide(new BigDecimal("100")));
                }
            }
        } catch (Exception e) {

        }

        if (statusEnum.equals(CarrierSettleStatementStatusEnum.NOT_RELATED)){
            updateWrapper.setStatementFreightTaxPoint(new BigDecimal("0.00"));
            updateWrapper.setStatementOtherFeeTaxPoint(new BigDecimal("0.00"));
        }


        //使用批量模式
        SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
        TCarrierOrderMapper tCarrierOrderMapperScope = sqlSession.getMapper(TCarrierOrderMapper.class);
        try {
            String operator = BaseContextHandler.getUserName();
            //拆分集合为10条一组
            List<List<Long>> partitionIdList = org.apache.commons.collections4.ListUtils.partition(carrierOrderIdList, CommonConstant.INTEGER_TEN);
            for (List<Long> idList : partitionIdList) {
                tCarrierOrderMapperScope.batchUpdateCarrierSettleStatus(idList, statusEnum.getKey(), operator,updateWrapper);
            }
            sqlSession.commit();
        } catch (Exception e) {
            sqlSession.rollback();
            log.warn(CarrierDataExceptionEnum.SETTLE_STATEMENT_CARRIER_ORDER_UPDATE_ERROR.getValue(), e);
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_CARRIER_ORDER_UPDATE_ERROR);
        } finally {
            sqlSession.close();
        }
    }


    /**
     * 查询配置的计费里程
     *
     * @param areaList 提卸货区域map
     * @return 计费里程map
     */
    public Map<String, BigDecimal> getConfigDistanceMap(List<AddressAreaExistRequestModel> areaList) {
        List<TRouteDistanceConfig> tRouteDistanceConfigs = tRouteDistanceConfigMapper.selectByFromAreaAndToArea(areaList);
        Map<String, BigDecimal> configDistanceMap = new HashMap<>();
        if (ListUtils.isNotEmpty(tRouteDistanceConfigs)) {
            configDistanceMap = tRouteDistanceConfigs
                    .stream().collect(Collectors.toMap(tRouteDistanceConfig -> tRouteDistanceConfig.getFromAreaId() + CommonConstant.COMMA + tRouteDistanceConfig.getToAreaId(),
                            TRouteDistanceConfig::getBillingDistance));
        }
        return configDistanceMap;
    }
}
