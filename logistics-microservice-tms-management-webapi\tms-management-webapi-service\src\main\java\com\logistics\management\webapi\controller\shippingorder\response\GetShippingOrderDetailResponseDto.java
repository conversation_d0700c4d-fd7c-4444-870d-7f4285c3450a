package com.logistics.management.webapi.controller.shippingorder.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/6 10:15
 */
@Data
public class GetShippingOrderDetailResponseDto {

    //基础信息
    /**
     * 运输单id
     */
    private String shippingOrderId="";

    /**
     * 运输单号
     */
    private String shippingOrderCode="";

    /**
     * 状态：0 待审核，1 已审核，2 已驳回
     */
    private String status="";
    /**
     * 状态
     */
    private String statusLabel="";

    /**
     * 创建时间
     */
    private String createdTime="";

    /**
     * 创建人
     */
    private String createdBy="";


    //零担拼单详情
    /**
     * 货物单位：1 件，2 吨，3 方，4 块
     */
    private String goodsUnit="";
    /**
     * 拼单列表
     */
    private List<GetShippingOrderDetailOrderListResponseDto> orderList=new ArrayList<>();
    /**
     * 装卸数-装
     */
    private String loadPointAmount="";

    /**
     * 装卸数-卸
     */
    private String unloadPointAmount="";

    /**
     * 车长
     */
    private String vehicleLength="";

    /**
     * 整车费用
     */
    private String carrierFreight="";

    /**
     * 串点费用
     */
    private String crossPointFee="";

    /**
     * 合计车主费用
     */
    private String expectCarrierFreight="";


    //业务审核
    /**
     * 审核时间
     */
    private String auditTime="";

    /**
     * 审核备注
     */
    private String auditRemark="";


    /**
     * 操作日志
     */
    private List<GetShippingOrderOperateLogListResponseDto> operateLogList=new ArrayList<>();

}
