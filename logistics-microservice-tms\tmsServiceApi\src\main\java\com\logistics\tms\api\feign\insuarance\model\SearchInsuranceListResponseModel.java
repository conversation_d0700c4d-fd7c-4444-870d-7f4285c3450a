package com.logistics.tms.api.feign.insuarance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/6/4 19:16
 */
@Data
public class SearchInsuranceListResponseModel {
    @ApiModelProperty("保险id")
    private Long insuranceId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆机构 1 自主，2 外部，3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("司机电话")
    private String driverPhone;
    @ApiModelProperty("险种")
    private Integer insuranceType;
    @ApiModelProperty("保险公司")
    private String insuranceCompanyName;
    @ApiModelProperty("保单号")
    private String policyNumber;
    @ApiModelProperty("保单状态：结算状态：-1 未开始，0 待结算，1 部分结算，2 已结算")
    private Integer settlementStatus;
    @ApiModelProperty("保费合计")
    private BigDecimal premium;
    @ApiModelProperty("未还保险费")
    private BigDecimal outstandingPremium;
    @ApiModelProperty("退保金额")
    private BigDecimal refundPremium;
    @ApiModelProperty("保险生效时间")
    private Date startTime;
    @ApiModelProperty("保险截止时间")
    private Date endTime;
    @ApiModelProperty("作废原因")
    private String cancelReason;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("新增人")
    private String addUserName;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;
    @ApiModelProperty("代缴车船税")
    private BigDecimal paymentOfVehicleAndVesselTax;

    @ApiModelProperty("状态类型：0 正常状态，1 取消，2 退保")
    private Integer statusType;

    //个人意外险时
    @ApiModelProperty("保险公司")
    private String insuranceCompanyNamePerson;
    @ApiModelProperty("保单号")
    private String policyNumberPerson;
    @ApiModelProperty("批单号")
    private String batchNumberPerson;
    @ApiModelProperty("保险生效时间")
    private Date startTimePerson;
    @ApiModelProperty("保险截止时间")
    private Date endTimePerson;
    @ApiModelProperty("保单总额")
    private BigDecimal grossPremiumPerson;
    @ApiModelProperty("保单人数")
    private Integer policyPersonCountPerson;
    //关联扣费保单
    @ApiModelProperty("保单总额")
    private BigDecimal grossPremium;
    @ApiModelProperty("保单人数")
    private Integer policyPersonCount;

    @ApiModelProperty(value = "关联保险费用集合")
    private List<InsuranceSettlementCostsRelationModel> insuranceSettlementCostsList;
}
