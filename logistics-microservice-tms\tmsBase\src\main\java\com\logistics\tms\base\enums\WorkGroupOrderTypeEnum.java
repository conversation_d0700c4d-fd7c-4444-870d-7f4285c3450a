package com.logistics.tms.base.enums;

import lombok.Getter;

/**
 * 业务类型, 10:需求单 20:运单
 */
@Getter
public enum WorkGroupOrderTypeEnum {
    DEFAULT(0, ""),
    DEMAND_ORDER(10, "需求单"),
    CARRIER_ORDER(20, "运单")
    ;

    private final Integer key;
    private final String value;

    WorkGroupOrderTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static WorkGroupOrderTypeEnum getEnum(Integer key) {
        for (WorkGroupOrderTypeEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return DEFAULT;
    }
}
