package com.logistics.management.webapi.client.workgroup.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.workgroup.WorkGroupClient;
import com.logistics.management.webapi.client.workgroup.request.*;
import com.logistics.management.webapi.client.workgroup.response.SearchWorkGroupListResponseModel;
import com.logistics.management.webapi.client.workgroup.response.WorkGroupDetailResponseModel;
import com.logistics.management.webapi.client.workgroup.response.WorkGroupNodeResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2023/12/22 16:04
 */
@Component
public class WorkGroupClientHystrix implements WorkGroupClient {
    @Override
    public Result<PageInfo<SearchWorkGroupListResponseModel>> searchList(SearchWorkGroupListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> workGroupEnable(WorkGroupEnableRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> delWorkGroup(WorkGroupIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<WorkGroupDetailResponseModel> getDetail(WorkGroupIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addEditWorkGroup(AddEditWorkGroupRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addEditNode(AddEditWorkGroupNodeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<WorkGroupNodeResponseModel> getNodeInfo(WorkGroupIdRequestModel requestModel) {
        return Result.timeout();
    }
}
