<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCustomerBestsignTaskInfoMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCustomerBestsignTaskInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="real_name_auth_id" property="realNameAuthId" jdbcType="BIGINT" />
    <result column="bestsign_account" property="bestsignAccount" jdbcType="VARCHAR" />
    <result column="task_id" property="taskId" jdbcType="VARCHAR" />
    <result column="task_expire_time" property="taskExpireTime" jdbcType="TIMESTAMP" />
    <result column="cert_apply_status" property="certApplyStatus" jdbcType="INTEGER" />
    <result column="message" property="message" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="INTEGER" />
    <result column="apply_error_count" property="applyErrorCount" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, real_name_auth_id, bestsign_account, task_id, task_expire_time, cert_apply_status, 
    message, STATUS, apply_error_count, remark, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_customer_bestsign_task_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_customer_bestsign_task_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCustomerBestsignTaskInfo" >
    insert into t_customer_bestsign_task_info (id, real_name_auth_id, bestsign_account, 
      task_id, task_expire_time, cert_apply_status, 
      message, STATUS, apply_error_count, 
      remark, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{realNameAuthId,jdbcType=BIGINT}, #{bestsignAccount,jdbcType=VARCHAR}, 
      #{taskId,jdbcType=VARCHAR}, #{taskExpireTime,jdbcType=TIMESTAMP}, #{certApplyStatus,jdbcType=INTEGER}, 
      #{message,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{applyErrorCount,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCustomerBestsignTaskInfo" >
    insert into t_customer_bestsign_task_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="realNameAuthId != null" >
        real_name_auth_id,
      </if>
      <if test="bestsignAccount != null" >
        bestsign_account,
      </if>
      <if test="taskId != null" >
        task_id,
      </if>
      <if test="taskExpireTime != null" >
        task_expire_time,
      </if>
      <if test="certApplyStatus != null" >
        cert_apply_status,
      </if>
      <if test="message != null" >
        message,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="applyErrorCount != null" >
        apply_error_count,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="realNameAuthId != null" >
        #{realNameAuthId,jdbcType=BIGINT},
      </if>
      <if test="bestsignAccount != null" >
        #{bestsignAccount,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null" >
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="taskExpireTime != null" >
        #{taskExpireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="certApplyStatus != null" >
        #{certApplyStatus,jdbcType=INTEGER},
      </if>
      <if test="message != null" >
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="applyErrorCount != null" >
        #{applyErrorCount,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCustomerBestsignTaskInfo" >
    update t_customer_bestsign_task_info
    <set >
      <if test="realNameAuthId != null" >
        real_name_auth_id = #{realNameAuthId,jdbcType=BIGINT},
      </if>
      <if test="bestsignAccount != null" >
        bestsign_account = #{bestsignAccount,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null" >
        task_id = #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="taskExpireTime != null" >
        task_expire_time = #{taskExpireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="certApplyStatus != null" >
        cert_apply_status = #{certApplyStatus,jdbcType=INTEGER},
      </if>
      <if test="message != null" >
        message = #{message,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=INTEGER},
      </if>
      <if test="applyErrorCount != null" >
        apply_error_count = #{applyErrorCount,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCustomerBestsignTaskInfo" >
    update t_customer_bestsign_task_info
    set real_name_auth_id = #{realNameAuthId,jdbcType=BIGINT},
      bestsign_account = #{bestsignAccount,jdbcType=VARCHAR},
      task_id = #{taskId,jdbcType=VARCHAR},
      task_expire_time = #{taskExpireTime,jdbcType=TIMESTAMP},
      cert_apply_status = #{certApplyStatus,jdbcType=INTEGER},
      message = #{message,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=INTEGER},
      apply_error_count = #{applyErrorCount,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>