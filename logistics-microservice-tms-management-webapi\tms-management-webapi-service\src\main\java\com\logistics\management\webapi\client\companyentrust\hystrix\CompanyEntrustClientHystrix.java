package com.logistics.management.webapi.client.companyentrust.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.companyentrust.CompanyEntrustClient;
import com.logistics.management.webapi.client.companyentrust.request.AddOrModifyCompanyEntrustRequestModel;
import com.logistics.management.webapi.client.companyentrust.request.CompanyEntrustIdRequestModel;
import com.logistics.management.webapi.client.companyentrust.request.SearchCompanyEntrustByNameRequestModel;
import com.logistics.management.webapi.client.companyentrust.request.SearchCompanyEntrustRequestModel;
import com.logistics.management.webapi.client.companyentrust.response.CompanyEntrustDetailResponseModel;
import com.logistics.management.webapi.client.companyentrust.response.SearchCompanyEntrustByNameResponseModel;
import com.logistics.management.webapi.client.companyentrust.response.SearchCompanyEntrustResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/9/18 19:43
 */
@Component
public class CompanyEntrustClientHystrix implements CompanyEntrustClient {

    @Override
    public Result<PageInfo<SearchCompanyEntrustResponseModel>> searchList(SearchCompanyEntrustRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CompanyEntrustDetailResponseModel> getDetail(CompanyEntrustIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result saveCompany(AddOrModifyCompanyEntrustRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchCompanyEntrustByNameResponseModel>> searchCompanyEntrustByName(SearchCompanyEntrustByNameRequestModel requestModel) {
        return Result.timeout();
    }
}
