package com.logistics.management.webapi.controller.oilfilled.mapping;

import com.logistics.management.webapi.client.oilfilled.response.OilFilledOperationRecordResponseModel;
import com.logistics.management.webapi.controller.oilfilled.response.OilFilledOperationRecordResponseDto;
import com.logistics.management.webapi.base.enums.OilFilledTypeEnum;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/10/9
 * @description:
 */
public class OilFilledRecordMapping extends MapperMapping<OilFilledOperationRecordResponseModel, OilFilledOperationRecordResponseDto> {
    @Override
    public void configure() {
        OilFilledOperationRecordResponseModel source = getSource();
        OilFilledOperationRecordResponseDto destination = getDestination();
        if (source.getOilFilledType() != null) {
            destination.setOilFilledTypeLabel(OilFilledTypeEnum.getEnum(source.getOilFilledType()).getValue());
        }
        if (source.getOilFilledDate() != null) {
            destination.setOilFilledDate(DateUtils.dateToString(source.getOilFilledDate(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if (OilFilledTypeEnum.OIL_FILLED_CAR.getKey().equals(source.getOilFilledType())) {
            destination.setRewardIntegral("");
            destination.setTopUpIntegral("");
        }
        if (OilFilledTypeEnum.OIL_FILLED_CARD.getKey().equals(source.getOilFilledType())) {
            destination.setLiter("");
        }
    }
}
