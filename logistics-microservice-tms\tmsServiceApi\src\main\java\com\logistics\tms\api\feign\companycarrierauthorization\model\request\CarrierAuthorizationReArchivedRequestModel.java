package com.logistics.tms.api.feign.companycarrierauthorization.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierAuthorizationReArchivedRequestModel {

    @ApiModelProperty(value = "车主授权信息id", required = true)
    private Long carrierAuthorizationId;

    @ApiModelProperty(value = "归档文件; 为空时清空文件", required = true)
    private String archivedFilePath;

    @ApiModelProperty(value = "备注")
    private String remark;

    public String getRemark() {
        return remark == null ? "" : remark;
    }
}
