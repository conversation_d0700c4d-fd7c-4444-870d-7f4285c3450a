<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCustomerBestsignCredentialInfoMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCustomerBestsignCredentialInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="real_name_auth_id" property="realNameAuthId" jdbcType="BIGINT" />
    <result column="bestsign_account" property="bestsignAccount" jdbcType="VARCHAR" />
    <result column="cert_id" property="certId" jdbcType="VARCHAR" />
    <result column="serial_number" property="serialNumber" jdbcType="VARCHAR" />
    <result column="subject_DN" property="subjectDn" jdbcType="VARCHAR" />
    <result column="issuer_DN" property="issuerDn" jdbcType="VARCHAR" />
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
    <result column="stop_time" property="stopTime" jdbcType="TIMESTAMP" />
    <result column="revoked_time" property="revokedTime" jdbcType="TIMESTAMP" />
    <result column="revoked_reason" property="revokedReason" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, real_name_auth_id, bestsign_account, cert_id, serial_number, subject_DN, issuer_DN, 
    start_time, stop_time, revoked_time, revoked_reason, STATUS, remark, created_by, 
    created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_customer_bestsign_credential_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_customer_bestsign_credential_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCustomerBestsignCredentialInfo" >
    insert into t_customer_bestsign_credential_info (id, real_name_auth_id, bestsign_account, 
      cert_id, serial_number, subject_DN, 
      issuer_DN, start_time, stop_time, 
      revoked_time, revoked_reason, STATUS, 
      remark, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{realNameAuthId,jdbcType=BIGINT}, #{bestsignAccount,jdbcType=VARCHAR}, 
      #{certId,jdbcType=VARCHAR}, #{serialNumber,jdbcType=VARCHAR}, #{subjectDn,jdbcType=VARCHAR}, 
      #{issuerDn,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, #{stopTime,jdbcType=TIMESTAMP}, 
      #{revokedTime,jdbcType=TIMESTAMP}, #{revokedReason,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCustomerBestsignCredentialInfo" >
    insert into t_customer_bestsign_credential_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="realNameAuthId != null" >
        real_name_auth_id,
      </if>
      <if test="bestsignAccount != null" >
        bestsign_account,
      </if>
      <if test="certId != null" >
        cert_id,
      </if>
      <if test="serialNumber != null" >
        serial_number,
      </if>
      <if test="subjectDn != null" >
        subject_DN,
      </if>
      <if test="issuerDn != null" >
        issuer_DN,
      </if>
      <if test="startTime != null" >
        start_time,
      </if>
      <if test="stopTime != null" >
        stop_time,
      </if>
      <if test="revokedTime != null" >
        revoked_time,
      </if>
      <if test="revokedReason != null" >
        revoked_reason,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="realNameAuthId != null" >
        #{realNameAuthId,jdbcType=BIGINT},
      </if>
      <if test="bestsignAccount != null" >
        #{bestsignAccount,jdbcType=VARCHAR},
      </if>
      <if test="certId != null" >
        #{certId,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null" >
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="subjectDn != null" >
        #{subjectDn,jdbcType=VARCHAR},
      </if>
      <if test="issuerDn != null" >
        #{issuerDn,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stopTime != null" >
        #{stopTime,jdbcType=TIMESTAMP},
      </if>
      <if test="revokedTime != null" >
        #{revokedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="revokedReason != null" >
        #{revokedReason,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCustomerBestsignCredentialInfo" >
    update t_customer_bestsign_credential_info
    <set >
      <if test="realNameAuthId != null" >
        real_name_auth_id = #{realNameAuthId,jdbcType=BIGINT},
      </if>
      <if test="bestsignAccount != null" >
        bestsign_account = #{bestsignAccount,jdbcType=VARCHAR},
      </if>
      <if test="certId != null" >
        cert_id = #{certId,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null" >
        serial_number = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="subjectDn != null" >
        subject_DN = #{subjectDn,jdbcType=VARCHAR},
      </if>
      <if test="issuerDn != null" >
        issuer_DN = #{issuerDn,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stopTime != null" >
        stop_time = #{stopTime,jdbcType=TIMESTAMP},
      </if>
      <if test="revokedTime != null" >
        revoked_time = #{revokedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="revokedReason != null" >
        revoked_reason = #{revokedReason,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCustomerBestsignCredentialInfo" >
    update t_customer_bestsign_credential_info
    set real_name_auth_id = #{realNameAuthId,jdbcType=BIGINT},
      bestsign_account = #{bestsignAccount,jdbcType=VARCHAR},
      cert_id = #{certId,jdbcType=VARCHAR},
      serial_number = #{serialNumber,jdbcType=VARCHAR},
      subject_DN = #{subjectDn,jdbcType=VARCHAR},
      issuer_DN = #{issuerDn,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      stop_time = #{stopTime,jdbcType=TIMESTAMP},
      revoked_time = #{revokedTime,jdbcType=TIMESTAMP},
      revoked_reason = #{revokedReason,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>