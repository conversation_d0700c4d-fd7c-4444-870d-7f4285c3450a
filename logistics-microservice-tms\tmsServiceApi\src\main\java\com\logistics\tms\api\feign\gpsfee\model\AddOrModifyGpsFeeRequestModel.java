package com.logistics.tms.api.feign.gpsfee.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/10/8 10:43
 */
@Data
public class AddOrModifyGpsFeeRequestModel {
    private Long gpsFeeId;
    @ApiModelProperty("车牌号")
    private Long vehicleId;
    @ApiModelProperty("司机")
    private Long staffId;
    @ApiModelProperty("服务商")
    private String gpsServiceProvider;
    @ApiModelProperty("终端型号")
    private String terminalType;
    @ApiModelProperty("服务费")
    private BigDecimal serviceFee;
    @ApiModelProperty("合作周期（月）")
    private Integer cooperationPeriod;
    @ApiModelProperty("SIM卡号")
    private String simNumber;
    @ApiModelProperty("安装日期")
    private Date installTime;
    @ApiModelProperty("起始日期")
    private Date startDate;
    @ApiModelProperty("截止时间")
    private Date endDate;
    @ApiModelProperty("备注")
    private String remark;
}
