package com.logistics.management.webapi.controller.settlestatement.packaging.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierAddCarrierOrderListResponseDto {

    @ApiModelProperty("运单id")
    private String carrierOrderId="";

    @ApiModelProperty("运单号")
    private String carrierOrderCode="";

    @ApiModelProperty("车牌号")
    private String vehicleNo = "";

    @ApiModelProperty("车主 企业：企业名 个人：姓名+手机号 ")
    private String companyCarrierName="";

    @ApiModelProperty("结算费用")
    private String settlementCost = "";

    @ApiModelProperty("货物名称")
    private String goodsName="";

    @ApiModelProperty("提货时间")
    private String loadTime="";

    @ApiModelProperty("签收时间")
    private String signTime="";

    @ApiModelProperty("发货地 省市区+详细地址")
    private String loadAddress="";

    @ApiModelProperty("收货地 省市区+详细地址")
    private String unloadAddress="";
}
