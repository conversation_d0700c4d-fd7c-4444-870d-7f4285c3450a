package com.logistics.tms.biz.entrustaddress

import com.logistics.tms.api.feign.entrustaddress.model.GetAddressByCompanyNameRequestModel
import com.logistics.tms.api.feign.entrustaddress.model.GetAddressByCompanyNameResponseModel
import com.logistics.tms.api.feign.entrustaddress.model.SearchEntrustAddressRequestModel
import com.logistics.tms.api.feign.entrustaddress.model.SearchEntrustAddressResponseModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.mapper.TEntrustAddressMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class EntrustAddressBizTest extends Specification {
    @Mock
    TEntrustAddressMapper entrustAddressMapper
    @Mock
    CommonBiz commonBiz
    @InjectMocks
    EntrustAddressBiz entrustAddressBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "get Address By Company Name Or Warehouse where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(entrustAddressMapper.getAddressByCompanyNameOrWarehouse(any())).thenReturn([new GetAddressByCompanyNameResponseModel()])

        expect:
        entrustAddressBiz.getAddressByCompanyNameOrWarehouse(requestModel) == expectedResult

        where:
        requestModel                              || expectedResult
        new GetAddressByCompanyNameRequestModel() || [new GetAddressByCompanyNameResponseModel()]
    }

    @Unroll
    def "search List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(entrustAddressMapper.searchList(anyInt())).thenReturn([new SearchEntrustAddressResponseModel()])

        expect:
        entrustAddressBiz.searchList(requestModel) == expectedResult

        where:
        requestModel                           || expectedResult
        new SearchEntrustAddressRequestModel() || [new SearchEntrustAddressResponseModel()]
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme