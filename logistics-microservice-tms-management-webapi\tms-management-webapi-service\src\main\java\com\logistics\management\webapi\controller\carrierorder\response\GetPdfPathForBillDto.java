package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2021/9/18 9:43
 */
@Data
public class GetPdfPathForBillDto {
    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";
    @ApiModelProperty("pdf文档全路径")
    private String pdfPath = "";
    @ApiModelProperty("word文档全路径")
    private String wordPath = "";
    @ApiModelProperty("word/pdf文件名")
    private String fileName = "";
}
