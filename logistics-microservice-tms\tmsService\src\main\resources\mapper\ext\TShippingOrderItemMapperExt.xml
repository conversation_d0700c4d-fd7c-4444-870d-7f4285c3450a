<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TShippingOrderItemMapper" >
    <select id="selectByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_shipping_order_item
        where valid = 1
        <if test="param1.carrierOrderCode != null and param1.carrierOrderCode != ''">
            and instr(carrier_order_code,#{param1.carrierOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="param1.demandOrderCode != null and param1.demandOrderCode != ''">
            and instr(demand_order_code,#{param1.demandOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="param1.carrierOrderIds != null and param1.carrierOrderIds.size() != 0">
            and carrier_order_id in
            <foreach collection="param1.carrierOrderIds" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="param1.shippingOrderIds != null and param1.shippingOrderIds.size() != 0">
            and shipping_order_id in
            <foreach collection="param1.shippingOrderIds" open="(" close=")" item="item" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="param1.orderNum != null and param1.orderNum != ''">
            and order_num = #{param1.orderNum,jdbcType=VARCHAR}
        </if>
        <if test="param1.loadAddress != null and param1.loadAddress != ''">
            and
            (
                instr(load_province_name,#{param1.loadAddress,jdbcType=VARCHAR})
                or instr(load_city_name,#{param1.loadAddress,jdbcType=VARCHAR})
                or instr(load_area_name,#{param1.loadAddress,jdbcType=VARCHAR})
            )
        </if>
        <if test="param1.unloadAddress != null and param1.unloadAddress != ''">
            and
            (
                instr(unload_province_name,#{param1.unloadAddress,jdbcType=VARCHAR})
                or instr(unload_city_name,#{param1.unloadAddress,jdbcType=VARCHAR})
                or instr(unload_area_name,#{param1.unloadAddress,jdbcType=VARCHAR})
            )
        </if>


    </select>

    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TShippingOrderItem" >
        <foreach collection="recordList" item="item" separator=";">
            insert into t_shipping_order_item
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    id,
                </if>
                <if test="item.shippingOrderId != null" >
                    shipping_order_id,
                </if>
                <if test="item.carrierOrderId != null" >
                    carrier_order_id,
                </if>
                <if test="item.carrierOrderCode != null" >
                    carrier_order_code,
                </if>
                <if test="item.demandOrderId != null" >
                    demand_order_id,
                </if>
                <if test="item.demandOrderCode != null" >
                    demand_order_code,
                </if>
                <if test="item.expectAmount != null" >
                    expect_amount,
                </if>
                <if test="item.orderNum != null" >
                    order_num,
                </if>
                <if test="item.nextPointDistance != null" >
                    next_point_distance,
                </if>
                <if test="item.loadProvinceId != null" >
                    load_province_id,
                </if>
                <if test="item.loadProvinceName != null" >
                    load_province_name,
                </if>
                <if test="item.loadCityId != null" >
                    load_city_id,
                </if>
                <if test="item.loadCityName != null" >
                    load_city_name,
                </if>
                <if test="item.loadAreaId != null" >
                    load_area_id,
                </if>
                <if test="item.loadAreaName != null" >
                    load_area_name,
                </if>
                <if test="item.loadDetailAddress != null" >
                    load_detail_address,
                </if>
                <if test="item.loadWarehouse != null" >
                    load_warehouse,
                </if>
                <if test="item.loadLongitude != null" >
                    load_longitude,
                </if>
                <if test="item.loadLatitude != null" >
                    load_latitude,
                </if>
                <if test="item.consignorName != null" >
                    consignor_name,
                </if>
                <if test="item.consignorMobile != null" >
                    consignor_mobile,
                </if>
                <if test="item.unloadProvinceId != null" >
                    unload_province_id,
                </if>
                <if test="item.unloadProvinceName != null" >
                    unload_province_name,
                </if>
                <if test="item.unloadCityId != null" >
                    unload_city_id,
                </if>
                <if test="item.unloadCityName != null" >
                    unload_city_name,
                </if>
                <if test="item.unloadAreaId != null" >
                    unload_area_id,
                </if>
                <if test="item.unloadAreaName != null" >
                    unload_area_name,
                </if>
                <if test="item.unloadDetailAddress != null" >
                    unload_detail_address,
                </if>
                <if test="item.unloadWarehouse != null" >
                    unload_warehouse,
                </if>
                <if test="item.unloadLongitude != null" >
                    unload_longitude,
                </if>
                <if test="item.unloadLatitude != null" >
                    unload_latitude,
                </if>
                <if test="item.receiverName != null" >
                    receiver_name,
                </if>
                <if test="item.receiverMobile != null" >
                    receiver_mobile,
                </if>
                <if test="item.source != null" >
                    source,
                </if>
                <if test="item.createdBy != null" >
                    created_by,
                </if>
                <if test="item.createdTime != null" >
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="item.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.shippingOrderId != null" >
                    #{item.shippingOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.carrierOrderId != null" >
                    #{item.carrierOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.carrierOrderCode != null" >
                    #{item.carrierOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.demandOrderId != null" >
                    #{item.demandOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.demandOrderCode != null" >
                    #{item.demandOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.expectAmount != null" >
                    #{item.expectAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.orderNum != null" >
                    #{item.orderNum,jdbcType=INTEGER},
                </if>
                <if test="item.nextPointDistance != null" >
                    #{item.nextPointDistance,jdbcType=DECIMAL},
                </if>
                <if test="item.loadProvinceId != null" >
                    #{item.loadProvinceId,jdbcType=BIGINT},
                </if>
                <if test="item.loadProvinceName != null" >
                    #{item.loadProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.loadCityId != null" >
                    #{item.loadCityId,jdbcType=BIGINT},
                </if>
                <if test="item.loadCityName != null" >
                    #{item.loadCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.loadAreaId != null" >
                    #{item.loadAreaId,jdbcType=BIGINT},
                </if>
                <if test="item.loadAreaName != null" >
                    #{item.loadAreaName,jdbcType=VARCHAR},
                </if>
                <if test="item.loadDetailAddress != null" >
                    #{item.loadDetailAddress,jdbcType=VARCHAR},
                </if>
                <if test="item.loadWarehouse != null" >
                    #{item.loadWarehouse,jdbcType=VARCHAR},
                </if>
                <if test="item.loadLongitude != null" >
                    #{item.loadLongitude,jdbcType=VARCHAR},
                </if>
                <if test="item.loadLatitude != null" >
                    #{item.loadLatitude,jdbcType=VARCHAR},
                </if>
                <if test="item.consignorName != null" >
                    #{item.consignorName,jdbcType=VARCHAR},
                </if>
                <if test="item.consignorMobile != null" >
                    #{item.consignorMobile,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadProvinceId != null" >
                    #{item.unloadProvinceId,jdbcType=BIGINT},
                </if>
                <if test="item.unloadProvinceName != null" >
                    #{item.unloadProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadCityId != null" >
                    #{item.unloadCityId,jdbcType=BIGINT},
                </if>
                <if test="item.unloadCityName != null" >
                    #{item.unloadCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadAreaId != null" >
                    #{item.unloadAreaId,jdbcType=BIGINT},
                </if>
                <if test="item.unloadAreaName != null" >
                    #{item.unloadAreaName,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadDetailAddress != null" >
                    #{item.unloadDetailAddress,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadWarehouse != null" >
                    #{item.unloadWarehouse,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadLongitude != null" >
                    #{item.unloadLongitude,jdbcType=VARCHAR},
                </if>
                <if test="item.unloadLatitude != null" >
                    #{item.unloadLatitude,jdbcType=VARCHAR},
                </if>
                <if test="item.receiverName != null" >
                    #{item.receiverName,jdbcType=VARCHAR},
                </if>
                <if test="item.receiverMobile != null" >
                    #{item.receiverMobile,jdbcType=VARCHAR},
                </if>
                <if test="item.source != null" >
                    #{item.source,jdbcType=INTEGER},
                </if>
                <if test="item.createdBy != null" >
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <select id="getRoutePlan" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_shipping_order_item
        where shipping_order_id = #{shippingOrderId,jdbcType=BIGINT}
        and valid = 1
        and source != 2
        and order_num > 0
        order by order_num
    </select>
</mapper>