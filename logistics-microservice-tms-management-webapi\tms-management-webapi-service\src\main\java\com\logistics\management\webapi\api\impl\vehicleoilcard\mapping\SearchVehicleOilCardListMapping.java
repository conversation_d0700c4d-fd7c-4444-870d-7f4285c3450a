package com.logistics.management.webapi.api.impl.vehicleoilcard.mapping;

import com.logistics.management.webapi.api.feign.vehicleoilcard.dto.SearchVehicleOilCardListResponseDto;
import com.logistics.management.webapi.base.enums.VehicleOilCardStatusEnum;
import com.logistics.management.webapi.base.enums.VehiclePropertyEnum;
import com.logistics.tms.api.feign.vehicleoilcard.model.SearchVehicleOilCardListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2022/8/4 17:11
 */
public class SearchVehicleOilCardListMapping extends MapperMapping<SearchVehicleOilCardListResponseModel, SearchVehicleOilCardListResponseDto> {
    @Override
    public void configure() {
        SearchVehicleOilCardListResponseModel source = getSource();
        SearchVehicleOilCardListResponseDto destination = getDestination();

        destination.setStatusLabel(VehicleOilCardStatusEnum.getEnum(source.getStatus()).getValue());
        destination.setVehicleProperty(VehiclePropertyEnum.getEnum(source.getVehicleProperty()).getValue());
    }
}
