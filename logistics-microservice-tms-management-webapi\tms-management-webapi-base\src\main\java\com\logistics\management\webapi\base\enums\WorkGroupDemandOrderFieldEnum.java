package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * tms服务有对应变量,需要同步修改 WorkGroupFieldTemplate
 */
@Getter
@AllArgsConstructor
public enum WorkGroupDemandOrderFieldEnum {

    STATUS_DESC("statusDesc", "状态", "调度完成"),
    DEMAND_ORDER_CODE("demandOrderCode", "需求单号", "HR2301010001"),
    CUSTOMER_ORDER_CODE("customerOrderCode", "客户单号", "R2312270002"),
    PUBLISH_TIME("publishTime", "下单时间", "2023-01-01 00:00:00"),
    LOADING_UNLOADING_PART_LABEL("loadingUnloadingPartLabel", "装卸方式", "我司装卸"),
    RECYCLE_TASK_TYPE_LABEL("recycleTaskTypeLabel", "时效要求", "日常回收"),
    LOADING_UNLOADING_CHARGE("loadingUnloadingCharge", "装卸费用", "0.00"),
    OTHER_REQUIREMENTS("otherRequirements", "其他要求", "周末可上门"),
    COMPANY_ENTRUST_NAME("companyEntrustName", "货主", "【公司】 江苏乐橘云盘科技有限公司"),
    UPSTREAM_CUSTOMER("upstreamCustomer", "上游客户", "上游客户"),
    LOAD_WAREHOUSE("loadWarehouse", "发货仓库", "【自有仓库】"),
    LOAD_ADDRESS("loadAddress", "发货省市区", "上海市上海城区闵行区"),
    LOAD_DETAIL_ADDRESS("loadDetailAddress", "发货详细地址", "苏召路1628号"),
    CONSIGNOR("consignor", "发货联系人", "张三 18927483923"),
    UNLOAD_WAREHOUSE("unloadWarehouse", "收货仓库", "【自有仓库】"),
    UNLOAD_ADDRESS("unloadAddress", "收货省市区", "上海市上海城区闵行区"),
    UNLOAD_DETAIL_ADDRESS("unloadDetailAddress", "收货详细地址", "苏召路1628号"),
    RECEIVER("receiver", "收货联系人", "李四 ***********"),
    EXPECTED_UNLOAD_TIME("expectedUnloadTime", "期望到货时间", "2024-01-03"),
    GOODS_AMOUNT("goodsAmount", "委托", "1件"),
    ARRANGED_AMOUNT("arrangedAmount", "已安排", "1件"),
    BACK_AMOUNT("backAmount", "已退回", "0件"),
    NOT_ARRANGED_AMOUNT("notArrangedAmount", "未安排", "0件"),
    DIFFERENCE_AMOUNT("differenceAmount", "差异数", "0件"),
    ABNORMAL_AMOUNT("abnormalAmount", "云仓异常数", "0件"),
    EXPECTED_LOAD_TIME("expectedLoadTime", "期望提货时间", "2024-01-03"),
    REMARK("remark", "备注", "备注"),
    DISPATCH_VALIDITY("dispatchValidity", "调度时效", "0"),
    LOAD_REGION_NAME("loadRegionName", "大区", "上海一区"),
    LOAD_REGION_CONTACT_NAME("loadRegionContactName", "负责人", "张三 ***********"),
    GOODS_NAME("goodsName", "品名", "乐橘托盘"),
    GOODS_SIZE("goodsSize", "规格(mm)", "0*0*0mm"),
    ENTRUST_TYPE("entrustType", "需求类型", "回收入库"),
    PUBLISH_ORG_NAME("publishOrgName", "下单部门", "公司-云途"),
    COMPANY_CARRIER_NAME("companyCarrierName", "车主", "江苏乐橘云途科技有限公司"),
    PUBLISH_NAME("publishName", "委托人", "张三"),
    ROLLBACK_REMARK("rollbackRemark", "回退原因", "不可抗力-恶劣天气，自测回退"),
    ;

    private final String field;

    private final String fieldLabel;

    private final String fieldValue;
}
