package com.logistics.appapi.client.driverappoint.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class SearchDrierAppointDetailResponseModel {

    @ApiModelProperty(value = "需求单id")
    private Long demandOrderId;
    /**
     * 需求单号
     */
    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    /**
     * 业务类型：1 公司，2 个人
     */
    @ApiModelProperty("业务类型：1 公司，2 个人")
    private Integer businessType;

    /**
     * 下单总数量(KG)
     */
    @ApiModelProperty("下单总数量(KG)")
    private BigDecimal goodsAmountTotal;

    /**
     * 金额合计
     */
    @ApiModelProperty("金额合计")
    private BigDecimal goodsPriceTotal;

    /**
     * 下单时间
     */
    @ApiModelProperty("下单时间")
    private Date publishTime;

    /**
     * 乐橘新生客户名称
     */
    @ApiModelProperty("乐橘新生客户名称（企业）")
    private String customerName;
    @ApiModelProperty("乐橘新生客户姓名（个人）")
    private String customerUserName;
    @ApiModelProperty("乐橘新生客户手机号（个人）")
    private String customerUserMobile;
    /**
     * 省份名字
     */
    @ApiModelProperty("省份名字")
    private String loadProvinceName;

    /**
     * 城市名字
     */
    @ApiModelProperty("城市名字")
    private String loadCityName;

    /**
     * 县区名字
     */
    @ApiModelProperty("县区名字")
    private String loadAreaName;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    private String loadDetailAddress;

    /**
     * 发货仓库
     */
    @ApiModelProperty("发货仓库")
    private String loadWarehouse;

    /**
     * 发货人姓名
     */
    @ApiModelProperty("发货人姓名")
    private String consignorName;

    /**
     * 发货人手机号(加密)
     */
    @ApiModelProperty("发货人手机号(加密)")
    private String consignorMobile;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;

    /**
     * 是否已关联车辆：0 否，1 是
     */
    @ApiModelProperty(value = "是否已关联车辆：0 否，1 是")
    private Integer ifAssociatedVehicle;

    @ApiModelProperty("审核进度")
    private List<AuditProcessResponseModel> auditProcess = new ArrayList<>();
}
