package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum AttendanceChangeTypeEnum {

    DEFAULT(-1, ""),
    ON_DUTY_WORK_APPLY(1, "上班"),
    OFF_DUTY_WORK_APPLY(2, "下班"),
    ;

    private Integer key;
    private String value;

    public static AttendanceChangeTypeEnum getEnumByKey(Integer key) {
        return Stream.of(AttendanceChangeTypeEnum.values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
