package com.logistics.tms.biz.driversafemeeting;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.common.model.WaterMarkModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.CopyFileTypeEnum;
import com.logistics.tms.base.enums.IfValidEnum;
import com.logistics.tms.base.enums.StudyStatusEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.driversafemeeting.request.*;
import com.logistics.tms.controller.driversafemeeting.response.*;
import com.logistics.tms.entity.TDriverSafeMeeting;
import com.logistics.tms.entity.TDriverSafeMeetingRelation;
import com.logistics.tms.entity.TStaffBasic;
import com.logistics.tms.mapper.TDriverSafeMeetingMapper;
import com.logistics.tms.mapper.TDriverSafeMeetingRelationMapper;
import com.logistics.tms.mapper.TStaffBasicMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2019/11/4 10:39
 */
@Service
public class DriverSafeMeetingBiz {

    @Resource
    private TDriverSafeMeetingMapper driverSafeMeetingMapper;
    @Resource
    private TDriverSafeMeetingRelationMapper driverSafeMeetingRelationMapper;
    @Resource
    private TStaffBasicMapper staffBasicMapper;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 例会看板（列表）
     * @param requestModel
     * @return
     */
    public List<DriverSafeMeetingKanBanResponseModel> driverSafeMeetingKanBan(DriverSafeMeetingKanBanRequestModel requestModel) {
        List<DriverSafeMeetingKanBanResponseModel> list = driverSafeMeetingMapper.driverSafeMeetingKanBan(requestModel);
        if (ListUtils.isNotEmpty(list)){
            List<Long> safeMeetingIdList = list.stream().map(DriverSafeMeetingKanBanResponseModel::getSafeMeetingId).collect(Collectors.toList());
            List<DriverSafeMeetingKanBanItemResponseModel> itemList = driverSafeMeetingRelationMapper.getDriverSafeMeetingKanBanItem(StringUtils.listToString(safeMeetingIdList,','));
            Map<Long, List<DriverSafeMeetingKanBanItemResponseModel>> itemMap = itemList.stream().collect(Collectors.groupingBy(DriverSafeMeetingKanBanItemResponseModel::getSafeMeetingId));
            list.forEach(item -> item.setItemList(itemMap.get(item.getSafeMeetingId())));
        }
        return list;
    }

    /**
     * 新增例会
     * @param requestModel
     */
    @Transactional
    public void addDriverSafeMeeting(AddDriverSafeMeetingRequestModel requestModel) {
        List<TStaffBasic> driverList = staffBasicMapper.getInternalDriverByIds(StringUtils.listToString(requestModel.getDriverIdList(),','));
        if (ListUtils.isEmpty(driverList) || driverList.size() != requestModel.getDriverIdList().size()){
            throw new BizException(CarrierDataExceptionEnum.INTERNAL_DRIVER_ERROR);
        }
        int driverCount = staffBasicMapper.getInternalDriverCount();

        TDriverSafeMeeting driverSafeMeeting = new TDriverSafeMeeting();
        driverSafeMeeting.setTitle(requestModel.getTitle());
        driverSafeMeeting.setPeriod(requestModel.getPeriod());
        driverSafeMeeting.setIntroduction(requestModel.getIntroduction());
        driverSafeMeeting.setType(requestModel.getType());
        driverSafeMeeting.setContent(commonBiz.processReplaceTempPicture(requestModel.getContent(),CopyFileTypeEnum.DRIVER_SAFE_MEETING_FILE,requestModel.getPeriod()));
        driverSafeMeeting.setStaffCount(driverCount);
        commonBiz.setBaseEntityAdd(driverSafeMeeting, BaseContextHandler.getUserName());
        driverSafeMeetingMapper.insertSelective(driverSafeMeeting);

        addDriverSafeMeetingRelation(driverSafeMeeting.getId(),driverList,requestModel.getDriverIdList());
    }

    public void addDriverSafeMeetingRelation(Long safeMeetingId,List<TStaffBasic> driverList,List<Long> driverIdList){
        Map<Long,TStaffBasic> driverMap = new HashMap<>();
        driverList.stream().forEach(item -> driverMap.put(item.getId(),item));
        TDriverSafeMeetingRelation driverSafeMeetingRelation;
        List<TDriverSafeMeetingRelation> addRelationList = new ArrayList<>();
        TStaffBasic tStaffBasic;
        for (Long driverId:driverIdList) {
            tStaffBasic = driverMap.get(driverId);
            driverSafeMeetingRelation = new TDriverSafeMeetingRelation();
            driverSafeMeetingRelation.setSafeMeetingId(safeMeetingId);
            driverSafeMeetingRelation.setStaffId(tStaffBasic.getId());
            driverSafeMeetingRelation.setStaffName(tStaffBasic.getName());
            driverSafeMeetingRelation.setStaffMobile(tStaffBasic.getMobile());
            driverSafeMeetingRelation.setStaffProperty(tStaffBasic.getStaffProperty());
            commonBiz.setBaseEntityAdd(driverSafeMeetingRelation,BaseContextHandler.getUserName());
            addRelationList.add(driverSafeMeetingRelation);
        }
        driverSafeMeetingRelationMapper.batchInsert(addRelationList);
    }

    /**
     * 重新编辑例会
     * @param requestModel
     */
    @Transactional
    public void modifyDriverSafeMeeting(ModifyDriverSafeMeetingRequestModel requestModel) {
        TDriverSafeMeeting tDriverSafeMeeting = driverSafeMeetingMapper.selectByPrimaryKey(requestModel.getSafeMeetingId());
        if (tDriverSafeMeeting == null || tDriverSafeMeeting.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_MEETING_EMPTY);
        }
        if (!tDriverSafeMeeting.getPeriod().equals(DateUtils.dateToString(new Date(),CommonConstant.DATE_TO_STRING_YM_PATTERN))){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_MEETING_MODIFY_ERROR);
        }
        TDriverSafeMeeting upDriverSafeMeeting = new TDriverSafeMeeting();
        upDriverSafeMeeting.setId(tDriverSafeMeeting.getId());
        upDriverSafeMeeting.setTitle(requestModel.getTitle());
        upDriverSafeMeeting.setIntroduction(requestModel.getIntroduction());
        upDriverSafeMeeting.setContent(commonBiz.processReplaceTempPicture(requestModel.getContent(),CopyFileTypeEnum.DRIVER_SAFE_MEETING_FILE,tDriverSafeMeeting.getPeriod()));
        commonBiz.setBaseEntityModify(upDriverSafeMeeting, BaseContextHandler.getUserName());
        driverSafeMeetingMapper.updateByPrimaryKeySelective(upDriverSafeMeeting);

        List<TDriverSafeMeetingRelation> relationList = driverSafeMeetingRelationMapper.getBySafeMeetingId(tDriverSafeMeeting.getId());
        if (ListUtils.isNotEmpty(relationList)){
            TDriverSafeMeetingRelation upRelation;
            List<TDriverSafeMeetingRelation> upRelationList = new ArrayList<>();
            for (TDriverSafeMeetingRelation meetingRelation : relationList) {
                if (StudyStatusEnum.STUDY.getKey().equals(meetingRelation.getStatus())) {
                    upRelation = new TDriverSafeMeetingRelation();
                    upRelation.setId(meetingRelation.getId());
                    upRelation.setStatus(StudyStatusEnum.NOT_STUDY.getKey());
                    upRelation.setStudyTime(null);
                    upRelation.setSignTime(null);
                    upRelation.setSignLocateAddress("");
                    upRelation.setStaffDriverImageUrl("");
                    upRelation.setSignImageUrl("");
                    commonBiz.setBaseEntityModify(upRelation,BaseContextHandler.getUserName());
                    upRelationList.add(upRelation);
                }
            }
            if (ListUtils.isNotEmpty(upRelationList)) {
                driverSafeMeetingRelationMapper.batchUpdateForStudySignTimeNull(upRelationList);
            }
        }
    }

    /**
     * 补发例会
     * @param requestModel
     */
    @Transactional
    public void replacementDriverSafeMeeting(ReplacementDriverSafeMeetingRequestModel requestModel) {
        TDriverSafeMeeting tDriverSafeMeeting = driverSafeMeetingMapper.selectByPrimaryKey(requestModel.getSafeMeetingId());
        if (tDriverSafeMeeting == null || tDriverSafeMeeting.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_MEETING_EMPTY);
        }
        if (!tDriverSafeMeeting.getPeriod().equals(DateUtils.dateToString(new Date(),CommonConstant.DATE_TO_STRING_YM_PATTERN))){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_MEETING_REISSUE_ERROR);
        }
        List<Long> driverIdList = requestModel.getDriverIdList();
        List<TStaffBasic> driverList = staffBasicMapper.getInternalDriverByIds(StringUtils.listToString(driverIdList,','));
        if (ListUtils.isEmpty(driverList) || driverList.size() != driverIdList.size()){
            throw new BizException(CarrierDataExceptionEnum.INTERNAL_DRIVER_ERROR);
        }
        int driverCount = staffBasicMapper.getInternalDriverCount();
        if (driverCount > tDriverSafeMeeting.getStaffCount()){
            TDriverSafeMeeting driverSafeMeeting = new TDriverSafeMeeting();
            driverSafeMeeting.setId(tDriverSafeMeeting.getId());
            driverSafeMeeting.setStaffCount(driverCount);
            commonBiz.setBaseEntityModify(driverSafeMeeting,BaseContextHandler.getUserName());
            driverSafeMeetingMapper.updateByPrimaryKeySelective(driverSafeMeeting);
        }

        addDriverSafeMeetingRelation(requestModel.getSafeMeetingId(),driverList,driverIdList);
    }

    /**
     * 例会内容详情
     * @param requestModel
     * @return
     */
    public DriverSafeMeetingContentDetailResponseModel driverSafeMeetingContentDetail(DriverSafeMeetingIdRequestModel requestModel) {
        DriverSafeMeetingContentDetailResponseModel detail = driverSafeMeetingMapper.driverSafeMeetingContentDetail(requestModel.getSafeMeetingId());
        if (detail == null){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_MEETING_EMPTY);
        }
        if (detail != null){
            detail.setContent(commonBiz.addRealPath(detail.getContent()));
        }
        return detail;
    }

    /**
     * 学习详情（司机学习列表）
     * @param requestModel
     * @return
     */
    public PageInfo<DriverSafeMeetingDetailResponseModel> driverSafeMeetingDetailList(DriverSafeMeetingDetailRequestModel requestModel) {
        requestModel.enablePaging();
        List<DriverSafeMeetingDetailResponseModel> list = driverSafeMeetingRelationMapper.driverSafeMeetingDetailList(requestModel);
        return new PageInfo<>(list);
    }

    /**
     * 学习详情（司机学习列表统计人数）
     * @param requestModel
     * @return
     */
    public DriverSafeMeetingListCountResponseModel driverSafeMeetingListCount(DriverSafeMeetingDetailRequestModel requestModel){
        return driverSafeMeetingRelationMapper.driverSafeMeetingListCount(requestModel);
    }

    /**
     * 删除学习详情
     * @param requestModel
     */
    @Transactional
    public void delDriverSafeMeetingRelation(DriverSafeMeetingRelationIdRequestModel requestModel) {
        TDriverSafeMeetingRelation tDriverSafeMeetingRelation = driverSafeMeetingRelationMapper.selectByPrimaryKey(requestModel.getSafeMeetingRelationId());
        if (tDriverSafeMeetingRelation == null || tDriverSafeMeetingRelation.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_MEETING_EMPTY);
        }
        if (StudyStatusEnum.STUDY.getKey().equals(tDriverSafeMeetingRelation.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_MEETING_STUDY);
        }
        TDriverSafeMeetingRelation driverSafeMeetingRelation = new TDriverSafeMeetingRelation();
        driverSafeMeetingRelation.setId(tDriverSafeMeetingRelation.getId());
        driverSafeMeetingRelation.setValid(IfValidEnum.INVALID.getKey());
        commonBiz.setBaseEntityModify(driverSafeMeetingRelation,BaseContextHandler.getUserName());
        driverSafeMeetingRelationMapper.updateByPrimaryKeySelective(driverSafeMeetingRelation);
    }

    /**
     * 小程序学习列表
     * @param requestModel
     * @return
     */
    public PageInfo<AppletSafeMeetingListResponseModel> appletSafeMeetingList(AppletSafeMeetingListRequestModel requestModel) {
        requestModel.enablePaging();
        List<AppletSafeMeetingListResponseModel> list = driverSafeMeetingRelationMapper.appletSafeMeetingList(requestModel,commonBiz.getLoginDriverAppletUserId());
        return new PageInfo<>(list);
    }

    /**
     * 小程序学习列表数量统计
     * @return
     */
    public DriverSafeMeetingListCountResponseModel appletSafeMeetingListCount() {
        return driverSafeMeetingRelationMapper.appletSafeMeetingListCount(commonBiz.getLoginDriverAppletUserId());
    }

    /**
     * 小程序学习详情
     * @param requestModel
     * @return
     */
    public AppletSafeMeetingDetailResponseModel appletSafeMeetingDetail(DriverSafeMeetingRelationIdRequestModel requestModel) {
        AppletSafeMeetingDetailResponseModel detail = driverSafeMeetingRelationMapper.appletSafeMeetingDetail(requestModel.getSafeMeetingRelationId(),commonBiz.getLoginDriverAppletUserId());
        if (detail == null){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_MEETING_EMPTY);
        }
        detail.setContent(commonBiz.addRealPath(detail.getContent()));
        return detail;
    }

    /**
     * 小程序提交学习
     * @param requestModel
     */
    @Transactional
    public void appletConfirmLeaning(AppletConfirmLeaningRequestModel requestModel) {
        TDriverSafeMeetingRelation tDriverSafeMeetingRelation = driverSafeMeetingRelationMapper.selectByPrimaryKey(requestModel.getSafeMeetingRelationId());
        if (tDriverSafeMeetingRelation == null || tDriverSafeMeetingRelation.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_MEETING_EMPTY);
        }
        TDriverSafeMeeting tDriverSafeMeeting = driverSafeMeetingMapper.selectByPrimaryKey(tDriverSafeMeetingRelation.getSafeMeetingId());
        if (tDriverSafeMeeting == null || tDriverSafeMeeting.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_MEETING_EMPTY);
        }
        if (!tDriverSafeMeetingRelation.getStaffId().equals(commonBiz.getLoginDriverAppletUserId())){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_MEETING_EMPTY);
        }
        if (StudyStatusEnum.STUDY.getKey().equals(tDriverSafeMeetingRelation.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_MEETING_STUDY);
        }
        TStaffBasic tStaffBasic = staffBasicMapper.selectByPrimaryKey(tDriverSafeMeetingRelation.getStaffId());
        if (tStaffBasic == null || tStaffBasic.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_NOT_EXIST);
        }

        String signLocateAddress="";
        if (StringUtils.isNotBlank(requestModel.getLongitude()) && StringUtils.isNotBlank(requestModel.getLatitude())){
            signLocateAddress = commonBiz.getAddressByLonAndLat(requestModel.getLongitude()+","+requestModel.getLatitude());
        }
        Date now = new Date();
        WaterMarkModel waterMarkModel = new WaterMarkModel();
        waterMarkModel.setWaterMark(DateUtils.dateToString(now,DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
        waterMarkModel.setWaterMarkTwo(signLocateAddress);

        TDriverSafeMeetingRelation driverSafeMeetingRelation = new TDriverSafeMeetingRelation();
        driverSafeMeetingRelation.setId(tDriverSafeMeetingRelation.getId());
        driverSafeMeetingRelation.setStaffName(tStaffBasic.getName());
        driverSafeMeetingRelation.setStaffMobile(tStaffBasic.getMobile());
        driverSafeMeetingRelation.setStatus(StudyStatusEnum.STUDY.getKey());
        driverSafeMeetingRelation.setStaffProperty(tStaffBasic.getStaffProperty());
        driverSafeMeetingRelation.setStudyTime(now);
        driverSafeMeetingRelation.setSignTime(now);
        driverSafeMeetingRelation.setSignLocateAddress(signLocateAddress);
        driverSafeMeetingRelation.setStaffDriverImageUrl(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DRIVER_SAFE_MEETING_FILE.getKey(),tDriverSafeMeeting.getPeriod(),requestModel.getStaffDriverImageUrl(),waterMarkModel));
        driverSafeMeetingRelation.setSignImageUrl(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DRIVER_SAFE_MEETING_FILE.getKey(),tDriverSafeMeeting.getPeriod(),requestModel.getSignImageUrl(),waterMarkModel));
        commonBiz.setBaseEntityModify(driverSafeMeetingRelation,BaseContextHandler.getUserName());
        driverSafeMeetingRelationMapper.updateByPrimaryKeySelective(driverSafeMeetingRelation);
    }
}
