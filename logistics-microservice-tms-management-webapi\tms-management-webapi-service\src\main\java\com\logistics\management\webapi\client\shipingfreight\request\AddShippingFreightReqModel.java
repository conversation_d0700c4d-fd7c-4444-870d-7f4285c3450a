package com.logistics.management.webapi.client.shipingfreight.request;

import lombok.Data;

import java.util.List;

@Data
public class AddShippingFreightReqModel {



    /**
     * 运价规则名称
     */
    private String carrierPriceRuleName ;


    /**
     * 适用业务类型文本  委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨   -99其他 (集合)
     */
    private List<String> entrustType;

    /**
     * 备注
     */
    private String remark ;



}
