package com.logistics.tms.controller.leave.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class LeaveApplySearchListRequestModel extends AbstractPageForm<LeaveApplySearchListRequestModel> {

    @ApiModelProperty("申请人,姓名或手机号模糊搜索")
    private String leaveApplyUser;

    @ApiModelProperty("请假申请审核状态,审核状态: 0 待审核，1 已审核，2 已驳回，3 已撤销")
    private Integer leaveAuditStatus;

    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    @ApiModelProperty("请假申请开始时间")
    private String leaveApplyStartTime;

    @ApiModelProperty("请假申请结束时间")
    private String leaveApplyEndTime;
}
