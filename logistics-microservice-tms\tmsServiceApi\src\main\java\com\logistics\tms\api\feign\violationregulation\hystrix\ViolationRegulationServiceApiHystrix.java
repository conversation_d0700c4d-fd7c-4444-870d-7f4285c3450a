package com.logistics.tms.api.feign.violationregulation.hystrix;

import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.violationregulation.ViolationRegulationServiceApi;
import com.logistics.tms.api.feign.violationregulation.model.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/6/3 10:05
 */
@Component("tmsViolationRegulationServiceApiHystrix")
public class ViolationRegulationServiceApiHystrix implements ViolationRegulationServiceApi {
    @Override
    public Result<SearchViolationRegulationListResponseModel> searchViolationRegulationList(SearchViolationRegulationListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ViolationRegulationDetailResponseModel> getViolationRegulationDetail(ViolationRegulationDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> saveOrModifyViolationRegulation(AddOrModifyViolationRegulationRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> deleteViolationRegulation(DeleteViolationRegulationRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<ViolationRegulationListResponseModel>> export(SearchViolationRegulationListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ImportViolationRegulationResponseModel> importViolationRegulations(ImportViolationRegulationRequestModel requestModel) {
        return Result.timeout();
    }
}
