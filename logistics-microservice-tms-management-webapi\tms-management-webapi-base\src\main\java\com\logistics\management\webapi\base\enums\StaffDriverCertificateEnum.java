package com.logistics.management.webapi.base.enums;

/**
 * @author: wjf
 * @date: 2019/6/6 9:48
 */
public enum StaffDriverCertificateEnum {

    DEFAULT(0,"",0,0,"0"),
    STAFF_IDENTITY_FRONT(1,"身份证人像面",8,1,"1"),
    STAFF_IDENTITY_BACK(2,"身份证国徽面",8,2,"2"),
    DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_FRONT(3,"从业资格证（卡片）正面",9,1,"3"),
    DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_BACK(4,"从业资格证（卡片）反面",9,2,"4"),
    DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_FRONT(5,"从业资格证（纸质）正面",9,3,"5"),
    DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_BACK(6,"从业资格证（纸质）反面",9,4,"6"),
    DRIVER_LICENSE_FRONT(7,"机动车驾驶证正面",9,5,"7"),
    ;

    private Integer key;
    private String value;
    private Integer objectType;
    private Integer fileType;
    private String keyStr;

    StaffDriverCertificateEnum(Integer key, String value, Integer objectType, Integer fileType, String keyStr) {
        this.key = key;
        this.value = value;
        this.objectType = objectType;
        this.fileType = fileType;
        this.keyStr = keyStr;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public Integer getObjectType() {
        return objectType;
    }

    public Integer getFileType() {
        return fileType;
    }

    public String getKeyStr() {
        return keyStr;
    }

    public static StaffDriverCertificateEnum getEnum(Integer key) {
        for (StaffDriverCertificateEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
