package com.logistics.management.webapi.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/14 9:41
 */
@Data
public class GetVehicleSettlementDetailResponseDto {
    @ApiModelProperty("对账单事件列表")
    private List<VehicleSettlementEventDto> eventList;

    private String vehicleSettlementId="";
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty("结算状态：0 待对账，1 待发送，2 待确认，3 待处理，4 待结清，5 部分结清，6 已结清")
    private String status="";
    @ApiModelProperty("状态文本")
    private String statusLabel="";
    @ApiModelProperty("对账月份 年-月")
    private String settlementMonth="";
    @ApiModelProperty("对账月")
    private String month="";

    @ApiModelProperty("(3.17.0)包装业务运单（云盘）")
    private List<GetCarrierOrderByVehicleIdResponseDto> carrierOrderListForLeYi;
    @ApiModelProperty("(3.17.0)石化业务运单（除云盘、新生外）")
    private List<GetCarrierOrderByVehicleIdResponseDto> carrierOrderList;
    @ApiModelProperty("运单数量")
    private String carrierOrderCount="0";
    @ApiModelProperty("车辆运费合计")
    private String dispatchFreightFeeTotal="0";
    @ApiModelProperty("是否调整费用：0 否，1 是")
    private String ifAdjustFee="";
    private String ifAdjustFeeLabel="";
    @ApiModelProperty("调整费用")
    private String adjustFee="";
    @ApiModelProperty("调整原因")
    private String adjustRemark;
    @ApiModelProperty("运费合计（车辆运费+调整费用）")
    private String freightFeeTotal="0";

    @ApiModelProperty("轮胎")
    private List<GetVehicleTireByVehicleIdResponseDto> vehicleTireList;
    @ApiModelProperty("轮胎数量")
    private String vehicleTireCount="0";
    @ApiModelProperty("轮胎费用合计")
    private String tireCostTotal="0";

    @ApiModelProperty("充油")
    private List<GetOilFilledByVehicleIdResponseDto> oilFilledList;
    @ApiModelProperty("充油次数")
    private String oilFilledCount="0";
    @ApiModelProperty("充油费用合计")
    private String oilFilledFeeTotal="0";

    @ApiModelProperty("gps费用合计")
    private String gpsFeeTotal="0";
    @ApiModelProperty("gps未扣减费用合计")
    private String gpsRemainingDeductingFeeTotal="0";
    @ApiModelProperty("gps当月扣减费用")
    private String gpsDeductingFee="0";
    @ApiModelProperty("gps剩余未扣减费用")
    private String gpsRemainingDeductingFee="0";
    @ApiModelProperty("是否可以修改应扣gps费用：0 否，1 是（最后一个月不能修改为0）")
    private String ifModifyReducedGPSCosts="1";

    @ApiModelProperty("停车费用合计")
    private String parkingFeeTotal="0";
    @ApiModelProperty("停车未扣减费用合计")
    private String parkingRemainingDeductingFeeTotal="0";
    @ApiModelProperty("停车当月扣减费用")
    private String parkingDeductingFee="0";
    @ApiModelProperty("停车剩余未扣减费用")
    private String parkingRemainingDeductingFee="0";
    @ApiModelProperty("是否可以修改应扣停车费用：0 否，1 是（最后一个月不能修改为0）")
    private String ifModifyReducedParkCosts="1";

    @ApiModelProperty("保险未扣除费用")
    private List<VehicleInsuranceCostResponseDto> insuranceCostList;
    @ApiModelProperty("应扣保险费")
    private String insuranceFee="0";
    @ApiModelProperty("车辆理赔费用")
    private String vehicleClaimFee="0";
    @ApiModelProperty("保险剩余未扣减费用")
    private String insuranceRemainingDeductingFee="0";

    @ApiModelProperty("个人意外险费用合计")
    private String accidentInsuranceExpenseTotal="0";
    @ApiModelProperty("应扣个人意外险费")
    private String accidentInsuranceFee="0";
    @ApiModelProperty("个人意外险费月理赔费用")
    private String accidentInsuranceClaimFee="0";
    @ApiModelProperty("个人意外险剩余未扣减费用")
    private String accidentInsuranceRemainingDeductingFee="0";

    @ApiModelProperty("贷款费用合计")
    private String loanFeeTotal="0";
    @ApiModelProperty("未扣贷款费用合计")
    private String loanFeeRemainingDeductingFeeTotal="0";
    @ApiModelProperty("应扣贷款费用")
    private String loanFee="0";
    @ApiModelProperty("剩余未扣贷款费用")
    private String loanFeeRemainingDeductingFee="0";
    @ApiModelProperty("是否可以修改应扣贷款费用：0 否，1 是（最后一个月不能修改为0）")
    private String ifModifyReducedLoanCosts="1";

    @ApiModelProperty("理赔合计")
    private String claimTotal="";
    @ApiModelProperty("扣减费用合计")
    private String deductingFeeTotal="0";
    @ApiModelProperty("剩余未扣费用合计")
    private String remainingDeductingFeeTotal="0";
    @ApiModelProperty("月实际应付运费")
    private String actualExpensesPayable="0";

    @ApiModelProperty("附件相对路径")
    private String attachment="";
    @ApiModelProperty("附件绝对路径")
    private String attachmentPath="";
    @ApiModelProperty("备注")
    private String remark="";


    //导出pdf用
    private String settlementYear="";
}
