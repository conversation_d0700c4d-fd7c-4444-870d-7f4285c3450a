package com.logistics.tms.controller.shippingorder.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2024/8/6 10:28
 */
@Data
public class ShippingOrderReQuoteRequestModel {

    /**
     * 运输单id
     */
    private Long shippingOrderId;

    /**
     * 车长
     */
    private BigDecimal vehicleLength;

    /**
     * 串点费用
     */
    private BigDecimal crossPointFee;

    /**
     * 整车运费
     */
    private BigDecimal carrierFreight;

    private String source;//1 后台，2 前台
}
