package com.logistics.tms.controller.reserveapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class ReserveBalanceApplyDetailResponseModel {

    @ApiModelProperty("申请记录id")
    private Long applyId;

    @ApiModelProperty("状态：-1 已撤销，0 待业务审核，1 待财务审核，2 已驳回，3 待打款，4 已打款")
    private Integer status;

    @ApiModelProperty("申请单号")
    private String applyCode;

    @ApiModelProperty("申请日期")
    private Date applyDate;

    @ApiModelProperty("申请人姓名")
    private String staffName;

    @ApiModelProperty("申请人手机号")
    private String staffMobile;

    @ApiModelProperty("收款账号")
    private String receiveBankAccount;

    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("批准金额")
    private BigDecimal approveAmount;

    @ApiModelProperty("申请备注")
    private String remark;

    @ApiModelProperty("审批记录")
    private List<ReserveBalanceApplyLogModel> applyLogs;
}
