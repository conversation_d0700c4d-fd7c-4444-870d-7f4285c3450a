package com.logistics.tms.controller.vehicleassetmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date:2019/6/3 10:00
 */

@Data
public class VehicleAssetManagementListResponseModel {

    @ApiModelProperty("车主车辆关联Id")
    private Long carrierVehicleId;

    @ApiModelProperty("车辆基本信息Id")
    private Long  vehicleBasicId;

    @ApiModelProperty("车辆机构：1 自主 2 外部 3 自营")
    private Integer vehicleProperty;

    @ApiModelProperty("车辆使用性质：1 普货 2 危货")
    private Integer usageProperty;

    @ApiModelProperty("车辆类别 1 牵引车 2 挂车 3 一体车")
    private Integer vehicleCategory;

    @ApiModelProperty("车辆类型")
    private String vehicleType;

    @ApiModelProperty("装载量（可装载托盘数）")
    private Integer loadingCapacity;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("核定载质量(KG)")
    private BigDecimal approvedLoadWeight;

    @ApiModelProperty("总质量(KG)")
    private BigDecimal totalWeight;

    @ApiModelProperty("车辆识别号")
    private String vehicleIdentificationNumber;

    @ApiModelProperty("发动机号码")
    private String engineNumber;

    @ApiModelProperty("新增人")
    private String createdBy;

    @ApiModelProperty("操作人")
    private String lastModifiedBy;

    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;

    @ApiModelProperty("完整性")
    private Integer ifComplete=0;

    @ApiModelProperty("停运原因")
    private String outageInfo;

    @ApiModelProperty(value = "车辆运营状态：1 运营中，2 已停运")
    private Integer operatingState;

    //车主信息
    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;
    @ApiModelProperty("车主id")
    private Long companyCarrierId;
    @ApiModelProperty(value = "车主名称")
    private String companyCarrierName;
    @ApiModelProperty(value = "车主公司类型：1 公司，2 个人")
    private Integer companyCarrierType;
    @ApiModelProperty("车主账号名称")
    private String carrierContactName;
    @ApiModelProperty("车主账号手机号（原长度50）")
    private String carrierContactPhone;
}
