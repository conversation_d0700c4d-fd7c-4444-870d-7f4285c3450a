package com.logistics.tms.api.feign.contractorder.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 合同详情
 * @Author: sj
 * @Date: 2019/4/4 16:57
 */
@Data
public class ContractOrderDetailResponseModel {
    @ApiModelProperty("内部id")
    private Long contractId;
    @ApiModelProperty("内部合同编号")
    private String contractNoInternal;
    @ApiModelProperty("外部部合同编号")
    private String contractNoExternal;
    @ApiModelProperty("合同性质：1 货源合同，2 车源合同")
    private Integer contractNature;
    @ApiModelProperty("合同状态: 1 待执行，2 执行中，3 已终止，4 已作废")
    private Integer contractStatus;
    @ApiModelProperty("合同类型：1 框架合同，2 单次合同")
    private Integer contractType;
    @ApiModelProperty("客户(签订对象)")
    private String contractObject;
    @ApiModelProperty("客户ID(签订对象)")
    private Long contractObjectId;
    @ApiModelProperty("公司抬头")
    private String contractHeader;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("合同有效期起始时间")
    private Date contractStartTime;
    @ApiModelProperty("合同有效期结束时间")
    private Date contractEndTime;

    @ApiModelProperty("操作日志列表")
    private List<ContractLogsResponseModel> contractLogs;
    @ApiModelProperty("合同附件列表")
    private List<ContractFileResponseModel> contractFiles;
}
