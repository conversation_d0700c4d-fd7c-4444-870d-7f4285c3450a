package com.logistics.management.webapi.base.constant;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 停车费操作记录导出
 * @Author: sj
 * @Date: 2019/10/11 17:22
 */
public class ExportParkingFeeOperationRecordInfo {
    private ExportParkingFeeOperationRecordInfo(){

    }
    private static final Map<String,String> EXPORT_PARKING_FEE_OPERATION_RECORD_MAP;
    static{
        EXPORT_PARKING_FEE_OPERATION_RECORD_MAP = new LinkedHashMap<>();
        EXPORT_PARKING_FEE_OPERATION_RECORD_MAP.put("车牌号","vehicleNo");
        EXPORT_PARKING_FEE_OPERATION_RECORD_MAP.put("停车费用","parkingFeeLabel");
        EXPORT_PARKING_FEE_OPERATION_RECORD_MAP.put("合作公司","cooperationCompany");
        EXPORT_PARKING_FEE_OPERATION_RECORD_MAP.put("合作起始日期","startDate");
        EXPORT_PARKING_FEE_OPERATION_RECORD_MAP.put("合作截止日期","endDate");
        EXPORT_PARKING_FEE_OPERATION_RECORD_MAP.put("终止日期","finishDate");
        EXPORT_PARKING_FEE_OPERATION_RECORD_MAP.put("备注","remark");
        EXPORT_PARKING_FEE_OPERATION_RECORD_MAP.put("操作人","lastModifiedBy");
        EXPORT_PARKING_FEE_OPERATION_RECORD_MAP.put("操作时间","lastModifiedTime");

    }

    public static Map<String,String> getExportParkingFeeOperationRecordMap(){
        return EXPORT_PARKING_FEE_OPERATION_RECORD_MAP;
    }
}
