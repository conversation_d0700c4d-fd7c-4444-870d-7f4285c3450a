package com.logistics.tms.mapper;

import com.logistics.tms.controller.settlestatement.packaging.request.StatementWaitArchiveListRequestModel;
import com.logistics.tms.controller.settlestatement.packaging.response.*;
import com.logistics.tms.entity.TSettleStatementItem;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2022/11/10
*/
@Mapper
public interface TSettleStatementItemMapper extends BaseMapper<TSettleStatementItem> {

	void batchValuesInsert(@Param("tSettleStatementItems") List<TSettleStatementItem> tSettleStatementItems);

	void batchUpdate(@Param("tSettleStatementItems") List<TSettleStatementItem> tSettleStatementItems);

	List<StatementArchiveListResponseModel> statementArchiveList(@Param("settleStatementId") Long settleStatementId);

	StatementArchiveDetailResponseModel statementArchiveDetail(@Param("id") Long id);

	List<StatementWaitArchiveListResponseModel> statementWaitArchiveList(@Param("params") StatementWaitArchiveListRequestModel requestModel);

	List<CarrierSettleStatementItemModel> getBySettleStatementIds(@Param("settleStatementIds") String settleStatementIds);

	List<TSettleStatementItem> selectByStatementItemIds(@Param("ids") String ids, @Param("companyCarrierId") Long companyCarrierId);

	List<TSettleStatementItem> selectByStatementId(@Param("pid") Long pid, @Param("ids") String ids, @Param("companyCarrierId") Long companyCarrierId);

	TSettleStatementItem getTopBySettleStatementId(@Param("settleStatementId") Long settleStatementId);

	CarrierAssociationCarrierOrderResponseModel carrierAssociationCarrierOrderCount(@Param("settleStatementId") Long settleStatementId);

	List<CarrierAssociationCarrierOrderItemModel> carrierAssociationCarrierOrder(@Param("settleStatementId") Long settleStatementId);

	List<Long> getCarrierOrderIdBySettleStatementId(@Param("settleStatementId") Long settleStatementId);

	/**
	 * 查询当前运单有效的对账单记录
	 *
	 * @param carrierOrderId 运单id
	 * @return 有效的对账记录
	 */
	TSettleStatementItem selectValidByCarrierOrderId(Long carrierOrderId);
}