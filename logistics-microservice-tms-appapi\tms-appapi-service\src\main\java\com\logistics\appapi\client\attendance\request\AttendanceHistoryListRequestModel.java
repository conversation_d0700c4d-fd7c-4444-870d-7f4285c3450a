package com.logistics.appapi.client.attendance.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 考勤打卡历史请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/14
 */
@Data
public class AttendanceHistoryListRequestModel extends AbstractPageForm<AttendanceHistoryListRequestModel> {

	@ApiModelProperty(value = "考勤日期(月份)", required = true)
	private String attendanceDate;
}
