package com.logistics.tms.api.feign.insuarance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/6/4 19:30
 */
@Data
public class ImportInsuranceListRequestModel {
    @ApiModelProperty("险种：1 商业险，2 交强险，3 个人意外险，4 货物险，5 危货承运人险")
    private Integer insuranceType;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("司机电话")
    private String driverPhone;
    @ApiModelProperty("保险公司")
    private String insuranceCompanyName;
    @ApiModelProperty("保单号")
    private String policyNumber;
    @ApiModelProperty("批单号")
    private String batchNumber;
    @ApiModelProperty("保费")
    private BigDecimal premium;
    @ApiModelProperty("保险生效时间")
    private Date startTime;
    @ApiModelProperty("保险截止时间")
    private Date endTime;
    @ApiModelProperty("代缴车船税")
    private BigDecimal paymentOfVehicleAndVesselTax;
}
