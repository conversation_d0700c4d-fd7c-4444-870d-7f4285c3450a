gateway=gmssl.crypto.sinopec.com
# \u751F\u4EA7
# gatewayPort=445
# \u6D4B\u8BD5
gatewayPort=443
#apiIp=http://**************
#apiIp=http://**************
apiIp=http://**************
chironPort=10006
dsvsPort=10700
sniopecCertPort=10909
appId=APP_7A2043BEFFA24FA892F1013E521DB955
deviceId=DEV_F762D7DDFD764CF59E1B9CD8DE4F7D29
#\u521B\u5EFA\u4E3B\u5BC6\u94A5url
#cmkServerApi=/chiron/system/createKey
cmkServerApi=/chiron/v2/system/createKey
#\u751F\u6210\u6570\u636E\u5BC6\u94A5
#generateDataKeyServerApi=/chiron/system/generateDataKeyByPubKey
generateDataKeyServerApi=/chiron/v1/system/generateDataKeyByPubKey
#\u6570\u636E\u5BC6\u94A5\u89E3\u5BC6
#decryptDataKeyServerApi=/chiron/system/decryptDataKeyByPubKey
decryptDataKeyServerApi=/chiron/v1/system/decryptDataKeyByPubKey
#\u4E3B\u5BC6\u94A5\u52A0\u5BC6\u6570\u636E
#cmkEncryptServerApi=/chiron/system/encrypt
cmkEncryptServerApi=/chiron/v2/system/encrypt
#\u4E3B\u5BC6\u94A5\u89E3\u5BC6\u6570\u636E
#cmkDecryptServerApi=/chiron/system/decrypt
cmkDecryptServerApi=/chiron/v2/system/decrypt
#\u751F\u6210\u975E\u5BF9\u79F0\u5BC6\u94A5
#generateKeyPairServerApi=127.0.0.1
generateKeyPairServerApi=/chiron/v2/system/generateKeyPair
#\u5BC6\u94A5\u5BF9\u751F\u6210
#generateCaKeyPairServerApi=/chiron/system/generateCaEncKeyPair
generateCaKeyPairServerApi=/chiron/v1/system/generateCaEncKeyPair
#\u4F7F\u7528\u79C1\u94A5\u89E3\u5BC6\u6570\u636E
#prikeyDecryptServerApi=/chiron/system/priKeyDec
prikeyDecryptServerApi=/chiron/v2/system/priKeyDec
#\u4F7F\u7528\u79C1\u94A5\u7B7E\u540D
#prikeySignServerApi=/chiron/v1/system/signature
prikeySignServerApi=/chiron/v2/system/signature
#\u751F\u6210\u8BBE\u5907\u4E3B\u5BC6\u94A5
#generateDeviceKeyServerApi=/chiron/system/generateDeviceKeyByPubKey
generateDeviceKeyServerApi=/chiron/v1/system/generateDeviceKeyByPubKey
#\u751F\u6210\u8BBE\u5907\u5DE5\u4F5C\u5BC6\u94A5
#generateDeviceWorkKeyServerApi=/chiron/system/generateWorkKeyByPubKey
generateDeviceWorkKeyServerApi=/chiron/v1/system/generateWorkKeyByPubKey
signDataServerApi=127.0.0.1
verifyDataSignServerApi=127.0.0.1
signDigestServerApi=127.0.0.1
verifyDigestSignServerApi=127.0.0.1
verifyDigestServerApi=127.0.0.1
#p7\u7B7E\u540D
#p7OriginSignServerApi=/dsvs/pkcs7/ext/signData
p7OriginSignServerApi=/dsvs/v1/pkcs7/ext/signData
#p7\u9A8C\u7B7E
#p7OriginVerifySignServerApi=/dsvs/v1/pkcs7/verifyDataSign
p7OriginVerifySignServerApi=/dsvs/v1/pkcs7/verifyDataSign

#p7DigestSignServerApi=/dsvs/v1/pkcs7/ext/signDigest
p7DigestSignServerApi=/dsvs/v1/pkcs7/ext/signDigest

#p7DigestVerifySignServerApi=/dsvs/v1/pkcs7/verifyDigestSign
p7DigestVerifySignServerApi=/dsvs/v1/pkcs7/verifyDigestSign
#\u6570\u5B57\u4FE1\u5C01\u52A0\u5BC6
#encodeEnvelopeServerApi=/dsvs/envelope/encode
encodeEnvelopeServerApi=/dsvs/v1/envelope/encode
#\u6570\u5B57\u4FE1\u5C01\u89E3\u5BC6
decodeEnvelopeServerApi=/dsvs/v1/envelope/decode
#decodeEnvelopeServerApi=/dsvs/v1/envelope/decode
#P10\u751F\u6210
generateP10ServerApi=/v1/hostCert/generateP10
deviceCert=123456