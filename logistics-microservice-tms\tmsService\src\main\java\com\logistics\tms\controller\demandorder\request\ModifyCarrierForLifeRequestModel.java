package com.logistics.tms.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ModifyCarrierForLifeRequestModel {

    /**
     * 需求单id
     */
    @ApiModelProperty(value = "需求单ID", required = true)
    private List<String> demandOrderIds;

    /**
     * 是否我司 1:我司,2:其他车主
     */
    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主", required = true)
    private Integer isOurCompany;

    /**
     * 车主ID（其他车主时必填）
     */
    @ApiModelProperty(value = "车主ID（其他车主时必填）")
    private Long companyCarrierId;

    /**
     * 修改原因
     */
    @ApiModelProperty(value = "修改原因", required = true)
    private String modifyReason;
}