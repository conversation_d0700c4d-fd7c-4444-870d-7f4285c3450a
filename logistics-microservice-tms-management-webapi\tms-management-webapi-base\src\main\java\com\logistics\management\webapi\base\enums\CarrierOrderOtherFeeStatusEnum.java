package com.logistics.management.webapi.base.enums;

public enum CarrierOrderOtherFeeStatusEnum {

    DEFAULT(-1,""),
    WAIT_COMMIT(1, "待提交"),
    WAIT_AUDIT(2, "待审核"),
    AUDIT_REJECT(3, "已驳回"),
    AUDIT_REVOKE(4, "已撤销"),
    ALREADY_AUDIT(5, "已审核");

    CarrierOrderOtherFeeStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    private Integer key;
    private String value;

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static CarrierOrderOtherFeeStatusEnum getEnum(Integer key) {
        for (CarrierOrderOtherFeeStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
