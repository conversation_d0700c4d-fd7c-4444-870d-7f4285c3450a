package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TLoanRecords extends BaseEntity {
    /**
    * 贷款状态：0 待结算，1 部分结算，2 已结算
    */
    @ApiModelProperty("贷款状态：0 待结算，1 部分结算，2 已结算")
    private Integer status;

    /**
    * 车辆id
    */
    @ApiModelProperty("车辆id")
    private Long vehicleId;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 品牌
    */
    @ApiModelProperty("品牌")
    private String brand;

    /**
    * 型号
    */
    @ApiModelProperty("型号")
    private String model;

    /**
    * 车辆识别号
    */
    @ApiModelProperty("车辆识别号")
    private String vehicleIdentificationNumber;

    /**
    * 发动机号码
    */
    @ApiModelProperty("发动机号码")
    private String engineNumber;

    /**
    * 车身颜色
    */
    @ApiModelProperty("车身颜色")
    private String bodyColor;

    /**
    * 生产地
    */
    @ApiModelProperty("生产地")
    private String producer;

    /**
    * 生产厂商
    */
    @ApiModelProperty("生产厂商")
    private String manufacturers;

    /**
    * 生产日期
    */
    @ApiModelProperty("生产日期")
    private Date productionDate;

    /**
    * 司机id
    */
    @ApiModelProperty("司机id")
    private Long staffId;

    /**
    * 司机姓名
    */
    @ApiModelProperty("司机姓名")
    private String name;

    /**
    * 手机号
    */
    @ApiModelProperty("手机号")
    private String mobile;

    /**
    * 身份证号码
    */
    @ApiModelProperty("身份证号码")
    private String identityNumber;

    /**
    * 裸车价
    */
    @ApiModelProperty("裸车价")
    private BigDecimal nakedCarPrice;

    /**
    * 保险费
    */
    @ApiModelProperty("保险费")
    private BigDecimal insurancePremium;

    /**
    * 购置税
    */
    @ApiModelProperty("购置税")
    private BigDecimal purchaseTax;

    /**
    * 购车总价
    */
    @ApiModelProperty("购车总价")
    private BigDecimal carPrice;

    /**
    * 司机已承担费用
    */
    @ApiModelProperty("司机已承担费用")
    private BigDecimal driverExpense;

    /**
    * 总贷款费用
    */
    @ApiModelProperty("总贷款费用")
    private BigDecimal loanFee;

    /**
    * 贷款总期数
    */
    @ApiModelProperty("贷款总期数")
    private Integer loanPeriods;

    /**
    * 贷款开始时间
    */
    @ApiModelProperty("贷款开始时间")
    private Date loanStartTime;

    /**
    * 结束时间
    */
    @ApiModelProperty("结束时间")
    private Date loadFinishTime;

    /**
    * 贷款利率
    */
    @ApiModelProperty("贷款利率")
    private BigDecimal loanRate;

    /**
    * 贷款手续费
    */
    @ApiModelProperty("贷款手续费")
    private BigDecimal loanCommission;

    /**
    * 贷款利息
    */
    @ApiModelProperty("贷款利息")
    private BigDecimal loanInterest;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}