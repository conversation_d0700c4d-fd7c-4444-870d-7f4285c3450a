<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TReservationOrderMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TReservationOrder" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="reservation_order_code" property="reservationOrderCode" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="reservation_type" property="reservationType" jdbcType="INTEGER" />
    <result column="driver_id" property="driverId" jdbcType="BIGINT" />
    <result column="driver_name" property="driverName" jdbcType="VARCHAR" />
    <result column="driver_mobile" property="driverMobile" jdbcType="VARCHAR" />
    <result column="driver_identity" property="driverIdentity" jdbcType="VARCHAR" />
    <result column="vehicle_id" property="vehicleId" jdbcType="BIGINT" />
    <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR" />
    <result column="province_id" property="provinceId" jdbcType="BIGINT" />
    <result column="province_name" property="provinceName" jdbcType="VARCHAR" />
    <result column="city_id" property="cityId" jdbcType="BIGINT" />
    <result column="city_name" property="cityName" jdbcType="VARCHAR" />
    <result column="area_id" property="areaId" jdbcType="BIGINT" />
    <result column="area_name" property="areaName" jdbcType="VARCHAR" />
    <result column="detail_address" property="detailAddress" jdbcType="VARCHAR" />
    <result column="warehouse" property="warehouse" jdbcType="VARCHAR" />
    <result column="longitude" property="longitude" jdbcType="VARCHAR" />
    <result column="latitude" property="latitude" jdbcType="VARCHAR" />
    <result column="reservation_start_time" property="reservationStartTime" jdbcType="TIMESTAMP" />
    <result column="reservation_end_time" property="reservationEndTime" jdbcType="TIMESTAMP" />
    <result column="sign_date" property="signDate" jdbcType="TIMESTAMP" />
    <result column="reservation_role" property="reservationRole" jdbcType="INTEGER" />
    <result column="reservation_person" property="reservationPerson" jdbcType="VARCHAR" />
    <result column="reservation_source" property="reservationSource" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, reservation_order_code, status, reservation_type, driver_id, driver_name, driver_mobile,
    driver_identity, vehicle_id, vehicle_no, province_id, province_name, city_id, city_name,
    area_id, area_name, detail_address, warehouse, longitude, latitude, reservation_start_time,
    reservation_end_time, sign_date, reservation_role, reservation_person, reservation_source,
    created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from t_reservation_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_reservation_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TReservationOrder" >
    insert into t_reservation_order (id, reservation_order_code, status,
      reservation_type, driver_id, driver_name,
      driver_mobile, driver_identity, vehicle_id,
      vehicle_no, province_id, province_name,
      city_id, city_name, area_id,
      area_name, detail_address, warehouse,
      longitude, latitude, reservation_start_time,
      reservation_end_time, sign_date, reservation_role,
      reservation_person, reservation_source, created_by,
      created_time, last_modified_by, last_modified_time,
      valid)
    values (#{id,jdbcType=BIGINT}, #{reservationOrderCode,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
      #{reservationType,jdbcType=INTEGER}, #{driverId,jdbcType=BIGINT}, #{driverName,jdbcType=VARCHAR},
      #{driverMobile,jdbcType=VARCHAR}, #{driverIdentity,jdbcType=VARCHAR}, #{vehicleId,jdbcType=BIGINT},
      #{vehicleNo,jdbcType=VARCHAR}, #{provinceId,jdbcType=BIGINT}, #{provinceName,jdbcType=VARCHAR},
      #{cityId,jdbcType=BIGINT}, #{cityName,jdbcType=VARCHAR}, #{areaId,jdbcType=BIGINT},
      #{areaName,jdbcType=VARCHAR}, #{detailAddress,jdbcType=VARCHAR}, #{warehouse,jdbcType=VARCHAR},
      #{longitude,jdbcType=VARCHAR}, #{latitude,jdbcType=VARCHAR}, #{reservationStartTime,jdbcType=TIMESTAMP},
      #{reservationEndTime,jdbcType=TIMESTAMP}, #{signDate,jdbcType=TIMESTAMP}, #{reservationRole,jdbcType=INTEGER},
      #{reservationPerson,jdbcType=VARCHAR}, #{reservationSource,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR},
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP},
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TReservationOrder" keyProperty="id" useGeneratedKeys="true">
    insert into t_reservation_order
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="reservationOrderCode != null" >
        reservation_order_code,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="reservationType != null" >
        reservation_type,
      </if>
      <if test="driverId != null" >
        driver_id,
      </if>
      <if test="driverName != null" >
        driver_name,
      </if>
      <if test="driverMobile != null" >
        driver_mobile,
      </if>
      <if test="driverIdentity != null" >
        driver_identity,
      </if>
      <if test="vehicleId != null" >
        vehicle_id,
      </if>
      <if test="vehicleNo != null" >
        vehicle_no,
      </if>
      <if test="provinceId != null" >
        province_id,
      </if>
      <if test="provinceName != null" >
        province_name,
      </if>
      <if test="cityId != null" >
        city_id,
      </if>
      <if test="cityName != null" >
        city_name,
      </if>
      <if test="areaId != null" >
        area_id,
      </if>
      <if test="areaName != null" >
        area_name,
      </if>
      <if test="detailAddress != null" >
        detail_address,
      </if>
      <if test="warehouse != null" >
        warehouse,
      </if>
      <if test="longitude != null" >
        longitude,
      </if>
      <if test="latitude != null" >
        latitude,
      </if>
      <if test="reservationStartTime != null" >
        reservation_start_time,
      </if>
      <if test="reservationEndTime != null" >
        reservation_end_time,
      </if>
      <if test="signDate != null" >
        sign_date,
      </if>
      <if test="reservationRole != null" >
        reservation_role,
      </if>
      <if test="reservationPerson != null" >
        reservation_person,
      </if>
      <if test="reservationSource != null" >
        reservation_source,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reservationOrderCode != null" >
        #{reservationOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="reservationType != null" >
        #{reservationType,jdbcType=INTEGER},
      </if>
      <if test="driverId != null" >
        #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null" >
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null" >
        #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="driverIdentity != null" >
        #{driverIdentity,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null" >
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null" >
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="provinceId != null" >
        #{provinceId,jdbcType=BIGINT},
      </if>
      <if test="provinceName != null" >
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null" >
        #{cityId,jdbcType=BIGINT},
      </if>
      <if test="cityName != null" >
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null" >
        #{areaId,jdbcType=BIGINT},
      </if>
      <if test="areaName != null" >
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="detailAddress != null" >
        #{detailAddress,jdbcType=VARCHAR},
      </if>
      <if test="warehouse != null" >
        #{warehouse,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null" >
        #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null" >
        #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="reservationStartTime != null" >
        #{reservationStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reservationEndTime != null" >
        #{reservationEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="signDate != null" >
        #{signDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reservationRole != null" >
        #{reservationRole,jdbcType=INTEGER},
      </if>
      <if test="reservationPerson != null" >
        #{reservationPerson,jdbcType=VARCHAR},
      </if>
      <if test="reservationSource != null" >
        #{reservationSource,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TReservationOrder" >
    update t_reservation_order
    <set >
      <if test="reservationOrderCode != null" >
        reservation_order_code = #{reservationOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="reservationType != null" >
        reservation_type = #{reservationType,jdbcType=INTEGER},
      </if>
      <if test="driverId != null" >
        driver_id = #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null" >
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null" >
        driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="driverIdentity != null" >
        driver_identity = #{driverIdentity,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null" >
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null" >
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="provinceId != null" >
        province_id = #{provinceId,jdbcType=BIGINT},
      </if>
      <if test="provinceName != null" >
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null" >
        city_id = #{cityId,jdbcType=BIGINT},
      </if>
      <if test="cityName != null" >
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null" >
        area_id = #{areaId,jdbcType=BIGINT},
      </if>
      <if test="areaName != null" >
        area_name = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="detailAddress != null" >
        detail_address = #{detailAddress,jdbcType=VARCHAR},
      </if>
      <if test="warehouse != null" >
        warehouse = #{warehouse,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null" >
        longitude = #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null" >
        latitude = #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="reservationStartTime != null" >
        reservation_start_time = #{reservationStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reservationEndTime != null" >
        reservation_end_time = #{reservationEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="signDate != null" >
        sign_date = #{signDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reservationRole != null" >
        reservation_role = #{reservationRole,jdbcType=INTEGER},
      </if>
      <if test="reservationPerson != null" >
        reservation_person = #{reservationPerson,jdbcType=VARCHAR},
      </if>
      <if test="reservationSource != null" >
        reservation_source = #{reservationSource,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TReservationOrder" >
    update t_reservation_order
    set reservation_order_code = #{reservationOrderCode,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      reservation_type = #{reservationType,jdbcType=INTEGER},
      driver_id = #{driverId,jdbcType=BIGINT},
      driver_name = #{driverName,jdbcType=VARCHAR},
      driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      driver_identity = #{driverIdentity,jdbcType=VARCHAR},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      province_id = #{provinceId,jdbcType=BIGINT},
      province_name = #{provinceName,jdbcType=VARCHAR},
      city_id = #{cityId,jdbcType=BIGINT},
      city_name = #{cityName,jdbcType=VARCHAR},
      area_id = #{areaId,jdbcType=BIGINT},
      area_name = #{areaName,jdbcType=VARCHAR},
      detail_address = #{detailAddress,jdbcType=VARCHAR},
      warehouse = #{warehouse,jdbcType=VARCHAR},
      longitude = #{longitude,jdbcType=VARCHAR},
      latitude = #{latitude,jdbcType=VARCHAR},
      reservation_start_time = #{reservationStartTime,jdbcType=TIMESTAMP},
      reservation_end_time = #{reservationEndTime,jdbcType=TIMESTAMP},
      sign_date = #{signDate,jdbcType=TIMESTAMP},
      reservation_role = #{reservationRole,jdbcType=INTEGER},
      reservation_person = #{reservationPerson,jdbcType=VARCHAR},
      reservation_source = #{reservationSource,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>