package com.logistics.tms.api.feign.carrierfreight.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询车主运价列表
 *
 * <AUTHOR>
 * @date 2022/9/2 16:18
 */
@Data
public class SearchCarrierFreightRequestModel extends AbstractPageForm<SearchCarrierFreightRequestModel> {

    @ApiModelProperty("公司名称或个人姓名手机号")
    private String companyCarrierName;
}
