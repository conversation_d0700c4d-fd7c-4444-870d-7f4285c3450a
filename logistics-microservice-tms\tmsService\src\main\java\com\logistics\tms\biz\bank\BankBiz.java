package com.logistics.tms.biz.bank;

import com.github.pagehelper.PageInfo;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tray.core.exception.BizException;
import com.logistics.tms.api.feign.bank.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.EnabledEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TBank;
import com.logistics.tms.mapper.TBankMapper;
import com.yelo.tools.utils.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * @Author: sj
 * @Date: 2019/7/10 16:56
 */
@Service
public class BankBiz {
    @Autowired
    private CommonBiz commonBiz;

    @Autowired
    private TBankMapper tBankMapper;
    /**
     * 查询银行列表信息
     * @param requestModel
     * @return
     */
    public PageInfo<SearchBankResponseModel> searchBankList(SearchBankRequestModel requestModel){
        requestModel.enablePaging();
        List<SearchBankResponseModel> bankList = tBankMapper.searchBankList(requestModel);
        return new PageInfo<>(bankList == null ? new ArrayList<>():bankList);
    }

    /**
     * 新增/修改银行信息
     * @param requestModel
     */
    @Transactional
    public void saveOrModifyBank(SaveOrModifyBankRequestModel requestModel){
        TBank tBank = new TBank();
        tBank.setBankName(requestModel.getBankName());
        tBank.setBranchName(requestModel.getBranchName());
        tBank.setRemark(requestModel.getRemark());

        TBank bdBankOne = tBankMapper.findByName(requestModel.getBankName(),requestModel.getBranchName());
        if(requestModel.getBankId() == null || requestModel.getBankId() <= CommonConstant.LONG_ZERO){//新增
            if(bdBankOne!=null){
                throw new BizException(CarrierDataExceptionEnum.BANK_NAME_HAS_EXIST);
            }
            tBank.setEnabled(EnabledEnum.ENABLED.getKey());
            commonBiz.setBaseEntityAdd(tBank, BaseContextHandler.getUserName());
            tBankMapper.insertSelective(tBank);
        }else{//修改
            TBank bdBankTwo = tBankMapper.selectByPrimaryKey(requestModel.getBankId());
            if(bdBankTwo == null){
                throw new BizException(CarrierDataExceptionEnum.BANK_INFO_IS_EMPTY);
            }
            if(bdBankOne!=null && !requestModel.getBankId().equals(bdBankOne.getId())){
                throw new BizException(CarrierDataExceptionEnum.BANK_NAME_HAS_EXIST);
            }
            tBank.setId(bdBankTwo.getId());
            commonBiz.setBaseEntityModify(tBank,BaseContextHandler.getUserName());
            tBankMapper.updateByPrimaryKeySelective(tBank);
        }
    }

    /**
     * 批量导入银行信息
     * @param requestModel
     */
    @Transactional
    public ImportBankResponseModel batchImportBankInfo(ImportBankRequestModel requestModel){
        ImportBankResponseModel responseModel = new ImportBankResponseModel();
        responseModel.initNumber(requestModel.getNumberFailures());

        List<ImportBankListRequestModel> importList = requestModel.getImportList();
        if(ListUtils.isEmpty(importList)){
            return responseModel;
        }

        TBank bank;
        List<TBank> addBankList = new ArrayList<>();
        List<TBank> upBankList = new ArrayList<>();
        List<SearchBankResponseModel> bankList = tBankMapper.searchBankList(new SearchBankRequestModel());
        Map<String,Long> bankNameMap = new HashMap<>();
        if(ListUtils.isNotEmpty(bankList)){
            bankList.stream().forEach(o-> bankNameMap.put(o.getBankName()+"|"+o.getBranchName(),o.getBankId()));
        }
        for (ImportBankListRequestModel itemModel : importList) {
            bank = new TBank();
            bank.setBankName(itemModel.getBankName());
            bank.setBranchName(itemModel.getBranchName());
            bank.setEnabled(EnabledEnum.ENABLED.getKey());
            bank.setRemark(itemModel.getRemark());

            String bankMapKey = itemModel.getBankName()+"|"+itemModel.getBranchName();
            if(bankNameMap.get(bankMapKey)!=null){
                bank.setId(bankNameMap.get(bankMapKey));
                commonBiz.setBaseEntityModify(bank,BaseContextHandler.getUserName());
                upBankList.add(bank);
            }else{
                commonBiz.setBaseEntityAdd(bank,BaseContextHandler.getUserName());
                addBankList.add(bank);
            }
            responseModel.addSuccessful();
        }

        if(ListUtils.isNotEmpty(addBankList)){
            tBankMapper.batchInsert(addBankList);
        }
        if(ListUtils.isNotEmpty(upBankList)){
            tBankMapper.batchUpdate(upBankList);
        }
        return responseModel;
    }

    /**
     * 获取银行详情
     * @param requestModel
     * @return
     */
    public BankDetailResponseModel getDetail(BankDetailRequestModel requestModel){
        BankDetailResponseModel responseModel = tBankMapper.getBankDetailById(requestModel.getBankId());
        if(responseModel == null){
            throw new BizException(CarrierDataExceptionEnum.BANK_INFO_IS_EMPTY);
        }
        return responseModel;
    }

    /**
     * 启用/禁用银行
     * @param requestModel
     */
    @Transactional
    public void enableOrDisable(EnableBankRequestModel requestModel){
        TBank dbBank = tBankMapper.selectByPrimaryKey(requestModel.getBankId());
        if(dbBank == null){
            throw new BizException(CarrierDataExceptionEnum.BANK_INFO_IS_EMPTY);
        }

        if (EnabledEnum.ENABLED.getKey().equals(requestModel.getEnable())) {
            if (EnabledEnum.ENABLED.getKey().equals(dbBank.getEnabled())) {
                throw new BizException(CarrierDataExceptionEnum.BANK_ENABLE_DISABLE_ERROR);
            }
        } else if (EnabledEnum.DISABLED.getKey().equals(requestModel.getEnable())) {
            if (EnabledEnum.DISABLED.getKey().equals(dbBank.getEnabled())) {
                throw new BizException(CarrierDataExceptionEnum.BANK_ENABLE_DISABLE_ERROR);
            }
        }else{
            throw new BizException(CarrierDataExceptionEnum.ENABLE_DISABLE_PARAMS_ERROR);
        }
        TBank upBank = new TBank();
        upBank.setId(requestModel.getBankId());
        upBank.setEnabled(requestModel.getEnable());
        commonBiz.setBaseEntityModify(upBank,BaseContextHandler.getUserName());
        tBankMapper.updateByPrimaryKeySelective(upBank);
    }

    /**
     * 根据银行名称模糊查询
     * @param requestModel
     * @return
     */
    public List<FuzzyQueryBankListResponseModel> fuzzyQueryInsuranceCompany(FuzzyQueryBankRequestModel requestModel){
        return tBankMapper.queryBankListByName(requestModel);
    }
}
