package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2018/10/10 16:07
 */
@Data
public class CopyCarrierOrderByDispatchVehicleModel {
    private String carrierOrderCode;
    private String demandOrderCode;
    private BigDecimal loadAmount;
    private String remark;
    @ApiModelProperty(value = "需求类型 ")
    private Integer type;
    private List<CopyCarrierOrderOrderRelByDispatchVehicleModel> orderRelList;
    private List<UpdateDemandOrderRelModel> updateDemandOrderRelModelList;

    @ApiModelProperty(value = "SKU对应的预计出入库数量")
    private List<LoadCountAndTypeModel> loadCountAndTypeModels;
}
