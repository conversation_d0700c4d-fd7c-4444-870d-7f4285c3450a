package com.logistics.management.webapi.api.impl.attendance.mapping;

import com.logistics.management.webapi.api.feign.attendance.dto.SearchAttendanceChangeListResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.AttendanceChangeAuditStatusEnum;
import com.logistics.management.webapi.base.enums.AttendanceChangeTypeEnum;
import com.logistics.management.webapi.base.enums.StaffPropertyEnum;
import com.logistics.tms.api.feign.attendance.model.SearchAttendanceChangeListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ObjectUtils;

import java.util.Date;

public class SearchAttendanceChangeListMapping extends MapperMapping<SearchAttendanceChangeListResponseModel, SearchAttendanceChangeListResponseDto> {

    @Override
    public void configure() {

        SearchAttendanceChangeListResponseModel source = getSource();
        SearchAttendanceChangeListResponseDto destination = getDestination();

        // 审核状态转换
        destination.setAuditStatusLabel(AttendanceChangeAuditStatusEnum.getEnumByKey(source.getAuditStatus()).getValue());
        //变更类型抓换
        destination.setChangeTypeLabel(AttendanceChangeTypeEnum.getEnumByKey(source.getChangeType()).getValue());
        if (ObjectUtils.isNotEmpty(source.getAttendanceDate())) {
            // 司机名称转换
            destination.setAttendanceUser(String.format(CommonConstant.NAME_MOBILE_FORMAT, source.getStaffName(), source.getStaffMobile()));
            // 司机司机机构转换
            destination.setStaffPropertyLabel(StaffPropertyEnum.getEnum(source.getStaffProperty()).getValue());
            //考勤日期转换
            Date attendanceDate = source.getAttendanceDate();
            destination.setAttendanceMonth(DateUtils.dateToString(attendanceDate, CommonConstant.DATE_TO_STRING_YM_PATTERN_TEXT));
            destination.setAttendanceDate(DateUtils.dateToString(attendanceDate, CommonConstant.DATE_TO_STRING_YMD_PATTERN_TEXT));
        }
    }
}
