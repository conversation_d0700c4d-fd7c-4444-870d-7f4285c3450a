<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TShippingFreightAddressMapper" >

<select id="listByCondition" resultMap="BaseResultMap">
select
<include refid="Base_Column_List"/>
from t_shipping_freight_address
where valid = 1
<if test="param1.ids != null and param1.ids.size() != 0">
 and id in
 <foreach collection="param1.ids" open="(" close=")" separator="," item="item">
 #{item,jdbcType=BIGINT}
</foreach>
</if>
<if test="param1.shippingFreightIds != null and param1.shippingFreightIds.size() != 0">
 and shipping_freight_id in
 <foreach collection="param1.shippingFreightIds" open="(" close=")" separator="," item="item">
 #{item,jdbcType=BIGINT}
</foreach>
</if>

<if test="param1.loadAddress != null and param1.loadAddress != ''">
    and instr(concat(from_province_name,from_city_name,from_area_name),#{param1.loadAddress,jdbcType=VARCHAR})
</if>
<if test="param1.unloadAddress != null and param1.unloadAddress != ''">
    and instr(concat(to_province_name,to_city_name,to_area_name),#{param1.unloadAddress,jdbcType=VARCHAR})
</if>
<if test="param1.enabled != null and param1.enabled != ''">
    and enabled = #{param1.enabled,jdbcType=INTEGER}
</if>
order by id desc
</select>


    <insert id="batchInsertSelective" parameterType="com.logistics.tms.entity.TShippingFreightAddress">
        <foreach collection="list" separator=";" item="item">
            insert into t_shipping_freight_address
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.shippingFreightId != null">
                    shipping_freight_id,
                </if>
                <if test="item.fromProvinceId != null">
                    from_province_id,
                </if>
                <if test="item.fromProvinceName != null">
                    from_province_name,
                </if>
                <if test="item.fromCityId != null">
                    from_city_id,
                </if>
                <if test="item.fromCityName != null">
                    from_city_name,
                </if>
                <if test="item.fromAreaId != null">
                    from_area_id,
                </if>
                <if test="item.fromAreaName != null">
                    from_area_name,
                </if>
                <if test="item.toProvinceId != null">
                    to_province_id,
                </if>
                <if test="item.toProvinceName != null">
                    to_province_name,
                </if>
                <if test="item.toCityId != null">
                    to_city_id,
                </if>
                <if test="item.toCityName != null">
                    to_city_name,
                </if>
                <if test="item.toAreaId != null">
                    to_area_id,
                </if>
                <if test="item.toAreaName != null">
                    to_area_name,
                </if>
                <if test="item.carrierDistance != null">
                    carrier_distance,
                </if>
                <if test="item.remark != null">
                    remark,
                </if>
                <if test="item.enabled != null">
                    enabled,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.shippingFreightId != null">
                    #{item.shippingFreightId,jdbcType=BIGINT},
                </if>
                <if test="item.fromProvinceId != null">
                    #{item.fromProvinceId,jdbcType=BIGINT},
                </if>
                <if test="item.fromProvinceName != null">
                    #{item.fromProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.fromCityId != null">
                    #{item.fromCityId,jdbcType=BIGINT},
                </if>
                <if test="item.fromCityName != null">
                    #{item.fromCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.fromAreaId != null">
                    #{item.fromAreaId,jdbcType=BIGINT},
                </if>
                <if test="item.fromAreaName != null">
                    #{item.fromAreaName,jdbcType=VARCHAR},
                </if>
                <if test="item.toProvinceId != null">
                    #{item.toProvinceId,jdbcType=BIGINT},
                </if>
                <if test="item.toProvinceName != null">
                    #{item.toProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.toCityId != null">
                    #{item.toCityId,jdbcType=BIGINT},
                </if>
                <if test="item.toCityName != null">
                    #{item.toCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.toAreaId != null">
                    #{item.toAreaId,jdbcType=BIGINT},
                </if>
                <if test="item.toAreaName != null">
                    #{item.toAreaName,jdbcType=VARCHAR},
                </if>
                <if test="item.carrierDistance != null">
                    #{item.carrierDistance,jdbcType=DECIMAL},
                </if>
                <if test="item.remark != null">
                    #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null">
                    #{item.enabled,jdbcType=INTEGER},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>


    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            update t_shipping_freight_address
            <set>
                <if test="item.shippingFreightId != null">
                    shipping_freight_id = #{item.shippingFreightId,jdbcType=BIGINT},
                </if>
                <if test="item.fromProvinceId != null">
                    from_province_id = #{item.fromProvinceId,jdbcType=BIGINT},
                </if>
                <if test="item.fromProvinceName != null">
                    from_province_name = #{item.fromProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.fromCityId != null">
                    from_city_id = #{item.fromCityId,jdbcType=BIGINT},
                </if>
                <if test="item.fromCityName != null">
                    from_city_name = #{item.fromCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.fromAreaId != null">
                    from_area_id = #{item.fromAreaId,jdbcType=BIGINT},
                </if>
                <if test="item.fromAreaName != null">
                    from_area_name = #{item.fromAreaName,jdbcType=VARCHAR},
                </if>

                <if test="item.toProvinceId != null">
                    to_province_id = #{item.toProvinceId,jdbcType=BIGINT},
                </if>
                <if test="item.toProvinceName != null">
                    to_province_name = #{item.toProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.toCityId != null">
                    to_city_id = #{item.toCityId,jdbcType=BIGINT},
                </if>
                <if test="item.toCityName != null">
                    to_city_name = #{item.toCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.toAreaId != null">
                    to_area_id = #{item.toAreaId,jdbcType=BIGINT},
                </if>
                <if test="item.toAreaName != null">
                    to_area_name = #{item.toAreaName,jdbcType=VARCHAR},
                </if>

                <if test="item.carrierDistance != null">
                    carrier_distance = #{item.carrierDistance,jdbcType=DECIMAL},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null">
                    enabled = #{item.enabled,jdbcType=INTEGER},
                </if>

                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

   <select id="selectByIds" resultMap="BaseResultMap">
   select
   <include refid="Base_Column_List"/>
   from t_shipping_freight_address
   where valid = 1
    and id in
   <foreach collection="param1" open="(" close=")" item="item" separator=",">
   #{item,jdbcType=BIGINT}
</foreach>
</select>

<select id="selectByShippingFreightId" resultMap="BaseResultMap">
select
<include refid="Base_Column_List"/>
from t_shipping_freight_address
where valid = 1
and shipping_freight_id = #{shippingFreightId,jdbcType=BIGINT}
</select>

</mapper>