package com.logistics.management.webapi.api.feign.dispatchorder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/1/22 11:36
 */
@Data
public class CarrierOrderGoodsResponseDto {
    @ApiModelProperty("需求单ID")
    private String demandOrderId;
    @ApiModelProperty("需求单号")
    private String demandOrderCode="";
    @ApiModelProperty("客户单号")
    private String customerOrderCode="";
    @ApiModelProperty("运单货物id")
    private String carrierOrderGoodsId="";
    @ApiModelProperty("品名")
    private String goodsName="";
    @ApiModelProperty("规格")
    private String goodsSize="";
    @ApiModelProperty("提货地址")
    private String loadDetailAddress="";
    @ApiModelProperty("卸货地址")
    private String unloadDetailAddress="";
    @ApiModelProperty("待承运/待提件数")
    private String notArrangedAmount="";
}
