package com.logistics.tms.api.feign.entrustaddress;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.entrustaddress.hystrix.EntrustAddressServiceApiHystrix;
import com.logistics.tms.api.feign.entrustaddress.model.GetAddressByCompanyNameRequestModel;
import com.logistics.tms.api.feign.entrustaddress.model.GetAddressByCompanyNameResponseModel;
import com.logistics.tms.api.feign.entrustaddress.model.SearchEntrustAddressRequestModel;
import com.logistics.tms.api.feign.entrustaddress.model.SearchEntrustAddressResponseModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/9/19 11:49
 */
@Api(value = "API-EntrustAddressServiceApi-委托方地址管理")
@FeignClient(name = "logistics-tms-services", fallback = EntrustAddressServiceApiHystrix.class)
public interface EntrustAddressServiceApi {

    @ApiOperation(value = "根据仓库或详细地址模糊搜索带出地址（委托发布）")
    @PostMapping(value = "/service/entrustAddress/getAddressByCompanyNameOrWarehouse")
    Result<PageInfo<GetAddressByCompanyNameResponseModel>> getAddressByCompanyNameOrWarehouse(@RequestBody GetAddressByCompanyNameRequestModel requestModel);

    @ApiOperation(value = "获取委托方地址列表")
    @PostMapping(value = "/service/entrustAddress/searchList")
    Result<PageInfo<SearchEntrustAddressResponseModel>> searchList(@RequestBody SearchEntrustAddressRequestModel requestModel);

    @ApiOperation(value = "导出")
    @PostMapping(value = "/service/entrustAddress/export")
    Result<List<SearchEntrustAddressResponseModel>> export(@RequestBody SearchEntrustAddressRequestModel requestModel);


}
