<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TStaffBasicMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TStaffBasic">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="staff_property" jdbcType="INTEGER" property="staffProperty" />
    <result column="gender" jdbcType="INTEGER" property="gender" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="identity_number" jdbcType="VARCHAR" property="identityNumber" />
    <result column="age" jdbcType="INTEGER" property="age" />
    <result column="identity_validity" jdbcType="TIMESTAMP" property="identityValidity" />
    <result column="identity_is_forever" jdbcType="INTEGER" property="identityIsForever" />
    <result column="labor_contract_no" jdbcType="VARCHAR" property="laborContractNo" />
    <result column="labor_contract_valid_date" jdbcType="TIMESTAMP" property="laborContractValidDate" />
    <result column="open_status" jdbcType="INTEGER" property="openStatus" />
    <result column="close_reason" jdbcType="VARCHAR" property="closeReason" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="real_name_authentication_status" jdbcType="INTEGER" property="realNameAuthenticationStatus" />
    <result column="warehouse_switch" jdbcType="INTEGER" property="warehouseSwitch" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, type, staff_property, gender, name, mobile, identity_number, age, identity_validity, 
    identity_is_forever, labor_contract_no, labor_contract_valid_date, open_status, close_reason, 
    source, remark, real_name_authentication_status, warehouse_switch, created_by, created_time, 
    last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_staff_basic
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_staff_basic
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TStaffBasic">
    insert into t_staff_basic (id, type, staff_property, 
      gender, name, mobile, 
      identity_number, age, identity_validity, 
      identity_is_forever, labor_contract_no, labor_contract_valid_date, 
      open_status, close_reason, source, 
      remark, real_name_authentication_status, warehouse_switch, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, #{staffProperty,jdbcType=INTEGER}, 
      #{gender,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, 
      #{identityNumber,jdbcType=VARCHAR}, #{age,jdbcType=INTEGER}, #{identityValidity,jdbcType=TIMESTAMP}, 
      #{identityIsForever,jdbcType=INTEGER}, #{laborContractNo,jdbcType=VARCHAR}, #{laborContractValidDate,jdbcType=TIMESTAMP}, 
      #{openStatus,jdbcType=INTEGER}, #{closeReason,jdbcType=VARCHAR}, #{source,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{realNameAuthenticationStatus,jdbcType=INTEGER}, #{warehouseSwitch,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TStaffBasic" keyProperty="id" useGeneratedKeys="true">
    insert into t_staff_basic
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="staffProperty != null">
        staff_property,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="identityNumber != null">
        identity_number,
      </if>
      <if test="age != null">
        age,
      </if>
      <if test="identityValidity != null">
        identity_validity,
      </if>
      <if test="identityIsForever != null">
        identity_is_forever,
      </if>
      <if test="laborContractNo != null">
        labor_contract_no,
      </if>
      <if test="laborContractValidDate != null">
        labor_contract_valid_date,
      </if>
      <if test="openStatus != null">
        open_status,
      </if>
      <if test="closeReason != null">
        close_reason,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="realNameAuthenticationStatus != null">
        real_name_authentication_status,
      </if>
      <if test="warehouseSwitch != null">
        warehouse_switch,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="staffProperty != null">
        #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="identityNumber != null">
        #{identityNumber,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        #{age,jdbcType=INTEGER},
      </if>
      <if test="identityValidity != null">
        #{identityValidity,jdbcType=TIMESTAMP},
      </if>
      <if test="identityIsForever != null">
        #{identityIsForever,jdbcType=INTEGER},
      </if>
      <if test="laborContractNo != null">
        #{laborContractNo,jdbcType=VARCHAR},
      </if>
      <if test="laborContractValidDate != null">
        #{laborContractValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="openStatus != null">
        #{openStatus,jdbcType=INTEGER},
      </if>
      <if test="closeReason != null">
        #{closeReason,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="realNameAuthenticationStatus != null">
        #{realNameAuthenticationStatus,jdbcType=INTEGER},
      </if>
      <if test="warehouseSwitch != null">
        #{warehouseSwitch,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TStaffBasic">
    update t_staff_basic
    <set>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="staffProperty != null">
        staff_property = #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="identityNumber != null">
        identity_number = #{identityNumber,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        age = #{age,jdbcType=INTEGER},
      </if>
      <if test="identityValidity != null">
        identity_validity = #{identityValidity,jdbcType=TIMESTAMP},
      </if>
      <if test="identityIsForever != null">
        identity_is_forever = #{identityIsForever,jdbcType=INTEGER},
      </if>
      <if test="laborContractNo != null">
        labor_contract_no = #{laborContractNo,jdbcType=VARCHAR},
      </if>
      <if test="laborContractValidDate != null">
        labor_contract_valid_date = #{laborContractValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="openStatus != null">
        open_status = #{openStatus,jdbcType=INTEGER},
      </if>
      <if test="closeReason != null">
        close_reason = #{closeReason,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="realNameAuthenticationStatus != null">
        real_name_authentication_status = #{realNameAuthenticationStatus,jdbcType=INTEGER},
      </if>
      <if test="warehouseSwitch != null">
        warehouse_switch = #{warehouseSwitch,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TStaffBasic">
    update t_staff_basic
    set type = #{type,jdbcType=INTEGER},
      staff_property = #{staffProperty,jdbcType=INTEGER},
      gender = #{gender,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      identity_number = #{identityNumber,jdbcType=VARCHAR},
      age = #{age,jdbcType=INTEGER},
      identity_validity = #{identityValidity,jdbcType=TIMESTAMP},
      identity_is_forever = #{identityIsForever,jdbcType=INTEGER},
      labor_contract_no = #{laborContractNo,jdbcType=VARCHAR},
      labor_contract_valid_date = #{laborContractValidDate,jdbcType=TIMESTAMP},
      open_status = #{openStatus,jdbcType=INTEGER},
      close_reason = #{closeReason,jdbcType=VARCHAR},
      source = #{source,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      real_name_authentication_status = #{realNameAuthenticationStatus,jdbcType=INTEGER},
      warehouse_switch = #{warehouseSwitch,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>