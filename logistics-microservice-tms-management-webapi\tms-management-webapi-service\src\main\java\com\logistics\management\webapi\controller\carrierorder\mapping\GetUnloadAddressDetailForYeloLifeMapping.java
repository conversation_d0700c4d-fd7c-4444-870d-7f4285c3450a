package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.client.carrierorder.response.UpdateUnloadAddressDetailResponseModel;
import com.logistics.management.webapi.controller.carrierorder.response.UpdateCarrierOrderUnloadAddressDetailYeloLifeResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

/**
 * @author: wjf
 * @date: 2021/9/18 16:55
 */
public class GetUnloadAddressDetailForYeloLifeMapping extends MapperMapping<UpdateUnloadAddressDetailResponseModel, UpdateCarrierOrderUnloadAddressDetailYeloLifeResponseDto> {
    @Override
    public void configure() {
        UpdateUnloadAddressDetailResponseModel source = getSource();
        UpdateCarrierOrderUnloadAddressDetailYeloLifeResponseDto destination = getDestination();

        destination.setUnloadAddress((StringUtils.isNotEmpty(source.getUnloadWarehouse())?"【" + source.getUnloadWarehouse() + "】":"")  + " " +source.getUnloadProvinceName()+source.getUnloadCityName()+source.getUnloadAreaName()+ source.getUnloadDetailAddress());
    }
}
