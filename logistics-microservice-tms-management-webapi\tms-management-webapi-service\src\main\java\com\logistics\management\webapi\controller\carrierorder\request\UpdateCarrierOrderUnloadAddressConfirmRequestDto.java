package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: wjf
 * @date: 2021/9/17 15:14
 */
@Data
public class UpdateCarrierOrderUnloadAddressConfirmRequestDto {

    @ApiModelProperty("运单ID")
    @NotEmpty(message = "id不能为空")
    private List<String> carrierOrderIds;

    @ApiModelProperty(value = "修改原因类型：1 联系问题，2 地址问题，3 装车问题，4 等待问题", required = true)
    @NotBlank(message = "请维护修改原因")
    private String unloadAddressUpdateType;

    @ApiModelProperty(value = "云盘仓库外部id", required = true)
    @NotBlank(message = "请维护新地址")
    private String warehouseId;

    @ApiModelProperty(value = "收货省份id", required = true)
    @NotBlank(message = "请维护新地址")
    private String unloadProvinceId;

    @ApiModelProperty("收货省份名字")
    private String unloadProvinceName;

    @ApiModelProperty(value = "收货城市id", required = true)
    @NotBlank(message = "请维护新地址")
    private String unloadCityId;

    @ApiModelProperty("收货城市名字")
    private String unloadCityName;

    @ApiModelProperty(value = "收货县区id", required = true)
    @NotBlank(message = "请维护新地址")
    private String unloadAreaId;

    @ApiModelProperty("收货县区名字")
    private String unloadAreaName;

    @ApiModelProperty("收货详细地址")
    private String unloadDetailAddress;

    @ApiModelProperty("收货仓库")
    private String unloadWarehouse;

    @ApiModelProperty("收货人姓名")
    private String receiverName;

    @ApiModelProperty("收货人手机号")
    private String receiverMobile;
}
