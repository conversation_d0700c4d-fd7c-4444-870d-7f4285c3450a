package com.logistics.management.webapi.client.oilfilled.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.oilfilled.OilFilledServiceClient;
import com.logistics.management.webapi.client.oilfilled.request.*;
import com.logistics.management.webapi.client.oilfilled.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class OilFilledServiceHystrix implements OilFilledServiceClient {
    @Override
    public Result<PageInfo<OilFilledListResponseModel>> searchList(OilFilledListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result addOrModify(AddOrModifyOilFilledRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<OilFilledGetSummaryResponseModel> getSummary(OilFilledListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<OilFilledDetailResponseModel> getDetail(OilFilledDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<OilFilledOperationRecordResponseModel>> getOperationRecord(OilFilledOperationRecordRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result addOrModifyRefund(AddOrModifyOilRefundRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<OilRefundDetailResponseModel> getOilRefundDetail(OilFilledDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ImportOilFilledResponseModel> importRefuelCard(ImportOilFilledCardInfoRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ImportOilFilledResponseModel> importRefuelCar(ImportOilFilledCarInfoRequestModel requestModel) {
        return Result.timeout();
    }
}
