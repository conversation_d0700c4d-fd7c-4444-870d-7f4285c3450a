package com.logistics.management.webapi.controller.companycarrierauthorization;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.constant.ExportExcelCarrierAuthorization;
import com.logistics.management.webapi.base.constant.ExportExcelRegionInfo;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.client.companycarrierauthorization.CompanyCarrierAuthorizationServiceApi;
import com.logistics.management.webapi.client.companycarrierauthorization.request.CarrierAuthorizationReArchivedRequestModel;
import com.logistics.management.webapi.client.companycarrierauthorization.response.CarrierAuthorizationDetailResponseModel;
import com.logistics.management.webapi.client.region.request.SearchRegionRequestModel;
import com.logistics.management.webapi.client.region.response.SearchRegionResponseModel;
import com.logistics.management.webapi.controller.companycarrierauthorization.mapping.CarrierAuthorizationDetailMapping;
import com.logistics.management.webapi.controller.companycarrierauthorization.mapping.CarrierAuthorizationListMapping;
import com.logistics.management.webapi.controller.companycarrierauthorization.request.*;
import com.logistics.management.webapi.controller.companycarrierauthorization.response.CarrierAuthorizationDetailResponseDto;
import com.logistics.management.webapi.controller.companycarrierauthorization.response.CarrierAuthorizationListResponseDto;
import com.logistics.management.webapi.client.companycarrierauthorization.request.*;
import com.logistics.management.webapi.client.companycarrierauthorization.response.*;
import com.logistics.management.webapi.controller.region.mapping.SearchRegionListMapping;
import com.logistics.management.webapi.controller.region.request.SearchRegionRequestDto;
import com.logistics.management.webapi.controller.region.response.SearchRegionResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ExcelUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/3
 */
@Api(value = "车主授权管理", tags = "车主授权管理")
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/api/companyCarrierAuthorization")
public class CompanyCarrierAuthorizationController {

    private final ConfigKeyConstant configKeyConstant;

    private final CompanyCarrierAuthorizationServiceApi companyCarrierAuthorizationServiceApi;

    private final CommonBiz commonBiz;

    /**
     * 查询车主授权列表
     *
     * @param requestDto
     * @return PageInfo<CarrierAuthorizationListResponseDto>
     */
    @ApiOperation(value = "查询车主授权列表", tags = "1.2.6")
    @PostMapping(value = "/carrierAuthorizationList")
    public Result<PageInfo<CarrierAuthorizationListResponseDto>> carrierAuthorizationList(@RequestBody CarrierAuthorizationListRequestDto requestDto) {
        CarrierAuthorizationListRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierAuthorizationListRequestModel.class);
        Result<PageInfo<CarrierAuthorizationListResponseModel>> resultResponse = companyCarrierAuthorizationServiceApi.carrierAuthorizationList(requestModel);
        resultResponse.throwException();

        PageInfo pageInfo = resultResponse.getData();
        List dtoList = MapperUtils.mapper(pageInfo.getList(), CarrierAuthorizationListResponseDto.class, new CarrierAuthorizationListMapping());
        pageInfo.setList(dtoList);
        return Result.success(pageInfo);
    }

    /**
     * 导出
     * @param requestDto
     * @param response
     */
    @ApiOperation(value = "导出")
    @GetMapping(value = "/export")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void export(CarrierAuthorizationListRequestDto requestDto, HttpServletResponse response) {
        CarrierAuthorizationListRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierAuthorizationListRequestModel.class);
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<CarrierAuthorizationListResponseModel>> resultResponse = companyCarrierAuthorizationServiceApi.carrierAuthorizationList(requestModel);
        resultResponse.throwException();
        PageInfo pageInfo = resultResponse.getData();
        List dtoList = MapperUtils.mapper(pageInfo.getList(), CarrierAuthorizationListResponseDto.class, new CarrierAuthorizationListMapping());
        String fileName = "车主授权列表" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String, String> exportTypeMap = ExportExcelCarrierAuthorization.getExportExcelCarrierAuthorization();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportTypeMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return dtoList;
            }
        });
    }


    /**
     * 查询车主授权详情
     *
     * @param requestDto
     * @return CarrierAuthorizationDetailResponseDto
     */
    @ApiOperation(value = "车主授权详情", tags = "1.3.6")
    @PostMapping(value = "/carrierAuthorizationDetail")
    public Result<CarrierAuthorizationDetailResponseDto> carrierAuthorizationDetail(@Valid @RequestBody CarrierAuthorizationDetailRequestDto requestDto) {
        CarrierAuthorizationDetailRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierAuthorizationDetailRequestModel.class);
        Result<CarrierAuthorizationDetailResponseModel> responseModelResult = companyCarrierAuthorizationServiceApi.carrierAuthorizationDetail(requestModel);
        responseModelResult.throwException();

        CarrierAuthorizationDetailResponseModel responseModel = responseModelResult.getData();
        // 获取图片访问路径
        Map<String, String> imageMap = Optional.ofNullable(responseModel.getAuthorizationImageList())
                .map(commonBiz::batchGetOSSFileUrl)
                .orElse(new HashMap<>());
        CarrierAuthorizationDetailResponseDto dto = MapperUtils.mapper(responseModel, CarrierAuthorizationDetailResponseDto.class,
                new CarrierAuthorizationDetailMapping(configKeyConstant.fileAccessAddress, imageMap));
        //获取归档文件
        CarrierAuthorizationDetailResponseDto.AuthorizationImage temp = new CarrierAuthorizationDetailResponseDto.AuthorizationImage();
        Optional.ofNullable(responseModel.getArchivedFilePath()).ifPresentOrElse(e -> {
            CarrierAuthorizationDetailResponseDto.AuthorizationImage authorizationImage = Optional.ofNullable(commonBiz.getImageURL(e)).map(i -> {
                temp.setSrc(configKeyConstant.fileAccessAddress + i);
                temp.setRelativePath(responseModel.getArchivedFilePath());
                return temp;
            }).orElse(temp);
            dto.setArchivedFile(authorizationImage);
        }, () -> {
            dto.setArchivedFile(temp);
        });
        return Result.success(dto);
    }

    /**
     * 新增车主授权信息
     *
     * @param requestDto
     * @return Boolean
     */
    @ApiOperation(value = "新增车主授权信息", tags = "1.2.6")
    @PostMapping(value = "/carrierAuthorizationAdd")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> carrierAuthorizationAdd(@Valid @RequestBody CarrierAuthorizationAddRequestDto requestDto) {
        CarrierAuthorizationAddRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierAuthorizationAddRequestModel.class);
        return companyCarrierAuthorizationServiceApi.carrierAuthorizationAdd(requestModel);
    }

    /**
     * 审核车主授权信息
     *
     * @param requestDto
     * @return Boolean
     */
    @ApiOperation(value = "审核车主授权信息", tags = "1.2.6")
    @PostMapping(value = "/carrierAuthorizationAudit")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> carrierAuthorizationAudit(@Valid @RequestBody CarrierAuthorizationAuditRequestDto requestDto) {
        if (CommonConstant.INTEGER_TWO.toString().equals(requestDto.getAuditModel())) {
            if (StringUtils.isBlank(requestDto.getRemark())) {
                throw new BizException(ManagementWebApiExceptionEnum.REMARKS_VERIFICATION_MESSAGE);
            }
        }
        CarrierAuthorizationAuditRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierAuthorizationAuditRequestModel.class);
        return companyCarrierAuthorizationServiceApi.carrierAuthorizationAudit(requestModel);
    }

    /**
     * 车主授权信息归档
     *
     * @param requestDto
     * @return Boolean
     */
    @ApiOperation(value = "车主授权信息归档", tags = "1.3.6")
    @PostMapping(value = "/carrierAuthorizationArchived")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> carrierAuthorizationArchived(@Valid @RequestBody CarrierAuthorizationArchivedRequestDto requestDto) {
        CarrierAuthorizationArchivedRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierAuthorizationArchivedRequestModel.class);
        return companyCarrierAuthorizationServiceApi.carrierAuthorizationArchived(requestModel);
    }

    /**
     * 车主授权信息重新归档
     *
     * @param requestDto
     * @return Boolean
     */
    @ApiOperation(value = "车主授权信息重新归档", tags = "1.3.6")
    @PostMapping(value = "/carrierAuthorizationReArchive")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> carrierAuthorizationReArchive(@Valid @RequestBody CarrierAuthorizationReArchivedRequestDto requestDto) {
        CarrierAuthorizationReArchivedRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierAuthorizationReArchivedRequestModel.class);
        return companyCarrierAuthorizationServiceApi.carrierAuthorizationReArchive(requestModel);
    }
}
