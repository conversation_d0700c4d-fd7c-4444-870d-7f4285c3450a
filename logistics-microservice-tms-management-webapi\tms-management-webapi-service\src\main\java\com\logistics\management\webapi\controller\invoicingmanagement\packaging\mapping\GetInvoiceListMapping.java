package com.logistics.management.webapi.controller.invoicingmanagement.packaging.mapping;

import com.logistics.management.webapi.client.invoicingmanagement.response.GetInvoiceListResponseModel;
import com.logistics.management.webapi.controller.invoicingmanagement.packaging.response.GetInvoiceListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * <AUTHOR>
 * @date 2024/04/03
 */
public class GetInvoiceListMapping extends MapperMapping<GetInvoiceListResponseModel, GetInvoiceListResponseDto> {
    @Override
    public void configure() {
        GetInvoiceListResponseDto destination = getDestination();
        GetInvoiceListResponseModel source = getSource();
        if(source!=null&&null!=source.getInvoiceDate()){
            destination.setInvoiceDate(DateUtils.dateToString(source.getInvoiceDate(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
    }
}
