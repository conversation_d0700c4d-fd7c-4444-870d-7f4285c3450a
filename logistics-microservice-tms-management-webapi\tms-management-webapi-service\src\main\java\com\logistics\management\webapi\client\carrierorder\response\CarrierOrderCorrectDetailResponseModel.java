package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2021/9/17 17:01
 */
@Data
public class CarrierOrderCorrectDetailResponseModel {

    @ApiModelProperty("入库数量")
    private BigDecimal stockInCount;

    @ApiModelProperty("入库备注信息")
    private String stockInRemark;

    @ApiModelProperty("入库单")
    private List<String> stockInTicketsList;

    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("原数据-预提")
    private BigDecimal expectAmount;

    @ApiModelProperty("原数据-实提")
    private BigDecimal loadAmountExpect;

    @ApiModelProperty("原数据-实卸")
    private BigDecimal unloadAmountExpect;

    @ApiModelProperty("实际卸货数")
    private BigDecimal unloadAmount;

    @ApiModelProperty("实际实提数量")
    private BigDecimal loadAmount;

    @ApiModelProperty("纠错原因类型: 1 系统数量错误，2  实物损耗，3 提错托盘  4 仓库入错")
    private Integer correctType;

    @ApiModelProperty("纠错操作人")
    private String correctUser;

    @ApiModelProperty("纠错操作时间")
    private Date correctTime;

    @ApiModelProperty("车主id ")
    private Long companyCarrierId;

    @ApiModelProperty("车主运费类型：1 单价，2 一口价")
    private Integer carrierFreightType;

    @ApiModelProperty("车主运费(元)")
    private BigDecimal carrierFreight;

    @ApiModelProperty("车主结算数量")
    private BigDecimal carrierSettlementAmount;

    @ApiModelProperty("临时费用(元)")
    private BigDecimal otherFee;

    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;

    @ApiModelProperty("提错托盘数量")
    private BigDecimal loadErrorAmount;

    @ApiModelProperty("遗失托盘数量")
    private BigDecimal loseErrorAmount;

    @ApiModelProperty("运单回单路径")
    private List<String> signTicketsList;

    //纠错状态：0 待纠错，1 已纠错，2 无需纠错
    @ApiModelProperty("纠错状态：0 待纠错，1 已纠错，2 无需纠错")
    private Integer correctStatus;

    @ApiModelProperty("提货区id")
    private Long loadAreaId;

    @ApiModelProperty("卸货区id")
    private Long unloadAreaId;

    @ApiModelProperty("卸货数量")
    private BigDecimal goodsAmount;

    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;

    @ApiModelProperty("是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;

    @ApiModelProperty("云仓异常数")
    private BigDecimal abnormalAmount;

    @ApiModelProperty("车主对账单状态, -2 未关联对账，-1 待提交，0 待业务审核，1 待财务审核，2 已对账，3 已驳回")
    private Integer carrierSettleStatementStatus;

    @ApiModelProperty("外部车辆结算支付状态：0 未支付，1 已支付")
    private Integer extVehiclePayStatus;

    @ApiModelProperty("是否关联自主运费结算")
    private boolean relDriverSettlement;

    @ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer carrierSettlement;

    @ApiModelProperty("运单预计里程数")
    private BigDecimal expectMileage;
}
