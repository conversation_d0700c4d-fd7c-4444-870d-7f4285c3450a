package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @author: wjf
 * @date: 2021/9/17 17:23
 */
@Data
public class CarrierOrderCorrectConfirmRequestDto {

    @ApiModelProperty(value = "运单ID",required = true)
    @NotBlank(message = "id不能为空")
    private String carrierOrderId;

    @ApiModelProperty(value = "实际实提数量",required = true)
    @NotBlank(message = "请维护实际实提数量")
    private String loadAmount;

    @ApiModelProperty(value = "纠错原因类型: 1 系统数量错误，2  实物损耗，3 提错托盘  4 仓库入错",required = true)
    @NotBlank(message = "请维护纠错原因")
    private String correctType;

    @ApiModelProperty(value = "车主运费类型：1 单价，2 一口价")
    private String carrierFreightType;

    @ApiModelProperty(value = "车主运费(元)")
    private String carrierFreight;

    @ApiModelProperty("提错托盘数量")
    private String loadErrorAmount;

    @ApiModelProperty("遗失托盘数量")
    private String loseErrorAmount;

    @ApiModelProperty(value = "运单回单路径")
    private List<String> signTicketsList;
}
