package com.logistics.appapi.controller.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class RenewableOrderGoodRequestDto {

	@ApiModelProperty(value = "sku编码", required = true)
	@NotBlank(message = "sku编码不能为空")
	private String skuCode;

	@ApiModelProperty(value = "货物名(sku名)", required = true)
	@NotBlank(message = "货物名不能为空")
	private String goodsName;

	@ApiModelProperty(value = "确认货物数量(Kg) 1<=数量<100000Kg", required = true)
	@NotBlank(message = "确认货物数量不能为空")
	@DecimalMin(value = "1.00", message = "货物数量要大于等于1Kg,小于100000Kg")
	@DecimalMax(value = "100000", inclusive = false, message = "货物数量要大于等于1Kg,小于100000Kg")
	private String goodsAmount;

	@ApiModelProperty(value = "收货单价 1<=收货单价<99999", required = true)
	@NotBlank(message = "收货单价不能为空")
	@DecimalMin(value = "1.00", message = "收货单价要大于等于1，小于99999")
	@DecimalMax(value = "99999", inclusive = false, message = "收货单价要大于等于1，小于99999")
	private String goodsPrice;
}
