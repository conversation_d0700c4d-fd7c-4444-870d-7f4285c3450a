package com.logistics.management.webapi.client.oilfilled.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ImportOilFilledCarListRequestModel {

    @ApiModelProperty(value = "合作公司")
    private String cooperationCompany;
    @ApiModelProperty(value = "升数")
    private Integer liter;
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;
    @ApiModelProperty(value = "司机")
    private String driverName;
    @ApiModelProperty(value = "司机手机号")
    private String driverPhone;
    @ApiModelProperty(value = "总金额")
    private BigDecimal totalAmount;
    @ApiModelProperty(value = "加油时间")
    private Date oilFilledDate;
    @ApiModelProperty(value = "备注")
    private String remark;

}
