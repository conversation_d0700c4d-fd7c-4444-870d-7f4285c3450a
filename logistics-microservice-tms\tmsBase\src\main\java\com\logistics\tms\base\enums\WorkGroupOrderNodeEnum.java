package com.logistics.tms.base.enums;

import lombok.Getter;

/**
 * 业务节点, 101:下单 102:调度 103:取消；201:提货 202:纠错 203:取消 204:生成运单 205:修改车辆 206:卸货 207:到达提货地 208:到达卸货地
 */
@Getter
public enum WorkGroupOrderNodeEnum {
    DEFAULT(0, ""),
    DEMAND_ORDER_CREATE(101, "下单"),
    DEMAND_ORDER_DISPATCH(102, "调度"),
    DEMAND_ORDER_CANCEL(103, "取消"),

    CARRIER_ORDER_LOAD(201, "提货"),
    CARRIER_ORDER_CORRECTION(202, "纠错"),
    CARRIER_ORDER_CANCEL(203, "取消"),
    CARRIER_ORDER_CREATE(204, "生成运单"),
    CARRIER_ORDER_UPDATE_VEHICLE(205, "修改车辆"),
    CARRIER_ORDER_UNLOAD(206, "卸货"),
    CARRIER_ORDER_REACH_LOAD_ADDRESS(207, "到达提货地"),
    CARRIER_ORDER_REACH_UNLOAD_ADDRESS(208, "到达卸货地"),
    ;

    private final Integer key;
    private final String value;

    WorkGroupOrderNodeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static WorkGroupOrderNodeEnum getEnum(Integer key) {
        for (WorkGroupOrderNodeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
