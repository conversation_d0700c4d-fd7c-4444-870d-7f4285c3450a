package com.logistics.appapi.client.homepage.hystrix;

import com.logistics.appapi.client.homepage.HomePageClient;
import com.logistics.appapi.client.homepage.response.HomeOrderCollectResponseModel;
import com.logistics.appapi.client.homepage.response.VerifyWarehousePermissionResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/3/7 14:33
 */
@Component
public class HomePageClientHystrix implements HomePageClient {
    @Override
    public Result<HomeOrderCollectResponseModel> homeOrderCollect() {
        return Result.timeout();
    }

    @Override
    public Result<VerifyWarehousePermissionResponseModel> verifyWarehousePermission() {
        return Result.timeout();
    }
}
