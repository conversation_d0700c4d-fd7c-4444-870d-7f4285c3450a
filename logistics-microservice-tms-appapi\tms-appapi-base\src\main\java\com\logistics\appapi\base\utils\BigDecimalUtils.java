package com.logistics.appapi.base.utils;

import com.logistics.appapi.base.constant.CommonConstant;

import java.math.BigDecimal;
import java.util.Objects;

public class BigDecimalUtils {

    public static String conversion(BigDecimal amount) {
        if (Objects.isNull(amount)) {
            return CommonConstant.BLANK_TEXT;
        }
        return amount.stripTrailingZeros().toPlainString();
    }
}
