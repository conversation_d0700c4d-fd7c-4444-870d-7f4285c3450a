<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TVehicleSafeCheckMapper">
    <select id="getCountBySpecifyPeriod" resultType="java.lang.Integer">
        select
        count(*)
        from t_vehicle_safe_check
        where valid = 1
        and period = #{period,jdbcType=VARCHAR}
    </select>

    <select id="getBySpecifyPeriod" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_vehicle_safe_check
        where valid = 1
        and period = #{period,jdbcType=VARCHAR}
    </select>
</mapper>