package com.logistics.tms.controller.carrierorderotherfee.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/1/19 9:43
 */
@Data
public class GetOtherFeeItemByCarrierOrderIdResponseModel {
    @ApiModelProperty(value = "主表id")
    private Long carrierOrderOtherFeeItemId;

    @ApiModelProperty(value = "费用类型：1 短驳费，2 保管费，3 装卸费，4 压车费，5 质量处罚费，6 其他杂费")
    private Integer feeType;

    @ApiModelProperty(value = "金额 :0<费用<=10000元")
    private BigDecimal feeAmount;

    @ApiModelProperty("单据-路径")
    private List<String> billsPicture =  new ArrayList<>();
}
