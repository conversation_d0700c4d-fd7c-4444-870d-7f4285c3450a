package com.logistics.management.webapi.api.feign.forthirdparty;

import com.logistics.management.webapi.api.feign.forthirdparty.dto.request.WorkOrderProcessSyncRequestDto;
import com.logistics.management.webapi.api.feign.forthirdparty.hystrix.ForThirdPartyApiHystrix;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@Api(value = "API-ForThirdPartyApi", tags = "供第三方调用API")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = ForThirdPartyApiHystrix.class)
public interface ForThirdPartyApi {

    @ApiOperation(value = "同步异常工单处理流程 - 智慧运营(任务中心)调用")
    @PostMapping(value = "/api/workOrderCenter/syncWorkOrderProcess")
    Result<Boolean> syncWorkOrderProcess(@RequestBody @Valid WorkOrderProcessSyncRequestDto requestDto);
}
