/**
 * Created by yun.zhou on 2017/12/12.
 */
package com.logistics.management.webapi.base.enums;

public enum SettlementTonnageLabelEnum {
    DEFAULT(0, ""),
    LOAD(1, "按照实际提货吨位"),
    UNLOAD(2, "按照实际卸货吨位"),
    SIGN(3,"按照实际签收吨位"),
    EXPECT(4,"按照实际委托吨位"),
    ;

    private Integer key;
    private String value;

    SettlementTonnageLabelEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static SettlementTonnageLabelEnum getEnum(Integer key) {
        for (SettlementTonnageLabelEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
