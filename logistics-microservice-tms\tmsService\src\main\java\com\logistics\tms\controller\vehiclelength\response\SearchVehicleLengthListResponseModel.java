package com.logistics.tms.controller.vehiclelength.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SearchVehicleLengthListResponseModel {

    /**
     * 车长配置id
     */
    @ApiModelProperty("车长配置id")
    private Long vehicleLengthId;

    /**
     * 车长
     */
    @ApiModelProperty("车长")
    private BigDecimal vehicleLength;

    /**
     * 承运范围-低
     */
    @ApiModelProperty("承运范围-低")
    private BigDecimal carriageScopeMin;

    /**
     * 承运范围-高
     */
    @ApiModelProperty("承运范围-高")
    private BigDecimal carriageScopeMax;

    /**
     * 最后操作人
     */
    @ApiModelProperty("最后操作人")
    private String lastModifiedBy;

    /**
     * 最后操作时间
     */
    @ApiModelProperty("最后操作时间")
    private Date lastModifiedTime;
}
