package com.logistics.management.webapi.controller.settlestatement.tradition.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2022/11/15 11:45
 */
@Data
public class TraditionStatementArchiveDetailRequestDto {
    @ApiModelProperty(value = "对账单详情item id", required = true)
    @NotBlank(message = "请选择要查看的记录")
    private String settleStatementItemId;
}
