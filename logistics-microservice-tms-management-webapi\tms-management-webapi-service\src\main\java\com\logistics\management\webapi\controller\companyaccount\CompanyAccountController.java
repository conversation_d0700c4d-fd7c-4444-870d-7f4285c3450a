package com.logistics.management.webapi.controller.companyaccount;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.client.companyaccount.CompanyAccountClient;
import com.logistics.management.webapi.client.companyaccount.request.CompanyAccountAddRequestModel;
import com.logistics.management.webapi.client.companyaccount.request.CompanyAccountEnabledRequestModel;
import com.logistics.management.webapi.client.companyaccount.request.CompanyAccountImageRequestModel;
import com.logistics.management.webapi.client.companyaccount.request.SearchCompanyAccountRequestModel;
import com.logistics.management.webapi.client.companyaccount.response.CompanyAccountImageResponseModel;
import com.logistics.management.webapi.client.companyaccount.response.SearchCompanyAccountResponseModel;
import com.logistics.management.webapi.controller.companyaccount.mapping.CompanyAccountSearchListMapping;
import com.logistics.management.webapi.controller.companyaccount.mapping.GetAccountImageListMapping;
import com.logistics.management.webapi.controller.companyaccount.request.CompanyAccountAddRequestDto;
import com.logistics.management.webapi.controller.companyaccount.request.CompanyAccountEnabledRequestDto;
import com.logistics.management.webapi.controller.companyaccount.request.CompanyAccountImageRequestDto;
import com.logistics.management.webapi.controller.companyaccount.request.SearchCompanyAccountRequestDto;
import com.logistics.management.webapi.controller.companyaccount.response.CompanyAccountImageResponseDto;
import com.logistics.management.webapi.controller.companyaccount.response.SearchCompanyAccountResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/03/27
 */
@Api(value = "API-CompanyAccountApi", tags = "公司账户管理")
@Slf4j
@RestController
public class CompanyAccountController {

    @Resource
    private CompanyAccountClient companyAccountClient;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 新增公司账户
     *
     * @param requestDto 公司账户信息
     * @return 操作结果
     */
    @ApiOperation(value = "新增公司账户", tags = "1.2.5")
    @PostMapping("/api/companyAccount/addAccount")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addAccount(@RequestBody @Valid CompanyAccountAddRequestDto requestDto) {
        Result<Boolean> result = companyAccountClient.addAccount(MapperUtils.mapper(requestDto, CompanyAccountAddRequestModel.class));
        result.throwException();
        return result;
    }

    /**
     * 公司账户列表
     *
     * @param requestDto 筛选条件
     * @return 公司账户列表
     */
    @ApiOperation(value = "公司账户查询列表", tags = "1.2.5")
    @PostMapping("/api/companyAccount/searchList")
    public Result<PageInfo<SearchCompanyAccountResponseDto>> searchList(@RequestBody SearchCompanyAccountRequestDto requestDto) {
        Result<PageInfo<SearchCompanyAccountResponseModel>> result = companyAccountClient.searchList(MapperUtils.mapper(requestDto, SearchCompanyAccountRequestModel.class));
        result.throwException();
        PageInfo pageData = result.getData();
        pageData.setList(MapperUtils.mapper(pageData.getList(), SearchCompanyAccountResponseDto.class, new CompanyAccountSearchListMapping()));
        return Result.success(pageData);
    }

    /**
     * 查看银行账户图片
     *
     * @param requestDto 公司银行账户id
     * @return 银行账户图片
     */
    @ApiOperation(value = "公司账户图片查询", tags = "1.2.5")
    @PostMapping("/api/companyAccount/getAccountImageList")
    public Result<CompanyAccountImageResponseDto> getAccountImageList(@RequestBody @Valid CompanyAccountImageRequestDto requestDto) {
        Result<CompanyAccountImageResponseModel> result = companyAccountClient.getAccountImageList(MapperUtils.mapper(requestDto, CompanyAccountImageRequestModel.class));
        result.throwException();
        List<String> imagePaths = result.getData().getImagePaths();
        List<String> sourceSrcList = new ArrayList<>();
        if (ListUtils.isNotEmpty(imagePaths)) {
            for (String imagePath : imagePaths) {
                sourceSrcList.add(imagePath);
            }
            Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
            return Result.success(MapperUtils.mapper(result.getData(), CompanyAccountImageResponseDto.class, new GetAccountImageListMapping(configKeyConstant, imageMap)));
        }
        return Result.success(new CompanyAccountImageResponseDto());
    }

    /**
     * 公司账户启用/禁用
     *
     * @param requestDto 启用/禁用
     * @return 操作结果
     */
    @ApiOperation(value = "公司账户禁用/启用", tags = "1.2.5")
    @PostMapping("/api/companyAccount/enabled")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> enabled(@RequestBody @Valid CompanyAccountEnabledRequestDto requestDto) {
        Result<Boolean> result = companyAccountClient.enabled(MapperUtils.mapper(requestDto, CompanyAccountEnabledRequestModel.class));
        result.throwException();
        return result;
    }
}
