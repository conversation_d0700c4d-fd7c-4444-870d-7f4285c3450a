package com.logistics.appapi.controller.driversafepromise.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/12/13 15:32
 */
@Data
public class SummarySearchSafePromiseResponseDto {
    @ApiModelProperty("全部")
    private String allCount = "";
    @ApiModelProperty("已经签订")
    private String hasSignCount = "";
    @ApiModelProperty("未签订")
    private String notSignCount = "";
}
