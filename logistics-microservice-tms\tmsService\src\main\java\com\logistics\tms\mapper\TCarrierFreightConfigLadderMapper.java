package com.logistics.tms.mapper;

import com.logistics.tms.entity.TCarrierFreightConfigLadder;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2023/06/30
*/
@Mapper
public interface TCarrierFreightConfigLadderMapper extends BaseMapper<TCarrierFreightConfigLadder> {

    List<TCarrierFreightConfigLadder> selectConfigLadderByConfigAddressId(Long freightConfigAddressId);

    List<TCarrierFreightConfigLadder> selectConfigLadderByLadderModeAndModeIds(@Param("ladderMode") Integer ladderMode, @Param("modeIds") List<Long> modeIds);

    int insertGeneratedKey(TCarrierFreightConfigLadder tCarrierFreightConfigLadder);

    void deleteByModeIds(@Param("modeIds") List<Long> modeIds, @Param("lastModifiedBy") String lastModifiedBy,@Param("ladderMode") Integer ladderMode);
}