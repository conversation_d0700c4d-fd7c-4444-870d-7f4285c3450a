package com.logistics.management.webapi.controller.invoicingmanagement.tradition.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/3/19 16:19
 */
@Data
public class TraditionSearchInvoicingManagementListRequestDto extends AbstractPageForm<TraditionSearchInvoicingManagementListRequestDto> {

    /**
     * 业务名称
     */
    @ApiModelProperty("业务名称")
    private String businessName;

    /**
     * 开票月份-开始
     */
    @ApiModelProperty("开票月份-开始")
    private String invoicingMonthStart;
    /**
     * 开票月份-结束
     */
    @ApiModelProperty("开票月份-结束")
    private String invoicingMonthEnd;

    /**
     * 承运商名称
     */
    @ApiModelProperty("承运商名称")
    private String companyCarrierName;
}
