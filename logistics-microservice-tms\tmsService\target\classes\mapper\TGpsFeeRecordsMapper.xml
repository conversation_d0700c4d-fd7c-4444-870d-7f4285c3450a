<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TGpsFeeRecordsMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TGpsFeeRecords" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="gps_fee_id" property="gpsFeeId" jdbcType="BIGINT" />
    <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR" />
    <result column="gps_service_provider" property="gpsServiceProvider" jdbcType="VARCHAR" />
    <result column="start_date" property="startDate" jdbcType="TIMESTAMP" />
    <result column="end_date" property="endDate" jdbcType="TIMESTAMP" />
    <result column="finish_date" property="finishDate" jdbcType="TIMESTAMP" />
    <result column="service_fee" property="serviceFee" jdbcType="DECIMAL" />
    <result column="cooperation_period" property="cooperationPeriod" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, gps_fee_id, vehicle_no, gps_service_provider, start_date, end_date, finish_date, 
    service_fee, cooperation_period, remark, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_gps_fee_records
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_gps_fee_records
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TGpsFeeRecords" >
    insert into t_gps_fee_records (id, gps_fee_id, vehicle_no, 
      gps_service_provider, start_date, end_date, 
      finish_date, service_fee, cooperation_period, 
      remark, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{gpsFeeId,jdbcType=BIGINT}, #{vehicleNo,jdbcType=VARCHAR}, 
      #{gpsServiceProvider,jdbcType=VARCHAR}, #{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP}, 
      #{finishDate,jdbcType=TIMESTAMP}, #{serviceFee,jdbcType=DECIMAL}, #{cooperationPeriod,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TGpsFeeRecords" >
    insert into t_gps_fee_records
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="gpsFeeId != null" >
        gps_fee_id,
      </if>
      <if test="vehicleNo != null" >
        vehicle_no,
      </if>
      <if test="gpsServiceProvider != null" >
        gps_service_provider,
      </if>
      <if test="startDate != null" >
        start_date,
      </if>
      <if test="endDate != null" >
        end_date,
      </if>
      <if test="finishDate != null" >
        finish_date,
      </if>
      <if test="serviceFee != null" >
        service_fee,
      </if>
      <if test="cooperationPeriod != null" >
        cooperation_period,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="gpsFeeId != null" >
        #{gpsFeeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null" >
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="gpsServiceProvider != null" >
        #{gpsServiceProvider,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null" >
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null" >
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="finishDate != null" >
        #{finishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="serviceFee != null" >
        #{serviceFee,jdbcType=DECIMAL},
      </if>
      <if test="cooperationPeriod != null" >
        #{cooperationPeriod,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TGpsFeeRecords" >
    update t_gps_fee_records
    <set >
      <if test="gpsFeeId != null" >
        gps_fee_id = #{gpsFeeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null" >
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="gpsServiceProvider != null" >
        gps_service_provider = #{gpsServiceProvider,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null" >
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null" >
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="finishDate != null" >
        finish_date = #{finishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="serviceFee != null" >
        service_fee = #{serviceFee,jdbcType=DECIMAL},
      </if>
      <if test="cooperationPeriod != null" >
        cooperation_period = #{cooperationPeriod,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TGpsFeeRecords" >
    update t_gps_fee_records
    set gps_fee_id = #{gpsFeeId,jdbcType=BIGINT},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      gps_service_provider = #{gpsServiceProvider,jdbcType=VARCHAR},
      start_date = #{startDate,jdbcType=TIMESTAMP},
      end_date = #{endDate,jdbcType=TIMESTAMP},
      finish_date = #{finishDate,jdbcType=TIMESTAMP},
      service_fee = #{serviceFee,jdbcType=DECIMAL},
      cooperation_period = #{cooperationPeriod,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>