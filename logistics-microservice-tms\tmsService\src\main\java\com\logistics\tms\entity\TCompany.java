package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TCompany extends BaseEntity {
    /**
    * 公司名称
    */
    @ApiModelProperty("公司名称")
    private String companyName;

    /**
    * 营业执照图片
    */
    @ApiModelProperty("营业执照图片")
    private String tradingCertificateImage;

    /**
    * 营业执照有效期
    */
    @ApiModelProperty("营业执照有效期")
    private Date tradingCertificateValidityTime;

    /**
    * 营业执照是否永久0否1是
    */
    @ApiModelProperty("营业执照是否永久0否1是")
    private Integer tradingCertificateIsForever;

    /**
    * 营业执照是否后补 0否1是
    */
    @ApiModelProperty("营业执照是否后补 0否1是")
    private Integer tradingCertificateIsAmend;
}