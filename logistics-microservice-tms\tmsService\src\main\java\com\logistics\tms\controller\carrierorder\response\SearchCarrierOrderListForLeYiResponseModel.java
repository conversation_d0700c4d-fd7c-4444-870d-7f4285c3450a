package com.logistics.tms.controller.carrierorder.response;

import com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class SearchCarrierOrderListForLeYiResponseModel {

    @ApiModelProperty("运单ID")
    private Long carrierOrderId ;

    @ApiModelProperty("10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0 已取消")
    private Integer status ;

    @ApiModelProperty("运单号")
    private String carrierOrderCode ;

    @ApiModelProperty("客户单号")
    private String customerOrderCode;

    @ApiModelProperty("需求单ID")
    private Long demandOrderId ;

    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("司机名")
    private String driverName;

    @ApiModelProperty("司机手机")
    private String driverMobile;

    @ApiModelProperty("司机身份证号")
    private String driverIdentity;

    @ApiModelProperty("司机运费价格类型 1 单价 2 一口价")
    private Integer dispatchFreightFeeType;

    @ApiModelProperty("司机运费")
    private BigDecimal dispatchFreightFee;

    @ApiModelProperty("委托费用类型 1 单价 2 一口价")
    private Integer entrustFreightType;

    @ApiModelProperty("委托运费")
    private BigDecimal entrustFreight;

    @ApiModelProperty("签收运费")
    private BigDecimal signFreightFee;

    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;

    @ApiModelProperty("多装多卸加价费用")
    private BigDecimal markupFee;

    @ApiModelProperty("预计承运数量")
    private BigDecimal expectAmount ;

    @ApiModelProperty("实际提货数量")
    private BigDecimal loadAmount ;

    @ApiModelProperty("纠错前实际提货数量")
    private BigDecimal loadAmountExpect ;

    @ApiModelProperty("实际卸货数量")
    private BigDecimal unloadAmount ;

    @ApiModelProperty("实际签收数量")
    private BigDecimal signAmount ;

    @ApiModelProperty("预计提货时间")
    private Date expectLoadTime ;

    @ApiModelProperty("预计到货时间")
    private Date expectArrivalTime ;

    //地址信息
    //提货信息
    @ApiModelProperty("发货地址")
    private String loadProvinceName ;
    private String loadCityName ;
    private String loadAreaName ;
    private String loadDetailAddress ;
    private String loadWarehouse ;
    @ApiModelProperty("发货人")
    private String consignorName ;
    private String consignorMobile ;
    @ApiModelProperty("期望提货时间")
    private Date expectedLoadTime;
    //提货定位信息
    @ApiModelProperty("提货定位位置")
    private String loadLocation;
    //卸货信息
    @ApiModelProperty("收货地址")
    private String unloadProvinceName;
    private String unloadCityName ;
    private String unloadAreaName ;
    private String unloadDetailAddress ;
    private String unloadWarehouse ;
    @ApiModelProperty("收货人")
    private String receiverName ;
    private String receiverMobile ;
    @ApiModelProperty("期望到货时间")
    private Date expectedUnloadTime;
    //卸货定位信息
    @ApiModelProperty("卸货定位位置")
    private String unloadLocation;

    @ApiModelProperty("预计里程数")
    private BigDecimal expectMileage;

    @ApiModelProperty("实际提货时间")
    private Date loadTime ;

    @ApiModelProperty("实际到货时间")
    private Date unloadTime ;

    @ApiModelProperty("实际签收时间")
    private Date signTime ;

    @ApiModelProperty("备注")
    private String remark ;

    @ApiModelProperty("调度人")
    private String dispatchUserName ;

    @ApiModelProperty("运单生成时间")
    private Date dispatchTime ;

    @ApiModelProperty("回单数")
    private Integer carrierOrderTicketsAmount;

    @ApiModelProperty("车主id")
    private Long companyCarrierId;

    @ApiModelProperty("车主")
    private String carrierCompany;

    @ApiModelProperty("车主公司类型")
    private Integer companyCarrierType;

    @ApiModelProperty("车主联系人id")
    private Long carrierContactId;

    @ApiModelProperty("车主联系人")
    private String carrierContactName;

    @ApiModelProperty("车主联系人电话")
    private String carrierContactMobile;

    @ApiModelProperty("货主")
    private String entrustCompany;

    @ApiModelProperty("是否取消")
    private Integer ifCancel;

    @ApiModelProperty("是否放空  0否1是")
    private Integer ifEmpty;

    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;

    @ApiModelProperty("是否加急：0 否，1 是")
    private Integer ifUrgent;

    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;

    @ApiModelProperty("纠错状态：0 待纠错，1 已纠错，2 无需纠错")
    private Integer correctStatus;

    @ApiModelProperty("调度单号")
    private String dispatchOrderCode;

    @ApiModelProperty("云仓异常数")
    private BigDecimal abnormalAmount ;

    private List<SearchCarrierOrderListGoodsInfoModel> goodsInfoList;

    @ApiModelProperty("结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer settlementTonnage;

    @ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位'")
    private Integer carrierSettlement;

    @ApiModelProperty("车主结算费用合计")
    private BigDecimal carrierSettlementCostTotal;

    @ApiModelProperty("车主结算数量")
    private BigDecimal carrierSettlementAmount;

    @ApiModelProperty("车主报价类型 1 单价 2 整车价")
    private Integer carrierPaymentPriceType;

    @ApiModelProperty("车主价格：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer carrierPriceType;

    @ApiModelProperty("车主价格")
    private BigDecimal carrierPrice;

    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;

    @ApiModelProperty("提货时效")
    private Integer loadValidity;

    @ApiModelProperty("大区")
    private String loadRegionName;

    @ApiModelProperty("大区负责人")
    private String loadRegionContactName;

    @ApiModelProperty("大区负责人手机号")
    private String loadRegionContactPhone;

    @ApiModelProperty("出库状态：0 待出库，1 部分出库，2 已出库")
    private Integer outStatus;

    @ApiModelProperty("运单其他费用")
    private List<CarrierOrderOtherFeeItemModel> otherFee;

    @ApiModelProperty("下单部门")
    private String publishOrgName;

    @ApiModelProperty("回收任务类型：1 日常回收，2 加急或节假日回收")
    private Integer recycleTaskType;

    @ApiModelProperty("配置距离")
    private String configDistance;

    /**
     * 对账单上的临时费用费点
     */
    @ApiModelProperty("对账单上的临时费用费点")
    private BigDecimal statementOtherFeeTaxPoint;



    /**
     * 对账单上的运费费点
     */
    @ApiModelProperty("对账单上的运费费点")
    private BigDecimal statementFreightTaxPoint ;

    @ApiModelProperty("是否是额外补的运单  0：否 1：是  V2.6.8")
    private Integer ifExtCarrierOrder;


    @ApiModelProperty("是否显示补单按钮  0：否 1：是  V2.6.8")
    private String enableExtCarrierOrder= "";

    private String demandOrderEntrustType;
}
