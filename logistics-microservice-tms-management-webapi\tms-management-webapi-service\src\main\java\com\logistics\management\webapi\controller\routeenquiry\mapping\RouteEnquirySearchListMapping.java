package com.logistics.management.webapi.controller.routeenquiry.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.RouteEnquiryStatusEnum;
import com.logistics.management.webapi.base.enums.YesOrNoEnum;
import com.logistics.management.webapi.client.routeenquiry.response.SearchRouteEnquiryListResponseModel;
import com.logistics.management.webapi.controller.routeenquiry.response.SearchRouteEnquiryListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @author: wjf
 * @date: 2024/7/10 13:53
 */
public class RouteEnquirySearchListMapping extends MapperMapping<SearchRouteEnquiryListResponseModel, SearchRouteEnquiryListResponseDto> {
    @Override
    public void configure() {
        SearchRouteEnquiryListResponseModel source = getSource();
        SearchRouteEnquiryListResponseDto destination = getDestination();

        //状态
        if (CommonConstant.INTEGER_ONE.equals(source.getIfCancel())){
            destination.setStatus(RouteEnquiryStatusEnum.QUOTATION_CANCEL.getKey().toString());
            destination.setStatusLabel(RouteEnquiryStatusEnum.QUOTATION_CANCEL.getValue());
        }else{
            destination.setStatusLabel(RouteEnquiryStatusEnum.getEnum(source.getStatus()).getValue());
        }

        //归档
        destination.setIfArchiveLabel(YesOrNoEnum.getEnum(source.getIfArchive()).getValue());

        //中标承运商、合同号
        if (RouteEnquiryStatusEnum.COMPLETE_QUOTATION.getKey().equals(source.getStatus())){
            if (CompanyTypeEnum.COMPANY.getKey().equals(source.getCompanyCarrierType())) {
                destination.setCompanyCarrierName(source.getCompanyCarrierName());
            } else if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())) {
                destination.setCompanyCarrierName(source.getCarrierContactName() + " " + source.getCarrierContactPhone());
            }
        }else{
            destination.setCompanyCarrierName(CommonConstant.BLANK_TEXT);
            destination.setContractCode(CommonConstant.BLANK_TEXT);
        }

        //报价生效期限
        if (source.getQuoteStartTime() != null) {
            destination.setQuoteStartTime(DateUtils.dateToString(source.getQuoteStartTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if (source.getQuoteEndTime() != null) {
            destination.setQuoteEndTime(DateUtils.dateToString(source.getQuoteEndTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }

        //审核时间
        if (source.getAuditTimeOne() != null) {
            destination.setAuditTimeOne(DateUtils.dateToString(source.getAuditTimeOne(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if (source.getAuditTimeTwo() != null) {
            destination.setAuditTimeTwo(DateUtils.dateToString(source.getAuditTimeTwo(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
    }
}
