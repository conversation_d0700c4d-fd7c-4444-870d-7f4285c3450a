package com.logistics.management.webapi.controller.settlestatement.packaging.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class CarrierSettleStatementDetailListRequestDto extends AbstractPageForm<CarrierSettleStatementDetailListRequestDto> {

    @ApiModelProperty(value = "对账单id", required = true)
    @NotBlank(message = "对账单id不能为空")
    private String settleStatementId;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("车牌号")
    private String vehicleNumber;

    @ApiModelProperty("司机名,司机姓名+手机号")
    private String driverName;

    @ApiModelProperty("需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨 1.3.3")
    private String entrustType;

    @ApiModelProperty("报价类型：1 单价，2 一口价")
    private String carrierPriceType;

    @ApiModelProperty("发货仓库")
    private String loadWarehouse;

    @ApiModelProperty("发货地址")
    private String loadAddress;

    @ApiModelProperty("收货仓库")
    private String unloadWareHouse;

    @ApiModelProperty("收货地址")
    private String unloadAddress;

    @ApiModelProperty("调度单号")
    private String dispatchOrderCode;

    @ApiModelProperty("提货时间起")
    private String loadTimeStart;

    @ApiModelProperty("提货时间始")
    private String loadTimeEnd;

    @ApiModelProperty("卸货时间起")
    private String unloadTimeStart;

    @ApiModelProperty("卸货时间止")
    private String unloadTimeEnd;

    @ApiModelProperty("签收时间起")
    private String signTimeStart;

    @ApiModelProperty("签收时间始")
    private String signTimeEnd;

    @ApiModelProperty("1.3.7新增；项目标签：1 石化板块，2 轮胎板块，3 涂料板块，4 其他")
    private String projectLabel;

    @ApiModelProperty("拼单助手运单号")
    private List<String> carrierOrderCodeList;

    @ApiModelProperty("拼单助手需求单号")
    private List<String> demandOrderCodeList;

    @ApiModelProperty("对账单明细Ids")
    private String settleStatementItemIds;
}
