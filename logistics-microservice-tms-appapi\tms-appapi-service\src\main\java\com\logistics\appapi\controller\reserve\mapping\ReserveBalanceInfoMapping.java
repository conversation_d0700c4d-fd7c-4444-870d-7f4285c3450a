package com.logistics.appapi.controller.reserve.mapping;


import com.logistics.appapi.client.reservebalance.response.ReserveBalanceInfoResponseModel;
import com.logistics.appapi.controller.reserve.response.ReserveBalanceInfoResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.math.BigDecimal;
import java.util.Objects;

public class ReserveBalanceInfoMapping extends MapperMapping<ReserveBalanceInfoResponseModel, ReserveBalanceInfoResponseDto> {

    @Override
    public void configure() {
        ReserveBalanceInfoResponseModel source = getSource();
        ReserveBalanceInfoResponseDto destination = getDestination();
        destination.setBalance(conversionAmount(source.getBalance()));
        destination.setWaitingWriteOffAmount(conversionAmount(source.getWaitingWriteOffAmount()));
    }

    private String conversionAmount(BigDecimal amount) {
        if (Objects.isNull(amount)) {
            return BigDecimal.ZERO.toString();
        }
        return amount.stripTrailingZeros().toPlainString();
    }
}
