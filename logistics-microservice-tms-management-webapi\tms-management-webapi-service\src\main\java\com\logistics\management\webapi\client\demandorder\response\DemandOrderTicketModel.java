package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DemandOrderTicketModel {
    private Long ticketsId;
    @ApiModelProperty("图片路径URL")
    private String imagePath;
    @ApiModelProperty("图片名")
    private String imageName;
    @ApiModelProperty("上传人姓名")
    private String uploadUserName;
    @ApiModelProperty("上传时间")
    private Date uploadTime;
    @ApiModelProperty("图片类型")
    private Integer imageType;
}
