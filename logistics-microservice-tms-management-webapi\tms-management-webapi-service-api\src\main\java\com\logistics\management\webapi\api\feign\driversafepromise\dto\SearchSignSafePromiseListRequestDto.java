package com.logistics.management.webapi.api.feign.driversafepromise.dto;
import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 签订承诺书列表
 * @Author: sj
 * @Date: 2019/11/4 10:18
 */
@Data
public class SearchSignSafePromiseListRequestDto extends AbstractPageForm<SearchSignSafePromiseListRequestDto>{
    @ApiModelProperty("安全承诺书ID")
    private String safePromiseId;
    @ApiModelProperty("签订状态: 0 待签订、1 已签订")
    private String status;
    @ApiModelProperty("司机姓名")
    private String staffName;
    @ApiModelProperty("司机电话")
    private String staffMobile;
    @ApiModelProperty("人员机构 1 自主 2外部 3 自营")
    private String staffProperty;
}
