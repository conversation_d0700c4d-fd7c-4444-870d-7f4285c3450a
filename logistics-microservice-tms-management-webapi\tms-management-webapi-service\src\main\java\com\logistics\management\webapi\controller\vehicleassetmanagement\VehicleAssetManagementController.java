package com.logistics.management.webapi.controller.vehicleassetmanagement;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.vehicleassetmanagement.VehicleAssetManagementClient;
import com.logistics.management.webapi.client.vehicleassetmanagement.request.SearchTrailerVehicleRequestModel;
import com.logistics.management.webapi.client.vehicleassetmanagement.response.SearchTrailerVehicleResponseModel;
import com.logistics.management.webapi.controller.vehicleassetmanagement.request.SearchTrailerVehicleRequestDto;
import com.logistics.management.webapi.controller.vehicleassetmanagement.response.SearchTrailerVehicleResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date:2019/6/5 9:20
 *
 */
@Api(value = "API-VehicleAssetManagementApi-车辆资产管理", tags = "车辆资产管理")
@RestController
@RequestMapping(value = "/api/vehicleAssetManagement")
public class VehicleAssetManagementController {

    @Resource
    private VehicleAssetManagementClient vehicleAssetManagementClient;

    @PostMapping(value = "/searchTrailerVehicle")
    @ApiOperation(value = "分页搜索挂车", tags = "1.4.0")
    public Result<PageInfo<SearchTrailerVehicleResponseDto>> searchTrailerVehicle(@RequestBody @Valid SearchTrailerVehicleRequestDto requestDto) {
        SearchTrailerVehicleRequestModel requestModel = MapperUtils.mapper(requestDto, SearchTrailerVehicleRequestModel.class);
        requestModel.setRequestSource(CommonConstant.INTEGER_ONE);
        Result<PageInfo<SearchTrailerVehicleResponseModel>> result = vehicleAssetManagementClient.searchTrailerVehicle(requestModel);
        result.throwException();
        PageInfo dtoPageInfo = result.getData();
        List<SearchTrailerVehicleResponseDto> dtoList = MapperUtils.mapper(result.getData().getList(), SearchTrailerVehicleResponseDto.class);
        dtoPageInfo.setList(dtoList == null ? new ArrayList() : dtoList);
        return Result.success(dtoPageInfo);
    }
}
