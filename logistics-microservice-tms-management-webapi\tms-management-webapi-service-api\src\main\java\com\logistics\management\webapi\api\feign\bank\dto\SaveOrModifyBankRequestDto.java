package com.logistics.management.webapi.api.feign.bank.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * @Author: sj
 * @Date: 2019/7/10 14:06
 */
@Data
public class SaveOrModifyBankRequestDto {
    @ApiModelProperty("银行ID")
    private String bankId;
    @ApiModelProperty("银行名称")
    @NotBlank(message = "银行名称为空")
    @Pattern(regexp = "[\\u4E00-\\u9FA5]{2,50}",message = "请维护正确的银行名称")
    private String bankName;
    @ApiModelProperty("备注")
    @Size(max = 300,message = "备注不超过300字")
    private String remark;
    @ApiModelProperty(value = "支行名字")
    private String branchName;
}
