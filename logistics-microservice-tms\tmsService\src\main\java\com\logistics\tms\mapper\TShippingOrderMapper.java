package com.logistics.tms.mapper;

import com.logistics.tms.biz.shippingorder.model.ShippingOrderSqlConditionModel;
import com.logistics.tms.controller.dispatch.request.SearchCanJoinShippingOrderRequestModel;
import com.logistics.tms.controller.dispatch.response.SearchCanJoinShippingOrderRespModel;
import com.logistics.tms.controller.shippingorder.request.SearchShippingOrderListRequestModel;
import com.logistics.tms.entity.TShippingOrder;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
* Created by Mybatis Generator on 2024/08/06
*/
@Mapper
public interface TShippingOrderMapper extends BaseMapper<TShippingOrder> {

    List<TShippingOrder> selectBatchIds(@Param("params") List<Long> ids);

    List<TShippingOrder> searchList(@Param("param1") SearchShippingOrderListRequestModel requestModel,
                                    @Param("dispatchOrderIds") Collection<Long> dispatchOrderIds,
                                    @Param("conditionShippingOrderIds") List<Long> conditionShippingOrderIds);


    Integer selectWaitAuditCount();


    List<SearchCanJoinShippingOrderRespModel> searchCanJoinShippingOrder(@Param("param1") SearchCanJoinShippingOrderRequestModel requestModel);

    List<TShippingOrder> selectByCondition(@Param("param1") ShippingOrderSqlConditionModel shippingOrderSqlConditionModel);

}