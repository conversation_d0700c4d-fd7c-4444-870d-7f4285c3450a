<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TPlatformCompanyMapper" >
  <select id="searchPlatformCompanyList" resultType="com.logistics.tms.api.feign.platformcompany.model.SearchPlatformCompanyListResponseModel">
    select
    id as platformCompanyId,
    company_name as platformCompanyName,
    created_by as createdBy,
    created_time as createdTime,
    last_modified_by as lastModifiedBy,
    last_modified_time as lastModifiedTime
    from t_platform_company
    where valid = 1
    order by last_modified_time desc, id desc
  </select>

  <select id="getByName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_platform_company
    where valid = 1
    and company_name = #{companyName,jdbcType=VARCHAR}
  </select>

  <select id="platformCompanySelectList" resultType="com.logistics.tms.api.feign.platformcompany.model.PlatformCompanySelectListResponseModel">
    select
    id as platformCompanyId,
    company_name as platformCompanyName
    from t_platform_company
    where valid = 1
    <if test="params.platformCompanyName != null and params.platformCompanyName != ''">
      and instr(company_name, #{params.platformCompanyName,jdbcType=VARCHAR})
    </if>
    order by last_modified_time desc, id desc
  </select>
</mapper>