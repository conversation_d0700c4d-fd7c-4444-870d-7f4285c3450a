package com.logistics.tms.biz.loanrecord

import com.logistics.tms.api.feign.driverpayee.model.ViewLogResponseModel
import com.logistics.tms.api.feign.loanrecord.model.ExportSettlementRecordResponseModel
import com.logistics.tms.api.feign.loanrecord.model.LoanOperationRecordRequestModel
import com.logistics.tms.api.feign.loanrecord.model.LoanRecordDetailRequestModel
import com.logistics.tms.api.feign.loanrecord.model.LoanRecordDetailResponseModel
import com.logistics.tms.api.feign.loanrecord.model.LoanRecordListRequestModel
import com.logistics.tms.api.feign.loanrecord.model.LoanRecordListResponseModel
import com.logistics.tms.api.feign.loanrecord.model.LoanSettlementRecordRequestModel
import com.logistics.tms.api.feign.loanrecord.model.SaveOrUpdateLoanRecordRequestModel
import com.logistics.tms.api.feign.loanrecord.model.SummaryLoanRecordResponseModel
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel
import com.logistics.tms.base.constant.ConfigKeyConstant
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TCertificationPictures
import com.logistics.tms.entity.TLoanRecords
import com.logistics.tms.entity.TLoanSettlementRecord
import com.logistics.tms.entity.TOperateLogs
import com.logistics.tms.entity.TVehicleSettlementRelation
import com.logistics.tms.mapper.TCertificationPicturesMapper
import com.logistics.tms.mapper.TLoanRecordsMapper
import com.logistics.tms.mapper.TLoanSettlementRecordMapper
import com.logistics.tms.mapper.TOperateLogsMapper
import com.logistics.tms.mapper.TStaffBasicMapper
import com.logistics.tms.mapper.TVehicleBasicMapper
import com.logistics.tms.mapper.TVehicleSettlementRelationMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class LoanRecordBizTest extends Specification {
    @Mock
    ConfigKeyConstant configKeyConstant
    @Mock
    CommonBiz commonBiz
    @Mock
    TVehicleBasicMapper tVehicleBasicMapper
    @Mock
    TStaffBasicMapper tStaffBasicMapper
    @Mock
    TLoanRecordsMapper tLoanRecordsMapper
    @Mock
    TCertificationPicturesMapper tCertificationPicturesMapper
    @Mock
    TLoanSettlementRecordMapper tLoanSettlementRecordMapper
    @Mock
    TOperateLogsMapper tOperateLogsMapper
    @Mock
    TVehicleSettlementRelationMapper tVehicleSettlementRelationMapper
    @InjectMocks
    LoanRecordBiz loanRecordBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tLoanRecordsMapper.getLoanRecordList(any())).thenReturn([new LoanRecordListResponseModel()])

        expect:
        loanRecordBiz.searchList(requestModel) == expectedResult

        where:
        requestModel                     || expectedResult
        new LoanRecordListRequestModel() || null
    }

    @Unroll
    def "get Summary where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tLoanRecordsMapper.getSummaryLoanRecordInfo(any())).thenReturn(new SummaryLoanRecordResponseModel())

        expect:
        loanRecordBiz.getSummary(requestModel) == expectedResult

        where:
        requestModel                     || expectedResult
        new LoanRecordListRequestModel() || new SummaryLoanRecordResponseModel()
    }

    @Unroll
    def "save Or Update where requestModel=#requestModel"() {
        given:
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(commonBiz.addOperateLogs(anyLong(), any(), anyString(), anyString())).thenReturn(new TOperateLogs())
        when(tVehicleBasicMapper.getVehicleBasicPropertyById(anyLong())).thenReturn(new VehicleBasicPropertyModel())
        when(tLoanRecordsMapper.getLoanRecordsByVehicleId(anyLong())).thenReturn(new TLoanRecords(status: 0, vehicleId: 1l, vehicleNo: "vehicleNo", brand: "brand", model: "model", vehicleIdentificationNumber: "vehicleIdentificationNumber", engineNumber: "engineNumber", bodyColor: "bodyColor", staffId: 1l, name: "name", mobile: "mobile", identityNumber: "identityNumber", nakedCarPrice: 0 as BigDecimal, insurancePremium: 0 as BigDecimal, purchaseTax: 0 as BigDecimal, carPrice: 0 as BigDecimal, driverExpense: 0 as BigDecimal, loanFee: 0 as BigDecimal, loanPeriods: 0, loanStartTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 21).getTime(), loadFinishTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 21).getTime(), loanRate: 0 as BigDecimal, loanCommission: 0 as BigDecimal, loanInterest: 0 as BigDecimal))
        when(tCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 21).getTime(), suffix: "suffix")])
        when(tCertificationPicturesMapper.batchInsert(any())).thenReturn(0)
        when(tCertificationPicturesMapper.updateFilePath(any())).thenReturn(0)
        when(tVehicleSettlementRelationMapper.getByObjectIdAndType(anyInt(), anyLong())).thenReturn([new TVehicleSettlementRelation()])

        expect:
        loanRecordBiz.saveOrUpdate(requestModel)
        assert expectedResult == false

        where:
        requestModel                             || expectedResult
        new SaveOrUpdateLoanRecordRequestModel() || true
    }

    @Unroll
    def "set Only Read where tLoanRecords=#tLoanRecords"() {
        expect:
        loanRecordBiz.setOnlyRead(tLoanRecords)
        assert expectedResult == false

        where:
        tLoanRecords                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              || expectedResult
        new TLoanRecords(status: 0, vehicleId: 1l, vehicleNo: "vehicleNo", brand: "brand", model: "model", vehicleIdentificationNumber: "vehicleIdentificationNumber", engineNumber: "engineNumber", bodyColor: "bodyColor", staffId: 1l, mobile: "mobile", identityNumber: "identityNumber", nakedCarPrice: 0 as BigDecimal, insurancePremium: 0 as BigDecimal, purchaseTax: 0 as BigDecimal, carPrice: 0 as BigDecimal, driverExpense: 0 as BigDecimal, loanFee: 0 as BigDecimal, loanPeriods: 0, loanStartTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 21).getTime(), loadFinishTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 21).getTime(), loanRate: 0 as BigDecimal, loanCommission: 0 as BigDecimal, loanInterest: 0 as BigDecimal) || true
    }

    @Unroll
    def "get Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(filePath: "filePath")])
        when(tVehicleSettlementRelationMapper.getByObjectIdAndType(anyInt(), anyLong())).thenReturn([new TVehicleSettlementRelation()])

        expect:
        loanRecordBiz.getDetail(requestModel) == expectedResult

        where:
        requestModel                       || expectedResult
        new LoanRecordDetailRequestModel() || new LoanRecordDetailResponseModel()
    }

    @Unroll
    def "get Settlement Record List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tLoanSettlementRecordMapper.getSettlementRecordList(anyLong())).thenReturn([new TLoanSettlementRecord(loanRecordsId: 1l, settlementDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 21).getTime(), settlementFee: 0 as BigDecimal, remainingRepaymentFee: 0 as BigDecimal, totalFee: 0 as BigDecimal)])

        expect:
        loanRecordBiz.getSettlementRecordList(requestModel) == expectedResult

        where:
        requestModel                           || expectedResult
        new LoanSettlementRecordRequestModel() || new ExportSettlementRecordResponseModel()
    }

    @Unroll
    def "get Operation Records where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tOperateLogsMapper.selectLogsByCondition(anyInt(), anyLong(), anyInt())).thenReturn([new ViewLogResponseModel()])

        expect:
        loanRecordBiz.getOperationRecords(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new LoanOperationRecordRequestModel() || new com.logistics.tms.api.feign.loanrecord.model.ExportOperationRecordResponseModel()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme