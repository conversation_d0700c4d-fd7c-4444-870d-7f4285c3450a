package com.logistics.tms.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 需求单-单个发布(中石化) - 请求实体类
 *
 * @author: wei.wang
 * @date: 2021/12/4
 */
@Data
public class PublishSinopecDemandRequestModel {

	@ApiModelProperty("需求单id")
	private Long demandOrderId;

	@ApiModelProperty("发货省份id")
	private Long loadProvinceId;
	@ApiModelProperty("发货省份名字")
	private String loadProvinceName;
	@ApiModelProperty("发货城市id")
	private Long loadCityId;
	@ApiModelProperty("发货城市名字")
	private String loadCityName;
	@ApiModelProperty("发货县区id")
	private Long loadAreaId;
	@ApiModelProperty("发货县区名字")
	private String loadAreaName;
	@ApiModelProperty("发货详细地址")
	private String loadDetailAddress;

	@ApiModelProperty("收货省份id")
	private Long unloadProvinceId;
	@ApiModelProperty("收货省份名字")
	private String unloadProvinceName;
	@ApiModelProperty("收货城市id")
	private Long unloadCityId;
	@ApiModelProperty("收货城市名字")
	private String unloadCityName;
	@ApiModelProperty("收货县区id")
	private Long unloadAreaId;
	@ApiModelProperty("收货县区名字")
	private String unloadAreaName;
	@ApiModelProperty("收货详细地址")
	private String unloadDetailAddress;

	@ApiModelProperty("调度人员姓名")
	private String dispatcherName;
	@ApiModelProperty("调度人员电话")
	private String dispatcherPhone;

	@ApiModelProperty(value = "货主价格类型：1 单价(元/吨，元/件)，2 一口价(元),发布单个必填")
	private Integer contractPriceType;
	@ApiModelProperty(value = "货主价格,发布单个可以是单价也可以是一口价,批量只能为单价")
	private BigDecimal contractPrice;

	@ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
	private Integer isOurCompany;
	@ApiModelProperty(value = "车主ID")
	private Long companyCarrierId;
	@ApiModelProperty(value = "车主价格：1 单价(元/吨，元/件)，2 一口价(元)")
	private Integer carrierPriceType;
	@ApiModelProperty(value = "车主价格,发布单个可以是单价也可以是一口价,批量为单价")
	private BigDecimal carrierPrice;
}
