package com.logistics.management.webapi.controller.dispatch.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/5/8 10:31
 */
@Data
public class DispatchForYeloLifeRequestDto {

    /**
     * 需求单货物信息
     */
    @Valid
    @NotEmpty(message = "需求单信息不能为空")
    private List<DispatchGoodsDto> vehicleRequestModels;

    /**
     * 预计到货时间
     */
    @NotBlank(message = "预计到货时间不能为空")
    private String expectArrivalTime;

    /**
     * 车辆ID
     */
    @ApiModelProperty("车辆ID")
    @NotBlank(message = "请选择车辆")
    private String vehicleId;

    /**
     * 司机ID
     */
    @ApiModelProperty("司机ID")
    @NotBlank(message = "请选择司机")
    private String driverId;

    /**
     * 多装多卸（装货数）
     */
    @ApiModelProperty("多装多卸（装货数）")
    @NotBlank(message = "请维护装卸数")
    private String loadPointAmount;

    /**
     * 多装多卸（卸货数）
     */
    @ApiModelProperty("多装多卸（卸货数）")
    @NotBlank(message = "请维护装卸数")
    private String unloadPointAmount;

    /**
     * 司机运费类型
     */
    @ApiModelProperty("司机结算运费价格类型 1 单价 2 一口价")
    private String dispatchFreightFeeType;

    /**
     * 司机运费
     */
    @ApiModelProperty("司机结算运费")
    private String dispatchFreightFee;

    /**
     * 备注
     */
    @ApiModelProperty("调度单备注")
    @Size(max = 80, message = "备注不能超过80字")
    private String remark;
}
