package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2025/01/03
*/
@Data
public class TCarrierOrderLoadCodeFile extends BaseEntity {
    /**
    * 运单提货托盘编码id
    */
    @ApiModelProperty("运单提货托盘编码id")
    private Long carrierOrderLoadCodeId;

    /**
    * 附件路径
    */
    @ApiModelProperty("附件路径")
    private String filePath;


}