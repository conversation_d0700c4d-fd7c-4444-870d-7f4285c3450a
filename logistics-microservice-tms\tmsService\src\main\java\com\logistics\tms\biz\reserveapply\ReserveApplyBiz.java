package com.logistics.tms.biz.reserveapply;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.driveraccount.DriverAccountBiz;
import com.logistics.tms.biz.reserveapply.model.ReserveApplyCancelModel;
import com.logistics.tms.biz.reserveapply.model.ReserveApplyListStatisticsModel;
import com.logistics.tms.biz.reserveapply.model.ReserveTicketsModel;
import com.logistics.tms.biz.reservebalance.ReserveBalanceBiz;
import com.logistics.tms.biz.reservebalance.model.DriverBalanceDetailModel;
import com.logistics.tms.biz.reservebalance.model.ReserveApplyBalanceChangeBoModel;
import com.logistics.tms.biz.reservebalance.model.ReserveBalanceChangeHandleBoModel;
import com.logistics.tms.controller.driveraccount.response.BankCardInfoResponseModel;
import com.logistics.tms.controller.reserveapply.request.*;
import com.logistics.tms.controller.reserveapply.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.TCertificationPicturesMapper;
import com.logistics.tms.mapper.TOperateLogsMapper;
import com.logistics.tms.mapper.TReserveApplyMapper;
import com.logistics.tms.mapper.TStaffBasicMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.ObjectUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ReserveApplyBiz {

    @Resource
    private CommonBiz commonBiz;
    @Resource
    private ReserveBalanceBiz reserveBalanceBiz;
    @Resource
    private DriverAccountBiz driverAccountBiz;
    @Resource
    private TStaffBasicMapper staffBasicMapper;
    @Resource
    private TOperateLogsMapper operateLogsMapper;
    @Resource
    private TReserveApplyMapper reserveApplyMapper;
    @Resource
    private TCertificationPicturesMapper certificationPicturesMapper;

    /**
     * 备用金申请列表查询
     *
     * @param requestModel 请求Model
     * @return PageInfo<ReserveApplyListResponseModel>
     */
    public PageInfo<ReserveApplyListResponseModel> reserveApplyList(ReserveApplyListRequestModel requestModel) {
        requestModel.enablePaging();
        List<ReserveApplyListResponseModel> reserveApplyList = reserveApplyMapper.selectReserveApplyList(requestModel);
        return new PageInfo<>(reserveApplyList);
    }

    /**
     * 备用金申请列表导出查询
     *
     * @param requestModel 请求Model
     * @return List<ReserveApplyListResponseModel>
     */
    public List<ReserveApplyListResponseModel> reserveApplyListExport(ReserveApplyListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        return reserveApplyList(requestModel).getList();
    }

    /**
     * 备用金申请详情
     *
     * @param requestModel 请求Model
     * @return ReserveApplyDetailResponseModel
     */
    public ReserveApplyDetailResponseModel reserveApplyDetail(ReserveApplyDetailRequestModel requestModel) {

        // 查询备用金申请个记录详情
        ReserveApplyDetailResponseModel reserveApplyDetailModel = reserveApplyMapper.selectReserveApplyDetailById(requestModel.getApplyId());
        assertionException.accept(ObjectUtils.isEmpty(reserveApplyDetailModel), CarrierDataExceptionEnum.RESERVE_APPLY_NOT_EXIST);

        // 业务审核 | 财务审核 展示余额、核销中的金额
        ReserveApplyAuditStatusEnum auditStatusEnum = ReserveApplyAuditStatusEnum.getEnumByKey(reserveApplyDetailModel.getStatus());
        if (ReserveApplyAuditStatusEnum.AUDIT_BUSINESS.equals(auditStatusEnum) || ReserveApplyAuditStatusEnum.AUDIT_FINANCIAL.equals(auditStatusEnum)) {
            DriverBalanceDetailModel reserveBalance = reserveBalanceBiz.getReserveBalanceDetailByDriverId(reserveApplyDetailModel.getDriverId());
            reserveApplyDetailModel.setBalanceAmount(reserveBalance.getBalanceAmount());
            reserveApplyDetailModel.setAwaitVerificationAmount(reserveBalance.getAwaitVerificationAmount());
        }

        // 查询备用金申请图片信息
        List<TCertificationPictures> reserveApplyPicList = certificationPicturesMapper.getByObjectIdAndType(requestModel.getApplyId(),
                CertificationPicturesObjectTypeEnum.T_RESERVE_APPLY_PIC.getObjectType());
        if (ListUtils.isNotEmpty(reserveApplyPicList)) {
            Map<Integer, List<String>> picTypeGroupMap = reserveApplyPicList.stream()
                    .collect(Collectors.groupingBy(TCertificationPictures::getFileType,
                            Collectors.mapping(TCertificationPictures::getFilePath, Collectors.toList())));
            List<String> remitTickets = picTypeGroupMap.get(CertificationPicturesFileTypeEnum.T_RESERVE_APPLY_REMIT_PIC_FILE.getFileType());
            reserveApplyDetailModel.setRemitTickets(remitTickets);
        }
        return reserveApplyDetailModel;
    }

    /**
     * 备用金申请撤销
     *
     * @param requestModel 请求Model
     * @return boolean
     */
    @Transactional
    public boolean reserveApplyCancel(ReserveApplyCancelRequestModel requestModel) {
        ReserveApplyCancelModel cancelModel = MapperUtils.mapper(requestModel, ReserveApplyCancelModel.class);
        cancelModel.setExceptionEnum(CarrierDataExceptionEnum.RESERVE_APPLY_AUDIT_HAD_CHANGE);
        cancelApply(cancelModel, (applyId) -> Optional.ofNullable(reserveApplyMapper.selectOneById(applyId)));
        return true;
    }

    // 撤销备用金申请
    private void cancelApply(ReserveApplyCancelModel model, Function<Long, Optional<TReserveApply>> getReserveApply) {

        // 查询申请记录
        TReserveApply reserveApply = getReserveApply.apply(model.getApplyId())
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.RESERVE_APPLY_NOT_EXIST));

        // 校验状态是否处于待业务审核/已驳回
        Integer currentStatus = reserveApply.getStatus();
        ReserveApplyAuditStatusEnum auditStatusEnum = ReserveApplyAuditStatusEnum.getEnumByKey(currentStatus);
        if (!(ReserveApplyAuditStatusEnum.AUDIT_BUSINESS.equals(auditStatusEnum)
                || ReserveApplyAuditStatusEnum.AUDIT_REJECT.equals(auditStatusEnum))) {
            throw new BizException(model.getExceptionEnum());
        }

        // 更新申请撤销状态
        TReserveApply cancelApply = new TReserveApply();
        cancelApply.setId(model.getApplyId());
        cancelApply.setStatus(ReserveApplyAuditStatusEnum.AUDIT_UNDO.getKey());
        cancelApply.setCancelRemark(model.getRemark());
        updateApplyAuditStatus(cancelApply, currentStatus);
    }

    // 更新审核状态
    private void updateApplyAuditStatus(TReserveApply apply, Integer currentStatus) {
        commonBiz.setBaseEntityModify(apply, BaseContextHandler.getUserName());
        int size = reserveApplyMapper.updateAudit(apply, currentStatus);
        assertionException.accept(size < CommonConstant.INTEGER_ONE, CarrierDataExceptionEnum.RESERVE_APPLY_AUDIT_HAD_CHANGE);
    }

    /**
     * 备用金申请审核
     *
     * @param requestModel 请求Model
     * @return boolean
     */
    @Transactional
    public boolean reserveApplyAudit(ReserveApplyAuditRequestModel requestModel) {

        // 查询申请记录
        TReserveApply reserveApply = reserveApplyMapper.selectOneById(requestModel.getApplyId());
        assertionException.accept(Objects.isNull(reserveApply), CarrierDataExceptionEnum.RESERVE_APPLY_NOT_EXIST);

        OperateLogsOperateTypeEnum logsOperateTypeEnum;
        TReserveApply auditApply = new TReserveApply();
        auditApply.setId(reserveApply.getId());
        Date auditDateTime = new Date();
        String auditUserName = BaseContextHandler.getUserName();
        ReserveApplyAuditorTypeEnum auditorTypeEnum = ReserveApplyAuditorTypeEnum.getEnumByKey(requestModel.getAuditorType());

        ReserveApplyAuditStatusEnum auditStatusEnum = ReserveApplyAuditStatusEnum.getEnumByKey(reserveApply.getStatus());
        // 通过审核
        if (ReserveApplyAuditResultEnum.PASS.getKey().equals(requestModel.getAuditResult())) {
            // 业务审核
            if (ReserveApplyAuditorTypeEnum.AUDIT_BUSINESS_TYPE.equals(auditorTypeEnum)) {
                // 校验余额是否变化
                TReserveBalance reserveBalance = reserveBalanceBiz.getReserveBalanceByDriverId(reserveApply.getStaffId());
                if (Objects.isNull(reserveBalance) ||
                        !CommonConstant.INTEGER_ZERO.equals(reserveBalance.getBalanceAmount().compareTo(requestModel.getReserveBalance()))) {
                    throw new BizException(CarrierDataExceptionEnum.REVERSE_BALANCE_AMOUNT_CHANGES);
                }
                assertionException.accept(!ReserveApplyAuditStatusEnum.AUDIT_BUSINESS.equals(auditStatusEnum),
                        CarrierDataExceptionEnum.RESERVE_APPLY_AUDIT_HAD_CHANGE);
                auditApply.setStatus(ReserveApplyAuditStatusEnum.AUDIT_FINANCIAL.getKey());
                auditApply.setApprovedAmount(requestModel.getApproveAmount());
                auditApply.setAuditorNameOne(auditUserName);
                auditApply.setAuditTimeOne(auditDateTime);
                auditApply.setAuditRemarkOne(requestModel.getRemark());
                logsOperateTypeEnum = OperateLogsOperateTypeEnum.RESERVE_APPLY_AUDIT_BUSINESS;
            }
            // 财务审核
            else {
                assertionException.accept(!ReserveApplyAuditStatusEnum.AUDIT_FINANCIAL.equals(auditStatusEnum),
                        CarrierDataExceptionEnum.RESERVE_APPLY_AUDIT_HAD_CHANGE);
                auditApply.setStatus(ReserveApplyAuditStatusEnum.AUDIT_WAIT_PAY.getKey());
                auditApply.setAuditorNameTwo(auditUserName);
                auditApply.setAuditTimeTwo(auditDateTime);
                auditApply.setAuditRemarkTwo(requestModel.getRemark());
                logsOperateTypeEnum = OperateLogsOperateTypeEnum.RESERVE_APPLY_AUDIT_FINANCIAL;
            }
        }
        // 驳回审核
        else {
            // 状态校验
            boolean isStatusHadChange = !(ReserveApplyAuditStatusEnum.AUDIT_BUSINESS.equals(auditStatusEnum) ||
                    ReserveApplyAuditStatusEnum.AUDIT_FINANCIAL.equals(auditStatusEnum));
            assertionException.accept(isStatusHadChange, CarrierDataExceptionEnum.RESERVE_APPLY_AUDIT_HAD_CHANGE);
            auditApply.setStatus(ReserveApplyAuditStatusEnum.AUDIT_REJECT.getKey());
            auditApply.setRejectRemark(requestModel.getRemark());
            logsOperateTypeEnum = ReserveApplyAuditorTypeEnum.AUDIT_BUSINESS_TYPE.equals(auditorTypeEnum) ?
                    OperateLogsOperateTypeEnum.RESERVE_APPLY_AUDIT_BUSINESS_REJECT : OperateLogsOperateTypeEnum.RESERVE_APPLY_AUDIT_FINANCIAL_REJECT;
        }

        // 更新审核状态
        updateApplyAuditStatus(auditApply, reserveApply.getStatus());
        // 记录操作日志
        addOperateLogs(reserveApply.getId(), auditDateTime, logsOperateTypeEnum, requestModel.getRemark());
        return true;
    }

    // 断言
    private final BiConsumer<Boolean, CarrierDataExceptionEnum> assertionException = (isTure, ex) -> {
        if (isTure) {
            throw new BizException(ex);
        }
    };

    /**
     * 备用金申请打款
     *
     * @param requestModel 请求Model
     * @return boolean
     */
    @Transactional
    public boolean driverReserveRemit(ReserveApplyRemitRequestModel requestModel) {

        // 查询申请记录
        TReserveApply reserveApply = reserveApplyMapper.selectOneById(requestModel.getApplyId());
        assertionException.accept(Objects.isNull(reserveApply), CarrierDataExceptionEnum.RESERVE_APPLY_NOT_EXIST);

        // 审核状态校验
        assertionException.accept(!ReserveApplyAuditStatusEnum.AUDIT_WAIT_PAY.getKey().equals(reserveApply.getStatus()),
                CarrierDataExceptionEnum.RESERVE_APPLY_AUDIT_HAD_CHANGE);

        Date operationTime = new Date();
        // 更新审核状态及余额
        TReserveApply auditApply = new TReserveApply();
        auditApply.setId(reserveApply.getId());
        auditApply.setPayTime(requestModel.getPayTime());
        auditApply.setPaymentRemark(requestModel.getRemark());
        auditApply.setRunningCode(requestModel.getRemitCode());
        auditApply.setBalanceAmount(reserveApply.getApprovedAmount());
        auditApply.setStatus(ReserveApplyAuditStatusEnum.AUDIT_PAY_DONE.getKey());
        updateApplyAuditStatus(auditApply, reserveApply.getStatus());

        // 上传凭据
        if (ListUtils.isNotEmpty(requestModel.getTickets())) {
            batchAddTickets(new ReserveTicketsModel()
                    .setObjectId(reserveApply.getId())
                    .setRemitPicEnum(CertificationPicturesFileTypeEnum.T_RESERVE_APPLY_REMIT_PIC_FILE)
                    .setFileTypeEnum(CopyFileTypeEnum.RESERVE_APPLY_REMIT_TICKET)
                    .setTickets(requestModel.getTickets()));
        }
        // 记录操作日志
        addOperateLogs(reserveApply.getId(), operationTime, OperateLogsOperateTypeEnum.RESERVE_APPLY_AUDIT_REMIT, requestModel.getRemark());

        // 充值金额
        this.syncReserveBalanceChange(reserveApply);
        return true;
    }

    // 上传凭据
    private void batchAddTickets(ReserveTicketsModel ticketsModel) {
        Date date = new Date();
        List<TCertificationPictures> pictures = ticketsModel.getTickets().stream()
                .map(pic -> {
                    // 上传图片
                    String path = commonBiz.copyFileToDirectoryOfType(ticketsModel.getFileTypeEnum().getKey(),
                            "", pic, null);
                    // 封装票据
                    TCertificationPictures picture = new TCertificationPictures();
                    picture.setObjectId(ticketsModel.getObjectId());
                    picture.setObjectType(ticketsModel.getRemitPicEnum().getObjectType().getObjectType());
                    picture.setFileType(ticketsModel.getRemitPicEnum().getFileType());
                    picture.setFileTypeName(ticketsModel.getRemitPicEnum().getFileName());
                    picture.setFileName(ticketsModel.getRemitPicEnum().getFileName());
                    picture.setFilePath(path);
                    picture.setUploadUserName(ticketsModel.getUserName());
                    picture.setUploadTime(date);
                    picture.setSuffix(pic.substring(pic.indexOf('.')));
                    commonBiz.setBaseEntityAdd(picture, BaseContextHandler.getUserName());
                    return picture;
                }).collect(Collectors.toList());
        // 保存图片路径
        certificationPicturesMapper.batchInsert(pictures);
    }

    // 记录操作日志
    private void addOperateLogs(Long objectId, Date operateTime, OperateLogsOperateTypeEnum logsEnum, String remark) {

        //添加操作日志
        String userName = BaseContextHandler.getUserName();
        TOperateLogs tOperateLogs = new TOperateLogs();
        tOperateLogs.setObjectType(logsEnum.getObjectType().getKey());
        tOperateLogs.setObjectId(objectId);
        tOperateLogs.setOperateType(logsEnum.getOperateType());
        tOperateLogs.setOperateContents(logsEnum.getOperateContents());
        tOperateLogs.setOperateUserName(userName);
        tOperateLogs.setOperateTime(operateTime);
        tOperateLogs.setRemark(remark);
        commonBiz.setBaseEntityAdd(tOperateLogs, userName);
        operateLogsMapper.insertSelective(tOperateLogs);
    }

    /**
     * 获取适用于核销的备用金申请列表 先进先出 以打款时间正序
     *
     * @param driverId 司机Id
     * @return List<TReserveApply>
     */
    public List<TReserveApply> getReserveApplyUsedInCostDeductions(Long driverId) {
        return reserveApplyMapper.selectReserveApplyUsedInCostDeductions(driverId);
    }

    /**
     * 创建备用金单号
     *
     * @return 备用金单号
     */
    public String createReserveApplyCode() {
        return commonBiz.getBusinessTypeCode(BusinessCodeTypeEnum.RESERVE_APPLY_CODE, "", BaseContextHandler.getUserName());
    }

    /**
     * 备用金申请/重新提交
     *
     * @param requestModel 请求Model
     * @return boolean
     */
    @Transactional
    public boolean applyReserveBalance(ApplyReserveBalanceRequestModel requestModel) {

        // 司机机构校验
        Long driverUserId = getLoginDriverAppletUserId();
        TStaffBasic staff = getOwnStaff(driverUserId);

        // 获取司机账户
        BankCardInfoResponseModel bankCardInfoResponseModel = driverAccountBiz.getBankCardInfoByDriverId(driverUserId);
        assertionException.accept(!bankCardInfoResponseModel.getBankAccount().equals(requestModel.getReceiveBankAccount()),
                CarrierDataExceptionEnum.REVERSE_APPLY_CARD_UPDATES);
        // 获取余额信息
        TDriverAccount account = driverAccountBiz.getAccountByDriverId(driverUserId);
        assertionException.accept(Objects.isNull(account), CarrierDataExceptionEnum.DRIVER_ACCOUNT_NOT_EXIST);

        Date currentDateTime = new Date();
        OperateLogsOperateTypeEnum operateLogsOperateTypeEnum;
        String userName = BaseContextHandler.getUserName();
        TReserveApply apply = new TReserveApply();
        apply.setStatus(ReserveApplyAuditStatusEnum.AUDIT_BUSINESS.getKey());
        apply.setApplyAmount(requestModel.getApplyAmount());
        apply.setApplyRemark(requestModel.getRemark());
        apply.setApplyTime(currentDateTime);
        apply.setReceiveBankAccount(account.getBankAccount());
        apply.setReceiveBankAccountName(account.getBankAccountName());
        apply.setReceiveBraBankName(account.getBraBankName());

        // 备用金申请
        if (Objects.isNull(requestModel.getApplyId())) {
            // 创建单号
            String reserveApplyCode = createReserveApplyCode();
            apply.setStaffId(driverUserId);
            apply.setStaffMobile(staff.getMobile());
            apply.setStaffName(staff.getName());
            apply.setStaffProperty(staff.getStaffProperty());
            apply.setReserveApplyCode(reserveApplyCode);
            commonBiz.setBaseEntityAdd(apply, userName);
            reserveApplyMapper.insertReserveApply(apply);

            operateLogsOperateTypeEnum = OperateLogsOperateTypeEnum.RESERVE_APPLY_AUDIT;
        }
        // 重新提交
        else {
            // 查询申请信息
            TReserveApply reserveApply = Optional.ofNullable(reserveApplyMapper.selectOneById(requestModel.getApplyId()))
                    .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.RESERVE_APPLY_NOT_EXIST));
            // 校验状态 非驳回、待业务审核状态 提示
            Integer status = reserveApply.getStatus();
            if (!(ReserveApplyAuditStatusEnum.AUDIT_REJECT.getKey().equals(status)
                    || ReserveApplyAuditStatusEnum.AUDIT_BUSINESS.getKey().equals(status))) {
                throw new BizException(CarrierDataExceptionEnum.RESERVE_APPLY_STATUS_CHANGE_NOT_UNDO);
            }
            // 重置申请信息
            apply.setApprovedAmount(BigDecimal.ZERO);
            apply.setAuditTimeOne(null);
            apply.setAuditorNameOne(CommonConstant.EMPTY_STRING);
            apply.setAuditRemarkOne(CommonConstant.EMPTY_STRING);
            apply.setAuditTimeTwo(null);
            apply.setAuditorNameTwo(CommonConstant.EMPTY_STRING);
            apply.setAuditRemarkTwo(CommonConstant.EMPTY_STRING);
            apply.setId(reserveApply.getId());
            commonBiz.setBaseEntityModify(apply, userName);
            reserveApplyMapper.updateReserveApply(apply);

            operateLogsOperateTypeEnum = OperateLogsOperateTypeEnum.RESERVE_APPLY_AUDIT_RESUBMIT;
        }
        // 记录日志
        addOperateLogs(apply.getId(), currentDateTime, operateLogsOperateTypeEnum, requestModel.getRemark());
        return true;
    }

    // 获取员工id
    private Long getLoginDriverAppletUserId() {
        // 获取员工id
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        assertionException.accept(CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId), CarrierDataExceptionEnum.DATA_ERROR_AND_RE_LOGIN);
        return loginDriverAppletUserId;
    }

    /**
     * 获取备用金申请列表
     *
     * @param requestModel 请求Model
     * @return ReserveBalanceApplyListResponseModel
     */
    public ReserveBalanceApplyListResponseModel reserveBalanceApplyList(ReserveBalanceApplyListRequestModel requestModel) {

        // 查询用户申请列表
        Long loginUserId = getLoginDriverAppletUserId();
        requestModel.enablePaging();
        List<ReserveBalanceApplyListItemModel> reserveBalanceApplyList = reserveApplyMapper.selectReserveBalanceApplyList(loginUserId,
                requestModel.getApplyMonth());

        // 统计月份数据
        ReserveApplyListStatisticsModel statisticsModel = reserveApplyMapper.statisticsReserveApplyList(loginUserId, requestModel.getApplyMonth());

        ReserveBalanceApplyListResponseModel responseModel = new ReserveBalanceApplyListResponseModel();
        responseModel.setApplyCount(statisticsModel.getApplyCount());
        responseModel.setApproveCount(statisticsModel.getApproveCount());
        responseModel.setApproveAmount(statisticsModel.getApproveAmount());
        responseModel.setApplyPageInfo(new PageInfo<>(reserveBalanceApplyList));
        return responseModel;
    }

    /**
     * 获取备用金详情及审批流程
     *
     * @param requestModel 请求Model
     * @return ReserveBalanceApplyDetailResponseModel
     */
    public ReserveBalanceApplyDetailResponseModel reserveBalanceApplyDetail(ReserveBalanceApplyDetailRequestModel requestModel) {

        // 查询备用金信息
        ReserveBalanceApplyDetailResponseModel responseModel = Optional.ofNullable(reserveApplyMapper.selectOneById(requestModel.getApplyId()))
                .map(a -> {
                    return new ReserveBalanceApplyDetailResponseModel()
                            .setApplyId(a.getId())
                            .setStatus(a.getStatus())
                            .setApplyCode(a.getReserveApplyCode())
                            .setApplyDate(a.getApplyTime())
                            .setStaffName(a.getStaffName())
                            .setStaffMobile(a.getStaffMobile())
                            .setReceiveBankAccount(a.getReceiveBankAccount())
                            .setApproveAmount(a.getApprovedAmount())
                            .setRemark(a.getApplyRemark())
                            .setApplyAmount(a.getApplyAmount());
                })
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.RESERVE_APPLY_NOT_EXIST));

        // 查询日志记录
        List<ReserveBalanceApplyLogModel> viewLogs = operateLogsMapper.selectLogsByCondition(OperateLogsObjectTypeEnum.RESERVE_APPLY_AUDIT.getKey(),
                        requestModel.getApplyId(), null)
                .stream()
                .filter(f -> f.getOperateTime().compareTo(responseModel.getApplyDate()) >= CommonConstant.INTEGER_ZERO)
                .sorted((o1, o2) -> {
                    Integer reserveApplyRemitType = OperateLogsOperateTypeEnum.RESERVE_APPLY_AUDIT_REMIT.getOperateType();
                    if (o1.getOperateType().equals(reserveApplyRemitType)) {
                        return CommonConstant.NEGATIVE_INTEGER_ONE;
                    } else if (reserveApplyRemitType.equals(o2.getOperateType())) {
                        return CommonConstant.INTEGER_ONE;
                    }
                    return o2.getOperateType().compareTo(o1.getOperateType());
                })
                .map(l -> {
                    return new ReserveBalanceApplyLogModel()
                            .setTitle(l.getOperateContents())
                            .setCreateTime(l.getOperateTime())
                            .setRemark(l.getRemark());
                })
                .collect(Collectors.toList());

        responseModel.setApplyLogs(viewLogs);
        return responseModel;
    }

    // 获取自主司机
    private TStaffBasic getOwnStaff(Long driverId) {
        return Optional.ofNullable(staffBasicMapper.selectByPrimaryKey(driverId))
                .filter(f -> IfValidEnum.VALID.getKey().equals(f.getValid()))
                .filter(f -> StaffPropertyEnum.OWN_STAFF.getKey().equals(f.getStaffProperty()))
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST));
    }

    /**
     * 司机撤销备用金申请
     *
     * @param requestModel 请求Model
     * @return boolean
     */
    @Transactional
    public boolean cancel(ReserveBalanceApplyCancelRequestModel requestModel) {

        // 司机机构校验
        Long loginUserId = getLoginDriverAppletUserId();
        getOwnStaff(loginUserId);

        // 备用金申请撤销
        ReserveApplyCancelModel model = MapperUtils.mapper(requestModel, ReserveApplyCancelModel.class);
        model.setExceptionEnum(CarrierDataExceptionEnum.RESERVE_APPLY_AUDIT_HAD_CHANGE_DRIVER);
        cancelApply(model, (applyId) -> Optional.ofNullable(reserveApplyMapper.selectOneByIdAndDriverId(applyId, loginUserId)));
        return true;
    }

    /**
     * 创建打款状态备用金申请
     *
     * @param driverId 司机ID
     * @param amount 金额
     * @param typeEnum 备用金申请类型
     * @param runningCode 流水号
     * @return 备用金实体
     */
    public TReserveApply createReserveRemitApply(Long driverId, BigDecimal amount, String runningCode, ReserveApplyTypeEnum typeEnum) {
        // 获取余额信息
        TDriverAccount account = driverAccountBiz.getAccountByDriverId(driverId);
        assertionException.accept(Objects.isNull(account), CarrierDataExceptionEnum.DRIVER_ACCOUNT_NOT_EXIST);
        TStaffBasic staff = getOwnStaff(driverId);
        Date currentDateTime = new Date();
        String userName = BaseContextHandler.getUserName();
        String reserveApplyCode = createReserveApplyCode();
        TReserveApply apply = new TReserveApply();
        apply.setReserveApplyCode(reserveApplyCode);
        apply.setStatus(ReserveApplyAuditStatusEnum.AUDIT_PAY_DONE.getKey());
        apply.setType(typeEnum.getKey());
        apply.setStaffId(staff.getId());
        apply.setStaffName(staff.getName());
        apply.setStaffMobile(staff.getMobile());
        apply.setStaffProperty(staff.getStaffProperty());
        apply.setReceiveBankAccount(account.getBankAccount());
        apply.setReceiveBankAccountName(account.getBankAccountName());
        apply.setReceiveBraBankName(account.getBraBankName());
        apply.setApplyAmount(amount);
        apply.setApplyTime(currentDateTime);
        apply.setApprovedAmount(amount);
        apply.setAuditorNameOne(userName);
        apply.setAuditTimeOne(currentDateTime);
        apply.setAuditorNameTwo(userName);
        apply.setAuditTimeTwo(currentDateTime);
        apply.setPayTime(currentDateTime);
        apply.setRunningCode(runningCode);
        apply.setBalanceAmount(amount);
        reserveApplyMapper.insertReserveApply(apply);

        OperateLogsOperateTypeEnum operateLogsOperateTypeEnum =
                ReserveApplyTypeEnum.RECHARGE_TYPE.equals(typeEnum) ?
                        OperateLogsOperateTypeEnum.RESERVE_APPLY_AUDIT :
                        ReserveApplyTypeEnum.ADVANCE_TYPE.equals(typeEnum) ?
                                OperateLogsOperateTypeEnum.RESERVE_APPLY_ADVANCE_AUTO_CREATED_BY_SYSTEM :
                                OperateLogsOperateTypeEnum.RESERVE_APPLY_RED_CHARGE_REFUND_AUTO_CREATED_BY_SYSTEM;
        // 记录操作日志
        addOperateLogs(apply.getId(), currentDateTime, operateLogsOperateTypeEnum, apply.getApplyRemark());
        addOperateLogs(apply.getId(), currentDateTime, OperateLogsOperateTypeEnum.RESERVE_APPLY_AUDIT_REMIT, CommonConstant.BLANK_TEXT);
        return apply;
    }

    // 同步余额变更
    private void syncReserveBalanceChange(TReserveApply entity) {
        ReserveApplyBalanceChangeBoModel reserveApplyBalanceChangeBoModel = new ReserveApplyBalanceChangeBoModel()
                .setBalance(entity.getApprovedAmount())
                .setReserveCode(entity.getReserveApplyCode());
        ReserveBalanceChangeHandleBoModel reserveBalanceChangeHandleBoModel = new ReserveBalanceChangeHandleBoModel()
                .setTypeEnum(ReserveBalanceRunningTypeEnum.RECHARGE_TYPE)
                .setDriverId(entity.getStaffId())
                .setReserveList(Collections.singletonList(reserveApplyBalanceChangeBoModel))
                .setAmount(entity.getApprovedAmount());
        reserveBalanceBiz.balanceChangeHandler(reserveBalanceChangeHandleBoModel);
    }
}
