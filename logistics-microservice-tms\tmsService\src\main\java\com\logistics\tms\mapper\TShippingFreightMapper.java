package com.logistics.tms.mapper;

import com.logistics.tms.controller.freightconfig.request.ListShippingFreightListReqModel;
import com.logistics.tms.controller.freightconfig.response.ListShippingFreightListRespModel;
import com.logistics.tms.entity.TShippingFreight;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/09/19
*/
@Mapper
public interface TShippingFreightMapper extends BaseMapper<TShippingFreight> {
    int deleteByPrimaryKey(Long id);

    int insert(TShippingFreight record);

    int insertSelective(TShippingFreight record);

    TShippingFreight selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TShippingFreight record);

    int updateByPrimaryKey(TShippingFreight record);


    List<ListShippingFreightListRespModel> getList(ListShippingFreightListReqModel reqModel);

    TShippingFreight getByName(String carrierPriceRuleName);






}