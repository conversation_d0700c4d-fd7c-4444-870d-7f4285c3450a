package com.logistics.appapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2018/10/16 13:59
 */
@Data
public class CarrierOrderLogisticsEventResponseModel {

    @ApiModelProperty("物流事件id")
    private Long eventId;
    @ApiModelProperty("物流事件类型")
    private Integer event;
    @ApiModelProperty("物流事件时间")
    private Date eventTime;
    @ApiModelProperty("物流事件描述")
    private String eventDesc;
    @ApiModelProperty("操作人")
    private String operatorName;
    @ApiModelProperty("单据数量")
    private Integer ticketCount;
}
