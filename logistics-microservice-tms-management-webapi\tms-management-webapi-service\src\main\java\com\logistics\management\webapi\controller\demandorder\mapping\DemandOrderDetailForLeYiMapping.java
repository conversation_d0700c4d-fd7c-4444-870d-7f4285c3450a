package com.logistics.management.webapi.controller.demandorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.client.demandorder.response.DemandCarrierOrderListGoodsInfoModel;
import com.logistics.management.webapi.client.demandorder.response.DemandOrderCarrierResponseModel;
import com.logistics.management.webapi.client.demandorder.response.DemandOrderDetailForLeYiResponseModel;
import com.logistics.management.webapi.client.demandorder.response.DemandOrderGoodsResponseModel;
import com.logistics.management.webapi.controller.demandorder.response.DemandOrderCarrierResponseDto;
import com.logistics.management.webapi.controller.demandorder.response.DemandOrderDetailForLeYiResponseDto;
import com.logistics.management.webapi.controller.demandorder.response.DemandOrderGoodsResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class DemandOrderDetailForLeYiMapping extends MapperMapping<DemandOrderDetailForLeYiResponseModel, DemandOrderDetailForLeYiResponseDto> {

    @Override
    public void configure() {
        DemandOrderDetailForLeYiResponseModel source = getSource();
        DemandOrderDetailForLeYiResponseDto destination = getDestination();
        destination.setGoodsUnit(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());
        destination.setEntrustType(EntrustTypeEnum.getEnum(source.getEntrustType()).getValue());

        //提货地址信息
        StringBuilder load = new StringBuilder();
        if (StringUtils.isNotBlank(source.getLoadWarehouse())) {
            load.append("【").append(source.getLoadWarehouse()).append("】");
        }
        load.append(Optional.ofNullable(source.getLoadProvinceName()).orElse("")).
                append(Optional.ofNullable(source.getLoadCityName()).orElse("")).
                append(Optional.ofNullable(source.getLoadAreaName()).orElse("")).
                append(Optional.ofNullable(source.getLoadDetailAddress()).orElse(""));
        destination.setLoadDetailAddress(load.toString());

        //货物信息
        List<DemandOrderGoodsResponseModel> goodsResponseModel = source.getGoodsResponseModel();
        if (ListUtils.isNotEmpty(goodsResponseModel)) {
            List<DemandOrderGoodsResponseDto> list = new ArrayList<>();
            for (DemandOrderGoodsResponseModel model : goodsResponseModel) {
                DemandOrderGoodsResponseDto responseDto = new DemandOrderGoodsResponseDto();
                responseDto.setGoodsName(model.getGoodsName());
                responseDto.setDemandOrderGoodsId(ConverterUtils.toString(model.getDemandOrderGoodsId()));
                responseDto.setGoodsSize(model.getGoodsSize());
                //委托
                responseDto.setGoodsAmountNumber(model.getGoodsAmountNumber().stripTrailingZeros().toPlainString());
                //已安排
                responseDto.setArrangedAmountNumber(model.getArrangedAmountNumber().stripTrailingZeros().toPlainString());
                //未安排
                responseDto.setNotArrangedAmountNumber(model.getNotArrangedAmountNumber().stripTrailingZeros().toPlainString());
                //已退回
                responseDto.setBackAmountNumber(model.getBackAmountNumber().stripTrailingZeros().toPlainString());

                if (GoodsUnitEnum.BY_VOLUME.getKey().equals(source.getGoodsUnit())) {
                    responseDto.setGoodsSize(model.getLength() + "*" + model.getWidth() + "*" + model.getHeight() + "mm "+responseDto.getGoodsSize());
                    responseDto.setGoodsAmountVolume(getVolumeString(model.getGoodsAmountNumber(), model.getLength(), model.getWidth(), model.getHeight()));
                    responseDto.setArrangedAmountVolume(getVolumeString(model.getArrangedAmountNumber(), model.getLength(), model.getWidth(), model.getHeight()));
                    responseDto.setNotArrangedAmountVolume(getVolumeString(model.getNotArrangedAmountNumber(), model.getLength(), model.getWidth(), model.getHeight()));
                    responseDto.setBackAmountVolume(getVolumeString(model.getBackAmountNumber(), model.getLength(), model.getWidth(), model.getHeight()));
                }
                list.add(responseDto);
            }
            destination.setGoodsResponseModel(list);
        }

        //状态转换
        if (CommonConstant.INTEGER_ONE.equals(source.getIfCancel())) {
            destination.setStatus(DemandOrderStatusEnum.CANCEL_DISPATCH.getKey().toString());
            destination.setStatusLabel(DemandOrderStatusEnum.CANCEL_DISPATCH.getValue());
        }else if (CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())) {
            destination.setStatus(DemandOrderStatusEnum.ORDER_EMPTY.getKey().toString());
            destination.setStatusLabel(DemandOrderStatusEnum.ORDER_EMPTY.getValue());
        }else if (CommonConstant.INTEGER_ONE.equals(source.getIfRollback())) {//已回退
            destination.setStatus(DemandOrderStatusEnum.ROLLBACK.getKey().toString());
            destination.setStatusLabel(DemandOrderStatusEnum.ROLLBACK.getValue());
        }else{
            destination.setStatus(source.getEntrustStatus().toString());
            destination.setStatusLabel(DemandOrderStatusEnum.getEnum(source.getEntrustStatus()).getValue());
        }

        if (source.getExpectedUnloadTime() != null) {
            destination.setExpectedUnloadTime(DateUtils.dateToString(source.getExpectedUnloadTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if (source.getExpectedLoadTime() != null) {
            destination.setExpectedLoadTime(DateUtils.dateToString(source.getExpectedLoadTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if(source.getTicketTime()!=null){
            destination.setTicketDate(DateUtils.dateToString(source.getTicketTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        destination.setConsignorName(source.getConsignorName() + source.getConsignorMobile());

        //货主
        String companyEntrustName = source.getCompanyEntrustName();
        if (EntrustTypeEnum.BOOKING.getKey().equals(source.getEntrustType())){
            companyEntrustName = CommonConstant.LEYI_POINTS_FOR_LOGISTICS;
        }
        destination.setCustomerCompanyName(companyEntrustName);

        List<DemandOrderCarrierResponseDto> carrierOrders = new ArrayList<>();
        //运单信息转换
        if (ListUtils.isNotEmpty(source.getCarrierResponseModel())) {
            for (DemandOrderCarrierResponseModel model : source.getCarrierResponseModel()) {
                DemandOrderCarrierResponseDto carrierOrder = new DemandOrderCarrierResponseDto();
                carrierOrder.setCarrierOrderCode(model.getCarrierOrderCode());
                carrierOrder.setCarrierOrderId(ConverterUtils.toString(model.getCarrierOrderId()));
                if (model.getDispatchTime() != null) {
                    carrierOrder.setDispatchTime(DateUtils.dateToString(model.getDispatchTime(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
                }
                //状态转换
                if (CommonConstant.INTEGER_ONE.equals(model.getIfCancel())) {
                    carrierOrder.setStatus(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getKey().toString());
                    carrierOrder.setStatusLabel(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getValue());
                }else if(CommonConstant.INTEGER_ONE.equals(model.getIfEmpty())){
                    carrierOrder.setStatus(CarrierOrderStatusEnum.EMPTY.getKey().toString());
                    carrierOrder.setStatusLabel(CarrierOrderStatusEnum.EMPTY.getValue());
                }else {
                    carrierOrder.setStatus(model.getStatus().toString());
                    carrierOrder.setStatusLabel(CarrierOrderStatusEnum.getEnum(model.getStatus()).getValue());
                }
                carrierOrder.setVehicleNumber(model.getVehicleNumber());
                carrierOrder.setDriverName(model.getDriverName() == null ? "" : model.getDriverName() + " " + model.getDriverMobile());
                carrierOrder.setDispatchUserName(model.getDispatchUserName());
                carrierOrder.setExpectAmount(model.getExpectAmount().stripTrailingZeros().toPlainString());
                if(model.getStatus()>CarrierOrderStatusEnum.WAIT_LOAD.getKey()){
                    carrierOrder.setExpectAmount(model.getLoadAmount().stripTrailingZeros().toPlainString());
                }
                List<DemandCarrierOrderListGoodsInfoModel> goodsInfoList = model.getGoodsInfoList();

                //运单预计体积
                BigDecimal exceptVolume = BigDecimal.ZERO;
                if (ListUtils.isNotEmpty(goodsInfoList)) {
                    for (DemandCarrierOrderListGoodsInfoModel tmp : goodsInfoList) {
                        exceptVolume = exceptVolume.add(getVolume(tmp.getExpectAmount(), tmp.getLength(), tmp.getWidth(), tmp.getHeight()));
                    }
                    if (exceptVolume.compareTo(BigDecimal.ZERO) > 0) {
                        carrierOrder.setAmountVolume(exceptVolume.setScale(3,BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
                    }
                }
                StringBuilder unload = new StringBuilder();
                if (StringUtils.isNotBlank(model.getUnloadWarehouse())) {
                    unload.append("【" + model.getUnloadWarehouse() + "】");
                }
                unload.append(Optional.ofNullable(model.getUnloadProvinceName()).orElse("")).
                        append(Optional.ofNullable(model.getUnloadCityName()).orElse("")).
                        append(Optional.ofNullable(model.getUnloadAreaName()).orElse("")).
                        append(Optional.ofNullable(model.getUnloadDetailAddress()).orElse(""));
                carrierOrder.setUnloadDetailAddress(unload.toString());
                carrierOrder.setReceiverName(model.getReceiverName() + model.getReceiverMobile());
                carrierOrder.setIsAmend(ConverterUtils.toString(model.getUnloadAddressIsAmend()));
                carrierOrder.setIsAmendLabel(IfRemindEnum.getEnum(model.getUnloadAddressIsAmend()).getValue());
                carrierOrders.add(carrierOrder);
            }
        }
        destination.setCarrierResponseModel(carrierOrders);

        String unit = GoodsUnitEnum.getEnum(source.getGoodsUnit()).getPriceUnit();
        //货主结算吨位
        if(source.getSettlementTonnage()!=null){
            destination.setSettlementTonnageLabel(SettlementTonnageLabelEnum.getEnum(source.getSettlementTonnage()).getValue());
        }

        //委托数量
        BigDecimal calcAmount = source.getGoodsAmount();

        //预计货主费用
        if(PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getExceptContractPriceType())){
            //单价：单价*委托数量；
            destination.setContractPriceType(ConverterUtils.toString(source.getExceptContractPriceType()));
            destination.setContractPrice(source.getExceptContractPrice()+unit);
            destination.setContractPriceTotal(ConverterUtils.toString(calcAmount.multiply(source.getExceptContractPrice()).setScale(2,BigDecimal.ROUND_HALF_UP))+CommonConstant.YUAN);
        }else if(PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getExceptContractPriceType())){
            destination.setContractPriceType(ConverterUtils.toString(source.getExceptContractPriceType()));
            destination.setContractPrice(source.getExceptContractPrice()+CommonConstant.YUAN);
            destination.setContractPriceTotal(ConverterUtils.toString(source.getExceptContractPrice().setScale(2,BigDecimal.ROUND_HALF_UP))+CommonConstant.YUAN);
        }

        //实际货主费用
        if ((source.getEntrustStatus() > DemandOrderStatusEnum.COMPLETE_DISPATCH.getKey() || CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())) && source.getEntrustSettlementCostTotal() != null) {
            if (source.getEntrustPriceType() != null && !ContractTypeEnum.DEFAULT_VALUE.getKey().equals(source.getEntrustPriceType())) {
                destination.setActualContractPriceType(ConverterUtils.toString(source.getEntrustPriceType()));
                destination.setActualContractPriceTotal(source.getEntrustSettlementCostTotal() + CommonConstant.YUAN);
                if (PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getEntrustPriceType())) {
                    if (source.getEntrustSettlementAmount().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                        destination.setActualContractPrice(source.getEntrustSettlementCostTotal().divide(source.getEntrustSettlementAmount(), 2, BigDecimal.ROUND_HALF_UP) + unit);
                    }
                } else {
                    destination.setActualContractPrice(source.getEntrustSettlementCostTotal() + CommonConstant.YUAN);
                }
            }
        }

        //车主费用
        //预计车主费用
        if (PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getCarrierFreightType())){ //单价
            //单价：单价*委托数量
            destination.setExpectedCarrierPriceType(ConverterUtils.toString(source.getCarrierFreightType()));
            destination.setExpectedCarrierPrice(source.getCarrierFreight()+unit);
            destination.setExpectedCarrierPriceTotal(ConverterUtils.toString(calcAmount.multiply(source.getCarrierFreight()).setScale(2,BigDecimal.ROUND_HALF_UP))+CommonConstant.YUAN);
        }else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getCarrierFreightType())){ //一口价
            destination.setExpectedCarrierPriceType(ConverterUtils.toString(source.getCarrierFreightType()));
            destination.setExpectedCarrierPrice(source.getCarrierFreight()+CommonConstant.YUAN);
            destination.setExpectedCarrierPriceTotal(ConverterUtils.toString(source.getCarrierFreight().setScale(2,BigDecimal.ROUND_HALF_UP))+CommonConstant.YUAN);
        }

        //车主实际费用
        if ((DemandOrderStatusEnum.COMPLETE_SIGN.getKey().equals(source.getEntrustStatus()) || CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())) && source.getCarrierSettlementCostTotal() != null){
            if(source.getCarrierPriceType() != null && !ContractTypeEnum.DEFAULT_VALUE.getKey().equals(source.getCarrierPriceType())){
                destination.setCarrierPriceType(ConverterUtils.toString(source.getCarrierPriceType()));
                destination.setCarrierPriceTotal(source.getCarrierSettlementCostTotal().add(source.getCarrierOtherFeeTotal()).setScale(2, RoundingMode.HALF_UP) + CommonConstant.YUAN);
                if(PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getCarrierPriceType())){
                    if (source.getCarrierSettlementAmount().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                        destination.setCarrierPrice(source.getCarrierSettlementCostTotal().divide(source.getCarrierSettlementAmount(), 2, BigDecimal.ROUND_HALF_UP) + unit);
                    }
                }else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getCarrierPriceType())){
                    destination.setCarrierPrice(source.getCarrierSettlementCostTotal() + CommonConstant.YUAN);
                }
            }
        }else{
            destination.setCarrierPriceType("");
            destination.setCarrierPrice("");
            destination.setCarrierPriceTotal("");
        }

        //装卸方式
        destination.setLoadingUnloadingPartLabel(LoadingUnloadingPartEnum.getEnum(source.getLoadingUnloadingPart()).getValue());

        //其他要求
        destination.setOtherRequirements(AvailableOnWeekendsEnum.getEnum(source.getAvailableOnWeekends()).getValue());

        //项目标签
        if (StringUtils.isNotBlank(source.getProjectLabel())){
            String projectLabel = Arrays.stream(source.getProjectLabel().split(","))
                    .map(s -> ProjectLabelEnum.getEnum(Integer.valueOf(s))
                            .getValue()).collect(Collectors.joining("、"));
            destination.setProjectLabel(projectLabel);
        }

    }

    /**
     * 计算物品体积
     * @param amount
     * @param length
     * @param width
     * @param height
     * @return
     */
    public BigDecimal getVolume(BigDecimal amount, Integer length, Integer width, Integer height) {
        amount = Optional.ofNullable(amount).orElse(CommonConstant.BIG_DECIMAL_ZERO);
        length = Optional.ofNullable(length).orElse(CommonConstant.INTEGER_ZERO);
        width = Optional.ofNullable(width).orElse(CommonConstant.INTEGER_ZERO);
        height = Optional.ofNullable(height).orElse(CommonConstant.INTEGER_ZERO);
        BigDecimal multiply = amount.multiply(BigDecimal.valueOf(length).multiply(BigDecimal.valueOf(width))).multiply(BigDecimal.valueOf(height));
        return multiply.divide(BigDecimal.valueOf(1000000000)).setScale(3, BigDecimal.ROUND_HALF_UP).stripTrailingZeros();
    }

    public String getVolumeString(BigDecimal amount, Integer length, Integer width, Integer height) {
        return getVolume(amount, length, width, height).toPlainString();
    }

}
