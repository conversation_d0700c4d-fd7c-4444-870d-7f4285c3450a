package com.logistics.management.webapi.client.reservebalance.model.response;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SearchDriverReserveBalanceResponseModel {

    @ApiModelProperty(value = "合计充值金额")
    private BigDecimal totalRechargeAmount;

    @ApiModelProperty(value = "合计已冲销金额")
    private BigDecimal totalVerificationAmount;

    @ApiModelProperty(value = "合计待冲销金额")
    private BigDecimal totalAwaitVerificationAmount;

    @ApiModelProperty(value = "剩余金额")
    private BigDecimal totalRemainingAmount;

    @ApiModelProperty("司机备用金列表")
    private PageInfo<DriverReserveBalanceListResponseModel> driverReserveBalancePageInfo;

}
