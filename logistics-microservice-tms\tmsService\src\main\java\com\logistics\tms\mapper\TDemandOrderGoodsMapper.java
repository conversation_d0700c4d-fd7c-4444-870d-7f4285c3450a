package com.logistics.tms.mapper;

import com.logistics.tms.controller.demandorder.response.DemandOrderGoodsResponseModel;
import com.logistics.tms.entity.TDemandOrderGoods;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TDemandOrderGoodsMapper extends BaseMapper<TDemandOrderGoods> {

   int batchInsert(@Param("list") List<TDemandOrderGoods> list);

   List<TDemandOrderGoods> getDemandOrderGoodsByGoodsIds(@Param("ids") String ids);

   List<TDemandOrderGoods> getDemandOrderGoodsByDemandOrderIds(@Param("demandOrderIds") String demandOrderIds);

   List<TDemandOrderGoods> getInvalidByDemandOrderId(@Param("demandOrderId") Long demandOrderId);

   int batchUpdateByPrimaryKeySelective(@Param("list") List<TDemandOrderGoods> list);

   List<DemandOrderGoodsResponseModel> getTDemandOrderGoodsByDemandId(@Param("demandId")Long demandId);

   /**
    * 根据需求单ID查询货物信息（用于调度）
    */
   List<TDemandOrderGoods> selectByDemandOrderId(@Param("demandOrderId") Long demandOrderId);
}