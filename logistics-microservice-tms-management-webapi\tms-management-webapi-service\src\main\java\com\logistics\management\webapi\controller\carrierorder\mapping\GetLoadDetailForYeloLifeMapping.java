package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.management.webapi.client.carrierorder.response.CarrierOrderGoodsResponseModel;
import com.logistics.management.webapi.client.carrierorder.response.GetLoadDetailForYeliLifeCodeModel;
import com.logistics.management.webapi.client.carrierorder.response.LoadDetailForYeloLifeGoodsResponseModel;
import com.logistics.management.webapi.client.carrierorder.response.LoadDetailForYeloLifeResponseModel;
import com.logistics.management.webapi.controller.carrierorder.response.CarrierOrderGoodsResponseDto;
import com.logistics.management.webapi.controller.carrierorder.response.GetLoadDetailForYeliLifeCodeDto;
import com.logistics.management.webapi.controller.carrierorder.response.LoadDetailForYeloLifeGoodsResponseDto;
import com.logistics.management.webapi.controller.carrierorder.response.LoadDetailForYeloLifeResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/22
 */
public class GetLoadDetailForYeloLifeMapping extends MapperMapping<LoadDetailForYeloLifeResponseModel, LoadDetailForYeloLifeResponseDto> {

	@Override
	public void configure() {
		LoadDetailForYeloLifeResponseModel source = getSource();
		LoadDetailForYeloLifeResponseDto destination = getDestination();

		if (source != null) {
			destination.setDriver(source.getDriverName());
			destination.setGoodsUnitLabel(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());

			StringBuilder loadAddress = new StringBuilder();
			loadAddress.append(source.getLoadProvinceName());
			loadAddress.append(source.getLoadCityName());
			loadAddress.append(source.getLoadAreaName());
			if(StringUtils.isNotBlank(source.getLoadWarehouse())){
				loadAddress.append("【").append(source.getLoadWarehouse()).append("】");
			}
			destination.setLoadAddress(loadAddress.toString());

			StringBuilder unLoadAddress = new StringBuilder();
			unLoadAddress.append(source.getUnloadProvinceName());
			unLoadAddress.append(source.getUnloadCityName());
			unLoadAddress.append(source.getUnloadAreaName());
			if(StringUtils.isNotBlank(source.getUnloadWarehouse())){
				unLoadAddress.append("【").append(source.getUnloadWarehouse()).append("】");
			}
			destination.setUnloadAddress(unLoadAddress.toString());

			List<LoadDetailForYeloLifeGoodsResponseDto> goodsList = new ArrayList<>();
			LoadDetailForYeloLifeGoodsResponseDto goodsDto;
			if(ListUtils.isNotEmpty(source.getGoodsList())){
				for (LoadDetailForYeloLifeGoodsResponseModel temp : source.getGoodsList()) {
					goodsDto = new LoadDetailForYeloLifeGoodsResponseDto();
					goodsDto.setGoodsId(ConverterUtils.toString(temp.getGoodsId()));
					goodsDto.setGoodsName(temp.getGoodsName());
					goodsDto.setExpectAmount(temp.getExpectAmount().stripTrailingZeros().toPlainString());
					goodsDto.setLoadAmount(temp.getLoadAmount().stripTrailingZeros().toPlainString());

					List<GetLoadDetailForYeliLifeCodeDto> codeDtoList = new ArrayList<>();
					for (GetLoadDetailForYeliLifeCodeModel codeModel : temp.getCodeDtoList()) {
						GetLoadDetailForYeliLifeCodeDto codeDto = new GetLoadDetailForYeliLifeCodeDto();
						codeDto.setLoadAmount(codeModel.getLoadAmount().stripTrailingZeros().toPlainString());
						if (codeModel.getUnit() != null) {
							codeDto.setUnit(codeModel.getUnit().toString());
						}
						codeDto.setUnloadAmount(codeModel.getUnloadAmount().stripTrailingZeros().toPlainString());
						codeDto.setYeloCode(codeModel.getYeloCode());
						codeDtoList.add(codeDto);
					}
					goodsDto.setCodeDtoList(codeDtoList);
					goodsList.add(goodsDto);
				}
			}
			destination.setGoodsList(goodsList);
		}
	}
}
