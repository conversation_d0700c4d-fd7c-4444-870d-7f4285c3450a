<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCompanyMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCompany" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="company_name" property="companyName" jdbcType="VARCHAR" />
    <result column="trading_certificate_image" property="tradingCertificateImage" jdbcType="VARCHAR" />
    <result column="trading_certificate_validity_time" property="tradingCertificateValidityTime" jdbcType="TIMESTAMP" />
    <result column="trading_certificate_is_forever" property="tradingCertificateIsForever" jdbcType="INTEGER" />
    <result column="trading_certificate_is_amend" property="tradingCertificateIsAmend" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, company_name, trading_certificate_image, trading_certificate_validity_time,
    trading_certificate_is_forever, trading_certificate_is_amend, created_by, created_time, 
    last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_company
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_company
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCompany" >
    insert into t_company (id, company_name,
      trading_certificate_image, trading_certificate_validity_time, 
      trading_certificate_is_forever, trading_certificate_is_amend, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{companyName,jdbcType=VARCHAR},
      #{tradingCertificateImage,jdbcType=VARCHAR}, #{tradingCertificateValidityTime,jdbcType=TIMESTAMP}, 
      #{tradingCertificateIsForever,jdbcType=INTEGER}, #{tradingCertificateIsAmend,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCompany" keyProperty="id" useGeneratedKeys="true">
    insert into t_company
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="companyName != null" >
        company_name,
      </if>
      <if test="tradingCertificateImage != null" >
        trading_certificate_image,
      </if>
      <if test="tradingCertificateValidityTime != null" >
        trading_certificate_validity_time,
      </if>
      <if test="tradingCertificateIsForever != null" >
        trading_certificate_is_forever,
      </if>
      <if test="tradingCertificateIsAmend != null" >
        trading_certificate_is_amend,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="companyName != null" >
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="tradingCertificateImage != null" >
        #{tradingCertificateImage,jdbcType=VARCHAR},
      </if>
      <if test="tradingCertificateValidityTime != null" >
        #{tradingCertificateValidityTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradingCertificateIsForever != null" >
        #{tradingCertificateIsForever,jdbcType=INTEGER},
      </if>
      <if test="tradingCertificateIsAmend != null" >
        #{tradingCertificateIsAmend,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCompany" >
    update t_company
    <set >
      <if test="companyName != null" >
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="tradingCertificateImage != null" >
        trading_certificate_image = #{tradingCertificateImage,jdbcType=VARCHAR},
      </if>
      <if test="tradingCertificateValidityTime != null" >
        trading_certificate_validity_time = #{tradingCertificateValidityTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradingCertificateIsForever != null" >
        trading_certificate_is_forever = #{tradingCertificateIsForever,jdbcType=INTEGER},
      </if>
      <if test="tradingCertificateIsAmend != null" >
        trading_certificate_is_amend = #{tradingCertificateIsAmend,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCompany" >
    update t_company
    set company_name = #{companyName,jdbcType=VARCHAR},
      trading_certificate_image = #{tradingCertificateImage,jdbcType=VARCHAR},
      trading_certificate_validity_time = #{tradingCertificateValidityTime,jdbcType=TIMESTAMP},
      trading_certificate_is_forever = #{tradingCertificateIsForever,jdbcType=INTEGER},
      trading_certificate_is_amend = #{tradingCertificateIsAmend,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>