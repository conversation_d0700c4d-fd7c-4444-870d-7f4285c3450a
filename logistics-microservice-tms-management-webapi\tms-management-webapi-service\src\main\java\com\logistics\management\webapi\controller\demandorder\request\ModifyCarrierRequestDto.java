package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class ModifyCarrierRequestDto {

    @ApiModelProperty(value = "需求单ID", required = true)
    @NotBlank(message = "需求单ID不能为空")
    private String demandOrderId;

    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主", required = true)
    @NotBlank(message = "请选择接单车主类型")
    private String isOurCompany;

    @ApiModelProperty(value = "车主ID")
    private String companyCarrierId;

    @ApiModelProperty(value = "运费")
    private String carrierPrice;

    @ApiModelProperty(value = "车主报价类型 1单价 2一口价")
    private String carrierPriceType;

    @ApiModelProperty(value = "修改原因", required = true)
    @NotBlank(message = "修改原因不能为空")
    @Size(min = 1, max = 300, message = "修改原因不能大于300")
    private String modifyReason;
}
