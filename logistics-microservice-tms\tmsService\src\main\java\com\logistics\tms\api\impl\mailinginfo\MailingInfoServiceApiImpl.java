package com.logistics.tms.api.impl.mailinginfo;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.mailinginfo.MailingInfoServiceApi;
import com.logistics.tms.api.feign.mailinginfo.model.request.MailingInfoDetailRequestModel;
import com.logistics.tms.api.feign.mailinginfo.model.request.MailingInfoListRequestModel;
import com.logistics.tms.api.feign.mailinginfo.model.request.MailingInfoModifyRequestModel;
import com.logistics.tms.api.feign.mailinginfo.model.response.MailingInfoDetailResponseModel;
import com.logistics.tms.api.feign.mailinginfo.model.response.MailingInfoListResponseModel;
import com.logistics.tms.biz.sysmailingaddress.SysMailingAddressBiz;
import com.yelo.tray.core.base.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class MailingInfoServiceApiImpl implements MailingInfoServiceApi {

    private final SysMailingAddressBiz sysMailingAddressBiz;

    /**
     * 邮寄信息配置列表
     * @param requestModel
     * @return PageInfo<MailingInfoListResponseModel>
     */
    @Override
    public Result<PageInfo<MailingInfoListResponseModel>> mailingInfoList(MailingInfoListRequestModel requestModel) {
        return Result.success(sysMailingAddressBiz.mailingInfoList(requestModel));
    }

    /**
     * 查询邮寄信息配置详情
     * @param requestModel
     * @return MailingInfoDetailResponseModel
     */
    @Override
    public Result<MailingInfoDetailResponseModel> mailingInfoDetail(MailingInfoDetailRequestModel requestModel) {
        return Result.success(sysMailingAddressBiz.mailingInfoDetail(requestModel));
    }

    /**
     * 邮寄信息地址修改
     * @param requestModel
     * @return Boolean
     */
    @Override
    public Result<Boolean> mailingInfoModify(MailingInfoModifyRequestModel requestModel) {
        return Result.success(sysMailingAddressBiz.mailingInfoModify(requestModel));
    }
}
