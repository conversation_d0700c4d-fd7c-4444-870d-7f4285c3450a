package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TicketsDto {
    @ApiModelProperty("图片Id")
    private String imageId;
    @ApiModelProperty("图片路径URL")
    private String imagePath;
    @ApiModelProperty("图片名")
    private String imageName;
    @ApiModelProperty("上传人姓名")
    private String uploadUserName;
    @ApiModelProperty("上传时间")
    private String uploadTime;
}
