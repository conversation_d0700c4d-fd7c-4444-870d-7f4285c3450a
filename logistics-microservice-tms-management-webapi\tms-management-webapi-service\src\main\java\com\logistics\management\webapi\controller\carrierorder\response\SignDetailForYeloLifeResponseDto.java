package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 新生签收详情
 *
 * <AUTHOR>
 * @date 2022/8/16 17:27
 */
@Data
public class SignDetailForYeloLifeResponseDto {

    @ApiModelProperty("运单ID")
    private String carrierOrderId = "";

    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";

    @ApiModelProperty("车辆")
    private String vehicleNo = "";

    @ApiModelProperty("司机")
    private String driver = "";

    @ApiModelProperty("发货地址")
    private String loadAddress = "";

    @ApiModelProperty("发货人")
    private String consignorName = "";

    @ApiModelProperty("发货人手机号")
    private String consignorMobile = "";

    @ApiModelProperty("收货地址")
    private String unloadAddress = "";

    @ApiModelProperty("收货人")
    private String receiverName = "";

    @ApiModelProperty("收货人手机号")
    private String receiverMobile = "";

    @ApiModelProperty("业务类型：1 公司，2 个人")
    private String businessType = "";

    /**
     * (3.23.0)车主运费
     */
    @ApiModelProperty("车主运费")
    private String carrierFreight = "";

    /**
     * (3.23.0)货主运费
     */
    @ApiModelProperty("货主运费")
    private String entrustFreightFee = "";

    @ApiModelProperty("司机实际运费")
    private String dispatchFreightFee = "";

    @ApiModelProperty("货物单位：1 件，2 吨，3 件（方），4 块")
    private String goodsUnitLabel = "";

    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
    private String isOurCompany = "";

    @ApiModelProperty("货物列表")
    private List<SignDetailForYeloLifeGoodsResponseDto> goodsList = new ArrayList<>();
}
