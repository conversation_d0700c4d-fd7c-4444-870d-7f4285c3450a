package com.logistics.tms.controller.carrierorderotherfee;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.carrierorderotherfee.CarrierOrderOtherFeeBiz;
import com.logistics.tms.controller.carrierorderotherfee.request.*;
import com.logistics.tms.controller.carrierorderotherfee.response.CarrierOrderOtherFeeDetailResponseModel;
import com.logistics.tms.controller.carrierorderotherfee.response.GetOtherFeeByCarrierOrderIdResponseModel;
import com.logistics.tms.controller.carrierorderotherfee.response.GetOtherFeeRecordResponseModel;
import com.logistics.tms.controller.carrierorderotherfee.response.SearchOtherFeeListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2023/10/26 11:24
 */
@RestController
@Api(value = "临时费用管理")
@RequestMapping(value = "/service/carrierOrderOtherFee")
public class CarrierOrderOtherFeeController {

    @Resource
    private CarrierOrderOtherFeeBiz carrierOrderOtherFeeBiz;

    /**
     * 临时费用列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "临时费用列表")
    @PostMapping(value = "/searchList")
    public Result<PageInfo<SearchOtherFeeListResponseModel>> searchList(@RequestBody SearchOtherFeeListRequestModel requestModel) {
        return Result.success(carrierOrderOtherFeeBiz.searchList(requestModel));
    }

    /**
     * 新增临时费用
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "新增临时费用")
    @PostMapping(value = "/addCarrierOrderOtherFee")
    public Result<Boolean> addCarrierOrderOtherFee(@RequestBody AddCarrierOrderOtherFeeRequestModel requestModel) {
        return Result.success(carrierOrderOtherFeeBiz.addCarrierOrderOtherFee(requestModel));
    }

    /**
     * 临时费用详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "临时费用详情")
    @PostMapping(value = "/getCarrierOrderOtherFeeDetail")
    public Result<CarrierOrderOtherFeeDetailResponseModel> getCarrierOrderOtherFeeDetail(@RequestBody CarrierOrderOtherFeeDetailRequestModel requestModel) {
        return Result.success(carrierOrderOtherFeeBiz.getCarrierOrderOtherFeeDetail(requestModel));
    }

    /**
     * 审核/提交临时费用
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "审核/提交临时费用")
    @PostMapping(value = "/commitCarrierOrderOtherFee")
    public Result<Boolean> commitCarrierOrderOtherFee(@RequestBody CommitCarrierOrderOtherFeeRequestModel requestModel) {
        return Result.success(carrierOrderOtherFeeBiz.commitCarrierOrderOtherFee(requestModel));
    }

    /**
     * 驳回临时费用
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "审核驳回临时费用")
    @PostMapping(value = "/rejectOtherFee")
    public Result<Boolean> rejectOtherFee(@RequestBody RejectCarrierOrderOtherFeeRequestModel requestModel) {
        return Result.success(carrierOrderOtherFeeBiz.rejectOtherFee(requestModel));
    }

    /**
     * 撤销
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "撤销")
    @PostMapping(value = "/cancelOtherFee")
    public Result<Boolean> cancelOtherFee(@RequestBody CarrierOrderOtherFeeDetailRequestModel requestModel) {
        return Result.success(carrierOrderOtherFeeBiz.cancelOtherFee(requestModel));
    }

    /**
     * 操作日志查询
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查看操作日志")
    @PostMapping(value = "/getOtherFeeRecord")
    public Result<List<GetOtherFeeRecordResponseModel>> getOtherFeeRecord(@RequestBody CarrierOrderOtherFeeDetailRequestModel requestModel) {
        return Result.success(carrierOrderOtherFeeBiz.getOtherFeeRecord(requestModel));
    }

    /**
     * 小程序-根据运单id查询【待提交】状态最新一条临时费用详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "小程序-根据运单id查询【待提交】状态最新一条临时费用详情")
    @PostMapping(value = "/getOtherFeeByCarrierOrderId")
    public Result<GetOtherFeeByCarrierOrderIdResponseModel> getOtherFeeByCarrierOrderId(@RequestBody GetOtherFeeByCarrierOrderIdRequestModel requestModel) {
        return Result.success(carrierOrderOtherFeeBiz.getOtherFeeByCarrierOrderId(requestModel));
    }

    /**
     * 回退
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "回退")
    @PostMapping(value = "/rollbackOtherFee")
    public Result<Boolean> rollbackOtherFee(@RequestBody CarrierOrderOtherFeeDetailRequestModel requestModel) {
        carrierOrderOtherFeeBiz.rollbackOtherFee(requestModel);
        return Result.success(true);
    }
}
