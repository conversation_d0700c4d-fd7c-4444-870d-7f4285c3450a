package com.logistics.management.webapi.controller.drivercostapply;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.ConvertPageInfoUtil;
import com.logistics.management.webapi.base.utils.ExportUtils;
import com.logistics.management.webapi.client.drivercostapply.DriverCostApplyClient;
import com.logistics.management.webapi.client.drivercostapply.request.*;
import com.logistics.management.webapi.client.drivercostapply.response.*;
import com.logistics.management.webapi.controller.drivercostapply.mapping.DriverCostApplyDetailMapping;
import com.logistics.management.webapi.controller.drivercostapply.mapping.SearchCostApplyListMapping;
import com.logistics.management.webapi.controller.drivercostapply.mapping.SearchCostApplySummaryMapping;
import com.logistics.management.webapi.controller.drivercostapply.request.*;
import com.logistics.management.webapi.controller.drivercostapply.response.DriverCostApplyDetailResponseDto;
import com.logistics.management.webapi.controller.drivercostapply.response.GetDeductionsCostBalanceResponseDto;
import com.logistics.management.webapi.controller.drivercostapply.response.SearchCostApplyListResponseDto;
import com.logistics.management.webapi.controller.drivercostapply.response.SearchCostApplySummaryResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Api(value = "费用申请列表",tags = "费用申请列表")
@RestController
@RequestMapping(value = "/api/driverCostApply")
public class DriverCostApplyController {

    @Resource
    private CommonBiz commonBiz;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private DriverCostApplyClient driverCostApplyClient;

    /**
     * 费用申请列表
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "费用申请列表", tags = "3.17.0")
    @PostMapping(value = "/searchCostApplyList")
    public Result<PageInfo<SearchCostApplyListResponseDto>> searchCostApplyList(@RequestBody SearchCostApplyListRequestDto requestDto) {
        SearchCostApplyListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchCostApplyListRequestModel.class);
        Result<PageInfo<SearchCostApplyListResponseModel>> result = driverCostApplyClient.searchCostApplyList(requestModel);
        result.throwException();
        var pageInfo = ConvertPageInfoUtil.convertPageInfo(result.getData(),
                SearchCostApplyListResponseDto.class,
                new SearchCostApplyListMapping());
        return Result.success(pageInfo);
    }

    /**
     * 导出费用申请列表
     * @param requestDto
     */
    @ApiOperation(value = "导出费用申请列表", tags = "3.17.0")
    @GetMapping(value = "/exportCostApplyList")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportCostApplyList(SearchCostApplyListRequestDto requestDto, HttpServletResponse response) {
        requestDto.setPageNum(CommonConstant.INTEGER_ZERO);
        requestDto.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<SearchCostApplyListResponseModel>> result = driverCostApplyClient.searchCostApplyList(MapperUtils.mapper(requestDto, SearchCostApplyListRequestModel.class));
        result.throwException();
        List<SearchCostApplyListResponseModel> resultList = result.getData().getList();
        List<SearchCostApplyListResponseDto> list = MapperUtils.mapper(resultList, SearchCostApplyListResponseDto.class, new SearchCostApplyListMapping());

        String fileName = "司机费用申请列表" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        ExportUtils.exportByYeloExcel(response, list, SearchCostApplyListResponseDto.class, fileName);
    }

    /**
     * 申请记录详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "申请记录详情", tags = "3.17.0")
    @PostMapping(value = "/driverCostApplyDetail")
    public Result<DriverCostApplyDetailResponseDto> driverCostApplyDetail(@RequestBody @Valid DriverCostApplyDetailRequestDto requestDto) {
        Result<DriverCostApplyDetailResponseModel> result = driverCostApplyClient.driverCostApplyDetail(MapperUtils.mapper(requestDto, DriverCostApplyDetailRequestModel.class));
        result.throwException();
        List<String> ticketList = result.getData().getTicketList().stream().map(DriverCostApplyTicketModel::getFilePath).collect(Collectors.toList());
        if (ListUtils.isNotEmpty(result.getData().getInvoiceInfoList())) {
            List<String> invoiceInfoList = result.getData().getInvoiceInfoList().stream().map(DriverCostApplyInvoiceResponseModel::getImagePath).collect(Collectors.toList());
            ticketList.addAll(invoiceInfoList);
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(ticketList);
        return Result.success(MapperUtils.mapper(result.getData(), DriverCostApplyDetailResponseDto.class, new DriverCostApplyDetailMapping(configKeyConstant, imageMap)));
    }

    /**
     * 撤销费用申请
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "撤销费用申请 v1.2.0")
    @PostMapping(value = "/undoDriverCostApply")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> undoDriverCostApply(@RequestBody @Valid UndoDriverCostApplyRequestDto requestDto) {
        UndoDriverCostApplyRequestModel requestModel = MapperUtils.mapper(requestDto, UndoDriverCostApplyRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        Result<Boolean> result = driverCostApplyClient.undoDriverCostApply(requestModel);
        result.throwException();
        return Result.success(true);
    }

    /**
     * 审核/驳回费用申请
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "审核/驳回费用申请", tags = "3.17.0")
    @PostMapping(value = "/auditOrRejectCostApply")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> auditOrRejectCostApply(@RequestBody @Valid AuditOrRejectCostApplyRequestDto requestDto) {
        if (CommonConstant.TWO.equals(requestDto.getOperateType())){
            if (StringUtils.isBlank(requestDto.getRemark())){
                throw new BizException(ManagementWebApiExceptionEnum.REMARK_EMPTY);
            }
        } else if (!CommonConstant.ONE.equals(requestDto.getOperateType())){
            throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
        }
        return driverCostApplyClient.auditOrRejectCostApply(MapperUtils.mapper(requestDto, AuditOrRejectCostApplyRequestModel.class));
    }

    /**
     * 费用申请汇总列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "费用申请汇总列表", tags = "3.17.0")
    @PostMapping(value = "/searchCostApplySummary")
    public Result<PageInfo<SearchCostApplySummaryResponseDto>> searchCostApplySummary(@RequestBody SearchCostApplySummaryRequestDto requestDto) {
        Result<PageInfo<SearchCostApplySummaryResponseModel>> result = driverCostApplyClient.searchCostApplySummary(MapperUtils.mapper(requestDto, SearchCostApplySummaryRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SearchCostApplySummaryResponseDto> resultList = MapperUtils.mapper(pageInfo.getList(), SearchCostApplySummaryResponseDto.class, new SearchCostApplySummaryMapping());
        pageInfo.setList(resultList);
        return Result.success(pageInfo);
    }

    /**
     * 导出费用申请汇总列表
     *
     * @param requestDto
     * @param response
     */
    @ApiOperation(value = "导出费用申请汇总列表", tags = "1.3.6")
    @GetMapping(value = "/exportCostApplySummary")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportCostApplySummary(SearchCostApplySummaryRequestDto requestDto, HttpServletResponse response) {
        requestDto.setPageNum(CommonConstant.INTEGER_ZERO);
        requestDto.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<SearchCostApplySummaryResponseModel>> result = driverCostApplyClient.searchCostApplySummary(MapperUtils.mapper(requestDto, SearchCostApplySummaryRequestModel.class));
        result.throwException();
        PageInfo<SearchCostApplySummaryResponseModel> resultList = result.getData();
        List<SearchCostApplySummaryResponseDto> list = MapperUtils.mapper(resultList.getList(), SearchCostApplySummaryResponseDto.class, new SearchCostApplySummaryMapping());

        String fileName = "出车费用表" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        ExportUtils.exportByYeloExcel(response, list, SearchCostApplySummaryResponseDto.class, fileName);
    }

    @ApiOperation(value = "扣款费用申请", tags = "1.3.6")
    @PostMapping(value = "/deductionsCostApply")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> deductionsCostApply(@Valid @RequestBody DeductionsCostApplyRequestDto requestDto) {
        DeductionsCostApplyRequestModel requestModel = MapperUtils.mapper(requestDto, DeductionsCostApplyRequestModel.class);
        return driverCostApplyClient.deductionsCostApply(requestModel);
    }

    @ApiOperation(value = "查询司机扣款余额", tags = "1.3.6")
    @PostMapping(value = "/getDeductionsCostBalance")
    public Result<List<GetDeductionsCostBalanceResponseDto>> getDeductionsCostBalance(@Valid @RequestBody GetDeductionsCostBalanceRequestDto requestDto) {
        Result<List<GetDeductionsCostBalanceResponseModel>> deductionsCostBalance =
                driverCostApplyClient.getDeductionsCostBalance(MapperUtils.mapper(requestDto, GetDeductionsCostBalanceRequestModel.class));
        deductionsCostBalance.throwException();
        return Result.success(MapperUtils.mapper(deductionsCostBalance.getData(),GetDeductionsCostBalanceResponseDto.class));
    }

    @ApiOperation(value = "费用申请红冲退款", tags = "1.3.6")
    @PostMapping(value = "/redChargeRefund")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> redChargeRefund(@Valid @RequestBody RedChargeRefundCostApplyRequestDto requestDto) {
        RedChargeRefundCostApplyRequestModel requestModel = MapperUtils.mapper(requestDto, RedChargeRefundCostApplyRequestModel.class);
        return driverCostApplyClient.redChargeRefund(requestModel);
    }
}
