package com.logistics.management.webapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class LoadRequestModel {
    @ApiModelProperty(value = "运单ID")
    private Long carrierOrderId;
    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("提货时间")
    private Date loadTime;
    @ApiModelProperty("票据临时路径")
    private List<String> tmpUrl;
    @ApiModelProperty("货物信息")
    private List<CarrierOrderNodeGoodsRequestModel> goodsList;

}
