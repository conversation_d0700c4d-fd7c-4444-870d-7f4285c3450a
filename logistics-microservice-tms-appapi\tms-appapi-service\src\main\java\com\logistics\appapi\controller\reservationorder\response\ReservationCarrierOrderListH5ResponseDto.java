package com.logistics.appapi.controller.reservationorder.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/8/14 14:25
 */
@Data
public class ReservationCarrierOrderListH5ResponseDto {

    /**
     * 运单id
     */
    private String carrierOrderId="";

    /**
     * 运单号
     */
    private String carrierOrderCode="";

    /**
     * 需求类型
     */
    private String entrustType ="";

    /**
     * 需求类型
     */
    private String entrustTypeLabel="";

    /**
     * 运单状态
     */
    private String statusLabel="";

    /**
     * 预计提货时间/预计卸货时间
     */
    private String expectedTime="";

    /**
     * 运单预计数量
     */
    private String expectAmount="";

    //发货地/收货地，借用字段
    @JsonIgnore
    private String address="";

}
