package com.logistics.tms.base.enums;


public enum AccountUserRoleTypeEnum {
    DRIVER_APPLET(1,"司机"),
    CARRIER(2, "车主"),

    ;
    private Integer key;
    private String value;

    AccountUserRoleTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static AccountUserRoleTypeEnum getEnum(Integer key) {
        for (AccountUserRoleTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }

}
