<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleTireMapper" >
    <select id="searchVehicleTireIdsList" resultType="java.lang.Long">
        select distinct
        tqvt.id as vehicleTireId
        from t_vehicle_tire tqvt
        left join t_vehicle_tire_no tqvtn on tqvtn.tire_id=tqvt.id and tqvtn.valid=1
        left join t_staff_basic tqsb on tqsb.id = tqvt.staff_id and tqsb.valid = 1 and tqsb.type in (1,3)
        left join t_vehicle_driving_license tqvd on tqvd.vehicle_id = tqvt.vehicle_id and tqvd.valid = 1
        where tqvt.valid = 1
        <if test="params.vehicleNo!=null and params.vehicleNo!=''">
            and instr(tqvd.vehicle_no,#{params.vehicleNo,jdbcType = VARCHAR})
        </if>
        <if test="params.driverName!=null and params.driverName!='' ">
            and (instr(tqsb.name,#{params.driverName,jdbcType = VARCHAR}) or instr(tqsb.mobile,#{params.driverName,jdbcType = VARCHAR}))
        </if>
        <if test="params.tireBrand!=null and params.tireBrand!=''">
            and instr(tqvtn.tire_brand,#{params.tireBrand,jdbcType = VARCHAR})
        </if>
        <if test="params.replaceDateFrom!=null and params.replaceDateFrom!=''">
            and tqvt.replace_date >= DATE_FORMAT(#{params.replaceDateFrom,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00')
        </if>
        <if test="params.replaceDateTo!=null and params.replaceDateTo!=''">
            and tqvt.replace_date &lt;= DATE_FORMAT(#{params.replaceDateTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.lastModifiedTimeFrom!=null and params.lastModifiedTimeFrom!=''" >
            and tqvt.last_modified_time >= DATE_FORMAT(#{params.lastModifiedTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00')
        </if>
        <if test="params.lastModifiedTimeTo!=null and params.lastModifiedTimeTo!=''" >
            and tqvt.last_modified_time &lt;= DATE_FORMAT(#{params.lastModifiedTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.lastModifiedBy!=null and params.lastModifiedBy!=''" >
            and instr(tqvt.last_modified_by,#{params.lastModifiedBy,jdbcType = VARCHAR})
        </if>
        <if test="params.settlementStatus != null">
            and tqvt.settlement_status = #{params.settlementStatus,jdbcType=INTEGER}
        </if>
        <if test="params.vehicleProperty != null">
            and tqvt.vehicle_property = #{params.vehicleProperty,jdbcType=INTEGER}
        </if>
        order by tqvt.last_modified_time desc,tqvt.id desc
    </select>

    <select id="searchVehicleTireList" resultMap="VehicleTireListMap">
    select
    tqvt.id as vehicleTireId,
    tqvt.settlement_status,
    tqvt.replace_date,
    tqvt.tire_company,
    tqvt.last_modified_by,
    tqvt.last_modified_time,
    tqvt.remark,
    tqvt.vehicle_property,
    tqvtn.id as vehicleTrieNoId,
    tqvtn.amount,
    tqvtn.unit_price,
    tqvtn.tire_brand,
    tqsb.name,
    tqsb.mobile,
    tqvd.vehicle_no
    from t_vehicle_tire tqvt
    left join t_vehicle_tire_no tqvtn on tqvtn.tire_id=tqvt.id and tqvtn.valid=1
    left join t_staff_basic tqsb on tqsb.id = tqvt.staff_id and tqsb.valid = 1 and tqsb.type in (1,3)
    left join t_vehicle_driving_license tqvd on tqvd.vehicle_id = tqvt.vehicle_id and tqvd.valid = 1
    where tqvt.id in (${ids}) and tqvt.valid=1
     order by tqvt.last_modified_time desc,tqvt.id desc
    </select>
    <resultMap id="VehicleTireListMap" type="com.logistics.tms.api.feign.vehicletire.model.VehicleTireListResponseModel">
        <id column="vehicleTireId" property="vehicleTireId" jdbcType="BIGINT"/>
        <result column="settlement_status" property="settlementStatus" jdbcType="INTEGER"/>
        <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="name" property="driverName" jdbcType="VARCHAR"/>
        <result column="mobile" property="driveMobile" jdbcType="VARCHAR"/>
        <result column="tire_company" property="tireCompany" jdbcType="VARCHAR"/>
        <result column="last_Modified_By" property="lastModifiedBy" jdbcType="VARCHAR"/>
        <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP"/>
        <result column="replace_date" property="replaceDate" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="vehicle_property" property="vehicleProperty" jdbcType="INTEGER"/>
        <collection property="vehicleTireNoList" ofType="com.logistics.tms.api.feign.vehicletire.model.VehicleTireNoListResponseModel">
            <id column="vehicleTrieNoId" property="vehicleTrieNoId" jdbcType="BIGINT" />
            <result column="tire_brand" property="tireBrand" jdbcType="VARCHAR"/>
            <result column="amount" property="amount" jdbcType="INTEGER"/>
            <result column="unit_price" property="unitPrice" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>

    <select id="getVehicleTireDetailById" resultMap="getVehicleTireDetailMap">
        select
        tqvt.id as vehicleTireId,
        tqvt.vehicle_id ,
        tqvt.trailer_vehicle_Id,
        tqvt.staff_id,
        tqvt.remark,
        tqvt.replace_date,
        tqvt.tire_company,
        tqvt.last_modified_time,
        tqvtn.id as vehicleTrieNoId,
        tqvtn.amount,
        tqvtn.unit_price,
        tqvtn.tire_brand,
        tqsb.name,
        tqsb.mobile,
        tqvd.vehicle_no,
        tqcp.id as fileId,
        tqcp.file_path
        from t_vehicle_tire tqvt
        left join t_vehicle_tire_no tqvtn on tqvtn.tire_id=tqvt.id and tqvtn.valid=1
        left join t_staff_basic tqsb on tqsb.id = tqvt.staff_id and tqsb.valid = 1 and tqsb.type in (1,3)
        left join t_vehicle_driving_license tqvd on tqvd.vehicle_id = tqvt.vehicle_id and tqvd.valid = 1
        left join t_certification_pictures tqcp on tqcp.object_id = tqvt.id and tqcp.object_type = 16  and tqcp.valid = 1
        where tqvt.id = #{vehicleTireId,jdbcType=BIGINT} and tqvt.valid=1
        order by tqvtn.id asc,tqcp.id asc
    </select>
    <resultMap id="getVehicleTireDetailMap" type="com.logistics.tms.api.feign.vehicletire.model.VehicleTireDetailResponseModel">
        <id column="vehicleTireId" property="vehicleTireId" jdbcType="BIGINT"/>
        <result column="vehicle_id" property="vehicleId" jdbcType="BIGINT"/>
        <result column="trailer_vehicle_Id" property="trailerVehicleId" jdbcType="BIGINT"/>
        <result column="staff_id" property="staffId" jdbcType="BIGINT"/>
        <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="name" property="driverName" jdbcType="VARCHAR"/>
        <result column="mobile" property="driveMobile" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="tire_company" property="tireCompany" jdbcType="VARCHAR"/>
        <result column="replace_date" property="replaceDate" jdbcType="TIMESTAMP"/>
        <collection property="vehicleTireNoList" ofType="com.logistics.tms.api.feign.vehicletire.model.VehicleTireNoListResponseModel">
            <id column="vehicleTrieNoId" property="vehicleTrieNoId" jdbcType="BIGINT"/>
            <result column="tire_brand" property="tireBrand" jdbcType="VARCHAR"/>
            <result column="amount" property="amount" jdbcType="INTEGER"/>
            <result column="unit_price" property="unitPrice" jdbcType="DECIMAL"/>
        </collection>
        <collection property="fileList" ofType="com.logistics.tms.api.feign.vehicletire.model.CertificationPicturesResponseModel">
            <id column="fileId" property="fileId" jdbcType="BIGINT" />
            <result column="file_path" property="relativeFilepath" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <resultMap id="getByTireIdOrVehicleId_Map" type="com.logistics.tms.api.feign.vehicletire.model.GetTireByTireIdsModel">
        <id column="id" property="vehicleTireId" jdbcType="BIGINT"/>
        <id column="vehicle_id" property="vehicleId" jdbcType="BIGINT"/>
        <id column="staff_id" property="staffId" jdbcType="BIGINT"/>
        <collection property="tireNoList" ofType="com.logistics.tms.api.feign.vehicletire.model.GetTireNoByTireIdsModel">
            <id column="vehicleTrieNoId" property="vehicleTrieNoId" jdbcType="BIGINT"/>
            <result column="tireBrand" property="tireBrand" jdbcType="VARCHAR"/>
            <result column="createdTime" property="createdTime" jdbcType="TIMESTAMP"/>
        </collection>
    </resultMap>
    <select id="getByTireIdOrVehicleId" resultMap="getByTireIdOrVehicleId_Map">
        select
        tvt.id,
        tvt.vehicle_id,
        tvt.staff_id,
        tvtn.id as vehicleTrieNoId,
        tvtn.tire_brand as tireBrand,
        tvtn.created_time as createdTime
        from t_vehicle_tire tvt
        left join t_vehicle_tire_no tvtn on tvtn.tire_id = tvt.id and tvtn.valid = 1
        where tvt.valid = 1
        <if test="id != null">
            and tvt.id = #{id,jdbcType=BIGINT}
        </if>
        <if test="vehicleId != null">
            and tvt.vehicle_id = #{vehicleId,jdbcType=BIGINT}
        </if>
        <if test="replaceDate != null">
            and tvt.replace_date = #{replaceDate,jdbcType=TIMESTAMP}
        </if>
        order by tvtn.created_time asc,tvtn.id asc
    </select>

    <update id="batchUpdate" parameterType="com.logistics.tms.entity.TVehicleTire" >
        <foreach collection="list" item="item" separator=";">
            update t_vehicle_tire
            <set >
                <if test="item.vehicleId != null" >
                    vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleProperty != null" >
                    vehicle_property = #{item.vehicleProperty,jdbcType=INTEGER},
                </if>
                <if test="item.staffId != null" >
                    staff_id = #{item.staffId,jdbcType=BIGINT},
                </if>
                <if test="item.replaceDate != null" >
                    replace_date = #{item.replaceDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.tireCompany != null" >
                    tire_company = #{item.tireCompany,jdbcType=VARCHAR},
                </if>
                <if test="item.settlementStatus != null">
                    settlement_status = #{item.settlementStatus,jdbcType=INTEGER},
                </if>
                <if test="item.remark != null" >
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getVehicleTireByIdsForSettlement" resultType="com.logistics.tms.controller.vehiclesettlement.response.GetVehicleTireByVehicleIdResponseModel">
        select
        tvt.id as vehicleTireId,
        tvt.replace_date as replaceDate,
        tvt.tire_company as tireCompany,
        tvtn.amount,
        tvtn.unit_price as unitPrice,
        tvtn.tire_brand as tireBrand,
        tsb.name as driverName,
        tsb.mobile as driverMobile,
        tvd.vehicle_no as vehicleNo
        from t_vehicle_tire tvt
        left join t_vehicle_tire_no tvtn on tvtn.tire_id = tvt.id and tvtn.valid = 1
        left join t_staff_basic tsb on tsb.id = tvt.staff_id and tsb.valid = 1 and tsb.type in (1,3)
        left join t_vehicle_driving_license tvd on tvd.vehicle_id = tvt.vehicle_id and tvd.valid = 1
        where tvt.valid = 1
        and tvt.id in (${ids})
        order by tvt.created_time desc,tvt.id desc,tvtn.id desc
    </select>

    <select id="getVehicleBySettlementMonth" resultType="com.logistics.tms.controller.vehiclesettlement.response.GetVehicleBySettlementMonthModel">
        select
        vehicle_id as vehicleId,
        id as objectId
        from t_vehicle_tire
        where valid = 1
        and date_format(created_time,'%Y-%m') = #{settlementMonth,jdbcType=VARCHAR}
    </select>

    <update id="settlementOilFilledByIds">
        update t_vehicle_tire set
        settlement_status = 1,
        last_modified_by = #{userName,jdbcType=VARCHAR},
        last_modified_time = now()
        where valid = 1
        and id in (${ids})
    </update>
    <update id="rollbackSettlementOilFilledByIds">
        update t_vehicle_tire set
        settlement_status = 0,
        last_modified_by = #{userName,jdbcType=VARCHAR},
        last_modified_time = now()
        where valid = 1
        and id in (${ids})
    </update>
    <select id="getByIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_vehicle_tire
        where valid=1 and id in (${ids});
    </select>
</mapper>