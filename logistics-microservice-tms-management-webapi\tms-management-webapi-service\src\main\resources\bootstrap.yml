feign:
  sentinel:
    enabled: true

server:
  shutdown: graceful

spring:
  lifecycle:
    timeout-per-shutdown-phase: 30s
  application:
    name: logistics-tms-managementWeb-api
    platform: logistics_tms
  profiles:
    active: ${env}
  cloud:
    nacos:
      password: ${NACOS-PASSWORD:nacos}
      username: ${NACOS-USERNAME:nacos}
      discovery:
        namespace: ${env}
        server-addr: ${NACOS-HOST:${env}.nacos.yelopack.com}:${NACOS-PORT:8848}
        group: ${REGISTRY_GROUP:DEFAULT_GROUP}
      config:
        username: ${spring.cloud.nacos.username}
        password: ${spring.cloud.nacos.password}
        namespace: ${env}
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        group: ${CONFIG_GROUP:LOGISTICS_GROUP}
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  servlet:
    multipart:
      max-request-size: 10MB
      max-file-size: 10MB

management:
  endpoint:
    health:
      sensitive: false
      show-details: always
    shutdown:
      enabled: true #启用shutdown
      sensitive: false #禁用密码验证
    loggers:
      enabled: true #启用loggers
      sensitive: false #禁用密码验证
    service-registry:
      enabled: true
  endpoints:
    web:
      exposure:
        include: shutdown,refresh,health,info,prometheus,service-registry,deregister

auth:
  serviceId: management-auth-server
  client:
    id: logistics-tms-managementWeb-api
    token-header: token
    pub-key:
      path: client/pub.key
  filter:
    excludePathPatterns: /cache,/error
