<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TVehicleSafeCheckVehicleMapper">

     <select id="searchList" resultType="com.logistics.tms.controller.vehiclesafecheck.response.SearchSafeCheckListResponseModel">
         select
         tvc.period             as period,
         tvv.id                 as safeCheckVehicleId,
         tvv.status             as status,
         tvv.check_user_name    as checkUserName,
         tvv.check_time         as checkTime,
         tvv.vehicle_no         as vehicleNo,
         tvv.trailer_vehicle_no as trailerVehicleNo,
         tvv.staff_name         as staffName,
         tvv.staff_mobile       as staffMobile,
         tvv.vehicle_property   as vehicleProperty,
         tvr.reform_count       as reformCount
         from t_vehicle_safe_check_vehicle tvv
         left join t_vehicle_safe_check tvc on tvc.id = tvv.safe_check_id and tvc.valid = 1
         left join t_vehicle_safe_check_reform tvr on tvr.safe_check_vehicle_id = tvv.id and tvr.valid = 1
         where tvv.valid = 1
         <if test="params.status!=null">
             and tvv.status =#{params.status,jdbcType = INTEGER}
         </if>
         <if test="params.ids!=null and params.ids!=''">
             and tvv.id in (${ids})
         </if>
         <if test="params.periodStart!=null and params.periodStart!='' ">
             and tvc.period >= #{params.periodStart,jdbcType = VARCHAR}
         </if>
         <if test="params.periodEnd!=null and params.periodEnd!=''">
            and tvc.period &lt;= #{params.periodEnd,jdbcType = VARCHAR}
         </if>
         <if test="params.vehicleNo!=null and params.vehicleNo!=''">
             and instr(tvv.vehicle_no,#{params.vehicleNo,jdbcType = VARCHAR})>0

         </if>
         <if test="params.trailerVehicleNo!=null and params.trailerVehicleNo!='' ">
             and instr(tvv.trailer_vehicle_no,#{params.trailerVehicleNo,jdbcType = VARCHAR})>0

         </if>
         <if test="params.staffName!=null and params.staffName!=''">
            and instr(tvv.staff_name,#{params.staffName,jdbcType = VARCHAR}) > 0
         </if>
         <if test="params.staffMobile!=null and params.staffMobile!=''">
            and instr(tvv.staff_mobile,#{params.staffMobile,jdbcType = VARCHAR}) > 0
         </if>
         <if test="params.vehicleProperty != null">
             and tvv.vehicle_property = #{params.vehicleProperty,jdbcType=INTEGER}
         </if>
         order by tvc.period desc, tvv.check_time desc,tvv.created_time desc,tvv.id desc
     </select>

    <select id="getListSummary" resultType="com.logistics.tms.controller.vehiclesafecheck.response.SummarySafeCheckResponseModel">
        select
          ifnull(sum(if(tvv.status = 0,1,0)),0) as notCheckCount,
          ifnull(sum(if(tvv.status = 10,1,0)),0) as waitConfirmCount,
          ifnull(sum(if(tvv.status = 20,1,0)),0) as waitReformCount,
          ifnull(sum(if(tvv.status = 30,1,0)),0) as hasReformCount,
          ifnull(sum(if(tvv.status = 40,1,0)),0) as hasCheckCount

        from t_vehicle_safe_check_vehicle tvv
        left join t_vehicle_safe_check tvc on tvc.id =  tvv.safe_check_id and tvc.valid =1
        left join t_vehicle_safe_check_reform tvr on tvr.safe_check_vehicle_id = tvv.id and tvr.valid = 1
        where tvv.valid = 1
        <if test="params.periodStart!=null and params.periodStart!='' ">
            and tvc.period >= #{params.periodStart,jdbcType = VARCHAR}
        </if>
        <if test="params.periodEnd!=null and params.periodEnd!=''">
            and tvc.period &lt;= #{params.periodEnd,jdbcType = VARCHAR}
        </if>
        <if test="params.vehicleNo!=null and params.vehicleNo!=''">
            and instr(tvv.vehicle_no,#{params.vehicleNo,jdbcType = VARCHAR})>0
        </if>
        <if test="params.trailerVehicleNo!=null and params.trailerVehicleNo!='' ">
            and instr(tvv.trailer_vehicle_no,#{params.trailerVehicleNo,jdbcType = VARCHAR})>0
        </if>
        <if test="params.staffName!=null and params.staffName!=''">
            and instr(tvv.staff_name,#{params.staffName,jdbcType = VARCHAR}) > 0
        </if>
        <if test="params.staffMobile!=null and params.staffMobile!=''">
            and instr(tvv.staff_mobile,#{params.staffMobile,jdbcType = VARCHAR}) > 0
        </if>
        <if test="params.vehicleProperty != null">
            and tvv.vehicle_property = #{params.vehicleProperty,jdbcType=INTEGER}
        </if>
    </select>

    <resultMap id="checkReformInfoMap" type="com.logistics.tms.controller.vehiclesafecheck.response.SafeCheckReformResponseModel">
        <id column="checkReformId" property="checkReformId" jdbcType="BIGINT"/>
        <result column="reformCount" property="reformCount" jdbcType="INTEGER"/>
        <result column="reformContent" property="reformContent" jdbcType="VARCHAR"/>
        <result column="reformResult" property="reformResult" jdbcType="VARCHAR"/>
        <result column="addReformResultTime" property="addReformResultTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="detailMap" type="com.logistics.tms.controller.vehiclesafecheck.response.SafeCheckDetailResponseModel">
        <id column="safeCheckVehicleId" property="safeCheckVehicleId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="checkTime" property="checkTime" jdbcType="TIMESTAMP"/>
        <result column="checkUserId" property="checkUserId" jdbcType="BIGINT"/>
        <result column="checkUserName" property="checkUserName" jdbcType="VARCHAR"/>
        <result column="vehicleId" property="vehicleId" jdbcType="BIGINT"/>
        <result column="vehicleNo" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="trailerVehicleNo" property="trailerVehicleNo" jdbcType="VARCHAR"/>
        <result column="staffName" property="staffName" jdbcType="VARCHAR"/>
        <result column="staffMobile" property="staffMobile" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <association property="checkReformInfo" resultMap="checkReformInfoMap"/>

        <collection property="fileList" ofType="com.logistics.tms.controller.vehiclesafecheck.response.SafeCheckFileResponseModel">
            <id column="fileId" property="fileId" jdbcType="BIGINT"/>
            <result column="relativeFilepath" property="relativeFilepath" jdbcType="VARCHAR"/>
        </collection>

        <collection property="itemList" ofType="com.logistics.tms.controller.vehiclesafecheck.response.SafeCheckItemResponseModel">
            <id column="checkItemId" property="checkItemId" jdbcType="BIGINT"/>
            <result column="itemType" property="itemType" jdbcType="INTEGER"/>
            <result column="itemStatus" property="itemStatus" jdbcType="INTEGER"/>
        </collection>
    </resultMap>

    <select id="getDetail" resultMap="detailMap">
       select
         tvv.id as safeCheckVehicleId,
         tvv.status as status,
         tvv.check_time as checkTime,
         tvv.check_user_id as checkUserId,
         tvv.check_user_name as checkUserName,
         tvv.vehicle_id as vehicleId,
         tvv.vehicle_no as vehicleNo,
         tvv.trailer_vehicle_no as trailerVehicleNo,
         tvv.staff_name as staffName,
         tvv.staff_mobile as staffMobile,
         tvv.remark as remark,

         tcp.id as fileId,
         tcp.file_path as relativeFilepath,

         item.id as checkItemId,
         item.item_type as itemType,
         item.status as itemStatus,

         reform.id as checkReformId,
         reform.reform_count as reformCount,
         reform.reform_content as reformContent,
         reform.reform_result as reformResult,
         reform.add_reform_result_time as addReformResultTime

       from t_vehicle_safe_check_vehicle tvv
       left join t_certification_pictures tcp on tcp.object_type = #{objectType,jdbcType= INTEGER} and tcp.object_id = tvv.id and tcp.valid = 1
       left join t_vehicle_safe_check_item item on item.safe_check_vehicle_id  = tvv.id and item.valid = 1
       left join t_vehicle_safe_check_reform reform on reform.safe_check_vehicle_id =  tvv.id and reform.valid =1
       where tvv.valid =1
       and tvv.id = #{safeCheckVehicleId,jdbcType = BIGINT}
    </select>

    <update id="delSafeCheck">
      update t_vehicle_safe_check_vehicle vehicle
      left join t_vehicle_safe_check_item item on item.safe_check_vehicle_id = vehicle.id and item.valid = 1
      left join t_vehicle_safe_check_reform reform on reform.safe_check_vehicle_id = vehicle.id and reform.valid = 1
      set vehicle.valid = 0 ,item.valid = 0,reform.valid = 0 ,
      vehicle.last_modified_by = #{modifiedBy,jdbcType=VARCHAR},vehicle.last_modified_time = #{modifiedTime,jdbcType=TIMESTAMP},
      item.last_modified_by = #{modifiedBy,jdbcType=VARCHAR},item.last_modified_time = #{modifiedTime,jdbcType=TIMESTAMP},
      reform.last_modified_by = #{modifiedBy,jdbcType=VARCHAR},reform.last_modified_time = #{modifiedTime,jdbcType=TIMESTAMP}
      where vehicle.valid =1
      and vehicle.id = #{safeCheckVehicleId,jdbcType = BIGINT}
    </update>

    <select id="getSafeCheckBoardInfo" resultType="com.logistics.tms.controller.vehiclesafecheck.response.SafeCheckBoardResponseModel">
        select
        ifnull(count(*), 0)                                      as checkVehicleCount,       -- 检查车辆
        sum(IF(tvv.vehicle_property = 1, 1, 0))                  as ownVehicleCount,         -- 此次检查自主车辆数
        sum(IF(tvv.vehicle_property = 3, 1, 0))                  as affiliationVehicleCount, -- 此次检查自营车辆数
        ifnull(sum(if(tvv.status = 40, 1, 0)) * 10, 0)           as checkItemCount,          -- 检查项
        tvc.vehicle_count                                        as vehicleCount,            -- 历史检查月总车辆
        tvc.period                                               as period,                  -- 历史年月
        ifnull(sum(if(tvv.status = 40, tvr.reform_count, 0)), 0) as reformCount              -- 整改项
        from t_vehicle_safe_check_vehicle tvv
        left join t_vehicle_safe_check tvc on tvc.id = tvv.safe_check_id and tvc.valid = 1
        left join t_vehicle_safe_check_reform tvr on tvr.safe_check_vehicle_id = tvv.id and tvr.valid = 1
        where tvv.valid = 1
        and instr(tvc.period, #{params.safeCheckYear,jdbcType = VARCHAR}) > 0
        group by tvc.period
    </select>

    <select id="getUnFinishCheckCount" resultType="java.lang.Integer">
        select count(*) from t_vehicle_safe_check_vehicle tvv
        where valid = 1
        and status not in (40)  -- 除去已检查完成的数据
        and vehicle_id = #{vehicleId,jdbcType = BIGINT}
    </select>


    <select id="getListByCheckId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_vehicle_safe_check_vehicle tvv
        where valid =1
        and tvv.safe_check_id = #{safeCheckId,jdbcType = BIGINT}
    </select>
    <!-- 小程序相关 -->
    <select id="searchAppletList" resultType="com.logistics.tms.controller.vehiclesafecheck.response.AppletSafeCheckListResponseModel">
        select
          tvv.id as safeCheckVehicleId,
          tvc.period as period,
          tvv.status as status,
          tvv.check_time as checkTime,
          tvv.vehicle_no as vehicleNo,
          tvr.reform_count as reformItemCount
        from t_vehicle_safe_check_vehicle tvv
        left join t_vehicle_safe_check tvc on tvc.id =  tvv.safe_check_id and tvc.valid =1
        left join t_vehicle_safe_check_reform tvr on tvr.safe_check_vehicle_id = tvv.id and tvr.valid = 1
        where tvv.valid = 1
        <if test="params.staffId!=null">
            and tvv.staff_id = #{params.staffId,jdbcType = BIGINT}
        </if>
        <if test="params.status!=null">
            and tvv.status = #{params.status,jdbcType = INTEGER}
        </if>
        order by tvv.created_time desc, tvv.id desc
    </select>

    <select id="getAppletSummary" resultType="com.logistics.tms.controller.vehiclesafecheck.response.AppletSafeCheckSummaryResponseModel">
        select
          ifnull(sum(if(tvv.status = 0,1,0)),0) as notCheckCount,
          ifnull(sum(if(tvv.status = 10,1,0)),0) as waitConfirmCount,
          ifnull(sum(if(tvv.status = 20,1,0)),0) as waitReformCount,
          ifnull(sum(if(tvv.status = 30,1,0)),0) as hasReformCount,
          ifnull(sum(if(tvv.status = 40,1,0)),0) as hasCheckCount
        from t_vehicle_safe_check_vehicle tvv
        left join t_vehicle_safe_check tvc on tvc.id =  tvv.safe_check_id and tvc.valid =1
        left join t_vehicle_safe_check_reform tvr on tvr.safe_check_vehicle_id = tvv.id and tvr.valid = 1
        where tvv.valid = 1
        <if test="params.staffId!=null">
            and tvv.staff_id = #{params.staffId,jdbcType = BIGINT}
        </if>
    </select>

</mapper>