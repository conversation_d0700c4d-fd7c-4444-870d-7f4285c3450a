package com.logistics.tms.controller.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class RenewableOrderSubmitTicketRequestModel {

	@ApiModelProperty(value = "乐橘新生订单审核表id",required = true)
	private Long renewableAuditId;

	@ApiModelProperty(value = "现场图片",required = true)
	private List<String> scenePictureList;

	@ApiModelProperty(value = "确认单据",required = true)
	private List<String> confirmPictureList;
}
