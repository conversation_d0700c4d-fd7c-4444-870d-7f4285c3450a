package com.logistics.tms.mapper;

import com.logistics.tms.controller.sysconfig.request.SysConfigRequestModel;
import com.logistics.tms.entity.TSysConfig;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
* Created by Mybatis Generator on 2023/02/03
*/
@Mapper
public interface TSysConfigMapper extends BaseMapper<TSysConfig> {

    TSysConfig selectOnByGroupCodeAndKey(@Param("groupCode") String groupCode, @Param("configKey") String configKey);

    List<TSysConfig> selectAllByGroupCode(@Param("groupCode") String groupCode);

    List<TSysConfig> selectInGroupCodeAndKey(@Param("sysConfigParams") Collection<SysConfigRequestModel> sysConfigParams);

    int updateByGroupAndKey(TSysConfig sysConfig);
}