package com.logistics.management.webapi.client.freightconfig.request.shipping;

import lombok.Data;

import java.math.BigDecimal;


@Data
public class AddShippingFreightRuleAddressReqModel {


    /**
     * 发货省
     */
    private Long fromProvinceId;


    /**
     * 收货省name
     */
    private String fromProvinceName;

    /**
     * 收货市id
     */
    private Long fromCityId;

    /**
     * 收货市name
     */
    private String fromCityName;


    /**
     * 收货区id
     */
    private Long fromAreaId;


    /**
     * 收货区name
     */
    private String fromAreaName;

    /**
     * 运距
     */
    private BigDecimal carrierDistance;


}
