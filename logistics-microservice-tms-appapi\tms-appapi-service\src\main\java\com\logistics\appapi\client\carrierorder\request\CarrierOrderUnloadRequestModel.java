package com.logistics.appapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2018/10/17 16:22
 */
@Data
public class CarrierOrderUnloadRequestModel {

    @ApiModelProperty("运单Id")
    private Long carrierOrderId;

    @ApiModelProperty("卸货数量")
    private List<CarrierOrderGoodsLoadUnloadRequestModel> goodsList;

    @ApiModelProperty("图片路径")
    private List<CarrierOrderTicketRequestModel> tickets;

    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;
}
