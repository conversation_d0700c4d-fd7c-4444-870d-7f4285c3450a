package com.logistics.tms.controller.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RenewableGoodsRequestModel {

    @ApiModelProperty("sku编码")
    private String skuCode;

    @ApiModelProperty(value = "货物名称")
    private String goodsName;

    @ApiModelProperty(value = "确认数量")
    private BigDecimal goodsAmount;

    @ApiModelProperty(value = "收货单价")
    private BigDecimal goodsPrice;
}
