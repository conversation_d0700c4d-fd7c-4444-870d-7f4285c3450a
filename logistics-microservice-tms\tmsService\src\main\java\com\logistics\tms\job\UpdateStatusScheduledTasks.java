package com.logistics.tms.job;

import com.logistics.tms.biz.contractorder.ContractOrderBiz;
import com.logistics.tms.biz.vehiclesettlement.VehicleSettlementBiz;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2022/9/5 15:36
 */
@Slf4j
@Component
public class UpdateStatusScheduledTasks {

    @Autowired
    private ContractOrderBiz contractOrderBiz;
    @Autowired
    private VehicleSettlementBiz vehicleSettlementBiz;

    /**
     * 合同有效期到期后自动终止（一天一次（凌晨一点））
     */
    @XxlJob("logisticsTmsUpdateContractStatus")
    public void updateContractStatus(){
        try{
            log.info("tms定时任务：合同有效期到期后自动终止-开始");
            contractOrderBiz.updateStatusFromJob();
            log.info("tms定时任务：合同有效期到期后自动终止-结束");
        }catch(Exception e){
            log.error("定时任务，合同有效期到期后自动终止错误: ", e);
        }
    }

    /**
     * 更新停车费用、gps费用的合作状态（一天一次（凌晨一点））
     */
    @XxlJob("logisticsTmsUpdateParkingGpsFeeCooperationStatus")
    public void updateParkingGpsFeeCooperationStatus(){
        try{
            log.info("tms定时任务：更新停车费用、gps费用的合作状态-开始");
            vehicleSettlementBiz.updateParkingGpsFeeCooperationStatus();
            log.info("tms定时任务：更新停车费用、gps费用的合作状态-结束");
        }catch(Exception e){
            log.error("定时任务，更新停车费用、gps费用的合作状态错误: ", e);
        }
    }
}
