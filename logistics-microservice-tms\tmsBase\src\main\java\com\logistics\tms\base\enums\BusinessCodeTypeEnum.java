/**
 * Created by yun<PERSON><PERSON><PERSON> on 2017/10/23.
 */
package com.logistics.tms.base.enums;

import com.logistics.tms.base.constant.CommonConstant;

public enum BusinessCodeTypeEnum {
    COMPANY_CARRIER(1, "CY","",8,99999999),
    DISPATCH_ORDER_CODE(2, "QD", CommonConstant.DATE_TO_STRING_SHORT_PATTERN,4,9999),
    ANNOUNCEMENT_CODE(3, "TZ",CommonConstant.DATE_TO_STRING_SHORT_PATTERN,4,9999),
    FEEDBACK_CODE(4,"WLFK",CommonConstant.DATE_TO_STRING_SHORT_PATTERN,5,99999),
    KEFU_CODE(5,"KF",CommonConstant.DATE_TO_STRING_SHORT_PATTERN,5,99999),
    C_FEEDBACK_CODE(6, "LCFK", CommonConstant.DATE_TO_STRING_SHORT_PATTERN, 5, 99999),
    COMPLAINT_CODE(7, "LCTS", CommonConstant.DATE_TO_STRING_SHORT_PATTERN, 5, 99999),
    CONTRACT_ORDER_HK(8, "PTQYHK", CommonConstant.YDM_FORMAT, 3, 999),
    CONTRACT_ORDER_HD(9, "PTQYHD", CommonConstant.YDM_FORMAT, 3, 999),
    CONTRACT_ORDER_CK(10, "PTQYCK", CommonConstant.YDM_FORMAT, 3, 999),
    CONTRACT_ORDER_CD(11, "PTQYCD", CommonConstant.YDM_FORMAT, 3, 999),
    CONTRACT_ORDER_ZK(12, "PTQYZK", CommonConstant.YDM_FORMAT, 3, 999),
    CONTRACT_ORDER_ZD(13, "PTQYZD", CommonConstant.YDM_FORMAT, 3, 999),
    DEMAND_ORDER_CODE(14, "QH0", CommonConstant.DATE_TO_STRING_SHORT_PATTERN, 4, 9999),
    DEMAND_ORDER_CODE_YELOLIFE(15, "HL", CommonConstant.DATE_TO_STRING_SHORT_PATTERN, 4, 9999),
    SETTLE_STATEMENT_CODE(16, "S", CommonConstant.YMDHMS_FORMAT, 3, 999),
    DRIVER_COST_APPLY_CODE(17, "DF", CommonConstant.YDM_FORMAT, 5, 99999),
    RESERVE_APPLY_CODE(18, "YLAC", CommonConstant.YDM_FORMAT, 3, 999),
    TRADITION_SETTLE_STATEMENT_CODE(19, "YeloPL", CommonConstant.DATE_TO_STRING_SHORT_PATTERN, 4, 9999),
    BIDDING_ORDER_CODE(20, "YeloC", CommonConstant.DATE_TO_STRING_SHORT_PATTERN, 4, 9999),
    ROUTE_ENQUIRY_CODE(21, "YeloT", CommonConstant.DATE_TO_STRING_SHORT_PATTERN, 4, 9999),
    SHIPPING_ORDER_CODE(22, "YeloP", CommonConstant.DATE_TO_STRING_SHORT_PATTERN, 5, 99999),
    RESERVATION_ORDER_CODE(23, "YeloA", CommonConstant.DATE_TO_STRING_SHORT_PATTERN, 4, 9999),

    ;
    private Integer key;
    private String value;
    private String dateFormat;
    private Integer codeLength;
    private Integer max;

    BusinessCodeTypeEnum(Integer key, String value, String dateFormat, Integer codeLength, Integer max) {
        this.key = key;
        this.value = value;
        this.dateFormat =  dateFormat;
        this.codeLength = codeLength;
        this.max = max;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getDateFormat() {
        return dateFormat;
    }

    public Integer getCodeLength() {
        return codeLength;
    }

    public Integer getMax() {
        return max;
    }


}
