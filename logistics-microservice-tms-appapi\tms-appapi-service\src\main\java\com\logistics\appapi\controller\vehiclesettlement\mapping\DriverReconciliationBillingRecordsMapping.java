package com.logistics.appapi.controller.vehiclesettlement.mapping;

import com.logistics.appapi.client.vehiclesettlement.response.ReconciliationBillingRecordsResponseModel;
import com.logistics.appapi.controller.vehiclesettlement.response.ReconciliationBillingRecordsResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @author：wjf
 * @date：2021/4/13 14:31
 */
public class DriverReconciliationBillingRecordsMapping extends MapperMapping<ReconciliationBillingRecordsResponseModel,ReconciliationBillingRecordsResponseDto> {
    @Override
    public void configure() {
        ReconciliationBillingRecordsResponseModel source = getSource();
        ReconciliationBillingRecordsResponseDto destination = getDestination();

        if (source.getPayTime() != null){
            destination.setPayTime(DateUtils.dateToString(source.getPayTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
    }
}
