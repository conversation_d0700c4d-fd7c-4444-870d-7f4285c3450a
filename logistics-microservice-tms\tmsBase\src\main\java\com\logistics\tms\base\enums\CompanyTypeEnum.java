/**
 * Created by yun<PERSON>zhou on 2017/12/12.
 */
package com.logistics.tms.base.enums;

public enum CompanyTypeEnum {
    DEFAULT(0,""),
    COMPANY(1, "企业"),
    PERSON(2, "个人")
    ;

    private Integer key;
    private String value;

    CompanyTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static CompanyTypeEnum getEnum(Integer key) {
        for (CompanyTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
