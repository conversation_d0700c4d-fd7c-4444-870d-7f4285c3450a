package com.logistics.management.webapi.api.impl.gpsfee.mapping;

import com.logistics.management.webapi.api.feign.gpsfee.dto.SearchGpsFeeListResponseDto;
import com.logistics.management.webapi.base.enums.CooperationStatusEnum;
import com.logistics.management.webapi.base.enums.SettlementStatusEnum;
import com.logistics.management.webapi.base.enums.VehiclePropertyEnum;
import com.logistics.tms.api.feign.gpsfee.model.SearchGpsFeeListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @author: wjf
 * @date: 2019/10/8 14:45
 */
public class SearchGpsFeeListMapping extends MapperMapping<SearchGpsFeeListResponseModel,SearchGpsFeeListResponseDto> {
    @Override
    public void configure() {
        SearchGpsFeeListResponseModel source = getSource();
        SearchGpsFeeListResponseDto destination = getDestination();
        if (source != null){
            destination.setStatusLabel(SettlementStatusEnum.getEnum(source.getStatus()).getValue());
            destination.setCooperationStatusLabel(CooperationStatusEnum.getEnum(source.getCooperationStatus()).getValue());
            destination.setServiceFee(source.getServiceFee() + "*" + source.getCooperationPeriod());
            destination.setStartDate(DateUtils.dateToString(source.getStartDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            destination.setEndDate(DateUtils.dateToString(source.getEndDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            destination.setFinishDate(DateUtils.dateToString(source.getFinishDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            destination.setVehiclePropertyLabel(VehiclePropertyEnum.getEnum(source.getVehicleProperty()).getValue());
        }
    }
}
