package com.logistics.tms.api.feign.parkingfee.hystrix;
import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.parkingfee.ParkingFeeServiceApi;
import com.logistics.tms.api.feign.parkingfee.dto.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/10/9 15:29
 */
@Component
public class ParkingFeeServiceApiHystrix implements ParkingFeeServiceApi {
    @Override
    public Result<PageInfo<ParkingFeeListResponseModel>> searchList(ParkingFeeListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SummaryParkingFeeResponseModel> getSummary(ParkingFeeListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ParkingFeeDetailResponseModel> getDetail(ParkingFeeDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> saveOrUpdate(SaveOrUpdateParkingFeeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> terminateParkingFee(TerminateParkingFeeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<ParkingFeeDeductingHistoryResponseModel>> getDeductingHistoryList(ParkingFeeDeductingHistoryRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<ParkingFeeOperationRecordResponsesModel>> getOperationRecordList(ParkingFeeOperationRecordRequestModel requestModel) {
        return Result.timeout();
    }
}
