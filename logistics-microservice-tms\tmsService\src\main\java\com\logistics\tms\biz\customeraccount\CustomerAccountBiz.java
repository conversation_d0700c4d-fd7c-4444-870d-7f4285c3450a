package com.logistics.tms.biz.customeraccount;

import com.leyi.auth.service.client.common.AuthExceptionCodeEnum;
import com.leyi.auth.service.client.common.PlatformProdEnums;
import com.leyi.auth.service.client.utils.YeloBcryptUtils;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.constant.ConfigKeyConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.ExceptionUtils;
import com.logistics.tms.biz.authentication.AuthenticationBiz;
import com.logistics.tms.biz.basicinfo.common.BasicInfoCommonBiz;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.client.AuthClient;
import com.logistics.tms.client.BasicDataClient;
import com.logistics.tms.client.feign.auth.request.CreateToken;
import com.logistics.tms.client.feign.auth.response.TokenModule;
import com.logistics.tms.client.feign.basicdata.BasicServiceClient;
import com.logistics.tms.client.feign.basicdata.user.response.UserInfoModel;
import com.logistics.tms.controller.baiscinfo.web.request.VerifyPersonThreeElementsRequestModel;
import com.logistics.tms.controller.baiscinfo.web.response.VerifyPersonThreeElementsResponseModel;
import com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.tms.controller.customeraccount.request.*;
import com.logistics.tms.controller.customeraccount.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.logistics.tms.rabbitmq.publisher.model.UpdateWechatUserIdMessage;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.utils.RedisUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.MD5Utils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tools.utils.UUIDGenerateUtil;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Consumer;

/**
 * @author：wjf
 * @date：2021/5/12 13:17
 */
@Slf4j
@Service
public class CustomerAccountBiz {

    @Autowired
    private TCustomerAccountMapper tCustomerAccountMapper;
    @Autowired
    private TCustomerAccountRelationMapper tCustomerAccountRelationMapper;
    @Autowired
    private AuthClient authClient;
    @Autowired
    private AuthenticationBiz authenticationBiz;
    @Autowired
    private BasicDataClient basicDataClient;
    @Resource
    private BasicServiceClient basicServiceClient;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private TCarrierContactMapper tCarrierContactMapper;
    @Autowired
    private TStaffBasicMapper tStaffBasicMapper;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private TCarrierDriverRelationMapper tCarrierDriverRelationMapper;
    @Autowired
    private TRealNameAuthenticationMapper tRealNameAuthenticationMapper;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private BasicInfoCommonBiz basicInfoCommonBiz;
    @Autowired
    private RabbitMqPublishBiz rabbitMqPublishBiz;

    /**
     * 司机小程序默认登录
     *
     * @param requestModel
     * @return
     */
    public CustomerLoginResponseModel driverAppletDefaultLogin(DriverAppletDefaultLoginRequestModel requestModel) {
        //查询司机登录的微信id是否存在
        AccountInfoResponseModel accountInfoResponseModel = tCustomerAccountMapper.selectByMobileAndRole(null, requestModel.getOpenId(), AccountUserRoleTypeEnum.DRIVER_APPLET.getKey());
        //账号不存在
        if (accountInfoResponseModel == null || ListUtils.isEmpty(accountInfoResponseModel.getRoles())){
            throw new BizException(AuthExceptionCodeEnum.JWT_TOKEN_USER_CODE_NOT_EXIST);
        }
        List<AccountRolesModel> accountRoleList = accountInfoResponseModel.getRoles();
        //判断账号是否都已关闭
        AccountRolesModel accountRolesModel = accountRoleList.stream().filter(item -> !CommonConstant.INTEGER_ONE.equals(item.getIfClose())).findFirst().orElse(null);
        if (accountRolesModel == null){
            throw new BizException(AuthExceptionCodeEnum.JWT_TOKEN_USER_CODE_NOT_EXIST);
        }

        //查询司机信息
        Long carrierDriverId = accountRolesModel.getUserId();//车主司机关系id
        TStaffBasic tStaffBasic = tStaffBasicMapper.getByCarrierDriverId(carrierDriverId);
        if (tStaffBasic == null || tStaffBasic.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }

        CustomerLoginResponseModel responseModel = new CustomerLoginResponseModel();
        responseModel.setUserId(accountInfoResponseModel.getAccountId());
        responseModel.setUserName(accountInfoResponseModel.getUserName());
        responseModel.setUserAccount(accountInfoResponseModel.getUserAccount());
        responseModel.setToken(this.createToken(MapperUtils.mapper(accountInfoResponseModel, TAccountModel.class), PlatformProdEnums.LOGISTICS_TMS_DRIVER_APPLET.getCode(), configKeyConstant.tokenAppExp));//生成token
        responseModel.setStaffProperty(tStaffBasic.getStaffProperty());//司机人员机构
        responseModel.setRealNameAuthenticationStatus(tStaffBasic.getRealNameAuthenticationStatus());
        responseModel.setWarehouseSwitch(tStaffBasic.getWarehouseSwitch());
        return responseModel;
    }

    /**
     * 司机小程序登录
     * @param requestModel
     * @return
     */
    public CustomerLoginResponseModel driverAppletLogin(CustomerLoginRequestModel requestModel) {
        //查询司机登录账号是否存在
        AccountInfoResponseModel accountInfoResponseModel = tCustomerAccountMapper.selectByMobileAndRole(requestModel.getUserAccount(),null, AccountUserRoleTypeEnum.DRIVER_APPLET.getKey());
        //账号不存在
        if (accountInfoResponseModel == null || ListUtils.isEmpty(accountInfoResponseModel.getRoles())){
            throw new BizException(CarrierDataExceptionEnum.NOT_APPLY_ACCOUNT_LOGIN);
        }
        List<AccountRolesModel> accountRoleList = accountInfoResponseModel.getRoles();
        //判断账号是否都已关闭
        AccountRolesModel accountRolesModel = accountRoleList.stream().filter(item -> !CommonConstant.INTEGER_ONE.equals(item.getIfClose())).findFirst().orElse(null);
        if (accountRolesModel == null){
            throw new BizException(CarrierDataExceptionEnum.NOT_APPLY_ACCOUNT_LOGIN);
        }

        //校验短信验证码或密码
        if (LoginTypeEnum.VERIFY_CODE.getKey().equals(requestModel.getLoginType())) {//账号验证码登录
            //验证短信验证码是否匹配
            VerificationCodeTypeEnum typeEnum = VerificationCodeTypeEnum.TMS_DRIVER_APPLET_LOGIN;
            basicDataClient.checkVerificationCode(typeEnum.getSourceTypeEnum().getKey(),typeEnum.getKey(),requestModel.getUserAccount(),requestModel.getVerificationCode());
        } else if (LoginTypeEnum.PASSWORD.getKey().equals(requestModel.getLoginType())) {//账号密码登录
            //校验密码是否正确
            String requestPassword;
            if (StringUtils.isNotBlank(accountInfoResponseModel.getUserSalt())){
                requestPassword = YeloBcryptUtils.bcryptForPassword(requestModel.getPassword(), accountInfoResponseModel.getUserSalt());
            }else{
                requestPassword = MD5Utils.encodeByMD5(requestModel.getPassword());
            }
            if (!requestPassword.equals(accountInfoResponseModel.getUserPassword())){
                throw new BizException(CarrierDataExceptionEnum.USER_NAME_PWD_ERROR);
            }
        }

        //查询司机信息
        Long carrierDriverId = accountRolesModel.getUserId();//车主司机关系id
        TStaffBasic tStaffBasic = tStaffBasicMapper.getByCarrierDriverId(carrierDriverId);
        if (tStaffBasic == null || tStaffBasic.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }

        //返回登录信息
        CustomerLoginResponseModel responseModel = new CustomerLoginResponseModel();
        responseModel.setUserId(accountInfoResponseModel.getAccountId());
        responseModel.setUserName(accountInfoResponseModel.getUserName());
        responseModel.setUserAccount(accountInfoResponseModel.getUserAccount());
        responseModel.setToken(this.createToken(MapperUtils.mapper(accountInfoResponseModel, TAccountModel.class), PlatformProdEnums.LOGISTICS_TMS_DRIVER_APPLET.getCode(), configKeyConstant.tokenAppExp));//生成token
        responseModel.setStaffProperty(tStaffBasic.getStaffProperty());//司机人员机构
        responseModel.setRealNameAuthenticationStatus(tStaffBasic.getRealNameAuthenticationStatus());
        responseModel.setWarehouseSwitch(tStaffBasic.getWarehouseSwitch());
        return responseModel;
    }

    /**
     * 前台web客户登录
     * @param requestModel
     * @return
     */
    public CustomerLoginResponseModel customerLoginForWeb(CustomerLoginRequestModel requestModel) {
        //查询车主登录账号是否存在
        AccountInfoResponseModel accountInfoResponseModel = tCustomerAccountMapper.selectByMobileAndRole(requestModel.getUserAccount(),null, AccountUserRoleTypeEnum.CARRIER.getKey());
        //账号不存在
        if (accountInfoResponseModel == null || ListUtils.isEmpty(accountInfoResponseModel.getRoles())){
            throw new BizException(CarrierDataExceptionEnum.NOT_APPLY_ACCOUNT_LOGIN);
        }
        List<AccountRolesModel> accountRoleList = accountInfoResponseModel.getRoles();
        //判断账号是否都已关闭
        AccountRolesModel accountRolesModel = accountRoleList.stream().filter(item -> !CommonConstant.INTEGER_ONE.equals(item.getIfClose())).findFirst().orElse(null);
        if (accountRolesModel == null){
            throw new BizException(CarrierDataExceptionEnum.NOT_APPLY_ACCOUNT_LOGIN);
        }

        //校验短信验证码或密码
        if (LoginTypeEnum.VERIFY_CODE.getKey().equals(requestModel.getLoginType())) {//账号验证码登录
            //验证短信验证码是否匹配
            VerificationCodeTypeEnum typeEnum = VerificationCodeTypeEnum.TMS_CUSTOMER_WEB_LOGIN;
            basicDataClient.checkVerificationCode(typeEnum.getSourceTypeEnum().getKey(),typeEnum.getKey(),requestModel.getUserAccount(),requestModel.getVerificationCode());
        } else if (LoginTypeEnum.PASSWORD.getKey().equals(requestModel.getLoginType())) {//账号密码登录
            //校验密码是否正确
            String requestPassword;
            if (StringUtils.isNotBlank(accountInfoResponseModel.getUserSalt())){
                requestPassword = YeloBcryptUtils.bcryptForPassword(requestModel.getPassword(), accountInfoResponseModel.getUserSalt());
            }else{
                requestPassword = MD5Utils.encodeByMD5(requestModel.getPassword());
            }
            if (!requestPassword.equals(accountInfoResponseModel.getUserPassword())){
                throw new BizException(CarrierDataExceptionEnum.USER_NAME_PWD_ERROR);
            }
        }

        //返回登录信息
        CustomerLoginResponseModel responseModel = new CustomerLoginResponseModel();
        responseModel.setUserId(accountInfoResponseModel.getAccountId());
        responseModel.setUserName(accountInfoResponseModel.getUserName());
        responseModel.setUserAccount(accountInfoResponseModel.getUserAccount());
        responseModel.setToken(this.createToken(MapperUtils.mapper(accountInfoResponseModel, TAccountModel.class), PlatformProdEnums.LOGISTICS_TMS_WEB.getCode(), configKeyConstant.tokenExp));//生成token
        return responseModel;
    }

    //生成token
    private String createToken(TAccountModel tAccountModel, String loginSource, String expireTime){
        CreateToken createTokenModel = new CreateToken();
        createTokenModel.setLoginSource(loginSource);
        createTokenModel.setUserId(tAccountModel.getAccountId());
        createTokenModel.setUserName(tAccountModel.getUserName());
        createTokenModel.setUserAccount(tAccountModel.getUserAccount());
        createTokenModel.setExpireTime(expireTime);

        //请求auth服务生成token
        return createToken(createTokenModel);
    }

    //生成token
    private String createToken(CreateToken createTokenModel) {
        //请求auth服务生成token
        TokenModule tokenModule = authClient.loginCreateToken(createTokenModel);
        if (tokenModule == null){
            throw new BizException(AuthExceptionCodeEnum.JWT_TOKEN_USER_CODE_NOT_EXIST);
        }
        return tokenModule.getToken();
    }

    /**
     * 小程序登录成功后绑定openId
     *
     * @param requestModel
     */
    @Transactional
    public void appletBindingOpenId(AppletBindingOpenIdRequestModel requestModel) {
        TCustomerAccount tAccount = tCustomerAccountMapper.selectByPrimaryKeyDecrypt(requestModel.getUserId());
        if (tAccount == null || tAccount.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_NOT_EXIST);
        }
        TCustomerAccount accountOpenId = tCustomerAccountMapper.getByOpenId(requestModel.getOpenId());
        if (accountOpenId != null && accountOpenId.getValid().equals(IfValidEnum.VALID.getKey()) && !accountOpenId.getId().equals(tAccount.getId())) {
            TCustomerAccount account = new TCustomerAccount();
            account.setId(accountOpenId.getId());
            account.setOpenId("");
            commonBiz.setBaseEntityModify(account, BaseContextHandler.getUserName());
            tCustomerAccountMapper.updateByPrimaryKeySelectiveEncrypt(account);
        }
        TCustomerAccount account = new TCustomerAccount();
        account.setId(tAccount.getId());
        account.setOpenId(requestModel.getOpenId());
        commonBiz.setBaseEntityModify(account, BaseContextHandler.getUserName());
        tCustomerAccountMapper.updateByPrimaryKeySelectiveEncrypt(account);
    }

    /**
     * 小程序退出登录（清空openid）
     */
    @Transactional
    public void appletLoginOut() {
        TCustomerAccount tAccount = tCustomerAccountMapper.selectByPrimaryKeyDecrypt(BaseContextHandler.getUserId());
        if (tAccount == null || tAccount.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_NOT_EXIST);
        }
        TCustomerAccount account = new TCustomerAccount();
        account.setId(tAccount.getId());
        account.setOpenId("");
        commonBiz.setBaseEntityModify(account, BaseContextHandler.getUserName());
        tCustomerAccountMapper.updateByPrimaryKeySelectiveEncrypt(account);
    }

    /**
     * 直接修改密码
     * @param requestModel
     */
    @Transactional
    public void updateAccountPassword(UpdateAccountPasswordRequestModel requestModel) {
        basicDataClient.checkVerificationCode(requestModel.getCodeSource(),requestModel.getCodeType(),requestModel.getUserAccount(),requestModel.getVerificationCode());
        TCustomerAccount tCustomerAccount = tCustomerAccountMapper.selectByMobile(requestModel.getUserAccount());
        if(tCustomerAccount == null){
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_NOT_EXIST);
        }

        //盐值不存在，则生成盐值，并生成新的密码
        String userSalt = tCustomerAccount.getUserSalt();
        if (StringUtils.isBlank(userSalt)){
            userSalt = YeloBcryptUtils.genSaltForUser();
        }

        TCustomerAccount upAccount = new TCustomerAccount();
        upAccount.setId(tCustomerAccount.getId());
        upAccount.setUserPassword(YeloBcryptUtils.bcryptForPassword(requestModel.getPassword(), userSalt));
        upAccount.setUserSalt(userSalt);
        commonBiz.setBaseEntityModify(upAccount, requestModel.getUserAccount());
        tCustomerAccountMapper.updateByPrimaryKeySelectiveEncrypt(upAccount);
    }

    /**
     * 根据用户账号id修改密码
     * @param requestModel
     */
    @Transactional
    public void modifyPassword(ModifyPasswordRequestModel requestModel) {
        TCustomerAccount tCustomerAccount = tCustomerAccountMapper.selectByPrimaryKeyDecrypt(BaseContextHandler.getUserId());
        if (tCustomerAccount == null || tCustomerAccount.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_NOT_EXIST);
        }
        //判断旧密码是否正确
        String oldPassword;
        String userSalt = tCustomerAccount.getUserSalt();
        if (StringUtils.isNotBlank(userSalt)){
            oldPassword = YeloBcryptUtils.bcryptForPassword(requestModel.getOldPassword(), userSalt);
        }else{
            oldPassword = MD5Utils.encodeByMD5(requestModel.getOldPassword());

            userSalt = YeloBcryptUtils.genSaltForUser();
        }
        if (!oldPassword.equals(tCustomerAccount.getUserPassword())) {
            throw new BizException(CarrierDataExceptionEnum.OLD_PASSWORD_NOT_TRUE);
        }
        //判断新旧密码是否一直
        if (requestModel.getOldPassword().equals(requestModel.getNewPassword())) {
            throw new BizException(CarrierDataExceptionEnum.OLE_PASSWORD_NOT_EQUALS_NEW_PASSWORD);
        }
        //回填新密码
        TCustomerAccount account = new TCustomerAccount();
        account.setId(tCustomerAccount.getId());
        account.setUserPassword(YeloBcryptUtils.bcryptForPassword(requestModel.getNewPassword(), userSalt));
        account.setUserSalt(userSalt);
        account.setOpenId("");//把openId置空，需要重新登录
        commonBiz.setBaseEntityModify(account, BaseContextHandler.getUserName());
        tCustomerAccountMapper.updateByPrimaryKeySelectiveEncrypt(account);
    }

    /**
     * 联系人开通或关闭账号
     *
     * @param requestModel
     */
    @Transactional
    public void openOrCloseAccount(OpenAccountRequestModel requestModel) {
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getType())) {//开通账号
            openAccount(requestModel);
        } else if (CommonConstant.INTEGER_TWO.equals(requestModel.getType())) {//关闭账号
            closeAccount(requestModel.getUserRole(), requestModel.getUserId());
        } else {//删除账号关系
            delAccountRelation(requestModel.getUserRole(), requestModel.getUserId());
        }
    }

    //开通账号
    private void openAccount(OpenAccountRequestModel requestModel) {
        TCustomerAccountRelation tCustomerAccountRelation = tCustomerAccountRelationMapper.getRelationByUserRoleAndUserId(requestModel.getUserRole(), requestModel.getUserId());
        if (tCustomerAccountRelation != null) {//关系存在
            TCustomerAccount existCustomerAccount = tCustomerAccountMapper.selectByPrimaryKeyDecrypt(tCustomerAccountRelation.getAccountId());
            if (existCustomerAccount != null && existCustomerAccount.getValid().equals(IfValidEnum.VALID.getKey()) && !existCustomerAccount.getUserName().equals(requestModel.getUserName())){//姓名改变了
                TCustomerAccount upAccount = new TCustomerAccount();
                upAccount.setId(existCustomerAccount.getId());
                upAccount.setUserName(requestModel.getUserName());
                commonBiz.setBaseEntityModify(upAccount, BaseContextHandler.getUserName());
                tCustomerAccountMapper.updateByPrimaryKeySelectiveEncrypt(upAccount);
            }
            if (CommonConstant.INTEGER_ONE.equals(tCustomerAccountRelation.getIfClose()) && !CommonConstant.INTEGER_ONE.equals(requestModel.getIfClose())) {//存在被关闭的账号关系
                TCustomerAccountRelation upRel = new TCustomerAccountRelation();
                upRel.setId(tCustomerAccountRelation.getId());
                upRel.setEnabled(requestModel.getEnabled());
                upRel.setIfClose(CommonConstant.INTEGER_ZERO);
                commonBiz.setBaseEntityModify(upRel, BaseContextHandler.getUserName());
                tCustomerAccountRelationMapper.updateByPrimaryKeySelective(upRel);
            }
        } else {//不存在账号关系
            TCustomerAccount existCustomerAccount = tCustomerAccountMapper.selectByMobile(requestModel.getMobile());
            tCustomerAccountRelation = new TCustomerAccountRelation();
            if (existCustomerAccount == null) {//不存在账号
                String userSalt = YeloBcryptUtils.genSaltForUser();
                String userPassword = YeloBcryptUtils.bcryptForPassword(requestModel.getMobile().substring(requestModel.getMobile().length() - 6), userSalt);

                TCustomerAccount newAccount = new TCustomerAccount();
                newAccount.setUserName(requestModel.getUserName());
                newAccount.setUserCode(UUIDGenerateUtil.generateUUID());
                newAccount.setUserAccount(requestModel.getMobile());
                newAccount.setUserPassword(userPassword);
                newAccount.setUserSalt(userSalt);
                commonBiz.setBaseEntityAdd(newAccount, BaseContextHandler.getUserName());
                tCustomerAccountMapper.insertSelectiveEncrypt(newAccount);

                tCustomerAccountRelation.setAccountId(newAccount.getId());
            } else if (StringUtils.isBlank(existCustomerAccount.getUserPassword())) {//账号存在，但密码不存在
                //盐值不存在，则生成盐值，并生成新的密码
                String userSalt = existCustomerAccount.getUserSalt();
                if (StringUtils.isBlank(userSalt)){
                    userSalt = YeloBcryptUtils.genSaltForUser();
                }

                //更新账号信息
                TCustomerAccount upAccount = new TCustomerAccount();
                upAccount.setId(existCustomerAccount.getId());
                upAccount.setUserName(requestModel.getUserName());
                upAccount.setUserPassword(YeloBcryptUtils.bcryptForPassword(requestModel.getMobile().substring(requestModel.getMobile().length() - 6), userSalt));
                upAccount.setUserSalt(userSalt);
                commonBiz.setBaseEntityModify(upAccount, BaseContextHandler.getUserName());
                tCustomerAccountMapper.updateByPrimaryKeySelectiveEncrypt(upAccount);

                tCustomerAccountRelation.setAccountId(existCustomerAccount.getId());
            } else {
                tCustomerAccountRelation.setAccountId(existCustomerAccount.getId());
            }
            if (requestModel.getEnabled() != null) {
                tCustomerAccountRelation.setEnabled(requestModel.getEnabled());
            }
            if (requestModel.getIfClose() != null) {
                tCustomerAccountRelation.setIfClose(requestModel.getIfClose());
            }
            tCustomerAccountRelation.setUserId(requestModel.getUserId());
            tCustomerAccountRelation.setUserRole(requestModel.getUserRole());
            commonBiz.setBaseEntityAdd(tCustomerAccountRelation, BaseContextHandler.getUserName());
            tCustomerAccountRelationMapper.insertSelective(tCustomerAccountRelation);
        }
    }

    //关闭账号
    private void closeAccount(Integer userRole, Long userId) {
        TCustomerAccountRelation tCustomerAccountRelation = tCustomerAccountRelationMapper.getRelationByUserRoleAndUserId(userRole, userId);
        if (tCustomerAccountRelation != null && CommonConstant.INTEGER_ZERO.equals(tCustomerAccountRelation.getIfClose())) {//存在被开启的账号
            TCustomerAccountRelation upRel = new TCustomerAccountRelation();
            upRel.setId(tCustomerAccountRelation.getId());
            upRel.setEnabled(EnabledEnum.DISABLED.getKey());
            upRel.setIfClose(CommonConstant.INTEGER_ONE);
            commonBiz.setBaseEntityModify(upRel, BaseContextHandler.getUserName());
            tCustomerAccountRelationMapper.updateByPrimaryKeySelective(upRel);
        } else if (tCustomerAccountRelation == null) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_NOT_EXIST);
        }
    }

    //删除账号关系：如果账号下只有待删除的账号关系，则删除账号和账号关系；否则只删账号关系
    private void delAccountRelation(Integer userRole, Long userId) {
        TCustomerAccountRelation tCustomerAccountRelation = tCustomerAccountRelationMapper.getRelationByUserRoleAndUserId(userRole, userId);
        if (tCustomerAccountRelation == null) {
            return;
        }
        AccountInfoResponseModel accountInfoResponseModel = tCustomerAccountMapper.getAccountInfoBy(null, tCustomerAccountRelation.getAccountId(), null);
        if (accountInfoResponseModel == null) {
            return;
        }
        List<AccountRolesModel> roles = accountInfoResponseModel.getRoles();
        if (roles.size() <= CommonConstant.INTEGER_ONE) {
            TCustomerAccount account = new TCustomerAccount();
            account.setId(tCustomerAccountRelation.getAccountId());
            account.setValid(IfValidEnum.INVALID.getKey());
            commonBiz.setBaseEntityModify(account, BaseContextHandler.getUserName());
            tCustomerAccountMapper.updateByPrimaryKeySelectiveEncrypt(account);
        }
        TCustomerAccountRelation customerAccountRelation = new TCustomerAccountRelation();
        customerAccountRelation.setId(tCustomerAccountRelation.getId());
        customerAccountRelation.setValid(IfValidEnum.INVALID.getKey());
        commonBiz.setBaseEntityModify(customerAccountRelation, BaseContextHandler.getUserName());
        tCustomerAccountRelationMapper.updateByPrimaryKeySelective(customerAccountRelation);
    }

    /**
     * 根据userIds和角色修改账号状态
     *
     * @param requestModel
     */
    @Transactional
    public void updateTCustomerAccountRelationIfClose(UpdateTCustomerAccountRelationIfCloseRequestModel requestModel) {
        List<TCustomerAccountRelation> accountRelationList = tCustomerAccountRelationMapper.getTCustomerAccountRelationByIds(requestModel.getUserIds(), requestModel.getUserRole());
        if (ListUtils.isEmpty(accountRelationList)) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_NOT_EXIST);
        }
        TCustomerAccountRelation upCustomerAccountRelation;
        List<TCustomerAccountRelation> list = new ArrayList<>();
        for (TCustomerAccountRelation relation : accountRelationList) {
            upCustomerAccountRelation = new TCustomerAccountRelation();
            upCustomerAccountRelation.setId(relation.getId());
            upCustomerAccountRelation.setEnabled(requestModel.getEnabled());
            upCustomerAccountRelation.setIfClose(requestModel.getIfClose());
            commonBiz.setBaseEntityModify(upCustomerAccountRelation, BaseContextHandler.getUserName());
            list.add(upCustomerAccountRelation);
        }
        if (ListUtils.isNotEmpty(list)) {
            tCustomerAccountRelationMapper.batchUpdate(list);
        }
    }

    /**
     * 找回密码-先验证手机号
     * @param requestModel
     * @return
     */
    public FindPasswordPrepareResponseModel findPasswordPrepare(FindPasswordPrepareRequestModel requestModel){
        //查询车主账号是否存在
        AccountInfoResponseModel accountInfoResponseModel = tCustomerAccountMapper.getAccountInfoBy(requestModel.getUserAccount(), null, AccountUserRoleTypeEnum.CARRIER.getKey());
        if(accountInfoResponseModel == null){
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_NOT_EXIST);
        }

        //校验短信验证码
        basicDataClient.checkVerificationCode(requestModel.getCodeSource(),requestModel.getCodeType(),requestModel.getUserAccount(),requestModel.getVerificationCode());

        //创建修改密码令牌，存入redis
        String updatePasswordToken = UUIDGenerateUtil.generateUUID();
        redisUtils.set(CommonConstant.TMS_UPDATE_PASSWORD_PREPARE_TOKEN_PREFIX+updatePasswordToken, requestModel.getUserAccount(),180L);
        //将令牌返回
        FindPasswordPrepareResponseModel responseModel = new FindPasswordPrepareResponseModel();
        responseModel.setUpdatePasswordToken(updatePasswordToken);
        return responseModel;
    }

    /**
     * 找回密码-更新密码
     * @param requestModel
     */
    @Transactional
    public void updatePasswordForFind(UpdatePasswordForFindRequestModel requestModel){
        //从redis获取令牌
        String userAccount = (String)redisUtils.get(CommonConstant.TMS_UPDATE_PASSWORD_PREPARE_TOKEN_PREFIX+requestModel.getUpdatePasswordToken());
        if(StringUtils.isBlank(userAccount)){//令牌不存在或失效不能修改
            throw new BizException(CarrierDataExceptionEnum.PASSWORD_UPDATE_FAIL);
        }
        //根据手机号查询账号是否存在
        TCustomerAccount tCustomerAccount = tCustomerAccountMapper.selectByMobile(userAccount);
        if(tCustomerAccount == null){
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_NOT_EXIST);
        }

        //盐值不存在，则生成盐值，并生成新的密码
        String userSalt = tCustomerAccount.getUserSalt();
        if (StringUtils.isBlank(userSalt)){
            userSalt = YeloBcryptUtils.genSaltForUser();
        }

        //更新密码
        TCustomerAccount upAccount = new TCustomerAccount();
        upAccount.setId(tCustomerAccount.getId());
        upAccount.setUserPassword(YeloBcryptUtils.bcryptForPassword(requestModel.getPassword(), userSalt));
        upAccount.setUserSalt(userSalt);
        commonBiz.setBaseEntityModify(upAccount,userAccount);
        tCustomerAccountMapper.updateByPrimaryKeySelectiveEncrypt(upAccount);

        //前台找回密码成功后，清除客户登录的错误次数（解除账号锁定）
        redisUtils.delete(CommonConstant.LOGISTICS_TMS_WEB_LOGIN_ERROR_COUNT + userAccount);
        //清掉修改密码令牌
        redisUtils.delete(CommonConstant.TMS_UPDATE_PASSWORD_PREPARE_TOKEN_PREFIX+requestModel.getUpdatePasswordToken());
    }

    /**
     * 更换手机号-验证手机号（前台车主、司机小程序）
     * @param requestModel
     * @return
     */
    public UpdatePhoneByVerifyPhoneResponseModel verifyPhone(UpdatePhoneByVerifyPhoneRequestModel requestModel) {
        //根据请求来源，获取角色
        Integer userRole;
        if (AccountUserRoleTypeEnum.DRIVER_APPLET.getKey().equals(requestModel.getRequestSource())){
            userRole = AccountUserRoleTypeEnum.DRIVER_APPLET.getKey();
        }else{
            userRole = AccountUserRoleTypeEnum.CARRIER.getKey();
        }
        //查询车主账号是否存在
        AccountInfoResponseModel accountInfoResponseModel = tCustomerAccountMapper.getAccountInfoBy(null, BaseContextHandler.getUserId(), userRole);
        if (accountInfoResponseModel == null) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_NOT_EXIST);
        }
        //判断入参手机号与登陆人手机号是否一致
        if (!accountInfoResponseModel.getUserAccount().equals(requestModel.getUserAccount())) {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }

        //前台请求校验个人车主是否已经实名
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getRequestSource())) {
            //查询是否含有个人车主
            FuzzySearchCompanyCarrierResponseModel personCarrierInfo = tCompanyCarrierMapper.selectPersonCarrierByMobile(accountInfoResponseModel.getUserAccount());
            //未实名不允许修改
            if (personCarrierInfo != null && !RealNameAuthenticationStatusEnum.REAL_NAME.getKey().equals(personCarrierInfo.getRealNameAuthenticationStatus())) {
                throw new BizException(CarrierDataExceptionEnum.CHECK_REAL_NAME);
            }
        }

        //校验短信验证码
        basicDataClient.checkVerificationCode(requestModel.getCodeSource(), requestModel.getCodeType(), requestModel.getUserAccount(), requestModel.getVerificationCode());

        //创建修改手机号令牌，存入redis
        String updatePhoneToken = UUIDGenerateUtil.generateUUID();
        redisUtils.set(CommonConstant.TMS_UPDATE_PHONE_PREPARE_TOKEN_PREFIX + updatePhoneToken, requestModel.getUserAccount(), 180L);
        //将令牌返回
        UpdatePhoneByVerifyPhoneResponseModel responseModel = new UpdatePhoneByVerifyPhoneResponseModel();
        responseModel.setUpdatePhoneToken(updatePhoneToken);
        return responseModel;
    }

    /**
     * 更换手机号-更新手机号（前台车主、司机小程序）
     *
     * @param requestModel
     */
    @Transactional
    public void updatePhone(UpdatePhoneRequestModel requestModel) {
        //从redis获取令牌
        String userAccount = (String) redisUtils.get(CommonConstant.TMS_UPDATE_PHONE_PREPARE_TOKEN_PREFIX + requestModel.getUpdatePhoneToken());
        if (StringUtils.isBlank(userAccount)) {//令牌不存在或失效不能修改
            throw new BizException(CarrierDataExceptionEnum.PHONE_UPDATE_FAIL);
        }

        //查询登陆人账号信息
        AccountInfoResponseModel dbCustomerAccount = tCustomerAccountMapper.getAccountInfoBy(null, BaseContextHandler.getUserId(), null);
        if (dbCustomerAccount == null) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_NOT_EXIST);
        }

        //判断令牌是否有效
        if (!dbCustomerAccount.getUserAccount().equals(userAccount)) {
            throw new BizException(CarrierDataExceptionEnum.PHONE_UPDATE_FAIL);
        }
        updateAccountPhone(requestModel, dbCustomerAccount);
    }

    /**
     * 更新手机号
     *
     * @param requestModel      新手机号等信息
     * @param dbCustomerAccount 当前登录用户
     */
    @Transactional
    public void updateAccountPhone(UpdatePhoneRequestModel requestModel, AccountInfoResponseModel dbCustomerAccount) {
        //获取登陆人角色信息
        List<Integer> dbRoleList = new ArrayList<>();
        Map<Integer, AccountRolesModel> dbRoleMap = new HashMap<>();
        for (AccountRolesModel rolesModel : dbCustomerAccount.getRoles()) {
            dbRoleMap.put(rolesModel.getUserRole(), rolesModel);
            dbRoleList.add(rolesModel.getUserRole());
        }

        //根据请求来源，获取角色
        Integer userRole;
        if (AccountUserRoleTypeEnum.DRIVER_APPLET.getKey().equals(requestModel.getRequestSource())) {
            userRole = AccountUserRoleTypeEnum.DRIVER_APPLET.getKey();
        } else {
            userRole = AccountUserRoleTypeEnum.CARRIER.getKey();
        }

        AccountRolesModel dbCarrierAccount = dbRoleMap.get(userRole);
        //不存在登录角色，说明车主或司机账号不存在
        if (dbCarrierAccount == null) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_NOT_EXIST);
        }

        //判断手机号是否更改
        String newPhone = requestModel.getUserAccount();
        if (dbCustomerAccount.getUserAccount().equals(newPhone)) {
            throw new BizException(CarrierDataExceptionEnum.PHONE_UPDATE_FAIL);
        }
        //更新后的手机号已存在，则不允许更改
        AccountInfoResponseModel accountInfoResponseModel = tCustomerAccountMapper.getAccountInfoBy(newPhone, null, null);
        if (accountInfoResponseModel != null) {//手机号已存在
            throw new BizException(CarrierDataExceptionEnum.UPDATE_PHONE_ACCOUNT_EXIST);
        }

        //查询是否含有个人车主
        FuzzySearchCompanyCarrierResponseModel personCarrierInfo = tCompanyCarrierMapper.selectPersonCarrierByMobile(dbCustomerAccount.getUserAccount());
        //如果是前台请求修改手机号,但是此账号是非个人车主并含有司机角色
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getRequestSource())
                && dbRoleMap.get(AccountUserRoleTypeEnum.DRIVER_APPLET.getKey()) != null
                && personCarrierInfo == null) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_CAN_NOT_CHANGE_PHONE);
        }

        TRealNameAuthentication oldRealNameAuthentication = null;
        //当前登录的个人车主或者司机才获取实名信息
        if (AccountUserRoleTypeEnum.DRIVER_APPLET.getKey().equals(userRole) || personCarrierInfo != null) {
            //三要素校验-验证新账户手机号是不是当前用户的
            oldRealNameAuthentication = checkAccountPersonThreeElements(dbCustomerAccount.getUserAccount(), requestModel.getUserAccount());
            if (oldRealNameAuthentication == null || !CommonConstant.INTEGER_TWO.equals(oldRealNameAuthentication.getCertificationStatus())) {
                throw new BizException(CarrierDataExceptionEnum.DRIVER_UPDATE_MOBILE_BY_AUTHORIZATION);
            }
        }

        //实名
        if (requestModel.isCheckRealName()) {
            //如果账号含有个人车主和司机两个角色,需要走实名验证流程, 其他情况走正常更换手机号逻辑
            if (AccountUserRoleTypeEnum.DRIVER_APPLET.getKey().equals(userRole) || personCarrierInfo != null) {
                //上上签短信认证或人脸识别认证
                if (StringUtils.isNotBlank(requestModel.getVerificationCode())) {
                    //上上签短信认证
                    basicInfoCommonBiz.bestsignVerificationCodeAuth(requestModel.getVerificationCode());
                } else if (StringUtils.isNotBlank(requestModel.getOrderNo())) {
                    //上上签人脸识别认证
                    basicInfoCommonBiz.bestsignFaceAuth(requestModel.getOrderNo());
                } else {
                    throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
                }
            } else {
                //走普通修改手机号流程
                //校验短信验证码
                basicDataClient.checkVerificationCode(requestModel.getCodeSource(), requestModel.getCodeType(), requestModel.getUserAccount(), requestModel.getVerificationCode());
            }
        }

        //修改账户手机号
        TCustomerAccount account = new TCustomerAccount();
        account.setId(dbCustomerAccount.getAccountId());
        account.setUserAccount(newPhone);
        commonBiz.setBaseEntityModify(account, BaseContextHandler.getUserName());
        tCustomerAccountMapper.updateByPrimaryKeySelectiveEncrypt(account);

        Consumer<Integer> updateDriverMobile = (role) -> {
            TCarrierDriverRelation tCarrierDriverRelation = tCarrierDriverRelationMapper.selectByPrimaryKey(dbRoleMap.get(role).getUserId());
            if (tCarrierDriverRelation == null || tCarrierDriverRelation.getValid().equals(IfValidEnum.INVALID.getKey())) {
                throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_NOT_EXIST);
            }
            //更新司机手机号
            TStaffBasic staffBasic = new TStaffBasic();
            staffBasic.setId(tCarrierDriverRelation.getDriverId());
            staffBasic.setMobile(newPhone);
            commonBiz.setBaseEntityModify(staffBasic, BaseContextHandler.getUserName());
            tStaffBasicMapper.updateByPrimaryKeySelective(staffBasic);
        };

        // 修改车主手机号
        Consumer<Integer> updateCarrierMobile = (role) -> {
            //更新车主账号
            TCarrierContact carrierContact = new TCarrierContact();
            carrierContact.setId(dbRoleMap.get(role).getUserId());
            carrierContact.setContactPhone(newPhone);
            commonBiz.setBaseEntityModify(carrierContact, BaseContextHandler.getUserName());
            tCarrierContactMapper.updateByPrimaryKeySelectiveEncrypt(carrierContact);
        };

        //更换手机号
        if (dbRoleMap.get(AccountUserRoleTypeEnum.DRIVER_APPLET.getKey()) != null) {
            //含有司机角色则更新司机手机号
            updateDriverMobile.accept(AccountUserRoleTypeEnum.DRIVER_APPLET.getKey());
        }
        if (dbRoleMap.get(AccountUserRoleTypeEnum.CARRIER.getKey()) != null) {
            //含有车主角色则更新车主手机号
            updateCarrierMobile.accept(AccountUserRoleTypeEnum.CARRIER.getKey());
        }

        //更新实名认证手机号
        if (oldRealNameAuthentication != null) {
            TRealNameAuthentication tRealNameAuthenticationUp = new TRealNameAuthentication();
            tRealNameAuthenticationUp.setId(oldRealNameAuthentication.getId());
            tRealNameAuthenticationUp.setMobile(newPhone);
            commonBiz.setBaseEntityModify(tRealNameAuthenticationUp, BaseContextHandler.getUserName());
            tRealNameAuthenticationMapper.updateByPrimaryKeySelectiveEncrypt(tRealNameAuthenticationUp);
        }

        //清空token
        commonBiz.clearToken(dbCustomerAccount.getUserAccount());
        commonBiz.clearToken(newPhone);
        //清掉修改手机号令牌
        redisUtils.delete(CommonConstant.TMS_UPDATE_PHONE_PREPARE_TOKEN_PREFIX + requestModel.getUpdatePhoneToken());
    }

    /**
     * 三要素校验-验证新账户手机号是不是当前用户的
     *
     * @param oldAccount 旧的账户手机号
     * @param newAccount 新账户手机号
     */
    public TRealNameAuthentication checkAccountPersonThreeElements(String oldAccount, String newAccount) {
        TRealNameAuthentication tRealNameAuthentication = tRealNameAuthenticationMapper.selectRealNameAuthenticationByMobile(oldAccount);
        if (tRealNameAuthentication != null) {
            //如果当前账号存在实名信息,验证新账号手机号是否属于当前用户
            //新手机号三要素验证
            VerifyPersonThreeElementsRequestModel verifyPersonThreeElementsRequestModel = new VerifyPersonThreeElementsRequestModel();
            verifyPersonThreeElementsRequestModel.setMobile(newAccount);
            verifyPersonThreeElementsRequestModel.setIdentity(tRealNameAuthentication.getIdentityNumber());
            verifyPersonThreeElementsRequestModel.setName(tRealNameAuthentication.getName());
            VerifyPersonThreeElementsResponseModel responseModel = authenticationBiz.verifyPersonalThreeElements(verifyPersonThreeElementsRequestModel);
            if (!CommonConstant.ONE.equals(responseModel.getResult())) {
                //三要素验证不通过,禁止后续操作
                throw new BizException(CarrierDataExceptionEnum.THREE_ELEMENTS_VERIFY_FAIL);
            }
        }
        return tRealNameAuthentication;
    }

    /**
     * TMS移动办公小程序 - 根据企业微信用户手机号登录
     *
     * @param requestModel 请求Model
     * @return token
     */
    public QYWeChatLoginResponseModel qyWeChatLogin(QYWeChatLoginRequestModel requestModel) {

        String qyWeChatMobile = requestModel.getQyWeChatMobile();
        String desensitizedMobile = qyWeChatMobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        log.info("TMS移动办公小程序 wechat mobile:【{}】 -> 开始登录", desensitizedMobile);

        // 1 根据企业微信手机号查询用户信息
        UserInfoModel userInfo = basicServiceClient.getUserInfoByMobilePhone(qyWeChatMobile);

        // 2 校验用户是否满足登录条件
        // 2.1 用户不存在
        ExceptionUtils.isTure(Objects.isNull(userInfo))
                .throwMessage(() -> log.info("TMS移动办公小程序 wechat mobile:【{}】 -> 未匹配到对应账号", desensitizedMobile),
                        CarrierDataExceptionEnum.USER_NAME_PWD_ERROR);
        // 2.2 用户账号禁用
        ExceptionUtils.isTure(!EnabledEnum.ENABLED.getKey().equals(userInfo.getEnabled()))
                .throwMessage(() -> log.info("TMS移动办公小程序 wechat mobile:【{}】 -> 未匹配到对应账号", desensitizedMobile),
                        CarrierDataExceptionEnum.USER_NAME_PWD_ERROR);

        // 3.创建Token
        CreateToken createTokenModel = new CreateToken();
        createTokenModel.setLoginSource(PlatformProdEnums.LOGISTICS_TMS_WX_APP.getCode());
        createTokenModel.setUserId(userInfo.getUserId());
        createTokenModel.setUserName(userInfo.getUserName());
        createTokenModel.setUserAccount(userInfo.getUserAccount());
        createTokenModel.setExpireTime(configKeyConstant.tokenAppExp);
        String token = createToken(createTokenModel);
        log.info("TMS移动办公小程序 wechat mobile:【{}】 -> 登录成功", desensitizedMobile);

        //更新t_user表绑定的企微id
        UpdateWechatUserIdMessage updateWechatUserIdMessage = new UpdateWechatUserIdMessage();
        updateWechatUserIdMessage.setUserId(userInfo.getUserId());
        updateWechatUserIdMessage.setWechatUserId(requestModel.getQyWeChatUserId());
        updateWechatUserIdMessage.setOperator(userInfo.getUserName());
        rabbitMqPublishBiz.syncUpdateWechatUserId(updateWechatUserIdMessage);
        return new QYWeChatLoginResponseModel()
                .setToken(token);
    }
}
