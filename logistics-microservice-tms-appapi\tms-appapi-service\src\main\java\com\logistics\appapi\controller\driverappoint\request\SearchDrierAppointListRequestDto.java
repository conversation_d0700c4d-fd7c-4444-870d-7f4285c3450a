package com.logistics.appapi.controller.driverappoint.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/17
 */
@Data
public class SearchDrierAppointListRequestDto extends AbstractPageForm<SearchDrierAppointListRequestDto> {

	@ApiModelProperty(value = "预约日期 yyyy-MM", required = true)
	@NotBlank(message = "请选择日期")
	private String appointDate;

	@ApiModelProperty(value = "关联车辆状态：0 未关联，1 已关联", required = true)
	@NotBlank(message = "关联车辆状态不能为空")
	private String associatedVehicleStatus;
}
