package com.logistics.tms.api.feign.platformcompany;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.platformcompany.hystrix.PlatformCompanyServiceApiHystrix;
import com.logistics.tms.api.feign.platformcompany.model.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/11/11 17:39
 */
@Api(value = "API-PlatformCompanyServiceApi-结算主体配置")
@FeignClient(name = "logistics-tms-services", fallback = PlatformCompanyServiceApiHystrix.class)
public interface PlatformCompanyServiceApi {

    @ApiOperation(value = "结算主体列表")
    @PostMapping(value = "/service/platformCompany/searchPlatformCompanyList")
    Result<PageInfo<SearchPlatformCompanyListResponseModel>> searchPlatformCompanyList(@RequestBody SearchPlatformCompanyListRequestModel requestModel);

    @ApiOperation(value = "新增结算主体")
    @PostMapping(value = "/service/platformCompany/addPlatformCompany")
    Result<Boolean> addPlatformCompany(@RequestBody AddPlatformCompanyRequestModel requestModel);

    @ApiOperation(value = "删除结算主体")
    @PostMapping(value = "/service/platformCompany/delPlatformCompany")
    Result<Boolean> delPlatformCompany(@RequestBody DelPlatformCompanyRequestModel requestModel);

    @ApiOperation(value = "查询结算主体(下拉列表使用)")
    @PostMapping(value = "/service/platformCompany/platformCompanySelectList")
    Result<List<PlatformCompanySelectListResponseModel>> platformCompanySelectList(@RequestBody PlatformCompanySelectListRequestModel requestModel);
    
}
