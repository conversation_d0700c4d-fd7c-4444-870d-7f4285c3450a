package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CarrierOrderDetailGoodsInfoModel {
    @ApiModelProperty("货物id")
    private Long goodsId;
    @ApiModelProperty("需求单货物id")
    private Long demandOrderGoodsId;
    @ApiModelProperty("品名")
    private String goodsName;
    @ApiModelProperty("规格")
    private Integer length;
    @ApiModelProperty("规格")
    private Integer width;
    @ApiModelProperty("规格")
    private Integer height;
    @ApiModelProperty("规格")
    private String goodsSize;
    @ApiModelProperty("预提件数")
    private BigDecimal expectAmount;
    @ApiModelProperty("实提件数")
    private BigDecimal loadAmount;
    @ApiModelProperty("实卸件数")
    private BigDecimal unloadAmount;
    @ApiModelProperty("签收件数")
    private BigDecimal signAmount;


}
