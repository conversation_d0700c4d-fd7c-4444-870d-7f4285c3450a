package com.logistics.management.webapi.api.impl.insurancecompany.mapper;

import com.logistics.management.webapi.api.feign.insurancecompany.dto.InsuranceCompanyListResponseDto;
import com.logistics.management.webapi.base.enums.EnabledEnum;
import com.logistics.tms.api.feign.insurancecompany.model.InsuranceCompanyListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @Author: sj
 * @Date: 2019/5/30 10:07
 */
public class InsuranceCompanyListMapping extends MapperMapping<InsuranceCompanyListResponseModel,InsuranceCompanyListResponseDto>{
    @Override
    public void configure() {
        InsuranceCompanyListResponseModel model = this.getSource();
        InsuranceCompanyListResponseDto dto = this.getDestination();
        if(model!=null){
            dto.setEnabledLabel(EnabledEnum.getEnum(model.getEnabled()).getValue());
            if(model.getLastModifiedTime()!=null){
                dto.setLastModifiedTime(DateUtils.dateToString(model.getLastModifiedTime(),DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
            }
        }
    }
}
