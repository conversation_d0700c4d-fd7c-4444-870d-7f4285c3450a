package com.logistics.tms.controller.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RenewableAssignDriverDetailResponseModel {

    @ApiModelProperty(value = "审核订单id")
    private Long renewableOrderId;

    @ApiModelProperty(value = "单号")
    private String renewableOrderCode;

    @ApiModelProperty("乐橘新生客户名称（企业）")
    private String customerName;
    @ApiModelProperty("乐橘新生客户姓名（个人）")
    private String customerUserName;
    @ApiModelProperty("乐橘新生客户手机号（个人）")
    private String customerUserMobile;

    @ApiModelProperty(value = "业务类型：1 公司，2 个人")
    private Integer businessType;


    @ApiModelProperty(value = "下单数量")
    private BigDecimal goodsAmountTotal;

    @ApiModelProperty(value = "发货仓库")
    private String loadWarehouse;
    @ApiModelProperty(value = "发货省份")
    private String loadProvinceName;
    @ApiModelProperty(value = "发货城市")
    private String loadCityName;
    @ApiModelProperty(value = "发货区县")
    private String loadAreaName;

    @ApiModelProperty(value = "司机")
    private String staffName;
    @ApiModelProperty(value = "手机号")
    private String staffMobile;

}
