package com.logistics.management.webapi.base.enums;

import lombok.Getter;

/**
 * 业务单据, 10:需求单 20:运单
 */
@Getter
public enum WorkGroupOrderTypeEnum {
    DEFAULT(0, ""),
    DEMAND_ORDER(10, "需求单"),
    CARRIER_ORDER(20, "运单")
    ;

    private final Integer key;
    private final String value;

    WorkGroupOrderTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static WorkGroupOrderTypeEnum getEnum(Integer key) {
        for (WorkGroupOrderTypeEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return DEFAULT;
    }

    public static WorkGroupOrderTypeEnum getEnumByKeyStr(String key) {
        for (WorkGroupOrderTypeEnum t : values()) {
            if (t.getKey().toString().equals(key) ) {
                return t;
            }
        }
        return DEFAULT;
    }
}
