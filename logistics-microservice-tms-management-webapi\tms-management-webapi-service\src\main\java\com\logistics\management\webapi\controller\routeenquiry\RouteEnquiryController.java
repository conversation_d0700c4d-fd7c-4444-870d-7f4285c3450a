package com.logistics.management.webapi.controller.routeenquiry;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.ConvertPageInfoUtil;
import com.logistics.management.webapi.client.routeenquiry.RouteEnquiryClient;
import com.logistics.management.webapi.client.routeenquiry.request.*;
import com.logistics.management.webapi.client.routeenquiry.response.*;
import com.logistics.management.webapi.controller.routeenquiry.mapping.RouteEnquiryGetDetailMapping;
import com.logistics.management.webapi.controller.routeenquiry.mapping.RouteEnquiryGetQuoteDetailMapping;
import com.logistics.management.webapi.controller.routeenquiry.mapping.RouteEnquirySearchListMapping;
import com.logistics.management.webapi.controller.routeenquiry.mapping.RouteEnquirySearchSummaryListMapping;
import com.logistics.management.webapi.controller.routeenquiry.request.*;
import com.logistics.management.webapi.controller.routeenquiry.response.GetRouteEnquiryDetailResponseDto;
import com.logistics.management.webapi.controller.routeenquiry.response.GetRouteEnquiryQuoteDetailResponseDto;
import com.logistics.management.webapi.controller.routeenquiry.response.SearchRouteEnquiryListResponseDto;
import com.logistics.management.webapi.controller.routeenquiry.response.SearchRouteEnquirySummaryListResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tools.utils.YeloExcelUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;

/**
 * 路线询价
 * @author: wjf
 * @date: 2024/7/8 15:16
 */
@Api(value = "路线询价",tags = "路线询价")
@RestController
@RequestMapping(value = "/api/routeEnquiry")
public class RouteEnquiryController {

    @Resource
    private RouteEnquiryClient routeEnquiryClient;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private ConfigKeyConstant configKeyConstant;

    /**
     * 查询列表 3.25.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/searchList")
    public Result<PageInfo<SearchRouteEnquiryListResponseDto>> searchList(@RequestBody SearchRouteEnquiryListRequestDto requestDto){
        Result<PageInfo<SearchRouteEnquiryListResponseModel>> result = routeEnquiryClient.searchList(MapperUtils.mapper(requestDto, SearchRouteEnquiryListRequestModel.class));
        result.throwException();
        PageInfo<SearchRouteEnquiryListResponseDto> pageInfo = ConvertPageInfoUtil.convertPageInfo(result.getData(), SearchRouteEnquiryListResponseDto.class, new RouteEnquirySearchListMapping());
        return Result.success(pageInfo);
    }

    /**
     * 创建 3.25.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/create")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> create(@RequestBody @Valid CreateRouteEnquiryRequestDto requestDto){
        //校验地址信息
        Map<String, String> areaIdMap = new HashMap<>();
        for (CreateRouteEnquiryAddressListRequestDto addressDto : requestDto.getAddressList()) {
            //生产企业-发货仓库必填
            if (CommonConstant.TWO.equals(addressDto.getFromAddressType()) && StringUtils.isBlank(addressDto.getFromWarehouse())){
                throw new BizException(ManagementWebApiExceptionEnum.ROUTE_ENQUIRY_CREATE_WAREHOUSE_EMPTY);
            }
            //发货仓库长度不能超过50
            if (StringUtils.isNotBlank(addressDto.getFromWarehouse()) && addressDto.getFromWarehouse().length() > CommonConstant.INT_FIFTY){
                throw new BizException(ManagementWebApiExceptionEnum.ROUTE_ENQUIRY_CREATE_WAREHOUSE_EMPTY);
            }
            //路线不能重复
            if (addressDto.getToAreaId().equals(areaIdMap.get(addressDto.getFromAreaId()))){
                throw new BizException(ManagementWebApiExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
            }

            areaIdMap.put(addressDto.getFromAreaId(), addressDto.getToAreaId());
        }
        //截止时间必须大于开始时间
        Date quoteStartTime = DateUtils.stringToDate(requestDto.getQuoteStartTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN);
        Date quoteEndTime = DateUtils.stringToDate(requestDto.getQuoteEndTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN);
        if (!quoteEndTime.after(quoteStartTime)){
            throw new BizException(ManagementWebApiExceptionEnum.ROUTE_ENQUIRY_CREATE_QUOTE_TIME_ERROR);
        }

        return routeEnquiryClient.create(MapperUtils.mapper(requestDto, CreateRouteEnquiryRequestModel.class));
    }

    /**
     * 查询详情 3.25.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/getDetail")
    public Result<GetRouteEnquiryDetailResponseDto> getDetail(@RequestBody @Valid GetRouteEnquiryDetailRequestDto requestDto){
        Result<GetRouteEnquiryDetailResponseModel> result = routeEnquiryClient.getDetail(MapperUtils.mapper(requestDto, GetRouteEnquiryDetailRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        if (result.getData() != null && ListUtils.isNotEmpty(result.getData().getFileList())) {
            for (RouteEnquiryFileListResponseModel model : result.getData().getFileList()) {
                sourceSrcList.addAll(model.getFileList());
            }
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(), GetRouteEnquiryDetailResponseDto.class, new RouteEnquiryGetDetailMapping(configKeyConstant.fileAccessAddress, imageMap)));
    }

    /**
     * 取消报价 3.25.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/cancelQuote")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> cancelQuote(@RequestBody @Valid GetRouteEnquiryDetailRequestDto requestDto){
        return routeEnquiryClient.cancelQuote(MapperUtils.mapper(requestDto, GetRouteEnquiryDetailRequestModel.class));
    }

    /**
     * 查询车主报价详情 3.25.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/getQuoteDetail")
    public Result<List<GetRouteEnquiryQuoteDetailResponseDto>> getQuoteDetail(@RequestBody @Valid GetRouteEnquiryQuoteDetailRequestDto requestDto){
        Result<List<GetRouteEnquiryQuoteDetailResponseModel>> result = routeEnquiryClient.getQuoteDetail(MapperUtils.mapper(requestDto, GetRouteEnquiryQuoteDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), GetRouteEnquiryQuoteDetailResponseDto.class, new RouteEnquiryGetQuoteDetailMapping()));
    }

    /**
     * 选择车主报价 3.25.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/selectCarrierQuote")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> selectCarrierQuote(@RequestBody @Valid RouteEnquirySelectCarrierQuoteRequestDto requestDto){
        return routeEnquiryClient.selectCarrierQuote(MapperUtils.mapper(requestDto, RouteEnquirySelectCarrierQuoteRequestModel.class));
    }

    /**
     * 结算审核 3.25.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/settleAudit")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> settleAudit(@RequestBody @Valid RouteEnquirySettleAuditRequestDto requestDto){
        return routeEnquiryClient.settleAudit(MapperUtils.mapper(requestDto, RouteEnquirySettleAuditRequestModel.class));
    }

    /**
     * 归档 3.25.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/archive")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> archive(@RequestBody @Valid RouteEnquiryArchiveRequestDto requestDto){
        return routeEnquiryClient.archive(MapperUtils.mapper(requestDto, RouteEnquiryArchiveRequestModel.class));
    }

    /**
     * 查询汇总列表 3.25.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/searchSummaryList")
    public Result<PageInfo<SearchRouteEnquirySummaryListResponseDto>> searchSummaryList(@RequestBody SearchRouteEnquirySummaryListRequestDto requestDto){
        Result<PageInfo<SearchRouteEnquirySummaryListResponseModel>> result = routeEnquiryClient.searchSummaryList(MapperUtils.mapper(requestDto, SearchRouteEnquirySummaryListRequestModel.class));
        result.throwException();
        return Result.success(ConvertPageInfoUtil.convertPageInfo(result.getData(), SearchRouteEnquirySummaryListResponseDto.class, new RouteEnquirySearchSummaryListMapping()));
    }

    /**
     * 导出汇总列表 3.25.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/exportSummaryList")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportSummaryList(@RequestBody SearchRouteEnquirySummaryListRequestDto requestDto, HttpServletResponse response){
        Result<List<SearchRouteEnquirySummaryListResponseModel>> result = routeEnquiryClient.exportSummaryList(MapperUtils.mapper(requestDto, SearchRouteEnquirySummaryListRequestModel.class));
        result.throwException();
        List<SearchRouteEnquirySummaryListResponseDto> list = MapperUtils.mapper(result.getData(), SearchRouteEnquirySummaryListResponseDto.class, new RouteEnquirySearchSummaryListMapping());
        String fileName = "自营物流路线竞价表";
        try {
            YeloExcelUtils.doExportMoreThreadByOneSheet(response, list, false, SearchRouteEnquirySummaryListResponseDto.class, fileName);
        } catch (Exception e) {
            return;
        }
    }

}
