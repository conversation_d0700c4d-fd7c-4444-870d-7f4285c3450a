package com.logistics.management.webapi.controller.vehiclesettlement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2019/10/14 9:20
 */
@Data
public class VehicleSettlementDetailRequestDto {
    @ApiModelProperty("结算ID")
    @NotBlank(message = "id不能为空")
    private String vehicleSettlementId;
    @ApiModelProperty("操作动作类型：1 查看，2 去结算")
    private String operateType;
}
