package com.logistics.tms.api.feign.entrustaddress.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2018/12/21 16:07
 */
@Data
public class AddOrModifyEntrustAddressRequestModel {
    @ApiModelProperty("地址id")
    private Long entrustAddressId;
    @ApiModelProperty("货主公司id")
    private Long companyEntrustId;
    @ApiModelProperty("购货公司名称")
    private String companyName;
    @ApiModelProperty("联系人")
    private String contactName;
    @ApiModelProperty("联系方式")
    private String contactMobile;
    @ApiModelProperty("仓库")
    private String warehouse;
    private Long provinceId;
    private String provinceName;
    private Long cityId;
    private String cityName;
    private Long areaId;
    private String areaName;
    private String detailAddress;

    @ApiModelProperty("地址类型：1 发货，2 收货")
    private Integer addressType;
}
