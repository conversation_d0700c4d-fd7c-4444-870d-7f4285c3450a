package com.logistics.tms.client.feign.tray.basicdata.commonaddress.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel("批量查询客户信息请求")
public class BatchQueryCustomerInfoRequest {

    @NotEmpty(message = "公司名称列表不能为空")
    @ApiModelProperty("公司名称列表")
    private List<String> companyNames;
}