package com.logistics.appapi.client.driverappoint.hystrix;

import com.logistics.appapi.client.driverappoint.DriverAppointClient;
import com.logistics.appapi.client.driverappoint.request.DriverAppointAssociatedVehicleRequestModel;
import com.logistics.appapi.client.driverappoint.request.SearchAppointRequestModel;
import com.logistics.appapi.client.driverappoint.request.SearchDrierAppointDetailRequestModel;
import com.logistics.appapi.client.driverappoint.response.SearchAppointCountResponseModel;
import com.logistics.appapi.client.driverappoint.response.SearchDrierAppointDetailResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/3/11 14:23
 */
@Component
public class DriverAppointClientHystrix implements DriverAppointClient {
    @Override
    public Result<SearchAppointCountResponseModel> searchAppointList(SearchAppointRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SearchDrierAppointDetailResponseModel> searchDrierAppointDetail(SearchDrierAppointDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> associatedVehicle(DriverAppointAssociatedVehicleRequestModel requestModel) {
        return Result.timeout();
    }
}
