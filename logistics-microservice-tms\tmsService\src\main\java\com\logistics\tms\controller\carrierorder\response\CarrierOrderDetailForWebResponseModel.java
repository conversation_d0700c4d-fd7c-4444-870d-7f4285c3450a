package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/22
 */
@Data
public class CarrierOrderDetailForWebResponseModel {

	@ApiModelProperty("运单ID")
	private Long carrierOrderId;

	@ApiModelProperty("需求单ID")
	private Long demandOrderId;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("客户单号")
	private String customerOrderCode;

	@ApiModelProperty("运单状态")
	private Integer status;

	@ApiModelProperty("是否取消")
	private Integer ifCancel;

	@ApiModelProperty("是否放空：0 否，1 是")
	private Integer ifEmpty;

	@ApiModelProperty("委托方")
	private String entrustCompany;

	@ApiModelProperty("拨打联系人")
	private String contactPerson;

	@ApiModelProperty("拨打联系人手机号")
	private String contactMobile;

	@ApiModelProperty("调度人")
	private String dispatchUserName;

	@ApiModelProperty("运单基本信息")
	private CarrierOrderDetailBasicInfoModel carrierOrderDetailBasicInfo;

	@ApiModelProperty("运单货物信息")
	private List<CarrierOrderDetailGoodsInfoModel> carrierOrderDetailGoodsInfo;

	@ApiModelProperty("运单车辆司机信息")
	private List<CarrierOrderDetailVehicleDriverInfoModel> carrierOrderDetailVehicleDriverInfo;

	@ApiModelProperty("运单事件")
	private List<CarrierOrderDetailEventModel> carrierOrderDetailEvents;

	@ApiModelProperty("票据信息")
	private CarrierOrderDetailTicketsModel carrierOrderDetailTickets;

	@ApiModelProperty("备注")
	private String remark;

	@ApiModelProperty("取消原因")
	private String cancelReason;

	@ApiModelProperty("货物单位：1 件，2 吨")
	private Integer goodsUnit;

	@ApiModelProperty("预计承运件数")
	private BigDecimal expectAmount;

	@ApiModelProperty("实际提货件数")
	private BigDecimal loadAmount;

	@ApiModelProperty("实际卸货件数")
	private BigDecimal unloadAmount;

	@ApiModelProperty("签收件数")
	private BigDecimal signAmount;

	@ApiModelProperty("中石化运单号")
	private String sinopecOrderNo;

	@ApiModelProperty("中石化下单类型：20 拉取，21 推送")
	private Integer orderType;

	@ApiModelProperty("车主对账单状态, -2:未关联对账")
	private Integer carrierSettleStatementStatus;

	@ApiModelProperty("是否按编码回收 0:否 1:是   v2.44")
	private Integer ifRecycleByCode;
}
