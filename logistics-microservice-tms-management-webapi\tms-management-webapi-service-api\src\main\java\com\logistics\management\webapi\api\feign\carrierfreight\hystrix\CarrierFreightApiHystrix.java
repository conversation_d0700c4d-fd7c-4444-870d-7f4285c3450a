package com.logistics.management.webapi.api.feign.carrierfreight.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.carrierfreight.CarrierFreightApi;
import com.logistics.management.webapi.api.feign.carrierfreight.dto.CarrierFreightAddRequestDto;
import com.logistics.management.webapi.api.feign.carrierfreight.dto.CarrierFreightEnableRequestDto;
import com.logistics.management.webapi.api.feign.carrierfreight.dto.SearchCarrierFreightRequestDto;
import com.logistics.management.webapi.api.feign.carrierfreight.dto.SearchCarrierFreightResponseDto;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/9/1 14:34
 */
@Component
public class CarrierFreightApiHystrix implements CarrierFreightApi {

    @Override
    public Result<Boolean> addCarrierFreight(CarrierFreightAddRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> enable(CarrierFreightEnableRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<SearchCarrierFreightResponseDto>> searchList(SearchCarrierFreightRequestDto requestDto) {
        return Result.timeout();
    }
}
