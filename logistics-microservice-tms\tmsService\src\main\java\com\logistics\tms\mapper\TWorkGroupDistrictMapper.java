package com.logistics.tms.mapper;

import com.logistics.tms.controller.workgroup.response.WorkGroupDistrictResponseModel;
import com.logistics.tms.entity.TWorkGroupDistrict;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
* Created by Mybatis Generator on 2023/12/21
*/
@Mapper
public interface TWorkGroupDistrictMapper extends BaseMapper<TWorkGroupDistrict> {

    List<TWorkGroupDistrict> selectByWorkGroupId(@Param("workGroupId") Long workGroupId);

    List<TWorkGroupDistrict> selectAllByWorkGroupIdInAndAddress(@Param("workGroupIds") Collection<Long> workGroupIds);

    List<WorkGroupDistrictResponseModel> getWorkGroupDistrictDetail(@Param("workGroupId") Long workGroupId);
}