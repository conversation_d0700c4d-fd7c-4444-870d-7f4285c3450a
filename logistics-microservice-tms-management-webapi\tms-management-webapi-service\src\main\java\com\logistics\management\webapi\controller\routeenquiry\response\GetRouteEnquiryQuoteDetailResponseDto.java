package com.logistics.management.webapi.controller.routeenquiry.response;

import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/7/9 10:12
 */
@Data
public class GetRouteEnquiryQuoteDetailResponseDto {

    /**
     * 路线询价单地址车主报价表id
     */
    private String routeEnquiryAddressQuoteId="";

    /**
     * 发货地
     */
    private String loadAddress="";


    /**
     * 收货地
     */
    private String unloadAddress="";

    /**
     * 运距
     */
    private String distance="";

    /**
     * 结算价
     */
    private String quotePrice="";

    /**
     * 结算模式：1 单价，2 一口价
     */
    private String quotePriceType="";
    /**
     * 结算模式文本
     */
    private String quotePriceTypeLabel="";

    /**
     * 备注
     */
    private String quoteRemark="";

}
