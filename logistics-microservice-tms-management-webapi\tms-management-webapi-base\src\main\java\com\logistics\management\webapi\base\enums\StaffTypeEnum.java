package com.logistics.management.webapi.base.enums;

/**
 * @author: wjf
 * @date: 2019/6/5 20:26
 */
public enum StaffTypeEnum {
    DEFAULT(0,"","0"),
    DRIVER(1,"驾驶员","1"),
    SUPERCARGO(2,"押运员","2"),
    DRIVER_SUPERCARGO(3,"驾驶员及押运员","3"),
    ;

    private Integer key;
    private String value;
    private String keyStr;

    StaffTypeEnum(Integer key, String value, String keyStr) {
        this.key = key;
        this.value = value;
        this.keyStr = keyStr;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getKeyStr() {
        return keyStr;
    }

    public static StaffTypeEnum getEnum(Integer key) {
        for (StaffTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
