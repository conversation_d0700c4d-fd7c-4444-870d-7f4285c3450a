package com.logistics.tms.mapper;

import com.logistics.tms.biz.shippingfreight.model.ShippingFreightRuleVehicleLengthSqlConditionModel;
import com.logistics.tms.entity.TShippingFreightRuleVehicleLength;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/09/19
*/
@Mapper
public interface TShippingFreightRuleVehicleLengthMapper extends BaseMapper<TShippingFreightRuleVehicleLength> {
    int deleteByPrimaryKey(Long id);

    int insert(TShippingFreightRuleVehicleLength record);

    int insertSelective(TShippingFreightRuleVehicleLength record);

    TShippingFreightRuleVehicleLength selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TShippingFreightRuleVehicleLength record);

    int updateByPrimaryKey(TShippingFreightRuleVehicleLength record);

    List<TShippingFreightRuleVehicleLength> selectListByCondition(@Param("param1") ShippingFreightRuleVehicleLengthSqlConditionModel sqlConditionModel);

    int batchInsert(@Param("list") List<TShippingFreightRuleVehicleLength> list);
    int batchUpdate(@Param("list") List<TShippingFreightRuleVehicleLength> list);


}