package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DemandOrderDetailRequestDto {

    @ApiModelProperty(value = "委托单id", required = true)
    @NotBlank(message = "请选中数据")
    private String demandId;

}
