package com.logistics.management.webapi.client.routeenquiry.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/7/9 17:16
 */
@Data
public class RouteEnquiryFileListResponseModel {

    /**
     * 操作时间
     */
    private Date lastModifiedTime;

    /**
     * 操作人
     */
    private String lastModifiedBy;

    /**
     * 附件类型：1 报价单，2 归档文件
     */
    private Integer attachmentType;

    /**
     * 文件列表
     */
    private List<String> fileList=new ArrayList<>();

}
