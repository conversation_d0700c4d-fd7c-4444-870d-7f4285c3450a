<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TWorkOrderMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TWorkOrder" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="work_order_priority" property="workOrderPriority" jdbcType="INTEGER" />
    <result column="work_order_code" property="workOrderCode" jdbcType="VARCHAR" />
    <result column="work_order_type" property="workOrderType" jdbcType="INTEGER" />
    <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT" />
    <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR" />
    <result column="carrier_order_id" property="carrierOrderId" jdbcType="BIGINT" />
    <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR" />
    <result column="anomaly_type_one" property="anomalyTypeOne" jdbcType="INTEGER" />
    <result column="anomaly_type_two" property="anomalyTypeTwo" jdbcType="INTEGER" />
    <result column="contact_name" property="contactName" jdbcType="VARCHAR" />
    <result column="contact_telephone" property="contactTelephone" jdbcType="VARCHAR" />
    <result column="check_contact" property="checkContact" jdbcType="INTEGER" />
    <result column="is_arrive_scene" property="isArriveScene" jdbcType="INTEGER" />
    <result column="arrive_scene_picture" property="arriveScenePicture" jdbcType="VARCHAR" />
    <result column="address_head" property="addressHead" jdbcType="VARCHAR" />
    <result column="address_detail" property="addressDetail" jdbcType="VARCHAR" />
    <result column="report_source" property="reportSource" jdbcType="INTEGER" />
    <result column="report_user_name" property="reportUserName" jdbcType="VARCHAR" />
    <result column="report_time" property="reportTime" jdbcType="TIMESTAMP" />
    <result column="solve_user_name" property="solveUserName" jdbcType="VARCHAR" />
    <result column="solve_time" property="solveTime" jdbcType="TIMESTAMP" />
    <result column="company_carrier_type" property="companyCarrierType" jdbcType="INTEGER" />
    <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT" />
    <result column="company_carrier_name" property="companyCarrierName" jdbcType="VARCHAR" />
    <result column="carrier_contact_id" property="carrierContactId" jdbcType="BIGINT" />
    <result column="carrier_contact_name" property="carrierContactName" jdbcType="VARCHAR" />
    <result column="carrier_contact_phone" property="carrierContactPhone" jdbcType="VARCHAR" />
    <result column="driver_id" property="driverId" jdbcType="BIGINT" />
    <result column="driver_name" property="driverName" jdbcType="VARCHAR" />
    <result column="driver_mobile" property="driverMobile" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, status, work_order_priority, work_order_code, work_order_type, demand_order_id, 
    demand_order_code, carrier_order_id, carrier_order_code, anomaly_type_one, anomaly_type_two, 
    contact_name, contact_telephone, check_contact, is_arrive_scene, arrive_scene_picture, 
    address_head, address_detail, report_source, report_user_name, report_time, solve_user_name, 
    solve_time, company_carrier_type, company_carrier_id, company_carrier_name, carrier_contact_id, 
    carrier_contact_name, carrier_contact_phone, driver_id, driver_name, driver_mobile, 
    remark, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_work_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_work_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TWorkOrder" >
    insert into t_work_order (id, status, work_order_priority, 
      work_order_code, work_order_type, demand_order_id, 
      demand_order_code, carrier_order_id, carrier_order_code, 
      anomaly_type_one, anomaly_type_two, contact_name, 
      contact_telephone, check_contact, is_arrive_scene, 
      arrive_scene_picture, address_head, address_detail, 
      report_source, report_user_name, report_time, 
      solve_user_name, solve_time, company_carrier_type, 
      company_carrier_id, company_carrier_name, carrier_contact_id, 
      carrier_contact_name, carrier_contact_phone, 
      driver_id, driver_name, driver_mobile, 
      remark, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{workOrderPriority,jdbcType=INTEGER}, 
      #{workOrderCode,jdbcType=VARCHAR}, #{workOrderType,jdbcType=INTEGER}, #{demandOrderId,jdbcType=BIGINT}, 
      #{demandOrderCode,jdbcType=VARCHAR}, #{carrierOrderId,jdbcType=BIGINT}, #{carrierOrderCode,jdbcType=VARCHAR}, 
      #{anomalyTypeOne,jdbcType=INTEGER}, #{anomalyTypeTwo,jdbcType=INTEGER}, #{contactName,jdbcType=VARCHAR}, 
      #{contactTelephone,jdbcType=VARCHAR}, #{checkContact,jdbcType=INTEGER}, #{isArriveScene,jdbcType=INTEGER}, 
      #{arriveScenePicture,jdbcType=VARCHAR}, #{addressHead,jdbcType=VARCHAR}, #{addressDetail,jdbcType=VARCHAR}, 
      #{reportSource,jdbcType=INTEGER}, #{reportUserName,jdbcType=VARCHAR}, #{reportTime,jdbcType=TIMESTAMP}, 
      #{solveUserName,jdbcType=VARCHAR}, #{solveTime,jdbcType=TIMESTAMP}, #{companyCarrierType,jdbcType=INTEGER}, 
      #{companyCarrierId,jdbcType=BIGINT}, #{companyCarrierName,jdbcType=VARCHAR}, #{carrierContactId,jdbcType=BIGINT}, 
      #{carrierContactName,jdbcType=VARCHAR}, #{carrierContactPhone,jdbcType=VARCHAR}, 
      #{driverId,jdbcType=BIGINT}, #{driverName,jdbcType=VARCHAR}, #{driverMobile,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TWorkOrder" >
    insert into t_work_order
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="workOrderPriority != null" >
        work_order_priority,
      </if>
      <if test="workOrderCode != null" >
        work_order_code,
      </if>
      <if test="workOrderType != null" >
        work_order_type,
      </if>
      <if test="demandOrderId != null" >
        demand_order_id,
      </if>
      <if test="demandOrderCode != null" >
        demand_order_code,
      </if>
      <if test="carrierOrderId != null" >
        carrier_order_id,
      </if>
      <if test="carrierOrderCode != null" >
        carrier_order_code,
      </if>
      <if test="anomalyTypeOne != null" >
        anomaly_type_one,
      </if>
      <if test="anomalyTypeTwo != null" >
        anomaly_type_two,
      </if>
      <if test="contactName != null" >
        contact_name,
      </if>
      <if test="contactTelephone != null" >
        contact_telephone,
      </if>
      <if test="checkContact != null" >
        check_contact,
      </if>
      <if test="isArriveScene != null" >
        is_arrive_scene,
      </if>
      <if test="arriveScenePicture != null" >
        arrive_scene_picture,
      </if>
      <if test="addressHead != null" >
        address_head,
      </if>
      <if test="addressDetail != null" >
        address_detail,
      </if>
      <if test="reportSource != null" >
        report_source,
      </if>
      <if test="reportUserName != null" >
        report_user_name,
      </if>
      <if test="reportTime != null" >
        report_time,
      </if>
      <if test="solveUserName != null" >
        solve_user_name,
      </if>
      <if test="solveTime != null" >
        solve_time,
      </if>
      <if test="companyCarrierType != null" >
        company_carrier_type,
      </if>
      <if test="companyCarrierId != null" >
        company_carrier_id,
      </if>
      <if test="companyCarrierName != null" >
        company_carrier_name,
      </if>
      <if test="carrierContactId != null" >
        carrier_contact_id,
      </if>
      <if test="carrierContactName != null" >
        carrier_contact_name,
      </if>
      <if test="carrierContactPhone != null" >
        carrier_contact_phone,
      </if>
      <if test="driverId != null" >
        driver_id,
      </if>
      <if test="driverName != null" >
        driver_name,
      </if>
      <if test="driverMobile != null" >
        driver_mobile,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="workOrderPriority != null" >
        #{workOrderPriority,jdbcType=INTEGER},
      </if>
      <if test="workOrderCode != null" >
        #{workOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="workOrderType != null" >
        #{workOrderType,jdbcType=INTEGER},
      </if>
      <if test="demandOrderId != null" >
        #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderCode != null" >
        #{demandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="carrierOrderId != null" >
        #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null" >
        #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="anomalyTypeOne != null" >
        #{anomalyTypeOne,jdbcType=INTEGER},
      </if>
      <if test="anomalyTypeTwo != null" >
        #{anomalyTypeTwo,jdbcType=INTEGER},
      </if>
      <if test="contactName != null" >
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactTelephone != null" >
        #{contactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="checkContact != null" >
        #{checkContact,jdbcType=INTEGER},
      </if>
      <if test="isArriveScene != null" >
        #{isArriveScene,jdbcType=INTEGER},
      </if>
      <if test="arriveScenePicture != null" >
        #{arriveScenePicture,jdbcType=VARCHAR},
      </if>
      <if test="addressHead != null" >
        #{addressHead,jdbcType=VARCHAR},
      </if>
      <if test="addressDetail != null" >
        #{addressDetail,jdbcType=VARCHAR},
      </if>
      <if test="reportSource != null" >
        #{reportSource,jdbcType=INTEGER},
      </if>
      <if test="reportUserName != null" >
        #{reportUserName,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null" >
        #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="solveUserName != null" >
        #{solveUserName,jdbcType=VARCHAR},
      </if>
      <if test="solveTime != null" >
        #{solveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyCarrierType != null" >
        #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null" >
        #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null" >
        #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactId != null" >
        #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactName != null" >
        #{carrierContactName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactPhone != null" >
        #{carrierContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null" >
        #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null" >
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null" >
        #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TWorkOrder" >
    update t_work_order
    <set >
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="workOrderPriority != null" >
        work_order_priority = #{workOrderPriority,jdbcType=INTEGER},
      </if>
      <if test="workOrderCode != null" >
        work_order_code = #{workOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="workOrderType != null" >
        work_order_type = #{workOrderType,jdbcType=INTEGER},
      </if>
      <if test="demandOrderId != null" >
        demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderCode != null" >
        demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="carrierOrderId != null" >
        carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null" >
        carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="anomalyTypeOne != null" >
        anomaly_type_one = #{anomalyTypeOne,jdbcType=INTEGER},
      </if>
      <if test="anomalyTypeTwo != null" >
        anomaly_type_two = #{anomalyTypeTwo,jdbcType=INTEGER},
      </if>
      <if test="contactName != null" >
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactTelephone != null" >
        contact_telephone = #{contactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="checkContact != null" >
        check_contact = #{checkContact,jdbcType=INTEGER},
      </if>
      <if test="isArriveScene != null" >
        is_arrive_scene = #{isArriveScene,jdbcType=INTEGER},
      </if>
      <if test="arriveScenePicture != null" >
        arrive_scene_picture = #{arriveScenePicture,jdbcType=VARCHAR},
      </if>
      <if test="addressHead != null" >
        address_head = #{addressHead,jdbcType=VARCHAR},
      </if>
      <if test="addressDetail != null" >
        address_detail = #{addressDetail,jdbcType=VARCHAR},
      </if>
      <if test="reportSource != null" >
        report_source = #{reportSource,jdbcType=INTEGER},
      </if>
      <if test="reportUserName != null" >
        report_user_name = #{reportUserName,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null" >
        report_time = #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="solveUserName != null" >
        solve_user_name = #{solveUserName,jdbcType=VARCHAR},
      </if>
      <if test="solveTime != null" >
        solve_time = #{solveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyCarrierType != null" >
        company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null" >
        company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null" >
        company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactId != null" >
        carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactName != null" >
        carrier_contact_name = #{carrierContactName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactPhone != null" >
        carrier_contact_phone = #{carrierContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null" >
        driver_id = #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null" >
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null" >
        driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TWorkOrder" >
    update t_work_order
    set status = #{status,jdbcType=INTEGER},
      work_order_priority = #{workOrderPriority,jdbcType=INTEGER},
      work_order_code = #{workOrderCode,jdbcType=VARCHAR},
      work_order_type = #{workOrderType,jdbcType=INTEGER},
      demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
      carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      anomaly_type_one = #{anomalyTypeOne,jdbcType=INTEGER},
      anomaly_type_two = #{anomalyTypeTwo,jdbcType=INTEGER},
      contact_name = #{contactName,jdbcType=VARCHAR},
      contact_telephone = #{contactTelephone,jdbcType=VARCHAR},
      check_contact = #{checkContact,jdbcType=INTEGER},
      is_arrive_scene = #{isArriveScene,jdbcType=INTEGER},
      arrive_scene_picture = #{arriveScenePicture,jdbcType=VARCHAR},
      address_head = #{addressHead,jdbcType=VARCHAR},
      address_detail = #{addressDetail,jdbcType=VARCHAR},
      report_source = #{reportSource,jdbcType=INTEGER},
      report_user_name = #{reportUserName,jdbcType=VARCHAR},
      report_time = #{reportTime,jdbcType=TIMESTAMP},
      solve_user_name = #{solveUserName,jdbcType=VARCHAR},
      solve_time = #{solveTime,jdbcType=TIMESTAMP},
      company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
      company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
      carrier_contact_name = #{carrierContactName,jdbcType=VARCHAR},
      carrier_contact_phone = #{carrierContactPhone,jdbcType=VARCHAR},
      driver_id = #{driverId,jdbcType=BIGINT},
      driver_name = #{driverName,jdbcType=VARCHAR},
      driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>