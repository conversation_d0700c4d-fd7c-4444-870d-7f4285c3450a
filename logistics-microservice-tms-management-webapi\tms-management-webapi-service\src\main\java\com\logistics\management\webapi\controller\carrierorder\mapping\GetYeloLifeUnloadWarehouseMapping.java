package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.client.carrierorder.response.GetYeloLifeUnloadWarehouseResponseModel;
import com.logistics.management.webapi.controller.carrierorder.response.GetYeloLifeUnloadWarehouseResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/25
 */
public class GetYeloLifeUnloadWarehouseMapping extends MapperMapping<GetYeloLifeUnloadWarehouseResponseModel, GetYeloLifeUnloadWarehouseResponseDto> {

	@Override
	public void configure() {
		GetYeloLifeUnloadWarehouseResponseModel source = getSource();
		GetYeloLifeUnloadWarehouseResponseDto destination = getDestination();

		if (source != null) {
			//卸货仓库
			destination.setUnloadWarehouseCode(source.getWarehouseCode());
		}
	}
}
