package com.logistics.tms.biz.common;

import com.google.gson.Gson;
import com.leyi.auth.service.client.common.PlatformProdEnums;
import com.leyi.auth.service.client.utils.TokenUtil;
import com.logistics.tms.api.feign.common.model.WaterMarkModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.constant.ConfigKeyConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.biz.common.model.CreateQrCodeModel;
import com.logistics.tms.biz.email.EmailConstant;
import com.logistics.tms.biz.staff.model.CarrierDriverRelationModel;
import com.logistics.tms.biz.sysconfig.SysConfigBiz;
import com.logistics.tms.client.BasicDataClient;
import com.logistics.tms.client.BigDataClient;
import com.logistics.tms.client.model.RecyclePublishUpdateDemandRequestModel;
import com.logistics.tms.config.rdelayqueue.core.RDelayQueue;
import com.logistics.tms.controller.customeraccount.response.AccountInfoResponseModel;
import com.logistics.tms.controller.customeraccount.response.AccountRolesModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.basicdata.api.feign.datamap.model.GetProvinceCityAreaByKeywordsNewResponseModel;
import com.yelo.basicdata.api.feign.file.model.FileCopyRequestModel;
import com.yelo.basicdata.api.feign.file.model.GetOSSUrlResponseModel;
import com.yelo.basicdata.api.feign.gaodemap.model.DirectionDrivingRequestModel;
import com.yelo.basicdata.api.feign.gaodemap.model.GeoCodeForGeoRequestModel;
import com.yelo.basicdata.api.feign.gaodemap.model.GeoCodeForReGeoRequestModel;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.utils.RedisLockUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.entity.BaseEntity;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CommonBiz {

    @Autowired
    private TBusinessCodeMapper tBusinessCodeMapper;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private TCompanyEntrustMapper tCompanyEntrustMapper;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private TCustomerAccountMapper tCustomerAccountMapper;
    @Autowired
    private BasicDataClient basicDataClient;
    @Autowired
    private RedisLockUtils redisLockUtils;
    @Autowired
    private TCarrierContactMapper tCarrierContactMapper;
    @Autowired
    private TCarrierDriverRelationMapper tCarrierDriverRelationMapper;
    @Resource
    private SysConfigBiz sysConfigBiz;
    @Autowired
    private BigDataClient bigDataClient;
    @Resource
    private RDelayQueue rDelayQueue;

    private static final double EARTH_RADIUS = 6371000; // 地球半径，单位米



    public void setBaseEntityAdd(BaseEntity baseEntity, String operationName) {
        Date now = new Date();
        baseEntity.setValid(CommonConstant.INTEGER_ONE);
        baseEntity.setCreatedBy(operationName);
        baseEntity.setCreatedTime(now);
        baseEntity.setLastModifiedBy(operationName);
        baseEntity.setLastModifiedTime(now);
    }

    public void setBaseEntityAdd(BaseEntity baseEntity, String operationName, Date now) {
        baseEntity.setValid(CommonConstant.INTEGER_ONE);
        baseEntity.setCreatedBy(operationName);
        baseEntity.setCreatedTime(now);
        baseEntity.setLastModifiedBy(operationName);
        baseEntity.setLastModifiedTime(now);
    }

    //设置实体类的基本数据，编辑
    public void setBaseEntityModify(BaseEntity baseEntity, String operationName) {
        Date now = new Date();
        baseEntity.setLastModifiedBy(operationName);
        baseEntity.setLastModifiedTime(now);
    }

    public void setBaseEntityModify(BaseEntity baseEntity, String operationName, Date now) {
        baseEntity.setLastModifiedBy(operationName);
        baseEntity.setLastModifiedTime(now);
    }

    //生成承运商公司/调度单编号单号
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getBusinessTypeCode(BusinessCodeTypeEnum type, String key, String userName) {
        //返回值
        String businessTypeCode;
        //当前年月日小时
        String businessCodePrefix = StringUtils.isNotBlank(key) ? type.getValue() + key : type.getValue();
        String businessCodeDateFormat = StringUtils.isBlank(type.getDateFormat()) ? "" : DateUtils.dateToString(new Date(), type.getDateFormat());
        //分布式锁
        String lockKey = type.getKey() + businessCodePrefix + businessCodeDateFormat; //根据参数拼接唯一标识当作锁资源
        String uuid = UUIDGenerateUtil.generateUUID();
        Long startTime = System.currentTimeMillis();
        while (!redisLockUtils.tryLock(lockKey, uuid, 3)) { //设置锁并设置过期时间为3秒;如果锁被占用,超过3秒获取不到锁时抛出异常
            try {
                Thread.sleep(100);
                Long endTime = System.currentTimeMillis();
                if (endTime - startTime > 3000) {
                    throw new BizException(EntrustDataExceptionEnum.GET_LOCK_ERROR.getCode(), "生成承运商公司/调度单编号单号：" + EntrustDataExceptionEnum.GET_LOCK_ERROR.getMsg());
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        try {
            //查询数据库是否有存在的数据，锁数据
            TBusinessCode tBusinessCode = tBusinessCodeMapper.selectBusinessCodeByCondition(type.getKey(), businessCodePrefix, businessCodeDateFormat);
            //存在，在原有的序列相加
            Integer sequence = 1;
            if (tBusinessCode != null) {
                sequence = tBusinessCode.getBusinessCodeSequence() + 1;
                if (sequence > type.getMax()) {
                    throw new BizException(12121, "编号溢出");
                }
                Long id = tBusinessCode.getId();
                tBusinessCode = new TBusinessCode();
                tBusinessCode.setId(id);
                tBusinessCode.setBusinessCodeSequence(sequence);
                this.setBaseEntityModify(tBusinessCode, userName);
                tBusinessCodeMapper.updateByPrimaryKeySelective(tBusinessCode);
            } else {//不存在，新建一条，重新计算
                tBusinessCode = new TBusinessCode();
                tBusinessCode.setBusinessType(type.getKey());
                tBusinessCode.setBusinessCodeLength(type.getCodeLength());
                tBusinessCode.setBusinessCodePrefix(businessCodePrefix);
                tBusinessCode.setBusinessCodeSequence(sequence);
                tBusinessCode.setBusinessCodeDateFormat(businessCodeDateFormat);
                this.setBaseEntityAdd(tBusinessCode, userName);
                tBusinessCodeMapper.insertSelective(tBusinessCode);
            }
            //序号根据长度补位0
            NumberFormat numberFormat = NumberFormat.getInstance();
            numberFormat.setGroupingUsed(false);//没有逗号分隔数字0,001
            numberFormat.setMinimumIntegerDigits(type.getCodeLength());//整数部分最小四位数，补0
            businessTypeCode = businessCodePrefix + businessCodeDateFormat + numberFormat.format(sequence);

        } finally {
            //释放锁
            redisLockUtils.releaseLock(lockKey, uuid);
        }
        return businessTypeCode;
    }

    /**
     * 批量获取图片访问路径
     *
     * @param fileSrc
     */
    public Map<String, String> batchGetOSSFileUrl(List<String> fileSrc) {
        if (ListUtils.isEmpty(fileSrc)) {
            return new HashMap<>();
        }
        List<GetOSSUrlResponseModel> ossUrlList = basicDataClient.batchGetOSSFileUrl(fileSrc);
        if (ListUtils.isNotEmpty(ossUrlList)) {
            return ossUrlList.stream().collect(Collectors.toMap(GetOSSUrlResponseModel::getSourceFileSrc, GetOSSUrlResponseModel::getFileSrc, (key1, key2) -> key2));
        } else {
            return new HashMap<>();
        }
    }

    //临时文件夹拷贝到正式文件夹
    public String copyFileToDirectoryOfType(int type, String imgPath) {
        return copyFileToDirectoryOfType(type, "", imgPath);
    }

    //临时文件夹拷贝到正式文件夹
    public String copyFileToDirectoryOfType(int type, String code, String imgPath) {
        return copyFileToDirectoryOfType(type, code, imgPath, null);
    }

    //临时文件夹拷贝到正式文件夹
    public String copyFileToDirectoryOfType(int type, String code, String imgPath, WaterMarkModel waterMarkModel) {
        if (StringUtils.isBlank(imgPath)) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        //type图片上传对象的类型，
        if (CopyFileTypeEnum.COMPANY_CARRIER_TRADING.getKey().equals(type)) {//承运商营业执照
            sb.append(configKeyConstant.customerCompanyImageCatalog);
        } else if (CopyFileTypeEnum.COMPANY_CARRIER_AUTHORIZATION.getKey().equals(type)) {//授权书
            sb.append(configKeyConstant.customerCompanyImageCatalog);
        } else if (CopyFileTypeEnum.COMPANY_CARRIER_ROAD_TRANSPORT.getKey().equals(type)) {//道路许可证
            sb.append(configKeyConstant.customerCompanyImageCatalog);
        } else if (CopyFileTypeEnum.CUSTOMER_AUTHORIZATION.getKey().equals(type)) {//添加账号公司授权书
            sb.append(configKeyConstant.customerCompanyImageCatalog);
        } else if (CopyFileTypeEnum.CUSTOMER_CARRIER_IDENTITY_FACE.getKey().equals(type)) {//身份证人面像
            sb.append(configKeyConstant.customerCompanyImageCatalog);
        } else if (CopyFileTypeEnum.CUSTOMER_CARRIER_IDENTITY_NATIONAL.getKey().equals(type)) {//身份证国徽面
            sb.append(configKeyConstant.customerCompanyImageCatalog);
        } else if (CopyFileTypeEnum.CARRIER_ORDER_LOAD_TICKETS.getKey().equals(type)) {//提货单
            sb.append(configKeyConstant.carrierOrderLoadTicketsCatalog);
        } else if (CopyFileTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getKey().equals(type)) {//出库单
            sb.append(configKeyConstant.carrierOrderUnloadTicketsCatalog);
        } else if (CopyFileTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey().equals(type)) {//签收单
            sb.append(configKeyConstant.carrierOrderSignTicketsCatalog);
        } else if (CopyFileTypeEnum.CARRIER_ORDER_OTHER_TICKETS.getKey().equals(type)) {//其他单据
            sb.append(configKeyConstant.carrierOrderOtherTicketsCatalog);
        } else if (CopyFileTypeEnum.CARRIER_ORDER_ARRIVE_PICK_UP.getKey().equals(type)) {//到达装货地凭证
            sb.append(configKeyConstant.carrierOrderArriveLoadTicketsCatalog);
        } else if (CopyFileTypeEnum.CARRIER_ORDER_ARRIVE_UNLOADING.getKey().equals(type)) {//到达卸货地凭证
            sb.append(configKeyConstant.carrierOrderArriveUnloadTicketsCatalog);
        } else if (CopyFileTypeEnum.ANNOUNCEMENT_PICTURE.getKey().equals(type)) {//公告图片
            sb.append(configKeyConstant.announcementContextCatlog);
        } else if (CopyFileTypeEnum.PERSONAL_ACCIDENT_INSURANCE.getKey().equals(type)) {//个人意外险保单
            sb.append(configKeyConstant.personalAccidentInsuranceCatalog);
        } else if (CopyFileTypeEnum.VIOLATION_REGULATIONS.getKey().equals(type)) {//违章事故凭证
            sb.append(configKeyConstant.violationRegulationsCatalog);
        } else if (CopyFileTypeEnum.VEHICLE_BASIC_INFO.getKey().equals(type)) { //车辆资产相关文件信息
            sb.append(configKeyConstant.vehicleBasicInfoCatalog);
        } else if (CopyFileTypeEnum.OCCUPATIONAL_RECORD.getKey().equals(type)) { //司机从业资格证
            sb.append(configKeyConstant.occupationalRecordCatalog);
        } else if (CopyFileTypeEnum.INTEGRITY_EXAMINATION_RECORD.getKey().equals(type)) { //司机诚信考试记录
            sb.append(configKeyConstant.integrityExaminationRecordCatalog);
        } else if (CopyFileTypeEnum.CONTINUE_LEARNING_RECORD.getKey().equals(type)) { //司机继续教育记录
            sb.append(configKeyConstant.continueLearningRecordCatalog);
        } else if (CopyFileTypeEnum.STAFF_CERTIFICATE.getKey().equals(type)) {//人员信息证件
            sb.append(configKeyConstant.staffCertificateCatalog);
        } else if (CopyFileTypeEnum.VEHICLE_TIRE.getKey().equals(type)) {//轮胎信息凭证
            sb.append(configKeyConstant.vehicleTireCatalog);
        } else if (CopyFileTypeEnum.APP_ADVERTISEMENT.getKey().equals(type)) {//app首页图
            sb.append(configKeyConstant.appAdvertisementCatalog);
        } else if (CopyFileTypeEnum.DRIVER_PAYEE.getKey().equals(type)) {//司机收款账户证件
            sb.append(configKeyConstant.driverPayeeCatalog);
        } else if (CopyFileTypeEnum.FEEDBACK_TYPE.getKey().equals(type)) {//app 反馈信息
            sb.append(configKeyConstant.feedbackInfoCatalog);
        } else if (CopyFileTypeEnum.EXTERNAL_CARRIER_ORDER_UNLOAD_TICKETS.getKey().equals(type)) {//外部运单出库单
            sb.append(configKeyConstant.externalCarrierOrderCatalog);
        } else if (CopyFileTypeEnum.INSURANCE_TICKETS.getKey().equals(type)) {//保险
            sb.append(configKeyConstant.insuranceTicketsCatalog);
        } else if (CopyFileTypeEnum.COMPANY_CONTRACT_ORDER_FILE.getKey().equals(type)) {//合同文件
            sb.append(configKeyConstant.contractImageCatalog);
        } else if (CopyFileTypeEnum.COMPANY_ENTRUST_TRADING.getKey().equals(type)) {//货主营业执照
            sb.append(configKeyConstant.customerCompanyImageCatalog);
        } else if (CopyFileTypeEnum.LOAN_RECORDS_FILE_TYPE.getKey().equals(type)) {//贷款记录附件
            sb.append(configKeyConstant.loanRecordImageCatalog);
        } else if (CopyFileTypeEnum.OIL_FILLED_ATTACHMENT_FILE_TYPE.getKey().equals(type)) {
            sb.append(configKeyConstant.oilFilledImageCatalog);
        } else if (CopyFileTypeEnum.VEHICLE_SETTLEMENT_ATTACHMENT.getKey().equals(type)) {
            sb.append(configKeyConstant.vehicleSettlementAttachmentCatalog);
        } else if (CopyFileTypeEnum.INSURANCE_COSTS_ATTACHMENT_FILE_TYPE.getKey().equals(type)) {
            sb.append(configKeyConstant.insuranceCostsCatalog);
            sb.append(configKeyConstant.customerCompanyImageCatalog);
        } else if (CopyFileTypeEnum.DRIVER_SAFE_PROMISE_FILE.getKey().equals(type)) {//承诺书
            sb.append(configKeyConstant.safePromiseCatalog);
        } else if (CopyFileTypeEnum.DRIVER_SAFE_MEETING_FILE.getKey().equals(type)) {//安全例会
            sb.append(configKeyConstant.safeMeetingCatalog);
        } else if (CopyFileTypeEnum.DRIVER_SAFE_CHECK_FILE.getKey().equals(type)) {//安全车辆检查
            sb.append(configKeyConstant.safeCheckCatalog);
        } else if (CopyFileTypeEnum.DRIVER_SAFE_CHECK_REFORM_FILE.getKey().equals(type)
                || CopyFileTypeEnum.DRIVER_SAFE_CHECK_RESULT_FILE.getKey().equals(type)) {//安全车辆检查-整改
            sb.append(configKeyConstant.safeCheckReformCatalog);
        } else if (CopyFileTypeEnum.EXT_VEHICLE_SETTLEMENT_ATTACHMENT_FILE.getKey().equals(type)) {
            sb.append(configKeyConstant.extVehicleSettlementCatalog);
        } else if (CopyFileTypeEnum.SAFETY_GROUP_MEETING_ATTACHMENT_FILE.getKey().equals(type)) {
            sb.append(configKeyConstant.safetyGroupMeetingCatalog);
        } else if (CopyFileTypeEnum.APPLET_DRIVER_SETTLE_IMAGE.getKey().equals(type)) {//小程序司机确认对账图片
            sb.append(configKeyConstant.appletDriverSettleCatalog);
        } else if (CopyFileTypeEnum.VEHICLE_OUTAGE.getKey().equals(type)) {//小程序司机确认对账图片
            sb.append(configKeyConstant.vehicleOutageCatalog);
        } else if (CopyFileTypeEnum.DEMAND_ORDER_OBJECTION_SINOPEC.getKey().equals(type)) {//中石化需求单异常审核图片
            sb.append(configKeyConstant.demandOrderObjectionCatalog);
        } else if (CopyFileTypeEnum.DRIVER_COST_APPLY.getKey().equals(type)) {//司机费用申请凭证
            sb.append(configKeyConstant.driverCostApply);
        } else if (CopyFileTypeEnum.CARRIER_ORDER_OTHER_FEE.getKey().equals(type)) {//临时费用单据
            sb.append(configKeyConstant.carrierOrderOtherFeeCatalog);
        } else if (CopyFileTypeEnum.RENEWABLE_ORDER_SITE_IMAGE.getKey().equals(type)) {// 新生订单现场图片
            sb.append(configKeyConstant.renewableOrderScenePicture);
        } else if (CopyFileTypeEnum.RENEWABLE_ORDER_CONFIRM_TICKET.getKey().equals(type)) {// 新生订单确认票据
            sb.append(configKeyConstant.renewableOrderConfirmPicture);
        } else if (CopyFileTypeEnum.DRIVER_APPOINT_SITE_IMAGE.getKey().equals(type)) {// 司机新生下单预约记录现场图片
            sb.append(configKeyConstant.driverAppointScenePicture);
        } else if (CopyFileTypeEnum.DRIVER_APPOINT_CONFIRM_TICKET.getKey().equals(type)) {// 司机新生下单预约记录确认票据
            sb.append(configKeyConstant.driverAppointConfirmPicture);
        } else if (CopyFileTypeEnum.CARRIER_ORDER_SETTLE_STATEMENT_ARCHIVE_TICKET.getKey().equals(type)) {// 对账单归档单据
            sb.append(configKeyConstant.settleStatementArchivePic);
        } else if (CopyFileTypeEnum.ATTENDANCE_DUTY_PUNCH.getKey().equals(type)) {// 小程序考勤打卡图片
            sb.append(configKeyConstant.attendanceDutyPunchPicture);
        } else if (CopyFileTypeEnum.RESERVE_APPLY_REMIT_TICKET.getKey().equals(type)) {// 备用金申请确认打款票据
            sb.append(configKeyConstant.reserveApplyRemitPicture);
        } else if (CopyFileTypeEnum.COMPANY_ACCOUNT_BANK_CARD_PIC.getKey().equals(type)) {// 公司账户银行卡图片
            sb.append(configKeyConstant.companyAccountBankCardPic);
        } else if (CopyFileTypeEnum.DRIVER_ACCOUNT_BANK_CARD_PIC.getKey().equals(type)) {// 司机账户银行卡图片
            sb.append(configKeyConstant.driverAccountBankCardPic);
        } else if (CopyFileTypeEnum.DRIVER_REACH_ATTACHMENT.getKey().equals(type)) {// 司机触达附件
            sb.append(configKeyConstant.driverReachAttachment);
        } else if (CopyFileTypeEnum.WORK_ORDER_DRIVER_ARRIVE_SCENE.getKey().equals(type)) {// 工单上报异常司机到达现场图片
            sb.append(configKeyConstant.workOrderDriverArriveScenePicture);
        } else if (CopyFileTypeEnum.T_ARCHIVED_FILE.getKey().equals(type)) {// 归档文件
            sb.append(configKeyConstant.archivedFile);
        } else if (CopyFileTypeEnum.INVOICE_IMAGE.getKey().equals(type)) {//发票图片
            sb.append(configKeyConstant.invoiceImage);
        } else if (CopyFileTypeEnum.ROUTE_ENQUIRY_ATTACHMENT.getKey().equals(type)) {//路线询价单附件
            sb.append(configKeyConstant.routeEnquiryAttachment);
        } else if (CopyFileTypeEnum.INVOICING_MANAGEMENT_ARCHIVED_FILE.getKey().equals(type)) {//发票管理归档文件
            sb.append(configKeyConstant.invoicingManagementArchiveFile);
        } else if (CopyFileTypeEnum.CARRIER_ORDER_CODE_FILE.getKey().equals(type)) {//发票管理归档文件
            sb.append(configKeyConstant.carrierOrderCodeFile);
        }

        String fileName = imgPath.substring(imgPath.lastIndexOf("/"));
        if (StringUtils.isNotBlank(code)) {
            sb.append(code).append(fileName);
        } else {//不要出现2个斜杆（OSS会创建一个空文件夹）
            if (sb.substring(sb.length() - CommonConstant.INTEGER_ONE).equals("/")) {
                fileName = fileName.substring(CommonConstant.INTEGER_ONE);
            }
            sb.append(fileName);
        }
        //如果拷贝后正式路径与入参路径相同，则直接返回
        String path = sb.toString();
        if (path.equals(imgPath)) {
            return imgPath;
        }

        FileCopyRequestModel fileCopyRequestModel = new FileCopyRequestModel();
        fileCopyRequestModel.setFileSrcPath(configKeyConstant.tempImageUploadCatalog + imgPath);
        fileCopyRequestModel.setFileTargetPath(configKeyConstant.imageUploadCatalog + path);
        fileCopyRequestModel.setSource(configKeyConstant.imageSource);
        fileCopyRequestModel.setOperationName(code);
        if (waterMarkModel != null && StringUtils.isNotBlank(waterMarkModel.getWaterMark())) {
            fileCopyRequestModel.setWaterMark(waterMarkModel.getWaterMark());
            fileCopyRequestModel.setWaterMarkTwo(waterMarkModel.getWaterMarkTwo());
        }
        log.info("开始拷贝文件：原路径-->" + fileCopyRequestModel.getFileSrcPath() + " 正式路径-->" + fileCopyRequestModel.getFileTargetPath());
        basicDataClient.copyFile(fileCopyRequestModel);

        //如果有水印，则拼接前缀
        if (StringUtils.isNotBlank(fileCopyRequestModel.getWaterMark())) {
            path = path.substring(0, path.lastIndexOf("/") + 1) + "wm" + path.substring(path.lastIndexOf("/") + 1);
        }
        return path;
    }

    /**
     * 插入日志
     *
     * @param type            日志类型 <br/>
     * @param objectId        对象id <br/>
     * @param operateContents 操作内容
     */
    public TOperateLogs insertLogs(OperateLogsOperateTypeEnum type, Long objectId, String operateContents) {
        TOperateLogs newLog = new TOperateLogs();
        newLog.setObjectId(objectId);
        newLog.setObjectType(type.getObjectType().getKey());
        newLog.setOperateType(type.getOperateType());
        if (StringUtils.isBlank(operateContents)) {
            newLog.setOperateContents(type.getOperateContents());
        } else {
            newLog.setOperateContents(operateContents);
        }
        newLog.setOperateTime(new Date());
        newLog.setOperateUserName(BaseContextHandler.getUserName());
        this.setBaseEntityAdd(newLog, BaseContextHandler.getUserName());
        return newLog;
    }

    //前台查询车主公司ID
    public Long getLoginUserCompanyCarrierId() {
        return Optional.ofNullable(this.getLoginUserCompanyCarrier())
                .map(TCarrierContact::getCompanyCarrierId)
                .orElse(CommonConstant.LONG_ZERO);
    }

    // 前台查询车主信息
    public TCarrierContact getLoginUserCompanyCarrier() {
        Long carrierContactId = getNotCloseLoginUserCarrierContactId();
        TCarrierContact tCarrierContact = tCarrierContactMapper.selectByPrimaryKeyDecrypt(carrierContactId);
        return Optional.ofNullable(tCarrierContact)
                .orElse(null);
    }

    //前台查询承运商联系人ID
    public Long getNotCloseLoginUserCarrierContactId() {
        return getCarrierContactIdByAccountId(BaseContextHandler.getUserId(), true);
    }

    /**
     * 前台登录-获取登录人车主账号表id
     *
     * @param accountId
     * @param openStatus
     * @return
     */
    public Long getCarrierContactIdByAccountId(Long accountId, boolean openStatus) {
        AccountInfoResponseModel accountInfo = this.getByAccountIdAndUserRole(accountId, AccountUserRoleTypeEnum.CARRIER.getKey());
        if (accountInfo != null && ListUtils.isNotEmpty(accountInfo.getRoles()) && accountInfo.getRoles().size() == 1) {
            AccountRolesModel userInfo = accountInfo.getRoles().get(CommonConstant.INTEGER_ZERO);
            if (openStatus) {
                return CommonConstant.INTEGER_ZERO.equals(userInfo.getIfClose()) ? userInfo.getUserId() : CommonConstant.LONG_ZERO;
            }
            return userInfo.getUserId();
        }
        return CommonConstant.LONG_ZERO;
    }

    /**
     * 获取用户信息
     *
     * @param accountId BaseContextHandler.getUserId() 基础数据表账号ID
     * @param userRole  用户角色
     * @return
     */
    public AccountInfoResponseModel getByAccountIdAndUserRole(Long accountId, Integer userRole) {
        return tCustomerAccountMapper.getAccountInfoBy(null, accountId, userRole);
    }

    /**
     * 小程序登录-获取司机小程序登录的司机表id（司机可以绑定多个车主，但实际司机id是同一个）
     *
     * @return
     */
    public Long getLoginDriverAppletUserId() {
        List<CarrierDriverRelationModel> relationList = getLoginUserDriver();
        if (ListUtils.isNotEmpty(relationList)) {
            return relationList.get(CommonConstant.INTEGER_ZERO).getDriverId();
        }
        return CommonConstant.LONG_ZERO;
    }

    /**
     * 小程序登录-查询当前登陆用户司机车主关系表信息
     *
     * @return
     */
    //司机小程序查询当前登陆用户司机车主关系表信息
    public List<CarrierDriverRelationModel> getLoginUserDriver() {
        List<CarrierDriverRelationModel> relationModelList = new ArrayList<>();
        AccountInfoResponseModel accountInfoResponseModel = this.getByAccountIdAndUserRole(BaseContextHandler.getUserId(), AccountUserRoleTypeEnum.DRIVER_APPLET.getKey());
        if (accountInfoResponseModel != null && ListUtils.isNotEmpty(accountInfoResponseModel.getRoles())) {
            List<Long> companyDriverRelationIdList = new ArrayList<>();
            accountInfoResponseModel.getRoles().stream().forEach(t -> {
                if (!CommonConstant.INTEGER_ONE.equals(t.getIfClose())) {
                    companyDriverRelationIdList.add(t.getUserId());
                }
            });
            if (ListUtils.isNotEmpty(companyDriverRelationIdList)) {
                List<TCarrierDriverRelation> relationList = tCarrierDriverRelationMapper.getByRelationIds(StringUtils.listToString(companyDriverRelationIdList, ','));
                relationModelList = MapperUtils.mapper(relationList, CarrierDriverRelationModel.class);
            }
        }
        return relationModelList;
    }

    //查询云途公司名称
    public String getQiyaCompanyName() {
        return sysConfigBiz.getSysConfig(ConfigKeyEnum.QIYA_COMPANY_NAME)
                .orElse("");
    }

    //查询云途车主ID
    public Long getQiyaCompanyCarrierId() {
        String qiyaCompanyName = getQiyaCompanyName();
        Long companyCarrierId = 0L;
        if (StringUtils.isNotBlank(qiyaCompanyName)) {
            TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.getByName(qiyaCompanyName);
            if (tCompanyCarrier != null) {
                companyCarrierId = tCompanyCarrier.getId();
            }
        }
        return companyCarrierId;
    }

    public Date getLastDayOfMonth(Date date) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.roll(Calendar.DAY_OF_MONTH, -1);
        return calendar.getTime();
    }

    //查询新生公司名称
    public String getYeloLifeCompanyName() {
        return sysConfigBiz.getSysConfig(ConfigKeyEnum.YELO_LIFE_COMPANY_NAME)
                .orElse("");
    }

    //查询乐医公司名称
    public String getLeyiCompanyName() {
        return sysConfigBiz.getSysConfig(ConfigKeyEnum.LEYI_COMPANY_NAME)
                .orElse("");
    }

    //查询默认结算主体
    public String getDefaultPlatformCompanyName() {
        return sysConfigBiz.getSysConfig(ConfigKeyEnum.SH_YELO_TOUR_COMPANY_NAME)
                .orElse("");
    }

    //查询乐医货主ID
    public Long getLeyiCompanyEntrustId() {
        String leyiCompanyName = getLeyiCompanyName();
        Long companyEntrustId = 0L;
        if (StringUtils.isNotBlank(leyiCompanyName)) {
            TCompanyEntrust tCompanyEntrust = tCompanyEntrustMapper.getByName(leyiCompanyName);
            if (tCompanyEntrust != null) {
                companyEntrustId = tCompanyEntrust.getId();
            }
        }
        return companyEntrustId;
    }

    /**
     * 新增操作日志
     *
     * @param objectId 物理主键
     * @param logsEnum 日志操作类型枚举
     * @param remark   备注
     * @param userName 操作人
     * @return
     */
    public TOperateLogs addOperateLogs(Long objectId, OperateLogsOperateTypeEnum logsEnum, String remark, String userName) {
        TOperateLogs tOperateLogs = new TOperateLogs();
        tOperateLogs.setObjectType(logsEnum.getObjectType().getKey());
        tOperateLogs.setObjectId(objectId);
        tOperateLogs.setOperateType(logsEnum.getOperateType());
        tOperateLogs.setOperateContents(logsEnum.getOperateContents());
        tOperateLogs.setOperateUserName(userName);
        tOperateLogs.setOperateTime(new Date());
        tOperateLogs.setRemark(remark);
        this.setBaseEntityAdd(tOperateLogs, userName);
        return tOperateLogs;
    }

    //根据地址查询省市区：调用高德搜索接口查询区code，再查库获取省市区信息
    public Map<String, String> getProvinceCityArea(String str) {
        log.info("搜索地址：" + str);
        Map<String, String> map = new HashMap<>();
        if (StringUtils.isNotBlank(str)) {
            try {
                GetProvinceCityAreaByKeywordsNewResponseModel responseModel = basicDataClient.getProvinceCityAreaByKeywordsNew(str);
                if (responseModel == null) {
                    throw new BizException(CarrierDataExceptionEnum.RECEIVER_ADDRESS_EMPTY);
                }
                map.put(EmailConstant.UNLOAD_PROVINCE_ID, responseModel.getProvinceId().toString());
                map.put(EmailConstant.UNLOAD_PROVINCE_NAME, responseModel.getProvinceName());
                map.put(EmailConstant.UNLOAD_CITY_ID, responseModel.getCityId().toString());
                map.put(EmailConstant.UNLOAD_CITY_NAME, responseModel.getCityName());
                map.put(EmailConstant.UNLOAD_AREA_ID, responseModel.getAreaId().toString());
                map.put(EmailConstant.UNLOAD_AREA_NAME, responseModel.getAreaName());
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
            log.info("根据地址查询省市区信息为：" + map.toString());
        }
        return map;
    }

    //将文本内容包含的图片拷贝替换成正式图片（新增例会/承诺书）
    public String processReplaceTempPicture(String context, CopyFileTypeEnum typeEnum, String code) {
        if (StringUtils.isBlank(context)) {
            return "";
        }
        String tempRegex = configKeyConstant.fileAccessAddressTemp + "[^\\s\"]+";
        Pattern p = Pattern.compile(tempRegex);//获取正则表达式中的分组，每一组小括号为一组
        Matcher m = p.matcher(context);//进行匹配
        Map<String, String> map = new HashMap<>();
        while (m.find()) {
            String tmpUrl = m.group();
            String tempImg = tmpUrl.substring(tmpUrl.lastIndexOf(File.separatorChar));
            map.put(m.group(), copyFileToDirectoryOfType(typeEnum.getKey(), code, this.fileSubSequence(tempImg), null));
        }
        for (Map.Entry<String, String> entry : map.entrySet()) {
            context = context.replace(entry.getKey(), entry.getValue());
        }
        context = context.replace("&nbsp;", "&#160;");
        return closeHTML(context);
    }

    //将文本内未闭合的标签全部闭合
    public static String closeHTML(String str) {
        List arrTags = Arrays.asList("img", "br", "hr");
        for (int i = 0; i < arrTags.size(); i++) {
            for (int j = 0; j < str.length(); ) {
                int tagStart = str.indexOf("<" + arrTags.get(i), j);
                if (tagStart >= 0) {
                    int tagEnd = str.indexOf('>', tagStart);
                    j = tagEnd;
                    String preCloseTag = str.substring(tagEnd - 1, tagEnd);
                    if (!"/".equals(preCloseTag)) {
                        String preStr = str.substring(0, tagEnd);
                        String afterStr = str.substring(tagEnd);
                        str = preStr + "/" + afterStr;
                    }
                } else {
                    break;
                }
            }
        }
        return str;
    }

    //将库里文本内容包含的图片加上正式路径（查询例会/承诺书详情）
    public String addRealPath(String context) {
        String tempRegex = "src=\"" + "[^\\s\"]+";
        Pattern p = Pattern.compile(tempRegex);//获取正则表达式中的分组，每一组小括号为一组
        Matcher m = p.matcher(context);//进行匹配
        Map<String, String> map = new HashMap<>();
        while (m.find()) {
            String tmpUrl = m.group();
            tmpUrl = tmpUrl.substring(5);
            if (!tmpUrl.contains("http")) {
                map.put(tmpUrl, configKeyConstant.fileAccessAddress + basicDataClient.getImageURL(tmpUrl));
            }
        }
        for (Map.Entry<String, String> entry : map.entrySet()) {
            context = context.replace(entry.getKey(), entry.getValue());
        }
        return context;
    }

    //去掉问号及后面内容
    public String fileSubSequence(String filePathFromRequest) {
        if (filePathFromRequest.contains("?")) {
            return ConverterUtils.toString(filePathFromRequest.subSequence(0, filePathFromRequest.lastIndexOf("?")));
        } else {
            return filePathFromRequest;
        }
    }

    //location：经纬度信息，经度在前，纬度在后，经纬度间以“,”分割
    public String getAddressByLonAndLat(String location) {
        String address = "";
        if (StringUtils.isBlank(location)) {
            return address;
        }
        log.info("根据经纬度查询地址入参：" + location);
        try {
            GeoCodeForReGeoRequestModel model = new GeoCodeForReGeoRequestModel();
            model.setLocation(location);
            List<Map<String, Object>> resultList = basicDataClient.geoCodeForReGeo(model);
            if (ListUtils.isEmpty(resultList) || MapUtils.isEmpty(resultList.get(CommonConstant.INTEGER_ZERO))) {
                log.info(CarrierDataExceptionEnum.SEARCH_EMPTY_BY_LON_LAT.getMsg());
                return address;
            }
            Map<String, Object> map = resultList.get(CommonConstant.INTEGER_ZERO);//一个经纬度查询
            Object formattedAddress = map.get("formatted_address");//查询到值返回字符串，未查询到则返回的是空数组
            if (formattedAddress == null || "[]".equals(formattedAddress.toString())) {
                log.info(CarrierDataExceptionEnum.SEARCH_EMPTY_BY_LON_LAT.getMsg());
                return address;
            }
            address = formattedAddress.toString();
        } catch (Exception e) {
            log.warn(e.getMessage());
        }
        log.info("根据经纬度查询地址结果：" + address);
        return address;
    }

    /**
     * 根据地址查询经纬度(单个)
     *
     * @param address
     * @return
     */
    public String getLocationByAddress(String address) {
        GeoCodeForGeoRequestModel requestModel = new GeoCodeForGeoRequestModel();
        requestModel.setAddress(address);
        List<Map<String, Object>> maps = basicDataClient.geoCodeForGeo(requestModel);
        if (ListUtils.isNotEmpty(maps)) {
            Map<String, Object> map = maps.get(CommonConstant.INTEGER_ZERO);
            Object location = map.get("location");
            return Optional.ofNullable(location).orElse("").toString();
        }
        return "";
    }

    /**
     * 根据提卸货经纬度查询里程
     *
     * @param loadLocation   提货经纬度，经度在前，纬度在后，经纬度间以“,”分割
     * @param unloadLocation 卸货经纬度，经度在前，纬度在后，经纬度间以“,”分割
     * @return 公里
     */
    public BigDecimal getMileageByLonAndLat(String loadLocation, String unloadLocation) {
        BigDecimal mileage = BigDecimal.ZERO;
        List<BigDecimal> mileageByLonAndLat =
                getMileageByLonAndLat(loadLocation, unloadLocation, CommonConstant.INTEGER_ONE);
        if(ListUtils.isNotEmpty(mileageByLonAndLat)){
            mileage = mileageByLonAndLat.get(CommonConstant.INTEGER_ZERO);
        }
        return mileage;
    }

    public List<BigDecimal> getThreeMileageByLonAndLat(String loadLocation, String unloadLocation) {
        return getMileageByLonAndLat(loadLocation, unloadLocation, CommonConstant.INTEGER_THREE);
    }

    /**
     * 根据提卸货经纬度查询里程
     *
     * @param loadLocation   提货经纬度，经度在前，纬度在后，经纬度间以“,”分割
     * @param unloadLocation 卸货经纬度，经度在前，纬度在后，经纬度间以“,”分割
     * @return 公里
     */
    private List<BigDecimal> getMileageByLonAndLat(String loadLocation, String unloadLocation, Integer time) {
        List<BigDecimal> mileageList = new LinkedList<>();
        if (StringUtils.isBlank(loadLocation) || StringUtils.isBlank(unloadLocation)) {
            return mileageList;
        }
        log.info("根据经纬度查询里程数入参：" + loadLocation + "，" + unloadLocation);
        try {
            DirectionDrivingRequestModel model = new DirectionDrivingRequestModel();
            model.setOrigin(loadLocation);
            model.setDestination(unloadLocation);
            List<Map<String, Object>> resultList = basicDataClient.directionDriving(model);
            for (int i = 0; i < time; i++) {
                if (ListUtils.isEmpty(resultList) || MapUtils.isEmpty(resultList.get(i))) {
                    log.info(CarrierDataExceptionEnum.SEARCH_MILEAGE_BY_LON_LAT.getMsg());
                    return mileageList;
                }
                Map<String, Object> map = resultList.get(i);//一个经纬度查询
                Object distance = map.get("distance");//查询到值返回字符串，未查询到则返回的是空数组
                if (distance == null || "[]".equals(distance.toString())) {
                    log.info(CarrierDataExceptionEnum.SEARCH_MILEAGE_BY_LON_LAT.getMsg());
                    return mileageList;
                }
                BigDecimal mileage = ConverterUtils.toBigDecimal(distance).divide(CommonConstant.BIG_DECIMAL_ONE_THOUSAND_HUNDRED).setScale(2, BigDecimal.ROUND_HALF_UP);
                mileageList.add(mileage);
            }

        } catch (Exception e) {
            log.warn(e.getMessage());
        }
        log.info("根据经纬度查询里程数结果：" + mileageList);
        return mileageList;
    }

    /**
     * 计算2个日期相隔天数（date2比date1多的天数）
     *
     * @param date1
     * @param date2
     * @return
     */
    public Integer differentDays(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if (year1 != year2) {//不同一年
            int timeDistance = 0;
            for (int i = year1; i < year2; i++) {
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0) {//闰年
                    timeDistance += 366;
                } else {//不是闰年
                    timeDistance += 365;
                }
            }
            return timeDistance + (day2 - day1);
        } else {//同一年
            return day2 - day1;
        }
    }


    /**
     * 清除指定账号的token
     *
     * @param userAccount
     */
    public void clearToken(String userAccount) {
        //异步操作
        AsyncProcessQueue.execute(() -> {
            //web
            TokenUtil.logoutByUserCode(PlatformProdEnums.LOGISTICS_TMS_WEB.getName() + "_" + userAccount);
            //小程序
            TokenUtil.logoutByUserCode(PlatformProdEnums.LOGISTICS_TMS_DRIVER_APPLET.getName() + "_" + userAccount);
        });
    }

    /**
     * 校验车主是否存在
     *
     * @param
     */
    public void checkAppCarrierPermission() {
        Long loginUserCarrierContactId = getNotCloseLoginUserCarrierContactId();
        if (loginUserCarrierContactId != null && loginUserCarrierContactId > CommonConstant.INTEGER_ZERO) {
            TCarrierContact tCarrierContact = tCarrierContactMapper.selectByPrimaryKeyDecrypt(loginUserCarrierContactId);
            if (tCarrierContact == null || IfValidEnum.INVALID.getKey().equals(tCarrierContact.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
            }
        }
    }

    /**
     * 获取分布式锁
     *
     * @param redisKey
     */
    public String getDistributedLock(String redisKey) {
        String uuid = UUIDGenerateUtil.generateUUID();
        Long startTime = System.currentTimeMillis();
        while (!redisLockUtils.tryLock(redisKey, uuid, 10)) {
            try {
                Thread.sleep(200);
                Long endTime = System.currentTimeMillis();
                if (endTime - startTime > 10000) {
                    throw new BizException(CarrierDataExceptionEnum.HYSTRIX_ERROR_MESSAGE);
                }
            } catch (Exception e) {
                log.info(e.getMessage());
            }
        }
        return uuid;
    }

    /**
     * 释放锁
     *
     * @param redisKey
     */
    public void removeDistributedLock(String redisKey, String uuid) {
        redisLockUtils.releaseLock(redisKey, uuid);
    }


    /**
     * 凭证对象构建
     *
     * @param objectId       外键id
     * @param objectTypeEnum 文件大类
     * @param fileTypeEnum   文件小类
     * @param filePath       图片正式路径
     * @return 凭证对象
     */
    public TCertificationPictures getCertificationPictures(Long objectId, CertificationPicturesObjectTypeEnum objectTypeEnum, CertificationPicturesFileTypeEnum fileTypeEnum, String filePath) {
        TCertificationPictures certificationPicturesFace = new TCertificationPictures();
        certificationPicturesFace.setObjectId(objectId);
        certificationPicturesFace.setObjectType(objectTypeEnum.getObjectType());
        certificationPicturesFace.setFileType(fileTypeEnum.getFileType());
        certificationPicturesFace.setFileTypeName(fileTypeEnum.getFileName());
        certificationPicturesFace.setFileName(fileTypeEnum.getFileName());
        certificationPicturesFace.setFilePath(filePath);
        certificationPicturesFace.setUploadUserName(BaseContextHandler.getUserName());
        certificationPicturesFace.setUploadTime(new Date());
        int index = filePath.lastIndexOf('.');
        if (index >= 0) {
            certificationPicturesFace.setSuffix(filePath.substring(index));
        }
        setBaseEntityAdd(certificationPicturesFace, BaseContextHandler.getUserName());
        return certificationPicturesFace;
    }

    /**
     * 批量构建图片对象
     *
     * @param objectId       外键id
     * @param objectTypeEnum 文件大类
     * @param fileTypeEnum   文件小类
     * @param filePaths      图片正式路径
     * @return 图片对象集合
     */
    public List<TCertificationPictures> getCertificationPictures(Long objectId,
                                                                          CertificationPicturesObjectTypeEnum objectTypeEnum,
                                                                          CertificationPicturesFileTypeEnum fileTypeEnum,
                                                                          List<String> filePaths) {
        return getCertificationPictures(objectId, objectTypeEnum, fileTypeEnum, filePaths, Function.identity());
    }

    /**
     * 批量构建图片对象
     *
     * @param objectId       外键id
     * @param objectTypeEnum 文件大类
     * @param fileTypeEnum   文件小类
     * @param filePaths      图片正式路径
     * @return 图片对象集合
     */
    public List<TCertificationPictures> getCertificationPictures(Long objectId,
                                                                 CertificationPicturesObjectTypeEnum objectTypeEnum,
                                                                 CertificationPicturesFileTypeEnum fileTypeEnum,
                                                                 List<String> filePaths,
                                                                 Function<String, String> copyFileToDirectoryOfTypeFunction) {
        return filePaths.stream()
                .map(filePath -> getCertificationPictures(objectId, objectTypeEnum, fileTypeEnum, copyFileToDirectoryOfTypeFunction.apply(filePath)))
                .collect(Collectors.toList());
    }

    /**
     * 异步更新智慧运营回收配置关联的需求单信息
     *
     * @param list
     */
    public void synRecyclePublishUpdateDemand(List<RecyclePublishUpdateDemandRequestModel> list) {
        if (ListUtils.isEmpty(list)) {
            return;
        }
        log.info("异步【更新智慧运营回收配置关联的需求单信息】入参：" + list.toString());
        AsyncProcessQueue.execute(() -> bigDataClient.recyclePublishUpdateDemand(list));
    }

    /**
     * 拼接二维码内容
     * @param linkPrefix 链接前缀
     * @param qrCodeParamsMap 业务参数
     * @return 跳转路径拼接业务参数的字符串
     */
    public String getQrCodeContent(String linkPrefix,
                                   Map<String, Object> qrCodeParamsMap){
        return CommonConstant.linkParamsJoinFunction.apply(linkPrefix, qrCodeParamsMap);
    }

    /**
     * 创建二维码
     *
     * @param linkPrefix 链接前缀
     * @param qrCodeParamsMap  业务参数
     * @return 二维码图片地址
     */
    public CreateQrCodeModel createQrCode(String linkPrefix,
                                          Map<String, Object> qrCodeParamsMap) {

        CreateQrCodeModel createQrCodeModel = new CreateQrCodeModel();
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            String qrCodeContent = getQrCodeContent(linkPrefix, qrCodeParamsMap);
            if (StringUtils.isNotBlank(qrCodeContent)) {
                //生成二维码
                BufferedImage bufferedImage = QRCodeUtils.createImage(qrCodeContent, null, false);
                ImageIO.write(bufferedImage, CommonConstant.PIC_JPG, bos);
                createQrCodeModel.setFileByte(bos.toByteArray());
            }
        } catch (Exception e) {
            log.error("生成二维码失败", e);
        }
        return createQrCodeModel;
    }

    /**
     * 解析二维码参数 (链接参数、JSON参数)
     *
     * @param filePath 二维码图片链接
     * @return MultiValueMap<key, value>
     */
    public MultiValueMap<String, String> parseQrCodeParametersByFilePath(String filePath) throws Exception {
        // 获取二维码内容
        String content = basicDataClient.qrCodeIdentify(filePath);
        return parseQrCodeParameters(content);
    }

    /**
     * 解析二维码参数 (链接参数、JSON参数)
     *
     * @param qrCodeContent 二维码内容
     * @return MultiValueMap<key, value>
     */
    public MultiValueMap<String, String> parseQrCodeParameters(String qrCodeContent) throws Exception {
        if (StringUtils.isBlank(qrCodeContent)) {
            return new LinkedMultiValueMap<>();
        }
        try {
            // 校验是否是链接
            URL url = new URL(qrCodeContent);
            return UriComponentsBuilder.fromUri(url.toURI()).build().getQueryParams();
        } catch (MalformedURLException e) {
            // 不是链接转为JSON格式
            Map<String, String> jsonMap = new Gson().fromJson(qrCodeContent, (Type) Map.class);
            MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
            for (Map.Entry<String, String> entry : jsonMap.entrySet()) {
                multiValueMap.add(entry.getKey(), entry.getValue());
            }
            return multiValueMap;
        }
    }

    /**
     * 新增消息到队列
     * @param delayMsgType 消息类型
     * @param object 消息内容
     * @param time 超时时间
     * @param timeUnit 时间单位
     */
    public void addDelayQueue(String delayMsgType, Object object ,long time, TimeUnit timeUnit) {
        rDelayQueue.add(delayMsgType, object, time, timeUnit);
    }

    /**
     * 删除消息
     * @param delayMsgType 消息类型
     * @return object 消息内容
     */
    public boolean removeDelayQueue(String delayMsgType,Object object) {
        return rDelayQueue.remove(delayMsgType, object);
    }

    /**
     * 米
     * @param lat1
     * @param lon1
     * @param lat2
     * @param lon2
     * @return
     */
    public  double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        double phi1 = Math.toRadians(lat1);
        double phi2 = Math.toRadians(lat2);
        double deltaPhi = Math.toRadians(lat2 - lat1);
        double deltaLambda = Math.toRadians(lon2 - lon1);

        double a = Math.sin(deltaPhi / 2) * Math.sin(deltaPhi / 2) +
                Math.cos(phi1) * Math.cos(phi2) *
                        Math.sin(deltaLambda / 2) * Math.sin(deltaLambda / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return EARTH_RADIUS * c;
    }
}
