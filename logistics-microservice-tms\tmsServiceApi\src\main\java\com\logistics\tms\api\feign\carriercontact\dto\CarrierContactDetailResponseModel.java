package com.logistics.tms.api.feign.carriercontact.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class CarrierContactDetailResponseModel {

    @ApiModelProperty("车主账号id")
    private Long carrierContactId;

    @ApiModelProperty("车主类型")
    private Integer type;

    @ApiModelProperty("车主id")
    private Long companyCarrierId;
    @ApiModelProperty("车主名称")
    private String companyCarrierName;

    @ApiModelProperty("姓名")
    private String contactName;

    @ApiModelProperty("手机号")
    private String contactPhone;

    @ApiModelProperty("身份证号")
    private String identityNumber;

}
