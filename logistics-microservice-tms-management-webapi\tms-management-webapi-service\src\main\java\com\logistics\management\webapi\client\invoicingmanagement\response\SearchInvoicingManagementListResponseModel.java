package com.logistics.management.webapi.client.invoicingmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2024/3/19 16:28
 */
@Data
public class SearchInvoicingManagementListResponseModel {
    @ApiModelProperty("发票管理id")
    private Long invoicingId;

    @ApiModelProperty("业务名称")
    private String businessName;

    @ApiModelProperty("开票月份")
    private String invoicingMonth;

    @ApiModelProperty(value = "车主公司类型：1 公司，2 个人")
    private Integer companyCarrierType;
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName;
    @ApiModelProperty("车主联系人")
    private String carrierContactName;
    @ApiModelProperty("车主联系人电话")
    private String carrierContactMobile;

    @ApiModelProperty("发票金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty("对账金额")
    private BigDecimal reconciliationFee;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("操作人")
    private String lastModifiedBy;

    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;
}
