package com.logistics.tms.biz.reservebalance;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.controller.reservebalance.request.DriverReserveBalanceListRequestModel;
import com.logistics.tms.controller.reservebalance.request.ReserveBalanceDetailRequestModel;
import com.logistics.tms.controller.reservebalance.response.DriverReserveBalanceListResponseModel;
import com.logistics.tms.controller.reservebalance.response.ReserveBalanceDetailResponseModel;
import com.logistics.tms.controller.reservebalance.response.ReserveBalanceInfoResponseModel;
import com.logistics.tms.controller.reservebalance.response.SearchDriverReserveBalanceResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.ReserveBalanceRunningTypeEnum;
import com.logistics.tms.base.utils.ExceptionUtils;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.reserveapply.model.ReserveApplyBalanceDeductionModel;
import com.logistics.tms.biz.reservebalance.model.*;
import com.logistics.tms.biz.reservebalancerunningrecord.ReserveBalanceRunningRecordBiz;
import com.logistics.tms.entity.TReserveBalance;
import com.logistics.tms.mapper.TDriverCostApplyMapper;
import com.logistics.tms.mapper.TReserveApplyMapper;
import com.logistics.tms.mapper.TReserveBalanceMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.groovy.util.Maps;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ReserveBalanceBiz {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private ReserveBalanceRunningRecordBiz reserveBalanceRunningRecordBiz;
    @Resource
    private TReserveApplyMapper reserveApplyMapper;
    @Resource
    private TReserveBalanceMapper reserveBalanceMapper;
    @Resource
    private TDriverCostApplyMapper driverCostApplyMapper;

    /**
     * 备用金余额台账搜索
     *
     * @param requestModel
     * @return SearchDriverReserveBalanceResponseModel
     */
    public SearchDriverReserveBalanceResponseModel searchList(DriverReserveBalanceListRequestModel requestModel) {

        // 计算合计
        StatisticsReserveModel statisticsReserve = reserveBalanceMapper.statisticsReserve();

        // 查询列表
        requestModel.enablePaging();
        List<DriverReserveBalanceListResponseModel> driverReserveBalanceList = reserveBalanceMapper.selectSearchList(requestModel);

        // 填充待冲销金额
        if (ListUtils.isNotEmpty(driverReserveBalanceList)) {
            awaitVerificationAmountDataPopulate(driverReserveBalanceList);
        }

        // 填充响应数据
        PageInfo pageInfo = new PageInfo<>(driverReserveBalanceList);
        SearchDriverReserveBalanceResponseModel responseModel = MapperUtils.mapper(statisticsReserve, SearchDriverReserveBalanceResponseModel.class);
        responseModel.setDriverReserveBalancePageInfo(pageInfo);
        return responseModel;
    }

    // 填充待冲销金额
    private void awaitVerificationAmountDataPopulate(List<DriverReserveBalanceListResponseModel> reserveBalanceList) {
        List<Long> driverIds = reserveBalanceList
                .stream()
                .map(DriverReserveBalanceListResponseModel::getDriverId)
                .collect(Collectors.toList());
        // 查询待冲销金额
        Map<Long, DriverBalanceDetailModel> driverAwaitVerificationAmountMap = getAwaitVerificationAmountByDriverIds(driverIds);
        reserveBalanceList.forEach(f -> {
            BigDecimal awaitVerificationAmount = Optional.ofNullable(driverAwaitVerificationAmountMap.get(f.getDriverId()))
                    .map(DriverBalanceDetailModel::getAwaitVerificationAmount).orElse(BigDecimal.ZERO);
            f.setAwaitVerificationAmount(awaitVerificationAmount);
        });
    }

    // 查询司机核销中的金额
    private Map<Long, DriverBalanceDetailModel> getAwaitVerificationAmountByDriverIds(Collection<Long> driverIds) {
        return driverCostApplyMapper.statisticsAwaitVerificationAmountByDriver(driverIds);
    }

    /**
     * 导出备用金余额台账
     *
     * @param requestModel
     * @return
     */
    public List<DriverReserveBalanceListResponseModel> searchListExport(DriverReserveBalanceListRequestModel requestModel) {

        // 查询列表
        List<DriverReserveBalanceListResponseModel> driverReserveBalanceList = reserveBalanceMapper.selectSearchList(requestModel);
        // 填充待冲销金额
        if (ListUtils.isNotEmpty(driverReserveBalanceList)) {
            awaitVerificationAmountDataPopulate(driverReserveBalanceList);
        }
        return driverReserveBalanceList;
    }

    /**
     * 创建司机余额
     *
     * @param driverId 司机Id
     * @return balanceId 余额Id
     */
    public Long createDriverBalance(Long driverId) {

        // 查询余额信息是否已创建
        TReserveBalance reserveBalance = reserveBalanceMapper.selectOneByDriverId(driverId);
        if (Objects.nonNull(reserveBalance)) {
            return reserveBalance.getId();
        }

        TReserveBalance createBalance = new TReserveBalance();
        createBalance.setDriverId(driverId);
        commonBiz.setBaseEntityAdd(createBalance, BaseContextHandler.getUserName());
        reserveBalanceMapper.createBalance(createBalance);
        return createBalance.getId();
    }

    /**
     * 查询司机余额信息
     *
     * @param driverId
     * @return TReserveBalance
     */
    public TReserveBalance getReserveBalanceByDriverId(Long driverId) {
        return reserveBalanceMapper.selectOneByDriverId(driverId);
    }

    /**
     * 查询司机余额明细
     *
     * @param driverId
     * @return DriverReserveBalanceListResponseModel
     */
    public DriverBalanceDetailModel getReserveBalanceDetailByDriverId(Long driverId) {

        // 查询司机账户信息
        TReserveBalance reserveBalance = getReserveBalanceByDriverId(driverId);

        DriverBalanceDetailModel driverBalanceDetail = Optional.ofNullable(reserveBalance)
                .map(s -> {
                    return new DriverBalanceDetailModel()
                            .setReserveBalanceId(s.getId())
                            .setVerificationAmount(s.getVerificationAmount())
                            .setBalanceAmount(s.getBalanceAmount());
                }).orElse(new DriverBalanceDetailModel());

        // 查询待核销金额
        BigDecimal awaitVerificationAmount = BigDecimal.ZERO;
        List<Long> driverIds = Collections.singletonList(driverId);
        Map<Long, DriverBalanceDetailModel> awaitVerificationAmountMap = getAwaitVerificationAmountByDriverIds(driverIds);
        DriverBalanceDetailModel awaitVerificationModel = awaitVerificationAmountMap.get(driverId);
        if (awaitVerificationModel != null) {
            awaitVerificationAmount = awaitVerificationModel.getAwaitVerificationAmount();
        }
        driverBalanceDetail.setAwaitVerificationAmount(awaitVerificationAmount);

        return driverBalanceDetail;
    }

    /**
     * 备用金管理明细
     *
     * @return ReserveBalanceInfoResponseModel
     */
    public ReserveBalanceInfoResponseModel reserveBalanceInfo() {
        Long loginUserId = getLoginDriverAppletUserId();
        return Optional.ofNullable(getReserveBalanceDetailByDriverId(loginUserId))
                .map(b -> {
                    ReserveBalanceInfoResponseModel reserveBalanceInfo = new ReserveBalanceInfoResponseModel();
                    reserveBalanceInfo.setBalance(b.getBalanceAmount());
                    reserveBalanceInfo.setWaitingWriteOffAmount(b.getAwaitVerificationAmount());
                    return reserveBalanceInfo;
                })
                .orElse(new ReserveBalanceInfoResponseModel());
    }

    /**
     * 备用金余额台账明细查询
     *
     * @param requestModel
     * @return
     */
    public PageInfo<ReserveBalanceDetailResponseModel> reserveBalanceDetail(ReserveBalanceDetailRequestModel requestModel) {
        // 查询明细
        return reserveBalanceRunningRecordBiz.reserveBalanceDetail(requestModel);
    }

    /**
     * 备用金明细导出列表
     *
     * @param requestModel
     * @return ReserveBalanceDetailExportResponseModel
     */
    public Map<String, List<ReserveBalanceDetailResponseModel>> reserveBalanceDetailExport(ReserveBalanceDetailRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        // 查询明细列表
        PageInfo<ReserveBalanceDetailResponseModel> pageinfo = reserveBalanceDetail(requestModel);
        // 查询司机姓名
        String driverName = reserveBalanceMapper.selectDriverNameById(requestModel.getReserveBalanceId());
        return Maps.of(driverName, pageinfo.getList());
    }

    // 获取员工id
    private Long getLoginDriverAppletUserId() {
        // 获取员工id
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        if (CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.DATA_ERROR_AND_RE_LOGIN);
        }
        return loginDriverAppletUserId;
    }


    /**
     * 查询司机备用金余额
     * @param driverIds 司机id
     */
    public List<TReserveBalance> getBalanceByDriverIds(List<Long> driverIds){
        return reserveBalanceMapper.selectBalanceByDriverIds(driverIds);
    }

    /**
     * 余额变更处理逻辑（注意锁释放时机）
     *
     * @param boModel 余额变更Model
     */
    @Transactional
    public void balanceChangeHandler(ReserveBalanceChangeHandleBoModel boModel) {

        String lockKey = String.format(CommonConstant.RESERVE_BALANCE_LOCK_KEY, boModel.getDriverId());
        RLock lock = null;
        boolean hasLock = false;
        try {
            lock = redissonClient.getLock(lockKey);
            hasLock = lock.tryLock(CommonConstant.LONG_THREE, TimeUnit.SECONDS);
            if (hasLock) {
                TReserveBalance balance = Optional.ofNullable(this.getReserveBalanceByDriverId(boModel.getDriverId()))
                        .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.DRIVER_ACCOUNT_NOT_EXIST));
                // 充值抵扣备用金申请余额
                if (boModel.getTypeEnum().isRecharge()) {
                    // 是否抵扣备用
                    if (balance.getBalanceAmount().signum() < CommonConstant.INTEGER_ZERO) {
                        this.reserveApplyRechargeDeductionHandel(boModel.getReserveList(), balance.getBalanceAmount());
                        this.batchReserveApplyBalanceDeductionByCostApply(boModel.getReserveList());
                    }
                }
                // 冲销
                else {
                    this.deductionCheck(boModel);
                    this.batchReserveApplyBalanceDeductionByCostApply(boModel.getReserveList());
                }
                // 更新余额 记录余额明细
                this.updateBalance(balance.getId(), boModel.getTypeEnum(), boModel.getAmount());
                return;
            }
        }
        // 业务异常
        catch (BizException biz) {
            throw biz;
        }
        // 系统异常
        catch (Exception e) {
            log.error("execute cost apply deduction error:{}", e.getMessage());
            throw new BizException(CarrierDataExceptionEnum.HYSTRIX_ERROR_MESSAGE);
        } finally {
            if (hasLock) {
                lock.unlock();
            }
        }
        // 超时未获取到锁
        throw new BizException(CarrierDataExceptionEnum.HYSTRIX_ERROR_MESSAGE.getCode(), CommonConstant.OPERATION_EXECUTING_MESSAGE);
    }

    /**
     * 根据Id更新余额
     *
     * @param balanceId  余额id
     * @param updateType 更新类型 {@link ReserveBalanceRunningTypeEnum}
     * @param amount     变动金额
     */
    private void updateBalance(Long balanceId, ReserveBalanceRunningTypeEnum updateType, BigDecimal amount) {
        // 更新余额
        UpdateBalanceModel updateBalance = new UpdateBalanceModel()
                .setRecharge(updateType.isRecharge())
                .setAmount(amount);
        updateBalance.setId(balanceId);
        commonBiz.setBaseEntityModify(updateBalance, BaseContextHandler.getUserName());
        reserveBalanceMapper.updateBalanceById(updateBalance);
        // 记录明细
        reserveBalanceRunningRecordBiz.addBalanceRunningRecord(balanceId, updateType.getKey(), amount);
    }

    // 费用申请扣减备用金余额
    private void batchReserveApplyBalanceDeductionByCostApply(List<ReserveApplyBalanceChangeBoModel> reserveList) {
        if (ListUtils.isEmpty(reserveList)) {
            return;
        }
        String userName = BaseContextHandler.getUserName();
        // 更新备用金余额
        List<ReserveApplyBalanceDeductionModel> updateModelList = reserveList
                .stream()
                .map(s -> {
                    ReserveApplyBalanceDeductionModel balanceDeduction = new ReserveApplyBalanceDeductionModel();
                    balanceDeduction.setReserveApplyCode(s.getReserveCode());
                    balanceDeduction.setBalanceAmount(s.getBalance());
                    balanceDeduction.setUpdatedBalance(s.getWriteOffAmount());
                    commonBiz.setBaseEntityModify(balanceDeduction, userName);
                    return balanceDeduction;
                })
                .collect(Collectors.toList());
        reserveApplyMapper.batchUpdateReserveApplyBalanceDeduction(updateModelList);
    }

    // 备用金充值抵扣处理逻辑
    private void reserveApplyRechargeDeductionHandel(List<ReserveApplyBalanceChangeBoModel> reserveList, BigDecimal balanceAmount) {
        // 扣减备用金余额
        balanceAmount = balanceAmount.abs();
        for (ReserveApplyBalanceChangeBoModel reserve : reserveList) {
            // 计算核销金额
            if (reserve.getBalance().compareTo(balanceAmount) >= CommonConstant.INTEGER_ZERO) {
                reserve.setWriteOffAmount(balanceAmount);
                break;
            }
            reserve.setWriteOffAmount(reserve.getBalance());
            balanceAmount = balanceAmount.subtract(reserve.getBalance());
        }
    }

    // 扣款备用金参数校验
    private void deductionCheck(ReserveBalanceChangeHandleBoModel boModel) {

        // 入参数验
        ExceptionUtils.isTure(ListUtils.isEmpty(boModel.getReserveList()))
                .throwMessage(CarrierDataExceptionEnum.RESERVE_BALANCE_INSUFFICIENT);

        // 金额校验
        BigDecimal reserveCostAmount = boModel.getReserveList()
                .stream()
                .map(ReserveApplyBalanceChangeBoModel::getWriteOffAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        ExceptionUtils.isTure(!CommonConstant.INTEGER_ZERO.equals(reserveCostAmount.compareTo(boModel.getAmount())))
                .throwMessage(CarrierDataExceptionEnum.RESERVE_BALANCE_INSUFFICIENT);

        // 校验备用金余额是否足够
        Map<String, BigDecimal> reserveApplyCodeBalanceMap = boModel.getReserveList()
                .stream()
                .collect(Collectors.toMap(ReserveApplyBalanceChangeBoModel::getReserveCode, ReserveApplyBalanceChangeBoModel::getBalance));
        // 校验备用金余额是否足够
        long size = reserveApplyMapper.selectReserveApplyUsedInCostDeductions(boModel.getDriverId())
                .stream()
                .filter(f -> {
                    boolean isAdvance = ReserveBalanceRunningTypeEnum.ADVANCE_TYPE.equals(boModel.getTypeEnum());
                    return Optional.ofNullable(reserveApplyCodeBalanceMap.get(f.getReserveApplyCode()))
                            .map(balance -> f.getBalanceAmount().compareTo(balance) >= CommonConstant.INTEGER_ZERO)
                            .orElse(isAdvance);
                })
                .count();
        ExceptionUtils.isTure((int) size != reserveApplyCodeBalanceMap.size())
                .throwMessage(CarrierDataExceptionEnum.REVERSE_BALANCE_AMOUNT_CHANGES);
    }
}
