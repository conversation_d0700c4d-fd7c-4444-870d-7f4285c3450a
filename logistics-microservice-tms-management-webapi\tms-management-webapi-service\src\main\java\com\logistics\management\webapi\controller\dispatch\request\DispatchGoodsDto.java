package com.logistics.management.webapi.controller.dispatch.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;


@Data
public class DispatchGoodsDto {

    /**
     * 需求单货物id
     */
    @ApiModelProperty(value = "需求单货物id", required = true)
    @NotBlank(message = "需求单货物id不能为空")
    private String demandOrderGoodsId;

    /**
     * 需求单id
     */
    @ApiModelProperty(value = "需求单id", required = true)
    @NotBlank(message = "需求单id不能为空")
    private String demandOrderId;

    /**
     * 预提数量
     */
    @ApiModelProperty(value = "预提数量", required = true)
    @NotBlank(message = "预提数量不能为空")
    @Pattern(regexp = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,3})$)",message = "请维护正确的预提数量")
    private String loadAmount;
}
