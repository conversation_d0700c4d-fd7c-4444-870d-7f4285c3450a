package com.logistics.tms.api.feign.safegroupmeeting.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SafeGroupMeetingResponseModel {

    @ApiModelProperty(value = "会议id")
    private Long safetyGroupMeetingId;

    @ApiModelProperty(value = "会议标题")
    private String meetingTitle;

    @ApiModelProperty(value = "会议季度")
    private Integer meetingSeason;

    @ApiModelProperty(value = "参与人数")
    private Integer joinInPersonCount;

}
