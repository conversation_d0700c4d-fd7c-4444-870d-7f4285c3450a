package com.logistics.tms.controller.vehiclesettlement;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.vehiclesettlement.VehicleSettlementAppletBiz;
import com.logistics.tms.controller.vehiclesettlement.request.DriverReconciliationConfirmRequestModel;
import com.logistics.tms.controller.vehiclesettlement.request.SearchDriverReconciliationListRequestModel;
import com.logistics.tms.controller.vehiclesettlement.request.VehicleSettlementIdRequestModel;
import com.logistics.tms.controller.vehiclesettlement.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/2/21 14:23
 */
@RestController
@Api(value = "对账管理")
@RequestMapping(value = "/service/vehicleSettlement")
public class VehicleSettlementAppletController {

    @Resource
    private VehicleSettlementAppletBiz vehicleSettlementAppletBiz;

    /**
     * 司机账单列表
     * @param requestModel
     * @return
     */
    @ApiOperation(("司机账单列表"))
    @PostMapping(value = "/searchDriverReconciliationList")
    public Result<PageInfo<SearchDriverReconciliationListResponseModel>> searchDriverReconciliationList(@RequestBody SearchDriverReconciliationListRequestModel requestModel) {
        return Result.success(vehicleSettlementAppletBiz.searchDriverReconciliationList(requestModel));
    }

    /**
     * 司机账单列表数量统计
     * @param requestModel
     * @return
     */
    @ApiOperation(("司机账单列表数量统计"))
    @PostMapping(value = "/driverReconciliationListCount")
    public Result<DriverReconciliationListCountResponseModel> driverReconciliationListCount(@RequestBody SearchDriverReconciliationListRequestModel requestModel) {
        return Result.success(vehicleSettlementAppletBiz.driverReconciliationListCount(requestModel));
    }

    /**
     * 司机账单详情
     * @param requestModel
     * @return
     */
    @ApiOperation(("司机账单详情"))
    @PostMapping(value = "/driverReconciliationDetail")
    public Result<DriverReconciliationDetailResponseModel> driverReconciliationDetail(@RequestBody VehicleSettlementIdRequestModel requestModel) {
        return Result.success(vehicleSettlementAppletBiz.driverReconciliationDetail(requestModel));
    }

    /**
     * 司机确认账单
     * @param requestModel
     * @return
     */
    @ApiOperation(("司机确认账单"))
    @PostMapping(value = "/driverReconciliationConfirm")
    public Result driverReconciliationConfirm(@RequestBody DriverReconciliationConfirmRequestModel requestModel) {
        vehicleSettlementAppletBiz.driverReconciliationConfirm(requestModel);
        return Result.success(true);
    }

    /**
     * 司机确认详情
     * @param requestModel
     * @return
     */
    @ApiOperation(("司机确认详情"))
    @PostMapping(value = "/driverReconciliationConfirmDetail")
    public Result<DriverReconciliationConfirmDetailResponseModel> driverReconciliationConfirmDetail(@RequestBody VehicleSettlementIdRequestModel requestModel) {
        return Result.success(vehicleSettlementAppletBiz.driverReconciliationConfirmDetail(requestModel));
    }

    /**
     * 司机账单运单列表
     * @param requestModel
     * @return
     */
    @ApiOperation(("司机账单运单列表"))
    @PostMapping(value = "/driverReconciliationCarrierOrder")
    public Result<ReconciliationCarrierOrderDetailResponseModel> driverReconciliationCarrierOrder(@RequestBody VehicleSettlementIdRequestModel requestModel) {
        return Result.success(vehicleSettlementAppletBiz.driverReconciliationCarrierOrder(requestModel));
    }

    /**
     * 司机账单收款记录
     * @param requestModel
     * @return
     */
    @ApiOperation(("司机账单收款记录"))
    @PostMapping(value = "/driverReconciliationBillingRecords")
    public Result<List<ReconciliationBillingRecordsResponseModel>> driverReconciliationBillingRecords(@RequestBody VehicleSettlementIdRequestModel requestModel) {
        return Result.success(vehicleSettlementAppletBiz.driverReconciliationBillingRecords(requestModel));
    }

    /**
     * 司机账单轮胎费用列表
     * @param requestModel
     * @return
     */
    @ApiOperation(("司机账单轮胎费用列表"))
    @PostMapping(value = "/driverReconciliationTire")
    public Result<ReconciliationTireDetailResponseModel> driverReconciliationTire(@RequestBody VehicleSettlementIdRequestModel requestModel) {
        return Result.success(vehicleSettlementAppletBiz.driverReconciliationTire(requestModel));
    }

    /**
     * 司机账单充油费用列表
     * @param requestModel
     * @return
     */
    @ApiOperation(("司机账单充油费用列表"))
    @PostMapping(value = "/driverReconciliationOilFilled")
    public Result<ReconciliationOilFilledDetailResponseModel> driverReconciliationOilFilled(@RequestBody VehicleSettlementIdRequestModel requestModel) {
        return Result.success(vehicleSettlementAppletBiz.driverReconciliationOilFilled(requestModel));
    }
}
