package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;

/**
 * @author: wjf
 * @date: 2021/9/24 17:54
 */
@Data
public class LifeBatchPublishRequestDto {

    /**
     * 需求单id
     */
    @ApiModelProperty(value = "需求单列表", required = true)
    @NotNull(message = "需求单信息不能为空")
    @Valid
    @Size(max = 10, message = "最多发布10条数据")
    private List<String> demandDtoList;


    /**
     * 货主价格类型
     */
    @ApiModelProperty(value = "货主价格类型：1 单价(元/吨，元/件)，2 一口价(元)",required = true)
    @NotBlank(message = "请选择接货主价格类型")
    @Pattern(regexp = "^[12]$", message = "请选择货主价格类型")
    private String contractPriceType;
    /**
     * 货主价格
     */
    @ApiModelProperty(value = "货主价格,发布单个可以是单价也可以是一口价,批量只能为单价",required = true)
    @NotBlank(message = "请维护货主报价，0<=金额<=10000000")
    @DecimalMax(value = "10000000",message = "货主报价，0<=金额<=10000000")
    private String contractPrice;

    /**
     * 是否我司 1:我司,2:其他车主
     */
    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主", required = true)
    @NotBlank(message = "请选择接单车主类型")
    @Pattern(regexp = "^[12]$", message = "请选择接单车主类型")
    private String isOurCompany;
    /**
     * 车主ID（其他车主时必填）
     */
    @ApiModelProperty(value = "车主ID（其他车主时必填）")
    private String companyCarrierId;
    /**
     * 车主价格类型
     */
    @ApiModelProperty(value = "车主价格：1 单价(元/吨，元/件)，2 一口价(元)",required = true)
    @NotBlank(message = "请选择接车主价格类型")
    @Pattern(regexp = "^[12]$", message = "请选择车主价格类型")
    private String carrierPriceType;
    /**
     * 车主价格
     */
    @ApiModelProperty(value = "车主价格,发布单个可以是单价也可以是一口价,批量为单价",required = true)
    @NotBlank(message = "请维护车主运费，0<=金额<=10000000")
    @DecimalMax(value = "10000000",message = "车主运费，0<=金额<=10000000")
    private String carrierPrice;
}
