package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2023/05/04
*/
@Data
public class TReachManagement extends BaseEntity {
    /**
    * 运单id
    */
    @ApiModelProperty("运单id")
    private Long carrierOrderId;

    /**
    * 运单号
    */
    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    /**
    * 触达司机名称
    */
    @ApiModelProperty("触达司机名称")
    private String reachDriverName;

    /**
    * 触达司机联系方式
    */
    @ApiModelProperty("触达司机联系方式")
    private String reachDriverPhone;

    /**
    * 触达抬头
    */
    @ApiModelProperty("触达抬头")
    private String terminalHead;

    /**
    * 触达省份id
    */
    @ApiModelProperty("触达省份id")
    private Long reachProvinceId;

    /**
    * 触达省市名称
    */
    @ApiModelProperty("触达省市名称")
    private String reachProvinceName;

    /**
    * 触达城市id
    */
    @ApiModelProperty("触达城市id")
    private Long reachCityId;

    /**
    * 触达城市名称
    */
    @ApiModelProperty("触达城市名称")
    private String reachCityName;

    /**
    * 触达区域id
    */
    @ApiModelProperty("触达区域id")
    private Long reachAreaId;

    /**
    * 触达区域名称
    */
    @ApiModelProperty("触达区域名称")
    private String reachAreaName;

    /**
    * 触达详细地址
    */
    @ApiModelProperty("触达详细地址")
    private String reachAddressDetail;

    /**
    * 触达地址备注
    */
    @ApiModelProperty("触达地址备注")
    private String reachAddressRemark;

    /**
    * 触达定位经度
    */
    @ApiModelProperty("触达定位经度")
    private String reachLongitude;

    /**
    * 触达定位纬度
    */
    @ApiModelProperty("触达定位纬度")
    private String reachLatitude;

    /**
    * 距离偏差(km)
    */
    @ApiModelProperty("距离偏差(km)")
    private BigDecimal distanceDeviation;

    /**
    * 触达联系人
    */
    @ApiModelProperty("触达联系人")
    private String reachContactor;

    /**
    * 触达联系方式
    */
    @ApiModelProperty("触达联系方式")
    private String reachTelephone;

    /**
    * 核验触达联系方式: 1、无误 2、有误
    */
    @ApiModelProperty("核验触达联系方式: 1、无误 2、有误")
    private Integer checkReachContact;

    /**
    * 触达时间
    */
    @ApiModelProperty("触达时间")
    private Date reachTime;

    /**
    * 空置数量
    */
    @ApiModelProperty("空置数量")
    private Integer emptyTraysAmount;

    /**
    * 带料占用数量
    */
    @ApiModelProperty("带料占用数量")
    private Integer employTraysAmount;

    /**
    * 有效(0.无效 1.有效 )
    */
    @ApiModelProperty("有效(0.无效 1.有效 )")
    private Integer valid;
}