<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderLocationMapper" >
    <select id="getByCarrierOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_order_location
        where valid = 1
        and carrier_order_id in (${carrierOrderIds})
    </select>

    <select id="getByCarrierOrderIdType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_order_location
        where valid = 1
        and carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
        and location_type = #{locationType,jdbcType=INTEGER}
    </select>
</mapper>