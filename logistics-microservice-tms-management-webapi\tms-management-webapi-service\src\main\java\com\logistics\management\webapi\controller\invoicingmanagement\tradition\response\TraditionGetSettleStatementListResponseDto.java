package com.logistics.management.webapi.controller.invoicingmanagement.tradition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/3/19 17:43
 */
@Data
public class TraditionGetSettleStatementListResponseDto {
    /**
     * 发票关联对账单id
     */
    @ApiModelProperty("发票关联对账单id")
    private String invoicingSettleStatementId = "";

    /**
     * 对账月份
     */
    @ApiModelProperty("对账月份")
    private String settleStatementMonth = "";

    /**
     * 对账单id
     */
    @ApiModelProperty("对账单id")
    private String settleStatementId = "";
    /**
     * 对账单号
     */
    @ApiModelProperty("对账单号")
    private String settleStatementCode = "";

    /**
     * 费额合计
     */
    @ApiModelProperty("费额合计")
    private String carrierFreightTotal = "";

    /**
     * 临时费用合计
     */
    @ApiModelProperty("临时费用合计")
    private String otherFeeTotal = "";

    /**
     * 差异调整费用
     */
    @ApiModelProperty("差异调整费用")
    private String adjustFee = "";

    /**
     * 对账费用
     */
    @ApiModelProperty("对账费用")
    private String reconciliationFee = "";

    /**
     * 对账单名称
     */
    @ApiModelProperty("对账单名称")
    private String settleStatementName = "";

    /**
     * 合同号
     */
    @ApiModelProperty("合同号")
    private String contractCode = "";
}
