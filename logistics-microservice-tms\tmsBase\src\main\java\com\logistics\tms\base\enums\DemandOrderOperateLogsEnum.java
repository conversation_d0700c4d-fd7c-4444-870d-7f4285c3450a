package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2018/9/27 11:55
 */
public enum DemandOrderOperateLogsEnum {


    CREATE_DEMAND_ORDER(1001,"生成需求单",""),
    CANCEL_DEMAND_ORDER(1002,"取消需求单","%1$s取消，%2$s"),
    DISPATCH_VEHICLE(1003, "调度车辆，生成运单%1$s", ""),
    COMPLETE_DISPATCH(1004, "完成调度", "未安排件数%1$s，已退回"),
    PUBLISH_DEMAND_ORDER(1005, "确认发布", ""),
    WAIT_SIGN_UP(1006, "待签收", ""),
    SIGN_UP(1007, "已签收", ""),
    ENTRUST_RECEIVEMENT(1008, "委托方收款", "%1$s收款%2$s元"),
    ENTRUST_REFUND(1009, "委托方回退", "%1$s回退%2$s元,回退原因：%3$s"),
    CARRIER_PAYMENT(1010, "承运商付款", "%1$s付款%2$s元"),
    CARRIER_REFUND(1011, "承运商回退", "%1$s回退%2$s元,回退原因：%3$s"),
    ENTRUST_MODIFY_COST(1012, "修改委托方运价", "需求单原报价类型%1$s，修改为%2$s，原运价%3$s元修改为%4$s元"),
    REPUBLISH_DEMAND_ORDER(1013, "重新发布", ""),
    MODIFY_CARRIER(1014, "修改车主", "修改前车主：%1$s，修改后车主：%2$s，修改原因：%3$s"),
    DEMAND_ORDER_UPDATE_ADDRESS(1015, "修改卸货地址", ""),
    DEMAND_ORDER_UPDATE_AMOUNT(1016, "修改委托数量", ""),
    DEMAND_ORDER_AUDIT_AUDITED(1017, "审核通过", ""),
    DEMAND_ORDER_AUDIT_REJECT(1018, "审核驳回", ""),
    SYNC_DEMAND_ORDER(1019, "同步网络货运", ""),
    WITHDRAW_DEMAND_ORDER(1020, "撤回", ""),
    DEMAND_ORDER_EMPTY(1030, "放空", ""),
    DEMAND_ORDER_ROLLBACK(1031, "回退", ""),
    CHANGE_UNLOAD_WAREHOUSE(1032, "变更收货地", "%1$s变更为%2$s"),
    DEMAND_ORDER_REJECT_ROLLBACK(1033, "重新推送", ""),
    ;

    private Integer key;
    private String value;
    private String format;

    DemandOrderOperateLogsEnum(Integer key, String value, String format) {
        this.key = key;
        this.value = value;
        this.format = format;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getFormat() {
        return format;
    }

}
