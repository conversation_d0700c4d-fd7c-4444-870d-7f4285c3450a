package com.logistics.appapi.client.sysconfig.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MailingInfoDetailResponseModel {

    @ApiModelProperty(value = "邮寄地址")
    private String mailingInfoId;

    @ApiModelProperty("收货地址")
    private String address;

    @ApiModelProperty("收货联系人")
    private String addressee;

    @ApiModelProperty("收货联系人手机号")
    private String addresseeMobile;

    @ApiModelProperty("应用场景")
    private String applyScope;
}
