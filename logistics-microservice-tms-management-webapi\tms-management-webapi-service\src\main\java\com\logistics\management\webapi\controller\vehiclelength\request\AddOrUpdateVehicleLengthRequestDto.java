package com.logistics.management.webapi.controller.vehiclelength.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;

@Data
public class AddOrUpdateVehicleLengthRequestDto {

    /**
     * 车长配置id
     */
    @ApiModelProperty("车长配置id")
    private String vehicleLengthId;

    /**
     * 车长
     */
    @ApiModelProperty("车长")
    @NotBlank(message = "车长不能为空")
    @DecimalMin(value = "0.01",message = "请维护车长，大于0小于999.99，且保留两位小数")
    @DecimalMax(value = "999.99",message = "请维护车长，大于0小于999.99，且保留两位小数")
    private String vehicleLength;

    /**
     * 承运范围-低
     */
    @ApiModelProperty("承运范围-低")
    @NotBlank(message = "承运范围不能为空")
    @DecimalMin(value = "1", message = "请维护承运范围")
    @DecimalMax(value = "100000", message = "请维护承运范围")
    private String carriageScopeMin;

    /**
     * 承运范围-高
     */
    @ApiModelProperty("承运范围-高")
    @NotBlank(message = "承运范围不能为空")
    @DecimalMin(value = "1", message = "请维护承运范围")
    @DecimalMax(value = "100000", message = "请维护承运范围")
    private String carriageScopeMax;

}
