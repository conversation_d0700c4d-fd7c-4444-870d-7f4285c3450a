package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TParkingFeeRecords extends BaseEntity {
    /**
    * 停车费用表id
    */
    @ApiModelProperty("停车费用表id")
    private Long parkingFeeId;

    /**
    * 合作公司
    */
    @ApiModelProperty("合作公司")
    private String cooperationCompany;

    /**
    * 起始日期
    */
    @ApiModelProperty("起始日期")
    private Date startDate;

    /**
    * 截止时间
    */
    @ApiModelProperty("截止时间")
    private Date endDate;

    /**
    * 终止时间
    */
    @ApiModelProperty("终止时间")
    private Date finishDate;

    /**
    * 停车费
    */
    @ApiModelProperty("停车费")
    private BigDecimal parkingFee;

    /**
    * 合作周期（月）
    */
    @ApiModelProperty("合作周期（月）")
    private Integer cooperationPeriod;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}