package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2022/8/4 16:20
 */
public enum VehicleOilCardStatusEnum {
    UNBINDING(0, "未绑定"),
    BINDING(1, "已绑定"),
    ;

    private Integer key;
    private String value;

    VehicleOilCardStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
