package com.logistics.tms.biz.carriercontact;

import com.logistics.tms.api.feign.carriercontact.dto.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.LocalStringUtil;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.customeraccount.CustomerAccountBiz;
import com.logistics.tms.controller.customeraccount.request.OpenAccountRequestModel;
import com.logistics.tms.entity.TCarrierContact;
import com.logistics.tms.entity.TCompany;
import com.logistics.tms.entity.TCompanyCarrier;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.MapUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service("carrierContactBiz")
public class CarrierContactBiz {

    @Autowired
    private TCarrierContactMapper tCarrierContactMapper;

    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;

    @Autowired
    private CommonBiz commonBiz;

    @Autowired
    private CustomerAccountBiz customerAccountBiz;

    @Autowired
    private TCompanyMapper tCompanyMapper;
    @Autowired
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Autowired
    private TDemandOrderMapper tDemandOrderMapper;

    /**
     * 车主账号新增编辑
     *
     * @param requestModel
     */
    @Transactional
    public void saveAccount(SaveCarrierContactRequestModel requestModel) {
        Long carrierContactId = requestModel.getCarrierContactId();
        //查询车主是否存在
        TCompanyCarrier dbCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(requestModel.getCompanyCarrierId());
        if (dbCompanyCarrier == null || IfValidEnum.INVALID.getKey().equals(dbCompanyCarrier.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }

        if (carrierContactId != null && carrierContactId > CommonConstant.LONG_ZERO) {//修改
            //查询账号是否存在
            TCarrierContact dbCarrierContact = tCarrierContactMapper.selectByPrimaryKeyDecrypt(carrierContactId);
            if (dbCarrierContact == null || IfValidEnum.INVALID.getKey().equals(dbCarrierContact.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
            }
            //修改的账号所属车主与选择的车主不一致
            if (!dbCarrierContact.getCompanyCarrierId().equals(dbCompanyCarrier.getId())){
                throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
            }
            //禁用不允许编辑
            if (EnabledEnum.DISABLED.getKey().equals(dbCarrierContact.getEnabled())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_CONTACT_FORBIDDEN_NOT_ALLOW_MODIFY);
            }

            //更新车主账号信息
            TCarrierContact carrierContact = new TCarrierContact();
            carrierContact.setId(carrierContactId);
            carrierContact.setContactName(requestModel.getContactName());
            carrierContact.setIdentityNumber(requestModel.getIdentityNumber());
            commonBiz.setBaseEntityModify(carrierContact, BaseContextHandler.getUserName());
            tCarrierContactMapper.updateByPrimaryKeySelectiveEncrypt(carrierContact);

            //修改用户名
            if (!dbCarrierContact.getContactName().equals(requestModel.getContactName())) {
                OpenAccountRequestModel openAccountRequestModel = new OpenAccountRequestModel();
                openAccountRequestModel.setUserId(carrierContact.getId());
                openAccountRequestModel.setUserRole(AccountUserRoleTypeEnum.CARRIER.getKey());
                openAccountRequestModel.setUserName(carrierContact.getContactName());
                openAccountRequestModel.setType(CommonConstant.INTEGER_ONE);
                if (CommonConstant.INTEGER_ONE.equals(dbCarrierContact.getEnabled())) {
                    openAccountRequestModel.setIfClose(CommonConstant.INTEGER_ZERO);
                } else {
                    openAccountRequestModel.setIfClose(CommonConstant.INTEGER_ONE);
                }
                customerAccountBiz.openOrCloseAccount(openAccountRequestModel);
            }
        } else {//新增
            //只有企业车主才能新增账号
            if (!CompanyTypeEnum.COMPANY.getKey().equals(dbCompanyCarrier.getType())) {
                throw new BizException(CarrierDataExceptionEnum.NO_COMPANYTYPE);
            }
            //根据手机查询账号
            TCarrierContact tCarrierContact = tCarrierContactMapper.selectByContactPhone(requestModel.getContactPhone());
            //车主账号存在
            if (tCarrierContact != null) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ACCOUNT_PHONE_REPEAT);
            }

            //新增车主账号
            TCarrierContact carrierContact = new TCarrierContact();
            carrierContact.setCompanyCarrierId(dbCompanyCarrier.getId());
            carrierContact.setContactName(requestModel.getContactName());
            carrierContact.setContactPhone(requestModel.getContactPhone());
            carrierContact.setIdentityNumber(requestModel.getIdentityNumber());
            carrierContact.setEnabled(EnabledEnum.DISABLED.getKey());//设为禁用状态
            commonBiz.setBaseEntityAdd(carrierContact, BaseContextHandler.getUserName());
            tCarrierContactMapper.insertSelectiveEncrypt(carrierContact);

            //账号开通
            OpenAccountRequestModel openAccountRequestModel = new OpenAccountRequestModel();
            openAccountRequestModel.setUserId(carrierContact.getId());
            openAccountRequestModel.setUserRole(AccountUserRoleTypeEnum.CARRIER.getKey());
            openAccountRequestModel.setMobile(carrierContact.getContactPhone());
            openAccountRequestModel.setUserName(carrierContact.getContactName());
            openAccountRequestModel.setType(CommonConstant.INTEGER_ONE);
            openAccountRequestModel.setEnabled(EnabledEnum.DISABLED.getKey());
            openAccountRequestModel.setIfClose(CommonConstant.INTEGER_ONE);
            customerAccountBiz.openOrCloseAccount(openAccountRequestModel);
        }
    }

    /**
     * 车主账号查看详情
     * @param requestModel
     * @return
     */
    public CarrierContactDetailResponseModel getDetail(CarrierContactDetailRequestModel requestModel) {
        CarrierContactDetailResponseModel carrierContactDetailById = tCarrierContactMapper.getCarrierContactDetailById(requestModel.getCarrierContactId());
        if (carrierContactDetailById == null){
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
        }
        return carrierContactDetailById;
    }

    /**
     * 禁用启用账号
     * @param requestModel
     */
    @Transactional
    public void enableDisableClosed(CarrierContactEnableRequestModel requestModel) {
        TCarrierContact tCarrierContact = tCarrierContactMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierContactId());
        //判断账号是否存在
        if (tCarrierContact == null || IfValidEnum.INVALID.getKey().equals(tCarrierContact.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
        }

        //启用校验身份证信息
        if (EnabledEnum.ENABLED.getKey().equals(requestModel.getUpdateEnable()) &&
                StringUtils.isBlank(tCarrierContact.getIdentityNumber())) {
            throw new BizException(CarrierDataExceptionEnum.IDENTITY_INFORMATION_INCOMPLETE);
        }

        //重复禁用启用判断
        if (tCarrierContact.getEnabled().equals(requestModel.getUpdateEnable())) {
            if (EnabledEnum.DISABLED.getKey().equals(tCarrierContact.getEnabled())) {
                throw new BizException(CarrierDataExceptionEnum.ACCOUNT_IS_DISABLED);
            }
            if (EnabledEnum.ENABLED.getKey().equals(tCarrierContact.getEnabled())) {
                throw new BizException(CarrierDataExceptionEnum.ACCOUNT_IS_ENABLED);
            }
        }

        //车主账号表
        TCarrierContact uPCarrierContact = new TCarrierContact();
        uPCarrierContact.setId(tCarrierContact.getId());
        uPCarrierContact.setEnabled(requestModel.getUpdateEnable());
        commonBiz.setBaseEntityModify(uPCarrierContact, BaseContextHandler.getUserName());
        tCarrierContactMapper.updateByPrimaryKeySelectiveEncrypt(uPCarrierContact);

        //关联账号表
        OpenAccountRequestModel openAccountRequestModel = new OpenAccountRequestModel();
        openAccountRequestModel.setUserId(tCarrierContact.getId());
        openAccountRequestModel.setUserRole(AccountUserRoleTypeEnum.CARRIER.getKey());
        openAccountRequestModel.setMobile(tCarrierContact.getContactPhone());
        openAccountRequestModel.setUserName(tCarrierContact.getContactName());
        if (requestModel.getUpdateEnable().equals(EnabledEnum.ENABLED.getKey())) {
            openAccountRequestModel.setType(CommonConstant.INTEGER_ONE);
            openAccountRequestModel.setEnabled(EnabledEnum.ENABLED.getKey());
            openAccountRequestModel.setIfClose(CommonConstant.INTEGER_ZERO);
        }else if (requestModel.getUpdateEnable().equals(EnabledEnum.DISABLED.getKey())) {
            openAccountRequestModel.setType(CommonConstant.INTEGER_TWO);
            openAccountRequestModel.setEnabled(EnabledEnum.DISABLED.getKey());
            openAccountRequestModel.setIfClose(CommonConstant.INTEGER_ONE);
        }
        customerAccountBiz.openOrCloseAccount(openAccountRequestModel);
        //清空token
        if (EnabledEnum.DISABLED.getKey().equals(requestModel.getUpdateEnable())) {
            commonBiz.clearToken(tCarrierContact.getContactPhone());
        }
    }

    /**
     * 车主账号列表
     * @param requestModel
     * @return
     */
    public List<SearchCarrierContactResponseModel> searchList(SearchCarrierContactRequestModel requestModel) {
        HashMap<Long, String> companyNameMap = new HashMap<>();
        //根据公司名称模糊查询企业公司
        if (StringUtils.isNotBlank(requestModel.getCompanyCarrierName())){
            List<TCompany> companyList = tCompanyMapper.getByIdsName(requestModel.getCompanyCarrierName(), null);
            List<Long> companyIdList = new ArrayList<>();
            if (ListUtils.isNotEmpty(companyList)){
                for (TCompany tCompany : companyList) {
                    companyNameMap.put(tCompany.getId(), tCompany.getCompanyName());
                    companyIdList.add(tCompany.getId());
                }
                requestModel.setCompanyIds(LocalStringUtil.listTostring(companyIdList, ','));
            }
        }

        //分页查询
        requestModel.enablePaging();
        List<SearchCarrierContactResponseModel> list = tCarrierContactMapper.selectCarrierContactList(requestModel);
        if (ListUtils.isEmpty(list)) {
            return list;
        }
        //查询企业类型公司名称
        if (MapUtils.isEmpty(companyNameMap)){
            //获取主公司id
            List<Long> companyIdList = list.stream().filter(item -> CompanyTypeEnum.COMPANY.getKey().equals(item.getType())).map(SearchCarrierContactResponseModel::getCompanyId).collect(Collectors.toList());
            //查询主公司信息
            if (ListUtils.isNotEmpty(companyIdList)){
                List<TCompany> companyList = tCompanyMapper.getByIdsName(null, StringUtils.listToString(companyIdList,','));
                companyList.forEach(item -> companyNameMap.put(item.getId(), item.getCompanyName()));
            }
        }
        //拼接数据
        for (SearchCarrierContactResponseModel model : list) {
            if (model.getType().equals(CompanyTypeEnum.COMPANY.getKey())){
                model.setCompanyCarrierName(companyNameMap.get(model.getCompanyId()));
            }
        }

        return list;
    }

    /**
     * 删除车主账号
     * @param requestModel
     */
    @Transactional
    public void delCarrierAccount(DelCarrierContactRequestModel requestModel) {
        Long carrierContactId = requestModel.getCarrierContactId();
        //查询账号是否存在
        TCarrierContact dbCarrierContact = tCarrierContactMapper.selectByPrimaryKeyDecrypt(carrierContactId);
        if (dbCarrierContact == null || IfValidEnum.INVALID.getKey().equals(dbCarrierContact.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
        }

        //判断车主公司是否存在
        TCompanyCarrier dbCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(dbCarrierContact.getCompanyCarrierId());
        if (dbCompanyCarrier == null || dbCompanyCarrier.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }
        //个人账号不能删除
        if (CompanyTypeEnum.PERSON.getKey().equals(dbCompanyCarrier.getType())){
            throw new BizException(CarrierDataExceptionEnum.PERSON_COMPANY_ACCOUNT_NOT_DELETE);
        }

        //启用中的不能删除
        if (EnabledEnum.ENABLED.getKey().equals(dbCarrierContact.getEnabled())){
            throw new BizException(CarrierDataExceptionEnum.ENABLE_IS_NOT_REMOVED);
        }

        //删除车主账号
        TCarrierContact contact = new TCarrierContact();
        contact.setId(carrierContactId);
        contact.setValid(IfValidEnum.INVALID.getKey());
        commonBiz.setBaseEntityModify(contact,BaseContextHandler.getUserName());
        tCarrierContactMapper.updateByPrimaryKeySelectiveEncrypt(contact);


        //删除客户关系
        OpenAccountRequestModel openAccountRequestModel = new OpenAccountRequestModel();
        openAccountRequestModel.setUserId(dbCarrierContact.getId());
        openAccountRequestModel.setUserRole(AccountUserRoleTypeEnum.CARRIER.getKey());
        openAccountRequestModel.setUserName(dbCarrierContact.getContactName());
        openAccountRequestModel.setType(CommonConstant.INTEGER_THREE);
        customerAccountBiz.openOrCloseAccount(openAccountRequestModel);
        //清除token
        commonBiz.clearToken(dbCarrierContact.getContactPhone());
    }
}
