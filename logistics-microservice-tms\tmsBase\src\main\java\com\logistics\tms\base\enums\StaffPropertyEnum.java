package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2018/9/27 15:12
 */
public enum StaffPropertyEnum {
    PROPERTY_ZERO(0,""),
    OWN_STAFF(1, "自有人员"),
    EXTERNAL_STAFF(2, "外部人员"),
    AFFILIATION_STAFF(3, "自营人员"),
    ;

    private Integer key;
    private String value;

    StaffPropertyEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
