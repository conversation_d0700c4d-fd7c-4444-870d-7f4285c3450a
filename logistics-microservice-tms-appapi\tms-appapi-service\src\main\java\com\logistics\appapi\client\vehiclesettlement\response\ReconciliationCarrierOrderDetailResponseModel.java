package com.logistics.appapi.client.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author：wjf
 * @date：2021/4/12 13:12
 */
@Data
public class ReconciliationCarrierOrderDetailResponseModel {
    @ApiModelProperty("账单月份")
    private String settlementMonth;
    @ApiModelProperty("运费合计")
    private BigDecimal carrierFreight;
    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;
    @ApiModelProperty("运单列表")
    private List<ReconciliationCarrierOrderListResponseModel> carrierOrderList;

    private List<Long> carrierOrderIdList;//结算表关联的运单ids
}
