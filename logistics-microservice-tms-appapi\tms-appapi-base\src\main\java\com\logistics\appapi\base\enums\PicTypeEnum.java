/**
 * Created by yun<PERSON>zhou on 2017/12/12.
 */
package com.logistics.appapi.base.enums;

import lombok.Getter;

@Getter
public enum PicTypeEnum {
    LIVE_PIC("1", "现场图片"),
    PAY_PIC("2", "支付图片"),
    ;

    private final String key;
    private final String value;

    PicTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static PicTypeEnum getEnum(String key) {
        for (PicTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
