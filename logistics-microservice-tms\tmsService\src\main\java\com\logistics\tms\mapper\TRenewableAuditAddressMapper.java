package com.logistics.tms.mapper;

import com.logistics.tms.entity.TRenewableAuditAddress;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2022/08/15
*/
@Mapper
public interface TRenewableAuditAddressMapper extends BaseMapper<TRenewableAuditAddress> {

	TRenewableAuditAddress selectByPrimaryKeyDecrypt(Long id);

	int insertSelectiveEncrypt(TRenewableAuditAddress tDriverAppoint);

	int updateByPrimaryKeySelectiveEncrypt(TRenewableAuditAddress tDriverAppoint);

	/**
	 * 批量查询新生审核列表地址信息
	 *
	 * @param renewableAuditIds
	 * @return
	 */
	List<TRenewableAuditAddress> queryRenewableAuditAddressList(@Param("renewableAuditIds") List<Long> renewableAuditIds);

	/**
	 * 查询新生审核地址列表信息
	 *
	 * @param renewableOrderId
	 * @return
	 */
	TRenewableAuditAddress getAddressByRenewableOrderId(@Param("renewableOrderId") Long renewableOrderId);
}