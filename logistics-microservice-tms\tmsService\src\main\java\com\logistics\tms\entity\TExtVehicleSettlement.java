package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TExtVehicleSettlement extends BaseEntity {
    /**
    * 运单Id
    */
    @ApiModelProperty("运单Id")
    private Long carrierOrderId;

    /**
    * 车辆Id
    */
    @ApiModelProperty("车辆Id")
    private Long vehicleId;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 司机id
    */
    @ApiModelProperty("司机id")
    private Long staffId;

    /**
    * 司机姓名
    */
    @ApiModelProperty("司机姓名")
    private String staffName;

    /**
    * 司机电话
    */
    @ApiModelProperty("司机电话")
    private String staffMobile;

    /**
    * 司机合计费用
    */
    @ApiModelProperty("司机合计费用")
    private BigDecimal driverTotalFee;

    /**
    * 单位：1 件，2 吨，3 方，4 块
    */
    @ApiModelProperty("单位：1 件，2 吨，3 方，4 块")
    private Integer settlementUnit;

    /**
    * 结算数量
    */
    @ApiModelProperty("结算数量")
    private BigDecimal settlementAmount;

    /**
    * 付款状态: 0 未支付 1 已支付
    */
    @ApiModelProperty("付款状态: 0 未支付 1 已支付")
    private Integer status;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}