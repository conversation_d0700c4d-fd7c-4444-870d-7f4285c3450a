package com.logistics.management.webapi.api.feign.driverpayee;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.driverpayee.dto.*;
import com.logistics.management.webapi.api.feign.driverpayee.hystrix.DriverPayeeApiHystrix;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@Api(value = "API-DriverPayeeApi-收款人账户管理")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = DriverPayeeApiHystrix.class)
public interface DriverPayeeApi {

    @PostMapping(value = "/api/driverPayee/driverPayeeList")
    @ApiOperation(value = "司机收款人账户列表")
    Result<PageInfo<DriverPayeeListResponseDto>> driverPayeeList(@RequestBody DriverPayeeListRequestDto requestDto);

    @PostMapping(value = "/api/driverPayee/addOrModify")
    @ApiOperation(value = "司机收款人账户新增/修改")
    Result<Boolean> addOrModifyDriverPayee(@RequestBody AddOrModifyDriverPayeeRequestDto requestDto);

    @PostMapping(value = "/api/driverPayee/detail")
    @ApiOperation(value = "司机收款人账户详情")
    Result<DriverPayeeDetailResponseDto> driverPayeeDetail(@RequestBody DriverPayeeDetailRequestDto requestDto);

    @PostMapping(value = "/api/driverPayee/auditOrReject")
    @ApiOperation(value = "司机收款人审核驳回作废")
    Result<Boolean> auditOrReject(@RequestBody AuditRejectDriverPayeeRequestDto requestDto);

    @ApiOperation(value = "导出")
    @GetMapping(value = "/api/driverPayee/export")
    void driverPayeeExport(DriverPayeeListRequestDto requestDto, HttpServletResponse response);

    @ApiOperation(value = "导入")
    @PostMapping(value = "/api/driverPayee/import")
    Result<ImportDriverPayeeResponseDto> importDriverPayee(@RequestParam("file") MultipartFile file, HttpServletRequest request);

    @ApiOperation(value = "导入证件信息")
    @PostMapping(value = "/api/driverPayee/importCertificateInfo")
    Result<Boolean> importDriverPayeeInfo(@RequestParam("file") MultipartFile file);

    @ApiOperation(value = "查看日志")
    @PostMapping(value = "/api/driverPayee/viewLogs")
    Result<List<ViewLogsResponseDto>> driverPayeeLogs(@RequestBody @Valid DriverPayeeDetailRequestDto requestDto);

    @PostMapping(value = "/api/driverPayee/searchDriverPayees")
    @ApiOperation(value = "查询已审核的收款账户信息")
    Result<List<SearchDriverPayeesResponseDto>> searchDriverPayees(@RequestBody SearchDriverPayeesRequestDto requestDto);

}
