package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TFreightAddressRuleMarkup extends BaseEntity {
    /**
    * 运价地址规则ID
    */
    @ApiModelProperty("运价地址规则ID")
    private Long freightAddressRuleId;

    /**
    * 阶梯序列
    */
    @ApiModelProperty("阶梯序列")
    private Integer markIndex;

    /**
    * 装货点数
    */
    @ApiModelProperty("装货点数")
    private Integer loadAmount;

    /**
    * 卸货点数
    */
    @ApiModelProperty("卸货点数")
    private Integer unloadAmount;

    /**
    * 加价金额
    */
    @ApiModelProperty("加价金额")
    private BigDecimal markupFreightFee;
}