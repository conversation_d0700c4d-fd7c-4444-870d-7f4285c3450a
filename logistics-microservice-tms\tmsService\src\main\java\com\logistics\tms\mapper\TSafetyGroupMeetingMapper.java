package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.safegroupmeeting.model.SafeGroupMeetingDetailResponseModel;
import com.logistics.tms.api.feign.safegroupmeeting.model.SafeGroupMeetingResponseModel;
import com.logistics.tms.entity.TSafetyGroupMeeting;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TSafetyGroupMeetingMapper extends BaseMapper<TSafetyGroupMeeting> {

    TSafetyGroupMeeting getSafetyGroupMeetingByYearAndSeason(@Param("meetingYear") String meetingYear,@Param("meetingSeason") Integer meetingSeason);

    List<SafeGroupMeetingResponseModel> getSafeGroupMeetingData(@Param("meetingYear") String meetingYear);


    SafeGroupMeetingDetailResponseModel getSafeGroupMeetingDetail(@Param("safetyGroupingMeetingId") Long safetyGroupingMeetingId);



}