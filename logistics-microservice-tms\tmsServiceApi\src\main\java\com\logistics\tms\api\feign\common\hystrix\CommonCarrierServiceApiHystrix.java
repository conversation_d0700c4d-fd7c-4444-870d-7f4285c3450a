package com.logistics.tms.api.feign.common.hystrix;

import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.common.CommonCarrierServiceApi;
import com.logistics.tms.api.feign.common.model.LoginUserCompanyCarrierBasicInfoResponseModel;
import org.springframework.stereotype.Component;


@Component("tmsCommonCarrierServiceApiHystrix")
public class CommonCarrierServiceApiHystrix implements CommonCarrierServiceApi {


    @Override
    public Result<Long> getLoginUserCompanyCarrierId() {
        return Result.timeout();
    }

    @Override
    public Result<LoginUserCompanyCarrierBasicInfoResponseModel> getLoginUserCompanyCarrierBasicInfo() {
        return Result.timeout();
    }

}
