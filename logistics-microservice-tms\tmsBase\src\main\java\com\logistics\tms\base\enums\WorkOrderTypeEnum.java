package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum WorkOrderTypeEnum {

    DEFAULT(-1, ""),
    DEMAND_ORDER_TYPE(10, "需求单"),
    CARRIER_ORDER_TYPE(20, "运单"),
    ;

    private final Integer key;
    private final String value;

    public static WorkOrderTypeEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
