package com.logistics.appapi.client.reservationorder.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ReservationOrderDetail4H5ReqDto {

    /**
     * 预约单id
     */
    @NotBlank(message = "运单号code")
    private String carrierOrderCode;

    /**
     * 定位-经度
     */
    @NotBlank(message = "请获取定位")
    private String longitude;
    /**
     * 定位-纬度
     */
    @NotBlank(message = "请获取定位")
    private String latitude;


}
