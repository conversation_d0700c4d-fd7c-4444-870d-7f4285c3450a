package com.logistics.tms.controller.freightconfig.response.ladder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.assertj.core.util.Lists;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CarrierFreightConfigLadderResponseModel {

    @ApiModelProperty(value = "阶梯ID")
    private Long freightConfigLadderId;

    @ApiModelProperty(value = "阶梯层级; 0 为固定单价/总价")
    private Integer ladderLevel;

    @ApiModelProperty(value = "阶梯层级父Id")
    private Long ladderPid;

    @ApiModelProperty(value = "阶梯类型; 1: KM(公里); 2: 数量;")
    private Integer ladderType;

    @ApiModelProperty(value = "阶梯起始")
    private BigDecimal ladderFrom;

    @ApiModelProperty(value = "阶梯终止（包含等于）")
    private BigDecimal ladderTo;

    @ApiModelProperty(value = "单位; 1: 件; 2: 吨; 4: 块")
    private Integer ladderUnit;

    @ApiModelProperty(value = "价格模式; 1: 单价; 2: 总价;")
    private Integer priceMode;

    @ApiModelProperty(value = "价格(元)")
    private BigDecimal unitPrice;
    
    @ApiModelProperty(value = "多级阶梯")
    private List<CarrierFreightConfigLadderResponseModel> ladderConfigList = Lists.newArrayList();
}
