<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDriverSafePromiseMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDriverSafePromise" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="period" property="period" jdbcType="VARCHAR" />
    <result column="agent" property="agent" jdbcType="VARCHAR" />
    <result column="attachment_url" property="attachmentUrl" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.logistics.tms.entity.TDriverSafePromise" extends="BaseResultMap" >
    <result column="content" property="content" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, title, period, agent, attachment_url, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <sql id="Blob_Column_List" >
    content
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from t_driver_safe_promise
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from t_driver_safe_promise
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDriverSafePromise" >
    insert into t_driver_safe_promise (id, title, period, 
      agent, attachment_url, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid, content)
    values (#{id,jdbcType=INTEGER}, #{title,jdbcType=VARCHAR}, #{period,jdbcType=VARCHAR}, 
      #{agent,jdbcType=VARCHAR}, #{attachmentUrl,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER}, #{content,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDriverSafePromise" useGeneratedKeys="true" keyProperty="id">
    insert into t_driver_safe_promise
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="title != null" >
        title,
      </if>
      <if test="period != null" >
        period,
      </if>
      <if test="agent != null" >
        agent,
      </if>
      <if test="attachmentUrl != null" >
        attachment_url,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
      <if test="content != null" >
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="title != null" >
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="period != null" >
        #{period,jdbcType=VARCHAR},
      </if>
      <if test="agent != null" >
        #{agent,jdbcType=VARCHAR},
      </if>
      <if test="attachmentUrl != null" >
        #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
      <if test="content != null" >
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDriverSafePromise" >
    update t_driver_safe_promise
    <set >
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="period != null" >
        period = #{period,jdbcType=VARCHAR},
      </if>
      <if test="agent != null" >
        agent = #{agent,jdbcType=VARCHAR},
      </if>
      <if test="attachmentUrl != null" >
        attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.logistics.tms.entity.TDriverSafePromise" >
    update t_driver_safe_promise
    set title = #{title,jdbcType=VARCHAR},
      period = #{period,jdbcType=VARCHAR},
      agent = #{agent,jdbcType=VARCHAR},
      attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDriverSafePromise" >
    update t_driver_safe_promise
    set title = #{title,jdbcType=VARCHAR},
      period = #{period,jdbcType=VARCHAR},
      agent = #{agent,jdbcType=VARCHAR},
      attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>