<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TSysConfigMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TSysConfig" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="group_code" property="groupCode" jdbcType="VARCHAR" />
    <result column="group_desc" property="groupDesc" jdbcType="VARCHAR" />
    <result column="config_key" property="configKey" jdbcType="VARCHAR" />
    <result column="config_value" property="configValue" jdbcType="OTHER" />
    <result column="config_desc" property="configDesc" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, group_code, group_desc, config_key, config_value, config_desc, remark, created_by, 
    created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_sys_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_sys_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TSysConfig" >
    insert into t_sys_config (id, group_code, group_desc, 
      config_key, config_value, config_desc, 
      remark, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{groupCode,jdbcType=VARCHAR}, #{groupDesc,jdbcType=VARCHAR}, 
      #{configKey,jdbcType=VARCHAR}, #{configValue,jdbcType=OTHER}, #{configDesc,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TSysConfig" >
    insert into t_sys_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="groupCode != null" >
        group_code,
      </if>
      <if test="groupDesc != null" >
        group_desc,
      </if>
      <if test="configKey != null" >
        config_key,
      </if>
      <if test="configValue != null" >
        config_value,
      </if>
      <if test="configDesc != null" >
        config_desc,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="groupCode != null" >
        #{groupCode,jdbcType=VARCHAR},
      </if>
      <if test="groupDesc != null" >
        #{groupDesc,jdbcType=VARCHAR},
      </if>
      <if test="configKey != null" >
        #{configKey,jdbcType=VARCHAR},
      </if>
      <if test="configValue != null" >
        #{configValue,jdbcType=OTHER},
      </if>
      <if test="configDesc != null" >
        #{configDesc,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TSysConfig" >
    update t_sys_config
    <set >
      <if test="groupCode != null" >
        group_code = #{groupCode,jdbcType=VARCHAR},
      </if>
      <if test="groupDesc != null" >
        group_desc = #{groupDesc,jdbcType=VARCHAR},
      </if>
      <if test="configKey != null" >
        config_key = #{configKey,jdbcType=VARCHAR},
      </if>
      <if test="configValue != null" >
        config_value = #{configValue,jdbcType=OTHER},
      </if>
      <if test="configDesc != null" >
        config_desc = #{configDesc,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TSysConfig" >
    update t_sys_config
    set group_code = #{groupCode,jdbcType=VARCHAR},
      group_desc = #{groupDesc,jdbcType=VARCHAR},
      config_key = #{configKey,jdbcType=VARCHAR},
      config_value = #{configValue,jdbcType=OTHER},
      config_desc = #{configDesc,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>