package com.logistics.appapi.controller.workordercenter.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/14
 */
@Data
public class ReportWorkExceptionRequestDto {

	@ApiModelProperty(value = "工单id,重新提交需要填写")
	private String workOrderId;

	@ApiModelProperty(value = "运单Id", required = true)
	@NotBlank(message = "运单id不能为空")
	private String carrierOrderId;

	@ApiModelProperty(value = "司机是否到达现场：0 否，1 是", required = true)
	@NotBlank(message = "请选择是否到达现场")
	private String isArriveScene;

	@ApiModelProperty(value = "定位抬头")
	private String addressHead;

	@ApiModelProperty(value = "定位详细地址")
	private String addressDetail;

	@ApiModelProperty(value = "异常类型（一级）：10 联系不上客户，20 不想还盘，30 不可抗力，40 重复下单", required = true)
	@NotBlank(message = "请选择异常问题类型")
	private String anomalyTypeOne;

	@ApiModelProperty(value = "异常类型（二级）：" +
			"101 电话空号 102 无人接听 103联系方式错误;" +
			"201 客户占用 202 客户不协助装车 203 贸易商流向错误 204 装车费用确认;" +
			"301 暴风暴雨 302 当地修路;" +
			"401 重复下单;", required = true)
	@NotBlank(message = "请选择异常问题类型")
	private String anomalyTypeTwo;

	@ApiModelProperty(value = "核验联系方式: 1 无误，2 有误", required = true)
	@NotBlank(message = "请选择联系人是否有误")
	private String checkContact;

	@ApiModelProperty(value = "联系人姓名", required = true)
	@NotBlank(message = "请填写联系人姓名")
	@Length(min = 2, max = 20, message = "请输入正确的联系人姓名")
	private String contactName;

	@ApiModelProperty(value = "联系人联系方式", required = true)
	@NotBlank(message = "请填写联系方式")
	@Pattern(regexp = "(^\\d{11}$)|(^(0[0-9]{2,3}-)?([2-9][0-9]{6,7})$)", message = "请填写正确的联系方式")
	private String contactTelephone;

	@ApiModelProperty(value = "备注")
	@Length(max = 100, message = " 备注不能超过100个字符")
	private String remark;
}
