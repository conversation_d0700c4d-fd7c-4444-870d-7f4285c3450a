package com.logistics.management.webapi.client.reservation;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.reservation.hystrix.ReservationClientHystrix;
import com.logistics.management.webapi.client.reservation.request.ReservationOrderSearchListForManagementWebReqModel;
import com.logistics.management.webapi.client.reservation.response.ReservationOrderSearchListForManagementWebResModel;
import com.logistics.management.webapi.client.reserveapply.hystrix.DriverReserveApplyClientHystrix;
import com.logistics.management.webapi.client.reserveapply.request.*;
import com.logistics.management.webapi.client.reserveapply.response.ReserveApplyDetailResponseModel;
import com.logistics.management.webapi.client.reserveapply.response.ReserveApplyListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = FeignClientName.TMS_SERVICES,
        fallback = ReservationClientHystrix.class)
public interface ReservationClient {

    @ApiOperation(value = "备用金申请列表查询")
    @PostMapping("/service/reservationOrder/searchListForManagementWeb")
    Result<PageInfo<ReservationOrderSearchListForManagementWebResModel>> searchListForManagementWeb(@RequestBody ReservationOrderSearchListForManagementWebReqModel requestModel);

}
