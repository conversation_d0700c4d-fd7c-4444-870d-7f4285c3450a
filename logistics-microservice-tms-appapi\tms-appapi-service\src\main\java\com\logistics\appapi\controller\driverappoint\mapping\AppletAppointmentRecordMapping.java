package com.logistics.appapi.controller.driverappoint.mapping;

import com.logistics.appapi.base.enums.CompanyTypeEnum;
import com.logistics.appapi.client.driverappoint.response.SearchDrierAppointDetailResponseModel;
import com.logistics.appapi.controller.driverappoint.response.SearchDrierAppointDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

public class AppletAppointmentRecordMapping extends MapperMapping<SearchDrierAppointDetailResponseModel, SearchDrierAppointDetailResponseDto> {

    @Override
    public void configure() {
        SearchDrierAppointDetailResponseModel source = getSource();
        SearchDrierAppointDetailResponseDto destination = getDestination();
        if(CompanyTypeEnum.COMPANY.getKey().equals(source.getBusinessType())){
            destination.setCustomerName(source.getCustomerName());
        }else if(CompanyTypeEnum.PERSONAL.getKey().equals(source.getBusinessType())){
            destination.setCustomerName(source.getCustomerUserName()+" "+source.getCustomerUserMobile());
        }
        destination.setConsignor(source.getConsignorName()+" "+source.getConsignorMobile());
        if(StringUtils.isNotBlank(source.getLoadWarehouse())){
            destination.setLoadDetailAddress("【"+source.getLoadWarehouse()+"】"+source.getLoadProvinceName()+source.getLoadCityName()+source.getLoadAreaName()+source.getLoadDetailAddress());
        }else {
            destination.setLoadDetailAddress(source.getLoadProvinceName()+source.getLoadCityName()+source.getLoadAreaName()+source.getLoadDetailAddress());
        }
        destination.setGoodsAmountTotal(source.getGoodsAmountTotal().stripTrailingZeros().toPlainString());
        destination.setGoodsPriceTotal(source.getGoodsPriceTotal().stripTrailingZeros().toPlainString());
    }
}
