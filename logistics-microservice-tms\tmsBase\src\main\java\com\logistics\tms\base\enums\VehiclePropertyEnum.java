package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2018/9/27 15:12
 */
public enum VehiclePropertyEnum {
    PROPERTY_ZERO(0,"全部车辆"),
    OWN_VEHICLE(1, "自主车辆"),
    EXTERNAL_VEHICLE(2, "外部车辆"),
    AFFILIATION_VEHICLE(3, "自营车辆"),
    ;

    private Integer key;
    private String value;

    VehiclePropertyEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
