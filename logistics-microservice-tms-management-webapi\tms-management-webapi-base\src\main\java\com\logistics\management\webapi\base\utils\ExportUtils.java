package com.logistics.management.webapi.base.utils;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.yelo.tools.utils.YeloExcelUtils;
import com.yelo.tray.core.exception.BizException;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public class ExportUtils {

    public static void exportByYeloExcel(HttpServletResponse response, List dataList, Class clazz, String fileName) {
        try {
            YeloExcelUtils.doExportMoreThreadByOneSheet(response, dataList, false, clazz, fileName);
        } catch (Exception e) {
            return;
        }
    }
}
