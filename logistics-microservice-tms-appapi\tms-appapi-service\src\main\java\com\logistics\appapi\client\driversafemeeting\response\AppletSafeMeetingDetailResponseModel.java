package com.logistics.appapi.client.driversafemeeting.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/11/8 10:26
 */
@Data
public class AppletSafeMeetingDetailResponseModel {
    @ApiModelProperty("学习例会关系id")
    private Long safeMeetingRelationId;
    @ApiModelProperty("学习状态：0未学习，1已学习")
    private Integer status;
    @ApiModelProperty(value = "学习月份")
    private String period;
    @ApiModelProperty(value = "创建人（组织人）")
    private String createdBy;
    @ApiModelProperty(value = "创建时间（发布时间）")
    private Date createdTime;
    @ApiModelProperty(value = "学习标题")
    private String title;
    @ApiModelProperty(value = "学习内容")
    private String content;
    @ApiModelProperty(value = "驾驶员图片")
    private String staffDriverImageUrl;
    @ApiModelProperty(value = "签字图片")
    private String signImageUrl;
}
