package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.controller.staff.response.ContinueLearningListResponseModel;
import com.logistics.tms.entity.TStaffDriverContinueLearningRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface TStaffDriverContinueLearningRecordMapper extends BaseMapper<TStaffDriverContinueLearningRecord>{

    List<ContinueLearningListResponseModel>  continueLearningList(@Param("staffId") Long staffId);

    Map<String,String> getDueContinueLearningCount(@Param("companyCarrierId") Long companyCarrierId,@Param("remindDays") Integer remindDays);

    int batchUpdate(@Param("list") List<TStaffDriverContinueLearningRecord> list);

    int selectRecordByStaffIdAndValidDate(@Param("staffId") Long staffId, @Param("validDate") Date date);

    List<TStaffDriverContinueLearningRecord> getTopContinueLearningByStaffId(@Param("staffIds") String staffIds);

}