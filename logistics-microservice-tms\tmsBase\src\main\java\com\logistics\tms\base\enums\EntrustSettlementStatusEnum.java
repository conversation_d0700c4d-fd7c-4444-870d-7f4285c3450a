/**
 * @author: wjf
 * @date: 2019/10/11 19:07
 */
package com.logistics.tms.base.enums;

public enum EntrustSettlementStatusEnum {

    NOT_PAYMENT(0, "未收款"),
    PAYMENT(1, "已收款"),
    ;

    private Integer key;
    private String value;

    EntrustSettlementStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
