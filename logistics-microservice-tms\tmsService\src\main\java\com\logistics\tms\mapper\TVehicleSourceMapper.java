package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.controller.website.vehicle.request.VehicleSourceListRequestModel;
import com.logistics.tms.controller.website.vehicle.response.VehicleSourceListResponseModel;
import com.logistics.tms.entity.TVehicleSource;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TVehicleSourceMapper extends BaseMapper<TVehicleSource> {

    TVehicleSource getByVehicleNo(@Param("vehicleNo")String vehicleNo);

    List<VehicleSourceListResponseModel> vehicleSourceList(@Param("params") VehicleSourceListRequestModel requestModel);
}