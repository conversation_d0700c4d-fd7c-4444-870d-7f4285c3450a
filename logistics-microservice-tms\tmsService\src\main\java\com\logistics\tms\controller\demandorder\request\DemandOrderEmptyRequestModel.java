package com.logistics.tms.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2021/10/21 13:52
 */
@Data
public class DemandOrderEmptyRequestModel {
    @ApiModelProperty("委托单id")
    private String demandIds;
    @ApiModelProperty("客户名称")
    private String customerName;
    @ApiModelProperty("放空原因类型：2 客户原因，3 不可抗力，4 物流原因，5 平台问题")
    private Integer objectionType;
    @ApiModelProperty("放空描述")
    private String objectionReason;
}
