package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2022/8/4 17:28
 */
public class ExportVehicleOilCard {
    private ExportVehicleOilCard() {

    }

    private static final Map<String, String> EXPORT_VEHICLE_OIL_CARD_MAP;

    static {
        EXPORT_VEHICLE_OIL_CARD_MAP = new LinkedHashMap<>();
        EXPORT_VEHICLE_OIL_CARD_MAP.put("卡号", "cardNumber");
        EXPORT_VEHICLE_OIL_CARD_MAP.put("绑定状态", "statusLabel");
        EXPORT_VEHICLE_OIL_CARD_MAP.put("机构", "vehicleProperty");
        EXPORT_VEHICLE_OIL_CARD_MAP.put("车牌号", "vehicleNo");
        EXPORT_VEHICLE_OIL_CARD_MAP.put("备注", "remark");
        EXPORT_VEHICLE_OIL_CARD_MAP.put("操作时间", "lastModifiedTime");
        EXPORT_VEHICLE_OIL_CARD_MAP.put("操作人", "lastModifiedBy");
    }

    public static Map<String, String> getExportVehicleOilCardMap() {
        return EXPORT_VEHICLE_OIL_CARD_MAP;
    }
}
