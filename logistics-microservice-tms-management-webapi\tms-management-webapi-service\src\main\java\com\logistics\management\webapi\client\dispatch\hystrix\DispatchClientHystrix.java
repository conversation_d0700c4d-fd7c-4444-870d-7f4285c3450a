package com.logistics.management.webapi.client.dispatch.hystrix;

import com.logistics.management.webapi.client.dispatch.DispatchClient;
import com.logistics.management.webapi.client.dispatch.request.*;
import com.logistics.management.webapi.client.dispatch.response.DemandOrderSpecialDispatchDetailResponseModel;
import com.logistics.management.webapi.client.dispatch.response.SearchCanJoinShippingOrderRespModel;
import com.logistics.management.webapi.client.dispatch.response.SearchSpecialDispatchIfMatchFreightRespModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;

@Component
public class DispatchClientHystrix implements DispatchClient {

    @Override
    public Result<Boolean> dispatchVehicle(DispatchRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<DemandOrderSpecialDispatchDetailResponseModel> specialDispatchDetail(DemandOrderSpecialDispatchDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> specialDispatchVehicle(SpecialDispatchVehicleRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SearchSpecialDispatchIfMatchFreightRespModel> searchSpecialDispatchIfMatchFreight(@Valid SearchSpecialDispatchIfMatchFreightReqModel requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchCanJoinShippingOrderRespModel>> searchCanJoinShippingOrder(@Valid SearchCanJoinShippingOrderRequestModel requestModel) {
        return Result.timeout();
    }
}
