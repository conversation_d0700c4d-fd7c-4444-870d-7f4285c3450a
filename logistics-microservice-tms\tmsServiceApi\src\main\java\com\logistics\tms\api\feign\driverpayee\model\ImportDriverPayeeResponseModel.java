package com.logistics.tms.api.feign.driverpayee.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/6/3 11:23
 */
@Data
public class ImportDriverPayeeResponseModel {
    @ApiModelProperty("失败数量")
    private Integer numberFailures = 0;
    @ApiModelProperty("成功数量")
    private Integer numberSuccessful = 0;

    public void failPlus(){
        numberFailures = numberFailures+1;
    }

    public void successPlus(){
        numberSuccessful = numberSuccessful+1;
    }


}
