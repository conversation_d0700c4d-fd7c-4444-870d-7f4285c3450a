package com.logistics.tms.biz.website.vehicle;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.website.vehicle.request.PublishVehicleSourceRequestModel;
import com.logistics.tms.controller.website.vehicle.request.VehicleSourceListRequestModel;
import com.logistics.tms.controller.website.vehicle.response.VehicleSourceListResponseModel;
import com.logistics.tms.entity.TVehicleSource;
import com.logistics.tms.mapper.TVehicleSourceMapper;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/31 16:47
 */
@Service
public class VehicleSourceBiz {

    @Resource
    private TVehicleSourceMapper vehicleSourceMapper;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 发布车源
     * @param requestModel
     */
    @Transactional
    public void publishVehicleSource(PublishVehicleSourceRequestModel requestModel){
        TVehicleSource tVehicleSource = vehicleSourceMapper.getByVehicleNo(requestModel.getVehicleNo());
        if (tVehicleSource != null){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SOURCE_EXIST);
        }
        TVehicleSource vehicleSource = MapperUtils.mapper(requestModel, TVehicleSource.class);
        commonBiz.setBaseEntityAdd(vehicleSource, CommonConstant.WEBSITE);
        vehicleSourceMapper.insertSelective(vehicleSource);
    }

    /**
     * 车源列表
     * @param requestModel
     * @return
     */
    public List<VehicleSourceListResponseModel> vehicleSourceList(VehicleSourceListRequestModel requestModel){
        requestModel.enablePaging();
        return vehicleSourceMapper.vehicleSourceList(requestModel);
    }
}
