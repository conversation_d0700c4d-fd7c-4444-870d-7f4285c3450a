package com.logistics.tms.api.feign.freight.model;
import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * 运价地址
 * @Author: sj
 * @Date: 2019/12/24 13:11
 */
@Data
public class SearchFreightAddressRequestModel extends AbstractPageForm<SearchFreightAddressRequestModel>{
    @ApiModelProperty("运价Id")
    private Long freightId;
    @ApiModelProperty("仓库名称")
    private String warehouseName;
    @ApiModelProperty("发货地址")
    private String fromAddress;
    @ApiModelProperty("卸货")
    private String toAddress;
    @ApiModelProperty("计价类型:1 基价 2 一日游 ")
    private Integer calcType;
    @ApiModelProperty("选择性导出id拼接")
    private String freightAddressIds;
}
