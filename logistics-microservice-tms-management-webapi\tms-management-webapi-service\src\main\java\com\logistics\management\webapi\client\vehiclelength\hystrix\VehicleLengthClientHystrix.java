package com.logistics.management.webapi.client.vehiclelength.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.vehiclelength.VehicleLengthClient;
import com.logistics.management.webapi.client.vehiclelength.request.AddOrUpdateVehicleLengthRequestModel;
import com.logistics.management.webapi.client.vehiclelength.request.SearchVehicleLengthListRequestModel;
import com.logistics.management.webapi.client.vehiclelength.request.VehicleLengthDetailRequestModel;
import com.logistics.management.webapi.client.vehiclelength.response.SearchVehicleLengthListResponseModel;
import com.logistics.management.webapi.client.vehiclelength.response.SelectVehicleLengthListResponseModel;
import com.logistics.management.webapi.client.vehiclelength.response.VehicleLengthDetailResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/4/29 9:13
 */
@Component
public class VehicleLengthClientHystrix implements VehicleLengthClient {
    @Override
    public Result<PageInfo<SearchVehicleLengthListResponseModel>> searchVehicleLengthList(SearchVehicleLengthListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<VehicleLengthDetailResponseModel> vehicleLengthDetail(VehicleLengthDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addOrUpdateVehicleLength(AddOrUpdateVehicleLengthRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> delVehicleLength(VehicleLengthDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SelectVehicleLengthListResponseModel>> selectVehicleLengthList() {
        return Result.timeout();
    }
}
