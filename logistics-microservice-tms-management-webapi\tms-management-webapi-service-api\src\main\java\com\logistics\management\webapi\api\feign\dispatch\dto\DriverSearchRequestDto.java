package com.logistics.management.webapi.api.feign.dispatch.dto;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class DriverSearchRequestDto  extends AbstractPageForm<DriverSearchRequestDto> {

    @ApiModelProperty(value = "司机机构 1 自主，2 外部，3 自营")
    private String staffProperty;

    @ApiModelProperty(value = "车主ID",required = true)
    @NotBlank(message = "车主id不能为空")
    private String companyCarrierId;

    @ApiModelProperty("是否我司: 1:我司 2:其他车主")
    private String isOurCompany;

    @ApiModelProperty("司机名字")
    private String driverNameAndPhone;
}
