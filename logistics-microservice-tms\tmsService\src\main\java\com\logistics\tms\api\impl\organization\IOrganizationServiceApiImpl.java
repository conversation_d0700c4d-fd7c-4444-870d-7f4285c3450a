package com.logistics.tms.api.impl.organization;

import com.logistics.tms.api.feign.organization.IOrganizationServiceApi;
import com.logistics.tms.api.feign.organization.model.IOrganizationNameResponseModel;
import com.logistics.tms.client.BasicDataClient;
import com.yelo.basicdata.api.feign.organization.model.OrganizationNameResponseModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.base.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class IOrganizationServiceApiImpl implements IOrganizationServiceApi {

    private final BasicDataClient basicDataClient;

    /**
     * 查询所有部门（列表筛选条件接口）
     * @return
     */
    @Override
    public Result<List<IOrganizationNameResponseModel>> getAllOrgForHierarchy() {
        List<OrganizationNameResponseModel> orgForHierarchyList = basicDataClient.getAllOrgForHierarchy();
        return Result.success(MapperUtils.mapper(orgForHierarchyList, IOrganizationNameResponseModel.class));
    }
}
