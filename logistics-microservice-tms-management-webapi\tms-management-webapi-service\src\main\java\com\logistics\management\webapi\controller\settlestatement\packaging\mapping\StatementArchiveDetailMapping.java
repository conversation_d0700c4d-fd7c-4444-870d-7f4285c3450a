package com.logistics.management.webapi.controller.settlestatement.packaging.mapping;

import com.logistics.management.webapi.client.settlestatement.packaging.response.StatementArchiveDetailResponseModel;
import com.logistics.management.webapi.controller.settlestatement.packaging.response.StatementArchiveDetailResponseDto;
import com.logistics.management.webapi.controller.settlestatement.packaging.response.StatementArchiveDetailTicketResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2022/11/15 13:30
 */
public class StatementArchiveDetailMapping extends MapperMapping<StatementArchiveDetailResponseModel, StatementArchiveDetailResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;

    public StatementArchiveDetailMapping(String imagePrefix, Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap = imageMap;
    }

    @Override
    public void configure() {
        StatementArchiveDetailResponseModel source = getSource();
        StatementArchiveDetailResponseDto destination = getDestination();

        //图片路径转换
        List<StatementArchiveDetailTicketResponseDto> archiveTicketList = new ArrayList<>();
        if (ListUtils.isNotEmpty(source.getArchiveTicketList())){
            StatementArchiveDetailTicketResponseDto ticketResponseDto;
            for (String path : source.getArchiveTicketList()) {
                ticketResponseDto = new StatementArchiveDetailTicketResponseDto();
                ticketResponseDto.setSrc(imagePrefix + imageMap.get(path));
                ticketResponseDto.setRelativePath(path);
                archiveTicketList.add(ticketResponseDto);
            }
        }
        destination.setArchiveTicketList(archiveTicketList);
    }
}
