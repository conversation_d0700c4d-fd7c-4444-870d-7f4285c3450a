<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TDemandOrderEventsMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDemandOrderEvents">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="demand_order_id" jdbcType="BIGINT" property="demandOrderId" />
    <result column="company_carrier_id" jdbcType="BIGINT" property="companyCarrierId" />
    <result column="event" jdbcType="INTEGER" property="event" />
    <result column="event_desc" jdbcType="VARCHAR" property="eventDesc" />
    <result column="event_time" jdbcType="TIMESTAMP" property="eventTime" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, demand_order_id, company_carrier_id, event, event_desc, event_time, operator_name, 
    operate_time, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_demand_order_events
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_demand_order_events
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDemandOrderEvents">
    insert into t_demand_order_events (id, demand_order_id, company_carrier_id, 
      event, event_desc, event_time, 
      operator_name, operate_time, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{demandOrderId,jdbcType=BIGINT}, #{companyCarrierId,jdbcType=BIGINT}, 
      #{event,jdbcType=INTEGER}, #{eventDesc,jdbcType=VARCHAR}, #{eventTime,jdbcType=TIMESTAMP}, 
      #{operatorName,jdbcType=VARCHAR}, #{operateTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDemandOrderEvents">
    insert into t_demand_order_events
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="demandOrderId != null">
        demand_order_id,
      </if>
      <if test="companyCarrierId != null">
        company_carrier_id,
      </if>
      <if test="event != null">
        event,
      </if>
      <if test="eventDesc != null">
        event_desc,
      </if>
      <if test="eventTime != null">
        event_time,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="operateTime != null">
        operate_time,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="demandOrderId != null">
        #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierId != null">
        #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="event != null">
        #{event,jdbcType=INTEGER},
      </if>
      <if test="eventDesc != null">
        #{eventDesc,jdbcType=VARCHAR},
      </if>
      <if test="eventTime != null">
        #{eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null">
        #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDemandOrderEvents">
    update t_demand_order_events
    <set>
      <if test="demandOrderId != null">
        demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierId != null">
        company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="event != null">
        event = #{event,jdbcType=INTEGER},
      </if>
      <if test="eventDesc != null">
        event_desc = #{eventDesc,jdbcType=VARCHAR},
      </if>
      <if test="eventTime != null">
        event_time = #{eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null">
        operate_time = #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDemandOrderEvents">
    update t_demand_order_events
    set demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      event = #{event,jdbcType=INTEGER},
      event_desc = #{eventDesc,jdbcType=VARCHAR},
      event_time = #{eventTime,jdbcType=TIMESTAMP},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      operate_time = #{operateTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>