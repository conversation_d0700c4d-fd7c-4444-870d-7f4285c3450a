package com.logistics.management.webapi.client.settlestatement.tradition.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TraditionStatementCancelRequestModel {

    @ApiModelProperty(value = "对账单id")
    private Long settleStatementId;

    @ApiModelProperty("撤销对账原因,仅撤销时用")
    private String remark;

    @ApiModelProperty("来源：1 后台，2 前台")
    private Integer source;
}
