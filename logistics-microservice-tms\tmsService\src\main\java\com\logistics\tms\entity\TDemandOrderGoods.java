package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2022/08/20
*/
@Data
public class TDemandOrderGoods extends BaseEntity {
    /**
    * 需求单ID
    */
    @ApiModelProperty("需求单ID")
    private Long demandOrderId;

    /**
    * sku编号
    */
    @ApiModelProperty("sku编号")
    private String skuCode;

    /**
    * 货物品名
    */
    @ApiModelProperty("货物品名")
    private String goodsName;

    /**
    * 大类名称
    */
    @ApiModelProperty("大类名称")
    private String categoryName;

    /**
    * 长
    */
    @ApiModelProperty("长")
    private Integer length;

    /**
    * 宽
    */
    @ApiModelProperty("宽")
    private Integer width;

    /**
    * 高
    */
    @ApiModelProperty("高")
    private Integer height;

    /**
    * 规格
    */
    @ApiModelProperty("规格")
    private String goodsSize;

    /**
    * 货物数量
    */
    @ApiModelProperty("货物数量")
    private BigDecimal goodsAmount;

    /**
    * 已安排数量
    */
    @ApiModelProperty("已安排数量")
    private BigDecimal arrangedAmount;

    /**
    * 未安排数量
    */
    @ApiModelProperty("未安排数量")
    private BigDecimal notArrangedAmount;

    /**
    * 退回数量
    */
    @ApiModelProperty("退回数量")
    private BigDecimal backAmount;
}