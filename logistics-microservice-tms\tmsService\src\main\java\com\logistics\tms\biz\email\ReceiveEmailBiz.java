package com.logistics.tms.biz.email;

import com.sun.mail.imap.IMAPFolder;
import com.sun.mail.imap.IMAPStore;
import com.yelo.tools.utils.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.mail.*;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * @author: wjf
 * @date: 2019/2/22 17:14
 */
@Service
@Slf4j
public class ReceiveEmailBiz {

    @Value("${spring.mail.host}")
    private String host;
    @Value("${spring.mail.username}")
    private String username;
    @Value("${spring.mail.password}")
    private String password;

    @Value("${spring.profiles.active}")
    private String env;

    private static final String FOLDER_NAME_DEV = "demandOrderMenuDev";
    private static final String FOLDER_NAME_TEST = "demandOrderMenuTest";
    private static final String FOLDER_NAME_UAT = "demandOrderMenuUat";
    private static final String FOLDER_NAME_STG = "demandOrderMenuStg";
    private static final String FOLDER_NAME_PROD = "demandOrderMenu";

    /**
     * 接收邮件
     * @return 附件解析后的内容
     */
    public List<String> receiveEmail(){
        //根据环境读取不同文件夹内的邮件
        String folderName;
        if ("dev".equals(env) || "local".equals(env) || "mit".equals(env)){
            folderName = FOLDER_NAME_DEV;
        }else if ("test".equals(env)){
            folderName = FOLDER_NAME_TEST;
        }else if ("uat".equals(env)){
            folderName = FOLDER_NAME_UAT;
        }else if ("stg".equals(env)){
            folderName = FOLDER_NAME_STG;
        }else if ("prod".equals(env)){
            folderName = FOLDER_NAME_PROD;
        }else{
            return new ArrayList<>();
        }

        //连接邮箱
        Properties props = new Properties();
        props.setProperty("mail.store.protocol", "imap");
        props.setProperty("mail.imap.host", host);

        Session session = Session.getInstance(props);
        IMAPStore store;
        IMAPFolder folder;
        List<String> list = new ArrayList<>();
        try {
            store = (IMAPStore) session.getStore("imap");
            store.connect(host, username, password);
            //获得收件箱
            folder = (IMAPFolder) store.getFolder(folderName);
            //打开收件箱 Folder.READ_ONLY：只读权限，Folder.READ_WRITE：可读可写（可以修改邮件的状态）
            folder.open(Folder.READ_WRITE);

            log.info("未读邮件数: " + folder.getUnreadMessageCount());
            log.info("邮件总数: " + folder.getMessageCount());

            // 得到收件箱中的所有邮件,并解析
            Message[] messages = folder.getMessages();
            list = parseMessage(messages);

            folder.close();
            store.close();
        }catch (Exception e){
            log.info(e.getMessage(),e);
        }
        return list;
    }

    /**
     * 解析邮件
     * @param messages 要解析的邮件列表
     * @return 附件解析后的内容
     * @throws Exception
     */
    public List<String> parseMessage(Message ...messages) throws MessagingException,IOException {
        if (messages == null || messages.length < 1) {
            throw new MessagingException("未找到要解析的邮件!");
        }
        // 解析所有邮件
        List<String> list = null;
        List<String> contentList = new ArrayList<>();
        for (int i = 0, count = messages.length; i < count; i++) {
            MimeMessage msg = (MimeMessage) messages[i];
            if (!isSeen(msg) && isContainAttachment(msg)) {//未读且有附件
                list = parseAttachment(msg);//解析附件
                if (ListUtils.isNotEmpty(list)){
                    contentList.addAll(list);
                }
            }
        }
        return contentList;
    }

    /**
     * 读取所有附件
     * @param part 邮件中多个组合体中的其中一个组合体
     * @return 单封邮件里附件解析后的内容
     * @throws MessagingException
     * @throws IOException
     */
    private static final String MEME_TYPE = "multipart/*";
    public List<String> parseAttachment(Part part) throws MessagingException, IOException {
        List<String> list = new ArrayList<>();
        if (part.isMimeType(MEME_TYPE)) {
            Multipart multipart = (Multipart) part.getContent();
            //复杂体邮件包含多个邮件体
            int partCount = multipart.getCount();
            for (int i = 0; i < partCount; i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                String disp = bodyPart.getDisposition();
                if (disp != null && (disp.equalsIgnoreCase(Part.ATTACHMENT) || disp.equalsIgnoreCase(Part.INLINE))) {
                    String fileName = decodeText(bodyPart.getFileName());
                    String suffix = fileName.substring(fileName.lastIndexOf('.')+1);
                    if ("pdf".equals(suffix) || "PDF".equals(suffix)) {
                        String content = readFdf(bodyPart.getInputStream());
                        list.add(content);
                    }
                } else if (bodyPart.isMimeType(MEME_TYPE)) {
                    parseAttachment(bodyPart);
                } else {
                    String contentType = bodyPart.getContentType();
                    if (contentType.indexOf("name") != -1 || contentType.indexOf("application") != -1) {
                        String fileName = decodeText(bodyPart.getFileName());
                        String suffix = fileName.substring(fileName.lastIndexOf('.')+1);
                        if ("pdf".equals(suffix) || "PDF".equals(suffix)) {
                            String content = readFdf(bodyPart.getInputStream());
                            list.add(content);
                        }
                    }
                }
            }
        } else if (part.isMimeType("message/rfc822")) {
            parseAttachment((Part) part.getContent());
        }
        return list;
    }

    /**
     * 解析pdf
     * @param input pdf文件流
     * @return pdf解析成文本内容
     */
    public String readFdf(InputStream input) {
        PDDocument document = null;
        String result = "";
        try {
            document = PDDocument.load(input);
            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setSortByPosition(true);//设置是否排序
            result = stripper.getText(document);
            if (document != null) {
                document.close();
            }
        }catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return result;
    }

    /**
     * 判断邮件中是否包含附件
     * @param part 邮件中多个组合体中的其中一个组合体
     * @return 邮件中存在附件返回true，不存在返回false
     * @throws MessagingException
     * @throws IOException
     */
    public boolean isContainAttachment(Part part) throws MessagingException, IOException {
        boolean flag = false;
        if (part.isMimeType(MEME_TYPE)) {
            MimeMultipart multipart = (MimeMultipart) part.getContent();
            int partCount = multipart.getCount();
            for (int i = 0; i < partCount; i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                String disp = bodyPart.getDisposition();
                if (disp != null && (disp.equalsIgnoreCase(Part.ATTACHMENT) || disp.equalsIgnoreCase(Part.INLINE))) {
                    flag = true;
                } else if (bodyPart.isMimeType(MEME_TYPE)) {
                    flag = isContainAttachment(bodyPart);
                } else {
                    String contentType = bodyPart.getContentType();
                    if (contentType.indexOf("application") != -1) {
                        flag = true;
                    }
                    if (contentType.indexOf("name") != -1) {
                        flag = true;
                    }
                }
                if (flag) break;
            }
        } else if (part.isMimeType("message/rfc822")) {
            flag = isContainAttachment((Part)part.getContent());
        }
        return flag;
    }

    /**
     * 判断邮件是否已读
     * @param msg 邮件内容
     * @return 如果邮件已读返回true,否则返回false
     * @throws MessagingException
     */
    public boolean isSeen(MimeMessage msg) throws MessagingException {
        return msg.getFlags().contains(Flags.Flag.SEEN);
    }

    /**
     * 文本解码
     * @param encodeText 解码MimeUtility.encodeText(String text)方法编码后的文本
     * @return 解码后的文本
     * @throws UnsupportedEncodingException
     */
    public String decodeText(String encodeText) throws UnsupportedEncodingException {
        if (encodeText == null || "".equals(encodeText)) {
            return "";
        } else {
            return MimeUtility.decodeText(encodeText);
        }
    }
}
