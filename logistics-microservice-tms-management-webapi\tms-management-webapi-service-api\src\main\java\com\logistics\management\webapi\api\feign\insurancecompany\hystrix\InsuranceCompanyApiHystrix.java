package com.logistics.management.webapi.api.feign.insurancecompany.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.insurancecompany.InsuranceCompanyApi;
import com.logistics.management.webapi.api.feign.insurancecompany.dto.*;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/5/29 14:14
 */
@Component
public class InsuranceCompanyApiHystrix implements InsuranceCompanyApi {
    @Override
    public Result<PageInfo<InsuranceCompanyListResponseDto>> searchInsuranceCompanyList(InsuranceCompanyListRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<InsuranceCompanyDetailResponseDto> getDetail(InsuranceCompanyDetailRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> saveOrModifyInsuranceCompany(SaveOrModifyInsuranceCompanyRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> enableOrDisable(EnableInsuranceCompanyRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void export(InsuranceCompanyListRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }

    @Override
    public Result<ImportInsuranceCompanyResponseDto> importInsuranceCompany(MultipartFile file, HttpServletRequest request) {
        return Result.timeout();
    }

    @Override
    public Result<List<FuzzyQueryInsuranceCompanyListResponseDto>> fuzzyQueryInsuranceCompanyByName(FuzzyQueryInsuranceCompanyRequestDto requestDto) {
        return Result.timeout();
    }
}
