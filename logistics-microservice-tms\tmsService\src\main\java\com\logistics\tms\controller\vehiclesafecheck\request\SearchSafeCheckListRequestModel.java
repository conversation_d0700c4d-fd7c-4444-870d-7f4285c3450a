package com.logistics.tms.controller.vehiclesafecheck.request;
import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 列表
 * @Author: sj
 * @Date: 2019/11/6 11:58
 */
@Data
public class SearchSafeCheckListRequestModel extends AbstractPageForm<SearchSafeCheckListRequestModel> {
    @ApiModelProperty("状态 0未检查、10待确认、20待整改、30已整改、40检查完成")
    private Integer status;
    @ApiModelProperty("检查开始时间")
    private String periodStart;
    @ApiModelProperty("检查结束时间")
    private String periodEnd;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("挂车车牌号")
    private String trailerVehicleNo;
    @ApiModelProperty("司机姓名")
    private String staffName;
    @ApiModelProperty("司机电话")
    private String staffMobile;
    @ApiModelProperty("选择导出使用")
    private String ids;

    @ApiModelProperty("车辆机构：1 自主 2 外部 3 自营")
    private Integer vehicleProperty;
}
