package com.logistics.appapi.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author：wjf
 * @date：2021/4/9 14:01
 */
@Data
public class SearchDriverReconciliationListResponseDto {
    @ApiModelProperty("结算id")
    private String vehicleSettlementId="";
    @ApiModelProperty("结算状态：空 全部，2 待确认，3 待处理，4 待结清,5 部分结清，6 已结清")
    private String status="";
    @ApiModelProperty("结算状态")
    private String statusLabel="";
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty("年（yyyy）")
    private String settlementYear="";
    @ApiModelProperty("月份（MM）")
    private String settlementMonth="";
    @ApiModelProperty("运单数量")
    private String carrierOrderCount="";
    @ApiModelProperty("调整费用")
    private String adjustFee="";
    @ApiModelProperty("应收费用")
    private String actualExpensesPayable="";
}
