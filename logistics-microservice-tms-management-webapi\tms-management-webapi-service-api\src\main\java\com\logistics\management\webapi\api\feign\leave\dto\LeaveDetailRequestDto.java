package com.logistics.management.webapi.api.feign.leave.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 请假申请详情请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/15
 */
@Data
public class LeaveDetailRequestDto {

	//请假申请ID
	@ApiModelProperty(value = "请假申请ID", required = true)
	@NotBlank(message = "请选择要撤销的请假记录")
	private String leaveApplyId;
}
