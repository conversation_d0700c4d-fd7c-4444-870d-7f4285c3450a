package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @author: wjf
 * @date: 2021/9/24 17:54
 */
@Data
public class BatchPublishRequestDto {

    @ApiModelProperty(value = "需求单列表", required = true)
    @NotNull(message = "需求单信息不能为空")
    @Valid
    @Size(max = 10, message = "最多发布10条数据")
    private List<BatchPublishDemandDto> demandDtoList;

    /**
     * (3.20.0)接单模式：1 指定车主，2 竞价抢单
     */
    @ApiModelProperty(value = "接单模式：1 指定车主，2 竞价抢单", required = true)
    @NotBlank(message = "请选择接单模式")
    @Pattern(regexp = "^[12]$", message = "请选择接单模式")
    private String orderMode;


    //指定车主时需要的字段
    @ApiModelProperty(value = "是否我司：1 我司，2 其他车主")
    private String isOurCompany;

    @ApiModelProperty(value = "车主ID")
    private String companyCarrierId;


    //竞价抢单时需要的字段
    /**
     * (3.20.0)报价时长：1 30分钟，2 60分钟，3 90分钟
     */
    @ApiModelProperty(value = "报价时长：1 30分钟，2 60分钟，3 90分钟")
    private String quoteDuration;

    /**
     * (3.20.0)车主范围：1 全部，2 定向选择
     */
    @ApiModelProperty(value = "车主范围：1 全部，2 定向选择")
    private String companyCarrierRange;

    /**
     * (3.22.0)车主ID（车主范围为【定向选择】时必填）
     */
    @ApiModelProperty(value = "车主ID（车主范围为【定向选择】时必填）")
    private List<String> companyCarrierIdList;

    /**
     * (3.20.0)车长要求：1 无，2 指定车长
     */
    @ApiModelProperty(value = "车长要求：1 无，2 指定车长")
    private String vehicleLengthNeed;

    /**
     * (3.20.0)车长id
     */
    @ApiModelProperty(value = "车长id")
    private String vehicleLengthId;

    /**
     * (3.20.0)期望提货时间
     */
    @ApiModelProperty(value = "期望提货时间")
    private String expectedLoadTime;

    /**
     * (3.20.0)期望卸货时间
     */
    @ApiModelProperty(value = "期望卸货时间")
    private String expectedUnloadTime;

    /**
     * (3.20.0)备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 300,message = "备注不超过300字")
    private String remark;
}
