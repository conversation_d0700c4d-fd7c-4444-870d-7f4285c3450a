package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2024/09/19
*/
@Data
public class TShippingFreightRuleCrossPoint extends BaseEntity {
    /**
    * 零担运价规则id
    */
    @ApiModelProperty("零担运价规则地址id")
    private Long shippingFreightAddressId;

    /**
    * 起始数量
    */
    @ApiModelProperty("起始数量")
    private BigDecimal countStart;

    /**
    * 结束数量
    */
    @ApiModelProperty("结束数量")
    private BigDecimal countEnd;

    /**
    * 排序
    */
    @ApiModelProperty("排序")
    private Integer sort;

    /**
    * 价格
    */
    @ApiModelProperty("价格")
    private BigDecimal price;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}