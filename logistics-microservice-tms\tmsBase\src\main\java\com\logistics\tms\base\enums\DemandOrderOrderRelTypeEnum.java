package com.logistics.tms.base.enums;

public enum DemandOrderOrderRelTypeEnum {

    DEFAULT(0,""),
    PUBLISH(1,"下单"),
    ADDITIONAL(2,"追加"),
    REPLENISH(3,"补单"),
    ;

    private Integer key;
    private String value;

    DemandOrderOrderRelTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
    public static DemandOrderOrderRelTypeEnum getEnum(Integer key) {
        for (DemandOrderOrderRelTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
