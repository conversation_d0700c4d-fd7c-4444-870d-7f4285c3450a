package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * @author: wjf
 * @date: 2024/7/10 17:45
 */
@AllArgsConstructor
@Getter
public enum RouteEnquiryAttachmentTypeEnum {
    DEFAULT(0, "", ""),
    QUOTED_FILE(1, "报价单", "车主确认"),
    ARCHIVE_FILE(2, "归档文件", "归档"),
    ;

    private final Integer key;
    private final String value;
    private final String uploadNode;

    public static RouteEnquiryAttachmentTypeEnum getEnum(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
