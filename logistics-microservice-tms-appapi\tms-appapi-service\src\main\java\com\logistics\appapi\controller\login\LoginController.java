package com.logistics.appapi.controller.login;

import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson.JSONObject;
import com.logistics.appapi.controller.common.CommonBiz;
import com.logistics.appapi.controller.common.WxBiz;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.*;
import com.logistics.appapi.base.utils.FrequentMethodUtils;
import com.logistics.appapi.client.customeraccount.CustomerAccountClient;
import com.logistics.appapi.client.customeraccount.request.*;
import com.logistics.appapi.client.customeraccount.response.CustomerLoginResponseModel;
import com.logistics.appapi.client.customeraccount.response.UpdatePhoneByVerifyPhoneResponseModel;
import com.logistics.appapi.client.staff.StaffClient;
import com.logistics.appapi.client.staff.response.GetStaffDetailByAccountIdResponseModel;
import com.logistics.appapi.client.sysconfig.SysConfigClient;
import com.logistics.appapi.client.sysconfig.request.SysConfigRequestModel;
import com.logistics.appapi.client.sysconfig.response.MailingInfoDetailResponseModel;
import com.logistics.appapi.client.sysconfig.response.SysConfigResponseModel;
import com.logistics.appapi.controller.login.request.*;
import com.logistics.appapi.controller.login.response.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: wjf
 * @date: 2024/3/7 16:47
 */
@Slf4j
@Api(value = "登录api", tags = "登录api")
@RestController
public class LoginController {

    @Resource
    private WxBiz wxBiz;
    @Resource
    private StaffClient staffClient;
    @Resource
    private CustomerAccountClient customerAccountClient;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 物流tms司机小程序默认登录（openId）
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "物流tms司机小程序默认登录（openId）", tags = "1.1.0")
    @PostMapping(value = "/api/driverApplet/driverAppletDefaultLogin")
    @SaIgnore
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<LogisticsTmsLoginResponseDto> driverAppletDefaultLogin(@RequestBody @Valid DriverAppletDefaultLoginRequestDto requestDto) {
        DriverAppletDefaultLoginRequestModel model = new DriverAppletDefaultLoginRequestModel();
        model.setOpenId(wxBiz.getOpenId(requestDto.getCode()));
        Result<CustomerLoginResponseModel> result = customerAccountClient.driverAppletDefaultLogin(model);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),LogisticsTmsLoginResponseDto.class));
    }

    /**
     * 物流tms司机小程序登录（帐密或验证码）
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "物流tms司机小程序登录（帐密或验证码）", tags = "1.1.0")
    @PostMapping(value = "/api/driverApplet/driverAppletLogin")
    @SaIgnore
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<LogisticsTmsLoginResponseDto> driverAppletLogin(@RequestBody @Valid DriverAppletLoginRequestDto requestDto) {
        //入参校验及入参解密
        if (LoginTypeEnum.VERIFY_CODE.getKey().equals(requestDto.getLoginType())) {//账号验证码登录
            if(StringUtils.isBlank(requestDto.getVerificationCode())){
                throw new BizException(AppApiExceptionEnum.USER_VERIFICATION_CANT_BE_NULL);
            }
            //解密入参
            requestDto.setVerificationCode(commonBiz.decode(requestDto.getVerificationCode()));
        } else if (LoginTypeEnum.PASSWORD.getKey().equals(requestDto.getLoginType())) {//账号密码登录
            if (StringUtils.isBlank(requestDto.getPassword())) {
                throw new BizException(AppApiExceptionEnum.USER_PASSWORD_CANT_BE_NULL);
            }
            //解密入参
            requestDto.setPassword(commonBiz.decode(requestDto.getPassword()));
        }else{
            throw new BizException(AppApiExceptionEnum.PARAMS_ERROR);
        }
        //解密入参
        requestDto.setUserAccount(commonBiz.decode(requestDto.getUserAccount()));
        log.info("小程序登录接口解密后的入参："+requestDto);
        //入参校验
        if (!FrequentMethodUtils.checkMobilePhone(requestDto.getUserAccount())){
            throw new BizException(AppApiExceptionEnum.MOBILE_PHONE_ERROR);
        }

        //账号密码登录，验证滑块
        if (LoginTypeEnum.PASSWORD.getKey().equals(requestDto.getLoginType())) {
            commonBiz.validationPuzzlePicture(requestDto.getLocationX(), requestDto.getVerificationId());
        }

        CustomerLoginRequestModel model = MapperUtils.mapper(requestDto,CustomerLoginRequestModel.class);
        Result<CustomerLoginResponseModel> result = customerAccountClient.driverAppletLogin(model);
        result.throwException();
        AppletBindingOpenIdRequestModel requestModel = new AppletBindingOpenIdRequestModel();
        requestModel.setOpenId(wxBiz.getOpenId(requestDto.getCode()));
        requestModel.setUserId(result.getData().getUserId());
        Result accountResult = customerAccountClient.appletBindingOpenId(requestModel);
        accountResult.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),LogisticsTmsLoginResponseDto.class));
    }

    /**
     * 获取用户信息(我的)
     * @return
     */
    @PostMapping(value = "/api/driverApplet/getUserInfo")
    @ApiOperation(value = "获取用户信息(我的)", tags = "1.1.0")
    public Result<UserInfoResponseDto> getUserInfo() {
        Result<GetStaffDetailByAccountIdResponseModel> staffDetailByAccountIdResult = staffClient.getStaffDetailByAccountId();
        staffDetailByAccountIdResult.throwException();
        GetStaffDetailByAccountIdResponseModel staffDetailByAccountIdResultData = staffDetailByAccountIdResult.getData();
        UserInfoResponseDto responseDto = MapperUtils.mapper(staffDetailByAccountIdResultData, UserInfoResponseDto.class);
        responseDto.setUserAccount(staffDetailByAccountIdResultData.getMobile());
        responseDto.setUserName(staffDetailByAccountIdResultData.getName());
        return Result.success(responseDto);
    }

    /**
     * 物流tms司机小程序退出登录
     * @return
     */
    @PostMapping(value = "/api/driverApplet/driverAppletLoginOut")
    @ApiOperation(value = "物流tms司机小程序退出登录")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result driverAppletLoginOut() {
        return customerAccountClient.appletLoginOut();
    }

    /**
     * 修改密码
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/api/driverApplet/modifyPassword")
    @ApiOperation(value = "修改密码")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result modifyPassword(@RequestBody @Valid ModifyPasswordRequestDto requestDto) {
        //解密入参
        requestDto.setOldPassword(commonBiz.decode(requestDto.getOldPassword()));
        requestDto.setNewPassword(commonBiz.decode(requestDto.getNewPassword()));
        log.info("小程序修改密码接口解密后的入参："+requestDto);
        //入参校验
        if (!FrequentMethodUtils.checkPassword(requestDto.getNewPassword())){
            throw new BizException(AppApiExceptionEnum.PASSWORD_ERROR);
        }
        //判断新旧密码是否一直
        if (requestDto.getOldPassword().equals(requestDto.getNewPassword())) {
            throw new BizException(AppApiExceptionEnum.OLE_PASSWORD_NOT_EQUALS_NEW_PASSWORD);
        }

        return customerAccountClient.modifyPassword(MapperUtils.mapper(requestDto, ModifyPasswordRequestModel.class));
    }

    /**
     * 获取配置项接口
     *
     * @return 配置项
     */
    @PostMapping(value = "/api/driverApplet/getConfiguration")
    @ApiOperation(value = "获取配置项接口")
    public Result<DriverAppletConfigurationResponseDto> getConfiguration() {
        // 批量获取配置
        List<SysConfigRequestModel> requestModels = Stream.of(ConfigKeyEnum.APPLET_RETURN_ADDRESS_CONFIG,
                        ConfigKeyEnum.CUSTOMER_SERVICE_PHONE_CONFIG)
                .map(s -> {
                    SysConfigRequestModel sysConfigRequestModel = new SysConfigRequestModel();
                    sysConfigRequestModel.setGroupCode(s.getGroupCode());
                    sysConfigRequestModel.setConfigKey(s.getValue());
                    return sysConfigRequestModel;
                })
                .collect(Collectors.toList());
        Result<List<SysConfigResponseModel>> result = sysConfigClient.batchGetSysConfig(requestModels);
        result.throwException();

        DriverAppletConfigurationResponseDto responseDto = new DriverAppletConfigurationResponseDto();
        List<SysConfigResponseModel> responseModels = result.getData();
        if (ListUtils.isNotEmpty(responseModels)) {
            Map<String, Map<String, String>> configMap = responseModels.stream()
                    .collect(Collectors.groupingBy(SysConfigResponseModel::getGroupCode,
                            Collectors.toMap(SysConfigResponseModel::getConfigKey, SysConfigResponseModel::getConfigValue)));
            responseDto.setTmsAppServicePhone(getConfigValue(configMap, ConfigKeyEnum.CUSTOMER_SERVICE_PHONE_CONFIG));

            // 地址配置
            DriverAppletConfigurationReceiptDto receiptDto = new DriverAppletConfigurationReceiptDto();
            String configValueJson = getConfigValue(configMap, ConfigKeyEnum.APPLET_RETURN_ADDRESS_CONFIG);
            if (StringUtils.isNotBlank(configValueJson)) {
                try {
                    MailingInfoDetailResponseModel mailingInfo = JSONObject.parseObject(configValueJson, MailingInfoDetailResponseModel.class);
                    receiptDto.setTmsAppReceiptAddress(mailingInfo.getAddress());
                    receiptDto.setTmsAppReceiptAddressName(mailingInfo.getAddressee());
                    receiptDto.setTmsAppReceiptPhone(mailingInfo.getAddresseeMobile());
                } catch (Exception e) {
                    log.error("回单系统配置转换异常, config value:{}", configValueJson);
                }
            }
            responseDto.setReceiptInfo(receiptDto);
        }
        return Result.success(responseDto);
    }

    private String getConfigValue(Map<String, Map<String, String>> configMap, ConfigKeyEnum configEnum) {
        return Optional.ofNullable(configMap.get(configEnum.getGroupCode()))
                .map(s -> {
                    return s.getOrDefault(configEnum.getValue(),"");
                })
                .orElse("");
    }

    /**
     * 找回密码
     * @param requestDto
     * @return
     */
    @ApiOperation("找回密码")
    @PostMapping(value = "/api/driverApplet/findPassword")
    @SaIgnore
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result findPassword(@RequestBody @Valid DriverAppletFindPasswordRequestDto requestDto) {
        //解密入参
        requestDto.setUserAccount(commonBiz.decode(requestDto.getUserAccount()));
        requestDto.setVerificationCode(commonBiz.decode(requestDto.getVerificationCode()));
        requestDto.setPassword(commonBiz.decode(requestDto.getPassword()));
        log.info("小程序找回密码接口解密后的入参："+requestDto);

        //入参校验
        if (!FrequentMethodUtils.checkMobilePhone(requestDto.getUserAccount())){
            throw new BizException(AppApiExceptionEnum.MOBILE_PHONE_ERROR);
        }
        if (!FrequentMethodUtils.checkPassword(requestDto.getPassword())){
            throw new BizException(AppApiExceptionEnum.PASSWORD_ERROR);
        }

        UpdateAccountPasswordRequestModel requestModel = MapperUtils.mapper(requestDto, UpdateAccountPasswordRequestModel.class);
        requestModel.setCodeSource(VerificationCodeSourceTypeEnum.TMS_DRIVER_APPLET.getKey());
        requestModel.setCodeType(VerificationCodeTypeEnum.TMS_DRIVER_APPLET_FIND_PASS.getKey());
        return customerAccountClient.updateAccountPassword(requestModel);
    }

    /**
     * 修改手机号第一步 验证手机号
     *
     * @param requestDto 手机号,验证码
     * @return 操作步骤token
     */
    @ApiOperation(value = "更换手机号-验证手机号 v1.0.3")
    @PostMapping(value = "/api/login/verifyPhone")
    public Result<UpdatePhoneByVerifyPhoneResponseDto> verifyPhone(@RequestBody @Valid UpdatePhoneByVerifyPhoneRequestDto requestDto) {
        //解密入参
        requestDto.setUserAccount(commonBiz.decode(requestDto.getUserAccount()));
        requestDto.setVerificationCode(commonBiz.decode(requestDto.getVerificationCode()));
        log.info("小程序修改手机号-验证手机号接口解密后的入参："+requestDto);
        //入参校验
        if (!FrequentMethodUtils.checkMobilePhone(requestDto.getUserAccount())){
            throw new BizException(AppApiExceptionEnum.MOBILE_PHONE_ERROR);
        }

        //验证手机号、短信验证码
        UpdatePhoneByVerifyPhoneRequestModel requestModel = MapperUtils.mapper(requestDto,UpdatePhoneByVerifyPhoneRequestModel.class);
        requestModel.setCodeSource(VerificationCodeSourceTypeEnum.TMS_DRIVER_APPLET.getKey());
        requestModel.setCodeType(VerificationCodeTypeEnum.TMS_DRIVER_APPLET_MODIFY_PHONE.getKey());
        requestModel.setRequestSource(CommonConstant.INTEGER_ONE);
        Result<UpdatePhoneByVerifyPhoneResponseModel> result = customerAccountClient.verifyPhone(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),UpdatePhoneByVerifyPhoneResponseDto.class));
    }

    /**
     * 修改手机号
     *
     * @param requestDto 手机号 验证码 步骤token
     * @return 操作结果
     */
    @ApiOperation(value = "更换手机号-更新手机号 v1.0.3")
    @PostMapping(value = "/api/login/updatePhone")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> updatePhone(@RequestBody @Valid UpdatePhoneRequestDto requestDto) {
        //解密入参
        requestDto.setUserAccount(commonBiz.decode(requestDto.getUserAccount()));
        requestDto.setVerificationCode(commonBiz.decode(requestDto.getVerificationCode()));
        log.info("小程序修改手机号接口解密后的入参："+requestDto);
        //入参校验
        if (!FrequentMethodUtils.checkMobilePhone(requestDto.getUserAccount())){
            throw new BizException(AppApiExceptionEnum.MOBILE_PHONE_ERROR);
        }

        UpdatePhoneRequestModel requestModel = MapperUtils.mapper(requestDto,UpdatePhoneRequestModel.class);
        requestModel.setCodeSource(VerificationCodeSourceTypeEnum.TMS_DRIVER_APPLET.getKey());
        requestModel.setCodeType(VerificationCodeTypeEnum.TMS_DRIVER_APPLET_MODIFY_PHONE.getKey());
        requestModel.setRequestSource(CommonConstant.INTEGER_ONE);
        return customerAccountClient.updatePhone(requestModel);
    }
}
