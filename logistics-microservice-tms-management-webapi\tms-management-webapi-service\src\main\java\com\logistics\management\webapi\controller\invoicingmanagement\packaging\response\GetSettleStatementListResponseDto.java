package com.logistics.management.webapi.controller.invoicingmanagement.packaging.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/3/19 17:43
 */
@Data
public class GetSettleStatementListResponseDto {
    @ApiModelProperty("发票关联对账单id")
    private String invoicingSettleStatementId = "";

    @ApiModelProperty("对账月份")
    private String settleStatementMonth = "";

    @ApiModelProperty("对账单id")
    private String settleStatementId = "";
    @ApiModelProperty("对账单号")
    private String settleStatementCode = "";

    @ApiModelProperty("费额合计")
    private String carrierFreightTotal = "";

    @ApiModelProperty("临时费用合计")
    private String otherFeeTotal = "";

    @ApiModelProperty("差异调整费用")
    private String adjustFee = "";

    @ApiModelProperty("对账费用")
    private String reconciliationFee = "";

    @ApiModelProperty("对账单名称")
    private String settleStatementName = "";

    @ApiModelProperty("合同号")
    private String contractCode = "";
}
