package com.logistics.management.webapi.controller.dispatch.mapping;

import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.management.webapi.client.dispatch.response.DemandOrderSpecialDispatchDetailListResponseModel;
import com.logistics.management.webapi.client.dispatch.response.DemandOrderSpecialDispatchDetailResponseModel;
import com.logistics.management.webapi.controller.dispatch.response.DemandOrderSpecialDispatchDetailListResponseDto;
import com.logistics.management.webapi.controller.dispatch.response.DemandOrderSpecialDispatchDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/7 9:51
 */
public class SpecialDispatchDetailMapping extends MapperMapping<DemandOrderSpecialDispatchDetailResponseModel, DemandOrderSpecialDispatchDetailResponseDto> {
    @Override
    public void configure() {
        DemandOrderSpecialDispatchDetailResponseModel source = getSource();
        DemandOrderSpecialDispatchDetailResponseDto destination = getDestination();

        //车主
        if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())) {
            destination.setCompanyCarrierName(source.getCarrierContactName() + " " + source.getCarrierContactPhone());
        }

        //需求单信息
        DemandOrderSpecialDispatchDetailListResponseDto dto;
        List<DemandOrderSpecialDispatchDetailListResponseDto> goodsList = new ArrayList<>();
        for (DemandOrderSpecialDispatchDetailListResponseModel model : source.getGoodsList()) {
            dto = MapperUtils.mapper(model, DemandOrderSpecialDispatchDetailListResponseDto.class);
            dto.setLoadDetailAddress(model.getLoadCityName() + model.getLoadAreaName() + model.getLoadDetailAddress());
            dto.setUnloadDetailAddress(model.getUnloadCityName() + model.getUnloadAreaName() + model.getUnloadDetailAddress());
            if (GoodsUnitEnum.BY_VOLUME.getKey().equals(source.getGoodsUnit())) {
                dto.setGoodsSize(dto.getGoodsSize() + " " + model.getLength() + "*" + model.getWidth() + "*" + model.getHeight() + "mm");
            }else{
                dto.setGoodsSize(model.getGoodsSize());
            }
            dto.setNotArrangedAmount(model.getNotArrangedAmount().stripTrailingZeros().toPlainString());
            if (model.getNextPointDistance() != null) {
                dto.setNextPointDistance(model.getNextPointDistance().stripTrailingZeros().toPlainString());
            }
            goodsList.add(dto);
        }
        destination.setGoodsList(goodsList);
    }
}
