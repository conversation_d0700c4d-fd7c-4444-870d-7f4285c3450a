package com.logistics.appapi.client.customeraccount.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author：wjf
 * @date：2021/5/11 13:51
 */
@Data
public class CustomerLoginRequestModel {
    @ApiModelProperty(value = "登陆方式 1 验证码 2密码")
    private String loginType;
    @ApiModelProperty(value = "手机号")
    private String userAccount;
    @ApiModelProperty(value = "密码")
    private String password;
    @ApiModelProperty(value = "手机验证码")
    private String verificationCode;
}
