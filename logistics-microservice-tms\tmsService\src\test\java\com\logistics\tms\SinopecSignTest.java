package com.logistics.tms;

import cn.org.secmid.phoenix.assemb.param.AlgPolicy;
import cn.org.secmid.phoenix.util.encoders.Base64;
import cn.org.secmid.security.SecurityEngineDeal;
import cn.org.sniopec.fades.UcspInterface;
import cn.org.sniopec.impl.UcspImplement;
import org.junit.Test;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/26
 */
public class SinopecSignTest {

	@Test
	public void testSignEn() throws IOException {
		UcspInterface ucspInterface = new UcspImplement();
		SecurityEngineDeal sed = ucspInterface.getInstance("D:\\projects\\leju\\qiya-wuliu\\logistics-microservice-tms\\tmsService\\src\\test\\resources\\sdk.properties");
		AlgPolicy asymPolicy = new AlgPolicy(AlgPolicy.SM3_SM2);
		Path of = Path.of("C:\\Users\\<USER>\\Desktop\\中石化小定单.json");
		String s = Files.readString(of);
		String encData = ucspInterface.signPkcs7Sdk(sed, asymPolicy,"SHZNYYZX", s, false);
		System.out.println(encData);
	}

	@Test
	public void testSignDe() {
		UcspInterface ucspInterface = new UcspImplement();
		SecurityEngineDeal sed = ucspInterface.getInstance("D:\\projects\\leju\\qiya-wuliu\\logistics-microservice-tms\\tmsService\\src\\test\\resources\\sdk.properties");
		String signValue = "MIIETQYKKoEcz1UGAQQCAqCCBD0wggQ5AgEBMQ4wDAYIKoEcz1UBgxEFADAMBgoqgRzPVQYBBAIBoIIDVDCCA1AwggL2oAMCAQICCHCg2nu7zPyGMAoGCCqBHM9VAYN1MEIxCzAJBgNVBAYTAkNOMRAwDgYDVQQKDAdTSU5PUEVDMSEwHwYDVQQDDBhTSU5PUEVDIFNNMiBPcGVyYXRpb24gQ0EwHhcNMjEwNjA5MDgwMjExWhcNNDEwNjA0MDgwMjExWjBKMQswCQYDVQQGEwJDTjEQMA4GA1UECgwHU0lOT1BFQzERMA8GA1UECwwIU0haTllZWlgxFjAUBgNVBAMMDTEwLjIzOC4yMjIuOTMwWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAARJe/UtpX/SlZuk8w23SdFVDfnAGPmQilVgXd3foZPoNdpTQQLlkqsNb8Wsc4iRLbD5iV5NVk40K9qIfJfHfY3vo4IBzDCCAcgwggE+BgNVHR8EggE1MIIBMTBDoEGgP6Q9MDsxCzAJBgNVBAYTAkNOMRAwDgYDVQQKDAdTSU5PUEVDMQwwCgYDVQQLDANDUkwxDDAKBgNVBAMMA2NybDAeoBygGoYYaHR0cDovLzEyNy4wLjAuMS9jcmwuY3JsMIHJoIHGoIHDhoHAbGRhcDovLzEwLjI0Ni4xODkuOTE6Mzg5L0NOPWNybCxDTj1TTTJDUkwsQ049Q0FTZXJ2ZXIyLENOPUNEUCxDTj1QdWJsaWMgS2V5IFNlcnZpY2VzLENOPVNlcnZpY2VzLENOPUNvbmZpZ3VyYXRpb24sTz1TSU5PUEVDLEM9Q04/Y2VydGlmaWNhdGVSZXZvY2F0aW9uTGlzdD9iYXNlP29iamVjdGNsYXNzPWNSTERpc3RyaWJ1dGlvblBvaW50MAsGA1UdDwQEAwIGwDAdBgNVHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUHAwIwHQYDVR0OBBYEFOtEuBqrpJtBOtIiv5pwd6GibQxsMBgGA1UdEQQRMA+CDTEwLjIzOC4yMjIuOTMwHwYDVR0jBBgwFoAUWxa+kaFgYTZ+Kn3/WBn2m19MfDAwCgYIKoEcz1UBg3UDSAAwRQIhAL+TzraDM/UBywiHTfhoxe1iQVZJ7F4LFhEZTm/tCLrcAiBhGohfNVzW3I+rg14+jHjW7CIyiUY4TFVVU2zUqCD5vDGBvTCBugIBATBOMEIxCzAJBgNVBAYTAkNOMRAwDgYDVQQKDAdTSU5PUEVDMSEwHwYDVQQDDBhTSU5PUEVDIFNNMiBPcGVyYXRpb24gQ0ECCHCg2nu7zPyGMAwGCCqBHM9VAYMRBQAwDQYJKoEcz1UBgi0BBQAESDBGAiEAjn52h2bBfz7olclL105xqfcmW5MdaxWVYa3+QOSrH2cCIQCefBdbf17/zPMp+qwdr0TNKnZYTDCAHleIz3OYzMhgcg==";
		byte[] InputData = "998111".getBytes(StandardCharsets.UTF_8);
		boolean outData = ucspInterface.verifyPkcs7Sign(sed, Base64.decode(signValue), InputData);
		System.out.println("PKCS7验签= " + outData);
	}
}
