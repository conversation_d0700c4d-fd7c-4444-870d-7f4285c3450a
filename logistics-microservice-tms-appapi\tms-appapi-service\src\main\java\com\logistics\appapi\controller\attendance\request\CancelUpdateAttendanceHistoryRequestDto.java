package com.logistics.appapi.controller.attendance.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 撤销修改考勤打卡请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/14
 */
@Data
public class CancelUpdateAttendanceHistoryRequestDto {

	@ApiModelProperty(value = "变更申请ID", required = true)
	@NotBlank(message = "请选择撤销的申请")
	private String attendanceChangeApplyId;

	@ApiModelProperty(value = "撤销说明,1-100个字符", required = true)
	@NotBlank(message = "请填写撤销说明,1-100字")
	@Length(min = 1, max = 100, message = "请填写撤销说明,1-100字")
	private String remark;
}
