package com.logistics.tms.mapper;

import com.logistics.tms.biz.carriervehiclerel.model.CarrierVehicleRelAndInfoModel;
import com.logistics.tms.controller.carriervehiclerel.response.GetCarrierVehicleDetailResponseModel;
import com.logistics.tms.controller.carriervehiclerel.request.SearchCarrierVehicleListRequestModel;
import com.logistics.tms.controller.carriervehiclerel.response.SearchCarrierVehicleListResponseModel;
import com.logistics.tms.controller.demandorder.response.WebDriverAndVehicleResponseModel;
import com.logistics.tms.controller.vehicleassetmanagement.request.VehicleAssetManagementListRequestModel;
import com.logistics.tms.controller.vehicleassetmanagement.response.ExportVehicleBasicInfoResponseModel;
import com.logistics.tms.controller.vehicleassetmanagement.response.VehicleAssetManagementListResponseModel;
import com.logistics.tms.entity.TCarrierVehicleRelation;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.Nullable;

import java.util.List;

@Mapper
public interface TCarrierVehicleRelationMapper extends BaseMapper<TCarrierVehicleRelation> {

    List<SearchCarrierVehicleListResponseModel> getList(@Param("condition") SearchCarrierVehicleListRequestModel requestModel);

    List<TCarrierVehicleRelation> getByVehicleIds(@Param("vehicleIds") String vehicleIds);

    int batchInsert(@Param("list") List<TCarrierVehicleRelation> relList);

    List<TCarrierVehicleRelation> getByCompanyCarrierId(@Param("companyCarrierId") Long companyCarrierId);

    List<TCarrierVehicleRelation> getByCompanyCarrierIds(@Param("companyCarrierIds") String companyCarrierIds);

    TCarrierVehicleRelation getByCompanyCarrierIdAndVehicleId(@Param("companyCarrierId") Long companyCarrierId, @Param("vehicleId") Long vehicleId);

    List<WebDriverAndVehicleResponseModel> searchRelByVehicleNoAndCarrierId(@Param("companyCarrierId") Long companyCarrierId, @Param("vehicleNo") String vehicleNo);

    GetCarrierVehicleDetailResponseModel getCarrierVehicleDetail(@Param("companyCarrierId") Long companyCarrierId, @Param("carrierVehicleId") Long carrierVehicleId);

    int batchUpdate(@Param("list") List<TCarrierVehicleRelation> relList);

    List<VehicleAssetManagementListResponseModel> getVehicleList(@Param("params") VehicleAssetManagementListRequestModel requestModel, @Nullable @Param("companyIds") List<Long> companyIds);

    List<ExportVehicleBasicInfoResponseModel> getExportVehicleList(@Param("params") VehicleAssetManagementListRequestModel requestModel, @Nullable @Param("companyIds") List<Long> companyIds);

    List<CarrierVehicleRelAndInfoModel> selectVehicleRelAndInfoByVehicleIds(@Param("vehicleidList") List<Long> vehicleidList);
}
