package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2022/12/16 13:26
 */
public enum CarrierOrderLocationTypeEnum {
    UNLOAD(0, "卸货"),
    LOAD(1, "提货"),;

    private Integer key;
    private String value;

    CarrierOrderLocationTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
