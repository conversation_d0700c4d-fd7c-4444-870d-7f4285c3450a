package com.logistics.management.webapi.api.impl.traycost.mapping;

import com.logistics.management.webapi.api.feign.traycost.dto.TrayCostDetailResponseDto;
import com.logistics.management.webapi.base.enums.EntrustTypeEnum;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.tms.api.feign.traycost.model.TrayCostDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * <AUTHOR>
 * @date 2020/4/21 13:20
 */
public class GetTrayCostDetailMapping extends MapperMapping<TrayCostDetailResponseModel, TrayCostDetailResponseDto> {
    @Override
    public void configure() {
        TrayCostDetailResponseModel source = getSource();
        TrayCostDetailResponseDto destination = getDestination();

        destination.setEntrustType(EntrustTypeEnum.getEnum(source.getEntrustType()).getValue());
        destination.setGoodsUnit(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());
    }
}
