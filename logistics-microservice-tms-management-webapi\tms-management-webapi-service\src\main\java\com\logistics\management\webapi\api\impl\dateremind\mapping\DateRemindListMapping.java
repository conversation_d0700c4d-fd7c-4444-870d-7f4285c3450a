package com.logistics.management.webapi.api.impl.dateremind.mapping;

import com.logistics.management.webapi.api.feign.dateremind.dto.DateRemindListResponseDto;
import com.logistics.management.webapi.base.enums.IfRemindEnum;
import com.logistics.tms.api.feign.dateremind.model.DateRemindListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @Author: sj
 * @Date: 2019/5/31 13:42
 */
public class DateRemindListMapping extends MapperMapping<DateRemindListResponseModel,DateRemindListResponseDto> {
    @Override
    public void configure() {
        DateRemindListResponseModel model = this.getSource();
        DateRemindListResponseDto dto = this.getDestination();
        if(model!=null){
            dto.setIfRemindLabel(IfRemindEnum.getEnum(model.getIfRemind()).getValue());
            if(model.getRemindDays()!=null){
                dto.setRemindDaysLabel(model.getRemindDays()+"天");
            }
            if(model.getLastModifiedTime()!=null){
                dto.setLastModifiedTime(DateUtils.dateToString(model.getLastModifiedTime(),DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
            }
        }
    }
}
