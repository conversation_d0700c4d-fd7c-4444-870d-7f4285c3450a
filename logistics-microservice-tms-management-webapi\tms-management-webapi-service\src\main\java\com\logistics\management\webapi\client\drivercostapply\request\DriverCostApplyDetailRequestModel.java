package com.logistics.management.webapi.client.drivercostapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/8/3 13:51
 */
@Data
public class DriverCostApplyDetailRequestModel {

    @ApiModelProperty(value = "司机费用申请表id")
    private Long driverCostApplyId;

    @ApiModelProperty("请求来源 1 后台，2 前台 3小程序")
    private Integer source;//1 后台，2 前台
}
