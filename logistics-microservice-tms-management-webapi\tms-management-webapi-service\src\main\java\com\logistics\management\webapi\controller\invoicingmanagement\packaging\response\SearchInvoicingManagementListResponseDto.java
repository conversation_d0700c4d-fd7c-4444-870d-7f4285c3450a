package com.logistics.management.webapi.controller.invoicingmanagement.packaging.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/3/19 16:28
 */
@Data
public class SearchInvoicingManagementListResponseDto {
    @ApiModelProperty("发票管理id")
    private String invoicingId="";

    @ApiModelProperty("业务名称")
    private String businessName="";

    @ApiModelProperty("开票月份")
    private String invoicingMonth="";

    @ApiModelProperty("承运商名称")
    private String companyCarrierName="";

    @ApiModelProperty("发票金额")
    private String invoiceAmount="";

    @ApiModelProperty("对账金额")
    private String reconciliationFee = "";

    @ApiModelProperty("备注")
    private String remark="";

    @ApiModelProperty("操作人")
    private String lastModifiedBy="";

    @ApiModelProperty("操作时间")
    private String lastModifiedTime="";
}
