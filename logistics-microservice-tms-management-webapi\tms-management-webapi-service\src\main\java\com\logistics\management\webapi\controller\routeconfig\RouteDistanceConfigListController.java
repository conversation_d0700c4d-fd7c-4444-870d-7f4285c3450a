package com.logistics.management.webapi.controller.routeconfig;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.routeconfig.RouteDistanceConfigListClient;
import com.logistics.management.webapi.client.routeconfig.request.*;
import com.logistics.management.webapi.client.routeconfig.response.RouteDistanceConfigDetailResponseModel;
import com.logistics.management.webapi.client.routeconfig.response.RouteDistanceConfigRecommendResponseModel;
import com.logistics.management.webapi.client.routeconfig.response.RouteDistanceConfigResponseModel;
import com.logistics.management.webapi.controller.routeconfig.mapping.RouteDistanceConfigMapping;
import com.logistics.management.webapi.controller.routeconfig.request.*;
import com.logistics.management.webapi.controller.routeconfig.response.RouteDistanceConfigDetailResponseDto;
import com.logistics.management.webapi.controller.routeconfig.response.RouteDistanceConfigRecommendResponseDto;
import com.logistics.management.webapi.controller.routeconfig.response.RouteDistanceConfigResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@Api(value = "路线配置管理", tags = "路线配置管理")
@RequestMapping(value = "/api/route/distance/config")
public class RouteDistanceConfigListController {

    @Resource
    private RouteDistanceConfigListClient routeDistanceConfigListClient;
    @PostMapping(value = "/searchList")
    @ApiOperation(value = "路线距离配置列表", tags = "1.3.5")
    Result<PageInfo<RouteDistanceConfigResponseDto>> searchList(@RequestBody RouteDistanceConfigListRequestDto requestDto) {
        Result<PageInfo<RouteDistanceConfigResponseModel>> pageInfoResult =
                routeDistanceConfigListClient.searchList(MapperUtils.mapper(requestDto, RouteDistanceConfigListRequestModel.class));
        pageInfoResult.throwException();
        PageInfo data = pageInfoResult.getData();
        List<RouteDistanceConfigResponseDto> mapper =
                MapperUtils.mapper(data.getList(), RouteDistanceConfigResponseDto.class,new RouteDistanceConfigMapping());
        data.setList(mapper);
        return Result.success(data);
    }

    @PostMapping(value = "/detail")
    @ApiOperation(value = "路线距离配置详情", tags = "1.3.5")
    Result<RouteDistanceConfigDetailResponseDto> detail(@Valid @RequestBody RouteDistanceConfigRequestDto requestDto) {
        Result<RouteDistanceConfigDetailResponseModel> detail =
                routeDistanceConfigListClient.detail(MapperUtils.mapper(requestDto, RouteDistanceConfigRequestModel.class));
        detail.throwException();
        RouteDistanceConfigDetailResponseDto responseDto =
                MapperUtils.mapper(detail.getData(), RouteDistanceConfigDetailResponseDto.class);
        return Result.success(responseDto);
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "路线距离配置新增", tags = "1.3.5")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    Result<Boolean> add(@Valid @RequestBody RouteDistanceConfigAddRequestDto requestDto) {
        Result<Boolean> add =
                routeDistanceConfigListClient.add(
                        MapperUtils.mapper(
                                requestDto,
                                RouteDistanceConfigAddRequestModel.class));
        add.throwException();
        return Result.success(true);
    }

    @PostMapping(value = "/edit")
    @ApiOperation(value = "路线距离配置编辑", tags = "1.3.5")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    Result<Boolean> edit(@Valid @RequestBody RouteDistanceConfigEditRequestDto requestDto) {
        Result<Boolean> edit =
                routeDistanceConfigListClient.edit(
                        MapperUtils.mapper(
                                requestDto,
                                RouteDistanceConfigEditRequestModel.class));
        edit.throwException();
        return Result.success(true);
    }

    @PostMapping(value = "/delete")
    @ApiOperation(value = "路线距离配置删除", tags = "1.3.5")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    Result<Boolean> delete(@Valid @RequestBody RouteDistanceConfigRequestDto requestDto) {
        Result<Boolean> delete =
                routeDistanceConfigListClient.delete(MapperUtils.mapper(requestDto, RouteDistanceConfigRequestModel.class));
        delete.throwException();
        return Result.success(true);
    }

    @PostMapping(value = "/recommend")
    @ApiOperation(value = "路线距离配置推荐", tags = "1.3.5")
    Result<RouteDistanceConfigRecommendResponseDto> recommend(@Valid @RequestBody RouteDistanceConfigRecommendRequestDto requestDto) {
        Result<RouteDistanceConfigRecommendResponseModel> recommend =
                routeDistanceConfigListClient.recommend(MapperUtils.mapper(requestDto, RouteDistanceConfigRecommendRequestModel.class));
        recommend.throwException();
        RouteDistanceConfigRecommendResponseDto responseDto =
                MapperUtils.mapper(recommend.getData(), RouteDistanceConfigRecommendResponseDto.class);
        return Result.success(responseDto);
    }
}
