package com.logistics.appapi.controller.workordercenter.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/14
 */
@Data
public class CancelWorkOrderRequestDto {

	@ApiModelProperty(value = "工单id", required = true)
	@NotBlank(message = "工单id不能为空")
	private String workOrderId;

	@ApiModelProperty(value = "备注", required = true)
	@NotBlank(message = "备注不能为空")
	@Length(min = 1, max = 300, message = "备注应为1-300个字符")
	private String remark;
}
