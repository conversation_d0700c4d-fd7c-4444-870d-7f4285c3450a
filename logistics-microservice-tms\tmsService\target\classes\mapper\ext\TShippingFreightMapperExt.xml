<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TShippingFreightMapper" >

  <select id="getList" parameterType="com.logistics.tms.controller.freightconfig.request.ListShippingFreightListReqModel"
          resultType="com.logistics.tms.controller.freightconfig.response.ListShippingFreightListRespModel">
    select
    tsf.id as shippingFreightId,
    tsf.carrier_price_rule_name as carrierPriceRuleName,
    tsf.entrust_type as entrustTypeLabel,
    tsf.remark,
    tsf.created_by as createdBy,
    tsf.created_time as createdTime,
    tsf.last_modified_by as lastModifiedBy,
    tsf.last_modified_time as lastModifiedTime
    from
    t_shipping_freight tsf
    LEFT JOIN t_company_carrier tcc ON tcc.shipping_freight_id = tsf.id and tcc.level = 2  and tcc.valid = 1
    left join t_company tc on tc.id = tcc.company_id and tc.valid = 1
    LEFT JOIN t_carrier_contact tac ON tcc.id = tac.company_carrier_id and tcc.type = 2 AND tac.valid = 1
    where
    tsf.valid = 1
    <if test="carrierName != null and carrierName !=''">
      and ((tcc.type = 1 and instr(tc.company_name,#{carrierName,jdbcType=VARCHAR}))
      or (tcc.type = 2 and (instr(tac.contact_name,#{carrierName,jdbcType=VARCHAR}) or instr(AES_DECRYPT(UNHEX(tac.contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') ,#{carrierName,jdbcType=VARCHAR}))))
    </if>
    group by tsf.id
    order by tsf.created_time desc

  </select>

  <select id="getByName"  resultMap="BaseResultMap" parameterType="String" >
    select
    <include refid="Base_Column_List" />
    from t_shipping_freight
    where carrier_price_rule_name = #{carrierPriceRuleName,jdbcType=VARCHAR}
    and  valid = 1

  </select>
</mapper>