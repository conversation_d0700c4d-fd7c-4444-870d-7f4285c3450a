package com.logistics.management.webapi.api.feign.driveraccount.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class DriverAccountUpdateRequestDto {

    @ApiModelProperty(value = "司机ID", required = true)
    @NotBlank(message = "请选择司机")
    private String driverId;

    @ApiModelProperty(value = "银行账号", required = true)
    @NotBlank(message = "请填写9-30位银行账号")
    @Range(min = 9, max = 30, message = "请填写9-30位银行账号")
    private String bankAccount;

    @ApiModelProperty(value = "开户银行名称", required = true)
    @NotBlank(message = "请填写4-20位银行名称")
    @Range(min = 4, max = 20, message = "请填写4-20位银行名称")
    private String bankAccountName;

    @ApiModelProperty(value = "开户支行名称, 非必填 填写时检验长度0-20")
    @Length(max = 20, message = "最多填写20个字符")
    private String braBankName;

    @ApiModelProperty(value = "行号", required = true)
    @NotBlank(message = "请填写4-20位银行账号")
    @Range(min = 4, max = 30, message = "请填写4-20位银行账号")
    private String bankCode;

    @ApiModelProperty(value = "收款证件图片", required = true)
    @NotEmpty(message = "请上传1-2张收款证件图片")
    @Size(max = 2, message = "最多上传2张收款证件图片片")
    private List<@NotBlank(message = "图片路径不允许为空") String> bankAccountImage;
}
