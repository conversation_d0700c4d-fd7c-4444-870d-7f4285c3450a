package com.logistics.tms.biz.staffvehiclerelation;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.controller.companycarrier.response.CompanyCarrierDetailResponseModel;
import com.logistics.tms.controller.companycarrier.request.FuzzySearchCompanyCarrierRequestModel;
import com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.staff.model.TStaffBasicModel;
import com.logistics.tms.biz.staffvehiclerelation.model.StaffVehicleRelationModel;
import com.logistics.tms.biz.staffvehiclerelation.model.TStaffVehicleRelationModel;
import com.logistics.tms.biz.staffvehiclerelation.model.TVehicleBasicModel;
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel;
import com.logistics.tms.controller.staffvehiclerelation.request.*;
import com.logistics.tms.controller.staffvehiclerelation.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 司机车辆关联关系
 * @Author: sj
 * @Date: 2019/7/26 16:18
 */
@Slf4j
@Service
public class StaffVehicleBiz {
    @Autowired
    private TStaffBasicMapper tqStaffBasicMapper;
    @Autowired
    private TVehicleBasicMapper tqVehicleBasicMapper;
    @Autowired
    private TStaffVehicleRelationMapper tqStaffVehicleRelationMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TVehicleGpsMapper tVehicleGpsMapper;
    @Autowired
    private TCarrierVehicleRelationMapper tCarrierVehicleRelationMapper;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private TCarrierDriverRelationMapper tCarrierDriverRelationMapper;

    /**
     * 列表
     *
     * @param requestModel 筛选条件
     * @return 车主车辆司机关联关系
     */
    public PageInfo<SearchStaffVehicleListResponseModel> searchStaffVehicleList(SearchStaffVehicleListRequestModel requestModel) {
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            /*前台请求*/
            Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
                return new PageInfo<>(new ArrayList<>());
            }
            requestModel.setCompanyCarrierIds(ConverterUtils.toString(loginUserCompanyCarrierId));
        }

        //勾选我司
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getIsOurCompany())) {
            requestModel.setCompanyCarrierIds(ConverterUtils.toString(commonBiz.getQiyaCompanyCarrierId()));
        } else {
            //车主名模糊查询
            if (StringUtils.isNotBlank(requestModel.getCompanyCarrierName())) {
                FuzzySearchCompanyCarrierRequestModel requestModel1 = new FuzzySearchCompanyCarrierRequestModel();
                requestModel1.setCompanyName(requestModel.getCompanyCarrierName());
                List<FuzzySearchCompanyCarrierResponseModel> companyCarrierInfo = tCompanyCarrierMapper.fuzzyQueryCompanyCarrierInfo(requestModel1);
                if (ListUtils.isNotEmpty(companyCarrierInfo)) {
                    requestModel.setCompanyCarrierIds(StringUtils.join(companyCarrierInfo.stream().map(FuzzySearchCompanyCarrierResponseModel::getCompanyId).collect(Collectors.toList()), ','));
                } else {
                    requestModel.setCompanyCarrierIds(CommonConstant.ZERO);
                }
            }
        }

        requestModel.enablePaging();
        List<SearchStaffVehicleListResponseModel> staffVehicleList = tqStaffVehicleRelationMapper.searchStaffVehicleList(requestModel);

        //查询车主信息拼接数据
        List<Long> companyCarrierIdList = staffVehicleList.stream().map(SearchStaffVehicleListResponseModel::getCompanyCarrierId).collect(Collectors.toList());
        if (ListUtils.isNotEmpty(companyCarrierIdList)) {
            List<FuzzySearchCompanyCarrierResponseModel> companyCarrierInfoByIds = tCompanyCarrierMapper.selectCompanyCarrierInfoByIds(companyCarrierIdList);
            //拼接数据
            if (ListUtils.isNotEmpty(companyCarrierInfoByIds)) {
                //转 车主id-车主信息 map
                Map<Long, FuzzySearchCompanyCarrierResponseModel> carrierInfoMap = companyCarrierInfoByIds.stream().collect(Collectors.toMap(FuzzySearchCompanyCarrierResponseModel::getCompanyId, item -> item));
                for (SearchStaffVehicleListResponseModel staffVehicleModel : staffVehicleList) {
                    FuzzySearchCompanyCarrierResponseModel carrierInfoModel = carrierInfoMap.get(staffVehicleModel.getCompanyCarrierId());
                    if (carrierInfoModel != null) {
                        staffVehicleModel.setCompanyCarrierType(carrierInfoModel.getCompanyType());
                        staffVehicleModel.setCompanyCarrierName(carrierInfoModel.getCompanyName());
                        staffVehicleModel.setCarrierContactName(carrierInfoModel.getContactName());
                        staffVehicleModel.setCarrierContactMobile(carrierInfoModel.getContactPhone());
                    }
                }
            }
        }
        return new PageInfo<>(ListUtils.isNotEmpty(staffVehicleList) ? staffVehicleList : new ArrayList<>());
    }

    /**
     * 新增/修改
     *
     * @param requestModel
     */
    @Transactional
    public void saveOrModifyStaffVehicle(SaveOrModifyStaffVehicleRequestModel requestModel) {
        //车辆机构
        if (!VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(requestModel.getType())
                && !VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(requestModel.getType())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_PROPERTY_NOT_EXIST);
        }
        //车辆类别
        if (!VehicleCategoryEnum.TRACTOR.getKey().equals(requestModel.getVehicleCategory())
                && !VehicleCategoryEnum.WHOLE.getKey().equals(requestModel.getVehicleCategory())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_CATEGORY_NOT_EXIST);
        }

        //司机
        TStaffBasic dbStaffBasic = tqStaffBasicMapper.selectByPrimaryKey(requestModel.getStaffId());
        if (dbStaffBasic == null
                || (!StaffTypeEnum.DRIVER.getKey().equals(dbStaffBasic.getType()) && !StaffTypeEnum.DRIVER_SUPERCARGO.getKey().equals(dbStaffBasic.getType()))
                || StaffPropertyEnum.EXTERNAL_STAFF.getKey().equals(dbStaffBasic.getStaffProperty())) {//禁止选择外部司机
            throw new BizException(CarrierDataExceptionEnum.DRIVER_NOT_EXIST);
        }

        VehicleBasicPropertyModel dbOneVehicleInfo;//一体车
        VehicleBasicPropertyModel dbTractorVehicleInfo;//牵引车
        VehicleBasicPropertyModel dbTrailerVehicleInfo = null;//挂车

        //入参 - 牵引车或挂车校验
        if (VehicleCategoryEnum.TRACTOR.getKey().equals(requestModel.getVehicleCategory())) {
            //查询牵引车
            dbTractorVehicleInfo = tqVehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getTractorVehicleId());
            //判断牵引车是否存在并判断是否为所选机构类型
            if (dbTractorVehicleInfo == null
                    || !VehicleCategoryEnum.TRACTOR.getKey().equals(dbTractorVehicleInfo.getVehicleCategory())
                    || !requestModel.getType().equals(dbTractorVehicleInfo.getVehicleProperty())
                    || VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(dbTractorVehicleInfo.getVehicleProperty())) {//禁止选择外部车辆
                throw new BizException(CarrierDataExceptionEnum.TRAILER_VEHICLE_NOT_EXIST);
            }

            //判断挂车是否存在并判断是否为所选机构类型
            if (requestModel.getTrailerVehicleId() != null) {
                //查询挂车
                dbTrailerVehicleInfo = tqVehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getTrailerVehicleId());
                if (dbTrailerVehicleInfo == null
                        || !VehicleCategoryEnum.TRAILER.getKey().equals(dbTrailerVehicleInfo.getVehicleCategory())
                        || !requestModel.getType().equals(dbTrailerVehicleInfo.getVehicleProperty())
                        || VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(dbTrailerVehicleInfo.getVehicleProperty())) {//禁止选择外部车辆
                    throw new BizException(CarrierDataExceptionEnum.TRACTOR_VEHICLE_NOT_EXIST);
                }
            }
        }

        //入参 - 一体车校验
        if (VehicleCategoryEnum.WHOLE.getKey().equals(requestModel.getVehicleCategory())) {
            //查询一体车
            dbOneVehicleInfo = tqVehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getOneVehicleId());
            //判断一体车是否存在并判断是否为所选机构类型
            if (dbOneVehicleInfo == null
                    || !VehicleCategoryEnum.WHOLE.getKey().equals(dbOneVehicleInfo.getVehicleCategory())
                    || !requestModel.getType().equals(dbOneVehicleInfo.getVehicleProperty())
                    || VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(dbOneVehicleInfo.getVehicleProperty())) {//禁止选择外部车辆
                throw new BizException(CarrierDataExceptionEnum.ONE_VEHICLE_NOT_EXIST);
            }
        }

        //判断司机机构和车辆机构是否匹配(自主司机-自营车辆，自主司机-自主车辆，自营车辆-自营司机)
        if (VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(requestModel.getType())) {//自主车
            //自主车辆只能关联自主司机
            if (!StaffPropertyEnum.OWN_STAFF.getKey().equals(dbStaffBasic.getStaffProperty())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_DRIVER_ASSOCIATED_WITH_THE_SAME_INSTITUTION);
            }
        } else {//自营车
            //自营车辆可以关联自主/自营司机
            if (!(StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(dbStaffBasic.getStaffProperty()) ||
                    StaffPropertyEnum.OWN_STAFF.getKey().equals(dbStaffBasic.getStaffProperty()))) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_DRIVER_ASSOCIATED_WITH_THE_SAME_INSTITUTION);
            }
        }

        List<TStaffVehicleRelation> tractorVehicleRelList;
        List<TStaffVehicleRelation> trailerVehicleRelList;
        List<TStaffVehicleRelation> oneVehicleRelList;
        List<TStaffVehicleRelationModel> staffRelList;
        List<TStaffVehicleRelationModel> vehicleRelList;

        //牵引车/一体车ID
        Long mainVehicleId = requestModel.getTractorVehicleId() == null || requestModel.getTractorVehicleId() == 0L ? requestModel.getOneVehicleId() : requestModel.getTractorVehicleId();
        //挂车ID
        Long trailerVehicleId = dbTrailerVehicleInfo != null ? dbTrailerVehicleInfo.getVehicleId() : null;
        //驾驶员ID
        Long staffId = dbStaffBasic.getId();

        if (requestModel.getStaffVehicleRelationId() == null || requestModel.getStaffVehicleRelationId() <= CommonConstant.LONG_ZERO) { //新增

            //查询是否已存在车辆司机关联关系
            Long relId = tqStaffVehicleRelationMapper.getRelationKeyById(staffId, mainVehicleId, trailerVehicleId);
            if (relId != null) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_HAS_EXIST);
            }

            //内部牵引车 检验关系是否已存在
            if (VehicleCategoryEnum.TRACTOR.getKey().equals(requestModel.getVehicleCategory()) && (VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(requestModel.getType()) || VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(requestModel.getType()))) {
                tractorVehicleRelList = tqStaffVehicleRelationMapper.findRelationByVehicleId(mainVehicleId);
                if (ListUtils.isNotEmpty(tractorVehicleRelList)) {
                    throw new BizException(CarrierDataExceptionEnum.STAFF_Q_VEHICLE_RELATION_HAS_EXIST);
                }

                if (dbTrailerVehicleInfo != null) {
                    trailerVehicleRelList = tqStaffVehicleRelationMapper.findRelationByTrailerVehicleId(trailerVehicleId);
                    if (ListUtils.isNotEmpty(trailerVehicleRelList)) {
                        throw new BizException(CarrierDataExceptionEnum.STAFF_G_VEHICLE_RELATION_HAS_EXIST);
                    }
                }

                staffRelList = tqStaffVehicleRelationMapper.getRelByStaffId(dbStaffBasic.getId());
                if (ListUtils.isNotEmpty(staffRelList)) {
                    TStaffVehicleRelationModel tempRelModel = staffRelList.stream().filter(o -> VehicleCategoryEnum.TRACTOR.getKey().equals(o.getVehicleCategory())).findFirst().orElse(null);
                    if (tempRelModel != null) {
                        throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_HAS_EXIST);
                    }
                }
            }

            //内部一体车 检验关系是否已存在
            if (VehicleCategoryEnum.WHOLE.getKey().equals(requestModel.getVehicleCategory()) && (VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(requestModel.getType()) || VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(requestModel.getType()))) {
                oneVehicleRelList = tqStaffVehicleRelationMapper.findRelationByVehicleId(mainVehicleId);
                if (ListUtils.isNotEmpty(oneVehicleRelList)) {
                    throw new BizException(CarrierDataExceptionEnum.STAFF_O_VEHICLE_RELATION_HAS_EXIST);
                }
                staffRelList = tqStaffVehicleRelationMapper.getRelByStaffId(dbStaffBasic.getId());
                if (ListUtils.isNotEmpty(staffRelList)) {
                    TStaffVehicleRelationModel tempRelModel = staffRelList.stream().filter(o -> VehicleCategoryEnum.WHOLE.getKey().equals(o.getVehicleCategory())).findFirst().orElse(null);
                    if (tempRelModel != null) {
                        throw new BizException(CarrierDataExceptionEnum.STAFF_O_VEHICLE_RELATION_HAS_EXIST);
                    }
                }
            }

            //新增
            TStaffVehicleRelation addStaffVehicleRelation = new TStaffVehicleRelation();
            addStaffVehicleRelation.setCompanyCarrierId(commonBiz.getQiyaCompanyCarrierId());
            addStaffVehicleRelation.setStaffId(staffId);
            addStaffVehicleRelation.setVehicleId(mainVehicleId);
            addStaffVehicleRelation.setTrailerVehicleId(trailerVehicleId);
            addStaffVehicleRelation.setRemark(requestModel.getRemark());
            commonBiz.setBaseEntityAdd(addStaffVehicleRelation, BaseContextHandler.getUserName());
            tqStaffVehicleRelationMapper.insertSelective(addStaffVehicleRelation);

        }else {//修改
            TStaffVehicleRelation dbVehicleRelation = tqStaffVehicleRelationMapper.selectByPrimaryKey(requestModel.getStaffVehicleRelationId());
            if (dbVehicleRelation == null) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_NOT_EXIST);
            }
            Long relId = tqStaffVehicleRelationMapper.getRelationKeyById(staffId, mainVehicleId, trailerVehicleId);
            if (relId != null && !relId.equals(dbVehicleRelation.getId())) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_HAS_EXIST);
            }

            staffRelList = tqStaffVehicleRelationMapper.getRelByStaffId(staffId);
            vehicleRelList = tqStaffVehicleRelationMapper.getRelByVehicleId(mainVehicleId);

            //内部牵引车 检验关系是否已存在
            if (VehicleCategoryEnum.TRACTOR.getKey().equals(requestModel.getVehicleCategory()) && (VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(requestModel.getType()) || VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(requestModel.getType()))) {
                if (ListUtils.isNotEmpty(staffRelList)) {
                    List<Long> relationIdByStaffIdList = staffRelList.stream().filter(o -> !VehicleCategoryEnum.WHOLE.getKey().equals(o.getVehicleCategory())).map(TStaffVehicleRelationModel::getId).distinct().collect(Collectors.toList());
                    if (ListUtils.isNotEmpty(relationIdByStaffIdList) && !relationIdByStaffIdList.contains(dbVehicleRelation.getId())) {
                        throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_HAS_EXIST);
                    }
                }

                if (ListUtils.isNotEmpty(vehicleRelList)) {
                    List<Long> relationIdByVehicleIdList = vehicleRelList.stream().filter(o -> !VehicleCategoryEnum.WHOLE.getKey().equals(o.getVehicleCategory())).map(TStaffVehicleRelationModel::getId).distinct().collect(Collectors.toList());
                    if (ListUtils.isNotEmpty(relationIdByVehicleIdList) && !relationIdByVehicleIdList.contains(dbVehicleRelation.getId())) {
                        throw new BizException(CarrierDataExceptionEnum.STAFF_Q_VEHICLE_RELATION_HAS_EXIST);
                    }
                }

                if (dbTrailerVehicleInfo != null) {
                    List<TStaffVehicleRelation> relList = tqStaffVehicleRelationMapper.findRelationByTrailerVehicleId(trailerVehicleId);
                    if (ListUtils.isNotEmpty(relList)) {
                        List<Long> idList = relList.stream().map(TStaffVehicleRelation::getId).collect(Collectors.toList());
                        if (!idList.contains(dbVehicleRelation.getId())) {
                            throw new BizException(CarrierDataExceptionEnum.STAFF_G_VEHICLE_RELATION_HAS_EXIST);
                        }
                    }
                }
            }

            //内部一体车 检验关系是否已存在
            if (VehicleCategoryEnum.WHOLE.getKey().equals(requestModel.getVehicleCategory()) && (VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(requestModel.getType()) || VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(requestModel.getType()))) {
                if (ListUtils.isNotEmpty(staffRelList)) {
                    List<Long> relationIdByStaffIdList = staffRelList.stream().filter(o -> VehicleCategoryEnum.WHOLE.getKey().equals(o.getVehicleCategory())).map(TStaffVehicleRelationModel::getId).distinct().collect(Collectors.toList());
                    if (ListUtils.isNotEmpty(relationIdByStaffIdList) && !relationIdByStaffIdList.contains(dbVehicleRelation.getId())) {
                        throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_HAS_EXIST);
                    }
                }

                if (ListUtils.isNotEmpty(vehicleRelList)) {
                    List<Long> relationIdByVehicleIdList = vehicleRelList.stream().filter(o -> VehicleCategoryEnum.WHOLE.getKey().equals(o.getVehicleCategory())).map(TStaffVehicleRelationModel::getId).distinct().collect(Collectors.toList());
                    if (ListUtils.isNotEmpty(relationIdByVehicleIdList) && !relationIdByVehicleIdList.contains(dbVehicleRelation.getId())) {
                        throw new BizException(CarrierDataExceptionEnum.STAFF_O_VEHICLE_RELATION_HAS_EXIST);
                    }
                }
            }

            TStaffVehicleRelation upTractorStaffVehicle = new TStaffVehicleRelation();
            upTractorStaffVehicle.setId(dbVehicleRelation.getId());
            upTractorStaffVehicle.setStaffId(staffId);
            upTractorStaffVehicle.setVehicleId(mainVehicleId);
            upTractorStaffVehicle.setTrailerVehicleId(trailerVehicleId);
            upTractorStaffVehicle.setRemark(requestModel.getRemark());
            commonBiz.setBaseEntityModify(upTractorStaffVehicle, BaseContextHandler.getUserName());
            tqStaffVehicleRelationMapper.updateByPrimaryKeySelectiveExt(upTractorStaffVehicle);
        }
    }

    /**
     * 新增其他车主车辆司机关联关系
     *
     * @param requestModel 请求信息
     */
    @Transactional
    public void saveOrModifyOtherStaffVehicle(SaveOrModifyStaffVehicleRequestModel requestModel) {
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            /*前台请求*/
            Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }
            requestModel.setCompanyCarrierId(loginUserCompanyCarrierId);
        }

        //查询其他车主是否存在
        CompanyCarrierDetailResponseModel companyCarrierDetail = tCompanyCarrierMapper.getCompanyCarrierDetailById(requestModel.getCompanyCarrierId());
        if (companyCarrierDetail == null) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }

        //查询当前车主下是否关联的有要添加的车辆
        TCarrierVehicleRelation carrierVehicleRelation = tCarrierVehicleRelationMapper.getByCompanyCarrierIdAndVehicleId(companyCarrierDetail.getCompanyCarrierId(), requestModel.getVehicleId());
        if (carrierVehicleRelation == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }

        //查询当前车主下是否关联的有要添加的司机
        TCarrierDriverRelation carrierDriverRelation = tCarrierDriverRelationMapper.getByCompanyCarrierIdAndDriverId(companyCarrierDetail.getCompanyCarrierId(), requestModel.getStaffId(), EnabledEnum.ENABLED.getKey());
        if (carrierDriverRelation == null) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_NOT_EXIST);
        }

        //查询是否已经存在关联关系
        TStaffVehicleRelation staffVehicleRelation = tqStaffVehicleRelationMapper.getByVehicleIdAndDriverId(companyCarrierDetail.getCompanyCarrierId(), requestModel.getVehicleId(), requestModel.getStaffId());
        if (staffVehicleRelation != null) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_HAS_EXIST);
        }

        if (requestModel.getStaffVehicleRelationId() != null) {
            /*修改*/
            TStaffVehicleRelation upTractorStaffVehicle = new TStaffVehicleRelation();
            upTractorStaffVehicle.setId(requestModel.getStaffVehicleRelationId());
            upTractorStaffVehicle.setStaffId(carrierDriverRelation.getDriverId());
            upTractorStaffVehicle.setVehicleId(carrierVehicleRelation.getVehicleId());
            upTractorStaffVehicle.setRemark(requestModel.getRemark());
            commonBiz.setBaseEntityModify(upTractorStaffVehicle, BaseContextHandler.getUserName());
            tqStaffVehicleRelationMapper.updateByPrimaryKeySelectiveExt(upTractorStaffVehicle);
        } else {
            /*新增*/
            TStaffVehicleRelation addStaffVehicleRelation = new TStaffVehicleRelation();
            addStaffVehicleRelation.setCompanyCarrierId(companyCarrierDetail.getCompanyCarrierId());
            addStaffVehicleRelation.setStaffId(carrierDriverRelation.getDriverId());
            addStaffVehicleRelation.setVehicleId(carrierVehicleRelation.getVehicleId());
            addStaffVehicleRelation.setRemark(requestModel.getRemark());
            commonBiz.setBaseEntityAdd(addStaffVehicleRelation, BaseContextHandler.getUserName());
            tqStaffVehicleRelationMapper.insertSelective(addStaffVehicleRelation);
        }
    }

    /**
     * 详情
     *
     * @param requestModel
     * @return
     */
    public StaffVehicleDetailResponseModel getDetail(StaffVehicleDetailRequestModel requestModel) {
        Long loginUserCompanyCarrierId = CommonConstant.LONG_ZERO;
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            /*前台请求*/
            loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }
        }

        StaffVehicleRelationModel dbVehicleRelation = tqStaffVehicleRelationMapper.findRelationByPrimaryId(requestModel.getStaffVehicleId());
        if (dbVehicleRelation == null || (CommonConstant.INTEGER_TWO.equals(requestModel.getSource()) && !loginUserCompanyCarrierId.equals(dbVehicleRelation.getCompanyCarrierId()))) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_NOT_EXIST);
        }

        StaffVehicleDetailResponseModel responseModel = new StaffVehicleDetailResponseModel();
        responseModel.setStaffVehicleRelationId(dbVehicleRelation.getVehicleRelationId());
        responseModel.setType(dbVehicleRelation.getVehicleProperty());
        responseModel.setVehicleCategory(dbVehicleRelation.getVehicleCategory());
        responseModel.setStaffId(dbVehicleRelation.getStaffId());
        responseModel.setStaffName(dbVehicleRelation.getStaffName());
        responseModel.setStaffPhoneNumber(dbVehicleRelation.getStaffPhone());
        responseModel.setTractorVehicleId(dbVehicleRelation.getVehicleId());
        responseModel.setTractorVehicleNo(dbVehicleRelation.getVehicleNo());
        responseModel.setTrailerVehicleId(dbVehicleRelation.getTrailerVehicleId());
        responseModel.setTrailerVehicleNo(dbVehicleRelation.getTrailerVehicleNo());
        responseModel.setRemark(dbVehicleRelation.getRemark());
        return responseModel;
    }

    /**
     * 删除车辆司机关联关系
     *
     * @param requestModel 关联id
     */
    @Transactional
    public void deleteStaffVehicle(DeleteStaffVehicleRequestModel requestModel){
        TStaffVehicleRelation dbVehicleRelation = tqStaffVehicleRelationMapper.selectByPrimaryKey(requestModel.getStaffVehicleId());
        if(dbVehicleRelation == null){
            throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_NOT_EXIST);
        }

        TStaffVehicleRelation updateModel = new TStaffVehicleRelation();
        updateModel.setId(dbVehicleRelation.getId());
        updateModel.setValid(IfValidEnum.INVALID.getKey());
        updateModel.setLastModifiedBy(BaseContextHandler.getUserName());
        updateModel.setLastModifiedTime(new Date());
        tqStaffVehicleRelationMapper.updateByPrimaryKeySelective(updateModel);
    }

    /**
     * 批量删除车辆司机关联关系(前台)
     *
     * @param requestModel 关联id
     */
    @Transactional
    public void batchDeleteStaffVehicleRelation(BatchDeleteStaffVehicleRelationRequestModel requestModel) {
        /*前台请求*/
        Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }
        //查询关联关系
        List<TStaffVehicleRelation> tStaffVehicleRelList = tqStaffVehicleRelationMapper.selectRelationsByIdsCarrierId(requestModel.getVehicleStaffIds(), loginUserCompanyCarrierId);
        if (ListUtils.isEmpty(tStaffVehicleRelList)) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_NOT_EXIST);
        }

        TStaffVehicleRelation updateModel;
        List<TStaffVehicleRelation> updateModelList = new ArrayList<>();
        for (TStaffVehicleRelation staffVehicleRelation : tStaffVehicleRelList) {
            updateModel = new TStaffVehicleRelation();
            updateModel.setId(staffVehicleRelation.getId());
            updateModel.setValid(IfValidEnum.INVALID.getKey());
            commonBiz.setBaseEntityModify(updateModel, BaseContextHandler.getUserName());
            updateModelList.add(updateModel);
        }
        tqStaffVehicleRelationMapper.batchUpdate(updateModelList);
    }

    /**
     * 导入
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public ImportStaffVehicleResponseModel importStaffVehicle(ImportStaffVehicleRequestModel requestModel) {
        ImportStaffVehicleResponseModel responseModel = new ImportStaffVehicleResponseModel();
        responseModel.initNumber(requestModel.getNumberFailures());
        List<StaffVehicleRelationRequestModel> importList = requestModel.getImportList();

        if (ListUtils.isEmpty(importList)) {
            return responseModel;
        }

        for (StaffVehicleRelationRequestModel tempModel : importList) {
            try {
                this.importSingleStaffVehicleInfo(tempModel);
                responseModel.addSuccessful();
            } catch (Exception e) {
                log.info("车辆司机关系导入失败: " + e.getMessage());
                responseModel.addFailures();
            }
        }

        return responseModel;
    }

    /**
     * 页面导入数据
     * @param rel
     */
    @Transactional
    public void importSingleStaffVehicleInfo(StaffVehicleRelationRequestModel rel) {
        if (rel == null
                || StringUtils.isBlank(rel.getTractorVehicleNo())
                || StringUtils.isBlank(rel.getStaffName())
                || StringUtils.isBlank(rel.getStaffMobile())
                || rel.getType() == null
                || rel.getVehicleCategory() == null) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_NOT_EXIST);
        }

        //我司
        Long qiyaCompanyCarrierId = commonBiz.getQiyaCompanyCarrierId();

        //司机
        TStaffBasic dbTStaffBasic = tqStaffBasicMapper.getByMobile(rel.getStaffMobile());
        if (dbTStaffBasic == null || !rel.getStaffName().equals(dbTStaffBasic.getName())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_NOT_EXIST);
        }
        //我司是否有此司机
        TCarrierDriverRelation carrierDriverRelation = tCarrierDriverRelationMapper.getByCompanyCarrierIdAndDriverId(qiyaCompanyCarrierId, dbTStaffBasic.getId(), null);
        if (carrierDriverRelation == null) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_NOT_EXIST);
        }

        //车辆
        TVehicleBasic dbTVehicleBasicOne = tqVehicleBasicMapper.getInfoByVehicleNo(rel.getTractorVehicleNo());
        if (dbTVehicleBasicOne == null) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_NOT_EXIST);
        }
        //我司是否有此车辆
        TCarrierVehicleRelation carrierVehicleRelation = tCarrierVehicleRelationMapper.getByCompanyCarrierIdAndVehicleId(qiyaCompanyCarrierId, dbTVehicleBasicOne.getId());
        if (carrierVehicleRelation == null) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_NOT_EXIST);
        }

        TVehicleBasic dbTVehicleBasicTwo = null;
        if (StringUtils.isNotEmpty(rel.getTrailerVehicleNo())) {
            dbTVehicleBasicTwo = tqVehicleBasicMapper.getInfoByVehicleNo(rel.getTrailerVehicleNo());
            if (dbTVehicleBasicTwo == null) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_NOT_EXIST);
            }
            //我司是否有此车辆
            TCarrierVehicleRelation carrierVehicleRelationTwo = tCarrierVehicleRelationMapper.getByCompanyCarrierIdAndVehicleId(qiyaCompanyCarrierId, dbTVehicleBasicTwo.getId());
            if (carrierVehicleRelationTwo == null) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_NOT_EXIST);
            }
        }

        //新增
        Long trailerVehicleId = null;
        SaveOrModifyStaffVehicleRequestModel saveOrUpdateModel = new SaveOrModifyStaffVehicleRequestModel();
        saveOrUpdateModel.setType(rel.getType());
        saveOrUpdateModel.setVehicleCategory(rel.getVehicleCategory());
        saveOrUpdateModel.setRemark(rel.getRemark());
        saveOrUpdateModel.setStaffId(dbTStaffBasic.getId());
        if (VehicleCategoryEnum.WHOLE.getKey().equals(rel.getVehicleCategory())) {
            saveOrUpdateModel.setOneVehicleId(dbTVehicleBasicOne.getId());
        }else{
            saveOrUpdateModel.setTractorVehicleId(dbTVehicleBasicOne.getId());
        }
        if(dbTVehicleBasicTwo != null){
            saveOrUpdateModel.setTrailerVehicleId(dbTVehicleBasicTwo.getId());
            trailerVehicleId = dbTVehicleBasicTwo.getId();
        }

        Long relId = tqStaffVehicleRelationMapper.getRelationKeyById(dbTStaffBasic.getId(),dbTVehicleBasicOne.getId(),trailerVehicleId);

        if(relId!=null){
            TStaffVehicleRelation updateModel = new TStaffVehicleRelation();
            updateModel.setId(relId);
            updateModel.setRemark(rel.getRemark());
            commonBiz.setBaseEntityModify(updateModel,BaseContextHandler.getUserName());
            tqStaffVehicleRelationMapper.updateByPrimaryKeySelective(updateModel);
        }else{
            this.saveOrModifyStaffVehicle(saveOrUpdateModel);
        }
    }

    /**
     *  同步更新车辆GPS信息
     * @param vehicleBasicModel 车辆信息
     * @param staffBasicModel 司机信息
     * @param dispatchOrderId 调度单ID
     * @param dispatchOrderCode 调度单号
     */
    @Transactional
    public void syncVehicleGpsInfo(TVehicleBasicModel vehicleBasicModel, TStaffBasicModel staffBasicModel, Long dispatchOrderId, String dispatchOrderCode, String userName) {
        if (vehicleBasicModel == null || staffBasicModel == null) {
            return;
        }
        TVehicleGps tVehicleGps = tVehicleGpsMapper.selectVehicleByVehicleId(vehicleBasicModel.getVehicleBasicId());
        if (tVehicleGps == null) {
            TVehicleGps newVehicleGPS = new TVehicleGps();
            newVehicleGPS.setVehicleId(vehicleBasicModel.getVehicleBasicId());
            newVehicleGPS.setVehicleNo(vehicleBasicModel.getVehicleNo());
            newVehicleGPS.setDriverId(staffBasicModel.getStaffBasicId());
            newVehicleGPS.setDriverName(staffBasicModel.getName());
            newVehicleGPS.setDriverMobile(staffBasicModel.getMobile());
            newVehicleGPS.setDriverIdentity(staffBasicModel.getIdentityNumber());
            newVehicleGPS.setDispatchOrderId(dispatchOrderId);
            newVehicleGPS.setDispatchOrderCode(dispatchOrderCode);
            commonBiz.setBaseEntityAdd(newVehicleGPS, userName);
            tVehicleGpsMapper.insertSelective(newVehicleGPS);
        } else {
            TVehicleGps upVehicleGPS = new TVehicleGps();
            upVehicleGPS.setId(tVehicleGps.getId());
            upVehicleGPS.setVehicleId(vehicleBasicModel.getVehicleBasicId());
            upVehicleGPS.setVehicleNo(vehicleBasicModel.getVehicleNo());
            upVehicleGPS.setDriverId(staffBasicModel.getStaffBasicId());
            upVehicleGPS.setDriverName(staffBasicModel.getName());
            upVehicleGPS.setDriverMobile(staffBasicModel.getMobile());
            upVehicleGPS.setDriverIdentity(staffBasicModel.getIdentityNumber());
            upVehicleGPS.setDispatchOrderId(dispatchOrderId);
            upVehicleGPS.setDispatchOrderCode(dispatchOrderCode);
            commonBiz.setBaseEntityModify(upVehicleGPS, userName);
            tVehicleGpsMapper.updateByPrimaryKeySelective(upVehicleGPS);
        }
    }

    /**
     * 新增GPS信息
     * @param vehicleBasicModel
     * @param staffBasicModel
     */
    public void addVehicleGpsInfo(TVehicleBasicModel vehicleBasicModel,  TStaffBasicModel staffBasicModel) {
        TVehicleGps tVehicleGps = tVehicleGpsMapper.selectVehicleByVehicleId(vehicleBasicModel.getVehicleBasicId());
        TVehicleGps vehicleGPS = new TVehicleGps();
        vehicleGPS.setVehicleId(vehicleBasicModel.getVehicleBasicId());
        vehicleGPS.setVehicleNo(vehicleBasicModel.getVehicleNo());
        if(staffBasicModel!=null){
            vehicleGPS.setDriverId(staffBasicModel.getStaffBasicId());
            vehicleGPS.setDriverName(staffBasicModel.getName());
            vehicleGPS.setDriverMobile(staffBasicModel.getMobile());
            vehicleGPS.setDriverIdentity(staffBasicModel.getIdentityNumber());
        }
        if (tVehicleGps == null) {
            commonBiz.setBaseEntityAdd(vehicleGPS, BaseContextHandler.getUserName());
            tVehicleGpsMapper.insertSelective(vehicleGPS);
        } else {
            commonBiz.setBaseEntityModify(vehicleGPS, BaseContextHandler.getUserName());
            tVehicleGpsMapper.updateByPrimaryKeySelective(vehicleGPS);
        }
    }

    /**
     * 获取车辆检查车辆司机列表
     * @param requestModel
     * @return
     */
    public List<SafeCheckStaffRelResponseModel> getVehicleStaffList(SafeCheckStaffRelRequestModel requestModel){
        return tqStaffVehicleRelationMapper.findRel4VehicleSafeCheck(requestModel.getVehicleNo(), null);
    }

    /**
     * 模糊查询司机信息(车辆机构自营，搜索自主和自营司机)
     *
     * @param
     * @return
     */
    public List<GetFuzzyQueryDriverInfoResponseModel> fuzzyQueryDriverInfo(GetFuzzyQueryDriverInfoRequestModel requestModel) {
        //默认查我司的司机
        if (requestModel.getCompanyCarrierId() == null || CommonConstant.LONG_ZERO.equals(requestModel.getCompanyCarrierId())) {
            /*我司*/
            requestModel.setCompanyCarrierId(commonBiz.getQiyaCompanyCarrierId());
        }
        return tqStaffBasicMapper.getFuzzyQueryDriverInfo(requestModel);
    }
}
