package com.logistics.management.webapi.controller.routeconfig.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RouteDistanceConfigDetailResponseDto {

    @ApiModelProperty(value = "路线距离配置Id")
    private String routeDistanceConfigId = "";

    @ApiModelProperty(value = "发货区; 省市区拼接")
    private String fromAreaName = "";

    @ApiModelProperty(value = "卸货区; 省市区拼接")
    private String toAreaName = "";

    @ApiModelProperty(value = "发货区id", required = true)
    private String fromAreaId = "";

    @ApiModelProperty(value = "收货区id", required = true)
    private String toAreaId = "";

    @ApiModelProperty(value = "计费距离（KM）")
    private String billingDistance = "";
}
