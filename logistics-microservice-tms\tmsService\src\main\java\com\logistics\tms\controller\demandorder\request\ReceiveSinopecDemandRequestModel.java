package com.logistics.tms.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author:lei.zhu
 * @date:2021/12/3 16:45:31
 */
@Data
public class ReceiveSinopecDemandRequestModel {

    @ApiModelProperty("集成消息统一编码 基于32位大写GUID表示")
    private String imGuid; //43BAC2C6CAB42EC15B05ABC62C79CB8E  注：随机生成GUID

    @ApiModelProperty("发送时间格式：YYYYMMDDHHMMSS")
    private String sendTime;   // 20210401235959 注：取当前时间

    @ApiModelProperty("发送方系统编码")
    private String sender; // LISBS

    @ApiModelProperty("接收方系统编码")
    private String receiver; //LEJU  乐橘为LEJU

    @ApiModelProperty("接收方接口编码")
    private String receiveIfid; // getConsignOrder

    @ApiModelProperty("接收方目标方法/函数名")
    private String receiveMethod;  // getConsignOrder

    @ApiModelProperty("发送方系统业务操作人账号，不涉及具体操作人填写NULL")
    private String sendOperator;  //system

    @ApiModelProperty("服务商代码")
    private String carrierCode;  //0041763020

    @ApiModelProperty("货主")
    private String consignName;  //华北分公司

    @ApiModelProperty("订单号")
    private String orderNo;   //2600141565

    @ApiModelProperty("生产企业")
    private String manufacturerName;  // 燕山分公司

    @ApiModelProperty("凭证日期")
    private Date expectExeDate; //  2021-12-02

    @ApiModelProperty("运输方式")
    private String transMode;  // 公路运输

    @ApiModelProperty("产品大类")
    private String itemCategory1Name; //合成树脂

    @ApiModelProperty("产品小类")
    private String itemCategoryName;  //低密度聚乙烯

    @ApiModelProperty("产品代码")
    private String itemCode;   // 001201020160098123F

    @ApiModelProperty("产品名称")
    private String itemName;  //低密度聚乙烯PEM187

    @ApiModelProperty("物料运输组")
    private String itemTransGroupName; //共享托盘

    @ApiModelProperty("包装规格")
    private String itemPackSpecName; //1.25吨/托

    @ApiModelProperty("数量")
    private BigDecimal qty;   //30

    @ApiModelProperty("计量单位")
    private String uomName;   //吨

    @ApiModelProperty("委托单号")
    private String sn;   //TC2021101500001A

    @ApiModelProperty("发货仓库")
    private String outWarehouseName;   //燕山石化驻厂办

    @ApiModelProperty("发运城市")
    private String loadZoneName;   //房山区

    @ApiModelProperty("发运城市编码")
    private String loadZoneCode;  //110111

    @ApiModelProperty("送达城市编码")
    private String recvDistrictCode;  //120101

    @ApiModelProperty("送达城市")
    private String recvDistrictName;  // 和平区

    @ApiModelProperty("订单有效期")
    private Date invalidatingStartDate;  //2021-12-31必须提货时间

    @ApiModelProperty("备注")
    private String remark; //目前送达时限，通过备注字段传输

    @ApiModelProperty("备用字段1")
    private String remark1;
    @ApiModelProperty("备用字段2")
    private String remark2;
    @ApiModelProperty("备用字段3")
    private String remark3;
    @ApiModelProperty("备用字段4")
    private String remark4;
    @ApiModelProperty("备用字段5")
    private String remark5;

    @ApiModelProperty("请求ip")
    private String ip;
}
