package com.logistics.management.webapi.api.impl.driversafepromise;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.driversafepromise.DriverSafePromiseApi;
import com.logistics.management.webapi.api.feign.driversafepromise.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.driversafepromise.mapping.GetSignDetailMapping;
import com.logistics.management.webapi.api.impl.driversafepromise.mapping.SafePromiseDetailMapping;
import com.logistics.management.webapi.api.impl.driversafepromise.mapping.SearchSignListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.tms.api.feign.driversafepromise.DriverSafePromiseServiceApi;
import com.logistics.tms.api.feign.driversafepromise.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/11/5 10:53
 */
@RestController
public class DriverSafePromiseApiImpl implements DriverSafePromiseApi {
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private DriverSafePromiseServiceApi driverSafePromiseServiceApi;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchSafePromiseListResponseDto>> searchList(@RequestBody SearchSafePromiseListRequestDto requestDto) {
        SearchSafePromiseListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchSafePromiseListRequestModel.class);
        Result<PageInfo<SearchSafePromiseListResponseModel>> result = driverSafePromiseServiceApi.searchList(requestModel);
        result.throwException();

        PageInfo dtoPageInfo = result.getData();
        List<SearchSafePromiseListResponseDto> dtoList = MapperUtils.mapper(result.getData().getList(),SearchSafePromiseListResponseDto.class);
        dtoPageInfo.setList(dtoList == null ? new ArrayList():dtoList);
        return Result.success(dtoPageInfo);
    }

    /**
     * 新增
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addSafePromise(@RequestBody @Valid AddSafePromiseRequestDto requestDto) {
        Result<Boolean> result = driverSafePromiseServiceApi.addSafePromise(MapperUtils.mapper(requestDto, AddSafePromiseRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<SafePromiseDetailResponseDto> getDetail(@RequestBody @Valid SafePromiseDetailRequestDto requestDto) {
        Result<SafePromiseDetailResponseModel> result = driverSafePromiseServiceApi.getDetail(MapperUtils.mapper(requestDto, SafePromiseDetailRequestModel.class));
        result.throwException();
        result.getData().setAttachmentUrl(commonBiz.getImageURL(result.getData().getAttachmentUrl()));
        return Result.success(MapperUtils.mapper(result.getData(),SafePromiseDetailResponseDto.class,new SafePromiseDetailMapping(configKeyConstant)));
    }

    /**
     * 删除
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> delSafePromise(@RequestBody @Valid DeleteSafePromiseRequestDto requestDto) {
        Result<Boolean> result = driverSafePromiseServiceApi.delSafePromise(MapperUtils.mapper(requestDto,DeleteSafePromiseRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 补发
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> reissueSafePromise(@RequestBody @Valid ReissueSavePromiseRequestDto requestDto) {
        Result<Boolean> result = driverSafePromiseServiceApi.reissueSafePromise(MapperUtils.mapper(requestDto,ReissueSavePromiseRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 签订列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchSignSafePromiseListResponseDto>> searchSignList(@RequestBody SearchSignSafePromiseListRequestDto requestDto) {
        Result<PageInfo<SearchSignSafePromiseListResponseModel>> result = driverSafePromiseServiceApi.searchSignList(MapperUtils.mapper(requestDto,SearchSignSafePromiseListRequestModel.class));
        result.throwException();

        PageInfo dtoPageInfo = result.getData();
        List<String> sourceSrcList=new ArrayList<>();
        for (SearchSignSafePromiseListResponseModel responseModel : result.getData().getList()) {
            sourceSrcList.add(responseModel.getHandPromiseUrl());
            sourceSrcList.add(responseModel.getSignResponsibilityUrl());
        }
        Map<String, String> stringStringMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        List<SearchSignSafePromiseListResponseDto> dtoList =MapperUtils.mapper(result.getData().getList(),SearchSignSafePromiseListResponseDto.class,new SearchSignListMapping(configKeyConstant,stringStringMap));
        dtoPageInfo.setList(dtoList == null ? new ArrayList() : dtoList);
        return Result.success(dtoPageInfo);
    }

    /**
     * 签订详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<SignSafePromiseDetailResponseDto> getSignDetail(@RequestBody @Valid SignSafePromiseDetailRequestDto requestDto) {
        Result<SignSafePromiseDetailResponseModel> result = driverSafePromiseServiceApi.getSignDetail(MapperUtils.mapper(requestDto,SignSafePromiseDetailRequestModel.class));
        result.throwException();
        List<String> sourceSrcList = new ArrayList<>();
        sourceSrcList.add(result.getData().getHandPromiseUrl());
        sourceSrcList.add(result.getData().getSignResponsibilityUrl());
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(),SignSafePromiseDetailResponseDto.class,new GetSignDetailMapping(configKeyConstant,imageMap)));
    }

    /**
     * 签订列表汇总
     * @param responseDto
     * @return
     */
    @Override
    public Result<SummarySignSafePromiseResponseDto> getSignSummary(@RequestBody SearchSignSafePromiseListRequestDto responseDto) {
        Result<SummarySignSafePromiseResponseModel> result = driverSafePromiseServiceApi.getSignSummary(MapperUtils.mapper(responseDto,SearchSignSafePromiseListRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),SummarySignSafePromiseResponseDto.class));
    }

    /**
     * 签订列表-上传/重新上传
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> uploadSafePromise(@RequestBody @Valid UploadSafePromiseRequestDto requestDto) {
        Result<Boolean> result = driverSafePromiseServiceApi.uploadSafePromise(MapperUtils.mapper(requestDto,UploadSafePromiseRequestModel.class));
        result.throwException();
        return Result.success(true);
    }
}
