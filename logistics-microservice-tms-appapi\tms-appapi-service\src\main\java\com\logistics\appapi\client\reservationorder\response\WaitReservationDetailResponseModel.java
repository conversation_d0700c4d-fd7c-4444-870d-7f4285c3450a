package com.logistics.appapi.client.reservationorder.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/14 14:37
 */
@Data
public class WaitReservationDetailResponseModel {

    /**
     * 预约类型：1 提货，2 卸货
     */
    private Integer reservationType;

    /**
     * 发货地/收货地
     */
    private String address;

    /**
     * 仓库名称
     */
    private String warehouse;

    /**
     * 运单列表
     */
    private List<WaitReservationCarrierOrderListResponseModel> orderList=new ArrayList<>();

    /**
     * 预计里程数（公里）
     */
    private BigDecimal expectMileage;

}
