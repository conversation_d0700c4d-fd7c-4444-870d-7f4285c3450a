package com.logistics.tms.biz.renewableaudit;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.driverpayee.model.ViewLogResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.base.utils.StripTrailingZerosUtils;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.renewableaudit.model.CreateDemandOrderAddressForRenewableAuditModel;
import com.logistics.tms.biz.renewableaudit.model.CreateDemandOrderForRenewableAuditModel;
import com.logistics.tms.biz.renewableaudit.model.CreateDemandOrderGoodsForRenewableAuditModel;
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel;
import com.logistics.tms.client.BigDataClient;
import com.logistics.tms.client.WarehouseLifeClient;
import com.logistics.tms.client.YeloLifeBasicDataClient;
import com.logistics.tms.client.YeloLifeOrderClient;
import com.logistics.tms.client.feign.warehouse.lift.reponse.GetWarehouseDetailForLifeResponseModel;
import com.logistics.tms.client.feign.warehouse.lift.request.GetWarehouseDetailForLifeRequestModel;
import com.logistics.tms.client.model.SearchConsignorListForBigDataRequestModel;
import com.logistics.tms.client.model.SearchConsignorListForBigDataResponseModel;
import com.logistics.tms.client.model.SearchWarehouseForBigDataRequestModel;
import com.logistics.tms.client.model.SearchWarehouseForBigDataResponseModel;
import com.logistics.tms.controller.renewableaudit.request.*;
import com.logistics.tms.controller.renewableaudit.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.logistics.tms.rabbitmq.consumer.model.YeloLifeRecycleOrderApplyModel;
import com.logistics.tms.rabbitmq.consumer.model.YeloLifeRecycleOrderApplyResultModel;
import com.logistics.tms.rabbitmq.consumer.model.YeloLifeRecycleOrderGoodsModel;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.logistics.tms.rabbitmq.publisher.model.LifeAddressInfoModel;
import com.logistics.tms.rabbitmq.publisher.model.LifePushSkuAndAddressInfoMessage;
import com.logistics.tms.rabbitmq.publisher.model.SkuInfoModel;
import com.yelo.life.basicdata.api.fegin.address.model.GetCustomerAddressRequestModel;
import com.yelo.life.basicdata.api.fegin.address.model.GetCustomerAddressResponseModel;
import com.yelo.life.basicdata.api.fegin.customeraccount.model.GetCustomerAccountAddressRequestModel;
import com.yelo.life.basicdata.api.fegin.customeraccount.model.GetCustomerAccountAddressResponseModel;
import com.yelo.life.order.api.fegin.recycle.model.GenerateRecycleOrderRequestModel;
import com.yelo.life.order.api.fegin.recycle.model.GenerateRecycleOrderResponseModel;
import com.yelo.life.order.api.fegin.recycle.model.RecycleOrderAddressInfoModel;
import com.yelo.life.order.api.fegin.recycle.model.RecycleOrderSkuInfoModel;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.MapUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RenewableAuditBiz {

    @Resource
    private TRenewableAuditMapper tRenewableAuditMapper;
    @Resource
    private TRenewableAuditAddressMapper tRenewableAuditAddressMapper;
    @Resource
    private TRenewableAuditGoodsMapper tRenewableAuditGoodsMapper;
    @Resource
    private TCertificationPicturesMapper tCertificationPicturesMapper;
    @Resource
    private TStaffBasicMapper tStaffBasicMapper;
    @Resource
    private TVehicleBasicMapper tVehicleBasicMapper;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private RenewableAuditCommonBiz renewableAuditCommonBiz;
    @Resource
    private RabbitMqPublishBiz rabbitMqPublishBiz;
    @Resource
    private TOperateLogsMapper tOperateLogsMapper;
    @Resource
    private YeloLifeBasicDataClient yeloLifeBasicDataClient;
    @Resource
    private YeloLifeOrderClient yeloLifeOrderClient;
    @Resource
    private WarehouseLifeClient warehouseLifeClient;
    @Resource
    private BigDataClient bigDataClient;
    @Resource
    private TCarrierDriverRelationMapper tCarrierDriverRelationMapper;

    /**
     * 查询新生审核列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<RenewableAuditResponseModel> renewableAuditList(RenewableAuditRequestModel requestModel) {
        requestModel.enablePaging();
        List<Long> auditListIds = tRenewableAuditMapper.renewableAuditListCountIds(requestModel);
        PageInfo pageInfo = new PageInfo(auditListIds);
        if(ListUtils.isNotEmpty(auditListIds)){
            List<RenewableAuditResponseModel> renewableAuditResponseModels = tRenewableAuditMapper.renewableAuditList(StringUtils.listToString(auditListIds,','));
            pageInfo.setList(renewableAuditResponseModels);
        }
        return pageInfo;
    }

    /**
     * 新生审核列表详情
     * @param requestModel
     * @return
     */
    public RenewableAuditDetailResponseModel getRenewableAuditDetail(RenewableAuditDetailRequestModel requestModel) {
        // 校验新生订单是否存在
        TRenewableAudit renewableAudit = tRenewableAuditMapper.selectByPrimaryKeyDecrypt(requestModel.getRenewableOrderId());
        if (renewableAudit == null || IfValidEnum.INVALID.getKey().equals(renewableAudit.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.RENEWABLE_ORDER_NOT_EXIST);
        }
        return tRenewableAuditMapper.getRenewableAuditDetail(requestModel);
    }

    /**
     * 查询操作日志
     * @param requestModel
     * @return
     */
    public List<RenewableAuditOperateLogsResponseModel> getRenewableAuditLogs(RenewableAuditDetailRequestModel requestModel){
        // 校验新生订单是否存在
        TRenewableAudit renewableAudit = tRenewableAuditMapper.selectByPrimaryKeyDecrypt(requestModel.getRenewableOrderId());
        if (renewableAudit == null || IfValidEnum.INVALID.getKey().equals(renewableAudit.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.RENEWABLE_ORDER_NOT_EXIST);
        }
        List<ViewLogResponseModel> viewLogResponseModels = tOperateLogsMapper.selectLogsByCondition(OperateLogsObjectTypeEnum.RENEWABLE_AUDIT_ORDER.getKey(), requestModel.getRenewableOrderId(), null);
        return MapperUtils.mapper(viewLogResponseModels,RenewableAuditOperateLogsResponseModel.class);
    }

    /**
     * 查询票据
     * @param requestModel
     * @return
     */
    public List<RenewableAuditTicketsResponseModel> getRenewableAuditTickets(RenewableAuditDetailRequestModel requestModel){
        List<TCertificationPictures> byObjectIdAndType = tCertificationPicturesMapper.getByObjectIdAndType(requestModel.getRenewableOrderId(), CertificationPicturesObjectTypeEnum.T_RENEWABLE_AUDIT.getObjectType());
        return MapperUtils.mapper(byObjectIdAndType, RenewableAuditTicketsResponseModel.class);
    }

    /**
     * 指派司机-详情
     * @param requestModel
     * @return
     */
    public List<RenewableAssignDriverDetailResponseModel> getAssignDriverDetail(RenewableAssignDriverDetailRequestModel requestModel){
        return tRenewableAuditMapper.getAssignDriverDetail(requestModel);
    }

    /**
     * 指派司机-指派
     * @param requestModel
     * @return
     */
    @Transactional
    public Boolean assignDriver(RenewableAssignDriverRequestModel requestModel){
        //查询车辆基本信息
        VehicleBasicPropertyModel vehicleBasicPropertyModel = tVehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getVehicleId());
        if (vehicleBasicPropertyModel == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }
        //运营中的车辆
        if (!OperatingStateEnum.IN_OPERATION.getKey().equals(vehicleBasicPropertyModel.getOperatingState())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLES_ARE_SCRAP);
        }
        //判断车辆是否是自主或自营的
        if (!VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehicleBasicPropertyModel.getVehicleProperty())
                && !VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehicleBasicPropertyModel.getVehicleProperty())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_VEHICLE_ERROR);
        }
        //判断车辆是否是牵引车或一体车
        if (!VehicleCategoryEnum.TRACTOR.getKey().equals(vehicleBasicPropertyModel.getVehicleCategory())
                && !VehicleCategoryEnum.WHOLE.getKey().equals(vehicleBasicPropertyModel.getVehicleCategory())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_VEHICLE_ERROR);
        }
        //查询司机
        TStaffBasic tStaffBasic = tStaffBasicMapper.selectByPrimaryKey(requestModel.getStaffId());
        if (tStaffBasic == null || IfValidEnum.INVALID.getKey().equals(tStaffBasic.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }

        Long qiyaCompanyCarrierId = commonBiz.getQiyaCompanyCarrierId();
        //查询我司和司机关联关系
        TCarrierDriverRelation tCarrierDriverRelation = tCarrierDriverRelationMapper.getByCompanyCarrierIdAndDriverId(qiyaCompanyCarrierId, tStaffBasic.getId(), null);
        if (tCarrierDriverRelation == null || IfValidEnum.INVALID.getKey().equals(tCarrierDriverRelation.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }

        //判断驾驶员是否启用
        if (!EnabledEnum.ENABLED.getKey().equals(tCarrierDriverRelation.getEnabled())) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_ACCOUNT_DISABLED);
        }
        //只能是驾驶员类型
        if (!StaffTypeEnum.DRIVER.getKey().equals(tStaffBasic.getType())
                && !StaffTypeEnum.DRIVER_SUPERCARGO.getKey().equals(tStaffBasic.getType())) {
            throw new BizException(CarrierDataExceptionEnum.ONLY_DRIVERS_OF_THE_DRIVER_TYPE_CAN_BE_DISPATCHED);
        }
        //判断驾驶员是否是自营或自主
        if (!StaffPropertyEnum.OWN_STAFF.getKey().equals(tStaffBasic.getStaffProperty())
                &&!StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(tStaffBasic.getStaffProperty())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_PROPERTY_ERROR);
        }

        List<Integer> status = tRenewableAuditMapper.queryStatus(StringUtils.listToString(requestModel.getRenewableOrderIds(),','));
        long statusCout = status.stream().filter(o -> !o.equals(RenewableAuditStatusEnum.WAIT_ASSIGN.getKey())).count();
        if(statusCout>0){
            throw new BizException(CarrierDataExceptionEnum.ASSIGN_DRIVER);
        }
        List<TRenewableAudit> tRenewableAuditList = new ArrayList<>();
        List<TOperateLogs> operateLogsList = new ArrayList<>();
        requestModel.getRenewableOrderIds().forEach(o->{
            TRenewableAudit assignDriver = new TRenewableAudit();
            assignDriver.setId(o);
            assignDriver.setStatus(RenewableAuditStatusEnum.WAIT_CONFIRM.getKey());
            assignDriver.setVehicleId(requestModel.getVehicleId());
            assignDriver.setVehicleNo(vehicleBasicPropertyModel.getVehicleNo());
            assignDriver.setStaffProperty(tStaffBasic.getStaffProperty());
            assignDriver.setStaffId(requestModel.getStaffId());
            assignDriver.setStaffName(tStaffBasic.getName());
            assignDriver.setStaffMobile(tStaffBasic.getMobile());
            commonBiz.setBaseEntityModify(assignDriver, BaseContextHandler.getUserName());
            TOperateLogs tOperateLogs = commonBiz.addOperateLogs(o, OperateLogsOperateTypeEnum.RENEWABLE_AUDIT_ORDER_ASSIGN,
                    String.format(RenewableAuditOperateLogsEnum.ASSIGN_DRIVER.getFormat(),tStaffBasic.getName(),vehicleBasicPropertyModel.getVehicleNo()), BaseContextHandler.getUserName());
            tRenewableAuditList.add(assignDriver);
            operateLogsList.add(tOperateLogs);
        });
        //批量指派
        tRenewableAuditMapper.batchUpdateSelectiveEncrypt(tRenewableAuditList);
        //记录日志
        tOperateLogsMapper.batchInsert(operateLogsList);
        return true;
    }

    /**
     * 修改指派
     * @param requestModel
     * @return
     */
    @Transactional
    public Boolean updateAssignDriver(RenewableAssignDriverRequestModel requestModel){
        //查询车辆基本信息
        VehicleBasicPropertyModel vehicleBasicPropertyModel = tVehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getVehicleId());
        if (vehicleBasicPropertyModel == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }
        //运营中的车辆
        if (!OperatingStateEnum.IN_OPERATION.getKey().equals(vehicleBasicPropertyModel.getOperatingState())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLES_ARE_SCRAP);
        }
        //判断车辆是否是自主或自营的
        if (!VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehicleBasicPropertyModel.getVehicleProperty())
                && !VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehicleBasicPropertyModel.getVehicleProperty())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_VEHICLE_ERROR);
        }
        //判断车辆是否是牵引车或一体车
        if (!VehicleCategoryEnum.TRACTOR.getKey().equals(vehicleBasicPropertyModel.getVehicleCategory())
                && !VehicleCategoryEnum.WHOLE.getKey().equals(vehicleBasicPropertyModel.getVehicleCategory())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_VEHICLE_ERROR);
        }
        //查询司机
        TStaffBasic tStaffBasic = tStaffBasicMapper.selectByPrimaryKey(requestModel.getStaffId());
        if (tStaffBasic == null || IfValidEnum.INVALID.getKey().equals(tStaffBasic.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }
        Long qiyaCompanyCarrierId = commonBiz.getQiyaCompanyCarrierId();
        //查询我司和司机关联关系
        TCarrierDriverRelation tCarrierDriverRelation = tCarrierDriverRelationMapper.getByCompanyCarrierIdAndDriverId(qiyaCompanyCarrierId, tStaffBasic.getId(), null);
        if (tCarrierDriverRelation == null || IfValidEnum.INVALID.getKey().equals(tCarrierDriverRelation.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }

        //判断驾驶员是否启用
        if (!EnabledEnum.ENABLED.getKey().equals(tCarrierDriverRelation.getEnabled())) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_ACCOUNT_DISABLED);
        }

        //只能是驾驶员类型
        if (!StaffTypeEnum.DRIVER.getKey().equals(tStaffBasic.getType())
                && !StaffTypeEnum.DRIVER_SUPERCARGO.getKey().equals(tStaffBasic.getType())){
            throw new BizException(CarrierDataExceptionEnum.ONLY_DRIVERS_OF_THE_DRIVER_TYPE_CAN_BE_DISPATCHED);
        }
        //判断驾驶员是否是自营或自主
        if (!StaffPropertyEnum.OWN_STAFF.getKey().equals(tStaffBasic.getStaffProperty())
                &&!StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(tStaffBasic.getStaffProperty())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_PROPERTY_ERROR);
        }

        List<Integer> status = tRenewableAuditMapper.queryStatus(StringUtils.listToString(requestModel.getRenewableOrderIds(),','));
        long statusCout = status.stream().filter(o -> !o.equals(RenewableAuditStatusEnum.WAIT_CONFIRM.getKey())).count();
        if(statusCout>0){
            throw new BizException(CarrierDataExceptionEnum.UPDATE_DRIVER);
        }
        List<TRenewableAudit> tRenewableAuditList = new ArrayList<>();
        List<TOperateLogs> operateLogsList = new ArrayList<>();
        requestModel.getRenewableOrderIds().forEach(o->{
            TRenewableAudit assignDriver = new TRenewableAudit();
            assignDriver.setId(o);
            assignDriver.setVehicleId(requestModel.getVehicleId());
            assignDriver.setVehicleNo(vehicleBasicPropertyModel.getVehicleNo());
            assignDriver.setStaffId(requestModel.getStaffId());
            assignDriver.setStaffProperty(tStaffBasic.getStaffProperty());
            assignDriver.setStaffName(tStaffBasic.getName());
            assignDriver.setStaffMobile(tStaffBasic.getMobile());
            commonBiz.setBaseEntityModify(assignDriver, BaseContextHandler.getUserName());
            TOperateLogs tOperateLogs = commonBiz.addOperateLogs(o, OperateLogsOperateTypeEnum.RENEWABLE_AUDIT_ORDER_UPDATE_ASSIGN,
                    String.format(RenewableAuditOperateLogsEnum.UPDATE_ASSIGN_DRIVER.getFormat(),tStaffBasic.getName(),vehicleBasicPropertyModel.getVehicleNo()), BaseContextHandler.getUserName());
            tRenewableAuditList.add(assignDriver);
            operateLogsList.add(tOperateLogs);
        });
        //批量指派
        tRenewableAuditMapper.batchUpdateSelectiveEncrypt(tRenewableAuditList);
        //记录日志
        tOperateLogsMapper.batchInsert(operateLogsList);
        return true;
    }


    /**
     * 确认信息-详情
     * @param requestModel
     * @return
     */
    public RenewableConfirmGoodsResponseModel confirmNewsDetail(RenewableAuditDetailRequestModel requestModel) {
        // 校验新生订单是否存在
        TRenewableAudit renewableAudit = tRenewableAuditMapper.selectByPrimaryKeyDecrypt(requestModel.getRenewableOrderId());
        if (renewableAudit == null || IfValidEnum.INVALID.getKey().equals(renewableAudit.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.RENEWABLE_ORDER_NOT_EXIST);
        }

        //校验审核单状态
        if (!RenewableAuditStatusEnum.WAIT_CONFIRM.getKey().equals(renewableAudit.getStatus())) {
            throw new BizException(CarrierDataExceptionEnum.RENEWABLE_ORDER_INOPERABLE);
        }
        RenewableConfirmGoodsResponseModel renewableConfirmGoodsResponseModel = tRenewableAuditMapper.confirmNewsDetail(CertificationPicturesObjectTypeEnum.T_RENEWABLE_AUDIT.getObjectType(), requestModel.getRenewableOrderId());

        //如果有司机确认的货物,就只展示司机确认的货物
        List<RenewableAuditGoodsResponseModel> confirmGoodsList = renewableConfirmGoodsResponseModel.getConfirmGoodsList();
        if (ListUtils.isNotEmpty(confirmGoodsList)) {
            List<RenewableAuditGoodsResponseModel> driverConfirmGoodsList = confirmGoodsList.stream().filter(goodsItem -> RenewableGoodsSourceTypeEnum.DRIVER_CONFIRM.getKey().equals(goodsItem.getGoodsSourceType())).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(driverConfirmGoodsList)) {
                renewableConfirmGoodsResponseModel.setConfirmGoodsList(driverConfirmGoodsList);
            }
        }
        return renewableConfirmGoodsResponseModel;
    }


    /**
     * 确认信息-确认
     * @param requestModel
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean confirmNews(RenewableConfirmGoodsRequestModel requestModel){
        // 校验新生订单是否存在
        TRenewableAudit renewableAudit = tRenewableAuditMapper.selectByPrimaryKeyDecrypt(requestModel.getRenewableOrderId());
        if (renewableAudit == null || IfValidEnum.INVALID.getKey().equals(renewableAudit.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.RENEWABLE_ORDER_NOT_EXIST);
        }
        // 只有待确认订单才能操作
        if (!RenewableAuditStatusEnum.WAIT_CONFIRM.getKey().equals(renewableAudit.getStatus())) {
            throw new BizException(CarrierDataExceptionEnum.ORDER_STATUS_NOT_MATCH);
        }
        // 查询地址信息
        TRenewableAuditAddress address = tRenewableAuditAddressMapper.getAddressByRenewableOrderId(renewableAudit.getId());
        if (address == null) {
            throw new BizException(CarrierDataExceptionEnum.ENTRUST_ADDRESS_EMPTY);
        }
        //分布式锁对象
        String key = CommonConstant.UPDATE_RECYCLE_ORDER_PREFIX + renewableAudit.getRenewableOrderCode();
        String uuid = commonBiz.getDistributedLock(key);
        try {
        // 删除该新生订单之前保存的司机确认货物
        tRenewableAuditGoodsMapper.deleteByRenewableOrderId(renewableAudit.getId(),
                RenewableGoodsSourceTypeEnum.DRIVER_CONFIRM.getKey(),
                BaseContextHandler.getUserName());
        // 保存新生订单货物信息
        AtomicReference<BigDecimal> goodsTotalAmount = new AtomicReference<>(BigDecimal.ZERO);
        List<TRenewableAuditGoods> goodsList = requestModel.getConfirmGoodsList()
                .stream().map(goods -> {
                    TRenewableAuditGoods renewableAuditGoods = new TRenewableAuditGoods();
                    renewableAuditGoods.setRenewableOrderId(renewableAudit.getId());
                    renewableAuditGoods.setGoodsPrice(goods.getGoodsPrice());
                    renewableAuditGoods.setGoodsUnit(YeloLifeGoodsUnitEnum.BY_WEIGHT.getKey());
                    renewableAuditGoods.setGoodsName(goods.getGoodsName());
                    renewableAuditGoods.setGoodsAmount(goods.getGoodsAmount());
                    renewableAuditGoods.setRenewableSkuCode(goods.getSkuCode());
                    renewableAuditGoods.setGoodsSourceType(RenewableGoodsSourceTypeEnum.DRIVER_CONFIRM.getKey());
                    commonBiz.setBaseEntityAdd(renewableAuditGoods, BaseContextHandler.getUserName());
                    goodsTotalAmount.set(goodsTotalAmount.get().add(goods.getGoodsAmount()));
                    return renewableAuditGoods;
                }).collect(Collectors.toList());
        tRenewableAuditGoodsMapper.batchInsert(goodsList);
        // 封装保存卸货地址信息
        TRenewableAuditAddress updateAddress = MapperUtils.mapper(requestModel, TRenewableAuditAddress.class);
        updateAddress.setId(address.getId());
        commonBiz.setBaseEntityModify(updateAddress, BaseContextHandler.getUserName());
        tRenewableAuditAddressMapper.updateByPrimaryKeySelectiveEncrypt(updateAddress);

        // 回填货物重量合计主表
        log.info("回填货物重量合计主表：renewableAuditId:{}, goodsTotalAmount:{}", renewableAudit.getId(), goodsTotalAmount.get());
        TRenewableAudit updateGoodsAmount = new TRenewableAudit();
        updateGoodsAmount.setId(renewableAudit.getId());
        updateGoodsAmount.setVerifiedGoodsAmountTotal(goodsTotalAmount.get());
        updateGoodsAmount.setStatus(RenewableAuditStatusEnum.WAIT_AUDIT.getKey());
        commonBiz.setBaseEntityModify(updateGoodsAmount, BaseContextHandler.getUserName());
        tRenewableAuditMapper.updateByPrimaryKeySelectiveEncrypt(updateGoodsAmount);

        List<SkuInfoModel> skuInfoModelList = new ArrayList<>();
        BigDecimal totalPrice = new BigDecimal(0);
        for (TRenewableAuditGoods goods : goodsList) {
            SkuInfoModel skuInfoModel = new SkuInfoModel();
            skuInfoModel.setSkuCode(goods.getRenewableSkuCode());
            skuInfoModel.setSkuAmount(goods.getGoodsAmount());
            skuInfoModel.setSkuPrice(goods.getGoodsPrice());
            skuInfoModelList.add(skuInfoModel);
            // 计算价格
            totalPrice = totalPrice.add((goods.getGoodsAmount().multiply(goods.getGoodsPrice())).setScale(2, RoundingMode.HALF_UP));
        }
            // 删除之前上传的票据
            tCertificationPicturesMapper.delByObjectTypeId(CertificationPicturesObjectTypeEnum.T_RENEWABLE_AUDIT.getObjectType(),
                    renewableAudit.getId(),
                    BaseContextHandler.getUserName());
            // 批量上传票据
            List<TCertificationPictures> picturesList = new ArrayList<>();
            for (String picturePath : requestModel.getScenePhotosList()) {
                picturesList.add(buildCertificationPicture(renewableAudit.getId(),
                        picturePath,
                        CertificationPicturesFileTypeEnum.T_RENEWABLE_AUDIT_SCENE_PICTURE_FILE,
                        CopyFileTypeEnum.RENEWABLE_ORDER_SITE_IMAGE.getKey(),
                        renewableAudit.getRenewableOrderCode()));
            }
            for (String picturePath : requestModel.getOrderPhotosList()) {
                picturesList.add(buildCertificationPicture(renewableAudit.getId(),
                        picturePath,
                        CertificationPicturesFileTypeEnum.T_RENEWABLE_AUDIT_CONFIRM_PICTURE_FILE,
                        CopyFileTypeEnum.RENEWABLE_ORDER_CONFIRM_TICKET.getKey(),
                        renewableAudit.getRenewableOrderCode()));
            }
            // 批量保存构建的图片信息
            tCertificationPicturesMapper.batchInsert(picturesList);
        // 备注
        String remark = String.format(RenewableAuditOperateLogsEnum.CONFIRMNEWS.getFormat(),
                StripTrailingZerosUtils.stripTrailingZerosToString(updateGoodsAmount.getVerifiedGoodsAmountTotal()),
                ConverterUtils.toString(totalPrice.setScale(2,RoundingMode.HALF_UP)));
        //记录日志
        tOperateLogsMapper.insertSelective(commonBiz.addOperateLogs(renewableAudit.getId(), OperateLogsOperateTypeEnum.RENEWABLE_AUDIT_ORDER_CONFIRM,remark,BaseContextHandler.getUserName()));
        // 推送给新生货物
        syncRenewableConfirmGoods2YeloLife(renewableAudit.getRenewableOrderCode(), updateAddress, skuInfoModelList);

        //异步更新收发货地址经纬度
        AsyncProcessQueue.execute(() -> renewableAuditCommonBiz.updateRenewableAddressLonAndLat(updateAddress));

    } finally {
        //释放锁
        commonBiz.removeDistributedLock(key, uuid);
    }
        return true;
    }

    /**
     * 新生审核列表统计
     * @param requestModel
     * @return
     */
    public RenewableAuditListStatisticsResponseModel getRenewableAuditListStatistics(RenewableAuditRequestModel requestModel){
        requestModel.setStatus(null);
        List<Long> renewableOrderIds = tRenewableAuditMapper.renewableAuditListCountIds(requestModel);
        RenewableAuditListStatisticsResponseModel model = new RenewableAuditListStatisticsResponseModel();
        if (ListUtils.isNotEmpty(renewableOrderIds)) {
            model = tRenewableAuditMapper.getRenewableAuditListStatistics(StringUtils.listToString(renewableOrderIds, ','));
        }
        return model;
    }

    /**
     * 查询司机新生订单数量
     *
     * @param staffId
     * @param status
     * @return
     */
    public int getCountByStaffId(Long staffId, Integer status) {
        if (staffId == null) {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }
        return tRenewableAuditMapper.getCountByStaffId(staffId, status);
    }

    /**
     * 新生订单确认列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<RenewableOrderListResponseModel> renewableOrderList(RenewableOrderListRequestModel requestModel) {
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        if (loginDriverAppletUserId == null || CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)){
            return new PageInfo<>(new ArrayList<>());
        }
        requestModel.setStaffId(loginDriverAppletUserId);
        // 查询新生订单列表
        requestModel.enablePaging();
        List<TRenewableAudit> renewableAudits = tRenewableAuditMapper.queryRenewableOrderListByStaffId(requestModel);
        PageInfo pageInfo = new PageInfo(renewableAudits);
        if (ListUtils.isEmpty(renewableAudits)) {
            return pageInfo;
        }
        List<Long> renewableOrderIds = renewableAudits.stream().map(TRenewableAudit::getId).collect(Collectors.toList());
        // 查询新生订单的地址
        Map<Long, TRenewableAuditAddress> auditAddressMap = tRenewableAuditAddressMapper.queryRenewableAuditAddressList(renewableOrderIds)
                .stream().collect(Collectors.toMap(TRenewableAuditAddress::getRenewableOrderId, Function.identity()));
        // 拼接数据
        List<RenewableOrderListResponseModel> responseModels = new ArrayList<>();
        for (TRenewableAudit renewableAudit : renewableAudits) {
            RenewableOrderListResponseModel responseModel = new RenewableOrderListResponseModel();
            responseModel.setRenewableAuditId(renewableAudit.getId());
            responseModel.setCustomerName(renewableAudit.getCustomerName());
            responseModel.setRenewableOrderCode(renewableAudit.getRenewableOrderCode());
            responseModel.setPublishTime(renewableAudit.getPublishTime());
            responseModel.setVerifiedGoodsAmountTotal(renewableAudit.getVerifiedGoodsAmountTotal());
            responseModel.setStatus(renewableAudit.getStatus());
            responseModel.setBusinessType(renewableAudit.getBusinessType());
            responseModel.setCustomerUserName(renewableAudit.getCustomerUserName());
            responseModel.setCustomerUserMobile(renewableAudit.getCustomerUserMobile());
            TRenewableAuditAddress auditAddress = auditAddressMap.get(renewableAudit.getId());
            if (auditAddress != null) {
                MapperUtils.mapper(auditAddress, responseModel);
            }
            responseModels.add(responseModel);
        }
        pageInfo.setList(responseModels);
        return pageInfo;
    }

    /**
     * 新生订单tab汇总
     *
     * @return
     */
    public RenewableOrderListStatisticResponseModel renewableOrderListStatistic() {
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        if (loginDriverAppletUserId == null || CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)){
            return new RenewableOrderListStatisticResponseModel();
        }
        return tRenewableAuditMapper.renewableOrderListStatistic(loginDriverAppletUserId);
    }

    /**
     * 新生订单详情
     *
     * @param requestModel
     * @return
     */
    public RenewableOrderDetailResponseModel renewableOrderDetail(RenewableOrderDetailRequestModel requestModel) {
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        if (loginDriverAppletUserId == null || CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)){
            throw new BizException(CarrierDataExceptionEnum.RENEWABLE_ORDER_NOT_EXIST);
        }
        // 查询新生订单信息
        TRenewableAudit renewableAudit = tRenewableAuditMapper.selectByPrimaryKeyDecrypt(requestModel.getRenewableAuditId());
        if (renewableAudit == null || IfValidEnum.INVALID.getKey().equals(renewableAudit.getValid())
                || !renewableAudit.getStaffId().equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.RENEWABLE_ORDER_NOT_EXIST);
        }
        // 查询新生订单关联的发货地址信息
        TRenewableAuditAddress renewableAuditAddress = tRenewableAuditAddressMapper.getAddressByRenewableOrderId(renewableAudit.getId());
        // 查询新生订单关联的货物信息（优先展示确认信息）
        Map<Integer, List<TRenewableAuditGoods>> goodsMap = tRenewableAuditGoodsMapper.queryGoodsListByRenewableOrderId(renewableAudit.getId())
                .stream().collect(Collectors.groupingBy(TRenewableAuditGoods::getGoodsSourceType));
        // 查询新生订单关联的票据信息（优先展示确认信息）
        List<TCertificationPictures> pictureList = tCertificationPicturesMapper.getByObjectIdAndType(renewableAudit.getId(),
                CertificationPicturesObjectTypeEnum.T_RENEWABLE_AUDIT.getObjectType());
        // 拼接数据
        RenewableOrderDetailResponseModel responseModel = new RenewableOrderDetailResponseModel();
        responseModel.setRenewableAuditId(renewableAudit.getId());
        responseModel.setStatus(renewableAudit.getStatus());
        responseModel.setBusinessType(renewableAudit.getBusinessType());
        responseModel.setCustomerName(renewableAudit.getCustomerName());
        responseModel.setCustomerUserName(renewableAudit.getCustomerUserName());
        responseModel.setCustomerUserMobile(renewableAudit.getCustomerUserMobile());
        responseModel.setVerifiedGoodsAmountTotal(renewableAudit.getVerifiedGoodsAmountTotal());
        responseModel.setRemark(renewableAudit.getRemark());
        // 拼接地址信息
        if (renewableAuditAddress != null) {
            responseModel.setLoadLongitude(renewableAuditAddress.getLoadLongitude());
            responseModel.setLoadLatitude(renewableAuditAddress.getLoadLatitude());
            responseModel.setUnloadLatitude(renewableAuditAddress.getUnloadLatitude());
            responseModel.setUnloadLongitude(renewableAuditAddress.getUnloadLongitude());
            responseModel.setLoadProvinceName(renewableAuditAddress.getLoadProvinceName());
            responseModel.setLoadCityName(renewableAuditAddress.getLoadCityName());
            responseModel.setLoadAreaName(renewableAuditAddress.getLoadAreaName());
            responseModel.setLoadDetailAddress(renewableAuditAddress.getLoadDetailAddress());
            responseModel.setLoadWarehouse(renewableAuditAddress.getLoadWarehouse());
            responseModel.setConsignorName(renewableAuditAddress.getConsignorName());
            responseModel.setConsignorMobile(renewableAuditAddress.getConsignorMobile());
            responseModel.setUnloadProvinceName(renewableAuditAddress.getUnloadProvinceName());
            responseModel.setUnloadCityName(renewableAuditAddress.getUnloadCityName());
            responseModel.setUnloadAreaName(renewableAuditAddress.getUnloadAreaName());
            responseModel.setUnloadDetailAddress(renewableAuditAddress.getUnloadDetailAddress());
            responseModel.setUnloadWarehouse(renewableAuditAddress.getUnloadWarehouse());
            responseModel.setReceiverName(renewableAuditAddress.getReceiverName());
            responseModel.setReceiverMobile(renewableAuditAddress.getReceiverMobile());
        }
        // 拼接货物信息
        List<RenewableOrderGoodModel> renewableOrderGoods = new ArrayList<>();
        BigDecimal goodsPriceTotal = BigDecimal.ZERO;
        //是否已经确认货物
        boolean goodsVerifyStatus = false;
        if (!MapUtils.isEmpty(goodsMap)) {
            //是否新生同步的货物
            boolean isYeloSync = false;
            List<TRenewableAuditGoods> renewableAuditGoodsList;
            if (goodsMap.containsKey(RenewableGoodsSourceTypeEnum.DRIVER_CONFIRM.getKey())) {
                goodsVerifyStatus = true;
                renewableAuditGoodsList = goodsMap.get(RenewableGoodsSourceTypeEnum.DRIVER_CONFIRM.getKey());
            } else {
                isYeloSync = true;
                renewableAuditGoodsList = goodsMap.get(RenewableGoodsSourceTypeEnum.YELOLIFE_SYNC.getKey());
            }
            for (TRenewableAuditGoods goods : renewableAuditGoodsList) {
                goodsPriceTotal = goodsPriceTotal.add(goods.getGoodsPrice().multiply(goods.getGoodsAmount())).setScale(2, RoundingMode.HALF_UP);
                RenewableOrderGoodModel model = new RenewableOrderGoodModel();
                model.setGoodsId(goods.getId());
                model.setGoodsName(goods.getGoodsName());
                model.setGoodsAmount(goods.getGoodsAmount());
                model.setGoodsPrice(goods.getGoodsPrice());
                model.setGoodsUnit(goods.getGoodsUnit());
                model.setSkuCode(goods.getRenewableSkuCode());
                if (isYeloSync) {
                    model.setSuggestGoodsPrice(goods.getGoodsPrice());
                }

                renewableOrderGoods.add(model);
            }
        }
        responseModel.setGoodsPriceTotal(goodsPriceTotal);
        responseModel.setRenewableOrderGoods(renewableOrderGoods);
        responseModel.setGoodsVerifyStatus(goodsVerifyStatus ? RenewableGoodsVerifyStatusEnum.HAS_CONFIRM.getKey() : RenewableGoodsVerifyStatusEnum.WAIT_CONFIRM.getKey());
        // 拼接票据信息
        List<RenewableOrderTicketModel> ticketModels = pictureList.stream().map(picture -> {
            RenewableOrderTicketModel model = new RenewableOrderTicketModel();
            model.setTicketId(picture.getId());
            model.setTicketType(picture.getFileType());
            model.setRelativePath(picture.getFilePath());
            return model;
        }).collect(Collectors.toList());
        responseModel.setRenewableOrderTickets(ticketModels);
        responseModel.setTicketUploadStatus(ListUtils.isEmpty(ticketModels) ?
                RenewableTicketUploadStatusEnum.WAIT_UPLOAD.getKey() :
                RenewableTicketUploadStatusEnum.HAS_UPLOAD.getKey());
        return responseModel;
    }

    /**
     * 小程序-驾驶员下单
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public PublishRenewableOrderResponseModel publishRenewableOrder(PublishRenewableOrderRequestModel requestModel) {
        //获取当前登录的司机id
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        if (loginDriverAppletUserId == null || CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.DATA_ERROR_AND_RE_LOGIN);
        }
        //查询司机基础信息
        TStaffBasic tStaffBasic = tStaffBasicMapper.selectByPrimaryKey(loginDriverAppletUserId);
        if (tStaffBasic == null || IfValidEnum.INVALID.getKey().equals(tStaffBasic.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }

        //遍历货物信息
        BigDecimal goodsAmountTotal = CommonConstant.BIG_DECIMAL_ZERO;
        BigDecimal goodsPriceTotal = CommonConstant.BIG_DECIMAL_ZERO;
        for (PublishRenewableOrderGoodsRequestModel orderGood : requestModel.getRenewableOrderGoods()) {
            goodsAmountTotal = goodsAmountTotal.add(orderGood.getGoodsAmount());
            goodsPriceTotal = goodsPriceTotal.add(orderGood.getGoodsAmount().multiply(orderGood.getGoodsPrice()));
        }
        //单笔不能超过3000元
        if (goodsPriceTotal.compareTo(CommonConstant.BIG_DECIMAL_THREE_THOUSAND_HUNDRED) > CommonConstant.INTEGER_ZERO){
            throw new BizException(CarrierDataExceptionEnum.PUBLISH_RENEWABLE_ORDER_PRICE_OVER);
        }

        boolean yeloLifeExist = false;
        Integer businessType = null;
        Integer lifeBusinessType = null;
        String customerName = null;
        String customerUserName = null;
        String customerUserMobile = null;
        //查询司机手机在新生系统是否存在账号（同时校验发货地址code是否存在）
        GetCustomerAccountAddressRequestModel yelolifeAccountRequestModel = new GetCustomerAccountAddressRequestModel();
        yelolifeAccountRequestModel.setDriverPhone(requestModel.getCustomerAccount());
        yelolifeAccountRequestModel.setLoadAddressCode(requestModel.getLoadAddressCode());
        log.info("调用新生->getCustomerAccountAndAddress, params:{}", JSON.toJSONString(yelolifeAccountRequestModel));
        GetCustomerAccountAddressResponseModel yelolifeAccountResponseModel = yeloLifeBasicDataClient.getCustomerAccountAndAddress(yelolifeAccountRequestModel);
        if (yelolifeAccountResponseModel != null){//调用新生接口成功
            if (CommonConstant.ONE.equals(yelolifeAccountResponseModel.getDriverPhoneExist())){//账号存在
                if (CommonConstant.INTEGER_ZERO.equals(yelolifeAccountResponseModel.getCustomerAccountStatus())) {//账号被禁用
                    throw new BizException(CarrierDataExceptionEnum.YELO_LIFE_CUSTOMER_ACCOUNT_DISABLED);
                }
                if (CommonConstant.ZERO.equals(yelolifeAccountResponseModel.getLoadAddressCodeExist())) {//地址不存在或不属于此账号
                    throw new BizException(CarrierDataExceptionEnum.PUBLISH_RENEWABLE_ORDER_LOAD_ADDRESS_CODE_ERROR);
                }
                yeloLifeExist = true;
                if (CommonConstant.INTEGER_ONE.equals(yelolifeAccountResponseModel.getBusinessType())) {
                    //个人
                    businessType = CommonConstant.INTEGER_TWO;
                    customerUserName = yelolifeAccountResponseModel.getCustomerName();
                    customerUserMobile = yelolifeAccountResponseModel.getMobilePhone();
                } else if (CommonConstant.INTEGER_TWO.equals(yelolifeAccountResponseModel.getBusinessType())) {
                    //公司
                    businessType = CommonConstant.INTEGER_ONE;
                    customerName = yelolifeAccountResponseModel.getCustomerCompanyName();
                }
                lifeBusinessType = yelolifeAccountResponseModel.getBusinessType();
            }
        }

        PublishRenewableOrderResponseModel responseModel = new PublishRenewableOrderResponseModel();
        Date now = new Date();
        goodsAmountTotal = goodsAmountTotal.setScale(3, BigDecimal.ROUND_HALF_UP);
        if (yeloLifeExist){//新生系统存在账号
            //生成新生订单审核信息
            TRenewableAudit renewableAudit = new TRenewableAudit();
            renewableAudit.setStatus(RenewableAuditStatusEnum.WAIT_AUDIT.getKey());
            renewableAudit.setBusinessType(businessType);
            renewableAudit.setSource(RenewableSourceTypeEnum.DRIVER_PLACE_ORDER.getKey());
            renewableAudit.setGoodsAmountTotal(goodsAmountTotal);
            renewableAudit.setVerifiedGoodsAmountTotal(goodsAmountTotal);
            renewableAudit.setStaffId(tStaffBasic.getId());
            renewableAudit.setStaffProperty(tStaffBasic.getStaffProperty());
            renewableAudit.setStaffName(tStaffBasic.getName());
            renewableAudit.setStaffMobile(tStaffBasic.getMobile());
            renewableAudit.setPublishTime(now);
            renewableAudit.setCustomerName(customerName);
            renewableAudit.setCustomerUserName(customerUserName);
            renewableAudit.setCustomerUserMobile(customerUserMobile);
            renewableAudit.setPublishUserName(tStaffBasic.getName());
            renewableAudit.setPublishUserMobile(tStaffBasic.getMobile());
            commonBiz.setBaseEntityAdd(renewableAudit, BaseContextHandler.getUserName());
            tRenewableAuditMapper.insertSelectiveEncrypt(renewableAudit);

            //生成新生订单审核地址信息
            TRenewableAuditAddress renewableAuditAddress = MapperUtils.mapper(requestModel, TRenewableAuditAddress.class);
            renewableAuditAddress.setRenewableOrderId(renewableAudit.getId());
            commonBiz.setBaseEntityAdd(renewableAuditAddress, BaseContextHandler.getUserName());
            tRenewableAuditAddressMapper.insertSelectiveEncrypt(renewableAuditAddress);

            //生成新生订单审核货物信息
            TRenewableAuditGoods renewableAuditGoods;
            List<TRenewableAuditGoods> goodsList = new ArrayList<>();
            for (PublishRenewableOrderGoodsRequestModel orderGood : requestModel.getRenewableOrderGoods()) {
                //新增新生同步的
                renewableAuditGoods = new TRenewableAuditGoods();
                renewableAuditGoods.setRenewableOrderId(renewableAudit.getId());
                renewableAuditGoods.setGoodsSourceType(RenewableGoodsSourceTypeEnum.YELOLIFE_SYNC.getKey());
                renewableAuditGoods.setRenewableSkuCode(orderGood.getSkuCode());
                renewableAuditGoods.setGoodsName(orderGood.getGoodsName());
                renewableAuditGoods.setGoodsAmount(orderGood.getGoodsAmount());
                renewableAuditGoods.setGoodsUnit(YeloLifeGoodsUnitEnum.BY_WEIGHT.getKey());
                renewableAuditGoods.setGoodsPrice(orderGood.getGoodsPrice());
                commonBiz.setBaseEntityAdd(renewableAuditGoods, BaseContextHandler.getUserName());
                goodsList.add(renewableAuditGoods);

                //新增司机下单的
                renewableAuditGoods = new TRenewableAuditGoods();
                renewableAuditGoods.setRenewableOrderId(renewableAudit.getId());
                renewableAuditGoods.setGoodsSourceType(RenewableGoodsSourceTypeEnum.DRIVER_CONFIRM.getKey());
                renewableAuditGoods.setRenewableSkuCode(orderGood.getSkuCode());
                renewableAuditGoods.setGoodsName(orderGood.getGoodsName());
                renewableAuditGoods.setGoodsAmount(orderGood.getGoodsAmount());
                renewableAuditGoods.setGoodsUnit(YeloLifeGoodsUnitEnum.BY_WEIGHT.getKey());
                renewableAuditGoods.setGoodsPrice(orderGood.getGoodsPrice());
                commonBiz.setBaseEntityAdd(renewableAuditGoods, BaseContextHandler.getUserName());
                goodsList.add(renewableAuditGoods);
            }
            tRenewableAuditGoodsMapper.batchInsert(goodsList);

            //上传现场图片
            List<TCertificationPictures> picturesList = new ArrayList<>();
            for (String picturePath : requestModel.getScenePictureList()) {
                picturesList.add(buildCertificationPicture(renewableAudit.getId(),
                        picturePath,
                        CertificationPicturesFileTypeEnum.T_RENEWABLE_AUDIT_SCENE_PICTURE_FILE,
                        CopyFileTypeEnum.RENEWABLE_ORDER_SITE_IMAGE.getKey(),
                        renewableAudit.getRenewableOrderCode()));
            }
            //上传确认单据
            for (String picturePath : requestModel.getConfirmPictureList()) {
                picturesList.add(buildCertificationPicture(renewableAudit.getId(),
                        picturePath,
                        CertificationPicturesFileTypeEnum.T_RENEWABLE_AUDIT_CONFIRM_PICTURE_FILE,
                        CopyFileTypeEnum.RENEWABLE_ORDER_CONFIRM_TICKET.getKey(),
                        renewableAudit.getRenewableOrderCode()));
            }
            // 批量保存构建的图片信息
            tCertificationPicturesMapper.batchInsert(picturesList);

            //记录日志
            tOperateLogsMapper.insertSelective(commonBiz.addOperateLogs(renewableAudit.getId(), OperateLogsOperateTypeEnum.RENEWABLE_AUDIT_ORDER_ORDER, RenewableSourceTypeEnum.DRIVER_PLACE_ORDER.getValue(), BaseContextHandler.getUserName()));

            //新生生成“待确认”的回收记录，返回回收记录code
            //组装信息
            GenerateRecycleOrderRequestModel generateRecycleOrderRequestModel = new GenerateRecycleOrderRequestModel();
            generateRecycleOrderRequestModel.setDriverName(tStaffBasic.getName());
            generateRecycleOrderRequestModel.setDriverPhone(requestModel.getCustomerAccount());
            generateRecycleOrderRequestModel.setAccountType(lifeBusinessType);
            generateRecycleOrderRequestModel.setOperator(BaseContextHandler.getUserName());
            //地址信息
            RecycleOrderAddressInfoModel recycleOrderAddressInfo = MapperUtils.mapper(renewableAuditAddress, RecycleOrderAddressInfoModel.class);
            recycleOrderAddressInfo.setUnloadAddressDetail(renewableAuditAddress.getUnloadDetailAddress());
            recycleOrderAddressInfo.setUnloadAddressPerson(renewableAuditAddress.getReceiverName());
            recycleOrderAddressInfo.setUnloadAddressPhone(renewableAuditAddress.getReceiverMobile());
            generateRecycleOrderRequestModel.setRecycleOrderAddressInfo(recycleOrderAddressInfo);
            //货物信息
            List<RecycleOrderSkuInfoModel> skuInfoList = new ArrayList<>();
            RecycleOrderSkuInfoModel recycleOrderSkuInfoModel;
            for (TRenewableAuditGoods tRenewableAuditGoods : goodsList) {
                if (RenewableGoodsSourceTypeEnum.DRIVER_CONFIRM.getKey().equals(tRenewableAuditGoods.getGoodsSourceType())) {
                    recycleOrderSkuInfoModel = new RecycleOrderSkuInfoModel();
                    recycleOrderSkuInfoModel.setSkuCode(tRenewableAuditGoods.getRenewableSkuCode());
                    recycleOrderSkuInfoModel.setPriceType(CommonConstant.INTEGER_ONE);
                    recycleOrderSkuInfoModel.setSkuPrice(tRenewableAuditGoods.getGoodsPrice());
                    recycleOrderSkuInfoModel.setSkuAmount(tRenewableAuditGoods.getGoodsAmount());
                    skuInfoList.add(recycleOrderSkuInfoModel);
                }
            }
            generateRecycleOrderRequestModel.setSkuInfoList(skuInfoList);
            //调新生系统接口
            GenerateRecycleOrderResponseModel generateRecycleOrderResponseModel = yeloLifeOrderClient.generateRecycleOrder(generateRecycleOrderRequestModel);

            //更新新生订单code
            TRenewableAudit upRenewableAudit = new TRenewableAudit();
            upRenewableAudit.setId(renewableAudit.getId());
            upRenewableAudit.setRenewableOrderCode(generateRecycleOrderResponseModel.getRenewableOrderCode());
            tRenewableAuditMapper.updateByPrimaryKeySelectiveEncrypt(upRenewableAudit);

            //异步更新收发货地址经纬度
            AsyncProcessQueue.execute(() -> renewableAuditCommonBiz.updateRenewableAddressLonAndLat(renewableAuditAddress));

            responseModel.setIfRenewableAccountExist(CommonConstant.ONE);

        }else{//新生系统不存在账号
            //生成需求单、预约记录
            //组装信息
            CreateDemandOrderForRenewableAuditModel demandOrderModel = new CreateDemandOrderForRenewableAuditModel();
            demandOrderModel.setCustomerOrderSource(RenewableSourceTypeEnum.DRIVER_PLACE_ORDER.getKey());
            demandOrderModel.setPublishName(tStaffBasic.getName());
            demandOrderModel.setPublishMobile(tStaffBasic.getMobile());
            demandOrderModel.setPublishTime(now);
            demandOrderModel.setRequestSource(CommonConstant.ONE);

            //车辆司机信息
            demandOrderModel.setStaffId(tStaffBasic.getId());
            demandOrderModel.setStaffName(tStaffBasic.getName());
            demandOrderModel.setStaffMobile(tStaffBasic.getMobile());
            demandOrderModel.setStaffProperty(tStaffBasic.getStaffProperty());

            //地址信息
            demandOrderModel.setAddressModel(MapperUtils.mapper(requestModel, CreateDemandOrderAddressForRenewableAuditModel.class));

            //货物信息
            demandOrderModel.setGoodsList(MapperUtils.mapper(requestModel.getRenewableOrderGoods(), CreateDemandOrderGoodsForRenewableAuditModel.class));

            //确认单据
            demandOrderModel.setScenePictureList(requestModel.getScenePictureList());
            demandOrderModel.setConfirmPictureList(requestModel.getConfirmPictureList());

            renewableAuditCommonBiz.createDemandOrder(demandOrderModel);

            responseModel.setIfRenewableAccountExist(CommonConstant.ZERO);
        }

        return responseModel;
    }

    /**
     * 新生订单详情-确认提交货物
     *
     * @param requestModel
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean submitGoods(RenewableOrderSubmitGoodsRequestModel requestModel) {
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        if (loginDriverAppletUserId == null || CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.RENEWABLE_ORDER_NOT_EXIST);
        }
        // 校验新生订单是否存在
        TRenewableAudit renewableAudit = tRenewableAuditMapper.selectByPrimaryKeyDecrypt(requestModel.getRenewableAuditId());
        if (renewableAudit == null || IfValidEnum.INVALID.getKey().equals(renewableAudit.getValid())
                || !renewableAudit.getStaffId().equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.RENEWABLE_ORDER_NOT_EXIST);
        }
        // 只有待确认订单才能操作
        if (!RenewableAuditStatusEnum.WAIT_CONFIRM.getKey().equals(renewableAudit.getStatus())) {
            throw new BizException(CarrierDataExceptionEnum.ORDER_STATUS_NOT_MATCH);
        }
        // 删除该新生订单之前保存的司机确认货物
        tRenewableAuditGoodsMapper.deleteByRenewableOrderId(renewableAudit.getId(),
                RenewableGoodsSourceTypeEnum.DRIVER_CONFIRM.getKey(),
                BaseContextHandler.getUserName());
        // 保存新生订单货物信息（司机确认）
        AtomicReference<BigDecimal> goodsTotalAmount = new AtomicReference<>(new BigDecimal(0));
        List<TRenewableAuditGoods> goodsList = requestModel.getRenewableOrderGoods()
                .stream().map(goods -> {
                    TRenewableAuditGoods renewableAuditGoods = new TRenewableAuditGoods();
                    renewableAuditGoods.setRenewableOrderId(renewableAudit.getId());
                    renewableAuditGoods.setGoodsPrice(goods.getGoodsPrice());
                    renewableAuditGoods.setGoodsUnit(YeloLifeGoodsUnitEnum.BY_WEIGHT.getKey());
                    renewableAuditGoods.setGoodsName(goods.getGoodsName());
                    renewableAuditGoods.setGoodsAmount(goods.getGoodsAmount());
                    renewableAuditGoods.setRenewableSkuCode(goods.getSkuCode());
                    renewableAuditGoods.setGoodsSourceType(RenewableGoodsSourceTypeEnum.DRIVER_CONFIRM.getKey());
                    commonBiz.setBaseEntityAdd(renewableAuditGoods, BaseContextHandler.getUserName());
                    goodsTotalAmount.set(goodsTotalAmount.get().add(goods.getGoodsAmount()));
                    return renewableAuditGoods;
                }).collect(Collectors.toList());
        tRenewableAuditGoodsMapper.batchInsert(goodsList);
        // 回填货物重量合计主表
        log.info("回填货物重量合计主表：renewableAuditId:{}, goodsTotalAmount:{}", renewableAudit.getId(), goodsTotalAmount.get());
        TRenewableAudit updateGoodsAmount = new TRenewableAudit();
        updateGoodsAmount.setId(renewableAudit.getId());
        updateGoodsAmount.setVerifiedGoodsAmountTotal(goodsTotalAmount.get());
        tRenewableAuditMapper.updateByPrimaryKeySelectiveEncrypt(updateGoodsAmount);
        return true;
    }

    /**
     * 新生订单详情-确认提交单据
     *
     * @param requestModel
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean submitTicket(RenewableOrderSubmitTicketRequestModel requestModel) {
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        if (loginDriverAppletUserId == null || CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.RENEWABLE_ORDER_NOT_EXIST);
        }
        // 校验新生订单是否存在
        TRenewableAudit renewableAudit = tRenewableAuditMapper.selectByPrimaryKeyDecrypt(requestModel.getRenewableAuditId());
        if (renewableAudit == null || IfValidEnum.INVALID.getKey().equals(renewableAudit.getValid())
                || !renewableAudit.getStaffId().equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.RENEWABLE_ORDER_NOT_EXIST);
        }
        // 只有待确认订单才能操作
        if (!RenewableAuditStatusEnum.WAIT_CONFIRM.getKey().equals(renewableAudit.getStatus())) {
            throw new BizException(CarrierDataExceptionEnum.ORDER_STATUS_NOT_MATCH);
        }
        // 删除之前上传的票据
        tCertificationPicturesMapper.delByObjectTypeId(CertificationPicturesObjectTypeEnum.T_RENEWABLE_AUDIT.getObjectType(),
                renewableAudit.getId(),
                BaseContextHandler.getUserName());
        // 批量上传票据
        List<TCertificationPictures> picturesList = new ArrayList<>();
        for (String picturePath : requestModel.getScenePictureList()) {
            picturesList.add(buildCertificationPicture(renewableAudit.getId(),
                    picturePath,
                    CertificationPicturesFileTypeEnum.T_RENEWABLE_AUDIT_SCENE_PICTURE_FILE,
                    CopyFileTypeEnum.RENEWABLE_ORDER_SITE_IMAGE.getKey(),
                    renewableAudit.getRenewableOrderCode()));
        }
        for (String picturePath : requestModel.getConfirmPictureList()) {
            picturesList.add(buildCertificationPicture(renewableAudit.getId(),
                    picturePath,
                    CertificationPicturesFileTypeEnum.T_RENEWABLE_AUDIT_CONFIRM_PICTURE_FILE,
                    CopyFileTypeEnum.RENEWABLE_ORDER_CONFIRM_TICKET.getKey(),
                    renewableAudit.getRenewableOrderCode()));
        }
        // 批量保存构建的图片信息
        tCertificationPicturesMapper.batchInsert(picturesList);
        return true;
    }

    /**
     * 新生订单详情-确认提交信息（保存收货仓库信息）
     *
     * @param requestModel
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean submitRenewableOrder(SubmitRenewableOrderRequestModel requestModel) {
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        if (loginDriverAppletUserId == null || CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.RENEWABLE_ORDER_NOT_EXIST);
        }
        // 校验新生订单是否存在
        TRenewableAudit renewableAudit = tRenewableAuditMapper.selectByPrimaryKeyDecrypt(requestModel.getRenewableAuditId());
        if (renewableAudit == null || IfValidEnum.INVALID.getKey().equals(renewableAudit.getValid())
                || !renewableAudit.getStaffId().equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.RENEWABLE_ORDER_NOT_EXIST);
        }

        //分布式锁对象
        String key = CommonConstant.UPDATE_RECYCLE_ORDER_PREFIX + renewableAudit.getRenewableOrderCode();
        String uuid = commonBiz.getDistributedLock(key);
        try {
            // 只有待确认订单才能操作
            if (!RenewableAuditStatusEnum.WAIT_CONFIRM.getKey().equals(renewableAudit.getStatus())) {
                throw new BizException(CarrierDataExceptionEnum.ORDER_STATUS_NOT_MATCH);
            }
            // 货物信息未确认无法提交
            List<TRenewableAuditGoods> goodsList = tRenewableAuditGoodsMapper.queryGoodsListByOrderIdAndSourceType(renewableAudit.getId(),
                    RenewableGoodsSourceTypeEnum.DRIVER_CONFIRM.getKey());
            if (ListUtils.isEmpty(goodsList)) {
                throw new BizException(CarrierDataExceptionEnum.UNCONFIRMED_GOODS);
            }
            // 单据未上传无法提交
            List<TCertificationPictures> pictureList = tCertificationPicturesMapper.getByObjectIdAndType(renewableAudit.getId(),
                    CertificationPicturesObjectTypeEnum.T_RENEWABLE_AUDIT.getObjectType());
            if (ListUtils.isEmpty(pictureList)) {
                throw new BizException(CarrierDataExceptionEnum.NOT_UPLOAD_TICKETS);
            }
            // 查询地址信息
            TRenewableAuditAddress address = tRenewableAuditAddressMapper.getAddressByRenewableOrderId(renewableAudit.getId());
            if (address == null) {
                throw new BizException(CarrierDataExceptionEnum.ENTRUST_ADDRESS_EMPTY);
            }
            // 封装保存卸货地址信息
            TRenewableAuditAddress updateAddress = MapperUtils.mapper(requestModel, TRenewableAuditAddress.class);
            updateAddress.setId(address.getId());
            commonBiz.setBaseEntityModify(updateAddress, BaseContextHandler.getUserName());
            tRenewableAuditAddressMapper.updateByPrimaryKeySelectiveEncrypt(updateAddress);
            // 回写订单主表，状态为待审核
            TRenewableAudit updateRenewableAudit = new TRenewableAudit();
            updateRenewableAudit.setId(renewableAudit.getId());
            updateRenewableAudit.setStatus(RenewableAuditStatusEnum.WAIT_AUDIT.getKey());
            commonBiz.setBaseEntityModify(updateRenewableAudit, BaseContextHandler.getUserName());
            tRenewableAuditMapper.updateByPrimaryKeySelectiveEncrypt(updateRenewableAudit);

            List<SkuInfoModel> skuInfoModelList = new ArrayList<>();
            BigDecimal totalPrice = new BigDecimal(0);
            for (TRenewableAuditGoods goods : goodsList) {
                SkuInfoModel skuInfoModel = new SkuInfoModel();
                skuInfoModel.setSkuCode(goods.getRenewableSkuCode());
                skuInfoModel.setSkuAmount(goods.getGoodsAmount());
                skuInfoModel.setSkuPrice(goods.getGoodsPrice());
                skuInfoModelList.add(skuInfoModel);

                // 计算价格
                totalPrice = totalPrice.add((goods.getGoodsAmount().multiply(goods.getGoodsPrice()).setScale(2,RoundingMode.HALF_UP)));
            }
            // 备注
            String remark = String.format(RenewableAuditOperateLogsEnum.CONFIRMNEWS.getFormat(),
                    StripTrailingZerosUtils.stripTrailingZerosToString(renewableAudit.getVerifiedGoodsAmountTotal()),
                    ConverterUtils.toString(totalPrice.setScale(2,RoundingMode.HALF_UP)));
            tOperateLogsMapper.insertSelective(commonBiz.addOperateLogs(
                    renewableAudit.getId(),
                    OperateLogsOperateTypeEnum.RENEWABLE_AUDIT_ORDER_CONFIRM,
                    remark,
                    BaseContextHandler.getUserName()));

            // 推送给新生货物
            syncRenewableConfirmGoods2YeloLife(renewableAudit.getRenewableOrderCode(), updateAddress, skuInfoModelList);

            //异步更新收发货地址经纬度
            AsyncProcessQueue.execute(() -> renewableAuditCommonBiz.updateRenewableAddressLonAndLat(updateAddress));
        } finally {
            //释放锁
            commonBiz.removeDistributedLock(key, uuid);
        }
        return true;
    }

    /**
     * 新生订单详情-查询收货仓库(先查询大数据再查询云仓仓库地址)
     *
     * @param requestModel
     * @return
     */
    public List<SearchWarehouseResponseModel> searchWarehouse(SearchWarehouseRequestModel requestModel) {
        //先查询大数据，如果未查询到数据或接口调用失败，再查询云仓
        List<SearchWarehouseResponseModel> list = new ArrayList<>();

        //距离优先：调用大数据接口
        if (CommonConstant.ONE.equals(requestModel.getSearchType())) {
            try {
                //通过大数据查询云仓仓库地址信息
                List<SearchWarehouseForBigDataResponseModel> warehouseList = bigDataClient.searchWarehouseList(MapperUtils.mapper(requestModel, SearchWarehouseForBigDataRequestModel.class));
                if (ListUtils.isNotEmpty(warehouseList)) {
                    list = MapperUtils.mapper(warehouseList, SearchWarehouseResponseModel.class);
                }
            }catch (Exception e){
                log.info("调用大数据接口失败：" + e.getMessage());
            }
            //大数据查出数据后，再查云仓仓库地址信息获取联系人信息（大数据无法解密手机号，需自己查）
            if (ListUtils.isNotEmpty(list)){
                GetWarehouseDetailForLifeRequestModel lifeRequestModel = new GetWarehouseDetailForLifeRequestModel();
                lifeRequestModel.setEnabled(EnabledEnum.ENABLED.getKey());
                lifeRequestModel.setWarehouseCode(list.stream().map(SearchWarehouseResponseModel::getUnloadAddressCode).collect(Collectors.toList()));
                log.info("查询收货仓库-调用云仓接口，lifeRequestModel:{}", JSON.toJSONString(lifeRequestModel));
                List<GetWarehouseDetailForLifeResponseModel> warehouseDetailForLife = warehouseLifeClient.getWarehouseDetailForLife(lifeRequestModel);
                if (ListUtils.isNotEmpty(warehouseDetailForLife)){
                    Map<String, String> receiverMobileMap = new HashMap<>();
                    warehouseDetailForLife.forEach(item -> receiverMobileMap.put(item.getWarehouseCode(), item.getReceiverMobile()));
                    //赋值联系人手机号
                    for (SearchWarehouseResponseModel model : list) {
                        model.setReceiverMobile(receiverMobileMap.get(model.getUnloadAddressCode()));
                    }
                }
            }
        }

        //综合：查询云仓仓库地址；如果调用大数据接口失败或未获取到数据，则再查云仓仓库地址
        if (CommonConstant.TWO.equals(requestModel.getSearchType()) || ListUtils.isEmpty(list)){
            //调云仓接口
            GetWarehouseDetailForLifeRequestModel lifeRequestModel = new GetWarehouseDetailForLifeRequestModel();
            lifeRequestModel.setEnabled(EnabledEnum.ENABLED.getKey());
            lifeRequestModel.setWarehouseName(requestModel.getWarehouseCondition());
            log.info("查询收货仓库-调用云仓接口，lifeRequestModel:{}", JSON.toJSONString(lifeRequestModel));
            List<GetWarehouseDetailForLifeResponseModel> warehouseDetailForLife = warehouseLifeClient.getWarehouseDetailForLife(lifeRequestModel);
            //组装数据
            if (ListUtils.isNotEmpty(warehouseDetailForLife)){
                SearchWarehouseResponseModel searchWarehouseResponseModel;
                for (GetWarehouseDetailForLifeResponseModel model : warehouseDetailForLife) {
                    searchWarehouseResponseModel = MapperUtils.mapper(model, SearchWarehouseResponseModel.class);
                    searchWarehouseResponseModel.setUnloadAddressCode(model.getWarehouseCode());
                    searchWarehouseResponseModel.setUnloadWarehouse(model.getWarehouseName());
                    list.add(searchWarehouseResponseModel);
                }
            }
        }

        return list;
    }

    /**
     * 驾驶员下单-查询发货人(先查询大数据再查询新生客户地址)
     * @param requestModel
     * @return
     */
    public List<SearchConsignorResponseModel> searchConsignor(SearchConsignorRequestModel requestModel) {
        //先查询大数据，如果未查询到数据或接口调用失败，再查询云仓
        List<SearchConsignorResponseModel> list = new ArrayList<>();

        //距离优先：调用大数据接口
        if (CommonConstant.ONE.equals(requestModel.getSearchType())
                && StringUtils.isNotBlank(requestModel.getLongitude())
                && StringUtils.isNotBlank(requestModel.getLatitude())) {
            try {
                //通过大数据查询新生客户地址信息
                List<SearchConsignorListForBigDataResponseModel> consignorList = bigDataClient.searchConsignorList(MapperUtils.mapper(requestModel, SearchConsignorListForBigDataRequestModel.class));
                if (ListUtils.isNotEmpty(consignorList)) {
                    list = MapperUtils.mapper(consignorList, SearchConsignorResponseModel.class);
                }
            } catch (Exception e) {
                log.info("调用大数据接口失败：" + e.getMessage());
            }
            //大数据查出数据后，再查新生地址信息获取联系人信息（大数据无法解密手机号，需自己查）
            if (ListUtils.isNotEmpty(list)){
                //调新生接口
                GetCustomerAddressRequestModel getCustomerAddressRequestModel = new GetCustomerAddressRequestModel();
                getCustomerAddressRequestModel.setEnabled(EnabledEnum.ENABLED.getKey());
                getCustomerAddressRequestModel.setAddressCodeList(list.stream().map(SearchConsignorResponseModel::getLoadAddressCode).collect(Collectors.toList()));
                List<GetCustomerAddressResponseModel> customerAddressList = yeloLifeBasicDataClient.getCustomerAddress(getCustomerAddressRequestModel);
                if (ListUtils.isNotEmpty(customerAddressList)){
                    Map<String, GetCustomerAddressResponseModel> consignorMobileMap = new HashMap<>();
                    customerAddressList.forEach(item -> consignorMobileMap.put(item.getCustomerAddressCode(), item));
                    //赋值联系人手机号
                    GetCustomerAddressResponseModel customerAddressModel;
                    for (SearchConsignorResponseModel model : list) {
                        customerAddressModel = consignorMobileMap.get(model.getLoadAddressCode());
                        if (customerAddressModel != null) {
                            model.setConsignorMobile(customerAddressModel.getContactPhone());
                            model.setCustomerAccount(customerAddressModel.getMobilePhone());
                            if (CommonConstant.INTEGER_ONE.equals(customerAddressModel.getAccountType())) {
                                model.setCustomerType(CommonConstant.INTEGER_TWO);
                                model.setCustomerName("");
                            } else if (CommonConstant.INTEGER_TWO.equals(customerAddressModel.getAccountType())) {
                                model.setCustomerType(CommonConstant.INTEGER_ONE);
                                model.setCustomerName(customerAddressModel.getCustomerCompanyName());
                            }
                        }
                    }
                }
            }
        }

        //综合：查询新生客户地址；如果调用大数据接口失败或未获取到数据，则再查新生客户地址
        if (CommonConstant.TWO.equals(requestModel.getSearchType()) || ListUtils.isEmpty(list)){
            //调新生接口
            GetCustomerAddressRequestModel getCustomerAddressRequestModel = new GetCustomerAddressRequestModel();
            getCustomerAddressRequestModel.setEnabled(EnabledEnum.ENABLED.getKey());
            getCustomerAddressRequestModel.setAddressName(requestModel.getConsignorCondition());
            List<GetCustomerAddressResponseModel> customerAddressList = yeloLifeBasicDataClient.getCustomerAddress(getCustomerAddressRequestModel);
            //组装数据
            if (ListUtils.isNotEmpty(customerAddressList)){
                SearchConsignorResponseModel searchConsignorResponseModel;
                for (GetCustomerAddressResponseModel model : customerAddressList) {
                    searchConsignorResponseModel = new SearchConsignorResponseModel();
                    searchConsignorResponseModel.setCustomerName(model.getCustomerName());
                    if (CommonConstant.INTEGER_ONE.equals(model.getAccountType())) {
                        searchConsignorResponseModel.setCustomerType(CommonConstant.INTEGER_TWO);
                        searchConsignorResponseModel.setCustomerName("");
                    } else if (CommonConstant.INTEGER_TWO.equals(model.getAccountType())) {
                        searchConsignorResponseModel.setCustomerType(CommonConstant.INTEGER_ONE);
                        searchConsignorResponseModel.setCustomerName(model.getCustomerCompanyName());
                    }
                    searchConsignorResponseModel.setLoadAddressCode(model.getCustomerAddressCode());
                    searchConsignorResponseModel.setLoadProvinceId(model.getProvinceId());
                    searchConsignorResponseModel.setLoadProvinceName(model.getProvinceName());
                    searchConsignorResponseModel.setLoadCityId(model.getCityId());
                    searchConsignorResponseModel.setLoadCityName(model.getCityName());
                    searchConsignorResponseModel.setLoadAreaId(model.getAreaId());
                    searchConsignorResponseModel.setLoadAreaName(model.getAreaName());
                    searchConsignorResponseModel.setLoadDetailAddress(model.getAddressDetail());
                    searchConsignorResponseModel.setConsignorName(model.getContactPerson());
                    searchConsignorResponseModel.setConsignorMobile(model.getContactPhone());
                    searchConsignorResponseModel.setCustomerAccount(model.getMobilePhone());
                    list.add(searchConsignorResponseModel);
                }
            }
        }
        return list;
    }

    /**
     * 查询新生货物详情
     *
     * @param requestModel
     * @return
     */
    public SearchSkuDetailResponseModel searchSkuDetail(SearchSkuDetailRequestModel requestModel) {
        return yeloLifeBasicDataClient.getLifeSkuDetail(requestModel);
    }

    /**
     * 查询sku列表
     *
     * @param requestModel
     * @return
     */
    public List<SearchLifeSkuResponseModel> searchLifeSku(SearchLifeSkuRequestModel requestModel) {
        return yeloLifeBasicDataClient.getLifeSkuList(requestModel);
    }

    /**
     * 推送给新生货物
     *
     * @param renewableOrderCode 新生订单编号
     * @param address 新生订单地址信息
     * @param skuInfoModelList sku列表信息
     */
    private void syncRenewableConfirmGoods2YeloLife(String renewableOrderCode,
                                                    TRenewableAuditAddress address,
                                                    List<SkuInfoModel> skuInfoModelList) {
        LifePushSkuAndAddressInfoMessage model = new LifePushSkuAndAddressInfoMessage();
        model.setRecycleOrderCode(renewableOrderCode);
        model.setOperator(BaseContextHandler.getUserName());
        LifeAddressInfoModel lifeAddressInfoModel = new LifeAddressInfoModel();
        lifeAddressInfoModel.setUnloadAddressCode(address.getUnloadAddressCode());
        lifeAddressInfoModel.setUnloadProvinceId(address.getUnloadProvinceId());
        lifeAddressInfoModel.setUnloadProvinceName(address.getUnloadProvinceName());
        lifeAddressInfoModel.setUnloadCityId(address.getUnloadCityId());
        lifeAddressInfoModel.setUnloadCityName(address.getUnloadCityName());
        lifeAddressInfoModel.setUnloadAreaId(address.getUnloadAreaId());
        lifeAddressInfoModel.setUnloadWarehouse(address.getUnloadWarehouse());
        lifeAddressInfoModel.setUnloadAddressDetail(address.getUnloadDetailAddress());
        lifeAddressInfoModel.setUnloadAddressPerson(address.getReceiverName());
        lifeAddressInfoModel.setUnloadAddressPhone(address.getReceiverMobile());
        model.setLifeAddressInfoDto(lifeAddressInfoModel);
        model.setSkuInfoDto(skuInfoModelList);
        rabbitMqPublishBiz.syncRenewableConfirmGoods2YeloLife(model);
    }

    /**
     * 构建图片文件存储实体
     *
     * @param objectId
     * @param picturePath
     * @param typeEnum
     * @param copyFileType
     * @param renewableOrderCode
     * @return
     */
    private TCertificationPictures buildCertificationPicture(Long objectId,
                                                             String picturePath,
                                                             CertificationPicturesFileTypeEnum typeEnum,
                                                             Integer copyFileType,
                                                             String renewableOrderCode) {
        TCertificationPictures certificationPictures = new TCertificationPictures();
        certificationPictures.setObjectId(objectId);
        certificationPictures.setObjectType(typeEnum.getObjectType().getObjectType());
        certificationPictures.setFileType(typeEnum.getFileType());
        certificationPictures.setFileTypeName(typeEnum.getFileName());
        certificationPictures.setFileName(typeEnum.getFileName());
        certificationPictures.setUploadTime(new Date());
        certificationPictures.setUploadUserName(BaseContextHandler.getUserName());
        certificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(copyFileType,
                renewableOrderCode,
                picturePath,
                null));
        certificationPictures.setSuffix(certificationPictures.getFilePath().substring(certificationPictures.getFilePath().lastIndexOf('.')));
        commonBiz.setBaseEntityAdd(certificationPictures, BaseContextHandler.getUserName());
        return certificationPictures;
    }

    /**
     * 新生回收单审核/取消-新生系统操作取消、确认、驳回
     * @param resultModel
     */
    @Transactional
    public void yeloLifeRecycleOrderApply(YeloLifeRecycleOrderApplyResultModel resultModel){
        //根据回收单号查询订单审核信息是否存在
        TRenewableAudit dbRenewableAudit = tRenewableAuditMapper.getByRenewableOrderCode(resultModel.getRecycleOrderCode());
        if (dbRenewableAudit == null){
            log.info("新生订单审核数据不存在");
            return;
        }
        //已取消，不能操作
        if (RenewableAuditStatusEnum.HAS_CANCELED.getKey().equals(dbRenewableAudit.getStatus())){
            log.info("新生订单审核数据已取消："+dbRenewableAudit.getRenewableOrderCode());
            return;
        }
        //已审核，不能操作
        if (RenewableAuditStatusEnum.AUDIT_THROUGH.getKey().equals(dbRenewableAudit.getStatus())){
            log.info("新生订单审核数据已审核："+dbRenewableAudit.getRenewableOrderCode());
            return;
        }

        OperateLogsOperateTypeEnum operateLogsOperateTypeEnum = null;
        Integer auditStatus = null;
        String auditorName = "";
        Date auditorTime = null;
        Date now;
        if (resultModel.getAuditorTime() != null){
            now = resultModel.getAuditorTime();
        }else{
            now = new Date();
        }
        if (CommonConstant.INTEGER_ONE.equals(resultModel.getApplyResult())) {//确认
            //非待审核状态，不能操作确认
            if (!RenewableAuditStatusEnum.WAIT_AUDIT.getKey().equals(dbRenewableAudit.getStatus())) {
                log.info("新生订单审核数据不是待审核状态：" + dbRenewableAudit.getRenewableOrderCode());
                return;
            }

            //确认订单审核信息（已审核）
            auditStatus = RenewableAuditStatusEnum.AUDIT_THROUGH.getKey();
            auditorName = resultModel.getAuditorName();
            auditorTime = now;

            //记录驳回操作日志
            operateLogsOperateTypeEnum = OperateLogsOperateTypeEnum.RENEWABLE_AUDIT_ORDER_AUDIT;
        } else if (CommonConstant.INTEGER_TWO.equals(resultModel.getApplyResult())) {//驳回
            //非待审核状态，不能操作驳回
            if (!RenewableAuditStatusEnum.WAIT_AUDIT.getKey().equals(dbRenewableAudit.getStatus())) {
                log.info("新生订单审核数据不是待审核状态：" + dbRenewableAudit.getRenewableOrderCode());
                return;
            }

            //驳回订单审核信息
            auditStatus = RenewableAuditStatusEnum.WAIT_CONFIRM.getKey();
            auditorName = resultModel.getAuditorName();
            auditorTime = now;

            //记录驳回操作日志
            operateLogsOperateTypeEnum = OperateLogsOperateTypeEnum.RENEWABLE_AUDIT_ORDER_REJECT;
        }

        //更新新生订单审核信息
        if (auditStatus != null) {
            TRenewableAudit upRenewableAudit = new TRenewableAudit();
            upRenewableAudit.setId(dbRenewableAudit.getId());
            upRenewableAudit.setStatus(auditStatus);
            upRenewableAudit.setAuditTime(auditorTime);
            upRenewableAudit.setAuditorName(auditorName);
            commonBiz.setBaseEntityModify(upRenewableAudit, resultModel.getAuditorName());
            tRenewableAuditMapper.updateByPrimaryKeySelectiveEncrypt(upRenewableAudit);
        }

        //记录操作日志
        if (operateLogsOperateTypeEnum != null) {
            tOperateLogsMapper.insertSelective(commonBiz.addOperateLogs(
                    dbRenewableAudit.getId(),
                    operateLogsOperateTypeEnum,
                    "",
                    resultModel.getAuditorName()));
        }

        //确认，新生订单变为已审核，并且生成“待调度”状态的需求单
        if (CommonConstant.INTEGER_ONE.equals(resultModel.getApplyResult())) {//确认
            //查询新生订单地址信息
            TRenewableAuditAddress dbRenewableAuditAddress = tRenewableAuditAddressMapper.getAddressByRenewableOrderId(dbRenewableAudit.getId());

            //查询新生订单司机确认的货物信息
            List<TRenewableAuditGoods> dbRenewableAuditGoodsList = tRenewableAuditGoodsMapper.queryGoodsListByOrderIdAndSourceType(dbRenewableAudit.getId(), RenewableGoodsSourceTypeEnum.DRIVER_CONFIRM.getKey());

            //查询新生订单票据信息
            List<TCertificationPictures> dbCertificationPicturesList = tCertificationPicturesMapper.getByObjectIdAndType(dbRenewableAudit.getId(), CertificationPicturesObjectTypeEnum.T_RENEWABLE_AUDIT.getObjectType());
            List<String> scenePictureList = new ArrayList<>();//现场图片
            List<String> confirmPictureList = new ArrayList<>();//确认单据
            for (TCertificationPictures tCertificationPictures : dbCertificationPicturesList) {
                if (CertificationPicturesFileTypeEnum.T_RENEWABLE_AUDIT_SCENE_PICTURE_FILE.getFileType().equals(tCertificationPictures.getFileType())){
                    scenePictureList.add(tCertificationPictures.getFilePath());
                }else if (CertificationPicturesFileTypeEnum.T_RENEWABLE_AUDIT_CONFIRM_PICTURE_FILE.getFileType().equals(tCertificationPictures.getFileType())){
                    confirmPictureList.add(tCertificationPictures.getFilePath());
                }
            }

            //组装信息
            CreateDemandOrderForRenewableAuditModel demandOrderModel = new CreateDemandOrderForRenewableAuditModel();
            demandOrderModel.setCustomerOrderSource(dbRenewableAudit.getSource());
            demandOrderModel.setPublishName(dbRenewableAudit.getPublishUserName());
            demandOrderModel.setPublishMobile(dbRenewableAudit.getPublishUserMobile());
            demandOrderModel.setPublishTime(now);
            demandOrderModel.setRequestSource(CommonConstant.TWO);
            demandOrderModel.setRenewableOrderId(dbRenewableAudit.getId());
            demandOrderModel.setRenewableOrderCode(dbRenewableAudit.getRenewableOrderCode());
            demandOrderModel.setBusinessType(dbRenewableAudit.getBusinessType());
            demandOrderModel.setCustomerName(dbRenewableAudit.getCustomerName());
            demandOrderModel.setCustomerUserName(dbRenewableAudit.getCustomerUserName());
            demandOrderModel.setCustomerUserMobile(dbRenewableAudit.getCustomerUserMobile());
            demandOrderModel.setRemark(dbRenewableAudit.getRemark());

            //车辆司机信息
            demandOrderModel.setStaffId(dbRenewableAudit.getStaffId());
            demandOrderModel.setStaffName(dbRenewableAudit.getStaffName());
            demandOrderModel.setStaffMobile(dbRenewableAudit.getStaffMobile());
            demandOrderModel.setStaffProperty(dbRenewableAudit.getStaffProperty());

            //地址信息
            demandOrderModel.setAddressModel(MapperUtils.mapper(dbRenewableAuditAddress, CreateDemandOrderAddressForRenewableAuditModel.class));

            //货物信息
            List<CreateDemandOrderGoodsForRenewableAuditModel> goodsList = new ArrayList<>();
            CreateDemandOrderGoodsForRenewableAuditModel goodsModel;
            for (TRenewableAuditGoods goods : dbRenewableAuditGoodsList) {
                goodsModel = new CreateDemandOrderGoodsForRenewableAuditModel();
                goodsModel.setSkuCode(goods.getRenewableSkuCode());
                goodsModel.setGoodsName(goods.getGoodsName());
                goodsModel.setGoodsAmount(goods.getGoodsAmount());
                goodsModel.setGoodsPrice(goods.getGoodsPrice());
                goodsList.add(goodsModel);
            }
            demandOrderModel.setGoodsList(goodsList);

            //确认单据
            demandOrderModel.setScenePictureList(scenePictureList);
            demandOrderModel.setConfirmPictureList(confirmPictureList);

            renewableAuditCommonBiz.createDemandOrder(demandOrderModel);
        }
    }

    /**
     * 新增审核信息（消费mq）
     * @param resultModel
     */
    @Transactional
    public void yeloLifeAddRecycleOrder(YeloLifeRecycleOrderApplyModel resultModel){
        //根据回收单号查询订单信息是否存在
        TRenewableAudit dbRenewableAudit = tRenewableAuditMapper.getByRenewableOrderCode(resultModel.getRecycleOrderCode());
        if (dbRenewableAudit != null){
            log.info("新生订单审核数据已存在");
            return;
        }
        List<YeloLifeRecycleOrderGoodsModel> recycleOrderGoods = resultModel.getRecycleOrderGoods();
        if(ListUtils.isEmpty(recycleOrderGoods)){
            log.info("新生订单货物信息为空");
            return;
        }
        BigDecimal totalAmount = recycleOrderGoods.stream().filter(o->o.getGoodsAmount()!=null).map(YeloLifeRecycleOrderGoodsModel::getGoodsAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
        //更新新生订单审核信息
        TRenewableAudit upRenewableAudit = new TRenewableAudit();
        upRenewableAudit.setRenewableOrderCode(resultModel.getRecycleOrderCode());
        upRenewableAudit.setStatus(RenewableAuditStatusEnum.WAIT_ASSIGN.getKey());
        upRenewableAudit.setBusinessType(resultModel.getBusinessType());
        upRenewableAudit.setSource(RenewableSourceTypeEnum.YELOLIFE_SYNC.getKey());
        upRenewableAudit.setGoodsAmountTotal(totalAmount);
        upRenewableAudit.setCustomerName(resultModel.getCustomerName());
        upRenewableAudit.setCustomerUserName(resultModel.getCustomerUserName());
        upRenewableAudit.setCustomerUserMobile(resultModel.getCustomerUserMobile());
        upRenewableAudit.setPublishTime(new Date());
        upRenewableAudit.setPublishUserName(resultModel.getPublishUserName());
        upRenewableAudit.setPublishUserMobile(resultModel.getPublishUserMobile());
        upRenewableAudit.setRemark(resultModel.getRemark());
        commonBiz.setBaseEntityAdd(upRenewableAudit, resultModel.getOperatorName());
        tRenewableAuditMapper.insertSelectiveEncrypt(upRenewableAudit);

        //地址信息
        TRenewableAuditAddress auditAddress = new TRenewableAuditAddress();
        auditAddress.setRenewableOrderId(upRenewableAudit.getId());
        auditAddress.setLoadProvinceId(resultModel.getLoadProvinceId());
        auditAddress.setLoadProvinceName(resultModel.getLoadProvinceName());
        auditAddress.setLoadCityId(resultModel.getLoadCityId());
        auditAddress.setLoadCityName(resultModel.getLoadCityName());
        auditAddress.setLoadAreaId(resultModel.getLoadAreaId());
        auditAddress.setLoadAreaName(resultModel.getLoadAreaName());
        auditAddress.setLoadDetailAddress(resultModel.getLoadDetailAddress());
        auditAddress.setLoadWarehouse(resultModel.getLoadWarehouse());
        auditAddress.setConsignorName(resultModel.getConsignorName());
        auditAddress.setConsignorMobile(resultModel.getConsignorMobile());
        commonBiz.setBaseEntityAdd(auditAddress, resultModel.getOperatorName());
        tRenewableAuditAddressMapper.insertSelectiveEncrypt(auditAddress);

        //封装货物信息
        List<TRenewableAuditGoods> renewableAuditGoodList = new ArrayList<>();
        TRenewableAuditGoods renewableAuditGoods;
        for (YeloLifeRecycleOrderGoodsModel recycleOrderGood : recycleOrderGoods) {
            renewableAuditGoods = new TRenewableAuditGoods();
            renewableAuditGoods.setRenewableOrderId(upRenewableAudit.getId());
            renewableAuditGoods.setGoodsName(recycleOrderGood.getGoodsName());
            renewableAuditGoods.setRenewableSkuCode(recycleOrderGood.getSkuCode());
            renewableAuditGoods.setGoodsAmount(recycleOrderGood.getGoodsAmount());
            renewableAuditGoods.setGoodsPrice(recycleOrderGood.getGoodsPrice());
            if (CommonConstant.INTEGER_ONE.equals(recycleOrderGood.getGoodsUnit())) {
                renewableAuditGoods.setGoodsUnit(YeloLifeGoodsUnitEnum.BY_WEIGHT.getKey());
            } else if (CommonConstant.INTEGER_TWO.equals(recycleOrderGood.getGoodsUnit())) {
                renewableAuditGoods.setGoodsUnit(YeloLifeGoodsUnitEnum.BY_PACKAGE.getKey());
            }
            renewableAuditGoods.setGoodsSourceType(RenewableGoodsSourceTypeEnum.YELOLIFE_SYNC.getKey());
            commonBiz.setBaseEntityAdd(renewableAuditGoods, resultModel.getOperatorName());
            renewableAuditGoodList.add(renewableAuditGoods);
        }
        //插入货物信息
        tRenewableAuditGoodsMapper.batchInsert(renewableAuditGoodList);

        //记录日志
        tOperateLogsMapper.insertSelective(commonBiz.addOperateLogs(upRenewableAudit.getId(), OperateLogsOperateTypeEnum.RENEWABLE_AUDIT_ORDER_ORDER, resultModel.getRemark(), resultModel.getOperatorName()));

        //异步更新收发货地址经纬度
        AsyncProcessQueue.execute(() -> renewableAuditCommonBiz.updateRenewableAddressLonAndLat(auditAddress));
    }

    /**
     * 取消回收单(新生调用),并同时取消审核记录
     *
     * @param requestModel 回收单号
     */
    public void cancelRecycleOrder(CancelRecycleOrderRequestModel requestModel) {
        //根据回收单号查询订单审核信息是否存在
        TRenewableAudit dbRenewableAudit = tRenewableAuditMapper.getByRenewableOrderCode(requestModel.getRecycleOrderCode());
        if (dbRenewableAudit == null) {
            throw new BizException(CarrierDataExceptionEnum.RECYCLE_ORDER_IS_NULL);
        }

        //分布式锁对象
        String key = CommonConstant.UPDATE_RECYCLE_ORDER_PREFIX + requestModel.getRecycleOrderCode();
        String uuid = commonBiz.getDistributedLock(key);
        try {
            //已取消，不能操作
            if (RenewableAuditStatusEnum.HAS_CANCELED.getKey().equals(dbRenewableAudit.getStatus())) {
                throw new BizException(CarrierDataExceptionEnum.RECYCLE_ORDER_CANCELED);
            }
            //已审核，不能操作
            if (RenewableAuditStatusEnum.AUDIT_THROUGH.getKey().equals(dbRenewableAudit.getStatus())) {
                throw new BizException(CarrierDataExceptionEnum.RECYCLE_ORDER_AUDIT_THROUGH);
            }

            OperateLogsOperateTypeEnum operateLogsOperateTypeEnum;
            Integer auditStatus;
            if (CommonConstant.INTEGER_ONE.equals(requestModel.getOperationType())) {//取消
                //取消订单审核信息
                auditStatus = RenewableAuditStatusEnum.HAS_CANCELED.getKey();

                //记录取消操作日志
                operateLogsOperateTypeEnum = OperateLogsOperateTypeEnum.RENEWABLE_AUDIT_ORDER_CANCEL;
            } else {
                //非法操作值
                throw new BizException(CarrierDataExceptionEnum.OPERATION_TYPE_ERROR);
            }

            //更新新生订单审核信息
            TRenewableAudit upRenewableAudit = new TRenewableAudit();
            upRenewableAudit.setId(dbRenewableAudit.getId());
            upRenewableAudit.setStatus(auditStatus);
            upRenewableAudit.setAuditTime(requestModel.getOperationTime());
            upRenewableAudit.setAuditorName(requestModel.getOperator());
            commonBiz.setBaseEntityModify(upRenewableAudit, requestModel.getOperator());
            tRenewableAuditMapper.updateByPrimaryKeySelectiveEncrypt(upRenewableAudit);

            //记录操作日志
            tOperateLogsMapper.insertSelective(commonBiz.addOperateLogs(
                    dbRenewableAudit.getId(),
                    operateLogsOperateTypeEnum,
                    "",
                    requestModel.getOperator()));
        } finally {
            //释放锁
            commonBiz.removeDistributedLock(key, uuid);
        }
    }
}
