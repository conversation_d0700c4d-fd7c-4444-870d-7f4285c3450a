package com.logistics.management.webapi.controller.companycarrier.mapping;

import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.client.companycarrier.response.SearchCompanyCarrierListResponseModel;
import com.logistics.management.webapi.controller.companycarrier.response.SearchCompanyCarrierListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @Author: sj
 * @Date: 2019/9/29 9:24
 */
public class CompanyCarrierSearchListMapping extends MapperMapping<SearchCompanyCarrierListResponseModel,SearchCompanyCarrierListResponseDto> {
    @Override
    public void configure() {
        SearchCompanyCarrierListResponseModel source = this.getSource();
        SearchCompanyCarrierListResponseDto destination = this.getDestination();
        if(source!=null){
            if (CertificateStatusEnum.ENABLED.getKey().equals(source.getRoadTransportCertificateIsAmend()) || CertificateStatusEnum.ENABLED.getKey().equals(source.getTradingCertificateIsAmend())) {
                destination.setCertificateSupplement(CertificateStatusEnum.ENABLED.getValue());
            } else {
                destination.setCertificateSupplement(CertificateStatusEnum.DISABLED.getValue());
            }
            destination.setTypeLabel(CompanyTypeEnum.getEnum(source.getType()).getValue());
            //2为个人类型展示姓名手机号
            if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getType())) {
                destination.setCompanyCarrierName(source.getCarrierContactName() + " " + source.getCarrierContactPhone());
                //实名状态
                destination.setRealNameAuthenticationStatusLabel(RealNameAuthenticationStatusEnum.getEnum(source.getRealNameAuthenticationStatus()).getValue());
            } else {
                //授权状态
                destination.setAuthorizationStatusLabel(CompanyCarrierAuthAuditStatusEnum.getEnum(source.getAuthorizationStatus()).getValue());
            }

            //黑名单
            destination.setIfAddBlacklistLabel(YesOrNoEnum.getEnum(source.getIfAddBlacklist()).getValue());

            //零担模式
            destination.setIfLessThanTruckloadLabel(YesOrNoEnum.getEnum(source.getIfLessThanTruckload()).getValue());
        }
    }
}
