package com.logistics.management.webapi.api.feign.carriercontact.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.carriercontact.CarrierContactApi;
import com.logistics.management.webapi.api.feign.carriercontact.dto.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;

/**
 * @Author: sj
 * @Date: 2019/10/14 13:35
 */
@Component
public class CarrierContactApiHystrix implements CarrierContactApi {

    @Override
    public Result<PageInfo<SearchCarrierContactResponseDto>> searchList(SearchCarrierContactRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void exportCarrierContact(SearchCarrierContactRequestDto requestDto, HttpServletResponse response) {
         Result.timeout();
    }

    @Override
    public Result<CarrierContactDetailResponseDto> getDetail(CarrierContactDetailRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> saveAccount(SaveCarrierContactRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> enableDisableClosed(CarrierContactEnableRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> delCarrierAccount(DelCarrierContactRequestDto requestDto) {
        return Result.timeout();
    }
}
