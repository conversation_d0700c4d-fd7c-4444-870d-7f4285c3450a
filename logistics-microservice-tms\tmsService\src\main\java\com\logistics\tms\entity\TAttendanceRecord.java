package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/11/10
*/
@Data
public class TAttendanceRecord extends BaseEntity {

    /**
    * 驾驶员ID
    */
    @ApiModelProperty("驾驶员ID")
    private Long staffId;

    /**
    * 驾驶员姓名
    */
    @ApiModelProperty("驾驶员姓名")
    private String staffName;

    /**
    * 驾驶员手机号
    */
    @ApiModelProperty("驾驶员手机号")
    private String staffMobile;

    /**
    * 人员机构：1 自主，2 外部，3 自营
    */
    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    /**
    * 考勤日期(年月日)
    */
    @ApiModelProperty("考勤日期(年月日)")
    private Date attendanceDate;

    /**
    * 工时
    */
    @ApiModelProperty("工时")
    private BigDecimal manHour;

    /**
    * 上班打卡时间
    */
    @ApiModelProperty("上班打卡时间")
    private Date onDutyPunchTime;

    /**
    * 上班打卡定位地址
    */
    @ApiModelProperty("上班打卡定位地址")
    private String onDutyPunchLocation;

    /**
    * 上班打卡照片
    */
    @ApiModelProperty("上班打卡照片")
    private String onDutyPunchPic;

    /**
    * 下班打卡时间
    */
    @ApiModelProperty("下班打卡时间")
    private Date offDutyPunchTime;

    /**
    * 下班打卡定位地址
    */
    @ApiModelProperty("下班打卡定位地址")
    private String offDutyPunchLocation;

    /**
    * 下班打卡图片
    */
    @ApiModelProperty("下班打卡图片")
    private String offDutyPunchPic;

    /**
    * 是否有效1有效0无效
    */
    @ApiModelProperty("是否有效1有效0无效")
    private Integer valid;
}