package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2021/9/22 15:22
 */
@Data
public class CarrierOrderCorrectDetailRequestDto {

    @ApiModelProperty(value = "运单ID",required = true)
    @NotBlank(message = "id不能为空")
    private String carrierOrderId;

    @ApiModelProperty(value = "操作类型：1 纠错，2 查看",required = true)
    @NotBlank(message = "操作类型（1 纠错，2 查看）不能为空")
    private String operateType;
}
