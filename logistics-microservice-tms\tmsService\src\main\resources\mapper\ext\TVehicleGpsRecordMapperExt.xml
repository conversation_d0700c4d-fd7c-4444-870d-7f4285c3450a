<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleGpsRecordMapper" >

  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TVehicleGpsRecord" useGeneratedKeys="true" keyProperty="id">
    <foreach collection="list" item="temp" separator=";">
      insert into t_vehicle_gps_record
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="temp.id != null" >
          id,
        </if>
        <if test="temp.vehicleId != null" >
          vehicle_id,
        </if>
        <if test="temp.installTime != null" >
          install_time,
        </if>
        <if test="temp.terminalType != null" >
          terminal_type,
        </if>
        <if test="temp.simNumber != null" >
          sim_number,
        </if>
        <if test="temp.gpsServiceProvider != null" >
          gps_service_provider,
        </if>
        <if test="temp.remark != null" >
          remark,
        </if>
        <if test="temp.createdBy != null" >
          created_by,
        </if>
        <if test="temp.createdTime != null" >
          created_time,
        </if>
        <if test="temp.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="temp.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="temp.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="temp.id != null" >
          #{temp.id,jdbcType=BIGINT},
        </if>
        <if test="temp.vehicleId != null" >
          #{temp.vehicleId,jdbcType=BIGINT},
        </if>
        <if test="temp.installTime != null" >
          #{temp.installTime,jdbcType=TIMESTAMP},
        </if>
        <if test="temp.terminalType != null" >
          #{terminalType,jdbcType=VARCHAR},
        </if>
        <if test="temp.simNumber != null" >
          #{temp.simNumber,jdbcType=VARCHAR},
        </if>
        <if test="temp.gpsServiceProvider != null" >
          #{temp.gpsServiceProvider,jdbcType=VARCHAR},
        </if>
        <if test="temp.remark != null" >
          #{temp.remark,jdbcType=VARCHAR},
        </if>
        <if test="temp.createdBy != null" >
          #{temp.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="temp.createdTime != null" >
          #{temp.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="temp.lastModifiedBy != null" >
          #{temp.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="temp.lastModifiedTime != null" >
          #{temp.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="temp.valid != null" >
          #{temp.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
 </insert>
  <select id="getVehicleGpsRecordByVehicleIds" resultMap="VehicleGpsRecordDetailMap">
select
  tqvgr.id as vehicleGpsRecordId,
  tqvgr.install_time as installTime,
  tqvgr.terminal_type as terminalType,
  tqvgr.sim_number as simNumber,
  tqvgr.gps_service_provider as gpsServiceProvider,
  tqvgr.remark as remark,
  tqvgr.last_Modified_By,
  tqvgr.last_modified_time,
  tqcp.id as fileId,
  tqcp.file_path,
  tqcp.upload_user_name,
  tqcp.upload_time
from t_vehicle_gps_record tqvgr
  LEFT JOIN t_certification_pictures tqcp ON tqcp.object_id = tqvgr.id AND tqcp.object_type = 10 and tqcp.valid=1
       where tqvgr.vehicle_id in (${vehicleIds}) and tqvgr.valid = 1
      order by tqvgr.install_time desc,tqvgr.id desc
  </select>
  <resultMap id="VehicleGpsRecordDetailMap" type="com.logistics.tms.controller.vehicleassetmanagement.response.VehicleGpsRecordListResponseModel">
    <id column="vehicleGpsRecordId" property="vehicleGpsRecordId" jdbcType="BIGINT"/>
    <result column="installTime" property="installTime" jdbcType="TIMESTAMP"/>
    <result column="terminalType" property="terminalType" jdbcType="VARCHAR"/>
    <result column="simNumber" property="simNumber" jdbcType="VARCHAR"/>
    <result column="gpsServiceProvider" property="gpsServiceProvider" jdbcType="VARCHAR"/>
    <result column="last_Modified_By" property="lastModifiedBy" jdbcType="VARCHAR"/>
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP"/>
    <result column="remark" property="remark" jdbcType="VARCHAR"/>

    <collection property="fileList" ofType="com.logistics.tms.controller.vehicleassetmanagement.response.CertificationPicturesResponseModel">
      <id column="fileId" property="fileId" jdbcType="BIGINT" />
      <result column="file_path" property="relativeFilepath" jdbcType="VARCHAR"/>
      <result column="upload_user_name" property="uploadUserName" jdbcType="VARCHAR"/>
      <result column="upload_time" property="uploadTime" jdbcType="TIMESTAMP"/>
    </collection>
  </resultMap>
  
  <update id="batchUpdate">
    <foreach collection="list" item="item" separator=";">
      update t_vehicle_gps_record
      <set >
        <if test="item.vehicleId != null" >
          vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
        </if>
        <if test="item.installTime != null" >
          install_time = #{item.installTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.terminalType != null" >
          terminal_type = #{item.terminalType,jdbcType=VARCHAR},
        </if>
        <if test="item.simNumber != null" >
          sim_number = #{item.simNumber,jdbcType=VARCHAR},
        </if>
        <if test="item.gpsServiceProvider != null" >
          gps_service_provider = #{item.gpsServiceProvider,jdbcType=VARCHAR},
        </if>
        <if test="item.remark != null" >
          remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="countGpsRecordByDate" resultType="java.lang.Integer">
      select count(1)
      from t_vehicle_gps_record
      where valid =1
      and vehicle_id = #{vehicleId,jdbcType = BIGINT}
      and install_time = #{installTime,jdbcType = TIMESTAMP}
  </select>
</mapper>