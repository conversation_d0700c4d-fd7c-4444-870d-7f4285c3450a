package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

public class ExportExcelVehicleTypeInfo {
    private ExportExcelVehicleTypeInfo() {
    }

    private static final Map<String, String> VEHICLE_TYPE_INFO;

    static {
        VEHICLE_TYPE_INFO = new LinkedHashMap<>();
        VEHICLE_TYPE_INFO.put("状态", "enabledLabel");
        VEHICLE_TYPE_INFO.put("车辆类型", "vehicleType");
        VEHICLE_TYPE_INFO.put("车辆类别", "vehicleCategoryLabel");
        VEHICLE_TYPE_INFO.put("备注", "remark");
        VEHICLE_TYPE_INFO.put("添加人", "addUserName");
        VEHICLE_TYPE_INFO.put("最近编辑人", "lastModifiedBy");
        VEHICLE_TYPE_INFO.put("最新编辑时间", "lastModifiedTime");
    }

    public static Map<String, String> getVehicleTypeInfo() {
        return VEHICLE_TYPE_INFO;
    }
}
