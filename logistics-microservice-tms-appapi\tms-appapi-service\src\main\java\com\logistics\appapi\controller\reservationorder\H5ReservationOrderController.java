package com.logistics.appapi.controller.reservationorder;

import cn.dev33.satoken.annotation.SaIgnore;
import com.leyi.auth.service.client.common.PlatformProdEnums;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.AppApiExceptionEnum;
import com.logistics.appapi.base.utils.FrequentMethodUtils;
import com.logistics.appapi.base.utils.TimeSplitterUtils;
import com.logistics.appapi.client.auth.AuthTokenServiceApi;
import com.logistics.appapi.client.auth.request.CreateToken;
import com.logistics.appapi.client.auth.response.TokenModule;
import com.logistics.appapi.client.reservationorder.ReservationOrderClient;
import com.logistics.appapi.client.reservationorder.request.*;
import com.logistics.appapi.client.reservationorder.response.*;
import com.logistics.appapi.client.thirdparty.warehouse.stock.WarehouseStockClient;
import com.logistics.appapi.client.thirdparty.warehouse.stock.reservationconfig.response.SearchReservationTimeByWarehouseResponseModel;
import com.logistics.appapi.controller.common.CommonBiz;
import com.logistics.appapi.controller.reservationorder.mapping.*;
import com.logistics.appapi.controller.reservationorder.request.*;
import com.logistics.appapi.controller.reservationorder.response.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.utils.RedisUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 预约单管理
 * @author: wjf
 * @date: 2024/8/14 14:00
 */
@Slf4j
@Api(value = "预约单管理", tags = "预约单管理")
@RestController
@RequestMapping(value = "/api/reservationOrder/h5")
public class H5ReservationOrderController {

    @Resource
    private ReservationOrderClient reservationOrderClient;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private AuthTokenServiceApi authTokenServiceApi;




    /**
     * H5 获取token
     * @return
     */
    @ApiOperation("H5 获取token v2.45")
    @PostMapping(value = "/getToken")
    @SaIgnore
    public Result<H5GetTokenRespDto> getToken(@RequestBody @Valid H5GetTokenReqDto requestDto, HttpServletRequest httpServletRequest){
        //解密
        requestDto.setKey(commonBiz.decode(requestDto.getKey()));
        requestDto.setCarrierOrderCode(commonBiz.decode(requestDto.getCarrierOrderCode()));
        String key = (String)redisUtils.get(CommonConstant.KEY_TOKEN_REDIS+requestDto.getCarrierOrderCode());
        if (StringUtils.isNotEmpty(key) && key.equals(requestDto.getKey())){
            String ip = "";
            //重新构建二维码
            //申请token
            if (httpServletRequest.getHeader("x-forwarded-for") == null) {
                ip = httpServletRequest.getRemoteAddr();
            } else {
                ip = httpServletRequest.getHeader("x-forwarded-for").split(",")[0];
            }
            CreateToken createTokenModel = new CreateToken();
            createTokenModel.setLoginSource(PlatformProdEnums.LOGISTICS_TMS_DRIVER_APPLET.getCode());
            createTokenModel.setUserId(-1L);
            createTokenModel.setUserName(ip);
            createTokenModel.setUserAccount(ip);
            createTokenModel.setExpireTime("600");
            //请求auth服务生成token
            Result<TokenModule> token = authTokenServiceApi.createToken(createTokenModel);
            token.throwException();
            if (token.getData() != null) {
                H5GetTokenRespDto h5GetTokenRespDto = new H5GetTokenRespDto();
                h5GetTokenRespDto.setToken(token.getData().getToken());
                return Result.success(h5GetTokenRespDto);

            }
        }
        throw new BizException(AppApiExceptionEnum.FAIL_GET_TOKEN);
    }

    /**
     * H5 查询预约信息
     * @return
     */
    @ApiOperation("H5 查询预约信息 v2.45")
    @PostMapping(value = "/getReservationInfo")
    public Result<GetReservationInfo4H5RespDto> getReservationInfo4H5(@RequestBody @Valid GetReservationInfo4H5ReqDto requestDto){
        Result<GetReservationInfo4H5RespModel> result = reservationOrderClient.getReservationInfo4H5(MapperUtils.mapper(requestDto, GetReservationInfo4H5ReqModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),GetReservationInfo4H5RespDto.class));
    }


    /**
     * H5 确认预约
     * @return
     */
    @ApiOperation("H5 确认预约 v2.45")
    @PostMapping(value = "/confirmReservation4H5")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> confirmReservation4H5(@RequestBody @Valid ConfirmReservation4H5RequestDto requestDto){
        ConfirmReservationRequestModel requestModel = MapperUtils.mapper(requestDto, ConfirmReservationRequestModel.class);
        requestModel.setSource(1);
        Result<Boolean> result = reservationOrderClient.confirmReservation(requestModel);
        result.throwException();
        return result;
    }



    /**
     * H5 确认签到
     * @return
     */
    @ApiOperation("H5 确认签到 v2.45")
    @PostMapping(value = "/confirmSign4H5")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> confirmSign4H5(@RequestBody @Valid ReservationOrderConfirmSign4H5RequestDto requestDto){
        ReservationOrderConfirmSignRequestModel requestModel = MapperUtils.mapper(requestDto, ReservationOrderConfirmSignRequestModel.class);
        requestModel.setSource(1);
        Result<Boolean> result = reservationOrderClient.confirmSign(requestModel);
        result.throwException();
        return result;
    }


    /**
     * h5预约单详情 v2.45
     * @return
     */
    @ApiOperation("H5 预约单详情 v2.45")
    @PostMapping(value = "/reservationOrderDetail4H5")
    public Result<ReservationOrderSignDetail4H5ResponseDto> reservationOrderDetail4H5(@RequestBody @Valid ReservationOrderSignDetail45HRequestDto requestDto){
        ReservationOrderSignDetailRequestModel requestModel = MapperUtils.mapper(requestDto, ReservationOrderSignDetailRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        Result<ReservationOrderSignDetailResponseModel> result = reservationOrderClient.reservationOrderDetail(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), ReservationOrderSignDetail4H5ResponseDto.class, new ReservationOrderDetail4H5Mapping()));
    }


    /**
     * h5待预约详情 3.13.0
     * @return
     */
    @PostMapping(value = "/waitReservationDetail4H5")
    public Result<WaitReservationDetail4H5ResponseDto> waitReservationDetail4H5(@RequestBody @Valid WaitReservationDetail4H5RequestDto requestDto){
        WaitReservationDetailRequestModel requestModel = MapperUtils.mapper(requestDto, WaitReservationDetailRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
//        requestModel.setReservationType(1);
        Result<WaitReservationDetailResponseModel> result = reservationOrderClient.waitReservationDetail(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), WaitReservationDetail4H5ResponseDto.class, new H5WaitReservationDetailMapping()));
    }




    @ApiOperation(value = "预约按钮 进入预约页面 v2.45")
    @PostMapping(value = "/enterReservation")
    public Result<Boolean> enterReservation(@RequestBody @Valid EnterReservationRequestDto requestDto) {
        //解密入参
        if (StringUtils.isNotEmpty(requestDto.getIfNeedVerifyDriver()) && "1".equals(requestDto.getIfNeedVerifyDriver())){
            requestDto.setUserAccount(commonBiz.decode(requestDto.getUserAccount()));
            requestDto.setVerificationCode(commonBiz.decode(requestDto.getVerificationCode()));
            if (!FrequentMethodUtils.checkMobilePhone(requestDto.getUserAccount())){
                throw new BizException(AppApiExceptionEnum.MOBILE_PHONE_ERROR);
            }
        }
        log.info("预约按钮 进入预约页面入参："+requestDto);
        //验证手机号、验证能否预约
        Result<Boolean> result = reservationOrderClient.enterReservation(MapperUtils.mapperNoDefault(requestDto, EnterReservationRequestModel.class));
        result.throwException();
        return Result.success(true);
    }



    @ApiOperation(value = "签到按钮 进入签到页面 v2.45")
    @PostMapping(value = "/enterSignUp")
    public Result<Boolean> enterSignUp(@RequestBody @Valid EnterSignUpRequestDto requestDto) {
        //解密入参
        if (StringUtils.isNotEmpty(requestDto.getIfNeedVerifyDriver()) && "1".equals(requestDto.getIfNeedVerifyDriver())){
            requestDto.setUserAccount(commonBiz.decode(requestDto.getUserAccount()));
            requestDto.setVerificationCode(commonBiz.decode(requestDto.getVerificationCode()));
            if (!FrequentMethodUtils.checkMobilePhone(requestDto.getUserAccount())){
                throw new BizException(AppApiExceptionEnum.MOBILE_PHONE_ERROR);
            }
        }
        log.info("签到按钮 进入签到页面："+requestDto);
        //入参校验
        if (!FrequentMethodUtils.checkMobilePhone(requestDto.getUserAccount())){
            throw new BizException(AppApiExceptionEnum.MOBILE_PHONE_ERROR);
        }
        //验证手机号、验证能否签到
        Result<Boolean> result = reservationOrderClient.enterSignUp(MapperUtils.mapperNoDefault(requestDto, EnterSignUpRequestModel.class));
        result.throwException();
        return Result.success(true);
    }


}
