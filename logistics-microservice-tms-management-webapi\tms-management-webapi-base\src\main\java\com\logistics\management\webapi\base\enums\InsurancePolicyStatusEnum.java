package com.logistics.management.webapi.base.enums;

/**
 * @author: wjf
 * @date: 2019/6/5 11:53
 */
public enum InsurancePolicyStatusEnum {
    DEFAULT(0,"","0"),
    VALID(1, "保障中","1"),
    OVERDUE(2, "已过期","2"),
    NO_START(3, "未开始","3"),
    CANCEL(4, "已作废","4"),
    REFUND(5,"已退保","5"),
    ;

    private Integer key;
    private String value;
    private String keyStr;

    InsurancePolicyStatusEnum(Integer key, String value, String keyStr) {
        this.key = key;
        this.value = value;
        this.keyStr = keyStr;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getKeyStr() {
        return keyStr;
    }

    public static InsurancePolicyStatusEnum getEnum(Integer key) {
        for (InsurancePolicyStatusEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return DEFAULT;
    }
}
