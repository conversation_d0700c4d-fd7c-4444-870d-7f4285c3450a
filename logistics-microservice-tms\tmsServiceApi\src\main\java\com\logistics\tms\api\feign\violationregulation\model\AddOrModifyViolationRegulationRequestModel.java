package com.logistics.tms.api.feign.violationregulation.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/6/3 10:12
 */
@Data
public class AddOrModifyViolationRegulationRequestModel {
    @ApiModelProperty("违章记录ID")
    private Long violationRegulationId;
    @ApiModelProperty("车辆Id")
    private Long vehicleId;
    @ApiModelProperty("司机Id")
    private Long driverId;
    @ApiModelProperty("扣分")
    private Integer deduction;
    @ApiModelProperty("罚款")
    private BigDecimal fine;
    @ApiModelProperty("事故发生时间")
    private Date occuranceTime;
    @ApiModelProperty("事故发生地点")
    private String occuranceAddress;
    @ApiModelProperty("备注")
    @Size(max = 300,message = "备注不超过300字")
    private String remark;
    @ApiModelProperty("凭证列表")
    private List<String> fileList;
}
