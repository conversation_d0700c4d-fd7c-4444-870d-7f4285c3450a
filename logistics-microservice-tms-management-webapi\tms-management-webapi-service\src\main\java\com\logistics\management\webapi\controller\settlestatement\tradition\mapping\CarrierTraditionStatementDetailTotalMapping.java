package com.logistics.management.webapi.controller.settlestatement.tradition.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.SettleStatementStatusEnum;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.client.settlestatement.tradition.response.CarrierTraditionStatementDetailTotalResponseModel;
import com.logistics.management.webapi.client.settlestatement.tradition.response.CarrierTraditionStatementItemModel;
import com.logistics.management.webapi.controller.settlestatement.tradition.response.CarrierTraditionStatementDetailTotalResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/17
 */
public class CarrierTraditionStatementDetailTotalMapping extends MapperMapping<CarrierTraditionStatementDetailTotalResponseModel, CarrierTraditionStatementDetailTotalResponseDto> {
	@Override
	public void configure() {
		CarrierTraditionStatementDetailTotalResponseModel source = getSource();
		CarrierTraditionStatementDetailTotalResponseDto destination = getDestination();

		//对账单状态label
		destination.setSettleStatementStatusLabel(SettleStatementStatusEnum.getEnum(source.getSettleStatementStatus()).getValue());

		//获取对账单运单信息
		List<CarrierTraditionStatementItemModel> settleStatementItemList = source.getSettleStatementItemList();
		if (ListUtils.isNotEmpty(settleStatementItemList)) {
			//车主信息
			CarrierTraditionStatementItemModel carrierSettleStatementItemModelOne = settleStatementItemList.get(CommonConstant.INTEGER_ZERO);
			destination.setCompanyCarrierId(ConverterUtils.toString(carrierSettleStatementItemModelOne.getCompanyCarrierId()));
			Integer companyCarrierType = carrierSettleStatementItemModelOne.getCompanyCarrierType();
			if (CompanyTypeEnum.PERSONAL.getKey().equals(companyCarrierType)) {
				//个人车主 (联系人 手机号)
				destination.setCompanyCarrierName(carrierSettleStatementItemModelOne.getCarrierContactName() + " " + carrierSettleStatementItemModelOne.getCarrierContactMobile());
			} else {
				destination.setCompanyCarrierName(carrierSettleStatementItemModelOne.getCompanyCarrierName());
			}

			BigDecimal freightTaxPoint = source.getFreightTaxPoint();//运费费点
			BigDecimal carrierFreight = BigDecimal.ZERO;//运费
			BigDecimal carrierFreightTotal;//费额合计
			BigDecimal applyFeeTotal = BigDecimal.ZERO;//申请运费总额
			BigDecimal reconciliationFee;//对账费用

			BigDecimal settlementAmountTotal = BigDecimal.ZERO;//货物结算总数量
			//累加费用信息
			for (CarrierTraditionStatementItemModel carrierSettleStatementItemModel : settleStatementItemList) {
				//运费处理
				BigDecimal carrierFreightScope = carrierSettleStatementItemModel.getEntrustFreight();
				carrierFreight = carrierFreight.add(carrierFreightScope);

				//结算总数量
				settlementAmountTotal = settlementAmountTotal.add(carrierSettleStatementItemModel.getSettlementAmount());
			}

			//运费费额合计
			carrierFreightTotal = carrierFreight.add(carrierFreight.multiply(freightTaxPoint).divide(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_NET_ZERO, CommonConstant.INTEGER_TWO, RoundingMode.HALF_UP));

			//申请费用 费额合计
			applyFeeTotal = applyFeeTotal.add(carrierFreightTotal);
			//对账费用 申请费用总额+差异调整
			reconciliationFee = applyFeeTotal.add(source.getAdjustFee());

			//赋值费用信息
			destination.setCarrierFreight(carrierFreight.setScale(2, RoundingMode.HALF_UP).toPlainString());
			destination.setCarrierFreightTotal(carrierFreightTotal.setScale(2, RoundingMode.HALF_UP).toPlainString());
			destination.setApplyFeeTotal(applyFeeTotal.setScale(2, RoundingMode.HALF_UP).toPlainString());
			destination.setReconciliationFee(reconciliationFee.setScale(2, RoundingMode.HALF_UP).toPlainString());

			//结算总数量
			destination.setSettlementAmountTotal(settlementAmountTotal.setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());

			//创建日期
			destination.setCreatedTime(DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT.format(source.getCreatedTime()));
		}
	}
}
