package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TOperateLogs extends BaseEntity {
    /**
    * 对象类型
    */
    @ApiModelProperty("对象类型")
    private Integer objectType;

    /**
    * 对象ID
    */
    @ApiModelProperty("对象ID")
    private Long objectId;

    /**
    * 操作类型
    */
    @ApiModelProperty("操作类型")
    private Integer operateType;

    /**
    * 操作内容
    */
    @ApiModelProperty("操作内容")
    private String operateContents;

    /**
    * 操作人姓名
    */
    @ApiModelProperty("操作人姓名")
    private String operateUserName;

    /**
    * 操作时间
    */
    @ApiModelProperty("操作时间")
    private Date operateTime;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}