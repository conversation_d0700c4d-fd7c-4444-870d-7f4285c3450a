package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TInsurance extends BaseEntity {
    /**
    * 险种 1 商业险 2 交强险 3 个人意外险 4货物险 5 危货承运人险
    */
    @ApiModelProperty("险种 1 商业险 2 交强险 3 个人意外险 4货物险 5 危货承运人险")
    private Integer insuranceType;

    /**
    * 状态类型：0 正常状态，1 取消，2 退保
    */
    @ApiModelProperty("状态类型：0 正常状态，1 取消，2 退保")
    private Integer statusType;

    /**
    * 作废、退保原因
    */
    @ApiModelProperty("作废、退保原因")
    private String cancelReason;

    /**
    * 车辆ID
    */
    @ApiModelProperty("车辆ID")
    private Long vehicleId;

    /**
    * 车辆机构：1 自主，2 外部，3 自营
    */
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;

    /**
    * 司机ID
    */
    @ApiModelProperty("司机ID")
    private Long driverId;

    /**
    * 保险公司ID
    */
    @ApiModelProperty("保险公司ID")
    private Long insuranceCompanyId;

    /**
    * 保险单号
    */
    @ApiModelProperty("保险单号")
    private String policyNo;

    /**
    * 结算状态：0 待结算，1 已结算
    */
    @ApiModelProperty("结算状态：0 待结算，1 已结算")
    private Integer settlementStatus;

    /**
    * 保费
    */
    @ApiModelProperty("保费")
    private BigDecimal premium;

    /**
    * 保费
    */
    @ApiModelProperty("保费")
    private BigDecimal unpaidPremium;

    /**
    * 保险开始时间
    */
    @ApiModelProperty("保险开始时间")
    private Date startTime;

    /**
    * 保险截止时间
    */
    @ApiModelProperty("保险截止时间")
    private Date endTime;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 代缴车船税
    */
    @ApiModelProperty("代缴车船税")
    private BigDecimal paymentOfVehicleAndVesselTax;

    /**
    * 个人意外险ID
    */
    @ApiModelProperty("个人意外险ID")
    private Long personalAccidentInsuranceId;

    /**
    * 关联扣费个人意外险ID
    */
    @ApiModelProperty("关联扣费个人意外险ID")
    private Long relatedPersonalAccidentInsuranceId;

    /**
    * 车辆添加来源：1新增 2导入
    */
    @ApiModelProperty("车辆添加来源：1新增 2导入")
    private Integer source;

    /**
    * 退保金额
    */
    @ApiModelProperty("退保金额")
    private BigDecimal refundPremium;
}