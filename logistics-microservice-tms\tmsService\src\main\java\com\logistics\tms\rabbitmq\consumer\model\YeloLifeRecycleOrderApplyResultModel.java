package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 新生回收单申请结果model
 *
 * <AUTHOR>
 * @date ：Created in 2022/8/19
 */
@Data
public class YeloLifeRecycleOrderApplyResultModel {

	@ApiModelProperty("回收申请单号")
	private String recycleOrderCode;

	@ApiModelProperty("回收单申请结果: 1:确认 2:驳回")
	private Integer applyResult;

	@ApiModelProperty("审核人")
	private String auditorName;

	@ApiModelProperty("审核时间")
	private Date auditorTime;
}
