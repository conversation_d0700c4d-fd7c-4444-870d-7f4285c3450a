package com.logistics.management.webapi.controller.drivercostapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class DeductionsCostApplyRequestDto {

    @ApiModelProperty(value = "司机ID")
    @NotBlank(message = "司机ID不允许为空")
    private String driverId;

    @ApiModelProperty(value = "备用金余额")
    @NotBlank(message = "备用金余额不允许为空")
    private String reserveBalance;

    @ApiModelProperty(value = "扣款金额")
    @NotBlank(message = "扣款金额不允许为空")
    @DecimalMin(value = "0.01", message = "0 < 扣款金额 <= 备用金余额, 最多2位小数")
    @Digits(integer = 7, fraction = 2, message = "0 < 扣款金额 <= 备用金余额, 最多2位小数")
    private String deductionAmount;

    @ApiModelProperty(value = "发生时间")
    @NotBlank(message = "发生时间不允许为空")
    private String occurrenceTime;

    @ApiModelProperty(value = "费用依据")
    @NotEmpty(message = "请上传 1 ~ 6张费用依据图片")
    private List<String> costBasisImagePaths;

    @ApiModelProperty(value = "申请费用")
    @NotBlank(message = "请填写 1 ~ 100 字符申请费用")
    @Length(min = 1, max = 100, message = "请填写 1 ~ 100 字符申请费用")
    private String applyRemark;
}
