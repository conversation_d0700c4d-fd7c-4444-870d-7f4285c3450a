/**
 * Created by yun<PERSON><PERSON><PERSON> on 2017/12/12.
 */
package com.logistics.management.webapi.base.enums;

public enum OcrInvoiceTypeEnum {
    VALUE_ADDED_TAX("vat_invoice", InvoiceTypeEnum.VALUE_ADDED_TAX),
    TAXI("taxi_receipt", InvoiceTypeEnum.TAXI),
    TRAIN("train_ticket", InvoiceTypeEnum.TRAIN),

    QUOTA("quota_invoice", InvoiceTypeEnum.QUOTA),

    ROLL_TICKET("roll_normal_invoice", InvoiceTypeEnum.ROLL_TICKET),
    MACHINE_PRINTING("printed_invoice", InvoiceTypeEnum.MACHINE_PRINTING),
    PASSING_BY("toll_invoice", InvoiceTypeEnum.PASSING_BY),
    ;

    private final String key;
    private final InvoiceTypeEnum value;

    OcrInvoiceTypeEnum(String key, InvoiceTypeEnum value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public InvoiceTypeEnum getValue() {
        return value;
    }

    public static OcrInvoiceTypeEnum getEnum(String key) {
        for (OcrInvoiceTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
