package com.logistics.management.webapi.controller.reserveapply.mapping;

import com.logistics.management.webapi.client.reserveapply.response.ReserveApplyDetailResponseModel;
import com.logistics.management.webapi.controller.reserveapply.response.ReserveApplyDetailResponseDto;
import com.logistics.management.webapi.base.enums.ReserveApplyAuditStatusEnum;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@AllArgsConstructor
public class ReserveApplyDetailMapping extends MapperMapping<ReserveApplyDetailResponseModel, ReserveApplyDetailResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;
    private final String RECEIVING_ACCOUNT_FORMAT = "%s（%s%s）";

    @Override
    public void configure() {

        ReserveApplyDetailResponseModel source = getSource();
        ReserveApplyDetailResponseDto destination = getDestination();

        // 审核状态
        destination.setStatusLabel(ReserveApplyAuditStatusEnum.getEnumByKey(source.getStatus()).getValue());

        // 司机
        destination.setDriver(source.getDriverName() + " " + source.getDriverMobile());

        // 收款账户
        String receiveAccount = String.format(RECEIVING_ACCOUNT_FORMAT, source.getReceiveAccount(),
                source.getReceiveAccountName(), source.getReceiveBraBankName());
        destination.setReceiveAccount(receiveAccount);

        // 金额
        destination.setApplyAmount(amountConversion(source.getApplyAmount()));
        destination.setApproveAmount(amountConversion(source.getApproveAmount()));
        destination.setBalanceAmount(amountConversion(source.getBalanceAmount()));
        destination.setAwaitVerificationAmount(amountConversion(source.getAwaitVerificationAmount()));

        // 日期时间格式化
        destination.setRemitDate(dateConversion(source.getRemitDate(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        destination.setRemitOperateTime(dateConversion(source.getRemitOperateTime(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
        destination.setBusinessAuditTime(dateConversion(source.getBusinessAuditTime(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
        destination.setFinanceAuditTime(dateConversion(source.getFinanceAuditTime(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));

        // 图片转换
        destination.setRemitTickets(picConversion(source.getRemitTickets()));
    }

    // 图片访问路径转换
    private List<String> picConversion(List<String> tickets) {
        if (ListUtils.isEmpty(tickets)) {
            return Collections.emptyList();
        }
        return tickets.stream()
                .map(s -> imageMap.get(s))
                .filter(Objects::nonNull)
                .map(p -> imagePrefix.concat(p))
                .collect(Collectors.toList());
    }

    // 金额转换
    private String amountConversion(BigDecimal amount) {
        if (Objects.isNull(amount)) {
            return "";
        }
        return amount.stripTrailingZeros().toPlainString();
    }
    
    // 时间转换
    private String dateConversion(Date dateTime, String format) {
        if (Objects.isNull(dateTime)) {
            return "";
        }
        return DateUtils.dateToString(dateTime, format);
    }
}
