package com.logistics.tms.controller.vehicleassetmanagement;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.vehicleassetmanagement.VehicleAssetManagementBiz;
import com.logistics.tms.controller.vehicleassetmanagement.request.*;
import com.logistics.tms.controller.vehicleassetmanagement.response.*;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @date:2019/6/3 9:39
 */
@RestController
public class VehicleAssetManagementController {

    @Autowired
    private VehicleAssetManagementBiz vehicleAssetManagementBiz;

    @ApiOperation(value = "分页搜索挂车")
    @PostMapping(value = "/service/vehicleAssetManagement/searchTrailerVehicle")
    public Result<PageInfo<SearchTrailerVehicleResponseModel>> searchTrailerVehicle(@RequestBody SearchTrailerVehicleRequestModel requestModel) {
        return Result.success(vehicleAssetManagementBiz.searchTrailerVehicle(requestModel));
    }

    /**
     * 获取车辆资产列表
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取车辆资产列表")
    @PostMapping(value = "/service/vehicleAssetManagement/searchVehicleBasicList")
    public Result<PageInfo<VehicleAssetManagementListResponseModel>> searchVehicleBasicList(@RequestBody VehicleAssetManagementListRequestModel requestModel) {
        return Result.success(vehicleAssetManagementBiz.searchVehicleBasicList(requestModel));
    }

    /**
     * 删除
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "删除", notes = "")
    @PostMapping({"/service/vehicleAssetManagement/delVehicle"})
    public Result<Boolean> delVehicle(@RequestBody DeleteVehicleAssetManagementRequest requestModel) {
        vehicleAssetManagementBiz.delVehicle(requestModel);
        return Result.success(true);
    }

    /**
     * 导出车辆基础信息
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出车辆基础信息")
    @PostMapping({"/service/vehicleAssetManagement/exportVehicleInfo"})
    @IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<ExportVehicleBasicInfoResponseModel>> exportVehicleInfo(@RequestBody VehicleAssetManagementListRequestModel requestModel) {
        return Result.success(vehicleAssetManagementBiz.searchExportVehicleInfo(requestModel));
    }

    /**
     * 车辆资产详情
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆资产详情")
    @PostMapping({"/service/vehicleAssetManagement/getDetailList"})
    public Result<VehicleAssetManagementDetailResponseModel> getDetailList(@RequestBody VehicleAssetManagementDetailRequestModel requestModel) {
        return Result.success(vehicleAssetManagementBiz.getDetailListById(requestModel));
    }

    /**
     * 新增/修改车辆资产信息
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "新增/修改车辆资产信息")
    @PostMapping({"/service/vehicleAssetManagement/saveOrModify"})
    public Result<Boolean> saveVehicleInfo(@RequestBody AddOrModifyVehicleBasicInfoRequestModel requestModel) {
        vehicleAssetManagementBiz.saveVehicleInfo(requestModel);
        return Result.success(true);
    }

    /**
     * 新增外部车辆资产信息
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "新增外部车辆资产信息")
    @PostMapping({"/service/vehicleAssetManagement/saveOrModifyExternalVehicle"})
    public Result<Boolean> saveOrModifyExternalVehicle(@RequestBody SaveOrModifyExternalVehicleRequestModel requestModel) {
        vehicleAssetManagementBiz.saveOrModifyExternalVehicle(requestModel);
        return Result.success(true);
    }

    /**
     * 外部车辆资产详情
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "外部车辆资产详情")
    @PostMapping({"/service/vehicleAssetManagement/getExternalVehicle"})
    public Result<ExternalVehicleDetailResponseModel> getExternalVehicle(@RequestBody VehicleAssetManagementDetailRequestModel requestModel) {
        return Result.success(vehicleAssetManagementBiz.getExternalVehicle(requestModel));
    }

    /**
     * Excel导入车辆资产信息
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "Excel导入车辆资产信息")
    @PostMapping({"/service/VehicleAssetManagement/import"})
    public Result<ImportVehicleBasicInfoResponseModel> importVehicleBasicInfo(@RequestBody ImportVehicleBasicInfoRequestModel requestModel) {
        return Result.success(vehicleAssetManagementBiz.importVehicleBasicInfo(requestModel));
    }

    /**
     * 导入车辆证件信息
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导入车辆证件信息")
    @PostMapping({"/service/VehicleAssetManagement/importCertificateInfo"})
    @IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.REQUEST)
    public Result<Boolean> importVehicleCertificateInfo(@RequestBody ImportVehicleCertificateRequestModel requestModel) {
        vehicleAssetManagementBiz.importVehicleCertificateInfo(requestModel);
        return Result.success(true);
    }

    /**
     * 资产管理看板
     *
     * @return
     */
    @ApiOperation(value = "资产管理看板", notes = "")
    @PostMapping({"/service/VehicleAssetManagement/board"})
    public Result<AssetsBoardResponseModel> assetBoard() {
        return Result.success(vehicleAssetManagementBiz.assetBoard());
    }

    /**
     * 模糊查询车辆信息
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "模糊查询车辆信息")
    @PostMapping({"/service/VehicleAssetManagement/VehicleInfo"})
    public Result<List<FuzzyQueryVehicleInfoResponseModel>> fuzzyQueryVehicleInfo(@RequestBody FuzzyQueryVehicleInfoRequestModel requestModel) {
        return Result.success(vehicleAssetManagementBiz.fuzzyQueryVehicleInfo(requestModel));
    }

    /**
     * 车辆停运
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆停运")
    @PostMapping({"/service/VehicleAssetManagement/vehicleOutage"})
    public Result<Boolean> vehicleOutage(@RequestBody VehicleAssertOutageRequestModel requestModel) {
        vehicleAssetManagementBiz.vehicleOutage(requestModel);
        return Result.success(true);
    }

    /**
     * 车辆停运检测结果
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆停运检测结果")
    @PostMapping({"/service/VehicleAssetManagement/outageCheck"})
    public Result<VehicleAssertOutageCheckResponseModel> vehicleOutageResult(@RequestBody VehicleAssertOutageCheckRequestModel requestModel) {
        return Result.success(vehicleAssetManagementBiz.getOutageCheckInfo(requestModel));
    }

    /**
     * 根据车牌号模糊查询车辆司机GPS信息（牵引车和一体车）
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "根据车牌号模糊查询车辆司机GPS信息（牵引车和一体车）")
    @PostMapping({"/service/VehicleAssetManagement/getGpsInfoByVehicleNo"})
    public Result<List<GetGpsInfoByVehicleNoResponseModel>> getGpsInfoByVehicleNo(@RequestBody VehicleNoRequestModel requestModel) {
        return Result.success(vehicleAssetManagementBiz.getGpsInfoByVehicleNo(requestModel));
    }

    /**
     * 车辆报废详情
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆报废详情")
    @PostMapping({"/service/VehicleAssetManagement/vehicleScrapDetail"})
    public Result<VehicleAssertScrapDetailResponseModel> vehicleScrapDetail(@RequestBody VehicleAssertScrapDetailRequestModel requestModel) {
        return Result.success(vehicleAssetManagementBiz.vehicleScrapDetail(requestModel));
    }

    /**
     * 车辆恢复运营
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "车辆恢复运营")
    @PostMapping({"/service/VehicleAssetManagement/vehicleRestoration"})
    public Result<Boolean> vehicleRestoration(@RequestBody VehicleAssertRestorationRequestModel requestModel) {
        vehicleAssetManagementBiz.vehicleRestoration(requestModel);
        return Result.success(true);
    }

    /**
     * 根据车牌号模糊搜索、车辆机构查询车辆信息（分页）
     *
     * @param requestModel 筛选条件
     * @return 车辆信息
     */
    @ApiOperation(value = "根据车牌号模糊搜索、车辆机构查询车辆信息（分页）")
    @PostMapping({"/service/vehicleAssetManagement/searchVehicleByProperty"})
    public Result<PageInfo<SearchVehicleByPropertyResponseModel>> searchVehicleByProperty(@RequestBody SearchVehicleByPropertyRequestModel requestModel) {
        return Result.success(vehicleAssetManagementBiz.searchVehicleByProperty(requestModel));
    }
}
