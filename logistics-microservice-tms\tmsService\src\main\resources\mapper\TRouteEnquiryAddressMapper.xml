<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRouteEnquiryAddressMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TRouteEnquiryAddress" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="route_enquiry_id" property="routeEnquiryId" jdbcType="BIGINT" />
    <result column="from_province_id" property="fromProvinceId" jdbcType="BIGINT" />
    <result column="from_province_name" property="fromProvinceName" jdbcType="VARCHAR" />
    <result column="from_city_id" property="fromCityId" jdbcType="BIGINT" />
    <result column="from_city_name" property="fromCityName" jdbcType="VARCHAR" />
    <result column="from_area_id" property="fromAreaId" jdbcType="BIGINT" />
    <result column="from_area_name" property="fromAreaName" jdbcType="VARCHAR" />
    <result column="from_warehouse" property="fromWarehouse" jdbcType="VARCHAR" />
    <result column="to_province_id" property="toProvinceId" jdbcType="BIGINT" />
    <result column="to_province_name" property="toProvinceName" jdbcType="VARCHAR" />
    <result column="to_city_id" property="toCityId" jdbcType="BIGINT" />
    <result column="to_city_name" property="toCityName" jdbcType="VARCHAR" />
    <result column="to_area_id" property="toAreaId" jdbcType="BIGINT" />
    <result column="to_area_name" property="toAreaName" jdbcType="VARCHAR" />
    <result column="distance" property="distance" jdbcType="DECIMAL" />
    <result column="quote_price_type" property="quotePriceType" jdbcType="INTEGER" />
    <result column="quote_price" property="quotePrice" jdbcType="DECIMAL" />
    <result column="quote_remark" property="quoteRemark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, route_enquiry_id, from_province_id, from_province_name, from_city_id, from_city_name, 
    from_area_id, from_area_name, from_warehouse, to_province_id, to_province_name, to_city_id, 
    to_city_name, to_area_id, to_area_name, distance, quote_price_type, quote_price, 
    quote_remark, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_route_enquiry_address
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_route_enquiry_address
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TRouteEnquiryAddress" >
    insert into t_route_enquiry_address (id, route_enquiry_id, from_province_id, 
      from_province_name, from_city_id, from_city_name, 
      from_area_id, from_area_name, from_warehouse, 
      to_province_id, to_province_name, to_city_id, 
      to_city_name, to_area_id, to_area_name, 
      distance, quote_price_type, quote_price, 
      quote_remark, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{routeEnquiryId,jdbcType=BIGINT}, #{fromProvinceId,jdbcType=BIGINT}, 
      #{fromProvinceName,jdbcType=VARCHAR}, #{fromCityId,jdbcType=BIGINT}, #{fromCityName,jdbcType=VARCHAR}, 
      #{fromAreaId,jdbcType=BIGINT}, #{fromAreaName,jdbcType=VARCHAR}, #{fromWarehouse,jdbcType=VARCHAR}, 
      #{toProvinceId,jdbcType=BIGINT}, #{toProvinceName,jdbcType=VARCHAR}, #{toCityId,jdbcType=BIGINT}, 
      #{toCityName,jdbcType=VARCHAR}, #{toAreaId,jdbcType=BIGINT}, #{toAreaName,jdbcType=VARCHAR}, 
      #{distance,jdbcType=DECIMAL}, #{quotePriceType,jdbcType=INTEGER}, #{quotePrice,jdbcType=DECIMAL}, 
      #{quoteRemark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TRouteEnquiryAddress" >
    insert into t_route_enquiry_address
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="routeEnquiryId != null" >
        route_enquiry_id,
      </if>
      <if test="fromProvinceId != null" >
        from_province_id,
      </if>
      <if test="fromProvinceName != null" >
        from_province_name,
      </if>
      <if test="fromCityId != null" >
        from_city_id,
      </if>
      <if test="fromCityName != null" >
        from_city_name,
      </if>
      <if test="fromAreaId != null" >
        from_area_id,
      </if>
      <if test="fromAreaName != null" >
        from_area_name,
      </if>
      <if test="fromWarehouse != null" >
        from_warehouse,
      </if>
      <if test="toProvinceId != null" >
        to_province_id,
      </if>
      <if test="toProvinceName != null" >
        to_province_name,
      </if>
      <if test="toCityId != null" >
        to_city_id,
      </if>
      <if test="toCityName != null" >
        to_city_name,
      </if>
      <if test="toAreaId != null" >
        to_area_id,
      </if>
      <if test="toAreaName != null" >
        to_area_name,
      </if>
      <if test="distance != null" >
        distance,
      </if>
      <if test="quotePriceType != null" >
        quote_price_type,
      </if>
      <if test="quotePrice != null" >
        quote_price,
      </if>
      <if test="quoteRemark != null" >
        quote_remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="routeEnquiryId != null" >
        #{routeEnquiryId,jdbcType=BIGINT},
      </if>
      <if test="fromProvinceId != null" >
        #{fromProvinceId,jdbcType=BIGINT},
      </if>
      <if test="fromProvinceName != null" >
        #{fromProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="fromCityId != null" >
        #{fromCityId,jdbcType=BIGINT},
      </if>
      <if test="fromCityName != null" >
        #{fromCityName,jdbcType=VARCHAR},
      </if>
      <if test="fromAreaId != null" >
        #{fromAreaId,jdbcType=BIGINT},
      </if>
      <if test="fromAreaName != null" >
        #{fromAreaName,jdbcType=VARCHAR},
      </if>
      <if test="fromWarehouse != null" >
        #{fromWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="toProvinceId != null" >
        #{toProvinceId,jdbcType=BIGINT},
      </if>
      <if test="toProvinceName != null" >
        #{toProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="toCityId != null" >
        #{toCityId,jdbcType=BIGINT},
      </if>
      <if test="toCityName != null" >
        #{toCityName,jdbcType=VARCHAR},
      </if>
      <if test="toAreaId != null" >
        #{toAreaId,jdbcType=BIGINT},
      </if>
      <if test="toAreaName != null" >
        #{toAreaName,jdbcType=VARCHAR},
      </if>
      <if test="distance != null" >
        #{distance,jdbcType=DECIMAL},
      </if>
      <if test="quotePriceType != null" >
        #{quotePriceType,jdbcType=INTEGER},
      </if>
      <if test="quotePrice != null" >
        #{quotePrice,jdbcType=DECIMAL},
      </if>
      <if test="quoteRemark != null" >
        #{quoteRemark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TRouteEnquiryAddress" >
    update t_route_enquiry_address
    <set >
      <if test="routeEnquiryId != null" >
        route_enquiry_id = #{routeEnquiryId,jdbcType=BIGINT},
      </if>
      <if test="fromProvinceId != null" >
        from_province_id = #{fromProvinceId,jdbcType=BIGINT},
      </if>
      <if test="fromProvinceName != null" >
        from_province_name = #{fromProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="fromCityId != null" >
        from_city_id = #{fromCityId,jdbcType=BIGINT},
      </if>
      <if test="fromCityName != null" >
        from_city_name = #{fromCityName,jdbcType=VARCHAR},
      </if>
      <if test="fromAreaId != null" >
        from_area_id = #{fromAreaId,jdbcType=BIGINT},
      </if>
      <if test="fromAreaName != null" >
        from_area_name = #{fromAreaName,jdbcType=VARCHAR},
      </if>
      <if test="fromWarehouse != null" >
        from_warehouse = #{fromWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="toProvinceId != null" >
        to_province_id = #{toProvinceId,jdbcType=BIGINT},
      </if>
      <if test="toProvinceName != null" >
        to_province_name = #{toProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="toCityId != null" >
        to_city_id = #{toCityId,jdbcType=BIGINT},
      </if>
      <if test="toCityName != null" >
        to_city_name = #{toCityName,jdbcType=VARCHAR},
      </if>
      <if test="toAreaId != null" >
        to_area_id = #{toAreaId,jdbcType=BIGINT},
      </if>
      <if test="toAreaName != null" >
        to_area_name = #{toAreaName,jdbcType=VARCHAR},
      </if>
      <if test="distance != null" >
        distance = #{distance,jdbcType=DECIMAL},
      </if>
      <if test="quotePriceType != null" >
        quote_price_type = #{quotePriceType,jdbcType=INTEGER},
      </if>
      <if test="quotePrice != null" >
        quote_price = #{quotePrice,jdbcType=DECIMAL},
      </if>
      <if test="quoteRemark != null" >
        quote_remark = #{quoteRemark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TRouteEnquiryAddress" >
    update t_route_enquiry_address
    set route_enquiry_id = #{routeEnquiryId,jdbcType=BIGINT},
      from_province_id = #{fromProvinceId,jdbcType=BIGINT},
      from_province_name = #{fromProvinceName,jdbcType=VARCHAR},
      from_city_id = #{fromCityId,jdbcType=BIGINT},
      from_city_name = #{fromCityName,jdbcType=VARCHAR},
      from_area_id = #{fromAreaId,jdbcType=BIGINT},
      from_area_name = #{fromAreaName,jdbcType=VARCHAR},
      from_warehouse = #{fromWarehouse,jdbcType=VARCHAR},
      to_province_id = #{toProvinceId,jdbcType=BIGINT},
      to_province_name = #{toProvinceName,jdbcType=VARCHAR},
      to_city_id = #{toCityId,jdbcType=BIGINT},
      to_city_name = #{toCityName,jdbcType=VARCHAR},
      to_area_id = #{toAreaId,jdbcType=BIGINT},
      to_area_name = #{toAreaName,jdbcType=VARCHAR},
      distance = #{distance,jdbcType=DECIMAL},
      quote_price_type = #{quotePriceType,jdbcType=INTEGER},
      quote_price = #{quotePrice,jdbcType=DECIMAL},
      quote_remark = #{quoteRemark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>