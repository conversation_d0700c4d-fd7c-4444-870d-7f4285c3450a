package com.logistics.tms.controller.demandorder.request.sinopec;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/12
 */
@Data
public class SinopecDeclarationPortModel {

	@ApiModelProperty("港口编码")
	private String port;

	@ApiModelProperty("港口名称")
	private String portName;

	@ApiModelProperty("站编码")
	private String stringStation;

	@ApiModelProperty("站名称")
	private String stationName;

	@ApiModelProperty("省名称")
	private String provinceName;

	@ApiModelProperty("省编码")
	private String province;

	@ApiModelProperty("城市名称")
	private String cityName;

	@ApiModelProperty("城市编码")
	private String city;

	@ApiModelProperty("类型 1-地 2-港 3-站")
	private Integer type;
}
