<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDeductingHistoryMapper" >
  <select id="getByObjectTypeId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_deducting_history
    where valid = 1
    and object_type = #{objectType,jdbcType=INTEGER}
    and object_id = #{objectId,jdbcType=BIGINT}
    order by last_modified_time desc
  </select>

  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TDeductingHistory" >
    <foreach collection="list" item="item" separator=";">
      insert into t_deducting_history
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.objectType != null" >
          object_type,
        </if>
        <if test="item.objectId != null" >
          object_id,
        </if>
        <if test="item.deductingMonth != null" >
          deducting_month,
        </if>
        <if test="item.totalFee != null" >
          total_fee,
        </if>
        <if test="item.deductingFee != null" >
          deducting_fee,
        </if>
        <if test="item.remainingDeductingFee != null" >
          remaining_deducting_fee,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.objectType != null" >
          #{item.objectType,jdbcType=INTEGER},
        </if>
        <if test="item.objectId != null" >
          #{item.objectId,jdbcType=BIGINT},
        </if>
        <if test="item.deductingMonth != null" >
          #{item.deductingMonth,jdbcType=VARCHAR},
        </if>
        <if test="item.totalFee != null" >
          #{item.totalFee,jdbcType=DECIMAL},
        </if>
        <if test="item.deductingFee != null" >
          #{item.deductingFee,jdbcType=DECIMAL},
        </if>
        <if test="item.remainingDeductingFee != null" >
          #{item.remainingDeductingFee,jdbcType=DECIMAL},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="com.logistics.tms.entity.TDeductingHistory">
    <foreach collection="list" item="item" separator=";">
      update t_deducting_history
      <set >
        <if test="item.objectType != null" >
          object_type = #{item.objectType,jdbcType=INTEGER},
        </if>
        <if test="item.objectId != null" >
          object_id = #{item.objectId,jdbcType=BIGINT},
        </if>
        <if test="item.deductingMonth != null" >
          deducting_month = #{item.deductingMonth,jdbcType=VARCHAR},
        </if>
        <if test="item.totalFee != null" >
          total_fee = #{item.totalFee,jdbcType=DECIMAL},
        </if>
        <if test="item.deductingFee != null" >
          deducting_fee = #{item.deductingFee,jdbcType=DECIMAL},
        </if>
        <if test="item.remainingDeductingFee != null" >
          remaining_deducting_fee = #{item.remainingDeductingFee,jdbcType=DECIMAL},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <select id="getByTypeObjectIdMonth" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"/>
    from t_deducting_history
    where valid=1
    and object_type = #{objectType,jdbcType=INTEGER}
    and object_id = #{objectId,jdbcType=BIGINT}
    and deducting_month= #{settleMonth,jdbcType=VARCHAR}
  </select>
</mapper>