package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RenewableDemandListStatisticsResponseDto {

    @ApiModelProperty("全部")
    private String allCount="";

    @ApiModelProperty("待发布")
    private String waitPublishCount="";

    @ApiModelProperty("待调度")
    private String waitDispatchCount="";

    @ApiModelProperty("部分调度")
    private String partDispatchCount="";

    @ApiModelProperty("调度完成")
    private String dispatchCompleteCount="";

    @ApiModelProperty("待签收")
    private String waitSignedAccount="";

    @ApiModelProperty("已签收")
    private String signedAccount="";

    @ApiModelProperty("取消")
    private String cancelCount="";
}
