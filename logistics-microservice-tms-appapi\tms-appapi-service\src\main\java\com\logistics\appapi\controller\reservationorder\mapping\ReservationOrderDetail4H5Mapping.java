package com.logistics.appapi.controller.reservationorder.mapping;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.CarrierOrderStatusEnum;
import com.logistics.appapi.base.enums.EntrustTypeEnum;
import com.logistics.appapi.base.enums.ReservationTypeEnum;
import com.logistics.appapi.client.reservationorder.response.ReservationCarrierOrderListResponseModel;
import com.logistics.appapi.client.reservationorder.response.ReservationOrderSignDetailResponseModel;
import com.logistics.appapi.controller.reservationorder.response.ReservationCarrierOrderList4H5ResponseDto;
import com.logistics.appapi.controller.reservationorder.response.ReservationCarrierOrderListResponseDto;
import com.logistics.appapi.controller.reservationorder.response.ReservationOrderSignDetail4H5ResponseDto;
import com.logistics.appapi.controller.reservationorder.response.ReservationOrderSignDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/22 14:21
 */
public class ReservationOrderDetail4H5Mapping extends MapperMapping<ReservationOrderSignDetailResponseModel, ReservationOrderSignDetail4H5ResponseDto> {
    @Override
    public void configure() {
        ReservationOrderSignDetailResponseModel source = getSource();
        ReservationOrderSignDetail4H5ResponseDto destination = getDestination();

        //预约类型
        destination.setReservationTypeLabel(ReservationTypeEnum.getEnumByKey(source.getReservationType()).getValue());

        //地址
        StringBuilder addressBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(source.getWarehouse())){
            addressBuilder.append("【");
            addressBuilder.append(source.getWarehouse());
            addressBuilder.append("】");
        }
        addressBuilder.append(source.getProvinceName());
        addressBuilder.append(source.getCityName());
        addressBuilder.append(source.getAreaName());
        addressBuilder.append(source.getDetailAddress());
        destination.setAddress(addressBuilder.toString());

        //预约时间
        String reservationDate = DateUtils.dateToString(source.getReservationStartTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN);
        String reservationStartTime = DateUtils.dateToString(source.getReservationStartTime(), CommonConstant.DATE_TO_STRING_HM_PATTERN);
        String reservationEndTime = DateUtils.dateToString(source.getReservationEndTime(), CommonConstant.DATE_TO_STRING_HM_PATTERN);
        destination.setReservationTime(reservationDate+" "+reservationStartTime+CommonConstant.MINUS+reservationEndTime);

        //数据转换
        ReservationCarrierOrderList4H5ResponseDto dto;
        List<ReservationCarrierOrderList4H5ResponseDto> carrierOrderDtoList = new ArrayList<>();
        for (ReservationCarrierOrderListResponseModel model : source.getOrderList()) {
            dto = MapperUtils.mapper(model, ReservationCarrierOrderList4H5ResponseDto.class);

            //需求类型
            dto.setEntrustTypeLabel(EntrustTypeEnum.getEnum(model.getEntrustType()).getValue());

            //运单状态
            dto.setStatusLabel(CarrierOrderStatusEnum.getEnum(model.getStatus()).getValue());

            //运单预计数量
            dto.setExpectAmount(model.getExpectAmount().stripTrailingZeros().toPlainString());

            if (model.getExpectedTime()!=null){
                dto.setExpectedTime(DateUtils.dateToString(model.getExpectedTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }


            carrierOrderDtoList.add(dto);
        }
        destination.setOrderList(carrierOrderDtoList);
    }
}
