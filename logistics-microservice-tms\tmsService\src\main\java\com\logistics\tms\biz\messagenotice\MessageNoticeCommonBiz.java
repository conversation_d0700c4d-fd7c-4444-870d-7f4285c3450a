package com.logistics.tms.biz.messagenotice;

import com.logistics.tms.base.enums.MessageNoticeEnum;
import com.logistics.tms.base.enums.MessageNoticeModuleEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.messagenotice.model.AddMessageNoticeModel;
import com.logistics.tms.entity.TMessageNotice;
import com.logistics.tms.mapper.TMessageNoticeMapper;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.logistics.tms.rabbitmq.publisher.model.WebSocketAddMessage;
import com.logistics.tms.rabbitmq.publisher.model.WebSocketSendMessage;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.UUIDGenerateUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/6/5 11:10
 */
@Service
public class MessageNoticeCommonBiz {

    @Resource
    private TMessageNoticeMapper tMessageNoticeMapper;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private RabbitMqPublishBiz rabbitMqPublishBiz;

    /**
     * 新增消息通知并发弹窗
     * @param addMessageNoticeModel
     */
    @Transactional
    public void addMessageNotice(AddMessageNoticeModel addMessageNoticeModel){
        MessageNoticeEnum messageNoticeEnum = addMessageNoticeModel.getMessageNoticeEnum();
        MessageNoticeModuleEnum messageNoticeModuleEnum = messageNoticeEnum.getMessageNoticeModuleEnum();

        //后台
        if (MessageNoticeModuleEnum.MANAGEMENT.equals(messageNoticeModuleEnum)){
            addMessageNoticeModel.setMessageReceiverList(null);
        }
        //前台
        else if (MessageNoticeModuleEnum.WEB.equals(messageNoticeModuleEnum)){
            //接收方不能为空
            if (ListUtils.isEmpty(addMessageNoticeModel.getMessageReceiverList())) {
                return;
            }
        }else{
            return;
        }

        //新增消息通知
        Date now = new Date();
        TMessageNotice addMessageNotice;
        List<TMessageNotice> addMessageNoticeList = new ArrayList<>();
        WebSocketAddMessage webSocketAddMessage;
        List<WebSocketAddMessage> messageList = new ArrayList<>();
        if (ListUtils.isNotEmpty(addMessageNoticeModel.getMessageReceiverList())){
            for (Long messageReceiver : addMessageNoticeModel.getMessageReceiverList()) {
                //组装消息通知
                addMessageNotice = new TMessageNotice();
                addMessageNotice.setMessageId(UUIDGenerateUtil.generateUUID());
                addMessageNotice.setMessageType(messageNoticeEnum.getMessageNoticeMessageTypeEnum().getKey());
                addMessageNotice.setMessageModule(messageNoticeEnum.getMessageNoticeModuleEnum().getKey());
                addMessageNotice.setObjectType(messageNoticeEnum.getMessageNoticeObjectTypeEnum().getKey());
                addMessageNotice.setObjectId(addMessageNoticeModel.getObjectId());
                addMessageNotice.setObjectCode(addMessageNoticeModel.getObjectCode());
                addMessageNotice.setMessageBody(addMessageNoticeModel.getMessageBody());
                addMessageNotice.setMessagePusher(addMessageNoticeModel.getMessagePusher());
                addMessageNotice.setMessagePushTime(now);
                addMessageNotice.setMessageReceiver(messageReceiver);
                commonBiz.setBaseEntityAdd(addMessageNotice, addMessageNoticeModel.getMessagePusher());
                addMessageNoticeList.add(addMessageNotice);

                //组装websocket
                webSocketAddMessage = new WebSocketAddMessage();
                webSocketAddMessage.setMessageId(addMessageNotice.getMessageId());
                webSocketAddMessage.setMessage(addMessageNotice.getMessageBody());
                webSocketAddMessage.setObjectId(addMessageNoticeModel.getObjectId());
                webSocketAddMessage.setObjectCode(addMessageNoticeModel.getObjectCode());
                webSocketAddMessage.setObjectType(addMessageNotice.getMessageType().toString());
                webSocketAddMessage.setMsgTo(ConverterUtils.toString(addMessageNotice.getMessageReceiver()));
                messageList.add(webSocketAddMessage);
            }
        }else{
            //组装消息通知
            addMessageNotice = new TMessageNotice();
            addMessageNotice.setMessageId(UUIDGenerateUtil.generateUUID());
            addMessageNotice.setMessageType(messageNoticeEnum.getMessageNoticeMessageTypeEnum().getKey());
            addMessageNotice.setMessageModule(messageNoticeEnum.getMessageNoticeModuleEnum().getKey());
            addMessageNotice.setObjectType(messageNoticeEnum.getMessageNoticeObjectTypeEnum().getKey());
            addMessageNotice.setObjectId(addMessageNoticeModel.getObjectId());
            addMessageNotice.setObjectCode(addMessageNoticeModel.getObjectCode());
            addMessageNotice.setMessageBody(addMessageNoticeModel.getMessageBody());
            addMessageNotice.setMessagePusher(addMessageNoticeModel.getMessagePusher());
            addMessageNotice.setMessagePushTime(now);
            commonBiz.setBaseEntityAdd(addMessageNotice, addMessageNoticeModel.getMessagePusher());
            addMessageNoticeList.add(addMessageNotice);

            //组装websocket
            webSocketAddMessage = new WebSocketAddMessage();
            webSocketAddMessage.setMessageId(addMessageNotice.getMessageId());
            webSocketAddMessage.setMessage(addMessageNotice.getMessageBody());
            webSocketAddMessage.setObjectId(addMessageNoticeModel.getObjectId());
            webSocketAddMessage.setObjectCode(addMessageNoticeModel.getObjectCode());
            webSocketAddMessage.setObjectType(addMessageNotice.getMessageType().toString());
            webSocketAddMessage.setMsgTo(ConverterUtils.toString(addMessageNotice.getMessageReceiver()));
            messageList.add(webSocketAddMessage);
        }

        //插入消息通知
        if (ListUtils.isNotEmpty(addMessageNoticeList)){
            tMessageNoticeMapper.batchInsert(addMessageNoticeList);
        }

        //发送弹窗
        if (ListUtils.isNotEmpty(messageList)) {
            WebSocketSendMessage webSocketSendMessage = new WebSocketSendMessage();
            webSocketSendMessage.setUserName(addMessageNoticeModel.getMessagePusher());
            webSocketSendMessage.setMessageList(messageList);
            webSocketSendMessage.setModuleType(messageNoticeEnum.getWebSocketModuleTypeEnum().getKey());

            rabbitMqPublishBiz.sendSocketMessage(webSocketSendMessage);
        }
    }
}
