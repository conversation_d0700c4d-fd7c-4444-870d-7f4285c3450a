package com.logistics.management.webapi.controller.shippingorder.mapping;

import cn.hutool.core.date.DateUtil;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.ShippingOrderStatusEnum;
import com.logistics.management.webapi.client.shippingorder.response.GetShippingOrderDetailResponseModel;
import com.logistics.management.webapi.client.shippingorder.response.GetShippingOrderOperateLogListResponseModel;
import com.logistics.management.webapi.client.shippingorder.response.SearchShippingOrderListResponseModel;
import com.logistics.management.webapi.controller.shippingorder.response.GetShippingOrderDetailResponseDto;
import com.logistics.management.webapi.controller.shippingorder.response.GetShippingOrderOperateLogListResponseDto;
import com.logistics.management.webapi.controller.shippingorder.response.SearchShippingOrderListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringHelper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


public class ShippingOrderGetDetailMapping extends MapperMapping<GetShippingOrderDetailResponseModel, GetShippingOrderDetailResponseDto> {
    @Override
    public void configure() {
        GetShippingOrderDetailResponseModel source = getSource();
        GetShippingOrderDetailResponseDto destination = getDestination();

        List<GetShippingOrderOperateLogListResponseDto> operateLogList = new ArrayList<>();
        for (GetShippingOrderOperateLogListResponseModel getShippingOrderOperateLogListResponseModel : source.getOperateLogList()) {
            GetShippingOrderOperateLogListResponseDto operateLogDto = new GetShippingOrderOperateLogListResponseDto();
            operateLogDto.setOperateTime(DateUtil.format(getShippingOrderOperateLogListResponseModel.getOperateTime(), CommonConstant.YYYY_MM_DD_SPACE_HH_MM_SS));
            operateLogDto.setOperateContents(getShippingOrderOperateLogListResponseModel.getOperateContents());
            operateLogDto.setOperateUserName(getShippingOrderOperateLogListResponseModel.getOperateUserName());
            operateLogDto.setRemark(getShippingOrderOperateLogListResponseModel.getRemark());
            operateLogList.add(operateLogDto);
        }
        destination.setOperateLogList(operateLogList);
        destination.setStatusLabel(ShippingOrderStatusEnum.getEnum(source.getStatus()).getValue());
        // =整车费用+串点费用；
        destination.setExpectCarrierFreight(source.getCarrierFreight().add(source.getCrossPointFee()).toPlainString());
        destination.setAuditTime(DateUtil.format(source.getAuditTime(), CommonConstant.YYYY_MM_DD_SPACE_HH_MM_SS));

        destination.setVehicleLength(new BigDecimal("-1.00").equals(source.getVehicleLength()) ? "零担" : source.getVehicleLength().stripTrailingZeros().toPlainString());


    }
}
