package com.logistics.tms.controller.biddingorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/04/26
 */
@Data
public class BiddingQuoteDemandModel {

    /**
     * 需求单id
     */
    private Long demandId;

    /**
     * 发货地址
     */
    private String loadAddress;


    /**
     * 收货地址
     */
    private String unloadAddress;


    /**
     * 需求单号
     */
    private String demandOrderCode;


    /**
     * 数量
     */
    private BigDecimal goodsCount;


    /**
     * 一口价
     */
    private BigDecimal onePrice;

    /**
     * 单价
     */
    private BigDecimal unitPrice;
}
