package com.logistics.tms.controller.demandorder.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2018/11/12 11:33
 */
@Data
public class DemandOrderOrderRelModel {
    private Long demandOrderOrderRelId;
    private Long demandOrderId;
    private Long orderId;
    private String orderCode;
    private BigDecimal totalAmount;
    private BigDecimal arrangedAmount;
    private BigDecimal backAmount;
    private Integer relType;
    private String remark;
}
