package com.logistics.management.webapi.controller.carrierorderotherfee.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetOtherFeeRecordResponseDto {

    @ApiModelProperty("操作人")
    private String operateUserName = "";
    @ApiModelProperty("操作时间")
    private String operateTime = "";
    @ApiModelProperty("操作类型")
    private String operateType = "";
    @ApiModelProperty("说明")
    private String operateContents = "";
    @ApiModelProperty("备注")
    private String remark = "";
}
