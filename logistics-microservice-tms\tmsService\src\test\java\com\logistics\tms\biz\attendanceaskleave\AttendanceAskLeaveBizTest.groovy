package com.logistics.tms.biz.attendanceaskleave

import com.logistics.tms.controller.leave.request.DriverLeaveApplyCancelRequestModel
import com.logistics.tms.controller.leave.request.DriverLeaveApplyDetailRequestModel
import com.logistics.tms.controller.leave.response.DriverLeaveApplyDetailResponseModel
import com.logistics.tms.controller.leave.response.DriverLeaveApplyListItemModel
import com.logistics.tms.controller.leave.request.DriverLeaveApplyListRequestModel
import com.logistics.tms.controller.leave.response.DriverLeaveApplyListResponseModel
import com.logistics.tms.controller.leave.request.DriverLeaveApplyRequestModel
import com.logistics.tms.controller.leave.request.DriverLeaveApplyResubmitRequestModel
import com.logistics.tms.controller.leave.request.LeaveApplyCancelRequestModel
import com.logistics.tms.controller.leave.request.LeaveApplyDetailRequestModel
import com.logistics.tms.controller.leave.response.LeaveApplyDetailResponseModel
import com.logistics.tms.controller.leave.request.LeaveApplySearchListRequestModel
import com.logistics.tms.controller.leave.response.LeaveApplySearchListResponseModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.controller.leave.request.LeaveApplyAuditRequestModel
import com.logistics.tms.entity.TAttendanceAskLeave
import com.logistics.tms.entity.TStaffBasic
import com.logistics.tms.mapper.TAttendanceAskLeaveMapper
import com.logistics.tms.mapper.TStaffBasicMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import java.util.function.Function
import static org.mockito.Mockito.*

class AttendanceAskLeaveBizTest extends Specification {
    @Mock
    CommonBiz commonBiz
    @Mock
    TStaffBasicMapper staffBasicMapper
    @Mock
    TAttendanceAskLeaveMapper attendanceAskLeaveMapper
    @Mock
    Function<Long, Optional<TAttendanceAskLeave>> getLeaveApply
    @Mock
    Function<Long, Optional<TAttendanceAskLeave>> getDriverLeaveApply
    @InjectMocks
    AttendanceAskLeaveBiz attendanceAskLeaveBiz

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "driver Leave Apply where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)
        when(staffBasicMapper.getStaffByIds(anyString())).thenReturn([new TStaffBasic()])
        when(attendanceAskLeaveMapper.selectAllByAuditStatusIn(anyLong(), any())).thenReturn([new TAttendanceAskLeave()])

        expect:
        attendanceAskLeaveBiz.driverLeaveApply(requestModel) == expectedResult

        where:
        requestModel                       || expectedResult
        new DriverLeaveApplyRequestModel() || true
    }

    @Unroll
    def "driver Leave Apply List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)
        when(attendanceAskLeaveMapper.selectLeaveApplyList(anyLong(), anyString())).thenReturn([new DriverLeaveApplyListItemModel()])
        when(attendanceAskLeaveMapper.selectLeaveApplyListStatistical(anyLong(), anyString())).thenReturn(0)

        expect:
        attendanceAskLeaveBiz.driverLeaveApplyList(requestModel) == expectedResult

        where:
        requestModel                           || expectedResult
        new DriverLeaveApplyListRequestModel() || new DriverLeaveApplyListResponseModel()
    }

    @Unroll
    def "driver Leave Apply Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)
        when(attendanceAskLeaveMapper.selectDriverLeaveApplyDetail(anyLong(), anyLong())).thenReturn(new DriverLeaveApplyDetailResponseModel())

        expect:
        attendanceAskLeaveBiz.driverLeaveApplyDetail(requestModel) == expectedResult

        where:
        requestModel                             || expectedResult
        new DriverLeaveApplyDetailRequestModel() || new DriverLeaveApplyDetailResponseModel()
    }

    @Unroll
    def "driver Resubmit Leave Apply where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(attendanceAskLeaveMapper.updateByIdAndAuditStatus(any(), anyLong(), anyInt())).thenReturn(0)
        when(attendanceAskLeaveMapper.selectAllByAuditStatusIn(anyLong(), any())).thenReturn([new TAttendanceAskLeave()])

        expect:
        attendanceAskLeaveBiz.driverResubmitLeaveApply(requestModel) == expectedResult

        where:
        requestModel                               || expectedResult
        new DriverLeaveApplyResubmitRequestModel() || true
    }

    @Unroll
    def "driver Cancel Leave Apply where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(attendanceAskLeaveMapper.updateByIdAndAuditStatus(any(), anyLong(), anyInt())).thenReturn(0)

        expect:
        attendanceAskLeaveBiz.driverCancelLeaveApply(requestModel) == expectedResult

        where:
        requestModel                             || expectedResult
        new DriverLeaveApplyCancelRequestModel() || true
    }

    @Unroll
    def "search Leave Apply List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(attendanceAskLeaveMapper.searchLeaveApplyList(any())).thenReturn([new LeaveApplySearchListResponseModel()])

        expect:
        attendanceAskLeaveBiz.searchLeaveApplyList(requestModel) == expectedResult

        where:
        requestModel                           || expectedResult
        new LeaveApplySearchListRequestModel() || null
    }

    @Unroll
    def "cancel Leave Apply where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(attendanceAskLeaveMapper.updateByIdAndAuditStatus(any(), anyLong(), anyInt())).thenReturn(0)

        expect:
        attendanceAskLeaveBiz.cancelLeaveApply(requestModel) == expectedResult

        where:
        requestModel                       || expectedResult
        new LeaveApplyCancelRequestModel() || true
    }

    @Unroll
    def "leave Apply Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(attendanceAskLeaveMapper.selectLeaveApplyDetail(anyLong())).thenReturn(new LeaveApplyDetailResponseModel())

        expect:
        attendanceAskLeaveBiz.leaveApplyDetail(requestModel) == expectedResult

        where:
        requestModel                       || expectedResult
        new LeaveApplyDetailRequestModel() || new LeaveApplyDetailResponseModel()
    }

    @Unroll
    def "audit Leave Apply where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(attendanceAskLeaveMapper.updateByIdAndAuditStatus(any(), anyLong(), anyInt())).thenReturn(0)

        expect:
        attendanceAskLeaveBiz.auditLeaveApply(requestModel) == expectedResult

        where:
        requestModel                                                              || expectedResult
        new LeaveApplyAuditRequestModel() || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme