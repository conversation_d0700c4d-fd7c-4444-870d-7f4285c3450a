package com.logistics.tms.controller.oilfilled.request;

import com.logistics.tms.controller.oilfilled.response.OilFilledFileModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class AddOrModifyOilFilledRequestModel {

    @ApiModelProperty(value = "充油ID,修改时候传入，新增不传")
    private Long oilFilledId;
    @ApiModelProperty(value = "充油方式：1 充油卡，2 加油车")
    private Integer oilFilledType;
    @ApiModelProperty(value = "车牌号Id")
    private Long vehicleId;
    @ApiModelProperty(value = "车牌号号码")
    private String vehicleNo;
    @ApiModelProperty(value = "司机ID")
    private Long staffId;
    @ApiModelProperty(value = "司机")
    private String name;
    @ApiModelProperty(value = "副卡卡号，当充值方式为充油卡时候传")
    private String subCardNumber;
    @ApiModelProperty(value = "副卡所属人，当充值方式为充油卡时候传")
    private String subCardOwner;
    @ApiModelProperty(value = "充值金额/总金额")
    private BigDecimal oilFilledFee;
    @ApiModelProperty(value = "充值积分")
    private BigDecimal topUpIntegral;
    @ApiModelProperty(value = "奖励积分")
    private Integer rewardIntegral;
    @ApiModelProperty(value = "充值时间/加油时间")
    private Date oilFilledDate;
    @ApiModelProperty(value = "合作公司,当充值方式为加油车时候传")
    private String cooperationCompany;
    @ApiModelProperty(value = "升数,当充值方式为加油车时候传")
    private Integer liter;
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "附件集合")
    private List<OilFilledFileModel> oilFilledFileList;

}
