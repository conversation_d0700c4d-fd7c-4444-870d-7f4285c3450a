package com.logistics.appapi.controller.login.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2019/10/22 15:30
 */
@Data
public class DriverAppletLoginRequestDto {
    @ApiModelProperty(value = "登陆方式 1 验证码 2密码",required = true)
    @NotBlank(message = "登陆方式不能为空 1 验证码 2密码")
    private String loginType;
    @ApiModelProperty(value = "用户账号",required = true)
    @NotBlank(message = "账号不能为空")
    private String userAccount;
    @ApiModelProperty(value = "用户密码（密码登录时必填）")
    private String password;
    @ApiModelProperty(value = "手机验证码（验证码登录时必填）")
    private String verificationCode;
    @ApiModelProperty(value = "绑定微信code",required = true)
    @NotBlank(message = "code不能为空")
    private String code;

    @ApiModelProperty("拼图验证码X轴（密码登录时必填）")
    private String locationX;
    @ApiModelProperty("拼图验证码验证ID（密码登录时必填）")
    private String verificationId;
}
