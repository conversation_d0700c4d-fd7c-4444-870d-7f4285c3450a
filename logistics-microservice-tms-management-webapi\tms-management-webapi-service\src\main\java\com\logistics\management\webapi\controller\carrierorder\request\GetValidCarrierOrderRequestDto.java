package com.logistics.management.webapi.controller.carrierorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2022/9/6 10:59
 */
@Data
public class GetValidCarrierOrderRequestDto extends AbstractPageForm<GetValidCarrierOrderRequestDto> {
    @ApiModelProperty(value = "车主是否我司: 1 我司，2 其他车主",required = true)
    @NotBlank(message = "请选择车主")
    private String isOurCompany;

    @ApiModelProperty("车主id（其他车主时必填）")
    private String companyCarrierId;

    @ApiModelProperty(value = "运单号",required = true)
    @NotBlank(message = "请输入运单号")
    private String carrierOrderCode;
}
