package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 上报详情 响应dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/5/30
 */
@Data
public class SinopecReportAbnormalDetailResponseDto {

	@ApiModelProperty("需求单id")
	private String demandId = "";
	@ApiModelProperty("需求单号")
	private String demandOrderCode = "";
	@ApiModelProperty("货主公司名称")
	private String companyEntrustName = "";
	@ApiModelProperty("生产企业")
	private String manufacturerName = "";

	@ApiModelProperty("委托数量")
	private String goodsAmount = "";
	@ApiModelProperty("货物单位：1 件，2 吨，3 方，4 块")
	private String goodsUnit = "";
	@ApiModelProperty("备注")
	private String remark = "";

	@ApiModelProperty(value = "调度人员姓名")
	private String dispatcherName = "";
	@ApiModelProperty(value = "调度人员电话")
	private String dispatcherPhone = "";
	@ApiModelProperty("货主费用（单价）")
	private String contractPrice = "";


	//发货信息
	@ApiModelProperty("发货省份id")
	private String loadProvinceId = "";
	@ApiModelProperty("发货省份名字")
	private String loadProvinceName = "";
	@ApiModelProperty("发货城市id")
	private String loadCityId = "";
	@ApiModelProperty("发货城市名字")
	private String loadCityName = "";
	@ApiModelProperty("发货县区id")
	private String loadAreaId = "";
	@ApiModelProperty("发货县区名字")
	private String loadAreaName = "";
	@ApiModelProperty("发货详细地址")
	private String loadDetailAddress = "";
	@ApiModelProperty("发货仓库")
	private String loadWarehouse = "";

	//收货信息
	@ApiModelProperty("收货省份id")
	private String unloadProvinceId = "";
	@ApiModelProperty("收货省份名字")
	private String unloadProvinceName = "";
	@ApiModelProperty("收货城市id")
	private String unloadCityId = "";
	@ApiModelProperty("收货城市名字")
	private String unloadCityName = "";
	@ApiModelProperty("收货县区id")
	private String unloadAreaId = "";
	@ApiModelProperty("收货县区名字")
	private String unloadAreaName = "";
	@ApiModelProperty("收货详细地址")
	private String unloadDetailAddress = "";
}
