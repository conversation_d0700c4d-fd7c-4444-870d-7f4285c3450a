package com.logistics.management.webapi.base.enums;

/**
 * Created by yuhong.lin on 2019/1/18
 */
public enum AdjustFeeTypeEnum {
    MARK_UP(1, "加价"),
    MARK_DOWN(2, "减价")
    ;

    private Integer key;
    private String value;

    AdjustFeeTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static AdjustFeeTypeEnum getEnum(Integer key) {
        for (AdjustFeeTypeEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return null;
    }
}
