package com.logistics.management.webapi.api.impl.parkingfee;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.parkingfee.ParkingFeeApi;
import com.logistics.management.webapi.api.feign.parkingfee.dto.*;
import com.logistics.management.webapi.api.impl.parkingfee.mapping.ParkingFeeDeductingHistoryMapping;
import com.logistics.management.webapi.api.impl.parkingfee.mapping.ParkingFeeDetailMapping;
import com.logistics.management.webapi.api.impl.parkingfee.mapping.ParkingFeeListMapping;
import com.logistics.management.webapi.api.impl.parkingfee.mapping.ParkingFeeOperationRecordMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportParkingFeeDeductingHistoryInfo;
import com.logistics.management.webapi.base.constant.ExportParkingFeeInfo;
import com.logistics.management.webapi.base.constant.ExportParkingFeeOperationRecordInfo;
import com.logistics.tms.api.feign.parkingfee.ParkingFeeServiceApi;
import com.logistics.tms.api.feign.parkingfee.dto.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ExcelUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/10/9 16:02
 */
@RestController
public class ParkingFeeApiImpl implements ParkingFeeApi {
    @Autowired
    private ParkingFeeServiceApi parkingFeeServiceApi;

    /**
     * 列表查询
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<ParkingFeeListResponseDto>> searchList(@RequestBody ParkingFeeListRequestDto requestDto) {
        Result<PageInfo<ParkingFeeListResponseModel>> result = parkingFeeServiceApi.searchList(MapperUtils.mapper(requestDto, ParkingFeeListRequestModel.class));
        result.throwException();

        PageInfo pageInfo = result.getData();
        List<ParkingFeeListResponseDto> dtoList = MapperUtils.mapper( result.getData().getList(),ParkingFeeListResponseDto.class,new ParkingFeeListMapping());
        pageInfo.setList(dtoList == null ? new ArrayList() : dtoList);
        return Result.success(pageInfo);
    }

    /**
     * 获取列表汇总
     * @param requestDto
     * @return
     */
    @Override
    public Result<SummaryParkingFeeResponseDto> getSummary(@RequestBody ParkingFeeListRequestDto requestDto) {
        Result<SummaryParkingFeeResponseModel> result = parkingFeeServiceApi.getSummary(MapperUtils.mapper(requestDto,ParkingFeeListRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),SummaryParkingFeeResponseDto.class));
    }

    /**
     * 详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<ParkingFeeDetailResponseDto> getDetail(@RequestBody @Valid ParkingFeeDetailRequestDto requestDto) {
        Result<ParkingFeeDetailResponseModel> result = parkingFeeServiceApi.getDetail(MapperUtils.mapper(requestDto, ParkingFeeDetailRequestModel.class));
        result.throwException();
        return Result.success( MapperUtils.mapper(result.getData(),ParkingFeeDetailResponseDto.class,new ParkingFeeDetailMapping()));
    }

    /**
     * 修改/保存
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> saveOrUpdate(@RequestBody @Valid SaveOrUpdateParkingFeeRequestDto requestDto) {
        Result<Boolean> result = parkingFeeServiceApi.saveOrUpdate(MapperUtils.mapperNoDefault(requestDto,SaveOrUpdateParkingFeeRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 费用终止
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> terminateParkingFee(@RequestBody @Valid TerminateParkingFeeRequestDto requestDto) {
        Result<Boolean> result = parkingFeeServiceApi.terminateParkingFee(MapperUtils.mapperNoDefault(requestDto,TerminateParkingFeeRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 扣减历史列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<ParkingFeeDeductingHistoryResponseDto>> getDeductingHistoryList(@RequestBody @Valid ParkingFeeDeductingHistoryRequestDto requestDto) {
        Result<List<ParkingFeeDeductingHistoryResponseModel>> result = parkingFeeServiceApi.getDeductingHistoryList(MapperUtils.mapper(requestDto,ParkingFeeDeductingHistoryRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),ParkingFeeDeductingHistoryResponseDto.class,new ParkingFeeDeductingHistoryMapping()));
    }

    /**
     * 操作记录列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<ParkingFeeOperationRecordResponsesDto>> getOperationRecordList(@RequestBody @Valid ParkingFeeOperationRecordRequestDto requestDto) {
        Result<List<ParkingFeeOperationRecordResponsesModel>> result = parkingFeeServiceApi.getOperationRecordList(MapperUtils.mapper(requestDto,ParkingFeeOperationRecordRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),ParkingFeeOperationRecordResponsesDto.class,new ParkingFeeOperationRecordMapping()));
    }

    /**
     * 导出停车费用列表
     * @param requestDto
     * @param response
     */
    @Override
    public void exportParkingFeeList(ParkingFeeListRequestDto requestDto, HttpServletResponse response) {
        ParkingFeeListRequestModel requestModel = MapperUtils.mapper(requestDto,ParkingFeeListRequestModel.class);
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<ParkingFeeListResponseModel>> result = parkingFeeServiceApi.searchList(requestModel);
        result.throwException();

        List<ParkingFeeListResponseDto> dtoList = MapperUtils.mapper(result.getData().getList(),ParkingFeeListResponseDto.class,new ParkingFeeListMapping());
        String fileName = "停车费用" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportParkingFeeInfo.getExportParkingFeeMap();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return dtoList;
            }
        });
    }

    /**
     * 导出扣减历史列表
     * @param requestDto
     * @param response
     */
    @Override
    public void exportDeductingHistoryList(ParkingFeeDeductingHistoryRequestDto requestDto, HttpServletResponse response) {
        Result<List<ParkingFeeDeductingHistoryResponseModel>> result = parkingFeeServiceApi.getDeductingHistoryList(MapperUtils.mapper(requestDto,ParkingFeeDeductingHistoryRequestModel.class));
        result.throwException();

        List<ParkingFeeDeductingHistoryResponseDto> dtoList = MapperUtils.mapper(result.getData(),ParkingFeeDeductingHistoryResponseDto.class,new ParkingFeeDeductingHistoryMapping());
        String vehicleNo = dtoList.stream().filter(o->o.getVehicleNo()!=null).map(ParkingFeeDeductingHistoryResponseDto::getVehicleNo).findFirst().orElse("");
        String fileName = vehicleNo + "停车费用扣减记录" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportParkingFeeDeductingHistoryInfo.getExportParkingFeeDeductingHistoryMap();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return dtoList;
            }
        });
    }

    /**
     * 导出操作日志列表
     * @param requestDto
     * @param response
     */
    @Override
    public void exportOperationRecordList(ParkingFeeOperationRecordRequestDto requestDto, HttpServletResponse response) {
        Result<List<ParkingFeeOperationRecordResponsesModel>> result = parkingFeeServiceApi.getOperationRecordList(MapperUtils.mapper(requestDto,ParkingFeeOperationRecordRequestModel.class));
        result.throwException();

        List<ParkingFeeOperationRecordResponsesDto> dtoList = MapperUtils.mapper(result.getData(),ParkingFeeOperationRecordResponsesDto.class,new ParkingFeeOperationRecordMapping());
        String vehicleNo = dtoList.stream().filter(o->o.getVehicleNo()!=null).map(ParkingFeeOperationRecordResponsesDto::getVehicleNo).findFirst().orElse("");
        String fileName = vehicleNo + "停车费用操作记录" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportParkingFeeOperationRecordInfo.getExportParkingFeeOperationRecordMap();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return dtoList;
            }
        });
    }
}
