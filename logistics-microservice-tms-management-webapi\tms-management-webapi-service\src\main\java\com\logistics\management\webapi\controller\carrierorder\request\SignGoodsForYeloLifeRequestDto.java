package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/24
 */
@Data
public class SignGoodsForYeloLifeRequestDto {

	@ApiModelProperty(value = "货物id", required = true)
	@NotBlank(message = "货物id不能为空")
	private String goodsId;

	@ApiModelProperty(value = "签收数量 0<货物数量<=实卸数量", required = true)
	@NotBlank(message = "请输入签收数量")
	private String signAmount;


	@ApiModelProperty(value = "货物的编码集合 v2.44", required = true)
	private List<LoadGoodsForYeloLifeRequestCodeDto> codeDtoList;


}
