package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/22
 */
@Data
public class CarrierOrderTicketsResponseModel {

	@ApiModelProperty("单据类型：1 提货单 2 出库单 3 签收单 4 其他 5 到达提货地凭证 6 到达卸货地凭证")
	private Integer ticketType;

	@ApiModelProperty("单据")
	private List<TicketsResponseModel> ticketsList;
}
