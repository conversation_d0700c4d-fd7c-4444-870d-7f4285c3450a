package com.logistics.appapi.controller.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class SearchConsignorRequestDto {

	@ApiModelProperty("当前经度")
	private String longitude;

	@ApiModelProperty("当前纬度")
	private String latitude;

	@ApiModelProperty(value = "搜索方式：1 距离优先，2 综合",required = true)
	@NotBlank(message = "请选择搜索方式")
	private String searchType;

	@ApiModelProperty("公司名,发货仓库、发货省市区、发货详细地址，发货联系人姓名以及联系方式 查询条件")
	private String consignorCondition;
}
