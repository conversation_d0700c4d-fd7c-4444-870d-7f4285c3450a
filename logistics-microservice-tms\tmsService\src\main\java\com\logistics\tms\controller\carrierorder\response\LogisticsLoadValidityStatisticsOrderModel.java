package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2021/10/29 15:10
 */
@Data
public class LogisticsLoadValidityStatisticsOrderModel {
    @ApiModelProperty("提货时效（天）")
    private BigDecimal loadValidity;
    @ApiModelProperty("运单数")
    private BigDecimal carrierOrderCount;

    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;
}
