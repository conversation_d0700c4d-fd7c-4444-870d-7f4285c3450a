package com.logistics.tms.rabbitmq.consumer.yelolife;

import com.logistics.tms.biz.carrierorder.CarrierOrderForYloLifeBiz;
import com.logistics.tms.rabbitmq.consumer.model.YeloLifeCarrierOrderStockOutModel;
import com.rabbitmq.client.Channel;
import com.yelo.tools.rabbitmq.annocation.EnableMqErrorMessageCollect;
import com.yelo.tools.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * @author: wjf
 * @date: 2024/7/22 9:42
 */
@Component
@Slf4j
public class YeloLifeCarrierOrderStockOutConsumer {

    private ObjectMapper objectMapper = JacksonUtils.getInstance();
    @Resource
    private CarrierOrderForYloLifeBiz carrierOrderForYloLifeBiz;

    @EnableMqErrorMessageCollect
    @RabbitListener(bindings = {@QueueBinding(
            exchange = @Exchange(name = "logistics.qiyatms.topic", type = "topic", durable = "true"),
            value = @Queue(value = "logistics.lifeCarrierOrderStockOutSyncTms", durable = "true"),
            key = "lifeCarrierOrderStockOutSyncTms")}
            , concurrency = "3")
    public void process(@Payload String message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) throws IOException {
        log.info("新生运单出库同步到物流系统：" + message);
        YeloLifeCarrierOrderStockOutModel parse = objectMapper.readValue(message, YeloLifeCarrierOrderStockOutModel.class);
        carrierOrderForYloLifeBiz.updateLifeCarrierCodeStockOutState(parse);
        channel.basicAck(deliveryTag, false);
    }

}
