package com.logistics.management.webapi.client.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/9/11 19:42
 */
@Data
public class SaveDemandOrderRequestModel {
    @ApiModelProperty("发货公司id")
    private Long companyEntrustId;
    @ApiModelProperty("发货公司名称")
    private String companyEntrustName;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("发货地址")
    private Long loadProvinceId;
    private String loadProvinceName;
    private Long loadCityId;
    private String loadCityName;
    private Long loadAreaId;
    private String loadAreaName;
    private String loadWarehouse;
    private String loadDetailAddress;
    private String loadContactName;
    private String loadContactMobile;
    @ApiModelProperty("收货地址")
    private String unloadCompanyName;
    private Long unloadProvinceId;
    private String unloadProvinceName;
    private Long unloadCityId;
    private String unloadCityName;
    private Long unloadAreaId;
    private String unloadAreaName;
    private String unloadWarehouse;
    private String unloadDetailAddress;
    private String unloadContactName;
    private String unloadContactMobile;
    @ApiModelProperty("期望提货时间")
    private Date expectedLoadTime;
    @ApiModelProperty("期望卸货时间")
    private Date expectedUnloadTime;
    @ApiModelProperty("委托单货物信息")
    private List<SaveDemandOrderGoodsRequestModel> demandOrderGoodsList;
    @ApiModelProperty("是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;
    @ApiModelProperty("车主ID,非我司时填写")
    private Long companyCarrierId;
    @ApiModelProperty("合同价类型：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer contractPriceType;
    @ApiModelProperty("合同价")
    private BigDecimal contractPrice;
    @ApiModelProperty("车主价格类型：0 空，1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer carrierPriceType;
    @ApiModelProperty("车主价格：0 空")
    private BigDecimal carrierPrice;
    @ApiModelProperty("备注")
    private String remark;
}
