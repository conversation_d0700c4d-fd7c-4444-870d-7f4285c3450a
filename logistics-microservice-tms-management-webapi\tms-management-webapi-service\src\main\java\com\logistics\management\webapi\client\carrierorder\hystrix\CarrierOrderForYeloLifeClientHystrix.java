package com.logistics.management.webapi.client.carrierorder.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.carrierorder.CarrierOrderForYeloLifeClient;
import com.logistics.management.webapi.client.carrierorder.request.*;
import com.logistics.management.webapi.client.carrierorder.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/26 14:06
 */
@Component
public class CarrierOrderForYeloLifeClientHystrix implements CarrierOrderForYeloLifeClient {
    @Override
    public Result<PageInfo<SearchCarrierOrderListForYeloLifeResponseModel>> searchCarrierOrderListForYeloLife(SearchCarrierOrderListForYeloLifeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchCarrierOrderListForYeloLifeResponseModel>> exportCarrierOrderForYeloLife(SearchCarrierOrderListForYeloLifeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SearchCarrierListStatisticsResponseModel> searchListStatisticsForYeloLife(SearchCarrierOrderListForYeloLifeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierOrderDetailForYeloLifeResponseModel> carrierOrderDetailForYeloLife(CarrierOrderIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<TicketsModel>> getTicketsForYeloLife(CarrierOrderIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<LoadDetailForYeloLifeResponseModel> getLoadDetailForYeloLife(LoadDetailForYeloLifeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result loadForYeloLife(LoadForYeloLifeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> unloadForYeloLife(UnLoadForYeloLifeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SignDetailForYeloLifeResponseModel> carrierOrderListBeforeSignUpForYeloLife(CarrierOrderIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> carrierOrderSignUpForYeloLife(CarrierOrderSignUpForYeloLifeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<CarrierOrderBeforeSignUpForYeloLifeResponseModel>> carrierOrderBeforeSignUpForYeloLife(CarrierOrderBeforeSignUpForYeloLifeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> carrierOrderConfirmSignUpForYeloLife(CarrierOrderConfirmSignUpForYeloLifeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetYeloLifeUnloadWarehouseResponseModel>> getYeloLifeUnloadWarehouse(GetYeloLifeUnloadWarehouseRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<UpdateUnloadAddressDetailResponseModel> getUnloadAddressDetailForYeloLife(CarrierOrderIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> updateUnloadAddressConfirmForYeloLife(UpdateCarrierOrderUnloadAddressForLifeRequestModel requestModel) {
        return Result.timeout();
    }
}
