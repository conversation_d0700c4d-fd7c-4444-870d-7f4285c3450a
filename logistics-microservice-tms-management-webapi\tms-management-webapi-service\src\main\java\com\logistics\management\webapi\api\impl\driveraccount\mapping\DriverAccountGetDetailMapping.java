package com.logistics.management.webapi.api.impl.driveraccount.mapping;

import com.logistics.management.webapi.api.feign.driveraccount.dto.response.DriverAccountDetailResponseDto;
import com.logistics.management.webapi.api.feign.driveraccount.dto.response.DriverAccountImageDetailResponseDto;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.tms.api.feign.driveraccount.model.response.DriverAccountDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/27
 */
public class DriverAccountGetDetailMapping extends MapperMapping<DriverAccountDetailResponseModel, DriverAccountDetailResponseDto> {

	private final ConfigKeyConstant configKeyConstant;

	private final Map<String, String> imageMap;

	public DriverAccountGetDetailMapping(ConfigKeyConstant configKeyConstant, Map<String, String> imageMap) {
		this.configKeyConstant = configKeyConstant;
		this.imageMap = imageMap;
	}

	@Override
	public void configure() {
		DriverAccountDetailResponseModel source = getSource();
		DriverAccountDetailResponseDto destination = getDestination();

		//司机姓名
		destination.setDriverName(source.getDriverName() + " " + source.getDriverMobile());

		//账户图片
		List<String> bankAccountImages = source.getBankAccountImages();
		List<DriverAccountImageDetailResponseDto> bankAccountImagesDetailResult = new ArrayList<>();
		if (ListUtils.isNotEmpty(bankAccountImages)) {
			bankAccountImagesDetailResult = bankAccountImages
					.stream()
					.map(i -> {
						DriverAccountImageDetailResponseDto imageDetail = new DriverAccountImageDetailResponseDto();
						imageDetail.setFilePath(i);
						imageDetail.setImagePath(configKeyConstant.fileAccessAddress + imageMap.get(i));
						return imageDetail;
					})
					.collect(Collectors.toList());
		}
		destination.setBankAccountImages(bankAccountImagesDetailResult);
	}
}
