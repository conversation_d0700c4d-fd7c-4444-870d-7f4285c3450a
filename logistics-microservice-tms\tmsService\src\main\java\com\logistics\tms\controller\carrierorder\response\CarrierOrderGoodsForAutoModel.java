package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2021/9/23 14:52
 */
@Data
public class CarrierOrderGoodsForAutoModel {
    @ApiModelProperty("货物ID")
    private Long goodsId;
    @ApiModelProperty("提货数量")
    private BigDecimal loadAmount;
    @ApiModelProperty("卸货数量")
    private BigDecimal unloadAmount;
}
