package com.logistics.tms.controller.shippingorder;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.shippingorder.ShippingOrderBiz;
import com.logistics.tms.controller.shippingorder.request.*;
import com.logistics.tms.controller.shippingorder.response.GetShippingOrderDetailResponseModel;
import com.logistics.tms.controller.shippingorder.response.GetShippingOrderRoutePlanResponseModel;
import com.logistics.tms.controller.shippingorder.response.SearchShippingOrderListResponseModel;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 零担运输单
 * @author: wjf
 * @date: 2024/8/6 10:01
 */
@Slf4j
@Api(tags = "零担运输单")
@RestController
@RequestMapping(value = "/service/shippingOrder")
public class ShippingOrderController {

    @Resource
    private ShippingOrderBiz shippingOrderBiz;

    /**
     * 列表
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/searchList")
    public Result<PageInfo<SearchShippingOrderListResponseModel>> searchList(@RequestBody SearchShippingOrderListRequestModel requestModel){
        return Result.success(shippingOrderBiz.searchList(requestModel,false));
    }

    /**
     * 列表导出
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/export")
    @IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchShippingOrderListResponseModel>> export(@RequestBody SearchShippingOrderListRequestModel requestModel){
        return Result.success(shippingOrderBiz.searchList(requestModel, true).getList());
    }

    /**
     * 查询详情
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/getDetail")
    public Result<GetShippingOrderDetailResponseModel> getDetail(@RequestBody GetShippingOrderDetailRequestModel requestModel){
        return Result.success(shippingOrderBiz.getDetail(requestModel));
    }

    /**
     * 审核
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/audit")
    public Result<Boolean> audit(@RequestBody ShippingOrderAuditRequestModel requestModel){
        shippingOrderBiz.audit(requestModel);
        return Result.success(true);
    }

    /**
     * 重新报价
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/reQuote")
    public Result<Boolean> reQuote(@RequestBody ShippingOrderReQuoteRequestModel requestModel){
        shippingOrderBiz.reQuote(requestModel);
        return Result.success(true);
    }

    /**
     * 查看路径规划
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/getRoutePlan")
    public Result<List<GetShippingOrderRoutePlanResponseModel>> getRoutePlan(@RequestBody GetShippingOrderDetailRequestModel requestModel){
        return Result.success(shippingOrderBiz.getRoutePlan(requestModel));
    }





}
