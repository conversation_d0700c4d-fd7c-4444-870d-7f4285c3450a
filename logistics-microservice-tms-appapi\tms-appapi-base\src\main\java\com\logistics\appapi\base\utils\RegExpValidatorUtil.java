package com.logistics.appapi.base.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @createDate 2018-08-01 19:32
 */
public class RegExpValidatorUtil {

    private RegExpValidatorUtil() {
    }

    public static final boolean isMobile(String str) {
        String regex = "^\\d{11}$";
        return match(regex, str);
    }

    public static final boolean isMobileOrTel(String str) {
        String regex = "(^\\d{11}$)|(^(0[0-9]{2,3}-)?([2-9][0-9]{6,7})$)";
        return match(regex, str);
    }

    public static final boolean isVehicleNo(String str){
        String regex = "(^[冀豫云辽黑湘皖鲁苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼渝京津沪新军空海北沈兰济南广成使领]{1}[A-Z]{1}[A-Z0-9]{4,5}[A-Z0-9挂学警港澳]{1}$)";
        return match(regex,str);
    }

    public static final boolean isNumber(String str) {
        String regex = "^[0-9]*$";
        return match(regex, str);
    }

    public static final boolean isFloatNumberForTwo(String str) {
        String regex = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,2})$)";
        return match(regex, str);
    }

    public static final boolean isFloatNumberForThree(String str) {
        String regex = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,3})$)";
        return match(regex, str);
    }

    private static final boolean match(String regex, String str) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }

    /**
     * 发票代码校验
     *
     * @param str
     * @return
     */
    public static boolean invoiceCodeMatch(String str) {
        String regex = "^[A-Za-z0-9]{1,12}$";
        return match(regex, str);
    }
}
