package com.logistics.tms.controller.workgroup.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * @author: wjf
 * @date: 2023/12/22 15:44
 */
@Data
public class AddEditWorkGroupDistrictRequestModel {

    @ApiModelProperty("区域配置id,编辑时填写")
    private Long workGroupDistrictId;

    @ApiModelProperty(value = "省份id")
    private Long provinceId;

    @ApiModelProperty(value = "省份名")
    private String provinceName;

    @ApiModelProperty(value = "市id")
    private Long cityId;

    @ApiModelProperty(value = "市名")
    private String cityName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof AddEditWorkGroupDistrictRequestModel)) return false;
        AddEditWorkGroupDistrictRequestModel that = (AddEditWorkGroupDistrictRequestModel) o;
        return Objects.equals(getProvinceId(), that.getProvinceId()) && Objects.equals(getCityId(), that.getCityId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getProvinceId(), getCityId());
    }
}
