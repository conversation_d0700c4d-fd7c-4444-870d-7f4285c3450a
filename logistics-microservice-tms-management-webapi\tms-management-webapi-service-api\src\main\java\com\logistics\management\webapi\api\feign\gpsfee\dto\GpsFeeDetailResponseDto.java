package com.logistics.management.webapi.api.feign.gpsfee.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/10/9 9:01
 */
@Data
public class GpsFeeDetailResponseDto {
    private String gpsFeeId="";
    @ApiModelProperty("结算状态")
    private String status="";
    @ApiModelProperty("车牌号")
    private String vehicleId="";
    private String vehicleNo="";
    @ApiModelProperty("司机")
    private String staffId="";
    private String driverName="";
    private String driverPhone="";
    @ApiModelProperty("服务商")
    private String gpsServiceProvider="";
    @ApiModelProperty("终端型号")
    private String terminalType="";
    @ApiModelProperty("服务费")
    private String serviceFee="";
    @ApiModelProperty("合作周期（月）")
    private String cooperationPeriod="";
    @ApiModelProperty("SIM卡号")
    private String simNumber="";
    @ApiModelProperty("安装日期")
    private String installTime="";
    @ApiModelProperty("起始日期")
    private String startDate="";
    @ApiModelProperty("截止时间")
    private String endDate="";
    @ApiModelProperty("终止时间")
    private String finishDate="";
    @ApiModelProperty("备注")
    private String remark="";

    @ApiModelProperty(value = "是否生成结算数据，1生成，0未生成")
    private String ifSettlement="0";
}
