package com.logistics.appapi.controller.drivercostapply;

import com.logistics.appapi.controller.common.CommonBiz;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.constant.ConfigKeyConstant;
import com.logistics.appapi.base.enums.*;
import com.logistics.appapi.base.utils.ApiParamsValidatorUtil;
import com.logistics.appapi.base.utils.RegExpValidatorUtil;
import com.logistics.appapi.client.drivercostapply.DriverCostApplyClient;
import com.logistics.appapi.client.drivercostapply.request.AddDriverCostApplyRequestModel;
import com.logistics.appapi.client.drivercostapply.request.DriverCostApplyDetailRequestModel;
import com.logistics.appapi.client.drivercostapply.request.SearchCostApplyListForAppletRequestModel;
import com.logistics.appapi.client.drivercostapply.request.UndoDriverCostApplyRequestModel;
import com.logistics.appapi.client.drivercostapply.response.DriverCostApplyDetailResponseModel;
import com.logistics.appapi.client.drivercostapply.response.DriverCostApplyInvoiceResponseModel;
import com.logistics.appapi.client.drivercostapply.response.DriverCostApplyTicketModel;
import com.logistics.appapi.client.drivercostapply.response.SearchCostApplyListCountForAppletResponseModel;
import com.logistics.appapi.client.thirdparty.basicdata.BasicServiceClient;
import com.logistics.appapi.client.thirdparty.basicdata.baidu.request.CheckSensitiveWordRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.baidu.response.CheckSensitiveWordResponseModel;
import com.logistics.appapi.client.thirdparty.basicdata.ocr.response.OcrMultipleInvoiceRes;
import com.logistics.appapi.client.thirdparty.basicdata.ocr.response.OcrMultipleInvoiceResponseModel;
import com.logistics.appapi.controller.drivercostapply.mapping.DriverCostApplyDetailMapping;
import com.logistics.appapi.controller.drivercostapply.mapping.SearchCostApplyListMapping;
import com.logistics.appapi.controller.drivercostapply.request.*;
import com.logistics.appapi.controller.drivercostapply.response.BillIdentifyResponseDto;
import com.logistics.appapi.controller.drivercostapply.response.DriverCostApplyDetailResponseDto;
import com.logistics.appapi.controller.drivercostapply.response.SearchCostApplyListCountResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2022/8/2 15:56
 */
@Api(value = "费用申请", tags = "费用申请")
@RestController
@RequestMapping(value = "/api/driverCostApply")
public class DriverCostApplyController {

    @Resource
    private DriverCostApplyClient driverCostApplyClient;
    @Resource
    private BasicServiceClient basicServiceClient;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private ConfigKeyConstant configKeyConstant;

    /**
     * 提交（重新提交）费用申请
     *
     * @param requestDto 请求参数
     */
    @ApiOperation(value = "提交（重新提交）费用申请", tags = "3.11.0")
    @PostMapping(value = "/addDriverCostApply")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addDriverCostApply(@RequestBody @Valid AddDriverCostApplyRequestDto requestDto) {
        //入参校验
        checkCostApplyInvoice(requestDto);

        //敏感词校验
        CheckSensitiveWordRequestModel checkSensitiveWordRequestModel = new CheckSensitiveWordRequestModel();
        checkSensitiveWordRequestModel.setContent(requestDto.getApplyRemark());
        CheckSensitiveWordResponseModel sensitiveWordResponseModelResultData = basicServiceClient.checkSensitiveWord(checkSensitiveWordRequestModel);
        if (sensitiveWordResponseModelResultData != null && CommonConstant.INTEGER_ONE.equals(sensitiveWordResponseModelResultData.getIfSensitive())) {
            throw new BizException(AppApiExceptionEnum.NOT_ALLOW_SUBMIT_SENSITIVE_WORD);
        }

        //请求业务服务
        AddDriverCostApplyRequestModel requestModel = MapperUtils.mapperNoDefault(requestDto, AddDriverCostApplyRequestModel.class);
        return driverCostApplyClient.addDriverCostApply(requestModel);
    }

    private void checkCostApplyInvoice(AddDriverCostApplyRequestDto requestDto) {
        //费用申请类型校验
        if (DriverCostApplyTypeEnum.getEnum(ConverterUtils.toInteger(requestDto.getCostType())).equals(DriverCostApplyTypeEnum.DEFAULT)) {
            throw new BizException(AppApiExceptionEnum.COST_TYPE_ERROR);
        }

        // 现场图片、支付图片 必须存在
        if (ListUtils.isEmpty(requestDto.getTicketList()) ||
                requestDto.getTicketList().size() != CommonConstant.INTEGER_TWO) {
            throw new BizException(AppApiExceptionEnum.MISSING_REQUIRED_PIC);
        }

        // 校验枚举type是否正确
        Collection<String> collect =
                requestDto.getTicketList().stream()
                        .map(AddDriverCostApplyRequestDto.DriverCostApplyTickDetail::getType)
                        .collect(Collectors.toSet());
        if (Arrays.stream(PicTypeEnum.values()).map(PicTypeEnum::getKey).anyMatch(e -> !collect.contains(e))) {
            throw new BizException(AppApiExceptionEnum.MISSING_REQUIRED_PIC);
        }

        //如果有发票信息
        if (ListUtils.isNotEmpty(requestDto.getInvoiceInfoList())) {

            Set<String> invoiceInfoSet = new HashSet<>();
            //发票信息校验
            List<DriverCostApplyInvoiceRequestDto> invoiceInfoList = requestDto.getInvoiceInfoList();
            for (DriverCostApplyInvoiceRequestDto invoiceInfo : invoiceInfoList) {
                invoiceInfoSet.add(invoiceInfo.getType() + "-" + invoiceInfo.getInvoiceCode() + "-" + invoiceInfo.getInvoiceNum());
                // 校验金额是否符合范围
                if (!ApiParamsValidatorUtil.verifyFloatAmount(invoiceInfo.getTotalPrice()) ||
                        !ApiParamsValidatorUtil.verifyFloatAmount(invoiceInfo.getTotalTax())) {
                    throw new BizException(AppApiExceptionEnum.ERROR_FEE_LIMIT);
                }
                //发票图片必须存在
                if (StringUtils.isBlank(invoiceInfo.getImagePath())) {
                    throw new BizException(AppApiExceptionEnum.MISSING_INVOICE_PIC_CONTAINS);
                }
                // 校验发票信息是否完整
                //增值税发票
                if (InvoiceTypeEnum.VALUE_ADDED_TAX.getKey().equals(invoiceInfo.getType())) {
                    if (StringUtils.isHasEmpty(
                            invoiceInfo.getInvoiceCode(),
                            invoiceInfo.getInvoiceNum(),
                            invoiceInfo.getTotalPrice(),
                            invoiceInfo.getTotalTax(),
                            invoiceInfo.getTotalTaxAndPrice())) {
                        throw new BizException(AppApiExceptionEnum.INVOICE_INFO_DELETION);
                    }
                }
                //出租车发票
                else if (InvoiceTypeEnum.TAXI.getKey().equals(invoiceInfo.getType())) {
                    if (StringUtils.isHasEmpty(
                            invoiceInfo.getInvoiceCode(),
                            invoiceInfo.getInvoiceNum(),
                            invoiceInfo.getTotalPrice())) {
                        throw new BizException(AppApiExceptionEnum.INVOICE_INFO_DELETION);
                    }
                    invoiceInfo.setInvoiceName(CommonConstant.TAXI);
                    invoiceInfo.setInvoiceType(CommonConstant.TRAFFIC);
                }
                //火车票
                else if (InvoiceTypeEnum.TRAIN.getKey().equals(invoiceInfo.getType())) {
                    if (StringUtils.isHasEmpty(
                            invoiceInfo.getInvoiceNum(),
                            invoiceInfo.getTotalPrice())) {
                        throw new BizException(AppApiExceptionEnum.INVOICE_INFO_DELETION);
                    }
                    invoiceInfo.setInvoiceName(CommonConstant.TRAIN_TICKETS);
                }
                //定额发票
                else if (InvoiceTypeEnum.QUOTA.getKey().equals(invoiceInfo.getType())) {
                    if (StringUtils.isHasEmpty(
                            invoiceInfo.getInvoiceCode(),
                            invoiceInfo.getInvoiceNum(),
                            invoiceInfo.getTotalPrice())) {
                        throw new BizException(AppApiExceptionEnum.INVOICE_INFO_DELETION);
                    }
                }
                //卷票
                else if (InvoiceTypeEnum.ROLL_TICKET.getKey().equals(invoiceInfo.getType())) {
                    if (StringUtils.isHasEmpty(
                            invoiceInfo.getInvoiceCode(),
                            invoiceInfo.getInvoiceNum(),
                            invoiceInfo.getTotalPrice())) {
                        throw new BizException(AppApiExceptionEnum.INVOICE_INFO_DELETION);
                    }
                }
                //机打发票
                else if (InvoiceTypeEnum.MACHINE_PRINTING.getKey().equals(invoiceInfo.getType())) {
                    if (StringUtils.isHasEmpty(
                            invoiceInfo.getInvoiceCode(),
                            invoiceInfo.getInvoiceNum(),
                            invoiceInfo.getTotalPrice(),
                            invoiceInfo.getTotalTax(),
                            invoiceInfo.getTotalTaxAndPrice())) {
                        throw new BizException(AppApiExceptionEnum.INVOICE_INFO_DELETION);
                    }
                }
                //过路过桥费发票
                else if (InvoiceTypeEnum.PASSING_BY.getKey().equals(invoiceInfo.getType())) {
                    if (StringUtils.isHasEmpty(
                            invoiceInfo.getInvoiceCode(),
                            invoiceInfo.getInvoiceNum(),
                            invoiceInfo.getTotalPrice())) {
                        throw new BizException(AppApiExceptionEnum.INVOICE_INFO_DELETION);
                    }
                    invoiceInfo.setInvoiceName(CommonConstant.PASSING_BY);
                }

                //发票代码校验
                if (StringUtils.isNotBlank(invoiceInfo.getInvoiceCode())
                        && !RegExpValidatorUtil.invoiceCodeMatch(invoiceInfo.getInvoiceCode())) {
                    throw new BizException(AppApiExceptionEnum.INVOICE_CODE_PARAMS_ERROR);
                }

                //计算价税合计
                invoiceInfo.setTotalTaxAndPrice(new BigDecimal(invoiceInfo.getTotalPrice()).add(new BigDecimal(invoiceInfo.getTotalTax())).toPlainString());
            }

            //发票是否有重复, 类型+发票代码+发票号码唯一
            if (invoiceInfoSet.size() != invoiceInfoList.size()) {
                throw new BizException(AppApiExceptionEnum.INVOICE_REPETITION);
            }
        }
    }

    /**
     * 申请记录列表
     * @param requestDto  请求参数
     */
    @ApiOperation(value = "申请记录列表",tags = "1.1.7")
    @PostMapping(value = "/searchCostApplyList")
    public Result<SearchCostApplyListCountResponseDto> searchCostApplyList(@RequestBody @Valid SearchCostApplyListRequestDto requestDto) {
        Result<SearchCostApplyListCountForAppletResponseModel> result = driverCostApplyClient.searchCostApplyListForApplet(MapperUtils.mapper(requestDto, SearchCostApplyListForAppletRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SearchCostApplyListCountResponseDto.class, new SearchCostApplyListMapping()));
    }

    /**
     * 申请记录详情
     * @param requestDto   请求参数
     */
    @ApiOperation(value = "申请记录详情", tags = "1.1.8")
    @PostMapping(value = "/driverCostApplyDetail")
    public Result<DriverCostApplyDetailResponseDto> driverCostApplyDetail(@RequestBody @Valid DriverCostApplyDetailRequestDto requestDto) {
        DriverCostApplyDetailRequestModel requestModel = MapperUtils.mapper(requestDto, DriverCostApplyDetailRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_THREE);
        Result<DriverCostApplyDetailResponseModel> result = driverCostApplyClient.driverCostApplyDetail(requestModel);
        result.throwException();
        List<String> ticketList = result.getData().getTicketList().stream().map(DriverCostApplyTicketModel::getFilePath).collect(Collectors.toList());
        if (ListUtils.isNotEmpty(result.getData().getInvoiceInfoList())) {
            List<String> invoiceInfoList = result.getData().getInvoiceInfoList().stream().map(DriverCostApplyInvoiceResponseModel::getImagePath).collect(Collectors.toList());
            ticketList.addAll(invoiceInfoList);
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(ticketList);
        return Result.success(MapperUtils.mapper(result.getData(), DriverCostApplyDetailResponseDto.class, new DriverCostApplyDetailMapping(configKeyConstant,imageMap)));
    }

    /**
     * 撤销费用申请
     * @param requestDto  请求参数
     */
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @ApiOperation(value = "撤销费用申请 v1.0.2")
    @PostMapping(value = "/undoDriverCostApply")
    public Result<Boolean> undoDriverCostApply(@RequestBody @Valid UndoDriverCostApplyRequestDto requestDto) {
        UndoDriverCostApplyRequestModel requestModel = MapperUtils.mapper(requestDto, UndoDriverCostApplyRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_THREE);
        Result<Boolean> result = driverCostApplyClient.undoDriverCostApply(requestModel);
        result.throwException();
        return Result.success(true);
    }

    /**
     * 票据识别
     * @param file 需要识别的发票
     */
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @ApiOperation(value = "票据识别",tags = "1.1.7")
    @PostMapping(value = "/billIdentify")
    public Result<BillIdentifyResponseDto> billIdentify(@RequestParam("file") MultipartFile file, @RequestParam("type") String type) {
        if (StringUtils.isBlank(type)) {
            throw new BizException(AppApiExceptionEnum.OCR_TYPE_CANNOT_BLANK);
        }
        OcrMultipleInvoiceResponseModel ocrMultipleInvoiceResponseModel = basicServiceClient.multipleInvoice(file, type);
        if (ocrMultipleInvoiceResponseModel != null) {
            BillIdentifyResponseDto responseDto = new BillIdentifyResponseDto();
            responseDto.setSrc(ocrMultipleInvoiceResponseModel.getSrc());
            responseDto.setRelativePath(ocrMultipleInvoiceResponseModel.getRelativePath());
            OcrMultipleInvoiceRes ocrMultipleInvoiceRes = ocrMultipleInvoiceResponseModel.getOcrInvoiceRes();
            if (Objects.nonNull(ocrMultipleInvoiceRes)) {
                if (OcrInvoiceTypeEnum.VALUE_ADDED_TAX.getKey().equals(ocrMultipleInvoiceRes.getType())) {
                    responseDto.setType(OcrInvoiceTypeEnum.VALUE_ADDED_TAX.getValue().getKey());
                    responseDto.setInvoiceName(ocrMultipleInvoiceRes.getInvoiceTypeOrg());
                    responseDto.setInvoiceType(ocrMultipleInvoiceRes.getInvoiceType());
                    responseDto.setInvoiceCode(ocrMultipleInvoiceRes.getInvoiceCode());
                    responseDto.setInvoiceNum(ocrMultipleInvoiceRes.getInvoiceNum());
                    responseDto.setTotalPrice(ocrMultipleInvoiceRes.getTotalAmount());
                    responseDto.setTotalTax(ocrMultipleInvoiceRes.getTotalTax());
                    responseDto.setTotalTaxAndPrice(ocrMultipleInvoiceRes.getAmountInFiguers());
                } else if(OcrInvoiceTypeEnum.ROLL_TICKET.getKey().equals(ocrMultipleInvoiceRes.getType())){
                    responseDto.setType(OcrInvoiceTypeEnum.ROLL_TICKET.getValue().getKey());
                    responseDto.setInvoiceName(ocrMultipleInvoiceRes.getInvoiceTypeOrg());
                    responseDto.setInvoiceType(ocrMultipleInvoiceRes.getInvoiceType());
                    responseDto.setInvoiceCode(ocrMultipleInvoiceRes.getInvoiceCode());
                    responseDto.setInvoiceNum(ocrMultipleInvoiceRes.getInvoiceNum());
                    responseDto.setTotalPrice(CommonConstant.DOUBLE_ZERO);
                    responseDto.setTotalTax(CommonConstant.DOUBLE_ZERO);
                    responseDto.setTotalTaxAndPrice(CommonConstant.DOUBLE_ZERO);
                }else if (OcrInvoiceTypeEnum.TAXI.getKey().equals(ocrMultipleInvoiceRes.getType())) {
                    responseDto.setType(OcrInvoiceTypeEnum.TAXI.getValue().getKey());
                    responseDto.setInvoiceType(ocrMultipleInvoiceRes.getInvoiceType());
                    responseDto.setInvoiceCode(ocrMultipleInvoiceRes.getInvoiceCode());
                    responseDto.setInvoiceNum(ocrMultipleInvoiceRes.getInvoiceNum());
                    responseDto.setTotalPrice(ocrMultipleInvoiceRes.getTotalFare().replace("￥", "").replace("元", ""));
                    try {
                        responseDto.setTotalTaxAndPrice(new BigDecimal(responseDto.getTotalPrice()).add(new BigDecimal(responseDto.getTotalTax())).toPlainString());
                    }catch (Exception e){
                        responseDto.setTotalTaxAndPrice("");
                    }
                } else if (OcrInvoiceTypeEnum.TRAIN.getKey().equals(ocrMultipleInvoiceRes.getType())) {
                    responseDto.setType(OcrInvoiceTypeEnum.TRAIN.getValue().getKey());
                    responseDto.setInvoiceType(ocrMultipleInvoiceRes.getInvoiceType());
                    responseDto.setInvoiceNum(ocrMultipleInvoiceRes.getTicket_num());
                    responseDto.setTotalPrice(ocrMultipleInvoiceRes.getTicket_rates().replace("￥", "").replace("元", ""));
                    try {
                        responseDto.setTotalTaxAndPrice(new BigDecimal(responseDto.getTotalPrice()).add(new BigDecimal(responseDto.getTotalTax())).toPlainString());
                    }catch (Exception e){
                        responseDto.setTotalTaxAndPrice("");
                    }
                } else if (OcrInvoiceTypeEnum.QUOTA.getKey().equals(ocrMultipleInvoiceRes.getType())) {
                    responseDto.setType(OcrInvoiceTypeEnum.QUOTA.getValue().getKey());
                    responseDto.setInvoiceName(ocrMultipleInvoiceRes.getInvoiceTypeOrg());
                    responseDto.setInvoiceType(ocrMultipleInvoiceRes.getInvoiceType());
                    responseDto.setInvoiceCode(ocrMultipleInvoiceRes.getInvoice_code());
                    responseDto.setInvoiceNum(ocrMultipleInvoiceRes.getInvoice_number());
                    responseDto.setTotalPrice(ocrMultipleInvoiceRes.getInvoice_rate_lowercase().replace("￥", "").replace("元", ""));
                    try {
                        responseDto.setTotalTaxAndPrice(new BigDecimal(responseDto.getTotalPrice()).add(new BigDecimal(responseDto.getTotalTax())).toPlainString());
                    }catch (Exception e){
                        responseDto.setTotalTaxAndPrice("");
                    }
                } else if (OcrInvoiceTypeEnum.MACHINE_PRINTING.getKey().equals(ocrMultipleInvoiceRes.getType())) {
                    responseDto.setType(OcrInvoiceTypeEnum.MACHINE_PRINTING.getValue().getKey());
                    responseDto.setInvoiceName(ocrMultipleInvoiceRes.getInvoiceType());
                    responseDto.setInvoiceType(ocrMultipleInvoiceRes.getInvoiceType());
                    responseDto.setInvoiceCode(ocrMultipleInvoiceRes.getInvoiceCode());
                    responseDto.setInvoiceNum(ocrMultipleInvoiceRes.getInvoiceNum());
                    responseDto.setTotalPrice(ocrMultipleInvoiceRes.getAmountInFiguers().replace("￥", "").replace("元", ""));
                    responseDto.setTotalTax(CommonConstant.DOUBLE_ZERO);
                    try {
                        responseDto.setTotalTaxAndPrice(new BigDecimal(responseDto.getTotalPrice()).add(new BigDecimal(responseDto.getTotalTax())).toPlainString());
                    }catch (Exception e){
                        responseDto.setTotalTaxAndPrice("");
                    }
                } else if (OcrInvoiceTypeEnum.PASSING_BY.getKey().equals(ocrMultipleInvoiceRes.getType())) {
                    responseDto.setType(OcrInvoiceTypeEnum.PASSING_BY.getValue().getKey());
                    responseDto.setInvoiceType(ocrMultipleInvoiceRes.getInvoiceType());
                    responseDto.setInvoiceCode(ocrMultipleInvoiceRes.getInvoiceCode());
                    responseDto.setInvoiceNum(ocrMultipleInvoiceRes.getInvoiceNum());
                    responseDto.setTotalPrice(ocrMultipleInvoiceRes.getFare().replace("￥", "").replace("元", ""));
                    try {
                        responseDto.setTotalTaxAndPrice(new BigDecimal(responseDto.getTotalPrice()).add(new BigDecimal(responseDto.getTotalTax())).toPlainString());
                    }catch (Exception e){
                        responseDto.setTotalTaxAndPrice("");
                    }
                }
            }
            return Result.success(responseDto);
        } else {
            throw new BizException(AppApiExceptionEnum.ERROR_OCR_INVOICE);
        }

    }

}
