<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleTypeMapper" >
  <select id="fuzzyVehicleType"
          resultType="com.logistics.tms.controller.vehicletype.response.GetVehicleTypeSearchByNameResponseModel">
    select
    tqvt.vehicle_category as  vehicleCategory,
    tqvt.vehicle_type as vehicleType,
    tqvt.id as vehicleTypeId
    from t_vehicle_type tqvt
    where tqvt.valid=1 and tqvt.enabled=1
    <if test="vehicleType!=null and vehicleType!=''">
       and (instr(tqvt.vehicle_type,#{vehicleType,jdbcType=VARCHAR}))
    </if>
  </select>

  <select id="searchVehicleTypeList" resultType="com.logistics.tms.controller.vehicletype.response.VehicleTypeListResponseModel">
      select
      tqvt.id as vehicleTypeId,
      tqvt.vehicle_type as vehicleType,
      tqvt.vehicle_category as  vehicleCategory,
      tqvt.add_user_name as addUserName,
      tqvt.enabled as enabled,
      tqvt.last_modified_by as lastModifiedBy,
      tqvt.last_modified_time as lastModifiedTime
      from  t_vehicle_type tqvt
      where tqvt.valid=1
      <if test="params.vehicleType!=null and params.vehicleType!=''">
        and (instr(tqvt.vehicle_type,#{params.vehicleType,jdbcType = VARCHAR}))
      </if>
      <if test="params.enabled!=null">
        and enabled = #{params.enabled,jdbcType = INTEGER}
      </if>
      order by tqvt.last_modified_time desc ,tqvt.id desc
  </select>


  <select id="searchVehicleTypeIdList" resultType="java.lang.Long">
    select DISTINCT
    tqvt.id as vehicleTypeId
    from t_vehicle_type tqvt
    where tqvt.valid=1
    <if test="params.vehicleType!=null and params.vehicleType!=''">
      and (instr(tqvt.vehicle_type,#{params.vehicleType,jdbcType = VARCHAR}))
    </if>
    <if test="params.enabled!=null">
      and enabled = #{params.enabled,jdbcType = INTEGER}
    </if>
    order by tqvt.last_modified_time desc,tqvt.id desc
  </select>

  <select id="searchVehicleType" resultType="com.logistics.tms.controller.vehicletype.response.VehicleTypeListResponseModel">
      select
      tqvt.id as vehicleTypeId,
      tqvt.vehicle_type as vehicleType,
      tqvt.vehicle_category as  vehicleCategory,
      tqvt.remark,
      tqvt.add_user_name as addUserName,
      tqvt.enabled as enabled,
      tqvt.last_modified_by as lastModifiedBy,
      tqvt.last_modified_time as lastModifiedTime
      from  t_vehicle_type tqvt
      where tqvt.valid=1
      and tqvt.id in (${ids})
      order by tqvt.last_modified_time desc,tqvt.id desc
  </select>

  <select id="selectListByType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_vehicle_type  tqvt
    where tqvt.valid = 1
    and tqvt.vehicle_type = #{vehicleType,jdbcType = VARCHAR}
  </select>

  <select id="selectListByTypeAndCategory" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_vehicle_type  tqvt
    where tqvt.valid = 1
    and tqvt.vehicle_type = #{vehicleType,jdbcType = VARCHAR}
    and tqvt.vehicle_category = #{vehicleCategory,jdbcType =INTEGER}
  </select>

  <select id="findListByType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_vehicle_type tqvt
    where tqvt.valid = 1
    <if test="vehicleType!=null and vehicleType!=''">
      and tqvt.vehicle_type = #{vehicleType,jdbcType = VARCHAR}
    </if>
  </select>

  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TVehicleType">
    <foreach collection="list" item="item" index="index" separator=";">
      insert into t_vehicle_type
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          id,
        </if>
        <if test="item.vehicleType != null">
          vehicle_type,
        </if>
        <if test="item.vehicleCategory !=null">
          vehicle_category,
        </if>
        <if test="item.remark != null">
          remark,
        </if>
        <if test="item.addUserId != null">
          add_user_id,
        </if>
        <if test="item.addUserName != null">
          add_user_name,
        </if>
        <if test="item.source != null">
          source,
        </if>
        <if test="item.enabled != null">
          enabled,
        </if>
        <if test="item.createdBy != null">
          created_by,
        </if>
        <if test="item.createdTime != null">
          created_time,
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time,
        </if>
        <if test="item.valid != null">
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.vehicleType != null">
          #{item.vehicleType,jdbcType=VARCHAR},
        </if>
        <if test="item.vehicleCategory !=null">
          #{item.vehicleCategory,jdbcType = INTEGER},
        </if>
        <if test="item.remark != null">
          #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.addUserId != null">
          #{item.addUserId,jdbcType=BIGINT},
        </if>
        <if test="item.addUserName != null">
          #{item.addUserName,jdbcType=VARCHAR},
        </if>
        <if test="item.source != null">
          #{item.source,jdbcType=INTEGER},
        </if>
        <if test="item.enabled != null">
          #{item.enabled,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null">
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

  <update id="batchUpdate" >
    <foreach collection="list" separator=";" item="item">
      update t_vehicle_type
      <set>
        <if test="item.vehicleType != null">
          vehicle_type = #{item.vehicleType,jdbcType=VARCHAR},
        </if>
        <if test="item.vehicleCategory !=null">
          vehicle_category = #{item.vehicleCategory,jdbcType = VARCHAR},
        </if>
        <if test="item.remark != null">
          remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.addUserId != null">
          add_user_id = #{item.addUserId,jdbcType=BIGINT},
        </if>
        <if test="item.addUserName != null">
          add_user_name = #{item.addUserName,jdbcType=VARCHAR},
        </if>
        <if test="item.source != null">
          source = #{item.source,jdbcType=INTEGER},
        </if>
        <if test="item.enabled != null">
          enabled = #{item.enabled,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null">
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="selectByVehicleTypes" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_vehicle_type tqvt
    where tqvt.valid = 1
      and tqvt.vehicle_category in (${vehicleTypes})
  </select>
</mapper>