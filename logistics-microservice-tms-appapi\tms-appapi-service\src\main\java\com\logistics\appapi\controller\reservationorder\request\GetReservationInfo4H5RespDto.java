package com.logistics.appapi.controller.reservationorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class GetReservationInfo4H5RespDto {

    /**
     * 预约单id
     */
    @ApiModelProperty("预约单id")
    private String reservationOrderId;


    @ApiModelProperty("运单id")
    private String carrierOrderId = "";


    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";


    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨；100 新生回收，101 新生销售")
    private String orderEntrustType = "";

    @ApiModelProperty("状态 0.可预约 1待签到 2已签到 3已失效4.异常 5.已完成")
    private String state = "";

    @ApiModelProperty("状态文本  0.可预约  1待签到 2已签到 3已失效4.异常 5.已完成")
    private String stateLabel = "";

    @ApiModelProperty("司机名称")
    private String driverName = "";

    @ApiModelProperty("车辆")
    private String vehicleNumber = "";

    @ApiModelProperty("提货省名称")
    private String loadProvinceName = "";

    @ApiModelProperty("提货城市名字")
    private String loadCityName = "";

    @ApiModelProperty("提货县区名字")
    private String loadAreaName = "";

    @ApiModelProperty("卸货省名称")
    private String unLoadProvinceName = "";

    @ApiModelProperty("卸货城市名字")
    private String unLoadCityName = "";

    @ApiModelProperty("卸货县区名字")
    private String unLoadAreaName = "";


    @ApiModelProperty("提货仓库")
    private String loadWarehouse =  "";


    @ApiModelProperty("卸货仓库")
    private String unLoadWarehouse =  "";

    @ApiModelProperty("0 否 1：是")
    private String ifNeedVerifyDriver =  "";


}
