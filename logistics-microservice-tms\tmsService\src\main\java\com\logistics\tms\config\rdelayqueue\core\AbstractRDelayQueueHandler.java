package com.logistics.tms.config.rdelayqueue.core;


import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 其他处理类继承该类即可
 * exc到期
 *
 * <AUTHOR>
 * @date 2024/04/25
 */
@Slf4j
public abstract class AbstractRDelayQueueHandler implements RDelayQueueHandler {

    /**
     *处理业务类型
     */
    protected String type;


    public AbstractRDelayQueueHandler() {
        setType();
        Objects.requireNonNull(type,this.getClass().getName()+"延迟队列处理器，业务类型未赋值");
    }

    /**
     * 设置处理类型
     */
    public abstract void setType();

    @Override
    public boolean support(String type) {
        return this.type.equals(type);
    }



    /**
     * 序列化对象
     *
     * @param object
     * @return {@link String}
     */
    protected String getMsg(Object object) {
        try {
            return JSONUtil.toJsonStr(object);
        } catch (Exception e) {
            log.error("延迟队列序列化对象异常", e);
            throw new RuntimeException(e.getMessage());
        }
    }


    /**
     * 反列化对象
     *
     * @param str
     * @param tClass
     * @return {@link T}
     */
    protected <T> T parseStr2Object(String str,Class<T> tClass) {
        return JSONUtil.toBean(str,tClass);
    }


}
