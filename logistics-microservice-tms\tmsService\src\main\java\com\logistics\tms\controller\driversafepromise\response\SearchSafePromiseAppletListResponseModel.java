package com.logistics.tms.controller.driversafepromise.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/11/18 15:05
 */
@Data
public class SearchSafePromiseAppletListResponseModel {
    @ApiModelProperty("安全承诺书关联司机ID")
    private Long relationId;
    @ApiModelProperty("所属年份")
    private String period;
    @ApiModelProperty("签订状态: 0待签订、1已签订")
    private Integer status;
    @ApiModelProperty("发布时间")
    private Date publishTime;
    @ApiModelProperty("经办人")
    private String agent;
}
