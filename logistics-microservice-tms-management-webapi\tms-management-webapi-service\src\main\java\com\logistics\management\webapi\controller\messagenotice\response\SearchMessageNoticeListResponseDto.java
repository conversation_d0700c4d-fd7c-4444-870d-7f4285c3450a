package com.logistics.management.webapi.controller.messagenotice.response;

import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/6/4 13:22
 */
@Data
public class SearchMessageNoticeListResponseDto {

    /**
     * 消息通知id
     */
    private String messageNoticeId="";

    /**
     * 状态：0 未读， 1 已读
     */
    private String messageStatus="";

    /**
     * 状态
     */
    private String messageStatusLabel="";

    /**
     * 消息内容
     */
    private String messageBody="";

    /**
     * 推送时间
     */
    private String messagePushTime="";

    /**
     * 消息id（用于置为已读）
     */
    private String messageId="";

    /**
     * 消息类型（用于跳转）：5000 竞价单详情
     */
    private String messageType="";
    /**
     * 业务表id（用于跳转）
     */
    private String objectId="";
}
