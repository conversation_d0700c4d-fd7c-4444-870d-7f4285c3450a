package com.logistics.tms.api.feign.companycarrierauthorization.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.companycarrierauthorization.CompanyCarrierAuthorizationServiceApi;
import com.logistics.tms.api.feign.companycarrierauthorization.model.request.*;
import com.logistics.tms.api.feign.companycarrierauthorization.model.response.CarrierAuthorizationDetailResponseModel;
import com.logistics.tms.api.feign.companycarrierauthorization.model.response.CarrierAuthorizationListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

@Component
public class CompanyCarrierAuthorizationServiceApiHystrix implements CompanyCarrierAuthorizationServiceApi {

    @Override
    public Result<PageInfo<CarrierAuthorizationListResponseModel>> carrierAuthorizationList(CarrierAuthorizationListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierAuthorizationDetailResponseModel> carrierAuthorizationDetail(CarrierAuthorizationDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> carrierAuthorizationAdd(CarrierAuthorizationAddRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> carrierAuthorizationAudit(CarrierAuthorizationAuditRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> carrierAuthorizationArchived(CarrierAuthorizationArchivedRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> carrierAuthorizationReArchive(CarrierAuthorizationReArchivedRequestModel requestModel) {
        return Result.timeout();
    }
}
