package com.logistics.tms.api.feign.dateremind.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.dateremind.DateRemindServiceApi;
import com.logistics.tms.api.feign.dateremind.model.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/5/31 8:47
 */
@Component("tmsDateRemindServiceApiHystrix")
public class DateRemindServiceApiHystrix implements DateRemindServiceApi {
    @Override
    public Result<PageInfo<DateRemindListResponseModel>> searchDateRemindList(DateRemindListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<DateRemindDetailResponseModel> dateRemindDetail(DateRemindDetailRequestModel responseModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> saveOrModifyDateRemind(SaveOrModifyDateRemindRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> unifiedDateRemind(UnifiedDateRemindRequestModel remindRequestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<DateRemindListResponseModel>> export(DateRemindListRequestModel requestModel) {
        return Result.timeout();
    }
}
