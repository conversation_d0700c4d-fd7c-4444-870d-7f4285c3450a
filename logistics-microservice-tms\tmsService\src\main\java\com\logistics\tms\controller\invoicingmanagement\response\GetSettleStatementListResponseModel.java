package com.logistics.tms.controller.invoicingmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/19 17:43
 */
@Data
public class GetSettleStatementListResponseModel {
    @ApiModelProperty("发票关联对账单id")
    private Long invoicingSettleStatementId;

    @ApiModelProperty("对账月份")
    private String settleStatementMonth;

    @ApiModelProperty("对账单id")
    private Long settleStatementId;
    @ApiModelProperty("对账单号")
    private String settleStatementCode;

    @ApiModelProperty("运费费点")
    private BigDecimal freightTaxPoint;

    @ApiModelProperty("临时费用费点")
    private BigDecimal otherFeeTaxPoint;

    @ApiModelProperty("差异调整费用")
    private BigDecimal adjustFee;

    @ApiModelProperty("对账费用")
    private BigDecimal reconciliationFee;

    @ApiModelProperty("对账单名称")
    private String settleStatementName;

    @ApiModelProperty("合同号")
    private String contractCode;

    @ApiModelProperty("对账单运单明细")
    private List<GetSettleStatementListItemResponseModel> itemList;
}
