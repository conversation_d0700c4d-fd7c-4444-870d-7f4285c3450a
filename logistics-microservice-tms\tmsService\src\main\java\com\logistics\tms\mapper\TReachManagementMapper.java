package com.logistics.tms.mapper;

import com.logistics.tms.api.feign.terminalreachmanagement.model.GetReachManagementDetailRequestModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.GetReachManagementDetailResponseModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.SearchReachManagementListRequestModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.SearchReachManagementListResponseModel;
import com.logistics.tms.entity.TReachManagement;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Mybatis Generator on 2023/03/15
 */
@Mapper
public interface TReachManagementMapper extends BaseMapper<TReachManagement> {

    TReachManagement selectByPrimaryKeyDecrypt(Long id);

    int insertSelectiveEncrypt(TReachManagement tReachManagement);

    int updateByPrimaryKeySelectiveEncrypt(TReachManagement tReachManagement);

    List<SearchReachManagementListResponseModel> searchReachManagementList(@Param("requestModel") SearchReachManagementListRequestModel requestModel);

    GetReachManagementDetailResponseModel getReachManagementDetail(@Param("requestModel") GetReachManagementDetailRequestModel requestModel);

    TReachManagement selectByCarrierOrderId(@Param("carrierOrderId") Long carrierOrderId);
}