package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/7/11 15:58
 */
public class ExportExcelVehiclePayeeRel {
    private ExportExcelVehiclePayeeRel() {

    }

    private static final Map<String, String> EXPORT_VEHICLE_PAYEE_REL;

    static {
        EXPORT_VEHICLE_PAYEE_REL = new LinkedHashMap<>();
        EXPORT_VEHICLE_PAYEE_REL.put("车牌号", "vehicleNo");
        EXPORT_VEHICLE_PAYEE_REL.put("收款人姓名", "name");
        EXPORT_VEHICLE_PAYEE_REL.put("身份证号", "identityNo");
        EXPORT_VEHICLE_PAYEE_REL.put("银行卡号", "bankCardNo");
        EXPORT_VEHICLE_PAYEE_REL.put("银行名称", "bankName");
        EXPORT_VEHICLE_PAYEE_REL.put("备注", "remark");
    }

    public static Map<String, String> getExportVehiclePayeeRel() {
        return EXPORT_VEHICLE_PAYEE_REL;
    }
}
