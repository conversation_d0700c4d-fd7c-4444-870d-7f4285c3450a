package com.logistics.management.webapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2021/9/17 15:14
 */
@Data
public class UpdateCarrierOrderUnloadAddressConfirmRequestModel {

    @ApiModelProperty("运单ID")
    private List<Long> carrierOrderIds;

    @ApiModelProperty("修改原因")
    private Integer unloadAddressUpdateType;

    @ApiModelProperty("云盘仓库外部id")
    private Long warehouseId;

    @ApiModelProperty("收货省份id")
    private Long unloadProvinceId;

    @ApiModelProperty("收货省份名字")
    private String unloadProvinceName;

    @ApiModelProperty("收货城市id")
    private Long unloadCityId;

    @ApiModelProperty("收货城市名字")
    private String unloadCityName;

    @ApiModelProperty("收货县区id")
    private Long unloadAreaId;

    @ApiModelProperty("收货县区名字")
    private String unloadAreaName;

    @ApiModelProperty("收货详细地址")
    private String unloadDetailAddress;

    @ApiModelProperty("收货仓库")
    private String unloadWarehouse;

    @ApiModelProperty("收货人姓名")
    private String receiverName;

    @ApiModelProperty("收货人手机号")
    private String receiverMobile;
}
