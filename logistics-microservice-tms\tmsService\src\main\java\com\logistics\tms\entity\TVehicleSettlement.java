package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleSettlement extends BaseEntity {
    /**
    * 结算状态：0 待对账，1 待发送，2 待确认，3 待处理，4 待结清，5 部分结清，6 已结清
    */
    @ApiModelProperty("结算状态：0 待对账，1 待发送，2 待确认，3 待处理，4 待结清，5 部分结清，6 已结清")
    private Integer status;

    /**
    * 车辆id
    */
    @ApiModelProperty("车辆id")
    private Long vehicleId;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 车辆机构：1 自主，2 外部，3 自营
    */
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;

    /**
    * 月份（yyyy-MM）
    */
    @ApiModelProperty("月份（yyyy-MM）")
    private String settlementMonth;

    /**
    * 月实际应付运费
    */
    @ApiModelProperty("月实际应付运费")
    private BigDecimal actualExpensesPayable;

    /**
    * 月运单数量
    */
    @ApiModelProperty("月运单数量")
    private Integer carrierOrderCount;

    /**
    * 月运单费用
    */
    @ApiModelProperty("月运单费用")
    private BigDecimal carrierFreight;

    /**
    * 月应扣轮胎费
    */
    @ApiModelProperty("月应扣轮胎费")
    private BigDecimal tireFee;

    /**
    * 月应扣gps费
    */
    @ApiModelProperty("月应扣gps费")
    private BigDecimal gpsFee;

    /**
    * 月应扣停车费
    */
    @ApiModelProperty("月应扣停车费")
    private BigDecimal parkingFee;

    /**
    * 月应扣贷款费
    */
    @ApiModelProperty("月应扣贷款费")
    private BigDecimal loanFee;

    /**
    * 月应扣充油费
    */
    @ApiModelProperty("月应扣充油费")
    private BigDecimal oilFilledFee;

    /**
    * 车辆月理赔费用
    */
    @ApiModelProperty("车辆月理赔费用")
    private BigDecimal vehicleClaimFee;

    /**
    * 月应扣保险费
    */
    @ApiModelProperty("月应扣保险费")
    private BigDecimal insuranceFee;

    /**
    * 月扣减费用合计
    */
    @ApiModelProperty("月扣减费用合计")
    private BigDecimal deductingFee;

    /**
    * 剩余未扣减费用
    */
    @ApiModelProperty("剩余未扣减费用")
    private BigDecimal remainingFee;

    /**
    * 月应扣个人意外险费
    */
    @ApiModelProperty("月应扣个人意外险费")
    private BigDecimal accidentInsuranceFee;

    /**
    * 个人意外险费用合计
    */
    @ApiModelProperty("个人意外险费用合计")
    private BigDecimal accidentInsuranceExpenseTotal;

    /**
    * 个人意外险费月理赔费用
    */
    @ApiModelProperty("个人意外险费月理赔费用")
    private BigDecimal accidentInsuranceClaimFee;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 油费退款费用
    */
    @ApiModelProperty("油费退款费用")
    private BigDecimal oilRefundFee;

    /**
    * 保险退款费用
    */
    @ApiModelProperty("保险退款费用")
    private BigDecimal insuranceRefundFee;

    /**
    * 是否调整费用：0 否，1 是
    */
    @ApiModelProperty("是否调整费用：0 否，1 是")
    private Integer ifAdjustFee;

    /**
    * 调整费用
    */
    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;

    /**
    * 调整原因
    */
    @ApiModelProperty("调整原因")
    private String adjustRemark;

    /**
    * 撤回原因
    */
    @ApiModelProperty("撤回原因")
    private String withdrawRemark;
}