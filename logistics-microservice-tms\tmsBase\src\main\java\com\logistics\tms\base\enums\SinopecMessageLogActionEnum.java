package com.logistics.tms.base.enums;


public enum SinopecMessageLogActionEnum {
    DEFAULT(-1,""),
    ADD(1,"新增"),
    CANCLE(2,"撤销需求单"),
    ;

    private Integer key;
    private String value;

    SinopecMessageLogActionEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
    public static SinopecMessageLogActionEnum getEnum(Integer key) {
        for (SinopecMessageLogActionEnum t : values()) {
            if (t.getKey() .equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
