package com.logistics.tms.controller.freightconfig;

import com.logistics.tms.biz.carrierfreight.CarrierFreightConfigBiz;
import com.logistics.tms.controller.freightconfig.request.CarrierFreightConfigAddRequestModel;
import com.logistics.tms.controller.freightconfig.request.CarrierFreightConfigEditRequestModel;
import com.logistics.tms.controller.freightconfig.request.CarrierFreightConfigListRequestModel;
import com.logistics.tms.controller.freightconfig.request.CarrierFreightConfigRequestModel;
import com.logistics.tms.controller.freightconfig.response.CarrierFreightConfigDetailResponseModel;
import com.logistics.tms.controller.freightconfig.response.CarrierFreightConfigListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "运价配置管理")
@RequestMapping(value = "/service/freight/config")
public class CarrierFreightConfigController {

    @Resource
    private CarrierFreightConfigBiz carrierFreightConfigBiz;
    @PostMapping(value = "/searchList")
    @ApiOperation(value = "车主运价配置管理列表", tags = "1.3.5")
    Result<List<CarrierFreightConfigListResponseModel>> searchList(@RequestBody CarrierFreightConfigListRequestModel requestModel) {
        List<CarrierFreightConfigListResponseModel> carrierFreightConfigListResponseModels =
                carrierFreightConfigBiz.searchList(requestModel);
        return Result.success(carrierFreightConfigListResponseModels);
    }

    @PostMapping(value = "/detail")
    @ApiOperation(value = "车主运价配置查看", tags = "1.3.5")
    Result<CarrierFreightConfigDetailResponseModel> detail(@RequestBody CarrierFreightConfigRequestModel requestModel) {
        CarrierFreightConfigDetailResponseModel detail = carrierFreightConfigBiz.detail(requestModel);
        return Result.success(detail);
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "新增车主运价配置", tags = "1.3.5")
    Result<Boolean> add(@RequestBody CarrierFreightConfigAddRequestModel requestModel) {
        carrierFreightConfigBiz.add(requestModel);
        return Result.success(true);
    }

    @PostMapping(value = "/edit")
    @ApiOperation(value = "编辑车主运价配置", tags = "1.3.5")
    Result<Boolean> edit(@RequestBody CarrierFreightConfigEditRequestModel requestModel) {
        return Result.success(carrierFreightConfigBiz.edit(requestModel));
    }

    @PostMapping(value = "/delete")
    @ApiOperation(value = "删除车主运价配置", tags = "1.3.5")
    Result<Boolean> delete(@RequestBody CarrierFreightConfigRequestModel requestModel) {
        return Result.success( carrierFreightConfigBiz.delete(requestModel));
    }
}
