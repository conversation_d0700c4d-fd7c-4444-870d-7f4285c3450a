package com.logistics.appapi.controller.baiscinfo;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.AppApiExceptionEnum;
import com.logistics.appapi.client.baiscinfo.DriverBasicInfoClient;
import com.logistics.appapi.client.baiscinfo.request.*;
import com.logistics.appapi.client.baiscinfo.response.*;
import com.logistics.appapi.controller.baiscinfo.mapping.DriverBasicInfoMapping;
import com.logistics.appapi.controller.baiscinfo.request.*;
import com.logistics.appapi.controller.baiscinfo.response.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @author: wjf
 * @date: 2024/3/11 9:14
 */
@Api(value = "司机基础信息管理", tags = "司机基础信息管理")
@RestController
@RequestMapping(value = "/api/driverApplet/basicInfo")
public class DriverBasicInfoController {

    @Resource
    private DriverBasicInfoClient driverBasicInfoClient;

    /**
     * 查询司机基本信息
     * @return
     */
    @ApiOperation(value = "查询司机基础信息", tags = "1.0.9")
    @PostMapping(value = "/driverBasicInfo")
    public Result<DriverBasicInfoResponseDto> driverBasicInfo() {
        Result<DriverBasicInfoResponseModel> responseModelResult = driverBasicInfoClient.driverBasicInfo();
        responseModelResult.throwException();

        DriverBasicInfoResponseDto dto = MapperUtils.mapper(responseModelResult.getData(),
                DriverBasicInfoResponseDto.class,
                new DriverBasicInfoMapping());
        return Result.success(dto);
    }

    /**
     * 个人基础信息提交
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "个人基础信息提交", tags = "1.0.9")
    @PostMapping(value = "/driverBasicInfoSubmit")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> driverBasicInfoSubmit(@RequestBody @Valid DriverBasicInfoSubmitRequestDto requestDto) {
        if (CommonConstant.ONE.equals(requestDto.getAuthModel())) {
            //手机号认证
            if (StringUtils.isBlank(requestDto.getVerificationCode())) {
                throw new BizException(AppApiExceptionEnum.REAL_NAME_PARAMS_ERROR);
            }
        } else if (CommonConstant.TWO.equals(requestDto.getAuthModel())) {
            //人脸识别
            if (StringUtils.isBlank(requestDto.getOrderNo())) {
                throw new BizException(AppApiExceptionEnum.REAL_NAME_PARAMS_ERROR);
            }
        }
        Result<Boolean> result = driverBasicInfoClient.driverBasicInfoSubmit(MapperUtils.mapper(requestDto, DriverBasicInfoSubmitRequestModel.class));
        result.throwException();
        return result;
    }

    /**
     * 个人手机号认证-获取验证码
     *
     * @return
     */
    @ApiOperation(value = "个人手机号认证-获取验证码", tags = "1.0.9")
    @PostMapping(value = "/getPersonAuthVerifyCode")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> getVerifyCode(@RequestBody @Valid GetPersonAuthVerifyCodeRequestDto requestDto) {
        Result<Boolean> result = driverBasicInfoClient.getVerifyCode(MapperUtils.mapper(requestDto, GetPersonAuthVerifyCodeRequestModel.class));
        result.throwException();
        return result;
    }

    /**
     * 个人二要素校验
     *
     * @return
     */
    @ApiOperation(value = "个人二要素校验", tags = "1.0.9")
    @PostMapping(value = "/verifyPersonTwoElements")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<VerifyPersonTwoElementsResponseDto> verifyPersonTwoElements(@RequestBody @Valid VerifyPersonTwoElementsRequestDto requestDto) {
        Result<VerifyPersonTwoElementsResponseModel> result = driverBasicInfoClient.verifyPersonTwoElements(MapperUtils.mapper(requestDto, VerifyPersonTwoElementsRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), VerifyPersonTwoElementsResponseDto.class));
    }

    /**
     * 个人三要素校验
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "个人三要素校验", tags = "1.0.9")
    @PostMapping(value = "/verifyPersonThreeElements")
    public Result<VerifyPersonThreeElementsResponseDto> verifyPersonThreeElements(@RequestBody @Valid VerifyPersonThreeElementsRequestDto requestDto) {
        Result<VerifyPersonThreeElementsResponseModel> result = driverBasicInfoClient.verifyPersonThreeElements(MapperUtils.mapper(requestDto, VerifyPersonThreeElementsRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), VerifyPersonThreeElementsResponseDto.class));
    }

    /**
     * 获取刷脸认证签名
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "获取刷脸认证签名", tags = "1.0.9")
    @PostMapping(value = "/faceRecognition")
    public Result<FaceRecognitionResponseDto> faceRecognition(@RequestBody @Valid FaceRecognitionRequestDto requestDto) {
        Result<FaceRecognitionResponseModel> result = driverBasicInfoClient.faceRecognition(MapperUtils.mapper(requestDto, FaceRecognitionRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), FaceRecognitionResponseDto.class));
    }

    /**
     * 获取刷脸认证结果
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "获取刷脸认证结果", tags = "1.0.9")
    @PostMapping(value = "/faceRecognitionResult")
    public Result<FaceRecognitionResultResponseDto> faceRecognitionResult(@RequestBody @Valid FaceRecognitionResultRequestDto requestDto) {
        Result<FaceRecognitionResultResponseModel> result = driverBasicInfoClient.faceRecognitionResult(MapperUtils.mapper(requestDto, FaceRecognitionResultRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), FaceRecognitionResultResponseDto.class));
    }
}
