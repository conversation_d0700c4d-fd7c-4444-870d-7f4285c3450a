package com.logistics.appapi.controller.vehiclesafecheck.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/11/18 13:23
 */
@Data
public class SafeCheckSummaryResponseDto {
    @ApiModelProperty("未检查")
    private String notCheckCount = "";
    @ApiModelProperty("待确认")
    private String waitConfirmCount = "";
    @ApiModelProperty("待整改")
    private String waitReformCount = "";
    @ApiModelProperty("已整改")
    private String hasReformCount = "";
    @ApiModelProperty("检查完成")
    private String hasCheckCount = "";
}
