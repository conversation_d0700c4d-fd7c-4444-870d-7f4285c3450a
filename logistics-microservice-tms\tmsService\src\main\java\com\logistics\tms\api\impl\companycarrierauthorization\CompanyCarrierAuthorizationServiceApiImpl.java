package com.logistics.tms.api.impl.companycarrierauthorization;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.companycarrierauthorization.CompanyCarrierAuthorizationServiceApi;
import com.logistics.tms.api.feign.companycarrierauthorization.model.request.*;
import com.logistics.tms.api.feign.companycarrierauthorization.model.response.CarrierAuthorizationDetailResponseModel;
import com.logistics.tms.api.feign.companycarrierauthorization.model.response.CarrierAuthorizationListResponseModel;
import com.logistics.tms.biz.companyauthorization.CompanyAuthorizationBiz;
import com.yelo.tray.core.base.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class CompanyCarrierAuthorizationServiceApiImpl implements CompanyCarrierAuthorizationServiceApi {

    private final CompanyAuthorizationBiz companyAuthorizationBiz;

    /**
     * 查询车主授权列表
     * @param requestModel
     * @return PageInfo<CarrierAuthorizationListResponseModel>
     */
    @Override
    public Result<PageInfo<CarrierAuthorizationListResponseModel>> carrierAuthorizationList(CarrierAuthorizationListRequestModel requestModel) {
        return Result.success(companyAuthorizationBiz.carrierAuthorizationList(requestModel));
    }

    /**
     * 车主授权详情
     * @param requestModel
     * @return CarrierAuthorizationDetailResponseModel
     */
    @Override
    public Result<CarrierAuthorizationDetailResponseModel> carrierAuthorizationDetail(CarrierAuthorizationDetailRequestModel requestModel) {
        return Result.success(companyAuthorizationBiz.carrierAuthorizationDetail(requestModel));
    }

    /**
     * 新增车主授权信息
     * @param requestModel
     * @return Boolean
     */
    @Override
    public Result<Boolean> carrierAuthorizationAdd(CarrierAuthorizationAddRequestModel requestModel) {
        return Result.success(companyAuthorizationBiz.carrierAuthorizationAdd(requestModel));
    }

    /**
     * 审核车主授权信息
     * @param requestModel
     * @return Boolean
     */
    @Override
    public Result<Boolean> carrierAuthorizationAudit(CarrierAuthorizationAuditRequestModel requestModel) {
        return Result.success(companyAuthorizationBiz.carrierAuthorizationAudit(requestModel));
    }

    /**
     * 车主授权信息归档
     * @param requestModel
     * @return Boolean
     */
    @Override
    public Result<Boolean> carrierAuthorizationArchived(CarrierAuthorizationArchivedRequestModel requestModel) {
        return Result.success(companyAuthorizationBiz.carrierAuthorizationArchived(requestModel));
    }

    /**
     * 车主授权信息归档
     * @param requestModel
     * @return Boolean
     */
    @Override
    public Result<Boolean> carrierAuthorizationReArchive(CarrierAuthorizationReArchivedRequestModel requestModel) {
        return Result.success(companyAuthorizationBiz.carrierAuthorizationReArchive(requestModel));
    }
}
