package com.logistics.appapi.controller.drivercostapply.mapping;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.base.enums.DriverCostApplyTypeEnum;
import com.logistics.appapi.base.enums.DriverCostAuditEnum;
import com.logistics.appapi.client.drivercostapply.response.SearchCostApplyListCountForAppletResponseModel;
import com.logistics.appapi.client.drivercostapply.response.SearchCostApplyListResponseModel;
import com.logistics.appapi.controller.drivercostapply.response.SearchCostApplyListCountResponseDto;
import com.logistics.appapi.controller.drivercostapply.response.SearchCostApplyListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/7
 */
public class SearchCostApplyListMapping extends MapperMapping<SearchCostApplyListCountForAppletResponseModel, SearchCostApplyListCountResponseDto> {

	@Override
	public void configure() {
		SearchCostApplyListCountForAppletResponseModel source = getSource();
		SearchCostApplyListCountResponseDto destination = getDestination();

		if (source != null) {
			PageInfo pageList = source.getPageList();
			List<SearchCostApplyListResponseModel> driverCostApplyList = pageList.getList();

			//处理分页数据
			List<SearchCostApplyListResponseDto> costApplyDtoList = new ArrayList<>();
			for (SearchCostApplyListResponseModel costApplyModel : driverCostApplyList) {
				SearchCostApplyListResponseDto costApplyDto = MapperUtils.mapper(costApplyModel, SearchCostApplyListResponseDto.class);
				costApplyDto.setStaffName(costApplyModel.getStaffName() + "_" + costApplyModel.getStaffMobile());
				//审核状态
				costApplyDto.setAuditStatusLabel(DriverCostAuditEnum.getEnum(costApplyModel.getAuditStatus()).getValue());
				//费用类型
				costApplyDto.setCostTypeLabel(DriverCostApplyTypeEnum.getEnum(costApplyModel.getCostType()).getValue());
				costApplyDtoList.add(costApplyDto);
			}
			pageList.setList(costApplyDtoList);
			destination.setPageList(pageList);
		}
	}
}
