package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.management.webapi.client.carrierorder.response.EditCarrierOrderCostDetailResponseModel;
import com.logistics.management.webapi.controller.carrierorder.response.EditCarrierOrderCostDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/2
 */
public class EditCarrierOrderCostDetailMapping extends MapperMapping<EditCarrierOrderCostDetailResponseModel, EditCarrierOrderCostDetailResponseDto> {
	@Override
	public void configure() {
		EditCarrierOrderCostDetailResponseModel source = getSource();
		EditCarrierOrderCostDetailResponseDto destination = getDestination();

		if (source != null) {
			//单位
			destination.setGoodsUnitLabel(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());

			//价格类型
			destination.setPriceType(source.getPriceType() == null || CommonConstant.INTEGER_ZERO.equals(source.getPriceType()) ? CommonConstant.BLANK_TEXT : ConverterUtils.toString(source.getPriceType()));

			//数量去零
			destination.setGoodsAmount(source.getGoodsAmount().stripTrailingZeros().toPlainString());
		}
	}
}
