package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 托盘同步需求单到 TMS
 * liang current user system login name
 * 2018/10/10 current system date
 */
@Data
public class SyncTrayDemandOrderMessage {

    @ApiModelProperty("编号")
    private String demandOrderCode;

    @ApiModelProperty("客户编号")
    private String customerOrderCode;

    @ApiModelProperty("下单人名字")
    private String publishName;

    @ApiModelProperty("下单人联系方式")
    private String logisticsDemandSponsorMobilePhone;

    @ApiModelProperty("下单时间")
    private Date publishTime;

    @ApiModelProperty("委托件数")
    private BigDecimal goodsAmount;

    @ApiModelProperty("单位：1 件，2 吨，3 件（托盘），4 块")
    private Integer goodsUnit;

    @ApiModelProperty("类型")
    private Integer entrustType;

    @ApiModelProperty("备注")
    private String remark;

    /**
     * 基础地址code
     */
    @ApiModelProperty("基础地址code")
    private String loadYeloAddressCode;
    @ApiModelProperty("发货仓库code")
    private String loadWarehouseCode;
    private Long loadProvinceId;
    private String loadProvinceName;
    private Long loadCityId;
    private String loadCityName;
    private Long loadAreaId;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    private String consignorName;
    private String consignorMobile;
    private Date expectedLoadTime;

    @ApiModelProperty(value = "收货客户名称")
    private String receiveCustomerCompanyName;
    private Long unloadProvinceId;
    private String unloadProvinceName;
    private Long unloadCityId;
    private String unloadCityName;
    private Long unloadAreaId;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;
    private String receiverName;
    private String receiverMobile;
    private Date expectedUnloadTime;
    private String upstreamCustomer;

    @ApiModelProperty("是否加急：0 否，1 是")
    private Integer ifUrgent;

    @ApiModelProperty("周末可上门: 0.空 1.是 2.否")
    private Byte availableOnWeekends;

    @ApiModelProperty("装卸方: 0.空 1.我司装卸 2.客户装卸")
    private Byte loadingUnloadingPart;

    @ApiModelProperty("装卸费用")
    private BigDecimal loadingUnloadingCharge;

    @ApiModelProperty("回收任务类型：1 日常回收，2 加急或节假日回收")
    private Integer recycleTaskType;

    @ApiModelProperty("地推任务单号")
    private String groundPushTaskCode;

    @ApiModelProperty("项目标签（多个标签,拼接）：1 石化板块，2 轮胎板块，3 涂料板块，4 其他")
    private String projectLabel;

    @ApiModelProperty("托盘品名")
    private List<SyncTrayDemandOrderGoodsModel> goodsModels;

    @ApiModelProperty("销售单的关联表")
    private List<SyncTrayDemandOrderOrdersModel> ordersModels;

    @ApiModelProperty("中间件LBScode")
    private String LBScode;

    @ApiModelProperty("固定需求唯一code")
    private String fixedDemand;

    @ApiModelProperty("客户id")
    private Long trayCustomerCompanyId;
    @ApiModelProperty("地址id")
    private Long trayCustomerAddressId;
    @ApiModelProperty("大类id")
    private Long productCategoryId;


    /************************************补单相关的参数***************************/
    @ApiModelProperty("是否是补充需求单的创建 0否 1是")
    private int ifExtDemandOrder;

    @ApiModelProperty("原始的需求单号")
    private String orgDemandOrderCode;
    @ApiModelProperty("原始的需求单号")
    private Long orgDemandOrderId;

    @ApiModelProperty("原始的运单号")
    private String  orgCarrierOrderCode;

    @ApiModelProperty("原始的运单id")
    private Long orgCarrierOrderId;

    @ApiModelProperty("原始的需求单部门编号")
    private String publishOrgCode;
    @ApiModelProperty("原始的需求单部门名称")
    private String publishOrgName;

    /**
     * 定位-经度
     */
    private String longitude;
    /**
     * 定位-纬度
     */
    private String latitude;


    /************************************补单相关的参数***************************/
}
