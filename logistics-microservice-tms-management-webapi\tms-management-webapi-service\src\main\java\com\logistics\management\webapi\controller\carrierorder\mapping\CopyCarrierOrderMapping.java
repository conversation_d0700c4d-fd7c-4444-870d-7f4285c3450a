package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.client.carrierorder.response.CopyCarrierOrderResponseModel;
import com.logistics.management.webapi.controller.carrierorder.response.CopyCarrierOrderResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/10/11 15:17
 */
public class CopyCarrierOrderMapping extends MapperMapping<CopyCarrierOrderResponseModel, CopyCarrierOrderResponseDto> {

    @Override
    public void configure() {

        CopyCarrierOrderResponseDto destination = getDestination();
        CopyCarrierOrderResponseModel source = getSource();
        destination.setLoadAddress(source.getLoadCityName() + source.getLoadAreaName() + source.getLoadDetailAddress());
        destination.setUnloadAddress(source.getUnloadCityName() + source.getUnloadAreaName() + source.getUnloadDetailAddress());
        if (source.getExpectLoadTime() != null) {
            destination.setExpectLoadTime(DateUtils.dateToString(source.getExpectLoadTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        destination.setExpectAmount(source.getExpectAmount().stripTrailingZeros().toPlainString());
        if (ListUtils.isNotEmpty(source.getGoodsNameList())) {
            destination.setGoodsName(StringUtils.listToString(source.getGoodsNameList(), '/'));
        }
    }
}
