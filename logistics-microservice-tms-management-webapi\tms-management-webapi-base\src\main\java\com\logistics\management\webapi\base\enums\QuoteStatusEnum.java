package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@AllArgsConstructor
@Getter
public enum QuoteStatusEnum {
    DEFAULT(-99999, ""),
    NO_QUOTE(-1, "未报价"),
    UNSELECTED(0, "未选择"),
    SELECTED(1, "已选择"),
    CANCELLED(2, "已取消"),
    ;

    private final Integer key;
    private final String value;

    public static QuoteStatusEnum getEnum(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
