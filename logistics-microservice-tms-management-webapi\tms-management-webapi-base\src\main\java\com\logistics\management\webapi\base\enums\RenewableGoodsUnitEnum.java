package com.logistics.management.webapi.base.enums;

public enum RenewableGoodsUnitEnum {

    PIECE(1,"件"),
    KILOGRAM(2,"kg");

    private Integer key;
    private String value;

    RenewableGoodsUnitEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
