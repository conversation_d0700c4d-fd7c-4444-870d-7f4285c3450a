package com.logistics.tms.api.impl.personalaccidentinsurance;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.personalaccidentinsurance.PersonalAccidentInsuranceServiceApi;
import com.logistics.tms.api.feign.personalaccidentinsurance.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.personalaccidentinsurance.PersonalAccidentInsuranceBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/5/30 14:09
 */
@RestController
public class PersonalAccidentInsuranceServiceApiImpl implements PersonalAccidentInsuranceServiceApi {

    @Autowired
    private PersonalAccidentInsuranceBiz personalAccidentInsuranceBiz;

    /**
     * 获取个人意外险列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<PersonalAccidentInsuranceListResponseModel>> searchPersonalAccidentInsuranceList(@RequestBody PersonalAccidentInsuranceListRequestModel requestModel) {
        return Result.success(personalAccidentInsuranceBiz.searchPersonalAccidentInsuranceList(requestModel.enablePaging()));
    }

    /**
     * 查询个人意外险详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<PersonalAccidentInsuranceDetailResponseModel> getPersonalAccidentInsuranceDetail(@RequestBody PersonalAccidentInsuranceIdRequestModel requestModel) {
        return Result.success(personalAccidentInsuranceBiz.getPersonalAccidentInsuranceDetail(requestModel));
    }

    /**
     * 新增/修改个人意外险
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> addOrModifyPersonalAccidentInsurance(@RequestBody AddOrModifyPersonalAccidentInsuranceRequestModel requestModel) {
        personalAccidentInsuranceBiz.addOrModifyPersonalAccidentInsurance(requestModel);
        return Result.success(true);
    }

    /**
     * 导出个人意外险
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<PersonalAccidentInsuranceListResponseModel>> exportPersonalAccidentInsurance(@RequestBody PersonalAccidentInsuranceListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        PageInfo<PersonalAccidentInsuranceListResponseModel> pageInfo = personalAccidentInsuranceBiz.searchPersonalAccidentInsuranceList(requestModel);
        return Result.success(pageInfo.getList());
    }

    /**
     * 根据保单号查询保单完整信息（新增个人意外险页面使用）
     * @param requestModel
     * @return
     */
    @Override
    public Result<List<GetInsuranceByPolicyNumberResponseModel>> getInsuranceByPolicyNumber(@RequestBody GetInsuranceByPolicyNumberRequestModel requestModel) {
        return Result.success(personalAccidentInsuranceBiz.getInsuranceByPolicyNumber(requestModel));
    }

    /**
     * 根据保单号模糊保单号
     * @param requestModel
     * @return
     */
    @Override
    public Result<List<SearchInsuranceByPolicyNumberResponseModel>> searchInsuranceByPolicyNumber(@RequestBody SearchInsuranceByPolicyNumberRequestModel requestModel) {
        return Result.success(personalAccidentInsuranceBiz.searchInsuranceByPolicyNumber(requestModel));
    }

    /**
     * 根据保单/批单号模糊查询保单号和金额（关联人数少于保单人数且保费大于，新增个人意外险页面使用0）
     * @param requestModel
     * @return
     */
    @Override
    public Result<List<GetPolicyNoPremiumByPolicyNoResponseModel>> getPolicyNoPremiumByPolicyNo(@RequestBody SearchInsuranceByPolicyNumberRequestModel requestModel) {
        return Result.success(personalAccidentInsuranceBiz.getPolicyNoPremiumByPolicyNo(requestModel));
    }

    /**
     * 导入个人意外险
     * @param requestModel
     * @return
     */
    @Override
    public Result<ImportPersonalAccidentInsuranceResponseModel> importPersonalAccidentInsurance(@RequestBody ImportPersonalAccidentInsuranceRequestModel requestModel) {
        return Result.success(personalAccidentInsuranceBiz.importPersonalAccidentInsurance(requestModel));
    }
}
