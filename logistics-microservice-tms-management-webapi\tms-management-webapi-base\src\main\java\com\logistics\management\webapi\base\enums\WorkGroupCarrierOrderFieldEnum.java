package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * tms服务有对应变量,需要同步修改 WorkGroupFieldTemplate
 */
@Getter
@AllArgsConstructor
public enum WorkGroupCarrierOrderFieldEnum {

    STATUS_DESC("statusDesc", "状态", "已签收"),
    CARRIER_ORDER_CODE("carrierOrderCode", "运单号", "YR2301010001-01"),
    CORRECT_STATUS_DESC("correctStatusDesc", "纠错状态", "无需纠错"),
    OUT_STATUS_LABEL("outStatusLabel", "出库状态", "已出库"),
    DEMAND_ORDER_CODE("demandOrderCode", "需求单号", "HR2301010001"),
    VEHICLE_NO("vehicleNo", "车牌", "浙A00000"),
    DRIVER("driver", "司机", "张三 ***********"),
    DRIVER_IDENTITY("driverIdentity", "司机身份证号", "330727198708172635"),
    RECYCLE_TASK_TYPE_LABEL("recycleTaskTypeLabel", "时效要求", "日常回收"),
    DISPATCH_FREIGHT_FEE("dispatchFreightFee", "司机运费合计(元)", "0.00"),
    LOAD_ADDRESS("loadAddress", "发货地址", "【仓库】 上海城区闵行区苏召路1628号"),
    CONSIGNOR("consignor", "发货人", "张三 ***********"),
    EXPECT_AMOUNT("expectAmount", "预计承运", "1件"),
    OTHER_FEE("otherFee", "临时费用（元）", "0.00"),
    ENTRUST_FREIGHT("entrustFreight", "实际货主费用（元）", "0.00"),
    CARRIER_PRICE_TOTAL("carrierPriceTotal", "车主运费金额(元)", "0.00"),
    ACTUAL_CARRIER_PRICE_TOTAL("actualCarrierPriceTotal", "实际车主费用(元)", "0.00"),
    LOAD_AMOUNT("loadAmount", "实际提货", "1件"),
    UNLOAD_AMOUNT("unloadAmount", "实际卸货", "1件"),
    SIGN_AMOUNT("signAmount", "实际签收", "1件"),
    BACK_AMOUNT("backAmount", "回退数", "0件"),
    DIFFERENCE_AMOUNT("differenceAmount", "差异数", "0件"),
    ABNORMAL_AMOUNT("abnormalAmount", "云仓异常数", "0件"),
    EXPECT_LOAD_TIME("expectLoadTime", "预计提货时间", "2023-12-01"),
    EXPECT_ARRIVAL_TIME("expectArrivalTime", "预计到货时间", "2023-12-01"),
    UNLOAD_ADDRESS("unloadAddress", "收货地址", "【仓库】 上海城区闵行区苏召路1628号"),
    RECEIVER("receiver", "收货人", "张三 ***********"),
    EXPECT_MILEAGE("expectMileage", "预计里程数KM", "124.55"),
    LOAD_TIME("loadTime", "实际提货时间", "2023-12-01 16:16"),
    UNLOAD_TIME("unloadTime", "实际到货时间", "2023-12-01 16:17"),
    SIGN_TIME("signTime", "实际签收时间", "2023-12-14 16:17:09"),
    GOODS_NAME("goodsName", "品名", "乐橘托盘"),
    REMARK("remark", "备注", "备注"),
    ENTRUST_TYPE("entrustType", "需求类型", "回收出库"),
    PUBLISH_ORG_NAME("publishOrgName", "下单部门", "公司-云途"),
    DISPATCH_USER_NAME("dispatchUserName", "调度人", "张三"),
    CARRIER_COMPANY("carrierCompany", "车主", "江苏乐橘云途科技有限公司"),
    ENTRUST_COMPANY("entrustCompany", "委托客户", "江苏乐橘云盘科技有限公司"),
    CUSTOMER_ORDER_CODE("customerOrderCode", "客户单号", "S-2311240001"),
    DISPATCH_TIME("dispatchTime", "运单生成时间", "2023-01-01 00:00:00"),
    DISPATCH_ORDER_CODE("dispatchOrderCode", "调度单号", "QD2301010001"),
    LOAD_REGION_NAME("loadRegionName", "大区", "上海一区"),
    LOAD_REGION_CONTACT_NAME("loadRegionContactName", "负责人", "张三 ***********"),
    LOAD_VALIDITY("loadValidity", "提货时效", "0"),
    CARRIER_ORDER_TICKETS_AMOUNT("carrierOrderTicketsAmount", "回单数", "0"),
    CONFIG_DISTANCE("configDistance", "配置距离KM", "100"),
    EXPECTED_LOAD_TIME("expectedLoadTime", "期望提货时间", "2024-01-03"),
    EXPECTED_UNLOAD_TIME("expectedUnloadTime", "期望到货时间", "2024-01-03"),
    ;

    private final String field;

    private final String fieldLabel;

    private final String fieldValue;
}
