package com.logistics.tms.api.feign.freight.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 运价管理
 * @Author: sj
 * @Date: 2019/12/24 13:03
 */
@Data
public class SearchFreightListRequestModel extends AbstractPageForm<SearchFreightListRequestModel>{
    @ApiModelProperty("角色类型：1 货主 2 车主")
    private Integer roleType;
    @ApiModelProperty("模糊匹配公司名称")
    private String companyName;
}
