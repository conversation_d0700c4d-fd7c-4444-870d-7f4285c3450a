package com.logistics.appapi.controller.reservationorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ReservationOrderConfirmSign4H5RequestDto {
    /**
     * 预约单id
     */
    @ApiModelProperty("预约单id")
    @NotBlank(message = "预约单id不能为空")
    private String reservationOrderId;

    /**
     * 定位-经度
     */
    @ApiModelProperty("定位-经度")
    @NotBlank(message = "请获取定位")
    private String longitude;
    /**
     * 定位-纬度
     */
    @ApiModelProperty("定位-纬度")
    @NotBlank(message = "请获取定位")
    private String latitude;


    /**
     * H5验证过的手机号
     */
    @ApiModelProperty("H5验证过的手机号")
//    @NotBlank(message = "H5验证过的手机号不能为空")
    private String mobilePhone;
}
