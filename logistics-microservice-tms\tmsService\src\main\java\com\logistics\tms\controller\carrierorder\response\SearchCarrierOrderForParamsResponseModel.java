package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/9/2 16:36
 */
@Data
public class SearchCarrierOrderForParamsResponseModel {
    @ApiModelProperty("运单id")
    private Long carrierOrderId;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("项目标签（多个标签,拼接）：1 石化板块，2 轮胎板块，3 涂料板块，4 其他")
    private String projectLabel;

    @ApiModelProperty("车主公司类型")
    private Integer companyCarrierType;
    @ApiModelProperty("车主公司id")
    private Long companyCarrierId;
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName;
    @ApiModelProperty("车主联系人")
    private String carrierContactName;
    @ApiModelProperty("车主联系人电话")
    private String carrierContactMobile;

    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("手机号")
    private String driverMobile;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("提货市")
    private String loadCityName;
    @ApiModelProperty("卸货市")
    private String unloadCityName;

    @ApiModelProperty("车主对账单状态: -2 未关联对账，-1 待提交，0 待业务审核，1 待财务审核，2 已对账，3 已驳回")
    private Integer carrierSettleStatementStatus;
}
