package com.logistics.management.webapi.client.routeconfig.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class RouteDistanceConfigResponseModel {

    @ApiModelProperty(value = "路线距离配置Id")
    private Long routeDistanceConfigId;

    @ApiModelProperty(value = "发货区; 省市区拼接")
    private String fromAreaName;

    @ApiModelProperty(value = "卸货区; 省市区拼接")
    private String toAreaName;

    @ApiModelProperty(value = "计费距离（KM）")
    private BigDecimal billingDistance;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "操作人")
    private String lastModifiedBy;

    @ApiModelProperty(value = "操作时间")
    private Date lastModifiedTime;
}
