package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2024/7/22 13:31
 */
@Data
public class CarrierOrderBeforeSignUpForYeloLifeResponseModel {

    @ApiModelProperty("运单ID")
    private Long carrierOrderId;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("货物单位：1 件，2 吨，3 件（方），4 块")
    private Integer goodsUnit;

    //车主信息
    @ApiModelProperty("车主id")
    private Long companyCarrierId;
    @ApiModelProperty("车主名")
    private String companyCarrierName;
    @ApiModelProperty("车主公司类型")
    private Integer companyCarrierType;
    @ApiModelProperty("车主联系人")
    private String carrierContactName;
    @ApiModelProperty("车主联系人电话")
    private String carrierContactMobile;

    //地址信息
    @ApiModelProperty("提货地")
    private String loadCityName;
    @ApiModelProperty("卸货地")
    private String unloadCityName;

    @ApiModelProperty("预提数量")
    private BigDecimal expectAmount;
    @ApiModelProperty("装货数量")
    private BigDecimal loadAmount;
    @ApiModelProperty("卸货数量")
    private BigDecimal unloadAmount;

    //司机费用
    @ApiModelProperty("司机运费价格类型 1 单价 2 一口价")
    private Integer dispatchFreightFeeType;
    @ApiModelProperty("司机运费")
    private BigDecimal dispatchFreightFee;
    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;
    @ApiModelProperty("多装多卸加价费用")
    private BigDecimal markupFee;

    @ApiModelProperty("车主价格：1 单价，2 一口价")
    private Integer carrierPriceType;
    @ApiModelProperty("车主价格")
    private BigDecimal carrierPrice;
    @ApiModelProperty("货主价格：1 单价，2 一口价")
    private Integer entrustFreightType;
    @ApiModelProperty("货主价格")
    private BigDecimal entrustFreight;


    @ApiModelProperty("货主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer settlementTonnage;
    @ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer carrierSettlement;
    @ApiModelProperty("委托类型：100 新生回收，101 新生销售")
    private Integer entrustType;
    @ApiModelProperty("委托单来源：1 乐医托盘委托单，2 委托方发单，3 中石化系统，4 扬巴邮件，5 乐橘新生")
    private Integer demandOrderSource;


    @ApiModelProperty("是否我司: 1:我司 2:其他车主")
    private Integer isOurCompany;

}
