package com.logistics.management.webapi.base.enums;

/**
 * 运单出库状态枚举
 */
public enum CarrierOrderOutStatusEnum {
    DEFAULT(-99, ""),
    WAIT_OUT(0, "待出库"),
    PART_OUT(1, "部分出库"),
    FINISH_OUT(2, "已出库"),
    ;

    private final Integer key;
    private final String value;

    CarrierOrderOutStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static CarrierOrderOutStatusEnum getEnum(Integer key) {
        for (CarrierOrderOutStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
