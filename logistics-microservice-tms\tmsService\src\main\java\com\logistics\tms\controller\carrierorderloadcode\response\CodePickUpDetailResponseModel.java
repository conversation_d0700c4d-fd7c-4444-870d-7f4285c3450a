package com.logistics.tms.controller.carrierorderloadcode.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CodePickUpDetailResponseModel {


    @ApiModelProperty(value = "运单Id")
    private Long carrierOrderId;

    @ApiModelProperty(value = "预计提货数量")
    private BigDecimal loadAmountExpect;

    @ApiModelProperty(value = "实际扫码数")
    private BigDecimal scanAmountActual;

    @ApiModelProperty(value = "编码有误数")
    private BigDecimal codeErrorCount;

    @ApiModelProperty(value = "实际提货数")
    private BigDecimal loadAmountActual;


}
