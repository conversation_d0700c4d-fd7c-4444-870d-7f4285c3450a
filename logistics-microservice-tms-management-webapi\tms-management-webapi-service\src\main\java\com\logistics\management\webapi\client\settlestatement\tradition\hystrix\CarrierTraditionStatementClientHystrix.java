package com.logistics.management.webapi.client.settlestatement.tradition.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.settlestatement.tradition.CarrierTraditionStatementClient;
import com.logistics.management.webapi.client.settlestatement.tradition.request.*;
import com.logistics.management.webapi.client.settlestatement.tradition.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CarrierTraditionStatementClientHystrix implements CarrierTraditionStatementClient {

    @Override
    public Result<PageInfo<TraditionWaitSettleStatementListResponseModel>> waitSettleStatementList(TraditionWaitSettleStatementListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<TraditionWaitSettleStatementListResponseModel>> exportWaitSettleStatementList(TraditionWaitSettleStatementListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> createSettleStatement(TraditionCreateSettleStatementRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<TraditionCarrierTaxPointResponseModel> queryTaxPoint(TraditionCarrierTaxPointRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<CarrierTraditionStatementListResponseModel>> settleStatementList(CarrierTraditionStatementListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<TraditionAssociationCarrierOrderResponseModel> associationCarrierOrder(TraditionAssociationCarrierOrderRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> modifySettleStatementMonth(ModifyTraditionStatementMonthRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> modifyPlatformCompany(TraditionModifyPlatformCompanyRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> modifyTaxPoint(TraditionCarrierModifyTaxPointRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> renameStatement(EditTraditionStatementNameRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<TraditionCarrierAdjustCostResponseModel> queryAdjustCost(TraditionCarrierQueryAdjustCostRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> adjustCost(TraditionCarrierAdjustRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> applyInvoicing(TraditionSettleStatementApplyInvoicingRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> cancel(TraditionStatementCancelRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<TraditionStatementArchiveListResponseModel>> statementArchiveList(TraditionStatementArchiveListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> statementArchive(TraditionStatementArchiveRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<String>> archiveTicketList(TraditionStatementArchiveTicketListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<TraditionStatementArchiveDetailResponseModel> statementArchiveDetail(TraditionStatementArchiveDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<TraditionStatementWaitArchiveListResponseModel>> statementWaitArchiveList(TraditionStatementWaitArchiveListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> submitSettleStatement(CarrierTraditionStatementIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> auditOrReject(ChangeTraditionStatementStatsRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierTraditionStatementDetailTotalResponseModel> settleStatementDetailTotal(CarrierTraditionStatementIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<CarrierTraditionStatementDetailListResponseModel>> settleStatementDetailList(CarrierTraditionStatementDetailListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<CarrierTraditionStatementDetailListResponseModel>> exportSettleStatementDetailList(CarrierTraditionStatementDetailListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<TraditionWaitSettleStatementListResponseModel>> addCarrierOrderList(TraditionAddCarrierOrderListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addCarrierOrderConfirm(TraditionAddCarrierOrderConfirmRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> cancelCarrierOrder(TraditionUndoCarrierOrderRequestModel requestModel) {
        return Result.timeout();
    }
}
