package com.logistics.management.webapi.controller.freightconfig.request.address;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class CarrierFreightAddressDeleteRequestDto {

    @ApiModelProperty(value = "路线计价配置ID,可批量", required = true)
    @NotEmpty(message = "路线计价配置ID不允许为空")
    private List<String> freightConfigAddressIds;
}
