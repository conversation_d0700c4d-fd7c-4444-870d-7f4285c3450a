package com.logistics.tms.controller.vehicleassetmanagement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/7/26 14:12
 */
@Data
public class VehicleAssertOutageRequestModel {

    @ApiModelProperty(value = "车主车辆关联Id")
    private Long carrierVehicleId;

    @ApiModelProperty("车辆停运原因")
    private String outageInfo;

    @ApiModelProperty(value = "停运原因类型：2 停运, 3 过户，4 报废")
    private Integer outageType;

    @ApiModelProperty("过户/报废证明文件列表")
    private List<String> pathList;
}
