package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/10/8 14:39
 */
public class ExportExcelGpsFeeRecords {
    private ExportExcelGpsFeeRecords() {
    }

    private static final Map<String, String> EXPORT_GPS_FEE_RECORDS;

    static {
        EXPORT_GPS_FEE_RECORDS = new LinkedHashMap<>();
        EXPORT_GPS_FEE_RECORDS.put("车牌号", "vehicleNo");
        EXPORT_GPS_FEE_RECORDS.put("服务费", "serviceFee");
        EXPORT_GPS_FEE_RECORDS.put("GPS服务商", "gpsServiceProvider");
        EXPORT_GPS_FEE_RECORDS.put("合作起始日期", "startDate");
        EXPORT_GPS_FEE_RECORDS.put("合作截至日期", "endDate");
        EXPORT_GPS_FEE_RECORDS.put("终止日期", "finishDate");
        EXPORT_GPS_FEE_RECORDS.put("备注", "remark");
        EXPORT_GPS_FEE_RECORDS.put("操作人", "lastModifiedBy");
        EXPORT_GPS_FEE_RECORDS.put("操作时间", "lastModifiedTime");
    }

    public static Map<String, String> getExportGpsFeeRecords() {
        return EXPORT_GPS_FEE_RECORDS;
    }
}
