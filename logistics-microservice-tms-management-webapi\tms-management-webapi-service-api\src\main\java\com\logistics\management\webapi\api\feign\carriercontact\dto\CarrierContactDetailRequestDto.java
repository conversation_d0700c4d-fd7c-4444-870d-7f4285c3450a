package com.logistics.management.webapi.api.feign.carriercontact.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * liang current user system login name
 * 2018/9/18 current system date
 */
@Data
public class CarrierContactDetailRequestDto {

    @ApiModelProperty(value = "车主账号id",required = true)
    @NotBlank(message = "车主账号id不能为空")
    private String carrierContactId;

}
