package com.logistics.management.webapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-07-29 13:24
 */
@Data
public class LoadDetailRequestModel {
    @ApiModelProperty(value = "运单ID")
    private List<Long> carrierOrderId;
    @ApiModelProperty("节点类型：1 已提货 2 已卸货")
    private Integer nodeType;
}
