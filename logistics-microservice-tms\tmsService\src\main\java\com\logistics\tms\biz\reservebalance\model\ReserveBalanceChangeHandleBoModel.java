package com.logistics.tms.biz.reservebalance.model;

import com.logistics.tms.base.enums.ReserveBalanceRunningTypeEnum;
import com.logistics.tms.controller.drivercostapply.request.DriverCostDeductionRequestModel;
import com.logistics.tms.entity.TReserveApply;
import com.yelo.tools.mapper.utils.MapperUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * 备用金余额变更
 */
@Data
@Accessors(chain = true)
public class ReserveBalanceChangeHandleBoModel {

    /**
     * 余额流水类型
     */
    private ReserveBalanceRunningTypeEnum typeEnum;

    /**
     * 司机Id
     */
    private Long driverId;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 备用金列表
     */
    private List<ReserveApplyBalanceChangeBoModel> reserveList;
}
