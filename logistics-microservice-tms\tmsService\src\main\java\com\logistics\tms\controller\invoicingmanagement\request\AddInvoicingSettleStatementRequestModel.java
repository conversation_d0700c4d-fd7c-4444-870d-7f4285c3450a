package com.logistics.tms.controller.invoicingmanagement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/20 9:19
 */
@Data
public class AddInvoicingSettleStatementRequestModel {
    @ApiModelProperty(value = "发票管理id")
    private Long invoicingId;

    @ApiModelProperty(value = "对账单id")
    private List<Long> settleStatementIdList;

    @ApiModelProperty("业务类型：1 包装业务，2 自营业务")
    private Integer businessType;
}
