package com.logistics.appapi.client.thirdparty.basicdata.ocr.hystrix;

import com.logistics.appapi.client.thirdparty.basicdata.ocr.OCRServiceApi;
import com.logistics.appapi.client.thirdparty.basicdata.ocr.response.OcrMultipleInvoiceResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

@Component
public class OCRServiceApiHystrix implements OCRServiceApi {

    @Override
    public Result<OcrMultipleInvoiceResponseModel> multipleInvoice(MultipartFile file, String type) {
        return Result.timeout();
    }
}
