package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/1
 */
@Data
public class CarrierOrderListBeforeSignUpForLeyiResponseDto {

	@ApiModelProperty("运单ID")
	private String carrierOrderId = "";

	@ApiModelProperty("运单号")
	private String carrierOrderCode = "";

	@ApiModelProperty("委托类型：1 发货，2 回收，3 采购，4 调拨，6 预约，7 退货,8 供应商直配")
	private String entrustType = "";

	@ApiModelProperty("车主")
	private String carrierCompany = "";

	@ApiModelProperty("提货地")
	private String loadCityName = "";

	@ApiModelProperty("卸货地")
	private String unloadCityName = "";

	@ApiModelProperty("货物单位：1 件，2 吨，3 件（方），4 块")
	private String goodsUnit = "";

	@ApiModelProperty("数量")
	private String amountTotal = "";

	@ApiModelProperty("司机运费合计(元)")
	private String dispatchFreightFeeTotal = "";

	@ApiModelProperty("实际货主费用(元)")
	private String actualEntrustFee = "";

	@ApiModelProperty("临时费用(元)")
	private String otherFee = "";

	@ApiModelProperty("车主运费金额(元)")
	private String carrierFreight = "";

	@ApiModelProperty("实际车主运费(元)")
	private String signCarrierFreight = "";

	@ApiModelProperty("是否我司: 1:我司 2:其他车主 1.3.5新增")
	private String isOurCompany;
}
