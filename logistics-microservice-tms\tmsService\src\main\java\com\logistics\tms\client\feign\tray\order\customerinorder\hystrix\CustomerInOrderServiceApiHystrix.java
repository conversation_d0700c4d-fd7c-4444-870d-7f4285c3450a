package com.logistics.tms.client.feign.tray.order.customerinorder.hystrix;

import com.logistics.tms.client.feign.tray.order.customerinorder.CustomerInOrderServiceApi;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.RollbackDemandRequestModel;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.request.*;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.response.GetCarrierOrderQRCodeResponseModel;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.response.GetCarrierOrderDetailResponseModel;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.response.GetCheckAddressCodeResponse;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.response.GetCustomerInOrderStateResponseModel;
import com.logistics.tms.client.model.TmsLockReplenishDemandFeignRequest;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CustomerInOrderServiceApiHystrix implements CustomerInOrderServiceApi {

    @Override
    public Result<List<GetCustomerInOrderStateResponseModel>> getCustomerStateByCarrierOrderCodes(GetCustomerInOrderStateRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetCarrierOrderQRCodeResponseModel> getCarrierOrderQRCode(GetCarrierOrderQRCodeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetCarrierOrderDetailResponseModel> getCarrierOrderDetail(GetCarrierOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> rejectOrderLoadAmount(RejectOrderLoadAmountRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> rollBackDemandOrder(RollbackDemandRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Void> tmsLockReplenishDemand(TmsLockReplenishDemandFeignRequest request) {
        return Result.timeout();
    }

    @Override
    public Result<GetCheckAddressCodeResponse> getCheckAddressCode() {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> logisticsAddOrUpdateActualAddress(LogisticsAddOrUpdateActualAddressRequest request) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> syncSupplementDemand(SyncSupplementDemandRequest request) {
        return Result.timeout();
    }
}
