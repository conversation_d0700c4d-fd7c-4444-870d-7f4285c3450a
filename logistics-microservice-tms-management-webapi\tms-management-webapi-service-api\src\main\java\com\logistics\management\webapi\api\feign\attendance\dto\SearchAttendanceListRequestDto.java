package com.logistics.management.webapi.api.feign.attendance.dto;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 考勤打卡列表查询请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/15
 */
@Data
public class SearchAttendanceListRequestDto extends AbstractPageForm<SearchAttendanceListRequestDto> {

	//考勤用户
	@ApiModelProperty("考勤用户,姓名或手机号模糊搜索")
	private String attendanceUser;

	//人员机构
	@ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
	private String staffProperty;

	//考勤查询开始时间
	@ApiModelProperty("考勤查询开始时间")
	private String attendanceStartTime;

	//考勤查询结束时间
	@ApiModelProperty("考勤查询结束时间")
	private String attendanceEndTime;
}
