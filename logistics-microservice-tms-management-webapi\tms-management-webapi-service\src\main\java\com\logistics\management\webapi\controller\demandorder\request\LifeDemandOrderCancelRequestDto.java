package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class LifeDemandOrderCancelRequestDto {
    /**
     * 需求单id
     */
    @ApiModelProperty(value = "需求单id",required = true)
    @NotEmpty(message = "请选择需求单")
    private List<String> demandOrderIds;

}
