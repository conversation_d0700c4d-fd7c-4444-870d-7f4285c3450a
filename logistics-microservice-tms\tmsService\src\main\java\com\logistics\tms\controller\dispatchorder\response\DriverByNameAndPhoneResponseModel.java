package com.logistics.tms.controller.dispatchorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DriverByNameAndPhoneResponseModel {
    @ApiModelProperty("司机ID")
    private Long driverId;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("司机电话")
    private String driverPhone;
    @ApiModelProperty("司机身份证号码")
    private String driverIdentityNumber;
}
