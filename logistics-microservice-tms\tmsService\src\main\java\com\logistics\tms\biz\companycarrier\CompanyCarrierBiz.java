package com.logistics.tms.biz.companycarrier;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.RegExpValidatorUtil;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.customeraccount.CustomerAccountBiz;
import com.logistics.tms.controller.companycarrier.request.*;
import com.logistics.tms.controller.companycarrier.response.CompanyCarrierDetailResponseModel;
import com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.tms.controller.companycarrier.response.SearchCompanyCarrierListResponseModel;
import com.logistics.tms.controller.companycarrier.response.UserCompanyCarrierInfoResponseModel;
import com.logistics.tms.controller.customeraccount.request.OpenAccountRequestModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @Author: sj
 * @Date: 2019/9/27 17:21
 */
@Slf4j
@Service("companyCarrierBiz")
public class CompanyCarrierBiz {

    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private TCompanyMapper tCompanyMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TCarrierContactMapper tCarrierContactMapper;
    @Autowired
    private CustomerAccountBiz customerAccountBiz;
    @Autowired
    private TCompanyAuthorizationMapper tCompanyAuthorizationMapper;
    @Autowired
    private TStaffBasicMapper tStaffBasicMapper;
    @Autowired
    private TCertificationPicturesMapper tCertificationPicturesMapper;

    /**
     * 查询公司列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<SearchCompanyCarrierListResponseModel> searchList(SearchCompanyCarrierListRequestModel requestModel) {
        List<SearchCompanyCarrierListResponseModel> carrierList = tCompanyCarrierMapper.getCompanyCarrierList(requestModel);
        return new PageInfo<>(Optional.ofNullable(carrierList).orElse(new ArrayList<>()));
    }

    /**
     * 获取公司详情
     *
     * @param requestModel
     * @return
     */
    public CompanyCarrierDetailResponseModel getDetail(CompanyCarrierDetailRequestModel requestModel) {
        CompanyCarrierDetailResponseModel companyCarrierDetailById = tCompanyCarrierMapper.getCompanyCarrierDetailById(requestModel.getCompanyCarrierId());
        if (companyCarrierDetailById == null) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }
        //个人类型获取数据
        if (CompanyTypeEnum.PERSON.getKey().equals(companyCarrierDetailById.getType())) {
            List<TCarrierContact> contactList = tCarrierContactMapper.getByCompanyCarrierId(companyCarrierDetailById.getCompanyCarrierId());
            if (ListUtils.isNotEmpty(contactList)) {
                TCarrierContact contact = contactList.get(0);
                MapperUtils.mapper(contact, companyCarrierDetailById);
                companyCarrierDetailById.setCarrierContactId(contact.getId());
            }
        }
        return companyCarrierDetailById;
    }

    /**
     * 新增修改
     *
     * @param requestModel
     */
    @Transactional
    public void saveOrUpdate(SaveOrModifyCompanyCarrierRequestModel requestModel) {
        //入参校验
        this.checkRequestParamsForSaveOrUpdate(requestModel);
        //新增修改车主
        if (CompanyTypeEnum.COMPANY.getKey().equals(requestModel.getType())) {//企业
            this.saveOrUpdateCompanyCarrier(requestModel);
        } else {//个人
            this.saveOrUpdatePersonCarrier(requestModel);
        }
    }

    //入参校验
    public void checkRequestParamsForSaveOrUpdate(SaveOrModifyCompanyCarrierRequestModel requestModel){
        Date now = DateUtils.stringToDate(DateUtils.dateToString(new Date(), DateUtils.DATE_TO_STRING_SHORT_PATTERN), DateUtils.DATE_TO_STRING_SHORT_PATTERN);

        //企业类型入参校验
        if (CompanyTypeEnum.COMPANY.getKey().equals(requestModel.getType())) {//企业
            //公司名称不能为空，长度判断
            if (StringUtils.isBlank(requestModel.getCompanyCarrierName())
                    || requestModel.getCompanyCarrierName().length() < CommonConstant.INTEGER_TWO
                    || requestModel.getCompanyCarrierName().length() > CommonConstant.INT_FIFTY) {
                throw new BizException(CarrierDataExceptionEnum.PARAMS_COMPANY_NAME_EMPTY);
            }
            //水印名称长度判断
            if (StringUtils.isNotBlank(requestModel.getCompanyWaterMark())
                    && (requestModel.getCompanyWaterMark().length() < CommonConstant.INTEGER_TWO
                    || requestModel.getCompanyWaterMark().length() > CommonConstant.INT_FIFTY)){
                throw new BizException(CarrierDataExceptionEnum.PARAMS_COMPANY_WATER_MARK_EMPTY);
            }

            //校验营业执照和公司有效期
            if (CommonConstant.INTEGER_ZERO.equals(requestModel.getTradingCertificateIsAmend())) {//非后补
                //营业执照图片不能为空
                if (StringUtils.isBlank(requestModel.getFileSrcPathTradingCertificateImage())) {
                    throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_TRADING_AMEND);
                }
                //公司有效期是永久
                if (CommonConstant.INTEGER_ONE.equals(requestModel.getTradingCertificateIsForever())) {
                    requestModel.setTradingCertificateValidityTime(null);
                }
            }else{//后补
                requestModel.setFileSrcPathTradingCertificateImage(" ");
            }

            //个人类型入参校验
        } else {//个人
            //姓名
            if (StringUtils.isEmpty(requestModel.getContactName())
                    || requestModel.getContactName().length() < CommonConstant.INTEGER_TWO
                    || requestModel.getContactName().length() > CommonConstant.INT_FIFTY) {
                throw new BizException(CarrierDataExceptionEnum.NOT_CONTACTNAME);
            }
            //手机号
            if (StringUtils.isEmpty(requestModel.getContactPhone()) || !RegExpValidatorUtil.isMobile(requestModel.getContactPhone())) {
                throw new BizException(CarrierDataExceptionEnum.NOT_CONTACTPHONE);
            }

            //校验身份证人像面
            if (CommonConstant.INTEGER_ZERO.equals(requestModel.getIdentityFaceFileIsAmend())) {//非后补
                //身份证人像面不能为空
                if (StringUtils.isBlank(requestModel.getIdentityFaceFile())) {
                    throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IDENTITYCARD_AMEND);
                }
                //发证机关
                if (null == requestModel.getProvinceId() || StringUtils.isEmpty(requestModel.getProvinceName())) {
                    throw new BizException(CarrierDataExceptionEnum.NOT_CONTACTPROVICE);
                }
                if (null == requestModel.getCityId() || StringUtils.isEmpty(requestModel.getCityName())) {
                    throw new BizException(CarrierDataExceptionEnum.NOT_CONTACTCITY);
                }
                if (null == requestModel.getAreaId() || StringUtils.isEmpty(requestModel.getAreaName())) {
                    throw new BizException(CarrierDataExceptionEnum.NOT_CONTACTAREA);
                }
                if (StringUtils.isEmpty(requestModel.getCertificationDepartmentDetail())) {
                    throw new BizException(CarrierDataExceptionEnum.NOT_CONTACTCERTIFICATION_DEPARTMENT_DETAIL);
                }
                //身份证号
                if (StringUtils.isBlank(requestModel.getIdentityNumber())) {
                    throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IDENTITYCARD_AMEND_NUMBER);
                }
            }else{//后补
                requestModel.setIdentityFaceFile(" ");
            }

            //校验身份证国徽面
            if (CommonConstant.INTEGER_ZERO.equals(requestModel.getIdentityNationalFileIsAmend())) {//非后补
                //身份证国徽面不能为空
                if (StringUtils.isBlank(requestModel.getIdentityNationalFile())) {
                    throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IDENTITYCARD_AMEND);
                }
                //身份证有效期
                if (CommonConstant.INTEGER_ONE.equals(requestModel.getIdentityIsForever())) {//永久
                    requestModel.setIdentityValidity(null);
                } else {//非永久
                    if (requestModel.getIdentityValidity() == null) {
                        throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IDENTITYCARD_AMEND_DATE);
                    }
                    if (requestModel.getIdentityValidity().before(now)) {
                        throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IDENTITYCARD_TIME_ERROR);
                    }
                }
            }else{//后补
                requestModel.setIdentityNationalFile(" ");
            }
        }

        //道路运输许可证校验
        if (CommonConstant.INTEGER_ZERO.equals(requestModel.getRoadTransportCertificateIsAmend())) {//非后补
            if (StringUtils.isBlank(requestModel.getFileSrcPathRoadTransportCertificateImage())) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_ROAD_AMEND);
            }
            //道路许可证号
            if (StringUtils.isEmpty(requestModel.getRoadTransportCertificateNumber())
                    || requestModel.getRoadTransportCertificateNumber().length() < CommonConstant.INTEGER_SIX
                    || requestModel.getRoadTransportCertificateNumber().length() > CommonConstant.INTEGER_THIRTY) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_ROAD_AMEND_NUMBER);
            }
            //道路许可证有效期
            if (CommonConstant.INTEGER_ZERO.equals(requestModel.getRoadTransportCertificateIsForever())) {//非永久
                //有效期不能为空
                if (requestModel.getRoadTransportCertificateValidityTime() == null) {
                    throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_ROAD_AMEND_DATE);
                }
                //有效期不能小于当前时间
                if (requestModel.getRoadTransportCertificateValidityTime().before(now)) {
                    throw new BizException(CarrierDataExceptionEnum.COMPANY_ERROR_CARRIER_ROAD_AMEND_DATE);
                }
            }else{
                requestModel.setRoadTransportCertificateValidityTime(null);
            }
        }else{//后补
            requestModel.setFileSrcPathRoadTransportCertificateImage(" ");
        }
    }

    /**
     * 新增修改企业车主
     * @param requestModel
     */
    public void saveOrUpdateCompanyCarrier(SaveOrModifyCompanyCarrierRequestModel requestModel) {
        //创建车主实体
        TCompanyCarrier companyCarrier = new TCompanyCarrier();
        companyCarrier.setCompanyWaterMark(requestModel.getCompanyWaterMark());
        companyCarrier.setRoadTransportCertificateIsForever(requestModel.getRoadTransportCertificateIsForever());
        companyCarrier.setRoadTransportCertificateIsAmend(requestModel.getRoadTransportCertificateIsAmend());
        companyCarrier.setRoadTransportCertificateValidityTime(requestModel.getRoadTransportCertificateValidityTime());
        companyCarrier.setRoadTransportCertificateNumber(requestModel.getRoadTransportCertificateNumber());
        companyCarrier.setCommitOtherFee(requestModel.getCommitOtherFee());
        companyCarrier.setRemark(requestModel.getRemark());
        companyCarrier.setFreightTaxPoint(requestModel.getFreightTaxPoint());
        companyCarrier.setOtherFeeTaxPoint(requestModel.getOtherFeeTaxPoint());

        //创建主公司实体
        TCompany company = new TCompany();
        company.setCompanyName(requestModel.getCompanyCarrierName());
        company.setTradingCertificateIsAmend(requestModel.getTradingCertificateIsAmend());
        company.setTradingCertificateIsForever(requestModel.getTradingCertificateIsForever());
        company.setTradingCertificateValidityTime(requestModel.getTradingCertificateValidityTime());

        //根据公司名称查询主表
        TCompany dbCompany = tCompanyMapper.getByName(requestModel.getCompanyCarrierName());

        //新增修改车主表
        if (requestModel.getCompanyCarrierId() != null && requestModel.getCompanyCarrierId() > CommonConstant.LONG_ZERO) {//修改
            //根据id查询车主
            TCompanyCarrier dbCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(requestModel.getCompanyCarrierId());
            if (dbCompanyCarrier == null || IfValidEnum.INVALID.getKey().equals(dbCompanyCarrier.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }

            //根据主公司id查询主公司信息
            TCompany tCompany = tCompanyMapper.selectByPrimaryKey(dbCompanyCarrier.getCompanyId());
            if (tCompany == null || IfValidEnum.INVALID.getKey().equals(tCompany.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }

            //判断要修改为的公司名是否存在
            if (dbCompany != null && !dbCompany.getId().equals(tCompany.getId())) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_REPEAT);
            }

            //更新公司主表信息
            if (StringUtils.isNotBlank(requestModel.getFileSrcPathTradingCertificateImage())) {
                if (!requestModel.getFileSrcPathTradingCertificateImage().equals(tCompany.getTradingCertificateImage())) {
                    company.setTradingCertificateImage(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.COMPANY_CARRIER_TRADING.getKey(), "", requestModel.getFileSrcPathTradingCertificateImage(), null));
                }
            } else {
                company.setTradingCertificateImage("");
            }

            company.setId(tCompany.getId());
            commonBiz.setBaseEntityModify(company, BaseContextHandler.getUserName());
            tCompanyMapper.updateByPrimaryKeySelectiveForTime(company);

            //更新车主信息
            if (StringUtils.isNotBlank(requestModel.getFileSrcPathRoadTransportCertificateImage())) {
                if (!requestModel.getFileSrcPathRoadTransportCertificateImage().equals(dbCompanyCarrier.getRoadTransportCertificateImage())) {
                    companyCarrier.setRoadTransportCertificateImage(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.COMPANY_CARRIER_ROAD_TRANSPORT.getKey(), "", requestModel.getFileSrcPathRoadTransportCertificateImage(), null));
                }
            } else {
                companyCarrier.setRoadTransportCertificateImage("");
            }
            companyCarrier.setId(dbCompanyCarrier.getId());
            commonBiz.setBaseEntityModify(companyCarrier, BaseContextHandler.getUserName());
            tCompanyCarrierMapper.updateByPrimaryKeySelectiveForTime(companyCarrier);
        } else {//新增

            //判断公司名是否存在
            if (dbCompany != null) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_REPEAT);
            }

            //新增公司主表信息
            if (StringUtils.isNotBlank(requestModel.getFileSrcPathTradingCertificateImage())) {
                company.setTradingCertificateImage(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.COMPANY_CARRIER_TRADING.getKey(), "", requestModel.getFileSrcPathTradingCertificateImage(), null));
            }
            commonBiz.setBaseEntityAdd(company, BaseContextHandler.getUserName());
            tCompanyMapper.insertSelective(company);

            //新增车主信息
            if (StringUtils.isNotBlank(requestModel.getFileSrcPathRoadTransportCertificateImage())) {
                companyCarrier.setRoadTransportCertificateImage(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.COMPANY_CARRIER_ROAD_TRANSPORT.getKey(), "", requestModel.getFileSrcPathRoadTransportCertificateImage(), null));
            }
            companyCarrier.setType(CompanyTypeEnum.COMPANY.getKey());
            companyCarrier.setCompanyId(company.getId());
            commonBiz.setBaseEntityAdd(companyCarrier, BaseContextHandler.getUserName());
            tCompanyCarrierMapper.insertSelective(companyCarrier);

            //新增车主授权列表数据
            TCompanyAuthorization tCompanyAuthorization = new TCompanyAuthorization();
            tCompanyAuthorization.setCompanyId(company.getId());
            tCompanyAuthorization.setCompanyType(CommonConstant.INTEGER_ONE);
            tCompanyAuthorization.setAuditStatus(CompanyCarrierAuthAuditStatusEnum.WAIT_AUTH.getKey());
            commonBiz.setBaseEntityModify(tCompanyAuthorization, BaseContextHandler.getUserName());
            tCompanyAuthorizationMapper.insertSelective(tCompanyAuthorization);
        }
    }

    /**
     * 新增修改个人车主
     * @param requestModel
     */
    public void saveOrUpdatePersonCarrier(SaveOrModifyCompanyCarrierRequestModel requestModel){
        //创建车主实体
        TCompanyCarrier companyCarrier = new TCompanyCarrier();
        companyCarrier.setRoadTransportCertificateIsForever(requestModel.getRoadTransportCertificateIsForever());
        companyCarrier.setRoadTransportCertificateIsAmend(requestModel.getRoadTransportCertificateIsAmend());
        companyCarrier.setRoadTransportCertificateValidityTime(requestModel.getRoadTransportCertificateValidityTime());
        companyCarrier.setRoadTransportCertificateNumber(requestModel.getRoadTransportCertificateNumber());
        companyCarrier.setType(requestModel.getType());
        companyCarrier.setCommitOtherFee(requestModel.getCommitOtherFee());
        companyCarrier.setRemark(requestModel.getRemark());
        companyCarrier.setFreightTaxPoint(requestModel.getFreightTaxPoint());
        companyCarrier.setOtherFeeTaxPoint(requestModel.getOtherFeeTaxPoint());

        //创建账号实体
        TCarrierContact carrierContact = new TCarrierContact();
        MapperUtils.mapper(requestModel, carrierContact);

        //根据手机查询账号
        TCarrierContact isContact = tCarrierContactMapper.selectByContactPhone(requestModel.getContactPhone());
        if (requestModel.getCompanyCarrierId() != null && requestModel.getCompanyCarrierId() > CommonConstant.LONG_ZERO) {//修改
            //查询车主是否存在
            TCompanyCarrier dbCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(requestModel.getCompanyCarrierId());
            if (dbCompanyCarrier == null || IfValidEnum.INVALID.getKey().equals(dbCompanyCarrier.getValid())){
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }
            //查询账号是否存在
            TCarrierContact dbCarrierContact = tCarrierContactMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierContactId());
            if (dbCarrierContact == null
                    || IfValidEnum.INVALID.getKey().equals(dbCarrierContact.getValid())
                    || !dbCarrierContact.getCompanyCarrierId().equals(dbCompanyCarrier.getId())) {
                throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
            }
            //修改手机号已存在且不是本账号，则不能修改
            if (isContact != null && !isContact.getId().equals(dbCarrierContact.getId())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ACCOUNT_PHONE_REPEAT);
            }

            //更新车主信息
            if (StringUtils.isNotBlank(requestModel.getFileSrcPathRoadTransportCertificateImage())) {
                if (!requestModel.getFileSrcPathRoadTransportCertificateImage().equals(dbCompanyCarrier.getRoadTransportCertificateImage())) {
                    companyCarrier.setRoadTransportCertificateImage(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.COMPANY_CARRIER_ROAD_TRANSPORT.getKey(), "", requestModel.getFileSrcPathRoadTransportCertificateImage(), null));
                }
            } else {
                companyCarrier.setRoadTransportCertificateImage("");
            }
            companyCarrier.setId(dbCompanyCarrier.getId());
            commonBiz.setBaseEntityModify(companyCarrier, BaseContextHandler.getUserName());
            tCompanyCarrierMapper.updateByPrimaryKeySelectiveForTime(companyCarrier);

            //更新车主账号
            //身份证人像面
            if (StringUtils.isNotBlank(requestModel.getIdentityFaceFile())) {
                if (!requestModel.getIdentityFaceFile().equals(dbCarrierContact.getIdentityFaceFile())) {
                    carrierContact.setIdentityFaceFile(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CUSTOMER_CARRIER_IDENTITY_FACE.getKey(), "", requestModel.getIdentityFaceFile(), null));
                }
            } else {
                carrierContact.setIdentityFaceFile("");
            }
            //身份证国徽面
            if (StringUtils.isNotBlank(requestModel.getIdentityNationalFile())) {
                if (!requestModel.getIdentityNationalFile().equals(dbCarrierContact.getIdentityNationalFile())) {
                    carrierContact.setIdentityNationalFile(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CUSTOMER_CARRIER_IDENTITY_NATIONAL.getKey(), "", requestModel.getIdentityNationalFile(), null));
                }
            } else {
                carrierContact.setIdentityNationalFile("");
            }
            carrierContact.setId(dbCarrierContact.getId());
            carrierContact.setContactPhone(null);//手机号不能修改
            // 编辑时,姓名、手机号、身份证号、身份证件信息，待实名允许修改，实名中以及已实名不允许修改
            if (RealNameAuthenticationStatusEnum.REAL_NAME.getKey().equals(dbCompanyCarrier.getRealNameAuthenticationStatus())) {
                if (!dbCarrierContact.getContactName().equals(requestModel.getContactName())
                        || !dbCarrierContact.getContactPhone().equals(requestModel.getContactPhone())
                        || !dbCarrierContact.getIdentityNumber().equals(requestModel.getIdentityNumber())
                        || !dbCarrierContact.getIdentityFaceFile().equals(requestModel.getIdentityFaceFile())
                        || !dbCarrierContact.getIdentityNationalFile().equals(requestModel.getIdentityNationalFile())
                ) {
                    throw new BizException(CarrierDataExceptionEnum.CARRIER_ALREADY_REAL_NAME_AUTH);
                }
            }
            commonBiz.setBaseEntityModify(carrierContact, BaseContextHandler.getUserName());
            tCarrierContactMapper.updateByPrimaryKeySelectiveTime(carrierContact);
            //修改用户名
            if (!dbCarrierContact.getContactName().equals(requestModel.getContactName())) {
                OpenAccountRequestModel openAccountRequestModel = new OpenAccountRequestModel();
                openAccountRequestModel.setUserId(carrierContact.getId());
                openAccountRequestModel.setUserRole(AccountUserRoleTypeEnum.CARRIER.getKey());
                openAccountRequestModel.setUserName(carrierContact.getContactName());
                openAccountRequestModel.setType(CommonConstant.INTEGER_ONE);
                if (CommonConstant.INTEGER_ONE.equals(dbCarrierContact.getEnabled())) {
                    openAccountRequestModel.setIfClose(CommonConstant.INTEGER_ZERO);
                } else {
                    openAccountRequestModel.setIfClose(CommonConstant.INTEGER_ONE);
                }
                customerAccountBiz.openOrCloseAccount(openAccountRequestModel);
            }
        }else{//新增
            if (isContact != null) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ACCOUNT_PHONE_REPEAT);
            }

            //新增车主信息
            if (StringUtils.isNotBlank(requestModel.getFileSrcPathRoadTransportCertificateImage())) {
                companyCarrier.setRoadTransportCertificateImage(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.COMPANY_CARRIER_ROAD_TRANSPORT.getKey(), "", requestModel.getFileSrcPathRoadTransportCertificateImage(), null));
            }

            //新增账号
            if (StringUtils.isNotBlank(requestModel.getIdentityFaceFile())) {
                carrierContact.setIdentityFaceFile(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CUSTOMER_CARRIER_IDENTITY_FACE.getKey(), "", requestModel.getIdentityFaceFile(), null));
            }
            if (StringUtils.isNotBlank(requestModel.getIdentityNationalFile())) {
                carrierContact.setIdentityNationalFile(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CUSTOMER_CARRIER_IDENTITY_NATIONAL.getKey(), "", requestModel.getIdentityNationalFile(), null));
            }

            //查询是否含有实名的司机
            TStaffBasic tStaffBasic = tStaffBasicMapper.getByMobile(requestModel.getContactPhone());
            if (tStaffBasic != null) {
                if (RealNameAuthenticationStatusEnum.REAL_NAME.getKey().equals(tStaffBasic.getRealNameAuthenticationStatus())) {
                    carrierContact.setContactName(tStaffBasic.getName());
                    carrierContact.setIdentityNumber(tStaffBasic.getIdentityNumber());

                    //设置身份证证件
                    List<TCertificationPictures> identityFrontList = tCertificationPicturesMapper.getByObjectIdType(tStaffBasic.getId(), CertificationPicturesObjectTypeEnum.T_STAFF_BASIC.getObjectType(), CertificationPicturesFileTypeEnum.STAFF_IDENTITY_FRONT.getFileType());
                    List<TCertificationPictures> identityBackList = tCertificationPicturesMapper.getByObjectIdType(tStaffBasic.getId(), CertificationPicturesObjectTypeEnum.T_STAFF_BASIC.getObjectType(), CertificationPicturesFileTypeEnum.STAFF_IDENTITY_BACK.getFileType());
                    if (ListUtils.isNotEmpty(identityFrontList)) {
                        //人像面
                        TCertificationPictures identityFront = identityFrontList.get(CommonConstant.INTEGER_ZERO);
                        carrierContact.setIdentityFaceFile(identityFront.getFilePath());
                    }
                    if (ListUtils.isNotEmpty(identityBackList)) {
                        //国徽面
                        TCertificationPictures identityBack = identityBackList.get(CommonConstant.INTEGER_ZERO);
                        carrierContact.setIdentityNationalFile(identityBack.getFilePath());
                    }

                    companyCarrier.setRealNameAuthenticationStatus(RealNameAuthenticationStatusEnum.REAL_NAME.getKey());
                }
            }

            commonBiz.setBaseEntityAdd(companyCarrier, BaseContextHandler.getUserName());
            tCompanyCarrierMapper.insertSelective(companyCarrier);

            //个人账号设置为禁用状态
            carrierContact.setEnabled(CommonConstant.INTEGER_ZERO);
            carrierContact.setCompanyCarrierId(companyCarrier.getId());
            commonBiz.setBaseEntityAdd(carrierContact, BaseContextHandler.getUserName());
            tCarrierContactMapper.insertSelectiveEncrypt(carrierContact);

            //账号开通
            OpenAccountRequestModel openAccountRequestModel = new OpenAccountRequestModel();
            openAccountRequestModel.setUserId(carrierContact.getId());
            openAccountRequestModel.setUserRole(AccountUserRoleTypeEnum.CARRIER.getKey());
            openAccountRequestModel.setMobile(carrierContact.getContactPhone());
            openAccountRequestModel.setUserName(carrierContact.getContactName());
            openAccountRequestModel.setType(CommonConstant.INTEGER_ONE);
            openAccountRequestModel.setEnabled(EnabledEnum.DISABLED.getKey());
            openAccountRequestModel.setIfClose(CommonConstant.INTEGER_ONE);
            customerAccountBiz.openOrCloseAccount(openAccountRequestModel);
        }
    }

    /**
     * 根据公司名称模糊查询（排除加入黑名单的车主）
     *
     * @param requestModel
     * @return
     */
    public List<FuzzySearchCompanyCarrierResponseModel> fuzzyQuery(FuzzySearchCompanyCarrierRequestModel requestModel) {
        return tCompanyCarrierMapper.fuzzyQueryCompanyCarrierInfo(requestModel);
    }

    /**
     * 根据登录人查询公司信息，界面上部bar
     * @return
     */
    public UserCompanyCarrierInfoResponseModel getUserAndCompanyInfo() {
        //查询登录人车主账号信息
        TCarrierContact dbCarrierContact = tCarrierContactMapper.selectByPrimaryKeyDecrypt(commonBiz.getNotCloseLoginUserCarrierContactId());
        if (dbCarrierContact == null || dbCarrierContact.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
        }
        //查询公司
        TCompanyCarrier dbCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(dbCarrierContact.getCompanyCarrierId());
        if (dbCompanyCarrier == null || dbCompanyCarrier.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }

        UserCompanyCarrierInfoResponseModel responseModel = new UserCompanyCarrierInfoResponseModel();
        responseModel.setCompanyCarrierId(dbCompanyCarrier.getId());
        responseModel.setCarrierContactName(dbCarrierContact.getContactName());
        responseModel.setCarrierContactPhone(dbCarrierContact.getContactPhone());
        responseModel.setType(dbCompanyCarrier.getType());
        if (CompanyTypeEnum.COMPANY.getKey().equals(dbCompanyCarrier.getType())){
            TCompany dbCompany = tCompanyMapper.selectByPrimaryKey(dbCompanyCarrier.getCompanyId());
            responseModel.setCompanyCarrierName(dbCompany.getCompanyName());
        }else{
            responseModel.setCompanyCarrierName(dbCarrierContact.getContactName() + " " + dbCarrierContact.getContactPhone());
        }
        return responseModel;
    }

    /**
     * 开启/关闭黑名单
     * @param requestModel
     */
    @Transactional
    public void openOrClose(OpenCloseBlacklistRequestModel requestModel) {
        List<Long> companyCarrierIdList = requestModel.getCompanyCarrierIdList();
        List<TCompanyCarrier> dbCompanyCarrierList = tCompanyCarrierMapper.getByIds(StringUtils.listToString(companyCarrierIdList,','));
        if (ListUtils.isEmpty(dbCompanyCarrierList)){
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }

        Integer ifAddBlacklist;
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getOperateType())){
            ifAddBlacklist = CommonConstant.INTEGER_ONE;
        }else{
            ifAddBlacklist = CommonConstant.INTEGER_ZERO;
        }

        TCompanyCarrier upCompanyCarrier;
        List<TCompanyCarrier> upList = new ArrayList<>();
        for (TCompanyCarrier tCompanyCarrier : dbCompanyCarrierList) {
            if (!ifAddBlacklist.equals(tCompanyCarrier.getIfAddBlacklist())) {
                upCompanyCarrier = new TCompanyCarrier();
                upCompanyCarrier.setId(tCompanyCarrier.getId());
                upCompanyCarrier.setIfAddBlacklist(ifAddBlacklist);
                commonBiz.setBaseEntityModify(upCompanyCarrier, BaseContextHandler.getUserName());
                upList.add(upCompanyCarrier);
            }
        }
        if (ListUtils.isNotEmpty(upList)) {
            tCompanyCarrierMapper.batchUpdateSelective(upList);
        }
    }

    /**
     * 开启/关闭零担模式
     *
     * @param requestModel
     */
    @Transactional
    public void openOrCloseLessThanTruckload(OpenCloseLessThanTruckloadRequestModel requestModel){
        List<Long> companyCarrierIdList = requestModel.getCompanyCarrierIdList();
        List<TCompanyCarrier> dbCompanyCarrierList = tCompanyCarrierMapper.getByIds(StringUtils.listToString(companyCarrierIdList,','));
        if (ListUtils.isEmpty(dbCompanyCarrierList)){
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }

        Integer ifLessThanTruckload;
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getOperateType())){
            ifLessThanTruckload = CommonConstant.INTEGER_ONE;
        }else{
            ifLessThanTruckload = CommonConstant.INTEGER_ZERO;
        }

        TCompanyCarrier upCompanyCarrier;
        List<TCompanyCarrier> upList = new ArrayList<>();
        for (TCompanyCarrier tCompanyCarrier : dbCompanyCarrierList) {
            if (!ifLessThanTruckload.equals(tCompanyCarrier.getIfLessThanTruckload())) {
                upCompanyCarrier = new TCompanyCarrier();
                upCompanyCarrier.setId(tCompanyCarrier.getId());
                upCompanyCarrier.setIfLessThanTruckload(ifLessThanTruckload);
                commonBiz.setBaseEntityModify(upCompanyCarrier, BaseContextHandler.getUserName());
                upList.add(upCompanyCarrier);
            }
        }
        if (ListUtils.isNotEmpty(upList)) {
            tCompanyCarrierMapper.batchUpdateSelective(upList);
        }
    }

    @Transactional
    public void autoCarrierBackList(){
        Long qiyaCompanyCarrierId = commonBiz.getQiyaCompanyCarrierId();
        // 排除江苏云途
        tCompanyCarrierMapper.updateNoAuthCompanyAndMore31Day(qiyaCompanyCarrierId);
    }
}
