package com.logistics.appapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CarrierOrderLoadRequestModel {

    @ApiModelProperty("运单Id")
    private Long carrierOrderId;
    @ApiModelProperty("提货数量")
    private List<CarrierOrderGoodsLoadUnloadRequestModel> goodsList;
    @ApiModelProperty("图片路径")
    private List<CarrierOrderTicketRequestModel> tickets;
    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;
    @ApiModelProperty("现场其他托盘信息")
    private SiteOtherPalletsRequestModel siteOtherPallets;
    @ApiModelProperty("提货方式 1 票据提货，2 二维码提货")
    private Integer deliveryMethod;
}
