<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TVehicleSafeCheckItemMapper">
  <select id="getListBySafeCheckVehicleId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_vehicle_safe_check_item
    where valid = 1
    and safe_check_vehicle_id = #{safeCheckVehicleId,jdbcType = BIGINT}
  </select>
  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TVehicleSafeCheckItem">
    <foreach collection="list" item="item" separator=";">
      insert into t_vehicle_safe_check_item
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          id,
        </if>
        <if test="item.safeCheckVehicleId != null">
          safe_check_vehicle_id,
        </if>
        <if test="item.itemType != null">
          item_type,
        </if>
        <if test="item.status != null">
          status,
        </if>
        <if test="item.createdBy != null">
          created_by,
        </if>
        <if test="item.createdTime != null">
          created_time,
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time,
        </if>
        <if test="item.valid != null">
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.safeCheckVehicleId != null">
          #{item.safeCheckVehicleId,jdbcType=BIGINT},
        </if>
        <if test="item.itemType != null">
          #{item.itemType,jdbcType=INTEGER},
        </if>
        <if test="item.status != null">
          #{item.status,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null">
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>


  <update id="batchUpdate" parameterType="com.logistics.tms.entity.TVehicleSafeCheckItem">
    <foreach collection="list" item="item" separator=";">
      update t_vehicle_safe_check_item
      <set>
        <if test="item.safeCheckVehicleId != null">
          safe_check_vehicle_id = #{item.safeCheckVehicleId,jdbcType=BIGINT},
        </if>
        <if test="item.itemType != null">
          item_type = #{item.itemType,jdbcType=INTEGER},
        </if>
        <if test="item.status != null">
          status = #{item.status,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null">
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>