package com.logistics.appapi.controller.reserve.mapping;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.ReserveApplyAuditStatusEnum;
import com.logistics.appapi.client.reserveapply.response.ReserveBalanceApplyListItemModel;
import com.logistics.appapi.client.reserveapply.response.ReserveBalanceApplyListResponseModel;
import com.logistics.appapi.controller.reserve.response.ReserveBalanceApplyListItemDto;
import com.logistics.appapi.controller.reserve.response.ReserveBalanceApplyListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

public class ReserveBalanceApplyListMapping extends MapperMapping<ReserveBalanceApplyListResponseModel, ReserveBalanceApplyListResponseDto> {

    @Override
    public void configure() {
        ReserveBalanceApplyListResponseModel source = getSource();
        ReserveBalanceApplyListResponseDto destination = getDestination();

        // 金额转换
        destination.setApproveAmount(conversionAmount(source.getApproveAmount()));
        // page转换
        PageInfo applyPageInfo = source.getApplyPageInfo();
        List applyList = applyPageInfo.getList();
        if (ListUtils.isNotEmpty(applyList)) {
            applyList = MapperUtils.mapper(applyList, ReserveBalanceApplyListItemDto.class, new ReserveBalanceApplyListItemMapping());
            applyPageInfo.setList(applyList);
        }
        destination.setApplyList(applyPageInfo);
    }

    class ReserveBalanceApplyListItemMapping extends MapperMapping<ReserveBalanceApplyListItemModel, ReserveBalanceApplyListItemDto> {
        @Override
        public void configure() {
            ReserveBalanceApplyListItemModel source = getSource();
            ReserveBalanceApplyListItemDto destination = getDestination();

            // 金额转换
            destination.setApplyAmount(conversionAmount(source.getApplyAmount()));
            destination.setApproveAmount(conversionAmount(source.getApproveAmount()));

            // 日期格式化
            destination.setApplyDate(DateUtils.dateToString(source.getApplyDate(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));

            // 申请人
            destination.setProposer(String.format(CommonConstant.NAME_MOBILE_FORMAT, source.getStaffName(), source.getStaffMobile()));

            // 审核状态文本
            destination.setStatusLabel(ReserveApplyAuditStatusEnum.getEnumByKey(source.getStatus()).getValue());
        }
    }

    private String conversionAmount(BigDecimal amount) {
        if (Objects.isNull(amount)) {
            return CommonConstant.BLANK_TEXT;
        }
        return amount.stripTrailingZeros().toPlainString();
    }
}
