package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2023/01/13
*/
@Data
public class TCustomerBestsignCredentialInfo extends BaseEntity {
    /**
    * 实名认证表id
    */
    @ApiModelProperty("实名认证表id")
    private Long realNameAuthId;

    /**
    * 上上签账户
    */
    @ApiModelProperty("上上签账户")
    private String bestsignAccount;

    /**
    * 证书编号
    */
    @ApiModelProperty("证书编号")
    private String certId;

    /**
    * CA证书序列号
    */
    @ApiModelProperty("CA证书序列号")
    private String serialNumber;

    /**
    * 证书主题
    */
    @ApiModelProperty("证书主题")
    private String subjectDn;

    /**
    * 颁发机构
    */
    @ApiModelProperty("颁发机构")
    private String issuerDn;

    /**
    * 有效期开始时间
    */
    @ApiModelProperty("有效期开始时间")
    private Date startTime;

    /**
    * 有效期截止时间
    */
    @ApiModelProperty("有效期截止时间")
    private Date stopTime;

    /**
    * 吊销时间，正常使用的证书无此项内容
    */
    @ApiModelProperty("吊销时间，正常使用的证书无此项内容")
    private Date revokedTime;

    /**
    * 吊销原因，正常使用de 证书无此项内容
    */
    @ApiModelProperty("吊销原因，正常使用de 证书无此项内容")
    private String revokedReason;

    /**
    * 状态码：1激活，-2吊销
    */
    @ApiModelProperty("状态码：1激活，-2吊销")
    private Integer status;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}