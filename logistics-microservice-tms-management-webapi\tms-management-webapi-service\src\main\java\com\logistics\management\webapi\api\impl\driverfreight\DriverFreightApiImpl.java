package com.logistics.management.webapi.api.impl.driverfreight;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.driverfreight.DriverFreightApi;
import com.logistics.management.webapi.api.feign.driverfreight.dto.DriverFreightListSearchRequestDto;
import com.logistics.management.webapi.api.feign.driverfreight.dto.DriverFreightListSearchResponseDto;
import com.logistics.management.webapi.api.impl.driverfreight.mapping.DriverFreightListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportDriverFreightInfo;
import com.logistics.tms.api.feign.driverfreight.DriverFreightServiceApi;
import com.logistics.tms.api.feign.driverfreight.model.DriverFreightListSearchRequestModel;
import com.logistics.tms.api.feign.driverfreight.model.DriverFreightListSearchResponseModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ExcelUtils;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Api(value = "API-司机运费管理")
@RestController
public class DriverFreightApiImpl implements DriverFreightApi {

    @Autowired
    private DriverFreightServiceApi driverFreightServiceApi;

    /**
     * 司机运费列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<DriverFreightListSearchResponseDto>> driverFreightList(@RequestBody DriverFreightListSearchRequestDto requestDto) {
        Result<PageInfo<DriverFreightListSearchResponseModel>> result = driverFreightServiceApi.driverFreightList(MapperUtils.mapper(requestDto, DriverFreightListSearchRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        Optional.ofNullable(result.getData()).ifPresent(t->{
            List<DriverFreightListSearchResponseDto> list = MapperUtils.mapper(pageInfo.getList(),DriverFreightListSearchResponseDto.class,new DriverFreightListMapping());
            pageInfo.setList(list);
        });
        return Result.success(pageInfo);
    }

    /**
     * 导出司机运费
     * @param requestDto
     * @param response
     */
    @Override
    public void exportDriverFreightList(DriverFreightListSearchRequestDto requestDto , HttpServletResponse response) {
        DriverFreightListSearchRequestModel driverPayeeListRequestModel = MapperUtils.mapper(requestDto, DriverFreightListSearchRequestModel.class);
        driverPayeeListRequestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        driverPayeeListRequestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<DriverFreightListSearchResponseModel>> result = driverFreightServiceApi.driverFreightList(driverPayeeListRequestModel);
        result.throwException();
        String fileName = "司机运费" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        if(result.getData()!=null){
            List<DriverFreightListSearchResponseModel> resultList = result.getData().getList();
            List<DriverFreightListSearchResponseDto> list = MapperUtils.mapper(resultList,DriverFreightListSearchResponseDto.class,new DriverFreightListMapping());
            Map<String, String> exportTypeMap = ExportDriverFreightInfo.getDriverFreightInfo();
            ExcelUtils.exportExcelForServlet(fileName, fileName, exportTypeMap, response, new AbstractExcelLoadBean() {
                @Override
                public List load() {
                    return list;
                }
            });
        }

    }
}
