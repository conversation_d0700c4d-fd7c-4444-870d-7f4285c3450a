package com.logistics.management.webapi.controller.vehiclelength.mapping;

import com.logistics.management.webapi.client.vehiclelength.response.SearchVehicleLengthListResponseModel;
import com.logistics.management.webapi.controller.vehiclelength.response.SearchVehicleLengthListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2024/4/29 9:25
 */
public class SearchVehicleLengthListMapping extends MapperMapping<SearchVehicleLengthListResponseModel, SearchVehicleLengthListResponseDto> {
    @Override
    public void configure() {
        SearchVehicleLengthListResponseModel source = getSource();
        SearchVehicleLengthListResponseDto destination = getDestination();

        destination.setVehicleLength(source.getVehicleLength().stripTrailingZeros().toPlainString());
        destination.setCarriageScopeMin(source.getCarriageScopeMin().stripTrailingZeros().toPlainString());
        destination.setCarriageScopeMax(source.getCarriageScopeMax().stripTrailingZeros().toPlainString());
    }
}
