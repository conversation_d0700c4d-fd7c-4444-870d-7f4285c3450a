/**
 * Created by yun.zhou on 2017/12/12.
 */
package com.logistics.appapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum LeaveApplyAuditStatusEnum {

    DEFAULT(-1, ""),
    WAIT_AUDIT(0, "待审核"),
    AUDIT_THROUGH(1, "已审核"),
    AUDIT_REJECT(2, "已驳回"),
    WAIT_SUBMIT(3, "已撤销"),;

    private Integer key;
    private String value;

    public static LeaveApplyAuditStatusEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
