package com.logistics.management.webapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/27
 */
@Data
public class CarrierOrderLoadRequestModel {

	@ApiModelProperty("要提货的运单")
	private List<LoadRequestModel> loadCarrierOrderList;

	@ApiModelProperty("请求来源: 1:后台 2:前台")
	private Integer source;
}
