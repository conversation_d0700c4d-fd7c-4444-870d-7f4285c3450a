package com.logistics.management.webapi.api.feign.driverappoint.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.driverappoint.DriverAppointApi;
import com.logistics.management.webapi.api.feign.driverappoint.dto.SearchDriverAppointRequestDto;
import com.logistics.management.webapi.api.feign.driverappoint.dto.SearchDriverAppointResponseDto;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2022/8/17 10:37
 */
@Component
public class DriverAppointApiHystrix implements DriverAppointApi {
    @Override
    public Result<PageInfo<SearchDriverAppointResponseDto>> searchDriverAppointList(SearchDriverAppointRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void exportDriverAppoint(SearchDriverAppointRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }
}
