package com.logistics.tms.api.feign.driverpayee.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchDriverPayeesResponseModel {

    @ApiModelProperty("司机账号ID")
    private Long driverPayeeId;
    @ApiModelProperty("收款人姓名")
    private String name = "";
    @ApiModelProperty("收款人联系方式")
    private String mobile = "";
    @ApiModelProperty("银行卡号")
    private String bankCardNo = "";
    @ApiModelProperty("银行名称")
    private String bankName = "";
}
