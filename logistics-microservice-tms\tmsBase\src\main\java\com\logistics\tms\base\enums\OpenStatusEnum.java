package com.logistics.tms.base.enums;

/**
 * @Author: sj
 * @Date: 2020/1/7 9:27
 */
public enum OpenStatusEnum {
    WAIT_OPEN(0, "待开通"),
    OPEN(1, "已开通"),
    CLOSE(2, "已关闭"),
    ;

    private Integer key;
    private String value;

    OpenStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
