package com.logistics.tms.controller.carriervehiclerel.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/10/14 13:36
 */
@Data
public class SearchCarrierVehicleListResponseModel {

    @ApiModelProperty("车主车辆关联Id")
    private Long carrierVehicleId;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("车辆类型")
    private String vehicleType;

    @ApiModelProperty("装载量（可装载托盘数）")
    private Integer loadingCapacity;

    @ApiModelProperty("添加人名称")
    private String createdByName;

    @ApiModelProperty("添加时间")
    private Date createdTime;
}
