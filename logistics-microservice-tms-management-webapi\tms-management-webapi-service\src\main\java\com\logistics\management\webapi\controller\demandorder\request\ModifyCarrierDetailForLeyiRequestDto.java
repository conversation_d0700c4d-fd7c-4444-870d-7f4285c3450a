package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/6
 */
@Data
public class ModifyCarrierDetailForLeyiRequestDto {

	@ApiModelProperty(value = "需求单ID", required = true)
	@NotEmpty(message = "需求单ID不能为空")
	private List<String> demandOrderIds;

}
