package com.logistics.management.webapi.api.feign.driveraccount.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchDriverAccountResponseDto {

    @ApiModelProperty(value = "司机账户ID")
    private String driverAccountId = "";

    @ApiModelProperty(value = "机构")
    private String driverPropertyLabel = "";

    @ApiModelProperty(value = "司机名称, 姓名 + 手机号（加密)")
    private String driverName = "";

    @ApiModelProperty(value = "银行账号")
    private String bankAccount = "";

    @ApiModelProperty(value = "银行名称, 开户银行名称 + 开户支行名称")
    private String bankAccountName = "";

    @ApiModelProperty(value = "银行卡图片数量")
    private String bankImageNumber = "";

    @ApiModelProperty(value = "新增人")
    private String createdBy = "";

    @ApiModelProperty(value = "操作人")
    private String operateBy = "";

    @ApiModelProperty(value = "操作时间, yyyy-MM-dd HH:mm:ss")
    private String operateDateTime = "";
}
