package com.logistics.management.webapi.controller.demandorder.mapping;

import com.logistics.management.webapi.client.demandorder.response.BatchPublishDetailResponseModel;
import com.logistics.management.webapi.client.demandorder.response.LifeBatchPublishDetailResponseModel;
import com.logistics.management.webapi.controller.demandorder.response.BatchPublishDetailResponseDto;
import com.logistics.management.webapi.controller.demandorder.response.LifeBatchPublishDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

import java.util.Optional;

/**
 * @author: wjf
 * @date: 2021/9/27 15:54
 */
public class LifePublishDetailMapping extends MapperMapping<LifeBatchPublishDetailResponseModel, LifeBatchPublishDetailResponseDto> {
    @Override
    public void configure() {
        LifeBatchPublishDetailResponseModel source = getSource();
        LifeBatchPublishDetailResponseDto destination = getDestination();

        destination.setGoodsAmount(source.getGoodsAmount().stripTrailingZeros().toPlainString());

        //发货地址
        StringBuilder load = new StringBuilder();
        if (StringUtils.isNotBlank(source.getLoadWarehouse())) {
            load.append("【" + source.getLoadWarehouse() + "】");
        }
        load .append(Optional.ofNullable(source.getLoadProvinceName()).orElse("")).
                append(Optional.ofNullable(source.getLoadCityName()).orElse("")).
                append(Optional.ofNullable(source.getLoadAreaName()).orElse(""));
        destination.setLoadDetailAddress(load.toString());

        //收货地址
        StringBuilder unLoad = new StringBuilder();
        if (StringUtils.isNotBlank(source.getUnloadWarehouse())) {
            unLoad.append("【" + source.getUnloadWarehouse() + "】");
        }
        unLoad.append(Optional.ofNullable(source.getUnloadProvinceName()).orElse("")).
                append(Optional.ofNullable(source.getUnloadCityName()).orElse("")).
                append(Optional.ofNullable(source.getUnloadAreaName()).orElse(""));
        destination.setUnloadDetailAddress(unLoad.toString());
    }
}
