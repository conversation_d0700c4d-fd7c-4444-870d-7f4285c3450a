package com.logistics.tms.api.feign.terminalcustomeraddress.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2022/1/4 13:11
 */
@Data
public class SearchTerminalCustomerAddressListResponseModel {
    @ApiModelProperty("终端客户地址id")
    private Long terminalCustomerAddressId;

    @ApiModelProperty("仓库")
    private String collectWarehouse;

    @ApiModelProperty("省份名字")
    private String collectProvinceName;

    @ApiModelProperty("城市名字")
    private String collectCityName;

    @ApiModelProperty("县区名字")
    private String collectAreaName;

    @ApiModelProperty("详细地址")
    private String collectDetailAddress;

    @ApiModelProperty("联系人姓名")
    private String collectContactName;

    @ApiModelProperty("联系方式")
    private String collectContactMobile;

    @ApiModelProperty("地图链接")
    private String mapLinkPath;

    @ApiModelProperty("客户情况")
    private String customerSituation;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("最新操作人")
    private String lastModifiedBy;

    @ApiModelProperty("最新操作时间")
    private Date lastModifiedTime;
}
