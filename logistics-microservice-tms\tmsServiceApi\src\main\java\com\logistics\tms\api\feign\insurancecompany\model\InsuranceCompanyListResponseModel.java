package com.logistics.tms.api.feign.insurancecompany.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/5/29 14:25
 */
@Data
public class InsuranceCompanyListResponseModel {
    @ApiModelProperty("保险公司ID")
    private Long insuranceCompanyId;
    @ApiModelProperty("状态: 0禁用 1启用")
    private Integer enabled;
    @ApiModelProperty("保险公司名称")
    private String companyName = "";
    @ApiModelProperty("添加人名称")
    private String addUserName = "";
    @ApiModelProperty("修改人")
    private String lastModifiedBy;
    @ApiModelProperty("修改时间")
    private Date lastModifiedTime;
    @ApiModelProperty("备注")
    private String remark;
}
