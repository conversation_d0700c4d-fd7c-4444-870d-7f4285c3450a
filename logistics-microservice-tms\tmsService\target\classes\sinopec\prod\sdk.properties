gateway=gmssl.crypto.sinopec.com
gatewayPort=445
#apiIp=http://**************
#apiIp=http://**************
apiIp=https://api.crypto.sinopec.com
chironPort=10006
dsvsPort=10700
sniopecCertPort=10909
appId=APP_F82BF73852244F358C588B8AC427D244
deviceId=DEV_D32081EA1AAF408C85547804C43256CB
#创建主密钥url
#cmkServerApi=/chiron/system/createKey
cmkServerApi=/chiron/v2/system/createKey
#生成数据密钥
#generateDataKeyServerApi=/chiron/system/generateDataKeyByPubKey
generateDataKeyServerApi=/chiron/v1/system/generateDataKeyByPubKey
#数据密钥解密
#decryptDataKeyServerApi=/chiron/system/decryptDataKeyByPubKey
decryptDataKeyServerApi=/chiron/v1/system/decryptDataKeyByPubKey
#主密钥加密数据
#cmkEncryptServerApi=/chiron/system/encrypt
cmkEncryptServerApi=/chiron/v2/system/encrypt
#主密钥解密数据
#cmkDecryptServerApi=/chiron/system/decrypt
cmkDecryptServerApi=/chiron/v2/system/decrypt
#生成非对称密钥
#generateKeyPairServerApi=127.0.0.1
generateKeyPairServerApi=/chiron/v2/system/generateKeyPair
#密钥对生成
#generateCaKeyPairServerApi=/chiron/system/generateCaEncKeyPair
generateCaKeyPairServerApi=/chiron/v1/system/generateCaEncKeyPair
#使用私钥解密数据
#prikeyDecryptServerApi=/chiron/system/priKeyDec
prikeyDecryptServerApi=/chiron/v2/system/priKeyDec
#使用私钥签名
#prikeySignServerApi=/chiron/v1/system/signature
prikeySignServerApi=/chiron/v2/system/signature
#生成设备主密钥
#generateDeviceKeyServerApi=/chiron/system/generateDeviceKeyByPubKey
generateDeviceKeyServerApi=/chiron/v1/system/generateDeviceKeyByPubKey
#生成设备工作密钥
#generateDeviceWorkKeyServerApi=/chiron/system/generateWorkKeyByPubKey
generateDeviceWorkKeyServerApi=/chiron/v1/system/generateWorkKeyByPubKey
signDataServerApi=127.0.0.1
verifyDataSignServerApi=127.0.0.1
signDigestServerApi=127.0.0.1
verifyDigestSignServerApi=127.0.0.1
verifyDigestServerApi=127.0.0.1
#p7签名
#p7OriginSignServerApi=/dsvs/pkcs7/ext/signData
p7OriginSignServerApi=/dsvs/v1/pkcs7/ext/signData
#p7验签
#p7OriginVerifySignServerApi=/dsvs/v1/pkcs7/verifyDataSign
p7OriginVerifySignServerApi=/dsvs/v1/pkcs7/verifyDataSign

#p7DigestSignServerApi=/dsvs/v1/pkcs7/ext/signDigest
p7DigestSignServerApi=/dsvs/v1/pkcs7/ext/signDigest

#p7DigestVerifySignServerApi=/dsvs/v1/pkcs7/verifyDigestSign
p7DigestVerifySignServerApi=/dsvs/v1/pkcs7/verifyDigestSign
#数字信封加密
#encodeEnvelopeServerApi=/dsvs/envelope/encode
encodeEnvelopeServerApi=/dsvs/v1/envelope/encode
#数字信封解密
decodeEnvelopeServerApi=/dsvs/v1/envelope/decode
#decodeEnvelopeServerApi=/dsvs/v1/envelope/decode
#P10生成
generateP10ServerApi=/v1/hostCert/generateP10
#在线申请设备证书
deviceCert=/cloudsign/v1/jidacertservice/deviceCertApply