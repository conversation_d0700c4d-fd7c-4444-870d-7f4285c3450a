package com.logistics.tms.api.feign.personalaccidentinsurance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/5/31 9:13
 */
@Data
public class ImportPersonalAccidentInsuranceRequestModel {
    private List<ImportPersonalAccidentInsuranceListRequestModel> importList;
    @ApiModelProperty("数据错误的数量")
    private Integer errorNumber;
}
