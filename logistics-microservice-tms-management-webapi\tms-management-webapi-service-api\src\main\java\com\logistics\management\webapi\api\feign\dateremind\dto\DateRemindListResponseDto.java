package com.logistics.management.webapi.api.feign.dateremind.dto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: sj
 * @Date: 2019/5/31 8:54
 */
@Data
public class DateRemindListResponseDto implements Serializable{
    @ApiModelProperty("日期提醒ID")
    private String dateRemindId;
    @ApiModelProperty("日期提醒名称")
    private String dateName = "";
    @ApiModelProperty("是否提醒：0 否，1 是")
    private String ifRemind = "";
    @ApiModelProperty("是否提醒文本")
    private String ifRemindLabel = "";
    @ApiModelProperty("提醒天数")
    private String remindDays = "";
    @ApiModelProperty("提醒天数文本")
    private String remindDaysLabel = "";
    @ApiModelProperty("添加人")
    private String addUserName = "";
    @ApiModelProperty("最后修改人")
    private String lastModifiedBy = "";
    @ApiModelProperty("最后修改日期")
    private String lastModifiedTime = "";
    @ApiModelProperty("备注")
    private String remark = "";
}
