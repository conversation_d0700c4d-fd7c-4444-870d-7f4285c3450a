package com.logistics.management.webapi.controller.oilfilled.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * @author: wjf
 * @date: 2019/12/23 16:12
 */
@Data
public class AddOrModifyOilRefundRequestDto {
    @ApiModelProperty(value = "充油ID,修改时候传入，新增不传")
    private String oilFilledId;
    @ApiModelProperty(value = "充油方式：1 充油卡，2 加油车")
    @NotBlank(message = "充油方式不能为空")
    private String oilFilledType;
    @ApiModelProperty(value = "车牌号Id")
    @NotBlank(message = "车牌号ID不能为空")
    private String vehicleId;
    @ApiModelProperty(value = "车牌号号码")
    private String vehicleNo;
    @ApiModelProperty(value = "司机ID")
    @NotBlank(message = "司机ID不能为空")
    private String staffId;
    @ApiModelProperty(value = "司机")
    private String name;
    @ApiModelProperty(value = "退款金额")
    @NotBlank(message = "退款金额不能为空")
    @Pattern(regexp = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,2})$)",message = "请维护退款金额，0<退款金额≤100000，且保留两位小数")
    @DecimalMin(value = "0.01",message = "请维护退款金额，0<退款金额≤100000，且保留两位小数")
    @DecimalMax(value = "100000.00",message = "请维护退款金额，0<退款金额≤100000，且保留两位小数")
    private String oilFilledFee;
    @ApiModelProperty(value = "退款时间")
    @NotBlank(message = "退款时间不能为空")
    @Pattern(regexp = "[\\d]{4}-[\\d]{1,2}-[\\d]{1,2}",message = "请维护正确的退款时间")
    private String oilFilledDate;
    @ApiModelProperty(value = "退款原因类型：0 其他，10 丢失副卡，20 车辆过户，30 车辆报废，40 充油充错")
    @NotBlank(message = "请选择退款原因")
    private String refundReasonType;
    @ApiModelProperty(value = "退款原因,当充退款原因为丢失副卡和其他时候传")
    private String refundReason;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "附件")
    private String refundFile;
}
