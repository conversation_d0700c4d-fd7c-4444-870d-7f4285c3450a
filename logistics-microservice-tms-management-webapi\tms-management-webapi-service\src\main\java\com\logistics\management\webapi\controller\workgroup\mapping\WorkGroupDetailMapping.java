package com.logistics.management.webapi.controller.workgroup.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.workgroup.response.WorkGroupDetailResponseModel;
import com.logistics.management.webapi.controller.workgroup.response.WorkGroupDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2024/1/29 9:55
 */
public class WorkGroupDetailMapping extends MapperMapping<WorkGroupDetailResponseModel, WorkGroupDetailResponseDto> {
    @Override
    public void configure() {
        WorkGroupDetailResponseModel source = getSource();
        WorkGroupDetailResponseDto destination = getDestination();

        //匹配地点转换
        destination.setMatchingLocationList(Arrays.stream(source.getMatchingLocation().split(CommonConstant.COMMA)).collect(Collectors.toList()));
    }
}
