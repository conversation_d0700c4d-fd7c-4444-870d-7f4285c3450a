package com.logistics.tms.mapper;

import com.logistics.tms.biz.shippingfreight.model.ShippingFreightRuleCrossPointConditionModel;
import com.logistics.tms.entity.TCarrierOrderOrderRel;
import com.logistics.tms.entity.TShippingFreightRuleCrossPoint;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/09/19
*/
@Mapper
public interface TShippingFreightRuleCrossPointMapper extends BaseMapper<TShippingFreightRuleCrossPoint> {
    int deleteByPrimaryKey(Long id);

    int insert(TShippingFreightRuleCrossPoint record);

    int insertSelective(TShippingFreightRuleCrossPoint record);

    TShippingFreightRuleCrossPoint selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TShippingFreightRuleCrossPoint record);

    int updateByPrimaryKey(TShippingFreightRuleCrossPoint record);

    List<TShippingFreightRuleCrossPoint> selectListByCondition(@Param("param1") ShippingFreightRuleCrossPointConditionModel crossPointConditionModel);


    int batchUpdateByAddressId(@Param("list")List<TShippingFreightRuleCrossPoint> list);



    int batchInsert(@Param("list")List<TShippingFreightRuleCrossPoint> list);


}