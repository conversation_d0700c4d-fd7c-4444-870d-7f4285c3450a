package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.controller.region.request.SearchRegionDetailRequestModel;
import com.logistics.tms.controller.region.response.SearchRegionDetailResponseModel;
import com.logistics.tms.entity.TRegionItem;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TRegionItemMapper extends BaseMapper<TRegionItem> {

    List<TRegionItem> getByNotRegionId(@Param("regionId") Long regionId);

    List<TRegionItem> selectByRegionId(@Param("regionId") Long regionId);

    int batchInsert(@Param("list") List<TRegionItem> list);

    int batchUpdate(@Param("list") List<TRegionItem> list);

    List<SearchRegionDetailResponseModel> searchDetailList(@Param("params") SearchRegionDetailRequestModel requestModel);
}