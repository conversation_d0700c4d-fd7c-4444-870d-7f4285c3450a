package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.controller.carrierorder.response.ExportCarrierOrderOtherFeeForLeYiResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CarrierOrderOtherFeeTypeEnum;
import com.logistics.management.webapi.base.enums.EntrustTypeEnum;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.management.webapi.client.carrierorder.response.CarrierOrderOtherFeeItemModel;
import com.logistics.management.webapi.client.carrierorder.response.SearchCarrierOrderListForLeYiResponseModel;
import com.logistics.management.webapi.client.carrierorder.response.SearchCarrierOrderListGoodsInfoModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * @author: wjf
 * @date: 2022/9/16 13:55
 */
public class ExportCarrierOrderOtherFeeForLeYiMapping extends MapperMapping<SearchCarrierOrderListForLeYiResponseModel, ExportCarrierOrderOtherFeeForLeYiResponseDto> {
    @Override
    public void configure() {
        SearchCarrierOrderListForLeYiResponseModel source = getSource();
        ExportCarrierOrderOtherFeeForLeYiResponseDto destination = getDestination();

        //司机
        destination.setDriver(source.getDriverName() + " " + source.getDriverMobile());

        //拼接品名
        if (ListUtils.isNotEmpty(source.getGoodsInfoList())) {
            StringBuilder goodsName = new StringBuilder();
            for (int index = 0; index < source.getGoodsInfoList().size(); index++) {
                SearchCarrierOrderListGoodsInfoModel tmpGoodsModel = source.getGoodsInfoList().get(index);
                if (source.getGoodsUnit().equals(GoodsUnitEnum.BY_VOLUME.getKey())) {
                    tmpGoodsModel.setGoodsSize(tmpGoodsModel.getGoodsSize() + " " + tmpGoodsModel.getLength() + "*" + tmpGoodsModel.getWidth() + "*" + tmpGoodsModel.getHeight() + "mm");
                }
                if (index != 0) {
                    goodsName.append("/");
                }
                goodsName.append(tmpGoodsModel.getGoodsName());
            }
            destination.setGoodsName(goodsName.toString());
        }

        //需求类型
        destination.setEntrustType(EntrustTypeEnum.getEnum(source.getEntrustType()).getValue());

        //展示车主名称
        if (CommonConstant.INTEGER_ONE.equals(source.getCompanyCarrierType())){
            destination.setCarrierCompany(source.getCarrierCompany());
        }else if (CommonConstant.INTEGER_TWO.equals(source.getCompanyCarrierType())){
            destination.setCarrierCompany(source.getCarrierContactName() + " " + source.getCarrierContactMobile());
        }

        //货主
        String companyEntrustName = source.getEntrustCompany();
        if (EntrustTypeEnum.BOOKING.getKey().equals(source.getEntrustType())){
            companyEntrustName = CommonConstant.LEYI_POINTS_FOR_LOGISTICS;
        }
        destination.setEntrustCompany(companyEntrustName);

        //临时费用
        if (ListUtils.isNotEmpty(source.getOtherFee())) {
            //临时费用合计
            BigDecimal otherFeeTotal = CommonConstant.BIG_DECIMAL_ZERO;
            //短驳费
            BigDecimal shortBargeFee = CommonConstant.BIG_DECIMAL_ZERO;
            //保管费
            BigDecimal storageFee = CommonConstant.BIG_DECIMAL_ZERO;
            //装卸费
            BigDecimal handlingChangesFee = CommonConstant.BIG_DECIMAL_ZERO;
            //压车费
            BigDecimal crushingFee = CommonConstant.BIG_DECIMAL_ZERO;
            //质量处罚费
            BigDecimal qualityPenaltyFee = CommonConstant.BIG_DECIMAL_ZERO;
            //其他杂费
            BigDecimal otherFee = CommonConstant.BIG_DECIMAL_ZERO;

            //计算费用
            for (CarrierOrderOtherFeeItemModel model : source.getOtherFee()) {
                //临时费用合计
                otherFeeTotal = otherFeeTotal.add(model.getFeeAmount());

                if (CarrierOrderOtherFeeTypeEnum.SHORT_BARGE_FEE.getKey().equals(model.getFeeType())){
                    //短驳费
                    shortBargeFee = shortBargeFee.add(model.getFeeAmount());
                }else if (CarrierOrderOtherFeeTypeEnum.STORAGE_FEE.getKey().equals(model.getFeeType())){
                    //保管费
                    storageFee = storageFee.add(model.getFeeAmount());
                }else if (CarrierOrderOtherFeeTypeEnum.HANDLING_CHARGES_FEE.getKey().equals(model.getFeeType())){
                    //装卸费
                    handlingChangesFee = handlingChangesFee.add(model.getFeeAmount());
                }else if (CarrierOrderOtherFeeTypeEnum.CRUSHING_FEE.getKey().equals(model.getFeeType())){
                    //压车费
                    crushingFee = crushingFee.add(model.getFeeAmount());
                }else if (CarrierOrderOtherFeeTypeEnum.QUALITY_PENALTY_FEE.getKey().equals(model.getFeeType())){
                    //质量处罚费
                    qualityPenaltyFee = qualityPenaltyFee.add(model.getFeeAmount());
                }else if (CarrierOrderOtherFeeTypeEnum.OTHER_FEE.getKey().equals(model.getFeeType())){
                    //其他杂费
                    otherFee = otherFee.add(model.getFeeAmount());
                }
            }

            //临时费用
            destination.setTemporaryFee(otherFeeTotal.setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
            destination.setOtherFeeFreight(otherFeeTotal.multiply(source.getStatementOtherFeeTaxPoint()).setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
            destination.setOtherFeeTotal(otherFeeTotal
                    .add(new BigDecimal(destination.getOtherFeeFreight())).setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
            //临时费用赋值
            destination.setShortBargeFee(ConverterUtils.toString(shortBargeFee.setScale(2,BigDecimal.ROUND_HALF_UP)));
            destination.setStorageFee(ConverterUtils.toString(storageFee.setScale(2,BigDecimal.ROUND_HALF_UP)));
            destination.setHandlingChangesFee(ConverterUtils.toString(handlingChangesFee.setScale(2,BigDecimal.ROUND_HALF_UP)));
            destination.setCrushingFee(ConverterUtils.toString(crushingFee.setScale(2,BigDecimal.ROUND_HALF_UP)));
            destination.setQualityPenaltyFee(ConverterUtils.toString(qualityPenaltyFee.setScale(2,BigDecimal.ROUND_HALF_UP)));
            destination.setOtherFee(ConverterUtils.toString(otherFee.setScale(2,BigDecimal.ROUND_HALF_UP)));
        }
    }
}
