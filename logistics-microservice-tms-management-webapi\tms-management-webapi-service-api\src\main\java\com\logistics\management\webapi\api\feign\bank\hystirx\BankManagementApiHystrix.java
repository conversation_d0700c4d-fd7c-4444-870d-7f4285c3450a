package com.logistics.management.webapi.api.feign.bank.hystirx;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.bank.BankManagementApi;
import com.logistics.management.webapi.api.feign.bank.dto.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/7/10 13:54
 */
@Component
public class BankManagementApiHystrix implements BankManagementApi {
    @Override
    public Result<PageInfo<SearchBankResponseDto>> searchBankList(SearchBankRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> saveOrModifyBank(SaveOrModifyBankRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<BankDetailResponseDto> getDetail(BankDetailRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> enableOrDisable(EnableBankRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void export(SearchBankRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }

    @Override
    public Result<ImportBankResponseDto> importBank(MultipartFile file, HttpServletRequest request) {
        return Result.timeout();
    }

    @Override
    public Result<List<FuzzyQueryBankListResponseDto>> fuzzyQueryBank(FuzzyQueryBankRequestDto requestDto) {
        return Result.timeout();
    }
}
