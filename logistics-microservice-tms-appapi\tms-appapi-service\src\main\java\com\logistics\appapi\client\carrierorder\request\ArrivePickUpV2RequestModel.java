package com.logistics.appapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/3/17
 */
@Data
public class ArrivePickUpV2RequestModel {

	@ApiModelProperty(value = "运单Id")
	private Long carrierOrderId;

	@ApiModelProperty("触达定位经度")
	private String reachLongitude;

	@ApiModelProperty("触达定位纬度")
	private String reachLatitude;

	@ApiModelProperty(value = "触达详细地址")
	private String reachAddressDetail;

	@ApiModelProperty(value = "触达抬头")
	private String terminalHead;

	@ApiModelProperty(value = "触达地址备注")
	private String reachAddressRemark;

	@ApiModelProperty(value = "触达联系人")
	private String reachContactor;

	@ApiModelProperty(value = "触达联系方式")
	private String reachTelephone;

	@ApiModelProperty(value = "联系人校验: 0、无误 1、有误")
	private Integer checkReachContact;

	@ApiModelProperty(value = "门头图片临时路径")
	private List<String> terminalTmpUrl;
}
