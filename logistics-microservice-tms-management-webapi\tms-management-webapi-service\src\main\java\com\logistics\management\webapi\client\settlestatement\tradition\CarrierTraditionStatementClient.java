package com.logistics.management.webapi.client.settlestatement.tradition;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.settlestatement.tradition.hystrix.CarrierTraditionStatementClientHystrix;
import com.logistics.management.webapi.client.settlestatement.tradition.request.*;
import com.logistics.management.webapi.client.settlestatement.tradition.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/carrierStatementManage/tradition",
        fallback = CarrierTraditionStatementClientHystrix.class)
public interface CarrierTraditionStatementClient {

    @ApiOperation(value = "待对账运单列表")
    @PostMapping(value = "/waitSettleStatementList")
    Result<PageInfo<TraditionWaitSettleStatementListResponseModel>> waitSettleStatementList(@RequestBody TraditionWaitSettleStatementListRequestModel requestModel);

    @ApiOperation(value = "导出待对账运单列表")
    @PostMapping(value = "/exportWaitSettleStatementList")
    Result<PageInfo<TraditionWaitSettleStatementListResponseModel>> exportWaitSettleStatementList(@RequestBody TraditionWaitSettleStatementListRequestModel requestModel);

    @ApiOperation(value = "生成对账单")
    @PostMapping(value = "/createSettleStatement")
    Result<Boolean> createSettleStatement(@RequestBody TraditionCreateSettleStatementRequestModel requestModel);

    @ApiOperation(value = "查询车主税点")
    @PostMapping(value = "/queryTaxPoint")
    Result<TraditionCarrierTaxPointResponseModel> queryTaxPoint(@RequestBody TraditionCarrierTaxPointRequestModel requestModel);

    @ApiOperation(value = "对账单列表")
    @PostMapping(value = "/settleStatementList")
    Result<PageInfo<CarrierTraditionStatementListResponseModel>> settleStatementList(@RequestBody CarrierTraditionStatementListRequestModel requestModel);

    @ApiOperation(value = "关联运单号")
    @PostMapping(value = "/associationCarrierOrder")
    Result<TraditionAssociationCarrierOrderResponseModel> associationCarrierOrder(@RequestBody TraditionAssociationCarrierOrderRequestModel requestModel);

    @ApiOperation(value = "编辑对账月份")
    @PostMapping(value = "/modifySettleStatementMonth")
    Result<Boolean> modifySettleStatementMonth(@RequestBody ModifyTraditionStatementMonthRequestModel requestModel);

    @ApiOperation(value = "编辑结算主体")
    @PostMapping(value = "/modifyPlatformCompany")
    Result<Boolean> modifyPlatformCompany(@RequestBody TraditionModifyPlatformCompanyRequestModel requestModel);

    @ApiOperation(value = "修改对账单费点")
    @PostMapping(value = "/modifyTaxPoint")
    Result<Boolean> modifyTaxPoint(@RequestBody TraditionCarrierModifyTaxPointRequestModel requestModel);

    @ApiOperation(value = "修改对账单名称")
    @PostMapping(value = "/renameStatement")
    Result<Boolean> renameStatement(@RequestBody EditTraditionStatementNameRequestModel requestModel);

    @ApiOperation(value = "差异调整-回显")
    @PostMapping(value = "/queryAdjustCost")
    Result<TraditionCarrierAdjustCostResponseModel> queryAdjustCost(@RequestBody TraditionCarrierQueryAdjustCostRequestModel requestModel);

    @ApiOperation(value = "差异调整-发起")
    @PostMapping(value = "/AdjustCost")
    Result<Boolean> adjustCost(@RequestBody TraditionCarrierAdjustRequestModel requestModel);

    @ApiOperation(value = "申请开票")
    @PostMapping(value = "/applyInvoicing")
    Result<Boolean> applyInvoicing(@RequestBody TraditionSettleStatementApplyInvoicingRequestModel requestModel);

    @ApiOperation(value = "撤销对账单")
    @PostMapping(value = "/cancel")
    Result<Boolean> cancel(@RequestBody TraditionStatementCancelRequestModel requestModel);

    @ApiOperation(value = "对账单归档列表")
    @PostMapping(value = "/statementArchiveList")
    Result<PageInfo<TraditionStatementArchiveListResponseModel>> statementArchiveList(@RequestBody TraditionStatementArchiveListRequestModel requestModel);

    @ApiOperation(value = "对账单归档/编辑")
    @PostMapping(value = "/statementArchive")
    Result<Boolean> statementArchive(@RequestBody TraditionStatementArchiveRequestModel requestModel);

    @ApiOperation(value = "查看归档图片")
    @PostMapping(value = "/archiveTicketList")
    Result<List<String>> archiveTicketList(@RequestBody TraditionStatementArchiveTicketListRequestModel requestModel);

    @ApiOperation(value = "查看归档详情")
    @PostMapping(value = "/statementArchiveDetail")
    Result<TraditionStatementArchiveDetailResponseModel> statementArchiveDetail(@RequestBody TraditionStatementArchiveDetailRequestModel requestModel);

    @ApiOperation(value = "查询对账单下待归档运单")
    @PostMapping(value = "/statementWaitArchiveList")
    Result<List<TraditionStatementWaitArchiveListResponseModel>> statementWaitArchiveList(@RequestBody TraditionStatementWaitArchiveListRequestModel requestModel);

    @ApiOperation(value = "对账单详情-提交")
    @PostMapping(value = "/applicationCheck")
    Result<Boolean> submitSettleStatement(@RequestBody CarrierTraditionStatementIdRequestModel requestModel);

    @ApiOperation(value = "对账单详情-审核/驳回")
    @PostMapping(value = "/auditOrReject")
    Result<Boolean> auditOrReject(@RequestBody ChangeTraditionStatementStatsRequestModel requestModel);

    @ApiOperation(value = "对账单详情-合计")
    @PostMapping(value = "/settleStatementDetailTotal")
    Result<CarrierTraditionStatementDetailTotalResponseModel> settleStatementDetailTotal(@RequestBody CarrierTraditionStatementIdRequestModel requestModel);

    @ApiOperation(value = "对账单详情-对账单运单列表")
    @PostMapping(value = "/settleStatementDetailList")
    Result<PageInfo<CarrierTraditionStatementDetailListResponseModel>> settleStatementDetailList(@RequestBody CarrierTraditionStatementDetailListRequestModel requestModel);

    @ApiOperation(value = "导出对账单详情-对账单运单列表")
    @PostMapping(value = "/exportSettleStatementDetailList")
    Result<PageInfo<CarrierTraditionStatementDetailListResponseModel>> exportSettleStatementDetailList(@RequestBody CarrierTraditionStatementDetailListRequestModel requestModel);

    @ApiOperation(value = "对账单详情-查询添加运单列表")
    @PostMapping(value = "/addCarrierOrderList")
    Result<PageInfo<TraditionWaitSettleStatementListResponseModel>> addCarrierOrderList(@RequestBody TraditionAddCarrierOrderListRequestModel requestModel);

    @ApiOperation(value = "对账单详情-添加运单")
    @PostMapping(value = "/addCarrierOrder")
    Result<Boolean> addCarrierOrderConfirm(@RequestBody TraditionAddCarrierOrderConfirmRequestModel requestModel);

    @ApiOperation(value = "对账单详情-撤销运单")
    @PostMapping(value = "/cancelCarrierOrder")
    Result<Boolean> cancelCarrierOrder(@RequestBody TraditionUndoCarrierOrderRequestModel requestModel);
}
