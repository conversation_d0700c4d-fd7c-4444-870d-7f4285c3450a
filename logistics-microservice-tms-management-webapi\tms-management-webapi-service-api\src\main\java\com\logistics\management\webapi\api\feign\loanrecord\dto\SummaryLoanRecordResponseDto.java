package com.logistics.management.webapi.api.feign.loanrecord.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/9/30 9:38
 */
@Data
public class SummaryLoanRecordResponseDto {
    @ApiModelProperty("待结算")
    private String waitCount = "0";
    @ApiModelProperty("部分结算")
    private String partCount = "0";
    @ApiModelProperty("已结算")
    private String hasCount ="0";
    @ApiModelProperty("所有数量")
    private String allCount = "0";
}
