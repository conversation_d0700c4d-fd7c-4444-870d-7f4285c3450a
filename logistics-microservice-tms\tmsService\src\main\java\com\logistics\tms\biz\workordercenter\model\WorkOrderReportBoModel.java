package com.logistics.tms.biz.workordercenter.model;

import com.logistics.tms.entity.TWorkOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class WorkOrderReportBoModel {

    private TWorkOrder entity;

    @ApiModelProperty(value = "业务单号")
    private String busCode;

    @ApiModelProperty(value = "提报人联系方式")
    private String reportByPhone;

    @ApiModelProperty(value = "客户名称")
    private String customer;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNumber;

    @ApiModelProperty(value = "委托人")
    private String client;
}
