package com.logistics.tms.biz.routeenquiry;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.driverpayee.model.ViewLogResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.routeenquiry.model.RouteEnquiryAddressCountModel;
import com.logistics.tms.biz.routeenquiry.model.RouteEnquiryQuotedCompanyCountModel;
import com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.tms.controller.routeenquiry.request.*;
import com.logistics.tms.controller.routeenquiry.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.MapUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2024/7/9 14:16
 */
@Service
public class RouteEnquiryBiz {

    @Resource
    private TRouteEnquiryMapper tRouteEnquiryMapper;
    @Resource
    private TRouteEnquiryAddressMapper tRouteEnquiryAddressMapper;
    @Resource
    private TRouteEnquiryCompanyMapper tRouteEnquiryCompanyMapper;
    @Resource
    private TRouteEnquiryAttachmentMapper tRouteEnquiryAttachmentMapper;
    @Resource
    private TRouteEnquiryAddressQuoteMapper tRouteEnquiryAddressQuoteMapper;
    @Resource
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private TOperateLogsMapper tOperateLogsMapper;

    /*
     * 后台
     */
    /**
     * 查询列表
     * @param requestModel
     * @return
     */
    public PageInfo<SearchRouteEnquiryListResponseModel> searchList(SearchRouteEnquiryListRequestModel requestModel){
        requestModel.enablePaging();
        List<SearchRouteEnquiryListResponseModel> list = tRouteEnquiryMapper.searchList(requestModel);
        PageInfo<SearchRouteEnquiryListResponseModel> pageInfo = new PageInfo<>(list);
        if (ListUtils.isEmpty(list)){
            return pageInfo;
        }

        List<Long> routeEnquiryIdList = list.stream().map(SearchRouteEnquiryListResponseModel::getRouteEnquiryId).collect(Collectors.toList());

        //查询路线数
        Map<Long, Integer> addressCountMap = tRouteEnquiryAddressMapper.getAddressCount(routeEnquiryIdList).stream().collect(Collectors.toMap(RouteEnquiryAddressCountModel::getRouteEnquiryId, RouteEnquiryAddressCountModel::getAddressCount));

        //查询报价承运商数
        Map<Long, Integer> quotedCompanyCountMap = tRouteEnquiryCompanyMapper.getQuotedCompanyCount(routeEnquiryIdList).stream().collect(Collectors.toMap(RouteEnquiryQuotedCompanyCountModel::getRouteEnquiryId, RouteEnquiryQuotedCompanyCountModel::getQuotedCarrierCount));

        //拼接数据
        for (SearchRouteEnquiryListResponseModel responseModel : list) {
            responseModel.setAddressCount(addressCountMap.get(responseModel.getRouteEnquiryId()));
            responseModel.setQuotedCarrierCount(ConverterUtils.toInt(quotedCompanyCountMap.get(responseModel.getRouteEnquiryId()), CommonConstant.INTEGER_ZERO));//可能还没有承运商报价
        }

        return pageInfo;
    }

    /**
     * 创建
     * @param requestModel
     * @return
     */
    @Transactional
    public void create(CreateRouteEnquiryRequestModel requestModel){
        //校验车主
        List<FuzzySearchCompanyCarrierResponseModel> dbCompanyCarrierList = tCompanyCarrierMapper.selectCompanyCarrierInfoByIds(requestModel.getCompanyCarrierIdList());
        if (ListUtils.isEmpty(dbCompanyCarrierList)){
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }

        String userName = BaseContextHandler.getUserName();

        //创建路线询价单
        TRouteEnquiry addRouteEnquiry = new TRouteEnquiry();
        addRouteEnquiry.setOrderCode(commonBiz.getBusinessTypeCode(BusinessCodeTypeEnum.ROUTE_ENQUIRY_CODE, "", userName));
        addRouteEnquiry.setStatus(RouteEnquiryStatusEnum.WAIT_BUSINESS_AUDIT.getKey());
        addRouteEnquiry.setGoodsName(requestModel.getGoodsName());
        addRouteEnquiry.setQuoteStartTime(requestModel.getQuoteStartTime());
        addRouteEnquiry.setQuoteEndTime(requestModel.getQuoteEndTime());
        addRouteEnquiry.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityAdd(addRouteEnquiry, userName);
        tRouteEnquiryMapper.insertSelectiveBackKey(addRouteEnquiry);

        //新增路线询价单-地址
        TRouteEnquiryAddress addRouteEnquiryAddress;
        List<TRouteEnquiryAddress> addRouteEnquiryAddressList = new ArrayList<>();
        for (CreateRouteEnquiryAddressListRequestModel model : requestModel.getAddressList()) {
            addRouteEnquiryAddress = MapperUtils.mapper(model, TRouteEnquiryAddress.class);
            addRouteEnquiryAddress.setRouteEnquiryId(addRouteEnquiry.getId());
            commonBiz.setBaseEntityAdd(addRouteEnquiryAddress, userName);
            addRouteEnquiryAddressList.add(addRouteEnquiryAddress);
        }
        if (ListUtils.isNotEmpty(addRouteEnquiryAddressList)){
            tRouteEnquiryAddressMapper.batchInsert(addRouteEnquiryAddressList);
        }

        //新增路线询价单-车主
        TRouteEnquiryCompany addRouteEnquiryCompany;
        List<TRouteEnquiryCompany> addRouteEnquiryCompanyList = new ArrayList<>();
        for (FuzzySearchCompanyCarrierResponseModel model : dbCompanyCarrierList) {
            addRouteEnquiryCompany = new TRouteEnquiryCompany();
            addRouteEnquiryCompany.setRouteEnquiryId(addRouteEnquiry.getId());
            addRouteEnquiryCompany.setCompanyCarrierType(model.getCompanyType());
            addRouteEnquiryCompany.setCompanyCarrierId(model.getCompanyId());
            addRouteEnquiryCompany.setCompanyCarrierName(model.getCompanyName());
            addRouteEnquiryCompany.setCarrierContactId(model.getContactId());
            addRouteEnquiryCompany.setCarrierContactName(model.getContactName());
            addRouteEnquiryCompany.setCarrierContactPhone(model.getContactPhone());
            commonBiz.setBaseEntityAdd(addRouteEnquiryCompany, userName);
            addRouteEnquiryCompanyList.add(addRouteEnquiryCompany);
        }
        if (ListUtils.isNotEmpty(addRouteEnquiryCompanyList)){
            tRouteEnquiryCompanyMapper.batchInsertSelectiveEncrypt(addRouteEnquiryCompanyList);
        }

        //记录操作日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(addRouteEnquiry.getId(), OperateLogsOperateTypeEnum.ROUTE_ENQUIRY_ADD, null, userName);
        tOperateLogsMapper.insertSelective(tOperateLogs);
    }

    /**
     * 查询详情
     * @param requestModel
     * @return
     */
    public GetRouteEnquiryDetailResponseModel getDetail(GetRouteEnquiryDetailRequestModel requestModel){
        //查询路线询价单
        TRouteEnquiry dbRouteEnquiry = getTRouteEnquiry(requestModel.getRouteEnquiryId());

        //地址列表
        List<GetRouteEnquiryDetailAddressListResponseModel> addressList = tRouteEnquiryAddressMapper.getAddressByRouteEnquiryId(dbRouteEnquiry.getId());

        //车主报价记录
        List<RouteEnquiryQuoteListResponseModel> quoteList = tRouteEnquiryCompanyMapper.getQuotedCompanyByRouteEnquiryId(dbRouteEnquiry.getId());

        //操作记录
        List<ViewLogResponseModel> viewLogResponseModels = tOperateLogsMapper.selectLogsByCondition(OperateLogsObjectTypeEnum.ROUTE_ENQUIRY.getKey(), dbRouteEnquiry.getId(), null);

        //上传文件列表
        List<RouteEnquiryFileListResponseModel> fileList = tRouteEnquiryAttachmentMapper.getFileGroupTypeByRouteEnquiryId(dbRouteEnquiry.getId());

        GetRouteEnquiryDetailResponseModel detail = MapperUtils.mapper(dbRouteEnquiry, GetRouteEnquiryDetailResponseModel.class);
        detail.setRouteEnquiryId(dbRouteEnquiry.getId());
        detail.setAddressList(addressList);
        detail.setQuoteList(quoteList);
        detail.setOperateLogList(MapperUtils.mapper(viewLogResponseModels, RouteEnquiryOperateLogListResponseModel.class));
        detail.setFileList(fileList);
        return detail;
    }

    //查询路线询价单
    private TRouteEnquiry getTRouteEnquiry(Long routeEnquiryId){
        TRouteEnquiry dbRouteEnquiry = tRouteEnquiryMapper.selectByPrimaryKey(routeEnquiryId);
        if (dbRouteEnquiry == null || IfValidEnum.INVALID.getKey().equals(dbRouteEnquiry.getValid())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_EXIST);
        }
        return dbRouteEnquiry;
    }

    /**
     * 取消报价
     * @param requestModel
     * @return
     */
    @Transactional
    public void cancelQuote(GetRouteEnquiryDetailRequestModel requestModel){
        //查询路线询价单
        TRouteEnquiry dbRouteEnquiry = getTRouteEnquiry(requestModel.getRouteEnquiryId());

        //已取消的不能操作
        if (CommonConstant.INTEGER_ONE.equals(dbRouteEnquiry.getIfCancel())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }
        //仅【待业务审核】、【待车主确认】状态可操作
        if (!RouteEnquiryStatusEnum.WAIT_BUSINESS_AUDIT.getKey().equals(dbRouteEnquiry.getStatus())
                && !RouteEnquiryStatusEnum.WAIT_CARRIER_CONFIRM.getKey().equals(dbRouteEnquiry.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }

        //更新路线询价单状态
        TRouteEnquiry upRouteEnquiry = new TRouteEnquiry();
        upRouteEnquiry.setId(dbRouteEnquiry.getId());
        upRouteEnquiry.setIfCancel(CommonConstant.INTEGER_ONE);
        commonBiz.setBaseEntityModify(upRouteEnquiry, BaseContextHandler.getUserName());
        tRouteEnquiryMapper.updateByPrimaryKeySelective(upRouteEnquiry);

        //记录操作日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(dbRouteEnquiry.getId(), OperateLogsOperateTypeEnum.ROUTE_ENQUIRY_CANCEL, null, BaseContextHandler.getUserName());
        tOperateLogsMapper.insertSelective(tOperateLogs);
    }

    /**
     * 查询车主报价详情
     * @param requestModel
     * @return
     */
    public List<GetRouteEnquiryQuoteDetailResponseModel> getQuoteDetail(GetRouteEnquiryQuoteDetailRequestModel requestModel){
        return tRouteEnquiryAddressQuoteMapper.getQuoteDetail(requestModel.getRouteEnquiryCompanyId());
    }

    /**
     * 选择车主报价
     * @param requestModel
     * @return
     */
    @Transactional
    public void selectCarrierQuote(RouteEnquirySelectCarrierQuoteRequestModel requestModel){
        //查询车主报价信息
        TRouteEnquiryCompany dbRouteEnquiryCompany = tRouteEnquiryCompanyMapper.selectByPrimaryKeyDecrypt(requestModel.getRouteEnquiryCompanyId());
        if (dbRouteEnquiryCompany == null || !QuoteStatusEnum.UNSELECTED.getKey().equals(dbRouteEnquiryCompany.getQuoteStatus())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }
        List<TRouteEnquiryAddressQuote> dbRouteEnquiryAddressQuoteList = tRouteEnquiryAddressQuoteMapper.getByCompanyId(dbRouteEnquiryCompany.getId());
        if (ListUtils.isEmpty(dbRouteEnquiryAddressQuoteList)){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }

        //查询路线询价单
        TRouteEnquiry dbRouteEnquiry = getTRouteEnquiry(dbRouteEnquiryCompany.getRouteEnquiryId());

        //已取消的不能操作
        if (CommonConstant.INTEGER_ONE.equals(dbRouteEnquiry.getIfCancel())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }
        //仅【待业务审核】状态可操作
        if (!RouteEnquiryStatusEnum.WAIT_BUSINESS_AUDIT.getKey().equals(dbRouteEnquiry.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }

        String userName = BaseContextHandler.getUserName();

        //更新路线询价单状态
        TRouteEnquiry upRouteEnquiry = new TRouteEnquiry();
        upRouteEnquiry.setId(dbRouteEnquiry.getId());
        upRouteEnquiry.setStatus(RouteEnquiryStatusEnum.WAIT_CARRIER_CONFIRM.getKey());
        upRouteEnquiry.setContractCode(requestModel.getContractCode());
        upRouteEnquiry.setAuditorNameOne(userName);
        upRouteEnquiry.setAuditTimeOne(new Date());
        upRouteEnquiry.setAuditRemarkOne(requestModel.getRemark());
        commonBiz.setBaseEntityModify(upRouteEnquiry, userName);
        tRouteEnquiryMapper.updateByPrimaryKeySelective(upRouteEnquiry);

        //更新车主报价状态
        TRouteEnquiryCompany upRouteEnquiryCompany = new TRouteEnquiryCompany();
        upRouteEnquiryCompany.setId(dbRouteEnquiryCompany.getId());
        upRouteEnquiryCompany.setQuoteStatus(QuoteStatusEnum.SELECTED.getKey());
        commonBiz.setBaseEntityModify(upRouteEnquiryCompany, userName);
        tRouteEnquiryCompanyMapper.updateByPrimaryKeySelectiveEncrypt(upRouteEnquiryCompany);

        //将最终的报价信息回填到地址表上
        TRouteEnquiryAddress upRouteEnquiryAddress;
        List<TRouteEnquiryAddress> upRouteEnquiryAddressList = new ArrayList<>();
        for (TRouteEnquiryAddressQuote addressQuote : dbRouteEnquiryAddressQuoteList) {
            upRouteEnquiryAddress = new TRouteEnquiryAddress();
            upRouteEnquiryAddress.setId(addressQuote.getRouteEnquiryAddressId());
            upRouteEnquiryAddress.setDistance(addressQuote.getDistance());
            upRouteEnquiryAddress.setQuotePriceType(addressQuote.getQuotePriceType());
            upRouteEnquiryAddress.setQuotePrice(addressQuote.getQuotePrice());
            upRouteEnquiryAddress.setQuoteRemark(addressQuote.getQuoteRemark());
            commonBiz.setBaseEntityModify(upRouteEnquiryAddress, userName);
            upRouteEnquiryAddressList.add(upRouteEnquiryAddress);
        }
        if (ListUtils.isNotEmpty(upRouteEnquiryAddressList)){
            tRouteEnquiryAddressMapper.batchUpdateSelective(upRouteEnquiryAddressList);
        }

        //记录操作日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(dbRouteEnquiry.getId(), OperateLogsOperateTypeEnum.ROUTE_ENQUIRY_BUSINESS_AUDIT, requestModel.getRemark(), userName);
        tOperateLogsMapper.insertSelective(tOperateLogs);
    }

    /**
     * 结算审核
     * @param requestModel
     * @return
     */
    @Transactional
    public void settleAudit(RouteEnquirySettleAuditRequestModel requestModel){
        //查询路线询价单
        TRouteEnquiry dbRouteEnquiry = getTRouteEnquiry(requestModel.getRouteEnquiryId());

        //已取消的不能操作
        if (CommonConstant.INTEGER_ONE.equals(dbRouteEnquiry.getIfCancel())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }
        //仅【待结算审核】状态可操作
        if (!RouteEnquiryStatusEnum.WAIT_SETTLE_AUDIT.getKey().equals(dbRouteEnquiry.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }

        String userName = BaseContextHandler.getUserName();
        Integer status;
        String remark;
        //审核通过
        if (CommonConstant.ONE.equals(requestModel.getOperateType())){
            status = RouteEnquiryStatusEnum.COMPLETE_QUOTATION.getKey();
            remark = "【通过】";
        }
        //驳回
        else{
            status = RouteEnquiryStatusEnum.WAIT_CARRIER_CONFIRM.getKey();
            remark = "【驳回】";
        }
        if (StringUtils.isNotBlank(requestModel.getRemark())){
            remark = remark + requestModel.getRemark();
        }

        //更新路线询价单状态
        TRouteEnquiry upRouteEnquiry = new TRouteEnquiry();
        upRouteEnquiry.setId(dbRouteEnquiry.getId());
        upRouteEnquiry.setStatus(status);
        upRouteEnquiry.setAuditorNameTwo(userName);
        upRouteEnquiry.setAuditTimeTwo(new Date());
        upRouteEnquiry.setAuditRemarkTwo(requestModel.getRemark());
        commonBiz.setBaseEntityModify(upRouteEnquiry, userName);
        tRouteEnquiryMapper.updateByPrimaryKeySelective(upRouteEnquiry);

        //记录操作日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(dbRouteEnquiry.getId(), OperateLogsOperateTypeEnum.ROUTE_ENQUIRY_SETTLE_AUDIT, remark, userName);
        tOperateLogsMapper.insertSelective(tOperateLogs);
    }

    /**
     * 归档
     * @param requestModel
     * @return
     */
    @Transactional
    public void archive(RouteEnquiryArchiveRequestModel requestModel){
        //查询路线询价单
        TRouteEnquiry dbRouteEnquiry = getTRouteEnquiry(requestModel.getRouteEnquiryId());

        //已取消的不能操作
        if (CommonConstant.INTEGER_ONE.equals(dbRouteEnquiry.getIfCancel())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }
        //已归档的不能操作
        if (CommonConstant.INTEGER_ONE.equals(dbRouteEnquiry.getIfArchive())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }
        //仅【完成竞价】状态可操作
        if (!RouteEnquiryStatusEnum.COMPLETE_QUOTATION.getKey().equals(dbRouteEnquiry.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }

        String userName = BaseContextHandler.getUserName();

        //更新归档状态
        TRouteEnquiry upRouteEnquiry = new TRouteEnquiry();
        upRouteEnquiry.setId(dbRouteEnquiry.getId());
        upRouteEnquiry.setIfArchive(CommonConstant.INTEGER_ONE);
        commonBiz.setBaseEntityModify(upRouteEnquiry, userName);
        tRouteEnquiryMapper.updateByPrimaryKeySelective(upRouteEnquiry);

        //新增归档文件
        if (ListUtils.isNotEmpty(requestModel.getFileList())) {
            Date now = new Date();
            TRouteEnquiryAttachment addRouteEnquiryAttachment;
            List<TRouteEnquiryAttachment> addRouteEnquiryAttachmentList = new ArrayList<>();
            for (String path : requestModel.getFileList()) {
                addRouteEnquiryAttachment = new TRouteEnquiryAttachment();
                addRouteEnquiryAttachment.setRouteEnquiryId(dbRouteEnquiry.getId());
                addRouteEnquiryAttachment.setAttachmentType(RouteEnquiryAttachmentTypeEnum.ARCHIVE_FILE.getKey());
                addRouteEnquiryAttachment.setAttachmentPath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.ROUTE_ENQUIRY_ATTACHMENT.getKey(), dbRouteEnquiry.getOrderCode(), path));
                commonBiz.setBaseEntityAdd(addRouteEnquiryAttachment, userName, now);
                addRouteEnquiryAttachmentList.add(addRouteEnquiryAttachment);
            }
            if (ListUtils.isNotEmpty(addRouteEnquiryAttachmentList)) {
                tRouteEnquiryAttachmentMapper.batchInsert(addRouteEnquiryAttachmentList);
            }
        }

        //记录操作日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(dbRouteEnquiry.getId(), OperateLogsOperateTypeEnum.ROUTE_ENQUIRY_ARCHIVE, null, userName);
        tOperateLogsMapper.insertSelective(tOperateLogs);

    }

    /**
     * 查询汇总列表
     * @param requestModel
     * @return
     */
    public PageInfo<SearchRouteEnquirySummaryListResponseModel> searchSummaryList(SearchRouteEnquirySummaryListRequestModel requestModel){
        requestModel.enablePaging();
        List<SearchRouteEnquirySummaryListResponseModel> list = tRouteEnquiryAddressMapper.searchSummaryList(requestModel);
        return new PageInfo<>(list);
    }



    /*
     * 前台
     */
    /**
     * 前台-查询列表
     * @param requestModel
     * @return
     */
    public PageInfo<SearchRouteEnquiryListForWebResponseModel> searchListForWeb(SearchRouteEnquiryListForWebRequestModel requestModel){
        //判断车主是否存在
        Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        if (companyCarrierId == null || CommonConstant.LONG_ZERO.equals(companyCarrierId)) {
            return new PageInfo<>(new ArrayList<>());
        }

        //分页查询报价单
        requestModel.enablePaging();
        List<SearchRouteEnquiryListForWebResponseModel> responseModels = tRouteEnquiryMapper.searchListForWeb(requestModel, companyCarrierId);
        if (CollectionUtil.isEmpty(responseModels)) {
            return new PageInfo<>(new ArrayList<>());
        }
        PageInfo<SearchRouteEnquiryListForWebResponseModel> pageInfo = new PageInfo<>(responseModels);
        List<Long> routeEnquiryIds = responseModels.stream().map(SearchRouteEnquiryListForWebResponseModel::getRouteEnquiryId).collect(Collectors.toList());

        //查询地址数量
        List<RouteEnquiryAddressCountModel> addressCountModels = tRouteEnquiryAddressMapper.getAddressCount(routeEnquiryIds);
        Map<Long, Integer> addressCountMap = addressCountModels.stream().collect(Collectors.toMap(RouteEnquiryAddressCountModel::getRouteEnquiryId, RouteEnquiryAddressCountModel::getAddressCount));

        //组装数据返回
        for (SearchRouteEnquiryListForWebResponseModel responseModel : responseModels) {
            responseModel.setStatus(this.getRouteEnquiryStatus(responseModel.getStatus(), responseModel.getIfCancel(), responseModel.getQuoteStatus()).getKey());
            responseModel.setAddressCount(addressCountMap.get(responseModel.getRouteEnquiryId()));
        }

        return pageInfo;

    }

    /**
     * 前台-查询详情
     * @param requestModel
     * @return
     */
    public GetRouteEnquiryDetailForWebResponseModel getDetailForWeb(GetRouteEnquiryDetailRequestModel requestModel){
        //判断车主是否存在
        Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        if (companyCarrierId == null || CommonConstant.LONG_ZERO.equals(companyCarrierId)) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
        }

        //查询竞价单
        TRouteEnquiry tRouteEnquiry = tRouteEnquiryMapper.selectByPrimaryKey(requestModel.getRouteEnquiryId());
        if (tRouteEnquiry == null) {
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_EXIST);
        }
        //查询司机竞价
        TRouteEnquiryCompany tRouteEnquiryCompany = tRouteEnquiryCompanyMapper.getByRouteEnquiryIdAndCompanyId(tRouteEnquiry.getId(), companyCarrierId);
        if (tRouteEnquiryCompany == null) {
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_EXIST);
        }

        //查询竞价地址
        List<GetRouteEnquiryDetailAddressListResponseModel> addressList = tRouteEnquiryAddressMapper.getAddressForWebByRouteEnquiryId(tRouteEnquiry.getId(), tRouteEnquiryCompany.getId());

        //查询附件
        List<RouteEnquiryFileListResponseModel> fileList = tRouteEnquiryAttachmentMapper.getFileGroupTypeByRouteEnquiryId(tRouteEnquiry.getId());
        RouteEnquiryFileListResponseModel quotedFileModel = fileList.stream().filter(fileModel -> RouteEnquiryAttachmentTypeEnum.QUOTED_FILE.getKey().equals(fileModel.getAttachmentType())).findFirst().orElse(null);
        RouteEnquiryFileListResponseModel archiveFileModel = fileList.stream().filter(fileModel -> RouteEnquiryAttachmentTypeEnum.ARCHIVE_FILE.getKey().equals(fileModel.getAttachmentType())).findFirst().orElse(null);

        //组装数据返回
        GetRouteEnquiryDetailForWebResponseModel responseModel = MapperUtils.mapper(tRouteEnquiry, GetRouteEnquiryDetailForWebResponseModel.class);
        responseModel.setRouteEnquiryId(tRouteEnquiry.getId());
        responseModel.setAddressList(addressList);
        if (archiveFileModel != null) {
            responseModel.setArchivedFileList(archiveFileModel.getFileList());
        }
        if (quotedFileModel != null) {
            responseModel.setQuotationFileList(quotedFileModel.getFileList());
            responseModel.setUploadTime(quotedFileModel.getLastModifiedTime());
            responseModel.setUploadUserName(quotedFileModel.getLastModifiedBy());
        }
        responseModel.setQuoteOperator(tRouteEnquiryCompany.getQuoteOperator());
        responseModel.setQuoteTime(tRouteEnquiryCompany.getQuoteTime());
        responseModel.setStatus(this.getRouteEnquiryStatus(tRouteEnquiry.getStatus(), tRouteEnquiry.getIfCancel(), tRouteEnquiryCompany.getQuoteStatus()).getKey());

        return responseModel;
    }

    /**
     * 计算竞价单状态
     */
    private RouteEnquiryStatusEnum getRouteEnquiryStatus(Integer status,Integer ifCancel, Integer quoteStatus) {
        if (YesOrNoEnum.YES.getKey().equals(ifCancel)) {
            return RouteEnquiryStatusEnum.QUOTATION_CANCEL;
        }
        if (QuoteStatusEnum.CANCELLED.getKey().equals(quoteStatus)) {
            return RouteEnquiryStatusEnum.QUOTATION_CANCEL;
        }
        if ((QuoteStatusEnum.NO_QUOTE.getKey().equals(quoteStatus) || QuoteStatusEnum.UNSELECTED.getKey().equals(quoteStatus))
                && !RouteEnquiryStatusEnum.WAIT_BUSINESS_AUDIT.getKey().equals(status)) {
            return RouteEnquiryStatusEnum.QUOTATION_CANCEL;
        }
        return RouteEnquiryStatusEnum.getEnum(status);
    }

    /**
     * 前台-报价
     * @param requestModel
     * @return
     */
    @Transactional
    public void quote(RouteEnquiryQuoteRequestModel requestModel){
        //判断车主是否存在
        TCarrierContact dbCarrierContact = commonBiz.getLoginUserCompanyCarrier();
        if (dbCarrierContact == null){
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
        }
        Long companyCarrierId = dbCarrierContact.getCompanyCarrierId();
        if (companyCarrierId == null || CommonConstant.LONG_ZERO.equals(companyCarrierId)) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
        }
        //查询路线询价单
        TRouteEnquiry dbRouteEnquiry = getTRouteEnquiry(requestModel.getRouteEnquiryId());

        //判断车主是否能看到此单
        TRouteEnquiryCompany dbRouteEnquiryCompany = tRouteEnquiryCompanyMapper.getByRouteEnquiryIdAndCompanyId(dbRouteEnquiry.getId(), companyCarrierId);
        if (dbRouteEnquiryCompany == null){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_EXIST);
        }

        //已取消的不能操作
        if (CommonConstant.INTEGER_ONE.equals(dbRouteEnquiry.getIfCancel())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }
        //车主取消了，不能操作
        if (QuoteStatusEnum.CANCELLED.getKey().equals(dbRouteEnquiryCompany.getQuoteStatus())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }
        //仅【待业务审核】状态可操作
        if (!RouteEnquiryStatusEnum.WAIT_BUSINESS_AUDIT.getKey().equals(dbRouteEnquiry.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }

        //查询询价单下地址信息
        List<Long> dbRouteEnquiryAddressIdList = tRouteEnquiryAddressMapper.getByRouteEnquiryId(dbRouteEnquiry.getId()).stream().map(TRouteEnquiryAddress::getId).collect(Collectors.toList());
        //判断该询价单下所有地址是否均已报价
        List<Long> rqRouteEnquiryAddressIdList = requestModel.getQuoteList().stream().map(RouteEnquiryQuoteListRequestModel::getRouteEnquiryAddressId).distinct().collect(Collectors.toList());
        if (rqRouteEnquiryAddressIdList.size() != dbRouteEnquiryAddressIdList.size() || rqRouteEnquiryAddressIdList.stream().anyMatch(item -> !dbRouteEnquiryAddressIdList.contains(item))){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }

        //查询该询价单车主报价信息
        Map<Long, Long> dbRouteEnquiryAddressQuoteIdMap = tRouteEnquiryAddressQuoteMapper.getByCompanyId(dbRouteEnquiryCompany.getId())
                .stream().collect(Collectors.toMap(TRouteEnquiryAddressQuote::getRouteEnquiryAddressId, TRouteEnquiryAddressQuote::getId));

        Date now = new Date();
        String userName = BaseContextHandler.getUserName();
        //新增/更新-路线询价单地址车主报价
        TRouteEnquiryAddressQuote tRouteEnquiryAddressQuote;
        List<TRouteEnquiryAddressQuote> tRouteEnquiryAddressQuoteList = new ArrayList<>();
        for (RouteEnquiryQuoteListRequestModel model : requestModel.getQuoteList()) {
            tRouteEnquiryAddressQuote = new TRouteEnquiryAddressQuote();
            tRouteEnquiryAddressQuote.setDistance(model.getDistance());
            tRouteEnquiryAddressQuote.setQuotePriceType(model.getQuotePriceType());
            tRouteEnquiryAddressQuote.setQuotePrice(model.getQuotePrice());
            tRouteEnquiryAddressQuote.setQuoteRemark(model.getQuoteRemark());
            if (dbRouteEnquiryAddressQuoteIdMap.get(model.getRouteEnquiryAddressId()) == null) {
                tRouteEnquiryAddressQuote.setRouteEnquiryAddressId(model.getRouteEnquiryAddressId());
                tRouteEnquiryAddressQuote.setRouteEnquiryCompanyId(dbRouteEnquiryCompany.getId());
                commonBiz.setBaseEntityAdd(tRouteEnquiryAddressQuote, userName, now);
            }else{
                tRouteEnquiryAddressQuote.setId(dbRouteEnquiryAddressQuoteIdMap.get(model.getRouteEnquiryAddressId()));
                commonBiz.setBaseEntityModify(tRouteEnquiryAddressQuote, userName, now);
            }
            tRouteEnquiryAddressQuoteList.add(tRouteEnquiryAddressQuote);
        }
        if (ListUtils.isNotEmpty(tRouteEnquiryAddressQuoteList)) {
            if (MapUtils.isEmpty(dbRouteEnquiryAddressQuoteIdMap)) {
                tRouteEnquiryAddressQuoteMapper.batchInsert(tRouteEnquiryAddressQuoteList);
            } else {
                tRouteEnquiryAddressQuoteMapper.batchUpdateSelective(tRouteEnquiryAddressQuoteList);
            }
        }

        //更新路线询价单-车主信息
        TRouteEnquiryCompany upRouteEnquiryCompany = new TRouteEnquiryCompany();
        upRouteEnquiryCompany.setId(dbRouteEnquiryCompany.getId());
        upRouteEnquiryCompany.setQuoteStatus(QuoteStatusEnum.UNSELECTED.getKey());
        upRouteEnquiryCompany.setQuoteOperator(userName);
        upRouteEnquiryCompany.setQuoteOperatorPhone(dbCarrierContact.getContactPhone());
        upRouteEnquiryCompany.setQuoteTime(now);
        commonBiz.setBaseEntityModify(upRouteEnquiryCompany, userName, now);
        tRouteEnquiryCompanyMapper.updateByPrimaryKeySelectiveEncrypt(upRouteEnquiryCompany);

        //记录操作日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(dbRouteEnquiry.getId(), OperateLogsOperateTypeEnum.ROUTE_ENQUIRY_QUOTE, null, userName);
        tOperateLogsMapper.insertSelective(tOperateLogs);
    }

    /**
     * 前台-取消报价
     * @param requestModel
     * @return
     */
    @Transactional
    public void cancelQuoteForWeb(GetRouteEnquiryDetailRequestModel requestModel){
        //判断车主是否存在
        TCarrierContact dbCarrierContact = commonBiz.getLoginUserCompanyCarrier();
        if (dbCarrierContact == null){
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
        }
        Long companyCarrierId = dbCarrierContact.getCompanyCarrierId();
        if (companyCarrierId == null || CommonConstant.LONG_ZERO.equals(companyCarrierId)) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
        }
        //查询路线询价单
        TRouteEnquiry dbRouteEnquiry = getTRouteEnquiry(requestModel.getRouteEnquiryId());

        //判断车主是否能看到此单
        TRouteEnquiryCompany dbRouteEnquiryCompany = tRouteEnquiryCompanyMapper.getByRouteEnquiryIdAndCompanyId(dbRouteEnquiry.getId(), companyCarrierId);
        if (dbRouteEnquiryCompany == null){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_EXIST);
        }

        //已取消的不能操作
        if (CommonConstant.INTEGER_ONE.equals(dbRouteEnquiry.getIfCancel())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }
        //车主取消了，不能操作
        if (QuoteStatusEnum.CANCELLED.getKey().equals(dbRouteEnquiryCompany.getQuoteStatus())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }
        //仅【待业务审核】状态可操作
        if (!RouteEnquiryStatusEnum.WAIT_BUSINESS_AUDIT.getKey().equals(dbRouteEnquiry.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }

        Date now = new Date();
        String userName = BaseContextHandler.getUserName();

        //更新路线询价单-车主信息
        TRouteEnquiryCompany upRouteEnquiryCompany = new TRouteEnquiryCompany();
        upRouteEnquiryCompany.setId(dbRouteEnquiryCompany.getId());
        upRouteEnquiryCompany.setQuoteStatus(QuoteStatusEnum.CANCELLED.getKey());
        upRouteEnquiryCompany.setQuoteOperator(userName);
        upRouteEnquiryCompany.setQuoteOperatorPhone(dbCarrierContact.getContactPhone());
        upRouteEnquiryCompany.setQuoteTime(now);
        commonBiz.setBaseEntityModify(upRouteEnquiryCompany, userName, now);
        tRouteEnquiryCompanyMapper.updateByPrimaryKeySelectiveEncrypt(upRouteEnquiryCompany);

        //跟新主表最后操作时间 用于列表排序
        TRouteEnquiry updateRouteEnquiry = new TRouteEnquiry();
        updateRouteEnquiry.setId(dbRouteEnquiry.getId());
        commonBiz.setBaseEntityModify(updateRouteEnquiry, userName, now);
        tRouteEnquiryMapper.updateByPrimaryKeySelective(updateRouteEnquiry);

        //记录操作日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(dbRouteEnquiry.getId(), OperateLogsOperateTypeEnum.ROUTE_ENQUIRY_CANCEL, null, userName);
        tOperateLogsMapper.insertSelective(tOperateLogs);

    }

    /**
     * 前台-查询临时报价单回填信息
     * @param requestModel
     * @return
     */
    public GetCreateQuotationInfoResponseModel createQuotation(GetRouteEnquiryDetailRequestModel requestModel){
        //判断车主是否存在
        Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        if (companyCarrierId == null || CommonConstant.LONG_ZERO.equals(companyCarrierId)) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
        }
        //查询路线询价单
        TRouteEnquiry dbRouteEnquiry = getTRouteEnquiry(requestModel.getRouteEnquiryId());

        //判断车主是否能看到此单
        TRouteEnquiryCompany dbRouteEnquiryCompany = tRouteEnquiryCompanyMapper.getByRouteEnquiryIdAndCompanyId(dbRouteEnquiry.getId(), companyCarrierId);
        if (dbRouteEnquiryCompany == null){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_EXIST);
        }

        //已取消的不能操作
        if (CommonConstant.INTEGER_ONE.equals(dbRouteEnquiry.getIfCancel())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }
        //仅【待车主确认】状态可操作
        if (!RouteEnquiryStatusEnum.WAIT_CARRIER_CONFIRM.getKey().equals(dbRouteEnquiry.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }
        //仅已选择的车主才能操作（未选择的车主显示的是已取消）
        if (!QuoteStatusEnum.SELECTED.getKey().equals(dbRouteEnquiryCompany.getQuoteStatus())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }

        //地址列表
        List<GetRouteEnquiryDetailAddressListResponseModel> addressList = tRouteEnquiryAddressMapper.getAddressForWebByRouteEnquiryId(dbRouteEnquiry.getId(), dbRouteEnquiryCompany.getId());

        //组装数据
        GetCreateQuotationInfoResponseModel detail = MapperUtils.mapper(dbRouteEnquiry, GetCreateQuotationInfoResponseModel.class);
        detail.setCompanyCarrierType(dbRouteEnquiryCompany.getCompanyCarrierType());
        detail.setCompanyCarrierName(dbRouteEnquiryCompany.getCompanyCarrierName());
        detail.setCarrierContactName(dbRouteEnquiryCompany.getCarrierContactName());
        detail.setCarrierContactPhone(dbRouteEnquiryCompany.getCarrierContactPhone());
        detail.setQuoteOperator(dbRouteEnquiryCompany.getQuoteOperator());
        detail.setQuoteOperatorPhone(dbRouteEnquiryCompany.getQuoteOperatorPhone());
        detail.setQuoteTime(dbRouteEnquiryCompany.getQuoteTime());
        detail.setAddressList(addressList);
        return detail;
    }

    /**
     * 前台-上传报价单
     * @param requestModel
     * @return
     */
    @Transactional
    public void uploadQuotation(UploadQuotationRequestModel requestModel){
        //判断车主是否存在
        Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        if (companyCarrierId == null || CommonConstant.LONG_ZERO.equals(companyCarrierId)) {
            throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
        }
        //查询路线询价单
        TRouteEnquiry dbRouteEnquiry = getTRouteEnquiry(requestModel.getRouteEnquiryId());

        //判断车主是否能看到此单
        TRouteEnquiryCompany dbRouteEnquiryCompany = tRouteEnquiryCompanyMapper.getByRouteEnquiryIdAndCompanyId(dbRouteEnquiry.getId(), companyCarrierId);
        if (dbRouteEnquiryCompany == null){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_EXIST);
        }

        //已取消的不能操作
        if (CommonConstant.INTEGER_ONE.equals(dbRouteEnquiry.getIfCancel())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }
        //仅【待车主确认】状态可操作
        if (!RouteEnquiryStatusEnum.WAIT_CARRIER_CONFIRM.getKey().equals(dbRouteEnquiry.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }
        //仅已选择的车主才能操作（未选择的车主显示的是已取消）
        if (!QuoteStatusEnum.SELECTED.getKey().equals(dbRouteEnquiryCompany.getQuoteStatus())){
            throw new BizException(CarrierDataExceptionEnum.ROUTE_ENQUIRY_NOT_OPERATE);
        }

        String userName = BaseContextHandler.getUserName();

        //更新归档状态
        TRouteEnquiry upRouteEnquiry = new TRouteEnquiry();
        upRouteEnquiry.setId(dbRouteEnquiry.getId());
        upRouteEnquiry.setStatus(RouteEnquiryStatusEnum.WAIT_SETTLE_AUDIT.getKey());
        commonBiz.setBaseEntityModify(upRouteEnquiry, userName);
        tRouteEnquiryMapper.updateByPrimaryKeySelective(upRouteEnquiry);

        //新增报价单文件
        Date now = new Date();
        TRouteEnquiryAttachment addRouteEnquiryAttachment;
        List<TRouteEnquiryAttachment> addRouteEnquiryAttachmentList = new ArrayList<>();
        for (String path : requestModel.getQuotationFileList()) {
            addRouteEnquiryAttachment = new TRouteEnquiryAttachment();
            addRouteEnquiryAttachment.setRouteEnquiryId(dbRouteEnquiry.getId());
            addRouteEnquiryAttachment.setAttachmentType(RouteEnquiryAttachmentTypeEnum.QUOTED_FILE.getKey());
            addRouteEnquiryAttachment.setAttachmentPath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.ROUTE_ENQUIRY_ATTACHMENT.getKey(), dbRouteEnquiry.getOrderCode(), path));
            commonBiz.setBaseEntityAdd(addRouteEnquiryAttachment, userName, now);
            addRouteEnquiryAttachmentList.add(addRouteEnquiryAttachment);
        }
        if (ListUtils.isNotEmpty(addRouteEnquiryAttachmentList)){
            tRouteEnquiryAttachmentMapper.batchInsert(addRouteEnquiryAttachmentList);
        }
    }
    
}
