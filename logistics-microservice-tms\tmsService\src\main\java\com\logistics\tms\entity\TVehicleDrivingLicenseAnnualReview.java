package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleDrivingLicenseAnnualReview extends BaseEntity {
    /**
    * 车辆行驶证ID
    */
    @ApiModelProperty("车辆行驶证ID")
    private Long drivingLicenseId;

    /**
    * 行驶证检查有效期
    */
    @ApiModelProperty("行驶证检查有效期")
    private Date checkValidDate;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}