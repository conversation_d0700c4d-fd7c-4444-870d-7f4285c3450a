<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierVehicleRelationMapper" >
   <select id="getList" resultType="com.logistics.tms.controller.carriervehiclerel.response.SearchCarrierVehicleListResponseModel">
       select
       tcvr.id                 as carrierVehicleId,
       tcvr.created_by         as createdByName,
       tcvr.created_time       as createdTime,
       tvb.loading_capacity    as loadingCapacity,
       tvdl.vehicle_no        as vehicleNo,
       tvt.vehicle_type       as vehicleType
       from t_carrier_vehicle_relation tcvr
       left join t_vehicle_basic tvb on tvb.id = tcvr.vehicle_id and tvb.valid = 1
       left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = tcvr.vehicle_id and tvdl.valid = 1
       left join t_vehicle_type tvt on tvdl.vehicle_type = tvt.id and tvt.valid = 1
       where tcvr.valid = 1
       and tcvr.company_carrier_id = #{condition.companyCarrierId,jdbcType = BIGINT}
       <if test="condition.vehicleNo != null and condition.vehicleNo != ''">
           and instr(tvdl.vehicle_no, #{condition.vehicleNo,jdbcType = VARCHAR}) > 0
       </if>
       order by tcvr.created_time desc
   </select>

    <select id="getByVehicleIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_carrier_vehicle_relation
        where valid = 1
        and vehicle_id in (${vehicleIds})
    </select>

    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TCarrierVehicleRelation">
        <foreach collection="list" separator=";" item="temp">
            insert into t_carrier_vehicle_relation
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="temp.id != null" >
                    id,
                </if>
                <if test="temp.companyCarrierId != null" >
                    company_carrier_id,
                </if>
                <if test="temp.vehicleId != null" >
                    vehicle_id,
                </if>
                <if test="temp.createdBy != null" >
                    created_by,
                </if>
                <if test="temp.createdTime != null" >
                    created_time,
                </if>
                <if test="temp.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="temp.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="temp.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="temp.id != null" >
                    #{temp.id,jdbcType=BIGINT},
                </if>
                <if test="temp.companyCarrierId != null" >
                    #{temp.companyCarrierId,jdbcType=BIGINT},
                </if>
                <if test="temp.vehicleId != null" >
                    #{temp.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="temp.createdBy != null" >
                    #{temp.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="temp.createdTime != null" >
                    #{temp.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="temp.lastModifiedBy != null" >
                    #{temp.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="temp.lastModifiedTime != null" >
                    #{temp.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="temp.valid != null" >
                    #{temp.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <select id="getByCompanyCarrierId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_vehicle_relation
        where valid = 1
        and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
    </select>

    <select id="getByCompanyCarrierIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_vehicle_relation
        where valid = 1
        and company_carrier_id in (${companyCarrierIds})
    </select>

    <select id="getByCompanyCarrierIdAndVehicleId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_carrier_vehicle_relation
        where valid = 1
        and vehicle_id = #{vehicleId,jdbcType = BIGINT}
        and company_carrier_id = #{companyCarrierId,jdbcType = BIGINT}
        limit 1
    </select>

    <select id="searchRelByVehicleNoAndCarrierId" resultType="com.logistics.tms.controller.demandorder.response.WebDriverAndVehicleResponseModel">
        select
        tvb.id as vehicleId,
        tvdl.vehicle_no as vehicleNo
        from t_carrier_vehicle_relation tcvr
        left join t_vehicle_basic tvb on tvb.id = tcvr.vehicle_id and tvb.valid = 1
        left join t_vehicle_driving_license tvdl on tvdl.vehicle_id=tvb.id and tvdl.valid=1
        where tcvr.valid = 1
        and tcvr.company_carrier_id = #{companyCarrierId,jdbcType = BIGINT}
        <if test="vehicleNo != null and vehicleNo != ''">
            and (instr(tvdl.vehicle_no,#{vehicleNo,jdbcType=VARCHAR}))
        </if>
    </select>

    <select id="getCarrierVehicleDetail"  resultType="com.logistics.tms.controller.carriervehiclerel.response.GetCarrierVehicleDetailResponseModel">
        select
        tcvr.id                 as carrierVehicleId,
        tvb.loading_capacity    as loadingCapacity,
        tvdl.vehicle_no         as vehicleNo,
        tvdl.vehicle_type       as vehicleType
        from t_carrier_vehicle_relation tcvr
        left join t_vehicle_basic tvb on tvb.id = tcvr.vehicle_id and tvb.valid = 1
        left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = tcvr.vehicle_id and tvdl.valid = 1
        where tcvr.valid = 1
        and tcvr.company_carrier_id = #{companyCarrierId,jdbcType = BIGINT}
        and tcvr.id = #{carrierVehicleId,jdbcType=BIGINT}
    </select>

    <update id="batchUpdate">
        <foreach collection="list" index="index" item="item">
            update t_carrier_vehicle_relation
            <set>
                <if test="item.companyCarrierId != null">
                    company_carrier_id = #{item.companyCarrierId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleId != null">
                    vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <resultMap id="vehicleBasicMap" type="com.logistics.tms.controller.vehicleassetmanagement.response.VehicleAssetManagementListResponseModel">
        <id column="carrierVehicleId" property="carrierVehicleId" jdbcType="BIGINT"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="vehicleBasicId" property="vehicleBasicId" jdbcType="BIGINT"/>
        <result column="usage_property" property="usageProperty" jdbcType="INTEGER"/>
        <result column="vehicle_property" property="vehicleProperty" jdbcType="INTEGER"/>
        <result column="vehicle_type" property="vehicleType" jdbcType="VARCHAR"/>
        <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="vehicle_identification_number" property="vehicleIdentificationNumber" jdbcType="VARCHAR"/>
        <result column="engine_number" property="engineNumber" jdbcType="VARCHAR"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR"/>
        <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP"/>
        <result column="operating_state" property="operatingState" jdbcType="INTEGER" />
        <result column="outageInfo" property="outageInfo" jdbcType="VARCHAR"/>
        <result column="approved_load_weight" property="approvedLoadWeight" jdbcType="DECIMAL"/>
        <result column="total_weight" property="totalWeight" jdbcType="DECIMAL"/>
        <result column="vehicle_category" property="vehicleCategory" jdbcType="INTEGER"/>
        <result column="loading_capacity" property="loadingCapacity" jdbcType="INTEGER"/>
    </resultMap>

    <select id="getVehicleList" resultMap="vehicleBasicMap">
        SELECT
        tcvr.id as carrierVehicleId,
        tcvr.company_carrier_id,
        tcvr.created_by,
        tcvr.last_modified_by,
        tcvr.last_modified_time,

        tqvb.id as vehicleBasicId,
        tqvb.usage_property,
        tqvb.vehicle_property,
        tqvb.operating_state,
        tqvb.shut_down_reason as outageInfo,
        tqvb.loading_capacity,

        tqvdl.vehicle_no,
        tqvdl.vehicle_identification_number,
        tqvdl.engine_number,
        tqvdl.approved_load_weight,
        tqvdl.total_weight,

        tvt.vehicle_type,
        tvt.vehicle_category
        FROM t_carrier_vehicle_relation tcvr
        left join t_vehicle_basic tqvb on tqvb.id = tcvr.vehicle_id and tqvb.valid = 1
        LEFT JOIN t_vehicle_driving_license tqvdl ON tqvb.id = tqvdl.vehicle_id and tqvdl.valid = 1
        left join t_vehicle_type tvt on tvt.id = tqvdl.vehicle_type and tvt.valid = 1
        where tcvr.valid = 1
        <if test = "params.vehicleCategory!=null">
            <choose>
                <!-- 牵引车和一体车-->
                <when test="params.vehicleCategory == 4">
                    and tvt.vehicle_category in (1,3)
                </when>
                <otherwise>
                    and tvt.vehicle_category = #{params.vehicleCategory,jdbcType = INTEGER}
                </otherwise>
            </choose>
        </if>
        <if test="params.operatingState != null">
            <if test="params.operatingState == 1">
                and (tqvb.operating_state = 1 or tcvr.company_carrier_id != #{params.companyCarrierId,jdbcType=BIGINT})
            </if>
            <if test="params.operatingState == 2">
                and tqvb.operating_state = 2
                and tcvr.company_carrier_id = #{params.companyCarrierId,jdbcType=BIGINT}
            </if>
            <if test="params.operatingState == 3">
                and tqvb.operating_state in (3, 4)
                and tcvr.company_carrier_id = #{params.companyCarrierId,jdbcType=BIGINT}
            </if>
        </if>
        <if test="params.usageProperty!=null">
            and tqvb.usage_property = #{params.usageProperty,jdbcType = INTEGER}
        </if>
        <if test="params.vehicleProperty!=null">
            and tqvb.vehicle_property = #{params.vehicleProperty,jdbcType = INTEGER}
        </if>
        <if test="params.vehicleTypeList!=null and params.vehicleTypeList.size>0">
            <trim prefix="and (" suffix=")" prefixOverrides="or">
                <foreach collection="params.vehicleTypeList" item="item">
                    or (instr(tvt.vehicle_type,#{item,jdbcType=VARCHAR}))
                </foreach>
            </trim>
        </if>
        <if test="params.vehicleNo!=null and params.vehicleNo!=''">
            and (instr(tqvdl.vehicle_no,#{params.vehicleNo,jdbcType=VARCHAR}))
        </if>
        <if test="params.lastModifiedBy!=null and params.lastModifiedBy !=''">
            and (instr(tcvr.last_modified_by,#{params.lastModifiedBy,jdbcType=VARCHAR}))
        </if>
        <if test="params.lastModifiedTimeFrom!=null and params.lastModifiedTimeFrom !=''">
            and tcvr.last_modified_time >= DATE_FORMAT(#{params.lastModifiedTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00')
        </if>
        <if test="params.lastModifiedTimeTo!=null and params.lastModifiedTimeTo !=''">
            and tcvr.last_modified_time &lt;= DATE_FORMAT(#{params.lastModifiedTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.vehicleBasicIds != null and params.vehicleBasicIds != ''">
            and tcvr.id in (${params.vehicleBasicIds})
        </if>
        <if test="params.ids!=null and params.ids!=''">
            and tcvr.id in (${params.ids})
        </if>
        <if test="params.engineNumber!=null and params.engineNumber!=''">
            and (instr(tqvdl.engine_number,#{params.engineNumber,jdbcType = VARCHAR}))
        </if>
        <if test="params.vehicleIdentificationNumber!=null and params.vehicleIdentificationNumber!=''">
            and (instr(tqvdl.vehicle_identification_number,#{params.vehicleIdentificationNumber,jdbcType = VARCHAR}))
        </if>
        <if test="companyIds != null and companyIds.size() > 0">
            and tcvr.company_carrier_id in
            <foreach collection="companyIds" item="companyId" separator="," open="(" close=")">
                #{companyId}
            </foreach>
        </if>
        order by tcvr.last_modified_time desc,tcvr.id desc
    </select>

    <select id="getExportVehicleList" resultType="com.logistics.tms.controller.vehicleassetmanagement.response.ExportVehicleBasicInfoResponseModel">
        SELECT
        tcvr.id as carrierVehicleId,
        tcvr.company_carrier_id as companyCarrierId,

        tqvb.id as vehicleBasicId,
        tqvb.usage_property as usageProperty,
        tqvb.if_install_gps as ifInstallGps,
        tqvb.if_access_sinopec as ifAccessSinopec,
        tqvb.vehicle_property as vehicleProperty,
        tqvb.vehicle_owner as vehicleOwner,
        tqvb.authentication_start_time as authenticationStartTime,
        tqvb.authentication_expire_time as authenticationExpireTime,
        tqvb.registration_certification_number as registrationCertificationNumber,
        tqvb.emission_standard_type as emissionStandardType,

        tqvdl.id as vehicleDrivingLicenseId,
        tqvdl.vehicle_no as vehicleNo,
        tqvdl.vehicle_type as vehicleType,
        tqvdl.owner as owner,
        tqvdl.address as address,
        tqvdl.brand as brand,
        tqvdl.model as model,
        tqvdl.vehicle_identification_number as vehicleIdentificationNumber,
        tqvdl.engine_number as engineNumber,
        tqvdl.certification_department as drivingCertificationDepartmentOne,
        tqvdl.registration_date as registrationDate,
        tqvdl.issue_date as drivingIssueDate,
        tqvdl.filing_number as filingNumber,
        tqvdl.authorized_carrying_capacity as authorizedCarryingCapacity,
        tqvdl.total_weight as totalWeight,
        tqvdl.curb_weight as curbWeight,
        tqvdl.traction_mass_weight as tractionMassWeight,
        tqvdl.approved_load_weight as approvedLoadWeight,
        tqvdl.length as length,
        tqvdl.width as width,
        tqvdl.height as height,
        tqvdl.obsolescence_date as obsolescenceDate,
        tqvdl.axle_number as axleNumber,
        tqvdl.drive_shaft_number as driveShaftNumber,
        tqvdl.tires_number as tiresNumber,
        tqvdl.plate_color as plateColor,
        tqvdl.body_color as bodyColor,
        tqvdl.vehicle_type as vehicleType,

        tvt.vehicle_type as vehicleTypeLabel
        FROM t_carrier_vehicle_relation tcvr
        left join t_vehicle_basic tqvb on tqvb.id = tcvr.vehicle_id and tqvb.valid = 1
        LEFT JOIN t_vehicle_driving_license tqvdl ON tqvb.id = tqvdl.vehicle_id and tqvdl.valid = 1
        left join t_vehicle_type tvt on tvt.id = tqvdl.vehicle_type and tvt.valid = 1
        where tcvr.valid = 1
        <if test = "params.vehicleCategory!=null">
            <choose>
                <!-- 牵引车和一体车-->
                <when test="params.vehicleCategory == 4">
                    and tvt.vehicle_category in (1,3)
                </when>
                <otherwise>
                    and tvt.vehicle_category = #{params.vehicleCategory,jdbcType = INTEGER}
                </otherwise>
            </choose>
        </if>
        <if test="params.operatingState != null">
            <if test="params.operatingState == 1">
                and (tqvb.operating_state = 1 or tcvr.company_carrier_id != #{params.companyCarrierId,jdbcType=BIGINT})
            </if>
            <if test="params.operatingState == 2">
                and tqvb.operating_state = 2
                and tcvr.company_carrier_id = #{params.companyCarrierId,jdbcType=BIGINT}
            </if>
            <if test="params.operatingState == 3">
                and tqvb.operating_state in (3, 4)
                and tcvr.company_carrier_id = #{params.companyCarrierId,jdbcType=BIGINT}
            </if>
        </if>
        <if test="params.usageProperty!=null">
            and tqvb.usage_property = #{params.usageProperty,jdbcType = INTEGER}
        </if>
        <if test="params.vehicleProperty!=null">
            and tqvb.vehicle_property = #{params.vehicleProperty,jdbcType = INTEGER}
        </if>
        <if test="params.vehicleTypeList!=null and params.vehicleTypeList.size>0">
            <trim prefix="and (" suffix=")" prefixOverrides="or">
                <foreach collection="params.vehicleTypeList" item="item">
                    or (instr(tvt.vehicle_type,#{item,jdbcType=VARCHAR}))
                </foreach>
            </trim>
        </if>
        <if test="params.vehicleNo!=null and params.vehicleNo!=''">
            and (instr(tqvdl.vehicle_no,#{params.vehicleNo,jdbcType=VARCHAR}))
        </if>
        <if test="params.lastModifiedBy!=null and params.lastModifiedBy !=''">
            and (instr(tqvb.last_modified_by,#{params.lastModifiedBy,jdbcType=VARCHAR}))
        </if>
        <if test="params.lastModifiedTimeFrom!=null and params.lastModifiedTimeFrom !=''">
            and tqvb.last_modified_time >= DATE_FORMAT(#{params.lastModifiedTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00')
        </if>
        <if test="params.lastModifiedTimeTo!=null and params.lastModifiedTimeTo !=''">
            and tqvb.last_modified_time &lt;= DATE_FORMAT(#{params.lastModifiedTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.vehicleBasicIds != null and params.vehicleBasicIds != ''">
            and tqvb.id in (${params.vehicleBasicIds})
        </if>
        <if test="params.ids!=null and params.ids!=''">
            and tqvb.id in (${params.ids})
        </if>
        <if test="params.engineNumber!=null and params.engineNumber!=''">
            and (instr(tqvdl.engine_number,#{params.engineNumber,jdbcType = VARCHAR}))
        </if>
        <if test="params.vehicleIdentificationNumber!=null and params.vehicleIdentificationNumber!=''">
            and (instr(tqvdl.vehicle_identification_number,#{params.vehicleIdentificationNumber,jdbcType = VARCHAR}))
        </if>
        <if test="companyIds != null and companyIds.size() > 0">
            and tcvr.company_carrier_id in
            <foreach collection="companyIds" item="companyId" separator="," open="(" close=")">
                #{companyId}
            </foreach>
        </if>
        order by tcvr.last_modified_time desc,tcvr.id desc
    </select>

    <select id="selectVehicleRelAndInfoByVehicleIds" resultType="com.logistics.tms.biz.carriervehiclerel.model.CarrierVehicleRelAndInfoModel">
        select
        tcvr.id                 as carrierVehicleRelId,
        tcvr.vehicle_id         as vehicleId,
        tcvr.company_carrier_id as companyCarrierId,
        tvb.loading_capacity    as loadingCapacity,
        tvb.vehicle_property    as vehicleProperty
        from t_carrier_vehicle_relation tcvr
        left join t_vehicle_basic tvb on tvb.id = tcvr.vehicle_id and tvb.valid = 1
        left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = tcvr.vehicle_id and tvdl.valid = 1
        where tcvr.valid = 1
        <choose>
            <when test="vehicleidList != null and vehicleidList.size() != 0">
                and tcvr.vehicle_id in
                <foreach collection="vehicleidList" open="(" separator="," close=")" item="item">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </select>
</mapper>
