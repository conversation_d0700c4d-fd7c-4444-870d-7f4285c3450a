package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierOrderDetailVehicleDriverInfoDto {

    @ApiModelProperty("车辆运单关联ID")
    private String vehicleHistoryId;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("审核状态 -1 无需审核 0 待审核 1 已审核 2 已驳回")
    private String auditStatus;

    @ApiModelProperty("是否有效 1 有效 0 无效")
    private String ifInvalid;

    @ApiModelProperty("车牌号码")
    private String vehicleNo;

    @ApiModelProperty("司机")
    private String driver;

    @ApiModelProperty("备注")
    private String remark;
}
