package com.logistics.management.webapi.client.freightconfig.request.mileage;

import com.logistics.management.webapi.client.freightconfig.request.ladder.CarrierFreightConfigLadderRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.scheme.CarrierFreightConfigSchemeEditRequestModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigMileageEditRequestModel extends CarrierFreightConfigSchemeEditRequestModel {

    @ApiModelProperty(value = "阶梯")
    private List<CarrierFreightConfigLadderRequestModel> ladderConfigList;

    @ApiModelProperty(value = "移除阶梯配置Id集合")
    private List<String> removeLadderConfigIds;
}
