package com.logistics.management.webapi.client.staff.hystrix;


import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.staff.StaffManagementClient;
import com.logistics.management.webapi.client.staff.request.FuzzyQueryDriverInfoFeignRequestModel;
import com.logistics.management.webapi.client.staff.response.FuzzyQueryDriverInfoFeignResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;


/**
 * @author: wjf
 * @date: 2024/3/20 9:46
 */
@Component
public class StaffManagementClientHystrix implements StaffManagementClient {

    @Override
    public Result<PageInfo<FuzzyQueryDriverInfoFeignResponseModel>> fuzzyQueryDriverInfo(FuzzyQueryDriverInfoFeignRequestModel requestModel) {
        return Result.timeout();
    }
}
