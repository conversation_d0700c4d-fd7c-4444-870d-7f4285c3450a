package com.logistics.management.webapi.api.impl.safegroupmeeting;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.safegroupmeeting.SafeGroupMeetingApi;
import com.logistics.management.webapi.api.feign.safegroupmeeting.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.safegroupmeeting.mapping.SafeGroupMeetingDetailMapping;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.tms.api.feign.safegroupmeeting.SafeGroupMeetingServiceApi;
import com.logistics.tms.api.feign.safegroupmeeting.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: MIkasa
 * @date: 2021/04/09
 */
@RestController
@Slf4j
public class SafeGroupMeetingApiImpl implements SafeGroupMeetingApi {

    @Autowired
    private SafeGroupMeetingServiceApi safeGroupMeetingServiceApi;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 安全小组领导会议记录列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<SafeGroupMeetingResponseDto>> safeGroupMeetingData(@RequestBody @Valid SafeGroupMeetingRequestDto requestDto) {
        Result<List<SafeGroupMeetingResponseModel>> result = safeGroupMeetingServiceApi.safeGroupMeetingData(MapperUtils.mapper(requestDto, SafeGroupMeetingRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SafeGroupMeetingResponseDto.class));
    }

    /**
     * 新建安全小组领导会议
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addSafeGroupMeeting(@RequestBody @Valid AddSafeGroupMeetingRequestDto requestDto) {
        Result<Boolean> result = safeGroupMeetingServiceApi.addSafeGroupMeeting(MapperUtils.mapperNoDefault(requestDto, AddSafeGroupMeetingRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 安全小组领导会议详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<SafeGroupMeetingDetailResponseDto> safeGroupMeetingDetail(@RequestBody  @Valid SafeGroupMeetingDetailRequestDto requestDto) {
        Result<SafeGroupMeetingDetailResponseModel> result = safeGroupMeetingServiceApi.safeGroupMeetingDetail(MapperUtils.mapper(requestDto, SafeGroupMeetingDetailRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (SafetyGroupMeetingAttachmentModel model : result.getData().getSafetyGroupMeetingAttachmentList()) {
            sourceSrcList.add(model.getMeetingImagePath());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(), SafeGroupMeetingDetailResponseDto.class,new SafeGroupMeetingDetailMapping(configKeyConstant.fileAccessAddress,imageMap)));
    }
}
