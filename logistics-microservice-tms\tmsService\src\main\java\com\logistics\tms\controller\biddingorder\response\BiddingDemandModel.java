package com.logistics.tms.controller.biddingorder.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/04/26
 */
@Data
public class BiddingDemandModel {

    /**
     * 发货地址
     */
    private String loadAddress;


    /**
     * 收货地址
     */
    private String unloadAddress;


    /**
     * 需求单号
     */
    private String demandOrderCode;

    /**
     * 需求单id
     */
    private Long demandOrderId;


    /**
     * 数量
     */
    private BigDecimal goodsCount;

    /**
     * 货物
     */
    private String goodsName;
}
