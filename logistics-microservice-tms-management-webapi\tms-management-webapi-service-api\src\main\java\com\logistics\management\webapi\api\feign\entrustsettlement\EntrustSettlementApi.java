package com.logistics.management.webapi.api.feign.entrustsettlement;

import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.entrustsettlement.dto.*;
import com.logistics.management.webapi.api.feign.entrustsettlement.hystrix.EntrustSettlementApiHystrix;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * @author: wjf
 * @date: 2019/10/11 19:52
 */
@Api(value = "API-EntrustSettlementApi-委托方结算管理")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = EntrustSettlementApiHystrix.class)
public interface EntrustSettlementApi {

    @ApiOperation(value = "查询委托方结算列表")
    @PostMapping(value = "/api/settlement/entrust/entrustSettlementList")
    Result<EntrustSettlementListResponseDto> entrustSettlementList(@RequestBody EntrustSettlementListRequestDto requestDto);

    @ApiOperation(value = "导出委托方结算列表")
    @GetMapping(value = "/api/settlement/entrust/exportEntrustSettlementList")
    void exportEntrustSettlementList(EntrustSettlementListRequestDto requestDto, HttpServletResponse response);

    @ApiOperation(value = "修改费用")
    @PostMapping(value = "/api/settlement/entrust/modifyCost")
    Result modifyCost(@RequestBody @Valid ModifyCostRequestDto requestDto);

    @ApiOperation(value = "结算详情，确认收款，回退界面")
    @PostMapping(value = "/api/settlement/entrust/getSettlementDetail")
    Result<GetSettlementDetailResponseDto> getSettlementDetail(@RequestBody @Valid GetSettlementDetailRequestDto requestDto);

    @ApiOperation(value = "结算详情，修改费用")
    @PostMapping(value = "/api/settlement/entrust/getDetail")
    Result<GetDetailResponseDto> getDetail(@RequestBody @Valid GetDetailRequestDto requestDto);

    @ApiOperation(value = "已收款")
    @PostMapping(value = "/api/settlement/entrust/receiveMoney")
    Result receiveMoney(@RequestBody @Valid ReceiveMoneyRequestDto requestDto);

    @ApiOperation(value = "退款")
    @PostMapping(value = "/api/settlement/entrust/refund")
    Result refund(@RequestBody @Valid RefundRequestDto requestDto);
}
