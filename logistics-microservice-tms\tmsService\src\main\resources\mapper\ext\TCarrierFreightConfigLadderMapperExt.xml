<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierFreightConfigLadderMapper">
    <update id="batchUpdateSelective">
        <foreach collection="recordList" item="item" separator=";">
            update t_carrier_freight_config_ladder
            <set>
                <if test="item.modeId != null">
                    mode_id = #{item.modeId,jdbcType=BIGINT},
                </if>
                <if test="item.priceMode != null">
                    price_mode = #{item.priceMode,jdbcType=INTEGER},
                </if>
                <if test="item.ladderMode != null">
                    ladder_mode = #{item.ladderMode,jdbcType=INTEGER},
                </if>
                <if test="item.ladderLevel != null">
                    ladder_level = #{item.ladderLevel,jdbcType=INTEGER},
                </if>
                <if test="item.ladderPid != null">
                    ladder_pid = #{item.ladderPid,jdbcType=BIGINT},
                </if>
                <if test="item.ladderType != null">
                    ladder_type = #{item.ladderType,jdbcType=INTEGER},
                </if>
                <if test="item.ladderFrom != null">
                    ladder_from = #{item.ladderFrom,jdbcType=DECIMAL},
                </if>
                <if test="item.ladderTo != null">
                    ladder_to = #{item.ladderTo,jdbcType=DECIMAL},
                </if>
                <if test="item.ladderUnit != null">
                    ladder_unit = #{item.ladderUnit,jdbcType=INTEGER},
                </if>
                <if test="item.unitPrice != null">
                    unit_price = #{item.unitPrice,jdbcType=DECIMAL},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectConfigLadderByLadderModeAndModeIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_freight_config_ladder
        where valid = 1
        <if test="ladderMode != null">
            <if test="modeIds != null and modeIds.size > 0">
                and mode_id in
                <foreach collection="modeIds" item="modeId" open="(" separator="," close=")">
                    #{modeId,jdbcType=BIGINT}
                </foreach>
            </if>
            and ladder_mode = #{ladderMode,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectConfigLadderByConfigAddressId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_freight_config_ladder
        where valid = 1
          and mode_id = #{freightConfigAddressId,jdbcType=BIGINT}
    </select>

    <insert id="insertGeneratedKey" parameterType="com.logistics.tms.entity.TCarrierFreightConfigLadder" keyProperty="id" useGeneratedKeys="true">
        insert into t_carrier_freight_config_ladder
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="modeId != null" >
                mode_id,
            </if>
            <if test="priceMode != null" >
                price_mode,
            </if>
            <if test="ladderMode != null" >
                ladder_mode,
            </if>
            <if test="ladderLevel != null" >
                ladder_level,
            </if>
            <if test="ladderPid != null" >
                ladder_pid,
            </if>
            <if test="ladderType != null" >
                ladder_type,
            </if>
            <if test="ladderFrom != null" >
                ladder_from,
            </if>
            <if test="ladderTo != null" >
                ladder_to,
            </if>
            <if test="ladderUnit != null" >
                ladder_unit,
            </if>
            <if test="unitPrice != null" >
                unit_price,
            </if>
            <if test="createdBy != null" >
                created_by,
            </if>
            <if test="createdTime != null" >
                created_time,
            </if>
            <if test="lastModifiedBy != null" >
                last_modified_by,
            </if>
            <if test="lastModifiedTime != null" >
                last_modified_time,
            </if>
            <if test="valid != null" >
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="modeId != null" >
                #{modeId,jdbcType=BIGINT},
            </if>
            <if test="priceMode != null" >
                #{priceMode,jdbcType=INTEGER},
            </if>
            <if test="ladderMode != null" >
                #{ladderMode,jdbcType=INTEGER},
            </if>
            <if test="ladderLevel != null" >
                #{ladderLevel,jdbcType=INTEGER},
            </if>
            <if test="ladderPid != null" >
                #{ladderPid,jdbcType=BIGINT},
            </if>
            <if test="ladderType != null" >
                #{ladderType,jdbcType=INTEGER},
            </if>
            <if test="ladderFrom != null" >
                #{ladderFrom,jdbcType=DECIMAL},
            </if>
            <if test="ladderTo != null" >
                #{ladderTo,jdbcType=DECIMAL},
            </if>
            <if test="ladderUnit != null" >
                #{ladderUnit,jdbcType=INTEGER},
            </if>
            <if test="unitPrice != null" >
                #{unitPrice,jdbcType=DECIMAL},
            </if>
            <if test="createdBy != null" >
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null" >
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null" >
                #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null" >
                #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null" >
                #{valid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TCarrierFreightConfigLadder">
        <foreach collection="recordList" item="item" separator=";">
            insert into t_carrier_freight_config_ladder
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    id,
                </if>
                <if test="item.modeId != null" >
                    mode_id,
                </if>
                <if test="item.priceMode != null" >
                    price_mode,
                </if>
                <if test="item.ladderMode != null" >
                    ladder_mode,
                </if>
                <if test="item.ladderLevel != null" >
                    ladder_level,
                </if>
                <if test="item.ladderPid != null" >
                    ladder_pid,
                </if>
                <if test="item.ladderType != null" >
                    ladder_type,
                </if>
                <if test="item.ladderFrom != null" >
                    ladder_from,
                </if>
                <if test="item.ladderTo != null" >
                    ladder_to,
                </if>
                <if test="item.ladderUnit != null" >
                    ladder_unit,
                </if>
                <if test="item.unitPrice != null" >
                    unit_price,
                </if>
                <if test="item.createdBy != null" >
                    created_by,
                </if>
                <if test="item.createdTime != null" >
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="item.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.modeId != null" >
                    #{item.modeId,jdbcType=BIGINT},
                </if>
                <if test="item.priceMode != null" >
                    #{item.priceMode,jdbcType=INTEGER},
                </if>
                <if test="item.ladderMode != null" >
                    #{item.ladderMode,jdbcType=INTEGER},
                </if>
                <if test="item.ladderLevel != null" >
                    #{item.ladderLevel,jdbcType=INTEGER},
                </if>
                <if test="item.ladderPid != null" >
                    #{item.ladderPid,jdbcType=BIGINT},
                </if>
                <if test="item.ladderType != null" >
                    #{item.ladderType,jdbcType=INTEGER},
                </if>
                <if test="item.ladderFrom != null" >
                    #{item.ladderFrom,jdbcType=DECIMAL},
                </if>
                <if test="item.ladderTo != null" >
                    #{item.ladderTo,jdbcType=DECIMAL},
                </if>
                <if test="item.ladderUnit != null" >
                    #{item.ladderUnit,jdbcType=INTEGER},
                </if>
                <if test="item.unitPrice != null" >
                    #{item.unitPrice,jdbcType=DECIMAL},
                </if>
                <if test="item.createdBy != null" >
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>


    <update id="deleteByModeIds">
        update t_carrier_freight_config_ladder set valid = 0, last_modified_time = now()
        <if test="lastModifiedBy != null" >
            , last_modified_by = #{lastModifiedBy}
        </if>
        where valid = 1
            and ladder_mode = #{ladderMode}
        <if test="modeIds != null and modeIds.size > 0">
            and mode_id in
            <foreach collection="modeIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>
</mapper>