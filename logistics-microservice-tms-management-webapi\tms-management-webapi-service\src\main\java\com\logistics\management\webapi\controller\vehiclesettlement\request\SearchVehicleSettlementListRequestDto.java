package com.logistics.management.webapi.controller.vehiclesettlement.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/10/12 19:18
 */
@Data
public class SearchVehicleSettlementListRequestDto extends AbstractPageForm<SearchVehicleSettlementListRequestDto> {
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private String vehicleProperty;
    @ApiModelProperty("结算状态：0 待对账，1 待发送，2 待确认，3 待处理，4 待结清，5 部分结清，6 已结清")
    private String status;
    @ApiModelProperty("司机姓名+手机号")
    private String driverName;
    @ApiModelProperty("账单时间")
    private String settlementMonthStart;
    private String settlementMonthEnd;
    @ApiModelProperty("列表多选导出使用")
    private String vehicleSettlementIds;
}
