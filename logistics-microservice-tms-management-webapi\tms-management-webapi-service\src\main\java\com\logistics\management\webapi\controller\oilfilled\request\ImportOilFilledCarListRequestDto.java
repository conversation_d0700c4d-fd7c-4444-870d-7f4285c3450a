package com.logistics.management.webapi.controller.oilfilled.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ImportOilFilledCarListRequestDto {

    @ApiModelProperty(value = "合作公司")
    private String cooperationCompany;
    @ApiModelProperty(value = "升数")
    private String liter;
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;
    @ApiModelProperty(value = "司机")
    private String driverName;
    @ApiModelProperty(value = "司机手机号")
    private String driverPhone;
    @ApiModelProperty(value = "总金额")
    private String totalAmount;
    @ApiModelProperty(value = "加油时间")
    private String oilFilledDate;
    @ApiModelProperty(value = "备注")
    private String remark;

}
