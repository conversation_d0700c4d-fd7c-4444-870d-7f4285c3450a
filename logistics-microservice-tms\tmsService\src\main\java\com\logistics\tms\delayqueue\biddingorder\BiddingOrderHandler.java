package com.logistics.tms.delayqueue.biddingorder;

import com.logistics.tms.biz.biddingorder.BiddingOrderBiz;
import com.logistics.tms.config.rdelayqueue.core.AbstractRDelayQueueHandler;
import com.logistics.tms.delayqueue.DelayQueueBizTypeEnum;
import com.logistics.tms.delayqueue.biddingorder.msg.BiddingOrderDelayMsg;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/05/07
 */
@Component
public class BiddingOrderHandler extends AbstractRDelayQueueHandler {

    @Resource
    private BiddingOrderBiz biddingOrderBiz;

    @Override
    public void exc(String delayMsg) {
        BiddingOrderDelayMsg biddingOrderDelayMsg = this.parseStr2Object(delayMsg, BiddingOrderDelayMsg.class);
        if(biddingOrderDelayMsg==null){
            return;
        }
        biddingOrderBiz.excDelayMsg(biddingOrderDelayMsg);
    }

    @Override
    public void setType() {
        type= DelayQueueBizTypeEnum.BIDDING_ORDER.name();
    }
}
