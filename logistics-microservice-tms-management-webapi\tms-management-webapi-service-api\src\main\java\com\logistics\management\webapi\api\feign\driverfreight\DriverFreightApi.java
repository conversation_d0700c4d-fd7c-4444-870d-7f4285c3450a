package com.logistics.management.webapi.api.feign.driverfreight;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.driverfreight.dto.DriverFreightListSearchRequestDto;
import com.logistics.management.webapi.api.feign.driverfreight.dto.DriverFreightListSearchResponseDto;
import com.logistics.management.webapi.api.feign.driverfreight.hystrix.DriverFreightApiHystrix1;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;

@Api(value = "API-司机运费管理")
@FeignClient(name = "logistics-managementWeb-api", fallback = DriverFreightApiHystrix1.class)
public interface DriverFreightApi {

    @PostMapping(value = "/api/driverFreight/searchList")
    @ApiOperation(value = "司机运费列表", notes = "")
    Result<PageInfo<DriverFreightListSearchResponseDto>> driverFreightList(@RequestBody DriverFreightListSearchRequestDto requestDto);

    @GetMapping(value = "/api/driverFreight/export")
    @ApiOperation(value = "导出司机运费", notes = "")
    void exportDriverFreightList(DriverFreightListSearchRequestDto requestDto, HttpServletResponse response);

}
