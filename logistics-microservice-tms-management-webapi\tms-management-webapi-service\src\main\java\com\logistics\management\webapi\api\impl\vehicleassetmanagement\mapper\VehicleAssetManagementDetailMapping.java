
package com.logistics.management.webapi.api.impl.vehicleassetmanagement.mapper;

import com.logistics.management.webapi.api.feign.vehicleassetmanagement.dto.CertificationPicturesRecordDto;
import com.logistics.management.webapi.api.feign.vehicleassetmanagement.dto.VehicleAssetManagementDetailResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.EmissionStandardTypeEnum;
import com.logistics.tms.api.feign.vehicleassetmanagement.model.VehicleAssetManagementDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

public class VehicleAssetManagementDetailMapping extends MapperMapping <VehicleAssetManagementDetailResponseModel, VehicleAssetManagementDetailResponseDto>{
    private ConfigKeyConstant configKeyConstant;
    private Map<String, String> imageMap;
    public VehicleAssetManagementDetailMapping(ConfigKeyConstant configKeyConstant,Map<String, String> imageMap) {
        this.configKeyConstant = configKeyConstant;
        this.imageMap=imageMap;
    }
    @Override
    public void configure() {
        VehicleAssetManagementDetailResponseModel source = this.getSource();
        VehicleAssetManagementDetailResponseDto destination = this.getDestination();
        if(source!=null){
            destination.setCompanyCarrierName(CompanyTypeEnum.COMPANY.getKey().equals(source.getCompanyCarrierType()) ?
                    source.getCompanyCarrierName() :
                    source.getCarrierContactName() + " " + source.getCarrierContactPhone());

            destination.setEmissionStandard(EmissionStandardTypeEnum.getEnum(source.getEmissionStandardType()).getValue());
            if(CommonConstant.ZERO.equals(destination.getVehicleType())){
                destination.setVehicleType(CommonConstant.BLANK_TEXT);
            }
            if(CommonConstant.ZERO.equals(destination.getVehicleTypeId())){
                destination.setVehicleTypeId(CommonConstant.BLANK_TEXT);
            }
            if(CommonConstant.ZERO.equals(destination.getPlateColor())){
                destination.setPlateColor(CommonConstant.BLANK_TEXT);
            }
            if(CommonConstant.ZERO.equals(destination.getUsageProperty())){
                destination.setUsageProperty(CommonConstant.BLANK_TEXT);
            }
            if(CommonConstant.ZERO.equals(destination.getIfInstallGps())){
                destination.setIfInstallGps(CommonConstant.BLANK_TEXT);
            }
            if(CommonConstant.ZERO.equals(destination.getIfAccessSinopec())){
                destination.setIfAccessSinopec(CommonConstant.BLANK_TEXT);
            }
            if(CommonConstant.ZERO.equals(destination.getConnectWay())){
                destination.setConnectWay(CommonConstant.BLANK_TEXT);
            }
            if(ListUtils.isNotEmpty(destination.getOtherDocumentsRecord())){
                for(CertificationPicturesRecordDto tmp :destination.getOtherDocumentsRecord()){
                    tmp.setAbsoluteFilePath(configKeyConstant.fileAccessAddress+imageMap.get(tmp.getRelativeFilepath()));
                }
            }
            if(ListUtils.isNotEmpty(destination.getVehicleGpsRecordList())){
                destination.getVehicleGpsRecordList().stream().forEach(tmp->{
                    if(StringUtils.isNotBlank(tmp.getInstallTime())){
                        tmp.setInstallTime(tmp.getInstallTime().substring(0,tmp.getInstallTime().indexOf(' ')));
                    }
                    if(ListUtils.isNotEmpty(tmp.getFileList())){
                        tmp.getFileList().stream().forEach(tmp2->{
                            if(StringUtils.isNotBlank(tmp2.getRelativeFilepath())){
                                tmp2.setAbsoluteFilePath(configKeyConstant.fileAccessAddress+imageMap.get(tmp2.getRelativeFilepath()));
                            }
                        });
                    }
                });

            }
            if(ListUtils.isNotEmpty(destination.getVehicleRoadTransportCertificateList())){
                destination.getVehicleRoadTransportCertificateList().stream().forEach(tmp->{
                    if(StringUtils.isNotBlank(tmp.getCheckValidDate())){
                        tmp.setCheckValidDate(tmp.getCheckValidDate().substring(0,tmp.getCheckValidDate().lastIndexOf('-')));
                    }
                    if(ListUtils.isNotEmpty(tmp.getFileList())){
                        tmp.getFileList().stream().forEach(tmp2->{
                            if(StringUtils.isNotBlank(tmp2.getRelativeFilepath()))
                                tmp2.setAbsoluteFilePath(configKeyConstant.fileAccessAddress+imageMap.get(tmp2.getRelativeFilepath()));
                        });
                    }
                });

            }
            if(ListUtils.isNotEmpty(destination.getVehicleDrivingLicensePageList())){
                destination.getVehicleDrivingLicensePageList().stream().forEach(tmp->{
                    if(StringUtils.isNotBlank(tmp.getCheckValidDate())){
                        tmp.setCheckValidDate(tmp.getCheckValidDate().substring(0,tmp.getCheckValidDate().lastIndexOf('-')));
                    }
                    if(ListUtils.isNotEmpty(tmp.getFileList())){
                        tmp.getFileList().stream().forEach(tmp2->{
                            if(StringUtils.isNotBlank(tmp2.getRelativeFilepath()))
                                tmp2.setAbsoluteFilePath(configKeyConstant.fileAccessAddress+imageMap.get(tmp2.getRelativeFilepath()));
                        });
                    }
                });
            }
            if(ListUtils.isNotEmpty(destination.getVehicleGradeEstimationList())){
                destination.getVehicleGradeEstimationList().stream().forEach(tmp->{
                    if(ListUtils.isNotEmpty(tmp.getFileList())){
                        tmp.getFileList().stream().forEach(tmp2->{
                            if(StringUtils.isNotBlank(tmp2.getRelativeFilepath()))
                                tmp2.setAbsoluteFilePath(configKeyConstant.fileAccessAddress+imageMap.get(tmp2.getRelativeFilepath()));
                        });
                    }
                });
            }

            //吨(坐)位
            destination.setTransportTonnage(Optional.ofNullable(source.getTransportTonnage()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());

            //核定载人数 整备质量 总质量 核定载质量 准牵引总质量
            if(isNullForInteger(source.getAuthorizedCarryingCapacity())){
                destination.setAuthorizedCarryingCapacity(CommonConstant.BLANK_TEXT);
            }
            if(isNullForBigDecimal(source.getCurbWeight())){
                destination.setCurbWeight(CommonConstant.BLANK_TEXT);
            }else{
                destination.setCurbWeight(source.getCurbWeight().stripTrailingZeros().toPlainString());
            }
            if(isNullForBigDecimal(source.getTotalWeight())){
                destination.setTotalWeight(CommonConstant.BLANK_TEXT);
            }else{
                destination.setTotalWeight(source.getTotalWeight().stripTrailingZeros().toPlainString());
            }
            if(isNullForBigDecimal(source.getApprovedLoadWeight())){
               destination.setApprovedLoadWeight(CommonConstant.BLANK_TEXT);
            }else{
                destination.setApprovedLoadWeight(source.getApprovedLoadWeight().stripTrailingZeros().toPlainString());
            }
            if(isNullForBigDecimal(source.getTractionMassWeight())){
                destination.setTractionMassWeight(CommonConstant.BLANK_TEXT);
            }else{
                destination.setTractionMassWeight(source.getTractionMassWeight().stripTrailingZeros().toPlainString());
            }
            //吨坐位 外廊尺寸-长 外廊尺寸-宽 外廊尺寸-高 车辆轴数 驱动轴数 轮胎数
            if(isNullForBigDecimal(source.getTransportTonnage())){
                destination.setTransportTonnage(CommonConstant.BLANK_TEXT);
            }
            if(isNullForInteger(source.getLength())){
                destination.setLength(CommonConstant.BLANK_TEXT);
            }
            if(isNullForInteger(source.getWidth())){
                destination.setWidth(CommonConstant.BLANK_TEXT);
            }
            if(isNullForInteger(source.getHeight())){
                destination.setHeight(CommonConstant.BLANK_TEXT);
            }
            if(isNullForInteger(source.getAxleNumber())){
                destination.setAxleNumber(CommonConstant.BLANK_TEXT);
            }
            if(isNullForInteger(source.getDriveShaftNumber())){
                destination.setDriveShaftNumber(CommonConstant.BLANK_TEXT);
            }
            if(isNullForInteger(source.getTiresNumber())){
                destination.setTiresNumber(CommonConstant.BLANK_TEXT);
            }
            if(CommonConstant.RETURN_FAILURE.equals(destination.getPlateColor())){
                destination.setPlateColor(CommonConstant.BLANK_TEXT);
            }

            if(source.getAuthenticationExpireTime()!=null){
                destination.setAuthenticationExpireTime(DateUtils.dateToString(source.getAuthenticationExpireTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getAuthenticationStartTime()!=null){
                destination.setAuthenticationStartTime(DateUtils.dateToString(source.getAuthenticationStartTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getIssueDate()!=null){
                destination.setIssueDate(DateUtils.dateToString(source.getIssueDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getObsolescenceDate()!=null){
                destination.setObsolescenceDate(DateUtils.dateToString(source.getObsolescenceDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getObtainDate()!=null){
                destination.setObtainDate(DateUtils.dateToString(source.getObtainDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getRegistrationDate()!=null){
                destination.setRegistrationDate(DateUtils.dateToString(source.getRegistrationDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getConnectTime()!=null){
                destination.setConnectTime(DateUtils.dateToString(source.getConnectTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getRoadIssueDate()!=null){
                destination.setRoadIssueDate(DateUtils.dateToString(source.getRoadIssueDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
        }
    }

    private boolean isNullForInteger(Integer source){
        return source == null || CommonConstant.INTEGER_ZERO.equals(source);
    }
    private boolean isNullForBigDecimal(BigDecimal source){
        return source == null || CommonConstant.BIG_DECIMAL_ZERO.compareTo(source) == 0;
    }
}
