package com.logistics.appapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CarrierOrderDetailResponseDto {

    @ApiModelProperty("运单ID")
    private String carrierOrderId = "";

    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";

    @ApiModelProperty("乐橘新生客户名称")
    private String customerName;

    @ApiModelProperty("客户单号")
    private String customerOrderCode = "";

    @ApiModelProperty("运单状态10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0 已取消 1待审核 2 已放空")
    private String status = "";

    private String statusLabel = "";

    @ApiModelProperty("提货省份")
    private String loadProvinceName = "";

    @ApiModelProperty("提货城市")
    private String loadCityName = "";

    @ApiModelProperty("提货区")
    private String loadAreaName = "";

    @ApiModelProperty("提货详细地址")
    private String loadDetailAddress = "";

    @ApiModelProperty("提货仓库")
    private String loadWarehouse = "";

    @ApiModelProperty("起点联系人")
    private String loadPerson = "";

    @ApiModelProperty("起点联系方式")
    private String loadMobile = "";

    @ApiModelProperty("提货经度")
    private String loadLongitude = "";

    @ApiModelProperty("提货纬度")
    private String loadLatitude = "";

    @ApiModelProperty("卸货省份")
    private String unloadProvinceName = "";

    @ApiModelProperty("卸货城市")
    private String unloadCityName = "";

    @ApiModelProperty("卸货区")
    private String unloadAreaName = "";

    @ApiModelProperty("卸货详细地址")
    private String unloadDetailAddress = "";

    @ApiModelProperty("卸货仓库")
    private String unloadWarehouse = "";

    @ApiModelProperty("卸点联系人")
    private String unloadPerson = "";

    @ApiModelProperty("卸点联系方式")
    private String unloadMobile = "";

    @ApiModelProperty("调度电话")
    private String dispatchMobile = "";

    @ApiModelProperty("提货时间")
    private String expectArrivalTime = "";

    private String unloadTime = "";

    @ApiModelProperty("卸货经度")
    private String unloadLongitude = "";

    @ApiModelProperty("卸货纬度")
    private String unloadLatitude = "";

    @ApiModelProperty("运单货物信息")
    private List<CarrierOrderDetailGoodsInfoDto> carrierOrderDetailGoodsInfo;

    @ApiModelProperty("总件数/吨位（包含单位）")
    private String totalAmount = "";

    @ApiModelProperty("总体积（为空不显示）")
    private String totalCapacity = "";

    @ApiModelProperty("备注")
    private String remark = "";

    @ApiModelProperty("货物单位：1 件，2 吨")
    private String goodsUnit = "";

    @ApiModelProperty("取消原因")
    private String cancelReason = "";

    @ApiModelProperty("取消人")
    private String cancelOperatorName = "";

    @ApiModelProperty("取消时间")
    private String cancelTime = "";

    @ApiModelProperty("车牌号")
    private String vehicleNo = "";

    @ApiModelProperty("司机名称")
    private String driverName = "";

    @ApiModelProperty("司机电话")
    private String driverMobile = "";

    @ApiModelProperty("临时费用提交：0 否，1 是")
    private String commitOtherFee = "";

    @ApiModelProperty("委托单来源：1 乐医托盘委托单，2 委托方发单，3 中石化系统，4 扬巴邮件，5 乐橘新生")
    private String demandOrderSource = "";

    @ApiModelProperty("需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private String entrustType = "";

    @ApiModelProperty("是否按编码回收 0:否 1:是   v2.44")
    private String ifRecycleByCode = "" ;

    @ApiModelProperty("是否需要编码核销 0:否 1:是 v2.46")
    private String ifCodeVerification = "";

    @ApiModelProperty("是否有额外补单 0:否 1:是 V2.6.8")
    private String hasExtCarrierOrder = "";




}
