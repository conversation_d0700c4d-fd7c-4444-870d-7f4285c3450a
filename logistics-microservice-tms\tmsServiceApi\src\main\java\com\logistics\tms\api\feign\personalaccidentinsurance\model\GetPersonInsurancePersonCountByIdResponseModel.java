package com.logistics.tms.api.feign.personalaccidentinsurance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/6/24 13:50
 */
@Data
public class GetPersonInsurancePersonCountByIdResponseModel {
    private Long personalAccidentInsuranceId;
    private String policyNumber;
    private String batchNumber;
    private Integer policyPersonCount;
    @ApiModelProperty("关联人数")
    private Integer associatedCount;
}
