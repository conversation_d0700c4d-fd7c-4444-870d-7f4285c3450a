package com.logistics.tms.mapper;

import com.logistics.tms.controller.dispatchorder.response.CarrierOrderGoodsResponseModel;
import com.logistics.tms.entity.TCarrierOrderGoods;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TCarrierOrderGoodsMapper extends BaseMapper<TCarrierOrderGoods> {

	int batchInsertSelective(@Param("list") List<TCarrierOrderGoods> list);

	List<TCarrierOrderGoods> selectGoodsByCarrierOrderIds(@Param("carrierOrderIds") String carrierOrderIds);

	int batchUpdateCarrierOrderGoods(@Param("carrierOrderGoodsList") List<TCarrierOrderGoods> list);

	List<CarrierOrderGoodsResponseModel> getByDispatchOrderId(@Param("dispatchOrderId") Long dispatchOrderId);

	List<TCarrierOrderGoods> selectGoodsByCarrierOrderId(@Param("carrierOrderId") Long carrierOrderId);
}