<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandOrderGoodsRelMapper" >
    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TDemandOrderGoodsRel" >
        <foreach collection="list" item="item" separator=";">
            insert into t_demand_order_goods_rel
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    id,
                </if>
                <if test="item.demandOrderGoodsId != null" >
                    demand_order_goods_id,
                </if>
                <if test="item.bookingOrderGoodsId != null" >
                    booking_order_goods_id,
                </if>
                <if test="item.createdBy != null" >
                    created_by,
                </if>
                <if test="item.createdTime != null" >
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="item.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.demandOrderGoodsId != null" >
                    #{item.demandOrderGoodsId,jdbcType=BIGINT},
                </if>
                <if test="item.bookingOrderGoodsId != null" >
                    #{item.bookingOrderGoodsId,jdbcType=BIGINT},
                </if>
                <if test="item.createdBy != null" >
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <select id="getByDemandOrderIds" resultMap="BaseResultMap">
    select
    tdogl.*
    from t_demand_order_goods_rel tdogl
    left join t_demand_order_goods tdog on tdog.id = tdogl.demand_order_goods_id and tdog.valid = 1
    where tdogl.valid = 1
    and tdog.demand_order_id in (${demandOrderIds})
  </select>

    <select id="getInvalidByDemandOrderId" resultMap="BaseResultMap">
        select
            tdogl.*
        from t_demand_order_goods_rel tdogl
        left join t_demand_order_goods tdog on tdog.id = tdogl.demand_order_goods_id and tdog.valid = 1
        where tdog.demand_order_id = #{demandOrderId,jdbcType=BIGINT}
    </select>
</mapper>