package com.logistics.management.webapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/25
 */
@Data
public class UpdateCarrierOrderUnloadAddressForLifeRequestModel {

	@ApiModelProperty("运单ID")

	private Long carrierOrderId;

	@ApiModelProperty(value = "修改原因类型：1 联系问题，2 地址问题，3 装车问题，4 等待问题")
	private Integer unloadAddressUpdateType;

	@ApiModelProperty(value = "卸货地址code")
	private String unloadWarehouseCode;

	@ApiModelProperty(value = "收货省份id")
	private Long unloadProvinceId;

	@ApiModelProperty("收货省份名字")
	private String unloadProvinceName;

	@ApiModelProperty(value = "收货城市id")
	private Long unloadCityId;

	@ApiModelProperty("收货城市名字")
	private String unloadCityName;

	@ApiModelProperty(value = "收货县区id")
	private Long unloadAreaId;

	@ApiModelProperty("收货县区名字")
	private String unloadAreaName;

	@ApiModelProperty("收货详细地址")
	private String unloadDetailAddress;

	@ApiModelProperty("收货仓库")
	private String unloadWarehouse;

	@ApiModelProperty("收货人姓名")
	private String receiverName;

	@ApiModelProperty("收货人手机号")
	private String receiverMobile;
}
