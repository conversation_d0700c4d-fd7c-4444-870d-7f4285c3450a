package com.logistics.management.webapi.base.enums;

import lombok.Getter;

/**
 * 业务节点, 101:下单 102:调度 103:取消；201:提货 202:纠错 203:取消 204:生成运单 205:修改车辆 206:卸货 207:到达提货地 208:到达卸货地
 */
@Getter
public enum WorkGroupOrderNodeEnum {
    DEFAULT(WorkGroupOrderTypeEnum.DEFAULT,0, ""),

    DEMAND_ORDER_CREATE(WorkGroupOrderTypeEnum.DEMAND_ORDER,101, "下单"),
    DEMAND_ORDER_DISPATCH(WorkGroupOrderTypeEnum.DEMAND_ORDER,102, "调度"),
    DEMAND_ORDER_CANCEL(WorkGroupOrderTypeEnum.DEMAND_ORDER,103, "取消"),

    CARRIER_ORDER_LOAD(WorkGroupOrderTypeEnum.CARRIER_ORDER,201, "提货"),
    CARRIER_ORDER_CORRECTION(WorkGroupOrderTypeEnum.CARRIER_ORDER,202, "纠错"),
    CARRIER_ORDER_CANCEL(WorkGroupOrderTypeEnum.CARRIER_ORDER,203, "取消"),
    CARRIER_ORDER_CREATE(WorkGroupOrderTypeEnum.CARRIER_ORDER,204, "生成运单"),
    CARRIER_ORDER_UPDATE_VEHICLE(WorkGroupOrderTypeEnum.CARRIER_ORDER,205, "修改车辆"),
    CARRIER_ORDER_UNLOAD(WorkGroupOrderTypeEnum.CARRIER_ORDER,206, "卸货"),
    CARRIER_ORDER_REACH_LOAD_ADDRESS(WorkGroupOrderTypeEnum.CARRIER_ORDER,207, "到达提货地"),
    CARRIER_ORDER_REACH_UNLOAD_ADDRESS(WorkGroupOrderTypeEnum.CARRIER_ORDER,208, "到达卸货地"),
    ;

    private final WorkGroupOrderTypeEnum orderTypeEnum;
    private final Integer key;
    private final String value;

    WorkGroupOrderNodeEnum(WorkGroupOrderTypeEnum orderTypeEnum, Integer key, String value) {
        this.orderTypeEnum = orderTypeEnum;
        this.key = key;
        this.value = value;
    }

    public static WorkGroupOrderNodeEnum getEnum(Integer key) {
        for (WorkGroupOrderNodeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }

    public static WorkGroupOrderNodeEnum getEnumByKeyStr(String key) {
        for (WorkGroupOrderNodeEnum t : values()) {
            if (t.getKey().toString().equals(key) ) {
                return t;
            }
        }
        return DEFAULT;
    }
}
