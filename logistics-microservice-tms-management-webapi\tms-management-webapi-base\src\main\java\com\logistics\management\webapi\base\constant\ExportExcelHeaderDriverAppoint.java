package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/26 11:35
 */
public class ExportExcelHeaderDriverAppoint {

    private ExportExcelHeaderDriverAppoint() {
    }

    private static final Map<String, String> EXCEL_HEADER;
    static {
        EXCEL_HEADER = new LinkedHashMap<>();
        EXCEL_HEADER.put("司机", "exportDriver");
        EXCEL_HEADER.put("司机机构", "staffPropertyLabel");
        EXCEL_HEADER.put("需求单号", "demandOrderCode");
        EXCEL_HEADER.put("车牌号", "vehicleNo");
        EXCEL_HEADER.put("业务类型", "businessLabel");
        EXCEL_HEADER.put("客户", "exportCustomer");
        EXCEL_HEADER.put("下单地址", "loadAddress");
        EXCEL_HEADER.put("发货联系人", "exportConsignor");
        EXCEL_HEADER.put("下单数量(kg)", "goodsAmountTotal");
        EXCEL_HEADER.put("合计", "goodsPriceTotal");
        EXCEL_HEADER.put("下单时间", "publishTime");
    }

    public static Map<String, String> getExcelDriverAppoint() {
        return EXCEL_HEADER;
    }
}
