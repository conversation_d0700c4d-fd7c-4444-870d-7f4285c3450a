package com.logistics.management.webapi.api.feign.demandorderobjectionsinopec.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/5/30 13:36
 */
@Data
public class GetSinopecObjectionDetailResponseDto {
    @ApiModelProperty("需求单异常id")
    private String demandOrderObjectionId="";
    @ApiModelProperty("需求单id")
    private String demandId= "";
    @ApiModelProperty("需求单号")
    private String demandOrderCode= "";
    @ApiModelProperty("客户单号")
    private String customerOrderCode= "";
    @ApiModelProperty("货主")
    private String companyEntrustName= "";
    @ApiModelProperty("异常类型")
    private String objectionTypeLabel= "";
    @ApiModelProperty("调度人")
    private String dispatcher="";

    //审核结果
    @ApiModelProperty("审核状态：0 待审核，1 已审核，２ 已驳回")
    private String auditStatus= "";

    @ApiModelProperty("审核结果")
    private String auditStatusLabel= "";
    @ApiModelProperty("审核异常类型")
    private String auditObjectionTypeLabel= "";
    @ApiModelProperty("审核依据")
    private List<String> auditTicketList;
    @ApiModelProperty("备注")
    private String auditRemark;
}
