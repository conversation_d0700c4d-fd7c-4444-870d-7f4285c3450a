package com.logistics.appapi.client.carrierorderotherfee;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.carrierorderotherfee.hystrix.CarrierOrderOtherFeeHystrix;
import com.logistics.appapi.client.carrierorderotherfee.request.AddCarrierOrderOtherFeeRequestModel;
import com.logistics.appapi.client.carrierorderotherfee.request.CommitCarrierOrderOtherFeeRequestModel;
import com.logistics.appapi.client.carrierorderotherfee.request.GetOtherFeeByCarrierOrderIdRequestModel;
import com.logistics.appapi.client.carrierorderotherfee.response.GetOtherFeeByCarrierOrderIdResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2023/10/26 14:25
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/carrierOrderOtherFee",
        fallback = CarrierOrderOtherFeeHystrix.class)
public interface CarrierOrderOtherFeeClient {

    /**
     * 新增临时费用
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "新增临时费用")
    @PostMapping(value = "/addCarrierOrderOtherFee")
    Result<Boolean> addCarrierOrderOtherFee(@RequestBody AddCarrierOrderOtherFeeRequestModel requestModel);

    /**
     * 审核/提交临时费用
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "审核/提交临时费用")
    @PostMapping(value = "/commitCarrierOrderOtherFee")
    Result<Boolean> commitCarrierOrderOtherFee(@RequestBody CommitCarrierOrderOtherFeeRequestModel requestModel);

    /**
     * 根据运单id查询【待提交】状态最新一条临时费用详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "根据运单id查询【待提交】状态最新一条临时费用详情")
    @PostMapping(value = "/getOtherFeeByCarrierOrderId")
    Result<GetOtherFeeByCarrierOrderIdResponseModel> getOtherFeeByCarrierOrderId(@RequestBody GetOtherFeeByCarrierOrderIdRequestModel requestModel);
}
