package com.logistics.tms.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-07-29 16:46
 */
@Data
public class CarrierOrderNodeGoodsRequestModel {
    @ApiModelProperty("货物id")
    private Long goodsId;
    @ApiModelProperty("提货/卸货件数")
    private BigDecimal count;

    @ApiModelProperty(value = "货物的编码集合 v2.44", required = true)
    private List<LoadGoodsForYeloLifeRequestCodeModel> codeDtoList;
}
