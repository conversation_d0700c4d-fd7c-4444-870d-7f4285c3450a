package com.logistics.tms.biz.workordercenter;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.forthirdparty.model.request.WorkOrderProcessSyncRequestModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.demandorder.model.WorkOrderPendingOrderModel;
import com.logistics.tms.biz.staff.model.CarrierDriverRelationModel;
import com.logistics.tms.biz.workordercenter.model.WorkOrderExceptionModel;
import com.logistics.tms.biz.workordercenter.model.WorkOrderReportBoModel;
import com.logistics.tms.client.WorkOrderCenterClient;
import com.logistics.tms.client.feign.basicdata.BasicServiceClient;
import com.logistics.tms.client.feign.basicdata.user.response.UserDetailResponseModel;
import com.logistics.tms.client.model.WorkOrderCancelSyncRequestDto;
import com.logistics.tms.client.model.WorkOrderReportSyncRequestDto;
import com.logistics.tms.controller.workordercenter.request.*;
import com.logistics.tms.controller.workordercenter.response.WorkOrderDetailResponseModel;
import com.logistics.tms.controller.workordercenter.response.WorkOrderListResponseModel;
import com.logistics.tms.controller.workordercenter.response.WorkOrderProcessResponseModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tools.utils.UUIDGenerateUtil;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2023/4/17 9:44
 */
@Slf4j
@Service
public class WorkOrderBiz {

    @Resource
    private RedissonClient redissonClient;
    @Autowired
    private TWorkOrderMapper tWorkOrderMapper;
    @Autowired
    private TWorkOrderProcessMapper tWorkOrderProcessMapper;
    @Resource
    private TDemandOrderMapper demandOrderMapper;
    @Resource
    private TCarrierOrderMapper carrierOrderMapper;
    @Resource
    private TCarrierOrderVehicleHistoryMapper carrierOrderVehicleHistoryMapper;
    @Resource
    private TDemandOrderAddressMapper demandOrderAddressMapper;
    @Resource
    private TCarrierOrderAddressMapper carrierOrderAddressMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Resource
    private WorkOrderCenterClient workOrderCenterClient;
    @Resource
    private BasicServiceClient basicServiceClient;


    /**
     * 根据运单id查询是否存在【待处理】【处理中】的工单
     *
     * @param carrierOrderIds 运单ids
     * @return 运单id-》工单id
     */
    public Map<Long, Long> checkCarrierExceptionExist(String carrierOrderIds) {
        Map<Long, Long> workOrderMap = new HashMap<>();
        if (StringUtils.isBlank(carrierOrderIds)) {
            return workOrderMap;
        }
        List<WorkOrderExceptionModel> workOrderExceptionModelList = tWorkOrderMapper.checkExceptionExist(null, carrierOrderIds);
        if (ListUtils.isNotEmpty(workOrderExceptionModelList)) {
            workOrderMap = workOrderExceptionModelList.stream().collect(Collectors.toMap(WorkOrderExceptionModel::getCarrierOrderId, WorkOrderExceptionModel::getWorkOrderId));
        }
        return workOrderMap;
    }

    /**
     * 根据需求单id查询是否存在【待处理】【处理中】的工单
     *
     * @param demandOrderIds 需求单ids
     * @return 需求单id-》最大工单id
     */
    public Map<Long, Long> checkDemandExceptionExist(String demandOrderIds) {
        Map<Long, Long> workOrderMap = new HashMap<>();
        if (StringUtils.isBlank(demandOrderIds)) {
            return workOrderMap;
        }
        List<WorkOrderExceptionModel> workOrderExceptionModelList = tWorkOrderMapper.checkExceptionExist(demandOrderIds, null);
        if (ListUtils.isNotEmpty(workOrderExceptionModelList)) {
            workOrderMap = workOrderExceptionModelList.stream().collect(Collectors.toMap(WorkOrderExceptionModel::getDemandOrderId, WorkOrderExceptionModel::getWorkOrderId, (m1, m2) -> m2));
        }
        return workOrderMap;
    }

    /**
     * 中单中心-工单列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<WorkOrderListResponseModel> workOrderList(WorkOrderListRequestModel requestModel) {
        //判断是否车主前台的请求
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }
            requestModel.setCompanyCarrierId(loginUserCompanyCarrierId);

            //后台提交的工单 前台展示的是 "工作人员"
            if (CommonConstant.WORK_ORDER_DEFAULT_PROCESSOR.equals(requestModel.getReportUserName())) {
                requestModel.setReportSource(CommonConstant.INTEGER_ONE);
                requestModel.setReportUserName(null);
            }
        }

        //根据条件分页查询数据
        requestModel.enablePaging();
        List<WorkOrderListResponseModel> responseModelList = tWorkOrderMapper.searchWorkOrderList(requestModel);
        return new PageInfo<>(responseModelList);
    }

    /**
     * 工单详情
     *
     * @param requestModel
     * @return
     */
    public WorkOrderDetailResponseModel workOrderDetail(WorkOrderDetailRequestModel requestModel) {
        //判断是否车主前台的请求
        Long loginUserCompanyCarrierId;
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }
        }
        WorkOrderDetailResponseModel workOrderDetail = tWorkOrderMapper.searchWorkOrderDetailById(requestModel);
        // 查询原发货人信息
        if (Objects.nonNull(workOrderDetail) && CommonConstant.INTEGER_TWO.equals(workOrderDetail.getCheckContact())) {
            // 运单
            if (!Objects.equals(workOrderDetail.getCarrierOrderId(), CommonConstant.LONG_ZERO)) {
                Optional.ofNullable(carrierOrderAddressMapper.getByCarrierOrderId(workOrderDetail.getCarrierOrderId()))
                        .ifPresent(s -> {
                            workOrderDetail.setConsignorName(s.getConsignorName());
                            workOrderDetail.setConsignorMobile(s.getConsignorMobile());
                        });
            }
            // 需求单
            else {
                demandOrderAddressMapper.getByDemandOrderIds(workOrderDetail.getDemandOrderId().toString())
                        .stream()
                        .findFirst()
                        .ifPresent(s -> {
                            workOrderDetail.setConsignorName(s.getConsignorName());
                            workOrderDetail.setConsignorMobile(s.getConsignorMobile());
                        });
            }
        }
        return workOrderDetail;
    }

    /**
     * 工单详情-处理过程列表
     *
     * @param requestModel
     * @return
     */
    public List<WorkOrderProcessResponseModel> workOrderProcessList(WorkOrderDetailRequestModel requestModel) {
        TWorkOrder tWorkOrder = checkWorkOrder(requestModel.getWorkOrderId(), requestModel.getSource());
        return tWorkOrderProcessMapper.searchProcessListByOrderId(tWorkOrder.getId());
    }

    /**
     * 撤销工单
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public void cancelWorkOrder(CancelWorkOrderRequestModel requestModel) {
        TWorkOrder tWorkOrder = checkWorkOrder(requestModel.getWorkOrderId(), requestModel.getSource());
        //待处理的工单才能撤销
        if (WorkOrderStatusEnum.negateCheckNextStatusByCurrentStatus(tWorkOrder.getStatus(), WorkOrderStatusEnum.RESCINDED.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.WORK_ORDER_ONLY_WAITING_TASK);
        }

        TWorkOrder tWorkOrderUp = new TWorkOrder();
        tWorkOrderUp.setId(tWorkOrder.getId());
        tWorkOrderUp.setStatus(WorkOrderStatusEnum.RESCINDED.getKey());
        commonBiz.setBaseEntityModify(tWorkOrderUp, BaseContextHandler.getUserName());
        tWorkOrderUp.setSolveUserName(tWorkOrderUp.getLastModifiedBy());
        tWorkOrderUp.setSolveTime(tWorkOrderUp.getLastModifiedTime());
        tWorkOrderMapper.updateByPrimaryKeySelective(tWorkOrderUp);

        // 记录节点数据
        Integer processStatus = WorkOrderStatusEnum.RESCINDED.getKey();
        TWorkOrderProcess processEntity = new TWorkOrderProcess();
        processEntity.setWorkOrderId(tWorkOrder.getId());
        processEntity.setStatus(processStatus);
        processEntity.setSolveDesc(WorkOrderProcessDescEnum.getDesc(processStatus));
        processEntity.setSolveSource(requestModel.getSource());
        processEntity.setSolveUserName(tWorkOrderUp.getLastModifiedBy());
        processEntity.setSolveTime(tWorkOrderUp.getLastModifiedTime());
        processEntity.setSolveRemark(requestModel.getRemark());
        tWorkOrderProcessMapper.insertSelective(processEntity);

        // 同步任务中心
        workOrderCenterClient.cancelWorkOrderSync(new WorkOrderCancelSyncRequestDto()
                .setCode(tWorkOrder.getWorkOrderCode())
                .setUserBy(BaseContextHandler.getUserName()));
    }

    /**
     * 校验工单数据权限
     *
     * @param workOrderId   工单id
     * @param requestSource 请求来源 1:后台 2:前台
     * @return 工单entity
     */
    private TWorkOrder checkWorkOrder(Long workOrderId, Integer requestSource) {
        TWorkOrder tWorkOrder = tWorkOrderMapper.selectByPrimaryKeyDecrypt(workOrderId);
        if (tWorkOrder == null || IfValidEnum.INVALID.getKey().equals(tWorkOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.WORK_ORDER_NOT_EXIST);
        }
        //前台请求判断
        if (CommonConstant.INTEGER_TWO.equals(requestSource)) {
            Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }
            //数据合法性校验
            if (!loginUserCompanyCarrierId.equals(tWorkOrder.getCompanyCarrierId())) {
                throw new BizException(CarrierDataExceptionEnum.WORK_ORDER_NOT_EXIST);
            }
        } else if (CommonConstant.INT_THREE.equals(requestSource)) {
            Long loginUserDriverId = commonBiz.getLoginDriverAppletUserId();
            if (loginUserDriverId == null || CommonConstant.LONG_ZERO.equals(loginUserDriverId)) {
                throw new BizException(CarrierDataExceptionEnum.DRIVER_NOT_EXIST);
            }
            //判断当前工单是不是当前司机的
            if (!loginUserDriverId.equals(tWorkOrder.getDriverId())) {
                throw new BizException(CarrierDataExceptionEnum.WORK_ORDER_NOT_EXIST);
            }
        }
        return tWorkOrder;
    }

    /**
     * 上报任务中心异常工单
     *
     * @param requestModel 请求Model
     * @return boolean
     */
    @Transactional
    public boolean reportWorkException(ReportWorkExceptionRequestModel requestModel) {

        // 针对订单上锁
        String lockKey = String.format(CommonConstant.REPORT_WORK_EXCEPTION_KEY, requestModel.getOrderId());
        RLock lock = null;
        boolean hasLock = false;
        try {
            lock = redissonClient.getLock(lockKey);
            hasLock = lock.tryLock();
            if (hasLock) {
                // 工单校验
                List<TWorkOrder> workOrders = tWorkOrderMapper.selectAllDecryptByOrderIdAndOrderType(requestModel.getOrderId(),
                        requestModel.getWorkOrderType());
                boolean isExist = workOrders
                        .stream()
                        .anyMatch(a -> {
                            WorkOrderStatusEnum statusEnum = WorkOrderStatusEnum.getEnumByKey(a.getStatus());
                            return WorkOrderStatusEnum.WAITING_TASK.equals(statusEnum)
                                    || WorkOrderStatusEnum.BEING_PROCESSED.equals(statusEnum);
                        });
                if (isExist) {
                    CarrierDataExceptionEnum exceptionEnum = WorkOrderTypeEnum.DEMAND_ORDER_TYPE.getKey().equals(requestModel.getWorkOrderType()) ?
                            CarrierDataExceptionEnum.REPORT_DEMAND_WORK_ORDER_EXIST
                            : CarrierDataExceptionEnum.REPORT_CARRIER_WORK_ORDER_EXIST;
                    throw new BizException(exceptionEnum);
                }

                // 上报校验
                WorkOrderReportBoModel boModel = this.reportCheckAndIsBuilderWorkEntity(requestModel, true);

                TWorkOrder entity = boModel.getEntity();
                entity.setWorkOrderCode(UUIDGenerateUtil.generateUUID());
                entity.setStatus(WorkOrderStatusEnum.WAITING_TASK.getKey());
                entity.setReportUserName(BaseContextHandler.getUserName());
                entity.setReportTime(new Date());
                tWorkOrderMapper.insertSelectiveEncrypt(entity);

                // 记录节点数据
                Integer processStatus = WorkOrderProcessStatusEnum.WAITING_TASK.getKey();
                String processDesc = WorkOrderProcessDescEnum.getDesc(processStatus, CommonConstant.INTEGER_ONE.equals(entity.getIsArriveScene()));
                TWorkOrderProcess processEntity = new TWorkOrderProcess();
                processEntity.setWorkOrderId(entity.getId());
                processEntity.setStatus(processStatus);
                processEntity.setSolveDesc(processDesc);
                processEntity.setSolveSource(entity.getReportSource());
                processEntity.setSolveUserName(entity.getReportUserName());
                processEntity.setSolveTime(entity.getReportTime());
                processEntity.setSolveRemark(entity.getRemark());
                tWorkOrderProcessMapper.insertSelective(processEntity);

                // 同步任务中心
                workOrderCenterClient.reportWorkOrderSync(new WorkOrderReportSyncRequestDto().of(boModel));
                return true;
            }
        }
        // 业务异常
        catch (BizException biz) {
            throw biz;
        }
        // 系统异常
        catch (Exception e) {
            log.error("report work exception error:{}", e.getMessage());
            throw new BizException(CarrierDataExceptionEnum.HYSTRIX_ERROR_MESSAGE);
        }
        finally {
            if (hasLock) {
                lock.unlock();
            }
        }
        // 超时未获取到锁
        throw new BizException(CarrierDataExceptionEnum.OPERATION_EXECUTING_MESSAGE);
    }

    /**
     * 重新上报异常
     *
     * @param requestModel 请求Model
     * @return boolean
     */
    @Transactional
    public boolean reReportWorkException(ReReportWorkExceptionRequestModel requestModel) {

        // 针对订单上锁
        String lockKey = String.format(CommonConstant.REPORT_WORK_EXCEPTION_KEY, requestModel.getOrderId());
        RLock lock = null;
        boolean hasLock = false;
        try {
            lock = redissonClient.getLock(lockKey);
            hasLock = lock.tryLock();
            if (hasLock) {

                // 工单校验
                List<TWorkOrder> workOrders = tWorkOrderMapper.selectAllDecryptByOrderIdAndOrderType(requestModel.getOrderId(),
                        requestModel.getWorkOrderType());
                if (ListUtils.isEmpty(workOrders)) {
                    throw new BizException(CarrierDataExceptionEnum.WORK_ORDER_NOT_EXIST);
                }
                TWorkOrder entity = workOrders
                        .stream()
                        .filter(f -> WorkOrderStatusEnum.WAITING_TASK.getKey().equals(f.getStatus()))
                        .findFirst()
                        .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.WORK_ORDER_STATUS_CANNOT_SUBMITTED));

                // 上报参数校验
                WorkOrderReportBoModel boModel = this.reportCheckAndIsBuilderWorkEntity(requestModel, false);

                TWorkOrder upEntity = MapperUtils.mapper(requestModel, TWorkOrder.class);
                upEntity.setId(requestModel.getWorkOrderId());
                upEntity.setReportSource(null);
                commonBiz.setBaseEntityModify(upEntity, BaseContextHandler.getUserName());
                // 获取图片路径
                if (StringUtils.isNotBlank(requestModel.getArriveScenePicture())) {
                    if (!requestModel.getArriveScenePicture().equals(entity.getArriveScenePicture())) {
                        String path = this.uploadPicture(requestModel.getArriveScenePicture());
                        upEntity.setArriveScenePicture(path);
                    }
                }else{
                    upEntity.setArriveScenePicture("");
                }
                tWorkOrderMapper.updateByPrimaryKeySelectiveEncrypt(upEntity);

                // 记录节点数据
                Integer processStatus = WorkOrderProcessStatusEnum.RESUBMIT.getKey();
                TWorkOrderProcess processEntity = new TWorkOrderProcess();
                processEntity.setWorkOrderId(requestModel.getWorkOrderId());
                processEntity.setStatus(processStatus);
                processEntity.setSolveDesc(WorkOrderProcessDescEnum.getDesc(processStatus));
                processEntity.setSolveSource(requestModel.getReportSource());
                processEntity.setSolveUserName(upEntity.getLastModifiedBy());
                processEntity.setSolveTime(upEntity.getLastModifiedTime());
                processEntity.setSolveRemark(upEntity.getRemark());
                tWorkOrderProcessMapper.insertSelective(processEntity);

                // 同步任务中心
                upEntity.setWorkOrderCode(entity.getWorkOrderCode());
                boModel.setEntity(upEntity);
                workOrderCenterClient.reportWorkOrderSync(new WorkOrderReportSyncRequestDto().of(boModel));
                return true;
            }
        }
        // 业务异常
        catch (BizException biz) {
            throw biz;
        }
        // 系统异常
        catch (Exception e) {
            log.error("report work exception error:{}", e.getMessage());
            throw new BizException(CarrierDataExceptionEnum.HYSTRIX_ERROR_MESSAGE);
        } finally {
            if (hasLock) {
                lock.unlock();
            }
        }
        // 超时未获取到锁
        throw new BizException(CarrierDataExceptionEnum.OPERATION_EXECUTING_MESSAGE);
    }

    /**
     * 同步异常工单处理流程
     * @param requestModel 请求Model
     * @return boolean
     */
    @Transactional
    public boolean syncWorkOrderProcess(WorkOrderProcessSyncRequestModel requestModel) {

        // 查询工单信息
        TWorkOrder workOrder = Optional.ofNullable(tWorkOrderMapper.selectOneByWorkOrderCode(requestModel.getCode()))
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.WORK_ORDER_NOT_EXIST));

        // 状态校验
        if (WorkOrderStatusEnum.negateCheckNextStatusByCurrentStatus(workOrder.getStatus(), requestModel.getStatus())) {
            throw new BizException(CarrierDataExceptionEnum.WORK_ORDER_STATUS_ABNORMAL_UNABLE_PROCESS);
        }

        // 更新工单处理人
        TWorkOrder upEntity = new TWorkOrder();
        upEntity.setId(workOrder.getId());
        upEntity.setStatus(requestModel.getStatus());
        //处理中不需要落处理人
        if (!WorkOrderStatusEnum.BEING_PROCESSED.getKey().equals(requestModel.getStatus())) {
            upEntity.setSolveUserName(requestModel.getSolveUserName());
            upEntity.setSolveTime(requestModel.getSolveTime());
        }
        commonBiz.setBaseEntityModify(upEntity, requestModel.getSolveUserName());
        tWorkOrderMapper.updateByPrimaryKeySelectiveEncrypt(upEntity);

        // 记录处理信息
        TWorkOrderProcess processEntity = new TWorkOrderProcess();
        processEntity.setWorkOrderId(workOrder.getId());
        processEntity.setStatus(requestModel.getStatus());
        processEntity.setSolveDesc(WorkOrderProcessDescEnum.getDesc(requestModel.getStatus()));
        processEntity.setSolveSource(requestModel.getSolveSource());
        processEntity.setSolveUserName(requestModel.getSolveUserName());
        processEntity.setSolveTime(requestModel.getSolveTime());
        processEntity.setSolveRemark(requestModel.getSolveRemark());
        commonBiz.setBaseEntityAdd(processEntity, requestModel.getSolveUserName());
        tWorkOrderProcessMapper.insertSelective(processEntity);
        return true;
    }

    /**
     * 检查上报合法性及填充所需数据
     *
     * @param requestModel 请求Model
     * @param isBuilder 是否构建 Entity
     */
    private WorkOrderReportBoModel reportCheckAndIsBuilderWorkEntity(ReportWorkExceptionRequestModel requestModel, boolean isBuilder) {

        WorkOrderReportBoModel boModel = new WorkOrderReportBoModel();
        TWorkOrder entity = null;
        if (isBuilder) {
            entity = MapperUtils.mapper(requestModel, TWorkOrder.class);
            // 获取图片路径
            String path = this.uploadPicture(requestModel.getArriveScenePicture());
            entity.setArriveScenePicture(path);
        }

        boolean isDemandType = WorkOrderTypeEnum.DEMAND_ORDER_TYPE.getKey().equals(requestModel.getWorkOrderType());
        BiConsumer<Boolean, CarrierDataExceptionEnum> isTrueException = (isTrue, ex) -> {
            if (isTrue) throw new BizException(ex);
        };

        // 上报异常类型、状态校验
        BiPredicate<Integer, Integer> checkPre = (entrustType, orderStatus) -> {
            if (!EntrustTypeEnum.RECYCLE_IN.getKey().equals(entrustType) && !EntrustTypeEnum.RECYCLE_OUT.getKey().equals(entrustType)) {
                return true;
            }
            List<Integer> checkStatusList = isDemandType ? Lists.newArrayList(DemandOrderStatusEnum.WAIT_PUBLISH.getKey(),
                    DemandOrderStatusEnum.WAIT_DISPATCH.getKey()) :
                    Lists.newArrayList(CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey(),
                            CarrierOrderStatusEnum.WAIT_LOAD.getKey());
            return !checkStatusList.contains(orderStatus);
        };

        // 需求单校验
        if (isDemandType) {
            // 查询需求单信息
            TDemandOrder tDemandOrder = Optional.ofNullable(demandOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getOrderId()))
                    .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.DEMAND_ORDER_NOT_EXIST));
            // 需求单校验
            isTrueException.accept(Lists.newArrayList(tDemandOrder.getIfCancel(), tDemandOrder.getIfEmpty(), tDemandOrder.getIfRollback())
                    .contains(CommonConstant.INTEGER_ONE), CarrierDataExceptionEnum.REPORT_ERROR_DEMAND_ORDER_ERROR_FOR_LEYI);
            isTrueException.accept(checkPre.test(tDemandOrder.getEntrustType(), tDemandOrder.getStatus()),
                    CarrierDataExceptionEnum.REPORT_ERROR_DEMAND_ORDER_ERROR_FOR_LEYI);
            if (CommonConstant.INTEGER_ONE.equals(requestModel.getReportSource())) {
                // 获取当前登陆人的手机号
                UserDetailResponseModel userDetailResponseModel = basicServiceClient.getUserInfo(BaseContextHandler.getUserId());
                if (Objects.nonNull(userDetailResponseModel)) {
                    boModel.setReportByPhone(userDetailResponseModel.getMobilePhone());
                }
            }
            // 校验车主
            else {
                TCarrierContact tCarrierContact = this.checkCarrierOrderCompanyCarrierForWeb(tDemandOrder.getCompanyCarrierId());
                if (isBuilder) {
                    boModel.setReportByPhone(tCarrierContact.getContactPhone());
                }
            }

            // 填充数据
            if (entity != null) {
                entity.setCompanyCarrierType(tDemandOrder.getCompanyCarrierType());
                entity.setCompanyCarrierId(tDemandOrder.getCompanyCarrierId());
                entity.setCompanyCarrierName(tDemandOrder.getCompanyCarrierName());
                entity.setCarrierContactId(tDemandOrder.getCarrierContactId());
                entity.setCarrierContactName(tDemandOrder.getCarrierContactName());
                entity.setCarrierContactPhone(tDemandOrder.getCarrierContactPhone());
                entity.setDemandOrderId(tDemandOrder.getId());
                entity.setDemandOrderCode(tDemandOrder.getDemandOrderCode());
            }
            boModel.setBusCode(tDemandOrder.getDemandOrderCode());
            boModel.setCustomer(tDemandOrder.getUpstreamCustomer());
            boModel.setClient(tDemandOrder.getPublishName());
        }

        // 运单校验
        else {
            String reportByPhone = null;
            // 查询运单信息
            TCarrierOrder order = Optional.ofNullable(carrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getOrderId()))
                    .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST));
            // 运单状态校验
            isTrueException.accept(Lists.newArrayList(order.getIfCancel(), order.getIfEmpty()).contains(CommonConstant.INTEGER_ONE),
                    CarrierDataExceptionEnum.REPORT_ERROR_CARRIER_ORDER_STATUS_ERROR);
            isTrueException.accept(checkPre.test(order.getDemandOrderEntrustType(), order.getStatus()),
                    CarrierDataExceptionEnum.REPORT_ERROR_CARRIER_ORDER_STATUS_ERROR);
            if (CommonConstant.INTEGER_TWO.equals(requestModel.getReportSource())) {
                // 校验车主信息
                TCarrierContact tCarrierContact = this.checkCarrierOrderCompanyCarrierForWeb(order.getCompanyCarrierId());
                reportByPhone = tCarrierContact.getContactPhone();
            }
            // 校验司机信息
            else if (CommonConstant.INTEGER_THREE.equals(requestModel.getReportSource())) {
                this.checkDriver(order.getCompanyCarrierId());
            }

            //查询运单上司机信息
            TCarrierOrderVehicleHistory driverInfo =
                    Optional.ofNullable(carrierOrderVehicleHistoryMapper.getValidTopByCarrierOrderId(order.getId()))
                            .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST));
            if (entity != null) {
                entity.setDriverId(driverInfo.getDriverId());
                entity.setDriverName(driverInfo.getDriverName());
                entity.setDriverMobile(driverInfo.getDriverMobile());
            }
            // 查询提报人手机号
            if (CommonConstant.INTEGER_ONE.equals(requestModel.getReportSource())) {
                // 获取当前登陆人的手机号
                UserDetailResponseModel userDetailResponseModel = basicServiceClient.getUserInfo(BaseContextHandler.getUserId());
                if (Objects.nonNull(userDetailResponseModel)) {
                    reportByPhone = userDetailResponseModel.getMobilePhone();
                }
            } else if (CommonConstant.INTEGER_THREE.equals(requestModel.getReportSource())) {
                reportByPhone = driverInfo.getDriverMobile();
            }
            boModel.setReportByPhone(reportByPhone);
            boModel.setVehicleNumber(driverInfo.getVehicleNo());

            // 填充数据
            if (entity != null) {
                entity.setCompanyCarrierType(order.getCompanyCarrierType());
                entity.setCompanyCarrierId(order.getCompanyCarrierId());
                entity.setCompanyCarrierName(order.getCompanyCarrierName());
                entity.setCarrierContactId(order.getCarrierContactId());
                entity.setCarrierContactName(order.getCarrierContactName());
                entity.setCarrierContactPhone(order.getCarrierContactPhone());
                entity.setDemandOrderId(order.getDemandOrderId());
                entity.setDemandOrderCode(order.getDemandOrderCode());
                entity.setCarrierOrderId(order.getId());
                entity.setCarrierOrderCode(order.getCarrierOrderCode());
            }
            boModel.setBusCode(order.getCarrierOrderCode());
            boModel.setCustomer(order.getUpstreamCustomer());
            boModel.setClient(order.getPublishName());
        }

        boModel.setEntity(entity);
        return boModel;
    }

    // 司机运单校验
    private void checkDriver(Long companyCarrierId) {
        //查询登录司机与车主关系信息
        List<CarrierDriverRelationModel> relationModelList = commonBiz.getLoginUserDriver();
        if (ListUtils.isEmpty(relationModelList)){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_NOT_EXIST);
        }

        //匹配运单上的车主与登录司机关联的车主是否有匹配的
        if (relationModelList.stream().noneMatch(a -> a.getCompanyCarrierId().equals(companyCarrierId))) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
    }

    // 车主校验
    public TCarrierContact checkCarrierOrderCompanyCarrierForWeb(Long carrierOrderCarrierId) {
        //获取当前登陆人公司id
        TCarrierContact loginUserCompanyCarrier = commonBiz.getLoginUserCompanyCarrier();
        if (loginUserCompanyCarrier == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrier.getCompanyCarrierId())) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }

        if (!loginUserCompanyCarrier.getCompanyCarrierId().equals(carrierOrderCarrierId)) {
            //判断当前运单是否当前车主的
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        return loginUserCompanyCarrier;
    }

    /**
     * 获取当前司机待处理工单数量
     *
     * @param driverId
     * @return
     */
    public Integer getPendingWorkOrderCount(Long driverId) {
        return tWorkOrderMapper.selectPendingOrderCountByDriverId(driverId);
    }

    // 上传现场图片
    private String uploadPicture(String arriveScenePicture) {
        String path = "";
        if (StringUtils.isNotBlank(arriveScenePicture)) {
            Integer fileType = CopyFileTypeEnum.WORK_ORDER_DRIVER_ARRIVE_SCENE.getKey();
            path = commonBiz.copyFileToDirectoryOfType(fileType, "", arriveScenePicture, null);
        }
        return path;
    }

    /**
     * 查询需要排除的单据号(需求单列表,运单列表 使用)
     *
     * @param workOrderTypeEnum 单子类型
     * @param requestSource     请求类型 1后台 2前台 3小程序
     * @param companyCarrierId  车主id
     * @param driverIds         司机id
     * @return 需要排除的单号
     */
    public List<Long> getNeedExcludedOrderIds(WorkOrderTypeEnum workOrderTypeEnum, Integer requestSource, Long companyCarrierId, List<Long> driverIds) {
        List<Long> responseOrderIdList = new ArrayList<>();
        //后台查询
        if (CommonConstant.INTEGER_ONE.equals(requestSource)) {
            //查询异常工单
            List<WorkOrderPendingOrderModel> workOrderPendingOrderModels = tWorkOrderMapper.selectAllPendingOrderIds();
            responseOrderIdList = getOrderIdList(workOrderTypeEnum, responseOrderIdList, workOrderPendingOrderModels);
        } else if (CommonConstant.INTEGER_TWO.equals(requestSource)) {
            //前台查询
            //查询异常工单
            List<WorkOrderPendingOrderModel> workOrderPendingOrderModels = tWorkOrderMapper.selectPendingOrderIdByCarrierId(companyCarrierId);
            responseOrderIdList = getOrderIdList(workOrderTypeEnum, responseOrderIdList, workOrderPendingOrderModels);
        } else if (CommonConstant.INTEGER_THREE.equals(requestSource)) {
            //小程序查询
            //司机运单
            //查询异常工单
            List<Long> excludeCarrierOrderIdList = tWorkOrderMapper.selectPendingOrderIdByDriverIds(driverIds);
            if (ListUtils.isNotEmpty(excludeCarrierOrderIdList)) {
                responseOrderIdList = excludeCarrierOrderIdList;
            }
        }
        return responseOrderIdList;
    }

    private List<Long> getOrderIdList(WorkOrderTypeEnum workOrderTypeEnum, List<Long> responseOrderIdList, List<WorkOrderPendingOrderModel> workOrderPendingOrderModels) {
        if (WorkOrderTypeEnum.CARRIER_ORDER_TYPE.equals(workOrderTypeEnum)) {
            //运单
            if (ListUtils.isNotEmpty(workOrderPendingOrderModels)) {
                responseOrderIdList = workOrderPendingOrderModels.stream().map(WorkOrderPendingOrderModel::getCarrierOrderId).distinct().collect(Collectors.toList());
            }
        } else if (WorkOrderTypeEnum.DEMAND_ORDER_TYPE.equals(workOrderTypeEnum)) {
            //需求单
            if (ListUtils.isNotEmpty(workOrderPendingOrderModels)) {
                responseOrderIdList = workOrderPendingOrderModels.stream().map(WorkOrderPendingOrderModel::getDemandOrderId).distinct().collect(Collectors.toList());
            }
        }
        return responseOrderIdList;
    }
}
