package com.logistics.management.webapi.client.messagenotice;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.messagenotice.hystrix.MessageNoticeClientHystrix;
import com.logistics.management.webapi.client.messagenotice.request.ReadMessageNoticeRequestModel;
import com.logistics.management.webapi.client.messagenotice.request.SearchMessageNoticeListRequestModel;
import com.logistics.management.webapi.client.messagenotice.response.SearchMessageNoticeListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2024/6/5 16:15
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/messageNotice",
        fallback = MessageNoticeClientHystrix.class)
public interface MessageNoticeClient {

    /**
     * 消息列表
     *
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/searchList")
    @ApiOperation(value = "消息列表")
    Result<PageInfo<SearchMessageNoticeListResponseModel>> searchList(@RequestBody SearchMessageNoticeListRequestModel requestModel);

    /**
     * 置为已读
     *
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/readMessageNotice")
    @ApiOperation(value = "置为已读")
    Result<Boolean> readMessageNotice(@RequestBody ReadMessageNoticeRequestModel requestModel);
}
