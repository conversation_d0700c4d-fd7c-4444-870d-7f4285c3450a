package com.logistics.management.webapi.controller.vehiclesettlement;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.constant.ExportExcelVehicleSettlement;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.base.utils.WordBarCodeUtils;
import com.logistics.management.webapi.client.vehiclesettlement.VehicleSettlementClient;
import com.logistics.management.webapi.client.vehiclesettlement.request.*;
import com.logistics.management.webapi.client.vehiclesettlement.response.*;
import com.logistics.management.webapi.controller.vehiclesettlement.mapping.*;
import com.logistics.management.webapi.controller.vehiclesettlement.request.*;
import com.logistics.management.webapi.controller.vehiclesettlement.response.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * @author: wjf
 * @date: 2024/2/22 9:19
 */
@Slf4j
@RestController
@Api(value = "车辆结算", tags = "车辆结算")
@RequestMapping(value = "/api/vehicleSettlement")
public class VehicleSettlementController {

    @Resource
    private VehicleSettlementClient vehicleSettlementClient;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 车辆结算列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "车辆结算列表 v1.0.7")
    @PostMapping(value = "/searchVehicleSettlementList")
    public Result<PageInfo<SearchVehicleSettlementListResponseDto>> searchVehicleSettlementList(@RequestBody SearchVehicleSettlementListRequestDto requestDto) {
        Result<PageInfo<SearchVehicleSettlementListResponseModel>> result = vehicleSettlementClient.searchVehicleSettlementList(MapperUtils.mapper(requestDto, SearchVehicleSettlementListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SearchVehicleSettlementListResponseDto> list = MapperUtils.mapper(pageInfo.getList(),SearchVehicleSettlementListResponseDto.class,new VehicleSettlementListMapping());
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    /**
     * 车辆结算列表数量
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "车辆结算列表数量")
    @PostMapping(value = "/searchVehicleSettlementListCount")
    public Result<SearchVehicleSettlementListCountResponseDto> searchVehicleSettlementListCount(@RequestBody SearchVehicleSettlementListRequestDto requestDto) {
        Result<SearchVehicleSettlementListCountResponseModel> result = vehicleSettlementClient.searchVehicleSettlementListCount(MapperUtils.mapper(requestDto, SearchVehicleSettlementListRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),SearchVehicleSettlementListCountResponseDto.class));
    }

    /**
     * 车辆结算详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "车辆结算详情", tags = "3.17.0")
    @PostMapping(value = "/getVehicleSettlementDetail")
    public Result<GetVehicleSettlementDetailResponseDto> getVehicleSettlementDetail(@RequestBody @Valid VehicleSettlementDetailRequestDto requestDto) {
        Result<GetVehicleSettlementDetailResponseModel> result = vehicleSettlementClient.getVehicleSettlementDetail(MapperUtils.mapper(requestDto, VehicleSettlementDetailRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        if(ListUtils.isNotEmpty(result.getData().getCarrierOrderList())) {
            for (GetCarrierOrderByVehicleIdResponseModel carrierOrder : result.getData().getCarrierOrderList()) {
                if(ListUtils.isNotEmpty(carrierOrder.getTicketList())){
                    sourceSrcList.addAll(carrierOrder.getTicketList());
                }
            }
        }
        if(result.getData().getAttachment()!=null) {
            sourceSrcList.add(result.getData().getAttachment());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(),GetVehicleSettlementDetailResponseDto.class,new VehicleSettlementDetailMapping(configKeyConstant.fileAccessAddress,imageMap)));
    }

    /**
     * 导出车辆结算列表
     * @param requestDto
     * @param response
     */
    @ApiOperation(value = "导出车辆结算列表 v1.0.7")
    @GetMapping(value = "/exportVehicleSettlement")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportVehicleSettlement(SearchVehicleSettlementListRequestDto requestDto, HttpServletResponse response) {
        Result<List<SearchVehicleSettlementListResponseModel>> result = vehicleSettlementClient.exportVehicleSettlement(MapperUtils.mapper(requestDto, SearchVehicleSettlementListRequestModel.class));
        result.throwException();
        List<SearchVehicleSettlementListResponseDto> list = MapperUtils.mapper(result.getData(),SearchVehicleSettlementListResponseDto.class,new VehicleSettlementListMapping());
        String fileName = "自有车辆结算" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportExcelVehicleSettlement.getExportVehicleSettlement();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return list;
            }
        });
    }

    /**
     * 确认结算
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "确认结算")
    @PostMapping(value = "/confirmSettlement")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result confirmSettlement(@RequestBody @Valid ConfirmSettlementRequestDto requestDto) {
        if (CommonConstant.ONE.equals(requestDto.getIfAdjustFee())){
            if (!FrequentMethodUtils.isNumberOrFloatNumberTwo(requestDto.getAdjustFee())
                    || ConverterUtils.toBigDecimal(requestDto.getAdjustFee()).compareTo(BigDecimal.ZERO) <= CommonConstant.INTEGER_ZERO
                    || ConverterUtils.toBigDecimal(requestDto.getAdjustFee()).compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ONE_MILLION){
                throw new BizException(ManagementWebApiExceptionEnum.VEHICLE_SETTLEMENT_ADJUST_FEE_ERROR);
            }
            if (StringUtils.isBlank(requestDto.getAdjustRemark()) || requestDto.getAdjustRemark().length() < CommonConstant.INTEGER_TWO || requestDto.getAdjustRemark().length() > CommonConstant.INTEGER_ONE_HUNDRED){
                throw new BizException(ManagementWebApiExceptionEnum.VEHICLE_SETTLEMENT_ADJUST_REMARK_ERROR);
            }
        }else{
            requestDto.setAdjustFeeSymbol(null);
            requestDto.setAdjustFee(null);
            requestDto.setAdjustRemark(null);
        }
        if (ListUtils.isEmpty(requestDto.getInsuranceCostList())){//无保险费用，则车辆保险理赔费用不可编辑，默认给0
            requestDto.setVehicleClaimFee(CommonConstant.ZERO);
        }else{
            for (VehicleInsuranceCostRequestDto dto : requestDto.getInsuranceCostList()) {//保险应扣费用为非必填，因涉及到计算，未填数值则默认给0
                if (StringUtils.isBlank(dto.getPayCost())){
                    dto.setPayCost(CommonConstant.ZERO);
                }
            }
        }
        if(StringUtils.isNotEmpty(requestDto.getRemark()) && requestDto.getRemark().length()>CommonConstant.INTEGER_ONE_HUNDRED){
            throw new BizException(ManagementWebApiExceptionEnum.VEHICLE_SETTLEMENT_REMARK_LENGTH_ERROR);
        }
        return vehicleSettlementClient.confirmSettlement(MapperUtils.mapper(requestDto,ConfirmSettlementRequestModel.class));
    }

    /**
     * 获取年份（2019到当前年份）
     * @return
     */
    @ApiOperation(value = "获取年份（2019到当前年份）")
    @PostMapping(value = "/getYear")
    public Result<List<String>> getYear() {
        String currentYear = DateUtils.dateToString(new Date(),CommonConstant.DATE_FORMAT_ACCURATE_YEAR);
        List<String> list = new ArrayList<>();
        for (int i = ConverterUtils.toInt(currentYear); i >= CommonConstant.VEHICLE_SETTLEMENT_START_YEAR; i--){
            list.add(ConverterUtils.toString(i));
        }
        return Result.success(list);
    }

    /**
     * 车辆结算看板
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "车辆结算看板 v1.0.7")
    @PostMapping(value = "/vehicleSettlementKanBan")
    public Result<List<VehicleSettlementKanBanResponseDto>> vehicleSettlementKanBan(@RequestBody @Valid VehicleSettlementKanBanRequestDto requestDto) {
        Result<List<VehicleSettlementKanBanResponseModel>> result = vehicleSettlementClient.vehicleSettlementKanBan(MapperUtils.mapper(requestDto, VehicleSettlementKanBanRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),VehicleSettlementKanBanResponseDto.class));
    }

    /**
     * 触发定时任务生成结算
     * @return
     */
    @ApiOperation(value = "触发定时任务生成结算")
    @PostMapping(value = "/triggerGenerateVehicleSettlement")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE,timeout = 5)
    public Result<Boolean> triggerGenerateVehicleSettlement() {
        return vehicleSettlementClient.generateVehicleSettlement();
    }

    /**
     * 车辆结算详情导出pdf
     * @param requestDto
     * @param response
     */
    @ApiOperation(value = "车辆结算详情导出pdf")
    @GetMapping(value = "/exportVehicleSettlementPdf")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportVehicleSettlementPdf(@Valid VehicleSettlementIdRequestDto requestDto, HttpServletResponse response) {
        Result<GetVehicleSettlementDetailResponseModel> result = vehicleSettlementClient.getVehicleSettlementDetail(MapperUtils.mapper(requestDto,VehicleSettlementDetailRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        if(ListUtils.isNotEmpty(result.getData().getCarrierOrderList())) {
            for (GetCarrierOrderByVehicleIdResponseModel carrierOrder : result.getData().getCarrierOrderList()) {
                if(ListUtils.isNotEmpty(carrierOrder.getTicketList())){
                    sourceSrcList.addAll(carrierOrder.getTicketList());
                }
            }
        }
        if(result.getData().getAttachment()!=null) {
            sourceSrcList.add(result.getData().getAttachment());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        GetVehicleSettlementDetailResponseDto responseDto = MapperUtils.mapper(result.getData(),GetVehicleSettlementDetailResponseDto.class,new VehicleSettlementDetailMapping(configKeyConstant.fileAccessAddress,imageMap));
        Map<String, String> templateValues = new HashMap<>();
        templateValues.put("settlementYear",responseDto.getSettlementYear());
        templateValues.put("settlementMonth",responseDto.getMonth());
        templateValues.put("vehicleNo",responseDto.getVehicleNo());
        //运费
        templateValues.put("freightFeeTotal",responseDto.getFreightFeeTotal());
        templateValues.put("adjustFee",responseDto.getAdjustFee());
        templateValues.put("adjustRemark",responseDto.getAdjustRemark());
        //应扣除费用
        templateValues.put("deductingFeeTotal",responseDto.getDeductingFeeTotal());
        templateValues.put("oilFilledFeeTotal",responseDto.getOilFilledFeeTotal());
        templateValues.put("insuranceFee",responseDto.getInsuranceFee());
        templateValues.put("tireCostTotal",responseDto.getTireCostTotal());
        templateValues.put("gpsDeductingFee",responseDto.getGpsDeductingFee());
        templateValues.put("parkingDeductingFee",responseDto.getParkingDeductingFee());
        templateValues.put("loanFee",responseDto.getLoanFee());
        templateValues.put("accidentInsuranceFee",responseDto.getAccidentInsuranceFee());
        //当月合计应付运费
        templateValues.put("freightFeeTotal1",responseDto.getFreightFeeTotal());
        templateValues.put("deductingFeeTotal1",responseDto.getDeductingFeeTotal());
        templateValues.put("vehicleClaimFee",responseDto.getVehicleClaimFee());
        templateValues.put("accidentInsuranceClaimFee",responseDto.getAccidentInsuranceClaimFee());
        templateValues.put("actualExpensesPayable",responseDto.getActualExpensesPayable());

        List<Map<String, List<String[]>>> tableInsertItems = new ArrayList<>();

        //运单列表-包装业务
        Map<String, List<String[]>> tableValueMapLeYi = new HashMap<>();
        List<String[]> tableItemsLeYi = new ArrayList<>();
        List<GetCarrierOrderByVehicleIdResponseDto> carrierOrderListForLeYi = responseDto.getCarrierOrderListForLeYi();
        if (ListUtils.isNotEmpty(carrierOrderListForLeYi)){
            for (GetCarrierOrderByVehicleIdResponseDto order : carrierOrderListForLeYi) {
                tableItemsLeYi.add(new String[]{CommonConstant.BLANK_TEXT, order.getCarrierOrderCode(),order.getLoadAddressPdf(),order.getUnloadAddressPdf(),order.getAmount(),order.getDispatchFreightFee()});
            }
        }
        tableValueMapLeYi.put(CommonConstant.TABLE_KEYWORD_PACKET, tableItemsLeYi);
        tableInsertItems.add(tableValueMapLeYi);

        //运单列表-石化业务
        Map<String, List<String[]>> tableValueMap = new HashMap<>();
        List<String[]> tableItems = new ArrayList<>();
        List<GetCarrierOrderByVehicleIdResponseDto> carrierOrderList = responseDto.getCarrierOrderList();
        if (ListUtils.isNotEmpty(carrierOrderList)){
            for (GetCarrierOrderByVehicleIdResponseDto order : carrierOrderList) {
                tableItems.add(new String[]{CommonConstant.BLANK_TEXT, order.getCarrierOrderCode(),order.getLoadAddressPdf(),order.getUnloadAddressPdf(),order.getAmount(),order.getDispatchFreightFee()});
            }
        }
        tableValueMap.put(CommonConstant.TABLE_KEYWORD_SINOPEC, tableItems);
        tableInsertItems.add(tableValueMap);

        String pdfBasePath = "/tmp/htmlfont";//临时保存word和pdf的文件夹
        commonBiz.dirIfExist(pdfBasePath);
        String tempBasePath = pdfBasePath + "/";
        String fileName = responseDto.getVehicleNo() + " " +responseDto.getSettlementYear()+"年"+responseDto.getSettlementMonth()+"月对账单";
        String filePath = tempBasePath + fileName;
        String wordPath = filePath + ".docx";//word文档全路径
        //替换word模板内容生成新word文档保存到指定的文件夹
        InputStream inputStream = VehicleSettlementController.class.getResourceAsStream("/template/vehicle_settlement_detail.docx");
        WordBarCodeUtils.fillWordTemplateValues(inputStream, wordPath, templateValues, null, null, tableInsertItems);
        //将生成的word文档转换为pdf，保存在同一个路径下
        WordToPdfUtils.wordConverterToPdf(filePath);
        //下载pdf，删除生成的word文档
        String pdfPath = filePath + ".pdf";//pdf全路径
        try (InputStream is = new FileInputStream(new File(pdfPath))) {
            commonBiz.downLoadFile(fileName, "pdf", is, response);
        } catch (Exception e) {
            log.info("downLoad vehicle settlement pdf File error", e);
        } finally {
            File wordFile = new File(wordPath);
            if (wordFile.isFile() && wordFile.exists()) {
                if (!wordFile.delete()) {
                    log.info("自有车辆结算 " + wordPath + " 删除失败 ");
                }
            }
            File pdfFile = new File(pdfPath);
            if (pdfFile.isFile() && pdfFile.exists()) {
                if (!pdfFile.delete()) {
                    log.info("自有车辆结算 " + pdfPath + " 删除失败 ");
                }
            }
        }
    }

    /**
     * 发送司机-确认
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发送司机-确认 v1.0.7")
    @PostMapping(value = "/confirmSendToDriver")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> confirmSendToDriver(@RequestBody @Valid ConfirmSendToDriverRequestDto requestDto) {
        Result<Boolean> result = vehicleSettlementClient.confirmSendToDriver(MapperUtils.mapper(requestDto, ConfirmSendToDriverRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 司机确认/无需确认/撤回详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "司机确认/无需确认/撤回详情 v1.0.7")
    @PostMapping(value = "/cancelVehicleSettlementDetail")
    public Result<CancelVehicleSettlementDetailResponseDto> cancelVehicleSettlementDetail(@RequestBody @Valid CancelVehicleSettlementDetailRequestDto requestDto) {
        Result<CancelVehicleSettlementDetailResponseModel> result = vehicleSettlementClient.cancelVehicleSettlementDetail(MapperUtils.mapper(requestDto, CancelVehicleSettlementDetailRequestModel.class));
        result.throwException();
        result.getData().setCommitImageUrl(commonBiz.getImageURL(result.getData().getCommitImageUrl()));
        CancelVehicleSettlementDetailResponseDto responseDto = MapperUtils.mapper(result.getData(), CancelVehicleSettlementDetailResponseDto.class, new CancelVehicleSettlementDetailMapping(configKeyConstant));
        return Result.success(responseDto);
    }

    /**
     * 无需确认/撤回
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "无需确认/撤回 v1.0.7")
    @PostMapping(value = "/cancelVehicleSettlement")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> cancelVehicleSettlement(@RequestBody @Valid CancelVehicleSettlementRequestDto requestDto) {
        Result<Boolean> result = vehicleSettlementClient.cancelVehicleSettlement(MapperUtils.mapper(requestDto, CancelVehicleSettlementRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 车辆结算列表-结清详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "车辆结算列表-结清详情 v1.0.7")
    @PostMapping(value = "/settleFreightDetail")
    public Result<SettleFreightDetailResponseDto> settleFreightDetail(@RequestBody @Valid VehicleSettlementIdRequestDto requestDto) {
        Result<SettleFreightDetailResponseModel> result = vehicleSettlementClient.settleFreightDetail(MapperUtils.mapper(requestDto, VehicleSettlementIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),SettleFreightDetailResponseDto.class,new SettleFreightDetailMapping()));
    }

    /**
     * 车辆结算列表-结清
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "车辆结算列表-结清 v1.0.7")
    @PostMapping(value = "/settleFreight")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> settleFreight(@RequestBody @Valid SettleFreightRequestDto requestDto) {
        Result<Boolean> result = vehicleSettlementClient.settleFreight(MapperUtils.mapperNoDefault(requestDto, SettleFreightRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 车辆结算列表-账单记录-查看
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "车辆结算列表-账单记录-查看 v1.0.7")
    @PostMapping(value = "/settlementStatementRecord")
    public Result<SettlementStatementRecordResponseDto> settlementStatementRecord(@RequestBody @Valid VehicleSettlementIdRequestDto requestDto) {
        Result<SettlementStatementRecordResponseModel> result = vehicleSettlementClient.settlementStatementRecord(MapperUtils.mapperNoDefault(requestDto, VehicleSettlementIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),SettlementStatementRecordResponseDto.class,new SettlementStatementRecordMapping()));
    }

    /**
     * 车辆结算列表-处理-详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "车辆结算列表-处理-详情 v1.0.7")
    @PostMapping(value = "/settlementStatementHandleDetail")
    public Result<SettlementStatementHandleDetailResponseDto> settlementStatementHandleDetail(@RequestBody @Valid VehicleSettlementIdRequestDto requestDto) {
        Result<SettlementStatementHandleDetailResponseModel> result = vehicleSettlementClient.settlementStatementHandleDetail(MapperUtils.mapper(requestDto, VehicleSettlementIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),SettlementStatementHandleDetailResponseDto.class,new SettlementStatementHandleDetailMapping()));
    }

    /**
     * 车辆结算列表-处理
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "车辆结算列表-处理 v1.0.7")
    @PostMapping(value = "/settlementStatementHandle")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> settlementStatementHandle(@RequestBody @Valid SettlementStatementHandleRequestDto requestDto) {
        if(CommonConstant.ONE.equals(requestDto.getOperatorType())){
            if(StringUtils.isEmpty(requestDto.getReason()) || requestDto.getReason().length()>CommonConstant.INTEGER_ONE_HUNDRED){
                throw new BizException(ManagementWebApiExceptionEnum.SETTLEMENT_STATEMENT_HANDLE_REMARK_ERROR);
            }
        }else if(CommonConstant.TWO.equals(requestDto.getOperatorType())){
            if(requestDto.getAdjustCost()==null || new BigDecimal(requestDto.getAdjustCost()).compareTo(CommonConstant.BIG_DECIMAL_ZERO_NET_ZERO_ONE)<CommonConstant.INTEGER_ZERO
                    || new BigDecimal(requestDto.getAdjustCost()).compareTo(new BigDecimal(CommonConstant.INTEGER_ONE_MILLION))>CommonConstant.INTEGER_ZERO){
                throw new BizException(ManagementWebApiExceptionEnum.VEHICLESETTLEMENT_ADJUST_FEE_ERROR);
            }
        }
        Result<Boolean> result = vehicleSettlementClient.settlementStatementHandle(MapperUtils.mapper(requestDto, SettlementStatementHandleRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 车辆结算-发送司机-查询司机
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "车辆结算-发送司机-查询司机 v1.0.7")
    @PostMapping(value = "/getDriver")
    public Result<List<GetSettlementDriverResponseDto>> getDriver(@RequestBody @Valid GetSettlementDriverRequestDto requestDto) {
        Result<List<GetSettlementDriverResponseModel>> result = vehicleSettlementClient.getDriver(MapperUtils.mapper(requestDto, GetSettlementDriverRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),GetSettlementDriverResponseDto.class));
    }

    /**
     * 车辆结算-发送司机-账单列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "车辆结算-发送司机-账单列表 v1.0.7")
    @PostMapping(value = "/sendDriverSettleStatementList")
    public Result<List<SendDriverSettleStatementListResponseDto>> sendDriverSettleStatementList(@RequestBody @Valid SendDriverSettleStatementListRequestDto requestDto) {
        Result<List<SendDriverSettleStatementListResponseModel>> result = vehicleSettlementClient.sendDriverSettleStatementList(MapperUtils.mapper(requestDto, SendDriverSettleStatementListRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),SendDriverSettleStatementListResponseDto.class));
    }

    /**
     * 车辆结算详情-查询账单上该车辆未关联的轮胎费用
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "车辆结算详情-查询账单上该车辆未关联的轮胎费用 v1.0.9")
    @PostMapping(value = "/getVehicleTireByVehicleSettlementId")
    public Result<GetVehicleTireByVehicleSettlementIdResponseDto> getVehicleTireByVehicleSettlementId(@RequestBody @Valid VehicleSettlementIdRequestDto requestDto) {
        Result<GetVehicleTireByVehicleSettlementIdResponseModel> result = vehicleSettlementClient.getVehicleTireByVehicleSettlementId(MapperUtils.mapper(requestDto, VehicleSettlementIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),GetVehicleTireByVehicleSettlementIdResponseDto.class,new GetVehicleTireByVehicleSettlementIdMapping()));
    }

    /**
     * 车辆结算详情-修改关联的轮胎（待对账状态操作）
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "车辆结算详情-修改关联的轮胎（待对账状态操作） v1.0.9")
    @PostMapping(value = "/updateVehicleSettlementTire")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> updateVehicleSettlementTire(@RequestBody @Valid UpdateVehicleSettlementTireRequestDto requestDto) {
        Result<Boolean> result = vehicleSettlementClient.updateVehicleSettlementTire(MapperUtils.mapper(requestDto, UpdateVehicleSettlementTireRequestModel.class));
        result.throwException();
        return Result.success(true);
    }
}
