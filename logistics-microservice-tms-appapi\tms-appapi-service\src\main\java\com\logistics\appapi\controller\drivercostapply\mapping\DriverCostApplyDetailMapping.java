package com.logistics.appapi.controller.drivercostapply.mapping;

import com.logistics.appapi.controller.uploadfile.response.SrcUrlDto;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.constant.ConfigKeyConstant;
import com.logistics.appapi.base.enums.DriverCostApplyTypeEnum;
import com.logistics.appapi.base.enums.DriverCostAuditEnum;
import com.logistics.appapi.base.enums.InvoiceTypeEnum;
import com.logistics.appapi.client.drivercostapply.response.DriverCostApplyDetailResponseModel;
import com.logistics.appapi.client.drivercostapply.response.DriverCostApplyInvoiceResponseModel;
import com.logistics.appapi.client.drivercostapply.response.DriverCostApplyTicketModel;
import com.logistics.appapi.controller.drivercostapply.response.DriverCostApplyDetailResponseDto;
import com.logistics.appapi.controller.drivercostapply.response.DriverCostApplyInvoiceResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/5
 */
public class DriverCostApplyDetailMapping extends MapperMapping<DriverCostApplyDetailResponseModel, DriverCostApplyDetailResponseDto> {

    private final Map<String, String> imageMap;
    private final ConfigKeyConstant configKeyConstant;

    public DriverCostApplyDetailMapping(ConfigKeyConstant configKeyConstant, Map<String, String> imageMap) {
        this.imageMap = imageMap;
        this.configKeyConstant = configKeyConstant;
    }

    @Override
    public void configure() {
        DriverCostApplyDetailResponseModel source = getSource();
        DriverCostApplyDetailResponseDto destination = getDestination();

        if (source != null) {
            //申请人
            destination.setStaffName(source.getStaffName() + "_" + source.getStaffMobile());

			//审核状态
			destination.setAuditStatusLabel(DriverCostAuditEnum.getEnum(source.getAuditStatus()).getValue());
			//费用类型
			destination.setCostTypeLabel(DriverCostApplyTypeEnum.getEnum(source.getCostType()).getValue());
			//发生时间 天维度
			destination.setOccurrenceTime(source.getOccurrenceTime() != null ? DateFormatUtils.format(source.getOccurrenceTime(), CommonConstant.DATE_TO_STRING_YMD_PATTERN) : "");
			//处理凭证图片
			List<DriverCostApplyDetailResponseDto.DriverCostApplyTickDetail> srcUrlDtoList = new ArrayList<>();
			List<DriverCostApplyTicketModel> ticketList = source.getTicketList();
			if(ListUtils.isNotEmpty(ticketList)){
				ticketList.stream().collect(Collectors.groupingBy(DriverCostApplyTicketModel::getFileType)).forEach((key, value) -> {
					DriverCostApplyDetailResponseDto.DriverCostApplyTickDetail tickDetail = new DriverCostApplyDetailResponseDto.DriverCostApplyTickDetail();
					tickDetail.setType(key);
					tickDetail.setImagePathList(value.stream().map(e -> {
						SrcUrlDto srcUrlDto = new SrcUrlDto();
						srcUrlDto.setSrc(configKeyConstant.fileAccessAddress + imageMap.get(e.getFilePath()));
						srcUrlDto.setRelativePath(e.getFilePath());
						return srcUrlDto;
					}).collect(Collectors.toList()));
					srcUrlDtoList.add(tickDetail);
				});

            }
            destination.setTicketList(srcUrlDtoList);

            //处理发票图片
            List<DriverCostApplyInvoiceResponseDto> invoiceDtoList = new ArrayList<>();
            List<DriverCostApplyInvoiceResponseModel> invoiceInfoList = source.getInvoiceInfoList();
            if (ListUtils.isNotEmpty(invoiceInfoList)) {
                BigDecimal totalTaxAndPrice = BigDecimal.ZERO;
                for (DriverCostApplyInvoiceResponseModel invoiceModel : invoiceInfoList) {
                    DriverCostApplyInvoiceResponseDto invoiceDto = MapperUtils.mapper(invoiceModel, DriverCostApplyInvoiceResponseDto.class);
                    invoiceDto.setTypeLabel(InvoiceTypeEnum.getEnum(String.valueOf(invoiceModel.getType())).getValue());
                    SrcUrlDto srcUrlDto = new SrcUrlDto();
                    srcUrlDto.setRelativePath(invoiceModel.getImagePath());
                    srcUrlDto.setSrc(configKeyConstant.fileAccessAddress + imageMap.get(invoiceModel.getImagePath()));
                    invoiceDto.setImagePathList(srcUrlDto);
                    invoiceDtoList.add(invoiceDto);

                    //价税合计
                    totalTaxAndPrice = totalTaxAndPrice.add(invoiceModel.getTotalTaxAndPrice());
                }
                destination.setInvoiceCount(String.valueOf(invoiceInfoList.size()));
                destination.setInvoiceTotalTaxAndPrice(totalTaxAndPrice.stripTrailingZeros().toPlainString());
            }
            destination.setInvoiceInfoList(invoiceDtoList);
        }
    }
}
