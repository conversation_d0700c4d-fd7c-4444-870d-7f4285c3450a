package com.logistics.tms.controller.demandorder;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.demandorder.DemandOrderForLifeBiz;
import com.logistics.tms.controller.demandorder.request.*;
import com.logistics.tms.controller.demandorder.response.LifeBatchPublishDetailResponseModel;
import com.logistics.tms.controller.demandorder.response.RenewableDemandListStatisticsResponseModel;
import com.logistics.tms.controller.demandorder.response.RenewableDemandOrderDetailResponseModel;
import com.logistics.tms.controller.demandorder.response.RenewableDemandOrderResponseModel;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/27 9:36
 */
@Api(value = "新生需求单管理")
@RestController
public class DemandOrderForLifeController {

    @Resource
    private DemandOrderForLifeBiz demandOrderForLifeBiz;

    /**
     * 新生需求单列表
     *
     * @param requestModel 筛选条件
     * @return 需求单列表
     */
    @ApiOperation("新生需求单列表")
    @PostMapping(value = "/service/renewableDemandOrder/renewableDemandOrderList")
    public Result<PageInfo<RenewableDemandOrderResponseModel>> renewableDemandOrderList(@RequestBody RenewableDemandOrderRequestModel requestModel) {
        return Result.success(demandOrderForLifeBiz.renewableDemandOrderList(requestModel, false));
    }

    /**
     * 新生需求单列表导出
     *
     * @param requestModel 筛选条件
     * @return 需求单列表
     */
    @ApiOperation("新生需求单列表导出")
    @PostMapping(value = "/service/renewableDemandOrder/renewableDemandOrderListExport")
    @IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<RenewableDemandOrderResponseModel>> renewableDemandOrderListExport(@RequestBody RenewableDemandOrderRequestModel requestModel) {
        return Result.success(demandOrderForLifeBiz.renewableDemandOrderList(requestModel, true).getList());
    }

    /**
     * 新生需求单详情
     *
     * @param requestModel 需求单id
     * @return 需求单详情
     */
    @ApiOperation("新生需求单详情")
    @PostMapping(value = "/service/renewableDemandOrder/renewableDemandOrderDetail")
    public Result<RenewableDemandOrderDetailResponseModel> renewableDemandOrderDetail(@RequestBody DemandOrderDetailRequestModel requestModel) {
        return Result.success(demandOrderForLifeBiz.renewableDemandOrderDetail(requestModel));
    }

    /**
     * 新生需求单列表统计
     *
     * @param requestModel 筛选条件
     * @return 统计TAB
     */
    @ApiOperation(value = "新生需求单列表统计")
    @PostMapping(value = "/service/renewableDemandOrder/getRenewableDemandOrderListStatistics")
    public Result<RenewableDemandListStatisticsResponseModel> getRenewableDemandOrderListStatistics(@RequestBody RenewableDemandOrderRequestModel requestModel) {
        return Result.success(demandOrderForLifeBiz.getRenewableDemandOrderListStatistics(requestModel));
    }

    /**
     * 需求单批量发布详情
     */
    @ApiOperation(value = "需求单批量发布详情")
    @PostMapping(value = "/service/renewableDemandOrder/publishDetail")
    public Result<List<LifeBatchPublishDetailResponseModel>> publishDetail(@RequestBody LifeBatchPublishDetailRequestModel requestModel) {
        return Result.success(demandOrderForLifeBiz.publishDetail(requestModel));
    }

    /**
     * 新生需求单批量发布
     */
    @PostMapping(value = "/service/renewableDemandOrder/confirmPublish")
    public Result<Boolean> confirmPublish(@RequestBody LifeBatchPublishRequestModel requestModel){
        demandOrderForLifeBiz.confirmPublish(requestModel);
        return Result.success(true);
    }

    /**
     * 新生取消需求单
     */
    @PostMapping(value = "/service/renewableDemandOrder/cancelDemandOrder")
    public Result<Boolean> cancelDemandOrder(@RequestBody LifeDemandOrderCancelRequestModel requestModel){
        demandOrderForLifeBiz.cancelDemandOrder(requestModel);
        return Result.success(true);
    }

    /**
     * 新生需求单修改车主
     */
    @PostMapping(value = "/service/renewableDemandOrder/modifyCarrier")
    public Result<Boolean> modifyCarrier(@RequestBody ModifyCarrierForLifeRequestModel requestModel){
        demandOrderForLifeBiz.modifyCarrier(requestModel);
        return Result.success(true);
    }

}
