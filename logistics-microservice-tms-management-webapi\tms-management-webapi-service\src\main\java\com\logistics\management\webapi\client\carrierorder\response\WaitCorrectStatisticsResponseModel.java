package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/4 14:38
 */
@Data
public class WaitCorrectStatisticsResponseModel {
    @ApiModelProperty("待提货")
    private List<WaitLoadStatisticsModel> waitLoadList;

    @ApiModelProperty("待纠错")
    private List<WaitCorrectStatisticsModel> waitCorrectList;

}
