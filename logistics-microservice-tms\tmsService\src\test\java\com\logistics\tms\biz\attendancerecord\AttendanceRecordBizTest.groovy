package com.logistics.tms.biz.attendancerecord

import com.logistics.tms.controller.attendance.response.AttendanceClockDetailResponseModel
import com.logistics.tms.controller.attendance.request.AttendanceClockRequestModel
import com.logistics.tms.controller.attendance.response.AttendanceHistoryItemResponseModel
import com.logistics.tms.controller.attendance.request.AttendanceHistoryListRequestModel
import com.logistics.tms.controller.attendance.response.AttendanceHistoryListResponseModel
import com.logistics.tms.controller.attendance.request.QueryPathByLonAndLatRequestModel
import com.logistics.tms.controller.attendance.response.QueryPathByLonAndLatResponseModel
import com.logistics.tms.controller.attendance.request.SearchAttendanceListRequestModel
import com.logistics.tms.controller.attendance.response.SearchAttendanceListResponseModel
import com.logistics.tms.biz.attendancerecord.handle.AttendancePunchHandleFactory
import com.logistics.tms.biz.attendancerecord.handle.UpdateDutyPunchHandle
import com.logistics.tms.biz.attendancerecord.model.AttendanceStatisticalModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.controller.attendance.request.AttendanceDetailRequestModel
import com.logistics.tms.controller.attendance.response.AttendanceDetailResponseModel
import com.logistics.tms.entity.TAttendanceChangeApply
import com.logistics.tms.entity.TAttendanceRecord
import com.logistics.tms.mapper.TAttendanceChangeApplyMapper
import com.logistics.tms.mapper.TAttendanceRecordMapper
import org.slf4j.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class AttendanceRecordBizTest extends Specification {
    @Mock
    CommonBiz commonBiz
    @Mock
    AttendancePunchHandleFactory punchHandleFactory
    @Mock
    TAttendanceRecordMapper attendanceRecordMapper
    @Mock
    TAttendanceChangeApplyMapper attendanceChangeApplyMapper
    @Mock
    Logger log
    @InjectMocks
    AttendanceRecordBiz attendanceRecordBiz

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "attendance Clock Detail"() {
        given:
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)
        when(attendanceRecordMapper.selectByStaffIdAndDate(anyLong(), any())).thenReturn(new TAttendanceRecord())

        expect:
        attendanceRecordBiz.attendanceClockDetail() == expectedResult

        where:
        expectedResult << new AttendanceClockDetailResponseModel()
    }

    @Unroll
    def "query Path By Lon And Lat where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(commonBiz.getAddressByLonAndLat(anyString())).thenReturn("getAddressByLonAndLatResponse")

        expect:
        attendanceRecordBiz.queryPathByLonAndLat(requestModel) == expectedResult

        where:
        requestModel                           || expectedResult
        new QueryPathByLonAndLatRequestModel() || new QueryPathByLonAndLatResponseModel()
    }

    @Unroll
    def "attendance Clock where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)
        when(punchHandleFactory.getHandle(anyInt())).thenReturn(new UpdateDutyPunchHandle(new CommonBiz(), null))
        when(attendanceRecordMapper.selectByStaffIdAndDate(anyLong(), any())).thenReturn(new TAttendanceRecord())

        expect:
        attendanceRecordBiz.attendanceClock(requestModel) == expectedResult

        where:
        requestModel                      || expectedResult
        new AttendanceClockRequestModel() || true
    }

    @Unroll
    def "upload Attendance Punch Image where dutyPunchLocation=#dutyPunchLocation and dutyPunchPic=#dutyPunchPic then expect: #expectedResult"() {
        given:
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")

        expect:
        attendanceRecordBiz.uploadAttendancePunchImage(dutyPunchPic, dutyPunchLocation) == expectedResult

        where:
        dutyPunchLocation   | dutyPunchPic   || expectedResult
        "dutyPunchLocation" | "dutyPunchPic" || "expectedResult"
    }

    @Unroll
    def "attendance History List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)
        when(attendanceRecordMapper.selectHistoryByStaffAndAttendanceDate(anyLong(), any())).thenReturn([new AttendanceHistoryItemResponseModel()])
        when(attendanceRecordMapper.selectStatistical(anyLong(), anyString())).thenReturn(new AttendanceStatisticalModel())
        when(attendanceChangeApplyMapper.selectByAttendanceRecordIdIn(any())).thenReturn([new TAttendanceChangeApply()])

        expect:
        attendanceRecordBiz.attendanceHistoryList(requestModel) == expectedResult

        where:
        requestModel                            || expectedResult
        new AttendanceHistoryListRequestModel() || new AttendanceHistoryListResponseModel()
    }

    @Unroll
    def "search Attendance List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(attendanceRecordMapper.selectSearchAttendanceList(any())).thenReturn([new SearchAttendanceListResponseModel()])

        expect:
        attendanceRecordBiz.searchAttendanceList(requestModel) == expectedResult

        where:
        requestModel                           || expectedResult
        new SearchAttendanceListRequestModel() || null
    }

    @Unroll
    def "attendance Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(attendanceRecordMapper.selectOneDetailById(anyLong())).thenReturn(new AttendanceDetailResponseModel())

        expect:
        attendanceRecordBiz.attendanceDetail(requestModel) == expectedResult

        where:
        requestModel                                                                    || expectedResult
        new AttendanceDetailRequestModel() || new AttendanceDetailResponseModel()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme