package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2024/11/07
*/
@Data
public class TReservationVisitorRecord extends BaseEntity {
    /**
    * 访客ip
    */
    @ApiModelProperty("访客ip")
    private String ip;

    /**
    * 操作节点1.预约2.签到 3.登录
    */
    @ApiModelProperty("操作节点1.预约2.签到 3.登录")
    private Integer operateType;

    /**
    * 预约单id
    */
    @ApiModelProperty("预约单id")
    private Long reservationOrderId;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}