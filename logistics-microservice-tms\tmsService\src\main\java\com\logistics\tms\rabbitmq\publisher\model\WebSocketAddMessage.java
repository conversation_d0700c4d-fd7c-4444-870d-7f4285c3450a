package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/6/5 11:19
 */
@Data
public class WebSocketAddMessage {
    @ApiModelProperty("消息id")
    private String messageId;
    @ApiModelProperty("消息体")
    private String message;
    private Long objectId;
    private String objectCode;
    private String objectType;
    private String msgTo;
}
