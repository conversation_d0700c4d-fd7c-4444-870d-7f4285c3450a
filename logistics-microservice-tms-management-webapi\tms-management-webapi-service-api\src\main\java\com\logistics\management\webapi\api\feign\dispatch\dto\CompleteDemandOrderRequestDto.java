package com.logistics.management.webapi.api.feign.dispatch.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class CompleteDemandOrderRequestDto {
    @ApiModelProperty("需求列表信息")
    @NotBlank(message = "选中数据,逗号分割")
    private String demandOrderId;

    @ApiModelProperty("（云盘需求单必填）原因类型：10 数量问题，20 重报问题，30 联系问题，40 地址问题，50 装车问题，60 等待问题，70 其他问题")
    private String objectionType;
    @ApiModelProperty("（云盘需求单必填）描述")
    private String objectionReason;
}
