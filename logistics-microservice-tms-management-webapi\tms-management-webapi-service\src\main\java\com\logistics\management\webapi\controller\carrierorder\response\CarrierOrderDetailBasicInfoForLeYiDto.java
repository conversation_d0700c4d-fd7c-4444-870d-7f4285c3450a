package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierOrderDetailBasicInfoForLeYiDto {

    @ApiModelProperty("发货地址")
    private String loadAddress = "";
    @ApiModelProperty("实际发货地址（提货定位）")
    private String loadLocation = "";
    @ApiModelProperty("发货人")
    private String consignor = "";

    @ApiModelProperty("收货地址")
    private String unloadAddress = "";
    @ApiModelProperty("实际收货地址（卸货定位）")
    private String unloadLocation = "";
    @ApiModelProperty("收货人")
    private String receiver = "";

    @ApiModelProperty("实际提货时间")
    private String loadTime = "";
    @ApiModelProperty("实际卸货时间")
    private String unloadTime = "";
    @ApiModelProperty("卸货地址是否后补 0 否  1 是")
    private String unloadAddressIsAmend = "";

}
