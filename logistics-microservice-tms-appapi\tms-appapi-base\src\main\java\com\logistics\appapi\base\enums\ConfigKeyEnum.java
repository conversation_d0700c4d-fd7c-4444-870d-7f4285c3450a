package com.logistics.appapi.base.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ConfigKeyEnum {

    APPLET_RETURN_ADDRESS_CONFIG(SysConfigGroupCodeEnum.RETURN_ADDRESS.getCode(), "applet_return_address", "回单寄送地址"),
    CUSTOMER_SERVICE_PHONE_CONFIG(SysConfigGroupCodeEnum.CUSTOMER_SERVICE_PHONE.getCode(), "app_service_phone", "客服电话");

    private final String groupCode;
    private final String value;
    private final String remark;

    public enum SysConfigGroupCodeEnum {
        RETURN_ADDRESS,
        COMPANY_NAME,
        CUSTOMER_SERVICE_PHONE;

        public String getCode() {
            return this.name().toLowerCase();
        }
    }

}
