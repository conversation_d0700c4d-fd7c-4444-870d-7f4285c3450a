package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2018/12/27 9:01
 */
public enum EntrustAddressTypeEnum {
    DELIVERY(1, "发货"),
    RECEIVING(2, "收货"),
    ;

    private Integer key;
    private String value;

    EntrustAddressTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
