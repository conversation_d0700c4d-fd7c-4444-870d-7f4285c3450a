package com.logistics.management.webapi.client.freightconfig.response;

import com.logistics.management.webapi.client.freightconfig.request.ConfigVehicleItemModel;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class GetConfigVechicleDetailRespModel {


    /**
     * 车长
     */
    private String vehicleLength = "";


    private List<ConfigVehicleItemModel> itemReqDtos = new ArrayList<>();

}