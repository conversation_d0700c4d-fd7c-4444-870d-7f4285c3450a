package com.logistics.management.webapi.api.feign.extvehiclesettlement.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.extvehiclesettlement.ExtVehicleSettlementApi;
import com.logistics.management.webapi.api.feign.extvehiclesettlement.dto.*;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: wjf
 * @date: 2019/11/21 13:19
 */
@Component
public class ExtVehicleSettlementApiHystrix implements ExtVehicleSettlementApi {
    @Override
    public Result<PageInfo<SearchExtVehicleSettlementListResponseDto>> searchExtVehicleSettlementList(SearchExtVehicleSettlementListRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<ExtVehicleSettlementDetailResponseDto> extVehicleSettlementDetail(ExtVehicleSettlementIdRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result extVehicleSettlementPayment(ExtVehicleSettlementPaymentRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result extVehicleSettlementFallback(ExtVehicleSettlementFallbackRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void exportExtVehicleSettlement(SearchExtVehicleSettlementListRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }
}
