package com.logistics.management.webapi.api.impl.terminalreachmanagement.mapping;

import com.logistics.management.webapi.api.feign.terminalreachmanagement.dto.GetReachManagementDetailResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CheckReachContactEnum;
import com.logistics.tms.api.feign.terminalreachmanagement.model.GetReachManagementDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class GetReachManagementDetailMapping extends MapperMapping<GetReachManagementDetailResponseModel, GetReachManagementDetailResponseDto> {

    private Map<String, String> imageMap;

    private String imagePrefix;

    public GetReachManagementDetailMapping() {
    }

    public GetReachManagementDetailMapping(String imagePrefix, Map<String, String> imageMap) {
        this.imageMap = imageMap;
        this.imagePrefix = imagePrefix;
    }

    @Override
    public void configure() {
        GetReachManagementDetailResponseModel source = getSource();
        GetReachManagementDetailResponseDto destination = getDestination();
        if (source != null){
            destination.setConsignor(source.getConsignorName() + " " + source.getConsignorMobile());
            destination.setLoadDetailAddress(source.getLoadProvinceName() + source.getLoadCityName() + source.getLoadAreaName() + source.getLoadDetailAddress());

            destination.setReachDriver(source.getReachDriverName() + " " + source.getReachDriverPhone());
            destination.setReachTime(DateUtils.dateToString(source.getReachTime(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
            destination.setCheckReachContactLabel(CheckReachContactEnum.getEnum(source.getCheckReachContact()).getValue());
            destination.setReachContactor(source.getReachContactor() + " " + source.getReachTelephone());
            destination.setReachAddress(source.getReachProvinceName() + source.getReachCityName() + source.getReachAreaName() + source.getReachAddressDetail());
            destination.setDistanceDeviation(Objects.isNull(source.getDistanceDeviation())? CommonConstant.ZERO:new BigDecimal(source.getDistanceDeviation()).setScale(CommonConstant.INTEGER_TWO, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());

            if (Objects.nonNull(imageMap)){
                source.getAttachmentMap().forEach((k, v) -> source.getAttachmentMap().put(k, v.stream().map(v1-> imagePrefix + imageMap.get(v1)).collect(Collectors.toList())));
                destination.setAttachmentMap(source.getAttachmentMap());
            }
        }
    }

}
