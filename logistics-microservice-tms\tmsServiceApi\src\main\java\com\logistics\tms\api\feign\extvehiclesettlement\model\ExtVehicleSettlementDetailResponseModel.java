package com.logistics.tms.api.feign.extvehiclesettlement.model;

import com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/11/20 13:53
 */
@Data
public class ExtVehicleSettlementDetailResponseModel {
    @ApiModelProperty("外部车辆结算ID")
    private Long extVehicleSettlementId;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("结算数量")
    private BigDecimal settlementAmount;
    @ApiModelProperty("单位：1 件，2 吨，3 方，4 块")
    private Integer settlementUnit;
    @ApiModelProperty("司机合计费用")
    private BigDecimal driverTotalFee;
    @ApiModelProperty("付款通道")
    private String paymentChannel;
    @ApiModelProperty("付款单号")
    private String paymentNo;
    @ApiModelProperty("应支付费用")
    private BigDecimal paymentFee;
    @ApiModelProperty("报销费用")
    private BigDecimal reimburseFee;
    @ApiModelProperty("合计总费用")
    private BigDecimal totalFee;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("附件")
    private List<String> attachmentList;

    private List<SearchCarrierOrderListGoodsInfoModel> goodsInfoList;

    @ApiModelProperty("司机临时费用")
    private BigDecimal driverOtherFee;

    @ApiModelProperty("是否放空 0否 1是")
    private Integer ifEmpty;
    @ApiModelProperty("车主id")
    private Long companyCarrierId;
}
