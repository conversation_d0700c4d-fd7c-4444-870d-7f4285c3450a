package com.logistics.management.webapi.api.impl.entrustsettlement;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.entrustsettlement.EntrustSettlementApi;
import com.logistics.management.webapi.api.feign.entrustsettlement.dto.*;
import com.logistics.management.webapi.api.impl.entrustsettlement.mapping.*;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportExcelEntrustSettlement;
import com.logistics.tms.api.feign.entrustsettlement.EntrustSettlementServiceApi;
import com.logistics.tms.api.feign.entrustsettlement.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ExcelUtils;
import com.yelo.tools.utils.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
/**
 * @author: wjf
 * @date: 2019/10/11 19:52
 */
@RestController
public class EntrustSettlementApiImpl implements EntrustSettlementApi {

    @Autowired
    private EntrustSettlementServiceApi entrustSettlementServiceApi;

    /**
     * 查询委托方结算列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<EntrustSettlementListResponseDto> entrustSettlementList(@RequestBody EntrustSettlementListRequestDto requestDto) {
        Result<EntrustSettlementListResponseModel> result = entrustSettlementServiceApi.entrustSettlementList(MapperUtils.mapperNoDefault(requestDto, EntrustSettlementListRequestModel.class));
        result.throwException();
        EntrustSettlementListResponseDto dto = MapperUtils.mapper(result.getData(),EntrustSettlementListResponseDto.class,new EntrustSettlementListTotalMapping());
        if (result.getData() != null && result.getData().getPageInfo() != null){
            PageInfo<EntrustSettlementRowModel> data = result.getData().getPageInfo();
            List<EntrustSettlementRowDto> mapper = MapperUtils.mapper(data.getList(),EntrustSettlementRowDto.class,new EntrustSettlementListMapping());
            PageInfo<EntrustSettlementRowDto> pageInfo = MapperUtils.mapper(data,PageInfo.class);
            pageInfo.setList(mapper == null ? new ArrayList<>() : mapper);
            dto.setPageInfo(pageInfo);
        }
        return Result.success(dto);
    }

    /**
     * 导出委托方结算列表
     * @param requestDto
     * @param response
     */
    @Override
    public void exportEntrustSettlementList(EntrustSettlementListRequestDto requestDto, HttpServletResponse response) {
        Result<List<EntrustSettlementRowModel>> listResult = entrustSettlementServiceApi.exportEntrustSettlementList(MapperUtils.mapperNoDefault(requestDto,EntrustSettlementListRequestModel.class));
        listResult.throwException();
        List<EntrustSettlementRowDto> list = MapperUtils.mapper(listResult.getData(),EntrustSettlementRowDto.class,new EntrustSettlementListMapping());
        String fileName = "货主结算费用" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportExcelEntrustSettlement.getExportEntrustSettlement();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return list;
            }
        });
    }

    /**
     * 修改费用
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result modifyCost(@RequestBody @Valid ModifyCostRequestDto requestDto) {
        Result result = entrustSettlementServiceApi.modifyCost(MapperUtils.mapper(requestDto, ModifyCostRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 结算详情，确认收款，回退界面
     * @param requestDto
     * @return
     */
    @Override
    public Result<GetSettlementDetailResponseDto> getSettlementDetail(@RequestBody @Valid GetSettlementDetailRequestDto requestDto) {
        Result<GetSettlementDetailResponseModel> result = entrustSettlementServiceApi.getSettlementDetail(MapperUtils.mapper(requestDto,GetSettlementDetailRequestModel.class));
        result.throwException();
        GetSettlementDetailResponseDto dto = MapperUtils.mapper(result.getData(),GetSettlementDetailResponseDto.class,new SettlementDetailTotalMapping());
        if (result.getData() != null && ListUtils.isNotEmpty(result.getData().getSettlementRows())){
            List<GetSettlementDetailRowModel> modelList = result.getData().getSettlementRows();
            List<GetSettlementDetailRowDto> dtoList = MapperUtils.mapper(modelList,GetSettlementDetailRowDto.class,new SettlementDetailMapping());
            dto.setSettlementRows(dtoList);
        }
        return Result.success(dto);
    }

    /**
     * 结算详情，修改费用
     * @param requestDto
     * @return
     */
    @Override
    public Result<GetDetailResponseDto> getDetail(@RequestBody @Valid GetDetailRequestDto requestDto) {
        Result<GetDetailResponseModel> result = entrustSettlementServiceApi.getDetail(MapperUtils.mapper(requestDto,GetDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),GetDetailResponseDto.class,new GetDetailMapping()));
    }

    /**
     * 已收款
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result receiveMoney(@RequestBody @Valid ReceiveMoneyRequestDto requestDto) {
        Result result = entrustSettlementServiceApi.receiveMoney(MapperUtils.mapper(requestDto, GetSettlementDetailRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 退款
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result refund(@RequestBody @Valid RefundRequestDto requestDto) {
        Result result = entrustSettlementServiceApi.refund(MapperUtils.mapper(requestDto, RefundRequestModel.class));
        result.throwException();
        return Result.success(true);
    }
}
