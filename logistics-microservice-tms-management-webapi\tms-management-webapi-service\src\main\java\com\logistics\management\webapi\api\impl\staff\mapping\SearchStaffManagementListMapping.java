package com.logistics.management.webapi.api.impl.staff.mapping;

import com.logistics.management.webapi.api.feign.staff.dto.SearchStaffManagementListResponseDto;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.staff.model.SearchStaffManagementListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2019/6/10 17:26
 */
public class SearchStaffManagementListMapping extends MapperMapping<SearchStaffManagementListResponseModel,SearchStaffManagementListResponseDto> {
    @Override
    public void configure() {
        SearchStaffManagementListResponseModel source = getSource();
        SearchStaffManagementListResponseDto destination = getDestination();
        if (source != null){
            ImportStaffPropertyEnum importStaffPropertyEnum = ImportStaffPropertyEnum.getEnum(source.getStaffProperty());
            if((importStaffPropertyEnum != null)){
                destination.setStaffPropertyLabel(importStaffPropertyEnum.getValue());
            }
            destination.setStaffType(StaffTypeEnum.getEnum(source.getStaffType()).getValue());
            destination.setGender(GenderEnum.getEnum(source.getGender()).getValue());
            destination.setStaffName(source.getStaffName() + " " + source.getStaffMobile());

            destination.setCompanyCarrierName(CompanyTypeEnum.COMPANY.getKey().equals(source.getCompanyCarrierType()) ?
                    source.getCompanyCarrierName() :
                    source.getCarrierContactName() + " "
                            + FrequentMethodUtils.encryptionData(source.getCarrierContactPhone(), EncodeTypeEnum.MOBILE_PHONE));

            //实名展示文本
            destination.setRealNameAuthenticationStatusLabel(RealNameAuthenticationStatusEnum.getEnum(source.getRealNameAuthenticationStatus()).getValue());

            //云仓开关
            destination.setWarehouseSwitchLabel(SwitchEnum.getEnum(source.getWarehouseSwitch()).getValue());
        }
    }
}
