package com.logistics.tms.controller.companyentrust.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/9/27 14:24
 */
@Data
public class CompanyEntrustDetailResponseModel {

    private Long companyEntrustId;
    private String companyName;
    @ApiModelProperty("营业执照")
    private String tradingCertificateImage;
    private Date tradingCertificateValidityTime;
    private Integer tradingCertificateIsForever;
    private Integer tradingCertificateIsAmend;
    @ApiModelProperty("结算吨位")
    private Integer settlementTonnage;
    private String companyShortName;
    private Integer ifAudit;
    private Integer signMode;
    private Integer signDays;
    @ApiModelProperty("货主类型")
    private Integer type;
    private String remark;
}
