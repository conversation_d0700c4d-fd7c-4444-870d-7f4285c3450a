package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/2
 */
@Data
public class EditCarrierOrderCostDetailResponseModel {

	@ApiModelProperty(value = "运单ID")
	private Long carrierOrderId;

	@ApiModelProperty(value = "价格类型 1 单价 2 一口价")
	private Integer priceType;

	@ApiModelProperty("货物单位：1 件，2 吨,3方,4块")
	private Integer goodsUnit;

	@ApiModelProperty("货物数量")
	private BigDecimal goodsAmount;

	@ApiModelProperty("车主费用/货主费用")
	private BigDecimal carrierFreight;
}
