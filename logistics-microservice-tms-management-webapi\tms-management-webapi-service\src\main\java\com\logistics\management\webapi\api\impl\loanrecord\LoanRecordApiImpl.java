package com.logistics.management.webapi.api.impl.loanrecord;

import com.github.pagehelper.PageInfo;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import com.logistics.management.webapi.api.feign.loanrecord.LoanRecordApi;
import com.logistics.management.webapi.api.feign.loanrecord.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.loanrecord.mapping.LoanOperationRecordMapping;
import com.logistics.management.webapi.api.impl.loanrecord.mapping.LoanRecordDetailMapping;
import com.logistics.management.webapi.api.impl.loanrecord.mapping.LoanRecordListMapping;
import com.logistics.management.webapi.base.constant.*;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.tms.api.feign.loanrecord.LoanRecordServiceApi;
import com.logistics.tms.api.feign.loanrecord.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;

/**
 * @Author: sj
 * @Date: 2019/9/30 9:55
 */
@RestController
public class LoanRecordApiImpl implements LoanRecordApi {
    @Autowired
    private LoanRecordServiceApi loanRecordServiceApi;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 查询贷款记录列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<LoanRecordListResponseDto>> searchList(@RequestBody LoanRecordListRequestDto requestDto) {
        Result<PageInfo<LoanRecordListResponseModel>> result = loanRecordServiceApi.searchList(MapperUtils.mapper(requestDto, LoanRecordListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<LoanRecordListResponseDto> dtoList = MapperUtils.mapper(result.getData().getList(),LoanRecordListResponseDto.class,new LoanRecordListMapping());
        pageInfo.setList(dtoList == null ? new ArrayList() : dtoList);
        return Result.success(pageInfo);
    }

    /**
     * 获取列表汇总数据
     * @param requestDto
     * @return
     */
    @Override
    public Result<SummaryLoanRecordResponseDto> getSummary(@RequestBody LoanRecordListRequestDto requestDto) {
        Result<SummaryLoanRecordResponseModel> result = loanRecordServiceApi.getSummary(MapperUtils.mapper(requestDto,LoanRecordListRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),SummaryLoanRecordResponseDto.class));
    }

    /**
     * 新增/修改
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> saveOrUpdate(@RequestBody @Valid SaveOrUpdateLoanRecordRequestDto requestDto) {
        if(StringUtils.isNotBlank(requestDto.getIdentityNumber()) && !IDCardValidator.isValidatedAllIdcard(requestDto.getIdentityNumber())){
            throw new BizException(ManagementWebApiExceptionEnum.ID_CARD_FORMAT_ERROR);
        }
        //附件信息
        if(ListUtils.isEmpty(requestDto.getFileList()) || requestDto.getFileList().size()> CommonConstant.INTEGER_THREE){
            throw new BizException(ManagementWebApiExceptionEnum.LOAN_RECORD_FILE_COUNT_MAX);
        }
        Result<Boolean> result = loanRecordServiceApi.saveOrUpdate(MapperUtils.mapperNoDefault(requestDto, SaveOrUpdateLoanRecordRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<LoanRecordDetailResponseDto> getDetail(@RequestBody @Valid LoanRecordDetailRequestDto requestDto) {
        Result<LoanRecordDetailResponseModel> result = loanRecordServiceApi.getDetail(MapperUtils.mapper(requestDto, LoanRecordDetailRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (LoanRecordFileResponseModel ticketsModel : result.getData().getFileList()) {
            sourceSrcList.add(ticketsModel.getRelativeFilepath());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(),LoanRecordDetailResponseDto.class,new LoanRecordDetailMapping(configKeyConstant,imageMap)));
    }

    /**
     * 查询操作记录
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<LoanOperationRecordResponseDto>> getOperationRecords(@RequestBody LoanOperationRecordRequestDto requestDto) {
        Result<List<LoanOperationRecordResponseModel>> result = loanRecordServiceApi.getOperationRecords(MapperUtils.mapper(requestDto,LoanOperationRecordRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),LoanOperationRecordResponseDto.class,new LoanOperationRecordMapping()));
    }

    /**
     * 查询结算记录
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<LoanSettlementRecordResponseDto>> getSettlementRecords(@RequestBody LoanSettlementRecordRequestDto requestDto) {
        Result<List<LoanSettlementRecordResponseModel>> result = loanRecordServiceApi.getSettlementRecords(MapperUtils.mapper(requestDto,LoanSettlementRecordRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),LoanSettlementRecordResponseDto.class));
    }

    /**
     * 导出贷款记录
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportLoanRecords(LoanRecordListRequestDto requestDto, HttpServletResponse response) {
        Result<List<LoanRecordListResponseModel>> result = loanRecordServiceApi.exportLoanRecords(MapperUtils.mapper(requestDto,LoanRecordListRequestModel.class));
        result.throwException();
        List<LoanRecordListResponseDto> dtoList = MapperUtils.mapper(result.getData(),LoanRecordListResponseDto.class,new LoanRecordListMapping());

        String fileName = "贷款费用" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportLoanRecordsInfo.getExportLoanRecordMap();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return dtoList;
            }
        });
    }

    /**
     * 导出结算记录
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportSettlementRecords(LoanSettlementRecordRequestDto requestDto, HttpServletResponse response) {
        Result<ExportSettlementRecordResponseModel> result = loanRecordServiceApi.exportSettlementRecords(MapperUtils.mapper(requestDto,LoanSettlementRecordRequestModel.class));
        result.throwException();
        String vehicleNo = Optional.ofNullable(result.getData().getVehicleNo()).orElse("");
        List<LoanSettlementRecordResponseDto> dtoList = MapperUtils.mapper(result.getData().getSettlementRecordList(),LoanSettlementRecordResponseDto.class);

        String fileName = vehicleNo+"贷款结算记录" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportSettlementRecords.getExportSettlementRecordsMap();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return dtoList;
            }
        });
    }

    /**
     * 导出操作记录
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportOperationRecords(LoanOperationRecordRequestDto requestDto, HttpServletResponse response) {
        Result<ExportOperationRecordResponseModel> result = loanRecordServiceApi.exportOperationRecords(MapperUtils.mapper(requestDto,LoanOperationRecordRequestModel.class));
        result.throwException();
        String vehicleNo = Optional.ofNullable(result.getData().getVehicleNo()).orElse("");
        List<LoanOperationRecordResponseDto> dtoList = MapperUtils.mapper(result.getData().getOperationRecordList(),LoanOperationRecordResponseDto.class,new LoanOperationRecordMapping());

        String fileName = vehicleNo+"贷款费用操作记录" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportLoanOperationRecords.getOperationMap();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return dtoList;
            }
        });
    }
}
