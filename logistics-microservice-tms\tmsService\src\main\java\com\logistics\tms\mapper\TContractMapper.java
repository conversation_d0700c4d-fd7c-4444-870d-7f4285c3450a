package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.contractorder.model.ContractOrderDetailResponseModel;
import com.logistics.tms.api.feign.contractorder.model.ContractOrderSearchRequestModel;
import com.logistics.tms.api.feign.contractorder.model.ContractOrderSearchResponseModel;
import com.logistics.tms.entity.TContract;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TContractMapper extends BaseMapper<TContract> {

    ContractOrderDetailResponseModel getContractDetail(@Param("contractId") Long contractId);

    List<ContractOrderSearchResponseModel> searchContractOrderList(@Param("params") ContractOrderSearchRequestModel requestModel);

    List<TContract> getValidContract();

    int batchUpdate(@Param("list") List<TContract> list);
}