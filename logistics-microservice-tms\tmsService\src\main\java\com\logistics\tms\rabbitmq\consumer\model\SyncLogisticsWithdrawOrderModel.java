package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SyncLogisticsWithdrawOrderModel {
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("撤回原因")
    private String withdrawReason;
    @ApiModelProperty("操作人")
    private String operateName;

    @ApiModelProperty("委托数量  追加、补单针对回收类型单货物")
    private BigDecimal goodsAmount;
    @ApiModelProperty("回收类型 需求单关系表")
    private List<WithdrawDemandRelModel> withdrawDemandRelModelList;
}
