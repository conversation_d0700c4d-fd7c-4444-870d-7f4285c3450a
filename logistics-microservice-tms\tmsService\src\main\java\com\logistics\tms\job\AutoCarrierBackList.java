package com.logistics.tms.job;

import com.logistics.tms.biz.companycarrier.CompanyCarrierBiz;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 每天轮询查看车主授权状态 拉黑
 */
@Slf4j
@Component
public class AutoCarrierBackList {

    @Autowired
    private CompanyCarrierBiz companyCarrierBiz;

    /**
     * 每天轮询查看车主授权状态 拉黑
     */
    @XxlJob("autoCarrierBackList")
    public void autoCarrierBackList() {
        log.info("=====================自动拉黑启动====================");
        companyCarrierBiz.autoCarrierBackList();
        log.info("=====================自动拉黑结束====================");
    }


}
