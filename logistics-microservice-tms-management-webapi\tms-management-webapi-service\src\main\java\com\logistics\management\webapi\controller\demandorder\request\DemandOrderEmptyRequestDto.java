package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @author: wjf
 * @date: 2021/10/18 11:03
 */
@Data
public class DemandOrderEmptyRequestDto {
    @ApiModelProperty("委托单id")
    @NotBlank(message = "请选中数据")
    private String demandIds;
    @ApiModelProperty("客户名称")
    @Size(min = 1, max = 20, message = "请维护客户名称1~20字")
    private String customerName;
    @ApiModelProperty("放空原因类型：2 客户原因，3 不可抗力，4 物流原因，5 平台问题")
    @NotBlank(message = "请选择放空原因")
    private String objectionType;
    @ApiModelProperty("放空描述")
    @Size(min = 1, max = 100, message = "请维护放空描述1~100字")
    private String objectionReason;
}
