package com.logistics.tms.controller.freightconfig.response.address;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierFreightConfigAddressItemResponseModel {

    @ApiModelProperty(value = "路线计价配置Id")
    private Long freightConfigAddressId;

    @ApiModelProperty(value = "卸货省名字")
    private String fromProvinceName = "";

    @ApiModelProperty(value = "发货城市名字")
    private String fromCityName;

    @ApiModelProperty(value = "发货县区名字")
    private String fromAreaName;

    @ApiModelProperty(value = "卸货省名字")
    private String toProvinceName = "";

    @ApiModelProperty(value = "卸货城市名字")
    private String toCityName;

    @ApiModelProperty(value = "卸货县区名字")
    private String toAreaName;

    @ApiModelProperty(value = "操作人")
    private String lastModifiedBy;

    @ApiModelProperty(value = "操作时间")
    private String lastModifiedTime;

    @ApiModelProperty(value = "状态; 1 启用 0 禁用")
    private Integer enabled;
}
