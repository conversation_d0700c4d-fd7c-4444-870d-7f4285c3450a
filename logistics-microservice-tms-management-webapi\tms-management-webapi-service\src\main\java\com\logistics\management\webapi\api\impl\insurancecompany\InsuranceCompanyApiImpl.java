package com.logistics.management.webapi.api.impl.insurancecompany;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.insurancecompany.InsuranceCompanyApi;
import com.logistics.management.webapi.api.feign.insurancecompany.dto.*;
import com.logistics.management.webapi.api.impl.insurancecompany.mapper.InsuranceCompanyListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportExcelInsuranceCompanyInfo;
import com.logistics.management.webapi.base.constant.ImportExcelHeaders;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.tms.api.feign.insurancecompany.InsuranceCompanyServiceApi;
import com.logistics.tms.api.feign.insurancecompany.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/5/29 14:16
 */
@Slf4j
@RestController
public class InsuranceCompanyApiImpl implements InsuranceCompanyApi {
    @Autowired
    private InsuranceCompanyServiceApi insuranceCompanyServiceApi;

    /**
     * 保险公司列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<InsuranceCompanyListResponseDto>> searchInsuranceCompanyList(@RequestBody InsuranceCompanyListRequestDto requestDto) {
        Result<PageInfo<InsuranceCompanyListResponseModel>> result = insuranceCompanyServiceApi.searchInsuranceCompanyList(MapperUtils.mapper(requestDto,InsuranceCompanyListRequestModel.class));
        result.throwException();
        PageInfo<InsuranceCompanyListResponseDto> retPageInfo = new PageInfo<>();
        if(result.getData()!=null){
            PageInfo<InsuranceCompanyListResponseModel> pageInfoData = result.getData();
            List<InsuranceCompanyListResponseDto> retList = MapperUtils.mapper(pageInfoData.getList(),InsuranceCompanyListResponseDto.class,new InsuranceCompanyListMapping());
            retPageInfo = MapperUtils.mapper(pageInfoData,PageInfo.class);
            retPageInfo.setList(retList == null ? new ArrayList<>() : retList);
        }
        return Result.success(retPageInfo);
    }

    /**
     * 查看详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<InsuranceCompanyDetailResponseDto> getDetail(@RequestBody @Valid InsuranceCompanyDetailRequestDto requestDto) {
        Result<InsuranceCompanyDetailResponseModel> result = insuranceCompanyServiceApi.getDetail(MapperUtils.mapper(requestDto, InsuranceCompanyDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),InsuranceCompanyDetailResponseDto.class));
    }

    /**
     * 保险公司新增修改
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> saveOrModifyInsuranceCompany(@RequestBody @Valid SaveOrModifyInsuranceCompanyRequestDto requestDto) {
        Result<Boolean> result = insuranceCompanyServiceApi.saveOrModifyInsuranceCompany(MapperUtils.mapperNoDefault(requestDto, SaveOrModifyInsuranceCompanyRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 启用/禁用保险公司
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> enableOrDisable(@RequestBody @Valid EnableInsuranceCompanyRequestDto requestDto) {
        Result<Boolean> result = insuranceCompanyServiceApi.enableOrDisable(MapperUtils.mapper(requestDto,EnableInsuranceCompanyRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 导出
     * @param requestDto
     * @param response
     */
    @Override
    public void export(InsuranceCompanyListRequestDto requestDto, HttpServletResponse response) {
        Result<List<InsuranceCompanyListResponseModel>> result = insuranceCompanyServiceApi.export(MapperUtils.mapper(requestDto,InsuranceCompanyListRequestModel.class));
        result.throwException();
        String fileName = "保险公司基础数据" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        List<InsuranceCompanyListResponseDto>  resultList  = MapperUtils.mapper(result.getData(),InsuranceCompanyListResponseDto.class,new InsuranceCompanyListMapping());
        Map<String, String> exportTypeMap = ExportExcelInsuranceCompanyInfo.getInsuranceCompanyInfo();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportTypeMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return resultList;
            }
        });

    }

    /**
     * 导入
     * @param file
     * @param request
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<ImportInsuranceCompanyResponseDto> importInsuranceCompany(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        if (null == file) {
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_INSURANCE_COMPANY_FILE_IS_EMPTY);
        }
        InputStream in;
        try {
            in = file.getInputStream();
        } catch (IOException e) {
          log.error("导入保险公司失败，",e);
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_INSURANCE_COMPANY_FILE_IS_EMPTY);
        }

        List<List<Object>> excelList = new ImportExcelUtil().getDataListByExcel(in, file.getOriginalFilename(), ImportExcelHeaders.getImportInsuranceCompanyType());//JSON处理类
        ImportInsuranceCompanyRequestDto requestDto = this.initImportRepeatData(excelList);
        Result<ImportInsuranceCompanyResponseModel> result = insuranceCompanyServiceApi.importInsuranceCompany(MapperUtils.mapper(requestDto,ImportInsuranceCompanyRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),ImportInsuranceCompanyResponseDto.class));
    }

    /**
     * 根据名称模糊匹配保险公司
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<FuzzyQueryInsuranceCompanyListResponseDto>> fuzzyQueryInsuranceCompanyByName(@RequestBody FuzzyQueryInsuranceCompanyRequestDto requestDto) {
        Result<List<FuzzyQueryInsuranceCompanyListResponseModel>> result = insuranceCompanyServiceApi.fuzzyQueryInsuranceCompanyByName(MapperUtils.mapperNoDefault(requestDto,FuzzyQueryInsuranceCompanyRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),FuzzyQueryInsuranceCompanyListResponseDto.class));
    }

    //导入-入参校验及转换
    public ImportInsuranceCompanyRequestDto initImportRepeatData(List<List<Object>> excelList){
        ImportInsuranceCompanyRequestDto requestDto = new ImportInsuranceCompanyRequestDto();
        if (ListUtils.isEmpty(excelList)) {
            return requestDto;
        }
        Integer failureNumber = CommonConstant.INTEGER_ZERO;
        Integer successNumber = CommonConstant.INTEGER_ZERO;
        List<InsuranceCompanyRequestDto> importList = new ArrayList<>();
        List<String> importInsuranceCompanyFilterContent = new ArrayList<>();
        InsuranceCompanyRequestDto insuranceCompanyDto;
        for (int i = 0; i < excelList.size(); i++) {
            List<Object> objects = excelList.get(i);
            if(objects!=null){
                String companyName = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ZERO));
                String remark = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ONE));
                if(StringUtils.isBlank(companyName) && StringUtils.isBlank(remark)){
                    continue;
                }
                if(StringUtils.isBlank(companyName)){
                    failureNumber ++;
                    continue;
                }

                insuranceCompanyDto = new InsuranceCompanyRequestDto();
                insuranceCompanyDto.setCompanyName(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ZERO)));
                insuranceCompanyDto.setRemark(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ONE)));
                if(ListUtils.isEmpty(importInsuranceCompanyFilterContent) || !importInsuranceCompanyFilterContent.contains(insuranceCompanyDto.getCompanyName())){
                    importInsuranceCompanyFilterContent.add(insuranceCompanyDto.getCompanyName());
                    importList.add(insuranceCompanyDto);
                    successNumber ++;
                }else{
                    failureNumber ++;
                }
            }
        }
        requestDto.setImportList(importList);
        requestDto.setNumberFailures(ConverterUtils.toString(failureNumber));
        requestDto.setNumberSuccessful(ConverterUtils.toString(successNumber));
        return requestDto;
    }
}
