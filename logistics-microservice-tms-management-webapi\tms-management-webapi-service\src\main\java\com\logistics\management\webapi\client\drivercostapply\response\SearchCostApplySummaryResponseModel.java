package com.logistics.management.webapi.client.drivercostapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/3 15:03
 */
@Data
public class SearchCostApplySummaryResponseModel {

    @ApiModelProperty("司机id")
    private Long staffId;

    @ApiModelProperty("司机")
    private String staffName;

    @ApiModelProperty("司机")
    private String staffMobile;

    @ApiModelProperty("人员机构")
    private Integer staffProperty;

    @ApiModelProperty("发生时间")
    private String occurrenceTime;

    @ApiModelProperty("费用信息")
    private List<SearchCostApplySummaryListResponseModel> costList;
}
