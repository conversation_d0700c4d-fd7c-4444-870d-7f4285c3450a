<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TMessageNoticeMapper" >
  <select id="searchList" resultType="com.logistics.tms.controller.messagenotice.response.SearchMessageNoticeListResponseModel">
    select
    id as messageNoticeId,
    message_id as messageId,
    message_type as messageType,
    object_id as objectId,
    message_body as messageBody,
    message_push_time as messagePushTime,
    if_read as ifRead
    from t_message_notice
    where valid = 1
    and message_module = #{params.messageModule,jdbcType=INTEGER}
    <if test="params.messageReceiver != null">
      and message_receiver = #{params.messageReceiver,jdbcType=BIGINT}
    </if>
    <if test="params.messageBody != null and params.messageBody != ''">
      and instr(message_body, #{params.messageBody,jdbcType=VARCHAR})
    </if>
    <if test="params.messagePushTimeStart != null and params.messagePushTimeStart != ''">
      and message_push_time &gt;= DATE_FORMAT(#{params.messagePushTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %k:%i:%S')
    </if>
    <if test="params.messagePushTimeEnd != null and params.messagePushTimeEnd != ''">
      and message_push_time &lt;= DATE_FORMAT(#{params.messagePushTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
    </if>
    order by message_push_time desc, id desc
  </select>

  <update id="readMessageNotice">
    update t_message_notice
    set
    if_read = 1,
    read_operator = #{params.userName,jdbcType=VARCHAR},
    read_time = #{params.updateTime,jdbcType=TIMESTAMP},
    last_modified_by = #{params.userName,jdbcType=VARCHAR},
    last_modified_time = #{params.updateTime,jdbcType=TIMESTAMP}
    where valid = 1
    and message_module = #{params.messageModule,jdbcType=INTEGER}
    <if test="params.messageId != null and params.messageId != ''">
      and message_id = #{params.messageId,jdbcType=VARCHAR}
    </if>
    <if test="params.messageReceiver != null">
      and message_receiver = #{params.messageReceiver,jdbcType=BIGINT}
    </if>
    and if_read = 0
  </update>

  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TMessageNotice" >
    <foreach collection="recordList" item="item" separator=";">
      insert into t_message_notice
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.messageId != null" >
          message_id,
        </if>
        <if test="item.messageType != null">
          message_type,
        </if>
        <if test="item.messageModule != null" >
          message_module,
        </if>
        <if test="item.objectType != null" >
          object_type,
        </if>
        <if test="item.objectId != null" >
          object_id,
        </if>
        <if test="item.objectCode != null" >
          object_code,
        </if>
        <if test="item.messageBody != null" >
          message_body,
        </if>
        <if test="item.messagePusher != null" >
          message_pusher,
        </if>
        <if test="item.messagePushTime != null" >
          message_push_time,
        </if>
        <if test="item.messageReceiver != null" >
          message_receiver,
        </if>
        <if test="item.ifRead != null" >
          if_read,
        </if>
        <if test="item.readOperator != null" >
          read_operator,
        </if>
        <if test="item.readTime != null" >
          read_time,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.messageId != null" >
          #{item.messageId,jdbcType=VARCHAR},
        </if>
        <if test="item.messageType != null">
          #{item.messageType,jdbcType=INTEGER},
        </if>
        <if test="item.messageModule != null" >
          #{item.messageModule,jdbcType=INTEGER},
        </if>
        <if test="item.objectType != null" >
          #{item.objectType,jdbcType=INTEGER},
        </if>
        <if test="item.objectId != null" >
          #{item.objectId,jdbcType=BIGINT},
        </if>
        <if test="item.objectCode != null" >
          #{item.objectCode,jdbcType=VARCHAR},
        </if>
        <if test="item.messageBody != null" >
          #{item.messageBody,jdbcType=VARCHAR},
        </if>
        <if test="item.messagePusher != null" >
          #{item.messagePusher,jdbcType=VARCHAR},
        </if>
        <if test="item.messagePushTime != null" >
          #{item.messagePushTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.messageReceiver != null" >
          #{item.messageReceiver,jdbcType=BIGINT},
        </if>
        <if test="item.ifRead != null" >
          #{item.ifRead,jdbcType=INTEGER},
        </if>
        <if test="item.readOperator != null" >
          #{item.readOperator,jdbcType=VARCHAR},
        </if>
        <if test="item.readTime != null" >
          #{item.readTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>
</mapper>