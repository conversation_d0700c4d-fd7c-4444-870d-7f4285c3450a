package com.logistics.management.webapi.controller.biddingorder.mapping;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.logistics.management.webapi.client.biddingorder.response.BiddingOrderDetailByManagerResponseModel;
import com.logistics.management.webapi.controller.biddingorder.response.BiddingOrderDetailDemandDto;
import com.logistics.management.webapi.controller.biddingorder.response.BiddingOrderDetailResponseDto;
import com.logistics.management.webapi.controller.biddingorder.response.BiddingOrderQuoteListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/05/11
 */
public class BiddingOrderDetailMapping  extends MapperMapping<BiddingOrderDetailByManagerResponseModel, BiddingOrderDetailResponseDto> {
    @Override
    public void configure() {
        BiddingOrderDetailResponseDto destination = getDestination();
        BiddingOrderDetailByManagerResponseModel source = getSource();
        if(null!=source){
            destination.setExpectedLoadTime(DateUtils.dateToString(source.getExpectedLoadTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            destination.setExpectedUnloadTime(DateUtils.dateToString(source.getExpectedUnloadTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            destination.setVehicleLength(null!=source.getVehicleLength()?source.getVehicleLength().stripTrailingZeros().toPlainString():"");
            if(CollUtil.isNotEmpty(destination.getBiddingOrderQuoteList())){
                for (BiddingOrderQuoteListResponseDto biddingOrderQuoteListResponseDto : destination.getBiddingOrderQuoteList()) {
                    biddingOrderQuoteListResponseDto.setVehicleLength(StrUtil.isNotBlank(biddingOrderQuoteListResponseDto.getVehicleLength())
                            ?new BigDecimal(biddingOrderQuoteListResponseDto.getVehicleLength()).stripTrailingZeros().toPlainString():"");
                }
            }
            if(CollUtil.isNotEmpty(destination.getDemandDtoList())){
                for (BiddingOrderDetailDemandDto biddingOrderDetailDemandDto : destination.getDemandDtoList()) {
                    biddingOrderDetailDemandDto.setGoodsCount(StrUtil.isNotBlank(biddingOrderDetailDemandDto.getGoodsCount())
                            ?new BigDecimal(biddingOrderDetailDemandDto.getGoodsCount()).stripTrailingZeros().toPlainString():"");
                }
            }
        }
    }
}
