package com.logistics.tms.controller.carrierorder.response;

import com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/21
 */
@Data
public class SearchCarrierOrderListForWebResponseModel {


	@ApiModelProperty("运单ID")
	private Long carrierOrderId;

	@ApiModelProperty("需求单ID")
	private Long demandOrderId;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("客户单号")
	private String customerOrderCode;

	@ApiModelProperty("运单状态10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0 已取消 2 已放空")
	private Integer status;

	@ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
	private Integer entrustType;

	@ApiModelProperty("委托方")
	private String entrust;

	@ApiModelProperty("派车时间")
	private Date dispatchTime;

	@ApiModelProperty("装货地址")
	private String loadAddress;

	@ApiModelProperty("卸货地址")
	private String unloadAddress;

	@ApiModelProperty("提货人姓名")
	private String consignorName;

	@ApiModelProperty("提货人手机号")
	private String consignorMobile;

	@ApiModelProperty("卸货人姓名")
	private String receiverName;

	@ApiModelProperty("卸货人手机号")
	private String receiverMobile;


	private String loadProvinceName;
	private String loadCityName;
	private String loadAreaName;
	private String loadDetailAddress;
	private String loadWarehouse;
	private String unloadProvinceName;
	private String unloadCityName;
	private String unloadAreaName;
	private String unloadDetailAddress;
	private String unloadWarehouse;
	private String exportConsignorName;
	private String exportConsignorMobile;


	@ApiModelProperty("车牌号")
	private String vehicleNo;

	@ApiModelProperty("司机姓名")
	private String driverName;

	@ApiModelProperty("司机手机号")
	private String driverMobile;

	@ApiModelProperty("货物单位：1 件，2 吨")
	private Integer goodsUnit;

	@ApiModelProperty("预提件数")
	private BigDecimal expectAmount;

	@ApiModelProperty("装货件数")
	private BigDecimal loadAmount;

	@ApiModelProperty("卸货件数")
	private BigDecimal unloadAmount;

	@ApiModelProperty("签收件数")
	private BigDecimal signAmount;

	@ApiModelProperty("预计到货时间")
	private Date expectArrivalTime;

	@ApiModelProperty("实际卸货时间")
	private Date unloadTime;

	@ApiModelProperty("实际提货时间")
	private Date loadTime;

	@ApiModelProperty("打印提货单次数")
	private Integer printCount;

	@ApiModelProperty("取消原因")
	private String cancelReason;

	@ApiModelProperty("是否取消：0 否，1 是")
	private Integer ifCancel;

	@ApiModelProperty("是否放空：0 否，1 是")
	private Integer ifEmpty;

	private List<SearchCarrierOrderListGoodsInfoModel> goodsInfoList;

	@ApiModelProperty("中石化运单号")
	private String sinopecOrderNo;

	@ApiModelProperty("中石化下单类型：20 拉取，21 推送")
	private Integer orderType;

	@ApiModelProperty("是否有异常工单: 0否 1是")
	private Integer whetherWorkOrder;

	@ApiModelProperty("回收任务类型：1 日常回收，2 加急或节假日回收")
	private Integer recycleTaskType;

	@ApiModelProperty("是否按编码回收 0:否 1:是   v2.44")
	private Integer ifRecycleByCode;

	@ApiModelProperty("是否是额外补的运单  0：否 1：是  V2.6.8")
	private Integer ifExtCarrierOrder;

	@ApiModelProperty("是否显示补单按钮  0：否 1：是  V2.6.8")
	private String enableExtCarrierOrder;

	private Integer demandOrderEntrustType;

	@ApiModelProperty(value = "需求单来源")
	private Integer demandOrderSource ;
}
