package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

public class ExportExcelContractOrder {
    private ExportExcelContractOrder() {
    }

    private static final Map<String, String> EXCEL_CONTRACT_ORDER;

    static {
        EXCEL_CONTRACT_ORDER = new LinkedHashMap<>();
        EXCEL_CONTRACT_ORDER.put("内部合同号", "contractNoInternal");
        EXCEL_CONTRACT_ORDER.put("外部合同号", "contractNoExternal");
        EXCEL_CONTRACT_ORDER.put("合同状态", "contractStatusDsc");
        EXCEL_CONTRACT_ORDER.put("合同性质", "contractNature");
        EXCEL_CONTRACT_ORDER.put("合同类型", "contractType");
        EXCEL_CONTRACT_ORDER.put("客户", "customerName");
        EXCEL_CONTRACT_ORDER.put("合同有效期", "contractValidTime");
        EXCEL_CONTRACT_ORDER.put("操作人", "lastModifiedBy");
        EXCEL_CONTRACT_ORDER.put("最后操作时间", "lastModifiedTime");
    }

    public static Map<String, String> getExcelContractOrder() {
        return EXCEL_CONTRACT_ORDER;
    }
}
