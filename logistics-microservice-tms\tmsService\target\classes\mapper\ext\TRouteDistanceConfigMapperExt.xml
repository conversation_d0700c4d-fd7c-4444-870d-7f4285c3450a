<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRouteDistanceConfigMapper" >
    <select id="selectByArea" resultType="com.logistics.tms.controller.routeconfig.response.RouteDistanceConfigResponseModel">
      select
         id    routeDistanceConfigId,
         concat(from_province_name,from_city_name,from_area_name) fromAreaName,
         concat(to_province_name,to_city_name,to_area_name)    toAreaName,
         billing_distance     billingDistance,
         created_by              createBy,
         created_time             createTime,
         last_modified_by      lastModifiedBy,
         last_modified_time     lastModifiedTime

      from t_route_distance_config
      where valid = 1
        <if test="param.fromArea != null" >
            and instr(concat(from_province_name,from_city_name,from_area_name),#{param.fromArea,jdbcType=VARCHAR})
        </if>
        <if test="param.toArea != null" >
            and instr(concat(to_province_name,to_city_name,to_area_name),#{param.toArea,jdbcType=VARCHAR})
        </if>
      order by last_modified_time desc
    </select>

    <select id="selectById" resultType="com.logistics.tms.controller.routeconfig.response.RouteDistanceConfigDetailResponseModel">
        select
        id    routeDistanceConfigId,
        concat(from_province_name,from_city_name,from_area_name) fromAreaName,
        concat(to_province_name,to_city_name,to_area_name)    toAreaName,
        from_area_id         fromAreaId,
        to_area_id           toAreaId,
        billing_distance     billingDistance
        from t_route_distance_config
        where valid = 1
        and id = #{routeDistanceConfigId,jdbcType=INTEGER}
        order by last_modified_time desc
    </select>

    <select id="selectByFromToArea" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_route_distance_config
        where valid = 1
        <if test="param.fromProvinceId != null" >
            and from_province_id = #{param.fromProvinceId,jdbcType=INTEGER}
        </if>
        <if test="param.fromCityId != null" >
            and from_city_id = #{param.fromCityId,jdbcType=INTEGER}
        </if>
        <if test="param.fromAreaId != null" >
            and from_area_id = #{param.fromAreaId,jdbcType=INTEGER}
        </if>
        <if test="param.toProvinceId != null" >
            and to_province_id = #{param.toProvinceId,jdbcType=INTEGER}
        </if>
        <if test="param.toCityId != null" >
            and to_city_id = #{param.toCityId,jdbcType=INTEGER}
        </if>
        <if test="param.toAreaId != null" >
            and to_area_id = #{param.toAreaId,jdbcType=INTEGER}
        </if>

    </select>

    <select id="selectByFromAreaAndToArea" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_route_distance_config
        where valid = 1
        <choose>
            <when test="areaList != null and areaList.size() != 0">
                and
                <foreach collection="areaList" item="item" open="(" close=")" separator="or">
                    (from_area_id = #{item.fromAreaId} and to_area_id = #{item.toAreaId})
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </select>
</mapper>