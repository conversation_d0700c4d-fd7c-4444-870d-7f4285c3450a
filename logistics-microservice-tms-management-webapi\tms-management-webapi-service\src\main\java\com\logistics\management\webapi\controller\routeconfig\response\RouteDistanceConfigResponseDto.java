package com.logistics.management.webapi.controller.routeconfig.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RouteDistanceConfigResponseDto {

    @ApiModelProperty(value = "路线距离配置Id")
    private String routeDistanceConfigId = "";

    @ApiModelProperty(value = "发货区; 省市区拼接")
    private String fromAreaName = "";

    @ApiModelProperty(value = "卸货区; 省市区拼接")
    private String toAreaName = "";

    @ApiModelProperty(value = "计费距离（KM）")
    private String billingDistance = "";

    @ApiModelProperty(value = "创建人")
    private String createBy = "";

    @ApiModelProperty(value = "创建时间")
    private String createTime = "";

    @ApiModelProperty(value = "操作人")
    private String lastModifiedBy = "";

    @ApiModelProperty(value = "操作时间")
    private String lastModifiedTime = "";
}
