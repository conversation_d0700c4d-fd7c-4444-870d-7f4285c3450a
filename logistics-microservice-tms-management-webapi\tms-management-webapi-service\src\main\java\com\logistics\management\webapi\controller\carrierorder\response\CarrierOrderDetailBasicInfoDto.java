package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierOrderDetailBasicInfoDto {

    @ApiModelProperty("发货地址")
    private String loadAddress;
    @ApiModelProperty("发货人")
    private String consignor;
    @ApiModelProperty("收货地址")
    private String unloadAddress;
    @ApiModelProperty("收货人")
    private String receiver;
    @ApiModelProperty("实际提货时间")
    private String loadTime;
    @ApiModelProperty("实际卸货时间")
    private String unloadTime;


}
