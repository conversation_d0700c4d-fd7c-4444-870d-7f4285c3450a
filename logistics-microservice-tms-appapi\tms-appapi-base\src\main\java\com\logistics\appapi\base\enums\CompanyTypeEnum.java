package com.logistics.appapi.base.enums;

public enum CompanyTypeEnum {

    DEFAULT(0,""),
    COMPANY(1, "企业"),
    PERSONAL(2, "个人")
    ;
    private Integer key;
    private String value;

    CompanyTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
