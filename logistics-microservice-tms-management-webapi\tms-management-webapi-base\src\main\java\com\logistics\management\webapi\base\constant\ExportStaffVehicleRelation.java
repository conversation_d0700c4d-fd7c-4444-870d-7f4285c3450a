package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/7/30 9:38
 */
public class ExportStaffVehicleRelation {
    private ExportStaffVehicleRelation() {
    }
    private static final Map<String,String> STAFF_VEHICLE_RELATION_INFO;
    static{
        STAFF_VEHICLE_RELATION_INFO=new LinkedHashMap<>();
        STAFF_VEHICLE_RELATION_INFO.put("关联车辆机构","typeLabel");
        STAFF_VEHICLE_RELATION_INFO.put("车主","exportCompanyCarrierName");
        STAFF_VEHICLE_RELATION_INFO.put("关联车辆类型","vehicleCategoryLabel");
        STAFF_VEHICLE_RELATION_INFO.put("品牌型号","brandModelLabel");
        STAFF_VEHICLE_RELATION_INFO.put("车辆类型","vehicleType");
        STAFF_VEHICLE_RELATION_INFO.put("车牌号","vehicleNo");
        STAFF_VEHICLE_RELATION_INFO.put("总质量","totalWeight");
        STAFF_VEHICLE_RELATION_INFO.put("装备质量","curbWeight");
        STAFF_VEHICLE_RELATION_INFO.put("准牵引总质量","tractionMassWeight");
        STAFF_VEHICLE_RELATION_INFO.put("排放标准","emissionStandard");

        STAFF_VEHICLE_RELATION_INFO.put("挂车车牌号","trailerVehicleNo");
        STAFF_VEHICLE_RELATION_INFO.put("最大限载数","approvedLoadWeight");
        STAFF_VEHICLE_RELATION_INFO.put("所有人","owner");
        STAFF_VEHICLE_RELATION_INFO.put("司机","staffName");
        STAFF_VEHICLE_RELATION_INFO.put("手机号","staffPhoneNumber");
        STAFF_VEHICLE_RELATION_INFO.put("身份证号","identityNumber");
        STAFF_VEHICLE_RELATION_INFO.put("从业资格证号","occupationalRequirementsCredentialNo");
        STAFF_VEHICLE_RELATION_INFO.put("驾驶证号","driversLicenseNo");

        STAFF_VEHICLE_RELATION_INFO.put("备注","remark");
        STAFF_VEHICLE_RELATION_INFO.put("操作人","lastModifiedBy");
        STAFF_VEHICLE_RELATION_INFO.put("操作时间","lastModifiedTime");
    }

    public static Map<String, String> getStaffVehicleRelation() {
        return STAFF_VEHICLE_RELATION_INFO;
    }

}
