package com.logistics.tms.controller.staffvehiclerelation.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/6/3 11:15
 */
@Data
public class GetFuzzyQueryDriverInfoRequestModel {

    @ApiModelProperty("类型 1 自主，2 外部，3 自营 多个类型以,分割")
    private String type;

    @ApiModelProperty("模糊匹配-可根据司机名称/手机号")
    private String fuzzyDriverField;

    @ApiModelProperty("启用 1，禁用 0")
    private Integer enabled;

    @ApiModelProperty("是否我司: 1:我司 2:其他车主")
    private Integer isOurCompany;

    @ApiModelProperty("车主id(其他车主时填写)")
    private Long companyCarrierId;
}
