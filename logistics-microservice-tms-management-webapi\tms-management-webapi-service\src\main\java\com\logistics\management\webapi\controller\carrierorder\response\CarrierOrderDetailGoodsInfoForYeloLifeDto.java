package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新生运单货物信息
 *
 * <AUTHOR>
 * @date 2022/8/17 13:27
 */
@ApiModel("运单货物信息")
@Data
public class CarrierOrderDetailGoodsInfoForYeloLifeDto {

    @ApiModelProperty("编码  如果是按编码 则有  v2.44")
    private String yeloCode= "";

    @ApiModelProperty("品名")
    private String goodsName= "";

    @ApiModelProperty("规格")
    private String size= "";

    @ApiModelProperty("预提数量")
    private String expectAmount= "";

    @ApiModelProperty("实提数量")
    private String loadAmount = "";

    @ApiModelProperty("实卸数量")
    private String unloadAmount = "";

    @ApiModelProperty("签收数量")
    private String signAmount = "";
}
