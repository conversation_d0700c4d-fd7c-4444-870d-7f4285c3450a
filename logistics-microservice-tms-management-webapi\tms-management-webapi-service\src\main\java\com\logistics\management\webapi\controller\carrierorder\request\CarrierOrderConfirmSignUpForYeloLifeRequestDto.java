package com.logistics.management.webapi.controller.carrierorder.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/7/22 10:04
 */
@Data
public class CarrierOrderConfirmSignUpForYeloLifeRequestDto {
    /**
     * 签收时间
     */
    @NotBlank(message = "请维护签收时间")
    private String signTime;

    /**
     * 签收的运单信息
     */
    @Valid
    @NotEmpty(message = "请填写签收信息")
    @Size(max = 40, message = "最多支持40条运单批量签收")
    private List<CarrierOrderConfirmSignUpListForYeloLifeRequestDto> signList;

}
