<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TVehicleSettlementDriverRelationMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TVehicleSettlementDriverRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_settlement_id" jdbcType="BIGINT" property="vehicleSettlementId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="commit_image_url" jdbcType="VARCHAR" property="commitImageUrl" />
    <result column="settlement_reason_remark" jdbcType="VARCHAR" property="settlementReasonRemark" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="confirm_time" jdbcType="TIMESTAMP" property="confirmTime" />
    <result column="driver_id" jdbcType="BIGINT" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="driver_mobile" jdbcType="VARCHAR" property="driverMobile" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, vehicle_settlement_id, status, commit_image_url, settlement_reason_remark, reason, 
    confirm_time, driver_id, driver_name, driver_mobile, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_vehicle_settlement_driver_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_vehicle_settlement_driver_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TVehicleSettlementDriverRelation">
    insert into t_vehicle_settlement_driver_relation (id, vehicle_settlement_id, status, 
      commit_image_url, settlement_reason_remark, 
      reason, confirm_time, driver_id, 
      driver_name, driver_mobile, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{vehicleSettlementId,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, 
      #{commitImageUrl,jdbcType=VARCHAR}, #{settlementReasonRemark,jdbcType=VARCHAR}, 
      #{reason,jdbcType=VARCHAR}, #{confirmTime,jdbcType=TIMESTAMP}, #{driverId,jdbcType=BIGINT}, 
      #{driverName,jdbcType=VARCHAR}, #{driverMobile,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TVehicleSettlementDriverRelation">
    insert into t_vehicle_settlement_driver_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="vehicleSettlementId != null">
        vehicle_settlement_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="commitImageUrl != null">
        commit_image_url,
      </if>
      <if test="settlementReasonRemark != null">
        settlement_reason_remark,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="confirmTime != null">
        confirm_time,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="driverName != null">
        driver_name,
      </if>
      <if test="driverMobile != null">
        driver_mobile,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vehicleSettlementId != null">
        #{vehicleSettlementId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="commitImageUrl != null">
        #{commitImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="settlementReasonRemark != null">
        #{settlementReasonRemark,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="confirmTime != null">
        #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null">
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null">
        #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TVehicleSettlementDriverRelation">
    update t_vehicle_settlement_driver_relation
    <set>
      <if test="vehicleSettlementId != null">
        vehicle_settlement_id = #{vehicleSettlementId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="commitImageUrl != null">
        commit_image_url = #{commitImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="settlementReasonRemark != null">
        settlement_reason_remark = #{settlementReasonRemark,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="confirmTime != null">
        confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null">
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null">
        driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TVehicleSettlementDriverRelation">
    update t_vehicle_settlement_driver_relation
    set vehicle_settlement_id = #{vehicleSettlementId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      commit_image_url = #{commitImageUrl,jdbcType=VARCHAR},
      settlement_reason_remark = #{settlementReasonRemark,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      driver_id = #{driverId,jdbcType=BIGINT},
      driver_name = #{driverName,jdbcType=VARCHAR},
      driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>