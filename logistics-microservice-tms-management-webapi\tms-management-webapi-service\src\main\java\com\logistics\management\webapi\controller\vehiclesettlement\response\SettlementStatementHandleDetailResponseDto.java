package com.logistics.management.webapi.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author:lei.zhu
 * @date:2021/4/9 13:49
 */
@Data
public class SettlementStatementHandleDetailResponseDto {
    @ApiModelProperty("车辆运费账单id")
    private String vehicleSettlementId="";
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty("账单月")
    private String settlementMonth="";
    @ApiModelProperty("实付运费")
    private String actualExpensesPayable="";
    @ApiModelProperty("司机  姓名+手机号")
    private String driverName="";
    @ApiModelProperty("司机异议")
    private String settlementReasonRemark="";
    @ApiModelProperty("调整费用符号：1 ‘+’， 2 ‘-’ ")
    private String adjustCostSymbol;
    @ApiModelProperty("调整费用")
    private String adjustFee="";
    @ApiModelProperty("调整费用原因")
    private String adjustRemark="";
    @ApiModelProperty("实付运费-调整费用  用于前端计算最终费用")
    private String originalFee="";
}
