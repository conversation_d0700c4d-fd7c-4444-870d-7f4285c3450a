<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRouteEnquiryMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TRouteEnquiry" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="order_code" property="orderCode" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="if_cancel" property="ifCancel" jdbcType="INTEGER" />
    <result column="goods_name" property="goodsName" jdbcType="VARCHAR" />
    <result column="quote_start_time" property="quoteStartTime" jdbcType="TIMESTAMP" />
    <result column="quote_end_time" property="quoteEndTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="if_archive" property="ifArchive" jdbcType="INTEGER" />
    <result column="contract_code" property="contractCode" jdbcType="VARCHAR" />
    <result column="auditor_name_one" property="auditorNameOne" jdbcType="VARCHAR" />
    <result column="audit_time_one" property="auditTimeOne" jdbcType="TIMESTAMP" />
    <result column="audit_remark_one" property="auditRemarkOne" jdbcType="VARCHAR" />
    <result column="auditor_name_two" property="auditorNameTwo" jdbcType="VARCHAR" />
    <result column="audit_time_two" property="auditTimeTwo" jdbcType="TIMESTAMP" />
    <result column="audit_remark_two" property="auditRemarkTwo" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, order_code, status, if_cancel, goods_name, quote_start_time, quote_end_time, 
    remark, if_archive, contract_code, auditor_name_one, audit_time_one, audit_remark_one, 
    auditor_name_two, audit_time_two, audit_remark_two, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_route_enquiry
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_route_enquiry
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TRouteEnquiry" >
    insert into t_route_enquiry (id, order_code, status, 
      if_cancel, goods_name, quote_start_time, 
      quote_end_time, remark, if_archive, 
      contract_code, auditor_name_one, audit_time_one, 
      audit_remark_one, auditor_name_two, audit_time_two, 
      audit_remark_two, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{orderCode,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{ifCancel,jdbcType=INTEGER}, #{goodsName,jdbcType=VARCHAR}, #{quoteStartTime,jdbcType=TIMESTAMP}, 
      #{quoteEndTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{ifArchive,jdbcType=INTEGER}, 
      #{contractCode,jdbcType=VARCHAR}, #{auditorNameOne,jdbcType=VARCHAR}, #{auditTimeOne,jdbcType=TIMESTAMP}, 
      #{auditRemarkOne,jdbcType=VARCHAR}, #{auditorNameTwo,jdbcType=VARCHAR}, #{auditTimeTwo,jdbcType=TIMESTAMP}, 
      #{auditRemarkTwo,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TRouteEnquiry" >
    insert into t_route_enquiry
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="orderCode != null" >
        order_code,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="ifCancel != null" >
        if_cancel,
      </if>
      <if test="goodsName != null" >
        goods_name,
      </if>
      <if test="quoteStartTime != null" >
        quote_start_time,
      </if>
      <if test="quoteEndTime != null" >
        quote_end_time,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="ifArchive != null" >
        if_archive,
      </if>
      <if test="contractCode != null" >
        contract_code,
      </if>
      <if test="auditorNameOne != null" >
        auditor_name_one,
      </if>
      <if test="auditTimeOne != null" >
        audit_time_one,
      </if>
      <if test="auditRemarkOne != null" >
        audit_remark_one,
      </if>
      <if test="auditorNameTwo != null" >
        auditor_name_two,
      </if>
      <if test="auditTimeTwo != null" >
        audit_time_two,
      </if>
      <if test="auditRemarkTwo != null" >
        audit_remark_two,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderCode != null" >
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="ifCancel != null" >
        #{ifCancel,jdbcType=INTEGER},
      </if>
      <if test="goodsName != null" >
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="quoteStartTime != null" >
        #{quoteStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="quoteEndTime != null" >
        #{quoteEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ifArchive != null" >
        #{ifArchive,jdbcType=INTEGER},
      </if>
      <if test="contractCode != null" >
        #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="auditorNameOne != null" >
        #{auditorNameOne,jdbcType=VARCHAR},
      </if>
      <if test="auditTimeOne != null" >
        #{auditTimeOne,jdbcType=TIMESTAMP},
      </if>
      <if test="auditRemarkOne != null" >
        #{auditRemarkOne,jdbcType=VARCHAR},
      </if>
      <if test="auditorNameTwo != null" >
        #{auditorNameTwo,jdbcType=VARCHAR},
      </if>
      <if test="auditTimeTwo != null" >
        #{auditTimeTwo,jdbcType=TIMESTAMP},
      </if>
      <if test="auditRemarkTwo != null" >
        #{auditRemarkTwo,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TRouteEnquiry" >
    update t_route_enquiry
    <set >
      <if test="orderCode != null" >
        order_code = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="ifCancel != null" >
        if_cancel = #{ifCancel,jdbcType=INTEGER},
      </if>
      <if test="goodsName != null" >
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="quoteStartTime != null" >
        quote_start_time = #{quoteStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="quoteEndTime != null" >
        quote_end_time = #{quoteEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ifArchive != null" >
        if_archive = #{ifArchive,jdbcType=INTEGER},
      </if>
      <if test="contractCode != null" >
        contract_code = #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="auditorNameOne != null" >
        auditor_name_one = #{auditorNameOne,jdbcType=VARCHAR},
      </if>
      <if test="auditTimeOne != null" >
        audit_time_one = #{auditTimeOne,jdbcType=TIMESTAMP},
      </if>
      <if test="auditRemarkOne != null" >
        audit_remark_one = #{auditRemarkOne,jdbcType=VARCHAR},
      </if>
      <if test="auditorNameTwo != null" >
        auditor_name_two = #{auditorNameTwo,jdbcType=VARCHAR},
      </if>
      <if test="auditTimeTwo != null" >
        audit_time_two = #{auditTimeTwo,jdbcType=TIMESTAMP},
      </if>
      <if test="auditRemarkTwo != null" >
        audit_remark_two = #{auditRemarkTwo,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TRouteEnquiry" >
    update t_route_enquiry
    set order_code = #{orderCode,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      if_cancel = #{ifCancel,jdbcType=INTEGER},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      quote_start_time = #{quoteStartTime,jdbcType=TIMESTAMP},
      quote_end_time = #{quoteEndTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      if_archive = #{ifArchive,jdbcType=INTEGER},
      contract_code = #{contractCode,jdbcType=VARCHAR},
      auditor_name_one = #{auditorNameOne,jdbcType=VARCHAR},
      audit_time_one = #{auditTimeOne,jdbcType=TIMESTAMP},
      audit_remark_one = #{auditRemarkOne,jdbcType=VARCHAR},
      auditor_name_two = #{auditorNameTwo,jdbcType=VARCHAR},
      audit_time_two = #{auditTimeTwo,jdbcType=TIMESTAMP},
      audit_remark_two = #{auditRemarkTwo,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>