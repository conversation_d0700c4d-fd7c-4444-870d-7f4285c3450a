package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TVehicleSettlementDriverRelation;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TVehicleSettlementDriverRelationMapper extends BaseMapper<TVehicleSettlementDriverRelation> {

    int batchInsertSelective(@Param("list") List<TVehicleSettlementDriverRelation> tInvoiceApplyItemList);

    TVehicleSettlementDriverRelation getByVehicleSettlementId(@Param("vehicleSettlementId") Long id);

    TVehicleSettlementDriverRelation getByVehicleSettlementIdDriverId(@Param("vehicleSettlementId") Long vehicleSettlementId, @Param("driverId") Long driverId);
}