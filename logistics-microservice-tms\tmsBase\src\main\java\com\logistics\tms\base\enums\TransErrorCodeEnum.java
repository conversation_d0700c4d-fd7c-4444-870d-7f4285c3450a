package com.logistics.tms.base.enums;

public enum TransErrorCodeEnum {
    ERROR_CODE_1001("1001", "接口执行成功"),
    ERROR_CODE_1002("1002", "参数不正确（参数为空、查询时间范围不正确、参数数量不正确）"),
    ERROR_CODE_1003("1003", "车辆调用数量已达上限"),
    ERROR_CODE_1004("1004", "接口调用次数已达上限"),
    ERROR_CODE_1005("1005", "该API账号未授权指定所属行政区划数据范围"),
    ERROR_CODE_1006("1006", "无结果"),
    ERROR_CODE_1010("1010", "用户名或密码不正确"),
    ERROR_CODE_1011("1011", "IP不在白名单列表"),
    ERROR_CODE_1012("1012", "账号已禁用"),
    ERROR_CODE_1013("1013", "账号已过有效期"),
    ERROR_CODE_1014("1014", "无接口权限"),
    ERROR_CODE_1015("1015", "用户认证系统已升级，请使用令牌访问"),
    ERROR_CODE_1016("1016", "令牌失效"),
    ERROR_CODE_1017("1017", "账号欠费"),
    ERROR_CODE_1018("1018", "授权的接口已禁用"),
    ERROR_CODE_1019("1019", "授权的接口已过期"),
    ERROR_CODE_9001("9001", "系统异常"),

    //
    ;

    private String key;

    private String value;

    private TransErrorCodeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
