package com.logistics.tms.controller.settlestatement.tradition.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/16
 */
@Data
public class CarrierTraditionStatementListRequestModel extends AbstractPageForm<CarrierTraditionStatementListRequestModel> {

	@ApiModelProperty("对账单状态,空:全部 -2:已撤销 -1:待提交 0:待业务审核 1:待财务审核 2:已对账 3:已驳回")
	private Integer settleStatementStatus;

	@ApiModelProperty("车主名称")
	private String companyCarrierName;

	@ApiModelProperty("对账单号")
	private String settleStatementCode;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("对账单名称")
	private String settleStatementName;

	@ApiModelProperty("关联发票：0 否，1 是")
	private Integer ifInvoice;

	@ApiModelProperty("对账月份起 yyyy-MM-dd")
	private String settleStatementMonthStart;

	@ApiModelProperty("对账月份止 yyyy-MM-dd")
	private String settleStatementMonthEnd;

	@ApiModelProperty("操作时间起")
	private String lastModifiedTimeStart;

	@ApiModelProperty("操作时间止")
	private String lastModifiedTimeEnd;

	@ApiModelProperty("若有勾选导出则传此字段，对账单ids ','拼接")
	private String settleStatementIds;

	@ApiModelProperty("来源：1 后台，2 前台")
	private Integer source;

	private Long companyCarrierId;//车主id
}

