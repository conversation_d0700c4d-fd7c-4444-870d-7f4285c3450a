package com.logistics.tms.api.feign.driverfreight.model;
import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DriverFreightListSearchRequestModel extends AbstractPageForm<DriverFreightListSearchRequestModel> {

    @ApiModelProperty("状态 50000 待签收 60000 已签收 ")
    private Integer status;
    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";
    @ApiModelProperty("需求单号")
    private String demandOrderCode= "";
    @ApiModelProperty("车辆机构 1 自主，2 外部，3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("车牌号")
    private String vehicleNo= "";
    @ApiModelProperty("司机")
    private String driver= "";
    @ApiModelProperty("发货地")
    private String loadAddress= "";
    @ApiModelProperty("卸货地")
    private String unloadAddress= "";
    @ApiModelProperty("调度人")
    private String dispatchUser= "";
    @ApiModelProperty("货主，公司ID，逗号分隔")
    private String companyEntrustIds= "";
    @ApiModelProperty("品名")
    private String goodsName= "";
    @ApiModelProperty("规格")
    private String goodsSize= "";
    @ApiModelProperty("运单生成时间从")
    private String dispatchTimeFrom= "";
    @ApiModelProperty("运单生成时间到")
    private String dispatchTimeTo= "";
    @ApiModelProperty("运单ID拼接例如，“1,2,3”")
    private String carrierOrderIds = "";
}
