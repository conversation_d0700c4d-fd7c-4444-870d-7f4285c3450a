package com.logistics.tms.controller.companyentrust;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.companyentrust.CompanyEntrustBiz;
import com.logistics.tms.controller.companyentrust.request.AddOrModifyCompanyEntrustRequestModel;
import com.logistics.tms.controller.companyentrust.request.CompanyEntrustIdRequestModel;
import com.logistics.tms.controller.companyentrust.request.SearchCompanyEntrustByNameRequestModel;
import com.logistics.tms.controller.companyentrust.request.SearchCompanyEntrustRequestModel;
import com.logistics.tms.controller.companyentrust.response.CompanyEntrustDetailResponseModel;
import com.logistics.tms.controller.companyentrust.response.SearchCompanyEntrustByNameResponseModel;
import com.logistics.tms.controller.companyentrust.response.SearchCompanyEntrustResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/9/19 10:26
 */
@Api(value = "API - CompanyEntrustServiceApi-委托方公司管理")
@RestController
public class CompanyEntrustController {

    @Autowired
    private CompanyEntrustBiz companyEntrustBiz;

    /**
     * 获取委托方公司列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取委托方公司列表")
    @PostMapping(value = "/service/companyEntrust/searchList")
    public Result<PageInfo<SearchCompanyEntrustResponseModel>> searchList(@RequestBody SearchCompanyEntrustRequestModel requestModel) {
        List<SearchCompanyEntrustResponseModel> list = companyEntrustBiz.searchList(requestModel.enablePaging());
        return Result.success(new PageInfo<>(list));
    }

    /**
     * 查看详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查看详情")
    @PostMapping(value = "/service/companyEntrust/getDetail")
    public Result<CompanyEntrustDetailResponseModel> getDetail(@RequestBody CompanyEntrustIdRequestModel requestModel) {
        return Result.success(companyEntrustBiz.getDetail(requestModel));
    }

    /**
     * 添加/修改公司
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "添加/修改公司")
    @PostMapping(value = "/service/companyEntrust/saveCompany")
    public Result saveCompany(@RequestBody AddOrModifyCompanyEntrustRequestModel requestModel) {
        companyEntrustBiz.saveCompany(requestModel);
        return Result.success(true);
    }

    /**
     * 根据公司名称模糊查询委托方公司
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "根据公司名称模糊查询委托方公司")
    @PostMapping(value = "/service/companyEntrust/searchCompanyEntrustByName")
    public Result<List<SearchCompanyEntrustByNameResponseModel>> searchCompanyEntrustByName(@RequestBody SearchCompanyEntrustByNameRequestModel requestModel) {
        return Result.success(companyEntrustBiz.searchCompanyEntrustByName(requestModel));
    }
}
