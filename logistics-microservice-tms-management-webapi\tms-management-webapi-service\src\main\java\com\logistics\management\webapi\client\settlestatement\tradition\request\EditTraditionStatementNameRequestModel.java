package com.logistics.management.webapi.client.settlestatement.tradition.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EditTraditionStatementNameRequestModel {

    @ApiModelProperty(value = "对账单id")
    private Long settleStatementId;

    @ApiModelProperty(value = "对账单名称")
    private String settleStatementName;

    @ApiModelProperty("来源：1 后台，2 前台")
    private Integer source;
}
