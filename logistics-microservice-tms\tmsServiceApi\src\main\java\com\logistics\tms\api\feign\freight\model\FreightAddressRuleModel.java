package com.logistics.tms.api.feign.freight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 运价格则
 * @Author: sj
 * @Date: 2019/12/24 13:14
 */
@Data
public class FreightAddressRuleModel {
    @ApiModelProperty("运价地址规则ID")
    private Long freightAddressRuleId;
    @ApiModelProperty("序列")
    private Integer ruleIndex;
    @ApiModelProperty("价格区间下线")
    private BigDecimal amountFrom;
    @ApiModelProperty("1 小于 2 小于等于")
    private Integer fromSymbol;
    @ApiModelProperty("价格区间上线")
    private BigDecimal amountTo;
    @ApiModelProperty("1 小于 2 小于等于")
    private Integer toSymbol;
    @ApiModelProperty("费用类型 1 单价 2 一口价")
    private Integer freightType;
    @ApiModelProperty("费用")
    private BigDecimal freightFee;
    @ApiModelProperty("运价多装多卸(车主)")
    private List<FreightAddressRuleMarkupModel> freightAddressRuleMarkupList;
}
