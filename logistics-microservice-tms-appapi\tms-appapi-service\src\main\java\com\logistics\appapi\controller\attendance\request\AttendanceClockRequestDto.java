package com.logistics.appapi.controller.attendance.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;

/**
 * 考勤打卡请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/14
 */
@Data
public class AttendanceClockRequestDto {

	@ApiModelProperty(value = "打卡类型, 1:上班打卡 2:下班打卡")
	@NotBlank(message = "打卡类型不能为空")
	@Range(min = 1, max = 2, message = "打卡类型不存在")
	private String punchType;

	@ApiModelProperty(value = "打卡地点", required = true)
	@NotBlank(message = "打卡地点不能为空")
	private String punchLocation;

	@ApiModelProperty(value = "打卡图片地址", required = true)
	@NotBlank(message = "打卡图片不能为空")
	private String punchPic;
}
