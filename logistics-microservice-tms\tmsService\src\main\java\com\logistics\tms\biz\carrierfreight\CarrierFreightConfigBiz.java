package com.logistics.tms.biz.carrierfreight;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.freightconfig.request.CarrierFreightConfigAddRequestModel;
import com.logistics.tms.controller.freightconfig.request.CarrierFreightConfigEditRequestModel;
import com.logistics.tms.controller.freightconfig.request.CarrierFreightConfigListRequestModel;
import com.logistics.tms.controller.freightconfig.request.CarrierFreightConfigRequestModel;
import com.logistics.tms.controller.freightconfig.response.CarrierFreightConfigDetailResponseModel;
import com.logistics.tms.controller.freightconfig.response.CarrierFreightConfigListResponseModel;
import com.logistics.tms.entity.TCarrierFreightConfig;
import com.logistics.tms.entity.TCarrierFreightConfigAddress;
import com.logistics.tms.entity.TCarrierFreightConfigScheme;
import com.logistics.tms.mapper.TCarrierFreightConfigMapper;
import com.logistics.tms.mapper.TCarrierFreightConfigSchemeMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 车主运价service
 *
 * <AUTHOR>
 * @date 2023/6/30 15:01
 */
@Service
public class CarrierFreightConfigBiz {


    @Resource
    private TCarrierFreightConfigMapper tCarrierFreightConfigMapper;
    @Resource
    private TCarrierFreightConfigSchemeMapper carrierFreightConfigSchemeMapper;

    @Resource
    private CarrierFreightBiz carrierFreightBiz;
    @Resource
    private CarrierFreightConfigSchemeBiz carrierFreightConfigSchemeBiz;

    @Resource
    private CarrierFreightConfigAddressBiz carrierFreightConfigAddressBiz;
    @Resource
    CarrierFreightConfigLadderBiz carrierFreightConfigLadderBiz;

    @Resource
    private CommonBiz commonBiz;

    /**
     * 根据运单id查询车主运价配置列表
     *
     * @param requestModel 运价Id
     * @return 车主运价配置列表
     */
    public List<CarrierFreightConfigListResponseModel> searchList(CarrierFreightConfigListRequestModel requestModel) {
        return tCarrierFreightConfigMapper.selectByFreightId(requestModel.getFreightId());
    }


    /**
     * 根据运价配置Id查询车主运价配置详情
     *
     * @param requestModel 运价Id
     * @return 车主运价配置列表
     */
    public CarrierFreightConfigDetailResponseModel detail(CarrierFreightConfigRequestModel requestModel) {
        return tCarrierFreightConfigMapper.selectById(requestModel.getFreightConfigId());
    }

    /**
     * 新增车主运价配置
     *
     * @param requestModel 车主运价
     * @return 新增车主运价成功
     */
    @Transactional
    public Boolean add(CarrierFreightConfigAddRequestModel requestModel) {
        // 校验车主运价id是否真实
        carrierFreightBiz.checkCarrierFreightExist(requestModel.getCarrierFreightId());
        // 校验价格模式是否在枚举范围内
        boolean matchConfigType = Arrays.stream(ConfigTypeEnum.values()).anyMatch(e ->
                e.getKey().equals(requestModel.getConfigType()) &&
                        !ConfigTypeEnum.DEFAULT_VALUE.getKey().equals(requestModel.getConfigType())
        );
        if (!matchConfigType) {
            throw new BizException(CarrierDataExceptionEnum.NOT_MATCH_CONFIG_TYPE);
        }
        // 校验需求类型是否在枚举范围内
        checkEntrustType(requestModel.getEntrustTypes(),
                requestModel.getCarrierFreightId(),
                null);
        // 新增车主运价配置
        TCarrierFreightConfig tCarrierFreightConfig = new TCarrierFreightConfig();
        tCarrierFreightConfig.setConfigType(requestModel.getConfigType());
        tCarrierFreightConfig.setCarrierFreightId(requestModel.getCarrierFreightId());
        tCarrierFreightConfig.setEntrustType(String.join(CommonConstant.COMMA, requestModel.getEntrustTypes()));
        commonBiz.setBaseEntityAdd(tCarrierFreightConfig, BaseContextHandler.getUserName());
        tCarrierFreightConfigMapper.insertSelective(tCarrierFreightConfig);
        // 如果是固定路线的价格类型新增-车主运价方案配置
        if (ConfigTypeEnum.FIXED_ROUTE.getKey().equals(requestModel.getConfigType())) {
            TCarrierFreightConfigScheme tCarrierFreightConfigScheme = new TCarrierFreightConfigScheme();
            tCarrierFreightConfigScheme.setFreightConfigId(tCarrierFreightConfig.getId());
            tCarrierFreightConfigScheme.setSchemeType(CarrierFreightConfigSchemeTypeEnum.ROUTE_CONFIG.getKey());
            commonBiz.setBaseEntityAdd(tCarrierFreightConfigScheme, BaseContextHandler.getUserName());
            carrierFreightConfigSchemeMapper.insertSelective(tCarrierFreightConfigScheme);
        }
        return true;
    }

    /**
     * 修改车主运价配置
     *
     * @param requestModel 修改需求类型
     * @return 修改成功
     */
    @Transactional
    public Boolean edit(CarrierFreightConfigEditRequestModel requestModel) {
        TCarrierFreightConfig tCarrierFreightConfig =
                tCarrierFreightConfigMapper.selectByPrimaryKey(requestModel.getFreightConfigId());
        if (Objects.isNull(tCarrierFreightConfig)) {
            throw new BizException(CarrierDataExceptionEnum.FREIGHT_CONFIG_NOT_EXIST);
        }
        checkEntrustType(requestModel.getEntrustTypes(),
                tCarrierFreightConfig.getCarrierFreightId(),
                tCarrierFreightConfig.getEntrustType()
        );

        TCarrierFreightConfig updateFreightConfig = new TCarrierFreightConfig();
        updateFreightConfig.setId(tCarrierFreightConfig.getId());
        updateFreightConfig.setEntrustType(String.join(CommonConstant.COMMA, requestModel.getEntrustTypes()));
        commonBiz.setBaseEntityModify(updateFreightConfig, BaseContextHandler.getUserName());
        tCarrierFreightConfigMapper.updateByPrimaryKeySelective(updateFreightConfig);
        return true;
    }

    /**
     * 校验需求类型是否在枚举范围内
     *
     * @param entrustTypeList 需求类型
     */
    private void checkEntrustType(List<String> entrustTypeList,
                                  Long carrierFreightId,
                                  String removeEntrust) {
        // 校验需求类型是否在枚举范围内
        for (String entrustType : entrustTypeList) {
            Integer type = Integer.parseInt(entrustType);
            boolean matchEntrustType = Arrays.stream(EntrustTypeEnum.values()).anyMatch(e ->
                    e.getKey().equals(type) &&
                            !EntrustTypeEnum.DEFAULT.getKey().equals(type)
            );
            if (!matchEntrustType) {
                throw new BizException(CarrierDataExceptionEnum.NOT_MATCH_ENTRUST_TYPE);
            }
        }

        List<String> entryTypes =
                tCarrierFreightConfigMapper.selectEntrustTypeByFreightId(carrierFreightId);
        if (StringUtils.isNotBlank(removeEntrust)) {
            entryTypes.remove(removeEntrust);
        }
        List<String> checkEntryType =
                entryTypes.stream().map(e ->
                        Lists.newArrayList(e.split(CommonConstant.COMMA))
                ).flatMap(Collection::stream).collect(Collectors.toList());
        Collection<String> intersection = CollectionUtils.intersection(checkEntryType, entrustTypeList);
        // 判断需求类型是否已经存在
        if (intersection.size() > 0) {
            throw new BizException(CarrierDataExceptionEnum.ENTRUST_TYPE_HAS_EXIST);
        }
    }

    /**
     * 删除车主运价配置
     *
     * @param requestModel 车主id
     * @return 新增失败
     */
    @Transactional
    public Boolean delete(CarrierFreightConfigRequestModel requestModel) {
        String userName = BaseContextHandler.getUserName();
        TCarrierFreightConfig tCarrierFreightConfig =
                tCarrierFreightConfigMapper.selectByPrimaryKey(requestModel.getFreightConfigId());
        if (Objects.isNull(tCarrierFreightConfig)) {
            throw new BizException(CarrierDataExceptionEnum.FREIGHT_CONFIG_NOT_EXIST);
        }
        TCarrierFreightConfig deleteFreightConfig = new TCarrierFreightConfig();
        deleteFreightConfig.setId(tCarrierFreightConfig.getId());
        deleteFreightConfig.setValid(IfValidEnum.INVALID.getKey());
        commonBiz.setBaseEntityModify(deleteFreightConfig, userName);
        tCarrierFreightConfigMapper.updateByPrimaryKeySelective(deleteFreightConfig);
        // 级联删除车主运价配置所有关联子表
        AsyncProcessQueue.execute(() -> {
            deleteChild(deleteFreightConfig.getId(), userName);
        });
        return true;
    }

    /**
     * 异步级联删除车主运价配置所有关联子表
     * @param freightConfigId 运价配置id
     * @param userName 操作人
     */
    private void deleteChild(Long freightConfigId, String userName) {
        List<TCarrierFreightConfigScheme> configScheme = carrierFreightConfigSchemeBiz.getConfigScheme(freightConfigId);
        if(ListUtils.isNotEmpty(configScheme)){
            carrierFreightConfigSchemeBiz.removeSchemeNotExistsIdByIds(freightConfigId, null, null, userName);
            List<Long> routeConfigAddressIds = new LinkedList<>();
            List<Long> schemeConfigIds = new LinkedList<>();
            for(TCarrierFreightConfigScheme tCarrierFreightConfigScheme: configScheme){
                // 路线计价只会有一条方案
                if (CarrierFreightConfigSchemeTypeEnum.ROUTE_CONFIG.getKey().equals(tCarrierFreightConfigScheme.getSchemeType())) {
                    List<Long> tCarrierFreightConfigAddressesIds =
                            carrierFreightConfigAddressBiz.searchAddressByConfigSchemeId(tCarrierFreightConfigScheme.getId()).stream()
                                    .map(TCarrierFreightConfigAddress::getId).collect(Collectors.toList());
                    if(ListUtils.isNotEmpty(tCarrierFreightConfigAddressesIds)){
                        routeConfigAddressIds.addAll(tCarrierFreightConfigAddressesIds);
                        carrierFreightConfigAddressBiz.deleteBySchemeId(tCarrierFreightConfigScheme.getId(), userName);
                    }
                    break;
                } else {
                    schemeConfigIds.add(tCarrierFreightConfigScheme.getId());
                }
            }
            if(ListUtils.isNotEmpty(routeConfigAddressIds)){
                carrierFreightConfigLadderBiz.deleteLadder(routeConfigAddressIds, userName,CarrierFreightConfigLadderModeEnum.ROUTE_CONFIG.getKey());
            }
            if(ListUtils.isNotEmpty(schemeConfigIds)){
                carrierFreightConfigLadderBiz.deleteLadder(schemeConfigIds, userName,CarrierFreightConfigLadderModeEnum.SCHEME_CONFIG.getKey());
            }

        }


    }

    /**
     * 根据配置ID查询信息
     *
     * @param freightConfigId 配置Id
     * @return 配置信息
     */
    public Optional<TCarrierFreightConfig> getFreightConfigById(Long freightConfigId) {
        return Optional.ofNullable(tCarrierFreightConfigMapper.selectByPrimaryKey(freightConfigId))
                .filter(f -> IfValidEnum.VALID.getKey().equals(f.getValid()));
    }
}
