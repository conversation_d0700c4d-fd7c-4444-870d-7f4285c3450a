package com.logistics.tms.mapper;

import com.logistics.tms.biz.routeenquiry.model.RouteEnquiryAddressCountModel;
import com.logistics.tms.controller.routeenquiry.request.SearchRouteEnquirySummaryListRequestModel;
import com.logistics.tms.controller.routeenquiry.response.GetRouteEnquiryDetailAddressListResponseModel;
import com.logistics.tms.controller.routeenquiry.response.SearchRouteEnquirySummaryListResponseModel;
import com.logistics.tms.entity.TRouteEnquiryAddress;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/07/09
*/
@Mapper
public interface TRouteEnquiryAddressMapper extends BaseMapper<TRouteEnquiryAddress> {

    List<RouteEnquiryAddressCountModel> getAddressCount(@Param("routeEnquiryIdList")List<Long> routeEnquiryIdList);

    List<GetRouteEnquiryDetailAddressListResponseModel> getAddressByRouteEnquiryId(@Param("routeEnquiryId")Long routeEnquiryId);

    List<GetRouteEnquiryDetailAddressListResponseModel> getAddressForWebByRouteEnquiryId(@Param("routeEnquiryId")Long routeEnquiryId, @Param("routeEnquiryCompanyId")Long routeEnquiryCompanyId);

    List<SearchRouteEnquirySummaryListResponseModel> searchSummaryList(@Param("params") SearchRouteEnquirySummaryListRequestModel requestModel);

    List<TRouteEnquiryAddress> getByRouteEnquiryId(@Param("routeEnquiryId")Long routeEnquiryId);

}