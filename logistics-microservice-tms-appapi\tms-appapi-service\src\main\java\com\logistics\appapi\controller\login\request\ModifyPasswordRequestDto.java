package com.logistics.appapi.controller.login.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/10/23
 * @description:
 */
@Data
public class ModifyPasswordRequestDto {

    @ApiModelProperty(value = "旧密码",required = true)
    @NotBlank(message = "旧密码不能为空")
    private String oldPassword;

    @ApiModelProperty(value = "新密码",required = true)
    @NotBlank(message = "新密码不能为空")
    private String newPassword;

}
