package com.logistics.tms.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author：wjf
 * @date：2021/4/12 13:12
 */
@Data
public class ReconciliationOilFilledListResponseModel {
    @ApiModelProperty(value = "充值时间")
    private Date oilFilledDate;
    @ApiModelProperty(value = "充油方式")
    private Integer oilFilledType;
    @ApiModelProperty(value = "副卡卡号")
    private String subCardNumber;
    @ApiModelProperty(value = "充值积分")
    private BigDecimal topUpIntegral;
    @ApiModelProperty(value = "奖励积分")
    private Integer rewardIntegral;
    @ApiModelProperty(value = "合作公司")
    private String cooperationCompany;
    @ApiModelProperty(value = "升数")
    private Integer liter;
    @ApiModelProperty(value = "充油金额")
    private BigDecimal oilFilledFee;
}
