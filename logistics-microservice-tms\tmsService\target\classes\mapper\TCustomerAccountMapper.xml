<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TCustomerAccountMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCustomerAccount">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_code" jdbcType="VARCHAR" property="userCode" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_account" jdbcType="VARCHAR" property="userAccount" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="user_password" jdbcType="VARCHAR" property="userPassword" />
    <result column="user_salt" jdbcType="VARCHAR" property="userSalt" />
    <result column="user_head_path" jdbcType="VARCHAR" property="userHeadPath" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
    <result column="enabled" jdbcType="INTEGER" property="enabled" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_code, user_name, user_account, email, user_password, user_salt, user_head_path, 
    open_id, enabled, remark, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_customer_account
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_customer_account
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCustomerAccount">
    insert into t_customer_account (id, user_code, user_name, 
      user_account, email, user_password, 
      user_salt, user_head_path, open_id, 
      enabled, remark, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{userCode,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, 
      #{userAccount,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{userPassword,jdbcType=VARCHAR}, 
      #{userSalt,jdbcType=VARCHAR}, #{userHeadPath,jdbcType=VARCHAR}, #{openId,jdbcType=VARCHAR}, 
      #{enabled,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCustomerAccount">
    insert into t_customer_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userCode != null">
        user_code,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="userAccount != null">
        user_account,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="userPassword != null">
        user_password,
      </if>
      <if test="userSalt != null">
        user_salt,
      </if>
      <if test="userHeadPath != null">
        user_head_path,
      </if>
      <if test="openId != null">
        open_id,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userCode != null">
        #{userCode,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userAccount != null">
        #{userAccount,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="userPassword != null">
        #{userPassword,jdbcType=VARCHAR},
      </if>
      <if test="userSalt != null">
        #{userSalt,jdbcType=VARCHAR},
      </if>
      <if test="userHeadPath != null">
        #{userHeadPath,jdbcType=VARCHAR},
      </if>
      <if test="openId != null">
        #{openId,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCustomerAccount">
    update t_customer_account
    <set>
      <if test="userCode != null">
        user_code = #{userCode,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userAccount != null">
        user_account = #{userAccount,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="userPassword != null">
        user_password = #{userPassword,jdbcType=VARCHAR},
      </if>
      <if test="userSalt != null">
        user_salt = #{userSalt,jdbcType=VARCHAR},
      </if>
      <if test="userHeadPath != null">
        user_head_path = #{userHeadPath,jdbcType=VARCHAR},
      </if>
      <if test="openId != null">
        open_id = #{openId,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCustomerAccount">
    update t_customer_account
    set user_code = #{userCode,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      user_account = #{userAccount,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      user_password = #{userPassword,jdbcType=VARCHAR},
      user_salt = #{userSalt,jdbcType=VARCHAR},
      user_head_path = #{userHeadPath,jdbcType=VARCHAR},
      open_id = #{openId,jdbcType=VARCHAR},
      enabled = #{enabled,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>