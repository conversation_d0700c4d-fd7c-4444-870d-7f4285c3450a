package com.logistics.management.webapi.controller.uploadfile.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/5/8 17:15
 */
@Data
public class DrivingLicenseResponseDto {
    @ApiModelProperty("姓名")
    private String name="";
    @ApiModelProperty("性别")
    private String sex="";
    @ApiModelProperty("有效期限始")
    private String startDate="";
    @ApiModelProperty("有效期限止")
    private String endDate="";
    @ApiModelProperty("证号")
    private String number="";
    @ApiModelProperty("出生日期")
    private String birthday="";
    @ApiModelProperty("准驾车型")
    private String type="";
    @ApiModelProperty("住址")
    private String address="";
    @ApiModelProperty("国籍")
    private String nationality="";
    @ApiModelProperty("初次领证日期")
    private String firstIssueDate="";
}
