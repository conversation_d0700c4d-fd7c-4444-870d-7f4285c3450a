package com.logistics.management.webapi.api.impl.transmap.mapping;

import com.logistics.management.webapi.api.feign.transmap.dto.CarrierOrderDestinationDto;
import com.logistics.management.webapi.api.feign.transmap.dto.SearchCarrierOrderDestinationByVehicleNoResponseDto;
import com.logistics.tms.api.feign.gpstrack.model.CarrierOrderDestinationModel;
import com.logistics.tms.api.feign.gpstrack.model.SearchCarrierOrderDestinationByVehicleNoResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;

import java.util.ArrayList;


public class CurrentPositionMapping extends MapperMapping<SearchCarrierOrderDestinationByVehicleNoResponseModel, SearchCarrierOrderDestinationByVehicleNoResponseDto> {

    @Override
    public void configure() {
        SearchCarrierOrderDestinationByVehicleNoResponseModel source = getSource();
        SearchCarrierOrderDestinationByVehicleNoResponseDto destination = getDestination();
        destination.setCarrierOrderDestinations(new ArrayList<>());
        //过滤被更换车辆的运单
        for(CarrierOrderDestinationModel tmp :source.getCarrierOrderDestinations()){
            if(tmp.getCarrierOrderVehicleId()!=null&&tmp.getCarrierOrderVehicleId().equals(source.getVehicleId())){
                destination.getCarrierOrderDestinations().add(MapperUtils.mapper(tmp, CarrierOrderDestinationDto.class));
            }
        }
        if(ListUtils.isEmpty(destination.getCarrierOrderDestinations())){
            destination.setDispatchOrderId("无调度单");
            destination.setDispatchOrderCode("无调度单");
        }
    }


}
