package com.logistics.tms.client.feign.auth.hystrix;

import com.logistics.tms.client.feign.auth.AuthTokenServiceApi;
import com.logistics.tms.client.feign.auth.request.CreateToken;
import com.logistics.tms.client.feign.auth.response.TokenModule;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2023/12/25 13:38
 */
@Component
public class AuthTokenServiceApiHystrix implements AuthTokenServiceApi {
    @Override
    public Result<TokenModule> createToken(CreateToken requestModel) {
        return Result.timeout();
    }
}
