package com.logistics.appapi.controller.attendance.mappping;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.AttendancePunchTypeEnum;
import com.logistics.appapi.client.attendance.response.AttendanceClockDetailResponseModel;
import com.logistics.appapi.controller.attendance.response.AttendanceClockDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import org.apache.commons.lang3.time.FastDateFormat;

import java.util.Optional;

public class AttendanceClockDetailMapping extends MapperMapping<AttendanceClockDetailResponseModel, AttendanceClockDetailResponseDto> {

    @Override
    public void configure() {

        AttendanceClockDetailResponseModel source = getSource();
        AttendanceClockDetailResponseDto destination = getDestination();

        // Enum处理
        destination.setIsOverClockInTypeLabel(AttendancePunchTypeEnum.getEnumByKey(source.getIsOverClockInType()).getValue());

        // 时间格式处理
        Optional.ofNullable(source)
                .ifPresent(s -> {
                    if (s.getOnDutyPunchTime() != null) {
                        String onDutyPunchTime = FastDateFormat.getInstance(CommonConstant.DATE_TO_STRING_HM_PATTERN).format(s.getOnDutyPunchTime());
                        destination.setOnDutyPunchTime(onDutyPunchTime);
                    }
                    if (s.getOffDutyPunchTime() != null) {
                        String offDutyPunchTime = FastDateFormat.getInstance(CommonConstant.DATE_TO_STRING_HM_PATTERN).format(s.getOffDutyPunchTime());
                        destination.setOffDutyPunchTime(offDutyPunchTime);
                    }
                });
    }
}
