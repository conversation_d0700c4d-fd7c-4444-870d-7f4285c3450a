package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/5/31 13:55
 */
public class ExportExcelDateRemind {
    private ExportExcelDateRemind() {
    }

    private static final Map<String, String> DATE_REMIND_MAP;

    static {
        DATE_REMIND_MAP = new LinkedHashMap<>();
        DATE_REMIND_MAP.put("日期名称", "dateName");
        DATE_REMIND_MAP.put("提醒设置", "ifRemindLabel");
        DATE_REMIND_MAP.put("预提醒天数", "remindDaysLabel");
        DATE_REMIND_MAP.put("备注", "remark");
        DATE_REMIND_MAP.put("添加人", "addUserName");
        DATE_REMIND_MAP.put("最近编辑人", "lastModifiedBy");
        DATE_REMIND_MAP.put("最新编辑时间", "lastModifiedTime");
    }

    public static Map<String, String> getDateRemindMap() {
        return DATE_REMIND_MAP;
    }
}
