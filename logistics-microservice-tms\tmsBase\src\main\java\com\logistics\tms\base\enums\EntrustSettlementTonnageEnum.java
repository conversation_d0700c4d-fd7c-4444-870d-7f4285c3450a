/**
 * Created by yun.zhou on 2017/12/12.
 */
package com.logistics.tms.base.enums;

public enum EntrustSettlementTonnageEnum {

    LOAD_AMOUNT(1, "实际提货数量"),
    UNLOAD_AMOUNT(2, "实际卸货数量"),
    SIGN_AMOUNT(3,"实际签收数量"),
    EXPECT_AMOUNT(4,"委托数量"),
    ;

    private Integer key;
    private String value;

    EntrustSettlementTonnageEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
