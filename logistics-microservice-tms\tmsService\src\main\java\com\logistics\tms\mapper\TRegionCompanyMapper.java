package com.logistics.tms.mapper;

import com.logistics.tms.entity.TRegionCompany;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* Created by Mybatis Generator on 2024/06/04
*/
@Mapper
public interface TRegionCompanyMapper extends BaseMapper<TRegionCompany> {

    List<Long> getCompanyByRegion(@Param("provinceId") Long provinceId, @Param("cityId") Long cityId);

    List<Long> getCompanyByRegionId(@Param("regionId") Long regionId);

    int batchInsert(@Param("list") List<TRegionCompany> list);

    int batchUpdate(@Param("list") List<TRegionCompany> list);

    void delByRegionId(@Param("regionId") Long regionId, @Param("userName") String userName, @Param("updateTime") Date updateTime);

}