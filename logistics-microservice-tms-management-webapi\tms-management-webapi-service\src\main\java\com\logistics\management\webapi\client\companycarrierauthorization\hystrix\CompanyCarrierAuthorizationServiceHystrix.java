package com.logistics.management.webapi.client.companycarrierauthorization.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.companycarrierauthorization.CompanyCarrierAuthorizationServiceApi;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.client.companycarrierauthorization.request.*;
import com.logistics.management.webapi.client.companycarrierauthorization.response.*;
import org.springframework.stereotype.Component;

@Component
public class CompanyCarrierAuthorizationServiceHystrix implements CompanyCarrierAuthorizationServiceApi {

    @Override
    public Result<PageInfo<CarrierAuthorizationListResponseModel>> carrierAuthorizationList(CarrierAuthorizationListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierAuthorizationDetailResponseModel> carrierAuthorizationDetail(CarrierAuthorizationDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> carrierAuthorizationAdd(CarrierAuthorizationAddRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> carrierAuthorizationAudit(CarrierAuthorizationAuditRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> carrierAuthorizationArchived(CarrierAuthorizationArchivedRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> carrierAuthorizationReArchive(CarrierAuthorizationReArchivedRequestModel requestModel) {
        return Result.timeout();
    }

}
