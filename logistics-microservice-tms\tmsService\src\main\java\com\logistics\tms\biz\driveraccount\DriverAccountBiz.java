package com.logistics.tms.biz.driveraccount;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.driverpayee.model.ViewLogResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.reservebalance.ReserveBalanceBiz;
import com.logistics.tms.client.BasicDataClient;
import com.logistics.tms.controller.driveraccount.request.*;
import com.logistics.tms.controller.driveraccount.response.*;
import com.logistics.tms.entity.TCertificationPictures;
import com.logistics.tms.entity.TDriverAccount;
import com.logistics.tms.entity.TOperateLogs;
import com.logistics.tms.entity.TStaffBasic;
import com.logistics.tms.mapper.TCertificationPicturesMapper;
import com.logistics.tms.mapper.TDriverAccountMapper;
import com.logistics.tms.mapper.TOperateLogsMapper;
import com.logistics.tms.mapper.TStaffBasicMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/27
 */
@Slf4j
@Service
public class DriverAccountBiz {

	@Resource
	private TDriverAccountMapper tDriverAccountMapper;
	@Resource
	private TStaffBasicMapper tStaffBasicMapper;
	@Resource
	private TOperateLogsMapper tOperateLogsMapper;
	@Resource
	private TCertificationPicturesMapper tCertificationPicturesMapper;
	@Resource
	private CommonBiz commonBiz;
	@Resource
	private ReserveBalanceBiz reserveBalanceBiz;
	@Resource
	private BasicDataClient basicDataClient;

	/**
	 * 新增/修改司机账户
	 *
	 * @param requestModel 请求Model
	 */
	@Transactional
	public void addAccount(DriverAccountAddRequestModel requestModel) {
		//查询司机是否存在
		TStaffBasic tStaffBasic = tStaffBasicMapper.selectByPrimaryKey(requestModel.getDriverId());
		if (tStaffBasic == null || IfValidEnum.INVALID.getKey().equals(tStaffBasic.getValid())) {
			throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
		}
		//自主自营司机
		if (!StaffPropertyEnum.OWN_STAFF.getKey().equals(tStaffBasic.getStaffProperty()) &&
				!StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(tStaffBasic.getStaffProperty())) {
			throw new BizException(CarrierDataExceptionEnum.DRIVER_ACCOUNT_STAFF_PROPERTY_ERROR);
		}

		if (requestModel.getDriverAccountId() == null) {
			/*新增司机账户*/
			//查询当前司机是否已经有账户了
			TDriverAccount tDriverAccountByDriverId = tDriverAccountMapper.selectOneByDriverId(requestModel.getDriverId());
			if (tDriverAccountByDriverId != null) {
				//一个司机只能有一个账户
				throw new BizException(CarrierDataExceptionEnum.DRIVER_ACCOUNT_IS_EXIST);
			}

			//查询当前账户是否已经存在了
			TDriverAccount tDriverAccountByDriverAccount = tDriverAccountMapper.selectOneByDriverAccount(requestModel.getBankAccount());
			if (tDriverAccountByDriverAccount != null) {
				throw new BizException(CarrierDataExceptionEnum.DRIVER_ACCOUNT_IS_EXIST);
			}

			//插入司机账户信息
			TDriverAccount tDriverAccount = MapperUtils.mapperNoDefault(requestModel, TDriverAccount.class);
			commonBiz.setBaseEntityAdd(tDriverAccount, BaseContextHandler.getUserName());
			tDriverAccountMapper.insertSelectiveEncrypt(tDriverAccount);

			//账户图片
			List<String> bankAccountImage = requestModel.getBankAccountImage();
			if (ListUtils.isNotEmpty(bankAccountImage)) {
				TCertificationPictures addCertificationPictures;
				List<TCertificationPictures> addCertificationPicturesList = new ArrayList<>();
				for (String picPath : bankAccountImage) {
					addCertificationPictures = getCertificationPictures(tDriverAccount.getId(), picPath);
					addCertificationPicturesList.add(addCertificationPictures);
				}
				//插入图片
				tCertificationPicturesMapper.batchInsert(addCertificationPicturesList);
			}

			String remark = "新增账号【" + requestModel.getBankAccount() + "】";
			//添加操作日志
			TOperateLogs tOperateLogs = commonBiz.addOperateLogs(tDriverAccount.getId(),
					OperateLogsOperateTypeEnum.DRIVER_ACCOUNT_ADD,
					remark,
					BaseContextHandler.getUserName());
			tOperateLogsMapper.insertSelective(tOperateLogs);

			// 初始化余额信息
			reserveBalanceBiz.createDriverBalance(tStaffBasic.getId());
		} else {
			/*编辑司机账户*/
			//查询司机账户是否存在
			TDriverAccount tDriverAccount = tDriverAccountMapper.selectByPrimaryKeyDecrypt(requestModel.getDriverAccountId());
			if (tDriverAccount == null || IfValidEnum.INVALID.getKey().equals(tDriverAccount.getValid())) {
				throw new BizException(CarrierDataExceptionEnum.DRIVER_ACCOUNT_NOT_EXIST);
			}

			//根据司机id查询司机账户
			TDriverAccount tDriverAccountByDriverId = tDriverAccountMapper.selectOneByDriverId(requestModel.getDriverId());
			if (tDriverAccountByDriverId != null && !tDriverAccount.getDriverId().equals(tDriverAccountByDriverId.getDriverId())) {
				//更换了司机,但是新更换的司机已经有账户了,不允许修改
				throw new BizException(CarrierDataExceptionEnum.DRIVER_ACCOUNT_IS_EXIST);
			}

			//根据账户查询
			TDriverAccount tDriverAccountByDriverAccount = tDriverAccountMapper.selectOneByDriverAccount(requestModel.getBankAccount());
			if (tDriverAccountByDriverAccount != null && !tDriverAccount.getId().equals(tDriverAccountByDriverAccount.getId())) {
				throw new BizException(CarrierDataExceptionEnum.DRIVER_ACCOUNT_IS_EXIST);
			}

			//根据账号查询司机账户
			TDriverAccount newCompanyAccount = tDriverAccountMapper.selectByBankAccountDriverId(requestModel.getBankAccount(), requestModel.getDriverId());
			if (newCompanyAccount != null && !newCompanyAccount.getId().equals(tDriverAccount.getId())) {
				//司机和账户都更换了,但是已经存在这样的记录了,不允许修改
				throw new BizException(CarrierDataExceptionEnum.DRIVER_ACCOUNT_IS_EXIST);
			}

			//更新账户信息
			TDriverAccount tDriverAccountUp = new TDriverAccount();
			tDriverAccountUp.setId(tDriverAccount.getId());
			MapperUtils.mapper(requestModel, tDriverAccountUp);
			tDriverAccountUp.setDriverId(null);//不更新司机
			commonBiz.setBaseEntityModify(tDriverAccountUp, BaseContextHandler.getUserName());
			tDriverAccountMapper.updateByPrimaryKeySelectiveEncrypt(tDriverAccountUp);

			//删除凭证
			tCertificationPicturesMapper.delByObjectTypeId(CertificationPicturesObjectTypeEnum.T_DRIVER_ACCOUNT_BANK_CARD_PIC.getObjectType(), tDriverAccount.getId(), BaseContextHandler.getUserName());
			//账户图片
			List<String> bankAccountImage = requestModel.getBankAccountImage();
			if (ListUtils.isNotEmpty(bankAccountImage)) {
				TCertificationPictures addCertificationPictures;
				List<TCertificationPictures> addCertificationPicturesList = new ArrayList<>();
				for (String picPath : bankAccountImage) {
					addCertificationPictures = getCertificationPictures(tDriverAccount.getId(), picPath);
					addCertificationPicturesList.add(addCertificationPictures);
				}
				//插入图片
				tCertificationPicturesMapper.batchInsert(addCertificationPicturesList);
			}

			String remark = "更换账号【" + tDriverAccount.getBankAccount() + "】";
			//添加操作日志
			TOperateLogs tOperateLogs = commonBiz.addOperateLogs(tDriverAccount.getId(),
					OperateLogsOperateTypeEnum.DRIVER_ACCOUNT_EDIT,
					remark,
					BaseContextHandler.getUserName());
			tOperateLogsMapper.insertSelective(tOperateLogs);
		}
	}

	/**
	 * 获取司机账户凭证
	 *
	 * @param objectId objectId
	 * @param picPath picPath
	 * @return 司机账户凭证
	 */
	private TCertificationPictures getCertificationPictures(Long objectId, String picPath) {
		TCertificationPictures addCertificationPictures;
		addCertificationPictures = new TCertificationPictures();
		addCertificationPictures.setObjectId(objectId);
		addCertificationPictures.setObjectType(CertificationPicturesObjectTypeEnum.T_DRIVER_ACCOUNT_BANK_CARD_PIC.getObjectType());
		addCertificationPictures.setFileType(CertificationPicturesFileTypeEnum.T_DRIVER_ACCOUNT_BANK_CARD_PIC_FILE.getFileType());
		addCertificationPictures.setFileTypeName(CertificationPicturesFileTypeEnum.T_DRIVER_ACCOUNT_BANK_CARD_PIC_FILE.getFileName());
		addCertificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DRIVER_ACCOUNT_BANK_CARD_PIC.getKey(), "", picPath, null));
		addCertificationPictures.setUploadUserName(BaseContextHandler.getUserName());
		addCertificationPictures.setUploadTime(new Date());
		addCertificationPictures.setSuffix(addCertificationPictures.getFilePath().substring(addCertificationPictures.getFilePath().lastIndexOf('.')));
		commonBiz.setBaseEntityAdd(addCertificationPictures, BaseContextHandler.getUserName());
		return addCertificationPictures;
	}

	/**
	 * 查询司机账户列表
	 *
	 * @param requestModel 请求Model
	 * @return 司机账户列表
	 */
	public PageInfo<SearchDriverAccountResponseModel> searchList(SearchDriverAccountRequestModel requestModel) {
		requestModel.enablePaging();
		List<SearchDriverAccountResponseModel> responseModelList = tDriverAccountMapper.searchList(requestModel);
		if (ListUtils.isNotEmpty(responseModelList)) {
			List<Long> picOutIds = new ArrayList<>();
			//查询银行账户图片
			for (SearchDriverAccountResponseModel driverAccountResponseModel : responseModelList) {
				picOutIds.add(driverAccountResponseModel.getDriverAccountId());
			}
			List<TCertificationPictures> tCertificationPicturesList = tCertificationPicturesMapper.getImageByIdsAndType(StringUtils.join(picOutIds, ','), CertificationPicturesObjectTypeEnum.T_DRIVER_ACCOUNT_BANK_CARD_PIC.getObjectType());
			if (ListUtils.isNotEmpty(tCertificationPicturesList)) {
				//分组图片
				Map<Long, List<TCertificationPictures>> certificationPicturesMap = tCertificationPicturesList.stream().collect(Collectors.groupingBy(TCertificationPictures::getObjectId));
				for (SearchDriverAccountResponseModel driverAccountResponseModel : responseModelList) {
					List<TCertificationPictures> tCertificationPicturesListScope = certificationPicturesMap.get(driverAccountResponseModel.getDriverAccountId());
					if (ListUtils.isNotEmpty(tCertificationPicturesListScope)) {
						//银行卡图片数量
						driverAccountResponseModel.setBankImageNumber(tCertificationPicturesListScope.size());
					}
				}
			}
		}
		return new PageInfo<>(responseModelList);
	}

	/**
	 * 司机账户详情
	 *
	 * @param requestModel 请求Model
	 * @return 司机账户详情
	 */
	public DriverAccountDetailResponseModel getDetail(DriverAccountDetailRequestModel requestModel) {
		DriverAccountDetailResponseModel driverAccountDetail = tDriverAccountMapper.getDetail(requestModel.getDriverAccountId());
		if (driverAccountDetail != null) {
			List<TCertificationPictures> tCertificationPicturesList = tCertificationPicturesMapper.getImageByIdsAndType(ConverterUtils.toString(driverAccountDetail.getDriverAccountId()), CertificationPicturesObjectTypeEnum.T_DRIVER_ACCOUNT_BANK_CARD_PIC.getObjectType());
			if (ListUtils.isNotEmpty(tCertificationPicturesList)) {
				List<String> imagePathList = new ArrayList<>();
				for (TCertificationPictures tCertificationPictures : tCertificationPicturesList) {
					imagePathList.add(tCertificationPictures.getFilePath());
				}
				driverAccountDetail.setBankAccountImages(imagePathList);
			}
		} else {
			throw new BizException(CarrierDataExceptionEnum.DRIVER_ACCOUNT_NOT_EXIST);
		}
		return driverAccountDetail;
	}

	/**
	 * 查询司机账户收款证件
	 *
	 * @param requestModel 请求Model
	 * @return 司机账户收款证件
	 */
	public DriverAccountImageResponseModel getAccountImageList(DriverAccountImageRequestModel requestModel) {
		TDriverAccount tDriverAccount = tDriverAccountMapper.selectByPrimaryKey(requestModel.getDriverAccountId());
		if (tDriverAccount == null || IfValidEnum.INVALID.getKey().equals(tDriverAccount.getValid())) {
			throw new BizException(CarrierDataExceptionEnum.DRIVER_ACCOUNT_NOT_EXIST);
		}
		DriverAccountImageResponseModel responseModel = new DriverAccountImageResponseModel();
		List<TCertificationPictures> tCertificationPicturesList = tCertificationPicturesMapper.getImageByIdsAndType(ConverterUtils.toString(tDriverAccount.getId()), CertificationPicturesObjectTypeEnum.T_DRIVER_ACCOUNT_BANK_CARD_PIC.getObjectType());
		if (ListUtils.isNotEmpty(tCertificationPicturesList)) {
			List<String> imagePathList = new ArrayList<>();
			for (TCertificationPictures tCertificationPictures : tCertificationPicturesList) {
				imagePathList.add(tCertificationPictures.getFilePath());
			}
			responseModel.setImagePaths(imagePathList);
		}
		return responseModel;
	}

	/**
	 * 操作日志列表
	 *
	 * @param requestModel 请求Model
	 * @return 日志列表
	 */
	public List<DriverAccountOperateLogResponseModel> getOperateLogList(DriverAccountOperateLogRequestModel requestModel) {
		TDriverAccount tDriverAccount = tDriverAccountMapper.selectByPrimaryKey(requestModel.getDriverAccountId());
		if (tDriverAccount == null || IfValidEnum.INVALID.getKey().equals(tDriverAccount.getValid())) {
			throw new BizException(CarrierDataExceptionEnum.DRIVER_ACCOUNT_NOT_EXIST);
		}
		List<DriverAccountOperateLogResponseModel> responseModelList = new ArrayList<>();
		//查询日志信息
		List<ViewLogResponseModel> viewLogResponseModels = tOperateLogsMapper.selectLogsByOperateType(OperateLogsObjectTypeEnum.DRIVER_ACCOUNT_ADD_EDIT.getKey(), tDriverAccount.getId(), null);
		if (ListUtils.isNotEmpty(viewLogResponseModels)) {
			for (ViewLogResponseModel viewLogResponseModel : viewLogResponseModels) {
				DriverAccountOperateLogResponseModel logResponseModel = MapperUtils.mapper(viewLogResponseModel, DriverAccountOperateLogResponseModel.class);
				logResponseModel.setOperateContent(viewLogResponseModel.getOperateContents());
				logResponseModel.setOperateName(viewLogResponseModel.getOperateUserName());
				responseModelList.add(logResponseModel);
			}
		}
		return responseModelList;
	}

	/**
	 * 根据司机Id查询司机账户
	 *
	 * @param driverId 司机Id
	 * @return 司机账户信息
	 */
	public TDriverAccount getAccountByDriverId(Long driverId) {
		return tDriverAccountMapper.selectOneByDriverId(driverId);
	}

	/**
	 * 小程序司机添加银行卡
	 *
	 * @param requestModel 请求Model
	 */
	@Transactional
	public void addBankCardForApplet(AddBankCardAppletRequestModel requestModel) {
		//获取当前登录司机id
		Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
		if (CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
			throw new BizException(CarrierDataExceptionEnum.DATA_ERROR_AND_RE_LOGIN);
		}
		//查询司机是否存在
		TStaffBasic tStaffBasic = tStaffBasicMapper.selectByPrimaryKey(loginDriverAppletUserId);
		if (tStaffBasic == null || IfValidEnum.INVALID.getKey().equals(tStaffBasic.getValid())) {
			throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
		}
		//校验短信验证码
		basicDataClient.checkVerificationCode(requestModel.getCodeSource(), requestModel.getCodeType(), tStaffBasic.getMobile(), requestModel.getVerificationCode());
		DriverAccountAddRequestModel requestModel1 = MapperUtils.mapper(requestModel, DriverAccountAddRequestModel.class);
		requestModel1.setDriverId(tStaffBasic.getId());
		addAccount(requestModel1);
	}

	/**
	 * 小程序获取当前登录司机绑定的银行卡信息
	 *
	 * @return 银行卡信息
	 */
	public BankCardInfoResponseModel currBankCardInfo() {
		//获取当前登录司机id
		Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
		if (CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
			throw new BizException(CarrierDataExceptionEnum.DATA_ERROR_AND_RE_LOGIN);
		}
		return getBankCardInfoByDriverId(loginDriverAppletUserId);
	}

	/**
	 * 根据司机获取绑定的银行卡信息
	 *
	 * @return 银行卡信息
	 */
	public BankCardInfoResponseModel getBankCardInfoByDriverId(Long driverId) {
		return tDriverAccountMapper.selectDetailByDriverId(driverId);
	}
}
