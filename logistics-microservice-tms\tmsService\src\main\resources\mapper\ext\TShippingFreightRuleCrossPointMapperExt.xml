<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TShippingFreightRuleCrossPointMapper">
    <select id="selectListByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_shipping_freight_rule_cross_point
        where valid = 1
        <if test="param1.shippingFreightRuleId != null">
            and shipping_freight_address_id = #{param1.shippingFreightRuleId,jdbcType=BIGINT}
        </if>
    </select>
    <update id="batchUpdateByAddressId" parameterType="com.logistics.tms.entity.TShippingFreightRuleCrossPoint">
        <foreach collection="list" item="item" separator=";">
            update t_shipping_freight_rule_cross_point
            <set >
                <if test="item.shippingFreightAddressId != null" >
                    shipping_freight_address_id = #{item.shippingFreightAddressId,jdbcType=BIGINT},
                </if>
                <if test="item.countStart != null" >
                    count_start = #{item.countStart,jdbcType=DECIMAL},
                </if>
                <if test="item.countEnd != null" >
                    count_end = #{item.countEnd,jdbcType=DECIMAL},
                </if>
                <if test="item.sort != null" >
                    sort = #{item.sort,jdbcType=INTEGER},
                </if>
                <if test="item.price != null" >
                    price = #{item.price,jdbcType=DECIMAL},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where shipping_freight_address_id = #{item.shippingFreightAddressId,jdbcType=BIGINT}
        </foreach>
    </update>

    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TShippingFreightRuleCrossPoint" >
        <foreach collection="list" item="item" separator=";">
            insert into t_shipping_freight_rule_cross_point
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    id,
                </if>
                <if test="item.shippingFreightAddressId != null" >
                    shipping_freight_address_id,
                </if>
                <if test="item.countStart != null" >
                    count_start,
                </if>
                <if test="item.countEnd != null" >
                    count_end,
                </if>
                <if test="item.sort != null" >
                    sort,
                </if>
                <if test="item.price != null" >
                    price,
                </if>
                <if test="item.createdBy != null" >
                    created_by,
                </if>
                <if test="item.createdTime != null" >
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="item.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.shippingFreightAddressId != null" >
                    #{item.shippingFreightAddressId,jdbcType=BIGINT},
                </if>
                <if test="item.countStart != null" >
                    #{item.countStart,jdbcType=DECIMAL},
                </if>
                <if test="item.countEnd != null" >
                    #{item.countEnd,jdbcType=DECIMAL},
                </if>
                <if test="item.sort != null" >
                    #{item.sort,jdbcType=INTEGER},
                </if>
                <if test="item.price != null" >
                    #{item.price,jdbcType=DECIMAL},
                </if>
                <if test="item.createdBy != null" >
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>


</mapper>