<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleTireNoMapper">
    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TVehicleTireNo">
        <foreach collection="list" item="item" separator=";">
            insert into t_vehicle_tire_no
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.tireId != null">
                    tire_id,
                </if>
                <if test="item.amount != null">
                    amount,
                </if>
                <if test="item.unitPrice != null">
                    unit_price,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
                <if test="item.tireBrand != null">
                    tire_brand,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.tireId != null">
                    #{item.tireId,jdbcType=BIGINT},
                </if>
                <if test="item.amount != null">
                    #{item.amount,jdbcType=INTEGER},
                </if>
                <if test="item.unitPrice != null">
                    #{item.unitPrice,jdbcType=DECIMAL},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
                <if test="item.tireBrand != null">
                    #{item.tireBrand,jdbcType=VARCHAR},
                </if>
            </trim>
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="com.logistics.tms.entity.TVehicleTireNo">
        <foreach collection="list" item="item" separator=";">
            update t_vehicle_tire_no
            <set>
                <if test="item.tireId != null">
                    tire_id= #{item.tireId,jdbcType=BIGINT},
                </if>
                <if test="item.amount != null">
                    amount=#{item.amount,jdbcType=INTEGER},
                </if>
                <if test="item.unitPrice != null">
                    unit_price=#{item.unitPrice,jdbcType=DECIMAL},
                </if>
                <if test="item.createdBy != null">
                    created_by=#{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time=#{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by=#{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time=#{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid=#{item.valid,jdbcType=INTEGER},
                </if>
                <if test="item.tireBrand != null">
                    tire_brand=#{item.tireBrand,jdbcType=VARCHAR},
                </if>
            </set>
            where id=#{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getByTireId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_vehicle_tire_no
        where valid = 1
        and tire_id = #{tireId,jdbcType=BIGINT}
    </select>
    <select id="getByTiredIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_vehicle_tire_no
        where valid = 1
        and tire_id in (${tireIds})
    </select>
</mapper>