package com.logistics.tms.controller.workordercenter.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/21
 */
@Data
public class WorkOrderProcessAppletResponseModel {

    @ApiModelProperty("状态：0 待处理，10 处理中，20 已处理，30 已关闭，40 已撤销")
    private Integer status;

    @ApiModelProperty("处理描述")
    private String solveDesc;

    @ApiModelProperty("角色,处理来源：1 后台，2 前台，3 小程序，4 任务中心")
    private Integer solveSource;

    @ApiModelProperty("时间")
    private Date solveTime;

    @ApiModelProperty("处理备注")
    private String solveRemark;
}
