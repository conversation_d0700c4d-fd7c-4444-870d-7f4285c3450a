package com.logistics.tms.mapper;

import com.logistics.tms.controller.oilfilled.request.OilFilledListRequestModel;
import com.logistics.tms.controller.oilfilled.response.OilFilledGetSummaryResponseModel;
import com.logistics.tms.controller.oilfilled.response.OilFilledListResponseModel;
import com.logistics.tms.controller.vehiclesettlement.response.GetOilFilledByVehicleIdResponseModel;
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleBySettlementMonthModel;
import com.logistics.tms.entity.TOilFilled;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TOilFilledMapper extends BaseMapper<TOilFilled> {

    List<OilFilledListResponseModel> getOilFilledList(@Param("params") OilFilledListRequestModel requestModel);

    OilFilledGetSummaryResponseModel getSummary(@Param("params") OilFilledListRequestModel requestModel);

    List<GetOilFilledByVehicleIdResponseModel> getOilFilledByIdsForSettlement(@Param("ids") String ids);

    int settlementOilFilledByIds(@Param("ids")String ids,@Param("userName")String userName);

    List<GetVehicleBySettlementMonthModel> getVehicleBySettlementMonth(@Param("settlementMonth") String settlementMonth);

    int getRefundCountByVehicleId(@Param("vehicleId")Long vehicleId);

    int rollbackSettlementOilFilledByIds(@Param("ids")String ids,@Param("userName")String userName);
}