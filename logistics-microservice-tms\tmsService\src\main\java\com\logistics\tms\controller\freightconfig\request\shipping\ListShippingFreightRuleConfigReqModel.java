package com.logistics.tms.controller.freightconfig.request.shipping;

import com.yelo.tray.core.page.AbstractPageForm;
import lombok.Data;

import java.util.List;


@Data
public class ListShippingFreightRuleConfigReqModel extends AbstractPageForm<ListShippingFreightRuleConfigReqModel> {

    /**
     * 是否导出,1导出，0不导出
     */
    private Integer ifExport;

    /**
     * 零担运价id
     */
    private Long shippingFreightId ;

    /**
     * 主键ids 用于勾选导出
     */
    private List<Long> shippingFreightRuleIds ;

    /**
     * 发货地
     */
    private String loadAddress = "";


    /**
     * 收货地
     */
    private String unloadAddress = "";


}
