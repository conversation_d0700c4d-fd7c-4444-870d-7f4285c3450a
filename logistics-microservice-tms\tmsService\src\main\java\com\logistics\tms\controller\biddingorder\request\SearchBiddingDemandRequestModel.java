package com.logistics.tms.controller.biddingorder.request;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SearchBiddingDemandRequestModel {


    /**
     * 发货地
     */
    private String loadAddress;

    /**
     * 卸货地
     */
    private String unloadAddress;

    /**
     * 货物数量-低档
     */
    private BigDecimal goodsCountLow;

    /**
     * 货物数量-高档
     */
    private BigDecimal goodsCountHigh;


}
