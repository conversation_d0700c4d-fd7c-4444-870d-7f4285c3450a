package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TGpsFeeRecords extends BaseEntity {
    /**
    * gps费用表id
    */
    @ApiModelProperty("gps费用表id")
    private Long gpsFeeId;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * gps服务商
    */
    @ApiModelProperty("gps服务商")
    private String gpsServiceProvider;

    /**
    * 起始日期
    */
    @ApiModelProperty("起始日期")
    private Date startDate;

    /**
    * 截止时间
    */
    @ApiModelProperty("截止时间")
    private Date endDate;

    /**
    * 终止时间
    */
    @ApiModelProperty("终止时间")
    private Date finishDate;

    /**
    * 服务费
    */
    @ApiModelProperty("服务费")
    private BigDecimal serviceFee;

    /**
    * 合作周期（月）
    */
    @ApiModelProperty("合作周期（月）")
    private Integer cooperationPeriod;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}