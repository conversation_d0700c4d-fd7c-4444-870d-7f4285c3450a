package com.logistics.management.webapi.api.feign.insuarance.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/6/4 18:01
 */
@Data
public class GetInsuranceDetailResponseDto {
    @ApiModelProperty("保险id")
    private String insuranceId = "";
    @ApiModelProperty("险种：1 商业险，2 交强险，3 个人意外险，4 货物险，5 危货承运人险")
    private String insuranceType;
    private String insuranceTypeDesc;
    @ApiModelProperty("车牌Id")
    private String vehicleId = "";
    @ApiModelProperty("车牌号")
    private String vehicleNo = "";
    @ApiModelProperty("司机id")
    private String driverId = "";
    @ApiModelProperty("司机姓名")
    private String driverName = "";
    @ApiModelProperty("保险公司id")
    private String insuranceCompanyId = "";
    @ApiModelProperty("保险公司")
    private String insuranceCompanyName = "";
    @ApiModelProperty("保单号")
    private String policyNumber = "";
    @ApiModelProperty("保费")
    private String premium = "";
    @ApiModelProperty("保险生效时间")
    private String startTime = "";
    @ApiModelProperty("保险截止时间")
    private String endTime = "";
    @ApiModelProperty("备注")
    private String remark = "";
    @ApiModelProperty("凭证")
    private List<InsuranceTicketsResponseDto> ticketList;
    @ApiModelProperty("退保证明")
    private String refundPath="";

    @ApiModelProperty("代缴车船税")
    private String paymentOfVehicleAndVesselTax = "";

    @ApiModelProperty("个人意外险表id")
    private String personalAccidentInsuranceId = "0";
    @ApiModelProperty("批单号")
    private String batchNumber = "";
    @ApiModelProperty("保单类型：1 保单，2 批单")
    private String policyType = "";
    private String policyTypeDesc = "";

    @ApiModelProperty("关联扣费保单id")
    private String relatedPersonalAccidentInsuranceId = "0";
    @ApiModelProperty("关联扣费保单号")
    private String relatedPersonalAccidentInsuranceNo = "";
    @ApiModelProperty("保笔保金")
    private String singlePremium = "";


    @ApiModelProperty("保单状态：结算状态：-1 未开始，0 待结算，1 部分结算，2 已结算")
    private String settlementStatus = "";
    private String settlementStatusDesc = "";

    @ApiModelProperty("保单状态：1 保障中，2 已过期，3 未开始，4 已作废，5 已退保")
    private String policyStatus = "";
    private String policyStatusDesc = "";

    @ApiModelProperty(value = "是否有账单月数据,1有，0没有")
    private String ifSettlement = "0";


}
