<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierFreightMapper" >

    <select id="selectFreightByCarrierCompanyIds" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from t_carrier_freight
      where valid = 1 and company_carrier_id in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id,jdbcType=BIGINT}
      </foreach>
      order by last_modified_time desc, id desc
    </select>

    <select id="getFreightByCarrierCompanyId" resultMap="BaseResultMap" parameterType="Long">
      select
      <include refid="Base_Column_List" />
      from t_carrier_freight
      where valid = 1 and company_carrier_id = #{carrierCompanyId,jdbcType=BIGINT}
    </select>

    <select id="selectFreightList" resultMap="BaseResultMap" resultType="com.logistics.tms.entity.TCarrierFreight">
      select
      <include refid="Base_Column_List" />
      from t_carrier_freight
      where valid = 1
        <if test="params.id !='' and params.id != null">
            and id = #{params.id}
        </if>
        <if test="params.companyCarrierId !='' and params.companyCarrierId != null">
            and company_carrier_id = #{params.companyCarrierId}
        </if>
        <if test="params.enabled !='' and params.enabled != null">
            and enabled = #{params.enabled}
        </if>
      order by last_modified_time desc, id desc
    </select>

    <select id="selectCarrierFreightBySchemeId" resultMap="BaseResultMap">
        select
        tcf.id,
        tcf.company_carrier_id,
        tcf.enabled,
        tcf.created_by,
        tcf.created_time,
        tcf.last_modified_by,
        tcf.last_modified_time,
        tcf.valid
        from t_carrier_freight tcf
        left join t_carrier_freight_config tcfc on tcfc.valid = 1 and tcf.id = tcfc.carrier_freight_id
        left join t_carrier_freight_config_scheme tcfcs on tcfcs.valid = 1 and tcfc.id = tcfcs.freight_config_id
        where tcf.valid = 1
        and tcfcs.id = #{schemeId,jdbcType=BIGINT}
        limit 1
    </select>
</mapper>