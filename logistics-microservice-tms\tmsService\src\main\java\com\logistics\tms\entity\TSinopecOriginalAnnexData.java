package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/12/14
*/
@Data
public class TSinopecOriginalAnnexData extends BaseEntity {
    /**
    * 中石化委托单原始数据表ID
    */
    @ApiModelProperty("中石化委托单原始数据表ID")
    private Long sinopecOriginalDataId;

    /**
    * 附件类型, 0: 危化品托运清单(hazardousChemicalsConsignList); 1: OMS上传附件(omsAttachment)
    */
    @ApiModelProperty("附件类型, 0: 危化品托运清单(hazardousChemicalsConsignList); 1: OMS上传附件(omsAttachment)")
    private Integer annexType;

    /**
    * 附件名(非必填)
    */
    @ApiModelProperty("附件名(非必填)")
    private String fileName;

    /**
    * 附件地址(非必填)
    */
    @ApiModelProperty("附件地址(非必填)")
    private String fileUrl;
}