package com.logistics.tms.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2019/9/11 19:43
 */
@Data
public class SaveDemandOrderGoodsRequestModel {
    @ApiModelProperty("货物品名")
    private String goodsName;
    @ApiModelProperty("规格")
    private String goodsSize;
    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;
    @ApiModelProperty("货物数量")
    private BigDecimal goodsAmount;
}
