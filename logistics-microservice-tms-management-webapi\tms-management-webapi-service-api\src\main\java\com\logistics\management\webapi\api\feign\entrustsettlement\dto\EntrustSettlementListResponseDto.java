package com.logistics.management.webapi.api.feign.entrustsettlement.dto;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EntrustSettlementListResponseDto {
    @ApiModelProperty("总单数")
    private String totalEntrustOrderCount="";
    @ApiModelProperty("总吨数")
    private String totalWeightSettlementAmount="";
    @ApiModelProperty("总件数")
    private String totalPackageSettlementAmount="";
    @ApiModelProperty("总金额")
    private String totalSettlementCost="";
    @ApiModelProperty("分页信息")
    private PageInfo<EntrustSettlementRowDto> pageInfo;
}
