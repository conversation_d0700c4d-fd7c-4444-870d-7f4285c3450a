package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/04/28
 */
@AllArgsConstructor
@Getter
public enum BiddingOrderIfCancelEnum {
    DEFAULT(0, "否"),
    YES(1,"是"),
    ;

    private final Integer key;
    private final String value;

    public static BiddingOrderIfCancelEnum getEnum(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
