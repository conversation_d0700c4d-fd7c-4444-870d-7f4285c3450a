package com.logistics.tms.client.feign.tray.basicdata.commonaddress;

import com.logistics.tms.client.feign.FeignClientName;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.hystrix.LeyiCommonAddressServiceApiHystrix;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.request.BatchQueryCustomerInfoRequest;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.request.UpdateAccessibilityVerifyRequestModel;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.response.AddressInfoResponseModel;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.response.BatchQueryCustomerInfoResponse;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/5/17 9:20
 */
@FeignClient(name = FeignClientName.LEYI_BASIC_DATA_SERVICES, fallback = LeyiCommonAddressServiceApiHystrix.class)
public interface LeyiCommonAddressServiceApi {

    @PostMapping("/service/commonAddress/updateAccessibilityVerifyFromGroundPush")
    @ApiOperation("根据地址code给地址打标(地推->云盘)")
    Result<Boolean> updateAccessibilityVerifyFromGroundPush(@RequestBody UpdateAccessibilityVerifyRequestModel requestModel);



    /**
     * 批量查询客户信息 250807
     */
    @ApiOperation(value = "批量查询客户信息")
    @PostMapping(value = "/service/customer/batchQueryCustomerInfo")
    Result<List<BatchQueryCustomerInfoResponse>> batchQueryCustomerInfo(@RequestBody @Valid BatchQueryCustomerInfoRequest request);
}
