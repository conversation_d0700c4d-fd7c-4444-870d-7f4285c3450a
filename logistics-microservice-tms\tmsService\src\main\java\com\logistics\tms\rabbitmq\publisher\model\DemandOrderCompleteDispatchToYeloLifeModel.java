package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/7/30 9:34
 */
@Data
public class DemandOrderCompleteDispatchToYeloLifeModel {

    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("客户单号")
    private String customerOrderCode;

    @ApiModelProperty(value = "操作人")
    private String userName;

    @ApiModelProperty(value = "需求单是否签收状态：0 否，1 是")
    private Integer demandOrderStatusSign = 0;

}
