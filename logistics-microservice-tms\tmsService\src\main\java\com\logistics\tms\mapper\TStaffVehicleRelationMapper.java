package com.logistics.tms.mapper;

import com.logistics.tms.biz.staffvehiclerelation.model.StaffVehicleRelationModel;
import com.logistics.tms.biz.staffvehiclerelation.model.TStaffVehicleRelationModel;
import com.logistics.tms.controller.staffvehiclerelation.request.SearchStaffVehicleListRequestModel;
import com.logistics.tms.controller.staffvehiclerelation.request.StaffAndVehicleSearchRequestModel;
import com.logistics.tms.controller.staffvehiclerelation.response.SafeCheckStaffRelResponseModel;
import com.logistics.tms.controller.staffvehiclerelation.response.SearchStaffVehicleListResponseModel;
import com.logistics.tms.controller.staffvehiclerelation.response.StaffAndVehicleSearchResponseModel;
import com.logistics.tms.entity.TStaffVehicleRelation;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TStaffVehicleRelationMapper extends BaseMapper<TStaffVehicleRelation> {

    int batchUpdate(@Param("list") List<TStaffVehicleRelation> list);

    List<SearchStaffVehicleListResponseModel> searchStaffVehicleList(@Param("params") SearchStaffVehicleListRequestModel requestModel);

    List<TStaffVehicleRelation> findRelationByStaffId(@Param("staffId") Long staffId);

    List<TStaffVehicleRelation> findRelationByVehicleId(@Param("vehicleId") Long vehicleId);

    List<TStaffVehicleRelation> findRelationByTrailerVehicleId(@Param("trailerVehicleId") Long trailerVehicleId);

    List<TStaffVehicleRelationModel> getRelByStaffId(@Param("staffId") Long staffId);

    List<TStaffVehicleRelationModel> getRelByVehicleId(@Param("vehicleId") Long vehicleId);

    List<TStaffVehicleRelationModel> getRelByTrailerVehicleId(@Param("trailerVehicleId") Long trailerVehicleId);

    List<TStaffVehicleRelation> findRelationList();

    StaffVehicleRelationModel findRelationByPrimaryId(@Param("id") Long tractorVehicleRelationId);

    List<StaffAndVehicleSearchResponseModel> getDriverAndVehicleByVehicleNumber(@Param("params") StaffAndVehicleSearchRequestModel requestModel);


    /**
     * 调度车辆-查询车辆司机关联关系
     *
     * @param vehicleId
     * @param staffId
     * @return
     */
    TStaffVehicleRelation getByVehicleIdAndDriverId(@Param("companyCarrierId") Long companyCarrierId, @Param("vehicleId") Long vehicleId, @Param("staffId") Long staffId);

    /**
     * 调度车辆-查询车辆列表
     * @param requestModel
     * @return
     */
    List<StaffAndVehicleSearchResponseModel> getDispatchVehicleListByVehicleNo(@Param("params") StaffAndVehicleSearchRequestModel requestModel);

    /**
     * 挂车ID可能为空,特殊处理
     * @param rel
     * @return
     */
    int updateByPrimaryKeySelectiveExt(TStaffVehicleRelation rel);

    /**
     * 匹配数据唯一性
     * @param staffId
     * @param tractorVehicleId
     * @param trailerVehicleId
     * @return
     */
    Long getRelationKeyById(@Param("staffId") Long staffId, @Param("tractorVehicleId") Long tractorVehicleId, @Param("trailerVehicleId") Long trailerVehicleId);

    /**
     * 车辆安全检查提供列表
     * @param vehicleNo
     * @return
     */
    List<SafeCheckStaffRelResponseModel> findRel4VehicleSafeCheck(@Param("vehicleNo") String vehicleNo, @Param("vehicleId") Long vehicleId);

    List<SafeCheckStaffRelResponseModel> findRelList4SafeCheck(@Param("vehicleIds") String vehicleIds);

    List<TStaffVehicleRelation> selectRelationsByVehicleIdDriverId(@Param("driverIds") String driverIds, @Param("vehicleIds" ) String vehicleIds, @Param("companyCarrierId")Long companyCarrierId);

    List<TStaffVehicleRelation> selectAllRelationsByVehicleIdDriverId(@Param("driverIds") String driverIds, @Param("vehicleIds" ) String vehicleIds);


    List<TStaffVehicleRelation> selectRelationsByStaffIds(@Param("driverIds") String driverIds);

    List<TStaffVehicleRelation> selectRelationsByIdsCarrierId(@Param("ids") List<Long> ids, @Param("companyCarrierId") Long companyCarrierId);
}