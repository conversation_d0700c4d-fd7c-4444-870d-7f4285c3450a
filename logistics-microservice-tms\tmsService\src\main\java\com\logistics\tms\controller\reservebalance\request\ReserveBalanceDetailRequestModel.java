package com.logistics.tms.controller.reservebalance.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReserveBalanceDetailRequestModel extends AbstractPageForm<ReserveBalanceDetailRequestModel> {

    @ApiModelProperty(value = "余额ID", required = true)
    private Long reserveBalanceId;

    @ApiModelProperty(value = "筛选月份,开始月份 yyyy-MM")
    private String startMonth;

    @ApiModelProperty(value = "筛选月份,结束月份 yyyy-MM")
    private String endMonth;
}
