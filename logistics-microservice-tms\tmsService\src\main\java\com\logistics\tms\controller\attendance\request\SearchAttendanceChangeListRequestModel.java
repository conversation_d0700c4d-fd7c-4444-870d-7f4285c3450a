package com.logistics.tms.controller.attendance.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchAttendanceChangeListRequestModel extends AbstractPageForm<SearchAttendanceChangeListRequestModel> {

    @ApiModelProperty("考勤用户,姓名或手机号模糊搜索")
    private String attendanceUser;

    //审核状态
    @ApiModelProperty("审核状态: 0 待审核，1 已审核，2 已驳回，3 已撤销")
    private Integer auditStatus;

    //人员机构
    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    //考勤查询开始时间
    @ApiModelProperty("考勤查询开始时间")
    private String attendanceStartTime;

    //考勤查询结束时间
    @ApiModelProperty("考勤查询结束时间")
    private String attendanceEndTime;

    //操作查询开始时间
    @ApiModelProperty("操作查询开始时间")
    private String operateStartTime;

    //操作查询开始时间
    @ApiModelProperty("操作查询结束时间")
    private String operateEndTime;
}
