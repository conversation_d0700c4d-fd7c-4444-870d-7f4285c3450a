package com.logistics.management.webapi.client.freightconfig.hystrix;

import com.logistics.management.webapi.client.freightconfig.CarrierFreightConfigRegionClient;
import com.logistics.management.webapi.client.freightconfig.request.region.CarrierFreightConfigRegionAddRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.region.CarrierFreightConfigRegionEditRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.region.CarrierFreightConfigRegionRequestModel;
import com.logistics.management.webapi.client.freightconfig.response.region.CarrierFreightConfigRegionResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CarrierFreightConfigRegionHystrix implements CarrierFreightConfigRegionClient {

    @Override
    public Result<List<CarrierFreightConfigRegionResponseModel>> detail(CarrierFreightConfigRegionRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> add(CarrierFreightConfigRegionAddRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> edit(CarrierFreightConfigRegionEditRequestModel requestModel) {
        return Result.timeout();
    }
}
