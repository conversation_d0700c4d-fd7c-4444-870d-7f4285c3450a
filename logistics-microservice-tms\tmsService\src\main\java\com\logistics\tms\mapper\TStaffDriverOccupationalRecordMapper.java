package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.controller.staff.response.OccupationalListResponseModel;
import com.logistics.tms.entity.TStaffDriverOccupationalRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface TStaffDriverOccupationalRecordMapper extends BaseMapper<TStaffDriverOccupationalRecord>{

    List<OccupationalListResponseModel> selectOccupationalList(@Param("staffId") Long staffId);

    Map<String,String> getDueOccupationalCount(@Param("companyCarrierId") Long companyCarrierId,@Param("remindDays") Integer remindDays);

    int batchUpdate(@Param("list") List<TStaffDriverOccupationalRecord> list);

    int selectRecordByStaffIdAndValidDate(@Param("staffId") Long staffId, @Param("validDate") Date date, @Param("issueDate") Date issueDate);

    List<TStaffDriverOccupationalRecord> getOccupationalRecordsByStaffId(@Param("staffIds") String staffIds);



}