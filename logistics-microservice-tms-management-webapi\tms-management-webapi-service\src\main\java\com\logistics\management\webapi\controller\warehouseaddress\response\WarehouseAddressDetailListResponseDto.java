package com.logistics.management.webapi.controller.warehouseaddress.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/12/27 16:00
 */
@Data
public class WarehouseAddressDetailListResponseDto {
    @ApiModelProperty("仓库ID")
    private String warehouseAddressId;
    @ApiModelProperty("仓库名称")
    private String warehouse;
    @ApiModelProperty("省份ID")
    private String provinceId;
    @ApiModelProperty("省份名称")
    private String provinceName;
    @ApiModelProperty("城市ID")
    private String cityId;
    @ApiModelProperty("城市名称")
    private String cityName;
    @ApiModelProperty("区县ID")
    private String areaId;
    @ApiModelProperty("区县名称")
    private String areaName;
}
