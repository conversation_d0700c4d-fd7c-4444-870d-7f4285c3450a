package com.logistics.tms.api.impl.insurancecompany;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.insurancecompany.InsuranceCompanyServiceApi;
import com.logistics.tms.api.feign.insurancecompany.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.insurancecompany.InsuranceCompanyBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/5/29 14:16
 */
@RestController
public class InsuranceCompanyServiceApiImpl implements InsuranceCompanyServiceApi {
    @Autowired
    private InsuranceCompanyBiz insuranceCompanyBiz;

    /**
     * 保险公司列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<InsuranceCompanyListResponseModel>> searchInsuranceCompanyList(@RequestBody InsuranceCompanyListRequestModel requestModel) {
        return Result.success(insuranceCompanyBiz.searchInsuranceCompanyList(requestModel));
    }

    /**
     * 查看详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<InsuranceCompanyDetailResponseModel> getDetail(@RequestBody InsuranceCompanyDetailRequestModel requestModel) {
        return Result.success(insuranceCompanyBiz.getDetail(requestModel));
    }

    /**
     * 保险公司新增修改
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> saveOrModifyInsuranceCompany(@RequestBody SaveOrModifyInsuranceCompanyRequestModel requestModel) {
        insuranceCompanyBiz.saveOrModifyInsuranceCompany(requestModel);
        return Result.success(true);
    }

    /**
     * 启用/禁用保险公司
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> enableOrDisable(@RequestBody EnableInsuranceCompanyRequestModel requestModel) {
        insuranceCompanyBiz.enableOrDisable(requestModel);
        return Result.success(true);
    }

    /**
     * 导出
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<InsuranceCompanyListResponseModel>> export(@RequestBody InsuranceCompanyListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        PageInfo<InsuranceCompanyListResponseModel> pageInfo = insuranceCompanyBiz.searchInsuranceCompanyList(requestModel);
        return Result.success(pageInfo.getList());
    }

    /**
     * 导入
     * @param requestModel
     * @return
     */
    @Override
    public Result<ImportInsuranceCompanyResponseModel> importInsuranceCompany(@RequestBody ImportInsuranceCompanyRequestModel requestModel) {
        return Result.success(insuranceCompanyBiz.batchImportInsuranceCompany(requestModel));
    }

    /**
     * 根据名称模糊匹配保险公司
     * @param requestModel
     * @return
     */
    @Override
    public Result<List<FuzzyQueryInsuranceCompanyListResponseModel>> fuzzyQueryInsuranceCompanyByName(@RequestBody FuzzyQueryInsuranceCompanyRequestModel requestModel) {
        return Result.success(insuranceCompanyBiz.fuzzyQueryInsuranceCompany(requestModel));
    }
}
