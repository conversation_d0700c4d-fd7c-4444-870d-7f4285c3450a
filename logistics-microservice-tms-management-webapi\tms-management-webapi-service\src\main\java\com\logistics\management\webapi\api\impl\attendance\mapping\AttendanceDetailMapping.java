package com.logistics.management.webapi.api.impl.attendance.mapping;

import com.logistics.management.webapi.api.feign.attendance.dto.AttendanceDetailResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.StaffPropertyEnum;
import com.logistics.tms.api.feign.attendance.model.AttendanceDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;
import lombok.AllArgsConstructor;

import java.util.Map;

@AllArgsConstructor
public class AttendanceDetailMapping extends MapperMapping<AttendanceDetailResponseModel, AttendanceDetailResponseDto> {

    private final String ATTENDANCE_USER_FORMAT = "【%s】%s_%s";
    private String imagePrefix;
    private Map<String, String> imageMap;

    @Override
    public void configure() {

        AttendanceDetailResponseModel source = getSource();
        AttendanceDetailResponseDto destination = getDestination();

        // 司机名称转换
        String staffName = source.getStaffName();
        String staffMobile = source.getStaffMobile();
        String propertyLabel = StaffPropertyEnum.getEnum(source.getStaffProperty()).getValue();
        destination.setAttendanceUser(String.format(ATTENDANCE_USER_FORMAT, propertyLabel, staffName, staffMobile));

        // 考勤时间转换
        destination.setAttendanceDate(DateUtils.dateToString(source.getAttendanceDate(), CommonConstant.DATE_TO_STRING_YMD_PATTERN_TEXT));
        destination.setOnDutyPunchDate(DateUtils.dateToString(source.getOnDutyPunchTime(), CommonConstant.DATE_TO_STRING_YMD_PATTERN));
        destination.setOnDutyPunchTime(DateUtils.dateToString(source.getOnDutyPunchTime(), CommonConstant.DATE_TO_STRING_HM_PATTERN));

        if (source.getOffDutyPunchTime() != null) {
            destination.setOffDutyPunchDate(DateUtils.dateToString(source.getOffDutyPunchTime(), CommonConstant.DATE_TO_STRING_YMD_PATTERN));
            destination.setOffDutyPunchTime(DateUtils.dateToString(source.getOffDutyPunchTime(), CommonConstant.DATE_TO_STRING_HM_PATTERN));
        }

        // 图片处理
        destination.setOnDutyPunchPic(getPicPath(source.getOnDutyPunchPic()));
        destination.setOffDutyPunchPic(getPicPath(source.getOffDutyPunchPic()));
    }

    private String getPicPath(String dutyPunchPic) {
        String pic = imageMap.getOrDefault(dutyPunchPic, CommonConstant.BLANK_TEXT);
        if (StringUtils.isNotBlank(pic)) {
            return imagePrefix.concat(pic);
        }
        return CommonConstant.BLANK_TEXT;
    }
}
