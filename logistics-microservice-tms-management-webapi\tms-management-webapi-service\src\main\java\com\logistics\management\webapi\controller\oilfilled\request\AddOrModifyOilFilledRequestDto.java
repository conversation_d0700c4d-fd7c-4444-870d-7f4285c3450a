package com.logistics.management.webapi.controller.oilfilled.request;

import com.logistics.management.webapi.controller.oilfilled.response.OilFilledFileDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

@Data
public class AddOrModifyOilFilledRequestDto {

    @ApiModelProperty(value = "充油ID,修改时候传入，新增不传")
    private String oilFilledId;
    @ApiModelProperty(value = "充油方式：1 充油卡，2 加油车")
    @NotBlank(message = "充油方式不能为空")
    private String oilFilledType;
    @ApiModelProperty(value = "车牌号Id")
    @NotBlank(message = "车牌号ID不能为空")
    private String vehicleId;
    @ApiModelProperty(value = "车牌号号码")
    private String vehicleNo;
    @ApiModelProperty(value = "司机ID")
    @NotBlank(message = "司机ID不能为空")
    private String staffId;
    @ApiModelProperty(value = "司机")
    private String name;
    @ApiModelProperty(value = "副卡卡号，当充值方式为充油卡时候传")
    private String subCardNumber;
    @ApiModelProperty(value = "副卡所属人，当充值方式为充油卡时候传")
    private String subCardOwner;
    @ApiModelProperty(value = "充值金额/总金额")
    @NotBlank(message = "充值金额不能为空")
    @Pattern(regexp = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,2})$)",message = "请维护充值金额，0≤充值金额≤100000，且保留两位小数")
    @DecimalMin(value = "0",message = "请维护充值金额，0≤充值金额≤100000，且保留两位小数")
    @DecimalMax(value = "100000.00",message = "请维护充值金额，0≤充值金额≤100000，且保留两位小数")
    private String oilFilledFee;
    @ApiModelProperty(value = "充值积分")
    private String topUpIntegral;
    @ApiModelProperty(value = "奖励积分")
    private String rewardIntegral;
    @ApiModelProperty(value = "充值时间/加油时间")
    @NotBlank(message = "充值时间不能为空")
    @Pattern(regexp = "[\\d]{4}-[\\d]{1,2}-[\\d]{1,2}",message = "请维护正确的充值/加油时间")
    private String oilFilledDate;
    @ApiModelProperty(value = "合作公司,当充值方式为加油车时候传")
    private String cooperationCompany;
    @ApiModelProperty(value = "升数,当充值方式为加油车时候传")
    private String liter;
    @ApiModelProperty(value = "备注")
    @Size(max = 300,message = "备注不超过300字")
    private String remark;

    @ApiModelProperty(value = "附件集合")
    private List<OilFilledFileDto> oilFilledFileList;

}
