package com.logistics.management.webapi.controller.carrierorderticketsaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchReceiptAuditListResponseDto {

    @ApiModelProperty(value = "回单审核Id")
    private String receiptAuditId="";

    @ApiModelProperty(value = "运单Id")
    private String carrierOrderId="";

    @ApiModelProperty(value = "运单号")
    private String carrierOrderCode="";

    @ApiModelProperty(value = "审核状态; 0 待审核，1 已审核，2 已驳回")
    private String auditStatus="";
    @ApiModelProperty(value = "审核状态文本")
    private String auditStatusLabel="";

    @ApiModelProperty("1.3.1新增；需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private String entrustType="";
    @ApiModelProperty("1.3.1新增；需求类型")
    private String entrustTypeLabel="";

    // 发货省市区 + 发货仓库，发货仓库使用【】包裹
    @ApiModelProperty(value = "发货地")
    private String loadAddress="";

    // 展示收货省市区 + 收货仓库，收货仓库使用【】包裹
    @ApiModelProperty(value = "收货地")
    private String unloadAddress="";

    // 展示运单司机姓名以及司机手机号，手机号加密
    @ApiModelProperty(value = "司机")
    private String driverName="";

    @ApiModelProperty(value = "车辆")
    private String vehicleNo="";

    @ApiModelProperty(value = "备注")
    private String remark="";

    @ApiModelProperty(value = "卸货时间")
    private String unloadTime="";

    @ApiModelProperty(value = "单据上传时间")
    private String ticketUploadTime="";

    @ApiModelProperty(value = "单据审核时间")
    private String ticketAuditTime="";

    @ApiModelProperty("操作人")
    private String lastModifiedBy="";

    @ApiModelProperty("操作时间")
    private String lastModifiedTime="";
}
