package com.logistics.tms.base.constant;

import com.google.common.base.Joiner;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Map;
import java.util.function.BiFunction;

public class CommonConstant {


    private CommonConstant() {
    }

    public static final String LOGISTICS_SINOPEC_SN="LOGISTICS_SINOPEC_SN_";

    public static final String LOGISTICS_SINOPEC_ASSOCIATED="LOGISTICS_SINOPEC_ASS_";

    public static final String DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES = "yyyy-MM-dd HH:mm";
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String HH_MM = "HH:mm";

    public static final String HYPHEN = "-";

    public static final String PIC_JPG = "jpg";
    public static final String RETURN_SUCCESS = "1";
    public static final String RETURN_FAILURE = "-1";
    public static final String COMMA_ONE = ",0";
    public static final String ZORE_NET_ZERO = "0.00";
    public static final String POINT_TWO_STR = "0.2";
    public static final String COMMA = ",";

    public static final String ONE = "1";
    public static final String TWO = "2";
    public static final String THREE = "3";
    public static final String ZERO = "0";
    public static final String FOUR = "4";
    public static final String FIVE = "5";
    public static final String SIX = "6";
    public static final String SEVEN = "7";
    public static final String EIGHT = "8";
    public static final String NINE = "9";
    public static final String ONE_ZERO = "10";
    public static final String TWO_ZERO = "20";
    public static final String THREE_ZERO = "30";

    public static final String TWO_HUNDRED = "200";
    public static final Integer INTEGER_FIFTY = 50;
    public static final String VALIDATION_GOODS = "validation_goods";
    public static final String LIMITED_IDEMPOTENT_MESSAGE = "请勿重复请求，稍后再试";
    public static final String OPERATION_EXECUTING_MESSAGE = "操作执行中，请稍后再试";
    public static final int WAIT_CARRIER_BIDDING_EXPIRED_TIME = 30;//委托单发布12m内无抢单，自动超时取消
    public static final BigDecimal BIG_DECIMAL_ZERO = new BigDecimal(0);
    public static final BigDecimal NEGATIVE_ONE = new BigDecimal(-1);
    public static final BigDecimal BIG_DECIMAL_ONE = new BigDecimal(1);
    public static final BigDecimal BIG_DECIMAL_TWO = new BigDecimal(2);
    public static final BigDecimal BIG_DECIMAL_THREE = new BigDecimal(3);
    public static final BigDecimal BIG_DECIMAL_FOUR = new BigDecimal(4);
    public static final BigDecimal BIG_DECIMAL_FIVE= new BigDecimal(5);
    public static final BigDecimal BIG_DECIMAL_TWENTY= new BigDecimal(20);
    public static final BigDecimal BIG_DECIMAL_FIFTY= new BigDecimal(50);
    public static final BigDecimal BIG_DECIMAL_THIRTY = new BigDecimal("30");
    public static final BigDecimal BIG_DECIMAL_SIXTY = new BigDecimal("60");
    public static final BigDecimal BIG_DECIMAL_ZERO_NET_ZERO = new BigDecimal("0.00");
    public static final BigDecimal ZERO_POINT_ZERO_ONE = new BigDecimal("0.01");
    public static final BigDecimal ZERO_POINT_FIVE = new BigDecimal("0.5");
    public static final  BigDecimal BIG_DECIMAL_TEN_THOUSAND = new BigDecimal("10000");
    public static final  BigDecimal FIFTY_THOUSAND = new BigDecimal("50000");
    public static final  BigDecimal BIG_DECIMAL_ONE_HUNDRED_THOUSAND = new BigDecimal("100000");
    public static final BigDecimal ONE_MILLION = new BigDecimal("1000000.00");
    public static final BigDecimal BIG_DECIMAL_ONE_HUNDRED = new BigDecimal("100");
    public static final BigDecimal BIG_DECIMAL_TWO_HUNDRED_NET_ZERO = new BigDecimal("200.000");
    public static final  BigDecimal BIG_DECIMAL_FIVE_THOUSAND_HUNDRED = new BigDecimal(5000);
    public static final  BigDecimal BIG_DECIMAL_THREE_THOUSAND_HUNDRED = new BigDecimal(3000);

    public static final String UPLOAD_FILE_PATH_TIMESTAMP_FORMATTER = "yyyyMM";
    public static final Integer NEGATIVE_INTEGER_ONE = -1;
    public static final Integer NEGATIVE_INTEGER_TWO = -2;
    public static final Integer INTEGER_ONE = 1;
    public static final Integer INTEGER_ZERO = 0;
    public static final Integer INTEGER_TWO = 2;
    public static final Integer INTEGER_THREE = 3;
    public static final Integer INTEGER_FOUR = 4;
    public static final Integer INT_FIFTY = 50;
    public static final Integer INT_THREE = 3;
    public static final Integer INTEGER_FIVE = 5;
    public static final Integer INTEGER_SIX = 6;
    public static final Integer INTEGER_SEVEN = 7;
    public static final Integer INTEGER_EIGHT = 8;
    public static final Integer INTEGER_NINE = 9;
    public static final Integer INTEGER_TEN = 10;
    public static final Integer INTEGER_ELVEN = 11;
    public static final Integer INTEGER_TWELVE = 12;
    public static final Integer INTEGER_THIRTEEN = 13;
    public static final Integer INTEGER_FOURTEEN = 14;
    public static final Integer INTEGER_FIFTEEN = 15;
    public static final Integer INTEGER_SIXTEEN = 16;
    public static final  Integer INTEGER_SEVENTEEN = 17;
    public static final  Integer INTEGER_EIGHTTEEN = 18;
    public static final Integer INTEGER_TWENTY = 20;
    public static final Integer INTEGER_TWENTY_ONE = 21;
    public static final Integer INTEGER_THIRTY = 30;
    public static final Integer INTEGER_THIRTY_ONE = 31;
    public static final Integer INTEGER_TWENTY_FIVE = 25;
    public static final Integer INTEGER_NINETY_NINE = 99;
    public static final Integer INTEGER_ONE_HUNDRED = 100;
    public static final Integer INTEGER_TWO_HUNDRED = 200;
    public static final Integer INTEGER_TWO_HUNDRED_ONE = 201;
    public static final Integer INTEGER_THREE_HUNDRED = 300;
    public static final Integer INTEGER_ONE_THOUSAND_HUNDRED = 1000;

    public static final Integer INTEGER_THREE_THOUSAND_HUNDRED = 3000;

    public static final Integer INTEGER_FIVE_THOUSAND_HUNDRED = 5000;
    public static final Integer INTEGER_TEN_THOUSAND = 10000;
    public static final Integer INTEGER_ONE_THOUSAND_ONE_HUNDRED = 1100;
    public static final Integer INTEGER_ONE_THOUSAND_ONE_HUNDRED_AND_FIFTY = 1350;
    public static final Integer INTEGER_ONE_MILLION = 1000000;
    public static final Long LONG_ZERO = 0L;

    public static final Long LONG_TWO = 2L;
    public static final Long LONG_THREE = 3L;
    public static final Long LONG_ONE = 1L;
    public static final Long LONG_TEN = 10L;
    public static final Long LONG_TWENTY_FIVE = 25L;
    public static final Long LONG_ONE_THOUSAND_HUNDRED = 1000L;
    public static final Long NEGATIVE_LONG_ONE = -1L;
    public static final String NEGATIVE_STRING_ONE = "-1";
    public static final boolean BOOLTRUE = true;
    public static final boolean BOOLFALES = false;
    public static final String YES = "YES";
    public static final String NO = "NO";

    public static final Integer PREFIX = 0000;

    public static final String ZERO_TWO_FOUR = "0.024";
    public static final String DATE_TO_STRING_DETAIAL_PATTERN = "yyyy-MM-dd HH:mm";
    public static final String ACCESS_TOKEN = "access_token";
    public static final String ERROR = "Error";
    public static final String DATE_TO_STRING_SHORT_PATTERN = "yyMMdd";
    public static final String YDM_FORMAT = "yyyyMMdd";
    public static final String DATE_TO_STRING_YM_PATTERN = "yyyy-MM";
    public static final String YYYY_FORMAT = "yyyy";
    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public static final String DATE_TO_STRING_YMDHMS_PATTERN_TEXT = "yyyy年MM月dd日 HH:mm:ss";

    public static final BigDecimal volume = new BigDecimal(1000000000);
    public static final String LIMITED_IDEMPOTENT_MESSAGE_FOR_JOB_IS_RUNNING = "服务正在运行";
    public static final String OTHER = "其他";
    public static final BigDecimal ONE_HUNDRED = new BigDecimal(100);
    public static final BigDecimal BIG_DECIMAL_ONE_THOUSAND_HUNDRED = new BigDecimal(1000);
    public static final BigDecimal ONE_CUBIC_METER = new BigDecimal("1000000000");
    public static final  String PERCENT = "%";
    public static final String PACKAGE = "件";
    public static final String WEIGHT = "吨";
    public static final String YUAN = "元";
    public static final String UNITYUAN = "(元)";
    public static final String GOODAMOUNT = "货量";
    public static final String YUAN_BRACKETS = "(元)";

    public static final String PROVINCE = "省";
    public static final String CITY = "市";
    public static final String AREA = "区";

    public static final String ADD_CONTRACT_TERMINATE = "新增合同时自动终止";
    public static final String MODIFY_CONTRACT_TERMINATE = "修改合同时自动终止";
    public static final String EXPIRE_CONTRACT_TERMINATE = "合同到期自动终止";
    public static final String EXPIRE_CONTRACT_EXECUTING = "合同到期自动执行";
    public static final String TIMING_TASK = "系统";

    public static final String ADMIN = "admin";
    public static final String SINOPEC_OPERATOR = "物联网";
    public static final String EMPTY_STRING = "";
    public static final String SHORT_LINE = "-";

    public static final String JANUARY = "01";
    public static final String FEBRUARY = "02";
    public static final String MARCH = "03";
    public static final String APRIL = "04";
    public static final String MAY = "05";
    public static final String JUNE = "06";
    public static final String JULY = "07";
    public static final String AUGUST = "08";
    public static final String SEPTEMBER = "09";
    public static final String OCTOBER = "10";
    public static final String NOVEMBER = "11";
    public static final String DECEMBER = "12";

    public static final char CHAR_A = 'A';
    public static final String POINT = ".";
    public static final String COLON_SEPARATOR = ":";
    public static final String STAR="*";
    public static final String VEHICLE_TYPE_LABEL = "重型半挂牵引车";
    public static final String ONE_DAY_TOUR = "一日游";
    public static final String WEBSITE="官网";

    //托盘同步需求单到物流默认手写或联系人
    public static final String YANG_HUI_NAME = "杨辉";
    public static final String YANG_HUI_PHONE = "15102192928";

    //提卸货备注前缀
    public static final String LOAD_REMARK_PREFIX = "【提货备注】";
    public static final String UNLOAD_REMARK_PREFIX = "【卸货备注】";

    public static final String LEYI_POINTS_FOR_LOGISTICS = "乐积分兑换物流";

    public static final String TMS_UPDATE_PASSWORD_PREPARE_TOKEN_PREFIX = "TMS_UPDATE_PASSWORD_PREPARE_TOKEN_PREFIX_";
    public static final String TMS_UPDATE_PHONE_PREPARE_TOKEN_PREFIX = "TMS_UPDATE_PHONE_PREPARE_TOKEN_PREFIX_";
    public static final String LOGISTICS_TMS_WEB_LOGIN_ERROR_COUNT = "logistics_tms_web_login_error_count_";

    //云仓回收类型入库运单自动签收分布式锁的key
    public static final String CARRIER_ORDER_CORRECT_TMS_KEY = "CARRIER_ORDER_CORRECT_TMS_KEY_";

    // 车主运价分布式锁的key
    public static final String CARRIER_FREIGHT_KEY = "CARRIER_FREIGHT_KEY_";

    //云盘运单详情编辑费用分布式锁key
    public static final String EDIT_CARRIER_ORDER_PRICE_KEY = "EDIT_CARRIER_ORDER_PRICE_KEY_";
    //回收单更新状态分布式锁前缀
    public static final String UPDATE_RECYCLE_ORDER_PREFIX = "UPDATE_RECYCLE_ORDER_";

    public static final String CARRIER_CREATE_SETTLE_STATEMENT_LOCK_KEY = "TMS_CARRIER_CREATE_SETTLE_STATEMENT_LOCK_KEY_";
    public static final String CARRIER_TRADITION_CREATE_SETTLE_STATEMENT_LOCK_KEY = "TMS_CARRIER_TRADITION_CREATE_SETTLE_STATEMENT_LOCK_KEY_";

    public static final String YMDHMS_FORMAT = "yyyyMMddHHmmss";

    public static final String BLANK_TEXT = "";

    public static final String NULL_TEXT = "null";

    public static final String RESERVE_BALANCE_LOCK_KEY = "RESERVE_BALANCE_LOCK_KEY:%s";

    public static final String REPORT_WORK_EXCEPTION_KEY = "REPORT_WORK_EXCEPTION_KEY:%s";

    public static final String RECEIPT_TICKETS_AUDIT_KEY = "RECEIPT_TICKETS_AUDIT_KEY:%s";

    public static final String ERRNO = "errno";

    public static final String VERIFY_CODE_USER = "验证码已使用";

    public static final String SYSTEM_ADMIN = "systemAdmin";

    public static final String SYSTEM = "system";

    public static final String LBS_CODE = "LBSCode";

    public static final String FIXED_FLAG = "fixedFlag";

    public static final String CODE = "code";

    public static final String TOKEN = "token";

    public static final String WORK_ORDER_DEFAULT_PROCESSOR = "工作人员";

    public static final String CARRIER_ORDER_TICKETS_AUTO_AUDIT = "回单自动审核";

    public static final String SYN_ADDITIONAL_FORM_LE_YI = "SYN_ADDITIONAL_FORM_LE_YI_";

    //实名认证key
    public static final String REAL_NAME_AUTHENTICATION_KEY = "REAL_NAME_AUTHENTICATION_KEY_";

    /** 物流到达提货地、提货、到达卸货地、卸货，分布式锁 key */
    public static final String CARRIER_ORDER_REACH_UNLOAD_ADDRESS_LOCK = "CARRIER_ORDER_REACH_UNLOAD_ADDRESS_LOCK";
    public static final String CARRIER_ORDER_REACH_LOAD_ADDRESS_LOCK = "CARRIER_ORDER_REACH_LOAD_ADDRESS_LOCK";
    public static final String CARRIER_ORDER_LOAD_LOCK = "CARRIER_ORDER_LOAD_LOCK";
    public static final String CARRIER_ORDER_UNLOAD_LOCK = "CARRIER_ORDER_UNLOAD_LOCK";

    public static final String TMS_ADD_STAFF_LOCK = "TMS_ADD_STAFF_LOCK_";
    public static final String TMS_ADD_VEHICLE_LOCK = "TMS_ADD_VEHICLE_LOCK";
    public static final String TMS_APPLY_INVOICING_LOCK = "TMS_APPLY_INVOICING_LOCK";
    public static final String TMS_ADD_MODIFY_DRIVER_LOCK = "TMS_MODIFY_DRIVER_LOCK_";


    public static final String DISPATCH_CAR_LOCK = "DISPATCH_CAR_LOCK_";

    public static final String TRAY_NAME = "共享托盘";
    public static final String TRAY_NAME1 = "乐橘托盘";


    public static final int INVOICING_MAX_SIZE=50;

    public static final int CODE_NOT_EXIST =-3000;

    public static final Integer EIGHTEEN = 18;

    public static final Integer SEVENTEEN = 17;

    // 链接参数拼接
    public static final BiFunction<String, Map<String, Object>, String> linkParamsJoinFunction = (l, m) -> {
        StringBuilder builder = new StringBuilder(l);
        if (!CollectionUtils.isEmpty(m)) {
            builder.append("?");
            builder.append(Joiner.on("&").useForNull("").withKeyValueSeparator("=").join(m));
        }
        return builder.toString();
    };
}


