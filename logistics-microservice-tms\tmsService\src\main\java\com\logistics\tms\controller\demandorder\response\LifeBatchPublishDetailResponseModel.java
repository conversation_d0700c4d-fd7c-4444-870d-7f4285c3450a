package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class LifeBatchPublishDetailResponseModel {

    @ApiModelProperty("货主公司名称")
    private String companyEntrustName;
    @ApiModelProperty("需求单ID")
    private Long demandId;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;
    @ApiModelProperty("委托数量")
    private BigDecimal goodsAmount;
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("发货地址")
    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    @ApiModelProperty("收货地址")
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;

}
