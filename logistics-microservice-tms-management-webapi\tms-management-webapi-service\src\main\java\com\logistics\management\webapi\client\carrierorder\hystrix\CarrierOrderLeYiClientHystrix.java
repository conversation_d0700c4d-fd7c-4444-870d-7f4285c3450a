package com.logistics.management.webapi.client.carrierorder.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.carrierorder.CarrierOrderLeYiClient;
import com.logistics.management.webapi.client.carrierorder.request.*;
import com.logistics.management.webapi.client.carrierorder.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CarrierOrderLeYiClientHystrix implements CarrierOrderLeYiClient {
    @Override
    public Result<PageInfo<SearchCarrierOrderListForLeYiResponseModel>> searchCarrierOrderListForLeYi(SearchCarrierOrderListForLeYiRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchCarrierOrderListForLeYiResponseModel>> exportCarrierOrderListForLeYi(SearchCarrierOrderListForLeYiRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<WaitAuditVehicleInfoResponseModel> getWaitAuditVehicleCountInfoForLeYi(WaitAuditVehicleInfoRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierOrderDetailForLeYiResponseModel> carrierOrderDetailForLeYi(CarrierOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<UpdateUnloadAddressDetailResponseModel>> updateCarrierOrderUnloadAddressDetail(CarrierOrderUpUnloadAddrRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> updateCarrierOrderUnloadAddressConfirm(UpdateCarrierOrderUnloadAddressConfirmRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierOrderCorrectDetailResponseModel> carrierOrderCorrectDetail(CarrierOrderCorrectDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<CarrierOrderOrdersResponseModel>> getCarrierOrderOrders(CarrierOrderIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> carrierOrderCorrectConfirm(CarrierOrderCorrectConfirmRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierOrderEmptyDetailResponseModel> carrierOrderEmptyDetail(CarrierOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> confirmEmpty(CarrierOrderEmptyRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CopyCarrierOrderResponseModel> copyCarrierOrder(CarrierOrderIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<LogisticsCostStatisticsResponseModel>> logisticsCostStatistics(LogisticsCostStatisticsRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<LogisticsLoadValidityStatisticsResponseModel>> logisticsLoadValidityStatistics(LogisticsLoadValidityStatisticsRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<WaitCorrectStatisticsResponseModel> waitCorrectStatistics() {
        return Result.timeout();
    }

    @Override
    public Result<List<CarrierOrderListBeforeSignUpForLeyiResponseModel>> carrierOrderListBeforeSignUpForLeyi(CarrierOrderListBeforeSignUpRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> carrierOrderSignUpForLeyi(CarrierOrderConfirmSignUpForLeyiRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<EditCarrierOrderCostDetailResponseModel> editCarrierOrderCostDetail(EditCarrierOrderCostDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> editCarrierOrderCost(EditCarrierOrderCostRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<GetValidCarrierOrderResponseModel>> getValidCarrierOrder(GetValidCarrierOrderRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetLeYiQrCodeResponseModel> getLeYiQrCode(GetLeYiQrCodeRequestModel requestModel) {
        return Result.timeout();
    }
}
