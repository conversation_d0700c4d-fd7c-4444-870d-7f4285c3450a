package com.logistics.tms.api.feign.gpsfee.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/10/9 9:01
 */
@Data
public class GpsFeeDetailResponseModel {
    private Long gpsFeeId;
    @ApiModelProperty("结算状态：0 待结算，1 部分结算，2 结算完成")
    private Integer status;
    @ApiModelProperty("车牌号")
    private Long vehicleId;
    private String vehicleNo;
    @ApiModelProperty("司机")
    private Long staffId;
    private String driverName;
    private String driverPhone;
    @ApiModelProperty("服务商")
    private String gpsServiceProvider;
    @ApiModelProperty("终端型号")
    private String terminalType;
    @ApiModelProperty("服务费")
    private BigDecimal serviceFee;
    @ApiModelProperty("合作周期（月）")
    private Integer cooperationPeriod;
    @ApiModelProperty("SIM卡号")
    private String simNumber;
    @ApiModelProperty("安装日期")
    private Date installTime;
    @ApiModelProperty("起始日期")
    private Date startDate;
    @ApiModelProperty("截止时间")
    private Date endDate;
    @ApiModelProperty("终止时间")
    private Date finishDate;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty(value = "是否生成结算数据，1生成，0未生成")
    private Integer ifSettlement = 0;
}
