package com.logistics.tms.api.feign.freight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 运价地址规则
 * @Author: sj
 * @Date: 2019/12/24 13:13
 */
@Data
public class AddOrModifyFreightAddressRuleRequestModel {
    @ApiModelProperty("运价地址ID")
    private Long freightAddressId;
    @ApiModelProperty("运价Id")
    private Long freightId;
    @ApiModelProperty("角色: 1 货主 2 车主")
    private Integer roleType;
    @ApiModelProperty("计价类型: 计价类型 1 基价 2 一日游")
    private Integer calcType;
    @ApiModelProperty("仓库ID")
    private Long warehouseId;
    @ApiModelProperty("仓库名称")
    private String warehouseName;
    @ApiModelProperty("发货省ID")
    private Long fromProvinceId;
    @ApiModelProperty("发货省名称")
    private String fromProvinceName;
    @ApiModelProperty("发货市ID")
    private Long fromCityId;
    @ApiModelProperty("发货市名称")
    private String fromCityName;
    @ApiModelProperty("发货区ID")
    private Long fromAreaId;
    @ApiModelProperty("发货区名称")
    private String fromAreaName;
    @ApiModelProperty("卸货省ID")
    private Long toProvinceId;
    @ApiModelProperty("卸货省名称")
    private String toProvinceName;
    @ApiModelProperty("卸货市ID")
    private Long toCityId;
    @ApiModelProperty("卸货市名称")
    private String toCityName;
    @ApiModelProperty("卸货区ID")
    private Long toAreaId;
    @ApiModelProperty("卸货区名称")
    private String toAreaName;
    @ApiModelProperty("运价规则阶梯")
    private List<FreightAddressRuleModel> freightAddressRuleList;
    @ApiModelProperty("运价多装多卸(车主)")
    private List<FreightAddressRuleMarkupModel> freightAddressRuleMarkupList;
}
