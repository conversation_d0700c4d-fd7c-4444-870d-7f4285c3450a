package com.logistics.appapi.controller.carrierorder.response;

import com.logistics.appapi.controller.carrierorder.request.CarrierOrderDetailCodeDto;
import com.logistics.appapi.controller.carrierorder.request.LoadGoodsForYeloLifeRequestCodeDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CarrierOrderDetailGoodsInfoDto {

    @ApiModelProperty("货物id")
    private String goodsId= "";
    @ApiModelProperty("品名")
    private String goodsName = "";
    @ApiModelProperty("件数/吨位")
    private String amount = "";
    @ApiModelProperty("规格")
    private String goodsSize="";
    @ApiModelProperty("体积（为空不显示）")
    private String capacity = "";

    @ApiModelProperty(value = "货物的编码集合 v2.44", required = true)
    private List<CarrierOrderDetailCodeDto> codeDtoList;

}
