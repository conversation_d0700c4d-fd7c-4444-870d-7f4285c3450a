package com.logistics.tms.api.feign.driverpayee.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DriverPayeeListRequestModel extends AbstractPageForm<DriverPayeeListRequestModel> {

    @ApiModelProperty("审核状态 0 待审核 1 已审核 2 已驳回")
    private Integer auditStatus ;
    @ApiModelProperty("收款人姓名，联系方式")
    private String driverPayee ;
    @ApiModelProperty("银行卡号")
    private String bankCardNo;
    @ApiModelProperty("操作人")
    private String lastModifiedBy ;
    @ApiModelProperty("操作时间从")
    private String lastModifiedTimeFrom;
    @ApiModelProperty("操作时间到")
    private String lastModifiedTimeTo;

}
