package com.logistics.management.webapi.controller.settlestatement.tradition.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.settlestatement.tradition.response.TraditionCarrierAdjustCostResponseModel;
import com.logistics.management.webapi.controller.settlestatement.tradition.response.TraditionCarrierAdjustCostResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @author: wjf
 * @date: 2022/11/17 18:14
 */
public class TraditionCarrierQueryAdjustCostMapping extends MapperMapping<TraditionCarrierAdjustCostResponseModel, TraditionCarrierAdjustCostResponseDto> {
    @Override
    public void configure() {
        TraditionCarrierAdjustCostResponseModel source = getSource();
        TraditionCarrierAdjustCostResponseDto destination = getDestination();

        //车主费用
        BigDecimal carrierFreight = source.getCarrierFreight().setScale(2, RoundingMode.HALF_UP);
        destination.setCarrierFreight(ConverterUtils.toString(carrierFreight));

        //费额合计
        BigDecimal carrierFreightTotal = carrierFreight.add(carrierFreight.multiply(source.getFreightTaxPoint()).divide(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_NET_ZERO, 2, RoundingMode.HALF_UP)).setScale(2, BigDecimal.ROUND_HALF_UP);
        destination.setCarrierFreightTotal(ConverterUtils.toString(carrierFreightTotal));

        //申请费用总额
        BigDecimal applyFeeTotal = carrierFreightTotal.setScale(2, RoundingMode.HALF_UP);
        destination.setApplyFeeTotal(ConverterUtils.toString(applyFeeTotal));

        //对账费用
        destination.setReconciliationFee(ConverterUtils.toString(applyFeeTotal.add(source.getAdjustCost()).setScale(2, RoundingMode.HALF_UP)));

        //调整费用
        if (source.getAdjustCost().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
            destination.setAdjustCostSymbol(CommonConstant.ONE);
        } else if (source.getAdjustCost().compareTo(BigDecimal.ZERO) < CommonConstant.INTEGER_ZERO) {
            destination.setAdjustCostSymbol(CommonConstant.TWO);
            destination.setAdjustCost(ConverterUtils.toString(source.getAdjustCost().negate()));
        } else {
            destination.setAdjustCostSymbol(CommonConstant.BLANK_TEXT);
        }
    }
}
