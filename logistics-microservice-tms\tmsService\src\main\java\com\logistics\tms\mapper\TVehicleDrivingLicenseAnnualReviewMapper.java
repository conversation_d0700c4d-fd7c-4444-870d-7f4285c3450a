package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.controller.vehicleassetmanagement.response.DrivingLicenseAnnualReviewListResponseModel;
import com.logistics.tms.entity.TVehicleDrivingLicenseAnnualReview;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface TVehicleDrivingLicenseAnnualReviewMapper extends BaseMapper<TVehicleDrivingLicenseAnnualReview> {

    List<DrivingLicenseAnnualReviewListResponseModel> getDrivingLicenseAnnualReviewByVehicleIds(@Param("vehicleIds") String vehicleIds);

    int batchInsert(@Param("list") List<TVehicleDrivingLicenseAnnualReview> list);

    Map<String,String> getDueDrivingLicenseCount(@Param("companyCarrierId") Long companyCarrierId,@Param("remindDays") Integer remindDays);

    int batchUpdate(@Param("list") List<TVehicleDrivingLicenseAnnualReview> list);

    int countDrivingLicenseByDate(@Param("drivingLicenseId") Long drivingLicenseId, @Param("checkValidDate") Date checkValidDate);
}