<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TWorkOrderMapper" >
  <sql id="Base_Column_List_Decrypt" >
    id, status, work_order_priority, work_order_code, work_order_type, demand_order_id,
    demand_order_code, carrier_order_id, carrier_order_code, anomaly_type_one, anomaly_type_two,
    contact_name, AES_DECRYPT(UNHEX(contact_telephone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as contact_telephone,
    check_contact, is_arrive_scene, arrive_scene_picture,
    address_head, address_detail, report_source, report_user_name, report_time, solve_user_name,
    solve_time, company_carrier_type, company_carrier_id, company_carrier_name, carrier_contact_id,
    carrier_contact_name, AES_DECRYPT(UNHEX(carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrier_contact_phone,
    driver_id, driver_name,
    AES_DECRYPT(UNHEX(driver_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as driver_mobile,
    remark, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>

  <select id="selectByPrimaryKeyDecrypt" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List_Decrypt"/>
    from t_work_order
    where valid = 1
    and id = #{id,jdbcType=BIGINT}
  </select>

  <insert id="insertSelectiveEncrypt" parameterType="com.logistics.tms.entity.TWorkOrder" keyProperty="id" useGeneratedKeys="true">
    insert into t_work_order
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="workOrderPriority != null" >
        work_order_priority,
      </if>
      <if test="workOrderCode != null" >
        work_order_code,
      </if>
      <if test="workOrderType != null" >
        work_order_type,
      </if>
      <if test="demandOrderId != null" >
        demand_order_id,
      </if>
      <if test="demandOrderCode != null" >
        demand_order_code,
      </if>
      <if test="carrierOrderId != null" >
        carrier_order_id,
      </if>
      <if test="carrierOrderCode != null" >
        carrier_order_code,
      </if>
      <if test="anomalyTypeOne != null" >
        anomaly_type_one,
      </if>
      <if test="anomalyTypeTwo != null" >
        anomaly_type_two,
      </if>
      <if test="contactName != null" >
        contact_name,
      </if>
      <if test="contactTelephone != null" >
        contact_telephone,
      </if>
      <if test="checkContact != null" >
        check_contact,
      </if>
      <if test="isArriveScene != null" >
        is_arrive_scene,
      </if>
      <if test="arriveScenePicture != null" >
        arrive_scene_picture,
      </if>
      <if test="addressHead != null" >
        address_head,
      </if>
      <if test="addressDetail != null" >
        address_detail,
      </if>
      <if test="reportSource != null" >
        report_source,
      </if>
      <if test="reportUserName != null" >
        report_user_name,
      </if>
      <if test="reportTime != null" >
        report_time,
      </if>
      <if test="solveUserName != null" >
        solve_user_name,
      </if>
      <if test="solveTime != null" >
        solve_time,
      </if>
      <if test="companyCarrierType != null" >
        company_carrier_type,
      </if>
      <if test="companyCarrierId != null" >
        company_carrier_id,
      </if>
      <if test="companyCarrierName != null" >
        company_carrier_name,
      </if>
      <if test="carrierContactId != null" >
        carrier_contact_id,
      </if>
      <if test="carrierContactName != null" >
        carrier_contact_name,
      </if>
      <if test="carrierContactPhone != null" >
        carrier_contact_phone,
      </if>
      <if test="driverId != null" >
        driver_id,
      </if>
      <if test="driverName != null" >
        driver_name,
      </if>
      <if test="driverMobile != null" >
        driver_mobile,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="workOrderPriority != null" >
        #{workOrderPriority,jdbcType=INTEGER},
      </if>
      <if test="workOrderCode != null" >
        #{workOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="workOrderType != null" >
        #{workOrderType,jdbcType=INTEGER},
      </if>
      <if test="demandOrderId != null" >
        #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderCode != null" >
        #{demandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="carrierOrderId != null" >
        #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null" >
        #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="anomalyTypeOne != null" >
        #{anomalyTypeOne,jdbcType=INTEGER},
      </if>
      <if test="anomalyTypeTwo != null" >
        #{anomalyTypeTwo,jdbcType=INTEGER},
      </if>
      <if test="contactName != null" >
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactTelephone != null" >
        HEX(AES_ENCRYPT(#{contactTelephone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="checkContact != null" >
        #{checkContact,jdbcType=INTEGER},
      </if>
      <if test="isArriveScene != null" >
        #{isArriveScene,jdbcType=INTEGER},
      </if>
      <if test="arriveScenePicture != null" >
        #{arriveScenePicture,jdbcType=VARCHAR},
      </if>
      <if test="addressHead != null" >
        #{addressHead,jdbcType=VARCHAR},
      </if>
      <if test="addressDetail != null" >
        #{addressDetail,jdbcType=VARCHAR},
      </if>
      <if test="reportSource != null" >
        #{reportSource,jdbcType=INTEGER},
      </if>
      <if test="reportUserName != null" >
        #{reportUserName,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null" >
        #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="solveUserName != null" >
        #{solveUserName,jdbcType=VARCHAR},
      </if>
      <if test="solveTime != null" >
        #{solveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyCarrierType != null" >
        #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null" >
        #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null" >
        #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactId != null" >
        #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactName != null" >
        #{carrierContactName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactPhone != null" >
        HEX(AES_ENCRYPT(#{carrierContactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="driverId != null" >
        #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null" >
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null" >
        HEX(AES_ENCRYPT(#{driverMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelectiveEncrypt" parameterType="com.logistics.tms.entity.TWorkOrder" >
    update t_work_order
    <set >
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="workOrderPriority != null" >
        work_order_priority = #{workOrderPriority,jdbcType=INTEGER},
      </if>
      <if test="workOrderCode != null" >
        work_order_code = #{workOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="workOrderType != null" >
        work_order_type = #{workOrderType,jdbcType=INTEGER},
      </if>
      <if test="demandOrderId != null" >
        demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderCode != null" >
        demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="carrierOrderId != null" >
        carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null" >
        carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="anomalyTypeOne != null" >
        anomaly_type_one = #{anomalyTypeOne,jdbcType=INTEGER},
      </if>
      <if test="anomalyTypeTwo != null" >
        anomaly_type_two = #{anomalyTypeTwo,jdbcType=INTEGER},
      </if>
      <if test="contactName != null" >
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactTelephone != null" >
        contact_telephone = HEX(AES_ENCRYPT(#{contactTelephone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="checkContact != null" >
        check_contact = #{checkContact,jdbcType=INTEGER},
      </if>
      <if test="isArriveScene != null" >
        is_arrive_scene = #{isArriveScene,jdbcType=INTEGER},
      </if>
      <if test="arriveScenePicture != null" >
        arrive_scene_picture = #{arriveScenePicture,jdbcType=VARCHAR},
      </if>
      <if test="addressHead != null" >
        address_head = #{addressHead,jdbcType=VARCHAR},
      </if>
      <if test="addressDetail != null" >
        address_detail = #{addressDetail,jdbcType=VARCHAR},
      </if>
      <if test="reportSource != null" >
        report_source = #{reportSource,jdbcType=INTEGER},
      </if>
      <if test="reportUserName != null" >
        report_user_name = #{reportUserName,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null" >
        report_time = #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="solveUserName != null" >
        solve_user_name = #{solveUserName,jdbcType=VARCHAR},
      </if>
      <if test="solveTime != null" >
        solve_time = #{solveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyCarrierType != null" >
        company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null" >
        company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null" >
        company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactId != null" >
        carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactName != null" >
        carrier_contact_name = #{carrierContactName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactPhone != null" >
        carrier_contact_phone = HEX(AES_ENCRYPT(#{carrierContactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="driverId != null" >
        driver_id = #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null" >
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null" >
        driver_mobile = HEX(AES_ENCRYPT(#{driverMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="checkExceptionExist" resultType="com.logistics.tms.biz.workordercenter.model.WorkOrderExceptionModel">
    select
    id as workOrderId,
    demand_order_id as demandOrderId,
    carrier_order_id as carrierOrderId
    from t_work_order
    where valid = 1
    <if test="demandOrderIds != null and demandOrderIds != ''">
      and demand_order_id in (${demandOrderIds})
    </if>
    <if test="carrierOrderIds != null and carrierOrderIds != ''">
      and carrier_order_id in (${carrierOrderIds})
    </if>
    and status in (0, 10)
  </select>

  <select id="searchWorkOrderList"
          resultType="com.logistics.tms.controller.workordercenter.response.WorkOrderListResponseModel">
    select
    id                                                                                           as workOrderId,
    status                                                                                       as status,
    work_order_type                                                                              as workOrderType,
    demand_order_code                                                                            as demandOrderCode,
    carrier_order_code                                                                           as carrierOrderCode,
    anomaly_type_one                                                                             as anomalyTypeOne,
    anomaly_type_two                                                                             as anomalyTypeTwo,
    report_user_name                                                                             as reportUserName,
    report_time                                                                                  as reportTime,
    solve_time                                                                                   as solveTime,
    company_carrier_type                                                                         as companyType,
    company_carrier_name                                                                         as companyName,
    carrier_contact_name                                                                         as contactName,
    AES_DECRYPT(UNHEX(carrier_contact_phone),
                '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as contactPhone,
    report_source                                                                                as reportSource
    from t_work_order
    where valid = 1
    <if test="companyCarrierId != null">
      and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
    </if>
    <if test="status != null">
      and status = #{status,jdbcType=INTEGER}
    </if>
    <if test="reportSource != null">
      and report_source = #{reportSource,jdbcType=INTEGER}
    </if>
    <if test="demandOrderCode != null and demandOrderCode != ''">
      and instr(demand_order_code, #{demandOrderCode,jdbcType=VARCHAR})
    </if>
    <if test="carrierOrderCode != null and carrierOrderCode != ''">
      and instr(carrier_order_code, #{carrierOrderCode,jdbcType=VARCHAR})
    </if>
    <if test="companyCarrierName != null and companyCarrierName != ''">
      and ((company_carrier_type = 1 and instr(company_carrier_name, #{companyCarrierName,jdbcType=VARCHAR}))
        or (company_carrier_type = 2 and (instr(carrier_contact_name, #{companyCarrierName,jdbcType=VARCHAR}) or
                                          instr(AES_DECRYPT(UNHEX(carrier_contact_phone),
                                                            '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'),
                                                #{companyCarrierName,jdbcType=VARCHAR}))))
    </if>
    <if test="reportUserName != null and reportUserName != ''">
      and instr(report_user_name, #{reportUserName,jdbcType=VARCHAR})
    </if>
    <if test="reportTimeStart != null and reportTimeStart != ''">
      and report_time &gt;= DATE_FORMAT(#{reportTimeStart,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%S')
    </if>
    <if test="reportTimeEnd != null and reportTimeEnd != ''">
      and report_time &lt;= DATE_FORMAT(#{reportTimeEnd,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
    </if>
    <if test="solveTimeStart != null and solveTimeStart != ''">
      and solve_time &gt;= DATE_FORMAT(#{solveTimeStart,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%S')
    </if>
    <if test="solveTimeEnd != null and solveTimeEnd != ''">
      and solve_time &lt;= DATE_FORMAT(#{solveTimeEnd,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
    </if>
    order by created_time desc
  </select>

  <select id="searchWorkOrderDetailById" resultType="com.logistics.tms.controller.workordercenter.response.WorkOrderDetailResponseModel">
    select
    id                                                                                                                     as workOrderId,
    status                                                                                                                 as status,
    demand_order_id                                                                                                        as demandOrderId,
    carrier_order_id                                                                                                       as carrierOrderId,
    demand_order_code                                                                                                      as demandOrderCode,
    carrier_order_code                                                                                                     as carrierOrderCode,
    anomaly_type_one                                                                                                       as anomalyTypeOne,
    anomaly_type_two                                                                                                       as anomalyTypeTwo,
    report_user_name                                                                                                       as reportUserName,
    report_time                                                                                                            as reportTime,
    driver_id                                                                                                              as driverId,
    driver_name                                                                                                            as driverName,
    AES_DECRYPT(UNHEX(driver_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')     as driverPhone,
    is_arrive_scene                                                                                                        as isArriveScene,
    address_head                                                                                                           as addressHead,
    report_source                                                                                                          as reportSource,
    address_detail                                                                                                         as addressDetail,
    check_contact                                                                                                          as checkContact,
    contact_name                                                                                                           as contactName,
    AES_DECRYPT(UNHEX(contact_telephone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as contactTelephone,
    remark,
    arrive_scene_picture                                                                                                   as arriveScenePicture
    from t_work_order
    where valid = 1
      and id = #{workOrderId,jdbcType=BIGINT}
    <if test="companyCarrierId != null">
      and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
    </if>
  </select>

  <select id="selectPendingOrderIdByCarrierId" resultType="com.logistics.tms.biz.demandorder.model.WorkOrderPendingOrderModel">
    select
    demand_order_id  as demandOrderId,
    carrier_order_id as carrierOrderId
    from t_work_order
    where valid = 1
      and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
      and status in (0, 10)
  </select>

  <select id="selectAllPendingOrderIds" resultType="com.logistics.tms.biz.demandorder.model.WorkOrderPendingOrderModel">
    select
    demand_order_id  as demandOrderId,
    carrier_order_id as carrierOrderId
    from t_work_order
    where valid = 1
      and status in (0, 10)
  </select>

  <select id="selectAllDecryptByOrderIdAndOrderType" resultMap="BaseResultMap">
    select <include refid="Base_Column_List_Decrypt"/>
    from t_work_order
    where valid = 1
    and work_order_type = #{orderType}
    <if test="orderId != null">
      <choose>
        <when test="orderType == 10">
            and demand_order_id = #{orderId}
        </when>
        <otherwise>
            and carrier_order_id = #{orderId}
        </otherwise>
      </choose>
    </if>
  </select>

  <sql id="selectWorkOrderCommonSql">
    select
    <include refid="Base_Column_List_Decrypt"/>
    from t_work_order
    where valid = 1
  </sql>

  <select id="selectWorkOrderByCarrierIds" resultMap="BaseResultMap">
    <include refid="selectWorkOrderCommonSql"/>
    <choose>
      <when test="carrierOrderIdList != null and carrierOrderIdList.size() != 0">
        and carrier_order_id in
        <foreach collection="carrierOrderIdList" open="(" separator="," close=")" item="item">
          #{item,jdbcType=BIGINT}
        </foreach>
      </when>
      <otherwise>
        and false
      </otherwise>
    </choose>
  </select>

  <select id="selectWorkOrderByDemandIds" resultMap="BaseResultMap">
    <include refid="selectWorkOrderCommonSql"/>
    <choose>
      <when test="demandOrderIdList != null and demandOrderIdList.size() != 0">
        and demand_order_id in
        <foreach collection="demandOrderIdList" open="(" separator="," close=")" item="item">
          #{item,jdbcType=BIGINT}
        </foreach>
      </when>
      <otherwise>
        and false
      </otherwise>
    </choose>
  </select>

  <select id="selectPendingOrderCountByDriverId" resultType="java.lang.Integer">
    select count(*)
    from t_work_order
    where valid = 1
      and driver_id = #{driverId,jdbcType=BIGINT}
      and status in (0, 10)
  </select>

  <select id="selectPendingOrderIdByDriverIds" resultType="java.lang.Long">
    select carrier_order_id
    from t_work_order
    where valid = 1
    and status in (0, 10)
    <choose>
      <when test="driverIdList != null and driverIdList.size() != 0">
        and driver_id in
        <foreach collection="driverIdList" open="(" separator="," close=")" item="item">
          #{item,jdbcType=BIGINT}
        </foreach>
      </when>
      <otherwise>
        and false
      </otherwise>
    </choose>
  </select>

  <select id="searchWorkOrderListForApplet" resultType="com.logistics.tms.controller.workordercenter.response.WorkOrderListAppletResponseModel">
    select
    id                 as workOrderId,
    status             as status,
    work_order_type    as workOrderType,
    carrier_order_id   as carrierOrderId,
    carrier_order_code as carrierOrderCode
    from t_work_order
    where valid = 1
    <if test="driverId != null">
      and driver_id = #{driverId,jdbcType=BIGINT}
    </if>
    order by created_time desc
  </select>

  <select id="searchWorkOrderDetailForApplet"
          resultType="com.logistics.tms.controller.workordercenter.response.WorkOrderDetailAppletResponseModel">
    select
    id                                                                                           as workOrderId,
    status                                                                                       as status,
    work_order_type                                                                              as workOrderType,
    carrier_order_id                                                                             as carrierOrderId,
    carrier_order_code                                                                           as carrierOrderCode,
    report_time                                                                                  as reportTime,
    report_user_name                                                                             as reportUserName,
    address_head                                                                                 as addressHead,
    address_detail                                                                               as addressDetail,
    anomaly_type_one                                                                             as anomalyTypeOne,
    anomaly_type_two                                                                             as anomalyTypeTwo,
    contact_name                                                                                 as contactName,
    AES_DECRYPT(UNHEX(contact_telephone),
                '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as contactTelephone,
    remark                                                                                       as remark,
    is_arrive_scene                                                                              as isArriveScene,
    check_contact                                                                                as checkContact,
    report_source                                                                                as reportSource
    from t_work_order
    where valid = 1
      and id = #{workOrderId,jdbcType=BIGINT}
      and driver_id = #{driverId,jdbcType=BIGINT}
  </select>

  <select id="selectOneByWorkOrderCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List_Decrypt"/>
    from t_work_order
    where valid = 1
    and work_order_code = #{workOrderCode}
  </select>
</mapper>
