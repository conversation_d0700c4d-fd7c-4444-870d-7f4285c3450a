package com.logistics.tms.controller.invoicingmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2024/3/22 13:28
 */
@Data
public class GetSettleStatementListItemResponseModel {

    @ApiModelProperty("对账单表Id")
    private Long settleStatementItemId;

    @ApiModelProperty("委托费用/车主运费")
    private BigDecimal entrustFreight;

    @ApiModelProperty("临时费用")
    private BigDecimal otherFees;

}
