package com.logistics.tms.mapper;

import com.logistics.tms.biz.reserveapply.model.ReserveApplyBalanceDeductionModel;
import com.logistics.tms.biz.reserveapply.model.ReserveApplyListStatisticsModel;
import com.logistics.tms.controller.reserveapply.request.ReserveApplyListRequestModel;
import com.logistics.tms.controller.reserveapply.response.ReserveApplyDetailResponseModel;
import com.logistics.tms.controller.reserveapply.response.ReserveApplyListResponseModel;
import com.logistics.tms.controller.reserveapply.response.ReserveBalanceApplyListItemModel;
import com.logistics.tms.entity.TReserveApply;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
* Created by Mybatis Generator on 2022/12/19
*/
@Mapper
public interface TReserveApplyMapper extends BaseMapper<TReserveApply> {

    List<ReserveApplyListResponseModel> selectReserveApplyList(@Param("params") ReserveApplyListRequestModel params);

    ReserveApplyDetailResponseModel selectReserveApplyDetailById(@Param("id") Long id);

    TReserveApply selectOneById(@Param("id") Long id);

    TReserveApply selectOneByIdAndDriverId(@Param("id") Long id, @Param("driverId") Long driverId);

    int updateAudit(@Param("apply") TReserveApply apply, @Param("currentStatus") Integer currentStatus);

    List<TReserveApply> selectReserveApplyUsedInCostDeductions(@Param("driverId") Long driverId);

    int batchUpdateReserveApplyBalanceDeduction(@Param("updateModelList") List<ReserveApplyBalanceDeductionModel> updateModelList);

    List<ReserveBalanceApplyListItemModel> selectReserveBalanceApplyList(@Param("driverId") Long driverId, @Param("applyMonth") String applyMonth);

    int insertReserveApply(TReserveApply apply);

    ReserveApplyListStatisticsModel statisticsReserveApplyList(@Param("driverId") Long driverId, @Param("applyMonth") String applyMonth);

    int updateReserveApply(TReserveApply apply);

    List<TReserveApply> selectAllByReserveApplyCodeIn(@Param("reserveApplyCodeList") Collection<String> reserveApplyCodeList);
}