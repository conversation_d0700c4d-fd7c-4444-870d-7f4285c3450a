package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.controller.driversafemeeting.response.DriverSafeMeetingContentDetailResponseModel;
import com.logistics.tms.controller.driversafemeeting.request.DriverSafeMeetingKanBanRequestModel;
import com.logistics.tms.controller.driversafemeeting.response.DriverSafeMeetingKanBanResponseModel;
import com.logistics.tms.entity.TDriverSafeMeeting;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TDriverSafeMeetingMapper extends BaseMapper<TDriverSafeMeeting> {

    List<DriverSafeMeetingKanBanResponseModel> driverSafeMeetingKanBan(DriverSafeMeetingKanBanRequestModel driverSafeMeetingKanBanRequestModel);

    TDriverSafeMeeting getByPeriod(@Param("period")String period);

    DriverSafeMeetingContentDetailResponseModel driverSafeMeetingContentDetail(@Param("id")Long id);
}