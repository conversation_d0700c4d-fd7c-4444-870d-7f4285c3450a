package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/10/11 17:07
 */
public class ExportParkingFeeDeductingHistoryInfo {
    private ExportParkingFeeDeductingHistoryInfo(){

    }
    private static final Map<String,String> EXPORT_PARKING_FEE_DEDUCTING_HISTORY_MAP;
    static{
        EXPORT_PARKING_FEE_DEDUCTING_HISTORY_MAP = new LinkedHashMap<>();
        EXPORT_PARKING_FEE_DEDUCTING_HISTORY_MAP.put("扣减月份","deductingMonth");
        EXPORT_PARKING_FEE_DEDUCTING_HISTORY_MAP.put("总金额","totalFee");
        EXPORT_PARKING_FEE_DEDUCTING_HISTORY_MAP.put("未扣减费用合计","remainingDeductingFeeTotal");
        EXPORT_PARKING_FEE_DEDUCTING_HISTORY_MAP.put("扣减费用","deductingFee");
        EXPORT_PARKING_FEE_DEDUCTING_HISTORY_MAP.put("剩余未扣减","remainingDeductingFee");
        EXPORT_PARKING_FEE_DEDUCTING_HISTORY_MAP.put("操作人","lastModifiedBy");
        EXPORT_PARKING_FEE_DEDUCTING_HISTORY_MAP.put("操作时间","lastModifiedTime");
    }

    public static Map<String,String> getExportParkingFeeDeductingHistoryMap(){
        return EXPORT_PARKING_FEE_DEDUCTING_HISTORY_MAP;
    }
}
