package com.logistics.management.webapi.api.feign.forthirdparty.hystrix;

import com.logistics.management.webapi.api.feign.forthirdparty.ForThirdPartyApi;
import com.logistics.management.webapi.api.feign.forthirdparty.dto.request.WorkOrderProcessSyncRequestDto;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

@Component
public class ForThirdPartyApiHystrix implements ForThirdPartyApi {

    @Override
    public Result<Boolean> syncWorkOrderProcess(WorkOrderProcessSyncRequestDto requestDto) {
        return Result.timeout();
    }
}
