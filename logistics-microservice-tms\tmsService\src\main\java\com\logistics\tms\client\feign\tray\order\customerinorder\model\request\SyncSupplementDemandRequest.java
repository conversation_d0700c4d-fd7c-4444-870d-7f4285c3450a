package com.logistics.tms.client.feign.tray.order.customerinorder.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SyncSupplementDemandRequest {
    @ApiModelProperty(value = "运单code")
    private String carrierOrderCode;

    @ApiModelProperty(value = "需求单code")
    private String demandOrderCode;

    @ApiModelProperty(value = "补充需求单code")
    private String extDemandOrderCode;

    @ApiModelProperty(value = "后补标识：0.否 1.是")
    private Integer backupFlag;

    @ApiModelProperty(value = "实际发货地址名称")
    private String actualDeliveryAddressName;

    @ApiModelProperty(value = "发货省份名称")
    private String actualFromProvinceName;

    @ApiModelProperty(value = "发货市区名称")
    private String actualFromCityName;

    @ApiModelProperty(value = "发货区域名称")
    private String actualFromAreaName;

    @ApiModelProperty(value = "实际发货地址详情")
    private String actualDeliveryAddressDetail;

    @ApiModelProperty(value = "发货联系人名称")
    private String deliveryContactName;

    @ApiModelProperty(value = "发货联系人手机号码")
    private String deliveryContactMobile;

    @ApiModelProperty(value = "收货仓库id")
    private Long toWarehouseId;

    @ApiModelProperty(value = "需求数量")
    private Integer count;


    @ApiModelProperty(value = "期望提货时间")
    private Date expectPickGoodsTime;

    @ApiModelProperty(value = "期望到达时间")
    private Date expectArrivalGoodsTime;

    @ApiModelProperty(value = "周末可上门: 0.空 1.是 2.否")
    private Byte availableOnWeekends;

    @ApiModelProperty(value = "装卸方: 0.空 1.我司装卸 2.客户装卸")
    private Byte loadingUnloadingPart;

    @ApiModelProperty(value = "装卸费用")
    private BigDecimal loadingUnloadingCharge;

    @ApiModelProperty(value = "运输方式：1、自有车辆  2、第三方车辆")
    private Integer transportType;

    @ApiModelProperty("上游企业名称")
    private String upCustomerName;

    @ApiModelProperty("采购单号")
    private String purchaseCode;

    @ApiModelProperty("上游联系人姓名")
    private String upContactName;

    @ApiModelProperty("上游联系人手机号")
    private String upContactMobile;

    @ApiModelProperty("取货区域id")
    private Long fromAreaId;

    @ApiModelProperty("取货市区id")
    private Long fromCityId;

    @ApiModelProperty("取货省份id")
    private Long fromProvinceId;

    private String createdBy;
}
