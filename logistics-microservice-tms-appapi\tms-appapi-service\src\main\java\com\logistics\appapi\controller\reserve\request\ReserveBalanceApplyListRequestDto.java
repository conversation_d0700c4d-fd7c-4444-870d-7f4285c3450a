package com.logistics.appapi.controller.reserve.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Data
public class ReserveBalanceApplyListRequestDto extends AbstractPageForm<ReserveBalanceApplyListRequestDto> {

	@ApiModelProperty(value = "申请月份", required = true)
	@NotBlank(message = "请选择申请月份")
	private String applyMonth;
}
