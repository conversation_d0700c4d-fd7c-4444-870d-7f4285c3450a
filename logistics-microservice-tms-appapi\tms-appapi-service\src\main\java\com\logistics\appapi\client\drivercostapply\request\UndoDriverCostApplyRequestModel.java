package com.logistics.appapi.client.drivercostapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/8/3 13:54
 */
@Data
public class UndoDriverCostApplyRequestModel {

    @ApiModelProperty("司机费用申请表id")
    private Long driverCostApplyId;

    @ApiModelProperty("撤销说明")
    private String remark;

    @ApiModelProperty("请求来源 1 后台，2 前台 3小程序")
    private Integer source;
}
