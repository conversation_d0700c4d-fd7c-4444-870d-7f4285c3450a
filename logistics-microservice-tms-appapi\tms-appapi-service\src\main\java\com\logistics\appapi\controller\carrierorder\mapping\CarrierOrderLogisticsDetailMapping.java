package com.logistics.appapi.controller.carrierorder.mapping;

import com.logistics.appapi.base.enums.CarrierOrderEventsTypeEnum;
import com.logistics.appapi.base.enums.CarrierOrderTicketsTypeEnum;
import com.logistics.appapi.client.carrierorder.response.CarrierOrderBillResponseModel;
import com.logistics.appapi.client.carrierorder.response.CarrierOrderLogisticsDetailResponseModel;
import com.logistics.appapi.client.carrierorder.response.CarrierOrderLogisticsEventResponseModel;
import com.logistics.appapi.controller.carrierorder.response.CarrierOrderBillResponseDto;
import com.logistics.appapi.controller.carrierorder.response.CarrierOrderLogisticsDetailResponseDto;
import com.logistics.appapi.controller.carrierorder.response.CarrierOrderLogisticsEventResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2018/10/23 9:33
 */
public class CarrierOrderLogisticsDetailMapping extends MapperMapping<CarrierOrderLogisticsDetailResponseModel,CarrierOrderLogisticsDetailResponseDto> {
    @Override
    public void configure() {
        CarrierOrderLogisticsDetailResponseModel model = getSource();
        CarrierOrderLogisticsDetailResponseDto dto = getDestination();
        if (model != null){
            if (ListUtils.isNotEmpty(model.getEventList())){
                for (CarrierOrderLogisticsEventResponseModel responseModel:model.getEventList()) {
                    for (CarrierOrderLogisticsEventResponseDto responseDto:dto.getEventList()) {
                        if (ConverterUtils.toString(responseModel.getEventId()).equals(responseDto.getEventId())) {
                            if (responseModel.getEvent().equals(CarrierOrderEventsTypeEnum.PICK_UP.getKey())) {
                                responseDto.setTicketType(ConverterUtils.toString(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getKey()));
                            }else if (responseModel.getEvent().equals(CarrierOrderEventsTypeEnum.UNLOADING.getKey())) {
                                responseDto.setTicketType(ConverterUtils.toString(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey()));
                            }else if (responseModel.getEvent().equals(CarrierOrderEventsTypeEnum.ARRIVED_PICK_UP.getKey())) {
                                responseDto.setTicketType(ConverterUtils.toString(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_ARRIVE_PICK_UP.getKey()));
                            }else if (responseModel.getEvent().equals(CarrierOrderEventsTypeEnum.ARRIVED_UNLOAD.getKey())) {
                                responseDto.setTicketType(ConverterUtils.toString(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_ARRIVE_UNLOADING.getKey()));
                            }
                            if (StringUtils.isNotBlank(responseDto.getEventTime()) && responseDto.getEventTime().length() > 16){
                                responseDto.setEventTime(DateUtils.dateToString(responseModel.getEventTime(),"yyyy/MM/dd HH:mm"));
                                responseDto.setEventDate(responseDto.getEventTime().substring(5,10));
                                responseDto.setEventTime(responseDto.getEventTime().substring(11,16));
                            }
                        }
                    }
                }
            }
            //APP页面只展示出库单、签收单、到货凭证（到货凭证为达到提货地凭证和到达卸货地凭证）
            List<CarrierOrderBillResponseDto> dtoList = new ArrayList<>();
            if (ListUtils.isNotEmpty(model.getTicketList())){
                List<String> arrivalPaths = new ArrayList<>();//到库凭证
                for (CarrierOrderBillResponseModel responseModel:model.getTicketList()) {
                    if (responseModel.getTicketType().equals(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getKey())
                            || responseModel.getTicketType().equals(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey())
                            || responseModel.getTicketType().equals(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_LOAD_SCENE_PIC.getKey())
                            || responseModel.getTicketType().equals(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_SCENE_PIC.getKey())) {
                        //出库单、签收单、提货现场图片、卸货现场图片
                        CarrierOrderBillResponseDto carrierOrderBillResponseDto = new CarrierOrderBillResponseDto();
                        carrierOrderBillResponseDto.setTicketType(ConverterUtils.toString(responseModel.getTicketType()));
                        carrierOrderBillResponseDto.setTicketPath(responseModel.getTicketPath());
                        dtoList.add(carrierOrderBillResponseDto);
                    }else if (responseModel.getTicketType().equals(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_ARRIVE_PICK_UP.getKey())
                            || responseModel.getTicketType().equals(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_ARRIVE_UNLOADING.getKey())) {
                        //到达提货地凭证、到达卸货地凭证
                        arrivalPaths.addAll(responseModel.getTicketPath());
                    }
                }
                if (!arrivalPaths.isEmpty()) {
                    CarrierOrderBillResponseDto billResponseDto = new CarrierOrderBillResponseDto();
                    billResponseDto.setTicketType(ConverterUtils.toString(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_ARRIVE_PICK_UP.getKey()));
                    billResponseDto.setTicketPath(arrivalPaths);
                    dtoList.add(billResponseDto);
                }
                dto.setTicketList(dtoList);
            }
        }
    }
}
