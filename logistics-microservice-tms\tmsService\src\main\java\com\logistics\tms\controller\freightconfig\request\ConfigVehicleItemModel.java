package com.logistics.tms.controller.freightconfig.request;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ConfigVehicleItemModel {


    /**
     * 起始数量
     */
    private String countStart;


    /**
     * 结束数量
     */
    private String countEnd;


    /**
     * 单位 1块 2吨 3件(件块整数 吨3位小数)
     */
    private Integer unit;

    /**
     * 价格类型 1 单价(元/块，元/吨，元/件)，2 一口价(元)
     */
    private Integer priceType;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 排序
     */
    private Integer sort;


}
