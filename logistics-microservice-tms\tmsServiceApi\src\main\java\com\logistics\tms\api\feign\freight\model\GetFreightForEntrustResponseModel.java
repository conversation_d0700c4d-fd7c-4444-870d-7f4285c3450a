package com.logistics.tms.api.feign.freight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: sj
 * @Date: 2020/1/2 13:52
 */
@Data
public class GetFreightForEntrustResponseModel {
    @ApiModelProperty("运价主表ID")
    private Long freightId;
    @ApiModelProperty("价格类型 1 单价 2 一口价")
    private Integer freightType;
    @ApiModelProperty("费用")
    private BigDecimal freightFee;
}
