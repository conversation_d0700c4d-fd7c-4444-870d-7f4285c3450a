package com.logistics.tms.api.feign.traycost.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/4/20 18:36
 */
@Data
public class SearchCostRecordsListResponseModel {
    @ApiModelProperty("委托类型：1 发货，2 回收，3 采购，4 调拨")
    private Integer entrustType;
    @ApiModelProperty("单位：1 件，2 吨，3 件（方），4 块")
    private Integer goodsUnit;
    @ApiModelProperty("价格")
    private BigDecimal unitPrice;
    @ApiModelProperty("生效时间")
    private Date startTime;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;
}
