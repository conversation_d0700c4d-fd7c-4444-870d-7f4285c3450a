package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TDemandOrderObjection extends BaseEntity {
    /**
    * 需求单id
    */
    @ApiModelProperty("需求单id")
    private Long demandOrderId;

    /**
    * 客户名称
    */
    @ApiModelProperty("客户名称")
    private String customerName;

    /**
    * 异常类型：10 数量问题，20 重报问题，30 联系问题，40 地址问题，50 装车问题，60 等待问题，70 其他问题
    */
    @ApiModelProperty("异常类型：10 数量问题，20 重报问题，30 联系问题，40 地址问题，50 装车问题，60 等待问题，70 其他问题")
    private Integer objectionType;

    /**
    * 异常原因
    */
    @ApiModelProperty("异常原因")
    private String objectionReason;

    /**
    * 上报人
    */
    @ApiModelProperty("上报人")
    private String reportContactName;

    /**
    * 上报时间
    */
    @ApiModelProperty("上报时间")
    private Date reportTime;
}