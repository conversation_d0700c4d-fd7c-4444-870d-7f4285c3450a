package com.logistics.tms.controller.vehicleassetmanagement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AddOrModifyVehicleGradeEstimationRecordRequestModel {
    @ApiModelProperty("等级评定检查Id")
    private Long vehicleGradeEstimationRecordId;
    @ApiModelProperty("等级评定检查日期")
    private Date estimationDate;
    @ApiModelProperty("等级 1 一级 2 二级 3 三级")
    private Integer grade;
    @ApiModelProperty("备注信息")
    private String remark;
    @ApiModelProperty("证件图片列表")
    private List<CertificationPicturesRequestModel> fileList;
}
