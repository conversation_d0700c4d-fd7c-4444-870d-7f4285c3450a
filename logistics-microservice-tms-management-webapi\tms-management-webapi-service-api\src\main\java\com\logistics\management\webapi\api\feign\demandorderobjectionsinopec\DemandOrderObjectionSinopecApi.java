package com.logistics.management.webapi.api.feign.demandorderobjectionsinopec;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.demandorderobjectionsinopec.dto.*;
import com.logistics.management.webapi.api.feign.demandorderobjectionsinopec.hystrix.DemandOrderObjectionSinopecApiHystrix;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * @author: wjf
 * @date: 2022/5/30 13:13
 */
@Api(value = "API - DemandOrderObjectionSinopecApi-中石化需求单异常管理")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = DemandOrderObjectionSinopecApiHystrix.class)
public interface DemandOrderObjectionSinopecApi {

    @ApiOperation(value = "中石化需求单异常列表 v1.1.6")
    @PostMapping(value = "/api/demandOrderObjectionSinopec/searchSinopecObjection")
    Result<PageInfo<SearchDemandOrderObjectionSinopecResponseDto>> searchSinopecObjection(@RequestBody SearchDemandOrderObjectionSinopecRequestDto requestDto);

    @ApiOperation(value = "中石化需求单异常详情 v1.1.6")
    @PostMapping(value = "/api/demandOrderObjectionSinopec/getSinopecObjectionDetail")
    Result<GetSinopecObjectionDetailResponseDto> getSinopecObjectionDetail(@RequestBody @Valid GetSinopecObjectionDetailRequestDto requestDto);

    @ApiOperation(value = "中石化需求单异常审核 v1.1.6")
    @PostMapping(value = "/api/demandOrderObjectionSinopec/sinopecObjectionAudit")
    Result<Boolean> sinopecObjectionAudit(@RequestBody @Valid SinopecObjectionAuditRequestDto requestDto);

}
