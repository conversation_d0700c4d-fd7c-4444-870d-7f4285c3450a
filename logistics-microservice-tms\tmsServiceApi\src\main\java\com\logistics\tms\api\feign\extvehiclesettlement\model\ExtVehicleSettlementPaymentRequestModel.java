package com.logistics.tms.api.feign.extvehiclesettlement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/11/20 14:27
 */
@Data
public class ExtVehicleSettlementPaymentRequestModel {
    @ApiModelProperty("外部车辆结算ID")
    private Long extVehicleSettlementId;
    @ApiModelProperty("付款通道")
    private String paymentChannel;
    @ApiModelProperty("付款单号")
    private String paymentNo;
    @ApiModelProperty("应支付费用")
    private BigDecimal paymentFee;
    @ApiModelProperty("报销费用")
    private BigDecimal reimburseFee;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("附件")
    private List<String> attachmentList;
}
