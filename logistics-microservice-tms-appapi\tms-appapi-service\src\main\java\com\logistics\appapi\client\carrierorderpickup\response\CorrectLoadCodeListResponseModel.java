package com.logistics.appapi.client.carrierorderpickup.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CorrectLoadCodeListResponseModel {


    @ApiModelProperty(value = "运单提货编码id")
    private Long carrierOrderLoadCodeId;

    @ApiModelProperty(value = "托盘客户name")
    private String trayCustomerCompanyName;

    @ApiModelProperty(value = "托盘编码")
    private String productCode;

}
