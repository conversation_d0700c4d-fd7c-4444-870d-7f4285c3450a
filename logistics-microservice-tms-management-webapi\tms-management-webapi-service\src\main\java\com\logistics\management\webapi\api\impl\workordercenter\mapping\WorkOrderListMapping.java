package com.logistics.management.webapi.api.impl.workordercenter.mapping;

import com.logistics.management.webapi.api.feign.workordercenter.dto.response.WorkOrderListResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.enums.WorkOrderStatusEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.workordercenter.enums.WorkOrderAnomalyTypeOneEnum;
import com.logistics.tms.api.feign.workordercenter.enums.WorkOrderAnomalyTypeTwoEnum;
import com.logistics.tms.api.feign.workordercenter.model.response.WorkOrderListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/17
 */
public class WorkOrderListMapping extends MapperMapping<WorkOrderListResponseModel, WorkOrderListResponseDto> {

	@Override
	public void configure() {
		WorkOrderListResponseModel source = getSource();
		WorkOrderListResponseDto destination = getDestination();

		//状态
		destination.setStatusLabel(WorkOrderStatusEnum.getEnumByKey(source.getStatus()).getValue());

		//车主
		if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyType())) {
			destination.setCompanyCarrierName(source.getContactName() + " " + FrequentMethodUtils.encryptionData(source.getContactPhone(), EncodeTypeEnum.MOBILE_PHONE));
		} else if (CompanyTypeEnum.COMPANY.getKey().equals(source.getCompanyType())) {
			destination.setCompanyCarrierName(source.getCompanyName());
		} else {
			destination.setCompanyCarrierName(CommonConstant.BLANK_TEXT);
		}

		//车主 导出用
		if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyType())) {
			destination.setExportCompanyCarrierName(source.getContactName() + " " + source.getContactPhone());
		} else if (CompanyTypeEnum.COMPANY.getKey().equals(source.getCompanyType())) {
			destination.setExportCompanyCarrierName(source.getCompanyName());
		} else {
			destination.setExportCompanyCarrierName(CommonConstant.BLANK_TEXT);
		}

		//问题类型
		destination.setAbnormalProblem(WorkOrderAnomalyTypeOneEnum.getEnumByKey(source.getAnomalyTypeOne()).getValue() + "/" + WorkOrderAnomalyTypeTwoEnum.getEnumByKey(source.getAnomalyTypeTwo()).getValue());
	}
}
