package com.logistics.management.webapi.client.reserveapply.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.reserveapply.DriverReserveApplyClient;
import com.logistics.management.webapi.client.reserveapply.request.*;
import com.logistics.management.webapi.client.reserveapply.response.ReserveApplyDetailResponseModel;
import com.logistics.management.webapi.client.reserveapply.response.ReserveApplyListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DriverReserveApplyClientHystrix implements DriverReserveApplyClient {

    @Override
    public Result<PageInfo<ReserveApplyListResponseModel>> reserveApplyList(ReserveApplyListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<ReserveApplyListResponseModel>> reserveApplyListExport(ReserveApplyListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ReserveApplyDetailResponseModel> reserveApplyDetail(ReserveApplyDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> reserveApplyCancel(ReserveApplyCancelRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> reserveApplyAudit(ReserveApplyAuditRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> driverReserveRemit(ReserveApplyRemitRequestModel requestModel) {
        return Result.timeout();
    }
}
