package com.logistics.tms.api.feign.vehicletire.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class VehicleTireNoListRequestModel {
    @ApiModelProperty("轮胎牌号Id")
    private Long vehicleTrieNoId;
    @ApiModelProperty("轮胎牌号")
    private String tireBrand;
    @ApiModelProperty("数量")
    private Integer amount;
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;
}
