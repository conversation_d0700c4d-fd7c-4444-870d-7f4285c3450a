package com.logistics.management.webapi.client.settlestatement.tradition.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/11
 */
@Data
public class TraditionWaitSettleStatementListRequestModel extends AbstractPageForm<TraditionWaitSettleStatementListRequestModel> {

	@ApiModelProperty("车主名模糊查询")
	private String companyCarrierName;

	@ApiModelProperty("货主名称")
	private String companyEntrustName;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("需求单号")
	private String demandOrderCode;

	@ApiModelProperty("调度单号")
	private String dispatchOrderCode;

	@ApiModelProperty("车牌号")
	private String vehicleNo;

	@ApiModelProperty("司机（支持模糊搜索司机姓名或手机号）")
	private String driver;

	@ApiModelProperty("报价类型：1 单价，2 一口价")
	private Integer carrierPriceType;

	@ApiModelProperty("发货地址，发货省市区+详细地址")
	private String loadAddress;

	@ApiModelProperty("收货地址，发货省市区+详细地址")
	private String unloadAddress;

	@ApiModelProperty("发货仓库")
	private String loadWarehouse;

	@ApiModelProperty("收货仓库")
	private String unloadWarehouse;

	@ApiModelProperty("提货时间起")
	private String loadTimeStart;

	@ApiModelProperty("提货时间止")
	private String loadTimeEnd;

	@ApiModelProperty("卸货时间起")
	private String unloadTimeStart;

	@ApiModelProperty("卸货时间始")
	private String unloadTimeEnd;

	@ApiModelProperty("签收时间起")
	private String signTimeStart;

	@ApiModelProperty("签收时间止")
	private String signTimeEnd;

	@ApiModelProperty("拼单助手运单号")
	private List<String> carrierOrderCodeList;

	@ApiModelProperty("拼单助手需求单号")
	private List<String> demandOrderCodeList;

	@ApiModelProperty("若有勾选导出则传此字段，运单ids  ','拼接")
	private String carrierOrderIds;

	@ApiModelProperty("车主id")
	private Long companyCarrierId;

	@ApiModelProperty("请求来源：1 后台，2 前台")
	private Integer source;
}
