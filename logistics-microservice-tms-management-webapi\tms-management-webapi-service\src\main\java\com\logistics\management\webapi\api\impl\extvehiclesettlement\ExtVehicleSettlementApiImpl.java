package com.logistics.management.webapi.api.impl.extvehiclesettlement;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import com.logistics.management.webapi.api.feign.extvehiclesettlement.ExtVehicleSettlementApi;
import com.logistics.management.webapi.api.feign.extvehiclesettlement.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.extvehiclesettlement.mapping.ExtVehicleSettlementDetailMapping;
import com.logistics.management.webapi.api.impl.extvehiclesettlement.mapping.ExtVehicleSettlementListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.constant.ExportExtVehicleSettlement;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.tms.api.feign.extvehiclesettlement.ExtVehicleSettlementServiceApi;
import com.logistics.tms.api.feign.extvehiclesettlement.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/11/21 13:45
 */
@RestController
public class ExtVehicleSettlementApiImpl implements ExtVehicleSettlementApi {

    @Autowired
    private ExtVehicleSettlementServiceApi extVehicleSettlementServiceApi;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchExtVehicleSettlementListResponseDto>> searchExtVehicleSettlementList(@RequestBody @Valid SearchExtVehicleSettlementListRequestDto requestDto) {
        Result<PageInfo<SearchExtVehicleSettlementListResponseModel>> result = extVehicleSettlementServiceApi.searchExtVehicleSettlementList(MapperUtils.mapper(requestDto, SearchExtVehicleSettlementListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SearchExtVehicleSettlementListResponseDto> list = MapperUtils.mapper(pageInfo.getList(),SearchExtVehicleSettlementListResponseDto.class, new ExtVehicleSettlementListMapping());
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    /**
     * 详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<ExtVehicleSettlementDetailResponseDto> extVehicleSettlementDetail(@RequestBody @Valid ExtVehicleSettlementIdRequestDto requestDto) {
        Result<ExtVehicleSettlementDetailResponseModel> result = extVehicleSettlementServiceApi.extVehicleSettlementDetail(MapperUtils.mapper(requestDto, ExtVehicleSettlementIdRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        if(ListUtils.isNotEmpty(result.getData().getAttachmentList())){
            sourceSrcList.addAll(result.getData().getAttachmentList());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(),ExtVehicleSettlementDetailResponseDto.class, new ExtVehicleSettlementDetailMapping(configKeyConstant.fileAccessAddress,imageMap)));
    }

    /**
     * 付款
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result extVehicleSettlementPayment(@RequestBody @Valid ExtVehicleSettlementPaymentRequestDto requestDto) {
        if (StringUtils.isNotBlank(requestDto.getRemark()) && requestDto.getRemark().length() > CommonConstant.INTEGER_THREE_HUNDRED){
            throw new BizException(ManagementWebApiExceptionEnum.REMARK_LENGTH_MAX);
        }
        if (ListUtils.isEmpty(requestDto.getAttachmentList()) || requestDto.getAttachmentList().size() > CommonConstant.INTEGER_SIX){
            throw new BizException(ManagementWebApiExceptionEnum.ATTACHMENT_FILE_SIZE_MAX);
        }
        return extVehicleSettlementServiceApi.extVehicleSettlementPayment(MapperUtils.mapper(requestDto, ExtVehicleSettlementPaymentRequestModel.class));
    }

    /**
     * 回退
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result extVehicleSettlementFallback(@RequestBody @Valid ExtVehicleSettlementFallbackRequestDto requestDto) {
        return extVehicleSettlementServiceApi.extVehicleSettlementFallback(MapperUtils.mapper(requestDto, ExtVehicleSettlementFallbackRequestModel.class));
    }

    /**
     * 导出
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportExtVehicleSettlement(SearchExtVehicleSettlementListRequestDto requestDto, HttpServletResponse response) {
        SearchExtVehicleSettlementListRequestModel model = MapperUtils.mapper(requestDto, SearchExtVehicleSettlementListRequestModel.class);
        model.setPageNum(CommonConstant.INTEGER_ZERO);
        model.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<SearchExtVehicleSettlementListResponseModel>> result = extVehicleSettlementServiceApi.searchExtVehicleSettlementList(model);
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SearchExtVehicleSettlementListResponseDto> list = MapperUtils.mapper(pageInfo.getList(),SearchExtVehicleSettlementListResponseDto.class, new ExtVehicleSettlementListMapping());
        String fileName = "外部车辆结算管理" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM);
        Map<String, String> exportMap = ExportExtVehicleSettlement.getExportExtVehicleSettlement();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return list;
            }
        });
    }
}
