package com.logistics.management.webapi.api.impl.freight;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.freight.FreightApi;
import com.logistics.management.webapi.api.feign.freight.dto.*;
import com.logistics.management.webapi.api.impl.freight.mapping.GetDriverFreightMapping;
import com.logistics.management.webapi.api.impl.freight.mapping.GetFreightRuleDetailMapping;
import com.logistics.management.webapi.api.impl.freight.mapping.SearchFreightAddressMapping;
import com.logistics.management.webapi.api.impl.freight.mapping.SearchFreightListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportCarrierFreightAddressInfo;
import com.logistics.management.webapi.base.constant.ExportEntrustFreightAddressInfo;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.enums.PriceTypeEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.freight.FreightServiceApi;
import com.logistics.tms.api.feign.freight.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/12/24 16:13
 */
@RestController
public class FreightApiImpl implements FreightApi{
    @Autowired
    private FreightServiceApi freightServiceApi;

    /**
     * 查询运价管理列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchFreightListResponseDto>> searchList(@RequestBody SearchFreightListRequestDto requestDto) {
        Result<PageInfo<SearchFreightListResponseModel>>  result = freightServiceApi.searchList(MapperUtils.mapper(requestDto, SearchFreightListRequestModel.class));
        result.throwException();

        PageInfo pageInfo = result.getData();
        List<SearchFreightListResponseDto> dtoList = MapperUtils.mapper(result.getData().getList(),SearchFreightListResponseDto.class,new SearchFreightListMapping());
        pageInfo.setList(dtoList == null ? new ArrayList() : dtoList);
        return Result.success(pageInfo);
    }

    /**
     * 添加运价管理
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addFreight(@RequestBody AddFreightRequestDto requestDto) {
        return freightServiceApi.addFreight(MapperUtils.mapper(requestDto,AddFreightRequestModel.class));
    }

    /**
     * 启用/禁用
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> enableFreight(@RequestBody @Valid EnableFreightRequestDto requestDto) {
        return freightServiceApi.enableFreight(MapperUtils.mapper(requestDto, EnableFreightRequestModel.class));
    }

    /**
     * 运价地址列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchFreightAddressResponseDto>> searchFreightAddressList(@RequestBody SearchFreightAddressRequestDto requestDto) {
        Result<PageInfo<SearchFreightAddressResponseModel>> result = freightServiceApi.searchFreightAddressList(MapperUtils.mapper(requestDto, SearchFreightAddressRequestModel.class));
        result.throwException();

        PageInfo pageInfo = result.getData();
        List<SearchFreightAddressResponseDto> dtoList = MapperUtils.mapper(result.getData().getList(),SearchFreightAddressResponseDto.class,new SearchFreightAddressMapping());
        pageInfo.setList(dtoList == null ? new ArrayList() : dtoList);
        return Result.success(pageInfo);
    }

    /**
     * 添加/修改运价地址规则
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addFreightAddressRule(@RequestBody @Valid AddOrModifyFreightAddressRuleRequestDto requestDto) {
        for (FreightAddressRuleDto ruleDto : requestDto.getFreightAddressRuleList()) {
            if (ConverterUtils.toString(PriceTypeEnum.UNIT_PRICE.getKey()).equals(ruleDto.getFreightType())){
                if (!FrequentMethodUtils.isNumberOrFloatNumberTwo(ruleDto.getFreightFee())
                        || ConverterUtils.toBigDecimal(ruleDto.getFreightFee()).compareTo(BigDecimal.ZERO) <= CommonConstant.INTEGER_ZERO
                        || ConverterUtils.toBigDecimal(ruleDto.getFreightFee()).compareTo(CommonConstant.BIG_DECIMAL_FIVE_THOUSAND) > CommonConstant.INTEGER_ZERO){
                    throw new BizException(ManagementWebApiExceptionEnum.FREIGHT_UNIT_PRICE_ERROR);
                }
            }else if (ConverterUtils.toString(PriceTypeEnum.FIXED_PRICE.getKey()).equals(ruleDto.getFreightType())){
                if (!FrequentMethodUtils.isNumberOrFloatNumberTwo(ruleDto.getFreightFee())
                        || ConverterUtils.toBigDecimal(ruleDto.getFreightFee()).compareTo(BigDecimal.ZERO) <= CommonConstant.INTEGER_ZERO
                        || ConverterUtils.toBigDecimal(ruleDto.getFreightFee()).compareTo(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_THOUSAND) > CommonConstant.INTEGER_ZERO){
                    throw new BizException(ManagementWebApiExceptionEnum.FREIGHT_FIXED_PRICE_ERROR);
                }
            }else{
                throw new BizException(ManagementWebApiExceptionEnum.FREIGHT_FEE_TYPE_ERROR);
            }
        }
        return freightServiceApi.addFreightAddressRule(MapperUtils.mapper(requestDto,AddOrModifyFreightAddressRuleRequestModel.class));
    }

    /**
     * 运价地址规则详情
     * @param responseDto
     * @return
     */
    @Override
    public Result<FreightAddressRuleDetailResponseDto> getFreightRuleDetail(@RequestBody @Valid FreightAddressRuleDetailRequestDto responseDto) {
        Result<FreightAddressRuleDetailResponseModel> result = freightServiceApi.getFreightRuleDetail(MapperUtils.mapper(responseDto,FreightAddressRuleDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),FreightAddressRuleDetailResponseDto.class,new GetFreightRuleDetailMapping()));
    }

    /**
     * 删除运价地址规则
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> deleteFreightAddressRule(@RequestBody @Valid DeleteFreightAddressRequestDto requestDto) {
        return freightServiceApi.deleteFreightAddressRule(MapperUtils.mapper(requestDto,DeleteFreightAddressRequestModel.class));
    }

    /**
     * 统一加价/减价
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> modifyFreightPrice(@RequestBody @Valid ModifyFreightPriceRequestDto requestDto) {
        return freightServiceApi.modifyFreightPrice(MapperUtils.mapper(requestDto,ModifyFreightPriceRequestModel.class));
    }

    /**
     * 运价日志
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<FreightLogsResponseDto>> freightLogs(@RequestBody @Valid FreightLogsRequestDto requestDto) {
        Result<List<FreightLogsResponseModel>> result = freightServiceApi.freightLogs(MapperUtils.mapper(requestDto,FreightLogsRequestModel.class));
        return Result.success(MapperUtils.mapper(result.getData(),FreightLogsResponseDto.class));
    }

    /**
     * 导出运价地址
     * @param requestDto
     * @param response
     */
    @Override
    public void exportFreightAddress(SearchFreightAddressRequestDto requestDto, HttpServletResponse response) {

        Result<PageInfo<SearchFreightAddressResponseModel>> result = freightServiceApi.searchFreightAddressList(MapperUtils.mapper(requestDto, SearchFreightAddressRequestModel.class));
        result.throwException();
        List<SearchFreightAddressResponseDto> dtoList = MapperUtils.mapper(result.getData().getList(),SearchFreightAddressResponseDto.class,new SearchFreightAddressMapping());

        String companyName ="";
        if(result.getData()!=null && ListUtils.isNotEmpty(result.getData().getList())){
            FreightCompanyInfoRequestModel requestCompanyInfoRequestModel = new FreightCompanyInfoRequestModel();
            requestCompanyInfoRequestModel.setFreightId( result.getData().getList().get(CommonConstant.INTEGER_ZERO).getFreightId());
            requestCompanyInfoRequestModel.setRoleType(ConverterUtils.toInt(requestDto.getRoleType()));
            Result<FreightCompanyInfoResponseModel> modelResult = freightServiceApi.getFreightCompanyInfo(requestCompanyInfoRequestModel);
            modelResult.throwException();
            companyName = modelResult.getData().getCompanyName();
        }
        String fileName =  companyName+"运价规则"+ DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        //货主
        if(CommonConstant.ONE.equals(requestDto.getRoleType())){
            Map<String, String> exportTypeMap = ExportEntrustFreightAddressInfo.getEntrustFreightAddressInfo();
            ExcelUtils.exportExcelForServlet(fileName, fileName, exportTypeMap, response, new AbstractExcelLoadBean() {
                @Override
                public List load() {
                    return dtoList;
                }
            });
        }

        //车主
        if(CommonConstant.TWO.equals(requestDto.getRoleType())){
            Map<String, String> exportTypeMap = ExportCarrierFreightAddressInfo.getCarrierFreightAddressInfo();
            ExcelUtils.exportExcelForServlet(fileName, fileName, exportTypeMap, response, new AbstractExcelLoadBean() {
                @Override
                public List load() {
                    return dtoList;
                }
            });
        }
    }

    /**
     * 调度车辆-司机运价
     * @param requestDto
     * @return
     */
    @Override
    public Result<DriverFreightByDemandOrderIdsAndVehicleResponseDto> getDriverFreight(@RequestBody DriverFreightByDemandOrderIdsAndVehicleRequestDto requestDto) {
        Result<DriverFreightByDemandOrderIdsAndVehicleResponseModel> result = freightServiceApi.getDriverFreight(MapperUtils.mapper(requestDto,DriverFreightByDemandOrderIdsAndVehicleRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),DriverFreightByDemandOrderIdsAndVehicleResponseDto.class,new GetDriverFreightMapping()));
    }

    /**
     * 委托发布-根据委托方地址和数量查询价格
     * @param requestDto
     * @return
     */
    @Override
    public Result<GetPriceByAddressAndAmountResponseDto> getPriceByAddressAndAmount(@RequestBody GetPriceByAddressAndAmountRequestDto requestDto) {
        Result<GetPriceByAddressAndAmountResponseModel> result = freightServiceApi.getPriceByAddressAndAmount(MapperUtils.mapper(requestDto,GetPriceByAddressAndAmountRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),GetPriceByAddressAndAmountResponseDto.class));
    }
}
