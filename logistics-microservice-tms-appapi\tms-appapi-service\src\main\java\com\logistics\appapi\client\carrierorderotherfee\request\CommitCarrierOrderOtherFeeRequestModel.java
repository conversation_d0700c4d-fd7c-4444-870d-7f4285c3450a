package com.logistics.appapi.client.carrierorderotherfee.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CommitCarrierOrderOtherFeeRequestModel {

    @ApiModelProperty(value = "费用主表id" ,required = true)
    private Long carrierOrderOtherFeeId;

    @ApiModelProperty(value = "费用信息",required = true)
    private List<CommitCarrierOrderOtherFeeItemRequestModel> otherFeeList;

    @ApiModelProperty(value = "操作类型：1 提交，2 审核通过",required = true)
    private String operateType;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("来源：1 后台，2 前台，3 小程序")
    private String requestSource;
}
