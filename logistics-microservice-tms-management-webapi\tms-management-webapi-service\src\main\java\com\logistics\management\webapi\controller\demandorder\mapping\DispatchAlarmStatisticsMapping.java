package com.logistics.management.webapi.controller.demandorder.mapping;

import com.logistics.management.webapi.client.demandorder.response.DispatchAlarmStatisticsResponseModel;
import com.logistics.management.webapi.controller.demandorder.response.DispatchAlarmStatisticsResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2021/10/28 10:58
 */
public class DispatchAlarmStatisticsMapping extends MapperMapping<DispatchAlarmStatisticsResponseModel, DispatchAlarmStatisticsResponseDto> {
    @Override
    public void configure() {
        DispatchAlarmStatisticsResponseModel source = getSource();
        DispatchAlarmStatisticsResponseDto destination = getDestination();

        destination.setGoodsAmount(source.getGoodsAmount().stripTrailingZeros().toPlainString());
    }
}
