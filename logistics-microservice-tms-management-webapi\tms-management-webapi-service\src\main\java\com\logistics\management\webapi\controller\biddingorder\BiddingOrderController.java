package com.logistics.management.webapi.controller.biddingorder;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.utils.ConvertPageInfoUtil;
import com.logistics.management.webapi.client.biddingorder.BiddingOrderClient;
import com.logistics.management.webapi.client.biddingorder.request.*;
import com.logistics.management.webapi.client.biddingorder.response.*;
import com.logistics.management.webapi.controller.biddingorder.mapping.BiddingOrderDetailMapping;
import com.logistics.management.webapi.controller.biddingorder.mapping.BiddingOrderQuoteDetailMapping;
import com.logistics.management.webapi.controller.biddingorder.mapping.SearchBiddingDemandMapping;
import com.logistics.management.webapi.controller.biddingorder.mapping.SearchBiddingOrderListMapping;
import com.logistics.management.webapi.controller.biddingorder.request.*;
import com.logistics.management.webapi.controller.biddingorder.response.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 竞价管理
 * <AUTHOR>
 * @date:2024/4/26 9:20
 */
@Api(value = "API-biddingOrder-竞价管理", tags = "竞价管理")
@RestController
@RequestMapping(value = "/api/biddingOrder")
public class BiddingOrderController {
    @Resource
    private BiddingOrderClient biddingOrderClient;

    /**
     * 查询竞价列表 v3.20.0
     * @param requestDto 请求参数
     * @return 竞价列表
     */
    @PostMapping(value = "/searchBiddingOrderList")
    @ApiOperation(value = "查询竞价列表", tags = "3.20.0")
    public Result<PageInfo<SearchBiddingOrderListResponseDto>> searchBiddingOrderList(
            @RequestBody @Valid SearchBiddingOrderListRequestDto requestDto){
        SearchBiddingOrderListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchBiddingOrderListRequestModel.class);
        Result<PageInfo<SearchBiddingOrderListResponseModel>> pageInfoResult = biddingOrderClient.searchBiddingOrderListByManager(requestModel);
        pageInfoResult.throwException();
        return Result.success(ConvertPageInfoUtil.convertPageInfo(pageInfoResult.getData(),SearchBiddingOrderListResponseDto.class,new SearchBiddingOrderListMapping()));
    }

    /**
     * 竞价单详情 v3.20.0
     * @param requestDto 请求参数
     * @return 竞价单详情
     */
    @PostMapping(value = "/biddingOrderDetail")
    @ApiOperation(value = "竞价单详情", tags = "3.20.0")
    public Result<BiddingOrderDetailResponseDto> biddingOrderDetail(
            @RequestBody @Valid BiddingOrderDetailRequestDto requestDto){
        BiddingOrderDetailRequestModel requestModel = MapperUtils.mapper(requestDto, BiddingOrderDetailRequestModel.class);
        Result<BiddingOrderDetailByManagerResponseModel> result = biddingOrderClient.biddingOrderDetailByManager(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),BiddingOrderDetailResponseDto.class,new BiddingOrderDetailMapping()));
    }


    /**
     * 车主报价详情 v3.20.0
     * @param requestDto 请求参数
     * @return 车主报价详情
     */
    @PostMapping(value = "/biddingOrderQuoteDetail")
    @ApiOperation(value = "车主报价详情", tags = "3.20.0")
    public Result<BiddingOrderQuoteDetailResponseDto> biddingOrderQuoteDetail(
            @RequestBody @Valid BiddingOrderQuoteDetailRequestDto requestDto){
        BiddingOrderQuoteDetailRequestModel requestModel = MapperUtils.mapper(requestDto, BiddingOrderQuoteDetailRequestModel.class);
        Result<BiddingOrderQuoteDetailResponseModel> result = biddingOrderClient.biddingOrderQuoteDetail(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),BiddingOrderQuoteDetailResponseDto.class,new BiddingOrderQuoteDetailMapping()));
    }

    /**
     * 获取最低价 v3.20.0
     * @param requestDto 请求参数
     * @return 最低价
     */
    @PostMapping(value = "/bottomPrice")
    @ApiOperation(value = "获取最低价", tags = "3.20.0")
    public Result<BottomPriceResponseDto> bottomPrice(
            @RequestBody @Valid BottomPriceRequestDto requestDto){
        BottomPriceRequestModel requestModel = MapperUtils.mapper(requestDto, BottomPriceRequestModel.class);
        Result<BottomPriceResponseModel> result = biddingOrderClient.bottomPrice(requestModel);
        result.throwException();
        BottomPriceResponseDto bottomPriceResponseDto = new BottomPriceResponseDto();
        if(result.getData() != null && result.getData().getBottomPrice() != null){
            bottomPriceResponseDto.setBottomPrice(result.getData().getBottomPrice().stripTrailingZeros().toPlainString());
        }
        return Result.success(bottomPriceResponseDto);
    }


    /**
     * 选择车主报价 v3.22.0
     * @param requestDto 请求参数
     * @return boolean
     */
    @PostMapping(value = "/confirmQuote")
    @ApiOperation(value = "选择车主报价", tags = "3.22.0")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> confirmQuote(
            @RequestBody @Valid ConfirmQuoteRequestDto requestDto){
        ConfirmQuoteRequestModel requestModel = MapperUtils.mapper(requestDto, ConfirmQuoteRequestModel.class);
        Result<Boolean> result = biddingOrderClient.confirmQuote(requestModel);
        result.throwException();
        return Result.success(true);
    }


    /**
     * 修改报价 v3.20.0
     * @param requestDto 请求参数
     * @return boolean
     */
    @PostMapping(value = "/modifyQuote")
    @ApiOperation(value = "修改报价", tags = "3.20.0")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> modifyQuote(
            @RequestBody @Valid ModifyQuoteRequestDto requestDto){
        ModifyQuoteRequestModel requestModel = MapperUtils.mapper(requestDto, ModifyQuoteRequestModel.class);
        Result<Boolean> result = biddingOrderClient.modifyQuote(requestModel);
        result.throwException();
        return Result.success(true);
    }

    /**
     * 暂停报价 v3.20.0
     * @param requestDto 请求参数
     * @return boolean
     */
    @PostMapping(value = "/stopBidding")
    @ApiOperation(value = "暂停报价", tags = "3.20.0")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> stopBidding(
            @RequestBody @Valid StopBiddingRequestDto requestDto){
        StopBiddingRequestModel requestModel = MapperUtils.mapper(requestDto, StopBiddingRequestModel.class);
        Result<Boolean> result = biddingOrderClient.stopBiddingByManager(requestModel);
        result.throwException();
        return result;
    }

    /**
     * 重新报价 v3.20.0
     * @param requestDto 请求参数
     * @return boolean
     */
    @PostMapping(value = "/rebiddingQuote")
    @ApiOperation(value = "重新报价", tags = "3.20.0")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> rebiddingQuote(
            @RequestBody @Valid RebiddingRequestDto requestDto){
        RebiddingRequestModel requestModel = MapperUtils.mapper(requestDto, RebiddingRequestModel.class);
        Result<Boolean> result = biddingOrderClient.rebiddingQuote(requestModel);
        result.throwException();
        return Result.success(true);
    }

    /**
     * 取消报价 v3.20.0
     * @param requestDto 请求参数
     * @return boolean
     */
    @PostMapping(value = "/cancelBidding")
    @ApiOperation(value = "取消报价", tags = "3.20.0")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> cancelBidding(
            @RequestBody @Valid CancelBiddingRequestDto requestDto){
        CancelBiddingRequestModel requestModel = MapperUtils.mapper(requestDto, CancelBiddingRequestModel.class);
        Result<Boolean> result = biddingOrderClient.cancelBiddingByManager(requestModel);
        result.throwException();
        return result;
    }

    /**
     * 删除需求单 v3.20.0
     * @param requestDto 请求参数
     * @return boolean
     */
    @PostMapping(value = "/delDemand")
    @ApiOperation(value = "删除需求单", tags = "3.20.0")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> delDemand(
            @RequestBody @Valid DelDemandRequestDto requestDto){
        DelDemandRequestModel requestModel = MapperUtils.mapper(requestDto, DelDemandRequestModel.class);
        Result<Boolean> result = biddingOrderClient.delDemand(requestModel);
        result.throwException();
        return Result.success(true);
    }

    /**
     * 竞价查询需求单 v3.20.0
     *
     * @param requestDto 请求参数
     * @return boolean
     */
    @PostMapping(value = "/searchBiddingDemand")
    @ApiOperation(value = "竞价查询需求单", tags = "3.20.0")
    public Result<List<SearchBiddingDemandResponseDto>> searchBiddingDemand(
            @RequestBody @Valid SearchBiddingDemandRequestDto requestDto) {
        SearchBiddingDemandRequestModel requestModel = MapperUtils.mapper(requestDto, SearchBiddingDemandRequestModel.class);
        Result<List<SearchBiddingDemandResponseModel>> result = biddingOrderClient.searchBiddingDemand(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SearchBiddingDemandResponseDto.class, new SearchBiddingDemandMapping()));
    }


    /**
     * 新增需求单 v3.20.0
     * @param requestDto 请求参数
     * @return boolean
     */
    @PostMapping(value = "/addDemand")
    @ApiOperation(value = "新增需求单", tags = "3.20.0")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addDemand(
            @RequestBody @Valid AddDemandRequestDto requestDto){
        AddDemandRequestModel requestModel = MapperUtils.mapper(requestDto, AddDemandRequestModel.class);
        Result<Boolean> result = biddingOrderClient.addDemand(requestModel);
        result.throwException();
        return Result.success(true);
    }






}
