package com.logistics.tms.controller.routeconfig;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.routedistance.RouteDistanceConfigBiz;
import com.logistics.tms.controller.routeconfig.request.*;
import com.logistics.tms.controller.routeconfig.response.RouteDistanceConfigDetailResponseModel;
import com.logistics.tms.controller.routeconfig.response.RouteDistanceConfigRecommendResponseModel;
import com.logistics.tms.controller.routeconfig.response.RouteDistanceConfigResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Api(value = "路线配置管理")
@RequestMapping(value = "/service/route/distance/config")
public class RouteDistanceConfigListController {

    @Resource
    private RouteDistanceConfigBiz routeDistanceConfigBiz;


    @PostMapping(value = "/searchList")
    @ApiOperation(value = "路线距离配置列表", tags = "1.3.5")
    Result<PageInfo<RouteDistanceConfigResponseModel>> searchList(@RequestBody RouteDistanceConfigListRequestModel requestModel) {
        return Result.success(routeDistanceConfigBiz.searchList(requestModel));
    }

    @PostMapping(value = "/detail")
    @ApiOperation(value = "路线距离配置详情", tags = "1.3.5")
    Result<RouteDistanceConfigDetailResponseModel> detail(@RequestBody RouteDistanceConfigRequestModel requestModel) {
        return Result.success(routeDistanceConfigBiz.detail(requestModel));
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "路线距离配置新增", tags = "1.3.5")
    Result<Boolean> add(@RequestBody RouteDistanceConfigAddRequestModel requestModel) {
        return Result.success(routeDistanceConfigBiz.add(requestModel));
    }

    @PostMapping(value = "/edit")
    @ApiOperation(value = "路线距离配置编辑", tags = "1.3.5")
    Result<Boolean> edit(@RequestBody RouteDistanceConfigEditRequestModel requestModel) {
        return Result.success(routeDistanceConfigBiz.edit(requestModel));
    }

    @PostMapping(value = "/delete")
    @ApiOperation(value = "路线距离配置删除", tags = "1.3.5")
    Result<Boolean> delete(@RequestBody RouteDistanceConfigRequestModel requestModel) {
        return Result.success(routeDistanceConfigBiz.delete(requestModel));
    }

    @PostMapping(value = "/recommend")
    @ApiOperation(value = "路线距离配置推荐", tags = "1.3.5")
    Result<RouteDistanceConfigRecommendResponseModel> recommend(@RequestBody RouteDistanceConfigRecommendRequestModel requestModel) {
        return Result.success(routeDistanceConfigBiz.recommend(requestModel));
    }
}
