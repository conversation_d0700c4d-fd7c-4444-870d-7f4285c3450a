package com.logistics.management.webapi.controller.freightconfig.request.address;

import com.logistics.management.webapi.controller.freightconfig.request.ladder.CarrierFreightConfigLadderRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigAddressEditRequestDto extends CarrierFreightAddressDetailRequestDto {

    @ApiModelProperty(value = "编辑、新增阶梯配置")
    @Size(max = 10, message = "一条路线最多存在10条阶梯价格")
    private List<CarrierFreightConfigLadderRequestDto> ladderConfigList;
}
