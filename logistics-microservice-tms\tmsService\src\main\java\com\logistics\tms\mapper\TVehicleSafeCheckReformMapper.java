package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TVehicleSafeCheckReform;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TVehicleSafeCheckReformMapper extends BaseMapper<TVehicleSafeCheckReform> {

    TVehicleSafeCheckReform getBySafeCheckVehicleId(@Param("safeCheckVehicleId") Long safeCheckVehicleId);

}