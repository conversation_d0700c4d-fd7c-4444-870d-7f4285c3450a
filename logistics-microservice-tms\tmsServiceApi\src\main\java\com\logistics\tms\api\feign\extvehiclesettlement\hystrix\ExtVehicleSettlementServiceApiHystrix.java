package com.logistics.tms.api.feign.extvehiclesettlement.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.extvehiclesettlement.ExtVehicleSettlementServiceApi;
import com.logistics.tms.api.feign.extvehiclesettlement.model.*;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2019/11/20 13:30
 */
@Component
public class ExtVehicleSettlementServiceApiHystrix implements ExtVehicleSettlementServiceApi {
    @Override
    public Result<PageInfo<SearchExtVehicleSettlementListResponseModel>> searchExtVehicleSettlementList(SearchExtVehicleSettlementListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ExtVehicleSettlementDetailResponseModel> extVehicleSettlementDetail(ExtVehicleSettlementIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result extVehicleSettlementPayment(ExtVehicleSettlementPaymentRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result extVehicleSettlementFallback(ExtVehicleSettlementFallbackRequestModel requestModel) {
        return Result.timeout();
    }
}
