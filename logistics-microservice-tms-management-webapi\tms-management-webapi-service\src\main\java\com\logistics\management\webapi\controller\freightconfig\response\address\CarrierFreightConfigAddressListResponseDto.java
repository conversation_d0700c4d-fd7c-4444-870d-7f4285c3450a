package com.logistics.management.webapi.controller.freightconfig.response.address;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.controller.freightconfig.response.scheme.CarrierFreightConfigSchemeResponseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigAddressListResponseDto extends CarrierFreightConfigSchemeResponseDto {

    @ApiModelProperty(value = "路线配置列表")
    private PageInfo<CarrierFreightConfigAddressItemResponseDto> addressConfigPage;
}
