package com.logistics.tms.client.feign.tray.basicdata.commonaddress.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("批量查询客户信息响应")
public class BatchQueryCustomerInfoResponse {

    @ApiModelProperty("客户ID")
    private Long id;

    @ApiModelProperty("公司中文名称")
    private String chineseName;

    @ApiModelProperty("客户公司类型 4:c  5:c1")
    private String customerCompanyType;

    @ApiModelProperty("会员版本")
    private String memberVersion;
}