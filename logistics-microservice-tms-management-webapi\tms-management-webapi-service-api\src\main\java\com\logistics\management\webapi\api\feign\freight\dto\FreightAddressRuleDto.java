package com.logistics.management.webapi.api.feign.freight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;

/**
 * 运价格则
 * @Author: sj
 * @Date: 2019/12/24 13:14
 */
@Data
public class FreightAddressRuleDto {
    @ApiModelProperty("运价地址规则ID")
    private String freightAddressRuleId = "";
    @ApiModelProperty("序列")
    private String ruleIndex = "";
    @ApiModelProperty("价格区间下线")
    @DecimalMax(value = "100000", message = "阶梯数量不能大于100000")
    private String amountFrom = "";
    @ApiModelProperty("1 小于 2 小于等于")
    private String fromSymbol = "1";
    @ApiModelProperty("价格区间上线")
    @DecimalMax(value = "100000", message = "阶梯数量不能大于100000")
    private String amountTo = "";
    @ApiModelProperty("1 小于 2 小于等于")
    private String toSymbol = "2";
    @ApiModelProperty("计价规则: 1 按件计价 2 按吨计价")
    private String calcRule = "2";
    @ApiModelProperty("费用类型 1 单价 2 一口价")
    private String freightType = "";
    @ApiModelProperty("费用")
    private String freightFee = "";
}
