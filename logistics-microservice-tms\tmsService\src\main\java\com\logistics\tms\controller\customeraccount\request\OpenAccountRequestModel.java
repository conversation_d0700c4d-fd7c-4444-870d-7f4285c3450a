package com.logistics.tms.controller.customeraccount.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author：wjf
 * @date：2021/5/13 17:45
 */
@Data
public class OpenAccountRequestModel {
    private Long userId;

    private Integer userRole;

    private String mobile;

    private String userName;

    @ApiModelProperty("类型：1 开通，2 关闭，3 删除")
    private Integer type;

    @ApiModelProperty(value = "禁启用 1 禁用 0")
    private Integer enabled;

    @ApiModelProperty(value = "账号是否关闭0否1是")
    private Integer ifClose;
}
