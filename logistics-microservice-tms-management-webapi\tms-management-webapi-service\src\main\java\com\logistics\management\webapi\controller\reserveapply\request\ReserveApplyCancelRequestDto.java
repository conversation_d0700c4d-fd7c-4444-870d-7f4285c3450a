package com.logistics.management.webapi.controller.reserveapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Data
public class ReserveApplyCancelRequestDto {

    @ApiModelProperty(value = "申请记录id", required = true)
    @NotBlank(message = "请选择要查看的记录")
    private String applyId;

    @ApiModelProperty(value = "撤销说明,1-100个字符", required = true)
    @NotBlank(message = "请填写撤销说明,1-100个字符")
    @Length(min = 1, max = 100, message = "请填写撤销说明,1-100个字符")
    private String remark;
}
