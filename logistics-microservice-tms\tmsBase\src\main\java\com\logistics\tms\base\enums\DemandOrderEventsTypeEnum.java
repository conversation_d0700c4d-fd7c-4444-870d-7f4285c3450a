package com.logistics.tms.base.enums;

/**
 * liang current user system login name
 * 2018/9/28 current system date
 */
public enum DemandOrderEventsTypeEnum {
    ACCEPT_CARRIERORDER(10,"承接订单"),
    DISPATCH_VEHICLE(20,"调度车辆"),
    CANCEL_DEMANDORDER(30,"已取消"),
    COMPLETE_DISPATCH_EVENTS(40,"完成调度"),
    WAIT_SIGN_UP(50,"待签收"),
    SIGN_UP(60,"已签收"),
    EMPTY(70,"已放空"),
    ;

    private Integer key;
    private String value;

    DemandOrderEventsTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
