package com.logistics.management.webapi.controller.freightconfig;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.vehicleassetmanagement.dto.ExportVehicleBasicInfoResponseDto;
import com.logistics.management.webapi.api.impl.vehicleassetmanagement.mapper.ExportVehicleBasicInfoListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportExcelShippingFreightRuleConfig;
import com.logistics.management.webapi.base.constant.ExportExcelVehicleAssertInfo;
import com.logistics.management.webapi.client.freightconfig.ShippingFreightRuleConfigClient;
import com.logistics.management.webapi.client.freightconfig.request.*;
import com.logistics.management.webapi.client.freightconfig.request.shipping.AddShippingFreightRuleReqModel;
import com.logistics.management.webapi.client.freightconfig.request.shipping.ListShippingFreightRuleConfigReqModel;
import com.logistics.management.webapi.client.freightconfig.response.GetConfigVechicleRespModel;
import com.logistics.management.webapi.client.freightconfig.response.shipping.ListShippingFreightRuleListRespModel;
import com.logistics.management.webapi.controller.freightconfig.request.ConfigVehicleItemReqDto;
import com.logistics.management.webapi.controller.freightconfig.request.shipping.*;
import com.logistics.management.webapi.controller.freightconfig.response.shipping.GetConfigVechicleRespDto;
import com.logistics.management.webapi.controller.freightconfig.response.shipping.ListShippingFreightRuleListRespDto;
import com.logistics.tms.api.feign.vehicleassetmanagement.model.ExportVehicleBasicInfoResponseModel;
import com.logistics.tms.api.feign.vehicleassetmanagement.model.VehicleAssetManagementListRequestModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ExcelUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 配置运价规则
 */
@RestController
@RequestMapping(value = "/api/freight/rule")
public class ShippingFreightRuleConfigController {

    @Resource
    ShippingFreightRuleConfigClient shippingFreightRuleConfigClient;

    /**
     * 配置运价规则列表  v2.42
     */
    @PostMapping(value = "/getList")
    public Result<PageInfo<ListShippingFreightRuleListRespDto>> getList(@RequestBody ListShippingFreightRuleConfigReqDto reqDto) {
        ListShippingFreightRuleConfigReqModel reqModel = MapperUtils.mapperNoDefault(reqDto, ListShippingFreightRuleConfigReqModel.class);
        Result<PageInfo<ListShippingFreightRuleListRespModel>> result = shippingFreightRuleConfigClient.getList(reqModel);
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<ListShippingFreightRuleListRespDto> respDtos = MapperUtils.mapperNoDefault(pageInfo.getList(), ListShippingFreightRuleListRespDto.class);
        pageInfo.setList(respDtos);
        return Result.success(pageInfo);
    }


    /**
     * 配置运价规则列表导出 v2.42
     */
    @GetMapping(value = "/exportList")
    void exportList(ListShippingFreightRuleConfigReqDto reqDto, HttpServletResponse response) {
        ListShippingFreightRuleConfigReqModel reqModel = MapperUtils.mapperNoDefault(reqDto, ListShippingFreightRuleConfigReqModel.class);
        reqModel.setPageNum(CommonConstant.INTEGER_ZERO);
        reqModel.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<List<ListShippingFreightRuleListRespModel>> result = shippingFreightRuleConfigClient.exportList(reqModel);
        result.throwException();
        String fileName="零担运价规则"+ DateUtils.dateToString(new Date(),CommonConstant.YYYY_MM_DD_HH_MM_SS);

        List<ListShippingFreightRuleListRespDto> responseDtoList = MapperUtils.mapper(result.getData(),ListShippingFreightRuleListRespDto.class);
        Map<String ,String> exportTypeMap= ExportExcelShippingFreightRuleConfig.getHeadMap();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportTypeMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return responseDtoList;
            }
        });
    }




    /**
     * 新增配置运价规则路线  v2.42
     */
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @PostMapping(value = "/add")
    public Result<Boolean> add(@RequestBody AddShippingFreightRuleReqDto reqDto) {
        AddShippingFreightRuleReqModel reqModel = MapperUtils.mapperNoDefault(reqDto, AddShippingFreightRuleReqModel.class);
        Result<Boolean> result = shippingFreightRuleConfigClient.add(reqModel);
        result.throwException();
        return Result.success(true);
    }




    /**
     * 零担运价管理启用禁用 v2.42
     * @param requestDto
     * @return
     */
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @PostMapping(value = "/enableOrForbidOrDelete")
    public Result<Boolean> enableOrForbid(@RequestBody @Valid EnableOrForbidOrDeleteReqDto requestDto) {
        EnableOrForbidOrDeleteReqModel reqModel = MapperUtils.mapperNoDefault(requestDto, EnableOrForbidOrDeleteReqModel.class);
        Result<Boolean> result = shippingFreightRuleConfigClient.enableOrForbid(reqModel);
        result.throwException();
        return Result.success(true);
    }


    /**
     * 配置车长运价 v2.42
     */
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @PostMapping(value = "/configVehicle")
    public Result<Boolean> configVehicle(@RequestBody @Valid ConfigVehicleReqDto requestDto) {

        List<ConfigVehicleReqModel> configVehicleReqModels = new ArrayList<>();
        for (String shippingFreightRuleId : requestDto.getShippingFreightRuleIds()) {
            Set<String> vehicleLengthSet = new LinkedHashSet<>();
            for (ConfigVehicleItemReqDto itemReqDto : requestDto.getItemReqDtos()) {

                for (String vehicleLength : itemReqDto.getVehicleLength()) {
                    if (!vehicleLengthSet.add(vehicleLength)) {
                        throw new BizException("存在相同车长配置");
                    }

                    ConfigVehicleReqModel configVehicleReqModel = new ConfigVehicleReqModel();
                    configVehicleReqModel.setShippingFreightRuleId(Long.parseLong(shippingFreightRuleId));
                    configVehicleReqModel.setVehicleLength(new BigDecimal(vehicleLength));

                    List<ConfigVehicleItemModel> itemReqDtos = new ArrayList<>();
                    for (ConfigVehicleItemDto configVehicleItemDto : itemReqDto.getLadderConfigList()) {
                        ConfigVehicleItemModel configVehicleItemModel = new ConfigVehicleItemModel();
                        configVehicleItemModel.setCountEnd(configVehicleItemDto.getCountEnd());
                        configVehicleItemModel.setCountStart(configVehicleItemDto.getCountStart());
                        configVehicleItemModel.setPrice(new BigDecimal(configVehicleItemDto.getPrice()));
                        configVehicleItemModel.setPriceType(Integer.parseInt(configVehicleItemDto.getPriceType()) );
                        configVehicleItemModel.setSort(Integer.parseInt(configVehicleItemDto.getSort()));
                        itemReqDtos.add(configVehicleItemModel);
                    }
                    configVehicleReqModel.setItemReqDtos(itemReqDtos);

                    configVehicleReqModels.add(configVehicleReqModel);
                }

            }
        }




        Result<Boolean> result = shippingFreightRuleConfigClient.configVehicle(configVehicleReqModels);
        result.throwException();
        return Result.success(true);
    }

    /**
     * 编辑 运价v2.42
     */
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @PostMapping(value = "/editVehicle")
    public Result<Boolean> editVehicle(@RequestBody @Valid ConfigVehicleReqDto requestDto) {

        List<ConfigVehicleReqModel> configVehicleReqModels = new ArrayList<>();
        for (String shippingFreightRuleId : requestDto.getShippingFreightRuleIds()) {
            Set<String> vehicleLengthSet = new LinkedHashSet<>();
            for (ConfigVehicleItemReqDto itemReqDto : requestDto.getItemReqDtos()) {

                for (String vehicleLength : itemReqDto.getVehicleLength()) {
                    if (!vehicleLengthSet.add(vehicleLength)) {
                        throw new BizException("存在相同车长配置");
                    }

                    ConfigVehicleReqModel configVehicleReqModel = new ConfigVehicleReqModel();
                    configVehicleReqModel.setShippingFreightRuleId(Long.parseLong(shippingFreightRuleId));
                    configVehicleReqModel.setVehicleLength(new BigDecimal(vehicleLength));

                    List<ConfigVehicleItemModel> itemReqDtos = new ArrayList<>();
                    for (ConfigVehicleItemDto configVehicleItemDto : itemReqDto.getLadderConfigList()) {
                        ConfigVehicleItemModel configVehicleItemModel = new ConfigVehicleItemModel();
                        configVehicleItemModel.setCountEnd(configVehicleItemDto.getCountEnd());
                        configVehicleItemModel.setCountStart(configVehicleItemDto.getCountStart());
                        configVehicleItemModel.setPrice(new BigDecimal(configVehicleItemDto.getPrice()));
                        configVehicleItemModel.setPriceType(Integer.parseInt(configVehicleItemDto.getPriceType()) );
                        configVehicleItemModel.setSort(Integer.parseInt(configVehicleItemDto.getSort()));
                        itemReqDtos.add(configVehicleItemModel);
                    }
                    configVehicleReqModel.setItemReqDtos(itemReqDtos);

                    configVehicleReqModels.add(configVehicleReqModel);
                }

            }
        }


        Result<Boolean> result = shippingFreightRuleConfigClient.editVehicle(configVehicleReqModels);
        result.throwException();
        return Result.success(true);
    }



    /**
     * 车长查看 v2.42
     */
    @PostMapping(value = "/getConfigVehicle")
    public Result<GetConfigVechicleRespDto> getConfigVehicle(@RequestBody @Valid ShippingFreightRuleIdReqDto reqDto) {
        ShippingFreightRuleIdReqModel shippingFreightRuleIdReqModel = MapperUtils.mapperNoDefault(reqDto, ShippingFreightRuleIdReqModel.class);
        Result<GetConfigVechicleRespModel> result = shippingFreightRuleConfigClient.getConfigVehicle(shippingFreightRuleIdReqModel);
        result.throwException();
        return Result.success(MapperUtils.mapperNoDefault(result.getData(), GetConfigVechicleRespDto.class));
    }




    /**
     * 配置串点运价 v2.42
     * @param requestDtos
     * @return
     */
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @PostMapping(value = "/configStringPoint")
    public Result<Boolean> configStringPoint(@RequestBody @Valid List<ConfigStringPointReqDto> requestDtos) {
        List<ConfigStringPointReqModel> configStringPointReqModels = MapperUtils.mapperNoDefault(requestDtos, ConfigStringPointReqModel.class);
        Result<Boolean> result = shippingFreightRuleConfigClient.configStringPoint(configStringPointReqModels);
        result.throwException();
        return Result.success(true);
    }


}
