package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum AttendancePunchTypeEnum {

    DEFAULT(-1,""),
    NOT_CLOCK_IN(0, "未打卡"),
    ON_DUTY_CLOCK_IN(1, "上班打卡"),
    OFF_DUTY_CLOCK_IN(2, "下班打卡"),
    UPDATE_DUTY_CLOCK_IN(3, "更新打卡"),
    ;

    private Integer key;

    private String value;

    public static AttendancePunchTypeEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst().orElse(DEFAULT);
    }
}
