package com.logistics.management.webapi.controller.freightconfig;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.freightconfig.CarrierFreightConfigRegionClient;
import com.logistics.management.webapi.client.freightconfig.request.region.CarrierFreightConfigRegionAddRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.region.CarrierFreightConfigRegionEditRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.region.CarrierFreightConfigRegionRequestModel;
import com.logistics.management.webapi.client.freightconfig.response.region.CarrierFreightConfigRegionResponseModel;
import com.logistics.management.webapi.controller.freightconfig.request.region.CarrierFreightConfigRegionAddRequestDto;
import com.logistics.management.webapi.controller.freightconfig.request.region.CarrierFreightConfigRegionEditRequestDto;
import com.logistics.management.webapi.controller.freightconfig.request.region.CarrierFreightConfigRegionRequestDto;
import com.logistics.management.webapi.controller.freightconfig.response.region.CarrierFreightConfigRegionResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@Api(tags = "区域计价配置管理")
@RequestMapping(value = "/api/freight/config/region")
public class CarrierFreightConfigRegionController {

    @Resource
    private CarrierFreightConfigRegionClient carrierFreightConfigRegionClient;

    @PostMapping(value = "/detail")
    @ApiOperation(value = "区域计价配置查看", tags = "1.3.5")
    Result<List<CarrierFreightConfigRegionResponseDto>> detail(@Valid @RequestBody CarrierFreightConfigRegionRequestDto requestDto) {
        CarrierFreightConfigRegionRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierFreightConfigRegionRequestModel.class);
        Result<List<CarrierFreightConfigRegionResponseModel>> result = carrierFreightConfigRegionClient.detail(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), CarrierFreightConfigRegionResponseDto.class));
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "区域计价配置新增", tags = "1.3.5")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    Result<Boolean> add(@Valid @RequestBody CarrierFreightConfigRegionAddRequestDto requestDto) {
        CarrierFreightConfigRegionAddRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierFreightConfigRegionAddRequestModel.class);
        // 校验
        requestModel.check();
        return carrierFreightConfigRegionClient.add(requestModel);
    }

    @PostMapping(value = "/edit")
    @ApiOperation(value = "区域计价配置编辑", tags = "1.3.5")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    Result<Boolean> edit(@Valid @RequestBody CarrierFreightConfigRegionEditRequestDto requestDto) {
        CarrierFreightConfigRegionEditRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierFreightConfigRegionEditRequestModel.class);
        requestModel.check();
        return carrierFreightConfigRegionClient.edit(requestModel);
    }
}
