package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.controller.carrierorder.response.TicketsDto;
import com.logistics.management.webapi.client.carrierorder.response.GetTicketsResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

import java.util.Map;


public class CarrierOrderTicketsMapping extends MapperMapping<GetTicketsResponseModel, TicketsDto> {
    private String imagePrefix;
    private Map<String, String> imageMap;
    public CarrierOrderTicketsMapping(String imagePrefix , Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        GetTicketsResponseModel source = getSource();
        TicketsDto destination = getDestination();
        if(StringUtils.isNotBlank(source.getImagePath())){
            destination.setImagePath(imagePrefix+imageMap.get(source.getImagePath()));
        }

    }

}
