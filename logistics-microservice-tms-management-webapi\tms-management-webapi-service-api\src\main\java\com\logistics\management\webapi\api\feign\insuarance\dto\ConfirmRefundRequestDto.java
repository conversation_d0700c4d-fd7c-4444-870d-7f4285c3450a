package com.logistics.management.webapi.api.feign.insuarance.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/12/25 17:53
 */
@Data
public class ConfirmRefundRequestDto {
    @ApiModelProperty(value = "车辆ID")
    @NotBlank(message = "请选择车辆")
    private String vehicleId;
    @ApiModelProperty("退保保险")
    @NotNull(message = "退保保险不能为空")
    private List<InsuranceRefundDto> insuranceRefundList;
    @ApiModelProperty("备注")
    private String remark;
}
