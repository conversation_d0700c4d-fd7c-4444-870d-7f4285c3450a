package com.logistics.management.webapi.controller.region.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/10/18 17:00
 */
@Data
public class ProvinceResponseDto {
    @ApiModelProperty("省份ID")
    private String provinceId = "";
    @ApiModelProperty("省份名称")
    private String provinceName = "";
    @ApiModelProperty("城市ID")
    private String cityId = "";
    @ApiModelProperty("城市名称")
    private String cityName = "";
    @ApiModelProperty("城市行政级别为2（方便前端框架使用）")
    private String cityLevel="2";
}
