package com.logistics.appapi.controller.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class SearchWarehouseRequestDto {

	@ApiModelProperty(value = "发货人地址-发货省",required = true)
	@NotBlank(message = "请选择地址-省")
	private String loadProvinceName;

	@ApiModelProperty(value = "发货人地址-发货市",required = true)
	@NotBlank(message = "请选择地址-市")
	private String loadCityName;

	@ApiModelProperty(value = "发货人地址-发货区",required = true)
	@NotBlank(message = "请选择地址-区")
	private String loadAreaName;

	@ApiModelProperty(value = "发货人地址-发货详细地址",required = true)
	@NotBlank(message = "请输入详细地址")
	private String loadDetailAddress;

	@ApiModelProperty("发货人地址-发货仓库")
	private String loadWarehouse;

	@ApiModelProperty(value = "搜索方式：1 距离优先，2 综合",required = true)
	@NotBlank(message = "请选择搜索方式")
	private String searchType;

	@ApiModelProperty("仓库名称，仓库地址，仓库联系人 搜索条件")
	private String warehouseCondition;
}
