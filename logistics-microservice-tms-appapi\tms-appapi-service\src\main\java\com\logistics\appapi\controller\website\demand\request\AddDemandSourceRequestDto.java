package com.logistics.appapi.controller.website.demand.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/10/31
 * @description:
 */
@Data
public class AddDemandSourceRequestDto {

    @ApiModelProperty(value = "货物名称")
    @Size(min = 2, max = 20, message = "请输入2-20个字符的货物名称")
    private String goodsName;

    @ApiModelProperty(value = "货物吨位")
    @Min(value = 2, message = "请输入大于2的吨位")
    @Max(value = 20000, message = "请输入小于20000的吨位")
    @Pattern(regexp = "^[0-9]{1,5}$",message = "请输入2-50000的整数")
    private String goodsAmount;

    @ApiModelProperty(value = "货物单价")
    @Min(value = 50, message = "请输入大于50的单价")
    @Max(value = 50000, message = "请输入小于50000的单价")
    @Pattern(regexp = "^[0-9]{2,5}$",message = "请输入50-50000的整数")
    private String goodsPrice;

    @ApiModelProperty(value = "联系人名称")
    @Pattern(regexp = "[\\u4E00-\\u9FA5a-zA-Z]{2,20}",message = "请输入联系人姓名，2≤字符长度≤20中文字符")
    private String contactName;

    @ApiModelProperty(value = "联系电话")
    @Pattern(regexp = "[1]\\d{10}",message = "请输入联系人手机号")
    @Size(min = 11,max = 11,message = "请输入联系人手机号")
    private String contactMobile;

    @ApiModelProperty(value = "装货地")
    @Length(min = 9, max = 20, message = "请输入9-20个字符的装货地")
    private String loadAddress;

    @ApiModelProperty(value = "卸货地")
    @Length(min = 9, max = 20, message = "请输入9-20个字符的卸货地")
    private String unloadAddress;

    @ApiModelProperty(value = "uuid",required = true)
    @NotBlank(message = "请输入图片验证码")
    private String uuid;

    @ApiModelProperty(value = "图片验证码",required = true)
    @NotBlank(message = "请输入图片验证码")
    private String pictureVerificationCode;

}
