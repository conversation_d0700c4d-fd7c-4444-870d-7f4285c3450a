package com.logistics.appapi.client.driversafemeeting.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.driversafemeeting.DriverSafeMeetingClient;
import com.logistics.appapi.client.driversafemeeting.request.AppletConfirmLeaningRequestModel;
import com.logistics.appapi.client.driversafemeeting.request.AppletSafeMeetingListRequestModel;
import com.logistics.appapi.client.driversafemeeting.request.DriverSafeMeetingRelationIdRequestModel;
import com.logistics.appapi.client.driversafemeeting.response.AppletSafeMeetingDetailResponseModel;
import com.logistics.appapi.client.driversafemeeting.response.AppletSafeMeetingListResponseModel;
import com.logistics.appapi.client.driversafemeeting.response.DriverSafeMeetingListCountResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/3/11 14:50
 */
@Component
public class DriverSafeMeetingClientHystrix implements DriverSafeMeetingClient {
    @Override
    public Result<PageInfo<AppletSafeMeetingListResponseModel>> appletSafeMeetingList(AppletSafeMeetingListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<DriverSafeMeetingListCountResponseModel> appletSafeMeetingListCount() {
        return Result.timeout();
    }

    @Override
    public Result<AppletSafeMeetingDetailResponseModel> appletSafeMeetingDetail(DriverSafeMeetingRelationIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result appletConfirmLeaning(AppletConfirmLeaningRequestModel requestModel) {
        return Result.timeout();
    }
}
