package com.logistics.management.webapi.client.freightconfig.response.address;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 车主运价细则日志
 *
 * <AUTHOR>
 * @date 2022/9/2 9:45
 */
@Data
public class CarrierFreightConfigAddressLogsResponseModel {

    @ApiModelProperty("操作内容")
    private String operateContents;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("操作人")
    private String operateUserName;

    @ApiModelProperty("时间")
    private Date operateTime;
}
