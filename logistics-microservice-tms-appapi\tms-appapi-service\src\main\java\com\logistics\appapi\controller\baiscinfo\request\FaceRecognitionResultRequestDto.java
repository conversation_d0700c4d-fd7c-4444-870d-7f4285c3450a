package com.logistics.appapi.controller.baiscinfo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/31
 */
@Data
public class FaceRecognitionResultRequestDto {

	@ApiModelProperty(value = "随机字符串 用于查询人脸识别结果", required = true)
	@NotBlank(message = "必要参数不能为空")
	private String orderNo;
}
