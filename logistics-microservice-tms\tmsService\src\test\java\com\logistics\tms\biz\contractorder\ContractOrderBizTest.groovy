package com.logistics.tms.biz.contractorder

import com.logistics.tms.api.feign.contractorder.model.AddOrModifyContractOrderRequestModel
import com.logistics.tms.api.feign.contractorder.model.ContractOrderDetailRequestModel
import com.logistics.tms.api.feign.contractorder.model.ContractOrderDetailResponseModel
import com.logistics.tms.api.feign.contractorder.model.ContractOrderSearchRequestModel
import com.logistics.tms.api.feign.contractorder.model.ContractOrderSearchResponseModel
import com.logistics.tms.api.feign.contractorder.model.TerminateOrCancelContractRequestModel
import com.logistics.tms.base.constant.ConfigKeyConstant
import com.logistics.tms.base.enums.OperateLogsOperateTypeEnum
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TContract
import com.logistics.tms.entity.TOperateLogs
import com.logistics.tms.mapper.TContractFileMapper
import com.logistics.tms.mapper.TContractMapper
import com.logistics.tms.mapper.TOperateLogsMapper
import org.slf4j.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class ContractOrderBizTest extends Specification {
    @Mock
    TContractMapper tContractMapper
    @Mock
    TContractFileMapper tContractFileMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TOperateLogsMapper tOperateLogsMapper
    @Mock
    ConfigKeyConstant configKeyConstant
    @Mock
    Logger log
    @InjectMocks
    ContractOrderBiz contractOrderBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Contract Order List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tContractMapper.searchContractOrderList(any())).thenReturn([new ContractOrderSearchResponseModel()])

        expect:
        contractOrderBiz.searchContractOrderList(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new ContractOrderSearchRequestModel() || [new ContractOrderSearchResponseModel()]
    }

    @Unroll
    def "ge Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tContractMapper.getContractDetail(anyLong())).thenReturn(new ContractOrderDetailResponseModel())

        expect:
        contractOrderBiz.geDetail(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new ContractOrderDetailRequestModel() || new ContractOrderDetailResponseModel()
    }

    @Unroll
    def "save Contract where requestModel=#requestModel"() {
        given:
        when(tContractFileMapper.updateContractFileByContractId(anyLong(), anyString())).thenReturn(0)
        when(tContractFileMapper.batchInsert(any())).thenReturn(0)
        when(commonBiz.getBusinessTypeCode(any(), anyString(), anyString())).thenReturn("getBusinessTypeCodeResponse")
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(commonBiz.getQiyaCompanyName()).thenReturn("getQiyaCompanyNameResponse")
        when(tOperateLogsMapper.batchInsert(any())).thenReturn(0)

        expect:
        contractOrderBiz.saveContract(requestModel)
        assert expectedResult == false

        where:
        requestModel                               || expectedResult
        new AddOrModifyContractOrderRequestModel() || true
    }

    @Unroll
    def "get Contract Status where now=#now and beginTime=#beginTime and endTime=#endTime and source=#source then expect: #expectedResult"() {
        expect:
        ContractOrderBiz.getContractStatus(now, beginTime, endTime, source) == expectedResult

        where:
        now                                                              | beginTime                                                        | endTime                                                          | source   || expectedResult
        new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 11).getTime() | new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 11).getTime() | new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 11).getTime() | "source" || 0
    }

    @Unroll
    def "terminate Or Cancel Contract where requestModel=#requestModel"() {
        expect:
        contractOrderBiz.terminateOrCancelContract(requestModel)
        assert expectedResult == false

        where:
        requestModel                                || expectedResult
        new TerminateOrCancelContractRequestModel() || true
    }

    @Unroll
    def "add Log where remark=#remark and logsEnum=#logsEnum and userName=#userName and objectId=#objectId then expect: #expectedResult"() {
        expect:
        contractOrderBiz.addLog(objectId, logsEnum, remark, userName) == expectedResult

        where:
        remark   | logsEnum                                 | userName   | objectId || expectedResult
        "remark" | OperateLogsOperateTypeEnum.AUDIT_THROUGH | "userName" | 1l       || new TOperateLogs(objectType: 0, objectId: 1l, operateType: 0, operateContents: "operateContents", operateUserName: "operateUserName", operateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 11).getTime(), remark: "remark")
    }

    @Unroll
    def "update Status From Job"() {
        given:
        when(tContractMapper.getValidContract()).thenReturn([new TContract(contractStatus: 0, contractStartTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 11).getTime(), contractEndTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 11).getTime(), endingCancelBy: "endingCancelBy", endingCancelRemark: "endingCancelRemark", endingCancelDatetime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 11).getTime())])
        when(tContractMapper.batchUpdate(any())).thenReturn(0)
        when(tOperateLogsMapper.batchInsert(any())).thenReturn(0)

        expect:
        contractOrderBiz.updateStatusFromJob()
        assert expectedResult == false

        where:
        expectedResult << true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme