package com.logistics.tms.controller.demandorder.request.sinopec;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/12
 */
@Data
public class SinopecFourLevelAddressModel {

	@ApiModelProperty("省份编码")
	private String provinceCode;

	@ApiModelProperty("省份名称")
	private String provinceName;

	@ApiModelProperty("地级市编码")
	private String cityCode;

	@ApiModelProperty("地级名称")
	private String cityName;

	@ApiModelProperty("区县编码")
	private String countyCode;

	@ApiModelProperty("区县名称")
	private String countyName;

	@ApiModelProperty("街道编码")
	private String townCode;

	@ApiModelProperty("街道名称【可并入详细地址展示】")
	private String townName;
}
