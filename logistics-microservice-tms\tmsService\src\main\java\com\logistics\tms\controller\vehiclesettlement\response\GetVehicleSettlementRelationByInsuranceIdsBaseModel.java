package com.logistics.tms.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/11/11
 * @description:
 */
@Data
public class GetVehicleSettlementRelationByInsuranceIdsBaseModel {

    @ApiModelProperty(value = "关系ID")
    private Long id;

    @ApiModelProperty(value = "车辆结算ID")
    private Long vehicleSettlementId;

    @ApiModelProperty(value = "对象ID(保险费用ID)")
    private Long objectId;


}
