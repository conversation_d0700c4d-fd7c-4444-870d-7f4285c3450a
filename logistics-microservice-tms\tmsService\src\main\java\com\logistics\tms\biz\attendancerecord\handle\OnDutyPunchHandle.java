package com.logistics.tms.biz.attendancerecord.handle;

import com.logistics.tms.base.enums.AttendancePunchTypeEnum;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.StaffPropertyEnum;
import com.logistics.tms.biz.attendancerecord.model.AttendanceClockModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TAttendanceRecord;
import com.logistics.tms.mapper.TAttendanceRecordMapper;
import com.logistics.tms.mapper.TStaffBasicMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Date;

@Component
@RequiredArgsConstructor
public class OnDutyPunchHandle implements AttendancePunchHandle {

    private final CommonBiz commonBiz;
    private final TStaffBasicMapper staffBasicMapper;
    private final TAttendanceRecordMapper attendanceRecordMapper;

    /**
     * 上班打卡 filter
     */
    @Override
    public boolean filter(Integer dutyPunchType) {
        return AttendancePunchTypeEnum.ON_DUTY_CLOCK_IN.getKey().equals(dutyPunchType);
    }

    /**
     * 上班打卡 handel
     * @param boModel
     * @return true 成功 false 失败
     */
    @Override
    public boolean handle(AttendanceClockModel boModel) {

        // 判断是否已打卡
        if (AttendancePunchTypeEnum.ON_DUTY_CLOCK_IN.getKey().equals(boModel.getToDayPunchType())) {
            throw new BizException(CarrierDataExceptionEnum.ATTENDANCE_CLOCK_REPEAT);
        }

        // 查询员工信息
        return staffBasicMapper.getStaffByIds(boModel.getStaffId().toString())
                .stream()
                // 过滤出自营自主员工
                .filter(f -> {
                    return StaffPropertyEnum.OWN_STAFF.getKey().equals(f.getStaffProperty()) ||
                            StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(f.getStaffProperty());
                })
                .findFirst()
                .map(staff -> {
                    // 构建打卡Model
                    Date onDutyPunchTime = new Date();
                    TAttendanceRecord record = new TAttendanceRecord();
                    record.setStaffId(staff.getId());
                    record.setStaffName(staff.getName());
                    record.setStaffMobile(staff.getMobile());
                    record.setStaffProperty(staff.getStaffProperty());
                    record.setAttendanceDate(DateUtils.localDate2Date(LocalDate.now()));
                    record.setOnDutyPunchPic(boModel.getDutyPunchPic());
                    record.setOnDutyPunchLocation(boModel.getDutyPunchLocation());
                    record.setOnDutyPunchTime(onDutyPunchTime);
                    commonBiz.setBaseEntityAdd(record, BaseContextHandler.getUserName());
                    // 添加员工考勤记录
                    attendanceRecordMapper.insertSelective(record);
                    return true;
                }).orElseThrow(() -> new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST));
    }
}
