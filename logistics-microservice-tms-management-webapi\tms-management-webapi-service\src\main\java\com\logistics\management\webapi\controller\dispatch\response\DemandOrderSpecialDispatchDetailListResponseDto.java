package com.logistics.management.webapi.controller.dispatch.response;

import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/8/6 10:13
 */
@Data
public class DemandOrderSpecialDispatchDetailListResponseDto {

    /**
     * 需求单货物id
     */
    private String demandOrderGoodsId="";

    /**
     * 需求单id
     */
    private String demandOrderId="";

    /**
     * 需求单号
     */
    private String demandOrderCode="";

    /**
     * 客户单号
     */
    private String customerOrderCode="";

    /**
     * 品名
     */
    private String goodsName="";

    /**
     * 规格
     */
    private String goodsSize="";
    private String length="";
    private String width="";
    private String height="";

    /**
     * 需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨；100 新生回收，101 新生销售
     */
    private String entrustType = "";

    /**
     * 待调度数
     */
    private String notArrangedAmount="";

//    /**
//     * 发货地址
//     */
//    private String loadDetailAddress="";
//
//    /**
//     * 收货地址
//     */
//    private String unloadDetailAddress="";


    /**
     * 发货地仓库 2.42
     */
    private String loadProvinceName;
    /**
     * 发货地市 2.42
     */
    private String loadCityName;
    /**
     * 发货地区 2.42
     */
    private String loadAreaName;
    /**
     * 发货地详细地址  2.42
     */
    private String loadDetailAddress;

    /**
     * 收货地省 2.42
     */
    private String unloadProvinceName;
    /**
     * 收货地市 2.42
     */
    private String unloadCityName;
    /**
     * 收货地区 2.42
     */
    private String unloadAreaName;
    /**
     * 收货地详细地址 2.42
     */
    private String unloadDetailAddress;



    /**
     * 排序
     */
    private String orderNum="";

    /**
     * 到下个点位距离
     */
    private String nextPointDistance="";

}
