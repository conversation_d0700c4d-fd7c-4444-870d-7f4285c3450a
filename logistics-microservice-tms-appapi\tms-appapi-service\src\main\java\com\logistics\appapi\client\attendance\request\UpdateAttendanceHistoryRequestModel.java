package com.logistics.appapi.client.attendance.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UpdateAttendanceHistoryRequestModel {

    @ApiModelProperty(value = "考勤打卡ID", required = true)
    private Long attendanceRecordId;

    @ApiModelProperty(value = "要变更的打卡类型 1: 上班 2:下班", required = true)
    private Integer changeType;

    @ApiModelProperty(value = "要变更的打卡时间", required = true)
    private String changePunchTime;

    @ApiModelProperty(value = "变更原因")
    private String changeReason;

}
