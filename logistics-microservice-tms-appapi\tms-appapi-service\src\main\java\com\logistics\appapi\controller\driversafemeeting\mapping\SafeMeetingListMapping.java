package com.logistics.appapi.controller.driversafemeeting.mapping;

import com.logistics.appapi.base.enums.StudyStatusEnum;
import com.logistics.appapi.base.utils.FrequentMethodUtils;
import com.logistics.appapi.client.driversafemeeting.response.AppletSafeMeetingListResponseModel;
import com.logistics.appapi.controller.driversafemeeting.response.SafeMeetingListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @author: wjf
 * @date: 2019/11/8 13:05
 */
public class SafeMeetingListMapping extends MapperMapping<AppletSafeMeetingListResponseModel,SafeMeetingListResponseDto> {
    @Override
    public void configure() {
        AppletSafeMeetingListResponseModel source = getSource();
        SafeMeetingListResponseDto destination = getDestination();
        if (source != null){
            destination.setPeriod(FrequentMethodUtils.encodeYearMonth(source.getPeriod()));
            destination.setStatusDesc(StudyStatusEnum.getEnum(source.getStatus()).getValue());
            if (source.getCreatedTime() != null){
                destination.setCreatedTime(DateUtils.dateToString(source.getCreatedTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
        }
    }
}
