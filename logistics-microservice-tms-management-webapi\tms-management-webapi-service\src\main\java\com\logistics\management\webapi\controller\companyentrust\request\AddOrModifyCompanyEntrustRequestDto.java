package com.logistics.management.webapi.controller.companyentrust.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2019/9/27 14:24
 */
@Data
public class AddOrModifyCompanyEntrustRequestDto {
    @ApiModelProperty("货主类型: 1公司 2 个人")
    @NotBlank(message = "请选择货主类型")
    private String type;
    @ApiModelProperty("公司id")
    private String companyEntrustId;
    @ApiModelProperty("公司名字")
    @NotBlank(message = "请维护公司名称")
    private String companyName;
    @ApiModelProperty("公司简称")
    private String companyShortName;
    @ApiModelProperty("营业执照路径相对")
    private String fileSrcPathTradingCertificateImage;
    @ApiModelProperty("营业执照有效期")
    private String tradingCertificateValidityTime;
    @ApiModelProperty("营业执照是否永久0否1是")
    private String tradingCertificateIsForever;
    @ApiModelProperty("营业执照是否后补0否1是")
    @NotBlank(message = "请选择是否后补")
    private String tradingCertificateIsAmend;
    @ApiModelProperty("结算吨位")
    @NotBlank(message = "请维护结算吨位")
    private String settlementTonnage;
    @ApiModelProperty("修改车辆是否审核：0 无需审核，1 需")
    @NotBlank(message = "请维护修改车辆是否审核")
    private String ifAudit;
    @ApiModelProperty("签收模式：1 人工签收 2 七个自然日默认签收")
    @NotBlank(message = "请维护签收模式")
    private String signMode;
    @ApiModelProperty("备注")
    private String remark;
}
