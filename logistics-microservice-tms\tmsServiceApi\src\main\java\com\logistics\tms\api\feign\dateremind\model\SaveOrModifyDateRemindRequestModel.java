package com.logistics.tms.api.feign.dateremind.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/5/31 8:55
 */
@Data
public class SaveOrModifyDateRemindRequestModel {
    @ApiModelProperty("日期提醒ID")
    private Long dateRemindId;
    @ApiModelProperty("日期提醒名称")
    private String dateName;
    @ApiModelProperty("是否提醒：0 否，1 是")
    private Integer ifRemind;
    @ApiModelProperty("提醒天数")
    private Integer remindDays;
    @ApiModelProperty("备注")
    private String remark;
}
