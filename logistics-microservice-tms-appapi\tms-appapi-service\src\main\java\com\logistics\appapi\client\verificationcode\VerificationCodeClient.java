package com.logistics.appapi.client.verificationcode;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.verificationcode.hystrix.VerificationCodeHystrix;
import com.logistics.appapi.client.verificationcode.request.VerificationCodeRequestModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2023/10/26 13:55
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/verificationCode",
        fallback = VerificationCodeHystrix.class)
public interface VerificationCodeClient {

    @ApiOperation(value = "获取短信验证码")
    @PostMapping(value = "/getVerifyCode")
    Result<Boolean> getVerifyCode(@RequestBody VerificationCodeRequestModel requestModel);

}
