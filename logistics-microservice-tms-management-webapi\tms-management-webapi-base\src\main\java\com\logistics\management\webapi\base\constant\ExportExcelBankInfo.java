package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/7/12 15:22
 */
public class ExportExcelBankInfo {
    private ExportExcelBankInfo() {

    }

    private static final Map<String, String> EXPORT_BANK_INFO;

    static {
        EXPORT_BANK_INFO = new LinkedHashMap<>();
        EXPORT_BANK_INFO.put("状态", "enableLabel");
        EXPORT_BANK_INFO.put("开户行", "bankName");
        EXPORT_BANK_INFO.put("支行名称","branchName");
        EXPORT_BANK_INFO.put("备注", "remark");
        EXPORT_BANK_INFO.put("添加人", "createdBy");
        EXPORT_BANK_INFO.put("操作人", "lastModifiedBy");
        EXPORT_BANK_INFO.put("操作时间", "lastModifiedTime");

    }

    public static Map<String, String> getExportBankInfo() {
        return EXPORT_BANK_INFO;
    }
}
