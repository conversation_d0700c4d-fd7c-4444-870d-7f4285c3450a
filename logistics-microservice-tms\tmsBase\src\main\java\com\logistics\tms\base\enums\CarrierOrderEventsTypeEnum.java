/**
 * Created by yun.zhou on 2017/10/23.
 */
package com.logistics.tms.base.enums;

public enum CarrierOrderEventsTypeEnum {
    BY_ORDER(10, "承接订单",""),
    DISPATCH_VEHICLE(20, "调度车辆", "调度车辆，生成运单"),
    HAS_CANCELED(30, "已取消","已取消"),
    UPDATE_VEHICLE(35,"修改车辆",""),
    UPDATE_VEHICLE_WAIT_AUDIT(40, "（待审核）修改车辆",""),
    AUDIT_VEHICLES(45, "审核车辆",""),
    UPDATE_VEHICLE_AUDIT(50, "（已审核）修改车辆",""),
    UPDATE_VEHICLE_REJECT(55, "（已驳回）修改车辆",""),
    ARRIVED_PICK_UP(60, "到达提货地","到达提货地"),
    PICK_UP(70, "提货","提货%1$s"),
    SCANNER_PICK_UP(71, "扫码提货","提货%1$s"),
    ARRIVED_UNLOAD(80, "到达卸货地","到达卸货地"),
    UNLOADING(90, "卸货","卸货%1$s"),
    SIGN_IN(100, "签收","签收%1$s"),
    EMPTY(110, "放空","放空"),
    ;
    private Integer key;
    private String value;
    private String format;

    public String format(String... values){
        return String.format(this.getFormat(),values);
    }

    CarrierOrderEventsTypeEnum(Integer key, String value, String format) {
        this.key = key;
        this.value = value;
        this.format = format;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getFormat(){return format;}

}
