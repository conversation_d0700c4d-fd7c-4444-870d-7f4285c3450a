package com.logistics.management.webapi.api.feign.insurancecompany.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: sj
 * @Date: 2019/5/29 14:25
 */
@Data
public class InsuranceCompanyListResponseDto implements Serializable{
    @ApiModelProperty("保险公司ID")
    private String insuranceCompanyId;
    @ApiModelProperty("状态: 0禁用 1启用")
    private String enabled = "";
    @ApiModelProperty("状态文本")
    private String enabledLabel = "";
    @ApiModelProperty("保险公司名称")
    private String companyName = "";
    @ApiModelProperty("添加人姓名")
    private String addUserName = "";
    @ApiModelProperty("修改人")
    private String lastModifiedBy;
    @ApiModelProperty("修改时间")
    private String lastModifiedTime;
    @ApiModelProperty("备注")
    private String remark;
}
