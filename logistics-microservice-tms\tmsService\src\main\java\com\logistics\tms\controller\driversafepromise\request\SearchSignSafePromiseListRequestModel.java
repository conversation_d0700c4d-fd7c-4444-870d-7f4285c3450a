package com.logistics.tms.controller.driversafepromise.request;
import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 签订承诺书列表
 * @Author: sj
 * @Date: 2019/11/4 10:18
 */
@Data
public class SearchSignSafePromiseListRequestModel extends AbstractPageForm<SearchSignSafePromiseListRequestModel> {
    @ApiModelProperty("安全承诺书ID")
    private Long safePromiseId;
    @ApiModelProperty("司机姓名")
    private String staffName;
    @ApiModelProperty("司机电话")
    private String staffMobile;
    @ApiModelProperty(" 签订状态: 0 未签订 1 已签订 ")
    private Integer status;
    @ApiModelProperty("人员机构 1 自主 2外部 3 自营")
    private Integer staffProperty;
}
