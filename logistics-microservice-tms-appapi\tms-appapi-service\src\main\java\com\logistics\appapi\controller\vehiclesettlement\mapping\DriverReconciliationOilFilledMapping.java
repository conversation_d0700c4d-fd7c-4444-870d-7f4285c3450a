package com.logistics.appapi.controller.vehiclesettlement.mapping;

import com.logistics.appapi.client.vehiclesettlement.response.ReconciliationOilFilledDetailResponseModel;
import com.logistics.appapi.controller.vehiclesettlement.response.ReconciliationOilFilledDetailResponseDto;
import com.logistics.appapi.controller.vehiclesettlement.response.ReconciliationOilFilledListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

/**
 * @author：wjf
 * @date：2021/4/25 11:03
 */
public class DriverReconciliationOilFilledMapping extends MapperMapping<ReconciliationOilFilledDetailResponseModel,ReconciliationOilFilledDetailResponseDto> {
    @Override
    public void configure() {
        ReconciliationOilFilledDetailResponseDto destination = getDestination();

        if (ListUtils.isNotEmpty(destination.getOilFilledList())){
            for (ReconciliationOilFilledListResponseDto dto : destination.getOilFilledList()) {
                if (StringUtils.isNotBlank(dto.getOilFilledDate())){
                    dto.setOilFilledDate(DateUtils.dateToString(DateUtils.stringToDate(dto.getOilFilledDate(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
                }
            }
        }
    }
}
