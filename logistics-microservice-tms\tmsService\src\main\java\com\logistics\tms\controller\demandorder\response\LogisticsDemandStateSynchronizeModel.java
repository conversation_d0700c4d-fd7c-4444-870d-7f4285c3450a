package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * liang current user system login name
 * 2018/10/19 current system date
 */
@Data
public class LogisticsDemandStateSynchronizeModel {
    private Long demandOrderId;//需求单id，借用字段，托盘不要
    private String demandCode;
    private Integer demandState;
    private BigDecimal completeBackAmount;
    private String lastModifiedBy;
    @ApiModelProperty(value = "需求类型")
    private Integer type;
    private List<CompleteDemandOrderCarrierOrderBackAmountModel> models;
    private List<CompleteDemandOrderGoodsBackAmountModel> tLogisticsDemandGoodsItemModels;
    @ApiModelProperty(value = "托盘类型对应物流需求单回退数量")
    private List<ProductTypeAndBackAmountModel> ProductTypeModel;
}
