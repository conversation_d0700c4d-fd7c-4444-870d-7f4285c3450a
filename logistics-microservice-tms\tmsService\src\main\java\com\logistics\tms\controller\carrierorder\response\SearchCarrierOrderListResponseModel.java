package com.logistics.tms.controller.carrierorder.response;

import com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel;
import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class SearchCarrierOrderListResponseModel extends AbstractPageForm<SearchCarrierOrderListResponseModel> {
    @ApiModelProperty("运单ID")
    private Long carrierOrderId;
    @ApiModelProperty("10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收")
    private Integer status;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("需求单ID")
    private Long demandOrderId;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机名")
    private String driverName;
    @ApiModelProperty("司机手机")
    private String driverMobile;
    @ApiModelProperty("司机运费价格类型 1 单价 2 一口价")
    private Integer dispatchFreightFeeType;
    @ApiModelProperty("司机运费")
    private BigDecimal dispatchFreightFee;
    @ApiModelProperty("委托费用类型 1 单价 2 一口价")
    private Integer entrustFreightType;
    @ApiModelProperty("委托运费")
    private BigDecimal entrustFreight;
    @ApiModelProperty("签收运费")
    private BigDecimal signFreightFee;
    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;
    @ApiModelProperty("多装多卸加价费用")
    private BigDecimal markupFee;
    @ApiModelProperty("预计承运件数")
    private BigDecimal expectAmount;
    @ApiModelProperty("实际提货件数")
    private BigDecimal loadAmount;
    @ApiModelProperty("实际卸货件数")
    private BigDecimal unloadAmount;
    @ApiModelProperty("签收件数")
    private BigDecimal signAmount;
    @ApiModelProperty("预计到货时间")
    private Date expectArrivalTime;
    @ApiModelProperty("提货人")
    private String consignorName;
    @ApiModelProperty("提货人")
    private String consignorMobile;
    @ApiModelProperty("卸货人")
    private String receiverName;
    @ApiModelProperty("卸货人")
    private String receiverMobile;
    @ApiModelProperty("里程数( KM )")
    private BigDecimal expectMileage;
    @ApiModelProperty("实际提货时间")
    private Date loadTime;
    @ApiModelProperty("实际到货时间")
    private Date unloadTime;
    @ApiModelProperty("实际签收时间")
    private Date signTime;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("调度人")
    private String dispatchUserName;
    @ApiModelProperty("运单生成时间")
    private Date dispatchTime;
    @ApiModelProperty("回单数")
    private Integer carrierOrderTicketsAmount;
    @ApiModelProperty("是否取消")
    private Integer ifCancel;
    @ApiModelProperty("是否放空")
    private Integer ifEmpty;
    @ApiModelProperty("是否异常：0 否，1 是")
    private Integer ifObjection;

    @ApiModelProperty("委托客户公司")
    private String entrustCompany;
    @ApiModelProperty("车主id")
    private Long companyCarrierId;
    @ApiModelProperty("车主公司")
    private String carrierCompany;
    @ApiModelProperty("车主公司类型")
    private Integer companyCarrierType;
    @ApiModelProperty("车主联系人id")
    private Long carrierContactId;
    @ApiModelProperty("车主联系人")
    private String carrierContactName;
    @ApiModelProperty("车主联系人电话")
    private String carrierContactMobile;
    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;

    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;

    @ApiModelProperty("结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer settlementTonnage;

    @ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位'")
    private Integer carrierSettlement;
    @ApiModelProperty("车主结算费用合计")
    private BigDecimal carrierSettlementCostTotal;
    @ApiModelProperty("车主结算数量")
    private BigDecimal carrierSettlementAmount;
    @ApiModelProperty("车主报价类型 1 单价 2 整车价")
    private Integer carrierPaymentPriceType;
    @ApiModelProperty("车主价格：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer carrierPriceType;
    @ApiModelProperty("车主价格")
    private BigDecimal carrierPrice;

    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;

    private List<SearchCarrierOrderListGoodsInfoModel> goodsInfoList;


    @ApiModelProperty("订单来源")
    private Integer demandOrderSource ;
}
