package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.entrustaddress.model.AddOrModifyEntrustAddressRequestModel;
import com.logistics.tms.api.feign.entrustaddress.model.GetAddressByCompanyNameRequestModel;
import com.logistics.tms.api.feign.entrustaddress.model.GetAddressByCompanyNameResponseModel;
import com.logistics.tms.api.feign.entrustaddress.model.SearchEntrustAddressResponseModel;
import com.logistics.tms.entity.TEntrustAddress;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TEntrustAddressMapper extends BaseMapper<TEntrustAddress> {

    TEntrustAddress getByCompanyAndAddress(@Param("params") AddOrModifyEntrustAddressRequestModel requestModel);

    int batchInsert(@Param("list") List<TEntrustAddress> list);

    int batchUpdate(@Param("list") List<TEntrustAddress> list);

    List<GetAddressByCompanyNameResponseModel> getAddressByCompanyNameOrWarehouse(@Param("params") GetAddressByCompanyNameRequestModel requestModel);

    List<SearchEntrustAddressResponseModel> searchList(@Param("addressType")Integer addressType);


}