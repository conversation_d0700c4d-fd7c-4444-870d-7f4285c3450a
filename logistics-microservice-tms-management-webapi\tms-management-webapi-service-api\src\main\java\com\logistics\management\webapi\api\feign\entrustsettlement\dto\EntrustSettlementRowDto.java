package com.logistics.management.webapi.api.feign.entrustsettlement.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EntrustSettlementRowDto {
    @ApiModelProperty("需求单ID")
    private String demandOrderId="";
    @ApiModelProperty("结算ID")
    private String settlementId="";
    @ApiModelProperty("需求单号")
    private String demandOrderCode="";
    @ApiModelProperty("结算状态 0 未收款 1 已收款")
    private String settlementStatus="";
    private String settlementStatusDesc="";
    @ApiModelProperty("委托单状态 1000：待调度 2000部分调度 3000调度完成 4000 待签收 5000 已签收 1已取消")
    private String entrustStatus="";
    @ApiModelProperty("客户单号")
    private String customerOrderCode="";
    @ApiModelProperty("结算数据")
    private String settlementAmount="";
    @ApiModelProperty("报价类型 1 单价 2整车价")
    private String contractPriceType="";
    @ApiModelProperty("结算费用")
    private String settlementCostTotal="";
    @ApiModelProperty("发货地址")
    private String loadDetailAddress="";
    @ApiModelProperty("发货人")
    private String consignorName="";
    private String consignorMobile="";
    @ApiModelProperty("收货地址详细")
    private String unloadDetailAddress="";
    @ApiModelProperty("收货人")
    private String receiverName="";
    private String receiverMobile="";
    @ApiModelProperty("品名")
    private String goodsName="";
    @ApiModelProperty("规格")
    private String goodsSize="";
    @ApiModelProperty("货物单位 1 件 2 吨")
    private String goodsUnit="";
    @ApiModelProperty("委托方")
    private String companyEntrust="";
    @ApiModelProperty("下单时间")
    private String publishTime="";
    @ApiModelProperty("结算时间")
    private String settlementTime="";
    @ApiModelProperty("导出用")
    private String loadCityName;
    private String loadAreaName;
    private String unloadCityName;
    private String unloadAreaName;

    @ApiModelProperty("委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件")
    private String demandOrderSource="";
}
