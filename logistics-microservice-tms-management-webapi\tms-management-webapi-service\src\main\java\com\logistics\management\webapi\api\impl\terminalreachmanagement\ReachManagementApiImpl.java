package com.logistics.management.webapi.api.impl.terminalreachmanagement;

import cn.dev33.satoken.annotation.SaIgnore;
import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.terminalreachmanagement.ReachManagementApi;
import com.logistics.management.webapi.api.feign.terminalreachmanagement.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.terminalreachmanagement.mapping.GetReachManagementDetailMapping;
import com.logistics.management.webapi.api.impl.terminalreachmanagement.mapping.ReachManagementExportMapping;
import com.logistics.management.webapi.api.impl.terminalreachmanagement.mapping.SearchReachManagementListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.utils.ExportUtils;
import com.logistics.tms.api.feign.terminalreachmanagement.ReachManagementServiceApi;
import com.logistics.tms.api.feign.terminalreachmanagement.model.GetReachManagementDetailRequestModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.GetReachManagementDetailResponseModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.SearchReachManagementListRequestModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.SearchReachManagementListResponseModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 触达管理
 */
@RestController
public class ReachManagementApiImpl implements ReachManagementApi {

    @Resource
    private ReachManagementServiceApi reachManagementServiceApi;

    @Resource
    private CommonBiz commonBiz;

    @Autowired
    private ConfigKeyConstant configKeyConstant;

    /**
     * 分页查询触达管理列表
     * @param requestDto
     * @return
     */
    @Override
    @SaIgnore
    public Result<PageInfo<SearchReachManagementListResponseDto>> searchReachManagementList(SearchReachManagementListRequestDto requestDto) {
        SearchReachManagementListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchReachManagementListRequestModel.class);
        Result<PageInfo<SearchReachManagementListResponseModel>> result = reachManagementServiceApi.searchReachManagementList(requestModel);
        result.throwException();
        PageInfo pageInfo = result.getData();
        pageInfo.setList(MapperUtils.mapper(pageInfo.getList(), SearchReachManagementListResponseDto.class, new SearchReachManagementListMapping()));
        return Result.success(pageInfo);
    }

    @Override
    @SaIgnore
    public void reachManagementExport(SearchReachManagementListRequestDto requestDto, HttpServletResponse response) {
        SearchReachManagementListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchReachManagementListRequestModel.class);
        if (StringUtils.isNotEmpty(requestDto.getReachManagementIds())){
            //勾选导出
            String[] split = StringUtils.split(requestDto.getReachManagementIds(), ",");
            List<Long> reachManagementIdList = new ArrayList<>(Arrays.asList(split)).stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            requestModel.setReachManagementIds(reachManagementIdList);
        }
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<SearchReachManagementListResponseModel>> result = reachManagementServiceApi.searchReachManagementList(requestModel);
        result.throwException();

        List<ReachManagementExportResponseDto> list = MapperUtils.mapper(result.getData().getList(), ReachManagementExportResponseDto.class, new ReachManagementExportMapping());
        ExportUtils.exportByYeloExcel(response, list, ReachManagementExportResponseDto.class, "终端客户信息列表");
    }

    /**
     * 触达管理详情
     * @param requestDto
     * @return
     */
    @Override
    @SaIgnore
    public Result<GetReachManagementDetailResponseDto> getReachManagementDetail(GetReachManagementDetailRequestDto requestDto) {
        GetReachManagementDetailRequestModel requestModel = MapperUtils.mapper(requestDto, GetReachManagementDetailRequestModel.class);
        Result<GetReachManagementDetailResponseModel> result = reachManagementServiceApi.getReachManagementDetail(requestModel);
        result.throwException();
        Map<String, List<String>> attachmentMap = result.getData().getAttachmentMap();
        Map<String, String> imageMap = null;
        if (Objects.nonNull(attachmentMap)) {
            List<String> attachmentList = new ArrayList<>(attachmentMap.values())
                    .stream().flatMap(List::stream).collect(Collectors.toList());
            imageMap = commonBiz.batchGetOSSFileUrl(attachmentList);
        }

        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), GetReachManagementDetailResponseDto.class, new GetReachManagementDetailMapping(configKeyConstant.fileAccessAddress, imageMap)));
    }

}
