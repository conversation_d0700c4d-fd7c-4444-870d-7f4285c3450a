<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TReservationOrderItemMapper" >
  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TReservationOrderItem" >
    <foreach collection="recordList" item="item" separator=";">
      insert into t_reservation_order_item
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.reservationOrderId != null" >
          reservation_order_id,
        </if>
        <if test="item.carrierOrderId != null" >
          carrier_order_id,
        </if>
        <if test="item.carrierOrderCode != null" >
          carrier_order_code,
        </if>
        <if test="item.expectAmount != null" >
          expect_amount,
        </if>
        <if test="item.demandOrderEntrustType != null">
          demand_order_entrust_type,
        </if>
        <if test="item.expectedTime != null">
          expected_time,
        </if>
        <if test="item.enabled != null" >
          enabled,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.reservationOrderId != null" >
          #{item.reservationOrderId,jdbcType=BIGINT},
        </if>
        <if test="item.carrierOrderId != null" >
          #{item.carrierOrderId,jdbcType=BIGINT},
        </if>
        <if test="item.carrierOrderCode != null" >
          #{item.carrierOrderCode,jdbcType=VARCHAR},
        </if>
        <if test="item.expectAmount != null" >
          #{item.expectAmount,jdbcType=DECIMAL},
        </if>
        <if test="item.demandOrderEntrustType != null">
          #{item.demandOrderEntrustType,jdbcType=INTEGER},
        </if>
        <if test="item.expectedTime != null">
          #{item.expectedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.enabled != null" >
          #{item.enabled,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

  <update id="batchUpdateSelective" parameterType="com.logistics.tms.entity.TReservationOrderItem" >
    <foreach collection="recordList" item="item" separator=";">
      update t_reservation_order_item
      <set >
        <if test="item.reservationOrderId != null" >
          reservation_order_id = #{item.reservationOrderId,jdbcType=BIGINT},
        </if>
        <if test="item.carrierOrderId != null" >
          carrier_order_id = #{item.carrierOrderId,jdbcType=BIGINT},
        </if>
        <if test="item.carrierOrderCode != null" >
          carrier_order_code = #{item.carrierOrderCode,jdbcType=VARCHAR},
        </if>
        <if test="item.expectAmount != null" >
          expect_amount = #{item.expectAmount,jdbcType=DECIMAL},
        </if>
        <if test="item.demandOrderEntrustType != null">
          demand_order_entrust_type = #{item.demandOrderEntrustType,jdbcType=INTEGER},
        </if>
        <if test="item.expectedTime != null">
          expected_time = #{item.expectedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.enabled != null" >
          enabled = #{item.enabled,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="getWaitSignIn" resultMap="BaseResultMap">
    select
    troi.*
    from t_reservation_order_item troi
    left join t_reservation_order tro on tro.id = troi.reservation_order_id and tro.valid = 1
    where troi.valid = 1
    and troi.carrier_order_id in
    <foreach collection="carrierOrderIdList" item="carrierOrderId" open="(" close=")" separator=",">
      #{carrierOrderId,jdbcType=BIGINT}
    </foreach>
    and tro.status = 10
    and troi.enabled = 1
  </select>


  <select id="getOrderByReservationOrderIds" resultMap="BaseResultMap">
    select
    troi.*
    from t_reservation_order_item troi
    where troi.valid = 1
    and troi.reservation_order_id in
    <foreach collection="list" item="reOrderId" open="(" close=")" separator=",">
      #{reOrderId,jdbcType=BIGINT}
    </foreach>
    and troi.enabled = 1
  </select>

  <select id="getOrderByReservationOrderId" resultType="com.logistics.tms.controller.reservationorder.response.ReservationCarrierOrderListResponseModel">
    select
    troi.carrier_order_id as carrierOrderId,
    troi.carrier_order_code as carrierOrderCode,
    troi.expect_amount as expectAmount,
    troi.demand_order_entrust_type as entrustType,
    troi.expected_time as expectedTime,

    tco.status
    from t_reservation_order_item troi
    left join t_carrier_order tco on tco.id = troi.carrier_order_id and tco.valid = 1
    where troi.valid = 1
    and troi.reservation_order_id = #{reservationOrderId,jdbcType=BIGINT}
    and troi.enabled = 1
  </select>

  <select id="listByCarrierOrderIds" resultMap="BaseResultMap">
  select
  <include refid="Base_Column_List"/>
  from t_reservation_order_item
  where valid = 1
  and carrier_order_id in
  <foreach collection="carrierOrderIds" open="(" close=")" item="item" separator=",">
        #{item,jdbcType=BIGINT}
    </foreach>
</select>

<select id="searchListForDriverWeb" resultMap="BaseResultMap">
select 
<include refid="Base_Column_List"/>
from t_reservation_order_item
where valid = 1
and id in 
<foreach collection="tReservationOrderIds" open="(" close=")" item="item" separator=",">
#{item,jdbcType=BIGINT}
</foreach>
<if test="requestModel.carrierOrderCode != null and requestModel.carrierOrderCode != ''">
and instr(carrier_order_code,#{param2.carrierOrderCode,jdbcType=VARCHAR}) > 0
</if>

</select>


<select id="searchListForManagementWeb" resultMap="BaseResultMap">
select
<include refid="Base_Column_List"/>
from t_reservation_order_item
where valid = 1
and id in
<foreach collection="tReservationOrderIds" open="(" close=")" item="item" separator=",">
#{item,jdbcType=BIGINT}
</foreach>
<if test="requestModel.carrierOrderCode != null and requestModel.carrierOrderCode != ''">
and instr(carrier_order_code,#{param2.carrierOrderCode,jdbcType=VARCHAR}) > 0
</if>
<!--ORDER BY CASE WHEN  = 0 THEN 2 WHEN status = 1 THEN 1 else 0 END desc ,id desc-->
</select>

</mapper>