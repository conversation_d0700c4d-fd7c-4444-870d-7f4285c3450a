package com.logistics.tms.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/5
 */
@Data
public class CarrierOrderConfirmSignUpForLeyiRequestModel {

	@ApiModelProperty("签收时间")
	private Date signTime;

	@Valid
	@NotEmpty(message = "请填写签收信息")
	private List<CarrierOrderSignUpForLeyiRequestModel> signList;
}
