package com.logistics.management.webapi.controller.settlestatement.packaging.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierSettleStatementDetailTotalResponseDto {

    @ApiModelProperty("对账单id")
    private String settleStatementId = "";

    @ApiModelProperty("对账单状态,-2:已撤销 -1:待提交 0:待业务审核 1:待财务审核 2:已对账 3:已驳回")
    private String settleStatementStatus = "";

    @ApiModelProperty("对账单状态文本")
    private String settleStatementStatusLabel = "";

    @ApiModelProperty("对账单名称")
    private String settleStatementName = "";

    @ApiModelProperty("对账月份")
    private String settleStatementMonth = "";

    @ApiModelProperty("车主运费")
    private String carrierFreight = "";

    @ApiModelProperty("运费费点")
    private String freightTaxPoint = "";

    @ApiModelProperty("费额合计")
    private String carrierFreightTotal = "";

    @ApiModelProperty("结算总数量(导出结算使用)")
    private String settlementAmountTotal = "";

    @ApiModelProperty("临时费用")
    private String otherFee = "";

    @ApiModelProperty("临时费用费点")
    private String otherFeeTaxPoint = "";

    @ApiModelProperty("临时费用合计")
    private String otherFeeTotal = "";

    @ApiModelProperty("申请运费总额")
    private String applyFeeTotal = "";

    @ApiModelProperty("调整费用")
    private String adjustFee = "";

    @ApiModelProperty("对账费用")
    private String reconciliationFee = "";

    @ApiModelProperty("对账单号")
    private String settleStatementCode = "";

    @ApiModelProperty("结算主体")
    private String platformCompanyName = "";

    @ApiModelProperty("车主Id")
    private String companyCarrierId = "";

    @ApiModelProperty("车主")
    private String companyCarrierName = "";

    @ApiModelProperty("备注")
    private String remark = "";

    @ApiModelProperty("合同号(导出结算使用)")
    private String contractCode = "";

    @ApiModelProperty("创建时间(导出结算使用)")
    private String createdTime = "";
}
