package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/8/20 14:09
 */
@Data
public class SyncLogisticsDemandOrderInfo {
    @ApiModelProperty("回收单code")
    private String recycleOrderCode;
    @ApiModelProperty("物流单code")
    private String demandOrderCode;
    @ApiModelProperty("操作人")
    private String operator;
}
