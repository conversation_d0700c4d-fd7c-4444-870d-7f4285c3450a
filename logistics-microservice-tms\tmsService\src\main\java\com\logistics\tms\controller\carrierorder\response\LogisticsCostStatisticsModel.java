package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2021/10/28 17:14
 */
@Data
public class LogisticsCostStatisticsModel {
    @ApiModelProperty("签收时间年月")
    private String yearMonth;
    @ApiModelProperty("签收时间年月下的运单")
    private List<LogisticsCostStatisticsOrderModel> orderList;
}
