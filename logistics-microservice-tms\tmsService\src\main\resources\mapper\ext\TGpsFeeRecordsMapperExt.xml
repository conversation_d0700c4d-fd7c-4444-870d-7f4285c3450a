<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TGpsFeeRecordsMapper" >
  <select id="getGpsFeeRecords" resultType="com.logistics.tms.api.feign.gpsfee.model.GpsFeeRecordsListResponseModel">
    select
    vehicle_no as vehicleNo,
    gps_service_provider as gpsServiceProvider,
    service_fee as serviceFee,
    cooperation_period as cooperationPeriod,
    start_date as startDate,
    end_date as endDate,
    finish_date as finishDate,
    remark as remark,
    last_modified_by as lastModifiedBy,
    last_modified_time as lastModifiedTime
    from t_gps_fee_records
    where valid = 1
    and gps_fee_id = #{gpsFeeId,jdbcType=BIGINT}
    order by last_modified_time desc
  </select>
</mapper>