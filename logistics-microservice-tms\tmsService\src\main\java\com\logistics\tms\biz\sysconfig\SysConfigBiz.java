package com.logistics.tms.biz.sysconfig;

import com.alibaba.fastjson.JSONObject;
import com.logistics.tms.base.enums.ConfigKeyEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.sysconfig.request.SysConfigRequestModel;
import com.logistics.tms.controller.sysconfig.response.SysConfigResponseModel;
import com.logistics.tms.entity.TSysConfig;
import com.logistics.tms.mapper.TSysConfigMapper;
import com.yelo.tools.context.BaseContextHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SysConfigBiz {

    @Resource
    private TSysConfigMapper sysConfigMapper;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 根据分组code查询系统配置
     * @param groupCode 分组Code
     * @return <key, value>
     */
    public Map<String, String> getSysConfig(String groupCode) {
        List<TSysConfig> configList = getSysConfigDetail(groupCode);
        if (CollectionUtils.isEmpty(configList)) {
            return Collections.emptyMap();
        }
        return configList.stream()
                .collect(Collectors.toMap(TSysConfig::getConfigKey, TSysConfig::getConfigValue));
    }

    /**
     * 根据分组code查询配置详情
     * @param groupCode 分组Code
     * @return 配置列表
     */
    public List<TSysConfig> getSysConfigDetail(String groupCode) {
        return sysConfigMapper.selectAllByGroupCode(groupCode);
    }

    /**
     * 根据分组code、配置Key查询配置
     * @param groupCode 分组Code
     * @param configKey 配置Key
     * @return Optional<配置Value>
     */
    public Optional<String> getSysConfig(String groupCode, String configKey) {
        Optional<TSysConfig> configOpt = getSysConfigDetail(groupCode, configKey);
        return configOpt.map(TSysConfig::getConfigValue);
    }

    /**
     * 根据分组 default code、配置Key查询配置
     * @param configKeyEnum 配置Key枚举
     * @return Optional<配置Value>
     */
    public Optional<String> getSysConfig(ConfigKeyEnum configKeyEnum) {
        Optional<TSysConfig> configOpt = getSysConfigDetail(configKeyEnum.getGroupCode(), configKeyEnum.getValue());
        return configOpt.map(TSysConfig::getConfigValue);
    }

    /**
     * 将Json转成对象格式
     * @return T 对象
     */
    public <T> T getSysConfig(String groupCode, String configKey, Class<T> clz) {
        String jsonValue = getSysConfig(groupCode, configKey)
                .orElse("");
        try {
            return JSONObject.parseObject(jsonValue, clz);
        } catch (Exception e) {
            log.error("获取系统配置 -> json 转换失败");
            return null;
        }
    }

    /**
     * 批量获取配置
     * @param requestModels 请求Model
     * @return 配置列表
     */
    public List<SysConfigResponseModel> batchGetSysConfig(Collection<SysConfigRequestModel> requestModels) {
        return sysConfigMapper.selectInGroupCodeAndKey(requestModels)
                .stream()
                .map(s -> {
                    SysConfigResponseModel model = new SysConfigResponseModel();
                    model.setGroupCode(s.getGroupCode());
                    model.setConfigKey(s.getConfigKey());
                    model.setConfigValue(s.getConfigValue());
                    return model;
                })
                .collect(Collectors.toList());

    }

    /**
     * 根据分组code、配置Key查询配置详情
     * @param groupCode 分组 Code
     * @param configKey 配置 Key
     * @return 配置详情
     */
    public Optional<TSysConfig> getSysConfigDetail(String groupCode, String configKey) {
        return Optional.ofNullable(sysConfigMapper.selectOnByGroupCodeAndKey(groupCode, configKey));
    }

    /**
     * 编辑对应配置
     * @param groupCode 分组 Code
     * @param configKey 配置 Key
     * @param configValue 配置 Value
     */
    public void editSysConfigValue(String groupCode, String configKey, String configValue) {
        TSysConfig entity = new TSysConfig();
        entity.setGroupCode(groupCode);
        entity.setConfigKey(configKey);
        entity.setConfigValue(configValue);
        editSysConfig(entity);
    }

    /**
     * 修改配置信息
     * @param entity 配置 Entity
     */
    public void editSysConfig(TSysConfig entity) {
        commonBiz.setBaseEntityModify(entity, BaseContextHandler.getUserName());
        sysConfigMapper.updateByGroupAndKey(entity);
    }

}
