package com.logistics.management.webapi.api.feign.extvehiclesettlement.dto;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2019/11/21 13:33
 */
@Data
public class SearchExtVehicleSettlementListRequestDto extends AbstractPageForm<SearchExtVehicleSettlementListRequestDto> {
    @ApiModelProperty("支付状态：0 未支付，1 已支付")
    private String payStatus;
    @ApiModelProperty("运单状态：50000 待签收，60000 已签收，2 已放空")
    private String status;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机")
    private String driver;
    @ApiModelProperty("发货地")
    private String loadAddress;
    @ApiModelProperty("卸货地")
    private String unloadAddress;
    @ApiModelProperty("调度人")
    private String dispatchUser;
    @ApiModelProperty("货主，公司ID，逗号分隔")
    private String companyEntrustIds;
    @ApiModelProperty("品名")
    private String goodsName;
    @ApiModelProperty("规格")
    private String goodsSize;
    @ApiModelProperty("运单生成时间从")
    @NotBlank(message = "请选择运单生成时间")
    private String dispatchTimeFrom;
    @ApiModelProperty("运单生成时间到")
    @NotBlank(message = "请选择运单生成时间")
    private String dispatchTimeTo;

    @ApiModelProperty("多选导出")
    private String ids;
}
