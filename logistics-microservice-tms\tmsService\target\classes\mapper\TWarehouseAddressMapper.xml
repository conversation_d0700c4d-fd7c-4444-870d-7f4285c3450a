<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TWarehouseAddressMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TWarehouseAddress">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_entrust_id" jdbcType="BIGINT" property="companyEntrustId" />
    <result column="company_entrust_name" jdbcType="VARCHAR" property="companyEntrustName" />
    <result column="warehouse" jdbcType="VARCHAR" property="warehouse" />
    <result column="province_id" jdbcType="BIGINT" property="provinceId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_id" jdbcType="BIGINT" property="cityId" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="area_id" jdbcType="BIGINT" property="areaId" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="enabled" jdbcType="INTEGER" property="enabled" />
    <result column="enabled_time" jdbcType="TIMESTAMP" property="enabledTime" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_entrust_id, company_entrust_name, warehouse, province_id, province_name, 
    city_id, city_name, area_id, area_name, enabled, enabled_time, created_by, created_time, 
    last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_warehouse_address
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_warehouse_address
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TWarehouseAddress">
    insert into t_warehouse_address (id, company_entrust_id, company_entrust_name, 
      warehouse, province_id, province_name, 
      city_id, city_name, area_id, 
      area_name, enabled, enabled_time, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{companyEntrustId,jdbcType=BIGINT}, #{companyEntrustName,jdbcType=VARCHAR}, 
      #{warehouse,jdbcType=VARCHAR}, #{provinceId,jdbcType=BIGINT}, #{provinceName,jdbcType=VARCHAR}, 
      #{cityId,jdbcType=BIGINT}, #{cityName,jdbcType=VARCHAR}, #{areaId,jdbcType=BIGINT}, 
      #{areaName,jdbcType=VARCHAR}, #{enabled,jdbcType=INTEGER}, #{enabledTime,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TWarehouseAddress" keyProperty="id" useGeneratedKeys="true">
    insert into t_warehouse_address
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyEntrustId != null">
        company_entrust_id,
      </if>
      <if test="companyEntrustName != null">
        company_entrust_name,
      </if>
      <if test="warehouse != null">
        warehouse,
      </if>
      <if test="provinceId != null">
        province_id,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="areaId != null">
        area_id,
      </if>
      <if test="areaName != null">
        area_name,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="enabledTime != null">
        enabled_time,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="companyEntrustId != null">
        #{companyEntrustId,jdbcType=BIGINT},
      </if>
      <if test="companyEntrustName != null">
        #{companyEntrustName,jdbcType=VARCHAR},
      </if>
      <if test="warehouse != null">
        #{warehouse,jdbcType=VARCHAR},
      </if>
      <if test="provinceId != null">
        #{provinceId,jdbcType=BIGINT},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=BIGINT},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=BIGINT},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=INTEGER},
      </if>
      <if test="enabledTime != null">
        #{enabledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TWarehouseAddress">
    update t_warehouse_address
    <set>
      <if test="companyEntrustId != null">
        company_entrust_id = #{companyEntrustId,jdbcType=BIGINT},
      </if>
      <if test="companyEntrustName != null">
        company_entrust_name = #{companyEntrustName,jdbcType=VARCHAR},
      </if>
      <if test="warehouse != null">
        warehouse = #{warehouse,jdbcType=VARCHAR},
      </if>
      <if test="provinceId != null">
        province_id = #{provinceId,jdbcType=BIGINT},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=BIGINT},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        area_id = #{areaId,jdbcType=BIGINT},
      </if>
      <if test="areaName != null">
        area_name = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=INTEGER},
      </if>
      <if test="enabledTime != null">
        enabled_time = #{enabledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TWarehouseAddress">
    update t_warehouse_address
    set company_entrust_id = #{companyEntrustId,jdbcType=BIGINT},
      company_entrust_name = #{companyEntrustName,jdbcType=VARCHAR},
      warehouse = #{warehouse,jdbcType=VARCHAR},
      province_id = #{provinceId,jdbcType=BIGINT},
      province_name = #{provinceName,jdbcType=VARCHAR},
      city_id = #{cityId,jdbcType=BIGINT},
      city_name = #{cityName,jdbcType=VARCHAR},
      area_id = #{areaId,jdbcType=BIGINT},
      area_name = #{areaName,jdbcType=VARCHAR},
      enabled = #{enabled,jdbcType=INTEGER},
      enabled_time = #{enabledTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>