package com.logistics.management.webapi.client.companyentrust.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/9/27 14:24
 */
@Data
public class AddOrModifyCompanyEntrustRequestModel {
    private Long companyEntrustId;
    private String companyName;
    private String companyShortName;
    private String fileSrcPathTradingCertificateImage;
    private Date tradingCertificateValidityTime;
    private Integer tradingCertificateIsForever;
    private Integer tradingCertificateIsAmend;
    private Integer settlementTonnage;
    private Integer ifAudit;
    private Integer signMode;
    @ApiModelProperty("货主类型: 1 企业 2 个人")
    private Integer type;
    private String remark;
}
