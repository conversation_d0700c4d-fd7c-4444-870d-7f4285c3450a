package com.logistics.management.webapi.api.impl.gpsfee.mapping;

import com.logistics.management.webapi.api.feign.gpsfee.dto.GpsFeeDetailResponseDto;
import com.logistics.management.webapi.base.enums.SettlementStatusEnum;
import com.logistics.tms.api.feign.gpsfee.model.GpsFeeDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @author: wjf
 * @date: 2019/10/9 11:59
 */
public class GpsFeeDetailMapping extends MapperMapping<GpsFeeDetailResponseModel,GpsFeeDetailResponseDto> {
    @Override
    public void configure() {
        GpsFeeDetailResponseModel source = getSource();
        GpsFeeDetailResponseDto destination = getDestination();
        if (source != null){
            destination.setStatus(SettlementStatusEnum.getEnum(source.getStatus()).getValue());
            if (source.getInstallTime() != null){
                destination.setInstallTime(DateUtils.dateToString(source.getInstallTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            destination.setStartDate(DateUtils.dateToString(source.getStartDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            destination.setEndDate(DateUtils.dateToString(source.getEndDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            destination.setFinishDate(DateUtils.dateToString(source.getFinishDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
    }
}
