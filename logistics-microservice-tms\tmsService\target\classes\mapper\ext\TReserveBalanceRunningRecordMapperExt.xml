<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TReserveBalanceRunningRecordMapper" >
  <select id="reserveBalanceDetailGroupMonth" resultType="com.logistics.tms.controller.reservebalance.response.ReserveBalanceDetailResponseModel">
    SELECT DATE_FORMAT(running_date, '%Y-%m') applyMonth,
           IFNULL(SUM(CASE when running_type IN(10, 11) then amount end ), 0) applyAmount,
           IFNULL(SUM(CASE when running_type IN(20, 21, 22) then amount end ), 0) verificationAmount
    FROM `t_reserve_balance_running_record`
    WHERE valid = 1
    AND reserve_balance_id = #{params.reserveBalanceId}
    <if test="params.startMonth != null and params.startMonth != ''">
      <if test="params.endMonth != null and params.endMonth != ''">
        AND DATE_FORMAT(running_date, '%Y-%m') BETWEEN #{params.startMonth} AND #{params.endMonth}
      </if>
    </if>
    GROUP BY DATE_FORMAT(running_date, '%Y-%m')
    ORDER BY running_date DESC
  </select>
</mapper>