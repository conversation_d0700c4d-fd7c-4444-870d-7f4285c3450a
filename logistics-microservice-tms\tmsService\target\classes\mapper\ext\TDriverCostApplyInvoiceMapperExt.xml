<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDriverCostApplyInvoiceMapper">
    <select id="selectByDriverCostApplyId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select
        <include refid="Base_Column_List" />
        from t_driver_cost_apply_invoice
        where valid = 1 and driver_cost_apply_id = #{driverCostApplyId,jdbcType=BIGINT}
    </select>

    <select id="selectByCodeAndNum" resultMap="BaseResultMap">
        select
        tdcai.*
        from t_driver_cost_apply_invoice tdcai
                 left join t_driver_cost_apply tdca on tdcai.driver_cost_apply_id = tdca.id and tdca.valid = 1
        where tdcai.valid = 1
          and tdca.audit_status in (0, 1, 2)
        <choose>
            <when test="list != null and list.size() != 0">
                and (
                <foreach collection="list" item="item" separator="or">
                    (tdcai.invoice_code = #{item.invoiceCode,jdbcType=VARCHAR}
                        and tdcai.invoice_num = #{item.invoiceNum,jdbcType=VARCHAR}
                        and tdcai.type = #{item.type,jdbcType=BIGINT})
                </foreach>
                )
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </select>

    <insert id="batchInsertAndReturnId" useGeneratedKeys="true" keyProperty="id">
        insert into t_driver_cost_apply_invoice (id, driver_cost_apply_id, type,
                                                 invoice_name, invoice_type, invoice_code,
                                                 invoice_num, total_price, total_tax,
                                                 total_tax_and_price, created_by, created_time,
                                                 last_modified_by, last_modified_time, valid)
        values
        <foreach collection="List" item="item" separator=",">
            (
            <choose>
                <when test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.driverCostApplyId != null">
                    #{item.driverCostApplyId,jdbcType=BIGINT},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.type != null">
                    #{item.type,jdbcType=INTEGER},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.invoiceName != null">
                    #{item.invoiceName,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.invoiceType != null">
                    #{item.invoiceType,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.invoiceCode != null">
                    #{item.invoiceCode,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.invoiceNum != null">
                    #{item.invoiceNum,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.totalPrice != null">
                    #{item.totalPrice,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.totalTax != null">
                    #{item.totalTax,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.totalTaxAndPrice != null">
                    #{item.totalTaxAndPrice,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER}
                </when>
                <otherwise>
                   default
                </otherwise>
            </choose>
            )
        </foreach>
    </insert>

    <resultMap id="selectInvoiceInfoByCostApplyIdMap" type="com.logistics.tms.controller.drivercostapply.response.DriverCostApplyInvoiceResponseModel">
        <result column="id" property="invoiceId"/>
        <result column="type" property="type"/>
        <result column="invoice_name" property="invoiceName"/>
        <result column="invoice_type" property="invoiceType"/>
        <result column="invoice_code" property="invoiceCode"/>
        <result column="invoice_num" property="invoiceNum"/>
        <result column="total_price" property="totalPrice"/>
        <result column="total_tax" property="totalTax"/>
        <result column="total_tax_and_price" property="totalTaxAndPrice"/>
        <result column="imagePath" property="imagePath"/>
    </resultMap>

    <select id="selectInvoiceInfoByCostApplyId" resultMap="selectInvoiceInfoByCostApplyIdMap">
        select
        tdcai.id,
        tdcai.type,
        tdcai.invoice_name,
        tdcai.invoice_type,
        tdcai.invoice_code,
        tdcai.invoice_num,
        tdcai.total_price,
        tdcai.total_tax,
        tdcai.total_tax_and_price,
        tcp.file_path imagePath
        from t_driver_cost_apply_invoice tdcai
        left join t_certification_pictures tcp on tcp.valid = 1 and tcp.object_id = tdcai.id and tcp.object_type = 29 and tcp.file_type = 3
        where tdcai.valid = 1
          and tdcai.driver_cost_apply_id = #{driverCostApplyId,jdbcType=BIGINT}
    </select>
</mapper>