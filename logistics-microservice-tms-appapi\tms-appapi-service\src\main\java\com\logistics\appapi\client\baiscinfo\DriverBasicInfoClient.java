package com.logistics.appapi.client.baiscinfo;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.baiscinfo.hystrix.DriverBasicInfoClientHystrix;
import com.logistics.appapi.client.baiscinfo.request.*;
import com.logistics.appapi.client.baiscinfo.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2024/3/11 9:06
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/driverApplet/basicInfo",
        fallback = DriverBasicInfoClientHystrix.class)
public interface DriverBasicInfoClient {

    @ApiOperation(value = "查询司机基础信息")
    @PostMapping(value = "/driverBasicInfo")
    Result<DriverBasicInfoResponseModel> driverBasicInfo();

    @ApiOperation(value = "司机基础信息提交")
    @PostMapping(value = "/driverBasicInfoSubmit")
    Result<Boolean> driverBasicInfoSubmit(@RequestBody DriverBasicInfoSubmitRequestModel requestModel);

    @ApiOperation(value = "个人手机号认证-获取验证码")
    @PostMapping(value = "/getPersonAuthVerifyCode")
    Result<Boolean> getVerifyCode(@RequestBody GetPersonAuthVerifyCodeRequestModel requestModel);

    @ApiOperation(value = "个人二要素校验")
    @PostMapping(value = "/verifyPersonTwoElements")
    Result<VerifyPersonTwoElementsResponseModel> verifyPersonTwoElements(@RequestBody VerifyPersonTwoElementsRequestModel requestModel);

    @ApiOperation(value = "个人三要素校验")
    @PostMapping(value = "/verifyPersonThreeElements")
    Result<VerifyPersonThreeElementsResponseModel> verifyPersonThreeElements(@RequestBody VerifyPersonThreeElementsRequestModel requestModel);

    @ApiOperation(value = "获取刷脸认证签名")
    @PostMapping(value = "/faceRecognition")
    Result<FaceRecognitionResponseModel> faceRecognition(@RequestBody FaceRecognitionRequestModel requestModel);

    @ApiOperation(value = "获取刷脸认证结果")
    @PostMapping(value = "/faceRecognitionResult")
    Result<FaceRecognitionResultResponseModel> faceRecognitionResult(@RequestBody FaceRecognitionResultRequestModel requestModel);
}
