package com.logistics.tms.client.feign.basicdata.ocr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OCRCustomizationIdentifyRequestDto {

    /**
     * 文件的相对路径
     */
    @ApiModelProperty("文件相对路径")
    private String filePath;

    /**
     * 图片iocr模板id
     */
    private String templateId;
}
