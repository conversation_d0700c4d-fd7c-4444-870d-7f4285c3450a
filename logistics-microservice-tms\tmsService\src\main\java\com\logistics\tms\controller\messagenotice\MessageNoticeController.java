package com.logistics.tms.controller.messagenotice;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.messagenotice.MessageNoticeBiz;
import com.logistics.tms.controller.messagenotice.request.ReadMessageNoticeRequestModel;
import com.logistics.tms.controller.messagenotice.request.SearchMessageNoticeListRequestModel;
import com.logistics.tms.controller.messagenotice.response.SearchMessageNoticeListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: wjf
 * @date: 2024/6/5 15:42
 */
@Api(value = "消息通知",tags = "消息通知")
@RestController
@RequestMapping(value = "/service/messageNotice")
public class MessageNoticeController {

    @Resource
    private MessageNoticeBiz messageNoticeBiz;

    /**
     * 消息列表
     *
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/searchList")
    @ApiOperation(value = "消息列表")
    public Result<PageInfo<SearchMessageNoticeListResponseModel>> searchList(@RequestBody SearchMessageNoticeListRequestModel requestModel) {
        return Result.success(messageNoticeBiz.searchList(requestModel));
    }

    /**
     * 置为已读
     *
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/readMessageNotice")
    @ApiOperation(value = "置为已读")
    public Result<Boolean> readMessageNotice(@RequestBody ReadMessageNoticeRequestModel requestModel) {
        messageNoticeBiz.readMessageNotice(requestModel);
        return Result.success(true);
    }

}
