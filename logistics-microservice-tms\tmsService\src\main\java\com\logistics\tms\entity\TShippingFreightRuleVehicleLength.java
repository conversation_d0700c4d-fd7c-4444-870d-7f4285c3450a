package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2024/09/19
*/
@Data
public class TShippingFreightRuleVehicleLength extends BaseEntity {
    /**
    * 零担运价规则id
    */
    @ApiModelProperty("零担运价规则id")
    private Long shippingFreightAddressId;

    /**
    * 车长（米） -1就是零担
    */
    @ApiModelProperty("车长（米） -1就是零担")
    private BigDecimal vehicleLength;

    /**
    * 起始数量
    */
    @ApiModelProperty("起始数量")
    private BigDecimal countStart;

    /**
    * 结束数量
    */
    @ApiModelProperty("结束数量")
    private BigDecimal countEnd;

    /**
    * 单位 1块 2吨 3件(件块整数 吨3位小数)
    */
    @ApiModelProperty("单位 1块 2吨 3件(件块整数 吨3位小数)")
    private Integer unit;

    /**
    * 价格类型 1 单价(元/块，元/吨，元/件)，2 一口价(元)
    */
    @ApiModelProperty("价格类型 1 单价(元/块，元/吨，元/件)，2 一口价(元)")
    private Integer priceType;

    /**
    * 排序
    */
    @ApiModelProperty("排序")
    private Integer sort;

    /**
    * 价格
    */
    @ApiModelProperty("价格")
    private BigDecimal price;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}