package com.logistics.appapi.controller.attendance.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 考勤打卡历史item
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/14
 */
@Data
public class AttendanceHistoryItemDto {

	@ApiModelProperty("考勤打卡ID")
	private String attendanceRecordId = "";

	@ApiModelProperty("考勤日期, 今日 or MM月dd日")
	private String attendanceDate = "";

	@ApiModelProperty("上班打卡时间, pattern: yyyy-MM-dd HH:mm:ss")
	private String onDutyPunchDateTime = "";

	@ApiModelProperty("上班打卡时间, pattern: HH:mm")
	private String onDutyPunchTime = "";

	@ApiModelProperty("上班打卡地点")
	private String onDutyPunchLocation = "";

	@ApiModelProperty("下班打卡时间, pattern: yyyy-MM-dd HH:mm:ss")
	private String offDutyPunchDateTime = "";

	@ApiModelProperty("下班打卡时间, pattern: HH:mm")
	private String offDutyPunchTime = "";

	@ApiModelProperty("下班打卡地点")
	private String offDutyPunchLocation = "";

	@ApiModelProperty("工时")
	private String manHour = "";

	@ApiModelProperty("修改申请按钮状态 0:修改申请 1:撤销申请 2:已过申请时间(隐藏按钮)")
	private String onDutyPunchApply = "";

	@ApiModelProperty("修改申请按钮状态 0:修改申请 1:撤销申请 2:已过申请时间(隐藏按钮)")
	private String offDutyPunchApply = "";
}
