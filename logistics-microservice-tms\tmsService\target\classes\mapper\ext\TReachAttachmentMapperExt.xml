<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TReachAttachmentMapper" >

    <select id="selectAttachmentInfoByReachId" resultMap="BaseResultMap">
      select
        <include refid="Base_Column_List"/>
      from t_reach_attachment
      <where>
        <if test="reachManagementId != null">
          reach_management_id = #{reachManagementId}
        </if>
      </where>
    </select>

    <insert id="batchInsert" >
        <foreach collection="recordList" item="item" separator=";">
            insert into t_reach_attachment
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.reachManagementId != null">
                    reach_management_id,
                </if>
                <if test="item.attachmentType != null">
                    attachment_type,
                </if>
                <if test="item.attachmentPath != null">
                    attachment_path,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.reachManagementId != null">
                    #{item.reachManagementId,jdbcType=BIGINT},
                </if>
                <if test="item.attachmentType != null">
                    #{item.attachmentType,jdbcType=INTEGER},
                </if>
                <if test="item.attachmentPath != null">
                    #{item.attachmentPath,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>
</mapper>