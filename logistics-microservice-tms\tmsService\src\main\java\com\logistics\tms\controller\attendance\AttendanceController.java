package com.logistics.tms.controller.attendance;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.attendancechangeapply.AttendanceChangeApplyBiz;
import com.logistics.tms.biz.attendancerecord.AttendanceRecordBiz;
import com.logistics.tms.controller.attendance.request.*;
import com.logistics.tms.controller.attendance.response.*;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
@Api(value = "考勤中心")
@Slf4j
@RestController
public class AttendanceController {

    @Resource
    private AttendanceRecordBiz attendanceRecordBiz;
    @Resource
    private AttendanceChangeApplyBiz attendanceChangeApplyBiz;

    /**
     * 小程序 - 根据token查询员工当日当卡状态
     * @return AttendanceClockDetailResponseModel
     */
    @ApiOperation(value = "根据登陆人获取打卡状态", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/attendance/attendanceClockDetail")
    public Result<AttendanceClockDetailResponseModel> attendanceClockDetail() {
        return Result.success(attendanceRecordBiz.attendanceClockDetail());
    }

    /**
     * 小程序 - 根据经纬度查询地址
     * @param requestModel
     * @return QueryPathByLonAndLatResponseModel
     */
    @ApiOperation(value = "根据经纬度获取地址", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/attendance/queryPathByLonAndLat")
    public Result<QueryPathByLonAndLatResponseModel> queryPathByLonAndLat(@RequestBody QueryPathByLonAndLatRequestModel requestModel) {
        return Result.success(attendanceRecordBiz.queryPathByLonAndLat(requestModel));
    }

    /**
     * 小程序 - 考勤打卡
     * @param requestModel
     * @return boolean
     */
    @ApiOperation(value = "考勤打卡", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/attendance/attendanceClock")
    public Result<Boolean> attendanceClock(@RequestBody AttendanceClockRequestModel requestModel) {
        return Result.success(attendanceRecordBiz.attendanceClock(requestModel));
    }

    /**
     * 小程序 - 考勤历史（按月份展示）
     * @param requestModel
     * @return AttendanceHistoryListResponseModel
     */
    @ApiOperation(value = "考勤历史列表", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/attendance/attendanceHistoryList")
    public Result<AttendanceHistoryListResponseModel> attendanceHistoryList(@RequestBody AttendanceHistoryListRequestModel requestModel) {
        return Result.success(attendanceRecordBiz.attendanceHistoryList(requestModel));
    }

    /**
     * 小程序 - 修改考勤申请
     * @param requestDto
     * @return boolean
     */
    @ApiOperation(value = "修改考勤打卡", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/attendance/updateAttendanceHistory")
    public Result<Boolean> updateAttendanceHistory(@RequestBody UpdateAttendanceHistoryRequestModel requestDto) {
        return Result.success(attendanceChangeApplyBiz.updateAttendanceHistory(requestDto));
    }

    /**
     * 小程序 - 撤销考勤变更申请
     * @param requestModel
     * @return boolean
     */
    @ApiOperation(value = "修改考勤打卡撤销", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/attendance/cancelUpdateAttendanceHistory")
    public Result<Boolean> cancelUpdateAttendanceHistory(@RequestBody CancelUpdateAttendanceHistoryRequestModel requestModel) {
        return Result.success(attendanceChangeApplyBiz.cancelUpdateAttendanceHistory(requestModel));
    }

    /**
     * 小程序 - 查询考勤变更申请详情
     * @param requestModel
     * @return UpdateAttendanceHistoryDetailResponseModel
     */
    @ApiOperation(value = "修改考勤打卡详情查询", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/attendance/updateAttendanceHistoryDetail")
    public Result<UpdateAttendanceHistoryDetailResponseModel> updateAttendanceHistoryDetail(@RequestBody UpdateAttendanceHistoryDetailRequestModel requestModel) {
        return Result.success(attendanceChangeApplyBiz.updateAttendanceHistoryDetail(requestModel));
    }

    /**
     * 后台 - 考勤打卡列表查询
     * @param requestModel
     * @return pageInfo
     */
    @ApiOperation(value = "考勤打卡列表查询", tags = "1.1.8")
    @PostMapping(value = "/service/attendance/searchAttendanceList")
    public Result<PageInfo<SearchAttendanceListResponseModel>> searchAttendanceList(@RequestBody SearchAttendanceListRequestModel requestModel) {
        return Result.success(attendanceRecordBiz.searchAttendanceList(requestModel));
    }

    /**
     * 后台 - 导出列表
     * @param requestModel
     * @return List<SearchAttendanceListResponseModel>
     */
    @ApiOperation(value = "导出考勤打卡列表", tags = "1.1.8")
    @PostMapping(value = "/service/attendance/exportAttendanceList")
    @IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchAttendanceListResponseModel>> exportAttendanceList(@RequestBody SearchAttendanceListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        PageInfo<SearchAttendanceListResponseModel> searchAttendanceListPageInfo = attendanceRecordBiz.searchAttendanceList(requestModel);
        return Result.success(searchAttendanceListPageInfo.getList());
    }

    /**
     * 后台 - 查询考勤打卡详情
     * @param requestModel
     * @return AttendanceDetailResponseModel
     */
    @ApiOperation(value = "考勤打卡详情查询", tags = "1.1.8")
    @PostMapping(value = "/service/attendance/attendanceDetail")
    public Result<AttendanceDetailResponseModel> attendanceDetail(@RequestBody AttendanceDetailRequestModel requestModel) {
        return Result.success(attendanceRecordBiz.attendanceDetail(requestModel));
    }

    /**
     * 后台 - 考勤变更申请列表
     * @param requestModel
     * @return PageInfo<SearchAttendanceChangeListResponseModel>
     */
    @ApiOperation(value = "考勤打卡变更列表查询", tags = "1.1.8")
    @PostMapping(value = "/service/attendance/searchAttendanceChangeList")
    public Result<PageInfo<SearchAttendanceChangeListResponseModel>> searchAttendanceChangeList(@RequestBody SearchAttendanceChangeListRequestModel requestModel) {
        return Result.success(attendanceChangeApplyBiz.searchAttendanceChangeList(requestModel));
    }

    /**
     * 后台 - 考勤变更列表导出
     * @param requestModel
     * @return List<SearchAttendanceChangeListResponseModel>
     */
    @ApiOperation(value = "导出考勤打卡变更列表", tags = "1.1.8")
    @PostMapping(value = "/service/attendance/exportAttendanceChangeList")
    @IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchAttendanceChangeListResponseModel>> exportAttendanceChangeList(@RequestBody SearchAttendanceChangeListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        PageInfo<SearchAttendanceChangeListResponseModel> searchAttendanceChangePageInfo = attendanceChangeApplyBiz.searchAttendanceChangeList(requestModel);
        return Result.success(searchAttendanceChangePageInfo.getList());
    }

    /**
     * 后台 - 考勤申请变更 详情
     * @param requestModel
     * @return AttendanceChangeDetailResponseModel
     */
    @ApiOperation(value = "考勤打卡变更详情查询", tags = "1.1.8")
    @PostMapping(value = "/service/attendance/attendanceChangeDetail")
    public Result<AttendanceChangeDetailResponseModel> attendanceChangeDetail(@RequestBody AttendanceChangeDetailRequestModel requestModel) {
        return Result.success(attendanceChangeApplyBiz.attendanceChangeDetail(requestModel));
    }

    /**
     * 后台 - 考勤变更申请 审核
     * @param requestModel
     * @return boolean
     */
    @ApiOperation(value = "审核考勤打卡变更申请", tags = "1.1.8")
    @PostMapping(value = "/service/attendance/auditAttendanceChangeApply")
    public Result<Boolean> auditAttendanceChangeApply(@RequestBody AuditAttendanceChangeApplyRequestModel requestModel) {
        return Result.success(attendanceChangeApplyBiz.auditAttendanceChangeApply(requestModel));
    }

    /**
     * 后台 - 考勤变更申请 撤销
     * @param requestModel
     * @return boolean
     */
    @ApiOperation(value = "撤销考勤打卡变更申请", tags = "1.1.8")
    @PostMapping(value = "/service/attendance/cancelAttendanceChangeApply")
    public Result<Boolean> cancelAttendanceChangeApply(@RequestBody CancelAttendanceChangeApplyRequestModel requestModel) {
        return Result.success(attendanceChangeApplyBiz.cancelAttendanceChangeApply(requestModel));
    }
}


