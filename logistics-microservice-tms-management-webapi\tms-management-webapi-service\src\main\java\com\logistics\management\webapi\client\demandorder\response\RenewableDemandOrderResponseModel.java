package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/26
 */
@Data
public class RenewableDemandOrderResponseModel {

	@ApiModelProperty("需求单ID")
	private Long demandId;

	@ApiModelProperty("委托单状态：500待发布 1000待调度 2000部分调度 3000调度完成 4000待签收 5000已签收")
	private Integer status;

	@ApiModelProperty("是否取消 1 是 0 否")
	private Integer ifCancel;

	@ApiModelProperty("需求单号")
	private String demandOrderCode;

	@ApiModelProperty("客户单号")
	private String customerOrderCode;

	@ApiModelProperty("乐橘新生客户名称（企业）")
	private String customerName;
	@ApiModelProperty("乐橘新生客户姓名（个人）")
	private String customerUserName;
	@ApiModelProperty("乐橘新生客户手机号（个人）")
	private String customerUserMobile;

	@ApiModelProperty("客户订单来源：1 乐橘新生客户，2 司机")
	private Integer customerOrderSource;

	@ApiModelProperty("业务类型：1 公司，2 个人")
	private Integer businessType;

	@ApiModelProperty("下单时间")
	private Date publishTime;

	@ApiModelProperty("货物单位：1 件，2 吨")
	private Integer goodsUnit;

	@ApiModelProperty("委托数量")
	private BigDecimal goodsAmount;

	@ApiModelProperty("已安排数量")
	private BigDecimal arrangedAmount;

	@ApiModelProperty("未安排数量")
	private BigDecimal notArrangedAmount;

	@ApiModelProperty("退回数量")
	private BigDecimal backAmount;

	@ApiModelProperty("车主id")
	private Long companyCarrierId;

	@ApiModelProperty("车主")
	private String companyCarrierName;

	@ApiModelProperty("车主公司类型")
	private Integer companyCarrierType;

	@ApiModelProperty("车主联系人")
	private String carrierContactName;

	@ApiModelProperty("车主联系人电话")
	private String carrierContactPhone;

	@ApiModelProperty("发货省")
	private String loadProvinceName;

	@ApiModelProperty("发货市")
	private String loadCityName;

	@ApiModelProperty("发货区")
	private String loadAreaName;

	@ApiModelProperty("发货地址详情")
	private String loadDetailAddress;

	@ApiModelProperty("提货仓库")
	private String loadWarehouse;

	@ApiModelProperty("发货人姓名")
	private String consignorName;

	@ApiModelProperty("发货人手机号")
	private String consignorMobile;

	@ApiModelProperty("期望提货时间")
	private Date expectedLoadTime;

	@ApiModelProperty("收货省")
	private String unloadProvinceName;

	@ApiModelProperty("收货市")
	private String unloadCityName;

	@ApiModelProperty("收货区")
	private String unloadAreaName;

	@ApiModelProperty("收货地址详情")
	private String unloadDetailAddress;

	@ApiModelProperty("卸货仓库")
	private String unloadWarehouse;

	@ApiModelProperty("收货人姓名")
	private String receiverName;

	@ApiModelProperty("收货人手机号")
	private String receiverMobile;

	@ApiModelProperty("期望卸货时间")
	private Date expectedUnloadTime;

	@ApiModelProperty("备注")
	private String remark;

	@ApiModelProperty("需求单货物列表")
	private List<DemandOrderGoodsResponseModel> goodsResponseModels;

	/**
	 * 需求类型 100 新生回收，101新生销售    3.23.0
	 */
	private Integer entrustType ;

}
