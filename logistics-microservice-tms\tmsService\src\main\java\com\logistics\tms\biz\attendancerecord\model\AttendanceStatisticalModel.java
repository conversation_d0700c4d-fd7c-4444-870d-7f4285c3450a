package com.logistics.tms.biz.attendancerecord.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AttendanceStatisticalModel {

    @ApiModelProperty("员工id")
    private Long staffId;

    @ApiModelProperty("总工时")
    private BigDecimal totalManHour;

    @ApiModelProperty("总上班打卡次数")
    private Integer totalOnDutyPunch;

    @ApiModelProperty("总下班打卡次数")
    private Integer totalOffDutyPunch;
}
