package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2024/06/20
*/
@Data
public class TDemandOrder extends BaseEntity {
    /**
    * 委托单状态：500待发布 600竞价中 1000待调度 2000部分调度 3000调度完成 4000待签收 5000已签收
    */
    @ApiModelProperty("委托单状态：500待发布 600竞价中 1000待调度 2000部分调度 3000调度完成 4000待签收 5000已签收")
    private Integer entrustStatus;

    /**
    * 状态更新时间
    */
    @ApiModelProperty("状态更新时间")
    private Date statusUpdateTime;

    /**
    * 委托单状态：500待发布 600竞价中 1000待调度 2000部分调度 3000调度完成
    */
    @ApiModelProperty("委托单状态：500待发布 600竞价中 1000待调度 2000部分调度 3000调度完成")
    private Integer status;

    /**
    * 是否取消 1 是 0 否
    */
    @ApiModelProperty("是否取消 1 是 0 否")
    private Integer ifCancel;

    /**
    * 取消类型：1 我司原因，2 客户原因，3 不可抗力，4 物流原因，5 沟通问题
    */
    @ApiModelProperty("取消类型：1 我司原因，2 客户原因，3 不可抗力，4 物流原因，5 沟通问题")
    private Integer cancelType;

    /**
    * 取消时间
    */
    @ApiModelProperty("取消时间")
    private Date cancelTime;

    /**
    * 取消原因
    */
    @ApiModelProperty("取消原因")
    private String cancelReason;

    /**
    * 委托单来源：1 乐医托盘委托单，2 委托方发单，3 中石化系统，4 扬巴邮件，5 乐橘新生
    */
    @ApiModelProperty("委托单来源：1 乐医托盘委托单，2 委托方发单，3 中石化系统，4 扬巴邮件，5 乐橘新生")
    private Integer source;

    /**
    * 委托单号
    */
    @ApiModelProperty("委托单号")
    private String demandOrderCode;

    /**
    * 业务类型：1 公司，2 个人
    */
    @ApiModelProperty("业务类型：1 公司，2 个人")
    private Integer businessType;

    /**
    * 乐橘新生客户名称（企业）
    */
    @ApiModelProperty("乐橘新生客户名称（企业）")
    private String customerName;

    /**
    * 乐橘新生客户姓名（个人）
    */
    @ApiModelProperty("乐橘新生客户姓名（个人）")
    private String customerUserName;

    /**
    * 乐橘新生客户手机号(加密)（个人）
    */
    @ApiModelProperty("乐橘新生客户手机号(加密)（个人）")
    private String customerUserMobile;

    /**
    * 客户订单来源：1 乐橘新生客户，2 司机
    */
    @ApiModelProperty("客户订单来源：1 乐橘新生客户，2 司机")
    private Integer customerOrderSource;

    /**
    * 客户单号
    */
    @ApiModelProperty("客户单号")
    private String customerOrderCode;

    /**
    * 需求生成人
    */
    @ApiModelProperty("需求生成人")
    private String publishName;

    /**
    * 下单人手机号(加密)
    */
    @ApiModelProperty("下单人手机号(加密)")
    private String publishMobile;

    /**
    * 下单时间
    */
    @ApiModelProperty("下单时间")
    private Date publishTime;

    /**
    * 下单人部门code
    */
    @ApiModelProperty("下单人部门code")
    private String publishOrgCode;

    /**
    * 下单人部门名称
    */
    @ApiModelProperty("下单人部门名称")
    private String publishOrgName;

    /**
    * 凭证日期
    */
    @ApiModelProperty("凭证日期")
    private Date ticketTime;

    /**
    * 货主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位
    */
    @ApiModelProperty("货主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer settlementTonnage;

    /**
    * 车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位'
    */
    @ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位'")
    private Integer carrierSettlement;

    /**
    * 委托数量
    */
    @ApiModelProperty("委托数量")
    private BigDecimal goodsAmount;

    /**
    * 已安排数量
    */
    @ApiModelProperty("已安排数量")
    private BigDecimal arrangedAmount;

    /**
    * 未安排数量
    */
    @ApiModelProperty("未安排数量")
    private BigDecimal notArrangedAmount;

    /**
    * 退回数量
    */
    @ApiModelProperty("退回数量")
    private BigDecimal backAmount;

    /**
    * 差异数，默认null，区分0和空
    */
    @ApiModelProperty("差异数，默认null，区分0和空")
    private BigDecimal differenceAmount;

    /**
    * 云仓异常数
    */
    @ApiModelProperty("云仓异常数")
    private BigDecimal abnormalAmount;

    /**
    * 预计合同价
    */
    @ApiModelProperty("预计合同价")
    private BigDecimal expectContractPrice;

    /**
    * 预计合同价类型：1 单价(元/吨，元/件)，2 一口价(元)
    */
    @ApiModelProperty("预计合同价类型：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer expectContractPriceType;

    /**
    * 合同价类型：1 单价(元/吨，元/件)，2 一口价(元)
    */
    @ApiModelProperty("合同价类型：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer contractPriceType;

    /**
    * 合同价
    */
    @ApiModelProperty("合同价")
    private BigDecimal contractPrice;

    /**
    * 货物单位：1 件，2 吨，3 方，4 块
    */
    @ApiModelProperty("货物单位：1 件，2 吨，3 方，4 块")
    private Integer goodsUnit;

    /**
    * 委托方公司id
    */
    @ApiModelProperty("委托方公司id")
    private Long companyEntrustId;

    /**
    * 委托方公司名称
    */
    @ApiModelProperty("委托方公司名称")
    private String companyEntrustName;

    /**
    * 上游客户
    */
    @ApiModelProperty("上游客户")
    private String upstreamCustomer;

    /**
    * 车主公司类型：1 公司，2 个人
    */
    @ApiModelProperty("车主公司类型：1 公司，2 个人")
    private Integer companyCarrierType;

    /**
    * 车主ID
    */
    @ApiModelProperty("车主ID")
    private Long companyCarrierId;

    /**
    * 车主公司名称
    */
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName;

    /**
    * 车主联系人id
    */
    @ApiModelProperty("车主联系人id")
    private Long carrierContactId;

    /**
    * 车主账号名称
    */
    @ApiModelProperty("车主账号名称")
    private String carrierContactName;

    /**
    * 车主账号手机号（原长度50）
    */
    @ApiModelProperty("车主账号手机号（原长度50）")
    private String carrierContactPhone;

    /**
    * 车主公司级别：1 云途，2 二级承运商
    */
    @ApiModelProperty("车主公司级别：1 云途，2 二级承运商")
    private Integer companyCarrierLevel;

    /**
    * 已调车数(包含已取消)
    */
    @ApiModelProperty("已调车数(包含已取消)")
    private Integer dispatchVehicleCount;

    /**
    * 委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨；100 新生回收，101 新生销售
    */
    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨；100 新生回收，101 新生销售")
    private Integer entrustType;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 车主价格：1 单价(元/吨，元/件)，2 一口价(元)
    */
    @ApiModelProperty("车主价格：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer carrierPriceType;

    /**
    * 车主价格
    */
    @ApiModelProperty("车主价格")
    private BigDecimal carrierPrice;

    /**
    * 是否加急：0 否，1 是
    */
    @ApiModelProperty("是否加急：0 否，1 是")
    private Integer ifUrgent;

    /**
    * 调度时效（天）
    */
    @ApiModelProperty("调度时效（天）")
    private Integer dispatchValidity;

    /**
    * 是否逾期：0 否，1 是
    */
    @ApiModelProperty("是否逾期：0 否，1 是")
    private Integer ifOverdue;

    /**
    * 是否放空：0 否，1 是
    */
    @ApiModelProperty("是否放空：0 否，1 是")
    private Integer ifEmpty;

    /**
    * 是否回退：0 否，1 是
    */
    @ApiModelProperty("是否回退：0 否，1 是")
    private Integer ifRollback;

    /**
    * 回退原因类型一级：2 客户原因，3 不可抗力，4 物流原因，5 沟通问题，6 其他原因
    */
    @ApiModelProperty("回退原因类型一级：2 客户原因，3 不可抗力，4 物流原因，5 沟通问题，6 其他原因")
    private Integer rollbackCauseType;

    /**
    * 回退原因类型二级：201 重复上报，202 更换签收单抬头，203 数据报错，重新下单，204 地址原因，205 等待问题，206 托盘占用，207 流向核对，208 不配合装车，209 客户临时有事，210 现场电话联系不上；301 恶劣天气，302 政府管制，303 修路，304 洪涝；401 回收不及时，402 车辆已满载；501 重复下单，502 操作不规范；601 物流提货现场并单
    */
    @ApiModelProperty("回退原因类型二级：201 重复上报，202 更换签收单抬头，203 数据报错，重新下单，204 地址原因，205 等待问题，206 托盘占用，207 流向核对，208 不配合装车，209 客户临时有事，210 现场电话联系不上；301 恶劣天气，302 政府管制，303 修路，304 洪涝；401 回收不及时，402 车辆已满载；501 重复下单，502 操作不规范；601 物流提货现场并单")
    private Integer rollbackCauseTypeTwo;

    /**
    * 回退备注
    */
    @ApiModelProperty("回退备注")
    private String rollbackRemark;

    /**
    * 周末是否可上门：0 空，1 是，2 否
    */
    @ApiModelProperty("周末是否可上门：0 空，1 是，2 否")
    private Integer availableOnWeekends;

    /**
    * 装卸方: 0 空，1 我司装卸，2 客户装卸
    */
    @ApiModelProperty("装卸方: 0 空，1 我司装卸，2 客户装卸")
    private Integer loadingUnloadingPart;

    /**
    * 装卸费用
    */
    @ApiModelProperty("装卸费用")
    private BigDecimal loadingUnloadingCharge;

    /**
    * 回收任务类型：1 日常回收，2 加急或节假日回收
    */
    @ApiModelProperty("回收任务类型：1 日常回收，2 加急或节假日回收")
    private Integer recycleTaskType;

    /**
    * 地推任务单号
    */
    @ApiModelProperty("地推任务单号")
    private String groundPushTaskCode;

    /**
    * 项目标签（多个标签,拼接）：1 石化板块，2 轮胎板块，3 涂料板块，4 其他
    */
    @ApiModelProperty("项目标签（多个标签,拼接）：1 石化板块，2 轮胎板块，3 涂料板块，4 其他")
    private String projectLabel;

    /**
    * 中石化下单类型：20 拉取，21 推送
    */
    @ApiModelProperty("中石化下单类型：20 拉取，21 推送")
    private Integer orderType;

    /**
    * 中石化订单号
    */
    @ApiModelProperty("中石化订单号")
    private String sinopecOrderNo;

    /**
    * 生产企业
    */
    @ApiModelProperty("生产企业")
    private String manufacturerName;

    /**
    * 物料运输组
    */
    @ApiModelProperty("物料运输组")
    private String itemTransGroupName;

    /**
    * 包装规格
    */
    @ApiModelProperty("包装规格")
    private String itemPackSpecName;

    /**
    * 调度员姓名
    */
    @ApiModelProperty("调度员姓名")
    private String dispatcherName;

    /**
    * 调度员电话
    */
    @ApiModelProperty("调度员电话")
    private String dispatcherPhone;

    /**
    * 是否异常：0 否，1 是
    */
    @ApiModelProperty("是否异常：0 否，1 是")
    private Integer ifObjection;

    /**
    * 是否异常（中石化推送单子）：0 否，1 是
    */
    @ApiModelProperty("是否异常（中石化推送单子）：0 否，1 是")
    private Integer ifObjectionSinopec;

    /**
    * 租户Id
    */
    @ApiModelProperty("租户Id")
    private Long sinopecCustomerId;

    /**
    * 租户名称
    */
    @ApiModelProperty("租户名称")
    private String sinopecCustomerName;

    /**
    * 是否网货 0: 否 1: 是
    */
    @ApiModelProperty("是否网货 0: 否 1: 是")
    private Integer sinopecOnlineGoodsFlag;

    /**
    * 中间件LBS单号
    */
    @ApiModelProperty("中间件LBS单号")
    private String lbsCode;

    /**
    * 固定需求唯一code
    */
    @ApiModelProperty("固定需求唯一code")
    private String fixedDemand;

    /**
    * 是否自动发布：0 否，1 是
    */
    @ApiModelProperty("是否自动发布：0 否，1 是")
    private Integer autoPublish;

    /**
    * 接单模式：1 指定车主，2 竞价抢单
    */
    @ApiModelProperty("接单模式：1 指定车主，2 竞价抢单")
    private Integer orderMode;

    /**
    * 车长id
    */
    @ApiModelProperty("车长id")
    private Long vehicleLengthId;

    /**
    * 车长（米）
    */
    @ApiModelProperty("车长（米）")
    private BigDecimal vehicleLength;

    /**
     * 是否按码回收 0：否 1：是
     */
    @ApiModelProperty("是否按码回收 0：否 1：是")
    private Integer ifRecycleByCode;

    /**
     * 是否按码回收 0：否 1：是
     */
    @ApiModelProperty("是否是额外补的需求单0：否1：是")
    private Integer ifExtDemandOrder;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}