package com.logistics.tms.api.impl.platformcompany;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.platformcompany.PlatformCompanyServiceApi;
import com.logistics.tms.api.feign.platformcompany.model.*;
import com.logistics.tms.biz.platformcompany.PlatformCompanyBiz;
import com.yelo.tray.core.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/11/11 17:46
 */
@RestController
public class PlatformCompanyServiceApiImpl implements PlatformCompanyServiceApi {

    @Autowired
    private PlatformCompanyBiz platformCompanyBiz;

    /**
     * 结算主体列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<SearchPlatformCompanyListResponseModel>> searchPlatformCompanyList(@RequestBody SearchPlatformCompanyListRequestModel requestModel) {
        return Result.success(platformCompanyBiz.searchPlatformCompanyList(requestModel));
    }

    /**
     * 新增结算主体
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> addPlatformCompany(@RequestBody AddPlatformCompanyRequestModel requestModel) {
        platformCompanyBiz.addPlatformCompany(requestModel);
        return Result.success(true);
    }

    /**
     * 删除结算主体
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> delPlatformCompany(@RequestBody DelPlatformCompanyRequestModel requestModel) {
        platformCompanyBiz.delPlatformCompany(requestModel);
        return Result.success(true);
    }

    /**
     * 查询结算主体(下拉列表使用)
     * @param requestModel
     * @return
     */
    @Override
    public Result<List<PlatformCompanySelectListResponseModel>> platformCompanySelectList(@RequestBody PlatformCompanySelectListRequestModel requestModel) {
        return Result.success(platformCompanyBiz.platformCompanySelectList(requestModel));
    }
}
