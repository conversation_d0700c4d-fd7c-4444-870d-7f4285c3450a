package com.logistics.management.webapi.base.enums;

public enum SafeGroupMeetingSeasonEnum {
    ONE(1,"第一季度"),
    TWO(2,"第二季度"),
    THREE(3,"第三季度"),
    FOUR(4,"第四季度"),
    NULL(0,""),;

    private Integer key;
    private String value;

    SafeGroupMeetingSeasonEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }
    public String getValue() {
        return value;
    }

    public static SafeGroupMeetingSeasonEnum getEnum(Integer key) {
        for (SafeGroupMeetingSeasonEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return NULL;
    }

}
