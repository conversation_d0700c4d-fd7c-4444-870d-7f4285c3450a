package com.logistics.tms.controller.driveraccount.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/27
 */
@Data
public class SearchDriverAccountResponseModel {

	@ApiModelProperty(value = "司机账户ID")
	private Long driverAccountId;

	@ApiModelProperty(value = "机构")
	private Integer driverProperty;

	@ApiModelProperty(value = "司机名称")
	private String driverName;

	@ApiModelProperty(value = "司机手机号")
	private String driverMobile;

	@ApiModelProperty(value = "银行账号")
	private String bankAccount;

	@ApiModelProperty(value = "银行名称")
	private String bankAccountName;

	@ApiModelProperty(value = "支行名称 ")
	private String braBankName;

	@ApiModelProperty(value = "银行卡图片数量")
	private Integer bankImageNumber;

	@ApiModelProperty(value = "新增人")
	private String createdBy;

	@ApiModelProperty(value = "操作人")
	private String operateBy;

	@ApiModelProperty(value = "操作时间")
	private String operateDateTime;
}
