package com.logistics.tms.api.impl.terminalreachmanagement;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.terminalreachmanagement.ReachManagementServiceApi;
import com.logistics.tms.api.feign.terminalreachmanagement.model.GetReachManagementDetailRequestModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.GetReachManagementDetailResponseModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.SearchReachManagementListRequestModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.SearchReachManagementListResponseModel;
import com.logistics.tms.biz.terminalreachmanagement.ReachManagementBiz;
import com.yelo.tray.core.base.Result;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class ReachManagementServiceApiImpl implements ReachManagementServiceApi {

    @Resource
    private ReachManagementBiz reachManagementBiz;

    /**
     * 分页查询页面列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<SearchReachManagementListResponseModel>> searchReachManagementList(SearchReachManagementListRequestModel requestModel) {
        return Result.success(reachManagementBiz.searchReachManagementList(requestModel));
    }

    /**
     * 查询页面详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<GetReachManagementDetailResponseModel> getReachManagementDetail(GetReachManagementDetailRequestModel requestModel) {
        return Result.success(reachManagementBiz.getReachManagementDetail(requestModel));
    }

}
