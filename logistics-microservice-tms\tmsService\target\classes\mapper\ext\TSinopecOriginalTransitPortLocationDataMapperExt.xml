<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TSinopecOriginalTransitPortLocationDataMapper">

    <insert id="batchInsertSelective">
        <foreach collection="list" item="item" separator=";">
            insert into t_sinopec_original_transit_port_location_data
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.sinopecOriginalDataId != null">
                    sinopec_original_data_id,
                </if>
                <if test="item.locationType != null">
                    location_type,
                </if>
                <if test="item.locationCode != null">
                    location_code,
                </if>
                <if test="item.locationName != null">
                    location_name,
                </if>
                <if test="item.provinceCode != null">
                    province_code,
                </if>
                <if test="item.provinceName != null">
                    province_name,
                </if>
                <if test="item.cityCode != null">
                    city_code,
                </if>
                <if test="item.cityName != null">
                    city_name,
                </if>
                <if test="item.countyCode != null">
                    county_code,
                </if>
                <if test="item.countyName != null">
                    county_name,
                </if>
                <if test="item.townCode != null">
                    town_code,
                </if>
                <if test="item.townName != null">
                    town_name,
                </if>
                <if test="item.detailAddress != null">
                    detail_address,
                </if>
                <if test="item.latitude != null">
                    latitude,
                </if>
                <if test="item.longitude != null">
                    longitude,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.sinopecOriginalDataId != null">
                    #{item.sinopecOriginalDataId,jdbcType=BIGINT},
                </if>
                <if test="item.locationType != null">
                    #{item.locationType,jdbcType=INTEGER},
                </if>
                <if test="item.locationCode != null">
                    #{item.locationCode,jdbcType=VARCHAR},
                </if>
                <if test="item.locationName != null">
                    #{item.locationName,jdbcType=VARCHAR},
                </if>
                <if test="item.provinceCode != null">
                    #{item.provinceCode,jdbcType=VARCHAR},
                </if>
                <if test="item.provinceName != null">
                    #{item.provinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.cityCode != null">
                    #{item.cityCode,jdbcType=VARCHAR},
                </if>
                <if test="item.cityName != null">
                    #{item.cityName,jdbcType=VARCHAR},
                </if>
                <if test="item.countyCode != null">
                    #{item.countyCode,jdbcType=VARCHAR},
                </if>
                <if test="item.countyName != null">
                    #{item.countyName,jdbcType=VARCHAR},
                </if>
                <if test="item.townCode != null">
                    #{item.townCode,jdbcType=VARCHAR},
                </if>
                <if test="item.townName != null">
                    #{item.townName,jdbcType=VARCHAR},
                </if>
                <if test="item.detailAddress != null">
                    #{item.detailAddress,jdbcType=VARCHAR},
                </if>
                <if test="item.latitude != null">
                    #{item.latitude,jdbcType=VARCHAR},
                </if>
                <if test="item.longitude != null">
                    #{item.longitude,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>
</mapper>