package com.logistics.management.webapi.controller.biddingorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchBiddingDemandResponseDto {

    /**
     * 需求单id
     */
    @ApiModelProperty("需求单id")
    private String demandOrderId= "";

    /**
     * 需求单号
     */
    @ApiModelProperty("需求单号")
    private String demandOrderCode = "";

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private String goodsCount = "";

    /**
     * 路线
     */
    @ApiModelProperty("路线")
    private String route = "";





}
