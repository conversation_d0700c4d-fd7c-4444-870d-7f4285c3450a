package com.logistics.tms.controller.demandorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


@Data
public class WebDemandOrderSearchRequestModel extends AbstractPageForm<WebDemandOrderSearchRequestModel> {


    @ApiModelProperty("需求单状态委托单状态：空 全部 500待发布 1000待调度 2000部分调度 3000调度完成 1取消 3 待评价 4 已评价")
    private Integer demandStatus;
    @ApiModelProperty("委托方公司")
    private String companyEntrustName;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("期望提货时间开始")
    private String expectedLoadTimeStart;
    @ApiModelProperty("期望提货时间结束")
    private String expectedLoadTimeEnd;
    @ApiModelProperty("期望卸货时间开始")
    private String expectedUnloadTimeStart;
    @ApiModelProperty("期望卸货时间结束")
    private String expectedUnloadTimeEnd;
    @ApiModelProperty("下单时间开始")
    private String publishTimeStart;
    @ApiModelProperty("下单时间结束")
    private String publishTimeEnd;
    @ApiModelProperty("请输入品名")
    private String goodsName;
    @ApiModelProperty("请输入规格")
    private String goodsSize;
    @ApiModelProperty("请输入起点")
    private String loadDetailAddress;
    @ApiModelProperty("请输入卸点")
    private String unloadDetailAddress;
    @ApiModelProperty("提卸货联系人")
    private String consignorMobileAndReceiver;
    @ApiModelProperty("拼单助手需求单号")
    private List<String> demandOrderCodeList;
    @ApiModelProperty("拼单助手客户单号")
    private List<String> customerOrderCodeList;


    @ApiModelProperty("排序字段")
    private String sort;
    @ApiModelProperty("顺序 asc 升序 desc 倒序")
    private String order;

    private Date serverTime;

    @ApiModelProperty("需求单id拼接")
    private String demandOrderIds;


    private String receiverName;
    @ApiModelProperty("委托人")
    private String customerEntrustName;
    @ApiModelProperty("需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;

    @ApiModelProperty("回收任务类型：1 日常回收，2 加急或节假日回收")
    private Integer recycleTaskType;

    @ApiModelProperty("是否是额外补的需求单0：否1：是 v2.6.8")
    private String ifExtDemandOrder;

    //需要排除的需求单id
    private List<Long> excludeDemandIdList;
}
