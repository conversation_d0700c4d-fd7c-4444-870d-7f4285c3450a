package com.logistics.tms.base.enums;

/**
 * liang current user system login name
 * 2018/9/26 current system date
 */
public enum DemandOrderStatusEnum {

    DEFAULT(0,""),
    CANCEL_DISPATCH(1,"已取消"),
    ORDER_EMPTY(2, "已放空"),
    ROLLBACK(3, "已回退"),
    WAIT_PUBLISH(500,"待发布"),
    BIDDING(600, "报价中"),
    WAIT_DISPATCH(1000,"待调度"),
    PART_DISPATCH(2000,"部分调度"),
    COMPLETE_DISPATCH(3000,"调度完成"),
    WAIT_SIGN_DISPATCH(4000,"待签收"),
    SIGN_DISPATCH(5000,"已签收"),
    ;

    private Integer key;
    private String value;

    DemandOrderStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static DemandOrderStatusEnum getEnum(Integer key) {
        for (DemandOrderStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
