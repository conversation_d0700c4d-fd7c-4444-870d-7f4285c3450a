package com.logistics.tms.base.enums;

/**
 * @Author: wjf
 * @Date: 2019/11/20 15:21
 */
public enum VehicleSettlementStatusEnum {

    NOT_PAYMENT(0, "未支付"),
    PAYMENT(1, "已支付"),
    ;
    private Integer key;
    private String value;

    VehicleSettlementStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
