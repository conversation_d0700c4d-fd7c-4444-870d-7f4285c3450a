package com.logistics.management.webapi.controller.vehiclesettlement.mapping;

import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.client.vehiclesettlement.response.CancelVehicleSettlementDetailResponseModel;
import com.logistics.management.webapi.controller.vehiclesettlement.response.CancelVehicleSettlementDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;
import lombok.Data;

import java.util.Optional;

/**
 * @author:lei.zhu
 * @date:2021/4/12 11:58
 */
@Data
public class CancelVehicleSettlementDetailMapping extends MapperMapping<CancelVehicleSettlementDetailResponseModel, CancelVehicleSettlementDetailResponseDto> {
    private ConfigKeyConstant configKeyConstant;
    public CancelVehicleSettlementDetailMapping(ConfigKeyConstant configKeyConstant){
        this.configKeyConstant=configKeyConstant;
    }
    @Override
    public void configure() {
        CancelVehicleSettlementDetailResponseModel source = getSource();
        CancelVehicleSettlementDetailResponseDto destination = getDestination();
        if(StringUtils.isNotEmpty(source.getCommitImageUrl())){
            destination.setCommitImageUrlSrc(configKeyConstant.fileAccessAddress+source.getCommitImageUrl());
        }
        destination.setDriverName(Optional.ofNullable(source.getDriverName()).orElse("")+" "+Optional.ofNullable(source.getDriverPhone()).orElse(""));
    }
}
