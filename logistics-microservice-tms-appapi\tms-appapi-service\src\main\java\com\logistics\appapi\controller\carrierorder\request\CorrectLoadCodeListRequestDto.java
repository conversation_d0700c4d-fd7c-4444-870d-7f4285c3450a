package com.logistics.appapi.controller.carrierorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CorrectLoadCodeListRequestDto extends AbstractPageForm<CorrectLoadCodeListRequestDto> {

    @ApiModelProperty(value = "运单Id", required = true)
    @NotBlank(message = "运单id不能为空")
    private String carrierOrderId;

}
