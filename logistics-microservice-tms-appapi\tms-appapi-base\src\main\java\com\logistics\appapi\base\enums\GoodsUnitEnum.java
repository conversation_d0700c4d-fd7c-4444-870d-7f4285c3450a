package com.logistics.appapi.base.enums;


public enum GoodsUnitEnum {
    DEFAULT(0," ","",""),
    BY_PACKAGE(1,"（元/件）","件","元/件"),
    BY_WEIGHT(2,"（元/吨）","吨","元/吨"),
    BY_VOLUME(3,"（元/方）","件","元/方"),
    BY_PIECE(4,"（元/块）","块","元/块"),

    ;

    private Integer key;
    private String value;
    private String unit;
    private String priceUnit;

    GoodsUnitEnum(Integer key, String value, String unit, String priceUnit) {
        this.key = key;
        this.value = value;
        this.unit = unit;
        this.priceUnit = priceUnit;
    }

    public Integer getKey() {
        return key;
    }

    public String getUnit() {
        return unit;
    }

    public String getValue() {
        return value;
    }

    public String getPriceUnit() {
        return priceUnit;
    }

    public static GoodsUnitEnum getEnum(Integer key) {
        for (GoodsUnitEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return DEFAULT;
    }
}
