<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TParkingFeeRecordsMapper" >

  <select id="getByParkingFeeId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_parking_fee_records
    where valid = 1
    and parking_fee_id = #{parkingFeeId,jdbcType=BIGINT}
    order by last_modified_time desc
  </select>

</mapper>