package com.logistics.management.webapi.controller.reserveapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Data
public class ReserveApplyDetailRequestDto {

	@ApiModelProperty(value = "申请记录id", required = true)
	@NotBlank(message = "请选择要查看的记录")
	private String applyId;
}
