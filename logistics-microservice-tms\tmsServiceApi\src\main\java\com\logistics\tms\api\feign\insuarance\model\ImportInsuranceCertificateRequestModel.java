package com.logistics.tms.api.feign.insuarance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/6/17 9:19
 */
@Data
public class ImportInsuranceCertificateRequestModel {
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("保险起始年份")
    private Integer startTime;
    @ApiModelProperty("保险结束年份")
    private Integer endTime;
    @ApiModelProperty("保险类型：1 商业险保单，2 交强险保单，3 个人意外险保单,4 货物险保单，5 承运人险保单")
    private Integer insuranceType;
    private String filePath;
}
