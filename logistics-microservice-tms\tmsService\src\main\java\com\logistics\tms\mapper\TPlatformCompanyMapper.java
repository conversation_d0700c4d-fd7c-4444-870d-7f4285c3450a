package com.logistics.tms.mapper;

import com.logistics.tms.api.feign.platformcompany.model.PlatformCompanySelectListRequestModel;
import com.logistics.tms.api.feign.platformcompany.model.PlatformCompanySelectListResponseModel;
import com.logistics.tms.api.feign.platformcompany.model.SearchPlatformCompanyListRequestModel;
import com.logistics.tms.api.feign.platformcompany.model.SearchPlatformCompanyListResponseModel;
import com.logistics.tms.entity.TPlatformCompany;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2022/11/10
*/
@Mapper
public interface TPlatformCompanyMapper extends BaseMapper<TPlatformCompany> {

    List<SearchPlatformCompanyListResponseModel> searchPlatformCompanyList(@Param("params") SearchPlatformCompanyListRequestModel requestModel);

    TPlatformCompany getByName(@Param("companyName")String companyName);

    List<PlatformCompanySelectListResponseModel> platformCompanySelectList(@Param("params") PlatformCompanySelectListRequestModel requestModel);

}