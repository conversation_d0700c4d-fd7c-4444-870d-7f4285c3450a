package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * @author: wjf
 * @date: 2024/1/29 9:31
 */
@Getter
@AllArgsConstructor
public enum WorkGroupSourceEnum {
    DEFAULT(0, ""),
    CREATE(1, "新建群聊"),
    EXIST(2, "已有群聊"),
    ;

    private final Integer key;
    private final String value;

    public static WorkGroupSourceEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
