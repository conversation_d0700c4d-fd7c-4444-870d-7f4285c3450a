package com.logistics.tms.controller.drivercostapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/3 13:43
 */
@Data
public class SearchCostApplyListResponseModel {

    @ApiModelProperty("司机费用申请表id")
    private Long driverCostApplyId;

    @ApiModelProperty("审核状态：0 待审核，1 已审核，2 已驳回，3 已撤销")
    private Integer auditStatus;

    @ApiModelProperty("司机姓名")
    private String staffName;

    @ApiModelProperty("司机手机号")
    private String staffMobile;

    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("费用类型：100 住宿费，101 装卸费，102 其他费用，103 加班费，104 劳保费，105 电话费，106 交通费， 107 打印费，108 叉车费，109 盖雨布， 110破包赔偿，111 交通罚款；200 维修费，201 车辆保养费，202 过路过桥费，203 停车费，204 尿素费，205 加油费；300 核酸检测费，301 医疗防护用品；400 扣款")
    private Integer costType;

    @ApiModelProperty("申请费用")
    private BigDecimal applyCost;

    @ApiModelProperty("发生时间")
    private Date occurrenceTime;

    @ApiModelProperty("申请时间")
    private Date applyTime;

    @ApiModelProperty("关联油卡")
    private String associatedOilCard;

    @ApiModelProperty("审核时间")
    private Date auditTime;

    @ApiModelProperty("审核人")
    private String auditorName;

    @ApiModelProperty("关联备用金单号")
    private List<String> reserveCodeList;

    @ApiModelProperty("垫付费用（元）; 1.3.6 新增")
    private BigDecimal advanceCosts;

    @ApiModelProperty("发票: 0 无票, 1 有票; 1.3.6 新增")
    private String invoice;

    @ApiModelProperty("发票文本; 1.3.6 新增")
    private String invoiceLabel;

    @ApiModelProperty("单号; 1.3.6 新增")
    private String costApplyCode;

}
