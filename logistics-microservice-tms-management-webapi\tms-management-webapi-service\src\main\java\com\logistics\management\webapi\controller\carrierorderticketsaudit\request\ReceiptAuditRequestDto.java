package com.logistics.management.webapi.controller.carrierorderticketsaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Data
public class ReceiptAuditRequestDto {

    @ApiModelProperty(value = "回单审核Id")
    @NotBlank(message = "回单审核Id不允许为空")
    private String receiptAuditId;

    @ApiModelProperty(value = "审核结果; 1 审核通过，2 驳回")
    @NotBlank(message = "审核结果不允许为空; ")
    private String auditStatus;

    // 通过非必填，驳回必填，不限字符，长度：0/1<=长度<=300
    @ApiModelProperty(value = "备注")
    @Length(max = 300, message = "备注输入长度为 1 ~ 300 字符")
    private String remark;
}
