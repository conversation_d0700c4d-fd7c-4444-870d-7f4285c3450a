package com.logistics.tms.mapper;

import com.logistics.tms.entity.TCarrierOrderAddress;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TCarrierOrderAddressMapper extends BaseMapper<TCarrierOrderAddress>{

    int batchInsertSelective(@Param("list") List<TCarrierOrderAddress> list);

    int batchUpdate(@Param("list") List<TCarrierOrderAddress> list);

    List<TCarrierOrderAddress> getByCarrierOrderIds(@Param("orderIds") String orderIds);

    List<Long> waitCorrectStatisticsAddress(@Param("loadCityIdList")List<Long> loadCityIdList);

    List<TCarrierOrderAddress> getByIds(@Param("ids")String listToString);

    TCarrierOrderAddress getByCarrierOrderId(@Param("carrierOrderId") Long carrierOrderId);
}