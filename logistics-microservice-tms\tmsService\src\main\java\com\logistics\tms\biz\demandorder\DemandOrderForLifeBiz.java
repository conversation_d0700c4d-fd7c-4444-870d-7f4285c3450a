package com.logistics.tms.biz.demandorder;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.demandorder.model.CompanyCarrierByIdModel;
import com.logistics.tms.controller.carrierorder.request.GetCarrierOrdersByDemandIdRequestModel;
import com.logistics.tms.controller.carrierorder.response.DemandOrderCarrierDetailResponseModel;
import com.logistics.tms.controller.demandorder.request.*;
import com.logistics.tms.controller.demandorder.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.logistics.tms.rabbitmq.consumer.model.SyncLifeDemandOrderGoodsModel;
import com.logistics.tms.rabbitmq.consumer.model.SyncLifeDemandOrderMessage;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.logistics.tms.rabbitmq.publisher.model.DemandOrderListToYeloLifeModel;
import com.logistics.tms.rabbitmq.publisher.model.DemandOrderToYeloLifeModel;
import com.logistics.tms.rabbitmq.publisher.model.SyncDemandOrderToYeloLifeModel;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/26
 */
@Slf4j
@Service
public class DemandOrderForLifeBiz {

	@Resource
	private CommonBiz commonBiz;
	@Resource
	private TDemandOrderMapper tDemandOrderMapper;
	@Resource
	private CarrierOrderCommonBiz carrierOrderCommonBiz;
	@Resource
	private TDemandOrderGoodsMapper tDemandOrderGoodsMapper;
	@Resource
	private TDemandReceivementMapper tDemandReceivementMapper;
	@Resource
	private TDemandOrderAddressMapper tDemandOrderAddressMapper;
	@Resource
	private DemandOrderCommonBiz demandOrderCommonBiz;
	@Resource
	private TCompanyEntrustMapper tCompanyEntrustMapper;
	@Resource
	private TDemandOrderOperateLogsMapper tDemandOrderOperateLogsMapper;
	@Resource
	private TDemandOrderCarrierMapper tDemandOrderCarrierMapper;
	@Resource
	private TDemandOrderOperateLogsMapper demandOrderOperateLogsMapper;
	@Resource
	private TDemandOrderEventsMapper demandOrderEventsMapper;
	@Resource
	private TDemandOrderGoodsMapper demandOrderGoodsMapper;
	@Resource
	private RabbitMqPublishBiz rabbitMqPublishBiz;


	/**
	 * 新生需求单列表
	 * @param requestModel
	 * @return
	 */
	public PageInfo<RenewableDemandOrderResponseModel> renewableDemandOrderList(RenewableDemandOrderRequestModel requestModel, boolean isExport) {
		//如果下单开始时间跟下单结束时间为空的话，默认为当前-60
		if (StringUtils.isBlank(requestModel.getPublishStartTime()) && StringUtils.isBlank(requestModel.getPublishEndTime())) {
			requestModel.setPublishEndTime(DateUtils.dateToString(new Date(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
			Calendar now = Calendar.getInstance();
			now.add(Calendar.DAY_OF_MONTH, -60);
			requestModel.setPublishStartTime(DateUtils.dateToString(now.getTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
		}
		processSortParams(requestModel);
		if (!isExport) {
			requestModel.enablePaging();
		}
		List<Long> idList = tDemandOrderMapper.searchYeloLifeDemandOrderListIds(requestModel);
		PageInfo page = new PageInfo(idList);
		if (ListUtils.isNotEmpty(idList)) {
			String demandOrderIds = StringUtils.listToString(idList, ',');
			//查询需求单信息
			List<RenewableDemandOrderResponseModel> demandOrderResponseModels = tDemandOrderMapper.searchYeloLifeDemandOrderList(requestModel, demandOrderIds);
			page.setList(demandOrderResponseModels);
		}
		return page;
	}

	//入参排序转换
	private void processSortParams(RenewableDemandOrderRequestModel requestModel) {
		String sort = requestModel.getSort();
		String order = requestModel.getOrder();
		if ("desc".equals(order) || "asc".equals(order)) {
			requestModel.setOrder(order);
		} else {
			requestModel.setOrder("asc");
		}

		if ("notArrangedAmount".equals(sort)) {
			requestModel.setSort("(tdo.not_arranged_amount)");
		} else if ("expectedLoadTime".equals(sort)) {
			requestModel.setSort("(tdoa.expected_load_time)");
		} else if ("expectedUnloadTime".equals(sort)) {
			requestModel.setSort("(tdoa.expected_unload_time)");
		} else if ("publishTime".equals(sort)) {
			requestModel.setSort("(tdo.publish_time)");
		} else {
			requestModel.setSort(null);
			requestModel.setOrder(null);
		}
	}

	/**
	 * 新生需求单列表统计
	 *
	 * @param requestModel 筛选条件
	 * @return 统计TAB
	 */
	public RenewableDemandListStatisticsResponseModel getRenewableDemandOrderListStatistics(RenewableDemandOrderRequestModel requestModel) {
		if (StringUtils.isBlank(requestModel.getPublishStartTime()) && StringUtils.isBlank(requestModel.getPublishEndTime())) {
			requestModel.setPublishEndTime(DateUtils.dateToString(new Date(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
			Calendar now = Calendar.getInstance();
			now.add(Calendar.DAY_OF_MONTH, -60);
			requestModel.setPublishStartTime(DateUtils.dateToString(now.getTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
		}
		return tDemandOrderMapper.searchYeloLifeDemandOrderStatisticsListIds(requestModel);
	}

	/**
	 * 新生需求单详情
	 *
	 * @param requestModel 需求单id
	 * @return 需求单详情
	 */
	public RenewableDemandOrderDetailResponseModel renewableDemandOrderDetail(DemandOrderDetailRequestModel requestModel) {
		RenewableDemandOrderDetailResponseModel demandOrderDetail = tDemandOrderMapper.renewableDemandOrderDetail(requestModel);
		//需求单是否存在
		if (demandOrderDetail == null) {
			throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
		}

		//一个需求单下对应多个运单
		GetCarrierOrdersByDemandIdRequestModel getCarrierOrdersByDemandIdRequestModel = new GetCarrierOrdersByDemandIdRequestModel();
		getCarrierOrdersByDemandIdRequestModel.setDemandId(requestModel.getDemandId());
		List<DemandOrderCarrierDetailResponseModel> carrierOrdersModels = carrierOrderCommonBiz.getCarrierOrderInfoByDemandId(getCarrierOrdersByDemandIdRequestModel);

		demandOrderDetail.setCarrierResponseModel(MapperUtils.mapper(carrierOrdersModels, DemandOrderCarrierResponseModel.class));

		List<DemandOrderGoodsResponseModel> tDemandOrderGoodsByDemandId = tDemandOrderGoodsMapper.getTDemandOrderGoodsByDemandId(demandOrderDetail.getDemandId());
		demandOrderDetail.setGoodsResponseModel(tDemandOrderGoodsByDemandId);

		//查询需求单货主结算信息
		List<TDemandReceivement> demandReceivementList = tDemandReceivementMapper.getByDemandOrderIds(ConverterUtils.toString(demandOrderDetail.getDemandId()));
		if (ListUtils.isNotEmpty(demandReceivementList)) {
			TDemandReceivement tDemandReceivement = demandReceivementList.get(CommonConstant.INTEGER_ZERO);
			demandOrderDetail.setSettlementAmount(tDemandReceivement.getSettlementAmount());
			demandOrderDetail.setSettlementPriceType(tDemandReceivement.getPriceType());
			demandOrderDetail.setSettlementCostTotal(tDemandReceivement.getSettlementCostTotal());
		}
		return demandOrderDetail;
	}

	/**
	 * 新生同步需求单到tms系统
	 * @param message
	 */
	public void SaveDemandOrderFromYeloLife(SyncLifeDemandOrderMessage message){
		//判断需求单是否存在
		TDemandOrder dbDemandOrder = tDemandOrderMapper.getByCode(message.getDemandOrderCode());
		if (dbDemandOrder != null){
			throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_EXIST);
		}
		//查询新生公司
		String yeloLifeCompanyName = commonBiz.getYeloLifeCompanyName();
		TCompanyEntrust dbCompanyEntrust = tCompanyEntrustMapper.getByName(yeloLifeCompanyName);
		if (dbCompanyEntrust == null) {
			throw new BizException(EntrustDataExceptionEnum.COMPANY_ENTRUST_EMPTY);
		}

		String userName = message.getOperatorName();
		Date now = new Date();

		//获取货物总数量
		BigDecimal goodsAmount = BigDecimal.ZERO;
		for (SyncLifeDemandOrderGoodsModel model : message.getGoodsModels()) {
			goodsAmount = goodsAmount.add(model.getGoodsAmount());
		}


		//新增需求单
		TDemandOrder tDemandOrder = MapperUtils.mapper(message, TDemandOrder.class);
		tDemandOrder.setStatus(DemandOrderStatusEnum.WAIT_PUBLISH.getKey());
		tDemandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_PUBLISH.getKey());
		tDemandOrder.setStatusUpdateTime(now);
		tDemandOrder.setSource(DemandOrderSourceEnum.YELO_LIFE.getKey());
		tDemandOrder.setCustomerOrderSource(RenewableSourceTypeEnum.YELOLIFE_SYNC.getKey());
		tDemandOrder.setPublishName(message.getPublishUserName());
		tDemandOrder.setPublishMobile(message.getPublishUserMobile());
		tDemandOrder.setSettlementTonnage(dbCompanyEntrust.getSettlementTonnage());
		tDemandOrder.setGoodsAmount(goodsAmount);
		tDemandOrder.setNotArrangedAmount(goodsAmount);
		//货主信息
		tDemandOrder.setCompanyEntrustId(dbCompanyEntrust.getId());
		tDemandOrder.setCompanyEntrustName(yeloLifeCompanyName);
		tDemandOrder.setIfRecycleByCode(message.getIfRecycleByCode());
		commonBiz.setBaseEntityAdd(tDemandOrder, userName, now);
		tDemandOrderMapper.insertSelectiveEncrypt(tDemandOrder);


		//新增需求单地址
		TDemandOrderAddress tDemandOrderAddress = MapperUtils.mapper(message.getAddressModel(), TDemandOrderAddress.class);
		tDemandOrderAddress.setDemandOrderId(tDemandOrder.getId());
		commonBiz.setBaseEntityAdd(tDemandOrderAddress, userName, now);
		tDemandOrderAddressMapper.insertSelective(tDemandOrderAddress);


		//新增需求单货物
		TDemandOrderGoods demandOrderGoods;
		List<TDemandOrderGoods> demandOrderGoodsList = new ArrayList<>();
		for (SyncLifeDemandOrderGoodsModel model : message.getGoodsModels()) {
			//组装货物信息
			demandOrderGoods = new TDemandOrderGoods();
			demandOrderGoods.setDemandOrderId(tDemandOrder.getId());
			demandOrderGoods.setSkuCode(model.getSkuCode());
			demandOrderGoods.setGoodsAmount(model.getGoodsAmount());
			demandOrderGoods.setNotArrangedAmount(demandOrderGoods.getGoodsAmount());
			demandOrderGoods.setGoodsName(model.getGoodsName());
			commonBiz.setBaseEntityAdd(demandOrderGoods, userName, now);
			demandOrderGoodsList.add(demandOrderGoods);
		}
		if (ListUtils.isNotEmpty(demandOrderGoodsList)) {
			tDemandOrderGoodsMapper.batchInsert(demandOrderGoodsList);
		}

		//生成需求单日志
		TDemandOrderOperateLogs demandOrderOperateLogs = demandOrderCommonBiz.getDemandOrderOperateLogs(tDemandOrder.getId(), DemandOrderOperateLogsEnum.CREATE_DEMAND_ORDER, userName, "");
		tDemandOrderOperateLogsMapper.insertSelective(demandOrderOperateLogs);

		//异步查询地址经纬度
		AsyncProcessQueue.execute(() -> demandOrderCommonBiz.updateAddressLonAndLat(Collections.singletonList(tDemandOrderAddress)));
	}

	/**
	 * 需求单批量发布详情(新生)
	 */
	public List<LifeBatchPublishDetailResponseModel> publishDetail(LifeBatchPublishDetailRequestModel requestModel) {
		//查询需求单
		List<TDemandOrder> tDemandOrders = tDemandOrderMapper.getByIds(requestModel.getDemandIds());
		if (ListUtils.isEmpty(tDemandOrders)) {
			throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
		}
		if (tDemandOrders.size() > CommonConstant.INTEGER_TEN) {
			throw new BizException(EntrustDataExceptionEnum.PUBLISH_DEMAND_ORDER_MAX);
		}
		//校验
		this.checkPublishDetail(tDemandOrders);

		//查询需求单地址
		List<TDemandOrderAddress> tDemandOrderAddresses = tDemandOrderAddressMapper.getByDemandOrderIds(requestModel.getDemandIds());
		Map<Long, TDemandOrderAddress> tDemandOrderAddressesMap = tDemandOrderAddresses.stream().collect(Collectors.toMap(TDemandOrderAddress::getDemandOrderId, Function.identity()));

		//组装返回数据
		List<LifeBatchPublishDetailResponseModel> responseModels = new ArrayList<>();
		for (TDemandOrder tDemandOrder : tDemandOrders) {
			TDemandOrderAddress tDemandOrderAddress = tDemandOrderAddressesMap.get(tDemandOrder.getId());
			if (tDemandOrderAddress == null) {
				throw new BizException(EntrustDataExceptionEnum.ADDRESS_EMPTY);
			}

			LifeBatchPublishDetailResponseModel responseModel = new LifeBatchPublishDetailResponseModel();
			responseModel.setCompanyEntrustName(tDemandOrder.getCompanyEntrustName());
			responseModel.setDemandId(tDemandOrder.getId());
			responseModel.setDemandOrderCode(tDemandOrder.getDemandOrderCode());
			responseModel.setGoodsAmount(tDemandOrder.getGoodsAmount());
			responseModel.setGoodsUnit(tDemandOrder.getGoodsUnit());
			responseModel.setRemark(tDemandOrder.getRemark());

			responseModel.setLoadAreaName(tDemandOrderAddress.getLoadAreaName());
			responseModel.setLoadCityName(tDemandOrderAddress.getLoadCityName());
			responseModel.setLoadDetailAddress(tDemandOrderAddress.getLoadDetailAddress());
			responseModel.setLoadProvinceName(tDemandOrderAddress.getLoadProvinceName());
			responseModel.setLoadWarehouse(tDemandOrderAddress.getLoadWarehouse());
			responseModel.setUnloadAreaName(tDemandOrderAddress.getUnloadAreaName());
			responseModel.setUnloadCityName(tDemandOrderAddress.getUnloadCityName());
			responseModel.setUnloadDetailAddress(tDemandOrderAddress.getUnloadDetailAddress());
			responseModel.setUnloadProvinceName(tDemandOrderAddress.getUnloadProvinceName());
			responseModel.setUnloadWarehouse(tDemandOrderAddress.getUnloadWarehouse());

			responseModels.add(responseModel);
		}
		return responseModels;
	}

	/**
	 * 需求单批量发布详情 校验
	 */
	private void checkPublishDetail(List<TDemandOrder> tDemandOrders) {
		Set<Integer> goodsUnitList = new LinkedHashSet<>();
		for (TDemandOrder demandOrder : tDemandOrders) {
			if (!DemandOrderSourceEnum.YELO_LIFE.getKey().equals(demandOrder.getSource())){
				throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY.getMsg());
			}
			//需求单已取消不能操作
			if (CommonConstant.INTEGER_ONE.equals(demandOrder.getIfCancel())) {
				throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_CANCEL.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMANDORDER_CANCEL.getMsg());
			}
			//需求单不是待发布状态不能操作
			if (!DemandOrderStatusEnum.WAIT_PUBLISH.getKey().equals(demandOrder.getEntrustStatus())) {
				throw new BizException(EntrustDataExceptionEnum.COMPANY_CARRIER_PUBLISH_STATUS_ERROR.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.COMPANY_CARRIER_PUBLISH_STATUS_ERROR.getMsg());
			}
			goodsUnitList.add(demandOrder.getGoodsUnit());
		}
		//不同单位的不能一起操作
		if (goodsUnitList.size() > CommonConstant.INTEGER_ONE) {
			throw new BizException(EntrustDataExceptionEnum.PUBLISH_DEMAND_ORDER_GOODS_UNIT_ERROR);
		}
	}


	/**
	 * 新生需求单批量发布
	 */
	@Transactional
	public void confirmPublish(LifeBatchPublishRequestModel requestModel) {
		String demandOrderIds = String.join(",", requestModel.getDemandDtoList());
		//查询需求单
		List<TDemandOrder> tDemandOrders = tDemandOrderMapper.getByIds(demandOrderIds);
		if (ListUtils.isEmpty(tDemandOrders)) {
			throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
		}
		if (tDemandOrders.size() > CommonConstant.INTEGER_TEN) {
			throw new BizException(EntrustDataExceptionEnum.PUBLISH_DEMAND_ORDER_MAX);
		}

		//校验判断条件
		this.checkPublishDetail(tDemandOrders);

		//查询车主信息
		CompanyCarrierByIdModel companyCarrierByIdModel = demandOrderCommonBiz.companyCarrierInfoByIsOurCompany(requestModel.getIsOurCompany(), requestModel.getCompanyCarrierId());
		if (CommonConstant.INTEGER_ONE.equals(companyCarrierByIdModel.getIfAddBlacklist())){
			throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
		}

		Date now = new Date();
		int totalDemandCount = tDemandOrders.size();

		TDemandOrder upTDemandOrder;
		List<TDemandOrder> upDemandOrderList = new ArrayList<>();
		TDemandOrderOperateLogs demandOrderOperateLogs;
		List<TDemandOrderOperateLogs> demandOrderLogsList = new ArrayList<>();
		TDemandOrderCarrier tDemandOrderCarrier;
		List<TDemandOrderCarrier> tDemandOrderCarrierAddList = new ArrayList<>();
		//遍历
		for (TDemandOrder demandOrder : tDemandOrders) {
			//更新需求单信息
			upTDemandOrder = new TDemandOrder();
			upTDemandOrder.setId(demandOrder.getId());
			upTDemandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
			upTDemandOrder.setStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
			upTDemandOrder.setStatusUpdateTime(now);
			upTDemandOrder.setOrderMode(OrderModeEnum.ASSIGN_CARRIER.getKey());
			//车主信息
			upTDemandOrder.setCompanyCarrierType(companyCarrierByIdModel.getCompanyCarrierType());
			upTDemandOrder.setCompanyCarrierId(companyCarrierByIdModel.getCompanyCarrierId());
			upTDemandOrder.setCompanyCarrierName(companyCarrierByIdModel.getCompanyCarrierName());
			upTDemandOrder.setCarrierContactId(companyCarrierByIdModel.getCarrierContactId());
			upTDemandOrder.setCarrierContactName(companyCarrierByIdModel.getCarrierContactName());
			upTDemandOrder.setCarrierContactPhone(companyCarrierByIdModel.getCarrierContactPhone());
			upTDemandOrder.setCompanyCarrierLevel(companyCarrierByIdModel.getCompanyCarrierLevel());

			//货主运费
			upTDemandOrder.setExpectContractPriceType(requestModel.getContractPriceType());
			upTDemandOrder.setContractPriceType(requestModel.getContractPriceType());
			if (ContractPriceTypeEnum.UNIT_PRICE.getKey().equals(requestModel.getContractPriceType())) {
				upTDemandOrder.setExpectContractPrice(requestModel.getContractPrice());
				upTDemandOrder.setContractPrice(requestModel.getContractPrice());
			}else {
				//如果是一口价 那么需要根据需求单数量均分
				upTDemandOrder.setExpectContractPrice(requestModel.getContractPrice().divide(new BigDecimal(totalDemandCount), 2, RoundingMode.HALF_UP));
				upTDemandOrder.setContractPrice(requestModel.getContractPrice().divide(new BigDecimal(totalDemandCount), 2, RoundingMode.HALF_UP));
			}

			//车主运费
			upTDemandOrder.setCarrierPriceType(requestModel.getCarrierPriceType());
			if (CarrierPriceTypeEnum.UNIT_PRICE.getKey().equals(requestModel.getCarrierPriceType())) {
				upTDemandOrder.setCarrierPrice(requestModel.getCarrierPrice());
			}else {
				//如果是一口价 那么需要根据需求单数量均分
				upTDemandOrder.setCarrierPrice(requestModel.getCarrierPrice().divide(new BigDecimal(totalDemandCount), 2, RoundingMode.HALF_UP));
			}

			commonBiz.setBaseEntityModify(upTDemandOrder, BaseContextHandler.getUserName());
			upDemandOrderList.add(upTDemandOrder);

			//需求单操作日志
			demandOrderOperateLogs = new TDemandOrderOperateLogs();
			demandOrderOperateLogs.setDemandOrderId(demandOrder.getId());
			demandOrderOperateLogs.setOperationType(DemandOrderOperateLogsEnum.PUBLISH_DEMAND_ORDER.getKey());
			demandOrderOperateLogs.setOperationContent(DemandOrderOperateLogsEnum.PUBLISH_DEMAND_ORDER.getValue());
			demandOrderOperateLogs.setOperatorName(BaseContextHandler.getUserName());
			demandOrderOperateLogs.setOperateTime(now);
			commonBiz.setBaseEntityAdd(demandOrderOperateLogs, BaseContextHandler.getUserName());
			demandOrderLogsList.add(demandOrderOperateLogs);

			//需求单车主信息
			tDemandOrderCarrier = new TDemandOrderCarrier();
			tDemandOrderCarrier.setDemandOrderId(demandOrder.getId());
			tDemandOrderCarrier.setCompanyCarrierId(upTDemandOrder.getCompanyCarrierId());
			tDemandOrderCarrier.setCarrierContactId(upTDemandOrder.getCarrierContactId());
			tDemandOrderCarrier.setCarrierPrice(upTDemandOrder.getCarrierPrice());
			tDemandOrderCarrier.setCarrierPriceType(upTDemandOrder.getCarrierPriceType());
			commonBiz.setBaseEntityAdd(tDemandOrderCarrier, BaseContextHandler.getUserName());
			tDemandOrderCarrierAddList.add(tDemandOrderCarrier);
		}

		//更新需求单
		tDemandOrderMapper.batchUpdateByPrimaryKeySelective(upDemandOrderList);
		//新增操作日志
		tDemandOrderOperateLogsMapper.batchInsertSelective(demandOrderLogsList);
		//需求单车主表信息插入
		tDemandOrderCarrierMapper.batchInsertSelective(tDemandOrderCarrierAddList);
	}


	/**
	 * 新生模块 取消需求单
	 */
	@Transactional
	public void cancelDemandOrder(LifeDemandOrderCancelRequestModel requestModel) {
		String demandOrderIds = String.join(",", requestModel.getDemandOrderIds());
		//查询需求单
		List<TDemandOrder> tDemandOrders = tDemandOrderMapper.getByIds(demandOrderIds);
		if (ListUtils.isEmpty(tDemandOrders) || tDemandOrders.size() != requestModel.getDemandOrderIds().size()) {
			throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY);
		}

		//校验取消需求单
		this.checkCancelDemandOrder(tDemandOrders);

		//查询需求单商品
		List<TDemandOrderGoods> tDemandOrderGoods = tDemandOrderGoodsMapper.getDemandOrderGoodsByDemandOrderIds(demandOrderIds);
		if (CollectionUtil.isEmpty(tDemandOrderGoods)) {
			throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_GOODS_EMPTY);
		}
		Map<Long, List<TDemandOrderGoods>> tDemandOrderGoodsMap = tDemandOrderGoods.stream().collect(Collectors.groupingBy(TDemandOrderGoods::getDemandOrderId));

		//同步新生需求单号
		DemandOrderListToYeloLifeModel demandOrderListToYeloLifeModel;
		List<DemandOrderListToYeloLifeModel> codeListSyncLife = new ArrayList<>();

		List<TDemandOrder> cancelList = new ArrayList<>();
		List<TDemandOrderOperateLogs> logList = new ArrayList<>();
		List<TDemandOrderEvents> eventsList = new ArrayList<>();
		List<TDemandOrderGoods> updateGoodList = new ArrayList<>();
		for (TDemandOrder tDemandOrder : tDemandOrders) {
			List<TDemandOrderGoods> demandOrderGoodsList = tDemandOrderGoodsMap.get(tDemandOrder.getId());
			if (CollectionUtil.isEmpty(demandOrderGoodsList)) {
				throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_GOODS_EMPTY);
			}
			//取消需求单
			TDemandOrder demandOrder = new TDemandOrder();
			demandOrder.setId(tDemandOrder.getId());
			demandOrder.setIfCancel(CommonConstant.INTEGER_ONE);
			demandOrder.setCancelTime(new Date());
			demandOrder.setNotArrangedAmount(CommonConstant.BIG_DECIMAL_ZERO);
			demandOrder.setBackAmount(tDemandOrder.getNotArrangedAmount().add(tDemandOrder.getBackAmount()));
			commonBiz.setBaseEntityModify(demandOrder, BaseContextHandler.getUserName());
			cancelList.add(demandOrder);

			//将物品未安排数量返回到退回数量里
			for (TDemandOrderGoods good : demandOrderGoodsList) {
				TDemandOrderGoods demandOrderGoods = new TDemandOrderGoods();
				demandOrderGoods.setId(good.getId());
				demandOrderGoods.setNotArrangedAmount(CommonConstant.BIG_DECIMAL_ZERO);
				demandOrderGoods.setBackAmount(good.getNotArrangedAmount().add(good.getBackAmount()));
				commonBiz.setBaseEntityModify(demandOrderGoods, BaseContextHandler.getUserName());
				updateGoodList.add(demandOrderGoods);
			}
			//新增取消需求单操作日志
			TDemandOrderOperateLogs demandOrderOperateLogs = demandOrderCommonBiz.getDemandOrderOperateLogs(tDemandOrder.getId(), DemandOrderOperateLogsEnum.CANCEL_DEMAND_ORDER, BaseContextHandler.getUserName(), "");
			logList.add(demandOrderOperateLogs);

			//新增取消需求单事件
			TDemandOrderEvents demandOrderEvents = demandOrderCommonBiz.generateEvent(tDemandOrder.getId(), tDemandOrder.getCompanyCarrierId(), DemandOrderEventsTypeEnum.CANCEL_DEMANDORDER, BaseContextHandler.getUserName());
			eventsList.add(demandOrderEvents);

			demandOrderListToYeloLifeModel = new DemandOrderListToYeloLifeModel();
			demandOrderListToYeloLifeModel.setDemandOrderCode(tDemandOrder.getDemandOrderCode());
			demandOrderListToYeloLifeModel.setCustomerOrderCode(tDemandOrder.getCustomerOrderCode());
			codeListSyncLife.add(demandOrderListToYeloLifeModel);

		}

		if (ListUtils.isNotEmpty(cancelList)) {
			tDemandOrderMapper.batchUpdateByPrimaryKeySelective(cancelList);
		}
		if (ListUtils.isNotEmpty(logList)) {
			demandOrderOperateLogsMapper.batchInsertSelective(logList);
		}
		if (ListUtils.isNotEmpty(eventsList)) {
			demandOrderEventsMapper.batchInsertSelective(eventsList);
		}
		if (ListUtils.isNotEmpty(updateGoodList)) {
			demandOrderGoodsMapper.batchUpdateByPrimaryKeySelective(updateGoodList);
		}

		//发消息给新生
		if (ListUtils.isNotEmpty(codeListSyncLife)) {
			DemandOrderToYeloLifeModel demandOrderToYeloLifeModel = new DemandOrderToYeloLifeModel();
			demandOrderToYeloLifeModel.setItems(codeListSyncLife);
			demandOrderToYeloLifeModel.setUserName(BaseContextHandler.getUserName());
			SyncDemandOrderToYeloLifeModel<Object> syncDemandOrderToYeloLifeModel = new SyncDemandOrderToYeloLifeModel<>();
			syncDemandOrderToYeloLifeModel.setType(SyncDemandOrderToYeloLifeModel.SyncTypeEnum.CANCEL.getKey());
			syncDemandOrderToYeloLifeModel.setMsgData(demandOrderToYeloLifeModel);
			rabbitMqPublishBiz.syncDemandOrderToYeloLife(syncDemandOrderToYeloLifeModel);
		}
	}

	/**
	 * 校验取消需求单
	 */
	private void checkCancelDemandOrder(List<TDemandOrder> tDemandOrders) {
		if (CollectionUtil.isEmpty(tDemandOrders)) {
			return;
		}

		//判断需求单状态
		for (TDemandOrder tDemandOrder : tDemandOrders) {
			if (!DemandOrderSourceEnum.YELO_LIFE.getKey().equals(tDemandOrder.getSource())){
				throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY.getCode(), tDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY.getMsg());
			}
			//已取消不能操作
			if (CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfCancel())) {
				throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_CANCEL.getCode(), tDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMANDORDER_IS_CANCEL.getMsg());
			}
			//已调度车辆不能取消
			if (!(DemandOrderStatusEnum.WAIT_PUBLISH.getKey().equals(tDemandOrder.getEntrustStatus()) || DemandOrderStatusEnum.WAIT_DISPATCH.getKey().equals(tDemandOrder.getEntrustStatus()))) {
				throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_NOT_CANCEL.getCode(), tDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMANDORDER_NOT_CANCEL.getMsg());
			}
		}
	}

	/**
	 * 新生需求单修改车主
	 */
	public void modifyCarrier(ModifyCarrierForLifeRequestModel requestModel) {
		String demandOrderIds = String.join(",", requestModel.getDemandOrderIds());
		//查询需求单
		List<TDemandOrder> tDemandOrders = tDemandOrderMapper.getByIds(demandOrderIds);
		if (CollectionUtil.isEmpty(tDemandOrders) || tDemandOrders.size() != requestModel.getDemandOrderIds().size()) {
			throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
		}

		//校验新生需求单修改车主
		this.checkModifyCarrier(tDemandOrders);

		//查询车主信息
		CompanyCarrierByIdModel companyCarrierByIdModel = demandOrderCommonBiz.companyCarrierInfoByIsOurCompany(requestModel.getIsOurCompany(), requestModel.getCompanyCarrierId());
		if (CommonConstant.INTEGER_ONE.equals(companyCarrierByIdModel.getIfAddBlacklist())){
			throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
		}


		//修改需求单
		List<TDemandOrder> upDemandOrderList = new ArrayList<>();
		List<TDemandOrderEvents> tDemandOrderEventsAddList = new ArrayList<>();
		List<TDemandOrderOperateLogs> tDemandOrderOperateLogsList = new ArrayList<>();

		//修改需求单车主
		List<TDemandOrderCarrier> upTDemandOrderCarrierList = new ArrayList<>();
		//新增需求单车主
		List<TDemandOrderCarrier> demandOrderCarrierAddList = new ArrayList<>();

		//需求单原车主id
		List<Long> oldCompanyCarrierIds = tDemandOrders.stream().map(TDemandOrder::getCompanyCarrierId).collect(Collectors.toList());
		//修改前车主修改原因
		Map<Long, TDemandOrderCarrier> demandOrderCarrierMap = tDemandOrderCarrierMapper.getNewestDemandCarrierByDemandOrderIds(StringUtils.join(requestModel.getDemandOrderIds(), ','), StringUtils.join(oldCompanyCarrierIds, ','))
				.stream().collect(Collectors.toMap(TDemandOrderCarrier::getDemandOrderId, item -> item, (t, t2) -> t.getCreatedTime().getTime() > t2.getCreatedTime().getTime() ? t : t2));
		for (TDemandOrder tDemandOrder : tDemandOrders) {
			//校验车主是否修改
			if (tDemandOrder.getCompanyCarrierId().equals(companyCarrierByIdModel.getCompanyCarrierId())) {
				throw new BizException(EntrustDataExceptionEnum.CARRIER_NOT_CHANGE);
			}

			//更新需求单
			TDemandOrder upDemandOrder = new TDemandOrder();
			upDemandOrder.setId(tDemandOrder.getId());
			upDemandOrder.setCompanyCarrierId(companyCarrierByIdModel.getCompanyCarrierId());
			upDemandOrder.setCompanyCarrierType(companyCarrierByIdModel.getCompanyCarrierType());
			upDemandOrder.setCompanyCarrierLevel(companyCarrierByIdModel.getCompanyCarrierLevel());
			//个人车主
			if (CommonConstant.INTEGER_TWO.equals(companyCarrierByIdModel.getCompanyCarrierType())) {
				upDemandOrder.setCompanyCarrierName("");
				upDemandOrder.setCarrierContactId(companyCarrierByIdModel.getCarrierContactId());
				upDemandOrder.setCarrierContactName(companyCarrierByIdModel.getCarrierContactName());
				upDemandOrder.setCarrierContactPhone(companyCarrierByIdModel.getCarrierContactPhone());
			}
			//企业车主
			else {
				upDemandOrder.setCompanyCarrierName(companyCarrierByIdModel.getCompanyCarrierName());
				upDemandOrder.setCarrierContactId(CommonConstant.LONG_ZERO);
				upDemandOrder.setCarrierContactName("");
				upDemandOrder.setCarrierContactPhone("");
			}

			commonBiz.setBaseEntityModify(upDemandOrder, BaseContextHandler.getUserName());
			upDemandOrderList.add(upDemandOrder);

			//添加取消事件，接单事件
			tDemandOrderEventsAddList.add(demandOrderCommonBiz.generateEvent(tDemandOrder.getId(), tDemandOrder.getCompanyCarrierId(), DemandOrderEventsTypeEnum.CANCEL_DEMANDORDER, BaseContextHandler.getUserName()));
			tDemandOrderEventsAddList.add(demandOrderCommonBiz.generateEvent(tDemandOrder.getId(), upDemandOrder.getCompanyCarrierId(), DemandOrderEventsTypeEnum.ACCEPT_CARRIERORDER, BaseContextHandler.getUserName()));

			TDemandOrderCarrier tDemandOrderCarrier = demandOrderCarrierMap.get(tDemandOrder.getId());
			//修改前车主修改原因
			if (tDemandOrderCarrier != null) {
				TDemandOrderCarrier upTDemandOrderCarrier = new TDemandOrderCarrier();
				upTDemandOrderCarrier.setId(tDemandOrderCarrier.getId());
				upTDemandOrderCarrier.setCancelReason(requestModel.getModifyReason());
				commonBiz.setBaseEntityModify(upTDemandOrderCarrier, BaseContextHandler.getUserName());
				upTDemandOrderCarrierList.add(upTDemandOrderCarrier);
			}

			//添加新的车主记录
			TDemandOrderCarrier demandOrderCarrierAdd = new TDemandOrderCarrier();
			demandOrderCarrierAdd.setDemandOrderId(tDemandOrder.getId());
			demandOrderCarrierAdd.setCompanyCarrierId(companyCarrierByIdModel.getCompanyCarrierId());
			demandOrderCarrierAdd.setCarrierContactId(companyCarrierByIdModel.getCarrierContactId());
			commonBiz.setBaseEntityAdd(demandOrderCarrierAdd, BaseContextHandler.getUserName());
			demandOrderCarrierAddList.add(demandOrderCarrierAdd);

			//生成修改车主日志
			String beforeCompanyName = CompanyTypeEnum.PERSON.getKey().equals(tDemandOrder.getCompanyCarrierType()) ? tDemandOrder.getCarrierContactName() + " " +
					tDemandOrder.getCarrierContactPhone() : tDemandOrder.getCompanyCarrierName();
			String afterCompanyName = CompanyTypeEnum.PERSON.getKey().equals(companyCarrierByIdModel.getCompanyCarrierType()) ? companyCarrierByIdModel.getCarrierContactName() + " " +
					companyCarrierByIdModel.getCarrierContactPhone() : companyCarrierByIdModel.getCompanyCarrierName();
			String operateLogRemark = String.format(DemandOrderOperateLogsEnum.MODIFY_CARRIER.getFormat(), beforeCompanyName, afterCompanyName, requestModel.getModifyReason());
			tDemandOrderOperateLogsList.add(demandOrderCommonBiz.getDemandOrderOperateLogs(tDemandOrder.getId(), DemandOrderOperateLogsEnum.MODIFY_CARRIER, BaseContextHandler.getUserName(), operateLogRemark));
		}

		if (ListUtils.isNotEmpty(upDemandOrderList)) {
			tDemandOrderMapper.batchUpdateByPrimaryKeySelective(upDemandOrderList);
		}

		if (ListUtils.isNotEmpty(tDemandOrderEventsAddList)) {
			demandOrderEventsMapper.batchInsertSelective(tDemandOrderEventsAddList);
		}

		if (ListUtils.isNotEmpty(tDemandOrderOperateLogsList)) {
			tDemandOrderOperateLogsMapper.batchInsertSelective(tDemandOrderOperateLogsList);
		}

		if (ListUtils.isNotEmpty(upTDemandOrderCarrierList)) {
			tDemandOrderCarrierMapper.batchUpdate(upTDemandOrderCarrierList);
		}

		if (ListUtils.isNotEmpty(demandOrderCarrierAddList)) {
			tDemandOrderCarrierMapper.batchInsertSelective(demandOrderCarrierAddList);
		}
	}

	/**
	 * 校验新生需求单修改车主
	 */
	private void checkModifyCarrier(List<TDemandOrder> tDemandOrders) {
		//校验需求单状态
		for (TDemandOrder tDemandOrder : tDemandOrders) {
			//非新生需求单
			if (!DemandOrderSourceEnum.YELO_LIFE.getKey().equals(tDemandOrder.getSource())) {
				throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
			}
			//需求单已取消不能操作
			if (CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfCancel())) {
				throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_CANCEL);
			}
			//非待调度状态无法操作
			if (!DemandOrderStatusEnum.WAIT_DISPATCH.getKey().equals(tDemandOrder.getStatus())) {
				throw new BizException(EntrustDataExceptionEnum.ONLY_WAIT_DISPATCH_DEMAND_ORDER_CAN_OPERATE);
			}
		}
	}

}
