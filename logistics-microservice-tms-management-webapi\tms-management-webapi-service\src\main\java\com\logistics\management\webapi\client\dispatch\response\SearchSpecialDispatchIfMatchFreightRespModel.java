package com.logistics.management.webapi.client.dispatch.response;


import lombok.Data;

import java.math.BigDecimal;

@Data
public class SearchSpecialDispatchIfMatchFreightRespModel {

    /**
     * 是否匹配零担运价规则 1匹配 0不匹配
     */
    private Integer ifMatchShippingFreightRule;
    /**
     * 串点费用
     */
    private BigDecimal crossPointFee;

    /**
     * 整车运费
     */
    private BigDecimal carrierFreight;
}
