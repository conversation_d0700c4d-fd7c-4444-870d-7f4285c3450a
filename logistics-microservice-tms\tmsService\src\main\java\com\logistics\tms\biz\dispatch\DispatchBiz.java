package com.logistics.tms.biz.dispatch;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.carriercontact.dto.CarrierContactDetailResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.LocalStringUtil;
import com.logistics.tms.base.utils.RegExpValidatorUtil;
import com.logistics.tms.biz.carrierorder.CarrierOrderBiz;
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.companycarrier.CompanyCarrierBiz;
import com.logistics.tms.biz.demandorder.DemandOrderBiz;
import com.logistics.tms.biz.demandorder.DemandOrderCommonBiz;
import com.logistics.tms.biz.dispatch.model.DemandOrderGoodsModel;
import com.logistics.tms.biz.dispatch.model.DemandOrderModel;
import com.logistics.tms.biz.dispatch.model.DriverByVehiclesModel;
import com.logistics.tms.biz.dispatch.model.UpdateCarrierExpectMileageModel;
import com.logistics.tms.biz.shippingorder.model.ShippingOrderSqlConditionModel;
import com.logistics.tms.biz.staff.model.TStaffBasicModel;
import com.logistics.tms.biz.staffvehiclerelation.StaffVehicleBiz;
import com.logistics.tms.biz.staffvehiclerelation.model.TVehicleBasicModel;
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel;
import com.logistics.tms.biz.workgroup.sendmsg.model.WorkGroupEventModel;
import com.logistics.tms.biz.workgroup.sendmsg.model.WorkGroupPushBoModel;
import com.logistics.tms.biz.workordercenter.WorkOrderBiz;
import com.logistics.tms.client.BigDataClient;
import com.logistics.tms.client.model.PathPlanListResponseModel;
import com.logistics.tms.client.model.PathPlanRequestModel;
import com.logistics.tms.client.model.PathPlanResponseModel;
import com.logistics.tms.client.model.RecyclePublishUpdateDemandRequestModel;
import com.logistics.tms.controller.carrierorder.request.*;
import com.logistics.tms.controller.carrierorder.response.*;
import com.logistics.tms.controller.companycarrier.response.UserCompanyCarrierInfoResponseModel;
import com.logistics.tms.controller.demandorder.request.DemandOrderSearchByIdsRequestModel;
import com.logistics.tms.controller.demandorder.request.UpdateDispatchVehicleCountRequestModel;
import com.logistics.tms.controller.demandorder.response.*;
import com.logistics.tms.controller.dispatch.request.*;
import com.logistics.tms.controller.dispatch.response.*;
import com.logistics.tms.controller.staffvehiclerelation.request.StaffAndVehicleSearchRequestModel;
import com.logistics.tms.controller.staffvehiclerelation.response.StaffAndVehicleSearchResponseModel;
import com.logistics.tms.controller.vehicleassetmanagement.response.VehicleAssetManagementListResponseModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.logistics.tms.rabbitmq.publisher.model.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.MapUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: dongya.li
 * @Date: 2019/9/12 13:26
 * @Description:
 */
@Service
@Slf4j
public class DispatchBiz {

    @Autowired
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Autowired
    private TDispatchOrderMapper dispatchOrderMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TCarrierOrderAddressMapper carrierOrderAddressMapper;
    @Autowired
    private TCarrierOrderGoodsMapper carrierOrderGoodsMapper;
    @Autowired
    private TCarrierOrderEventsMapper carrierOrderEventsMapper;
    @Autowired
    private TCarrierOrderOperateLogsMapper carrierOrderOperateLogsMapper;
    @Autowired
    private TCarrierOrderVehicleHistoryMapper carrierOrderVehicleHistoryMapper;
    @Autowired
    private TDemandOrderAddressMapper demandOrderAddressMapper;
    @Autowired
    private TExtDemandOrderRelationMapper tExtDemandOrderRelationMapper;
    @Autowired
    private TDemandOrderGoodsMapper tDemandOrderGoodsMapper;
    @Autowired
    private TVehicleTransportLineMapper vehicleTransportLineMapper;
    @Autowired
    private TCarrierOrderOrderRelMapper carrierOrderOrderRelMapper;
    @Autowired
    private TStaffBasicMapper tStaffBasicMapper;
    @Autowired
    private TVehicleBasicMapper tVehicleBasicMapper;
    @Autowired
    private DemandOrderBiz demandOrderBiz;
    @Autowired
    private TDemandOrderMapper demandOrderMapper;
    @Autowired
    private TDemandOrderGoodsMapper demandOrderGoodsMapper;
    @Autowired
    private CarrierOrderBiz carrierOrderBiz;
    @Autowired
    private TDemandOrderOperateLogsMapper tDemandOrderOperateLogsMapper;
    @Autowired
    private TDemandOrderEventsMapper tDemandOrderEventsMapper;
    @Autowired
    private StaffVehicleBiz staffVehicleBiz;
    @Autowired
    private TStaffVehicleRelationMapper tStaffVehicleRelationMapper;
    @Autowired
    private TCarrierOrderWxMapper tCarrierOrderWxMapper;
    @Autowired
    private TCarrierVehicleRelationMapper tCarrierVehicleRelationMapper;
    @Autowired
    private TDemandOrderOrderRelMapper tDemandOrderOrderRelMapper;
    @Autowired
    private TDemandOrderGoodsRelMapper tDemandOrderGoodsRelMapper;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private TDemandOrderObjectionMapper tDemandOrderObjectionMapper;
    @Autowired
    private TCarrierDriverRelationMapper tCarrierDriverRelationMapper;
    @Autowired
    private TCarrierContactMapper tCarrierContactMapper;
    @Autowired
    private CompanyCarrierBiz companyCarrierBiz;
    @Autowired
    private CarrierOrderCommonBiz carrierOrderCommonBiz;
    @Autowired
    private RabbitMqPublishBiz rabbitMqPublishBiz;
    @Autowired
    private DemandOrderCommonBiz demandOrderCommonBiz;
    @Autowired
    private TVehicleTypeMapper tVehicleTypeMapper;
    @Autowired
    private WorkOrderBiz workOrderBiz;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private TShippingOrderMapper tShippingOrderMapper;
    @Resource
    private TShippingOrderItemMapper tShippingOrderItemMapper;
    @Resource
    private BigDataClient bigDataClient;
    @Resource
    private TCarrierOrderGoodsMapper tCarrierOrderGoodsMapper;

    /**
     * 调度车辆
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public void dispatchVehicle(DispatchRequestModel requestModel) {
        List<Long> idList = new ArrayList<>();
        List<Long> goodIdList = new ArrayList<>();
        List<Long> demandOrderRelIdList = new ArrayList<>();
        Map<Long, BigDecimal> loadAmountMap = new HashMap<>();
        Map<Long, BigDecimal> carrierOrderLoadAmountMap = new HashMap<>();
        Map<String, Integer> demandOrderStatusMap = new HashMap<>();//需求单号对应本次调度结束后需求单需要变更的状态
        Map<Long, BigDecimal> demandGoodArrangedAmountMap = new HashMap<>();
        Map<Long, BigDecimal> demandOrderRelArrangedAmountMap = new HashMap<>();
        Map<Long, BigDecimal> demandOrderAmountMap = new HashMap<>();//每个需求单数量
        Map<Long, BigDecimal> demandOrderCalcFreightAmountMap = new HashMap<>();//每个需求单预提数量之和
        BigDecimal totalCalcFreightAmount = new BigDecimal(0);//预提数量之和
        List<DemandOrderSearchByIdsResponseModel> demandOrderList;//查询到的需求单信息
        Map<Long, Map<Long, BigDecimal>> demandsLoadInfo = new HashMap<>();//每个需求单需要装货的数量信息
        Map<Long, List<DemandOrderOrderRelModel>> orderRelMap = new HashMap<>();
        Map<Long, Integer> loadValidityMap = new HashMap<>();//提货时效（需求单id-》天数）
        Map<Long, Integer> orderNumMap = new HashMap<>();//需求单排序（需求单id-》排序数值）
        Map<Long, BigDecimal> nextPointDistanceMap = new HashMap<>();//到下个点位距离（需求单id-》到下个点位距离）
        Date now = new Date();
        Map<String, Integer> shipmentCountMap = new HashMap<>();
        Integer goodsUnit;
        Long companyCarrierId;
        String companyCarrierName = "";
        Integer companyCarrierType;
        Integer companyCarrierLevel;
        Long carrierContactId = null;//车主账号id：后台是单子所属人车主账号id，前台和app是登录人所属车主账号id
        String carrierContactName = "";
        String carrierContactPhone = "";
        if (ListUtils.isEmpty(requestModel.getVehicleRequestModels())) {
            throw new BizException(CarrierDataExceptionEnum.DEMAND_ORDER_EMPTY);
        }
        for (DispatchGoodsModel model : requestModel.getVehicleRequestModels()) {
            if (model.getLoadAmount().compareTo(BigDecimal.ZERO) < 0) {
                throw new BizException(CarrierDataExceptionEnum.DEMAND_ORDER_LOAD_ACCOUNT_ERROR);
            }
            if (ListUtils.isEmpty(idList) || !idList.contains(model.getDemandOrderId())) {
                idList.add(model.getDemandOrderId());
            }
            goodIdList.add(model.getDemandOrderGoodsId());

            loadAmountMap.put(model.getDemandOrderGoodsId(), model.getLoadAmount());

            demandGoodArrangedAmountMap.put(model.getDemandOrderGoodsId(), model.getLoadAmount());
            Map<Long, BigDecimal> goodsMap = demandsLoadInfo.get(model.getDemandOrderId());
            if (goodsMap == null) {
                goodsMap = new HashMap();
                goodsMap.put(model.getDemandOrderGoodsId(), model.getLoadAmount());
            } else {
                BigDecimal goods = goodsMap.get(model.getDemandOrderGoodsId());
                if (goods == null) {
                    goodsMap.put(model.getDemandOrderGoodsId(), model.getLoadAmount());
                }
            }
            demandsLoadInfo.put(model.getDemandOrderId(), goodsMap);

            orderNumMap.put(model.getDemandOrderId(), model.getOrderNum());
            nextPointDistanceMap.put(model.getDemandOrderId(), model.getNextPointDistance());
        }
        if (null != demandsLoadInfo) {
            for (Map.Entry<Long, Map<Long, BigDecimal>> entry : demandsLoadInfo.entrySet()) {
                BigDecimal totalAccount = BigDecimal.ZERO;
                Map<Long, BigDecimal> amountMap = entry.getValue();
                for (BigDecimal loadAmount : amountMap.values()) {
                    totalAccount = totalAccount.add(loadAmount);
                }
                if (totalAccount.compareTo(BigDecimal.ZERO) == 0) {
                    throw new BizException(CarrierDataExceptionEnum.DEMAND_ORDER_LOAD_ACCOUNT_ZERO);
                }
                carrierOrderLoadAmountMap.put(entry.getKey(), totalAccount);
            }
        }
        if (ListUtils.isEmpty(idList)){
            throw new BizException(CarrierDataExceptionEnum.DEMAND_ORDER_EMPTY);
        }
        String demandOrderIds = StringUtils.listToString(idList, ',');
        //根据需求单ids查询需求单相关信息
        DemandOrderSearchByIdsRequestModel searchRequestModel = new DemandOrderSearchByIdsRequestModel();
        searchRequestModel.setDemandIds(demandOrderIds);
        searchRequestModel.setCompanyCarrierId(CommonConstant.NEGATIVE_LONG_ONE);
        List<DemandOrderSearchByIdsResponseModel> demandOrderByIdList = demandOrderMapper.getDemandOrderGoodsAndRelByIds(searchRequestModel);
        //需求单不存在抛异常
        if (ListUtils.isEmpty(demandOrderByIdList)) {
            throw new BizException(CarrierDataExceptionEnum.DEMAND_ORDER_NOT_EXIST);
        }
        //需求单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkDemandExceptionExist(demandOrderIds);
        if (!workOrderMap.isEmpty()){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EXCEPTION);
        }

        //查询需求单地址、事件信息（一条sql语句查询效率太低，故拆分查询）
        List<DemandOrderSearchByIdsResponseModel> demandOrderAddressEventByIdList = demandOrderMapper.getDemandOrderAddressEventByIds(searchRequestModel);
        Map<Long,DemandOrderSearchByIdsResponseModel> map=new HashMap<>();
        for (DemandOrderSearchByIdsResponseModel responseModel : demandOrderAddressEventByIdList) {
            map.put(responseModel.getDemandId(),responseModel);
        }

        TDispatchOrder dispatchOrder = null;
        TShippingOrder shippingOrder = null;
        //不为空 就是加入零担 需要提前查询出已存在的订单 用于绑定赋值
        if (StringUtils.isNotBlank(requestModel.getShippingOrderCode())){

            ShippingOrderSqlConditionModel shippingOrderSqlConditionModel = new ShippingOrderSqlConditionModel();
            shippingOrderSqlConditionModel.setShippingOrderCodes(Collections.singletonList(requestModel.getShippingOrderCode()));

            List<TShippingOrder> tShippingOrders = tShippingOrderMapper.selectByCondition(shippingOrderSqlConditionModel);
            if (CollectionUtil.isEmpty(tShippingOrders)) {
                throw new BizException(CarrierDataExceptionEnum.SHIPPING_ORDER_NOT_EXIST);
            }
            shippingOrder = tShippingOrders.get(0);
            //再次校验 防止页面停留阶段零担运输单发送更改
            if (!Arrays.asList(ShippingOrderStatusEnum.AUDIT_WAIT.getKey(), ShippingOrderStatusEnum.AUDIT_REJECT.getKey()).contains(shippingOrder.getStatus())) {
                throw new BizException(CarrierDataExceptionEnum.SHIPPING_ORDER_CANNOT_OPERATE);
            }

            Long dispatchOrderId = shippingOrder.getDispatchOrderId();

            dispatchOrder = dispatchOrderMapper.selectByPrimaryKeyDecrypt(dispatchOrderId);
            if (dispatchOrder == null) {
                throw new BizException(CarrierDataExceptionEnum.DISPATCH_ORDER_NOT_EXIST);
            }

            //给入参赋值 因为如果是加入零担 入参没有预计到达时间和司机车辆和挂车信息了 需要被从加入的调度单上取
            requestModel.setDriverId(dispatchOrder.getDriverId());
            requestModel.setExpectArrivalTime(dispatchOrder.getExpectArrivalTime());
            if (dispatchOrder.getTrailerVehicleId() != null && !CommonConstant.LONG_ZERO.equals(dispatchOrder.getTrailerVehicleId())) {
                requestModel.setTrailerVehicleId(dispatchOrder.getTrailerVehicleId());
            }
        }


        List<Integer> entrustTypeList = new ArrayList<>();
        Integer loadValidity;//提货时效
        List<Long> companyCarrierIds = new ArrayList<>();//车主id
        for (DemandOrderSearchByIdsResponseModel model : demandOrderByIdList) {
            if (!companyCarrierIds.contains(model.getCompanyCarrierId())) {
                companyCarrierIds.add(model.getCompanyCarrierId());
            }
            //需求单数据与地址数据拼接
            setDemandOrderSearchModelProperty(model,map.get(model.getDemandId()));
            //托盘单子类型
            if (!entrustTypeList.contains(model.getEntrustType())){
                entrustTypeList.add(model.getEntrustType());
            }
            //计算提货时效
            loadValidity = CommonConstant.INTEGER_ZERO;
            if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(model.getEntrustType())|| EntrustTypeEnum.RECYCLE_OUT.getKey().equals(model.getEntrustType())){//回收入库、回收出库类型：预计提货时间-H单期望提货时间
                loadValidity = commonBiz.differentDays(model.getExpectedLoadTime(), requestModel.getExpectArrivalTime());
            }else if (EntrustTypeEnum.DELIVER.getKey().equals(model.getEntrustType()) || EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey().equals(model.getEntrustType())){//发货、退货仓库配送类型：调度时间-H单期望提货时间
                loadValidity = commonBiz.differentDays(model.getExpectedLoadTime(), now);
            }else if (EntrustTypeEnum.TRANSFERS.getKey().equals(model.getEntrustType()) || EntrustTypeEnum.RETURN_GOODS_TRANSFERS.getKey().equals(model.getEntrustType())){//调拨、退货调拨类型：调度时间-H单下单时间
                loadValidity = commonBiz.differentDays(model.getPublishTime(), now);
            }
            loadValidityMap.put(model.getDemandId(), loadValidity);
        }
        if (ListUtils.isEmpty(companyCarrierIds)){
            throw new BizException(CarrierDataExceptionEnum.NO_COMPANY_CARRIER);
        }
        //不同车主，不能合并调度车辆
        if (companyCarrierIds.size() > CommonConstant.INTEGER_ONE){
            throw new BizException(EntrustDataExceptionEnum.ONLY_THE_SAME_OWNER_CAN_MERGE_AND_DISPATCH_VEHICLES);
        }

        //查询车主
        TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(companyCarrierIds.get(0));
        if (tCompanyCarrier == null || tCompanyCarrier.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.NO_COMPANY_CARRIER);
        }
        //零担调度，判断车主是否有零担模式权限
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getIfLessThanTruckload()) && !YesOrNoEnum.YES.getKey().equals(tCompanyCarrier.getIfLessThanTruckload())){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_NOT_OPERATION);
        }

        DemandOrderSearchByIdsResponseModel demandCompanyModel = demandOrderByIdList.get(CommonConstant.INTEGER_ZERO);
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getSource())){//后台调度车辆
            carrierContactId = demandCompanyModel.getCarrierContactId();
            carrierContactName = demandCompanyModel.getCarrierContactName();
            carrierContactPhone = demandCompanyModel.getCarrierContactMobile();
        }else{//前台和app
            companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            Long contactId = commonBiz.getNotCloseLoginUserCarrierContactId();
            for (DemandOrderSearchByIdsResponseModel demandOrder : demandOrderByIdList) {
                //需求单不是当前车主的（更换车主后，前车主看到的是取消状态）
                if (!companyCarrierId.equals(demandOrder.getCompanyCarrierId())){
                    throw new BizException(CarrierDataExceptionEnum.DEMAND_ORDER_CANCEL.getCode(), demandOrder.getDemandOrderCode() + CarrierDataExceptionEnum.DEMAND_ORDER_CANCEL.getMsg());
                }
            }
            CarrierContactDetailResponseModel carrierContactDetailById = tCarrierContactMapper.getCarrierContactDetailById(contactId);
            if (carrierContactDetailById!=null){
                carrierContactId = carrierContactDetailById.getCarrierContactId();
                carrierContactName = carrierContactDetailById.getContactName();
                carrierContactPhone = carrierContactDetailById.getContactPhone();
            }
        }
        companyCarrierId = demandCompanyModel.getCompanyCarrierId();
        companyCarrierName = demandCompanyModel.getCompanyCarrierName();
        companyCarrierType = demandCompanyModel.getCompanyCarrierType();
        companyCarrierLevel = demandCompanyModel.getCompanyCarrierLevel();

        //回收类型不能与其他类型混合调度
        if (entrustTypeList.contains(EntrustTypeEnum.RECYCLE_IN.getKey()) || entrustTypeList.contains(EntrustTypeEnum.RECYCLE_OUT.getKey())) {
            List<Integer> entrustTypes = entrustTypeList;
            entrustTypes.remove(EntrustTypeEnum.RECYCLE_IN.getKey());
            entrustTypes.remove(EntrustTypeEnum.RECYCLE_OUT.getKey());
            if (entrustTypes.size() > CommonConstant.INTEGER_ZERO) {
                throw new BizException(CarrierDataExceptionEnum.RECYCLE_ORDER_DISPATCH);
            }
        }

        //判断车辆司机是否存在
        List<VehicleAssetManagementListResponseModel> vehicleBasicInfoList = tVehicleBasicMapper.getVehicleBasicByIds(LocalStringUtil.listTostring(Collections.singletonList(requestModel.getVehicleId()), ','));
        if (ListUtils.isEmpty(vehicleBasicInfoList)) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }
        VehicleAssetManagementListResponseModel vehicleBasicInfo = vehicleBasicInfoList.get(CommonConstant.INTEGER_ZERO);

        //我司车辆,检查车辆是否处于运营中
        if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(tCompanyCarrier.getLevel())) {
            if (!VehicleOutageStatus.IN_OPERATION.getKey().equals(vehicleBasicInfo.getOperatingState())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_OUTAGE_ERROR);
            }
        }

        TStaffBasic staffBasic = tStaffBasicMapper.selectByPrimaryKey(requestModel.getDriverId());
        if (staffBasic == null || staffBasic.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_NOT_EXIST);
        }
        if (!StaffTypeEnum.DRIVER.getKey().equals(staffBasic.getType()) && !StaffTypeEnum.DRIVER_SUPERCARGO.getKey().equals(staffBasic.getType())) {
            throw new BizException(CarrierDataExceptionEnum.ONLY_DRIVERS_OF_THE_DRIVER_TYPE_CAN_BE_DISPATCHED);
        }
        //车辆司机跟车主的关联关系是否存在
        TCarrierVehicleRelation vehicleRelation = tCarrierVehicleRelationMapper.getByCompanyCarrierIdAndVehicleId(companyCarrierId, vehicleBasicInfo.getVehicleBasicId());
        if (vehicleRelation == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_CARRIER_REL_EMPTY);
        }
        TCarrierDriverRelation driverRelation = tCarrierDriverRelationMapper.getByCompanyCarrierIdAndDriverId(companyCarrierId, staffBasic.getId(), EnabledEnum.ENABLED.getKey());
        if (driverRelation == null) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_CARRIER_REL_EMPTY);
        }

        //如果选择了挂车
        VehicleBasicPropertyModel trailerVehicleInfo = null;
        if (requestModel.getTrailerVehicleId() != null && !CommonConstant.LONG_ZERO.equals(requestModel.getTrailerVehicleId())) {
            trailerVehicleInfo = tVehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getTrailerVehicleId());
            if (trailerVehicleInfo == null || !VehicleCategoryEnum.TRAILER.getKey().equals(trailerVehicleInfo.getVehicleCategory())) {
                throw new BizException(CarrierDataExceptionEnum.TRACTOR_VEHICLE_NOT_EXIST);
            }

            //判断车主和挂车车辆关系
            TCarrierVehicleRelation trailerVehicleRelation = tCarrierVehicleRelationMapper.getByCompanyCarrierIdAndVehicleId(companyCarrierId, trailerVehicleInfo.getVehicleId());
            if (trailerVehicleRelation == null) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_CARRIER_REL_EMPTY);
            }
        }

        demandOrderList = demandOrderByIdList;
        Integer lastDemandType = CommonConstant.INTEGER_ZERO;//校验需求单货品类型用
        for (DemandOrderSearchByIdsResponseModel demandOrder : demandOrderList) {
            // 只有云盘单子可以零担调度
            if (CommonConstant.INTEGER_ONE.equals(requestModel.getIfLessThanTruckload()) && !DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(demandOrder.getSource())){
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_NOT_OPERATION.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_NOT_OPERATION.getMsg());
            }
            // 后补需求单不允许零担调度
            if (CommonConstant.INTEGER_ONE.equals(requestModel.getIfLessThanTruckload()) && demandOrder.getIfExtDemandOrder().equals(CommonConstant.INTEGER_ONE)) {
                throw new BizException(EntrustDataExceptionEnum.SYSTEM_NOT_SUPPORT);
            }
            // 后补需求单不允许调度
            if (demandOrder.getIfExtDemandOrder().equals(CommonConstant.INTEGER_ONE)) {
                throw new BizException(EntrustDataExceptionEnum.SYSTEM_NOT_SUPPORT);
            }
            // 已取消的不能操作
            if (demandOrder.getIfCancel().equals(CommonConstant.INTEGER_ONE)) {
                throw new BizException(CarrierDataExceptionEnum.DEMAND_ORDER_CANCEL.getCode(), demandOrder.getDemandOrderCode() + CarrierDataExceptionEnum.DEMAND_ORDER_CANCEL.getMsg());
            }
            //已放空的不能操作
            if (demandOrder.getIfEmpty().equals(CommonConstant.INTEGER_ONE)) {
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getMsg());
            }
            //已回退的不能操作
            if (demandOrder.getIfRollback().equals(CommonConstant.INTEGER_ONE)) {

                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_ROLLBACK.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_ROLLBACK.getMsg());
            }
            //有异常的不能操作
            if (CommonConstant.INTEGER_ONE.equals(demandOrder.getIfObjectionSinopec())) {

                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_OBJECTION_SINOPEC.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_OBJECTION_SINOPEC.getMsg());
            }

            // 只有【待调度】【部分调度】状态才能操作
            if (!(DemandOrderStatusEnum.WAIT_DISPATCH.getKey().equals(demandOrder.getStatus()) || DemandOrderStatusEnum.PART_DISPATCH.getKey().equals(demandOrder.getStatus()))){
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_NOT_DISPATCH.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_NOT_DISPATCH.getMsg());
            }
            // 一个需求单最多调度99次
            if (demandOrder.getDispatchVehicleCount() >= CommonConstant.INTEGER_NINETY_NINE) {
                throw new BizException(CarrierDataExceptionEnum.DEMAND_ORDER_DISPATCH_MAX.getCode(), demandOrder.getDemandOrderCode() + CarrierDataExceptionEnum.DEMAND_ORDER_DISPATCH_MAX.getMsg());
            }

            //云盘我司运单车辆司机判断
            if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(demandOrder.getSource()) && IsOurCompanyEnum.OUR_COMPANY.getKey().equals(tCompanyCarrier.getLevel())) {
                //车辆为自营自主非挂车
                if (VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(vehicleBasicInfo.getVehicleProperty()) ||
                        VehicleCategoryEnum.TRAILER.getKey().equals(vehicleBasicInfo.getVehicleCategory())) {
                    throw new BizException(CarrierDataExceptionEnum.DISPATCH_VEHICLE_ERROR);
                }

                //司机自营自主启用
                if (StaffPropertyEnum.EXTERNAL_STAFF.getKey().equals(staffBasic.getStaffProperty()) ||
                        EnabledEnum.DISABLED.getKey().equals(driverRelation.getEnabled())) {
                    throw new BizException(CarrierDataExceptionEnum.DISPATCH_DRIVER_ERROR);
                }
            }

            BigDecimal calcFreightAmount = new BigDecimal(0);
            Integer thisDemandType = demandOrder.getGoodsUnit();
            Integer lastGoodsType = CommonConstant.INTEGER_ZERO;
            for (DemandOrderGoodsResponseModel demandOrderGoods : demandOrder.getGoodsResponseModels()) {//需求单下的货物
                if (loadAmountMap.get(demandOrderGoods.getDemandOrderGoodsId()).compareTo(demandOrderGoods.getNotArrangedAmountNumber()) > 0) {
                    throw new BizException(CarrierDataExceptionEnum.EXPECTED_AMOUNT_CANT_GT_CARRIER_AMOUNT);
                }
                if (loadAmountMap.get(demandOrderGoods.getDemandOrderGoodsId()).compareTo(BigDecimal.ZERO) < 0) {
                    throw new BizException(CarrierDataExceptionEnum.EXPECTED_AMOUNT_CANT_BE_ZERO);
                }
                BigDecimal goodsCalcFreightAmount = ConverterUtils.toBigDecimal(ConverterUtils.toBigDecimal(loadAmountMap.get(demandOrderGoods.getDemandOrderGoodsId())));
                int thisGoodsType = demandOrder.getGoodsUnit();
                calcFreightAmount = calcFreightAmount.add(goodsCalcFreightAmount);
                if (!lastGoodsType.equals(CommonConstant.INTEGER_ZERO) && !lastGoodsType.equals(thisGoodsType)) {
                    throw new BizException(CarrierDataExceptionEnum.CHOOSE_SAME_UNIT_DEMAND_ORDER_TO_DISPATCH);
                }
                lastGoodsType = thisGoodsType;
            }
            demandOrderCalcFreightAmountMap.put(demandOrder.getDemandId(), calcFreightAmount);
            totalCalcFreightAmount = totalCalcFreightAmount.add(calcFreightAmount);

            demandOrderAmountMap.put(demandOrder.getDemandId(), demandOrder.getGoodsAmount());

            if (carrierOrderLoadAmountMap.get(demandOrder.getDemandId()) != null) {
                BigDecimal amount = carrierOrderLoadAmountMap.get(demandOrder.getDemandId());
                shipmentCountMap.put(demandOrder.getDemandOrderCode(), amount.intValue());
                if (thisDemandType.equals(GoodsUnitEnum.BY_WEIGHT.getKey())) {
                    if (!RegExpValidatorUtil.isFloatNumber(ConverterUtils.toString(amount))) {
                        throw new BizException(CarrierDataExceptionEnum.EXPECT_AMOUNT_NOT_FLOAT_NUMBER);
                    }
                } else {
                    if (!RegExpValidatorUtil.isNumber(ConverterUtils.toString(amount))) {
                        throw new BizException(CarrierDataExceptionEnum.EXPECT_AMOUNT_NOT_POSITIVE_NUMBER);
                    }
                }
                BigDecimal notArrangedAmount = demandOrder.getNotArrangedAmount().subtract(amount);
                if (notArrangedAmount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                    demandOrderStatusMap.put(demandOrder.getDemandOrderCode(), DemandOrderStatusEnum.PART_DISPATCH.getKey());
                } else {
                    demandOrderStatusMap.put(demandOrder.getDemandOrderCode(), DemandOrderStatusEnum.COMPLETE_DISPATCH.getKey());
                }
            }

            orderRelMap.put(demandOrder.getDemandId(), demandOrder.getDemandOrderOrderRelModels());
            if (!lastDemandType.equals(CommonConstant.INTEGER_ZERO) && !lastDemandType.equals(thisDemandType)) {
                throw new BizException(CarrierDataExceptionEnum.CHOOSE_SAME_UNIT_DEMAND_ORDER_TO_DISPATCH);
            }
            lastDemandType = thisDemandType;
        }
        goodsUnit = lastDemandType;

        Long driverId = staffBasic.getId();//司机id
        Long vehicleId = vehicleBasicInfo.getVehicleBasicId();//车辆id

        String dispatchOrderCode = commonBiz.getBusinessTypeCode(BusinessCodeTypeEnum.DISPATCH_ORDER_CODE, "", BaseContextHandler.getUserName());


        if (StringUtils.isBlank(requestModel.getShippingOrderCode())){
            //生成调度单
            dispatchOrder = new TDispatchOrder();
            dispatchOrder.setDriverId(driverId);
            dispatchOrder.setVehicleId(vehicleId);
            if (trailerVehicleInfo != null) {
                //挂车信息
                dispatchOrder.setTrailerVehicleId(trailerVehicleInfo.getVehicleId());
                dispatchOrder.setTrailerVehicleNo(trailerVehicleInfo.getVehicleNo());
            }
            dispatchOrder.setDispatchOrderCode(dispatchOrderCode);
            dispatchOrder.setCarrierOrderCount(idList.size());
            dispatchOrder.setVehicleNo(vehicleBasicInfo.getVehicleNo());
            dispatchOrder.setDriverName(staffBasic.getName());
            dispatchOrder.setDriverMobile(staffBasic.getMobile());
            dispatchOrder.setDriverIdentity(staffBasic.getIdentityNumber());
            dispatchOrder.setExpectArrivalTime(requestModel.getExpectArrivalTime());
            dispatchOrder.setRemark(requestModel.getRemark());
            dispatchOrder.setDispatchFreightFeeType(requestModel.getDispatchFreightFeeType());
            dispatchOrder.setDispatchFreightFee(requestModel.getDispatchFreightFee() == null ? CommonConstant.BIG_DECIMAL_ZERO : requestModel.getDispatchFreightFee());
            dispatchOrder.setLoadPointAmount(requestModel.getLoadPointAmount());
            dispatchOrder.setUnloadPointAmount(requestModel.getUnloadPointAmount());
            dispatchOrder.setMarkupFee(requestModel.getMarkupFee() == null ? CommonConstant.BIG_DECIMAL_ZERO : requestModel.getMarkupFee());
            dispatchOrder.setGoodsUnit(goodsUnit);
            dispatchOrder.setSource(requestModel.getSource());
            dispatchOrder.setDispatchUserId(CommonConstant.INTEGER_ONE.equals(requestModel.getSource())?BaseContextHandler.getUserId():carrierContactId);
            dispatchOrder.setDispatchUserName(BaseContextHandler.getUserName());
            dispatchOrder.setDispatchTime(now);
            dispatchOrder.setCompanyCarrierId(companyCarrierId);
            dispatchOrder.setCompanyCarrierName(companyCarrierName);
            dispatchOrder.setCompanyCarrierType(companyCarrierType);
            dispatchOrder.setCarrierContactId(carrierContactId);
            dispatchOrder.setCarrierContactName(carrierContactName);
            dispatchOrder.setCarrierContactPhone(carrierContactPhone);
            commonBiz.setBaseEntityAdd(dispatchOrder, BaseContextHandler.getUserName());
            dispatchOrderMapper.insertSelectiveEncrypt(dispatchOrder);
        }else {
            //更新调度单的运单数量
            TDispatchOrder updateDispatchOrder = new TDispatchOrder();
            updateDispatchOrder.setId(dispatchOrder.getId());
            updateDispatchOrder.setCarrierOrderCount(dispatchOrder.getCarrierOrderCount() + idList.size());
            commonBiz.setBaseEntityAdd(updateDispatchOrder, BaseContextHandler.getUserName());
            dispatchOrderMapper.updateByPrimaryKeySelectiveEncrypt(updateDispatchOrder);
        }



        //零担调度-生成零担运输单
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getIfLessThanTruckload())) {
            BigDecimal expectAmount = ConverterUtils.toBigDecimal(carrierOrderLoadAmountMap.values().stream().mapToDouble(BigDecimal::doubleValue).sum());
            if (StringUtils.isBlank(requestModel.getShippingOrderCode())) {
                shippingOrder = new TShippingOrder();
                shippingOrder.setShippingOrderCode(commonBiz.getBusinessTypeCode(BusinessCodeTypeEnum.SHIPPING_ORDER_CODE, "", BaseContextHandler.getUserName()));
                if (CommonConstant.INTEGER_ONE.equals(requestModel.getIfMatch())) {
                    shippingOrder.setStatus(ShippingOrderStatusEnum.AUDIT_THROUGH.getKey());
                } else {
                    shippingOrder.setStatus(ShippingOrderStatusEnum.AUDIT_WAIT.getKey());
                }
                shippingOrder.setDispatchOrderId(dispatchOrder.getId());
                shippingOrder.setDispatchOrderCode(dispatchOrderCode);
                shippingOrder.setExpectAmount(expectAmount);
                shippingOrder.setGoodsUnit(goodsUnit);
                shippingOrder.setVehicleLength(requestModel.getVehicleLength());
                shippingOrder.setCarrierFreight(requestModel.getCarrierFreight());
                shippingOrder.setCrossPointFee(requestModel.getCrossPointFee());
                shippingOrder.setCrossPointDistance(ConverterUtils.toBigDecimal(nextPointDistanceMap.values().stream().filter(Objects::nonNull).mapToDouble(BigDecimal::doubleValue).sum()));
                commonBiz.setBaseEntityAdd(shippingOrder, BaseContextHandler.getUserName());
                tShippingOrderMapper.insertSelective(shippingOrder);
            } else {
                TShippingOrder updateShippingOrder = new TShippingOrder();
                updateShippingOrder.setId(shippingOrder.getId());
                updateShippingOrder.setExpectAmount(shippingOrder.getExpectAmount().add(expectAmount));
                updateShippingOrder.setCarrierFreight(requestModel.getCarrierFreight());
                updateShippingOrder.setCrossPointFee(requestModel.getCrossPointFee());
                updateShippingOrder.setStatus(ShippingOrderStatusEnum.AUDIT_WAIT.getKey());
                commonBiz.setBaseEntityAdd(updateShippingOrder, BaseContextHandler.getUserName());
                tShippingOrderMapper.updateByPrimaryKeySelective(updateShippingOrder);
            }

        }

        //新增或修改车辆GPS跟踪信息
        TVehicleBasicModel gpsVehicle = new TVehicleBasicModel();
        gpsVehicle.setVehicleBasicId(vehicleId);
        gpsVehicle.setVehicleNo(dispatchOrder.getVehicleNo());
        TStaffBasicModel gpsDriver = new TStaffBasicModel();
        gpsDriver.setStaffBasicId(driverId);
        gpsDriver.setName(dispatchOrder.getDriverName());
        gpsDriver.setMobile(dispatchOrder.getDriverMobile());
        gpsDriver.setIdentityNumber(dispatchOrder.getDriverIdentity());
        staffVehicleBiz.syncVehicleGpsInfo(gpsVehicle, gpsDriver, dispatchOrder.getId(), dispatchOrderCode, BaseContextHandler.getUserName());

        //生成运单相关信息
        if (ListUtils.isNotEmpty(demandOrderList)) {
            TCarrierOrder carrierOrder;
            TCarrierOrderAddress carrierOrderAddress;
            List<TCarrierOrderAddress> carrierOrderAddressList = new ArrayList<>();
            TCarrierOrderGoods carrierOrderGoods;
            List<TCarrierOrderGoods> carrierOrderGoodsList = new ArrayList<>();
            TShippingOrderItem shippingOrderItem;
            List<TShippingOrderItem> shippingOrderItemList = new ArrayList<>();
            TCarrierOrderEvents carrierOrderEvents;
            List<TCarrierOrderEvents> carrierOrderEventsList = new ArrayList<>();
            TCarrierOrderOperateLogs carrierOrderOperateLogs;
            List<TCarrierOrderOperateLogs> carrierOrderOperateLogsList = new ArrayList<>();
            TCarrierOrderVehicleHistory carrierOrderVehicleHistory;
            List<TCarrierOrderVehicleHistory> carrierOrderVehicleHistoryList = new ArrayList<>();
            TVehicleTransportLine vehicleTransportLine;
            Map<Long, String> demandOrderCarrierMap = new HashMap<>();//需求单id-》运单号
            List<Long> demandOrderIdList = new ArrayList<>();
            TCarrierOrderWx tCarrierOrderWx;
            List<TCarrierOrderWx> insertCarrierOrderWx;

            CopyCarrierOrderByDispatchVehicleModel copyCarrierOrderByDispatchVehicleModel;
            CopyCarrierRecycleOrderByDispatchVehicleModel copyCarrierRecycleOrderByDispatchVehicleModel;
            List<CopyCarrierOrderByDispatchVehicleModel> copyCarrierOrderList = new ArrayList<>();
            List<CopyCarrierRecycleOrderByDispatchVehicleModel> copyCarrierOrderRecycleList = new ArrayList<>();
            List<String> demandOrderCodeList = new ArrayList<>();
            List<String> demandOrderCodeRecycleList = new ArrayList<>();

            //新生运单
            CarrierOrderCreateListToYeloLifeModel carrierOrderCreateListToYeloLifeModel;
            List<CarrierOrderCreateListToYeloLifeModel> carrierOrderCreateListToYeloLifeModelList = new ArrayList<>();

            BigDecimal freightFee = new BigDecimal(0);
            BigDecimal carrierFee = new BigDecimal(0);
            BigDecimal markup = new BigDecimal(0);
            int index = 0;
            UpdateDispatchVehicleCountRequestModel dispatchVehicleCount;
            List<UpdateDispatchVehicleCountRequestModel> dispatchVehicleCountList = new ArrayList<>();

            List<Long> carrierOrderIds = new ArrayList<>();
            TCarrierOrderOrderRel carrierOrderOrderRel;
            List<TCarrierOrderOrderRel> carrierOrderOrderRelList = new ArrayList<>();
            String carrierOrderCode;
            LogisticsShipmentCountModel countModel;
            List<LogisticsShipmentCountModel> goodsCountModelList=new ArrayList<>();
            List<WorkGroupPushBoModel> workGroupPushDemandBoModels = Lists.newArrayList();
            List<WorkGroupPushBoModel> workGroupPushCarrierBoModels = Lists.newArrayList();
            for (DemandOrderSearchByIdsResponseModel model : demandOrderList) {
                //修改需求单状态
                demandOrderIdList.add(model.getDemandId());
                //生成运单
                carrierOrderCode = createCarrierOrderCode(model.getDemandOrderCode(), model.getDispatchVehicleCount());
                carrierOrder = new TCarrierOrder();
                carrierOrder.setUpstreamCustomer(model.getUpstreamCustomer());
                carrierOrder.setDispatchOrderId(dispatchOrder.getId());
                carrierOrder.setDispatchOrderCode(dispatchOrder.getDispatchOrderCode());
                carrierOrder.setDemandOrderId(model.getDemandId());
                carrierOrder.setDemandOrderCode(model.getDemandOrderCode());
                carrierOrder.setBusinessType(model.getBusinessType());
                carrierOrder.setCustomerName(model.getCustomerName());
                carrierOrder.setCustomerUserName(model.getCustomerUserName());
                carrierOrder.setCustomerUserMobile(model.getCustomerUserMobile());
                carrierOrder.setCustomerOrderSource(model.getCustomerOrderSource());
                carrierOrder.setPublishName(model.getPublishName());
                carrierOrder.setPublishMobile(model.getPublishMobile());
                carrierOrder.setPublishTime(model.getPublishTime());
                carrierOrder.setPublishOrgCode(model.getPublishOrgCode());
                carrierOrder.setPublishOrgName(model.getPublishOrgName());
                carrierOrder.setCarrierOrderCode(carrierOrderCode);
                carrierOrder.setCustomerOrderCode(model.getCustomerOrderCode());
                carrierOrder.setStatus(CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey());
                carrierOrder.setStatusUpdateTime(now);
                carrierOrder.setSource(dispatchOrder.getSource());
                carrierOrder.setDispatchTime(now);
                carrierOrder.setDispatchUserId(dispatchOrder.getDispatchUserId());
                carrierOrder.setDispatchUserName(dispatchOrder.getDispatchUserName());
                carrierOrder.setCompanyCarrierId(companyCarrierId);
                carrierOrder.setCompanyCarrierName(companyCarrierName);
                carrierOrder.setCompanyCarrierId(companyCarrierId);
                carrierOrder.setCompanyCarrierType(companyCarrierType);
                carrierOrder.setCarrierContactId(carrierContactId);
                carrierOrder.setCarrierContactName(carrierContactName);
                carrierOrder.setCarrierContactPhone(carrierContactPhone);
                carrierOrder.setCompanyCarrierLevel(companyCarrierLevel);
                carrierOrder.setCompanyEntrustId(model.getCompanyEntrustId());
                carrierOrder.setCompanyEntrustName(model.getCompanyEntrustName());
                carrierOrder.setExpectAmount(carrierOrderLoadAmountMap.get(model.getDemandId()));
                carrierOrder.setDemandOrderSource(model.getSource());
                carrierOrder.setDemandOrderEntrustType(model.getEntrustType());
                carrierOrder.setSettlementTonnage(model.getSettlementTonnage());
                carrierOrder.setCarrierSettlement(model.getCarrierSettlement());
                carrierOrder.setIfUrgent(model.getIfUrgent());
                carrierOrder.setAvailableOnWeekends(model.getAvailableOnWeekends());
                carrierOrder.setLoadingUnloadingPart(model.getLoadingUnloadingPart());
                carrierOrder.setLoadingUnloadingCharge(model.getLoadingUnloadingCharge());
                carrierOrder.setRecycleTaskType(model.getRecycleTaskType());
                carrierOrder.setProjectLabel(model.getProjectLabel());
                carrierOrder.setIfRecycleByCode(model.getIfRecycleByCode());
                //司机费用
                carrierOrder.setDispatchFreightFeeType(requestModel.getDispatchFreightFeeType());
                if (null != requestModel.getDispatchFreightFee()) {
                    if (requestModel.getDispatchFreightFeeType().equals(FreightTypeEnum.UNIT_PRICE.getKey())) {
                        carrierOrder.setDispatchFreightFee(requestModel.getDispatchFreightFee());
                    } else {
                        BigDecimal dispatchFreightFee = computeDispatchFreightFee(totalCalcFreightAmount, demandOrderCalcFreightAmountMap.get(model.getDemandId()), requestModel.getDispatchFreightFee());
                        if (demandOrderList.size() > 1) {
                            if (index == demandOrderList.size() - 1) {
                                dispatchFreightFee = requestModel.getDispatchFreightFee().subtract(freightFee);
                            } else {
                                freightFee = freightFee.add(dispatchFreightFee);
                            }
                        }
                        carrierOrder.setDispatchFreightFee(dispatchFreightFee);
                    }
                }

                //多装多卸费用
                if (null != requestModel.getMarkupFee()) {
                    BigDecimal markupFee = computeDispatchFreightFee(totalCalcFreightAmount, demandOrderCalcFreightAmountMap.get(model.getDemandId()), requestModel.getMarkupFee());
                    if (demandOrderList.size() > 1) {
                        if (index == demandOrderList.size() - 1) {
                            markupFee = requestModel.getMarkupFee().subtract(markup);
                        } else {
                            markup = markup.add(markupFee);
                        }
                    }
                    carrierOrder.setMarkupFee(markupFee);
                }

                //货主费用
                carrierOrder.setEntrustFreightType(model.getContractPriceType());
                carrierOrder.setExpectEntrustFreightType(model.getExceptContractPriceType());
                if (model.getContractPriceType().equals(FreightTypeEnum.UNIT_PRICE.getKey())) {
                    carrierOrder.setEntrustFreight(model.getContractPrice());
                } else {
                    carrierOrder.setEntrustFreight(computeDispatchFreightFee(demandOrderAmountMap.get(model.getDemandId()), demandOrderCalcFreightAmountMap.get(model.getDemandId()), model.getContractPrice()));
                }
                //计算预计货主费用到运单上
                if (model.getExceptContractPriceType().equals(FreightTypeEnum.UNIT_PRICE.getKey())) {
                    carrierOrder.setExpectEntrustFreight(model.getExceptContractPrice());
                } else {
                    carrierOrder.setExpectEntrustFreight(computeDispatchFreightFee(demandOrderAmountMap.get(model.getDemandId()), demandOrderCalcFreightAmountMap.get(model.getDemandId()), model.getExceptContractPrice()));
                }

                //议价模式
                Integer bargainingMode = model.getOrderMode();
                if (CommonConstant.INTEGER_ONE.equals(requestModel.getIfProvisionalPricing())){
                    bargainingMode = BargainingModeEnum.PROVISIONAL_PRICING.getKey();
                }else if (CommonConstant.INTEGER_ONE.equals(requestModel.getIfLessThanTruckload())){
                    bargainingMode = BargainingModeEnum.LESS_THAN_TRUCKLOAD_PRICING.getKey();
                }
                //临时定价
                if (BargainingModeEnum.PROVISIONAL_PRICING.getKey().equals(bargainingMode)){
                    //车主费用使用页面传过来的
                    carrierOrder.setCarrierPriceType(requestModel.getCarrierPriceType());
                    if (FreightTypeEnum.UNIT_PRICE.getKey().equals(requestModel.getCarrierPriceType())) {
                        carrierOrder.setCarrierPrice(requestModel.getCarrierPrice());
                    } else {
                        BigDecimal carrierPrice = computeDispatchFreightFee(totalCalcFreightAmount, demandOrderCalcFreightAmountMap.get(model.getDemandId()), requestModel.getCarrierPrice());
                        if (demandOrderList.size() > 1) {
                            if (index == demandOrderList.size() - 1) {
                                carrierPrice = requestModel.getCarrierPrice().subtract(carrierFee);
                            } else {
                                carrierFee = carrierFee.add(carrierPrice);
                            }
                        }
                        carrierOrder.setCarrierPrice(carrierPrice);
                    }
                }
                //零担定价
                else if (BargainingModeEnum.LESS_THAN_TRUCKLOAD_PRICING.getKey().equals(bargainingMode)){
                    //车主对账状态为【待完结】
                    carrierOrder.setCarrierSettleStatementStatus(CarrierSettleStatementStatusEnum.WAIT_FINISH.getKey());
                }
                //其他模式
                else{
                    //车主费用使用需求单上的
                    carrierOrder.setCarrierPriceType(model.getCarrierPriceType());
                    if (FreightTypeEnum.UNIT_PRICE.getKey().equals(model.getCarrierPriceType())) {
                        carrierOrder.setCarrierPrice(model.getCarrierPrice());
                    } else {
                        carrierOrder.setCarrierPrice(computeDispatchFreightFee(demandOrderAmountMap.get(model.getDemandId()), demandOrderCalcFreightAmountMap.get(model.getDemandId()), model.getCarrierPrice()));
                    }
                }

                carrierOrder.setGoodsUnit(goodsUnit);
                carrierOrder.setLoadValidity(loadValidityMap.get(model.getDemandId()));
                carrierOrder.setRemark(model.getRemark());
                carrierOrder.setDispatchRemark(requestModel.getRemark());
                carrierOrder.setOrderMode(model.getOrderMode());
                carrierOrder.setVehicleLengthId(model.getVehicleLengthId());
                carrierOrder.setVehicleLength(model.getVehicleLength());
                carrierOrder.setBargainingMode(bargainingMode);

                commonBiz.setBaseEntityAdd(carrierOrder, BaseContextHandler.getUserName());
                tCarrierOrderMapper.insertSelectiveEncrypt(carrierOrder);
                carrierOrderIds.add(carrierOrder.getId());

                insertCarrierOrderWx = new ArrayList<>();
                //发货人
                if (RegExpValidatorUtil.isMobile(model.getConsignorMobile())) {
                    tCarrierOrderWx = new TCarrierOrderWx();
                    tCarrierOrderWx.setCarrierOrderId(carrierOrder.getId());
                    tCarrierOrderWx.setMobile(model.getConsignorMobile());
                    tCarrierOrderWx.setName(model.getConsignorName());
                    tCarrierOrderWx.setRole(WeixinPushInfoRoleEnum.SENDER.getKey());
                    commonBiz.setBaseEntityAdd(tCarrierOrderWx, BaseContextHandler.getUserName());
                    insertCarrierOrderWx.add(tCarrierOrderWx);
                }
                //收货人
                if (RegExpValidatorUtil.isMobile(model.getReceiverMobile())) {
                    tCarrierOrderWx = new TCarrierOrderWx();
                    tCarrierOrderWx.setCarrierOrderId(carrierOrder.getId());
                    tCarrierOrderWx.setMobile(model.getReceiverMobile());
                    tCarrierOrderWx.setName(model.getReceiverName());
                    tCarrierOrderWx.setRole(WeixinPushInfoRoleEnum.RECEIVER.getKey());
                    commonBiz.setBaseEntityAdd(tCarrierOrderWx, BaseContextHandler.getUserName());
                    insertCarrierOrderWx.add(tCarrierOrderWx);
                }
                if (ListUtils.isNotEmpty(insertCarrierOrderWx)) {
                    tCarrierOrderWxMapper.batchInsert(insertCarrierOrderWx);
                }

                //生成运单地址
                carrierOrderAddress = new TCarrierOrderAddress();
                carrierOrderAddress.setCarrierOrderId(carrierOrder.getId());
                carrierOrderAddress.setLoadYeloAddressCode(model.getLoadYeloAddressCode());
                carrierOrderAddress.setLoadAddressCode(model.getLoadAddressCode());
                carrierOrderAddress.setLoadProvinceId(model.getLoadProvinceId());
                carrierOrderAddress.setLoadProvinceName(model.getLoadProvinceName());
                carrierOrderAddress.setLoadCityId(model.getLoadCityId());
                carrierOrderAddress.setLoadCityName(model.getLoadCityName());
                carrierOrderAddress.setLoadAreaId(model.getLoadAreaId());
                carrierOrderAddress.setLoadAreaName(model.getLoadAreaName());
                carrierOrderAddress.setLoadDetailAddress(model.getLoadDetailAddress());
                carrierOrderAddress.setLoadWarehouse(model.getLoadWarehouse());
                carrierOrderAddress.setLoadCompany(model.getLoadCompany());
                carrierOrderAddress.setLoadLongitude(model.getLoadLongitude());
                carrierOrderAddress.setLoadLatitude(model.getLoadLatitude());
                carrierOrderAddress.setConsignorName(model.getConsignorName());
                carrierOrderAddress.setConsignorMobile(model.getConsignorMobile());
                carrierOrderAddress.setExpectedLoadTime(model.getExpectedLoadTime());
                carrierOrderAddress.setLoadRegionId(model.getLoadRegionId());
                carrierOrderAddress.setLoadRegionName(model.getLoadRegionName());
                carrierOrderAddress.setLoadRegionContactName(model.getLoadRegionContactName());
                carrierOrderAddress.setLoadRegionContactPhone(model.getLoadRegionContactPhone());
                carrierOrderAddress.setUnloadAddressCode(model.getUnloadAddressCode());
                carrierOrderAddress.setUnloadProvinceId(model.getUnloadProvinceId());
                carrierOrderAddress.setUnloadProvinceName(model.getUnloadProvinceName());
                carrierOrderAddress.setUnloadCityId(model.getUnloadCityId());
                carrierOrderAddress.setUnloadCityName(model.getUnloadCityName());
                carrierOrderAddress.setUnloadAreaId(model.getUnloadAreaId());
                carrierOrderAddress.setUnloadAreaName(model.getUnloadAreaName());
                carrierOrderAddress.setUnloadDetailAddress(model.getUnloadDetailAddress());
                carrierOrderAddress.setUnloadWarehouse(model.getUnloadWarehouse());
                carrierOrderAddress.setUnloadCompany(model.getUnloadCompany());
                carrierOrderAddress.setUnloadLongitude(model.getUnloadLongitude());
                carrierOrderAddress.setUnloadLatitude(model.getUnloadLatitude());
                carrierOrderAddress.setUnloadAddressIsAmend(model.getUnloadAddressIsAmend());
                carrierOrderAddress.setReceiverName(model.getReceiverName());
                carrierOrderAddress.setReceiverMobile(model.getReceiverMobile());
                carrierOrderAddress.setExpectedUnloadTime(model.getExpectedUnloadTime());
                commonBiz.setBaseEntityAdd(carrierOrderAddress, BaseContextHandler.getUserName());
                carrierOrderAddressList.add(carrierOrderAddress);

                //同步云仓sku 逻辑
                List<LoadCountAndTypeModel> loadCountAndTypeModels=new ArrayList<>();
                LoadCountAndTypeModel loadCountAndTypeModel;
                //同步新生sku
                CarrierOrderGoodsToYeloLifeModel carrierOrderGoodsToYeloLifeModel;
                List<CarrierOrderGoodsToYeloLifeModel> carrierOrderGoodsToYeloLifeModelList = new ArrayList<>();
                //生成运单货物
                for (DemandOrderGoodsResponseModel goods : model.getGoodsResponseModels()) {
                    if (null != loadAmountMap.get(goods.getDemandOrderGoodsId()) && loadAmountMap.get(goods.getDemandOrderGoodsId()).compareTo(BigDecimal.ZERO) > 0) {
                        carrierOrderGoods = new TCarrierOrderGoods();
                        carrierOrderGoods.setCarrierOrderId(carrierOrder.getId());
                        carrierOrderGoods.setDemandOrderGoodsId(goods.getDemandOrderGoodsId());
                        carrierOrderGoods.setSkuCode(goods.getSkuCode());
                        carrierOrderGoods.setGoodsName(goods.getGoodsName());
                        carrierOrderGoods.setCategoryName(goods.getCategoryName());
                        carrierOrderGoods.setLength(goods.getLength());
                        carrierOrderGoods.setWidth(goods.getWidth());
                        carrierOrderGoods.setHeight(goods.getHeight());
                        carrierOrderGoods.setGoodsSize(goods.getGoodsSize());
                        carrierOrderGoods.setExpectAmount(loadAmountMap.get(goods.getDemandOrderGoodsId()));
                        commonBiz.setBaseEntityAdd(carrierOrderGoods, BaseContextHandler.getUserName());
                        carrierOrderGoodsList.add(carrierOrderGoods);

                        //云盘
                        if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(model.getSource())) {
                            countModel = new LogisticsShipmentCountModel();
                            countModel.setProductTypeCode(goods.getSkuCode());
                            countModel.setCategoryName(goods.getCategoryName());
                            countModel.setHeight(ConverterUtils.toString(goods.getHeight()));
                            countModel.setLength(ConverterUtils.toString(goods.getLength()));
                            countModel.setWidth(ConverterUtils.toString(goods.getWidth()));
                            countModel.setLogisticsDemandCode(model.getDemandOrderCode());
                            countModel.setSortName(goods.getGoodsName());
                            countModel.setShipmentCount(carrierOrderGoods.getExpectAmount().intValue());
                            goodsCountModelList.add(countModel);

                            loadCountAndTypeModel=new LoadCountAndTypeModel();
                            loadCountAndTypeModel.setProductTypeCode(countModel.getProductTypeCode());
                            loadCountAndTypeModel.setCategoryName(countModel.getCategoryName());
                            loadCountAndTypeModel.setHeight(countModel.getHeight());
                            loadCountAndTypeModel.setLength(countModel.getLength());
                            loadCountAndTypeModel.setWidth(countModel.getWidth());
                            loadCountAndTypeModel.setLoadAmount(countModel.getShipmentCount());
                            loadCountAndTypeModel.setSortName(countModel.getSortName());
                            loadCountAndTypeModels.add(loadCountAndTypeModel);
                        }
                        //新生
                        else if (DemandOrderSourceEnum.YELO_LIFE.getKey().equals(model.getSource())) {
                            carrierOrderGoodsToYeloLifeModel = new CarrierOrderGoodsToYeloLifeModel();
                            carrierOrderGoodsToYeloLifeModel.setSkuCode(carrierOrderGoods.getSkuCode());
                            carrierOrderGoodsToYeloLifeModel.setCount(carrierOrderGoods.getExpectAmount());
                            carrierOrderGoodsToYeloLifeModelList.add(carrierOrderGoodsToYeloLifeModel);
                        }
                    }
                }

                //零担调度-生成零担运输单明细
                if (CommonConstant.INTEGER_ONE.equals(requestModel.getIfLessThanTruckload())) {
                    shippingOrderItem = MapperUtils.mapper(carrierOrderAddress, TShippingOrderItem.class);
                    shippingOrderItem.setShippingOrderId(shippingOrder.getId());
                    shippingOrderItem.setCarrierOrderCode(carrierOrder.getCarrierOrderCode());
                    shippingOrderItem.setDemandOrderId(carrierOrder.getDemandOrderId());
                    shippingOrderItem.setDemandOrderCode(carrierOrder.getDemandOrderCode());
                    shippingOrderItem.setExpectAmount(carrierOrder.getExpectAmount());
                    //如果是加入零担
                    if (StringUtils.isNotBlank(requestModel.getShippingOrderCode())) {
                        shippingOrderItem.setSource(ShippingOrderItemSourceEnum.LATER_ADD.getKey());
                    }
                    //如果是非加入零担
                    else {
                        shippingOrderItem.setSource(ShippingOrderItemSourceEnum.INITIAL.getKey());
                    }
                    shippingOrderItem.setOrderNum(orderNumMap.get(carrierOrder.getDemandOrderId()));
                    shippingOrderItem.setNextPointDistance(nextPointDistanceMap.get(carrierOrder.getDemandOrderId()));
                    commonBiz.setBaseEntityAdd(shippingOrderItem, BaseContextHandler.getUserName());
                    shippingOrderItemList.add(shippingOrderItem);
                }

                if (ListUtils.isNotEmpty(model.getDemandOrderEventModels())) {
                    for (DemandOrderEventModel event : model.getDemandOrderEventModels()) {
                        if (DemandOrderEventsTypeEnum.ACCEPT_CARRIERORDER.getKey().equals(event.getEvent())) {
                            //生成承接订单事件
                            carrierOrderEvents = carrierOrderCommonBiz.getCarrierOrderEvent(carrierOrder.getId(), CarrierOrderEventsTypeEnum.BY_ORDER, BaseContextHandler.getUserName(), "");
                            carrierOrderEventsList.add(carrierOrderEvents);
                        }
                    }
                }

                //生成运单事件
                carrierOrderEvents = carrierOrderCommonBiz.getCarrierOrderEvent(carrierOrder.getId(), CarrierOrderEventsTypeEnum.DISPATCH_VEHICLE, BaseContextHandler.getUserName(), CarrierOrderEventsTypeEnum.DISPATCH_VEHICLE.getFormat());
                carrierOrderEventsList.add(carrierOrderEvents);

                //生成操作日志
                carrierOrderOperateLogs = carrierOrderCommonBiz.getCarrierOrderOperateLogs(carrierOrder.getId(), CarrierOrderOperateLogsTypeEnum.CREATE_CARRIER_ORDER, BaseContextHandler.getUserName(), CarrierOrderOperateLogsTypeEnum.CREATE_CARRIER_ORDER.format(dispatchOrder.getVehicleNo(), dispatchOrder.getDriverName() + " " + dispatchOrder.getDriverMobile()));
                carrierOrderOperateLogsList.add(carrierOrderOperateLogs);

                //生成运单车辆历史
                carrierOrderVehicleHistory = new TCarrierOrderVehicleHistory();
                carrierOrderVehicleHistory.setCarrierOrderId(carrierOrder.getId());
                carrierOrderVehicleHistory.setVehicleId(vehicleId);
                carrierOrderVehicleHistory.setVehicleNo(dispatchOrder.getVehicleNo());
                if (trailerVehicleInfo != null) {
                    //挂车信息
                    carrierOrderVehicleHistory.setTrailerVehicleId(trailerVehicleInfo.getVehicleId());
                    carrierOrderVehicleHistory.setTrailerVehicleNo(trailerVehicleInfo.getVehicleNo());
                }
                carrierOrderVehicleHistory.setDriverId(driverId);
                carrierOrderVehicleHistory.setDriverName(dispatchOrder.getDriverName());
                carrierOrderVehicleHistory.setDriverMobile(dispatchOrder.getDriverMobile());
                carrierOrderVehicleHistory.setDriverIdentity(dispatchOrder.getDriverIdentity());
                if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(model.getEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(model.getEntrustType())){
                    carrierOrderVehicleHistory.setExpectLoadTime(requestModel.getExpectArrivalTime());
                }else {
                    carrierOrderVehicleHistory.setExpectArrivalTime(requestModel.getExpectArrivalTime());
                }
                carrierOrderVehicleHistory.setAuditStatus(AuditStatusEnum.NOT_NEED_AUDIT.getKey());
                commonBiz.setBaseEntityAdd(carrierOrderVehicleHistory, BaseContextHandler.getUserName());
                carrierOrderVehicleHistoryList.add(carrierOrderVehicleHistory);

                //生成车辆路线图
                TVehicleTransportLine lineRequest = new TVehicleTransportLine();
                lineRequest.setVehicleId(vehicleId);
                lineRequest.setVehicleNo(dispatchOrder.getVehicleNo());
                lineRequest.setLoadCityId(model.getLoadCityId());
                lineRequest.setLoadAreaId(model.getLoadAreaId());
                lineRequest.setUnloadCityId(model.getUnloadCityId());
                lineRequest.setUnloadAreaId(model.getUnloadAreaId());
                TVehicleTransportLine tVehicleTransportLine = vehicleTransportLineMapper.getLineByVehicleId(lineRequest);
                if (null == tVehicleTransportLine) {//线路不存在则新增
                    vehicleTransportLine = new TVehicleTransportLine();
                    vehicleTransportLine.setVehicleId(vehicleId);
                    vehicleTransportLine.setLoadProvinceId(model.getLoadProvinceId());
                    vehicleTransportLine.setLoadProvinceName(model.getLoadProvinceName());
                    vehicleTransportLine.setLoadCityId(model.getLoadCityId());
                    vehicleTransportLine.setLoadCityName(model.getLoadCityName());
                    vehicleTransportLine.setLoadAreaId(model.getLoadAreaId());
                    vehicleTransportLine.setLoadAreaName(model.getLoadAreaName());
                    vehicleTransportLine.setLoadDetailAddress(model.getLoadDetailAddress());
                    vehicleTransportLine.setLoadWarehouse(model.getLoadWarehouse());
                    vehicleTransportLine.setUnloadProvinceId(model.getUnloadProvinceId());
                    vehicleTransportLine.setUnloadProvinceName(model.getUnloadProvinceName());
                    vehicleTransportLine.setUnloadCityId(model.getUnloadCityId());
                    vehicleTransportLine.setUnloadCityName(model.getUnloadCityName());
                    vehicleTransportLine.setUnloadAreaId(model.getUnloadAreaId());
                    vehicleTransportLine.setUnloadAreaName(model.getUnloadAreaName());
                    vehicleTransportLine.setUnloadDetailAddress(model.getUnloadDetailAddress());
                    vehicleTransportLine.setUnloadWarehouse(model.getUnloadWarehouse());
                    commonBiz.setBaseEntityAdd(vehicleTransportLine, BaseContextHandler.getUserName());
                    vehicleTransportLineMapper.insertSelective(vehicleTransportLine);
                }

                CopyCarrierOrderOrderRelByDispatchVehicleModel copyCarrierOrderOrderRelByDispatchVehicleModel;
                CopyCarrierRecycleOrderOrderRelByDispatchVehicleModel copyCarrierRecycleOrderOrderRelByDispatchVehicleModel;
                List<CopyCarrierOrderOrderRelByDispatchVehicleModel> copyCarrierOrderOrderRelList = new ArrayList<>();
                List<CopyCarrierRecycleOrderOrderRelByDispatchVehicleModel> copyCarrierRecycleOrderOrderRelList = new ArrayList<>();
                UpdateDemandOrderRelModel updateDemandOrderRelModel;
                List<UpdateDemandOrderRelModel> updateDemandOrderRelModelList = new ArrayList<>();

                //生成运单、S单关系
                if (!MapUtils.isEmpty(orderRelMap)){
                    List<DemandOrderOrderRelModel> orderRelList = orderRelMap.get(model.getDemandId());
                    if (ListUtils.isNotEmpty(orderRelList)) {
                        DemandOrderOrderRelModel relModel;
                        BigDecimal expectAmount = carrierOrderLoadAmountMap.get(model.getDemandId());
                        int i = 0;
                        do {
                            if (orderRelList.size() > i) {
                                relModel = orderRelList.get(i);
                                if (relModel != null) {
                                    BigDecimal amount = relModel.getTotalAmount().subtract(relModel.getArrangedAmount()).subtract(relModel.getBackAmount()) ;//未安排的数量
                                    if (amount.compareTo(BigDecimal.ZERO) > 0) {
                                        copyCarrierOrderOrderRelByDispatchVehicleModel = new CopyCarrierOrderOrderRelByDispatchVehicleModel();
                                        copyCarrierOrderOrderRelByDispatchVehicleModel.setCarrierOrderCode(carrierOrderCode);
                                        copyCarrierOrderOrderRelByDispatchVehicleModel.setOrderId(relModel.getOrderId());
                                        copyCarrierOrderOrderRelByDispatchVehicleModel.setOrderCode(relModel.getOrderCode());

                                        copyCarrierRecycleOrderOrderRelByDispatchVehicleModel = new CopyCarrierRecycleOrderOrderRelByDispatchVehicleModel();
                                        copyCarrierRecycleOrderOrderRelByDispatchVehicleModel.setCarrierOrderCode(carrierOrderCode);
                                        copyCarrierRecycleOrderOrderRelByDispatchVehicleModel.setOrderId(relModel.getOrderId());
                                        copyCarrierRecycleOrderOrderRelByDispatchVehicleModel.setOrderCode(relModel.getOrderCode());

                                        carrierOrderOrderRel = new TCarrierOrderOrderRel();
                                        carrierOrderOrderRel.setCarrierOrderId(carrierOrder.getId());
                                        carrierOrderOrderRel.setDemandOrderOrderId(relModel.getDemandOrderOrderRelId());
                                        carrierOrderOrderRel.setOrderId(relModel.getOrderId());
                                        carrierOrderOrderRel.setOrderCode(relModel.getOrderCode());
                                        carrierOrderOrderRel.setRelType(relModel.getRelType());
                                        carrierOrderOrderRel.setRemark(relModel.getRemark());

                                        updateDemandOrderRelModel = new UpdateDemandOrderRelModel();
                                        updateDemandOrderRelModel.setDemandOrderCode(model.getDemandOrderCode());
                                        updateDemandOrderRelModel.setOrderId(relModel.getOrderId());
                                        updateDemandOrderRelModel.setOrderCode(relModel.getOrderCode());

                                        if (expectAmount.compareTo(amount)>0) {
                                            carrierOrderOrderRel.setExpectAmount(amount);
                                            demandOrderRelArrangedAmountMap.put(relModel.getDemandOrderOrderRelId(), amount);
                                            copyCarrierOrderOrderRelByDispatchVehicleModel.setExpectAmount(amount);
                                            copyCarrierRecycleOrderOrderRelByDispatchVehicleModel.setExpectAmount(amount.intValue());
                                            updateDemandOrderRelModel.setAmount(amount);
                                        } else {
                                            carrierOrderOrderRel.setExpectAmount(expectAmount);
                                            demandOrderRelArrangedAmountMap.put(relModel.getDemandOrderOrderRelId(), expectAmount);
                                            copyCarrierOrderOrderRelByDispatchVehicleModel.setExpectAmount(expectAmount);
                                            copyCarrierRecycleOrderOrderRelByDispatchVehicleModel.setExpectAmount(expectAmount.intValue());
                                            updateDemandOrderRelModel.setAmount(expectAmount);
                                        }
                                        commonBiz.setBaseEntityAdd(carrierOrderOrderRel, BaseContextHandler.getUserName());
                                        carrierOrderOrderRelList.add(carrierOrderOrderRel);
                                        demandOrderRelIdList.add(relModel.getDemandOrderOrderRelId());
                                        copyCarrierOrderOrderRelList.add(copyCarrierOrderOrderRelByDispatchVehicleModel);
                                        copyCarrierRecycleOrderOrderRelList.add(copyCarrierRecycleOrderOrderRelByDispatchVehicleModel);
                                        updateDemandOrderRelModelList.add(updateDemandOrderRelModel);
                                        expectAmount  = expectAmount.subtract(amount) ;
                                    }
                                }
                                i++;
                            }else{
                                break;
                            }
                        } while (expectAmount.compareTo(BigDecimal.ZERO)>0);
                        if(expectAmount.compareTo(BigDecimal.ZERO)>0){
                            throw new BizException(CarrierDataExceptionEnum.AMOUNT_ERROR);
                        }
                    }
                }

                //需求单id-》运单号
                demandOrderCarrierMap.put(model.getDemandId(), carrierOrderCode);

                //乐医的需求单生成的运单才需同步到乐医系统
                if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(model.getSource())) {
                    copyCarrierOrderByDispatchVehicleModel = new CopyCarrierOrderByDispatchVehicleModel();
                    copyCarrierOrderByDispatchVehicleModel.setType(model.getEntrustType());
                    copyCarrierOrderByDispatchVehicleModel.setCarrierOrderCode(carrierOrderCode);
                    copyCarrierOrderByDispatchVehicleModel.setDemandOrderCode(model.getDemandOrderCode());
                    copyCarrierOrderByDispatchVehicleModel.setOrderRelList(copyCarrierOrderOrderRelList);
                    copyCarrierOrderByDispatchVehicleModel.setUpdateDemandOrderRelModelList(updateDemandOrderRelModelList);
                    copyCarrierOrderByDispatchVehicleModel.setLoadAmount(carrierOrderLoadAmountMap.get(model.getDemandId()));
                    copyCarrierOrderByDispatchVehicleModel.setRemark(model.getRemark());
                    copyCarrierOrderByDispatchVehicleModel.setLoadCountAndTypeModels(loadCountAndTypeModels);
                    copyCarrierOrderList.add(copyCarrierOrderByDispatchVehicleModel);
                    demandOrderCodeList.add(model.getDemandOrderCode());
                    if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(model.getEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(model.getEntrustType()) || EntrustTypeEnum.BOOKING.getKey().equals(model.getEntrustType())) {
                        copyCarrierRecycleOrderByDispatchVehicleModel = new CopyCarrierRecycleOrderByDispatchVehicleModel();
                        copyCarrierRecycleOrderByDispatchVehicleModel.setCarrierOrderCode(carrierOrderCode);
                        copyCarrierRecycleOrderByDispatchVehicleModel.setDemandOrderCode(model.getDemandOrderCode());
                        copyCarrierRecycleOrderByDispatchVehicleModel.setOrderRelList(copyCarrierRecycleOrderOrderRelList);
                        copyCarrierRecycleOrderByDispatchVehicleModel.setWarehouseName(model.getUnloadWarehouse());
                        copyCarrierRecycleOrderByDispatchVehicleModel.setLoadAmount(carrierOrderLoadAmountMap.get(model.getDemandId()).intValue());
                        copyCarrierOrderRecycleList.add(copyCarrierRecycleOrderByDispatchVehicleModel);
                        demandOrderCodeRecycleList.add(model.getDemandOrderCode());
                    }
                }

                //新生需求单调度车辆同步新生
                else if (DemandOrderSourceEnum.YELO_LIFE.getKey().equals(model.getSource())) {
                    //同步新生数据
                    carrierOrderCreateListToYeloLifeModel = new CarrierOrderCreateListToYeloLifeModel();
                    carrierOrderCreateListToYeloLifeModel.setDemandOrderId(model.getDemandId());
                    carrierOrderCreateListToYeloLifeModel.setDemandOrderCode(model.getDemandOrderCode());
                    carrierOrderCreateListToYeloLifeModel.setCustomerOrderCode(model.getCustomerOrderCode());
                    carrierOrderCreateListToYeloLifeModel.setCarrierOrderCode(carrierOrderCode);
                    carrierOrderCreateListToYeloLifeModel.setLifeGoodsModels(carrierOrderGoodsToYeloLifeModelList);
                    carrierOrderCreateListToYeloLifeModel.setVehicleNumber(dispatchOrder.getVehicleNo());
                    carrierOrderCreateListToYeloLifeModel.setDriverName(dispatchOrder.getDriverName());
                    carrierOrderCreateListToYeloLifeModel.setDriverMobilePhone(dispatchOrder.getDriverMobile());
                    carrierOrderCreateListToYeloLifeModel.setToAddressCode(model.getUnloadAddressCode());
                    carrierOrderCreateListToYeloLifeModel.setFromAddressProvince(model.getLoadProvinceName());
                    carrierOrderCreateListToYeloLifeModel.setFromAddressCity(model.getLoadCityName());
                    carrierOrderCreateListToYeloLifeModel.setFromAddressArea(model.getLoadAreaName());
                    carrierOrderCreateListToYeloLifeModel.setFromAddressDetail(model.getLoadDetailAddress());
                    if (CompanyTypeEnum.COMPANY.getKey().equals(model.getBusinessType())) {
                        carrierOrderCreateListToYeloLifeModel.setFromCustomerName(model.getCustomerName());
                    }else if (CompanyTypeEnum.PERSON.getKey().equals(model.getBusinessType())){
                        carrierOrderCreateListToYeloLifeModel.setFromCustomerName(model.getCustomerUserName());
                        carrierOrderCreateListToYeloLifeModel.setFromCustomerPhone(model.getCustomerUserMobile());
                    }
                    carrierOrderCreateListToYeloLifeModel.setUserName(BaseContextHandler.getUserName());
                    carrierOrderCreateListToYeloLifeModel.setRemark(model.getRemark());
                    carrierOrderCreateListToYeloLifeModelList.add(carrierOrderCreateListToYeloLifeModel);
                }

                //调度车辆成功后修改需求单表已调车辆数
                dispatchVehicleCount = new UpdateDispatchVehicleCountRequestModel();
                dispatchVehicleCount.setDemandId(model.getDemandId());
                dispatchVehicleCount.setDispatchVehicleCount(model.getDispatchVehicleCount() + 1);
                dispatchVehicleCountList.add(dispatchVehicleCount);
                index++;

                // 构建需求单智能推送调度节点事件Model
                workGroupPushDemandBoModels.add(new WorkGroupPushBoModel()
                        .setOrderId(model.getDemandId())
                        .setOrderSource(model.getSource())
                        .setEntrustTypeGroup(model.getEntrustType())
                        .setProjectLabel(model.getProjectLabel())
                        .setOrderType(WorkGroupOrderTypeEnum.DEMAND_ORDER)
                        .setOrderNode(WorkGroupOrderNodeEnum.DEMAND_ORDER_DISPATCH));

                // 构建运单智能推送生成运单节点事件Model
                workGroupPushCarrierBoModels.add(new WorkGroupPushBoModel()
                        .setOrderId(carrierOrder.getId())
                        .setOrderSource(carrierOrder.getDemandOrderSource())
                        .setEntrustTypeGroup(carrierOrder.getDemandOrderEntrustType())
                        .setProjectLabel(carrierOrder.getProjectLabel())
                        .setOrderType(WorkGroupOrderTypeEnum.CARRIER_ORDER)
                        .setOrderNode(WorkGroupOrderNodeEnum.CARRIER_ORDER_CREATE));
            }
            if (ListUtils.isNotEmpty(carrierOrderAddressList)) {
                carrierOrderAddressMapper.batchInsertSelective(carrierOrderAddressList);
            }
            if (ListUtils.isNotEmpty(carrierOrderGoodsList)) {
                carrierOrderGoodsMapper.batchInsertSelective(carrierOrderGoodsList);
            }
            if (ListUtils.isNotEmpty(shippingOrderItemList)){
                tShippingOrderItemMapper.batchInsert(shippingOrderItemList);
            }
            if (ListUtils.isNotEmpty(carrierOrderEventsList)) {
                carrierOrderEventsMapper.batchInsertSelective(carrierOrderEventsList);
            }
            if (ListUtils.isNotEmpty(carrierOrderOperateLogsList)) {
                carrierOrderOperateLogsMapper.batchInsertSelective(carrierOrderOperateLogsList);
            }
            if (ListUtils.isNotEmpty(carrierOrderVehicleHistoryList)) {
                carrierOrderVehicleHistoryMapper.batchInsertSelective(carrierOrderVehicleHistoryList);
            }
            if (ListUtils.isNotEmpty(carrierOrderOrderRelList)) {
                carrierOrderOrderRelMapper.batchInsertSelective(carrierOrderOrderRelList);
            }
            //调度车辆生成运单同时修改需求单状态、调车数，记录需求单日志
            if (ListUtils.isNotEmpty(demandOrderIdList) && ListUtils.isNotEmpty(dispatchVehicleCountList) && !MapUtils.isEmpty(demandOrderCarrierMap)) {
                OperationDemandOrderByDispatchVehicleModel operationModel = new OperationDemandOrderByDispatchVehicleModel();
                operationModel.setUpdateCountList(dispatchVehicleCountList);
                operationModel.setDemandOrderCarrierMap(demandOrderCarrierMap);
                UpdateDemandOrderStatusAndAmountModel model = new UpdateDemandOrderStatusAndAmountModel();
                model.setDemandOrderIds(demandOrderIdList);
                model.setDemandGoodIds(goodIdList);
                model.setDemandOrderRelIds(demandOrderRelIdList);
                model.setDemandOrderArrangedAmountMap(carrierOrderLoadAmountMap);
                model.setDemandGoodArrangedAmountMap(demandGoodArrangedAmountMap);
                model.setDemandOrderRelArrangedAmountMap(demandOrderRelArrangedAmountMap);
                operationModel.setStatusAmountModel(model);
                operationModel.setCarrierOrderForLifeList(carrierOrderCreateListToYeloLifeModelList);
                demandOrderBiz.operationDemandOrderByDispatchVehicle(operationModel);
            }

            UpdateCarrierExpectMileageModel eventModel = new UpdateCarrierExpectMileageModel();
            eventModel.setCarrierOrderIds(carrierOrderIds);
            //异步更新运单预计里程数
            applicationContext.publishEvent(eventModel);

            //同步给云仓系统
            if (ListUtils.isNotEmpty(copyCarrierOrderList)) {
                CopyCarrierOrderByDispatchVehicleMessage message = new CopyCarrierOrderByDispatchVehicleMessage();
                message.setDemandOrderCodeList(demandOrderCodeList);
                message.setDemandOrderStatusMap(demandOrderStatusMap);
                message.setLogisticsShipmentCountModels(goodsCountModelList);
                message.setItems(copyCarrierOrderList);
                message.setVehicleNo(dispatchOrder.getVehicleNo());
                message.setDriverName(dispatchOrder.getDriverName());
                message.setDriverMobile(dispatchOrder.getDriverMobile());
                message.setDriverIdentity(dispatchOrder.getDriverIdentity());
                message.setExpectArrivalTime(requestModel.getExpectArrivalTime());
                message.setUserId(BaseContextHandler.getUserId());
                message.setUserName(BaseContextHandler.getUserName());
                message.setDispatchOrderCode(dispatchOrder.getDispatchOrderCode());
                message.setDispatchUserName(dispatchOrder.getDispatchUserName());
                if (entrustTypeList.contains(EntrustTypeEnum.RECYCLE_IN.getKey()) || entrustTypeList.contains(EntrustTypeEnum.RECYCLE_OUT.getKey())) {//回收类型单独调度，调度时间传页面维护的预计提货时间
                    message.setDispatchTime(requestModel.getExpectArrivalTime());
                }else{//其他类型的单子传操作时间
                    message.setDispatchTime(now);
                }
                rabbitMqPublishBiz.createCarrierOrderSyn(message);
            }
            //同步给云盘系统
            if (ListUtils.isNotEmpty(copyCarrierOrderRecycleList)){
                CopyCarrierRecycleOrderByDispatchVehicleMessage message = new CopyCarrierRecycleOrderByDispatchVehicleMessage();
                message.setDemandOrderCodeList(demandOrderCodeRecycleList);
                message.setDemandOrderStatusMap(demandOrderStatusMap);
                message.setShipmentCountMap(shipmentCountMap);
                message.setItems(copyCarrierOrderRecycleList);
                message.setVehicleNo(dispatchOrder.getVehicleNo());
                message.setDriverName(dispatchOrder.getDriverName());
                message.setDriverMobile(dispatchOrder.getDriverMobile());
                message.setDriverIdentity(dispatchOrder.getDriverIdentity());
                message.setExpectArrivalTime(requestModel.getExpectArrivalTime());
                message.setUserId(BaseContextHandler.getUserId());
                message.setUserName(BaseContextHandler.getUserName());
                message.setDispatchOrderCode(dispatchOrder.getDispatchOrderCode());
                message.setRemark(requestModel.getRemark());
                //回收类型单独调度，调度时间传页面维护的预计提货时间
                message.setDispatchTime(requestModel.getExpectArrivalTime());
                rabbitMqPublishBiz.createCarrierOrderSynToLeYi(message);
            }

            // 发布智能推送需求单调度节点事件
            applicationContext.publishEvent(new WorkGroupEventModel()
                    .setWorkGroupPushBoModels(workGroupPushDemandBoModels));

            // 发布智能推送生成运单节点事件
            applicationContext.publishEvent(new WorkGroupEventModel()
                    .setWorkGroupPushBoModels(workGroupPushCarrierBoModels));
        }
    }
    //拼接数据
    private void setDemandOrderSearchModelProperty(DemandOrderSearchByIdsResponseModel model, DemandOrderSearchByIdsResponseModel dataModel) {
        if(dataModel==null){
            return;
        }
        model.setLoadYeloAddressCode(dataModel.getLoadYeloAddressCode());
        model.setLoadAddressCode(dataModel.getLoadAddressCode());
        model.setLoadProvinceId(dataModel.getLoadProvinceId());
        model.setLoadProvinceName(dataModel.getLoadProvinceName());
        model.setLoadCityId(dataModel.getLoadCityId());
        model.setLoadCityName(dataModel.getLoadCityName());
        model.setLoadAreaId(dataModel.getLoadAreaId());
        model.setLoadAreaName(dataModel.getLoadAreaName());
        model.setLoadDetailAddress(dataModel.getLoadDetailAddress());
        model.setLoadWarehouse(dataModel.getLoadWarehouse());
        model.setLoadCompany(dataModel.getLoadCompany());
        model.setConsignorName(dataModel.getConsignorName());
        model.setConsignorMobile(dataModel.getConsignorMobile());
        model.setExpectedLoadTime(dataModel.getExpectedLoadTime());
        model.setLoadLongitude(dataModel.getLoadLongitude());
        model.setLoadLatitude(dataModel.getLoadLatitude());
        model.setLoadRegionId(dataModel.getLoadRegionId());
        model.setLoadRegionName(dataModel.getLoadRegionName());
        model.setLoadRegionContactName(dataModel.getLoadRegionContactName());
        model.setLoadRegionContactPhone(dataModel.getLoadRegionContactPhone());
        model.setUnloadAddressCode(dataModel.getUnloadAddressCode());
        model.setUnloadProvinceId(dataModel.getUnloadProvinceId());
        model.setUnloadProvinceName(dataModel.getUnloadProvinceName());
        model.setUnloadCityId(dataModel.getUnloadCityId());
        model.setUnloadCityName(dataModel.getUnloadCityName());
        model.setUnloadAreaId(dataModel.getUnloadAreaId());
        model.setUnloadAreaName(dataModel.getUnloadAreaName());
        model.setUnloadDetailAddress(dataModel.getUnloadDetailAddress());
        model.setUnloadWarehouse(dataModel.getUnloadWarehouse());
        model.setUnloadCompany(dataModel.getUnloadCompany());
        model.setReceiverName(dataModel.getReceiverName());
        model.setReceiverMobile(dataModel.getReceiverMobile());
        model.setExpectedUnloadTime(dataModel.getExpectedUnloadTime());
        model.setUnloadLongitude(dataModel.getUnloadLongitude());
        model.setUnloadLatitude(dataModel.getUnloadLatitude());
        model.setUnloadAddressIsAmend(dataModel.getUnloadAddressIsAmend());

        model.setDemandOrderEventModels(dataModel.getDemandOrderEventModels());
    }

    /**
     * 生成运单编号
     * @param demandOrderCode 需求单号
     * @param dispatchVehicleCount
     * @return
     */
    private String createCarrierOrderCode(String demandOrderCode, Integer dispatchVehicleCount) {
        String code = "";
        Integer count = dispatchVehicleCount + 1;
        if (count < CommonConstant.INTEGER_TEN) {
            code = "0" + count;
        } else {
            code = ConverterUtils.toString(count);
        }
        String prefixCode;
        if(demandOrderCode.contains("Q")){
            prefixCode = "QY" + demandOrderCode.substring(2);
        }else{
            prefixCode = "Y" + demandOrderCode.substring(1);
        }
        return prefixCode + "-" + code;
    }

    //计算调度运费
    private BigDecimal computeDispatchFreightFee(BigDecimal totalVolume, BigDecimal volume, BigDecimal totalDispatchFreightFee) {
        return totalDispatchFreightFee.multiply(volume).divide(totalVolume, 2, BigDecimal.ROUND_HALF_UP);
    }

    /***
     * 调度车辆查询车辆和司机
     * @param requestModel
     * @return
     */
    public List<DriverAndVehicleSearchResponseModel> getDriverAndVehicleByVehicleNumber(DriverAndVehicleSearchRequestModel requestModel) {
        StaffAndVehicleSearchRequestModel staffRequestModel = MapperUtils.mapper(requestModel,StaffAndVehicleSearchRequestModel.class);
        List<StaffAndVehicleSearchResponseModel> responseModelList = tStaffVehicleRelationMapper.getDispatchVehicleListByVehicleNo(staffRequestModel);
        List<DriverAndVehicleSearchResponseModel>  retList = MapperUtils.mapper(responseModelList,DriverAndVehicleSearchResponseModel.class);
        return retList == null ? new ArrayList<>() : retList;
    }

    /**
     * 根据需求单ids查询需求单信息（用于调度车辆和完成调度时查看详情）
     *
     * @param requestModel
     * @return
     */
    public List<DemandOrderDispatchResponseModel> getDispatchDetail(DemandOrderDispatchRequestModel requestModel) {
        if (StringUtils.isBlank(requestModel.getDemandId())) {
            return new ArrayList<>();
        }
        List<DemandOrderModel> demandOrderList = demandOrderMapper.getDemandOrderGoodsByDemandIds(requestModel.getDemandId());
        if (ListUtils.isEmpty(demandOrderList)) {
            return new ArrayList<>();
        }

        List<Integer> entrustTypeList = new ArrayList<>();
        List<Integer> goodsUnitList = new ArrayList<>();
        List<Long> companyCarrierIds = new ArrayList<>();

        UserCompanyCarrierInfoResponseModel userAndCompanyInfo = null;
        //前台请求根据当前登陆人获取公司id
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            userAndCompanyInfo = companyCarrierBiz.getUserAndCompanyInfo();
        }
        //判断需求单状态
        for (DemandOrderModel order : demandOrderList) {
            if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource()) && userAndCompanyInfo != null) {//前台
                if (!order.getCompanyCarrierId().equals(userAndCompanyInfo.getCompanyCarrierId())) {//需求单不是当前车主的（更换车主后，前车主看到的是取消状态）
                    throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_CANCEL.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_CANCEL.getMsg());
                }
            }
            //已取消的不能操作
            if (order.getIfCancel().equals(CommonConstant.INTEGER_ONE)) {
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_CANCEL.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_CANCEL.getMsg());
            } else if (order.getIfEmpty().equals(CommonConstant.INTEGER_ONE)) {
                //已放空的不能操作
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getMsg());
            } else if (order.getIfRollback().equals(CommonConstant.INTEGER_ONE)) {
                //已回退的不能操作
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_ROLLBACK.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_ROLLBACK.getMsg());
            } else if (CommonConstant.INTEGER_ONE.equals(order.getIfObjectionSinopec())) {
                //有异常的不能操作(中石化推送的单子)
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_OBJECTION_SINOPEC.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_OBJECTION_SINOPEC.getMsg());
            }
            //只有【待调度】【部分调度】状态才能操作
            if (!(DemandOrderStatusEnum.WAIT_DISPATCH.getKey().equals(order.getStatus()) || DemandOrderStatusEnum.PART_DISPATCH.getKey().equals(order.getStatus()))){
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_NOT_DISPATCH.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_NOT_DISPATCH.getMsg());
            }
            if (!entrustTypeList.contains(order.getEntrustType())){
                entrustTypeList.add(order.getEntrustType());
            }
            if (!goodsUnitList.contains(order.getGoodsUnit())) {
                goodsUnitList.add(order.getGoodsUnit());
            }
            if (!companyCarrierIds.contains(order.getCompanyCarrierId())) {
                companyCarrierIds.add(order.getCompanyCarrierId());
            }
        }
        //回收类型不能与其他类型混合调度
        if (entrustTypeList.contains(EntrustTypeEnum.RECYCLE_IN.getKey()) || entrustTypeList.contains(EntrustTypeEnum.RECYCLE_OUT.getKey())) {
            entrustTypeList.remove(EntrustTypeEnum.RECYCLE_IN.getKey());
            entrustTypeList.remove(EntrustTypeEnum.RECYCLE_OUT.getKey());
            if (entrustTypeList.size() > CommonConstant.INTEGER_ZERO) {
                throw new BizException(CarrierDataExceptionEnum.RECYCLE_ORDER_DISPATCH);
            }
        }
        //不同单位的不能一起调度
        if (goodsUnitList.size() > CommonConstant.INTEGER_ONE){
            throw new BizException(EntrustDataExceptionEnum.GOODS_UNIT_DIFFERENT);
        }
        //不同车主，不能合并调度车辆
        if (companyCarrierIds.size() > CommonConstant.INTEGER_ONE){
            throw new BizException(EntrustDataExceptionEnum.ONLY_THE_SAME_OWNER_CAN_MERGE_AND_DISPATCH_VEHICLES);
        }
        //查询车主
        TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(companyCarrierIds.get(0));
        if (tCompanyCarrier == null || tCompanyCarrier.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.NO_COMPANY_CARRIER);
        }
        //需求单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkDemandExceptionExist(requestModel.getDemandId());
        if (!workOrderMap.isEmpty()){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EXCEPTION);
        }

        //组装数据
        DemandOrderDispatchResponseModel responseModel;
        List<DemandOrderDispatchResponseModel> list = new ArrayList<>();
        for (DemandOrderModel demandOrderModel : demandOrderList) {
            for (DemandOrderGoodsModel goodsModel : demandOrderModel.getGoodsList()) {
                responseModel = MapperUtils.mapper(demandOrderModel, DemandOrderDispatchResponseModel.class);
                responseModel.setCarrierType(demandOrderModel.getCompanyCarrierType());
                responseModel.setCustomerCarrierName(demandOrderModel.getCarrierContactName());
                responseModel.setCustomerCarrierMobile(demandOrderModel.getCarrierContactPhone());
                responseModel.setIsOurCompany(tCompanyCarrier.getLevel());
                responseModel.setDemandOrderGoodsId(goodsModel.getDemandOrderGoodsId());
                responseModel.setGoodsName(goodsModel.getGoodsName());
                responseModel.setGoodsSize(goodsModel.getGoodsSize());
                responseModel.setLength(goodsModel.getLength());
                responseModel.setWidth(goodsModel.getWidth());
                responseModel.setHeight(goodsModel.getHeight());
                responseModel.setNotArrangedAmount(goodsModel.getNotArrangedAmount());

                list.add(responseModel);
            }
        }
        return list;
    }

    /**
     * 完成调度（手动完成调度-数量未调度完）
     *
     * @param requestModel
     */
    @Transactional
    public void saveCompleteDispatch(CompleteDemandOrderRequestModel requestModel) {
        String demandOrderId = requestModel.getDemandOrderId();
        if (StringUtils.isBlank(demandOrderId)) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }
        List<TDemandOrder> orderMapperByIds = demandOrderMapper.getByIds(demandOrderId);
        if (ListUtils.isEmpty(orderMapperByIds)) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }
        //前台校验
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())){
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            for (TDemandOrder order : orderMapperByIds) {
                if (!order.getCompanyCarrierId().equals(companyCarrierId)) {
                    throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_CANCEL.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_CANCEL.getMsg());
                }
            }
        }else {//后台
            //入参校验
            TDemandOrder dbDemandOrder = orderMapperByIds.stream().filter(item -> DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(item.getSource())).findFirst().orElse(null);
            if (dbDemandOrder != null && (requestModel.getObjectionType() == null || StringUtils.isBlank(requestModel.getObjectionReason()) || requestModel.getObjectionReason().length() > CommonConstant.INTEGER_ONE_HUNDRED)) {//云盘单子完成调度，则需填写原因
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_COMPLETE_DISPATCH_FOR_LEYI_REQUEST_PARAM_ERROR);
            }
        }
        //判断需求单状态
        List<Integer> sourceList = new ArrayList<>();
        for (TDemandOrder order : orderMapperByIds) {
            // 已取消的不能操作
            if (order.getIfCancel().equals(CommonConstant.INTEGER_ONE)) {
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_CANCEL.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_CANCEL.getMsg());
            }
            // 后补需求单不能操作
            if (CommonConstant.INTEGER_ONE.equals(order.getIfExtDemandOrder())) {
                throw new BizException(EntrustDataExceptionEnum.SYSTEM_NOT_SUPPORT);
            }

            // 已放空的不能操作
            if (order.getIfEmpty().equals(CommonConstant.INTEGER_ONE)) {

                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getMsg());
            }

            // 已回退的不能操作
            if (order.getIfRollback().equals(CommonConstant.INTEGER_ONE)) {
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_ROLLBACK.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_ROLLBACK.getMsg());
            }

            // 只有【部分调度】状态才能操作
            if (!DemandOrderStatusEnum.PART_DISPATCH.getKey().equals(order.getStatus())){
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_WAIT_DISPATCH.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_WAIT_DISPATCH.getMsg());
            }
            // 新生运单如果未调度数量超过0.2吨不允许调度完成
            if (DemandOrderSourceEnum.YELO_LIFE.getKey().equals(order.getSource()) &&
                    new BigDecimal(CommonConstant.POINT_TWO_STR).compareTo(order.getNotArrangedAmount()) < CommonConstant.INTEGER_ZERO) {
                throw new BizException(EntrustDataExceptionEnum.LIFE_DEMAND_ORDER_CANT_COMPLETE_DISPATCH);
            }
            if (!sourceList.contains(order.getSource())) {
                sourceList.add(order.getSource());
            }
        }
        // 云盘的单子不能与别的单子一起操作
        if (sourceList.contains(DemandOrderSourceEnum.LEYI_TRAY.getKey()) && sourceList.size() > CommonConstant.INTEGER_ONE){
            throw new BizException(EntrustDataExceptionEnum.REQUEST_PARAM_ERROR);
        }
        //需求单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkDemandExceptionExist(demandOrderId);
        if (!workOrderMap.isEmpty()){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EXCEPTION);
        }

        //查询需求单下所有运单信息（除取消外）
        DemandOrderIdsRequestModel demandOrderIdsRequestModel = new DemandOrderIdsRequestModel();
        demandOrderIdsRequestModel.setDemandOrderIds(requestModel.getDemandOrderId());
        List<DemandCarrierOrderRecursiveModel> carrierOrderList = carrierOrderBiz.getByDemandOrderIds(demandOrderIdsRequestModel);
        List<Long> completeDemandIdList = new ArrayList<>();
        if (ListUtils.isNotEmpty(carrierOrderList)) {
            carrierOrderList.forEach(order -> {
                DemandCarrierOrderModel completeDemandCarrierOrderModel = order.getCarrierOrderList().stream().filter(carrierOrder -> !CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(carrierOrder.getStatus())).findFirst().orElse(null);
                if (completeDemandCarrierOrderModel == null) {
                    completeDemandIdList.add(order.getDemandOrderId());
                }
            });
        }

        //查询需求单是否存在异常信息
        List<TDemandOrderObjection> tDemandOrderObjectionList = tDemandOrderObjectionMapper.getByDemandOrderIds(demandOrderId);
        Map<Long, Long> demandOrderObjectionMap = tDemandOrderObjectionList.stream().collect(Collectors.toMap(TDemandOrderObjection::getDemandOrderId, TDemandOrderObjection::getId));

        List<TDemandOrder> list = new ArrayList<>();
        List<TDemandOrderOperateLogs> logList = new ArrayList<>();
        List<TDemandOrderEvents> eventsList = new ArrayList<>();
        LogisticsDemandStateSynchronizeModel synchronizeModel;
        List<LogisticsDemandStateSynchronizeModel> demandStateSynchronizeModels = new ArrayList<>();
        List<DispatchCompleteModel> dispatchCompleteModelList=new ArrayList<>();
        DispatchCompleteModel dispatchCompleteModel;
        Map<Long, CompleteDemandOrderBackAmountModel> backAmountModelMap = new HashMap<>();
        Map<Long,Integer> demandSourceMap=new HashMap<>();//需求单id-》委托类型
        Map<String, TDemandOrder> dbDemandOrderMap = new HashMap<>();//需求单号-》需求单信息
        //同步新生需求单号
        DemandOrderCompleteDispatchToYeloLifeModel demandOrderCompleteDispatchToYeloLifeModel;
        List<DemandOrderCompleteDispatchToYeloLifeModel> codeListSyncLife = new ArrayList<>();

        String unit;
        Date now = new Date();
        Integer dispatchValidity;
        CompleteDemandOrderBackAmountModel  amountModel;
        TDemandOrder tDemandOrder;
        TDemandOrderOperateLogs demandOrderOperateLogs;
        TDemandOrderEvents upEvent;
        TDemandOrderObjection addDemandOrderObjection;
        List<TDemandOrderObjection> addDemandOrderObjectionList = new ArrayList<>();
        TDemandOrderObjection upDemandOrderObjection;
        List<TDemandOrderObjection> upDemandOrderObjectionList = new ArrayList<>();
        for (TDemandOrder model : orderMapperByIds) {
            //更新需求单
            tDemandOrder = new TDemandOrder();
            tDemandOrder.setId(model.getId());
            tDemandOrder.setStatus(DemandOrderStatusEnum.COMPLETE_DISPATCH.getKey());
            if (completeDemandIdList.contains(model.getId())) {
                tDemandOrder.setEntrustStatus(DemandOrderStatusEnum.SIGN_DISPATCH.getKey());
            } else {
                tDemandOrder.setEntrustStatus(DemandOrderStatusEnum.COMPLETE_DISPATCH.getKey());
            }
            tDemandOrder.setStatusUpdateTime(now);
            tDemandOrder.setBackAmount(model.getBackAmount().add(model.getNotArrangedAmount()));
            tDemandOrder.setNotArrangedAmount(CommonConstant.BIG_DECIMAL_ZERO);
            commonBiz.setBaseEntityModify(tDemandOrder, BaseContextHandler.getUserName());

            //托盘同步过来的需求单
            if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(model.getSource())) {
                //计算调度时效、是否逾期
                dispatchValidity = commonBiz.differentDays(model.getPublishTime(), now);
                tDemandOrder.setDispatchValidity(dispatchValidity);
                if (CommonConstant.INTEGER_ONE.equals(model.getIfUrgent())){//加急的单子，超过1天算逾期
                    if (dispatchValidity > CommonConstant.INTEGER_ONE){
                        tDemandOrder.setIfOverdue(CommonConstant.INTEGER_ONE);
                    }
                }else{//非加急的单子，超过3天算逾期
                    if (dispatchValidity > CommonConstant.INTEGER_THREE){
                        tDemandOrder.setIfOverdue(CommonConstant.INTEGER_ONE);
                    }
                }

                //数量未安排完，手动完成调度记录异常
                if (demandOrderObjectionMap.get(model.getId()) != null){
                    upDemandOrderObjection = new TDemandOrderObjection();
                    upDemandOrderObjection.setId(demandOrderObjectionMap.get(model.getId()));
                    upDemandOrderObjection.setCustomerName("");
                    upDemandOrderObjection.setObjectionType(requestModel.getObjectionType());
                    upDemandOrderObjection.setObjectionReason(requestModel.getObjectionReason());
                    upDemandOrderObjection.setReportContactName(BaseContextHandler.getUserName());
                    upDemandOrderObjection.setReportTime(now);
                    commonBiz.setBaseEntityModify(upDemandOrderObjection, BaseContextHandler.getUserName());
                    upDemandOrderObjectionList.add(upDemandOrderObjection);
                }else {
                    addDemandOrderObjection = new TDemandOrderObjection();
                    addDemandOrderObjection.setDemandOrderId(model.getId());
                    addDemandOrderObjection.setObjectionType(requestModel.getObjectionType());
                    addDemandOrderObjection.setObjectionReason(requestModel.getObjectionReason());
                    addDemandOrderObjection.setReportContactName(BaseContextHandler.getUserName());
                    addDemandOrderObjection.setReportTime(now);
                    commonBiz.setBaseEntityAdd(addDemandOrderObjection, BaseContextHandler.getUserName());
                    addDemandOrderObjectionList.add(addDemandOrderObjection);
                }

                //同步给托盘
                synchronizeModel = new LogisticsDemandStateSynchronizeModel();
                synchronizeModel.setDemandOrderId(model.getId());
                synchronizeModel.setType(model.getEntrustType());
                synchronizeModel.setDemandState(DemandOrderStatusEnum.COMPLETE_DISPATCH.getKey());
                synchronizeModel.setDemandCode(model.getDemandOrderCode());
                synchronizeModel.setCompleteBackAmount(model.getNotArrangedAmount());
                synchronizeModel.setLastModifiedBy(BaseContextHandler.getUserName());
                demandStateSynchronizeModels.add(synchronizeModel);

                if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(model.getEntrustType())
                        || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(model.getEntrustType())
                        || EntrustTypeEnum.BOOKING.getKey().equals(model.getEntrustType())){
                    dispatchCompleteModel=new DispatchCompleteModel();
                    dispatchCompleteModel.setDemandOrderId(model.getId());
                    dispatchCompleteModel.setDemandCode(model.getDemandOrderCode());
                    dispatchCompleteModel.setDemandState(DemandOrderStatusEnum.COMPLETE_DISPATCH.getKey());
                    dispatchCompleteModel.setCompleteBackAmount(model.getNotArrangedAmount().intValue());
                    dispatchCompleteModel.setDispatchTime(now);
                    dispatchCompleteModel.setDifferenceAmount(model.getDifferenceAmount());
                    if(DemandOrderStatusEnum.SIGN_DISPATCH.getKey().equals(tDemandOrder.getEntrustStatus())){
                        dispatchCompleteModel.setIfSign(true);
                    }
                    dispatchCompleteModelList.add(dispatchCompleteModel);
                }
            } else if (DemandOrderSourceEnum.YELO_LIFE.getKey().equals(model.getSource())) {
                demandOrderCompleteDispatchToYeloLifeModel = new DemandOrderCompleteDispatchToYeloLifeModel();
                demandOrderCompleteDispatchToYeloLifeModel.setDemandOrderCode(model.getDemandOrderCode());
                demandOrderCompleteDispatchToYeloLifeModel.setCustomerOrderCode(model.getCustomerOrderCode());
                demandOrderCompleteDispatchToYeloLifeModel.setUserName(BaseContextHandler.getUserName());
                if (DemandOrderStatusEnum.SIGN_DISPATCH.getKey().equals(tDemandOrder.getEntrustStatus())){
                    demandOrderCompleteDispatchToYeloLifeModel.setDemandOrderStatusSign(YesOrNoEnum.YES.getKey());
                }
                codeListSyncLife.add(demandOrderCompleteDispatchToYeloLifeModel);
            }

            list.add(tDemandOrder);

            //新增完成调度操作日志
            unit = GoodsUnitEnum.getEnum(model.getGoodsUnit()).getUnit();
            String remark = "";
            if (model.getBackAmount().add(model.getNotArrangedAmount()).compareTo(CommonConstant.BIG_DECIMAL_ZERO) > CommonConstant.INTEGER_ZERO) {
                remark = String.format(DemandOrderOperateLogsEnum.COMPLETE_DISPATCH.getFormat(), model.getBackAmount().add(model.getNotArrangedAmount()).stripTrailingZeros().toPlainString() + unit);
            }
            demandOrderOperateLogs = demandOrderCommonBiz.getDemandOrderOperateLogs(model.getId(), DemandOrderOperateLogsEnum.COMPLETE_DISPATCH, BaseContextHandler.getUserName(), remark);
            logList.add(demandOrderOperateLogs);
            //新增完成调度事件
            upEvent = demandOrderCommonBiz.generateEvent(model.getId(), model.getCompanyCarrierId(), DemandOrderEventsTypeEnum.COMPLETE_DISPATCH_EVENTS, BaseContextHandler.getUserName());
            eventsList.add(upEvent);
            if (completeDemandIdList.contains(model.getId())) {
                demandOrderOperateLogs = demandOrderCommonBiz.getDemandOrderOperateLogs(model.getId(), DemandOrderOperateLogsEnum.SIGN_UP, BaseContextHandler.getUserName(), null);
                logList.add(demandOrderOperateLogs);
                upEvent = demandOrderCommonBiz.generateEvent(model.getId(), model.getCompanyCarrierId(), DemandOrderEventsTypeEnum.SIGN_UP, BaseContextHandler.getUserName());
                eventsList.add(upEvent);
            }
            //需求单退回数量
            amountModel = new CompleteDemandOrderBackAmountModel();
            amountModel.setCompleteBackAmount(model.getNotArrangedAmount().add(Optional.ofNullable(model.getBackAmount()).orElse(BigDecimal.ZERO)));
            amountModel.setDemandOrderCode(model.getDemandOrderCode());
            amountModel.setLastModifiedBy(BaseContextHandler.getUserName());
            backAmountModelMap.put(model.getId(), amountModel);

            demandSourceMap.put(model.getId(),model.getSource());
            dbDemandOrderMap.put(model.getDemandOrderCode(), model);
        }
        if (ListUtils.isNotEmpty(list)) {
            demandOrderMapper.batchUpdateByPrimaryKeySelective(list);
        }
        if (ListUtils.isNotEmpty(logList)) {
            tDemandOrderOperateLogsMapper.batchInsertSelective(logList);
        }
        if (ListUtils.isNotEmpty(eventsList)) {
            tDemandOrderEventsMapper.batchInsertSelective(eventsList);
        }
        if (ListUtils.isNotEmpty(addDemandOrderObjectionList)){
            tDemandOrderObjectionMapper.batchInsert(addDemandOrderObjectionList);
        }
        if (ListUtils.isNotEmpty(upDemandOrderObjectionList)){
            tDemandOrderObjectionMapper.batchUpdate(upDemandOrderObjectionList);
        }

        //s单数据 退回数据
        List<TDemandOrderOrderRel> orderRelByDemandIds = tDemandOrderOrderRelMapper.getDemandOrderOrderRelByDemandIds(demandOrderId);
        Map<String, CompleteDemandOrderBackAmountModel> modelMap = new HashMap<>();
        List<TDemandOrderOrderRel> relList = new ArrayList<>();
        if (ListUtils.isNotEmpty(orderRelByDemandIds)) {
            Iterator<Map.Entry<Long, CompleteDemandOrderBackAmountModel>> iterator = backAmountModelMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Long, CompleteDemandOrderBackAmountModel> next = iterator.next();
                Long key = next.getKey();
                CompleteDemandOrderBackAmountModel demandOrderBackModel = next.getValue();
                List<CompleteDemandOrderCarrierOrderBackAmountModel> amountModels = new ArrayList<>();
                orderRelByDemandIds.stream().filter(s -> s.getDemandOrderId().equals(key)).forEach(s -> {
                    if ((s.getTotalAmount().subtract(s.getArrangedAmount()).subtract(s.getBackAmount())).compareTo(CommonConstant.BIG_DECIMAL_ZERO) > CommonConstant.INTEGER_ZERO) {
                        TDemandOrderOrderRel orderOrderRel = new TDemandOrderOrderRel();
                        orderOrderRel.setId(s.getId());
                        orderOrderRel.setBackAmount(s.getBackAmount().add(s.getTotalAmount().subtract(s.getArrangedAmount()).subtract(s.getBackAmount())));
                        commonBiz.setBaseEntityModify(orderOrderRel, BaseContextHandler.getUserName());
                        relList.add(orderOrderRel);

                        CompleteDemandOrderCarrierOrderBackAmountModel backAmountModel = new CompleteDemandOrderCarrierOrderBackAmountModel();
                        backAmountModel.setOrderBackAmount(s.getTotalAmount().subtract(s.getArrangedAmount()).subtract(s.getBackAmount()));
                        backAmountModel.setOrderId(s.getOrderId());
                        amountModels.add(backAmountModel);
                    }
                });
                demandOrderBackModel.setModels(amountModels);
                modelMap.put(demandOrderBackModel.getDemandOrderCode(), demandOrderBackModel);
            }
        }

        //更新货物
        Map<Long,List<CompleteDemandOrderGoodsBackAmountModel>> goodsBackMap = new HashMap();
        Map<Long,List<ProductTypeAndBackAmountModel>> demandBackAmountModelMap=new HashMap<>();
        List<TDemandOrderGoods> byIds = demandOrderGoodsMapper.getDemandOrderGoodsByDemandOrderIds(demandOrderId);
        if (ListUtils.isNotEmpty(byIds)) {
            List<TDemandOrderGoodsRel> demandOrderGoodsRelList = tDemandOrderGoodsRelMapper.getByDemandOrderIds(demandOrderId);
            Map<Long,Long> goodsIdMap = new HashMap<>();
            if (ListUtils.isNotEmpty(demandOrderGoodsRelList)){
                demandOrderGoodsRelList.forEach(item -> goodsIdMap.put(item.getDemandOrderGoodsId(),item.getBookingOrderGoodsId()));
            }

            TDemandOrderGoods good;
            List<TDemandOrderGoods> orderGoodsList = new ArrayList<>();
            List<CompleteDemandOrderGoodsBackAmountModel> goodsBackList;
            CompleteDemandOrderGoodsBackAmountModel goodsBackAmountModel;
            for (TDemandOrderGoods goods : byIds) {
                BigDecimal goodsNotArrangedAmount=goods.getNotArrangedAmount();
                good = new TDemandOrderGoods();
                good.setId(goods.getId());
                good.setNotArrangedAmount(CommonConstant.BIG_DECIMAL_ZERO);
                good.setBackAmount(goods.getBackAmount().add(goods.getNotArrangedAmount()));
                commonBiz.setBaseEntityModify(good, BaseContextHandler.getUserName());
                orderGoodsList.add(good);

                //预约单货物信息
                if (goodsIdMap.get(good.getId()) != null){
                    goodsBackAmountModel = new CompleteDemandOrderGoodsBackAmountModel();
                    goodsBackAmountModel.setGoodsId(goodsIdMap.get(good.getId()));
                    goodsBackAmountModel.setArrangedAmount(goods.getArrangedAmount());
                    goodsBackAmountModel.setNotArrangedAmount(good.getNotArrangedAmount());
                    goodsBackAmountModel.setFallbackCount(good.getBackAmount());
                    goodsBackList = goodsBackMap.get(goods.getDemandOrderId());
                    if (ListUtils.isEmpty(goodsBackList)) {
                        goodsBackList = new ArrayList<>();
                    }
                    goodsBackList.add(goodsBackAmountModel);
                    goodsBackMap.put(goods.getDemandOrderId(),goodsBackList);
                }
                //手动完成调度，同步货物信息和退回数量
                if(DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(demandSourceMap.get(goods.getDemandOrderId()))){
                    List<ProductTypeAndBackAmountModel> productTypeAndBackAmountModels = demandBackAmountModelMap.computeIfAbsent(goods.getDemandOrderId(), k -> new ArrayList<>());
                    ProductTypeAndBackAmountModel productTypeAndBackAmountModel=new ProductTypeAndBackAmountModel();
                    productTypeAndBackAmountModel.setCompleteBackAmount(goodsNotArrangedAmount.intValue());
                    productTypeAndBackAmountModel.setProductTypeCode(goods.getSkuCode());
                    productTypeAndBackAmountModel.setSortName(goods.getGoodsName());
                    productTypeAndBackAmountModel.setCategoryName(goods.getCategoryName());
                    productTypeAndBackAmountModel.setWidth(ConverterUtils.toString(goods.getWidth()));
                    productTypeAndBackAmountModel.setLength(ConverterUtils.toString(goods.getLength()));
                    productTypeAndBackAmountModel.setHeight(ConverterUtils.toString(goods.getHeight()));

                    productTypeAndBackAmountModels.add(productTypeAndBackAmountModel);

                }
            }
            if (ListUtils.isNotEmpty(orderGoodsList)) {
                demandOrderGoodsMapper.batchUpdateByPrimaryKeySelective(orderGoodsList);
            }
        }

        if (ListUtils.isNotEmpty(relList)) {
            tDemandOrderOrderRelMapper.batchUpdateByPrimaryKeySelective(relList);
        }

        //需求单签收时生成结算信息
        if (ListUtils.isNotEmpty(completeDemandIdList)) {//需求单签收时生成运单结算信息
            //生成需求单结算信息
            CreateSettlementForEntrustConsumerModel createSettlementForEntrustConsumerModel = new CreateSettlementForEntrustConsumerModel();
            createSettlementForEntrustConsumerModel.setDemandOrderIds(StringUtils.listToString(completeDemandIdList,','));
            createSettlementForEntrustConsumerModel.setUserName(BaseContextHandler.getUserName());
            demandOrderBiz.createSettlementCost(createSettlementForEntrustConsumerModel);

            //需求单（HR单）签收同步给地推系统
            demandOrderCommonBiz.demandOrderSignSyncGroundPush(completeDemandIdList, BaseContextHandler.getUserName());
        }

        //同步云仓数据信息
        if (ListUtils.isNotEmpty(demandStateSynchronizeModels)) {
            for (LogisticsDemandStateSynchronizeModel model : demandStateSynchronizeModels) {
                amountModel = modelMap.get(model.getDemandCode());
                if (amountModel != null) {
                    model.setModels(amountModel.getModels());
                }
                if (ListUtils.isNotEmpty(goodsBackMap.get(model.getDemandOrderId()))){
                    model.setTLogisticsDemandGoodsItemModels(goodsBackMap.get(model.getDemandOrderId()));
                }
                model.setProductTypeModel(demandBackAmountModelMap.get(model.getDemandOrderId()));
            }
            rabbitMqPublishBiz.completeDemandOrderSyn(demandStateSynchronizeModels, CommonConstant.TWO);
        }
       //同步云盘数据信息
        if (ListUtils.isNotEmpty(dispatchCompleteModelList)) {
            DispatchCompleteModelMessage dispatchCompleteModelMessage=new DispatchCompleteModelMessage();
            MapperMapping<CompleteDemandOrderGoodsBackAmountModel,TLogisticsDemandGoodsItemModel> mapping= new MapperMapping<CompleteDemandOrderGoodsBackAmountModel,TLogisticsDemandGoodsItemModel>() {
                @Override
                public void configure() {
                    TLogisticsDemandGoodsItemModel destination = getDestination();
                    destination.setLastModifiedBy(BaseContextHandler.getUserName());
                    destination.setLastModifiedTime(now);
                }
            };
            //回退数量model
            List<DemandBackCountModel> demandBackCountModelList=new ArrayList<>();
            DemandBackCountModel demandBackCountModel=null;
            for (DispatchCompleteModel model : dispatchCompleteModelList) {
                if (ListUtils.isNotEmpty(goodsBackMap.get(model.getDemandOrderId()))){
                    List<CompleteDemandOrderGoodsBackAmountModel> completeDemandOrderGoodsBackAmountModels = goodsBackMap.get(model.getDemandOrderId());
                    model.setTLogisticsDemandGoodsItemModels(MapperUtils.mapper(completeDemandOrderGoodsBackAmountModels, TLogisticsDemandGoodsItemModel.class,mapping));
                }

                //有回退数，组装同步回退数mq的model
                if(model.getCompleteBackAmount()>CommonConstant.INTEGER_ZERO){
                    demandBackCountModel=new DemandBackCountModel();
                    demandBackCountModel.setCompleteBackAmount(model.getCompleteBackAmount());
                    demandBackCountModel.setDemandCode(model.getDemandCode());
                    demandBackCountModelList.add(demandBackCountModel);
                }
                //已签收，且有差异数且差异数<0  则退回数量=需求单未分配数量+差异数
                if(model.getDifferenceAmount()!=null && model.getIfSign() && model.getDifferenceAmount().compareTo(BigDecimal.ZERO)<CommonConstant.INTEGER_ZERO){
                    if(demandBackCountModel==null){
                        demandBackCountModel=new DemandBackCountModel();
                        demandBackCountModel.setCompleteBackAmount(model.getDifferenceAmount().abs().intValue());
                        demandBackCountModel.setDemandCode(model.getDemandCode());
                        demandBackCountModelList.add(demandBackCountModel);
                    }else{
                        demandBackCountModel.setCompleteBackAmount(demandBackCountModel.getCompleteBackAmount()+model.getDifferenceAmount().abs().intValue());
                    }
                }
            }
            dispatchCompleteModelMessage.setDispatchCompleteModelList(dispatchCompleteModelList);
            dispatchCompleteModelMessage.setUserName(BaseContextHandler.getUserName());
            rabbitMqPublishBiz.completeDemandOrderSynToLeYi(dispatchCompleteModelMessage, CommonConstant.TWO);

            if(ListUtils.isNotEmpty(demandBackCountModelList)){
                DemandBackCountMessage demandBackCountMessage=new DemandBackCountMessage();
                demandBackCountMessage.setDemandBackCountModelList(demandBackCountModelList);
                demandBackCountMessage.setUserName(BaseContextHandler.getUserName());

                log.info("完成调度同步云盘退回数量");
                rabbitMqPublishBiz.syncBackAmountToLeYi(demandBackCountMessage);

                //打标的回收入库类型且是自动发布，有回退数量需同步给智慧运营系统
                TDemandOrder demandOrder;
                RecyclePublishUpdateDemandRequestModel recyclePublishUpdateDemandRequestModel;
                List<RecyclePublishUpdateDemandRequestModel> recyclePublishUpdateDemandList = new ArrayList<>();
                for (DemandBackCountModel backCountModel : demandBackCountModelList) {
                    demandOrder = dbDemandOrderMap.get(backCountModel.getDemandCode());
                    if (demandOrder != null
                            && EntrustTypeEnum.RECYCLE_IN.getKey().equals(demandOrder.getEntrustType())
                            && StringUtils.isNotBlank(demandOrder.getFixedDemand())
                            && CommonConstant.INTEGER_ONE.equals(demandOrder.getAutoPublish())){

                        recyclePublishUpdateDemandRequestModel = new RecyclePublishUpdateDemandRequestModel();
                        recyclePublishUpdateDemandRequestModel.setConfigCode(demandOrder.getFixedDemand());
                        recyclePublishUpdateDemandRequestModel.setDemandOrderNo(demandOrder.getDemandOrderCode());
                        recyclePublishUpdateDemandRequestModel.setBackspaceNum(ConverterUtils.toBigDecimal(backCountModel.getCompleteBackAmount()));
                        recyclePublishUpdateDemandList.add(recyclePublishUpdateDemandRequestModel);
                    }
                }
                if (ListUtils.isNotEmpty(recyclePublishUpdateDemandList)){
                    commonBiz.synRecyclePublishUpdateDemand(recyclePublishUpdateDemandList);
                }
            }
        }

        //同步新生数据信息
        if (ListUtils.isNotEmpty(codeListSyncLife)) {
            SyncDemandOrderToYeloLifeModel<Object> syncDemandOrderToYeloLifeModel = new SyncDemandOrderToYeloLifeModel<>();
            syncDemandOrderToYeloLifeModel.setType(SyncDemandOrderToYeloLifeModel.SyncTypeEnum.COMPLETE_DISPATCH.getKey());
            syncDemandOrderToYeloLifeModel.setMsgData(codeListSyncLife);
            rabbitMqPublishBiz.syncDemandOrderToYeloLife(syncDemandOrderToYeloLifeModel);
        }
    }
    /**
     * 调度车辆-分页搜索司机
     * @param
     * @return
     */
    public PageInfo<DriverSearchResponseModel> searchDriver(DriverSearchRequestModel requestModel) {
        requestModel.enablePaging();
        List<DriverSearchResponseModel> driverSearchResponseModelList = new ArrayList<>();
        if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(requestModel.getIsOurCompany())){
            Long qiyaCompanyCarrierId = commonBiz.getQiyaCompanyCarrierId();
            requestModel.setCompanyCarrierId(qiyaCompanyCarrierId);
            //如果是我司查询全部司机
            driverSearchResponseModelList = tStaffBasicMapper.searchDriver(requestModel);
        }else if (IsOurCompanyEnum.OTHER_COMPANY.getKey().equals(requestModel.getIsOurCompany())){
            //如果是其他公司查询跟车主关联的司机
            driverSearchResponseModelList = tCarrierDriverRelationMapper.searchDriver(requestModel);
        }
        return new PageInfo<>(driverSearchResponseModelList);
    }


    /**
     * 调度车辆-分页搜索车辆
     *
     * @param requestModel 筛选条件
     * @return 结果
     */
    public PageInfo<VehicleSearchResponseModel> searchVehicle(VehicleSearchRequestModel requestModel) {
        Long companyCarrierId = CommonConstant.LONG_ZERO;
        List<VehicleSearchResponseModel> vehicleSearchResponseModelList = new ArrayList<>();
        if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(requestModel.getIsOurCompany())) {
            //查询我司id
            companyCarrierId = commonBiz.getQiyaCompanyCarrierId();
            requestModel.setCompanyCarrierId(companyCarrierId);
            //查询我司关联的车辆
            List<Integer> vehicleTypeList = new ArrayList<>();
            vehicleTypeList.add(VehicleCategoryEnum.TRACTOR.getKey());
            vehicleTypeList.add(VehicleCategoryEnum.WHOLE.getKey());
            //只查询 一体车,牵引车
            List<TVehicleType> tVehicleTypeList = tVehicleTypeMapper.selectByVehicleTypes(StringUtils.join(vehicleTypeList, ','));
            if (ListUtils.isNotEmpty(tVehicleTypeList)) {
                String vehicleTypeIds = StringUtils.join(tVehicleTypeList.stream().map(TVehicleType::getId).collect(Collectors.toList()), ',');
                requestModel.setVehicleTypeIds(vehicleTypeIds);
            } else {
                requestModel.setVehicleTypeIds(CommonConstant.ZERO);
            }
            requestModel.enablePaging();
            vehicleSearchResponseModelList = tVehicleBasicMapper.searchVehicleForOurCompany(requestModel);
            if (ListUtils.isNotEmpty(vehicleSearchResponseModelList)){
                String vehicleIdListStr = StringUtils.join(vehicleSearchResponseModelList.stream().map(VehicleSearchResponseModel::getVehicleId).collect(Collectors.toList()), ',');
                List<TStaffVehicleRelation> tStaffVehicleRelationList = tStaffVehicleRelationMapper.selectRelationsByVehicleIdDriverId(null, vehicleIdListStr, companyCarrierId);
                if (ListUtils.isNotEmpty(tStaffVehicleRelationList)){
                    Map<Long, Long> vehicleDriverMap = tStaffVehicleRelationList.stream().collect(Collectors.toMap(TStaffVehicleRelation::getVehicleId, TStaffVehicleRelation::getStaffId));
                    vehicleSearchResponseModelList.forEach(item -> item.setDriverId(vehicleDriverMap.get(item.getVehicleId())));
                }
            }
        } else if (IsOurCompanyEnum.OTHER_COMPANY.getKey().equals(requestModel.getIsOurCompany())) {
            //如果是其他公司查询跟车主关联的司机
            List<TCarrierVehicleRelation> carrierVehicleRelationList = tCarrierVehicleRelationMapper.getByCompanyCarrierId(requestModel.getCompanyCarrierId());
            if (ListUtils.isNotEmpty(carrierVehicleRelationList)) {
                requestModel.enablePaging();
                String vehicleIdListStr = StringUtils.join(carrierVehicleRelationList.stream().map(TCarrierVehicleRelation::getVehicleId).collect(Collectors.toList()), ',');
                vehicleSearchResponseModelList = tVehicleBasicMapper.searchVehicle(vehicleIdListStr, requestModel.getVehicleNo(), requestModel.getCompanyCarrierId());
            }
            companyCarrierId = requestModel.getCompanyCarrierId();
        }

        if (ListUtils.isNotEmpty(vehicleSearchResponseModelList)) {
            List<Long> driverIds = vehicleSearchResponseModelList.stream().filter(item -> item.getDriverId() != null).map(VehicleSearchResponseModel::getDriverId).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(driverIds)) {
                //查询车辆关联的司机
                List<DriverByVehiclesModel> driverVehiclesModelList = tStaffBasicMapper.selectDriverByVehicles(LocalStringUtil.listTostring(driverIds, ','), companyCarrierId, EnabledEnum.ENABLED.getKey());
                if (ListUtils.isNotEmpty(driverVehiclesModelList)) {
                    Map<Long, DriverByVehiclesModel> driverInfoMap = driverVehiclesModelList.stream().collect(Collectors.toMap(DriverByVehiclesModel::getStaffId, item -> item, (o1, o2) -> o2));
                    //拼接数据
                    for (VehicleSearchResponseModel vehicleSearchResponseModel : vehicleSearchResponseModelList) {
                        DriverByVehiclesModel driverByVehiclesModel = driverInfoMap.get(vehicleSearchResponseModel.getDriverId());
                        if (driverByVehiclesModel != null) {
                            vehicleSearchResponseModel.setDriverId(driverByVehiclesModel.getStaffId());
                            vehicleSearchResponseModel.setDriverName(driverByVehiclesModel.getDriverName());
                            vehicleSearchResponseModel.setDriverPhone(driverByVehiclesModel.getDriverPhone());
                            vehicleSearchResponseModel.setDriverIdentityNumber(driverByVehiclesModel.getDriverIdentityNumber());
                        }
                    }
                }
            }
        }
        return new PageInfo<>(vehicleSearchResponseModelList);
    }

    /**
     * 零担调度详情
     * @param requestModel
     * @return
     */
    public DemandOrderSpecialDispatchDetailResponseModel specialDispatchDetail(DemandOrderSpecialDispatchDetailRequestModel requestModel) {
        String demandOrderIds = StringUtils.listToString(requestModel.getDemandIdList(),',');
        //查询调度详情
        List<DemandOrderModel> demandOrderList = demandOrderMapper.getDemandOrderGoodsByDemandIds(demandOrderIds);
        DemandOrderSpecialDispatchDetailResponseModel responseModel = new DemandOrderSpecialDispatchDetailResponseModel();
        if (ListUtils.isEmpty(demandOrderList)) {
            return responseModel;
        }
        DemandOrderModel dispatchDetailModel = demandOrderList.get(CommonConstant.INTEGER_ZERO);


        List<Integer> entrustTypeList = new ArrayList<>();
        List<Integer> goodsUnitList = new ArrayList<>();
        List<Long> companyCarrierIds = new ArrayList<>();

        UserCompanyCarrierInfoResponseModel userAndCompanyInfo = null;
        //前台请求根据当前登陆人获取公司id
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            userAndCompanyInfo = companyCarrierBiz.getUserAndCompanyInfo();
        }
        //判断需求单状态
        for (DemandOrderModel order : demandOrderList) {
            if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource()) && userAndCompanyInfo != null) {//前台
                if (!order.getCompanyCarrierId().equals(userAndCompanyInfo.getCompanyCarrierId())) {//需求单不是当前车主的（更换车主后，前车主看到的是取消状态）
                    throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_CANCEL.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_CANCEL.getMsg());
                }
            }
            if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(order.getSource())){
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_NOT_OPERATION.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_NOT_OPERATION.getMsg());
            }
            //已取消的不能操作
            if (order.getIfCancel().equals(CommonConstant.INTEGER_ONE)) {
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_CANCEL.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_CANCEL.getMsg());
            } else if (order.getIfEmpty().equals(CommonConstant.INTEGER_ONE)) {
                //已放空的不能操作
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getMsg());
            } else if (order.getIfRollback().equals(CommonConstant.INTEGER_ONE)) {
                //已回退的不能操作
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_ROLLBACK.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_ROLLBACK.getMsg());
            } else if (CommonConstant.INTEGER_ONE.equals(order.getIfObjectionSinopec())) {
                //有异常的不能操作(中石化推送的单子)
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_OBJECTION_SINOPEC.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_OBJECTION_SINOPEC.getMsg());
            }
            //只有【待调度】【部分调度】状态才能操作
            if (!(DemandOrderStatusEnum.WAIT_DISPATCH.getKey().equals(order.getStatus()) || DemandOrderStatusEnum.PART_DISPATCH.getKey().equals(order.getStatus()))){
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_NOT_DISPATCH.getCode(), order.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_NOT_DISPATCH.getMsg());
            }
            if (!entrustTypeList.contains(order.getEntrustType())){
                entrustTypeList.add(order.getEntrustType());
            }
            if (!goodsUnitList.contains(order.getGoodsUnit())) {
                goodsUnitList.add(order.getGoodsUnit());
            }
            if (!companyCarrierIds.contains(order.getCompanyCarrierId())) {
                companyCarrierIds.add(order.getCompanyCarrierId());
            }
        }
        //回收类型不能与其他类型混合调度
        if (entrustTypeList.contains(EntrustTypeEnum.RECYCLE_IN.getKey()) || entrustTypeList.contains(EntrustTypeEnum.RECYCLE_OUT.getKey())) {
            entrustTypeList.remove(EntrustTypeEnum.RECYCLE_IN.getKey());
            entrustTypeList.remove(EntrustTypeEnum.RECYCLE_OUT.getKey());
            if (entrustTypeList.size() > CommonConstant.INTEGER_ZERO) {
                throw new BizException(CarrierDataExceptionEnum.RECYCLE_ORDER_DISPATCH);
            }
        }
        //不同单位的不能一起调度
        if (goodsUnitList.size() > CommonConstant.INTEGER_ONE){
            throw new BizException(EntrustDataExceptionEnum.GOODS_UNIT_DIFFERENT);
        }
        //不同车主，不能合并调度车辆
        if (companyCarrierIds.size() > CommonConstant.INTEGER_ONE){
            throw new BizException(EntrustDataExceptionEnum.ONLY_THE_SAME_OWNER_CAN_MERGE_AND_DISPATCH_VEHICLES);
        }
        //查询车主
        TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(companyCarrierIds.get(0));
        if (tCompanyCarrier == null || tCompanyCarrier.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.NO_COMPANY_CARRIER);
        }
        //判断车主是否有零担模式权限
        if (!YesOrNoEnum.YES.getKey().equals(tCompanyCarrier.getIfLessThanTruckload())){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_NOT_OPERATION);
        }
        //需求单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkDemandExceptionExist(demandOrderIds);
        if (!workOrderMap.isEmpty()){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EXCEPTION);
        }

        //调用大数据获取经纬度
        PathPlanResponseModel pathPlanResponseModel = this.getPathPlanResponseModel(demandOrderList);

        //匹配需求单model
        List<DemandOrderSpecialDispatchDetailListResponseModel> goodsList = this.getSortDemandOrderModels(demandOrderList, pathPlanResponseModel);

        //组装数据
        responseModel.setCompanyCarrierType(dispatchDetailModel.getCompanyCarrierType());
        responseModel.setCompanyCarrierId(dispatchDetailModel.getCompanyCarrierId());
        responseModel.setCompanyCarrierName(dispatchDetailModel.getCompanyCarrierName());
        responseModel.setCarrierContactName(dispatchDetailModel.getCarrierContactName());
        responseModel.setCarrierContactPhone(dispatchDetailModel.getCarrierContactPhone());
        responseModel.setIsOurCompany(tCompanyCarrier.getLevel());
        responseModel.setGoodsUnit(dispatchDetailModel.getGoodsUnit());
        responseModel.setLoadPointAmount(demandOrderList.size());
        responseModel.setUnloadPointAmount(CommonConstant.INTEGER_ONE);
        responseModel.setGoodsList(goodsList);
        if (pathPlanResponseModel != null) {
            responseModel.setCrossPointDistance(pathPlanResponseModel.getTotal_distance());
        }

        return responseModel;
    }

    public List<DemandOrderSpecialDispatchDetailListResponseModel> getSortDemandOrderModels(List<DemandOrderModel> demandOrderList, PathPlanResponseModel pathPlanResponseModel) {
        DemandOrderSpecialDispatchDetailListResponseModel demandOrderDetailModel;
        List<DemandOrderSpecialDispatchDetailListResponseModel> goodsList = new ArrayList<>();
        //存在路线规划
        if (pathPlanResponseModel != null){
            Map<String, List<DemandOrderModel>> demandOrderMap = demandOrderList
                    .stream()
                    .filter(f -> StringUtils.isNotBlank(f.getLoadLongitude()) && StringUtils.isNotBlank(f.getLoadLatitude()))
                    .collect(Collectors.groupingBy(g -> g.getLoadLongitude() + "," + g.getLoadLatitude(),
                            Collectors.mapping(Function.identity(), Collectors.toList())));
            //去掉卸货地址经纬度
            pathPlanResponseModel.getPoints_position().remove(pathPlanResponseModel.getPoints_position().size() - CommonConstant.INTEGER_ONE);

            AtomicInteger atomicInteger = new AtomicInteger(CommonConstant.INTEGER_ZERO);
            //按顺序遍历路线
            for (PathPlanListResponseModel location : pathPlanResponseModel.getPoints_position()) {
                List<DemandOrderModel> demandOrderModelList = demandOrderMap.get(location.getLongitude() + "," + location.getLatitude());
                if (ListUtils.isNotEmpty(demandOrderModelList)){
                    for (DemandOrderModel demandOrderModel : demandOrderModelList) {
                        int orderNum = atomicInteger.incrementAndGet();
                        for (DemandOrderGoodsModel goodsModel : demandOrderModel.getGoodsList()) {
                            demandOrderDetailModel = MapperUtils.mapper(demandOrderModel, DemandOrderSpecialDispatchDetailListResponseModel.class);
                            demandOrderDetailModel.setDemandOrderGoodsId(goodsModel.getDemandOrderGoodsId());
                            demandOrderDetailModel.setGoodsName(goodsModel.getGoodsName());
                            demandOrderDetailModel.setGoodsSize(goodsModel.getGoodsSize());
                            demandOrderDetailModel.setLength(goodsModel.getLength());
                            demandOrderDetailModel.setWidth(goodsModel.getWidth());
                            demandOrderDetailModel.setHeight(goodsModel.getHeight());
                            demandOrderDetailModel.setNotArrangedAmount(goodsModel.getNotArrangedAmount());
                            demandOrderDetailModel.setOrderNum(orderNum);
                            demandOrderDetailModel.setNextPointDistance(location.getDistance_next_point());
                            goodsList.add(demandOrderDetailModel);
                        }
                    }
                }
            }

        }
        //不存在路线规划
        else{
            for (DemandOrderModel demandOrderModel : demandOrderList) {
                for (DemandOrderGoodsModel goodsModel : demandOrderModel.getGoodsList()) {
                    demandOrderDetailModel = MapperUtils.mapper(demandOrderModel, DemandOrderSpecialDispatchDetailListResponseModel.class);
                    demandOrderDetailModel.setDemandOrderGoodsId(goodsModel.getDemandOrderGoodsId());
                    demandOrderDetailModel.setGoodsName(goodsModel.getGoodsName());
                    demandOrderDetailModel.setGoodsSize(goodsModel.getGoodsSize());
                    demandOrderDetailModel.setLength(goodsModel.getLength());
                    demandOrderDetailModel.setWidth(goodsModel.getWidth());
                    demandOrderDetailModel.setHeight(goodsModel.getHeight());
                    demandOrderDetailModel.setNotArrangedAmount(goodsModel.getNotArrangedAmount());

                    goodsList.add(demandOrderDetailModel);
                }
            }
        }
        return goodsList;
    }

    public PathPlanResponseModel getPathPlanResponseModel(List<DemandOrderModel> demandOrderList) {

        DemandOrderModel dispatchDetailModel = demandOrderList.get(CommonConstant.INTEGER_ZERO);


        PathPlanRequestModel pathPlanRequestModel;
        List<PathPlanRequestModel> pathPlanRequestModelList = new ArrayList<>();
        Map<String, String> demandOrderCodeMap = new HashMap<>();//经纬度-》需求单号
        //获取需求单卸货经纬度存在的需求单
        DemandOrderModel DemandOrderEndPointModel = demandOrderList
                .stream()
                .filter(f -> StringUtils.isNotBlank(f.getUnloadLongitude()) && StringUtils.isNotBlank(f.getUnloadLatitude()))
                .findFirst()
                .orElse(null);
        //校验卸货地址是否一致
        Map<Long, String> areaIdAddressMap = new HashMap<>();
        areaIdAddressMap.put(dispatchDetailModel.getUnloadAreaId(), dispatchDetailModel.getUnloadDetailAddress());
        for (DemandOrderModel model : demandOrderList) {
            //需求单卸货地址必须一致（省市区+详细地址）
            if (areaIdAddressMap.get(model.getUnloadAreaId()) == null
                    || !areaIdAddressMap.get(model.getUnloadAreaId()).equals(model.getUnloadDetailAddress())){
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_NOT_OPERATION);
            }

            //组装提货经纬度
            if (DemandOrderEndPointModel != null
                    && StringUtils.isNotBlank(model.getLoadLongitude())
                    && StringUtils.isNotBlank(model.getLoadLatitude())) {
                String location = model.getLoadLongitude() + "," + model.getLoadLatitude();
                if (demandOrderCodeMap.get(location) == null) {
                    pathPlanRequestModel = new PathPlanRequestModel();
                    pathPlanRequestModel.setLongitude(model.getLoadLongitude());
                    pathPlanRequestModel.setLatitude(model.getLoadLatitude());
                    pathPlanRequestModelList.add(pathPlanRequestModel);

                    demandOrderCodeMap.put(location, model.getDemandOrderCode());
                }
            }
        }

        //存在串点地址才请求大数据接口（除了起点和终点外，还存在其他提货地点）
        PathPlanResponseModel pathPlanResponseModel = null;
        if (DemandOrderEndPointModel != null && pathPlanRequestModelList.size() > CommonConstant.INTEGER_ONE) {
            //将卸货经纬度放在最后
            pathPlanRequestModel = new PathPlanRequestModel();
            pathPlanRequestModel.setLongitude(DemandOrderEndPointModel.getUnloadLongitude());
            pathPlanRequestModel.setLatitude(DemandOrderEndPointModel.getUnloadLatitude());
            pathPlanRequestModelList.add(pathPlanRequestModel);

            //调大数据接口-路线规划
            pathPlanResponseModel = bigDataClient.pathPlan(pathPlanRequestModelList);
        }
        return pathPlanResponseModel;
    }

    /**
     * 零担调度车辆
     * @param requestModel
     * @return
     */
    @Transactional
    public void specialDispatchVehicle(SpecialDispatchVehicleRequestModel requestModel) {
        DispatchRequestModel dispatchRequestModel = MapperUtils.mapper(requestModel, DispatchRequestModel.class);
        dispatchRequestModel.setIfLessThanTruckload(YesOrNoEnum.YES.getKey());
        this.dispatchVehicle(dispatchRequestModel);
    }

    @Transactional
    public AssociateExtDemandOrderRespModel associateExtDemandOrder(AssociateExtDemandOrderReqModel requestModel) {
        DispatchRequestModel dispatchRequestModel = new DispatchRequestModel();

        // 将demandOrderId转换为Long类型
        Long demandOrderId = Long.valueOf(requestModel.getDemandOrderId());

        // 1. 根据demandOrderId在t_demand_order_address中查询expected_unload_time作为expectArrivalTime
        TDemandOrderAddress demandOrderAddress = demandOrderAddressMapper.getByDemandOrderId(demandOrderId);
        if (demandOrderAddress != null && demandOrderAddress.getExpectedUnloadTime() != null) {
            dispatchRequestModel.setExpectArrivalTime(demandOrderAddress.getExpectedUnloadTime());
        }

        // 2. 根据demandOrderId在t_ext_demand_order_relation中查询carrier_order_code和carrier_order_id
        TExtDemandOrderRelation extDemandOrderRelation = tExtDemandOrderRelationMapper.selectByExtDemandOrderId(demandOrderId);

        if (extDemandOrderRelation == null) {
            throw new BizException("未找到对应的运单关联信息");
        }

        String carrierOrderCode = extDemandOrderRelation.getCarrierOrderCode();
        Long carrierOrderId = extDemandOrderRelation.getCarrierOrderId();

        // 3. 根据carrier_order_id在t_carrier_order_vehicle_history中查询最新的一条记录获取driverId, vehicleId, trailerVehicleId
        TCarrierOrderVehicleHistory vehicleHistory = carrierOrderVehicleHistoryMapper.getValidTopByCarrierOrderId(carrierOrderId);

        if (vehicleHistory != null) {
            dispatchRequestModel.setDriverId(vehicleHistory.getDriverId());
            dispatchRequestModel.setVehicleId(vehicleHistory.getVehicleId());
            dispatchRequestModel.setTrailerVehicleId(vehicleHistory.getTrailerVehicleId());
        }

        // 4. 根据carrier_order_code在t_carrier_order中查询dispatch_freight_fee_type和dispatch_freight_fee
        TCarrierOrder carrierOrder = tCarrierOrderMapper.getByCode(carrierOrderCode);
        if (null == carrierOrder) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        dispatchRequestModel.setDispatchFreightFeeType(carrierOrder.getDispatchFreightFeeType());
        dispatchRequestModel.setDispatchFreightFee(carrierOrder.getDispatchFreightFee());

        // 5. 根据dispatch_order_code在t_dispatch_order中查询remark
        if (StringUtils.isNotBlank(carrierOrder.getDispatchOrderCode())) {
            TDispatchOrder dispatchOrder = dispatchOrderMapper.selectByDispatchOrderCode(carrierOrder.getDispatchOrderCode());
            if (dispatchOrder != null) {
                dispatchRequestModel.setRemark(dispatchOrder.getRemark());
            }
        }


        // 6. 根据demandOrderId在t_demand_order_goods中查询货物信息设置vehicleRequestModels
        List<TDemandOrderGoods> demandOrderGoodsList = tDemandOrderGoodsMapper.selectByDemandOrderId(demandOrderId);
        if (ListUtils.isNotEmpty(demandOrderGoodsList)) {
            List<DispatchGoodsModel> vehicleRequestModels = new ArrayList<>();
            for (TDemandOrderGoods demandOrderGoods : demandOrderGoodsList) {
                DispatchGoodsModel dispatchGoodsModel = new DispatchGoodsModel();
                dispatchGoodsModel.setDemandOrderGoodsId(demandOrderGoods.getId());
                dispatchGoodsModel.setDemandOrderId(demandOrderGoods.getDemandOrderId());
                dispatchGoodsModel.setLoadAmount(demandOrderGoods.getGoodsAmount());
                vehicleRequestModels.add(dispatchGoodsModel);
            }
            dispatchRequestModel.setVehicleRequestModels(vehicleRequestModels);
        }

        // 7. 设置固定值
        dispatchRequestModel.setLoadPointAmount(1);
        dispatchRequestModel.setUnloadPointAmount(1);
        dispatchRequestModel.setMarkupFee(null);
        dispatchRequestModel.setIfProvisionalPricing(0);
        dispatchRequestModel.setCarrierPriceType(null);
        dispatchRequestModel.setIfLessThanTruckload(0);
        dispatchRequestModel.setVehicleLength(null);
        dispatchRequestModel.setCrossPointFee(null);
        dispatchRequestModel.setCarrierFreight(null);
        dispatchRequestModel.setSource(1);
        dispatchRequestModel.setIfMatch(0);
        dispatchRequestModel.setShippingOrderCode(null);

        // 调用调度车辆方法
        this.dispatchVehicle(dispatchRequestModel);


        // 返回结果
        AssociateExtDemandOrderRespModel respModel = new AssociateExtDemandOrderRespModel();
        respModel.setCarrierOrderId(carrierOrderId.toString());

        List<TCarrierOrderGoods> carrierOrderGoodsList = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderId(carrierOrderId);
        CarrierOrderNodeGoodsRequestModel carrierOrderNodeGoodsRequestModel;
        List<CarrierOrderNodeGoodsRequestModel> carrierGoodsRequestModelList = new ArrayList<>();
        for (TCarrierOrderGoods originalCarrierGoods : carrierOrderGoodsList) {
            carrierOrderNodeGoodsRequestModel = new CarrierOrderNodeGoodsRequestModel();
            carrierOrderNodeGoodsRequestModel.setGoodsId(originalCarrierGoods.getId());
            carrierOrderNodeGoodsRequestModel.setCount(originalCarrierGoods.getExpectAmount());
            carrierGoodsRequestModelList.add(carrierOrderNodeGoodsRequestModel);
        }

        // 关联运单到达提货地
        ReachLoadAddressRequestModel reachLoadAddressRequestModel = new ReachLoadAddressRequestModel();
        reachLoadAddressRequestModel.setCarrierOrderId(Collections.singletonList(carrierOrderId));
        reachLoadAddressRequestModel.setSource(requestModel.getSource());
        carrierOrderBiz.reachLoadAddress(reachLoadAddressRequestModel);

        // 关联运单提货
        CarrierOrderLoadRequestModel loadRequest = new CarrierOrderLoadRequestModel();
        LoadRequestModel loadRequestModel = new LoadRequestModel();
        loadRequestModel.setCarrierOrderId(carrierOrderId);
        loadRequestModel.setGoodsUnit(carrierOrder.getGoodsUnit());
        loadRequestModel.setLoadTime(new Date());
        loadRequestModel.setTmpUrl(requestModel.getFilesList().stream().map(LoadFiles::getRelativeFilepath).collect(Collectors.toList()));
        loadRequestModel.setGoodsList(carrierGoodsRequestModelList);
        loadRequest.setSource(requestModel.getSource());
        carrierOrderBiz.load(loadRequest);
        return respModel;
    }

}
