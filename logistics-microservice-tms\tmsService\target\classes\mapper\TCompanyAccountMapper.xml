<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCompanyAccountMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCompanyAccount" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="bank_account" property="bankAccount" jdbcType="VARCHAR" />
    <result column="bank_account_name" property="bankAccountName" jdbcType="VARCHAR" />
    <result column="bra_bank_name" property="braBankName" jdbcType="VARCHAR" />
    <result column="bank_code" property="bankCode" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="enabled" property="enabled" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, bank_account, bank_account_name, bra_bank_name, bank_code, remark, enabled, created_by, 
    created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_company_account
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_company_account
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCompanyAccount" >
    insert into t_company_account (id, bank_account, bank_account_name, 
      bra_bank_name, bank_code, remark, 
      enabled, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{bankAccount,jdbcType=VARCHAR}, #{bankAccountName,jdbcType=VARCHAR}, 
      #{braBankName,jdbcType=VARCHAR}, #{bankCode,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{enabled,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCompanyAccount" useGeneratedKeys="true" keyProperty="id">
    insert into t_company_account
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="bankAccount != null" >
        bank_account,
      </if>
      <if test="bankAccountName != null" >
        bank_account_name,
      </if>
      <if test="braBankName != null" >
        bra_bank_name,
      </if>
      <if test="bankCode != null" >
        bank_code,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="enabled != null" >
        enabled,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bankAccount != null" >
        #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountName != null" >
        #{bankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="braBankName != null" >
        #{braBankName,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null" >
        #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null" >
        #{enabled,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCompanyAccount" >
    update t_company_account
    <set >
      <if test="bankAccount != null" >
        bank_account = #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountName != null" >
        bank_account_name = #{bankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="braBankName != null" >
        bra_bank_name = #{braBankName,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null" >
        bank_code = #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null" >
        enabled = #{enabled,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCompanyAccount" >
    update t_company_account
    set bank_account = #{bankAccount,jdbcType=VARCHAR},
      bank_account_name = #{bankAccountName,jdbcType=VARCHAR},
      bra_bank_name = #{braBankName,jdbcType=VARCHAR},
      bank_code = #{bankCode,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      enabled = #{enabled,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>