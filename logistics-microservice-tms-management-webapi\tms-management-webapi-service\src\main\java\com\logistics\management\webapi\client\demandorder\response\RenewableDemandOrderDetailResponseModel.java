package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/29
 */
@Data
public class RenewableDemandOrderDetailResponseModel {

	@ApiModelProperty("需求单id")
	private Long demandId;

	@ApiModelProperty("需求单号")
	private String demandOrderCode;

	@ApiModelProperty("客户单号")
	private String customerOrderCode;

	@ApiModelProperty("需求单状态")
	private Integer entrustStatus;

	@ApiModelProperty("是否取消 0:否 1:是")
	private Integer ifCancel;

	@ApiModelProperty("乐橘新生客户名称（企业）")
	private String customerName;
	@ApiModelProperty("乐橘新生客户姓名（个人）")
	private String customerUserName;
	@ApiModelProperty("乐橘新生客户手机号（个人）")
	private String customerUserMobile;

	@ApiModelProperty("业务类型：1 公司，2 个人")
	private Integer businessType;

	@ApiModelProperty("发布人")
	private String publishName;

	@ApiModelProperty("发布人手机号")
	private String publishMobile;

	@ApiModelProperty("发布时间")
	private Date publishTime;

	@ApiModelProperty("货物单位：1 件，2 吨，3 方，4 块")
	private Integer goodsUnit;

	@ApiModelProperty("备注")
	private String remark;

	/*发货信息*/
	private String loadProvinceName;
	private String loadCityName;
	private String loadAreaName;
	private String loadDetailAddress;
	private String loadWarehouse;
	private String consignorName;
	private String consignorMobile;
	private Date expectedLoadTime;

	/*收货信息*/
	private String unloadProvinceName;
	private String unloadCityName;
	private String unloadAreaName;
	private String unloadDetailAddress;
	private String unloadWarehouse;
	private String receiverName;
	private String receiverMobile;
	private Date expectedUnloadTime;

	@ApiModelProperty("货物数量")
	private BigDecimal goodsAmount;

	@ApiModelProperty("预计货主价格类型")
	private Integer expectContractPriceType;

	@ApiModelProperty("预计货主价格")
	private BigDecimal expectContractPrice;

	@ApiModelProperty("货主价格类型")
	private Integer contractPriceType;

	@ApiModelProperty("货主价格")
	private BigDecimal contractPrice;

	@ApiModelProperty("车主id")
	private Long companyCarrierId;
	@ApiModelProperty("车主名称")
	private String companyCarrierName;
	@ApiModelProperty("车主公司类型")
	private Integer companyCarrierType;

	@ApiModelProperty("车主联系人")
	private String carrierContactName;

	@ApiModelProperty("车主联系人电话")
	private String carrierContactPhone;

	@ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
	private Integer isOurCompany;

	@ApiModelProperty("报价类型 1 单价 2 整车价")
	private Integer settlementPriceType;

	@ApiModelProperty("需求单结算费用合计")
	private BigDecimal settlementCostTotal;

	@ApiModelProperty("结算数量")
	private BigDecimal settlementAmount;

	@ApiModelProperty("客户订单来源：1 乐橘新生客户，2 司机")
	private Integer customerOrderSource;

	@ApiModelProperty("需求单货物信息")
	private List<DemandOrderGoodsResponseModel> goodsResponseModel;

	@ApiModelProperty("需求单下运单信息")
	private List<DemandOrderCarrierResponseModel> carrierResponseModel;
}
