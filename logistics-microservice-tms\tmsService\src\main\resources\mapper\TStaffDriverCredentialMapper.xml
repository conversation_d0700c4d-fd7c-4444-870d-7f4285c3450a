<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TStaffDriverCredentialMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TStaffDriverCredential" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="staff_id" property="staffId" jdbcType="BIGINT" />
    <result column="occupational_requirements_credential_no" property="occupationalRequirementsCredentialNo" jdbcType="VARCHAR" />
    <result column="initial_issuance_date" property="initialIssuanceDate" jdbcType="TIMESTAMP" />
    <result column="drivers_license_no" property="driversLicenseNo" jdbcType="VARCHAR" />
    <result column="permitted_type" property="permittedType" jdbcType="VARCHAR" />
    <result column="drivers_license_date_from" property="driversLicenseDateFrom" jdbcType="TIMESTAMP" />
    <result column="drivers_license_date_to" property="driversLicenseDateTo" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, staff_id, occupational_requirements_credential_no, initial_issuance_date, drivers_license_no, 
    permitted_type, drivers_license_date_from, drivers_license_date_to, created_by, created_time, 
    last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_staff_driver_credential
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_staff_driver_credential
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TStaffDriverCredential" >
    insert into t_staff_driver_credential (id, staff_id, occupational_requirements_credential_no,
      initial_issuance_date, drivers_license_no, 
      permitted_type, drivers_license_date_from, 
      drivers_license_date_to, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{staffId,jdbcType=BIGINT}, #{occupationalRequirementsCredentialNo,jdbcType=VARCHAR}, 
      #{initialIssuanceDate,jdbcType=TIMESTAMP}, #{driversLicenseNo,jdbcType=VARCHAR}, 
      #{permittedType,jdbcType=VARCHAR}, #{driversLicenseDateFrom,jdbcType=TIMESTAMP}, 
      #{driversLicenseDateTo,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TStaffDriverCredential" useGeneratedKeys="true" keyProperty="id">
    insert into t_staff_driver_credential
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="staffId != null" >
        staff_id,
      </if>
      <if test="occupationalRequirementsCredentialNo != null" >
        occupational_requirements_credential_no,
      </if>
      <if test="initialIssuanceDate != null" >
        initial_issuance_date,
      </if>
      <if test="driversLicenseNo != null" >
        drivers_license_no,
      </if>
      <if test="permittedType != null" >
        permitted_type,
      </if>
      <if test="driversLicenseDateFrom != null" >
        drivers_license_date_from,
      </if>
      <if test="driversLicenseDateTo != null" >
        drivers_license_date_to,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="staffId != null" >
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="occupationalRequirementsCredentialNo != null" >
        #{occupationalRequirementsCredentialNo,jdbcType=VARCHAR},
      </if>
      <if test="initialIssuanceDate != null" >
        #{initialIssuanceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="driversLicenseNo != null" >
        #{driversLicenseNo,jdbcType=VARCHAR},
      </if>
      <if test="permittedType != null" >
        #{permittedType,jdbcType=VARCHAR},
      </if>
      <if test="driversLicenseDateFrom != null" >
        #{driversLicenseDateFrom,jdbcType=TIMESTAMP},
      </if>
      <if test="driversLicenseDateTo != null" >
        #{driversLicenseDateTo,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TStaffDriverCredential" >
    update t_staff_driver_credential
    <set >
      <if test="staffId != null" >
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="occupationalRequirementsCredentialNo != null" >
        occupational_requirements_credential_no = #{occupationalRequirementsCredentialNo,jdbcType=VARCHAR},
      </if>
      <if test="initialIssuanceDate != null" >
        initial_issuance_date = #{initialIssuanceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="driversLicenseNo != null" >
        drivers_license_no = #{driversLicenseNo,jdbcType=VARCHAR},
      </if>
      <if test="permittedType != null" >
        permitted_type = #{permittedType,jdbcType=VARCHAR},
      </if>
      <if test="driversLicenseDateFrom != null" >
        drivers_license_date_from = #{driversLicenseDateFrom,jdbcType=TIMESTAMP},
      </if>
      <if test="driversLicenseDateTo != null" >
        drivers_license_date_to = #{driversLicenseDateTo,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TStaffDriverCredential" >
    update t_staff_driver_credential
    set staff_id = #{staffId,jdbcType=BIGINT},
      occupational_requirements_credential_no = #{occupationalRequirementsCredentialNo,jdbcType=VARCHAR},
      initial_issuance_date = #{initialIssuanceDate,jdbcType=TIMESTAMP},
      drivers_license_no = #{driversLicenseNo,jdbcType=VARCHAR},
      permitted_type = #{permittedType,jdbcType=VARCHAR},
      drivers_license_date_from = #{driversLicenseDateFrom,jdbcType=TIMESTAMP},
      drivers_license_date_to = #{driversLicenseDateTo,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>