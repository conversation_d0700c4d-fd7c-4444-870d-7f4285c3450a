<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderTicketsAuditMapper">

    <select id="searchList" resultType="com.logistics.tms.controller.carrierorderticketsaudit.response.SearchReceiptAuditListResponseModel">
        select
        id receiptAuditId,
        carrier_order_id carrierOrderId,
        tickets_audit_status auditStatus,
        tickets_audit_time ticketAuditTime,
        remark,
        last_modified_by lastModifiedBy,
        last_modified_time lastModifiedTime
        from t_carrier_order_tickets_audit
        where valid = 1
        <if test="requestModel.carrierOrderIds != null and requestModel.carrierOrderIds.size() > 0">
            and carrier_order_id IN
            <foreach collection="requestModel.carrierOrderIds" item="carrierOrderId" open="(" separator="," close=")">
                #{carrierOrderId}
            </foreach>
        </if>
        <if test="requestModel.auditStatus != null">
            and tickets_audit_status = #{requestModel.auditStatus}
        </if>
        <if test="requestModel.ticketAuditStartDate != null and requestModel.ticketAuditStartDate != ''">
            and tickets_audit_time &gt;= DATE_FORMAT(#{requestModel.ticketAuditStartDate}, '%Y-%m-%d 00:00:00')
        </if>
        <if test="requestModel.ticketAuditEndDate != null and requestModel.ticketAuditEndDate != ''">
            and tickets_audit_time &lt;= DATE_FORMAT(#{requestModel.ticketAuditEndDate}, '%Y-%m-%d 23:59:59')
        </if>
        order by last_modified_time desc , id desc
    </select>

    <select id="selectOneById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_order_tickets_audit
        where valid = 1
        and id = #{auditId}
    </select>

    <select id="selectOneByCarrierOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_order_tickets_audit
        where valid = 1
        and carrier_order_id = #{carrierOrderId}
    </select>

    <select id="selectAllByCarrierOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_order_tickets_audit
        where valid = 1
        and carrier_order_id IN
        <foreach collection="carrierOrderIds" item="carrierOrderId" open="(" separator="," close=")">
            #{carrierOrderId}
        </foreach>
    </select>

    <update id="updateAudit">
        update t_carrier_order_tickets_audit
        <set>
            <if test="entity.ticketsAuditStatus != null">
                tickets_audit_status = #{entity.ticketsAuditStatus},
            </if>
            <if test="entity.ticketsAuditorName != null and entity.ticketsAuditorName != ''">
                tickets_auditor_name = #{entity.ticketsAuditorName},
            </if>
            <if test="entity.ticketsAuditTime != null">
                tickets_audit_time = #{entity.ticketsAuditTime},
            </if>
            <if test="entity.ifAutomaticAudit != null">
                if_automatic_audit = #{entity.ifAutomaticAudit},
            </if>
            <if test="entity.remark != null">
                remark = #{entity.remark},
            </if>
            <if test="entity.lastModifiedBy != null and entity.lastModifiedBy != ''">
                last_modified_by = #{entity.lastModifiedBy},
            </if>
            <if test="entity.lastModifiedTime != null">
                last_modified_time = #{entity.lastModifiedTime},
            </if>
        </set>
        where valid = 1
        and id = #{entity.id}
        <if test="currentStatus != null">
            and tickets_audit_status = #{currentStatus}
        </if>
    </update>

    <insert id="batchInsert">
        <foreach collection="list" item="item" separator=";">
            insert into t_carrier_order_tickets_audit
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.carrierOrderId != null">
                    carrier_order_id,
                </if>
                <if test="item.ticketsAuditStatus != null">
                    tickets_audit_status,
                </if>
                <if test="item.ticketsAuditorName != null">
                    tickets_auditor_name,
                </if>
                <if test="item.ticketsAuditTime != null">
                    tickets_audit_time,
                </if>
                <if test="item.ifAutomaticAudit != null">
                    if_automatic_audit,
                </if>
                <if test="item.remark != null">
                    remark,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.carrierOrderId != null">
                    #{item.carrierOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.ticketsAuditStatus != null">
                    #{item.ticketsAuditStatus,jdbcType=INTEGER},
                </if>
                <if test="item.ticketsAuditorName != null">
                    #{item.ticketsAuditorName,jdbcType=VARCHAR},
                </if>
                <if test="item.ticketsAuditTime != null">
                    #{item.ticketsAuditTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.ifAutomaticAudit != null">
                    #{item.ifAutomaticAudit,jdbcType=INTEGER},
                </if>
                <if test="item.remark != null">
                    #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>
</mapper>