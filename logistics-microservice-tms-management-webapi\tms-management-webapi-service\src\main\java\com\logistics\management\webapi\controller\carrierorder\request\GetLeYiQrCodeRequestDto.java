package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2023/11/13 9:24
 */
@Data
public class GetLeYiQrCodeRequestDto {
    @ApiModelProperty(value = "运单ID", required = true)
    @NotBlank(message = "id不能为空")
    private String carrierOrderId;
}
