package com.logistics.appapi.controller.reservationorder.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/14 14:32
 */
@Data
public class WaitReservationResponseDto {

    /**
     * 预约类型：1 提货，2 卸货
     */
    private String reservationType="";

    /**
     * 预约类型
     */
    private String reservationTypeLabel="";

    /**
     * 单子数量
     */
    private String orderCount="";

    /**
     * 按地址聚合的运单列表
     */
    private List<WaitReservationListResponseDto> orderList=new ArrayList<>();
}
