package com.logistics.management.webapi.api.impl.violationregulation.mapper;

import com.logistics.management.webapi.api.feign.violationregulation.dto.CertificationPicturesResponseDto;
import com.logistics.management.webapi.api.feign.violationregulation.dto.ViolationRegulationDetailResponseDto;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.tms.api.feign.violationregulation.model.ViolationRegulationDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/6/4 10:34
 */
public class ViolationRegulationDetailMapping extends MapperMapping<ViolationRegulationDetailResponseModel,ViolationRegulationDetailResponseDto> {
    private ConfigKeyConstant configKeyConstant;
    private Map<String, String> imageMap;

    public ViolationRegulationDetailMapping(ConfigKeyConstant configKeyConstant, Map<String, String> imageMap) {
        this.configKeyConstant = configKeyConstant;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        ViolationRegulationDetailResponseModel source = this.getSource();
        ViolationRegulationDetailResponseDto destination = this.getDestination();
        if(source!=null){
          if(StringUtils.isNotBlank(source.getDriverName()) && StringUtils.isNotBlank(source.getDriverPhone())){
            destination.setDriverLabel(source.getDriverName()+" "+source.getDriverPhone());
          }
          if(source.getOccuranceTime()!=null){
              destination.setOccuranceTime(DateUtils.dateToString(source.getOccuranceTime(),DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
          }
          if(ListUtils.isNotEmpty(destination.getFileList())){
              for (CertificationPicturesResponseDto tempCertificationDto : destination.getFileList() ) {
                  tempCertificationDto.setAbsoluteFilePath(configKeyConstant.fileAccessAddress+imageMap.get(tempCertificationDto.getRelativeFilepath()));
              }
          }
        }
    }
}
