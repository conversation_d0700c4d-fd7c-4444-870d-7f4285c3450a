package com.logistics.tms.biz.demandorder;

import com.leyi.auth.service.client.common.AuthCommonConstants;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.DemandOrderStatusEnum;
import com.logistics.tms.base.enums.EntrustTypeEnum;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.base.utils.MysqlAESUtils;
import com.logistics.tms.biz.carriervehiclerel.model.CarrierVehicleRelAndInfoModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.demandorder.model.AutoPublishRecycleDemandOrderModel;
import com.logistics.tms.biz.demandorder.model.AutoPublishWaitDispatchDemandModel;
import com.logistics.tms.biz.demandorder.model.FixedDemandOrderModel;
import com.logistics.tms.biz.demandorder.model.PublishAndDispatchModel;
import com.logistics.tms.biz.dispatch.DispatchBiz;
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleRelationInfoModel;
import com.logistics.tms.biz.workordercenter.WorkOrderBiz;
import com.logistics.tms.client.BigDataClient;
import com.logistics.tms.client.model.*;
import com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.tms.controller.demandorder.request.BatchPublishDemandModel;
import com.logistics.tms.controller.demandorder.request.BatchPublishRequestModel;
import com.logistics.tms.controller.dispatch.request.DispatchGoodsModel;
import com.logistics.tms.controller.dispatch.request.DispatchRequestModel;
import com.logistics.tms.entity.TCarrierVehicleRelation;
import com.logistics.tms.entity.TDemandOrderGoods;
import com.logistics.tms.entity.TStaffBasic;
import com.logistics.tms.entity.TStaffVehicleRelation;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.MapUtils;
import com.yelo.tools.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/23
 */
@Slf4j
@Service
public class DemandOrderTaskBiz {

	@Autowired
	private TStaffVehicleRelationMapper tStaffVehicleRelationMapper;
	@Autowired
	private TStaffBasicMapper tStaffBasicMapper;
	@Autowired
	private TVehicleBasicMapper tVehicleBasicMapper;
	@Autowired
	private TDemandOrderGoodsMapper tDemandOrderGoodsMapper;
	@Autowired
	private TDemandOrderMapper tDemandOrderMapper;
	@Autowired
	private TCompanyCarrierMapper tCompanyCarrierMapper;
	@Autowired
	private TCarrierVehicleRelationMapper tCarrierVehicleRelationMapper;
	@Autowired
	private DispatchBiz dispatchBiz;
	@Autowired
	private DemandOrderForLeYiBiz demandOrderForLeYiBiz;
	@Autowired
	private CommonBiz commonBiz;
	@Autowired
	private BigDataClient bigDataClient;
	@Autowired
	private WorkOrderBiz workOrderBiz;

	/**
	 * 异步发布并调度单子
	 *
	 * @param companyCarrierId            车主id
	 * @param isOurCompany                是否我司
	 * @param publishAndDispatchModelList 要发布调度的需求单
	 */
	public void publishAndDispatch(Long companyCarrierId, Integer isOurCompany, List<PublishAndDispatchModel> publishAndDispatchModelList) {
		try {
			BaseContextHandler.setUserName(CommonConstant.SYSTEM);
			List<Long> publishDemandIds = new ArrayList<>();
			List<Long> vehicleIds = new ArrayList<>();
			List<Long> driverIds = new ArrayList<>();

			/*
			发布需求单
			*/
			BatchPublishRequestModel batchPublishRequestModel = new BatchPublishRequestModel();
			batchPublishRequestModel.setCompanyCarrierId(companyCarrierId);
			batchPublishRequestModel.setIsOurCompany(isOurCompany);
			List<BatchPublishDemandModel> publishDemandModelList = new ArrayList<>();
			for (PublishAndDispatchModel publishAndDispatchModel : publishAndDispatchModelList) {
				//此次发布的需求单id
				publishDemandIds.add(publishAndDispatchModel.getDemandId());
				//此次调度的车辆id
				if (publishAndDispatchModel.getVehicleId() != null) {
					vehicleIds.add(publishAndDispatchModel.getVehicleId());
				}
				//此次调度的司机id
				if (publishAndDispatchModel.getDriverId() != null) {
					driverIds.add(publishAndDispatchModel.getDriverId());
				}

				//组装数据
				BatchPublishDemandModel publishDemandModel = new BatchPublishDemandModel();
				//回收入库类型,设置仓库信息
				if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(publishAndDispatchModel.getEntrustType())) {
					publishDemandModel.setWarehouseId(publishAndDispatchModel.getWarehouseId());
					publishDemandModel.setUnloadWarehouse(publishAndDispatchModel.getWarehouseName());
					publishDemandModel.setUnloadProvinceId(publishAndDispatchModel.getProvinceId());
					publishDemandModel.setUnloadProvinceName(publishAndDispatchModel.getProvinceName());
					publishDemandModel.setUnloadCityId(publishAndDispatchModel.getCityId());
					publishDemandModel.setUnloadCityName(publishAndDispatchModel.getCityName());
					publishDemandModel.setUnloadAreaId(publishAndDispatchModel.getAreaId());
					publishDemandModel.setUnloadAreaName(publishAndDispatchModel.getAreaName());
					publishDemandModel.setUnloadDetailAddress(publishAndDispatchModel.getDetailAddress());
					publishDemandModel.setReceiverName(publishAndDispatchModel.getContactName());
					publishDemandModel.setReceiverMobile(MysqlAESUtils.decrypt(publishAndDispatchModel.getContactPhone(), AuthCommonConstants.MYSQL_ENCRYPT_KEY));
				}
				publishDemandModel.setAutoPublish(CommonConstant.INTEGER_ONE);
				publishDemandModel.setDemandOrderId(publishAndDispatchModel.getDemandId());
				publishDemandModelList.add(publishDemandModel);
			}
			batchPublishRequestModel.setDemandDtoList(publishDemandModelList);
			//调用发布接口
			demandOrderForLeYiBiz.confirmPublish(batchPublishRequestModel);

			//发布和调度要保持间隔,线程休眠3s后再查询需要调度的单子
			Thread.sleep(CommonConstant.INTEGER_THREE_THOUSAND_HUNDRED);

			 /*
			调度需求单
			*/
			if (ListUtils.isEmpty(publishDemandIds)) {
				return;
			}
			//查询已经发布处于待调度的需求单
			List<AutoPublishWaitDispatchDemandModel> waitDispatchDemandOrderIds = tDemandOrderMapper.selectAutoPublishWaitDispatchDemandOrders(publishDemandIds, DemandOrderStatusEnum.WAIT_DISPATCH.getKey());

			RecyclePublishUpdateDemandRequestModel recycleSyncModel;
			List<RecyclePublishUpdateDemandRequestModel> recycleSyncModelList = new ArrayList<>();
			List<Long> publishSuccessDemandOrderIdList = new ArrayList<>();
			for (AutoPublishWaitDispatchDemandModel waitDispatchDemandOrderId : waitDispatchDemandOrderIds) {
				publishSuccessDemandOrderIdList.add(waitDispatchDemandOrderId.getDemandOrderId());
				//回收入库且是自动发布的才同步到智慧物流
				if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(waitDispatchDemandOrderId.getEntrustType())
						&& CommonConstant.INTEGER_ONE.equals(waitDispatchDemandOrderId.getAutoPublish())) {
					recycleSyncModel = new RecyclePublishUpdateDemandRequestModel();
					recycleSyncModel.setDemandOrderNo(waitDispatchDemandOrderId.getDemandOrderCode());
					recycleSyncModel.setConfigCode(waitDispatchDemandOrderId.getFixedDemand());
					recycleSyncModel.setEntrustNum(waitDispatchDemandOrderId.getGoodsAmount());
					recycleSyncModel.setEntrustPublishTime(waitDispatchDemandOrderId.getPublishTime());
					recycleSyncModelList.add(recycleSyncModel);
				}
			}

			//过滤出来待调度的单子
			List<PublishAndDispatchModel> filterDispatchDemandList = publishAndDispatchModelList.stream()
					.filter(publishAndDispatchModel -> publishSuccessDemandOrderIdList.contains(publishAndDispatchModel.getDemandId())
							&& publishAndDispatchModel.getVehicleId() != null).collect(Collectors.toList());

			if (ListUtils.isNotEmpty(filterDispatchDemandList)) {
				//查询车辆司机关联信息
				List<Long> vehicleIs = new ArrayList<>();
				List<Long> staffIds = new ArrayList<>();
				Map<Long, TStaffVehicleRelation> staffVehicleRelationMap = new HashMap<>();
				if (ListUtils.isNotEmpty(vehicleIds)) {
					String driverIdsStr = null;
					if (ListUtils.isNotEmpty(driverIds)) {
						//回收类型自动发布会有司机id
						driverIdsStr = StringUtils.join(driverIds, ',');
					}
					//查询当前车主下的车辆司机关联关系
					List<TStaffVehicleRelation> staffVehicleRelationList = tStaffVehicleRelationMapper.selectRelationsByVehicleIdDriverId(driverIdsStr, StringUtils.join(vehicleIds, ','), companyCarrierId);
					if (ListUtils.isEmpty(staffVehicleRelationList)) {
						return;
					}
					for (TStaffVehicleRelation staffVehicleRelation : staffVehicleRelationList) {
						staffVehicleRelationMap.put(staffVehicleRelation.getVehicleId(), staffVehicleRelation);
						vehicleIs.add(staffVehicleRelation.getVehicleId());
						staffIds.add(staffVehicleRelation.getStaffId());
					}
				}

				//查询车辆信息
				Map<Long, VehicleRelationInfoModel> vehicleBasicInfoMap = new HashMap<>();
				List<VehicleRelationInfoModel> vehicleRelationInfoByVehicleIds = tVehicleBasicMapper.getVehicleRelationInfoByVehicleIds(vehicleIs);
				if (ListUtils.isNotEmpty(vehicleRelationInfoByVehicleIds)) {
					vehicleBasicInfoMap = vehicleRelationInfoByVehicleIds.stream().collect(Collectors.toMap(VehicleRelationInfoModel::getVehicleId, item -> item));
				}

				//查询司机信息
				Map<Long, TStaffBasic> staffBasicInfoMap = new HashMap<>();
				if (ListUtils.isNotEmpty(staffIds)) {
					List<TStaffBasic> staffBasicInfoList = tStaffBasicMapper.getStaffByIds(StringUtils.join(staffIds, ','));
					if (ListUtils.isNotEmpty(staffBasicInfoList)) {
						staffBasicInfoMap = staffBasicInfoList.stream().collect(Collectors.toMap(TStaffBasic::getId, item -> item));
					}
				}

				//查询货物信息
				List<TDemandOrderGoods> demandOrderGoodsList = tDemandOrderGoodsMapper.getDemandOrderGoodsByDemandOrderIds(StringUtils.join(publishDemandIds, ','));
				Map<Long, List<TDemandOrderGoods>> demandOrderGoodsMap = demandOrderGoodsList.stream().collect(Collectors.groupingBy(TDemandOrderGoods::getDemandOrderId));

				//同一个车主的单子再根据车辆分组
				Map<Long, List<PublishAndDispatchModel>> dispatchDemandOrderVehicleGroup = filterDispatchDemandList.stream().collect(Collectors.groupingBy(PublishAndDispatchModel::getVehicleId));
				Collection<List<PublishAndDispatchModel>> dispatchDemandOrderVehicleGroupValue = dispatchDemandOrderVehicleGroup.values();
				for (List<PublishAndDispatchModel> dispatchDemandOrderModels : dispatchDemandOrderVehicleGroupValue) {
					//相同车主和车辆的单子
					PublishAndDispatchModel demandOrderModel = dispatchDemandOrderModels.get(CommonConstant.INTEGER_ZERO);

					//获取车辆
					VehicleRelationInfoModel vehicleBasicInfo = vehicleBasicInfoMap.get(demandOrderModel.getVehicleId());
					//获取司机
					TStaffBasic tStaffBasic = null;
					TStaffVehicleRelation staffInfo = staffVehicleRelationMap.get(demandOrderModel.getVehicleId());
					if (staffInfo != null && companyCarrierId.equals(staffInfo.getCompanyCarrierId())) {//当前车主绑定的司机
						tStaffBasic = staffBasicInfoMap.get(staffInfo.getStaffId());
					}

					if (vehicleBasicInfo != null && tStaffBasic != null) {
						DispatchRequestModel dispatchRequestModel = new DispatchRequestModel();
						//设置车辆信息
						dispatchRequestModel.setVehicleId(vehicleBasicInfo.getVehicleId());
						//设置司机信息
						dispatchRequestModel.setDriverId(tStaffBasic.getId());

						//预计提货时间,同批调度的单子里预计提货时间最晚的
						Date expectArrivalTime = null;
						//调度的货物信息
						List<DispatchGoodsModel> vehicleRequestModels = new ArrayList<>();
						for (PublishAndDispatchModel dispatchDemandOrderModel : dispatchDemandOrderModels) {
							if (expectArrivalTime == null) {
								expectArrivalTime = dispatchDemandOrderModel.getExpectedLoadTime();
							} else {
								if (dispatchDemandOrderModel.getExpectedLoadTime() != null) {
									if (expectArrivalTime.compareTo(dispatchDemandOrderModel.getExpectedLoadTime()) < CommonConstant.INTEGER_ZERO) {
										expectArrivalTime = dispatchDemandOrderModel.getExpectedLoadTime();
									}
								}
							}
							//组装货物信息
							List<TDemandOrderGoods> demandOrderGoods = demandOrderGoodsMap.get(dispatchDemandOrderModel.getDemandId());
							for (TDemandOrderGoods demandOrderGood : demandOrderGoods) {
								DispatchGoodsModel dispatchGoodsModel = new DispatchGoodsModel();
								dispatchGoodsModel.setDemandOrderId(dispatchDemandOrderModel.getDemandId());
								dispatchGoodsModel.setDemandOrderGoodsId(demandOrderGood.getId());
								dispatchGoodsModel.setLoadAmount(new BigDecimal(demandOrderGood.getGoodsAmount().stripTrailingZeros().toString()));
								vehicleRequestModels.add(dispatchGoodsModel);
							}
						}
						dispatchRequestModel.setExpectArrivalTime(expectArrivalTime);
						dispatchRequestModel.setVehicleRequestModels(vehicleRequestModels);
						dispatchRequestModel.setLoadPointAmount(CommonConstant.INTEGER_ONE);
						dispatchRequestModel.setUnloadPointAmount(CommonConstant.INTEGER_ONE);
						dispatchRequestModel.setSource(CommonConstant.INTEGER_ONE);
						try {
							dispatchBiz.dispatchVehicle(dispatchRequestModel);
						} catch (Exception e) {
							log.info("回收类型自动调度异常: " + e.getMessage(), e);
						}
					}
				}
			}

			//把发布成功的回收入库的单子同步到智慧物流(异步)
			if (ListUtils.isNotEmpty(recycleSyncModelList)) {
				commonBiz.synRecyclePublishUpdateDemand(recycleSyncModelList);
			}
		} catch (Exception e) {
			log.info("异步发布调度需求单异常: " + e.getMessage(), e);
		}
	}

	/**
	 * 自动调度固定需求需求单定时任务（十分钟一次）
	 */
	public void publishDispatchFixedDemand() {
		BaseContextHandler.setUserName(CommonConstant.SYSTEM);
		//查询三天内待处理的固定需求需求单
		List<FixedDemandOrderModel> demandOrderModel = tDemandOrderMapper.selectFixedDemandOrder();
		if (ListUtils.isNotEmpty(demandOrderModel)) {
			//固定客户仓库code
			List<String> warehouseCodeList = demandOrderModel.stream().map(FixedDemandOrderModel::getFixedDemand).collect(Collectors.toList());

			List<Long> companyCarrierIdList = new ArrayList<>();
			List<Long> vehicleIds = new ArrayList<>();
			//请求大数据接口
			FixedCompanyVehicleInfoRequestModel requestModel = new FixedCompanyVehicleInfoRequestModel();
			requestModel.setWhsCode(warehouseCodeList);
			requestModel.setLineType(CommonConstant.INTEGER_TWO);
			List<FixedCompanyVehicleInfoResponseModel> fixedCompanyVehicleInfo = bigDataClient.searchFixedInfo(requestModel);
			if (ListUtils.isNotEmpty(fixedCompanyVehicleInfo)) {
				Map<String, FixedCompanyVehicleInfoResponseModel> fixedCompanyVehicleInfoMap = new HashMap<>();
				for (FixedCompanyVehicleInfoResponseModel fixedInfoItem : fixedCompanyVehicleInfo) {
					fixedCompanyVehicleInfoMap.put(fixedInfoItem.getWarehouseCode(), fixedInfoItem);
					if (fixedInfoItem.getTcompanyCarrierId() != null) {
						companyCarrierIdList.add(fixedInfoItem.getTcompanyCarrierId());
					}
					if (fixedInfoItem.getVehicleId() != null) {
						vehicleIds.add(fixedInfoItem.getVehicleId());
					}
				}

				//查询车辆车主关联关系
				Map<Long, List<TCarrierVehicleRelation>> carrierVehicleRelation = new HashMap<>();
				if (ListUtils.isNotEmpty(vehicleIds)) {
					List<TCarrierVehicleRelation> carrierVehicleRelationList = tCarrierVehicleRelationMapper.getByVehicleIds(StringUtils.join(vehicleIds, ','));
					if (ListUtils.isNotEmpty(carrierVehicleRelationList)) {
						carrierVehicleRelation = carrierVehicleRelationList.stream().collect(Collectors.groupingBy(TCarrierVehicleRelation::getCompanyCarrierId));
					}
				}

				//设置和仓库绑定的车辆承运商到需求单上
				Iterator<FixedDemandOrderModel> demandOrderModelIterator = demandOrderModel.iterator();
				while (demandOrderModelIterator.hasNext()) {
					FixedDemandOrderModel demandOrderModelItem = demandOrderModelIterator.next();
					FixedCompanyVehicleInfoResponseModel fixedCompanyVehicle = fixedCompanyVehicleInfoMap.get(demandOrderModelItem.getFixedDemand());
					if (fixedCompanyVehicle != null && fixedCompanyVehicle.getTcompanyCarrierId() != null) {
						Long originCompanyCarrierId = fixedCompanyVehicle.getTcompanyCarrierId();
						//有固定履约信息
						demandOrderModelItem.setCompanyCarrierId(originCompanyCarrierId);
						//判断车辆关系是否正确
						//获取当前车主下的车辆关联关系,并且在大数据获取的车辆属于这个车主
						List<TCarrierVehicleRelation> carrierVehicleRelationList = carrierVehicleRelation.get(originCompanyCarrierId);
						if (ListUtils.isNotEmpty(carrierVehicleRelationList)
								&& ListUtils.isNotEmpty(carrierVehicleRelationList.stream()
								.filter(tCarrierVehicleRelation -> tCarrierVehicleRelation.getVehicleId().equals(fixedCompanyVehicle.getVehicleId())).collect(Collectors.toList()))) {
							demandOrderModelItem.setVehicleId(fixedCompanyVehicle.getVehicleId());
						}
					} else {
						//不存在固定信息或不是履约路线,不处理
						demandOrderModelIterator.remove();
					}
				}
			} else {
				//大数据没有维护固定线路
				return;
			}

			//没有符合的需求单
			if (ListUtils.isEmpty(demandOrderModel)) {
				return;
			}
			//根据车主分组
			Map<Long, List<FixedDemandOrderModel>> groupByCarrierDemandMap = demandOrderModel.stream().collect(Collectors.groupingBy(FixedDemandOrderModel::getCompanyCarrierId));
			Collection<List<FixedDemandOrderModel>> groupByCarrierDemandMapValues = groupByCarrierDemandMap.values();

			//根据id 查询车主
			Map<Long, Integer> companyCarrierLevelMap = new HashMap<>();
			if (ListUtils.isNotEmpty(companyCarrierIdList)) {
				List<FuzzySearchCompanyCarrierResponseModel> companyCarrierInfoList = tCompanyCarrierMapper.selectCompanyCarrierInfoByIds(companyCarrierIdList);
				if (ListUtils.isNotEmpty(companyCarrierInfoList)) {
					companyCarrierLevelMap = companyCarrierInfoList.stream().collect(Collectors.toMap(FuzzySearchCompanyCarrierResponseModel::getCompanyId, FuzzySearchCompanyCarrierResponseModel::getIsOurCompany));
				}
			}

			//发布需求单
			for (List<FixedDemandOrderModel> groupByCarrierDemandMapValue : groupByCarrierDemandMapValues) {
				FixedDemandOrderModel demandOrderInfoScope = groupByCarrierDemandMapValue.get(CommonConstant.INTEGER_ZERO);
				//获取是我司还是其他车主
				Integer isOurCompany = companyCarrierLevelMap.get(demandOrderInfoScope.getCompanyCarrierId());
				if (isOurCompany != null) {
					//一次发布20个
					List<List<FixedDemandOrderModel>> fixedDemandPartitionList = org.apache.commons.collections4.ListUtils.partition(groupByCarrierDemandMapValue, CommonConstant.INTEGER_TWENTY);

					for (List<FixedDemandOrderModel> fixedDemandPartition : fixedDemandPartitionList) {
						List<PublishAndDispatchModel> publishAndDispatchModelList = MapperUtils.mapperNoDefault(fixedDemandPartition, PublishAndDispatchModel.class);
						//异步发布调度
						if (ListUtils.isNotEmpty(publishAndDispatchModelList)) {
							AsyncProcessQueue.execute(() -> publishAndDispatch(demandOrderInfoScope.getCompanyCarrierId(), isOurCompany, publishAndDispatchModelList));
						}
					}
				}
			}
		}
	}

	/**
	 * 发布调度回收类型需求单定时任务（十分钟一次）
	 */
	public void publishDispatchRecycleDemand() {
		BaseContextHandler.setUserName(CommonConstant.SYSTEM);
		//查询三天内待处理的自动发布的回收入库需求单
		List<AutoPublishRecycleDemandOrderModel> demandOrderModel = tDemandOrderMapper.selectAutoPublishRecycleDemandOrder();
		if (ListUtils.isNotEmpty(demandOrderModel)) {
			List<Long> demandOrderIds = new ArrayList<>();
			//自动发布配置code
			List<String> autoPublishConfigCodeList = new ArrayList<>();
			for (AutoPublishRecycleDemandOrderModel publishRecycleDemandOrderModel : demandOrderModel) {
				autoPublishConfigCodeList.add(publishRecycleDemandOrderModel.getConfigCode());
				demandOrderIds.add(publishRecycleDemandOrderModel.getDemandId());
			}

			//回收类型可能存在异常工单
			Map<Long, Long> workOrderMap = workOrderBiz.checkDemandExceptionExist(StringUtils.listToString(demandOrderIds, ','));

			//我司车主id
			Long qiyaCompanyCarrierId = commonBiz.getQiyaCompanyCarrierId();
			List<Long> companyCarrierIdList = new ArrayList<>();
			List<Long> vehicleIds = new ArrayList<>();
			List<Long> driverIds = new ArrayList<>();
			//请求大数据接口
			AutoPublishInfoRequestModel requestModel = new AutoPublishInfoRequestModel();
			requestModel.setConfigCodeList(autoPublishConfigCodeList);
			List<AutoPublishInfoResponseModel> fixedCompanyVehicleInfo = bigDataClient.searchAutoPublishInfo(requestModel);
			if (ListUtils.isNotEmpty(fixedCompanyVehicleInfo)) {
				Map<String, AutoPublishInfoResponseModel> fixedCompanyVehicleInfoMap = new HashMap<>();
				for (AutoPublishInfoResponseModel fixedInfoItem : fixedCompanyVehicleInfo) {
					fixedCompanyVehicleInfoMap.put(fixedInfoItem.getConfigCode(), fixedInfoItem);
					if (CommonConstant.INTEGER_ONE.equals(fixedInfoItem.getIsCarrier())) {
						//自主车辆
						companyCarrierIdList.add(qiyaCompanyCarrierId);
					} else if (CommonConstant.INTEGER_ZERO.equals(fixedInfoItem.getIsCarrier()) && fixedInfoItem.getCarrierId() != null) {
						//指定承运商
						companyCarrierIdList.add(fixedInfoItem.getCarrierId());
					}
					if (fixedInfoItem.getVehicleId() != null) {
						vehicleIds.add(fixedInfoItem.getVehicleId());
					}
					if (fixedInfoItem.getStaffId() != null) {
						driverIds.add(fixedInfoItem.getStaffId());
					}
				}

				//查询车辆信息和车主关联关系
				Map<Long, CarrierVehicleRelAndInfoModel> vehicleinfoMap = new HashMap<>();
				if (ListUtils.isNotEmpty(vehicleIds)) {
					List<CarrierVehicleRelAndInfoModel> carrierVehicleRelationList = tCarrierVehicleRelationMapper.selectVehicleRelAndInfoByVehicleIds(vehicleIds);
					if (ListUtils.isNotEmpty(carrierVehicleRelationList)) {
						for (CarrierVehicleRelAndInfoModel carrierVehicleRelAndInfoModel : carrierVehicleRelationList) {
							//车辆基本信息
							vehicleinfoMap.putIfAbsent(carrierVehicleRelAndInfoModel.getVehicleId(), carrierVehicleRelAndInfoModel);
						}
					}
				}

				/*查询车辆司机关系*/
				Map<Long, Map<Long, Long>> carrierStaffVehicleRelationListMap = new HashMap<>();
				if (ListUtils.isNotEmpty(driverIds) && ListUtils.isNotEmpty(vehicleIds)) {
					List<TStaffVehicleRelation> staffVehicleRelationList = tStaffVehicleRelationMapper.selectAllRelationsByVehicleIdDriverId(StringUtils.join(driverIds, ','), StringUtils.join(vehicleIds, ','));
					Map<Long, List<TStaffVehicleRelation>> carrierRelationMap = staffVehicleRelationList.stream().collect(Collectors.groupingBy(TStaffVehicleRelation::getCompanyCarrierId));
					carrierRelationMap.forEach((companyCarrierId, tStaffVehicleRelations) -> {
						Map<Long, Long> vehicleDirverIdMap = tStaffVehicleRelations.stream().collect(Collectors.toMap(TStaffVehicleRelation::getVehicleId, TStaffVehicleRelation::getStaffId, (v1, v2) -> v1));
						carrierStaffVehicleRelationListMap.put(companyCarrierId, vehicleDirverIdMap);
					});
				}

				//设置和仓库绑定的车辆承运商到需求单上
				Iterator<AutoPublishRecycleDemandOrderModel> demandOrderModelIterator = demandOrderModel.iterator();
				while (demandOrderModelIterator.hasNext()) {
					AutoPublishRecycleDemandOrderModel demandOrderModelItem = demandOrderModelIterator.next();
					AutoPublishInfoResponseModel autoPublishInfoModel = fixedCompanyVehicleInfoMap.get(demandOrderModelItem.getConfigCode());
					if (autoPublishInfoModel != null) {
						boolean removeItem = false;
						//包含异常工单
						if (!MapUtils.isEmpty(workOrderMap) && workOrderMap.get(demandOrderModelItem.getDemandId()) != null) {
							removeItem = true;
						} else {
							//获取车主id
							Long companyCarrierId = autoPublishInfoModel.getCarrierId();
							//获取车辆信息
							CarrierVehicleRelAndInfoModel vehicleInfo = vehicleinfoMap.get(autoPublishInfoModel.getVehicleId());
							//获取仓库信息
							AutoPublishInfoWarehouseInfo warehouseInfo = autoPublishInfoModel.getWarehouseInfo();
							//获取司机id
							Long staffId = autoPublishInfoModel.getStaffId();

							//获取不到收货仓库,说明是不合规数据,不发布调度
							if (warehouseInfo == null) {
								removeItem = true;
							} else {
								setWarehouseInfo(warehouseInfo, demandOrderModelItem);
								if (CommonConstant.INTEGER_ONE.equals(autoPublishInfoModel.getIsCarrier()) && vehicleInfo != null && staffId != null) {
									//不指定承运商(我司)
									demandOrderModelItem.setCompanyCarrierId(qiyaCompanyCarrierId);
									demandOrderModelItem.setDriverId(staffId);
									Map<Long, Long> vehicleDriverMap = carrierStaffVehicleRelationListMap.get(qiyaCompanyCarrierId);
									//判断车辆司机关系
									Long driverId = null;
									if (vehicleDriverMap != null) {
										driverId = vehicleDriverMap.get(vehicleInfo.getVehicleId());
									}
									if (staffId.equals(driverId)) {
										//判断装载量和车辆所属车主为我司
										//单子的货物数量不能大于车的装载量
										Integer loadingCapacity = vehicleInfo.getLoadingCapacity();
										if (loadingCapacity != null
												&& qiyaCompanyCarrierId.equals(vehicleInfo.getCompanyCarrierId())
												&& demandOrderModelItem.getGoodsAmount().compareTo(new BigDecimal(loadingCapacity)) <= CommonConstant.INTEGER_ZERO) {
											//自有车辆
											demandOrderModelItem.setVehicleId(autoPublishInfoModel.getVehicleId());
										} else {
											removeItem = true;
										}
									} else {
										//智慧物流的配置 车辆司机和库里面的不一致
										removeItem = true;
									}
								} else if (CommonConstant.INTEGER_ZERO.equals(autoPublishInfoModel.getIsCarrier()) && companyCarrierId != null) {
									//指定承运商(其他车主)
									demandOrderModelItem.setCompanyCarrierId(companyCarrierId);
								} else {
									//不合规数据
									removeItem = true;
								}
							}
						}

						//不符合发布条件
						if (removeItem) {
							demandOrderModelIterator.remove();
						}
					} else {
						//不存在固定信息或不是履约路线,不处理
						demandOrderModelIterator.remove();
					}
				}
			} else {
				//大数据没有维护固定线路
				return;
			}

			//没有符合的需求单
			if (ListUtils.isEmpty(demandOrderModel)) {
				return;
			}
			//根据车主分组
			Map<Long, List<AutoPublishRecycleDemandOrderModel>> groupByCarrierDemandMap = demandOrderModel.stream().collect(Collectors.groupingBy(AutoPublishRecycleDemandOrderModel::getCompanyCarrierId));
			Collection<List<AutoPublishRecycleDemandOrderModel>> groupByCarrierDemandMapValues = groupByCarrierDemandMap.values();

			//根据id 查询车主
			Map<Long, Integer> companyCarrierLevelMap = new HashMap<>();
			if (ListUtils.isNotEmpty(companyCarrierIdList)) {
				List<FuzzySearchCompanyCarrierResponseModel> companyCarrierInfoList = tCompanyCarrierMapper.selectCompanyCarrierInfoByIds(companyCarrierIdList);
				if (ListUtils.isNotEmpty(companyCarrierInfoList)) {
					companyCarrierLevelMap = companyCarrierInfoList.stream().collect(Collectors.toMap(FuzzySearchCompanyCarrierResponseModel::getCompanyId, FuzzySearchCompanyCarrierResponseModel::getIsOurCompany));
				}
			}

			//发布需求单
			for (List<AutoPublishRecycleDemandOrderModel> groupByCarrierDemandMapValue : groupByCarrierDemandMapValues) {
				AutoPublishRecycleDemandOrderModel demandOrderInfoScope = groupByCarrierDemandMapValue.get(CommonConstant.INTEGER_ZERO);
				//获取是我司还是其他车主
				Integer isOurCompany = companyCarrierLevelMap.get(demandOrderInfoScope.getCompanyCarrierId());
				if (isOurCompany != null) {
					//一次发布20个
					List<List<AutoPublishRecycleDemandOrderModel>> demandPartitionList = org.apache.commons.collections4.ListUtils.partition(groupByCarrierDemandMapValue, CommonConstant.INTEGER_TWENTY);

					for (List<AutoPublishRecycleDemandOrderModel> demandPartition : demandPartitionList) {
						List<PublishAndDispatchModel> publishAndDispatchModelList = MapperUtils.mapperNoDefault(demandPartition, PublishAndDispatchModel.class);
						//异步发布调度
						if (ListUtils.isNotEmpty(publishAndDispatchModelList)) {
							AsyncProcessQueue.execute(() -> publishAndDispatch(demandOrderInfoScope.getCompanyCarrierId(), isOurCompany, publishAndDispatchModelList));
						}
					}
				}
			}
		}
	}

	/**
	 * 自动发布调度设置仓库信息
	 *
	 * @param warehouseInfo        智慧物流配置的仓库
	 * @param demandOrderModelItem 自动调度发布需求单model
	 */
	private void setWarehouseInfo(AutoPublishInfoWarehouseInfo warehouseInfo, AutoPublishRecycleDemandOrderModel demandOrderModelItem) {
		demandOrderModelItem.setWarehouseId(warehouseInfo.getWarehouseId());
		demandOrderModelItem.setWarehouseName(warehouseInfo.getWhsName());
		demandOrderModelItem.setProvinceId(warehouseInfo.getProvinceId());
		demandOrderModelItem.setProvinceName(warehouseInfo.getProvinceName());
		demandOrderModelItem.setCityId(warehouseInfo.getCityId());
		demandOrderModelItem.setCityName(warehouseInfo.getCityName());
		demandOrderModelItem.setAreaId(warehouseInfo.getAreaId());
		demandOrderModelItem.setAreaName(warehouseInfo.getAreaName());
		demandOrderModelItem.setDetailAddress(warehouseInfo.getDetailAddress());
		demandOrderModelItem.setContactName(warehouseInfo.getContactName());
		demandOrderModelItem.setContactPhone(warehouseInfo.getContactPhone());
	}
}
