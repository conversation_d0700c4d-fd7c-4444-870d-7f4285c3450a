package com.logistics.tms.controller.settlestatement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/20 14:08
 */
@Data
public class SettleStatementApplyInvoicingRequestModel {
    @ApiModelProperty(value = "对账单id")
    private List<Long> settleStatementIdList;

    @ApiModelProperty(value = "业务名称")
    private String businessName;

    @ApiModelProperty(value = "开票月份")
    private String invoicingMonth;

    @ApiModelProperty("备注")
    private String remark;
}
