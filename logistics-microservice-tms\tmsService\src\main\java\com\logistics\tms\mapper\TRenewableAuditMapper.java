package com.logistics.tms.mapper;

import com.logistics.tms.controller.renewableaudit.request.RenewableAssignDriverDetailRequestModel;
import com.logistics.tms.controller.renewableaudit.request.RenewableAuditDetailRequestModel;
import com.logistics.tms.controller.renewableaudit.request.RenewableAuditRequestModel;
import com.logistics.tms.controller.renewableaudit.request.RenewableOrderListRequestModel;
import com.logistics.tms.controller.renewableaudit.response.*;
import com.logistics.tms.entity.TRenewableAudit;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2022/08/15
*/
@Mapper
public interface TRenewableAuditMapper extends BaseMapper<TRenewableAudit> {

	TRenewableAudit selectByPrimaryKeyDecrypt(Long id);

	int insertSelectiveEncrypt(TRenewableAudit tRenewableAudit);

	int updateByPrimaryKeySelectiveEncrypt(TRenewableAudit tRenewableAudit);

	int batchUpdateSelectiveEncrypt(@Param("list") List<TRenewableAudit> list);

	/**
     * 获取分页总条数
     * @param requestModel
     * @return
     */
    List<Long> renewableAuditListCountIds(RenewableAuditRequestModel requestModel);
    /**
     * 查询新生审核列表
     * @param renewableAuditIdLists
     * @return
     */
    List<RenewableAuditResponseModel> renewableAuditList(@Param("renewableAuditIdLists") String renewableAuditIdLists);


	/**
	 * 新生审核列表详情
	 * @param requestModel
	 * @return
	 */
	RenewableAuditDetailResponseModel getRenewableAuditDetail(@Param("requestModel") RenewableAuditDetailRequestModel requestModel);

	/**
	 * 指派司机-详情
	 * @return
	 */
	List<RenewableAssignDriverDetailResponseModel> getAssignDriverDetail(RenewableAssignDriverDetailRequestModel requestModel);

	/**
	 * 确认信息-详情
	 * @param objectType
	 * @param renewableOrderId
	 * @return
	 */
	RenewableConfirmGoodsResponseModel confirmNewsDetail(@Param("objectType") Integer objectType, @Param("renewableOrderId") Long renewableOrderId);

	/**
	 * 新生审核列表统计
	 * @param renewableOrderIds
	 * @return
	 */
	RenewableAuditListStatisticsResponseModel getRenewableAuditListStatistics(@Param("renewableOrderIds") String renewableOrderIds);

	/**
	 * 查询审核单状态
	 * @param renewableOrderIds
	 * @return
	 */
	List<Integer> queryStatus(@Param("renewableOrderIds") String renewableOrderIds);
	/**
	 * 查询司机新生订单列表
	 *
	 * @param requestModel
	 * @return
	 */
	List<TRenewableAudit> queryRenewableOrderListByStaffId(RenewableOrderListRequestModel requestModel);

    /**
     * 查询司机新生订单数量
     *
     * @param staffId
     * @param status 订单状态：0 待指派，1 待确认，2 待审核，3 已审核 4 已取消，null时不根据此条件过滤
     * @return
     */
    int getCountByStaffId(@Param("staffId") Long staffId, @Param("status") Integer status);

	/**
	 * 订单tab汇总
	 *
	 * @param staffId 司机id
	 * @return
	 */
	RenewableOrderListStatisticResponseModel renewableOrderListStatistic(@Param("staffId") Long staffId);

	/**
	 * 根据新生订单号查询订单审核信息
	 * @param renewableOrderCode
	 * @return
	 */
	TRenewableAudit getByRenewableOrderCode(@Param("renewableOrderCode")String renewableOrderCode);

	TRenewableAudit selectByDemandOrderId(@Param("demandOrderId") Long demandOrderId);
}