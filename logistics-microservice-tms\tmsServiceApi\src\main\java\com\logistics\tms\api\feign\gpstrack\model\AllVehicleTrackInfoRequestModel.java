package com.logistics.tms.api.feign.gpstrack.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AllVehicleTrackInfoRequestModel extends AbstractPageForm<AllVehicleTrackInfoRequestModel> {

    @ApiModelProperty("车辆状态 1 行驶 0 静止")
    private Integer  vehicleStatus;

    private String vehicleNo;

    private String driver;

    private Integer ifHasCarrierOrders;

    private String currentPosition;




}
