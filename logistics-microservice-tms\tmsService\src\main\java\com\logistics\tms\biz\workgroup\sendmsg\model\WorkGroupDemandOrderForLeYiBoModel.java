package com.logistics.tms.biz.workgroup.sendmsg.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WorkGroupDemandOrderForLeYiBoModel {

    @ApiModelProperty("状态")
    private String statusDesc = "";

    @ApiModelProperty("需求单号")
    private String demandOrderCode = "";

    @ApiModelProperty("客户单号")
    private String customerOrderCode = "";

    @ApiModelProperty("下单时间")
    private String publishTime = "";

    @ApiModelProperty("装卸方式")
    private String loadingUnloadingPartLabel = "";

    @ApiModelProperty("1.3.2新增；时效要求")
    private String recycleTaskTypeLabel = "";

    @ApiModelProperty("装卸费用")
    private String loadingUnloadingCharge = "";

    @ApiModelProperty("其他要求")
    private String otherRequirements = "";

    @ApiModelProperty("货主公司名称")
    private String companyEntrustName = "";

    @ApiModelProperty("上游客户")
    private String upstreamCustomer = "";

    @ApiModelProperty("发货仓库")
    private String loadWarehouse = "";
    @ApiModelProperty("发货省市区")
    private String loadAddress = "";
    @ApiModelProperty("发货详细地址")
    private String loadDetailAddress = "";
    @ApiModelProperty("发货联系人")
    private String consignor = "";

    @ApiModelProperty("收货仓库")
    private String unloadWarehouse = "";
    @ApiModelProperty("收货省市区")
    private String unloadAddress = "";
    @ApiModelProperty("收货地址详细")
    private String unloadDetailAddress = "";
    @ApiModelProperty("收货联系人")
    private String receiver = "";

    @ApiModelProperty("期望到货时间")
    private String expectedUnloadTime = "";

    @ApiModelProperty("委托件数")
    private String goodsAmount = "";

    @ApiModelProperty("已安排数量")
    private String arrangedAmount = "";

    @ApiModelProperty("退回件数")
    private String backAmount = "";

    @ApiModelProperty("差异数量（运单下差异数总和）")
    private String differenceAmount = "";

    @ApiModelProperty("云仓异常数")
    private String abnormalAmount = "";

    @ApiModelProperty("未安排数量")
    private String notArrangedAmount = "";

    @ApiModelProperty("期望提货时间")
    private String expectedLoadTime = "";

    @ApiModelProperty("备注")
    private String remark = "";

    @ApiModelProperty("调度时效")
    private String dispatchValidity = "";

    @ApiModelProperty("大区")
    private String loadRegionName = "";

    @ApiModelProperty("大区负责人")
    private String loadRegionContactName = "";

    @ApiModelProperty("品名")
    private String goodsName = "";

    @ApiModelProperty("规格")
    private String goodsSize = "";

    @ApiModelProperty("委托类型")
    private String entrustType = "";

    @ApiModelProperty("下单部门")
    private String publishOrgName = "";

    @ApiModelProperty("车主公司名称")
    private String companyCarrierName = "";

    @ApiModelProperty("委托人")
    private String publishName = "";

    @ApiModelProperty("回退原因")
    private String rollbackRemark = "";
}
