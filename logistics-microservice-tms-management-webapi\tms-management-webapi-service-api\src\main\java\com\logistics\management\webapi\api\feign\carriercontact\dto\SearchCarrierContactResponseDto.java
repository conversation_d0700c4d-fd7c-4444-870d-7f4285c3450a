package com.logistics.management.webapi.api.feign.carriercontact.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/7/10 13:58
 */
@Data
public class SearchCarrierContactResponseDto {

    @ApiModelProperty("车主类型：1 企业 2 个人")
    private String type = "";

    @ApiModelProperty("车主类型文本")
    private String typeLabel = "";

    @ApiModelProperty("账号id")
    private String carrierContactId = "";

    @ApiModelProperty("联系人名字")
    private String contactName = "";

    @ApiModelProperty("联系方式")
    private String contactPhone = "";

    @ApiModelProperty("联系方式导出使用")
    private String exportContactPhone = "";

    @ApiModelProperty("公司名字")
    private String companyCarrierName = "";

    @ApiModelProperty("账号状态 禁用中0 启用中1")
    private String carrierContactStatus = "";

    @ApiModelProperty("账号状态文本")
    private String carrierContactStatusLabel = "";

}
