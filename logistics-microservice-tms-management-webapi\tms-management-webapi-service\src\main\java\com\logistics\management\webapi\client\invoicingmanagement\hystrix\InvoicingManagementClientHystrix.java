package com.logistics.management.webapi.client.invoicingmanagement.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.invoicingmanagement.InvoicingManagementClient;
import com.logistics.management.webapi.client.invoicingmanagement.request.*;
import com.logistics.management.webapi.client.invoicingmanagement.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/21 14:14
 */
@Component
public class InvoicingManagementClientHystrix implements InvoicingManagementClient {
    @Override
    public Result<PageInfo<SearchInvoicingManagementListResponseModel>> searchList(SearchInvoicingManagementListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetInvoicePicturesResponseModel>> getInvoicePictures(InvoicingManagementDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> updateBusinessName(UpdateBusinessNameRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<InvoicingManagementDetailResponseModel> getDetail(InvoicingManagementDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetInvoiceListResponseModel>> getInvoiceList(InvoicingManagementDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetInvoiceDetailResponseModel> getInvoiceDetail(GetInvoiceDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addOrModifyInvoice(AddOrModifyInvoiceRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> delInvoice(GetInvoiceDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<GetSettleStatementListResponseModel>> getSettleStatementList(GetSettleStatementListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<GetAddSettleStatementListResponseModel>> getAddSettleStatementList(GetAddSettleStatementListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addSettleStatement(AddInvoicingSettleStatementRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> delSettleStatement(DelSettleStatementRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetInvoicingArchiveListResponseModel>> getInvoicingArchiveList(InvoicingManagementDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> invoicingArchive(InvoicingArchiveRequestModel requestModel) {
        return Result.timeout();
    }
}
