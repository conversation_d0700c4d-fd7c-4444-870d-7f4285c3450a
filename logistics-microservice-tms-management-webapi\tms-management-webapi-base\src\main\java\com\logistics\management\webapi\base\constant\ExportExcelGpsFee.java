package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/10/8 14:39
 */
public class ExportExcelGpsFee {
    private ExportExcelGpsFee() {
    }

    private static final Map<String, String> EXPORT_GPS_FEE;

    static {
        EXPORT_GPS_FEE = new LinkedHashMap<>();
        EXPORT_GPS_FEE.put("车牌号", "vehicleNo");
        EXPORT_GPS_FEE.put("车辆机构", "vehiclePropertyLabel");
        EXPORT_GPS_FEE.put("司机", "driverName");
        EXPORT_GPS_FEE.put("手机号", "driverPhone");
        EXPORT_GPS_FEE.put("GPS终端号", "terminalType");
        EXPORT_GPS_FEE.put("SIM卡号", "simNumber");
        EXPORT_GPS_FEE.put("服务费用", "serviceFee");
        EXPORT_GPS_FEE.put("操作人", "lastModifiedBy");
        EXPORT_GPS_FEE.put("操作时间", "lastModifiedTime");
    }

    public static Map<String, String> getExportGpsFee() {
        return EXPORT_GPS_FEE;
    }
}
