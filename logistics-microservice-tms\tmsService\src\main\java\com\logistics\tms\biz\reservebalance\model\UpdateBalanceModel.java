package com.logistics.tms.biz.reservebalance.model;

import com.logistics.tms.entity.TReserveBalance;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class UpdateBalanceModel extends TReserveBalance {


    /**
     * 更新金额
     */
    private BigDecimal amount;

    /**
     * 更新类型 true 充值 false 扣款
     */
    private boolean isRecharge;
}
