package com.logistics.tms.mapper;

import com.logistics.tms.biz.carrierfreight.model.AddressAreaExistRequestModel;
import com.logistics.tms.biz.carrierfreight.model.AddressConfigModel;
import com.logistics.tms.controller.freightconfig.request.address.CarrierFreightConfigAddressListRequestModel;
import com.logistics.tms.entity.TCarrierFreightConfigAddress;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Mybatis Generator on 2023/06/30
 */
@Mapper
public interface TCarrierFreightConfigAddressMapper extends BaseMapper<TCarrierFreightConfigAddress> {

    void batchOneSqlInsert(@Param("list") List<TCarrierFreightConfigAddress> tCarrierFreightConfigAddresses);

    List<TCarrierFreightConfigAddress> searchAddressRuleList(CarrierFreightConfigAddressListRequestModel requestModel);

    List<TCarrierFreightConfigAddress> searchFreightAddressListByIds(@Param("ids") List<Long> updateFreightAddressIds);

    List<TCarrierFreightConfigAddress> searchAreaRouteExist(@Param("freightConfigSchemeId") Long freightConfigSchemeId, @Param("areaList") List<AddressAreaExistRequestModel> areaList);

    List<TCarrierFreightConfigAddress> searchAddressByConfigSchemeId(@Param("freightConfigSchemeId") Long freightConfigSchemeId);

    void deleteBySchemeId(@Param("schemeId") Long schemeId,@Param("lastModifiedBy") String lastModifiedBy);

    List<AddressConfigModel> selectAddressConfigAndLadderBySchemeId(@Param("schemeId") Long schemeId, @Param("enabled") Integer enabled);
}