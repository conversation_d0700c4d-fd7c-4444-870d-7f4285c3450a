package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2018/10/10 16:05
 */
@Data
public class CopyCarrierOrderByDispatchVehicleMessage {
    private List<String> demandOrderCodeList;
    @ApiModelProperty("key为需求单号，value为需求单需要变更的状态")
    private Map<String,Integer> demandOrderStatusMap;
    private List<CopyCarrierOrderByDispatchVehicleModel> items;
    @ApiModelProperty("物流需求对应待发货数量")
    private List<LogisticsShipmentCountModel> LogisticsShipmentCountModels;
    private Date expectArrivalTime;
    private String vehicleNo;
    private String driverName;
    private String driverMobile;
    private String driverIdentity;
    private Long userId;
    private String userName;
    @ApiModelProperty("调度单号")
    private String dispatchOrderCode;
    @ApiModelProperty("调度人姓名")
    private String dispatchUserName;
    @ApiModelProperty("调度时间")
    private Date dispatchTime;
}
