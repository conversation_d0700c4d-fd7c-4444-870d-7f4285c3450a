package com.logistics.management.webapi.controller.region.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2021/10/18 10:33
 */
@Data
public class SaveOrModifyRegionRequestDto {
    @ApiModelProperty("大区ID")
    private String regionId;

    @ApiModelProperty("大区名称")
    @NotBlank(message = "大区名称为空")
    @Pattern(regexp = "[\\u4E00-\\u9FA5A-Za-z0-9]{1,8}", message = "请维护正确的大区名称")
    private String regionName;

    @ApiModelProperty("大区负责人")
    @NotBlank(message = "大区负责人姓名为空")
    @Pattern(regexp = "[\\u4E00-\\u9FA5A-Za-z0-9]{1,20}", message = "请维护正确的大区负责人姓名")
    private String regionContactName;
    @ApiModelProperty("大区负责人手机号")
    @NotBlank(message = "大区负责人手机号为空")
    @Pattern(regexp = "^\\d{11}$", message = "请维护正确的大区负责人手机号")
    private String regionContactPhone;

    @ApiModelProperty("城市集合 批量字符串")
    @NotEmpty(message = "请选择城市")
    private List<ProvinceRequestDto> provinceDtoList;

    /**
     * (3.22.0)车主id
     */
    @ApiModelProperty("车主id")
    private List<String> companyCarrierIdList;
}
