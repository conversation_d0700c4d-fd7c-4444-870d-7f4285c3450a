package com.logistics.tms.rabbitmq.consumer.tms;

import com.alibaba.fastjson.JSONObject;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.carrierorder.CarrierOrderForLeYiBiz;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.rabbitmq.consumer.model.SyncStockInfoMessage;
import com.rabbitmq.client.Channel;
import com.yelo.tools.rabbitmq.annocation.EnableMqErrorMessageCollect;
import com.yelo.tools.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;


@Component
@Slf4j
public class SyncStockInfoFromLeyiStockInConsumer {

    private ObjectMapper objectMapper = JacksonUtils.getInstance();

    @Autowired
    private CarrierOrderForLeYiBiz carrierOrderForLeYiBiz;
    @Autowired
    private CommonBiz commonBiz;

    @EnableMqErrorMessageCollect
    @RabbitListener(bindings = {@QueueBinding(
            exchange = @Exchange(name = "logistics.topic", type = "topic", durable = "true"),
            value = @Queue(value = "logistics.tmsSyncStockInfoFromLeyiStockIn", durable = "true"),
            key = "tmsSyncStockInfoFromLeyiStockIn")}
            , concurrency = "3")
    public void process(@Payload String message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) throws IOException {
        SyncStockInfoMessage parse = objectMapper.readValue(message, SyncStockInfoMessage.class);
        log.info("托盘入库同步入库数量：" + parse.toString());

        //以需求单维度加锁，防止同需求单下运单同时操作入库，导致没有触发需求单自动签收
        String redisKey = CommonConstant.CARRIER_ORDER_CORRECT_TMS_KEY + parse.getDemandOrderCode();
        String uuid = commonBiz.getDistributedLock(redisKey);
        try {
            carrierOrderForLeYiBiz.syncStockInfoFromLeyiStockIn(parse);
        }catch (Exception e){
            throw e;
        }finally {
            commonBiz.removeDistributedLock(redisKey, uuid);
        }
        channel.basicAck(deliveryTag, false);
    }


    @EnableMqErrorMessageCollect
    @RabbitListener(bindings = {@QueueBinding(
            exchange = @Exchange(name = "logistics.topic", type = "topic", durable = "true"),
            value = @Queue(value = "logistics.batchTmsSyncStockInfoFromLeyiStockIn", durable = "true"),
            key = "batchTmsSyncStockInfoFromLeyiStockIn")}
            , concurrency = "3")
    public void batchTmsSyncStockInfoFromLeyiStockIn(@Payload String message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) throws IOException {
        List<SyncStockInfoMessage> syncStockInfoMessages = objectMapper.readValue(
                message,
                new TypeReference<List<SyncStockInfoMessage>>(){});
        log.info("托盘入库同步入库数量：" + JSONObject.toJSONString(syncStockInfoMessages));
        for (SyncStockInfoMessage e: syncStockInfoMessages){
            carrierOrderForLeYiBiz.syncStockInfoFromLeyiStockIn(e);
        }
        channel.basicAck(deliveryTag, false);
    }

}
