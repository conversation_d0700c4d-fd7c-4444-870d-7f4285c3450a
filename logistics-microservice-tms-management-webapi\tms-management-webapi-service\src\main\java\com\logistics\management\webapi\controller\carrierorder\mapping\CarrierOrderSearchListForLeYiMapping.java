package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.client.carrierorder.response.CarrierOrderOtherFeeItemModel;
import com.logistics.management.webapi.client.carrierorder.response.SearchCarrierOrderListForLeYiResponseModel;
import com.logistics.management.webapi.client.carrierorder.response.SearchCarrierOrderListGoodsInfoModel;
import com.logistics.management.webapi.controller.carrierorder.response.SearchCarrierOrderListForLeYiResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;


public class CarrierOrderSearchListForLeYiMapping extends MapperMapping<SearchCarrierOrderListForLeYiResponseModel, SearchCarrierOrderListForLeYiResponseDto> {
    @Override
    public void configure() {
        SearchCarrierOrderListForLeYiResponseModel source = getSource();
        SearchCarrierOrderListForLeYiResponseDto destination = getDestination();

        //运单状态转换
        if(CommonConstant.INTEGER_ONE.equals(source.getIfCancel())){
            destination.setStatus(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getKey().toString());
            destination.setStatusDesc(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getValue());
        }else if(CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())){
            destination.setStatus(CarrierOrderStatusEnum.EMPTY.getKey().toString());
            destination.setStatusDesc(CarrierOrderStatusEnum.EMPTY.getValue());
        }else{
            destination.setStatusDesc(CarrierOrderStatusEnum.getEnum(source.getStatus()).getValue());
        }

        //出库状态（发货、调拨、退货、供应商直配、采购、退货仓库配送、退货调拨才显示出库状态）
        List<Integer> entrustTypeListModel = Arrays.asList(EntrustTypeEnum.DELIVER.getKey(), EntrustTypeEnum.TRANSFERS.getKey(),
                EntrustTypeEnum.RETURN_GOODS.getKey(), EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey(), EntrustTypeEnum.PROCUREMENT.getKey(),
                EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey(), EntrustTypeEnum.RETURN_GOODS_TRANSFERS.getKey());
        if (entrustTypeListModel.contains(source.getEntrustType())) {
            destination.setOutStatusLabel(CarrierOrderOutStatusEnum.getEnum(source.getOutStatus()).getValue());
        }else{
            destination.setOutStatus("");
            destination.setOutStatusLabel("");
        }

        //货主公司名称
        String companyEntrustName = source.getEntrustCompany();
        if (EntrustTypeEnum.BOOKING.getKey().equals(source.getEntrustType())){
            companyEntrustName = CommonConstant.LEYI_POINTS_FOR_LOGISTICS;
        }
        destination.setEntrustCompany(companyEntrustName);

        //展示车主名称
        if (CommonConstant.INTEGER_ONE.equals(source.getCompanyCarrierType())) {
            destination.setCarrierCompany(source.getCarrierCompany());
        } else if (CommonConstant.INTEGER_TWO.equals(source.getCompanyCarrierType())) {
            destination.setCarrierCompany(source.getCarrierContactName() + " " + source.getCarrierContactMobile());
        }

        //司机
        destination.setDriver(source.getDriverName() + " " + source.getDriverMobile());
        destination.setExportDriverName(source.getDriverName());
        destination.setExportDriverMobile(source.getDriverMobile());

        //收发货地址
        destination.setLoadAddress((StringUtils.isNotEmpty(source.getLoadWarehouse()) ? "【" + source.getLoadWarehouse() + "】" : "") + " " + source.getLoadCityName() + source.getLoadAreaName() + source.getLoadDetailAddress());
        destination.setUnloadAddress((StringUtils.isNotEmpty(source.getUnloadWarehouse()) ? "【" + source.getUnloadWarehouse() + "】" : "") + " " + source.getUnloadCityName() + source.getUnloadAreaName() + source.getUnloadDetailAddress());

        //大区
        destination.setLoadRegionContactName(Optional.ofNullable(source.getLoadRegionContactName()).orElse("") + " " + Optional.ofNullable(source.getLoadRegionContactPhone()).orElse(""));

        //预计里程数
        BigDecimal expectMileage = BigDecimal.ZERO;
        if (source.getExpectMileage() != null) {
            expectMileage = source.getExpectMileage();
        }
        destination.setExpectMileage(expectMileage.stripTrailingZeros().toPlainString());

        //预计里程数、差异数、回退数、云仓异常数
        destination.setAbnormalAmount("");
        if (CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(source.getStatus())) {
            if (source.getCorrectStatus() != null) {
                BigDecimal differenceAmount = source.getLoadAmount().subtract(source.getExpectAmount());
                destination.setDifferenceAmount(differenceAmount.stripTrailingZeros().toPlainString());
                BigDecimal backAmount = source.getLoadAmount().subtract(source.getLoadAmountExpect());
                destination.setBackAmount(backAmount.stripTrailingZeros().toPlainString());
                destination.setAbnormalAmount(Optional.ofNullable(source.getAbnormalAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
            }
        }

        //货物信息
        if (ListUtils.isNotEmpty(source.getGoodsInfoList())) {
            StringBuilder goodsName = new StringBuilder();
            for (int index = 0; index < source.getGoodsInfoList().size(); index++) {
                SearchCarrierOrderListGoodsInfoModel tmpGoodsModel = source.getGoodsInfoList().get(index);
                if (source.getGoodsUnit().equals(GoodsUnitEnum.BY_VOLUME.getKey())) {
                    tmpGoodsModel.setGoodsSize(tmpGoodsModel.getGoodsSize() + " " + tmpGoodsModel.getLength() + "*" + tmpGoodsModel.getWidth() + "*" + tmpGoodsModel.getHeight() + "mm");
                }
                if (index != 0) {
                    goodsName.append("/");
                }
                goodsName.append(tmpGoodsModel.getGoodsName());
            }
            destination.setGoodsName(goodsName.toString());
        }

        //时间转换
        if(source.getLoadTime()!=null){
            destination.setLoadTime(DateUtils.dateToString(source.getLoadTime(),CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
        }
        //时间转换
        if(source.getUnloadTime()!=null){
            destination.setUnloadTime(DateUtils.dateToString(source.getUnloadTime(),CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
        }
        if(source.getExpectArrivalTime()!=null){
            destination.setExpectArrivalTime(DateUtils.dateToString(source.getExpectArrivalTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if(source.getDispatchTime()!=null){
            destination.setDispatchTime(DateUtils.dateToString(source.getDispatchTime(),DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
        }
        if(source.getExpectLoadTime()!=null){
            destination.setExpectLoadTime(DateUtils.dateToString(source.getExpectLoadTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }

        //数量去零、枚举值转换
        destination.setEntrustType(EntrustTypeEnum.getEnum(source.getEntrustType()).getValue());
        destination.setCorrectStatusDesc(CorrectStatusEnum.getEnum(source.getCorrectStatus()).getValue());
        destination.setGoodsUnit(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());
        destination.setExpectAmount(source.getExpectAmount().stripTrailingZeros().toPlainString());
        destination.setLoadAmount(source.getLoadAmount().stripTrailingZeros().toPlainString());
        destination.setUnloadAmount(source.getUnloadAmount().stripTrailingZeros().toPlainString());
        destination.setSignAmount(source.getSignAmount().stripTrailingZeros().toPlainString());

        //只有发货、回收、调拨、退货仓库配送、退货调拨类型展示提货时效
        List<Integer> entrustTypeListLoadValidity = Arrays.asList(EntrustTypeEnum.DELIVER.getKey(), EntrustTypeEnum.TRANSFERS.getKey(),
                EntrustTypeEnum.RECYCLE_IN.getKey(), EntrustTypeEnum.RECYCLE_OUT.getKey(),
                EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey(), EntrustTypeEnum.RETURN_GOODS_TRANSFERS.getKey());
        if (!entrustTypeListLoadValidity.contains(source.getEntrustType())) {
            destination.setLoadValidity("");
        }

        //时效要求
        destination.setRecycleTaskTypeLabel(RecycleTaskTypeEnum.getEnum(source.getRecycleTaskType()).getValue());

        //已签收才展示
        BigDecimal otherFeeTotal = CommonConstant.BIG_DECIMAL_ZERO;
        //货主结算数量
        BigDecimal entrustSettlementAmount = BigDecimal.ZERO;
        //实际货主运费
        destination.setEntrustFreight(CommonConstant.ZERO_NET_ZERO);
        destination.setOtherFee(CommonConstant.ZERO_NET_ZERO);
        if (CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(source.getStatus()) || CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())) {
            //货主费用
            if (CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())){
                entrustSettlementAmount = source.getExpectAmount();
            }else {
                if (SettlementTonnageEnum.LOAD.getKey().equals(source.getSettlementTonnage())) {
                    entrustSettlementAmount = source.getLoadAmount();
                } else if (SettlementTonnageEnum.UNLOAD.getKey().equals(source.getSettlementTonnage())) {
                    entrustSettlementAmount = source.getUnloadAmount();
                } else if (SettlementTonnageEnum.SIGN.getKey().equals(source.getSettlementTonnage())) {
                    entrustSettlementAmount = source.getSignAmount();
                } else if (SettlementTonnageEnum.EXPECT.getKey().equals(source.getSettlementTonnage())) {
                    entrustSettlementAmount = source.getExpectAmount();
                }
            }

            BigDecimal entrustFreight;
            //单价
            if (source.getEntrustFreightType().equals(PriceTypeEnum.UNIT_PRICE.getKey())) {
                if (entrustSettlementAmount.compareTo(BigDecimal.ZERO) == CommonConstant.INTEGER_ZERO) {
                    entrustSettlementAmount = source.getExpectAmount();
                }
                entrustFreight = source.getEntrustFreight().multiply(entrustSettlementAmount).setScale(2, RoundingMode.HALF_UP);

            } else {
                //一口价
                entrustFreight = source.getEntrustFreight();
            }
            destination.setEntrustFreight(ConverterUtils.toString(entrustFreight));

            //临时费用
            List<CarrierOrderOtherFeeItemModel> otherFee = source.getOtherFee();
            if (ListUtils.isNotEmpty(otherFee)) {
                for (CarrierOrderOtherFeeItemModel carrierOrderOtherFeeItemModel : otherFee) {
                    otherFeeTotal = otherFeeTotal.add(carrierOrderOtherFeeItemModel.getFeeAmount()).setScale(2, RoundingMode.HALF_UP);
                }
                destination.setOtherFee(otherFeeTotal.toPlainString());
            } else {
                destination.setOtherFee(CommonConstant.ZERO_NET_ZERO);
            }
        }

        destination.setDispatchFreightFee(CommonConstant.ZERO_NET_ZERO);
        destination.setCarrierPriceTotal(CommonConstant.ZERO_NET_ZERO);
        destination.setActualCarrierPriceTotal(CommonConstant.ZERO_NET_ZERO);
        //其他车主-展示车主费用
        if (CommonConstant.INTEGER_TWO.equals(source.getIsOurCompany())) {
            destination.setDispatchFreightFee(CommonConstant.ZERO_NET_ZERO);
            //已签收
            if (CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(source.getStatus())) {
                //车主实际结算数量
                BigDecimal carrierSettlementAmount = BigDecimal.ZERO;
                if (SettlementTonnageEnum.LOAD.getKey().equals(source.getCarrierSettlement())) {
                    carrierSettlementAmount = source.getLoadAmount();
                } else if (SettlementTonnageEnum.UNLOAD.getKey().equals(source.getCarrierSettlement())) {
                    carrierSettlementAmount = source.getUnloadAmount();
                } else if (SettlementTonnageEnum.SIGN.getKey().equals(source.getCarrierSettlement())) {
                    carrierSettlementAmount = source.getSignAmount();
                } else if (SettlementTonnageEnum.EXPECT.getKey().equals(source.getCarrierSettlement())) {
                    carrierSettlementAmount = source.getExpectAmount();
                }

                //实际车主费用
                BigDecimal signCarrierFreightFeeTotal = BigDecimal.ZERO;
                if (source.getCarrierSettlementCostTotal() != null) {//已生成结算数据
                    signCarrierFreightFeeTotal = source.getCarrierSettlementCostTotal();
                    destination.setCarrierPriceTotal(ConverterUtils.toString(signCarrierFreightFeeTotal));
                }else{
                    if (PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getCarrierPriceType())) {
                        signCarrierFreightFeeTotal = source.getCarrierPrice().multiply(carrierSettlementAmount).setScale(2, RoundingMode.HALF_UP);
                        destination.setCarrierPriceTotal(ConverterUtils.toString(signCarrierFreightFeeTotal));
                    } else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getCarrierPriceType())) {
                        signCarrierFreightFeeTotal = source.getCarrierPrice();
                        destination.setCarrierPriceTotal(ConverterUtils.toString(signCarrierFreightFeeTotal));
                    }
                }

                //车主费用合计=实际车主费用+临时费用
                destination.setActualCarrierPriceTotal(ConverterUtils.toString(signCarrierFreightFeeTotal.add(otherFeeTotal).setScale(2, RoundingMode.HALF_UP)));
            }
        } else if (CommonConstant.INTEGER_ONE.equals(source.getIsOurCompany())) {//我司
            //司机费用
            if (CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(source.getStatus()) || CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())) {
                BigDecimal dispatchFreightFee = BigDecimal.ZERO;
                //司机费用=货主费用+临时费用（所以单价时用货主结算数量结算）
                if (source.getDispatchFreightFeeType().equals(PriceTypeEnum.UNIT_PRICE.getKey())) {
                    dispatchFreightFee = source.getDispatchFreightFee().multiply(entrustSettlementAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (source.getDispatchFreightFeeType().equals(PriceTypeEnum.FIXED_PRICE.getKey())) {
                    dispatchFreightFee = source.getDispatchFreightFee();
                }
                destination.setDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee.add(otherFeeTotal).setScale(2, RoundingMode.HALF_UP)));
            }
        }
        destination.setOtherFeeFreight(new BigDecimal(destination.getOtherFee()).multiply(source.getStatementOtherFeeTaxPoint())
                .setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
        destination.setCarrierPriceTotalFreight(new BigDecimal(destination.getCarrierPriceTotal()).multiply(source.getStatementFreightTaxPoint())
                .setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
    }
}
