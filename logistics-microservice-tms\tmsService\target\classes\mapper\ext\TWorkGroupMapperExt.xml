<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TWorkGroupMapper" >
  <sql id="Base_Column_List_Decrypt" >
    id,
    group_name,
    group_desc,
    group_owner_id,
    AES_DECRYPT(UNHEX(group_owner_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as group_owner_mobile,
    group_owner_username,
    participant_id,
    AES_DECRYPT(UNHEX(participant_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as participant_mobile,
    participant_username,
    work_group_source,
    work_group_code,
    matching_location,
    matching_field,
    config_warehouse,
    entrust_type_group,
    project_label,
    enabled,
    created_by,
    created_time,
    last_modified_by,
    last_modified_time,
    valid
  </sql>
  <select id="selectByPrimaryKeyDecrypt" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List_Decrypt" />
    from t_work_group
    where id = #{id,jdbcType=BIGINT}
    and valid = 1
  </select>
  <insert id="insertSelectiveEncrypt" parameterType="com.logistics.tms.entity.TWorkGroup" keyProperty="id" useGeneratedKeys="true">
    insert into t_work_group
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="groupName != null" >
        group_name,
      </if>
      <if test="groupDesc != null" >
        group_desc,
      </if>
      <if test="groupOwnerId != null" >
        group_owner_id,
      </if>
      <if test="groupOwnerMobile != null" >
        group_owner_mobile,
      </if>
      <if test="groupOwnerUsername != null" >
        group_owner_username,
      </if>
      <if test="participantId != null" >
        participant_id,
      </if>
      <if test="participantMobile != null" >
        participant_mobile,
      </if>
      <if test="participantUsername != null" >
        participant_username,
      </if>
      <if test="workGroupSource != null">
        work_group_source,
      </if>
      <if test="workGroupCode != null" >
        work_group_code,
      </if>
      <if test="matchingLocation != null">
        matching_location,
      </if>
      <if test="matchingField != null">
        matching_field,
      </if>
      <if test="configWarehouse != null">
        config_warehouse,
      </if>
      <if test="entrustTypeGroup != null" >
        entrust_type_group,
      </if>
      <if test="projectLabel != null">
        project_label,
      </if>
      <if test="enabled != null" >
        enabled,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="groupName != null" >
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="groupDesc != null" >
        #{groupDesc,jdbcType=VARCHAR},
      </if>
      <if test="groupOwnerId != null" >
        #{groupOwnerId,jdbcType=BIGINT},
      </if>
      <if test="groupOwnerMobile != null" >
        HEX(AES_ENCRYPT(#{groupOwnerMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="groupOwnerUsername != null" >
        #{groupOwnerUsername,jdbcType=VARCHAR},
      </if>
      <if test="participantId != null" >
        #{participantId,jdbcType=BIGINT},
      </if>
      <if test="participantMobile != null" >
        HEX(AES_ENCRYPT(#{participantMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="participantUsername != null" >
        #{participantUsername,jdbcType=VARCHAR},
      </if>
      <if test="workGroupSource != null">
        #{workGroupSource,jdbcType=INTEGER},
      </if>
      <if test="workGroupCode != null" >
        #{workGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="matchingLocation != null">
        #{matchingLocation,jdbcType=VARCHAR},
      </if>
      <if test="matchingField != null">
        #{matchingField,jdbcType=INTEGER},
      </if>
      <if test="configWarehouse != null">
        #{configWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="entrustTypeGroup != null" >
        #{entrustTypeGroup,jdbcType=VARCHAR},
      </if>
      <if test="projectLabel != null">
        #{projectLabel,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null" >
        #{enabled,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelectiveEncrypt" parameterType="com.logistics.tms.entity.TWorkGroup" >
    update t_work_group
    <set >
      <if test="groupName != null" >
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="groupDesc != null" >
        group_desc = #{groupDesc,jdbcType=VARCHAR},
      </if>
      <if test="groupOwnerId != null" >
        group_owner_id = #{groupOwnerId,jdbcType=BIGINT},
      </if>
      <if test="groupOwnerMobile != null" >
        group_owner_mobile = HEX(AES_ENCRYPT(#{groupOwnerMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="groupOwnerUsername != null" >
        group_owner_username = #{groupOwnerUsername,jdbcType=VARCHAR},
      </if>
      <if test="participantId != null" >
        participant_id = #{participantId,jdbcType=BIGINT},
      </if>
      <if test="participantMobile != null" >
        participant_mobile = HEX(AES_ENCRYPT(#{participantMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="participantUsername != null" >
        participant_username = #{participantUsername,jdbcType=VARCHAR},
      </if>
      <if test="workGroupSource != null">
        work_group_source = #{workGroupSource,jdbcType=INTEGER},
      </if>
      <if test="workGroupCode != null" >
        work_group_code = #{workGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="matchingLocation != null">
        matching_location = #{matchingLocation,jdbcType=VARCHAR},
      </if>
      <if test="matchingField != null">
        matching_field = #{matchingField,jdbcType=INTEGER},
      </if>
      <if test="configWarehouse != null">
        config_warehouse = #{configWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="entrustTypeGroup != null" >
        entrust_type_group = #{entrustTypeGroup,jdbcType=VARCHAR},
      </if>
      <if test="projectLabel != null">
        project_label = #{projectLabel,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null" >
        enabled = #{enabled,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="searchList" resultType="com.logistics.tms.controller.workgroup.response.SearchWorkGroupListResponseModel">
    select
    id as workGroupId,
    group_name as groupName,
    group_owner_username as groupOwnerUsername,
    AES_DECRYPT(UNHEX(group_owner_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as groupOwnerMobile,
    group_desc as groupDesc,
    enabled,
    created_by as createdBy,
    created_time as createdTime
    from t_work_group
    where valid = 1
    <if test="params.groupName != null and params.groupName != ''">
      and instr(group_name, #{params.groupName,jdbcType=VARCHAR})
    </if>
    <if test="params.groupOwnerUser != null and params.groupOwnerUser != ''">
      and instr(group_owner_username, #{params.groupOwnerUser,jdbcType=VARCHAR})
    </if>
    <if test="params.groupDesc != null and params.groupDesc != ''">
      and instr(group_desc, #{params.groupDesc,jdbcType=VARCHAR})
    </if>
    order by created_time desc, id desc
  </select>

  <select id="selectCount" resultType="int">
      select count(*)
      from t_work_group
      where  valid = 1
  </select>

  <select id="selectAllByEntrustTypeGroupDecrypt" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List_Decrypt"/>
    from t_work_group
    where FIND_IN_SET(#{entrustTypeGroup}, entrust_type_group)
    and enabled = 1
    and valid = 1
  </select>

  <update id="delWorkGroup">
    update t_work_group twg
    left join t_work_group_district twgd on twg.id = twgd.work_group_id and twgd.valid = 1
    left join t_work_group_node twgn on twg.id = twgn.work_group_id and twgn.valid = 1
    left join t_work_group_node_field twgnf on twgn.id = twgnf.work_group_node_id and twgnf.valid = 1
    set
    twg.valid = 0,
    twg.last_modified_by = #{userName,jdbcType=VARCHAR},
    twg.last_modified_time = #{updateTime,jdbcType=TIMESTAMP},

    twgd.valid = 0,
    twgd.last_modified_by = #{userName,jdbcType=VARCHAR},
    twgd.last_modified_time = #{updateTime,jdbcType=TIMESTAMP},

    twgn.valid = 0,
    twgn.last_modified_by = #{userName,jdbcType=VARCHAR},
    twgn.last_modified_time = #{updateTime,jdbcType=TIMESTAMP},

    twgnf.valid = 0,
    twgnf.last_modified_by = #{userName,jdbcType=VARCHAR},
    twgnf.last_modified_time = #{updateTime,jdbcType=TIMESTAMP}

    where twg.valid = 1
    and twg.id = #{id,jdbcType=BIGINT}
  </update>
</mapper>