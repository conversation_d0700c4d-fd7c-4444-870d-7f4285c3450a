package com.logistics.management.webapi.controller.biddingorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class BottomPriceRequestDto {

    /**
     * 竞价单id
     */
    @NotBlank(message = "竞价单id不能为空")
    private String biddingOrderId;


    /**
     * 发货地省id
     */
    @NotBlank(message = "发货地省id不能为空")
    private String loadProvinceId;
    /**
     * 发货地市id
     */
    @NotBlank(message = "发货地市id不能为空")
    private String loadCityId;
    /**
     * 发货地区id
     */
    @NotBlank(message = "发货地区id不能为空")
    private String loadAreaId;


    /**
     * 收货地省id
     */
    @NotBlank(message = "收货地省id不能为空")
    private String unloadProvinceId;
    /**
     * 收货地市id
     */
    @NotBlank(message = "收货地市id不能为空")
    private String unloadCityId;
    /**
     * 收货地区id
     */
    @NotBlank(message = "收货地区id不能为空")
    private String unloadAreaId;

    /**
     * 货物数量
     */
    @ApiModelProperty("货物数量")
    @NotBlank(message = "货物数量不能为空")
    private String goodsCount;




}
