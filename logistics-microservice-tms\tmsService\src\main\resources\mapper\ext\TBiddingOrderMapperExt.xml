<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TBiddingOrderMapper" >
  <insert id="insertSelectiveBackKey" parameterType="com.logistics.tms.entity.TBiddingOrder" keyProperty="id" useGeneratedKeys="true">
    insert into t_bidding_order
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="biddingOrderCode != null" >
        bidding_order_code,
      </if>
      <if test="biddingStatus != null" >
        bidding_status,
      </if>
      <if test="ifCancel != null" >
        if_cancel,
      </if>
      <if test="companyCarrierRange != null" >
        company_carrier_range,
      </if>
      <if test="quoteStartTime != null" >
        quote_start_time,
      </if>
      <if test="quoteDuration != null" >
        quote_duration,
      </if>
      <if test="quoteEndTime != null" >
        quote_end_time,
      </if>
      <if test="expectedLoadTime != null" >
        expected_load_time,
      </if>
      <if test="expectedUnloadTime != null" >
        expected_unload_time,
      </if>
      <if test="vehicleLengthId != null" >
        vehicle_length_id,
      </if>
      <if test="vehicleLength != null" >
        vehicle_length,
      </if>
        <if test="lowestPrice != null">
            lowest_price,
        </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="biddingOrderCode != null" >
        #{biddingOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="biddingStatus != null" >
        #{biddingStatus,jdbcType=INTEGER},
      </if>
      <if test="ifCancel != null" >
        #{ifCancel,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierRange != null" >
        #{companyCarrierRange,jdbcType=INTEGER},
      </if>
      <if test="quoteStartTime != null" >
        #{quoteStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="quoteDuration != null" >
        #{quoteDuration,jdbcType=INTEGER},
      </if>
      <if test="quoteEndTime != null" >
        #{quoteEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectedLoadTime != null" >
        #{expectedLoadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectedUnloadTime != null" >
        #{expectedUnloadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleLengthId != null" >
        #{vehicleLengthId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLength != null" >
        #{vehicleLength,jdbcType=DECIMAL},
      </if>
        <if test="lowestPrice != null">
            #{lowestPrice,jdbcType=DECIMAL},
        </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

    <select id="searchBiddingOrderListByManager"
            resultType="com.logistics.tms.biz.biddingorder.bo.SearchBiddingOrderListRespBo">
        select tbo.id                 as biddingOrderId,
               tbo.bidding_order_code as biddingOrderCode,
               tbo.bidding_status     as biddingStatus,
               tbo.if_cancel          as ifCancel,
               if(sum(1) = 1, 1, 2)   as handlingMode,
               sum(1)                 as demandCount,
               sum(1)                 as pathwayCount,
               sum(tdo.goods_amount)  as goodsCount,
               tbo.created_by         as createdBy,
               tbo.quote_start_time   as quoteStartTime,
               tbo.quote_end_time     as quoteEndTime
        from t_bidding_order tbo
                     inner join t_bidding_order_demand tbod on tbo.id = tbod.bidding_order_id and tbod.valid = 1
                     inner join t_demand_order tdo on tbod.demand_order_id = tdo.id and tdo.valid = 1
                where  tbo.valid = 1
        <if test="biddingOrderIdsByAddress != null and biddingOrderIdsByAddress.size() != 0">
            and tbo.id in
            <foreach collection="biddingOrderIdsByAddress" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="biddingOrderIdsByVehicleLength != null and biddingOrderIdsByVehicleLength.size() != 0">
            and tbo.id in
            <foreach collection="biddingOrderIdsByVehicleLength" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="biddingStatus != null and biddingStatus!=5">
            and tbo.bidding_status = #{biddingStatus,jdbcType=INTEGER} and tbo.if_cancel = 0
        </if>
        <if test="biddingStatus != null and biddingStatus==5">
            and tbo.if_cancel = 1
        </if>
        group by tbo.id
        <if test="handlingMode != null and handlingMode == 1">
            having sum(1) = 1
        </if>
        <if test="handlingMode != null and handlingMode == 2">
            having sum(1) >1
        </if>
        order by tbo.created_time desc
    </select>

    <select id="searchBiddingOrderListByCustomer"
            resultType="com.logistics.tms.biz.biddingorder.bo.SearchBiddingOrderListByCustomerRespBo">
        select tbo.id                 as biddingOrderId,
               tbo.bidding_order_code as biddingOrderCode,
               tbo.bidding_status     as biddingStatus,
               tbo.if_cancel          as ifCancel,
               if(sum(1) = 1, 1, 2)   as handlingMode,
               sum(1)                 as demandCount,
               sum(1)                 as pathwayCount,
               sum(tdo.goods_amount)  as goodsCount,
               tbo.created_by         as createdBy,
               tbo.quote_start_time   as quoteStartTime,
               tbo.quote_end_time     as quoteEndTime,
               tboq.quote_price       as quotePrice,
               tboq.vehicle_length    as vehicleLength,
               tboq.quote_status      as quoteStatus,
               tboq.quote_price_type  as quotePriceType
        from t_bidding_order tbo
        inner join t_bidding_order_demand tbod on tbo.id = tbod.bidding_order_id and tbod.valid = 1
        inner join t_demand_order tdo on tbod.demand_order_id = tdo.id and tdo.valid = 1
        left join t_bidding_order_quote tboq on tbo.id = tboq.bidding_order_id and tboq.valid = 1 and tboq.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        left join t_bidding_order_company tboc on tboc.bidding_order_id = tbo.id and tboc.valid = 1 and tboc.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        where  tbo.valid = 1
        <choose>
            <when test="companyCarrierIfAddBlacklist == 1">
                and (tboc.id is not null or tboq.id is not null)
            </when>
            <otherwise>
                and (tbo.company_carrier_range = 1 or tboc.id is not null or tboq.id is not null)
            </otherwise>
        </choose>
        <if test="biddingStatus != null and biddingStatus !=4 and biddingStatus !=5">
            and tbo.bidding_status = #{biddingStatus,jdbcType=INTEGER} and tbo.if_cancel = 0 and (tboq.quote_status !=2 or tboq.quote_status is null)
        </if>
        <if test="biddingStatus != null and biddingStatus == 4">
            and tbo.bidding_status = 4 and tboq.quote_status=1
        </if>
        <if test="biddingStatus != null and biddingStatus == 5">
            and (tboq.quote_status = 2 or tbo.if_cancel=1 or (tbo.bidding_status =4 and (tboq.quote_status !=1 or tboq.quote_status is null)))
        </if>

        <if test="biddingOrderIdsByAddress != null and biddingOrderIdsByAddress.size() != 0">
            and tbo.id in
            <foreach collection="biddingOrderIdsByAddress" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="vehicleLength != null">
            and tboq.vehicle_length_id = #{vehicleLength,jdbcType=BIGINT}
        </if>
        group by tbo.id
        <if test="handlingMode != null and handlingMode == 1">
            having sum(1) = 1
        </if>
        <if test="handlingMode != null and handlingMode == 2">
            having sum(1) >1
        </if>
        order by tbo.created_time desc, tbo.id desc
    </select>

    <update id="updateBiddingOrderStateByCAS">
        update t_bidding_order tbo
        <set>
            <choose>
                <when test="targetState != null and targetState == 5">
                    tbo.if_cancel=1,
                </when>
                <otherwise>
                    tbo.bidding_status = #{targetState,jdbcType=INTEGER},
                </otherwise>
            </choose>
            <if test="lastModifiedBy != null and lastModifiedBy != ''">
                tbo.last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                tbo.last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
        </set>

        where tbo.id = #{biddingOrderId,jdbcType=BIGINT}
          and tbo.valid = 1
        <if test="sourceState != null and sourceState.size() != 0">
            and
            <foreach collection="sourceState" item="item" open="(" close=")" separator="or">
                <choose>
                    <when test="item != null and item == 5">
                        tbo.if_cancel = 1
                    </when>
                    <otherwise>
                        tbo.bidding_status = #{item,jdbcType=INTEGER}
                    </otherwise>
                </choose>
            </foreach>
        </if>
    </update>
</mapper>