package com.logistics.management.webapi.controller.demandorder.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class DemandOrderForLeYiResponseDto {
    @ApiModelProperty("需求单id")
    private String demandId= "";

    @ApiModelProperty("需求单状态：1000待调度 2000部分调度 3000调度完成 4000待签收 5000已签收 1取消 2放空 3回退")
    private String status= "";
    @ExcelProperty("状态")
    private String statusDesc = "";

    @ExcelProperty("需求单号")
    @ApiModelProperty("需求单号")
    private String demandOrderCode= "";

    @ExcelProperty("客户单号")
    @ApiModelProperty("客户单号")
    private String customerOrderCode= "";

    @ExcelProperty("下单时间")
    @ApiModelProperty("下单时间")
    private String publishTime= "";

    @ExcelProperty("装卸方式")
    @ApiModelProperty("装卸方式")
    private String loadingUnloadingPartLabel= "";

    @ApiModelProperty("1.3.2新增；时效要求：1 日常回收，2 加急或节假日回收")
    private String recycleTaskType = "";
    @ExcelProperty("时效要求")
    @ApiModelProperty("1.3.2新增；时效要求")
    private String recycleTaskTypeLabel = "";

    @ExcelProperty("装卸费用")
    @ApiModelProperty("装卸费用")
    private String loadingUnloadingCharge= "";

    @ExcelProperty("其他要求")
    @ApiModelProperty("其他要求")
    private String otherRequirements= "";

    @ApiModelProperty("货主公司ID")
    private String companyEntrustId= "";
    @ApiModelProperty("货主公司类型")
    private String companyEntrustType= "";
    @ExcelProperty("货主")
    @ApiModelProperty("货主公司名称")
    private String companyEntrustName= "";
    @ApiModelProperty("货主联系人")
    private String entrustContactName= "";
    @ApiModelProperty("货主联系人电话")
    private String entrustContactMobile= "";

    @ExcelProperty("上游客户")
    @ApiModelProperty("上游客户")
    private String upstreamCustomer= "";

    @ApiModelProperty("已调车数(包含已取消)")
    private String dispatchVehicleCount= "";

    @ExcelProperty("发货仓库")
    @ApiModelProperty("发货仓库")
    private String loadWarehouse= "";
    @ExcelProperty("发货省")
    @ApiModelProperty("发货省市区")
    private String loadProvinceName = "";
    @ExcelProperty("发货市")
    private String loadCityName = "";
    @ExcelProperty("发货区")
    private String loadAreaName = "";
    private String loadAddress= "";//拼接后的数据，列表展示
    @ExcelProperty("发货详细地址")
    @ApiModelProperty("发货详细地址")
    private String loadDetailAddress= "";
    @ExcelProperty("发货人姓名")
    @ApiModelProperty("发货人")
    private String consignorName= "";
    @ExcelProperty("发货人联系方式")
    private String consignorMobile="";

    @ExcelProperty("收货仓库")
    @ApiModelProperty("收货仓库")
    private String unloadWarehouse= "";
    @ExcelProperty("收货省")
    @ApiModelProperty("收货省市区")
    private String unloadProvinceName = "";
    @ExcelProperty("收货市")
    private String unloadCityName = "";
    @ExcelProperty("收货区")
    private String unloadAreaName = "";
    private String unloadAddress= "";//拼接后的数据，列表展示
    @ExcelProperty("收货详细地址")
    @ApiModelProperty("收货地址详细")
    private String unloadDetailAddress= "";
    @ExcelProperty("收货人姓名")
    @ApiModelProperty("收货人")
    private String receiverName= "";
    @ExcelProperty("收货人联系方式")
    private String receiverMobile="";

    @ExcelProperty("期望到货时间")
    @ApiModelProperty("期望到货时间")
    private String expectedUnloadTime= "";

    @ExcelProperty("单位")
    @ApiModelProperty("货物单位 1 件 2 吨 3 方 4 块")
    private String goodsUnit= "";

    @ExcelProperty("委托")
    @ApiModelProperty("委托件数")
    private String goodsAmount= "";

    @ExcelProperty("已安排")
    @ApiModelProperty("已安排数量")
    private String arrangedAmount= "";

    @ExcelProperty("已退回")
    @ApiModelProperty("退回件数")
    private String backAmount= "";

    @ExcelProperty("差异数")
    @ApiModelProperty("差异数量（运单下差异数总和）")
    private String differenceAmount = "";

    @ExcelProperty("云仓异常数")
    @ApiModelProperty("云仓异常数")
    private String abnormalAmount = "";

    @ExcelProperty("未安排")
    @ApiModelProperty("未安排数量")
    private String notArrangedAmount= "";

    @ExcelProperty("期望提货时间")
    @ApiModelProperty("期望提货时间")
    private String expectedLoadTime= "";

    @ExcelProperty("备注")
    @ApiModelProperty("备注")
    private String remark="";

    @ExcelProperty("调度时效")
    @ApiModelProperty("调度时效")
    private String dispatchValidity="";

    @ExcelProperty("大区")
    @ApiModelProperty("大区")
    private String loadRegionName="";

    @ExcelProperty("负责人")
    @ApiModelProperty("大区负责人")
    private String loadRegionContactName="";

    @ExcelProperty("品名")
    @ApiModelProperty("品名")
    private String goodsName= "";

    @ExcelProperty("规格")
    @ApiModelProperty("规格")
    private String goodsSize= "";

    @ExcelProperty("需求类型")
    @ApiModelProperty("委托类型")
    private String entrustType= "";

    @ExcelProperty("下单部门")
    @ApiModelProperty("下单部门")
    private String publishOrgName = "";

    @ApiModelProperty("车主公司ID")
    private String companyCarrierId= "";
    @ApiModelProperty("车主公司类型")
    private String companyCarrierType= "";
    @ExcelProperty("车主")
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName= "";

    @ExcelProperty("委托人")
    @ApiModelProperty("委托人")
    private String publishName= "";

    @ApiModelProperty("预计货主费用")
    private String expectEntrustFee = "";

    @ApiModelProperty("是否同步到网络货运平台：0 否，1 是")
    private String ifSynNetworkFreight = "";
    @ApiModelProperty("是否加急：0 否，1 是")
    private String ifUrgent="";
    @ApiModelProperty("是否逾期：0 否，1 是")
    private String ifOverdue="";

    @ApiModelProperty("是否是额外补的需求单0：否1：是 v2.6.8")
    private String ifExtDemandOrder = "";


}
