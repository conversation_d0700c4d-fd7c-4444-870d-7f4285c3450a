package com.logistics.tms.biz.dispatch.model;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2024/8/7 17:30
 */
@Data
public class DemandOrderGoodsModel {

    /**
     * 需求单货物id
     */
    private Long demandOrderGoodsId;

    /**
     * 品名
     */
    private String goodsName;

    /**
     * 规格
     */
    private String goodsSize;
    private Integer length;
    private Integer width;
    private Integer height;

    /**
     * 未安排数量
     */
    private BigDecimal notArrangedAmount;
}
