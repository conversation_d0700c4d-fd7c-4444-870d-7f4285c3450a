package com.logistics.tms.biz.carrierorderapplet;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.constant.ConfigKeyConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.base.utils.ExceptionUtils;
import com.logistics.tms.base.utils.RegExpValidatorUtil;
import com.logistics.tms.base.utils.StripTrailingZerosUtils;
import com.logistics.tms.biz.basicinfo.applet.DriverBasicInfoBiz;
import com.logistics.tms.biz.carrierorder.CarrierOrderBiz;
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz;
import com.logistics.tms.biz.carrierorder.model.CarrierOrderSynchronizeLeyiModel;
import com.logistics.tms.biz.carrierorder.model.CarrierOrderSynchronizeModel;
import com.logistics.tms.biz.carrierorderticketsaudit.CarrierOrderTicketsAuditBiz;
import com.logistics.tms.biz.carrierorderticketsaudit.model.CreateOrResetReceiptTicketAuditBoModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.email.EmailConstant;
import com.logistics.tms.biz.extvehiclesettlement.ExtVehicleSettlementBiz;
import com.logistics.tms.biz.reservationorder.ReservationOrderCommonBiz;
import com.logistics.tms.biz.staff.model.CarrierDriverRelationModel;
import com.logistics.tms.biz.sysconfig.SysConfigBiz;
import com.logistics.tms.biz.terminalreachmanagement.ReachManagementBiz;
import com.logistics.tms.biz.workgroup.sendmsg.model.WorkGroupEventModel;
import com.logistics.tms.biz.workordercenter.WorkOrderBiz;
import com.logistics.tms.client.TrayOrderServiceClient;
import com.logistics.tms.client.WarehouseStockClient;
import com.logistics.tms.client.feign.basicdata.BasicServiceClient;
import com.logistics.tms.client.feign.basicdata.user.response.UserDetailResponseModel;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.request.BatchQueryCustomerInfoRequest;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.response.BatchQueryCustomerInfoResponse;
import com.logistics.tms.client.feign.tray.order.customerinorder.CustomerInOrderServiceApi;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.request.RejectOrderLoadAmountRequestModel;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.response.GetCarrierOrderDetailResponseModel;
import com.logistics.tms.client.feign.warehouse.stock.reponse.GetCompanyNameByCodeRespModel;
import com.logistics.tms.client.feign.yelolife.basicdata.YeloLifeBasicDataServiceApi;
import com.logistics.tms.controller.carrierorder.request.LoadGoodsForYeloLifeRequestCodeModel;
import com.logistics.tms.controller.carrierorder.response.CarrierOrderGoodsCodeResponseModel;
import com.logistics.tms.controller.carrierorder.response.CarrierOrderGoodsResponseModel;
import com.logistics.tms.controller.carrierorder.response.GetTicketsResponseModel;
import com.logistics.tms.controller.carrierorderapplet.request.*;
import com.logistics.tms.controller.carrierorderapplet.response.*;
import com.logistics.tms.controller.staff.request.SearchStaffManagementListRequestModel;
import com.logistics.tms.controller.staff.response.SearchStaffManagementListResponseModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2018/10/19 10:32
 */
@Slf4j
@Service
public class CarrierOrderAppletBiz {

    @Autowired
    private TCarrierOrderMapper carrierOrderMapper;
    @Autowired
    private TCarrierOrderTicketsMapper carrierOrderTicketsMapper;
    @Autowired
    private TCarrierOrderGoodsMapper carrierOrderGoodsMapper;
    @Autowired
    private TCarrierOrderOperateLogsMapper carrierOrderOperateLogsMapper;
    @Autowired
    private TCarrierOrderEventsMapper carrierOrderEventsMapper;
    @Autowired
    private TCarrierOrderVehicleHistoryMapper carrierOrderVehicleHistoryMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private CarrierOrderCommonBiz carrierOrderCommonBiz;
    @Autowired
    private ExtVehicleSettlementBiz extVehicleSettlementBiz;
    @Autowired
    private TDemandOrderMapper tDemandOrderMapper;
    @Autowired
    private CarrierOrderBiz carrierOrderBiz;
    @Autowired
    private RabbitMqPublishBiz rabbitMqPublishBiz;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Resource
    private BasicServiceClient basicServiceClient;
    @Autowired
    private TCarrierOrderLocationMapper tCarrierOrderLocationMapper;
    @Autowired
    private DriverBasicInfoBiz driverBasicInfoBiz;
    @Autowired
    private TStaffBasicMapper tStaffBasicMapper;
    @Autowired
    private TReachManagementMapper tReachManagementMapper;
    @Autowired
    private TReachAttachmentMapper tReachAttachmentMapper;
    @Autowired
    private ReachManagementBiz reachManagementBiz;
    @Resource
    private CarrierOrderTicketsAuditBiz carrierOrderTicketsAuditBiz;
    @Autowired
    private WorkOrderBiz workOrderBiz;
    @Resource
    private CustomerInOrderServiceApi customerInOrderServiceApi;
    @Autowired
    private TrayOrderServiceClient trayOrderServiceClient;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private ReservationOrderCommonBiz reservationOrderCommonBiz;
    @Resource
    private TCarrierOrderGoodsCodeMapper tCarrierOrderGoodsCodeMapper;
    @Resource
    private YeloLifeBasicDataServiceApi yeloLifeBasicDataServiceApi;
    @Resource
    private SysConfigBiz sysConfigBiz;
    @Resource
    private TCarrierDriverRelationMapper tCarrierDriverRelationMapper;
    @Resource
    private TCarrierOrderGoodsMapper tCarrierOrderGoodsMapper;
    @Resource
    private TExtDemandOrderRelationMapper tExtDemandOrderRelationMapper;


    /**
     * 查询运单列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo searchList(SearchCarrierOrderListAppRequestModel requestModel) {
        if (requestModel == null) {
            return new PageInfo<>();
        }

        if (requestModel.getStartTime() != null && requestModel.getEndTime() != null && requestModel.getStartTime().compareTo(requestModel.getEndTime()) > 0) {
            throw new BizException(CarrierDataExceptionEnum.STARTIM_ENDTIME_ERROR);
        }
        //查询登录司机与车主关系信息
        List<CarrierDriverRelationModel> driverModel = commonBiz.getLoginUserDriver();
        if (ListUtils.isEmpty(driverModel)) {
            return new PageInfo<>(new ArrayList<>());
        }
        //requestModel.setDriverModel(MapperUtils.mapper(driverModel, CarrierDriverRelationForAppletModel.class));

        //查询异常工单
        requestModel.setExcludeCarrierOrderIdList(workOrderBiz.getNeedExcludedOrderIds(WorkOrderTypeEnum.CARRIER_ORDER_TYPE, CommonConstant.INTEGER_THREE, null, driverModel.stream().map(CarrierDriverRelationModel::getDriverId).collect(Collectors.toList())));

        //分页查询
        requestModel.enablePaging();
        requestModel.setDriverId(driverModel.get(0).getDriverId());
        requestModel.setCarrierCompanyIds(driverModel.stream().map(CarrierDriverRelationModel::getCompanyCarrierId).collect(Collectors.toList()));
        List<Long> idsList = carrierOrderMapper.searchCarrierOrderIdsForApp(requestModel);
        PageInfo pageInfo = new PageInfo<>(idsList);
        if (ListUtils.isNotEmpty(idsList)) {
            List<SearchCarrierOrderListAppResponseModel> list = carrierOrderMapper.searchCarrierOrderForApp(StringUtils.listToString(idsList, ','));

            //查询需求单
            List<Long> demandOrderIdList = list.stream().map(SearchCarrierOrderListAppResponseModel::getDemandOrderId).distinct().collect(Collectors.toList());
            List<TDemandOrder> demandOrderList = tDemandOrderMapper.getByIds(StringUtils.listToString(demandOrderIdList, ','));
            Map<Long, TDemandOrder> demandOrderMap = demandOrderList.stream().collect(Collectors.toMap(TDemandOrder::getId, tDemandOrder -> tDemandOrder));

            //拼接数据
            for (SearchCarrierOrderListAppResponseModel model : list) {
                if (DemandOrderSourceEnum.SINOPEC_SYSTEM.getKey().equals(model.getDemandOrderSource())) {
                    TDemandOrder tDemandOrder = demandOrderMap.get(model.getDemandOrderId());
                    if (tDemandOrder != null) {
                        //拼接中石化相关数据
                        model.setOrderType(tDemandOrder.getOrderType());
                        model.setSinopecOrderNo(tDemandOrder.getSinopecOrderNo());
                    }
                }
                model.setEnableExtCarrierOrder(CommonConstant.ZERO);
                // 回收入
                if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(model.getDemandOrderEntrustType())){
                    //运单状态：≠已取消/已放空；
                    Integer ifCancel = model.getIfCancel();
                    Integer ifEmpty = model.getIfEmpty();
                    if (!CommonConstant.INTEGER_ONE.equals(ifCancel) && !CommonConstant.INTEGER_ONE.equals(ifEmpty)){
                        // 非补单
                        if (!model.getIfExtCarrierOrder().equals(CommonConstant.INTEGER_ONE)){
                            //查询是否货物是共享托盘
                            List<TCarrierOrderGoods> tCarrierOrderGoods = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderId(model.getCarrierOrderId());
                            if (ListUtils.isNotEmpty(tCarrierOrderGoods)){
                                model.setEnableExtCarrierOrder(CommonConstant.ONE);
                            }
                        }
                    }
                }
            }
            pageInfo.setList(list);
        }
        return pageInfo;
    }

    /**
     * 查询运单列表每个状态的数量
     * @param requestModel
     * @return
     */
    public SearchCarrierOrderCountResponseModel searchListAccount(SearchCarrierOrderListAppRequestModel requestModel) {
        //查询登录司机与车主关系信息
        List<CarrierDriverRelationModel> driverModel = commonBiz.getLoginUserDriver();
        if (ListUtils.isEmpty(driverModel)) {
            return new SearchCarrierOrderCountResponseModel();
        }
        requestModel.setDriverModel(MapperUtils.mapper(driverModel, CarrierDriverRelationForAppletModel.class));

        //查询异常工单
        requestModel.setExcludeCarrierOrderIdList(workOrderBiz.getNeedExcludedOrderIds(WorkOrderTypeEnum.CARRIER_ORDER_TYPE, CommonConstant.INTEGER_THREE, null, driverModel.stream().map(CarrierDriverRelationModel::getDriverId).collect(Collectors.toList())));

        return carrierOrderMapper.searchListAccountByApp(requestModel);
    }

    /**
     * 运单详情页
     * @param requestModel
     * @return
     */
    public CarrierOrderDetailAppResponseModel carrierOrderDetail(CarrierOrderIdRequestModel requestModel){

        // 实名认证校验
        driverBasicInfoBiz.checkRealNameAuth();

        //查询运单详情
        CarrierOrderDetailAppResponseModel responseModel = carrierOrderMapper.carrierOrderDetailByApp(requestModel.getCarrierOrderId());
        if (responseModel == null){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //校验运单上司机信息与登录人信息是否一致
        carrierOrderCommonBiz.checkDriver(responseModel.getCarrierOrderId(), responseModel.getCompanyCarrierId());

        //查询车主否存在
        TCompanyCarrier dbCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(responseModel.getCompanyCarrierId());
        if (dbCompanyCarrier == null || IfValidEnum.INVALID.getKey().equals(dbCompanyCarrier.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }
        //赋值此单子是否是我司
        responseModel.setIsOurCompany(dbCompanyCarrier.getLevel());

        //中石化的单子
        if (DemandOrderSourceEnum.SINOPEC_SYSTEM.getKey().equals(responseModel.getDemandOrderSource())){
            //查询对应的需求单
            TDemandOrder tDemandOrder = tDemandOrderMapper.selectByPrimaryKeyDecrypt(responseModel.getDemandOrderId());
            //查询对应的需求单
            if (tDemandOrder != null && IfValidEnum.VALID.getKey().equals(tDemandOrder.getValid())) {
                responseModel.setOrderType(tDemandOrder.getOrderType());
                responseModel.setSinopecOrderNo(tDemandOrder.getSinopecOrderNo());
            }
        }

        //查询运单有效的车辆司机记录
        TCarrierOrderVehicleHistory validHistory = carrierOrderVehicleHistoryMapper.getValidTopByCarrierOrderId(requestModel.getCarrierOrderId());
        if (validHistory != null){
            responseModel.setCancelOperatorNameTwo(validHistory.getCreatedBy());
            responseModel.setCancelTimeTwo(validHistory.getCreatedTime());
            responseModel.setVehicleNo(validHistory.getVehicleNo());
            responseModel.setDriverId(validHistory.getDriverId());
            responseModel.setDriverName(validHistory.getDriverName());
            responseModel.setDriverMobile(validHistory.getDriverMobile());
        }
        //查询运单最近的一条无效的车辆司机记录
        TCarrierOrderVehicleHistory invalidHistory = carrierOrderVehicleHistoryMapper.getInvalidTopByCarrierOrderId(requestModel.getCarrierOrderId());
        if (invalidHistory != null){
            responseModel.setCancelReasonTwo(invalidHistory.getRemark());
        }

        //如果是后台调度的，则调度人手机号为后台员工
        if (CommonConstant.INTEGER_ONE.equals(responseModel.getSource())){
            Result<UserDetailResponseModel> result = basicServiceClient.getUserInfoResult(responseModel.getDispatchUserId());
            if (result.isSuccess() && result.getData() != null){
                responseModel.setDispatchMobile(result.getData().getMobilePhone());
            }
        }

        //编码重量置为2位小数
        for (CarrierOrderDetailGoodsInfoAppModel coodModel : responseModel.getCarrierOrderDetailGoodsInfo()) {
            for (CarrierOrderDetailCodeModel carrierOrderGoodsCodeResponseModel : coodModel.getCodeDtoList()) {
                carrierOrderGoodsCodeResponseModel.setLoadAmount(carrierOrderGoodsCodeResponseModel.getLoadAmount().setScale(2, RoundingMode.HALF_UP));
                carrierOrderGoodsCodeResponseModel.setSignAmount(carrierOrderGoodsCodeResponseModel.getSignAmount().setScale(2, RoundingMode.HALF_UP));
                carrierOrderGoodsCodeResponseModel.setUnloadAmount(carrierOrderGoodsCodeResponseModel.getUnloadAmount().setScale(2, RoundingMode.HALF_UP));
            }
        }


        Map<String, String> configMap = sysConfigBiz.getSysConfig(ConfigKeyEnum.ConfigGroupCodeEnum.CODE_VERIFY_FLAG.getCode());
        if (MapUtils.isEmpty(configMap)) {
            throw new BizException(CarrierDataExceptionEnum.CODE_RULE_GROUP_NOT_EXIST);
        }
        String demandOrderSourceFlag = configMap.get(ConfigKeyEnum.DEMAND_SOURCE_FLAG.getValue());
        String driverType = configMap.get(ConfigKeyEnum.DRIVER_TYPE.getValue());
        String driverStatus = configMap.get(ConfigKeyEnum.DRIVER_STATUS.getValue());
        String demandOrderSourceType = configMap.get(ConfigKeyEnum.DEMAND_SOURCE_TYPE.getValue());
        responseModel.setIfCodeVerification(0);
        if (demandOrderSourceType.contains(responseModel.getEntrustType().toString())) {
            //查询是否需要开启编码核销
            if (demandOrderSourceFlag.contains(responseModel.getProjectLabel())) {
                if (responseModel.getDriverId() != null) {
                    //需要在判断司机类型
                    SearchStaffManagementListRequestModel searchStaffManagementListRequestModel = new SearchStaffManagementListRequestModel();
                    List<Long> driverIds = new ArrayList<>();
                    driverIds.add(responseModel.getDriverId());
                    String driverIdsJoin = driverIds.stream().map(Object::toString).collect(Collectors.joining(","));
                    searchStaffManagementListRequestModel.setStaffIds(driverIdsJoin);
                    List<Long> companyIdList = new ArrayList<>();
                    companyIdList.add(responseModel.getCompanyCarrierId());
                    List<SearchStaffManagementListResponseModel> staffList = tCarrierDriverRelationMapper.queryDriverList(searchStaffManagementListRequestModel, companyIdList);
                    if (ListUtils.isNotEmpty(staffList)) {
                        SearchStaffManagementListResponseModel searchStaffManagementListResponseModel = staffList.get(0);
                        Integer staffProperty = searchStaffManagementListResponseModel.getStaffProperty();
                        if (driverType.contains(staffProperty.toString())) {
                            Integer realNameAuthenticationStatus = searchStaffManagementListResponseModel.getRealNameAuthenticationStatus();
                            if (driverStatus.contains(realNameAuthenticationStatus.toString())) {
                                responseModel.setIfCodeVerification(1);
                            }
                        }
                    }
                }
            }
        }
        //查询是否有补单
        TExtDemandOrderRelation tExtDemandOrderRelation = tExtDemandOrderRelationMapper.selectByCarrierOrderCode(responseModel.getCarrierOrderCode());
        if (ObjectUtils.isNotEmpty(tExtDemandOrderRelation)) {
            responseModel.setHasExtCarrierOrder(CommonConstant.INTEGER_ONE);

        }
        return responseModel;
    }

    /**
     * 物流详情页
     * @param requestModel
     * @return
     */
    public CarrierOrderLogisticsDetailResponseModel carrierOrderLogisticsDetail(CarrierOrderIdRequestModel requestModel){
        //查询运单是否存在
        TCarrierOrder tCarrierOrder = carrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (tCarrierOrder == null){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //校验运单上司机信息与登录人信息是否一致
        carrierOrderCommonBiz.checkDriver(tCarrierOrder.getId(), tCarrierOrder.getCompanyCarrierId());

        //查询运单上司机车辆信息
        TCarrierOrderVehicleHistory tCarrierOrderVehicleHistory = carrierOrderVehicleHistoryMapper.getValidTopByCarrierOrderId(tCarrierOrder.getId());

        //查询运单事件（）
        List<CarrierOrderLogisticsEventResponseModel> eventList = carrierOrderEventsMapper.getEvent4App(tCarrierOrder.getId());

        //查询运单上票据信息（不查入库单）
        List<CarrierOrderBillResponseModel> ticketList = carrierOrderTicketsMapper.getTicketsByCarrierOrderIdByApp(tCarrierOrder.getId());
        if (ListUtils.isNotEmpty(ticketList)){
            List<String> pathList= new ArrayList<>();
            for (CarrierOrderBillResponseModel model : ticketList) {
                pathList.addAll(model.getTicketPath());
            }
            Map<String, String> pathMap = commonBiz.batchGetOSSFileUrl(pathList);
            for (CarrierOrderBillResponseModel model:ticketList) {
                List<String> realPathList=new ArrayList<>();
                for (String path : model.getTicketPath()) {
                    realPathList.add(configKeyConstant.fileAccessAddress+pathMap.get(path));
                }
                model.setTicketPath(realPathList);
            }
        }else{
            ticketList = new ArrayList<>();
        }

        //组装信息
        CarrierOrderLogisticsDetailResponseModel responseModel = new CarrierOrderLogisticsDetailResponseModel();
        responseModel.setCarrierOrderCode(tCarrierOrder.getCarrierOrderCode());
        responseModel.setDriverName(tCarrierOrderVehicleHistory.getDriverName());
        responseModel.setDriverPhone(tCarrierOrderVehicleHistory.getDriverMobile());
        responseModel.setVehicleNo(tCarrierOrderVehicleHistory.getVehicleNo());
        responseModel.setEventList(eventList);
        responseModel.setTicketList(ticketList);
        return responseModel;
    }

    /**
     * 提货
     * @param requestModel
     * @return
     */
    @Transactional
    public Result pickUp(CarrierOrderLoadRequestModel requestModel) {

        //运单状态校验
        TCarrierOrder tCarrierOrder = this.checkCarrierDriverIfEnabled(requestModel.getCarrierOrderId(), CarrierOrderEventsTypeEnum.PICK_UP.getKey());
        boolean ifYelolifeRecycleByCode = EntrustTypeEnum.LIFE_TRANSFER.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())
                && CarrierOrderIfRecycleByCodeEnum.RECYCLE_BY_CODE.getCode().equals(tCarrierOrder.getIfRecycleByCode());

        // 提卸货前校验
        if (!ifYelolifeRecycleByCode) {
            verifyBeforeLoadUnload(requestModel.getGoodsList(), requestModel.getGoodsUnit());
        }

        if (tCarrierOrder.getIfExtCarrierOrder().equals(CommonConstant.INTEGER_ONE)){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EXT_ORDER);
        }



        //统计入参货物数量
        BigDecimal count = BigDecimal.ZERO;
        Map<Long,BigDecimal> pageGoodsLoadAmountMap=new HashMap<>(); //装货 货物id  装货数映射

        Map<Long,List<LoadGoodsForYeloLifeRequestCodeModel>> goodCodeMap = new HashMap<>();
        List<String> code = new ArrayList<>();
        for (CarrierOrderGoodsLoadUnloadRequestModel model : requestModel.getGoodsList()) {
            count = count.add(model.getCount());
            pageGoodsLoadAmountMap.put(model.getGoodsId(),model.getCount());
            if (ListUtils.isNotEmpty(model.getCodeDtoList())) {
                goodCodeMap.put(model.getGoodsId(), model.getCodeDtoList());
                code.addAll(model.getCodeDtoList().stream().map(LoadGoodsForYeloLifeRequestCodeModel::getYeloCode).collect(Collectors.toList()));
            }
        }
        if (CollectionUtil.isNotEmpty(code) && ifYelolifeRecycleByCode){
            if (code.size() != new HashSet<>(code).size()) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_GOODS_CODE_CANNOT_DEP);
            }
            Result<List<String>> listResult = yeloLifeBasicDataServiceApi.verifyGoodsCodeList(code);
            listResult.throwException();
            List<String> codes = listResult.getData();
            if (code.size() != codes.size()) {
                code.removeAll(codes);
                return new Result(CommonConstant.CODE_NOT_EXIST,code,CarrierDataExceptionEnum.CARRIER_ORDER_GOODS_CODE_NOT_STANDARD.getMsg());
            }
        }
        //总数量不能小于0
        if (!ifYelolifeRecycleByCode) {
            if (count.compareTo(BigDecimal.ZERO) <= CommonConstant.INTEGER_ZERO) {
                if (GoodsUnitEnum.BY_WEIGHT.getKey().equals(requestModel.getGoodsUnit())) {
                    throw new BizException(CarrierDataExceptionEnum.TOTAL_EXPECT_AMOUNT_NOT_WEIGHT);
                } else {
                    throw new BizException(CarrierDataExceptionEnum.TOTAL_EXPECT_AMOUNT_NOT_NUMBER);
                }
            }
        }

        if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())){
            // 回收入 不再允许多提（小程序独有逻辑）
            List<TCarrierOrderGoods> tCarrierOrderGoodsList = carrierOrderGoodsMapper.selectGoodsByCarrierOrderId(tCarrierOrder.getId());
            tCarrierOrderGoodsList.forEach(e -> {
                if (pageGoodsLoadAmountMap.get(e.getId()) != null
                        && pageGoodsLoadAmountMap.get(e.getId()).compareTo(e.getLoadAmount()) > 0) {
                    throw new BizException(CarrierDataExceptionEnum.EXT_CARRIER_ORDER_CANNOT_MORE_PICK_UP);
                }else {
                    throw new BizException(CarrierDataExceptionEnum.GOODS_LIST_EMPTY);
                }
            });

        }

        // 获取提货方式Enum
        CarrierOrderDeliverMethodEnum deliverMethodEnum =
                CarrierOrderDeliverMethodEnum.getEnumByKey(requestModel.getDeliveryMethod());

        // 回收入库
        if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())) {
            // 获取云盘提货状态
            LeyiPickupConfirmResponseModel confirmDetail = pickupConfirm(tCarrierOrder.getCarrierOrderCode(), deliverMethodEnum);
            CarrierDataExceptionEnum exceptionEnum = CarrierOrderDeliverMethodEnum.BILL_PICKUP.equals(deliverMethodEnum) ?
                    CarrierDataExceptionEnum.CARRIER_ORDER_PICKUP_BILL_CONFIRM_STATE_ERROR :
                    CarrierDataExceptionEnum.CARRIER_ORDER_PICKUP_QRCODE_CONFIRM_STATE_ERROR;
            ExceptionUtils.isTure(CommonConstant.INTEGER_ZERO.equals(confirmDetail.getConfirmStatus()))
                    .throwMessage(exceptionEnum);


            //二维码提货，提货数量必须与云盘确认数量相等
            if (CarrierOrderDeliverMethodEnum.QRCODE_PICKUP.equals(deliverMethodEnum)
                    && count.compareTo(confirmDetail.getLoadingCount()) != CommonConstant.INTEGER_ZERO){
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_PICKUP_QRCODE_CONFIRM_COUNT_ERROR);
            }
        }
        // 回收出库类型
        else if (EntrustTypeEnum.RECYCLE_OUT.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())) {
            // 实体数不能大于预提数
            if (count.compareTo(tCarrierOrder.getExpectAmount()) > CommonConstant.INTEGER_ZERO) {
                throw new BizException(CarrierDataExceptionEnum.RECYCLE_OUT_LOAD_AMOUNT_MAX);
            }
            // 提货方式校验
            ExceptionUtils.isTure(CarrierOrderDeliverMethodEnum.QRCODE_PICKUP.equals(deliverMethodEnum))
                    .throwMessage(CarrierDataExceptionEnum.CARRIER_ORDER_PICKUP_DELIVER_METHOD_ERROR);
        }
        // 非回收类型校验
        else {
            // 提货方式校验
            ExceptionUtils.isTure(CarrierOrderDeliverMethodEnum.QRCODE_PICKUP.equals(deliverMethodEnum))
                    .throwMessage(CarrierDataExceptionEnum.CARRIER_ORDER_PICKUP_DELIVER_METHOD_ERROR);

            // 提货总数量与出库总数量必须相等
            if (BigDecimal.ZERO.compareTo(tCarrierOrder.getLoadAmount()) != CommonConstant.INTEGER_ZERO
                    && tCarrierOrder.getLoadAmount().compareTo(count) != CommonConstant.INTEGER_ZERO){
                StringBuilder loadAmountNumber = new StringBuilder();
                loadAmountNumber
                        .append(tCarrierOrder.getLoadAmount().stripTrailingZeros().toPlainString())
                        .append(GoodsUnitEnum.getEnum(tCarrierOrder.getGoodsUnit()).getUnit());
                String errorMsg = MessageFormat.format(CarrierDataExceptionEnum.CARRIER_ORDER_LOAD_AMOUNT_ERROR.getMsg(),
                                tCarrierOrder.getCarrierOrderCode(), loadAmountNumber);
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_LOAD_AMOUNT_ERROR.getCode(), errorMsg);
            }
        }

        List<TCarrierOrderGoods> dbTCarrierOrderGoods = new ArrayList<>();
        //发货、调拨、退货、供应商直配、采购、退货仓库配送、退货调拨、新生销售
        List<Integer> entrustTypeListModel = Arrays.asList(EntrustTypeEnum.DELIVER.getKey(), EntrustTypeEnum.TRANSFERS.getKey(),
                EntrustTypeEnum.RETURN_GOODS.getKey(), EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey(), EntrustTypeEnum.PROCUREMENT.getKey(),
                EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey(), EntrustTypeEnum.RETURN_GOODS_TRANSFERS.getKey(), EntrustTypeEnum.LIFE_SALE.getKey());
        if (entrustTypeListModel.contains(tCarrierOrder.getDemandOrderEntrustType())) {
            //货物完成出库才能提货
            if (!CarrierOrderOutStatusEnum.FINISH_OUT.getKey().equals(tCarrierOrder.getOutStatus())) {
                throw new BizException(CarrierDataExceptionEnum.NOT_ALLOWED_PICKUP);
            }

            //每个货物对应的数量必须跟出库数量一致
            dbTCarrierOrderGoods = carrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(ConverterUtils.toString(requestModel.getCarrierOrderId()));
            for (TCarrierOrderGoods good : dbTCarrierOrderGoods) {
                if (good.getLoadAmount().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO
                        && good.getLoadAmount().compareTo(pageGoodsLoadAmountMap.get(good.getId())) != CommonConstant.INTEGER_ZERO) {
                    throw new BizException(CarrierDataExceptionEnum.LOAD_AMOUNT_ERROR_FOR_LE_YI);
                }
            }
        }

        if (ListUtils.isEmpty(dbTCarrierOrderGoods)) {
            dbTCarrierOrderGoods = carrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(ConverterUtils.toString(requestModel.getCarrierOrderId()));
        }

        //修改运单状态
        Date now = new Date();
        TCarrierOrder carrierOrder = new TCarrierOrder();
        carrierOrder.setId(tCarrierOrder.getId());
        carrierOrder.setStatus(CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey());
        carrierOrder.setStatusUpdateTime(now);
        carrierOrder.setLoadTime(now);
        carrierOrder.setLoadAmount(count);
        carrierOrder.setLoadAmountExpect(count);
        carrierOrder.setDeliveryMethod(deliverMethodEnum.getKey());
        commonBiz.setBaseEntityModify(carrierOrder,BaseContextHandler.getUserName());

        tCarrierOrder.setStatus(carrierOrder.getStatus());
        tCarrierOrder.setStatusUpdateTime(now);
        tCarrierOrder.setLoadTime(carrierOrder.getLoadTime());
        tCarrierOrder.setLoadAmount(carrierOrder.getLoadAmount());
        tCarrierOrder.setLoadAmountExpect(carrierOrder.getLoadAmountExpect());

        //修改运单货物信息
        TCarrierOrderGoods upOrderGood;
        List<TCarrierOrderGoods> upList = new ArrayList<>();
        List<TCarrierOrderGoodsCode> insertGoodsCodeList = new ArrayList<>();
        // 特殊逻辑 如果是新生回收的话，并且是按编码回收，那么需要下落编码code表和 good做关联，并且good的所有数量都是code表的综合 ----jiang 2.44
        boolean ifYeloLifeRecycleAndByCode = tCarrierOrder.getIfRecycleByCode().equals(CommonConstant.INTEGER_ONE) &&
                EntrustTypeEnum.LIFE_TRANSFER.getKey().equals(tCarrierOrder.getDemandOrderEntrustType());
        BigDecimal carrierOrderLoad = new BigDecimal("0.00");
        for (TCarrierOrderGoods good : dbTCarrierOrderGoods) {
            upOrderGood = new TCarrierOrderGoods();
            upOrderGood.setId(good.getId());
            BigDecimal loadAmount = pageGoodsLoadAmountMap.get(good.getId());
            upOrderGood.setLoadAmount(loadAmount);
            if (ifYeloLifeRecycleAndByCode){
                loadAmount = new BigDecimal("0.00");
                if (goodCodeMap.get(good.getId()) != null) {
                    for (LoadGoodsForYeloLifeRequestCodeModel  e : goodCodeMap.get(good.getId())){
                        TCarrierOrderGoodsCode tCarrierOrderGoodsCode = new TCarrierOrderGoodsCode();
                        tCarrierOrderGoodsCode.setCarrierOrderGoodsId(good.getId());
                        tCarrierOrderGoodsCode.setLoadAmount(e.getWeight());
                        tCarrierOrderGoodsCode.setYeloGoodCode(e.getYeloCode());
                        tCarrierOrderGoodsCode.setUnit(e.getUnit());
                        commonBiz.setBaseEntityAdd(tCarrierOrderGoodsCode, BaseContextHandler.getUserName());
                        insertGoodsCodeList.add(tCarrierOrderGoodsCode);
                        loadAmount = loadAmount.add(e.getWeight());
                    }
                }
                upOrderGood.setLoadAmount(loadAmount);
                carrierOrderLoad = carrierOrderLoad.add(loadAmount);
            }
            commonBiz.setBaseEntityModify(upOrderGood, BaseContextHandler.getUserName());
            upList.add(upOrderGood);
        }

        if(ifYeloLifeRecycleAndByCode){
            count = carrierOrderLoad;
            carrierOrder.setLoadAmount(carrierOrderLoad);
            carrierOrder.setLoadAmountExpect(carrierOrderLoad);
            tCarrierOrder.setLoadAmount(carrierOrder.getLoadAmount());
            tCarrierOrder.setLoadAmountExpect(carrierOrder.getLoadAmountExpect());

        }
        carrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(carrierOrder);
        if (ListUtils.isNotEmpty(upList)) {
            carrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(upList);
        }
        if (ListUtils.isNotEmpty(insertGoodsCodeList)) {
            tCarrierOrderGoodsCodeMapper.batchInsertSelective(insertGoodsCodeList);
        }
        List<String> outStockTickets = null;
        //处理运单单据
        int ticketCount = CommonConstant.INTEGER_ZERO;
        if (ListUtils.isNotEmpty(requestModel.getTickets())) {

            //根据单据类型分组
            Map<Integer, List<CarrierOrderTicketRequestModel>> ticketsMap = requestModel.getTickets().stream().collect(Collectors.groupingBy(CarrierOrderTicketRequestModel::getTicketType));
            //出库单
            List<CarrierOrderTicketRequestModel> carrierOrderTickets = ticketsMap.get(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getKey());
            //提货现场图片
            List<CarrierOrderTicketRequestModel> carrierOrderLoadScenePic = ticketsMap.get(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_LOAD_SCENE_PIC.getKey());

            //已存在和新添加的单据不能超过6张
            if (ListUtils.isNotEmpty(carrierOrderTickets)) {
                ticketCount = carrierOrderTickets.size();
                List<GetTicketsResponseModel> existTicketsCount = carrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(requestModel.getCarrierOrderId(), CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getKey());
                if (ticketCount + existTicketsCount.size() > CommonConstant.INTEGER_SIX) {
                    throw new BizException(CarrierDataExceptionEnum.PICTURES_OVER_COUNT);
                }
                //新增出库单据
                List<String> ticketList = carrierOrderTickets.stream().map(CarrierOrderTicketRequestModel::getSrc).collect(Collectors.toList());
                outStockTickets = this.addCarrierOrderTickets(ticketList, tCarrierOrder, CopyFileTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS, CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS);
            }

            //新增现场图片
            if (ListUtils.isNotEmpty(carrierOrderLoadScenePic)) {
                List<String> scenePicList = carrierOrderLoadScenePic.stream().map(CarrierOrderTicketRequestModel::getSrc).collect(Collectors.toList());
                addCarrierOrderTickets(scenePicList, tCarrierOrder, CopyFileTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS, CarrierOrderTicketsTypeEnum.CARRIER_ORDER_LOAD_SCENE_PIC);
            }
        }

        //记录提货事件
        String unit = GoodsUnitEnum.getEnum(tCarrierOrder.getGoodsUnit()).getUnit();
        CarrierOrderEventsTypeEnum pickUpEventEnum = CarrierOrderEventsTypeEnum.PICK_UP;
        addCarrierOrderEvents(requestModel.getCarrierOrderId(), pickUpEventEnum, ticketCount, StripTrailingZerosUtils.stripTrailingZerosToString(count) + unit);

        //记录提货日志
        CarrierOrderOperateLogsTypeEnum pickUpLogEnum = CarrierOrderOperateLogsTypeEnum.PICK_UP;
        addCarrierOrderOperateLogs(requestModel.getCarrierOrderId(), pickUpLogEnum, StripTrailingZerosUtils.stripTrailingZerosToString(count) + unit);
        //提卸货同步托盘
        Map<Long, BigDecimal> pageLoadAmountMap = new HashMap<>();
        pageLoadAmountMap.put(tCarrierOrder.getId(), count);

        //如果是回收类型，将提货票据同步给云盘
        Map<String, List<String>> outStockCarrierOrderTicketsMap = null;//map为：运单号-》提货票据路径
        if ((EntrustTypeEnum.RECYCLE_IN.getKey().equals(tCarrierOrder.getDemandOrderEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(tCarrierOrder.getDemandOrderEntrustType()))
                && ListUtils.isNotEmpty(outStockTickets)) {
            outStockCarrierOrderTicketsMap = new HashMap<>();
            outStockCarrierOrderTicketsMap.put(tCarrierOrder.getCarrierOrderCode(), outStockTickets);
        }

        //回收类型(司机承担地推触达)
        if ((EntrustTypeEnum.RECYCLE_IN.getKey().equals(tCarrierOrder.getDemandOrderEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(tCarrierOrder.getDemandOrderEntrustType()))
                && requestModel.getSiteOtherPallets() != null) {
            //查询触达信息
            TReachManagement tReachManagement = tReachManagementMapper.selectByCarrierOrderId(tCarrierOrder.getId());
            if (tReachManagement != null) {
                SiteOtherPalletsRequestModel siteOtherPallets = requestModel.getSiteOtherPallets();
                TReachManagement tReachManagementUp = new TReachManagement();
                tReachManagementUp.setId(tReachManagement.getId());
                tReachManagementUp.setEmployTraysAmount(siteOtherPallets.getEmployTraysAmount());
                tReachManagementUp.setEmptyTraysAmount(siteOtherPallets.getEmptyTraysAmount());
                commonBiz.setBaseEntityModify(tReachManagementUp, BaseContextHandler.getUserName());
                tReachManagementMapper.updateByPrimaryKeySelectiveEncrypt(tReachManagementUp);

                //保存带料托盘图片
                List<String> sharedTrayPics = siteOtherPallets.getSharedTrayPics();
                if (ListUtils.isNotEmpty(sharedTrayPics)) {
                    TReachAttachment tReachAttachment;
                    List<TReachAttachment> tReachAttachmentList = new ArrayList<>();
                    for (String picUrl : sharedTrayPics) {
                        tReachAttachment = new TReachAttachment();
                        tReachAttachment.setReachManagementId(tReachManagement.getId());
                        tReachAttachment.setAttachmentType(CommonConstant.INTEGER_ONE);
                        tReachAttachment.setAttachmentPath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DRIVER_REACH_ATTACHMENT.getKey(), "", picUrl, null));
                        commonBiz.setBaseEntityAdd(tReachAttachment, BaseContextHandler.getUserName());
                        tReachAttachmentList.add(tReachAttachment);
                    }
                    tReachAttachmentMapper.batchInsert(tReachAttachmentList);
                }
            }
        }

        //预约单解除关联运单
        reservationOrderCommonBiz.disassociateCarrierOrder(List.of(tCarrierOrder.getId()));

        //提货同步托盘
        carrierOrderBiz.loadUnloadSync(CommonConstant.INTEGER_ONE,
                tCarrierOrder.getId().toString(),
                Collections.singletonList(tCarrierOrder),
                pageLoadAmountMap,
                pageGoodsLoadAmountMap,
                outStockCarrierOrderTicketsMap);

        // 发布智能推送运单提货节点事件
        applicationContext.publishEvent(new WorkGroupEventModel()
                .setWorkGroupPushBoModels(tCarrierOrder.getId(),
                        tCarrierOrder.getDemandOrderSource(),
                        tCarrierOrder.getDemandOrderEntrustType(),
                        tCarrierOrder.getProjectLabel(),
                        WorkGroupOrderTypeEnum.CARRIER_ORDER,
                        WorkGroupOrderNodeEnum.CARRIER_ORDER_LOAD));

        //异步方法放在最后执行
        //节点信息推送微信公众号
        AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.singleWxPush(carrierOrder));
        return Result.success(true);
    }

    /**
     * 卸货
     * @param requestModel
     * @return
     */
    @Transactional
    public void unloading(CarrierOrderUnloadRequestModel requestModel){
        //运单状态校验
        TCarrierOrder tCarrierOrder = this.checkCarrierDriverIfEnabled(requestModel.getCarrierOrderId(), CarrierOrderEventsTypeEnum.UNLOADING.getKey());
        boolean ifYelolifeRecycleByCode = EntrustTypeEnum.LIFE_TRANSFER.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())
                && CarrierOrderIfRecycleByCodeEnum.RECYCLE_BY_CODE.getCode().equals(tCarrierOrder.getIfRecycleByCode());

        // 提卸货前校验
        if (!ifYelolifeRecycleByCode) {
            //入参校验
            verifyBeforeLoadUnload(requestModel.getGoodsList(), requestModel.getGoodsUnit());
        }


        List<TCarrierOrderGoods> goodsList = carrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(ConverterUtils.toString(requestModel.getCarrierOrderId()));
        BigDecimal count = BigDecimal.ZERO;
        Map<Long,BigDecimal> pageGoodsLoadAmountMap=new HashMap<>(); //装货 货物id  装货数映射
        for (CarrierOrderGoodsLoadUnloadRequestModel model : requestModel.getGoodsList()) {
            for (TCarrierOrderGoods goods : goodsList) {
                if (model.getGoodsId().equals(goods.getId()) && model.getCount().compareTo(goods.getLoadAmount()) > CommonConstant.INTEGER_ZERO){
                    throw new BizException(CarrierDataExceptionEnum.UNLOADING_AMOUNT_CANT_GT_LOAD_AMOUNT);
                }
            }
            count = count.add(model.getCount());
            pageGoodsLoadAmountMap.put(model.getGoodsId(),model.getCount());
        }
        if (!ifYelolifeRecycleByCode) {
            if (count.compareTo(BigDecimal.ZERO) <= CommonConstant.INTEGER_ZERO) {
                if (requestModel.getGoodsUnit().equals(GoodsUnitEnum.BY_WEIGHT.getKey())) {
                    throw new BizException(CarrierDataExceptionEnum.TOTAL_EXPECT_AMOUNT_NOT_WEIGHT);
                } else {
                    throw new BizException(CarrierDataExceptionEnum.TOTAL_EXPECT_AMOUNT_NOT_NUMBER);
                }
            }
        }
        if (count.compareTo(tCarrierOrder.getLoadAmount()) > CommonConstant.INTEGER_ZERO){
            throw new BizException(CarrierDataExceptionEnum.UNLOADING_AMOUNT_CANT_GT_LOAD_AMOUNT);
        }
        String carrierOrderGoodsJoin = goodsList.stream().map(tCarrierOrderGoods -> tCarrierOrderGoods.getId().toString()).collect(Collectors.joining(","));


        //查询运单货物编码
        Map<String, TCarrierOrderGoodsCode> tCarrierOrderGoodsCodesMap = new HashMap<>();
        if (CarrierOrderIfRecycleByCodeEnum.RECYCLE_BY_CODE.getCode().equals(tCarrierOrder.getIfRecycleByCode())) {
            List<TCarrierOrderGoodsCode> tCarrierOrderGoodsCodes = tCarrierOrderGoodsCodeMapper.selectGoodsCodeByGoodsIds(carrierOrderGoodsJoin);
            tCarrierOrderGoodsCodesMap = tCarrierOrderGoodsCodes.stream().collect(Collectors.toMap(tCarrierOrderGoodsCode -> tCarrierOrderGoodsCode.getCarrierOrderGoodsId() + "-" + tCarrierOrderGoodsCode.getYeloGoodCode(), Function.identity()));
        }

        //修改运单状态
        Date now = new Date();
        TCarrierOrder carrierOrder = new TCarrierOrder();
        carrierOrder.setId(tCarrierOrder.getId());
        carrierOrder.setStatus(CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey());
        carrierOrder.setStatusUpdateTime(now);
        carrierOrder.setUnloadAmount(count);
        carrierOrder.setUnloadAmountExpect(count);
        carrierOrder.setUnloadTime(now);
        commonBiz.setBaseEntityModify(carrierOrder,BaseContextHandler.getUserName());
        carrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(carrierOrder);

        //提卸货同步数据逻辑需要
        tCarrierOrder.setStatus(carrierOrder.getStatus());
        tCarrierOrder.setStatusUpdateTime(carrierOrder.getStatusUpdateTime());
        tCarrierOrder.setUnloadAmount(carrierOrder.getUnloadAmount());
        tCarrierOrder.setUnloadAmountExpect(carrierOrder.getUnloadAmountExpect());
        tCarrierOrder.setUnloadTime(carrierOrder.getUnloadTime());


        //修改运单货物信息 货物明细信息(if_recycle_by_code=1 是否按码回收)
        TCarrierOrderGoods carrierOrderGoods;
        List<TCarrierOrderGoods> upList = new ArrayList<>();
        List<TCarrierOrderGoodsCode> updateCarrierOrderGoodsCodes = new ArrayList<>();
        for (CarrierOrderGoodsLoadUnloadRequestModel model:requestModel.getGoodsList()) {
            carrierOrderGoods = new TCarrierOrderGoods();
            carrierOrderGoods.setId(model.getGoodsId());
            carrierOrderGoods.setUnloadAmount(model.getCount());
            commonBiz.setBaseEntityModify(carrierOrderGoods, BaseContextHandler.getUserName());
            upList.add(carrierOrderGoods);

            //如果是货物编码回收 还需要更新货物编码
            if (CarrierOrderIfRecycleByCodeEnum.RECYCLE_BY_CODE.getCode().equals(tCarrierOrder.getIfRecycleByCode())) {
                for (LoadGoodsForYeloLifeRequestCodeModel codeModel : model.getCodeDtoList()) {
                    TCarrierOrderGoodsCode tCarrierOrderGoodsCode = tCarrierOrderGoodsCodesMap.get(model.getGoodsId() + "-" + codeModel.getYeloCode());
                    TCarrierOrderGoodsCode updateCarrierOrderGoodsCode = new TCarrierOrderGoodsCode();
                    updateCarrierOrderGoodsCode.setId(tCarrierOrderGoodsCode.getId());
                    updateCarrierOrderGoodsCode.setUnloadAmount(codeModel.getWeight());
                    updateCarrierOrderGoodsCode.setUnit(codeModel.getUnit());
                    updateCarrierOrderGoodsCodes.add(updateCarrierOrderGoodsCode);
                }
            }
        }
        if (ListUtils.isNotEmpty(upList)) {
            carrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(upList);
        }
        if (CollectionUtil.isNotEmpty(updateCarrierOrderGoodsCodes)) {
            tCarrierOrderGoodsCodeMapper.batchUpdate(updateCarrierOrderGoodsCodes);
        }
        //处理运单单据
        int ticketCount = CommonConstant.INTEGER_ZERO;
        //出库单据
        List<String> signTicketsList = null;
        if (ListUtils.isNotEmpty(requestModel.getTickets())) {

            //根据单据类型分组
            Map<Integer, List<CarrierOrderTicketRequestModel>> ticketsMap = requestModel.getTickets().stream().collect(Collectors.groupingBy(CarrierOrderTicketRequestModel::getTicketType));
            //签收单
            List<CarrierOrderTicketRequestModel> carrierOrderTickets = ticketsMap.get(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey());
            //卸货现场图片
            List<CarrierOrderTicketRequestModel> carrierOrderUnloadScenePic = ticketsMap.get(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_SCENE_PIC.getKey());

            //已存在和新添加的单据不能超过6张
            if (ListUtils.isNotEmpty(carrierOrderTickets)) {
                ticketCount = carrierOrderTickets.size();
                List<GetTicketsResponseModel> existTicketsCount = carrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(requestModel.getCarrierOrderId(), CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey());
                if (ticketCount + existTicketsCount.size() > CommonConstant.INTEGER_SIX) {
                    throw new BizException(CarrierDataExceptionEnum.PICTURES_OVER_COUNT);
                }
                //新增出库单据
                List<String> ticketList = carrierOrderTickets.stream().map(CarrierOrderTicketRequestModel::getSrc).collect(Collectors.toList());
                signTicketsList = addCarrierOrderTickets(ticketList, tCarrierOrder, CopyFileTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS, CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS);
            }

            //新增现场图片
            if (ListUtils.isNotEmpty(carrierOrderUnloadScenePic)) {
                List<String> scenePicList = carrierOrderUnloadScenePic.stream().map(CarrierOrderTicketRequestModel::getSrc).collect(Collectors.toList());
                addCarrierOrderTickets(scenePicList, tCarrierOrder, CopyFileTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS, CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_SCENE_PIC);
            }
        }

        //记录卸货事件
        String unit = GoodsUnitEnum.getEnum(tCarrierOrder.getGoodsUnit()).getUnit();
        addCarrierOrderEvents(requestModel.getCarrierOrderId(), CarrierOrderEventsTypeEnum.UNLOADING, ticketCount, StripTrailingZerosUtils.stripTrailingZerosToString(count) + unit);

        //记录提货日志
        addCarrierOrderOperateLogs(requestModel.getCarrierOrderId(), CarrierOrderOperateLogsTypeEnum.UNLOADING, StripTrailingZerosUtils.stripTrailingZerosToString(count) + unit);

        //生成车辆结算数据（外部车辆）
        List<Long> createSettlementIdList = new ArrayList<>();
        createSettlementIdList.add(requestModel.getCarrierOrderId());
        extVehicleSettlementBiz.createExtVehicleSettlement(createSettlementIdList, false, false);
        Map<Long, BigDecimal> pageLoadAmountMap = new HashMap<>();
        pageLoadAmountMap.put(tCarrierOrder.getId(), count);

        // 创建审核回单
        carrierOrderTicketsAuditBiz.createOrResetReceiptTicketAudit(new CreateOrResetReceiptTicketAuditBoModel()
                .setSource(tCarrierOrder.getDemandOrderSource())
                .setEntrustType(tCarrierOrder.getDemandOrderEntrustType())
                .setTicketType(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey())
                .setCarrierOrderId(tCarrierOrder.getId())
                .setCarrierOrderCode(tCarrierOrder.getCarrierOrderCode())
                .setCarrierOrderStatus(CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey())
                .setTicketsPathList(signTicketsList));

        //预约单解除关联运单
        reservationOrderCommonBiz.disassociateCarrierOrder(List.of(tCarrierOrder.getId()));

        //如果是发货、回收入库、回收出库、供应商直配、退货仓库配送类型，将卸货票据同步给云盘
        Map<String, List<String>> carrierOrderTicketsMap = null;//map为：运单号-》卸货票据路径
        List<Integer> entrustTypeListModel = Arrays.asList(EntrustTypeEnum.DELIVER.getKey(), EntrustTypeEnum.RECYCLE_IN.getKey(),
                EntrustTypeEnum.RECYCLE_OUT.getKey(), EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey(), EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey());
        if (entrustTypeListModel.contains(tCarrierOrder.getDemandOrderEntrustType()) && ListUtils.isNotEmpty(signTicketsList)) {
            carrierOrderTicketsMap = new HashMap<>();
            carrierOrderTicketsMap.put(tCarrierOrder.getCarrierOrderCode(), signTicketsList);
        }

        //卸货同步托盘
        carrierOrderBiz.loadUnloadSync(CommonConstant.INTEGER_TWO,
                tCarrierOrder.getId().toString(),
                Collections.singletonList(tCarrierOrder),
                pageLoadAmountMap,
                pageGoodsLoadAmountMap,
                carrierOrderTicketsMap);

        // 发布智能推送运单卸货节点事件
        applicationContext.publishEvent(new WorkGroupEventModel()
                .setWorkGroupPushBoModels(tCarrierOrder.getId(),
                        tCarrierOrder.getDemandOrderSource(),
                        tCarrierOrder.getDemandOrderEntrustType(),
                        tCarrierOrder.getProjectLabel(),
                        WorkGroupOrderTypeEnum.CARRIER_ORDER,
                        WorkGroupOrderNodeEnum.CARRIER_ORDER_UNLOAD));

        //异步方法放在最后执行
        //节点信息推送微信公众号
        AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.singleWxPush(carrierOrder));
    }

    /**
     * 到达提货地
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public void arrivePickUp(CarrierOrderArriveLoadUnloadRequestModel requestModel) {
        TCarrierOrder tCarrierOrder = this.checkCarrierDriverIfEnabled(requestModel.getCarrierOrderId(),
                CarrierOrderEventsTypeEnum.ARRIVED_PICK_UP.getKey());

        appletArrivePickUp(requestModel, tCarrierOrder);
    }

    /**
     * 到达提货地v2(司机承担触达)
     *
     * @param requestModel
     */
    @Transactional
    public void arrivePickUpV2(ArrivePickUpV2RequestModel requestModel) {
        //获取当前登录司机Id
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        if (CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.DATA_ERROR_AND_RE_LOGIN);
        }

        //校验运单状态
        TCarrierOrder tCarrierOrder = this.checkCarrierDriverIfEnabled(requestModel.getCarrierOrderId(),
                CarrierOrderEventsTypeEnum.ARRIVED_PICK_UP.getKey());

        //查询司机信息
        TStaffBasic tStaffBasic = tStaffBasicMapper.selectByPrimaryKey(loginDriverAppletUserId);
        if (tStaffBasic == null || IfValidEnum.INVALID.getKey().equals(tStaffBasic.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }

        //根据司机机构判校验参数
        checkArrivePickUpParams(requestModel, tStaffBasic);

        //回收类型才能操作
        if (!EntrustTypeEnum.RECYCLE_IN.getKey().equals(tCarrierOrder.getDemandOrderEntrustType()) && !EntrustTypeEnum.RECYCLE_OUT.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())) {
            throw new BizException(CarrierDataExceptionEnum.ONLY_RECYCLE_LOAD);
        }

        //到达提货地
        CarrierOrderArriveLoadUnloadRequestModel arrivePickUpModel = new CarrierOrderArriveLoadUnloadRequestModel();
        arrivePickUpModel.setCarrierOrderId(tCarrierOrder.getId());
        arrivePickUpModel.setLongitude(requestModel.getReachLongitude());
        arrivePickUpModel.setLatitude(requestModel.getReachLatitude());
        arrivePickUpModel.setTmpUrl(requestModel.getTerminalTmpUrl());
        appletArrivePickUp(arrivePickUpModel, tCarrierOrder);

        //查询省市区code
        Map<String, String> provinceCityArea = commonBiz.getProvinceCityArea(requestModel.getReachAddressDetail());

        //保存触达信息
        TReachManagement tReachManagement = new TReachManagement();
        MapperUtils.mapper(requestModel, tReachManagement);
        tReachManagement.setCarrierOrderCode(tCarrierOrder.getCarrierOrderCode());
        tReachManagement.setReachDriverName(tStaffBasic.getName());
        tReachManagement.setReachDriverPhone(tStaffBasic.getMobile());
        tReachManagement.setReachProvinceId(ConverterUtils.toLong(provinceCityArea.get(EmailConstant.UNLOAD_PROVINCE_ID)));
        tReachManagement.setReachProvinceName(provinceCityArea.get(EmailConstant.UNLOAD_PROVINCE_NAME));
        tReachManagement.setReachCityId(ConverterUtils.toLong(provinceCityArea.get(EmailConstant.UNLOAD_CITY_ID)));
        tReachManagement.setReachCityName(provinceCityArea.get(EmailConstant.UNLOAD_CITY_NAME));
        tReachManagement.setReachAreaId(ConverterUtils.toLong(provinceCityArea.get(EmailConstant.UNLOAD_AREA_ID)));
        tReachManagement.setReachAreaName(provinceCityArea.get(EmailConstant.UNLOAD_AREA_NAME));
        tReachManagement.setReachTime(new Date());
        commonBiz.setBaseEntityAdd(tReachManagement, BaseContextHandler.getUserName());
        tReachManagementMapper.insertSelectiveEncrypt(tReachManagement);

        //保存触达附件
        if (ListUtils.isNotEmpty(requestModel.getTerminalTmpUrl())) {
            TReachAttachment tReachAttachment;
            List<TReachAttachment> tReachAttachmentList = new ArrayList<>();
            for (String picUrl : requestModel.getTerminalTmpUrl()) {
                tReachAttachment = new TReachAttachment();
                tReachAttachment.setReachManagementId(tReachManagement.getId());
                tReachAttachment.setAttachmentType(CommonConstant.INTEGER_TWO);
                tReachAttachment.setAttachmentPath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DRIVER_REACH_ATTACHMENT.getKey(), "", picUrl, null));
                commonBiz.setBaseEntityAdd(tReachAttachment, BaseContextHandler.getUserName());
                tReachAttachmentList.add(tReachAttachment);
            }
            tReachAttachmentMapper.batchInsert(tReachAttachmentList);
        }

        //异步修改偏差距离
        String destinationLocation = requestModel.getReachLongitude() + "," + requestModel.getReachLatitude();
        AsyncProcessQueue.execute(() -> reachManagementBiz.updateDistanceDeviation(tReachManagement.getId(), tCarrierOrder.getId(), destinationLocation));
    }

    /**
     * @param requestModel
     * @param tStaffBasic
     */
    private void checkArrivePickUpParams(ArrivePickUpV2RequestModel requestModel, TStaffBasic tStaffBasic) {
        //根据司机机构校验入参
        if (StaffPropertyEnum.OWN_STAFF.getKey().equals(tStaffBasic.getStaffProperty())
                || StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(tStaffBasic.getStaffProperty())) {
            //自主,自营司机
            String result = requestModel.checkNoExternalRule();
            if (StringUtils.isNotBlank(result)) {
                throw new BizException(30020, result);
            }
        }
    }

    /**
     * 到达提货地 公共方法-小程序
     *
     * @param requestModel
     * @param tCarrierOrder
     */
    @Transactional
    public void appletArrivePickUp(CarrierOrderArriveLoadUnloadRequestModel requestModel, TCarrierOrder tCarrierOrder) {
        //修改运单状态
        TCarrierOrder carrierOrder = new TCarrierOrder();
        carrierOrder.setId(tCarrierOrder.getId());
        carrierOrder.setStatus(CarrierOrderStatusEnum.WAIT_LOAD.getKey());
        carrierOrder.setStatusUpdateTime(new Date());
        commonBiz.setBaseEntityModify(carrierOrder, BaseContextHandler.getUserName());
        carrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(carrierOrder);

        //新增单据
        int ticketCount = 0;
        if (ListUtils.isNotEmpty(requestModel.getTmpUrl())) {
            ticketCount = requestModel.getTmpUrl().size();
            List<GetTicketsResponseModel> existTicketsCount = carrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(requestModel.getCarrierOrderId(), CarrierOrderTicketsTypeEnum.CARRIER_ORDER_ARRIVE_PICK_UP.getKey());
            if (ticketCount + existTicketsCount.size() > CommonConstant.INTEGER_SIX) {
                throw new BizException(CarrierDataExceptionEnum.PICTURES_OVER_COUNT);
            }
            addCarrierOrderTickets(requestModel.getTmpUrl(), tCarrierOrder, CopyFileTypeEnum.CARRIER_ORDER_ARRIVE_PICK_UP, CarrierOrderTicketsTypeEnum.CARRIER_ORDER_ARRIVE_PICK_UP);
        }

        //同步托盘运单
        List<CarrierOrderSynchronizeModel> synchronizeModels = new ArrayList<>();//云仓
        List<CarrierOrderSynchronizeModel> synchronizeToLeYiModels = new ArrayList<>();//云盘
        if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(tCarrierOrder.getDemandOrderSource())) {
            CarrierOrderSynchronizeModel syncModel = new CarrierOrderSynchronizeModel();
            syncModel.setType(tCarrierOrder.getDemandOrderEntrustType());
            syncModel.setCarrierOrderCode(tCarrierOrder.getCarrierOrderCode());
            syncModel.setStatus(CarrierOrderStatusEnum.WAIT_LOAD.getKey());
            syncModel.setUserName(BaseContextHandler.getUserName());
            synchronizeModels.add(syncModel);

            //云盘回收入库、回收出库、预约类型
            if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())
                    || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())
                    || EntrustTypeEnum.BOOKING.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())) {
                synchronizeToLeYiModels.add(syncModel);
            }
        }

        //记录到达提货地事件
        addCarrierOrderEvents(requestModel.getCarrierOrderId(), CarrierOrderEventsTypeEnum.ARRIVED_PICK_UP, ticketCount, null);
        //记录到达提货地日志
        addCarrierOrderOperateLogs(requestModel.getCarrierOrderId(), CarrierOrderOperateLogsTypeEnum.ARRIVED_PICK_UP, null);


        //异步方法放在最后执行
        //新增/修改运单上的定位信息
        AsyncProcessQueue.execute(() -> addOrModifyOrderLocation(requestModel,CarrierOrderLocationTypeEnum.LOAD.getKey()));

        //异步方法放在最后执行
        //节点信息推送微信公众号
        AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.singleWxPush(carrierOrder));

        // 发布智能推送运单到达提货地节点事件
        applicationContext.publishEvent(new WorkGroupEventModel()
                .setWorkGroupPushBoModels(tCarrierOrder.getId(),
                        tCarrierOrder.getDemandOrderSource(),
                        tCarrierOrder.getDemandOrderEntrustType(),
                        tCarrierOrder.getProjectLabel(),
                        WorkGroupOrderTypeEnum.CARRIER_ORDER,
                        WorkGroupOrderNodeEnum.CARRIER_ORDER_REACH_LOAD_ADDRESS));

        //同步云仓
        if (ListUtils.isNotEmpty(synchronizeModels)) {
            log.info("小程序操作到达提货地-同步托盘运单状态信息:" + synchronizeModels);
            CarrierOrderSynchronizeLeyiModel carrierOrderSynchronizeLeyiModel = new CarrierOrderSynchronizeLeyiModel();
            carrierOrderSynchronizeLeyiModel.setCarrierOrderSynchronizeModels(synchronizeModels);
            rabbitMqPublishBiz.carrierOrderSynchronizeToWarehouse(carrierOrderSynchronizeLeyiModel);
        }

        //同步云盘
        if (ListUtils.isNotEmpty(synchronizeToLeYiModels)) {
            log.info("小程序操作到达提货地-同步云盘运单状态信息:" + synchronizeToLeYiModels);
            CarrierOrderSynchronizeLeyiModel carrierOrderSynchronizeLeyiModel = new CarrierOrderSynchronizeLeyiModel();
            carrierOrderSynchronizeLeyiModel.setCarrierOrderSynchronizeModels(synchronizeToLeYiModels);
            rabbitMqPublishBiz.carrierOrderSynchronizeToLeyi(carrierOrderSynchronizeLeyiModel);
        }
    }

    /**
     * 到达卸货地
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public void arriveUnloading(CarrierOrderArriveLoadUnloadRequestModel requestModel) {
        TCarrierOrder tCarrierOrder = this.checkCarrierDriverIfEnabled(requestModel.getCarrierOrderId(),
                CarrierOrderEventsTypeEnum.ARRIVED_UNLOAD.getKey());

        //修改运单状态
        TCarrierOrder carrierOrder = new TCarrierOrder();
        carrierOrder.setId(tCarrierOrder.getId());
        carrierOrder.setStatus(CarrierOrderStatusEnum.WAIT_UNLOAD.getKey());
        carrierOrder.setStatusUpdateTime(new Date());
        commonBiz.setBaseEntityModify(carrierOrder, BaseContextHandler.getUserName());
        carrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(carrierOrder);


        //达卸货地操作同步运单
        List<CarrierOrderSynchronizeModel> synchronizeModels = new ArrayList<>();//云仓
        List<CarrierOrderSynchronizeModel> synchronizeToLeYiModels = new ArrayList<>();//云盘
        if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(tCarrierOrder.getDemandOrderSource())) {
            CarrierOrderSynchronizeModel syncModel = new CarrierOrderSynchronizeModel();
            syncModel.setType(tCarrierOrder.getDemandOrderEntrustType());
            syncModel.setCarrierOrderCode(tCarrierOrder.getCarrierOrderCode());
            syncModel.setStatus(CarrierOrderStatusEnum.WAIT_UNLOAD.getKey());
            syncModel.setUserName(BaseContextHandler.getUserName());
            synchronizeModels.add(syncModel);

            //云盘回收入库、回收出库、预约单子
            if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())
                    || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())
                    || EntrustTypeEnum.BOOKING.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())) {
                synchronizeToLeYiModels.add(syncModel);
            }
        }

        //新增单据
        int ticketCount = 0;
        if (ListUtils.isNotEmpty(requestModel.getTmpUrl())){
            ticketCount = requestModel.getTmpUrl().size();
            List<GetTicketsResponseModel> existTicketsCount = carrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(requestModel.getCarrierOrderId(), CarrierOrderTicketsTypeEnum.CARRIER_ORDER_ARRIVE_UNLOADING.getKey());
            if (ticketCount + existTicketsCount.size() > CommonConstant.INTEGER_SIX) {
                throw new BizException(CarrierDataExceptionEnum.PICTURES_OVER_COUNT);
            }
            addCarrierOrderTickets(requestModel.getTmpUrl(),tCarrierOrder,CopyFileTypeEnum.CARRIER_ORDER_ARRIVE_UNLOADING,CarrierOrderTicketsTypeEnum.CARRIER_ORDER_ARRIVE_UNLOADING);
        }

        //记录到达卸货地事件
        addCarrierOrderEvents(requestModel.getCarrierOrderId(),CarrierOrderEventsTypeEnum.ARRIVED_UNLOAD,ticketCount,null);

        //记录到达卸货地日志
        addCarrierOrderOperateLogs(requestModel.getCarrierOrderId(),CarrierOrderOperateLogsTypeEnum.ARRIVED_UNLOAD,null);


        //异步方法放在最后执行
        //新增/修改运单上的定位信息
        AsyncProcessQueue.execute(() -> addOrModifyOrderLocation(requestModel,CarrierOrderLocationTypeEnum.UNLOAD.getKey()));

        //异步方法放在最后执行
        //节点信息推送微信公众号
        AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.singleWxPush(carrierOrder));

        // 发布智能推送运单到达卸货地节点事件
        applicationContext.publishEvent(new WorkGroupEventModel()
                .setWorkGroupPushBoModels(tCarrierOrder.getId(),
                        tCarrierOrder.getDemandOrderSource(),
                        tCarrierOrder.getDemandOrderEntrustType(),
                        tCarrierOrder.getProjectLabel(),
                        WorkGroupOrderTypeEnum.CARRIER_ORDER,
                        WorkGroupOrderNodeEnum.CARRIER_ORDER_REACH_UNLOAD_ADDRESS));

        //同步云仓
        if(ListUtils.isNotEmpty(synchronizeModels)) {
            log.info("小程序操作到达卸货地-同步托盘运单状态信息:"+ synchronizeModels);
            CarrierOrderSynchronizeLeyiModel carrierOrderSynchronizeLeyiModel = new CarrierOrderSynchronizeLeyiModel();
            carrierOrderSynchronizeLeyiModel.setCarrierOrderSynchronizeModels(synchronizeModels);
            rabbitMqPublishBiz.carrierOrderSynchronizeToWarehouse(carrierOrderSynchronizeLeyiModel);
        }

        //同步云盘
        if(ListUtils.isNotEmpty(synchronizeToLeYiModels)) {
            log.info("小程序操作到达卸货地-同步云盘运单状态信息:"+ synchronizeToLeYiModels);
            CarrierOrderSynchronizeLeyiModel carrierOrderSynchronizeLeyiModel = new CarrierOrderSynchronizeLeyiModel();
            carrierOrderSynchronizeLeyiModel.setCarrierOrderSynchronizeModels(synchronizeToLeYiModels);
            rabbitMqPublishBiz.carrierOrderSynchronizeToLeyi(carrierOrderSynchronizeLeyiModel);
        }
    }

    //新增/修改运单上的定位信息
    public void addOrModifyOrderLocation(CarrierOrderArriveLoadUnloadRequestModel requestModel, Integer locationType){
        //经纬度信息不存在
        if (StringUtils.isBlank(requestModel.getLongitude()) || StringUtils.isBlank(requestModel.getLatitude())){
            return;
        }

        //根据经纬度查询地址
        String lonLatSplicing = String.join(",", requestModel.getLongitude(), requestModel.getLatitude());
        String location = commonBiz.getAddressByLonAndLat(lonLatSplicing);
        if (StringUtils.isBlank(location)){
            return;
        }

        //查询运单定位信息
        TCarrierOrderLocation dbCarrierOrderLocation = tCarrierOrderLocationMapper.getByCarrierOrderIdType(requestModel.getCarrierOrderId(), locationType);
        //不存在-新增
        TCarrierOrderLocation carrierOrderLocation = new TCarrierOrderLocation();
        if (dbCarrierOrderLocation == null){
            //新增
            carrierOrderLocation.setCarrierOrderId(requestModel.getCarrierOrderId());
            carrierOrderLocation.setLocationType(locationType);
            carrierOrderLocation.setLocation(location);
            carrierOrderLocation.setLongitude(requestModel.getLongitude());
            carrierOrderLocation.setLatitude(requestModel.getLatitude());
            commonBiz.setBaseEntityAdd(carrierOrderLocation,BaseContextHandler.getUserName());
            tCarrierOrderLocationMapper.insertSelective(carrierOrderLocation);
        }else{//存在-修改
            //修改
            carrierOrderLocation.setId(dbCarrierOrderLocation.getId());
            carrierOrderLocation.setLocation(location);
            carrierOrderLocation.setLongitude(requestModel.getLongitude());
            carrierOrderLocation.setLatitude(requestModel.getLatitude());
            commonBiz.setBaseEntityModify(carrierOrderLocation,BaseContextHandler.getUserName());
            tCarrierOrderLocationMapper.updateByPrimaryKeySelective(carrierOrderLocation);
        }
    }

    /**
     * 各个节点新增单据
     * @param imaUrl 图片临时路径
     * @param tCarrierOrder 运单信息
     * @param typeEnum 票据枚举
     */
    public List<String> addCarrierOrderTickets(List<String> imaUrl, TCarrierOrder tCarrierOrder, CopyFileTypeEnum typeEnum, CarrierOrderTicketsTypeEnum ticketsTypeEnum){
        Date now = new Date();
        TCarrierOrderTickets carrierOrderTickets;
        List<TCarrierOrderTickets> ticketsList = new ArrayList<>();
        List<String> imgPathList=new ArrayList<>();
        for (String path : imaUrl) {
            carrierOrderTickets = new TCarrierOrderTickets();
            carrierOrderTickets.setCarrierOrderId(tCarrierOrder.getId());
            carrierOrderTickets.setImageType(ticketsTypeEnum.getKey());
            carrierOrderTickets.setImageName(ticketsTypeEnum.getValue());
            carrierOrderTickets.setImagePath(commonBiz.copyFileToDirectoryOfType(typeEnum.getKey(), tCarrierOrder.getCarrierOrderCode(), path, null));
            carrierOrderTickets.setUploadUserName(BaseContextHandler.getUserName());
            carrierOrderTickets.setUploadTime(now);
            commonBiz.setBaseEntityAdd(carrierOrderTickets,BaseContextHandler.getUserName());
            ticketsList.add(carrierOrderTickets);
            imgPathList.add(carrierOrderTickets.getImagePath());
        }
        if (ListUtils.isNotEmpty(ticketsList)){
            carrierOrderTicketsMapper.batchInsertTickets(ticketsList);
        }
        return imgPathList;
    }

    /**
     * 各个节点记录事件
     * @param carrierOrderId 运单id
     * @param typeEnum 运单事件枚举
     * @param remark 备注
     */
    public void addCarrierOrderEvents(Long carrierOrderId, CarrierOrderEventsTypeEnum typeEnum, Integer ticketCount, String remark){
        Date now = new Date();
        TCarrierOrderEvents carrierOrderEvents = new TCarrierOrderEvents();
        carrierOrderEvents.setCarrierOrderId(carrierOrderId);
        carrierOrderEvents.setEvent(typeEnum.getKey());
        carrierOrderEvents.setEventDesc(typeEnum.getValue());
        if (StringUtils.isNotBlank(remark)) {
            carrierOrderEvents.setRemark(typeEnum.format(remark));
        }else{
            carrierOrderEvents.setRemark(typeEnum.getFormat());
        }
        carrierOrderEvents.setEventTime(now);
        carrierOrderEvents.setTicketsCount(ticketCount);
        carrierOrderEvents.setOperatorName(BaseContextHandler.getUserName());
        carrierOrderEvents.setOperateTime(now);
        commonBiz.setBaseEntityAdd(carrierOrderEvents,BaseContextHandler.getUserName());
        carrierOrderEventsMapper.insertSelective(carrierOrderEvents);
    }

    /**
     * 各个节点记录日志
     * @param carrierOrderId 运单id
     * @param typeEnum 运单日志类型枚举
     * @param remark 备注
     */
    public void addCarrierOrderOperateLogs(Long carrierOrderId, CarrierOrderOperateLogsTypeEnum typeEnum, String remark){
        Date now = new Date();
        TCarrierOrderOperateLogs carrierOrderOperateLogs = new TCarrierOrderOperateLogs();
        carrierOrderOperateLogs.setCarrierOrderId(carrierOrderId);
        carrierOrderOperateLogs.setOperationType(typeEnum.getKey());
        carrierOrderOperateLogs.setOperationContent(typeEnum.getValue());
        if (StringUtils.isNotBlank(remark)) {
            carrierOrderOperateLogs.setRemark(typeEnum.format(remark));
        }
        carrierOrderOperateLogs.setOperatorName(BaseContextHandler.getUserName());
        carrierOrderOperateLogs.setOperateTime(now);
        commonBiz.setBaseEntityAdd(carrierOrderOperateLogs,BaseContextHandler.getUserName());
        carrierOrderOperateLogsMapper.insertSelective(carrierOrderOperateLogs);
    }


    /**
     * 提卸货前校验
     *
     * @param goodsList
     */
    private void verifyBeforeLoadUnload(List<CarrierOrderGoodsLoadUnloadRequestModel> goodsList, Integer goodsUnit) {
        //入参校验
        if (ListUtils.isEmpty(goodsList)){
            throw new BizException(CarrierDataExceptionEnum.GOODS_LIST_EMPTY);
        }
        //不同的单位对数量最小值最大值校验
        if (GoodsUnitEnum.BY_WEIGHT.getKey().equals(goodsUnit)){
            for (CarrierOrderGoodsLoadUnloadRequestModel item : goodsList) {
                if (!RegExpValidatorUtil.isFloatNumber(ConverterUtils.toString(item.getCount()))
                        || item.getCount().compareTo(BigDecimal.ZERO) < CommonConstant.INTEGER_ZERO
                        || item.getCount().compareTo(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_THOUSAND) > CommonConstant.INTEGER_ZERO){
                    throw new BizException(CarrierDataExceptionEnum.TOTAL_EXPECT_AMOUNT_NOT_WEIGHT);
                }
            }
        }else{
            for (CarrierOrderGoodsLoadUnloadRequestModel item : goodsList) {
                if (!RegExpValidatorUtil.isNumber(ConverterUtils.toString(item.getCount()))
                        || item.getCount().compareTo(BigDecimal.ZERO) < CommonConstant.INTEGER_ZERO
                        || item.getCount().compareTo(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_THOUSAND) > CommonConstant.INTEGER_ZERO){
                    throw new BizException(CarrierDataExceptionEnum.TOTAL_EXPECT_AMOUNT_NOT_NUMBER);
                }
            }
        }
    }

    /**
     * 判断运单状态
     * @param carrierOrderId
     * @param eventsType if null，不校验运单任何状态。{@link CarrierOrderEventsTypeEnum#getKey()}
     * @return
     */
    public TCarrierOrder checkCarrierDriverIfEnabled(Long carrierOrderId, Integer eventsType){
        TCarrierOrder tCarrierOrder = checkCarrierOrder(carrierOrderId, eventsType);
        // 根据运单校验司机信息
        carrierOrderCommonBiz.checkDriver4Node(carrierOrderId, tCarrierOrder.getCompanyCarrierId());
        return tCarrierOrder;
    }

    /**
     * 校验运单
     *
     * @param carrierOrderId
     * @param eventsType if null，不校验运单任何状态。{@link CarrierOrderEventsTypeEnum#getKey()}
     * @return
     */
    private TCarrierOrder checkCarrierOrder(Long carrierOrderId, Integer eventsType) {
        TCarrierOrder tCarrierOrder = carrierOrderMapper.selectByPrimaryKeyDecrypt(carrierOrderId);
        if (tCarrierOrder == null || IfValidEnum.INVALID.getKey().equals(tCarrierOrder.getValid())){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //已取消不能操作
        if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfCancel())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL);
        }
        //运单放空不能操作
        if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfEmpty())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EMPTY);
        }
        if (CarrierOrderEventsTypeEnum.PICK_UP.getKey().equals(eventsType)) {
            //不是待提货状态不能操作
            if (!CarrierOrderStatusEnum.WAIT_LOAD.getKey().equals(tCarrierOrder.getStatus())){
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_ERROR);
            }
        } else if (CarrierOrderEventsTypeEnum.UNLOADING.getKey().equals(eventsType)) {
            //不是待卸货状态不能操作
            if (!CarrierOrderStatusEnum.WAIT_UNLOAD.getKey().equals(tCarrierOrder.getStatus())){
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_ERROR);
            }
            if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfExtCarrierOrder())){
                throw new BizException(EntrustDataExceptionEnum.SYSTEM_NOT_SUPPORT);
            }
        } else if (CarrierOrderEventsTypeEnum.ARRIVED_PICK_UP.getKey().equals(eventsType)) {
            //不是待到达提货地状态不能操作
            if (!tCarrierOrder.getStatus().equals(CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey())){
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_ERROR);
            }
        } else if (CarrierOrderEventsTypeEnum.ARRIVED_UNLOAD.getKey().equals(eventsType)) {
            //不是待到达卸货地状态不能操作
            if (!tCarrierOrder.getStatus().equals(CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey())){
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_ERROR);
            }
            if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfExtCarrierOrder())) {
                throw new BizException(EntrustDataExceptionEnum.SYSTEM_NOT_SUPPORT);
            }
        }
        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(tCarrierOrder.getId().toString());
        if (!workOrderMap.isEmpty()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }
        return tCarrierOrder;
    }

    /**
     * 根据运单号查询运单id
     *
     * @param requestModel 运单号
     * @return 运单id
     */
    public QueryIdByCarrierOrderCodeResponseModel queryIdByCarrierOrderCode(QueryIdByCarrierOrderCodeRequestModel requestModel) {
        Long driverId = commonBiz.getLoginDriverAppletUserId();
        //根据运单号查询运单信息
        Long carrierOrderId = carrierOrderMapper.selectCarrierOrderIdByCode(requestModel.getCarrierOrderCode(), driverId);
        if (carrierOrderId == null || carrierOrderId <= CommonConstant.LONG_ZERO) {
            return new QueryIdByCarrierOrderCodeResponseModel();
        }
        //返回运单Id
        QueryIdByCarrierOrderCodeResponseModel responseModel = new QueryIdByCarrierOrderCodeResponseModel();
        responseModel.setCarrierOderId(carrierOrderId);
        return responseModel;
    }

    /**
     * 司机打印详情
     * @param requestDto
     * @return
     */
    public PrintBillDetailResponseModel printBillDetail(CarrierOrderIdRequestModel requestDto) {
        PrintBillDetailResponseModel detail = carrierOrderMapper.printBillDetail(requestDto.getCarrierOrderId());
        if (detail != null){
            //二维码参数
            Map<String, Object> qrCodeParamsMap = DriverAppletQrCodeJumpPageEnum.CARRIER_ORDER_DETAIL_PAGE.getParamsMap();
            qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_CODE.getKey(), detail.getCarrierOrderCode());
            qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_BILL_TYPE.getKey(), QrCodeParamsBillTypeEnum.SMALL.getKey());
            detail.setQrCodeContent(commonBiz.getQrCodeContent(configKeyConstant.qrcodeCommonPrefix, qrCodeParamsMap));
        }
        return detail;
    }

    /**
     * (云盘) 提货确认接口
     *
     * @param requestModel 运单Id
     * @return 提货数
     */
    public LeyiPickupConfirmResponseModel pickupConfirm(PickupConfirmRequestModel requestModel) {
        // 查询运单号
        String carrierOrderCode =
                Optional.ofNullable(carrierOrderMapper.selectByPrimaryKey(requestModel.getCarrierOrderId()))
                        .filter(f -> IfValidEnum.VALID.getKey().equals(f.getValid()))
                        .map(TCarrierOrder::getCarrierOrderCode)
                        .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST));

        // 查询云盘提货数及状态\根据提货方式校验状态
        return pickupConfirm(carrierOrderCode, CarrierOrderDeliverMethodEnum.getEnumByKey(requestModel.getDeliveryMethod()));
    }

    private LeyiPickupConfirmResponseModel pickupConfirm(String carrierOrderCode,
                                                         CarrierOrderDeliverMethodEnum deliverMethodEnum) {
        LeyiPickupConfirmResponseModel confirm = new LeyiPickupConfirmResponseModel()
                .setConfirmStatus(CommonConstant.INTEGER_ZERO);
        if (StringUtils.isNotBlank(carrierOrderCode)) {
            // 查询云盘提货数及状态
            Optional<GetCarrierOrderDetailResponseModel> pickupConfirmOpt =
                    customerInOrderServiceApi.getCarrierOrderDetail(carrierOrderCode);
            // 二维码提货确认状态 -> 待确认
            if (CarrierOrderDeliverMethodEnum.QRCODE_PICKUP.equals(deliverMethodEnum)) {
                pickupConfirmOpt
                        .filter(f -> CommonConstant.INTEGER_ONE.equals(f.getCustomerOutState()))
                        .ifPresent(f -> confirm.setConfirmStatus(CommonConstant.INTEGER_ONE)
                                .setLoadingCount(f.getLoadingCount()));
            }
            // 其他类型提货 -> 非已驳回
            else {
                if (pickupConfirmOpt
                        .filter(f -> CommonConstant.INTEGER_THREE.equals(f.getCustomerOutState()))
                        .isEmpty()) {
                    confirm.setConfirmStatus(CommonConstant.INTEGER_ONE);
                }
            }
        }
        return confirm;
    }

    /**
     * 司机申请修改提货数量 驳回
     *
     * @param requestModel 运单id
     */
    public void denyLoadAmount(CarrierOrderIdRequestModel requestModel) {
        //查询运单是否存在
        TCarrierOrder tCarrierOrder = carrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (tCarrierOrder == null || IfValidEnum.INVALID.getKey().equals(tCarrierOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //校验运单上司机信息与登录人信息是否一致
        carrierOrderCommonBiz.checkDriver(tCarrierOrder.getId(), tCarrierOrder.getCompanyCarrierId());

        //驳回状态同步云盘
        trayOrderServiceClient.rejectOrderLoadAmount(new RejectOrderLoadAmountRequestModel()
                .setCarrierOrderCode(tCarrierOrder.getCarrierOrderCode())
                .setOperator(BaseContextHandler.getUserName()));
    }
}
