package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class LoadRequestDto {

    @NotBlank(message = "运单id不能为空")
    @ApiModelProperty(value = "运单ID")
    private String carrierOrderId;

    @ApiModelProperty("货物单位：1 件，2 吨")
    private String goodsUnit;

    @Size(max = 100, message = "备注内容0-100字")
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("提货时间")
    @NotBlank(message = "提货时间不能为空")
    private String loadTime;

    @ApiModelProperty("票据临时路径")
    private String tmpUrl;

    @NotEmpty
    @ApiModelProperty("货物信息")
    private List<CarrierOrderNodeGoodsRequestDto> goodsList;
}
