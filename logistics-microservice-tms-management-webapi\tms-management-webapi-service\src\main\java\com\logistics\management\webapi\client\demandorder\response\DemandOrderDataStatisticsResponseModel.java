package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/1/10 9:55
 */
@Data
public class DemandOrderDataStatisticsResponseModel {
    @ApiModelProperty("需求单id")
    private Long demandOrderId;
    @ApiModelProperty("需求单状态500待发布 1000待调度 2000部分调度 3000调度完成'")
    private Integer status;
    @ApiModelProperty("需求单货物数量")
    private BigDecimal goodsAmount;
}
