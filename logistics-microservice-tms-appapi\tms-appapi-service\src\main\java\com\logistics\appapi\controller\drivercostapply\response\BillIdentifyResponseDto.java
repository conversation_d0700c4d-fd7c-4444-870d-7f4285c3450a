package com.logistics.appapi.controller.drivercostapply.response;

import com.yelo.tools.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BillIdentifyResponseDto {

    @ApiModelProperty("图片全路径")
    private String src = "";

    @ApiModelProperty("图片相对路径")
    private String relativePath = "";

    @ApiModelProperty("1 增值税，2 出租车，3 火车票，4 定额，5 卷票，6 机打，7 过路")
    private String type = "";

    @ApiModelProperty("发票名称")
    private String invoiceName = "";

    @ApiModelProperty("票据类型")
    private String invoiceType = "";

    @ApiModelProperty("发票代码")
    private String invoiceCode = "";

    @ApiModelProperty("发票号码")
    private String invoiceNum = "";

    @ApiModelProperty("合计金额")
    private String totalPrice = "";

    @ApiModelProperty("合计税额")
    private String totalTax = "";

    @ApiModelProperty("价税合计")
    private String totalTaxAndPrice = "";

    public String getTotalPrice() {
        return StringUtils.isBlank(totalPrice) ? "0" : totalPrice;
    }

    public String getTotalTax() {
        return StringUtils.isBlank(totalTax) ? "0" : totalTax;
    }
}
