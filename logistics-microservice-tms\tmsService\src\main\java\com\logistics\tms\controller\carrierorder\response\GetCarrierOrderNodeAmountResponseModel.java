package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/27
 */
@Data
public class GetCarrierOrderNodeAmountResponseModel {
	@ApiModelProperty("运单id")
	private Long carrierOrderId;

	@ApiModelProperty("货物单位：1 件，2 吨，3 件（方），4 块")
	private Integer goodsUnit;

	private List<GetCarrierOrderNodeAmountGoodsModel> goodsList = new ArrayList<>();
}
