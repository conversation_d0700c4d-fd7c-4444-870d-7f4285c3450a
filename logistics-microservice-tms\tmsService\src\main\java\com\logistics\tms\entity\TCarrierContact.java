package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/11
*/
@Data
public class TCarrierContact extends BaseEntity {
    /**
    * 车主公司ID
    */
    @ApiModelProperty("车主公司ID")
    private Long companyCarrierId;

    /**
    * 客户联系人名称
    */
    @ApiModelProperty("客户联系人名称")
    private String contactName;

    /**
    * 联系方式（原长度50）
    */
    @ApiModelProperty("联系方式（原长度50）")
    private String contactPhone;

    /**
    * 身份证号（原长度50）
    */
    @ApiModelProperty("身份证号（原长度50）")
    private String identityNumber;

    /**
    * 身份证人面像
    */
    @ApiModelProperty("身份证人面像")
    private String identityFaceFile;

    /**
    * 身份证人面像图片是否后补 0否1是
    */
    @ApiModelProperty("身份证人面像图片是否后补 0否1是")
    private Integer identityFaceFileIsAmend;

    /**
    * 身份证国徽图片
    */
    @ApiModelProperty("身份证国徽图片")
    private String identityNationalFile;

    /**
    * 身份证国徽图片是否后补 0否1是
    */
    @ApiModelProperty("身份证国徽图片是否后补 0否1是")
    private Integer identityNationalFileIsAmend;

    /**
    * 身份证有效期
    */
    @ApiModelProperty("身份证有效期")
    private Date identityValidity;

    /**
    * 身份证是否永久: 0 否 1 是
    */
    @ApiModelProperty("身份证是否永久: 0 否 1 是")
    private Integer identityIsForever;

    /**
    * 省ID
    */
    @ApiModelProperty("省ID")
    private Long provinceId;

    /**
    * 省名字
    */
    @ApiModelProperty("省名字")
    private String provinceName;

    /**
    * 城市ID
    */
    @ApiModelProperty("城市ID")
    private Long cityId;

    /**
    * 城市名字
    */
    @ApiModelProperty("城市名字")
    private String cityName;

    /**
    * 区ID
    */
    @ApiModelProperty("区ID")
    private Long areaId;

    /**
    * 区名字
    */
    @ApiModelProperty("区名字")
    private String areaName;

    /**
    * 发证机关详情
    */
    @ApiModelProperty("发证机关详情")
    private String certificationDepartmentDetail;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 0禁用1启用
    */
    @ApiModelProperty("0禁用1启用")
    private Integer enabled;
}