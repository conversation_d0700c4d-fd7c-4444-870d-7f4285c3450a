package com.logistics.appapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum AuthModelEnum {

    DEFAULT(0, ""),
    MOBILE_AUTH(1, "手机号认证"),
    FACE_AUTH(2, "人脸识别"),
    ;

    private Integer key;

    private String value;

    public static AuthModelEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
