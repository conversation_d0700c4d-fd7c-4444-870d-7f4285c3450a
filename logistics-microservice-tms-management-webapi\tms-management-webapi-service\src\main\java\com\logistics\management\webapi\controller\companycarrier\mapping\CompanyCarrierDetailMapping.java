package com.logistics.management.webapi.controller.companycarrier.mapping;

import com.logistics.management.webapi.client.companycarrier.response.CompanyCarrierDetailResponseModel;
import com.logistics.management.webapi.controller.companycarrier.response.CompanyCarrierDetailResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/9/29 9:42
 */
public class CompanyCarrierDetailMapping extends MapperMapping<CompanyCarrierDetailResponseModel,CompanyCarrierDetailResponseDto> {
    
    private String imagePrefix;
    private Map<String, String> imageMap;
    public CompanyCarrierDetailMapping(String imagePrefix, Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        CompanyCarrierDetailResponseModel model = getSource();
        CompanyCarrierDetailResponseDto dto = getDestination();
        if (model != null) {
            //车主类型
            dto.setTypeLabel(CompanyTypeEnum.getEnum(model.getType()).getValue());
            if (model.getTradingCertificateValidityTime() != null) {
                dto.setTradingCertificateValidityTime(DateUtils.dateToString(model.getTradingCertificateValidityTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if (model.getRoadTransportCertificateValidityTime() != null) {
                dto.setRoadTransportCertificateValidityTime(DateUtils.dateToString(model.getRoadTransportCertificateValidityTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }

            if (model.getIdentityValidity() != null) {
                dto.setIdentityValidity(DateUtils.dateToString(model.getIdentityValidity(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }

            //身份证人面像
            if (StringUtils.isNotBlank(model.getIdentityFaceFile()) && CommonConstant.INTEGER_ZERO.equals(model.getIdentityFaceFileIsAmend())) {
                dto.setFileAboIdentityFaceFile(imagePrefix + imageMap.get(model.getIdentityFaceFile()));
                dto.setIdentityFaceFile(model.getIdentityFaceFile());
            } else {
                dto.setFileAboIdentityFaceFile("");
                dto.setIdentityFaceFile("");
            }

            //身份证国徽像
            if (StringUtils.isNotBlank(model.getIdentityNationalFile()) && CommonConstant.INTEGER_ZERO.equals(model.getIdentityNationalFileIsAmend())) {
                dto.setFileAboIdentityNationalFile(imagePrefix + imageMap.get(model.getIdentityNationalFile()));
                dto.setIdentityNationalFile(model.getIdentityNationalFile());
            } else {
                dto.setFileAboIdentityNationalFile("");
                dto.setIdentityNationalFile("");
            }

            //营业执照
            if (StringUtils.isNotBlank(model.getTradingCertificateImage()) && CommonConstant.INTEGER_ZERO.equals(model.getTradingCertificateIsAmend())) {
                dto.setFileTargetPathTradingCertificateImage(imagePrefix + imageMap.get(model.getTradingCertificateImage()));
                dto.setFileTradingCertificateImage(model.getTradingCertificateImage());
            } else {
                dto.setFileTargetPathTradingCertificateImage("");
                dto.setFileTradingCertificateImage("");
            }

            //道路运输许可证
            if (StringUtils.isNotBlank(model.getRoadTransportCertificateImage()) && CommonConstant.INTEGER_ZERO.equals(model.getRoadTransportCertificateIsAmend())) {
                dto.setFileTargetPathRoadTransportCertificateImage(imagePrefix + imageMap.get(model.getRoadTransportCertificateImage()));
                dto.setFileRoadTransportCertificateImage(model.getRoadTransportCertificateImage());
            } else {
                dto.setFileTargetPathRoadTransportCertificateImage("");
                dto.setFileRoadTransportCertificateImage("");
            }
        }
    }
}
