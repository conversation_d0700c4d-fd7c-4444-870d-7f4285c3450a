package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 *
 */
@Getter
@AllArgsConstructor
public enum WorkOrderProcessSolveLabelEnum {

    DEFAULT(-1, ""),
    BACK_STAGE(1, "工作人员"),
    WEB(2, "车主"),
    APPLET(3, "司机"),
    CENTER(4, "工作人员"),
    ;
    private final Integer key;
    private final String value;

    public static WorkOrderProcessSolveLabelEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
