package com.logistics.tms.client.feign.warehouse.stock.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class GetWareHouseByIdsAndKeyWordAndTypeRequestModel {

    @ApiModelProperty(value = "仓库ids")
    private List<Long> ids;

    @ApiModelProperty(value = "模糊搜索‘仓库中文名’,‘仓库英文名’,‘省份名字’,‘城市名字’,‘县区名字’,‘详细地址’")
    private String keyWord;

    @ApiModelProperty(value = "仓库类型")
    private Integer type;

    @ApiModelProperty(value = "是否启用,1启用,0禁用")
    private Integer enabled;

    @ApiModelProperty(value = "‘仓库中文名’,‘仓库英文名’")
    private String warehouseName;

    @ApiModelProperty(value = "仓库codes")
    private List<String> warehouseCodes;

    @ApiModelProperty(value = "模糊搜索‘仓库中文名’,‘仓库英文名’,‘联系人’,‘详细地址’")
    private String lifeKeyWord;

    @ApiModelProperty("仓库类型,1是自有,2是第三方,4虚拟,5其他、6 粉碎工厂")
    private List<Integer> types;



    @ApiModelProperty(value = "‘仓库中文名’,‘仓库英文名(用于精确搜索)")
    private String warehouseNameInAllMatch;
}
