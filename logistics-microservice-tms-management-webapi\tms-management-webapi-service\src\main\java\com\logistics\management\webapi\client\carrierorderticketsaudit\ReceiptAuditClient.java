package com.logistics.management.webapi.client.carrierorderticketsaudit;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.carrierorderticketsaudit.hystrix.ReceiptAuditClientHystrix;
import com.logistics.management.webapi.client.carrierorderticketsaudit.response.GetReceiptAuditDetailResponseModel;
import com.logistics.management.webapi.client.carrierorderticketsaudit.request.GetReceiptAuditDetailRequestModel;
import com.logistics.management.webapi.client.carrierorderticketsaudit.request.ReceiptAgainAuditRequestModel;
import com.logistics.management.webapi.client.carrierorderticketsaudit.request.ReceiptAuditRequestModel;
import com.logistics.management.webapi.client.carrierorderticketsaudit.request.SearchReceiptAuditListRequestModel;
import com.logistics.management.webapi.client.carrierorderticketsaudit.response.SearchReceiptAuditListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 * @date 2024/03/27
 */
@FeignClient(name = FeignClientName.TMS_SERVICES, fallback = ReceiptAuditClientHystrix.class)
public interface ReceiptAuditClient {

    @ApiOperation(value = "回单审核列表", tags = "1.2.9")
    @PostMapping("/service/receiptReviewManagement/searchList")
    Result<PageInfo<SearchReceiptAuditListResponseModel>> searchList(@RequestBody SearchReceiptAuditListRequestModel requestModel);

    @ApiOperation(value = "回单审核详情", tags = "1.2.9")
    @PostMapping("/service/receiptReviewManagement/getDetail")
    Result<GetReceiptAuditDetailResponseModel> getDetail(@RequestBody GetReceiptAuditDetailRequestModel requestModel);

    @ApiOperation(value = "回单审核", tags = "1.2.9")
    @PostMapping("/service/receiptReviewManagement/audit")
    Result<Boolean> audit(@RequestBody ReceiptAuditRequestModel requestModel);

    @ApiOperation(value = "回单重新审核", tags = "1.2.9")
    @PostMapping("/service/receiptReviewManagement/againAudit")
    Result<Boolean> againAudit(@RequestBody ReceiptAgainAuditRequestModel requestModel);
}
