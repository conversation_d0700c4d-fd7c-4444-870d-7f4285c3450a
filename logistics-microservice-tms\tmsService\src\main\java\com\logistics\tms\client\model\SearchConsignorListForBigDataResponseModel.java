package com.logistics.tms.client.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/9/1 16:18
 */
@Data
public class SearchConsignorListForBigDataResponseModel {
    @ApiModelProperty("乐橘新生客户名称")
    private String customerName;

    @ApiModelProperty("乐橘新生客户公司名称")
    private String customerCompanyName;

    @ApiModelProperty("乐橘新生客户类型: 1 个人，2 公司")
    private Integer customerType;

    @ApiModelProperty("发货地址code（新生客户地址code）")
    private String loadAddressCode;

    @ApiModelProperty("发货省id")
    private Long loadProvinceId;

    @ApiModelProperty("发货省")
    private String loadProvinceName;

    @ApiModelProperty("发货市id")
    private Long loadCityId;

    @ApiModelProperty("发货市")
    private String loadCityName;

    @ApiModelProperty("发货区ID")
    private Long loadAreaId;

    @ApiModelProperty("发货区")
    private String loadAreaName;

    @ApiModelProperty("发货详细地址")
    private String loadDetailAddress;

    @ApiModelProperty("发货人")
    private String consignorName;

    @ApiModelProperty("目标经度")
    private String longitude;

    @ApiModelProperty("目标纬度")
    private String latitude;

    @ApiModelProperty("距离(KM)")
    private String distance;
}
