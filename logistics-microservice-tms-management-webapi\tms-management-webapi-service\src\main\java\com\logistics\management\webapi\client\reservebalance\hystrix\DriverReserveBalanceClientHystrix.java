package com.logistics.management.webapi.client.reservebalance.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.reservebalance.DriverReserveBalanceClient;
import com.logistics.management.webapi.client.reservebalance.model.request.DriverReserveBalanceListRequestModel;
import com.logistics.management.webapi.client.reservebalance.model.request.ReserveBalanceDetailRequestModel;
import com.logistics.management.webapi.client.reservebalance.model.response.DriverReserveBalanceListResponseModel;
import com.logistics.management.webapi.client.reservebalance.model.response.ReserveBalanceDetailResponseModel;
import com.logistics.management.webapi.client.reservebalance.model.response.SearchDriverReserveBalanceResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class DriverReserveBalanceClientHystrix implements DriverReserveBalanceClient {

    @Override
    public Result<SearchDriverReserveBalanceResponseModel> searchList(DriverReserveBalanceListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<DriverReserveBalanceListResponseModel>> searchListExport(DriverReserveBalanceListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<ReserveBalanceDetailResponseModel>> reserveBalanceDetail(ReserveBalanceDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Map<String, List<ReserveBalanceDetailResponseModel>>> reserveBalanceDetailExport(ReserveBalanceDetailRequestModel requestModel) {
        return Result.timeout();
    }
}
