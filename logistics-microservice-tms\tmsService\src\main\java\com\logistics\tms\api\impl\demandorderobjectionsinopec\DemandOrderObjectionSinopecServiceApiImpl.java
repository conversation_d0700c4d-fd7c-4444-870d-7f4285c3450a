package com.logistics.tms.api.impl.demandorderobjectionsinopec;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.demandorderobjectionsinopec.DemandOrderObjectionSinopecServiceApi;
import com.logistics.tms.api.feign.demandorderobjectionsinopec.model.*;
import com.logistics.tms.biz.demandorderobjectionsinopec.DemandOrderObjectionSinopecBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: wjf
 * @date: 2022/5/30 15:27
 */
@RestController
public class DemandOrderObjectionSinopecServiceApiImpl implements DemandOrderObjectionSinopecServiceApi {

    @Autowired
    private DemandOrderObjectionSinopecBiz demandOrderObjectionSinopecBiz;

    /**
     * 中石化需求单异常列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<SearchDemandOrderObjectionSinopecResponseModel>> searchSinopecObjection(@RequestBody SearchDemandOrderObjectionSinopecRequestModel requestModel) {
        return Result.success(demandOrderObjectionSinopecBiz.searchSinopecObjection(requestModel));
    }

    /**
     * 中石化需求单异常详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<GetSinopecObjectionDetailResponseModel> getSinopecObjectionDetail(@RequestBody GetSinopecObjectionDetailRequestModel requestModel) {
        return Result.success(demandOrderObjectionSinopecBiz.getSinopecObjectionDetail(requestModel));
    }

    /**
     * 中石化需求单异常审核
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> sinopecObjectionAudit(@RequestBody SinopecObjectionAuditRequestModel requestModel) {
        demandOrderObjectionSinopecBiz.sinopecObjectionAudit(requestModel);
        return Result.success(true);
    }
}
