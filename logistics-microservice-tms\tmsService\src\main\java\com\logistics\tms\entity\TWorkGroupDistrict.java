package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2023/12/21
*/
@Data
public class TWorkGroupDistrict extends BaseEntity {
    /**
    * 智能推送配置表id
    */
    @ApiModelProperty("智能推送配置表id")
    private Long workGroupId;

    /**
    * 省份ID
    */
    @ApiModelProperty("省份ID")
    private Long provinceId;

    /**
    * 省份名字
    */
    @ApiModelProperty("省份名字")
    private String provinceName;

    /**
    * 城市ID
    */
    @ApiModelProperty("城市ID")
    private Long cityId;

    /**
    * 城市名字
    */
    @ApiModelProperty("城市名字")
    private String cityName;
}