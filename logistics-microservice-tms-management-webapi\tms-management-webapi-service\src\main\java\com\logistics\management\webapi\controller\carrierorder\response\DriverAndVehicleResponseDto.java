package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * liang current user system login name
 * 2018/9/26 current system date
 */
@Data
public class DriverAndVehicleResponseDto {

    @ApiModelProperty("运单ID")
    private String carrierOrderId = "";

    @ApiModelProperty("司机id")
    private String driverId = "";

    @ApiModelProperty("司机名字")
    private String driverName = "";

    @ApiModelProperty("司机手机号")
    private String driverPhone = "";

    @ApiModelProperty("司机身份证")
    private String driverIdentityNumber = "";

    @ApiModelProperty("车辆id")
    private String vehicleId = "";

    @ApiModelProperty("车牌号")
    private String vehicleNo = "";

    @ApiModelProperty("挂车车辆ID,1.4.0新增")
    private String trailerVehicleId;

    @ApiModelProperty("挂车车牌号,1.4.0新增")
    private String trailerVehicleNo;

    @ApiModelProperty("车辆机构 1 自有 2外部")
    private String vehicleProperty = "";

    @ApiModelProperty("承运商id")
    private String companyCarrierId = "";

    @ApiModelProperty("是否我司 1:我司,2:其他车主")
    private String isOurCompany = "";
}
