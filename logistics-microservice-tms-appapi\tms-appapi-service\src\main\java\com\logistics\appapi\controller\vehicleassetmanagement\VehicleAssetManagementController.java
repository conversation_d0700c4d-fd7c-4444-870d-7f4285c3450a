package com.logistics.appapi.controller.vehicleassetmanagement;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.AppApiExceptionEnum;
import com.logistics.appapi.client.vehicleassetmanagement.VehicleAssetManagementClient;
import com.logistics.appapi.client.vehicleassetmanagement.request.SearchVehicleByPropertyRequestModel;
import com.logistics.appapi.client.vehicleassetmanagement.response.SearchVehicleByPropertyResponseModel;
import com.logistics.appapi.controller.vehicleassetmanagement.request.SearchVehicleByPropertyRequestDto;
import com.logistics.appapi.controller.vehicleassetmanagement.response.SearchVehicleByPropertyResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2024/3/11 14:32
 */
@Api(value = "车辆管理", tags = "车辆管理")
@RestController
@RequestMapping(value = "/api/vehicleAssetManagement")
public class VehicleAssetManagementController {

    @Resource
    private VehicleAssetManagementClient vehicleAssetManagementClient;

    /**
     * 根据车牌号模糊搜索、车辆机构查询车辆信息（分页）
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "根据车牌号模糊搜索、车辆机构查询车辆信息（分页）")
    @PostMapping(value = "/searchVehicleByProperty")
    public Result<PageInfo<SearchVehicleByPropertyResponseDto>> searchVehicleByProperty(@RequestBody @Valid SearchVehicleByPropertyRequestDto requestDto) {
        SearchVehicleByPropertyRequestModel requestModel = MapperUtils.mapper(requestDto, SearchVehicleByPropertyRequestModel.class);
        //处理车辆机构参数
        try {
            requestModel.setVehicleProperty(Arrays.stream(requestDto.getVehicleProperty().split(CommonConstant.COMMA)).map(Integer::parseInt).collect(Collectors.toList()));
        } catch (NumberFormatException e) {
            throw new BizException(AppApiExceptionEnum.PARAMS_ERROR);
        }
        Result<PageInfo<SearchVehicleByPropertyResponseModel>> result = vehicleAssetManagementClient.searchVehicleByProperty(requestModel);
        result.throwException();
        PageInfo pageInfo = result.getData();
        pageInfo.setList(MapperUtils.mapper(result.getData().getList(), SearchVehicleByPropertyResponseDto.class));
        return Result.success(pageInfo);
    }
}
