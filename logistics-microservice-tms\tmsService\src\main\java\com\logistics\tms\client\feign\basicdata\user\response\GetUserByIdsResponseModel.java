package com.logistics.tms.client.feign.basicdata.user.response;

import lombok.Data;

import java.util.Date;

@Data
public class GetUserByIdsResponseModel {

    private Long id;

    private String userName;

    private String userAccount;

    private String password;

    private String mobilePhone;

    private String telephone;

    private Integer enabled;

    private String createdBy;

    private Date createdTime;

    private String lastModifiedBy;

    private Date lastModifiedTime;

    private Integer valid;

    private String remark;

    private String wechatUserid;
}
