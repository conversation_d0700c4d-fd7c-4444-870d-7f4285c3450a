package com.logistics.management.webapi.client.carrierorder.request;

import com.logistics.management.webapi.controller.carrierorder.request.LoadGoodsForYeloLifeRequestCodeDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/24
 */
@Data
public class SignGoodsForYeloLifeRequestModel {

	@ApiModelProperty(value = "货物id")
	private Long goodsId;

	@ApiModelProperty(value = "签收数量 0<货物数量<=实卸数量")
	private BigDecimal signAmount;

	@ApiModelProperty(value = "货物的编码集合 v2.44", required = true)
	private List<CarrierOrderSignUpForYeloLifeCodeModel> codeDtoList;
}
