package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2021/9/27 15:23
 */
@Data
public class BatchPublishDetailResponseModel {

    @ApiModelProperty("货主公司名称")
    private String companyEntrustName;
    @ApiModelProperty("需求单ID")
    private Long demandId;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;
    @ApiModelProperty("委托数量")
    private BigDecimal goodsAmount;

    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;

    private Long demandOrderAddressId;
    @ApiModelProperty("发货地址")
    private Long loadProvinceId;
    private String loadProvinceName;
    private Long loadCityId;
    private String loadCityName;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    @ApiModelProperty("收货地址")
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;

    private Integer entrustStatus;//需求单状态
    private Integer ifCancel;//是否取消
    private Integer ifEmpty;//是否放空
    private Integer ifRollback;//是否回退
    /**
     * 是否后补需求单 0.不是 1.是
     */
    private Integer ifeExtDemandOrder;

    @ApiModelProperty("是否修改卸货地址,非数据库字段")
    private boolean changeUnloadWarehouse;


    private Integer demandOrderSource;//委托单来源：1 乐医托盘委托单，2 委托方发单，3 中石化系统，4 扬巴邮件，5 乐橘新生
}
