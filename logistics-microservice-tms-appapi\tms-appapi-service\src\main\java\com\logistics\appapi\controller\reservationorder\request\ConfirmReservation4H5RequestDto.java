package com.logistics.appapi.controller.reservationorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/14 14:48
 */
@Data
public class ConfirmReservation4H5RequestDto {

    /**
     * 预约类型：1 提货，2 卸货
     */
    @ApiModelProperty("预约类型：1 提货，2 卸货")
    @NotBlank(message = "预约类型不能为空")
    @Pattern(regexp = "^[12]$", message = "预约类型不能为空")
    private String reservationType;

    /**
     * 运单id
     */
    @ApiModelProperty("运单id")
    @NotEmpty(message = "运单id不能为空")
    private List<String> carrierOrderIdList;

    /**
     * 预约时间段类型：1 今天，2 明天
     */
    @ApiModelProperty("预约时间段类型：1 今天，2 明天")
    @NotBlank(message = "预约时间段类型不能为空")
    @Pattern(regexp = "^[12]$", message = "预约时间段类型不能为空")
    private String reservationTimeType;

    /**
     * 预约时间段
     */
    @ApiModelProperty("预约时间段")
    @NotBlank(message = "请选择预约时间段")
    private String reservationTime;


    /**
     * H5验证过的手机号
     */
    @ApiModelProperty("H5验证过的手机号")
    private String mobilePhone;


    /**
     * 预计入库数
     */
    @ApiModelProperty("预计入库数")
    private String expectedStockIn;


}
