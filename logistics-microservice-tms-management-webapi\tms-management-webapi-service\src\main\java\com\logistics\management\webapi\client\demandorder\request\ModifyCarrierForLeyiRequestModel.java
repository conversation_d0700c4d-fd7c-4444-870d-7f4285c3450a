package com.logistics.management.webapi.client.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/10/10
 */
@Data
public class ModifyCarrierForLeyiRequestModel {

	@ApiModelProperty(value = "需求单ID")
	private List<Long> demandOrderIds;

	@ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
	private Integer isOurCompany;

	@ApiModelProperty(value = "车主ID")
	private Long companyCarrierId;

	@ApiModelProperty(value = "修改原因")
	private String modifyReason;
}
