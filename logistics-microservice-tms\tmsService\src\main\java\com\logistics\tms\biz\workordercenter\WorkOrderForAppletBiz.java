package com.logistics.tms.biz.workordercenter;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.controller.workordercenter.request.WorkOrderDetailAppletRequestModel;
import com.logistics.tms.controller.workordercenter.request.WorkOrderListAppletRequestModel;
import com.logistics.tms.controller.workordercenter.response.WorkOrderDetailAppletResponseModel;
import com.logistics.tms.controller.workordercenter.response.WorkOrderListAppletResponseModel;
import com.logistics.tms.controller.workordercenter.response.WorkOrderProcessAppletResponseModel;
import com.logistics.tms.controller.workordercenter.response.WorkOrderProcessResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.client.WorkOrderCenterClient;
import com.logistics.tms.entity.TCarrierOrderAddress;
import com.logistics.tms.entity.TWorkOrder;
import com.logistics.tms.mapper.TCarrierOrderAddressMapper;
import com.logistics.tms.mapper.TWorkOrderMapper;
import com.logistics.tms.mapper.TWorkOrderProcessMapper;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2023/4/17 9:44
 */
@Service
public class WorkOrderForAppletBiz {

    @Autowired
    private TWorkOrderMapper tWorkOrderMapper;
    @Autowired
    private TWorkOrderProcessMapper tWorkOrderProcessMapper;
    @Autowired
    private TCarrierOrderAddressMapper tCarrierOrderAddressMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private WorkOrderCenterClient workOrderCenterClient;

    /**
     * 小程序工单列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<WorkOrderListAppletResponseModel> workOrderListForApplet(WorkOrderListAppletRequestModel requestModel) {
        //获取当前登用户 人员表ID
        Long loginUserDriverId = getLoginUserDriverId();
        requestModel.setDriverId(loginUserDriverId);

        //分页查询数据
        requestModel.enablePaging();
        List<WorkOrderListAppletResponseModel> responseModelList = tWorkOrderMapper.searchWorkOrderListForApplet(requestModel);
        if (ListUtils.isNotEmpty(responseModelList)) {
            List<Long> carrierOrderIdList = responseModelList.stream().map(WorkOrderListAppletResponseModel::getCarrierOrderId).collect(Collectors.toList());

            Map<Long, TCarrierOrderAddress> carrierOrderAddressMap = new HashMap<>();
            //查询运单地址信息
            List<TCarrierOrderAddress> carrierOrderAddressList = tCarrierOrderAddressMapper.getByCarrierOrderIds(StringUtils.join(carrierOrderIdList, ','));
            if (ListUtils.isNotEmpty(carrierOrderAddressList)) {
                carrierOrderAddressMap = carrierOrderAddressList.stream().collect(Collectors.toMap(TCarrierOrderAddress::getCarrierOrderId, Function.identity()));
            }

            //组装数据
            for (WorkOrderListAppletResponseModel item : responseModelList) {
                TCarrierOrderAddress tCarrierOrderAddress = carrierOrderAddressMap.get(item.getCarrierOrderId());
                if (tCarrierOrderAddress != null) {
                    item.setLoadProvinceName(tCarrierOrderAddress.getLoadProvinceName());
                    item.setLoadCityName(tCarrierOrderAddress.getLoadCityName());
                    item.setLoadAreaName(tCarrierOrderAddress.getLoadAreaName());
                    item.setLoadDetailAddress(tCarrierOrderAddress.getLoadDetailAddress());
                    item.setLoadWarehouse(tCarrierOrderAddress.getLoadWarehouse());
                    item.setLoadPerson(tCarrierOrderAddress.getConsignorName());
                    item.setLoadMobile(tCarrierOrderAddress.getConsignorMobile());
                }
            }
        }
        return new PageInfo<>(responseModelList);
    }

    /**
     * 工单详情
     *
     * @param requestModel
     * @return
     */
    public WorkOrderDetailAppletResponseModel workOrderDetail(WorkOrderDetailAppletRequestModel requestModel) {
        //获取当前登用户 人员表ID
        Long loginUserDriverId = getLoginUserDriverId();
        requestModel.setDriverId(loginUserDriverId);
        WorkOrderDetailAppletResponseModel responseModel = tWorkOrderMapper.searchWorkOrderDetailForApplet(requestModel);

        if (Objects.nonNull(responseModel)) {
            //查询运单地址信息
            TCarrierOrderAddress tCarrierOrderAddress = tCarrierOrderAddressMapper.getByCarrierOrderId(responseModel.getCarrierOrderId());
            if (tCarrierOrderAddress != null) {
                responseModel.setLoadProvinceName(tCarrierOrderAddress.getLoadProvinceName());
                responseModel.setLoadCityName(tCarrierOrderAddress.getLoadCityName());
                responseModel.setLoadAreaName(tCarrierOrderAddress.getLoadAreaName());
                responseModel.setLoadDetailAddress(tCarrierOrderAddress.getLoadDetailAddress());
                responseModel.setLoadWarehouse(tCarrierOrderAddress.getLoadWarehouse());
                responseModel.setLoadPerson(tCarrierOrderAddress.getConsignorName());
                responseModel.setLoadMobile(tCarrierOrderAddress.getConsignorMobile());
                responseModel.setLoadLongitude(tCarrierOrderAddress.getLoadLongitude());
                responseModel.setLoadLatitude(tCarrierOrderAddress.getLoadLatitude());
                // 填充原发货人信息
                if (CommonConstant.INTEGER_TWO.equals(responseModel.getCheckContact())) {
                    responseModel.setConsignorName(tCarrierOrderAddress.getConsignorName());
                    responseModel.setConsignorMobile(tCarrierOrderAddress.getConsignorMobile());
                }
            }
        }

        return responseModel;
    }

    //获取当前登陆人 人员表id
    private Long getLoginUserDriverId() {
        Long loginUserDriverId = commonBiz.getLoginDriverAppletUserId();
        if (loginUserDriverId == null || CommonConstant.LONG_ZERO.equals(loginUserDriverId)) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_NOT_EXIST);
        }
        return loginUserDriverId;
    }

    /**
     * 工单处理过程列表
     *
     * @param requestModel
     * @return
     */
    public List<WorkOrderProcessAppletResponseModel> workOrderProcess(WorkOrderDetailAppletRequestModel requestModel) {
        TWorkOrder tWorkOrder = checkDriverWorkOrder(requestModel.getWorkOrderId());
        List<WorkOrderProcessResponseModel> workOrderProcessResponseModels = tWorkOrderProcessMapper.searchProcessListByOrderId(tWorkOrder.getId());
        return MapperUtils.mapper(workOrderProcessResponseModels, WorkOrderProcessAppletResponseModel.class);
    }

    /**
     * 校验工单权限
     *
     * @param workOrderId 工单id
     * @return 工单
     */
    private TWorkOrder checkDriverWorkOrder(Long workOrderId) {
        Long loginUserDriverId = getLoginUserDriverId();
        TWorkOrder tWorkOrder = tWorkOrderMapper.selectByPrimaryKeyDecrypt(workOrderId);
        if (tWorkOrder == null) {
            throw new BizException(CarrierDataExceptionEnum.WORK_ORDER_NOT_EXIST);
        }
        //判断当前工单是不是当前司机的
        if (!loginUserDriverId.equals(tWorkOrder.getDriverId())) {
            throw new BizException(CarrierDataExceptionEnum.WORK_ORDER_NOT_EXIST);
        }
        return tWorkOrder;
    }
}
