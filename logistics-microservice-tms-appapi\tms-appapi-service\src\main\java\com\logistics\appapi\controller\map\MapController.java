package com.logistics.appapi.controller.map;

import com.logistics.appapi.client.thirdparty.basicdata.BasicServiceClient;
import com.logistics.appapi.client.thirdparty.basicdata.datamap.request.GetMapListRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.datamap.response.GetMapListResponseModel;
import com.logistics.appapi.controller.map.request.GetMapListRequestDto;
import com.logistics.appapi.controller.map.response.GetMapListResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/7 16:33
 */
@Api(value = "地图相关api", tags = "地图相关api")
@RestController
@RequestMapping(value = "/api/map")
public class MapController {

    @Resource
    private BasicServiceClient basicServiceClient;

    /**
     * 省市区-列表-查询(三级联动)
     *
     * @param requestDto 筛选条件
     * @return 行政区域信息
     */
    @ApiOperation(value = "省市区-列表-查询(三级联动)")
    @PostMapping(value = "/getMapList")
    public Result<List<GetMapListResponseDto>> getMapList(@RequestBody @Valid GetMapListRequestDto requestDto) {
        List<GetMapListResponseModel> list = basicServiceClient.getMapList(MapperUtils.mapper(requestDto, GetMapListRequestModel.class));
        return Result.success(MapperUtils.mapper(list, GetMapListResponseDto.class));
    }

}
