package com.logistics.management.webapi.controller.freightconfig.request.region;

import com.logistics.management.webapi.controller.freightconfig.request.scheme.CarrierFreightConfigSchemeEditRequestDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigRegionDeleteRequestDto extends CarrierFreightConfigSchemeEditRequestDto {

}
