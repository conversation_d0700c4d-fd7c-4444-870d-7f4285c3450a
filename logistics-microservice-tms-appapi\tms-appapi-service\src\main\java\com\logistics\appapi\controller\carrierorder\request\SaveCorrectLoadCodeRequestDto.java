package com.logistics.appapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class SaveCorrectLoadCodeRequestDto {

    @ApiModelProperty(value = "产品编码", required = true)
    @NotBlank(message = "产品编码不能为空")
    private String productCode;

    @ApiModelProperty(value = "运单Id", required = true)
    @NotBlank(message = "运单id不能为空")
    private String carrierOrderId;


}
