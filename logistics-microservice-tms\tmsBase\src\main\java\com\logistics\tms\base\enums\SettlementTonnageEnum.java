/**
 * @author: wjf
 * @date: 2019/3/26 9:55
 */
package com.logistics.tms.base.enums;

public enum SettlementTonnageEnum {

    LOAD(1, "实际提货数量"),
    UNLOAD(2, "实际卸货数量"),
    SIGN(3,"实际签收数量"),
    EXPECT(4,"委托数量"),
    ;

    private Integer key;
    private String value;

    SettlementTonnageEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
