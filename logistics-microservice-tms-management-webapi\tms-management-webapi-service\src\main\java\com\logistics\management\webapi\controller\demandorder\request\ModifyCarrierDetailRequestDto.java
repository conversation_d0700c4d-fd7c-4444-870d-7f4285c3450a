package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/6
 */
@Data
public class ModifyCarrierDetailRequestDto {

	@ApiModelProperty(value = "需求单ID", required = true)
	@NotBlank(message = "需求单ID不能为空")
	private String demandOrderId;

}
