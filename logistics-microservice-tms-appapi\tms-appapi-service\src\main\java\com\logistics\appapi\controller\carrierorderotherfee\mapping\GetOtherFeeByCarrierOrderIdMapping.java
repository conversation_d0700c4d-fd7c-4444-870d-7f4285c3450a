package com.logistics.appapi.controller.carrierorderotherfee.mapping;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.CarrierOrderOtherFeeTypeEnum;
import com.logistics.appapi.client.carrierorderotherfee.response.GetOtherFeeByCarrierOrderIdResponseModel;
import com.logistics.appapi.client.carrierorderotherfee.response.GetOtherFeeItemByCarrierOrderIdResponseModel;
import com.logistics.appapi.controller.carrierorderotherfee.response.BillPicDto;
import com.logistics.appapi.controller.carrierorderotherfee.response.GetOtherFeeByCarrierOrderIdResponseDto;
import com.logistics.appapi.controller.carrierorderotherfee.response.GetOtherFeeItemByCarrierOrderIdResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2024/1/19 10:47
 */
public class GetOtherFeeByCarrierOrderIdMapping extends MapperMapping<GetOtherFeeByCarrierOrderIdResponseModel, GetOtherFeeByCarrierOrderIdResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;

    public GetOtherFeeByCarrierOrderIdMapping(String imagePrefix, Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap = imageMap;
    }

    @Override
    public void configure() {
        GetOtherFeeByCarrierOrderIdResponseModel source = getSource();
        GetOtherFeeByCarrierOrderIdResponseDto destination = getDestination();

        //临时费用转换
        if (ListUtils.isNotEmpty(source.getOtherFeeList())) {
            List<GetOtherFeeItemByCarrierOrderIdResponseModel> otherFeeModelList = source.getOtherFeeList();
            List<GetOtherFeeItemByCarrierOrderIdResponseDto> otherFeeDtoList = new ArrayList<>();
            otherFeeModelList.forEach(o -> {
                GetOtherFeeItemByCarrierOrderIdResponseDto carrierOrderOtherFeeItemDetailResponseDto = new GetOtherFeeItemByCarrierOrderIdResponseDto();
                carrierOrderOtherFeeItemDetailResponseDto.setCarrierOrderOtherFeeItemId(ConverterUtils.toString(o.getCarrierOrderOtherFeeItemId()));
                if (o.getFeeAmount().compareTo(BigDecimal.ZERO) < CommonConstant.INTEGER_ZERO) {
                    carrierOrderOtherFeeItemDetailResponseDto.setFeeAmountSymbol(CommonConstant.TWO);
                    carrierOrderOtherFeeItemDetailResponseDto.setFeeAmount(ConverterUtils.toString(o.getFeeAmount().negate()));
                } else {
                    carrierOrderOtherFeeItemDetailResponseDto.setFeeAmountSymbol(CommonConstant.ONE);
                    carrierOrderOtherFeeItemDetailResponseDto.setFeeAmount(ConverterUtils.toString(o.getFeeAmount()));
                }
                carrierOrderOtherFeeItemDetailResponseDto.setFeeType(ConverterUtils.toString(o.getFeeType()));
                carrierOrderOtherFeeItemDetailResponseDto.setFeeTypeLabel(CarrierOrderOtherFeeTypeEnum.getEnum(o.getFeeType()).getValue());
                List<String> billsPicture = o.getBillsPicture();
                List<BillPicDto> picList = new ArrayList<>();
                for (String pic : billsPicture) {
                    BillPicDto billPicDto = new BillPicDto();
                    billPicDto.setBillsPicture(pic);
                    billPicDto.setBillsPictureUrl(imagePrefix + imageMap.get(pic));
                    picList.add(billPicDto);
                }
                carrierOrderOtherFeeItemDetailResponseDto.setPicList(picList);
                otherFeeDtoList.add(carrierOrderOtherFeeItemDetailResponseDto);
            });
            destination.setOtherFeeList(otherFeeDtoList);
        } else {
            destination.setOtherFeeList(new ArrayList<>());
        }
    }
}
