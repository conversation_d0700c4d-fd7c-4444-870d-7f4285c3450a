package com.logistics.tms.rabbitmq.consumer.entrust;

import com.yelo.tools.rabbitmq.annocation.EnableMqErrorMessageCollect;
import com.logistics.tms.biz.demandorder.DemandOrderBiz;
import com.logistics.tms.rabbitmq.consumer.model.SyncLogisticsWithdrawOrderModel;
import com.rabbitmq.client.Channel;
import com.yelo.tools.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020-08-04 16:08
 */
@Slf4j
@Component
public class WithdrawOrderToTmsConsumer {
    private ObjectMapper objectMapper = JacksonUtils.getInstance();

    @Autowired
    private DemandOrderBiz demandOrderBiz;

    @EnableMqErrorMessageCollect
    @RabbitListener(bindings = {@QueueBinding(
            exchange = @Exchange(name = "logistics.qiyatms.topic", type = "topic", durable = "true"),
            value = @Queue(value = "logistics.qiyatms.withdrawOrderToTms", durable = "true"),
            key = "withdrawOrderToTms")}
            , concurrency = "3")
    public void process(@Payload String message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) throws IOException {
        SyncLogisticsWithdrawOrderModel withdrawOrderModel = objectMapper.readValue(message, SyncLogisticsWithdrawOrderModel.class);
        log.info("网络货运同步-撤回需求单:" + withdrawOrderModel.toString());
        demandOrderBiz.withdrawOrder(withdrawOrderModel);
        channel.basicAck(deliveryTag, false);
    }
}
