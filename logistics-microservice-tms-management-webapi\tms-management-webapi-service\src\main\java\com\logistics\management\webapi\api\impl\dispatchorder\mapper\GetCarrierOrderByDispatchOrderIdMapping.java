package com.logistics.management.webapi.api.impl.dispatchorder.mapper;

import com.logistics.management.webapi.api.feign.dispatchorder.dto.DispatchOrderCarrierChildResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CarrierOrderStatusEnum;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.management.webapi.base.enums.PriceTypeEnum;
import com.logistics.tms.api.feign.dispatchorder.model.DispatchOrderCarrierChildResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * @author: wjf
 * @date: 2018/9/26 11:32
 */
public class GetCarrierOrderByDispatchOrderIdMapping extends MapperMapping<DispatchOrderCarrierChildResponseModel,DispatchOrderCarrierChildResponseDto> {
    @Override
    public void configure() {
        DispatchOrderCarrierChildResponseModel model = getSource();
        DispatchOrderCarrierChildResponseDto dto = getDestination();
        if (null != model){
            if (StringUtils.isNotBlank(model.getUnloadWarehouse())) {
                dto.setUnloadDetailAddress("【" + model.getUnloadWarehouse() + "】 " + model.getUnloadProvinceName() + model.getUnloadCityName() + model.getUnloadAreaName() + model.getUnloadDetailAddress());
            }else{
                dto.setUnloadDetailAddress(model.getUnloadProvinceName() + model.getUnloadCityName() + model.getUnloadAreaName() + model.getUnloadDetailAddress());
            }

            BigDecimal expectAmountTotal = Optional.ofNullable(model.getCarrierExpectAmount()).orElse(CommonConstant.BIG_DECIMAL_ZERO);
            BigDecimal amountTotal = CommonConstant.BIG_DECIMAL_ZERO;
            BigDecimal unloadAmountTotal = CommonConstant.BIG_DECIMAL_ZERO;

            if (model.getStatus().equals(CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey())) {
                amountTotal = model.getCarrierSignAmount();
                unloadAmountTotal = model.getCarrierUnloadAmount();
            } else if (model.getStatus().equals(CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey())) {
                amountTotal = model.getCarrierUnloadAmount();
                unloadAmountTotal = model.getCarrierUnloadAmount();
            } else if (model.getStatus().equals(CarrierOrderStatusEnum.WAIT_UNLOAD.getKey()) || model.getStatus().equals(CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey())) {
                amountTotal = model.getCarrierLoadAmount();
            } else if (model.getStatus().equals(CarrierOrderStatusEnum.WAIT_LOAD.getKey()) || model.getStatus().equals(CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey())) {
                amountTotal = model.getCarrierExpectAmount();
            }

            dto.setGoodsName(model.getCarrierOrderGoodsList().get(0).getGoodsName());
            if (model.getCarrierOrderGoodsList().size()>1){
                dto.setGoodsName(dto.getGoodsName()+" 等");
            }

            BigDecimal dispatchFreightFee = model.getDispatchFreightFee();
            if (model.getDispatchFreightFeeType().equals(PriceTypeEnum.UNIT_PRICE.getKey())){
                dto.setDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee.multiply(expectAmountTotal).setScale(2, BigDecimal.ROUND_HALF_UP)));
            }else if (model.getDispatchFreightFeeType().equals(PriceTypeEnum.FIXED_PRICE.getKey())){
                dto.setDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee));
            }
            if (model.getStatus() > CarrierOrderStatusEnum.WAIT_UNLOAD.getKey()){
                if (model.getDispatchFreightFeeType().equals(PriceTypeEnum.UNIT_PRICE.getKey())){
                    dto.setSignDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee.multiply(unloadAmountTotal).setScale(2, BigDecimal.ROUND_HALF_UP)));
                }else if (model.getDispatchFreightFeeType().equals(PriceTypeEnum.FIXED_PRICE.getKey())){
                    dto.setSignDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee));
                }
            }

            if (CommonConstant.INTEGER_ONE.equals(model.getIfCancel())){
                dto.setStatus(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getValue());
            }else if(CommonConstant.INTEGER_ONE.equals(model.getIfEmpty())){
                dto.setStatus(CarrierOrderStatusEnum.EMPTY.getValue());
            }else{
                dto.setStatus(CarrierOrderStatusEnum.getEnum(model.getStatus()).getValue());
            }
            dto.setExpectAmount(amountTotal.stripTrailingZeros().toPlainString()+GoodsUnitEnum.getEnum(model.getGoodsUnit()).getUnit());
            if (model.getAdjustFee().compareTo(BigDecimal.ZERO) > 0){
                dto.setAdjustFee("+"+model.getAdjustFee());
            }
            if (model.getMarkupFee().compareTo(BigDecimal.ZERO) > 0){
                dto.setMarkupFee("+"+model.getMarkupFee());
            }
        }
    }
}
