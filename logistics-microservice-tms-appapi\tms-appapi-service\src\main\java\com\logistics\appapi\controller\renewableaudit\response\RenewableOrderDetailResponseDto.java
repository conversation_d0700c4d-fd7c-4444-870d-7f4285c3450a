package com.logistics.appapi.controller.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class RenewableOrderDetailResponseDto {

	@ApiModelProperty("乐橘新生订单审核表id")
	private String renewableAuditId = "";

	@ApiModelProperty("订单状态：0 待指派，1 待确认，2 待审核，3 已审核")
	private String status = "";

	@ApiModelProperty("订单状态展示文本")
	private String statusLabel = "";

	@ApiModelProperty("乐橘新生客户名称")
	private String customerName = "";
	@ApiModelProperty("发货省")
	private String loadProvinceName = "";
	@ApiModelProperty("发货市")
	private String loadCityName = "";
	@ApiModelProperty("发货区")
	private String loadAreaName = "";
	@ApiModelProperty("发货详细地址")
	private String loadDetailAddress = "";
	@ApiModelProperty("发货仓库")
	private String loadWarehouse = "";
	@ApiModelProperty("提货经度")
	private String loadLongitude = "";
	@ApiModelProperty("提货纬度")
	private String loadLatitude = "";
	@ApiModelProperty("发货人")
	private String consignorName = "";
	private String consignorMobile = "";

	@ApiModelProperty("收货省")
	private String unloadProvinceName = "";
	@ApiModelProperty("收货市")
	private String unloadCityName = "";
	@ApiModelProperty("收货区")
	private String unloadAreaName = "";
	@ApiModelProperty("收货地址详细")
	private String unloadDetailAddress = "";
	@ApiModelProperty("收货仓库")
	private String unloadWarehouse = "";
	@ApiModelProperty("卸货经度")
	private String unloadLongitude = "";
	@ApiModelProperty("卸货纬度")
	private String unloadLatitude = "";
	@ApiModelProperty("收货人")
	private String receiverName = "";
	private String receiverMobile = "";

	@ApiModelProperty("货物合计")
	private String verifiedGoodsAmountTotal = "";

	@ApiModelProperty("合计")
	private String goodsPriceTotal = "";

	@ApiModelProperty("货物确认状态: 0:待确认 1:已确认")
	private String goodsVerifyStatus = "";

	@ApiModelProperty("货物确认状态: 0:待确认 1:已确认")
	private String goodsVerifyStatusLabel = "";

	@ApiModelProperty("新生订单货物信息")
	private List<RenewableOrderGoodDto> renewableOrderGoods;

	@ApiModelProperty("单据上传状态: 0:待上传 1:已上传")
	private String ticketUploadStatus = "";

	@ApiModelProperty("单据上传状态: 0:待上传 1:已上传")
	private String ticketUploadStatusLabel = "";

	@ApiModelProperty("新生订单单据信息")
	private List<RenewableOrderTicketDto> renewableOrderTickets;

	@ApiModelProperty("驳回备注")
	private String remark = "";
}
