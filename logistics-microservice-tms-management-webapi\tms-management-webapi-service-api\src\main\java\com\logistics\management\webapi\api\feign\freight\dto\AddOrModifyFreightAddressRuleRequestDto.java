package com.logistics.management.webapi.api.feign.freight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 运价地址规则
 * @Author: sj
 * @Date: 2019/12/24 13:13
 */
@Data
public class AddOrModifyFreightAddressRuleRequestDto {
    @ApiModelProperty("运价地址ID")
    private String freightAddressId;
    @ApiModelProperty("运价Id")
    private String freightId;
    @ApiModelProperty("角色: 1 货主 2 车主")
    private String roleType;
    @ApiModelProperty("计价类型: 计价类型 1 基价 2 一日游")
    private String calcType = "1";
    @ApiModelProperty("仓库ID")
    private String warehouseId;
    @ApiModelProperty("仓库名称")
    private String warehouseName;
    @ApiModelProperty("发货省ID")
    private String fromProvinceId;
    @ApiModelProperty("发货省名称")
    private String fromProvinceName;
    @ApiModelProperty("发货市ID")
    private String fromCityId;
    @ApiModelProperty("发货市名称")
    private String fromCityName;
    @ApiModelProperty("发货区ID")
    private String fromAreaId;
    @ApiModelProperty("发货区名称")
    private String fromAreaName;
    @ApiModelProperty("卸货省ID")
    private String toProvinceId;
    @ApiModelProperty("卸货省名称")
    private String toProvinceName;
    @ApiModelProperty("卸货市ID")
    private String toCityId;
    @ApiModelProperty("卸货市名称")
    private String toCityName;
    @ApiModelProperty("卸货区ID")
    private String toAreaId;
    @ApiModelProperty("卸货区名称")
    private String toAreaName;
    @ApiModelProperty(value = "运价规则阶梯",required = true)
    @NotEmpty(message = "请维护基价阶梯")
    @Valid
    private List<FreightAddressRuleDto> freightAddressRuleList;
    @ApiModelProperty("运价多装多卸(车主)")
    private List<FreightAddressRuleMarkupDto> freightAddressRuleMarkupList;
}
