package com.logistics.appapi.controller.website.vehicle.mapping;

import com.logistics.appapi.base.enums.VehicleSourceTypeEnum;
import com.logistics.appapi.base.utils.FrequentMethodUtils;
import com.logistics.appapi.client.website.vehicle.response.VehicleSourceListResponseModel;
import com.logistics.appapi.controller.website.vehicle.response.VehicleSourceListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2019/10/31 19:03
 */
public class VehicleSourceListMapping extends MapperMapping<VehicleSourceListResponseModel,VehicleSourceListResponseDto> {
    @Override
    public void configure() {
        VehicleSourceListResponseModel source = getSource();
        VehicleSourceListResponseDto destination = getDestination();
        if (source != null){
            destination.setType(VehicleSourceTypeEnum.getEnum(source.getType()).getValue());
            destination.setContactMobile(FrequentMethodUtils.encodePhone(source.getContactMobile()));
            destination.setVehicleNo(FrequentMethodUtils.encodeVehicleNo(source.getVehicleNo()));
            destination.setContactName(FrequentMethodUtils.encodeUserName(source.getContactName()));
        }
    }
}
