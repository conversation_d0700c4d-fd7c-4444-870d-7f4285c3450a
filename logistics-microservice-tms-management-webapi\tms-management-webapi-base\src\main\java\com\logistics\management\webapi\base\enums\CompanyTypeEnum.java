package com.logistics.management.webapi.base.enums;

/**
 * @Author: sj
 * @Date: 2019/5/6 17:21
 */
public enum CompanyTypeEnum {
    DEFAULT(0,""),
    COMPANY(1, "企业"),
    PERSONAL(2, "个人"),
            ;
    private Integer key;
    private String value;

    CompanyTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static CompanyTypeEnum getEnum(Integer key) {
        for (CompanyTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
