package com.logistics.tms.base.utils.model.watermark;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.swing.*;

/**
 * 图片属性
 *
 * <AUTHOR>
 * @date 2022/10/14 14:54
 */
@Data
@NoArgsConstructor
public class ImageCoordinate extends Coordinate {

    /**
     * 图片
     */
    private ImageIcon imageIcon;

    public ImageCoordinate(ImageIcon imageIcon, int x, int y) {
        setX(x);
        setY(y);
        this.imageIcon = imageIcon;
    }
}
