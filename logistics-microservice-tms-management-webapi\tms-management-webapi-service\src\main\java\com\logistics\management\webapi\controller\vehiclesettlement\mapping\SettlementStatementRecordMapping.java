package com.logistics.management.webapi.controller.vehiclesettlement.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.VehicleSettlementStatusEnum;
import com.logistics.management.webapi.client.vehiclesettlement.response.SettleStatementRecordItemModel;
import com.logistics.management.webapi.client.vehiclesettlement.response.SettlementStatementRecordResponseModel;
import com.logistics.management.webapi.controller.vehiclesettlement.response.SettleStatementRecordItemDto;
import com.logistics.management.webapi.controller.vehiclesettlement.response.SettlementStatementRecordResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @author:lei.zhu
 * @date:2021/4/12 11:58
 */
@Data
public class SettlementStatementRecordMapping extends MapperMapping<SettlementStatementRecordResponseModel, SettlementStatementRecordResponseDto> {
    @Override
    public void configure() {
        SettlementStatementRecordResponseModel source = getSource();
        SettlementStatementRecordResponseDto destination = getDestination();
        if(source!=null){
            BigDecimal payed = CommonConstant.BIG_DECIMAL_ZERO;
            List<SettleStatementRecordItemDto> list = new ArrayList<>();
            if (ListUtils.isNotEmpty(source.getItemList())){
                for (SettleStatementRecordItemModel itemModel :source.getItemList() ){
                    SettleStatementRecordItemDto settleStatementRecordItemDto = new SettleStatementRecordItemDto();
                    MapperUtils.mapper(itemModel,settleStatementRecordItemDto);
                    settleStatementRecordItemDto.setPayTime(DateUtils.dateToString(itemModel.getPayTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
                    payed = payed.add(itemModel.getPayFee());
                    list.add(settleStatementRecordItemDto);
                }
            }
            destination.setItemList(list);
            destination.setStatusLabel(VehicleSettlementStatusEnum.getEnum(source.getStatus()).getValue());
            destination.setDriverName(Optional.ofNullable(source.getDriverName()).orElse("")+Optional.ofNullable(source.getDriverMobile()).orElse(""));
            if(VehicleSettlementStatusEnum.SETTLED.getKey().equals(source.getStatus())){
                destination.setPayFeeTotal(ConverterUtils.toString(Optional.ofNullable(source.getActualExpensesPayable()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP)));
                destination.setNotPayMoney(ConverterUtils.toString(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP)));
            }else{
                destination.setPayFeeTotal(ConverterUtils.toString(payed.setScale(2, BigDecimal.ROUND_HALF_UP)));
                BigDecimal notPayed = source.getActualExpensesPayable().subtract(payed);
                destination.setNotPayMoney(ConverterUtils.toString(notPayed.setScale(2, BigDecimal.ROUND_HALF_UP)));
            }
        }
    }
}
