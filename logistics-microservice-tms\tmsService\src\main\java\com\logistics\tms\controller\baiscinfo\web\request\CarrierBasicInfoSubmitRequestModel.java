package com.logistics.tms.controller.baiscinfo.web.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/10
 */
@Data
public class CarrierBasicInfoSubmitRequestModel {

	@ApiModelProperty(value = "车主联系人姓名,2-50个汉字")
	private String carrierContactName;

	@ApiModelProperty(value = "车主手机号,2-50个汉字")
	private String carrierContactPhone;

	@ApiModelProperty(value = "车主联系人身份证号,18-21位")
	private String carrierContactIdentityNumber;

	@ApiModelProperty(value = "身份证头像面")
	private String carrierContactIdentityFaceFile;

	@ApiModelProperty(value = "身份证国徽面")
	private String carrierContactIdentityNationalFile;

	@ApiModelProperty(value = "短信验证码")
	private String verificationCode;
}
