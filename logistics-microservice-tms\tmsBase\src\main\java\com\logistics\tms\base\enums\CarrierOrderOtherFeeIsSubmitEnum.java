package com.logistics.tms.base.enums;

public enum CarrierOrderOtherFeeIsSubmitEnum {

    NOT_SUBMIT(0,"否"),
    SUBMIT(1,"是");

    private Integer code;
    private String value;

    CarrierOrderOtherFeeIsSubmitEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
