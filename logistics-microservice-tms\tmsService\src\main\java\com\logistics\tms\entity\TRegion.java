package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TRegion extends BaseEntity {
    /**
    * 大区名称
    */
    @ApiModelProperty("大区名称")
    private String regionName;

    /**
    * 联系人姓名
    */
    @ApiModelProperty("联系人姓名")
    private String contactName;

    /**
    * 联系方式
    */
    @ApiModelProperty("联系方式")
    private String contactPhone;

    /**
    * 0禁用 1启用
    */
    @ApiModelProperty("0禁用 1启用")
    private Integer enabled;
}