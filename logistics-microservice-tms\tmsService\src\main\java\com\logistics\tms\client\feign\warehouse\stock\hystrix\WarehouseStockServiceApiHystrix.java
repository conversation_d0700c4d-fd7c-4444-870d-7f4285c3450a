package com.logistics.tms.client.feign.warehouse.stock.hystrix;

import com.logistics.tms.client.feign.warehouse.stock.WarehouseStockServiceApi;
import com.logistics.tms.client.feign.warehouse.stock.reponse.*;
import com.logistics.tms.client.feign.warehouse.stock.request.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/4 16:02
 */
@Component
public class WarehouseStockServiceApiHystrix implements WarehouseStockServiceApi {
    @Override
    public Result<List<CheckChangeUnloadWarehouseResponseModel>> checkChangeUnloadWarehouse(CheckChangeUnloadWarehouseRequestModel carrierOrderCodes) {
        return Result.timeout();
    }

    @Override
    public Result<List<ListWarehouseByNameResponseModel>> listWarehouseByName(ListWarehouseByNameRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetSkipTokenResponseModel> getSkipToken(GetSkipTokenRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetWareHouseByIdsAndKeyWordAndTypeResponseModel>> getWareHouseByIdsAndKeyWordAndType(@Valid GetWareHouseByIdsAndKeyWordAndTypeRequestModel requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<GetCompanyNameByCodeRespModel> getCompanyNameByCode(@Valid GetCompanyNameByCodeReqModel reqModel) {
        return Result.timeout();
    }
}
