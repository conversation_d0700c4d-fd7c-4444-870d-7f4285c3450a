package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierOrderDetailGoodsInfoDto {

    @ApiModelProperty("品名")
    private String goodsName= "";
    @ApiModelProperty("规格")
    private String size= "";
    @ApiModelProperty("预提件数")
    private String expectAmount= "";
    @ApiModelProperty("预提体积")
    private String expectCapacity= "";
    @ApiModelProperty("预提预计重量")
    private String expectWeight= "";
    @ApiModelProperty("实提件数")
    private String loadAmount = "";
    @ApiModelProperty("实提体积")
    private String loadCapacity = "";
    @ApiModelProperty("实提预计重量")
    private String loadWeight = "";
    @ApiModelProperty("实卸件数")
    private String unloadAmount = "";
    @ApiModelProperty("实卸体积")
    private String unloadCapacity = "";
    @ApiModelProperty("实卸预计重量")
    private String unloadWeight = "";

    @ApiModelProperty("签收件数")
    private String signAmount = "";
    @ApiModelProperty("签收体积")
    private String signLoadCapacity = "";
    @ApiModelProperty("签收预计重量")
    private String signLoadWeight = "";



}
