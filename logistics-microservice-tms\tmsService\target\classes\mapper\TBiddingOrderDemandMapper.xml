<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TBiddingOrderDemandMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TBiddingOrderDemand" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="bidding_order_id" property="biddingOrderId" jdbcType="BIGINT" />
    <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT" />
    <result column="bidding_price_type" property="biddingPriceType" jdbcType="INTEGER" />
    <result column="bidding_price" property="biddingPrice" jdbcType="DECIMAL" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, bidding_order_id, demand_order_id, bidding_price_type, bidding_price, created_by, 
    created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_bidding_order_demand
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_bidding_order_demand
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TBiddingOrderDemand" >
    insert into t_bidding_order_demand (id, bidding_order_id, demand_order_id, 
      bidding_price_type, bidding_price, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{biddingOrderId,jdbcType=BIGINT}, #{demandOrderId,jdbcType=BIGINT}, 
      #{biddingPriceType,jdbcType=INTEGER}, #{biddingPrice,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TBiddingOrderDemand" >
    insert into t_bidding_order_demand
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="biddingOrderId != null" >
        bidding_order_id,
      </if>
      <if test="demandOrderId != null" >
        demand_order_id,
      </if>
      <if test="biddingPriceType != null" >
        bidding_price_type,
      </if>
      <if test="biddingPrice != null" >
        bidding_price,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="biddingOrderId != null" >
        #{biddingOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderId != null" >
        #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="biddingPriceType != null" >
        #{biddingPriceType,jdbcType=INTEGER},
      </if>
      <if test="biddingPrice != null" >
        #{biddingPrice,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TBiddingOrderDemand" >
    update t_bidding_order_demand
    <set >
      <if test="biddingOrderId != null" >
        bidding_order_id = #{biddingOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderId != null" >
        demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="biddingPriceType != null" >
        bidding_price_type = #{biddingPriceType,jdbcType=INTEGER},
      </if>
      <if test="biddingPrice != null" >
        bidding_price = #{biddingPrice,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TBiddingOrderDemand" >
    update t_bidding_order_demand
    set bidding_order_id = #{biddingOrderId,jdbcType=BIGINT},
      demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      bidding_price_type = #{biddingPriceType,jdbcType=INTEGER},
      bidding_price = #{biddingPrice,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>