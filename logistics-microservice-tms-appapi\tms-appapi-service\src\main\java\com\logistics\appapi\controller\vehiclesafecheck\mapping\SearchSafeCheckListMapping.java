package com.logistics.appapi.controller.vehiclesafecheck.mapping;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.SafeCheckStatusEnum;
import com.logistics.appapi.client.vehiclesafecheck.response.AppletSafeCheckListResponseModel;
import com.logistics.appapi.controller.vehiclesafecheck.response.SafeCheckListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/11/25 14:53
 */
public class SearchSafeCheckListMapping extends MapperMapping<AppletSafeCheckListResponseModel,SafeCheckListResponseDto> {
    @Override
    public void configure() {
        AppletSafeCheckListResponseModel model = this.getSource();
        SafeCheckListResponseDto dto = this.getDestination();

        if(StringUtils.isNotBlank(dto.getPeriod())){
            Date periodDate = DateUtils.stringToDate(dto.getPeriod(),CommonConstant.DATE_TO_STRING_YM_PATTERN);
            dto.setPeriod(DateUtils.dateToString(periodDate,CommonConstant.VEHICLE_CHECK_PERIOD_FORMAT));
        }
        if(model!=null){
            dto.setCheckItemCount(CommonConstant.TEN);
            dto.setReformItemCount(CommonConstant.ZERO);
            dto.setQualifiedItemCount(CommonConstant.TEN);
            dto.setStatusLabel(SafeCheckStatusEnum.getEnum(model.getStatus()).getValue());
            if(!SafeCheckStatusEnum.NOT_CHECK.getKey().equals(model.getStatus()) && !SafeCheckStatusEnum.WAIT_CONFIRM.getKey().equals(model.getStatus())){
                if(model.getReformItemCount() != null && model.getReformItemCount() > CommonConstant.INTEGER_ZERO){
                    dto.setReformItemCount(ConverterUtils.toString(model.getReformItemCount()));
                    dto.setQualifiedItemCount(ConverterUtils.toString(CommonConstant.INT_TEN - model.getReformItemCount()));
                }
            }
        }
    }
}
