package com.logistics.management.webapi.api.feign.gpsfee;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.gpsfee.dto.*;
import com.logistics.management.webapi.api.feign.gpsfee.hystrix.GpsFeeApiHystrix;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/8 14:23
 */
@Api(value = "API-GpsFeeApi-gps费用管理")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = GpsFeeApiHystrix.class)
public interface GpsFeeApi {

    @ApiOperation("查询gps费用列表v1.1.7")
    @PostMapping(value = "/api/gpsFee/searchGpsFeeList")
    Result<PageInfo<SearchGpsFeeListResponseDto>> searchGpsFeeList(@RequestBody SearchGpsFeeListRequestDto requestDto);

    @ApiOperation("统计gps费用列表各状态数量v1.1.7")
    @PostMapping(value = "/api/gpsFee/searchGpsFeeListCount")
    Result<SearchGpsFeeListCountResponseDto> searchGpsFeeListCount(@RequestBody SearchGpsFeeListRequestDto requestDto);

    @ApiOperation("查看详情")
    @PostMapping(value = "/api/gpsFee/getGpsFeeDetail")
    Result<GpsFeeDetailResponseDto> getGpsFeeDetail(@RequestBody @Valid GpsFeeIdRequestDto requestDto);

    @ApiOperation("新增/修改gps费用")
    @PostMapping(value = "/api/gpsFee/addOrModifyGpsFee")
    Result addOrModifyGpsFee(@RequestBody @Valid AddOrModifyGpsFeeRequestDto requestDto);

    @ApiOperation("导出gps费用")
    @GetMapping(value = "/api/gpsFee/exportGpsFee")
    void exportGpsFee(SearchGpsFeeListRequestDto requestDto, HttpServletResponse response);

    @ApiOperation("终止gps费用（修改终止时间）")
    @PostMapping(value = "/api/gpsFee/terminationGpsFee")
    Result terminationGpsFee(@RequestBody @Valid TerminationGpsFeeRequestDto requestDto);

    @ApiOperation("查询gps费用操作记录")
    @PostMapping(value = "/api/gpsFee/getGpsFeeRecords")
    Result<List<GpsFeeRecordsListResponseDto>> getGpsFeeRecords(@RequestBody @Valid GpsFeeIdRequestDto requestDto);

    @ApiOperation("导出gps费用操作记录")
    @GetMapping(value = "/api/gpsFee/exportGpsFeeRecords")
    void exportGpsFeeRecords(GpsFeeIdRequestDto requestDto, HttpServletResponse response);

    @ApiOperation("查询gps费用扣减历史")
    @PostMapping(value = "/api/gpsFee/getGpsFeeDeductingHistory")
    Result<List<GetDeductingHistoryResponseDto>> getGpsFeeDeductingHistory(@RequestBody @Valid GpsFeeIdRequestDto requestDto);

    @ApiOperation("导出gps费用扣减历史")
    @GetMapping(value = "/api/gpsFee/exportGpsFeeDeductingHistory")
    void exportGpsFeeDeductingHistory(GpsFeeIdRequestDto requestDto, HttpServletResponse response);
}
