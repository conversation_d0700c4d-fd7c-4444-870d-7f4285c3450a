package com.logistics.management.webapi.controller.companyentrust.mapping;

import com.logistics.management.webapi.base.enums.CertificateStatusEnum;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.client.companyentrust.response.SearchCompanyEntrustResponseModel;
import com.logistics.management.webapi.controller.companyentrust.response.SearchCompanyEntrustResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2019/9/28 13:25
 */
public class ListCompanyEntrustMapping extends MapperMapping<SearchCompanyEntrustResponseModel,SearchCompanyEntrustResponseDto> {
    @Override
    public void configure() {
        SearchCompanyEntrustResponseModel source = getSource();
        SearchCompanyEntrustResponseDto destination = getDestination();
        if (source != null){
            destination.setTradingCertificateIsAmend(CertificateStatusEnum.getEnum(source.getTradingCertificateIsAmend()).getValue());
            destination.setTypeLabel(CompanyTypeEnum.getEnum(source.getType()).getValue());
        }
    }
}
