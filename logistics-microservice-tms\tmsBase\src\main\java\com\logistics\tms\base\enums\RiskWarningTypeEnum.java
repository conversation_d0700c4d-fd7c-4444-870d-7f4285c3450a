/**
 * Created by yun.zhou on 2017/12/12.
 */
package com.logistics.tms.base.enums;

import java.util.HashMap;
import java.util.Map;

public enum RiskWarningTypeEnum {


    //车辆证件风险
    VEHICLE_LICENSE_DATE_VALIDATE(1,"行驶证检查有效期{expire}天内到期：共{total}辆车，其中一周内到期{week}辆，已过期{hasExpired}辆；",DateRemindTypeEnum.VEHICLE_LICENSE_DATE_VALIDATE),
    ROAD_TRANSPORT_DATE_VALIDATE(2,"营运证车辆年审有效期{expire}天内到期：共{total}辆车，其中一周内到期{week}辆，已过期{hasExpired}辆；",DateRemindTypeEnum.ROAD_TRANSPORT_DATE_VALIDATE),
    GRADE_ESTIMATION_DATE_VALIDATE(3,"车辆等级评定检查日期{expire}天内到期：共{total}辆车，其中一周内到期{week}辆，已过期{hasExpired}辆；",DateRemindTypeEnum.GRADE_ESTIMATION_DATE_VALIDATE),
    DRIVING_LICENSE_OBSOLESCENSE_DATE_VALIDATE(9,"车辆强制报废日期{expire}天内到期：共{total}辆车，其中一周内到期{week}辆，已过期{hasExpired}辆；",DateRemindTypeEnum.VEHICLE_OBSOLESCENSE_VALIDATE),

    //人员证件风险
    DRIVER_OCCUPATIONAL_DATE_VALIDATE(4,"从业资格证{expire}天内到期提醒：共{total}人，其中一周内到期{week}人，已过期{hasExpired}人；",DateRemindTypeEnum.DRIVER_OCCUPATIONAL_DATE_VALIDATE),
    INTEGRITY_EXAMINATION_DATE_VALIDATE(5,"诚信考核{expire}天内到期提醒：共{total}人，其中一周内到期{week}人，已过期{hasExpired}人；",DateRemindTypeEnum.INTEGRITY_EXAMINATION_DATE_VALIDATE),
    CONTINUE_LEARNING_DATE_VALIDATE(6,"继续教育{expire}天内到期提醒：共{total}人，其中一周内到期{week}人，已过期{hasExpired}人；",DateRemindTypeEnum.CONTINUE_LEARNING_DATE_VALIDATE),
    IDENTITY_DATE_VALIDATE(7,"身份证{expire}天内到期提醒：共{total}人，其中一周内到期{week}人，已过期{hasExpired}人；",DateRemindTypeEnum.IDENTITY_NUMBER_VALID_DATE),
    DRIVING_LICENSE_DATE_VALIDATE(8,"驾驶证{expire}天内到期提醒：共{total}人，其中一周内到期{week}人，已过期{hasExpired}人；",DateRemindTypeEnum.DRIVING_LICENSE_DATE_VALIDATE),



    ;

    private Integer key;
    private String text;
    private Map<String,Object> placeHolder;
    private DateRemindTypeEnum dateRemindTypeEnum;



    RiskWarningTypeEnum(Integer key, String text,DateRemindTypeEnum dateRemindTypeEnum) {
        this.key = key;
        this.text = text;
        this.placeHolder = new HashMap<>();
        this.dateRemindTypeEnum = dateRemindTypeEnum;
        placeHolder.put("expire",30);
        placeHolder.put("total",null);
        placeHolder.put("week",null);
        placeHolder.put("hasExpired",null);

    }

    public Integer getKey() {
        return key;
    }

    public String getText() {
        return text;
    }

    public DateRemindTypeEnum getDateRemindTypeEnum() {
        return dateRemindTypeEnum;
    }

    public Map<String, Object> getPlaceHolder() {
        return placeHolder;
    }

}
