package com.logistics.management.webapi.api.feign.dateremind.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.dateremind.DateRemindApi;
import com.logistics.management.webapi.api.feign.dateremind.dto.*;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
/**
 * @Author: sj
 * @Date: 2019/5/31 8:47
 */
@Component
public class DateRemindApiHystrix implements DateRemindApi {
    @Override
    public Result<PageInfo<DateRemindListResponseDto>> searchDateRemindList(DateRemindListRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> saveOrModifyDateRemind(SaveOrModifyDateRemindRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> unifiedDateRemind(UnifiedDateRemindRequestDto remindRequestDto) {
        return Result.timeout();
    }

    @Override
    public Result<DateRemindDetailResponseDto> dateRemindDetail(DateRemindDetailRequestDto responseDto) {
        return Result.timeout();
    }

    @Override
    public void export(DateRemindListRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }
}
