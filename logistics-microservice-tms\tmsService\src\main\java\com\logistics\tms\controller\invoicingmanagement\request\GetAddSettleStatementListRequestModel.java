package com.logistics.tms.controller.invoicingmanagement.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/3/20 9:11
 */
@Data
public class GetAddSettleStatementListRequestModel extends AbstractPageForm<GetAddSettleStatementListRequestModel> {
    @ApiModelProperty(value = "承运商id")
    private Long companyCarrierId;

    @ApiModelProperty("对账月份起")
    private String settleStatementMonthStart;
    @ApiModelProperty("对账月份止")
    private String settleStatementMonthEnd;

    @ApiModelProperty("业务类型：1 包装业务，2 自营业务")
    private Integer businessType;
}
