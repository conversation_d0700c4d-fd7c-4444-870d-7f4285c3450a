package com.logistics.appapi.controller.homepage.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class HomeOrderCollectResponseDto {

	@ApiModelProperty("待确认乐橘新生订单")
	private String waitConfirmedCount = "0";

	@ApiModelProperty("待提货运单")
	private String waitLoadCount = "0";

	@ApiModelProperty("工单数量 1.1.2")
	private String workOrderCount = "0";
}
