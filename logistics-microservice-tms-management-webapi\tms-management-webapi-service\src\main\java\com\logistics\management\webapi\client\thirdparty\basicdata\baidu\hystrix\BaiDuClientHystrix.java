package com.logistics.management.webapi.client.thirdparty.basicdata.baidu.hystrix;

import com.logistics.management.webapi.client.thirdparty.basicdata.baidu.BaiDuClient;
import com.logistics.management.webapi.client.thirdparty.basicdata.baidu.request.CheckSensitiveWordRequestModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.baidu.response.CheckSensitiveWordResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2019/12/17 14:45
 */
@Component
public class BaiDuClientHystrix implements BaiDuClient {

    @Override
    public Result<CheckSensitiveWordResponseModel> checkSensitiveWord(CheckSensitiveWordRequestModel requestModel) {
        return Result.timeout();
    }
}
