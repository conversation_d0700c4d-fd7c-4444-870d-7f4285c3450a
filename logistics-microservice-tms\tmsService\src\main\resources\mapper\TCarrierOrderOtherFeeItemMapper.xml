<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderOtherFeeItemMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCarrierOrderOtherFeeItem" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="carrier_order_other_fee_id" property="carrierOrderOtherFeeId" jdbcType="BIGINT" />
    <result column="fee_amount" property="feeAmount" jdbcType="DECIMAL" />
    <result column="fee_type" property="feeType" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, carrier_order_other_fee_id, fee_amount, fee_type, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_carrier_order_other_fee_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_carrier_order_other_fee_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCarrierOrderOtherFeeItem" >
    insert into t_carrier_order_other_fee_item (id, carrier_order_other_fee_id, fee_amount, 
      fee_type, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{carrierOrderOtherFeeId,jdbcType=BIGINT}, #{feeAmount,jdbcType=DECIMAL}, 
      #{feeType,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCarrierOrderOtherFeeItem" keyProperty="id" useGeneratedKeys="true">
    insert into t_carrier_order_other_fee_item
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="carrierOrderOtherFeeId != null" >
        carrier_order_other_fee_id,
      </if>
      <if test="feeAmount != null" >
        fee_amount,
      </if>
      <if test="feeType != null" >
        fee_type,
      </if>
      <if test="feeSource != null" >
        fee_source,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderOtherFeeId != null" >
        #{carrierOrderOtherFeeId,jdbcType=BIGINT},
      </if>
      <if test="feeAmount != null" >
        #{feeAmount,jdbcType=DECIMAL},
      </if>
      <if test="feeType != null" >
        #{feeType,jdbcType=INTEGER},
      </if>
      <if test="feeSource != null" >
        #{feeSource,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCarrierOrderOtherFeeItem" >
    update t_carrier_order_other_fee_item
    <set >
      <if test="carrierOrderOtherFeeId != null" >
        carrier_order_other_fee_id = #{carrierOrderOtherFeeId,jdbcType=BIGINT},
      </if>
      <if test="feeAmount != null" >
        fee_amount = #{feeAmount,jdbcType=DECIMAL},
      </if>
      <if test="feeType != null" >
        fee_type = #{feeType,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCarrierOrderOtherFeeItem" >
    update t_carrier_order_other_fee_item
    set carrier_order_other_fee_id = #{carrierOrderOtherFeeId,jdbcType=BIGINT},
      fee_amount = #{feeAmount,jdbcType=DECIMAL},
      fee_type = #{feeType,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>