package com.logistics.tms.controller.staffvehiclerelation.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/7/29 9:29
 */
@Data
public class SafeCheckStaffRelResponseModel {
    @ApiModelProperty("关系ID")
    private Long vehicleRelationId;

    @ApiModelProperty("牵引车或一体车-车牌ID)")
    private Long vehicleId;
    @ApiModelProperty("牵引车或一体车-车牌号")
    private String vehicleNo;

    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;

    @ApiModelProperty("挂车ID")
    private Long trailerVehicleId;
    @ApiModelProperty("挂车车牌号")
    private String trailerVehicleNo;

    @ApiModelProperty("司机Id")
    private Long staffId;
    @ApiModelProperty("司机名称")
    private String staffName;
    @ApiModelProperty("司机电话")
    private String staffPhone;
}
