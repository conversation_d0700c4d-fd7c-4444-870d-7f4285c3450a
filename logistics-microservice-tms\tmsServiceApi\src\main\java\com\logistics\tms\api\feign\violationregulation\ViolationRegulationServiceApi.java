package com.logistics.tms.api.feign.violationregulation;

import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.violationregulation.hystrix.ViolationRegulationServiceApiHystrix;
import com.logistics.tms.api.feign.violationregulation.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/6/3 10:03
 */
@Api(value = "API-ViolationRegulationServiceApi-违章信息管理")
@FeignClient(name = "logistics-tms-services", fallback = ViolationRegulationServiceApiHystrix.class)
public interface ViolationRegulationServiceApi {

    @ApiOperation(value = "列表")
    @PostMapping(value = "/service/violationRegulation/searchList")
    Result<SearchViolationRegulationListResponseModel> searchViolationRegulationList(@RequestBody SearchViolationRegulationListRequestModel requestModel);

    @ApiOperation(value = "详情")
    @PostMapping(value = "/service/violationRegulation/detail")
    Result<ViolationRegulationDetailResponseModel> getViolationRegulationDetail(@RequestBody ViolationRegulationDetailRequestModel requestModel);

    @ApiOperation(value = "新增/修改")
    @PostMapping(value = "/service/violationRegulation/saveOrModify")
    Result<Boolean> saveOrModifyViolationRegulation(@RequestBody AddOrModifyViolationRegulationRequestModel requestModel);

    @ApiOperation(value = "删除")
    @PostMapping(value = "/service/violationRegulation/delete")
    Result<Boolean> deleteViolationRegulation(@RequestBody DeleteViolationRegulationRequestModel requestModel);

    @ApiOperation(value = "导出")
    @PostMapping(value = "/service/violationRegulation/export")
    Result<List<ViolationRegulationListResponseModel>> export(@RequestBody SearchViolationRegulationListRequestModel requestModel);

    @ApiOperation(value = "导入")
    @PostMapping(value = "/service/violationRegulation/import")
    Result<ImportViolationRegulationResponseModel> importViolationRegulations(@RequestBody ImportViolationRegulationRequestModel requestModel);
}
