package com.logistics.management.webapi.base.constant;

import java.util.LinkedList;
import java.util.List;

public class ImportExcelHeaders {
    private ImportExcelHeaders() {

    }

    public static List<String> getImportVehicleType() {
        return IMPORT_VEHICLE_TYPE;
    }

    public static List<String> getImportInsuranceCompanyType() {
        return IMPORT_INSURANCE_COMPANY_TYPE;
    }

    public static List<String> getImportPersonalAccidentType() {
        return IMPORT_PERSONAL_ACCIDENT_TYPE;
    }

    public static List<String> getImportViolationRegulationType() {
        return IMPORT_VIOLATION_REGULATION_TYPE;
    }

    public static List<String> getImportInsuranceType() {
        return IMPORT_INSURANCE_TYPE;
    }

    public static List<String> getImportVehicleAssetsType() {
        return IMPORT_VEHICLE_ASSETS_TYPE;
    }

    public static List<String> getImportStaffAssetsType() {
        return IMPORT_STAFF_ASSETS_TYPE;
    }

    public static List<String> getImportVehicleTireType() {
        return IMPORT_VEHICLE_TIRE_TYPE;
    }
    public static List<String> getImportImportRefuelCardType() {
        return IMPORT_REFUEL_CARD_TYPE;
    }

    public static List<String> getImportImportRefuelCarType() {
        return IMPORT_REFUEL_CAR_TYPE;
    }

    public static List<String> getImportVehiclePayeeRelType() {
        return IMPORT_VEHICLE_PAYEE_REL_TYPE;
    }

    public static List<String> getImportBankType() {
        return IMPORT_BANK_TYPE;
    }

    public static List<String> getImportDriverPayeeType() {
        return IMPORT_DRIVER_PAYEE_TYPE;
    }

    public static List<String> getImportStaffVehicleType() {
        return IMPORT_STAFF_VEHICLE_TYPE;
    }

    private static final String SAME_TITLE_ONE = "备注（非必填）";
    private static final String SAME_VEHICLE_NO = "车牌号（必填）";
    private static final String SAME_DRIVER_NAME = "司机姓名（必填）";
    private static final String SAME_DRIVER_MOBILE = "司机联系方式（必填）";
    private static final String SAME_BANK_NAME = "银行名称（必填）";


    private static final List<String> IMPORT_VEHICLE_TYPE;
    private static final List<String> IMPORT_INSURANCE_COMPANY_TYPE;
    private static final List<String> IMPORT_PERSONAL_ACCIDENT_TYPE;
    private static final List<String> IMPORT_VIOLATION_REGULATION_TYPE;
    private static final List<String> IMPORT_INSURANCE_TYPE;
    private static final List<String> IMPORT_VEHICLE_ASSETS_TYPE;
    private static final List<String> IMPORT_STAFF_ASSETS_TYPE;
    private static final List<String> IMPORT_VEHICLE_TIRE_TYPE;
    private static final List<String> IMPORT_VEHICLE_PAYEE_REL_TYPE;
    private static final List<String> IMPORT_BANK_TYPE;
    private static final List<String> IMPORT_DRIVER_PAYEE_TYPE;
    private static final List<String> IMPORT_STAFF_VEHICLE_TYPE;
    private static final List<String> IMPORT_REFUEL_CARD_TYPE;
    private static final List<String> IMPORT_REFUEL_CAR_TYPE;


    static {
        IMPORT_VEHICLE_TYPE = new LinkedList();
        IMPORT_VEHICLE_TYPE.add("车辆类型（必填）");
        IMPORT_VEHICLE_TYPE.add("车辆类别（必填）");
        IMPORT_VEHICLE_TYPE.add(SAME_TITLE_ONE);

        IMPORT_INSURANCE_COMPANY_TYPE = new LinkedList();
        IMPORT_INSURANCE_COMPANY_TYPE.add("保险公司名称（必填项）");
        IMPORT_INSURANCE_COMPANY_TYPE.add(SAME_TITLE_ONE);

        IMPORT_PERSONAL_ACCIDENT_TYPE = new LinkedList();
        IMPORT_PERSONAL_ACCIDENT_TYPE.add("保单类型(必填)");
        IMPORT_PERSONAL_ACCIDENT_TYPE.add("保险公司（必填）");
        IMPORT_PERSONAL_ACCIDENT_TYPE.add("保单号（必填）");
        IMPORT_PERSONAL_ACCIDENT_TYPE.add("批单号（批单类型必填）");
        IMPORT_PERSONAL_ACCIDENT_TYPE.add("保费总额（必填）");
        IMPORT_PERSONAL_ACCIDENT_TYPE.add("保单人数（必填）");
        IMPORT_PERSONAL_ACCIDENT_TYPE.add("保险生效时间（必填XXXX-XX-XX）");
        IMPORT_PERSONAL_ACCIDENT_TYPE.add("保险截止时间（必填XXXX-XX-XX）");

        IMPORT_VIOLATION_REGULATION_TYPE = new LinkedList();
        IMPORT_VIOLATION_REGULATION_TYPE.add(SAME_VEHICLE_NO);
        IMPORT_VIOLATION_REGULATION_TYPE.add(SAME_DRIVER_NAME);
        IMPORT_VIOLATION_REGULATION_TYPE.add(SAME_DRIVER_MOBILE);
        IMPORT_VIOLATION_REGULATION_TYPE.add("扣分（非必填）");
        IMPORT_VIOLATION_REGULATION_TYPE.add("罚款（非必填）");
        IMPORT_VIOLATION_REGULATION_TYPE.add("违章时间（必填）");
        IMPORT_VIOLATION_REGULATION_TYPE.add("违章地点（必填）");
        IMPORT_VIOLATION_REGULATION_TYPE.add(SAME_TITLE_ONE);

        IMPORT_INSURANCE_TYPE = new LinkedList();
        IMPORT_INSURANCE_TYPE.add("险种（必填）");
        IMPORT_INSURANCE_TYPE.add(SAME_VEHICLE_NO);
        IMPORT_INSURANCE_TYPE.add(SAME_DRIVER_NAME);
        IMPORT_INSURANCE_TYPE.add(SAME_DRIVER_MOBILE);
        IMPORT_INSURANCE_TYPE.add("保险公司（必填）");
        IMPORT_INSURANCE_TYPE.add("保险单号（必填）");
        IMPORT_INSURANCE_TYPE.add("批单号（个人意外险批单类型必填）");
        IMPORT_INSURANCE_TYPE.add("保费（元）（必填）");
        IMPORT_INSURANCE_TYPE.add("保险开始时间（必填XXXX-XX-XX）");
        IMPORT_INSURANCE_TYPE.add("保险截止时间（必填XXXX-XX-XX）");
        IMPORT_INSURANCE_TYPE.add("代缴车船税（交强险必填）");

        IMPORT_VEHICLE_ASSETS_TYPE = new LinkedList();
        IMPORT_VEHICLE_ASSETS_TYPE.add("车辆使用性质（必填）");
        IMPORT_VEHICLE_ASSETS_TYPE.add("是否安装GPS");
        IMPORT_VEHICLE_ASSETS_TYPE.add("是否入网石化");
        IMPORT_VEHICLE_ASSETS_TYPE.add("车辆机构(必填)");
        IMPORT_VEHICLE_ASSETS_TYPE.add("真实所属车主(必填)");
        IMPORT_VEHICLE_ASSETS_TYPE.add("车牌号(必填)");
        IMPORT_VEHICLE_ASSETS_TYPE.add("车辆类型");
        IMPORT_VEHICLE_ASSETS_TYPE.add("所有人");
        IMPORT_VEHICLE_ASSETS_TYPE.add("住址");
        IMPORT_VEHICLE_ASSETS_TYPE.add("品牌");
        IMPORT_VEHICLE_ASSETS_TYPE.add("型号");
        IMPORT_VEHICLE_ASSETS_TYPE.add("车辆识别号");
        IMPORT_VEHICLE_ASSETS_TYPE.add("发动机号码");
        IMPORT_VEHICLE_ASSETS_TYPE.add("发证部门");
        IMPORT_VEHICLE_ASSETS_TYPE.add("注册日期(非必填XXXX-XX-XX)");
        IMPORT_VEHICLE_ASSETS_TYPE.add("发证日期(非必填XXXX-XX-XX)");
        IMPORT_VEHICLE_ASSETS_TYPE.add("归档编号");
        IMPORT_VEHICLE_ASSETS_TYPE.add("核定载人数（人）");
        IMPORT_VEHICLE_ASSETS_TYPE.add("总质量(KG)");
        IMPORT_VEHICLE_ASSETS_TYPE.add("整备质量(KG)");
        IMPORT_VEHICLE_ASSETS_TYPE.add("核定载质量(KG)");
        IMPORT_VEHICLE_ASSETS_TYPE.add("准牵引总质量(KG)");
        IMPORT_VEHICLE_ASSETS_TYPE.add("车外廓长（mm）");
        IMPORT_VEHICLE_ASSETS_TYPE.add("车外廓宽（mm）");
        IMPORT_VEHICLE_ASSETS_TYPE.add("车外廓高（mm）");
        IMPORT_VEHICLE_ASSETS_TYPE.add("车辆强制报废期（非必填XXXX-XX-XX）");
        IMPORT_VEHICLE_ASSETS_TYPE.add("车辆轴数");
        IMPORT_VEHICLE_ASSETS_TYPE.add("驱动轴数");
        IMPORT_VEHICLE_ASSETS_TYPE.add("轮胎数");
        IMPORT_VEHICLE_ASSETS_TYPE.add("车牌颜色");
        IMPORT_VEHICLE_ASSETS_TYPE.add("车身颜色");
        IMPORT_VEHICLE_ASSETS_TYPE.add("行驶证检查有效期(非必填XXXX-XX-XX）");
        IMPORT_VEHICLE_ASSETS_TYPE.add("道路运输证发证签");
        IMPORT_VEHICLE_ASSETS_TYPE.add("道路运输证经营许可证号");
        IMPORT_VEHICLE_ASSETS_TYPE.add("道路运输证经济类型");
        IMPORT_VEHICLE_ASSETS_TYPE.add("道路运输证吨（坐）位");
        IMPORT_VEHICLE_ASSETS_TYPE.add("道路运输证经营范围");
        IMPORT_VEHICLE_ASSETS_TYPE.add("道路运输证发证部门");
        IMPORT_VEHICLE_ASSETS_TYPE.add("道路运输证发证日期（非必填XXXX-XX-XX）");
        IMPORT_VEHICLE_ASSETS_TYPE.add("道路运输证初领日期（非必填XXXX-XX-XX）");
        IMPORT_VEHICLE_ASSETS_TYPE.add("车辆年审有效期（非必填XXXX-XX-XX）");
        IMPORT_VEHICLE_ASSETS_TYPE.add("认证开始时间（非必填XXXX-XX-XX）");
        IMPORT_VEHICLE_ASSETS_TYPE.add("认证截止时间（非必填XXXX-XX-XX）");
        IMPORT_VEHICLE_ASSETS_TYPE.add("GPS安装日期（非必填XXXX-XX-XX）");
        IMPORT_VEHICLE_ASSETS_TYPE.add("终端型号");
        IMPORT_VEHICLE_ASSETS_TYPE.add("GPS终端SIM卡号");
        IMPORT_VEHICLE_ASSETS_TYPE.add("GPS服务商名称");
        IMPORT_VEHICLE_ASSETS_TYPE.add("登记证书编号");
        IMPORT_VEHICLE_ASSETS_TYPE.add("等级评定检查日期（非必填XXXX-XX-XX）");
        IMPORT_VEHICLE_ASSETS_TYPE.add("排放标准");

        IMPORT_STAFF_ASSETS_TYPE = new LinkedList();
        IMPORT_STAFF_ASSETS_TYPE.add("人员类型（必填）");
        IMPORT_STAFF_ASSETS_TYPE.add("人员机构（必填）");
        IMPORT_STAFF_ASSETS_TYPE.add("性别（必填）");
        IMPORT_STAFF_ASSETS_TYPE.add("人员姓名（必填）");
        IMPORT_STAFF_ASSETS_TYPE.add("手机号（必填）");
        IMPORT_STAFF_ASSETS_TYPE.add("身份证号码（必填）");
        IMPORT_STAFF_ASSETS_TYPE.add("年龄");
        IMPORT_STAFF_ASSETS_TYPE.add("身份证期限");
        IMPORT_STAFF_ASSETS_TYPE.add("劳动合同编号");
        IMPORT_STAFF_ASSETS_TYPE.add("劳动合同有效期");
        IMPORT_STAFF_ASSETS_TYPE.add("从业资格证证号");
        IMPORT_STAFF_ASSETS_TYPE.add("初次发证日期");
        IMPORT_STAFF_ASSETS_TYPE.add("机动车驾驶证号");
        IMPORT_STAFF_ASSETS_TYPE.add("准驾车型");
        IMPORT_STAFF_ASSETS_TYPE.add("驾照开始时间");
        IMPORT_STAFF_ASSETS_TYPE.add("驾照截止时间");
        IMPORT_STAFF_ASSETS_TYPE.add("从业资格证发证期");
        IMPORT_STAFF_ASSETS_TYPE.add("从业资格证有效期");
        IMPORT_STAFF_ASSETS_TYPE.add("诚信考核有效期");
        IMPORT_STAFF_ASSETS_TYPE.add("继续教育有效期");


        IMPORT_VEHICLE_TIRE_TYPE = new LinkedList<>();
        IMPORT_VEHICLE_TIRE_TYPE.add(SAME_VEHICLE_NO);
        IMPORT_VEHICLE_TIRE_TYPE.add(SAME_DRIVER_NAME);
        IMPORT_VEHICLE_TIRE_TYPE.add(SAME_DRIVER_MOBILE);
        IMPORT_VEHICLE_TIRE_TYPE.add("更换日期（必填）");
        IMPORT_VEHICLE_TIRE_TYPE.add("轮胎企业（必填）");
        IMPORT_VEHICLE_TIRE_TYPE.add(SAME_TITLE_ONE);
        IMPORT_VEHICLE_TIRE_TYPE.add("轮胎号1");
        IMPORT_VEHICLE_TIRE_TYPE.add("数量");
        IMPORT_VEHICLE_TIRE_TYPE.add("单价");
        IMPORT_VEHICLE_TIRE_TYPE.add("轮胎号2");
        IMPORT_VEHICLE_TIRE_TYPE.add("数量");
        IMPORT_VEHICLE_TIRE_TYPE.add("单价");
        IMPORT_VEHICLE_TIRE_TYPE.add("轮胎号3");
        IMPORT_VEHICLE_TIRE_TYPE.add("数量");
        IMPORT_VEHICLE_TIRE_TYPE.add("单价");
        IMPORT_VEHICLE_TIRE_TYPE.add("轮胎号4");
        IMPORT_VEHICLE_TIRE_TYPE.add("数量");
        IMPORT_VEHICLE_TIRE_TYPE.add("单价");
        IMPORT_VEHICLE_TIRE_TYPE.add("轮胎号5");
        IMPORT_VEHICLE_TIRE_TYPE.add("数量");
        IMPORT_VEHICLE_TIRE_TYPE.add("单价");

        IMPORT_VEHICLE_PAYEE_REL_TYPE = new LinkedList<>();
        IMPORT_VEHICLE_PAYEE_REL_TYPE.add(SAME_VEHICLE_NO);
        IMPORT_VEHICLE_PAYEE_REL_TYPE.add("收款人姓名（必填）");
        IMPORT_VEHICLE_PAYEE_REL_TYPE.add("身份证号（必填）");
        IMPORT_VEHICLE_PAYEE_REL_TYPE.add("银行卡号（必填）");
        IMPORT_VEHICLE_PAYEE_REL_TYPE.add(SAME_BANK_NAME);
        IMPORT_VEHICLE_PAYEE_REL_TYPE.add(SAME_TITLE_ONE);

        IMPORT_BANK_TYPE = new LinkedList<>();
        IMPORT_BANK_TYPE.add(SAME_BANK_NAME);
        IMPORT_BANK_TYPE.add("支行名称（必填）");
        IMPORT_BANK_TYPE.add(SAME_TITLE_ONE);

        IMPORT_DRIVER_PAYEE_TYPE = new LinkedList<>();
        IMPORT_DRIVER_PAYEE_TYPE.add("收款人姓名（必填）");
        IMPORT_DRIVER_PAYEE_TYPE.add("联系方式（非必填）");
        IMPORT_DRIVER_PAYEE_TYPE.add("身份证号（必填）");
        IMPORT_DRIVER_PAYEE_TYPE.add("银行卡号（必填）");
        IMPORT_DRIVER_PAYEE_TYPE.add(SAME_BANK_NAME);
        IMPORT_DRIVER_PAYEE_TYPE.add(SAME_TITLE_ONE);

        IMPORT_STAFF_VEHICLE_TYPE = new LinkedList<>();
        IMPORT_STAFF_VEHICLE_TYPE.add("关联车辆机构(必填)");
        IMPORT_STAFF_VEHICLE_TYPE.add("关联车辆类型(必填)");
        IMPORT_STAFF_VEHICLE_TYPE.add("车牌号(必填)");
        IMPORT_STAFF_VEHICLE_TYPE.add("挂车车牌号(必填)");
        IMPORT_STAFF_VEHICLE_TYPE.add("司机姓名(必填)");
        IMPORT_STAFF_VEHICLE_TYPE.add("司机电话(必填)");
        IMPORT_STAFF_VEHICLE_TYPE.add(SAME_TITLE_ONE);

        IMPORT_REFUEL_CARD_TYPE = new LinkedList<>();
        IMPORT_REFUEL_CARD_TYPE.add("*副卡卡号");
        IMPORT_REFUEL_CARD_TYPE.add("*车牌号");
        IMPORT_REFUEL_CARD_TYPE.add("*司机");
        IMPORT_REFUEL_CARD_TYPE.add("*司机手机号");
        IMPORT_REFUEL_CARD_TYPE.add("*充值金额");
        IMPORT_REFUEL_CARD_TYPE.add("*充值时间");
        IMPORT_REFUEL_CARD_TYPE.add("备注");


        IMPORT_REFUEL_CAR_TYPE = new LinkedList<>();
        IMPORT_REFUEL_CAR_TYPE.add("*合作公司");
        IMPORT_REFUEL_CAR_TYPE.add("*升数");
        IMPORT_REFUEL_CAR_TYPE.add("*车牌号");
        IMPORT_REFUEL_CAR_TYPE.add("*司机");
        IMPORT_REFUEL_CAR_TYPE.add("*司机手机号");
        IMPORT_REFUEL_CAR_TYPE.add("*总金额");
        IMPORT_REFUEL_CAR_TYPE.add("*加油时间");
        IMPORT_REFUEL_CAR_TYPE.add("备注");
    }
}
