package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CarrierOrderListBeforeSignUpRequestDto {

    @ApiModelProperty(value = "运单ID集合", required = true)
    @NotNull(message = "请选择订单")
    private List<String> carrierOrderIds;
}
