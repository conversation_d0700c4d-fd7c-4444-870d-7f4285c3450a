package com.logistics.tms.api.feign.freight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: sj
 * @Date: 2019/12/31 18:54
 */
@Data
public class DemandOrderIdAndExpectAmountRequestModel {
    @ApiModelProperty("需求单ID")
    private Long demandOrderId;
    @ApiModelProperty("预计提货吨位数")
    private BigDecimal expectAmount = BigDecimal.ZERO;
}
