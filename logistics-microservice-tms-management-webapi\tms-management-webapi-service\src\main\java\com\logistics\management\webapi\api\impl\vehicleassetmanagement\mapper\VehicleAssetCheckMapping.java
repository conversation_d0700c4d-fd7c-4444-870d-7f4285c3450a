package com.logistics.management.webapi.api.impl.vehicleassetmanagement.mapper;

import com.logistics.management.webapi.api.feign.vehicleassetmanagement.dto.AddOrModifyVehicleBasicInfoRequestDto;
import com.logistics.management.webapi.api.feign.vehicleassetmanagement.dto.ImportVehicleBasicInfoListRequestDto;
import com.logistics.management.webapi.api.feign.vehicleassetmanagement.dto.ImportVehicleBasicInfoRequestDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: sj
 * @Date: 2019/6/21 9:27
 */
@Slf4j
@Component
public class VehicleAssetCheckMapping {
    private static final String REG_ONE = "^[A-Z0-9]{1,30}$";
    private static final String REG_TWO = "^[\\u4E00-\\u9FA5a-zA-Z]{2,50}$";
    private static final String REG_FOUR = "^[A-Z0-9]{2,50}$";
    private static final String REG_FIVE = "^[1-8]{1}$";
    private static final String REG_SIX = "^[2-9]$|^[1-9]{1}[0-9]{1,4}$|^100000$";
    private static final String REG_SEVEN = "^[1-9]$|^10$";
    private static final String REG_EIGHT = "^[\\u4E00-\\u9FA5a-zA-Z]{1,10}$";
    private static final String REG_NINE = "^[\\u4E00-\\u9FA5-0-9]{2,50}$";
    private static final String REG_TEN = "^[1-9]$|^[1][0-9]$|^20$";
    private static final String REG_TRANSPORT_TONNAGE = "^\\d+(\\.\\d+)?$";

    /**
     * 初始化Excel车辆信息导入
     * @param excelList
     * @return
     */

    public ImportVehicleBasicInfoRequestDto initImportRepeatData(List<List<Object>> excelList){
        ImportVehicleBasicInfoRequestDto requestDto = new ImportVehicleBasicInfoRequestDto();
        if(ListUtils.isEmpty(excelList)){
            return requestDto;
        }

        List<ImportVehicleBasicInfoListRequestDto> importList = new ArrayList<>();
        Set<ImportVehicleBasicInfoListRequestDto> setContains = new HashSet<>();
        ImportVehicleBasicInfoListRequestDto vehicleBasicInfoListRequestDto;
        for (int i = 0; i < excelList.size(); i++) {
            vehicleBasicInfoListRequestDto = new ImportVehicleBasicInfoListRequestDto();
            List<Object> objects = excelList.get(i);
            if(objects.size() < CommonConstant.INTEGER_FIVE
                    || StringUtils.isBlank(objToString(objects.get(CommonConstant.INTEGER_FIVE)))){
                continue;
            }

            //车辆机构 自有 外包
            String vehicleProperty = objToString(objects.get(CommonConstant.INTEGER_THREE));
            if(StringUtils.isBlank(vehicleProperty)
                    || VehiclePropertyEnum.getEnumByValue(vehicleProperty) == null){
                continue;
            }
            vehicleBasicInfoListRequestDto.setVehicleProperty(objToString(VehiclePropertyEnum.getEnumByValue(vehicleProperty).getKey()));

            //按照Excel顺序取值 == > 使用性质
            String usegeProperty = objToString(objects.get(CommonConstant.INTEGER_ZERO));
            if(StringUtils.isNotBlank(usegeProperty)
                    && VehicleUsagePropertyEnum.getEnumByValue(usegeProperty) == null){
                continue;
            }else{
                if(CommonConstant.ONE.equals(vehicleBasicInfoListRequestDto.getVehicleProperty())
                        && StringUtils.isBlank(usegeProperty)){
                    continue;
                }
            }
            vehicleBasicInfoListRequestDto.setUsageProperty(objToString(VehicleUsagePropertyEnum.getEnumByValue(usegeProperty).getKey()));

            //是否安装GPS
            String ifInstallGps = objToString(objects.get(CommonConstant.INTEGER_ONE));
            if(StringUtils.isNotBlank(ifInstallGps)
                    && IfInstallGpsEnum.getEnumByName(ifInstallGps) == null){
                continue;
            }
            vehicleBasicInfoListRequestDto.setIfInstallGps(objToString(IfInstallGpsEnum.getEnumByName(ifInstallGps).getKey()));

            //是否入网中石化
            String ifAccessSinopec = objToString(objects.get(CommonConstant.INTEGER_TWO));
            if(StringUtils.isNotBlank(ifAccessSinopec)
                    && IfAccessSinopecEnum.getEnumByValue(ifAccessSinopec) == null){
                continue;
            }
            vehicleBasicInfoListRequestDto.setIfAccessSinopec(objToString(IfAccessSinopecEnum.getEnumByValue(ifAccessSinopec).getKey()));

            //真实所属车主
            vehicleBasicInfoListRequestDto.setVehicleOwner(objToString(objects.get(CommonConstant.INTEGER_FOUR)));
            if(StringUtils.isBlank(vehicleBasicInfoListRequestDto.getVehicleOwner())){
                continue;
            }

            //车牌号(外包车辆的必填项校验)
            vehicleBasicInfoListRequestDto.setVehicleNo(objToString(objects.get(CommonConstant.INTEGER_FIVE)));
            if(!FrequentMethodUtils.validateVehicleFormat(vehicleBasicInfoListRequestDto.getVehicleNo())){
                continue;
            }

            //车辆类型(外包车辆的必填项校验)
            vehicleBasicInfoListRequestDto.setVehicleTypeLabel(objToString(objects.get(CommonConstant.INTEGER_SIX)));

            //所有人
            vehicleBasicInfoListRequestDto.setOwner(objToString(objects.get(CommonConstant.INTEGER_SEVEN)));

            //住址
            vehicleBasicInfoListRequestDto.setAddress(objToString(objects.get(CommonConstant.INTEGER_EIGHT)));

            //品牌
            vehicleBasicInfoListRequestDto.setBrand(objToString(objects.get(CommonConstant.INTEGER_NINE)));
            if((StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getBrand())
                    && (vehicleBasicInfoListRequestDto.getBrand().length() < CommonConstant.INTEGER_TWO) || vehicleBasicInfoListRequestDto.getBrand().length() > CommonConstant.INT_FIFTY)){
                log.info("check: 品牌");
                continue;
            }

            //型号
            vehicleBasicInfoListRequestDto.setModel(objToString(objects.get(CommonConstant.INT_TEN)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getModel())
                    && (vehicleBasicInfoListRequestDto.getModel().length() < CommonConstant.INTEGER_TWO ||vehicleBasicInfoListRequestDto.getModel().length() > CommonConstant.INT_FIFTY )){
                log.info("check: 型号");
                continue;
            }

            //车辆识别号
            vehicleBasicInfoListRequestDto.setVehicleIdentificationNumber(objToString(objects.get(CommonConstant.INT_ELEVEN)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getVehicleIdentificationNumber())
                    && !FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getVehicleIdentificationNumber(),REG_ONE)){
                log.info("check2: 车辆识别号");
                continue;
            }

            //发动机号码
            vehicleBasicInfoListRequestDto.setEngineNumber(objToString(objects.get(CommonConstant.INT_TWELVE)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getEngineNumber())
                    && !FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getEngineNumber(),REG_ONE)){
                log.info("check2: 发动机号码");
                continue;
            }

            //发证部门
            vehicleBasicInfoListRequestDto.setDrivingCertificationDepartmentOne(objToString(objects.get(CommonConstant.INT_THIRTEEN)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getDrivingCertificationDepartmentOne())
                    && !FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getDrivingCertificationDepartmentOne(),REG_TWO)){
                log.info("check2: 发证部门");
                continue;
            }

            //注册日期
            vehicleBasicInfoListRequestDto.setRegistrationDate(objToDate(objects.get(CommonConstant.INT_FOURTEEN)));


            //发证日期
            vehicleBasicInfoListRequestDto.setDrivingIssueDate(objToDate(objects.get(CommonConstant.INT_FIFTEEN)));


            //归档编号
            vehicleBasicInfoListRequestDto.setFilingNumber(objToString(objects.get(CommonConstant.INT_SIXTEEN)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getFilingNumber())
                    && !FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getFilingNumber(),REG_FOUR)){
                log.info("check2: 归档编号");
                continue;
            }

            //核定载人数
            vehicleBasicInfoListRequestDto.setAuthorizedCarryingCapacity(objToString(objects.get(CommonConstant.INT_SEVENTEEN)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getAuthorizedCarryingCapacity())
                    && !FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getAuthorizedCarryingCapacity(),REG_FIVE)){
                log.info("check2: 核定载人数");
                continue;
            }

            //总质量
            vehicleBasicInfoListRequestDto.setTotalWeight(objToString(objects.get(CommonConstant.INT_EIGHTEEN)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getTotalWeight())
                    && !FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getTotalWeight(),REG_SIX)){
                log.info("check1: 总质量");
                continue;
            }

            //整备质量
            vehicleBasicInfoListRequestDto.setCurbWeight(objToString(objects.get(CommonConstant.INT_NINETEEN)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getCurbWeight())
                    && !FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getCurbWeight(),REG_SIX)){
                log.info("check2: 整备质量");
                continue;
            }

            //核定载质量
            vehicleBasicInfoListRequestDto.setApprovedLoadWeight(objToString(objects.get(CommonConstant.INT_TWENTY)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getApprovedLoadWeight())&&!FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getApprovedLoadWeight(),REG_SIX)){
                    log.info("check1: 核定载质量");
                    continue;
            }

            //牵引总质量
            vehicleBasicInfoListRequestDto.setTractionMassWeight(objToString(objects.get(CommonConstant.INT_TWENTY_ONE)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getTractionMassWeight())&&!FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getTractionMassWeight(),REG_SIX)){
                    log.info("check1: 牵引总质量");
                    continue;
            }

            //长 (外包车辆的必填项校验)
            vehicleBasicInfoListRequestDto.setLength(objToString(objects.get(CommonConstant.INT_TWENTY_TWO)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getLength())
                    && !FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getLength(),REG_SIX)){
                log.info("check1: 长");
                continue;
            }

            //宽 (外包车辆的必填项校验)
            vehicleBasicInfoListRequestDto.setWidth(objToString(objects.get(CommonConstant.INT_TWENTY_THREE)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getWidth())
                    && !FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getWidth(),REG_SIX)){
                log.info("check2: 宽");
                continue;
            }

            //高 (外包车辆的必填项校验)
            vehicleBasicInfoListRequestDto.setHeight(objToString(objects.get(CommonConstant.INT_TWENTY_FOUR)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getHeight())
                    && !FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getHeight(),REG_SIX)){
                log.info("check2: 高");
                continue;
            }

            //车辆强制报废期
            vehicleBasicInfoListRequestDto.setObsolescenceDate(objToDate(objects.get(CommonConstant.INT_TWENTY_FIVE)));

            //车辆轴数
            vehicleBasicInfoListRequestDto.setAxleNumber(objToString(objects.get(CommonConstant.INT_TWENTY_SIX)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getAxleNumber())
                    && !FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getAxleNumber(),REG_SEVEN)){
                log.info("check1: 车辆轴数");
                continue;
            }

            //驱动轴数
            vehicleBasicInfoListRequestDto.setDriveShaftNumber(objToString(objects.get(CommonConstant.INT_TWENTY_SEVEN)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getDriveShaftNumber())
                    && !FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getDriveShaftNumber(),REG_SEVEN)){
                log.info("check1: 驱动轴数");
                continue;
            }

            //轮胎数
            vehicleBasicInfoListRequestDto.setTiresNumber(objToString(objects.get(CommonConstant.INT_TWENTY_EIGHT)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getTiresNumber())
                    && !FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getTiresNumber(),REG_TEN)){
                log.info("check2: 轮胎数");
                continue;
            }

            //车牌颜色(外包车辆的必填项校验)
            String plateColor = objToString(objects.get(CommonConstant.INT_TWENTY_NIGHT));
            if(StringUtils.isNotBlank(plateColor) && PlateColorEnum.getEnumByValue(plateColor)==null){
                log.info("check2: 车牌颜色");
                continue;
            }
            if(PlateColorEnum.getEnumByValue(plateColor) != null){
                vehicleBasicInfoListRequestDto.setPlateColor(objToString(PlateColorEnum.getEnumByValue(plateColor).getKey()));
            }

            //车身颜色
            vehicleBasicInfoListRequestDto.setBodyColor(objToString(objects.get(CommonConstant.INT_THIRTY)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getBodyColor())
                    && !FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getBodyColor().trim(),REG_EIGHT)){
                log.info("check2: 车身颜色");
                continue;
            }

            //行驶证检查有效期
            vehicleBasicInfoListRequestDto.setCheckVehicleValidDate(objToDate(objects.get(CommonConstant.INT_THIRTY_ONE)));

            //发证签
            vehicleBasicInfoListRequestDto.setCertificationSign(objToString(objects.get(CommonConstant.INT_THIRTY_TWO)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getCertificationSign())
                    && !FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getCertificationSign(),REG_NINE)){
                log.info("check2: 发证签");
                continue;
            }

            //经营许可证
            vehicleBasicInfoListRequestDto.setBusinessLicenseNumber(objToString(objects.get(CommonConstant.INT_THIRTY_THREE)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getBusinessLicenseNumber())
                    && !FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getBusinessLicenseNumber(),REG_NINE)){
                log.info("check2: 经营许可证");
                continue;
            }

            //经济类型
            vehicleBasicInfoListRequestDto.setEconomicType(objToString(objects.get(CommonConstant.INT_THIRTY_FOUR)));

            //吨位
            vehicleBasicInfoListRequestDto.setTransportTonnage(objToString(objects.get(CommonConstant.INT_THIRTY_FIVE)));
            if(StringUtils.isNotBlank(vehicleBasicInfoListRequestDto.getTransportTonnage())
                    && !FrequentMethodUtils.checkReg(vehicleBasicInfoListRequestDto.getTransportTonnage(),REG_TRANSPORT_TONNAGE)){
                log.info("check1: 吨位");
                continue;
            }

            //经营范围
            vehicleBasicInfoListRequestDto.setBusinessScope(objToString(objects.get(CommonConstant.INT_THIRTY_SIX)));
            //道路运输证发证部门
            vehicleBasicInfoListRequestDto.setRoadTransportCertificationDepartment(objToString(objects.get(CommonConstant.INT_THIRTY_SEVEN)));
            //道路运输证发证日期
            vehicleBasicInfoListRequestDto.setIssueDate(objToDate(objects.get(CommonConstant.INT_THIRTY_EIGHT)));
            //道路运输证初领日期
            vehicleBasicInfoListRequestDto.setObtainDate(objToDate(objects.get(CommonConstant.INT_THIRTY_NIGHT)));
            //车辆年审有效期
            vehicleBasicInfoListRequestDto.setCheckRoadValidDate(objToDate(objects.get(CommonConstant.INT_FORTY)));
            //认证开始时间
            vehicleBasicInfoListRequestDto.setAuthenticationStartTime(objToDate(objects.get(CommonConstant.INT_FORTY_ONE)));
            //认证结束时间
            vehicleBasicInfoListRequestDto.setAuthenticationExpireTime(objToDate(objects.get(CommonConstant.INT_FORTY_TWO)));

            //是否安装GPS为否 GPS为非必填
            if(CommonConstant.ONE.equals(vehicleBasicInfoListRequestDto.getIfInstallGps())){
                //GPS安装日期 终端型号 GPS终端SIM卡号 GPS服务商名称
                vehicleBasicInfoListRequestDto.setInstallTime(objToDate(objects.get(CommonConstant.INT_FORTY_THREE)));
                vehicleBasicInfoListRequestDto.setTerminalType(objToString(objects.get(CommonConstant.INT_FORTY_FOUR)));
                vehicleBasicInfoListRequestDto.setSimNumber(objToString(objects.get(CommonConstant.INT_FORTY_FIVE)));
                vehicleBasicInfoListRequestDto.setGpsServiceProvider(objToString(objects.get(CommonConstant.INT_FORTY_SIX)));
            }

            //登记证书编号
            vehicleBasicInfoListRequestDto.setRegistrationCertificationNumber(objToString(objects.get(CommonConstant.INT_FORTY_SEVEN)));

            //等级评定检查日期
            vehicleBasicInfoListRequestDto.setEstimationDate(objToDate(objects.get(CommonConstant.INT_FORTY_EIGHT)));

            //排放标准
            String emissionStandard = objToString(objects.get(CommonConstant.INT_FORTY_NIGHT));
            vehicleBasicInfoListRequestDto.setEmissionStandardType(ConverterUtils.toString(EmissionStandardTypeEnum.getEnumByValue(emissionStandard).getKey()));

            setContains.add(vehicleBasicInfoListRequestDto);
        }

        if(setContains!=null&&!setContains.isEmpty()){
            for (ImportVehicleBasicInfoListRequestDto tempDto: setContains) {
                importList.add(tempDto);
            }
        }
        requestDto.setImportList(importList);
        requestDto.setNumberFailures(objToString(excelList.size() - setContains.size()));
        return requestDto;
    }

    /**
     * 校验车辆资产新增修改请求数据信息
     * @param requestDto
     * @return 返回校验信息
     */
    public String checkVehicleBasicInfo(AddOrModifyVehicleBasicInfoRequestDto requestDto){

        //车辆机构 自有 外包
        if(StringUtils.isBlank(requestDto.getVehicleProperty())){
            return "车辆机构必填";
        }
        //使用性质
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())&&StringUtils.isBlank(requestDto.getUsageProperty())){
                return "使用性质必填";
        }
        //是否安装GPS
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())&&StringUtils.isBlank(requestDto.getIfInstallGps())){
                return "是否安装GPS必填";
        }
        //是否入网中石化
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())&&StringUtils.isBlank(requestDto.getIfAccessSinopec())){
                return "是否入网中石化必填";
        }
        //真实所属车主
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())&&StringUtils.isBlank(requestDto.getVehicleOwner())){
                return "真实所属车主必填";
        }
        //车牌号(外包车辆的必填项校验)
        if(!FrequentMethodUtils.validateVehicleFormat(requestDto.getVehicleNo())){
            return "车牌号有校验有误";
        }
        //车辆类型(外包车辆的必填项校验)
        if(StringUtils.isBlank(requestDto.getVehicleType())){
            return "车辆类型必填";
        }
        //校验可装载托盘数
        if (StringUtils.isNotBlank(requestDto.getLoadingCapacity()) && !FrequentMethodUtils.integerNum(requestDto.getLoadingCapacity())) {
            return "可装托盘数范围0至5000的整数";
        }
        //所有人
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())
                && StringUtils.isBlank(requestDto.getOwner()) ){
            return "车辆所有人必填";
        }
        //住址
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())
                && StringUtils.isBlank(requestDto.getAddress()) ){
            return "住址必填";
        }
        //品牌
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())){
            if(StringUtils.isBlank(requestDto.getBrand())
                    || requestDto.getBrand().length() < CommonConstant.INTEGER_TWO
                    || requestDto.getBrand().length() > CommonConstant.INT_FIFTY ){
                return "品牌有误";
            }
        }else{
            if((StringUtils.isNotBlank(requestDto.getBrand())
                    && (requestDto.getBrand().length() < CommonConstant.INTEGER_TWO) || requestDto.getBrand().length() > CommonConstant.INT_FIFTY)){
                return "品牌有误";
            }
        }
        //型号
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())){
            if(StringUtils.isBlank(requestDto.getModel())
                    || requestDto.getModel().length() < CommonConstant.INTEGER_TWO
                    || requestDto.getModel().length() > CommonConstant.INT_FIFTY){
                return "型号有误";
            }
        }else{
            if(StringUtils.isNotBlank(requestDto.getModel())
                    && (requestDto.getModel().length() < CommonConstant.INTEGER_TWO || requestDto.getModel().length() > CommonConstant.INT_FIFTY )){
                return "型号有误";
            }
        }
        //车辆识别号
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())){
            if(StringUtils.isBlank(requestDto.getVehicleIdentificationNumber())
                    || !FrequentMethodUtils.checkReg(requestDto.getVehicleIdentificationNumber(),REG_ONE)){
                return "车辆识别号必填，英文大写字母+数字，10-30个字符";
            }
        }else{
            if(StringUtils.isNotBlank(requestDto.getVehicleIdentificationNumber())
                    && !FrequentMethodUtils.checkReg(requestDto.getVehicleIdentificationNumber(),REG_ONE)){
                return "车辆识别号格式，英文大写字母+数字，10-30个字符";
            }
        }
        //发动机号码
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())){
            String vehicleTypeLabel = Optional.ofNullable(requestDto.getVehicleTypeLabel()).orElse("");
            if((vehicleTypeLabel.contains("牵引车")
                    || vehicleTypeLabel.contains("厢"))&&(StringUtils.isBlank(requestDto.getEngineNumber())
                    || !FrequentMethodUtils.checkReg(requestDto.getEngineNumber(),REG_ONE))){
                    return "发动机号码必填,牵引车校验必填，英文大写字母/数字，1-30个字符";
            }

        }
        if(StringUtils.isNotBlank(requestDto.getEngineNumber())
                && !FrequentMethodUtils.checkReg(requestDto.getEngineNumber(),REG_ONE)){
            return "发动机号码格式,英文大写字母/数字，1-30个字符";
        }

        //发证部门
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())){
            if(StringUtils.isBlank(requestDto.getDrivingCertificationDepartmentOne())
                    || !FrequentMethodUtils.checkReg(requestDto.getDrivingCertificationDepartmentOne(),REG_TWO)){
                return "发证部门必填，2-50中文字符";
            }
        }else{
            if(StringUtils.isNotBlank(requestDto.getDrivingCertificationDepartmentOne())
                    && !FrequentMethodUtils.checkReg(requestDto.getDrivingCertificationDepartmentOne(),REG_TWO)){
                return "发证部门中文字符限制";
            }
        }

        //注册日期
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())){
            if(StringUtils.isBlank(requestDto.getRegistrationDate())){
                return "注册日期必填";
            }

            try{
                Date pageRegistrationDate = DateUtils.stringToDate(requestDto.getRegistrationDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN);
                if(pageRegistrationDate.after(new Date())){
                    return "注册日期,未来的时间不允许选择";
                }
            }catch (Exception e){
                return "注册日期格式有误";
            }

        }

        //发证日期
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())){
            if(StringUtils.isBlank(requestDto.getDrivingIssueDate())){
                return "发证日期必填";
            }

            try{
                Date pageDrivingIssueDate = DateUtils.stringToDate(requestDto.getDrivingIssueDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN);
                if(pageDrivingIssueDate.after(new Date())){
                    return "注册日期,未来的时间不允许选择";
                }
            }catch (Exception e){
                return "发证日期格式有误";
            }
        }

        //归档编号
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())){
            if(StringUtils.isBlank(requestDto.getFilingNumber())
                    || !FrequentMethodUtils.checkReg(requestDto.getFilingNumber(),REG_FOUR)){
                return "归档编号必填，2-50字符，数字，大写英文限制";
            }
        }else{
            if(StringUtils.isNotBlank(requestDto.getFilingNumber())
                    && !FrequentMethodUtils.checkReg(requestDto.getFilingNumber(),REG_FOUR)){
                return "归档编号格式，数字，大写英文限制";
            }
        }
        //核定载人数
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())){
            String vehicleTypeLabel = Optional.ofNullable(requestDto.getVehicleTypeLabel()).orElse("");
            if((vehicleTypeLabel.contains("牵引车") || vehicleTypeLabel.contains("厢"))&&
                    (StringUtils.isBlank(requestDto.getAuthorizedCarryingCapacity())
                            || !FrequentMethodUtils.checkReg(requestDto.getAuthorizedCarryingCapacity(),REG_FIVE))){
                    return "核定载人数必填，限制1≤人数≤8";
            }
        }
        if(StringUtils.isNotBlank(requestDto.getAuthorizedCarryingCapacity())
                && !FrequentMethodUtils.checkReg(requestDto.getAuthorizedCarryingCapacity(),REG_FIVE)){
            return "核定载人数允许填写整数，限制1≤人数≤8";
        }

        //总质量
        if(StringUtils.isNotBlank(requestDto.getTotalWeight())
                && !FrequentMethodUtils.checkReg(requestDto.getTotalWeight(),REG_SIX)){
            return "总质量只允许填写数字，不允许填写小数，1＜数≤100000";
        }

        //整备质量
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())){
            if(StringUtils.isBlank(requestDto.getCurbWeight())
                    || !FrequentMethodUtils.checkReg(requestDto.getCurbWeight(),REG_SIX)){
                return "整备质量必填，只允许填写数字，不允许填写小数，1＜数≤100000";
            }
        }else{
            if(StringUtils.isNotBlank(requestDto.getCurbWeight())
                    && !FrequentMethodUtils.checkReg(requestDto.getCurbWeight(),REG_SIX)){
                return "整备质量只允许填写数字，不允许填写小数，1＜数≤100000";
            }
        }
        //核定载质量
        if(StringUtils.isNotBlank(requestDto.getApprovedLoadWeight()) && !FrequentMethodUtils.checkReg(requestDto.getApprovedLoadWeight(),REG_SIX)){
            return "核定载质量只允许填写数字，不允许填写小数，1＜数≤100000";
        }
        //牵引总质量
        if(StringUtils.isNotBlank(requestDto.getTractionMassWeight()) && !FrequentMethodUtils.checkReg(requestDto.getTractionMassWeight(),REG_SIX)){
            return "牵引总质量只允许填写数字，不允许填写小数，1＜数≤100000";
        }
        //长 (外包车辆的必填项校验)
        if(StringUtils.isBlank(requestDto.getLength())
                || !FrequentMethodUtils.checkReg(requestDto.getLength(),REG_SIX)){
            return "车辆长必填,只允许填写数字，不允许填写小数，1＜尺寸≤100000";
        }
        //宽 (外包车辆的必填项校验)
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())){
            if(StringUtils.isBlank(requestDto.getWidth())
                    || !FrequentMethodUtils.checkReg(requestDto.getWidth(),REG_SIX)){
                return "车辆宽必填，只允许填写数字，不允许填写小数，1＜尺寸≤100000";
            }
        }else{
            if(StringUtils.isNotBlank(requestDto.getWidth())
                    && !FrequentMethodUtils.checkReg(requestDto.getWidth(),REG_SIX)){
                return "车辆宽只允许填写数字，不允许填写小数，1＜尺寸≤100000";
            }
        }

        //高 (外包车辆的必填项校验)
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())){
            if(StringUtils.isBlank(requestDto.getHeight())
                    || !FrequentMethodUtils.checkReg(requestDto.getHeight(),REG_SIX)){
                return "车辆高必填，只允许填写数字，不允许填写小数，1＜尺寸≤100000";
            }
        }else{
            if(StringUtils.isNotBlank(requestDto.getHeight())
                    && !FrequentMethodUtils.checkReg(requestDto.getHeight(),REG_SIX)){
                return "车辆高只允许填写数字，不允许填写小数，1＜尺寸≤100000";
            }
        }

        //车辆强制报废期
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())&&StringUtils.isBlank(requestDto.getObsolescenceDate())){
                return "车辆强制报废期必填";
        }
        //车辆轴数
        if(StringUtils.isNotBlank(requestDto.getAxleNumber())
                && !FrequentMethodUtils.checkReg(requestDto.getAxleNumber(),REG_SEVEN)){
            return "车辆轴数只允许填写整数，1≤数≤10";
        }
        //驱动轴数
        if(StringUtils.isNotBlank(requestDto.getDriveShaftNumber())
                && !FrequentMethodUtils.checkReg(requestDto.getDriveShaftNumber(),REG_SEVEN)){
            return "驱动轴数只允许填写整数，1≤数≤10";
        }

        //轮胎数
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())){
            if(StringUtils.isBlank(requestDto.getTiresNumber())
                    || !FrequentMethodUtils.checkReg(requestDto.getTiresNumber(),REG_TEN)){
                return "轮胎数必填，只允许填写整数，1≤数≤20";
            }
        }else{
            if(StringUtils.isNotBlank(requestDto.getTiresNumber())
                    && !FrequentMethodUtils.checkReg(requestDto.getTiresNumber(),REG_TEN)){
                return "轮胎数只允许填写整数";
            }
        }
        //车牌颜色(外包车辆的必填项校验)
        if(StringUtils.isBlank(requestDto.getPlateColor())){
            return "车牌颜色必填";
        }

        //车身颜色
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())){
            if(StringUtils.isBlank(requestDto.getBodyColor())
                    ||!FrequentMethodUtils.checkReg(requestDto.getBodyColor().trim(),REG_EIGHT)){
                return "车身颜色必填,1≤中文字符≤10";
            }
        }else{
            if(StringUtils.isNotBlank(requestDto.getBodyColor())
                    && !FrequentMethodUtils.checkReg(requestDto.getBodyColor().trim(),REG_EIGHT)){
                return "只允许维护中文，1≤中文字符≤10";
            }
        }
        //发证签
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())){
            if(StringUtils.isBlank(requestDto.getCertificationSign())
                    || !FrequentMethodUtils.checkReg(requestDto.getCertificationSign(),REG_NINE)){
                return  "发证签必填,2-50字符，数字,中文限制";
            }
        }else{
            if(StringUtils.isNotBlank(requestDto.getCertificationSign())
                    && !FrequentMethodUtils.checkReg(requestDto.getCertificationSign(),REG_NINE)){
                return "发证签,2-50字符，数字,中文限制";
            }
        }
        //经营许可证
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())){
            if(StringUtils.isBlank(requestDto.getBusinessLicenseNumber())
                    || !FrequentMethodUtils.checkReg(requestDto.getBusinessLicenseNumber(),REG_NINE)){
                return "经营许可证必填，只允许填写数字，中文";
            }
        }else{
            if(StringUtils.isNotBlank(requestDto.getBusinessLicenseNumber())
                    && !FrequentMethodUtils.checkReg(requestDto.getBusinessLicenseNumber(),REG_NINE)){
                return "只允许填写数字，中文";
            }
        }
        //经济类型
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())&&StringUtils.isBlank(requestDto.getEconomicType())){
                return "经济类型必填";
        }
        //吨位 (自有车辆的必填项校验,外包车辆非必填)
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())){
            if(StringUtils.isBlank(requestDto.getTransportTonnage())
                    || !FrequentMethodUtils.checkReg(requestDto.getTransportTonnage(),REG_TRANSPORT_TONNAGE)){
                return "自用车辆吨位必填";
            }
        }else{
            if(StringUtils.isNotBlank(requestDto.getTransportTonnage())
                    && !FrequentMethodUtils.checkReg(requestDto.getTransportTonnage(),REG_TRANSPORT_TONNAGE)){
                return "吨（坐）位,填写数字";
            }
        }

        //经营范围
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())&&StringUtils.isBlank(requestDto.getBusinessScope())){
                return "经营范围必填";
        }
        //道路运输证发证部门
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())&&StringUtils.isBlank(requestDto.getRoadTransportCertificationDepartment())){
                return "道路运输证发证部门必填" ;
        }
        //道路运输证发证日期,
        if(CommonConstant.ONE.equals(requestDto.getVehicleProperty())&&StringUtils.isBlank(requestDto.getIssueDate())){
                return "道路运输证发证日期必填";
        }

        //是否安装GPS为否 GPS为非必填
        if(CommonConstant.ONE.equals(requestDto.getIfInstallGps())
                && CommonConstant.ONE.equals(requestDto.getVehicleProperty())
                &&ListUtils.isEmpty(requestDto.getVehicleGpsRecordList())){
                return "GPS信息为空";
        }

        return "";
    }

    private String objToString(Object obj){
        if(obj instanceof BigDecimal){
            BigDecimal decimal = (BigDecimal)obj;
            return  ConverterUtils.toString(decimal.stripTrailingZeros().toPlainString());
        }else{
            return ConverterUtils.toString(obj);
        }

    }

    /**
     * 格式化成 "2019/10/10" -> "2019-10-10"
     * @return
     */
    private String objToDate(Object obj){
        Date date = MapperUtils.mapperNoDefault(obj,Date.class);
        return date==null?null:DateUtils.dateToString(date,DateUtils.DATE_TO_STRING_DETAIAL_PATTERN);
    }

}
