package com.logistics.appapi.client.thirdparty.basicdata.file.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author:lei.zhu
 * @date:2021/10/25 16:06:16
 */
@Data
public class BatchGetOSSFileUrlRequestModel {

    @ApiModelProperty("类型：1.临时/正式图片 2.ocr识别数据转excel (默认为1)")
    private Integer type = 1;

    @ApiModelProperty("文件路径列表")
    private List<String> fileSrcList;

}
