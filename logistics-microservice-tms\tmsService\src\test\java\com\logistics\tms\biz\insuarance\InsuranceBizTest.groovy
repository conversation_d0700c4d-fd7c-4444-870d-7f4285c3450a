package com.logistics.tms.biz.insuarance

import com.yelo.tray.core.exception.BizException
import com.logistics.tms.api.feign.insuarance.model.*
import com.logistics.tms.api.feign.personalaccidentinsurance.model.GetPersonInsurancePersonCountByIdResponseModel
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleSettlementRelationByInsuranceIdsBaseModel
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleSettlementRelationByInsuranceIdsModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.*
import com.logistics.tms.mapper.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class InsuranceBizTest extends Specification {
    @Mock
    TInsuranceMapper insuranceMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TCertificationPicturesMapper certificationPicturesMapper
    @Mock
    TInsuranceCompanyMapper insuranceCompanyMapper
    @Mock
    TVehicleDrivingLicenseMapper vehicleDrivingLicenseMapper
    @Mock
    TStaffBasicMapper staffBasicMapper
    @Mock
    TVehicleBasicMapper vehicleBasicMapper
    @Mock
    TPersonalAccidentInsuranceMapper personalAccidentInsuranceMapper
    @Mock
    TInsuranceCostsRelationMapper insuranceCostsRelationMapper
    @Mock
    TInsuranceCostsMapper insuranceCostsMapper
    @Mock
    TVehicleSettlementMapper tVehicleSettlementMapper
    @Mock
    TVehicleSettlementRelationMapper tVehicleSettlementRelationMapper
    @Mock
    TInsuranceRefundMapper insuranceRefundMapper
    @InjectMocks
    InsuranceBiz insuranceBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Insurance List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(insuranceMapper.searchInsuranceList(any())).thenReturn([new SearchInsuranceListResponseModel()])
        when(insuranceMapper.getAllCount()).thenReturn(1)
        when(insuranceMapper.getSettlementCostsRelation(anyString())).thenReturn(null)
        when(tVehicleSettlementRelationMapper.getByInsuranceIds(anyString())).thenReturn(null)

        expect:
        insuranceBiz.searchInsuranceList(requestModel).getList().size() == expectedResult

        where:
        requestModel                          || expectedResult
        new SearchInsuranceListRequestModel() || 1
    }

    @Unroll
    def "get Insurance Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given: "准备数据"
        //mock sql查到的保险数据
        when(insuranceMapper.getInsuranceDetail(anyLong())).thenReturn(mapperResult)

        when: "执行方法"
        insuranceBiz.getInsuranceDetail(requestModel)

        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where: "批量验证"
        requestModel                                 || mapperResult || expectedResult
        new InsuranceIdRequestModel(insuranceId: -1) || null         || "保险不存在"
    }

    @Unroll
    def "add Or Modify Insurance where requestModel=#requestModel"() {
        given:

        when:
        insuranceBiz.addOrModifyInsurance(requestModel)

        then: "验证"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where:
        requestModel                                                                                                                                                                             || expectedResult
        new AddOrModifyInsuranceRequestModel(insuranceType: 1, startTime: new GregorianCalendar(2022, 06, 17, 15, 30).getTime(), endTime: new GregorianCalendar(2022, 06, 16, 15, 30).getTime()) || "保险生效时间不能大于保险截止时间"
        new AddOrModifyInsuranceRequestModel(insuranceType: 3, endTime: new GregorianCalendar(2022, 4, 16, 15, 30).getTime())                                                                    || "保险截止时间不能小于当前时间"
    }

    @Unroll
    def "cancel Insurance where requestModel=#requestModel"() {
        given:
        when(insuranceMapper.getInsuranceDetail(any())).thenReturn(insuranceDetail)
        when(tVehicleSettlementMapper.getVehicleSettlementByIds(anyString())).thenReturn(vehicleSettlementByIds)
        when(tVehicleSettlementRelationMapper.getByInsuranceIds(anyString())).thenReturn(byInsuranceIds)

        when:
        insuranceBiz.cancelInsurance(requestModel)

        then:
        def e = thrown(BizException)
        with(e) {
            message == messageResult
            println(message)
        }

        where:
        requestModel                      || insuranceDetail                                                                                                                           || vehicleSettlementByIds              || byInsuranceIds                                                                                                                                                          || messageResult
        new CancelInsuranceRequestModel() || null                                                                                                                                      || [new TVehicleSettlement(status: 0)] || [new GetVehicleSettlementRelationByInsuranceIdsModel()]                                                                                                                 || "保险不存在"
        new CancelInsuranceRequestModel() || new GetInsuranceDetailResponseModel(statusType: 1)                                                                                        || [new TVehicleSettlement(status: 0)] || [new GetVehicleSettlementRelationByInsuranceIdsModel()]                                                                                                                 || "保险已作废，不能进行该操作"
        new CancelInsuranceRequestModel() || new GetInsuranceDetailResponseModel(statusType: 2)                                                                                        || [new TVehicleSettlement(status: 0)] || [new GetVehicleSettlementRelationByInsuranceIdsModel()]                                                                                                                 || "保险已退保，不能进行该操作"
        new CancelInsuranceRequestModel() || new GetInsuranceDetailResponseModel(statusType: 0, insuranceType: 3, endTimePerson: new GregorianCalendar(2022, 4, 16, 15, 30).getTime()) || [new TVehicleSettlement(status: 0)] || [new GetVehicleSettlementRelationByInsuranceIdsModel()]                                                                                                                 || "保险已过期，不能进行该操作"
        new CancelInsuranceRequestModel() || new GetInsuranceDetailResponseModel(statusType: 0, insuranceType: 1, endTime: new GregorianCalendar(2022, 4, 16, 15, 30).getTime())       || [new TVehicleSettlement(status: 0)] || [new GetVehicleSettlementRelationByInsuranceIdsModel()]                                                                                                                 || "保险已过期，不能进行该操作"
        new CancelInsuranceRequestModel() || new GetInsuranceDetailResponseModel(statusType: 0, insuranceType: 1, endTime: new GregorianCalendar(2024, 4, 16, 15, 30).getTime())       || [new TVehicleSettlement(status: 0)] || [new GetVehicleSettlementRelationByInsuranceIdsModel(vehicleSettlementRelationList: [new GetVehicleSettlementRelationByInsuranceIdsBaseModel(vehicleSettlementId: 1)])] || "当前保险有待确认账单，请先完成对账"
    }

    @Unroll
    def "import Insurance where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(insuranceMapper.getByTypeVehicleIdOrDriverId(any())).thenReturn([new TInsurance(insuranceType: 0, vehicleId: 1l, driverId: 1l, insuranceCompanyId: 1l, policyNo: "policyNo", premium: 0 as BigDecimal, unpaidPremium: 0 as BigDecimal, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 19).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 19).getTime(), paymentOfVehicleAndVesselTax: 0 as BigDecimal, personalAccidentInsuranceId: 1l, source: 0)])
        when(insuranceMapper.batchInsert(any())).thenReturn(0)
        when(insuranceCompanyMapper.findInsuranceCompanyByName(anyString())).thenReturn(new TInsuranceCompany())
        when(vehicleDrivingLicenseMapper.getByVehicleNo(anyString())).thenReturn(new TVehicleDrivingLicense(vehicleId: 1l))
        when(staffBasicMapper.getByMobile(anyString())).thenReturn(new TStaffBasic(type: 0))
        when(personalAccidentInsuranceMapper.getByTypePolicyNumber(anyInt(), anyString(), anyString())).thenReturn(new TPersonalAccidentInsurance())
        when(personalAccidentInsuranceMapper.getPersonInsurancePersonCountById(anyLong())).thenReturn(new GetPersonInsurancePersonCountByIdResponseModel())

        expect:
        insuranceBiz.importInsurance(requestModel) == expectedResult

        where:
        requestModel                      || expectedResult
        new ImportInsuranceRequestModel() || new ImportInsuranceResponseModel()
    }

    @Unroll
    def "import Insurance Certificate Info where requestModel=#requestModel"() {
        given:
        when(insuranceMapper.getByVehicleIdAndTime(anyInt(), anyLong(), anyInt(), anyInt())).thenReturn([new TInsurance()])
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(certificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 19).getTime(), suffix: "suffix")])
        when(vehicleBasicMapper.getInfoByVehicleNo(anyString())).thenReturn(new TVehicleBasic())

        expect:
        insuranceBiz.importInsuranceCertificateInfo(requestModel)
        assert expectedResult == false

        where:
        requestModel                                 || expectedResult
        new ImportInsuranceCertificateRequestModel() || true
    }

    @Unroll
    def "get Insurance Info By Vehicle Id where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(insuranceMapper.getByVehicleIdAndPeriod(anyLong(), any())).thenReturn([new TInsurance(insuranceType: 0, policyNo: "policyNo", premium: 0 as BigDecimal, unpaidPremium: 0 as BigDecimal, paymentOfVehicleAndVesselTax: 0 as BigDecimal)])

        expect:
        insuranceBiz.getInsuranceInfoByVehicleId(requestModel) == expectedResult

        where:
        requestModel                                  || expectedResult
        new GetInsuranceInfoByVehicleIdRequestModel() || new GetInsuranceInfoByVehicleIdResponseModel()
    }

    @Unroll
    def "confirm Refund where requestModel=#requestModel"() {
        given:
        when(insuranceMapper.getByIds(anyString())).thenReturn([new TInsurance(statusType: 0, cancelReason: "cancelReason", premium: 0 as BigDecimal, unpaidPremium: 0 as BigDecimal, refundPremium: 0 as BigDecimal, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 19).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 19).getTime(), remark: "remark", paymentOfVehicleAndVesselTax: 0 as BigDecimal)])
        when(insuranceMapper.batchUpdate(any())).thenReturn(0)
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(vehicleBasicMapper.getVehicleBasicPropertyById(anyLong())).thenReturn(new VehicleBasicPropertyModel())
        when(insuranceCostsRelationMapper.getWaitSettlementByInsuranceIds(anyString())).thenReturn([new com.logistics.tms.entity.TInsuranceCostsRelation()])
        when(insuranceRefundMapper.batchInsert(any())).thenReturn(0)

        when:
        insuranceBiz.confirmRefund(requestModel)

        then:
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where:
        requestModel                                                                     || expectedResult
        new ConfirmRefundRequestModel(insuranceRefundList: [])                           || "请选择退保保险"
        new ConfirmRefundRequestModel(insuranceRefundList: [new InsuranceRefundModel()]) || "请上传退保保险证明"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme