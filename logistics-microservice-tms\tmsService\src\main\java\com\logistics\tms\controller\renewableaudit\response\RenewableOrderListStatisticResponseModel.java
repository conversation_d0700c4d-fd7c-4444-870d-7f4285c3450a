package com.logistics.tms.controller.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class RenewableOrderListStatisticResponseModel {

	@ApiModelProperty("全部")
	private Integer allCount = 0;

	@ApiModelProperty("待确定")
	private Integer waitConfirm = 0;

	@ApiModelProperty("待审核")
	private Integer waitAudit = 0;

	@ApiModelProperty("已审核")
	private Integer audited = 0;
}
