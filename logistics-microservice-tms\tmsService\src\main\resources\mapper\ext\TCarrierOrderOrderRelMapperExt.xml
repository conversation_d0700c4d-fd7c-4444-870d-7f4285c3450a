<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderOrderRelMapper" >
    <insert id="batchInsertSelective" parameterType="com.logistics.tms.entity.TCarrierOrderOrderRel" >
        <foreach collection="list" item="item" separator=";">
            insert into t_carrier_order_order_rel
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    id,
                </if>
                <if test="item.carrierOrderId != null" >
                    carrier_order_id,
                </if>
                <if test="item.demandOrderOrderId != null" >
                    demand_order_order_id,
                </if>
                <if test="item.orderId != null" >
                    order_id,
                </if>
                <if test="item.orderCode != null" >
                    order_code,
                </if>
                <if test="item.expectAmount != null" >
                    expect_amount,
                </if>
                <if test="item.loadAmount != null" >
                    load_amount,
                </if>
                <if test="item.unloadAmount != null" >
                    unload_amount,
                </if>
                <if test="item.signAmount != null" >
                    sign_amount,
                </if>
                <if test="item.relType != null" >
                    rel_type,
                </if>
                <if test="item.remark != null and item.remark != ''" >
                    remark,
                </if>
                <if test="item.createdBy != null" >
                    created_by,
                </if>
                <if test="item.createdTime != null" >
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="item.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.carrierOrderId != null" >
                    #{item.carrierOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.demandOrderOrderId != null" >
                    #{item.demandOrderOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.orderId != null" >
                    #{item.orderId,jdbcType=BIGINT},
                </if>
                <if test="item.orderCode != null" >
                    #{item.orderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.expectAmount != null" >
                    #{item.expectAmount,jdbcType=INTEGER},
                </if>
                <if test="item.loadAmount != null" >
                    #{item.loadAmount,jdbcType=INTEGER},
                </if>
                <if test="item.unloadAmount != null" >
                    #{item.unloadAmount,jdbcType=INTEGER},
                </if>
                <if test="item.signAmount != null" >
                    #{item.signAmount,jdbcType=INTEGER},
                </if>
                <if test="item.relType != null" >
                   #{item.relType,jdbcType=INTEGER},
                </if>
                <if test="item.remark != null and item.remark != ''" >
                    #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null" >
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <select id="selectCarrierOrderRelsByCarrierOrderIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_carrier_order_order_rel
        where valid = 1 and carrier_order_id in (${carrierOrderIds})
        order by id
    </select>

    <update id="batchUpdateSelective" >
        <foreach collection="list" item="item" separator=";">
            update t_carrier_order_order_rel
            <set >
                <if test="item.carrierOrderId != null" >
                    carrier_order_id = #{item.carrierOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.demandOrderOrderId != null" >
                    demand_order_order_id = #{item.demandOrderOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.orderId != null" >
                    order_id = #{item.orderId,jdbcType=BIGINT},
                </if>
                <if test="item.orderCode != null" >
                    order_code = #{item.orderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.expectAmount != null" >
                    expect_amount = #{item.expectAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.loadAmount != null" >
                    load_amount = #{item.loadAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.unloadAmount != null" >
                    unload_amount = #{item.unloadAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.signAmount != null" >
                    sign_amount = #{item.signAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.relType != null" >
                    rel_type = #{item.relType,jdbcType=INTEGER},
                </if>
                <if test="item.remark != null and item.remark != ''" >
                    remark =#{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT} and valid = 1
        </foreach>
    </update>

    <select id="getCarrierOrderOrdersByCarrierCode"
            resultType="com.logistics.tms.controller.carrierorder.response.CarrierOrderOrdersResponseModel">
        select
        order_code    as orderCode,
        expect_amount as totalAmount,
        rel_type      as relType,
        remark        as remark,
        created_by    as createdBy,
        created_time  as createdTime
        from t_carrier_order_order_rel
        where valid = 1
          and carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
        order by created_time desc,id desc
    </select>
</mapper>