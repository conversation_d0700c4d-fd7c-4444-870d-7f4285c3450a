package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author:lei.zhu
 * @date:2021/12/23 10:04:49
 */
@Data
public class TmsSyncAdditionalOrderModel {
    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("同步类型  1 追加  2 补单")
    private Integer type;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("下单人名字")
    private String publishName;

    @ApiModelProperty("H单下所有R单信息（包含此次追加/补单的R单）")
    private List<AdditionalOrderModel> additionalOrderModelList;
}
