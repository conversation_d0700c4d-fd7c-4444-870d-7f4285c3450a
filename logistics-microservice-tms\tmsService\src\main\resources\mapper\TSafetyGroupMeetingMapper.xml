<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TSafetyGroupMeetingMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TSafetyGroupMeeting" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="meeting_year" property="meetingYear" jdbcType="VARCHAR" />
    <result column="meeting_season" property="meetingSeason" jdbcType="INTEGER" />
    <result column="meeting_title" property="meetingTitle" jdbcType="VARCHAR" />
    <result column="meeting_time" property="meetingTime" jdbcType="TIMESTAMP" />
    <result column="meeting_place" property="meetingPlace" jdbcType="VARCHAR" />
    <result column="anchor" property="anchor" jdbcType="VARCHAR" />
    <result column="record_person" property="recordPerson" jdbcType="VARCHAR" />
    <result column="content" property="content" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, meeting_year, meeting_season, meeting_title, meeting_time, meeting_place, anchor, 
    record_person, content, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_safety_group_meeting
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_safety_group_meeting
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TSafetyGroupMeeting" >
    insert into t_safety_group_meeting (id, meeting_year, meeting_season, 
      meeting_title, meeting_time, meeting_place, 
      anchor, record_person, content, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{meetingYear,jdbcType=VARCHAR}, #{meetingSeason,jdbcType=INTEGER}, 
      #{meetingTitle,jdbcType=VARCHAR}, #{meetingTime,jdbcType=TIMESTAMP}, #{meetingPlace,jdbcType=VARCHAR}, 
      #{anchor,jdbcType=VARCHAR}, #{recordPerson,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TSafetyGroupMeeting" keyProperty="id" useGeneratedKeys="true" >
    insert into t_safety_group_meeting
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="meetingYear != null" >
        meeting_year,
      </if>
      <if test="meetingSeason != null" >
        meeting_season,
      </if>
      <if test="meetingTitle != null" >
        meeting_title,
      </if>
      <if test="meetingTime != null" >
        meeting_time,
      </if>
      <if test="meetingPlace != null" >
        meeting_place,
      </if>
      <if test="anchor != null" >
        anchor,
      </if>
      <if test="recordPerson != null" >
        record_person,
      </if>
      <if test="content != null" >
        content,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="meetingYear != null" >
        #{meetingYear,jdbcType=VARCHAR},
      </if>
      <if test="meetingSeason != null" >
        #{meetingSeason,jdbcType=INTEGER},
      </if>
      <if test="meetingTitle != null" >
        #{meetingTitle,jdbcType=VARCHAR},
      </if>
      <if test="meetingTime != null" >
        #{meetingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="meetingPlace != null" >
        #{meetingPlace,jdbcType=VARCHAR},
      </if>
      <if test="anchor != null" >
        #{anchor,jdbcType=VARCHAR},
      </if>
      <if test="recordPerson != null" >
        #{recordPerson,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TSafetyGroupMeeting" >
    update t_safety_group_meeting
    <set >
      <if test="meetingYear != null" >
        meeting_year = #{meetingYear,jdbcType=VARCHAR},
      </if>
      <if test="meetingSeason != null" >
        meeting_season = #{meetingSeason,jdbcType=INTEGER},
      </if>
      <if test="meetingTitle != null" >
        meeting_title = #{meetingTitle,jdbcType=VARCHAR},
      </if>
      <if test="meetingTime != null" >
        meeting_time = #{meetingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="meetingPlace != null" >
        meeting_place = #{meetingPlace,jdbcType=VARCHAR},
      </if>
      <if test="anchor != null" >
        anchor = #{anchor,jdbcType=VARCHAR},
      </if>
      <if test="recordPerson != null" >
        record_person = #{recordPerson,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TSafetyGroupMeeting" >
    update t_safety_group_meeting
    set meeting_year = #{meetingYear,jdbcType=VARCHAR},
      meeting_season = #{meetingSeason,jdbcType=INTEGER},
      meeting_title = #{meetingTitle,jdbcType=VARCHAR},
      meeting_time = #{meetingTime,jdbcType=TIMESTAMP},
      meeting_place = #{meetingPlace,jdbcType=VARCHAR},
      anchor = #{anchor,jdbcType=VARCHAR},
      record_person = #{recordPerson,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>