<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierDriverRelationMapper" >
    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TCarrierDriverRelation">
        <foreach collection="list" item="item" separator=";">
            insert into t_carrier_driver_relation
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.companyCarrierId != null">
                    company_carrier_id,
                </if>
                <if test="item.driverId != null">
                    driver_id,
                </if>
                <if test="item.enabled != null">
                    enabled,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.companyCarrierId != null">
                    #{item.companyCarrierId,jdbcType=BIGINT},
                </if>
                <if test="item.driverId != null">
                    #{item.driverId,jdbcType=BIGINT},
                </if>
                <if test="item.enabled != null">
                    #{item.enabled,jdbcType=INTEGER},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

  <select id="getByCompanyCarrierIdAndDriverId" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from t_carrier_driver_relation
      where valid = 1
      and driver_id = #{driverId,jdbcType = BIGINT}
      and company_carrier_id = #{companyCarrierId,jdbcType = BIGINT}
      <if test="isEnable != null">
          and enabled = #{isEnable,jdbcType=INTEGER}
      </if>
      limit 1
  </select>

  <select id="searchDriver" resultType="com.logistics.tms.controller.dispatch.response.DriverSearchResponseModel">
    select
    tsb.id as driverId,
    tsb.name as driverName,
    tsb.mobile as driverPhone,
    tsb.identity_number as driverIdentityNumber
    from t_carrier_driver_relation tcdr
    left join t_staff_basic tsb on tsb.id = tcdr.driver_id and tsb.valid = 1
    where tcdr.valid = 1
    and tsb.type in (1,3)
    and tcdr.enabled = 1
    and company_carrier_id = #{params.companyCarrierId,jdbcType = BIGINT}
    <if test="params.driverNameAndPhone != null and params.driverNameAndPhone != ''">
      and (instr(tsb.name,#{params.driverNameAndPhone,jdbcType=VARCHAR}) or instr(tsb.mobile,#{params.driverNameAndPhone,jdbcType=VARCHAR}))
    </if>
  </select>

    <select id="getDriverByNameAndPhone" resultType="com.logistics.tms.controller.dispatchorder.response.DriverByNameAndPhoneResponseModel">
        select
        tsb.id              as driverId,
        tsb.name            as driverName,
        tsb.mobile          as driverPhone,
        tsb.identity_number as driverIdentityNumber
        from t_carrier_driver_relation tcdr
        left join t_staff_basic tsb on tsb.id = tcdr.driver_id and tsb.valid = 1
        where tcdr.valid = 1
        and tcdr.enabled = 1
        and company_carrier_id = #{companyCarrierId,jdbcType = BIGINT}
        <if test="driverNameAndPhone != null and driverNameAndPhone != ''">
            and (instr(tsb.name, #{driverNameAndPhone,jdbcType=VARCHAR}) or instr(tsb.mobile, #{driverNameAndPhone,jdbcType=VARCHAR}))
        </if>
    </select>

    <select id="searchCarrierDriverList"
            resultType="com.logistics.tms.controller.carrierdriverrel.response.SearchCarrierDriverListResponseModel">
        select
        tcdr.id                             as carrierDriverId,
        tcdr.enabled,
        tsb.name                            as staffName,
        tsb.gender                          as staffGender,
        tsb.age                             as staffAge,
        tsb.mobile                          as staffMobile,
        tsb.identity_number                 as identityNumber,
        tsb.real_name_authentication_status as realNameAuthenticationStatus,
        tcdr.created_time                   as createdTime,
        tcdr.created_by                     as createdBy
        from t_carrier_driver_relation tcdr
        left join t_staff_basic tsb on tsb.id = tcdr.driver_id and tsb.valid = 1
        where tcdr.valid = 1
        AND tcdr.company_carrier_id = #{requestModel.companyCarrierId,jdbcType = BIGINT}
        <if test="requestModel.nameOrPhone != null and requestModel.nameOrPhone != ''">
            and (instr(tsb.name, #{requestModel.nameOrPhone,jdbcType=VARCHAR}) or instr(tsb.mobile, #{requestModel.nameOrPhone,jdbcType=VARCHAR}))
        </if>
        <if test="requestModel.enabled != null">
            and tcdr.enabled = #{requestModel.enabled,jdbcType=INTEGER}
        </if>
        ORDER BY tcdr.last_modified_time DESC, tcdr.id DESC
    </select>

    <select id="getCompanyDriverDetailByDriverId" resultType="com.logistics.tms.controller.carrierdriverrel.response.DriveDetailResponseModel">
        select
        tcdr.id              as carrierDriverId,
        tsb.name            as staffDriverName,
        tsb.gender          as staffDriverGender,
        tsb.age             as staffDriverAge,
        tsb.mobile          as staffDriverMobile,
        tsb.identity_number as identityNumber,
        tcdr.created_time   as createdTime,
        tcdr.created_by     as createdByName
        from t_carrier_driver_relation tcdr
        left join t_staff_basic tsb on tsb.id = tcdr.driver_id and tsb.valid = 1
        where tcdr.valid = 1
        AND tcdr.id = #{staffId,jdbcType=BIGINT}
        AND tcdr.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
  </select>

    <select id="getByRelationIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_driver_relation
        where valid = 1
        and id in (${relationIds})
    </select>

    <select id="queryDriverIdsByCompanyIds" resultType="java.lang.Long">
        select driver_id
        from t_carrier_driver_relation
        where valid = 1
        and company_carrier_id in
        <foreach collection="companyIds" item="companyId" separator="," open="(" close=")">
            #{companyId}
        </foreach>
    </select>

    <select id="queryByDriverIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_driver_relation
        where valid = 1
        and driver_id in
        <foreach collection="driverIds" item="driverId" separator="," open="(" close=")">
            #{driverId}
        </foreach>
    </select>

    <resultMap id="queryDriverList_Map" type="com.logistics.tms.controller.staff.response.SearchStaffManagementListResponseModel">
        <id column="id" property="carrierDriverId" jdbcType="BIGINT"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="staffId" property="staffId" jdbcType="BIGINT"/>
        <result column="staff_property" property="staffProperty" jdbcType="INTEGER"/>
        <result column="real_name_authentication_status" property="realNameAuthenticationStatus" jdbcType="INTEGER"/>
        <result column="warehouse_switch" property="warehouseSwitch" jdbcType="INTEGER"/>
        <result column="open_status" property="openStatus" jdbcType="INTEGER"/>
        <result column="type" property="staffType" jdbcType="INTEGER"/>
        <result column="gender" property="gender" jdbcType="INTEGER"/>
        <result column="occupational_requirements_credential_no" property="occupationalRequirementsCredentialNo" jdbcType="VARCHAR"/>
        <result column="name" property="staffName" jdbcType="VARCHAR"/>
        <result column="mobile" property="staffMobile" jdbcType="VARCHAR"/>
        <result column="permitted_type" property="permittedType" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR"/>
        <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="queryDriverList" resultMap="queryDriverList_Map">
        select
        tcdr.id,
        tcdr.company_carrier_id,
        (case
        when tcdr.enabled = 1 then 1
        when tcdr.enabled = 0 then 2
        else 0 end) as open_status,
        tcdr.created_by,
        tcdr.last_modified_by,
        tcdr.last_modified_time,

        tsb.id           as staffId,
        tsb.type,
        tsb.gender,
        tsb.name,
        tsb.mobile,
        tsb.remark,
        tsb.staff_property,
        tsb.real_name_authentication_status,
        tsb.warehouse_switch,

        tsdc.occupational_requirements_credential_no,
        tsdc.permitted_type
        from t_carrier_driver_relation tcdr
        left join t_staff_basic tsb on tsb.id = tcdr.driver_id and tsb.valid = 1
        left join t_staff_driver_credential tsdc on tsdc.staff_id = tsb.id and tsdc.valid = 1
        where tcdr.valid = 1
        <if test="params.staffProperty != null">
            and tsb.staff_property = #{params.staffProperty,jdbcType = INTEGER}
        </if>
        <if test="params.realNameAuthenticationStatus != null">
            and tsb.real_name_authentication_status = #{params.realNameAuthenticationStatus,jdbcType=INTEGER}
        </if>
        <if test="params.accountSwitchStatus != null">
            and tsb.warehouse_switch = #{params.accountSwitchStatus,jdbcType=INTEGER}
        </if>
        <if test="params.staffType != null and params.staffType != ''">
            and tsb.type in (${params.staffType})
        </if>
        <if test="params.openStatus != null">
            <if test="params.openStatus == 1">
                and tcdr.enabled = 1
            </if>
            <if test="params.openStatus == 2">
                and tcdr.enabled = 0
            </if>
        </if>
        <if test="params.occupationalRequirementsCredentialNo != null and params.occupationalRequirementsCredentialNo != ''">
            and instr(tsdc.occupational_requirements_credential_no, #{params.occupationalRequirementsCredentialNo,jdbcType=VARCHAR})
        </if>
        <if test="params.staffName != null and params.staffName != ''">
            and (instr(tsb.name, #{params.staffName,jdbcType=VARCHAR}) or instr(tsb.mobile, #{params.staffName,jdbcType=VARCHAR}))
        </if>
        <if test="params.lastModifiedBy != null and params.lastModifiedBy != ''">
            and instr(tcdr.last_modified_by, #{params.lastModifiedBy,jdbcType=VARCHAR})
        </if>
        <if test="params.lastModifiedTimeStart != null and params.lastModifiedTimeStart != ''">
            and tcdr.last_modified_time &gt;= DATE_FORMAT(#{params.lastModifiedTimeStart,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%S')
        </if>
        <if test="params.lastModifiedTimeEnd != null and params.lastModifiedTimeEnd != ''">
            and tcdr.last_modified_time &lt;= DATE_FORMAT(#{params.lastModifiedTimeEnd,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="params.staffIds != null and params.staffIds != ''">
            and tsb.id in (${params.staffIds})
        </if>
        <if test="params.ids != null and params.ids != ''">
            and tcdr.id in (${params.ids})
        </if>
        <if test="companyIds != null and companyIds.size() > 0">
            and tcdr.company_carrier_id in
            <foreach collection="companyIds" item="companyId" separator="," open="(" close=")">
                #{companyId}
            </foreach>
        </if>
        order by tcdr.last_modified_time desc, tcdr.id desc
    </select>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            update t_carrier_driver_relation
            <set>
                <if test="item.companyCarrierId != null">
                    company_carrier_id = #{item.companyCarrierId,jdbcType=BIGINT},
                </if>
                <if test="item.driverId != null">
                    driver_id = #{item.driverId,jdbcType=BIGINT},
                </if>
                <if test="item.enabled != null">
                    enabled = #{item.enabled,jdbcType=INTEGER},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectByCarrierIdAndDriverIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_driver_relation
        where valid = 1
        and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        AND driver_id in (${driverIds})
    </select>
</mapper>