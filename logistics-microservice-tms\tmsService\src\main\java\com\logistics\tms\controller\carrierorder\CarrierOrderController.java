package com.logistics.tms.controller.carrierorder;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.carrierorder.CarrierOrderBiz;
import com.logistics.tms.controller.carrierorder.request.*;
import com.logistics.tms.controller.carrierorder.response.*;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.ApiOperation;
import org.redisson.RedissonMultiLock;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/*
  前台运单管理
 */
@RestController
public class CarrierOrderController {

    @Autowired
    private CarrierOrderBiz carrierOrderBiz;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 运单列表
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/service/management/carrierOrderManagement/searchList")
    public Result<PageInfo<SearchCarrierOrderListResponseModel>> searchList(@RequestBody SearchCarrierOrderListRequestModel requestModel) {
        PageInfo<SearchCarrierOrderListResponseModel> page =  carrierOrderBiz.searchCarrierOrderListManagement(requestModel);
        return Result.success(page);
    }

    /**
     * 待审核车辆数
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/service/management/carrierOrderManagement/getWaitAuditVehicleCountInfo")
    public Result<WaitAuditVehicleInfoResponseModel> waitAuditVehicleCountInfo(@RequestBody WaitAuditVehicleInfoRequestModel requestModel) {
        return Result.success(carrierOrderBiz.waitAuditVehicleCountInfo(requestModel));
    }

    /**
     * 运单详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "运单详情")
    @PostMapping(value = "/service/management/carrierOrderManagement/detail")
    public Result<CarrierOrderDetailResponseModel> carrierOrderDetail(@RequestBody CarrierOrderDetailRequestModel requestModel) {
        return Result.success(carrierOrderBiz.getCarrierOrderDetailById(requestModel));
    }

    /**
     * 票据信息列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "票据信息列表")
    @PostMapping(value = "/service/management/carrierOrderManagement/getTickets")
    public Result<List<GetTicketsResponseModel>> getTickets(@RequestBody GetTicketsRequestModel requestModel) {
        return Result.success(carrierOrderBiz.getTicketsByCarrierOrderId(requestModel.getCarrierOrderId(), requestModel.getImageType()));
    }

    /**
     * 上传票据
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "上传票据")
    @PostMapping(value = "/service/management/carrierOrderManagement/uploadTickets")
    public Result<Boolean> uploadTickets(@RequestBody UploadTicketsRequestModel requestModel) {
        carrierOrderBiz.uploadTickets(requestModel);
        return Result.success(true);
    }

    /**
     * 前台编辑票据
     *
     * @param requestModel 票据信息
     * @return 操作结果
     */
    @ApiOperation(value = "前台编辑票据", tags = "1.2.4")
    @PostMapping(value = "/service/carrierOrderManagement/uploadTicketsForWeb")
    public Result<Boolean> uploadTicketsForWeb(@RequestBody UploadTicketsForWebRequestModel requestModel) {
        carrierOrderBiz.uploadTicketsForWeb(requestModel);
        return Result.success(true);
    }

    /**
     * 运单取消
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "运单取消")
    @PostMapping(value = "/service/management/carrierOrderManagement/cancelCarrierOrder")
    public Result<Boolean> cancelCarrierOrder(@RequestBody CancelCarrierOrderRequestModel requestModel) {
        carrierOrderBiz.cancelCarrierOrder(requestModel);
        return Result.success(true);
    }

    /**
     * 签收详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "签收详情")
    @PostMapping(value = "/service/management/carrierOrderManagement/getSignDetail")
    public Result<CarrierOrderBeforeSignUpResponseModel> carrierOrderListBeforeSignUp(@RequestBody CarrierOrderListBeforeSignUpRequestModel requestModel) {
        CarrierOrderBeforeSignUpResponseModel responseModel = new CarrierOrderBeforeSignUpResponseModel();
        List<CarrierOrderListBeforeSignUpResponseModel> list = carrierOrderBiz.carrierOrderListBeforeSignUp(requestModel);
        responseModel.setCarrierOrderList(list);
        return Result.success(responseModel);
    }

    /**
     * 签收
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "签收")
    @PostMapping(value = "/service/management/carrierOrderManagement/signUp")
    public Result<Boolean> carrierOrderSignUp(@RequestBody CarrierOrderConfirmSignUpRequestModel requestModel) {
        carrierOrderBiz.carrierOrderSignUp(requestModel);
        return Result.success(null);
    }

    /**
     * 审核/驳回车辆
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "审核/驳回车辆")
    @PostMapping(value = "/service/management/carrierOrderManagement/auditOrRejectVehicle")
    public Result<Boolean> auditOrRejectVehicle(@RequestBody AuditOrRejectVehicleRequestModel requestModel) {
        carrierOrderBiz.auditOrRejectVehicle(requestModel);
        return Result.success(true);
    }

    /**
     * 下载提货单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "下载提货单")
    @PostMapping(value = "/service/management/carrierOrderManagement/downloadLadingBill")
    public Result<DownloadLadingBillResponseModel> downloadLadingBill(@RequestBody CarrierOrderDetailRequestModel requestModel) {
        return Result.success(carrierOrderBiz.downloadLadingBill(requestModel));
    }

    /**
     * 下载提货单根据运单code
     */
    @ApiOperation(value = "下载提货单根据运单code")
    @PostMapping(value = "/service/management/carrierOrderManagement/downloadLadingBillByCarrierCode")
    public Result<DownloadLadingBillByCarrierCodeResponseModel> downloadLadingBillByCarrierCode(@RequestBody CarrierOrderCodeRequestModel requestModel) {
        return Result.success(carrierOrderBiz.downloadLadingBillByCarrierCode(requestModel));
    }

    /**
     * 运单日志
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "运单日志")
    @PostMapping(value = "/service/management/carrierOrderManagement/getCarrierOrderLogs")
    public Result<List<GetCarrierOrderLogsResponseModel>> getCarrierOrderLogs(@RequestBody CarrierOrderDetailRequestModel requestModel) {
        return Result.success(carrierOrderBiz.getCarrierOrderLogs(requestModel.getCarrierOrderId()));
    }

    /**
     * 导出运单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出运单")
    @PostMapping(value = "/service/management/carrierOrderManagement/exportCarrierOrder")
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchCarrierOrderListResponseModel>> exportCarrierOrder(@RequestBody SearchCarrierOrderListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        PageInfo<SearchCarrierOrderListResponseModel> page =  carrierOrderBiz.searchCarrierOrderListManagement(requestModel);
        return Result.success(page.getList());
    }

    /**
     * 删除票据
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "删除票据")
    @PostMapping(value = "/service/management/carrierOrderManagement/delTickets")
    public Result<Boolean> delTickets(@RequestBody DeleteTicketsRequestModel requestModel) {
        carrierOrderBiz.delTickets(requestModel);
        return Result.success(true);
    }

    /**
     * 获取微信推送人
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取微信推送人")
    @PostMapping(value = "/service/carrierOrderManagement/getWeixinPushInfo")
    public Result<List<GetCarrierOrderWeixinPushResponseModel>> getWeixinPushInfo(@RequestBody GetCarrierOrderWeixinPushRequestModel requestModel) {
        return Result.success(carrierOrderBiz.getCarrierOrderWeixinPushInfo(requestModel));
    }

    /**
     * 确定推送微信消息
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "确定推送微信消息")
    @PostMapping(value = "/service/carrierOrderManagement/confirmPushWeixin")
    public Result<Boolean> confirmPushWeixin(@RequestBody ConfirmPushWeixinRequestModel requestModel) {
        carrierOrderBiz.confirmPushWeixin(requestModel);
        return Result.success(true);
    }

    /**
     * 确定推送微信消息
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "确定推送微信消息")
    @PostMapping(value = "/service/carrierOrderManagement/searchDriverAndVehicle")
    public Result<List<DriverAndVehicleResponseModel>> searchDriverAndVehicle(@RequestBody WebDemandOrderDriverRequestModel requestModel) {
        return Result.success(carrierOrderBiz.getDriverAndVehicleByVehicleNumber(requestModel));
    }

    /**
     * 修改司机运费（运单详情页面）
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "修改司机运费（运单详情页面）")
    @PostMapping(value = "/service/carrierOrderManagement/updateDriverFreightFee")
    public Result<Boolean> updateDriverFreightFee(@RequestBody UpdateDriverFreightFeeRequestModel requestModel) {
        carrierOrderBiz.updateDriverFreightFee(requestModel);
        return Result.success(true);
    }

    /**
     * 提货
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "提货")
    @PostMapping(value = "/service/carrierOrderManagement/load")
    public Result  load(@RequestBody CarrierOrderLoadRequestModel requestModel) {
        if (ListUtils.isNotEmpty(requestModel.getLoadCarrierOrderList())) {
            RedissonMultiLock redissonMultiLock = null;
            boolean hasLock = false;
            try {
                // 加锁
                RLock[] rLocks = requestModel.getLoadCarrierOrderList()
                        .stream()
                        .map(s -> redissonClient.getLock(CommonConstant.CARRIER_ORDER_LOAD_LOCK +
                                CommonConstant.COLON_SEPARATOR +
                                s.getCarrierOrderId()))
                        .toArray(RLock[]::new);
                redissonMultiLock = new RedissonMultiLock(rLocks);
                hasLock = redissonMultiLock.tryLock();
                if (hasLock) {
                    return carrierOrderBiz.load(requestModel);
                }
            }finally {
                if (hasLock) {
                    redissonMultiLock.unlock();
                }
            }
            if (!hasLock) {
                throw new BizException(-1, "操作执行中,请稍后再试");
            }
        }
        return Result.success(true);
    }

    /**
     * 卸货
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "卸货")
    @PostMapping(value = "/service/carrierOrderManagement/unload")
    public Result<Boolean> unload(@RequestBody CarrierOrderUnLoadRequestModel requestModel) {
        if (ListUtils.isNotEmpty(requestModel.getUnloadCarrierOrderList())) {
            RedissonMultiLock redissonMultiLock = null;
            boolean hasLock = false;
            try {
                // 加锁
                RLock[] rLocks = requestModel.getUnloadCarrierOrderList()
                        .stream()
                        .map(s -> redissonClient.getLock(CommonConstant.CARRIER_ORDER_UNLOAD_LOCK +
                                CommonConstant.COLON_SEPARATOR +
                                s.getCarrierOrderId()))
                        .toArray(RLock[]::new);
                redissonMultiLock = new RedissonMultiLock(rLocks);
                hasLock = redissonMultiLock.tryLock();
                if (hasLock) {
                    carrierOrderBiz.unload(requestModel);
                }
            } finally {
                if (hasLock) {
                    redissonMultiLock.unlock();
                }
            }
            if (!hasLock) {
                throw new BizException(-1, "操作执行中,请稍后再试");
            }
        }
        return Result.success(true);
    }

    /**
     * 到达提货地
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "到达卸货地")
    @PostMapping(value = "/service/carrierOrderManagement/reachLoadAddress")
    public Result<Boolean> reachLoadAddress(@RequestBody ReachLoadAddressRequestModel requestModel) {
        if (ListUtils.isNotEmpty(requestModel.getCarrierOrderId())) {
            RedissonMultiLock redissonMultiLock = null;
            boolean hasLock = false;
            try {
                // 加锁
                RLock[] rLocks = requestModel.getCarrierOrderId()
                        .stream()
                        .map(order -> redissonClient.getLock(CommonConstant.CARRIER_ORDER_REACH_LOAD_ADDRESS_LOCK +
                                CommonConstant.COLON_SEPARATOR +
                                order))
                        .toArray(RLock[]::new);
                redissonMultiLock = new RedissonMultiLock(rLocks);
                hasLock = redissonMultiLock.tryLock();
                if (hasLock) {
                    carrierOrderBiz.reachLoadAddress(requestModel);
                }
            } finally {
                if (hasLock) {
                    redissonMultiLock.unlock();
                }
            }
            if (!hasLock) {
                throw new BizException(-1, "操作执行中,请稍后再试");
            }
        }
        return Result.success(true);
    }

    /**
     * 到达卸货地
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "卸货")
    @PostMapping(value = "/service/carrierOrderManagement/reachUnloadAddress")
    public Result<Boolean> reachUnloadAddress(@RequestBody ReachUnloadAddressRequestModel requestModel) {
        if (ListUtils.isNotEmpty(requestModel.getCarrierOrderId())) {
            RedissonMultiLock redissonMultiLock = null;
            boolean hasLock = false;
            try {
                // 加锁
                RLock[] rLocks = requestModel.getCarrierOrderId()
                        .stream()
                        .map(order -> redissonClient.getLock(CommonConstant.CARRIER_ORDER_REACH_UNLOAD_ADDRESS_LOCK +
                                CommonConstant.COLON_SEPARATOR +
                                order))
                        .toArray(RLock[]::new);
                redissonMultiLock = new RedissonMultiLock(rLocks);
                hasLock = redissonMultiLock.tryLock();
                if (hasLock) {
                    carrierOrderBiz.reachUnloadAddress(requestModel);
                }
            } finally {
                if (hasLock) {
                    redissonMultiLock.unlock();
                }
            }
            if (!hasLock) {
                throw new BizException(-1, "操作执行中,请稍后再试");
            }
        }
        return Result.success(true);
    }

    /**
     * 查询提卸货详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查询提卸货详情")
    @PostMapping(value = "/service/carrierOrderManagement/getLoadDetail")
    public Result<List<LoadDetailResponseModel>> getLoadDetail(@RequestBody LoadDetailRequestModel requestModel) {
        return Result.success(carrierOrderBiz.getLoadDetail(requestModel));
    }

    /**
     * 托盘运单货物出库将数量同步物流系统(托盘调用)
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "托盘运单货物出库将数量同步物流系统(托盘调用)")
    @PostMapping(value = "/service/carrierOrderManagement/synCarrierOrderLoadAmount")
    public Result<Boolean> synCarrierOrderLoadAmount(@RequestBody SyncCarrierOrderLoadAmountRequestModel requestModel) {
        carrierOrderBiz.synCarrierOrderLoadAmount(requestModel);
        return Result.success(true);
    }

    /**
     * 检测导出运单回单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "检测导出运单回单")
    @PostMapping(value = "/service/carrierOrderManagement/checkExportCarrierOrderTickets")
    public Result<Boolean> checkExportCarrierOrderTickets(@RequestBody ExportCarrierOrderTicketsRequestModel requestModel) {
        carrierOrderBiz.checkExportCarrierOrderTickets(requestModel);
        return Result.success(true);
    }

    /**
     * 导出运单回单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出运单回单")
    @PostMapping(value = "/service/carrierOrderManagement/exportCarrierOrderTickets")
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<ExportCarrierOrderTicketsResponseModel> exportCarrierOrderTickets(@RequestBody ExportCarrierOrderTicketsRequestModel requestModel) {
        return Result.success(carrierOrderBiz.exportCarrierOrderTickets(requestModel));
    }

    @ApiOperation(value = "修改车辆")
    @PostMapping(value = "/service/carrierOrderManagement/modifyVehicle")
    public Result<Boolean> modifyVehicle(@RequestBody ModifyVehicleRequestModel requestModel) {
        carrierOrderBiz.modifyVehicle(requestModel);
        return Result.success(true);
    }

    @ApiOperation(value = "根据运单id查询司机车辆信息（后台修改车辆详情）")
    @PostMapping(value = "/service/carrierOrderManagement/getDriverVehicleInfoByCarrierOrderId")
    public Result<DriverAndVehicleResponseModel> getDriverVehicleInfoByCarrierOrderId(@RequestBody CarrierOrderDetailRequestModel requestModel) {
        return Result.success(carrierOrderBiz.getDriverVehicleInfoByCarrierOrderId(requestModel));
    }
}
