package com.logistics.tms.controller.driversafepromise.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 新增
 * @Author: sj
 * @Date: 2019/11/4 10:11
 */
@Data
public class AddSafePromiseRequestModel {
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("周期")
    private String period;
    @ApiModelProperty("经办人")
    private String agent;
    @ApiModelProperty("内容")
    private String content;
    @ApiModelProperty("附件")
    private String attachmentUrl;
    @ApiModelProperty("承诺司机列表")
    private List<Long> driverList;
}
