package com.logistics.tms.controller.companyentrust.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/9/27 14:24
 */
@Data
public class SearchCompanyEntrustResponseModel {
    private Long companyEntrustId;
    private String companyName;
    private Date createdTime;
    private String lastModifiedBy;
    private Date lastModifiedTime;
    private Integer tradingCertificateIsAmend;
    @ApiModelProperty("货主类型：1 企业 2 个人")
    private Integer type;
    @ApiModelProperty("来源 1 后台添加 2 web注册")
    private Integer source;
}
