package com.logistics.tms.client.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/22
 */
@Data
public class FixedCompanyVehicleInfoResponseModel {

	@ApiModelProperty("车辆id")
	private Long vehicleId;

	@ApiModelProperty("车牌号")
	private String vehicleNo;

	@ApiModelProperty("仓库code")
	private String warehouseCode;

	@ApiModelProperty("线路类型  1回收 2履约 3回收+履约")
	private Integer lineType;

	@ApiModelProperty("车主名称")
	private String carrierName;

	@ApiModelProperty("车主id")
	private Long tcompanyCarrierId;
}
