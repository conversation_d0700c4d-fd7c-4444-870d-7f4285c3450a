package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ConfirmPushWeixinContactItemsRequestDto {

    @ApiModelProperty("id")
    private String carrierOrderWxId;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("角色")
    private String role;
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("是否推送")
    private String ifPush;
}
