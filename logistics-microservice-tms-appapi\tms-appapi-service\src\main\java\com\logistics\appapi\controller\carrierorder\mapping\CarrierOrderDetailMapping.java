package com.logistics.appapi.controller.carrierorder.mapping;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.*;
import com.logistics.appapi.client.carrierorder.response.CarrierOrderDetailAppResponseModel;
import com.logistics.appapi.client.carrierorder.response.CarrierOrderDetailGoodsInfoAppModel;
import com.logistics.appapi.controller.carrierorder.response.CarrierOrderDetailGoodsInfoDto;
import com.logistics.appapi.controller.carrierorder.response.CarrierOrderDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;

import java.math.BigDecimal;
import java.util.Calendar;

/**
 * @author: wjf
 * @date: 2018/10/19 12:46
 */
public class CarrierOrderDetailMapping extends MapperMapping<CarrierOrderDetailAppResponseModel,CarrierOrderDetailResponseDto> {
    @Override
    public void configure() {
        CarrierOrderDetailAppResponseModel model = getSource();
        CarrierOrderDetailResponseDto dto = getDestination();
        if (null != model){
            //时间的转换
            Calendar todayC = Calendar.getInstance();
            todayC.set(Calendar.HOUR_OF_DAY,0);
            todayC.set(Calendar.MINUTE,0);
            todayC.set(Calendar.SECOND,0);
            todayC.set(Calendar.MILLISECOND,0);
            Calendar tomorrowC = Calendar.getInstance();
            tomorrowC.add(Calendar.DAY_OF_MONTH,1);
            tomorrowC.set(Calendar.HOUR_OF_DAY,0);
            tomorrowC.set(Calendar.MINUTE,0);
            tomorrowC.set(Calendar.SECOND,0);
            tomorrowC.set(Calendar.MILLISECOND,0);
            Calendar tomorrowAfterC = Calendar.getInstance();
            tomorrowAfterC.add(Calendar.DAY_OF_MONTH,2);
            tomorrowAfterC.set(Calendar.HOUR_OF_DAY,0);
            tomorrowAfterC.set(Calendar.MINUTE,0);
            tomorrowAfterC.set(Calendar.SECOND,0);
            tomorrowAfterC.set(Calendar.MILLISECOND,0);
            Calendar outC = Calendar.getInstance();
            outC.add(Calendar.DAY_OF_MONTH,3);
            outC.set(Calendar.HOUR_OF_DAY,0);
            outC.set(Calendar.MINUTE,0);
            outC.set(Calendar.SECOND,0);
            outC.set(Calendar.MILLISECOND,0);
            if (model.getExpectArrivalTime() != null){
                Calendar expectArrivalTimeC = Calendar.getInstance();
                expectArrivalTimeC.setTime(model.getExpectArrivalTime());
                if (expectArrivalTimeC.compareTo(todayC) >= 0 && expectArrivalTimeC.compareTo(tomorrowC) < 0) {
                    dto.setExpectArrivalTime("今天装");
                } else if (expectArrivalTimeC.compareTo(tomorrowC) >= 0 && expectArrivalTimeC.compareTo(tomorrowAfterC) < 0) {
                    dto.setExpectArrivalTime("明天装");
                } else if (expectArrivalTimeC.compareTo(tomorrowAfterC) >= 0 && expectArrivalTimeC.compareTo(outC) < 0) {
                    dto.setExpectArrivalTime("后天装");
                } else {
                    dto.setExpectArrivalTime(DateUtils.dateToString(model.getExpectArrivalTime(), "MM/dd") + "装");
                }
            }else if (model.getExpectedUnloadTime() != null){
                Calendar expectedUnloadTimeC = Calendar.getInstance();
                expectedUnloadTimeC.setTime(model.getExpectedUnloadTime());
                if (expectedUnloadTimeC.compareTo(todayC) >= 0 && expectedUnloadTimeC.compareTo(tomorrowC) < 0) {
                    dto.setExpectArrivalTime("今天卸");
                } else if (expectedUnloadTimeC.compareTo(tomorrowC) >= 0 && expectedUnloadTimeC.compareTo(tomorrowAfterC) < 0) {
                    dto.setExpectArrivalTime("明天卸");
                } else if (expectedUnloadTimeC.compareTo(tomorrowAfterC) >= 0 && expectedUnloadTimeC.compareTo(outC) < 0) {
                    dto.setExpectArrivalTime("后天卸");
                } else {
                    dto.setExpectArrivalTime(DateUtils.dateToString(model.getExpectedUnloadTime(),"MM/dd")+"卸");
                }
            }else {
                dto.setExpectArrivalTime("");
            }
            //货物信息的转换
            if (ListUtils.isNotEmpty(model.getCarrierOrderDetailGoodsInfo())){
                String unit = GoodsUnitEnum.getEnum(model.getGoodsUnit()).getUnit();
                BigDecimal totalAmount = BigDecimal.ZERO;
                BigDecimal totalCapacity = BigDecimal.ZERO;
                for (CarrierOrderDetailGoodsInfoAppModel good:model.getCarrierOrderDetailGoodsInfo()) {
                    BigDecimal capacity = ConverterUtils.toBigDecimal(good.getLength()).multiply(ConverterUtils.toBigDecimal(good.getWidth())).multiply(ConverterUtils.toBigDecimal(good.getHeight()));
                    for (CarrierOrderDetailGoodsInfoDto goodDto:dto.getCarrierOrderDetailGoodsInfo()) {
                        if (goodDto.getGoodsId().equals(ConverterUtils.toString(good.getGoodsId()))){
                            BigDecimal amount = BigDecimal.ZERO;
                            if (model.getStatus().equals(CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey()) || model.getStatus().equals(CarrierOrderStatusEnum.WAIT_LOAD.getKey())){
                                amount = good.getExpectAmount();
                            }else if (model.getStatus().equals(CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey()) || model.getStatus().equals(CarrierOrderStatusEnum.WAIT_UNLOAD.getKey())){
                                amount = good.getLoadAmount();
                            }else if (model.getStatus().equals(CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey())){
                                amount = good.getUnloadAmount();
                            }else if (model.getStatus().equals(CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey())){
                                amount = good.getSignAmount();
                            }
                            goodDto.setAmount(amount.stripTrailingZeros().toPlainString());
                            if (model.getGoodsUnit().equals(GoodsUnitEnum.BY_VOLUME.getKey())){
                                BigDecimal goodsVolume = capacity.multiply(ConverterUtils.toBigDecimal(amount)).divide(CommonConstant.CUBIC_MILLIMETER_TO_CUBIC_METER,3,BigDecimal.ROUND_HALF_UP);
                                goodDto.setCapacity(goodsVolume.stripTrailingZeros().toPlainString());
                                totalCapacity = totalCapacity.add(goodsVolume);
                            }
                            totalAmount = totalAmount.add(amount);
                            goodDto.setGoodsSize(good.getLength() + "*" + good.getWidth() + "*" + good.getHeight() + "mm");
                        }
                    }
                }
                dto.setTotalAmount(totalAmount.stripTrailingZeros().toPlainString() + unit);
                if (model.getGoodsUnit().equals(GoodsUnitEnum.BY_VOLUME.getKey())) {
                    dto.setTotalCapacity(totalCapacity.stripTrailingZeros().toPlainString() + CommonConstant.SQUARE);
                }
            }
            if (CommonConstant.INTEGER_ONE.equals(model.getIfCancel())||CommonConstant.INTEGER_TWO.equals(model.getIfCancel())){
                dto.setStatus(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getKey().toString());
                dto.setStatusLabel(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getValue());
                if (CommonConstant.INTEGER_TWO.equals(model.getIfCancel())){
                    dto.setCancelReason(model.getCancelReasonTwo());
                    dto.setCancelOperatorName(model.getCancelOperatorNameTwo());
                    dto.setCancelTime(DateUtils.dateToString(model.getCancelTimeTwo(),"yyyy-MM-dd HH:mm:ss"));
                }
            }else if (CommonConstant.INTEGER_ONE.equals(model.getIfEmpty())){
                dto.setStatus(CarrierOrderStatusEnum.EMPTY.getKey().toString());
                dto.setStatusLabel(CarrierOrderStatusEnum.EMPTY.getValue());
            }else{
                dto.setStatusLabel(CarrierOrderStatusEnum.getEnum(model.getStatus()).getValue());
            }

            if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(model.getDemandOrderSource())) {
                //云盘运单
                dto.setCustomerOrderCode("");
            }else if (DemandOrderSourceEnum.SINOPEC.getKey().equals(model.getDemandOrderSource())) {
                //中石化推送的单子
                if (DemandOrderOrderTypeEnum.PUSH.getKey().equals(model.getOrderType())) {
                    dto.setCustomerOrderCode(model.getSinopecOrderNo());
                }
            }else if (DemandOrderSourceEnum.YELO_LIFE.getKey().equals(model.getDemandOrderSource())) {
                //新生单子
                if (CompanyTypeEnum.PERSONAL.getKey().equals(model.getBusinessType())) {
                    dto.setCustomerName(model.getCustomerUserName() + " " + model.getCustomerUserMobile());
                }
            }

            //我司且是云盘单子，此单才能提交临时费用
            if (CommonConstant.INTEGER_ONE.equals(model.getIsOurCompany()) && DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(model.getDemandOrderSource())) {
                dto.setCommitOtherFee(CommonConstant.ONE);
            }else{
                dto.setCommitOtherFee(CommonConstant.ZERO);
            }
        }
    }
}
