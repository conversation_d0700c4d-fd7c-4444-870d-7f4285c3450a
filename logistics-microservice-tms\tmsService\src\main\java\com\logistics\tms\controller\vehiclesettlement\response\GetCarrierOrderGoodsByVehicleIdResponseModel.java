package com.logistics.tms.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2019/10/14 15:20
 */
@Data
public class GetCarrierOrderGoodsByVehicleIdResponseModel {
    @ApiModelProperty("货物id")
    private Long goodsId;
    @ApiModelProperty("规格")
    private Integer length;
    @ApiModelProperty("规格")
    private Integer width;
    @ApiModelProperty("规格")
    private Integer height;
    @ApiModelProperty("预提件数")
    private BigDecimal expectAmount;
    @ApiModelProperty("实提件数")
    private BigDecimal loadAmount;
    @ApiModelProperty("实卸件数")
    private BigDecimal unloadAmount;
    @ApiModelProperty("签收件数")
    private BigDecimal signAmount;
}
