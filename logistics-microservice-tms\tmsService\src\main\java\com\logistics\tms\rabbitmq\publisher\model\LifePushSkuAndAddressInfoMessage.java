package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 推送给新生货物
 *
 * <AUTHOR>
 * @date 2022/8/20 13:19
 */
@Data
public class LifePushSkuAndAddressInfoMessage {

    @ApiModelProperty("回收单code")
    private String recycleOrderCode;
    @ApiModelProperty("卸货地址信息 存放卸货地址信息")
    private LifeAddressInfoModel lifeAddressInfoDto;
    @ApiModelProperty("货物信息")
    private List<SkuInfoModel> skuInfoDto;
    @ApiModelProperty("操作人")
    private String operator;

}
