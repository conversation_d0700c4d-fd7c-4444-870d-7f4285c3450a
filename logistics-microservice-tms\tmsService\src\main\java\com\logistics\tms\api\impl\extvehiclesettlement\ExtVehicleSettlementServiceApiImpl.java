package com.logistics.tms.api.impl.extvehiclesettlement;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.extvehiclesettlement.ExtVehicleSettlementServiceApi;
import com.logistics.tms.api.feign.extvehiclesettlement.model.*;
import com.logistics.tms.biz.extvehiclesettlement.ExtVehicleSettlementBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: wjf
 * @date: 2019/11/20 14:29
 */
@RestController
public class ExtVehicleSettlementServiceApiImpl implements ExtVehicleSettlementServiceApi {

    @Autowired
    private ExtVehicleSettlementBiz extVehicleSettlementBiz;

    /**
     * 列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<SearchExtVehicleSettlementListResponseModel>> searchExtVehicleSettlementList(@RequestBody SearchExtVehicleSettlementListRequestModel requestModel) {
        return Result.success(extVehicleSettlementBiz.searchExtVehicleSettlementList(requestModel));
    }

    /**
     * 详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<ExtVehicleSettlementDetailResponseModel> extVehicleSettlementDetail(@RequestBody ExtVehicleSettlementIdRequestModel requestModel) {
        return Result.success(extVehicleSettlementBiz.extVehicleSettlementDetail(requestModel));
    }

    /**
     * 付款
     * @param requestModel
     * @return
     */
    @Override
    public Result extVehicleSettlementPayment(@RequestBody ExtVehicleSettlementPaymentRequestModel requestModel) {
        extVehicleSettlementBiz.extVehicleSettlementPayment(requestModel);
        return Result.success(true);
    }

    /**
     * 回退
     * @param requestModel
     * @return
     */
    @Override
    public Result extVehicleSettlementFallback(@RequestBody ExtVehicleSettlementFallbackRequestModel requestModel) {
        extVehicleSettlementBiz.extVehicleSettlementFallback(requestModel);
        return Result.success(true);
    }
}
