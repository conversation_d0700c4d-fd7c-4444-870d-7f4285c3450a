<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandOrderObjectionSinopecMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDemandOrderObjectionSinopec" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT" />
    <result column="load_province_id" property="loadProvinceId" jdbcType="BIGINT" />
    <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR" />
    <result column="load_city_id" property="loadCityId" jdbcType="BIGINT" />
    <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR" />
    <result column="load_area_id" property="loadAreaId" jdbcType="BIGINT" />
    <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR" />
    <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR" />
    <result column="unload_province_id" property="unloadProvinceId" jdbcType="BIGINT" />
    <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR" />
    <result column="unload_city_id" property="unloadCityId" jdbcType="BIGINT" />
    <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR" />
    <result column="unload_area_id" property="unloadAreaId" jdbcType="BIGINT" />
    <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR" />
    <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR" />
    <result column="contract_price" property="contractPrice" jdbcType="DECIMAL" />
    <result column="dispatcher_name" property="dispatcherName" jdbcType="VARCHAR" />
    <result column="dispatcher_phone" property="dispatcherPhone" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="objection_type" property="objectionType" jdbcType="INTEGER" />
    <result column="audit_status" property="auditStatus" jdbcType="INTEGER" />
    <result column="auditor_name" property="auditorName" jdbcType="VARCHAR" />
    <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
    <result column="audit_objection_type" property="auditObjectionType" jdbcType="INTEGER" />
    <result column="audit_remark" property="auditRemark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, demand_order_id, load_province_id, load_province_name, load_city_id, load_city_name, 
    load_area_id, load_area_name, load_detail_address, unload_province_id, unload_province_name, 
    unload_city_id, unload_city_name, unload_area_id, unload_area_name, unload_detail_address, 
    contract_price, dispatcher_name, dispatcher_phone, remark, objection_type, audit_status, 
    auditor_name, audit_time, audit_objection_type, audit_remark, created_by, created_time, 
    last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_demand_order_objection_sinopec
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_demand_order_objection_sinopec
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDemandOrderObjectionSinopec" >
    insert into t_demand_order_objection_sinopec (id, demand_order_id, load_province_id, 
      load_province_name, load_city_id, load_city_name, 
      load_area_id, load_area_name, load_detail_address, 
      unload_province_id, unload_province_name, unload_city_id, 
      unload_city_name, unload_area_id, unload_area_name, 
      unload_detail_address, contract_price, dispatcher_name, 
      dispatcher_phone, remark, objection_type, 
      audit_status, auditor_name, audit_time, 
      audit_objection_type, audit_remark, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{demandOrderId,jdbcType=BIGINT}, #{loadProvinceId,jdbcType=BIGINT}, 
      #{loadProvinceName,jdbcType=VARCHAR}, #{loadCityId,jdbcType=BIGINT}, #{loadCityName,jdbcType=VARCHAR}, 
      #{loadAreaId,jdbcType=BIGINT}, #{loadAreaName,jdbcType=VARCHAR}, #{loadDetailAddress,jdbcType=VARCHAR}, 
      #{unloadProvinceId,jdbcType=BIGINT}, #{unloadProvinceName,jdbcType=VARCHAR}, #{unloadCityId,jdbcType=BIGINT}, 
      #{unloadCityName,jdbcType=VARCHAR}, #{unloadAreaId,jdbcType=BIGINT}, #{unloadAreaName,jdbcType=VARCHAR}, 
      #{unloadDetailAddress,jdbcType=VARCHAR}, #{contractPrice,jdbcType=DECIMAL}, #{dispatcherName,jdbcType=VARCHAR}, 
      #{dispatcherPhone,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{objectionType,jdbcType=INTEGER}, 
      #{auditStatus,jdbcType=INTEGER}, #{auditorName,jdbcType=VARCHAR}, #{auditTime,jdbcType=TIMESTAMP}, 
      #{auditObjectionType,jdbcType=INTEGER}, #{auditRemark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDemandOrderObjectionSinopec" >
    insert into t_demand_order_objection_sinopec
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="demandOrderId != null" >
        demand_order_id,
      </if>
      <if test="loadProvinceId != null" >
        load_province_id,
      </if>
      <if test="loadProvinceName != null" >
        load_province_name,
      </if>
      <if test="loadCityId != null" >
        load_city_id,
      </if>
      <if test="loadCityName != null" >
        load_city_name,
      </if>
      <if test="loadAreaId != null" >
        load_area_id,
      </if>
      <if test="loadAreaName != null" >
        load_area_name,
      </if>
      <if test="loadDetailAddress != null" >
        load_detail_address,
      </if>
      <if test="unloadProvinceId != null" >
        unload_province_id,
      </if>
      <if test="unloadProvinceName != null" >
        unload_province_name,
      </if>
      <if test="unloadCityId != null" >
        unload_city_id,
      </if>
      <if test="unloadCityName != null" >
        unload_city_name,
      </if>
      <if test="unloadAreaId != null" >
        unload_area_id,
      </if>
      <if test="unloadAreaName != null" >
        unload_area_name,
      </if>
      <if test="unloadDetailAddress != null" >
        unload_detail_address,
      </if>
      <if test="contractPrice != null" >
        contract_price,
      </if>
      <if test="dispatcherName != null" >
        dispatcher_name,
      </if>
      <if test="dispatcherPhone != null" >
        dispatcher_phone,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="objectionType != null" >
        objection_type,
      </if>
      <if test="auditStatus != null" >
        audit_status,
      </if>
      <if test="auditorName != null" >
        auditor_name,
      </if>
      <if test="auditTime != null" >
        audit_time,
      </if>
      <if test="auditObjectionType != null" >
        audit_objection_type,
      </if>
      <if test="auditRemark != null" >
        audit_remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="demandOrderId != null" >
        #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="loadProvinceId != null" >
        #{loadProvinceId,jdbcType=BIGINT},
      </if>
      <if test="loadProvinceName != null" >
        #{loadProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="loadCityId != null" >
        #{loadCityId,jdbcType=BIGINT},
      </if>
      <if test="loadCityName != null" >
        #{loadCityName,jdbcType=VARCHAR},
      </if>
      <if test="loadAreaId != null" >
        #{loadAreaId,jdbcType=BIGINT},
      </if>
      <if test="loadAreaName != null" >
        #{loadAreaName,jdbcType=VARCHAR},
      </if>
      <if test="loadDetailAddress != null" >
        #{loadDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="unloadProvinceId != null" >
        #{unloadProvinceId,jdbcType=BIGINT},
      </if>
      <if test="unloadProvinceName != null" >
        #{unloadProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="unloadCityId != null" >
        #{unloadCityId,jdbcType=BIGINT},
      </if>
      <if test="unloadCityName != null" >
        #{unloadCityName,jdbcType=VARCHAR},
      </if>
      <if test="unloadAreaId != null" >
        #{unloadAreaId,jdbcType=BIGINT},
      </if>
      <if test="unloadAreaName != null" >
        #{unloadAreaName,jdbcType=VARCHAR},
      </if>
      <if test="unloadDetailAddress != null" >
        #{unloadDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="contractPrice != null" >
        #{contractPrice,jdbcType=DECIMAL},
      </if>
      <if test="dispatcherName != null" >
        #{dispatcherName,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherPhone != null" >
        #{dispatcherPhone,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="objectionType != null" >
        #{objectionType,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null" >
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditorName != null" >
        #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null" >
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditObjectionType != null" >
        #{auditObjectionType,jdbcType=INTEGER},
      </if>
      <if test="auditRemark != null" >
        #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDemandOrderObjectionSinopec" >
    update t_demand_order_objection_sinopec
    <set >
      <if test="demandOrderId != null" >
        demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="loadProvinceId != null" >
        load_province_id = #{loadProvinceId,jdbcType=BIGINT},
      </if>
      <if test="loadProvinceName != null" >
        load_province_name = #{loadProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="loadCityId != null" >
        load_city_id = #{loadCityId,jdbcType=BIGINT},
      </if>
      <if test="loadCityName != null" >
        load_city_name = #{loadCityName,jdbcType=VARCHAR},
      </if>
      <if test="loadAreaId != null" >
        load_area_id = #{loadAreaId,jdbcType=BIGINT},
      </if>
      <if test="loadAreaName != null" >
        load_area_name = #{loadAreaName,jdbcType=VARCHAR},
      </if>
      <if test="loadDetailAddress != null" >
        load_detail_address = #{loadDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="unloadProvinceId != null" >
        unload_province_id = #{unloadProvinceId,jdbcType=BIGINT},
      </if>
      <if test="unloadProvinceName != null" >
        unload_province_name = #{unloadProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="unloadCityId != null" >
        unload_city_id = #{unloadCityId,jdbcType=BIGINT},
      </if>
      <if test="unloadCityName != null" >
        unload_city_name = #{unloadCityName,jdbcType=VARCHAR},
      </if>
      <if test="unloadAreaId != null" >
        unload_area_id = #{unloadAreaId,jdbcType=BIGINT},
      </if>
      <if test="unloadAreaName != null" >
        unload_area_name = #{unloadAreaName,jdbcType=VARCHAR},
      </if>
      <if test="unloadDetailAddress != null" >
        unload_detail_address = #{unloadDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="contractPrice != null" >
        contract_price = #{contractPrice,jdbcType=DECIMAL},
      </if>
      <if test="dispatcherName != null" >
        dispatcher_name = #{dispatcherName,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherPhone != null" >
        dispatcher_phone = #{dispatcherPhone,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="objectionType != null" >
        objection_type = #{objectionType,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null" >
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditorName != null" >
        auditor_name = #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null" >
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditObjectionType != null" >
        audit_objection_type = #{auditObjectionType,jdbcType=INTEGER},
      </if>
      <if test="auditRemark != null" >
        audit_remark = #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDemandOrderObjectionSinopec" >
    update t_demand_order_objection_sinopec
    set demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      load_province_id = #{loadProvinceId,jdbcType=BIGINT},
      load_province_name = #{loadProvinceName,jdbcType=VARCHAR},
      load_city_id = #{loadCityId,jdbcType=BIGINT},
      load_city_name = #{loadCityName,jdbcType=VARCHAR},
      load_area_id = #{loadAreaId,jdbcType=BIGINT},
      load_area_name = #{loadAreaName,jdbcType=VARCHAR},
      load_detail_address = #{loadDetailAddress,jdbcType=VARCHAR},
      unload_province_id = #{unloadProvinceId,jdbcType=BIGINT},
      unload_province_name = #{unloadProvinceName,jdbcType=VARCHAR},
      unload_city_id = #{unloadCityId,jdbcType=BIGINT},
      unload_city_name = #{unloadCityName,jdbcType=VARCHAR},
      unload_area_id = #{unloadAreaId,jdbcType=BIGINT},
      unload_area_name = #{unloadAreaName,jdbcType=VARCHAR},
      unload_detail_address = #{unloadDetailAddress,jdbcType=VARCHAR},
      contract_price = #{contractPrice,jdbcType=DECIMAL},
      dispatcher_name = #{dispatcherName,jdbcType=VARCHAR},
      dispatcher_phone = #{dispatcherPhone,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      objection_type = #{objectionType,jdbcType=INTEGER},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      auditor_name = #{auditorName,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      audit_objection_type = #{auditObjectionType,jdbcType=INTEGER},
      audit_remark = #{auditRemark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>