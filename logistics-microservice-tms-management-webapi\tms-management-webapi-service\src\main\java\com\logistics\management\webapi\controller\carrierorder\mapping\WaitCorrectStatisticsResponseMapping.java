package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.carrierorder.response.WaitCorrectStatisticsModel;
import com.logistics.management.webapi.client.carrierorder.response.WaitCorrectStatisticsResponseModel;
import com.logistics.management.webapi.client.carrierorder.response.WaitLoadStatisticsModel;
import com.logistics.management.webapi.controller.carrierorder.response.WaitCorrectStatisticsDto;
import com.logistics.management.webapi.controller.carrierorder.response.WaitCorrectStatisticsResponseDto;
import com.logistics.management.webapi.controller.carrierorder.response.WaitLoadStatisticsDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import lombok.SneakyThrows;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: fjh
 * @date: 2022/1/6 11:20
 */
public class WaitCorrectStatisticsResponseMapping extends MapperMapping<WaitCorrectStatisticsResponseModel, WaitCorrectStatisticsResponseDto> {
    @SneakyThrows
    @Override
    public void configure() {
        WaitCorrectStatisticsResponseModel source = getSource();
        WaitCorrectStatisticsResponseDto destination = getDestination();

        //待纠错集合
        List<WaitCorrectStatisticsDto> waitCorrectList = new ArrayList<>();
        //待纠错总单数
        Integer waitCorrectOrderCount = CommonConstant.INTEGER_ZERO;
        //待提货集合
        List<WaitLoadStatisticsDto> waitLoadList = new ArrayList<>();

        //云盘物流看板-待纠错集合
        List<WaitCorrectStatisticsModel> waitCorrectStatisticsModelList = source.getWaitCorrectList();
        if (ListUtils.isNotEmpty(waitCorrectStatisticsModelList)){
            //待纠错总单数
            waitCorrectOrderCount = waitCorrectStatisticsModelList.stream().collect(Collectors.summingInt(WaitCorrectStatisticsModel::getCarrierOrderCount));

            WaitCorrectStatisticsDto waitCorrectStatisticsDto;
            for (WaitCorrectStatisticsModel model : waitCorrectStatisticsModelList) {
                waitCorrectStatisticsDto = MapperUtils.mapper(model, WaitCorrectStatisticsDto.class);
                waitCorrectStatisticsDto.setProportion(ConverterUtils.toString(ConverterUtils.toBigDecimal(model.getCarrierOrderCount()).multiply(CommonConstant.PERCENT).divide(ConverterUtils.toBigDecimal(waitCorrectOrderCount), 2, BigDecimal.ROUND_HALF_UP)));
                waitCorrectList.add(waitCorrectStatisticsDto);
            }
        }

        //云盘物流看板-待提货集合
        List<WaitLoadStatisticsModel> waitLoadStatisticsModelList = source.getWaitLoadList();
        if (ListUtils.isNotEmpty(waitLoadStatisticsModelList)){
            //待提货总单数
            Integer waitLoadOrderCount = waitLoadStatisticsModelList.size();
            //三天前
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Calendar data = Calendar.getInstance();
            data.setTime(dateFormat.parse(dateFormat.format(data.getTime())));
            data.add(Calendar.DATE, - CommonConstant.INTEGER_THREE);
            Date threeDaysAgo = data.getTime();

            //十天前
            data.setTime(dateFormat.parse(dateFormat.format(data.getTime())));
            data.add(Calendar.DATE, - CommonConstant.INT_TEN);
            Date tenDaysAgo = data.getTime();

            //二十天前
            data.setTime(dateFormat.parse(dateFormat.format(data.getTime())));
            data.add(Calendar.DATE, - CommonConstant.INT_TWENTY);
            Date twentyDaysAgo = data.getTime();

            //三十一天前
            data.setTime(dateFormat.parse(dateFormat.format(data.getTime())));
            data.add(Calendar.DATE, - CommonConstant.INT_THIRTY_ONE);
            Date thirtyOneDaysAgo = data.getTime();


            //待提货运单单数
            Integer orderCountOne= CommonConstant.INTEGER_ZERO;
            Integer orderCountTwo= CommonConstant.INTEGER_ZERO;
            Integer orderCountThree= CommonConstant.INTEGER_ZERO;
            Integer orderCountFour= CommonConstant.INTEGER_ZERO;
            Integer orderCountFive= CommonConstant.INTEGER_ZERO;
            //待提货运单货物数量
            BigDecimal orderAmountOne = CommonConstant.BIG_DECIMAL_ZERO;
            BigDecimal orderAmountTwo = CommonConstant.BIG_DECIMAL_ZERO;
            BigDecimal orderAmountThree = CommonConstant.BIG_DECIMAL_ZERO;
            BigDecimal orderAmountFour = CommonConstant.BIG_DECIMAL_ZERO;
            BigDecimal orderAmountFive = CommonConstant.BIG_DECIMAL_ZERO;
            for (WaitLoadStatisticsModel model : waitLoadStatisticsModelList) {
                if (model.getDispatchTime().getTime()>=threeDaysAgo.getTime()){//0-3天
                    orderCountOne++;
                    orderAmountOne=orderAmountOne.add(model.getExpectAmount());
                }else if (threeDaysAgo.getTime()> model.getDispatchTime().getTime() && model.getDispatchTime().getTime()>=tenDaysAgo.getTime()){//4-10天
                    orderCountTwo++;
                    orderAmountTwo=orderAmountTwo.add(model.getExpectAmount());
                }else if (tenDaysAgo.getTime()> model.getDispatchTime().getTime() && model.getDispatchTime().getTime()>=twentyDaysAgo.getTime()){//11-20天
                    orderCountThree++;
                    orderAmountThree=orderAmountThree.add(model.getExpectAmount());
                }else if (twentyDaysAgo.getTime()> model.getDispatchTime().getTime() && model.getDispatchTime().getTime()>=thirtyOneDaysAgo.getTime()){//21-31天
                    orderCountFour++;
                    orderAmountFour=orderAmountFour.add(model.getExpectAmount());
                }else if (thirtyOneDaysAgo.getTime()>model.getDispatchTime().getTime()){//大于31天
                    orderCountFive++;
                    orderAmountFive=orderAmountFive.add(model.getExpectAmount());
                }
            }
            //云盘物流看板-待提货 0~3天数据
            WaitLoadStatisticsDto waitLoadStatisticsModelOne = new WaitLoadStatisticsDto();
            waitLoadStatisticsModelOne.setDayType(String.valueOf(CommonConstant.INTEGER_ONE));
            waitLoadStatisticsModelOne.setOrderCount(String.valueOf(orderCountOne));
            waitLoadStatisticsModelOne.setOrderAmount(orderAmountOne.stripTrailingZeros().toPlainString());
            waitLoadStatisticsModelOne.setProportion(ConverterUtils.toString(ConverterUtils.toBigDecimal(orderCountOne).multiply(CommonConstant.PERCENT).divide(ConverterUtils.toBigDecimal(waitLoadOrderCount), 2, BigDecimal.ROUND_HALF_UP)));
            waitLoadList.add(waitLoadStatisticsModelOne);
            //云盘物流看板-待提货 4~10天数据
            WaitLoadStatisticsDto waitLoadStatisticsModelTwo = new WaitLoadStatisticsDto();
            waitLoadStatisticsModelTwo.setDayType(String.valueOf(CommonConstant.INTEGER_TWO));
            waitLoadStatisticsModelTwo.setOrderCount(String.valueOf(orderCountTwo));
            waitLoadStatisticsModelTwo.setOrderAmount(orderAmountTwo.stripTrailingZeros().toPlainString());
            waitLoadStatisticsModelTwo.setProportion(ConverterUtils.toString(ConverterUtils.toBigDecimal(orderCountTwo).multiply(CommonConstant.PERCENT).divide(ConverterUtils.toBigDecimal(waitLoadOrderCount), 2, BigDecimal.ROUND_HALF_UP)));
            waitLoadList.add(waitLoadStatisticsModelTwo);
            //云盘物流看板-待提货 11~20天数据
            WaitLoadStatisticsDto waitLoadStatisticsModelThree = new WaitLoadStatisticsDto();
            waitLoadStatisticsModelThree.setDayType(String.valueOf(CommonConstant.INTEGER_THREE));
            waitLoadStatisticsModelThree.setOrderCount(String.valueOf(orderCountThree));
            waitLoadStatisticsModelThree.setOrderAmount(orderAmountThree.stripTrailingZeros().toPlainString());
            waitLoadStatisticsModelThree.setProportion(ConverterUtils.toString(ConverterUtils.toBigDecimal(orderCountThree).multiply(CommonConstant.PERCENT).divide(ConverterUtils.toBigDecimal(waitLoadOrderCount), 2, BigDecimal.ROUND_HALF_UP)));
            waitLoadList.add(waitLoadStatisticsModelThree);
            //云盘物流看板-待提货 21~31天数据
            WaitLoadStatisticsDto waitLoadStatisticsModelFour = new WaitLoadStatisticsDto();
            waitLoadStatisticsModelFour.setDayType(String.valueOf(CommonConstant.INTEGER_FOUR));
            waitLoadStatisticsModelFour.setOrderCount(String.valueOf(orderCountFour));
            waitLoadStatisticsModelFour.setOrderAmount(orderAmountFour.stripTrailingZeros().toPlainString());
            waitLoadStatisticsModelFour.setProportion(ConverterUtils.toString(ConverterUtils.toBigDecimal(orderCountFour).multiply(CommonConstant.PERCENT).divide(ConverterUtils.toBigDecimal(waitLoadOrderCount), 2, BigDecimal.ROUND_HALF_UP)));
            waitLoadList.add(waitLoadStatisticsModelFour);
            //云盘物流看板-待提货 大于31天数据
            WaitLoadStatisticsDto waitLoadStatisticsModelFive = new WaitLoadStatisticsDto();
            waitLoadStatisticsModelFive.setDayType(String.valueOf(CommonConstant.INTEGER_FIVE));
            waitLoadStatisticsModelFive.setOrderCount(String.valueOf(orderCountFive));
            waitLoadStatisticsModelFive.setOrderAmount(orderAmountFive.stripTrailingZeros().toPlainString());
            waitLoadStatisticsModelFive.setProportion(ConverterUtils.toString(ConverterUtils.toBigDecimal(orderCountFive).multiply(CommonConstant.PERCENT).divide(ConverterUtils.toBigDecimal(waitLoadOrderCount), 2, BigDecimal.ROUND_HALF_UP)));
            waitLoadList.add(waitLoadStatisticsModelFive);
        }
        destination.setWaitCorrectList(waitCorrectList);
        destination.setWaitLoadList(waitLoadList);
        destination.setWaitCorrectOrderCount(waitCorrectOrderCount.toString());

    }
}