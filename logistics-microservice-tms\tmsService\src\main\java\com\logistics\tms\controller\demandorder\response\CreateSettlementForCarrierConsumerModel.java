package com.logistics.tms.controller.demandorder.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2019/5/29 13:43
 */
@Data
public class CreateSettlementForCarrierConsumerModel {
    private Long carrierOrderId;
    private Integer priceType;
    private Integer payPriceType;
    private BigDecimal settlementAmount;
    private BigDecimal paySettlementAmount;
    private BigDecimal settlementCost;
    private BigDecimal paySettlementCost;
    private String userName;
}
