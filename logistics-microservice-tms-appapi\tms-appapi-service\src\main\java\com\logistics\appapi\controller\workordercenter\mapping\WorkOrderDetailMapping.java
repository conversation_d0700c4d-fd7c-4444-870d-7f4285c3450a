package com.logistics.appapi.controller.workordercenter.mapping;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.WorkOrderAnomalyTypeOneEnum;
import com.logistics.appapi.base.enums.WorkOrderAnomalyTypeTwoEnum;
import com.logistics.appapi.base.enums.WorkOrderStatusEnum;
import com.logistics.appapi.client.workordercenter.response.WorkOrderDetailAppletResponseModel;
import com.logistics.appapi.controller.workordercenter.response.WorkOrderDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.Optional;

public class WorkOrderDetailMapping extends MapperMapping<WorkOrderDetailAppletResponseModel, WorkOrderDetailResponseDto> {

    @Override
    public void configure() {
        WorkOrderDetailAppletResponseModel source = getSource();
        WorkOrderDetailResponseDto destination = getDestination();

        //状态
        destination.setStatusLabel(WorkOrderStatusEnum.getEnumByKey(source.getStatus()).getValue());

        //提货地址
        String loadDetailAddress = source.getLoadProvinceName() + source.getLoadCityName() + source.getLoadAreaName()
                + (StringUtils.isNotBlank(source.getLoadWarehouse()) ? "【" + source.getLoadWarehouse() + "】" : "") + source.getLoadDetailAddress();
        destination.setLoadDetailAddressShow(loadDetailAddress);

        destination.setAnomalyTypeOneLabel(WorkOrderAnomalyTypeOneEnum.getEnumByKey(source.getAnomalyTypeOne()).getValue());
        destination.setAnomalyTypeTwoLabel(WorkOrderAnomalyTypeTwoEnum.getEnumByKey(source.getAnomalyTypeTwo()).getValue());
        //问题类型
        destination.setAbnormalProblem(WorkOrderAnomalyTypeOneEnum.getEnumByKey(source.getAnomalyTypeOne()).getValue() + "/" + WorkOrderAnomalyTypeTwoEnum.getEnumByKey(source.getAnomalyTypeTwo()).getValue());

        String reportAddress;
        if (CommonConstant.INTEGER_ONE.equals(source.getIsArriveScene())) {
            reportAddress = (StringUtils.isNotBlank(source.getAddressHead()) ? ("【" + source.getAddressHead() + "】") : "") + Optional.ofNullable(source.getAddressDetail()).orElse("");
        } else {
            reportAddress = "未到现场";
        }

        //现场情况
        destination.setReportAddress(reportAddress);

        //提报人设置
        destination.setReportUserName(CommonConstant.INTEGER_ONE.equals(source.getReportSource()) ? "工作人员" : ConverterUtils.toString(source.getReportUserName()));
    }
}
