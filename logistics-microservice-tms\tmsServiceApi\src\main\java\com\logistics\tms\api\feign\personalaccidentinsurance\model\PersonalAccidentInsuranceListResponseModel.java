package com.logistics.tms.api.feign.personalaccidentinsurance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/5/30 13:28
 */
@Data
public class PersonalAccidentInsuranceListResponseModel {
    @ApiModelProperty("个人意外险表id")
    private Long personalAccidentInsuranceId;
    @ApiModelProperty("保险公司名称")
    private String insuranceCompany;
    @ApiModelProperty("保单类型：1 保单，2 批单")
    private Integer insuranceType;
    @ApiModelProperty("保单号")
    private String policyNumber;
    @ApiModelProperty("批单号")
    private String batchNumber;
    @ApiModelProperty("保单总额")
    private BigDecimal grossPremium;
    @ApiModelProperty("保单人数")
    private Integer policyPersonCount;
    @ApiModelProperty("关联人数")
    private Integer associatedCount;
    @ApiModelProperty("保险生效时间")
    private Date startTime;
    @ApiModelProperty("保险截止时间")
    private Date endTime;
    private String lastModifiedBy;
    private Date lastModifiedTime;
    private List<PersonalAccidentInsuranceRelatedVehicleResponseModel> insuranceList;
    @ApiModelProperty("个人意外险批单单据")
    private List<PersonalAccidentInsuranceTicketsResponseModel> ticketsList;
}
