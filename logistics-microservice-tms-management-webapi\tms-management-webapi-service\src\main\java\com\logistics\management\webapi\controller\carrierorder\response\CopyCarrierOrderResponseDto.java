package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/11 11:03
 */
@Data
public class CopyCarrierOrderResponseDto {
    //运单基础信息
    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";
    @ApiModelProperty("需求单号")
    private String demandOrderCode = "";
    @ApiModelProperty("预计承运数量")
    private String expectAmount = "";

    //运单地址信息
    //发货地址
    @ApiModelProperty("发货仓库")
    private String loadWarehouse = "";
    @ApiModelProperty("发货地址")
    private String loadAddress = "";
    @ApiModelProperty("发货人")
    private String consignorName = "";
    private String consignorMobile = "";
    //收货地址
    @ApiModelProperty("卸货仓库")
    private String unloadWarehouse = "";
    @ApiModelProperty("收货地址")
    private String unloadAddress = "";
    @ApiModelProperty("收货人")
    private String receiverName = "";
    private String receiverMobile = "";

    //运单货物信息
    @ApiModelProperty("品名")
    private String goodsName = "";

    //运单车辆司机信息
    @ApiModelProperty("车牌号")
    private String vehicleNo = "";
    @ApiModelProperty("司机姓名")
    private String driverName = "";
    @ApiModelProperty("司机手机号")
    private String driverMobile = "";

    @ApiModelProperty("预计提货时间")
    private String expectLoadTime = "";

}
