package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/12/23 16:52
 */
public class ExportOilRefundRecord {
    private ExportOilRefundRecord() {

    }

    private static final Map<String, String> EXPORT_OIL_REFUND_RECORDS_MAP;

    static {
        EXPORT_OIL_REFUND_RECORDS_MAP = new LinkedHashMap<>();
        EXPORT_OIL_REFUND_RECORDS_MAP.put("车牌号", "vehicleNo");
        EXPORT_OIL_REFUND_RECORDS_MAP.put("退款金额 ", "oilFilledFee");
        EXPORT_OIL_REFUND_RECORDS_MAP.put("退款时间", "oilFilledDate");
        EXPORT_OIL_REFUND_RECORDS_MAP.put("充油方式", "oilFilledTypeLabel");
        EXPORT_OIL_REFUND_RECORDS_MAP.put("退款原因", "refundReasonTypeDesc");
        EXPORT_OIL_REFUND_RECORDS_MAP.put("退款原因描述", "refundReason");
        EXPORT_OIL_REFUND_RECORDS_MAP.put("操作人", "lastModifiedBy");
        EXPORT_OIL_REFUND_RECORDS_MAP.put("操作时间", "lastModifiedTime");
    }

    public static Map<String, String> getExportOilRefundRecordMap() {
        return EXPORT_OIL_REFUND_RECORDS_MAP;
    }
}
