package com.logistics.appapi.base.enums;
/**
 * @Author: sj
 * @Date: 2019/11/8 9:24
 */
public enum SafeCheckItemEnum {
    SAFETY_PRF_STEERING(100,"转向系统"),
    SAFETY_PRF_BRAKING(101,"制动系统"),
    SAFETY_PRF_LIGHTING(102,"灯光"),
    SAFETY_PRF_EMAIL_TYRE(103,"油箱轮胎"),

    SAFETY_EQP_PROTECTIVE(200,"防护用品使用情况"),
    SAFETY_EQP_BELT(201,"安全带"),
    SAFETY_EQP_FIRE(202,"消防器材"),
    SAFETY_EQP_FASTENING(203,"各部门紧固"),

    SAFETY_OTHER_TRANS_SSP(300,"传动-悬挂"),
    SAFETY_OTHER_GPS(301,"GPS设备使用情况"),
    ;
    private Integer itemType;
    private String itemName;

    SafeCheckItemEnum(Integer itemType, String itemName){
      this.itemType = itemType;
      this.itemName = itemName;
    }

    public Integer getItemType() { return itemType; }
    public String getItemName() { return itemName; }

    public static SafeCheckItemEnum getEnum(Integer itemType) {
        for (SafeCheckItemEnum t : values()) {
            if (t.getItemType().equals(itemType)) {
                return t;
            }
        }
        return null;
    }
}
