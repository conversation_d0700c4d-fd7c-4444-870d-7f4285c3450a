package com.logistics.management.webapi.controller.freightconfig.mapping;

import com.alibaba.excel.util.DateUtils;
import com.logistics.management.webapi.base.enums.ConfigLabelEnum;
import com.logistics.management.webapi.client.freightconfig.response.CarrierFreightConfigDetailResponseModel;
import com.logistics.management.webapi.controller.freightconfig.response.CarrierFreightConfigDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: sj
 * @Date: 2019/9/29 9:24
 */
public class CarrierFreightConfigDetailMapping extends MapperMapping<CarrierFreightConfigDetailResponseModel, CarrierFreightConfigDetailResponseDto> {
    @Override
    public void configure() {
        CarrierFreightConfigDetailResponseModel source = this.getSource();
        CarrierFreightConfigDetailResponseDto destination = this.getDestination();
        if(source!=null){
            if(Objects.nonNull(source.getConfigType())){
                destination.setConfigTypeLabel(ConfigLabelEnum.getEnum(source.getConfigType()).getValue());
            }
            if(Objects.nonNull(source.getCreatedTime())){
                destination.setCreatedTime(DateUtils.format(source.getCreatedTime()));
            }
            if(Objects.nonNull(source.getLastModifiedTime())){
                destination.setLastModifiedTime(DateUtils.format(source.getLastModifiedTime()));
            }
            if(Objects.nonNull(source.getEntrustType())){
                List<Integer> entryTypes = Arrays.stream(source.getEntrustType().split(","))
                        .map(Integer::parseInt).collect(Collectors.toList());

                destination.setEntrustTypes(entryTypes);
            }
        }
    }
}
