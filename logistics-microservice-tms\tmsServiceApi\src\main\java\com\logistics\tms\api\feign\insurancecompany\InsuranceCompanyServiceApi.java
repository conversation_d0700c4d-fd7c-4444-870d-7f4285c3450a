package com.logistics.tms.api.feign.insurancecompany;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.insurancecompany.hystrix.InsuranceCompanyApiHystrix;
import com.logistics.tms.api.feign.insurancecompany.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/5/29 14:13
 */
@Api(value = "API-InsuranceCompanyApi-配置中心-保险公司管理")
@FeignClient(name = "logistics-tms-services", fallback = InsuranceCompanyApiHystrix.class)
public interface InsuranceCompanyServiceApi {

    @ApiOperation(value = "保险公司列表")
    @PostMapping(value = "/service/insuranceCompany/insuranceCompanyList")
    Result<PageInfo<InsuranceCompanyListResponseModel>> searchInsuranceCompanyList(@RequestBody InsuranceCompanyListRequestModel requestModel);

    @ApiOperation(value = "保险公司新增修改")
    @PostMapping(value = "/service/insuranceCompany/saveInsuranceCompany")
    Result<Boolean> saveOrModifyInsuranceCompany(@RequestBody SaveOrModifyInsuranceCompanyRequestModel requestModel);

    @ApiOperation(value = "查看详情")
    @PostMapping(value = "/service/insuranceCompany/getDetail")
    Result<InsuranceCompanyDetailResponseModel> getDetail(@RequestBody InsuranceCompanyDetailRequestModel requestModel);

    @ApiOperation(value = "启用/禁用保险公司")
    @PostMapping(value = "/service/insuranceCompany/enable")
    Result<Boolean> enableOrDisable(@RequestBody EnableInsuranceCompanyRequestModel requestModel);

    @ApiOperation(value = "导出", notes = "")
    @PostMapping(value = "/service/insuranceCompany/export")
    Result<List<InsuranceCompanyListResponseModel>> export(@RequestBody InsuranceCompanyListRequestModel requestModel);

    @ApiOperation(value = "导入")
    @PostMapping(value = "/service/insuranceCompany/import")
    Result<ImportInsuranceCompanyResponseModel> importInsuranceCompany(@RequestBody ImportInsuranceCompanyRequestModel requestModel);

    @ApiOperation(value = "根据名称模糊匹配保险公司", notes = "")
    @PostMapping(value = "/service/insuranceCompany/fuzzyQuery")
    Result<List<FuzzyQueryInsuranceCompanyListResponseModel>> fuzzyQueryInsuranceCompanyByName(@RequestBody FuzzyQueryInsuranceCompanyRequestModel requestModel);
}
