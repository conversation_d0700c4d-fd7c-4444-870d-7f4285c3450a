package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/25
 */
@Data
public class UpdateLifeCarrierOrderAddressMessage {

	@ApiModelProperty(value = "运单号")
	private String carrierOrderCode;

	@ApiModelProperty("客户单号")
	private String customerOrderCode;

	@ApiModelProperty(value = "收货地code")
	private String toAddressCode;

	@ApiModelProperty(value = "操作人")
	private String userName;
}
