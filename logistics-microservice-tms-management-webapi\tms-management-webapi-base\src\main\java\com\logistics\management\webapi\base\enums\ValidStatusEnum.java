package com.logistics.management.webapi.base.enums;


public enum ValidStatusEnum {
    VALID(1,"有效"),
    INVALID(0,"无效"),
    ;

    private Integer key;
    private String value;

    ValidStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static ValidStatusEnum getEnum(Integer key) {
        for (ValidStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
