package com.logistics.tms.mapper;

import com.logistics.tms.biz.routeenquiry.model.RouteEnquiryQuotedCompanyCountModel;
import com.logistics.tms.controller.routeenquiry.response.RouteEnquiryQuoteListResponseModel;
import com.logistics.tms.entity.TRouteEnquiryCompany;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/07/09
*/
@Mapper
public interface TRouteEnquiryCompanyMapper extends BaseMapper<TRouteEnquiryCompany> {

    TRouteEnquiryCompany selectByPrimaryKeyDecrypt(Long id);

    int batchInsertSelectiveEncrypt(@Param("list") List<TRouteEnquiryCompany> list);

    int updateByPrimaryKeySelectiveEncrypt(TRouteEnquiryCompany tRouteEnquiryCompany);

    List<RouteEnquiryQuotedCompanyCountModel> getQuotedCompanyCount(@Param("routeEnquiryIdList")List<Long> routeEnquiryIdList);

    List<RouteEnquiryQuoteListResponseModel> getQuotedCompanyByRouteEnquiryId(@Param("routeEnquiryId")Long routeEnquiryId);

    TRouteEnquiryCompany getByRouteEnquiryIdAndCompanyId(@Param("routeEnquiryId")Long routeEnquiryId, @Param("companyCarrierId")Long companyCarrierId);

}