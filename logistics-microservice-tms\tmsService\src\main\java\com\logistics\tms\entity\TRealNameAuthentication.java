package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2023/01/10
*/
@Data
public class TRealNameAuthentication extends BaseEntity {
    /**
    * 上上签账户
    */
    @ApiModelProperty("上上签账户")
    private String bestsignAccount;

    /**
    * 认证类型: 1 司机，2 车主和司机，3 解绑
    */
    @ApiModelProperty("认证类型: 1 司机，2 车主和司机，3 解绑")
    private Integer authenticationType;

    /**
    * 认证人名
    */
    @ApiModelProperty("认证人名")
    private String name;

    /**
    * 认证手机号（原长度50）
    */
    @ApiModelProperty("认证手机号（原长度50）")
    private String mobile;

    /**
    * 认证身份证号（原长度50）
    */
    @ApiModelProperty("认证身份证号（原长度50）")
    private String identityNumber;

    /**
    * 0 待认证，1 认证中， 2 认证成功， 3 认证失败
    */
    @ApiModelProperty("0 待认证，1 认证中， 2 认证成功， 3 认证失败")
    private Integer certificationStatus;

    /**
    * 认证模式: 1 个人手机号认证 2 人脸识别
    */
    @ApiModelProperty("认证模式: 1 个人手机号认证 2 人脸识别")
    private Integer authMode;
}