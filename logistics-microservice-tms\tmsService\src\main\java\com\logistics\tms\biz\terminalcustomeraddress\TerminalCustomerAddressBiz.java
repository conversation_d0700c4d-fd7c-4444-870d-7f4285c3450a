package com.logistics.tms.biz.terminalcustomeraddress;

import com.github.pagehelper.PageInfo;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tray.core.exception.BizException;
import com.logistics.tms.api.feign.terminalcustomeraddress.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.EntrustDataExceptionEnum;
import com.logistics.tms.base.enums.IfValidEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TTerminalCustomerAddress;
import com.logistics.tms.mapper.TTerminalCustomerAddressMapper;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
/**
 * @author: wjf
 * @date: 2022/1/4 16:38
 */
public class TerminalCustomerAddressBiz {
    
    @Autowired
    private TTerminalCustomerAddressMapper tTerminalCustomerAddressMapper;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 终端客户地址列表
     * @param requestModel
     * @return
     */
    public PageInfo<SearchTerminalCustomerAddressListResponseModel> searchTerminalCustomerAddressList(SearchTerminalCustomerAddressListRequestModel requestModel) {
        requestModel.enablePaging();
        return new PageInfo<>(tTerminalCustomerAddressMapper.searchTerminalCustomerAddressList(requestModel));
    }

    /**
     * 新增/修改终端客户地址
     * @param requestModel
     * @return
     */
    @Transactional
    public void addOrModifyTerminalCustomerAddress(AddOrModifyTerminalCustomerAddressRequestModel requestModel) {
        TTerminalCustomerAddress tTerminalCustomerAddress = MapperUtils.mapper(requestModel, TTerminalCustomerAddress.class);
        if (requestModel.getTerminalCustomerAddressId() != null && requestModel.getTerminalCustomerAddressId() > CommonConstant.LONG_ZERO){
            TTerminalCustomerAddress dbTerminalCustomerAddress = tTerminalCustomerAddressMapper.selectByPrimaryKey(requestModel.getTerminalCustomerAddressId());
            if (dbTerminalCustomerAddress == null || dbTerminalCustomerAddress.getValid().equals(IfValidEnum.INVALID.getKey())){
                throw new BizException(EntrustDataExceptionEnum.ADDRESS_EMPTY);
            }

            tTerminalCustomerAddress.setId(requestModel.getTerminalCustomerAddressId());
            commonBiz.setBaseEntityModify(tTerminalCustomerAddress, BaseContextHandler.getUserName());
            tTerminalCustomerAddressMapper.updateByPrimaryKeySelective(tTerminalCustomerAddress);
        }else{
            commonBiz.setBaseEntityAdd(tTerminalCustomerAddress, BaseContextHandler.getUserName());
            tTerminalCustomerAddressMapper.insertSelective(tTerminalCustomerAddress);
        }
    }

    /**
     * 终端客户地址详情
     * @param requestModel
     * @return
     */
    public GetTerminalCustomerAddressDetailResponseModel getTerminalCustomerAddressDetail(GetTerminalCustomerAddressDetailRequestModel requestModel) {
        return tTerminalCustomerAddressMapper.getTerminalCustomerAddressDetail(requestModel.getTerminalCustomerAddressId());
    }

    /**
     * 批量删除终端客户地址
     * @param requestModel
     * @return
     */
    @Transactional
    public void delTerminalCustomerAddress(DeleteTerminalCustomerAddressRequestModel requestModel) {
        List<TTerminalCustomerAddress> dbList = tTerminalCustomerAddressMapper.getByIds(requestModel.getTerminalCustomerAddressIds());
        if (ListUtils.isEmpty(dbList)){
            throw new BizException(EntrustDataExceptionEnum.ADDRESS_EMPTY);
        }

        tTerminalCustomerAddressMapper.delTerminalCustomerAddress(requestModel.getTerminalCustomerAddressIds(), BaseContextHandler.getUserName(), new Date());
    }
}
