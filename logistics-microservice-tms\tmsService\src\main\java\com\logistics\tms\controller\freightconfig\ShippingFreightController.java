package com.logistics.tms.controller.freightconfig;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.shippingfreight.ShippingFreightBiz;
import com.logistics.tms.controller.freightconfig.request.AddShippingFreightReqModel;
import com.logistics.tms.controller.freightconfig.request.AssociateCarrierDeleteReqModel;
import com.logistics.tms.controller.freightconfig.request.AssociateCarrierReqModel;
import com.logistics.tms.controller.freightconfig.request.ListShippingFreightListReqModel;
import com.logistics.tms.controller.freightconfig.response.AssociateCarrierListRespModel;
import com.logistics.tms.controller.freightconfig.response.ListShippingFreightListRespModel;
import com.logistics.tms.controller.freightconfig.request.ShippingFreightIdReqModel;
import com.yelo.tray.core.base.Result;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


/**
 * 零担运价管理
 */
@RestController
@RequestMapping(value = "/service/freight/shipping")
public class ShippingFreightController {

    @Resource
    private ShippingFreightBiz shippingFreightBiz;


    /**
     * 零担运价管理列表  v2.42
     * @return
     */
    @PostMapping(value = "/getList")
    public Result<PageInfo<ListShippingFreightListRespModel>> getList(@RequestBody ListShippingFreightListReqModel reqModel) {
        return Result.success(shippingFreightBiz.getList(reqModel));
    }


    /**
     * 零担运价管理新增  v2.42
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/add")
    public Result<Boolean> add(@RequestBody @Valid AddShippingFreightReqModel reqModel) {
        shippingFreightBiz.add(reqModel);
        return Result.success(true);
    }



    /**
     * 零担运价管理关联承运商  v2.42
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/associateCarrier")
    public Result<Boolean> associateCarrier(@RequestBody @Valid AssociateCarrierReqModel reqModel) {
        shippingFreightBiz.associateCarrier(reqModel);
        return Result.success(true);
    }



    /**
     * 关联承运商列表  v2.42
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/associateCarrierList")
    public Result<PageInfo<AssociateCarrierListRespModel>> associateCarrierList(@RequestBody @Valid ShippingFreightIdReqModel reqModel) {
        return Result.success(shippingFreightBiz.associateCarrierList(reqModel));
    }


    /**
     * 关联承运商删除  v2.42
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/associateCarrierDelete")
    public Result<Boolean> associateCarrierDelete(@RequestBody @Valid AssociateCarrierDeleteReqModel reqModel) {
        shippingFreightBiz.associateCarrierDelete(reqModel);
        return Result.success(true);
    }

}
