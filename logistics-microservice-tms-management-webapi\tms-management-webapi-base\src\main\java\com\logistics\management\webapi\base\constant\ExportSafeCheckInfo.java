package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/11/15 9:52
 */
public class ExportSafeCheckInfo {
    private ExportSafeCheckInfo(){

    }

    private static final Map<String,String> EXPORT_SAFECHECK;
    static{
        EXPORT_SAFECHECK = new LinkedHashMap<>();
        EXPORT_SAFECHECK.put("状态", "statusLabel");
        EXPORT_SAFECHECK.put("检查月份", "period");
        EXPORT_SAFECHECK.put("整改项", "reformCount");
        EXPORT_SAFECHECK.put("主车号", "vehicleNo");
        EXPORT_SAFECHECK.put("车辆机构", "vehiclePropertyLabel");
        EXPORT_SAFECHECK.put("挂车号", "trailerVehicleNo");
        EXPORT_SAFECHECK.put("司机", "staffName");
        EXPORT_SAFECHECK.put("检查人", "checkUserName");
        EXPORT_SAFECHECK.put("检查时间", "checkTime");
    }

    public static Map<String,String> getExportSafeCheckInfo(){
        return EXPORT_SAFECHECK;
    }
}
