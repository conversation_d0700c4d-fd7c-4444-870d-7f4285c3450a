package com.logistics.tms.controller.driversafepromise;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.driversafepromise.DriverSafePromiseBiz;
import com.logistics.tms.controller.driversafepromise.request.*;
import com.logistics.tms.controller.driversafepromise.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: wjf
 * @date: 2024/3/14 14:04
 */
@Api(value = "承诺书")
@RestController
public class DriverSafePromiseController {

    @Resource
    private DriverSafePromiseBiz driverSafePromiseBiz;

    /**
     * 列表
     * @param requestModel
     * @return
     */
    @ApiOperation("列表")
    @PostMapping(value = "/service/safePromise/searchList")
    public Result<PageInfo<SearchSafePromiseListResponseModel>> searchList(@RequestBody SearchSafePromiseListRequestModel requestModel) {
        return Result.success(driverSafePromiseBiz.searchList(requestModel));
    }

    /**
     * 新增
     * @param requestModel
     * @return
     */
    @ApiOperation("新增")
    @PostMapping(value = "/service/safePromise/addSafePromise")
    public Result<Boolean> addSafePromise(@RequestBody AddSafePromiseRequestModel requestModel) {
        driverSafePromiseBiz.addSafePromise(requestModel);
        return Result.success(true);
    }

    /**
     * 详情
     * @param requestModel
     * @return
     */
    @ApiOperation("详情")
    @PostMapping(value = "/service/safePromise/getDetail")
    public Result<SafePromiseDetailResponseModel> getDetail(@RequestBody SafePromiseDetailRequestModel requestModel) {
        return Result.success(driverSafePromiseBiz.getDetail(requestModel));
    }

    /**
     * 删除
     * @param requestModel
     * @return
     */
    @ApiOperation("删除")
    @PostMapping(value = "/service/safePromise/delSafePromise")
    public Result<Boolean> delSafePromise(@RequestBody DeleteSafePromiseRequestModel requestModel) {
        driverSafePromiseBiz.delSafePromise(requestModel);
        return Result.success(true);
    }

    /**
     * 补发
     * @param requestModel
     * @return
     */
    @ApiOperation("补发")
    @PostMapping(value = "/service/safePromise/reissueSafePromise")
    public Result<Boolean> reissueSafePromise(@RequestBody ReissueSavePromiseRequestModel requestModel) {
        driverSafePromiseBiz.reissueSafePromise(requestModel);
        return Result.success(true);
    }

    /**
     * 签订列表
     * @param requestModel
     * @return
     */
    @ApiOperation("签订列表")
    @PostMapping(value = "/service/safePromise/searchSignList")
    public Result<PageInfo<SearchSignSafePromiseListResponseModel>> searchSignList(@RequestBody SearchSignSafePromiseListRequestModel requestModel) {
        return Result.success(driverSafePromiseBiz.searchSignList(requestModel));
    }

    /**
     * 签订详情
     * @param requestModel
     * @return
     */
    @ApiOperation("签订详情")
    @PostMapping(value = "/service/safePromise/getSignDetail")
    public Result<SignSafePromiseDetailResponseModel> getSignDetail(@RequestBody  SignSafePromiseDetailRequestModel requestModel) {
        return Result.success(driverSafePromiseBiz.getSignDetail(requestModel));
    }

    /**
     * 签订列表-汇总
     * @param responseModel
     * @return
     */
    @ApiOperation("签订列表汇总")
    @PostMapping(value = "/service/safePromise/getSignSummary")
    public Result<SummarySignSafePromiseResponseModel> getSignSummary(@RequestBody SearchSignSafePromiseListRequestModel responseModel) {
        return Result.success(driverSafePromiseBiz.getSignSummary(responseModel));
    }

    /**
     * 签订列表-上传/重新上传
     * @param requestModel
     * @return
     */
    @ApiOperation("签订列表-上传/重新上传")
    @PostMapping(value = "/service/safePromise/uploadSafePromise")
    public Result<Boolean> uploadSafePromise(@RequestBody UploadSafePromiseRequestModel requestModel) {
        driverSafePromiseBiz.uploadSafePromise(requestModel);
        return Result.success(true);
    }

    /**
     * 小程序承诺书列表
     * @param requestModel
     * @return
     */
    @ApiOperation("小程序承诺书列表")
    @PostMapping(value = "/service/applet/safePromise/searchList")
    public Result<PageInfo<SearchSafePromiseAppletListResponseModel>> searchAppletList(@RequestBody SearchSafePromiseAppletListRequestModel requestModel) {
        return Result.success(driverSafePromiseBiz.searchAppletList(requestModel));
    }

    /**
     * 小程序承诺书列表汇总
     * @param requestModel
     * @return
     */
    @ApiOperation("小程序承诺书列表汇总")
    @PostMapping(value = "/service/applet/safePromise/searchAppletSummary")
    public Result<SummarySignSafePromiseAppletResponseModel> searchAppletSummary(@RequestBody SearchSafePromiseAppletListRequestModel requestModel) {
        return Result.success(driverSafePromiseBiz.getAppletSignSummary(requestModel));
    }

    /**
     * 小程序承诺书详情
     * @param requestModel
     * @return
     */
    @ApiOperation("小程序承诺书详情")
    @PostMapping(value = "/service/applet/safePromise/getDetail")
    public Result<SafePromiseAppletDetailResponseModel> getAppletDetail(@RequestBody SafePromiseAppletDetailRequestModel requestModel) {
        return Result.success(driverSafePromiseBiz.getAppletDetail(requestModel));
    }
}
