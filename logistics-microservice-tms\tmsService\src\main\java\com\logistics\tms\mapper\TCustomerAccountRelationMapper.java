package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TCustomerAccountRelation;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TCustomerAccountRelationMapper extends BaseMapper<TCustomerAccountRelation> {

    TCustomerAccountRelation getRelationByUserRoleAndUserId(@Param("userRole") Integer userRole, @Param("userId") Long userId);

    List<TCustomerAccountRelation> getTCustomerAccountRelationByIds(@Param("userIds") String userIds, @Param("userRole") Integer userRole);

    void batchUpdate(@Param("list") List<TCustomerAccountRelation> list);

}