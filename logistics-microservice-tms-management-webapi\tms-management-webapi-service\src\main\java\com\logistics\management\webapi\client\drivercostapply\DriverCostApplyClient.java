package com.logistics.management.webapi.client.drivercostapply;


import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.drivercostapply.hystrix.DriverCostApplyClientHystrix;
import com.logistics.management.webapi.client.drivercostapply.request.*;
import com.logistics.management.webapi.client.drivercostapply.response.DriverCostApplyDetailResponseModel;
import com.logistics.management.webapi.client.drivercostapply.response.GetDeductionsCostBalanceResponseModel;
import com.logistics.management.webapi.client.drivercostapply.response.SearchCostApplyListResponseModel;
import com.logistics.management.webapi.client.drivercostapply.response.SearchCostApplySummaryResponseModel;
import com.logistics.management.webapi.controller.drivercostapply.request.DeductionsCostApplyRequestModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/3 13:40
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/driverCostApply",
        fallback = DriverCostApplyClientHystrix.class)
public interface DriverCostApplyClient {

    @ApiOperation(value = "费用申请列表")
    @PostMapping(value = "/searchCostApplyList")
    Result<PageInfo<SearchCostApplyListResponseModel>> searchCostApplyList(@RequestBody SearchCostApplyListRequestModel requestModel);

    @ApiOperation(value = "申请记录详情")
    @PostMapping(value = "/driverCostApplyDetail")
    Result<DriverCostApplyDetailResponseModel> driverCostApplyDetail(@RequestBody DriverCostApplyDetailRequestModel requestModel);

    @ApiOperation(value = "撤销费用申请")
    @PostMapping(value = "/undoDriverCostApply")
    Result<Boolean> undoDriverCostApply(@RequestBody UndoDriverCostApplyRequestModel requestModel);

    @ApiOperation(value = "审核/驳回费用申请")
    @PostMapping(value = "/auditOrRejectCostApply")
    Result<Boolean> auditOrRejectCostApply(@RequestBody AuditOrRejectCostApplyRequestModel requestModel);

    @ApiOperation(value = "费用申请汇总列表")
    @PostMapping(value = "/searchCostApplySummary")
    Result<PageInfo<SearchCostApplySummaryResponseModel>> searchCostApplySummary(@RequestBody SearchCostApplySummaryRequestModel requestModel);

    @ApiOperation(value = "司机扣款费用申请")
    @PostMapping(value = "/deductionsCostApply")
    Result<Boolean> deductionsCostApply(@RequestBody DeductionsCostApplyRequestModel requestModel);

    @ApiOperation(value = "查询司机扣款余额")
    @PostMapping(value = "/getDeductionsCostBalance")
    Result<List<GetDeductionsCostBalanceResponseModel>> getDeductionsCostBalance(@RequestBody GetDeductionsCostBalanceRequestModel requestDto);

    @ApiOperation(value = "费用申请红冲退款")
    @PostMapping(value = "/redChargeRefund")
    Result<Boolean> redChargeRefund(@RequestBody RedChargeRefundCostApplyRequestModel requestModel);
}
