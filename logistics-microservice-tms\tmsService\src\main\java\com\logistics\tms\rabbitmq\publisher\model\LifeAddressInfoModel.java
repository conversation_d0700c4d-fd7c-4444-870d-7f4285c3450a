package com.logistics.tms.rabbitmq.publisher.model;

import lombok.Data;

/**
 * 卸货地
 *
 * <AUTHOR>
 * @date 2022/8/20 13:20
 */
@Data
public class LifeAddressInfoModel {
    //卸货地信息(物流信息)
    private String unloadAddressCode;
    private Long unloadProvinceId;
    private String unloadProvinceName;
    private Long unloadCityId;
    private String unloadCityName;
    private Long unloadAreaId;
    private String unloadWarehouse;
    private String unloadAddressDetail;
    private String unloadAddressPerson;
    private String unloadAddressPhone;
    //提货地信息 新生提供的唯一标识
    private String loadAddressCode;
}
