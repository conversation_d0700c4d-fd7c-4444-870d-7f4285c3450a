package com.logistics.tms.api.feign.carrierfreight.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.carrierfreight.CarrierFreightServiceApi;
import com.logistics.tms.api.feign.carrierfreight.model.CarrierFreightAddRequestModel;
import com.logistics.tms.api.feign.carrierfreight.model.CarrierFreightEnableRequestModel;
import com.logistics.tms.api.feign.carrierfreight.model.SearchCarrierFreightRequestModel;
import com.logistics.tms.api.feign.carrierfreight.model.SearchCarrierFreightResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/9/2 14:53
 */
@Component
public class CarrierFreightServiceApiHystrix implements CarrierFreightServiceApi {

    @Override
    public Result<Boolean> addCarrierFreight(CarrierFreightAddRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> enable(CarrierFreightEnableRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<SearchCarrierFreightResponseModel>> searchList(SearchCarrierFreightRequestModel requestDto) {
        return Result.timeout();
    }
}
