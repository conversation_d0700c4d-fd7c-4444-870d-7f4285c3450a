package com.logistics.management.webapi.controller.invoicingmanagement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2024/3/19 16:47
 */
@Data
public class InvoicingManagementDetailRequestDto {

    /**
     * 发票管理id
     */
    @ApiModelProperty(value = "发票管理id",required = true)
    @NotBlank(message = "发票管理id不能为空")
    private String invoicingId;

}
