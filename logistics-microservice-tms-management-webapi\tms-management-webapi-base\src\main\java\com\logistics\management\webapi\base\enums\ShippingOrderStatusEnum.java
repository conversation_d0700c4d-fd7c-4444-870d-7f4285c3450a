package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ShippingOrderStatusEnum {

    DEFAULT(-99, ""),
    AUDIT_WAIT(0, "待审核"),
    AUDIT_THROUGH(1, "已审核"),
    AUDIT_REJECT(2, "已驳回"),
    ;

    private final Integer key;
    private final String value;


    public static ShippingOrderStatusEnum getEnum(Integer key) {
        for (ShippingOrderStatusEnum t : values()) {
            if (t.getKey() .equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}