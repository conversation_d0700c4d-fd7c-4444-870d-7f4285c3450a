package com.logistics.management.webapi.controller.invoicingmanagement.packaging.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2024/3/20 9:17
 */
@Data
public class GetSettleStatementListRequestDto extends AbstractPageForm<GetSettleStatementListRequestDto> {

    @ApiModelProperty(value = "发票管理id",required = true)
    @NotBlank(message = "发票管理id不能为空")
    private String invoicingId;

}
