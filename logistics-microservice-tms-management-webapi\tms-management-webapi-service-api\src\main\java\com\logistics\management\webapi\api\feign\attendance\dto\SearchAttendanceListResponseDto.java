package com.logistics.management.webapi.api.feign.attendance.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 考勤打卡列表查询响应dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/15
 */
@Data
@ExcelIgnoreUnannotated
public class SearchAttendanceListResponseDto {

	@ApiModelProperty("考勤打卡ID")
	private String attendanceRecordId = "";

	@ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
	private String staffProperty = "";

	/*
		以下为导出字段（字段顺序导对应表格列顺序）
	 */

	@ApiModelProperty("考勤用户,姓名_手机号")
	@ExcelProperty(value = "司机")
	private String attendanceUser = "";

	@ApiModelProperty("人员机构展示文本")
	@ExcelProperty(value = "司机机构")
	private String staffPropertyLabel = "";

	@ApiModelProperty("考勤月份")
	@ExcelProperty(value = "考勤月份")
	private String attendanceMonth = "";

	@ApiModelProperty("考勤日期")
	@ExcelProperty(value = "考勤日期")
	private String attendanceDate = "";

	@ApiModelProperty("工时")
	@ExcelProperty(value = "工时（H）")
	private String manHour = "";

	@ApiModelProperty("上班打卡时间")
	@ExcelProperty(value = "上班打卡时间")
	private String onDutyPunchTime = "";

	@ApiModelProperty("下班打卡时间")
	@ExcelProperty(value = "下班打卡时间")
	private String offDutyPunchTime = "";

	@ApiModelProperty("上班打卡地点")
	@ExcelProperty(value = "上班定位地址")
	private String onDutyPunchLocation = "";

	@ApiModelProperty("下班打卡地点")
	@ExcelProperty(value = "下班定位地址")
	private String offDutyPunchLocation = "";


}
