package com.logistics.appapi.controller.driverappoint.response;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchDrierAppointCountResponseDto {

    @ApiModelProperty(value = "预约记录(次数)")
    private String appointCount ="";

    @ApiModelProperty(value = "下单数量(总吨数)")
    private String appointAmountTotal ="";

    @ApiModelProperty(value = "申请记录列表")
    private PageInfo<SearchDrierAppointListResponseDto> modelList;
}
