package com.logistics.management.webapi.controller.settlestatement.tradition.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class CarrierTraditionStatementListResponseDto {

	@ApiModelProperty("对账单id")
	private String settleStatementId = "";

	@ApiModelProperty("对账单状态,-2:已撤销 -1:待提交 0:待业务审核 1:待财务审核 2:已对账 3:已驳回")
	private String settleStatementStatus = "";

	@ApiModelProperty("对账单状态文本")
	@ExcelProperty("对账状态")
	private String settleStatementStatusLabel = "";

	@ApiModelProperty("对账月份")
	@ExcelProperty("费用月份")
	private String settleStatementMonth = "";

	@ApiModelProperty("对账单号")
	@ExcelProperty("对账单号")
	private String settleStatementCode = "";

	@ApiModelProperty("结算主体")
	private String platformCompanyId = "";

	@ExcelProperty("结算主体")
	private String platformCompanyName = "";

	@ApiModelProperty("车主")
	private String companyCarrierId = "";
	private String companyCarrierName = "";

	//"(导出) 车主  企业：企业名 个人：姓名+手机号"
	@ExcelProperty("车主")
	@JsonIgnore
	private String exportCompanyCarrierName = "";

	@ApiModelProperty("关联运单号数量")
	@ExcelProperty("关联运单号")
	private String carrierOrderAmount = "";

	@ApiModelProperty("车主运费")
	@ExcelProperty("运费（元）")
	private String carrierFreight = "";

	@ApiModelProperty("运费费点")
	@ExcelProperty("运费费点（%）")
	private String freightTaxPoint = "";

	@ApiModelProperty("费额合计")
	@ExcelProperty("费额合计（元）")
	private String carrierFreightTotal = "";

	@ApiModelProperty("申请运费总额")
	@ExcelProperty("申请运费总额（元）")
	private String applyFeeTotal = "";

	@ApiModelProperty("差异调整费用")
	@ExcelProperty("差异调整（元）")
	private String adjustFee = "";

	@ApiModelProperty("调整理由")
	@ExcelProperty("差异备注")
	private String adjustRemark = "";

	@ApiModelProperty("对账费用")
	@ExcelProperty("对账费用（元）")
	private String reconciliationFee = "";

	/**
	 * (3.24.0)关联发票：0 否，1 是
	 */
	@ApiModelProperty("关联发票：0 否，1 是")
	private String ifInvoice = "";
	/**
	 * (3.24.0)关联发票文本
	 */
	@ApiModelProperty("关联发票文本")
	@ExcelProperty("关联发票")
	private String ifInvoiceLabel = "";

	@ApiModelProperty("对账单名称")
	@ExcelProperty("对账单名称")
	private String settleStatementName = "";

	@ApiModelProperty("合同号")
	@ExcelProperty("合同号")
	private String contractCode = "";

	@ApiModelProperty("备注")
	@ExcelProperty("备注")
	private String remark = "";

	@ApiModelProperty("创建时间")
	@ExcelProperty("生成日期")
	private String createdTime = "";

	@ApiModelProperty("操作人")
	@ExcelProperty("操作人")
	private String lastModifiedBy = "";

	@ApiModelProperty("操作时间")
	@ExcelProperty("操作时间")
	private String lastModifiedTime = "";
}
