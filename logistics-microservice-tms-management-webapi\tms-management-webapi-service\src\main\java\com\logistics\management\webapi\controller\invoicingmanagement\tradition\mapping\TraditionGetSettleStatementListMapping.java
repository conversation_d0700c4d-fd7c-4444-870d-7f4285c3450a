package com.logistics.management.webapi.controller.invoicingmanagement.tradition.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.invoicingmanagement.response.GetSettleStatementListItemResponseModel;
import com.logistics.management.webapi.client.invoicingmanagement.response.GetSettleStatementListResponseModel;
import com.logistics.management.webapi.controller.invoicingmanagement.tradition.response.TraditionGetSettleStatementListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2024/3/22 13:30
 */
public class TraditionGetSettleStatementListMapping extends MapperMapping<GetSettleStatementListResponseModel, TraditionGetSettleStatementListResponseDto> {
    @Override
    public void configure() {
        GetSettleStatementListResponseModel source = getSource();
        TraditionGetSettleStatementListResponseDto destination = getDestination();

        BigDecimal carrierFreight = BigDecimal.ZERO;//车主运费
        BigDecimal otherFees = BigDecimal.ZERO;//临时费用
        //计算数量
        for (GetSettleStatementListItemResponseModel statementItemModel : source.getItemList()) {
            carrierFreight = carrierFreight.add(statementItemModel.getEntrustFreight());
            otherFees = otherFees.add(statementItemModel.getOtherFees());
        }
        carrierFreight = carrierFreight.setScale(2, BigDecimal.ROUND_HALF_UP);
        otherFees = otherFees.setScale(2, BigDecimal.ROUND_HALF_UP);

        //费额合计
        BigDecimal carrierFreightTotal = carrierFreight.add(carrierFreight.multiply(source.getFreightTaxPoint()).divide(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_NET_ZERO, 2, BigDecimal.ROUND_HALF_UP)).setScale(2, BigDecimal.ROUND_HALF_UP);
        destination.setCarrierFreightTotal(ConverterUtils.toString(carrierFreightTotal));

        //临时运费合计
        BigDecimal otherFeesTotal;
        if (otherFees.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO && source.getOtherFeeTaxPoint().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO){
            otherFeesTotal = otherFees.add(otherFees.multiply(source.getOtherFeeTaxPoint()).divide(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_NET_ZERO, 2, BigDecimal.ROUND_HALF_UP)).setScale(2, BigDecimal.ROUND_HALF_UP);
        }else{
            otherFeesTotal = otherFees;
        }
        destination.setOtherFeeTotal(ConverterUtils.toString(otherFeesTotal));

        //差异调整
        if (source.getAdjustFee().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO){
            destination.setAdjustFee(CommonConstant.PLUS_SYMBOL + ConverterUtils.toString(source.getAdjustFee()));
        }else{
            destination.setAdjustFee(ConverterUtils.toString(source.getAdjustFee()));
        }
    }
}
