package com.logistics.tms.biz.driverappoint.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/25 17:25
 */
@Data
public class CreateCarrierOrderByAssociatedVehicleModel {

    @ApiModelProperty("需求单id")
    private Long demandOrderId;

    @ApiModelProperty("金额合计")
    private BigDecimal goodsPriceTotal;

    @ApiModelProperty("业务类型：1 公司，2 个人")
    private Integer businessType;
    @ApiModelProperty("乐橘新生客户名称（企业）")
    private String customerName;
    @ApiModelProperty("乐橘新生客户姓名（个人）")
    private String customerUserName;
    @ApiModelProperty("乐橘新生客户手机号(（个人）")
    private String customerUserMobile;

    //司机信息
    @ApiModelProperty("司机id")
    private Long staffId;

    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    @ApiModelProperty("司机姓名")
    private String staffName;

    @ApiModelProperty("司机手机号")
    private String staffMobile;

    @ApiModelProperty("司机身份证号码")
    private String driverIdentity;

    //车辆信息
    @ApiModelProperty("车辆ID")
    private Long vehicleId;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty(value = "确认单据")
    private List<String> confirmPictureList;
}
