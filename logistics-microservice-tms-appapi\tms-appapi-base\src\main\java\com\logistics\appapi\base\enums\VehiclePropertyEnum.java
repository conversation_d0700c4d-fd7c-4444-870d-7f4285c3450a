package com.logistics.appapi.base.enums;

/**
 * 车辆机构枚举
 *
 * @Author: sj
 * @Date: 2019/6/10 17:39
 */
public enum VehiclePropertyEnum {
    NULL(-1,""),
    OWNER (1,"自主"),
    OUTSIDE(2,"外部"),
    AFFILIATION(3,"自营"),
    ;

    private Integer key;
    private String value;

    VehiclePropertyEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static VehiclePropertyEnum getEnum(Integer key) {
        for (VehiclePropertyEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return NULL;
    }

    public static VehiclePropertyEnum getEnumByValue(String value) {
        for (VehiclePropertyEnum t : values()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        return null;
    }
}
