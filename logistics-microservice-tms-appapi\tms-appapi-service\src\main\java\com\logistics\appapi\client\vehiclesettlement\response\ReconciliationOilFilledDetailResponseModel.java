package com.logistics.appapi.client.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author：wjf
 * @date：2021/4/12 13:12
 */
@Data
public class ReconciliationOilFilledDetailResponseModel {
    @ApiModelProperty("账单月份")
    private String settlementMonth;
    @ApiModelProperty("充油费用")
    private BigDecimal oilFilledFee;
    @ApiModelProperty("充油费用列表")
    private List<ReconciliationOilFilledListResponseModel> oilFilledList;

    private List<Long> oilFilledIdList;//结算表关联的充油费用ids
}
