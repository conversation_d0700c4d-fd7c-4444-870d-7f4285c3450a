package com.logistics.management.webapi.base.enums;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/10/9
 * @description:
 */
public enum OilFilledTypeEnum {

    OIL_FILLED_CARD(1, "充油卡"),
    OIL_FILLED_CAR(2, "充油车"),
    DEFAULT(-999,""),

    ;



    private Integer key;
    private String value;

    OilFilledTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static OilFilledTypeEnum getEnum(Integer key) {
        for (OilFilledTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }


}
