package com.logistics.management.webapi.api.feign.driversafepromise.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 新增
 * @Author: sj
 * @Date: 2019/11/4 10:11
 */
@Data
public class AddSafePromiseRequestDto {
    @ApiModelProperty("标题")
    @Size(min = 1,max = 255,message = "承诺书标题1-255字范围")
    private String title;
    @ApiModelProperty("周期")
    private String period;
    @Pattern(regexp = "^[\\u4E00-\\u9FA5A-Za-z0-9]{2,10}$",message = "请维护正确的经办人名称")
    @ApiModelProperty("经办人")
    private String agent;
    @ApiModelProperty("内容")
    @Size(max = 20000, message = "承诺书内容0-10000字范围")
    private String content;
    @ApiModelProperty("附件")
    @NotBlank(message = "请上传附件")
    private String attachmentUrl;
    @ApiModelProperty("承诺司机列表")
    private List<String> driverList;
}
