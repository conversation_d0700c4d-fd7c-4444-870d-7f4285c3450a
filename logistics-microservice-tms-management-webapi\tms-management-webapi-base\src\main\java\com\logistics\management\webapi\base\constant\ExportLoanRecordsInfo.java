package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/10/9 10:26
 */
public class ExportLoanRecordsInfo {
    private ExportLoanRecordsInfo() {

    }

    private static final Map<String, String> EXPORT_LOAN_RECORD_MAP;

    static {
        EXPORT_LOAN_RECORD_MAP = new LinkedHashMap<>();
        EXPORT_LOAN_RECORD_MAP.put("贷款状态", "statusLabel");
        EXPORT_LOAN_RECORD_MAP.put("车牌号", "vehicleNo");
        EXPORT_LOAN_RECORD_MAP.put("司机", "name");
        EXPORT_LOAN_RECORD_MAP.put("手机号", "mobile");
        EXPORT_LOAN_RECORD_MAP.put("品牌型号", "brandModel");
        EXPORT_LOAN_RECORD_MAP.put("购车总价", "carPrice");
        EXPORT_LOAN_RECORD_MAP.put("贷款总额", "loanFee");
        EXPORT_LOAN_RECORD_MAP.put("贷款期数", "loanPeriods");
        EXPORT_LOAN_RECORD_MAP.put("贷款利率(年利率)", "loanRate");
        EXPORT_LOAN_RECORD_MAP.put("贷款利息", "loanInterest");
        EXPORT_LOAN_RECORD_MAP.put("手续费", "loanCommission");
        EXPORT_LOAN_RECORD_MAP.put("剩余金额", "remainingRepaymentFee");
        EXPORT_LOAN_RECORD_MAP.put("备注", "remark");
        EXPORT_LOAN_RECORD_MAP.put("操作人", "lastModifiedBy");
        EXPORT_LOAN_RECORD_MAP.put("操作时间", "lastModifiedTime");
    }

    public static Map<String, String> getExportLoanRecordMap() {
        return EXPORT_LOAN_RECORD_MAP;
    }
}
