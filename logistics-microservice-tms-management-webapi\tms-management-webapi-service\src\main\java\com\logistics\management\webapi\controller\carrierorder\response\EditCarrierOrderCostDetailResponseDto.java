package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/1
 */
@Data
public class EditCarrierOrderCostDetailResponseDto {

	@ApiModelProperty(value = "运单ID")
	private String carrierOrderId = "";

	@ApiModelProperty(value = "价格类型 1 单价 2 一口价")
	private String priceType;

	@ApiModelProperty("货物单位：件，吨, 方, 块")
	private String goodsUnitLabel = "";

	@ApiModelProperty("货物数量")
	private String goodsAmount = "";

	@ApiModelProperty("车主费用/货主费用")
	private String carrierFreight = "";
}
