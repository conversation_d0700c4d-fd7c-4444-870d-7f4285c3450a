package com.logistics.tms.base.enums;

import com.logistics.tms.base.constant.CommonConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CarrierFreightConfigPriceDesignModeEnum {
    FIXED_UNIT_PRICE(1, "固定单价"),
    LADDER_UNIT_PRICE(2, "阶梯单价"),
    LADDER_TOTAL_PRICE(3, "阶梯总价"),
    MULTI_LADDER_UNIT_PRICE(4, "多级阶梯单价"),
    MULTI_LADDER_TOTAL_PRICE(5, "多级阶梯总价"),
    ;

    private final Integer key;

    private final String value;

    public static CarrierFreightConfigPriceDesignModeEnum getEnumByLadderLevel(Integer level, Integer priceMode) {

        boolean isUnitPrice = priceMode.equals(CommonConstant.INTEGER_ONE);

        // 阶梯
        if (level.equals(CommonConstant.INTEGER_ONE)) {
            if (isUnitPrice) {
                return LADDER_UNIT_PRICE;
            }
            return LADDER_TOTAL_PRICE;
        }
        // 多级阶梯
        if (level > CommonConstant.INTEGER_ONE) {
            if (isUnitPrice) {
                return MULTI_LADDER_UNIT_PRICE;
            }
            return MULTI_LADDER_TOTAL_PRICE;
        }
        // 固定
        else {
            return FIXED_UNIT_PRICE;
        }
    }
}
