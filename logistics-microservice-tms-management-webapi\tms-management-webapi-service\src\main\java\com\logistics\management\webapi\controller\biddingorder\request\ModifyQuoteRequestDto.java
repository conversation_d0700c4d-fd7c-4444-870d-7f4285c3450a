package com.logistics.management.webapi.controller.biddingorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ModifyQuoteRequestDto {

    /**
     * 报价单id
     */
    @ApiModelProperty("报价单id")
    @NotBlank(message = "报价单id不能为空")
    private String biddingOrderQuoteId;

    /**
     * 竞价金额类型：1 单价，2 一口价
     */
    @ApiModelProperty("竞价金额类型：1 单价，2 一口价")
    @NotBlank(message = "竞价金额类型不能为空")
    private String biddingPriceType;

    /**
     * 报价金额
     */
    @ApiModelProperty("报价金额")
    @NotBlank(message = "报价金额不能为空")
    private String biddingPrice;

    /**
     * 车长id
     */
    @ApiModelProperty("车长id")
    @NotBlank(message = "车长id不能为空")
    private String vehicleLengthId;

    /**
     * 车长
     */
    @ApiModelProperty("车长")
    @NotBlank(message = "车长不能为空")
    private String vehicleLength;




}
