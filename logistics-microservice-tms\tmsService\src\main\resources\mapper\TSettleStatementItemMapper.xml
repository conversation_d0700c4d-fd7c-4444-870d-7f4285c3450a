<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TSettleStatementItemMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TSettleStatementItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="settle_statement_id" jdbcType="BIGINT" property="settleStatementId" />
    <result column="carrier_order_id" jdbcType="BIGINT" property="carrierOrderId" />
    <result column="carrier_order_code" jdbcType="VARCHAR" property="carrierOrderCode" />
    <result column="settlement_amount" jdbcType="DECIMAL" property="settlementAmount" />
    <result column="goods_unit" jdbcType="INTEGER" property="goodsUnit" />
    <result column="entrust_freight" jdbcType="DECIMAL" property="entrustFreight" />
    <result column="other_fees" jdbcType="DECIMAL" property="otherFees" />
    <result column="payable_fee" jdbcType="DECIMAL" property="payableFee" />
    <result column="company_entrust_id" jdbcType="BIGINT" property="companyEntrustId" />
    <result column="company_entrust_name" jdbcType="VARCHAR" property="companyEntrustName" />
    <result column="company_carrier_id" jdbcType="BIGINT" property="companyCarrierId" />
    <result column="company_carrier_name" jdbcType="VARCHAR" property="companyCarrierName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="if_archive" jdbcType="INTEGER" property="ifArchive" />
    <result column="archive_remark" jdbcType="VARCHAR" property="archiveRemark" />
    <result column="archive_by" jdbcType="VARCHAR" property="archiveBy" />
    <result column="archive_time" jdbcType="TIMESTAMP" property="archiveTime" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, settle_statement_id, carrier_order_id, carrier_order_code, settlement_amount, 
    goods_unit, entrust_freight, other_fees, payable_fee, company_entrust_id, company_entrust_name, 
    company_carrier_id, company_carrier_name, contact_phone, contact_name, type, if_archive, 
    archive_remark, archive_by, archive_time, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_settle_statement_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_settle_statement_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TSettleStatementItem">
    insert into t_settle_statement_item (id, settle_statement_id, carrier_order_id, 
      carrier_order_code, settlement_amount, goods_unit, 
      entrust_freight, other_fees, payable_fee, 
      company_entrust_id, company_entrust_name, company_carrier_id, 
      company_carrier_name, contact_phone, contact_name, 
      type, if_archive, archive_remark, 
      archive_by, archive_time, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{settleStatementId,jdbcType=BIGINT}, #{carrierOrderId,jdbcType=BIGINT}, 
      #{carrierOrderCode,jdbcType=VARCHAR}, #{settlementAmount,jdbcType=DECIMAL}, #{goodsUnit,jdbcType=INTEGER}, 
      #{entrustFreight,jdbcType=DECIMAL}, #{otherFees,jdbcType=DECIMAL}, #{payableFee,jdbcType=DECIMAL}, 
      #{companyEntrustId,jdbcType=BIGINT}, #{companyEntrustName,jdbcType=VARCHAR}, #{companyCarrierId,jdbcType=BIGINT}, 
      #{companyCarrierName,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR}, #{contactName,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{ifArchive,jdbcType=INTEGER}, #{archiveRemark,jdbcType=VARCHAR}, 
      #{archiveBy,jdbcType=VARCHAR}, #{archiveTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TSettleStatementItem">
    insert into t_settle_statement_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="settleStatementId != null">
        settle_statement_id,
      </if>
      <if test="carrierOrderId != null">
        carrier_order_id,
      </if>
      <if test="carrierOrderCode != null">
        carrier_order_code,
      </if>
      <if test="settlementAmount != null">
        settlement_amount,
      </if>
      <if test="goodsUnit != null">
        goods_unit,
      </if>
      <if test="entrustFreight != null">
        entrust_freight,
      </if>
      <if test="otherFees != null">
        other_fees,
      </if>
      <if test="payableFee != null">
        payable_fee,
      </if>
      <if test="companyEntrustId != null">
        company_entrust_id,
      </if>
      <if test="companyEntrustName != null">
        company_entrust_name,
      </if>
      <if test="companyCarrierId != null">
        company_carrier_id,
      </if>
      <if test="companyCarrierName != null">
        company_carrier_name,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="contactName != null">
        contact_name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="ifArchive != null">
        if_archive,
      </if>
      <if test="archiveRemark != null">
        archive_remark,
      </if>
      <if test="archiveBy != null">
        archive_by,
      </if>
      <if test="archiveTime != null">
        archive_time,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="settleStatementId != null">
        #{settleStatementId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderId != null">
        #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null">
        #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="settlementAmount != null">
        #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="goodsUnit != null">
        #{goodsUnit,jdbcType=INTEGER},
      </if>
      <if test="entrustFreight != null">
        #{entrustFreight,jdbcType=DECIMAL},
      </if>
      <if test="otherFees != null">
        #{otherFees,jdbcType=DECIMAL},
      </if>
      <if test="payableFee != null">
        #{payableFee,jdbcType=DECIMAL},
      </if>
      <if test="companyEntrustId != null">
        #{companyEntrustId,jdbcType=BIGINT},
      </if>
      <if test="companyEntrustName != null">
        #{companyEntrustName,jdbcType=VARCHAR},
      </if>
      <if test="companyCarrierId != null">
        #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null">
        #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="ifArchive != null">
        #{ifArchive,jdbcType=INTEGER},
      </if>
      <if test="archiveRemark != null">
        #{archiveRemark,jdbcType=VARCHAR},
      </if>
      <if test="archiveBy != null">
        #{archiveBy,jdbcType=VARCHAR},
      </if>
      <if test="archiveTime != null">
        #{archiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TSettleStatementItem">
    update t_settle_statement_item
    <set>
      <if test="settleStatementId != null">
        settle_statement_id = #{settleStatementId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderId != null">
        carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null">
        carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="settlementAmount != null">
        settlement_amount = #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=INTEGER},
      </if>
      <if test="entrustFreight != null">
        entrust_freight = #{entrustFreight,jdbcType=DECIMAL},
      </if>
      <if test="otherFees != null">
        other_fees = #{otherFees,jdbcType=DECIMAL},
      </if>
      <if test="payableFee != null">
        payable_fee = #{payableFee,jdbcType=DECIMAL},
      </if>
      <if test="companyEntrustId != null">
        company_entrust_id = #{companyEntrustId,jdbcType=BIGINT},
      </if>
      <if test="companyEntrustName != null">
        company_entrust_name = #{companyEntrustName,jdbcType=VARCHAR},
      </if>
      <if test="companyCarrierId != null">
        company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null">
        company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="ifArchive != null">
        if_archive = #{ifArchive,jdbcType=INTEGER},
      </if>
      <if test="archiveRemark != null">
        archive_remark = #{archiveRemark,jdbcType=VARCHAR},
      </if>
      <if test="archiveBy != null">
        archive_by = #{archiveBy,jdbcType=VARCHAR},
      </if>
      <if test="archiveTime != null">
        archive_time = #{archiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TSettleStatementItem">
    update t_settle_statement_item
    set settle_statement_id = #{settleStatementId,jdbcType=BIGINT},
      carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      settlement_amount = #{settlementAmount,jdbcType=DECIMAL},
      goods_unit = #{goodsUnit,jdbcType=INTEGER},
      entrust_freight = #{entrustFreight,jdbcType=DECIMAL},
      other_fees = #{otherFees,jdbcType=DECIMAL},
      payable_fee = #{payableFee,jdbcType=DECIMAL},
      company_entrust_id = #{companyEntrustId,jdbcType=BIGINT},
      company_entrust_name = #{companyEntrustName,jdbcType=VARCHAR},
      company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      contact_phone = #{contactPhone,jdbcType=VARCHAR},
      contact_name = #{contactName,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      if_archive = #{ifArchive,jdbcType=INTEGER},
      archive_remark = #{archiveRemark,jdbcType=VARCHAR},
      archive_by = #{archiveBy,jdbcType=VARCHAR},
      archive_time = #{archiveTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>