package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author:lei.zhu
 * @date:2021/12/28 15:18:43
 */
@Data
public class ProductTypeAndBackAmountModel {
    @ApiModelProperty("sku code")
    private String productTypeCode;

    @ApiModelProperty(value = "大类名称")
    private String categoryName;

    @ApiModelProperty(value = "托盘品名")
    private String sortName;

    @ApiModelProperty(value = "长")
    private String length;

    @ApiModelProperty(value = "宽")
    private String width;

    @ApiModelProperty(value = "高")
    private String height;

    @ApiModelProperty(value = "物流需求单回退数量")
    private Integer completeBackAmount;
}
