package com.logistics.management.webapi.api.feign.freight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 统一加价/减价
 * @Author: sj
 * @Date: 2019/12/24 13:26
 */
@Data
public class ModifyFreightPriceRequestDto {
    @NotBlank
    @ApiModelProperty("运价规则地址ID")
    private String freightAddressIds;
    @NotBlank
    @ApiModelProperty("加价还是减价 1 加价 2 减价")
    private String calcType;
    @NotBlank
    @ApiModelProperty("加减价类型 1 百分比 2 元")
    private String unitType;
    @ApiModelProperty("加减价数量")
    @NotBlank(message = "请输入统一加减价数量")
    private String amount;
}
