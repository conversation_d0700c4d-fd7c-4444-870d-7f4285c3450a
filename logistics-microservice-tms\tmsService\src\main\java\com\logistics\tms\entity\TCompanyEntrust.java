package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TCompanyEntrust extends BaseEntity {
    /**
    * 主公司id
    */
    @ApiModelProperty("主公司id")
    private Long companyId;

    /**
    * 公司简称
    */
    @ApiModelProperty("公司简称")
    private String companyShortName;

    /**
    * 结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位
    */
    @ApiModelProperty("结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer settlementTonnage;

    /**
    * 修改车辆是否审核：0 否，1 是
    */
    @ApiModelProperty("修改车辆是否审核：0 否，1 是")
    private Integer ifAudit;

    /**
    * 货主类型 1公司 2 个人
    */
    @ApiModelProperty("货主类型 1公司 2 个人")
    private Integer type;

    /**
    * 签收模式 1 人工签收 2 自动签收
    */
    @ApiModelProperty("签收模式 1 人工签收 2 自动签收")
    private Integer signMode;

    /**
    * 自动签收天数
    */
    @ApiModelProperty("自动签收天数")
    private Integer signDays;

    /**
    * 是否启用,1启用,0禁用
    */
    @ApiModelProperty("是否启用,1启用,0禁用")
    private Integer enabled;

    /**
    * 来源 1 后台添加 2 web注册
    */
    @ApiModelProperty("来源 1 后台添加 2 web注册")
    private Integer source;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}