package com.logistics.tms.controller.reservationorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/19 09:48
 */
@Data
public class ConfirmReservationRequestModel {

    /**
     * 预约类型：1 提货，2 卸货
     */
    private Integer reservationType;

    /**
     * 运单id
     */
    private List<Long> carrierOrderIdList;

    /**
     * 预约时间段类型：1 今天，2 明天
     */
    private Integer reservationTimeType;

    /**
     * 预约时间段
     */
    private String reservationTime;


    /**
     * 来源 0。小程序司机 1.h5访客越野 2车主前台
     */
    private Integer source;

    /**
     * 访客手机号
     */
    private String mobilePhone;


    /**
     * 预计入库数
     */
    @ApiModelProperty("预计入库数")
    private BigDecimal expectedStockIn;




}
