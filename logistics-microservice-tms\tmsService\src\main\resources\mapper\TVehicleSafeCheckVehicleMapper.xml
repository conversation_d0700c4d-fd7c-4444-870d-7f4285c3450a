<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TVehicleSafeCheckVehicleMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TVehicleSafeCheckVehicle">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="safe_check_id" jdbcType="BIGINT" property="safeCheckId" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_property" jdbcType="INTEGER" property="vehicleProperty" />
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="trailer_vehicle_no" jdbcType="VARCHAR" property="trailerVehicleNo" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
    <result column="staff_mobile" jdbcType="VARCHAR" property="staffMobile" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="check_time" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="check_user_id" jdbcType="BIGINT" property="checkUserId" />
    <result column="check_user_name" jdbcType="VARCHAR" property="checkUserName" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, safe_check_id, vehicle_id, vehicle_property, vehicle_no, trailer_vehicle_no, 
    staff_id, staff_name, staff_mobile, status, check_time, check_user_id, check_user_name, 
    created_by, created_time, last_modified_by, last_modified_time, remark, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_vehicle_safe_check_vehicle
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_vehicle_safe_check_vehicle
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TVehicleSafeCheckVehicle">
    insert into t_vehicle_safe_check_vehicle (id, safe_check_id, vehicle_id, 
      vehicle_property, vehicle_no, trailer_vehicle_no, 
      staff_id, staff_name, staff_mobile, 
      status, check_time, check_user_id, 
      check_user_name, created_by, created_time, 
      last_modified_by, last_modified_time, remark, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{safeCheckId,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, 
      #{vehicleProperty,jdbcType=INTEGER}, #{vehicleNo,jdbcType=VARCHAR}, #{trailerVehicleNo,jdbcType=VARCHAR}, 
      #{staffId,jdbcType=BIGINT}, #{staffName,jdbcType=VARCHAR}, #{staffMobile,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{checkTime,jdbcType=TIMESTAMP}, #{checkUserId,jdbcType=BIGINT}, 
      #{checkUserName,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TVehicleSafeCheckVehicle"  keyProperty="id" useGeneratedKeys="true">
    insert into t_vehicle_safe_check_vehicle
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="safeCheckId != null">
        safe_check_id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleProperty != null">
        vehicle_property,
      </if>
      <if test="vehicleNo != null">
        vehicle_no,
      </if>
      <if test="trailerVehicleNo != null">
        trailer_vehicle_no,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="staffName != null">
        staff_name,
      </if>
      <if test="staffMobile != null">
        staff_mobile,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="checkTime != null">
        check_time,
      </if>
      <if test="checkUserId != null">
        check_user_id,
      </if>
      <if test="checkUserName != null">
        check_user_name,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="safeCheckId != null">
        #{safeCheckId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleProperty != null">
        #{vehicleProperty,jdbcType=INTEGER},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="trailerVehicleNo != null">
        #{trailerVehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffName != null">
        #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null">
        #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkUserId != null">
        #{checkUserId,jdbcType=BIGINT},
      </if>
      <if test="checkUserName != null">
        #{checkUserName,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TVehicleSafeCheckVehicle">
    update t_vehicle_safe_check_vehicle
    <set>
      <if test="safeCheckId != null">
        safe_check_id = #{safeCheckId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleProperty != null">
        vehicle_property = #{vehicleProperty,jdbcType=INTEGER},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="trailerVehicleNo != null">
        trailer_vehicle_no = #{trailerVehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffName != null">
        staff_name = #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null">
        staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        check_time = #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkUserId != null">
        check_user_id = #{checkUserId,jdbcType=BIGINT},
      </if>
      <if test="checkUserName != null">
        check_user_name = #{checkUserName,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TVehicleSafeCheckVehicle">
    update t_vehicle_safe_check_vehicle
    set safe_check_id = #{safeCheckId,jdbcType=BIGINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_property = #{vehicleProperty,jdbcType=INTEGER},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      trailer_vehicle_no = #{trailerVehicleNo,jdbcType=VARCHAR},
      staff_id = #{staffId,jdbcType=BIGINT},
      staff_name = #{staffName,jdbcType=VARCHAR},
      staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      check_time = #{checkTime,jdbcType=TIMESTAMP},
      check_user_id = #{checkUserId,jdbcType=BIGINT},
      check_user_name = #{checkUserName,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>