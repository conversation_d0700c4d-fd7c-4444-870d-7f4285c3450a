package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.driverpayee.model.ViewLogResponseModel;
import com.logistics.tms.entity.TOperateLogs;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TOperateLogsMapper extends BaseMapper<TOperateLogs> {

    List<ViewLogResponseModel> selectLogsByCondition(@Param("objectType")Integer type, @Param("objectId")Long objectId, @Param("operateType")Integer operateType);

    int batchInsert(@Param("list") List<TOperateLogs> list);

    List<ViewLogResponseModel> selectLogsByOperateType(@Param("objectType")Integer type, @Param("objectId")Long objectId, @Param("operateType")String operateType);

}