/**
 * Created by yun<PERSON><PERSON><PERSON> on 2017/12/12.
 */
package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum InvoiceTypeEnum {
    DEFAULT(-1, "-1",""),

    VALUE_ADDED_TAX(1, "1","增值税发票"),
    TAXI(2, "2","出租车发票"),
    TRAIN(3, "3","火车票"),
    QUOTA(4, "4","定额发票"),
    ROLL_TICKET(5, "5","卷票"),
    MACHINE_PRINTING(6, "6","机打发票"),
    PASSING_BY(7, "7","过路过桥费发票"),
    ;

    private final Integer key;
    private final String stringKey;
    private final String value;

    public static InvoiceTypeEnum getEnum(Integer key) {
        for (InvoiceTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
