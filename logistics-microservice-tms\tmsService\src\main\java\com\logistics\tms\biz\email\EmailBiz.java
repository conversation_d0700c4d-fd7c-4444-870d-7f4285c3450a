package com.logistics.tms.biz.email;

import com.logistics.tms.api.feign.freight.model.GetPriceByAddressAndAmountRequestModel;
import com.logistics.tms.api.feign.freight.model.GetPriceByAddressAndAmountResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.demandorder.DemandOrderCommonBiz;
import com.logistics.tms.biz.freight.FreightBiz;
import com.logistics.tms.biz.sysconfig.SysConfigBiz;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.MapUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: wjf
 * @date: 2019/2/25 14:53
 */
@Slf4j
@Service
public class EmailBiz {

    @Autowired
    private ReceiveEmailBiz receiveEmailBiz;
    @Autowired
    private TCompanyEntrustMapper companyEntrustMapper;
    @Autowired
    private TCompanyCarrierMapper companyCarrierMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TDemandOrderMapper demandOrderMapper;
    @Autowired
    private TDemandOrderAddressMapper demandOrderAddressMapper;
    @Autowired
    private TDemandOrderGoodsMapper demandOrderGoodsMapper;
    @Autowired
    private TDemandOrderEventsMapper demandOrderEventsMapper;
    @Autowired
    private TDemandOrderOperateLogsMapper demandOrderOperateLogsMapper;
    @Autowired
    private FreightBiz freightBiz;
    @Autowired
    private DemandOrderCommonBiz demandOrderCommonBiz;
    @Autowired
    private TDemandOrderCarrierMapper tDemandOrderCarrierMapper;
    @Resource
    private SysConfigBiz sysConfigBiz;

    /**
     * 从扬巴邮件附件里解析pdf相关内容生成需求单（每3分钟执行一次）
     */
    @Transactional
    public void processSaveDemandOrder(){
        try{
            //解析邮件内容
            List<Map<String,Object>> orderMapList = getEmailText();
            if (ListUtils.isNotEmpty(orderMapList) && !MapUtils.isEmpty(orderMapList.get(CommonConstant.INTEGER_ZERO))) {
                //生成需求单
                saveDemandOrder(orderMapList);
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
        }
    }

    //生成需求单
    public void saveDemandOrder(List<Map<String,Object>> orderMapList){
        if (ListUtils.isNotEmpty(orderMapList)){
            Map<String, String> keyMap = sysConfigBiz.getSysConfig(ConfigKeyEnum.ConfigGroupCodeEnum.COMPANY_NAME.getCode());
            String companyEntrustName = keyMap.get(ConfigKeyEnum.YANGBA_COMPANY_NAME.getValue());
            TCompanyEntrust companyEntrust = companyEntrustMapper.getByName(companyEntrustName);
            if (companyEntrust == null){
                throw new BizException(CarrierDataExceptionEnum.COMPANY_ENTRUST_EMPTY);
            }
            Long companyCarrierId = CommonConstant.LONG_ZERO;
            String companyCarrierName = keyMap.get(ConfigKeyEnum.QIYA_COMPANY_NAME.getValue());
            Integer companyCarrierLevel = null;
            TCompanyCarrier tCompanyCarrier = companyCarrierMapper.getByName(companyCarrierName);
            if (tCompanyCarrier != null){
                companyCarrierId = tCompanyCarrier.getId();
                companyCarrierLevel = tCompanyCarrier.getLevel();
            }

            List<String> customerOrderCodeList = new ArrayList<>();
            List<String> codeList = new ArrayList<>();
            orderMapList.forEach(item ->
                customerOrderCodeList.add((String)item.get(EmailConstant.CUSTOMER_ORDER_CODE))
            );
            if (ListUtils.isNotEmpty(customerOrderCodeList)){
                codeList = demandOrderMapper.getExistCodesByCustomerCodes(customerOrderCodeList);
                if (codeList == null){
                    codeList = new ArrayList<>();
                }
            }
            TDemandOrder demandOrder;
            TDemandOrderAddress demandOrderAddress;
            TDemandOrderGoods demandOrderGoods;
            TDemandOrderEvents demandOrderEvents;
            TDemandOrderOperateLogs demandOrderOperateLogs;
            List<TDemandOrderAddress> demandOrderAddressList = new ArrayList<>();
            List<TDemandOrderGoods> demandOrderGoodsList = new ArrayList<>();
            List<TDemandOrderEvents> demandOrderEventsList = new ArrayList<>();
            List<TDemandOrderOperateLogs> demandOrderOperateLogsList = new ArrayList<>();
            BigDecimal goodsAmount;
            Date now = new Date();
            Map<String,String> addressMap = new HashMap<>();
            for (Map<String,Object> map:orderMapList) {
                if (codeList.contains(map.get(EmailConstant.CUSTOMER_ORDER_CODE))){//如果库里已存在此客户单号，则不用重复插入
                    log.info(map.get(EmailConstant.CUSTOMER_ORDER_CODE)+CarrierDataExceptionEnum.CUSTOMER_ORDER_CODE_EXIST.getValue());
                    continue;
                }
                List<TDemandOrderGoods> goodsList = (List<TDemandOrderGoods>)map.get(EmailConstant.GOODS);
                goodsAmount = kgToTons((String)map.get(EmailConstant.GOODS_AMOUNT));
                try {
                    addressMap = commonBiz.getProvinceCityArea((String)map.get(EmailConstant.REMARK));
                    if (MapUtils.isEmpty(addressMap)) {
                        addressMap = commonBiz.getProvinceCityArea((String) map.get(EmailConstant.ADDRESS_ONE));
                        if (MapUtils.isEmpty(addressMap)) {
                            addressMap = commonBiz.getProvinceCityArea((String) map.get(EmailConstant.ADDRESS_TWO));
                        }
                    }
                } catch (Exception e) {
                    log.info(e.getMessage(),e);
                }
                //生成需求单
                demandOrder = new TDemandOrder();
                demandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
                demandOrder.setStatusUpdateTime(now);
                demandOrder.setStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
                demandOrder.setDemandOrderCode(commonBiz.getBusinessTypeCode(BusinessCodeTypeEnum.DEMAND_ORDER_CODE,"", (String)map.get(EmailConstant.CONSIGNOR_NAME)));
                demandOrder.setCustomerOrderCode((String)map.get(EmailConstant.CUSTOMER_ORDER_CODE));
                demandOrder.setPublishName((String)map.get(EmailConstant.CONSIGNOR_NAME));
                demandOrder.setPublishTime(getTime((String)map.get(EmailConstant.PUBLISH_TIME)));
                demandOrder.setSettlementTonnage(companyEntrust.getSettlementTonnage());
                demandOrder.setGoodsAmount(goodsAmount);
                demandOrder.setNotArrangedAmount(goodsAmount);
                demandOrder.setContractPriceType(0);
                demandOrder.setContractPrice(ConverterUtils.toBigDecimal(0));
                demandOrder.setGoodsUnit(GoodsUnitEnum.BY_WEIGHT.getKey());
                demandOrder.setCompanyEntrustId(companyEntrust.getId());
                demandOrder.setCompanyEntrustName(companyEntrustName);
                demandOrder.setCompanyCarrierId(companyCarrierId);
                demandOrder.setCompanyCarrierName(companyCarrierName);
                demandOrder.setCompanyCarrierLevel(companyCarrierLevel);
                demandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
                demandOrder.setStatusUpdateTime(now);
                demandOrder.setStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
                demandOrder.setSource(DemandOrderSourceEnum.PDF_MAIL.getKey());
                demandOrder.setRemark((String)map.get(EmailConstant.REMARK));
                commonBiz.setBaseEntityAdd(demandOrder,(String)map.get(EmailConstant.CONSIGNOR_NAME));
                demandOrderMapper.insertSelectiveEncrypt(demandOrder);

                //生成需求单地址
                demandOrderAddress = new TDemandOrderAddress();
                demandOrderAddress.setDemandOrderId(demandOrder.getId());
                demandOrderAddress.setLoadProvinceId(EmailConstant.PROVINCE_ID);
                demandOrderAddress.setLoadProvinceName(EmailConstant.PROVINCE_NAME);
                demandOrderAddress.setLoadCityId(EmailConstant.CITY_ID);
                demandOrderAddress.setLoadCityName(EmailConstant.CITY_NAME);
                demandOrderAddress.setLoadAreaId(EmailConstant.AREA_ID);
                demandOrderAddress.setLoadAreaName(EmailConstant.AREA_NAME);
                demandOrderAddress.setLoadDetailAddress("");
                demandOrderAddress.setLoadWarehouse((String)map.get(EmailConstant.LOAD_WAREHOUSE));
                demandOrderAddress.setConsignorName((String)map.get(EmailConstant.CONSIGNOR_NAME));
                demandOrderAddress.setConsignorMobile("");
                demandOrderAddress.setExpectedLoadTime(getTime((String)map.get(EmailConstant.EXPECTED_LOAD_TIME)));
                demandOrderAddress.setUnloadProvinceId(ConverterUtils.toLong(addressMap.get(EmailConstant.UNLOAD_PROVINCE_ID),0L));
                demandOrderAddress.setUnloadProvinceName(addressMap.get(EmailConstant.UNLOAD_PROVINCE_NAME));
                demandOrderAddress.setUnloadCityId(ConverterUtils.toLong(addressMap.get(EmailConstant.UNLOAD_CITY_ID),0L));
                demandOrderAddress.setUnloadCityName(addressMap.get(EmailConstant.UNLOAD_CITY_NAME));
                demandOrderAddress.setUnloadAreaId(ConverterUtils.toLong(addressMap.get(EmailConstant.UNLOAD_AREA_ID),0L));
                demandOrderAddress.setUnloadAreaName(addressMap.get(EmailConstant.UNLOAD_AREA_NAME));
                demandOrderAddress.setUnloadDetailAddress((String)map.get(EmailConstant.UNLOAD_ADDRESS));
                demandOrderAddress.setUnloadWarehouse("");
                demandOrderAddress.setReceiverName((String)map.get(EmailConstant.UNLOAD_CONTACT_NAME));
                demandOrderAddress.setReceiverMobile((String)map.get(EmailConstant.UNLOAD_CONTACT_MOBILE));
                demandOrderAddress.setExpectedUnloadTime(getTime((String)map.get(EmailConstant.EXPECTED_UNLOAD_TIME)));
                demandOrderAddress.setLoadCompany(companyEntrustName);
                demandOrderAddress.setUnloadCompany("");
                commonBiz.setBaseEntityAdd(demandOrderAddress,(String)map.get(EmailConstant.CONSIGNOR_NAME));
                demandOrderAddressMapper.insertSelective(demandOrderAddress);
                demandOrderAddressList.add(demandOrderAddress);

                //生成货物
                for (TDemandOrderGoods goods:goodsList) {
                    demandOrderGoods = new TDemandOrderGoods();
                    demandOrderGoods.setDemandOrderId(demandOrder.getId());
                    demandOrderGoods.setGoodsName(goods.getGoodsName());
                    demandOrderGoods.setGoodsSize("");
                    demandOrderGoods.setGoodsAmount(goods.getGoodsAmount());
                    demandOrderGoods.setNotArrangedAmount(goods.getGoodsAmount());
                    commonBiz.setBaseEntityAdd(demandOrderGoods,(String)map.get(EmailConstant.CONSIGNOR_NAME));
                    demandOrderGoodsList.add(demandOrderGoods);
                }

                //生成需求单事件
                demandOrderEvents = new TDemandOrderEvents();
                demandOrderEvents.setDemandOrderId(demandOrder.getId());
                demandOrderEvents.setCompanyCarrierId(demandOrder.getCompanyCarrierId());
                demandOrderEvents.setEvent(DemandOrderEventsTypeEnum.ACCEPT_CARRIERORDER.getKey());
                demandOrderEvents.setEventDesc(DemandOrderEventsTypeEnum.ACCEPT_CARRIERORDER.getValue());
                demandOrderEvents.setEventTime(now);
                demandOrderEvents.setOperatorName((String)map.get(EmailConstant.CONSIGNOR_NAME));
                demandOrderEvents.setOperateTime(now);
                commonBiz.setBaseEntityAdd(demandOrderEvents,(String)map.get(EmailConstant.CONSIGNOR_NAME));
                demandOrderEventsList.add(demandOrderEvents);

                //新增记录到需求单车主变更表
                TDemandOrderCarrier tDemandOrderCarrier = new TDemandOrderCarrier();
                tDemandOrderCarrier.setDemandOrderId(demandOrder.getId());
                tDemandOrderCarrier.setCompanyCarrierId(demandOrder.getCompanyCarrierId());
                tDemandOrderCarrier.setCarrierContactId(demandOrder.getCarrierContactId());
                tDemandOrderCarrier.setCarrierPrice(demandOrder.getCarrierPrice());
                tDemandOrderCarrier.setCarrierPriceType(demandOrder.getCarrierPriceType());
                commonBiz.setBaseEntityAdd(tDemandOrderCarrier, BaseContextHandler.getUserName());
                tDemandOrderCarrierMapper.insertSelective(tDemandOrderCarrier);

                //生成需求单日志
                demandOrderOperateLogs = new TDemandOrderOperateLogs();
                demandOrderOperateLogs.setDemandOrderId(demandOrder.getId());
                demandOrderOperateLogs.setOperationType(DemandOrderOperateLogsEnum.CREATE_DEMAND_ORDER.getKey());
                demandOrderOperateLogs.setOperationContent(DemandOrderOperateLogsEnum.CREATE_DEMAND_ORDER.getValue());
                demandOrderOperateLogs.setOperatorName((String)map.get(EmailConstant.CONSIGNOR_NAME));
                demandOrderOperateLogs.setOperateTime(now);
                commonBiz.setBaseEntityAdd(demandOrderOperateLogs,(String)map.get(EmailConstant.CONSIGNOR_NAME));
                demandOrderOperateLogsList.add(demandOrderOperateLogs);

                codeList.add((String)map.get(EmailConstant.CUSTOMER_ORDER_CODE));

                //为需求单设置运价信息
                try{
                    GetPriceByAddressAndAmountRequestModel requestFreightModel = new GetPriceByAddressAndAmountRequestModel();
                    requestFreightModel.setFromAreaId(EmailConstant.AREA_ID);
                    requestFreightModel.setFromCityId(EmailConstant.CITY_ID);
                    requestFreightModel.setFromProvinceId(EmailConstant.PROVINCE_ID);
                    requestFreightModel.setFromWarehouse((String)map.get(EmailConstant.LOAD_WAREHOUSE));

                    requestFreightModel.setToAreaId(ConverterUtils.toLong(addressMap.get(EmailConstant.UNLOAD_AREA_ID),0L));
                    requestFreightModel.setToCityId(ConverterUtils.toLong(addressMap.get(EmailConstant.UNLOAD_CITY_ID),0L));
                    requestFreightModel.setToProvinceId(ConverterUtils.toLong(addressMap.get(EmailConstant.UNLOAD_PROVINCE_ID),0L));
                    requestFreightModel.setToWarehouseDetail((String)map.get(EmailConstant.UNLOAD_ADDRESS));
                    requestFreightModel.setGoodsAmount(demandOrder.getGoodsAmount());
                    requestFreightModel.setGoodsUnit(demandOrder.getGoodsUnit());
                    requestFreightModel.setCompanyEntrustId(demandOrder.getCompanyEntrustId());

                    log.info("pull demand order input params：" +  requestFreightModel.toString());

                    GetPriceByAddressAndAmountResponseModel dbEntrustInfo = freightBiz.getEntrustFreightInfo(requestFreightModel);
                    if(dbEntrustInfo != null && dbEntrustInfo.getFreightFee()!=null){
                        TDemandOrder upDemandOrder = new TDemandOrder();
                        upDemandOrder.setId(demandOrder.getId());
                        upDemandOrder.setContractPriceType(dbEntrustInfo.getFreightType());
                        upDemandOrder.setContractPrice(dbEntrustInfo.getFreightFee());
                        demandOrderMapper.updateByPrimaryKeySelectiveEncrypt(upDemandOrder);
                    }

                }catch (Exception e){
                    log.info("扬巴邮件-匹配货主运价信息",e);
                }

            }
            if (ListUtils.isNotEmpty(demandOrderGoodsList)){
                demandOrderGoodsMapper.batchInsert(demandOrderGoodsList);
            }
            if (ListUtils.isNotEmpty(demandOrderEventsList)){
                demandOrderEventsMapper.batchInsertSelective(demandOrderEventsList);
            }
            if (ListUtils.isNotEmpty(demandOrderOperateLogsList)){
                demandOrderOperateLogsMapper.batchInsertSelective(demandOrderOperateLogsList);
            }

            //异步查询地址经纬度
            if (ListUtils.isNotEmpty(demandOrderAddressList)) {
                AsyncProcessQueue.execute(() -> demandOrderCommonBiz.updateAddressLonAndLat(demandOrderAddressList));
            }
        }
    }
    //从附近中提取需求单相关的数据
    public List<Map<String,Object>> getEmailText(){
        List<String> textList = receiveEmailBiz.receiveEmail();
        List<Map<String,Object>> orderMapList = new ArrayList<>();
        if (ListUtils.isNotEmpty(textList)){
            String content = "";
            String[] contents;
            TDemandOrderGoods demandOrderGoods = new TDemandOrderGoods();
            for (int i=0; i<textList.size(); i++){
                log.info("**************附件"+(i+1)+"解析后的内容开始*****************");
                contents = textList.get(i).split("\n");
                Map<String,Object> orderMap = new HashMap<>();
                StringBuilder addressBuffer = new StringBuilder();
                StringBuilder goodsNameBuffer = new StringBuilder();
                int line = 0;
                int goodsLine = 0;
                List<TDemandOrderGoods> goodsList = new ArrayList<>();
                for (int j=0; j<contents.length; j++){
                    content = contents[j].trim();
                    log.info(content);
                    if (content.contains(EmailConstant.TOTAL_NET_WEIGHT)){
                        if (!content.substring(content.length()-2,content.length()).equals("KG") && !content.substring(content.length()-2,content.length()).equals("kg")){
                            break;
                        }
                        orderMap.put(EmailConstant.GOODS_AMOUNT,content.substring(content.indexOf(EmailConstant.TOTAL_NET_WEIGHT)+EmailConstant.TOTAL_NET_WEIGHT.length(),content.length()-2).trim());
                    }
                    if (content.contains(EmailConstant.CONTACT)){
                        orderMap.put(EmailConstant.CONSIGNOR_NAME,content.substring(content.indexOf(EmailConstant.CONTACT)+EmailConstant.CONTACT.length()).trim());
                    }
                    if (content.contains(EmailConstant.SHIPMENT_NUMBER)){
                        orderMap.put(EmailConstant.CUSTOMER_ORDER_CODE,content.substring(content.indexOf(EmailConstant.SHIPMENT_NUMBER)+EmailConstant.SHIPMENT_NUMBER.length(),content.indexOf(EmailConstant.PRINT_TIME)).trim());
                        orderMap.put(EmailConstant.PUBLISH_TIME,content.substring(content.indexOf(EmailConstant.PRINT_TIME)+EmailConstant.PRINT_TIME.length(),content.indexOf(EmailConstant.PAGE_NUMBER)).trim());
                    }
                    if (content.contains(EmailConstant.REQ_LOAD_TIME)){
                        orderMap.put(EmailConstant.EXPECTED_LOAD_TIME,content.substring(content.indexOf(EmailConstant.REQ_LOAD_TIME)+EmailConstant.REQ_LOAD_TIME.length()).trim());
                    }
                    if (content.contains(EmailConstant.REQ_DEL_TIME)){
                        orderMap.put(EmailConstant.EXPECTED_UNLOAD_TIME,content.substring(content.indexOf(EmailConstant.REQ_DEL_TIME)+EmailConstant.REQ_DEL_TIME.length()).trim());
                    }
                    if (content.contains(EmailConstant.LOAD_PORT)){
                        orderMap.put(EmailConstant.LOAD_WAREHOUSE,content.substring(content.indexOf(EmailConstant.LOAD_PORT)+EmailConstant.LOAD_PORT.length(),content.indexOf(EmailConstant.SPECIAL_REQUEST)).trim());
                    }
                    if (content.contains(EmailConstant.RECEIVER_ADDRESS)){
                        line = j;
                        addressBuffer.append(content.substring(content.indexOf(EmailConstant.RECEIVER_ADDRESS)+EmailConstant.RECEIVER_ADDRESS.length()).trim());
                    }
                    if (j > 9 && j == (line+1)){
                        if (StringUtils.isNotBlank(content) && isNotUnderline(content)) {
                            addressBuffer.append(content.trim());
                        }
                        String address = addressBuffer.toString().trim();
                        String contactName = getContactName(address);
                        orderMap.put(EmailConstant.ADDRESS_ONE, getAddress(address));
                        orderMap.put(EmailConstant.ADDRESS_TWO, getAddressTwo(address));
                        orderMap.put(EmailConstant.REMARK, address);
                        orderMap.put(EmailConstant.UNLOAD_ADDRESS, address.substring(0,address.lastIndexOf(contactName)).replaceAll("[a-zA-Z\\pP]","").trim());
                        String mobile = getFirstMobile(address);
                        if (StringUtils.isBlank(mobile)) {
                            mobile = getFirstPhone(address);
                        }
                        orderMap.put(EmailConstant.UNLOAD_CONTACT_MOBILE, mobile);
                        orderMap.put(EmailConstant.UNLOAD_CONTACT_NAME, contactName);
                    }
                    if (isGoodsLine(content)){
                        demandOrderGoods = new TDemandOrderGoods();
                        demandOrderGoods.setGoodsAmount(kgToTons(getGoodsAmount(content).trim()));
                        goodsNameBuffer = new StringBuilder();
                        goodsNameBuffer.append(getGoodsName(content).trim());
                        goodsLine = j;
                    }
                    if (j > 13 && j == (goodsLine+1)&&!content.contains(EmailConstant.TOTAL_NET_WEIGHT) && !isGoodsLine(content)){
                            goodsNameBuffer.append(" ");
                            goodsNameBuffer.append(content);
                            demandOrderGoods.setGoodsName(goodsNameBuffer.toString());
                            goodsList.add(demandOrderGoods);
                    }
                    if (content.contains(EmailConstant.TOTAL_NET_WEIGHT)){
                        orderMap.put(EmailConstant.GOODS, goodsList);
                    }
                }
                log.info("**************附件"+(i+1)+"解析后的内容结束*****************\n");
                log.info("第"+(i+1)+"个附件有效内容为："+orderMap.toString());
                orderMapList.add(orderMap);
            }
        }
        return orderMapList;
    }

    //获取收货详细地址（邮政编码前的地址）
    public static String getAddress(String str){
        String address = "";
        Pattern pattern = Pattern.compile("([\\s]\\d{6}[\\s])");
        Matcher matcher = pattern.matcher(str);
        if(matcher.find()){
            address = str.substring(0, matcher.start()).replaceAll("[a-zA-Z\\pP]", "").trim();
            if (address.contains("县") || address.contains("区")){
                String addressOne = address.substring(0,address.indexOf('县')+1);
                if (StringUtils.isBlank(addressOne)){
                    address = address.substring(0,address.indexOf('区')+1);
                }else{
                    address = addressOne;
                }
            }
        }
        return address;
    }
    //获取收货详细地址（邮政编码后的地址）
    public static String getAddressTwo(String str){
        String address = "";
        Pattern pattern = Pattern.compile("([\\s]\\d{6}[\\s][\\u4E00-\\u9FA5]{0,}[\\s])");
        Matcher matcher = pattern.matcher(str);
        if(matcher.find()){
            address = matcher.group().substring(7);
        }
        return address;
    }
    //判断收货地址下一行是否是下划线
    public static boolean isNotUnderline(String str){
        boolean flag = true;
        Pattern pattern = Pattern.compile("(_{6})");
        Matcher matcher = pattern.matcher(str);
        if(matcher.find()){
            flag = false;
        }
        return flag;
    }
    //获取联系人姓名
    public static String getContactName(String str){
        String contactName = "";
        Pattern pattern = Pattern.compile("((0\\d{2,3}-\\d{7,8})|0\\d{9,11})|(\\d{11})");
        Matcher matcher = pattern.matcher(str);
        if(matcher.find()){
            int index = matcher.start();
            if (index > 0){
                String indexStr;
                int starIndex = 0;
                int endIndex = index;
                int ind = index - 1;
                for (int i=index-1; i>=0; i--){
                    indexStr = str.substring(i,endIndex).replaceAll("[\\pP]","");
                    if (StringUtils.isBlank(indexStr)&&i == ind){
                        ind--;
                    }else if(StringUtils.isBlank(indexStr)&&i != ind){
                        starIndex = i;
                        break;
                    }
                    endIndex--;
                }
                contactName = str.substring(starIndex,index).replaceAll("[\\pP]","").trim();
            }
        }
        return contactName;
    }

    //获取第一个联系方式
    public static String getFirstPhone(String str){
        String phone = "";
        Pattern pattern = Pattern.compile("((0\\d{2,3}-\\d{7,8})|0\\d{9,11})|(\\d{11})");
        Matcher matcher = pattern.matcher(str);
        if(matcher.find()){
            phone = matcher.group();
        }
        return phone;
    }
    //获取第一个手机号
    public static String getFirstMobile(String str){
        String mobile = "";
        Pattern pattern = Pattern.compile("(\\d{11})");
        Matcher matcher = pattern.matcher(str);
        if(matcher.find()){
            mobile = matcher.group();
        }
        return mobile;
    }
    //判断是否是物品行
    public static boolean isGoodsLine(String str){
        boolean flag = false;
        Pattern pattern = Pattern.compile("(\\d{10})");
        Matcher matcher = pattern.matcher(str);
        if (matcher.find() && (str.contains("KG") || str.contains("kg"))){
            flag = true;
        }
        return flag;
    }
    //获取每行货物的品名
    public static String getGoodsName(String str){
        String goodsName = "";
        Pattern pattern = Pattern.compile("([\\u4E00-\\u9FA5a-zA-Z][\\u4E00-\\u9FA5\\w\\s\\W]*)");
        Matcher matcher = pattern.matcher(str);
        if(matcher.find()){
            goodsName = matcher.group();
            pattern = Pattern.compile("[\\s][\\d,]{1,}[.\\d]{4}KG");
            matcher = pattern.matcher(goodsName);
            if (matcher.find()){
                goodsName = goodsName.substring(0,matcher.start());
            }
        }
        return goodsName;
    }
    //获取每行货物的数量
    public static String getGoodsAmount(String str){
        String goodsAmount = "";
        Pattern pattern = Pattern.compile("[\\d,]{1,}[.\\d]{4}(KG|kg)");
        Matcher matcher = pattern.matcher(str);
        int index = 0;
        while (matcher.find()){
            if (index > 0){
                goodsAmount = matcher.group().substring(0,matcher.group().length()-2);
                break;
            }
            index++;
        }
        return goodsAmount;
    }
    //千克转为吨
    public static BigDecimal kgToTons(String amount){
        BigDecimal goodsAmount = BigDecimal.ZERO;
        if (StringUtils.isNotBlank(amount)){
            if (amount.contains(",")){
                amount = amount.replace(",","");
            }
            goodsAmount = ConverterUtils.toBigDecimal(amount);
            goodsAmount = goodsAmount.divide(ConverterUtils.toBigDecimal(1000)).setScale(3,BigDecimal.ROUND_HALF_UP);
        }
        return goodsAmount;
    }
    //时间格式转换
    public static Date getTime(String time){
        Date date = null;
        try {
            date = new SimpleDateFormat("dd.MM.yyyy HH:mm:ss").parse(time);
        }catch (ParseException p){
            log.warn(p.getMessage());
        }
        return date;
    }
}
