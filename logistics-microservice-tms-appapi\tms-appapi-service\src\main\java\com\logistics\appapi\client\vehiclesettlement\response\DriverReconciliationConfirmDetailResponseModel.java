package com.logistics.appapi.client.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author：wjf
 * @date：2021/4/12 13:12
 */
@Data
public class DriverReconciliationConfirmDetailResponseModel {
    @ApiModelProperty("结算id")
    private Long vehicleSettlementId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机名称")
    private String driverName;
    @ApiModelProperty("司机电话")
    private String driverMobile;
    @ApiModelProperty("账单月份")
    private String settlementMonth;
    @ApiModelProperty("运费（应收）")
    private BigDecimal actualExpensesPayable;
    @ApiModelProperty("确认时间")
    private Date confirmTime;
    @ApiModelProperty("确认状态：0 无需确认，1 司机确认")
    private Integer confirmStatus;
    @ApiModelProperty("司机确认图片路径")
    private String commitImageUrl;
    @ApiModelProperty("无需确认理由")
    private String reason;
}
