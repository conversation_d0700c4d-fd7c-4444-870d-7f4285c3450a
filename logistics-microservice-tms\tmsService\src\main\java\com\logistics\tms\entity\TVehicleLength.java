package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2024/04/26
*/
@Data
public class TVehicleLength extends BaseEntity {
    /**
    * 车长（米）
    */
    @ApiModelProperty("车长（米）")
    private BigDecimal vehicleLength;

    /**
    * 承运范围（小）
    */
    @ApiModelProperty("承运范围（小）")
    private BigDecimal carriageScopeMin;

    /**
    * 承运范围（大）
    */
    @ApiModelProperty("承运范围（大）")
    private BigDecimal carriageScopeMax;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}