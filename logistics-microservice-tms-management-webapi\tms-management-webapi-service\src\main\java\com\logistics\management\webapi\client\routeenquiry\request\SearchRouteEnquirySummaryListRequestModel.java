package com.logistics.management.webapi.client.routeenquiry.request;

import com.yelo.tray.core.page.AbstractPageForm;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/7/9 17:16
 */
@Data
public class SearchRouteEnquirySummaryListRequestModel extends AbstractPageForm<SearchRouteEnquirySummaryListRequestModel> {

    /**
     * 竞价单号
     */
    private String orderCode;

    /**
     * 发货地
     */
    private String loadAddress;


    /**
     * 收货地
     */
    private String unloadAddress;

    /**
     * 中标承运商
     */
    private String companyCarrierName;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间-开始
     */
    private String createdTimeStart;
    /**
     * 创建时间-结束
     */
    private String createdTimeEnd;

}
