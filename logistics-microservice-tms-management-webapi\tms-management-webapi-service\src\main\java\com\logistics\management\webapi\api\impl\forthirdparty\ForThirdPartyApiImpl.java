package com.logistics.management.webapi.api.impl.forthirdparty;

import com.logistics.management.webapi.api.feign.forthirdparty.ForThirdPartyApi;
import com.logistics.management.webapi.api.feign.forthirdparty.dto.request.WorkOrderProcessSyncRequestDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.tms.api.feign.forthirdparty.ForThirdPartyServiceApi;
import com.logistics.tms.api.feign.forthirdparty.model.request.WorkOrderProcessSyncRequestModel;
import com.logistics.tms.api.feign.workordercenter.enums.WorkOrderProcessSolveSourceEnum;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
public class ForThirdPartyApiImpl implements ForThirdPartyApi {

    @Resource
    private ForThirdPartyServiceApi forThirdPartyServiceApi;


    /**
     * 同步工单处理流程 - 智慧运营(任务中心)调用
     * @param requestDto 请求DTO
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> syncWorkOrderProcess(@RequestBody @Valid WorkOrderProcessSyncRequestDto requestDto) {
        WorkOrderProcessSyncRequestModel requestModel = MapperUtils.mapper(requestDto, WorkOrderProcessSyncRequestModel.class);
        requestModel.setSolveSource(WorkOrderProcessSolveSourceEnum.TASK_CENTER.getKey());
        return forThirdPartyServiceApi.syncWorkOrderProcess(requestModel);
    }

}
