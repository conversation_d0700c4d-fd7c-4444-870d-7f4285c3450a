package com.logistics.tms.controller.vehiclesettlement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import java.math.BigDecimal;
import java.util.Date;

/**
 * @author:lei.zhu
 * @date:2021/4/9 13:18
 */
@Data
public class SettleFreightRequestModel {
    @ApiModelProperty("车辆结算账单id")
    private Long vehicleSettlementId;
    @ApiModelProperty(value = "打款公司")
    private String payCompany;
    @ApiModelProperty(value = "收款人")
    private String receivedName;
    @ApiModelProperty(value = "打款时间")
    private Date payTime;
    @ApiModelProperty(value = "打款金额")
    private BigDecimal payFee;
}
