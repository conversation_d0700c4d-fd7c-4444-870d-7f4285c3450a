package com.logistics.tms.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/4/16 8:57
 */
@Data
public class CarrierOrderConfirmSignUpRequestModel {
    @ApiModelProperty("运单集合")
    private List<CarrierOrderSignUpRequestModel> signList;
    @ApiModelProperty("签收时间")
    private Date signTime;
}
