package com.logistics.management.webapi.controller.warehouseaddress.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WarehouseAddressListRequestDto extends AbstractPageForm<WarehouseAddressListRequestDto> {
    @ApiModelProperty("状态  空 全部  0 禁用 1 启用  ")
    private String enabled;
    @ApiModelProperty("货主")
    private String companyEntrustName;
    @ApiModelProperty("仓库")
    private String warehouseName;
    @ApiModelProperty("省市区")
    private String address;
    @ApiModelProperty("仓库Ids拼接")
    private String warehouseAddressIds;
}
