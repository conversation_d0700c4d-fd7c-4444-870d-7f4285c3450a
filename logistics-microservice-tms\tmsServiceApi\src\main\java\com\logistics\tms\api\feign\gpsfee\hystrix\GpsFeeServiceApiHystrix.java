package com.logistics.tms.api.feign.gpsfee.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.gpsfee.GpsFeeServiceApi;
import com.logistics.tms.api.feign.gpsfee.model.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/8 10:26
 */
@Component("tmsGpsFeeServiceApiHystrix")
public class GpsFeeServiceApiHystrix implements GpsFeeServiceApi {
    @Override
    public Result<PageInfo<SearchGpsFeeListResponseModel>> searchGpsFeeList(SearchGpsFeeListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SearchGpsFeeListCountResponseModel> searchGpsFeeListCount(SearchGpsFeeListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GpsFeeDetailResponseModel> getGpsFeeDetail(GpsFeeIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result addOrModifyGpsFee(AddOrModifyGpsFeeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchGpsFeeListResponseModel>> exportGpsFee(SearchGpsFeeListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result terminationGpsFee(TerminationGpsFeeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GpsFeeRecordsListResponseModel>> getGpsFeeRecords(GpsFeeIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetDeductingHistoryByGpsFeeIdResponseModel> getGpsFeeDeductingHistory(GpsFeeIdRequestModel requestModel) {
        return Result.timeout();
    }
}
