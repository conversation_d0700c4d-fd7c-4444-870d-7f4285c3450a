package com.logistics.management.webapi.base.enums;


public enum EncodeTypeEnum {

    MOBILE_PHONE(3, 4, "手机号脱敏"),
    IDENTITY_NUMBER(3, 3, "身份证号脱敏"),
    BANK_CODE(4, 4, "银行账号脱敏"),
    CUSTOMER_NAME(1, 0, "客户名称"),
    ;

    private Integer startLength;
    private Integer endLength;
    private String remark;

    EncodeTypeEnum(Integer startLength, Integer endLength, String remark) {
        this.startLength = startLength;
        this.endLength = endLength;
        this.remark = remark;
    }

    public Integer getStartLength() {
        return startLength;
    }

    public Integer getEndLength() {
        return endLength;
    }

    public String getRemark() {
        return remark;
    }
}
