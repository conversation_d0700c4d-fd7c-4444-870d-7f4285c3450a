package com.logistics.appapi.controller.vehiclesafecheck.mapping;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.constant.ConfigKeyConstant;
import com.logistics.appapi.base.enums.SafeCheckItemEnum;
import com.logistics.appapi.base.enums.SafeCheckItemStatusEnum;
import com.logistics.appapi.client.vehiclesafecheck.response.SafeCheckDetailResponseModel;
import com.logistics.appapi.client.vehiclesafecheck.response.SafeCheckFileResponseModel;
import com.logistics.appapi.client.vehiclesafecheck.response.SafeCheckItemResponseModel;
import com.logistics.appapi.controller.vehiclesafecheck.response.SafeCheckDetailResponseDto;
import com.logistics.appapi.controller.vehiclesafecheck.response.SafeCheckFileResponseDto;
import com.logistics.appapi.controller.vehiclesafecheck.response.SafeCheckReformResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: sj
 * @Date: 2019/11/25 15:00
 */
public class SafeCheckDetailMapping extends MapperMapping<SafeCheckDetailResponseModel,SafeCheckDetailResponseDto> {

    private ConfigKeyConstant configKeyConstant;
    private Map<String, String> imageMap;
    public SafeCheckDetailMapping(ConfigKeyConstant configKeyConstant, Map<String, String> imageMap){
        this.configKeyConstant = configKeyConstant;
        this.imageMap=imageMap;
    }
    @Override
    public void configure() {
        SafeCheckDetailResponseModel source = this.getSource();
        SafeCheckDetailResponseDto dto = this.getDestination();
        if(source!=null){
            if(source.getCheckTime() != null){
                dto.setCheckTime(DateUtils.dateToString(source.getCheckTime(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
            }

            //车辆检查图片信息
            if(ListUtils.isNotEmpty(source.getFileList())){
                List<SafeCheckFileResponseDto> dtoFileList = new ArrayList();
                SafeCheckFileResponseDto fileModelDto;
                for (SafeCheckFileResponseModel tempModel : source.getFileList()) {
                    fileModelDto = new SafeCheckFileResponseDto();
                    fileModelDto.setRelativeFilepath(tempModel.getRelativeFilepath());
                    fileModelDto.setAbsoluteFilePath(configKeyConstant.fileAccessAddress+imageMap.get(tempModel.getRelativeFilepath()));
                    dtoFileList.add(fileModelDto);
                }
                dto.setVehicleFileList(dtoFileList);
            }

            //检查项
            List<String> checkItemList = new ArrayList<>();
            if(ListUtils.isNotEmpty(source.getItemList())){
                for (SafeCheckItemResponseModel itemModel : source.getItemList()) {
                    checkItemList.add(ConverterUtils.toString(itemModel.getItemType()));
                }
                dto.setCheckItemList(checkItemList);
            }

            //整改附件信息
            SafeCheckReformResponseDto checkReformInfo = dto.getCheckReformInfo();
            if(checkReformInfo != null){
                if(ListUtils.isNotEmpty(checkReformInfo.getItemFileList())){
                    for (SafeCheckFileResponseDto tempFile: checkReformInfo.getItemFileList()) {
                        tempFile.setAbsoluteFilePath(configKeyConstant.fileAccessAddress+imageMap.get(tempFile.getRelativeFilepath()));
                    }
                }else{
                    checkReformInfo.setItemFileList(new ArrayList<>());
                }
                if(ListUtils.isNotEmpty(checkReformInfo.getResultFileList())){
                    for (SafeCheckFileResponseDto tempFile : checkReformInfo.getResultFileList()) {
                        tempFile.setAbsoluteFilePath(configKeyConstant.fileAccessAddress+imageMap.get(tempFile.getRelativeFilepath()));
                    }
                }else{
                    checkReformInfo.setResultFileList(new ArrayList<>());
                }

                if(ListUtils.isNotEmpty(source.getItemList())){
                    List<SafeCheckItemResponseModel> filterItemList = source.getItemList().stream().filter(o-> SafeCheckItemStatusEnum.UNQUALIFIED.getKey().equals(o.getItemStatus())).collect(Collectors.toList());
                    if(ListUtils.isNotEmpty(filterItemList)){
                        checkReformInfo.setReformItemSummary("部分合格,"+filterItemList.size() +"项整改");
                    }else{
                        if(source.getStatus()> CommonConstant.INT_TEN){
                            checkReformInfo.setReformItemSummary("全部合格");
                        }
                    }

                    StringBuilder sb = new StringBuilder();
                    for (SafeCheckItemResponseModel tempModel : filterItemList) {
                        sb.append(SafeCheckItemEnum.getEnum(tempModel.getItemType()).getItemName()+";");
                    }
                    checkReformInfo.setReformItems(sb.toString());
                }

            }else{
                dto.setCheckReformInfo(new SafeCheckReformResponseDto());
            }
        }
    }
}
