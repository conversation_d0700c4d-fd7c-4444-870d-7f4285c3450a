package com.logistics.management.webapi.controller.drivercostapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/2/21 13:21
 */
@Data
public class DriverCostApplyRecordResponseDto {
    @ApiModelProperty("操作人")
    private String operateUserName = "";
    @ApiModelProperty("操作时间")
    private String operateTime = "";
    @ApiModelProperty("操作内容")
    private String operateContents = "";
    @ApiModelProperty("备注")
    private String remark = "";
}
