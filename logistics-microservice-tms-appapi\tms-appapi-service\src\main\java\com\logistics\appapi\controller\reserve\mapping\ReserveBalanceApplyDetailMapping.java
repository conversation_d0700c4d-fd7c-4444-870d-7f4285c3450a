package com.logistics.appapi.controller.reserve.mapping;


import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.ReserveApplyAuditStatusEnum;
import com.logistics.appapi.base.utils.BigDecimalUtils;
import com.logistics.appapi.client.reserveapply.response.ReserveBalanceApplyDetailResponseModel;
import com.logistics.appapi.controller.reserve.response.ReserveBalanceApplyDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

public class ReserveBalanceApplyDetailMapping extends MapperMapping<ReserveBalanceApplyDetailResponseModel, ReserveBalanceApplyDetailResponseDto> {

    @Override
    public void configure() {
        ReserveBalanceApplyDetailResponseModel source = getSource();
        ReserveBalanceApplyDetailResponseDto destination = getDestination();

        // 状态
        destination.setStatusLabel(ReserveApplyAuditStatusEnum.getEnumByKey(source.getStatus()).getValue());

        // 金额处理
        destination.setApplyAmount(BigDecimalUtils.conversion(source.getApplyAmount()));
        destination.setApproveAmount(BigDecimalUtils.conversion(source.getApproveAmount()));

        // 申请人
        destination.setProposer(String.format(CommonConstant.NAME_MOBILE_FORMAT, source.getStaffName(), source.getStaffMobile()));

        // 申请时间格式化
        destination.setApplyDate(DateUtils.dateToString(source.getApplyDate(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
    }
}
