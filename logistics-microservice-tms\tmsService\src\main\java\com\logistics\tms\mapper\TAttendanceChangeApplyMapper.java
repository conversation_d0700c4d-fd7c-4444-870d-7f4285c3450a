package com.logistics.tms.mapper;

import com.logistics.tms.controller.attendance.request.SearchAttendanceChangeListRequestModel;
import com.logistics.tms.controller.attendance.response.SearchAttendanceChangeListResponseModel;
import com.logistics.tms.entity.TAttendanceChangeApply;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
* Created by Mybatis Generator on 2022/11/10
*/
@Mapper
public interface TAttendanceChangeApplyMapper extends BaseMapper<TAttendanceChangeApply> {

    List<TAttendanceChangeApply> selectByAttendanceRecordIdIn(@Param("attendanceRecordIdCollection")Collection<Long> attendanceRecordIdCollection);

    List<TAttendanceChangeApply> selectAllByIdIn(@Param("idCollection")Collection<Long> idCollection);

    List<SearchAttendanceChangeListResponseModel> selectSearchAttendanceChangeList(@Param("params") SearchAttendanceChangeListRequestModel requestModel);

    int updateByIdAndAuditStatus(@Param("updated")TAttendanceChangeApply updated,@Param("id")Long id,@Param("auditStatus")Integer auditStatus);
}