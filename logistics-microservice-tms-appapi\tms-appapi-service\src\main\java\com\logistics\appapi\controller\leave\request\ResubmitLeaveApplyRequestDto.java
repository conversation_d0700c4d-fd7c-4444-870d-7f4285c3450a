package com.logistics.appapi.controller.leave.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;

@Data
public class ResubmitLeaveApplyRequestDto {

    @ApiModelProperty(value = "请假申请ID")
    @NotBlank(message = "请选择要重新提交的的请假记录")
    private String leaveApplyId;

    @ApiModelProperty(value = "请假类型: 1 事假", required = true)
    @NotBlank(message = "请选择请假类型")
    @Range(min = 1, max = 1, message = "请假类型有误")
    private String leaveType;

    @ApiModelProperty(value = "请假开始时间", required = true)
    @NotBlank(message = "请选择请假开始时间")
    private String leaveStartTime;

    @ApiModelProperty(value = "请假开始时间类型, 1:上午, 2:下午", required = true)
    @NotBlank(message = "请选择请假开始时间类型")
    @Range(min = 1, max = 2, message = "请假时间类型不正确")
    private String leaveStartTimeType;

    @ApiModelProperty(value = "请假结束时间", required = true)
    @NotBlank(message = "请选择请假结束时间")
    private String leaveEndTime;

    @ApiModelProperty(value = "请假结束时间类型, 1:上午, 2:下午", required = true)
    @NotBlank(message = "请选择请假结束时间类型")
    @Range(min = 1, max = 2, message = "请假时间类型不正确")
    private String leaveEndTimeType;

    @ApiModelProperty(value = "请假时长", required = true)
    @NotBlank(message = "请选择请假时长")
    private String leaveDuration;

    @ApiModelProperty(value = "请假事由", required = true)
    @NotBlank(message = "请填写请假事由,1-100字符")
    @Length(min = 1, max = 100, message = "请填写请假事由,1-100字符")
    private String leaveReason;
}
