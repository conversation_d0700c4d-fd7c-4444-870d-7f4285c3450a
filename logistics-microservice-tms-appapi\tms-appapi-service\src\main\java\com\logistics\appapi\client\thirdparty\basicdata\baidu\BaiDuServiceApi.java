package com.logistics.appapi.client.thirdparty.basicdata.baidu;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.thirdparty.basicdata.baidu.hystrix.BaiDuServiceApiHystrix;
import com.logistics.appapi.client.thirdparty.basicdata.baidu.request.CheckSensitiveWordRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.baidu.response.CheckSensitiveWordResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2024/3/7 13:04
 */
@FeignClient(name = FeignClientName.BASIC_DATA_SERVICES,
        path = "/service/baiDu",
        fallback = BaiDuServiceApiHystrix.class)
public interface BaiDuServiceApi {

    @ApiOperation("判断是否具有敏感词汇")
    @PostMapping(name = "判断是否具有敏感词汇",value = {"/checkSensitiveWord"})
    Result<CheckSensitiveWordResponseModel> checkSensitiveWord(@RequestBody CheckSensitiveWordRequestModel requestModel);

}
