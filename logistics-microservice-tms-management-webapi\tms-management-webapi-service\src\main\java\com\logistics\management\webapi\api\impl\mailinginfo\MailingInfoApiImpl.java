package com.logistics.management.webapi.api.impl.mailinginfo;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.mailinginfo.MailingInfoApi;
import com.logistics.management.webapi.api.feign.mailinginfo.dto.request.MailingInfoDetailRequestDto;
import com.logistics.management.webapi.api.feign.mailinginfo.dto.request.MailingInfoListRequestDto;
import com.logistics.management.webapi.api.feign.mailinginfo.dto.request.MailingInfoModifyRequestDto;
import com.logistics.management.webapi.api.feign.mailinginfo.dto.response.MailingInfoDetailResponseDto;
import com.logistics.management.webapi.api.feign.mailinginfo.dto.response.MailingInfoListResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.tms.api.feign.mailinginfo.MailingInfoServiceApi;
import com.logistics.tms.api.feign.mailinginfo.model.request.MailingInfoDetailRequestModel;
import com.logistics.tms.api.feign.mailinginfo.model.request.MailingInfoListRequestModel;
import com.logistics.tms.api.feign.mailinginfo.model.request.MailingInfoModifyRequestModel;
import com.logistics.tms.api.feign.mailinginfo.model.response.MailingInfoDetailResponseModel;
import com.logistics.tms.api.feign.mailinginfo.model.response.MailingInfoListResponseModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/3
 */
@RestController
@RequiredArgsConstructor
public class MailingInfoApiImpl implements MailingInfoApi {

	private final MailingInfoServiceApi mailingInfoServiceApi;

	/**
	 * 邮寄信息配置列表
	 * @param requestDto
	 * @return PageInfo<MailingInfoListResponseDto>
	 */
	@Override
	public Result<PageInfo<MailingInfoListResponseDto>> mailingInfoList(MailingInfoListRequestDto requestDto) {
		MailingInfoListRequestModel requestModel = MapperUtils.mapper(requestDto, MailingInfoListRequestModel.class);
		Result<PageInfo<MailingInfoListResponseModel>> result = mailingInfoServiceApi.mailingInfoList(requestModel);
		result.throwException();

		PageInfo pageInfo = result.getData();
		List<MailingInfoListResponseDto> dtoList = MapperUtils.mapper(pageInfo.getList(), MailingInfoListResponseDto.class);
		pageInfo.setList(dtoList);
		return Result.success(pageInfo);
	}

	/**
	 * 查询系统邮寄信息配置详情
	 * @param requestDto
	 * @return MailingInfoDetailResponseDto
	 */
	@Override
	public Result<MailingInfoDetailResponseDto> mailingInfoDetail(MailingInfoDetailRequestDto requestDto) {
		MailingInfoDetailRequestModel requestModel = MapperUtils.mapper(requestDto, MailingInfoDetailRequestModel.class);
		Result<MailingInfoDetailResponseModel> responseModelResult = mailingInfoServiceApi.mailingInfoDetail(requestModel);
		responseModelResult.throwException();

		MailingInfoDetailResponseDto dto = MapperUtils.mapper(responseModelResult.getData(), MailingInfoDetailResponseDto.class);
		return Result.success(dto);
	}

	/**
	 * 编辑系统邮寄信息配置
	 * @param requestDto
	 * @return Boolean
	 */
	@Override
	@IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
	public Result<Boolean> mailingInfoModify(MailingInfoModifyRequestDto requestDto) {
		MailingInfoModifyRequestModel requestModel = MapperUtils.mapper(requestDto, MailingInfoModifyRequestModel.class);
		return mailingInfoServiceApi.mailingInfoModify(requestModel);
	}
}
