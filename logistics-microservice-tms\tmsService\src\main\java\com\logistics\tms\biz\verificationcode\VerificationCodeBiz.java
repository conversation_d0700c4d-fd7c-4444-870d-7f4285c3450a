package com.logistics.tms.biz.verificationcode;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.client.BasicDataClient;
import com.logistics.tms.controller.customeraccount.response.AccountInfoResponseModel;
import com.logistics.tms.controller.customeraccount.response.AccountRolesModel;
import com.logistics.tms.controller.verificationcode.request.VerificationCodeRequestModel;
import com.logistics.tms.mapper.TCustomerAccountMapper;
import com.yelo.basicdata.api.feign.verifycode.model.VerifyCodeRequestModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author：wjf
 * @date：2021/5/12 16:47
 */
@Service
public class VerificationCodeBiz {

    @Autowired
    private TCustomerAccountMapper tCustomerAccountMapper;
    @Autowired
    private BasicDataClient basicDataClient;

    /**
     * 获取短信验证码
     * @param requestModel
     */
    @Transactional
    public void getVerifyCode(VerificationCodeRequestModel requestModel) {
        ConfigKeyEnum type;
        AccountInfoResponseModel accountInfoResponseModelResult = tCustomerAccountMapper.getAccountInfoBy(requestModel.getMobile(),null,null);
        //发送之前根据类型check
        if (VerificationCodeSourceTypeEnum.TMS_DRIVER_APPLET.getKey().equals(requestModel.getSource())) {//小程序
            //验证账号
            if (!(VerificationCodeTypeEnum.TMS_DRIVER_APPLET_MODIFY_PHONE.getKey().equals(requestModel.getVerificationType())
                  || VerificationCodeTypeEnum.H5_VISITOR_RESERVATION.getKey().equals(requestModel.getVerificationType())
                    || VerificationCodeTypeEnum.H5_VISITOR_SIGN_IN.getKey().equals(requestModel.getVerificationType())) ) {
                this.getRole(accountInfoResponseModelResult, AccountUserRoleTypeEnum.DRIVER_APPLET.getKey().toString());
            }

            if (VerificationCodeTypeEnum.TMS_DRIVER_APPLET_LOGIN.getKey().equals(requestModel.getVerificationType())
                    || VerificationCodeTypeEnum.TMS_DRIVER_APPLET_MODIFY_PHONE.getKey().equals(requestModel.getVerificationType())) {
                type = ConfigKeyEnum.SMS_VERIFICATION_CODE_TEMPLATE;
            } else if (VerificationCodeTypeEnum.TMS_DRIVER_APPLET_FIND_PASS.getKey().equals(requestModel.getVerificationType())) {
                type = ConfigKeyEnum.SMS_FIND_PASS_VERIFICATION_CODE_TEMPLATE;
            } else if (VerificationCodeTypeEnum.TMS_DRIVER_APPLET_MODIFY_BANKCARD.getKey().equals(requestModel.getVerificationType())) {
                type = ConfigKeyEnum.SMS_VERIFICATION_CODE_TEMPLATE;
            }  else if (VerificationCodeTypeEnum.H5_VISITOR_RESERVATION.getKey().equals(requestModel.getVerificationType())) {
                type = ConfigKeyEnum.SMS_VERIFICATION_CODE_TEMPLATE;
            } else if (VerificationCodeTypeEnum.H5_VISITOR_SIGN_IN.getKey().equals(requestModel.getVerificationType())) {
                type = ConfigKeyEnum.SMS_VERIFICATION_CODE_TEMPLATE;
            }else {
                throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
            }
        } else if (VerificationCodeSourceTypeEnum.TMS_CUSTOMER_WEB.getKey().equals(requestModel.getSource())) {//客户前台
            //验证账号
            if (!VerificationCodeTypeEnum.TMS_CUSTOMER_WEB_MODIFY_PHONE.getKey().equals(requestModel.getVerificationType())) {
                this.getRole(accountInfoResponseModelResult, AccountUserRoleTypeEnum.CARRIER.getKey().toString());
            }

            if (VerificationCodeTypeEnum.TMS_CUSTOMER_WEB_LOGIN.getKey().equals(requestModel.getVerificationType())
                    || VerificationCodeTypeEnum.TMS_CUSTOMER_WEB_MODIFY_PHONE.getKey().equals(requestModel.getVerificationType())) {
                type = ConfigKeyEnum.SMS_VERIFICATION_CODE_TEMPLATE;
            } else if (VerificationCodeTypeEnum.TMS_CUSTOMER_WEB_FIND_PASS.getKey().equals(requestModel.getVerificationType())) {
                type = ConfigKeyEnum.SMS_FIND_PASS_VERIFICATION_CODE_TEMPLATE;
            } else {
                throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
            }
        } else {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }

        VerifyCodeRequestModel verifyCodeRequestModel = MapperUtils.mapper(requestModel, VerifyCodeRequestModel.class);
        verifyCodeRequestModel.setBaseBusinessConfigType(type.getValue());
        basicDataClient.sendSms(verifyCodeRequestModel);
    }

    //验证账号
    private void getRole(AccountInfoResponseModel accountInfoResponseModel, String role) {
        if (accountInfoResponseModel == null || ListUtils.isEmpty(accountInfoResponseModel.getRoles())) {
            throw new BizException(CarrierDataExceptionEnum.MOBILE_NOT_AUTHORIZED);
        }
        AccountRolesModel accountRolesModel = accountInfoResponseModel.getRoles().stream().filter(t -> role.contains(t.getUserRole().toString()) && CommonConstant.INTEGER_ZERO.equals(t.getIfClose())).findFirst().orElse(null);
        if (accountRolesModel == null) {
            throw new BizException(CarrierDataExceptionEnum.MOBILE_NOT_AUTHORIZED);
        }
    }
}
