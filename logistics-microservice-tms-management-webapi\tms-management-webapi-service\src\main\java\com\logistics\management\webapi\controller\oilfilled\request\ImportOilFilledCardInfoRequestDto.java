package com.logistics.management.webapi.controller.oilfilled.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/6/10 14:52
 */
@Data
public class ImportOilFilledCardInfoRequestDto {
    @ApiModelProperty("导入List")
    private List<ImportOilFilledCardListRequestDto> importList;
    @ApiModelProperty("失败数量")
    private String numberFailures="0";

}
