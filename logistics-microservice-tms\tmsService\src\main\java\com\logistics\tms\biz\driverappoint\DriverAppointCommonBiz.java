package com.logistics.tms.biz.driverappoint;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.base.utils.RegExpValidatorUtil;
import com.logistics.tms.base.utils.StripTrailingZerosUtils;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.driverappoint.model.CreateCarrierOrderByAssociatedVehicleModel;
import com.logistics.tms.biz.staff.model.TStaffBasicModel;
import com.logistics.tms.biz.staffvehiclerelation.StaffVehicleBiz;
import com.logistics.tms.biz.staffvehiclerelation.model.TVehicleBasicModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.logistics.tms.rabbitmq.publisher.model.InsertBillOrderLogisticsMessage;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/25 17:13
 */
@Service
public class DriverAppointCommonBiz {

    //需求单相关
    @Autowired
    private TDemandOrderMapper tDemandOrderMapper;
    @Autowired
    private TDemandOrderAddressMapper tDemandOrderAddressMapper;
    @Autowired
    private TDemandOrderGoodsMapper tDemandOrderGoodsMapper;
    @Autowired
    private TDemandOrderOperateLogsMapper tDemandOrderOperateLogsMapper;
    @Autowired
    private TDemandOrderEventsMapper tDemandOrderEventsMapper;

    //运单相关
    @Autowired
    private TDispatchOrderMapper tDispatchOrderMapper;
    @Autowired
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Autowired
    private TCarrierOrderAddressMapper tCarrierOrderAddressMapper;
    @Autowired
    private TCarrierOrderGoodsMapper tCarrierOrderGoodsMapper;
    @Autowired
    private TCarrierOrderVehicleHistoryMapper tCarrierOrderVehicleHistoryMapper;
    @Autowired
    private TCarrierOrderOperateLogsMapper tCarrierOrderOperateLogsMapper;
    @Autowired
    private TCarrierOrderEventsMapper tCarrierOrderEventsMapper;
    @Autowired
    private TCarrierOrderWxMapper tCarrierOrderWxMapper;
    @Autowired
    private StaffVehicleBiz staffVehicleBiz;
    @Autowired
    private TVehicleTransportLineMapper tVehicleTransportLineMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private RabbitMqPublishBiz rabbitMqPublishBiz;
    @Autowired
    private TCarrierOrderTicketsMapper tCarrierOrderTicketsMapper;

    /**
     * 生成运单-预约记录关联车辆
     *
     * @param createCarrierOrderModel
     * @return
     */
    @Transactional
    public String createCarrierOrder(CreateCarrierOrderByAssociatedVehicleModel createCarrierOrderModel) {
        //查询需求单
        TDemandOrder tDemandOrder = tDemandOrderMapper.selectByPrimaryKeyDecrypt(createCarrierOrderModel.getDemandOrderId());
        //查询需求单货物信息
        List<TDemandOrderGoods> tDemandOrderGoodsList = tDemandOrderGoodsMapper.getDemandOrderGoodsByDemandOrderIds(tDemandOrder.getId().toString());
        //查询需求单地址信息
        TDemandOrderAddress tDemandOrderAddress = tDemandOrderAddressMapper.getByDemandOrderId(tDemandOrder.getId());

        //初始化数据
        String userName = BaseContextHandler.getUserName();
        Date now = new Date();

        //更新需求单状态
        TDemandOrder demandOrder = new TDemandOrder();
        demandOrder.setId(tDemandOrder.getId());
        demandOrder.setStatus(DemandOrderStatusEnum.COMPLETE_DISPATCH.getKey());
        demandOrder.setEntrustStatus(DemandOrderStatusEnum.COMPLETE_DISPATCH.getKey());
        demandOrder.setArrangedAmount(tDemandOrder.getGoodsAmount());
        demandOrder.setNotArrangedAmount(BigDecimal.ZERO);
        demandOrder.setDispatchVehicleCount(CommonConstant.INTEGER_ONE);
        demandOrder.setStatusUpdateTime(now);
        commonBiz.setBaseEntityModify(demandOrder, userName);
        tDemandOrderMapper.updateByPrimaryKeySelectiveEncrypt(demandOrder);

        //更新货物数量
        TDemandOrderGoods demandOrderGoods;
        List<TDemandOrderGoods> demandOrderGoodsList = new ArrayList<>();
        for (TDemandOrderGoods goods : tDemandOrderGoodsList) {
            //组装货物信息
            demandOrderGoods = new TDemandOrderGoods();
            demandOrderGoods.setId(goods.getId());
            demandOrderGoods.setArrangedAmount(goods.getGoodsAmount());
            demandOrderGoods.setNotArrangedAmount(BigDecimal.ZERO);
            commonBiz.setBaseEntityModify(demandOrderGoods, userName);
            demandOrderGoodsList.add(demandOrderGoods);
        }
        if (ListUtils.isNotEmpty(demandOrderGoodsList)) {
            tDemandOrderGoodsMapper.batchUpdateByPrimaryKeySelective(demandOrderGoodsList);
        }

        //调度单号
        String dispatchOrderCode = commonBiz.getBusinessTypeCode(BusinessCodeTypeEnum.DISPATCH_ORDER_CODE, "", userName);
        //生成调度单
        TDispatchOrder dispatchOrder = new TDispatchOrder();
        dispatchOrder.setDriverId(createCarrierOrderModel.getStaffId());
        dispatchOrder.setVehicleId(createCarrierOrderModel.getVehicleId());
        dispatchOrder.setDispatchOrderCode(dispatchOrderCode);
        dispatchOrder.setCarrierOrderCount(CommonConstant.INTEGER_ONE);
        dispatchOrder.setVehicleNo(createCarrierOrderModel.getVehicleNo());
        dispatchOrder.setDriverName(createCarrierOrderModel.getStaffName());
        dispatchOrder.setDriverMobile(createCarrierOrderModel.getStaffMobile());
        dispatchOrder.setDriverIdentity(createCarrierOrderModel.getDriverIdentity());
        dispatchOrder.setRemark(tDemandOrder.getRemark());
        dispatchOrder.setLoadPointAmount(CommonConstant.INTEGER_ONE);
        dispatchOrder.setUnloadPointAmount(CommonConstant.INTEGER_ONE);
        dispatchOrder.setGoodsUnit(tDemandOrder.getGoodsUnit());
        dispatchOrder.setSource(CommonConstant.INTEGER_THREE);//来源3是指司机
        dispatchOrder.setDispatchUserId(createCarrierOrderModel.getStaffId());
        dispatchOrder.setDispatchUserName(tDemandOrder.getPublishName());
        dispatchOrder.setDispatchTime(now);
        dispatchOrder.setCompanyCarrierId(tDemandOrder.getCompanyCarrierId());
        dispatchOrder.setCompanyCarrierName(tDemandOrder.getCompanyCarrierName());
        dispatchOrder.setCompanyCarrierType(tDemandOrder.getCompanyCarrierType());
        dispatchOrder.setCarrierContactId(tDemandOrder.getCarrierContactId());
        dispatchOrder.setCarrierContactName(tDemandOrder.getCarrierContactName());
        dispatchOrder.setCarrierContactPhone(tDemandOrder.getCarrierContactPhone());
        commonBiz.setBaseEntityAdd(dispatchOrder, userName);
        tDispatchOrderMapper.insertSelectiveEncrypt(dispatchOrder);

        //生成运单（状态为”待到达卸货地“）
        String carrierOrderCode = "Y" + tDemandOrder.getDemandOrderCode().substring(1) + "-01";
        TCarrierOrder carrierOrder = new TCarrierOrder();
        carrierOrder.setUpstreamCustomer(tDemandOrder.getUpstreamCustomer());
        carrierOrder.setDispatchOrderId(dispatchOrder.getId());
        carrierOrder.setDispatchOrderCode(dispatchOrder.getDispatchOrderCode());
        carrierOrder.setDemandOrderId(tDemandOrder.getId());
        carrierOrder.setDemandOrderCode(tDemandOrder.getDemandOrderCode());
        carrierOrder.setCarrierOrderCode(carrierOrderCode);
        carrierOrder.setCustomerOrderCode(tDemandOrder.getCustomerOrderCode());
        carrierOrder.setStatus(CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey());
        carrierOrder.setStatusUpdateTime(now);
        carrierOrder.setBusinessType(tDemandOrder.getBusinessType());
        carrierOrder.setCustomerName(tDemandOrder.getCustomerName());
        carrierOrder.setCustomerUserName(tDemandOrder.getCustomerUserName());
        carrierOrder.setCustomerUserMobile(tDemandOrder.getCustomerUserMobile());
        carrierOrder.setCustomerOrderSource(tDemandOrder.getCustomerOrderSource());
        carrierOrder.setSource(dispatchOrder.getSource());
        carrierOrder.setDispatchTime(now);
        carrierOrder.setDispatchUserId(dispatchOrder.getDispatchUserId());
        carrierOrder.setDispatchUserName(dispatchOrder.getDispatchUserName());

        //车主信息
        carrierOrder.setCompanyCarrierId(tDemandOrder.getCompanyCarrierId());
        carrierOrder.setCompanyCarrierName(tDemandOrder.getCompanyCarrierName());
        carrierOrder.setCompanyCarrierId(tDemandOrder.getCompanyCarrierId());
        carrierOrder.setCompanyCarrierType(tDemandOrder.getCompanyCarrierType());
        carrierOrder.setCarrierContactId(tDemandOrder.getCarrierContactId());
        carrierOrder.setCarrierContactName(tDemandOrder.getCarrierContactName());
        carrierOrder.setCarrierContactPhone(tDemandOrder.getCarrierContactPhone());
        carrierOrder.setCompanyCarrierLevel(tDemandOrder.getCompanyCarrierLevel());

        //货主信息
        carrierOrder.setCompanyEntrustId(tDemandOrder.getCompanyEntrustId());
        carrierOrder.setCompanyEntrustName(tDemandOrder.getCompanyEntrustName());

        //数量
        carrierOrder.setExpectAmount(tDemandOrder.getGoodsAmount());
        carrierOrder.setLoadAmount(tDemandOrder.getGoodsAmount());
        carrierOrder.setLoadAmountExpect(tDemandOrder.getGoodsAmount());
        carrierOrder.setLoadTime(now);
        carrierOrder.setGoodsUnit(tDemandOrder.getGoodsUnit());

        carrierOrder.setDemandOrderSource(tDemandOrder.getSource());
        carrierOrder.setDemandOrderEntrustType(tDemandOrder.getEntrustType());
        carrierOrder.setSettlementTonnage(tDemandOrder.getSettlementTonnage());
        carrierOrder.setCarrierSettlement(tDemandOrder.getCarrierSettlement());

        carrierOrder.setPublishName(tDemandOrder.getPublishName());
        carrierOrder.setPublishMobile(tDemandOrder.getPublishMobile());
        carrierOrder.setPublishTime(tDemandOrder.getPublishTime());
        carrierOrder.setPublishOrgCode(tDemandOrder.getPublishOrgCode());
        carrierOrder.setPublishOrgName(tDemandOrder.getPublishOrgName());
        carrierOrder.setIfUrgent(tDemandOrder.getIfUrgent());
        carrierOrder.setAvailableOnWeekends(tDemandOrder.getAvailableOnWeekends());
        carrierOrder.setLoadingUnloadingPart(tDemandOrder.getLoadingUnloadingPart());
        carrierOrder.setLoadingUnloadingCharge(tDemandOrder.getLoadingUnloadingCharge());
        carrierOrder.setRecycleTaskType(tDemandOrder.getRecycleTaskType());
        carrierOrder.setProjectLabel(tDemandOrder.getProjectLabel());
        carrierOrder.setRemark(tDemandOrder.getRemark());
        carrierOrder.setDeliveryMethod(CarrierOrderDeliverMethodEnum.BILL_PICKUP.getKey());
        commonBiz.setBaseEntityAdd(carrierOrder, userName);
        tCarrierOrderMapper.insertSelectiveEncrypt(carrierOrder);

        //生成运单地址
        TCarrierOrderAddress carrierOrderAddress = MapperUtils.mapper(tDemandOrderAddress, TCarrierOrderAddress.class);
        carrierOrderAddress.setId(null);
        carrierOrderAddress.setCarrierOrderId(carrierOrder.getId());
        commonBiz.setBaseEntityAdd(carrierOrderAddress, userName);
        tCarrierOrderAddressMapper.insertSelective(carrierOrderAddress);

        //生成运单货物
        TCarrierOrderGoods carrierOrderGoods;
        List<TCarrierOrderGoods> carrierOrderGoodsList = new ArrayList<>();
        for (TDemandOrderGoods goods : tDemandOrderGoodsList) {
            carrierOrderGoods = MapperUtils.mapper(goods, TCarrierOrderGoods.class);
            carrierOrderGoods.setId(null);
            carrierOrderGoods.setCarrierOrderId(carrierOrder.getId());
            carrierOrderGoods.setDemandOrderGoodsId(goods.getId());
            carrierOrderGoods.setExpectAmount(goods.getGoodsAmount());
            carrierOrderGoods.setLoadAmount(goods.getGoodsAmount());
            commonBiz.setBaseEntityAdd(carrierOrderGoods, userName);
            carrierOrderGoodsList.add(carrierOrderGoods);
        }
        if (ListUtils.isNotEmpty(carrierOrderGoodsList)){
            tCarrierOrderGoodsMapper.batchInsertSelective(carrierOrderGoodsList);
        }

        //生成运单车辆历史
        TCarrierOrderVehicleHistory carrierOrderVehicleHistory = new TCarrierOrderVehicleHistory();
        carrierOrderVehicleHistory.setCarrierOrderId(carrierOrder.getId());
        carrierOrderVehicleHistory.setVehicleId(createCarrierOrderModel.getVehicleId());
        carrierOrderVehicleHistory.setVehicleNo(createCarrierOrderModel.getVehicleNo());
        carrierOrderVehicleHistory.setDriverId(createCarrierOrderModel.getStaffId());
        carrierOrderVehicleHistory.setDriverName(createCarrierOrderModel.getStaffName());
        carrierOrderVehicleHistory.setDriverMobile(createCarrierOrderModel.getStaffMobile());
        carrierOrderVehicleHistory.setDriverIdentity(createCarrierOrderModel.getDriverIdentity());
        carrierOrderVehicleHistory.setExpectLoadTime(now);
        carrierOrderVehicleHistory.setAuditStatus(AuditStatusEnum.NOT_NEED_AUDIT.getKey());
        commonBiz.setBaseEntityAdd(carrierOrderVehicleHistory, userName);
        tCarrierOrderVehicleHistoryMapper.insertSelective(carrierOrderVehicleHistory);

        //运单提货单据
        TCarrierOrderTickets tCarrierOrderTickets;
        List<TCarrierOrderTickets> tCarrierOrderTicketsList = new ArrayList<>();
        if (ListUtils.isNotEmpty(createCarrierOrderModel.getConfirmPictureList())) {
            for (String picUrl : createCarrierOrderModel.getConfirmPictureList()) {
                tCarrierOrderTickets = new TCarrierOrderTickets();
                tCarrierOrderTickets.setCarrierOrderId(carrierOrder.getId());
                tCarrierOrderTickets.setImageName(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_LOAD_TICKETS.getValue());
                tCarrierOrderTickets.setImagePath(picUrl);
                tCarrierOrderTickets.setImageType(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_LOAD_TICKETS.getKey());
                tCarrierOrderTickets.setUploadTime(now);
                tCarrierOrderTickets.setUploadUserName(BaseContextHandler.getUserName());
                commonBiz.setBaseEntityAdd(tCarrierOrderTickets, BaseContextHandler.getUserName());
                tCarrierOrderTicketsList.add(tCarrierOrderTickets);
            }
        }

        if (ListUtils.isNotEmpty(tCarrierOrderTicketsList)) {
            tCarrierOrderTicketsMapper.batchInsertTickets(tCarrierOrderTicketsList);
        }

        //生成运单日志、需求单日志
        AsyncProcessQueue.execute(() -> addCarrierOrderLogs(carrierOrder, createCarrierOrderModel));

        //生成车辆gps与线路图
        AsyncProcessQueue.execute(() -> createVehicleGpsLine(carrierOrder, carrierOrderAddress, carrierOrderVehicleHistory, dispatchOrder));

        //同步新生生成账单
        InsertBillOrderLogisticsMessage insertBillOrderLogisticsMessage = new InsertBillOrderLogisticsMessage();
        insertBillOrderLogisticsMessage.setTotalPrice(createCarrierOrderModel.getGoodsPriceTotal());
        insertBillOrderLogisticsMessage.setReceiverName(createCarrierOrderModel.getStaffName());
        insertBillOrderLogisticsMessage.setReceiverPhone(createCarrierOrderModel.getStaffMobile());
        insertBillOrderLogisticsMessage.setBillOrderAttachment(createCarrierOrderModel.getConfirmPictureList());
        insertBillOrderLogisticsMessage.setOperator(userName);
        rabbitMqPublishBiz.insertBillOrderLogisticsMessage2YeloLife(insertBillOrderLogisticsMessage);

        //后续如果放开此代码，则此mq要改为同步给新生，由新生转发给云仓或工厂
//        //同步云仓生成入库单
//        List<LifeStockInProductModel> lifeStockInProductModels = new ArrayList<>();
//        LifeStockInProductModel lifeStockInProductModel;
//        for (TDemandOrderGoods goods : tDemandOrderGoodsList) {
//            lifeStockInProductModel = new LifeStockInProductModel();
//            lifeStockInProductModel.setSkuCode(goods.getSkuCode());
//            lifeStockInProductModel.setCount(goods.getGoodsAmount());
//            lifeStockInProductModels.add(lifeStockInProductModel);
//        }
//
//        CreateRecycleWarehouseLifeStockInLogisticsMessage stockInLogisticsMessage = new CreateRecycleWarehouseLifeStockInLogisticsMessage();
//        stockInLogisticsMessage.setCarrierOrderCode(carrierOrderCode);
//        stockInLogisticsMessage.setVehicleNumber(createCarrierOrderModel.getVehicleNo());
//        stockInLogisticsMessage.setDriverName(createCarrierOrderModel.getStaffName());
//        stockInLogisticsMessage.setDriverMobilePhone(createCarrierOrderModel.getStaffMobile());
//        stockInLogisticsMessage.setToAddressCode(tDemandOrderAddress.getUnloadAddressCode());
//        stockInLogisticsMessage.setFromAddressProvince(tDemandOrderAddress.getLoadProvinceName());
//        stockInLogisticsMessage.setFromAddressCity(tDemandOrderAddress.getLoadCityName());
//        stockInLogisticsMessage.setFromAddressArea(tDemandOrderAddress.getLoadAreaName());
//        stockInLogisticsMessage.setFromAddressDetail(tDemandOrderAddress.getLoadDetailAddress());
//        if (CompanyTypeEnum.COMPANY.getKey().equals(createCarrierOrderModel.getBusinessType())) {
//            stockInLogisticsMessage.setFromCustomerName(createCarrierOrderModel.getCustomerName());
//        }else if (CompanyTypeEnum.PERSON.getKey().equals(createCarrierOrderModel.getBusinessType())){
//            stockInLogisticsMessage.setFromCustomerName(createCarrierOrderModel.getCustomerUserName());
//            stockInLogisticsMessage.setFromCustomerPhone(createCarrierOrderModel.getCustomerUserMobile());
//        }
//        stockInLogisticsMessage.setUserName(userName);
//        stockInLogisticsMessage.setRemark(tDemandOrder.getRemark());
//        stockInLogisticsMessage.setLifeStockInProductModels(lifeStockInProductModels);
//        rabbitMqPublishBiz.createRecycleWarehouseLifeStockInLogistics(stockInLogisticsMessage);

        return carrierOrderCode;
    }

    //生成运单日志、需求单日志
    public void addCarrierOrderLogs(TCarrierOrder tCarrierOrder, CreateCarrierOrderByAssociatedVehicleModel createCarrierOrderModel){
        Date now = tCarrierOrder.getDispatchTime();
        String userName = tCarrierOrder.getPublishName();
        String unit = GoodsUnitEnum.getEnum(tCarrierOrder.getGoodsUnit()).getUnit();

        //生成运单事件
        List<TCarrierOrderEvents> carrierOrderEventsList = new ArrayList<>();
        //承接订单
        TCarrierOrderEvents carrierOrderEvents = getCarrierOrderEvent(tCarrierOrder.getId(), CarrierOrderEventsTypeEnum.BY_ORDER, userName, "", now);
        carrierOrderEventsList.add(carrierOrderEvents);
        //调度车辆
        carrierOrderEvents = getCarrierOrderEvent(tCarrierOrder.getId(), CarrierOrderEventsTypeEnum.DISPATCH_VEHICLE, userName, CarrierOrderEventsTypeEnum.DISPATCH_VEHICLE.getFormat(), now);
        carrierOrderEventsList.add(carrierOrderEvents);
        //待到达提货地
        carrierOrderEvents = getCarrierOrderEvent(tCarrierOrder.getId(), CarrierOrderEventsTypeEnum.ARRIVED_PICK_UP, userName, CarrierOrderEventsTypeEnum.ARRIVED_PICK_UP.getFormat(), now);
        carrierOrderEventsList.add(carrierOrderEvents);
        //提货
        carrierOrderEvents = getCarrierOrderEvent(tCarrierOrder.getId(), CarrierOrderEventsTypeEnum.PICK_UP, userName, CarrierOrderEventsTypeEnum.PICK_UP.format(StripTrailingZerosUtils.stripTrailingZerosToString(tCarrierOrder.getLoadAmount()) + unit), now);
        carrierOrderEventsList.add(carrierOrderEvents);

        //生成操作日志
        List<TCarrierOrderOperateLogs> carrierOrderOperateLogsList = new ArrayList<>();
        //生成运单
        TCarrierOrderOperateLogs carrierOrderOperateLogs = getCarrierOrderOperateLogs(tCarrierOrder.getId(), CarrierOrderOperateLogsTypeEnum.CREATE_CARRIER_ORDER, userName, CarrierOrderOperateLogsTypeEnum.CREATE_CARRIER_ORDER.format(createCarrierOrderModel.getVehicleNo(), createCarrierOrderModel.getStaffName() + " " + createCarrierOrderModel.getStaffMobile()), now);
        carrierOrderOperateLogsList.add(carrierOrderOperateLogs);
        //到达提货地
        carrierOrderOperateLogs = getCarrierOrderOperateLogs(tCarrierOrder.getId(), CarrierOrderOperateLogsTypeEnum.ARRIVED_PICK_UP, userName, CarrierOrderOperateLogsTypeEnum.ARRIVED_PICK_UP.getFormat(), now);
        carrierOrderOperateLogsList.add(carrierOrderOperateLogs);
        //提货
        carrierOrderOperateLogs = getCarrierOrderOperateLogs(tCarrierOrder.getId(), CarrierOrderOperateLogsTypeEnum.PICK_UP, userName, CarrierOrderOperateLogsTypeEnum.PICK_UP.format(StripTrailingZerosUtils.stripTrailingZerosToString(tCarrierOrder.getLoadAmount()) + unit), now);
        carrierOrderOperateLogsList.add(carrierOrderOperateLogs);



        //生成需求单日志
        List<TDemandOrderOperateLogs> demandOrderOperateLogsList = new ArrayList<>();
        //调度车辆
        TDemandOrderOperateLogs demandOrderOperateLogs = getDemandOrderOperateLogs(tCarrierOrder.getDemandOrderId(), DemandOrderOperateLogsEnum.DISPATCH_VEHICLE, userName, "", now, String.format(DemandOrderOperateLogsEnum.DISPATCH_VEHICLE.getValue(), tCarrierOrder.getCarrierOrderCode()));
        demandOrderOperateLogsList.add(demandOrderOperateLogs);
        //完成调度
        demandOrderOperateLogs = getDemandOrderOperateLogs(tCarrierOrder.getDemandOrderId(), DemandOrderOperateLogsEnum.COMPLETE_DISPATCH, userName, DemandOrderOperateLogsEnum.COMPLETE_DISPATCH.getValue(), now, null);
        demandOrderOperateLogsList.add(demandOrderOperateLogs);

        //生成需求单事件
        List<TDemandOrderEvents> demandOrderEventsList = new ArrayList<>();
        //调度车辆
        TDemandOrderEvents demandOrderEvents = getDemandOrderEvent(tCarrierOrder.getDemandOrderId(), tCarrierOrder.getCompanyCarrierId(), DemandOrderEventsTypeEnum.DISPATCH_VEHICLE, userName, now);
        demandOrderEventsList.add(demandOrderEvents);
        //完成调度
        demandOrderEvents = getDemandOrderEvent(tCarrierOrder.getDemandOrderId(), tCarrierOrder.getCompanyCarrierId(), DemandOrderEventsTypeEnum.COMPLETE_DISPATCH_EVENTS, userName, now);
        demandOrderEventsList.add(demandOrderEvents);


        //落库
        if (ListUtils.isNotEmpty(carrierOrderEventsList)) {
            tCarrierOrderEventsMapper.batchInsertSelective(carrierOrderEventsList);
        }
        if (ListUtils.isNotEmpty(carrierOrderOperateLogsList)) {
            tCarrierOrderOperateLogsMapper.batchInsertSelective(carrierOrderOperateLogsList);
        }
        if (ListUtils.isNotEmpty(demandOrderOperateLogsList)) {
            tDemandOrderOperateLogsMapper.batchInsertSelective(demandOrderOperateLogsList);
        }
        if (ListUtils.isNotEmpty(demandOrderEventsList)) {
            tDemandOrderEventsMapper.batchInsertSelective(demandOrderEventsList);
        }
    }

    //创建运单事件
    private TCarrierOrderEvents getCarrierOrderEvent(Long carrierOrderId, CarrierOrderEventsTypeEnum eventTypeEnum, String userName, String remark, Date now) {
        TCarrierOrderEvents carrierOrderEvents = new TCarrierOrderEvents();
        carrierOrderEvents.setCarrierOrderId(carrierOrderId);
        carrierOrderEvents.setEvent(eventTypeEnum.getKey());
        carrierOrderEvents.setEventDesc(eventTypeEnum.getValue());
        carrierOrderEvents.setRemark(remark);
        carrierOrderEvents.setEventTime(now);
        carrierOrderEvents.setOperatorName(userName);
        carrierOrderEvents.setOperateTime(now);
        commonBiz.setBaseEntityAdd(carrierOrderEvents, userName);
        return carrierOrderEvents;
    }

    //创建运单日志
    private TCarrierOrderOperateLogs getCarrierOrderOperateLogs(Long carrierOrderId, CarrierOrderOperateLogsTypeEnum logTypeEnum, String userName, String remark, Date now) {
        TCarrierOrderOperateLogs carrierOrderOperateLogs = new TCarrierOrderOperateLogs();
        carrierOrderOperateLogs.setCarrierOrderId(carrierOrderId);
        carrierOrderOperateLogs.setOperationType(logTypeEnum.getKey());
        carrierOrderOperateLogs.setOperationContent(logTypeEnum.getValue());
        carrierOrderOperateLogs.setRemark(remark);
        carrierOrderOperateLogs.setOperatorName(userName);
        carrierOrderOperateLogs.setOperateTime(now);
        commonBiz.setBaseEntityAdd(carrierOrderOperateLogs, userName);
        return carrierOrderOperateLogs;
    }

    //创建需求单日志
    public TDemandOrderOperateLogs getDemandOrderOperateLogs(Long demandOrderId, DemandOrderOperateLogsEnum logsEnum, String userName, String remark, Date now, String operationContent) {
        TDemandOrderOperateLogs demandOrderOperateLogs = new TDemandOrderOperateLogs();
        demandOrderOperateLogs.setDemandOrderId(demandOrderId);
        demandOrderOperateLogs.setOperationType(logsEnum.getKey());
        if (StringUtils.isBlank(operationContent)) {
            demandOrderOperateLogs.setOperationContent(logsEnum.getValue());
        }else{
            demandOrderOperateLogs.setOperationContent(operationContent);
        }
        demandOrderOperateLogs.setRemark(remark);
        demandOrderOperateLogs.setOperatorName(userName);
        demandOrderOperateLogs.setOperateTime(now);
        commonBiz.setBaseEntityAdd(demandOrderOperateLogs, userName);
        return demandOrderOperateLogs;
    }

    //创建需求单事件
    public TDemandOrderEvents getDemandOrderEvent(Long demandOrderId, Long companyCarrierId, DemandOrderEventsTypeEnum typeEnum, String userName, Date now) {
        TDemandOrderEvents tDemandOrderEvents = new TDemandOrderEvents();
        tDemandOrderEvents.setDemandOrderId(demandOrderId);
        tDemandOrderEvents.setCompanyCarrierId(companyCarrierId);
        tDemandOrderEvents.setEvent(typeEnum.getKey());
        tDemandOrderEvents.setEventDesc(typeEnum.getValue());
        tDemandOrderEvents.setEventTime(now);
        tDemandOrderEvents.setOperatorName(userName);
        tDemandOrderEvents.setOperateTime(now);
        commonBiz.setBaseEntityAdd(tDemandOrderEvents, userName);
        return tDemandOrderEvents;
    }

    //生成车辆gps与线路图
    public void createVehicleGpsLine(TCarrierOrder tCarrierOrder, TCarrierOrderAddress tCarrierOrderAddress, TCarrierOrderVehicleHistory tCarrierOrderVehicleHistory, TDispatchOrder tDispatchOrder){
        String userName = tCarrierOrder.getPublishName();

        //新增或修改车辆GPS跟踪信息
        TVehicleBasicModel gpsVehicle = new TVehicleBasicModel();
        gpsVehicle.setVehicleBasicId(tCarrierOrderVehicleHistory.getVehicleId());
        gpsVehicle.setVehicleNo(tCarrierOrderVehicleHistory.getVehicleNo());
        TStaffBasicModel gpsDriver = new TStaffBasicModel();
        gpsDriver.setStaffBasicId(tCarrierOrderVehicleHistory.getDriverId());
        gpsDriver.setName(tCarrierOrderVehicleHistory.getDriverName());
        gpsDriver.setMobile(tCarrierOrderVehicleHistory.getDriverMobile());
        gpsDriver.setIdentityNumber(tCarrierOrderVehicleHistory.getDriverIdentity());
        staffVehicleBiz.syncVehicleGpsInfo(gpsVehicle, gpsDriver, tDispatchOrder.getId(), tDispatchOrder.getDispatchOrderCode(), userName);

        //生成运单与微信推送关系表
        //发货人
        List<TCarrierOrderWx> insertCarrierOrderWx = new ArrayList<>();
        TCarrierOrderWx tCarrierOrderWx;
        if (RegExpValidatorUtil.isMobile(tCarrierOrderAddress.getConsignorMobile())) {
            tCarrierOrderWx = new TCarrierOrderWx();
            tCarrierOrderWx.setCarrierOrderId(tCarrierOrder.getId());
            tCarrierOrderWx.setMobile(tCarrierOrderAddress.getConsignorMobile());
            tCarrierOrderWx.setName(tCarrierOrderAddress.getConsignorName());
            tCarrierOrderWx.setRole(WeixinPushInfoRoleEnum.SENDER.getKey());
            commonBiz.setBaseEntityAdd(tCarrierOrderWx, userName);
            insertCarrierOrderWx.add(tCarrierOrderWx);
        }
        //收货人
        if (RegExpValidatorUtil.isMobile(tCarrierOrderAddress.getReceiverMobile())) {
            tCarrierOrderWx = new TCarrierOrderWx();
            tCarrierOrderWx.setCarrierOrderId(tCarrierOrder.getId());
            tCarrierOrderWx.setMobile(tCarrierOrderAddress.getReceiverMobile());
            tCarrierOrderWx.setName(tCarrierOrderAddress.getReceiverName());
            tCarrierOrderWx.setRole(WeixinPushInfoRoleEnum.RECEIVER.getKey());
            commonBiz.setBaseEntityAdd(tCarrierOrderWx, userName);
            insertCarrierOrderWx.add(tCarrierOrderWx);
        }
        if (ListUtils.isNotEmpty(insertCarrierOrderWx)) {
            tCarrierOrderWxMapper.batchInsert(insertCarrierOrderWx);
        }

        //生成车辆路线图
        TVehicleTransportLine lineRequest = new TVehicleTransportLine();
        lineRequest.setVehicleId(tCarrierOrderVehicleHistory.getVehicleId());
        lineRequest.setVehicleNo(tCarrierOrderVehicleHistory.getVehicleNo());
        lineRequest.setLoadCityId(tCarrierOrderAddress.getLoadCityId());
        lineRequest.setLoadAreaId(tCarrierOrderAddress.getLoadAreaId());
        lineRequest.setUnloadCityId(tCarrierOrderAddress.getUnloadCityId());
        lineRequest.setUnloadAreaId(tCarrierOrderAddress.getUnloadAreaId());
        TVehicleTransportLine tVehicleTransportLine = tVehicleTransportLineMapper.getLineByVehicleId(lineRequest);
        if (null == tVehicleTransportLine) {//线路不存在则新增
            TVehicleTransportLine vehicleTransportLine = MapperUtils.mapper(tCarrierOrderAddress, TVehicleTransportLine.class);
            vehicleTransportLine.setId(null);
            vehicleTransportLine.setVehicleId(tCarrierOrderVehicleHistory.getVehicleId());
            vehicleTransportLine.setVehicleNo(tCarrierOrderVehicleHistory.getVehicleNo());
            commonBiz.setBaseEntityAdd(vehicleTransportLine, userName);
            tVehicleTransportLineMapper.insertSelective(vehicleTransportLine);
        }
    }
}
