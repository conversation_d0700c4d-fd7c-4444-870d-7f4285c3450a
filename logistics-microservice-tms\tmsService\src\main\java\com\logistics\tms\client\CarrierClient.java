//package com.logistics.tms.client;
//
//import com.logistics.carrier.api.feign.carrierorder.management.CarrierOrderManagementForManageServiceApi;
//import com.logistics.carrier.api.feign.carrierorder.management.model.SyncCarrierOrderLoadAmountRequestModel;
//import com.yelo.tray.core.base.Result;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
///**
// * @author: wjf
// * @date: 2022/9/19 14:30
// */
//@Service
//public class CarrierClient {
//
//    @Autowired
//    private CarrierOrderManagementForManageServiceApi carrierOrderManagementForManageServiceApi;
//
////    /**
////     * 托盘同步运单货物出库数量
////     * @param requestModel
////     */
////    public  void synCarrierOrderLoadAmount(SyncCarrierOrderLoadAmountRequestModel requestModel){
////        Result result = carrierOrderManagementForManageServiceApi.synCarrierOrderLoadAmount(requestModel);
////        result.throwException();
////    }
//}
