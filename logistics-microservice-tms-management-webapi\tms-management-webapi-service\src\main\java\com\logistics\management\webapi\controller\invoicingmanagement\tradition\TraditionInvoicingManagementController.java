package com.logistics.management.webapi.controller.invoicingmanagement.tradition;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.SettleStatementTypeEnum;
import com.logistics.management.webapi.base.utils.ConvertPageInfoUtil;
import com.logistics.management.webapi.client.invoicingmanagement.InvoicingManagementClient;
import com.logistics.management.webapi.client.invoicingmanagement.request.*;
import com.logistics.management.webapi.client.invoicingmanagement.response.*;
import com.logistics.management.webapi.controller.invoicingmanagement.tradition.mapping.*;
import com.logistics.management.webapi.controller.invoicingmanagement.tradition.request.*;
import com.logistics.management.webapi.controller.invoicingmanagement.tradition.response.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 自营业务发票管理
 * @author: wjf
 * @date: 2024/6/25 9:33
 */
@Api(value = "自营业务发票管理",tags = "自营业务发票管理")
@RestController
@RequestMapping(value = "/api/invoicingManagement/tradition")
public class TraditionInvoicingManagementController {

    @Resource
    private InvoicingManagementClient invoicingManagementClient;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 发票管理-列表 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-列表")
    @PostMapping(value = "/searchList")
    public Result<PageInfo<TraditionSearchInvoicingManagementListResponseDto>> searchList(@RequestBody TraditionSearchInvoicingManagementListRequestDto requestDto){
        SearchInvoicingManagementListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchInvoicingManagementListRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
        Result<PageInfo<SearchInvoicingManagementListResponseModel>> result = invoicingManagementClient.searchList(requestModel);
        result.throwException();
        PageInfo<TraditionSearchInvoicingManagementListResponseDto> pageInfo = ConvertPageInfoUtil.convertPageInfo(result.getData(), TraditionSearchInvoicingManagementListResponseDto.class, new TraditionSearchInvoicingManagementListMapping());
        return Result.success(pageInfo);
    }

    /**
     * 发票管理-查看发票 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-查看发票")
    @PostMapping(value = "/getInvoicePictures")
    public Result<List<TraditionGetInvoicePicturesResponseDto>> getInvoicePictures(@RequestBody @Valid TraditionInvoicingManagementDetailRequestDto requestDto){
        InvoicingManagementDetailRequestModel requestModel = MapperUtils.mapper(requestDto, InvoicingManagementDetailRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
        Result<List<GetInvoicePicturesResponseModel>> result = invoicingManagementClient.getInvoicePictures(requestModel);
        List<String> sourceSrcList = new ArrayList<>();
        if (ListUtils.isNotEmpty(result.getData())){
            sourceSrcList = result.getData().stream().map(GetInvoicePicturesResponseModel::getInvoicePicture).collect(Collectors.toList());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(), TraditionGetInvoicePicturesResponseDto.class, new TraditionGetInvoicePicturesMapping(configKeyConstant.fileAccessAddress, imageMap)));
    }

    /**
     * 发票管理-详情-头部信息 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-头部信息")
    @PostMapping(value = "/getDetail")
    public Result<TraditionInvoicingManagementDetailResponseDto> getDetail(@RequestBody @Valid TraditionInvoicingManagementDetailRequestDto requestDto){
        InvoicingManagementDetailRequestModel requestModel = MapperUtils.mapper(requestDto, InvoicingManagementDetailRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
        Result<InvoicingManagementDetailResponseModel> result = invoicingManagementClient.getDetail(requestModel);
        return Result.success(MapperUtils.mapper(result.getData(), TraditionInvoicingManagementDetailResponseDto.class, new TraditionInvoicingManagementDetailMapping()));
    }

    /**
     * 发票管理-详情-发票列表 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-发票列表")
    @PostMapping(value = "/getInvoiceList")
    public Result<List<TraditionGetInvoiceListResponseDto>> getInvoiceList(@RequestBody @Valid TraditionInvoicingManagementDetailRequestDto requestDto){
        InvoicingManagementDetailRequestModel requestModel = MapperUtils.mapper(requestDto, InvoicingManagementDetailRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
        Result<List<GetInvoiceListResponseModel>> result = invoicingManagementClient.getInvoiceList(requestModel);
        return Result.success(MapperUtils.mapper(result.getData(), TraditionGetInvoiceListResponseDto.class,new TraditionGetInvoiceListMapping()));
    }

    /**
     * 发票管理-详情-发票列表-查看详情 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-发票列表-查看详情")
    @PostMapping(value = "/getInvoiceDetail")
    public Result<TraditionGetInvoiceDetailResponseDto> getInvoiceDetail(@RequestBody @Valid TraditionGetInvoiceDetailRequestDto requestDto){
        GetInvoiceDetailRequestModel requestModel = MapperUtils.mapper(requestDto, GetInvoiceDetailRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
        Result<GetInvoiceDetailResponseModel> result = invoicingManagementClient.getInvoiceDetail(requestModel);
        return Result.success(MapperUtils.mapper(result.getData(), TraditionGetInvoiceDetailResponseDto.class));
    }

    /**
     * 发票管理-详情-发票列表-新增/编辑发票 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-发票列表-新增/编辑发票")
    @PostMapping(value = "/addOrModifyInvoice")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addOrModifyInvoice(@RequestBody @Valid TraditionAddOrModifyInvoiceRequestDto requestDto){
        AddOrModifyInvoiceRequestModel requestModel = MapperUtils.mapper(requestDto, AddOrModifyInvoiceRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
        return invoicingManagementClient.addOrModifyInvoice(requestModel);
    }

    /**
     * 发票管理-详情-发票列表-删除发票 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-发票列表-删除发票")
    @PostMapping(value = "/delInvoice")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> delInvoice(@RequestBody @Valid TraditionGetInvoiceDetailRequestDto requestDto){
        GetInvoiceDetailRequestModel requestModel = MapperUtils.mapper(requestDto, GetInvoiceDetailRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
        return invoicingManagementClient.delInvoice(requestModel);
    }

    /**
     * 发票管理-详情-对账单列表 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-对账单列表")
    @PostMapping(value = "/getSettleStatementList")
    public Result<PageInfo<TraditionGetSettleStatementListResponseDto>> getSettleStatementList(@RequestBody @Valid TraditionGetSettleStatementListRequestDto requestDto){
        GetSettleStatementListRequestModel requestModel = MapperUtils.mapper(requestDto, GetSettleStatementListRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
        Result<PageInfo<GetSettleStatementListResponseModel>> result = invoicingManagementClient.getSettleStatementList(requestModel);
        result.throwException();
        PageInfo<TraditionGetSettleStatementListResponseDto> pageInfo = ConvertPageInfoUtil.convertPageInfo(result.getData(), TraditionGetSettleStatementListResponseDto.class, new TraditionGetSettleStatementListMapping());
        return Result.success(pageInfo);
    }

    /**
     * 发票管理-详情-对账单-添加对账单列表 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-对账单-添加对账单列表")
    @PostMapping(value = "/getAddSettleStatementList")
    public Result<PageInfo<TraditionGetAddSettleStatementListResponseDto>> getAddSettleStatementList(@RequestBody @Valid TraditionGetAddSettleStatementListRequestDto requestDto){
        GetAddSettleStatementListRequestModel requestModel = MapperUtils.mapper(requestDto, GetAddSettleStatementListRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
        Result<PageInfo<GetAddSettleStatementListResponseModel>> result = invoicingManagementClient.getAddSettleStatementList(requestModel);
        result.throwException();
        PageInfo<TraditionGetAddSettleStatementListResponseDto> pageInfo = ConvertPageInfoUtil.convertPageInfo(result.getData(), TraditionGetAddSettleStatementListResponseDto.class);
        return Result.success(pageInfo);
    }

    /**
     * 发票管理-详情-对账单-添加对账单 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-对账单-添加对账单")
    @PostMapping(value = "/addSettleStatement")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addSettleStatement(@RequestBody @Valid TraditionAddInvoicingSettleStatementRequestDto requestDto){
        AddInvoicingSettleStatementRequestModel requestModel = MapperUtils.mapper(requestDto, AddInvoicingSettleStatementRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
        return invoicingManagementClient.addSettleStatement(requestModel);
    }

    /**
     * 发票管理-详情-对账单-移除对账单 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-对账单-移除对账单")
    @PostMapping(value = "/delSettleStatement")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> delSettleStatement(@RequestBody @Valid TraditionDelSettleStatementRequestDto requestDto){
        DelSettleStatementRequestModel requestModel = MapperUtils.mapper(requestDto, DelSettleStatementRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
        return invoicingManagementClient.delSettleStatement(requestModel);
    }

    /**
     * 发票管理-详情-归档列表 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-归档列表")
    @PostMapping(value = "/getInvoicingArchiveList")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<List<TraditionGetInvoicingArchiveListResponseDto>> getInvoicingArchiveList(@RequestBody @Valid TraditionInvoicingManagementDetailRequestDto requestDto){
        InvoicingManagementDetailRequestModel requestModel = MapperUtils.mapper(requestDto, InvoicingManagementDetailRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
        Result<List<GetInvoicingArchiveListResponseModel>> result = invoicingManagementClient.getInvoicingArchiveList(requestModel);
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        if (ListUtils.isNotEmpty(result.getData())){
            sourceSrcList = result.getData().stream().map(GetInvoicingArchiveListResponseModel::getRelativePath).collect(Collectors.toList());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(), TraditionGetInvoicingArchiveListResponseDto.class, new TraditionGetInvoicingArchiveListMapping(configKeyConstant.fileAccessAddress, imageMap)));
    }

    /**
     * 发票管理-详情-确认归档 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-确认归档")
    @PostMapping(value = "/invoicingArchive")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> invoicingArchive(@RequestBody @Valid TraditionInvoicingArchiveRequestDto requestDto){
        InvoicingArchiveRequestModel requestModel = MapperUtils.mapper(requestDto, InvoicingArchiveRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
        return invoicingManagementClient.invoicingArchive(requestModel);
    }
}
