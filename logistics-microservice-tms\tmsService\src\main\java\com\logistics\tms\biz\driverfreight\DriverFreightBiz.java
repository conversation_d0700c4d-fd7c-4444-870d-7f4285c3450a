package com.logistics.tms.biz.driverfreight;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.driverfreight.model.DriverFreightListSearchRequestModel;
import com.logistics.tms.api.feign.driverfreight.model.DriverFreightListSearchResponseModel;
import com.logistics.tms.base.enums.IsOurCompanyEnum;
import com.logistics.tms.biz.carrierorderotherfee.CarrierOrderOtherFeeCommonBiz;
import com.logistics.tms.entity.TCompanyCarrier;
import com.logistics.tms.entity.TVehicleBasic;
import com.logistics.tms.mapper.TCarrierOrderMapper;
import com.logistics.tms.mapper.TCompanyCarrierMapper;
import com.logistics.tms.mapper.TVehicleBasicMapper;
import com.yelo.tools.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DriverFreightBiz {

    @Autowired
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Autowired
    private TVehicleBasicMapper tVehicleBasicMapper;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private CarrierOrderOtherFeeCommonBiz carrierOrderOtherFeeCommonBiz;

    /**
     * 司机运费列表
     * @param requestModel
     * @return
     */
    public PageInfo<DriverFreightListSearchResponseModel> driverFreightList(DriverFreightListSearchRequestModel requestModel){
        requestModel.enablePaging();
        List<Long> carrierOrderIdList = tCarrierOrderMapper.searchDriverFreightOrdersIds(requestModel);
        PageInfo page = new PageInfo(carrierOrderIdList);

        String carrierOrderIds = requestModel.getCarrierOrderIds();
        if(StringUtils.isBlank(carrierOrderIds)){
            carrierOrderIds = StringUtils.listToString(carrierOrderIdList,',');
        }

        if(StringUtils.isNotEmpty(carrierOrderIds)){
            List<DriverFreightListSearchResponseModel> driverFreightList = tCarrierOrderMapper.searchDriverFreightOrdersByIds(carrierOrderIds);

            //车辆id
            List<Long> vehicleIdList = new ArrayList<>();
            //车主id
            List<Long> companyCarrierIdList = new ArrayList<>();
            for (DriverFreightListSearchResponseModel model : driverFreightList) {
                if (!vehicleIdList.contains(model.getVehicleId())){
                    vehicleIdList.add(model.getVehicleId());
                }
                if (!companyCarrierIdList.contains(model.getCompanyCarrierId())){
                    companyCarrierIdList.add(model.getCompanyCarrierId());
                }
            }

            //查询车辆信息
            List<TVehicleBasic> tVehicleBasicList = tVehicleBasicMapper.getByIds(StringUtils.listToString(vehicleIdList,','));
            Map<Long, Integer> vehicleMap = tVehicleBasicList.stream().collect(Collectors.toMap(TVehicleBasic::getId, TVehicleBasic::getVehicleProperty));

            //查询车主信息
            Map<Long, Integer> companyLevelMap = tCompanyCarrierMapper.getByIds(StringUtils.listToString(companyCarrierIdList,',')).stream().collect(Collectors.toMap(TCompanyCarrier::getId, TCompanyCarrier::getLevel));

            //查询运单临时费用信息
            Map<Long, BigDecimal> driverOtherFeeMap = carrierOrderOtherFeeCommonBiz.getAuditFee(carrierOrderIds);

            for (DriverFreightListSearchResponseModel model : driverFreightList) {
                //赋值车辆机构
                model.setVehicleProperty(vehicleMap.get(model.getVehicleId()));

                //赋值司机临时费用-我司时，司机才加临时费用
                if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(companyLevelMap.get(model.getCompanyCarrierId()))) {
                    model.setDriverOtherFee(driverOtherFeeMap.get(model.getCarrierOrderId()));
                }
            }

            page.setList(driverFreightList);
        }
        return page;
    }
}
