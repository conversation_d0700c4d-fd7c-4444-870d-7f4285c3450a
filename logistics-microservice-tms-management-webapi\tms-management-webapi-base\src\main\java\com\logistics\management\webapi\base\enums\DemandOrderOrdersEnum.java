package com.logistics.management.webapi.base.enums;

public enum DemandOrderOrdersEnum {
    DEFAULT(-99, ""),
    PLACE_AN_ORDER(1, "下单"),
    ADD(2, "追加"),
    SUPPLEMENT(3, "补单")


    ;
    private Integer key;
    private String value;

    DemandOrderOrdersEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static DemandOrderOrdersEnum getEnum(Integer key) {
        for (DemandOrderOrdersEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return DEFAULT;
    }
}
