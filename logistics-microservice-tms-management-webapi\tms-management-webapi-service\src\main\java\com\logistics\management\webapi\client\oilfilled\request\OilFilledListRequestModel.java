package com.logistics.management.webapi.client.oilfilled.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OilFilledListRequestModel extends AbstractPageForm<OilFilledListRequestModel> {

    @ApiModelProperty(value = "结算状态：0 待结算，1 已结算")
    private Integer status;
    @ApiModelProperty(value = "充油方式：1 充油卡，2 加油车")
    private Integer oilFilledType;
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆机构：1 自主 2 外部 3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty(value = "司机")
    private String name;
    @ApiModelProperty(value = "费用来源：10 充油，20 退款")
    private Integer source;
    @ApiModelProperty(value = "副卡卡号")
    private String subCardNumber;
    @ApiModelProperty(value = "副卡所属人")
    private String subCardOwner;
    @ApiModelProperty(value = "充值时间开始")
    private String oilFilledDateStart;
    @ApiModelProperty(value = "充值时间结束")
    private String oilFilledDateEnd;

    @ApiModelProperty(value = "充油IDs，用于选择导出")
    private String oilFilledIds;

}
