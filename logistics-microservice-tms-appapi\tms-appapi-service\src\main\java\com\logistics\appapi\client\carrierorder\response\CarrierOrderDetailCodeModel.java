package com.logistics.appapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CarrierOrderDetailCodeModel {

    @ApiModelProperty(value = "code", required = true)
    private String yeloCode;

    @ApiModelProperty(value = "装货数量/实提数量", required = true)
    private BigDecimal loadAmount;

    @ApiModelProperty(value = "卸货数量/实卸数量", required = true)
    private BigDecimal unloadAmount;

    @ApiModelProperty(value = "签收件数", required = true)
    private BigDecimal signAmount;

    @ApiModelProperty(value = "单位 1.kg", required = true)
    private Integer unit;

}
