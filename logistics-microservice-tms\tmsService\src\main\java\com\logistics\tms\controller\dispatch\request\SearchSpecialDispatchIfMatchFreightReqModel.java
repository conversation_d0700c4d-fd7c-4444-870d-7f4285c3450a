package com.logistics.tms.controller.dispatch.request;

import com.logistics.tms.controller.dispatchorder.request.SearchSpecialDispatchIfMatchFreightDemandReqModel;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

@Data
public class SearchSpecialDispatchIfMatchFreightReqModel {

    /**
     * 需求单集合
     */
    private List<SearchSpecialDispatchIfMatchFreightDemandReqModel> demandIdList;


    /**
     * 车长（米） -1就是零担
     */
    private BigDecimal vehicleLength;


}
