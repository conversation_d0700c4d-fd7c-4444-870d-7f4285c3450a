package com.logistics.tms.base.enums;


public enum YeloLifeGoodsUnitEnum {

    DEFAULT(0,"","",""),
    BY_PACKAGE(1,"（元/件）","件","元/件"),
    BY_WEIGHT(2,"（元/kg）","公斤(kg)","元/kg"),
    ;

    private Integer key;
    private String value;
    private String unit;
    private String priceUnit;

    YeloLifeGoodsUnitEnum(Integer key, String value, String unit, String priceUnit) {
        this.key = key;
        this.value = value;
        this.unit = unit;
        this.priceUnit = priceUnit;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getUnit() {
        return unit;
    }

    public String getPriceUnit() {
        return priceUnit;
    }

    public static YeloLifeGoodsUnitEnum getEnum(Integer key) {
        for (YeloLifeGoodsUnitEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
