package com.logistics.appapi.controller.baiscinfo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/4
 */
@Data
public class FaceRecognitionRequestDto {

	@ApiModelProperty(value = "身份证姓名", required = true)
	@NotBlank(message = "身份证姓名不能为空")
	private String name;

	@ApiModelProperty(value = "身份证号码", required = true)
	@NotBlank(message = "身份证号码不能为空")
	private String idNo;
}
