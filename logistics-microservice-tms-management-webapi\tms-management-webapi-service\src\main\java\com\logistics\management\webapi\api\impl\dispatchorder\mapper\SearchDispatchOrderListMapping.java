package com.logistics.management.webapi.api.impl.dispatchorder.mapper;

import com.logistics.management.webapi.api.feign.dispatchorder.dto.DispatchOrderSearchResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.AdjustFeeTypeEnum;
import com.logistics.management.webapi.base.enums.CarrierOrderStatusEnum;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.PriceTypeEnum;
import com.logistics.tms.api.feign.dispatchorder.model.CarrierOrderModel;
import com.logistics.tms.api.feign.dispatchorder.model.DispatchOrderSearchResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * @author: wjf
 * @date: 2018/9/26 11:21
 */
public class SearchDispatchOrderListMapping extends MapperMapping<DispatchOrderSearchResponseModel,DispatchOrderSearchResponseDto> {
    @Override
    public void configure() {
        DispatchOrderSearchResponseModel model = getSource();
        DispatchOrderSearchResponseDto dto = getDestination();
        if (null != model) {
            dto.setDriverName(model.getDriverName() + " " + model.getDriverMobile());
            if (model.getLoadPointAmount() > 0 && model.getUnloadPointAmount() > 0) {
                dto.setPointAmount(model.getLoadPointAmount() + "装" + model.getUnloadPointAmount() + "卸");
            }
            if (model.getAdjustFeeType().equals(AdjustFeeTypeEnum.MARK_UP.getKey())) {
                dto.setAdjustFee("+" + model.getAdjustFee());
            } else if (model.getAdjustFeeType().equals(AdjustFeeTypeEnum.MARK_DOWN.getKey())) {
                dto.setAdjustFee("-" + model.getAdjustFee());
            }
            if (model.getMarkupFee().compareTo(BigDecimal.ZERO) > 0) {
                dto.setMarkupFee("+" + model.getMarkupFee());
            }

            CarrierOrderModel carrierOrderModel = model.getCarrierOrderList().get(0);
            if (CompanyTypeEnum.PERSONAL.getKey().equals(carrierOrderModel.getCompanyCarrierType())) {
                dto.setCompanyCarrierName(carrierOrderModel.getCarrierContactName() + " " + carrierOrderModel.getCarrierContactPhone());
            } else {
                dto.setCompanyCarrierName(StringUtils.isBlank(carrierOrderModel.getCompanyCarrierName()) ? "" : carrierOrderModel.getCompanyCarrierName());
            }
            BigDecimal dispatchFreightFee = CommonConstant.BIG_DECIMAL_ZERO_NET_ZERO;
            BigDecimal signDispatchFreightFee = CommonConstant.BIG_DECIMAL_ZERO_NET_ZERO;
            boolean flag = false;
            for (CarrierOrderModel order : model.getCarrierOrderList()) {
                BigDecimal expectAmount = Optional.ofNullable(order.getCarrierExpectAmount()).orElse(CommonConstant.BIG_DECIMAL_ZERO_NET_ZERO);
                BigDecimal unloadAmount = CommonConstant.BIG_DECIMAL_ZERO_NET_ZERO;
                if (CommonConstant.INTEGER_ZERO.equals(order.getIfCancel())) {
                    unloadAmount = Optional.ofNullable(order.getCarrierUnloadAmount()).orElse(CommonConstant.BIG_DECIMAL_ZERO_NET_ZERO);
                }

                if (model.getDispatchFreightFeeType().equals(PriceTypeEnum.UNIT_PRICE.getKey())) {
                    dispatchFreightFee = dispatchFreightFee.add(order.getDispatchFreightFee().multiply(expectAmount).setScale(2, BigDecimal.ROUND_HALF_UP));
                } else if (model.getDispatchFreightFeeType().equals(PriceTypeEnum.FIXED_PRICE.getKey())) {
                    dispatchFreightFee = dispatchFreightFee.add(order.getDispatchFreightFee());
                }
                if (order.getStatus() > CarrierOrderStatusEnum.WAIT_UNLOAD.getKey()) {
                    if (model.getDispatchFreightFeeType().equals(PriceTypeEnum.UNIT_PRICE.getKey())) {
                        signDispatchFreightFee = signDispatchFreightFee.add(order.getDispatchFreightFee().multiply(unloadAmount).setScale(2, BigDecimal.ROUND_HALF_UP));
                    } else if (model.getDispatchFreightFeeType().equals(PriceTypeEnum.FIXED_PRICE.getKey())) {
                        signDispatchFreightFee = signDispatchFreightFee.add(order.getDispatchFreightFee());
                    }
                    flag = true;
                }
            }
            dto.setDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee));
            if (flag) {
                dto.setSignDispatchFreightFee(ConverterUtils.toString(signDispatchFreightFee));
            }
        }
    }
}
