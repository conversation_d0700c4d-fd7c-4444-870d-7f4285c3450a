<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDriverAppointMapper">
    <sql id="Base_Column_List_Decrypt">
        id, demand_order_id, demand_order_code, business_type, staff_id, staff_property, staff_name,
    AES_DECRYPT(UNHEX(staff_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')        as staff_mobile,
    vehicle_id, vehicle_no, goods_amount_total, goods_price_total,
    publish_time, customer_name, customer_user_name,
    AES_DECRYPT(UNHEX(customer_user_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')        as customer_user_mobile,
    publish_user_name, AES_DECRYPT(UNHEX(publish_user_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')        as publish_user_mobile,
    if_associated_vehicle, created_by, created_time, last_modified_by, last_modified_time, valid
    </sql>

    <select id="selectByPrimaryKeyDecrypt" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_driver_appoint
        where id = #{id,jdbcType=BIGINT}
        and valid = 1
    </select>

    <insert id="insertSelectiveEncrypt" parameterType="com.logistics.tms.entity.TDriverAppoint" keyProperty="id" useGeneratedKeys="true">
        insert into t_driver_appoint
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="demandOrderId != null">
                demand_order_id,
            </if>
            <if test="demandOrderCode != null">
                demand_order_code,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="staffId != null">
                staff_id,
            </if>
            <if test="staffProperty != null">
                staff_property,
            </if>
            <if test="staffName != null">
                staff_name,
            </if>
            <if test="staffMobile != null">
                staff_mobile,
            </if>
            <if test="vehicleId != null">
                vehicle_id,
            </if>
            <if test="vehicleNo != null">
                vehicle_no,
            </if>
            <if test="goodsAmountTotal != null">
                goods_amount_total,
            </if>
            <if test="goodsPriceTotal != null">
                goods_price_total,
            </if>
            <if test="publishTime != null">
                publish_time,
            </if>
            <if test="customerName != null">
                customer_name,
            </if>
            <if test="customerUserName != null">
                customer_user_name,
            </if>
            <if test="customerUserMobile != null">
                customer_user_mobile,
            </if>
            <if test="publishUserName != null">
                publish_user_name,
            </if>
            <if test="publishUserMobile != null">
                publish_user_mobile,
            </if>
            <if test="ifAssociatedVehicle != null">
                if_associated_vehicle,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by,
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time,
            </if>
            <if test="valid != null">
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="demandOrderId != null">
                #{demandOrderId,jdbcType=BIGINT},
            </if>
            <if test="demandOrderCode != null">
                #{demandOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=INTEGER},
            </if>
            <if test="staffId != null">
                #{staffId,jdbcType=BIGINT},
            </if>
            <if test="staffProperty != null">
                #{staffProperty,jdbcType=INTEGER},
            </if>
            <if test="staffName != null">
                #{staffName,jdbcType=VARCHAR},
            </if>
            <if test="staffMobile != null">
                HEX(AES_ENCRYPT(#{staffMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="vehicleId != null">
                #{vehicleId,jdbcType=BIGINT},
            </if>
            <if test="vehicleNo != null">
                #{vehicleNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsAmountTotal != null">
                #{goodsAmountTotal,jdbcType=DECIMAL},
            </if>
            <if test="goodsPriceTotal != null">
                #{goodsPriceTotal,jdbcType=DECIMAL},
            </if>
            <if test="publishTime != null">
                #{publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="customerName != null">
                #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="customerUserName != null">
                #{customerUserName,jdbcType=VARCHAR},
            </if>
            <if test="customerUserMobile != null">
                HEX(AES_ENCRYPT(#{customerUserMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="publishUserName != null">
                #{publishUserName,jdbcType=VARCHAR},
            </if>
            <if test="publishUserMobile != null">
                HEX(AES_ENCRYPT(#{publishUserMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="ifAssociatedVehicle != null">
                #{ifAssociatedVehicle,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelectiveEncrypt" parameterType="com.logistics.tms.entity.TDriverAppoint">
        update t_driver_appoint
        <set>
            <if test="demandOrderId != null">
                demand_order_id = #{demandOrderId,jdbcType=BIGINT},
            </if>
            <if test="demandOrderCode != null">
                demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                business_type = #{businessType,jdbcType=INTEGER},
            </if>
            <if test="staffId != null">
                staff_id = #{staffId,jdbcType=BIGINT},
            </if>
            <if test="staffProperty != null">
                staff_property = #{staffProperty,jdbcType=INTEGER},
            </if>
            <if test="staffName != null">
                staff_name = #{staffName,jdbcType=VARCHAR},
            </if>
            <if test="staffMobile != null">
                staff_mobile = HEX(AES_ENCRYPT(#{staffMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="vehicleId != null">
                vehicle_id = #{vehicleId,jdbcType=BIGINT},
            </if>
            <if test="vehicleNo != null">
                vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsAmountTotal != null">
                goods_amount_total = #{goodsAmountTotal,jdbcType=DECIMAL},
            </if>
            <if test="goodsPriceTotal != null">
                goods_price_total = #{goodsPriceTotal,jdbcType=DECIMAL},
            </if>
            <if test="publishTime != null">
                publish_time = #{publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="customerName != null">
                customer_name = #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="customerUserName != null">
                customer_user_name = #{customerUserName,jdbcType=VARCHAR},
            </if>
            <if test="customerUserMobile != null">
                customer_user_mobile = HEX(AES_ENCRYPT(#{customerUserMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="publishUserName != null">
                publish_user_name = #{publishUserName,jdbcType=VARCHAR},
            </if>
            <if test="publishUserMobile != null">
                publish_user_mobile = HEX(AES_ENCRYPT(#{publishUserMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="ifAssociatedVehicle != null">
                if_associated_vehicle = #{ifAssociatedVehicle,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="searchDrierAppointDetail" resultType="com.logistics.tms.controller.driverappoint.response.SearchDrierAppointDetailResponseModel">
        SELECT
            tda.demand_order_id demandOrderId,
            tda.demand_order_code demandOrderCode,
            tda.business_type businessType,
            tda.goods_amount_total goodsAmountTotal,
            tda.goods_price_total goodsPriceTotal,
            tda.publish_time publishTime,
            tda.customer_name customerName,
            tda.customer_user_name customerUserName,
            tda.vehicle_no vehicleNo,
            tda.if_associated_vehicle ifAssociatedVehicle,
            AES_DECRYPT(UNHEX(tda.customer_user_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as customerUserMobile,

            tdaa.load_province_name loadProvinceName,
            tdaa.load_city_name loadCityName,
            tdaa.load_area_name loadAreaName,
            tdaa.load_detail_address loadDetailAddress,
            tdaa.load_warehouse loadWarehouse,
            tdaa.consignor_name consignorName,
            AES_DECRYPT(UNHEX(tdaa.consignor_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as consignorMobile
        FROM
            t_driver_appoint tda
            LEFT JOIN t_driver_appoint_address tdaa ON tda.id = tdaa.driver_appoint_id and tdaa.valid = 1
        where tda.valid = 1 and tda.id = #{requestModel.driverAppointId,jdbcType=BIGINT}
    </select>

    <select id="queryDriverAppointList" resultType="com.logistics.tms.controller.driverappoint.request.SearchDriverAppointRequestModel"
    resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_driver_appoint
        where valid = 1
            <if test="requestModel.driverAppointIds!=null and requestModel.driverAppointIds!=''">
                and id in (${requestModel.driverAppointIds})
            </if>
            <if test="requestModel.demandOrderCode != null and requestModel.demandOrderCode != ''">
                and instr(demand_order_code,#{requestModel.demandOrderCode,jdbcType=VARCHAR}) > 0
            </if>
            <if test="requestModel.driver != null and requestModel.driver != ''">
                and (instr(staff_name,#{requestModel.driver,jdbcType = VARCHAR}) > 0
                or instr(AES_DECRYPT(UNHEX(staff_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'),#{requestModel.driver,jdbcType = VARCHAR}) > 0)
            </if>
            <if test="requestModel.vehicleNo != null and requestModel.vehicleNo != ''">
                and instr(vehicle_no,#{requestModel.vehicleNo,jdbcType = VARCHAR}) > 0
            </if>
            <if test="requestModel.publishTimeFrom!=null and requestModel.publishTimeFrom!=''">
                and publish_time &gt;= DATE_FORMAT(#{requestModel.publishTimeFrom},'%Y-%m-%d
                %H:%i:%S')
            </if>
            <if test="requestModel.publishTimeTo != null and requestModel.publishTimeTo != ''">
                and publish_time &lt;= DATE_FORMAT(#{requestModel.publishTimeTo},'%Y-%m-%d
                23:59:59')
            </if>
            <if test="requestModel.customerName != null and requestModel.customerName != ''">
                and (case business_type
                         when 1 then instr(customer_name, #{requestModel.customerName,jdbcType=VARCHAR})
                         when 2 then instr(customer_user_name, #{requestModel.customerName,jdbcType=VARCHAR})
                             or instr(AES_DECRYPT(UNHEX(customer_user_mobile),
                                                  '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'), #{requestModel.customerName,jdbcType=VARCHAR})
                    end)
            </if>
        order by created_time desc, id desc
    </select>
    <select id="searchAppointList" resultType="com.logistics.tms.controller.driverappoint.response.SearchAppointResponseModel">
        SELECT
            id driverAppointId,
            demand_order_code demandOrderCode,
            customer_name customerName,
            goods_amount_total goodsAmountTotal,
            publish_time publishTime,
            customer_user_name customerUserName,
            business_type businessType,
            AES_DECRYPT(UNHEX(customer_user_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as customerUserMobile
        FROM
            t_driver_appoint
        WHERE
        valid = 1
          AND date_format(publish_time,'%Y-%m') = #{requestModel.appointDate,jdbcType=VARCHAR}
          and if_associated_vehicle = #{requestModel.associatedVehicleStatus,jdbcType=INTEGER}
          and staff_id = #{staffId,jdbcType=BIGINT}
        order by publish_time desc,id desc
    </select>
    <select id="searchAppointCount" resultType="com.logistics.tms.controller.driverappoint.response.SearchAppointCountResponseModel">
        SELECT
            ifnull(count(*), 0) AS appointCount,
            ifnull(sum(ifnull(goods_amount_total,0)), 0) AS appointAmountTotal
        FROM
            t_driver_appoint
        WHERE
            valid = 1
          AND date_format(publish_time,'%Y-%m') = #{requestModel.appointDate,jdbcType=VARCHAR}
          and staff_id = #{staffId,jdbcType=BIGINT}
    </select>
</mapper>