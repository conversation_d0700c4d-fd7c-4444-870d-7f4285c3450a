package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 新生推送物流生成需求单实体
 * @author: wjf
 * @date: 2024/6/18 14:04
 */
@Data
public class SyncLifeDemandOrderMessage {

    //基本信息
    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("客户编号（回收申请单号）")
    private String customerOrderCode;

    @ApiModelProperty("业务类型：1 公司，2 个人")
    private Integer businessType;

    @ApiModelProperty("乐橘新生客户名称（企业）")
    private String customerName;

    @ApiModelProperty("乐橘新生客户姓名（个人）")
    private String customerUserName;

    @ApiModelProperty("乐橘新生客户手机号（个人）")
    private String customerUserMobile;

    @ApiModelProperty("下单人")
    private String publishUserName;

    @ApiModelProperty("下单人手机号")
    private String publishUserMobile;

    @ApiModelProperty("下单时间")
    private Date publishTime;

    @ApiModelProperty("单位：1 件，2 吨，3 件（托盘），4 块")
    private Integer goodsUnit;

    @ApiModelProperty("委托类型：100 新生回收，101 新生销售")
    private Integer entrustType;

    @ApiModelProperty("备注")
    private String remark;


    //地址信息
    private SyncLifeDemandOrderAddressModel addressModel;


    //货物
    @ApiModelProperty("货物")
    private List<SyncLifeDemandOrderGoodsModel> goodsModels;


    @ApiModelProperty("推送物流操作人")
    private String operatorName;

    @ApiModelProperty("需按编码回收 0:否 1：是")
    private Integer ifRecycleByCode;


}
