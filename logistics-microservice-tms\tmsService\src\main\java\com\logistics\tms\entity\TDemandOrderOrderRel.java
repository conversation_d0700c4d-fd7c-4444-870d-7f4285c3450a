package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TDemandOrderOrderRel extends BaseEntity {
    private Long demandOrderId;

    /**
    * 销售单/补货单订单ID
    */
    @ApiModelProperty("销售单/补货单订单ID")
    private Long orderId;

    /**
    * 销售单/补货单号
    */
    @ApiModelProperty("销售单/补货单号")
    private String orderCode;

    /**
    * S单的数量
    */
    @ApiModelProperty("S单的数量")
    private BigDecimal totalAmount;

    /**
    * 已安排数量
    */
    @ApiModelProperty("已安排数量")
    private BigDecimal arrangedAmount;

    /**
    * 退回数量
    */
    @ApiModelProperty("退回数量")
    private BigDecimal backAmount;

    /**
    * 类型 1 下单   2 追加   3 补单
    */
    @ApiModelProperty("类型 1 下单   2 追加   3 补单")
    private Integer relType;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}