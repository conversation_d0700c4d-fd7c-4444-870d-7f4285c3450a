package com.logistics.tms.api.feign.loanrecord;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.loanrecord.hystrix.LoanRecordServiceApiHystrix;
import com.logistics.tms.api.feign.loanrecord.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/9/30 10:06
 */
@Api(value = "API-LoanRecordServiceApi-贷款记录")
@FeignClient(name = "logistics-tms-services", fallback = LoanRecordServiceApiHystrix.class)
public interface LoanRecordServiceApi {

    @ApiOperation("查询贷款记录列表")
    @PostMapping(value = "/service/loanRecord/searchList")
    Result<PageInfo<LoanRecordListResponseModel>> searchList(@RequestBody LoanRecordListRequestModel requestModel);

    @ApiOperation("获取列表汇总数据")
    @PostMapping(value = "/service/loanRecord/getSummary")
    Result<SummaryLoanRecordResponseModel> getSummary(@RequestBody LoanRecordListRequestModel requestModel);

    @ApiOperation("新增/修改")
    @PostMapping(value = "/service/loanRecord/saveOrUpdate")
    Result<Boolean> saveOrUpdate(@RequestBody SaveOrUpdateLoanRecordRequestModel recordRequestModel);

    @ApiOperation("详情")
    @PostMapping(value = "/service/loanRecord/getDetail")
    Result<LoanRecordDetailResponseModel> getDetail(@RequestBody LoanRecordDetailRequestModel requestModel);

    @ApiOperation("查询操作记录")
    @PostMapping(value = "/service/loanRecord/getOperationRecords")
    Result<List<LoanOperationRecordResponseModel>> getOperationRecords(@RequestBody LoanOperationRecordRequestModel requestModel);

    @ApiOperation("查询结算记录")
    @PostMapping(value = "/service/loanRecord/getSettlementRecords")
    Result<List<LoanSettlementRecordResponseModel>> getSettlementRecords(@RequestBody LoanSettlementRecordRequestModel requestModel);

    @ApiOperation(value = "导出贷款记录")
    @PostMapping(value = "/service/loanRecord/exportLoanRecords")
    Result<List<LoanRecordListResponseModel>> exportLoanRecords(@RequestBody LoanRecordListRequestModel requestModel);

    @ApiOperation(value = "导出结算记录")
    @PostMapping(value = "/service/loanRecord/exportSettlementRecords")
    Result<ExportSettlementRecordResponseModel> exportSettlementRecords(@RequestBody LoanSettlementRecordRequestModel requestModel);

    @ApiOperation(value = "导出操作记录")
    @PostMapping(value = "/service/loanRecord/exportOperationRecords")
    Result<ExportOperationRecordResponseModel> exportOperationRecords(@RequestBody LoanOperationRecordRequestModel requestModel);

}
