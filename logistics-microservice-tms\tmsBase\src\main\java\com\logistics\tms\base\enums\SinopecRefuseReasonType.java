package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum SinopecRefuseReasonType {

    CARRIER_REASONS(1, "承运商原因"),
    SHIPPER_REASONS(2, "托运人原因"),
    LOADER_REASONS(3, "装货人原因"),
    GOVERNMENT_POLICY_REASONS(4, "政府政策原因"),
    FORCE_MAJEURE_CAUSES(5, "不可抗力原因"),
    ;

    private Integer key;
    private String value;

    public static String getValueByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .map(SinopecRefuseReasonType::getValue)
                .findFirst()
                .orElse("");
    }
}
