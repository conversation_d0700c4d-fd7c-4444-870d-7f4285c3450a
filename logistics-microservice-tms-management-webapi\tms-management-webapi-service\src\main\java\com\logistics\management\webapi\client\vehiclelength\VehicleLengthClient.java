package com.logistics.management.webapi.client.vehiclelength;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.vehiclelength.hystrix.VehicleLengthClientHystrix;
import com.logistics.management.webapi.client.vehiclelength.request.AddOrUpdateVehicleLengthRequestModel;
import com.logistics.management.webapi.client.vehiclelength.request.SearchVehicleLengthListRequestModel;
import com.logistics.management.webapi.client.vehiclelength.request.VehicleLengthDetailRequestModel;
import com.logistics.management.webapi.client.vehiclelength.response.SearchVehicleLengthListResponseModel;
import com.logistics.management.webapi.client.vehiclelength.response.SelectVehicleLengthListResponseModel;
import com.logistics.management.webapi.client.vehiclelength.response.VehicleLengthDetailResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/4/29 9:12
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/vehicleLength",
        fallback = VehicleLengthClientHystrix.class)
public interface VehicleLengthClient {

    /**
     * 分页搜索车长配置
     * @param requestModel 请求参数
     * @return 车长配置
     */
    @PostMapping(value = "/searchVehicleLengthList")
    @ApiOperation(value = "分页搜索车长配置")
    Result<PageInfo<SearchVehicleLengthListResponseModel>> searchVehicleLengthList(@RequestBody SearchVehicleLengthListRequestModel requestModel);


    /**
     * 车长配置详情
     * @param requestModel 请求参数
     * @return 车长配置详情
     */
    @PostMapping(value = "/vehicleLengthDetail")
    @ApiOperation(value = "车长配置详情")
    Result<VehicleLengthDetailResponseModel> vehicleLengthDetail(@RequestBody VehicleLengthDetailRequestModel requestModel);

    /**
     * 新增编辑车长
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/addOrUpdateVehicleLength")
    @ApiOperation(value = "新增编辑车长")
    Result<Boolean> addOrUpdateVehicleLength(@RequestBody AddOrUpdateVehicleLengthRequestModel requestModel);

    /**
     * 删除车长
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/delVehicleLength")
    @ApiOperation(value = "删除车长")
    Result<Boolean> delVehicleLength(@RequestBody VehicleLengthDetailRequestModel requestModel);

    /**
     * 下拉选择车长
     * @return 下拉选择车长
     */
    @PostMapping(value = "/selectVehicleLengthList")
    @ApiOperation(value = "下拉选择车长")
    Result<List<SelectVehicleLengthListResponseModel>> selectVehicleLengthList();
    
}
