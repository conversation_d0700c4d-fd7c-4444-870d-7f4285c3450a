package com.logistics.management.webapi.controller.companyentrust;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.client.companyentrust.CompanyEntrustClient;
import com.logistics.management.webapi.client.companyentrust.request.AddOrModifyCompanyEntrustRequestModel;
import com.logistics.management.webapi.client.companyentrust.request.CompanyEntrustIdRequestModel;
import com.logistics.management.webapi.client.companyentrust.request.SearchCompanyEntrustByNameRequestModel;
import com.logistics.management.webapi.client.companyentrust.request.SearchCompanyEntrustRequestModel;
import com.logistics.management.webapi.client.companyentrust.response.CompanyEntrustDetailResponseModel;
import com.logistics.management.webapi.client.companyentrust.response.SearchCompanyEntrustByNameResponseModel;
import com.logistics.management.webapi.client.companyentrust.response.SearchCompanyEntrustResponseModel;
import com.logistics.management.webapi.controller.companyentrust.mapping.CompanyEntrustDetailMapping;
import com.logistics.management.webapi.controller.companyentrust.mapping.ListCompanyEntrustMapping;
import com.logistics.management.webapi.controller.companyentrust.request.AddOrModifyCompanyEntrustRequestDto;
import com.logistics.management.webapi.controller.companyentrust.request.CompanyEntrustIdRequestDto;
import com.logistics.management.webapi.controller.companyentrust.request.SearchCompanyEntrustByNameRequestDto;
import com.logistics.management.webapi.controller.companyentrust.request.SearchCompanyEntrustRequestDto;
import com.logistics.management.webapi.controller.companyentrust.response.CompanyEntrustDetailResponseDto;
import com.logistics.management.webapi.controller.companyentrust.response.SearchCompanyEntrustByNameResponseDto;
import com.logistics.management.webapi.controller.companyentrust.response.SearchCompanyEntrustResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/9/19 14:15
 */
@Api(value = "API-CompanyEntrustApi-委托方管理")
@RestController
public class CompanyEntrustController{

    @Resource
    private CompanyEntrustClient companyEntrustClient;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 获取委托方公司列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "获取委托方公司列表")
    @PostMapping(value = "/api/companyEntrust/searchList")
    public Result<PageInfo<SearchCompanyEntrustResponseDto>> searchList(@RequestBody SearchCompanyEntrustRequestDto requestDto) {
        Result<PageInfo<SearchCompanyEntrustResponseModel>> pageInfoResult = companyEntrustClient.searchList(MapperUtils.mapper(requestDto, SearchCompanyEntrustRequestModel.class));
        if (!pageInfoResult.isSuccess()) {
            throw new BizException(pageInfoResult.getErrcode(), pageInfoResult.getErrmsg());
        }
        PageInfo data = pageInfoResult.getData();
        if (pageInfoResult.isSuccess()) {
            List<SearchCompanyEntrustResponseDto> dtoList = MapperUtils.mapper(data.getList(), SearchCompanyEntrustResponseDto.class, new ListCompanyEntrustMapping());
            data.setList(dtoList);
        }
        return Result.success(data);
    }

    /**
     * 查看详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "查看详情")
    @PostMapping(value = "/api/companyEntrust/getDetail")
    public Result<CompanyEntrustDetailResponseDto> getDetail(@RequestBody @Valid CompanyEntrustIdRequestDto requestDto) {
        Result<CompanyEntrustDetailResponseModel> detail = companyEntrustClient.getDetail(MapperUtils.mapper(requestDto, CompanyEntrustIdRequestModel.class));
        if(!detail.isSuccess()){
            throw new BizException(detail.getErrcode(),detail.getErrmsg());
        }
        Map<String, String> imageMap = new HashMap<>();
        imageMap.put(detail.getData().getTradingCertificateImage(), commonBiz.getImageURL(detail.getData().getTradingCertificateImage()));
        return Result.success(MapperUtils.mapper(detail.getData(),CompanyEntrustDetailResponseDto.class,new CompanyEntrustDetailMapping(configKeyConstant.fileAccessAddress,imageMap)));
    }

    /**
     * 添加/修改公司
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "添加/修改公司")
    @PostMapping(value = "/api/companyEntrust/saveCompany")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result saveCompany(@RequestBody @Valid AddOrModifyCompanyEntrustRequestDto requestDto) {
        if (CommonConstant.ONE.equals(requestDto.getTradingCertificateIsAmend())){
            requestDto.setFileSrcPathTradingCertificateImage("");
            requestDto.setTradingCertificateIsForever(null);
            requestDto.setTradingCertificateValidityTime(null);
        }else{
            if (StringUtils.isBlank(requestDto.getFileSrcPathTradingCertificateImage())){
                throw new BizException(ManagementWebApiExceptionEnum.COMPANY_CARRIER_TRADING_AMEND);
            }
            if (StringUtils.isBlank(requestDto.getTradingCertificateIsForever()) && StringUtils.isBlank(requestDto.getTradingCertificateValidityTime())){
                throw new BizException(ManagementWebApiExceptionEnum.COMPANY_CARRIER_TRADING_AMEND_DATE);
            }
        }
        Result result = companyEntrustClient.saveCompany(MapperUtils.mapperNoDefault(requestDto, AddOrModifyCompanyEntrustRequestModel.class));
        if(!result.isSuccess()){
            throw new BizException(result.getErrcode(),result.getErrmsg());
        }
        return Result.success(true);
    }

    /**
     * 根据公司名称模糊查询委托方公司
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "根据公司名称模糊查询委托方公司")
    @PostMapping(value = "/api/companyEntrust/getCompanyEntrustByName")
    public Result<List<SearchCompanyEntrustByNameResponseDto>> searchCompanyEntrustByName(@RequestBody SearchCompanyEntrustByNameRequestDto requestDto) {
        Result<List<SearchCompanyEntrustByNameResponseModel>> result = companyEntrustClient.searchCompanyEntrustByName(MapperUtils.mapper(requestDto, SearchCompanyEntrustByNameRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),SearchCompanyEntrustByNameResponseDto.class));
    }
}
