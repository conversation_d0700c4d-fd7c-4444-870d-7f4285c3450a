package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@AllArgsConstructor
@Getter
public enum CompanyCarrierRangeEnum {
    //车主范围：1 全部，2 定向选择
    DEFAULT(-99999, ""),
    ALL(1, "全部"),
    DIRECTIONAL(2, "定向选择"),
    ;

    private final Integer key;
    private final String value;

    public static CompanyCarrierRangeEnum getEnum(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
