package com.logistics.management.webapi.controller.biddingorder.mapping;

import com.logistics.management.webapi.client.biddingorder.response.SearchBiddingDemandResponseModel;
import com.logistics.management.webapi.controller.biddingorder.response.SearchBiddingDemandResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2021/9/18 16:59
 */
public class SearchBiddingDemandMapping extends MapperMapping<SearchBiddingDemandResponseModel, SearchBiddingDemandResponseDto> {



    @Override
    public void configure() {
        SearchBiddingDemandResponseModel source = getSource();
        SearchBiddingDemandResponseDto destination = getDestination();
        if(source != null && source.getGoodsCount() != null){
            destination.setGoodsCount(source.getGoodsCount().stripTrailingZeros().toPlainString());
        }

    }
}
