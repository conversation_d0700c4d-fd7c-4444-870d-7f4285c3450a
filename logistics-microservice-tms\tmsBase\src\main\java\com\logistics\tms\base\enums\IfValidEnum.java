package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2018/9/29 10:46
 */
public enum IfValidEnum {
    VALID(1, "有效"),
    INVALID(0, "无效"),;

    private Integer key;
    private String value;

    IfValidEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
