package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2023/08/02
*/
@Data
public class TDriverCostApply extends BaseEntity {
    /**
    * 费用申请单号
    */
    @ApiModelProperty("费用申请单号")
    private String costApplyCode;

    /**
    * 司机id
    */
    @ApiModelProperty("司机id")
    private Long staffId;

    /**
    * 司机姓名
    */
    @ApiModelProperty("司机姓名")
    private String staffName;

    /**
    * 司机手机号（原长度50）
    */
    @ApiModelProperty("司机手机号（原长度50）")
    private String staffMobile;

    /**
    * 人员机构：1 自主，2 外部，3 自营
    */
    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    /**
    * 车辆id
    */
    @ApiModelProperty("车辆id")
    private Long vehicleId;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 费用类型：100 住宿费，101 装卸费，102 其他费用，103 加班费，104 劳保费，105 电话费，106 交通费， 107 打印费，108 叉车费，109 盖雨布， 110破包赔偿，111 交通罚款；200 维修费，201 车辆保养费，202 过路过桥费，203 停车费，204 尿素费，205 加油费；300 核酸检测费，301 医疗防护用品；400 扣款
    */
    @ApiModelProperty("费用类型：100 住宿费，101 装卸费，102 其他费用，103 加班费，104 劳保费，105 电话费，106 交通费， 107 打印费，108 叉车费，109 盖雨布， 110破包赔偿，111 交通罚款；200 维修费，201 车辆保养费，202 过路过桥费，203 停车费，204 尿素费，205 加油费；300 核酸检测费，301 医疗防护用品；400 扣款")
    private Integer costType;

    /**
    * 申请费用
    */
    @ApiModelProperty("申请费用")
    private BigDecimal applyCost;

    /**
    * 发生时间
    */
    @ApiModelProperty("发生时间")
    private Date occurrenceTime;

    /**
    * 申请时间
    */
    @ApiModelProperty("申请时间")
    private Date applyTime;

    /**
    * 申请说明
    */
    @ApiModelProperty("申请说明")
    private String applyRemark;

    /**
    * 关联油卡
    */
    @ApiModelProperty("关联油卡")
    private String associatedOilCard;

    /**
    * 审核状态：-1 待业务审核，0 待财务审核，1 已审核，2 已驳回，3 已撤销，4 已红冲
    */
    @ApiModelProperty("审核状态：-1 待业务审核，0 待财务审核，1 已审核，2 已驳回，3 已撤销，4 已红冲")
    private Integer auditStatus;

    /**
    * 审核人
    */
    @ApiModelProperty("审核人")
    private String auditorName;

    /**
    * 审核时间
    */
    @ApiModelProperty("审核时间")
    private Date auditTime;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}