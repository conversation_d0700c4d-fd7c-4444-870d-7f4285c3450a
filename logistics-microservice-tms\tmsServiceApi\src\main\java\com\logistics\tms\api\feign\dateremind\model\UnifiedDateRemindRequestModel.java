package com.logistics.tms.api.feign.dateremind.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * @Author: sj
 * @Date: 2019/5/31 8:59
 */
@Data
public class UnifiedDateRemindRequestModel {
    @ApiModelProperty("日期提醒ID列表")
    private List<Long> dateRemindIds;
    @ApiModelProperty("是否提醒：0 否，1 是")
    private Integer ifRemind;
    @ApiModelProperty("提醒天数")
    private Integer remindDays;
    @ApiModelProperty("备注")
    private String remark;
}
