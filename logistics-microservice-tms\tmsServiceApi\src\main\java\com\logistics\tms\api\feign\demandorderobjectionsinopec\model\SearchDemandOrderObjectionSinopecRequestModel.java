package com.logistics.tms.api.feign.demandorderobjectionsinopec.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/5/30 13:15
 */
@Data
public class SearchDemandOrderObjectionSinopecRequestModel extends AbstractPageForm<SearchDemandOrderObjectionSinopecRequestModel> {
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("异常类型：1 已报价，2 已取消")
    private Integer auditObjectionType;
    @ApiModelProperty("审核时间开始")
    private String auditTimeStart;
    @ApiModelProperty("审核时间结束")
    private String auditTimeEnd;
}
