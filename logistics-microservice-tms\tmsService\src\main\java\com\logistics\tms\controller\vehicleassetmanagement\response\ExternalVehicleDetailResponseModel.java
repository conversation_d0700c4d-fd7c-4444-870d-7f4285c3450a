package com.logistics.tms.controller.vehicleassetmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/10/18 19:08
 */
@Data
public class ExternalVehicleDetailResponseModel {
    @ApiModelProperty("车主车辆关联Id")
    private Long carrierVehicleId;

    //车主信息
    @ApiModelProperty("车主id")
    private Long companyCarrierId;
    @ApiModelProperty(value = "车主名称")
    private String companyCarrierName;
    @ApiModelProperty(value = "车主公司类型：1 公司，2 个人")
    private Integer companyCarrierType;
    @ApiModelProperty("车主账号名称")
    private String carrierContactName;
    @ApiModelProperty("车主账号手机号（原长度50）")
    private String carrierContactPhone;

    @ApiModelProperty("车辆资产基础信息ID")
    private Long vehicleBasicId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("车辆类型")
    private Long vehicleType;
    @ApiModelProperty("车辆类型名称")
    private String vehicleTypeLabel;

    @ApiModelProperty("装载量（可装载托盘数）")
    private Integer loadingCapacity;
}
