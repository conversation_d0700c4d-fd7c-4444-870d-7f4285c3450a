package com.logistics.tms.controller.freightconfig;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.shippingfreight.ShippingFreightAddressBiz;
import com.logistics.tms.biz.shippingfreight.ShippingFreightRuleCrossPointBiz;
import com.logistics.tms.biz.shippingfreight.ShippingFreightRuleVehicleLengthBiz;
import com.logistics.tms.controller.freightconfig.request.ConfigStringPointReqModel;
import com.logistics.tms.controller.freightconfig.request.ConfigVehicleReqModel;
import com.logistics.tms.controller.freightconfig.request.EnableOrForbidOrDeleteReqModel;
import com.logistics.tms.controller.freightconfig.request.shipping.AddShippingFreightRuleReqModel;
import com.logistics.tms.controller.freightconfig.request.shipping.ListShippingFreightRuleConfigReqModel;
import com.logistics.tms.controller.freightconfig.request.ShippingFreightRuleIdReqModel;
import com.logistics.tms.controller.freightconfig.response.GetConfigVechicleRespModel;
import com.logistics.tms.controller.freightconfig.response.shipping.ListShippingFreightRuleListRespModel;
import com.logistics.tms.entity.TShippingFreightRuleCrossPoint;
import com.yelo.tray.core.base.Result;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.RoundingMode;
import java.util.List;


/**
 * 配置运价规则
 */
@RestController
@RequestMapping(value = "/service/freight/rule")
public class ShippingFreightRuleConfigController {

    @Resource
    ShippingFreightRuleVehicleLengthBiz shippingFreightRuleVehicleLengthBiz;
    @Resource
    ShippingFreightRuleCrossPointBiz shippingFreightRuleCrossPointBiz;
    @Resource
    ShippingFreightAddressBiz shippingFreightAddressBiz;

    /**
     * 配置运价规则列表  v2.42
     */
    @PostMapping(value = "/getList")
    public Result<PageInfo<ListShippingFreightRuleListRespModel>> getList(@RequestBody ListShippingFreightRuleConfigReqModel reqModel) {
        reqModel.setIfExport(CommonConstant.INTEGER_ZERO);
        return Result.success(shippingFreightAddressBiz.getList(reqModel));
    }


    /**
     * 配置运价规则列表导出 v2.42
     */
    @PostMapping(value = "/exportList")
    Result<List<ListShippingFreightRuleListRespModel>> exportList(@RequestBody ListShippingFreightRuleConfigReqModel reqModel) {
        reqModel.setIfExport(CommonConstant.INTEGER_ONE);
        return Result.success(shippingFreightAddressBiz.getList(reqModel).getList());
    }

    /**
     * 新增配置运价规则  v2.42
     */
    @PostMapping(value = "/add")
    public Result<Boolean> add(@RequestBody AddShippingFreightRuleReqModel reqModel) {
        shippingFreightAddressBiz.add(reqModel);
        return Result.success(true);
    }

    /**
     * 零担运价管理启用禁用 v2.42
     */
    @PostMapping(value = "/enableOrForbidOrDelete")
    public Result<Boolean> enableOrForbid(@RequestBody @Valid EnableOrForbidOrDeleteReqModel reqModel) {
        shippingFreightRuleVehicleLengthBiz.enableOrForbid(reqModel);
        return Result.success(true);
    }

    /**
     * 配置车长运价/运价编辑 v2.42
     */
    @PostMapping(value = "/configVehicle")
    public Result<Boolean> configVehicle(@RequestBody @Valid List<ConfigVehicleReqModel> configVehicleReqModels) {
        //车长 vehicleLength 设置2位小数  方便后续和数据库中做匹配
        configVehicleReqModels.forEach(configVehicleReqModel -> configVehicleReqModel.setVehicleLength(configVehicleReqModel.getVehicleLength().setScale(2, RoundingMode.HALF_UP)));
        shippingFreightRuleVehicleLengthBiz.configVehicles(configVehicleReqModels);
        return Result.success(true);
    }

    /**
     * 配置车长运价/运价编辑 v2.42
     */
    @PostMapping(value = "/editVehicle")
    public Result<Boolean> editVehicle(@RequestBody @Valid List<ConfigVehicleReqModel> configVehicleReqModels) {
        //车长 vehicleLength 设置2位小数  方便后续和数据库中做匹配
        configVehicleReqModels.forEach(configVehicleReqModel -> configVehicleReqModel.setVehicleLength(configVehicleReqModel.getVehicleLength().setScale(2, RoundingMode.HALF_UP)));
        shippingFreightRuleVehicleLengthBiz.editVehicle(configVehicleReqModels);
        return Result.success(true);
    }

    /**
     * 车长查看 v2.42
     */
    @PostMapping(value = "/getConfigVehicle")
    public Result<GetConfigVechicleRespModel> getConfigVehicle(@RequestBody @Valid ShippingFreightRuleIdReqModel reqModel) {
        return Result.success(shippingFreightRuleVehicleLengthBiz.getConfigVehicle(reqModel));
    }


    /**
     * 配置串点运价 v2.42
     */
    @PostMapping(value = "/configStringPoint")
    public Result<Boolean> configStringPoint(@RequestBody @Valid List<ConfigStringPointReqModel> configStringPointReqModels) {
        shippingFreightRuleCrossPointBiz.configStringPoint(configStringPointReqModels);
        return Result.success(true);
    }








}
