package com.logistics.tms.client.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2023/6/20 11:12
 */
@Data
public class RecyclePublishUpdateDemandRequestModel {
    @ApiModelProperty("配置编码")
    private String configCode;
    @ApiModelProperty("需求单号")
    private String demandOrderNo;
    @ApiModelProperty("委托量")
    private BigDecimal entrustNum;
    @ApiModelProperty("回退量")
    private BigDecimal backspaceNum;
    @ApiModelProperty("委托发布时间")
    private Date entrustPublishTime;
}
