package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2021/9/24 17:47
 */
@Data
public class BatchPublishDetailResponseDto {
    @ApiModelProperty("需求单id")
    private String demandId="";
    @ApiModelProperty("需求单号")
    private String demandOrderCode="";

    /**
     * (3.22.0)发货省份id
     */
    @ApiModelProperty("发货省份id")
    private String loadProvinceId="";
    /**
     * (3.22.0)发货城市id
     */
    @ApiModelProperty("发货城市id")
    private String loadCityId="";
    @ApiModelProperty("发货地址")
    private String loadDetailAddress="";

    @ApiModelProperty("收货地址")
    private String unloadDetailAddress="";

    @ApiModelProperty("委托数量")
    private String goodsAmount="";
    @ApiModelProperty("货物单位：1 件，2 吨，3 方，4 块")
    private String goodsUnit="";
    @ApiModelProperty("货主公司名称")
    private String companyEntrustName;
}
