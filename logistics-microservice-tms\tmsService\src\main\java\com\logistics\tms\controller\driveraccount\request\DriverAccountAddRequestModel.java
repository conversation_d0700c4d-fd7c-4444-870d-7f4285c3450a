package com.logistics.tms.controller.driveraccount.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/27
 */
@Data
public class DriverAccountAddRequestModel {

	@ApiModelProperty(value = "司机账户ID,编辑时必填")
	private Long driverAccountId;

	@ApiModelProperty(value = "司机id")
	private Long driverId;

	@ApiModelProperty(value = "银行账号,9-30位")
	private String bankAccount;

	@ApiModelProperty(value = "开户银行名称,4-20位")
	private String bankAccountName;

	@ApiModelProperty(value = "开户支行名称, 非必填 填写时检验长度0-20")
	private String braBankName;

	@ApiModelProperty(value = "行号,4-20位数字")
	private String bankCode;

	@ApiModelProperty(value = "收款证件图片")
	private List<String> bankAccountImage;
}
