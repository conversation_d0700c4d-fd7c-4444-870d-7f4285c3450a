package com.logistics.appapi.base.enums;

/**
 * @author: wjf
 * @date: 2023/5/5 10:50
 */
public enum QrCodeParamsEnum {

    //此枚举如果更改，则云仓也需更改（云仓生成的二维码物流司机会扫）
    CARRIER_ORDER_CODE("c", "运单号"),
    CARRIER_ORDER_BILL_TYPE("t", "票据类型"),
    ;

    private String key;
    private String remark;

    QrCodeParamsEnum(String key, String remark) {
        this.key = key;
        this.remark = remark;
    }

    public String getKey() {
        return key;
    }

    public String getRemark() {
        return remark;
    }

}
