package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DemandOrderCarrierResponseModel {
    private Long carrierOrderId;
    private Long demandOrderId;
    private String carrierOrderCode;
    private Integer status;
    private Integer ifCancel;
    @ApiModelProperty("是否放空 0 否 1 是")
    private Integer ifEmpty;
    private String vehicleNumber;
    private Long driverId;
    private String driverName;
    private String driverMobile;
    private BigDecimal dispatchUnitPrice;
    private String dispatchUserName;
    private Date dispatchTime;
    private Integer goodsUnit;
    private BigDecimal expectAmount;
    private BigDecimal loadAmount;
    private BigDecimal unloadAmount;
    private BigDecimal signAmount;
    private Integer entrustFreightType;
    private BigDecimal entrustFreight;
    private BigDecimal signFreightFee;
    private List<DemandCarrierOrderListGoodsInfoModel> goodsInfoList;
    private List<DemandOrderTicketModel> tickets;
    @ApiModelProperty("运单下是否有待审核的车辆：0 否，1 是")
    private Integer ifWaitAudit;


    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;
    private String receiverName;
    private String receiverMobile;
    @ApiModelProperty("收货地址是否后补 0否 1是")
    private Integer unloadAddressIsAmend;

    @ApiModelProperty("司机运费价格类型 1 单价 2 一口价")
    private Integer dispatchFreightFeeType;
    @ApiModelProperty("司机运费")
    private BigDecimal dispatchFreightFee;
    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;
    @ApiModelProperty("多装多卸加价费用")
    private BigDecimal markupFee;
}
