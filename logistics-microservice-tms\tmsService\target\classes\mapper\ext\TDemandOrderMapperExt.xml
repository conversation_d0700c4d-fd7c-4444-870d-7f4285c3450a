<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandOrderMapper">
    <sql id="Base_Column_List_Decrypt">
    id, entrust_status, status_update_time, status, if_cancel, cancel_type, cancel_time,
    cancel_reason, source, demand_order_code, business_type, customer_name, customer_user_name,
    AES_DECRYPT(UNHEX(customer_user_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as customer_user_mobile,
    customer_order_source, customer_order_code, publish_name,
    AES_DECRYPT(UNHEX(publish_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as publish_mobile,
    publish_time, publish_org_code, publish_org_name, ticket_time, settlement_tonnage, carrier_settlement, goods_amount, arranged_amount,
    not_arranged_amount, back_amount, difference_amount, abnormal_amount, expect_contract_price,
    expect_contract_price_type, contract_price_type, contract_price, goods_unit, company_entrust_id,
    company_entrust_name, upstream_customer, company_carrier_type, company_carrier_id,
    company_carrier_name, carrier_contact_id, carrier_contact_name,
    AES_DECRYPT(UNHEX(carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrier_contact_phone,
    company_carrier_level, dispatch_vehicle_count, entrust_type, remark, carrier_price_type, carrier_price,
    if_urgent, dispatch_validity, if_overdue, if_empty, if_rollback, rollback_cause_type, rollback_cause_type_two, rollback_remark, available_on_weekends, loading_unloading_part, loading_unloading_charge,
    recycle_task_type, ground_push_task_code, project_label, order_type, sinopec_order_no, manufacturer_name, item_trans_group_name, item_pack_spec_name, dispatcher_name, dispatcher_phone,
    if_objection, if_objection_sinopec, sinopec_customer_id, sinopec_customer_name, sinopec_online_goods_flag,lbs_code,fixed_demand, auto_publish,
    order_mode, vehicle_length_id, vehicle_length, created_by, created_time, last_modified_by, last_modified_time,
    valid, if_ext_demand_order
    </sql>

    <select id="selectByPrimaryKeyDecrypt" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt" />
        from t_demand_order
        where id = #{id,jdbcType=BIGINT}
        and valid = 1
    </select>

    <insert id="insertSelectiveEncrypt" parameterType="com.logistics.tms.entity.TDemandOrder" keyProperty="id" useGeneratedKeys="true">
        insert into t_demand_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="entrustStatus != null">
                entrust_status,
            </if>
            <if test="statusUpdateTime != null">
                status_update_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="ifCancel != null">
                if_cancel,
            </if>
            <if test="cancelType != null">
                cancel_type,
            </if>
            <if test="cancelTime != null">
                cancel_time,
            </if>
            <if test="cancelReason != null">
                cancel_reason,
            </if>
            <if test="source != null">
                source,
            </if>
            <if test="demandOrderCode != null">
                demand_order_code,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="customerName != null">
                customer_name,
            </if>
            <if test="customerUserName != null">
                customer_user_name,
            </if>
            <if test="customerUserMobile != null">
                customer_user_mobile,
            </if>
            <if test="customerOrderSource != null">
                customer_order_source,
            </if>
            <if test="customerOrderCode != null">
                customer_order_code,
            </if>
            <if test="publishName != null">
                publish_name,
            </if>
            <if test="publishMobile != null">
                publish_mobile,
            </if>
            <if test="publishTime != null">
                publish_time,
            </if>
            <if test="publishOrgCode != null">
                publish_org_code,
            </if>
            <if test="publishOrgName != null">
                publish_org_name,
            </if>
            <if test="ticketTime != null">
                ticket_time,
            </if>
            <if test="settlementTonnage != null">
                settlement_tonnage,
            </if>
            <if test="carrierSettlement != null">
                carrier_settlement,
            </if>
            <if test="goodsAmount != null">
                goods_amount,
            </if>
            <if test="arrangedAmount != null">
                arranged_amount,
            </if>
            <if test="notArrangedAmount != null">
                not_arranged_amount,
            </if>
            <if test="backAmount != null">
                back_amount,
            </if>
            <if test="differenceAmount != null">
                difference_amount,
            </if>
            <if test="abnormalAmount != null">
                abnormal_amount,
            </if>
            <if test="expectContractPrice != null">
                expect_contract_price,
            </if>
            <if test="expectContractPriceType != null">
                expect_contract_price_type,
            </if>
            <if test="contractPriceType != null">
                contract_price_type,
            </if>
            <if test="contractPrice != null">
                contract_price,
            </if>
            <if test="goodsUnit != null">
                goods_unit,
            </if>
            <if test="companyEntrustId != null">
                company_entrust_id,
            </if>
            <if test="companyEntrustName != null">
                company_entrust_name,
            </if>
            <if test="upstreamCustomer != null">
                upstream_customer,
            </if>
            <if test="companyCarrierType != null">
                company_carrier_type,
            </if>
            <if test="companyCarrierId != null">
                company_carrier_id,
            </if>
            <if test="companyCarrierName != null">
                company_carrier_name,
            </if>
            <if test="carrierContactId != null">
                carrier_contact_id,
            </if>
            <if test="carrierContactName != null">
                carrier_contact_name,
            </if>
            <if test="carrierContactPhone != null">
                carrier_contact_phone,
            </if>
            <if test="companyCarrierLevel != null">
                company_carrier_level,
            </if>
            <if test="dispatchVehicleCount != null">
                dispatch_vehicle_count,
            </if>
            <if test="entrustType != null">
                entrust_type,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="carrierPriceType != null">
                carrier_price_type,
            </if>
            <if test="carrierPrice != null">
                carrier_price,
            </if>
            <if test="ifUrgent != null">
                if_urgent,
            </if>
            <if test="dispatchValidity != null">
                dispatch_validity,
            </if>
            <if test="ifOverdue != null">
                if_overdue,
            </if>
            <if test="ifEmpty != null">
                if_empty,
            </if>
            <if test="ifRollback != null">
                if_rollback,
            </if>
            <if test="rollbackCauseType != null">
                rollback_cause_type,
            </if>
            <if test="rollbackCauseTypeTwo != null">
                rollback_cause_type_two,
            </if>
            <if test="rollbackRemark != null">
                rollback_remark,
            </if>
            <if test="availableOnWeekends != null">
                available_on_weekends,
            </if>
            <if test="loadingUnloadingPart != null">
                loading_unloading_part,
            </if>
            <if test="loadingUnloadingCharge != null">
                loading_unloading_charge,
            </if>
            <if test="recycleTaskType != null">
                recycle_task_type,
            </if>
            <if test="groundPushTaskCode != null">
                ground_push_task_code,
            </if>
            <if test="projectLabel != null">
                project_label,
            </if>
            <if test="orderType != null">
                order_type,
            </if>
            <if test="sinopecOrderNo != null">
                sinopec_order_no,
            </if>
            <if test="manufacturerName != null">
                manufacturer_name,
            </if>
            <if test="itemTransGroupName != null">
                item_trans_group_name,
            </if>
            <if test="itemPackSpecName != null">
                item_pack_spec_name,
            </if>
            <if test="dispatcherName != null">
                dispatcher_name,
            </if>
            <if test="dispatcherPhone != null">
                dispatcher_phone,
            </if>
            <if test="ifObjection != null">
                if_objection,
            </if>
            <if test="ifObjectionSinopec != null">
                if_objection_sinopec,
            </if>
            <if test="sinopecCustomerId != null">
                sinopec_customer_id,
            </if>
            <if test="sinopecCustomerName != null">
                sinopec_customer_name,
            </if>
            <if test="sinopecOnlineGoodsFlag != null">
                sinopec_online_goods_flag,
            </if>
            <if test="lbsCode != null">
                lbs_code,
            </if>
            <if test="fixedDemand != null">
                fixed_demand,
            </if>
            <if test="autoPublish != null">
                auto_publish,
            </if>
            <if test="orderMode != null">
                order_mode,
            </if>
            <if test="vehicleLengthId != null">
                vehicle_length_id,
            </if>
            <if test="vehicleLength != null">
                vehicle_length,
            </if>
            <if test="ifRecycleByCode != null">
                if_recycle_by_code,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by,
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time,
            </if>
            <if test="valid != null">
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="entrustStatus != null">
                #{entrustStatus,jdbcType=INTEGER},
            </if>
            <if test="statusUpdateTime != null">
                #{statusUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="ifCancel != null">
                #{ifCancel,jdbcType=INTEGER},
            </if>
            <if test="cancelType != null">
                #{cancelType,jdbcType=INTEGER},
            </if>
            <if test="cancelTime != null">
                #{cancelTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cancelReason != null">
                #{cancelReason,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                #{source,jdbcType=INTEGER},
            </if>
            <if test="demandOrderCode != null">
                #{demandOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=INTEGER},
            </if>
            <if test="customerName != null">
                #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="customerUserName != null">
                #{customerUserName,jdbcType=VARCHAR},
            </if>
            <if test="customerUserMobile != null">
                HEX(AES_ENCRYPT(#{customerUserMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="customerOrderSource != null">
                #{customerOrderSource,jdbcType=INTEGER},
            </if>
            <if test="customerOrderCode != null">
                #{customerOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="publishName != null">
                #{publishName,jdbcType=VARCHAR},
            </if>
            <if test="publishMobile != null">
                HEX(AES_ENCRYPT(#{publishMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="publishTime != null">
                #{publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="publishOrgCode != null">
                #{publishOrgCode,jdbcType=VARCHAR},
            </if>
            <if test="publishOrgName != null">
                #{publishOrgName,jdbcType=VARCHAR},
            </if>
            <if test="ticketTime != null">
                #{ticketTime,jdbcType=TIMESTAMP},
            </if>
            <if test="settlementTonnage != null">
                #{settlementTonnage,jdbcType=INTEGER},
            </if>
            <if test="carrierSettlement != null">
                #{carrierSettlement,jdbcType=INTEGER},
            </if>
            <if test="goodsAmount != null">
                #{goodsAmount,jdbcType=DECIMAL},
            </if>
            <if test="arrangedAmount != null">
                #{arrangedAmount,jdbcType=DECIMAL},
            </if>
            <if test="notArrangedAmount != null">
                #{notArrangedAmount,jdbcType=DECIMAL},
            </if>
            <if test="backAmount != null">
                #{backAmount,jdbcType=DECIMAL},
            </if>
            <if test="differenceAmount != null">
                #{differenceAmount,jdbcType=DECIMAL},
            </if>
            <if test="abnormalAmount != null">
                #{abnormalAmount,jdbcType=DECIMAL},
            </if>
            <if test="expectContractPrice != null">
                #{expectContractPrice,jdbcType=DECIMAL},
            </if>
            <if test="expectContractPriceType != null">
                #{expectContractPriceType,jdbcType=INTEGER},
            </if>
            <if test="contractPriceType != null">
                #{contractPriceType,jdbcType=INTEGER},
            </if>
            <if test="contractPrice != null">
                #{contractPrice,jdbcType=DECIMAL},
            </if>
            <if test="goodsUnit != null">
                #{goodsUnit,jdbcType=INTEGER},
            </if>
            <if test="companyEntrustId != null">
                #{companyEntrustId,jdbcType=BIGINT},
            </if>
            <if test="companyEntrustName != null">
                #{companyEntrustName,jdbcType=VARCHAR},
            </if>
            <if test="upstreamCustomer != null">
                #{upstreamCustomer,jdbcType=VARCHAR},
            </if>
            <if test="companyCarrierType != null">
                #{companyCarrierType,jdbcType=INTEGER},
            </if>
            <if test="companyCarrierId != null">
                #{companyCarrierId,jdbcType=BIGINT},
            </if>
            <if test="companyCarrierName != null">
                #{companyCarrierName,jdbcType=VARCHAR},
            </if>
            <if test="carrierContactId != null">
                #{carrierContactId,jdbcType=BIGINT},
            </if>
            <if test="carrierContactName != null">
                #{carrierContactName,jdbcType=VARCHAR},
            </if>
            <if test="carrierContactPhone != null">
                HEX(AES_ENCRYPT(#{carrierContactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="companyCarrierLevel != null">
                #{companyCarrierLevel,jdbcType=INTEGER},
            </if>
            <if test="dispatchVehicleCount != null">
                #{dispatchVehicleCount,jdbcType=INTEGER},
            </if>
            <if test="entrustType != null">
                #{entrustType,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="carrierPriceType != null">
                #{carrierPriceType,jdbcType=INTEGER},
            </if>
            <if test="carrierPrice != null">
                #{carrierPrice,jdbcType=DECIMAL},
            </if>
            <if test="ifUrgent != null">
                #{ifUrgent,jdbcType=INTEGER},
            </if>
            <if test="dispatchValidity != null">
                #{dispatchValidity,jdbcType=INTEGER},
            </if>
            <if test="ifOverdue != null">
                #{ifOverdue,jdbcType=INTEGER},
            </if>
            <if test="ifEmpty != null">
                #{ifEmpty,jdbcType=INTEGER},
            </if>
            <if test="ifRollback != null">
                #{ifRollback,jdbcType=INTEGER},
            </if>
            <if test="rollbackCauseType != null">
                #{rollbackCauseType,jdbcType=INTEGER},
            </if>
            <if test="rollbackCauseTypeTwo != null">
                #{rollbackCauseTypeTwo,jdbcType=INTEGER},
            </if>
            <if test="rollbackRemark != null">
                #{rollbackRemark,jdbcType=VARCHAR},
            </if>
            <if test="availableOnWeekends != null">
                #{availableOnWeekends,jdbcType=INTEGER},
            </if>
            <if test="loadingUnloadingPart != null">
                #{loadingUnloadingPart,jdbcType=INTEGER},
            </if>
            <if test="loadingUnloadingCharge != null">
                #{loadingUnloadingCharge,jdbcType=DECIMAL},
            </if>
            <if test="recycleTaskType != null">
                #{recycleTaskType,jdbcType=INTEGER},
            </if>
            <if test="groundPushTaskCode != null">
                #{groundPushTaskCode,jdbcType=VARCHAR},
            </if>
            <if test="projectLabel != null">
                #{projectLabel,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                #{orderType,jdbcType=INTEGER},
            </if>
            <if test="sinopecOrderNo != null">
                #{sinopecOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="manufacturerName != null">
                #{manufacturerName,jdbcType=VARCHAR},
            </if>
            <if test="itemTransGroupName != null">
                #{itemTransGroupName,jdbcType=VARCHAR},
            </if>
            <if test="itemPackSpecName != null">
                #{itemPackSpecName,jdbcType=VARCHAR},
            </if>
            <if test="dispatcherName != null">
                #{dispatcherName,jdbcType=VARCHAR},
            </if>
            <if test="dispatcherPhone != null">
                #{dispatcherPhone,jdbcType=VARCHAR},
            </if>
            <if test="ifObjection != null">
                #{ifObjection,jdbcType=INTEGER},
            </if>
            <if test="ifObjectionSinopec != null">
                #{ifObjectionSinopec,jdbcType=INTEGER},
            </if>
            <if test="sinopecCustomerId != null">
                #{sinopecCustomerId,jdbcType=BIGINT},
            </if>
            <if test="sinopecCustomerName != null">
                #{sinopecCustomerName,jdbcType=VARCHAR},
            </if>
            <if test="sinopecOnlineGoodsFlag != null">
                #{sinopecOnlineGoodsFlag,jdbcType=INTEGER},
            </if>
            <if test="lbsCode != null">
                #{lbsCode,jdbcType=VARCHAR},
            </if>
            <if test="fixedDemand != null">
                #{fixedDemand,jdbcType=VARCHAR},
            </if>
            <if test="autoPublish != null">
                #{autoPublish,jdbcType=INTEGER},
            </if>
            <if test="orderMode != null">
                #{orderMode,jdbcType=INTEGER},
            </if>
            <if test="vehicleLengthId != null">
                #{vehicleLengthId,jdbcType=BIGINT},
            </if>
            <if test="vehicleLength != null">
                #{vehicleLength,jdbcType=DECIMAL},
            </if>
            <if test="ifRecycleByCode != null">
                #{ifRecycleByCode,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelectiveEncrypt" parameterType="com.logistics.tms.entity.TDemandOrder">
        update t_demand_order
        <set>
            <if test="entrustStatus != null">
                entrust_status = #{entrustStatus,jdbcType=INTEGER},
            </if>
            <if test="statusUpdateTime != null">
                status_update_time = #{statusUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="ifCancel != null">
                if_cancel = #{ifCancel,jdbcType=INTEGER},
            </if>
            <if test="cancelType != null">
                cancel_type = #{cancelType,jdbcType=INTEGER},
            </if>
            <if test="cancelTime != null">
                cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cancelReason != null">
                cancel_reason = #{cancelReason,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                source = #{source,jdbcType=INTEGER},
            </if>
            <if test="demandOrderCode != null">
                demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                business_type = #{businessType,jdbcType=INTEGER},
            </if>
            <if test="customerName != null">
                customer_name = #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="customerUserName != null">
                customer_user_name = #{customerUserName,jdbcType=VARCHAR},
            </if>
            <if test="customerUserMobile != null">
                customer_user_mobile = HEX(AES_ENCRYPT(#{customerUserMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="customerOrderSource != null">
                customer_order_source = #{customerOrderSource,jdbcType=INTEGER},
            </if>
            <if test="customerOrderCode != null">
                customer_order_code = #{customerOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="publishName != null">
                publish_name = #{publishName,jdbcType=VARCHAR},
            </if>
            <if test="publishMobile != null">
                publish_mobile = HEX(AES_ENCRYPT(#{publishMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="publishTime != null">
                publish_time = #{publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="publishOrgCode != null">
                publish_org_code = #{publishOrgCode,jdbcType=VARCHAR},
            </if>
            <if test="publishOrgName != null">
                publish_org_name = #{publishOrgName,jdbcType=VARCHAR},
            </if>
            <if test="ticketTime != null">
                ticket_time = #{ticketTime,jdbcType=TIMESTAMP},
            </if>
            <if test="settlementTonnage != null">
                settlement_tonnage = #{settlementTonnage,jdbcType=INTEGER},
            </if>
            <if test="carrierSettlement != null">
                carrier_settlement = #{carrierSettlement,jdbcType=INTEGER},
            </if>
            <if test="goodsAmount != null">
                goods_amount = #{goodsAmount,jdbcType=DECIMAL},
            </if>
            <if test="arrangedAmount != null">
                arranged_amount = #{arrangedAmount,jdbcType=DECIMAL},
            </if>
            <if test="notArrangedAmount != null">
                not_arranged_amount = #{notArrangedAmount,jdbcType=DECIMAL},
            </if>
            <if test="backAmount != null">
                back_amount = #{backAmount,jdbcType=DECIMAL},
            </if>
            <if test="differenceAmount != null">
                difference_amount = #{differenceAmount,jdbcType=DECIMAL},
            </if>
            <if test="abnormalAmount != null">
                abnormal_amount = #{abnormalAmount,jdbcType=DECIMAL},
            </if>
            <if test="expectContractPrice != null">
                expect_contract_price = #{expectContractPrice,jdbcType=DECIMAL},
            </if>
            <if test="expectContractPriceType != null">
                expect_contract_price_type = #{expectContractPriceType,jdbcType=INTEGER},
            </if>
            <if test="contractPriceType != null">
                contract_price_type = #{contractPriceType,jdbcType=INTEGER},
            </if>
            <if test="contractPrice != null">
                contract_price = #{contractPrice,jdbcType=DECIMAL},
            </if>
            <if test="goodsUnit != null">
                goods_unit = #{goodsUnit,jdbcType=INTEGER},
            </if>
            <if test="companyEntrustId != null">
                company_entrust_id = #{companyEntrustId,jdbcType=BIGINT},
            </if>
            <if test="companyEntrustName != null">
                company_entrust_name = #{companyEntrustName,jdbcType=VARCHAR},
            </if>
            <if test="upstreamCustomer != null">
                upstream_customer = #{upstreamCustomer,jdbcType=VARCHAR},
            </if>
            <if test="companyCarrierType != null">
                company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
            </if>
            <if test="companyCarrierId != null">
                company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
            </if>
            <if test="companyCarrierName != null">
                company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
            </if>
            <if test="carrierContactId != null">
                carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
            </if>
            <if test="carrierContactName != null">
                carrier_contact_name = #{carrierContactName,jdbcType=VARCHAR},
            </if>
            <if test="carrierContactPhone != null">
                carrier_contact_phone = HEX(AES_ENCRYPT(#{carrierContactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="companyCarrierLevel != null">
                company_carrier_level = #{companyCarrierLevel,jdbcType=INTEGER},
            </if>
            <if test="dispatchVehicleCount != null">
                dispatch_vehicle_count = #{dispatchVehicleCount,jdbcType=INTEGER},
            </if>
            <if test="entrustType != null">
                entrust_type = #{entrustType,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="carrierPriceType != null">
                carrier_price_type = #{carrierPriceType,jdbcType=INTEGER},
            </if>
            <if test="carrierPrice != null">
                carrier_price = #{carrierPrice,jdbcType=DECIMAL},
            </if>
            <if test="ifUrgent != null">
                if_urgent = #{ifUrgent,jdbcType=INTEGER},
            </if>
            <if test="dispatchValidity != null">
                dispatch_validity = #{dispatchValidity,jdbcType=INTEGER},
            </if>
            <if test="ifOverdue != null">
                if_overdue = #{ifOverdue,jdbcType=INTEGER},
            </if>
            <if test="ifEmpty != null">
                if_empty = #{ifEmpty,jdbcType=INTEGER},
            </if>
            <if test="ifRollback != null">
                if_rollback = #{ifRollback,jdbcType=INTEGER},
            </if>
            <if test="rollbackCauseType != null">
                rollback_cause_type = #{rollbackCauseType,jdbcType=INTEGER},
            </if>
            <if test="rollbackCauseTypeTwo != null">
                rollback_cause_type_two = #{rollbackCauseTypeTwo,jdbcType=INTEGER},
            </if>
            <if test="rollbackRemark != null">
                rollback_remark = #{rollbackRemark,jdbcType=VARCHAR},
            </if>
            <if test="availableOnWeekends != null">
                available_on_weekends = #{availableOnWeekends,jdbcType=INTEGER},
            </if>
            <if test="loadingUnloadingPart != null">
                loading_unloading_part = #{loadingUnloadingPart,jdbcType=INTEGER},
            </if>
            <if test="loadingUnloadingCharge != null">
                loading_unloading_charge = #{loadingUnloadingCharge,jdbcType=DECIMAL},
            </if>
            <if test="recycleTaskType != null">
                recycle_task_type = #{recycleTaskType,jdbcType=INTEGER},
            </if>
            <if test="groundPushTaskCode != null">
                ground_push_task_code = #{groundPushTaskCode,jdbcType=VARCHAR},
            </if>
            <if test="projectLabel != null">
                project_label = #{projectLabel,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                order_type = #{orderType,jdbcType=INTEGER},
            </if>
            <if test="sinopecOrderNo != null">
                sinopec_order_no = #{sinopecOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="manufacturerName != null">
                manufacturer_name = #{manufacturerName,jdbcType=VARCHAR},
            </if>
            <if test="itemTransGroupName != null">
                item_trans_group_name = #{itemTransGroupName,jdbcType=VARCHAR},
            </if>
            <if test="itemPackSpecName != null">
                item_pack_spec_name = #{itemPackSpecName,jdbcType=VARCHAR},
            </if>
            <if test="dispatcherName != null">
                dispatcher_name = #{dispatcherName,jdbcType=VARCHAR},
            </if>
            <if test="dispatcherPhone != null">
                dispatcher_phone = #{dispatcherPhone,jdbcType=VARCHAR},
            </if>
            <if test="ifObjection != null">
                if_objection = #{ifObjection,jdbcType=INTEGER},
            </if>
            <if test="ifObjectionSinopec != null">
                if_objection_sinopec = #{ifObjectionSinopec,jdbcType=INTEGER},
            </if>
            <if test="sinopecCustomerId != null">
                sinopec_customer_id = #{sinopecCustomerId,jdbcType=BIGINT},
            </if>
            <if test="sinopecCustomerName != null">
                sinopec_customer_name = #{sinopecCustomerName,jdbcType=VARCHAR},
            </if>
            <if test="sinopecOnlineGoodsFlag != null">
                sinopec_online_goods_flag = #{sinopecOnlineGoodsFlag,jdbcType=INTEGER},
            </if>
            <if test="lbsCode != null">
                lbs_code = #{lbsCode,jdbcType=VARCHAR},
            </if>
            <if test="fixedDemand != null">
                fixed_demand = #{fixedDemand,jdbcType=VARCHAR},
            </if>
            <if test="autoPublish != null">
                auto_publish = #{autoPublish,jdbcType=INTEGER},
            </if>
            <if test="orderMode != null">
                order_mode = #{orderMode,jdbcType=INTEGER},
            </if>
            <if test="vehicleLengthId != null">
                vehicle_length_id = #{vehicleLengthId,jdbcType=BIGINT},
            </if>
            <if test="vehicleLength != null">
                vehicle_length = #{vehicleLength,jdbcType=DECIMAL},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_demand_order
        where valid = 1
        and id in (${demandOrderIds})
    </select>

    <select id="getByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_demand_order
        where valid = 1
        and demand_order_code = #{demandOrderCode}
    </select>

    <select id="getByCodeList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_demand_order
        where valid = 1
        and demand_order_code in
        <foreach collection="demandOrderCodeList" item="demandOrderCode" open="(" close=")" separator=",">
            #{demandOrderCode}
        </foreach>
    </select>

    <resultMap type="com.logistics.tms.controller.demandorder.response.DemandOrderSearchByIdsResponseModel" id="getDemandOrderDetailsByIdsResultMap">
        <id column="id" property="demandId" jdbcType="BIGINT"/>
        <result column="entrust_status" property="status" jdbcType="INTEGER"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="if_rollback" property="ifRollback" jdbcType="INTEGER"/>
        <result column="if_urgent" property="ifUrgent" jdbcType="INTEGER"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="publish_name" property="publishName" jdbcType="VARCHAR"/>
        <result column="publish_mobile" property="publishMobile" jdbcType="VARCHAR"/>
        <result column="publish_time" property="publishTime" jdbcType="TIMESTAMP"/>
        <result column="publish_org_code" property="publishOrgCode" jdbcType="VARCHAR"/>
        <result column="publish_org_name" property="publishOrgName" jdbcType="VARCHAR"/>
        <result column="goods_amount" property="goodsAmount" jdbcType="DECIMAL"/>
        <result column="arranged_amount" property="arrangedAmount" jdbcType="DECIMAL"/>
        <result column="not_arranged_amount" property="notArrangedAmount" jdbcType="DECIMAL"/>
        <result column="dispatch_vehicle_count" property="dispatchVehicleCount" jdbcType="INTEGER"/>
        <result column="entrust_type" property="entrustType" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
        <result column="customer_user_name" property="customerUserName" jdbcType="VARCHAR"/>
        <result column="customer_user_mobile" property="customerUserMobile" jdbcType="VARCHAR"/>
        <result column="cancel_reason" property="cancelReason" jdbcType="VARCHAR"/>
        <result column="load_yelo_address_code" property="loadYeloAddressCode" jdbcType="VARCHAR"/>
        <result column="load_address_code" property="loadAddressCode" jdbcType="VARCHAR"/>
        <result column="upstream_customer" property="upstreamCustomer" jdbcType="VARCHAR"/>
        <result column="load_province_id" property="loadProvinceId" jdbcType="BIGINT"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_id" property="loadCityId" jdbcType="BIGINT"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_id" property="loadAreaId" jdbcType="BIGINT"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="load_company" property="loadCompany" jdbcType="VARCHAR"/>
        <result column="load_longitude" property="loadLongitude" jdbcType="VARCHAR"/>
        <result column="load_latitude" property="loadLatitude" jdbcType="VARCHAR"/>
        <result column="expected_load_time" property="expectedLoadTime" jdbcType="TIMESTAMP"/>
        <result column="load_region_id" property="loadRegionId" jdbcType="BIGINT"/>
        <result column="load_region_name" property="loadRegionName" jdbcType="VARCHAR"/>
        <result column="load_region_contact_name" property="loadRegionContactName" jdbcType="VARCHAR"/>
        <result column="load_region_contact_phone" property="loadRegionContactPhone" jdbcType="VARCHAR"/>
        <result column="unload_province_id" property="unloadProvinceId" jdbcType="BIGINT"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_id" property="unloadCityId" jdbcType="BIGINT"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_id" property="unloadAreaId" jdbcType="BIGINT"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_address_code" property="unloadAddressCode" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="unload_company" property="unloadCompany" jdbcType="VARCHAR"/>
        <result column="unload_longitude" property="unloadLongitude" jdbcType="VARCHAR"/>
        <result column="unload_latitude" property="unloadLatitude" jdbcType="VARCHAR"/>
        <result column="unload_address_is_amend" property="unloadAddressIsAmend" jdbcType="INTEGER"/>
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR"/>
        <result column="consignor_name" property="consignorName" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR"/>
        <result column="expected_unload_time" property="expectedUnloadTime" jdbcType="TIMESTAMP"/>
        <result column="company_entrust_id" property="companyEntrustId" jdbcType="BIGINT"/>
        <result column="company_entrust_name" property="companyEntrustName" jdbcType="VARCHAR"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="company_carrier_type" property="companyCarrierType" jdbcType="INTEGER"/>
        <result column="company_carrier_name" property="companyCarrierName" jdbcType="VARCHAR"/>
        <result column="carrier_contact_id" property="carrierContactId" jdbcType="BIGINT"/>
        <result column="carrier_contact_name" property="carrierContactName" jdbcType="VARCHAR"/>
        <result column="carrier_contact_phone" property="carrierContactMobile" jdbcType="VARCHAR"/>
        <result column="company_carrier_level" property="companyCarrierLevel" jdbcType="INTEGER"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="contract_price_type" property="contractPriceType" jdbcType="INTEGER"/>
        <result column="contract_price" property="contractPrice" jdbcType="DECIMAL"/>
        <result column="expect_contract_price_type" property="exceptContractPriceType" jdbcType="INTEGER"/>
        <result column="expect_contract_price" property="exceptContractPrice" jdbcType="DECIMAL"/>
        <result column="source" property="source" jdbcType="INTEGER"/>
        <result column="settlement_tonnage" property="settlementTonnage" jdbcType="INTEGER"/>
        <result column="carrier_settlement" property="carrierSettlement" jdbcType="INTEGER"/>
        <result column="if_objection_sinopec" property="ifObjectionSinopec" jdbcType="INTEGER"/>
        <result column="carrier_price_type" property="carrierPriceType" jdbcType="INTEGER"/>
        <result column="carrier_price" property="carrierPrice" jdbcType="DECIMAL"/>
        <result column="business_type" property="businessType" jdbcType="INTEGER"/>
        <result column="customer_order_source" property="customerOrderSource" jdbcType="INTEGER"/>
        <result column="available_on_weekends" property="availableOnWeekends" jdbcType="INTEGER"/>
        <result column="loading_unloading_part" property="loadingUnloadingPart" jdbcType="INTEGER"/>
        <result column="loading_unloading_charge" property="loadingUnloadingCharge" jdbcType="DECIMAL"/>
        <result column="recycle_task_type" property="recycleTaskType" jdbcType="INTEGER"/>
        <result column="project_label" property="projectLabel" jdbcType="VARCHAR"/>
        <result column="if_recycle_by_code" property="ifRecycleByCode" jdbcType="INTEGER"/>
        <result column="if_ext_demand_order" property="ifExtDemandOrder" jdbcType="INTEGER"/>

        <result column="order_mode" property="orderMode" jdbcType="INTEGER"/>
        <result column="vehicle_length_id" property="vehicleLengthId" jdbcType="BIGINT"/>
        <result column="vehicle_length" property="vehicleLength" jdbcType="DECIMAL"/>
        <collection property="goodsResponseModels" column="demandId" ofType="com.logistics.tms.controller.demandorder.response.DemandOrderGoodsResponseModel" javaType="ArrayList">
            <id column="demandOrderGoodsId" property="demandOrderGoodsId" jdbcType="BIGINT"/>
            <result column="demandOrderId" property="demandOrderId" jdbcType="BIGINT"/>
            <result column="sku_code" property="skuCode" jdbcType="VARCHAR"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="category_name" property="categoryName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="goodsAmountNumber" property="goodsAmountNumber" jdbcType="DECIMAL"/>
            <result column="arrangedAmountNumber" property="arrangedAmountNumber" jdbcType="DECIMAL"/>
            <result column="notArrangedAmountNumber" property="notArrangedAmountNumber" jdbcType="DECIMAL"/>
        </collection>
        <collection property="demandOrderEventModels" ofType="com.logistics.tms.controller.demandorder.response.DemandOrderEventModel" javaType="ArrayList">
            <id column="eventId" property="eventId" jdbcType="BIGINT"/>
            <result column="event" property="event" jdbcType="INTEGER"/>
            <result column="event_desc" property="eventDesc" jdbcType="VARCHAR"/>
            <result column="event_time" property="eventTime" jdbcType="TIMESTAMP"/>
            <result column="operator_name" property="operatorName" jdbcType="VARCHAR"/>
            <result column="operate_time" property="operateTime" jdbcType="TIMESTAMP"/>
        </collection>
        <collection property="demandOrderOrderRelModels" ofType="com.logistics.tms.controller.demandorder.response.DemandOrderOrderRelModel">
            <id column="demandOrderOrderRelId" property="demandOrderOrderRelId" jdbcType="BIGINT"/>
            <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
            <result column="order_id" property="orderId" jdbcType="BIGINT"/>
            <result column="order_code" property="orderCode" jdbcType="VARCHAR"/>
            <result column="total_amount" property="totalAmount" jdbcType="DECIMAL"/>
            <result column="arrangedAmount" property="arrangedAmount" jdbcType="DECIMAL"/>
            <result column="backAmount" property="backAmount" jdbcType="DECIMAL"/>
            <result column="rel_type" property="relType" jdbcType="INTEGER"/>
            <result column="relRemark" property="remark" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <select id="getDemandOrderGoodsAndRelByIds" resultMap="getDemandOrderDetailsByIdsResultMap">
        SELECT
        tdo.id,
        tdo.entrust_status,
        tdo.if_cancel,
        tdo.if_empty,
        tdo.if_rollback,
        tdo.if_urgent,
        tdo.demand_order_code,
        tdo.customer_order_code,
        tdo.publish_name,
        AES_DECRYPT(UNHEX(tdo.publish_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as publish_mobile,
        tdo.publish_time,
        tdo.publish_org_code,
        tdo.publish_org_name,
        tdo.goods_amount,
        tdo.arranged_amount,
        tdo.not_arranged_amount,
        tdo.cancel_reason,
        tdo.dispatch_vehicle_count,
        tdo.remark,
        tdo.company_entrust_id,
        tdo.company_entrust_name,
        tdo.company_carrier_name,
        tdo.goods_unit,
        tdo.contract_price_type,
        tdo.contract_price,
        tdo.expect_contract_price_type,
        tdo.expect_contract_price,
        tdo.entrust_type,
        tdo.source,
        tdo.if_objection_sinopec,
        tdo.company_carrier_id,
        tdo.company_carrier_type,
        tdo.company_carrier_name,
        tdo.carrier_contact_id,
        tdo.carrier_contact_name,
        AES_DECRYPT(UNHEX(tdo.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrier_contact_phone,
        tdo.company_carrier_level,
        tdo.settlement_tonnage,
        tdo.carrier_settlement,
        tdo.customer_name,
        tdo.customer_user_name,
        AES_DECRYPT(UNHEX(tdo.customer_user_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as customer_user_mobile,
        tdo.business_type,
        tdo.customer_order_source,
        tdo.carrier_price_type,
        tdo.carrier_price,
        tdo.available_on_weekends,
        tdo.loading_unloading_part,
        tdo.loading_unloading_charge,
        tdo.recycle_task_type,
        tdo.project_label,
        tdo.upstream_customer,
        tdo.order_mode,
        tdo.vehicle_length_id,
        tdo.vehicle_length,
        tdo.if_recycle_by_code,
        tdo.if_ext_demand_order,

        tdog.id as demandOrderGoodsId,
        tdog.demand_order_id as demandOrderId,
        tdog.sku_code,
        tdog.goods_name,
        tdog.category_name,
        tdog.length,
        tdog.width,
        tdog.height,
        tdog.goods_size,
        tdog.goods_amount as goodsAmountNumber,
        tdog.arranged_amount as arrangedAmountNumber,
        tdog.not_arranged_amount as notArrangedAmountNumber,

        tdoor.id as demandOrderOrderRelId,
        tdoor.demand_order_id,
        tdoor.order_id,
        tdoor.order_code,
        tdoor.total_amount,
        tdoor.arranged_amount as arrangedAmount,
        tdoor.back_amount as backAmount,
        tdoor.rel_type,
        tdoor.remark as relRemark

        FROM t_demand_order tdo
        LEFT JOIN t_demand_order_goods tdog ON tdo.id = tdog.demand_order_id and tdog.valid = 1
        left join t_demand_order_order_rel tdoor on tdoor.demand_order_id = tdo.id and tdoor.valid = 1
        WHERE tdo.valid = 1
        and tdo.id in (${params.demandIds})
    </select>
    <select id="getDemandOrderAddressEventByIds" resultMap="getDemandOrderDetailsByIdsResultMap">
        SELECT
        tdo.id,

        tdoa.load_yelo_address_code,
        tdoa.load_address_code,
        tdoa.load_province_id,
        tdoa.load_province_name,
        tdoa.load_city_id,
        tdoa.load_city_name,
        tdoa.load_area_id,
        tdoa.load_area_name,
        tdoa.load_detail_address,
        tdoa.load_warehouse,
        tdoa.load_company,
        tdoa.load_longitude,
        tdoa.load_latitude,
        tdoa.consignor_name,
        tdoa.consignor_mobile,
        tdoa.expected_load_time,
        tdoa.load_region_id,
        tdoa.load_region_name,
        tdoa.load_region_contact_name,
        tdoa.load_region_contact_phone,
        tdoa.unload_province_id,
        tdoa.unload_province_name,
        tdoa.unload_city_id,
        tdoa.unload_city_name,
        tdoa.unload_area_id,
        tdoa.unload_area_name,
        tdoa.unload_detail_address,
        tdoa.unload_warehouse,
        tdoa.unload_company,
        tdoa.unload_longitude,
        tdoa.unload_latitude,
        tdoa.unload_address_is_amend,
        tdoa.unload_address_code,
        tdoa.receiver_name,
        tdoa.receiver_mobile,
        tdoa.expected_unload_time,

        tdoe.id as eventId,
        tdoe.event,
        tdoe.event_desc,
        tdoe.event_time,
        tdoe.operate_time,
        tdoe.operator_name

        FROM t_demand_order tdo
        LEFT JOIN t_demand_order_address tdoa ON tdo.id = tdoa.demand_order_id and tdoa.valid = 1
        LEFT JOIN t_demand_order_events tdoe on tdoe.valid = 1 and tdoe.demand_order_id = tdo.id
        WHERE tdo.valid = 1
        and tdo.id in (${params.demandIds})
    </select>

    <update id="batchUpdateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDemandOrder">
        <foreach collection="list" item="item" separator=";">
            update t_demand_order
            <set>
                <if test="item.entrustStatus != null">
                    entrust_status = #{item.entrustStatus,jdbcType=INTEGER},
                </if>
                <if test="item.statusUpdateTime != null">
                    status_update_time = #{item.statusUpdateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.status != null">
                    status = #{item.status,jdbcType=INTEGER},
                </if>
                <if test="item.ifCancel != null">
                    if_cancel = #{item.ifCancel,jdbcType=INTEGER},
                </if>
                <if test="item.cancelType != null">
                    cancel_type = #{item.cancelType,jdbcType=INTEGER},
                </if>
                <if test="item.cancelTime != null">
                    cancel_time = #{item.cancelTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.cancelReason != null">
                    cancel_reason = #{item.cancelReason,jdbcType=VARCHAR},
                </if>
                <if test="item.source != null">
                    source = #{item.source,jdbcType=INTEGER},
                </if>
                <if test="item.demandOrderCode != null">
                    demand_order_code = #{item.demandOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.businessType != null">
                    business_type = #{item.businessType,jdbcType=INTEGER},
                </if>
                <if test="item.customerName != null">
                    customer_name = #{item.customerName,jdbcType=VARCHAR},
                </if>
                <if test="item.customerUserName != null">
                    customer_user_name = #{item.customerUserName,jdbcType=VARCHAR},
                </if>
                <if test="item.customerUserMobile != null">
                    customer_user_mobile = HEX(AES_ENCRYPT(#{item.customerUserMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
                </if>
                <if test="item.customerOrderSource != null">
                    customer_order_source = #{item.customerOrderSource,jdbcType=INTEGER},
                </if>
                <if test="item.customerOrderCode != null">
                    customer_order_code = #{item.customerOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.publishName != null">
                    publish_name = #{item.publishName,jdbcType=VARCHAR},
                </if>
                <if test="item.publishMobile != null">
                    publish_mobile = HEX(AES_ENCRYPT(#{item.publishMobile,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
                </if>
                <if test="item.publishTime != null">
                    publish_time = #{item.publishTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.publishOrgCode != null">
                    publish_org_code = #{item.publishOrgCode,jdbcType=VARCHAR},
                </if>
                <if test="item.publishOrgName != null">
                    publish_org_name = #{item.publishOrgName,jdbcType=VARCHAR},
                </if>
                <if test="item.ticketTime != null">
                    ticket_time = #{item.ticketTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.settlementTonnage != null">
                    settlement_tonnage = #{item.settlementTonnage,jdbcType=INTEGER},
                </if>
                <if test="item.carrierSettlement != null">
                    carrier_settlement = #{item.carrierSettlement,jdbcType=INTEGER},
                </if>
                <if test="item.goodsAmount != null">
                    goods_amount = #{item.goodsAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.arrangedAmount != null">
                    arranged_amount = #{item.arrangedAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.notArrangedAmount != null">
                    not_arranged_amount = #{item.notArrangedAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.backAmount != null">
                    back_amount = #{item.backAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.differenceAmount != null">
                    difference_amount = #{item.differenceAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.abnormalAmount != null">
                    abnormal_amount = #{item.abnormalAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.expectContractPrice != null">
                    expect_contract_price = #{item.expectContractPrice,jdbcType=DECIMAL},
                </if>
                <if test="item.expectContractPriceType != null">
                    expect_contract_price_type = #{item.expectContractPriceType,jdbcType=INTEGER},
                </if>
                <if test="item.contractPriceType != null">
                    contract_price_type = #{item.contractPriceType,jdbcType=INTEGER},
                </if>
                <if test="item.contractPrice != null">
                    contract_price = #{item.contractPrice,jdbcType=DECIMAL},
                </if>
                <if test="item.goodsUnit != null">
                    goods_unit = #{item.goodsUnit,jdbcType=INTEGER},
                </if>
                <if test="item.companyEntrustId != null">
                    company_entrust_id = #{item.companyEntrustId,jdbcType=BIGINT},
                </if>
                <if test="item.companyEntrustName != null">
                    company_entrust_name = #{item.companyEntrustName,jdbcType=VARCHAR},
                </if>
                <if test="item.upstreamCustomer != null">
                    upstream_customer = #{item.upstreamCustomer,jdbcType=VARCHAR},
                </if>
                <if test="item.companyCarrierType != null">
                    company_carrier_type = #{item.companyCarrierType,jdbcType=INTEGER},
                </if>
                <if test="item.companyCarrierId != null">
                    company_carrier_id = #{item.companyCarrierId,jdbcType=BIGINT},
                </if>
                <if test="item.companyCarrierName != null">
                    company_carrier_name = #{item.companyCarrierName,jdbcType=VARCHAR},
                </if>
                <if test="item.carrierContactId != null">
                    carrier_contact_id = #{item.carrierContactId,jdbcType=BIGINT},
                </if>
                <if test="item.carrierContactName != null">
                    carrier_contact_name = #{item.carrierContactName,jdbcType=VARCHAR},
                </if>
                <if test="item.carrierContactPhone != null">
                    carrier_contact_phone = HEX(AES_ENCRYPT(#{item.carrierContactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
                </if>
                <if test="item.companyCarrierLevel != null">
                    company_carrier_level = #{item.companyCarrierLevel,jdbcType=INTEGER},
                </if>
                <if test="item.dispatchVehicleCount != null">
                    dispatch_vehicle_count = #{item.dispatchVehicleCount,jdbcType=INTEGER},
                </if>
                <if test="item.entrustType != null">
                    entrust_type = #{item.entrustType,jdbcType=INTEGER},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.carrierPriceType != null">
                    carrier_price_type = #{item.carrierPriceType,jdbcType=INTEGER},
                </if>
                <if test="item.carrierPrice != null">
                    carrier_price = #{item.carrierPrice,jdbcType=DECIMAL},
                </if>
                <if test="item.ifUrgent != null">
                    if_urgent = #{item.ifUrgent,jdbcType=INTEGER},
                </if>
                <if test="item.dispatchValidity != null">
                    dispatch_validity = #{item.dispatchValidity,jdbcType=INTEGER},
                </if>
                <if test="item.ifOverdue != null">
                    if_overdue = #{item.ifOverdue,jdbcType=INTEGER},
                </if>
                <if test="item.ifEmpty != null">
                    if_empty = #{item.ifEmpty,jdbcType=INTEGER},
                </if>
                <if test="item.ifRollback != null">
                    if_rollback = #{item.ifRollback,jdbcType=INTEGER},
                </if>
                <if test="item.rollbackCauseType != null">
                    rollback_cause_type = #{item.rollbackCauseType,jdbcType=INTEGER},
                </if>
                <if test="item.rollbackCauseTypeTwo != null">
                    rollback_cause_type_two = #{item.rollbackCauseTypeTwo,jdbcType=INTEGER},
                </if>
                <if test="item.rollbackRemark != null">
                    rollback_remark = #{item.rollbackRemark,jdbcType=VARCHAR},
                </if>
                <if test="item.availableOnWeekends != null">
                    available_on_weekends = #{item.availableOnWeekends,jdbcType=INTEGER},
                </if>
                <if test="item.loadingUnloadingPart != null">
                    loading_unloading_part = #{item.loadingUnloadingPart,jdbcType=INTEGER},
                </if>
                <if test="item.loadingUnloadingCharge != null">
                    loading_unloading_charge = #{item.loadingUnloadingCharge,jdbcType=DECIMAL},
                </if>
                <if test="item.recycleTaskType != null">
                    recycle_task_type = #{item.recycleTaskType,jdbcType=INTEGER},
                </if>
                <if test="item.groundPushTaskCode != null">
                    ground_push_task_code = #{item.groundPushTaskCode,jdbcType=VARCHAR},
                </if>
                <if test="item.projectLabel != null">
                    project_label = #{item.projectLabel,jdbcType=VARCHAR},
                </if>
                <if test="item.orderType != null">
                    order_type = #{item.orderType,jdbcType=INTEGER},
                </if>
                <if test="item.sinopecOrderNo != null">
                    sinopec_order_no = #{item.sinopecOrderNo,jdbcType=VARCHAR},
                </if>
                <if test="item.manufacturerName != null">
                    manufacturer_name = #{item.manufacturerName,jdbcType=VARCHAR},
                </if>
                <if test="item.itemTransGroupName != null">
                    item_trans_group_name = #{item.itemTransGroupName,jdbcType=VARCHAR},
                </if>
                <if test="item.itemPackSpecName != null">
                    item_pack_spec_name = #{item.itemPackSpecName,jdbcType=VARCHAR},
                </if>
                <if test="item.dispatcherName != null">
                    dispatcher_name = #{item.dispatcherName,jdbcType=VARCHAR},
                </if>
                <if test="item.dispatcherPhone != null">
                    dispatcher_phone = #{item.dispatcherPhone,jdbcType=VARCHAR},
                </if>
                <if test="item.ifObjection != null">
                    if_objection = #{item.ifObjection,jdbcType=INTEGER},
                </if>
                <if test="item.ifObjectionSinopec != null">
                    if_objection_sinopec = #{item.ifObjectionSinopec,jdbcType=INTEGER},
                </if>
                <if test="item.sinopecCustomerId != null">
                    sinopec_customer_id = #{item.sinopecCustomerId,jdbcType=BIGINT},
                </if>
                <if test="item.sinopecCustomerName != null">
                    sinopec_customer_name = #{item.sinopecCustomerName,jdbcType=VARCHAR},
                </if>
                <if test="item.sinopecOnlineGoodsFlag != null">
                    sinopec_online_goods_flag = #{item.sinopecOnlineGoodsFlag,jdbcType=INTEGER},
                </if>
                <if test="item.lbsCode != null">
                    lbs_code = #{item.lbsCode,jdbcType=VARCHAR},
                </if>
                <if test="item.fixedDemand != null">
                    fixed_demand = #{item.fixedDemand,jdbcType=VARCHAR},
                </if>
                <if test="item.autoPublish != null">
                    auto_publish = #{item.autoPublish,jdbcType=INTEGER},
                </if>
                <if test="item.orderMode != null">
                    order_mode = #{item.orderMode,jdbcType=INTEGER},
                </if>
                <if test="item.vehicleLengthId != null">
                    vehicle_length_id = #{item.vehicleLengthId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleLength != null">
                    vehicle_length = #{item.vehicleLength,jdbcType=DECIMAL},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <resultMap id="getDemandOrderInfoByIdsMap" type="com.logistics.tms.controller.demandorder.response.GetDemandOrderInfoByIdsModel">
        <id column="demandOrderId" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="demandOrderCode" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="entrustStatus" property="entrustStatus" jdbcType="INTEGER"/>
        <result column="demandOrderStatus" property="demandOrderStatus" jdbcType="INTEGER"/>
        <result column="source" property="source" jdbcType="INTEGER"/>
        <result column="ifCancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="ifEmpty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="ifRollback" property="ifRollback" jdbcType="INTEGER"/>
        <result column="arrangedAmount" property="arrangedAmount" jdbcType="DECIMAL"/>
        <result column="notArrangedAmount" property="notArrangedAmount" jdbcType="DECIMAL"/>
        <result column="backAmount" property="backAmount" jdbcType="DECIMAL"/>
        <result column="goodsAmount" property="goodsAmount" jdbcType="DECIMAL"/>
        <result column="entrustType" property="entrustType" jdbcType="INTEGER"/>
        <result column="settlementTonnage" property="settlementTonnage" jdbcType="INTEGER"/>
        <result column="carrierSettlement" property="carrierSettlement" jdbcType="INTEGER"/>
        <result column="contractPriceType" property="contractPriceType" jdbcType="INTEGER"/>
        <result column="contractPrice" property="contractPrice" jdbcType="DECIMAL"/>
        <result column="expectContractPriceType" property="exceptContractPriceType" jdbcType="INTEGER"/>
        <result column="expectContractPrice" property="exceptContractPrice" jdbcType="DECIMAL"/>
        <result column="carrierPriceType" property="carrierPriceType" jdbcType="INTEGER"/>
        <result column="carrierPrice" property="carrierPrice" jdbcType="DECIMAL"/>
        <result column="goodsUnit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="companyEntrustId" property="companyEntrustId" jdbcType="BIGINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="order_type" property="orderType" jdbcType="INTEGER"/>
        <result column="loadProvinceId" property="loadProvinceId" jdbcType="BIGINT"/>
        <result column="loadCityId" property="loadCityId" jdbcType="BIGINT"/>
        <result column="loadAreaId" property="loadAreaId" jdbcType="BIGINT"/>
        <result column="unloadProvinceId" property="unloadProvinceId" jdbcType="BIGINT"/>
        <result column="unloadCityId" property="unloadCityId" jdbcType="BIGINT"/>
        <result column="unloadAreaId" property="unloadAreaId" jdbcType="BIGINT"/>
        <result column="loadProvinceName" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="loadCityName" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="loadAreaName" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="unloadProvinceName" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unloadCityName" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unloadAreaName" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="loadDetailAddress" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unloadDetailAddress" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="consignorName" property="consignorName" jdbcType="VARCHAR"/>
        <result column="consignorMobile" property="consignorMobile" jdbcType="VARCHAR"/>
        <result column="receiverName" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiverMobile" property="receiverMobile" jdbcType="VARCHAR"/>
        <result column="entrustCompany" property="entrustCompany" jdbcType="VARCHAR"/>
        <result column="upstreamCustomer" property="upstreamCustomer" jdbcType="VARCHAR"/>
        <result column="companyCarrierId" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="fixedDemand" property="fixedDemand" jdbcType="VARCHAR"/>
        <result column="autoPublish" property="autoPublish" jdbcType="INTEGER"/>
        <result column="projectLabel" property="projectLabel" jdbcType="VARCHAR"/>

        <collection property="goodsList" ofType="com.logistics.tms.controller.demandorder.response.GetDemandOrderGoodsInfoByIdsModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goodsName" property="goodsName" jdbcType="VARCHAR"/>
            <result column="goodsAmount2" property="goodsAmount" jdbcType="DECIMAL"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
        </collection>
    </resultMap>

    <select id="getDemandOrderInfoByIds" resultMap="getDemandOrderInfoByIdsMap">
        select
        tdo.id                         as demandOrderId,
        tdo.demand_order_code          as demandOrderCode,
        tdo.entrust_status             as entrustStatus,
        tdo.status                     as demandOrderStatus,
        tdo.source,
        tdo.if_cancel                  as ifCancel,
        tdo.if_empty                   as ifEmpty,
        tdo.if_rollback                as ifRollback,
        tdo.arranged_amount            as arrangedAmount,
        tdo.not_arranged_amount        as notArrangedAmount,
        tdo.back_amount                as backAmount,
        tdo.goods_amount               as goodsAmount,
        tdo.entrust_type               as entrustType,
        tdo.settlement_tonnage         as settlementTonnage,
        tdo.carrier_settlement         as carrierSettlement,
        tdo.contract_price_type        as contractPriceType,
        tdo.contract_price             as contractPrice,
        tdo.expect_contract_price_type as expectContractPriceType,
        tdo.expect_contract_price      as expectContractPrice,
        tdo.carrier_price_type         as carrierPriceType,
        tdo.carrier_price              as carrierPrice,
        tdo.goods_unit                 as goodsUnit,
        tdo.company_entrust_id         as companyEntrustId,
        tdo.company_entrust_name       as entrustCompany,
        tdo.remark                     as remark,
        tdo.order_type,
        tdo.upstream_customer          as upstreamCustomer,
        tdo.company_carrier_id         as companyCarrierId,
        tdo.fixed_demand               as fixedDemand,
        tdo.auto_publish               as autoPublish,
        tdo.project_label              as projectLabel,

        tdoa.load_province_id          as loadProvinceId,
        tdoa.load_city_id              as loadCityId,
        tdoa.load_area_id              as loadAreaId,
        tdoa.unload_province_id        as unloadProvinceId,
        tdoa.unload_city_id            as unloadCityId,
        tdoa.unload_area_id            as unloadAreaId,
        tdoa.load_province_name        as loadProvinceName,
        tdoa.load_city_name            as loadCityName,
        tdoa.load_area_name            as loadAreaName,
        tdoa.load_detail_address       as loadDetailAddress,
        tdoa.unload_province_name      as unloadProvinceName,
        tdoa.unload_city_name          as unloadCityName,
        tdoa.unload_area_name          as unloadAreaName,
        tdoa.unload_detail_address     as unloadDetailAddress,
        tdoa.consignor_name            as consignorName,
        tdoa.consignor_mobile          as consignorMobile,
        tdoa.receiver_name             as receiverName,
        tdoa.receiver_mobile           as receiverMobile,

        tdog.id                        as goodsId,
        tdog.goods_name                as goodsName,
        tdog.goods_amount              as goodsAmount2,
        tdog.length,
        tdog.width
        from t_demand_order tdo
        left join t_demand_order_address tdoa on tdoa.demand_order_id = tdo.id and tdoa.valid = 1
        left join t_demand_order_goods tdog on tdog.demand_order_id = tdo.id and tdog.valid = 1
        where tdo.valid = 1
        and tdo.id in (${ids})
    </select>

    <resultMap type="com.logistics.tms.controller.demandorder.response.DemandOrderResponseModel" id="getSearchListManageResult">
        <id column="id" property="demandId" jdbcType="BIGINT"/>
        <result column="entrust_status" property="status" jdbcType="INTEGER"/>
        <result column="useStatus" property="useStatus" jdbcType="INTEGER"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="publish_name" property="publishName" jdbcType="VARCHAR"/>
        <result column="publish_time" property="publishTime" jdbcType="TIMESTAMP"/>
        <result column="ticket_time" property="ticketTime" jdbcType="TIMESTAMP"/>
        <result column="goods_amount" property="goodsAmount" jdbcType="DECIMAL"/>
        <result column="arranged_amount" property="arrangedAmount" jdbcType="DECIMAL"/>
        <result column="not_arranged_amount" property="notArrangedAmount" jdbcType="DECIMAL"/>
        <result column="entrust_type" property="entrustType" jdbcType="INTEGER"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="contract_price_type" property="contractPriceType" jdbcType="INTEGER"/>
        <result column="contract_price" property="contractPrice" jdbcType="DECIMAL"/>
        <result column="expect_contract_price_type" property="exceptContractPriceType" jdbcType="INTEGER"/>
        <result column="expect_contract_price" property="exceptContractPrice" jdbcType="DECIMAL"/>
        <result column="source" property="source" jdbcType="INTEGER"/>
        <result column="back_amount" property="backAmount" jdbcType="DECIMAL"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="cancel_reason" property="cancelReason" jdbcType="VARCHAR"/>
        <result column="load_province_id" property="loadProvinceId" jdbcType="BIGINT"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_id" property="loadCityId" jdbcType="BIGINT"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_id" property="loadAreaId" jdbcType="BIGINT"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="consignor_name" property="consignorName" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR"/>
        <result column="expected_load_time" property="expectedLoadTime" jdbcType="VARCHAR"/>
        <result column="unload_province_id" property="unloadProvinceId" jdbcType="BIGINT"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_id" property="unloadCityId" jdbcType="BIGINT"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_id" property="unloadAreaId" jdbcType="BIGINT"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR"/>
        <result column="expected_unload_time" property="expectedUnloadTime" jdbcType="TIMESTAMP"/>
        <result column="company_entrust_id" property="companyEntrustId" jdbcType="BIGINT"/>
        <result column="company_entrust_name" property="companyEntrustName" jdbcType="VARCHAR"/>
        <result column="upstream_customer" property="upstreamCustomer" jdbcType="VARCHAR"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="company_carrier_type" property="companyCarrierType" jdbcType="INTEGER"/>
        <result column="company_carrier_name" property="companyCarrierName" jdbcType="VARCHAR"/>
        <result column="actualEntrustFee" property="actualEntrustFee" jdbcType="DECIMAL"/>
        <result column="priceType" property="priceType" jdbcType="INTEGER"/>
        <result column="if_urgent" property="ifUrgent" jdbcType="INTEGER"/>
        <result column="order_type" property="orderType" jdbcType="INTEGER"/>
        <result column="sinopec_order_no" property="sinopecOrderNo" jdbcType="VARCHAR"/>
        <result column="manufacturer_name" property="manufacturerName" jdbcType="VARCHAR"/>
        <result column="item_trans_group_name" property="itemTransGroupName" jdbcType="VARCHAR"/>
        <result column="item_pack_spec_name" property="itemPackSpecName" jdbcType="VARCHAR"/>
        <result column="if_objection" property="ifObjection" jdbcType="INTEGER"/>
        <result column="if_objection_sinopec" property="ifObjectionSinopec" jdbcType="INTEGER"/>
        <result column="dispatcher_name" property="dispatcherName" jdbcType="VARCHAR"/>
        <result column="dispatcher_phone" property="dispatcherPhone" jdbcType="VARCHAR"/>
        <result column="carrier_contact_id" property="carrierContactId" jdbcType="BIGINT"/>
        <result column="carrier_contact_name" property="carrierContactName" jdbcType="VARCHAR"/>
        <result column="carrier_contact_phone" property="carrierContactMobile" jdbcType="VARCHAR"/>
        <result column="sinopec_online_goods_flag" property="sinopecOnlineGoodsFlag" jdbcType="INTEGER"/>
        <result column="settlement_tonnage" property="settlementTonnage" jdbcType="INTEGER"/>

        <collection property="goodsResponseModels" column="demandId" ofType="com.logistics.tms.controller.demandorder.response.DemandOrderGoodsResponseModel" javaType="ArrayList">
            <id column="demandOrderGoodsId" property="demandOrderGoodsId" jdbcType="BIGINT"/>
            <result column="demandOrderId" property="demandOrderId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goodsAmountNumber" property="goodsAmountNumber" jdbcType="DECIMAL"/>
            <result column="arrangedAmountNumber" property="arrangedAmountNumber" jdbcType="DECIMAL"/>
            <result column="notArrangedAmountNumber" property="notArrangedAmountNumber" jdbcType="DECIMAL"/>
            <result column="backAmountNumber" property="backAmountNumber" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>

    <select id="searchListManageAddressGoodsDemand" resultMap="getSearchListManageResult">
        SELECT
        tdo.id,
        tdo.entrust_status,
        tdo.status as useStatus,
        tdo.if_cancel,
        tdo.if_empty,
        tdo.cancel_reason,
        tdo.demand_order_code,
        tdo.customer_order_code,
        tdo.publish_name,
        tdo.publish_time,
        tdo.ticket_time,
        tdo.goods_amount,
        tdo.arranged_amount,
        tdo.not_arranged_amount,
        tdo.remark,
        tdo.entrust_type,
        tdo.back_amount,
        tdo.goods_unit,
        tdo.source,
        tdo.contract_price_type,
        tdo.contract_price,
        tdo.expect_contract_price_type,
        tdo.expect_contract_price,
        tdo.company_entrust_name,
        tdo.upstream_customer,
        tdo.company_carrier_name,
        tdo.if_urgent,
        tdo.order_type,
        tdo.sinopec_order_no,
        tdo.manufacturer_name,
        tdo.item_trans_group_name,
        tdo.item_pack_spec_name,
        tdo.if_objection,
        tdo.if_objection_sinopec,
        tdo.dispatcher_name,
        tdo.dispatcher_phone,
        tdo.company_carrier_type,
        tdo.company_carrier_id,
        tdo.company_carrier_name,
        tdo.carrier_contact_id,
        tdo.carrier_contact_name,
        AES_DECRYPT(UNHEX(tdo.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrier_contact_phone,
        tdo.sinopec_online_goods_flag,
        tdo.settlement_tonnage,

        tdoa.load_province_id,
        tdoa.load_province_name,
        tdoa.load_city_id,
        tdoa.load_city_name,
        tdoa.load_area_id,
        tdoa.load_area_name,
        tdoa.load_detail_address,
        tdoa.load_warehouse,
        tdoa.consignor_name,
        tdoa.consignor_mobile,
        tdoa.expected_load_time,
        tdoa.unload_province_id,
        tdoa.unload_province_name,
        tdoa.unload_city_id,
        tdoa.unload_city_name,
        tdoa.unload_area_id,
        tdoa.unload_area_name,
        tdoa.unload_detail_address,
        tdoa.unload_warehouse,
        tdoa.receiver_name,
        tdoa.receiver_mobile,
        tdoa.expected_unload_time,

        tdog.id as demandOrderGoodsId,
        tdog.demand_order_id as demandOrderId,
        tdog.goods_name,
        tdog.length,
        tdog.width,
        tdog.height,
        tdog.goods_size,
        tdog.goods_amount as goodsAmountNumber,
        tdog.arranged_amount as arrangedAmountNumber,
        tdog.not_arranged_amount as notArrangedAmountNumber,
        tdog.back_amount as backAmountNumber
        FROM t_demand_order tdo
        LEFT JOIN t_demand_order_goods tdog ON tdo.id = tdog.demand_order_id and tdog.valid=1
        LEFT JOIN t_demand_order_address tdoa ON tdo.id = tdoa.demand_order_id and tdoa.valid=1
        WHERE tdo.valid = 1
        <if test="demandOrderIds!=null and demandOrderIds!=''">
            and tdo.id in (${demandOrderIds})
        </if>
        <if test="params.sort!=null and params.sort!=''">
            order by ${params.sort} ${params.order},tdo.id desc
        </if>
        <if test="params.sort==null">
            order by tdo.id desc
        </if>
    </select>

    <select id="searchListStatistics" resultType="com.logistics.tms.controller.demandorder.response.SearchListStatisticsResponseModel">
        SELECT
        ifnull(count(0),0) as allcount,
        ifnull(sum(if(tdo.if_cancel = 0 and tdo.if_empty = 0 and tdo.if_rollback = 0 and tdo.entrust_status = 500,1,0)),0) as waitPublishCount,
        ifnull(sum(if(tdo.if_cancel = 0 and tdo.if_empty = 0 and tdo.if_rollback = 0 and tdo.entrust_status = 1000,1,0)),0) as waitDispatchCount,
        ifnull(sum(if(tdo.if_cancel = 0 and tdo.if_empty = 0 and tdo.if_rollback = 0 and tdo.entrust_status = 2000,1,0)),0) as partDispatchCount,
        ifnull(sum(if(tdo.if_cancel = 0 and tdo.if_empty = 0 and tdo.if_rollback = 0 and tdo.entrust_status = 3000,1,0)),0) as completeDispatchCount,
        ifnull(sum(if(tdo.if_cancel = 0 and tdo.if_empty = 0 and tdo.if_rollback = 0 and tdo.entrust_status = 4000,1,0)),0) as waitSignedAccount,
        ifnull(sum(if(tdo.if_cancel = 0 and tdo.if_empty = 0 and tdo.if_rollback = 0 and tdo.entrust_status = 5000,1,0)),0) as signedAccount,
        ifnull(sum(if(tdo.if_cancel=1,1,0)),0) as cancelCount
        from (
        select tdo.if_cancel,tdo.if_empty,tdo.if_rollback,tdo.entrust_status
        FROM t_demand_order tdo
        LEFT JOIN t_demand_order_address tdoa ON tdo.id = tdoa.demand_order_id and tdoa.valid=1
        WHERE tdo.valid = 1 and source in (2,3,4)
        <if test="params.companyEntrustIds!=null and params.companyEntrustIds!=''">
            and tdo.company_entrust_id in (${params.companyEntrustIds})
        </if>
        <if test="params.companyCarrierIds!=null and params.companyCarrierIds!=''">
            and tdo.company_carrier_id in (${params.companyCarrierIds})
        </if>
        <if test="params.demandOrderCode!=null and params.demandOrderCode!=''">
            and instr(tdo.demand_order_code,#{params.demandOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="params.customerOrderCode!=null and params.customerOrderCode!=''">
            and (instr(tdo.customer_order_code,#{params.customerOrderCode,jdbcType=VARCHAR}) or instr(tdo.sinopec_order_no,#{params.customerOrderCode,jdbcType=VARCHAR}))
        </if>
        <if test="params.expectedLoadTimeStart!=null and params.expectedLoadTimeStart!=''">
            and tdoa.expected_load_time &gt;= DATE_FORMAT(#{params.expectedLoadTimeStart,jdbcType=VARCHAR},'%Y-%m-%d
            %k:%i:%S')
        </if>
        <if test="params.expectedLoadTimeEnd!=null and params.expectedLoadTimeEnd!=''">
            and tdoa.expected_load_time &lt;= DATE_FORMAT(#{params.expectedLoadTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d
            23:59:59')
        </if>
        <if test="params.expectedUnloadTimeStart!=null and params.expectedUnloadTimeStart!=''">
            and tdoa.expected_unload_time &gt;= DATE_FORMAT(#{params.expectedUnloadTimeStart,jdbcType=VARCHAR},'%Y-%m-%d
            %k:%i:%S')
        </if>
        <if test="params.expectedUnloadTimeEnd!=null and params.expectedUnloadTimeEnd!=''">
            and tdoa.expected_unload_time &lt;= DATE_FORMAT(#{params.expectedUnloadTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d
            23:59:59')
        </if>
        <if test="params.publishTimeStart!=null and params.publishTimeStart!=''">
            and tdo.publish_time &gt;= DATE_FORMAT(#{params.publishTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %k:%i:%S')
        </if>
        <if test="params.publishTimeEnd!=null and params.publishTimeEnd!=''">
            and tdo.publish_time &lt;= DATE_FORMAT(#{params.publishTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.ticketDateFrom!=null and params.ticketDateFrom!=''">
            and tdo.ticket_time &gt;= DATE_FORMAT(#{params.ticketDateFrom,jdbcType=VARCHAR},'%Y-%m-%d %k:%i:%S')
        </if>
        <if test="params.ticketDateTo!=null and params.ticketDateTo!=''">
            and tdo.ticket_time &lt;= DATE_FORMAT(#{params.ticketDateTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.loadDetailAddress!=null and params.loadDetailAddress!=''">
            and instr(CONCAT(tdoa.load_province_name,tdoa.load_city_name,tdoa.load_area_name,tdoa.load_detail_address,tdoa.load_warehouse),#{params.loadDetailAddress,jdbcType=VARCHAR})
        </if>
        <if test="params.consignorMobile!=null and params.consignorMobile!=''">
            and (instr(tdoa.consignor_name,#{params.consignorMobile,jdbcType=VARCHAR}) or
            instr(tdoa.consignor_mobile,#{params.consignorMobile,jdbcType=VARCHAR}))
        </if>
        <if test="params.unloadDetailAddress!=null and params.unloadDetailAddress!=''">
            and instr(CONCAT(tdoa.unload_province_name,tdoa.unload_city_name,tdoa.unload_area_name,tdoa.unload_detail_address,tdoa.unload_warehouse),#{params.unloadDetailAddress,jdbcType=VARCHAR})
        </if>
        <if test="params.receiverName!=null and params.receiverName!=''">
            and (instr(tdoa.receiver_name,#{params.receiverName,jdbcType=VARCHAR}) or
            instr(tdoa.receiver_mobile,#{params.receiverName,jdbcType=VARCHAR}))
        </if>
        <if test="params.consignorMobileAndReceiver != null and params.consignorMobileAndReceiver != ''">
            and (instr(tdoa.consignor_name,#{params.consignorMobileAndReceiver,jdbcType=VARCHAR}) > 0
            or instr(tdoa.consignor_mobile,#{params.consignorMobileAndReceiver,jdbcType=VARCHAR}) > 0
            or instr(tdoa.receiver_name,#{params.consignorMobileAndReceiver,jdbcType=VARCHAR}) > 0
            or instr(tdoa.receiver_mobile,#{params.consignorMobileAndReceiver,jdbcType=VARCHAR}) > 0)
        </if>
        <if test="params.customerEntrustName!=null and params.customerEntrustName!=''">
            and instr(tdo.publish_name,#{params.customerEntrustName,jdbcType=VARCHAR})
        </if>
        <if test="params.entrustType!=null and params.entrustType!=''">
            and tdo.entrust_type=#{params.entrustType}
        </if>
        <if test="params.demandOrderCodeList != null and params.demandOrderCodeList.size > 0">
            and tdo.demand_order_code in
            <foreach collection="params.demandOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.customerOrderCodeList != null and params.customerOrderCodeList.size > 0">
            and tdo.customer_order_code in
            <foreach collection="params.customerOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.showIfCancelStatus !=null and params.showIfCancelStatus!='' and params.showIfCancelStatus==1">
            and tdo.if_cancel=0
        </if>
        <if test="params.ifObjection !=null and params.ifObjection!='' and params.ifObjection==1">
            and (tdo.if_objection = 1 or tdo.if_objection_sinopec = 1)
        </if>
        <if test="params.orderType != null and params.orderType != ''">
            and tdo.order_type = #{params.orderType}
        </if>
        <if test="params.companyCarrierName!=null and params.companyCarrierName!=''">
            and ((tdo.company_carrier_type = 1 and instr(tdo.company_carrier_name,#{params.companyCarrierName,jdbcType=VARCHAR}))
            or (tdo.company_carrier_type = 2 and (instr(tdo.carrier_contact_name,#{params.companyCarrierName,jdbcType=VARCHAR}) or instr(AES_DECRYPT(UNHEX(tdo.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') ,#{params.companyCarrierName,jdbcType=VARCHAR}))))
        </if>
        group by tdo.id
        ) tdo
    </select>

    <select id="searchListForLeYiManageAddressGoodsDemand" resultMap="searchListForLeYiManageReceiveEntrustCarrierMap">
        SELECT
        tdo.id,
        tdo.entrust_status,
        tdo.status as useStatus,
        tdo.if_cancel,
        tdo.if_empty,
        tdo.if_rollback,
        tdo.cancel_reason,
        tdo.demand_order_code,
        tdo.customer_order_code,
        tdo.publish_name,
        tdo.publish_time,
        tdo.publish_org_name,
        tdo.goods_amount,
        tdo.arranged_amount,
        tdo.not_arranged_amount,
        tdo.remark,
        tdo.entrust_type,
        tdo.back_amount,
        tdo.difference_amount,
        tdo.abnormal_amount,
        tdo.goods_unit,
        tdo.source,
        tdo.contract_price_type,
        tdo.contract_price,
        tdo.expect_contract_price_type,
        tdo.expect_contract_price,
        tdo.company_entrust_id,
        tdo.company_entrust_name,
        tdo.upstream_customer,
        tdo.company_carrier_id,
        tdo.company_carrier_type,
        tdo.company_carrier_name,
        tdo.carrier_contact_name,
        AES_DECRYPT(UNHEX(tdo.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrier_contact_phone,
        tdo.if_urgent,
        tdo.dispatch_validity,
        tdo.if_overdue,
        tdo.available_on_weekends,
        tdo.loading_unloading_part,
        tdo.loading_unloading_charge,
        tdo.recycle_task_type,
        tdo.settlement_tonnage,
        tdo.rollback_cause_type,
        tdo.rollback_cause_type_two,
        tdo.rollback_remark,
        tdo.if_ext_demand_order,

        tdoa.load_province_id,
        tdoa.load_province_name,
        tdoa.load_city_id,
        tdoa.load_city_name,
        tdoa.load_area_id,
        tdoa.load_area_name,
        tdoa.load_detail_address,
        tdoa.load_warehouse,
        tdoa.consignor_name,
        tdoa.consignor_mobile,
        tdoa.expected_load_time,
        tdoa.load_region_name,
        tdoa.load_region_contact_name,
        tdoa.load_region_contact_phone,
        tdoa.unload_province_id,
        tdoa.unload_province_name,
        tdoa.unload_city_id,
        tdoa.unload_city_name,
        tdoa.unload_area_id,
        tdoa.unload_area_name,
        tdoa.unload_detail_address,
        tdoa.unload_warehouse,
        tdoa.receiver_name,
        tdoa.receiver_mobile,
        tdoa.expected_unload_time,

        tdog.id as demandOrderGoodsId,
        tdog.demand_order_id as demandOrderId,
        tdog.goods_name,
        tdog.length,
        tdog.width,
        tdog.height,
        tdog.goods_size,
        tdog.goods_amount as goodsAmountNumber,
        tdog.arranged_amount as arrangedAmountNumber,
        tdog.not_arranged_amount as notArrangedAmountNumber,
        tdog.back_amount as backAmountNumber
        FROM t_demand_order tdo
        LEFT JOIN t_demand_order_goods tdog ON tdo.id = tdog.demand_order_id and tdog.valid=1
        LEFT JOIN t_demand_order_address tdoa ON tdo.id = tdoa.demand_order_id and tdoa.valid=1
        WHERE tdo.valid = 1
        <if test="demandOrderIds!=null and demandOrderIds!=''">
            and tdo.id in (${demandOrderIds})
        </if>
        <if test="params.sort!=null and params.sort!=''">
            order by ${params.sort} ${params.order},tdo.id desc
        </if>
        <if test="params.sort==null">
            order by tdo.id desc
        </if>
    </select>

    <resultMap type="com.logistics.tms.controller.demandorder.response.DemandOrderForLeYiResponseModel" id="searchListForLeYiManageReceiveEntrustCarrierMap">
        <id column="id" property="demandId" jdbcType="BIGINT"/>
        <result column="entrust_status" property="status" jdbcType="INTEGER"/>
        <result column="useStatus" property="useStatus" jdbcType="INTEGER"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="if_rollback" property="ifRollback" jdbcType="INTEGER"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="publish_name" property="publishName" jdbcType="VARCHAR"/>
        <result column="publish_time" property="publishTime" jdbcType="TIMESTAMP"/>
        <result column="publish_org_name" property="publishOrgName" jdbcType="VARCHAR"/>
        <result column="goods_amount" property="goodsAmount" jdbcType="DECIMAL"/>
        <result column="arranged_amount" property="arrangedAmount" jdbcType="DECIMAL"/>
        <result column="not_arranged_amount" property="notArrangedAmount" jdbcType="DECIMAL"/>
        <result column="entrust_type" property="entrustType" jdbcType="INTEGER"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="contract_price_type" property="contractPriceType" jdbcType="INTEGER"/>
        <result column="contract_price" property="contractPrice" jdbcType="DECIMAL"/>
        <result column="expect_contract_price_type" property="exceptContractPriceType" jdbcType="INTEGER"/>
        <result column="expect_contract_price" property="exceptContractPrice" jdbcType="DECIMAL"/>
        <result column="source" property="source" jdbcType="INTEGER"/>
        <result column="back_amount" property="backAmount" jdbcType="DECIMAL"/>
        <result column="difference_amount" property="differenceAmount" jdbcType="DECIMAL"/>
        <result column="abnormal_amount" property="abnormalAmount" jdbcType="DECIMAL"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="cancel_reason" property="cancelReason" jdbcType="VARCHAR"/>
        <result column="load_province_id" property="loadProvinceId" jdbcType="BIGINT"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_id" property="loadCityId" jdbcType="BIGINT"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_id" property="loadAreaId" jdbcType="BIGINT"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="consignor_name" property="consignorName" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR"/>
        <result column="expected_load_time" property="expectedLoadTime" jdbcType="TIMESTAMP"/>
        <result column="load_region_name" property="loadRegionName" jdbcType="VARCHAR"/>
        <result column="load_region_contact_name" property="loadRegionContactName" jdbcType="VARCHAR"/>
        <result column="load_region_contact_phone" property="loadRegionContactPhone" jdbcType="VARCHAR"/>
        <result column="unload_province_id" property="unloadProvinceId" jdbcType="BIGINT"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_id" property="unloadCityId" jdbcType="BIGINT"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_id" property="unloadAreaId" jdbcType="BIGINT"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR"/>
        <result column="expected_unload_time" property="expectedUnloadTime" jdbcType="TIMESTAMP"/>
        <result column="company_entrust_id" property="companyEntrustId" jdbcType="BIGINT"/>
        <result column="company_entrust_name" property="companyEntrustName" jdbcType="VARCHAR"/>
        <result column="upstream_customer" property="upstreamCustomer" jdbcType="VARCHAR"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="company_carrier_type" property="companyCarrierType" jdbcType="INTEGER"/>
        <result column="company_carrier_name" property="companyCarrierName" jdbcType="VARCHAR"/>
        <result column="carrier_contact_name" property="carrierContactName" jdbcType="VARCHAR"/>
        <result column="carrier_contact_phone" property="carrierContactPhone" jdbcType="VARCHAR"/>
        <result column="if_urgent" property="ifUrgent" jdbcType="INTEGER"/>
        <result column="dispatch_validity" property="dispatchValidity" jdbcType="INTEGER"/>
        <result column="if_overdue" property="ifOverdue" jdbcType="INTEGER"/>
        <result column="available_on_weekends" property="availableOnWeekends" jdbcType="INTEGER"/>
        <result column="loading_unloading_part" property="loadingUnloadingPart" jdbcType="INTEGER"/>
        <result column="loading_unloading_charge" property="loadingUnloadingCharge" jdbcType="DECIMAL"/>
        <result column="recycle_task_type" property="recycleTaskType" jdbcType="INTEGER"/>
        <result column="settlement_tonnage" property="settlementTonnage" jdbcType="INTEGER"/>
        <result column="rollback_cause_type" property="rollbackCauseType" jdbcType="INTEGER"/>
        <result column="rollback_cause_type_two" property="rollbackCauseTypeTwo" jdbcType="INTEGER"/>
        <result column="rollback_remark" property="rollbackRemark" jdbcType="VARCHAR"/>
        <result column="if_ext_demand_order" property="ifExtDemandOrder" jdbcType="INTEGER"/>

        <collection property="goodsResponseModels" column="demandId" ofType="com.logistics.tms.controller.demandorder.response.DemandOrderGoodsResponseModel" javaType="ArrayList">
            <id column="demandOrderGoodsId" property="demandOrderGoodsId" jdbcType="BIGINT"/>
            <result column="demandOrderId" property="demandOrderId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goodsAmountNumber" property="goodsAmountNumber" jdbcType="DECIMAL"/>
            <result column="arrangedAmountNumber" property="arrangedAmountNumber" jdbcType="DECIMAL"/>
            <result column="notArrangedAmountNumber" property="notArrangedAmountNumber" jdbcType="DECIMAL"/>
            <result column="backAmountNumber" property="backAmountNumber" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>

    <select id="searchListManageIds" resultType="java.lang.Long" parameterType="com.logistics.tms.controller.demandorder.request.DemandOrderSearchRequestModel">
        SELECT DISTINCT
        tdo.id
        FROM t_demand_order tdo
        LEFT JOIN t_demand_order_address tdoa ON tdo.id = tdoa.demand_order_id and tdoa.valid=1
        WHERE tdo.valid = 1 and tdo.source in (2,3,4)
        <if test="params.demandIds!=null and params.demandIds!=''">
            and tdo.id in (${params.demandIds})
        </if>
        <if test="params.demandStatus!=null and params.demandStatus!=''">
            <choose>
                <!-- 已取消-->
                <when test="params.demandStatus==1">
                    and tdo.if_cancel=1
                </when>
                <otherwise>
                    and tdo.entrust_status =#{params.demandStatus} and tdo.if_cancel=0 and tdo.if_empty=0 and tdo.if_rollback = 0
                </otherwise>
            </choose>
        </if>
        <if test="params.companyEntrustIds!=null and params.companyEntrustIds!=''">
            and tdo.company_entrust_id in (${params.companyEntrustIds})
        </if>
        <if test="params.companyCarrierIds!=null and params.companyCarrierIds!=''">
            and tdo.company_carrier_id in (${params.companyCarrierIds})
        </if>
        <if test="params.demandOrderCode!=null and params.demandOrderCode!=''">
            and instr(tdo.demand_order_code,#{params.demandOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="params.customerOrderCode!=null and params.customerOrderCode!=''">
            and (instr(tdo.customer_order_code,#{params.customerOrderCode,jdbcType=VARCHAR}) or instr(tdo.sinopec_order_no,#{params.customerOrderCode,jdbcType=VARCHAR}))
        </if>
        <if test="params.expectedLoadTimeStart!=null and params.expectedLoadTimeStart!=''">
            and tdoa.expected_load_time &gt;= DATE_FORMAT(#{params.expectedLoadTimeStart,jdbcType=VARCHAR},'%Y-%m-%d
            %k:%i:%S')
        </if>
        <if test="params.expectedLoadTimeEnd!=null and params.expectedLoadTimeEnd!=''">
            and tdoa.expected_load_time &lt;= DATE_FORMAT(#{params.expectedLoadTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d
            23:59:59')
        </if>
        <if test="params.expectedUnloadTimeStart!=null and params.expectedUnloadTimeStart!=''">
            and tdoa.expected_unload_time &gt;= DATE_FORMAT(#{params.expectedUnloadTimeStart,jdbcType=VARCHAR},'%Y-%m-%d
            %k:%i:%S')
        </if>
        <if test="params.expectedUnloadTimeEnd!=null and params.expectedUnloadTimeEnd!=''">
            and tdoa.expected_unload_time &lt;= DATE_FORMAT(#{params.expectedUnloadTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d
            23:59:59')
        </if>
        <if test="params.publishTimeStart!=null and params.publishTimeStart!=''">
            and tdo.publish_time &gt;= DATE_FORMAT(#{params.publishTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %k:%i:%S')
        </if>
        <if test="params.publishTimeEnd!=null and params.publishTimeEnd!=''">
            and tdo.publish_time &lt;= DATE_FORMAT(#{params.publishTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.ticketDateFrom!=null and params.ticketDateFrom!=''">
            and tdo.ticket_time &gt;= DATE_FORMAT(#{params.ticketDateFrom,jdbcType=VARCHAR},'%Y-%m-%d %k:%i:%S')
        </if>
        <if test="params.ticketDateTo!=null and params.ticketDateTo!=''">
            and tdo.ticket_time &lt;= DATE_FORMAT(#{params.ticketDateTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.loadDetailAddress!=null and params.loadDetailAddress!=''">
            and instr(CONCAT(tdoa.load_province_name,tdoa.load_city_name,tdoa.load_area_name,tdoa.load_detail_address,tdoa.load_warehouse),#{params.loadDetailAddress,jdbcType=VARCHAR})
        </if>
        <if test="params.consignorMobile!=null and params.consignorMobile!=''">
            and (instr(tdoa.consignor_name,#{params.consignorMobile,jdbcType=VARCHAR}) or
            instr(tdoa.consignor_mobile,#{params.consignorMobile,jdbcType=VARCHAR}))
        </if>
        <if test="params.unloadDetailAddress!=null and params.unloadDetailAddress!=''">
            and instr(CONCAT(tdoa.unload_province_name,tdoa.unload_city_name,tdoa.unload_area_name,tdoa.unload_detail_address,tdoa.unload_warehouse),#{params.unloadDetailAddress,jdbcType=VARCHAR})
        </if>
        <if test="params.receiverName!=null and params.receiverName!=''">
            and (instr(tdoa.receiver_name,#{params.receiverName,jdbcType=VARCHAR}) or
            instr(tdoa.receiver_mobile,#{params.receiverName,jdbcType=VARCHAR}))
        </if>
        <if test="params.consignorMobileAndReceiver != null and params.consignorMobileAndReceiver != ''">
            and (instr(tdoa.consignor_name,#{params.consignorMobileAndReceiver,jdbcType=VARCHAR}) > 0
            or instr(tdoa.consignor_mobile,#{params.consignorMobileAndReceiver,jdbcType=VARCHAR}) > 0
            or instr(tdoa.receiver_name,#{params.consignorMobileAndReceiver,jdbcType=VARCHAR}) > 0
            or instr(tdoa.receiver_mobile,#{params.consignorMobileAndReceiver,jdbcType=VARCHAR}) > 0)
        </if>
        <if test="params.customerEntrustName!=null and params.customerEntrustName!=''">
            and instr(tdo.publish_name,#{params.customerEntrustName,jdbcType=VARCHAR})
        </if>
        <if test="params.entrustType!=null and params.entrustType!=''">
            and tdo.entrust_type=#{params.entrustType}
        </if>
        <if test="params.showIfCancelStatus !=null and params.showIfCancelStatus!='' and params.showIfCancelStatus==1">
            and tdo.if_cancel=0
        </if>
        <if test="params.demandOrderCodeList != null and params.demandOrderCodeList.size > 0">
            and tdo.demand_order_code in
            <foreach collection="params.demandOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.customerOrderCodeList != null and params.customerOrderCodeList.size > 0">
            and tdo.customer_order_code in
            <foreach collection="params.customerOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.ifObjection !=null and params.ifObjection!='' and params.ifObjection==1">
            and (tdo.if_objection = 1 or tdo.if_objection_sinopec = 1)
        </if>
        <if test="params.orderType != null and params.orderType != ''">
            and tdo.order_type = #{params.orderType}
        </if>
        <if test="params.companyCarrierName!=null and params.companyCarrierName!=''">
            and ((tdo.company_carrier_type = 1 and instr(tdo.company_carrier_name,#{params.companyCarrierName,jdbcType=VARCHAR}))
            or (tdo.company_carrier_type = 2 and (instr(tdo.carrier_contact_name,#{params.companyCarrierName,jdbcType=VARCHAR}) or instr(AES_DECRYPT(UNHEX(tdo.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') ,#{params.companyCarrierName,jdbcType=VARCHAR}))))
        </if>
        <if test="params.sort!=null and params.sort!=''">
            order by ${params.sort} ${params.order},tdo.id desc
        </if>
        <if test="params.sort==null">
            order by tdo.id desc
        </if>
    </select>

    <select id="searchListForLeYiManageIds" resultType="java.lang.Long" parameterType="com.logistics.tms.controller.demandorder.request.DemandOrderSearchForLeYiRequestModel">
        SELECT DISTINCT tdo.id
        FROM t_demand_order tdo
        LEFT JOIN t_demand_order_address tdoa ON tdo.id = tdoa.demand_order_id and tdoa.valid = 1
        LEFT JOIN t_demand_order_goods tdog ON tdo.id = tdog.demand_order_id and tdog.valid = 1
        WHERE tdo.valid = 1
          and tdo.source = 1
        <if test="params.demandIds != null and params.demandIds != ''">
            and tdo.id in (${params.demandIds})
        </if>
        <if test="params.demandStatusList != null and params.demandStatusList.size > 0">
            and
            <foreach collection="params.demandStatusList" item="demandStatus" separator="or" open="(" close=")">
                <choose>
                    <when test="demandStatus == 1">
                        tdo.if_cancel = 1
                    </when>
                    <when test="demandStatus == 2">
                        tdo.if_empty = 1
                    </when>
                    <when test="demandStatus == 3">
                        (tdo.if_rollback = 1
                        and tdo.if_cancel = 0)
                    </when>
                    <otherwise>
                        (tdo.entrust_status = #{demandStatus}
                        and tdo.if_cancel = 0
                        and tdo.if_empty = 0
                        and tdo.if_rollback = 0)
                    </otherwise>
                </choose>
            </foreach>
        </if>
        <if test="params.demandStatus != null and params.demandStatus != ''">
            <choose>
                <when test="params.demandStatus == 1">
                    and tdo.if_cancel = 1
                </when>
                <when test="params.demandStatus == 2">
                    and tdo.if_empty = 1
                </when>
                <when test="params.demandStatus == 3">
                    and tdo.if_rollback = 1
                    and tdo.if_cancel = 0
                </when>
                <otherwise>
                    and tdo.entrust_status = #{params.demandStatus}
                    and tdo.if_cancel = 0
                    and tdo.if_empty = 0
                    and tdo.if_rollback = 0
                </otherwise>
            </choose>
        </if>
        <if test="params.companyEntrustIds != null and params.companyEntrustIds != ''">
            and tdo.company_entrust_id in (${params.companyEntrustIds})
        </if>
        <if test="params.demandOrderCode != null and params.demandOrderCode != ''">
            and instr(tdo.demand_order_code, #{params.demandOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="params.customerOrderCode != null and params.customerOrderCode != ''">
            and instr(tdo.customer_order_code, #{params.customerOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="params.publishTimeStart != null and params.publishTimeStart != ''">
            and tdo.publish_time &gt;= DATE_FORMAT(#{params.publishTimeStart,jdbcType=VARCHAR}, '%Y-%m-%d %k:%i:%S')
        </if>
        <if test="params.publishTimeEnd != null and params.publishTimeEnd != ''">
            and tdo.publish_time &lt;= DATE_FORMAT(#{params.publishTimeEnd,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="params.loadDetailAddress != null and params.loadDetailAddress != ''">
            and instr(CONCAT(tdoa.load_province_name, tdoa.load_city_name, tdoa.load_area_name, tdoa.load_detail_address), #{params.loadDetailAddress,jdbcType=VARCHAR})
        </if>
        <if test="params.consignorMobile != null and params.consignorMobile != ''">
            and (instr(tdoa.consignor_name, #{params.consignorMobile,jdbcType=VARCHAR}) or
                 instr(tdoa.consignor_mobile, #{params.consignorMobile,jdbcType=VARCHAR}))
        </if>
        <if test="params.unloadDetailAddress != null and params.unloadDetailAddress != ''">
            and instr(CONCAT(tdoa.unload_province_name, tdoa.unload_city_name, tdoa.unload_area_name, tdoa.unload_detail_address), #{params.unloadDetailAddress,jdbcType=VARCHAR})
        </if>
        <if test="params.receiverName != null and params.receiverName != ''">
            and (instr(tdoa.receiver_name, #{params.receiverName,jdbcType=VARCHAR}) or
                 instr(tdoa.receiver_mobile, #{params.receiverName,jdbcType=VARCHAR}))
        </if>
        <if test="params.loadWarehouse != null and params.loadWarehouse != ''">
            and instr(tdoa.load_warehouse, #{params.loadWarehouse,jdbcType=VARCHAR})
        </if>
        <if test="params.unloadWarehouse != null and params.unloadWarehouse != ''">
            and instr(tdoa.unload_warehouse, #{params.unloadWarehouse,jdbcType=VARCHAR})
        </if>
        <if test="params.entrustType != null and params.entrustType != ''">
            and tdo.entrust_type = #{params.entrustType}
        </if>
        <if test="params.showIfCancelStatus != null and params.showIfCancelStatus != '' and params.showIfCancelStatus == 1">
            and tdo.if_cancel = 0
        </if>
        <if test="params.demandOrderCodeList != null and params.demandOrderCodeList.size > 0">
            and tdo.demand_order_code in
            <foreach collection="params.demandOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.customerOrderCodeList != null and params.customerOrderCodeList.size > 0">
            and tdo.customer_order_code in
            <foreach collection="params.customerOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.loadRegionName != null and params.loadRegionName != ''">
            and instr(tdoa.load_region_name, #{params.loadRegionName,jdbcType=VARCHAR})
        </if>
        <if test="params.ifUrgent != null">
            and tdo.if_urgent = #{params.ifUrgent,jdbcType=INTEGER}
        </if>
        <if test="params.ifOverdue != null">
            and tdo.if_overdue = #{params.ifOverdue,jdbcType=INTEGER}
        </if>
        <if test="params.goodsName != null and params.goodsName != ''">
            and (instr(tdog.goods_name, #{params.goodsName,jdbcType=VARCHAR})
                or instr(tdog.goods_size, #{params.goodsName,jdbcType=VARCHAR})
                or instr(tdog.length, #{params.goodsName,jdbcType=VARCHAR})
                or instr(tdog.width, #{params.goodsName,jdbcType=VARCHAR})
                or instr(tdog.height, #{params.goodsName,jdbcType=VARCHAR}))
        </if>
        <if test="params.companyCarrierName != null and params.companyCarrierName != ''">
            and ((tdo.company_carrier_type = 1 and instr(tdo.company_carrier_name, #{params.companyCarrierName,jdbcType=VARCHAR}))
                or (tdo.company_carrier_type = 2 and (instr(tdo.carrier_contact_name, #{params.companyCarrierName,jdbcType=VARCHAR}) or instr(
                        AES_DECRYPT(UNHEX(tdo.carrier_contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'),
                        #{params.companyCarrierName,jdbcType=VARCHAR}))))
        </if>
        <if test="params.orgCode != null and params.orgCode != ''">
            <bind name="orgCodeLike" value="params.orgCode + '%'"/>
            and tdo.publish_org_code like #{orgCodeLike}
        </if>
        <if test="params.recycleTaskType != null">
            and tdo.recycle_task_type = #{params.recycleTaskType,jdbcType=INTEGER}
        </if>
        <if test="params.ifExtDemandOrder != null">
            and tdo.if_ext_demand_order = #{params.ifExtDemandOrder,jdbcType=INTEGER}
        </if>
        <if test="params.projectLabel != null">
            and FIND_IN_SET(#{params.projectLabel,jdbcType=INTEGER}, tdo.project_label)
        </if>
        <if test="params.excludeDemandIdList != null and params.excludeDemandIdList.size() > 0">
            and tdo.id not in
            <foreach collection="params.excludeDemandIdList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="params.sort != null and params.sort != ''">
            order by ${params.sort} ${params.order}, tdo.id desc
        </if>
        <if test="params.sort == null">
            order by tdo.id desc
        </if>
    </select>

    <select id="getDemandOrderDetail" resultType="com.logistics.tms.controller.demandorder.response.DemandOrderDetailResponseModel">
        SELECT
        tdo.id as demandId,
        tdo.entrust_status as entrustStatus,
        tdo.status,
        tdo.if_cancel as ifCancel,
        tdo.if_empty as ifEmpty,
        tdo.if_rollback as ifRollback,
        tdo.demand_order_code as demandOrderCode,
        tdo.customer_order_code as customerOrderCode,
        tdo.publish_name as publishName,
        tdo.publish_time as publishTime,
        tdo.ticket_time as ticketTime,
        tdo.remark as remark,
        tdo.cancel_reason as cancelReason,
        tdo.rollback_remark as rollbackRemark,
        tdo.contract_price_type as contractPriceType,
        tdo.contract_price as contractPrice,
        tdo.expect_contract_price_type as exceptContractPriceType,
        tdo.expect_contract_price as exceptContractPrice,
        tdo.settlement_tonnage as settlementTonnage,
        tdo.goods_unit as goodsUnit,
        tdo.goods_amount as goodsAmount,
        tdo.source,
        tdo.entrust_type as entrustType,
        tdo.carrier_price_type as carrierPriceType,
        tdo.carrier_price as carrierPrice,
        tdo.company_entrust_id as companyEntrustId,
        tdo.company_entrust_name as companyEntrustName,
        tdo.company_entrust_name as customerCompanyName,
        tdo.upstream_customer as upstreamCustomer,
        tdo.order_type as orderType,
        tdo.sinopec_order_no as sinopecOrderNo,
        tdo.carrier_settlement as carrierSettlement,
        tdo.company_carrier_id as companyCarrierId,
        tdo.company_carrier_type as companyCarrierType,
        tdo.company_carrier_name as companyCarrierName,
        tdo.carrier_contact_id as carrierContactId,
        tdo.carrier_contact_name as carrierContactName,
        AES_DECRYPT(UNHEX(tdo.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactMobile,

        tdoa.load_province_name as loadProvinceName,
        tdoa.load_city_name as loadCityName,
        tdoa.load_area_name as loadAreaName,
        tdoa.load_detail_address as loadDetailAddress,
        tdoa.load_warehouse as loadWarehouse,
        tdoa.consignor_name as consignorName,
        tdoa.consignor_mobile as consignorMobile,
        tdoa.expected_load_time as expectedLoadTime,
        tdoa.unload_province_name as unloadProvinceName,
        tdoa.unload_city_name as unloadCityName,
        tdoa.unload_area_name as unloadAreaName,
        tdoa.unload_detail_address as unloadDetailAddress,
        tdoa.unload_warehouse as unloadWarehouse,
        tdoa.receiver_name as receiverName,
        tdoa.receiver_mobile as receiverMobile,
        tdoa.expected_unload_time as expectedUnloadTime,

        tr.settlement_amount as settlementAmount,
        tr.settlement_cost_total as settlementCostTotal,
        tr.price_type as priceType,

        tcc.level as isOurCompany,

        tdp.settlement_amount as carrierSettlementAmount,
        tdp.settlement_cost_total as carrierSettlementCostTotal,
        tdp.price_type as carrierPaymentPriceType

        FROM t_demand_order tdo
        LEFT JOIN t_demand_order_address tdoa on tdo.id = tdoa.demand_order_id and tdoa.valid = 1
        LEFT JOIN t_demand_receivement tr on tr.demand_order_id = tdo.id and tr.valid = 1
        LEFT JOIN t_company_carrier tcc on tcc.id = tdo.company_carrier_id and tcc.valid = 1
        LEFT JOIN t_demand_payment tdp on tdp.demand_order_id = tdo.id and tdp.valid = 1
        WHERE tdo.valid = 1
        and tdo.id = #{params.demandId}
    </select>

    <select id="getDemandOrderDetailForLeYi" resultType="com.logistics.tms.controller.demandorder.response.DemandOrderDetailForLeYiResponseModel">
        SELECT
        tdo.id                                                                                                                         as demandId,
        tdo.entrust_status                                                                                                             as entrustStatus,
        tdo.if_cancel                                                                                                                  as ifCancel,
        tdo.if_empty                                                                                                                   as ifEmpty,
        tdo.if_rollback                                                                                                                as ifRollback,
        tdo.status                                                                                                                     as status,
        tdo.demand_order_code                                                                                                          as demandOrderCode,
        tdo.customer_order_code                                                                                                        as customerOrderCode,
        tdo.publish_name                                                                                                               as publishName,
        tdo.publish_time                                                                                                               as publishTime,
        tdo.ticket_time                                                                                                                as ticketTime,
        tdo.remark                                                                                                                     as remark,
        tdo.cancel_reason                                                                                                              as cancelReason,
        tdo.expect_contract_price_type                                                                                                 as exceptContractPriceType,
        tdo.expect_contract_price                                                                                                      as exceptContractPrice,
        tdo.carrier_price_type                                                                                                         as carrierFreightType,
        tdo.carrier_price                                                                                                              as carrierFreight,
        tdo.company_carrier_id                                                                                                         as companyCarrierId,
        tdo.company_carrier_type                                                                                                       as companyCarrierType,
        tdo.company_carrier_name                                                                                                       as companyCarrierName,
        tdo.carrier_contact_name                                                                                                       AS carrierContactName,
        AES_DECRYPT(UNHEX(tdo.carrier_contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactPhone,
        tdo.settlement_tonnage                                                                                                         as settlementTonnage,
        tdo.carrier_settlement                                                                                                         as carrierSettlement,
        tdo.goods_unit                                                                                                                 as goodsUnit,
        tdo.goods_amount                                                                                                               as goodsAmount,
        tdo.not_arranged_amount                                                                                                        as notArrangedAmount,
        tdo.source,
        tdo.entrust_type                                                                                                               as entrustType,
        tdo.company_entrust_id                                                                                                         as companyEntrustId,
        tdo.company_entrust_name                                                                                                       as companyEntrustName,
        tdo.company_entrust_name                                                                                                       as customerCompanyName,
        tdo.upstream_customer                                                                                                          as upstreamCustomer,
        tdo.if_objection                                                                                                               as ifObjection,
        tdo.available_on_weekends                                                                                                      as availableOnWeekends,
        tdo.loading_unloading_part                                                                                                     as loadingUnloadingPart,
        tdo.loading_unloading_charge                                                                                                   as loadingUnloadingCharge,
        tdo.project_label                                                                                                              as projectLabel,

        tdoa.load_province_name                                                                                                        as loadProvinceName,
        tdoa.load_city_name                                                                                                            as loadCityName,
        tdoa.load_area_name                                                                                                            as loadAreaName,
        tdoa.load_detail_address                                                                                                       as loadDetailAddress,
        tdoa.load_warehouse                                                                                                            as loadWarehouse,
        tdoa.load_longitude                                                                                                            as loadLongitude,
        tdoa.load_latitude                                                                                                             as loadLatitude,
        tdoa.consignor_name                                                                                                            as consignorName,
        tdoa.consignor_mobile                                                                                                          as consignorMobile,
        tdoa.expected_load_time                                                                                                        as expectedLoadTime,
        tdoa.unload_province_name                                                                                                      as unloadProvinceName,
        tdoa.unload_city_name                                                                                                          as unloadCityName,
        tdoa.unload_area_name                                                                                                          as unloadAreaName,
        tdoa.unload_detail_address                                                                                                     as unloadDetailAddress,
        tdoa.unload_warehouse                                                                                                          as unloadWarehouse,
        tdoa.unload_longitude                                                                                                          as unloadLongitude,
        tdoa.unload_latitude                                                                                                           as unloadLatitude,
        tdoa.receiver_name                                                                                                             as receiverName,
        tdoa.receiver_mobile                                                                                                           as receiverMobile,
        tdoa.expected_unload_time                                                                                                      as expectedUnloadTime
        FROM t_demand_order tdo
        LEFT JOIN t_demand_order_address tdoa on tdo.id = tdoa.demand_order_id and tdoa.valid = 1
        WHERE tdo.valid = 1
          and tdo.id = #{params.demandId}
    </select>

    <select id="selectDemandOrdersBySourceAndPubDate" resultType="com.logistics.tms.entity.TDemandOrder">
        select
        id,
        customer_order_code as customerOrderCode
        from t_demand_order
        where valid = 1
        and source = #{source,jdbcType=INTEGER}
        <if test="fromDate!=null">
            and publish_time &gt;= #{fromDate,jdbcType=TIMESTAMP}
        </if>
        <if test="endDate!=null">
            and publish_time &lt;= #{endDate,jdbcType=TIMESTAMP}
        </if>
    </select>

    <select id="getExistCodesByCustomerCodes" resultType="java.lang.String">
        select
        customer_order_code
        from t_demand_order
        where valid = 1
        and source = 4
        and customer_order_code in
        <if test="list != null and list.size > 0">
            <foreach collection="list" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="list == null or list.size == 0">
            and false
        </if>
    </select>

    <resultMap id="getPublishDemandOrderDetailMap" type="com.logistics.tms.controller.demandorder.response.GetPublishDemandOrderDetailResponseModel">
        <id column="id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="company_entrust_id" property="companyEntrustId" jdbcType="BIGINT"/>
        <result column="company_entrust_name" property="companyEntrustName" jdbcType="VARCHAR"/>
        <result column="contract_price_type" property="contractPriceType" jdbcType="INTEGER"/>
        <result column="contract_price" property="contractPrice" jdbcType="DECIMAL"/>
        <result column="expect_contract_price_type" property="exceptContractPriceType" jdbcType="INTEGER"/>
        <result column="expect_contract_price" property="exceptContractPrice" jdbcType="DECIMAL"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="INTEGER"/>
        <result column="company_carrier_name" property="companyCarrierName" jdbcType="VARCHAR"/>
        <result column="company_carrier_type" property="companyCarrierType" jdbcType="VARCHAR"/>
        <result column="carrier_contact_id" property="carrierContactId" jdbcType="INTEGER"/>
        <result column="carrier_contact_name" property="companyCarrierContactName" jdbcType="VARCHAR"/>
        <result column="carrier_contact_phone" property="companyCarrierContactPhone" jdbcType="VARCHAR"/>
        <result column="carrier_price_type" property="carrierPriceType" jdbcType="INTEGER"/>
        <result column="carrier_price" property="carrierPrice" jdbcType="DECIMAL"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="load_province_id" property="loadProvinceId" jdbcType="BIGINT"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_id" property="loadCityId" jdbcType="BIGINT"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_id" property="loadAreaId" jdbcType="BIGINT"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="consignor_name" property="loadContactName" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="loadContactMobile" jdbcType="VARCHAR"/>
        <result column="expected_load_time" property="expectedLoadTime" jdbcType="TIMESTAMP"/>
        <result column="unload_province_id" property="unloadProvinceId" jdbcType="BIGINT"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_id" property="unloadCityId" jdbcType="BIGINT"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_id" property="unloadAreaId" jdbcType="BIGINT"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="receiver_name" property="unloadContactName" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="unloadContactMobile" jdbcType="VARCHAR"/>
        <result column="expected_unload_time" property="expectedUnloadTime" jdbcType="TIMESTAMP"/>

        <collection property="demandOrderGoodsList" ofType="com.logistics.tms.controller.demandorder.response.DemandOrderGoodsBaseInfoResponseModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="goods_unit" property="goodsUnit" jdbcType="VARCHAR"/>
            <result column="goods_amount" property="goodsAmount" jdbcType="DECIMAL"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
        </collection>
    </resultMap>

    <select id="getPublishDemandOrderDetail" resultMap="getPublishDemandOrderDetailMap">
        select
        tdo.id as id,
        tdo.demand_order_code,
        tdo.customer_order_code,
        tdo.goods_unit,
        tdo.company_entrust_id,
        tdo.company_entrust_name,
        tdo.contract_price_type,
        tdo.contract_price,
        tdo.expect_contract_price_type,
        tdo.expect_contract_price,
        tdo.company_carrier_id,
        tdo.company_carrier_name,
        tdo.company_carrier_type,
        tdo.carrier_contact_id,
        tdo.carrier_contact_name,
        tdo.carrier_contact_phone,
        tdo.carrier_price_type,
        tdo.carrier_price,
        tdo.remark,

        tdoa.load_province_id,
        tdoa.load_province_name,
        tdoa.load_city_id,
        tdoa.load_city_name,
        tdoa.load_area_id,
        tdoa.load_area_name,
        tdoa.load_detail_address,
        tdoa.load_warehouse,
        tdoa.consignor_name,
        tdoa.consignor_mobile,
        tdoa.expected_load_time,
        tdoa.unload_province_id,
        tdoa.unload_province_name,
        tdoa.unload_city_id,
        tdoa.unload_city_name,
        tdoa.unload_area_id,
        tdoa.unload_area_name,
        tdoa.unload_detail_address,
        tdoa.unload_warehouse,
        tdoa.receiver_name,
        tdoa.receiver_mobile,
        tdoa.expected_unload_time,

        tdog.id as goodsId,
        tdog.goods_name,
        tdog.goods_size,
        tdog.goods_amount,
        tdog.length,
        tdog.width,
        tdog.height
        from t_demand_order tdo
        left join t_demand_order_address tdoa on tdoa.valid = 1 and tdoa.demand_order_id = tdo.id
        left join t_demand_order_goods tdog on tdog.valid = 1 and tdog.demand_order_id = tdo.id
        where tdo.valid = 1
        and tdo.id = #{demandOrderId,jdbcType=BIGINT}
    </select>

    <resultMap id="getSyncTMSDemandOrderModelByIdMap" type="com.logistics.tms.biz.demandorder.model.SyncTMSDemandOrderModel">
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="publish_name" property="publishName" jdbcType="VARCHAR"/>
        <result column="publish_time" property="publishTime" jdbcType="TIMESTAMP"/>
        <result column="goods_amount" property="goodsAmount" jdbcType="DECIMAL"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="entrust_type" property="entrustType" jdbcType="INTEGER"/>
        <result column="if_urgent" property="ifUrgent" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="company_entrust_name" property="companyEntrustName" jdbcType="VARCHAR"/>
        <result column="expect_contract_price_type" property="contractPriceType" jdbcType="INTEGER"/>
        <result column="expect_contract_price" property="contractPrice" jdbcType="DECIMAL"/>
        <result column="upstream_customer" property="upstreamCustomer" jdbcType="VARCHAR"/>
        <result column="load_province_id" property="loadProvinceId" jdbcType="BIGINT"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_id" property="loadCityId" jdbcType="BIGINT"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_id" property="loadAreaId" jdbcType="BIGINT"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="load_company" property="loadCompany" jdbcType="VARCHAR"/>
        <result column="load_longitude" property="loadLongitude" jdbcType="VARCHAR"/>
        <result column="load_latitude" property="loadLatitude" jdbcType="VARCHAR"/>
        <result column="consignor_name" property="consignorName" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR"/>
        <result column="expected_load_time" property="expectedLoadTime" jdbcType="TIMESTAMP"/>
        <result column="unload_province_id" property="unloadProvinceId" jdbcType="BIGINT"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_id" property="unloadCityId" jdbcType="BIGINT"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_id" property="unloadAreaId" jdbcType="BIGINT"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="unload_company" property="unloadCompany" jdbcType="VARCHAR"/>
        <result column="unload_longitude" property="unloadLongitude" jdbcType="VARCHAR"/>
        <result column="unload_latitude" property="unloadLatitude" jdbcType="VARCHAR"/>
        <result column="unload_address_is_amend" property="unloadAddressIsAmend" jdbcType="INTEGER"/>
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR"/>
        <result column="expected_unload_time" property="expectedUnloadTime" jdbcType="TIMESTAMP"/>
        <collection property="goodsModels" ofType="com.logistics.tms.biz.demandorder.model.SyncTMSDemandOrderGoodsModel">
            <result column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="sku_code" property="skuCode" jdbcType="VARCHAR"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="category_name" property="categoryName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goodsGoodsAmount" property="goodsAmount" jdbcType="DECIMAL"/>
        </collection>
        <collection property="ordersModels" ofType="com.logistics.tms.rabbitmq.consumer.model.SyncTrayDemandOrderOrdersModel">
            <result column="order_id" property="orderId" jdbcType="BIGINT"/>
            <result column="order_code" property="orderCode" jdbcType="VARCHAR"/>
            <result column="total_amount" property="totalAmount" jdbcType="DECIMAL"/>
            <result column="rel_type" property="relType" jdbcType="INTEGER"/>
            <result column="relRemark" property="remark" jdbcType="VARCHAR"/>
            <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
            <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        </collection>
    </resultMap>
    <select id="getSyncTMSDemandOrderModelById" resultMap="getSyncTMSDemandOrderModelByIdMap">
        select
        tdo.demand_order_code,
        tdo.customer_order_code,
        tdo.publish_name,
        tdo.publish_time,
        tdo.goods_amount,
        tdo.goods_unit,
        tdo.entrust_type,
        tdo.remark,
        tdo.company_entrust_name,
        tdo.expect_contract_price_type,
        tdo.expect_contract_price,
        tdo.upstream_customer,
        tdo.if_urgent,

        tdoa.load_province_id,
        tdoa.load_province_name,
        tdoa.load_city_id,
        tdoa.load_city_name,
        tdoa.load_area_id,
        tdoa.load_area_name,
        tdoa.load_detail_address,
        tdoa.load_warehouse,
        tdoa.load_company,
        tdoa.load_longitude,
        tdoa.load_latitude,
        tdoa.consignor_name,
        tdoa.consignor_mobile,
        tdoa.expected_load_time,
        tdoa.unload_province_id,
        tdoa.unload_province_name,
        tdoa.unload_city_id,
        tdoa.unload_city_name,
        tdoa.unload_area_id,
        tdoa.unload_area_name,
        tdoa.unload_detail_address,
        tdoa.unload_warehouse,
        tdoa.unload_company,
        tdoa.unload_longitude,
        tdoa.unload_latitude,
        tdoa.unload_address_is_amend,
        tdoa.receiver_name,
        tdoa.receiver_mobile,
        tdoa.expected_unload_time,

        tdogr.booking_order_goods_id as goodsId,

        tdog.sku_code,
        tdog.goods_name,
        tdog.category_name,
        tdog.length,
        tdog.width,
        tdog.height,
        tdog.goods_amount as goodsGoodsAmount,

        tdoor.order_id,
        tdoor.total_amount,
        tdoor.order_code,
        tdoor.rel_type,
        tdoor.remark as relRemark,
        tdoor.created_by,
        tdoor.created_time
        from t_demand_order tdo
        left join t_demand_order_address tdoa on tdo.id = tdoa.demand_order_id and tdoa.valid = 1
        left join t_demand_order_goods tdog on tdo.id = tdog.demand_order_id and tdog.valid = 1
        left join t_demand_order_goods_rel tdogr on tdog.id = tdogr.demand_order_goods_id and tdogr.valid = 1
        left join t_demand_order_order_rel tdoor on tdoor.demand_order_id = tdo.id and tdoor.valid = 1
        where tdo.id in (${demandOrderIds})
        and tdo.valid = 1
    </select>

    <select id="getInvalidTopByDemandOrderCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_demand_order
        where demand_order_code = #{demandOrderCode,jdbcType=VARCHAR}
        and valid = 0
        order by id desc
        limit 1
    </select>

    <resultMap id="getDemandOrderGoodsByDemandIds_Map" type="com.logistics.tms.biz.dispatch.model.DemandOrderModel">
        <id column="demandOrderId" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="demandOrderCode" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="ifCancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="ifEmpty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="ifRollback" property="ifRollback" jdbcType="INTEGER"/>
        <result column="ifObjectionSinopec" property="ifObjectionSinopec" jdbcType="INTEGER"/>
        <result column="customerOrderCode" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="source" property="source" jdbcType="INTEGER"/>
        <result column="entrustType" property="entrustType" jdbcType="INTEGER"/>
        <result column="goodsAmount" property="goodsAmount" jdbcType="DECIMAL"/>
        <result column="goodsUnit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="companyCarrierType" property="companyCarrierType" jdbcType="INTEGER"/>
        <result column="companyCarrierId" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="companyCarrierName" property="companyCarrierName" jdbcType="VARCHAR"/>
        <result column="carrierContactId" property="carrierContactId" jdbcType="BIGINT"/>
        <result column="carrierContactName" property="carrierContactName" jdbcType="VARCHAR"/>
        <result column="carrierContactPhone" property="carrierContactPhone" jdbcType="VARCHAR"/>
        <result column="companyEntrustId" property="companyEntrustId" jdbcType="BIGINT"/>

        <result column="loadProvinceName" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="loadCityName" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="loadAreaName" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="loadWarehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="loadDetailAddress" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="loadLongitude" property="loadLongitude" jdbcType="VARCHAR"/>
        <result column="loadLatitude" property="loadLatitude" jdbcType="VARCHAR"/>
        <result column="unloadProvinceName" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unloadCityName" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unloadAreaId" property="unloadAreaId" jdbcType="BIGINT"/>
        <result column="unloadAreaName" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unloadWarehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="unloadDetailAddress" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unloadLongitude" property="unloadLongitude" jdbcType="VARCHAR"/>
        <result column="unloadLatitude" property="unloadLatitude" jdbcType="VARCHAR"/>

        <collection property="goodsList" ofType="com.logistics.tms.biz.dispatch.model.DemandOrderGoodsModel">
            <id column="demandOrderGoodsId" property="demandOrderGoodsId" jdbcType="BIGINT"/>
            <result column="goodsName" property="goodsName" jdbcType="VARCHAR"/>
            <result column="goodsSize" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="notArrangedAmount" property="notArrangedAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>
    <select id="getDemandOrderGoodsByDemandIds" resultMap="getDemandOrderGoodsByDemandIds_Map">
        select
        tdo.id as demandOrderId,
        tdo.demand_order_code as demandOrderCode,
        tdo.customer_order_code as customerOrderCode,
        tdo.status,
        tdo.if_cancel as ifCancel,
        tdo.if_empty as ifEmpty,
        tdo.if_rollback as ifRollback,
        tdo.goods_amount as goodsAmount,
        tdo.goods_unit as goodsUnit,
        tdo.entrust_type as entrustType,
        tdo.source as source,
        tdo.company_carrier_name as companyCarrierName,
        tdo.company_carrier_id as companyCarrierId,
        tdo.company_carrier_type as companyCarrierType,
        tdo.carrier_contact_id as carrierContactId,
        tdo.carrier_contact_name as carrierContactName,
        AES_DECRYPT(UNHEX(tdo.carrier_contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactPhone,
        tdo.company_entrust_id as companyEntrustId,
        tdo.if_objection_sinopec as ifObjectionSinopec,

        tdog.id as demandOrderGoodsId,
        tdog.goods_name as goodsName,
        tdog.length as length,
        tdog.width as width,
        tdog.height as height,
        tdog.goods_size as goodsSize,
        tdog.not_arranged_amount as notArrangedAmount,

        tdoa.load_province_name as loadProvinceName,
        tdoa.load_city_name as loadCityName,
        tdoa.load_area_name as loadAreaName,
        tdoa.load_detail_address as loadDetailAddress,
        tdoa.load_warehouse as loadWarehouse,
        tdoa.load_longitude as loadLongitude,
        tdoa.load_latitude as loadLatitude,
        tdoa.unload_province_name as unloadProvinceName,
        tdoa.unload_city_name as unloadCityName,
        tdoa.unload_area_id as unloadAreaId,
        tdoa.unload_area_name as unloadAreaName,
        tdoa.unload_detail_address as unloadDetailAddress,
        tdoa.unload_warehouse as unloadWarehouse,
        tdoa.unload_longitude as unloadLongitude,
        tdoa.unload_latitude as unloadLatitude
        from t_demand_order tdo
        left join t_demand_order_goods tdog on tdo.id = tdog.demand_order_id and tdog.valid = 1
        left join t_demand_order_address tdoa on tdoa.demand_order_id = tdo.id and tdoa.valid = 1
        where tdo.valid = 1
        and tdo.id in (${ids})
    </select>

    <update id="delDemandOrderAddressGoodsLogsByDemandOrderIds">
        update t_demand_order tdo
        left join t_demand_order_address tdoa on tdoa.demand_order_id = tdo.id and tdoa.valid = 1
        left join t_demand_order_goods tdog on tdog.demand_order_id = tdo.id and tdog.valid = 1
        left join t_demand_order_operate_logs tdol on tdol.demand_order_id = tdo.id and tdol.valid = 1
        left join t_demand_order_order_rel tdoor on tdoor.demand_order_id = tdo.id and tdoor.valid = 1
        left join t_demand_order_goods_rel tdogr on tdogr.demand_order_goods_id = tdog.id and tdogr.valid = 1
        set
        tdo.valid = 0, tdo.last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR}, tdo.last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
        tdoa.valid = 0, tdoa.last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR}, tdoa.last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
        tdog.valid = 0, tdog.last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR}, tdog.last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
        tdol.valid = 0, tdol.last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR}, tdol.last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
        tdoor.valid = 0, tdoor.last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR}, tdoor.last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
        tdogr.valid = 0, tdogr.last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR}, tdogr.last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP}
        where tdo.valid = 1
        and tdo.id in (${demandOrderIds})
    </update>

    <select id="publishDetail" resultType="com.logistics.tms.controller.demandorder.response.BatchPublishDetailResponseModel">
        select
        tdo.id                     as demandId,
        tdo.demand_order_code      as demandOrderCode,
        tdo.goods_unit             as goodsUnit,
        tdo.goods_amount           as goodsAmount,
        tdo.entrust_status         as entrustStatus,
        tdo.if_cancel              as ifCancel,
        tdo.if_empty               as ifEmpty,
        tdo.if_rollback            as ifRollback,
        tdo.entrust_type           as entrustType,
        tdo.company_entrust_name   as companyEntrustName,
        tdo.source                 as demandOrderSource,
        tdo.if_ext_demand_order    as ifeExtDemandOrder,

        tdoa.id                    as demandOrderAddressId,
        tdoa.load_province_id      as loadProvinceId,
        tdoa.load_province_name    as loadProvinceName,
        tdoa.load_city_id          as loadCityId,
        tdoa.load_city_name        as loadCityName,
        tdoa.load_area_name        as loadAreaName,
        tdoa.load_detail_address   as loadDetailAddress,
        tdoa.load_warehouse        as loadWarehouse,
        tdoa.unload_province_name  as unloadProvinceName,
        tdoa.unload_city_name      as unloadCityName,
        tdoa.unload_area_name      as unloadAreaName,
        tdoa.unload_detail_address as unloadDetailAddress,
        tdoa.unload_warehouse      as unloadWarehouse
        from t_demand_order tdo
        left join t_demand_order_address tdoa on tdoa.demand_order_id = tdo.id and tdoa.valid = 1
        where tdo.valid = 1
        and tdo.id in (${demandOrderIds})
        order by tdo.id desc
    </select>

    <select id="dispatchAlarmStatistics" resultType="com.logistics.tms.controller.demandorder.response.DispatchAlarmStatisticsResponseModel">
        SELECT
        count(tdo.id)           AS demandOrderCount,
        sum(tdo.goods_amount)   AS goodsAmount,
        tdoa.load_province_name AS loadProvinceName,
        tdoa.load_city_id       AS loadCityId,
        tdoa.load_city_name     AS loadCityName
        FROM t_demand_order tdo
        LEFT JOIN t_demand_order_address tdoa ON tdo.id = tdoa.demand_order_id AND tdoa.valid = 1
        WHERE tdo.`status` in (1000, 2000)
        AND tdo.valid = 1
        and tdo.source = 1
        and tdo.if_cancel = 0
        and tdo.if_empty = 0
        and tdo.if_rollback = 0
        AND TIMESTAMPDIFF(DAY, tdo.publish_time, NOW()) > (IF(tdo.if_urgent = 0, 3, 1))
        AND tdoa.load_city_id > 0
        AND tdo.entrust_type in (2,10)
        GROUP BY tdoa.load_city_id
        ORDER BY goodsAmount desc, tdoa.load_city_id desc
    </select>

    <select id="waitDispatchStatistics" resultType="com.logistics.tms.controller.demandorder.response.WaitDispatchStatisticsModel">
        SELECT
        date(publish_time) AS publishTime,
        status             AS status,
        goods_amount       AS goodsAmount
        FROM t_demand_order
        where status in (500, 1000, 2000)
        and valid = 1
        and source = 1
        and if_cancel = 0
        and if_empty = 0
        and publish_time is not null
    </select>

    <select id="mapDataStatistics" resultType="com.logistics.tms.controller.demandorder.response.MapDataStatisticsResponseModel">
        select
        count(tdo.id) as waitDispatchCount,
        tdoa.load_city_name as cityName,
        tdoa.load_city_id as loadCityId
        from t_demand_order tdo
        left join t_demand_order_address tdoa on tdo.id = tdoa.demand_order_id and tdoa.valid =1
        where tdo.entrust_status in (1000,2000)
        and tdo.valid =1
        and tdo.source = 1
        and tdo.if_cancel = 0
        and tdo.if_empty = 0
        and tdo.if_rollback = 0
        and tdoa.load_city_id > 0
        GROUP BY tdoa.load_city_id
    </select>

    <select id="getValidSinopecOrderByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_demand_order
        where valid = 1
        and customer_order_code = #{orderNo,jdbcType=VARCHAR}
        and if_cancel = 0
        and order_type = 21
        and source = 3
    </select>

    <select id="getValidSinopecOrderByOrderNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_demand_order
        where valid = 1
        <choose>
            <when test="orderNos != null and orderNos.size() != 0">
                and customer_order_code in
                <foreach collection="orderNos" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
        and if_cancel = 0
        and order_type = 21
        and source = 3
    </select>

    <update id="rollBackSinopecDemandOrderByCancel">
        update t_demand_order tdo
        left join t_demand_order_events tdoe on tdoe.demand_order_id = tdo.id and tdoe.event = 30 and tdoe.valid = 1
        left join t_demand_order_operate_logs tdol on tdol.demand_order_id = tdo.id and tdol.operation_type = 1002 and tdol.valid = 1
        set
        tdo.if_cancel = 0,
        tdo.cancel_reason = '',
        tdo.cancel_time = null,
        tdoe.valid = 0,
        tdol.valid = 0
        where tdo.id in (${demandOrderIds})
        and tdo.valid = 1
    </update>

    <select id="sinopecPublishDetail"
            resultType="com.logistics.tms.controller.demandorder.response.PublishSinopecResponseModel">
        select
        tdo.sinopec_customer_id       as sinopecCustomerId,
        tdo.sinopec_customer_name     as sinopecCustomerName,
        tdo.id                        as demandId,
        tdo.demand_order_code         as demandOrderCode,
        tdo.goods_unit                as goodsUnit,
        tdo.goods_amount              as goodsAmount,
        tdo.company_entrust_id        as companyEntrustId,
        tdo.company_entrust_name      as companyEntrustName,
        tdo.manufacturer_name         as manufacturerName,
        tdo.remark                    as remark,
        tdo.entrust_status            as entrustStatus,
        tdo.if_cancel                 as ifCancel,
        tdo.if_empty                  as ifEmpty,
        tdo.source                    as source,
        tdo.order_type                as orderType,
        tdo.customer_order_code       as customerOrderCode,
        tdo.sinopec_order_no          as sinopecOrderNo,
        tdo.if_objection_sinopec      as ifObjectionSinopec,
        tdo.sinopec_online_goods_flag as sinopecOnlineGoodsFlag,

        tdoa.id                       as demandOrderAddressId,
        tdoa.load_province_id         as loadProvinceId,
        tdoa.load_province_name       as loadProvinceName,
        tdoa.load_city_id             as loadCityId,
        tdoa.load_city_name           as loadCityName,
        tdoa.load_area_id             as loadAreaId,
        tdoa.load_area_name           as loadAreaName,
        tdoa.load_detail_address      as loadDetailAddress,
        tdoa.load_warehouse           as loadWarehouse,
        tdoa.unload_province_id       as unloadProvinceId,
        tdoa.unload_province_name     as unloadProvinceName,
        tdoa.unload_city_id           as unloadCityId,
        tdoa.unload_city_name         as unloadCityName,
        tdoa.unload_area_id           as unloadAreaId,
        tdoa.unload_area_name         as unloadAreaName,
        tdoa.unload_detail_address    as unloadDetailAddress
        from t_demand_order tdo
        left join t_demand_order_address tdoa on tdoa.demand_order_id = tdo.id and tdoa.valid = 1
        where tdo.valid = 1
        and tdo.id in
        <foreach collection="demandOrderIds" open="(" separator="," close=")" item="item">
            #{item,jdbcType=BIGINT}
        </foreach>
        order by tdo.id desc
    </select>

    <update id="rollBackSinopecDemandOrderByPublish">
        <foreach collection="list" item="item" separator=";">
            update t_demand_order tdo
            left join t_demand_order_address tdoa on tdo.id = tdoa.demand_order_id and tdoa.valid = 1
            left join t_demand_order_operate_logs tdol on tdol.demand_order_id = tdo.id and tdol.operation_type = 1005 and tdol.valid = 1
            set
            tdo.entrust_status =500,
            tdo.status = 500,
            tdo.company_carrier_id = 0,
            tdo.company_carrier_name = '',
            tdo.dispatcher_name = '',
            tdo.dispatcher_phone = '',
            tdo.expect_contract_price_type = 0,
            tdo.expect_contract_price = 0,
            tdo.contract_price_type =0,
            tdo.contract_price = 0,

            tdoa.load_province_id = #{item.loadProvinceId,jdbcType=BIGINT},
            tdoa.load_province_name = #{item.loadProvinceName,jdbcType=VARCHAR},
            tdoa.load_city_id = #{item.loadCityId,jdbcType=BIGINT},
            tdoa.load_city_name = #{item.loadCityName,jdbcType=VARCHAR},
            tdoa.load_area_id = #{item.loadAreaId,jdbcType=BIGINT},
            tdoa.load_area_name = #{item.loadAreaName,jdbcType=VARCHAR},
            tdoa.load_detail_address = #{item.loadDetailAddress,jdbcType=VARCHAR},
            tdoa.unload_province_id = #{item.unloadProvinceId,jdbcType=BIGINT},
            tdoa.unload_province_name = #{item.unloadProvinceName,jdbcType=VARCHAR},
            tdoa.unload_city_id = #{item.unloadCityId,jdbcType=BIGINT},
            tdoa.unload_city_name = #{item.unloadCityName,jdbcType=VARCHAR},
            tdoa.unload_area_id = #{item.unloadAreaId,jdbcType=BIGINT},
            tdoa.unload_area_name = #{item.unloadAreaName,jdbcType=VARCHAR},
            tdoa.unload_detail_address = #{item.unloadDetailAddress,jdbcType=VARCHAR},

            tdol.valid = 0
            where tdo.id = #{item.demandId,jdbcType=BIGINT}
            and tdo.entrust_status != 500
            and tdo.status != 500
            and tdo.if_cancel = 0
            and tdo.source = 3
            and tdo.order_type = 21
            and tdo.if_empty = 0
            and tdo.if_rollback = 0
            and tdo.valid = 1
        </foreach>
    </update>
    <select id="selectDemandOrders" resultType="com.logistics.tms.controller.demandorder.response.DemandOrderDataStatisticsResponseModel">
        select
        tdo.id as demandOrderId,
        tdo.status as status,
        tdo.goods_amount as goodsAmount
        FROM t_demand_order tdo
        WHERE tdo.source=1
        AND tdo.valid = 1
    </select>
    <select id="sinopecReportAbnormalDetail" resultType="com.logistics.tms.controller.demandorder.response.SinopecReportAbnormalDetailResponseModel">
        SELECT
        tdo.id                        as demandId,
        tdo.demand_order_code         as demandOrderCode,
        tdo.goods_unit                as goodsUnit,
        tdo.goods_amount              as goodsAmount,
        tdo.company_entrust_name      as companyEntrustName,
        tdo.manufacturer_name         as manufacturerName,
        tdo.remark                    as remark,
        tdo.dispatcher_name           as dispatcherName,
        tdo.dispatcher_phone          as dispatcherPhone,
        tdo.contract_price            as contractPrice,
        tdo.sinopec_online_goods_flag as sinopecOnlineGoodsFlag,

        tdoa.load_province_id         as loadProvinceId,
        tdoa.load_province_name       as loadProvinceName,
        tdoa.load_city_id             as loadCityId,
        tdoa.load_city_name           as loadCityName,
        tdoa.load_area_id             as loadAreaId,
        tdoa.load_area_name           as loadAreaName,
        tdoa.load_detail_address      as loadDetailAddress,
        tdoa.load_warehouse           as loadWarehouse,
        tdoa.unload_province_id       as unloadProvinceId,
        tdoa.unload_province_name     as unloadProvinceName,
        tdoa.unload_city_id           as unloadCityId,
        tdoa.unload_city_name         as unloadCityName,
        tdoa.unload_area_id           as unloadAreaId,
        tdoa.unload_area_name         as unloadAreaName,
        tdoa.unload_detail_address    as unloadDetailAddress

        FROM t_demand_order tdo
        LEFT JOIN t_demand_order_address tdoa ON tdo.id = tdoa.demand_order_id and tdoa.valid = 1
        where tdo.valid = 1
        and tdo.id = #{demandOrderId,jdbcType=BIGINT}
    </select>


    <select id="searchDemandOrderListIdsForWebCarrier" resultType="java.lang.Long">
        SELECT
        DISTINCT tdo.id
        FROM
        t_demand_order tdo
        LEFT JOIN t_demand_order_address tdoa ON tdo.id = tdoa.demand_order_id and tdoa.valid=1
        LEFT JOIN t_demand_order_goods tdog ON tdo.id = tdog.demand_order_id and tdog.valid=1
        LEFT JOIN t_demand_order_carrier tdoc on tdoc.demand_order_id = tdo.id and tdoc.valid=1
        WHERE
        tdo.valid = 1
        and tdoc.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        <if test="params.demandOrderIds != null and params.demandOrderIds !=''">
            and tdo.id in (${params.demandOrderIds})
        </if>
        <if test="params.demandStatus!=null and params.demandStatus!=''">
            <choose>
                <when test='params.demandStatus=="1"'>
                    and (tdo.if_cancel=1 or tdo.if_rollback = 1 or tdo.company_carrier_id != #{companyCarrierId,jdbcType=BIGINT})
                </when>
                <when test="params.demandStatus==2000">
                    and tdo.status in (1000,2000) and tdo.if_cancel=0 and tdo.if_empty = 0 and tdo.if_rollback = 0 and tdo.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
                </when>
                <when test='params.demandStatus=="3"'>
                    and tdo.entrust_status = 5000 and tdo.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
                </when>
                <when test='params.demandStatus=="4"'>
                    and tdo.entrust_status = 5000 and tdo.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
                </when>
                <otherwise>
                    and tdo.status =#{params.demandStatus} and tdo.if_cancel=0 and tdo.if_empty = 0 and tdo.if_rollback = 0 and tdo.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
                </otherwise>
            </choose>
        </if>
        <if test="params.companyEntrustName!=null and params.companyEntrustName!=''">
            and instr(tdo.company_entrust_name, #{params.companyEntrustName,jdbcType=VARCHAR})
        </if>
        <if test="params.demandOrderCode!=null and params.demandOrderCode!=''">
            and instr(tdo.demand_order_code,#{params.demandOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="params.customerOrderCode!=null and params.customerOrderCode!=''">
            and if(tdo.order_type = 21, instr(tdo.sinopec_order_no, #{params.customerOrderCode,jdbcType=VARCHAR}) > 0,
            instr(tdo.customer_order_code, #{params.customerOrderCode,jdbcType=VARCHAR}) > 0)
        </if>
        <if test="params.expectedLoadTimeStart!=null and params.expectedLoadTimeStart!=''">
            and tdoa.expected_load_time &gt;= DATE_FORMAT(#{params.expectedLoadTimeStart,jdbcType=VARCHAR},'%Y-%m-%d
            %k:%i:%S')
        </if>
        <if test="params.expectedLoadTimeEnd!=null and params.expectedLoadTimeEnd!=''">
            and tdoa.expected_load_time &lt;= DATE_FORMAT(#{params.expectedLoadTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d
            23:59:59')
        </if>
        <if test="params.expectedUnloadTimeStart!=null and params.expectedUnloadTimeStart!=''">
            and tdoa.expected_unload_time &gt;= DATE_FORMAT(#{params.expectedUnloadTimeStart,jdbcType=VARCHAR},'%Y-%m-%d
            %k:%i:%S')
        </if>
        <if test="params.expectedUnloadTimeEnd!=null and params.expectedUnloadTimeEnd!=''">
            and tdoa.expected_unload_time &lt;= DATE_FORMAT(#{params.expectedUnloadTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d
            23:59:59')
        </if>
        <if test="params.publishTimeStart!=null and params.publishTimeStart!=''">
            and tdo.publish_time &gt;= DATE_FORMAT(#{params.publishTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %k:%i:%S')
        </if>
        <if test="params.publishTimeEnd!=null and params.publishTimeEnd!=''">
            and tdo.publish_time &lt;= DATE_FORMAT(#{params.publishTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.goodsName!=null and params.goodsName!=''">
            and instr(tdog.goods_name,#{params.goodsName,jdbcType=VARCHAR})
        </if>
        <if test="params.goodsSize!=null and params.goodsSize!=''">
            and (
            instr(tdog.goods_size,#{params.goodsSize,jdbcType=VARCHAR})
            OR instr(CONCAT(tdog.length,'*',tdog.width, '*',tdog.height),#{params.goodsSize,jdbcType=VARCHAR})
            )
        </if>
        <if test="params.loadDetailAddress!=null and params.loadDetailAddress!=''">
            and instr(CONCAT(tdoa.load_province_name,tdoa.load_city_name,tdoa.load_area_name,tdoa.load_detail_address),#{params.loadDetailAddress,jdbcType=VARCHAR})
        </if>

        <if test="params.unloadDetailAddress!=null and params.unloadDetailAddress!=''">
            and instr(CONCAT(tdoa.unload_province_name,tdoa.unload_city_name,tdoa.unload_area_name,tdoa.unload_detail_address),#{params.unloadDetailAddress,jdbcType=VARCHAR})
        </if>
        <if test="params.consignorMobileAndReceiver != null and params.consignorMobileAndReceiver != ''">
            and (instr(tdoa.consignor_name,#{params.consignorMobileAndReceiver,jdbcType=VARCHAR}) > 0
            or instr(tdoa.consignor_mobile,#{params.consignorMobileAndReceiver,jdbcType=VARCHAR}) > 0
            or instr(tdoa.receiver_name,#{params.consignorMobileAndReceiver,jdbcType=VARCHAR}) > 0
            or instr(tdoa.receiver_mobile,#{params.consignorMobileAndReceiver,jdbcType=VARCHAR}) > 0)
        </if>
        <if test="params.recycleTaskType != null">
            and tdo.recycle_task_type = #{params.recycleTaskType,jdbcType=INTEGER}
        </if>
        <if test="params.entrustType != null">
            and tdo.entrust_type = #{params.entrustType,jdbcType=INTEGER}
        </if>
        <if test="params.ifExtDemandOrder != null and params.ifExtDemandOrder != ''">
            and tdo.if_ext_demand_order = #{params.ifExtDemandOrder,jdbcType=VARCHAR}
        </if>
        <if test="params.demandOrderCodeList != null and params.demandOrderCodeList.size > 0">
            and tdo.demand_order_code in
            <foreach collection="params.demandOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.customerOrderCodeList != null and params.customerOrderCodeList.size > 0">
            and tdo.customer_order_code in
            <foreach collection="params.customerOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.excludeDemandIdList != null and params.excludeDemandIdList.size() > 0">
            and tdo.id not in
            <foreach collection="params.excludeDemandIdList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="params.sort!=null and params.sort!='' and params.order!=null and params.order!=''">
            order by ${params.sort} ${params.order}
        </if>
        <if test="params.sort!=null and params.sort!='' and (params.order==null or params.order=='')">
            order by ${params.sort}
        </if>
        <if test="params.sort==null or params.sort==''">
            order by tdo.id desc
        </if>
    </select>

    <resultMap type="com.logistics.tms.controller.demandorder.response.WebDemandOrderResponseModel"
               id="getSearchListWebResult">
        <id column="id" property="demandId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="entrust_status" property="entrustStatus" jdbcType="INTEGER"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="if_rollback" property="ifRollback" jdbcType="INTEGER"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="sinopec_order_no" property="sinopecOrderNo" jdbcType="VARCHAR"/>
        <result column="order_type" property="orderType" jdbcType="VARCHAR"/>
        <result column="publish_name" property="publishName" jdbcType="VARCHAR"/>
        <result column="publish_time" property="publishTime" jdbcType="TIMESTAMP"/>
        <result column="goods_amount" property="goodsAmount" jdbcType="DECIMAL"/>
        <result column="arranged_amount" property="arrangedAmount" jdbcType="DECIMAL"/>
        <result column="not_arranged_amount" property="notArrangedAmount" jdbcType="DECIMAL"/>
        <result column="entrust_type" property="entrustType" jdbcType="INTEGER"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="source" property="source" jdbcType="INTEGER"/>
        <result column="back_amount" property="backAmount" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="cancel_reason" property="cancelReason" jdbcType="VARCHAR"/>
        <result column="rollback_remark" property="rollbackRemark" jdbcType="VARCHAR"/>
        <result column="load_province_id" property="loadProvinceId" jdbcType="BIGINT"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_id" property="loadCityId" jdbcType="BIGINT"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_id" property="loadAreaId" jdbcType="BIGINT"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="consignor_name" property="consignorName" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR"/>
        <result column="expected_load_time" property="expectedLoadTime" jdbcType="VARCHAR"/>
        <result column="unload_province_id" property="unloadProvinceId" jdbcType="BIGINT"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_id" property="unloadCityId" jdbcType="BIGINT"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_id" property="unloadAreaId" jdbcType="BIGINT"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR"/>
        <result column="expected_unload_time" property="expectedUnloadTime" jdbcType="TIMESTAMP"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="company_entrust_id" property="companyEntrustId" jdbcType="BIGINT"/>
        <result column="company_entrust_name" property="companyEntrustName" jdbcType="VARCHAR"/>
        <result column="carrier_contact_id" property="carrierContactId" jdbcType="BIGINT"/>
        <result column="recycle_task_type" property="recycleTaskType" jdbcType="INTEGER"/>
        <result column="if_ext_demand_order" property="ifExtDemandOrder" jdbcType="INTEGER"/>


        <collection property="goodsResponseModels" column="demandId"
                    ofType="com.logistics.tms.controller.demandorder.response.DemandOrderGoodsResponseModel"
                    javaType="ArrayList">
            <id column="demandOrderGoodsId" property="demandOrderGoodsId" jdbcType="BIGINT"/>
            <result column="demandOrderId" property="demandOrderId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="goods_size" property="goodsSize" jdbcType="INTEGER"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goodsAmountNumber" property="goodsAmountNumber" jdbcType="DECIMAL"/>
            <result column="arrangedAmountNumber" property="arrangedAmountNumber" jdbcType="DECIMAL"/>
            <result column="notArrangedAmountNumber" property="notArrangedAmountNumber" jdbcType="DECIMAL"/>
            <result column="backAmountNumber" property="backAmountNumber" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>
    <select id="searchDemandOrderListForWebCarrier" resultMap="getSearchListWebResult">
        SELECT
        tdo.id,
        tdo.status,
        tdo.entrust_status,
        tdo.if_cancel,
        tdo.if_empty,
        tdo.if_rollback,
        tdo.demand_order_code,
        if(entrust_type=0,customer_order_code,'') as customer_order_code,
        tdo.sinopec_order_no,
        tdo.order_type,
        tdo.publish_name,
        tdo.publish_time,
        tdo.goods_amount,
        tdo.arranged_amount,
        tdo.not_arranged_amount,
        tdo.cancel_reason,
        tdo.rollback_remark,
        tdo.remark,
        tdo.entrust_type,
        tdo.back_amount,
        tdo.goods_unit,
        tdo.source,
        tdo.company_entrust_id,
        tdo.company_entrust_name,
        tdo.company_carrier_id,
        tdo.carrier_contact_id,
        tdo.recycle_task_type,
        tdo.if_ext_demand_order,

        tdoa.load_province_id,
        tdoa.load_province_name,
        tdoa.load_city_id,
        tdoa.load_city_name,
        tdoa.load_area_id,
        tdoa.load_area_name,
        tdoa.load_detail_address,
        tdoa.load_warehouse,
        tdoa.consignor_name,
        tdoa.consignor_mobile,
        tdoa.expected_load_time,
        tdoa.unload_province_id,
        tdoa.unload_province_name,
        tdoa.unload_city_id,
        tdoa.unload_city_name,
        tdoa.unload_area_id,
        tdoa.unload_area_name,
        tdoa.unload_detail_address,
        tdoa.unload_warehouse,
        tdoa.receiver_name,
        tdoa.receiver_mobile,
        tdoa.expected_unload_time,

        tdog.id as demandOrderGoodsId,
        tdog.demand_order_id as demandOrderId,
        tdog.goods_name,
        tdog.length,
        tdog.width,
        tdog.height,
        tdog.goods_size,
        tdog.goods_amount as goodsAmountNumber,
        tdog.arranged_amount as arrangedAmountNumber,
        tdog.not_arranged_amount as notArrangedAmountNumber,
        tdog.back_amount as backAmountNumber
        FROM
        t_demand_order tdo
        LEFT JOIN t_demand_order_address tdoa ON tdo.id = tdoa.demand_order_id and tdoa.valid=1
        LEFT JOIN t_demand_order_goods tdog ON tdo.id = tdog.demand_order_id and tdog.valid=1
        WHERE tdo.valid = 1
        <if test="demandOrderIds!=null and demandOrderIds!=''">
            and tdo.id in (${demandOrderIds})
        </if>
        <if test="params.sort!=null and params.sort!='' and params.order!=null and params.order!=''">
            order by ${params.sort} ${params.order}
        </if>
        <if test="params.sort!=null and params.sort!='' and (params.order==null or params.order=='')">
            order by ${params.sort}
        </if>
        <if test="params.sort==null or params.sort==''">
            order by tdo.id desc
        </if>
    </select>

    <select id="searchDemandOrderListForWebCarrierStatistics"
            resultType="com.logistics.tms.controller.demandorder.response.WebDemandOrderSearchAccountResponseModel">
        select
        ifnull(sum(if(tdo.status in (1000,2000,3000),1,0)),0) as allAccount,
        ifnull(sum(if(tdo.if_cancel = 0 and tdo.if_empty = 0 and tdo.if_rollback = 0 and tdo.status = 1000 ,1,0)),0) as waitAccount,
        ifnull(sum(if(tdo.if_cancel = 0 and tdo.if_empty = 0 and tdo.if_rollback = 0 and tdo.status = 2000  ,1,0)),0) as partAccount,
        ifnull(sum(if(tdo.if_cancel = 0 and tdo.if_empty = 0 and tdo.if_rollback = 0 and tdo.status = 3000  ,1,0)),0) as completeAccount,
        ifnull(sum(if(tdo.if_cancel = 1 or tdo.if_rollback = 1 ,1,0)),0) as cancelAccount
        from t_demand_order tdo
        where tdo.valid = 1
        and tdo.id in (${demandOrderIds})
        and tdo.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
    </select>

    <select id="searchDemandListForStatisticsChangeCarrier" resultType="com.logistics.tms.controller.demandorder.response.SearchDemandListForStatisticsChangeCarrierModel">
        select
        count(tdoc.demand_order_id) as cancelAccount
        from (
            select
            tdoc.demand_order_id
            from t_demand_order_carrier tdoc
            left join t_demand_order tdo on tdo.id = tdoc.demand_order_id and tdo.valid=1
            where tdoc.valid=1
            and tdo.id in (${demandOrderIds})
            and tdo.company_carrier_id != #{companyCarrierId,jdbcType=BIGINT}
            group by tdoc.demand_order_id
            ) tdoc
    </select>

    <select id="searchYeloLifeDemandOrderListIds" resultType="java.lang.Long">
        SELECT
        DISTINCT tdo.id
        FROM t_demand_order tdo
        LEFT JOIN t_demand_order_address tdoa ON tdo.id = tdoa.demand_order_id and tdoa.valid=1
        LEFT JOIN t_demand_order_goods tdog ON tdo.id = tdog.demand_order_id and tdog.valid=1
        WHERE tdo.valid = 1 and tdo.source = 5
        <if test="params.demandIds!=null and params.demandIds!=''">
            and tdo.id in (${params.demandIds})
        </if>
        <if test="params.demandStatus!=null and params.demandStatus!=''">
            <choose>
                <!-- 已取消-->
                <when test="params.demandStatus==1">
                    and tdo.if_cancel=1
                </when>
                <otherwise>
                    and tdo.entrust_status =#{params.demandStatus} and tdo.if_cancel=0 and tdo.if_empty=0 and tdo.if_rollback = 0
                </otherwise>
            </choose>
        </if>
        <if test="params.demandOrderCode!=null and params.demandOrderCode!=''">
            and instr(tdo.demand_order_code,#{params.demandOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="params.customerName != null and params.customerName != ''">
            and
            (IF(tdo.business_type = 1, instr(tdo.customer_name,#{params.customerName,jdbcType=VARCHAR}) > 0, (instr(tdo.customer_user_name,#{params.customerName,jdbcType=VARCHAR}) > 0 or
            instr(AES_DECRYPT(UNHEX(tdo.customer_user_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'),#{params.customerName,jdbcType=VARCHAR}) > 0 )))
        </if>
        <if test="params.loadWarehouse!=null and params.loadWarehouse!=''">
            and instr(tdoa.load_warehouse,#{params.loadWarehouse,jdbcType=VARCHAR})
        </if>
        <if test="params.loadDetailAddress!=null and params.loadDetailAddress!=''">
            and instr(CONCAT(tdoa.load_province_name,tdoa.load_city_name,tdoa.load_area_name,tdoa.load_detail_address,tdoa.load_warehouse),#{params.loadDetailAddress,jdbcType=VARCHAR})
        </if>
        <if test="params.consignor!=null and params.consignor!=''">
            and (instr(tdoa.consignor_name,#{params.consignor,jdbcType=VARCHAR}) or
            instr(tdoa.consignor_mobile,#{params.consignor,jdbcType=VARCHAR}))
        </if>
        <if test="params.unloadWarehouse!=null and params.unloadWarehouse!=''">
            and instr(tdoa.unload_warehouse,#{params.unloadWarehouse,jdbcType=VARCHAR})
        </if>
        <if test="params.unloadDetailAddress!=null and params.unloadDetailAddress!=''">
            and instr(CONCAT(tdoa.unload_province_name,tdoa.unload_city_name,tdoa.unload_area_name,tdoa.unload_detail_address,tdoa.unload_warehouse),#{params.unloadDetailAddress,jdbcType=VARCHAR})
        </if>
        <if test="params.receiver!=null and params.receiver!=''">
            and (instr(tdoa.receiver_name,#{params.receiver,jdbcType=VARCHAR}) or
            instr(tdoa.receiver_mobile,#{params.receiver,jdbcType=VARCHAR}))
        </if>
        <if test="params.publishStartTime!=null and params.publishStartTime!=''">
            and tdo.publish_time >= DATE_FORMAT(#{params.publishStartTime,jdbcType=VARCHAR},'%Y-%m-%d %k:%i:%S')
        </if>
        <if test="params.publishEndTime!=null and params.publishEndTime!=''">
            and tdo.publish_time &lt;= DATE_FORMAT(#{params.publishEndTime,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.customerOrderSource!=null">
            and tdo.customer_order_source = #{params.customerOrderSource,jdbcType=INTEGER}
        </if>
        <if test="params.goodsName!=null and params.goodsName!=''">
            and instr(tdog.goods_name,#{params.goodsName,jdbcType=VARCHAR})
        </if>
        <if test="params.customerNo!=null and params.customerNo!=''">
            and instr(tdo.customer_order_code,#{params.customerNo,jdbcType=VARCHAR})
        </if>
        <if test="params.demandOrderCodeList != null and params.demandOrderCodeList.size > 0">
            and tdo.demand_order_code in
            <foreach collection="params.demandOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.customerOrderCodeList != null and params.customerOrderCodeList.size > 0">
            and tdo.customer_order_code in
            <foreach collection="params.customerOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.entrustType!=null ">
            and tdo.entrust_type = #{params.entrustType,jdbcType=INTEGER}
        </if>
        <if test="params.sort!=null and params.sort!=''">
            order by ${params.sort} ${params.order},tdo.id desc
        </if>
        <if test="params.sort==null">
            order by tdo.id desc
        </if>
    </select>

    <resultMap id="searchYeloLifeDemandOrderList_map" type="com.logistics.tms.controller.demandorder.response.RenewableDemandOrderResponseModel">
        <id column="id" property="demandId" jdbcType="BIGINT"/>
        <result column="entrust_status" property="status" jdbcType="INTEGER"/>
        <result column="if_cancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
        <result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
        <result column="customer_user_name" property="customerUserName" jdbcType="VARCHAR"/>
        <result column="customer_user_mobile" property="customerUserMobile" jdbcType="VARCHAR"/>
        <result column="customer_order_source" property="customerOrderSource" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="INTEGER"/>
        <result column="publish_time" property="publishTime" jdbcType="TIMESTAMP"/>
        <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="goods_amount" property="goodsAmount" jdbcType="DECIMAL"/>
        <result column="arranged_amount" property="arrangedAmount" jdbcType="DECIMAL"/>
        <result column="not_arranged_amount" property="notArrangedAmount" jdbcType="DECIMAL"/>
        <result column="back_amount" property="backAmount" jdbcType="DECIMAL"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="consignor_name" property="consignorName" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR"/>
        <result column="expected_load_time" property="expectedLoadTime" jdbcType="TIMESTAMP"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR"/>
        <result column="expected_unload_time" property="expectedUnloadTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="entrust_type" property="entrustType" jdbcType="INTEGER"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="company_carrier_name" property="companyCarrierName" jdbcType="VARCHAR"/>
        <result column="company_carrier_type" property="companyCarrierType" jdbcType="INTEGER"/>
        <result column="carrier_contact_name" property="carrierContactName" jdbcType="VARCHAR"/>
        <result column="carrier_contact_phone" property="carrierContactPhone" jdbcType="VARCHAR"/>

        <collection property="goodsResponseModels" ofType="com.logistics.tms.controller.demandorder.response.DemandOrderGoodsResponseModel" javaType="ArrayList">
            <id column="demandOrderGoodsId" property="demandOrderGoodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <select id="searchYeloLifeDemandOrderList" resultMap="searchYeloLifeDemandOrderList_map">
        SELECT
        tdo.id,
        tdo.entrust_status,
        tdo.if_cancel,
        tdo.demand_order_code,
        tdo.customer_order_code,
        tdo.customer_name,
        tdo.customer_user_name,
        AES_DECRYPT(UNHEX(tdo.customer_user_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as customer_user_mobile,
        tdo.customer_order_source,
        tdo.business_type,
        tdo.publish_time,
        tdo.goods_amount,
        tdo.arranged_amount,
        tdo.not_arranged_amount,
        tdo.back_amount,
        tdo.remark,
        tdo.entrust_type,
        tdo.goods_unit,
        tdo.company_carrier_id,
        tdo.company_carrier_name,
        tdo.company_carrier_type,
        tdo.carrier_contact_name,
        AES_DECRYPT(UNHEX(tdo.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrier_contact_phone,

        tdoa.load_province_name,
        tdoa.load_city_name,
        tdoa.load_area_name,
        tdoa.load_detail_address,
        tdoa.load_warehouse,
        tdoa.consignor_name,
        tdoa.consignor_mobile,
        tdoa.expected_load_time,
        tdoa.unload_province_name,
        tdoa.unload_city_name,
        tdoa.unload_area_name,
        tdoa.unload_detail_address,
        tdoa.unload_warehouse,
        tdoa.receiver_name,
        tdoa.receiver_mobile,
        tdoa.expected_unload_time,

        tdog.id as demandOrderGoodsId,
        tdog.goods_name
        FROM t_demand_order tdo
        LEFT JOIN t_demand_order_goods tdog ON tdo.id = tdog.demand_order_id and tdog.valid=1
        LEFT JOIN t_demand_order_address tdoa ON tdo.id = tdoa.demand_order_id and tdoa.valid=1
        WHERE tdo.valid = 1
        <if test="demandOrderIds!=null and demandOrderIds!=''">
            and tdo.id in (${demandOrderIds})
        </if>
        <if test="params.sort!=null and params.sort!=''">
            order by ${params.sort} ${params.order},tdo.id desc
        </if>
        <if test="params.sort==null">
            order by tdo.id desc
        </if>
    </select>

    <select id="searchYeloLifeDemandOrderStatisticsListIds" resultType="com.logistics.tms.controller.demandorder.response.RenewableDemandListStatisticsResponseModel">
        SELECT
        ifnull(count(0),0) as allcount,
        ifnull(sum(if(tdo.if_cancel = 0 and tdo.entrust_status = 500,1,0)),0) as waitPublishCount,
        ifnull(sum(if(tdo.if_cancel = 0 and tdo.entrust_status = 1000,1,0)),0) as waitDispatchCount,
        ifnull(sum(if(tdo.if_cancel = 0 and tdo.entrust_status = 2000,1,0)),0) as partDispatchCount,
        ifnull(sum(if(tdo.if_cancel = 0 and tdo.entrust_status = 3000,1,0)),0) as dispatchCompleteCount,
        ifnull(sum(if(tdo.if_cancel = 0 and tdo.entrust_status = 4000,1,0)),0) as waitSignedAccount,
        ifnull(sum(if(tdo.if_cancel = 0 and tdo.entrust_status = 5000,1,0)),0) as signedAccount,
        ifnull(sum(if(tdo.if_cancel=1,1,0)),0) as cancelCount
        from (
        select tdo.if_cancel,tdo.if_empty,tdo.if_rollback,tdo.entrust_status
        FROM t_demand_order tdo
        LEFT JOIN t_demand_order_address tdoa ON tdo.id = tdoa.demand_order_id and tdoa.valid=1
        LEFT JOIN t_demand_order_goods tdog ON tdo.id = tdog.demand_order_id and tdog.valid=1
        WHERE tdo.valid = 1 and tdo.source = 5
        <if test="params.demandIds!=null and params.demandIds!=''">
            and tdo.id in (${params.demandIds})
        </if>
        <if test="params.demandOrderCode!=null and params.demandOrderCode!=''">
            and instr(tdo.demand_order_code,#{params.demandOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="params.customerName != null and params.customerName != ''">
            and
            (IF(tdo.business_type = 1, instr(tdo.customer_name,#{params.customerName,jdbcType=VARCHAR}) > 0, (instr(tdo.customer_user_name,#{params.customerName,jdbcType=VARCHAR}) > 0 or
            instr(AES_DECRYPT(UNHEX(tdo.customer_user_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'),#{params.customerName,jdbcType=VARCHAR}) > 0 )))
        </if>
        <if test="params.loadWarehouse!=null and params.loadWarehouse!=''">
            and instr(tdoa.load_warehouse,#{params.loadWarehouse,jdbcType=VARCHAR})
        </if>
        <if test="params.loadDetailAddress!=null and params.loadDetailAddress!=''">
            and instr(CONCAT(tdoa.load_province_name,tdoa.load_city_name,tdoa.load_area_name,tdoa.load_detail_address,tdoa.load_warehouse),#{params.loadDetailAddress,jdbcType=VARCHAR})
        </if>
        <if test="params.consignor!=null and params.consignor!=''">
            and (instr(tdoa.consignor_name,#{params.consignor,jdbcType=VARCHAR}) or
            instr(tdoa.consignor_mobile,#{params.consignor,jdbcType=VARCHAR}))
        </if>
        <if test="params.unloadWarehouse!=null and params.unloadWarehouse!=''">
            and instr(tdoa.unload_warehouse,#{params.unloadWarehouse,jdbcType=VARCHAR})
        </if>
        <if test="params.unloadDetailAddress!=null and params.unloadDetailAddress!=''">
            and instr(CONCAT(tdoa.unload_province_name,tdoa.unload_city_name,tdoa.unload_area_name,tdoa.unload_detail_address,tdoa.unload_warehouse),#{params.unloadDetailAddress,jdbcType=VARCHAR})
        </if>
        <if test="params.receiver!=null and params.receiver!=''">
            and (instr(tdoa.receiver_name,#{params.receiver,jdbcType=VARCHAR}) or
            instr(tdoa.receiver_mobile,#{params.receiver,jdbcType=VARCHAR}))
        </if>
        <if test="params.publishStartTime!=null and params.publishStartTime!=''">
            and tdo.publish_time >= DATE_FORMAT(#{params.publishStartTime,jdbcType=VARCHAR},'%Y-%m-%d %k:%i:%S')
        </if>
        <if test="params.publishEndTime!=null and params.publishEndTime!=''">
            and tdo.publish_time &lt;= DATE_FORMAT(#{params.publishEndTime,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.customerOrderSource!=null">
            and tdo.customer_order_source = #{params.customerOrderSource,jdbcType=INTEGER}
        </if>
        <if test="params.goodsName!=null and params.goodsName!=''">
            and instr(tdog.goods_name,#{params.goodsName,jdbcType=VARCHAR})
        </if>
        <if test="params.customerNo!=null and params.customerNo!=''">
            and instr(tdo.customer_order_code,#{params.customerNo,jdbcType=VARCHAR})
        </if>
        <if test="params.demandOrderCodeList != null and params.demandOrderCodeList.size > 0">
            and tdo.demand_order_code in
            <foreach collection="params.demandOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.customerOrderCodeList != null and params.customerOrderCodeList.size > 0">
            and tdo.customer_order_code in
            <foreach collection="params.customerOrderCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.entrustType!=null ">
            and tdo.entrust_type = #{params.entrustType,jdbcType=INTEGER}
        </if>
        group by tdo.id
        ) tdo
    </select>

    <select id="renewableDemandOrderDetail" resultType="com.logistics.tms.controller.demandorder.response.RenewableDemandOrderDetailResponseModel">
        SELECT
        tdo.id                                                                                                                  as demandId,
        tdo.entrust_status                                                                                                      as entrustStatus,
        tdo.if_cancel                                                                                                           as ifCancel,
        tdo.customer_name                                                                                                       as customerName,
        tdo.customer_user_name                                                                                                  as customerUserName,
        AES_DECRYPT(UNHEX(tdo.customer_user_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as customerUserMobile,
        tdo.customer_order_code                                                                                                 as customerOrderCode,
        tdo.business_type                                                                                                       as businessType,
        tdo.demand_order_code                                                                                                   as demandOrderCode,
        tdo.publish_name                                                                                                        as publishName,
        AES_DECRYPT(UNHEX(tdo.publish_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as publishMobile,
        tdo.publish_time                                                                                                        as publishTime,
        tdo.remark                                                                                                              as remark,
        tdo.expect_contract_price_type                                                                                          as expectContractPriceType,
        tdo.expect_contract_price                                                                                               as expectContractPrice,
        tdo.contract_price_type                                                                                                 as contractPriceType,
        tdo.contract_price                                                                                                      as contractPrice,
        tdo.goods_unit                                                                                                          as goodsUnit,
        tdo.goods_amount                                                                                                        as goodsAmount,
        tdo.company_carrier_id                                                                                                  as companyCarrierId,
        tdo.company_carrier_name                                                                                                as companyCarrierName,
        tdo.company_carrier_type as companyCarrierType,
        tdo.carrier_contact_name as carrierContactName,
        AES_DECRYPT(UNHEX(tdo.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactPhone,
        tdo.customer_order_source                                                                                               as customerOrderSource,

        tdoa.load_province_name                                                                                                 as loadProvinceName,
        tdoa.load_city_name                                                                                                     as loadCityName,
        tdoa.load_area_name                                                                                                     as loadAreaName,
        tdoa.load_detail_address                                                                                                as loadDetailAddress,
        tdoa.load_warehouse                                                                                                     as loadWarehouse,
        tdoa.consignor_name                                                                                                     as consignorName,
        tdoa.consignor_mobile                                                                                                   as consignorMobile,
        tdoa.expected_load_time                                                                                                 as expectedLoadTime,
        tdoa.unload_province_name                                                                                               as unloadProvinceName,
        tdoa.unload_city_name                                                                                                   as unloadCityName,
        tdoa.unload_area_name                                                                                                   as unloadAreaName,
        tdoa.unload_detail_address                                                                                              as unloadDetailAddress,
        tdoa.unload_warehouse                                                                                                   as unloadWarehouse,
        tdoa.receiver_name                                                                                                      as receiverName,
        tdoa.receiver_mobile                                                                                                    as receiverMobile,
        tdoa.expected_unload_time                                                                                               as expectedUnloadTime,
        tco.level                                                                                                               as isOurCompany
        FROM t_demand_order tdo
        LEFT JOIN t_demand_order_address tdoa on tdo.id = tdoa.demand_order_id and tdoa.valid = 1
        LEFT JOIN t_company_carrier tco on tco.id = tdo.company_carrier_id and tco.valid = 1
        WHERE tdo.valid = 1
          and tdo.id = #{params.demandId}
    </select>

    <select id="selectFixedDemandOrder" resultType="com.logistics.tms.biz.demandorder.model.FixedDemandOrderModel">
        SELECT
        tdo.id                 as demandId,
        tdo.demand_order_code  as demandOrderCode,
        tdo.fixed_demand       as fixedDemand
        FROM t_demand_order tdo
        WHERE tdo.valid = 1
          and tdo.fixed_demand != ''
          and tdo.entrust_type = 1
          and tdo.status = 500
          and tdo.if_cancel = 0
          and tdo.if_empty = 0
          and tdo.if_rollback = 0
          and tdo.created_time >= current_date - 3
        limit 100
    </select>

    <select id="selectAutoPublishWaitDispatchDemandOrders" resultType="com.logistics.tms.biz.demandorder.model.AutoPublishWaitDispatchDemandModel">
        select
        id                as demandOrderId,
        demand_order_code as demandOrderCode,
        entrust_type      as entrustType,
        goods_amount      as goodsAmount,
        fixed_demand      as fixedDemand,
        auto_publish      as autoPublish,
        publish_time      as publishTime
        from t_demand_order
        where valid = 1
          and status = #{status,jdbcType=INTEGER}
          and id in
        <foreach collection="ids" open="(" separator="," close=")" item="item">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectAutoPublishRecycleDemandOrder" resultType="com.logistics.tms.biz.demandorder.model.AutoPublishRecycleDemandOrderModel">
        SELECT
        tdo.id                  as demandId,
        tdo.demand_order_code   as demandOrderCode,
        tdo.fixed_demand        as configCode,
        tdo.goods_amount        as goodsAmount,
        tdo.entrust_type        as entrustType,
        tdoa.expected_load_time as expectedLoadTime
        FROM t_demand_order tdo
        left join t_demand_order_address tdoa on tdo.id = tdoa.demand_order_id and tdoa.valid = 1
        WHERE tdo.valid = 1
          and tdo.fixed_demand != ''
          and tdo.entrust_type = 2
          and tdo.status = 500
          and tdo.if_cancel = 0
          and tdo.if_empty = 0
          and tdo.if_rollback = 0
          and current_timestamp > DATE_ADD(tdo.created_time, INTERVAL 30 MINUTE)
          and tdo.created_time >= current_date - 3
        limit 100
    </select>

    <select id="demandOrderStatistics" resultType="com.logistics.tms.controller.homepage.response.DemandOrderStatisticsResponseModel">
        SELECT
        ifnull(sum(if(tdo.if_cancel = 0 and tdo.if_empty = 0 and tdo.if_rollback = 0 and tdo.entrust_status = 500, 1, 0)), 0)                                 as waitPublishCount,
        ifnull(sum(if(tdo.if_cancel = 0 and tdo.if_empty = 0 and tdo.if_rollback = 0 and (tdo.entrust_status = 2000 or tdo.entrust_status = 1000), 1, 0)), 0) as waitDispatchCount
        from (
        select tdo.if_cancel, tdo.if_empty, tdo.if_rollback, tdo.entrust_status
        FROM t_demand_order tdo
        WHERE tdo.valid = 1
          and source = 1
        <if test="excludeIds != null and excludeIds.size() > 0">
            and tdo.id not in
            <foreach collection="excludeIds" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        ) tdo
    </select>

    <select id="searchDemandOrderInfo" resultType="com.logistics.tms.controller.demandorder.response.SearchDemandOrderInfoResponseModel">
        SELECT
        tdo.entrust_status            as `status`,
        tdo.demand_order_code         as demandOrderCode,
        tdo.if_cancel                 as ifCancel,
        tdo.if_empty                  as ifEmpty,
        tdo.if_rollback               as ifRollback,
        tdo.publish_time              as publishTime,
        tdoa.consignor_name           as consignorName,
        tdoa.consignor_mobile         as consignorMobile,
        tdo.goods_unit                as goodsUnit,
        tdo.goods_amount              as goodsAmount,
        tdo.upstream_customer         as upstreamCustomer,
        tdoa.load_warehouse           as loadWarehouse,
        tdoa.load_province_name       as loadProvinceName,
        tdoa.load_city_name           as loadCityName,
        tdoa.load_area_name           as loadAreaName,
        tdoa.load_detail_address      as loadDetailAddress,
        group_concat(tdog.goods_name) as goodsName
        FROM t_demand_order tdo
        left join t_demand_order_address tdoa on tdo.id = tdoa.demand_order_id and tdoa.valid = 1
        left join t_demand_order_goods tdog on tdo.id = tdog.demand_order_id and tdog.valid = 1
        WHERE tdo.valid = 1
        and tdo.id in
        <foreach collection="demandOrderIds" open="(" separator="," close=")" item="item">
            #{item,jdbcType=BIGINT}
        </foreach>
        GROUP BY tdo.id
        order by tdo.publish_time desc, tdo.id desc
    </select>

    <select id="searchBiddingDemand" resultType="com.logistics.tms.controller.biddingorder.response.SearchBiddingDemandResponseModel">
        SELECT
        tdo.id as demandOrderId,
        tdo.demand_order_code as demandOrderCode,
        tdo.goods_amount as goodsCount,
        concat(tdoa.load_province_name,tdoa.load_city_name,tdoa.load_area_name,
            '-',tdoa.unload_province_name,tdoa.unload_city_name,tdoa.unload_area_name)   as route
        FROM t_demand_order tdo
        left join t_demand_order_address tdoa on tdo.id = tdoa.demand_order_id and tdoa.valid = 1
        WHERE tdo.valid = 1 and tdoa.valid = 1
          and tdo.source = 1
          and tdo.entrust_status = 500
          and tdo.if_cancel=0
          and tdo.entrust_type not in(2,10)
        <if test="params.loadAddress!=null and params.loadAddress!=''">
            and instr(concat(tdoa.load_province_name,tdoa.load_city_name,tdoa.load_area_name),#{params.loadAddress,jdbcType=VARCHAR})
        </if>
        <if test="params.unloadAddress!=null and params.unloadAddress!=''">
            and instr(concat(tdoa.unload_province_name,tdoa.unload_city_name,tdoa.unload_area_name),#{params.unloadAddress,jdbcType=VARCHAR})
        </if>
        <if test="params.goodsCountLow!=null and params.goodsCountHigh!=''">
            and tdo.goods_amount &lt;= #{params.goodsCountHigh,jdbcType=DECIMAL}
            and tdo.goods_amount &gt;= #{params.goodsCountLow,jdbcType=DECIMAL}
        </if>
    </select>


    <select id="selectQuoteDemand" resultType="com.logistics.tms.controller.biddingorder.response.BiddingQuoteDemandModel">
        SELECT
               tdo.id                                                                          as demandId,
               tdo.demand_order_code                                                           as demandOrderCode,
               tdo.goods_amount                                                                as goodsCount,
               concat(tdoa.load_province_name, tdoa.load_city_name, tdoa.load_area_name)       as loadAddress,
               concat(tdoa.unload_province_name, tdoa.unload_city_name, tdoa.unload_area_name) as unloadAddress
        FROM t_demand_order tdo
                 left join t_demand_order_address tdoa on tdo.id = tdoa.demand_order_id and tdoa.valid = 1
        WHERE tdo.valid = 1
          and tdoa.valid = 1
          and tdo.source = 1
        <if test="demandIds != null and demandIds.size() > 0">
            and tdo.id in
            <foreach collection="demandIds" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>
</mapper>