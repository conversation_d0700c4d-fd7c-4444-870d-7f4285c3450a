<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TWarehouseAddressMapper" >
    <select id="warehouseAddressList" resultType="com.logistics.tms.controller.warehouseaddress.response.WarehouseAddressListResponseModel">
        SELECT
        id as warehouseAddressId,
        warehouse as warehouse,
        province_id as provinceId,
        province_name as provinceName,
        city_id as cityId,
        city_name as cityName,
        area_id as areaId,
        enabled as enabled,
        company_entrust_name as companyEntrustName,
        area_name as areaName,
        last_modified_by as operateUserName,
        last_modified_time as operateTime
        from t_warehouse_address
        where valid = 1
        <if test="params.warehouseName !=null and params.warehouseName !='' ">
            and instr(warehouse,#{params.warehouseName,jdbcType = VARCHAR})
        </if>
        <if test="params.enabled !=null ">
            and enabled=#{params.enabled,jdbcType=INTEGER}
        </if>
        <if test="params.companyEntrustName !=null and params.companyEntrustName !='' ">
            and instr(company_entrust_name,#{params.companyEntrustName,jdbcType = VARCHAR})
        </if>
        <if test="params.address !=null and params.address !='' ">
            and (
              instr(province_name,#{params.address,jdbcType = VARCHAR}) or
              instr(city_name,#{params.address,jdbcType = VARCHAR}) or
              instr(area_name,#{params.address,jdbcType = VARCHAR})
            )
        </if>
        <if test="params.warehouseAddressIds != null and params.warehouseAddressIds !='' " >
            and id in (${params.warehouseAddressIds})
        </if>
        <if test="params.companyEntrustId !=null ">
            and company_entrust_id = #{params.companyEntrustId,jdbcType=BIGINT}
        </if>
        order by last_modified_time desc, id desc
    </select>

    <select id="getValidWarehouseByNameForSinoper" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_warehouse_address
        where valid = 1
        and company_entrust_name = #{companyEntrustName,jdbcType=VARCHAR}
        and warehouse = #{warehouse,jdbcType=VARCHAR}
        <choose>
            <when test="enabledTime != null and enabledTime != ''">
                and ((enabled = 1 and date_format(enabled_time, '%Y-%m-%d') &lt; date_format(#{enabledTime,jdbcType=VARCHAR}, '%Y-%m-%d') and date_format(enabled_time, '%Y-%m-%d') &lt; date_format(current_date(), '%Y-%m-%d'))
                or (enabled = 0 and date_format(enabled_time, '%Y-%m-%d') >= date_format(#{enabledTime,jdbcType=VARCHAR}, '%Y-%m-%d') and date_format(enabled_time, '%Y-%m-%d') &lt;= date_format(current_date(), '%Y-%m-%d')))
            </when>
            <otherwise>
                and enabled = 1
            </otherwise>
        </choose>
        LIMIT 1
    </select>

    <select id="getWarehouseByName" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_warehouse_address
        where valid = 1
        and warehouse = #{warehouse,jdbcType=VARCHAR}
        and company_entrust_id = #{companyEntrustId,jdbcType=BIGINT}
    </select>

    <select id="getDetail" resultType="com.logistics.tms.controller.warehouseaddress.response.WarehouseAddressDetailResponseModel">
        SELECT
         id as warehouseAddressId,
        warehouse as warehouse,
        province_id as provinceId,
        province_name as provinceName,
        city_id as cityId,
        company_entrust_name as companyEntrustName,
        city_name as cityName,
        area_id as areaId,
        area_name as areaName
        from t_warehouse_address
        where valid = 1 and id = #{warehouseId,jdbcType=BIGINT}
    </select>
    <update id="enable" parameterType="com.logistics.tms.controller.warehouseaddress.request.WarehouseAddressEnableRequestModel">
        update t_warehouse_address
        set enabled=#{requestModel.operateType,jdbcType=INTEGER},
        enabled_time=now(),
        last_modified_by=#{requestModel.operator,jdbcType=VARCHAR},
        last_modified_time=now()
        where id in (${requestModel.warehouseAddressIds})
        and valid=1
        and enabled!=#{requestModel.operateType,jdbcType=INTEGER}
    </update>

    <select id="currentEnableWarehouse" resultType="java.lang.String">
        select
        warehouse
        from t_warehouse_address
        where valid = 1
        and enabled = 1
        and date_format(enabled_time, '%Y-%m-%d') = DATE_SUB(CURRENT_DATE (), INTERVAL 1 DAY)
    </select>

    <select id="searchWarehouseAddress" resultType="com.logistics.tms.controller.warehouseaddress.response.SearchWarehouseAddressResponseModel">
        select
        id as warehouseAddressId,
        warehouse as warehouse,
        province_id as provinceId,
        province_name as provinceName,
        city_id as cityId,
        city_name as cityName,
        area_id as areaId,
        area_name as areaName
        from t_warehouse_address
        where valid = 1
        <if test="params.warehouseName != null and params.warehouseName != '' ">
            and instr(warehouse,#{params.warehouseName,jdbcType = VARCHAR})
        </if>
        <if test="params.enabled != null ">
            and enabled = #{params.enabled,jdbcType=INTEGER}
        </if>
        order by last_modified_time desc, id desc
    </select>
</mapper>