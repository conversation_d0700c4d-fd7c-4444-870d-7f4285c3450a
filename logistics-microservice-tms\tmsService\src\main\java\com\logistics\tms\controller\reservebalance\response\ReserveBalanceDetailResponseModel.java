package com.logistics.tms.controller.reservebalance.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ReserveBalanceDetailResponseModel {

    @ApiModelProperty(value = "申请月份")
    private String applyMonth;

    @ApiModelProperty(value = "充值金额")
    private BigDecimal applyAmount;

    @ApiModelProperty(value = "已冲销金额")
    private BigDecimal verificationAmount;

}
