package com.logistics.tms.api.impl.violationregulation;

import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.violationregulation.ViolationRegulationServiceApi;
import com.logistics.tms.api.feign.violationregulation.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.violationregulation.ViolationRegulationBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/6/3 14:27
 */
@RestController
public class ViolationRegulationServiceApiImpl implements ViolationRegulationServiceApi {
    @Autowired
    private ViolationRegulationBiz violationRegulationBiz;

    /**
     * 列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<SearchViolationRegulationListResponseModel> searchViolationRegulationList(@RequestBody SearchViolationRegulationListRequestModel requestModel) {
        return Result.success(violationRegulationBiz.searchViolationRegulationsInfo(requestModel));
    }

    /**
     * 详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<ViolationRegulationDetailResponseModel> getViolationRegulationDetail(@RequestBody ViolationRegulationDetailRequestModel requestModel) {
        return Result.success(violationRegulationBiz.getDetail(requestModel));
    }

    /**
     * 新增/修改"
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> saveOrModifyViolationRegulation(@RequestBody AddOrModifyViolationRegulationRequestModel requestModel) {
        violationRegulationBiz.saveViolationRegulation(requestModel);
        return Result.success(true);
    }

    /**
     * 删除
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> deleteViolationRegulation(@RequestBody DeleteViolationRegulationRequestModel requestModel) {
        violationRegulationBiz.deleteViolationRegulation(requestModel);
        return Result.success(true);
    }

    /**
     * 导出
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<ViolationRegulationListResponseModel>> export(@RequestBody SearchViolationRegulationListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        SearchViolationRegulationListResponseModel violationRegulationListModel = violationRegulationBiz.searchViolationRegulationsInfo(requestModel);
        return Result.success(violationRegulationListModel.getPageInfo().getList());
    }

    /**
     * 导入
     * @param requestModel
     * @return
     */
    @Override
    public Result<ImportViolationRegulationResponseModel> importViolationRegulations(@RequestBody ImportViolationRegulationRequestModel requestModel) {
        return Result.success(violationRegulationBiz.importViolationRegulation(requestModel));
    }
}
