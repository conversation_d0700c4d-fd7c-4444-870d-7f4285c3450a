package com.logistics.tms.api.feign.entrustsettlement.model;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2019/10/11 18:58
 */
@Data
public class EntrustSettlementListResponseModel {
    @ApiModelProperty("总单数")
    private Integer totalEntrustOrderCount = 0;
    @ApiModelProperty("总吨数")
    private BigDecimal totalWeightSettlementAmount = BigDecimal.ZERO;
    @ApiModelProperty("总件数")
    private BigDecimal totalPackageSettlementAmount = BigDecimal.ZERO;
    @ApiModelProperty("总金额")
    private BigDecimal totalSettlementCost = BigDecimal.ZERO;
    @ApiModelProperty("分页信息")
    private PageInfo<EntrustSettlementRowModel> pageInfo;
}
