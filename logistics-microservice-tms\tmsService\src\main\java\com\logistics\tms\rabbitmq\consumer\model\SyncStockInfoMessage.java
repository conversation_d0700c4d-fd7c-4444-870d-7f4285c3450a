package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author:lei.zhu
 * @date:2021/9/23 16:49:29
 */
@Data
public class SyncStockInfoMessage {
    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("入库数量")
    private BigDecimal stockInCount;

    @ApiModelProperty("入库状态")
    private Integer stockInState;

    @ApiModelProperty("入库备注信息")
    private String stockInRemark;

    @ApiModelProperty("入库单据路径列表")
    private List<String> stockInImagePathList;

    @ApiModelProperty("操作人")
    private String operatorName;


}
