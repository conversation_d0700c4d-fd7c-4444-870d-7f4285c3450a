<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TBusinessCodeMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TBusinessCode" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="business_type" property="businessType" jdbcType="INTEGER" />
    <result column="business_code_length" property="businessCodeLength" jdbcType="INTEGER" />
    <result column="business_code_prefix" property="businessCodePrefix" jdbcType="VARCHAR" />
    <result column="business_code_sequence" property="businessCodeSequence" jdbcType="INTEGER" />
    <result column="business_code_date_format" property="businessCodeDateFormat" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
    <result column="enabled" property="enabled" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, business_type, business_code_length, business_code_prefix, business_code_sequence, 
    business_code_date_format, created_by, created_time, last_modified_by, last_modified_time, 
    valid, enabled, remark
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_business_code
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_business_code
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TBusinessCode" >
    insert into t_business_code (id, business_type, business_code_length, 
      business_code_prefix, business_code_sequence, 
      business_code_date_format, created_by, created_time, 
      last_modified_by, last_modified_time, valid, 
      enabled, remark)
    values (#{id,jdbcType=BIGINT}, #{businessType,jdbcType=INTEGER}, #{businessCodeLength,jdbcType=INTEGER}, 
      #{businessCodePrefix,jdbcType=VARCHAR}, #{businessCodeSequence,jdbcType=INTEGER}, 
      #{businessCodeDateFormat,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}, 
      #{enabled,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TBusinessCode" >
    insert into t_business_code
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="businessType != null" >
        business_type,
      </if>
      <if test="businessCodeLength != null" >
        business_code_length,
      </if>
      <if test="businessCodePrefix != null" >
        business_code_prefix,
      </if>
      <if test="businessCodeSequence != null" >
        business_code_sequence,
      </if>
      <if test="businessCodeDateFormat != null" >
        business_code_date_format,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
      <if test="enabled != null" >
        enabled,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessType != null" >
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="businessCodeLength != null" >
        #{businessCodeLength,jdbcType=INTEGER},
      </if>
      <if test="businessCodePrefix != null" >
        #{businessCodePrefix,jdbcType=VARCHAR},
      </if>
      <if test="businessCodeSequence != null" >
        #{businessCodeSequence,jdbcType=INTEGER},
      </if>
      <if test="businessCodeDateFormat != null" >
        #{businessCodeDateFormat,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
      <if test="enabled != null" >
        #{enabled,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TBusinessCode" >
    update t_business_code
    <set >
      <if test="businessType != null" >
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="businessCodeLength != null" >
        business_code_length = #{businessCodeLength,jdbcType=INTEGER},
      </if>
      <if test="businessCodePrefix != null" >
        business_code_prefix = #{businessCodePrefix,jdbcType=VARCHAR},
      </if>
      <if test="businessCodeSequence != null" >
        business_code_sequence = #{businessCodeSequence,jdbcType=INTEGER},
      </if>
      <if test="businessCodeDateFormat != null" >
        business_code_date_format = #{businessCodeDateFormat,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
      <if test="enabled != null" >
        enabled = #{enabled,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TBusinessCode" >
    update t_business_code
    set business_type = #{businessType,jdbcType=INTEGER},
      business_code_length = #{businessCodeLength,jdbcType=INTEGER},
      business_code_prefix = #{businessCodePrefix,jdbcType=VARCHAR},
      business_code_sequence = #{businessCodeSequence,jdbcType=INTEGER},
      business_code_date_format = #{businessCodeDateFormat,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER},
      enabled = #{enabled,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>