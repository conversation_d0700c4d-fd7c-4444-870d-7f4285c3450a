package com.logistics.appapi.client.staff.hystrix;

import com.logistics.appapi.client.staff.StaffClient;
import com.logistics.appapi.client.staff.response.GetStaffDetailByAccountIdResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/3/7 17:07
 */
@Component
public class StaffClientHystrix implements StaffClient {
    @Override
    public Result<GetStaffDetailByAccountIdResponseModel> getStaffDetailByAccountId() {
        return Result.timeout();
    }
}
