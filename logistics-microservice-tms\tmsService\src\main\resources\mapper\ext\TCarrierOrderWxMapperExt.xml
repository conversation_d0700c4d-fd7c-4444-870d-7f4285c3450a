<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderWxMapper" >

  <select id="selectCarrierOrderWeixinPushInfoByCarrierOrder" resultType="com.logistics.tms.controller.carrierorder.response.GetCarrierOrderWeixinPushResponseModel">
    select
      id as id,
      role as role,
      `name` as name,
      mobile as mobile,
       if_push as ifPush
    from t_carrier_order_wx
    where valid = 1 and carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
  </select>
  
  <insert id="batchInsert" >
    <foreach collection="list" separator=";" item="item">
      insert into t_carrier_order_wx
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          id,
        </if>
        <if test="item.carrierOrderId != null">
          carrier_order_id,
        </if>
        <if test="item.role != null">
          role,
        </if>
        <if test="item.name != null">
          name,
        </if>
        <if test="item.mobile != null">
          mobile,
        </if>
        <if test="item.ifPush != null">
          if_push,
        </if>
        <if test="item.createdBy != null">
          created_by,
        </if>
        <if test="item.createdTime != null">
          created_time,
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time,
        </if>
        <if test="item.valid != null">
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.carrierOrderId != null">
          #{item.carrierOrderId,jdbcType=BIGINT},
        </if>
        <if test="item.role != null">
          #{item.role,jdbcType=INTEGER},
        </if>
        <if test="item.name != null">
          #{item.name,jdbcType=VARCHAR},
        </if>
        <if test="item.mobile != null">
          #{item.mobile,jdbcType=VARCHAR},
        </if>
        <if test="item.ifPush != null">
          #{item.ifPush,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null">
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>
  <update id="batchUpdate">
    <foreach collection="list" separator=";" item="item">
      update t_carrier_order_wx
      <set>
        <if test="item.carrierOrderId != null">
          carrier_order_id = #{item.carrierOrderId,jdbcType=BIGINT},
        </if>
        <if test="item.role != null">
          role = #{item.role,jdbcType=INTEGER},
        </if>
        <if test="item.name != null">
          name = #{item.name,jdbcType=VARCHAR},
        </if>
        <if test="item.mobile != null">
          mobile = #{item.mobile,jdbcType=VARCHAR},
        </if>
        <if test="item.ifPush != null">
          if_push = #{item.ifPush,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null">
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>