package com.logistics.management.webapi.api.impl.extvehiclesettlement.mapping;

import com.logistics.management.webapi.api.feign.extvehiclesettlement.dto.SearchExtVehicleSettlementListResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CarrierOrderStatusEnum;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.management.webapi.base.enums.VehicleSettleStatusEnum;
import com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel;
import com.logistics.tms.api.feign.extvehiclesettlement.model.SearchExtVehicleSettlementListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/11/21 14:17
 */
public class ExtVehicleSettlementListMapping extends MapperMapping<SearchExtVehicleSettlementListResponseModel,SearchExtVehicleSettlementListResponseDto> {
    @Override
    public void configure() {
        SearchExtVehicleSettlementListResponseModel source = getSource();
        SearchExtVehicleSettlementListResponseDto destination = getDestination();
        if (source != null){
            //支付状态
            destination.setPayStatusDesc(VehicleSettleStatusEnum.getEnum(source.getPayStatus()).getValue());

            //运单状态转换
            if (CommonConstant.INTEGER_ONE.equals(source.getIfEmpty()) ) {
                destination.setStatus(CarrierOrderStatusEnum.EMPTY.getKey().toString());
                destination.setStatusDesc(CarrierOrderStatusEnum.EMPTY.getValue());
            }else {
                destination.setStatusDesc(CarrierOrderStatusEnum.getEnum(source.getStatus()).getValue());
            }

            //拼接司机
            destination.setDriverName(source.getDriverName() + " " + source.getDriverMobile());

            //司机费用
            BigDecimal driverTotalFee = source.getDriverTotalFee();
            if (source.getDriverOtherFee() != null){
                driverTotalFee = driverTotalFee.add(source.getDriverOtherFee());
            }
            destination.setDriverTotalFee(ConverterUtils.toString(driverTotalFee.setScale(2, RoundingMode.HALF_UP)));

            //货物信息处理
            BigDecimal unloadCapacity = CommonConstant.BIG_DECIMAL_ZERO;
            List<SearchCarrierOrderListGoodsInfoModel> goodsList = source.getGoodsInfoList();
            if(ListUtils.isNotEmpty(goodsList)){
                StringBuilder goodsName = new StringBuilder();
                StringBuilder goodsSize = new StringBuilder();
                BigDecimal unloadCapacityTotal = CommonConstant.BIG_DECIMAL_ZERO;
                for(int index = 0;index<goodsList.size() ;index++){
                    SearchCarrierOrderListGoodsInfoModel tmpGoodsModel = goodsList.get(index);
                    if (source.getSettlementUnit().equals(GoodsUnitEnum.BY_VOLUME.getKey())) {
                        if (StringUtils.isNotBlank(tmpGoodsModel.getGoodsSize())){
                            tmpGoodsModel.setGoodsSize(tmpGoodsModel.getGoodsSize() + " " + tmpGoodsModel.getLength() + "*" + tmpGoodsModel.getWidth() + "*" + tmpGoodsModel.getHeight() + "mm");
                        }else {
                            tmpGoodsModel.setGoodsSize(tmpGoodsModel.getLength() + "*" + tmpGoodsModel.getWidth() + "*" + tmpGoodsModel.getHeight() + "mm");
                        }
                    }
                    if(index !=0){
                        goodsName.append("/");
                    }
                    if(StringUtils.isNotBlank(goodsSize.toString())&&StringUtils.isNotBlank(tmpGoodsModel.getGoodsSize())){
                        goodsSize.append("/");
                    }
                    goodsName.append(tmpGoodsModel.getGoodsName());
                    if(StringUtils.isNotBlank(tmpGoodsModel.getGoodsSize())){
                        goodsSize.append(tmpGoodsModel.getGoodsSize());
                    }

                    BigDecimal capacity = ConverterUtils.toBigDecimal(tmpGoodsModel.getLength()).multiply(ConverterUtils.toBigDecimal(tmpGoodsModel.getWidth())).multiply(ConverterUtils.toBigDecimal(tmpGoodsModel.getHeight()));
                    if (CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())){
                        unloadCapacityTotal = unloadCapacityTotal.add(tmpGoodsModel.getExpectAmount().multiply(capacity).divide(new BigDecimal(1000).pow(3), 3, BigDecimal.ROUND_HALF_UP));
                    }else {
                        unloadCapacityTotal = unloadCapacityTotal.add(tmpGoodsModel.getUnloadAmount().multiply(capacity).divide(new BigDecimal(1000).pow(3), 3, BigDecimal.ROUND_HALF_UP));
                    }
                }
                unloadCapacity = unloadCapacity.add(unloadCapacityTotal);
                destination.setGoodsName(goodsName.toString());
                destination.setGoodsSize(goodsSize.toString());
            }

            //结算数量
            destination.setSettlementAmount(source.getSettlementAmount().stripTrailingZeros().toPlainString() + GoodsUnitEnum.getEnum(source.getSettlementUnit()).getUnit());
            if (GoodsUnitEnum.BY_VOLUME.getKey().equals(source.getSettlementUnit())) {
                destination.setSettlementAmount(destination.getSettlementAmount() + "(" + unloadCapacity.stripTrailingZeros().toPlainString() + CommonConstant.SQUARE + ")");
            }

            //付款信息处理
            if (VehicleSettleStatusEnum.PAYMENT.getKey().equals(source.getPayStatus()) && source.getTotalFee() != null) {
                BigDecimal totalFee = source.getTotalFee();
                if (source.getDriverOtherFee() != null){
                    totalFee = totalFee.add(source.getDriverOtherFee());
                }
                destination.setTotalFee("¥" + totalFee.setScale(2, RoundingMode.HALF_UP));
            }

            //个人车主展示 姓名 手机号
            //个人车主展示公司名为 姓名 手机号
            if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())) {
                destination.setCompanyCarrierName(source.getCarrierContactName() + " " + source.getCarrierContactPhone());
            }
        }
    }
}
