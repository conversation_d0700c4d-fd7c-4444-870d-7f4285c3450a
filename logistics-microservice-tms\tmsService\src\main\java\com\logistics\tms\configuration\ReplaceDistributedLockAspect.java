package com.logistics.tms.configuration;

import com.yelo.tools.redis.redisdistributedlock.DistributedLock;
import com.yelo.tools.redis.redisdistributedlock.SpelUtil;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;

/**
 *
 * 替换 {@link com.yelo.tools.redis.redisdistributedlock.DistributedLockAspect DistributedLockAspect} 原有实现
 */
@Aspect
@Component
@Slf4j
public class ReplaceDistributedLockAspect {

    @Resource
    private RedissonClient redissonClient;
    private static final long LONG_ZERO = 0L;

    @Pointcut("@annotation(com.yelo.tools.redis.redisdistributedlock.DistributedLock)")
    public void pointCut() {
    }


    /**
     * 环绕增强，尝试获取锁/释放锁
     *
     * @param joinPoint 切面
     * @return Object
     */
    @Around("pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method targetMethod = methodSignature.getMethod();
        DistributedLock annotation = AnnotationUtils.findAnnotation(targetMethod, DistributedLock.class);
        assert annotation != null;
        String key = getLockKey(targetMethod, joinPoint, annotation);
        Object proceed = null;
        RLock lock = null;
        boolean hasLock = false;
        try {
            lock = redissonClient.getLock(key);
            // 尝试获取锁
            hasLock = tryLock(lock, annotation);
            if (hasLock) {
                log.info("success to get distributed lock with key {}", key);
                // 加锁成功执行方法
                proceed = joinPoint.proceed();
            }
        } finally {
            if (hasLock) {
                // 释放锁
                lock.unlock();
                log.info("lock {} has been released", key);
            }
        }
        // 等待过后仍未 获取到锁 抛出异常
        if(!hasLock){
            throw new BizException(-1, "操作执行中,请稍后再试");
        }
        return proceed;
    }

    private boolean tryLock(RLock lock, DistributedLock annotation) throws InterruptedException {
        if (LONG_ZERO != annotation.leaseTime()) {
            return lock.tryLock(annotation.waitTime(), annotation.leaseTime(), annotation.timeUnit());
        } else {
            return lock.tryLock(annotation.waitTime(), annotation.timeUnit());
        }
    }


    /**
     * 获取拦截到的方法,解析分布式锁key值（如果包含el表达式，则从中解析出内容）
     *
     * @param joinPoint 切点
     * @return redisKey
     */
    private String getLockKey(Method targetMethod,
                              ProceedingJoinPoint joinPoint, DistributedLock targetAnnotation) {
        Object target = joinPoint.getTarget();
        Object[] arguments = joinPoint.getArgs();
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < targetAnnotation.keys().length; i++) {
            String subKey = targetAnnotation.keys()[i];
            if (StringUtils.isNotBlank(subKey) && StringUtils.contains(subKey, "#")) {
                stringBuilder.append(SpelUtil.parse(target, subKey, targetMethod, arguments));
            } else {
                stringBuilder.append(subKey);
            }
        }
        if (StringUtils.isNotBlank(targetAnnotation.prefix())) {
            return concat(targetAnnotation.prefix(), targetAnnotation.separator(), stringBuilder);
        } else {
            return concat(target.getClass().getName(), targetAnnotation.separator(), targetMethod.getName(), targetAnnotation.separator(), stringBuilder);
        }
    }

    public String concat(CharSequence... strings) {
        StringBuilder stringBuilder = new StringBuilder();
        for (CharSequence string : strings) {
            stringBuilder.append(string);
        }
        return stringBuilder.toString();
    }
}
