package com.logistics.tms.controller.routeenquiry.request;

import com.yelo.tray.core.page.AbstractPageForm;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/7/9 13:46
 */
@Data
public class SearchRouteEnquiryListForWebRequestModel extends AbstractPageForm<SearchRouteEnquiryListForWebRequestModel> {

    /**
     * 竞价状态：1 待业务审核，2 待车主确认，3 待结算审核，4 完成竞价，5 竞价取消
     */
    private Integer status;

    /**
     * 报价人
     */
    private String quoteOperator;

    /**
     * 报价时间-开始
     */
    private String quoteTimeStart;
    /**
     * 报价时间-结束
     */
    private String quoteTimeEnd;

}
