package com.logistics.tms.biz.demandorder;

import cn.hutool.core.collection.ListUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.base.utils.LocalStringUtil;
import com.logistics.tms.biz.carrierorder.CarrierOrderForLeYiBiz;
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.demandorder.model.*;
import com.logistics.tms.biz.email.EmailConstant;
import com.logistics.tms.biz.messagenotice.MessageNoticeCommonBiz;
import com.logistics.tms.biz.messagenotice.model.AddMessageNoticeModel;
import com.logistics.tms.biz.region.RegionBiz;
import com.logistics.tms.biz.sysconfig.SysConfigBiz;
import com.logistics.tms.biz.workgroup.sendmsg.model.WorkGroupEventModel;
import com.logistics.tms.biz.workordercenter.WorkOrderBiz;
import com.logistics.tms.client.BasicDataClient;
import com.logistics.tms.client.BigDataClient;
import com.logistics.tms.client.WarehouseStockClient;
import com.logistics.tms.client.feign.basicdata.map.MapServiceApi;
import com.logistics.tms.client.feign.basicdata.map.request.GetMapByLonLatReqFeignModel;
import com.logistics.tms.client.feign.basicdata.map.response.GetMapByLonLatRespFeignModel;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.LeyiCommonAddressServiceApi;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.request.BatchQueryCustomerInfoRequest;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.response.AddressInfoResponseModel;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.response.BatchQueryCustomerInfoResponse;
import com.logistics.tms.client.feign.tray.order.customerinorder.CustomerInOrderServiceApi;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.RollbackDemandRequestModel;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.request.LogisticsAddOrUpdateActualAddressRequest;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.request.SyncSupplementDemandRequest;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.response.GetCheckAddressCodeResponse;
import com.logistics.tms.client.feign.warehouse.stock.reponse.ListWarehouseByNameResponseModel;
import com.logistics.tms.client.model.OrgForHierarchyModel;
import com.logistics.tms.client.model.RecyclePublishUpdateDemandRequestModel;
import com.logistics.tms.controller.carrierorder.request.GetCarrierOrdersByDemandIdRequestModel;
import com.logistics.tms.controller.carrierorder.response.DemandCarrierOrderModel;
import com.logistics.tms.controller.carrierorder.response.DemandCarrierOrderRecursiveModel;
import com.logistics.tms.controller.carrierorder.response.DemandOrderCarrierDetailResponseModel;
import com.logistics.tms.controller.carrierorderapplet.request.VerifyEnablePickUpMoreReqModel;
import com.logistics.tms.controller.demandorder.request.*;
import com.logistics.tms.controller.demandorder.response.*;
import com.logistics.tms.controller.homepage.response.DemandOrderStatisticsResponseModel;
import com.logistics.tms.controller.region.request.GetCompanyCarrierByRegionRequestModel;
import com.logistics.tms.controller.region.response.GetCompanyCarrierByRegionResponseModel;
import com.logistics.tms.delayqueue.DelayQueueBizTypeEnum;
import com.logistics.tms.delayqueue.biddingorder.msg.BiddingOrderDelayMsg;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.logistics.tms.mapper.message.DealReplenishOrderMessage;
import com.logistics.tms.rabbitmq.consumer.model.*;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.logistics.tms.rabbitmq.publisher.model.DelayMsg;
import com.logistics.tms.rabbitmq.publisher.model.SynDemandOrderAddressSourceToLeYiModel;
import com.logistics.tms.rabbitmq.publisher.model.SynDemandOrderAddressToLeYiModel;
import com.logistics.tms.rabbitmq.publisher.model.SyncSignDemandOrderListToGroundPushModel;
import com.yelo.basicdata.api.feign.datamap.model.GetLonLatByMapIdResponseModel;
import com.yelo.basicdata.api.feign.datamap.model.GetProvinceCityAreaByKeywordsNewResponseModel;
import com.yelo.life.basicdata.api.base.enums.ValidTypeEnum;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2021/9/27 15:30
 */
@Slf4j
@Service
public class DemandOrderForLeYiBiz {

    @Autowired
    private TDemandOrderMapper tDemandOrderMapper;
    @Autowired
    private TDemandOrderAddressMapper tDemandOrderAddressMapper;
    @Autowired
    private TDemandOrderOperateLogsMapper tDemandOrderOperateLogsMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private TCompanyEntrustMapper tCompanyEntrustMapper;
    @Autowired
    private DemandOrderCommonBiz demandOrderCommonBiz;
    @Autowired
    private TDemandOrderGoodsMapper tDemandOrderGoodsMapper;
    @Autowired
    private TDemandOrderGoodsRelMapper  tDemandOrderGoodsRelMapper;
    @Autowired
    private TDemandOrderOrderRelMapper tDemandOrderOrderRelMapper;
    @Autowired
    private TDemandOrderCustomerRelMapper tDemandOrderCustomerRelMapper;
    @Autowired
    private CarrierOrderCommonBiz carrierOrderCommonBiz;
    @Autowired
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Autowired
    private DemandOrderBiz demandOrderBiz;
    @Autowired
    private RabbitMqPublishBiz rabbitMqPublishBiz;
    @Autowired
    private WarehouseStockClient warehouseStockClient;
    @Autowired
    private TDemandOrderObjectionMapper tDemandOrderObjectionMapper;
    @Autowired
    private TRegionMapper tRegionMapper;
    @Autowired
    private BasicDataClient basicDataClient;
    @Autowired
    private TDemandOrderEventsMapper tDemandOrderEventsMapper;
    @Autowired
    private TDemandOrderCarrierMapper tDemandOrderCarrierMapper;
    @Autowired
    private TDemandReceivementMapper tDemandReceivementMapper;
    @Autowired
    private TDemandPaymentMapper tDemandPaymentMapper;
    @Autowired
    private TCarrierOrderOtherFeeMapper tCarrierOrderOtherFeeMapper;
    @Autowired
    private BigDataClient bigDataClient;
    @Autowired
    private WorkOrderBiz workOrderBiz;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private TBiddingOrderMapper tBiddingOrderMapper;
    @Resource
    private TBiddingOrderDemandMapper tBiddingOrderDemandMapper;
    @Resource
    private TVehicleLengthMapper tVehicleLengthMapper;
    @Resource
    private TOperateLogsMapper tOperateLogsMapper;
    @Resource
    private RegionBiz regionBiz;
    @Resource
    private TBiddingOrderCompanyMapper tBiddingOrderCompanyMapper;
    @Resource
    private MessageNoticeCommonBiz messageNoticeCommonBiz;
    @Resource
    private SysConfigBiz sysConfigBiz;
    @Resource
    private TShippingOrderMapper tShippingOrderMapper;
    @Resource
    private CustomerInOrderServiceApi customerInOrderServiceApi;
    @Resource
    private LeyiCommonAddressServiceApi leyiCommonAddressServiceApi;
    @Resource
    private TExtDemandOrderRelationMapper tExtDemandOrderRelationMapper;
    @Resource
    private CarrierOrderForLeYiBiz carrierOrderForLeYiBiz;
    @Resource
    private MapServiceApi mapServiceApi;

    /**
     * 需求单批量发布详情
     *
     * @param requestModel
     * @return
     */
    public List<BatchPublishDetailResponseModel> publishDetail(BatchPublishDetailRequestModel requestModel) {
        List<BatchPublishDetailResponseModel> detailList = tDemandOrderMapper.publishDetail(requestModel.getDemandIds());
        if (ListUtils.isEmpty(detailList)) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }
        if (detailList.size() > CommonConstant.INTEGER_TEN) {
            throw new BizException(EntrustDataExceptionEnum.PUBLISH_DEMAND_ORDER_MAX);
        }
        List<Integer> goodsUnitList = new ArrayList<>();
        //判断需求单状态
        for (BatchPublishDetailResponseModel demandOrder : detailList) {
            //非云盘单子不能操作
            if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(demandOrder.getDemandOrderSource())){
                throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY.getMsg());
            }
            //需求单已取消不能操作
            if (CommonConstant.INTEGER_ONE.equals(demandOrder.getIfCancel())) {
                throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_CANCEL.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMANDORDER_CANCEL.getMsg());
            }
            //需求单已放空不能操作
            if (CommonConstant.INTEGER_ONE.equals(demandOrder.getIfEmpty())) {
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getMsg());
            }
            //需求单已回退不能操作
            if(CommonConstant.INTEGER_ONE.equals(demandOrder.getIfRollback())){
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_ROLLBACK.getCode(),demandOrder.getDemandOrderCode()+EntrustDataExceptionEnum.DEMAND_ORDER_IS_ROLLBACK.getMsg());
            }
            //需求单不是待发布状态不能操作
            if (!DemandOrderStatusEnum.WAIT_PUBLISH.getKey().equals(demandOrder.getEntrustStatus())) {
                throw new BizException(EntrustDataExceptionEnum.COMPANY_CARRIER_PUBLISH_STATUS_ERROR.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.COMPANY_CARRIER_PUBLISH_STATUS_ERROR.getMsg());
            }
            if (!goodsUnitList.contains(demandOrder.getGoodsUnit())) {
                goodsUnitList.add(demandOrder.getGoodsUnit());
            }
        }
        //不同单位的不能一起操作
        if (goodsUnitList.size() > CommonConstant.INTEGER_ONE) {
            throw new BizException(EntrustDataExceptionEnum.PUBLISH_DEMAND_ORDER_GOODS_UNIT_ERROR);
        }
        //需求单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkDemandExceptionExist(requestModel.getDemandIds());
        if (!workOrderMap.isEmpty()){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EXCEPTION);
        }
        return detailList;
    }

    /**
     * 需求单批量发布
     *
     * @param requestModel
     */
    @Transactional
    public void confirmPublish(BatchPublishRequestModel requestModel) {
        //解析入参需求单信息
        List<Long> demandOrderIdList = new ArrayList<>();
        Map<Long, BatchPublishDemandModel> publishDemandMap = new HashMap<>();
        for (BatchPublishDemandModel model : requestModel.getDemandDtoList()) {
            demandOrderIdList.add(model.getDemandOrderId());
            publishDemandMap.put(model.getDemandOrderId(), model);
        }
        String demandOrderIds = StringUtils.listToString(demandOrderIdList, ',');

        //查询需求单信息
        List<BatchPublishDetailResponseModel> detailList = tDemandOrderMapper.publishDetail(demandOrderIds);
        if (ListUtils.isEmpty(detailList)){
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }

        BatchPublishDemandModel publishDemandModel;
        List<Integer> goodsUnitList = new ArrayList<>();
        //校验需求单信息
        for (BatchPublishDetailResponseModel demandOrder : detailList) {
            //非云盘单子不能操作
            if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(demandOrder.getDemandOrderSource())){
                throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY.getMsg());
            }
            if (CommonConstant.INTEGER_ONE.equals(demandOrder.getIfeExtDemandOrder())){
                throw new BizException(EntrustDataExceptionEnum.SYSTEM_NOT_SUPPORT.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.SYSTEM_NOT_SUPPORT.getMsg());
            }
            //需求单取消
            if (CommonConstant.INTEGER_ONE.equals(demandOrder.getIfCancel())) {
                throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_CANCEL.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMANDORDER_CANCEL.getMsg());
            }
            //需求单放空
            if (CommonConstant.INTEGER_ONE.equals(demandOrder.getIfEmpty())) {
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getMsg());
            }
            //需求单已回退不能操作
            if (CommonConstant.INTEGER_ONE.equals(demandOrder.getIfRollback())) {
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_ROLLBACK.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_ROLLBACK.getMsg());
            }
            //需求单不是待发布状态
            if (!DemandOrderStatusEnum.WAIT_PUBLISH.getKey().equals(demandOrder.getEntrustStatus())) {
                throw new BizException(EntrustDataExceptionEnum.COMPANY_CARRIER_PUBLISH_STATUS_ERROR.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.COMPANY_CARRIER_PUBLISH_STATUS_ERROR.getMsg());
            }
            //竞价抢单发布
            if (OrderModeEnum.BIDDING_PRICE.getKey().equals(requestModel.getOrderMode())){
                //回收类型不能发布
                if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(demandOrder.getEntrustType())
                        || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(demandOrder.getEntrustType())){
                    throw new BizException(EntrustDataExceptionEnum.RECYCLE_DEMAND_ORDER_NOT_OPERATION.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.RECYCLE_DEMAND_ORDER_NOT_OPERATION.getMsg());
                }
            }
            //校验卸货地址
            publishDemandModel = publishDemandMap.get(demandOrder.getDemandId());
            if (StringUtils.isBlank(demandOrder.getUnloadProvinceName())) {
                //需求单不存在卸货地址但是又没传递卸货地址时
                if (publishDemandModel == null || StringUtils.isBlank(publishDemandModel.getUnloadProvinceName())) {
                    throw new BizException(EntrustDataExceptionEnum.ENTRUST_UNLOAD_ADDRESS_BLANK.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.ENTRUST_UNLOAD_ADDRESS_BLANK.getMsg());
                }
                demandOrder.setChangeUnloadWarehouse(true);
            } else {
                //卸货地址不为空时,如果是回收入库类型自动发布也修改地址
                if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(demandOrder.getEntrustType())
                        && CommonConstant.INTEGER_ONE.equals(publishDemandModel.getAutoPublish())) {
                    demandOrder.setChangeUnloadWarehouse(true);
                }
            }
            //获取需求单货物单位
            if (!goodsUnitList.contains(demandOrder.getGoodsUnit())) {
                goodsUnitList.add(demandOrder.getGoodsUnit());
            }
        }
        //非同一单位不能发布
        if (goodsUnitList.size() > CommonConstant.INTEGER_ONE) {
            throw new BizException(EntrustDataExceptionEnum.PUBLISH_DEMAND_ORDER_GOODS_UNIT_ERROR);
        }

        //需求单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkDemandExceptionExist(demandOrderIds);
        if (!workOrderMap.isEmpty()){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EXCEPTION);
        }

        //指定车主发布
        if (OrderModeEnum.ASSIGN_CARRIER.getKey().equals(requestModel.getOrderMode())){
            confirmPublishForAssignCarrier(detailList, publishDemandMap, demandOrderIds, requestModel.getIsOurCompany(), requestModel.getCompanyCarrierId());
        }
        //竞价抢单发布
        else if (OrderModeEnum.BIDDING_PRICE.getKey().equals(requestModel.getOrderMode())){
            confirmPublishForBidding(requestModel, detailList);
        }
    }
    //指定车主发布
    private void confirmPublishForAssignCarrier(List<BatchPublishDetailResponseModel> detailList,
                                                Map<Long, BatchPublishDemandModel> publishDemandMap,
                                                String demandOrderIds,
                                                Integer isOurCompany,
                                                Long companyCarrierId){
        //查询车主信息
        CompanyCarrierByIdModel companyCarrierByIdModel = demandOrderCommonBiz.companyCarrierInfoByIsOurCompany(isOurCompany, companyCarrierId);
        if (CommonConstant.INTEGER_ONE.equals(companyCarrierByIdModel.getIfAddBlacklist())){
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }

        //处理需求单大区信息
        List<GetRegionInfoByCityIdModel> regionInfoList = tRegionMapper.getEnableRegionInfoByDemandIds(demandOrderIds);
        Map<Long, GetRegionInfoByCityIdModel> regionInfoMap = regionInfoList.stream().collect(Collectors.toMap(GetRegionInfoByCityIdModel::getDemandId, Function.identity(), (key1, key2) -> key2));

        String userName = BaseContextHandler.getUserName();
        Date now = new Date();

        SynDemandOrderAddressToLeYiModel synDemandOrderAddressToLeYiModel;
        List<SynDemandOrderAddressToLeYiModel> synDemandOrderAddressList = new ArrayList<>();

        BatchPublishDemandModel publishDemandModel;
        List<TDemandOrder> upDemandOrderList = new ArrayList<>();
        TDemandOrder upTDemandOrder;
        TDemandOrderOperateLogs demandOrderOperateLogs;
        List<TDemandOrderOperateLogs> demandOrderLogsList = new ArrayList<>();
        TDemandOrderAddress upDemandOrderAddress;
        List<TDemandOrderAddress> upDemandOrderAddressList = new ArrayList<>();
        TDemandOrderCarrier tDemandOrderCarrier;
        List<TDemandOrderCarrier> tDemandOrderCarrierAddList = new ArrayList<>();
        GetRegionInfoByCityIdModel regionInfo;
        Long warehouseId;

        //遍历
        for (BatchPublishDetailResponseModel demandOrder : detailList) {
            publishDemandModel = publishDemandMap.get(demandOrder.getDemandId());

            //更新需求单信息
            upTDemandOrder = new TDemandOrder();
            upTDemandOrder.setId(demandOrder.getDemandId());
            upTDemandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
            upTDemandOrder.setStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
            upTDemandOrder.setStatusUpdateTime(now);
            upTDemandOrder.setOrderMode(OrderModeEnum.ASSIGN_CARRIER.getKey());
            //车主信息
            upTDemandOrder.setCompanyCarrierType(companyCarrierByIdModel.getCompanyCarrierType());
            upTDemandOrder.setCompanyCarrierId(companyCarrierByIdModel.getCompanyCarrierId());
            upTDemandOrder.setCompanyCarrierName(companyCarrierByIdModel.getCompanyCarrierName());
            upTDemandOrder.setCarrierContactId(companyCarrierByIdModel.getCarrierContactId());
            upTDemandOrder.setCarrierContactName(companyCarrierByIdModel.getCarrierContactName());
            upTDemandOrder.setCarrierContactPhone(companyCarrierByIdModel.getCarrierContactPhone());
            upTDemandOrder.setCompanyCarrierLevel(companyCarrierByIdModel.getCompanyCarrierLevel());
            upTDemandOrder.setAutoPublish(publishDemandModel.getAutoPublish());
            commonBiz.setBaseEntityModify(upTDemandOrder, userName);
            upDemandOrderList.add(upTDemandOrder);

            //需求单操作日志
            demandOrderOperateLogs = new TDemandOrderOperateLogs();
            demandOrderOperateLogs.setDemandOrderId(demandOrder.getDemandId());
            demandOrderOperateLogs.setOperationType(DemandOrderOperateLogsEnum.PUBLISH_DEMAND_ORDER.getKey());
            demandOrderOperateLogs.setOperationContent(DemandOrderOperateLogsEnum.PUBLISH_DEMAND_ORDER.getValue());
            demandOrderOperateLogs.setOperatorName(userName);
            demandOrderOperateLogs.setOperateTime(now);
            commonBiz.setBaseEntityAdd(demandOrderOperateLogs, userName);
            demandOrderLogsList.add(demandOrderOperateLogs);

            //需求单车主信息
            tDemandOrderCarrier = new TDemandOrderCarrier();
            tDemandOrderCarrier.setDemandOrderId(demandOrder.getDemandId());
            tDemandOrderCarrier.setCompanyCarrierId(upTDemandOrder.getCompanyCarrierId());
            tDemandOrderCarrier.setCarrierContactId(upTDemandOrder.getCarrierContactId());
            tDemandOrderCarrier.setCarrierPrice(upTDemandOrder.getCarrierPrice());
            tDemandOrderCarrier.setCarrierPriceType(upTDemandOrder.getCarrierPriceType());
            commonBiz.setBaseEntityAdd(tDemandOrderCarrier, BaseContextHandler.getUserName());
            tDemandOrderCarrierAddList.add(tDemandOrderCarrier);

            //更新地址信息
            regionInfo = regionInfoMap.get(demandOrder.getDemandId());
            if (regionInfo != null || demandOrder.isChangeUnloadWarehouse()) {
                upDemandOrderAddress = new TDemandOrderAddress();
                upDemandOrderAddress.setId(demandOrder.getDemandOrderAddressId());
                commonBiz.setBaseEntityModify(upDemandOrderAddress, userName);
                upDemandOrderAddressList.add(upDemandOrderAddress);
                if (regionInfo != null) {//更新提货大区
                    upDemandOrderAddress.setLoadRegionId(regionInfo.getRegionId());
                    upDemandOrderAddress.setLoadRegionName(regionInfo.getRegionName());
                    upDemandOrderAddress.setLoadRegionContactName(regionInfo.getRegionContactName());
                    upDemandOrderAddress.setLoadRegionContactPhone(regionInfo.getRegionContactPhone());
                }
                if (demandOrder.isChangeUnloadWarehouse()) {//回填卸货地址
                    upDemandOrderAddress.setUnloadAreaId(publishDemandModel.getUnloadAreaId());
                    upDemandOrderAddress.setUnloadAreaName(publishDemandModel.getUnloadAreaName());
                    upDemandOrderAddress.setUnloadCityId(publishDemandModel.getUnloadCityId());
                    upDemandOrderAddress.setUnloadCityName(publishDemandModel.getUnloadCityName());
                    upDemandOrderAddress.setUnloadProvinceId(publishDemandModel.getUnloadProvinceId());
                    upDemandOrderAddress.setUnloadProvinceName(publishDemandModel.getUnloadProvinceName());
                    upDemandOrderAddress.setUnloadDetailAddress(publishDemandModel.getUnloadDetailAddress());
                    upDemandOrderAddress.setReceiverMobile(publishDemandModel.getReceiverMobile());
                    upDemandOrderAddress.setUnloadWarehouse(publishDemandModel.getUnloadWarehouse());
                    upDemandOrderAddress.setReceiverName(publishDemandModel.getReceiverName());

                    warehouseId = publishDemandModel.getWarehouseId();
                } else {
                    warehouseId = null;
                }
            } else {
                warehouseId = null;
            }

            //自动发布修改卸货地址替换掉旧的地址
            if (demandOrder.isChangeUnloadWarehouse() && CommonConstant.INTEGER_ONE.equals(publishDemandModel.getAutoPublish())) {
                //需求单操作日志
                demandOrderOperateLogs = new TDemandOrderOperateLogs();
                demandOrderOperateLogs.setDemandOrderId(demandOrder.getDemandId());
                demandOrderOperateLogs.setOperationType(DemandOrderOperateLogsEnum.CHANGE_UNLOAD_WAREHOUSE.getKey());
                demandOrderOperateLogs.setOperationContent(DemandOrderOperateLogsEnum.CHANGE_UNLOAD_WAREHOUSE.getValue());
                String beforeChangeWarehouse = Optional.ofNullable(demandOrder.getUnloadWarehouse()).orElse(CommonConstant.EMPTY_STRING);
                demandOrderOperateLogs.setRemark(String.format(DemandOrderOperateLogsEnum.CHANGE_UNLOAD_WAREHOUSE.getFormat(), beforeChangeWarehouse, publishDemandModel.getUnloadWarehouse()));
                demandOrderOperateLogs.setOperatorName(userName);
                demandOrderOperateLogs.setOperateTime(now);
                commonBiz.setBaseEntityAdd(demandOrderOperateLogs, userName);
                demandOrderLogsList.add(demandOrderOperateLogs);
            }

            //同步云盘需求单来源和仓库id（没有卸货地址的同步仓库id）
            synDemandOrderAddressToLeYiModel = new SynDemandOrderAddressToLeYiModel();
            synDemandOrderAddressToLeYiModel.setDemandOrderCode(demandOrder.getDemandOrderCode());
            synDemandOrderAddressToLeYiModel.setWarehouseId(warehouseId);
            synDemandOrderAddressList.add(synDemandOrderAddressToLeYiModel);

        }

        //更新需求单
        tDemandOrderMapper.batchUpdateByPrimaryKeySelective(upDemandOrderList);
        //新增操作日志
        tDemandOrderOperateLogsMapper.batchInsertSelective(demandOrderLogsList);
        //更新地址信息
        if (ListUtils.isNotEmpty(upDemandOrderAddressList)) {
            tDemandOrderAddressMapper.batchUpdateSelective(upDemandOrderAddressList);
            //异步查询地址经纬度
            AsyncProcessQueue.execute(() -> demandOrderCommonBiz.updateAddressLonAndLat(upDemandOrderAddressList));
        }
        //需求单车主表信息插入
        if (ListUtils.isNotEmpty(tDemandOrderCarrierAddList)){
            tDemandOrderCarrierMapper.batchInsertSelective(tDemandOrderCarrierAddList);
        }

        //需求单来源于TMS、卸货地址同步云仓和云盘
        if (ListUtils.isNotEmpty(synDemandOrderAddressList)) {
            SynDemandOrderAddressSourceToLeYiModel synDemandOrderAddressSourceToLeYiModel = new SynDemandOrderAddressSourceToLeYiModel();
            synDemandOrderAddressSourceToLeYiModel.setDemandOrderAddressList(synDemandOrderAddressList);
            synDemandOrderAddressSourceToLeYiModel.setOperateUserName(userName);
            rabbitMqPublishBiz.synDemandOrderAddressSourceToWarehouse(synDemandOrderAddressSourceToLeYiModel);

            rabbitMqPublishBiz.synDemandOrderAddressSourceToLeYi(synDemandOrderAddressSourceToLeYiModel);
        }
    }
    //竞价抢单发布
    private void confirmPublishForBidding(BatchPublishRequestModel requestModel,
                                          List<BatchPublishDetailResponseModel> detailList){

        //查询车主
        List<Long> companyCarrierIdList;
        if (CompanyCarrierRangeEnum.ALL.getKey().equals(requestModel.getCompanyCarrierRange())){
            List<TCompanyCarrier> tCompanyCarrierList = tCompanyCarrierMapper.getByIds(null);
            //获取未加入黑名单的车主id
            companyCarrierIdList = tCompanyCarrierList.stream().filter(item -> CommonConstant.INTEGER_ZERO.equals(item.getIfAddBlacklist())).map(TCompanyCarrier::getId).collect(Collectors.toList());
            if (ListUtils.isEmpty(companyCarrierIdList)){
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }
        }else if (CompanyCarrierRangeEnum.DIRECTIONAL.getKey().equals(requestModel.getCompanyCarrierRange())){
            //校验入参车主id
            companyCarrierIdList = requestModel.getCompanyCarrierIdList();
            if (ListUtils.isEmpty(companyCarrierIdList)){
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }
            //校验入参车主id是否存在
            BatchPublishDetailResponseModel batchPublishDetailResponseModel = detailList.get(CommonConstant.INTEGER_ZERO);
            GetCompanyCarrierByRegionRequestModel getCompanyCarrierByRegionRequestModel = new GetCompanyCarrierByRegionRequestModel();
            getCompanyCarrierByRegionRequestModel.setProvinceId(batchPublishDetailResponseModel.getLoadProvinceId());
            getCompanyCarrierByRegionRequestModel.setCityId(batchPublishDetailResponseModel.getLoadCityId());
            List<GetCompanyCarrierByRegionResponseModel> getCompanyCarrierByRegionList = regionBiz.getCompanyByRegion(getCompanyCarrierByRegionRequestModel);
            if (ListUtils.isEmpty(getCompanyCarrierByRegionList)){
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }
            List<Long> dbCompanyCarrierIdList = getCompanyCarrierByRegionList.stream().map(GetCompanyCarrierByRegionResponseModel::getCompanyId).collect(Collectors.toList());
            if (companyCarrierIdList.stream().anyMatch(id -> !dbCompanyCarrierIdList.contains(id))){
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }
        }else{
            return;
        }

        //校验车长
        TVehicleLength dbVehicleLength = null;
        if (requestModel.getVehicleLengthId() != null) {
            dbVehicleLength = tVehicleLengthMapper.selectByPrimaryKey(requestModel.getVehicleLengthId());
            if (dbVehicleLength == null || IfValidEnum.INVALID.getKey().equals(dbVehicleLength.getValid())) {
                throw new BizException(EntrustDataExceptionEnum.VEHICLE_LENGTH_NOT_EXIST);
            }
        }

        String userName = BaseContextHandler.getUserName();
        Date now = new Date();

        List<TDemandOrder> upDemandOrderList = new ArrayList<>();
        TDemandOrder upTDemandOrder;
        TDemandOrderOperateLogs demandOrderOperateLogs;
        List<TDemandOrderOperateLogs> demandOrderLogsList = new ArrayList<>();
        TBiddingOrderDemand addBiddingOrderDemand;
        List<TBiddingOrderDemand> addBiddingOrderDemandList = new ArrayList<>();

        //生成竞价单
        TBiddingOrder addBiddingOrder = new TBiddingOrder();
        addBiddingOrder.setBiddingOrderCode(commonBiz.getBusinessTypeCode(BusinessCodeTypeEnum.BIDDING_ORDER_CODE, "", userName));
        addBiddingOrder.setBiddingStatus(BiddingOrderStatusEnum.IN_QUOTATION.getKey());
        addBiddingOrder.setCompanyCarrierRange(requestModel.getCompanyCarrierRange());
        addBiddingOrder.setQuoteStartTime(now);
        addBiddingOrder.setQuoteDuration(requestModel.getQuoteDuration());
        addBiddingOrder.setExpectedLoadTime(requestModel.getExpectedLoadTime());
        addBiddingOrder.setExpectedUnloadTime(requestModel.getExpectedUnloadTime());
        if (dbVehicleLength != null) {
            addBiddingOrder.setVehicleLengthId(dbVehicleLength.getId());
            addBiddingOrder.setVehicleLength(dbVehicleLength.getVehicleLength());
        }
        addBiddingOrder.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityAdd(addBiddingOrder, userName);
        tBiddingOrderMapper.insertSelectiveBackKey(addBiddingOrder);

        //定向选择
        List<TBiddingOrderCompany> addBiddingOrderCompanyList = new ArrayList<>();
        if (CompanyCarrierRangeEnum.DIRECTIONAL.getKey().equals(requestModel.getCompanyCarrierRange())){
            TBiddingOrderCompany addBiddingOrderCompany;
            for (Long companyCarrierId : companyCarrierIdList) {
                addBiddingOrderCompany = new TBiddingOrderCompany();
                addBiddingOrderCompany.setBiddingOrderId(addBiddingOrder.getId());
                addBiddingOrderCompany.setCompanyCarrierId(companyCarrierId);
                commonBiz.setBaseEntityAdd(addBiddingOrderCompany, userName);
                addBiddingOrderCompanyList.add(addBiddingOrderCompany);
            }
        }

        //遍历
        for (BatchPublishDetailResponseModel demandOrder : detailList) {
            //更新需求单信息
            upTDemandOrder = new TDemandOrder();
            upTDemandOrder.setId(demandOrder.getDemandId());
            upTDemandOrder.setEntrustStatus(DemandOrderStatusEnum.BIDDING.getKey());
            upTDemandOrder.setStatus(DemandOrderStatusEnum.BIDDING.getKey());
            upTDemandOrder.setStatusUpdateTime(now);
            upTDemandOrder.setOrderMode(OrderModeEnum.BIDDING_PRICE.getKey());
            commonBiz.setBaseEntityModify(upTDemandOrder, userName);
            upDemandOrderList.add(upTDemandOrder);

            //需求单操作日志
            demandOrderOperateLogs = new TDemandOrderOperateLogs();
            demandOrderOperateLogs.setDemandOrderId(demandOrder.getDemandId());
            demandOrderOperateLogs.setOperationType(DemandOrderOperateLogsEnum.PUBLISH_DEMAND_ORDER.getKey());
            demandOrderOperateLogs.setOperationContent(DemandOrderOperateLogsEnum.PUBLISH_DEMAND_ORDER.getValue());
            demandOrderOperateLogs.setOperatorName(userName);
            demandOrderOperateLogs.setOperateTime(now);
            commonBiz.setBaseEntityAdd(demandOrderOperateLogs, userName);
            demandOrderLogsList.add(demandOrderOperateLogs);

            //生成竞价单-需求单关系
            addBiddingOrderDemand = new TBiddingOrderDemand();
            addBiddingOrderDemand.setBiddingOrderId(addBiddingOrder.getId());
            addBiddingOrderDemand.setDemandOrderId(demandOrder.getDemandId());
            commonBiz.setBaseEntityAdd(addBiddingOrderDemand, userName);
            addBiddingOrderDemandList.add(addBiddingOrderDemand);
        }

        //记录竞价单日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(addBiddingOrder.getId(), OperateLogsOperateTypeEnum.BIDDING_ORDER_ADD, null, userName);

        //更新需求单
        tDemandOrderMapper.batchUpdateByPrimaryKeySelective(upDemandOrderList);

        //新增操作日志
        tDemandOrderOperateLogsMapper.batchInsertSelective(demandOrderLogsList);

        //新增竞价单-需求单关系
        tBiddingOrderDemandMapper.batchInsert(addBiddingOrderDemandList);

        //新增竞价单日志
        tOperateLogsMapper.insertSelective(tOperateLogs);

        if (ListUtils.isNotEmpty(addBiddingOrderCompanyList)){
            tBiddingOrderCompanyMapper.batchInsert(addBiddingOrderCompanyList);
        }

        //将竞价单添加到延迟队列
        BiddingOrderDelayMsg biddingOrderDelayMsg = new BiddingOrderDelayMsg();
        biddingOrderDelayMsg.setBiddingOrderId(addBiddingOrder.getId());
        commonBiz.addDelayQueue(DelayQueueBizTypeEnum.BIDDING_ORDER.name(), biddingOrderDelayMsg, BiddingOrderQuoteDurationEnum.getEnum(addBiddingOrder.getQuoteDuration()).getValue(), TimeUnit.MINUTES);


        //给车主发消息弹窗
        MessageNoticeEnum messageNoticeEnum = MessageNoticeEnum.DEMAND_ORDER_BIDDING_PUBLISH;
        //获取消息体文本
        ConfigKeyEnum configKeyEnum = messageNoticeEnum.getConfigKeyEnum();
        String socketTemplateMessage = sysConfigBiz.getSysConfig(configKeyEnum.getGroupCode(), configKeyEnum.getValue()).orElse(null);
        if (StringUtils.isNotBlank(socketTemplateMessage)){
            String messageBody = MessageFormat.format(socketTemplateMessage, addBiddingOrder.getBiddingOrderCode());
            //组装消息
            AddMessageNoticeModel addMessageNoticeModel = new AddMessageNoticeModel();
            addMessageNoticeModel.setMessageNoticeEnum(messageNoticeEnum);
            addMessageNoticeModel.setObjectId(addBiddingOrder.getId());
            addMessageNoticeModel.setObjectCode(addBiddingOrder.getBiddingOrderCode());
            addMessageNoticeModel.setMessageBody(messageBody);
            addMessageNoticeModel.setMessageReceiverList(companyCarrierIdList);
            addMessageNoticeModel.setMessagePusher(userName);
            AsyncProcessQueue.execute(() -> messageNoticeCommonBiz.addMessageNotice(addMessageNoticeModel));
        }
    }

    /**
     * 竞价单选择车主方法调用
     * 1、需求单：状态-待调度，落车主信息、车主费用信息、车长信息
     * 2、生成需求单、车主关系
     * 3、需求单地址表落大区信息
     * 4、需求单来源于TMS同步云仓
     * @param requestModel
     */
    public void biddingOrderSelectCarrier(BiddingOrderSelectCarrierModel requestModel){
        //解析入参
        List<Long> demandOrderIdList = new ArrayList<>();
        Map<Long, BiddingOrderSelectCarrierDemandModel> carrierDemandMap = new HashMap<>();
        for (BiddingOrderSelectCarrierDemandModel carrierDemandModel : requestModel.getDemandOrderList()) {
            demandOrderIdList.add(carrierDemandModel.getDemandOrderId());
            carrierDemandMap.put(carrierDemandModel.getDemandOrderId(), carrierDemandModel);
        }
        String demandOrderIds = StringUtils.listToString(demandOrderIdList,',');

        //查询需求单
        List<BatchPublishDetailResponseModel> detailList = tDemandOrderMapper.publishDetail(demandOrderIds);
        if (ListUtils.isEmpty(detailList)){
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }

        //查询车主信息
        CompanyCarrierByIdModel companyCarrierByIdModel = demandOrderCommonBiz.companyCarrierInfoByIsOurCompany(requestModel.getIsOurCompany(), requestModel.getCompanyCarrierId());

        //处理需求单大区信息
        List<GetRegionInfoByCityIdModel> regionInfoList = tRegionMapper.getEnableRegionInfoByDemandIds(demandOrderIds);
        Map<Long, GetRegionInfoByCityIdModel> regionInfoMap = regionInfoList.stream().collect(Collectors.toMap(GetRegionInfoByCityIdModel::getDemandId, Function.identity(), (key1, key2) -> key2));

        String userName = BaseContextHandler.getUserName();
        Date now = new Date();

        SynDemandOrderAddressToLeYiModel synDemandOrderAddressToLeYiModel;
        List<SynDemandOrderAddressToLeYiModel> synDemandOrderAddressList = new ArrayList<>();

        BiddingOrderSelectCarrierDemandModel carrierDemandModel;
        List<TDemandOrder> upDemandOrderList = new ArrayList<>();
        TDemandOrder upTDemandOrder;
        TDemandOrderAddress upDemandOrderAddress;
        List<TDemandOrderAddress> upDemandOrderAddressList = new ArrayList<>();
        TDemandOrderCarrier tDemandOrderCarrier;
        List<TDemandOrderCarrier> tDemandOrderCarrierAddList = new ArrayList<>();
        GetRegionInfoByCityIdModel regionInfo;

        //遍历
        for (BatchPublishDetailResponseModel demandOrder : detailList) {
            carrierDemandModel = carrierDemandMap.get(demandOrder.getDemandId());
            if (carrierDemandModel == null){
                continue;
            }

            //更新需求单信息
            upTDemandOrder = new TDemandOrder();
            upTDemandOrder.setId(demandOrder.getDemandId());
            upTDemandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
            upTDemandOrder.setStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
            upTDemandOrder.setStatusUpdateTime(now);
            commonBiz.setBaseEntityModify(upTDemandOrder, userName);
            upDemandOrderList.add(upTDemandOrder);
            //车主信息
            upTDemandOrder.setCompanyCarrierType(companyCarrierByIdModel.getCompanyCarrierType());
            upTDemandOrder.setCompanyCarrierId(companyCarrierByIdModel.getCompanyCarrierId());
            upTDemandOrder.setCompanyCarrierName(companyCarrierByIdModel.getCompanyCarrierName());
            upTDemandOrder.setCarrierContactId(companyCarrierByIdModel.getCarrierContactId());
            upTDemandOrder.setCarrierContactName(companyCarrierByIdModel.getCarrierContactName());
            upTDemandOrder.setCarrierContactPhone(companyCarrierByIdModel.getCarrierContactPhone());
            upTDemandOrder.setCompanyCarrierLevel(companyCarrierByIdModel.getCompanyCarrierLevel());
            //车主费用
            upTDemandOrder.setCarrierPriceType(carrierDemandModel.getBiddingPriceType());
            upTDemandOrder.setCarrierPrice(carrierDemandModel.getBiddingPrice());
            //车长信息
            upTDemandOrder.setVehicleLengthId(requestModel.getVehicleLengthId());
            upTDemandOrder.setVehicleLength(requestModel.getVehicleLength());


            //需求单车主信息
            tDemandOrderCarrier = new TDemandOrderCarrier();
            tDemandOrderCarrier.setDemandOrderId(demandOrder.getDemandId());
            tDemandOrderCarrier.setCompanyCarrierId(upTDemandOrder.getCompanyCarrierId());
            tDemandOrderCarrier.setCarrierContactId(upTDemandOrder.getCarrierContactId());
            tDemandOrderCarrier.setCarrierPrice(upTDemandOrder.getCarrierPrice());
            tDemandOrderCarrier.setCarrierPriceType(upTDemandOrder.getCarrierPriceType());
            commonBiz.setBaseEntityAdd(tDemandOrderCarrier, BaseContextHandler.getUserName());
            tDemandOrderCarrierAddList.add(tDemandOrderCarrier);

            //更新地址信息
            regionInfo = regionInfoMap.get(demandOrder.getDemandId());
            if (regionInfo != null) {
                //更新提货大区
                upDemandOrderAddress = new TDemandOrderAddress();
                upDemandOrderAddress.setId(demandOrder.getDemandOrderAddressId());
                upDemandOrderAddress.setLoadRegionId(regionInfo.getRegionId());
                upDemandOrderAddress.setLoadRegionName(regionInfo.getRegionName());
                upDemandOrderAddress.setLoadRegionContactName(regionInfo.getRegionContactName());
                upDemandOrderAddress.setLoadRegionContactPhone(regionInfo.getRegionContactPhone());
                commonBiz.setBaseEntityModify(upDemandOrderAddress, userName);
                upDemandOrderAddressList.add(upDemandOrderAddress);
            }

            //同步云盘需求单来源和仓库id（没有卸货地址的同步仓库id）
            synDemandOrderAddressToLeYiModel = new SynDemandOrderAddressToLeYiModel();
            synDemandOrderAddressToLeYiModel.setDemandOrderCode(demandOrder.getDemandOrderCode());
            synDemandOrderAddressList.add(synDemandOrderAddressToLeYiModel);

        }

        //更新需求单
        tDemandOrderMapper.batchUpdateByPrimaryKeySelective(upDemandOrderList);
        //更新地址信息
        if (ListUtils.isNotEmpty(upDemandOrderAddressList)) {
            tDemandOrderAddressMapper.batchUpdateSelective(upDemandOrderAddressList);
        }
        //需求单车主表信息插入
        if (ListUtils.isNotEmpty(tDemandOrderCarrierAddList)){
            tDemandOrderCarrierMapper.batchInsertSelective(tDemandOrderCarrierAddList);
        }

        //需求单来源于TMS同步云仓
        if (ListUtils.isNotEmpty(synDemandOrderAddressList)) {
            SynDemandOrderAddressSourceToLeYiModel synDemandOrderAddressSourceToLeYiModel = new SynDemandOrderAddressSourceToLeYiModel();
            synDemandOrderAddressSourceToLeYiModel.setDemandOrderAddressList(synDemandOrderAddressList);
            synDemandOrderAddressSourceToLeYiModel.setOperateUserName(userName);
            rabbitMqPublishBiz.synDemandOrderAddressSourceToWarehouse(synDemandOrderAddressSourceToLeYiModel);
        }
    }

    /**
     * 获取云盘需求单列表
     */
    public PageInfo<DemandOrderForLeYiResponseModel> searchListForLeYi(DemandOrderSearchForLeYiRequestModel requestModel) {
        //如果下单开始时间跟下单结束时间为空的话，默认为当前-90
        if (ListUtils.isEmpty(requestModel.getDemandOrderCodeList()) && ListUtils.isEmpty(requestModel.getCustomerOrderCodeList())) {
            //非批量查询情况下
            if (StringUtils.isBlank(requestModel.getPublishTimeStart()) && StringUtils.isBlank(requestModel.getPublishTimeEnd())) {
                //选择待发布、待调度、部分调度状态筛选时，下单时间允许清空
                List<Integer> statusList = Arrays.asList(DemandOrderStatusEnum.WAIT_PUBLISH.getKey(),DemandOrderStatusEnum.WAIT_DISPATCH.getKey(),DemandOrderStatusEnum.PART_DISPATCH.getKey());
                if (CommonConstant.ONE.equals(requestModel.getRequestSource())){//后台
                    Integer status = null;
                    if (ListUtils.isNotEmpty(requestModel.getDemandStatusList())){
                        status = requestModel.getDemandStatusList().stream().filter(item -> !statusList.contains(item)).findFirst().orElse(null);
                    }
                    if (ListUtils.isEmpty(requestModel.getDemandStatusList()) || status != null) {
                        requestModel.setPublishTimeEnd(DateUtils.dateToString(new Date(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
                        Calendar now = Calendar.getInstance();
                        now.add(Calendar.DAY_OF_MONTH, -90);
                        requestModel.setPublishTimeStart(DateUtils.dateToString(now.getTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
                    }
                }else {//小程序
                    if (!statusList.contains(requestModel.getDemandStatus())) {
                        //如果下单开始时间跟下单结束时间为空的话，默认为当前-90
                        requestModel.setPublishTimeEnd(DateUtils.dateToString(new Date(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
                        Calendar now = Calendar.getInstance();
                        now.add(Calendar.DAY_OF_MONTH, -90);
                        requestModel.setPublishTimeStart(DateUtils.dateToString(now.getTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
                    }
                }
            }
        } else {
            //以批量查询条件为准,清掉默认的时间条件
            requestModel.setPublishTimeEnd("");
            requestModel.setPublishTimeStart("");
        }

        //入参排序转换
        processSortParams(requestModel);

        //需要排除的需求单号
        requestModel.setExcludeDemandIdList(workOrderBiz.getNeedExcludedOrderIds(WorkOrderTypeEnum.DEMAND_ORDER_TYPE, CommonConstant.INTEGER_ONE, null, null));

        //分页查询需求单id
        requestModel.enablePaging();
        List<Long> idList = tDemandOrderMapper.searchListForLeYiManageIds(requestModel);
        PageInfo page = new PageInfo(idList);
        if (ListUtils.isNotEmpty(idList)) {
            List<DemandOrderForLeYiResponseModel> demandOrderResponseModels = searchListForLeYiByIds(requestModel, idList);
            page.setList(demandOrderResponseModels);
        }
        return page;
    }

    public List<DemandOrderForLeYiResponseModel> searchListForLeYiByIds(DemandOrderSearchForLeYiRequestModel requestModel, List<Long> ids) {
        String demandOrderIds = StringUtils.listToString(ids, ',');
        //查询需求单信息
        List<DemandOrderForLeYiResponseModel> demandOrderResponseModels = tDemandOrderMapper.searchListForLeYiManageAddressGoodsDemand(requestModel, demandOrderIds);

        //查询需求单与s单关系信息
        List<TDemandOrderOrderRel> demandOrderOrderRelByDemandIds = tDemandOrderOrderRelMapper.getDemandOrderOrderRelByDemandIds(demandOrderIds);

        //组装s单备注信息（需求单id-》备注）
        Map<Long,String> demandOrderOrderRelRemarkMap=new HashMap<>();
        for (TDemandOrderOrderRel item : demandOrderOrderRelByDemandIds) {
            if(StringUtils.isBlank(item.getRemark())){
                continue;
            }
            String remark = demandOrderOrderRelRemarkMap.get(item.getDemandOrderId());
            if(StringUtils.isBlank(remark)){
                demandOrderOrderRelRemarkMap.put(item.getDemandOrderId(),item.getRemark());
            }else{
                demandOrderOrderRelRemarkMap.put(item.getDemandOrderId(),remark+"——"+item.getRemark());
            }
        }

        //拼接数据
        for (DemandOrderForLeYiResponseModel responseModel : demandOrderResponseModels) {
            //拼接备注信息
            String remark = demandOrderOrderRelRemarkMap.get(responseModel.getDemandId());
            if(StringUtils.isNotBlank(remark)){
                if(StringUtils.isNotBlank(responseModel.getRemark())){
                    responseModel.setRemark(responseModel.getRemark()+"——"+remark);
                }else{
                    responseModel.setRemark(remark);
                }
            }
        }
        return demandOrderResponseModels;
    }

    //入参排序转换
    private void processSortParams(DemandOrderSearchForLeYiRequestModel requestModel) {
        String sort = requestModel.getSort();
        String order = requestModel.getOrder();
        if ("desc".equals(order) || "asc".equals(order)) {
            requestModel.setOrder(order);
        } else {
            requestModel.setOrder("asc");
        }

        if ("notArrangedAmount".equals(sort)) {
            requestModel.setSort("(tdo.not_arranged_amount)");
        } else if ("expectedLoadTime".equals(sort)) {
            requestModel.setSort("(tdoa.expected_load_time)");
        } else if ("expectedUnloadTime".equals(sort)) {
            requestModel.setSort("(tdoa.expected_unload_time)");
        } else if ("publishTime".equals(sort)) {
            requestModel.setSort("(tdo.publish_time)");
        } else {
            requestModel.setSort(null);
            requestModel.setOrder(null);
        }
    }

    /**
     * 获取需求单详情
     *
     * @param requestModel
     */
    public DemandOrderDetailForLeYiResponseModel getDetailForLeYi(DemandOrderDetailRequestModel requestModel) {
        DemandOrderDetailForLeYiResponseModel demandOrderDetail = tDemandOrderMapper.getDemandOrderDetailForLeYi(requestModel);
        //需求单是否存在
        if (demandOrderDetail == null) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }

        //一个需求单下对应多个运单
        GetCarrierOrdersByDemandIdRequestModel getCarrierOrdersByDemandIdRequestModel = new GetCarrierOrdersByDemandIdRequestModel();
        getCarrierOrdersByDemandIdRequestModel.setDemandId(requestModel.getDemandId());
        List<DemandOrderCarrierDetailResponseModel> carrierOrdersModels = carrierOrderCommonBiz.getCarrierOrderInfoByDemandId(getCarrierOrdersByDemandIdRequestModel);
        demandOrderDetail.setCarrierResponseModel(MapperUtils.mapper(carrierOrdersModels, DemandOrderCarrierResponseModel.class));

        BigDecimal carrierOtherFeeTotal = BigDecimal.ZERO;
        //查询运单下的所有临时费用
        List<Long> carrierOrderIds = carrierOrdersModels.stream().map(DemandOrderCarrierDetailResponseModel::getCarrierOrderId).collect(Collectors.toList());
        List<TCarrierOrderOtherFee> carrierOrderOtherFeeList = tCarrierOrderOtherFeeMapper.getAuditByCarrierOrderIds(LocalStringUtil.listTostring(carrierOrderIds, ','));
        for (TCarrierOrderOtherFee tCarrierOrderOtherFee : carrierOrderOtherFeeList) {
            if (tCarrierOrderOtherFee.getTotalAmount() != null) {
                carrierOtherFeeTotal = carrierOtherFeeTotal.add(tCarrierOrderOtherFee.getTotalAmount());
            }
        }
        //所有运单临时费用
        demandOrderDetail.setCarrierOtherFeeTotal(carrierOtherFeeTotal);

        //需求单车主结算费用
        TDemandPayment tDemandPayment = tDemandPaymentMapper.getByDemandOrderId(demandOrderDetail.getDemandId());
        if (tDemandPayment != null && tDemandPayment.getValid().equals(IfValidEnum.VALID.getKey())) {
            demandOrderDetail.setCarrierPriceType(tDemandPayment.getPriceType());
            demandOrderDetail.setCarrierSettlementAmount(tDemandPayment.getSettlementAmount());
            demandOrderDetail.setCarrierSettlementCostTotal(tDemandPayment.getSettlementCostTotal());
        }

        //需求单货主结算费用
        TDemandReceivement tDemandReceivement = tDemandReceivementMapper.getByDemandOrderId(demandOrderDetail.getDemandId());
        if (tDemandReceivement != null && tDemandReceivement.getValid().equals(IfValidEnum.VALID.getKey())){
            demandOrderDetail.setEntrustPriceType(tDemandReceivement.getPriceType());
            demandOrderDetail.setEntrustSettlementAmount(tDemandReceivement.getSettlementAmount());
            demandOrderDetail.setEntrustSettlementCostTotal(tDemandReceivement.getSettlementCostTotal());
        }

        //查询车主信息
        TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(demandOrderDetail.getCompanyCarrierId());
        if (tCompanyCarrier != null && tCompanyCarrier.getValid().equals(IfValidEnum.VALID.getKey())){
            demandOrderDetail.setIsOurCompany(tCompanyCarrier.getLevel());
        }

        //查询需求单货物
        List<DemandOrderGoodsResponseModel> tDemandOrderGoodsByDemandId = tDemandOrderGoodsMapper.getTDemandOrderGoodsByDemandId(demandOrderDetail.getDemandId());
        demandOrderDetail.setGoodsResponseModel(tDemandOrderGoodsByDemandId);
        return demandOrderDetail;

    }

    /**
     * 模糊搜索仓库地址(发布页面调用云盘仓库接口)
     *
     * @param requestModel
     * @return
     */
    public List<SearchDemandUnLoadAddressResponseModel> searchYPWarehouse(SearchDemandUnLoadAddressRequestModel requestModel) {
        List<SearchDemandUnLoadAddressResponseModel> responseModelList = new ArrayList<>();
        List<ListWarehouseByNameResponseModel> list = warehouseStockClient.listWarehouseByName(requestModel.getUnloadSearchName());
        if (ListUtils.isEmpty(list)) {
            return responseModelList;
        }
        SearchDemandUnLoadAddressResponseModel responseModel;
        for (ListWarehouseByNameResponseModel model : list) {
            responseModel = new SearchDemandUnLoadAddressResponseModel();
            responseModel.setWarehouseId(model.getId());
            responseModel.setUnloadProvinceId(model.getProvinceId());
            responseModel.setUnloadProvinceName(model.getProvinceName());
            responseModel.setUnloadCityId(model.getCityId());
            responseModel.setUnloadCityName(model.getCityName());
            responseModel.setUnloadAreaId(model.getAreaId());
            responseModel.setUnloadAreaName(model.getAreaName());
            responseModel.setUnloadDetailAddress(model.getDetailAddress());
            responseModel.setUnloadWarehouse(model.getChineseName());
            responseModel.setReceiverName(model.getContactName());
            responseModel.setReceiverMobile(model.getContactMobile());
            responseModelList.add(responseModel);
        }
        return responseModelList;
    }

    /**
     * 确认放空（需求单仅存在【已放空】且无其他有效状态运单&【待调度】需求单）
     * @param requestModel
     */
    @Transactional
    public void confirmEmpty(DemandOrderEmptyRequestModel requestModel) {
        List<TDemandOrder> dbDemandOrderList = tDemandOrderMapper.getByIds(requestModel.getDemandIds());
        if (ListUtils.isEmpty(dbDemandOrderList)) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }
        //判断运单：查询需求单下的运单
        List<DemandCarrierOrderRecursiveModel> demandCarrierOrderList = tCarrierOrderMapper.getByDemandOrderIds(requestModel.getDemandIds());
        if (ListUtils.isEmpty(demandCarrierOrderList)) {
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EMPTY_STATUS_ERROR.getCode(), dbDemandOrderList.get(CommonConstant.INTEGER_ZERO).getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_EMPTY_STATUS_ERROR.getMsg());
        }
        Map<Long, List<DemandCarrierOrderModel>> demandCarrierOrderMap = new HashMap<>();
        demandCarrierOrderList.forEach(item -> demandCarrierOrderMap.put(item.getDemandOrderId(), item.getCarrierOrderList()));
        List<DemandCarrierOrderModel> carrierOrderModelList;
        boolean demandIfEmptyFlag = false;
        //判断需求单
        for (TDemandOrder tDemandOrder : dbDemandOrderList) {
            //非云盘单子不能操作
            if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(tDemandOrder.getSource())){
                throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY.getCode(), tDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY.getMsg());
            }
            // 后补需求单不允许放空
            if (CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfExtDemandOrder())) {
                throw new BizException(EntrustDataExceptionEnum.SYSTEM_NOT_SUPPORT.getCode(), tDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.SYSTEM_NOT_SUPPORT.getMsg());
            }
            //已取消
            if (CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfCancel())) {
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EMPTY_STATUS_ERROR.getCode(), tDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_EMPTY_STATUS_ERROR.getMsg());
            }
            //已放空
            if (CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfEmpty())) {
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EMPTY_STATUS_ERROR.getCode(), tDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_EMPTY_STATUS_ERROR.getMsg());
            }
            //需求单已回退不能操作
            if(CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfRollback())){
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EMPTY_STATUS_ERROR.getCode(), tDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_EMPTY_STATUS_ERROR.getMsg());
            }
            //不是待调度状态
            if (!DemandOrderStatusEnum.WAIT_DISPATCH.getKey().equals(tDemandOrder.getEntrustStatus())) {
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EMPTY_STATUS_ERROR.getCode(), tDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_EMPTY_STATUS_ERROR.getMsg());
            }
            //判断运单
            carrierOrderModelList = demandCarrierOrderMap.get(tDemandOrder.getId());
            //需求单下不存在运单
            if (ListUtils.isEmpty(carrierOrderModelList)) {
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EMPTY_STATUS_ERROR.getCode(), tDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_EMPTY_STATUS_ERROR.getMsg());
            }
            //需求单下的运单必须要有已放空且无正常状态的运单
            for (DemandCarrierOrderModel tCarrierOrder : carrierOrderModelList) {
                if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfEmpty())){
                    demandIfEmptyFlag = true;
                }else if (!CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfCancel())){
                    break;
                }
            }
            if (!demandIfEmptyFlag){
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EMPTY_STATUS_ERROR.getCode(), tDemandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_EMPTY_STATUS_ERROR.getMsg());
            }
        }

        //需求单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkDemandExceptionExist(requestModel.getDemandIds());
        if (!workOrderMap.isEmpty()){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EXCEPTION);
        }

        //查询需求单是否存在异常信息
        List<TDemandOrderObjection> tDemandOrderObjectionList = tDemandOrderObjectionMapper.getByDemandOrderIds(requestModel.getDemandIds());
        Map<Long, Long> demandOrderObjectionMap = tDemandOrderObjectionList.stream().collect(Collectors.toMap(TDemandOrderObjection::getDemandOrderId, TDemandOrderObjection::getId));

        //更新需求单和记录异常
        TDemandOrderObjection addDemandOrderObjection;
        List<TDemandOrderObjection> addDemandOrderObjectionList = new ArrayList<>();
        TDemandOrder upDemandOrder;
        List<TDemandOrder> upDemandOrderList = new ArrayList<>();
        TDemandOrderOperateLogs addDemandOrderOperateLogs;
        List<TDemandOrderOperateLogs> addDemandOrderOperateLogsList = new ArrayList<>();
        TDemandOrderObjection upDemandOrderObjection;
        List<TDemandOrderObjection> upDemandOrderObjectionList = new ArrayList<>();
        TDemandOrderEvents demandOrderEvents;
        List<String> demandOrderCodesRecycle = new ArrayList<>();
        CancelDemandCodesAndTypeModel cancelDemandCodesAndTypeModel;
        List<CancelDemandCodesAndTypeModel> demandOrderCodesLeYi = new ArrayList<>();
        CancelDemandOrderBackAmountModel amountModel;
        Map<Long, CancelDemandOrderBackAmountModel> backAmountModelMap = new HashMap<>();
        List<TDemandOrderEvents> events = new ArrayList<>();
        Date now = new Date();
        String emptyReason = "【"+DemandOrderObjectionTypeEnum.getEnum(requestModel.getObjectionType()).getValue()+"】"+requestModel.getObjectionReason();

        for (TDemandOrder tDemandOrder : dbDemandOrderList) {
            //将需求单置为已放空
            upDemandOrder = new TDemandOrder();
            upDemandOrder.setId(tDemandOrder.getId());
            upDemandOrder.setNotArrangedAmount(CommonConstant.BIG_DECIMAL_ZERO);
            upDemandOrder.setBackAmount(tDemandOrder.getNotArrangedAmount().add(tDemandOrder.getBackAmount()));
            upDemandOrder.setIfEmpty(CommonConstant.INTEGER_ONE);
            commonBiz.setBaseEntityModify(upDemandOrder, BaseContextHandler.getUserName());
            upDemandOrderList.add(upDemandOrder);

            //记录异常
            if (demandOrderObjectionMap.get(tDemandOrder.getId()) != null){
                upDemandOrderObjection = new TDemandOrderObjection();
                upDemandOrderObjection.setId(demandOrderObjectionMap.get(tDemandOrder.getId()));
                upDemandOrderObjection.setCustomerName(requestModel.getCustomerName());
                upDemandOrderObjection.setObjectionType(requestModel.getObjectionType());
                upDemandOrderObjection.setObjectionReason(requestModel.getObjectionReason());
                upDemandOrderObjection.setReportContactName(BaseContextHandler.getUserName());
                upDemandOrderObjection.setReportTime(now);
                commonBiz.setBaseEntityModify(upDemandOrderObjection, BaseContextHandler.getUserName());
                upDemandOrderObjectionList.add(upDemandOrderObjection);
            }else {
                addDemandOrderObjection = new TDemandOrderObjection();
                addDemandOrderObjection.setDemandOrderId(tDemandOrder.getId());
                addDemandOrderObjection.setCustomerName(requestModel.getCustomerName());
                addDemandOrderObjection.setObjectionType(requestModel.getObjectionType());
                addDemandOrderObjection.setObjectionReason(requestModel.getObjectionReason());
                addDemandOrderObjection.setReportContactName(BaseContextHandler.getUserName());
                addDemandOrderObjection.setReportTime(now);
                commonBiz.setBaseEntityAdd(addDemandOrderObjection, BaseContextHandler.getUserName());
                addDemandOrderObjectionList.add(addDemandOrderObjection);
            }

            //记录日志
            addDemandOrderOperateLogs = new TDemandOrderOperateLogs();
            addDemandOrderOperateLogs.setDemandOrderId(tDemandOrder.getId());
            addDemandOrderOperateLogs.setOperationType(DemandOrderOperateLogsEnum.DEMAND_ORDER_EMPTY.getKey());
            addDemandOrderOperateLogs.setOperationContent(DemandOrderOperateLogsEnum.DEMAND_ORDER_EMPTY.getValue());
            addDemandOrderOperateLogs.setRemark(emptyReason);
            addDemandOrderOperateLogs.setOperatorName(BaseContextHandler.getUserName());
            addDemandOrderOperateLogs.setOperateTime(now);
            commonBiz.setBaseEntityAdd(addDemandOrderOperateLogs, BaseContextHandler.getUserName());
            addDemandOrderOperateLogsList.add(addDemandOrderOperateLogs);

            demandOrderEvents=new TDemandOrderEvents();
            demandOrderEvents.setDemandOrderId(tDemandOrder.getId());
            demandOrderEvents.setCompanyCarrierId(tDemandOrder.getCompanyCarrierId());
            demandOrderEvents.setEvent(DemandOrderEventsTypeEnum.EMPTY.getKey());
            demandOrderEvents.setEventDesc(DemandOrderEventsTypeEnum.EMPTY.getValue());
            demandOrderEvents.setEventTime(now);
            demandOrderEvents.setOperateTime(now);
            demandOrderEvents.setOperatorName(BaseContextHandler.getUserName());
            commonBiz.setBaseEntityAdd(demandOrderEvents,BaseContextHandler.getUserName());
            events.add(demandOrderEvents);

            //回收类型同步云盘系统
            if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(tDemandOrder.getEntrustType())
                    || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(tDemandOrder.getEntrustType())) {
                demandOrderCodesRecycle.add(tDemandOrder.getDemandOrderCode());
            }
            //非回收类型同步云仓
            else{
                cancelDemandCodesAndTypeModel=new CancelDemandCodesAndTypeModel();
                cancelDemandCodesAndTypeModel.setDemandCode(tDemandOrder.getDemandOrderCode());
                cancelDemandCodesAndTypeModel.setType(tDemandOrder.getEntrustType());
                demandOrderCodesLeYi.add(cancelDemandCodesAndTypeModel);

                //需求单回退的数量
                amountModel = new CancelDemandOrderBackAmountModel();
                amountModel.setCancelBackAmount(tDemandOrder.getNotArrangedAmount());
                amountModel.setDemandOrderCode(tDemandOrder.getDemandOrderCode());
                amountModel.setLastModifiedBy(BaseContextHandler.getUserName());
                backAmountModelMap.put(tDemandOrder.getId(), amountModel);
            }
        }

        //货物回退
        List<TDemandOrderGoods> tDemandOrderGoodsList = tDemandOrderGoodsMapper.getDemandOrderGoodsByDemandOrderIds(requestModel.getDemandIds());
        TDemandOrderGoods demandOrderGoods;
        List<TDemandOrderGoods> upDemandOrderGoodsList = new ArrayList<>();
        for (TDemandOrderGoods tDemandOrderGoods : tDemandOrderGoodsList) {
            demandOrderGoods = new TDemandOrderGoods();
            demandOrderGoods.setId(tDemandOrderGoods.getId());
            demandOrderGoods.setNotArrangedAmount(CommonConstant.BIG_DECIMAL_ZERO);
            demandOrderGoods.setBackAmount(tDemandOrderGoods.getNotArrangedAmount().add(tDemandOrderGoods.getBackAmount()));
            commonBiz.setBaseEntityAdd(demandOrderGoods, BaseContextHandler.getUserName());
            upDemandOrderGoodsList.add(demandOrderGoods);
        }

        List<TDemandOrderOrderRel> orderRelByDemandIds = tDemandOrderOrderRelMapper.getDemandOrderOrderRelByDemandIds(requestModel.getDemandIds());
        //s单数据 退回数据
        List<TDemandOrderOrderRel> relList = new ArrayList<>();
        List<CancelDemandOrderBackAmountModel> returnBackAmount = new ArrayList<>();

        if (ListUtils.isNotEmpty(orderRelByDemandIds)) {
            Iterator<Map.Entry<Long, CancelDemandOrderBackAmountModel>> iterator = backAmountModelMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Long, CancelDemandOrderBackAmountModel> next = iterator.next();
                Long key = next.getKey();
                CancelDemandOrderBackAmountModel value = next.getValue();
                List<CancelDemandOrderSaleOrderBackAmountModel> amountModels = new ArrayList<>();
                orderRelByDemandIds.stream().filter(s -> s.getDemandOrderId().equals(key)).forEach(s -> {
                    if ((s.getTotalAmount().subtract(s.getArrangedAmount()).subtract(s.getBackAmount())).compareTo(CommonConstant.BIG_DECIMAL_ZERO) > CommonConstant.INTEGER_ZERO) {
                        TDemandOrderOrderRel orderOrderRel = new TDemandOrderOrderRel();
                        orderOrderRel.setId(s.getId());
                        orderOrderRel.setBackAmount(s.getBackAmount().add(s.getTotalAmount().subtract(s.getArrangedAmount()).subtract(s.getBackAmount())));
                        commonBiz.setBaseEntityModify(orderOrderRel, BaseContextHandler.getUserName());
                        relList.add(orderOrderRel);

                        CancelDemandOrderSaleOrderBackAmountModel backAmountModel = new CancelDemandOrderSaleOrderBackAmountModel();
                        backAmountModel.setOrderBackAmount(s.getTotalAmount().subtract(s.getArrangedAmount()).subtract(s.getBackAmount()));
                        backAmountModel.setOrderId(s.getOrderId());
                        amountModels.add(backAmountModel);
                    }
                });
                value.setModels(amountModels);
                returnBackAmount.add(value);
            }
        }

        tDemandOrderMapper.batchUpdateByPrimaryKeySelective(upDemandOrderList);
        tDemandOrderGoodsMapper.batchUpdateByPrimaryKeySelective(upDemandOrderGoodsList);
        tDemandOrderOperateLogsMapper.batchInsertSelective(addDemandOrderOperateLogsList);
        tDemandOrderEventsMapper.batchInsertSelective(events);
        if (ListUtils.isNotEmpty(addDemandOrderObjectionList)){
            tDemandOrderObjectionMapper.batchInsert(addDemandOrderObjectionList);
        }
        if (ListUtils.isNotEmpty(upDemandOrderObjectionList)){
            tDemandOrderObjectionMapper.batchUpdate(upDemandOrderObjectionList);
        }
        if (ListUtils.isNotEmpty(relList)) {
            tDemandOrderOrderRelMapper.batchUpdateByPrimaryKeySelective(relList);
        }

        //生成需求单结算信息
        CreateSettlementForEntrustConsumerModel createSettlementForEntrustConsumerModel = new CreateSettlementForEntrustConsumerModel();
        createSettlementForEntrustConsumerModel.setDemandOrderIds(requestModel.getDemandIds());
        createSettlementForEntrustConsumerModel.setUserName(BaseContextHandler.getUserName());
        demandOrderBiz.createSettlementCost(createSettlementForEntrustConsumerModel);

        if (ListUtils.isNotEmpty(demandOrderCodesRecycle)) {
            //放空按回退操作同步云盘（云盘操作取消后同步云仓取消）
            RollbackDemandOrderToLeYiModel rollbackDemandOrderToLeYiModel = new RollbackDemandOrderToLeYiModel();
            rollbackDemandOrderToLeYiModel.setDemandCodeList(demandOrderCodesRecycle);
            rollbackDemandOrderToLeYiModel.setCancelReason(requestModel.getObjectionReason());
            rollbackDemandOrderToLeYiModel.setCancelType(requestModel.getObjectionType());
            rollbackDemandOrderToLeYiModel.setUserName(BaseContextHandler.getUserName());
            rabbitMqPublishBiz.rollbackDemandOrderToLeYi(rollbackDemandOrderToLeYiModel);
        }
        if (ListUtils.isNotEmpty(demandOrderCodesLeYi)){
            CancelDemandByDemandOrderMessage message = new CancelDemandByDemandOrderMessage();
            message.setBackAmountModels(returnBackAmount);
            message.setItems(demandOrderCodesLeYi);
            message.setCancelReason(requestModel.getObjectionReason());
            message.setCancelType(DemandOrderCancelTypeEnum.COMPANY.getKey());
            message.setUserName(BaseContextHandler.getUserName());
            rabbitMqPublishBiz.cancelDemandOrderSyn(message);
        }
    }

    /**
     * 云盘物流看板--调度报警
     *
     * @return 调度报警信息
     */
    public List<DispatchAlarmStatisticsResponseModel> dispatchAlarmStatistics() {
        //查询统计的数量
        List<DispatchAlarmStatisticsResponseModel> dispatchAlarmStatisticsList = tDemandOrderMapper.dispatchAlarmStatistics();
        if (ListUtils.isNotEmpty(dispatchAlarmStatisticsList)) {
            //提货市id list
            List<Long> loadCityIdList = new ArrayList<>();
            for (DispatchAlarmStatisticsResponseModel model : dispatchAlarmStatisticsList) {
                loadCityIdList.add(model.getLoadCityId());
            }
            //查询每个市存在大区负责人的最新单子地址id
            List<Long> addressIdList = tDemandOrderAddressMapper.dispatchAlarmStatisticsAddress(loadCityIdList);
            if (ListUtils.isNotEmpty(addressIdList)) {
                List<TDemandOrderAddress> tDemandOrderAddressList = tDemandOrderAddressMapper.getByIds(StringUtils.listToString(addressIdList, ','));
                Map<Long, String> loadRegionContactNameMap = new HashMap<>();
                tDemandOrderAddressList.forEach(item -> loadRegionContactNameMap.put(item.getLoadCityId(), item.getLoadRegionContactName()));

                //拼接数据
                for (DispatchAlarmStatisticsResponseModel model : dispatchAlarmStatisticsList) {
                    model.setLoadRegionContactName(loadRegionContactNameMap.get(model.getLoadCityId()));
                }
            }
        }
        return dispatchAlarmStatisticsList;
    }

    /**
     * 云盘物流看板-待调度、待发布
     *
     * @return 待发布, 待调度数据
     */
    public WaitDispatchStatisticsResponseModel waitDispatchStatistics() {

        WaitDispatchStatisticsResponseModel responseModel = new WaitDispatchStatisticsResponseModel();
        //查询待调度待发布的需求单
        List<WaitDispatchStatisticsModel> waitPublishDispatchModels = tDemandOrderMapper.waitDispatchStatistics();
        //非空判断
        if (ListUtils.isNotEmpty(waitPublishDispatchModels)) {
            //分组 分出待发布和待调度的
            Map<Integer, List<WaitDispatchStatisticsModel>> demandOrderStatisticsMap = waitPublishDispatchModels.stream().collect(Collectors.groupingBy(WaitDispatchStatisticsModel::getStatus));

            List<WaitDispatchStatisticsModel> waitDispatchListALL = new ArrayList<>();
            //待调度
            List<WaitDispatchStatisticsModel> waitDispatchList = demandOrderStatisticsMap.get(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
            if (ListUtils.isNotEmpty(waitDispatchList)) {
                waitDispatchListALL.addAll(waitDispatchList);
            }

            //部分调度
            List<WaitDispatchStatisticsModel> partDispatchList = demandOrderStatisticsMap.get(DemandOrderStatusEnum.PART_DISPATCH.getKey());
            if (ListUtils.isNotEmpty(partDispatchList)) {
                waitDispatchListALL.addAll(partDispatchList);
            }

            //待发布
            List<WaitDispatchStatisticsModel> waitPublishList = demandOrderStatisticsMap.get(DemandOrderStatusEnum.WAIT_PUBLISH.getKey());

            //设置响应model属性值
            responseModel.setWaitDispatchList(waitDispatchListALL)
                    .setWaitPublishList(waitPublishList);
        }
        return responseModel;
    }

    /**
     * 云盘物流看板--合计数据
     *
     * @return 合计数据
     */
    public AggregateDataStatisticsResponseModel aggregateDataStatistics() {
        //查询所有需求单
        List<DemandOrderDataStatisticsResponseModel> demandOrders =tDemandOrderMapper.selectDemandOrders();
        //查询待纠错运单数量和货物数量
        CorrectCarrierOrderDataStatisticsResponseModel correctCarrierOrder = tCarrierOrderMapper.selectCorrectCarrierOrderCount();
        AggregateDataStatisticsResponseModel aggregateDataStatisticsResponseModel = new AggregateDataStatisticsResponseModel();
        aggregateDataStatisticsResponseModel.setDemandOrders(demandOrders);
        aggregateDataStatisticsResponseModel.setCorrectCarrierOrder(correctCarrierOrder);
        //返回结果
        return aggregateDataStatisticsResponseModel;
    }

    /**
     * 云盘物流看板-地图数据
     *
     * @return 市维度数据
     */
    public List<MapDataStatisticsResponseModel> mapDataStatistics() {
        List<MapDataStatisticsResponseModel> responseModelList = new ArrayList<>();
        //查询待调度需求单数、市id
        List<MapDataStatisticsResponseModel> demandOrderList = tDemandOrderMapper.mapDataStatistics();
        //查询待提货,待纠错运单数、市id
        List<MapDataStatisticsResponseModel> carrierOrderList = tCarrierOrderMapper.mapDataStatistics();
        demandOrderList.addAll(carrierOrderList);//合并两个集合

        //集合非空判断
        if (ListUtils.isNotEmpty(demandOrderList)) {
            //获取结果集里面所有的城市ID 去基础数据查询城市对应经纬度 将经纬度信息封装为 cityId-经纬度 映射的map
            List<Long> loadCityIdList = demandOrderList.stream().map(MapDataStatisticsResponseModel::getLoadCityId).collect(Collectors.toList());
            Map<Long, GetLonLatByMapIdResponseModel> lonLatIdMap = basicDataClient.getLonLatByMapIds(loadCityIdList).stream().collect(Collectors.toMap(GetLonLatByMapIdResponseModel::getMapId, lonLatMode -> lonLatMode));

            //把合并后的集合根据cityId 分组
            Map<Long, List<MapDataStatisticsResponseModel>> mapDataList = demandOrderList.stream().collect(Collectors.groupingBy(MapDataStatisticsResponseModel::getLoadCityId));
            //处理相同cityId的数据
            mapDataList.forEach((cityId, mapDataModel) -> {
                //抽出相同cityId list中的第一条数据
                MapDataStatisticsResponseModel innerMapDataModel = null;
                //遍历根据cityId分组后的数据
                for (MapDataStatisticsResponseModel cycleMapDataModel : mapDataModel) {
                    if (innerMapDataModel == null) {
                        innerMapDataModel = cycleMapDataModel;//初始化innerMapDataModel
                        //根据cityId从 cityId-经纬度map 中获取经纬度model
                        GetLonLatByMapIdResponseModel lonLatMode = lonLatIdMap.get(innerMapDataModel.getLoadCityId());
                        if (lonLatMode != null) { //先设置经纬度
                            innerMapDataModel.setLongitude(lonLatMode.getLongitude());
                            innerMapDataModel.setLatitude(lonLatMode.getLatitude());
                        }
                    } else { //补充累加第一条数据里面的,待纠错 待提货 待调度数量
                        //待纠错
                        innerMapDataModel.setWaitCorrectCount(innerMapDataModel.getWaitCorrectCount() + cycleMapDataModel.getWaitCorrectCount());
                        //待提货
                        innerMapDataModel.setWaitLoadCount(innerMapDataModel.getWaitLoadCount() + cycleMapDataModel.getWaitLoadCount());
                        //待调度
                        innerMapDataModel.setWaitDispatchCount(innerMapDataModel.getWaitDispatchCount() + cycleMapDataModel.getWaitDispatchCount());
                    }
                }
                //处理完的放入结果集
                responseModelList.add(innerMapDataModel);
            });
        }

        //return结果
        return responseModelList;

    }

    /**
     * 根据需求单ID获取需求单客户单号详情
     *
     * @param requestModel 需求单客户单号详情
     */
    public List<DemandOrderOrderRelResponseModel> getDemandOrderOrders(DemandOrderDetailRequestModel requestModel) {
        return tDemandOrderOrderRelMapper.getDemandOrderOrders(requestModel.getDemandId());
    }

    /**
     * 云盘同步需求单到tms系统
     *
     * @param message
     */
    @Transactional
    public void saveSyncTrayStockPlanDemandOrderToTms(SyncTrayDemandOrderMessage message) {
        //判断需求单是否存在
        TDemandOrder dbDemandOrder = tDemandOrderMapper.getByCode(message.getDemandOrderCode());
        if (dbDemandOrder != null){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_EXIST);
        }

        //查询云盘公司
        String leyiCompanyEntrustName = commonBiz.getLeyiCompanyName();
        TCompanyEntrust companyEntrust = tCompanyEntrustMapper.getByName(leyiCompanyEntrustName);
        if (companyEntrust == null) {
            throw new BizException(EntrustDataExceptionEnum.COMPANY_ENTRUST_EMPTY);
        }

        Date now = new Date();
        Date createdTime;
        if (message.getPublishTime().before(now)){
            createdTime = now;
        }else{
            createdTime = message.getPublishTime();
        }

        boolean ifExt = message.getIfExtDemandOrder() == 1;

        //需求单
        TDemandOrder tDemandOrder = new TDemandOrder();
        tDemandOrder.setStatus(DemandOrderStatusEnum.WAIT_PUBLISH.getKey());
        tDemandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_PUBLISH.getKey());
        tDemandOrder.setStatusUpdateTime(now);
        tDemandOrder.setGoodsAmount(message.getGoodsAmount());
        tDemandOrder.setDemandOrderCode(message.getDemandOrderCode());
        tDemandOrder.setEntrustType(message.getEntrustType());
        tDemandOrder.setNotArrangedAmount(message.getGoodsAmount());
        tDemandOrder.setRemark(message.getRemark());
        tDemandOrder.setPublishName(message.getPublishName());
        tDemandOrder.setPublishMobile(message.getLogisticsDemandSponsorMobilePhone());
        tDemandOrder.setPublishTime(message.getPublishTime());
        tDemandOrder.setNotArrangedAmount(message.getGoodsAmount());
        tDemandOrder.setCompanyEntrustId(companyEntrust.getId());
        tDemandOrder.setCompanyEntrustName(leyiCompanyEntrustName);
        tDemandOrder.setGoodsUnit(message.getGoodsUnit());
        tDemandOrder.setSettlementTonnage(companyEntrust.getSettlementTonnage());
        tDemandOrder.setSource(DemandOrderSourceEnum.LEYI_TRAY.getKey());
        tDemandOrder.setIfUrgent(message.getIfUrgent());
        tDemandOrder.setAvailableOnWeekends(ConverterUtils.toInteger(message.getAvailableOnWeekends()));
        tDemandOrder.setLoadingUnloadingPart(ConverterUtils.toInteger(message.getLoadingUnloadingPart()));
        tDemandOrder.setLoadingUnloadingCharge(message.getLoadingUnloadingCharge());
        tDemandOrder.setRecycleTaskType(message.getRecycleTaskType());
        tDemandOrder.setGroundPushTaskCode(message.getGroundPushTaskCode());
        tDemandOrder.setProjectLabel(message.getProjectLabel());
        tDemandOrder.setUpstreamCustomer(message.getUpstreamCustomer());
        tDemandOrder.setLbsCode(message.getLBScode());
        tDemandOrder.setFixedDemand(message.getFixedDemand());
        if (ifExt){
            tDemandOrder.setIfExtDemandOrder(1);
        }
        if (ListUtils.isNotEmpty(message.getOrdersModels())) {
            List<String> codeList = new ArrayList<>();
            message.getOrdersModels().forEach(item ->
                    codeList.add(item.getOrderCode())
            );
            tDemandOrder.setCustomerOrderCode(StringUtils.listToString(codeList, ','));
        }
        // 查询下单人部门
        if (StringUtils.isNotBlank(message.getPublishName())) {
            if (!ifExt){

                try {
                    OrgForHierarchyModel orgForHierarchy = basicDataClient.getOrgForHierarchyByUserName(message.getPublishName());
                    tDemandOrder.setPublishOrgCode(orgForHierarchy.getOrgCode());
                    tDemandOrder.setPublishOrgName(orgForHierarchy.getOrgName());
                } catch (Exception e) {
                    log.warn("同步托盘需求单 -> 获取下单人部门接口请求失败, DemandOrderCode:{}, publishName:{}, Exception:{}",
                            message.getDemandOrderCode(), message.getPublishName(), e.getMessage());
                }
            }else {
                tDemandOrder.setPublishOrgCode(message.getPublishOrgCode());
                tDemandOrder.setPublishOrgName(message.getPublishOrgName());
            }

        }
        tDemandOrder.setOrderMode(OrderModeEnum.DEFAULT.getKey());
        commonBiz.setBaseEntityAdd(tDemandOrder, message.getPublishName(), createdTime);
        tDemandOrderMapper.insertSelectiveEncrypt(tDemandOrder);

        List<TDemandOrderOrderRel> relations = new ArrayList<>();
        if (ListUtils.isNotEmpty(message.getOrdersModels())) {
            TDemandOrderOrderRel tmp;
            for (SyncTrayDemandOrderOrdersModel item:message.getOrdersModels()) {
                tmp = new TDemandOrderOrderRel();
                tmp.setTotalAmount(item.getTotalAmount());
                tmp.setOrderCode(item.getOrderCode());
                tmp.setOrderId(item.getOrderId());
                tmp.setDemandOrderId(tDemandOrder.getId());
                tmp.setRelType(DemandOrderOrderRelTypeEnum.PUBLISH.getKey());
                commonBiz.setBaseEntityAdd(tmp, message.getPublishName(), createdTime);
                relations.add(tmp);
            }
        }

        //需求单地址
        TDemandOrderAddress tDemandOrderAddress = new TDemandOrderAddress();
        tDemandOrderAddress.setDemandOrderId(tDemandOrder.getId());

        tDemandOrderAddress.setLoadYeloAddressCode(message.getLoadYeloAddressCode());
        tDemandOrderAddress.setLoadAddressCode(message.getLoadWarehouseCode());
        tDemandOrderAddress.setLoadProvinceId(message.getLoadProvinceId());
        tDemandOrderAddress.setLoadProvinceName(message.getLoadProvinceName());
        tDemandOrderAddress.setLoadCityId(message.getLoadCityId());
        tDemandOrderAddress.setLoadCityName(message.getLoadCityName());
        tDemandOrderAddress.setLoadAreaId(message.getLoadAreaId());
        tDemandOrderAddress.setLoadAreaName(message.getLoadAreaName());
        tDemandOrderAddress.setLoadDetailAddress(message.getLoadDetailAddress());
        tDemandOrderAddress.setLoadWarehouse(message.getLoadWarehouse());
        if (message.getExpectedLoadTime() == null){
            Calendar expectedLoadTime = Calendar.getInstance();
            expectedLoadTime.setTime(now);
            expectedLoadTime.add(Calendar.DATE,CommonConstant.INTEGER_TWO);
            tDemandOrderAddress.setExpectedLoadTime(expectedLoadTime.getTime());
        }else {
            tDemandOrderAddress.setExpectedLoadTime(message.getExpectedLoadTime());
        }
        if(StringUtils.isBlank(message.getConsignorMobile()) || StringUtils.isBlank(message.getConsignorName())){
            tDemandOrderAddress.setConsignorMobile(CommonConstant.YANG_HUI_PHONE);
            tDemandOrderAddress.setConsignorName(CommonConstant.YANG_HUI_NAME);
        }else{
            tDemandOrderAddress.setConsignorMobile(message.getConsignorMobile());
            tDemandOrderAddress.setConsignorName(message.getConsignorName());
        }
        tDemandOrderAddress.setUnloadCompany(message.getReceiveCustomerCompanyName());
        tDemandOrderAddress.setUnloadProvinceId(message.getUnloadProvinceId());
        tDemandOrderAddress.setUnloadProvinceName(message.getUnloadProvinceName());
        tDemandOrderAddress.setUnloadCityId(message.getUnloadCityId());
        tDemandOrderAddress.setUnloadCityName(message.getUnloadCityName());
        tDemandOrderAddress.setUnloadAreaId(message.getUnloadAreaId());
        tDemandOrderAddress.setUnloadAreaName(message.getUnloadAreaName());
        tDemandOrderAddress.setUnloadDetailAddress(message.getUnloadDetailAddress());
        tDemandOrderAddress.setUnloadWarehouse(message.getUnloadWarehouse());
        if (StringUtils.isBlank(message.getUnloadProvinceName())){
            tDemandOrderAddress.setUnloadAddressIsAmend(CommonConstant.INTEGER_ONE);
        }
        if (message.getExpectedUnloadTime() == null){
            Calendar expectedUnloadTime = Calendar.getInstance();
            expectedUnloadTime.setTime(now);
            expectedUnloadTime.add(Calendar.DATE,CommonConstant.INTEGER_SEVEN);
            tDemandOrderAddress.setExpectedUnloadTime(expectedUnloadTime.getTime());
        }else {
            tDemandOrderAddress.setExpectedUnloadTime(message.getExpectedUnloadTime());
        }
        if(!EntrustTypeEnum.RECYCLE_IN.getKey().equals(message.getEntrustType()) && !EntrustTypeEnum.RECYCLE_OUT.getKey().equals(message.getEntrustType())
                &&(StringUtils.isBlank(message.getReceiverMobile()) || StringUtils.isBlank(message.getReceiverName()))){
            tDemandOrderAddress.setReceiverMobile(CommonConstant.YANG_HUI_PHONE);
            tDemandOrderAddress.setReceiverName(CommonConstant.YANG_HUI_NAME);
        }else{
            tDemandOrderAddress.setReceiverMobile(message.getReceiverMobile());
            tDemandOrderAddress.setReceiverName(message.getReceiverName());
        }
        commonBiz.setBaseEntityAdd(tDemandOrderAddress, message.getPublishName(), createdTime);
        tDemandOrderAddressMapper.insertSelective(tDemandOrderAddress);

        //需求单货物
        List<SyncTrayDemandOrderGoodsModel> goodsModels = message.getGoodsModels();
        if (ListUtils.isEmpty(goodsModels)) {
            return;
        }
        TDemandOrderGoods orderGood;
        List<TDemandOrderGoods> orderGoodsList = new ArrayList<>();
        TDemandOrderGoodsRel goodsRel;
        List<TDemandOrderGoodsRel> goodsRelList = new ArrayList<>();
        for (SyncTrayDemandOrderGoodsModel model : goodsModels) {
            orderGood = new TDemandOrderGoods();
            orderGood.setGoodsAmount(model.getGoodsAmount());
            orderGood.setSkuCode(model.getProductTypeCode());
            if (StringUtils.isBlank(model.getGoodsName())){
                //云盘C1客户下的单没有二级类名称，故将大类名称赋值给品名字段
                orderGood.setGoodsName(model.getCategoryName());
            }else {
                orderGood.setGoodsName(model.getGoodsName());
            }
            orderGood.setCategoryName(model.getCategoryName());
            orderGood.setHeight(model.getHeight());
            orderGood.setLength(model.getLength());
            orderGood.setWidth(model.getWidth());
            orderGood.setDemandOrderId(tDemandOrder.getId());
            orderGood.setNotArrangedAmount(model.getGoodsAmount());
            commonBiz.setBaseEntityAdd(orderGood, message.getPublishName(), createdTime);
            if (EntrustTypeEnum.BOOKING.getKey().equals(message.getEntrustType())) {//预约单
                tDemandOrderGoodsMapper.insertSelective(orderGood);
                goodsRel = new TDemandOrderGoodsRel();
                goodsRel.setDemandOrderGoodsId(orderGood.getId());
                goodsRel.setBookingOrderGoodsId(model.getGoodsId());
                commonBiz.setBaseEntityAdd(goodsRel, message.getPublishName(), createdTime);
                goodsRelList.add(goodsRel);
            }else{
                orderGoodsList.add(orderGood);
            }
        }

        //需求单与云盘客户关系
        if (message.getTrayCustomerCompanyId() != null
                && message.getTrayCustomerAddressId() != null
                && message.getProductCategoryId() != null){
            TDemandOrderCustomerRel tDemandOrderCustomerRel = new TDemandOrderCustomerRel();
            tDemandOrderCustomerRel.setDemandOrderId(tDemandOrder.getId());
            tDemandOrderCustomerRel.setTrayCustomerCompanyId(message.getTrayCustomerCompanyId());
            tDemandOrderCustomerRel.setTrayCustomerAddressId(message.getTrayCustomerAddressId());
            tDemandOrderCustomerRel.setProductCategoryId(message.getProductCategoryId());
            commonBiz.setBaseEntityAdd(tDemandOrderCustomerRel, message.getPublishName(), createdTime);
            tDemandOrderCustomerRelMapper.insertSelective(tDemandOrderCustomerRel);
        }
        if (ListUtils.isNotEmpty(orderGoodsList)) {
            tDemandOrderGoodsMapper.batchInsert(orderGoodsList);
        }
        if (ListUtils.isNotEmpty(goodsRelList)) {
            tDemandOrderGoodsRelMapper.batchInsert(goodsRelList);
        }
        //记录日志
        TDemandOrderOperateLogs demandOrderOperateLogs = demandOrderCommonBiz.getDemandOrderOperateLogs(tDemandOrder.getId(), DemandOrderOperateLogsEnum.CREATE_DEMAND_ORDER, message.getPublishName(), "");
        tDemandOrderOperateLogsMapper.insertSelective(demandOrderOperateLogs);

        if (ListUtils.isNotEmpty(relations)) {
            tDemandOrderOrderRelMapper.batchInsertDemandOrderOrderRelSelective(relations);
        }


        if (ifExt){
            //保存补单的关系
            TExtDemandOrderRelation insertRelation = new TExtDemandOrderRelation();
            insertRelation.setCarrierOrderId(message.getOrgCarrierOrderId());
            insertRelation.setCarrierOrderCode(message.getOrgCarrierOrderCode());
            insertRelation.setDemandOrderId(message.getOrgDemandOrderId());
            insertRelation.setDemandOrderCode(message.getOrgDemandOrderCode());
            insertRelation.setExtDemandOrderId(tDemandOrder.getId());
            insertRelation.setExtDemandOrderCode(tDemandOrder.getDemandOrderCode());
            commonBiz.setBaseEntityAdd(insertRelation,BaseContextHandler.getUserName());
            tExtDemandOrderRelationMapper.insertSelective(insertRelation);

            //todo 请求云盘
        }



        // 发布智能推送需求单下单事件
        applicationContext.publishEvent(new WorkGroupEventModel()
                .setWorkGroupPushBoModels(
                        tDemandOrder.getId(),
                        tDemandOrder.getSource(),
                        tDemandOrder.getEntrustType(),
                        tDemandOrder.getProjectLabel(),
                        WorkGroupOrderTypeEnum.DEMAND_ORDER,
                        WorkGroupOrderNodeEnum.DEMAND_ORDER_CREATE
                ));

        //异步查询地址经纬度
        List<TDemandOrderAddress> addDemandOrderAddressList = new ArrayList<>();
        addDemandOrderAddressList.add(tDemandOrderAddress);
        AsyncProcessQueue.execute(() -> demandOrderCommonBiz.updateAddressLonAndLat(addDemandOrderAddressList));
    }

    /**
     * 托盘同步H单追加到tms（补单暂未同步）
     *
     * @param  model
     */
    @Transactional
    public void syncAdditionalOrderFromLeyiTray(TmsSyncAdditionalOrderModel model) {
        TDemandOrder dbDemandOrder = tDemandOrderMapper.getByCode(model.getDemandOrderCode());
        if(dbDemandOrder==null ){
            rabbitMqPublishBiz.syncAdditionalOrderToNetworkFreight(model);
            return;
        }
        Integer demandOrderOrderRelType;
        if (CommonConstant.INTEGER_ONE.equals(model.getType())) {
            demandOrderOrderRelType = DemandOrderOrderRelTypeEnum.ADDITIONAL.getKey();
        } else if (CommonConstant.INTEGER_TWO.equals(model.getType())) {
            demandOrderOrderRelType = DemandOrderOrderRelTypeEnum.REPLENISH.getKey();
        } else {
            return;
        }
        //需求单状态校验
        if(!checkParamsSyncAdditionalOrder(dbDemandOrder,model)){
            log.info("=========状态错误=======");
            return;
        }

        //查需求单下已有的R单
        List<TDemandOrderOrderRel> dbDemandOrderRelList = tDemandOrderOrderRelMapper.getDemandOrderOrderRelByDemandIds(dbDemandOrder.getId().toString());
        Map<Long, TDemandOrderOrderRel> dbDemandOrderRelMap = new HashMap<>();
        for (TDemandOrderOrderRel orderRel : dbDemandOrderRelList) {
            dbDemandOrderRelMap.put(orderRel.getOrderId(), orderRel);
        }

        BigDecimal additionalAmount = BigDecimal.ZERO;//追加的数量
        BigDecimal goodsAmount = BigDecimal.ZERO;
        BigDecimal notArrangedAmount = BigDecimal.ZERO;
        TDemandOrderOrderRel tDemandOrderOrderRel;
        List<TDemandOrderOrderRel> addRelList=new ArrayList<>();
        List<TDemandOrderOrderRel> upRelList=new ArrayList<>();
        TDemandOrderOrderRel dbDemandOrderOrderRel;
        RecyclePublishUpdateDemandRequestModel recyclePublishUpdateDemandRequestModel = null;
        for(AdditionalOrderModel additionalOrderModel : model.getAdditionalOrderModelList()) {
            //更新需求单上的总数量
            goodsAmount = goodsAmount.add(additionalOrderModel.getTotalAmount());
            notArrangedAmount = notArrangedAmount.add(additionalOrderModel.getTotalAmount());

            //同步过来的R单中，数据库里已存在，如果数量不等则更新；不存在则新增
            dbDemandOrderOrderRel = dbDemandOrderRelMap.get(additionalOrderModel.getOrderId());
            if (dbDemandOrderOrderRel != null){
                if (dbDemandOrderOrderRel.getTotalAmount().compareTo(additionalOrderModel.getTotalAmount()) != CommonConstant.INTEGER_ZERO) {
                    tDemandOrderOrderRel = new TDemandOrderOrderRel();
                    tDemandOrderOrderRel.setId(dbDemandOrderOrderRel.getId());
                    tDemandOrderOrderRel.setTotalAmount(additionalOrderModel.getTotalAmount());
                    commonBiz.setBaseEntityModify(tDemandOrderOrderRel, model.getPublishName());
                    upRelList.add(tDemandOrderOrderRel);

                    //追加的数量
                    additionalAmount = additionalAmount.add(additionalOrderModel.getTotalAmount()).subtract(dbDemandOrderOrderRel.getTotalAmount());
                }
            }else {
                //需求单订单关系新增
                tDemandOrderOrderRel = new TDemandOrderOrderRel();
                tDemandOrderOrderRel.setDemandOrderId(dbDemandOrder.getId());
                tDemandOrderOrderRel.setOrderCode(additionalOrderModel.getOrderCode());
                tDemandOrderOrderRel.setTotalAmount(additionalOrderModel.getTotalAmount());
                tDemandOrderOrderRel.setRelType(demandOrderOrderRelType);
                tDemandOrderOrderRel.setOrderId(additionalOrderModel.getOrderId());
                commonBiz.setBaseEntityAdd(tDemandOrderOrderRel, model.getPublishName());
                addRelList.add(tDemandOrderOrderRel);

                //追加的数量
                additionalAmount = additionalAmount.add(additionalOrderModel.getTotalAmount());
            }
        }
        //如果是追加的R单，则H单上数量增加
        if(DemandOrderOrderRelTypeEnum.ADDITIONAL.getKey().equals(demandOrderOrderRelType)){
            //追加逻辑
            //追加需求单表货物数量信息
            TDemandOrder updateDemandOrder=new TDemandOrder();
            updateDemandOrder.setId(dbDemandOrder.getId());
            updateDemandOrder.setGoodsAmount(goodsAmount);
            updateDemandOrder.setNotArrangedAmount(notArrangedAmount);
            commonBiz.setBaseEntityModify(updateDemandOrder,model.getPublishName());

            //追加需求单货物表数量信息，因回收单仅一个货物，故取第一个
            List<DemandOrderGoodsResponseModel> demandGoods = tDemandOrderGoodsMapper.getTDemandOrderGoodsByDemandId(dbDemandOrder.getId());
            DemandOrderGoodsResponseModel dbDemandGoods = demandGoods.get(CommonConstant.INTEGER_ZERO);
            TDemandOrderGoods goods=new TDemandOrderGoods();
            goods.setId(dbDemandGoods.getDemandOrderGoodsId());
            goods.setGoodsAmount(goodsAmount);
            goods.setNotArrangedAmount(notArrangedAmount);
            commonBiz.setBaseEntityModify(goods,model.getPublishName());

            tDemandOrderMapper.updateByPrimaryKeySelectiveEncrypt(updateDemandOrder);
            tDemandOrderGoodsMapper.updateByPrimaryKeySelective(goods);

            //打标的回收入库类型且是自动发布，需求单追加的数量需同步给智慧运营系统
            if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(dbDemandOrder.getEntrustType())
                    && StringUtils.isNotBlank(dbDemandOrder.getFixedDemand())
                    && CommonConstant.INTEGER_ONE.equals(dbDemandOrder.getAutoPublish())) {

                recyclePublishUpdateDemandRequestModel = new RecyclePublishUpdateDemandRequestModel();
                recyclePublishUpdateDemandRequestModel.setConfigCode(dbDemandOrder.getFixedDemand());
                recyclePublishUpdateDemandRequestModel.setDemandOrderNo(dbDemandOrder.getDemandOrderCode());
                recyclePublishUpdateDemandRequestModel.setEntrustNum(additionalAmount);
            }
        }
        if (ListUtils.isNotEmpty(addRelList)) {
            // 多个R单 时，备注落在第一条记录里
            addRelList.get(CommonConstant.INTEGER_ZERO).setRemark(model.getRemark());
            tDemandOrderOrderRelMapper.batchInsertDemandOrderOrderRelSelective(addRelList);
        }
        if (ListUtils.isNotEmpty(upRelList)) {
            // 多个R单 时，备注落在第一条记录里
            upRelList.get(CommonConstant.INTEGER_ZERO).setRemark(model.getRemark());
            tDemandOrderOrderRelMapper.batchUpdateByPrimaryKeySelective(upRelList);
        }

        if (recyclePublishUpdateDemandRequestModel != null){
            commonBiz.synRecyclePublishUpdateDemand(Collections.singletonList(recyclePublishUpdateDemandRequestModel));
        }
    }

    /**
     * 托盘同步H单补单到tms----需求单状态校验
     *
     * @param  demandOrder
     * @param  model
     */
    private boolean checkParamsSyncAdditionalOrder(TDemandOrder demandOrder, TmsSyncAdditionalOrderModel model) {
        //需求单取消、放空 不可进行追加或补单操作
        if(CommonConstant.INTEGER_ONE.equals(demandOrder.getIfCancel()) || CommonConstant.INTEGER_ONE.equals(demandOrder.getIfEmpty()) || CommonConstant.INTEGER_ONE.equals(demandOrder.getIfRollback())){
            return false;
        }
        if(CommonConstant.INTEGER_ONE.equals(model.getType())){
            //追加
            if(demandOrder.getEntrustStatus()> DemandOrderStatusEnum.WAIT_DISPATCH.getKey()){
                return false;
            }
        }else if(CommonConstant.INTEGER_TWO.equals(model.getType())){
            //补单
            if(!DemandOrderStatusEnum.SIGN_DISPATCH.getKey().equals(demandOrder.getEntrustStatus())){
                return false;
            }
        }

        return true;
    }

    /**
     * 云盘需求单-智能拼单
     *
     * @return
     */
    public SmartSpellListResponseModel smartSpellList() {
        return MapperUtils.mapper(bigDataClient.smartSpellList(), SmartSpellListResponseModel.class);
    }


    /**
     * 云盘需求单修改车主详情查询
     *
     * @param requestModel 需求单id集合
     * @return 详情信息
     */
    public List<ModifyCarrierDetailForLeyiResponseModel> modifyCarrierDetailForLeyi(ModifyCarrierDetailForLeyiRequestModel requestModel) {
        String demandOrderIds = StringUtils.listToString(requestModel.getDemandOrderIds(),',');
        //查询要修改车主的需求单
        List<TDemandOrder> tDemandOrders = tDemandOrderMapper.getByIds(demandOrderIds);
        if (ListUtils.isEmpty(tDemandOrders)) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }

        ModifyCarrierDetailForLeyiResponseModel responseModel;
        List<ModifyCarrierDetailForLeyiResponseModel> responseModelList = new ArrayList<>();
        //校验需求单状态
        for (TDemandOrder tDemandOrder : tDemandOrders) {
            //非云盘需求单
            if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(tDemandOrder.getSource())) {
                throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
            }
            //非待调度状态无法操作
            if (!DemandOrderStatusEnum.WAIT_DISPATCH.getKey().equals(tDemandOrder.getStatus())) {
                throw new BizException(EntrustDataExceptionEnum.ONLY_WAIT_DISPATCH_DEMAND_ORDER_CAN_OPERATE);
            }
            //放空状态不能操作
            if (CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfCancel())) {
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY);
            }
            //需求单已回退不能操作
            if(CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfRollback())){
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_ROLLBACK);
            }
            //异常状态不能操作
            if (CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfObjection())) {
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_OBJECTION);
            }

            responseModel = new ModifyCarrierDetailForLeyiResponseModel();
            responseModel.setDemandOrderId(tDemandOrder.getId());
            responseModelList.add(responseModel);
        }

        //需求单下有放空的运单，则不允许更换车主
        List<DemandOrderCarrierDetailResponseModel> orderInfoByDemandId = tCarrierOrderMapper.getEmptyCarrierOrderInfoByDemandIds(demandOrderIds);
        if (ListUtils.isNotEmpty(orderInfoByDemandId)) {
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_HAVE_EMPTY_ORDER);
        }

        //需求单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkDemandExceptionExist(demandOrderIds);
        if (!workOrderMap.isEmpty()){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EXCEPTION);
        }

        return responseModelList;
    }

    /**
     * 云盘需求单修改车主
     *
     * @param requestModel 修改车主信息
     */
    @Transactional
    public void modifyCarrierForLeyi(ModifyCarrierForLeyiRequestModel requestModel) {
        //校验
        ModifyCarrierDetailForLeyiRequestModel detailRequestModel = new ModifyCarrierDetailForLeyiRequestModel();
        detailRequestModel.setDemandOrderIds(requestModel.getDemandOrderIds());
        modifyCarrierDetailForLeyi(detailRequestModel);

        //查询要更换的车主信息
        CompanyCarrierByIdModel companyCarrierByIdModel = demandOrderCommonBiz.companyCarrierInfoByIsOurCompany(requestModel.getIsOurCompany(), requestModel.getCompanyCarrierId());

        //修改需求单
        TDemandOrder upDemandOrder;
        List<TDemandOrder> upDemandOrderList = new ArrayList<>();
        List<TDemandOrderEvents> tDemandOrderEventsAddList = new ArrayList<>();
        List<TDemandOrderOperateLogs> tDemandOrderOperateLogsList = new ArrayList<>();

        //修改需求单车主
        TDemandOrderCarrier upTDemandOrderCarrier;
        List<TDemandOrderCarrier> upTDemandOrderCarrierList = new ArrayList<>();

        //新增需求单车主
        TDemandOrderCarrier demandOrderCarrierAdd;
        List<TDemandOrderCarrier> demandOrderCarrierAddList = new ArrayList<>();

        List<TDemandOrder> tDemandOrders = tDemandOrderMapper.getByIds(LocalStringUtil.listTostring(requestModel.getDemandOrderIds(), ','));

        //需求单原车主id
        List<Long> oldCompanyCarrierIds = tDemandOrders.stream().map(TDemandOrder::getCompanyCarrierId).collect(Collectors.toList());
        //修改前车主修改原因
        Map<Long, TDemandOrderCarrier> demandOrderCarrierMap = tDemandOrderCarrierMapper.getNewestDemandCarrierByDemandOrderIds(StringUtils.join(requestModel.getDemandOrderIds(), ','), StringUtils.join(oldCompanyCarrierIds, ','))
                .stream().collect(Collectors.toMap(TDemandOrderCarrier::getDemandOrderId, item -> item, (t, t2) -> t.getCreatedTime().getTime() > t2.getCreatedTime().getTime() ? t : t2));
        for (TDemandOrder tDemandOrder : tDemandOrders) {
            //校验车主是否修改
            if (tDemandOrder.getCompanyCarrierId().equals(companyCarrierByIdModel.getCompanyCarrierId())) {
                throw new BizException(EntrustDataExceptionEnum.CARRIER_NOT_CHANGE);
            }

            // 后补需求单不允许修改车主
            if (CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfExtDemandOrder())) {
                throw new BizException(EntrustDataExceptionEnum.SYSTEM_NOT_SUPPORT);
            }

            //更新需求单
            upDemandOrder = new TDemandOrder();
            upDemandOrder.setId(tDemandOrder.getId());
            upDemandOrder.setCompanyCarrierId(companyCarrierByIdModel.getCompanyCarrierId());
            upDemandOrder.setCompanyCarrierType(companyCarrierByIdModel.getCompanyCarrierType());
            upDemandOrder.setCompanyCarrierLevel(companyCarrierByIdModel.getCompanyCarrierLevel());
            //个人车主
            if (CommonConstant.INTEGER_TWO.equals(companyCarrierByIdModel.getCompanyCarrierType())) {
                upDemandOrder.setCompanyCarrierName("");
                upDemandOrder.setCarrierContactId(companyCarrierByIdModel.getCarrierContactId());
                upDemandOrder.setCarrierContactName(companyCarrierByIdModel.getCarrierContactName());
                upDemandOrder.setCarrierContactPhone(companyCarrierByIdModel.getCarrierContactPhone());
            }
            //企业车主
            else {
                upDemandOrder.setCompanyCarrierName(companyCarrierByIdModel.getCompanyCarrierName());
                upDemandOrder.setCarrierContactId(CommonConstant.LONG_ZERO);
                upDemandOrder.setCarrierContactName("");
                upDemandOrder.setCarrierContactPhone("");
            }

            commonBiz.setBaseEntityModify(upDemandOrder, BaseContextHandler.getUserName());
            upDemandOrderList.add(upDemandOrder);

            //添加取消事件，接单事件
            tDemandOrderEventsAddList.add(demandOrderCommonBiz.generateEvent(tDemandOrder.getId(), tDemandOrder.getCompanyCarrierId(), DemandOrderEventsTypeEnum.CANCEL_DEMANDORDER, BaseContextHandler.getUserName()));
            tDemandOrderEventsAddList.add(demandOrderCommonBiz.generateEvent(tDemandOrder.getId(), upDemandOrder.getCompanyCarrierId(), DemandOrderEventsTypeEnum.ACCEPT_CARRIERORDER, BaseContextHandler.getUserName()));

            TDemandOrderCarrier tDemandOrderCarrier = demandOrderCarrierMap.get(tDemandOrder.getId());
            //修改前车主修改原因
            if (tDemandOrderCarrier != null) {
                upTDemandOrderCarrier = new TDemandOrderCarrier();
                upTDemandOrderCarrier.setId(tDemandOrderCarrier.getId());
                upTDemandOrderCarrier.setCancelReason(requestModel.getModifyReason());
                commonBiz.setBaseEntityModify(upTDemandOrderCarrier, BaseContextHandler.getUserName());
                upTDemandOrderCarrierList.add(upTDemandOrderCarrier);
            }

            //添加新的车主记录
            demandOrderCarrierAdd = new TDemandOrderCarrier();
            demandOrderCarrierAdd.setDemandOrderId(tDemandOrder.getId());
            demandOrderCarrierAdd.setCompanyCarrierId(companyCarrierByIdModel.getCompanyCarrierId());
            demandOrderCarrierAdd.setCarrierContactId(companyCarrierByIdModel.getCarrierContactId());
            commonBiz.setBaseEntityAdd(demandOrderCarrierAdd, BaseContextHandler.getUserName());
            demandOrderCarrierAddList.add(demandOrderCarrierAdd);

            //生成修改车主日志
            String beforeCompanyName = CompanyTypeEnum.PERSON.getKey().equals(tDemandOrder.getCompanyCarrierType()) ? tDemandOrder.getCarrierContactName() + " " +
                    tDemandOrder.getCarrierContactPhone() : tDemandOrder.getCompanyCarrierName();
            String afterCompanyName = CompanyTypeEnum.PERSON.getKey().equals(companyCarrierByIdModel.getCompanyCarrierType()) ? companyCarrierByIdModel.getCarrierContactName() + " " +
                    companyCarrierByIdModel.getCarrierContactPhone() : companyCarrierByIdModel.getCompanyCarrierName();
            String operateLogRemark = String.format(DemandOrderOperateLogsEnum.MODIFY_CARRIER.getFormat(), beforeCompanyName, afterCompanyName, requestModel.getModifyReason());
            tDemandOrderOperateLogsList.add(demandOrderCommonBiz.getDemandOrderOperateLogs(tDemandOrder.getId(), DemandOrderOperateLogsEnum.MODIFY_CARRIER, BaseContextHandler.getUserName(), operateLogRemark));
        }

        if (ListUtils.isNotEmpty(upDemandOrderList)) {
            tDemandOrderMapper.batchUpdateByPrimaryKeySelective(upDemandOrderList);
        }

        if (ListUtils.isNotEmpty(tDemandOrderEventsAddList)) {
            tDemandOrderEventsMapper.batchInsertSelective(tDemandOrderEventsAddList);
        }

        if (ListUtils.isNotEmpty(tDemandOrderOperateLogsList)) {
            tDemandOrderOperateLogsMapper.batchInsertSelective(tDemandOrderOperateLogsList);
        }

        if (ListUtils.isNotEmpty(upTDemandOrderCarrierList)) {
            tDemandOrderCarrierMapper.batchUpdate(upTDemandOrderCarrierList);
        }

        if (ListUtils.isNotEmpty(demandOrderCarrierAddList)) {
            tDemandOrderCarrierMapper.batchInsertSelective(demandOrderCarrierAddList);
        }
    }

    /**
     * 云盘需求单回退
     * @param requestModel
     */
    @Transactional
    public void rollbackDemandOrder(RollbackDemandOrderRequestModel requestModel) {
        TDemandOrder tDemandOrder = tDemandOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getDemandId());
        if (tDemandOrder == null || IfValidEnum.INVALID.getKey().equals(tDemandOrder.getValid())) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }
        //非云盘单子不能操作
        if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(tDemandOrder.getSource())){
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }
        //已取消
        if (CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfCancel())){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_ROLLBACK_ERROR);
        }
        //已放空
        if (CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfEmpty())){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_ROLLBACK_ERROR);
        }
        //需求单已回退不能操作
        if(CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfRollback())){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_ROLLBACK_ERROR);
        }
        //不是待发布、待调度状态，不能操作
        if (!(DemandOrderStatusEnum.WAIT_PUBLISH.getKey().equals(tDemandOrder.getEntrustStatus())
                || DemandOrderStatusEnum.WAIT_DISPATCH.getKey().equals(tDemandOrder.getEntrustStatus()))){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_ROLLBACK_ERROR);
        }
        //不是回收类型
        if (!EntrustTypeEnum.RECYCLE_IN.getKey().equals(tDemandOrder.getEntrustType()) && !EntrustTypeEnum.RECYCLE_OUT.getKey().equals(tDemandOrder.getEntrustType())){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_ROLLBACK_ERROR);
        }
        //需求单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkDemandExceptionExist(requestModel.getDemandId().toString());
        if (!workOrderMap.isEmpty()){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EXCEPTION);
        }

        //查询开关 是否是直接取消
        Map<String, String> configMap = sysConfigBiz.getSysConfig(ConfigKeyEnum.ConfigGroupCodeEnum.DEMAND_ORDER_FLAG.getCode());
        if (MapUtils.isEmpty(configMap)) {
            throw new BizException(CarrierDataExceptionEnum.CODE_RULE_GROUP_NOT_EXIST);
        }
        String ifCancel = configMap.get(ConfigKeyEnum.ROLLBACK_DEMAND_ORDER_FLAG.getValue());
        if (CommonConstant.ONE.equals(ifCancel)){
            //直接取消
            RollbackDemandRequestModel rollbackDemandRequestModel = new RollbackDemandRequestModel();
            rollbackDemandRequestModel.setLogisticsDemandCode(tDemandOrder.getDemandOrderCode());
            rollbackDemandRequestModel.setRollbackCauseType(requestModel.getRollbackCauseType());
            rollbackDemandRequestModel.setRollbackCauseTypeTwo(requestModel.getRollbackCauseTypeTwo());
            rollbackDemandRequestModel.setRollbackRemark(requestModel.getRollbackRemark());
            rollbackDemandRequestModel.setIfTmsCancel(1);
            Result<Boolean> result = customerInOrderServiceApi.rollBackDemandOrder(rollbackDemandRequestModel);
            result.throwException();

            TDemandOrder demandOrder = new TDemandOrder();
            demandOrder.setId(tDemandOrder.getId());
            demandOrder.setIfRollback(CommonConstant.INTEGER_ONE);
            demandOrder.setRollbackCauseType(requestModel.getRollbackCauseType());
            demandOrder.setRollbackCauseTypeTwo(requestModel.getRollbackCauseTypeTwo());
            demandOrder.setRollbackRemark(requestModel.getRollbackRemark());
            commonBiz.setBaseEntityModify(demandOrder, BaseContextHandler.getUserName());
            tDemandOrderMapper.updateByPrimaryKeySelectiveEncrypt(demandOrder);

            return;
        }

        //获取回退原因
        DemandOrderCancelTypeEnum.DemandOrderCancelTypeTwoEnum typeTwoEnum = DemandOrderCancelTypeEnum.DemandOrderCancelTypeTwoEnum.getEnum(requestModel.getRollbackCauseTypeTwo());
        DemandOrderCancelTypeEnum typeEnum = typeTwoEnum.getTypeEnum();
        //拼接操作日志备注
        String remark = typeEnum.getValue() +
                "-" +
                typeTwoEnum.getValue() +
                "，" +
                requestModel.getRollbackRemark();

        //更新需求单
        TDemandOrder demandOrder = new TDemandOrder();
        demandOrder.setId(tDemandOrder.getId());
        demandOrder.setIfRollback(CommonConstant.INTEGER_ONE);
        demandOrder.setRollbackCauseType(requestModel.getRollbackCauseType());
        demandOrder.setRollbackCauseTypeTwo(requestModel.getRollbackCauseTypeTwo());
        demandOrder.setRollbackRemark(requestModel.getRollbackRemark());
        commonBiz.setBaseEntityModify(demandOrder, BaseContextHandler.getUserName());
        tDemandOrderMapper.updateByPrimaryKeySelectiveEncrypt(demandOrder);

        //记录操作日志
        DemandOrderOperateLogsEnum logsEnum = DemandOrderOperateLogsEnum.DEMAND_ORDER_ROLLBACK;

        TDemandOrderOperateLogs addDemandOrderOperateLogs = new TDemandOrderOperateLogs();
        addDemandOrderOperateLogs.setDemandOrderId(tDemandOrder.getId());
        addDemandOrderOperateLogs.setOperationType(logsEnum.getKey());
        addDemandOrderOperateLogs.setOperationContent(logsEnum.getValue());
        addDemandOrderOperateLogs.setRemark(remark);
        addDemandOrderOperateLogs.setOperatorName(BaseContextHandler.getUserName());
        addDemandOrderOperateLogs.setOperateTime(new Date());
        commonBiz.setBaseEntityAdd(addDemandOrderOperateLogs, BaseContextHandler.getUserName());
        tDemandOrderOperateLogsMapper.insertSelective(addDemandOrderOperateLogs);

        //将回退信息同步给云盘
        RollbackDemandOrderToLeYiModel rollbackDemandOrderToLeYiModel = new RollbackDemandOrderToLeYiModel();
        rollbackDemandOrderToLeYiModel.setDemandCodeList(Collections.singletonList(tDemandOrder.getDemandOrderCode()));
        rollbackDemandOrderToLeYiModel.setCancelReason(requestModel.getRollbackRemark());
        rollbackDemandOrderToLeYiModel.setCancelType(requestModel.getRollbackCauseType());
        rollbackDemandOrderToLeYiModel.setRollbackCauseTypeTwo(requestModel.getRollbackCauseTypeTwo());
        rollbackDemandOrderToLeYiModel.setUserName(BaseContextHandler.getUserName());
        rabbitMqPublishBiz.rollbackDemandOrderToLeYi(rollbackDemandOrderToLeYiModel);

        // 发布智能推送取消节点事件
        applicationContext.publishEvent(new WorkGroupEventModel()
                .setWorkGroupPushBoModels(tDemandOrder.getId(),
                tDemandOrder.getSource(),
                tDemandOrder.getEntrustType(),
                tDemandOrder.getProjectLabel(),
                WorkGroupOrderTypeEnum.DEMAND_ORDER,
                WorkGroupOrderNodeEnum.DEMAND_ORDER_CANCEL));
    }

    /**
     * 驳回回退的需求单（云盘重新推送需求单调用）
     * @param requestModel
     * @return
     */
    @Transactional
    public List<RejectRollbackDemandOrderResponseModel> rejectRollbackDemandOrder(RejectRollbackDemandOrderRequestModel requestModel) {
        //解析入参
        List<String> requestDemandOrderList = new ArrayList<>();
        Map<String, String> reasonMap = new HashMap<>();
        for (RejectRollbackDemandOrderListModel model : requestModel.getDemandOrderList()) {
            if (!requestDemandOrderList.contains(model.getDemandCode())){
                requestDemandOrderList.add(model.getDemandCode());
            }
            reasonMap.put(model.getDemandCode(), model.getRejectReason());
        }
        //查询需求单
        List<TDemandOrder> dbDemandOrderList = tDemandOrderMapper.getByCodeList(requestDemandOrderList);
        if (ListUtils.isEmpty(dbDemandOrderList)){
            List<RejectRollbackDemandOrderResponseModel> responseModelList = new ArrayList<>();
            RejectRollbackDemandOrderResponseModel responseModel;
            for (RejectRollbackDemandOrderListModel model : requestModel.getDemandOrderList()) {
                responseModel = new RejectRollbackDemandOrderResponseModel();
                responseModel.setDemandCode(model.getDemandCode());
                responseModel.setFailReason(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY.getMsg());
                responseModel.setIfSuccess(CommonConstant.INTEGER_ZERO);
                responseModelList.add(responseModel);
            }
            return responseModelList;
        }

        Date now = new Date();
        DemandOrderOperateLogsEnum logsEnum = DemandOrderOperateLogsEnum.DEMAND_ORDER_REJECT_ROLLBACK;

        List<RejectRollbackDemandOrderResponseModel> responseModelList = new ArrayList<>();
        RejectRollbackDemandOrderResponseModel responseModel;

        TDemandOrder upDemandOrder;
        List<TDemandOrder> upDemandOrderList = new ArrayList<>();
        TDemandOrderOperateLogs addDemandOrderOperateLogs;
        List<TDemandOrderOperateLogs> addLogsList = new ArrayList<>();
        //遍历需求单
        for (TDemandOrder dbDemandOrder : dbDemandOrderList) {
            //从请求入参里去掉数据库里存在的需求单号
            requestDemandOrderList.remove(dbDemandOrder.getDemandOrderCode());

            //构建返参
            responseModel = new RejectRollbackDemandOrderResponseModel();
            responseModel.setDemandCode(dbDemandOrder.getDemandOrderCode());
            responseModel.setIfSuccess(CommonConstant.INTEGER_ONE);
            responseModel.setDemandOrderStatus(dbDemandOrder.getEntrustStatus());
            responseModelList.add(responseModel);

            //需求单已取消
            if (CommonConstant.INTEGER_ONE.equals(dbDemandOrder.getIfCancel())){
                responseModel.setFailReason(EntrustDataExceptionEnum.DEMANDORDER_CANCEL.getMsg());
                responseModel.setIfSuccess(CommonConstant.INTEGER_ZERO);
                responseModel.setDemandOrderStatus(DemandOrderStatusEnum.CANCEL_DISPATCH.getKey());
                continue;
            }
            //需求单已放空
            if (CommonConstant.INTEGER_ONE.equals(dbDemandOrder.getIfEmpty())){
                responseModel.setFailReason(EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getMsg());
                responseModel.setIfSuccess(CommonConstant.INTEGER_ZERO);
                responseModel.setDemandOrderStatus(DemandOrderStatusEnum.ORDER_EMPTY.getKey());
                continue;
            }
            //需求单非【已回退】状态
            if (!CommonConstant.INTEGER_ONE.equals(dbDemandOrder.getIfRollback())){
                responseModel.setFailReason(EntrustDataExceptionEnum.DEMAND_ORDER_STATUS_CHANGE.getMsg());
                responseModel.setIfSuccess(CommonConstant.INTEGER_ZERO);
                continue;
            }

            //更新需求单
            upDemandOrder = new TDemandOrder();
            upDemandOrder.setId(dbDemandOrder.getId());
            upDemandOrder.setIfRollback(CommonConstant.INTEGER_ZERO);
            upDemandOrder.setRollbackCauseType(CommonConstant.INTEGER_ZERO);
            upDemandOrder.setRollbackCauseTypeTwo(CommonConstant.INTEGER_ZERO);
            upDemandOrder.setRollbackRemark(CommonConstant.EMPTY_STRING);
            commonBiz.setBaseEntityModify(upDemandOrder, requestModel.getOperator());
            upDemandOrderList.add(upDemandOrder);

            //记录操作日志
            addDemandOrderOperateLogs = new TDemandOrderOperateLogs();
            addDemandOrderOperateLogs.setDemandOrderId(dbDemandOrder.getId());
            addDemandOrderOperateLogs.setOperationType(logsEnum.getKey());
            addDemandOrderOperateLogs.setOperationContent(logsEnum.getValue());
            addDemandOrderOperateLogs.setRemark(reasonMap.get(dbDemandOrder.getDemandOrderCode()));
            addDemandOrderOperateLogs.setOperatorName(requestModel.getOperator());
            addDemandOrderOperateLogs.setOperateTime(now);
            commonBiz.setBaseEntityAdd(addDemandOrderOperateLogs, requestModel.getOperator(), now);
            addLogsList.add(addDemandOrderOperateLogs);
        }
        if (ListUtils.isNotEmpty(upDemandOrderList)){
            tDemandOrderMapper.batchUpdateByPrimaryKeySelective(upDemandOrderList);
        }
        if (ListUtils.isNotEmpty(addLogsList)){
            tDemandOrderOperateLogsMapper.batchInsertSelective(addLogsList);
        }
        //如果入参需求单号不存在于数据库，则算失败
        if (ListUtils.isNotEmpty(requestDemandOrderList)){
            for (String demandCode : requestDemandOrderList) {
                responseModel = new RejectRollbackDemandOrderResponseModel();
                responseModel.setDemandCode(demandCode);
                responseModel.setFailReason(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY.getMsg());
                responseModel.setIfSuccess(CommonConstant.INTEGER_ZERO);
                responseModelList.add(responseModel);
            }
        }
        return responseModelList;
    }

    /**
     * 移动办公小程序首页统计
     *
     * @return
     */
    public DemandOrderStatisticsResponseModel demandOrderStatistics() {
        //待发布  待调度(包含部分调度) 数量
        DemandOrderStatisticsResponseModel demandOrderStatisticsResponseModel = tDemandOrderMapper.demandOrderStatistics(workOrderBiz.getNeedExcludedOrderIds(WorkOrderTypeEnum.DEMAND_ORDER_TYPE, CommonConstant.INTEGER_ONE, null, null));
        //待审核数量
        demandOrderStatisticsResponseModel.setWaitAuditCount(tShippingOrderMapper.selectWaitAuditCount());

        return demandOrderStatisticsResponseModel;
    }

    /**
     * 根据需求单id查询信息
     *
     * @param requestModel
     * @return
     */
    public List<SearchDemandOrderInfoResponseModel> searchDemandOrderInfo(SearchDemandOrderInfoRequestModel requestModel) {
        if (ListUtils.isEmpty(requestModel.getDemandOrderIds())){
            return new ArrayList<>();
        }
        List<SearchDemandOrderInfoResponseModel> responseList = tDemandOrderMapper.searchDemandOrderInfo(requestModel);
        if (ListUtils.isNotEmpty(responseList)) {
            responseList.forEach(model -> {
                model.setGoodsUnitLabel(GoodsUnitEnum.getEnum(model.getGoodsUnit()).getUnit());
                //转换需求单状态
                if (CommonConstant.INTEGER_ONE.equals(model.getIfCancel())) {
                    model.setStatus(DemandOrderStatusEnum.CANCEL_DISPATCH.getKey());
                    model.setStatusDesc(DemandOrderStatusEnum.CANCEL_DISPATCH.getValue());
                } else if (CommonConstant.INTEGER_ONE.equals(model.getIfEmpty())) {
                    model.setStatus(DemandOrderStatusEnum.ORDER_EMPTY.getKey());
                    model.setStatusDesc(DemandOrderStatusEnum.ORDER_EMPTY.getValue());
                } else if (CommonConstant.INTEGER_ONE.equals(model.getIfRollback())) {//已回退
                    model.setStatus(DemandOrderStatusEnum.ROLLBACK.getKey());
                    model.setStatusDesc(DemandOrderStatusEnum.ROLLBACK.getValue());
                } else {
                    model.setStatusDesc(DemandOrderStatusEnum.getEnum(model.getStatus()).getValue());
                }
            });
        }
        return responseList;
    }


    /**
     * 确认关联需求单信息
     * @param reqModel
     * @return
     */
    @Transactional
    public CommitExtDemandOrderRespModel commitExtDemandOrder(CommitExtDemandOrderReqModel reqModel){

        // 校验是否能多提
        VerifyEnablePickUpMoreReqModel verifyEnablePickUpMoreReqModel = new VerifyEnablePickUpMoreReqModel();
        verifyEnablePickUpMoreReqModel.setCarrierOrderId(reqModel.getCarrierOrderId());
        carrierOrderForLeYiBiz.verifyEnablePickUpMore(verifyEnablePickUpMoreReqModel);


        // 组装参数-调用生成需求单接口 单号是原单的补
        Date now = new Date();
        TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(reqModel.getCarrierOrderId());
        if (tCarrierOrder == null || IfValidEnum.INVALID.getKey().equals(tCarrierOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //查询该需求单下已经有多少补的单了
        int count = tCarrierOrderMapper.selectExtCarrierOrderCountByDemandOrderCode(tCarrierOrder.getDemandOrderCode());

        String demandOrderCode = tCarrierOrder.getDemandOrderCode() + "(补"+count+1+")";
        // 调用生成需求单的接口
        //生成物流需求单

        TDemandOrder tDemandOrder = tDemandOrderMapper.selectByPrimaryKeyDecrypt(tCarrierOrder.getDemandOrderId());
        if (tDemandOrder == null || IfValidEnum.INVALID.getKey().equals(tDemandOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.DEMAND_ORDER_NOT_EXIST);
        }

        Map<String, String> configMap = sysConfigBiz.getSysConfig(ConfigKeyEnum.ConfigGroupCodeEnum.EXT_RECYCLE_DEMAND_ORDER.getCode());
        if (MapUtils.isEmpty(configMap)) {
            throw new BizException(CarrierDataExceptionEnum.EXT_CARRIER_ORDER_CONFIG_ERROR);
        }
        String recycleCompany = configMap.get(ConfigKeyEnum.EXT_DEMAND_ORDER_COMPANY.getValue());


        BatchQueryCustomerInfoRequest batchQueryCustomerInfoRequest = new BatchQueryCustomerInfoRequest();
        batchQueryCustomerInfoRequest.setCompanyNames(Lists.newArrayList(recycleCompany));

        Result<List<BatchQueryCustomerInfoResponse>> batchQueryCustomerInfo = leyiCommonAddressServiceApi.batchQueryCustomerInfo(batchQueryCustomerInfoRequest);
        batchQueryCustomerInfo.throwException();

        Result<GetCheckAddressCodeResponse>  addressCodeByName = customerInOrderServiceApi.getCheckAddressCode();
        addressCodeByName.throwException();


        SyncTrayDemandOrderMessage syncTrayDemandOrderMessage = new SyncTrayDemandOrderMessage();
        syncTrayDemandOrderMessage.setDemandOrderCode(demandOrderCode);
        syncTrayDemandOrderMessage.setPublishTime(new Date());

        syncTrayDemandOrderMessage.setGoodsAmount(reqModel.getLoadAmount());
        syncTrayDemandOrderMessage.setEntrustType(tDemandOrder.getEntrustType());
        syncTrayDemandOrderMessage.setRemark(reqModel.getRemark());

        syncTrayDemandOrderMessage.setPublishName(BaseContextHandler.getUserName());
        syncTrayDemandOrderMessage.setPublishOrgCode(tDemandOrder.getPublishOrgCode());
        syncTrayDemandOrderMessage.setPublishOrgName(tDemandOrder.getPublishOrgName());


        syncTrayDemandOrderMessage.setLogisticsDemandSponsorMobilePhone(tDemandOrder.getPublishMobile());
        syncTrayDemandOrderMessage.setGoodsUnit(tDemandOrder.getGoodsUnit());
        syncTrayDemandOrderMessage.setIfUrgent(tDemandOrder.getIfUrgent());
        syncTrayDemandOrderMessage.setAvailableOnWeekends(tDemandOrder.getAvailableOnWeekends().byteValue());
        syncTrayDemandOrderMessage.setLoadingUnloadingPart(tDemandOrder.getLoadingUnloadingPart().byteValue());
        syncTrayDemandOrderMessage.setLoadingUnloadingCharge(tDemandOrder.getLoadingUnloadingCharge());
        syncTrayDemandOrderMessage.setRecycleTaskType(tDemandOrder.getRecycleTaskType());
        syncTrayDemandOrderMessage.setGroundPushTaskCode(tDemandOrder.getGroundPushTaskCode());
        syncTrayDemandOrderMessage.setProjectLabel(tDemandOrder.getProjectLabel());

        syncTrayDemandOrderMessage.setUpstreamCustomer(recycleCompany);

        syncTrayDemandOrderMessage.setFixedDemand(tDemandOrder.getFixedDemand());
        // List<DemandOrderOrderRelResponseModel> demandOrderOrders = tDemandOrderOrderRelMapper.getDemandOrderOrders(tDemandOrder.getId());
//        if (ListUtils.isNotEmpty(demandOrderOrders)){
//            List<SyncTrayDemandOrderOrdersModel> ordersModels = new ArrayList<>();
//            demandOrderOrders.forEach(e->{
//                SyncTrayDemandOrderOrdersModel syncTrayDemandOrderOrdersModel = new SyncTrayDemandOrderOrdersModel();
//                syncTrayDemandOrderOrdersModel.setOrderCode(e.getOrderCode());
//                syncTrayDemandOrderOrdersModel.setRelType(e.getRelType());
//                syncTrayDemandOrderOrdersModel.setRemark(e.getRemark());
//                syncTrayDemandOrderOrdersModel.setTotalAmount(reqModel.getLoadAmount());
//                ordersModels.add(syncTrayDemandOrderOrdersModel);
//            });
//            syncTrayDemandOrderMessage.setOrdersModels(ordersModels);
//        }
        // 处理地址
        String loadAddress = reqModel.getLoadAddress();
        TDemandOrderAddress oldDemandOrderAddress = tDemandOrderAddressMapper.getByDemandOrderId(tDemandOrder.getId());

        syncTrayDemandOrderMessage.setLoadYeloAddressCode(addressCodeByName.getData().getAddressCode());
//        syncTrayDemandOrderMessage.setLoadWarehouseCode();  //云盘没有
        syncTrayDemandOrderMessage.setLoadWarehouse(oldDemandOrderAddress.getLoadWarehouse());

        //通过经纬度 获取省市区
        try {
            GetMapByLonLatReqFeignModel reqFeignModel = new GetMapByLonLatReqFeignModel();
            reqFeignModel.setLatitude(reqModel.getLatitude());
            reqFeignModel.setLongitude(reqModel.getLongitude());
            Result<GetMapByLonLatRespFeignModel> mapByLonLat = mapServiceApi.getMapByLonLat(reqFeignModel);
            mapByLonLat.throwException();
            if (mapByLonLat.getData() == null
                    || mapByLonLat.getData().getAreaId() == null
                    || mapByLonLat.getData().getCityId() == null
                    | mapByLonLat.getData().getProvinceId() == null) {
                throw new BizException(CarrierDataExceptionEnum.RECEIVER_ADDRESS_EMPTY);
            }
            syncTrayDemandOrderMessage.setLoadProvinceId(mapByLonLat.getData().getProvinceId());
            syncTrayDemandOrderMessage.setLoadProvinceName(mapByLonLat.getData().getProvinceName());
            syncTrayDemandOrderMessage.setLoadCityId(mapByLonLat.getData().getCityId());
            syncTrayDemandOrderMessage.setLoadCityName(mapByLonLat.getData().getCityName());
            syncTrayDemandOrderMessage.setLoadAreaId(mapByLonLat.getData().getAreaId());
            syncTrayDemandOrderMessage.setLoadAreaName(mapByLonLat.getData().getAreaName());
            syncTrayDemandOrderMessage.setLoadDetailAddress(reqModel.getLoadAddress());
        } catch (Exception e) {
            // 报错就取原值
            syncTrayDemandOrderMessage.setLoadProvinceId(oldDemandOrderAddress.getLoadProvinceId());
            syncTrayDemandOrderMessage.setLoadProvinceName(oldDemandOrderAddress.getLoadProvinceName());
            syncTrayDemandOrderMessage.setLoadCityId(oldDemandOrderAddress.getLoadCityId());
            syncTrayDemandOrderMessage.setLoadCityName(oldDemandOrderAddress.getLoadCityName());
            syncTrayDemandOrderMessage.setLoadAreaId(oldDemandOrderAddress.getLoadAreaId());
            syncTrayDemandOrderMessage.setLoadAreaName(oldDemandOrderAddress.getLoadAreaName());
            syncTrayDemandOrderMessage.setLoadDetailAddress(oldDemandOrderAddress.getLoadDetailAddress());
        }
        syncTrayDemandOrderMessage.setExpectedLoadTime(now);
        syncTrayDemandOrderMessage.setConsignorMobile(reqModel.getConsignorMobile());
        syncTrayDemandOrderMessage.setConsignorName(reqModel.getConsignorName());
        syncTrayDemandOrderMessage.setReceiveCustomerCompanyName(oldDemandOrderAddress.getUnloadCompany());
        syncTrayDemandOrderMessage.setUnloadProvinceId(oldDemandOrderAddress.getUnloadProvinceId());
        syncTrayDemandOrderMessage.setUnloadProvinceName(oldDemandOrderAddress.getUnloadProvinceName());
        syncTrayDemandOrderMessage.setUnloadCityId(oldDemandOrderAddress.getUnloadCityId());
        syncTrayDemandOrderMessage.setUnloadCityName(oldDemandOrderAddress.getUnloadCityName());
        syncTrayDemandOrderMessage.setUnloadAreaId(oldDemandOrderAddress.getUnloadAreaId());
        syncTrayDemandOrderMessage.setUnloadAreaName(oldDemandOrderAddress.getUnloadAreaName());
        syncTrayDemandOrderMessage.setUnloadDetailAddress(oldDemandOrderAddress.getUnloadDetailAddress());
        syncTrayDemandOrderMessage.setUnloadWarehouse(oldDemandOrderAddress.getUnloadWarehouse());
        syncTrayDemandOrderMessage.setExpectedUnloadTime(now);
        syncTrayDemandOrderMessage.setReceiverName(oldDemandOrderAddress.getReceiverName());
        syncTrayDemandOrderMessage.setReceiverMobile(oldDemandOrderAddress.getReceiverMobile());
        // 拼接需求单货物
        List<DemandOrderGoodsResponseModel> tDemandOrderGoodsByDemandId = tDemandOrderGoodsMapper.getTDemandOrderGoodsByDemandId(tDemandOrder.getId());
        if (ListUtils.isNotEmpty(tDemandOrderGoodsByDemandId)){
            List<SyncTrayDemandOrderGoodsModel> goodsModels = new ArrayList<>();
            tDemandOrderGoodsByDemandId.forEach(e->{
                SyncTrayDemandOrderGoodsModel syncTrayDemandOrderGoodsModel = new SyncTrayDemandOrderGoodsModel();
                syncTrayDemandOrderGoodsModel.setGoodsAmount(reqModel.getLoadAmount());
                syncTrayDemandOrderGoodsModel.setProductTypeCode(e.getSkuCode());
                syncTrayDemandOrderGoodsModel.setGoodsName(e.getGoodsName());
                syncTrayDemandOrderGoodsModel.setCategoryName(e.getCategoryName());
                syncTrayDemandOrderGoodsModel.setHeight(e.getHeight());
                syncTrayDemandOrderGoodsModel.setLength(e.getLength());
                syncTrayDemandOrderGoodsModel.setWidth(e.getWidth());
                goodsModels.add(syncTrayDemandOrderGoodsModel);
            });
            syncTrayDemandOrderMessage.setGoodsModels(goodsModels);
        }
        List<SyncSignDemandOrderListToGroundPushModel> demandOrderCustomerRelMapperByDemandOrderIds = tDemandOrderCustomerRelMapper.getByDemandOrderIds(tDemandOrder.getId().toString());
        if (ListUtils.isNotEmpty(demandOrderCustomerRelMapperByDemandOrderIds)){
            SyncSignDemandOrderListToGroundPushModel syncSignDemandOrderListToGroundPushModel = demandOrderCustomerRelMapperByDemandOrderIds.get(0);
            syncTrayDemandOrderMessage.setTrayCustomerCompanyId(syncSignDemandOrderListToGroundPushModel.getTrayCustomerCompanyId());

            syncTrayDemandOrderMessage.setTrayCustomerAddressId(addressCodeByName.getData().getId());

            syncTrayDemandOrderMessage.setProductCategoryId(syncSignDemandOrderListToGroundPushModel.getProductCategoryId());

        }
        log.info("生成补的HR开始：" + syncTrayDemandOrderMessage);
        //生成需求单
        syncTrayDemandOrderMessage.setIfExtDemandOrder(1);
        syncTrayDemandOrderMessage.setOrgCarrierOrderId(tCarrierOrder.getId());
        syncTrayDemandOrderMessage.setOrgCarrierOrderCode(tCarrierOrder.getCarrierOrderCode());
        syncTrayDemandOrderMessage.setOrgDemandOrderId(tDemandOrder.getId());
        syncTrayDemandOrderMessage.setOrgDemandOrderCode(tDemandOrder.getCustomerOrderCode());

        //请求云盘校验地址
        LogisticsAddOrUpdateActualAddressRequest logisticsAddOrUpdateActualAddressRequest = new LogisticsAddOrUpdateActualAddressRequest();
        logisticsAddOrUpdateActualAddressRequest.setDemandCode(tDemandOrder.getDemandOrderCode());
        logisticsAddOrUpdateActualAddressRequest.setWarehouseName(oldDemandOrderAddress.getLoadWarehouse());
        logisticsAddOrUpdateActualAddressRequest.setProvinceId(syncTrayDemandOrderMessage.getLoadProvinceId());
        logisticsAddOrUpdateActualAddressRequest.setProvinceName(syncTrayDemandOrderMessage.getLoadProvinceName());
        logisticsAddOrUpdateActualAddressRequest.setCityId(syncTrayDemandOrderMessage.getLoadCityId());
        logisticsAddOrUpdateActualAddressRequest.setCityName(syncTrayDemandOrderMessage.getLoadCityName());
        logisticsAddOrUpdateActualAddressRequest.setAreaId(syncTrayDemandOrderMessage.getLoadAreaId());
        logisticsAddOrUpdateActualAddressRequest.setAreaName(syncTrayDemandOrderMessage.getLoadAreaName());
        logisticsAddOrUpdateActualAddressRequest.setDetailAddress(syncTrayDemandOrderMessage.getLoadDetailAddress());
        logisticsAddOrUpdateActualAddressRequest.setContactName(reqModel.getConsignorName());
        logisticsAddOrUpdateActualAddressRequest.setContactMobile(reqModel.getConsignorMobile());
        logisticsAddOrUpdateActualAddressRequest.setAvailableOnWeekends(1);
        logisticsAddOrUpdateActualAddressRequest.setLoadingUnloadingPartingPart(2);
        logisticsAddOrUpdateActualAddressRequest.setLongitude(reqModel.getLongitude());
        logisticsAddOrUpdateActualAddressRequest.setLatitude(reqModel.getLatitude());
        logisticsAddOrUpdateActualAddressRequest.setCreatedBy(BaseContextHandler.getUserName());
        Result<Boolean> createAddressResult = customerInOrderServiceApi.logisticsAddOrUpdateActualAddress(logisticsAddOrUpdateActualAddressRequest);
        createAddressResult.throwException();
        this.saveSyncTrayStockPlanDemandOrderToTms(syncTrayDemandOrderMessage);
        log.info("生成补的HR结束");

        TDemandOrder extDemandOrder = tDemandOrderMapper.getByCode(demandOrderCode);

        SyncSupplementDemandRequest syncSupplementDemandRequest = new SyncSupplementDemandRequest();
        syncSupplementDemandRequest.setCarrierOrderCode(tCarrierOrder.getCarrierOrderCode());
        syncSupplementDemandRequest.setDemandOrderCode(tDemandOrder.getDemandOrderCode());
        syncSupplementDemandRequest.setExtDemandOrderCode(extDemandOrder.getDemandOrderCode());
        syncSupplementDemandRequest.setActualDeliveryAddressName(oldDemandOrderAddress.getLoadWarehouse());
        syncSupplementDemandRequest.setFromProvinceId(syncTrayDemandOrderMessage.getLoadProvinceId());
        syncSupplementDemandRequest.setActualFromProvinceName(syncTrayDemandOrderMessage.getLoadProvinceName());
        syncSupplementDemandRequest.setFromCityId(syncTrayDemandOrderMessage.getLoadCityId());
        syncSupplementDemandRequest.setActualFromCityName(syncTrayDemandOrderMessage.getLoadCityName());
        syncSupplementDemandRequest.setFromAreaId(syncTrayDemandOrderMessage.getLoadAreaId());
        syncSupplementDemandRequest.setActualFromAreaName(syncTrayDemandOrderMessage.getLoadAreaName());
        syncSupplementDemandRequest.setActualDeliveryAddressDetail(syncTrayDemandOrderMessage.getLoadDetailAddress());
        syncSupplementDemandRequest.setDeliveryContactMobile(syncTrayDemandOrderMessage.getConsignorMobile());
        syncSupplementDemandRequest.setDeliveryContactName(syncTrayDemandOrderMessage.getConsignorName());
        syncSupplementDemandRequest.setCount(Integer.parseInt(reqModel.getLoadAmount().toString()));
        syncSupplementDemandRequest.setExpectPickGoodsTime(now);
        syncSupplementDemandRequest.setExpectArrivalGoodsTime(now);
        syncSupplementDemandRequest.setAvailableOnWeekends(Integer.valueOf(1).byteValue());
        syncSupplementDemandRequest.setLoadingUnloadingPart(Integer.valueOf(2).byteValue());
        syncSupplementDemandRequest.setLoadingUnloadingCharge(new BigDecimal("0.00"));
        syncSupplementDemandRequest.setTransportType(1);
        syncSupplementDemandRequest.setUpCustomerName(reqModel.getUpstreamCompanyName());
        syncSupplementDemandRequest.setPurchaseCode(reqModel.getPurchaseOrderCode());
        syncSupplementDemandRequest.setUpContactName(reqModel.getUpstreamContactPerson());
        syncSupplementDemandRequest.setUpContactMobile(reqModel.getUpstreamContactPhone());
        Result<Boolean> syncSupplementDemand = customerInOrderServiceApi.syncSupplementDemand(syncSupplementDemandRequest);
        syncSupplementDemand.throwException();
        // 进行发布 查看原需求单的模式 是否是我司还是其他车主，补单需要一致
        BatchPublishRequestModel requestModel = new BatchPublishRequestModel();
        List<BatchPublishDemandModel> demandDtoList = new ArrayList<>();
        BatchPublishDemandModel batchPublishDemandModel = new BatchPublishDemandModel();
        batchPublishDemandModel.setDemandOrderId(extDemandOrder.getId());
//        batchPublishDemandModel.setReceiverMobile();
//        batchPublishDemandModel.setReceiverName();
        batchPublishDemandModel.setUnloadProvinceId(oldDemandOrderAddress.getUnloadProvinceId());
        batchPublishDemandModel.setUnloadProvinceName(oldDemandOrderAddress.getUnloadProvinceName());
        batchPublishDemandModel.setUnloadCityId(oldDemandOrderAddress.getUnloadCityId());
        batchPublishDemandModel.setUnloadCityName(oldDemandOrderAddress.getUnloadCityName());
        batchPublishDemandModel.setUnloadAreaId(oldDemandOrderAddress.getUnloadAreaId());
        batchPublishDemandModel.setUnloadAreaName(oldDemandOrderAddress.getUnloadAreaName());
        batchPublishDemandModel.setUnloadDetailAddress(oldDemandOrderAddress.getUnloadDetailAddress());
        batchPublishDemandModel.setUnloadWarehouse(oldDemandOrderAddress.getUnloadWarehouse());
//        batchPublishDemandModel.setWarehouseId();
        requestModel.setOrderMode(1);
        String companyCarrierName = commonBiz.getQiyaCompanyName();
        if (tDemandOrder.getCompanyCarrierName().equalsIgnoreCase(companyCarrierName)){
            requestModel.setIsOurCompany(1);
        }else{
            // 非我司的车主
            requestModel.setIsOurCompany(2);
            requestModel.setCompanyCarrierId(tDemandOrder.getCompanyCarrierId());

        }
        demandDtoList.add(batchPublishDemandModel);
        requestModel.setDemandDtoList(demandDtoList);
        this.confirmPublish(requestModel);
        CommitExtDemandOrderRespModel commitExtDemandOrderRespModel = new CommitExtDemandOrderRespModel();
        commitExtDemandOrderRespModel.setDemandOrderId(extDemandOrder.getId());

        //发送mq
        CommonDelayMsg delayMsg = new CommonDelayMsg();
        delayMsg.setTime(CommonConstant.LONG_TEN);
        delayMsg.setTimeUnit(TimeUnit.MINUTES);
        delayMsg.setDelayMsgType(DelayQueueBizTypeEnum.DEAL_REPLENISH.name());
        DealReplenishOrderMessage dealReplenishOrderMessage = new DealReplenishOrderMessage();
        dealReplenishOrderMessage.setReplenishDemandCode(extDemandOrder.getDemandOrderCode());
        delayMsg.setObject(dealReplenishOrderMessage);
        rabbitMqPublishBiz.dealReplenishOrder(delayMsg);
        return commitExtDemandOrderRespModel;
    }

    @Transactional(rollbackFor = Exception.class)
    public void dealReplenishOrder(DealReplenishOrderMessage dealReplenishOrderMessage) {
        TDemandOrder targetDemand = tDemandOrderMapper.getByCode(dealReplenishOrderMessage.getReplenishDemandCode());
        if (null == targetDemand || ValidTypeEnum.INVALID.getKey().equals(targetDemand.getValid())) {
            return;
        }
        if (targetDemand.getStatus() >= DemandOrderStatusEnum.PART_DISPATCH.getKey()) {
            return;
        }
        TDemandOrder dealDemand = new TDemandOrder();
        dealDemand.setId(targetDemand.getId());
        dealDemand.setValid(ValidTypeEnum.INVALID.getKey());

        List<TExtDemandOrderRelation> extelationList = tExtDemandOrderRelationMapper.selectByExtCode(dealDemand.getDemandOrderCode());

        TExtDemandOrderRelation dealExtRelation;
        List<TExtDemandOrderRelation> dealExtRelationList = new ArrayList<>();
        for (TExtDemandOrderRelation extRelation : extelationList) {
            dealExtRelation = new TExtDemandOrderRelation();
            dealExtRelation.setId(extRelation.getId());
            dealExtRelation.setValid(ValidTypeEnum.INVALID.getKey());
            dealExtRelationList.add(dealExtRelation);
        }
        // 通知云盘直接取消
        RollbackDemandRequestModel rollbackDemandRequestModel = new RollbackDemandRequestModel();
        rollbackDemandRequestModel.setLogisticsDemandCode(targetDemand.getDemandOrderCode());
        rollbackDemandRequestModel.setRollbackCauseType(DemandOrderCancelTypeEnum.LOGISTICS.getKey());
        rollbackDemandRequestModel.setIfTmsCancel(CommonConstant.INTEGER_ONE);
        Result<Boolean> result = customerInOrderServiceApi.rollBackDemandOrder(rollbackDemandRequestModel);
        result.throwException();

        tDemandOrderMapper.updateByPrimaryKeySelectiveEncrypt(dealDemand);
        if (ListUtils.isNotEmpty(dealExtRelationList)) {
            tExtDemandOrderRelationMapper.batchUpdateSelective(dealExtRelationList);
        }
    }
}
