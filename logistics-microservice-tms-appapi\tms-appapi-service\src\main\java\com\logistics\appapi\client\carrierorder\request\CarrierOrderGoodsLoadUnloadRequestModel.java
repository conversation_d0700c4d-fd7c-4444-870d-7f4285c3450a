package com.logistics.appapi.client.carrierorder.request;

import com.logistics.appapi.controller.carrierorder.request.LoadGoodsForYeloLifeRequestCodeDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/3/13 9:44
 */
@Data
public class CarrierOrderGoodsLoadUnloadRequestModel {
    @ApiModelProperty("货物id")
    private Long goodsId;
    @ApiModelProperty("数量")
    private BigDecimal count;

    @ApiModelProperty(value = "货物的编码集合 v2.44", required = true)
    private List<LoadGoodsForYeloLifeRequestCodeModel> codeDtoList;
}
