package com.logistics.management.webapi.api.feign.dispatchorder.dto;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DispatchOrderSearchRequestDto extends AbstractPageForm<DispatchOrderSearchRequestDto> {
    @ApiModelProperty("调度单号")
    private String dispatchOrderCode;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机")
    private String driverName;
    @ApiModelProperty("调度人")
    private String dispatchUserName;
    @ApiModelProperty("调度时间开始")
    private String dispatchTimeStart;
    @ApiModelProperty("调度时间结束")
    private String dispatchTimeEnd;
    @ApiModelProperty("需求单创建时间开始")
    private String demandOrderTimeFrom;
    @ApiModelProperty("需求单创建时间结束")
    private String demandOrderTimeTo;
}
