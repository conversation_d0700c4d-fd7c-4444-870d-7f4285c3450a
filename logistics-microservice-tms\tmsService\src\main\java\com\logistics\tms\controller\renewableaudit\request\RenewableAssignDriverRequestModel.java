package com.logistics.tms.controller.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class RenewableAssignDriverRequestModel {

    @ApiModelProperty(value = "新生订单审核列表id" ,required = true)
    private List<Long> renewableOrderIds;

    @ApiModelProperty(value = "车辆id" ,required = true)
    private Long vehicleId;

    @ApiModelProperty(value = "司机id" ,required = true)
    private Long staffId;

    @ApiModelProperty(value = "备注")
    private String remark;
}
