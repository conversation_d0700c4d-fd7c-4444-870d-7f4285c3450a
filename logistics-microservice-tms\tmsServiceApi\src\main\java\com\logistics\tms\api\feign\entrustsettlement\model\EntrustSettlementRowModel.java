package com.logistics.tms.api.feign.entrustsettlement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/11 18:58
 */
@Data
public class EntrustSettlementRowModel {
    @ApiModelProperty("需求单ID")
    private Long demandOrderId;
    @ApiModelProperty("结算ID")
    private Long settlementId;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("结算状态 0 未收款 1 已收款")
    private Integer settlementStatus;
    @ApiModelProperty("委托单状态 1000：待调度 2000部分调度 3000调度完成 4000 待签收 5000 已签收 1已取消")
    private Integer entrustStatus;
    private Integer ifEmpty;//是否放空：0 否，1 是
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("结算数据")
    private BigDecimal settlementAmount;
    @ApiModelProperty("报价类型 1 单价 2整车价")
    private Integer contractPriceType;
    @ApiModelProperty("结算费用")
    private BigDecimal settlementCostTotal;
    @ApiModelProperty("发货地址")
    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    @ApiModelProperty("发货人")
    private String consignorName;
    private String consignorMobile;
    @ApiModelProperty("收货地址详细")
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;
    @ApiModelProperty("收货人")
    private String receiverName;
    private String receiverMobile;
    @ApiModelProperty("货物单位 1 件 2 吨 3 方")
    private Integer goodsUnit;
    @ApiModelProperty("委托方")
    private String companyEntrust;
    private Integer entrustType;
    @ApiModelProperty("下单时间")
    private Date publishTime;
    @ApiModelProperty("结算时间")
    private Date settlementTime;

    @ApiModelProperty("规格/品名")
    private List<EntrustSettlementGoodsResponseModel> goodsList;

    @ApiModelProperty("委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件")
    private Integer demandOrderSource;
}
