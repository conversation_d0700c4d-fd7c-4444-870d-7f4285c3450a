package com.logistics.tms.biz.drivercostapply;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Maps;
import com.logistics.tms.api.feign.driverpayee.model.ViewLogResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.ExceptionUtils;
import com.logistics.tms.base.utils.LocalStringUtil;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.drivercostapply.model.SelectByCodeAndNumModel;
import com.logistics.tms.biz.drivercostdeduction.DriverCostDeductionBiz;
import com.logistics.tms.biz.drivercostdeduction.model.DriverCostDeductionQueryModel;
import com.logistics.tms.biz.reserveapply.ReserveApplyBiz;
import com.logistics.tms.biz.reservebalance.ReserveBalanceBiz;
import com.logistics.tms.biz.reservebalance.model.ReserveApplyBalanceChangeBoModel;
import com.logistics.tms.biz.reservebalance.model.ReserveBalanceChangeHandleBoModel;
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel;
import com.logistics.tms.controller.drivercostapply.request.*;
import com.logistics.tms.controller.drivercostapply.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/5
 */
@Service
public class DriverCostApplyBiz {

	@Autowired
	private TDriverCostApplyMapper tDriverCostApplyMapper;
	@Autowired
	private TStaffBasicMapper tStaffBasicMapper;
	@Autowired
	private TVehicleBasicMapper tVehicleBasicMapper;
	@Autowired
	private TVehicleOilCardMapper tVehicleOilCardMapper;
	@Autowired
	private TCertificationPicturesMapper tCertificationPicturesMapper;
	@Resource
	private TDriverCostApplyInvoiceMapper tDriverCostApplyInvoiceMapper;
	@Autowired
	private CommonBiz commonBiz;
	@Autowired
	private DriverCostDeductionBiz driverCostDeductionBiz;
	@Autowired
	private ReserveApplyBiz reserveApplyBiz;
	@Resource
	private ReserveBalanceBiz reserveBalanceBiz;
	@Resource
	private TOperateLogsMapper tOperateLogsMapper;

	/**
	 * 费用申请列表
	 *
	 * @param requestModel 筛选条件
	 * @return 费用申请列表
	 */
	public PageInfo<SearchCostApplyListResponseModel> searchCostApplyList(SearchCostApplyListRequestModel requestModel) {

		List<TDriverCostDeduction> costDeductions = Collections.emptyList();
		// 根据备用金单号模糊匹配
		if (StringUtils.isNotBlank(requestModel.getReserveCode())) {
			costDeductions = getCostDeductions(new DriverCostDeductionQueryModel()
					.setReserveApplyCode(requestModel.getReserveCode()));
			if (ListUtils.isEmpty(costDeductions)) {
				return new PageInfo<>(Collections.emptyList());
			}
			List<Long> costApplyIds = costDeductions.stream()
					.map(TDriverCostDeduction::getDriverCostApplyId)
					.distinct()
					.collect(Collectors.toList());
			requestModel.setDriverCostApplyIds(costApplyIds);
		}

		//分页查询司机费用申请列表
		requestModel.setSource(CommonConstant.INTEGER_ONE);
		requestModel.enablePaging();
		List<SearchCostApplyListResponseModel> driverCostApplyList = tDriverCostApplyMapper.searchCostApplyList(requestModel);

		// 查询费用冲销
		if (ListUtils.isEmpty(costDeductions)) {
			List<Long> costApplyIds = driverCostApplyList.stream()
					.map(SearchCostApplyListResponseModel::getDriverCostApplyId)
					.collect(Collectors.toList());
			costDeductions = getCostDeductions(new DriverCostDeductionQueryModel()
					.setDriverCostApplyIds(costApplyIds));
		}

		paddingCostDeduction(driverCostApplyList, costDeductions);
		return new PageInfo<>(driverCostApplyList);
	}

	// 填充费用冲销列表
	private void paddingCostDeduction(List<SearchCostApplyListResponseModel> driverCostApplyList, List<TDriverCostDeduction> costDeductions) {
		if (ListUtils.isEmpty(driverCostApplyList) || ListUtils.isEmpty(costDeductions)) {
			return;
		}
		Map<Long, BigDecimal> advanceCosts = Maps.newHashMap();
		ArrayListMultimap<Long, String> costDeductionMap = ArrayListMultimap.create();
		costDeductions.forEach(f -> {
			costDeductionMap.put(f.getDriverCostApplyId(), f.getReserveApplyCode());
			if (ReserveApplyTypeEnum.ADVANCE_TYPE.getKey().equals(f.getReserveType())) {
				advanceCosts.merge(f.getDriverCostApplyId(), f.getVerificationAmount(), BigDecimal::add);
			}
		});
		driverCostApplyList.forEach(f -> {
			f.setReserveCodeList(costDeductionMap.get(f.getDriverCostApplyId()));
			f.setAdvanceCosts(advanceCosts.get(f.getDriverCostApplyId()));
		});
	}

	// 查询冲销信息
	private List<TDriverCostDeduction> getCostDeductions(DriverCostDeductionQueryModel queryModel) {
		return driverCostDeductionBiz.getCostDeduction(queryModel);
	}

	/**
	 * 申请记录详情
	 *
	 * @param requestModel 司机费用申请表id
	 * @return 申请记录详情
	 */
	public DriverCostApplyDetailResponseModel driverCostApplyDetail(DriverCostApplyDetailRequestModel requestModel) {
		//小程序请求
		Long loginDriverId = null;
		if (CommonConstant.INTEGER_THREE.equals(requestModel.getSource())) {
			loginDriverId = commonBiz.getLoginDriverAppletUserId();
			if (loginDriverId == null || CommonConstant.LONG_ZERO.equals(loginDriverId)) {
				throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_ERROR);
			}
		}
		DriverCostApplyDetailResponseModel driverCostApplyDetail = tDriverCostApplyMapper.selectDriverCostApplyDetail(requestModel.getDriverCostApplyId(), loginDriverId);
		if (driverCostApplyDetail == null){
			throw new BizException(CarrierDataExceptionEnum.DRIVER_COST_APPLY_NOT_EXIST);
		}
		// 过滤前台|小程序 备用用金信息展示
		if (CommonConstant.INTEGER_ONE.equals(requestModel.getSource())|| CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
			return driverCostApplyDetail;
		}

		List<DriverCostApplyInvoiceResponseModel> tDriverCostApplyInvoice = tDriverCostApplyInvoiceMapper.selectInvoiceInfoByCostApplyId(driverCostApplyDetail.getDriverCostApplyId());
		if (ListUtils.isNotEmpty(tDriverCostApplyInvoice)) {
			driverCostApplyDetail.setInvoiceInfoList(tDriverCostApplyInvoice);
		}

		List<DriverCostDeductionResponseModel> reserveList = Collections.emptyList();
		// 仅自主司机关联备用金信息
		if (StaffPropertyEnum.OWN_STAFF.getKey().equals(driverCostApplyDetail.getStaffProperty())) {
			Integer costApplyAuditStatus = driverCostApplyDetail.getAuditStatus();
			// 待业务审核、待财务审核查询有余额的备用金列表先进先出 以打款时间正序排
			if (DriverCostAuditEnum.WAIT_BUSINESS_AUDIT.getKey().equals(costApplyAuditStatus) || DriverCostAuditEnum.WAIT_FINANCIAL_AUDIT.getKey().equals(costApplyAuditStatus)) {
				reserveList = getReserveApplyUsedInCostDeductions(driverCostApplyDetail.getStaffId(), driverCostApplyDetail.getApplyCost());
				// 扣款类型无垫付统计
				if (!DriverCostApplyTypeEnum.DEDUCTIONS.getKey().equals(driverCostApplyDetail.getCostType())) {
					BigDecimal totalBalance = reserveList.stream()
							.map(DriverCostDeductionResponseModel::getBalance)
							.reduce(BigDecimal.ZERO, BigDecimal::add);
					BigDecimal advanceAmount = driverCostApplyDetail.getApplyCost().subtract(totalBalance);
					if (advanceAmount.signum() > CommonConstant.INTEGER_ZERO) {
						driverCostApplyDetail.setAdvanceCosts(advanceAmount);
					}
				}
			}
			// 查询已冲销列表
			else if (DriverCostAuditEnum.isShowReserveApply(costApplyAuditStatus)) {
				reserveList = getDriverCostDetailDeductions(driverCostApplyDetail.getDriverCostApplyId());
			}
		}
		driverCostApplyDetail.setReserveList(reserveList);

		//查询操作日志
		List<ViewLogResponseModel> viewLogResponseModels = tOperateLogsMapper.selectLogsByCondition(OperateLogsObjectTypeEnum.DRIVER_COST_APPLY.getKey(), requestModel.getDriverCostApplyId(), null);
		driverCostApplyDetail.setRecordList(MapperUtils.mapper(viewLogResponseModels, DriverCostApplyRecordResponseModel.class));

		return driverCostApplyDetail;
	}

	// 获取用于冲销使用的备用金信息列表
	private List<DriverCostDeductionResponseModel> getReserveApplyUsedInCostDeductions(Long driverId, BigDecimal applyCost) {
		List<DriverCostDeductionResponseModel> usedInCostDeductions = new ArrayList<>();
		// 查询所有满足条件的备用金 打款时间升序
		List<TReserveApply> reserveApply = reserveApplyBiz.getReserveApplyUsedInCostDeductions(driverId);
		for (TReserveApply apply : reserveApply) {
			DriverCostDeductionResponseModel costDeduction = new DriverCostDeductionResponseModel();
			costDeduction.setBalance(apply.getBalanceAmount());
			costDeduction.setReserveCode(apply.getReserveApplyCode());
			costDeduction.setReserveType(apply.getType());
			// 计算核销金额
			if (apply.getBalanceAmount().compareTo(applyCost) >= CommonConstant.INTEGER_ZERO) {
				costDeduction.setWriteOffAmount(applyCost);
				usedInCostDeductions.add(costDeduction);
				break;
			}
			costDeduction.setWriteOffAmount(apply.getBalanceAmount());
			applyCost = applyCost.subtract(apply.getBalanceAmount());
			usedInCostDeductions.add(costDeduction);
		}

		return usedInCostDeductions;
	}

	// 获取已冲销备用金信息列表
	private List<DriverCostDeductionResponseModel> getDriverCostDetailDeductions(Long costApplyId) {
		return getCostDeductions(new DriverCostDeductionQueryModel()
				.setDriverCostApplyIds(Collections.singletonList(costApplyId)))
				.stream()
				.map(s -> {
					DriverCostDeductionResponseModel costDeduction = new DriverCostDeductionResponseModel();
					costDeduction.setBalance(s.getBalanceAmount());
					costDeduction.setReserveCode(s.getReserveApplyCode());
					costDeduction.setWriteOffAmount(s.getVerificationAmount());
					costDeduction.setReserveType(s.getReserveType());
					return costDeduction;
				})
				.collect(Collectors.toList());
	}

	/**
	 * 审核/驳回费用申请
	 *
	 * @param requestModel 司机费用申请表id
	 */
	@Transactional
	public void auditOrRejectCostApply(AuditOrRejectCostApplyRequestModel requestModel) {
		//判断费用申请记录是否存在
		TDriverCostApply dbDriverCostApply = tDriverCostApplyMapper.selectByPrimaryKeyDecrypt(requestModel.getDriverCostApplyId());
		if (dbDriverCostApply == null || dbDriverCostApply.getValid().equals(IfValidEnum.INVALID.getKey())) {
			throw new BizException(CarrierDataExceptionEnum.DRIVER_COST_APPLY_NOT_EXIST);
		}
		//判断状态
		//已撤销的不能审核
		if (DriverCostAuditEnum.REPEAL.getKey().equals(dbDriverCostApply.getAuditStatus())) {
			throw new BizException(CarrierDataExceptionEnum.DRIVER_COST_APPLY_REPEAL);
		}

		ReserveApplyAuditorTypeEnum auditorTypeEnum = ReserveApplyAuditorTypeEnum.getEnumByKey(requestModel.getAuditorType());
		//业务审核
		if (ReserveApplyAuditorTypeEnum.AUDIT_BUSINESS_TYPE.equals(auditorTypeEnum)) {
			//不是待业务审核的不能审核
			if (!DriverCostAuditEnum.WAIT_BUSINESS_AUDIT.getKey().equals(dbDriverCostApply.getAuditStatus())) {
				throw new BizException(CarrierDataExceptionEnum.DRIVER_COST_APPLY_AUDIT_STATUS_ERROR);
			}
		}
		//财务审核
		else if (ReserveApplyAuditorTypeEnum.AUDIT_FINANCIAL_TYPE.equals(auditorTypeEnum)){
			//不是财务待审核的不能审核
			if (!DriverCostAuditEnum.WAIT_FINANCIAL_AUDIT.getKey().equals(dbDriverCostApply.getAuditStatus())) {
				throw new BizException(CarrierDataExceptionEnum.DRIVER_COST_APPLY_AUDIT_STATUS_ERROR);
			}
		}else{
			throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
		}

		Integer auditStatus;
		boolean isNoReserveDeduction = true;
		String remark;
		//审核通过
		if (CommonConstant.INTEGER_ONE.equals(requestModel.getOperateType())) {
			if (StaffPropertyEnum.OWN_STAFF.getKey().equals(dbDriverCostApply.getStaffProperty())) {
				isNoReserveDeduction = false;
			}
			if (ReserveApplyAuditorTypeEnum.AUDIT_BUSINESS_TYPE.equals(auditorTypeEnum)) {
				auditStatus = DriverCostAuditEnum.WAIT_FINANCIAL_AUDIT.getKey();
			}else {
				auditStatus = DriverCostAuditEnum.AUDITED.getKey();
			}

			remark = "【通过】";
		}
		//驳回
		else {
			auditStatus = DriverCostAuditEnum.REJECT.getKey();

			remark = "【驳回】";
		}
		if (StringUtils.isNotBlank(requestModel.getRemark())){
			remark = remark + requestModel.getRemark();
		}

		//修改审核状态
		TDriverCostApply driverCostApply = new TDriverCostApply();
		driverCostApply.setId(dbDriverCostApply.getId());
		driverCostApply.setAuditStatus(auditStatus);
		driverCostApply.setAuditorName(BaseContextHandler.getUserName());
		driverCostApply.setAuditTime(new Date());
		driverCostApply.setRemark(requestModel.getRemark());
		commonBiz.setBaseEntityModify(driverCostApply, BaseContextHandler.getUserName());
		tDriverCostApplyMapper.updateByPrimaryKeySelectiveEncrypt(driverCostApply);

		//记录操作日志
		OperateLogsOperateTypeEnum operateTypeEnum;
		if (ReserveApplyAuditorTypeEnum.AUDIT_BUSINESS_TYPE.equals(auditorTypeEnum)) {
			operateTypeEnum = OperateLogsOperateTypeEnum.DRIVER_COST_APPLY_BUSINESS_AUDIT;
		}else{
			operateTypeEnum = OperateLogsOperateTypeEnum.DRIVER_COST_APPLY_FINANCIAL_AUDIT;
		}
		TOperateLogs tOperateLogs = commonBiz.addOperateLogs(dbDriverCostApply.getId(), operateTypeEnum, remark, BaseContextHandler.getUserName());
		tOperateLogsMapper.insertSelective(tOperateLogs);

		// 外部司机、业务审核-无须处理备用金即返回
		if (isNoReserveDeduction || ReserveApplyAuditorTypeEnum.AUDIT_BUSINESS_TYPE.equals(auditorTypeEnum)) {
			return;
		}

		// 只有内部司机且财务审核通过后，才扣除备用金
		// 备用金费用扣除
		BigDecimal totalWriteOffAmount = BigDecimal.ZERO;
		// 将冲销金额转为绝对值
		for (DriverCostDeductionRequestModel driverCostDeductionRequestModel : requestModel.getReserveList()) {
			BigDecimal writeOffAmount = driverCostDeductionRequestModel.getWriteOffAmount().abs();
			totalWriteOffAmount = totalWriteOffAmount.add(writeOffAmount);
			driverCostDeductionRequestModel.setWriteOffAmount(writeOffAmount);
		}

		ReserveBalanceRunningTypeEnum typeEnum = ReserveBalanceRunningTypeEnum.WRITE_OFF_TYPE;

		// 扣款类型不允许垫付
		if (DriverCostApplyTypeEnum.DEDUCTIONS.getKey().equals(dbDriverCostApply.getCostType())) {
			typeEnum = ReserveBalanceRunningTypeEnum.DEDUCTIONS_TYPE;
		}
		// 垫付
		else if (requestModel.getAdvanceCosts() != null) {
			BigDecimal advanceAmount = dbDriverCostApply.getApplyCost().subtract(totalWriteOffAmount);
			boolean isAdvanceType = advanceAmount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO;
			if (isAdvanceType) {
				// 校验垫付参数
				ExceptionUtils.isTure(!CommonConstant.INTEGER_ZERO.equals(advanceAmount.compareTo(requestModel.getAdvanceCosts())))
						.throwMessage(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
				typeEnum = ReserveBalanceRunningTypeEnum.ADVANCE_TYPE;
				TReserveApply reserveRemitApply = reserveApplyBiz.createReserveRemitApply(dbDriverCostApply.getStaffId(),
						advanceAmount,
						dbDriverCostApply.getCostApplyCode(),
						ReserveApplyTypeEnum.ADVANCE_TYPE);
				DriverCostDeductionRequestModel driverCostDeductionRequestModel = new DriverCostDeductionRequestModel();
				driverCostDeductionRequestModel.setReserveCode(reserveRemitApply.getReserveApplyCode());
				driverCostDeductionRequestModel.setReserveType(reserveRemitApply.getType());
				driverCostDeductionRequestModel.setBalance(reserveRemitApply.getBalanceAmount());
				driverCostDeductionRequestModel.setWriteOffAmount(advanceAmount);
				requestModel.getReserveList().add(driverCostDeductionRequestModel);
			}
		}
		// 入参数验
		ExceptionUtils.isTure(ListUtils.isEmpty(requestModel.getReserveList()))
				.throwMessage(CarrierDataExceptionEnum.RESERVE_BALANCE_INSUFFICIENT);
		// 记录费用申请流水
		driverCostDeductionBiz.batchAddCostDeduction(dbDriverCostApply.getId(), requestModel.getReserveList());
		// 扣除余额
		ReserveBalanceChangeHandleBoModel reserveBalanceChangeHandleBoModel = new ReserveBalanceChangeHandleBoModel()
				.setTypeEnum(typeEnum)
				.setDriverId(dbDriverCostApply.getStaffId())
				.setReserveList(MapperUtils.mapper(requestModel.getReserveList(), ReserveApplyBalanceChangeBoModel.class))
				.setAmount(dbDriverCostApply.getApplyCost());
		reserveBalanceBiz.balanceChangeHandler(reserveBalanceChangeHandleBoModel);
	}

	/**
	 * 小程序费用申请列表
	 *
	 * @param requestModel 申请时间
	 * @return 司机费用费用申请列表
	 */
	public SearchCostApplyListCountForAppletResponseModel searchCostApplyListForApplet(SearchCostApplyListForAppletRequestModel requestModel) {
		//获取当前登录的司机id
		Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
		if (loginDriverAppletUserId == null || CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
			throw new BizException(CarrierDataExceptionEnum.DATA_ERROR_AND_RE_LOGIN);
		}

		requestModel.setStaffId(loginDriverAppletUserId);
		SearchCostApplyListCountForAppletResponseModel responseMode = new SearchCostApplyListCountForAppletResponseModel();
		//分页查询司机费用申请列表
		SearchCostApplyListRequestModel queryModel = MapperUtils.mapper(requestModel, SearchCostApplyListRequestModel.class);
		queryModel.setSource(CommonConstant.INTEGER_THREE);
		requestModel.enablePaging();
		List<SearchCostApplyListResponseModel> driverCostApplyList = tDriverCostApplyMapper.searchCostApplyList(queryModel);

		//统计申请次数 批准费用等信息
		DriverCostStatisticsModel driverCostStatisticsModel = tDriverCostApplyMapper.searchCostStatisticsByApplyTime(requestModel.getApplyTime(), loginDriverAppletUserId);
		responseMode.setApplyCount(driverCostStatisticsModel.getApplyCount());
		responseMode.setApprovalCount(driverCostStatisticsModel.getApprovalCount());
		responseMode.setApprovalFee(driverCostStatisticsModel.getApprovalFee());
		responseMode.setPageList(new PageInfo<>(driverCostApplyList));
		return responseMode;
	}

	/**
	 * 小程序提交（重新提交）费用申请
	 *
	 * @param requestModel 费用申请信息
	 */
	@Transactional
	public void addDriverCostApply(AddDriverCostApplyRequestModel requestModel) {
		//获取当前登录的司机id
		Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
		if (loginDriverAppletUserId == null || CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
			throw new BizException(CarrierDataExceptionEnum.DATA_ERROR_AND_RE_LOGIN);
		}

		//查询司机基础信息
		TStaffBasic tStaffBasic = tStaffBasicMapper.selectByPrimaryKey(loginDriverAppletUserId);
		if (tStaffBasic == null || IfValidEnum.INVALID.getKey().equals(tStaffBasic.getValid())) {
			throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
		}
		//查询车辆基本信息
		VehicleBasicPropertyModel vehicleBasicPropertyById = tVehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getVehicleId());
		if (vehicleBasicPropertyById == null) {
			throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
		}

		//内部司机校验
		if (!StaffPropertyEnum.OWN_STAFF.getKey().equals(tStaffBasic.getStaffProperty()) &&
				!StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(tStaffBasic.getStaffProperty())) {
			throw new BizException(CarrierDataExceptionEnum.ONLY_INTERIOR_STAFF);
		}

		//内部车辆校验
		if (!VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehicleBasicPropertyById.getVehicleProperty()) &&
				!VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehicleBasicPropertyById.getVehicleProperty())) {
			throw new BizException(CarrierDataExceptionEnum.INTERNAL_VEHICLE_ERROR);
		}

		//运营中的车辆
		if (!OperatingStateEnum.IN_OPERATION.getKey().equals(vehicleBasicPropertyById.getOperatingState())) {
			throw new BizException(CarrierDataExceptionEnum.VEHICLES_ARE_SCRAP);
		}

		//如果是加油费用
		TVehicleOilCard vehicleOilCard = null;
		if (DriverCostApplyTypeEnum.OIL_FEE.getKey().equals(requestModel.getCostType())) {
			//查询车辆油卡信息
			vehicleOilCard = tVehicleOilCardMapper.getByCardNoOrVehicleId(null, requestModel.getVehicleId());
			if (vehicleOilCard == null || IfValidEnum.INVALID.getKey().equals(vehicleOilCard.getValid()) || !CommonConstant.INTEGER_ONE.equals(vehicleOilCard.getStatus())) {
				throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_IS_NULL);
			}
		}

		//新增
		TDriverCostApply tDriverCostApply;
		if (requestModel.getDriverCostApplyId() == null || CommonConstant.LONG_ZERO.equals(requestModel.getDriverCostApplyId())) {

			// 新增 发票代码+发票号码不能重复
			if (ListUtils.isNotEmpty(requestModel.getInvoiceInfoList())) {
				List<SelectByCodeAndNumModel> selectByCodeAndNumModels = new ArrayList<>();
				List<DriverCostApplyInvoiceRequestModel> invoiceInfoList = requestModel.getInvoiceInfoList();
				for (DriverCostApplyInvoiceRequestModel invoiceInfo : invoiceInfoList) {
					selectByCodeAndNumModels.add(new SelectByCodeAndNumModel().setInvoiceCode(invoiceInfo.getInvoiceCode()).setInvoiceNum(invoiceInfo.getInvoiceNum()).setType(invoiceInfo.getType()));
				}
				List<TDriverCostApplyInvoice> checkInvoice = tDriverCostApplyInvoiceMapper.selectByCodeAndNum(selectByCodeAndNumModels);
				if (ListUtils.isNotEmpty(checkInvoice)) {
					throw new BizException(CarrierDataExceptionEnum.INVOICE_CODE_AND_NUM_CANNOT_REPEAT);
				}
			}

			tDriverCostApply = new TDriverCostApply();
			tDriverCostApply.setCostApplyCode(commonBiz.getBusinessTypeCode(BusinessCodeTypeEnum.DRIVER_COST_APPLY_CODE, "", BaseContextHandler.getUserName()));
			tDriverCostApply.setStaffId(tStaffBasic.getId());
			tDriverCostApply.setStaffName(tStaffBasic.getName());
			tDriverCostApply.setStaffMobile(tStaffBasic.getMobile());
			tDriverCostApply.setStaffProperty(tStaffBasic.getStaffProperty());
			tDriverCostApply.setVehicleId(vehicleBasicPropertyById.getVehicleId());
			tDriverCostApply.setVehicleNo(vehicleBasicPropertyById.getVehicleNo());
			tDriverCostApply.setCostType(requestModel.getCostType());
			tDriverCostApply.setApplyCost(requestModel.getApplyCost());
			tDriverCostApply.setOccurrenceTime(requestModel.getOccurrenceTime());
			tDriverCostApply.setApplyTime(new Date());
			tDriverCostApply.setApplyRemark(requestModel.getApplyRemark());
			if (vehicleOilCard != null) {
				tDriverCostApply.setAssociatedOilCard(vehicleOilCard.getCardNumber());
			}
			commonBiz.setBaseEntityAdd(tDriverCostApply, BaseContextHandler.getUserName());
			tDriverCostApplyMapper.insertSelectiveEncrypt(tDriverCostApply);

			//添加票据信息
			List<TCertificationPictures> tCertificationPicturesList = getCertificationPicturesList(requestModel, tDriverCostApply);

			if (ListUtils.isNotEmpty(requestModel.getInvoiceInfoList())) {
				// 插入发票信息
				Map<TDriverCostApplyInvoice, String> invoicePicMap = new HashMap<>();
				List<TDriverCostApplyInvoice> tDriverCostApplyInvoicesInsertList = new ArrayList<>();
				List<DriverCostApplyInvoiceRequestModel> invoiceInfoList = requestModel.getInvoiceInfoList();
				for (DriverCostApplyInvoiceRequestModel invoiceInfo : invoiceInfoList) {
					TDriverCostApplyInvoice tDriverCostApplyInvoice = MapperUtils.mapper(invoiceInfo, TDriverCostApplyInvoice.class);
					tDriverCostApplyInvoice.setDriverCostApplyId(tDriverCostApply.getId());
					commonBiz.setBaseEntityAdd(tDriverCostApplyInvoice, BaseContextHandler.getUserName());
					tDriverCostApplyInvoicesInsertList.add(tDriverCostApplyInvoice);
					invoicePicMap.put(tDriverCostApplyInvoice, invoiceInfo.getImagePath());
				}
				tDriverCostApplyInvoiceMapper.batchInsertAndReturnId(tDriverCostApplyInvoicesInsertList);

				invoicePicMap.forEach((tDriverCostApplyInvoice, picPath) -> tCertificationPicturesList.add(getCertificationPictures(tDriverCostApplyInvoice.getId(), picPath, CertificationPicturesFileTypeEnum.T_DRIVER_COST_INVOICE_FILE)));
			}

			if (ListUtils.isNotEmpty(tCertificationPicturesList)) {
				tCertificationPicturesMapper.batchInsert(tCertificationPicturesList);
			}
		} else {
			//编辑(重新提交)
			//查询存在的费用申请记录
			tDriverCostApply = tDriverCostApplyMapper.selectByIdAndDriverId(requestModel.getDriverCostApplyId(), loginDriverAppletUserId);
			if (tDriverCostApply == null) {
				throw new BizException(CarrierDataExceptionEnum.DRIVER_COST_APPLY_NOT_EXIST);
			}

			// 查询发票信息
			List<TDriverCostApplyInvoice> tDriverCostApplyInvoiceList = tDriverCostApplyInvoiceMapper.selectByDriverCostApplyId(tDriverCostApply.getId());
			Map<Long, TDriverCostApplyInvoice> invoiceMap = new HashMap<>();
			List<Long> invoiceIdList = new ArrayList<>();
			List<Long> removeInvoiceIdList = new ArrayList<>();
			if (ListUtils.isNotEmpty(tDriverCostApplyInvoiceList)) {
				for (TDriverCostApplyInvoice tDriverCostApplyInvoice : tDriverCostApplyInvoiceList) {
					invoiceMap.put(tDriverCostApplyInvoice.getId(), tDriverCostApplyInvoice);
					invoiceIdList.add(tDriverCostApplyInvoice.getId());
					removeInvoiceIdList.add(tDriverCostApplyInvoice.getId());
				}
			}

			// 编辑 发票代码+发票号码不能重复 修改发票信息时，发票代码+发票号码重复需过滤此校验
			List<SelectByCodeAndNumModel> selectByCodeAndNumModels = new ArrayList<>();
			List<DriverCostApplyInvoiceRequestModel> invoiceInfoList = requestModel.getInvoiceInfoList();
			for (DriverCostApplyInvoiceRequestModel invoiceInfo : invoiceInfoList) {
				//过滤没有变动的发票
				if (invoiceInfo.getInvoiceId() != null) {
					TDriverCostApplyInvoice tDriverCostApplyInvoice = invoiceMap.get(invoiceInfo.getInvoiceId());
					if (tDriverCostApplyInvoice == null
							|| !tDriverCostApplyInvoice.getType().equals(invoiceInfo.getType())
							|| !tDriverCostApplyInvoice.getInvoiceCode().equals(invoiceInfo.getInvoiceCode())
							|| !tDriverCostApplyInvoice.getInvoiceNum().equals(invoiceInfo.getInvoiceNum())) {
						selectByCodeAndNumModels.add(new SelectByCodeAndNumModel().setInvoiceCode(invoiceInfo.getInvoiceCode()).setInvoiceNum(invoiceInfo.getInvoiceNum()).setType(invoiceInfo.getType()));
					}
				} else {
					selectByCodeAndNumModels.add(new SelectByCodeAndNumModel().setInvoiceCode(invoiceInfo.getInvoiceCode()).setInvoiceNum(invoiceInfo.getInvoiceNum()).setType(invoiceInfo.getType()));
				}
			}

			List<TDriverCostApplyInvoice> checkInvoice = tDriverCostApplyInvoiceMapper.selectByCodeAndNum(selectByCodeAndNumModels);
			if (ListUtils.isNotEmpty(checkInvoice)) {
				throw new BizException(CarrierDataExceptionEnum.INVOICE_CODE_AND_NUM_CANNOT_REPEAT);
			}

			//仅已驳回才可以重新提交
			if (!DriverCostAuditEnum.REJECT.getKey().equals(tDriverCostApply.getAuditStatus())) {
				throw new BizException(CarrierDataExceptionEnum.DRIVER_COST_APPLY_OPERATE_ERROR);
			}

			//更新费用申请信息
			tDriverCostApply.setAuditStatus(DriverCostAuditEnum.WAIT_BUSINESS_AUDIT.getKey());
			tDriverCostApply.setVehicleId(vehicleBasicPropertyById.getVehicleId());
			tDriverCostApply.setVehicleNo(vehicleBasicPropertyById.getVehicleNo());
			tDriverCostApply.setCostType(requestModel.getCostType());
			tDriverCostApply.setApplyCost(requestModel.getApplyCost());
			tDriverCostApply.setOccurrenceTime(requestModel.getOccurrenceTime());
			tDriverCostApply.setApplyRemark(requestModel.getApplyRemark());
			if (vehicleOilCard != null) {
				tDriverCostApply.setAssociatedOilCard(vehicleOilCard.getCardNumber());
			} else {
				tDriverCostApply.setAssociatedOilCard("");
			}
			//清空审核人
			tDriverCostApply.setAuditorName("");
			tDriverCostApply.setAuditTime(null);
			commonBiz.setBaseEntityModify(tDriverCostApply, BaseContextHandler.getUserName());
			tDriverCostApplyMapper.updateByPrimaryKey(tDriverCostApply);

			//插入新的票据信息
			List<TCertificationPictures> tCertificationPicturesList = getCertificationPicturesList(requestModel, tDriverCostApply);

			Map<TDriverCostApplyInvoice, String> invoicePicMap = new HashMap<>();
			List<TDriverCostApplyInvoice> tDriverCostApplyInvoicesInsertList = new ArrayList<>();
			List<TDriverCostApplyInvoice> tDriverCostApplyInvoicesUpdateList = new ArrayList<>();
			//发票信息编辑
			if (ListUtils.isNotEmpty(invoiceInfoList)) {
				for (DriverCostApplyInvoiceRequestModel invoiceInfoModel : invoiceInfoList) {
					TDriverCostApplyInvoice applyInvoice = MapperUtils.mapper(invoiceInfoModel, TDriverCostApplyInvoice.class);
					if (invoiceInfoModel.getInvoiceId() == null) {
						//新增
						applyInvoice.setDriverCostApplyId(tDriverCostApply.getId());
						commonBiz.setBaseEntityAdd(applyInvoice, BaseContextHandler.getUserName());
						tDriverCostApplyInvoicesInsertList.add(applyInvoice);
						invoicePicMap.put(applyInvoice, invoiceInfoModel.getImagePath());
					} else {
						//编辑
						applyInvoice.setId(invoiceInfoModel.getInvoiceId());
						commonBiz.setBaseEntityModify(applyInvoice, BaseContextHandler.getUserName());
						tDriverCostApplyInvoicesUpdateList.add(applyInvoice);
						invoicePicMap.put(applyInvoice, invoiceInfoModel.getImagePath());
					}

					//删除
					removeInvoiceIdList.removeIf(next -> next.equals(invoiceInfoModel.getInvoiceId()));
				}

				if (ListUtils.isNotEmpty(tDriverCostApplyInvoicesInsertList)) {
					//批量插入发票信息
					tDriverCostApplyInvoiceMapper.batchInsertAndReturnId(tDriverCostApplyInvoicesInsertList);
				}

				//新增票据
				invoicePicMap.forEach((tDriverCostApplyInvoice, picPath) -> tCertificationPicturesList.add(getCertificationPictures(tDriverCostApplyInvoice.getId(), picPath, CertificationPicturesFileTypeEnum.T_DRIVER_COST_INVOICE_FILE)));
			}

			//删除票据
			if (ListUtils.isNotEmpty(removeInvoiceIdList)) {
				for (Long removeInvoiceId : removeInvoiceIdList) {
					TDriverCostApplyInvoice tDriverCostApplyInvoiceDel = new TDriverCostApplyInvoice();
					tDriverCostApplyInvoiceDel.setId(removeInvoiceId);
					tDriverCostApplyInvoiceDel.setValid(IfValidEnum.INVALID.getKey());
					commonBiz.setBaseEntityModify(tDriverCostApplyInvoiceDel, BaseContextHandler.getUserName());
					tDriverCostApplyInvoicesUpdateList.add(tDriverCostApplyInvoiceDel);
				}
			}

			if (ListUtils.isNotEmpty(tDriverCostApplyInvoicesUpdateList)) {
				//批量更新发票信息
				tDriverCostApplyInvoiceMapper.batchUpdateSelective(tDriverCostApplyInvoicesUpdateList);
			}

			List<Long> picObjectIdList = new ArrayList<>();
			picObjectIdList.add(tDriverCostApply.getId());
			picObjectIdList.addAll(invoiceIdList);

			//删除存在的票据信息
			List<TCertificationPictures> certificationPicturesList = tCertificationPicturesMapper.getTPicsByIds(LocalStringUtil.listTostring(picObjectIdList, ','),
					LocalStringUtil.listTostring(Collections.singletonList(CertificationPicturesObjectTypeEnum.T_DRIVER_COST.getObjectType()), ','));

			if (ListUtils.isNotEmpty(certificationPicturesList)) {
				List<TCertificationPictures> delList = certificationPicturesList.stream().map(e -> {
					TCertificationPictures tCertificationPictures = new TCertificationPictures();
					tCertificationPictures.setId(e.getId());
					tCertificationPictures.setValid(IfValidEnum.INVALID.getKey());
					commonBiz.setBaseEntityModify(tCertificationPictures, BaseContextHandler.getUserName());
					return tCertificationPictures;
				}).collect(Collectors.toList());
				tCertificationPicturesMapper.batchUpdate(delList);
			}

			if (ListUtils.isNotEmpty(tCertificationPicturesList)) {
				tCertificationPicturesMapper.batchInsert(tCertificationPicturesList);
			}
		}
	}

	private List<TCertificationPictures> getCertificationPicturesList(AddDriverCostApplyRequestModel requestModel, TDriverCostApply tDriverCostApply) {
		TCertificationPictures tCertificationPictures;
		List<TCertificationPictures> tCertificationPicturesList = new ArrayList<>();
		List<AddDriverCostApplyRequestModel.DriverCostApplyTickDetail> ticketList = requestModel.getTicketList();
		for (AddDriverCostApplyRequestModel.DriverCostApplyTickDetail ticketDetail : ticketList) {
			CertificationPicturesFileTypeEnum typeEnum = CertificationPicturesFileTypeEnum.getEnum(
					CertificationPicturesObjectTypeEnum.T_DRIVER_COST,
					ticketDetail.getType());
			for(String imagePath : ticketDetail.getImagePathList()){
				tCertificationPictures = getCertificationPictures(tDriverCostApply.getId(), imagePath, typeEnum);
				tCertificationPicturesList.add(tCertificationPictures);
			}
		}
		return tCertificationPicturesList;
	}

	private TCertificationPictures getCertificationPictures(Long objectId, String imagePath, CertificationPicturesFileTypeEnum typeEnum) {
		TCertificationPictures tCertificationPictures;
		tCertificationPictures = new TCertificationPictures();
		tCertificationPictures.setObjectId(objectId);
		tCertificationPictures.setObjectType(CertificationPicturesObjectTypeEnum.T_DRIVER_COST.getObjectType());
		tCertificationPictures.setFileType(typeEnum.getFileType());
		tCertificationPictures.setFileTypeName(typeEnum.getFileName());
		tCertificationPictures.setFileName(typeEnum.getFileName());
		tCertificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DRIVER_COST_APPLY.getKey(), "", imagePath, null));
		commonBiz.setBaseEntityAdd(tCertificationPictures, BaseContextHandler.getUserName());
		return tCertificationPictures;
	}

	/**
	 * 撤销费用申请
	 *
	 * @param requestModel 司机费用申请表id
	 */
	@Transactional
	public void undoDriverCostApply(UndoDriverCostApplyRequestModel requestModel) {
		//小程序请求
		Long loginDriverAppletUserId = null;
		if (CommonConstant.INTEGER_THREE.equals(requestModel.getSource())) {
			loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
			if (loginDriverAppletUserId == null || CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
				throw new BizException(CarrierDataExceptionEnum.DATA_ERROR_AND_RE_LOGIN);
			}
		}

		//查询费用申请记录
		TDriverCostApply tDriverCostApply = tDriverCostApplyMapper.selectByIdAndDriverId(requestModel.getDriverCostApplyId(), loginDriverAppletUserId);
		if (tDriverCostApply == null) {
			throw new BizException(CarrierDataExceptionEnum.DATA_STATUS_HAS_UPDATE);
		}

		//已审核提示
		if (DriverCostAuditEnum.AUDITED.getKey().equals(tDriverCostApply.getAuditStatus())) {
			throw new BizException(CarrierDataExceptionEnum.DRIVER_COST_APPLY_AUDITED);
		}

		//待业务审核/待财务审核/已驳回 才可以撤销
		if (!DriverCostAuditEnum.WAIT_BUSINESS_AUDIT.getKey().equals(tDriverCostApply.getAuditStatus())
				&& !DriverCostAuditEnum.WAIT_FINANCIAL_AUDIT.getKey().equals(tDriverCostApply.getAuditStatus())
				&& !DriverCostAuditEnum.REJECT.getKey().equals(tDriverCostApply.getAuditStatus())) {
			throw new BizException(CarrierDataExceptionEnum.DRIVER_COST_APPLY_CANCEL_ERROR);
		}

		//更新费用申请记录
		TDriverCostApply tDriverCostApplyUp = new TDriverCostApply();
		tDriverCostApplyUp.setId(tDriverCostApply.getId());
		tDriverCostApplyUp.setAuditStatus(DriverCostAuditEnum.REPEAL.getKey());
		tDriverCostApplyUp.setRemark(requestModel.getRemark());
		tDriverCostApplyUp.setAuditTime(new Date());
		tDriverCostApplyUp.setAuditorName(BaseContextHandler.getUserName());
		commonBiz.setBaseEntityModify(tDriverCostApplyUp, BaseContextHandler.getUserName());
		tDriverCostApplyMapper.updateByPrimaryKeySelectiveEncrypt(tDriverCostApplyUp);
	}

	/**
	 * 费用申请汇总列表
	 *
	 * @param requestModel 筛选条件
	 * @return 费用申请汇总列表
	 */
	public PageInfo<SearchCostApplySummaryResponseModel> searchCostApplySummary(SearchCostApplySummaryRequestModel requestModel) {
		requestModel.enablePaging();
		List<String> ids = tDriverCostApplyMapper.searchCostApplySummaryIds(requestModel);
		PageInfo pageInfo = new PageInfo<>(ids);
		//处理sql参数
		List<Long> sqlIds = new ArrayList<>();
		for (String id : ids) {
			sqlIds.addAll(Arrays.stream(id.split(CommonConstant.COMMA)).map(Long::parseLong).collect(Collectors.toList()));
		}
		List<SearchCostApplySummaryResponseModel> costApplySummaryResponseList;
		if (ListUtils.isNotEmpty(sqlIds)) {
			costApplySummaryResponseList = tDriverCostApplyMapper.searchCostApplySummary(sqlIds);
		} else {
			costApplySummaryResponseList = new ArrayList<>();
		}
		pageInfo.setList(costApplySummaryResponseList);
		return pageInfo;
	}

	/**
	 * 司机扣款费用申请
	 *
	 * @param requestModel 请求入参
	 */
	@Transactional(rollbackFor = Exception.class)
	public void deductionsCostApply(DeductionsCostApplyRequestModel requestModel) {

		//查询司机基础信息
		TStaffBasic tStaffBasic = tStaffBasicMapper.selectByPrimaryKey(requestModel.getDriverId());
		if (tStaffBasic == null || IfValidEnum.INVALID.getKey().equals(tStaffBasic.getValid())) {
			throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
		}

		// 内部司机校验
		if (!StaffPropertyEnum.OWN_STAFF.getKey().equals(tStaffBasic.getStaffProperty()) &&
				!StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(tStaffBasic.getStaffProperty())) {
			throw new BizException(CarrierDataExceptionEnum.ONLY_INTERIOR_STAFF);
		}

		// 校验司机账户余额
		TReserveBalance reserveBalance = reserveBalanceBiz.getReserveBalanceByDriverId(tStaffBasic.getId());
		Optional.ofNullable(reserveBalance)
				.filter(f -> requestModel.getDeductionAmount().compareTo(f.getBalanceAmount()) < CommonConstant.INTEGER_ONE)
				.orElseThrow(() -> new BizException(CarrierDataExceptionEnum.RESERVE_BALANCE_INSUFFICIENT));

		TDriverCostApply tDriverCostApply = new TDriverCostApply();
		tDriverCostApply.setCostApplyCode(commonBiz.getBusinessTypeCode(BusinessCodeTypeEnum.DRIVER_COST_APPLY_CODE, "", BaseContextHandler.getUserName()));
		tDriverCostApply.setStaffId(tStaffBasic.getId());
		tDriverCostApply.setStaffName(tStaffBasic.getName());
		tDriverCostApply.setStaffMobile(tStaffBasic.getMobile());
		tDriverCostApply.setStaffProperty(tStaffBasic.getStaffProperty());
		tDriverCostApply.setCostType(DriverCostApplyTypeEnum.DEDUCTIONS.getKey());
		tDriverCostApply.setApplyCost(requestModel.getDeductionAmount());
		tDriverCostApply.setOccurrenceTime(requestModel.getOccurrenceTime());
		tDriverCostApply.setApplyTime(new Date());
		tDriverCostApply.setApplyRemark(requestModel.getApplyRemark());
		commonBiz.setBaseEntityAdd(tDriverCostApply, BaseContextHandler.getUserName());
		tDriverCostApplyMapper.insertSelectiveEncrypt(tDriverCostApply);

		//添加票据信息
		var tCertificationPicturesList = commonBiz.getCertificationPictures(tDriverCostApply.getId(),
				CertificationPicturesObjectTypeEnum.T_DRIVER_COST,
				CertificationPicturesFileTypeEnum.T_DRIVER_COST_SCENE_FILE,
				requestModel.getCostBasisImagePaths(),
				(imageFile) -> commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DRIVER_COST_APPLY.getKey(), "", imageFile, null));
		if (ListUtils.isNotEmpty(tCertificationPicturesList)) {
			tCertificationPicturesMapper.batchInsert(tCertificationPicturesList);
		}
	}

	/**
	 * 费用申请红冲退款
	 *
	 * @param requestModel 请求 Model
	 */
	@Transactional
	public void redChargeRefund(RedChargeRefundCostApplyRequestModel requestModel) {

		// 查询费用申请
		TDriverCostApply dbDriverCostApply= Optional.ofNullable(tDriverCostApplyMapper.selectByPrimaryKeyDecrypt(requestModel.getDriverCostApplyId()))
				.filter(f -> IfValidEnum.VALID.getKey().equals(f.getValid()))
				.orElseThrow(() -> new BizException(CarrierDataExceptionEnum.DRIVER_COST_APPLY_NOT_EXIST));

		// 扣款类型不允许红冲
		ExceptionUtils.isTure(DriverCostApplyTypeEnum.DEDUCTIONS.getKey().equals(dbDriverCostApply.getCostType()))
				.throwMessage(CarrierDataExceptionEnum.NOT_ALLOWED_RED_CHARGE_REFUND_BY_COST_DEDUCTIONS);
		// 仅已审核才能操作
		ExceptionUtils.isTure(!DriverCostAuditEnum.AUDITED.getKey().equals(dbDriverCostApply.getAuditStatus()))
				.throwMessage(CarrierDataExceptionEnum.ONLY_AUDIT_OPERATED_ERROR);
		// 内部司机校验
		if (!StaffPropertyEnum.OWN_STAFF.getKey().equals(dbDriverCostApply.getStaffProperty()) &&
				!StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(dbDriverCostApply.getStaffProperty())) {
			throw new BizException(CarrierDataExceptionEnum.ONLY_INTERIOR_STAFF_RED_CHARGE_REFUND);
		}

		// 更新费用申请状态
		TDriverCostApply driverCostApply = new TDriverCostApply();
		driverCostApply.setId(dbDriverCostApply.getId());
		driverCostApply.setAuditStatus(DriverCostAuditEnum.RED_CHARGE_REFUND.getKey());
		driverCostApply.setAuditorName(BaseContextHandler.getUserName());
		driverCostApply.setAuditTime(new Date());
		commonBiz.setBaseEntityModify(driverCostApply, BaseContextHandler.getUserName());
		tDriverCostApplyMapper.updateByPrimaryKeySelectiveEncrypt(driverCostApply);

		TOperateLogs tOperateLogs = commonBiz.addOperateLogs(dbDriverCostApply.getId(), OperateLogsOperateTypeEnum.DRIVER_COST_APPLY_RED_CHARGE_REFUND, "【通过】", BaseContextHandler.getUserName());
		tOperateLogsMapper.insertSelective(tOperateLogs);

		// 生成红冲备用金申请
		TReserveApply redChargeRefundReserveApply = reserveApplyBiz.createReserveRemitApply(dbDriverCostApply.getStaffId(),
				dbDriverCostApply.getApplyCost(),
				dbDriverCostApply.getCostApplyCode(),
				ReserveApplyTypeEnum.RED_CHARGE_REFUND_TYPE);

		// 充值备用金余额
		ReserveApplyBalanceChangeBoModel model = new ReserveApplyBalanceChangeBoModel()
				.setBalance(redChargeRefundReserveApply.getBalanceAmount())
				.setReserveCode(redChargeRefundReserveApply.getReserveApplyCode());
		ReserveBalanceChangeHandleBoModel boModel = new ReserveBalanceChangeHandleBoModel()
				.setDriverId(dbDriverCostApply.getStaffId())
				.setAmount(dbDriverCostApply.getApplyCost())
				.setReserveList(Collections.singletonList(model))
				.setTypeEnum(ReserveBalanceRunningTypeEnum.RED_CHARGE_REFUND_TYPE);
		reserveBalanceBiz.balanceChangeHandler(boModel);
	}
}
