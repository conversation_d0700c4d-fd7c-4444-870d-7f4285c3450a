package com.logistics.management.webapi.api.feign.driversafepromise.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 签订承诺书列表
 * @Author: sj
 * @Date: 2019/11/4 10:19
 */
@Data
public class SearchSignSafePromiseListResponseDto {
    @ApiModelProperty("承诺书ID")
    private String safePromiseId = "";
    @ApiModelProperty("签订承诺书ID")
    private String signSafePromiseId = "";
    @ApiModelProperty("签订状态: 0 待签订、1 已签订")
    private String status = "";
    @ApiModelProperty("签订状态文本")
    private String statusLabel = "";
    @ApiModelProperty("司机ID")
    private String staffId;
    @ApiModelProperty("司机名称")
    private String staffName = "";
    @ApiModelProperty("司机电话")
    private String staffMobile = "";
    @ApiModelProperty("人员机构 1 自主 2外部 3 自营")
    private String staffProperty = "";
    @ApiModelProperty("人员机构展示文本")
    private String staffPropertyLabel = "";
    @ApiModelProperty("车牌号")
    private String vehicleNo = "";
    @ApiModelProperty("签订时间")
    private String signTime = "";

    @ApiModelProperty("手持承诺书图片地址-相对路径")
    private String handPromiseUrl = "";
    @ApiModelProperty("手持承诺书图片地址-绝对路径")
    private String absoluteHandPromiseUrl = "";

    @ApiModelProperty("签字责任书图片地址-相对路径")
    private String signResponsibilityUrl = "";
    @ApiModelProperty("签字责任书图片地址-绝对路径")
    private String absoluteSignResponsibilityUrl = "";
}
