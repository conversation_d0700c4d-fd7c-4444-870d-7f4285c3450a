package com.logistics.appapi.client.thirdparty.warehouse.stock.reservationconfig.hystrix;

import com.logistics.appapi.client.thirdparty.warehouse.stock.reservationconfig.WarehouseReservationConfigServiceApi;
import com.logistics.appapi.client.thirdparty.warehouse.stock.reservationconfig.request.SearchReservationTimeByWarehouseRequestModel;
import com.logistics.appapi.client.thirdparty.warehouse.stock.reservationconfig.response.SearchReservationTimeByWarehouseResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/8/26 10:08
 */
@Component
public class WarehouseReservationConfigServiceApiHystrix implements WarehouseReservationConfigServiceApi {
    @Override
    public Result<SearchReservationTimeByWarehouseResponseModel> searchReservationTimeByWarehouse(SearchReservationTimeByWarehouseRequestModel requestModel) {
        return Result.timeout();
    }
}
