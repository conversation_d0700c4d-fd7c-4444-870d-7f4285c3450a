package com.logistics.tms.api.feign.gpsfee.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/10/9 10:11
 */
@Data
public class GetDeductingHistoryResponseModel {
    @ApiModelProperty("扣减月份")
    private String deductingMonth;
    @ApiModelProperty("总金额")
    private BigDecimal totalFee;
    @ApiModelProperty("扣减费用")
    private BigDecimal deductingFee;
    @ApiModelProperty("剩余未扣减费用")
    private BigDecimal remainingDeductingFee;
    @ApiModelProperty("最后修改人")
    private String lastModifiedBy;
    @ApiModelProperty("最后修改时间")
    private Date lastModifiedTime;
}
