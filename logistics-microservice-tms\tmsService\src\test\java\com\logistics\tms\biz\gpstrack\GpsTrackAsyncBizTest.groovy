package com.logistics.tms.biz.gpstrack


import com.logistics.tms.api.feign.gpstrack.model.VehicleLatestPositionModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TVehicleGps
import com.logistics.tms.mapper.TVehicleGpsMapper
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class GpsTrackAsyncBizTest extends Specification {
    @Mock
    TVehicleGpsMapper tVehicleGpsMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    Logger log
    @InjectMocks
    GpsTrackAsyncBiz gpsTrackAsyncBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "pull Sync Vehicle Latest Info"() {
        given:
        when(tVehicleGpsMapper.selectAllVehicles()).thenReturn([new TVehicleGps(vehicleNo: "vehicleNo", vehicleStatus: 0, uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 18).getTime(), currentLocation: "currentLocation")])
        when(tVehicleGpsMapper.batchUpdateVehicleGps(any())).thenReturn(0)

        expect:
        gpsTrackAsyncBiz.pullSyncVehicleLatestInfo()
        assert expectedResult == false

        where:
        expectedResult << true
    }

    @Unroll
    def "get Vehicle Latest Position And Update Local where searchVehicleNo=#searchVehicleNo then expect: #expectedResult"() {
        given:
        when(tVehicleGpsMapper.selectVehicleGpsByVehicleNo(anyString())).thenReturn([new TVehicleGps(vehicleStatus: 0, uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 18).getTime(), currentLocation: "currentLocation")])
        when(tVehicleGpsMapper.batchUpdateVehicleGps(any())).thenReturn(0)

        expect:
        gpsTrackAsyncBiz.getVehicleLatestPositionAndUpdateLocal(searchVehicleNo) == expectedResult

        where:
        searchVehicleNo   || expectedResult
        "searchVehicleNo" || new VehicleLatestPositionModel()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme