package com.logistics.management.webapi.controller.routeenquiry.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.client.routeenquiry.response.GetRouteEnquiryDetailAddressListResponseModel;
import com.logistics.management.webapi.client.routeenquiry.response.GetRouteEnquiryDetailResponseModel;
import com.logistics.management.webapi.client.routeenquiry.response.RouteEnquiryFileListResponseModel;
import com.logistics.management.webapi.client.routeenquiry.response.RouteEnquiryQuoteListResponseModel;
import com.logistics.management.webapi.controller.routeenquiry.response.*;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2024/7/10 17:19
 */
public class RouteEnquiryGetDetailMapping extends MapperMapping<GetRouteEnquiryDetailResponseModel, GetRouteEnquiryDetailResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;

    public RouteEnquiryGetDetailMapping(String imagePrefix , Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        GetRouteEnquiryDetailResponseModel source = getSource();
        GetRouteEnquiryDetailResponseDto destination = getDestination();

        //状态
        if (CommonConstant.INTEGER_ONE.equals(source.getIfCancel())){
            destination.setStatus(RouteEnquiryStatusEnum.QUOTATION_CANCEL.getKey().toString());
            destination.setStatusLabel(RouteEnquiryStatusEnum.QUOTATION_CANCEL.getValue());
        }else{
            destination.setStatusLabel(RouteEnquiryStatusEnum.getEnum(source.getStatus()).getValue());
        }

        //报价生效期限
        if (source.getQuoteStartTime() != null) {
            destination.setQuoteStartTime(DateUtils.dateToString(source.getQuoteStartTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if (source.getQuoteEndTime() != null) {
            destination.setQuoteEndTime(DateUtils.dateToString(source.getQuoteEndTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }

        //地址列表
        GetRouteEnquiryDetailAddressListResponseDto addressDto;
        List<GetRouteEnquiryDetailAddressListResponseDto> addressList = new ArrayList<>();
        for (GetRouteEnquiryDetailAddressListResponseModel model : source.getAddressList()) {
            addressDto = new GetRouteEnquiryDetailAddressListResponseDto();
            addressDto.setRouteEnquiryAddressId(model.getRouteEnquiryAddressId().toString());

            addressDto.setLoadAddress((StringUtils.isBlank(model.getFromWarehouse()) ? CommonConstant.BLANK_TEXT : ("【"+model.getFromWarehouse()+"】")) + model.getFromProvinceName() + model.getFromCityName() + model.getFromAreaName());
            addressDto.setUnloadAddress(model.getToProvinceName() + model.getToCityName() + model.getToAreaName());

            //大于等于【待车主确认】状态展示报价信息
            if (source.getStatus() >= RouteEnquiryStatusEnum.WAIT_CARRIER_CONFIRM.getKey()){
                addressDto.setDistance(model.getDistance().stripTrailingZeros().toPlainString());
                if (PriceTypeEnum.UNIT_PRICE.getKey().equals(model.getQuotePriceType())){
                    addressDto.setQuotePrice(ConverterUtils.toString(model.getQuotePrice()) + GoodsUnitEnum.BY_WEIGHT.getPriceUnit());
                }else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(model.getQuotePriceType())){
                    addressDto.setQuotePrice(ConverterUtils.toString(model.getQuotePrice()) + CommonConstant.YUAN);
                }
                addressDto.setQuotePriceType(model.getQuotePriceType().toString());
                addressDto.setQuotePriceTypeLabel(PriceTypeEnum.getEnum(model.getQuotePriceType()).getValue());
                addressDto.setQuoteRemark(model.getQuoteRemark());
            }else{
                addressDto.setDistance(CommonConstant.BLANK_TEXT);
                addressDto.setQuotePrice(CommonConstant.BLANK_TEXT);
                addressDto.setQuotePriceType(CommonConstant.BLANK_TEXT);
                addressDto.setQuotePriceTypeLabel(CommonConstant.BLANK_TEXT);
                addressDto.setQuoteRemark(CommonConstant.BLANK_TEXT);
            }
            addressList.add(addressDto);
        }
        destination.setAddressList(addressList);

        //车主报价记录
        if (ListUtils.isNotEmpty(source.getAddressList())) {
            RouteEnquiryQuoteListResponseDto quoteDto;
            List<RouteEnquiryQuoteListResponseDto> quoteList = new ArrayList<>();
            for (RouteEnquiryQuoteListResponseModel model : source.getQuoteList()) {
                quoteDto = new RouteEnquiryQuoteListResponseDto();
                quoteDto.setRouteEnquiryCompanyId(model.getRouteEnquiryCompanyId().toString());

                //车主
                if (CompanyTypeEnum.COMPANY.getKey().equals(model.getCompanyCarrierType())) {
                    quoteDto.setCompanyCarrierName(model.getCompanyCarrierName());
                } else if (CompanyTypeEnum.PERSONAL.getKey().equals(model.getCompanyCarrierType())) {
                    quoteDto.setCompanyCarrierName(model.getCarrierContactName() + " " + model.getCarrierContactPhone());
                }

                quoteDto.setQuoteTime(DateUtils.dateToString(model.getQuoteTime(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));

                //竞价单未取消且已选择该车主时，显示“是”
                if (QuoteStatusEnum.SELECTED.getKey().equals(model.getQuoteStatus()) && CommonConstant.INTEGER_ZERO.equals(source.getIfCancel())) {
                    quoteDto.setSelectCarrier(YesOrNoEnum.YES.getValue());
                }
                //竞价单已取消或未选择该车主时，显示“否”
                else {
                    quoteDto.setSelectCarrier(YesOrNoEnum.NO.getValue());
                }

                quoteList.add(quoteDto);
            }
            destination.setQuoteList(quoteList);
        }

        //上传文件列表
        if (ListUtils.isNotEmpty(source.getFileList())) {
            RouteEnquiryFileListResponseDto fileListDto;
            List<RouteEnquiryFileListResponseDto> fileList = new ArrayList<>();
            for (RouteEnquiryFileListResponseModel model : source.getFileList()) {
                fileListDto = new RouteEnquiryFileListResponseDto();
                fileListDto.setLastModifiedBy(model.getLastModifiedBy());
                fileListDto.setLastModifiedTime(DateUtils.dateToString(model.getLastModifiedTime(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
                fileListDto.setUploadNode(RouteEnquiryAttachmentTypeEnum.getEnum(model.getAttachmentType()).getUploadNode());

                RouteEnquiryFileResponseDto fileDto;
                List<RouteEnquiryFileResponseDto> list = new ArrayList<>();
                for (String path : model.getFileList()) {
                    fileDto = new RouteEnquiryFileResponseDto();
                    fileDto.setRelativePath(path);
                    fileDto.setSrc(imagePrefix + imageMap.get(path));
                    list.add(fileDto);
                }
                fileListDto.setFileList(list);

                fileList.add(fileListDto);
            }
            destination.setFileList(fileList);
        }
    }
}
