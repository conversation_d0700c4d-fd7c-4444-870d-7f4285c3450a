package com.logistics.appapi.client.reserveapply;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.reserveapply.hystrix.DriverReserveApplyClientHystrix;
import com.logistics.appapi.client.reserveapply.request.ApplyReserveBalanceRequestModel;
import com.logistics.appapi.client.reserveapply.request.ReserveBalanceApplyCancelRequestModel;
import com.logistics.appapi.client.reserveapply.request.ReserveBalanceApplyDetailRequestModel;
import com.logistics.appapi.client.reserveapply.request.ReserveBalanceApplyListRequestModel;
import com.logistics.appapi.client.reserveapply.response.ReserveBalanceApplyDetailResponseModel;
import com.logistics.appapi.client.reserveapply.response.ReserveBalanceApplyListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Api(value = "司机备用申请列表")
@FeignClient(name = FeignClientName.TMS_SERVICES,
        fallback = DriverReserveApplyClientHystrix.class,
        path = "/service/DriverReserveApply/applet")
public interface DriverReserveApplyClient {

    @ApiOperation(value = "申请备用金/重新申请")
    @PostMapping(value = "/applyReserveBalance")
    Result<Boolean> applyReserveBalance(@RequestBody ApplyReserveBalanceRequestModel requestModel);

    @ApiOperation(value = "备用金额申请记录列表")
    @PostMapping(value = "/reserveBalanceApplyList")
    Result<ReserveBalanceApplyListResponseModel> reserveBalanceApplyList(@RequestBody ReserveBalanceApplyListRequestModel requestModel);

    @ApiOperation(value = "申请记录详情")
    @PostMapping(value = "/reserveBalanceApplyDetail")
    Result<ReserveBalanceApplyDetailResponseModel> reserveBalanceApplyDetail(@RequestBody ReserveBalanceApplyDetailRequestModel requestModel);

    @ApiOperation(value = "撤销申请记录")
    @PostMapping(value = "/cancel")
    Result<Boolean> cancel(@RequestBody ReserveBalanceApplyCancelRequestModel requestModel);
}
