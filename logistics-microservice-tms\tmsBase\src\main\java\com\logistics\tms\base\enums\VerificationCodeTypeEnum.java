package com.logistics.tms.base.enums;

/**
 * @Autho: jianfeng.wu
 * @Date:Create in 下午1:34 2018/4/14
 */
public enum VerificationCodeTypeEnum {

    TMS_DRIVER_APPLET_LOGIN(VerificationCodeSourceTypeEnum.TMS_DRIVER_APPLET, 1, "tms司机小程序登录"),
    TMS_DRIVER_APPLET_FIND_PASS(VerificationCodeSourceTypeEnum.TMS_DRIVER_APPLET, 2, "tms司机小程序找回密码"),
    TMS_DRIVER_APPLET_MODIFY_PHONE(VerificationCodeSourceTypeEnum.TMS_DRIVER_APPLET, 3, "tms司机小程序修改手机号"),
    TMS_DRIVER_APPLET_MODIFY_BANKCARD(VerificationCodeSourceTypeEnum.TMS_DRIVER_APPLET, 4, "tms司机小程序银行卡变更"),
    H5_VISITOR_RESERVATION(VerificationCodeSourceTypeEnum.TMS_DRIVER_APPLET, 5, "访客预约"),
    H5_VISITOR_SIGN_IN(VerificationCodeSourceTypeEnum.TMS_DRIVER_APPLET, 6, "访客签到"),

    TMS_CUSTOMER_WEB_LOGIN(VerificationCodeSourceTypeEnum.TMS_CUSTOMER_WEB, 1, "tms客户前台登录"),
    TMS_CUSTOMER_WEB_FIND_PASS(VerificationCodeSourceTypeEnum.TMS_CUSTOMER_WEB, 2, "tms客户前台找回密码"),
    TMS_CUSTOMER_WEB_MODIFY_PHONE(VerificationCodeSourceTypeEnum.TMS_CUSTOMER_WEB, 3, "tms客户前台修改手机号"),
    ;

    private VerificationCodeSourceTypeEnum sourceTypeEnum;
    private Integer key;
    private String value;

    VerificationCodeTypeEnum(VerificationCodeSourceTypeEnum sourceTypeEnum, Integer key, String value) {
        this.sourceTypeEnum = sourceTypeEnum;
        this.key = key;
        this.value = value;
    }

    public VerificationCodeSourceTypeEnum getSourceTypeEnum() {
        return sourceTypeEnum;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
