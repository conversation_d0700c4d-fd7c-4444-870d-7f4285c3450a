package com.logistics.management.webapi.controller.carrierorder.response;

import com.logistics.management.webapi.controller.carrierorder.request.LoadGoodsForYeloLifeRequestCodeDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-07-29 15:30
 */
@Data
public class SignDetailForYeloLifeGoodsCodeResponseDto {

    @ApiModelProperty(value = "code", required = true)
    private String yeloCode;

    @ApiModelProperty(value = "重量 对应实提实卸实签", required = true)
    private String weight;

    @ApiModelProperty(value = "单位 1.kg", required = true)
    private String unit;

}
