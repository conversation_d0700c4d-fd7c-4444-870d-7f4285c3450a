package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2018/9/29 10:46
 */
public enum SwitchEnum {
    OPEN(1, "开"),
    CLOSE(0, "关"),
    ;

    private final Integer key;
    private final String value;

    SwitchEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
