package com.logistics.management.webapi.api.feign.leave.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 请假记录列表查询响应dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/15
 */
@Data
@ExcelIgnoreUnannotated
public class SearchLeaveListResponseDto {

	@ApiModelProperty("请假申请ID")
	private String leaveApplyId = "";

	@ApiModelProperty("请假申请审核状态,审核状态: 0 待审核，1 已审核，2 已驳回，3 已撤销")
	private String leaveAuditStatus = "";

	@ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
	private String staffProperty = "";

	@ApiModelProperty("请假类型: 1: 事假")
	private String leaveType = "";

	@ApiModelProperty("请假申请审核状态,审核状态展示文本")
	@ExcelProperty(value = "审核状态")
	private String leaveAuditStatusLabel = "";

	@ApiModelProperty("申请人,姓名_手机号")
	@ExcelProperty(value = "司机")
	private String leaveApplyStaff = "";

	@ApiModelProperty("人员机构展示文本")
	@ExcelProperty(value = "司机机构")
	private String staffPropertyLabel = "";

	@ApiModelProperty("请假类型展示文本")
	@ExcelProperty(value = "请假类型")
	private String leaveTypeLabel = "";

	@ApiModelProperty("请假申请开始时间 (年-月-日 上午/下午)")
	@ExcelProperty(value = "开始时间")
	private String leaveStartTime = "";

	@ApiModelProperty("请假申请结束时间 (年-月-日 上午/下午)")
	@ExcelProperty(value = "结束时间")
	private String leaveEndTime = "";

	@ApiModelProperty("请假时长")
	@ExcelProperty(value = "时长（天）")
	private String leaveDuration = "";

	@ApiModelProperty("审核人")
	@ExcelProperty(value = "审核人")
	private String auditorName = "";

	@ApiModelProperty("审核时间")
	@ExcelProperty(value = "审核时间")
	private String auditTime = "";

}
