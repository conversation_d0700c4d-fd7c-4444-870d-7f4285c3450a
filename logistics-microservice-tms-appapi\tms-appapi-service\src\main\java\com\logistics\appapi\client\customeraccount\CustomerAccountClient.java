package com.logistics.appapi.client.customeraccount;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.customeraccount.hystrix.CustomerAccountClientHystrix;
import com.logistics.appapi.client.customeraccount.request.*;
import com.logistics.appapi.client.customeraccount.response.CustomerLoginResponseModel;
import com.logistics.appapi.client.customeraccount.response.UpdatePhoneByVerifyPhoneResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2024/3/7 16:53
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/customerAccount",
        fallback = CustomerAccountClientHystrix.class)
public interface CustomerAccountClient {

    /**
     * 司机小程序默认登录
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "司机小程序默认登录")
    @PostMapping(value = "/driverAppletDefaultLogin")
    Result<CustomerLoginResponseModel> driverAppletDefaultLogin(@RequestBody DriverAppletDefaultLoginRequestModel requestModel);

    /**
     * 司机小程序登录
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "司机小程序登录")
    @PostMapping(value = "/driverAppletLogin")
    Result<CustomerLoginResponseModel> driverAppletLogin(@RequestBody CustomerLoginRequestModel requestModel);

    /**
     * 小程序登录成功后绑定openId
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "小程序登录成功后绑定openId")
    @PostMapping(value = "/appletBindingOpenId")
    Result<Boolean> appletBindingOpenId(@RequestBody AppletBindingOpenIdRequestModel requestModel);

    /**
     * 小程序退出登录（清空openid）
     * @return
     */
    @ApiOperation(value = "小程序退出登录（清空openid）")
    @PostMapping(value = "/appletLoginOut")
    Result<Boolean> appletLoginOut();

    /**
     * 直接修改密码（需要验证手机验证码）
     * @param requestModel
     * @return
     */
    @ApiOperation("直接修改密码（需要验证手机验证码）")
    @PostMapping(value = "/updateAccountPassword")
    Result<Boolean> updateAccountPassword(@RequestBody UpdateAccountPasswordRequestModel requestModel);

    /**
     * 根据用户账号id修改密码
     * @param requestModel
     * @return
     */
    @ApiOperation("根据用户账号id修改密码")
    @PostMapping(value = "/modifyPassword")
    Result<Boolean> modifyPassword(@RequestBody ModifyPasswordRequestModel requestModel);

    /**
     * 更换手机号-验证手机号（前台车主、司机小程序）
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "更换手机号-验证手机号（前台车主、司机小程序）")
    @PostMapping(value = "/verifyPhone")
    Result<UpdatePhoneByVerifyPhoneResponseModel> verifyPhone(@RequestBody UpdatePhoneByVerifyPhoneRequestModel requestModel);

    /**
     * 更换手机号-更新手机号（前台车主、司机小程序）
     * @param requestModel
     * @return
     */
    @ApiOperation("更换手机号-更新手机号（前台车主、司机小程序）")
    @PostMapping(value = "/updatePhone")
    Result<Boolean> updatePhone(@RequestBody UpdatePhoneRequestModel requestModel);
}
