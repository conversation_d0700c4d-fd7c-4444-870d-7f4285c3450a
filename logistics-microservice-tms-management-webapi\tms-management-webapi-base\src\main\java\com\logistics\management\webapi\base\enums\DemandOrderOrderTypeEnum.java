package com.logistics.management.webapi.base.enums;

/**
 * @Author: sj
 * @Date: 2020/5/18 13:19
 */
public enum DemandOrderOrderTypeEnum {
    DEFAULT(0, ""),
    PUBLISH(10, "发布"),
    PULL(20, "拉取"),
    PUSH(21, "推送"),
    ;

    private Integer key;
    private String value;

    DemandOrderOrderTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static DemandOrderOrderTypeEnum getEnum(Integer key) {
        for (DemandOrderOrderTypeEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return DEFAULT;
    }
}
