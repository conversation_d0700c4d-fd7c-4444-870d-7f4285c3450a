package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum AmountSymbolEnum {

    DEFAULT(0, ""),
    GREATER_THAN_OR_EQUAL(1, ">="),
    EQUAL_TO(2, "="),
    LESS_THAN_OR_EQUAL(3, "<="),
    ;
    private final Integer key;
    private final String value;

    public static AmountSymbolEnum getEnum(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }

    public boolean compareTo(BigDecimal v1, BigDecimal v2) {
        switch (this) {
            case GREATER_THAN_OR_EQUAL:
                return v1.compareTo(v2) >= 0;
            case EQUAL_TO:
                return v1.compareTo(v2) == 0;
            case LESS_THAN_OR_EQUAL:
                return v1.compareTo(v2) <=0;
            default:
                return false;
        }
    }
}
