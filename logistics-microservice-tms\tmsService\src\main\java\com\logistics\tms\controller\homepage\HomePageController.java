package com.logistics.tms.controller.homepage;

import com.logistics.tms.biz.demandorder.DemandOrderForLeYiBiz;
import com.logistics.tms.biz.homepage.HomePageBiz;
import com.logistics.tms.controller.homepage.response.DemandOrderStatisticsResponseModel;
import com.logistics.tms.controller.homepage.response.HomeOrderCollectResponseModel;
import com.logistics.tms.controller.homepage.response.VerifyWarehousePermissionResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(value = "首页")
@RestController
public class HomePageController {

    @Resource
    private DemandOrderForLeYiBiz demandOrderForLeYiBiz;
    @Resource
    private HomePageBiz homePageBiz;

    @ApiOperation(value = "移动办公小程序首页-需求单统计")
    @PostMapping(value = "/service/workApplet/homePage/demandOrderStatistics")
    public Result<DemandOrderStatisticsResponseModel> demandOrderStatistics() {
        return Result.success(demandOrderForLeYiBiz.demandOrderStatistics());
    }

    /**
     * 小程序-首页单据汇总
     *
     * @return
     */
    @ApiOperation("小程序-首页单据汇总")
    @PostMapping(value = "/service/applet/homePage/homeOrderCollect")
    public Result<HomeOrderCollectResponseModel> homeOrderCollect() {
        return Result.success(homePageBiz.homeOrderCollect());
    }

    /**
     * 小程序-验证是否可以跳转云仓小程序
     *
     * @return
     */
    @ApiOperation(value = "小程序-验证是否可以跳转云仓小程序")
    @PostMapping(value = "/service/applet/homePage/verifyWarehousePermission")
    public Result<VerifyWarehousePermissionResponseModel> verifyWarehousePermission() {
        return Result.success(homePageBiz.verifyWarehousePermission());
    }
}
