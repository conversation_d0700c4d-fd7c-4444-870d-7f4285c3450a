package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/10/15 9:16
 */
public class ExportExcelVehicleSettlement {
    private ExportExcelVehicleSettlement(){}

    private static final Map<String, String> EXPORT_VEHICLE_SETTLEMENT;

    static {
        EXPORT_VEHICLE_SETTLEMENT = new LinkedHashMap<>();
        EXPORT_VEHICLE_SETTLEMENT.put("状态", "statusLabel");
        EXPORT_VEHICLE_SETTLEMENT.put("车辆机构", "vehiclePropertyLabel");
        EXPORT_VEHICLE_SETTLEMENT.put("车牌号", "vehicleNo");
        EXPORT_VEHICLE_SETTLEMENT.put("账单月", "settlementMonth");
        EXPORT_VEHICLE_SETTLEMENT.put("司机", "driverName");
        EXPORT_VEHICLE_SETTLEMENT.put("运单数量", "carrierOrderCount");
        EXPORT_VEHICLE_SETTLEMENT.put("运费(元)", "carrierFreight");
        EXPORT_VEHICLE_SETTLEMENT.put("调整费用(元)", "adjustFee");
        EXPORT_VEHICLE_SETTLEMENT.put("实付运费(元)", "actualExpensesPayable");
        EXPORT_VEHICLE_SETTLEMENT.put("月扣减合计(元)", "deductingFeeTotal");
        EXPORT_VEHICLE_SETTLEMENT.put("操作人", "lastModifiedBy");
        EXPORT_VEHICLE_SETTLEMENT.put("操作时间", "lastModifiedTime");
        EXPORT_VEHICLE_SETTLEMENT.put("备注", "remark");
    }

    public static Map<String, String> getExportVehicleSettlement() {
        return EXPORT_VEHICLE_SETTLEMENT;
    }
}
