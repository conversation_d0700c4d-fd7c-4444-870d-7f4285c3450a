package com.logistics.management.webapi.controller.uploadfile.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/5/8 16:56
 */
@Data
public class FileUploadForAIResponseDto {
    @ApiModelProperty("图片绝对路径")
    private String src="";
    @ApiModelProperty("图片相对路径")
    private String relativePath="";
    @ApiModelProperty("身份证头像面文字信息")
    private IDCardFrontResponseDto cardFront;
    @ApiModelProperty("身份证非头像面文字信息")
    private IDCardBackResponseDto cardBack;
    @ApiModelProperty("营业执照文字信息")
    private BusinessLicenseResponseDto businessLicense;
    @ApiModelProperty("车牌号文字信息")
    private PlateLicenseResponseDto plateLicense;
    @ApiModelProperty("驾驶证文字信息")
    private DrivingLicenseResponseDto drivingLicense;
    @ApiModelProperty("行驶证主页文字信息")
    private VehicleLicenseResponseDto vehicleLicense;
    @ApiModelProperty("行驶证副页文字信息")
    private VehicleLicenseBackResponseDto vehicleLicenseBack;
    @ApiModelProperty("银行卡文字信息")
    private BankCardResponseDto bankCard;
    @ApiModelProperty("护照文字信息")
    private PassPortResponseDto passPort;
    @ApiModelProperty("车牌文字信息")
    private TrainTicketResponseDto trainTicket;
}
