package com.logistics.management.webapi.controller.carrierorder;

import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.leyi.auth.service.client.common.AuthExceptionCodeEnum;
import com.leyi.auth.service.client.common.PlatformProdEnums;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.base.utils.ConversionUtils;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.base.utils.MurmurHashUtils;
import com.logistics.management.webapi.base.utils.WordBarCodeUtils;
import com.logistics.management.webapi.client.auth.AuthTokenServiceApi;
import com.logistics.management.webapi.client.auth.request.CreateToken;
import com.logistics.management.webapi.client.auth.response.TokenModule;
import com.logistics.management.webapi.client.carrierorder.CarrierOrderClient;
import com.logistics.management.webapi.client.carrierorder.request.*;
import com.logistics.management.webapi.client.carrierorder.response.*;
import com.logistics.management.webapi.client.thirdparty.basicdata.ocr.OCRClient;
import com.logistics.management.webapi.client.thirdparty.basicdata.ocr.response.OcrPictureRequestModel;
import com.logistics.management.webapi.controller.carrierorder.mapping.*;
import com.logistics.management.webapi.controller.carrierorder.request.*;
import com.logistics.management.webapi.controller.carrierorder.response.*;
import com.yelo.basicdata.api.feign.file.FileServiceApi;
import com.yelo.basicdata.api.feign.file.model.PdfWriteToImgRequestModel;
import com.yelo.basicdata.api.feign.file.model.PdfWriteToImgResponseModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.utils.RedisUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;
@Slf4j
@Api(value = "运单管理")
@RestController
public class CarrierOrderManagementController {

    @Resource
    private CarrierOrderClient carrierOrderClient;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private FileServiceApi fileServiceApi;
    @Resource
    private OCRClient ocrClient;
    @Resource
    private AuthTokenServiceApi authTokenServiceApi;
    @Resource
    private RedisUtils redisUtils;

    /**
     * 查询运单列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "查询运单列表", tags = "1.2.9")
    @PostMapping(value = "/api/carrierOrderManagement/searchList")
    public Result<PageInfo<SearchCarrierOrderResponseDto>> searchList(@RequestBody SearchCarrierOrderListRequestDto requestDto) {
        Result<PageInfo<SearchCarrierOrderListResponseModel>> pageInfoResult =  carrierOrderClient.searchList(MapperUtils.mapper(requestDto, SearchCarrierOrderListRequestModel.class));
        pageInfoResult.throwException();
        PageInfo pageInfo = pageInfoResult.getData();
        List<SearchCarrierOrderResponseDto> searchCarrierOrderListRequestDtos = MapperUtils.mapper(pageInfo.getList(),SearchCarrierOrderResponseDto.class,new CarrierOrderSearchListMapping());
        pageInfo.setList(searchCarrierOrderListRequestDtos);
        return Result.success(pageInfo);
    }

    /**
     * 待审核车辆信息
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "待审核车辆信息")
    @PostMapping(value = "/api/carrierOrderManagement/getWaitAuditVehicleCountInfo")
    public Result<WaitAuditVehicleInfoResponseDto> waitAuditVehicleCountInfo(@RequestBody WaitAuditVehicleInfoRequestDto requestDto) {
        Result<WaitAuditVehicleInfoResponseModel> result = carrierOrderClient.waitAuditVehicleCountInfo(MapperUtils.mapperNoDefault(requestDto, WaitAuditVehicleInfoRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),WaitAuditVehicleInfoResponseDto.class));
    }

    /**
     * 取消运单
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "取消运单")
    @PostMapping(value = "/api/carrierOrderManagement/cancelCarrierOrder")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> cancelCarrierOrder(@RequestBody CancelCarrierOrderRequestDto requestDto) {
        CancelCarrierOrderRequestModel requestModel = MapperUtils.mapper(requestDto, CancelCarrierOrderRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        return carrierOrderClient.cancelCarrierOrder(requestModel);
    }

    /**
     * 签收详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "签收详情")
    @PostMapping(value = "/api/carrierOrderManagement/carrierOrderListBeforeSignUp")
    public Result<CarrierOrderBeforeSignUpResponseDto> carrierOrderListBeforeSignUp(@RequestBody @Valid CarrierOrderListBeforeSignUpRequestDto requestDto) {
        Result<CarrierOrderBeforeSignUpResponseModel> result = carrierOrderClient.carrierOrderListBeforeSignUp(MapperUtils.mapper(requestDto,CarrierOrderListBeforeSignUpRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),CarrierOrderBeforeSignUpResponseDto.class,new CarrierOrderListBeforeSignUpMapping()));
    }

    /**
     * 签收
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "签收")
    @PostMapping(value = "/api/carrierOrderManagement/carrierOrderSignUp")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result carrierOrderSignUp(@RequestBody @Valid CarrierOrderConfirmSignUpRequestDto requestDto) {
        return carrierOrderClient.carrierOrderSignUp(MapperUtils.mapperNoDefault(requestDto,CarrierOrderConfirmSignUpRequestModel.class));
    }

    /**
     * 运单详情页
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "运单详情页v1.1.9")
    @PostMapping(value = "/api/carrierOrderManagement/carrierOrderDetail")
    public Result<CarrierOrderDetailResponseDto> carrierOrderDetail(@RequestBody @Valid CarrierOrderIdRequestDto requestDto) {
        Result<CarrierOrderDetailResponseModel> result = carrierOrderClient.carrierOrderDetail(MapperUtils.mapper(requestDto, CarrierOrderDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), CarrierOrderDetailResponseDto.class, new CarrierOrderDetailMapping()));
    }

    /**
     * 审核/驳回修改车辆
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "审核/驳回车辆")
    @PostMapping(value = "/api/carrierOrderManagement/auditOrRejectVehicle")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result auditOrRejectVehicle(@RequestBody @Valid AuditOrRejectVehicleRequestDto requestDto) {
        AuditOrRejectVehicleRequestModel model = MapperUtils.mapper(requestDto,AuditOrRejectVehicleRequestModel.class);
        model.setAuditSource(CommonConstant.INTEGER_ONE);
        return carrierOrderClient.auditOrRejectVehicle(model);
    }

    /**
     * 下载签收单
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "下载签收单")
    @GetMapping(value = "/api/carrierOrderManagement/downloadLadingBill")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void downloadLadingBill(@Valid DownloadLadingBillRequestDto requestDto,HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        GetPdfPathForBillDto getPdfPathForBillDto = getPdfPathForBill(requestDto,httpServletRequest);
        String pdfPath = getPdfPathForBillDto.getPdfPath();
        String wordPath = getPdfPathForBillDto.getWordPath();
        try (InputStream is = new FileInputStream(new File(pdfPath))) {
            commonBiz.downLoadFile(getPdfPathForBillDto.getFileName(), "pdf", is, httpServletResponse);
        } catch (Exception e) {
            log.info("downLoad pick up bill pdf File error", e);
        } finally {
            File wordFile = new File(wordPath);
            if (wordFile.isFile() && wordFile.exists()) {
                if (!wordFile.delete()) {
                    log.info("下载提货单 " + wordPath + " 删除失败 ");
                }
            }
            File pdfFile = new File(pdfPath);
            if (pdfFile.isFile() && pdfFile.exists()) {
                if (!pdfFile.delete()) {
                    log.info("下载提货单 " + pdfPath + " 删除失败 ");
                }
            }
        }
    }

    /**
     * 上传票据
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "上传票据")
    @PostMapping(value = "/api/carrierOrderManagement/uploadTickets")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result uploadTickets(@RequestBody UploadTicketsRequestDto requestDto) {
        return carrierOrderClient.uploadTickets(MapperUtils.mapper(requestDto,UploadTicketsRequestModel.class));
    }

    /**
     * 查询票据
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "查询票据",tags = "1.2.4")
    @PostMapping(value = "/api/carrierOrderManagement/getTickets")
    public Result<List<TicketsDto>> getTickets(@RequestBody GetTicketsRequestDto requestDto) {
        Result<List<GetTicketsResponseModel>> result = carrierOrderClient.getTickets(MapperUtils.mapper(requestDto,GetTicketsRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (GetTicketsResponseModel model : result.getData()) {
            sourceSrcList.add(model.getImagePath());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(),TicketsDto.class,new CarrierOrderTicketsMapping(configKeyConstant.fileAccessAddress,imageMap)));
    }

    /**
     * 运单日志
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "运单日志")
    @PostMapping(value = "/api/carrierOrderManagement/getCarrierOrderLogs")
    public Result<List<GetCarrierOrderLogsResponseDto>> getCarrierOrderLogs(@RequestBody @Valid CarrierOrderIdRequestDto requestDto) {
        Result<List<GetCarrierOrderLogsResponseModel>> result = carrierOrderClient.getCarrierOrderLogs(MapperUtils.mapper(requestDto, CarrierOrderDetailRequestModel.class));
        result.throwException();
        List<GetCarrierOrderLogsResponseDto> list = new ArrayList<>();
        if (ListUtils.isNotEmpty(result.getData())){
            list = MapperUtils.mapper(result.getData(),GetCarrierOrderLogsResponseDto.class, new CarrierOrderLogsMapping());
        }
        return Result.success(list);
    }

    /**
     * 导出运单
     * @param requestDto
     * @param response
     * @return
     */
    @ApiOperation(value = "导出运单 v1.2.3")
    @PostMapping(value = "/api/carrierOrderManagement/exportCarrierOrder")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportCarrierOrder(@RequestBody SearchCarrierOrderListRequestDto requestDto, HttpServletResponse response) {
        Result<List<SearchCarrierOrderListResponseModel>> result = carrierOrderClient.exportCarrierOrder(MapperUtils.mapper(requestDto,SearchCarrierOrderListRequestModel.class));
        result.throwException();

        List<SearchCarrierOrderResponseDto> list = MapperUtils.mapper(result.getData(), SearchCarrierOrderResponseDto.class, new CarrierOrderSearchListMapping());
        String fileName = "运单列表" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM);
        try {
            YeloExcelUtils.doExportMoreThreadByOneSheet(response, list, false, SearchCarrierOrderResponseDto.class, fileName);
        } catch (Exception e) {
            return;
        }
    }

    /**
     * 删除票据
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "删除票据")
    @PostMapping(value = "/api/carrierOrderManagement/delTickets")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result delTickets(@RequestBody DeleteTicketsRequestDto requestDto) {
        return carrierOrderClient.delTickets(MapperUtils.mapper(requestDto,DeleteTicketsRequestModel.class));
    }

    /**
     * 获取微信推送人
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "获取微信推送人")
    @PostMapping(value = "/api/carrierOrderManagement/getWeixinPushInfo")
    public Result<List<GetCarrierOrderWeixinPushResponseDto>> getWeixinPushInfo( @RequestBody  GetCarrierOrderWeixinPushRequestDto requestDto) {
        Result<List<GetCarrierOrderWeixinPushResponseModel>> result = carrierOrderClient.getWeixinPushInfo(MapperUtils.mapper(requestDto,GetCarrierOrderWeixinPushRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),GetCarrierOrderWeixinPushResponseDto.class));
    }

    /**
     * 确定推送微信消息
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "确定推送微信消息")
    @PostMapping(value = "/api/carrierOrderManagement/confirmPushWeixin")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result confirmPushWeixin(@RequestBody ConfirmPushWeixinRequestDto requestDto) {
        return carrierOrderClient.confirmPushWeixin(MapperUtils.mapper(requestDto,ConfirmPushWeixinRequestModel.class));
    }

    /**
     * 已提货
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "已提货")
    @PostMapping(value = "/api/carrierOrderManagement/load")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result load(@RequestBody @Valid List<LoadRequestDto> requestDto) {
        CarrierOrderLoadRequestModel requestModel = new CarrierOrderLoadRequestModel();
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        requestModel.setLoadCarrierOrderList(MapperUtils.mapper(requestDto, LoadRequestModel.class,new LoadRequestMapping()));
        Result<Boolean> result = carrierOrderClient.load(requestModel);
        result.throwException();
        return Result.success(true);
    }

    /**
     * 到达提货地
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "到达提货地")
    @PostMapping(value = "/api/carrierOrderManagement/reachLoad")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result reachLoad(@RequestBody ReachLoadRequestDto requestDto) {
        ReachLoadAddressRequestModel model = MapperUtils.mapper(requestDto,ReachLoadAddressRequestModel.class);
        model.setSource(CommonConstant.INTEGER_ONE);
        return carrierOrderClient.reachLoadAddress(model);
    }

    /**
     * 已卸货
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "已卸货")
    @PostMapping(value = "/api/carrierOrderManagement/unload")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result unload(@RequestBody @Valid List<UnloadRequestDto> requestDto) {
        CarrierOrderUnLoadRequestModel requestModel = new CarrierOrderUnLoadRequestModel();
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        requestModel.setUnloadCarrierOrderList(MapperUtils.mapperNoDefault(requestDto, UnloadRequestModel.class));
        Result<Boolean> result = carrierOrderClient.unload(requestModel);
        result.throwException();
        return Result.success(true);
    }

    /**
     * 到达卸货地
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "到达卸货地")
    @PostMapping(value = "/api/carrierOrderManagement/reachUnload")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result reachUnload(@RequestBody ReachUnloadRequestDto requestDto) {
        ReachUnloadAddressRequestModel model = MapperUtils.mapper(requestDto,ReachUnloadAddressRequestModel.class);
        model.setSource(CommonConstant.INTEGER_ONE);
        return carrierOrderClient.reachUnloadAddress(model);
    }

    /**
     * 修改司机运费（订单详情页面）
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "修改司机运费（订单详情页面）")
    @PostMapping(value = "/api/carrierOrderManagement/updateDriverFreightFee")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result updateDriverFreightFee(@RequestBody @Valid UpdateDriverFreightFeeRequestDto requestDto) {
        return carrierOrderClient.updateDriverFreightFee(MapperUtils.mapper(requestDto,UpdateDriverFreightFeeRequestModel.class));
    }

    /**
     * 查询提卸货详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "查询提卸货详情")
    @PostMapping(value = "/api/carrierOrderManagement/getLoadDetail")
    public Result<List<LoadDetailResponseDto>> getLoadDetail(@RequestBody @Valid LoadDetailRequestDto requestDto) {
        Result<List<LoadDetailResponseModel>>  result = carrierOrderClient.getLoadDetail(MapperUtils.mapper(requestDto,LoadDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),LoadDetailResponseDto.class,new GetLoadDetailMapping()));
    }

    /**
     * 导出运单回单
     * @param requestDto
     * @param response
     */
    @ApiOperation(value = "导出运单回单v1.0.6")
    @GetMapping(value = "/api/carrierOrderManagement/exportCarrierOrderTickets")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportCarrierOrderTickets(@Valid ExportCarrierOrderTicketsRequestDto requestDto, HttpServletResponse response) {
        Result<ExportCarrierOrderTicketsResponseModel> result = carrierOrderClient.exportCarrierOrderTickets(MapperUtils.mapper(requestDto, ExportCarrierOrderTicketsRequestModel.class));
        result.throwException();
        if (result.getData() != null && result.getData().getFileByte() != null){
            commonBiz.downLoadFile(CommonConstant.EXPORT_TICKETS_NAME,"zip",result.getData().getFileByte(), response);
        }
    }

    /**
     * 导出运单回单前校验
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "导出运单回单前校验接口")
    @PostMapping(value = "/api/carrierOrderManagement/checkExportCarrierOrderTickets")
    public Result<Boolean> checkExportCarrierOrderTickets(@RequestBody @Valid ExportCarrierOrderTicketsRequestDto requestDto) {
        Result<Boolean> result = carrierOrderClient.checkExportCarrierOrderTickets(MapperUtils.mapper(requestDto, ExportCarrierOrderTicketsRequestModel.class));
        result.throwException();
        return Result.success(true);
    }


    /**
     * 查看运单签收单3
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "查看运单签收单3 v2.47")
    @PostMapping(value = "/api/carrierOrderManagement/reviewSignTickets3")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<ReviewSignTicketsResponseDto> reviewSignTickets3(@RequestBody @Valid DownloadLadingBillRequestDto requestDto, HttpServletRequest httpServletRequest){
        GetPdfPathForBillDto getPdfPathForBillDto = getPdfPathForBill3(requestDto,httpServletRequest);
        String pdfPath = getPdfPathForBillDto.getPdfPath();
        String wordPath = getPdfPathForBillDto.getWordPath();
        List<String> signTicketsPath = new ArrayList<>();
        try (InputStream is = new FileInputStream(new File(pdfPath)); ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = is.read(buffer)) != -1){
                bos.write(buffer, 0, len);
            }
            PdfWriteToImgRequestModel pdfWriteToImgRequestModel = new PdfWriteToImgRequestModel();
            pdfWriteToImgRequestModel.setBytes(bos.toByteArray());
            pdfWriteToImgRequestModel.setPath(getPdfPathForBillDto.getCarrierOrderCode());
            pdfWriteToImgRequestModel.setDpi(150);//图片清晰度
            Result<PdfWriteToImgResponseModel> imgResult = fileServiceApi.pdfWriteToImgOSS(pdfWriteToImgRequestModel);
            imgResult.throwException();
            List<String> ticketsPathList = imgResult.getData().getImgPathList();
            for (String path : ticketsPathList) {
                signTicketsPath.add(configKeyConstant.fileAccessAddressTemp + commonBiz.getImageURL(path.substring(path.lastIndexOf("/"))));
            }
        } catch (Exception e) {
            log.info("downLoad pick up bill pdf File error", e);
        } finally {
            File wordFile = new File(wordPath);
            if (wordFile.isFile() && wordFile.exists()) {
                if (!wordFile.delete()) {
                    log.info("下载提货单 " + wordPath + " 删除失败 ");
                }
            }
            File pdfFile = new File(pdfPath);
            if (pdfFile.isFile() && pdfFile.exists()) {
                if (!pdfFile.delete()) {
                    log.info("下载提货单 " + pdfPath + " 删除失败 ");
                }
            }
        }
        ReviewSignTicketsResponseDto responseDto = new ReviewSignTicketsResponseDto();
        responseDto.setSignTicketsPath(signTicketsPath);
        return Result.success(responseDto);
    }


    //获取运单签收单pdf、word全路径（下载签收单、查看签收单）
    public GetPdfPathForBillDto getPdfPathForBill3(DownloadLadingBillRequestDto requestDto, HttpServletRequest httpServletRequest) {
        Result<DownloadLadingBillResponseModel> result = carrierOrderClient.downloadLadingBill(MapperUtils.mapper(requestDto, com.logistics.management.webapi.client.carrierorder.request.CarrierOrderDetailRequestModel.class));
        result.throwException();
        DownloadLadingBillResponseDto responseDto = MapperUtils.mapper(result.getData(), DownloadLadingBillResponseDto.class, new DownloadLadingBillMapping());
        if (!(EntrustTypeEnum.RECYCLE_IN.getKey().toString().equals(responseDto.getEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().toString().equals(responseDto.getEntrustType()))) {
            throw new BizException(ManagementWebApiExceptionEnum.SIGN_UP_BILL_3_ONLY_FOR_YR);
        }

        String tmpFileName;
        List<String[]> tableItems = new ArrayList<>();
        Map<String, String> templateValues = new HashMap<>();
        Map<String, InputStream> picMap = new HashMap<>();
        //要填充的货物数据
        List<Map<String, List<String[]>>> tableInsertItems = new ArrayList<>();
        templateValues.put("carrierOrderCode", responseDto.getCarrierOrderCode());
        templateValues.put("expectedLoadTime", responseDto.getExpectedLoadTime());
        templateValues.put("companyCarrierName", responseDto.getCompanyCarrierName());
        templateValues.put("demandOrderCode", responseDto.getDemandOrderCode());
        templateValues.put("driverIdentityNumber", responseDto.getDriverIdentityNumber());
        templateValues.put("driverName", Optional.ofNullable(responseDto.getDriverName()).orElse("") + " " + Optional.ofNullable(responseDto.getDriverMobile()).orElse(""));
        templateValues.put("vehicleNo", responseDto.getVehicleNo());
        templateValues.put("remark", responseDto.getRemark());
        templateValues.put("upstreamCustomer", responseDto.getLoadWarehouse());
        templateValues.put("consignorName", Optional.ofNullable(responseDto.getConsignorName()).orElse("") + " " + Optional.ofNullable(responseDto.getConsignorMobile()).orElse(""));
        templateValues.put("loadDetailAddress", responseDto.getLoadDetailAddress());
        templateValues.put("expectAmount", responseDto.getExpectAmount());
        templateValues.put("unloadWarehouse", responseDto.getUnloadWarehouse());
        templateValues.put("receiverName",Optional.ofNullable(responseDto.getReceiverName()).orElse("") + " " + Optional.ofNullable(responseDto.getReceiverMobile()).orElse(""));
        templateValues.put("unloadDetailAddress", responseDto.getUnloadDetailAddress());
        templateValues.put("publisher", Optional.ofNullable(responseDto.getPublishName()).orElse(CommonConstant.BLANK_TEXT) + Optional.ofNullable(responseDto.getPublishMobile()).orElse(CommonConstant.BLANK_TEXT));
        //获取二维码图片
        byte[] qrCodePicByte = result.getData().getQrCodePicByte();
        //获取二维码图片流
        String qrCodePicPath = responseDto.getQrCodePicPath();
        if (qrCodePicByte == null && StringUtils.isNotBlank(qrCodePicPath)) {
            qrCodePicByte = commonBiz.getOssFileByte(configKeyConstant.imageUploadCatalog + qrCodePicPath);
        }
        ByteArrayInputStream qrCodeInputStream = null;
        if (qrCodePicByte != null) {
            qrCodeInputStream = new ByteArrayInputStream(qrCodePicByte);
        }
        //需要重新生成二维码
        if ((CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey().equals(responseDto.getStatus())
                || CarrierOrderStatusEnum.WAIT_LOAD.getKey().equals(responseDto.getStatus())
                || CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey().equals(responseDto.getStatus())
                || CarrierOrderStatusEnum.WAIT_UNLOAD.getKey().equals(responseDto.getStatus()))
                && "0".equals(responseDto.getIfCancel())
                && "0".equals(responseDto.getIfEmpty())) {
            //重新构建二维码
            //二维码参数
            Map<String, Object> qrCodeParamsMap = DriverAppletQrCodeJumpPageEnum.CARRIER_ORDER_DETAIL_PAGE.getParamsMap();
            qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_CODE.getKey(), responseDto.getCarrierOrderCode());
            qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_BILL_TYPE.getKey(), QrCodeParamsBillTypeEnum.RECYCLE.getKey());
            String key = (String) redisUtils.get(CommonConstant.KEY_TOKEN_REDIS + responseDto.getCarrierOrderCode());
            if (StringUtils.isEmpty(key)) {
                Long hashCodeUnsigned = MurmurHashUtils.hash128Unsigned(JSONObject.toJSONString(responseDto) + CommonConstant.SHORT_URL_HASH_COLLISION_SUFFIX);
                // 对哈希值进行62进制转换
                key = ConversionUtils.base62(hashCodeUnsigned);
                redisUtils.getRedisTemplate().opsForValue().set(CommonConstant.KEY_TOKEN_REDIS + responseDto.getCarrierOrderCode(), key);
            }
            qrCodeParamsMap.put("f", "1");
            qrCodeParamsMap.put("k", key);
            qrCodePicByte = commonBiz.createQrCode(configKeyConstant.qrcodeCommonPrefix, qrCodeParamsMap).getFileByte();
            qrCodeInputStream = new ByteArrayInputStream(qrCodePicByte);

        }
        //回收类型
        if (qrCodeInputStream != null) {
            picMap.put(CommonConstant.PIC_RID_R, qrCodeInputStream);
        }
        tmpFileName = "/template/sign_bill_3.docx";

        String pdfBasePath = "/tmp/htmlfont";//临时保存word和pdf的文件夹
        commonBiz.dirIfExist(pdfBasePath);
        String tempBasePath = pdfBasePath + "/";
        String fileName = "云途签收单3" + responseDto.getCarrierOrderCode();
        String filePath = tempBasePath + fileName;
        String wordPath = filePath + ".docx";//word文档全路径
        //替换word模板内容生成新word文档保存到指定的文件夹
        InputStream inputStream = CarrierOrderManagementController.class.getResourceAsStream(tmpFileName);
        WordBarCodeUtils.fillWordTemplateValues(inputStream, wordPath, templateValues, picMap, tableItems, tableInsertItems);
        //将生成的word文档转换为pdf，保存在同一个路径下
        WordToPdfUtils.wordConverterToPdf(filePath);

        GetPdfPathForBillDto getPdfPathForBillDto = new GetPdfPathForBillDto();
        getPdfPathForBillDto.setCarrierOrderCode(responseDto.getCarrierOrderCode());
        getPdfPathForBillDto.setPdfPath(filePath + ".pdf");
        getPdfPathForBillDto.setWordPath(wordPath);
        getPdfPathForBillDto.setFileName(fileName);
        return getPdfPathForBillDto;
    }





    /**
     * 查看运单签收单
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "查看运单签收单 v2.45")
    @PostMapping(value = "/api/carrierOrderManagement/reviewSignTickets")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<ReviewSignTicketsResponseDto> reviewSignTickets(@RequestBody @Valid DownloadLadingBillRequestDto requestDto, HttpServletRequest httpServletRequest) {
        GetPdfPathForBillDto getPdfPathForBillDto = getPdfPathForBill(requestDto,httpServletRequest);
        String pdfPath = getPdfPathForBillDto.getPdfPath();
        String wordPath = getPdfPathForBillDto.getWordPath();
        List<String> signTicketsPath = new ArrayList<>();
        try (InputStream is = new FileInputStream(new File(pdfPath)); ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = is.read(buffer)) != -1){
                bos.write(buffer, 0, len);
            }
            PdfWriteToImgRequestModel pdfWriteToImgRequestModel = new PdfWriteToImgRequestModel();
            pdfWriteToImgRequestModel.setBytes(bos.toByteArray());
            pdfWriteToImgRequestModel.setPath(getPdfPathForBillDto.getCarrierOrderCode());
            pdfWriteToImgRequestModel.setDpi(150);//图片清晰度
            Result<PdfWriteToImgResponseModel> imgResult = fileServiceApi.pdfWriteToImgOSS(pdfWriteToImgRequestModel);
            imgResult.throwException();
            List<String> ticketsPathList = imgResult.getData().getImgPathList();
            for (String path : ticketsPathList) {
                signTicketsPath.add(configKeyConstant.fileAccessAddressTemp + commonBiz.getImageURL(path.substring(path.lastIndexOf("/"))));
            }
        } catch (Exception e) {
            log.info("downLoad pick up bill pdf File error", e);
        } finally {
            File wordFile = new File(wordPath);
            if (wordFile.isFile() && wordFile.exists()) {
                if (!wordFile.delete()) {
                    log.info("下载提货单 " + wordPath + " 删除失败 ");
                }
            }
            File pdfFile = new File(pdfPath);
            if (pdfFile.isFile() && pdfFile.exists()) {
                if (!pdfFile.delete()) {
                    log.info("下载提货单 " + pdfPath + " 删除失败 ");
                }
            }
        }
        ReviewSignTicketsResponseDto responseDto = new ReviewSignTicketsResponseDto();
        responseDto.setSignTicketsPath(signTicketsPath);
        return Result.success(responseDto);
    }

    //获取运单签收单pdf、word全路径（下载签收单、查看签收单）
    public GetPdfPathForBillDto getPdfPathForBill(DownloadLadingBillRequestDto requestDto ,HttpServletRequest httpServletRequest) {
        Result<DownloadLadingBillResponseModel> result = carrierOrderClient.downloadLadingBill(MapperUtils.mapper(requestDto, com.logistics.management.webapi.client.carrierorder.request.CarrierOrderDetailRequestModel.class));
        result.throwException();
        DownloadLadingBillResponseDto responseDto = MapperUtils.mapper(result.getData(), DownloadLadingBillResponseDto.class, new DownloadLadingBillMapping());

        String tmpFileName;
        List<String[]> tableItems = new ArrayList<>();
        Map<String, String> templateValues = new HashMap<>();
        Map<String, InputStream> picMap = new HashMap<>();
        //要填充的货物数据
        List<Map<String, List<String[]>>> tableInsertItems = new ArrayList<>();
        templateValues.put("carrierOrderCode", responseDto.getCarrierOrderCode());
        templateValues.put("vehicleNumber", responseDto.getVehicleNo());
        templateValues.put("driverName", responseDto.getDriverName());
        templateValues.put("driverIdCardNum", responseDto.getDriverIdentityNumber());
        templateValues.put("driverPhone", responseDto.getDriverMobile());
        templateValues.put("driver", Optional.ofNullable(responseDto.getDriverName()).orElse("") + " " + Optional.ofNullable(responseDto.getDriverMobile()).orElse(""));
        templateValues.put("pickUpWarehouseAddress", responseDto.getLoadDetailAddress());
        templateValues.put("pickUpWarehouse", responseDto.getLoadWarehouse());
        templateValues.put("receiptsWarehouseAddress", responseDto.getUnloadDetailAddress());
        templateValues.put("receiptsWarehouse", responseDto.getUnloadWarehouse());
        templateValues.put("pickUpExpectedTime", responseDto.getExpectedLoadTime());
        templateValues.put("receiptsExpectedTime", responseDto.getExpectedUnloadTime());
        templateValues.put("companyEntrustName", responseDto.getCompanyEntrustName());
        templateValues.put("dispatchTime", responseDto.getDispatchTime());

        if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(responseDto.getDemandOrderSource())) {
            templateValues.put("goodsName", responseDto.getGoodsName());
            templateValues.put("consignor", Optional.ofNullable(responseDto.getConsignorName()).orElse("") + " " + Optional.ofNullable(responseDto.getConsignorMobile()).orElse(""));
            templateValues.put("receiver", Optional.ofNullable(responseDto.getReceiverName()).orElse("") + " " + Optional.ofNullable(responseDto.getReceiverMobile()).orElse(""));
            templateValues.put("goodsUnit", responseDto.getGoodsUnit());
            templateValues.put("demandOrderCode", responseDto.getDemandOrderCode());
            templateValues.put("receiverMobile", responseDto.getReceiverMobile());
            if (responseDto.getStatus() > CarrierOrderStatusEnum.WAIT_LOAD.getKey() && responseDto.getStatus() < CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey()) {
                templateValues.put("loadAmount", responseDto.getLoadAmount());
                templateValues.put("amountDiff", responseDto.getAmountDiff());
            } else if (CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(responseDto.getStatus())) {
                templateValues.put("loadAmount", responseDto.getLoadAmount());
                templateValues.put("signAmount", responseDto.getSignAmount());
                templateValues.put("amountDiff", responseDto.getAmountDiff());
            }

            //获取二维码图片
            byte[] qrCodePicByte = result.getData().getQrCodePicByte();
            //获取二维码图片流
            String qrCodePicPath = responseDto.getQrCodePicPath();
            if (qrCodePicByte == null && StringUtils.isNotBlank(qrCodePicPath)){
                qrCodePicByte = commonBiz.getOssFileByte(configKeyConstant.imageUploadCatalog + qrCodePicPath);
            }
            ByteArrayInputStream qrCodeInputStream = null;
            if (qrCodePicByte != null) {
                qrCodeInputStream = new ByteArrayInputStream(qrCodePicByte);
            }
            //需要重新生成二维码
            if ((CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey().equals(responseDto.getStatus())
                    || CarrierOrderStatusEnum.WAIT_LOAD.getKey().equals(responseDto.getStatus())
                    || CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey().equals(responseDto.getStatus())
                    || CarrierOrderStatusEnum.WAIT_UNLOAD.getKey().equals(responseDto.getStatus()))
                    && "0".equals(responseDto.getIfCancel())
                    && "0".equals(responseDto.getIfEmpty())) {
                //重新构建二维码
                //二维码参数
                Map<String, Object> qrCodeParamsMap = DriverAppletQrCodeJumpPageEnum.CARRIER_ORDER_DETAIL_PAGE.getParamsMap();
                qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_CODE.getKey(), responseDto.getCarrierOrderCode());
                if (EntrustTypeEnum.RECYCLE_IN.getKey().toString().equals(responseDto.getEntrustType())
                        || EntrustTypeEnum.RECYCLE_OUT.getKey().toString().equals(responseDto.getEntrustType())) {
                    qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_BILL_TYPE.getKey(), QrCodeParamsBillTypeEnum.RECYCLE.getKey());
                } else {
                    qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_BILL_TYPE.getKey(), QrCodeParamsBillTypeEnum.OTHER.getKey());
                }
                String key = (String)redisUtils.get(CommonConstant.KEY_TOKEN_REDIS+responseDto.getCarrierOrderCode());
                if (StringUtils.isEmpty(key)) {
                    Long hashCodeUnsigned = MurmurHashUtils.hash128Unsigned(JSONObject.toJSONString(responseDto) + CommonConstant.SHORT_URL_HASH_COLLISION_SUFFIX);
                    // 对哈希值进行62进制转换
                    key = ConversionUtils.base62(hashCodeUnsigned);
                    redisUtils.getRedisTemplate().opsForValue().set(CommonConstant.KEY_TOKEN_REDIS + responseDto.getCarrierOrderCode(), key);
                }
                qrCodeParamsMap.put("f", "1");
                qrCodeParamsMap.put("k", key);
                qrCodePicByte = commonBiz.createQrCode(configKeyConstant.qrcodeCommonPrefix, qrCodeParamsMap).getFileByte();
                qrCodeInputStream = new ByteArrayInputStream(qrCodePicByte);

            }

            if (!EntrustTypeEnum.RECYCLE_IN.getKey().toString().equals(responseDto.getEntrustType())
                    && !EntrustTypeEnum.RECYCLE_OUT.getKey().toString().equals(responseDto.getEntrustType())) {
                //非回收类型
                if (qrCodeInputStream != null) {
                    picMap.put(CommonConstant.PIC_RID, qrCodeInputStream);
                }

                templateValues.put("unloadCompany", responseDto.getUnloadCompany());
                templateValues.put("entrustType", responseDto.getEntrustTypeLabel());
                templateValues.put("loadTime", responseDto.getLoadTime());
                templateValues.put("loadAmount", BigDecimal.ZERO.compareTo(new BigDecimal(responseDto.getLoadAmount())) < CommonConstant.INTEGER_ZERO ? responseDto.getLoadAmount() : CommonConstant.BLANK_TEXT);

                List<DownloadLadingBillGoodsResponseDto> goodsInfoList = responseDto.getGoodsInfoList();
                if (goodsInfoList == null) {
                    goodsInfoList = new ArrayList<>();
                }
                if (ListUtils.isNotEmpty(goodsInfoList)) {
                    for (int i = 0; i < goodsInfoList.size(); i++) {
                        DownloadLadingBillGoodsResponseDto dto = goodsInfoList.get(i);
                        String loadAmount = CommonConstant.BLANK_TEXT;
                        if (responseDto.getStatus() > CarrierOrderStatusEnum.WAIT_LOAD.getKey()) {
                            loadAmount = dto.getLoadAmount();
                        }
                        if (String.valueOf(EntrustTypeEnum.DELIVER.getKey()).equals(responseDto.getEntrustType())) {
                            tableItems.add(new String[]{ConverterUtils.toString(i + 1),
                                    StringUtils.isNotEmpty(dto.getCategoryName()) ? dto.getCategoryName() : dto.getGoodsName(),
                                    dto.getGoodsName(), dto.getExpectAmount(), loadAmount, ""});
                        } else {
                            tableItems.add(new String[]{ConverterUtils.toString(i + 1), dto.getGoodsName(), dto.getGoodsSize(), dto.getExpectAmount(), loadAmount, ""});
                        }
                    }
                }
                tmpFileName = "/template/stock_out_for_leyi_template.docx";
            }else {
                //回收类型
                if (qrCodeInputStream != null) {
                    picMap.put(CommonConstant.PIC_RID_R, qrCodeInputStream);
                }

                //提货货物
                Map<String, List<String[]>> tableValueLoadMap = new HashMap<>();
                List<String[]> tableCellValueLoadList = new ArrayList<>();
                tableValueLoadMap.put(CommonConstant.TABLE_KEYWORD_1, tableCellValueLoadList);

                //卸货货物
                Map<String, List<String[]>> tableValueUnloadMap = new HashMap<>();
                List<String[]> tableCellValueUnloadList = new ArrayList<>();
                tableValueUnloadMap.put(CommonConstant.TABLE_KEYWORD_2, tableCellValueUnloadList);

                tableInsertItems.add(tableValueLoadMap);
                tableInsertItems.add(tableValueUnloadMap);
                for (DownloadLadingBillGoodsResponseDto goodsDtoItem : responseDto.getGoodsInfoList()) {
                    tableCellValueLoadList.add(new String[]{CommonConstant.BLANK_TEXT, goodsDtoItem.getGoodsName(), goodsDtoItem.getExpectAmount(), goodsDtoItem.getLoadAmount(), goodsDtoItem.getLoadDiffAmount()});
                    tableCellValueUnloadList.add(new String[]{CommonConstant.BLANK_TEXT, goodsDtoItem.getGoodsName(), goodsDtoItem.getLoadAmount(), goodsDtoItem.getUnloadAmount(), goodsDtoItem.getUnloadDiffAmount()});
                }

                templateValues.put("remark", (StringUtils.isNotBlank(responseDto.getRemark()) ? responseDto.getRemark() + CommonConstant.SEMICOLON : CommonConstant.BLANK_TEXT) + responseDto.getOtherRequirements());
                templateValues.put("pickUpExpectedTime", responseDto.getRecycleExpectedLoadTime());
                templateValues.put("publisher", Optional.ofNullable(responseDto.getPublishName()).orElse(CommonConstant.BLANK_TEXT) + Optional.ofNullable(responseDto.getPublishMobile()).orElse(CommonConstant.BLANK_TEXT));

                tmpFileName = "/template/pick_up_goods_bill_for_leyi.docx";
            }
        }else {
            templateValues.put("pickUpPerson", responseDto.getConsignorName());
            templateValues.put("pickUpPhone", responseDto.getConsignorMobile());
            templateValues.put("receiptsPerson", responseDto.getReceiverName());
            templateValues.put("receiptsPhone", responseDto.getReceiverMobile());
            templateValues.put("totalCount", responseDto.getTotalCount());
            templateValues.put("volume", responseDto.getTotalVolume());

            List<DownloadLadingBillGoodsResponseDto> goodsInfoList = responseDto.getGoodsInfoList();
            if (ListUtils.isNotEmpty(goodsInfoList)) {
                for (int i = 0; i < goodsInfoList.size(); i++) {
                    DownloadLadingBillGoodsResponseDto dto = goodsInfoList.get(i);
                    tableItems.add(new String[]{ConverterUtils.toString(i + 1), dto.getGoodsName(), dto.getGoodsSize(), dto.getExpectAmount(), dto.getVolume()});
                }
            }
            tmpFileName = "/template/pick_up_goods_bill.docx";
        }
        String pdfBasePath = "/tmp/htmlfont";//临时保存word和pdf的文件夹
        commonBiz.dirIfExist(pdfBasePath);
        String tempBasePath = pdfBasePath + "/";
        String fileName = "云途签收单" + responseDto.getCarrierOrderCode();
        String filePath = tempBasePath + fileName;
        String wordPath = filePath + ".docx";//word文档全路径
        //替换word模板内容生成新word文档保存到指定的文件夹
        InputStream inputStream = CarrierOrderManagementController.class.getResourceAsStream(tmpFileName);
        WordBarCodeUtils.fillWordTemplateValues(inputStream, wordPath, templateValues, picMap, tableItems, tableInsertItems);
        //将生成的word文档转换为pdf，保存在同一个路径下
        WordToPdfUtils.wordConverterToPdf(filePath);

        GetPdfPathForBillDto getPdfPathForBillDto = new GetPdfPathForBillDto();
        getPdfPathForBillDto.setCarrierOrderCode(responseDto.getCarrierOrderCode());
        getPdfPathForBillDto.setPdfPath(filePath + ".pdf");
        getPdfPathForBillDto.setWordPath(wordPath);
        getPdfPathForBillDto.setFileName(fileName);
        return getPdfPathForBillDto;
    }

    @ApiOperation(value = "修改车辆", tags = "1.4.0")
    @PostMapping(value = "/api/carrierOrderManagement/modifyVehicle")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> modifyVehicle(@RequestBody @Valid ModifyVehicleRequestDto requestDto) {
        if (StringUtils.isNotBlank(requestDto.getDriverIdentity()) && !IDCardValidator.isValidatedAllIdcard(requestDto.getDriverIdentity())) {
            throw new BizException(ManagementWebApiExceptionEnum.ID_CARD_FORMAT_ERROR);
        }
        if (!FrequentMethodUtils.validateVehicleFormat(requestDto.getVehicleNo())) {
            throw new BizException(ManagementWebApiExceptionEnum.VEHICLE_NUMBER_IS_ERROR);
        }
        ModifyVehicleRequestModel requestModel = MapperUtils.mapperNoDefault(requestDto, ModifyVehicleRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        return carrierOrderClient.modifyVehicle(requestModel);
    }

    @ApiOperation(value = "根据运单号查询司机车辆信息", tags = "1.4.0")
    @PostMapping(value = "/api/carrierOrderManagement/getDriverVehicleInfoByCarrierOrderId")
    public Result<DriverAndVehicleResponseDto> getDriverVehicleInfoByCarrierOrderId(@RequestBody @Valid CarrierOrderVehicleInfoRequestDto requestDto) {
        Result<DriverAndVehicleResponseModel> result = carrierOrderClient.getDriverVehicleInfoByCarrierOrderId(MapperUtils.mapper(requestDto, CarrierOrderDetailRequestModel.class));
        result.throwException();
        DriverAndVehicleResponseDto responseDto = MapperUtils.mapper(result.getData(), DriverAndVehicleResponseDto.class);
        if (StringUtils.isBlank(responseDto.getTrailerVehicleNo())) {
            responseDto.setTrailerVehicleId(CommonConstant.BLANK_TEXT);
        }
        return Result.success(responseDto);
    }



}
