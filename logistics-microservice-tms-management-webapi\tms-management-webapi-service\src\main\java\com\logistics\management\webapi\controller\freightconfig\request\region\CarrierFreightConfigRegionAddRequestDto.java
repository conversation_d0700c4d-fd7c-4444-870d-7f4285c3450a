package com.logistics.management.webapi.controller.freightconfig.request.region;

import com.logistics.management.webapi.controller.freightconfig.request.CarrierFreightConfigRequestDto;
import com.logistics.management.webapi.controller.freightconfig.request.ladder.CarrierFreightConfigLadderRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigRegionAddRequestDto extends CarrierFreightConfigRequestDto {

    @Valid
    @ApiModelProperty(value = "新增方案集合", required = true)
    @Size(min = 1, max = 2, message = "方案数量为 1 ~ 2 种方案")
    private List<CarrierFreightConfigRegionAddItemRequestModel> schemeList;

    @Data
    public static class CarrierFreightConfigRegionAddItemRequestModel {
        @ApiModelProperty(value = "运价方案类型; 100: 路线配置; 200:同区-跨区价格; 201: 同区价格; 202: 跨区价格; 301:系统计算预计距离; 302:系统配置距离", required = true)
        @NotBlank(message = "运价方案类型不允许为空")
        private String schemeType;

        @Valid
        @ApiModelProperty(value = "阶梯", required = true)
        @Size(min = 1, max = 10, message = "请维护 1 ~ 10 条阶梯配置")
        private List<CarrierFreightConfigLadderRequestDto> ladderConfigList;
    }
}
