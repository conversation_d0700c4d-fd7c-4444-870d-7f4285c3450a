package com.logistics.tms.controller.vehicleassetmanagement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/6/10 9:23
 */
@Data
public class ImportVehicleBasicInfoRequestModel {
    @ApiModelProperty("导入List")
    private List<ImportVehicleBasicInfoListRequestModel> importList;
    @ApiModelProperty("失败数量")
    private Integer numberFailures;
    @ApiModelProperty("成功数量")
    private Integer numberSuccessful;
}
