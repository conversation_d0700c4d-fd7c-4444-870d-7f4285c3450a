package com.logistics.management.webapi.controller.companycarrier.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/9/27 17:38
 */
@Data
public class SearchCompanyCarrierListRequestDto extends AbstractPageForm<SearchCompanyCarrierListRequestDto> {

    @ApiModelProperty("承运商公司名字")
    private String companyCarrierName;

    @ApiModelProperty("证件补充状态 1 待补充 2 齐全")
    private String certificateSupplement;

    @ApiModelProperty("创建时间开始")
    private String createdTimeStart;

    @ApiModelProperty("创建时间结束")
    private String createdTimeEnd;

    @ApiModelProperty("最后操作人")
    private String lastModifiedBy;

    @ApiModelProperty("最后修改时间开始")
    private String lastModifiedTimeStart;

    @ApiModelProperty("最后修改时间结束")
    private String lastModifiedTimeEnd;

    @ApiModelProperty("货主类型：1 公司 2 个人")
    private String type;

    @ApiModelProperty("授权状态, 0:待授权 1:待审核 2:已驳回 3:已授权")
    private String authorizationStatus;

    @ApiModelProperty("实名状态, 0:待实名 1:实名中 2:已实名")
    private String realNameAuthenticationStatus;

    @ApiModelProperty("来源 1 后台添加 2 web注册 3 app注册 4 车主升级 5 刷数据")
    private String source;

    /**
     * (3.22.0)是否加入黑名单：0 否，1 是
     */
    @ApiModelProperty("是否加入黑名单：0 否，1 是")
    private String ifAddBlacklist;
}
