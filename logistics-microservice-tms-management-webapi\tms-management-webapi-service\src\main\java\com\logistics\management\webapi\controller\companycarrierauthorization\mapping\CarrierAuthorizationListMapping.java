package com.logistics.management.webapi.controller.companycarrierauthorization.mapping;

import com.logistics.management.webapi.base.enums.CompanyAuthorizationAuditStatusEnum;
import com.logistics.management.webapi.base.enums.CompanyAuthorizationIsArchivedEnum;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.client.companycarrierauthorization.response.CarrierAuthorizationListResponseModel;
import com.logistics.management.webapi.controller.companycarrierauthorization.response.CarrierAuthorizationListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

public class CarrierAuthorizationListMapping extends MapperMapping<CarrierAuthorizationListResponseModel, CarrierAuthorizationListResponseDto> {

    @Override
    public void configure() {
        CarrierAuthorizationListResponseModel source = getSource();
        CarrierAuthorizationListResponseDto destination = getDestination();

        // 车主公司类型展示文本
        destination.setCompanyCarrierTypeLabel(CompanyTypeEnum.getEnum(source.getCompanyCarrierType()).getValue());
        // 是否归档展示文本
        destination.setIsArchivedLabel(CompanyAuthorizationIsArchivedEnum.getEnumByKey(source.getIsArchived()).getValue());
        // 授权状态展示文本
        destination.setAuthorizationStatusLabel(CompanyAuthorizationAuditStatusEnum.getEnumByKey(source.getAuthorizationStatus()).getValue());
    }
}
