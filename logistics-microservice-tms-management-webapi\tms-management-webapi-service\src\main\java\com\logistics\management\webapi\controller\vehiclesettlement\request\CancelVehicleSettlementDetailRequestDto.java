package com.logistics.management.webapi.controller.vehiclesettlement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author:lei.zhu
 * @date:2021/4/9 11:35
 */
@Data
public class CancelVehicleSettlementDetailRequestDto {
    @ApiModelProperty(value = "车辆账单id",required = true)
    @NotBlank(message = "id不能为空")
    private String vehicleSettlementId;
    @ApiModelProperty(value = "操作类型  1 无需确认查看  2 司机确认查看  3 撤回查看",required = true)
    @NotBlank(message = "操作类型不能为空")
    private String operatorType;
}
