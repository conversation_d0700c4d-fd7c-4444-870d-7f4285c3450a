package com.logistics.tms.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author：wjf
 * @date：2021/4/12 13:12
 */
@Data
public class DriverReconciliationDetailResponseModel {
    @ApiModelProperty("结算id")
    private Long vehicleSettlementId;
    @ApiModelProperty("结算状态：空 全部，2 待确认，3 待处理，4 待结清,5 部分结清，6 已结清")
    private Integer status;

    //费用信息
    @ApiModelProperty("运费合计")
    private BigDecimal carrierFreight;
    @ApiModelProperty("运单数量")
    private Integer carrierOrderCount;
    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;
    @ApiModelProperty("调整原因")
    private String adjustRemark;
    @ApiModelProperty("轮胎费用")
    private BigDecimal tireFee;
    @ApiModelProperty("轮胎数量")
    private Integer tireCount;
    @ApiModelProperty("充油费用")
    private BigDecimal oilFilledFee;
    @ApiModelProperty("GPS费用")
    private BigDecimal gpsFee;
    @ApiModelProperty("停车费用")
    private BigDecimal parkingFee;
    @ApiModelProperty("保险费用合计")
    private BigDecimal insuranceFee;
    @ApiModelProperty("理赔费用合计")
    private BigDecimal claimFee;
    @ApiModelProperty("贷款扣除费用")
    private BigDecimal loanFee;
    @ApiModelProperty("合计（应收费用）")
    private BigDecimal actualExpensesPayable;

    //付款信息（部分结清、已结清）
    @ApiModelProperty("已结清运费")
    private BigDecimal completeSettlePayable;
    @ApiModelProperty("未结清运费")
    private BigDecimal waitSettlePayable;
    @ApiModelProperty("账单记录数量")
    private Integer billingRecordsCount;

    //异议信息（待处理）
    @ApiModelProperty("司机对账问题备注")
    private String settlementReasonRemark;


    private BigDecimal vehicleClaimFee;//车辆月理赔费用
    private BigDecimal accidentInsuranceClaimFee;//个人意外险费月理赔费用
    private BigDecimal accidentInsuranceFee; // 月应扣个人意外险费
}
