/**
 * Created by yun<PERSON>zhou on 2017/12/12.
 */
package com.logistics.tms.base.enums;

public enum IsOurCompanyEnum {

    OUR_COMPANY(1, "我司"),
    OTHER_COMPANY(2, "其他车主");

    private Integer key;
    private String value;

    IsOurCompanyEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
