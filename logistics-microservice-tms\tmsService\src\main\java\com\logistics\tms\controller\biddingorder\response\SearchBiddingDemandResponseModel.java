package com.logistics.tms.controller.biddingorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SearchBiddingDemandResponseModel {

    /**
     * 需求单号
     */
    @ApiModelProperty("需求单号")
    private Long demandOrderId;

    /**
     * 需求单号
     */
    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private BigDecimal goodsCount;

    /**
     * 路线
     */
    @ApiModelProperty("路线")
    private String route;





}
