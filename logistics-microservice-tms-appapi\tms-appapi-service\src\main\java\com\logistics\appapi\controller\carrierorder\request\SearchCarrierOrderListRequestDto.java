package com.logistics.appapi.controller.carrierorder.request;
import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchCarrierOrderListRequestDto extends AbstractPageForm<SearchCarrierOrderListRequestDto> {

    @ApiModelProperty("状态：20000 待提货 40000 待卸货 50000 待签收 60000已签收 0 已取消，搜索时此字段为空")
    private String status;
    @ApiModelProperty("搜索开始时间")
    private String startTime;
    @ApiModelProperty("搜索结束时间")
    private String endTime;
    @ApiModelProperty("是否新生单子：0 否，1 是")
    private String ifYeloLife;

    @ApiModelProperty("是否按编码回收 0:否 1:是   v2.44")
    private String ifRecycleByCode;
    @ApiModelProperty("是否是额外补的运单  0：否 1：是  V2.6.8")
    private String ifExtCarrierOrder;

}
