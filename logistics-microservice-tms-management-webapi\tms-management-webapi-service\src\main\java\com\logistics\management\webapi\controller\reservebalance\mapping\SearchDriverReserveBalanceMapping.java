package com.logistics.management.webapi.controller.reservebalance.mapping;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.reservebalance.model.response.SearchDriverReserveBalanceResponseModel;
import com.logistics.management.webapi.controller.reservebalance.dto.response.DriverReserveBalanceListResponseDto;
import com.logistics.management.webapi.controller.reservebalance.dto.response.SearchDriverReserveBalanceResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;

import java.math.BigDecimal;
import java.util.List;

public class SearchDriverReserveBalanceMapping extends MapperMapping<SearchDriverReserveBalanceResponseModel, SearchDriverReserveBalanceResponseDto> {

    @Override
    public void configure() {

        SearchDriverReserveBalanceResponseModel source = getSource();
        SearchDriverReserveBalanceResponseDto destination = getDestination();

        // 金额转换
        destination.setTotalRemainingAmount(amountConversion(source.getTotalRemainingAmount()));
        destination.setTotalRechargeAmount(amountConversion(source.getTotalRechargeAmount()));
        destination.setTotalAwaitVerificationAmount(amountConversion(source.getTotalAwaitVerificationAmount()));
        destination.setTotalVerificationAmount(amountConversion(source.getTotalVerificationAmount()));

        // 列表转换
        PageInfo pageInfo = source.getDriverReserveBalancePageInfo();
        if (ListUtils.isNotEmpty(pageInfo.getList())) {
            List list = MapperUtils.mapper(pageInfo.getList(), DriverReserveBalanceListResponseDto.class, new DriverReserveBalanceListMapping());
            pageInfo.setList(list);
        }
    }

    private String amountConversion(BigDecimal amount) {
        return amount.stripTrailingZeros().toPlainString();
    }
}
