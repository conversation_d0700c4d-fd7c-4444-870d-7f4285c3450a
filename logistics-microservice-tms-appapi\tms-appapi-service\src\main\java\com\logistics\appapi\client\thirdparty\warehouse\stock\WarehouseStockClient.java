package com.logistics.appapi.client.thirdparty.warehouse.stock;

import com.logistics.appapi.client.thirdparty.warehouse.stock.reservationconfig.WarehouseReservationConfigServiceApi;
import com.logistics.appapi.client.thirdparty.warehouse.stock.reservationconfig.request.SearchReservationTimeByWarehouseRequestModel;
import com.logistics.appapi.client.thirdparty.warehouse.stock.reservationconfig.response.SearchReservationTimeByWarehouseResponseModel;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: wjf
 * @date: 2024/8/26 10:08
 */
@Service
public class WarehouseStockClient {

    @Resource
    private WarehouseReservationConfigServiceApi warehouseReservationConfigServiceApi;

    /**
     * 查询仓库可用预约时间
     * @param warehouseName 网库名称
     * @return
     */
    public SearchReservationTimeByWarehouseResponseModel searchReservationTimeByWarehouse(String warehouseName){
        if (StringUtils.isBlank(warehouseName)){
            return null;
        }
        SearchReservationTimeByWarehouseRequestModel requestModel = new SearchReservationTimeByWarehouseRequestModel();
        requestModel.setWarehouseName(warehouseName);
        Result<SearchReservationTimeByWarehouseResponseModel> result = warehouseReservationConfigServiceApi.searchReservationTimeByWarehouse(requestModel);
        result.throwException();
        return result.getData();
    }
}
