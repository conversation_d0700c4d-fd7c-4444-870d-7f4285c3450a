<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TReachManagementMapper" >
  <sql id="Base_Column_List_Decrypt" >
    id, carrier_order_id, carrier_order_code, reach_driver_name,
    AES_DECRYPT(UNHEX(reach_driver_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as reach_driver_phone,
    terminal_head, reach_province_id, reach_province_name, reach_city_id, reach_city_name, 
    reach_area_id, reach_area_name, reach_address_detail, reach_address_remark, reach_longitude,
    reach_latitude, distance_deviation, reach_contactor,
    AES_DECRYPT(UNHEX(reach_telephone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as reach_telephone, check_reach_contact,
    reach_time, empty_trays_amount, employ_trays_amount, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>

  <select id="selectByPrimaryKeyDecrypt" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List_Decrypt" />
    from t_reach_management
    where id = #{id,jdbcType=BIGINT}
    and valid = 1
  </select>

  <insert id="insertSelectiveEncrypt" parameterType="com.logistics.tms.entity.TReachManagement" keyProperty="id" useGeneratedKeys="true">
    insert into t_reach_management
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="carrierOrderId != null" >
        carrier_order_id,
      </if>
      <if test="carrierOrderCode != null" >
        carrier_order_code,
      </if>
      <if test="reachDriverName != null" >
        reach_driver_name,
      </if>
      <if test="reachDriverPhone != null" >
        reach_driver_phone,
      </if>
      <if test="terminalHead != null" >
        terminal_head,
      </if>
      <if test="reachProvinceId != null" >
        reach_province_id,
      </if>
      <if test="reachProvinceName != null" >
        reach_province_name,
      </if>
      <if test="reachCityId != null" >
        reach_city_id,
      </if>
      <if test="reachCityName != null" >
        reach_city_name,
      </if>
      <if test="reachAreaId != null" >
        reach_area_id,
      </if>
      <if test="reachAreaName != null" >
        reach_area_name,
      </if>
      <if test="reachAddressDetail != null" >
        reach_address_detail,
      </if>
      <if test="reachAddressRemark != null" >
        reach_address_remark,
      </if>
      <if test="reachLongitude != null">
        reach_longitude,
      </if>
      <if test="reachLatitude != null">
        reach_latitude,
      </if>
      <if test="distanceDeviation != null" >
        distance_deviation,
      </if>
      <if test="reachContactor != null" >
        reach_contactor,
      </if>
      <if test="reachTelephone != null" >
        reach_telephone,
      </if>
      <if test="checkReachContact != null" >
        check_reach_contact,
      </if>
      <if test="reachTime != null" >
        reach_time,
      </if>
      <if test="emptyTraysAmount != null" >
        empty_trays_amount,
      </if>
      <if test="employTraysAmount != null" >
        employ_trays_amount,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderId != null" >
        #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null" >
        #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="reachDriverName != null" >
        #{reachDriverName,jdbcType=VARCHAR},
      </if>
      <if test="reachDriverPhone != null" >
        HEX(AES_ENCRYPT(#{reachDriverPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="terminalHead != null" >
        #{terminalHead,jdbcType=VARCHAR},
      </if>
      <if test="reachProvinceId != null" >
        #{reachProvinceId,jdbcType=BIGINT},
      </if>
      <if test="reachProvinceName != null" >
        #{reachProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="reachCityId != null" >
        #{reachCityId,jdbcType=BIGINT},
      </if>
      <if test="reachCityName != null" >
        #{reachCityName,jdbcType=VARCHAR},
      </if>
      <if test="reachAreaId != null" >
        #{reachAreaId,jdbcType=BIGINT},
      </if>
      <if test="reachAreaName != null" >
        #{reachAreaName,jdbcType=VARCHAR},
      </if>
      <if test="reachAddressDetail != null" >
        #{reachAddressDetail,jdbcType=VARCHAR},
      </if>
      <if test="reachAddressRemark != null" >
        #{reachAddressRemark,jdbcType=VARCHAR},
      </if>
      <if test="reachLongitude != null">
        #{reachLongitude,jdbcType=VARCHAR},
      </if>
      <if test="reachLatitude != null">
        #{reachLatitude,jdbcType=VARCHAR},
      </if>
      <if test="distanceDeviation != null" >
        #{distanceDeviation,jdbcType=DECIMAL},
      </if>
      <if test="reachContactor != null" >
        #{reachContactor,jdbcType=VARCHAR},
      </if>
      <if test="reachTelephone != null" >
        HEX(AES_ENCRYPT(#{reachTelephone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="checkReachContact != null" >
        #{checkReachContact,jdbcType=INTEGER},
      </if>
      <if test="reachTime != null" >
        #{reachTime,jdbcType=TIMESTAMP},
      </if>
      <if test="emptyTraysAmount != null" >
        #{emptyTraysAmount,jdbcType=INTEGER},
      </if>
      <if test="employTraysAmount != null" >
        #{employTraysAmount,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelectiveEncrypt" parameterType="com.logistics.tms.entity.TReachManagement" >
    update t_reach_management
    <set >
      <if test="carrierOrderId != null" >
        carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null" >
        carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="reachDriverName != null" >
        reach_driver_name = #{reachDriverName,jdbcType=VARCHAR},
      </if>
      <if test="reachDriverPhone != null" >
        reach_driver_phone = HEX(AES_ENCRYPT(#{reachDriverPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="terminalHead != null" >
        terminal_head = #{terminalHead,jdbcType=VARCHAR},
      </if>
      <if test="reachProvinceId != null" >
        reach_province_id = #{reachProvinceId,jdbcType=BIGINT},
      </if>
      <if test="reachProvinceName != null" >
        reach_province_name = #{reachProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="reachCityId != null" >
        reach_city_id = #{reachCityId,jdbcType=BIGINT},
      </if>
      <if test="reachCityName != null" >
        reach_city_name = #{reachCityName,jdbcType=VARCHAR},
      </if>
      <if test="reachAreaId != null" >
        reach_area_id = #{reachAreaId,jdbcType=BIGINT},
      </if>
      <if test="reachAreaName != null" >
        reach_area_name = #{reachAreaName,jdbcType=VARCHAR},
      </if>
      <if test="reachAddressDetail != null" >
        reach_address_detail = #{reachAddressDetail,jdbcType=VARCHAR},
      </if>
      <if test="reachAddressRemark != null" >
        reach_address_remark = #{reachAddressRemark,jdbcType=VARCHAR},
      </if>
      <if test="reachLongitude != null">
        reach_longitude = #{reachLongitude,jdbcType=VARCHAR},
      </if>
      <if test="reachLatitude != null">
        reach_latitude = #{reachLatitude,jdbcType=VARCHAR},
      </if>
      <if test="distanceDeviation != null" >
        distance_deviation = #{distanceDeviation,jdbcType=DECIMAL},
      </if>
      <if test="reachContactor != null" >
        reach_contactor = #{reachContactor,jdbcType=VARCHAR},
      </if>
      <if test="reachTelephone != null" >
        reach_telephone = HEX(AES_ENCRYPT(#{reachTelephone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="checkReachContact != null" >
        check_reach_contact = #{checkReachContact,jdbcType=INTEGER},
      </if>
      <if test="reachTime != null" >
        reach_time = #{reachTime,jdbcType=TIMESTAMP},
      </if>
      <if test="emptyTraysAmount != null" >
        empty_trays_amount = #{emptyTraysAmount,jdbcType=INTEGER},
      </if>
      <if test="employTraysAmount != null" >
        employ_trays_amount = #{employTraysAmount,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByCarrierOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List_Decrypt"/>
    from t_reach_management
    where valid = 1
    and carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
  </select>

  <select id="searchReachManagementList"
          resultType="com.logistics.tms.api.feign.terminalreachmanagement.model.SearchReachManagementListResponseModel">
    SELECT
      t1.id reachManagementId,
      t1.carrier_order_id carrierOrderId,
      t1.carrier_order_code carrierOrderCode,
      t1.reach_driver_name reachDriverName,
      AES_DECRYPT(UNHEX(t1.reach_driver_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') reachDriverPhone,
      t1.terminal_head terminalHead,
      t1.reach_province_name reachProvinceName,
      t1.reach_city_name reachCityName,
      t1.reach_area_name reachAreaName,
      t1.reach_address_detail reachAddressDetail,
      t1.distance_deviation distanceDeviation,
      t1.check_reach_contact checkReachContact,
      t1.reach_contactor reachContactor,
      AES_DECRYPT(UNHEX(t1.reach_telephone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') reachTelephone,
      t1.empty_trays_amount emptyTraysAmount,
      t1.employ_trays_amount employTraysAmount,
      t1.reach_time reachTime,
      t1.last_modified_by lastModifiedBy,
      t1.last_modified_time lastModifiedTime

    FROM t_reach_management t1
    WHERE
      t1.valid = 1
      <if test="requestModel.reachManagementIds != null and requestModel.reachManagementIds.size() != 0">
        AND t1.id in
        <foreach collection="requestModel.reachManagementIds" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
      <if test="requestModel.carrierOrderCode != null and requestModel.carrierOrderCode != ''">
        AND instr(t1.carrier_order_code, #{requestModel.carrierOrderCode})
      </if>
      <if test="requestModel.reachDriver != null and requestModel.reachDriver != ''">
        AND (
        instr(t1.reach_driver_name, #{requestModel.reachDriver})
        or
        instr(AES_DECRYPT(UNHEX(t1.reach_driver_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'), #{requestModel.reachDriver}))
      </if>
      <if test="requestModel.reachAddress != null and requestModel.reachAddress != ''">
        AND (
        instr(t1.reach_province_name, #{requestModel.reachAddress})
        or instr(t1.reach_city_name, #{requestModel.reachAddress})
        or instr(t1.reach_area_name, #{requestModel.reachAddress})
        )
      </if>
      <if test="requestModel.checkReachContact != null">
        AND t1.check_reach_contact = #{requestModel.checkReachContact}
      </if>
      <if test="requestModel.contact != null and requestModel.contact != ''">
        AND (
        instr(t1.reach_contactor, #{requestModel.contact})
        or
        instr(AES_DECRYPT(UNHEX(t1.reach_telephone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'), #{requestModel.contact}))
      </if>
      <if test="requestModel.reachTimeStart != null and requestModel.reachTimeStart != ''">
        AND t1.reach_time &gt;= DATE_FORMAT(#{requestModel.reachTimeStart},'%Y-%m-%d 00:00:00')
      </if>
      <if test="requestModel.reachTimeEnd != null and requestModel.reachTimeEnd != ''">
        AND t1.reach_time &lt;= DATE_FORMAT(#{requestModel.reachTimeEnd},'%Y-%m-%d 23:59:59')
      </if>
      <if test="requestModel.lastModifiedBy != null and requestModel.lastModifiedBy != ''">
        AND instr(t1.last_modified_by, #{requestModel.lastModifiedBy})
      </if>
      <if test="requestModel.lastModifiedTimeStart != null and requestModel.lastModifiedTimeStart != ''">
        AND t1.last_modified_time &gt;= DATE_FORMAT(#{requestModel.lastModifiedTimeStart},'%Y-%m-%d 00:00:00')
      </if>
      <if test="requestModel.lastModifiedTimeEnd != null and requestModel.lastModifiedTimeEnd != ''">
        AND t1.last_modified_time &lt;= DATE_FORMAT(#{requestModel.lastModifiedTimeEnd},'%Y-%m-%d 23:59:59')
      </if>
    order by t1.last_modified_time desc
  </select>

  <select id="getReachManagementDetail"
          resultType="com.logistics.tms.api.feign.terminalreachmanagement.model.GetReachManagementDetailResponseModel">
    select
      t1.id reachManagementId,
      t1.carrier_order_code carrierOrderCode,
      t1.reach_driver_name reachDriverName,
      AES_DECRYPT(UNHEX(t1.reach_driver_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') reachDriverPhone,
      t1.reach_time reachTime,
      t1.check_reach_contact checkReachContact,
      t1.reach_contactor reachContactor,
      AES_DECRYPT(UNHEX(t1.reach_telephone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') reachTelephone,
      t1.terminal_head terminalHead,
      t1.reach_province_name reachProvinceName,
      t1.reach_city_name reachCityName,
      t1.reach_area_name reachAreaName,
      t1.reach_address_detail reachAddressDetail,
      t1.distance_deviation distanceDeviation,
      t1.empty_trays_amount emptyTraysAmount,
      t1.employ_trays_amount employTraysAmount,

      t2.upstream_customer upstreamCustomer,

      t3.consignor_name consignorName,
      t3.consignor_mobile consignorMobile,
      t3.load_warehouse loadWarehouse,
      t3.load_province_name loadProvinceName,
      t3.load_city_name loadCityName,
      t3.load_area_name loadAreaName,
      t3.load_detail_address loadDetailAddress

    from t_reach_management t1
    left join t_carrier_order t2 on t1.carrier_order_code = t2.carrier_order_code and t2.valid = 1
    left join t_carrier_order_address t3 on t2.id = t3.carrier_order_id and t3.valid = 1
    WHERE t1.valid = 1
    AND t1.id = #{requestModel.reachManagementId}
  </select>
</mapper>