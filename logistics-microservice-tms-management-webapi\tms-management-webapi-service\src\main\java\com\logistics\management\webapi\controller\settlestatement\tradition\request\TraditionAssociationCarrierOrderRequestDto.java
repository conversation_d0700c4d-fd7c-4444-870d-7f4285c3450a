package com.logistics.management.webapi.controller.settlestatement.tradition.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2022/11/17 17:21
 */
@Data
public class TraditionAssociationCarrierOrderRequestDto extends AbstractPageForm<TraditionAssociationCarrierOrderRequestDto> {

    @ApiModelProperty(value = "对账单id", required = true)
    @NotBlank(message = "id不能为空")
    private String settleStatementId;
}
