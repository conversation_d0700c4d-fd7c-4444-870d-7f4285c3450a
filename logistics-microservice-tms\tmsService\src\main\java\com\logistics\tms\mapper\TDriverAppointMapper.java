package com.logistics.tms.mapper;

import com.logistics.tms.controller.driverappoint.request.SearchAppointRequestModel;
import com.logistics.tms.controller.driverappoint.request.SearchDrierAppointDetailRequestModel;
import com.logistics.tms.controller.driverappoint.request.SearchDriverAppointRequestModel;
import com.logistics.tms.controller.driverappoint.response.SearchAppointCountResponseModel;
import com.logistics.tms.controller.driverappoint.response.SearchAppointResponseModel;
import com.logistics.tms.controller.driverappoint.response.SearchDrierAppointDetailResponseModel;
import com.logistics.tms.entity.TDriverAppoint;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2022/08/15
*/
@Mapper
public interface TDriverAppointMapper extends BaseMapper<TDriverAppoint> {

	TDriverAppoint selectByPrimaryKeyDecrypt(Long id);

	int insertSelectiveEncrypt(TDriverAppoint tDriverAppoint);

	int updateByPrimaryKeySelectiveEncrypt(TDriverAppoint tDriverAppoint);

	/**
	 * 预约记录-详情
	 * @param requestModel
	 * @return
	 */
	SearchDrierAppointDetailResponseModel searchDrierAppointDetail(@Param("requestModel") SearchDrierAppointDetailRequestModel requestModel);

	/**
	 * 条件查询驾驶员预约记录列表
	 *
	 * @param requestModel
	 * @return
	 */
	List<TDriverAppoint> queryDriverAppointList(@Param("requestModel") SearchDriverAppointRequestModel requestModel);

	/**
	 * 小程序-预约记录-列表
	 * @param requestModel
	 * @return
	 */
	List<SearchAppointResponseModel> searchAppointList(@Param("staffId") Long staffId, @Param("requestModel") SearchAppointRequestModel requestModel);

	/**
	 * 查询预约记录数量
	 * @param requestModel
	 * @return
	 */
	SearchAppointCountResponseModel searchAppointCount(@Param("staffId") Long staffId, @Param("requestModel") SearchAppointRequestModel requestModel);
}