package com.logistics.tms.controller.settlestatement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/18
 */
@Data
public class CarrierUndoCarrierOrderRequestModel {

	@ApiModelProperty(value = "对账单明细id")
	private String settleStatementItemIds;

	@ApiModelProperty("请求来源:1 后台，2 前台")
	private Integer source;//1 后台，2 前台
}
