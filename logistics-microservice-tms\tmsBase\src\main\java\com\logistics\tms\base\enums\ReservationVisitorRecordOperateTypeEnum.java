/**
 * Created by yun<PERSON>zhou on 2017/12/12.
 */
package com.logistics.tms.base.enums;

public enum ReservationVisitorRecordOperateTypeEnum {

    RESERVE(1, "预约"),
    SIGN(2, "签到"),;

    private Integer key;
    private String value;

    ReservationVisitorRecordOperateTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
