package com.logistics.tms.client.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class AutoPublishInfoResponseModel {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "配置编码")
    private String configCode;

    @ApiModelProperty(value = "大区ID")
    private Long regionId;

    @ApiModelProperty(value = "大区")
    private String regionName;

    @ApiModelProperty(value = "区域ID")
    private String districtId;

    @ApiModelProperty(value = "区域")
    private String districtName;

    @ApiModelProperty(value = "指定承运商 0是 1否（自有车辆）")
    private Integer isCarrier;

    @ApiModelProperty(value = "承运商(车主)id  t_company id")
    private Long carrierId;

    @ApiModelProperty(value = "承运商名称 企业车主用公司名称，个人车主用姓名+手机号")
    private String carrierName;

    @ApiModelProperty(value = "车辆id")
    private Long vehicleId;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;

    @ApiModelProperty(value = "司机id")
    private Long staffId;

    @ApiModelProperty(value = "仓库信息")
    private AutoPublishInfoWarehouseInfo warehouseInfo;

    @ApiModelProperty(value = "执行等级 0低 1高")
    private Integer executionLevel;

    @ApiModelProperty(value = "需求类型")
    private Integer demandType;

    @ApiModelProperty(value = "指定客户 0否 1是")
    private Integer assignCustomer;

    @ApiModelProperty(value = "客户ID list")
    private String customerId;

    @ApiModelProperty(value = "客户名称 list")
    private String customerName;

    @ApiModelProperty(value = "需求量 0全量 1指定")
    private Integer quantityDemanded;

    @ApiModelProperty(value = "需求量首值")
    private Integer startNum;

    @ApiModelProperty(value = "需求量尾值")
    private Integer endNum;

    @ApiModelProperty(value = "完结量")
    private Integer finishNum;

    @ApiModelProperty(value = "状态 1启动 0禁用")
    private Integer enable;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    @ApiModelProperty(value = "创建人")
    private String createdPer;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedTime;

    @ApiModelProperty(value = "更新人")
    private String updatedPer;
}
