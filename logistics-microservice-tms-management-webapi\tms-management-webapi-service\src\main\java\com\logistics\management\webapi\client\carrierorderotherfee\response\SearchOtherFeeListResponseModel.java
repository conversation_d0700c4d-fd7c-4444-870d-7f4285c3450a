package com.logistics.management.webapi.client.carrierorderotherfee.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SearchOtherFeeListResponseModel {

    @ApiModelProperty(value = "主表id")
    private Long carrierOrderOtherFeeId;

    @ApiModelProperty(value = "审核状态")
    private Integer auditStatus;

    @ApiModelProperty(value = "运单id")
    private Long carrierOrderId;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("车主公司类型")
    private Integer companyCarrierType;
    @ApiModelProperty("车主公司id")
    private Long companyCarrierId;
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName;
    @ApiModelProperty("车主联系人")
    private String carrierContactName;
    @ApiModelProperty("车主联系人电话")
    private String carrierContactMobile;

    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("手机号")
    private String driverMobile;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;

    @ApiModelProperty("提货市")
    private String loadCityName;
    @ApiModelProperty("卸货市")
    private String unloadCityName;

    @ApiModelProperty(value = "合计费用")
    private BigDecimal totalAmount;

    @ApiModelProperty("项目标签（多个标签,拼接）：1 石化板块，2 轮胎板块，3 涂料板块，4 其他")
    private String projectLabel;

    @ApiModelProperty(value = "新增人")
    private String createdBy;
    @ApiModelProperty(value = "新增时间")
    private Date createdTime;

    @ApiModelProperty(value = "最新操作人")
    private String lastModifiedBy;
    @ApiModelProperty(value = "最新操作时间")
    private Date lastModifiedTime;

    @ApiModelProperty("车主对账单状态, -2:未关联对账")
    private Integer carrierSettleStatementStatus;
}
