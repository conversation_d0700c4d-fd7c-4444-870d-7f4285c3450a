package com.logistics.management.webapi.api.impl.terminalcustomeraddress.mapping;

import com.logistics.management.webapi.api.feign.terminalcustomeraddress.dto.SearchTerminalCustomerAddressListResponseDto;
import com.logistics.tms.api.feign.terminalcustomeraddress.model.SearchTerminalCustomerAddressListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

import java.util.Optional;

/**
 * @author: wjf
 * @date: 2022/1/4 17:08
 */
public class SearchTerminalCustomerAddressListMapping extends MapperMapping<SearchTerminalCustomerAddressListResponseModel, SearchTerminalCustomerAddressListResponseDto> {
    @Override
    public void configure() {
        SearchTerminalCustomerAddressListResponseModel source = getSource();
        SearchTerminalCustomerAddressListResponseDto destination = getDestination();

        StringBuilder collectAddress = new StringBuilder();
        if (StringUtils.isNotBlank(source.getCollectWarehouse())){
            collectAddress.append("【"+source.getCollectWarehouse()+"】");
        }
        collectAddress.append(Optional.ofNullable(source.getCollectProvinceName()).orElse("")).
                append(Optional.ofNullable(source.getCollectCityName()).orElse("")).
                append(Optional.ofNullable(source.getCollectAreaName()).orElse(""));
        destination.setCollectAddress(collectAddress.toString());

        destination.setCollectContactName(Optional.ofNullable(source.getCollectContactName()).orElse("")+" " +Optional.ofNullable(source.getCollectContactMobile()).orElse(""));
    }
}
