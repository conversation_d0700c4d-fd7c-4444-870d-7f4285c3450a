package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TVehicleTireNo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TVehicleTireNoMapper extends BaseMapper<TVehicleTireNo> {

    int batchUpdate(@Param("list") List<TVehicleTireNo> upList);

    int batchInsert(@Param("list") List<TVehicleTireNo> addList);

    List<TVehicleTireNo> getByTireId(@Param("tireId") Long tireId);

    List<TVehicleTireNo> getByTiredIds(@Param("tireIds") String listToString);
}