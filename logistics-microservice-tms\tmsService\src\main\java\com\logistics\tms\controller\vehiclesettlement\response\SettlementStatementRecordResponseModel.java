package com.logistics.tms.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author:lei.zhu
 * @date:2021/4/9 13:33
 */
@Data
public class SettlementStatementRecordResponseModel {
    @ApiModelProperty("结算Id")
    private Long vehicleSettlementId;
    @ApiModelProperty("结算状态：0 待对账，1 待发送，2 待确认，3 待处理，4 待结清，5 部分结清，6 已结清")
    private Integer status;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("账单月  年-月")
    private String settlementMonth;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("司机手机号")
    private String driverMobile;
    @ApiModelProperty("月应付运费")
    private BigDecimal actualExpensesPayable;
    @ApiModelProperty("付款记录列表")
    List<SettleStatementRecordItemModel> itemList;
}
