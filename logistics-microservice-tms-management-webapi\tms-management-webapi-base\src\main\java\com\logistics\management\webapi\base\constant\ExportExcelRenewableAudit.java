package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

public class ExportExcelRenewableAudit {

    public ExportExcelRenewableAudit() {
        
    }

    private static final Map<String, String> EXCEL_RENEWABLEAUDIT_ORDER;

    static {
        EXCEL_RENEWABLEAUDIT_ORDER = new LinkedHashMap<>();
        EXCEL_RENEWABLEAUDIT_ORDER.put("单号", "renewableOrderCode");
        EXCEL_RENEWABLEAUDIT_ORDER.put("状态", "statusDesc");
        EXCEL_RENEWABLEAUDIT_ORDER.put("客户", "customerName");
        EXCEL_RENEWABLEAUDIT_ORDER.put("下单数量","goodsAmountTotal");
        EXCEL_RENEWABLEAUDIT_ORDER.put("确认数量", "verifiedGoodsAmountTotal");
        EXCEL_RENEWABLEAUDIT_ORDER.put("业务类型", "businessTypeDesc");
        EXCEL_RENEWABLEAUDIT_ORDER.put("发货地址", "loadDetailAddress");
        EXCEL_RENEWABLEAUDIT_ORDER.put("发货联系人","consignor");
        EXCEL_RENEWABLEAUDIT_ORDER.put("司机","driver");
        EXCEL_RENEWABLEAUDIT_ORDER.put("车辆", "vehicleNo");
        EXCEL_RENEWABLEAUDIT_ORDER.put("卸货仓库", "unloadWarehouse");
        EXCEL_RENEWABLEAUDIT_ORDER.put("卸货地址", "unloadDetailAddress");
        EXCEL_RENEWABLEAUDIT_ORDER.put("卸货联系人", "receiver");
    }

    public static Map<String, String> getExcelRenewableOrder() {
        return EXCEL_RENEWABLEAUDIT_ORDER;
    }
}
