<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TVehicleTireMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TVehicleTire">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_property" jdbcType="INTEGER" property="vehicleProperty" />
    <result column="trailer_vehicle_Id" jdbcType="BIGINT" property="trailerVehicleId" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="replace_date" jdbcType="TIMESTAMP" property="replaceDate" />
    <result column="tire_company" jdbcType="VARCHAR" property="tireCompany" />
    <result column="settlement_status" jdbcType="INTEGER" property="settlementStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, vehicle_id, vehicle_property, trailer_vehicle_Id, staff_id, replace_date, tire_company, 
    settlement_status, remark, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_vehicle_tire
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_vehicle_tire
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TVehicleTire">
    insert into t_vehicle_tire (id, vehicle_id, vehicle_property, 
      trailer_vehicle_Id, staff_id, replace_date, 
      tire_company, settlement_status, remark, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, #{vehicleProperty,jdbcType=INTEGER}, 
      #{trailerVehicleId,jdbcType=BIGINT}, #{staffId,jdbcType=BIGINT}, #{replaceDate,jdbcType=TIMESTAMP}, 
      #{tireCompany,jdbcType=VARCHAR}, #{settlementStatus,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TVehicleTire" keyProperty="id" useGeneratedKeys="true">
    insert into t_vehicle_tire
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleProperty != null">
        vehicle_property,
      </if>
      <if test="trailerVehicleId != null">
        trailer_vehicle_Id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="replaceDate != null">
        replace_date,
      </if>
      <if test="tireCompany != null">
        tire_company,
      </if>
      <if test="settlementStatus != null">
        settlement_status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleProperty != null">
        #{vehicleProperty,jdbcType=INTEGER},
      </if>
      <if test="trailerVehicleId != null">
        #{trailerVehicleId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="replaceDate != null">
        #{replaceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="tireCompany != null">
        #{tireCompany,jdbcType=VARCHAR},
      </if>
      <if test="settlementStatus != null">
        #{settlementStatus,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TVehicleTire">
    update t_vehicle_tire
    <set>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleProperty != null">
        vehicle_property = #{vehicleProperty,jdbcType=INTEGER},
      </if>
      <if test="trailerVehicleId != null">
        trailer_vehicle_Id = #{trailerVehicleId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="replaceDate != null">
        replace_date = #{replaceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="tireCompany != null">
        tire_company = #{tireCompany,jdbcType=VARCHAR},
      </if>
      <if test="settlementStatus != null">
        settlement_status = #{settlementStatus,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TVehicleTire">
    update t_vehicle_tire
    set vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_property = #{vehicleProperty,jdbcType=INTEGER},
      trailer_vehicle_Id = #{trailerVehicleId,jdbcType=BIGINT},
      staff_id = #{staffId,jdbcType=BIGINT},
      replace_date = #{replaceDate,jdbcType=TIMESTAMP},
      tire_company = #{tireCompany,jdbcType=VARCHAR},
      settlement_status = #{settlementStatus,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>