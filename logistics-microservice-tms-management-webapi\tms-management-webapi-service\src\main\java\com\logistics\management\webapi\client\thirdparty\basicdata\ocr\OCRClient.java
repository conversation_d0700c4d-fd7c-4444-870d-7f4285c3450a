package com.logistics.management.webapi.client.thirdparty.basicdata.ocr;

import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.thirdparty.basicdata.ocr.hystrix.OCRClientHystrix;
import com.logistics.management.webapi.client.thirdparty.basicdata.ocr.response.OcrMultipleInvoiceResponseModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.ocr.response.OcrPictureRequestModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 * @author: wjf
 * @date: 2024/6/27 10:58
 */
@FeignClient(name = FeignClientName.BASIC_DATA_SERVICES,
        path = "/service/ocr",
        fallback = OCRClientHystrix.class)
public interface OCRClient {

    /**
     * 发票数据ocr识别
     *
     * @param file 要识别的ocr数量
     * @return 费用申请列表
     */
    @PostMapping(value = "/multipleInvoice", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    Result<OcrMultipleInvoiceResponseModel> multipleInvoice(@RequestPart(value = "file") MultipartFile file, @RequestParam("type") String type);


    @PostMapping(value = "/qRCodeIO")
    @ApiOperation(value = "iOCR识别图片二维码")
    Result<String> qRCodeIO(@RequestBody OcrPictureRequestModel requestModel);


}
