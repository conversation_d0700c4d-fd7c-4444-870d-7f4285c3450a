package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * @author: wjf
 * @date: 2024/1/29 9:31
 */
@Getter
@AllArgsConstructor
public enum MatchingLocationEnum {
    DEFAULT(0, ""),
    LOAD(1, "发货信息"),
    UNLOAD(2, "收货信息"),
    ;

    private final Integer key;
    private final String value;

    public static MatchingLocationEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
