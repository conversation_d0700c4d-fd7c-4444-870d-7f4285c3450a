package com.logistics;

import com.leyi.auth.service.client.annotation.EnableTransUser;
import com.logistics.tms.config.rdelayqueue.spring.EnabledDelayQueue;
import com.yelo.tools.mybatis.annocation.EnableMySqlDataSource;
import com.yelo.tools.rabbitmq.annocation.EnableRabbitListenerAcknowledgeMode;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

/**
 * @Author: sj
 * @Date: 2019/9/9 11:52
 */
@EnabledDelayQueue
@EnableWebMvc
@EnableMySqlDataSource
@EnableTransUser
@EnableFeignClients({"com.logistics","com.leyi","com.yelo"})
@EnableDiscoveryClient
@SpringBootApplication
@EnableRabbitListenerAcknowledgeMode
@ComponentScan(basePackages = {"com.logistics","com.yelo","com.leyi"})
public class TmsApplication {
    public static void main(String[] args) {
        SpringApplication.run(TmsApplication.class, args);
    }

}
