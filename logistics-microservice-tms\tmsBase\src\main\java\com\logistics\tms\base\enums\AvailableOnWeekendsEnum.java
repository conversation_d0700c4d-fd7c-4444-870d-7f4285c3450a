package com.logistics.tms.base.enums;

/**
 * Created by yuhong.lin on 2019/1/18
 */
public enum AvailableOnWeekendsEnum {
    DEFAULT(0 ,""),
    MARK_UP(1, "周末可上门"),
    MARK_DOWN(2, "周末不可上门")
    ;

    private Integer key;
    private String value;

    AvailableOnWeekendsEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static AvailableOnWeekendsEnum getEnum(Integer key) {
        for (AvailableOnWeekendsEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return DEFAULT;
    }
}
