package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TDemandPayment extends BaseEntity {
    /**
    * 需求单ID
    */
    @ApiModelProperty("需求单ID")
    private Long demandOrderId;

    /**
    * 报价类型 1 单价 2 整车价
    */
    @ApiModelProperty("报价类型 1 单价 2 整车价")
    private Integer priceType;

    /**
    * 结算数量
    */
    @ApiModelProperty("结算数量")
    private BigDecimal settlementAmount;

    /**
    * 结算费用合计
    */
    @ApiModelProperty("结算费用合计")
    private BigDecimal settlementCostTotal;

    /**
    * 结算时间
    */
    @ApiModelProperty("结算时间")
    private Date settlementTime;

    /**
    * 付款状态0 未收款 1 已收款
    */
    @ApiModelProperty("付款状态0 未收款 1 已收款")
    private Integer status;
}