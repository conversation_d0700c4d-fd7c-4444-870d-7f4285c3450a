package com.logistics.tms.controller.dispatch.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * liang current user system login name
 * 2018/9/26 current system date
 */
@Data
public class DemandOrderDispatchResponseModel {
    private Long demandOrderGoodsId;
    private Long demandOrderId;
    private String demandOrderCode;
    private String customerOrderCode;
    private Integer source;

    private Long companyEntrustId;
    private Integer entrustType;
    private String goodsName;
    private Integer goodsUnit;
    private String goodsSize;
    private Integer length;
    private Integer width;
    private Integer height;
    private BigDecimal notArrangedAmount;
    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadWarehouse;
    private String loadDetailAddress;
    private String unloadWarehouse;
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private BigDecimal goodsAmount;
    @ApiModelProperty("车主类型 1 公司 2 个人")
    private Integer carrierType;
    @ApiModelProperty("承运商id")
    private Long companyCarrierId;
    @ApiModelProperty("车主公司名")
    private String companyCarrierName;
    @ApiModelProperty("承运商联系人id")
    private Long carrierContactId;
    @ApiModelProperty("车主联系人")
    private String customerCarrierName;
    @ApiModelProperty("车主联系方式")
    private String customerCarrierMobile;
    @ApiModelProperty("是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;
}
