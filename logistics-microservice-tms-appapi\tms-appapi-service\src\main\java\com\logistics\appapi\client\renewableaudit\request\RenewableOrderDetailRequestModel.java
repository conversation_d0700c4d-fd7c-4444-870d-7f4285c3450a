package com.logistics.appapi.client.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class RenewableOrderDetailRequestModel {

	@ApiModelProperty(value = "乐橘新生订单审核表id", required = true)
	@NotBlank(message = "请选择要查看的订单")
	private Long renewableAuditId;
}
