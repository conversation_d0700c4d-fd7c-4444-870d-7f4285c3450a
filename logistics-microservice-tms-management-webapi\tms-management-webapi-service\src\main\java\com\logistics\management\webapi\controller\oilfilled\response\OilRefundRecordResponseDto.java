package com.logistics.management.webapi.controller.oilfilled.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/12/23 16:17
 */
@Data
public class OilRefundRecordResponseDto {
    @ApiModelProperty(value = "充油记录ID")
    private String oilFilledId = "";
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo = "";
    @ApiModelProperty(value = "退款金额")
    private String oilFilledFee = "";
    @ApiModelProperty(value = "退款时间")
    private String oilFilledDate = "";
    @ApiModelProperty(value = "充油方式")
    private String oilFilledType = "";
    @ApiModelProperty(value = "充油方式文字")
    private String oilFilledTypeLabel = "";
    @ApiModelProperty(value = "退款原因类型：0 其他，10 丢失副卡，20 车辆过户，30 车辆报废，40 充油充错")
    private String refundReasonType="";
    private String refundReasonTypeDesc="";
    @ApiModelProperty(value = "退款原因描述")
    private String refundReason="";
    @ApiModelProperty(value = "操作人")
    private String lastModifiedBy = "";
    @ApiModelProperty(value = "操作时间")
    private String lastModifiedTime = "";
}
