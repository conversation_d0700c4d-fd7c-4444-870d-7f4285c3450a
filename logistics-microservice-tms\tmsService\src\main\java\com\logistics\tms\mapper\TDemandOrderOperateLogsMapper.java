package com.logistics.tms.mapper;

import com.logistics.tms.controller.demandorder.response.GetDemandOrderLogsResponseModel;
import com.logistics.tms.entity.TDemandOrderOperateLogs;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TDemandOrderOperateLogsMapper extends BaseMapper<TDemandOrderOperateLogs> {

    int batchInsertSelective(@Param("list") List<TDemandOrderOperateLogs> list);

    List<GetDemandOrderLogsResponseModel> getDemandOrderLogs(@Param("demandOrderId")Long demandOrderId);

    List<TDemandOrderOperateLogs> getInvalidByDemandOrderId(@Param("demandOrderId")Long demandOrderId);

}