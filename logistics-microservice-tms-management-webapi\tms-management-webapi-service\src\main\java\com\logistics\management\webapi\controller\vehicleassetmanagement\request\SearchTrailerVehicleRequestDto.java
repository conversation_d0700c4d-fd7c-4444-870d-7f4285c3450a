package com.logistics.management.webapi.controller.vehicleassetmanagement.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class SearchTrailerVehicleRequestDto extends AbstractPageForm<SearchTrailerVehicleRequestDto> {

    @ApiModelProperty(value = "车主ID",required = true)
    @NotBlank(message = "车主id不能为空")
    private String companyCarrierId;

    @ApiModelProperty("挂车车牌号")
    private String trailerVehicleNo;
}
