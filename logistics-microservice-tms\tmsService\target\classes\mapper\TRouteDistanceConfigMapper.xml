<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TRouteDistanceConfigMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TRouteDistanceConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="from_province_id" jdbcType="BIGINT" property="fromProvinceId" />
    <result column="from_province_name" jdbcType="VARCHAR" property="fromProvinceName" />
    <result column="from_city_id" jdbcType="BIGINT" property="fromCityId" />
    <result column="from_city_name" jdbcType="VARCHAR" property="fromCityName" />
    <result column="from_area_id" jdbcType="BIGINT" property="fromAreaId" />
    <result column="from_area_name" jdbcType="VARCHAR" property="fromAreaName" />
    <result column="to_province_id" jdbcType="BIGINT" property="toProvinceId" />
    <result column="to_province_name" jdbcType="VARCHAR" property="toProvinceName" />
    <result column="to_city_id" jdbcType="BIGINT" property="toCityId" />
    <result column="to_city_name" jdbcType="VARCHAR" property="toCityName" />
    <result column="to_area_id" jdbcType="BIGINT" property="toAreaId" />
    <result column="to_area_name" jdbcType="VARCHAR" property="toAreaName" />
    <result column="billing_distance" jdbcType="DECIMAL" property="billingDistance" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, from_province_id, from_province_name, from_city_id, from_city_name, from_area_id, 
    from_area_name, to_province_id, to_province_name, to_city_id, to_city_name, to_area_id, 
    to_area_name, billing_distance, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_route_distance_config
    where id = #{id,jdbcType=BIGINT} and valid = 1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_route_distance_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TRouteDistanceConfig">
    insert into t_route_distance_config (id, from_province_id, from_province_name, 
      from_city_id, from_city_name, from_area_id, 
      from_area_name, to_province_id, to_province_name, 
      to_city_id, to_city_name, to_area_id, 
      to_area_name, billing_distance, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{fromProvinceId,jdbcType=BIGINT}, #{fromProvinceName,jdbcType=VARCHAR}, 
      #{fromCityId,jdbcType=BIGINT}, #{fromCityName,jdbcType=VARCHAR}, #{fromAreaId,jdbcType=BIGINT}, 
      #{fromAreaName,jdbcType=VARCHAR}, #{toProvinceId,jdbcType=BIGINT}, #{toProvinceName,jdbcType=VARCHAR}, 
      #{toCityId,jdbcType=BIGINT}, #{toCityName,jdbcType=VARCHAR}, #{toAreaId,jdbcType=BIGINT}, 
      #{toAreaName,jdbcType=VARCHAR}, #{billingDistance,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TRouteDistanceConfig">
    insert into t_route_distance_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fromProvinceId != null">
        from_province_id,
      </if>
      <if test="fromProvinceName != null">
        from_province_name,
      </if>
      <if test="fromCityId != null">
        from_city_id,
      </if>
      <if test="fromCityName != null">
        from_city_name,
      </if>
      <if test="fromAreaId != null">
        from_area_id,
      </if>
      <if test="fromAreaName != null">
        from_area_name,
      </if>
      <if test="toProvinceId != null">
        to_province_id,
      </if>
      <if test="toProvinceName != null">
        to_province_name,
      </if>
      <if test="toCityId != null">
        to_city_id,
      </if>
      <if test="toCityName != null">
        to_city_name,
      </if>
      <if test="toAreaId != null">
        to_area_id,
      </if>
      <if test="toAreaName != null">
        to_area_name,
      </if>
      <if test="billingDistance != null">
        billing_distance,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fromProvinceId != null">
        #{fromProvinceId,jdbcType=BIGINT},
      </if>
      <if test="fromProvinceName != null">
        #{fromProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="fromCityId != null">
        #{fromCityId,jdbcType=BIGINT},
      </if>
      <if test="fromCityName != null">
        #{fromCityName,jdbcType=VARCHAR},
      </if>
      <if test="fromAreaId != null">
        #{fromAreaId,jdbcType=BIGINT},
      </if>
      <if test="fromAreaName != null">
        #{fromAreaName,jdbcType=VARCHAR},
      </if>
      <if test="toProvinceId != null">
        #{toProvinceId,jdbcType=BIGINT},
      </if>
      <if test="toProvinceName != null">
        #{toProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="toCityId != null">
        #{toCityId,jdbcType=BIGINT},
      </if>
      <if test="toCityName != null">
        #{toCityName,jdbcType=VARCHAR},
      </if>
      <if test="toAreaId != null">
        #{toAreaId,jdbcType=BIGINT},
      </if>
      <if test="toAreaName != null">
        #{toAreaName,jdbcType=VARCHAR},
      </if>
      <if test="billingDistance != null">
        #{billingDistance,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TRouteDistanceConfig">
    update t_route_distance_config
    <set>
      <if test="fromProvinceId != null">
        from_province_id = #{fromProvinceId,jdbcType=BIGINT},
      </if>
      <if test="fromProvinceName != null">
        from_province_name = #{fromProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="fromCityId != null">
        from_city_id = #{fromCityId,jdbcType=BIGINT},
      </if>
      <if test="fromCityName != null">
        from_city_name = #{fromCityName,jdbcType=VARCHAR},
      </if>
      <if test="fromAreaId != null">
        from_area_id = #{fromAreaId,jdbcType=BIGINT},
      </if>
      <if test="fromAreaName != null">
        from_area_name = #{fromAreaName,jdbcType=VARCHAR},
      </if>
      <if test="toProvinceId != null">
        to_province_id = #{toProvinceId,jdbcType=BIGINT},
      </if>
      <if test="toProvinceName != null">
        to_province_name = #{toProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="toCityId != null">
        to_city_id = #{toCityId,jdbcType=BIGINT},
      </if>
      <if test="toCityName != null">
        to_city_name = #{toCityName,jdbcType=VARCHAR},
      </if>
      <if test="toAreaId != null">
        to_area_id = #{toAreaId,jdbcType=BIGINT},
      </if>
      <if test="toAreaName != null">
        to_area_name = #{toAreaName,jdbcType=VARCHAR},
      </if>
      <if test="billingDistance != null">
        billing_distance = #{billingDistance,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TRouteDistanceConfig">
    update t_route_distance_config
    set from_province_id = #{fromProvinceId,jdbcType=BIGINT},
      from_province_name = #{fromProvinceName,jdbcType=VARCHAR},
      from_city_id = #{fromCityId,jdbcType=BIGINT},
      from_city_name = #{fromCityName,jdbcType=VARCHAR},
      from_area_id = #{fromAreaId,jdbcType=BIGINT},
      from_area_name = #{fromAreaName,jdbcType=VARCHAR},
      to_province_id = #{toProvinceId,jdbcType=BIGINT},
      to_province_name = #{toProvinceName,jdbcType=VARCHAR},
      to_city_id = #{toCityId,jdbcType=BIGINT},
      to_city_name = #{toCityName,jdbcType=VARCHAR},
      to_area_id = #{toAreaId,jdbcType=BIGINT},
      to_area_name = #{toAreaName,jdbcType=VARCHAR},
      billing_distance = #{billingDistance,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>