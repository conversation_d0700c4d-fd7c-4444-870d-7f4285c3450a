package com.logistics.appapi.controller.driversafepromise.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @Author: sj
 * @Date: 2019/11/18 15:18
 */
@Data
public class SafePromiseDetailResponseDto {
    @ApiModelProperty("承诺书关系人员ID")
    private String relationId = "";
    @ApiModelProperty("所属年份")
    private String period = "";
    @ApiModelProperty("承诺书名称如,2019年驾驶员安全承诺书")
    private String periodLabel = "";
    @ApiModelProperty("签订状态: 0待签订、1已签订")
    private String status = "";
    @ApiModelProperty("签订状态文本")
    private String statusLabel = "";
    @ApiModelProperty("发布时间")
    private String publishTime = "";
    @ApiModelProperty("经办人")
    private String agent = "";
    @ApiModelProperty("承诺书内容")
    private String content = "";

    @ApiModelProperty("手持承诺书图片地址-相对路径")
    private String handPromiseUrl = "";
    @ApiModelProperty("手持承诺书图片地址-绝对路径")
    private String absoluteHandPromiseUrl = "";

    @ApiModelProperty("签字责任书图片地址-相对路径")
    private String signResponsibilityUrl = "";
    @ApiModelProperty("签字责任书图片地址-绝对路径")
    private String absoluteSignResponsibilityUrl = "";

    @ApiModelProperty("附件路径")
    private String attachmentUrl= "";
    @ApiModelProperty("附件路径-绝对路径")
    private String absoluteAttachmentUrl= "";

}
