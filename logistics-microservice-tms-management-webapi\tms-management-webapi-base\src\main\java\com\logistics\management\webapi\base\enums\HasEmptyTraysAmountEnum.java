package com.logistics.management.webapi.base.enums;

public enum HasEmptyTraysAmountEnum {

    NULL(-1,""),
    NOT_EXIST(0, "不存在"),
    EXIST(1, "存在"),

    ;

    private Integer key;
    private String value;

    HasEmptyTraysAmountEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static HasEmptyTraysAmountEnum getEnum(Integer key) {
        for (HasEmptyTraysAmountEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return NULL;
    }

}
