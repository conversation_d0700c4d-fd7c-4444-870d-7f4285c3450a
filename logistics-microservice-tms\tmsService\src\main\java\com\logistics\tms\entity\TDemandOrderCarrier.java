package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2022/07/08
*/
@Data
public class TDemandOrderCarrier extends BaseEntity {
    /**
    * 需求单ID
    */
    @ApiModelProperty("需求单ID")
    private Long demandOrderId;

    /**
    * 车主ID
    */
    @ApiModelProperty("车主ID")
    private Long companyCarrierId;

    /**
    * 车主联系人ID
    */
    @ApiModelProperty("车主联系人ID")
    private Long carrierContactId;

    /**
    * 车主价格：1 单价(元/吨，元/件)，2 一口价(元)
    */
    @ApiModelProperty("车主价格：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer carrierPriceType;

    /**
    * 车主价格
    */
    @ApiModelProperty("车主价格")
    private BigDecimal carrierPrice;

    /**
    * 取消原因
    */
    @ApiModelProperty("取消原因")
    private String cancelReason;
}