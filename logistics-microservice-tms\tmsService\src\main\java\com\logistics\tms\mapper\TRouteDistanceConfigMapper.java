package com.logistics.tms.mapper;

import com.logistics.tms.biz.carrierfreight.model.AddressAreaExistRequestModel;
import com.logistics.tms.controller.routeconfig.request.RouteDistanceConfigListRequestModel;
import com.logistics.tms.controller.routeconfig.response.RouteDistanceConfigDetailResponseModel;
import com.logistics.tms.controller.routeconfig.response.RouteDistanceConfigResponseModel;
import com.logistics.tms.entity.TRouteDistanceConfig;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2023/06/30
*/
@Mapper
public interface TRouteDistanceConfigMapper extends BaseMapper<TRouteDistanceConfig> {

    List<RouteDistanceConfigResponseModel> selectByArea(@Param("param") RouteDistanceConfigListRequestModel requestModel);

    RouteDistanceConfigDetailResponseModel selectById(@Param("routeDistanceConfigId") Long routeDistanceConfigId);

    TRouteDistanceConfig selectByFromToArea(@Param("param") TRouteDistanceConfig tRouteDistanceConfig);

    List<TRouteDistanceConfig> selectByFromAreaAndToArea(@Param("areaList") List<AddressAreaExistRequestModel> areaList);
}