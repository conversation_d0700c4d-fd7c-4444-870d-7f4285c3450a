package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class DemandOrderForLeYiResponseModel {
    @ApiModelProperty("需求单ID")
    private Long demandId;
    @ApiModelProperty("委托单状态：1000待调度 2000部分调度 3000调度完成 - 用于列表计算需求单实际货主费用使用")
    private Integer useStatus;
    @ApiModelProperty("委托单状态：500待发布 1000待调度 2000部分调度 3000调度完成 4000待签收 5000已签收 1取消 2放空")
    private Integer status;
    @ApiModelProperty("是否取消 1 是 0 否")
    private Integer ifCancel;
    @ApiModelProperty("是否放空 1 是 0 否")
    private Integer ifEmpty;
    @ApiModelProperty("是否回退")
    private Integer ifRollback;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("下单人")
    private String publishName;
    @ApiModelProperty("下单时间")
    private Date publishTime;
    @ApiModelProperty("周末是否可上门：0 空，1 是，2 否")
    private Integer availableOnWeekends;
    @ApiModelProperty("装卸方: 0 空，1 我司装卸，2 客户装卸")
    private Integer loadingUnloadingPart;
    @ApiModelProperty("装卸费用")
    private BigDecimal loadingUnloadingCharge;
    @ApiModelProperty("回收任务类型：1 日常回收，2 加急或节假日回收")
    private Integer recycleTaskType;

    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;
    @ApiModelProperty("合同价类型：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer contractPriceType;
    @ApiModelProperty("合同价")
    private BigDecimal contractPrice;
    @ApiModelProperty("预计合同价类型：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer exceptContractPriceType;
    @ApiModelProperty("预计合同价")
    private BigDecimal exceptContractPrice;
    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;
    @ApiModelProperty("委托数量")
    private BigDecimal goodsAmount;
    @ApiModelProperty("已安排数量")
    private BigDecimal arrangedAmount;
    @ApiModelProperty("未安排数量")
    private BigDecimal notArrangedAmount;
    @ApiModelProperty("退回数量")
    private BigDecimal backAmount;
    @ApiModelProperty("备注")
    private String remark;

    private String userName;
    private Long loadProvinceId;
    private String loadProvinceName;
    private Long loadCityId;
    private String loadCityName;
    private Long loadAreaId;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    private String consignorName;
    private String consignorMobile;
    private Date expectedLoadTime;
    @ApiModelProperty("提货大区名称")
    private String loadRegionName;
    @ApiModelProperty("提货大区负责人姓名")
    private String loadRegionContactName;
    @ApiModelProperty("提货大区负责人联系方式")
    private String loadRegionContactPhone;
    private Long unloadProvinceId;
    private String unloadProvinceName;
    private Long unloadCityId;
    private String unloadCityName;
    private Long unloadAreaId;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;
    private String receiverName;
    private String receiverMobile;
    private Date expectedUnloadTime;

    @ApiModelProperty("货主公司ID")
    private Long companyEntrustId;
    @ApiModelProperty("货主公司名称")
    private String companyEntrustName;
    @ApiModelProperty("上游客户")
    private String upstreamCustomer;

    @ApiModelProperty("车主公司ID")
    private Long companyCarrierId;
    @ApiModelProperty("车主公司类型")
    private Integer companyCarrierType;
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName;
    @ApiModelProperty("车主账号名称")
    private String carrierContactName;
    @ApiModelProperty("车主账号手机号")
    private String carrierContactPhone;

    @ApiModelProperty("取消原因")
    private String cancelReason;
    @ApiModelProperty("委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件")
    private Integer source;

    @ApiModelProperty("预计货主费用")
    private BigDecimal expectEntrustFee;

    @ApiModelProperty("是否加急：0 否，1 是")
    private Integer ifUrgent;

    @ApiModelProperty("差异数量（运单下差异数总和） null即没有纠错数据或无需纠错，")
    private BigDecimal differenceAmount;
    @ApiModelProperty("云仓异常数")
    private BigDecimal abnormalAmount ;

    @ApiModelProperty("调度时效")
    private Integer dispatchValidity;
    @ApiModelProperty("是否逾期：0 否，1 是")
    private Integer ifOverdue;

    @ApiModelProperty("需求单货物列表")
    private List<DemandOrderGoodsResponseModel> goodsResponseModels;

    @ApiModelProperty("下单人部门名称")
    private String publishOrgName;

    @ApiModelProperty("货主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer settlementTonnage;

    @ApiModelProperty("回退原因类型一级：2 客户原因，3 不可抗力，4 物流原因，5 平台问题，6 其他原因")
    private Integer rollbackCauseType;
    @ApiModelProperty("回退原因类型二级：201 重复上报，202 更换签收单抬头，203 数据报错，重新下单，204 地址原因，205 等待问题，206 托盘占用，207 流向核对，208 不配合装车，209 客户临时有事，210 现场电话联系不上；301 恶劣天气，302 政府管制，303 修路，304 洪涝；401 回收不及时，402 车辆已满载；501 重复下单，502 操作不规范；601 物流提货现场并单")
    private Integer rollbackCauseTypeTwo;
    @ApiModelProperty("回退备注")
    private String rollbackRemark;

    @ApiModelProperty("是否是补充需求单的创建 0否 1是")
    private Integer ifExtDemandOrder;
}

