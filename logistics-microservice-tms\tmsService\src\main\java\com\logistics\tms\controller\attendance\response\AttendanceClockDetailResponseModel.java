package com.logistics.tms.controller.attendance.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 考勤打卡详情响应
 *
 */
@Data
@Accessors(chain = true)
public class AttendanceClockDetailResponseModel {

	@ApiModelProperty("已完成的打卡类型,0:未打卡 1:上班打卡 2:下班打卡")
	private Integer isOverClockInType;

	@ApiModelProperty("上班打卡时间")
	private Date onDutyPunchTime;

	@ApiModelProperty("下班打卡时间")
	private Date offDutyPunchTime;
}
