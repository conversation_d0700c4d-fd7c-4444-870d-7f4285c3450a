<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TSettleStatementArchiveAttachmentMapper">
    <insert id="batchInsertSelective">
        <foreach collection="list" item="item" separator=";">
            insert into t_settle_statement_archive_attachment
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.settleStatementItemId != null">
                    settle_statement_item_id,
                </if>
                <if test="item.imagePath != null">
                    image_path,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.settleStatementItemId != null">
                    #{item.settleStatementItemId,jdbcType=BIGINT},
                </if>
                <if test="item.imagePath != null">
                    #{item.imagePath,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <select id="archiveTicketPathByItemId" resultType="java.lang.String">
        select image_path
        from t_settle_statement_archive_attachment
        where valid = 1
        and settle_statement_item_id = #{itemId,jdbcType=BIGINT}
    </select>

    <update id="delBySettleStatementItemId">
        update t_settle_statement_archive_attachment
        set valid = 0
        where settle_statement_item_id = #{itemId,jdbcType=BIGINT}
        and valid = 1
    </update>
</mapper>