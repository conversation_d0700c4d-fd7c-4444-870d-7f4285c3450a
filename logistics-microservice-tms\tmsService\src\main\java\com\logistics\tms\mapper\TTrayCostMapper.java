package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.traycost.model.SearchCostRecordsListRequestModel;
import com.logistics.tms.api.feign.traycost.model.SearchCostRecordsListResponseModel;
import com.logistics.tms.api.feign.traycost.model.SearchTrayCostListResponseModel;
import com.logistics.tms.entity.TTrayCost;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TTrayCostMapper extends BaseMapper<TTrayCost> {
    List<SearchTrayCostListResponseModel> searchTrayCostList();

    TTrayCost getTopByEntrustTypeGoodsUnit(@Param("entrustType")Integer entrustType, @Param("goodsUnit")Integer goodsUnit);

    List<TTrayCost> getTopByEntrustTypesAndGoodsUnits(@Param("entrustTypes")String entrustType, @Param("goodsUnits")String goodsUnit);

    List<SearchCostRecordsListResponseModel> searchCostRecordsList(@Param("prams") SearchCostRecordsListRequestModel requestModel);

}