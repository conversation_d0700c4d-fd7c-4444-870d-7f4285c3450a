package com.logistics.management.webapi.client.freightconfig.request.scheme;

import com.logistics.management.webapi.client.freightconfig.request.CarrierFreightConfigRequestModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigSchemeAddRequestModel extends CarrierFreightConfigRequestModel {

    @ApiModelProperty(value = "运价方案类型; 100: 路线配置; 200:同区-跨区价格; 201: 同区价格; 202: 跨区价格; 301:系统计算预计距离; 302:系统配置距离", required = true)
    private Integer schemeType;
}
