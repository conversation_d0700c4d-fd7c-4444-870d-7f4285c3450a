package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 提卸货详情
 *
 * <AUTHOR>
 * @date 2022/8/16 16:46
 */
@Data
public class LoadDetailForYeloLifeResponseDto {

    @ApiModelProperty("运单ID")
    private String carrierOrderId = "";

    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";

    @ApiModelProperty("车辆")
    private String vehicleNo = "";

    @ApiModelProperty("司机")
    private String driver = "";

    @ApiModelProperty("发货地址")
    private String loadAddress = "";

    @ApiModelProperty("发货人")
    private String consignorName = "";

    @ApiModelProperty("发货人手机号")
    private String consignorMobile = "";

    @ApiModelProperty("收货地址")
    private String unloadAddress = "";

    @ApiModelProperty("收货人")
    private String receiverName = "";

    @ApiModelProperty("收货人手机号")
    private String receiverMobile = "";

    @ApiModelProperty("货物单位")
    private String goodsUnit = "";

    @ApiModelProperty("单位文本")
    private String goodsUnitLabel = "";

    @ApiModelProperty("业务类型：1 公司，2 个人")
    private String businessType = "";

    @ApiModelProperty("货物列表")
    private List<LoadDetailForYeloLifeGoodsResponseDto> goodsList = new ArrayList<>();
}
