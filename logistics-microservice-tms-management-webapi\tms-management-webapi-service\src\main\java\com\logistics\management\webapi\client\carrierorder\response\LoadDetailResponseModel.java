package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-07-29 13:24
 */
@Data
public class LoadDetailResponseModel {
    @ApiModelProperty("运单ID")
    private Long carrierOrderId;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;
    @ApiModelProperty("货主")
    private Long companyEntrustId;
    private String companyEntrustName;
    @ApiModelProperty("单位")
    private Integer goodsUnit;
    @ApiModelProperty("调度时间/运单生成时间")
    private Date dispatchTime;

    @ApiModelProperty("车辆")
    private String vehicleNo;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("司机电话")
    private String driverMobile;

    @ApiModelProperty("提货地址信息")
    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    @ApiModelProperty("卸货地址信息")
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;
    @ApiModelProperty("是否云盘数据: 1 是 0 否")
    private Integer ifTray = 0;
    @ApiModelProperty("货物品名")
    private List<CarrierOrderGoodsResponseModel> goodsList;
}
