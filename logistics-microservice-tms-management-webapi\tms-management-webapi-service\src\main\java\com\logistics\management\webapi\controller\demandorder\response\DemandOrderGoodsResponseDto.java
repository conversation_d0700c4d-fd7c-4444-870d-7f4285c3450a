package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DemandOrderGoodsResponseDto {
    @ApiModelProperty("货物信息id")
    private String demandOrderGoodsId="";
    @ApiModelProperty("品名")
    private String goodsName="";
    @ApiModelProperty("规格")
    private String goodsSize="";
    @ApiModelProperty("委托件数")
    private String goodsAmountNumber="";
    @ApiModelProperty("委托体积")
    private String goodsAmountVolume="";
    @ApiModelProperty("委托预计重量")
    private String goodsAmountExpectWeight="";
    @ApiModelProperty("已安排件数")
    private String arrangedAmountNumber="";
    @ApiModelProperty("已委托体积")
    private String arrangedAmountVolume="";
    @ApiModelProperty("已委托预计重量")
    private String arrangedAmountExpectWeight="";
    @ApiModelProperty("未安排件数")
    private String notArrangedAmountNumber="";
    @ApiModelProperty("未委托体积")
    private String notArrangedAmountVolume="";
    @ApiModelProperty("未委托预计重量")
    private String notArrangedAmountExpectWeight="";
    @ApiModelProperty("退回件数")
    private String backAmountNumber="";
    @ApiModelProperty("退回体积")
    private String backAmountVolume="";
    @ApiModelProperty("退回预计重量")
    private String backAmountExpectWeight="";

}
