package com.logistics.tms.controller.carrierdriverrel.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/10/14 13:36
 */
@Data
public class SearchCarrierDriverListResponseModel {

    @ApiModelProperty("车主司机关系表id")
    private Long carrierDriverId;

    @ApiModelProperty("姓名")
    private String staffName;

    @ApiModelProperty("性别")
    private Integer staffGender;

    @ApiModelProperty("年龄")
    private Integer staffAge;

    @ApiModelProperty("手机号")
    private String staffMobile;

    @ApiModelProperty("身份证号")
    private String identityNumber;

    @ApiModelProperty("状态：启用 1 禁用 0")
    private Integer enabled;

    @ApiModelProperty("新增时间")
    private Date createdTime;

    @ApiModelProperty("新增人")
    private String createdBy;

    @ApiModelProperty("实名认证(个人): 0 待实名 1 实名中 2 已实名")
    private Integer realNameAuthenticationStatus;
}
