package com.logistics.management.webapi.controller.carrierorderotherfee.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class CommitCarrierOrderOtherFeeRequestDto {

    @ApiModelProperty(value = "临时费用id",required = true)
    @NotBlank(message = "id不能为空")
    private String carrierOrderOtherFeeId;

    @ApiModelProperty(value = "临时费用",required = true)
    @NotEmpty(message = "请维护临时费用")
    @Valid
    private List<CommitCarrierOrderOtherFeeItemRequestDto> otherFeeList;

    @ApiModelProperty(value = "操作类型：1 提交，2 审核通过",required = true)
    @NotBlank(message = "操作类型不能为空")
    @Range(min = 1, max = 2, message = "操作类型不能为空")
    private String operateType;

    @ApiModelProperty("备注")
    @Size(max = 300, message = "备注不能超过300字")
    private String remark;
}
