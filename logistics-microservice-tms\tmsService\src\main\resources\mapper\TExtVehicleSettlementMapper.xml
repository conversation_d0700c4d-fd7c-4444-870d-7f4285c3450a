<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TExtVehicleSettlementMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TExtVehicleSettlement">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="carrier_order_id" jdbcType="BIGINT" property="carrierOrderId" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
    <result column="staff_mobile" jdbcType="VARCHAR" property="staffMobile" />
    <result column="driver_total_fee" jdbcType="DECIMAL" property="driverTotalFee" />
    <result column="settlement_unit" jdbcType="INTEGER" property="settlementUnit" />
    <result column="settlement_amount" jdbcType="DECIMAL" property="settlementAmount" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, carrier_order_id, vehicle_id, vehicle_no, staff_id, staff_name, staff_mobile, 
    driver_total_fee, settlement_unit, settlement_amount, status, remark, created_by, 
    created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_ext_vehicle_settlement
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_ext_vehicle_settlement
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TExtVehicleSettlement">
    insert into t_ext_vehicle_settlement (id, carrier_order_id, vehicle_id, 
      vehicle_no, staff_id, staff_name, 
      staff_mobile, driver_total_fee, settlement_unit, 
      settlement_amount, status, remark, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{carrierOrderId,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, 
      #{vehicleNo,jdbcType=VARCHAR}, #{staffId,jdbcType=BIGINT}, #{staffName,jdbcType=VARCHAR}, 
      #{staffMobile,jdbcType=VARCHAR}, #{driverTotalFee,jdbcType=DECIMAL}, #{settlementUnit,jdbcType=INTEGER}, 
      #{settlementAmount,jdbcType=DECIMAL}, #{status,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TExtVehicleSettlement" keyProperty="id" useGeneratedKeys="true">
    insert into t_ext_vehicle_settlement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="carrierOrderId != null">
        carrier_order_id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleNo != null">
        vehicle_no,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="staffName != null">
        staff_name,
      </if>
      <if test="staffMobile != null">
        staff_mobile,
      </if>
      <if test="driverTotalFee != null">
        driver_total_fee,
      </if>
      <if test="settlementUnit != null">
        settlement_unit,
      </if>
      <if test="settlementAmount != null">
        settlement_amount,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderId != null">
        #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffName != null">
        #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null">
        #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="driverTotalFee != null">
        #{driverTotalFee,jdbcType=DECIMAL},
      </if>
      <if test="settlementUnit != null">
        #{settlementUnit,jdbcType=INTEGER},
      </if>
      <if test="settlementAmount != null">
        #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TExtVehicleSettlement">
    update t_ext_vehicle_settlement
    <set>
      <if test="carrierOrderId != null">
        carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffName != null">
        staff_name = #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null">
        staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="driverTotalFee != null">
        driver_total_fee = #{driverTotalFee,jdbcType=DECIMAL},
      </if>
      <if test="settlementUnit != null">
        settlement_unit = #{settlementUnit,jdbcType=INTEGER},
      </if>
      <if test="settlementAmount != null">
        settlement_amount = #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TExtVehicleSettlement">
    update t_ext_vehicle_settlement
    set carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      staff_id = #{staffId,jdbcType=BIGINT},
      staff_name = #{staffName,jdbcType=VARCHAR},
      staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      driver_total_fee = #{driverTotalFee,jdbcType=DECIMAL},
      settlement_unit = #{settlementUnit,jdbcType=INTEGER},
      settlement_amount = #{settlementAmount,jdbcType=DECIMAL},
      status = #{status,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>