package com.logistics.tms.api.feign.vehicletire.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.vehicletire.VehicleTireServiceApi;
import com.logistics.tms.api.feign.vehicletire.model.*;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("tmsVehicleTireServiceApiHystrix")
public class VehicleTireServiceApiHystrix implements VehicleTireServiceApi {
    @Override
    public Result<PageInfo<VehicleTireListResponseModel>> searchVehicleTireList(VehicleTireListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<VehicleTireDetailResponseModel> getVehicleTireDetail(VehicleTireIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addOrModifyVehicleTire(AddOrModifyVehicleTireRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> deleteVehicleTire(VehicleTireIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<VehicleTireListResponseModel>> export(VehicleTireListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ImportVehicleTireInfoResponseModel> importExcelInfoVehicleTireInfo(ImportVehicleTireInfoRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> importVehicleTireCertificateInfo(ImportTireCertificateRequestModel requestModel) {
        return Result.timeout();
    }
}
