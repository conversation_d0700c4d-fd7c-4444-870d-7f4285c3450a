package com.logistics.tms.job;

import com.logistics.tms.biz.reservationorder.ReservationOrderCommonBiz;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @author: wjf
 * @date: 2024/8/26 9:35
 */
@Slf4j
@Component
public class ReservationOrderScheduledTasks {

    @Resource
    private ReservationOrderCommonBiz reservationOrderCommonBiz;

    /**
     * 预约单到期未签到后自动失效（一天一次（0点30分））
     */
    @XxlJob("logisticsTmsReservationOrderInvalid")
    public void reservationOrderInvalid(){
        try{
            log.info("tms定时任务：预约单到期未签到后自动失效-开始");
            reservationOrderCommonBiz.reservationOrderInvalid();
            log.info("tms定时任务：合同有效期到期后自动终止-结束");
        }catch(Exception e){
            log.error("定时任务，预约单到期未签到后自动失效: ", e);
        }
    }

}
