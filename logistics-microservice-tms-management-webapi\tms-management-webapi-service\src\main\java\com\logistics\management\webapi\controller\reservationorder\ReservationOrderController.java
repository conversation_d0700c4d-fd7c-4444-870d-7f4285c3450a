package com.logistics.management.webapi.controller.reservationorder;


import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.reservation.ReservationClient;
import com.logistics.management.webapi.client.reservation.request.ReservationOrderSearchListForManagementWebReqModel;
import com.logistics.management.webapi.client.reservation.response.ReservationOrderSearchListForManagementWebResModel;
import com.logistics.management.webapi.controller.reservationorder.dto.ReservationOrderSearchListRequestDto;
import com.logistics.management.webapi.controller.reservationorder.dto.ReservationOrderSearchListResponseDto;
import com.logistics.management.webapi.controller.reservationorder.mapping.ReservationOrderSearchListMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "预约配置")
@RestController
public class ReservationOrderController {

    @Resource
    ReservationClient reservationClient;

    /**
     * 预约单列表 (v1.2.1)
     */
    @ApiOperation(value = "预约单列表 V2.45", tags = " V2.45")
    @PostMapping(value = "/api/reservationOrder/searchList")
    public Result<PageInfo<ReservationOrderSearchListResponseDto>> searchList(@RequestBody ReservationOrderSearchListRequestDto requestDto) {
        ReservationOrderSearchListForManagementWebReqModel reqModel = MapperUtils.mapperNoDefault(requestDto, ReservationOrderSearchListForManagementWebReqModel.class);
        Result<PageInfo<ReservationOrderSearchListForManagementWebResModel>> result = reservationClient.searchListForManagementWeb(reqModel);
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<ReservationOrderSearchListResponseDto> responseDtos = MapperUtils.mapper(pageInfo.getList(), ReservationOrderSearchListResponseDto.class,new ReservationOrderSearchListMapping());
        pageInfo.setList(responseDtos);
        return Result.success(pageInfo);
    }



}