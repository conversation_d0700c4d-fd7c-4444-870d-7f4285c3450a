package com.logistics.tms.controller.renewableaudit.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/16 14:00
 */
@Data
public class RenewableAuditRequestModel extends AbstractPageForm<RenewableAuditRequestModel> {

    @ApiModelProperty(value = "新生订单审核列表id")
    private List<Long> renewableOrderIds;

    @ApiModelProperty(value = "单号")
    private String renewableOrderCode;

    @ApiModelProperty(value = "客户")
    private String customerName;

    @ApiModelProperty(value = "业务类型:1:公司,2:个人")
    private Integer businessType;

    @ApiModelProperty(value = "订单状态:0:待指派,1:待确认,2:待审核,3:已审核,4:已取消")
    private Integer status;

    @ApiModelProperty(value = "发货地址")
    private String loadDetailAddress;

    @ApiModelProperty(value = "发货联系人")
    private String consignor;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;

    @ApiModelProperty(value = "司机")
    private String driver;

    @ApiModelProperty(value = "收货仓库")
    private String unloadWarehouse;

    @ApiModelProperty(value = "收货地址")
    private String unloadDetailAddress;

    @ApiModelProperty(value = "收货联系人")
    private String receiver;

    @ApiModelProperty(value = "下单开始时间")
    private String publishStartTime;

    @ApiModelProperty(value = "下单结束时间")
    private String publishEndTime;

    @ApiModelProperty(value = "审核开始时间")
    private String auditStartTime;

    @ApiModelProperty(value = "审核结束时间")
    private String auditEndTime;
}
