package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2024/07/09
*/
@Data
public class TRouteEnquiryAddress extends BaseEntity {
    /**
    * 路线询价单表id
    */
    @ApiModelProperty("路线询价单表id")
    private Long routeEnquiryId;

    /**
    * 发货省份ID
    */
    @ApiModelProperty("发货省份ID")
    private Long fromProvinceId;

    /**
    * 发货省份名字
    */
    @ApiModelProperty("发货省份名字")
    private String fromProvinceName;

    /**
    * 发货城市ID
    */
    @ApiModelProperty("发货城市ID")
    private Long fromCityId;

    /**
    * 发货城市名字
    */
    @ApiModelProperty("发货城市名字")
    private String fromCityName;

    /**
    * 发货县区ID
    */
    @ApiModelProperty("发货县区ID")
    private Long fromAreaId;

    /**
    * 发货县区名字
    */
    @ApiModelProperty("发货县区名字")
    private String fromAreaName;

    /**
    * 发货仓库
    */
    @ApiModelProperty("发货仓库")
    private String fromWarehouse;

    /**
    * 收货省份ID
    */
    @ApiModelProperty("收货省份ID")
    private Long toProvinceId;

    /**
    * 收货省份名字
    */
    @ApiModelProperty("收货省份名字")
    private String toProvinceName;

    /**
    * 收货城市ID
    */
    @ApiModelProperty("收货城市ID")
    private Long toCityId;

    /**
    * 收货城市名字
    */
    @ApiModelProperty("收货城市名字")
    private String toCityName;

    /**
    * 收货县区id
    */
    @ApiModelProperty("收货县区id")
    private Long toAreaId;

    /**
    * 收货县区名字
    */
    @ApiModelProperty("收货县区名字")
    private String toAreaName;

    /**
    * 距离（KM）
    */
    @ApiModelProperty("距离（KM）")
    private BigDecimal distance;

    /**
    * 报价金额类型：1 单价，2 一口价
    */
    @ApiModelProperty("报价金额类型：1 单价，2 一口价")
    private Integer quotePriceType;

    /**
    * 报价金额
    */
    @ApiModelProperty("报价金额")
    private BigDecimal quotePrice;

    /**
    * 报价备注
    */
    @ApiModelProperty("报价备注")
    private String quoteRemark;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}