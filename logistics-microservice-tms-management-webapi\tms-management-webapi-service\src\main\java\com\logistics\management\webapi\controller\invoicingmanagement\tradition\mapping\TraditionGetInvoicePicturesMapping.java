package com.logistics.management.webapi.controller.invoicingmanagement.tradition.mapping;

import com.logistics.management.webapi.client.invoicingmanagement.response.GetInvoicePicturesResponseModel;
import com.logistics.management.webapi.controller.invoicingmanagement.tradition.response.TraditionGetInvoicePicturesResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

import java.util.Map;

/**
 * @author: wjf
 * @date: 2024/3/21 14:27
 */
public class TraditionGetInvoicePicturesMapping extends MapperMapping<GetInvoicePicturesResponseModel, TraditionGetInvoicePicturesResponseDto> {

    private final String imagePrefix;
    private final Map<String, String> imageMap;
    public TraditionGetInvoicePicturesMapping(String imagePrefix , Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        GetInvoicePicturesResponseModel source = getSource();
        TraditionGetInvoicePicturesResponseDto destination = getDestination();

        if (StringUtils.isNotBlank(source.getInvoicePicture())){
            destination.setInvoicePictureUrl(imagePrefix + imageMap.get(source.getInvoicePicture()));
        }
    }
}
