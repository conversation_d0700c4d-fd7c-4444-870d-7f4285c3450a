package com.logistics.appapi.controller.driversafemeeting;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.controller.common.CommonBiz;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.constant.ConfigKeyConstant;
import com.logistics.appapi.client.driversafemeeting.DriverSafeMeetingClient;
import com.logistics.appapi.client.driversafemeeting.request.AppletConfirmLeaningRequestModel;
import com.logistics.appapi.client.driversafemeeting.request.AppletSafeMeetingListRequestModel;
import com.logistics.appapi.client.driversafemeeting.request.DriverSafeMeetingRelationIdRequestModel;
import com.logistics.appapi.client.driversafemeeting.response.AppletSafeMeetingDetailResponseModel;
import com.logistics.appapi.client.driversafemeeting.response.AppletSafeMeetingListResponseModel;
import com.logistics.appapi.client.driversafemeeting.response.DriverSafeMeetingListCountResponseModel;
import com.logistics.appapi.controller.driversafemeeting.mapping.SafeMeetingDetailMapping;
import com.logistics.appapi.controller.driversafemeeting.mapping.SafeMeetingListMapping;
import com.logistics.appapi.controller.driversafemeeting.request.ConfirmLeaningRequestDto;
import com.logistics.appapi.controller.driversafemeeting.request.DriverSafeMeetingRelationIdRequestDto;
import com.logistics.appapi.controller.driversafemeeting.request.SafeMeetingListRequestDto;
import com.logistics.appapi.controller.driversafemeeting.response.DriverSafeMeetingListCountResponseDto;
import com.logistics.appapi.controller.driversafemeeting.response.SafeMeetingDetailResponseDto;
import com.logistics.appapi.controller.driversafemeeting.response.SafeMeetingListResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2024/3/11 14:42
 */
@Api(value = "安全例会", tags = "安全例会")
@RestController
@RequestMapping(value = "/api/driverSafeMeeting")
public class DriverSafeMeetingController {

    @Resource
    private DriverSafeMeetingClient driverSafeMeetingClient;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 小程序学习列表
     * @param requestDto
     * @return
     */
    @ApiOperation(("小程序学习列表"))
    @PostMapping(value = "/appletSafeMeetingList")
    public Result<PageInfo<SafeMeetingListResponseDto>> appletSafeMeetingList(@RequestBody SafeMeetingListRequestDto requestDto) {
        Result<PageInfo<AppletSafeMeetingListResponseModel>> result = driverSafeMeetingClient.appletSafeMeetingList(MapperUtils.mapper(requestDto, AppletSafeMeetingListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SafeMeetingListResponseDto> list = MapperUtils.mapper(pageInfo.getList(),SafeMeetingListResponseDto.class,new SafeMeetingListMapping());
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    /**
     * 小程序学习列表数量统计
     * @return
     */
    @ApiOperation(("小程序学习列表数量统计"))
    @PostMapping(value = "/appletSafeMeetingListCount")
    public Result<DriverSafeMeetingListCountResponseDto> appletSafeMeetingListCount() {
        Result<DriverSafeMeetingListCountResponseModel> result = driverSafeMeetingClient.appletSafeMeetingListCount();
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),DriverSafeMeetingListCountResponseDto.class));
    }

    /**
     * 小程序学习详情
     * @param requestDto
     * @return
     */
    @ApiOperation(("小程序学习详情"))
    @PostMapping(value = "/appletSafeMeetingDetail")
    public Result<SafeMeetingDetailResponseDto> appletSafeMeetingDetail(@RequestBody @Valid DriverSafeMeetingRelationIdRequestDto requestDto) {
        Result<AppletSafeMeetingDetailResponseModel> result = driverSafeMeetingClient.appletSafeMeetingDetail(MapperUtils.mapper(requestDto, DriverSafeMeetingRelationIdRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        sourceSrcList.add(result.getData().getStaffDriverImageUrl());
        sourceSrcList.add(result.getData().getSignImageUrl());
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(),SafeMeetingDetailResponseDto.class,new SafeMeetingDetailMapping(configKeyConstant.fileAccessAddress,imageMap)));
    }

    /**
     * 小程序提交学习
     * @param requestDto
     * @return
     */
    @ApiOperation(("小程序提交学习"))
    @PostMapping(value = "/appletConfirmLeaning")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result appletConfirmLeaning(@RequestBody @Valid ConfirmLeaningRequestDto requestDto) {
        return Result.success(driverSafeMeetingClient.appletConfirmLeaning(MapperUtils.mapper(requestDto,AppletConfirmLeaningRequestModel.class)));
    }
}
