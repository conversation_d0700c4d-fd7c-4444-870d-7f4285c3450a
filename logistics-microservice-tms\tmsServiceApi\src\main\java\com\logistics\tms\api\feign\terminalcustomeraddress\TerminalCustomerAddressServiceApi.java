package com.logistics.tms.api.feign.terminalcustomeraddress;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.terminalcustomeraddress.hystrix.TerminalCustomerAddressServiceApiHystrix;
import com.logistics.tms.api.feign.terminalcustomeraddress.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/1/4 16:12
 */
@Api(value = "API-TerminalCustomerAddressServiceApi-终端客户地址管理")
@FeignClient(name = "logistics-tms-services", fallback = TerminalCustomerAddressServiceApiHystrix.class)
public interface TerminalCustomerAddressServiceApi {

    @ApiOperation(value = "终端客户地址列表")
    @PostMapping(value = "/service/terminalCustomerAddress/searchTerminalCustomerAddressList")
    Result<PageInfo<SearchTerminalCustomerAddressListResponseModel>> searchTerminalCustomerAddressList(@RequestBody SearchTerminalCustomerAddressListRequestModel requestModel);

    @ApiOperation(value = "导出终端客户地址列表")
    @PostMapping(value = "/service/terminalCustomerAddress/exportTerminalCustomerAddressList")
    Result<List<SearchTerminalCustomerAddressListResponseModel>> exportTerminalCustomerAddressList(@RequestBody SearchTerminalCustomerAddressListRequestModel requestModel);

    @ApiOperation(value = "新增/修改终端客户地址")
    @PostMapping(value = "/service/terminalCustomerAddress/addOrModifyTerminalCustomerAddress")
    Result<Boolean> addOrModifyTerminalCustomerAddress(@RequestBody AddOrModifyTerminalCustomerAddressRequestModel requestModel);

    @ApiOperation(value = "终端客户地址详情")
    @PostMapping(value = "/service/terminalCustomerAddress/getTerminalCustomerAddressDetail")
    Result<GetTerminalCustomerAddressDetailResponseModel> getTerminalCustomerAddressDetail(@RequestBody GetTerminalCustomerAddressDetailRequestModel requestModel);

    @ApiOperation(value = "批量删除终端客户地址")
    @PostMapping(value = "/service/terminalCustomerAddress/delTerminalCustomerAddress")
    Result<Boolean> delTerminalCustomerAddress(@RequestBody DeleteTerminalCustomerAddressRequestModel requestModel);
}
