<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TAttendanceChangeApplyMapper">
    <!--auto generated by <PERSON>batisCodeHelper on 2022-11-15-->
    <select id="selectByAttendanceRecordIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_attendance_change_apply
        where valid = 1
          and attendance_record_id in
        <foreach item="item" index="index" collection="attendanceRecordIdCollection" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-11-16-->
    <select id="selectAllByIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_attendance_change_apply
        where valid = 1
          and id in
        <foreach item="item" index="index" collection="idCollection" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-11-18-->
    <select id="selectSearchAttendanceChangeList" resultType="com.logistics.tms.controller.attendance.response.SearchAttendanceChangeListResponseModel">
        select
        taca.id attendanceChangeApplyId,
        taca.audit_status auditStatus,
        taca.change_type changeType,
        taca.change_reason changeReason,
        taca.change_punch_time changePunchTime,
        taca.audit_time auditTime,
        taca.auditor_name auditorName,
        tar.staff_name staffName,
        tar.staff_mobile staffMobile,
        tar.staff_property staffProperty,
        tar.attendance_date attendanceDate
        from t_attendance_change_apply taca
        left join t_attendance_record tar on tar.id = taca.attendance_record_id and tar.valid = 1
        where taca.valid = 1
        <if test="params.attendanceUser != null and params.attendanceUser != ''">
            and (instr(tar.staff_name, #{params.attendanceUser})
            or instr(tar.staff_mobile, #{params.attendanceUser}))
        </if>
        <if test="params.staffProperty != null">
            and tar.staff_property = #{params.staffProperty}
        </if>
        <if test="params.auditStatus != null ">
            and taca.audit_status = #{params.auditStatus}
        </if>
        <if test="(params.attendanceStartTime != null and params.attendanceStartTime != '') and (params.attendanceEndTime != null and params.attendanceEndTime != '')">
            and (taca.change_punch_time >= DATE_FORMAT(#{params.attendanceStartTime}, '%Y-%m-%d 00:00:00' )
            and taca.change_punch_time &lt;= DATE_FORMAT(#{params.attendanceEndTime}, '%Y-%m-%d 23:59:59' ))
        </if>
        <if test="(params.operateStartTime != null and params.operateStartTime != '') and (params.operateEndTime != null and params.operateEndTime != '')">
            and (taca.last_modified_time >= DATE_FORMAT(#{params.operateStartTime}, '%Y-%m-%d 00:00:00' )
            and taca.last_modified_time &lt;= DATE_FORMAT(#{params.operateEndTime}, '%Y-%m-%d 23:59:59' ))
        </if>
        order by taca.last_modified_time desc, taca.id desc
    </select>

<!--auto generated by MybatisCodeHelper on 2022-11-21-->
    <update id="updateByIdAndAuditStatus">
        update t_attendance_change_apply
        <set>
            <if test="updated.changePunchTime != null">
                change_punch_time = #{updated.changePunchTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.changeReason != null">
                change_reason = #{updated.changeReason,jdbcType=VARCHAR},
            </if>
            <if test="updated.auditStatus != null">
                audit_status = #{updated.auditStatus,jdbcType=INTEGER},
            </if>
            <if test="updated.auditorName != null">
                auditor_name = #{updated.auditorName,jdbcType=VARCHAR},
            </if>
            <if test="updated.auditTime != null">
                audit_time = #{updated.auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.remark != null">
                remark = #{updated.remark,jdbcType=VARCHAR},
            </if>
            <if test="updated.lastModifiedBy != null">
                last_modified_by = #{updated.lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="updated.lastModifiedTime != null">
                last_modified_time = #{updated.lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where valid = 1 and id=#{id} and audit_status = #{auditStatus}
    </update>
</mapper>