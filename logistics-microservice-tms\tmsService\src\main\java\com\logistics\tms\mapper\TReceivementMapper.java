package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.biz.carrierorder.model.GetSettlementByDemandOrderIdModel;
import com.logistics.tms.entity.TReceivement;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TReceivementMapper extends BaseMapper<TReceivement> {

    int batchInsert(@Param("list") List<TReceivement> list);

    int batchUpdate(@Param("list") List<TReceivement> list);

    List<GetSettlementByDemandOrderIdModel> getSettlementByDemandOrderId(@Param("demandOrderId")Long demandOrderId);

    TReceivement getByCarrierOrderId(@Param("carrierOrderId")Long carrierOrderId);

	List<TReceivement> getByCarrierOrderIds(@Param("carrierOrderIds") String carrierOrderIds);

    void batchUpdateByCarrierOrderIds(@Param("list") List<TReceivement> tReceivementUpList);
}