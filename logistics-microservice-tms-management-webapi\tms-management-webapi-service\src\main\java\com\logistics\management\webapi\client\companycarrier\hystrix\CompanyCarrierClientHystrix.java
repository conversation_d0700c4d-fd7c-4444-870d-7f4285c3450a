package com.logistics.management.webapi.client.companycarrier.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.companycarrier.CompanyCarrierClient;
import com.logistics.management.webapi.client.companycarrier.request.*;
import com.logistics.management.webapi.client.companycarrier.response.CompanyCarrierDetailResponseModel;
import com.logistics.management.webapi.client.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.management.webapi.client.companycarrier.response.SearchCompanyCarrierListResponseModel;
import com.logistics.management.webapi.client.companycarrier.response.UserCompanyCarrierInfoResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CompanyCarrierClientHystrix implements CompanyCarrierClient {
    @Override
    public Result<PageInfo<SearchCompanyCarrierListResponseModel>> searchList(SearchCompanyCarrierListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CompanyCarrierDetailResponseModel> getDetail(CompanyCarrierDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> saveOrUpdate(SaveOrModifyCompanyCarrierRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<FuzzySearchCompanyCarrierResponseModel>> fuzzyQuery(FuzzySearchCompanyCarrierRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<UserCompanyCarrierInfoResponseModel> getUserAndCompanyInfo() {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> openOrClose(OpenCloseBlacklistRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> openOrCloseLessThanTruckload(OpenCloseLessThanTruckloadRequestModel requestModel) {
        return Result.timeout();
    }
}
