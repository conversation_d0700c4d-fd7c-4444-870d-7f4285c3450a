package com.logistics.management.webapi.api.impl.driversafepromise.mapping;

import com.logistics.management.webapi.api.feign.driversafepromise.dto.SearchSignSafePromiseListResponseDto;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.SafePromiseStatusEnum;
import com.logistics.management.webapi.base.enums.StaffPropertyEnum;
import com.logistics.tms.api.feign.driversafepromise.model.SearchSignSafePromiseListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/11/5 11:40
 */
public class SearchSignListMapping extends MapperMapping<SearchSignSafePromiseListResponseModel,SearchSignSafePromiseListResponseDto> {
    private ConfigKeyConstant configKeyConstant;
    private Map<String,String> imageMap;

    public SearchSignListMapping(ConfigKeyConstant configKeyConstant,Map<String,String>imageMap){
        this.configKeyConstant = configKeyConstant;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        SearchSignSafePromiseListResponseModel source = this.getSource();
        SearchSignSafePromiseListResponseDto dto = this.getDestination();

        if(source!=null) {
            dto.setStatusLabel(SafePromiseStatusEnum.getEnum(source.getStatus()).getValue());
            if (StringUtils.isNotBlank(source.getHandPromiseUrl())) {
                dto.setAbsoluteHandPromiseUrl(configKeyConstant.fileAccessAddress + imageMap.get(source.getHandPromiseUrl()));
            }
            if (StringUtils.isNotBlank(source.getSignResponsibilityUrl())) {
                dto.setAbsoluteSignResponsibilityUrl(configKeyConstant.fileAccessAddress + imageMap.get(source.getSignResponsibilityUrl()));
            }
            //人员机构展示文本
            dto.setStaffPropertyLabel(StaffPropertyEnum.getEnum(source.getStaffProperty()).getValue());
        }
    }
}
