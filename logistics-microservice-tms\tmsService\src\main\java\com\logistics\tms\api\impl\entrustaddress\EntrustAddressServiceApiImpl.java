package com.logistics.tms.api.impl.entrustaddress;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.entrustaddress.EntrustAddressServiceApi;
import com.logistics.tms.api.feign.entrustaddress.model.GetAddressByCompanyNameRequestModel;
import com.logistics.tms.api.feign.entrustaddress.model.GetAddressByCompanyNameResponseModel;
import com.logistics.tms.api.feign.entrustaddress.model.SearchEntrustAddressRequestModel;
import com.logistics.tms.api.feign.entrustaddress.model.SearchEntrustAddressResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.entrustaddress.EntrustAddressBiz;
import com.yelo.tools.utils.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/9/19 11:52
 */
@RestController
public class EntrustAddressServiceApiImpl implements EntrustAddressServiceApi {

    @Autowired
    private EntrustAddressBiz entrustAddressBiz;

    /**
     * 根据仓库或详细地址模糊搜索带出地址（委托发布）
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<GetAddressByCompanyNameResponseModel>> getAddressByCompanyNameOrWarehouse(@RequestBody GetAddressByCompanyNameRequestModel requestModel) {
        return Result.success(entrustAddressBiz.getAddressByCompanyNameOrWarehouse(requestModel));
    }

    /**
     * 收发货地址列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<SearchEntrustAddressResponseModel>> searchList(@RequestBody SearchEntrustAddressRequestModel requestModel) {
        return Result.success(new PageInfo<>(entrustAddressBiz.searchList(requestModel)));
    }

    /**
     * 导出
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchEntrustAddressResponseModel>> export(@RequestBody SearchEntrustAddressRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        List<SearchEntrustAddressResponseModel> list = entrustAddressBiz.searchList(requestModel);
        if (ListUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        return Result.success(list);
    }
}
