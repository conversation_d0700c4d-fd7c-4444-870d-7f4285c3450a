package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2021/10/18 17:01
 */
@Data
public class LogisticsCostStatisticsResponseDto {
    @ApiModelProperty("统计年月")
    private String yearMonth="";
    @ApiModelProperty("发货类型：元/件（签收运单：司机费用/运单签收数量）")
    private String deliverCount = "";
    @ApiModelProperty("回收类型：元/件（签收运单：司机费用/运单签收数量）")
    private String recycleCount ="";
    @ApiModelProperty("调拨类型：元/件（签收运单：司机费用/运单签收数量）")
    private String transfersCount = "";
}
