package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class DemandOrderDispatchResponseDto {
    @ApiModelProperty("需求单货物id")
    private String demandOrderGoodsId="";
    @ApiModelProperty("需求单id")
    private String demandOrderId="";
    @ApiModelProperty("单号")
    private String demandOrderCode="";
    @ApiModelProperty("客户单号")
    private String customerOrderCode="";
    @ApiModelProperty("委托方id")
    private String companyEntrustId="";
    @ApiModelProperty("委托类型：0 非乐医，大于0 乐医")
    private String entrustType="";
    @ApiModelProperty("品名")
    private String goodsName="";
    @ApiModelProperty("规格")
    private String goodsSize="";
    @ApiModelProperty("货物单位 1 件 2 吨")
    private String goodsUnit="";
    @ApiModelProperty("长")
    private String length="";
    @ApiModelProperty("宽")
    private String width="";
    @ApiModelProperty("高")
    private String height="";
    @ApiModelProperty("待承运/待提件数")
    private String notArrangedAmount="";
    @ApiModelProperty("提货地址")
    private String loadDetailAddress="";
    @ApiModelProperty("卸货地址")
    private String unloadDetailAddress="";

    @ApiModelProperty("车主类型 1 公司 2 个人")
    private String carrierType ="";
    @ApiModelProperty("委托方id")
    private String companyCarrierId="";
    @ApiModelProperty("车主公司名")
    private String companyCarrierName ="";
    @ApiModelProperty("车主联系人")
    private String customerCarrierName ="";
    @ApiModelProperty("车主联系方式")
    private String customerCarrierMobile ="";
}
