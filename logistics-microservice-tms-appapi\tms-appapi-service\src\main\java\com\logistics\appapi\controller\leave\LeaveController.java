package com.logistics.appapi.controller.leave;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.client.leave.LeaveClient;
import com.logistics.appapi.client.leave.request.*;
import com.logistics.appapi.client.leave.response.DriverLeaveApplyDetailResponseModel;
import com.logistics.appapi.client.leave.response.DriverLeaveApplyListResponseModel;
import com.logistics.appapi.controller.leave.mapping.LeaveApplyDetailMapping;
import com.logistics.appapi.controller.leave.mapping.LeaveApplyListMapping;
import com.logistics.appapi.controller.leave.request.*;
import com.logistics.appapi.controller.leave.response.LeaveApplyDetailResponseDto;
import com.logistics.appapi.controller.leave.response.LeaveApplyListResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @author: wjf
 * @date: 2024/3/14 13:47
 */
@Api(value = "请假申请", tags = "请假申请")
@RestController
@RequestMapping(value = "/api/driverApplet/leave")
public class LeaveController {

    @Resource
    private LeaveClient leaveClient;

    /**
     * 请假申请
     * @param requestDto
     * @return boolean
     */
    @ApiOperation(value = "请假申请", tags = "1.0.6")
    @PostMapping(value = "/leaveApply")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> leaveApply(@RequestBody @Valid LeaveApplyRequestDto requestDto) {
        DriverLeaveApplyRequestModel requestModel = MapperUtils.mapper(requestDto, DriverLeaveApplyRequestModel.class);
        return leaveClient.driverLeaveApply(requestModel);
    }

    /**
     * 请假记录列表
     * @param requestDto
     * @return LeaveApplyListResponseDto
     */
    @ApiOperation(value = "请假记录查询", tags = "1.0.6")
    @PostMapping(value = "/leaveApplyList")
    public Result<LeaveApplyListResponseDto> leaveApplyList(@RequestBody @Valid LeaveApplyListRequestDto requestDto) {
        DriverLeaveApplyListRequestModel requestModel = MapperUtils.mapper(requestDto, DriverLeaveApplyListRequestModel.class);
        Result<DriverLeaveApplyListResponseModel> responseModelResult = leaveClient.driverLeaveApplyList(requestModel);
        responseModelResult.throwException();

        LeaveApplyListResponseDto responseDto = MapperUtils.mapper(responseModelResult.getData(), LeaveApplyListResponseDto.class,
                new LeaveApplyListMapping());
        return Result.success(responseDto);
    }

    /**
     * 请假记录详情
     * @param requestDto
     * @return LeaveApplyDetailResponseDto
     */
    @ApiOperation(value = "请假记录详情查询", tags = "1.0.6")
    @PostMapping(value = "/leaveApplyDetail")
    public Result<LeaveApplyDetailResponseDto> leaveApplyDetail(@RequestBody @Valid LeaveApplyDetailRequestDto requestDto) {
        DriverLeaveApplyDetailRequestModel requestModel = MapperUtils.mapper(requestDto, DriverLeaveApplyDetailRequestModel.class);
        Result<DriverLeaveApplyDetailResponseModel> responseModelResult = leaveClient.driverLeaveApplyDetail(requestModel);
        responseModelResult.throwException();

        LeaveApplyDetailResponseDto responseDto = MapperUtils.mapper(responseModelResult.getData(), LeaveApplyDetailResponseDto.class,
                new LeaveApplyDetailMapping());
        return Result.success(responseDto);
    }

    /**
     * 插销请假申请
     * @param requestDto
     * @return boolean
     */
    @ApiOperation(value = "撤销请假申请", tags = "1.0.6")
    @PostMapping(value = "/cancelLeaveApply")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> cancelLeaveApply(@RequestBody @Valid CancelLeaveApplyRequestDto requestDto) {
        DriverLeaveApplyCancelRequestModel requestModel = MapperUtils.mapper(requestDto, DriverLeaveApplyCancelRequestModel.class);
        return leaveClient.driverCancelLeaveApply(requestModel);
    }

    /**
     * 重新提交请假申请
     * @param requestDto
     * @return boolean
     */
    @ApiOperation(value = "重新提交请假申请", tags = "1.0.6")
    @PostMapping(value = "/resubmitLeaveApply")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> resubmitLeaveApply(@RequestBody @Valid ResubmitLeaveApplyRequestDto requestDto) {
        DriverLeaveApplyResubmitRequestModel requestModel = MapperUtils.mapper(requestDto, DriverLeaveApplyResubmitRequestModel.class);
        return leaveClient.driverResubmitLeaveApply(requestModel);
    }
}
