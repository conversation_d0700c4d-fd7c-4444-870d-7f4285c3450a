package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2022/08/17
*/
@Data
public class TDriverAppointGoods extends BaseEntity {
    /**
    * 司机预约表id
    */
    @ApiModelProperty("司机预约表id")
    private Long driverAppointId;

    /**
    * 新生sku编号
    */
    @ApiModelProperty("新生sku编号")
    private String renewableSkuCode;

    /**
    * 货物品名
    */
    @ApiModelProperty("货物品名")
    private String goodsName;

    /**
    * 货物数量
    */
    @ApiModelProperty("货物数量")
    private BigDecimal goodsAmount;

    /**
    * 货物单位：1 件，2 KG
    */
    @ApiModelProperty("货物单位：1 件，2 KG")
    private Integer goodsUnit;

    /**
    * 货物单价
    */
    @ApiModelProperty("货物单价")
    private BigDecimal goodsPrice;
}