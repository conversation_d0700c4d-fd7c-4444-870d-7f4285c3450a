package com.logistics.management.webapi.api.impl.renewableaudit.mapping;

import com.logistics.management.webapi.api.feign.renewableaudit.dto.RenewableAuditDetailResponseDto;
import com.logistics.management.webapi.api.feign.renewableaudit.dto.RenewableGoodsResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.tms.api.feign.renewableaudit.model.RenewableAuditDetailResponseModel;
import com.logistics.tms.api.feign.renewableaudit.model.RenewableAuditGoodsResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class RenewableAuditDetailMapping extends MapperMapping<RenewableAuditDetailResponseModel, RenewableAuditDetailResponseDto> {

    @Override
    public void configure() {
        RenewableAuditDetailResponseModel source = getSource();
        RenewableAuditDetailResponseDto destination = getDestination();

        //状态转换
        destination.setStatusDesc(RenewableAuditStatusEnum.getEnum(source.getStatus()).getValue());

        //来源，下单人转换
        if (RenewableAuditOrderSourceEnum.CUSTOMER_SOURCE.getCode().equals(source.getSource())) {
            destination.setSourceDesc(RenewableAuditOrderSourceEnum.CUSTOMER_SOURCE.getName());

            if (RenewableBusinessType.COMPANY.getCode().equals(source.getBusinessType())) {
                destination.setPublishUser(source.getCustomerName() + " " +
                        Optional.ofNullable(source.getPublishUserName()).orElse("") + " " +
                        Optional.ofNullable(source.getPublishUserMobile()).orElse(""));
            } else if (RenewableBusinessType.PERSON.getCode().equals(source.getBusinessType())) {
                destination.setPublishUser(Optional.ofNullable(source.getPublishUserName()).orElse("") + " " +
                        Optional.ofNullable(source.getPublishUserMobile()).orElse(""));
            }

        } else if (RenewableAuditOrderSourceEnum.DRIVER_SOURCE.getCode().equals(source.getSource())) {
            destination.setSourceDesc(RenewableAuditOrderSourceEnum.DRIVER_SOURCE.getName());
            destination.setPublishUser(Optional.ofNullable(source.getPublishUserName()).orElse("") + " " +
                    Optional.ofNullable(source.getPublishUserMobile()).orElse(""));
        }

        //发货地址
        if (StringUtils.isNotBlank(source.getLoadWarehouse())) {
            destination.setLoadDetailAddress("【" + source.getLoadWarehouse() + "】" +
                    Optional.ofNullable(source.getLoadProvinceName()).orElse("") +
                    Optional.ofNullable(source.getLoadCityName()).orElse("") +
                    Optional.ofNullable(source.getLoadAreaName()).orElse("") +
                    Optional.ofNullable(source.getLoadDetailAddress()).orElse(""));
        } else {
            destination.setLoadDetailAddress(Optional.ofNullable(source.getLoadProvinceName()).orElse("") +
                    Optional.ofNullable(source.getLoadCityName()).orElse("") +
                    Optional.ofNullable(source.getLoadAreaName()).orElse("") +
                    Optional.ofNullable(source.getLoadDetailAddress()).orElse(""));
        }
        //发货联系人转换
        destination.setConsignor(Optional.ofNullable(source.getConsignorName()).orElse("") + " " + Optional.ofNullable(source.getConsignorMobile()).orElse(""));

        //初始化地址
        destination.setUnloadDetailAddress(CommonConstant.BLANK_TEXT);
        //收货地址待审核或已审核展示
        if (RenewableAuditStatusEnum.WAIT_AUDIT.getKey().equals(source.getStatus()) || RenewableAuditStatusEnum.AUDIT_THROUGH.getKey().equals(source.getStatus())) {
            if (StringUtils.isNotBlank(source.getUnloadWarehouse())) {
                destination.setUnloadDetailAddress("【" + source.getUnloadWarehouse() + "】" +
                        Optional.ofNullable(source.getUnloadProvinceName()).orElse("") +
                        Optional.ofNullable(source.getUnloadCityName()).orElse("") +
                        Optional.ofNullable(source.getUnloadAreaName()).orElse("") +
                        Optional.ofNullable(source.getUnloadDetailAddress()).orElse(""));
            } else {
                destination.setUnloadDetailAddress(Optional.ofNullable(source.getUnloadProvinceName()).orElse("") +
                        Optional.ofNullable(source.getUnloadCityName()).orElse("") +
                        Optional.ofNullable(source.getUnloadAreaName()).orElse("") +
                        Optional.ofNullable(source.getUnloadDetailAddress()).orElse(""));
            }
            //收货联系人转换
            destination.setReceiver(Optional.ofNullable(source.getReceiverName()).orElse("") + " " +
                    Optional.ofNullable(source.getReceiverMobile()).orElse(""));
        }

        //货物信息转换
        if (ListUtils.isNotEmpty(source.getGoodsResponseModelList())) {
            //确认合计数量
            BigDecimal amountTotal = BigDecimal.ZERO;
            //确认合计价格
            BigDecimal aAmountTotalPrice = BigDecimal.ZERO;

            //审核信息下的所有货物
            List<RenewableAuditGoodsResponseModel> goodsResponseModelList = source.getGoodsResponseModelList();
            //存放货物信息
            RenewableGoodsResponseDto renewableGoodsResponseDto;
            //存放司机确认后的货物
            List<RenewableGoodsResponseDto> driverConfirmGoods = new ArrayList<>();
            //存放新生同步的原始货物
            List<RenewableGoodsResponseDto> syncGoods = new ArrayList<>();

            for (RenewableAuditGoodsResponseModel renewableAuditGoodsResponseModel : goodsResponseModelList) {
                //区分货物类型
                if (RenewableSourceTypeEnum.YELOLIFE_SYNC.getKey().equals(renewableAuditGoodsResponseModel.getGoodsSourceType())) {
                    //新生同步的原始货物
                    renewableGoodsResponseDto = new RenewableGoodsResponseDto();
                    renewableGoodsResponseDto.setGoodsName(renewableAuditGoodsResponseModel.getGoodsName());
                    renewableGoodsResponseDto.setSkuCode(renewableAuditGoodsResponseModel.getSkuCode());
                    //单位转换
                    if (RenewableGoodsUnitEnum.KILOGRAM.getKey().equals(renewableAuditGoodsResponseModel.getGoodsUnit())) {
                        renewableGoodsResponseDto.setGoodsAmount(ConverterUtils.toString(renewableAuditGoodsResponseModel.getGoodsAmount().stripTrailingZeros().toPlainString()) + RenewableGoodsUnitEnum.KILOGRAM.getValue());
                        renewableGoodsResponseDto.setGoodsPrice(ConverterUtils.toString(renewableAuditGoodsResponseModel.getGoodsPrice()) + CommonConstant.KILOGRAM_UNIT);

                    } else if (RenewableGoodsUnitEnum.PIECE.getKey().equals(renewableAuditGoodsResponseModel.getGoodsUnit())) {
                        renewableGoodsResponseDto.setGoodsAmount(ConverterUtils.toString(renewableAuditGoodsResponseModel.getGoodsAmount().stripTrailingZeros().toPlainString()) + RenewableGoodsUnitEnum.PIECE.getValue());
                        renewableGoodsResponseDto.setGoodsPrice(ConverterUtils.toString(renewableAuditGoodsResponseModel.getGoodsPrice()) + CommonConstant.PACKAGE_UNIT);
                    }

                    syncGoods.add(renewableGoodsResponseDto);

                } else if (RenewableSourceTypeEnum.DRIVER_CONFIRM.getKey().equals(renewableAuditGoodsResponseModel.getGoodsSourceType())) {

                    //确认货物信息待审核，已审核展示
                    if (RenewableAuditStatusEnum.WAIT_AUDIT.getKey().equals(source.getStatus()) || RenewableAuditStatusEnum.AUDIT_THROUGH.getKey().equals(source.getStatus())) {
                        //司机确认的货物
                        renewableGoodsResponseDto = new RenewableGoodsResponseDto();
                        renewableGoodsResponseDto.setGoodsName(renewableAuditGoodsResponseModel.getGoodsName());
                        renewableGoodsResponseDto.setSkuCode(renewableAuditGoodsResponseModel.getSkuCode());
                        if (RenewableGoodsUnitEnum.KILOGRAM.getKey().equals(renewableAuditGoodsResponseModel.getGoodsUnit())) {
                            renewableGoodsResponseDto.setVerifiedGoodsAmount(ConverterUtils.toString(renewableAuditGoodsResponseModel.getGoodsAmount().stripTrailingZeros().toPlainString()) + RenewableGoodsUnitEnum.KILOGRAM.getValue());
                            renewableGoodsResponseDto.setActualGoodsPrice(ConverterUtils.toString(renewableAuditGoodsResponseModel.getGoodsPrice()) + CommonConstant.KILOGRAM_UNIT);
                        } else if (RenewableGoodsUnitEnum.PIECE.getKey().equals(renewableAuditGoodsResponseModel.getGoodsUnit())) {
                            renewableGoodsResponseDto.setVerifiedGoodsAmount(ConverterUtils.toString(renewableAuditGoodsResponseModel.getGoodsAmount().stripTrailingZeros().toPlainString()) + RenewableGoodsUnitEnum.PIECE.getValue());
                            renewableGoodsResponseDto.setActualGoodsPrice(ConverterUtils.toString(renewableAuditGoodsResponseModel.getGoodsPrice()) + CommonConstant.PACKAGE_UNIT);
                        }
                        driverConfirmGoods.add(renewableGoodsResponseDto);
                        //确认数量和总价
                        amountTotal = amountTotal.add(Optional.ofNullable(renewableAuditGoodsResponseModel.getGoodsAmount()).orElse(BigDecimal.ZERO));
                        aAmountTotalPrice = aAmountTotalPrice.add(((Optional.ofNullable(renewableAuditGoodsResponseModel.getGoodsPrice()).orElse(BigDecimal.ZERO))
                                .multiply(Optional.ofNullable(renewableAuditGoodsResponseModel.getGoodsAmount()).orElse(BigDecimal.ZERO))).setScale(2, RoundingMode.HALF_UP));
                    }

                }
            }
            //合计吨位
            destination.setVerifiedGoodsAmountTotal(amountTotal.stripTrailingZeros().toPlainString() + CommonConstant.KILOGRAM);
            //应付价格
            destination.setVerifiedGoodsAmountTotalPrice(ConverterUtils.toString(aAmountTotalPrice.setScale(2, RoundingMode.HALF_UP)) + CommonConstant.YUAN);
            //封装货物信息
            destination.setGoodsResponseModelList(syncGoods);
            //确认货物信息
            destination.setVerifiedGoodsResponseModelList(driverConfirmGoods);
        }
    }
}
