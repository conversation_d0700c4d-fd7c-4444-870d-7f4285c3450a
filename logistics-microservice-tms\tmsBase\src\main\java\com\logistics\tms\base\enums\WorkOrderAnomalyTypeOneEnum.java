package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * 异常类型（一级）：10 联系不上客户，20 不想还盘，30 不可抗力，40 重复下单
 */
@Getter
@AllArgsConstructor
public enum WorkOrderAnomalyTypeOneEnum {

    DEFAULT(-1, ""),
    CAN_NOT_CONTACT(10, "联系不上客户"),
    REFUSAL_RETURN(20, "不想还盘"),
    FORCE_MAJEURE(30, "不可抗力"),
    REPETITION(40, "重复下单");

    private final Integer key;
    private final String value;

    public static WorkOrderAnomalyTypeOneEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
