package com.logistics.management.webapi.base.enums;

public enum CarrierOrderOtherFeeSourceEnum {

    DEFAULT(0,""),
    COMPANY(1,"我司"),
    CUSTOMER(2,"客戶");

    CarrierOrderOtherFeeSourceEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    private Integer key;
    private String value;

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static CarrierOrderOtherFeeSourceEnum getEnum(Integer key) {
        for (CarrierOrderOtherFeeSourceEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
