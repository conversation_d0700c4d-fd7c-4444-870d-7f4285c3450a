package com.logistics.management.webapi.client.freightconfig.request;


import lombok.Data;

import java.math.BigDecimal;

@Data
public class ConfigStringPointItemModel {


    /**
     * 起始数量
     */
    private BigDecimal countStart;


    /**
     * 结束数量
     */
    private BigDecimal countEnd;


    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 排序
     */
    private Integer sort;
}
