<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDriverCostDeductionMapper">
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_driver_cost_deduction
        where valid = 1
        <if test="queryModel.driverCostApplyIds != null and queryModel.driverCostApplyIds.size() > 0">
            and driver_cost_apply_id in
            <foreach collection="queryModel.driverCostApplyIds" item="costApplyId" open="(" close=")" separator=",">
                #{costApplyId}
            </foreach>
        </if>
        <if test="queryModel.reserveApplyCode != null and queryModel.reserveApplyCode != ''">
            and instr(reserve_apply_code, #{queryModel.reserveApplyCode})
        </if>
    </select>

    <insert id="batchInsert">
        insert into t_driver_cost_deduction (
        driver_cost_apply_id, reserve_apply_id,reserve_apply_code, reserve_type, balance_amount, verification_amount,
        created_by, created_time, last_modified_by,last_modified_time)
        <foreach collection="costDeductionList" item="item" open="values" separator="," close=";">
            (#{item.driverCostApplyId},
            #{item.reserveApplyId},
            #{item.reserveApplyCode},
            #{item.reserveType},
            #{item.balanceAmount},
            #{item.verificationAmount},
            #{item.createdBy},
            #{item.createdTime},
            #{item.lastModifiedBy},
            #{item.lastModifiedTime}
            )
        </foreach>
    </insert>
</mapper>