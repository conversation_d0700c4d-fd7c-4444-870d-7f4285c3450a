package com.logistics.management.webapi.api.feign.contractorder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ContractOrderSearchResponseDto {
    @ApiModelProperty("合同Id")
    private String contractId="";
    @ApiModelProperty("内部合同号")
    private String contractNoInternal="";
    @ApiModelProperty("外部合同号")
    private String contractNoExternal="";
    @ApiModelProperty("合同状态 1 待执行，2 执行中，3 已终止，4 已作废")
    private String contractStatus="";
    private String contractStatusDsc="";
    @ApiModelProperty("合同类型")
    private String contractType="";
    @ApiModelProperty("合同性质")
    private String contractNature="";
    @ApiModelProperty("客户")
    private String customerName="";
    @ApiModelProperty("合同有效期")
    private String contractValidTime="";
    @ApiModelProperty("操作人")
    private String lastModifiedBy="";
    @ApiModelProperty("最后操作时间")
    private String lastModifiedTime="";
}
