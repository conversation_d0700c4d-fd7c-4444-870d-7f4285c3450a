<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TVehicleSafeCheckReformMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TVehicleSafeCheckReform">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="safe_check_vehicle_id" jdbcType="BIGINT" property="safeCheckVehicleId" />
    <result column="reform_count" jdbcType="INTEGER" property="reformCount" />
    <result column="reform_content" jdbcType="VARCHAR" property="reformContent" />
    <result column="add_reform_time" jdbcType="TIMESTAMP" property="addReformTime" />
    <result column="reform_result" jdbcType="VARCHAR" property="reformResult" />
    <result column="add_reform_result_time" jdbcType="TIMESTAMP" property="addReformResultTime" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, safe_check_vehicle_id, reform_count, reform_content, add_reform_time, reform_result, 
    add_reform_result_time, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_vehicle_safe_check_reform
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_vehicle_safe_check_reform
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TVehicleSafeCheckReform">
    insert into t_vehicle_safe_check_reform (id, safe_check_vehicle_id, reform_count, 
      reform_content, add_reform_time, reform_result, 
      add_reform_result_time, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{safeCheckVehicleId,jdbcType=BIGINT}, #{reformCount,jdbcType=INTEGER}, 
      #{reformContent,jdbcType=VARCHAR}, #{addReformTime,jdbcType=TIMESTAMP}, #{reformResult,jdbcType=VARCHAR}, 
      #{addReformResultTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TVehicleSafeCheckReform" keyProperty="id" useGeneratedKeys="true">
    insert into t_vehicle_safe_check_reform
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="safeCheckVehicleId != null">
        safe_check_vehicle_id,
      </if>
      <if test="reformCount != null">
        reform_count,
      </if>
      <if test="reformContent != null">
        reform_content,
      </if>
      <if test="addReformTime != null">
        add_reform_time,
      </if>
      <if test="reformResult != null">
        reform_result,
      </if>
      <if test="addReformResultTime != null">
        add_reform_result_time,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="safeCheckVehicleId != null">
        #{safeCheckVehicleId,jdbcType=BIGINT},
      </if>
      <if test="reformCount != null">
        #{reformCount,jdbcType=INTEGER},
      </if>
      <if test="reformContent != null">
        #{reformContent,jdbcType=VARCHAR},
      </if>
      <if test="addReformTime != null">
        #{addReformTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reformResult != null">
        #{reformResult,jdbcType=VARCHAR},
      </if>
      <if test="addReformResultTime != null">
        #{addReformResultTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TVehicleSafeCheckReform">
    update t_vehicle_safe_check_reform
    <set>
      <if test="safeCheckVehicleId != null">
        safe_check_vehicle_id = #{safeCheckVehicleId,jdbcType=BIGINT},
      </if>
      <if test="reformCount != null">
        reform_count = #{reformCount,jdbcType=INTEGER},
      </if>
      <if test="reformContent != null">
        reform_content = #{reformContent,jdbcType=VARCHAR},
      </if>
      <if test="addReformTime != null">
        add_reform_time = #{addReformTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reformResult != null">
        reform_result = #{reformResult,jdbcType=VARCHAR},
      </if>
      <if test="addReformResultTime != null">
        add_reform_result_time = #{addReformResultTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TVehicleSafeCheckReform">
    update t_vehicle_safe_check_reform
    set safe_check_vehicle_id = #{safeCheckVehicleId,jdbcType=BIGINT},
      reform_count = #{reformCount,jdbcType=INTEGER},
      reform_content = #{reformContent,jdbcType=VARCHAR},
      add_reform_time = #{addReformTime,jdbcType=TIMESTAMP},
      reform_result = #{reformResult,jdbcType=VARCHAR},
      add_reform_result_time = #{addReformResultTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>