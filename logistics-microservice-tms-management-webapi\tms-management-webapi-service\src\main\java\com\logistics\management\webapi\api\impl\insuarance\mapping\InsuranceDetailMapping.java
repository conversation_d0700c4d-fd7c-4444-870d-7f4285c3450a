package com.logistics.management.webapi.api.impl.insuarance.mapping;

import com.logistics.management.webapi.api.feign.insuarance.dto.GetInsuranceDetailResponseDto;
import com.logistics.management.webapi.api.feign.insuarance.dto.InsuranceTicketsResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.tms.api.feign.insuarance.model.GetInsuranceDetailResponseModel;
import com.logistics.tms.api.feign.insuarance.model.InsuranceTicketsResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/6/5 12:55
 */
public class InsuranceDetailMapping extends MapperMapping<GetInsuranceDetailResponseModel, GetInsuranceDetailResponseDto> {
    private String imagePrefix;
    private Map<String,String> imageMap;
    public InsuranceDetailMapping(String imagePrefix, Map<String,String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        GetInsuranceDetailResponseModel source = getSource();
        GetInsuranceDetailResponseDto destination = getDestination();
        if (source != null) {
            Date now = new Date();
            destination.setInsuranceTypeDesc(InsuranceCoverageEnum.getEnum(source.getInsuranceType()).getValue());
            destination.setDriverName((StringUtils.isNotBlank(source.getDriverName()) ? source.getDriverName() + " " : "") + (StringUtils.isNotBlank(source.getDriverPhone()) ? source.getDriverPhone() : ""));
            if (InsuranceCoverageEnum.PERSONAL_INSURANCE.getKey().equals(source.getInsuranceType())) {
                destination.setSettlementStatus(SettlementStatusEnum.NULL.getKey().toString());
                destination.setSettlementStatusDesc(SettlementStatusEnum.NULL.getValue());
                if (source.getPolicyType() != null) {
                    destination.setPolicyTypeDesc(InsuranceTypeEnum.getEnum(source.getPolicyType()).getValue());
                }
                destination.setPolicyNumber(source.getPolicyNumberPerson());
                destination.setInsuranceCompanyId(source.getInsuranceCompanyIdPerson().toString());
                destination.setInsuranceCompanyName(source.getInsuranceCompanyNamePerson());
                if (source.getStartTimePerson() != null) {
                    destination.setStartTime(DateUtils.dateToString(source.getStartTimePerson(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
                }
                if (source.getEndTimePerson() != null) {
                    destination.setEndTime(DateUtils.dateToString(source.getEndTimePerson(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
                }
                destination.setPremium(ConverterUtils.toString(source.getGrossPremiumPerson().divide(ConverterUtils.toBigDecimal(source.getPolicyPersonCountPerson()), 2, BigDecimal.ROUND_HALF_UP)));
                if (source.getRelatedPersonalAccidentInsuranceId() != null) {
                    destination.setSinglePremium(ConverterUtils.toString(source.getRelatedGrossPremium().divide(ConverterUtils.toBigDecimal(source.getRelatedPolicyPersonCount()), 2, BigDecimal.ROUND_HALF_UP)));
                    if (source.getRelatedPolicyType().equals(InsuranceTypeEnum.POLICY.getKey())) {
                        destination.setRelatedPersonalAccidentInsuranceNo(source.getRelatedPolicyNumber());
                    } else if (source.getRelatedPolicyType().equals(InsuranceTypeEnum.BATCH.getKey())) {
                        destination.setRelatedPersonalAccidentInsuranceNo(source.getRelatedBatchNumber());
                    }
                }
                if (source.getStatusType().equals(InsuranceStatusTypeEnum.CANCEL.getKey())) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.CANCEL.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.CANCEL.getValue());
                }else if (source.getStatusType().equals(InsuranceStatusTypeEnum.REFUND.getKey())) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.REFUND.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.REFUND.getValue());
                } else if (source.getStartTimePerson().getTime() > now.getTime()) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.NO_START.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.NO_START.getValue());
                } else if (source.getEndTimePerson().getTime() < now.getTime()) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.OVERDUE.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.OVERDUE.getValue());
                } else if (source.getStartTimePerson().getTime() <= now.getTime() && source.getEndTimePerson().getTime() >= now.getTime()) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.VALID.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.VALID.getValue());
                }
            } else {
                if (source.getStartTime() != null && now.before(source.getStartTime())) {
                    destination.setSettlementStatus(SettlementStatusEnum.NULL.getKey().toString());
                    destination.setSettlementStatusDesc(SettlementStatusEnum.NULL.getValue());
                }
                if (source.getStartTime() != null) {
                    destination.setStartTime(DateUtils.dateToString(source.getStartTime(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
                }
                if (source.getEndTime() != null) {
                    destination.setEndTime(DateUtils.dateToString(source.getEndTime(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
                }
                if (source.getStatusType().equals(InsuranceStatusTypeEnum.CANCEL.getKey())) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.CANCEL.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.CANCEL.getValue());
                }else if (source.getStatusType().equals(InsuranceStatusTypeEnum.REFUND.getKey())) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.REFUND.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.REFUND.getValue());
                } else if (source.getStartTime().getTime() > now.getTime()) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.NO_START.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.NO_START.getValue());
                } else if (source.getEndTime().getTime() < now.getTime()) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.OVERDUE.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.OVERDUE.getValue());
                } else if (source.getStartTime().getTime() <= now.getTime() && source.getEndTime().getTime() >= now.getTime()) {
                    destination.setPolicyStatus(InsurancePolicyStatusEnum.VALID.getKeyStr());
                    destination.setPolicyStatusDesc(InsurancePolicyStatusEnum.VALID.getValue());
                }
            }

            if (ListUtils.isNotEmpty(source.getTicketList())) {
                for (InsuranceTicketsResponseDto dto:destination.getTicketList()) {
                    for (InsuranceTicketsResponseModel model:source.getTicketList()) {
                        if (model.getTicketId().toString().equals(dto.getTicketId())) {
                            dto.setFilePathSrc(imagePrefix + imageMap.get(model.getFilePath()));
                        }
                    }
                }
            }
            if (StringUtils.isNotBlank(source.getRefundPath())){
                destination.setRefundPath(imagePrefix + imageMap.get(source.getRefundPath()));
            }
        }
    }
}
