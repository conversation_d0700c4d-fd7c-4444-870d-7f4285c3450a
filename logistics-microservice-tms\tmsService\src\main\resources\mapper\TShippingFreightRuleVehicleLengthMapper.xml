<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TShippingFreightRuleVehicleLengthMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TShippingFreightRuleVehicleLength" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shipping_freight_address_id" property="shippingFreightAddressId" jdbcType="BIGINT" />
    <result column="vehicle_length" property="vehicleLength" jdbcType="DECIMAL" />
    <result column="count_start" property="countStart" jdbcType="DECIMAL" />
    <result column="count_end" property="countEnd" jdbcType="DECIMAL" />
    <result column="unit" property="unit" jdbcType="INTEGER" />
    <result column="price_type" property="priceType" jdbcType="INTEGER" />
    <result column="sort" property="sort" jdbcType="INTEGER" />
    <result column="price" property="price" jdbcType="DECIMAL" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, shipping_freight_address_id, vehicle_length, count_start, count_end, unit, price_type,
    sort, price, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_shipping_freight_rule_vehicle_length
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_shipping_freight_rule_vehicle_length
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TShippingFreightRuleVehicleLength" >
    insert into t_shipping_freight_rule_vehicle_length (id, shipping_freight_address_id, vehicle_length,
      count_start, count_end, unit, 
      price_type, sort, price, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{shippingFreightAddressId,jdbcType=BIGINT}, #{vehicleLength,jdbcType=DECIMAL},
      #{countStart,jdbcType=DECIMAL}, #{countEnd,jdbcType=DECIMAL}, #{unit,jdbcType=INTEGER}, 
      #{priceType,jdbcType=INTEGER}, #{sort,jdbcType=INTEGER}, #{price,jdbcType=DECIMAL}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TShippingFreightRuleVehicleLength" >
    insert into t_shipping_freight_rule_vehicle_length
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="shippingFreightAddressId != null" >
        shipping_freight_address_id,
      </if>
      <if test="vehicleLength != null" >
        vehicle_length,
      </if>
      <if test="countStart != null" >
        count_start,
      </if>
      <if test="countEnd != null" >
        count_end,
      </if>
      <if test="unit != null" >
        unit,
      </if>
      <if test="priceType != null" >
        price_type,
      </if>
      <if test="sort != null" >
        sort,
      </if>
      <if test="price != null" >
        price,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shippingFreightAddressId != null" >
        #{shippingFreightAddressId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLength != null" >
        #{vehicleLength,jdbcType=DECIMAL},
      </if>
      <if test="countStart != null" >
        #{countStart,jdbcType=DECIMAL},
      </if>
      <if test="countEnd != null" >
        #{countEnd,jdbcType=DECIMAL},
      </if>
      <if test="unit != null" >
        #{unit,jdbcType=INTEGER},
      </if>
      <if test="priceType != null" >
        #{priceType,jdbcType=INTEGER},
      </if>
      <if test="sort != null" >
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="price != null" >
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TShippingFreightRuleVehicleLength" >
    update t_shipping_freight_rule_vehicle_length
    <set >
      <if test="shippingFreightAddressId != null" >
        shipping_freight_address_id = #{shippingFreightAddressId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLength != null" >
        vehicle_length = #{vehicleLength,jdbcType=DECIMAL},
      </if>
      <if test="countStart != null" >
        count_start = #{countStart,jdbcType=DECIMAL},
      </if>
      <if test="countEnd != null" >
        count_end = #{countEnd,jdbcType=DECIMAL},
      </if>
      <if test="unit != null" >
        unit = #{unit,jdbcType=INTEGER},
      </if>
      <if test="priceType != null" >
        price_type = #{priceType,jdbcType=INTEGER},
      </if>
      <if test="sort != null" >
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TShippingFreightRuleVehicleLength" >
    update t_shipping_freight_rule_vehicle_length
    set shipping_freight_address_id = #{shippingFreightAddressId,jdbcType=BIGINT},
      vehicle_length = #{vehicleLength,jdbcType=DECIMAL},
      count_start = #{countStart,jdbcType=DECIMAL},
      count_end = #{countEnd,jdbcType=DECIMAL},
      unit = #{unit,jdbcType=INTEGER},
      price_type = #{priceType,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      price = #{price,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>