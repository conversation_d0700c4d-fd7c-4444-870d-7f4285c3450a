package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DemandOrderCancelRequestDto {
    @ApiModelProperty("委托单id 批量字符串")
    @NotBlank(message = "请选择需求单")
    private String demandId;
    @ApiModelProperty("备注")
    @Size(min = 1,max = 300,message = "备注不能为空，且不超过300字")
    private String cancelReason;
    @ApiModelProperty("取消原因：1 我司原因， 2 客户原因")
    @NotBlank(message = "请选择取消原因")
    private String cancelType;
}
