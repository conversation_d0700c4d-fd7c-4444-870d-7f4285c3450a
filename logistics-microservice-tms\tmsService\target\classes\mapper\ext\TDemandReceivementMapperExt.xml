<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandReceivementMapper" >
  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TDemandReceivement" >
    <foreach collection="list" item="item" separator=";">
      insert into t_demand_receivement
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.demandOrderId != null" >
          demand_order_id,
        </if>
        <if test="item.priceType != null">
          price_type,
        </if>
        <if test="item.settlementAmount != null" >
          settlement_amount,
        </if>
        <if test="item.settlementCostTotal != null">
          settlement_cost_total,
        </if>
        <if test="item.settlementTime != null" >
          settlement_time,
        </if>
        <if test="item.status != null" >
          status,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.demandOrderId != null" >
          #{item.demandOrderId,jdbcType=BIGINT},
        </if>
        <if test="item.priceType != null">
          #{item.priceType,jdbcType=INTEGER},
        </if>
        <if test="item.settlementAmount != null" >
          #{item.settlementAmount,jdbcType=DECIMAL},
        </if>
        <if test="item.settlementCostTotal != null">
          #{item.settlementCostTotal,jdbcType=DECIMAL},
        </if>
        <if test="item.settlementTime != null" >
          #{item.settlementTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.status != null" >
          #{item.status,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="com.logistics.tms.entity.TDemandReceivement" >
    <foreach collection="list" item="item" separator=";">
      update t_demand_receivement
      <set >
        <if test="item.demandOrderId != null" >
          demand_order_id = #{item.demandOrderId,jdbcType=BIGINT},
        </if>
        <if test="item.priceType != null">
          price_type = #{item.priceType,jdbcType=INTEGER},
        </if>
        <if test="item.settlementAmount != null" >
          settlement_amount = #{item.settlementAmount,jdbcType=DECIMAL},
        </if>
        <if test="item.settlementCostTotal != null">
          settlement_cost_total = #{item.settlementCostTotal,jdbcType=DECIMAL},
        </if>
        <if test="item.settlementTime != null" >
          settlement_time = #{item.settlementTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.status != null" >
          status = #{item.status,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <update id="batchUpdateForTime" parameterType="com.logistics.tms.entity.TDemandReceivement" >
    <foreach collection="list" item="item" separator=";">
      update t_demand_receivement
      <set >
        <if test="item.demandOrderId != null" >
          demand_order_id = #{item.demandOrderId,jdbcType=BIGINT},
        </if>
        <if test="item.priceType != null">
          price_type = #{item.priceType,jdbcType=INTEGER},
        </if>
        <if test="item.settlementAmount != null" >
          settlement_amount = #{item.settlementAmount,jdbcType=DECIMAL},
        </if>
        <if test="item.settlementCostTotal != null">
          settlement_cost_total = #{item.settlementCostTotal,jdbcType=DECIMAL},
        </if>
        settlement_time = #{item.settlementTime,jdbcType=TIMESTAMP},
        <if test="item.status != null" >
          status = #{item.status,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="searchIdForEntrustSettlementList" resultType="java.lang.Long">
    select DISTINCT
    tr.id
    from t_demand_receivement tr
    left join t_demand_order tdo on tdo.id = tr.demand_order_id and tdo.valid = 1
    left join t_demand_order_address tdoa on tdoa.demand_order_id = tdo.id and tdoa.valid = 1
    left join t_demand_order_goods tdog on tdog.demand_order_id = tdo.id and tdog.valid = 1
    left join t_company_entrust tce on tce.id = tdo.company_entrust_id and tce.valid = 1
    where tr.valid = 1
    <if test="params.settlementStatus != null">
      and tr.status = #{params.settlementStatus,jdbcType=INTEGER}
    </if>
    <if test="params.contractPriceType != null">
      and tr.price_type = #{params.contractPriceType,jdbcType=INTEGER}
    </if>
    <if test="params.demandOrderCode != null and params.demandOrderCode != ''">
      and instr(tdo.demand_order_code,#{params.demandOrderCode,jdbcType=VARCHAR})
    </if>
    <if test="params.customerOrderCode != null and params.customerOrderCode != ''">
      and instr(tdo.customer_order_code,#{params.customerOrderCode,jdbcType=VARCHAR})
    </if>
    <if test="params.companyEntrustIds != null and params.companyEntrustIds != ''">
      and tce.id in (${params.companyEntrustIds})
    </if>
    <if test="params.goodsName != null and params.goodsName != ''">
      and instr(tdog.goods_name,#{params.goodsName,jdbcType=VARCHAR})
    </if>
    <if test="params.goodsSize != null and params.goodsSize != ''">
      and (instr(tdog.goods_size,#{params.goodsSize,jdbcType=VARCHAR})
      or instr(CONCAT(tdog.length,'*',tdog.width, '*',tdog.height),#{params.goodsSize}))
    </if>
    <if test="params.loadAddress != null and params.loadAddress != ''">
      and (instr(tdoa.load_province_name,#{params.loadAddress,jdbcType=VARCHAR}) or
      instr(tdoa.load_city_name,#{params.loadAddress,jdbcType=VARCHAR}) or
      instr(tdoa.load_detail_address,#{params.loadAddress,jdbcType=VARCHAR}) or
      instr(tdoa.load_area_name,#{params.loadAddress,jdbcType=VARCHAR}) or
      instr(tdoa.load_warehouse,#{params.loadAddress,jdbcType=VARCHAR}) or
      instr(CONCAT(tdoa.load_province_name,tdoa.load_city_name,tdoa.load_area_name,tdoa.load_detail_address,tdoa.load_warehouse),#{params.loadAddress,jdbcType=VARCHAR}))
    </if>
    <if test="params.unloadAddress != null and params.unloadAddress != ''">
      and (instr(tdoa.unload_province_name,#{params.unloadAddress,jdbcType=VARCHAR}) or
      instr(tdoa.unload_city_name,#{params.unloadAddress,jdbcType=VARCHAR}) or
      instr(tdoa.unload_area_name,#{params.unloadAddress,jdbcType=VARCHAR}) or
      instr(tdoa.unload_detail_address,#{params.unloadAddress,jdbcType=VARCHAR}) or
      instr(tdoa.unload_warehouse,#{params.unloadAddress,jdbcType=VARCHAR}) or
      instr(CONCAT(tdoa.unload_province_name,tdoa.unload_city_name,tdoa.unload_area_name,tdoa.unload_detail_address,tdoa.unload_warehouse),#{params.unloadAddress,jdbcType=VARCHAR}))
    </if>
    <if test="params.contactPerson != null and params.contactPerson != ''">
      and (instr(tdoa.consignor_name,#{params.contactPerson,jdbcType=VARCHAR})
      or instr(tdoa.consignor_mobile,#{params.contactPerson,jdbcType=VARCHAR})
      or instr(tdoa.receiver_name,#{params.contactPerson,jdbcType=VARCHAR})
      or instr(tdoa.receiver_mobile,#{params.contactPerson,jdbcType=VARCHAR}))
    </if>
    <if test="params.publishTimeStart != null and params.publishTimeStart != ''">
      and tdo.publish_time &gt;= DATE_FORMAT(#{params.publishTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %k:%i:%S')
    </if>
    <if test="params.publishTimeEnd != null and params.publishTimeEnd != ''">
      and tdo.publish_time &lt;= DATE_FORMAT(#{params.publishTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
    </if>
    <if test="params.settlementTimeStart != null and params.settlementTimeStart != ''">
      and tr.settlement_time &gt;= DATE_FORMAT(#{params.settlementTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %k:%i:%S')
    </if>
    <if test="params.settlementTimeEnd != null and params.settlementTimeEnd != ''">
      and tr.settlement_time &lt;= DATE_FORMAT(#{params.settlementTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
    </if>
    order by tdo.publish_time desc,tr.id desc
  </select>
  <resultMap id="entrustSettlementList_Map" type="com.logistics.tms.api.feign.entrustsettlement.model.EntrustSettlementRowModel">
    <id column="id" property="settlementId" jdbcType="BIGINT"/>
    <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
    <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
    <result column="status" property="settlementStatus" jdbcType="INTEGER"/>
    <result column="entrust_status" property="entrustStatus" jdbcType="INTEGER"/>
    <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
    <result column="customer_order_code" property="customerOrderCode" jdbcType="VARCHAR"/>
    <result column="settlement_amount" property="settlementAmount" jdbcType="DECIMAL"/>
    <result column="price_type" property="contractPriceType" jdbcType="INTEGER"/>
    <result column="settlement_cost_total" property="settlementCostTotal" jdbcType="DECIMAL"/>
    <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
    <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
    <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
    <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
    <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
    <result column="consignor_name" property="consignorName" jdbcType="VARCHAR"/>
    <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR"/>
    <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
    <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
    <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
    <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
    <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
    <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
    <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR"/>
    <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
    <result column="company_entrust_name" property="companyEntrust" jdbcType="VARCHAR"/>
    <result column="publish_time" property="publishTime" jdbcType="TIMESTAMP"/>
    <result column="settlement_time" property="settlementTime" jdbcType="TIMESTAMP"/>
    <result column="entrust_type" property="entrustType" jdbcType="INTEGER"/>
    <result column="source" property="demandOrderSource" jdbcType="INTEGER"/>
    <collection property="goodsList" ofType="com.logistics.tms.api.feign.entrustsettlement.model.EntrustSettlementGoodsResponseModel">
      <id column="demandOrderGoodsId" property="demandOrderGoodsId" jdbcType="BIGINT"/>
      <result column="demandOrderId" property="demandOrderId" jdbcType="BIGINT"/>
      <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
      <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
      <result column="length" property="length" jdbcType="INTEGER"/>
      <result column="width" property="width" jdbcType="INTEGER"/>
      <result column="height" property="height" jdbcType="INTEGER"/>
    </collection>
  </resultMap>
  <select id="entrustSettlementList" resultMap="entrustSettlementList_Map">
    select
    tr.id,
    tr.demand_order_id,
    tr.status,
    tr.settlement_amount,
    tr.settlement_cost_total,
    tr.settlement_time,
    tr.price_type,
    tdo.demand_order_code,
    tdo.entrust_status,
    tdo.if_empty,
    tdo.customer_order_code,
    tdo.goods_unit,
    tdo.publish_time,
    tdo.entrust_type,
    tdo.company_entrust_name,
    tdo.source,
    tdoa.load_province_name,
    tdoa.load_city_name,
    tdoa.load_area_name,
    tdoa.load_detail_address,
    tdoa.load_warehouse,
    tdoa.consignor_name,
    tdoa.consignor_mobile,
    tdoa.unload_province_name,
    tdoa.unload_city_name,
    tdoa.unload_area_name,
    tdoa.unload_detail_address,
    tdoa.unload_warehouse,
    tdoa.receiver_name,
    tdoa.receiver_mobile,
    tdog.id as demandOrderGoodsId,
    tdog.demand_order_id as demandOrderId,
    tdog.goods_name,
    tdog.goods_size,
    tdog.length,
    tdog.width,
    tdog.height
    from t_demand_receivement tr
    left join t_demand_order tdo on tdo.id = tr.demand_order_id and tdo.valid = 1
    left join t_demand_order_address tdoa on tdoa.demand_order_id = tdo.id and tdoa.valid = 1
    left join t_demand_order_goods tdog on tdog.demand_order_id = tdo.id and tdog.valid = 1
    where tr.valid = 1
    <if test="ids != null and ids != ''">
      and tr.id in (${ids})
    </if>
    order by tdo.publish_time desc,tr.id desc
  </select>

  <select id="entrustSettlementListCount" resultType="com.logistics.tms.api.feign.entrustsettlement.model.EntrustSettlementListResponseModel">
    select
    ifnull(count(0),0) as totalEntrustOrderCount,
    ifnull(sum(if(tmp.goods_unit = 1 or tmp.goods_unit = 3,tmp.settlement_amount,0)),0) as totalPackageSettlementAmount,
    ifnull(sum(if(tmp.goods_unit = 2,tmp.settlement_amount,0)),0) as totalWeightSettlementAmount,
    ifnull(sum(tmp.settlement_cost_total),0) as totalSettlementCost
    from (
    select tr.id,tdo.goods_unit,tr.settlement_cost_total,tr.settlement_amount
    from t_demand_receivement tr
    left join t_demand_order tdo on tdo.id = tr.demand_order_id and tdo.valid = 1
    left join t_demand_order_address tdoa on tdoa.demand_order_id = tdo.id and tdoa.valid = 1
    left join t_demand_order_goods tdog on tdog.demand_order_id = tdo.id and tdog.valid = 1
    left join t_company_entrust tce on tce.id = tdo.company_entrust_id and tce.valid = 1
    where tr.valid = 1
    <if test="params.settlementStatus != null">
      and tr.status = #{params.settlementStatus,jdbcType=INTEGER}
    </if>
    <if test="params.contractPriceType != null">
      and tr.price_type = #{params.contractPriceType,jdbcType=INTEGER}
    </if>
    <if test="params.demandOrderCode != null and params.demandOrderCode != ''">
      and instr(tdo.demand_order_code,#{params.demandOrderCode,jdbcType=VARCHAR})
    </if>
    <if test="params.customerOrderCode != null and params.customerOrderCode != ''">
      and instr(tdo.customer_order_code,#{params.customerOrderCode,jdbcType=VARCHAR})
    </if>
    <if test="params.companyEntrustIds != null and params.companyEntrustIds != ''">
      and tce.id in (${params.companyEntrustIds})
    </if>
    <if test="params.goodsName != null and params.goodsName != ''">
      and instr(tdog.goods_name,#{params.goodsName,jdbcType=VARCHAR})
    </if>
    <if test="params.goodsSize != null and params.goodsSize != ''">
      and (instr(tdog.goods_size,#{params.goodsSize,jdbcType=VARCHAR})
      or instr(CONCAT(tdog.length,'*',tdog.width, '*',tdog.height),#{params.goodsSize}))
    </if>
    <if test="params.loadAddress != null and params.loadAddress != ''">
      and (instr(tdoa.load_province_name,#{params.loadAddress,jdbcType=VARCHAR}) or
      instr(tdoa.load_city_name,#{params.loadAddress,jdbcType=VARCHAR}) or
      instr(tdoa.load_detail_address,#{params.loadAddress,jdbcType=VARCHAR}) or
      instr(tdoa.load_area_name,#{params.loadAddress,jdbcType=VARCHAR}) or
      instr(tdoa.load_warehouse,#{params.loadAddress,jdbcType=VARCHAR}) or
      instr(CONCAT(tdoa.load_province_name,tdoa.load_city_name,tdoa.load_area_name,tdoa.load_detail_address,tdoa.load_warehouse),#{params.loadAddress,jdbcType=VARCHAR}))
    </if>
    <if test="params.unloadAddress != null and params.unloadAddress != ''">
      and (instr(tdoa.unload_province_name,#{params.unloadAddress,jdbcType=VARCHAR}) or
      instr(tdoa.unload_city_name,#{params.unloadAddress,jdbcType=VARCHAR}) or
      instr(tdoa.unload_area_name,#{params.unloadAddress,jdbcType=VARCHAR}) or
      instr(tdoa.unload_detail_address,#{params.unloadAddress,jdbcType=VARCHAR}) or
      instr(tdoa.load_warehouse,#{params.loadAddress,jdbcType=VARCHAR}) or
      instr(CONCAT(tdoa.load_province_name,tdoa.load_city_name,tdoa.load_area_name,tdoa.load_detail_address,tdoa.load_warehouse),#{params.loadAddress,jdbcType=VARCHAR}))
    </if>
    <if test="params.contactPerson != null and params.contactPerson != ''">
      and (instr(tdoa.consignor_name,#{params.contactPerson,jdbcType=VARCHAR})
      or instr(tdoa.consignor_mobile,#{params.contactPerson,jdbcType=VARCHAR})
      or instr(tdoa.receiver_name,#{params.contactPerson,jdbcType=VARCHAR})
      or instr(tdoa.receiver_mobile,#{params.contactPerson,jdbcType=VARCHAR}))
    </if>
    <if test="params.publishTimeStart != null and params.publishTimeStart != ''">
      and tdo.publish_time &gt;= DATE_FORMAT(#{params.publishTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %k:%i:%S')
    </if>
    <if test="params.publishTimeEnd != null and params.publishTimeEnd != ''">
      and tdo.publish_time &lt;= DATE_FORMAT(#{params.publishTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
    </if>
    <if test="params.settlementTimeStart != null and params.settlementTimeStart != ''">
      and tr.settlement_time &gt;= DATE_FORMAT(#{params.settlementTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %k:%i:%S')
    </if>
    <if test="params.settlementTimeEnd != null and params.settlementTimeEnd != ''">
      and tr.settlement_time &lt;= DATE_FORMAT(#{params.settlementTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
    </if>
    <if test="params.settlementIds != null and params.settlementIds != ''">
      and tr.id in (${params.settlementIds})
    </if>
    group by tr.id) tmp
  </select>

  <select id="getSettlementDetail" resultType="com.logistics.tms.api.feign.entrustsettlement.model.GetSettlementDetailRowModel">
    select
    tc.company_name as companyEntrust,
    tdo.goods_unit as goodsUnit,
    SUM(tr.settlement_amount) as settlementAmount,
    SUM(tr.settlement_cost_total) as settlementCostTotal
    from t_demand_receivement tr
    left join t_demand_order tdo on tdo.id = tr.demand_order_id and tdo.valid = 1
    left join t_company_entrust tce on tce.id = tdo.company_entrust_id and tce.valid = 1
    left join t_company tc on tc.id = tce.company_id and tc.valid = 1
    where tr.valid = 1
    and tr.id in (${ids})
    GROUP BY tdo.company_entrust_id,tdo.goods_unit
  </select>

  <select id="getByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_demand_receivement
    where valid = 1
    and id in (${ids})
  </select>

  <select id="getDetailForUpdateCost" resultType="com.logistics.tms.api.feign.entrustsettlement.model.GetDetailResponseModel">
    select
    tr.id as settlementId,
    tr.settlement_cost_total as settlementCostTotal,
    tdo.demand_order_code as demandOrderCode,
    tdo.customer_order_code as customerOrderCode,
    tr.price_type as contractPriceType
    from t_demand_receivement tr
    left join t_demand_order tdo on tdo.id = tr.demand_order_id and tdo.valid = 1
    where tr.valid = 1
    and tr.id = #{settlementId,jdbcType=BIGINT}
  </select>

  <select id="getByDemandOrderIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_demand_receivement
    where valid = 1
    and demand_order_id in (${demandOrderIds})
  </select>

  <select id="getByDemandOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_demand_receivement
    where valid = 1
    and demand_order_id = #{demandOrderId,jdbcType=BIGINT}
  </select>

  <update id="updateByDemandOrderId">
    update t_demand_receivement
    <set>
      <if test="priceType != null">
        price_type = #{priceType,jdbcType=INTEGER},
      </if>
      <if test="settlementAmount != null">
        settlement_amount = #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementCostTotal != null">
        settlement_cost_total = #{settlementCostTotal,jdbcType=DECIMAL},
      </if>
      <if test="settlementTime != null">
        settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where demand_order_id = #{demandOrderId,jdbcType=BIGINT}
  </update>
</mapper>