package com.logistics.tms.biz.terminalreachmanagement;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.terminalreachmanagement.model.GetReachManagementDetailRequestModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.GetReachManagementDetailResponseModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.SearchReachManagementListRequestModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.SearchReachManagementListResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.client.LeyiBasicDataServiceClient;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.request.UpdateAccessibilityVerifyRequestModel;
import com.logistics.tms.entity.TCarrierOrder;
import com.logistics.tms.entity.TCarrierOrderAddress;
import com.logistics.tms.entity.TReachAttachment;
import com.logistics.tms.entity.TReachManagement;
import com.logistics.tms.mapper.TCarrierOrderAddressMapper;
import com.logistics.tms.mapper.TCarrierOrderMapper;
import com.logistics.tms.mapper.TReachAttachmentMapper;
import com.logistics.tms.mapper.TReachManagementMapper;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
@Slf4j
@Service
public class ReachManagementBiz {

    @Resource
    private TReachManagementMapper reachManagementMapper;
    @Resource
    private TReachAttachmentMapper reachAttachmentMapper;
    @Resource
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Resource
    private TCarrierOrderAddressMapper tCarrierOrderAddressMapper;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private LeyiBasicDataServiceClient leyiBasicDataServiceClient;

    /**
     * 分页查询触达管理列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<SearchReachManagementListResponseModel> searchReachManagementList(SearchReachManagementListRequestModel requestModel) {
        //分页查询列表
        requestModel.enablePaging();
        List<SearchReachManagementListResponseModel> list = reachManagementMapper.searchReachManagementList(requestModel);
        PageInfo pageInfo = new PageInfo(list);
        if (ListUtils.isNotEmpty(list)){
            //运单id
            List<Long> carrierOrderIdList = list.stream().map(SearchReachManagementListResponseModel::getCarrierOrderId).collect(Collectors.toList());
            String carrierOrderIds = StringUtils.listToString(carrierOrderIdList,',');

            //查询上游客户信息
            List<TCarrierOrder> tCarrierOrderList = tCarrierOrderMapper.selectCarrierOrdersByIds(carrierOrderIds);
            Map<Long, String> upstreamCustomerMap = tCarrierOrderList.stream().collect(Collectors.toMap(TCarrierOrder::getId, TCarrierOrder::getUpstreamCustomer));

            //查询地址信息
            List<TCarrierOrderAddress> tCarrierOrderAddressList = tCarrierOrderAddressMapper.getByCarrierOrderIds(carrierOrderIds);
            Map<Long, TCarrierOrderAddress> carrierOrderAddressMap = tCarrierOrderAddressList.stream().collect(Collectors.toMap(TCarrierOrderAddress::getCarrierOrderId, item->item));

            //拼接数据
            TCarrierOrderAddress tCarrierOrderAddress;
            for (SearchReachManagementListResponseModel model : list) {
                //拼接上游客户
                model.setUpstreamCustomer(upstreamCustomerMap.get(model.getCarrierOrderId()));

                //拼接地址
                tCarrierOrderAddress = carrierOrderAddressMap.get(model.getCarrierOrderId());
                if (tCarrierOrderAddress != null){
                    model.setConsignorName(tCarrierOrderAddress.getConsignorName());
                    model.setConsignorMobile(tCarrierOrderAddress.getConsignorMobile());
                    model.setLoadWarehouse(tCarrierOrderAddress.getLoadWarehouse());
                    model.setLoadProvinceName(tCarrierOrderAddress.getLoadProvinceName());
                    model.setLoadCityName(tCarrierOrderAddress.getLoadCityName());
                    model.setLoadAreaName(tCarrierOrderAddress.getLoadAreaName());
                    model.setLoadDetailAddress(tCarrierOrderAddress.getLoadDetailAddress());
                }
            }
        }
        return pageInfo;
    }

    /**
     * 触达管理列表详情
     * @param requestModel
     * @return
     */
    public GetReachManagementDetailResponseModel getReachManagementDetail(GetReachManagementDetailRequestModel requestModel) {
        //判断数据是否存在
        TReachManagement dbReachManagement = reachManagementMapper.selectByPrimaryKeyDecrypt(requestModel.getReachManagementId());
        if (Objects.isNull(dbReachManagement)){
            throw new BizException(CarrierDataExceptionEnum.REACH_INFO_NOT_EXISTS);
        }

        //获取详情文本信息
        GetReachManagementDetailResponseModel result = reachManagementMapper.getReachManagementDetail(requestModel);

        //获取详情图片信息
        List<TReachAttachment> reachAttachmentList = reachAttachmentMapper.selectAttachmentInfoByReachId(requestModel.getReachManagementId());
        if (ListUtils.isNotEmpty(reachAttachmentList)) {
            Map<String, List<String>> attachmentMap = reachAttachmentList.stream()
                    .collect(Collectors.groupingBy(reachAttachment -> reachAttachment.getAttachmentType().toString(), Collectors.mapping(TReachAttachment::getAttachmentPath, Collectors.toList())));
            result.setAttachmentMap(attachmentMap);
        }

        return result;
    }

    /**
     * 修改触达距离偏差
     *
     * @param tReachManagementId 触达表id
     * @param carrierOrderId     运单id
     * @param destination        终点经纬度拼接字符串 (经度,纬度)
     */
    @Transactional
    public void updateDistanceDeviation(Long tReachManagementId,
                                        Long carrierOrderId,
                                        String destination) {
        //查询发货地址
        TCarrierOrderAddress tCarrierOrderAddress = tCarrierOrderAddressMapper.getByCarrierOrderId(carrierOrderId);
        //起点经纬度拼接字符串 (经度,纬度)
        String originLocation = tCarrierOrderAddress.getLoadLongitude() + "," + tCarrierOrderAddress.getLoadLatitude();

        //调高德接口查询
        BigDecimal mileage = commonBiz.getMileageByLonAndLat(originLocation, destination);

        TReachManagement tReachManagementUp = new TReachManagement();
        tReachManagementUp.setId(tReachManagementId);
        tReachManagementUp.setDistanceDeviation(mileage);
        reachManagementMapper.updateByPrimaryKeySelectiveEncrypt(tReachManagementUp);

        try {
            //基础地址code不为空，且距离偏差小于等于5公里，则同步云盘【司机可达】
            if (StringUtils.isNotBlank(tCarrierOrderAddress.getLoadYeloAddressCode()) && mileage.compareTo(CommonConstant.BIG_DECIMAL_FIVE) <= CommonConstant.INTEGER_ZERO){
                //调云盘提供TMS的司机可达接口
                UpdateAccessibilityVerifyRequestModel updateAccessibilityVerifyRequestModel = new UpdateAccessibilityVerifyRequestModel();
                updateAccessibilityVerifyRequestModel.setAddressCode(tCarrierOrderAddress.getLoadYeloAddressCode());
                updateAccessibilityVerifyRequestModel.setAccessibilityVerify(CommonConstant.INTEGER_ONE);
                leyiBasicDataServiceClient.updateAccessibilityVerifyFromGroundPush(updateAccessibilityVerifyRequestModel);
            }
        }catch (Exception e){
            log.warn("同步云盘【司机可达】失败："+e.getMessage());
        }
    }
}
