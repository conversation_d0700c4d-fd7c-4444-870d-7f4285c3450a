package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2023/04/27
*/
@Data
public class TWorkOrderProcess extends BaseEntity {
    /**
    * 工单id
    */
    @ApiModelProperty("工单id")
    private Long workOrderId;

    /**
    * 状态：0 待处理，10 处理中，20 已处理，30 已关闭，40 已撤销 50 重新提交
    */
    @ApiModelProperty("状态：0 待处理，10 处理中，20 已处理，30 已关闭，40 已撤销 50 重新提交")
    private Integer status;

    /**
    * 处理描述
    */
    @ApiModelProperty("处理描述")
    private String solveDesc;

    /**
    * 处理人
    */
    @ApiModelProperty("处理人")
    private String solveUserName;

    /**
    * 处理时间
    */
    @ApiModelProperty("处理时间")
    private Date solveTime;

    /**
    * 处理来源：1 后台，2 前台，3 小程序，4 任务中心
    */
    @ApiModelProperty("处理来源：1 后台，2 前台，3 小程序，4 任务中心")
    private Integer solveSource;

    /**
    * 处理备注
    */
    @ApiModelProperty("处理备注")
    private String solveRemark;
}