package com.logistics.tms.controller.vehicleassetmanagement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date:2019/6/4 9:40
 *
 */

@Data
public class AddOrModifyDrivingLicenseAnnualReviewRequestModel  {
    @ApiModelProperty("行驶证记录Id")
    private Long drivingLicenseAnnualReviewId;
    @ApiModelProperty("行驶证检查有效期")
    private Date checkValidDate;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("证件图片列表")
    private List<CertificationPicturesRequestModel> fileList;
}
