package com.logistics.appapi.client.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class RenewableOrderListResponseModel {

	@ApiModelProperty("乐橘新生订单审核表id")
	private Long renewableAuditId;

	@ApiModelProperty("乐橘单号")
	private String renewableOrderCode;

	@ApiModelProperty(value = "业务类型:1:公司,2:个人")
	private Integer businessType;
	@ApiModelProperty("乐橘新生客户名称（企业）")
	private String customerName;
	@ApiModelProperty("乐橘新生客户姓名（个人）")
	private String customerUserName;
	@ApiModelProperty("乐橘新生客户手机号（个人）")
	private String customerUserMobile;

	@ApiModelProperty("下单日期")
	private Date publishTime;

	@ApiModelProperty("发货省市区")
	private String loadProvinceName = "";
	private String loadCityName = "";
	private String loadAreaName = "";
	@ApiModelProperty("发货详细地址")
	private String loadDetailAddress = "";
	@ApiModelProperty("发货人")
	private String consignorName = "";
	private String consignorMobile = "";
	@ApiModelProperty("发货经度")
	private String loadLongitude;
	@ApiModelProperty("发货经度")
	private String loadLatitude;

	@ApiModelProperty("收货省市区")
	private String unloadProvinceName = "";
	private String unloadCityName = "";
	private String unloadAreaName = "";
	@ApiModelProperty("收货地址详细")
	private String unloadDetailAddress = "";
	@ApiModelProperty("收货仓库")
	private String unloadWarehouse = "";
	@ApiModelProperty("收货人")
	private String receiverName = "";
	private String receiverMobile = "";
	@ApiModelProperty("收货经度")
	private String unloadLongitude;
	@ApiModelProperty("收货纬度")
	private String unloadLatitude;

	@ApiModelProperty("司机确认数量")
	private BigDecimal verifiedGoodsAmountTotal;

	@ApiModelProperty("订单状态：0 待指派，1 待确认，2 待审核，3 已审核")
	private Integer status;

}
