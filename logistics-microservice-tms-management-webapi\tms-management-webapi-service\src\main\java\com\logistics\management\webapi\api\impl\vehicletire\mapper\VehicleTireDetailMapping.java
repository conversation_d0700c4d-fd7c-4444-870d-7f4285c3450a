package com.logistics.management.webapi.api.impl.vehicletire.mapper;

import com.logistics.management.webapi.api.feign.vehicletire.dto.CertificationPicturesResponseDto;
import com.logistics.management.webapi.api.feign.vehicletire.dto.VehicleTireDetailResponseDto;
import com.logistics.tms.api.feign.vehicletire.model.VehicleTireDetailResponseModel;
import com.logistics.tms.api.feign.vehicletire.model.VehicleTireNoListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;

import java.math.BigDecimal;
import java.util.Map;

public class VehicleTireDetailMapping extends MapperMapping<VehicleTireDetailResponseModel, VehicleTireDetailResponseDto> {

    private String imagePrefix;
    private Map<String,String> imageMap;
    public VehicleTireDetailMapping(String imagePrefix , Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        VehicleTireDetailResponseModel source = this.getSource();
        VehicleTireDetailResponseDto destination = this.getDestination();
        if (source != null) {
            destination.setDriverName(source.getDriverName() + " " + source.getDriveMobile());
            if (ListUtils.isNotEmpty(source.getVehicleTireNoList())) {
                BigDecimal sum = BigDecimal.ZERO;
                for (VehicleTireNoListResponseModel tmp : source.getVehicleTireNoList()) {
                    sum = sum.add(tmp.getUnitPrice().multiply(ConverterUtils.toBigDecimal(tmp.getAmount())).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
                destination.setTotalCost(ConverterUtils.toString(sum));
            }
            if (source.getReplaceDate() != null) {
                destination.setReplaceDate(DateUtils.dateToString(source.getReplaceDate(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if (ListUtils.isNotEmpty(destination.getFileList())) {
                for (CertificationPicturesResponseDto tmp : destination.getFileList()) {
                    tmp.setAbsoluteFilePath(imagePrefix + imageMap.get(tmp.getRelativeFilepath()));
                }
            }
        }
    }
}
