package com.logistics.tms.api.feign.extvehiclesettlement.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/11/20 13:33
 */
@Data
public class SearchExtVehicleSettlementListRequestModel extends AbstractPageForm<SearchExtVehicleSettlementListRequestModel> {
    @ApiModelProperty("支付状态：0 未支付，1 已支付")
    private Integer payStatus;
    @ApiModelProperty("运单状态：50000 待签收，60000 已签收  2已放空")
    private Integer status;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机")
    private String driver;
    @ApiModelProperty("发货地")
    private String loadAddress;
    @ApiModelProperty("卸货地")
    private String unloadAddress;
    @ApiModelProperty("调度人")
    private String dispatchUser;
    @ApiModelProperty("货主，公司ID，逗号分隔")
    private String companyEntrustIds;
    @ApiModelProperty("品名")
    private String goodsName;
    @ApiModelProperty("规格")
    private String goodsSize;
    @ApiModelProperty("运单生成时间从")
    private String dispatchTimeFrom;
    @ApiModelProperty("运单生成时间到")
    private String dispatchTimeTo;

    @ApiModelProperty("多选导出")
    private String ids;
}
