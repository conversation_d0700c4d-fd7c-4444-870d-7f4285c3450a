package com.logistics.tms.api.feign.violationregulation.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/6/3 13:22
 */
@Data
public class ImportViolationRegulationListRequestModel {
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("司机电话")
    private String driverPhone;
    @ApiModelProperty("违章时间")
    private Date occuranceTime;
    @ApiModelProperty("违章地点")
    private String occuranceAddress;
    @ApiModelProperty("扣分")
    private Integer deduction;
    @ApiModelProperty("罚款")
    private BigDecimal fine;
    @ApiModelProperty("备注信息")
    private String remark;
}
