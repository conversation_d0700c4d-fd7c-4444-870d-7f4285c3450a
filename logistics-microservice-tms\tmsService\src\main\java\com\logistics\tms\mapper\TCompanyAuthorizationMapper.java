package com.logistics.tms.mapper;

import com.logistics.tms.api.feign.companycarrierauthorization.model.request.CarrierAuthorizationListRequestModel;
import com.logistics.tms.api.feign.companycarrierauthorization.model.response.CarrierAuthorizationDetailResponseModel;
import com.logistics.tms.api.feign.companycarrierauthorization.model.response.CarrierAuthorizationListResponseModel;
import com.logistics.tms.entity.TCompanyAuthorization;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Mybatis Generator on 2023/01/09
 */
@Mapper
public interface TCompanyAuthorizationMapper extends BaseMapper<TCompanyAuthorization> {

	TCompanyAuthorization selectByCompanyIdAndType(@Param("companyId") Long companyId, @Param("companyType") Integer companyType);

	List<CarrierAuthorizationListResponseModel> selectCarrierAuthorizationList(@Param("params") CarrierAuthorizationListRequestModel requestModel);

	TCompanyAuthorization selectOneByCompanyId(@Param("companyId") Long companyId);

	TCompanyAuthorization selectOneById(@Param("id") Long id);

	int addCompanyAuthorization(TCompanyAuthorization companyAuthorization);

	CarrierAuthorizationDetailResponseModel selectCarrierAuthorizationDetailById(@Param("carrierAuthorizationId")Long carrierAuthorizationId);

	int updateCompanyAuthorization(TCompanyAuthorization companyAuthorization);
}