package com.logistics.appapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2018/9/30 11:48
 */
@Data
public class DownloadLadingBillResponseDto {

    @ApiModelProperty("需求单号")
    private String demandOrderCode="";

    @ApiModelProperty("需求类型")
    private String entrustType="";

    @ApiModelProperty("需求类型label")
    private String entrustTypeLabel="";

    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";

    @ApiModelProperty("车牌号码")
    private String vehicleNo = "";

    @ApiModelProperty("司机姓名")
    private String driverName = "";

    @ApiModelProperty("司机手机号")
    private String driverMobile = "";

    @ApiModelProperty("司机身份证号")
    private String driverIdentityNumber = "";

    private String loadCompany="";
    private String loadDetailAddress = "";
    private String loadWarehouse = "";

    private String unloadCompany="";
    private String unloadDetailAddress = "";
    private String unloadWarehouse = "";

    @ApiModelProperty("提货人")
    private String consignorName = "";

    @ApiModelProperty("提货人电话")
    private String consignorMobile = "";

    @ApiModelProperty("卸货人")
    private String receiverName = "";

    @ApiModelProperty("卸货人电话")
    private String receiverMobile = "";

    @ApiModelProperty("预计提货时间")
    private String expectedLoadTime = "";

    @ApiModelProperty("回收预计提货时间")
    private String recycleExpectedLoadTime = "";

    @ApiModelProperty("预计卸货时间")
    private String expectedUnloadTime = "";

    @ApiModelProperty("实际提货时间")
    private String loadTime = "";

    @ApiModelProperty("调度时间")
    private String dispatchTime = "";

    @ApiModelProperty("货物信息")
    private List<DownloadLadingBillGoodsResponseDto> goodsInfoList;

    @ApiModelProperty("合计")
    private String totalCount = "";

    private String totalVolume = "";

    @ApiModelProperty("预计提货数量")
    private String expectAmount = "";

    @ApiModelProperty("实际提货数量")
    private String loadAmount = "";

    @ApiModelProperty("签收数量")
    private String signAmount = "";

    @ApiModelProperty("差异  实提-预提")
    private String amountDiff = "";

    @ApiModelProperty("提货人（姓名+电话）")
    private String consignor = "";

    @ApiModelProperty("卸货人（姓名+电话）")
    private String receiver = "";

    @ApiModelProperty("品名")
    private String goodsName = "";

    @ApiModelProperty("货物单位")
    private String goodsUnit = "";

    //判断用
    @ApiModelProperty("运单状态 10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收")
    private Integer status;

    @ApiModelProperty("委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件")
    private Integer demandOrderSource;

    @ApiModelProperty("运单二维码图片路径")
    private String qrCodePicPath = "";

    @ApiModelProperty("备注")
    private String remark = "";

    @ApiModelProperty("调度备注")
    private String dispatchRemark = "";

    @ApiModelProperty("其他要求")
    private String otherRequirements = "";

    @ApiModelProperty("下单人")
    private String publishName = "";

    @ApiModelProperty("下单人手机号")
    private String publishMobile = "";

    @ApiModelProperty("货主")
    private String companyEntrustName = "";

    @ApiModelProperty("是否取消 0 否 1 是")
    private String ifCancel;

    @ApiModelProperty("是否放空：0 否，1 是")
    private String ifEmpty;

    @ApiModelProperty("上游客户（YR）")
    private String upstreamCustomer;

    @ApiModelProperty("承运商")
    private String companyCarrierName;

}
