package com.logistics.management.webapi.base.enums;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

/**
 * 司机小程序跳转页面枚举
 */
@Getter
@AllArgsConstructor
public enum DriverAppletQrCodeJumpPageEnum {

    // 运单详情页
    CARRIER_ORDER_DETAIL_PAGE(1),
    ;
    // 跳转参数标识
    private static final String JUMP_PARAM = "p";
    private final Integer key;

    public Map<String, Object> getParamsMap(Map<String, Object> paramMap) {
        paramMap.put(JUMP_PARAM, key);
        return paramMap;
    }

    public Map<String, Object> getParamsMap() {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put(JUMP_PARAM, key);
        return paramMap;
    }
}
