package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class CarrierOrderDetailResponseModel {

    @ApiModelProperty("运单ID")
    private Long carrierOrderId;
    @ApiModelProperty("需求单ID")
    private Long demandOrderId;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("运单状态")
    private Integer status;
    @ApiModelProperty("是否取消")
    private Integer ifCancel;
    private String cancelReason;
    @ApiModelProperty("是否放空 0 否 1 是")
    private Integer ifEmpty;
    @ApiModelProperty("委托方")
    private String entrustCompany;
    @ApiModelProperty("调度人")
    private String dispatchUserName;
    @ApiModelProperty("运单生成时间")
    private Date dispatchTime;
    @ApiModelProperty("运单基本信息")
    private CarrierOrderDetailBasicInfoModel carrierOrderDetailBasicInfo;
    @ApiModelProperty("运单运费信息")
    private CarrierOrderDetailFreightFeeInfoModel carrierOrderDetailFreightFeeInfo;
    @ApiModelProperty("运单货物信息")
    private List<CarrierOrderDetailGoodsInfoModel> carrierOrderDetailGoodsInfo;
    @ApiModelProperty("运单车辆司机信息")
    private List<CarrierOrderDetailVehicleDriverInfoModel> carrierOrderDetailVehicleDriverInfo;
    @ApiModelProperty("运单事件")
    private List<CarrierOrderDetailEventModel> carrierOrderDetailEvents = new ArrayList<>();
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;
    @ApiModelProperty("预计承运件数")
    private BigDecimal expectAmount;
    @ApiModelProperty("实际提货件数")
    private BigDecimal loadAmount;
    @ApiModelProperty("实际卸货件数")
    private BigDecimal unloadAmount;
    @ApiModelProperty("签收件数")
    private BigDecimal signAmount;
    @ApiModelProperty("货主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer entrustSettlementTonnage;
    @ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer carrierSettlementTonnage;
    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;
    @ApiModelProperty("货主预计费用类型")
    private Integer exceptContractPriceType;
    @ApiModelProperty("货主预计费用")
    private BigDecimal exceptContractPrice;
    @ApiModelProperty("是否是本公司：1 云途 2 二级承运商")
    private Integer ifQiya;
    @ApiModelProperty("车主对账单状态, -2:未关联对账")
    private Integer carrierSettleStatementStatus;

    private Long companyCarrierId;//车主id
}
