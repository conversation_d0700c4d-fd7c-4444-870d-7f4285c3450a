package com.logistics.tms.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierOrderEmptyRequestModel {

    @ApiModelProperty("运单d")
    private Long carrierOrderId;

    @ApiModelProperty("放空原因类型：10 数量问题，20 重报问题，30 联系问题，40 地址问题，50 装车问题，60 等待问题，70 其他问题")
    private Integer objectionType;

    @ApiModelProperty("放空描述")
    private String objectionReason;
}
