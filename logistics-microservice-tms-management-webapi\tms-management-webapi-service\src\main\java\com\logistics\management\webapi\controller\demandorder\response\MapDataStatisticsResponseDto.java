package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2021/10/18 17:18
 */
@Data
public class MapDataStatisticsResponseDto {
    @ApiModelProperty("市名称")
    private String cityName="";
    @ApiModelProperty("市经度")
    private String longitude="";
    @ApiModelProperty("市维度")
    private String latitude="";
    @ApiModelProperty("待提货运单数")
    private String waitLoadCount="";
    @ApiModelProperty("待纠错运单数")
    private String waitCorrectCount="";
    @ApiModelProperty("待调度需求单数")
    private String waitDispatchCount="";
}
