package com.logistics.tms.base.enums;


public enum MQBaseMessageKeyEnum {
    CREATE_DEMAND_ORDER("demandOrderCreate","生成物流需求单"),
    ADDITIONAL_ORDER("recycleAddTo", "托盘追加/补单R单"),
    ;

    private final String key;
    private final String value;

    MQBaseMessageKeyEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static MQBaseMessageKeyEnum getEnum(String key) {
        for (MQBaseMessageKeyEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }

}
