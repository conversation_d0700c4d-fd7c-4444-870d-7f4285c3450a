package com.logistics.tms.mapper;

import com.logistics.tms.controller.routeenquiry.response.RouteEnquiryFileListResponseModel;
import com.logistics.tms.entity.TRouteEnquiryAttachment;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/07/09
*/
@Mapper
public interface TRouteEnquiryAttachmentMapper extends BaseMapper<TRouteEnquiryAttachment> {

    List<RouteEnquiryFileListResponseModel> getFileGroupTypeByRouteEnquiryId(@Param("routeEnquiryId")Long routeEnquiryId);

}