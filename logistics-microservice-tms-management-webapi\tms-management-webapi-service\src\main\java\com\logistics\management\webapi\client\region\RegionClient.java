package com.logistics.management.webapi.client.region;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.region.hystrix.RegionClientHystrix;
import com.logistics.management.webapi.client.region.request.*;
import com.logistics.management.webapi.client.region.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/6/4 11:22
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/region",
        fallback = RegionClientHystrix.class)
public interface RegionClient {

    @ApiOperation(value = "物流大区列表")
    @PostMapping(value = "/searchList")
    Result<PageInfo<SearchRegionResponseModel>> searchList(@RequestBody SearchRegionRequestModel requestModel);

    @ApiOperation(value = "获取物流大区下车主列表")
    @PostMapping(value = "/getRegionCompany")
    Result<List<RegionCompanyResponseModel>> getRegionCompany(@RequestBody RegionDetailRequestModel requestModel);

    @ApiOperation(value = "物流大区新增修改")
    @PostMapping(value = "/saveOrModifyRegion")
    Result<Boolean> saveOrModifyRegion(@RequestBody SaveOrModifyRegionRequestModel requestModel);

    @ApiOperation(value = "编辑详情")
    @PostMapping(value = "/getDetail")
    Result<RegionDetailResponseModel> getDetail(@RequestBody RegionDetailRequestModel requestModel);

    @ApiOperation(value = "启用/禁用大区信息")
    @PostMapping(value = "/enableRegion")
    Result<Boolean> enableRegion(@RequestBody EnableRegionRequestModel requestModel);

    @ApiOperation(value = "导出")
    @PostMapping(value = "/export")
    Result<List<SearchRegionResponseModel>> export(@RequestBody SearchRegionRequestModel requestModel);

    @ApiOperation(value = "移除")
    @PostMapping(value = "/removeRegion")
    Result<Boolean> removeRegion(@RequestBody RemoveRegionRequestModel requestModel);

    @ApiOperation(value = "大区详情列表")
    @PostMapping(value = "/searchDetailList")
    Result<PageInfo<SearchRegionDetailResponseModel>> searchDetailList(@RequestBody SearchRegionDetailRequestModel requestModel);

    @ApiOperation(value = "大区详情列表导出")
    @PostMapping(value = "/exportRegionDetail")
    Result<List<SearchRegionDetailResponseModel>> exportRegionDetail(@RequestBody SearchRegionDetailRequestModel requestModel);

    @ApiOperation(value = "根据省市查询区域下的车主（排除加入黑名单的车主）")
    @PostMapping(value = "/getCompanyByRegion")
    Result<List<GetCompanyCarrierByRegionResponseModel>> getCompanyByRegion(@RequestBody GetCompanyCarrierByRegionRequestModel requestModel);

}
