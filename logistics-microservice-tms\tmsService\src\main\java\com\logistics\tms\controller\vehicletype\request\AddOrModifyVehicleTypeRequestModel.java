package com.logistics.tms.controller.vehicletype.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AddOrModifyVehicleTypeRequestModel  {
    @ApiModelProperty("车辆类型")
    private String vehicleType;
    @ApiModelProperty("车辆类别：1 牵引车 2 挂车 3 一体车")
    private Integer vehicleCategory;
    @ApiModelProperty("车辆类型Id")
    private Long vehicleTypeId;
    @ApiModelProperty("备注")
    private String remark;
}
