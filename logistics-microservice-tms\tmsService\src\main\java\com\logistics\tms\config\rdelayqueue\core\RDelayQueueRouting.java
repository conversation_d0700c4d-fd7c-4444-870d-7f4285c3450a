package com.logistics.tms.config.rdelayqueue.core;

import lombok.extern.slf4j.Slf4j;
import org.redisson.RedissonShutdownException;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executor;

/**
 * 转发业务类型到具体的处理类中
 *
 * <AUTHOR>
 * @date 2024/04/25
 */
@Slf4j
public class RDelayQueueRouting {

    private RDelayQueue rDelayQueue;

    private List<RDelayQueueHandler> rDelayQueueHandlers;

    private Executor executor;

    private volatile boolean flag = true;

    public RDelayQueueRouting(RDelayQueue rDelayQueue, List<RDelayQueueHandler> rDelayQueueHandlers, Executor executor) {
        Objects.requireNonNull(rDelayQueue,"Spring 上下文中 RDelayQueue 不存在！");
        this.rDelayQueue = rDelayQueue;
        this.rDelayQueueHandlers = rDelayQueueHandlers;
        this.executor = executor;
    }

    public void init() {
        log.info("延迟队列业务路由器启动成功");
        new Thread(() -> {
            while (flag) {
                try {
                    DelayMsg delayMsg = rDelayQueue.take();
                    for (RDelayQueueHandler rDelayQueueHandler : rDelayQueueHandlers) {
                        if (rDelayQueueHandler.support(delayMsg.getDelayMsgType())) {
                            executor.execute(() -> {
                                try {
                                    rDelayQueueHandler.exc(delayMsg.getObject());
                                } catch (Exception e) {
                                    log.error("延迟消息处理失败：业务类型：{},业务消息内容：{}", delayMsg.getDelayMsgType(), delayMsg.getObject(), e);
                                }
                            });
                            break;
                        }
                    }
                } catch (RedissonShutdownException e) {
                    log.info("延迟对接获取数据失败,客户端已经关闭", e);
                    flag = false;
                } catch (Exception e) {
                    log.error("延迟对接获取数据失败", e);
                }
            }
        }).start();
    }
}
