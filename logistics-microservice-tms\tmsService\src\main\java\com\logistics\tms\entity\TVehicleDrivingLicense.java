package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleDrivingLicense extends BaseEntity {
    /**
    * 基本信息外键
    */
    @ApiModelProperty("基本信息外键")
    private Long vehicleId;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 车辆类型
    */
    @ApiModelProperty("车辆类型")
    private Long vehicleType;

    /**
    * 住址
    */
    @ApiModelProperty("住址")
    private String address;

    /**
    * 所有人
    */
    @ApiModelProperty("所有人")
    private String owner;

    /**
    * 品牌
    */
    @ApiModelProperty("品牌")
    private String brand;

    /**
    * 型号
    */
    @ApiModelProperty("型号")
    private String model;

    /**
    * 车辆识别号
    */
    @ApiModelProperty("车辆识别号")
    private String vehicleIdentificationNumber;

    /**
    * 发动机号码
    */
    @ApiModelProperty("发动机号码")
    private String engineNumber;

    /**
    * 发证部门
    */
    @ApiModelProperty("发证部门")
    private String certificationDepartment;

    /**
    * 注册日期
    */
    @ApiModelProperty("注册日期")
    private Date registrationDate;

    /**
    * 发证日期
    */
    @ApiModelProperty("发证日期")
    private Date issueDate;

    /**
    * 归档编号
    */
    @ApiModelProperty("归档编号")
    private String filingNumber;

    /**
    * 核定载人数
    */
    @ApiModelProperty("核定载人数")
    private Integer authorizedCarryingCapacity;

    /**
    * 总质量(KG)
    */
    @ApiModelProperty("总质量(KG)")
    private BigDecimal totalWeight;

    /**
    * 整备质量(KG)
    */
    @ApiModelProperty("整备质量(KG)")
    private BigDecimal curbWeight;

    /**
    * 准牵引总质量(KG)
    */
    @ApiModelProperty("准牵引总质量(KG)")
    private BigDecimal tractionMassWeight;

    /**
    * 核定载质量(KG)
    */
    @ApiModelProperty("核定载质量(KG)")
    private BigDecimal approvedLoadWeight;

    /**
    * 长(mm)
    */
    @ApiModelProperty("长(mm)")
    private Integer length;

    /**
    * 宽(mm)
    */
    @ApiModelProperty("宽(mm)")
    private Integer width;

    /**
    * 高(mm)
    */
    @ApiModelProperty("高(mm)")
    private Integer height;

    /**
    * 车辆强制报废期
    */
    @ApiModelProperty("车辆强制报废期")
    private Date obsolescenceDate;

    /**
    * 车辆轴数
    */
    @ApiModelProperty("车辆轴数")
    private Integer axleNumber;

    /**
    * 驱动轴数
    */
    @ApiModelProperty("驱动轴数")
    private Integer driveShaftNumber;

    /**
    * 轮胎数
    */
    @ApiModelProperty("轮胎数")
    private Integer tiresNumber;

    /**
    * 车牌颜色 1 黄色 2 蓝色
    */
    @ApiModelProperty("车牌颜色 1 黄色 2 蓝色")
    private Integer plateColor;

    /**
    * 车身颜色
    */
    @ApiModelProperty("车身颜色")
    private String bodyColor;
}