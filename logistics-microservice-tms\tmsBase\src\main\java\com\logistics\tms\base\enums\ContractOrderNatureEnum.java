package com.logistics.tms.base.enums;

/**
 * 合同性质枚举
 * @Author: sj
 * @Date: 2019/4/8 9:14
 */
public enum ContractOrderNatureEnum {

    GOODS_CONTRACT(1, "货源合同"),
    VEHICLE_CONTRACT(2, "车源合同"),
    LEASE_CONTRACT(3,"租赁合同"),;

    private Integer key;
    private String value;

    ContractOrderNatureEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
