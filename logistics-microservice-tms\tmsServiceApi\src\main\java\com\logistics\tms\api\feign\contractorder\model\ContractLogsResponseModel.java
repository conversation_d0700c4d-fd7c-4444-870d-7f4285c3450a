package com.logistics.tms.api.feign.contractorder.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 合同操作日志
 * @Author: sj
 * @Date: 2019/4/4 17:22
 */
@Data
public class ContractLogsResponseModel {
    @ApiModelProperty("日志id")
    private Long operateLogsId;
    @ApiModelProperty("操作人")
    private String operateUserName;
    @ApiModelProperty("操作时间")
    private Date operateTime;
    @ApiModelProperty("操作动作")
    private Integer operateType;
    @ApiModelProperty("说明")
    private String operateContents;
    @ApiModelProperty("备注")
    private String remark;
}
