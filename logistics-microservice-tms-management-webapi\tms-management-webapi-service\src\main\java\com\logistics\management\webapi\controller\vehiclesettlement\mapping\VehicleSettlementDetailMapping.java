package com.logistics.management.webapi.controller.vehiclesettlement.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.client.vehiclesettlement.response.*;
import com.logistics.management.webapi.controller.vehiclesettlement.response.GetCarrierOrderByVehicleIdResponseDto;
import com.logistics.management.webapi.controller.vehiclesettlement.response.GetOilFilledByVehicleIdResponseDto;
import com.logistics.management.webapi.controller.vehiclesettlement.response.GetVehicleSettlementDetailResponseDto;
import com.logistics.management.webapi.controller.vehiclesettlement.response.GetVehicleTireByVehicleIdResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @author: wjf
 * @date: 2019/10/15 9:59
 */
public class VehicleSettlementDetailMapping extends MapperMapping<GetVehicleSettlementDetailResponseModel,GetVehicleSettlementDetailResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;
    public VehicleSettlementDetailMapping(String imagePrefix , Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        GetVehicleSettlementDetailResponseModel source = getSource();
        GetVehicleSettlementDetailResponseDto destination = getDestination();
        if (source != null) {
            destination.setSettlementYear(source.getSettlementMonth().substring(0,4));
            if (source.getSettlementMonth().substring(source.getSettlementMonth().length() - 2).compareTo(CommonConstant.TEN) < CommonConstant.INTEGER_ZERO) {
                destination.setMonth(source.getSettlementMonth().substring(source.getSettlementMonth().length() - 1));
            } else {
                destination.setMonth(source.getSettlementMonth().substring(source.getSettlementMonth().length() - 2));
            }
            destination.setStatusLabel(VehicleSettlementStatusEnum.getEnum(source.getStatus()).getValue());
            //运单信息处理
            BigDecimal dispatchFreightFeeTotal = CommonConstant.BIG_DECIMAL_ZERO;//运单运费
            if (ListUtils.isNotEmpty(source.getCarrierOrderList())) {
                List<GetCarrierOrderByVehicleIdResponseDto> carrierOrderList = new ArrayList<>();
                List<GetCarrierOrderByVehicleIdResponseDto> carrierOrderListForLeYi = new ArrayList<>();
                GetCarrierOrderByVehicleIdResponseDto dto;
                BigDecimal amount;
                for (GetCarrierOrderByVehicleIdResponseModel model : source.getCarrierOrderList()) {
                    dto = MapperUtils.mapper(model, GetCarrierOrderByVehicleIdResponseDto.class);
                    amount = model.getUnloadAmount();
                    dto.setAmount(amount.stripTrailingZeros().toPlainString()+GoodsUnitEnum.getEnum(model.getGoodsUnit()).getUnit());

                    //计算司机费用
                    BigDecimal dispatchFreightFee = model.getDispatchFreightFee();
                    if (model.getDispatchFreightFeeType().equals(PriceTypeEnum.UNIT_PRICE.getKey())) {
                        dispatchFreightFee = dispatchFreightFee.multiply(amount).setScale(2, BigDecimal.ROUND_HALF_UP);
                    }
                    //司机费用加上调整费用、多装多装费用
                    dispatchFreightFee = dispatchFreightFee.add(model.getAdjustFee()).add(model.getMarkupFee());
                    //如果存在临时费用，则加上临时费用
                    if (model.getDriverOtherFee() != null){
                        dispatchFreightFee = dispatchFreightFee.add(model.getDriverOtherFee());
                    }
                    dto.setDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee));
                    //处理发卸地址信息
                    dto.setLoadAddress((StringUtils.isNotEmpty(model.getLoadWarehouse())?"【" + model.getLoadWarehouse() + "】":model.getLoadProvinceName()+model.getLoadCityName()+model.getLoadAreaName()));
                    dto.setUnloadAddress(model.getUnloadProvinceName()+model.getUnloadCityName()+model.getUnloadAreaName());
                    dto.setLoadAddressPdf(model.getLoadProvinceName()+model.getLoadCityName()+model.getLoadAreaName());
                    dto.setUnloadAddressPdf(model.getUnloadProvinceName()+model.getUnloadCityName()+model.getUnloadAreaName());
                    dto.setDispatchTime(DateUtils.dateToString(model.getDispatchTime(),CommonConstant.DATE_TO_STRING_YMD_PATTERN));
                    if(ListUtils.isEmpty(model.getTicketList())){
                        dto.setTicketList(new ArrayList<>());
                    }else{
                        List<String> ticketList=new ArrayList<>();
                        model.getTicketList().forEach(item->ticketList.add(imagePrefix+imageMap.get(item)));
                        dto.setTicketList(ticketList);
                    }
                    dispatchFreightFeeTotal = dispatchFreightFeeTotal.add(dispatchFreightFee);

                    //云盘、新生的单子归属【包装业务】
                    if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(model.getDemandOrderSource()) || DemandOrderSourceEnum.YELO_LIFE.getKey().equals(model.getDemandOrderSource())){
                        carrierOrderListForLeYi.add(dto);
                    }
                    //其他为【石化业务】
                    else{
                        carrierOrderList.add(dto);
                    }
                }
                destination.setCarrierOrderList(carrierOrderList);
                destination.setCarrierOrderListForLeYi(carrierOrderListForLeYi);
                destination.setCarrierOrderCount(ConverterUtils.toString(source.getCarrierOrderList().size()));
                destination.setDispatchFreightFeeTotal(ConverterUtils.toString(dispatchFreightFeeTotal));
            }
            BigDecimal freightFeeTotal = CommonConstant.BIG_DECIMAL_ZERO_NET_ZERO;//运费合计（运单运费+调整费用）
            freightFeeTotal = freightFeeTotal.add(dispatchFreightFeeTotal);
            if (CommonConstant.INTEGER_ONE.equals(source.getIfAdjustFee())){
                freightFeeTotal = freightFeeTotal.add(source.getAdjustFee());
                destination.setIfAdjustFeeLabel("是");
            }else{
                destination.setAdjustFee("");
                destination.setIfAdjustFeeLabel("否");
            }
            destination.setFreightFeeTotal(ConverterUtils.toString(freightFeeTotal));

            //轮胎信息处理
            BigDecimal tireCostTotal = CommonConstant.BIG_DECIMAL_ZERO;
            if (ListUtils.isNotEmpty(source.getVehicleTireList())) {
                List<GetVehicleTireByVehicleIdResponseDto> vehicleTireList = new ArrayList<>();
                GetVehicleTireByVehicleIdResponseDto dto;
                for (GetVehicleTireByVehicleIdResponseModel model : source.getVehicleTireList()) {
                    dto = MapperUtils.mapper(model, GetVehicleTireByVehicleIdResponseDto.class);
                    BigDecimal tireCost = model.getUnitPrice().multiply(ConverterUtils.toBigDecimal(model.getAmount())).setScale(2, BigDecimal.ROUND_HALF_UP);
                    dto.setTireCost(ConverterUtils.toString(tireCost));
                    if (model.getReplaceDate() != null) {
                        dto.setReplaceDate(DateUtils.dateToString(model.getReplaceDate(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
                    }
                    vehicleTireList.add(dto);
                    tireCostTotal = tireCostTotal.add(tireCost);
                }
                destination.setVehicleTireList(vehicleTireList);
                destination.setVehicleTireCount(ConverterUtils.toString(source.getVehicleTireList().size()));
                destination.setTireCostTotal(ConverterUtils.toString(tireCostTotal));
            }

            //充油信息处理
            BigDecimal oilFilledFeeTotal = CommonConstant.BIG_DECIMAL_ZERO;
            if (ListUtils.isNotEmpty(source.getOilFilledList())) {
                List<GetOilFilledByVehicleIdResponseDto> oilFilledList = new ArrayList<>();
                GetOilFilledByVehicleIdResponseDto dto;
                for (GetOilFilledByVehicleIdResponseModel model : source.getOilFilledList()) {
                    dto = MapperUtils.mapper(model, GetOilFilledByVehicleIdResponseDto.class);
                    if (model.getOilFilledDate() != null) {
                        dto.setOilFilledDate(DateUtils.dateToString(model.getOilFilledDate(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
                    }
                    if (model.getOilFilledType() != null) {
                        dto.setOilFilledType(OilFilledTypeEnum.getEnum(model.getOilFilledType()).getValue());
                    }
                    if (OilFilledTypeEnum.OIL_FILLED_CARD.getKey().equals(model.getOilFilledType())) {
                        dto.setLiter("");
                    }
                    if (OilFilledTypeEnum.OIL_FILLED_CAR.getKey().equals(model.getOilFilledType())) {
                        dto.setTopUpIntegral("");
                        dto.setRewardIntegral("");
                    }
                    oilFilledList.add(dto);
                    oilFilledFeeTotal = oilFilledFeeTotal.add(model.getOilFilledFee());
                }
                destination.setOilFilledList(oilFilledList);
                destination.setOilFilledCount(ConverterUtils.toString(source.getOilFilledList().size()));
                destination.setOilFilledFeeTotal(ConverterUtils.toString(oilFilledFeeTotal));
            }

            String currentMonth = source.getSettlementMonth();

            //gps费用信息处理
            BigDecimal gpsRemainingDeductingFeeTotal = CommonConstant.BIG_DECIMAL_ZERO;
            if (source.getGpsFeeModel() != null) {
                GetGpsFeeByVehicleIdResponseModel model = source.getGpsFeeModel();
                BigDecimal gpsFeeTotal = model.getServiceFee().multiply(ConverterUtils.toBigDecimal(model.getCooperationPeriod())).setScale(2, BigDecimal.ROUND_HALF_UP);
                destination.setGpsFeeTotal(ConverterUtils.toString(gpsFeeTotal));
                if (model.getDeductingFee() != null && model.getDeductingFee().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                    if (model.getCurrentMonth().equals(model.getDeductingMonth())) {
                        destination.setGpsDeductingFee(ConverterUtils.toString(model.getDeductingFee()));
                        gpsRemainingDeductingFeeTotal = model.getDeductingFee().add(model.getRemainingDeductingFee());
                    }else{
                        gpsRemainingDeductingFeeTotal = model.getRemainingDeductingFee();
                    }
                } else {
                    if (model.getRemainingDeductingFee() != null){
                        gpsRemainingDeductingFeeTotal = model.getRemainingDeductingFee();
                    }else {
                        gpsRemainingDeductingFeeTotal = gpsFeeTotal;
                    }
                }
                destination.setGpsRemainingDeductingFee(ConverterUtils.toString(model.getRemainingDeductingFee()));
                destination.setGpsRemainingDeductingFeeTotal(ConverterUtils.toString(gpsRemainingDeductingFeeTotal));
                if (model.getFinishDate() != null && currentMonth.equals(DateUtils.dateToString(model.getFinishDate(), CommonConstant.DATE_TO_STRING_YM_PATTERN))) {
                    destination.setIfModifyReducedGPSCosts(CommonConstant.ZERO);
                }
            }

            //停车费用信息处理
            BigDecimal parkingRemainingDeductingFeeTotal = CommonConstant.BIG_DECIMAL_ZERO;
            if (source.getParkingFeeModel() != null) {
                GetParkingFeeByVehicleIdResponseModel model = source.getParkingFeeModel();
                BigDecimal parkingFeeTotal = model.getParkingFee().multiply(ConverterUtils.toBigDecimal(model.getCooperationPeriod())).setScale(2, BigDecimal.ROUND_HALF_UP);
                destination.setParkingFeeTotal(ConverterUtils.toString(parkingFeeTotal));
                if (model.getDeductingFee() != null && model.getDeductingFee().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                    if (model.getCurrentMonth().equals(model.getDeductingMonth())) {
                        destination.setParkingDeductingFee(ConverterUtils.toString(model.getDeductingFee()));
                        parkingRemainingDeductingFeeTotal = model.getDeductingFee().add(model.getRemainingDeductingFee());
                    }else{
                        parkingRemainingDeductingFeeTotal = model.getRemainingDeductingFee();
                    }
                } else {
                    if (model.getRemainingDeductingFee() != null){
                        parkingRemainingDeductingFeeTotal = model.getRemainingDeductingFee();
                    }else{
                        parkingRemainingDeductingFeeTotal = parkingFeeTotal;
                    }
                }
                destination.setParkingRemainingDeductingFee(ConverterUtils.toString(model.getRemainingDeductingFee()));
                destination.setParkingRemainingDeductingFeeTotal(ConverterUtils.toString(parkingRemainingDeductingFeeTotal));
                if (model.getFinishDate() != null && currentMonth.equals(DateUtils.dateToString(model.getFinishDate(), CommonConstant.DATE_TO_STRING_YM_PATTERN))) {
                    destination.setIfModifyReducedParkCosts(CommonConstant.ZERO);
                }
            }

            //保险费用信息处理
            BigDecimal insuranceFee = CommonConstant.BIG_DECIMAL_ZERO;
            BigDecimal vehicleClaimFee = CommonConstant.BIG_DECIMAL_ZERO;
            BigDecimal insuranceRemainingDeductingFeeTotal = CommonConstant.BIG_DECIMAL_ZERO;
            if (ListUtils.isNotEmpty(source.getInsuranceCostList())) {
                vehicleClaimFee = source.getVehicleClaimFee();
                for (VehicleInsuranceCostResponseModel model : source.getInsuranceCostList()) {
                    if (model.getPayCost() != null) {
                        insuranceFee = insuranceFee.add(model.getPayCost());
                    }
                    if (model.getUnPaidCost() != null) {
                        insuranceRemainingDeductingFeeTotal = insuranceRemainingDeductingFeeTotal.add(model.getUnPaidCost());
                    }
                }
                destination.setVehicleClaimFee(ConverterUtils.toString(vehicleClaimFee));
                destination.setInsuranceFee(ConverterUtils.toString(insuranceFee));
                destination.setInsuranceRemainingDeductingFee(ConverterUtils.toString(insuranceRemainingDeductingFeeTotal));
            }
            //个人意外险
            if (source.getAccidentInsuranceFee().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                destination.setAccidentInsuranceRemainingDeductingFee(ConverterUtils.toString(source.getAccidentInsuranceExpenseTotal().subtract(source.getAccidentInsuranceFee())));
            } else {
                destination.setAccidentInsuranceRemainingDeductingFee(ConverterUtils.toString(source.getAccidentInsuranceExpenseTotal()));
                if (source.getAccidentInsuranceFee().compareTo(BigDecimal.ZERO) == CommonConstant.INTEGER_ZERO) {
                    destination.setAccidentInsuranceFee(CommonConstant.ZERO);
                }
            }

            //贷款费用信息处理
            BigDecimal loanFeeRemainingDeductingFeeTotal = CommonConstant.BIG_DECIMAL_ZERO;
            if (source.getLoanFeeModel() != null) {
                GetLoanFeeByVehicleIdResponseModel model = source.getLoanFeeModel();
                destination.setLoanFeeTotal(ConverterUtils.toString(model.getLoanFee()));
                if (model.getDeductingFee() != null && model.getDeductingFee().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                    if (model.getCurrentMonth().equals(model.getDeductingMonth())) {
                        destination.setLoanFee(ConverterUtils.toString(model.getDeductingFee()));
                        destination.setLoanFeeRemainingDeductingFee(ConverterUtils.toString(model.getRemainingDeductingFee()));
                        loanFeeRemainingDeductingFeeTotal = model.getDeductingFee().add(model.getRemainingDeductingFee());
                    }else{
                        loanFeeRemainingDeductingFeeTotal = model.getRemainingDeductingFee();
                    }
                } else {
                    if (model.getRemainingDeductingFee() != null){
                        loanFeeRemainingDeductingFeeTotal = model.getRemainingDeductingFee();
                    }else {
                        loanFeeRemainingDeductingFeeTotal = model.getLoanFee();
                    }
                }
                destination.setLoanFeeRemainingDeductingFeeTotal(ConverterUtils.toString(loanFeeRemainingDeductingFeeTotal));
                if (model.getLoadFinishTime() != null && currentMonth.equals(DateUtils.dateToString(model.getLoadFinishTime(), CommonConstant.DATE_TO_STRING_YM_PATTERN))) {
                    destination.setIfModifyReducedLoanCosts(CommonConstant.ZERO);
                }
            }
            //理赔费用
            destination.setClaimTotal(ConverterUtils.toString(vehicleClaimFee.add(Optional.ofNullable(source.getAccidentInsuranceClaimFee()).orElse(BigDecimal.ZERO)).setScale(2,BigDecimal.ROUND_HALF_UP)));
            //附件
            if (StringUtils.isNotBlank(source.getAttachment())) {
                destination.setAttachmentPath(imagePrefix + imageMap.get(source.getAttachment()));
            }
            if (OilFilledStatusEnum.WAIT_SETTLE.getKey().equals(source.getStatus())) {
                destination.setGpsRemainingDeductingFee(ConverterUtils.toString(gpsRemainingDeductingFeeTotal));
                destination.setParkingRemainingDeductingFee(ConverterUtils.toString(parkingRemainingDeductingFeeTotal));
                destination.setLoanFeeRemainingDeductingFee(ConverterUtils.toString(loanFeeRemainingDeductingFeeTotal));
                BigDecimal deductingFeeTotal = tireCostTotal.add(oilFilledFeeTotal).add(insuranceFee);
                destination.setDeductingFeeTotal(ConverterUtils.toString(deductingFeeTotal.compareTo(CommonConstant.BIG_DECIMAL_ZERO)==CommonConstant.INTEGER_ZERO?CommonConstant.BIG_DECIMAL_ZERO_NET_ZERO:deductingFeeTotal));
                destination.setRemainingDeductingFeeTotal(ConverterUtils.toString(gpsRemainingDeductingFeeTotal.add(parkingRemainingDeductingFeeTotal).add(loanFeeRemainingDeductingFeeTotal).add(insuranceRemainingDeductingFeeTotal)));
                destination.setActualExpensesPayable(ConverterUtils.toString(freightFeeTotal.add(vehicleClaimFee).subtract(deductingFeeTotal).setScale(2,BigDecimal.ROUND_HALF_UP)));
            }
            if(ListUtils.isEmpty(source.getEventList())){
                destination.setEventList(new ArrayList<>());
            }
        }
    }
}
