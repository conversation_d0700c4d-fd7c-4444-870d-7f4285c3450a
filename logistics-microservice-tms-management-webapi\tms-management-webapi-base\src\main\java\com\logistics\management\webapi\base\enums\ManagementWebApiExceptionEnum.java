package com.logistics.management.webapi.base.enums;

import com.yelo.tray.core.base.enums.BaseExceptionEnum;

public enum ManagementWebApiExceptionEnum implements BaseExceptionEnum{
    UPLOAD_SIZE_TO_LONG(60001, "文件大于5M"),
    BASE_DATA_SERVICE_ERROR(60002, "基础数据服务请求失败"),
    COMMON_IO_EXCEPTION(60003, "图片上传失败"),
    IMPORT_FAILURE(60004,"导入失败"),
    MOBILE_FORMAT_ERROR(60005, "手机号格式不正确"),
    VERIFY_CODE_FORMAT_ERROR(60006, "密码格式不正确"),
    CARRIER_ORDER_EXIST(60007,"运单不存在"),
    GENERATE_PICK_UP_GOOD_FAILED(60008,"生成提货单失败"),
    ZIP_COMPRESS_ERROR(60009,"文件压缩失败"),
    TRAY_BE_DISABLED_OR_NULL(60010, "托盘被禁用或不存在"),
    WAREHOUSE_BE_DISABLED_OR_NULL(60011, "仓库被禁用或不存在"),
    ID_CARD_FORMAT_ERROR(60012, "身份证号格式不正确"),
    COMPANY_CARRIER_DIFFERENT(60014,"承运商不同不允许批量调度车辆"),
    AI_UPLOAD_SIZE_TO_LONG(60015, "文件大于4M"),
    STATUS_CHANGE(60016,"操作失败，请刷新重试"),
    COMPANY_ENTRUST_EMPTY(60017,"请先选择货主"),
    IMPORT_INSURANCE_COMPANY_FILE_IS_EMPTY(60018,"导入保险公司文件信息为空"),
    BATCH_NUMBER_ERROR(60019,"请维护批单号，批单号只允许大写英文/数字，0＜单号≤50"),
    INSURANCE_TICKETS(60020,"请维护单据，且不超过10张"),
    IMPORT_PERSONAL_ACCIDENT_INSURANCE_EMPTY(60021,"导入个人意外险文件为空"),
    REMINDER_DAYS_ERROR(60022,"预提醒天数必填，范围1≤天数≤365"),
    IMPORT_VEHICLE_TYPE_FILE_IS_EMPTY(60023,"导入车辆类型文件为空"),
    IMPORT_VIOLATION_REGULATIONS_FILE_IS_EMPTY(60024,"导入违章事故信息为空"),
    SCOPE_OF_FINES_EXCEPTION(60025,"罚款金额范围 0≤返款≤10000"),
    PAYMENT_OF_VEHICLE_AND_VESSEL_TAX_EMPTY(60026,"请维护代缴车船税"),
    POLICY_TYPE_ERROR(60027,"请选择保单类型"),
    POLICY_NUMBER_EMPTY(60028,"请维护保单号"),
    INSURANCE_TICKETS_ERROR(60029,"请上传凭证，1≤图片张数≤4"),
    STAFF_TYPE_ERROR(60030,"请选择人员类别"),
    GENDER_ERROR(60031,"请选择性别"),
    PERMITTED_TYPE_ERROR(60032,"请维护准驾车型"),
    DRIVER_LICENSE_DATE(60033,"请维护驾照期限"),
    DRIVER_TICKETS_EMPTY(60034,"请上传证件"),
    IDENTITY_NUMBER_EMPTY(60035,"请上传身份证"),
    DRIVER_LICENSE_EMPTY(60036,"请上传机动车驾驶证"),
    OCCUPATIONAL_REQUIREMENTS_EMPTY(60037,"请上传从业资格证"),
    VEHICLE_NUMBER_IS_ERROR(60038,"请维护正确的车牌号"),
    IMPORT_VEHICLE_FILE_IS_EMPTY(60039,"导入车辆资质信息为空"),
    ASSOCIATED_WITHHOLDING_POLICY_EMPTY(60040,"请关联扣费保单"),
    REJECT_REASON_EMPTY(60041,"驳回原因不能为空，且1≤长度≤300"),
    OPERATION_TYPE_ERROR(60042,"操作类型：1 审核，2 驳回"),
    FORMAT_ERROR(60043, "格式有误"),
    IMPORT_VEHICLE_TIRE_FILE_IS_EMPTY(60044,"导入轮胎管理文件为空"),
    ORDER_BATCH_SEARCH_MAX_ONE_THOUSAND(60045, "查询数量最多1000条"),
    DRIVER_LICENSE_NO_ERROR(60046,"请维护机动车驾驶证号"),
    OCR_TYPE_CANNOT_BLANK(60047, "票据识别类型不能为空"),
    ERROR_OCR_INVOICE(60048, "发票有误，请重新上传"),
    DISPATCH_FEE_TYPE_ERROR(60049,"请选择司机运费类型"),
    MODIFY_ADJUST_FEE_EMPTY(60050,"是否修改调整价格：0 否 1 是"),
    ADJUST_FEE_EMPTY(60051,"请维护调整价格"),
    DRIVER_FEE_ERROR(60052, "请维护正确的司机运费"),
    TIRE_NO_COUNT_ERROR(60053,"牌号数量不能超过5条"),
    TIRE_BRAND_REPEAT(60054,"牌号不能重复"),
    VEHICLE_PAYEE_REL_EMPTY(60055,"导入车辆账户关联关系文件为空"),
    IMPORT_BANK_CONFIG_FILE_IS_EMPTY(60056,"导入银行配置文件信息为空"),
    IMPORT_DRIVER_PAYEE_FILE_IS_EMPTY(60057,"导入收款账号文件为空"),
    PARAMETER_ERROR(60058,"参数错误"),
    CONTRACT_FILE_COUNT_MAX(60059,"最多上传50个附件"),
    DEMAND_ORDER_GOODS_UNIT_DIFFERENT(60060,"委托货物请选择正确且相同的单位"),
    IMPORT_STAFF_VEHICLE_FILE_IS_EMPTY(60061,"导入车辆司机信息为空"),
    COMPANY_CARRIER_TRADING_AMEND_DATE(60062,"请选择营业执照的有效期"),
    COMPANY_CARRIER_TRADING_AMEND(60063,"请上传营业执照"),
    LOAN_RECORD_FILE_COUNT_MAX(60064,"最多上传3个附件"),
    NAKED_CAR_PRICE_REG(60065,"0<裸车价<2000000"),
    INSURANCE_PREMIUM_REG(60066,"0<保险费<500000"),
    PURCHASE_TAX_REG(60067,"0<购置税<500000"),
    CAR_PRICE_REG(60068,"总价,纯数字,可保留2位小数"),
    LOAN_FEE_REG(60069,"总贷款费用,纯数字,可保留2位小数"),
    LOAN_PERIODS_REG(60070,"0<总期数≤500"),
    LOAN_RATE_REG(60071,"0<贷款利率<100"),
    LOAN_COMMISSION_REG(60072,"0<贷款手续费<500000"),
    LOAN_INTEREST_REG(60073,"0<贷款利息<500000"),
    PLEASE_SELECT_UPLOAD_IMAGE(60074,"请选择上传的图片"),
    REMARK_LENGTH_MAX(60075,"备注不能超过300字"),
    ATTACHMENT_FILE_SIZE_MAX(60076,"附件最多六张"),

    DEDUCTION_RANGE_OF_ILLEGAL_POINTS(60077,"违章扣分范围【0-12】"),
    FINE_RANGE_OF_ILLEGAL_POINTS(60078,"罚款金额范围【0-10000】"),

    SUB_CARD_NUMBER_EMPTY(60078,"副卡卡号不能为空"),
    REFUND_REASON_EMPTY(60079,"其他原因不能为空，且不能超过10个字符"),

    VEHICLE_SETTLEMENT_ADJUST_FEE_ERROR(60080,"请维护调整费用，0<调整费用≤1000000，最多2位小数"),
    VEHICLE_SETTLEMENT_ADJUST_REMARK_ERROR(60081,"请填写调整原因，1~100字"),
    FREIGHT_UNIT_PRICE_ERROR(60082,"请维护正确的单价，0.01~50,00"),
    FREIGHT_FIXED_PRICE_ERROR(60083,"请维护正确的一口价，0.01~100,000"),
    FREIGHT_FEE_TYPE_ERROR(60084,"请选择价格类型"),
    PLEASE_SELECT_CUSTOMER(60086,"请选择客户"),
    SETTLEMENT_STATEMENT_HANDLE_REMARK_ERROR(60087,"请填写调整原因，1~100字"),
    VEHICLESETTLEMENT_ADJUST_FEE_ERROR(60088,"调整费用金额错误"),
    VEHICLE_SETTLEMENT_REMARK_LENGTH_ERROR(60089,"备注不能超过100字"),
    CARRIER_ORDER_CORRECT_TICKETS_SIZE_MAX(60090,"请上传回单，最多2张"),
    SINOPEC_OBJECTION_TICKETS_SIZE_MAX(60091,"请上传审核依据，最多2张"),
    REMARK_EMPTY(60092, "请填写备注"),
    COMPANY_NAME_OR_WATR_FORBBDIEN_WORD(60093, "公司名称或水印含有违禁词"),
    COMPANY_CARRIER_ID_IS_NULL(60094, "请选择车主"),
    FREIGHT_FEE_IS_NULL(60095,"请填写价格" ),
    CARRIER_PRICE_TYPE_ERROR(60096, "请选择正确的价格类型"),
    CARRIER_UNIT_PRICE_ERROR(60097, "请填写正确的单价,大于0,小于等于1000"),
    CARRIER_FIXED_PRICE_ERROR(60098, "请填写正确的一口价,大于0,小于等于50000"),
    NO_COMPANY_CARRIER(60100, "车主不存在"),
    CHOOSE_CARDATA(60107, "请选择要添加的车辆"),
    BATCH_RELEASE_SELECT_UNIT_PRICE(60108, "批量发布：仅允许单价"),
    DEMADNLIST_ISNOTNULL(60109, "请选择需求单"),
    PUBLISH_DEMAND_ORDER_MAX(60110, "最多发布10条数据"),
    SKU_CODE_NOT_UNIQUE(60111, "货物不可重复"),
    BUSINESS_TYPE_NOT_DUPLICATE(60111,"业务类型重复，请检查业务类型"),
    DEFEND_LADDER_PRICE(60112,"请维护当前阶梯价格"),
    START_PRICE_HIGHER_THAN_END_PRICE(60113,"起始数量必须从0开始且不能低于结束数量"),
    CARRIER_FREIGHT_AMOUNT_ERROR(60114,"件为单位时，数量不能为小数"),
    FROM_AREA_NOT_DUPLICATE(60115,"发货区不能重复"),
    TO_AREA_NOT_DUPLICATE(60116,"收货区不能重复"),
    CARRIER_ORDER_OTHER_FEE_TYPE_REPEAT(60117, "临时费用申请不可重复"),
    PRICE_ERROR(60118, "请填写正确的价格范围,大于0 小于100000"),
    CARRIER_SETTLE_STATEMENT_CONTRACT_CODE_ERROR(60119, "合同号不能超过30字符"),
    RESERVE_APPLY_AUDIT_NOT_EMPTY_APPROVE_AMOUNT(60120, "请输入批准金额"),
    REMARKS_VERIFICATION_MESSAGE(60121, "请输入驳回原因, 1-100字符"),
    RESERVE_COST_DEDUCTION_NOT_EMPTY(60122, "冲销费用列表不能为空"),
    RESERVE_APPLY_AUDIT_APPROVE_AMOUNT_EXCESS(60123, "批准金额范围在1-20000"),
    RESERVE_APPLY_AUDIT_NOT_EMPTY_BALANCE_AMOUNT(60124, "备用金余额不允许为空"),
    LOAD_CONTACT_MOBILE_CHECK_FAIL(60125, "请维护正确的发货地址联系方式"),
    RE_REPORT_WORK_NOT_EMPTY(60126, "工单ID不允许为空"),
    RE_REPORT_WORK_ARRIVE_SCENE_PICTURE_NOT_EMPTY(60127, "到达现场图片不允许为空"),
    LOADING_TRAYS_AMOUNT_LIMIT(60128, "可装托盘数范围0至5000的整数"),
    FREIGHT_REGION_SCHEME_CONFIG_REPEAT(60129, "区域方案配置重复！"),
    REFUEL_CARD_TIRE_FILE_IS_EMPTY(60130,"导入充油卡文件为空"),
    REFUEL_CAR_TIRE_FILE_IS_EMPTY(60131,"导入加油车文件为空"),
    IMPORT_FILE_LARGER_THAN_100(60132,"导入的文件大于100，无法导入"),
    WORK_GROUP_TIME_REQUIRE_IS_NULL(60133, "时间要求不能为空"),
    WORK_GROUP_USER_ERROR(60134, "请选择不同的人员"),
    WORK_GROUP_NODE_REPETITION(60135, "节点信息不允许重复创建"),
    ROLLBACK_CAUSE_TYPE_THREE(60136, "请选择回退原因三级"),
    WORK_GROUP_CODE_EMPTY(60137, "请正确维护机器人编码"),
    CONFIG_WAREHOUSE_EMPTY(60138, "请正确维护配置仓库"),
    DISTRICT_EMPTY(60139, "请维护配置区域"),
    WORK_GROUP_ORDER_FIELD_ERROR(60140, "信息模板字段错误"),
    CARRIER_PRICE_ERROR(60141, "请维护正确的车主运费"),
    ROUTE_ENQUIRY_CREATE_WAREHOUSE_EMPTY(60142, "发货仓库不能为空，且长度不能超过50"),
    ROUTE_ENQUIRY_CREATE_QUOTE_TIME_ERROR(60143, "报价生效期限截止时间必须大于开始时间"),
    ROUTE_ENQUIRY_NOT_OPERATE(60144, "竞价单不支持操作"),
    DISPATCH_COUNT_MAX(60145, "最多支持10个需求单一起调度"),
    SIGN_UP_BILL_3_ONLY_FOR_YR(60146, "该类型不支持查看预览"),
    ;


    private Integer code;
    private String msg;

    ManagementWebApiExceptionEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
