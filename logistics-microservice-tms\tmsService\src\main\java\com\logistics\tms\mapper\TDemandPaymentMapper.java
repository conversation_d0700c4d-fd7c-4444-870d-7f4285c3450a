package com.logistics.tms.mapper;

import com.logistics.tms.entity.TDemandPayment;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface TDemandPaymentMapper  extends BaseMapper<TDemandPayment> {

    int batchInsert(@Param("list")List<TDemandPayment> list);

    int batchUpdate(@Param("list") List<TDemandPayment> upPaymentList);

    TDemandPayment getByDemandOrderId(@Param("demandOrderId")Long demandOrderId);

    List<TDemandPayment> getByDemandOrderIds(@Param("demandOrderIds")String demandOrderIds);

    void updateByDemandOrderId(TDemandPayment tDemandPayment);
}