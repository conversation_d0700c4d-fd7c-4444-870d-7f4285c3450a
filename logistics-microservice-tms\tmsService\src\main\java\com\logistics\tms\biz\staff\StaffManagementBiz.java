package com.logistics.tms.biz.staff;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.common.SrcUrlModel;
import com.logistics.tms.api.feign.common.model.CertificatePictureModel;
import com.logistics.tms.controller.companycarrier.request.FuzzySearchCompanyCarrierRequestModel;
import com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.tms.biz.staffvehiclerelation.model.TStaffVehicleRelationModel;
import com.logistics.tms.api.feign.violationregulation.model.FuzzyQueryDriverInfoRequestModel;
import com.logistics.tms.api.feign.violationregulation.model.FuzzyQueryDriverInfoResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.ListJoinResultUtil;
import com.logistics.tms.base.utils.RegExpValidatorUtil;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.customeraccount.CustomerAccountBiz;
import com.logistics.tms.biz.staff.model.GetStaffIfCompleteByIdsModel;
import com.logistics.tms.controller.customeraccount.request.OpenAccountRequestModel;
import com.logistics.tms.controller.customeraccount.request.UpdateTCustomerAccountRelationIfCloseRequestModel;
import com.logistics.tms.controller.staff.request.*;
import com.logistics.tms.controller.staff.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.redisdistributedlock.DistributedLock;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.MapUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class StaffManagementBiz {

    @Autowired
    private TStaffBasicMapper tqStaffBasicMapper;
    @Autowired
    private TStaffDriverOccupationalRecordMapper tqStaffDriverOccupationalRecordMapper;
    @Autowired
    private TStaffDriverContinueLearningRecordMapper tqStaffDriverContinueLearningRecordMapper;
    @Autowired
    private TStaffDriverIntegrityExaminationRecordMapper tqStaffDriverIntegrityExaminationRecordMapper;
    @Autowired
    private TCertificationPicturesMapper tqCertificationPicturesMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TStaffDriverCredentialMapper staffDriverCredentialMapper;
    @Autowired
    private StaffTransBiz staffTransBiz;
    @Autowired
    private TStaffVehicleRelationMapper tStaffVehicleRelationMapper;
    @Autowired
    private CustomerAccountBiz customerAccountBiz;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private TCarrierDriverRelationMapper tCarrierDriverRelationMapper;
    @Autowired
    private TOperateLogsMapper tOperateLogsMapper;
    @Autowired
    private TCarrierOrderMapper tcarrierOrderMapper;
    @Autowired
    private TRealNameAuthenticationMapper tRealNameAuthenticationMapper;

    /**
     * 列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<SearchStaffManagementListResponseModel> searchStaffManagementList(SearchStaffManagementListRequestModel requestModel) {
        // 添加车主查询字段
        List<Long> companyIdList = null;
        if (StringUtils.isNotBlank(requestModel.getCompanyCarrierName())) {
            // 先根据公司名称、司机姓名、手机号模糊查询车主id
            FuzzySearchCompanyCarrierRequestModel fuzzyRequestModel = new FuzzySearchCompanyCarrierRequestModel();
            fuzzyRequestModel.setCompanyName(requestModel.getCompanyCarrierName());
            companyIdList = tCompanyCarrierMapper.fuzzyQueryCompanyCarrierInfo(fuzzyRequestModel)
                    .stream()
                    .map(FuzzySearchCompanyCarrierResponseModel::getCompanyId).collect(Collectors.toList());
            if (ListUtils.isEmpty(companyIdList)) {
                return new PageInfo<>(new ArrayList<>());
            }
        } else {
            //资产管理面板跳转只查询我司数据
            if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(requestModel.getIsOurCompany())) {
                companyIdList = Collections.singletonList(commonBiz.getQiyaCompanyCarrierId());
            }
        }

        requestModel.enablePaging();
        List<SearchStaffManagementListResponseModel> staffList = tCarrierDriverRelationMapper.queryDriverList(requestModel, companyIdList);
        PageInfo pageInfo = new PageInfo(staffList);
        pageInfo.setList(staffList);
        // 员工信息完整性
        getStaffIfComplete(staffList);
        return pageInfo;
    }

    //员工信息完整性
    private void getStaffIfComplete(List<SearchStaffManagementListResponseModel> staffList) {
        if(ListUtils.isEmpty(staffList)){
            return;
        }
        List<Long> staffIdList=staffList.stream().map(SearchStaffManagementListResponseModel::getStaffId).collect(Collectors.toList());
        String staffIds=StringUtils.listToString(staffIdList,',');


        Map<Long,Integer> staffContinueLearningMap=new HashMap<>();
        Map<Long,Integer> staffIntegrityExaminationMap=new HashMap<>();
        Map<Long,Integer> saffOccupationalMap=new HashMap<>();

        List<GetStaffIfCompleteByIdsModel> staffContinueLearningComplete = tqStaffBasicMapper.getStaffContinueLearningComplete(staffIds);
        if(ListUtils.isNotEmpty(staffContinueLearningComplete)){
            staffContinueLearningComplete.forEach(item->{
                staffContinueLearningMap.put(item.getStaffId(),item.getIfComplete());
            });
        }
        List<GetStaffIfCompleteByIdsModel> staffIntegrityExaminationComplete = tqStaffBasicMapper.getStaffIntegrityExaminationComplete(staffIds);
        if(ListUtils.isNotEmpty(staffIntegrityExaminationComplete)){
            staffIntegrityExaminationComplete.forEach(item->{
                staffIntegrityExaminationMap.put(item.getStaffId(),item.getIfComplete());
            });
        }
        List<GetStaffIfCompleteByIdsModel> staffOccupationalComplete = tqStaffBasicMapper.getStaffOccupationalComplete(staffIds);
        if(ListUtils.isNotEmpty(staffOccupationalComplete)){
            staffOccupationalComplete.forEach(item->{
                saffOccupationalMap.put(item.getStaffId(),item.getIfComplete());
            });
        }

        // 查询司机关联车主
        List<Long> companyIds = staffList.stream().map(SearchStaffManagementListResponseModel::getCompanyCarrierId)
                .distinct().collect(Collectors.toList());
        Map<Long, FuzzySearchCompanyCarrierResponseModel> companyCarrierMap = tCompanyCarrierMapper.selectCompanyCarrierInfoByIds(new ArrayList<>(companyIds))
                .stream().collect(Collectors.toMap(FuzzySearchCompanyCarrierResponseModel::getCompanyId, Function.identity(), (o1, o2) -> o2));

        staffList.forEach(item->{
            Integer continueLearningComplete = Optional.ofNullable(staffContinueLearningMap.get(item.getStaffId())).orElse(CommonConstant.INTEGER_ZERO);
            Integer integrityExaminationComplete = Optional.ofNullable(staffIntegrityExaminationMap.get(item.getStaffId())).orElse(CommonConstant.INTEGER_ZERO);
            Integer occupationalComplete = Optional.ofNullable(saffOccupationalMap.get(item.getStaffId())).orElse(CommonConstant.INTEGER_ZERO);

            if(CommonConstant.INTEGER_ZERO.equals(continueLearningComplete) || CommonConstant.INTEGER_ZERO.equals(integrityExaminationComplete)
                    ||CommonConstant.INTEGER_ZERO.equals(occupationalComplete)){
                item.setIfComplete(CommonConstant.INTEGER_ZERO);
            }else{
                item.setIfComplete(CommonConstant.INTEGER_ONE);
            }
            // 封装车主信息
            if (!MapUtils.isEmpty(companyCarrierMap) && companyCarrierMap.containsKey(item.getCompanyCarrierId())) {
                FuzzySearchCompanyCarrierResponseModel carrierResponseModel = companyCarrierMap.get(item.getCompanyCarrierId());
                if (carrierResponseModel != null) {
                    item.setCompanyCarrierType(carrierResponseModel.getCompanyType());
                    item.setCompanyCarrierName(carrierResponseModel.getCompanyName());
                    item.setCarrierContactName(carrierResponseModel.getContactName());
                    item.setCarrierContactPhone(carrierResponseModel.getContactPhone());
                    item.setIsOurCompany(carrierResponseModel.getIsOurCompany());
                }
            }
        });
    }

    /**
     * 导出
     * @param requestModel
     * @return
     */
    public List<ExportStaffManagementListResponseModel> exportStaffManagementList(SearchStaffManagementListRequestModel requestModel) {
        // 添加车主查询字段
        List<Long> companyIdList = null;
        if (StringUtils.isNotBlank(requestModel.getCompanyCarrierName())) {
            // 先根据公司名称、司机姓名、手机号模糊查询车主id
            FuzzySearchCompanyCarrierRequestModel fuzzyRequestModel = new FuzzySearchCompanyCarrierRequestModel();
            fuzzyRequestModel.setCompanyName(requestModel.getCompanyCarrierName());
            companyIdList = tCompanyCarrierMapper.fuzzyQueryCompanyCarrierInfo(fuzzyRequestModel)
                    .stream()
                    .map(FuzzySearchCompanyCarrierResponseModel::getCompanyId).collect(Collectors.toList());
            if (ListUtils.isEmpty(companyIdList)) {
                return new ArrayList<>();
            }
        } else {
            //资产管理面板跳转只查询我司数据
            if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(requestModel.getIsOurCompany())) {
                companyIdList = Collections.singletonList(commonBiz.getQiyaCompanyCarrierId());
            }
        }

        List<Long> staffRelIdList = tqStaffBasicMapper.searchStaffRelIdList(requestModel,companyIdList);
        List<ExportStaffManagementListResponseModel> resultList = new ArrayList<>();
        if (ListUtils.isNotEmpty(staffRelIdList)) {
            resultList = tqStaffBasicMapper.exportStaffList(StringUtils.listToString(staffRelIdList, ','));
            if (ListUtils.isNotEmpty(resultList)) {
                String staffIds = StringUtils.listToString(resultList.stream().map(ExportStaffManagementListResponseModel::getStaffId).collect(Collectors.toList()), ',');
                List<TStaffDriverIntegrityExaminationRecord> tqStaffDriverIntegrityExaminationRecords = tqStaffDriverIntegrityExaminationRecordMapper.getTopDriverIntegrityExaminationByStaffId(staffIds);
                ListJoinResultUtil.joinList(resultList, tqStaffDriverIntegrityExaminationRecords, (s, d) -> s.getStaffId() != null && s.getStaffId().equals(d.getStaffId()), (s, d) -> s.setIntegrityExaminationValidDate(d.getValidDate()));
                List<TStaffDriverContinueLearningRecord> topContinueLearningByStaffId = tqStaffDriverContinueLearningRecordMapper.getTopContinueLearningByStaffId(staffIds);
                ListJoinResultUtil.joinList(resultList, topContinueLearningByStaffId, (s, d) -> s.getStaffId() != null && s.getStaffId().equals(d.getStaffId()), (s, d) -> s.setContinueLearningValidDate(d.getValidDate()));
                List<TStaffDriverOccupationalRecord> occupationalRecordsByStaffIdList = tqStaffDriverOccupationalRecordMapper.getOccupationalRecordsByStaffId(staffIds);
                ListJoinResultUtil.joinList(resultList, occupationalRecordsByStaffIdList, (s, d) -> s.getStaffId() != null && s.getStaffId().equals(d.getStaffId()), (s, d) -> {
                    s.setOccupationalIssueDate(d.getIssueDate());
                    s.setOccupationalValidDate(d.getValidDate());
                });
            }
        }

        // 查询司机关联车主
        List<Long> companyIds = resultList.stream().map(ExportStaffManagementListResponseModel::getCompanyCarrierId)
                .distinct().collect(Collectors.toList());
        Map<Long, FuzzySearchCompanyCarrierResponseModel> companyCarrierMap = tCompanyCarrierMapper.selectCompanyCarrierInfoByIds(new ArrayList<>(companyIds))
                .stream().collect(Collectors.toMap(FuzzySearchCompanyCarrierResponseModel::getCompanyId, Function.identity(), (o1, o2) -> o2));
        for (ExportStaffManagementListResponseModel item : resultList) {
            // 封装车主信息
            if (!MapUtils.isEmpty(companyCarrierMap) && companyCarrierMap.containsKey(item.getCompanyCarrierId())) {
                FuzzySearchCompanyCarrierResponseModel carrierResponseModel = companyCarrierMap.get(item.getCompanyCarrierId());
                if (carrierResponseModel != null) {
                    item.setCompanyCarrierType(carrierResponseModel.getCompanyType());
                    item.setCompanyCarrierName(carrierResponseModel.getCompanyName());
                    item.setCarrierContactName(carrierResponseModel.getContactName());
                    item.setCarrierContactPhone(carrierResponseModel.getContactPhone());
                }
            }
        }
        return resultList;
    }

    /**
     * 人员信息新增/修改
     *
     * @param requestModel 人员信息
     */
    @Transactional
    @DistributedLock(prefix = CommonConstant.TMS_ADD_STAFF_LOCK,
            keys = "#requestModel.mobile",
            waitTime = 3)
    public Long addOrModifyStaff(AddOrModifyStaffTicketsRequestModel requestModel) {

        //驾照期限判断
        if ((CommonConstant.INTEGER_ONE.equals(requestModel.getStaffProperty()) || CommonConstant.INT_THREE.equals(requestModel.getStaffProperty()))
                && (requestModel.getType().equals(StaffTypeEnum.DRIVER.getKey()) || requestModel.getType().equals(StaffTypeEnum.DRIVER_SUPERCARGO.getKey()))
                && (requestModel.getDriversLicenseDateFrom().getTime() > requestModel.getDriversLicenseDateTo().getTime())) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_LICENSE_DATE_ERROR);
        }

        //人员信息
        TStaffBasic staffBasic = new TStaffBasic();
        staffBasic.setType(requestModel.getType());
        staffBasic.setGender(requestModel.getGender());
        staffBasic.setName(requestModel.getName());
        staffBasic.setAge(requestModel.getAge());
        staffBasic.setIdentityNumber(requestModel.getIdentityNumber());
        staffBasic.setIdentityValidity(requestModel.getIdentityValidity());
        staffBasic.setIdentityIsForever(requestModel.getIdentityIsForever());
        staffBasic.setLaborContractNo(requestModel.getLaborContractNo());
        staffBasic.setLaborContractValidDate(requestModel.getLaborContractValidDate());
        staffBasic.setRemark(requestModel.getRemark());
        staffBasic.setStaffProperty(requestModel.getStaffProperty());

        //证件信息
        TStaffDriverCredential staffDriverCredential = new TStaffDriverCredential();
        staffDriverCredential.setOccupationalRequirementsCredentialNo(requestModel.getOccupationalRequirementsCredentialNo());
        staffDriverCredential.setInitialIssuanceDate(requestModel.getInitialIssuanceDate());
        staffDriverCredential.setDriversLicenseNo(requestModel.getDriversLicenseNo());
        staffDriverCredential.setPermittedType(requestModel.getPermittedType());
        staffDriverCredential.setDriversLicenseDateFrom(requestModel.getDriversLicenseDateFrom());
        staffDriverCredential.setDriversLicenseDateTo(requestModel.getDriversLicenseDateTo());

        //与我司关联关系
        TCarrierDriverRelation tCarrierDriverRelationAdd;

        String userName = BaseContextHandler.getUserName();
        Date now = new Date();
        boolean ifOpenAccount = false;
        boolean ifDeleteAccount = false;
        TStaffBasic tqStaffBasic = tqStaffBasicMapper.getByMobile(requestModel.getMobile());
        Long staffId;
        Long carrierStaffRelId;
        boolean hasCarrierRealName = false;//包含车主角色并且已经实名
        //我司id
        Long qiyaCompanyCarrierId = commonBiz.getQiyaCompanyCarrierId();
        if (requestModel.getCarrierDriverId() != null && requestModel.getCarrierDriverId() > CommonConstant.LONG_ZERO) {
            /*人员信息修改逻辑*/
            if (tqStaffBasic == null || IfValidEnum.INVALID.getKey().equals(tqStaffBasic.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
            }
            //如果司机已经实名,禁止修改姓名和身份证号
            if (RealNameAuthenticationStatusEnum.REAL_NAME.getKey().equals(tqStaffBasic.getRealNameAuthenticationStatus())) {
                if (!tqStaffBasic.getName().equals(requestModel.getName())
                        || !tqStaffBasic.getIdentityNumber().equals(requestModel.getIdentityNumber())
                        || !tqStaffBasic.getMobile().equals(requestModel.getMobile())) {
                    throw new BizException(CarrierDataExceptionEnum.STAFF_ALREADY_REAL_NAME_AUTH);
                }
            }
            //查询车主人员关联关系
            TCarrierDriverRelation tCarrierDriverRelation = tCarrierDriverRelationMapper.selectByPrimaryKey(requestModel.getCarrierDriverId());
            if (tCarrierDriverRelation == null || IfValidEnum.INVALID.getKey().equals(tCarrierDriverRelation.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
            }
            carrierStaffRelId = tCarrierDriverRelation.getId();

            //手机号重复
            if (!tqStaffBasic.getId().equals(tCarrierDriverRelation.getDriverId())) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_EXIST);
            }
            staffId = tCarrierDriverRelation.getDriverId();

            //查询车主信息
            FuzzySearchCompanyCarrierResponseModel companyCarrierInfo = tCompanyCarrierMapper.getCompanyCarrierInfoById(tCarrierDriverRelation.getCompanyCarrierId());

            //修改我司数据还是其他车主数据
            if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(companyCarrierInfo.getIsOurCompany())) {
                /*我司*/

                //外部修改为自主/自营
                if (StaffPropertyEnum.EXTERNAL_STAFF.getKey().equals(tqStaffBasic.getStaffProperty())
                        && !StaffPropertyEnum.EXTERNAL_STAFF.getKey().equals(requestModel.getStaffProperty())) {
                    //查询当前司机是否关联其他车主
                    List<TCarrierDriverRelation> tCarrierDriverRelations = tCarrierDriverRelationMapper.queryByDriverIds(Collections.singletonList(tCarrierDriverRelation.getDriverId()));
                    if (tCarrierDriverRelations.size() > CommonConstant.INTEGER_ONE) {
                        throw new BizException(CarrierDataExceptionEnum.ONLY_OUR_COMPANY_EDIT_STAFF_PROPERTY);
                    }
                }
            } else {
                /*其他车主*/
                if (!tqStaffBasic.getStaffProperty().equals(requestModel.getStaffProperty())) {
                    throw new BizException(CarrierDataExceptionEnum.ONLY_OUR_COMPANY_EDIT_STAFF_PROPERTY);
                }
            }

            //校验人员机构是否更改
            List<TStaffVehicleRelationModel> relList = tStaffVehicleRelationMapper.getRelByStaffId(staffId);
            if (ListUtils.isNotEmpty(relList)) {
                TStaffVehicleRelationModel onlyModel = relList.stream().filter(o -> !requestModel.getStaffProperty().equals(o.getVehicleProperty())).findFirst().orElse(null);
                if (onlyModel != null) {
                    throw new BizException(CarrierDataExceptionEnum.STAFF_PROPERTY_CONFLICT);
                }
            }

            TStaffBasic tStaffBasic = tqStaffBasicMapper.selectByPrimaryKey(tCarrierDriverRelation.getDriverId());
            if(tStaffBasic==null){
                throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
            }

            staffBasic.setId(tStaffBasic.getId());
            staffBasic.setOpenStatus(tStaffBasic.getOpenStatus());
            commonBiz.setBaseEntityModify(staffBasic, userName);
            tqStaffBasicMapper.updateByPrimaryKeySelective(staffBasic);

            staffDriverCredential.setId(requestModel.getStaffDriverCredentialId());
            commonBiz.setBaseEntityModify(staffDriverCredential, userName);

            if (StaffTypeEnum.SUPERCARGO.getKey().equals(tStaffBasic.getType()) || StaffTypeEnum.DEFAULT.getKey().equals(tStaffBasic.getType())){//原来是押运员或无类型
                if (StaffTypeEnum.DRIVER.getKey().equals(requestModel.getType()) || StaffTypeEnum.DRIVER_SUPERCARGO.getKey().equals(requestModel.getType())){//修改为司机或司机&押运员，则为手机号开通登录账号
                    ifOpenAccount = true;
                }
            }else if (StaffTypeEnum.DRIVER.getKey().equals(tStaffBasic.getType()) || StaffTypeEnum.DRIVER_SUPERCARGO.getKey().equals(tStaffBasic.getType())){//原来是司机或司机&押运员
                if (StaffTypeEnum.SUPERCARGO.getKey().equals(requestModel.getType())){//修改为押运员后，需删除账号
                    ifDeleteAccount = true;
                }else {//类型没变
                    if (!tStaffBasic.getName().equals(requestModel.getName())) {//修改了名字，同步修改账号名字
                        ifOpenAccount = true;
                    }
                    if (!tStaffBasic.getMobile().equals(requestModel.getMobile())) {//手机号改了，删除原手机号登录账号，为新手机号开通登录账号
                        ifDeleteAccount = true;
                        ifOpenAccount = true;
                    }
                }
            }

            //处理司机证件图片信息
            List<TCertificationPictures> upList = new ArrayList<>();
            processStaffCertificationPic(requestModel, tStaffBasic, upList);

            //更新司机证件
            staffDriverCredentialMapper.updateByPrimaryKeySelective(staffDriverCredential);
            //更新证件文件信息
            if (ListUtils.isNotEmpty(upList)) {
                tqCertificationPicturesMapper.batchUpdate(upList);
            }
        } else {
            /*人员信息新增逻辑*/
            if (tqStaffBasic != null) {
                staffId = tqStaffBasic.getId();
                //查询已存在的人员信息和车主关联关系
                List<TCarrierDriverRelation> tCarrierDriverRelationList = tCarrierDriverRelationMapper.queryByDriverIds(Collections.singletonList(tqStaffBasic.getId()));
                //只有一条关联关系
                if (tCarrierDriverRelationList.size() == CommonConstant.INTEGER_ONE) {
                    TCarrierDriverRelation tCarrierDriverRelation = tCarrierDriverRelationList.get(CommonConstant.INTEGER_ZERO);
                    if (qiyaCompanyCarrierId.equals(tCarrierDriverRelation.getCompanyCarrierId())) {
                        /*已经和我司关联*/
                        throw new BizException(CarrierDataExceptionEnum.STAFF_EXIST);
                    } else {
                        /*增加自主自营的人员,判断司机是否关联其他车主*/
                        if (!StaffPropertyEnum.EXTERNAL_STAFF.getKey().equals(requestModel.getStaffProperty())) {
                            throw new BizException(CarrierDataExceptionEnum.ONLY_OUR_COMPANY_EDIT_STAFF_PROPERTY);
                        }
                    }
                }

                //有多条关联关系
                if (!StaffPropertyEnum.EXTERNAL_STAFF.getKey().equals(requestModel.getStaffProperty())) {
                    /*增加自主自营的人员,判断司机是否关联其他车主*/
                    List<Long> carrierIdList = tCarrierDriverRelationList.stream().map(TCarrierDriverRelation::getCompanyCarrierId).distinct().collect(Collectors.toList());
                    if (carrierIdList.size() > CommonConstant.INTEGER_ONE) {
                        throw new BizException(CarrierDataExceptionEnum.ONLY_OUR_COMPANY_EDIT_STAFF_PROPERTY);
                    }
                } else {
                    //判断和我司是否存在关系
                    for (TCarrierDriverRelation tCarrierDriverRelation : tCarrierDriverRelationList) {
                        if (qiyaCompanyCarrierId.equals(tCarrierDriverRelation.getCompanyCarrierId())) {
                            /*已经和我司关联*/
                            throw new BizException(CarrierDataExceptionEnum.STAFF_EXIST);
                        }
                    }
                }
                //根据手机号查询实名信息,如果新增的司机和已存在的实名信息不一致则不允许新增
                TRealNameAuthentication tRealNameAuthentication = tRealNameAuthenticationMapper.selectRealNameAuthenticationByMobile(requestModel.getMobile());
                if (tRealNameAuthentication != null && CommonConstant.INTEGER_TWO.equals(tRealNameAuthentication.getCertificationStatus())) {
                    if (!tRealNameAuthentication.getName().equals(requestModel.getName())
                            || !tRealNameAuthentication.getIdentityNumber().equals(requestModel.getIdentityNumber())) {
                        throw new BizException(CarrierDataExceptionEnum.STAFF_ALREADY_REAL_NAME_AUTH);
                    }
                }

                if (StaffTypeEnum.SUPERCARGO.getKey().equals(tqStaffBasic.getType()) || StaffTypeEnum.DEFAULT.getKey().equals(tqStaffBasic.getType())) {//原来是押运员或无类型
                    if (StaffTypeEnum.DRIVER.getKey().equals(requestModel.getType()) || StaffTypeEnum.DRIVER_SUPERCARGO.getKey().equals(requestModel.getType())) {//修改为司机或司机&押运员，则为手机号开通登录账号
                        ifOpenAccount = true;
                    }
                } else if (StaffTypeEnum.DRIVER.getKey().equals(tqStaffBasic.getType()) || StaffTypeEnum.DRIVER_SUPERCARGO.getKey().equals(tqStaffBasic.getType())) {//原来是司机或司机&押运员
                    if (StaffTypeEnum.SUPERCARGO.getKey().equals(requestModel.getType())) {//修改为押运员后，需删除账号
                        ifDeleteAccount = true;
                    } else {//类型没变
                        if (!tqStaffBasic.getName().equals(requestModel.getName())) {//修改了名字，同步修改账号名字
                            ifOpenAccount = true;
                        }
                        if (!tqStaffBasic.getMobile().equals(requestModel.getMobile())) {//手机号改了，删除原手机号登录账号，为新手机号开通登录账号
                            ifDeleteAccount = true;
                            ifOpenAccount = true;
                        }
                    }
                }

                //更新司机基本信息
                staffBasic.setId(tqStaffBasic.getId());
                staffBasic.setOpenStatus(tqStaffBasic.getOpenStatus());
                commonBiz.setBaseEntityModify(staffBasic, userName);
                tqStaffBasicMapper.updateByPrimaryKeySelective(staffBasic);

                //查询是否存在司机凭证记录
                TStaffDriverCredential dbStaffDriverCredential = staffDriverCredentialMapper.getDriverCredentialByStaffId(staffId);
                if (dbStaffDriverCredential != null) {
                    //更新司机证件
                    staffDriverCredential.setId(dbStaffDriverCredential.getId());
                    commonBiz.setBaseEntityModify(staffDriverCredential, userName);
                    staffDriverCredentialMapper.updateByPrimaryKeySelective(staffDriverCredential);
                } else {
                    //新增司机证件
                    commonBiz.setBaseEntityAdd(staffDriverCredential, userName);
                    staffDriverCredentialMapper.insertSelective(staffDriverCredential);
                }

                //处理司机证件图片信息
                List<TCertificationPictures> upList = new ArrayList<>();
                processStaffCertificationPic(requestModel, tqStaffBasic, upList);

                //更新证件文件信息
                if (ListUtils.isNotEmpty(upList)) {
                    tqCertificationPicturesMapper.batchUpdate(upList);
                }
            } else {
                /*司机不存在*/
                //新增人员基本信息
                staffBasic.setMobile(requestModel.getMobile());
                staffBasic.setSource(CommonConstant.INTEGER_ONE);
                staffBasic.setOpenStatus(StaffBasicOpenStatusEnum.OPEN.getKey());

                //查询当前新增的司机是否存在已经实名的车主
                FuzzySearchCompanyCarrierResponseModel personCarrierInfo = tCompanyCarrierMapper.selectPersonCarrierByMobile(requestModel.getMobile());
                if (personCarrierInfo != null) {
                    if (RealNameAuthenticationStatusEnum.REAL_NAME.getKey().equals(personCarrierInfo.getRealNameAuthenticationStatus())) {
                        //存在已实名车主,使用实名后的信息
                        staffBasic.setName(personCarrierInfo.getContactName());
                        staffBasic.setIdentityNumber(personCarrierInfo.getIdentityNumber());
                        staffBasic.setRealNameAuthenticationStatus(RealNameAuthenticationStatusEnum.REAL_NAME.getKey());

                        //修改身份证证件图片
                        for (AddOrModifyStaffRequestModel addOrModifyStaffRequestModel : requestModel.getTicketsList()) {
                            if (StaffDriverCertificateEnum.STAFF_IDENTITY_FRONT.getKey().equals(addOrModifyStaffRequestModel.getFileType())) {
                                hasCarrierRealName = true;
                                //身份证人面
                                addOrModifyStaffRequestModel.setFilePath(personCarrierInfo.getIdentityFaceFile());
                            } else if (StaffDriverCertificateEnum.STAFF_IDENTITY_BACK.getKey().equals(addOrModifyStaffRequestModel.getFileType())) {
                                hasCarrierRealName = true;
                                //身份证国徽面
                                addOrModifyStaffRequestModel.setFilePath(personCarrierInfo.getIdentityNationalFile());
                            }
                        }
                    }
                }

            commonBiz.setBaseEntityAdd(staffBasic, userName);
            tqStaffBasicMapper.insertSelective(staffBasic);
            staffId = staffBasic.getId();

            //新增司机证件信息
            staffDriverCredential.setStaffId(staffId);
            commonBiz.setBaseEntityAdd(staffDriverCredential, userName);
            staffDriverCredentialMapper.insertSelective(staffDriverCredential);
        }

            //新增账号
            if (StaffTypeEnum.DRIVER.getKey().equals(requestModel.getType()) || StaffTypeEnum.DRIVER_SUPERCARGO.getKey().equals(requestModel.getType())) {
                ifOpenAccount = true;
            }

            //新增我司与人员的关联关系
            tCarrierDriverRelationAdd = new TCarrierDriverRelation();
            tCarrierDriverRelationAdd.setDriverId(staffId);
            tCarrierDriverRelationAdd.setCompanyCarrierId(qiyaCompanyCarrierId);
            commonBiz.setBaseEntityAdd(tCarrierDriverRelationAdd, BaseContextHandler.getUserName());
            tCarrierDriverRelationMapper.insertSelective(tCarrierDriverRelationAdd);
            carrierStaffRelId = tCarrierDriverRelationAdd.getId();
        }

        AddOrModifyStaffRequestModel driverLicenseModel = new AddOrModifyStaffRequestModel();
        AddOrModifyStaffRequestModel qualificationModel = new AddOrModifyStaffRequestModel();
        //新增凭证
        if (ListUtils.isNotEmpty(requestModel.getTicketsList())) {
            TCertificationPictures certificationPictures;
            List<TCertificationPictures> addList = new ArrayList<>();
            CertificationPicturesFileTypeEnum fileTypeEnum = null;
            String filePath;
            for (AddOrModifyStaffRequestModel ticket : requestModel.getTicketsList()) {
                if (StringUtils.isBlank(ticket.getFilePath())) {
                    continue;
                }
                filePath = ticket.getFilePath();
                //包含车主角色并且已经实名不需要重新拷贝图片
                if (ticket.getFileType().equals(StaffDriverCertificateEnum.STAFF_IDENTITY_FRONT.getKey()) || ticket.getFileType().equals(StaffDriverCertificateEnum.STAFF_IDENTITY_BACK.getKey())) {
                    //身份证图片, 如果含有已实名车主则不用重新拷贝图片
                    if (!hasCarrierRealName) {
                        filePath = commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.STAFF_CERTIFICATE.getKey(), "", ticket.getFilePath(), null);
                    }
                } else {
                    //除了身份证外的图片正常拷贝
                    filePath = commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.STAFF_CERTIFICATE.getKey(), "", ticket.getFilePath(), null);
                }
                if (ticket.getFileType().equals(StaffDriverCertificateEnum.STAFF_IDENTITY_FRONT.getKey())) {
                    fileTypeEnum = CertificationPicturesFileTypeEnum.STAFF_IDENTITY_FRONT;
                } else if (ticket.getFileType().equals(StaffDriverCertificateEnum.STAFF_IDENTITY_BACK.getKey())) {
                    fileTypeEnum = CertificationPicturesFileTypeEnum.STAFF_IDENTITY_BACK;
                } else if (ticket.getFileType().equals(StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_FRONT.getKey())) {
                    fileTypeEnum = CertificationPicturesFileTypeEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_FRONT;
                    qualificationModel = ticket;
                } else if (ticket.getFileType().equals(StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_BACK.getKey())) {
                    fileTypeEnum = CertificationPicturesFileTypeEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_BACK;
                    if (qualificationModel == null || StringUtils.isBlank(qualificationModel.getFilePath())) {
                        qualificationModel = ticket;
                    }
                } else if (ticket.getFileType().equals(StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_FRONT.getKey())) {
                    fileTypeEnum = CertificationPicturesFileTypeEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_FRONT;
                    if (qualificationModel == null || StringUtils.isBlank(qualificationModel.getFilePath())) {
                        qualificationModel = ticket;
                    }
                } else if (ticket.getFileType().equals(StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_BACK.getKey())) {
                    fileTypeEnum = CertificationPicturesFileTypeEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_BACK;
                    if (qualificationModel == null || StringUtils.isBlank(qualificationModel.getFilePath())) {
                        qualificationModel = ticket;
                    }
                } else if (ticket.getFileType().equals(StaffDriverCertificateEnum.DRIVER_LICENSE_FRONT.getKey())) {
                    fileTypeEnum = CertificationPicturesFileTypeEnum.DRIVER_LICENSE_FRONT;
                    if (driverLicenseModel == null || StringUtils.isBlank(driverLicenseModel.getFilePath())) {
                        driverLicenseModel = ticket;
                    }
                }
                if (fileTypeEnum == null) {
                    continue;
                }
                certificationPictures = new TCertificationPictures();
                certificationPictures.setObjectType(fileTypeEnum.getObjectType().getObjectType());
                if (fileTypeEnum.getObjectType() == CertificationPicturesObjectTypeEnum.T_STAFF_BASIC) {
                    certificationPictures.setObjectId(staffId);
                } else if (fileTypeEnum.getObjectType() == CertificationPicturesObjectTypeEnum.T_STAFF_DRIVER_CREDENTIAL) {
                    certificationPictures.setObjectId(staffDriverCredential.getId());
                }
                certificationPictures.setFileType(fileTypeEnum.getFileType());
                certificationPictures.setFileTypeName(fileTypeEnum.getFileName());
                certificationPictures.setFileName(fileTypeEnum.getFileName());
                certificationPictures.setFilePath(filePath);
                certificationPictures.setUploadUserName(userName);
                certificationPictures.setUploadTime(now);
                certificationPictures.setSuffix(ticket.getFilePath().substring(ticket.getFilePath().lastIndexOf('.')));
                commonBiz.setBaseEntityAdd(certificationPictures, userName);
                addList.add(certificationPictures);
            }
            if (ListUtils.isNotEmpty(addList)) {
                tqCertificationPicturesMapper.batchInsert(addList);
            }
        }
        continueLearningAddOrModify(requestModel.getContinueLearningList(), staffId);
        occupationalAddOrModify(requestModel.getOccupationalList(), staffId);
        integrityExaminationAddOrModify(requestModel.getIntegrityExaminationList(), staffId);

        //对司机登录账号进行处理
        if (ifDeleteAccount) {
            OpenAccountRequestModel openAccountRequestModel = new OpenAccountRequestModel();
            openAccountRequestModel.setType(CommonConstant.INTEGER_THREE);
            openAccountRequestModel.setUserId(carrierStaffRelId);
            openAccountRequestModel.setUserRole(AccountUserRoleTypeEnum.DRIVER_APPLET.getKey());
            customerAccountBiz.openOrCloseAccount(openAccountRequestModel);
        }
        //如果身份是司机或则司机&&押运员，则需要给司机开通账号
        if (ifOpenAccount) {
            OpenAccountRequestModel openAccountRequestModel = new OpenAccountRequestModel();
            openAccountRequestModel.setType(CommonConstant.INTEGER_ONE);
            openAccountRequestModel.setMobile(requestModel.getMobile());
            openAccountRequestModel.setUserId(carrierStaffRelId);
            openAccountRequestModel.setUserRole(AccountUserRoleTypeEnum.DRIVER_APPLET.getKey());
            openAccountRequestModel.setUserName(requestModel.getName());
            if (StaffBasicOpenStatusEnum.CLOSE.getKey().equals(staffBasic.getOpenStatus())) {
                openAccountRequestModel.setIfClose(CommonConstant.INTEGER_ONE);
            }
            customerAccountBiz.openOrCloseAccount(openAccountRequestModel);
        }
        return staffBasic.getId();
    }

    private void processStaffCertificationPic(AddOrModifyStaffTicketsRequestModel requestModel, TStaffBasic tStaffBasic, List<TCertificationPictures> upList) {
        TCertificationPictures certificationPictures;
        String driveLicense = "";
        String qualification = "";
        String objectIds = tStaffBasic.getId() + "," + requestModel.getStaffDriverCredentialId();
        String objectTypes = CertificationPicturesObjectTypeEnum.T_STAFF_BASIC.getObjectType() + "," + CertificationPicturesObjectTypeEnum.T_STAFF_DRIVER_CREDENTIAL.getObjectType();
        List<TCertificationPictures> staffDriverTicketsList = tqCertificationPicturesMapper.getTPicsByIds(objectIds, objectTypes);
        List<String> ticketPathList = staffDriverTicketsList.stream().map(TCertificationPictures::getFilePath).collect(Collectors.toList());
        List<Long> ticketIdList = staffDriverTicketsList.stream().map(TCertificationPictures::getId).collect(Collectors.toList());
        List<Long> idList = new ArrayList<>();
        String filePath = "";
        if (ListUtils.isEmpty(requestModel.getTicketsList())) {
            requestModel.setTicketsList(new ArrayList<>());
        }
        for (Iterator<AddOrModifyStaffRequestModel> iterator = requestModel.getTicketsList().iterator(); iterator.hasNext(); ) {
            AddOrModifyStaffRequestModel ticket = iterator.next();
            if (ticket.getTicketId() != null
                    && StringUtils.isNotBlank(ticket.getFilePath())
                    && ticket.getTicketId() > CommonConstant.LONG_ZERO) {
                if (!ticketPathList.contains(ticket.getFilePath())) {

                    //司机已经实名不允许修改身份证图片信息
                    if (RealNameAuthenticationStatusEnum.REAL_NAME.getKey().equals(tStaffBasic.getRealNameAuthenticationStatus())) {
                        if (ticket.getFileType().equals(CertificationPicturesFileTypeEnum.STAFF_IDENTITY_BACK.getFileType())
                                || ticket.getFileType().equals(CertificationPicturesFileTypeEnum.STAFF_IDENTITY_FRONT.getFileType())) {
                            continue;
                        }
                    }
                    certificationPictures = new TCertificationPictures();
                    certificationPictures.setId(ticket.getTicketId());
                    filePath = commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.STAFF_CERTIFICATE.getKey(), "", ticket.getFilePath(), null);
                    certificationPictures.setFilePath(filePath);
                    commonBiz.setBaseEntityModify(certificationPictures, BaseContextHandler.getUserName());
                    upList.add(certificationPictures);
                }
                idList.add(ticket.getTicketId());
                iterator.remove();
                if (ticket.getFileType().equals(StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_FRONT.getKey())) {
                    qualification = filePath;
                } else if (ticket.getFileType().equals(StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_BACK.getKey())
                        || ticket.getFileType().equals(StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_FRONT.getKey())
                        || ticket.getFileType().equals(StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_BACK.getKey())) {
                    if (StringUtils.isBlank(qualification)) {
                        qualification = filePath;
                    }
                } else if (ticket.getFileType().equals(StaffDriverCertificateEnum.DRIVER_LICENSE_FRONT.getKey()) && StringUtils.isBlank(driveLicense)) {
                    driveLicense = filePath;
                }
            }
        }
        if (ListUtils.isNotEmpty(idList)) {
            ticketIdList.removeAll(idList);
        }
        if (ListUtils.isNotEmpty(ticketIdList)) {
            for (Long id : ticketIdList) {
                certificationPictures = new TCertificationPictures();
                certificationPictures.setId(id);
                certificationPictures.setValid(CommonConstant.INTEGER_ZERO);
                commonBiz.setBaseEntityModify(certificationPictures, BaseContextHandler.getUserName());
                upList.add(certificationPictures);
            }
        }
    }

    /**
     * 外部人员信息新增/修改
     *
     * @param requestModel
     */
    @Transactional
    @DistributedLock(prefix = CommonConstant.TMS_ADD_STAFF_LOCK,
            keys = "#requestModel.mobile",
            waitTime = 3)
    public void addOrModifyExternalStaff(AddOrModifyExternalStaffRequestModel requestModel) {
        // 校验车主是否存在
        Long companyCarrierId = requestModel.getCompanyCarrierId();
        TCompanyCarrier companyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(companyCarrierId);
        if (companyCarrier == null || IfValidEnum.INVALID.getKey().equals(companyCarrier.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }

        //只有其他车主才能新增
        if (!IsOurCompanyEnum.OTHER_COMPANY.getKey().equals(companyCarrier.getLevel())) {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }

        //车主授权或实名后才能新增
        if (CompanyTypeEnum.COMPANY.getKey().equals(companyCarrier.getType())) {
            //企业类型判断是否授权
            if (!CompanyCarrierAuthAuditStatusEnum.BE_AUTH.getKey().equals(companyCarrier.getAuthorizationStatus())) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_NOT_AUTH);
            }
        } else {
            //个人车主判断是否实名
            if (!RealNameAuthenticationStatusEnum.REAL_NAME.getKey().equals(companyCarrier.getRealNameAuthenticationStatus())) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_NOT_AUTH);
            }
        }

        // 车主司机关联id
        Long carrierDriverId = requestModel.getCarrierDriverId();
        TStaffBasic tStaffBasicUp = null;
        //判断是新增还是编辑
        if (carrierDriverId == null) { //新增
            Long relId;
            //根据手机号查询人员资产
            TStaffBasic tStaffBasic = tqStaffBasicMapper.getStaffByMobile(requestModel.getMobile());
            //人员信息存在
            if (tStaffBasic != null && IfValidEnum.VALID.getKey().equals(tStaffBasic.getValid())) {
                //判断人员的机构是内部(自主,自营)还是外部
                //外部
                if (StaffPropertyEnum.EXTERNAL_STAFF.getKey().equals(tStaffBasic.getStaffProperty())) {
                    //查询当前车主和司机的关联关系
                    TCarrierDriverRelation carrierDriverRelation = tCarrierDriverRelationMapper.getByCompanyCarrierIdAndDriverId(companyCarrierId, tStaffBasic.getId(), null);
                    //不存在关联关系则新增关联关系和操作记录
                    if (carrierDriverRelation == null) {
                        //更新人员信息
                        tStaffBasicUp = new TStaffBasic();
                        tStaffBasicUp.setId(tStaffBasic.getId());
                        tStaffBasicUp.setIdentityNumber(requestModel.getIdentityNumber());
                        //添加车主司机关联关系和操作记录
                        relId = addCarrierDriverRelAndLog(tStaffBasic, companyCarrierId);
                    } else {
                        //已存在就提示
                        throw new BizException(CarrierDataExceptionEnum.STAFF_EXIST);
                    }

                    //当前司机姓名是否和输入的一致,并且是驾驶员
                    if (!tStaffBasic.getName().equals(requestModel.getName()) ||
                            !(StaffTypeEnum.DRIVER.getKey().equals(tStaffBasic.getType()) || StaffTypeEnum.DRIVER_SUPERCARGO.getKey().equals(tStaffBasic.getType()))) {
                        throw new BizException(CarrierDataExceptionEnum.BASIC_DATA_ERROR);
                    }
                } else {
                    //不能添加内部人员
                    throw new BizException(CarrierDataExceptionEnum.BASIC_DATA_ERROR);
                }
            } else {
                //人员信息不存在

                TStaffBasic tStaffBasicAdd = new TStaffBasic();
                tStaffBasicAdd.setStaffProperty(StaffPropertyEnum.EXTERNAL_STAFF.getKey());
                tStaffBasicAdd.setType(StaffTypeEnum.DRIVER.getKey());
                tStaffBasicAdd.setMobile(requestModel.getMobile());
                tStaffBasicAdd.setName(requestModel.getName());
                tStaffBasicAdd.setIdentityNumber(requestModel.getIdentityNumber());
                tStaffBasicAdd.setOpenStatus(OpenStatusEnum.OPEN.getKey());

                List<TCertificationPictures> tCertificationPicturesAddList = new ArrayList<>();
                //查询当前新增的司机是否存在已经实名的车主
                FuzzySearchCompanyCarrierResponseModel personCarrierInfo = tCompanyCarrierMapper.selectPersonCarrierByMobile(requestModel.getMobile());
                if (personCarrierInfo != null) {
                    if (RealNameAuthenticationStatusEnum.REAL_NAME.getKey().equals(personCarrierInfo.getRealNameAuthenticationStatus())) {
                        //存在已实名车主,使用实名后的信息
                        tStaffBasicAdd.setName(personCarrierInfo.getContactName());
                        tStaffBasicAdd.setIdentityNumber(personCarrierInfo.getIdentityNumber());
                        tStaffBasicAdd.setRealNameAuthenticationStatus(RealNameAuthenticationStatusEnum.REAL_NAME.getKey());

                        //增加身份证证件
                        if (StringUtils.isNotBlank(personCarrierInfo.getIdentityFaceFile())) {
                            tCertificationPicturesAddList.add(commonBiz.getCertificationPictures(null, CertificationPicturesObjectTypeEnum.T_STAFF_BASIC, CertificationPicturesFileTypeEnum.STAFF_IDENTITY_FRONT, personCarrierInfo.getIdentityFaceFile()));
                        }
                        if (StringUtils.isNotBlank(personCarrierInfo.getIdentityNationalFile())) {
                            tCertificationPicturesAddList.add(commonBiz.getCertificationPictures(null, CertificationPicturesObjectTypeEnum.T_STAFF_BASIC, CertificationPicturesFileTypeEnum.STAFF_IDENTITY_BACK, personCarrierInfo.getIdentityNationalFile()));
                        }
                    }
                }

                //新增人员信息
                commonBiz.setBaseEntityAdd(tStaffBasicAdd, BaseContextHandler.getUserName());
                tqStaffBasicMapper.insertSelective(tStaffBasicAdd);

                if (ListUtils.isNotEmpty(tCertificationPicturesAddList)) {
                    tCertificationPicturesAddList.forEach(tCertificationPictures -> tCertificationPictures.setObjectId(tStaffBasicAdd.getId()));
                    tqCertificationPicturesMapper.batchInsert(tCertificationPicturesAddList);
                }

                //新增司机证件信息
                TStaffDriverCredential tStaffDriverCredential = new TStaffDriverCredential();
                tStaffDriverCredential.setStaffId(tStaffBasicAdd.getId());
                commonBiz.setBaseEntityAdd(tStaffDriverCredential, BaseContextHandler.getUserName());
                staffDriverCredentialMapper.insertSelective(tStaffDriverCredential);

                //添加车主司机关联记录
                relId = addCarrierDriverRelAndLog(tStaffBasicAdd, companyCarrierId);
            }

            if (relId != null) {
                //新增司机账号信息并开通
                OpenAccountRequestModel openAccountRequestModel = new OpenAccountRequestModel();
                openAccountRequestModel.setType(CommonConstant.INTEGER_ONE);
                openAccountRequestModel.setMobile(requestModel.getMobile());
                openAccountRequestModel.setUserId(relId);
                openAccountRequestModel.setUserRole(AccountUserRoleTypeEnum.DRIVER_APPLET.getKey());
                openAccountRequestModel.setUserName(requestModel.getName());
                openAccountRequestModel.setIfClose(CommonConstant.INTEGER_ZERO);
                customerAccountBiz.openOrCloseAccount(openAccountRequestModel);
            }
        } else {
            //编辑时手机号不能修改

            //查询车主司机关联信息
            TCarrierDriverRelation driverRelation = tCarrierDriverRelationMapper.selectByPrimaryKey(requestModel.getCarrierDriverId());
            if (driverRelation == null || IfValidEnum.INVALID.getKey().equals(driverRelation.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
            }

            //查询人员基本信息
            TStaffBasic tStaffBasic = tqStaffBasicMapper.selectByPrimaryKey(driverRelation.getDriverId());
            if (tStaffBasic == null || IfValidEnum.INVALID.getKey().equals(tStaffBasic.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
            }

            //更新人员信息
            tStaffBasicUp = new TStaffBasic();
            tStaffBasicUp.setId(tStaffBasic.getId());
            tStaffBasicUp.setIdentityNumber(requestModel.getIdentityNumber());
        }

        //更新人员信息
        if (tStaffBasicUp != null) {
            commonBiz.setBaseEntityModify(tStaffBasicUp, BaseContextHandler.getUserName());
            tqStaffBasicMapper.updateByPrimaryKeySelective(tStaffBasicUp);
        }
    }


    /**
     * 批量添加车主司机关联关系并记录日志
     *
     * @param tStaffBasic      要添加关联关系的司机集合
     * @param companyCarrierId 车主公司id
     * @return 车主司机关联id
     */
    @Transactional
    public Long addCarrierDriverRelAndLog(TStaffBasic tStaffBasic, Long companyCarrierId) {
        //创建关联记录
        TCarrierDriverRelation tCarrierDriverRelation = new TCarrierDriverRelation();
        tCarrierDriverRelation.setDriverId(tStaffBasic.getId());
        tCarrierDriverRelation.setCompanyCarrierId(companyCarrierId);
        commonBiz.setBaseEntityAdd(tCarrierDriverRelation, BaseContextHandler.getUserName());
        tCarrierDriverRelationMapper.insertSelective(tCarrierDriverRelation);

        //添加操作日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(companyCarrierId, OperateLogsOperateTypeEnum.CARRIER_DRIVER_ADD, tStaffBasic.getName() + " " + tStaffBasic.getMobile(), BaseContextHandler.getUserName());
        tOperateLogsMapper.insertSelective(tOperateLogs);
        return tCarrierDriverRelation.getId();
    }

    /**
     * 查询人员信息详情
     *
     * @param requestModel 车主司机关联id
     * @return 司机详情
     */
    public GetStaffDetailResponseModel getStaffDetail(CarrierStaffIdRequestModel requestModel) {
        // 查询关联是否存在
        TCarrierDriverRelation driverRelation = tCarrierDriverRelationMapper.selectByPrimaryKey(requestModel.getCarrierDriverId());
        if (driverRelation == null || IfValidEnum.INVALID.getKey().equals(driverRelation.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }
        // 查询司机信息
        GetStaffDetailResponseModel staffDetail = tqStaffBasicMapper.getStaffDetail(driverRelation.getDriverId());
        staffDetail.setCarrierDriverId(driverRelation.getId());
        staffDetail.setEnabled(driverRelation.getEnabled());
        // 查询车主信息
        FuzzySearchCompanyCarrierResponseModel companyCarrier = tCompanyCarrierMapper.getCompanyCarrierInfoById(driverRelation.getCompanyCarrierId());
        staffDetail.setCompanyCarrierId(companyCarrier.getCompanyId());
        staffDetail.setCompanyCarrierName(companyCarrier.getCompanyName());
        staffDetail.setCompanyCarrierType(companyCarrier.getCompanyType());
        staffDetail.setCarrierContactName(companyCarrier.getContactName());
        staffDetail.setCarrierContactPhone(companyCarrier.getContactPhone());
        staffDetail.setIsOurCompany(companyCarrier.getIsOurCompany());
        return staffDetail;
    }

    /**
     * 从业资格证列表
     * @param requestModel
     * @return
     */
    public List<OccupationalListResponseModel> occupationalList(StaffIdRequestModel requestModel) {
        List<OccupationalListResponseModel> occupationalListResponseModels = tqStaffDriverOccupationalRecordMapper.selectOccupationalList(requestModel.getStaffId());
        List<Long> objectIdList = occupationalListResponseModels.stream().map(OccupationalListResponseModel::getOccupationalRecordId).distinct().collect(Collectors.toList());
        Map<Long, List<CertificatePictureModel>> longListMap = getMultiImageList(objectIdList, CertificationPicturesFileTypeEnum.OCCUPATIONAL_RECORD);
        occupationalListResponseModels.stream().forEach(tmp ->
                tmp.setImageList(longListMap.get(tmp.getOccupationalRecordId()))
        );
        return occupationalListResponseModels;
    }

    //新增/修改从业资格证
    public void occupationalAddOrModify(List<OccupationalAddOrModifyRequestModel> requestModelList, Long staffId) {
        if (requestModelList == null) {
            requestModelList = new ArrayList<>();
        }
        //删除从业资格证记录
        List<OccupationalListResponseModel> needToDeleteList = tqStaffDriverOccupationalRecordMapper.selectOccupationalList(staffId);
        List<Long> deleteList = needToDeleteList.stream().map(OccupationalListResponseModel::getOccupationalRecordId).collect(Collectors.toList());
        List<TStaffDriverOccupationalRecord> updateRecordList = new ArrayList<>();
        for (OccupationalAddOrModifyRequestModel requestModel : requestModelList) {
            if (requestModel.getOccupationalRecordId() == null || requestModel.getOccupationalRecordId() <= 0) {//新增
                TStaffDriverOccupationalRecord newOccupationalRecord = new TStaffDriverOccupationalRecord();
                newOccupationalRecord.setIssueDate(requestModel.getIssueDate());
                newOccupationalRecord.setStaffId(staffId);
                newOccupationalRecord.setValidDate(requestModel.getValidDate());
                newOccupationalRecord.setRemark(requestModel.getRemark());
                commonBiz.setBaseEntityAdd(newOccupationalRecord, BaseContextHandler.getUserName());
                tqStaffDriverOccupationalRecordMapper.insertSelective(newOccupationalRecord);
                if (ListUtils.isNotEmpty(requestModel.getImagePaths())) {
                    modifyPictures(newOccupationalRecord.getId(), requestModel.getImagePaths(), CertificationPicturesFileTypeEnum.OCCUPATIONAL_RECORD, CopyFileTypeEnum.OCCUPATIONAL_RECORD);
                }
            } else {//修改
                deleteList.remove(requestModel.getOccupationalRecordId());
                TStaffDriverOccupationalRecord upOccupationalRecord = new TStaffDriverOccupationalRecord();
                upOccupationalRecord.setId(requestModel.getOccupationalRecordId());
                upOccupationalRecord.setIssueDate(requestModel.getIssueDate());
                upOccupationalRecord.setStaffId(staffId);
                upOccupationalRecord.setValidDate(requestModel.getValidDate());
                upOccupationalRecord.setRemark(requestModel.getRemark());
                commonBiz.setBaseEntityModify(upOccupationalRecord, BaseContextHandler.getUserName());
                updateRecordList.add(upOccupationalRecord);
                if (ListUtils.isEmpty(requestModel.getImagePaths())) {
                    requestModel.setImagePaths(new ArrayList<>());
                }
                modifyPictures(upOccupationalRecord.getId(), requestModel.getImagePaths(), CertificationPicturesFileTypeEnum.OCCUPATIONAL_RECORD, CopyFileTypeEnum.OCCUPATIONAL_RECORD);
            }
        }
        if (ListUtils.isNotEmpty(deleteList)) {
            for (Long id : deleteList) {
                TStaffDriverOccupationalRecord deleteRecord = new TStaffDriverOccupationalRecord();
                deleteRecord.setId(id);
                deleteRecord.setValid(CommonConstant.INTEGER_ZERO);
                commonBiz.setBaseEntityModify(deleteRecord, BaseContextHandler.getUserName());
                updateRecordList.add(deleteRecord);
            }
        }
        if (ListUtils.isNotEmpty(updateRecordList)) {
            tqStaffDriverOccupationalRecordMapper.batchUpdate(updateRecordList);
        }

    }

    //修改图片
    private void modifyPictures(Long objectId, List<String> imagePaths, CertificationPicturesFileTypeEnum certificationPicturesFileTypeEnum, CopyFileTypeEnum copyFileTypeEnum) {
        List<TCertificationPictures> picturesList = tqCertificationPicturesMapper.getByObjectIdType(objectId, certificationPicturesFileTypeEnum.getObjectType().getObjectType(), certificationPicturesFileTypeEnum.getFileType());
        Map<String, Long> pathMap = new HashMap<>();
        picturesList.stream().forEach(tmp -> pathMap.put(tmp.getFilePath(), tmp.getId()));
        List<TCertificationPictures> addPictureList = new ArrayList<>();
        List<TCertificationPictures> delPictureList = new ArrayList<>();
        Date now = new Date();

        for (String tmp : imagePaths) {
            if (!pathMap.keySet().contains(tmp)) {
                TCertificationPictures newPic = new TCertificationPictures();
                newPic.setObjectType(certificationPicturesFileTypeEnum.getObjectType().getObjectType());
                newPic.setObjectId(objectId);
                newPic.setFileType(certificationPicturesFileTypeEnum.getFileType());
                newPic.setFilePath(commonBiz.copyFileToDirectoryOfType(copyFileTypeEnum.getKey(), "", tmp, null));
                newPic.setUploadUserName(BaseContextHandler.getUserName());
                newPic.setUploadTime(now);
                newPic.setFileTypeName(certificationPicturesFileTypeEnum.getFileName());
                newPic.setFileName(certificationPicturesFileTypeEnum.getFileName());
                newPic.setSuffix(tmp.substring(tmp.indexOf('.')));
                commonBiz.setBaseEntityAdd(newPic, BaseContextHandler.getUserName());
                addPictureList.add(newPic);
            }
        }
        for (Map.Entry<String, Long> entry : pathMap.entrySet()) {
            if (!imagePaths.contains(entry.getKey())) {
                TCertificationPictures upPic = new TCertificationPictures();
                upPic.setId(entry.getValue());
                upPic.setValid(CommonConstant.INTEGER_ZERO);
                commonBiz.setBaseEntityModify(upPic, BaseContextHandler.getUserName());
                delPictureList.add(upPic);
            }
        }
        if (ListUtils.isNotEmpty(addPictureList)) {
            tqCertificationPicturesMapper.batchInsert(addPictureList);
        }
        if (ListUtils.isNotEmpty(delPictureList)) {
            tqCertificationPicturesMapper.batchUpdate(delPictureList);
        }
    }

    /**
     * 继续教育列表
     * @param requestModel
     * @return
     */
    public List<ContinueLearningListResponseModel> continueLearningList(StaffIdRequestModel requestModel) {
        List<ContinueLearningListResponseModel> continueLearningListResponseModels = tqStaffDriverContinueLearningRecordMapper.continueLearningList(requestModel.getStaffId());
        List<Long> objectIdList = continueLearningListResponseModels.stream().map(ContinueLearningListResponseModel::getContinueLearningRecordId).distinct().collect(Collectors.toList());
        Map<Long, List<CertificatePictureModel>> longListMap = getMultiImageList(objectIdList, CertificationPicturesFileTypeEnum.CONTINUE_LEARNING_RECORD);
        continueLearningListResponseModels.stream().forEach(tmp ->
                tmp.setImageList(longListMap.get(tmp.getContinueLearningRecordId()))
        );
        return continueLearningListResponseModels;
    }

    //新增/修改继续教育
    public void continueLearningAddOrModify(List<ContinueLearningAddOrModifyRequestModel> requestModelList, Long staffId) {
        if (requestModelList == null) {
            requestModelList = new ArrayList<>();
        }
        //删除继续教育记录
        List<ContinueLearningListResponseModel> needToDeleteList = tqStaffDriverContinueLearningRecordMapper.continueLearningList(staffId);
        List<Long> deleteList = needToDeleteList.stream().map(ContinueLearningListResponseModel::getContinueLearningRecordId).collect(Collectors.toList());
        List<TStaffDriverContinueLearningRecord> updateRecordList = new ArrayList<>();

        for (ContinueLearningAddOrModifyRequestModel requestModel : requestModelList) {
            if (requestModel.getContinueLearningRecordId() == null || requestModel.getContinueLearningRecordId() <= 0) {//新增
                TStaffDriverContinueLearningRecord continueLearningRecord = new TStaffDriverContinueLearningRecord();
                continueLearningRecord.setStaffId(staffId);
                continueLearningRecord.setValidDate(requestModel.getValidDate());
                continueLearningRecord.setRemark(requestModel.getRemark());
                commonBiz.setBaseEntityAdd(continueLearningRecord, BaseContextHandler.getUserName());
                tqStaffDriverContinueLearningRecordMapper.insertSelective(continueLearningRecord);
                if (ListUtils.isNotEmpty(requestModel.getImagePaths())) {
                    modifyPictures(continueLearningRecord.getId(), requestModel.getImagePaths(), CertificationPicturesFileTypeEnum.CONTINUE_LEARNING_RECORD, CopyFileTypeEnum.CONTINUE_LEARNING_RECORD);
                }
            } else {//修改
                deleteList.remove(requestModel.getContinueLearningRecordId());
                TStaffDriverContinueLearningRecord upContinueLearningRecord = new TStaffDriverContinueLearningRecord();
                upContinueLearningRecord.setId(requestModel.getContinueLearningRecordId());
                upContinueLearningRecord.setStaffId(staffId);
                upContinueLearningRecord.setValidDate(requestModel.getValidDate());
                upContinueLearningRecord.setRemark(requestModel.getRemark());
                commonBiz.setBaseEntityModify(upContinueLearningRecord, BaseContextHandler.getUserName());
                updateRecordList.add(upContinueLearningRecord);
                if (ListUtils.isEmpty(requestModel.getImagePaths())) {
                    requestModel.setImagePaths(new ArrayList<>());
                }
                modifyPictures(upContinueLearningRecord.getId(), requestModel.getImagePaths(), CertificationPicturesFileTypeEnum.CONTINUE_LEARNING_RECORD, CopyFileTypeEnum.CONTINUE_LEARNING_RECORD);
            }
        }
        if (ListUtils.isNotEmpty(deleteList)) {
            for (Long id : deleteList) {
                TStaffDriverContinueLearningRecord deleteRecord = new TStaffDriverContinueLearningRecord();
                deleteRecord.setId(id);
                deleteRecord.setValid(CommonConstant.INTEGER_ZERO);
                commonBiz.setBaseEntityModify(deleteRecord, BaseContextHandler.getUserName());
                updateRecordList.add(deleteRecord);
            }
        }
        if (ListUtils.isNotEmpty(updateRecordList)) {
            tqStaffDriverContinueLearningRecordMapper.batchUpdate(updateRecordList);
        }
    }

    /**
     * 诚信考核列表
     * @param requestModel
     * @return
     */
    public List<IntegrityExaminationListResponseModel> integrityExaminationList(StaffIdRequestModel requestModel) {
        List<IntegrityExaminationListResponseModel> integrityExaminationListResponseModels = tqStaffDriverIntegrityExaminationRecordMapper.integrityExaminationList(requestModel.getStaffId());
        List<Long> objectIdList = integrityExaminationListResponseModels.stream().map(IntegrityExaminationListResponseModel::getIntegrityExaminationRecordId).distinct().collect(Collectors.toList());
        Map<Long, List<CertificatePictureModel>> longListMap = getMultiImageList(objectIdList, CertificationPicturesFileTypeEnum.INTEGRITY_EXAMINATION_RECORD);
        integrityExaminationListResponseModels.stream().forEach(tmp ->
                tmp.setImageList(longListMap.get(tmp.getIntegrityExaminationRecordId()))
        );
        return integrityExaminationListResponseModels;
    }

    //新增/修改诚信考核
    public void integrityExaminationAddOrModify(List<IntegrityExaminationAddOrModifyRequestModel> integrityExaminationAddOrModifyRequestModels, Long staffId) {
        if (integrityExaminationAddOrModifyRequestModels == null) {
            integrityExaminationAddOrModifyRequestModels = new ArrayList<>();
        }
        //删除诚信考试记录
        List<IntegrityExaminationListResponseModel> needToDeleteList = tqStaffDriverIntegrityExaminationRecordMapper.integrityExaminationList(staffId);
        List<Long> deleteList = needToDeleteList.stream().map(IntegrityExaminationListResponseModel::getIntegrityExaminationRecordId).collect(Collectors.toList());
        List<TStaffDriverIntegrityExaminationRecord> updateRecordList = new ArrayList<>();
        for (IntegrityExaminationAddOrModifyRequestModel requestModel : integrityExaminationAddOrModifyRequestModels) {
            if (requestModel.getIntegrityExaminationRecordId() == null || requestModel.getIntegrityExaminationRecordId() <= 0) {//新增
                TStaffDriverIntegrityExaminationRecord newExaminationRecord = new TStaffDriverIntegrityExaminationRecord();
                newExaminationRecord.setStaffId(staffId);
                newExaminationRecord.setValidDate(commonBiz.getLastDayOfMonth(requestModel.getValidDate()));
                newExaminationRecord.setRemark(requestModel.getRemark());
                commonBiz.setBaseEntityAdd(newExaminationRecord, BaseContextHandler.getUserName());
                tqStaffDriverIntegrityExaminationRecordMapper.insertSelective(newExaminationRecord);
                if (ListUtils.isNotEmpty(requestModel.getImagePaths())) {
                    modifyPictures(newExaminationRecord.getId(), requestModel.getImagePaths(), CertificationPicturesFileTypeEnum.INTEGRITY_EXAMINATION_RECORD, CopyFileTypeEnum.INTEGRITY_EXAMINATION_RECORD);
                }
            } else {//修改
                deleteList.remove(requestModel.getIntegrityExaminationRecordId());
                TStaffDriverIntegrityExaminationRecord upExaminationRecord = new TStaffDriverIntegrityExaminationRecord();
                upExaminationRecord.setId(requestModel.getIntegrityExaminationRecordId());
                upExaminationRecord.setStaffId(staffId);
                upExaminationRecord.setValidDate(commonBiz.getLastDayOfMonth(requestModel.getValidDate()));
                upExaminationRecord.setRemark(requestModel.getRemark());
                commonBiz.setBaseEntityModify(upExaminationRecord, BaseContextHandler.getUserName());
                updateRecordList.add(upExaminationRecord);
                if (ListUtils.isEmpty(requestModel.getImagePaths())) {
                    requestModel.setImagePaths(new ArrayList<>());
                }
                modifyPictures(upExaminationRecord.getId(), requestModel.getImagePaths(), CertificationPicturesFileTypeEnum.INTEGRITY_EXAMINATION_RECORD, CopyFileTypeEnum.INTEGRITY_EXAMINATION_RECORD);
            }
        }
        if (ListUtils.isNotEmpty(deleteList)) {
            for (Long upExaminationRecord : deleteList) {
                TStaffDriverIntegrityExaminationRecord deleteRecord = new TStaffDriverIntegrityExaminationRecord();
                deleteRecord.setId(upExaminationRecord);
                deleteRecord.setValid(CommonConstant.INTEGER_ZERO);
                commonBiz.setBaseEntityModify(deleteRecord, BaseContextHandler.getUserName());
                updateRecordList.add(deleteRecord);
            }
        }
        if (ListUtils.isNotEmpty(updateRecordList)) {
            tqStaffDriverIntegrityExaminationRecordMapper.batchUpdate(updateRecordList);
        }
    }

    //将图片进行分类
    private Map<Long, List<CertificatePictureModel>> getMultiImageList(List<Long> objectIds, CertificationPicturesFileTypeEnum certificationPicturesObjectTypeEnum) {
        if (ListUtils.isEmpty(objectIds)) {
            return new HashMap<>();
        }
        Map<Long, List<CertificatePictureModel>> longListMap = new HashMap<>();
        for (Long objectId : objectIds) {
            longListMap.put(objectId, new ArrayList<>());
        }
        List<CertificatePictureModel> certificatePictureModels = tqCertificationPicturesMapper.getPicsByIdsAndType(StringUtils.listToString(objectIds, ','), certificationPicturesObjectTypeEnum.getObjectType().getObjectType(), certificationPicturesObjectTypeEnum.getFileType());
        for (CertificatePictureModel certificatePictureModel : certificatePictureModels) {
            List<CertificatePictureModel> certificatePictureModels1 = longListMap.get(certificatePictureModel.getObjectId());
            if (!(certificatePictureModels1 instanceof ArrayList)) {
                longListMap.put(certificatePictureModel.getObjectId(), new ArrayList());
            }
            longListMap.get(certificatePictureModel.getObjectId()).add(certificatePictureModel);
        }
        return longListMap;
    }

    /**
     * 导入
     * @param importStaffRequestModel
     * @return
     */
    public ImportStaffResponseModel importStaff(ImportStaffRequestModel importStaffRequestModel) {
        ImportStaffResponseModel importStaffResponseModel = new ImportStaffResponseModel();
        if (importStaffRequestModel == null) {
            return importStaffResponseModel;
        }
        importStaffResponseModel.setFailuresNumber(importStaffRequestModel.getFailureCount());
        if (ListUtils.isEmpty(importStaffRequestModel.getImportList())) {
            return importStaffResponseModel;

        }
        //我司id
        Long qiyaCompanyCarrierId = commonBiz.getQiyaCompanyCarrierId();
        for (ImportStaffListRequestModel importStaffListRequestModel : importStaffRequestModel.getImportList()) {
            TStaffBasic tqStaffBasic = tqStaffBasicMapper.getByMobile(importStaffListRequestModel.getMobile());
            //查找我司关联关系
            TCarrierDriverRelation carrierDriverRelation = null;
            if (tqStaffBasic != null) {
                carrierDriverRelation = tCarrierDriverRelationMapper.getByCompanyCarrierIdAndDriverId(qiyaCompanyCarrierId, tqStaffBasic.getId(), null);
            }
            try {
                if (tqStaffBasic != null && carrierDriverRelation != null) {
                    //已存在司机信息
                    staffTransBiz.importModifyStaff(carrierDriverRelation, tqStaffBasic, importStaffListRequestModel);
                } else {
                    //不存在司机信息
                    staffTransBiz.importAddStaff(tqStaffBasic, qiyaCompanyCarrierId, importStaffListRequestModel);
                }
                importStaffResponseModel.setSuccessNumber(importStaffResponseModel.getSuccessNumber() + 1);
            } catch (Exception e) {
                importStaffResponseModel.setFailuresNumber(importStaffResponseModel.getFailuresNumber() + 1);
            }

        }
        return importStaffResponseModel;
    }

    /**
     * 导入人员证件
     * @param srcUrlModel
     */
    @Transactional
    public void importStaffCertificate(SrcUrlModel srcUrlModel) {
        if (StringUtils.isBlank(srcUrlModel.getFileName()) || StringUtils.isBlank(srcUrlModel.getRelativePath())) {
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }
        CertificationPicturesFileTypeEnum typeEnum = null;
        if (RegExpValidatorUtil.match("^([\\u4e00-\\u9fa5A-Za-z])+-身份证1$", srcUrlModel.getFileName())) {
            typeEnum = CertificationPicturesFileTypeEnum.STAFF_IDENTITY_FRONT;
        } else if (RegExpValidatorUtil.match("^([\\u4e00-\\u9fa5A-Za-z])+-身份证2$", srcUrlModel.getFileName())) {
            typeEnum = CertificationPicturesFileTypeEnum.STAFF_IDENTITY_BACK;
        } else if (RegExpValidatorUtil.match("^([\\u4e00-\\u9fa5A-Za-z])+-资格证卡1$", srcUrlModel.getFileName())) {
            typeEnum = CertificationPicturesFileTypeEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_FRONT;
        } else if (RegExpValidatorUtil.match("^([\\u4e00-\\u9fa5A-Za-z])+-资格证卡2$", srcUrlModel.getFileName())) {
            typeEnum = CertificationPicturesFileTypeEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_BACK;
        } else if (RegExpValidatorUtil.match("^([\\u4e00-\\u9fa5A-Za-z])+-资格证纸1$", srcUrlModel.getFileName())) {
            typeEnum = CertificationPicturesFileTypeEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_FRONT;
        } else if (RegExpValidatorUtil.match("^([\\u4e00-\\u9fa5A-Za-z])+-资格证纸2$", srcUrlModel.getFileName())) {
            typeEnum = CertificationPicturesFileTypeEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_BACK;
        } else if (RegExpValidatorUtil.match("^([\\u4e00-\\u9fa5A-Za-z])+-驾驶证$", srcUrlModel.getFileName())) {
            typeEnum = CertificationPicturesFileTypeEnum.DRIVER_LICENSE_FRONT;
        }

        if (typeEnum == null) {
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }
        String userName = srcUrlModel.getFileName().substring(0, srcUrlModel.getFileName().indexOf('-'));
        if (StringUtils.isBlank(userName)) {
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }
        if (tqStaffBasicMapper.getCountByName(userName) > 1) {
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }
        TStaffBasic tqStaffBasic = tqStaffBasicMapper.getByName(userName);
        if (tqStaffBasic == null) {
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);

        }
        List<TCertificationPictures> staffDriverTicketsList = null;
        Long objectId = null;
        if (CertificationPicturesObjectTypeEnum.T_STAFF_BASIC == typeEnum.getObjectType()) {
            objectId = tqStaffBasic.getId();
        } else if (CertificationPicturesObjectTypeEnum.T_STAFF_DRIVER_CREDENTIAL == typeEnum.getObjectType()) {
            TStaffDriverCredential tqStaffDriverCredential = staffDriverCredentialMapper.getDriverCredentialByStaffId(tqStaffBasic.getId());
            if (tqStaffDriverCredential == null) {
                throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
            }
            objectId = tqStaffDriverCredential.getId();
        }

        staffDriverTicketsList = tqCertificationPicturesMapper.getByObjectIdType(objectId, typeEnum.getObjectType().getObjectType(), typeEnum.getFileType());
        if (staffDriverTicketsList.size() > CommonConstant.INTEGER_ONE) {
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }
        String relativeUrl = commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.STAFF_CERTIFICATE.getKey(), "", srcUrlModel.getRelativePath(), null);
        if (ListUtils.isNotEmpty(staffDriverTicketsList)) {
            TCertificationPictures tqCertificationPictures = staffDriverTicketsList.get(CommonConstant.INTEGER_ZERO);
            TCertificationPictures upPicture = new TCertificationPictures();
            upPicture.setId(tqCertificationPictures.getId());
            upPicture.setFileType(typeEnum.getFileType());
            upPicture.setFileTypeName(typeEnum.getFileName());
            upPicture.setFileName(typeEnum.getFileName());
            upPicture.setFilePath(relativeUrl);
            upPicture.setObjectType(typeEnum.getObjectType().getObjectType());
            upPicture.setObjectId(objectId);
            upPicture.setUploadUserName(BaseContextHandler.getUserName());
            upPicture.setUploadTime(new Date());
            upPicture.setSuffix(relativeUrl.substring(relativeUrl.lastIndexOf('.')));
            commonBiz.setBaseEntityModify(upPicture, BaseContextHandler.getUserName());
            tqCertificationPicturesMapper.updateByPrimaryKeySelective(upPicture);
        } else {
            TCertificationPictures newPicture = new TCertificationPictures();
            newPicture.setFileType(typeEnum.getFileType());
            newPicture.setFileTypeName(typeEnum.getFileName());
            newPicture.setFileName(typeEnum.getFileName());
            newPicture.setFilePath(relativeUrl);
            newPicture.setObjectType(typeEnum.getObjectType().getObjectType());
            newPicture.setObjectId(objectId);
            newPicture.setUploadUserName(BaseContextHandler.getUserName());
            newPicture.setUploadTime(new Date());
            newPicture.setSuffix(relativeUrl.substring(relativeUrl.lastIndexOf('.')));
            commonBiz.setBaseEntityAdd(newPicture, BaseContextHandler.getUserName());
            tqCertificationPicturesMapper.insertSelective(newPicture);
        }
    }

    /**
     * 模糊查询司机信息
     *
     * @param requestModel
     * @return
     */
    public PageInfo<FuzzyQueryDriverInfoResponseModel> fuzzyQueryDriverInfo(FuzzyQueryDriverInfoRequestModel requestModel) {
        requestModel.enablePaging();
        return new PageInfo<>(tqStaffBasicMapper.fuzzyQueryDriverMessage(requestModel));
    }

    /**
     * 司机启用/禁用
     *
     * @param requestModel
     */
    @Transactional
    public void enableOrDisable(EnableStaffRequestModel requestModel) {
        //查询车主司机关联关系
        List<TCarrierDriverRelation> dbCarrierDriverRelationList = tCarrierDriverRelationMapper.getByRelationIds(StringUtils.join(requestModel.getCarrierDriverIds(), ','));
        if (ListUtils.isEmpty(dbCarrierDriverRelationList)) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }

        //拼接司机id
        List<Long> driverIdList = dbCarrierDriverRelationList.stream().map(TCarrierDriverRelation::getDriverId).collect(Collectors.toList());
        String driverIdsStr = StringUtils.join(driverIdList, ',');
        //查询司机基础信息
        List<TStaffBasic> tStaffBasicList = tqStaffBasicMapper.getStaffByIds(driverIdsStr);
        if (ListUtils.isEmpty(tStaffBasicList)) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }
        Map<Long, TStaffBasic> tStaffBasicMap = tStaffBasicList.stream().collect(Collectors.toMap(TStaffBasic::getId, item -> item));

        //查询车辆司机关联信息
        Map<Long, TStaffVehicleRelation> staffVehicleRelationMap = tStaffVehicleRelationMapper.selectRelationsByStaffIds(driverIdsStr)
                .stream().collect(Collectors.toMap(TStaffVehicleRelation::getStaffId, item -> item, (r1, r2) -> r2));

        List<Long> carrierStaffRelIds = new ArrayList<>();
        List<Long> staffIds = new ArrayList<>();
        Map<Long, String> driverPhoneMap = new HashMap<>();
        TCarrierDriverRelation tCarrierDriverRelationUp;
        List<TCarrierDriverRelation> tCarrierDriverRelationUpList = new ArrayList<>();
        for (TCarrierDriverRelation carrierDriverRelation : dbCarrierDriverRelationList) {
            //启用禁用判断
            if (EnabledEnum.ENABLED.getKey().equals(requestModel.getEnabled())) {
                if (EnabledEnum.ENABLED.getKey().equals(carrierDriverRelation.getEnabled())) {
                    throw new BizException(CarrierDataExceptionEnum.ENABLE_DISABLE_ERROR);
                }
            } else if (EnabledEnum.DISABLED.getKey().equals(requestModel.getEnabled())) {
                if (EnabledEnum.DISABLED.getKey().equals(carrierDriverRelation.getEnabled())) {
                    throw new BizException(CarrierDataExceptionEnum.ENABLE_DISABLE_ERROR);
                }
            } else {
                throw new BizException(CarrierDataExceptionEnum.ENABLE_DISABLE_PARAMS_ERROR);
            }

            TStaffVehicleRelation staffVehicleRelation = staffVehicleRelationMap.get(carrierDriverRelation.getDriverId());
            if (staffVehicleRelation != null && staffVehicleRelation.getCompanyCarrierId().equals(carrierDriverRelation.getCompanyCarrierId())) {
                /*如果司机和车辆还存在关联关系*/
                throw new BizException(CarrierDataExceptionEnum.STAFF_HAVE_VEHICLE_RELATION);
            }

            //禁用司机基础信息
            tCarrierDriverRelationUp = new TCarrierDriverRelation();
            tCarrierDriverRelationUp.setId(carrierDriverRelation.getId());
            tCarrierDriverRelationUp.setEnabled(requestModel.getEnabled());
            commonBiz.setBaseEntityModify(tCarrierDriverRelationUp, BaseContextHandler.getUserName());
            tCarrierDriverRelationUpList.add(tCarrierDriverRelationUp);

            TStaffBasic tStaffBasic = tStaffBasicMap.get(carrierDriverRelation.getDriverId());
            //人员类型是司机,并且司机只关联了一个车主
            if (tStaffBasic != null
                    && (StaffTypeEnum.DRIVER.getKey().equals(tStaffBasic.getType()) || StaffTypeEnum.DRIVER_SUPERCARGO.getKey().equals(tStaffBasic.getType()))) {
                carrierStaffRelIds.add(carrierDriverRelation.getId());
                if (!staffIds.contains(tStaffBasic.getId())){
                    staffIds.add(tStaffBasic.getId());
                    driverPhoneMap.put(tStaffBasic.getId(), tStaffBasic.getMobile());
                }
            }
        }

        //更新车主司机关联关系
        if (ListUtils.isNotEmpty(tCarrierDriverRelationUpList)) {
            tCarrierDriverRelationMapper.batchUpdate(tCarrierDriverRelationUpList);
        }


        //修改司机登录账号状态
        if (ListUtils.isNotEmpty(carrierStaffRelIds)) {
            //禁用启用yelo基础数据的关系
            UpdateTCustomerAccountRelationIfCloseRequestModel batchUpdateAccountRelationRequestModel = new UpdateTCustomerAccountRelationIfCloseRequestModel();
            batchUpdateAccountRelationRequestModel.setUserIds(StringUtils.listToString(carrierStaffRelIds, ','));
            batchUpdateAccountRelationRequestModel.setUserRole(AccountUserRoleTypeEnum.DRIVER_APPLET.getKey());
            if (EnabledEnum.DISABLED.getKey().equals(requestModel.getEnabled())) {
                batchUpdateAccountRelationRequestModel.setIfClose(CommonConstant.INTEGER_ONE);
            } else {
                batchUpdateAccountRelationRequestModel.setIfClose(CommonConstant.INTEGER_ZERO);
            }
            batchUpdateAccountRelationRequestModel.setEnabled(requestModel.getEnabled());
            customerAccountBiz.updateTCustomerAccountRelationIfClose(batchUpdateAccountRelationRequestModel);
        }

        //如果司机的账号都被禁用，则强制退出登录
        if (EnabledEnum.DISABLED.getKey().equals(requestModel.getEnabled()) && ListUtils.isNotEmpty(staffIds)) {//操作禁用
            List<TCarrierDriverRelation> tCarrierDriverRelationList = tCarrierDriverRelationMapper.queryByDriverIds(staffIds);
            Map<Long, List<Integer>> driverEnabledMap = tCarrierDriverRelationList.stream().collect(Collectors.groupingBy(TCarrierDriverRelation::getDriverId, Collectors.mapping(TCarrierDriverRelation::getEnabled, Collectors.toList())));
            for (Map.Entry<Long, List<Integer>> entry : driverEnabledMap.entrySet()) {
                if (!entry.getValue().contains(EnabledEnum.ENABLED.getKey()) && StringUtils.isNotBlank(driverPhoneMap.get(entry.getKey()))) {
                    commonBiz.clearToken(driverPhoneMap.get(entry.getKey()));
                }
            }
        }
    }

    /**
     * 根据accountId查询司机信息
     * @return
     */
    public GetStaffDetailByAccountIdResponseModel getStaffDetailByAccountId(){
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        //查询司机信息
        TStaffBasic tStaffBasic = tqStaffBasicMapper.selectByPrimaryKey(loginDriverAppletUserId);
        if (tStaffBasic == null || tStaffBasic.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }
        GetStaffDetailByAccountIdResponseModel detail = MapperUtils.mapper(tStaffBasic, GetStaffDetailByAccountIdResponseModel.class);
        detail.setStaffId(tStaffBasic.getId());
        return detail;

    }

    /**
     * 删除外部司机
     *
     * @param requestModel 车主人员关联id
     */
    public void delStaff(DelStaffRequestModel requestModel) {
        //查询人员车主关联信息
        TCarrierDriverRelation tCarrierDriverRelation = tCarrierDriverRelationMapper.selectByPrimaryKey(requestModel.getCarrierDriverId());
        if (tCarrierDriverRelation == null || IfValidEnum.INVALID.getKey().equals(tCarrierDriverRelation.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }

        //查询人员基本信息
        TStaffBasic tStaffBasic = tqStaffBasicMapper.selectByPrimaryKey(tCarrierDriverRelation.getDriverId());
        if (tStaffBasic == null || IfValidEnum.INVALID.getKey().equals(tStaffBasic.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }

        //禁用状态才可以修改
        if (!EnabledEnum.DISABLED.getKey().equals(tCarrierDriverRelation.getEnabled())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_ONLY_DISABLED);
        }

        //查询车主信息
        TCompanyCarrier dbCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(tCarrierDriverRelation.getCompanyCarrierId());
        if (dbCompanyCarrier == null || dbCompanyCarrier.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }
        //其他车主才有删除功能
        if (!IsOurCompanyEnum.OTHER_COMPANY.getKey().equals(dbCompanyCarrier.getLevel())) {
            throw new BizException(CarrierDataExceptionEnum.OTHER_COMPANY_DEL_PERMISSION);
        }

        //判断这个车辆是否存在未卸货的运单
        if (tcarrierOrderMapper.getSpecifiedStateCount(ConverterUtils.toString(tCarrierDriverRelation.getDriverId()), null, tCarrierDriverRelation.getCompanyCarrierId()) > CommonConstant.INTEGER_ZERO) {
            throw new BizException(CarrierDataExceptionEnum.THE_VEHICLE_IS_CARRYING_GOODS);
        }

        List<TCarrierDriverRelation> carrierDriverRelationList = tCarrierDriverRelationMapper.queryByDriverIds(Collections.singletonList(tStaffBasic.getId()));
        if (ListUtils.isNotEmpty(carrierDriverRelationList) && carrierDriverRelationList.size() == CommonConstant.INTEGER_ONE) {
            /*只和当前车主有关联,删除司机信息*/
            //删除司机
            TStaffBasic tStaffBasicUp = new TStaffBasic();
            tStaffBasicUp.setId(tStaffBasic.getId());
            tStaffBasicUp.setValid(IfValidEnum.INVALID.getKey());
            commonBiz.setBaseEntityModify(tStaffBasicUp, BaseContextHandler.getUserName());
            tqStaffBasicMapper.updateByPrimaryKeySelective(tStaffBasicUp);

            //只有司机一个角色没有个人车主,也删除实名信息
            FuzzySearchCompanyCarrierResponseModel personCarrier = tCompanyCarrierMapper.selectPersonCarrierByMobile(tStaffBasic.getMobile());
            if (personCarrier == null) {
                tRealNameAuthenticationMapper.delRealNameAuthenticationByMobile(Collections.singletonList(tStaffBasic.getMobile()));
            }
        }

        //删除当前车主司机关联关系
        TCarrierDriverRelation tCarrierDriverRelationUp = new TCarrierDriverRelation();
        tCarrierDriverRelationUp.setId(tCarrierDriverRelation.getId());
        tCarrierDriverRelationUp.setValid(IfValidEnum.INVALID.getKey());
        commonBiz.setBaseEntityModify(tCarrierDriverRelationUp, BaseContextHandler.getUserName());
        tCarrierDriverRelationMapper.updateByPrimaryKeySelective(tCarrierDriverRelationUp);

        //查询车辆司机关系
        List<TStaffVehicleRelation> dbStaffVehicleRelationList = tStaffVehicleRelationMapper.selectRelationsByVehicleIdDriverId(ConverterUtils.toString(tCarrierDriverRelation.getDriverId()), null, tCarrierDriverRelation.getCompanyCarrierId());
        //存在车辆司机关系，则删除
        if (ListUtils.isNotEmpty(dbStaffVehicleRelationList)) {
            TStaffVehicleRelation staffVehicleRelation;
            List<TStaffVehicleRelation> delRelationList = new ArrayList<>();
            for (TStaffVehicleRelation relation : dbStaffVehicleRelationList) {
                staffVehicleRelation = new TStaffVehicleRelation();
                staffVehicleRelation.setId(relation.getId());
                staffVehicleRelation.setValid(IfValidEnum.INVALID.getKey());
                commonBiz.setBaseEntityModify(staffVehicleRelation, BaseContextHandler.getUserName());
                delRelationList.add(staffVehicleRelation);
            }
            tStaffVehicleRelationMapper.batchUpdateSelective(delRelationList);
        }

        //删除登录账号
        OpenAccountRequestModel openAccountRequestModel = new OpenAccountRequestModel();
        openAccountRequestModel.setType(CommonConstant.INTEGER_THREE);
        openAccountRequestModel.setUserId(tCarrierDriverRelation.getId());
        openAccountRequestModel.setUserRole(AccountUserRoleTypeEnum.DRIVER_APPLET.getKey());
        customerAccountBiz.openOrCloseAccount(openAccountRequestModel);
    }

    /**
     * 前台车主批量启用/禁用司机
     *
     * @param requestModel 司机id
     */
    @Transactional
    public void batchEnableOrDisable(BatchEnableOrDisableRequestModel requestModel) {
        //查询车主司机关联关系
        List<TCarrierDriverRelation> dbCarrierDriverRelationList = tCarrierDriverRelationMapper.getByRelationIds(StringUtils.join(requestModel.getCarrierDriverIds(), ','));
        if (ListUtils.isEmpty(dbCarrierDriverRelationList)) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }

        //拼接司机id
        List<Long> driverIdList = dbCarrierDriverRelationList.stream().map(TCarrierDriverRelation::getDriverId).collect(Collectors.toList());
        String driverIdsStr = StringUtils.join(driverIdList, ',');
        //查询司机基础信息
        List<TStaffBasic> tStaffBasicList = tqStaffBasicMapper.getStaffByIds(driverIdsStr);
        if (ListUtils.isEmpty(tStaffBasicList)) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }
        Map<Long, TStaffBasic> tStaffBasicMap = tStaffBasicList.stream().collect(Collectors.toMap(TStaffBasic::getId, item -> item));

        //查询车辆司机关联信息
        Map<Long, TStaffVehicleRelation> staffVehicleRelationMap = tStaffVehicleRelationMapper.selectRelationsByStaffIds(driverIdsStr)
                .stream().collect(Collectors.toMap(TStaffVehicleRelation::getStaffId, item -> item, (r1, r2) -> r2));

        //查询当前登录司机车主id
        Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();

        List<Long> carrierStaffRelIds = new ArrayList<>();
        TCarrierDriverRelation tCarrierDriverRelationUp;
        List<TCarrierDriverRelation> tCarrierDriverRelationUpList = new ArrayList<>();
        for (TCarrierDriverRelation carrierDriverRelation : dbCarrierDriverRelationList) {
            if (!carrierDriverRelation.getCompanyCarrierId().equals(loginUserCompanyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
            }
            //启用禁用判断
            if (EnabledEnum.ENABLED.getKey().equals(requestModel.getEnabled())) {
                if (EnabledEnum.ENABLED.getKey().equals(carrierDriverRelation.getEnabled())) {
                    throw new BizException(CarrierDataExceptionEnum.ENABLE_DISABLE_ERROR);
                }
            } else if (EnabledEnum.DISABLED.getKey().equals(requestModel.getEnabled())) {
                if (EnabledEnum.DISABLED.getKey().equals(carrierDriverRelation.getEnabled())) {
                    throw new BizException(CarrierDataExceptionEnum.ENABLE_DISABLE_ERROR);
                }
            } else {
                throw new BizException(CarrierDataExceptionEnum.ENABLE_DISABLE_PARAMS_ERROR);
            }

            TStaffVehicleRelation staffVehicleRelation = staffVehicleRelationMap.get(carrierDriverRelation.getDriverId());
            if (staffVehicleRelation != null && staffVehicleRelation.getCompanyCarrierId().equals(carrierDriverRelation.getCompanyCarrierId())) {
                /*如果司机和车辆还存在关联关系*/
                throw new BizException(CarrierDataExceptionEnum.STAFF_HAVE_VEHICLE_RELATION);
            }

            //禁用司机基础信息
            tCarrierDriverRelationUp = new TCarrierDriverRelation();
            tCarrierDriverRelationUp.setId(carrierDriverRelation.getId());
            tCarrierDriverRelationUp.setEnabled(requestModel.getEnabled());
            commonBiz.setBaseEntityModify(tCarrierDriverRelationUp, BaseContextHandler.getUserName());
            tCarrierDriverRelationUpList.add(tCarrierDriverRelationUp);

            TStaffBasic tStaffBasic = tStaffBasicMap.get(carrierDriverRelation.getDriverId());
            //人员类型是司机,并且司机只关联了一个车主
            if (tStaffBasic != null
                    && (StaffTypeEnum.DRIVER.getKey().equals(tStaffBasic.getType()) || StaffTypeEnum.DRIVER_SUPERCARGO.getKey().equals(tStaffBasic.getType()))) {
                carrierStaffRelIds.add(carrierDriverRelation.getId());
            }
        }

        //更新车主司机关联关系
        if (ListUtils.isNotEmpty(tCarrierDriverRelationUpList)) {
            tCarrierDriverRelationMapper.batchUpdate(tCarrierDriverRelationUpList);
        }

        //如果司机和其他车主还有关联那就不关闭/账号
        if (ListUtils.isNotEmpty(carrierStaffRelIds)) {
            //禁用启用yelo基础数据的关系
            UpdateTCustomerAccountRelationIfCloseRequestModel batchUpdateAccountRelationRequestModel = new UpdateTCustomerAccountRelationIfCloseRequestModel();
            batchUpdateAccountRelationRequestModel.setUserIds(StringUtils.listToString(carrierStaffRelIds, ','));
            batchUpdateAccountRelationRequestModel.setUserRole(AccountUserRoleTypeEnum.DRIVER_APPLET.getKey());
            if (EnabledEnum.DISABLED.getKey().equals(requestModel.getEnabled())) {
                batchUpdateAccountRelationRequestModel.setIfClose(CommonConstant.INTEGER_ONE);
            } else {
                batchUpdateAccountRelationRequestModel.setIfClose(CommonConstant.INTEGER_ZERO);
            }
            batchUpdateAccountRelationRequestModel.setEnabled(requestModel.getEnabled());
            customerAccountBiz.updateTCustomerAccountRelationIfClose(batchUpdateAccountRelationRequestModel);
        }
    }

    /**
     * 前台车主批量删除司机
     *
     * @param requestModel 司机id
     */
    @Transactional
    public void batchDelStaff(BatchDelStaffRequestModel requestModel) {
        //查询当前登录司机车主id
        Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        //查询车主信息
        FuzzySearchCompanyCarrierResponseModel companyCarrierInfo = tCompanyCarrierMapper.getCompanyCarrierInfoById(loginUserCompanyCarrierId);
        if (companyCarrierInfo == null) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }

        //其他车主才有删除功能
        if (!IsOurCompanyEnum.OTHER_COMPANY.getKey().equals(companyCarrierInfo.getIsOurCompany())) {
            throw new BizException(CarrierDataExceptionEnum.OTHER_COMPANY_DEL_PERMISSION);
        }

        String relIds = StringUtils.join(requestModel.getCarrierDriverIds(), ',');
        List<TCarrierDriverRelation> dbCarrierDriverRelationsList = tCarrierDriverRelationMapper.getByRelationIds(relIds);
        if (ListUtils.isEmpty(dbCarrierDriverRelationsList)) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }
        List<Long> driverIdList = dbCarrierDriverRelationsList.stream().map(TCarrierDriverRelation::getDriverId).collect(Collectors.toList());
        String staffIds = StringUtils.join(driverIdList, ',');
        //查询司机基础信息
        List<TStaffBasic> tStaffBasicList = tqStaffBasicMapper.getStaffByIds(staffIds);
        if (ListUtils.isEmpty(tStaffBasicList)) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }

        //判断这个车辆是否存在未卸货的运单
        if (tcarrierOrderMapper.getSpecifiedStateCount(StringUtils.join(tStaffBasicList.stream().map(TStaffBasic::getId).collect(Collectors.toList()), ','), null, loginUserCompanyCarrierId) > CommonConstant.INTEGER_ZERO) {
            throw new BizException(CarrierDataExceptionEnum.THE_VEHICLE_IS_CARRYING_GOODS);
        }

        //查询司机与车主所有关系
        List<TCarrierDriverRelation> tCarrierDriverRelationsList = tCarrierDriverRelationMapper.queryByDriverIds(driverIdList);

        //查询司机车主关联关系
        Map<Long, List<TCarrierDriverRelation>> carrierDriverGroupMap = tCarrierDriverRelationsList
                .stream().collect(Collectors.groupingBy(TCarrierDriverRelation::getDriverId));

        //查询司机是否还是个人车主
        Map<String, FuzzySearchCompanyCarrierResponseModel> personCarrierMap = new HashMap<>();
        List<FuzzySearchCompanyCarrierResponseModel> personCarriers = tCompanyCarrierMapper.selectPersonCarrierByMobiles(tStaffBasicList.stream().map(TStaffBasic::getMobile).collect(Collectors.toList()));
        if (ListUtils.isNotEmpty(personCarriers)) {
            personCarrierMap = personCarriers.stream().collect(Collectors.toMap(FuzzySearchCompanyCarrierResponseModel::getContactPhone, Function.identity()));
        }

        TStaffBasic tStaffBasicUp;
        List<TStaffBasic> tStaffBasicUpList = new ArrayList<>();

        List<String> delRealNameMobile = new ArrayList<>();
        TCarrierDriverRelation tCarrierDriverRelationUp;
        List<TCarrierDriverRelation> tCarrierDriverRelationUpList = new ArrayList<>();
        for (TStaffBasic tStaffBasic : tStaffBasicList) {
            TCarrierDriverRelation currCarrierDriverRelation = null;
            //判断当前司机是否只和当前车主关联
            List<TCarrierDriverRelation> tCarrierDriverRelations = carrierDriverGroupMap.get(tStaffBasic.getId());
            if (ListUtils.isEmpty(tCarrierDriverRelations)) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
            }
            if (tCarrierDriverRelations.size() == CommonConstant.INTEGER_ONE) {
                /*只和当前车主有关联,删除司机信息*/
                //删除司机
                tStaffBasicUp = new TStaffBasic();
                tStaffBasicUp.setId(tStaffBasic.getId());
                tStaffBasicUp.setValid(IfValidEnum.INVALID.getKey());
                commonBiz.setBaseEntityModify(tStaffBasicUp, BaseContextHandler.getUserName());
                tStaffBasicUpList.add(tStaffBasicUp);
                //要删除的关联关系
                currCarrierDriverRelation = tCarrierDriverRelations.get(CommonConstant.INTEGER_ZERO);
                //删除实名信息
                if (personCarrierMap.get(tStaffBasic.getMobile()) == null) {
                    delRealNameMobile.add(tStaffBasic.getMobile());
                }
            } else {
                for (TCarrierDriverRelation tCarrierDriverRelation : tCarrierDriverRelations) {
                    //是当前司机与当前车主的关联关系
                    if (tCarrierDriverRelation.getCompanyCarrierId().equals(loginUserCompanyCarrierId)) {
                        //要删除的关联关系
                        currCarrierDriverRelation = tCarrierDriverRelation;
                        break;
                    }
                }
            }

            if (currCarrierDriverRelation == null) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
            }

            //禁用状态才可以删除
            if (!EnabledEnum.DISABLED.getKey().equals(currCarrierDriverRelation.getEnabled())) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_ONLY_DISABLED);
            }
            //判断是不是当前车主的司机
            if (!currCarrierDriverRelation.getCompanyCarrierId().equals(loginUserCompanyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
            }
            //删除当前车主司机关联关系
            tCarrierDriverRelationUp = new TCarrierDriverRelation();
            tCarrierDriverRelationUp.setId(currCarrierDriverRelation.getId());
            tCarrierDriverRelationUp.setValid(IfValidEnum.INVALID.getKey());
            commonBiz.setBaseEntityModify(tCarrierDriverRelationUp, BaseContextHandler.getUserName());
            tCarrierDriverRelationUpList.add(tCarrierDriverRelationUp);
        }

        if (ListUtils.isNotEmpty(tStaffBasicUpList)) {
            tqStaffBasicMapper.batchUpdate(tStaffBasicUpList);
        }

        if (ListUtils.isNotEmpty(tCarrierDriverRelationUpList)) {
            tCarrierDriverRelationMapper.batchUpdate(tCarrierDriverRelationUpList);
        }

        if (ListUtils.isNotEmpty(delRealNameMobile)) {
            tRealNameAuthenticationMapper.delRealNameAuthenticationByMobile(delRealNameMobile);
        }

        //查询车辆司机关系
        List<TStaffVehicleRelation> dbStaffVehicleRelationList = tStaffVehicleRelationMapper.selectRelationsByVehicleIdDriverId(relIds, null, loginUserCompanyCarrierId);
        //存在车辆司机关系，则删除
        if (ListUtils.isNotEmpty(dbStaffVehicleRelationList)) {
            TStaffVehicleRelation staffVehicleRelation;
            List<TStaffVehicleRelation> delRelationList = new ArrayList<>();
            for (TStaffVehicleRelation relation : dbStaffVehicleRelationList) {
                staffVehicleRelation = new TStaffVehicleRelation();
                staffVehicleRelation.setId(relation.getId());
                staffVehicleRelation.setValid(IfValidEnum.INVALID.getKey());
                commonBiz.setBaseEntityModify(staffVehicleRelation, BaseContextHandler.getUserName());
                delRelationList.add(staffVehicleRelation);
            }
            tStaffVehicleRelationMapper.batchUpdateSelective(delRelationList);
        }

        //删除登录账号
        if (ListUtils.isNotEmpty(tCarrierDriverRelationUpList)) {
            for (TCarrierDriverRelation tCarrierDriverRelation : tCarrierDriverRelationUpList) {
                OpenAccountRequestModel openAccountRequestModel = new OpenAccountRequestModel();
                openAccountRequestModel.setType(CommonConstant.INTEGER_THREE);
                openAccountRequestModel.setUserId(tCarrierDriverRelation.getId());
                openAccountRequestModel.setUserRole(AccountUserRoleTypeEnum.DRIVER_APPLET.getKey());
                customerAccountBiz.openOrCloseAccount(openAccountRequestModel);
            }
        }
    }

    /**
     * 仓库权限开关
     */
    public void warehouseSwitch(WarehouseSwitchStaffRequestModel requestModel) {
        //查询人员基本信息
        TStaffBasic tStaffBasic = tqStaffBasicMapper.selectByPrimaryKey(requestModel.getStaffId());
        if (tStaffBasic == null || IfValidEnum.INVALID.getKey().equals(tStaffBasic.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }

        //开关判断
        if (SwitchEnum.OPEN.getKey().equals(tStaffBasic.getWarehouseSwitch())) {
            if (!SwitchEnum.CLOSE.getKey().equals(requestModel.getWarehouseSwitch())) {
                throw new BizException(CarrierDataExceptionEnum.DATA_STATUS_ERROR);
            }
        } else if (SwitchEnum.CLOSE.getKey().equals(tStaffBasic.getWarehouseSwitch())) {
            if (!SwitchEnum.OPEN.getKey().equals(requestModel.getWarehouseSwitch())) {
                throw new BizException(CarrierDataExceptionEnum.DATA_STATUS_ERROR);
            }
        }

        //更新开关状态
        TStaffBasic tStaffBasicUp = new TStaffBasic();
        tStaffBasicUp.setId(tStaffBasic.getId());
        tStaffBasicUp.setWarehouseSwitch(requestModel.getWarehouseSwitch());
        commonBiz.setBaseEntityModify(tStaffBasicUp, BaseContextHandler.getUserName());
        tqStaffBasicMapper.updateByPrimaryKeySelective(tStaffBasicUp);
    }
}
