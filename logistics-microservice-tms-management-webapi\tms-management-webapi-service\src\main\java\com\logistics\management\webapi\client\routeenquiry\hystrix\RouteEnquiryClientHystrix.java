package com.logistics.management.webapi.client.routeenquiry.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.routeenquiry.RouteEnquiryClient;
import com.logistics.management.webapi.client.routeenquiry.request.*;
import com.logistics.management.webapi.client.routeenquiry.response.GetRouteEnquiryDetailResponseModel;
import com.logistics.management.webapi.client.routeenquiry.response.GetRouteEnquiryQuoteDetailResponseModel;
import com.logistics.management.webapi.client.routeenquiry.response.SearchRouteEnquiryListResponseModel;
import com.logistics.management.webapi.client.routeenquiry.response.SearchRouteEnquirySummaryListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/7/10 13:46
 */
@Component
public class RouteEnquiryClientHystrix implements RouteEnquiryClient {
    @Override
    public Result<PageInfo<SearchRouteEnquiryListResponseModel>> searchList(SearchRouteEnquiryListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> create(CreateRouteEnquiryRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetRouteEnquiryDetailResponseModel> getDetail(GetRouteEnquiryDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> cancelQuote(GetRouteEnquiryDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetRouteEnquiryQuoteDetailResponseModel>> getQuoteDetail(GetRouteEnquiryQuoteDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> selectCarrierQuote(RouteEnquirySelectCarrierQuoteRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> settleAudit(RouteEnquirySettleAuditRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> archive(RouteEnquiryArchiveRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<SearchRouteEnquirySummaryListResponseModel>> searchSummaryList(SearchRouteEnquirySummaryListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchRouteEnquirySummaryListResponseModel>> exportSummaryList(SearchRouteEnquirySummaryListRequestModel requestModel) {
        return Result.timeout();
    }
}
