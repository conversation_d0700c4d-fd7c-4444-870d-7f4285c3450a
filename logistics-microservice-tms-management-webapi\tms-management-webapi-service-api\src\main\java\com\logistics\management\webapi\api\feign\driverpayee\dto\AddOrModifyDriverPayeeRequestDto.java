package com.logistics.management.webapi.api.feign.driverpayee.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.List;

@Data
public class AddOrModifyDriverPayeeRequestDto {

    @ApiModelProperty("收款人账户ID")
    private String driverPayeeId = "";
    @ApiModelProperty("收款人姓名")
    @NotBlank(message = "姓名不能为空")
    @Pattern(regexp = "[\\u4E00-\\u9FA5a-zA-Z]{2,50}" ,message= "请输入正确的姓名")
    private String name = "";
    @ApiModelProperty("收款人联系方式")
    private String mobile = "";
    @NotBlank(message = "身份证号码不能为空")
    @ApiModelProperty("收款人身份证号码")
    private String identityNo = "";
    @ApiModelProperty("银行Id")
    private String bankId = "";
    @ApiModelProperty("银行卡号")
    @NotBlank(message = "银行卡号不能为空")
    @Pattern(regexp = "[\\d]{1,50}" ,message= "请输入正确的银行卡号")
    private String bankCardNo = "";
    @ApiModelProperty("身份证正面")
    private String identityFront = "";
    @ApiModelProperty("身份证背面")
    private String identityBack = "";
    @ApiModelProperty("收款证件列表,相对路径")
    private List<String> imageList;
    @ApiModelProperty("备注")
    @Length(min=0, max=300,message= "请输入300字符以内的备注")
    private String remark = "";

}
