package com.logistics.tms.api.feign.freight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: sj
 * @Date: 2019/12/31 18:33
 */
@Data
public class CompanyDriverFreightResponseModel {
    @ApiModelProperty("运价ID")
    private Long freightId;
    @ApiModelProperty("运价规则ID")
    private Long freightAddressRuleId;
    @ApiModelProperty("费用类型")
    private Integer freightType;
    @ApiModelProperty("阶梯费用")
    private BigDecimal freightFee;
    @ApiModelProperty("多装多卸价")
    private BigDecimal markupFreightFee;
    @ApiModelProperty("阶梯费用(单价*数量或整车价)")
    private BigDecimal totalFee;
}
