package com.logistics.appapi.client.website.entrust;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.website.entrust.hystrix.EntrustSourceClientHystrix;
import com.logistics.appapi.client.website.entrust.request.AddEntrustSourceRequestModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2024/3/15 11:00
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        fallback = EntrustSourceClientHystrix.class)
public interface EntrustSourceClient {

    @ApiOperation("成为货主")
    @PostMapping("/service/entrustSource/addEntrustSource")
    Result addEntrustSource(@RequestBody AddEntrustSourceRequestModel requestModel);

}
