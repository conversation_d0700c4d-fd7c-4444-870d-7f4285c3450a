package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/22
 */
@Data
public class CarrierOrderDetailTicketsModel {

	@ApiModelProperty("回单审核状态：0 待审核，1 已审核，2 已驳回")
	private Integer ticketsAuditStatus;

	@ApiModelProperty("到达提货地凭证数量")
	private String reachLoadTicketsCount="0";

	@ApiModelProperty("出库单数量")
	private String loadTicketsCount="0";

	@ApiModelProperty("到达卸货地凭证数量")
	private String reachUnloadTicketsCount="0";

	@ApiModelProperty("签收单/回单数量")
	private String unLoadTicketsCount="0";

	@ApiModelProperty("其他单据数量")
	private String otherTicketsCount="0";
}
