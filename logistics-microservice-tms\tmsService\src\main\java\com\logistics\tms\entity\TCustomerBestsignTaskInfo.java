package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2023/01/13
*/
@Data
public class TCustomerBestsignTaskInfo extends BaseEntity {
    /**
    * 实名认证表id
    */
    @ApiModelProperty("实名认证表id")
    private Long realNameAuthId;

    /**
    * 上上签账户
    */
    @ApiModelProperty("上上签账户")
    private String bestsignAccount;

    /**
    * 异步校验证书申请状态taskid
    */
    @ApiModelProperty("异步校验证书申请状态taskid")
    private String taskId;

    /**
    * task过期时间
    */
    @ApiModelProperty("task过期时间")
    private Date taskExpireTime;

    /**
    * 证书申请状态：1 新申请 ，2 申请中， 3 超时，  4 申请失败，  5 成功，   0 taskId 不存在或已过期，  -1 无效的申请（数据库无此值）， -2 系统异常
    */
    @ApiModelProperty("证书申请状态：1 新申请 ，2 申请中， 3 超时，  4 申请失败，  5 成功，   0 taskId 不存在或已过期，  -1 无效的申请（数据库无此值）， -2 系统异常")
    private Integer certApplyStatus;

    /**
    * 申请结果message
    */
    @ApiModelProperty("申请结果message")
    private String message;

    /**
    * 0待处理，1处理完成，2证书已回填
    */
    @ApiModelProperty("0待处理，1处理完成，2证书已回填")
    private Integer status;

    /**
    * 申请错误次数
    */
    @ApiModelProperty("申请错误次数")
    private Integer applyErrorCount;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}