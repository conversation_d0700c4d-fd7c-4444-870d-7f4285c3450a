package com.logistics.tms.biz.attendancerecord.handle;

import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.yelo.tray.core.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class AttendancePunchHandleFactory {

    private final List<AttendancePunchHandle> attendancePunchHandles;

    public AttendancePunchHandle getHandle(Integer punchType) {
        return attendancePunchHandles.stream()
                .filter(f -> f.filter(punchType))
                .findFirst()
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR));
    }
}
