package com.logistics.management.webapi.client.vehicleassetmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/6/3 11:17
 */
@Data
public class FuzzyQueryVehicleInfoResponseModel {
    @ApiModelProperty("车辆ID")
    private Long vehicleId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("挂车号")
    private String trailerVehicleNo;
    @ApiModelProperty("车辆机构 1 自主，2 外部，3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("车辆类别：车辆类别 1 牵引车 2 挂车 3 一体车")
    private Integer vehicleCategory;
    @ApiModelProperty("车辆类型")
    private Long vehicleType;
    @ApiModelProperty("车辆运营状态：1 运营中，2 已停运，3 过户，4 报废")
    private Integer operatingState;
    @ApiModelProperty("装载量（可装载托盘数）")
    private Integer loadingCapacity;

    @ApiModelProperty("车辆行驶证表id")
    private Long drivingLicenseId;

    @ApiModelProperty("品牌")
    private String brand;
    @ApiModelProperty("型号")
    private String model;
    @ApiModelProperty("车辆识别号")
    private String vehicleIdentificationNumber;
    @ApiModelProperty("发动机号码")
    private String engineNumber;
    @ApiModelProperty("车身颜色")
    private String bodyColor;

}
