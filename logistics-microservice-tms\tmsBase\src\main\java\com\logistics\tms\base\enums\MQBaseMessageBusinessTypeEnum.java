package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2023/6/16 10:17
 */
public enum MQBaseMessageBusinessTypeEnum {
    DELIVER(1,"履约单（销售发运需求）"),
    TRANSFERS(2,"调拨链路"),
    RECYCLE(3,"回收"),
    ;

    private final Integer key;
    private final String value;

    MQBaseMessageBusinessTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static MQBaseMessageBusinessTypeEnum getEnum(Integer key) {
        for (MQBaseMessageBusinessTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
