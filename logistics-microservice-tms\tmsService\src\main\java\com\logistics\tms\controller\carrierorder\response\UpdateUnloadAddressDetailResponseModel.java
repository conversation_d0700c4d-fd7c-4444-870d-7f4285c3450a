package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2021/9/17 15:10
 */
@Data
public class UpdateUnloadAddressDetailResponseModel {

    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("客户单号")
    private String customerOrderCode;

    @ApiModelProperty("卸货省")
    private String unloadProvinceName;

    @ApiModelProperty("卸货市")
    private String unloadCityName;

    @ApiModelProperty("卸货区")
    private String unloadAreaName;

    @ApiModelProperty("卸货详细地址")
    private String unloadDetailAddress;

    @ApiModelProperty("卸货仓库")
    private String unloadWarehouse;

    @ApiModelProperty("地址表id")
    private Long carrierOrderAddressId;//地址表id

    @ApiModelProperty("入库状态：0:未入库,3 已入库")
    private Integer stockInState;

    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;
    @ApiModelProperty("委托单来源：1 乐医托盘委托单，2 委托方发单，3 中石化系统，4 扬巴邮件，5 乐橘新生")
    private Integer demandOrderSource;
}
