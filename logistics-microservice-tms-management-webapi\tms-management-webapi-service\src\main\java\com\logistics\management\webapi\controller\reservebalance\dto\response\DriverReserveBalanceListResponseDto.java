package com.logistics.management.webapi.controller.reservebalance.dto.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class DriverReserveBalanceListResponseDto {

    @ApiModelProperty(value = "余额ID")
    private String reserveBalanceId;

    @ApiModelProperty(value = "机构, 1 自主 2 外包 3 自营")
    @ExcelProperty(value = "机构")
    private String driverPropertyLabel = "";

    @ApiModelProperty(value = "司机, 姓名 + 手机号")
    @ExcelProperty(value = "司机")
    private String driverName = "";

    @ApiModelProperty(value = "收款账户")
    @ExcelProperty(value = "收款账户")
    private String bankAccount = "";

    @ApiModelProperty(value = "余额")
    @ExcelProperty(value = "余额（元）")
    private String balanceAmount = "0";

    @ApiModelProperty(value = "待核销金额")
    @ExcelProperty(value = "核销中金额（元）")
    private String awaitVerificationAmount = "0";

    @ApiModelProperty(value = "已冲销金额")
    @ExcelProperty(value = "已冲销（元）")
    private String verificationAmount = "0";
}
