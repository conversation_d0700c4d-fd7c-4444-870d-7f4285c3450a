package com.logistics.management.webapi.api.feign.insuarance.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/12/26 16:45
 */
@Data
public class GetInsuranceInfoByVehicleIdDto {
    @ApiModelProperty(value = "保单Id")
    private String insuranceId="";
    @ApiModelProperty(value = "保单号")
    private String policyNo="";
    @ApiModelProperty(value = "险种 1 商业险 2 交强险 3 个人意外险 4货物险 5 危货承运人险")
    private String insuranceType="";
    @ApiModelProperty(value = "总金额")
    private String totalCost="";
    @ApiModelProperty(value = "已扣除费用")
    private String payCost="";
}
