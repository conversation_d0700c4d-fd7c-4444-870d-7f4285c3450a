package com.logistics.tms.api.feign.entrustsettlement.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/10/11 18:58
 */
@Data
public class EntrustSettlementListRequestModel extends AbstractPageForm<EntrustSettlementListRequestModel> {
    @ApiModelProperty("结算状态 '' 全部 0 未收款 1 已收款")
    private Integer settlementStatus;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("委托方,逗号分隔")
    private String companyEntrustIds;
    @ApiModelProperty("报价类型 1 单价  2 一口价")
    private Integer contractPriceType;
    @ApiModelProperty("发布时间")
    private String publishTimeStart;
    private String publishTimeEnd;
    @ApiModelProperty("品名")
    private String goodsName;
    @ApiModelProperty("规格")
    private String goodsSize;
    @ApiModelProperty("发货地址")
    private String loadAddress;
    @ApiModelProperty("卸货地址")
    private String unloadAddress;
    @ApiModelProperty("提卸货联系人")
    private String contactPerson;
    @ApiModelProperty("结算时间 2019-02-03 12:22:22")
    private String settlementTimeStart;
    private String settlementTimeEnd;

    private String settlementIds;//结算ID，逗号分隔
}
