<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TRenewableAuditMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TRenewableAudit">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="demand_order_id" jdbcType="BIGINT" property="demandOrderId" />
    <result column="demand_order_code" jdbcType="VARCHAR" property="demandOrderCode" />
    <result column="renewable_order_code" jdbcType="VARCHAR" property="renewableOrderCode" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="goods_amount_total" jdbcType="DECIMAL" property="goodsAmountTotal" />
    <result column="verified_goods_amount_total" jdbcType="DECIMAL" property="verifiedGoodsAmountTotal" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="staff_property" jdbcType="INTEGER" property="staffProperty" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
    <result column="staff_mobile" jdbcType="VARCHAR" property="staffMobile" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_user_name" jdbcType="VARCHAR" property="customerUserName" />
    <result column="customer_user_mobile" jdbcType="VARCHAR" property="customerUserMobile" />
    <result column="publish_user_name" jdbcType="VARCHAR" property="publishUserName" />
    <result column="publish_user_mobile" jdbcType="VARCHAR" property="publishUserMobile" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="auditor_name" jdbcType="VARCHAR" property="auditorName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, demand_order_id, demand_order_code, renewable_order_code, status, business_type, 
    source, goods_amount_total, verified_goods_amount_total, vehicle_id, vehicle_no, 
    staff_id, staff_property, staff_name, staff_mobile, publish_time, customer_name, 
    customer_user_name, customer_user_mobile, publish_user_name, publish_user_mobile, 
    audit_time, auditor_name, remark, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_renewable_audit
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_renewable_audit
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TRenewableAudit">
    insert into t_renewable_audit (id, demand_order_id, demand_order_code, 
      renewable_order_code, status, business_type, 
      source, goods_amount_total, verified_goods_amount_total, 
      vehicle_id, vehicle_no, staff_id, 
      staff_property, staff_name, staff_mobile, 
      publish_time, customer_name, customer_user_name, 
      customer_user_mobile, publish_user_name, publish_user_mobile, 
      audit_time, auditor_name, remark, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{demandOrderId,jdbcType=BIGINT}, #{demandOrderCode,jdbcType=VARCHAR}, 
      #{renewableOrderCode,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{businessType,jdbcType=INTEGER}, 
      #{source,jdbcType=INTEGER}, #{goodsAmountTotal,jdbcType=DECIMAL}, #{verifiedGoodsAmountTotal,jdbcType=DECIMAL}, 
      #{vehicleId,jdbcType=BIGINT}, #{vehicleNo,jdbcType=VARCHAR}, #{staffId,jdbcType=BIGINT}, 
      #{staffProperty,jdbcType=INTEGER}, #{staffName,jdbcType=VARCHAR}, #{staffMobile,jdbcType=VARCHAR}, 
      #{publishTime,jdbcType=TIMESTAMP}, #{customerName,jdbcType=VARCHAR}, #{customerUserName,jdbcType=VARCHAR}, 
      #{customerUserMobile,jdbcType=VARCHAR}, #{publishUserName,jdbcType=VARCHAR}, #{publishUserMobile,jdbcType=VARCHAR}, 
      #{auditTime,jdbcType=TIMESTAMP}, #{auditorName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TRenewableAudit">
    insert into t_renewable_audit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="demandOrderId != null">
        demand_order_id,
      </if>
      <if test="demandOrderCode != null">
        demand_order_code,
      </if>
      <if test="renewableOrderCode != null">
        renewable_order_code,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="goodsAmountTotal != null">
        goods_amount_total,
      </if>
      <if test="verifiedGoodsAmountTotal != null">
        verified_goods_amount_total,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleNo != null">
        vehicle_no,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="staffProperty != null">
        staff_property,
      </if>
      <if test="staffName != null">
        staff_name,
      </if>
      <if test="staffMobile != null">
        staff_mobile,
      </if>
      <if test="publishTime != null">
        publish_time,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerUserName != null">
        customer_user_name,
      </if>
      <if test="customerUserMobile != null">
        customer_user_mobile,
      </if>
      <if test="publishUserName != null">
        publish_user_name,
      </if>
      <if test="publishUserMobile != null">
        publish_user_mobile,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="auditorName != null">
        auditor_name,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="demandOrderId != null">
        #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderCode != null">
        #{demandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="renewableOrderCode != null">
        #{renewableOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="goodsAmountTotal != null">
        #{goodsAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="verifiedGoodsAmountTotal != null">
        #{verifiedGoodsAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffProperty != null">
        #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="staffName != null">
        #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null">
        #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="publishTime != null">
        #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerUserName != null">
        #{customerUserName,jdbcType=VARCHAR},
      </if>
      <if test="customerUserMobile != null">
        #{customerUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="publishUserName != null">
        #{publishUserName,jdbcType=VARCHAR},
      </if>
      <if test="publishUserMobile != null">
        #{publishUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditorName != null">
        #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TRenewableAudit">
    update t_renewable_audit
    <set>
      <if test="demandOrderId != null">
        demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderCode != null">
        demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="renewableOrderCode != null">
        renewable_order_code = #{renewableOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="goodsAmountTotal != null">
        goods_amount_total = #{goodsAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="verifiedGoodsAmountTotal != null">
        verified_goods_amount_total = #{verifiedGoodsAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffProperty != null">
        staff_property = #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="staffName != null">
        staff_name = #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null">
        staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="publishTime != null">
        publish_time = #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerUserName != null">
        customer_user_name = #{customerUserName,jdbcType=VARCHAR},
      </if>
      <if test="customerUserMobile != null">
        customer_user_mobile = #{customerUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="publishUserName != null">
        publish_user_name = #{publishUserName,jdbcType=VARCHAR},
      </if>
      <if test="publishUserMobile != null">
        publish_user_mobile = #{publishUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditorName != null">
        auditor_name = #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TRenewableAudit">
    update t_renewable_audit
    set demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
      renewable_order_code = #{renewableOrderCode,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      business_type = #{businessType,jdbcType=INTEGER},
      source = #{source,jdbcType=INTEGER},
      goods_amount_total = #{goodsAmountTotal,jdbcType=DECIMAL},
      verified_goods_amount_total = #{verifiedGoodsAmountTotal,jdbcType=DECIMAL},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      staff_id = #{staffId,jdbcType=BIGINT},
      staff_property = #{staffProperty,jdbcType=INTEGER},
      staff_name = #{staffName,jdbcType=VARCHAR},
      staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      publish_time = #{publishTime,jdbcType=TIMESTAMP},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_user_name = #{customerUserName,jdbcType=VARCHAR},
      customer_user_mobile = #{customerUserMobile,jdbcType=VARCHAR},
      publish_user_name = #{publishUserName,jdbcType=VARCHAR},
      publish_user_mobile = #{publishUserMobile,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      auditor_name = #{auditorName,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>