package com.logistics.management.webapi.base.enums;

/**
 * @author: wjf
 * @date: 2019/11/4 17:39
 */
public enum StudyStatusEnum {
    NOT_STUDY(0,"未学习"),
    STUDY(1,"已学习"),
    ;

    private Integer key;
    private String value;

    StudyStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static StudyStatusEnum getEnum(Integer key) {
        for (StudyStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
