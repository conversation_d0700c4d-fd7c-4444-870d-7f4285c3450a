package com.logistics.tms.api.feign.terminalreachmanagement;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.terminalreachmanagement.hystrix.ReachManagementServiceApiHystrix;
import com.logistics.tms.api.feign.terminalreachmanagement.model.GetReachManagementDetailRequestModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.GetReachManagementDetailResponseModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.SearchReachManagementListRequestModel;
import com.logistics.tms.api.feign.terminalreachmanagement.model.SearchReachManagementListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@Api(value = "API-触达管理")
@FeignClient(name = "logistics-tms-services", fallback = ReachManagementServiceApiHystrix.class)
public interface ReachManagementServiceApi {

    @ApiOperation(value = "司机触达任务需求-终端客户管理列表", tags = "v1.1.5")
    @PostMapping(value = "/service/reachManagement/searchReachManagementList")
    Result<PageInfo<SearchReachManagementListResponseModel>> searchReachManagementList(@RequestBody SearchReachManagementListRequestModel requestModel);

    @ApiOperation(value = "司机触达任务需求-终端客户管理详情", tags = "v1.1.5")
    @PostMapping(value = "/service/reachManagement/getReachManagementDetail")
    Result<GetReachManagementDetailResponseModel> getReachManagementDetail(@RequestBody @Valid GetReachManagementDetailRequestModel requestModel);

}
