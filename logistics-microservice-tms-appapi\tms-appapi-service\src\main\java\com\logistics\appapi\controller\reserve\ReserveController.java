package com.logistics.appapi.controller.reserve;


import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.client.reserveapply.DriverReserveApplyClient;
import com.logistics.appapi.client.reserveapply.request.ApplyReserveBalanceRequestModel;
import com.logistics.appapi.client.reserveapply.request.ReserveBalanceApplyCancelRequestModel;
import com.logistics.appapi.client.reserveapply.request.ReserveBalanceApplyDetailRequestModel;
import com.logistics.appapi.client.reserveapply.request.ReserveBalanceApplyListRequestModel;
import com.logistics.appapi.client.reserveapply.response.ReserveBalanceApplyDetailResponseModel;
import com.logistics.appapi.client.reserveapply.response.ReserveBalanceApplyListResponseModel;
import com.logistics.appapi.client.reservebalance.DriverReserveBalanceClient;
import com.logistics.appapi.client.reservebalance.response.ReserveBalanceInfoResponseModel;
import com.logistics.appapi.controller.reserve.mapping.ReserveBalanceApplyDetailMapping;
import com.logistics.appapi.controller.reserve.mapping.ReserveBalanceApplyListMapping;
import com.logistics.appapi.controller.reserve.mapping.ReserveBalanceInfoMapping;
import com.logistics.appapi.controller.reserve.request.ApplyReserveBalanceRequestDto;
import com.logistics.appapi.controller.reserve.request.ReserveBalanceApplyCancelRequestDto;
import com.logistics.appapi.controller.reserve.request.ReserveBalanceApplyDetailRequestDto;
import com.logistics.appapi.controller.reserve.request.ReserveBalanceApplyListRequestDto;
import com.logistics.appapi.controller.reserve.response.ReserveBalanceApplyDetailResponseDto;
import com.logistics.appapi.controller.reserve.response.ReserveBalanceApplyListResponseDto;
import com.logistics.appapi.controller.reserve.response.ReserveBalanceInfoResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Api(value = "备用金", tags = "备用金")
@RestController
@RequestMapping(value = "/api/driverApplet/reserve")
public class ReserveController {

	@Resource
	private DriverReserveApplyClient reserveApplyClient;
	@Resource
	private DriverReserveBalanceClient driverReserveBalanceClient;

	/**
	 * 获取当前的备用金额余额
	 *
	 * @return 备用金余额
	 */
	@ApiOperation(value = "当前备用金信息查询", tags = "1.0.8")
	@PostMapping(value = "/reserveBalanceInfo")
	public Result<ReserveBalanceInfoResponseDto> reserveBalanceInfo() {
		Result<ReserveBalanceInfoResponseModel> responseModelResult = driverReserveBalanceClient.reserveBalanceInfo();
		responseModelResult.throwException();

		ReserveBalanceInfoResponseDto responseDto = MapperUtils.mapper(responseModelResult.getData(), ReserveBalanceInfoResponseDto.class,
				new ReserveBalanceInfoMapping());
		return Result.success(responseDto);
	}

	/**
	 * 申请/重新申请备用金
	 *
	 * @param requestDto 申请信息
	 * @return 操作结果
	 */
	@ApiOperation(value = "申请备用金/重新申请", tags = "1.0.8")
	@PostMapping(value = "/applyReserveBalance")
	@IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
	public Result<Boolean> applyReserveBalance(@RequestBody @Valid ApplyReserveBalanceRequestDto requestDto) {
		ApplyReserveBalanceRequestModel requestModel = MapperUtils.mapper(requestDto, ApplyReserveBalanceRequestModel.class);
		return reserveApplyClient.applyReserveBalance(requestModel);
	}

	/**
	 * 申请记录列表查询
	 *
	 * @param requestDto id
	 * @return 申请记录列表
	 */
	@ApiOperation(value = "备用金额申请记录列表", tags = "1.0.8")
	@PostMapping(value = "/reserveBalanceApplyList")
	public Result<ReserveBalanceApplyListResponseDto> reserveBalanceApplyList(@RequestBody @Valid ReserveBalanceApplyListRequestDto requestDto) {
		ReserveBalanceApplyListRequestModel requestModel = MapperUtils.mapper(requestDto, ReserveBalanceApplyListRequestModel.class);
		Result<ReserveBalanceApplyListResponseModel> responseModelResult = reserveApplyClient.reserveBalanceApplyList(requestModel);
		responseModelResult.throwException();

		ReserveBalanceApplyListResponseDto responseDto = MapperUtils.mapper(responseModelResult.getData(), ReserveBalanceApplyListResponseDto.class,
				new ReserveBalanceApplyListMapping());
		return Result.success(responseDto);
	}

	/**
	 * 申请记录详情查询
	 *
	 * @param requestDto id
	 * @return 详情
	 */
	@ApiOperation(value = "申请记录详情", tags = "1.0.8")
	@PostMapping(value = "/reserveBalanceApplyDetail")
	public Result<ReserveBalanceApplyDetailResponseDto> reserveBalanceApplyDetail(@RequestBody @Valid ReserveBalanceApplyDetailRequestDto requestDto) {
		ReserveBalanceApplyDetailRequestModel requestModel = MapperUtils.mapper(requestDto, ReserveBalanceApplyDetailRequestModel.class);
		Result<ReserveBalanceApplyDetailResponseModel> responseModelResult = reserveApplyClient.reserveBalanceApplyDetail(requestModel);
		responseModelResult.throwException();

		ReserveBalanceApplyDetailResponseDto responseDto = MapperUtils.mapper(responseModelResult.getData(), ReserveBalanceApplyDetailResponseDto.class,
				new ReserveBalanceApplyDetailMapping());
		return Result.success(responseDto);
	}

	/**
	 * 撤销申请记录
	 *
	 * @param requestDto id
	 * @return 操作结果
	 */
	@ApiOperation(value = "撤销申请记录", tags = "1.0.8")
	@PostMapping(value = "/cancel")
	@IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
	public Result<Boolean> cancel(@RequestBody @Valid ReserveBalanceApplyCancelRequestDto requestDto) {
		ReserveBalanceApplyCancelRequestModel requestModel = MapperUtils.mapper(requestDto, ReserveBalanceApplyCancelRequestModel.class);
		return reserveApplyClient.cancel(requestModel);
	}
}
