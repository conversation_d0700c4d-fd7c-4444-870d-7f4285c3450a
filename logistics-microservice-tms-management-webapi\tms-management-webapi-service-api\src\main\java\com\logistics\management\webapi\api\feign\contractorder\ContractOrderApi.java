package com.logistics.management.webapi.api.feign.contractorder;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.contractorder.dto.*;
import com.logistics.management.webapi.api.feign.contractorder.hystrix.ContractOrderApiHystrix;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;


@Api(value = "API-ContractOrderApi-合同管理")
@FeignClient(name = "logistics-tms-managementWeb-api",fallback = ContractOrderApiHystrix.class)
public interface ContractOrderApi {
    @ApiOperation(value = "获取合同列表")
    @PostMapping(value = "/api/contractOrder/searchList")
    Result<PageInfo<ContractOrderSearchResponseDto>> searchContractOrderList(@RequestBody ContractOrderSearchRequestDto requestDto);

    @ApiOperation(value = "获取合同详情")
    @PostMapping(value = "/api/contractOrder/getDetail")
    Result<ContractOrderDetailResponseDto> getDetail(@RequestBody @Valid ContractOrderDetailRequestDto requestDto);

    @ApiOperation(value = "新增修改合同信息")
    @PostMapping(value = "/api/contractOrder/saveContract")
    Result saveContract(@RequestBody @Valid AddOrModifyContractOrderRequestDto requestDto);

    @ApiOperation(value = "作废或终止合同")
    @PostMapping(value = "/api/contractOrder/terminateOrCancelContract")
    Result terminateOrCancelContract(@RequestBody @Valid TerminateOrCancelContractRequestDto requestDto);

    @ApiOperation(value = "导出合同")
    @GetMapping(value = "/api/contractOrder/exportContractOrder")
    void exportContractOrder(ContractOrderSearchRequestDto requestDto, HttpServletResponse response);

}
