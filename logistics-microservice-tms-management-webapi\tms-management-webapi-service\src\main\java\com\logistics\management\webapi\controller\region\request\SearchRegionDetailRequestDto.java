package com.logistics.management.webapi.controller.region.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2021/10/18 15:50
 */
@Data
public class SearchRegionDetailRequestDto extends AbstractPageForm<SearchRegionDetailRequestDto> {
    @ApiModelProperty("大区ID")
    @NotBlank(message = "大区ID为空")
    private String regionId;
    @ApiModelProperty("省份名称")
    private String provinceName;
    @ApiModelProperty("市区名称")
    private String cityName;

}
