package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2023/09/11
*/
@Data
public class TSettleStatement extends BaseEntity {
    /**
    * 对账单号
    */
    @ApiModelProperty("对账单号")
    private String settleStatementCode;

    /**
    * 对账单类型：1 包装业务，2 自营业务
    */
    @ApiModelProperty("对账单类型：1 包装业务，2 自营业务")
    private Integer settleStatementType;

    /**
    * 对账单名称
    */
    @ApiModelProperty("对账单名称")
    private String settleStatementName;

    /**
    * 对账月份
    */
    @ApiModelProperty("对账月份")
    private String settleStatementMonth;

    /**
    * 公司角色：1 货主，2 车主
    */
    @ApiModelProperty("公司角色：1 货主，2 车主")
    private Integer companyRole;

    /**
    * 临时费用费点
    */
    @ApiModelProperty("临时费用费点")
    private BigDecimal otherFeeTaxPoint;

    /**
    * 运费费点
    */
    @ApiModelProperty("运费费点")
    private BigDecimal freightTaxPoint;

    /**
    * 申请金额
    */
    @ApiModelProperty("申请金额")
    private BigDecimal applyTotalFee;

    /**
    * 差异调整金额
    */
    @ApiModelProperty("差异调整金额")
    private BigDecimal adjustFee;

    /**
    * 调整理由
    */
    @ApiModelProperty("调整理由")
    private String adjustRemark;

    /**
    * 对账单状态：-2 已撤销，-1 待提交，0 待业务审核 1 待财务审核，2 已对账，3 已驳回
    */
    @ApiModelProperty("对账单状态：-2 已撤销，-1 待提交，0 待业务审核 1 待财务审核，2 已对账，3 已驳回")
    private Integer settleStatementStatus;

    /**
    * 审核人姓名
    */
    @ApiModelProperty("审核人姓名")
    private String auditName;

    /**
    * 审核时间
    */
    @ApiModelProperty("审核时间")
    private Date auditTime;

    /**
    * 平台公司id(结算主体)
    */
    @ApiModelProperty("平台公司id(结算主体)")
    private Long platformCompanyId;

    /**
    * 平台公司名称(结算主体)
    */
    @ApiModelProperty("平台公司名称(结算主体)")
    private String platformCompanyName;

    /**
    * 合同号
    */
    @ApiModelProperty("合同号")
    private String contractCode;

    /**
     * 是否开票：0 否，1 是
     */
    @ApiModelProperty("是否开票：0 否，1 是")
    private Integer ifInvoice;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}