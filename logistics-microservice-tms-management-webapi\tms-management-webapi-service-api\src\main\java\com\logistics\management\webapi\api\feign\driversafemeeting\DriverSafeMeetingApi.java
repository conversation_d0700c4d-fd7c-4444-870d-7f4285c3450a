package com.logistics.management.webapi.api.feign.driversafemeeting;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.driversafemeeting.dto.*;
import com.logistics.management.webapi.api.feign.driversafemeeting.hystrix.DriverSafeMeetingApiHystrix;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/11/1 18:5
 */
@Api(value = "API-DriverSafeMeetingApi-司机安全例会")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = DriverSafeMeetingApiHystrix.class)
public interface DriverSafeMeetingApi {

    @ApiOperation(("例会看板（列表）v1.1.7"))
    @PostMapping(value = "/api/driverSafeMeeting/driverSafeMeetingKanBan")
    Result<List<DriverSafeMeetingKanBanResponseDto>> driverSafeMeetingKanBan(@RequestBody @Valid DriverSafeMeetingKanBanRequestDto requestDto);

    @ApiOperation(("新增例会 v1.0.7"))
    @PostMapping(value = "/api/driverSafeMeeting/addDriverSafeMeeting")
    Result addDriverSafeMeeting(@RequestBody @Valid AddDriverSafeMeetingRequestDto requestDto);

    @ApiOperation(("重新编辑例会"))
    @PostMapping(value = "/api/driverSafeMeeting/modifyDriverSafeMeeting")
    Result modifyDriverSafeMeeting(@RequestBody @Valid ModifyDriverSafeMeetingRequestDto requestDto);

    @ApiOperation(("补发例会"))
    @PostMapping(value = "/api/driverSafeMeeting/replacementDriverSafeMeeting")
    Result replacementDriverSafeMeeting(@RequestBody @Valid ReplacementDriverSafeMeetingRequestDto requestDto);

    @ApiOperation(("例会内容详情"))
    @PostMapping(value = "/api/driverSafeMeeting/driverSafeMeetingContentDetail")
    Result<DriverSafeMeetingContentDetailResponseDto> driverSafeMeetingContentDetail(@RequestBody @Valid DriverSafeMeetingIdRequestDto requestDto);

    @ApiOperation(("导出例会内容详情（pdf）"))
    @GetMapping(value = "/api/driverSafeMeeting/exportDriverSafeMeetingContentDetail")
    void exportDriverSafeMeetingContentDetail(DriverSafeMeetingIdRequestDto requestDto, HttpServletResponse response, HttpServletRequest request);

    @ApiOperation(("学习详情（司机学习列表）v1.1.7"))
    @PostMapping(value = "/api/driverSafeMeeting/driverSafeMeetingDetailList")
    Result<PageInfo<DriverSafeMeetingDetailResponseDto>> driverSafeMeetingDetailList(@RequestBody DriverSafeMeetingDetailRequestDto requestDto);

    @ApiOperation(("学习详情（司机学习列表统计人数）v1.1.7"))
    @PostMapping(value = "/api/driverSafeMeeting/driverSafeMeetingListCount")
    Result<DriverSafeMeetingListCountResponseDto> driverSafeMeetingListCount(@RequestBody DriverSafeMeetingDetailRequestDto requestDto);

    @ApiOperation(("导出学习详情（司机学习列表）"))
    @GetMapping(value = "/api/driverSafeMeeting/exportDriverSafeMeetingDetailList")
    void exportDriverSafeMeetingDetailList(DriverSafeMeetingDetailRequestDto requestDto, HttpServletResponse response);

    @ApiOperation(("删除学习详情"))
    @PostMapping(value = "/api/driverSafeMeeting/delDriverSafeMeetingRelation")
    Result delDriverSafeMeetingRelation(@RequestBody @Valid DriverSafeMeetingRelationIdRequestDto requestDto);

    @ApiOperation(value = "获取年份（2019到当前年份）")
    @PostMapping(value = "/api/driverSafeMeeting/getYear")
    Result<List<String>> getYear();
}
