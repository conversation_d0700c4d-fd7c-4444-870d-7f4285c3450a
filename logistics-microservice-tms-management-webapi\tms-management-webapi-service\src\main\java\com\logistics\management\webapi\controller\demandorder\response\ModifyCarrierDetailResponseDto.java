package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ModifyCarrierDetailResponseDto {

    @ApiModelProperty("需求单ID")
    private String demandOrderId;

    @ApiModelProperty(value = "车主ID")
    private String companyCarrierId;

    @ApiModelProperty(value = "车主名称")
    private String companyCarrierName;

    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
    private String isOurCompany;

    @ApiModelProperty(value = "车主报价")
    private String carrierPrice;

    @ApiModelProperty(value = "价格类型 1 单价 2 一口价")
    private String carrierPriceType;

    @ApiModelProperty(value = "货物数量")
    private String goodsAmount;
}
