package com.logistics.management.webapi.controller.routeconfig.mapping;

import com.alibaba.excel.util.DateUtils;
import com.logistics.management.webapi.base.enums.ConfigLabelEnum;
import com.logistics.management.webapi.client.freightconfig.response.CarrierFreightConfigListResponseModel;
import com.logistics.management.webapi.client.routeconfig.response.RouteDistanceConfigResponseModel;
import com.logistics.management.webapi.controller.freightconfig.response.CarrierFreightConfigListResponseDto;
import com.logistics.management.webapi.controller.routeconfig.response.RouteDistanceConfigResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.util.Objects;

/**
 * @Author: sj
 * @Date: 2019/9/29 9:24
 */
public class RouteDistanceConfigMapping extends MapperMapping<RouteDistanceConfigResponseModel, RouteDistanceConfigResponseDto> {
    @Override
    public void configure() {
        RouteDistanceConfigResponseModel source = this.getSource();
        RouteDistanceConfigResponseDto destination = this.getDestination();
        if(source!=null){

            if(Objects.nonNull(source.getCreateTime())){
                destination.setCreateTime(DateUtils.format(source.getCreateTime()));
            }
            if(Objects.nonNull(source.getLastModifiedTime())){
                destination.setLastModifiedTime(DateUtils.format(source.getLastModifiedTime()));
            }
        }
    }
}
