package com.logistics.management.webapi.controller.uploadfile.response;

import com.yelo.tools.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/6/25 13:09
 */
@Data
public class BillIdentifyResponseDto {

    /**
     * 图片全路径
     */
    @ApiModelProperty("图片全路径")
    private String src = "";

    /**
     * 图片相对路径
     */
    @ApiModelProperty("图片相对路径")
    private String relativePath = "";

    /**
     * 发票类型：1 增值税发票，2 出租车发票，3 火车票，4 定额发票，5 卷票，6 机打发票，7 过路过桥费发票
     */
    @ApiModelProperty("发票类型：1 增值税发票，2 出租车发票，3 火车票，4 定额发票，5 卷票，6 机打发票，7 过路过桥费发票")
    private String type = "";

    /**
     * 发票名称
     */
    @ApiModelProperty("发票名称")
    private String invoiceName = "";

    /**
     * 票据类型
     */
    @ApiModelProperty("票据类型")
    private String invoiceType = "";

    /**
     * 发票代码
     */
    @ApiModelProperty("发票代码")
    private String invoiceCode = "";

    /**
     * 发票号码
     */
    @ApiModelProperty("发票号码")
    private String invoiceNum = "";

    /**
     * 合计金额
     */
    @ApiModelProperty("合计金额")
    private String totalPrice = "";

    /**
     * 合计税额
     */
    @ApiModelProperty("合计税额")
    private String totalTax = "";

    /**
     * 价税合计
     */
    @ApiModelProperty("价税合计")
    private String totalTaxAndPrice = "";

    public String getTotalPrice() {
        return StringUtils.isBlank(totalPrice) ? "0" : totalPrice;
    }

    public String getTotalTax() {
        return StringUtils.isBlank(totalTax) ? "0" : totalTax;
    }
}
