package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class GetDemandOrderInfoByIdsModel {

    private Long demandOrderId;

    private String demandOrderCode;

    private Integer entrustStatus;

    private Integer demandOrderStatus;

    private Integer source;

    private Integer orderType;

    private Integer ifCancel;

    private Integer ifEmpty;

    @ApiModelProperty("是否回退")
    private Integer ifRollback;

    private Integer goodsUnit;

    private BigDecimal arrangedAmount;

    private BigDecimal notArrangedAmount;

    private BigDecimal backAmount;

    private BigDecimal goodsAmount;

    private Integer entrustType;

    private String upstreamCustomer;

    //货主价格
    private Integer contractPriceType;
    private BigDecimal contractPrice;

    private Integer exceptContractPriceType;
    private BigDecimal exceptContractPrice;

    //货主结算方式
    private Integer settlementTonnage;


    //车主价格
    private Integer carrierPriceType;
    private BigDecimal carrierPrice;

    //车主结算方式
    private Integer carrierSettlement;

    //货主联系人
    private Long companyEntrustId;
    private String entrustCompany;


    //地址信息
    private Long loadProvinceId;
    private Long loadCityId;
    private Long loadAreaId;
    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadWarehouse;
    private String loadDetailAddress;
    private String consignorName;
    private String consignorMobile;
    private Long unloadProvinceId;
    private Long unloadCityId;
    private Long unloadAreaId;
    private String unloadWarehouse;
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String receiverName;
    private String receiverMobile;

    private String remark;
    private String fixedDemand;
    @ApiModelProperty("是否自动发布：0 否，1 是")
    private Integer autoPublish;

    @ApiModelProperty("项目标签")
    private String projectLabel;

    private Long companyCarrierId;
    private List<GetDemandOrderGoodsInfoByIdsModel> goodsList;
}
