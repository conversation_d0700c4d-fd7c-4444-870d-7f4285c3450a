<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TStaffBasicMapper" >
    <select id="getByMobile" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_staff_basic
        where valid = 1
        and mobile = #{mobile,jdbcType=VARCHAR} limit 1
    </select>

    <select id="getByIds" resultType="com.logistics.tms.biz.staff.model.StaffIdDriverCredentialIdModel">
        select
        tsb.id as staffId,
        tsdc.id as staffDriverCredentialId
        from t_staff_basic tsb
        left join t_staff_driver_credential tsdc on tsdc.staff_id = tsb.id and tsdc.valid = 1
        where tsb.valid = 1
        and tsb.id in (${ids})
    </select>

    <update id="batchUpdate" parameterType="com.logistics.tms.entity.TStaffBasic">
        <foreach collection="list" item="item" separator=";">
            update t_staff_basic
            <set>
                <if test="item.type != null">
                    type = #{item.type,jdbcType=INTEGER},
                </if>
                <if test="item.staffProperty != null">
                    staff_property = #{item.staffProperty,jdbcType=INTEGER},
                </if>
                <if test="item.gender != null">
                    gender = #{item.gender,jdbcType=INTEGER},
                </if>
                <if test="item.name != null">
                    name = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.mobile != null">
                    mobile = #{item.mobile,jdbcType=VARCHAR},
                </if>
                <if test="item.identityNumber != null">
                    identity_number = #{item.identityNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.age != null">
                    age = #{item.age,jdbcType=INTEGER},
                </if>
                <if test="item.identityValidity != null">
                    identity_validity = #{item.identityValidity,jdbcType=TIMESTAMP},
                </if>
                <if test="item.identityIsForever != null">
                    identity_is_forever = #{item.identityIsForever,jdbcType=INTEGER},
                </if>
                <if test="item.laborContractNo != null">
                    labor_contract_no = #{item.laborContractNo,jdbcType=VARCHAR},
                </if>
                <if test="item.laborContractValidDate != null">
                    labor_contract_valid_date = #{item.laborContractValidDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.openStatus != null">
                    open_status = #{item.openStatus,jdbcType=INTEGER},
                </if>
                <if test="item.closeReason != null">
                    close_reason = #{item.closeReason,jdbcType=VARCHAR},
                </if>
                <if test="item.source != null">
                    source = #{item.source,jdbcType=INTEGER},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.realNameAuthenticationStatus != null">
                    real_name_authentication_status = #{item.realNameAuthenticationStatus,jdbcType=INTEGER},
                </if>
                <if test="item.warehouseSwitch != null">
                    warehouse_switch = #{item.warehouseSwitch,jdbcType=INTEGER},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <resultMap id="getStaffDetail_Map" type="com.logistics.tms.controller.staff.response.GetStaffDetailResponseModel">
        <id column="id" property="staffId" jdbcType="BIGINT"/>
        <result column="staff_property" property="staffProperty" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="gender" property="gender" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="identity_number" property="identityNumber" jdbcType="VARCHAR"/>
        <result column="age" property="age" jdbcType="INTEGER"/>
        <result column="identity_validity" property="identityValidity" jdbcType="TIMESTAMP"/>
        <result column="identity_is_forever" property="identityIsForever" jdbcType="INTEGER"/>
        <result column="labor_contract_no" property="laborContractNo" jdbcType="VARCHAR"/>
        <result column="labor_contract_valid_date" property="laborContractValidDate" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="open_status" property="openStatus" jdbcType="INTEGER" />
        <result column="staffDriverCredentialId" property="staffDriverCredentialId" jdbcType="BIGINT"/>
        <result column="occupational_requirements_credential_no" property="occupationalRequirementsCredentialNo" jdbcType="VARCHAR"/>
        <result column="initial_issuance_date" property="initialIssuanceDate" jdbcType="TIMESTAMP"/>
        <result column="drivers_license_no" property="driversLicenseNo" jdbcType="VARCHAR"/>
        <result column="permitted_type" property="permittedType" jdbcType="VARCHAR"/>
        <result column="drivers_license_date_from" property="driversLicenseDateFrom" jdbcType="TIMESTAMP"/>
        <result column="drivers_license_date_to" property="driversLicenseDateTo" jdbcType="TIMESTAMP"/>
        <collection property="ticketsList" ofType="com.logistics.tms.controller.staff.response.GetStaffTicketsResponseModel">
            <id column="ticketId" property="ticketId" jdbcType="BIGINT"/>
            <result column="file_path" property="filePath" jdbcType="VARCHAR"/>
            <result column="object_type" property="objectType" jdbcType="INTEGER"/>
            <result column="file_type" property="fileType" jdbcType="INTEGER"/>
            <result column="upload_user_name" property="uploadUserName" jdbcType="VARCHAR"/>
            <result column="upload_time" property="uploadTime" jdbcType="TIMESTAMP"/>
        </collection>
    </resultMap>
    <select id="getStaffDetail" resultMap="getStaffDetail_Map">
        select
        tsb.id,
        tsb.type,
        tsb.gender,
        tsb.name,
        tsb.mobile,
        tsb.identity_number,
        tsb.age,
        tsb.identity_validity,
        tsb.identity_is_forever,
        tsb.labor_contract_no,
        tsb.labor_contract_valid_date,
        tsb.remark,
        tsb.staff_property,
        tsb.open_status,
        tsdc.id as staffDriverCredentialId,
        tsdc.occupational_requirements_credential_no,
        tsdc.initial_issuance_date,
        tsdc.drivers_license_no,
        tsdc.permitted_type,
        tsdc.drivers_license_date_from,
        tsdc.drivers_license_date_to,
        tcp.id as ticketId,
        tcp.object_type,
        tcp.file_path,
        tcp.file_type,
        tcp.upload_user_name,
        tcp.upload_time
        from t_staff_basic tsb
        left join t_staff_driver_credential tsdc on tsdc.staff_id = tsb.id and tsdc.valid = 1
        left join t_certification_pictures tcp on ((tcp.object_type = 8 and tcp.object_id = tsb.id) or (tcp.object_type = 9 and tcp.object_id = tsdc.id)) and tcp.valid = 1
        where tsb.valid = 1
        and tsb.id = #{staffId,jdbcType=BIGINT}
    </select>

    <select id="searchStaffRelIdList" resultType="java.lang.Long">
        select
        DISTINCT tcdr.id
        from t_carrier_driver_relation tcdr
        left join t_staff_basic tsb on tsb.valid = 1 and tcdr.driver_id = tsb.id
        left join t_staff_driver_credential tsdc on tsdc.staff_id = tsb.id and tsdc.valid = 1
        where tcdr.valid = 1
        <if test="params.staffProperty != null">
            and tsb.staff_property = #{params.staffProperty,jdbcType = INTEGER}
        </if>
        <if test="params.staffType != null and params.staffType != ''">
            and tsb.type in (${params.staffType})
        </if>
        <if test="params.openStatus != null">
            <if test="params.openStatus == 1">
                and tcdr.enabled = 1
            </if>
            <if test="params.openStatus == 2">
                and tcdr.enabled = 0
            </if>
        </if>
        <if test="params.occupationalRequirementsCredentialNo != null and params.occupationalRequirementsCredentialNo != ''">
            and instr(tsdc.occupational_requirements_credential_no, #{params.occupationalRequirementsCredentialNo,jdbcType=VARCHAR})
        </if>
        <if test="params.staffName != null and params.staffName != ''">
            and (instr(tsb.name, #{params.staffName,jdbcType=VARCHAR}) or instr(tsb.mobile, #{params.staffName,jdbcType=VARCHAR}))
        </if>
        <if test="params.lastModifiedBy != null and params.lastModifiedBy != ''">
            and instr(tsb.last_modified_by, #{params.lastModifiedBy,jdbcType=VARCHAR})
        </if>
        <if test="params.lastModifiedTimeStart != null and params.lastModifiedTimeStart != ''">
            and tsb.last_modified_time &gt;= DATE_FORMAT(#{params.lastModifiedTimeStart,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%S')
        </if>
        <if test="params.lastModifiedTimeEnd != null and params.lastModifiedTimeEnd != ''">
            and tsb.last_modified_time &lt;= DATE_FORMAT(#{params.lastModifiedTimeEnd,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="params.staffIds != null and params.staffIds != ''">
            and tsb.id in (${params.staffIds})
        </if>
        <if test="params.ids != null and params.ids != ''">
            and tcdr.id in (${params.ids})
        </if>
        <if test="companyIds != null and companyIds.size() > 0">
            and tcdr.company_carrier_id in
            <foreach collection="companyIds" item="companyId" separator="," open="(" close=")">
                #{companyId}
            </foreach>
        </if>
        order by tsb.last_modified_time desc, tsb.id desc
    </select>
    <resultMap id="searchStaffList_Map" type="com.logistics.tms.controller.staff.response.SearchStaffManagementListResponseModel">
        <id column="id" property="staffId" jdbcType="BIGINT"/>
        <result column="staff_property" property="staffProperty" jdbcType="INTEGER"/>
        <result column="open_status" property="openStatus" jdbcType="INTEGER"/>
        <result column="type" property="staffType" jdbcType="INTEGER"/>
        <result column="gender" property="gender" jdbcType="INTEGER"/>
        <result column="occupational_requirements_credential_no" property="occupationalRequirementsCredentialNo" jdbcType="VARCHAR"/>
        <result column="name" property="staffName" jdbcType="VARCHAR"/>
        <result column="mobile" property="staffMobile" jdbcType="VARCHAR"/>
        <result column="permitted_type" property="permittedType" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR"/>
        <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP"/>
        <result column="ifComplete" property="ifComplete" jdbcType="INTEGER"/>
    </resultMap>
    <select id="searchStaffList" resultMap="searchStaffList_Map">
        select
        tsb.id,
        tsb.type,
        tsb.gender,
        tsb.name,
        tsb.mobile,
        tsb.remark,
        tsb.created_by,
        tsb.last_modified_by,
        tsb.last_modified_time,
        tsb.staff_property,
        tsb.open_status,
        tsdc.occupational_requirements_credential_no,
        tsdc.permitted_type,
        if(count(t1.id)>0 and count(t2.id)>0 and count(t3.id)>0,1,0) as ifComplete
        from t_staff_basic tsb
        left join t_staff_driver_credential tsdc on tsdc.staff_id = tsb.id and tsdc.valid = 1
        left join t_staff_driver_continue_learning_record t1 on t1.staff_id = tsb.id and t1.valid = 1
        left join t_staff_driver_integrity_examination_record t2 on t2.staff_id = tsb.id and t2.valid = 1
        left join t_staff_driver_occupational_record t3 on t3.staff_id = tsb.id and t3.valid = 1
        where tsb.valid = 1
        and tsb.id in (${ids})
        group by tsb.id
        order by tsb.last_modified_time desc,tsb.id desc
    </select>
    <select id="getStaffContinueLearningComplete" resultType="com.logistics.tms.biz.staff.model.GetStaffIfCompleteByIdsModel">
        select
        tsb.id as staffId,
        if(count(t1.id)>0,1,0) as ifComplete
        from t_staff_basic tsb
        left join t_staff_driver_continue_learning_record t1 on t1.staff_id = tsb.id and t1.valid = 1
        where tsb.valid = 1
        and tsb.id in (${ids})
        group by tsb.id
    </select>
    <select id="getStaffIntegrityExaminationComplete" resultType="com.logistics.tms.biz.staff.model.GetStaffIfCompleteByIdsModel">
        select
        tsb.id as staffId,
        if(count(t2.id)>0 ,1,0) as ifComplete
        from t_staff_basic tsb
        left join t_staff_driver_integrity_examination_record t2 on t2.staff_id = tsb.id and t2.valid = 1
        where tsb.valid = 1
        and tsb.id in (${ids})
        group by tsb.id
    </select>
    <select id="getStaffOccupationalComplete" resultType="com.logistics.tms.biz.staff.model.GetStaffIfCompleteByIdsModel">
        select
        tsb.id as staffId,
        if(count(t3.id)>0,1,0) as ifComplete
        from t_staff_basic tsb
        left join t_staff_driver_occupational_record t3 on t3.staff_id = tsb.id and t3.valid = 1
        where tsb.valid = 1
        and tsb.id in (${ids})
        group by tsb.id
    </select>

    <select id="exportStaffList"
            resultType="com.logistics.tms.controller.staff.response.ExportStaffManagementListResponseModel">
        select
        tcdr.company_carrier_id                      as companyCarrierId,
        tsb.id                                       as staffId,
        tsb.staff_property                           as staffProperty,
        tsb.type                                     as staffType,
        tsb.gender,
        tsb.name                                     as staffName,
        tsb.mobile                                   as staffMobile,
        tsb.identity_number                          as identityNumber,
        tsb.age,
        tsb.identity_validity                        as identityValidity,
        tsb.identity_is_forever                      as identityIsForever,
        tsb.labor_contract_no                        as laborContractNo,
        tsb.labor_contract_valid_date                as laborContractValidDate,
        tsdc.occupational_requirements_credential_no as occupationalRequirementsCredentialNo,
        tsdc.initial_issuance_date                   as initialIssuanceDate,
        tsdc.drivers_license_no                      as driversLicenseNo,
        tsdc.permitted_type                          as permittedType,
        tsdc.drivers_license_date_from               as driversLicenseDateFrom,
        tsdc.drivers_license_date_to                 as driversLicenseDateTo,
        tsb.remark,
        tsb.real_name_authentication_status          as realNameAuthenticationStatus,
        tsb.created_by                               as createdBy,
        tsb.last_modified_by                         as lastModifiedBy,
        tsb.last_modified_time                       as lastModifiedTime
        from t_carrier_driver_relation tcdr
        left join t_staff_basic tsb on tsb.valid = 1 and tcdr.driver_id = tsb.id
        left join t_staff_driver_credential tsdc on tsdc.staff_id = tsb.id and tsdc.valid = 1
        where tcdr.valid = 1
          and tcdr.id in (${ids})
        order by tsb.last_modified_time desc, tsb.id desc
    </select>

    <select id="getDriverStatics" resultType="com.logistics.tms.controller.vehicleassetmanagement.response.AssetsBoardResponseModel">
        select
        ifnull(sum(if(tsb.type = 1 and tsb.staff_property = 1, 1, 0)), 0)                   as driverCount,
        ifnull(sum(if(tsb.type = 2 and tsb.staff_property = 1, 1, 0)), 0)                   as superCargoCount,
        ifnull(sum(if(tsb.type = 3 and tsb.staff_property = 1, 1, 0)), 0)                   as driverAndSuperCargoCount,
        ifnull(sum(if((tsb.type = 1 or tsb.type = 3) and tsb.staff_property = 2, 1, 0)), 0) as extDriverCount
        from t_carrier_driver_relation tcdr
        left join t_staff_basic tsb on tsb.valid = 1 and tcdr.driver_id = tsb.id and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        where tcdr.valid = 1
        and tcdr.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
    </select>

    <select id="getDueIdentityCount" resultType="java.util.HashMap">
        select
        ifnull(group_concat(if(thrityDueCount = 1, id, null)), '') as totalIds,
        ifnull(group_concat(if(sevenDueCount = 1, id, null)), '')  as weekIds,
        ifnull(group_concat(if(dueCount = 1, id, null)), '')       as hasExpiredIds,
        ifnull(sum(thrityDueCount), 0)                             as total,
        ifnull(sum(sevenDueCount), 0)                              as week,
        ifnull(sum(dueCount), 0)                                   as hasExpired
        from (SELECT tcdr.id,
              if(DATE_SUB(max(identity_validity), INTERVAL #{remindDays,jdbcType=INTEGER} DAY) &lt;= DATE_FORMAT(now(), '%Y-%m-%d') and max(identity_validity) >= DATE_FORMAT(now(), '%Y-%m-%d'),
                 1, 0)                                                                                                                                                     as thrityDueCount,
              if(DATE_SUB(max(identity_validity), INTERVAL 7 DAY) &lt;= DATE_FORMAT(now(), '%Y-%m-%d') and max(identity_validity) >= DATE_FORMAT(now(), '%Y-%m-%d'), 1, 0) as sevenDueCount,
              if(max(identity_validity) &lt; DATE_FORMAT(now(), '%Y-%m-%d'), 1, 0)                                                                                         as dueCount
              from t_staff_basic tsb
              left join t_carrier_driver_relation tcdr on tcdr.valid = 1 and tcdr.driver_id = tsb.id and tcdr.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
              where tsb.valid = 1
              and tcdr.id is not null
              group by id) tmp
    </select>

    <select id="getByName" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_staff_basic
        where valid = 1 and name = #{name,jdbcType=VARCHAR}
    </select>

    <select id="getCountByName" resultType="java.lang.Integer">
        select count(0)
        from t_staff_basic
        where valid = 1 and name = #{name,jdbcType=VARCHAR}
    </select>

    <select id="fuzzyQueryDriverInfo" resultType="com.logistics.tms.api.feign.violationregulation.model.FuzzyQueryDriverInfoResponseModel">
        select
        tqsb.staff_property as staffProperty,
        tqsb.id as driverId,
        tqsb.name as driverName,
        tqsb.mobile as driverPhone,
        tqsb.identity_number as identityNumber
        from t_staff_basic tqsb
        where tqsb.valid =1
        and tqsb.type in (1,3)
        <if test="fuzzyDriverField!=null and fuzzyDriverField!=''">
            and (instr(tqsb.name,#{fuzzyDriverField,jdbcType = VARCHAR})
            or instr(tqsb.mobile,#{fuzzyDriverField,jdbcType = VARCHAR}))
        </if>
    </select>

    <select id="fuzzyQueryDriverMessage" resultType="com.logistics.tms.api.feign.violationregulation.model.FuzzyQueryDriverInfoResponseModel">
        select distinct
        tsb.id as driverId,
        tsb.name as driverName,
        tsb.mobile as driverPhone,
        tsb.identity_number as identityNumber
        from t_staff_basic tsb
        <if test="params.enabled != null">
        left join t_carrier_driver_relation tcdr on tcdr.driver_id = tsb.id and tcdr.valid = 1
        </if>
        where tsb.valid =1
        and tsb.type in (1,3)
        <if test="params.type != null and params.type != ''">
            and tsb.staff_property in (${params.type})
        </if>
        <if test="params.fuzzyDriverField!=null and params.fuzzyDriverField!=''">
            and (instr(tsb.name,#{params.fuzzyDriverField,jdbcType = VARCHAR})
            or instr(tsb.mobile,#{params.fuzzyDriverField,jdbcType = VARCHAR}))
        </if>
        <if test="params.enabled != null">
            and tcdr.enabled = #{params.enabled,jdbcType = INTEGER}
        </if>
    </select>

    <select id="getStaffByMobile" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_staff_basic
        where valid = 1 and mobile = #{mobile,jdbcType=VARCHAR}
    </select>
    <select id="getStaffByIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_staff_basic
        where valid=1
        and id in (${ids})
    </select>
    <select id="getStaffByMobiles" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_staff_basic
        where valid=1
        and mobile in (${mobiles})
    </select>
    <select id="getInternalDriverCount" resultType="java.lang.Integer">
        select
        count(distinct tsb.id)
        from t_staff_basic tsb
        left join t_carrier_driver_relation tcdr on tcdr.driver_id = tsb.id and tcdr.valid = 1
        where tsb.valid = 1
        and tsb.type in (1,3)
        and tsb.staff_property in (1, 3)
        and tcdr.enabled = 1
    </select>

    <select id="getInternalDriverByIds" resultMap="BaseResultMap">
        select
        tsb.*
        from t_staff_basic tsb
        left join t_carrier_driver_relation tcdr on tcdr.driver_id = tsb.id and tcdr.valid = 1
        where tsb.valid = 1
        and tsb.type in (1,3)
        and tsb.staff_property in (1, 3)
        and tcdr.enabled = 1
        and tsb.id in (${ids})
    </select>

    <select id="getDriverInfoForVehicleSettlement" resultType="com.logistics.tms.controller.vehiclesettlement.response.GetSettlementDriverResponseModel">
    select
    tsb.id driverId,
    tsb.name driverName,
    tsb.mobile driverMobile
    from t_staff_basic tsb
    left join t_carrier_driver_relation tcdr on tcdr.driver_id = tsb.id and tcdr.valid = 1
    where tsb.valid = 1
    and tsb.staff_property in (1, 3)
    and tcdr.enabled = 1
    <if test="driverName!=null and driverName!=''">
       and (instr(tsb.name,#{driverName,jdbcType=VARCHAR})
       or instr(tsb.mobile,#{driverName,jdbcType=VARCHAR}))
    </if>
    </select>

    <select id="getFuzzyQueryDriverInfo" resultType="com.logistics.tms.controller.staffvehiclerelation.response.GetFuzzyQueryDriverInfoResponseModel">
        select
        tqsb.id              as driverId,
        tqsb.name            as driverName,
        tqsb.mobile          as driverPhone,
        tqsb.identity_number as identityNumber
        from t_carrier_driver_relation tcdr
        left join t_staff_basic tqsb on tqsb.valid = 1 and tqsb.id = tcdr.driver_id
        where tcdr.valid = 1
        and tcdr.company_carrier_id = #{params.companyCarrierId,jdbcType=BIGINT}
        <if test="params.isOurCompany != null and params.isOurCompany == 1">
            and tqsb.type in (1, 3)
        </if>
        <if test="params.type != null and params.type != ''">
            and tqsb.staff_property in (${params.type})
        </if>
        <if test="params.fuzzyDriverField != null and params.fuzzyDriverField != ''">
            and (instr(tqsb.name, #{params.fuzzyDriverField,jdbcType = VARCHAR})
                or instr(tqsb.mobile, #{params.fuzzyDriverField,jdbcType = VARCHAR}))
        </if>
        <if test="params.enabled != null">
            and tcdr.enabled = #{params.enabled,jdbcType = INTEGER}
        </if>
    </select>

    <select id="searchDriver" resultType="com.logistics.tms.controller.dispatch.response.DriverSearchResponseModel">
        select
        tsb.id              as driverId,
        tsb.name            as driverName,
        tsb.mobile          as driverPhone,
        tsb.identity_number as driverIdentityNumber
        from t_staff_basic tsb
        left join t_carrier_driver_relation tcdr on tcdr.valid = 1 and tsb.id = tcdr.driver_id
        where tsb.valid = 1
        and tsb.type in (1, 3)
        and tcdr.enabled = 1
        and tcdr.company_carrier_id = #{params.companyCarrierId,jdbcType=BIGINT}
        <if test="params.staffProperty != null and params.staffProperty != ''">
            and tsb.staff_property in (${params.staffProperty})
        </if>
        <if test="params.driverNameAndPhone != null and params.driverNameAndPhone != ''">
            and (instr(tsb.name, #{params.driverNameAndPhone,jdbcType=VARCHAR}) or instr(tsb.mobile, #{params.driverNameAndPhone,jdbcType=VARCHAR}))
        </if>
    </select>

    <select id="selectDriverByVehicles" resultType="com.logistics.tms.biz.dispatch.model.DriverByVehiclesModel">
        SELECT
        tsb.id              as staffId,
        tsb.name            as driverName,
        tsb.mobile          as driverPhone,
        tsb.identity_number as driverIdentityNumber
        from t_staff_basic tsb
        left join t_carrier_driver_relation tcdr on tcdr.valid = 1 and tsb.id = tcdr.driver_id and tcdr.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        where tsb.valid = 1
        and tcdr.enabled = #{isEnable,jdbcType=INTEGER}
        and tsb.id in (${driverIds})
    </select>

    <select id="getByCarrierDriverId" resultMap="BaseResultMap">
        select
        tsb.*
        from t_staff_basic tsb
        left join t_carrier_driver_relation tcdr on tcdr.driver_id = tsb.id and tcdr.valid = 1
        where tsb.valid = 1
        and tcdr.id = #{carrierDriverId,jdbcType=BIGINT}
    </select>

    <select id="selectDriverBasicInfo" resultType="com.logistics.tms.controller.baiscinfo.applet.response.DriverBasicInfoResponseModel">
        select
        tsb.id                              as driverId,
        tsb.name                            as driverName,
        tsb.mobile                          as driverMobile,
        tsb.identity_number                 as driverIdentityNumber,
        tsb.real_name_authentication_status as realNameAuthenticationStatus,
        trna.auth_mode                      as authModel
        from t_staff_basic tsb
        left join t_real_name_authentication trna on AES_DECRYPT(UNHEX(trna.mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') = tsb.mobile
        and trna.authentication_type in (1, 2)
        and trna.certification_status = 2
        and trna.valid = 1
        where tsb.valid = 1
        and tsb.id = #{driverId}
    </select>
</mapper>
