package com.logistics.management.webapi.controller.routeenquiry.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * @author: wjf
 * @date: 2024/7/9 10:22
 */
@Data
public class RouteEnquirySettleAuditRequestDto {

    /**
     * 路线询价单表id
     */
    @NotBlank(message = "id不能为空")
    private String routeEnquiryId;

    /**
     * 审核结果：1 通过，2 驳回
     */
    @NotBlank(message = "请选择审核结果")
    @Pattern(regexp = "^([12])$", message = "请选择审核结果")
    private String operateType;

    /**
     * 备注
     */
    @Size(max = 200, message = "备注最多200字")
    private String remark;
}
