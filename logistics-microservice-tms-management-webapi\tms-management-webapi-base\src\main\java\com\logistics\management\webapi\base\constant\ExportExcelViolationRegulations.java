package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/6/4 10:37
 */
public class ExportExcelViolationRegulations {
    private ExportExcelViolationRegulations() {
    }

    private static final Map<String, String> VIOLATION_REGULATIONS_INFO;

    static {
        VIOLATION_REGULATIONS_INFO = new LinkedHashMap<>();
        VIOLATION_REGULATIONS_INFO.put("车牌号", "vehicleNo");
        VIOLATION_REGULATIONS_INFO.put("车辆机构", "vehiclePropertyLabel");
        VIOLATION_REGULATIONS_INFO.put("司机", "driverLabel");
        VIOLATION_REGULATIONS_INFO.put("违章事故时间", "occuranceTime");
        VIOLATION_REGULATIONS_INFO.put("违章事故地点", "occuranceAddress");
        VIOLATION_REGULATIONS_INFO.put("扣分", "deduction");
        VIOLATION_REGULATIONS_INFO.put("罚款(元)", "fine");
        VIOLATION_REGULATIONS_INFO.put("凭证", "countCertificates");
        VIOLATION_REGULATIONS_INFO.put("备注", "remark");
        VIOLATION_REGULATIONS_INFO.put("操作人", "lastModifiedBy");
        VIOLATION_REGULATIONS_INFO.put("修改时间", "lastModifiedTime");
    }

    public static Map<String, String> getViolationRegulationsInfo() {
        return VIOLATION_REGULATIONS_INFO;
    }
}
