package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2021/10/29 14:13
 */
@Data
public class LogisticsCostStatisticsOrderModel {
    @ApiModelProperty("签收数量")
    private BigDecimal signAmount;

    @ApiModelProperty("司机运费")
    private BigDecimal dispatchFreightFee;
    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;
    @ApiModelProperty("多装多卸加价费用")
    private BigDecimal markupFee;

    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;
}
