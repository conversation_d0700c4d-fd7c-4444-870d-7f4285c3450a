package com.logistics.tms.controller.vehicleassetmanagement.response;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/7/26 14:21
 */
@Data
public class VehicleAssertOutageCheckResponseModel {

    @ApiModelProperty("保险检验状态: 0 不通过 1 通过")
    private Integer insuranceCheckState = 1;

    @ApiModelProperty("保险ID,多个拼接")
    private String insuranceIds;

    @ApiModelProperty("保险保障中")
    private Integer safeguardCount = 0;

    @ApiModelProperty("保险未开始")
    private Integer notStartCount = 0;

    @ApiModelProperty("车主车辆关联Id")
    private Long carrierVehicleId;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("运单检测结果：0 不通过 1 通过")
    private Integer carrierOrderCheckState = 1;

    @ApiModelProperty("运单ID,多个拼接")
    private String carrierOrderIds;
}
