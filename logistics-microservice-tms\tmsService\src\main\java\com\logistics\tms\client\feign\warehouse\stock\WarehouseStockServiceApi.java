package com.logistics.tms.client.feign.warehouse.stock;

import com.logistics.tms.client.feign.FeignClientName;
import com.logistics.tms.client.feign.warehouse.stock.hystrix.WarehouseStockServiceApiHystrix;
import com.logistics.tms.client.feign.warehouse.stock.reponse.*;
import com.logistics.tms.client.feign.warehouse.stock.request.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/4 15:58
 */
@FeignClient(name = FeignClientName.WAREHOUSE_STOCK_SERVICES, fallback = WarehouseStockServiceApiHystrix.class)
public interface WarehouseStockServiceApi {

    @PostMapping(value = "/service/prepareStockIn/checkChangeUnloadWarehouse")
    @ApiOperation(value = "查询运单能否修改卸货地址")
    Result<List<CheckChangeUnloadWarehouseResponseModel>> checkChangeUnloadWarehouse(@RequestBody CheckChangeUnloadWarehouseRequestModel carrierOrderCodes);

    @ApiOperation("根据仓库地址名称地址详细查询仓库(物流用)")
    @PostMapping({"/service/warehouse/listWarehouseByName"})
    Result<List<ListWarehouseByNameResponseModel>> listWarehouseByName(@RequestBody ListWarehouseByNameRequestModel requestModel);

    @ApiOperation("获取跳转令牌")
    @PostMapping({"/service/wxapp/getSkipToken"})
    Result<GetSkipTokenResponseModel> getSkipToken(@RequestBody GetSkipTokenRequestModel requestModel);

    @ApiOperation(value = "根据条件查询仓库信息")
    @RequestMapping(value = "/api/warehouse/getWareHouseByIdsAndKeyWordAndType", method = RequestMethod.POST)
    Result<List<GetWareHouseByIdsAndKeyWordAndTypeResponseModel>> getWareHouseByIdsAndKeyWordAndType(@RequestBody @Valid GetWareHouseByIdsAndKeyWordAndTypeRequestModel requestDto);

    @ApiOperation(value = "通过托盘编码查询云仓出库计划上托盘来源公司(物流调用)")
    @PostMapping(value = "/service/stockout/getCompanyNameByCode")
    Result<GetCompanyNameByCodeRespModel> getCompanyNameByCode(@RequestBody @Valid GetCompanyNameByCodeReqModel reqModel);

}
