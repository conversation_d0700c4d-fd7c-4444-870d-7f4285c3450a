package com.logistics.appapi.client.reservationorder.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2024/8/14 14:25
 */
@Data
public class WaitReservationCarrierOrderListResponseModel {

    /**
     * 运单id
     */
    private Long carrierOrderId;

    /**
     * 运单号
     */
    private String carrierOrderCode;

    /**
     * 委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨
     */
    private Integer entrustType;

    /**
     * 运单状态 10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收
     */
    private Integer status;

    /**
     * 运单预计数量
     */
    private BigDecimal expectAmount;

    /**
     * 提卸货-省
     */
    private String provinceName;
    /**
     * 提卸货-市
     */
    private String cityName;
    /**
     * 提卸货-区
     */
    private String areaName;
    /**
     * 提卸货-详细地址
     */
    private String detailAddress;
    /**
     * 提卸货-仓库名称
     */
    private String warehouse;

    /**
     * 提卸货-预计时间
     */
    private Date expectedTime;

}
