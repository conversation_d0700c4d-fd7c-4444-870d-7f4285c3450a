package com.logistics.appapi.client.sysconfig.hystrix;

import com.logistics.appapi.client.sysconfig.SysConfigClient;
import com.logistics.appapi.client.sysconfig.request.SysConfigRequestModel;
import com.logistics.appapi.client.sysconfig.response.SysConfigResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/7 17:19
 */
@Component
public class SysConfigClientHystrix implements SysConfigClient {
    @Override
    public Result<List<SysConfigResponseModel>> batchGetSysConfig(Collection<SysConfigRequestModel> requestModel) {
        return Result.timeout();
    }
}
