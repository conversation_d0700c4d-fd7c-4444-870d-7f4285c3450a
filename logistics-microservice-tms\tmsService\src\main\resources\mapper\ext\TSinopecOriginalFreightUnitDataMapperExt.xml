<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TSinopecOriginalFreightUnitDataMapper">

    <insert id="batchInsertSelective">
        <foreach collection="list" item="item" separator=";">
            insert into t_sinopec_original_freight_unit_data
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.sinopecOriginalDataId != null">
                    sinopec_original_data_id,
                </if>
                <if test="item.name != null">
                    name,
                </if>
                <if test="item.type != null">
                    type,
                </if>
                <if test="item.count != null">
                    count,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.sinopecOriginalDataId != null">
                    #{item.sinopecOriginalDataId,jdbcType=BIGINT},
                </if>
                <if test="item.name != null">
                    #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.type != null">
                    #{item.type,jdbcType=VARCHAR},
                </if>
                <if test="item.count != null">
                    #{item.count,jdbcType=INTEGER},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>
</mapper>