package com.logistics.management.webapi.api.impl.personalaccidentinsurance.mapping;

import com.logistics.management.webapi.api.feign.personalaccidentinsurance.dto.PersonalAccidentInsuranceDetailResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.tms.api.feign.personalaccidentinsurance.model.PersonalAccidentInsuranceDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;

import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/5/30 19:33
 */
public class PersonalAccidentInsuranceDetailMapping extends MapperMapping<PersonalAccidentInsuranceDetailResponseModel,PersonalAccidentInsuranceDetailResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;
    public PersonalAccidentInsuranceDetailMapping(String imagePrefix ,Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        PersonalAccidentInsuranceDetailResponseModel source = getSource();
        PersonalAccidentInsuranceDetailResponseDto destination = getDestination();
        if (source != null){
            if (source.getStartTime() != null){
                destination.setStartTime(DateUtils.dateToString(source.getStartTime(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
            }
            if (source.getEndTime() != null){
                destination.setEndTime(DateUtils.dateToString(source.getEndTime(),CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
            }
            if (ListUtils.isNotEmpty(source.getTicketsList())){
                destination.getTicketsList().stream().forEach(dto ->
                    source.getTicketsList().stream().forEach(model -> {
                        if (model.getTicketId().toString().equals(dto.getTicketId())){
                            dto.setFilePathSrc(imagePrefix + imageMap.get(model.getFilePath()));
                        }
                    })
                );
            }
        }
    }
}
