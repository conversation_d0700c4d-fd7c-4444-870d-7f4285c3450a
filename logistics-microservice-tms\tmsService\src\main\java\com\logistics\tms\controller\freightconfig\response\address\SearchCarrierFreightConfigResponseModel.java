package com.logistics.tms.controller.freightconfig.response.address;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchCarrierFreightConfigResponseModel {

    @ApiModelProperty("车主运价id")
    private Long carrierFreightId;

    @ApiModelProperty("状态")
    private Integer enabled;

    @ApiModelProperty("车主名称")
    private String companyCarrierName;

    @ApiModelProperty("车主公司类型：1 公司，2 个人")
    private Integer companyCarrierType;

    @ApiModelProperty("车主账号名称")
    private String carrierContactName;

    @ApiModelProperty("车主账号手机号（原长度50）")
    private String carrierContactPhone;
}
