<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TVehicleDrivingLicenseMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TVehicleDrivingLicense">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="vehicle_type" jdbcType="BIGINT" property="vehicleType" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="vehicle_identification_number" jdbcType="VARCHAR" property="vehicleIdentificationNumber" />
    <result column="engine_number" jdbcType="VARCHAR" property="engineNumber" />
    <result column="certification_department" jdbcType="VARCHAR" property="certificationDepartment" />
    <result column="registration_date" jdbcType="TIMESTAMP" property="registrationDate" />
    <result column="issue_date" jdbcType="TIMESTAMP" property="issueDate" />
    <result column="filing_number" jdbcType="VARCHAR" property="filingNumber" />
    <result column="authorized_carrying_capacity" jdbcType="INTEGER" property="authorizedCarryingCapacity" />
    <result column="total_weight" jdbcType="DECIMAL" property="totalWeight" />
    <result column="curb_weight" jdbcType="DECIMAL" property="curbWeight" />
    <result column="traction_mass_weight" jdbcType="DECIMAL" property="tractionMassWeight" />
    <result column="approved_load_weight" jdbcType="DECIMAL" property="approvedLoadWeight" />
    <result column="length" jdbcType="INTEGER" property="length" />
    <result column="width" jdbcType="INTEGER" property="width" />
    <result column="height" jdbcType="INTEGER" property="height" />
    <result column="obsolescence_date" jdbcType="TIMESTAMP" property="obsolescenceDate" />
    <result column="axle_number" jdbcType="INTEGER" property="axleNumber" />
    <result column="drive_shaft_number" jdbcType="INTEGER" property="driveShaftNumber" />
    <result column="tires_number" jdbcType="INTEGER" property="tiresNumber" />
    <result column="plate_color" jdbcType="INTEGER" property="plateColor" />
    <result column="body_color" jdbcType="VARCHAR" property="bodyColor" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, vehicle_id, vehicle_no, vehicle_type, address, owner, brand, model, vehicle_identification_number, 
    engine_number, certification_department, registration_date, issue_date, filing_number, 
    authorized_carrying_capacity, total_weight, curb_weight, traction_mass_weight, approved_load_weight, 
    length, width, height, obsolescence_date, axle_number, drive_shaft_number, tires_number, 
    plate_color, body_color, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_vehicle_driving_license
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_vehicle_driving_license
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TVehicleDrivingLicense">
    insert into t_vehicle_driving_license (id, vehicle_id, vehicle_no,
      vehicle_type, address, owner, 
      brand, model, vehicle_identification_number, 
      engine_number, certification_department, 
      registration_date, issue_date, filing_number, 
      authorized_carrying_capacity, total_weight, 
      curb_weight, traction_mass_weight, approved_load_weight, 
      length, width, height, 
      obsolescence_date, axle_number, drive_shaft_number, 
      tires_number, plate_color, body_color, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, #{vehicleNo,jdbcType=VARCHAR}, 
      #{vehicleType,jdbcType=BIGINT}, #{address,jdbcType=VARCHAR}, #{owner,jdbcType=VARCHAR}, 
      #{brand,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, #{vehicleIdentificationNumber,jdbcType=VARCHAR}, 
      #{engineNumber,jdbcType=VARCHAR}, #{certificationDepartment,jdbcType=VARCHAR}, 
      #{registrationDate,jdbcType=TIMESTAMP}, #{issueDate,jdbcType=TIMESTAMP}, #{filingNumber,jdbcType=VARCHAR}, 
      #{authorizedCarryingCapacity,jdbcType=INTEGER}, #{totalWeight,jdbcType=DECIMAL}, 
      #{curbWeight,jdbcType=DECIMAL}, #{tractionMassWeight,jdbcType=DECIMAL}, #{approvedLoadWeight,jdbcType=DECIMAL}, 
      #{length,jdbcType=INTEGER}, #{width,jdbcType=INTEGER}, #{height,jdbcType=INTEGER}, 
      #{obsolescenceDate,jdbcType=TIMESTAMP}, #{axleNumber,jdbcType=INTEGER}, #{driveShaftNumber,jdbcType=INTEGER}, 
      #{tiresNumber,jdbcType=INTEGER}, #{plateColor,jdbcType=INTEGER}, #{bodyColor,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TVehicleDrivingLicense" useGeneratedKeys="true" keyProperty="id">
    insert into t_vehicle_driving_license
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleNo != null">
        vehicle_no,
      </if>
      <if test="vehicleType != null">
        vehicle_type,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="owner != null">
        owner,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="vehicleIdentificationNumber != null">
        vehicle_identification_number,
      </if>
      <if test="engineNumber != null">
        engine_number,
      </if>
      <if test="certificationDepartment != null">
        certification_department,
      </if>
      <if test="registrationDate != null">
        registration_date,
      </if>
      <if test="issueDate != null">
        issue_date,
      </if>
      <if test="filingNumber != null">
        filing_number,
      </if>
      <if test="authorizedCarryingCapacity != null">
        authorized_carrying_capacity,
      </if>
      <if test="totalWeight != null">
        total_weight,
      </if>
      <if test="curbWeight != null">
        curb_weight,
      </if>
      <if test="tractionMassWeight != null">
        traction_mass_weight,
      </if>
      <if test="approvedLoadWeight != null">
        approved_load_weight,
      </if>
      <if test="length != null">
        length,
      </if>
      <if test="width != null">
        width,
      </if>
      <if test="height != null">
        height,
      </if>
      <if test="obsolescenceDate != null">
        obsolescence_date,
      </if>
      <if test="axleNumber != null">
        axle_number,
      </if>
      <if test="driveShaftNumber != null">
        drive_shaft_number,
      </if>
      <if test="tiresNumber != null">
        tires_number,
      </if>
      <if test="plateColor != null">
        plate_color,
      </if>
      <if test="bodyColor != null">
        body_color,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleType != null">
        #{vehicleType,jdbcType=BIGINT},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="vehicleIdentificationNumber != null">
        #{vehicleIdentificationNumber,jdbcType=VARCHAR},
      </if>
      <if test="engineNumber != null">
        #{engineNumber,jdbcType=VARCHAR},
      </if>
      <if test="certificationDepartment != null">
        #{certificationDepartment,jdbcType=VARCHAR},
      </if>
      <if test="registrationDate != null">
        #{registrationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="issueDate != null">
        #{issueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="filingNumber != null">
        #{filingNumber,jdbcType=VARCHAR},
      </if>
      <if test="authorizedCarryingCapacity != null">
        #{authorizedCarryingCapacity,jdbcType=INTEGER},
      </if>
      <if test="totalWeight != null">
        #{totalWeight,jdbcType=DECIMAL},
      </if>
      <if test="curbWeight != null">
        #{curbWeight,jdbcType=DECIMAL},
      </if>
      <if test="tractionMassWeight != null">
        #{tractionMassWeight,jdbcType=DECIMAL},
      </if>
      <if test="approvedLoadWeight != null">
        #{approvedLoadWeight,jdbcType=DECIMAL},
      </if>
      <if test="length != null">
        #{length,jdbcType=INTEGER},
      </if>
      <if test="width != null">
        #{width,jdbcType=INTEGER},
      </if>
      <if test="height != null">
        #{height,jdbcType=INTEGER},
      </if>
      <if test="obsolescenceDate != null">
        #{obsolescenceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="axleNumber != null">
        #{axleNumber,jdbcType=INTEGER},
      </if>
      <if test="driveShaftNumber != null">
        #{driveShaftNumber,jdbcType=INTEGER},
      </if>
      <if test="tiresNumber != null">
        #{tiresNumber,jdbcType=INTEGER},
      </if>
      <if test="plateColor != null">
        #{plateColor,jdbcType=INTEGER},
      </if>
      <if test="bodyColor != null">
        #{bodyColor,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TVehicleDrivingLicense">
    update t_vehicle_driving_license
    <set>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleType != null">
        vehicle_type = #{vehicleType,jdbcType=BIGINT},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="owner != null">
        owner = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="vehicleIdentificationNumber != null">
        vehicle_identification_number = #{vehicleIdentificationNumber,jdbcType=VARCHAR},
      </if>
      <if test="engineNumber != null">
        engine_number = #{engineNumber,jdbcType=VARCHAR},
      </if>
      <if test="certificationDepartment != null">
        certification_department = #{certificationDepartment,jdbcType=VARCHAR},
      </if>
      <if test="registrationDate != null">
        registration_date = #{registrationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="issueDate != null">
        issue_date = #{issueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="filingNumber != null">
        filing_number = #{filingNumber,jdbcType=VARCHAR},
      </if>
      <if test="authorizedCarryingCapacity != null">
        authorized_carrying_capacity = #{authorizedCarryingCapacity,jdbcType=INTEGER},
      </if>
      <if test="totalWeight != null">
        total_weight = #{totalWeight,jdbcType=DECIMAL},
      </if>
      <if test="curbWeight != null">
        curb_weight = #{curbWeight,jdbcType=DECIMAL},
      </if>
      <if test="tractionMassWeight != null">
        traction_mass_weight = #{tractionMassWeight,jdbcType=DECIMAL},
      </if>
      <if test="approvedLoadWeight != null">
        approved_load_weight = #{approvedLoadWeight,jdbcType=DECIMAL},
      </if>
      <if test="length != null">
        length = #{length,jdbcType=INTEGER},
      </if>
      <if test="width != null">
        width = #{width,jdbcType=INTEGER},
      </if>
      <if test="height != null">
        height = #{height,jdbcType=INTEGER},
      </if>
      <if test="obsolescenceDate != null">
        obsolescence_date = #{obsolescenceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="axleNumber != null">
        axle_number = #{axleNumber,jdbcType=INTEGER},
      </if>
      <if test="driveShaftNumber != null">
        drive_shaft_number = #{driveShaftNumber,jdbcType=INTEGER},
      </if>
      <if test="tiresNumber != null">
        tires_number = #{tiresNumber,jdbcType=INTEGER},
      </if>
      <if test="plateColor != null">
        plate_color = #{plateColor,jdbcType=INTEGER},
      </if>
      <if test="bodyColor != null">
        body_color = #{bodyColor,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TVehicleDrivingLicense">
    update t_vehicle_driving_license
    set vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      vehicle_type = #{vehicleType,jdbcType=BIGINT},
      address = #{address,jdbcType=VARCHAR},
      owner = #{owner,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      vehicle_identification_number = #{vehicleIdentificationNumber,jdbcType=VARCHAR},
      engine_number = #{engineNumber,jdbcType=VARCHAR},
      certification_department = #{certificationDepartment,jdbcType=VARCHAR},
      registration_date = #{registrationDate,jdbcType=TIMESTAMP},
      issue_date = #{issueDate,jdbcType=TIMESTAMP},
      filing_number = #{filingNumber,jdbcType=VARCHAR},
      authorized_carrying_capacity = #{authorizedCarryingCapacity,jdbcType=INTEGER},
      total_weight = #{totalWeight,jdbcType=DECIMAL},
      curb_weight = #{curbWeight,jdbcType=DECIMAL},
      traction_mass_weight = #{tractionMassWeight,jdbcType=DECIMAL},
      approved_load_weight = #{approvedLoadWeight,jdbcType=DECIMAL},
      length = #{length,jdbcType=INTEGER},
      width = #{width,jdbcType=INTEGER},
      height = #{height,jdbcType=INTEGER},
      obsolescence_date = #{obsolescenceDate,jdbcType=TIMESTAMP},
      axle_number = #{axleNumber,jdbcType=INTEGER},
      drive_shaft_number = #{driveShaftNumber,jdbcType=INTEGER},
      tires_number = #{tiresNumber,jdbcType=INTEGER},
      plate_color = #{plateColor,jdbcType=INTEGER},
      body_color = #{bodyColor,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>