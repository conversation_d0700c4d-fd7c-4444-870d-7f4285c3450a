package com.logistics.management.webapi.base.enums;

/**
 * 车辆使用性质
 * @Author: sj
 * @Date: 2019/6/10 17:29
 */
public enum VehicleUsagePropertyEnum {
    NULL(-1,""),
    COMMON_GOODS (1,"普货运输"),
    DANGEROUS_GOODS(2,"危货运输"),
    ;

    private Integer key;
    private String value;

    VehicleUsagePropertyEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static VehicleUsagePropertyEnum getEnum(Integer key) {
        for (VehicleUsagePropertyEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return NULL;
    }

    public static VehicleUsagePropertyEnum getEnumByValue(String value) {
        for (VehicleUsagePropertyEnum t : values()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        return null;
    }
}
