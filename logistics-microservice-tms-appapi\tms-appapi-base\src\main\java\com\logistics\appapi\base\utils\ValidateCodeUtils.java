package com.logistics.appapi.base.utils;

public class ValidateCodeUtils {
    private ValidateCodeUtils() {
    }

    public static final int WIDTH = 90;//定义图片的width
    public static final int HEIGHT = 20;//定义图片的height
    public static final int CODECOUNT = 4;//定义图片上显示验证码的个数
    public static final int XX = 15;
    public static final int FONTHEIGHT = 18;
    public static final int CODEY = 16;

    private static final char[] CODESEQUENCE = {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J',
            'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W',
            'X', 'Y', 'Z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};

    public static char[] getCodeSequence(){
        return CODESEQUENCE;
    }
}
