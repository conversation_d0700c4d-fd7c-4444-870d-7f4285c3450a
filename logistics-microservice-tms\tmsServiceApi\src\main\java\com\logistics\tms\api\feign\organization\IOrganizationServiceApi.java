package com.logistics.tms.api.feign.organization;

import com.logistics.tms.api.feign.organization.hystrix.IOrganizationServiceApiHystrix;
import com.logistics.tms.api.feign.organization.model.IOrganizationNameResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

@Api(value = "API - OrganizationServiceApi", tags = "部门管理")
@FeignClient(name = "logistics-tms-services", fallback = IOrganizationServiceApiHystrix.class)
public interface IOrganizationServiceApi {

    @ApiOperation(value = "查询所有部门（列表筛选条件接口）", tags = "1.2.8")
    @PostMapping(value = "/service/organization/getAllOrgForHierarchy")
    Result<List<IOrganizationNameResponseModel>> getAllOrgForHierarchy();
}
