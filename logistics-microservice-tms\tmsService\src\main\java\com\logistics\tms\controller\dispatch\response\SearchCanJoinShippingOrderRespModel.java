package com.logistics.tms.controller.dispatch.response;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SearchCanJoinShippingOrderRespModel {

    /**
     * 零担id
     */
    private Long shippingOrderId;

    /**
     * 零担单号
     */
    private String shippingOrderCode;

    /**
     * 车牌号
     */
    private String vehicleNo;

    /**
     * 串点费用
     */
    private BigDecimal crossPointFee;

    /**
     * 整车运费
     */
    private BigDecimal carrierFreight;


    private Long demandOrderId;
    /**
     * 收货地省 2.42
     */
    private String unloadProvinceName;
    /**
     * 收货地市 2.42
     */
    private String unloadCityName;
    /**
     * 收货地区 2.42
     */
    private String unloadAreaName;

}
