package com.logistics.appapi.client.carrierorderpickup;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.carrierorder.request.CarrierOrderIdRequestModel;
import com.logistics.appapi.client.carrierorderpickup.hystrix.CustomerOrderLoadCodeClientHystrix;
import com.logistics.appapi.client.carrierorderpickup.request.*;
import com.logistics.appapi.client.carrierorderpickup.response.*;
import com.logistics.appapi.client.customeraccount.hystrix.CustomerAccountClientHystrix;
import com.logistics.appapi.client.customeraccount.request.*;
import com.logistics.appapi.client.customeraccount.response.CustomerLoginResponseModel;
import com.logistics.appapi.client.customeraccount.response.UpdatePhoneByVerifyPhoneResponseModel;
import com.logistics.appapi.controller.carrierorder.request.ErrorLoadCodeGetDetailRequestDto;
import com.logistics.appapi.controller.carrierorder.response.CodePickUpDetailResponseModel;
import com.logistics.appapi.controller.carrierorder.response.ErrorLoadCodeGetDetailResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * @author: wjf
 * @date: 2024/3/7 16:53
 */
@FeignClient(name = FeignClientName.TMS_SERVICES, fallback = CustomerOrderLoadCodeClientHystrix.class)
public interface CustomerOrderLoadCodeClient {


    @ApiOperation(value = "提货-正确临时编码列表 (v2.46)")
    @PostMapping(value = "/service/carrierOrderLoadCode/correctLoadCodeList")
    public Result<PageInfo<CorrectLoadCodeListResponseModel>> correctLoadCodeList(@RequestBody @Valid CorrectLoadCodeListRequestModel requestModel);

    @ApiOperation(value = "提货-有误临时编码列表 (v2.46)")
    @PostMapping(value = "/service/carrierOrderLoadCode/errorLoadCodeList")
    public Result<PageInfo<ErrorLoadCodeListResponseModel>> errorLoadCodeList(@RequestBody @Valid ErrorLoadCodeListRequestModel requestModel);


    @ApiOperation(value = "提货-保存正确的临时编码 (v2.46)")
    @PostMapping(value = "/service/carrierOrderLoadCode/saveCorrectLoadCode")
    public Result<SaveCorrectLoadCodeResponseModel> saveCorrectLoadCode(@RequestBody @Valid SaveCorrectLoadCodeRequestModel requestModel);


    @ApiOperation(value = "提货-保存错误的临时编码附件 (v2.46)")
    @PostMapping(value = "/service/carrierOrderLoadCode/saveErrorLoadCodeFile")
    public Result<SaveErrorLoadCodeFileResponseModel> saveErrorLoadCodeFile(@RequestBody @Valid SaveErrorLoadCodeFileRequestModel requestModel);

    @ApiOperation(value = "提货-删除临时编码 (v2.46)")
    @PostMapping(value = "/service/carrierOrderLoadCode/deleteCarrierOrderLoadCode")
    public Result<Boolean> deleteCarrierOrderLoadCode(@RequestBody @Valid CarrierOrderLoadCodeIdRequestModel requestModel);

    @ApiOperation(value = "提货详情(编码提货) (v2.46)")
    @PostMapping(value = "/service/carrierOrderLoadCode/codePickUpDetail")
    Result<CodePickUpDetailResponseModel> codePickUpDetail(CarrierOrderIdRequestModel requestModel);

    @ApiOperation(value = "提货-有误临时编码查看 (v2.46)")
    @PostMapping(value = "/service/carrierOrderLoadCode/errorLoadCodeGetDetail")
    public Result<ErrorLoadCodeGetDetailResponseModel> errorLoadCodeGetDetail(@RequestBody @Valid ErrorLoadCodeGetDetailRequestModel requestDto);

}
