package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2023/07/31
*/
@Data
public class TReserveApply extends BaseEntity {
    /**
    * 申请单号
    */
    @ApiModelProperty("申请单号")
    private String reserveApplyCode;

    /**
    * 状态：-1 已撤销，0 待业务审核，1 待财务审核，2 已驳回，3 待打款，4 已打款
    */
    @ApiModelProperty("状态：-1 已撤销，0 待业务审核，1 待财务审核，2 已驳回，3 待打款，4 已打款")
    private Integer status;

    /**
    * 备用金类型: 1 申请，2 垫付，3 红冲退款
    */
    @ApiModelProperty("备用金类型: 1 申请，2 垫付，3 红冲退款")
    private Integer type;

    /**
    * 司机id
    */
    @ApiModelProperty("司机id")
    private Long staffId;

    /**
    * 司机姓名
    */
    @ApiModelProperty("司机姓名")
    private String staffName;

    /**
    * 司机手机号
    */
    @ApiModelProperty("司机手机号")
    private String staffMobile;

    /**
    * 人员机构：1 自主，2 外部，3 自营
    */
    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    /**
    * 收款银行账号（原长度50）
    */
    @ApiModelProperty("收款银行账号（原长度50）")
    private String receiveBankAccount;

    /**
    * 收款银行名称
    */
    @ApiModelProperty("收款银行名称")
    private String receiveBankAccountName;

    /**
    * 收款支行名称
    */
    @ApiModelProperty("收款支行名称")
    private String receiveBraBankName;

    /**
    * 申请金额
    */
    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;

    /**
    * 申请时间
    */
    @ApiModelProperty("申请时间")
    private Date applyTime;

    /**
    * 申请备注
    */
    @ApiModelProperty("申请备注")
    private String applyRemark;

    /**
    * 批准金额
    */
    @ApiModelProperty("批准金额")
    private BigDecimal approvedAmount;

    /**
    * 业务审核人
    */
    @ApiModelProperty("业务审核人")
    private String auditorNameOne;

    /**
    * 业务审核时间
    */
    @ApiModelProperty("业务审核时间")
    private Date auditTimeOne;

    /**
    * 业务审核备注
    */
    @ApiModelProperty("业务审核备注")
    private String auditRemarkOne;

    /**
    * 财务审核人
    */
    @ApiModelProperty("财务审核人")
    private String auditorNameTwo;

    /**
    * 财务审核时间
    */
    @ApiModelProperty("财务审核时间")
    private Date auditTimeTwo;

    /**
    * 财务审核备注
    */
    @ApiModelProperty("财务审核备注")
    private String auditRemarkTwo;

    /**
    * 打款日期
    */
    @ApiModelProperty("打款日期")
    private Date payTime;

    /**
    * 流水号
    */
    @ApiModelProperty("流水号")
    private String runningCode;

    /**
    * 打款备注
    */
    @ApiModelProperty("打款备注")
    private String paymentRemark;

    /**
    * 驳回备注
    */
    @ApiModelProperty("驳回备注")
    private String rejectRemark;

    /**
    * 撤销备注
    */
    @ApiModelProperty("撤销备注")
    private String cancelRemark;

    /**
    * 可用余额
    */
    @ApiModelProperty("可用余额")
    private BigDecimal balanceAmount;
}