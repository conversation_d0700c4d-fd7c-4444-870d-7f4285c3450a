<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TLoanSettlementRecordMapper">

  <select id="getSettlementRecordList" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from t_loan_settlement_record
    where valid = 1
    and loan_records_id = #{loanRecordId,jdbcType=BIGINT}
    order by last_modified_time desc
  </select>
  <select id="getByLoanRecordsIdMonth" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_loan_settlement_record
    where valid=1
    and deducting_month= #{settlementMonth,jdbcType=VARCHAR}
    and loan_records_id= #{loanRecordId,jdbcType=BIGINT}
    order by id desc limit 1
  </select>
</mapper>