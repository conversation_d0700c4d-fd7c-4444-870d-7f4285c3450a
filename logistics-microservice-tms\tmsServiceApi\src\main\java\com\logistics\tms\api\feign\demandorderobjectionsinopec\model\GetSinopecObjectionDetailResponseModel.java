package com.logistics.tms.api.feign.demandorderobjectionsinopec.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/5/30 13:36
 */
@Data
public class GetSinopecObjectionDetailResponseModel {
    @ApiModelProperty("需求单异常id")
    private Long demandOrderObjectionId;
    @ApiModelProperty("需求单id")
    private Long demandId;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("货主")
    private String companyEntrustName;
    @ApiModelProperty("异常类型：1 已报价，2 已取消")
    private Integer objectionType;
    @ApiModelProperty("调度人姓名")
    private String dispatcherName;
    @ApiModelProperty("调度人电话")
    private String dispatcherPhone;

    //审核结果
    @ApiModelProperty("审核状态：0 待审核，1 已审核，２ 已驳回")
    private Integer auditStatus;
    @ApiModelProperty("审核异常类型：1 已报价，2 已取消")
    private Integer auditObjectionType;
    @ApiModelProperty("审核依据")
    private List<String> auditTicketList;
    @ApiModelProperty("备注")
    private String auditRemark;

    @ApiModelProperty("中石化订单号")
    private String sinopecOrderNo;
}
