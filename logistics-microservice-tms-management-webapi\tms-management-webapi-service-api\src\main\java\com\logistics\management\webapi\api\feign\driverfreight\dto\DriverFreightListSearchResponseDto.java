package com.logistics.management.webapi.api.feign.driverfreight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DriverFreightListSearchResponseDto {
    @ApiModelProperty("运单ID")
    private String carrierOrderId = "";
    @ApiModelProperty("50000 待签收 60000 已签收 2 已放空")
    private String status = "";
    @ApiModelProperty("运单状态描述")
    private String statusDesc = "";
    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";
    @ApiModelProperty("客户单号")
    private String customerOrderCode = "";
    @ApiModelProperty("需求单ID")
    private String demandOrderId = "";
    @ApiModelProperty("需求单号")
    private String demandOrderCode = "";
    @ApiModelProperty("车辆机构 1 自主，2 外部，3 自营")
    private String vehicleProperty= "";
    private String vehiclePropertyDesc= "";
    @ApiModelProperty("车牌号")
    private String vehicleNo = "";
    @ApiModelProperty("司机")
    private String driver = "";
    @ApiModelProperty("司机运费合计")
    private String driverFreightTotal = "";
    @ApiModelProperty("预计司机运费")
    private String driverFreight="";
    @ApiModelProperty("调整费用")
    private String adjustFee="";
    @ApiModelProperty("多装多卸加价费用")
    private String markupFee="";
    @ApiModelProperty("实际结算数据")
    private String unloadAmount = "";
    @ApiModelProperty("货物单位：1 件，2 吨，3方，4块")
    private String goodsUnit;
    @ApiModelProperty("货物单位：1 件，2 吨，3方，4块")
    private String goodsUnitDesc;
    @ApiModelProperty("发货地址")
    private String loadAddress = "";
    @ApiModelProperty("收货地址")
    private String unloadAddress = "";
    @ApiModelProperty("实际签收时间")
    private String signTime = "";
    @ApiModelProperty("品名")
    private String goodsName = "";
    @ApiModelProperty("规格")
    private String size = "";
    @ApiModelProperty("备注")
    private String remark = "";
    @ApiModelProperty("调度人")
    private String dispatchUserName = "";
    @ApiModelProperty("运单生成时间")
    private String dispatchTime = "";

    @ApiModelProperty("货主公司名称")
    private String companyEntrustName= "";
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName= "";

    @ApiModelProperty("委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件")
    private String demandOrderSource="";
}
