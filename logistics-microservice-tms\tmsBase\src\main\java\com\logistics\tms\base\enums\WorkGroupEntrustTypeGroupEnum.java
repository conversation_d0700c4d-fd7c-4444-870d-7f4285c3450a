package com.logistics.tms.base.enums;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

@Getter
@AllArgsConstructor
public enum WorkGroupEntrustTypeGroupEnum {

    RECYCLE(1, "回收业务"),
    PROCUREMENT(2, "采购业务"),
    WAREHOUSE(3, "仓库业务"),
    ;
    private final Integer key;
    private final String value;

    private static final Multimap<WorkGroupEntrustTypeGroupEnum, Integer> entrustTypeMap;

    static {
        entrustTypeMap = ArrayListMultimap.create();
        // 1.回收业务(回收入库、回收出库)
        entrustTypeMap.putAll(RECYCLE,
                Lists.newArrayList(EntrustTypeEnum.RECYCLE_IN.getKey(), EntrustTypeEnum.RECYCLE_OUT.getKey()));
        // 2.采购业务（供应商直配、采购)
        entrustTypeMap.putAll(PROCUREMENT,
                Lists.newArrayList(EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey(), EntrustTypeEnum.PROCUREMENT.getKey()));
    }

    public static WorkGroupEntrustTypeGroupEnum getEnumByOrderEntrustType(Integer orderEntrustType) {
        return entrustTypeMap.entries()
                .stream()
                .filter(f -> f.getValue().equals(orderEntrustType))
                .findFirst()
                .map(Map.Entry::getKey)
                .orElse(WAREHOUSE);
    }
}
