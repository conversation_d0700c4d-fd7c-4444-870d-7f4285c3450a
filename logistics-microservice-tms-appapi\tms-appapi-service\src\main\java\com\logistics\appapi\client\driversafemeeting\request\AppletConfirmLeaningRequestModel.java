package com.logistics.appapi.client.driversafemeeting.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/11/8 13:31
 */
@Data
public class AppletConfirmLeaningRequestModel {
    @ApiModelProperty("学习例会关系id")
    private Long safeMeetingRelationId;
    @ApiModelProperty(value = "驾驶员图片")
    private String staffDriverImageUrl;
    @ApiModelProperty(value = "签字图片")
    private String signImageUrl;
    @ApiModelProperty(value = "经度")
    private String longitude;
    @ApiModelProperty(value = "纬度")
    private String latitude;
}
