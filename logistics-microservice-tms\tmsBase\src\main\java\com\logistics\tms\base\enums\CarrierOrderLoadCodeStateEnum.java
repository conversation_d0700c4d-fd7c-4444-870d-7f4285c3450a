package com.logistics.tms.base.enums;


public enum CarrierOrderLoadCodeStateEnum {
    DEFAULT(-9999,""),
    CORRECT_CODE(1,"正确编码"),
    CAN_NOT_IDENTIFY_CODE(2,"无法识别编码"),

    ;

    private Integer key;
    private String value;

    CarrierOrderLoadCodeStateEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
    public static CarrierOrderLoadCodeStateEnum getEnum(Integer key) {
        for (CarrierOrderLoadCodeStateEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
