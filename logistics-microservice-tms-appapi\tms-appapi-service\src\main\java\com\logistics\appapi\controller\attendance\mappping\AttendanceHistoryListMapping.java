package com.logistics.appapi.controller.attendance.mappping;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.client.attendance.response.AttendanceHistoryItemResponseModel;
import com.logistics.appapi.client.attendance.response.AttendanceHistoryListResponseModel;
import com.logistics.appapi.controller.attendance.response.AttendanceHistoryItemDto;
import com.logistics.appapi.controller.attendance.response.AttendanceHistoryListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.ObjectUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;

public class AttendanceHistoryListMapping extends MapperMapping<AttendanceHistoryListResponseModel, AttendanceHistoryListResponseDto> {

    @Override
    public void configure() {

        AttendanceHistoryListResponseModel source = getSource();
        AttendanceHistoryListResponseDto destination = getDestination();
        PageInfo pageAttendanceHistoryItem = source.getAttendanceHistoryItem();

        // 工时格式化
        destination.setManHourSum(source.getManHourSum().stripTrailingZeros().toPlainString());


        // item mapping 转换
        List<AttendanceHistoryItemResponseModel> historyItemList = pageAttendanceHistoryItem.getList();
        if (ListUtils.isNotEmpty(historyItemList)) {
            // 考勤日期处理
            String attendanceDate = historyItemList.stream()
                    .findFirst()
                    .map(d -> {
                        LocalDate attendanceLocalDate = LocalDate.ofInstant(d.getAttendanceDate().toInstant(), ZoneId.systemDefault());
                        return LocalDate.now().isEqual(attendanceLocalDate) ?
                                CommonConstant.DATE_TODAY : DateUtils.dateToString(d.getAttendanceDate(), CommonConstant.DATE_TO_STRING_MD_PATTERN_TEXT);
                    }).get();

            List<AttendanceHistoryItemDto> attendanceHistoryItemDtoList = MapperUtils.mapper(historyItemList,
                            AttendanceHistoryItemDto.class,
                            new AttendanceHistoryItemMapping());
            attendanceHistoryItemDtoList
                    .stream()
                    .limit(CommonConstant.INTEGER_ONE)
                    .forEach(f -> f.setAttendanceDate(attendanceDate));

            pageAttendanceHistoryItem.setList(attendanceHistoryItemDtoList);
        }
        destination.setAttendanceHistoryItem(pageAttendanceHistoryItem);
    }

    /**
     * Item转换mapping
     */
    class AttendanceHistoryItemMapping extends MapperMapping<AttendanceHistoryItemResponseModel, AttendanceHistoryItemDto> {

        @Override
        public void configure() {

            AttendanceHistoryItemResponseModel source = getSource();
            AttendanceHistoryItemDto destination = getDestination();

            // 工时处理
            destination.setManHour(source.getManHour().stripTrailingZeros().toPlainString());

            // 考勤日期时间格式转换
            destination.setAttendanceDate(DateUtils.dateToString(source.getAttendanceDate(), CommonConstant.DATE_TO_STRING_MD_PATTERN_TEXT));

            // 上班时间格式转换
            String onDutyPunchDateTime = DateUtils.dateToString(source.getOnDutyPunchTime(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN);
            String onDutyPunchTime = DateUtils.dateToString(source.getOnDutyPunchTime(), CommonConstant.DATE_TO_STRING_HM_PATTERN);
            destination.setOnDutyPunchDateTime(onDutyPunchDateTime);
            destination.setOnDutyPunchTime(onDutyPunchTime);

            // 下班时间格式转换
            if (ObjectUtils.isNotEmpty(source.getOffDutyPunchTime())) {
                String offDutyPunchDateTime = DateUtils.dateToString(source.getOffDutyPunchTime(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN);
                String offDutyPunchTime = DateUtils.dateToString(source.getOffDutyPunchTime(), CommonConstant.DATE_TO_STRING_HM_PATTERN);
                destination.setOffDutyPunchDateTime(offDutyPunchDateTime);
                destination.setOffDutyPunchTime(offDutyPunchTime);
            }
        }
    }
}
