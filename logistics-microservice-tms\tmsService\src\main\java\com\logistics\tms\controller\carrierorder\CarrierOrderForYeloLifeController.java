package com.logistics.tms.controller.carrierorder;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.carrierorder.CarrierOrderForYloLifeBiz;
import com.logistics.tms.controller.carrierorder.request.*;
import com.logistics.tms.controller.carrierorder.response.*;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/26 13:56
 */
@Api(value = "新生运单管理")
@RestController
public class CarrierOrderForYeloLifeController {

    @Resource
    private CarrierOrderForYloLifeBiz carrierOrderForYloLifeBiz;

    /**
     * 查询新生运单列表
     *
     * @param requestModel 筛选条件
     * @return 运单信息
     */
    @ApiOperation(value = "查询运单列表")
    @PostMapping(value = "/service/carrierOrderManagement/searchCarrierOrderListForYeloLife")
    public Result<PageInfo<SearchCarrierOrderListForYeloLifeResponseModel>> searchCarrierOrderListForYeloLife(@RequestBody SearchCarrierOrderListForYeloLifeRequestModel requestModel) {
        PageInfo<SearchCarrierOrderListForYeloLifeResponseModel> result = carrierOrderForYloLifeBiz.searchCarrierOrderListForYeloLife(requestModel);
        return Result.success(result);
    }

    /**
     * 导出运单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出运单")
    @PostMapping(value = "/service/carrierOrderManagement/exportCarrierOrderForYeloLife")
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchCarrierOrderListForYeloLifeResponseModel>> exportCarrierOrderForYeloLife(@RequestBody SearchCarrierOrderListForYeloLifeRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        return Result.success(carrierOrderForYloLifeBiz.searchCarrierOrderListForYeloLife(requestModel).getList());
    }

    /**
     * 运单列表统计 TAB
     *
     * @param requestModel 筛选条件
     * @return 运单TAB信息
     */
    @ApiOperation(value = "运单列表统计")
    @PostMapping(value = "/service/carrierOrderManagement/searchListStatisticsForYeloLife")
    public Result<SearchCarrierListStatisticsResponseModel> searchListStatisticsForYeloLife(@RequestBody SearchCarrierOrderListForYeloLifeRequestModel requestModel) {
        return Result.success(carrierOrderForYloLifeBiz.searchListStatisticsForYeloLife(requestModel));
    }

    /**
     * 查询新生运单详情
     *
     * @param requestModel 运单ID
     * @return 运单详情
     */
    @ApiOperation(value = "运单详情页")
    @PostMapping(value = "/service/carrierOrderManagement/carrierOrderDetailForYeloLife")
    public Result<CarrierOrderDetailForYeloLifeResponseModel> carrierOrderDetailForYeloLife(@RequestBody CarrierOrderIdRequestModel requestModel) {
        return Result.success(carrierOrderForYloLifeBiz.carrierOrderDetailForYeloLife(requestModel));
    }

    /**
     * 查询新生运单票据信息
     *
     * @param requestModel 运单ID
     * @return 运单票据
     */
    @ApiOperation(value = "查询票据")
    @PostMapping(value = "/service/carrierOrderManagement/getTicketsForYeloLife")
    public Result<List<TicketsModel>> getTicketsForYeloLife(@RequestBody CarrierOrderIdRequestModel requestModel) {
        return Result.success(carrierOrderForYloLifeBiz.getTicketsForYeloLife(requestModel));
    }

    /**
     * 提卸货详情
     *
     * @param requestModel 运单did
     * @return 提卸货详情
     */
    @ApiOperation(value = "查询提卸货详情")
    @PostMapping(value = "/service/carrierOrderManagement/getLoadDetailForYeloLife")
    public Result<LoadDetailForYeloLifeResponseModel> getLoadDetailForYeloLife(@RequestBody LoadDetailForYeloLifeRequestModel requestModel) {
        return Result.success(carrierOrderForYloLifeBiz.getLoadDetailForYeloLife(requestModel));
    }

    /**
     * 新生运单提货
     *
     * @param requestModel 提货信息
     * @return 操作结果
     */
    @ApiOperation(value = "提货")
    @PostMapping(value = "/service/carrierOrderManagement/loadForYeloLife")
    public Result loadForYeloLife(@RequestBody LoadForYeloLifeRequestModel requestModel) {
        return carrierOrderForYloLifeBiz.loadForYeloLife(requestModel);
    }

    /**
     * 新生运单卸货
     *
     * @param requestModel 卸货信息
     * @return 操作结果
     */
    @ApiOperation(value = "卸货")
    @PostMapping(value = "/service/carrierOrderManagement/unloadForYeloLife")
    public Result<Boolean> unloadForYeloLife(@RequestBody UnLoadForYeloLifeRequestModel requestModel) {
        carrierOrderForYloLifeBiz.unloadForYeloLife(requestModel);
        return Result.success(true);
    }

    /**
     * 查询签收确认详情-回收类型
     *
     * @param requestModel 运单ID
     * @return 签收确认详情
     */
    @ApiOperation(value = "查询签收确认详情")
    @PostMapping(value = "/service/carrierOrderManagement/carrierOrderListBeforeSignUpForYeloLife")
    public Result<SignDetailForYeloLifeResponseModel> carrierOrderListBeforeSignUpForYeloLife(@RequestBody CarrierOrderIdRequestModel requestModel) {
        return Result.success(carrierOrderForYloLifeBiz.carrierOrderListBeforeSignUpForYeloLife(requestModel));
    }

    /**
     * 签收-回收类型
     *
     * @param requestModel 签收信息
     * @return 操作结果
     */
    @ApiOperation(value = "签收")
    @PostMapping(value = "/service/carrierOrderManagement/carrierOrderSignUpForYeloLife")
    public Result<Boolean> carrierOrderSignUpForYeloLife(@RequestBody CarrierOrderSignUpForYeloLifeRequestModel requestModel) {
        carrierOrderForYloLifeBiz.carrierOrderSignUpForYeloLife(requestModel);
        return Result.success(true);
    }

    /**
     * 查询签收详情-销售类型
     *
     * @param requestModel 运单id
     * @return 签收详情
     */
    @PostMapping(value = "/service/carrierOrderManagement/carrierOrderBeforeSignUpForYeloLife")
    public Result<List<CarrierOrderBeforeSignUpForYeloLifeResponseModel>> carrierOrderBeforeSignUpForYeloLife(@RequestBody CarrierOrderBeforeSignUpForYeloLifeRequestModel requestModel) {
        return Result.success(carrierOrderForYloLifeBiz.carrierOrderBeforeSignUpForYeloLife(requestModel));
    }

    /**
     * 运单签收-销售类型
     *
     * @param requestModel 运单id,签收信息
     * @return 操作结果
     */
    @PostMapping(value = "/service/carrierOrderManagement/carrierOrderConfirmSignUpForYeloLife")
    public Result<Boolean> carrierOrderConfirmSignUpForYeloLife(@RequestBody CarrierOrderConfirmSignUpForYeloLifeRequestModel requestModel) {
        carrierOrderForYloLifeBiz.carrierOrderConfirmSignUpForYeloLife(requestModel);
        return Result.success(true);
    }

    /**
     * 修改卸货地址-查询新生仓库
     *
     * @param requestModel 筛选条件
     * @return 新生仓库列表
     */
    @ApiOperation(value = "修改卸货地址-查询新生仓库")
    @PostMapping(value = "/service/carrierOrderManagement/getYeloLifeUnloadWarehouse")
    public Result<List<GetYeloLifeUnloadWarehouseResponseModel>> getYeloLifeUnloadWarehouse(@RequestBody GetYeloLifeUnloadWarehouseRequestModel requestModel) {
        return Result.success(carrierOrderForYloLifeBiz.getYeloLifeUnloadWarehouse(requestModel));
    }

    /**
     * 修改卸货地址详情
     *
     * @param requestModel 运单ID
     * @return 现地址信息
     */
    @ApiOperation(value = "修改卸货地址详情")
    @PostMapping(value = "/service/carrierOrderManagement/getUnloadAddressDetailForYeloLife")
    public Result<UpdateUnloadAddressDetailResponseModel> getUnloadAddressDetailForYeloLife(@RequestBody CarrierOrderIdRequestModel requestModel) {
        return Result.success(carrierOrderForYloLifeBiz.getUnloadAddressDetailForYeloLife(requestModel));
    }

    /**
     * 确认修改卸货地址
     *
     * @param requestModel 新地址信息
     * @return 操作结果
     */
    @ApiOperation(value = "确认修改卸货地址")
    @PostMapping(value = "/service/carrierOrderManagement/updateUnloadAddressConfirmForYeloLife")
    public Result<Boolean> updateUnloadAddressConfirmForYeloLife(@RequestBody UpdateCarrierOrderUnloadAddressForLifeRequestModel requestModel) {
        carrierOrderForYloLifeBiz.updateUnloadAddressConfirmForYeloLife(requestModel);
        return Result.success(true);
    }

    /**
     * 查询运单列表(新生调用)
     *
     * @param requestModel 筛选条件
     * @return 运单列表
     */
    @ApiOperation(value = "查询运单列表(新生调用)")
    @PostMapping(value = "/service/carrierOrderManagement/searchYeloLifeCarrierOrder")
    public Result<PageInfo<SearchYeloLifeCarrierOrderResponseModel>> searchYeloLifeCarrierOrder(@RequestBody SearchYeloLifeCarrierOrderRequestModel requestModel) {
        return Result.success(carrierOrderForYloLifeBiz.searchYeloLifeCarrierOrder(requestModel));
    }

    /**
     * 查询运单详情(新生调用)
     *
     * @param requestModel 运单id
     * @return 运单详情
     */
    @ApiOperation(value = "查询运单详情(新生调用)")
    @PostMapping(value = "/service/carrierOrderManagement/searchYeloLifeCarrierOrderDetail")
    public Result<SearchYeloLifeCarrierOrderDetailResponseModel> searchYeloLifeCarrierOrderDetail(@RequestBody SearchYeloLifeCarrierOrderDetailRequestModel requestModel) {
        return Result.success(carrierOrderForYloLifeBiz.searchYeloLifeCarrierOrderDetail(requestModel));
    }
}
