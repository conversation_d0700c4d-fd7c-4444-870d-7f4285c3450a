package com.logistics.tms.controller.driversafepromise.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/11/18 15:18
 */
@Data
public class SafePromiseAppletDetailResponseModel {
    @ApiModelProperty("承诺书关系人员ID")
    private Long relationId;
    @ApiModelProperty("所属年份")
    private String period;
    @ApiModelProperty("签订状态: 0待签订、1已签订")
    private Integer status;
    @ApiModelProperty("发布时间")
    private Date publishTime;
    @ApiModelProperty("经办人")
    private String agent;
    @ApiModelProperty("承诺书内容")
    private String content;

    @ApiModelProperty("手持承诺书图片地址-相对路径")
    private String handPromiseUrl;
    @ApiModelProperty("签字责任书图片地址-相对路径")
    private String signResponsibilityUrl;
    @ApiModelProperty("附件路径")
    private String attachmentUrl= "";
    @ApiModelProperty(value = "签字时间")
    private Date signTime;
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;

}
