package com.logistics.management.webapi.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author:lei.zhu
 * @date:2021/4/9 13:33
 */
@Data
public class SettlementStatementRecordResponseDto {
    @ApiModelProperty("结算状态：0 待对账，1 待发送，2 待确认，3 待处理，4 待结清，5 部分结清，6 已结清")
    private String status="";
    @ApiModelProperty("结算状态文本")
    private String statusLabel="";
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty("账单月  年-月")
    private String settlementMonth="";
    @ApiModelProperty("司机姓名+手机号")
    private String driverName="";
    @ApiModelProperty("月应付运费")
    private String actualExpensesPayable="";
    @ApiModelProperty("已付费用")
    private String payFeeTotal="";
    @ApiModelProperty("未付运费")
    private String notPayMoney="";
    @ApiModelProperty("付款记录列表")
    List<SettleStatementRecordItemDto> itemList;
}
