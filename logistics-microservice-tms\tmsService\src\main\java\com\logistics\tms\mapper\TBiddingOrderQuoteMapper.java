package com.logistics.tms.mapper;

import com.logistics.tms.biz.biddingorder.bo.QuotePriceBo;
import com.logistics.tms.biz.biddingorder.bo.SelectBiddingOrderQuoteByConditionBo;
import com.logistics.tms.biz.biddingorder.bo.VehicleLengthBo;
import com.logistics.tms.entity.TBiddingOrderQuote;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/04/26
*/
@Mapper
public interface TBiddingOrderQuoteMapper extends BaseMapper<TBiddingOrderQuote> {

    TBiddingOrderQuote selectByPrimaryKeyDecrypt(Long id);

    int insertSelectiveEncrypt(TBiddingOrderQuote tBiddingOrderQuote);

    int updateByPrimaryKeySelectiveEncrypt(TBiddingOrderQuote tBiddingOrderQuote);

    /**
     * 查询竞价完成且被选中竞价单
     * @param biddingOrderIds
     * @return {@link List}<{@link QuotePriceBo}>
     */
    List<QuotePriceBo> selectEndCompleteQuotePrice(@Param("biddingOrderIds") List<Long> biddingOrderIds);

    /**
     * 通过条件查询报价单
     * @param orderQuoteByConditionBo
     * @return {@link List}<{@link TBiddingOrderQuote}>
     */
    List<TBiddingOrderQuote> selectBiddingOrderQuoteByCondition(SelectBiddingOrderQuoteByConditionBo orderQuoteByConditionBo);

    int emptyOrderQuote(@Param("biddingOrderId") Long biddingOrderId);

    /**
     * @param vehicleLengthId
     * @param biddingOrderIds
     * @return {@link List}<{@link VehicleLengthBo}>
     */
    List<VehicleLengthBo> selectEndCompleteVehicleLength(@Param("vehicleLengthId") Long vehicleLengthId,@Param("biddingOrderIds") List<Long> biddingOrderIds);
}