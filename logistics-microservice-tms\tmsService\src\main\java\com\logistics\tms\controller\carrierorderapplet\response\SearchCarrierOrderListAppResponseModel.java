package com.logistics.tms.controller.carrierorderapplet.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SearchCarrierOrderListAppResponseModel {

    @ApiModelProperty("运单ID")
    private Long carrierOrderId;
    @ApiModelProperty("需求单ID")
    private Long demandOrderId;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("运单状态10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0 已取消")
    private Integer status;
    private Integer ifCancel;
    @ApiModelProperty("是否放空 0 否 1 是")
    private Integer ifEmpty;
    @ApiModelProperty("起点")
    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    @ApiModelProperty("起点联系方式")
    private String loadMobile;
    @ApiModelProperty("提货经度")
    private String loadLongitude;
    @ApiModelProperty("提货纬度")
    private String loadLatitude;
    @ApiModelProperty("卸点")
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;
    @ApiModelProperty("卸点联系方式")
    private String unloadMobile;
    @ApiModelProperty("卸货时间")
    private Date unloadTime;
    @ApiModelProperty("卸货经度")
    private String unloadLongitude;
    @ApiModelProperty("卸货纬度")
    private String unloadLatitude;
    @ApiModelProperty("装货时间")
    private Date loadTime;
    @ApiModelProperty("预计提货时间")
    private Date expectArrivalTime;
    @ApiModelProperty("预计卸货时间")
    private Date expectedUnloadTime;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("货物信息")
    private List<CarrierOrderDetailGoodsInfoAppModel> goodList;

    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;

    @ApiModelProperty("中石化运单号")
    private String sinopecOrderNo;

    @ApiModelProperty("中石化下单类型：20 拉取，21 推送")
    private Integer orderType;

    //新生客户
    @ApiModelProperty("业务类型：1 公司，2 个人")
    private Integer businessType;

    @ApiModelProperty("乐橘新生客户名称,个人不填")
    private String customerName;

    @ApiModelProperty("个人客户姓名")
    private String customerUserName;

    @ApiModelProperty("个人客户手机号")
    private String customerUserMobile;

    @ApiModelProperty("下单人")
    private String publishName;

    @ApiModelProperty("下单人手机号")
    private String publishMobile;


    private Integer demandOrderSource;//需求单来源

    @ApiModelProperty("是否按编码回收 0:否 1:是   v2.44")
    private Integer ifRecycleByCode;

    @ApiModelProperty("是否是额外补的运单  0：否 1：是  V2.6.8")
    private Integer ifExtCarrierOrder;

    @ApiModelProperty("是否显示补单按钮  0：否 1：是  V2.6.8")
    private String enableExtCarrierOrder= "";

    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨；100 新生回收，101 新生销售")
    private Integer demandOrderEntrustType;
}
