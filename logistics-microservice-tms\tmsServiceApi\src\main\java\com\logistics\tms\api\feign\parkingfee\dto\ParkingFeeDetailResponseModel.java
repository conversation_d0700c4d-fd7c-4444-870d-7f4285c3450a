package com.logistics.tms.api.feign.parkingfee.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/10/9 15:33
 */
@Data
public class ParkingFeeDetailResponseModel {
    @ApiModelProperty("物理主键")
    private Long parkingFeeId;
    @ApiModelProperty("结算状态：0 待结算，1 部分结算，2 结算完成")
    private Integer status;
    @ApiModelProperty("车辆id")
    private Long vehicleId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机id")
    private Long staffId;
    @ApiModelProperty("司机姓名")
    private String name;
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("合作公司")
    private String cooperationCompany;
    @ApiModelProperty("起始日期")
    private Date startDate;
    @ApiModelProperty("截止时间")
    private Date endDate;
    @ApiModelProperty("终止时间")
    private Date finishDate;
    @ApiModelProperty("停车费")
    private BigDecimal parkingFee;
    @ApiModelProperty("合作周期（月）")
    private Integer cooperationPeriod;
    @ApiModelProperty("合作状态：1 已预付，2 进行中，3 已终止")
    private Integer cooperationStatus;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("创建人")
    private String createdBy;
    @ApiModelProperty("创建时间")
    private Date createdTime;
    @ApiModelProperty("最后修改人")
    private String lastModifiedBy;
    @ApiModelProperty("最后修改时间")
    private Date lastModifiedTime;
    @ApiModelProperty(value = "是否生成结算数据，1生成，0未生成")
    private Integer ifSettlement = 0;
    @ApiModelProperty("扣减历史")
    private List<ParkingFeeDeductingHistoryResponseModel> deductingHistoryList;
}
