package com.logistics.tms.client.feign.basicdata.map;

import com.logistics.tms.client.feign.FeignClientName;
import com.logistics.tms.client.feign.basicdata.map.hystrix.BasicMapServiceApiHystrix;
import com.logistics.tms.client.feign.basicdata.map.request.GetMapByLonLatReqFeignModel;
import com.logistics.tms.client.feign.basicdata.map.response.GetMapByLonLatRespFeignModel;
import com.yelo.basicdata.api.feign.datamap.model.GetMapByLonLatRequestModel;
import com.yelo.basicdata.api.feign.datamap.model.GetMapByLonLatResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2023/12/25 13:19
 */
@FeignClient(name = FeignClientName.BASIC_DATA_SERVICES, fallback = BasicMapServiceApiHystrix.class)
public interface MapServiceApi {

    @ApiOperation(value = "根据经纬度查询省市区信息")
    @PostMapping(value = "/service/datamap/getMapByLonLat")
    Result<GetMapByLonLatRespFeignModel> getMapByLonLat(@RequestBody GetMapByLonLatReqFeignModel requestModel);
}
