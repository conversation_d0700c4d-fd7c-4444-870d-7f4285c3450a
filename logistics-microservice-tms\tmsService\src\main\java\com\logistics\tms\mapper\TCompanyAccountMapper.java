package com.logistics.tms.mapper;

import com.logistics.tms.controller.companyaccount.request.SearchCompanyAccountRequestModel;
import com.logistics.tms.controller.companyaccount.response.SearchCompanyAccountResponseModel;
import com.logistics.tms.entity.TCompanyAccount;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Mybatis Generator on 2022/12/06
 */
@Mapper
public interface TCompanyAccountMapper extends BaseMapper<TCompanyAccount> {

	int insertSelectiveEncrypt(TCompanyAccount tCompanyAccount);

	List<SearchCompanyAccountResponseModel> searchList(SearchCompanyAccountRequestModel requestModel);

	TCompanyAccount selectByBankAccount(@Param("bankAccount") String bankAccount);
}