package com.logistics.tms.api.feign.personalaccidentinsurance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/6/4 17:00
 */
@Data
public class SearchInsuranceByPolicyNumberResponseModel {
    @ApiModelProperty("个人意外险表id")
    private Long personalAccidentInsuranceId;
    @ApiModelProperty("保单号")
    private String policyNumber;
    @ApiModelProperty("保险公司id")
    private Long insuranceCompanyId;
    @ApiModelProperty("保险公司名称")
    private String insuranceCompany;
}
