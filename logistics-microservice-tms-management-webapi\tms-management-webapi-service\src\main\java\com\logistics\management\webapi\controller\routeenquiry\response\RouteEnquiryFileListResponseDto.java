package com.logistics.management.webapi.controller.routeenquiry.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/7/9 9:45
 */
@Data
public class RouteEnquiryFileListResponseDto {

    /**
     * 操作时间
     */
    private String lastModifiedTime="";

    /**
     * 操作人
     */
    private String lastModifiedBy="";

    /**
     * 上传节点
     */
    private String uploadNode="";

    /**
     * 文件列表
     */
    private List<RouteEnquiryFileResponseDto> fileList=new ArrayList<>();

}
