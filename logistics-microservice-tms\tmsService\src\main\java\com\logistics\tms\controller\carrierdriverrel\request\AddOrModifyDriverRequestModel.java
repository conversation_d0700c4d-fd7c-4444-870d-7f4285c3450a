package com.logistics.tms.controller.carrierdriverrel.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/18
 */
@Data
public class AddOrModifyDriverRequestModel {

	@ApiModelProperty("人员基本信息表ID")
	private Long carrierDriverId;

	@ApiModelProperty(value = "人员姓名")
	private String name;

	@ApiModelProperty(value = "手机号")
	private String mobile;

	@ApiModelProperty(value = "身份证号码")
	private String identityNumber;
}
