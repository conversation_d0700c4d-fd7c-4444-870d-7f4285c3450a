package com.logistics.tms.controller.invoicingmanagement;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.invoicingmanagement.InvoicingManagementBiz;
import com.logistics.tms.controller.invoicingmanagement.request.*;
import com.logistics.tms.controller.invoicingmanagement.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/21 11:08
 */
@Api(value = "发票管理",tags = "发票管理")
@RestController
@RequestMapping(value = "/service/invoicingManagement")
public class InvoicingManagementController {

    @Resource
    private InvoicingManagementBiz invoicingManagementBiz;

    @ApiOperation(value = "发票管理-列表")
    @PostMapping(value = "/searchList")
    public Result<PageInfo<SearchInvoicingManagementListResponseModel>> searchList(@RequestBody SearchInvoicingManagementListRequestModel requestModel){
        return Result.success(invoicingManagementBiz.searchList(requestModel));
    }

    @ApiOperation(value = "发票管理-查看发票")
    @PostMapping(value = "/getInvoicePictures")
    public Result<List<GetInvoicePicturesResponseModel>> getInvoicePictures(@RequestBody InvoicingManagementDetailRequestModel requestModel){
        return Result.success(invoicingManagementBiz.getInvoicePictures(requestModel));
    }

    @ApiOperation(value = "发票管理-修改业务名称")
    @PostMapping(value = "/updateBusinessName")
    public Result<Boolean> updateBusinessName(@RequestBody UpdateBusinessNameRequestModel requestModel){
        invoicingManagementBiz.updateBusinessName(requestModel);
        return Result.success(true);
    }

    @ApiOperation(value = "发票管理-详情-头部信息")
    @PostMapping(value = "/getDetail")
    public Result<InvoicingManagementDetailResponseModel> getDetail(@RequestBody InvoicingManagementDetailRequestModel requestModel){
        return Result.success(invoicingManagementBiz.getDetail(requestModel));
    }

    @ApiOperation(value = "发票管理-详情-发票列表")
    @PostMapping(value = "/getInvoiceList")
    public Result<List<GetInvoiceListResponseModel>> getInvoiceList(@RequestBody InvoicingManagementDetailRequestModel requestModel){
        return Result.success(invoicingManagementBiz.getInvoiceList(requestModel));
    }

    @ApiOperation(value = "发票管理-详情-发票列表-查看详情")
    @PostMapping(value = "/getInvoiceDetail")
    public Result<GetInvoiceDetailResponseModel> getInvoiceDetail(@RequestBody GetInvoiceDetailRequestModel requestModel){
        return Result.success(invoicingManagementBiz.getInvoiceDetail(requestModel));
    }

    @ApiOperation(value = "发票管理-详情-发票列表-新增/编辑发票")
    @PostMapping(value = "/addOrModifyInvoice")
    public Result<Boolean> addOrModifyInvoice(@RequestBody AddOrModifyInvoiceRequestModel requestModel){
        invoicingManagementBiz.addOrModifyInvoice(requestModel);
        return Result.success(true);
    }

    @ApiOperation(value = "发票管理-详情-发票列表-删除发票")
    @PostMapping(value = "/delInvoice")
    public Result<Boolean> delInvoice(@RequestBody GetInvoiceDetailRequestModel requestModel){
        invoicingManagementBiz.delInvoice(requestModel);
        return Result.success(true);
    }

    @ApiOperation(value = "发票管理-详情-对账单列表")
    @PostMapping(value = "/getSettleStatementList")
    public Result<PageInfo<GetSettleStatementListResponseModel>> getSettleStatementList(@RequestBody GetSettleStatementListRequestModel requestModel){
        return Result.success(invoicingManagementBiz.getSettleStatementList(requestModel));
    }

    @ApiOperation(value = "发票管理-详情-对账单-添加对账单列表")
    @PostMapping(value = "/getAddSettleStatementList")
    public Result<PageInfo<GetAddSettleStatementListResponseModel>> getAddSettleStatementList(@RequestBody GetAddSettleStatementListRequestModel requestModel){
        return Result.success(invoicingManagementBiz.getAddSettleStatementList(requestModel));
    }

    @ApiOperation(value = "发票管理-详情-对账单-添加对账单")
    @PostMapping(value = "/addSettleStatement")
    public Result<Boolean> addSettleStatement(@RequestBody AddInvoicingSettleStatementRequestModel requestModel){
        invoicingManagementBiz.addSettleStatement(requestModel);
        return Result.success(true);
    }

    @ApiOperation(value = "发票管理-详情-对账单-移除对账单")
    @PostMapping(value = "/delSettleStatement")
    public Result<Boolean> delSettleStatement(@RequestBody DelSettleStatementRequestModel requestModel){
        invoicingManagementBiz.delSettleStatement(requestModel);
        return Result.success(true);
    }

    @ApiOperation(value = "发票管理-详情-归档列表")
    @PostMapping(value = "/getInvoicingArchiveList")
    public Result<List<GetInvoicingArchiveListResponseModel>> getInvoicingArchiveList(@RequestBody InvoicingManagementDetailRequestModel requestModel){
        return Result.success(invoicingManagementBiz.getInvoicingArchiveList(requestModel));
    }

    @ApiOperation(value = "发票管理-详情-确认归档")
    @PostMapping(value = "/invoicingArchive")
    public Result<Boolean> invoicingArchive(@RequestBody InvoicingArchiveRequestModel requestModel){
        invoicingManagementBiz.invoicingArchive(requestModel);
        return Result.success(true);
    }

}
