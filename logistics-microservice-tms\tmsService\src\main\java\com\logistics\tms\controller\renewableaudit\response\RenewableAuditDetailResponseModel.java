package com.logistics.tms.controller.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class RenewableAuditDetailResponseModel {

    @ApiModelProperty(value = "审核表id")
    private Long renewableOrderId;

    @ApiModelProperty(value = "单号")
    private String renewableOrderCode;

    @ApiModelProperty(value = "订单状态:0:待指派,1:待确认,2:待审核,3:已审核,4:已取消")
    private Integer status;

    @ApiModelProperty(value = "订单来源:0:新生同步,1:司机下单")
    private Integer source;

    @ApiModelProperty(value = "业务类型：1 公司，2 个人")
    private Integer businessType;
    @ApiModelProperty("乐橘新生客户名称（企业）")
    private String customerName;
    @ApiModelProperty("乐橘新生客户姓名（个人）")
    private String customerUserName;
    @ApiModelProperty("乐橘新生客户手机号（个人）")
    private String customerUserMobile;

    @ApiModelProperty(value = "下单人")
    private String publishUserName;
    @ApiModelProperty(value = "下单人手机号")
    private String publishUserMobile;

    @ApiModelProperty(value = "下单时间")
    private Date publishTime;

    @ApiModelProperty(value = "司机姓名")
    private String staffName;

    @ApiModelProperty(value = "司机手机号")
    private String staffMobile;

    @ApiModelProperty(value = "期望提货时间")
    private Date expectedLoadTime;

    @ApiModelProperty(value = "发货仓库")
    private String loadWarehouse;

    @ApiModelProperty(value = "发货省份")
    private String loadProvinceName;

    @ApiModelProperty(value = "发货城市")
    private String loadCityName;

    @ApiModelProperty(value = "发货区县")
    private String loadAreaName;

    @ApiModelProperty(value = "发货详细地址")
    private String loadDetailAddress;

    @ApiModelProperty(value = "发货人")
    private String consignorName;

    @ApiModelProperty(value = "发货人手机号")
    private String consignorMobile;

    @ApiModelProperty(value = "收货仓库")
    private String unloadWarehouse;

    @ApiModelProperty(value = "收货省份")
    private String unloadProvinceName;

    @ApiModelProperty(value = "收货城市")
    private String unloadCityName;

    @ApiModelProperty(value = "收货区县")
    private String unloadAreaName;

    @ApiModelProperty(value = "收货详细地址")
    private String unloadDetailAddress;

    @ApiModelProperty(value = "收货人")
    private String receiverName;

    @ApiModelProperty(value = "收货人手机号")
    private String receiverMobile;

    @ApiModelProperty(value = "货物信息")
    private List<RenewableAuditGoodsResponseModel> goodsResponseModelList;

    @ApiModelProperty(value = "备注")
    private String remark;

}
