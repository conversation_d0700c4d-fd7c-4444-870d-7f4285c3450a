package com.logistics.tms.mapper;

import com.logistics.tms.entity.TCarrierOrderCorrect;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TCarrierOrderCorrectMapper extends BaseMapper<TCarrierOrderCorrect> {

    List<TCarrierOrderCorrect> getByCarrierOrderIds(@Param("carrierOrderIds") String carrierOrderIds);

    TCarrierOrderCorrect getByCarrierOrderId(@Param("carrierOrderId") Long carrierOrderId);
}