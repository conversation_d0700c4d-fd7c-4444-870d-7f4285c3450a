package com.logistics.management.webapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * liang current user system login name
 * 2018/9/28 current system date
 */
@Data
public class CancelCarrierOrderRequestModel {

    @ApiModelProperty("运单ID; 批量','分隔")
    private String carrierOrderId;

    @ApiModelProperty("取消原因")
    private String cancelReason;

    @ApiModelProperty("来源：1 后台，2 前台 5外部系统")
    private Integer source;
}
