package com.logistics.tms.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2021/12/4 14:00
 */
@Data
public class SinopecDemandOrderCancelRequestModel {
    @ApiModelProperty("委托单id 批量字符串")
    private String demandIds;

    @ApiModelProperty("调度人员姓名")
    private String dispatcherName;
    @ApiModelProperty("调度人员电话")
    private String dispatcherPhone;
    @ApiModelProperty("取消原因")
    private String cancelReason;

    @ApiModelProperty("原因类型, 1-承运商原因、2-托运人原因、3-装货人原因、4-政府政策原因、5-不可抗力原因")
    private Integer cancelReasonType;
}
