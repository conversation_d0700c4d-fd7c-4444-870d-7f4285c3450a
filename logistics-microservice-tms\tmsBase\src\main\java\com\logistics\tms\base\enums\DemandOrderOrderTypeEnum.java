package com.logistics.tms.base.enums;

public enum DemandOrderOrderTypeEnum {

    DEFAULT(0,""),
    PULL(20,"拉取"),
    PUSH(21,"推送"),
    ;

    private Integer key;
    private String value;

    DemandOrderOrderTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
    public static DemandOrderOrderTypeEnum getEnum(Integer key) {
        for (DemandOrderOrderTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
