package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2022/09/02
*/
@Data
public class TCarrierOrderOtherFeeItem extends BaseEntity {
    /**
    * 运单其他费用主键
    */
    @ApiModelProperty("运单其他费用主键")
    private Long carrierOrderOtherFeeId;

    /**
    * 费用金额
    */
    @ApiModelProperty("费用金额")
    private BigDecimal feeAmount;

    /**
    * 费用类型：1 短驳费，2 保管费，3 装卸费，4 压车费，5 质量处罚费，6 其他杂费
    */
    @ApiModelProperty("费用类型：1 短驳费，2 保管费，3 装卸费，4 压车费，5 质量处罚费，6 其他杂费")
    private Integer feeType;



    /**
     * 费用需求 1:我司 2.客户
     */
    @ApiModelProperty("费用需求 1:我司 2.客户")
    private Integer feeSource;
}