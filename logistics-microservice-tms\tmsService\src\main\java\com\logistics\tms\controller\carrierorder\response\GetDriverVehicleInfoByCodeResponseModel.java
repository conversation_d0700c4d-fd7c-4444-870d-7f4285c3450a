package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2023/9/26 13:45
 */
@Data
public class GetDriverVehicleInfoByCodeResponseModel {
    @ApiModelProperty(value = "运单号")
    private String carrierOrderCode;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;

    @ApiModelProperty(value = "挂车号")
    private String trailerVehicleNo;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "司机手机号")
    private String driverMobile;

    @ApiModelProperty(value = "司机身份证号")
    private String driverIdentity;
}
