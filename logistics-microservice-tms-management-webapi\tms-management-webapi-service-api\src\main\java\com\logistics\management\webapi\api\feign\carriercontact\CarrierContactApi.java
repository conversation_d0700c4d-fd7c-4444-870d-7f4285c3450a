package com.logistics.management.webapi.api.feign.carriercontact;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.carriercontact.dto.*;
import com.logistics.management.webapi.api.feign.carriercontact.hystrix.CarrierContactApiHystrix;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * @Author: sj
 * @Date: 2019/10/14 13:33
 */
@Api(value = "API-CarrierContactApi-车主账号管理")
@FeignClient(name = "logistics-tms-managementWeb-api",fallback = CarrierContactApiHystrix.class)
public interface CarrierContactApi {

    @ApiOperation(value = "车主账号列表v1.1.9")
    @PostMapping(value = "/api/carrierContact/searchList")
    Result<PageInfo<SearchCarrierContactResponseDto>> searchList(@RequestBody SearchCarrierContactRequestDto requestDto );

    @ApiOperation(value = "导出车主账号v1.1.9")
    @GetMapping(value = "/api/carrierContact/exportCarrierContact")
    void exportCarrierContact(SearchCarrierContactRequestDto requestDto , HttpServletResponse response);

    @ApiOperation(value = "查看详情v1.1.9")
    @PostMapping(value = "/api/carrierContact/getDetail")
    Result<CarrierContactDetailResponseDto> getDetail(@RequestBody @Valid CarrierContactDetailRequestDto requestDto);

    @ApiOperation(value = "新增编辑车主账号v1.1.9")
    @PostMapping(value = "/api/carrierContact/saveAccount")
    Result<Boolean> saveAccount(@RequestBody @Valid SaveCarrierContactRequestDto requestDto);

    @ApiOperation(value = "禁用启用车主账号v1.1.9")
    @PostMapping(value = "/api/carrierContact/enableDisableClosed")
    Result<Boolean> enableDisableClosed(@RequestBody @Valid CarrierContactEnableRequestDto requestDto);

    @ApiOperation(value = "删除车主账号v1.1.9")
    @PostMapping(value = "/api/carrierAccount/delCarrierAccount")
    Result<Boolean> delCarrierAccount(@RequestBody @Valid DelCarrierContactRequestDto requestDto);

}
