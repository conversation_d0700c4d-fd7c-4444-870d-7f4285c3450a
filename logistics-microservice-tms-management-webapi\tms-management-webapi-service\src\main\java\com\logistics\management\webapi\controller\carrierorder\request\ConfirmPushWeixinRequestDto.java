package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ConfirmPushWeixinRequestDto {

    @ApiModelProperty("运单号")
    private String carrierOrderId;
    @ApiModelProperty("手机号 逗号分隔")
    private String mobiles;
    @ApiModelProperty("推送信息")
    private List<ConfirmPushWeixinContactItemsRequestDto> pushContacts;
}
