package com.logistics.tms.biz.driverfreight


import com.logistics.tms.api.feign.driverfreight.model.DriverFreightListSearchRequestModel
import com.logistics.tms.api.feign.driverfreight.model.DriverFreightListSearchResponseModel
import com.logistics.tms.entity.TVehicleBasic
import com.logistics.tms.mapper.TCarrierOrderMapper
import com.logistics.tms.mapper.TVehicleBasicMapper
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class DriverFreightBizTest extends Specification {
    @Mock
    TCarrierOrderMapper tCarrierOrderMapper
    @Mock
    TVehicleBasicMapper tVehicleBasicMapper
    @InjectMocks
    DriverFreightBiz driverFreightBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "driver Freight List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCarrierOrderMapper.searchDriverFreightOrdersIds(any())).thenReturn([1l])
        when(tCarrierOrderMapper.searchDriverFreightOrdersByIds(anyString())).thenReturn([new DriverFreightListSearchResponseModel()])
        when(tVehicleBasicMapper.getByIds(anyString())).thenReturn([new TVehicleBasic(vehicleProperty: 0)])

        expect:
        driverFreightBiz.driverFreightList(requestModel) == expectedResult

        where:
        requestModel                              || expectedResult
        new DriverFreightListSearchRequestModel() || null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme