package com.logistics.tms.api.feign.gpsfee.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/10/8 13:32
 */
@Data
public class SearchGpsFeeListCountResponseModel {
    @ApiModelProperty("总数量")
    private Integer allSettlementCount;
    @ApiModelProperty("待结算数量")
    private Integer waitSettlementCount;
    @ApiModelProperty("部分结算数量")
    private Integer partSettlementCount;
    @ApiModelProperty("结算完成数量")
    private Integer completeSettlementCount;
    @ApiModelProperty("已终止数量")
    private Integer terminationCount;
}
