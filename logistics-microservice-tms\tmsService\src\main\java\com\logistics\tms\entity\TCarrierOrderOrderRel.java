package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TCarrierOrderOrderRel extends BaseEntity {
    /**
    * 运单ID
    */
    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    /**
     * 需求单与s单关系表id
     */
    @ApiModelProperty("需求单与s单关系表id")
    private Long demandOrderOrderId;

    /**
    * 销售单/补货单订单ID
    */
    @ApiModelProperty("销售单/补货单订单ID")
    private Long orderId;

    /**
    * 销售单/补货单号
    */
    @ApiModelProperty("销售单/补货单号")
    private String orderCode;

    /**
    * 运单分配到的S单的预计数量
    */
    @ApiModelProperty("运单分配到的S单的预计数量")
    private BigDecimal expectAmount;

    /**
    * 装货数量/实提数量
    */
    @ApiModelProperty("装货数量/实提数量")
    private BigDecimal loadAmount;

    /**
    * 卸货数量/实卸数量
    */
    @ApiModelProperty("卸货数量/实卸数量")
    private BigDecimal unloadAmount;

    /**
    * 签收件数
    */
    @ApiModelProperty("签收件数")
    private BigDecimal signAmount;

    /**
    * 类型 1 下单   2 追加   3 补单
    */
    @ApiModelProperty("类型 1 下单   2 追加   3 补单")
    private Integer relType;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}