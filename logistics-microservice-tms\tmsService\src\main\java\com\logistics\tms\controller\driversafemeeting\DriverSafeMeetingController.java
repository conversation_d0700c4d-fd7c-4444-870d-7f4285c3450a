package com.logistics.tms.controller.driversafemeeting;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.driversafemeeting.DriverSafeMeetingBiz;
import com.logistics.tms.controller.driversafemeeting.request.*;
import com.logistics.tms.controller.driversafemeeting.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/11 14:47
 */
@RestController
@RequestMapping(value = "/service/driverSafeMeeting")
public class DriverSafeMeetingController {

    @Resource
    private DriverSafeMeetingBiz driverSafeMeetingBiz;

    /**
     * 例会看板（列表）
     * @param requestModel
     * @return
     */
    @ApiOperation(("例会看板（列表）"))
    @PostMapping(value = "/driverSafeMeetingKanBan")
    public Result<List<DriverSafeMeetingKanBanResponseModel>> driverSafeMeetingKanBan(@RequestBody DriverSafeMeetingKanBanRequestModel requestModel) {
        return Result.success(driverSafeMeetingBiz.driverSafeMeetingKanBan(requestModel));
    }

    /**
     * 新增例会
     * @param requestModel
     * @return
     */
    @ApiOperation(("新增例会"))
    @PostMapping(value = "/addDriverSafeMeeting")
    public Result addDriverSafeMeeting(@RequestBody AddDriverSafeMeetingRequestModel requestModel) {
        driverSafeMeetingBiz.addDriverSafeMeeting(requestModel);
        return Result.success(true);
    }

    /**
     * 重新编辑例会
     * @param requestModel
     * @return
     */
    @ApiOperation(("重新编辑例会"))
    @PostMapping(value = "/modifyDriverSafeMeeting")
    public Result modifyDriverSafeMeeting(@RequestBody ModifyDriverSafeMeetingRequestModel requestModel) {
        driverSafeMeetingBiz.modifyDriverSafeMeeting(requestModel);
        return Result.success(true);
    }

    /**
     * 补发例会
     * @param requestModel
     * @return
     */
    @ApiOperation(("补发例会"))
    @PostMapping(value = "/replacementDriverSafeMeeting")
    public Result replacementDriverSafeMeeting(@RequestBody ReplacementDriverSafeMeetingRequestModel requestModel) {
        driverSafeMeetingBiz.replacementDriverSafeMeeting(requestModel);
        return Result.success(true);
    }

    /**
     * 例会内容详情
     * @param requestModel
     * @return
     */
    @ApiOperation(("例会内容详情"))
    @PostMapping(value = "/driverSafeMeetingContentDetail")
    public Result<DriverSafeMeetingContentDetailResponseModel> driverSafeMeetingContentDetail(@RequestBody DriverSafeMeetingIdRequestModel requestModel) {
        return Result.success(driverSafeMeetingBiz.driverSafeMeetingContentDetail(requestModel));
    }

    /**
     * 学习详情（司机学习列表）
     * @param requestModel
     * @return
     */
    @ApiOperation(("学习详情（司机学习列表）"))
    @PostMapping(value = "/driverSafeMeetingDetailList")
    public Result<PageInfo<DriverSafeMeetingDetailResponseModel>> driverSafeMeetingDetailList(@RequestBody DriverSafeMeetingDetailRequestModel requestModel) {
        return Result.success(driverSafeMeetingBiz.driverSafeMeetingDetailList(requestModel));
    }

    /**
     * 学习详情（司机学习列表统计人数）
     * @param requestModel
     * @return
     */
    @ApiOperation(("学习详情（司机学习列表统计人数）"))
    @PostMapping(value = "/driverSafeMeetingListCount")
    public Result<DriverSafeMeetingListCountResponseModel> driverSafeMeetingListCount(@RequestBody DriverSafeMeetingDetailRequestModel requestModel) {
        return Result.success(driverSafeMeetingBiz.driverSafeMeetingListCount(requestModel));
    }

    /**
     * 删除学习详情
     * @param requestModel
     * @return
     */
    @ApiOperation(("删除学习详情"))
    @PostMapping(value = "/delDriverSafeMeetingRelation")
    public Result delDriverSafeMeetingRelation(@RequestBody DriverSafeMeetingRelationIdRequestModel requestModel) {
        driverSafeMeetingBiz.delDriverSafeMeetingRelation(requestModel);
        return Result.success(true);
    }

    /**
     * 小程序学习列表
     * @param requestModel
     * @return
     */
    @ApiOperation(("小程序学习列表"))
    @PostMapping(value = "/appletSafeMeetingList")
    public Result<PageInfo<AppletSafeMeetingListResponseModel>> appletSafeMeetingList(@RequestBody AppletSafeMeetingListRequestModel requestModel) {
        return Result.success(driverSafeMeetingBiz.appletSafeMeetingList(requestModel));
    }

    /**
     * 小程序学习列表数量统计
     * @return
     */
    @ApiOperation(("小程序学习列表数量统计"))
    @PostMapping(value = "/appletSafeMeetingListCount")
    public Result<DriverSafeMeetingListCountResponseModel> appletSafeMeetingListCount() {
        return Result.success(driverSafeMeetingBiz.appletSafeMeetingListCount());
    }

    /**
     * 小程序学习详情
     * @param requestModel
     * @return
     */
    @ApiOperation(("小程序学习详情"))
    @PostMapping(value = "/appletSafeMeetingDetail")
    public Result<AppletSafeMeetingDetailResponseModel> appletSafeMeetingDetail(@RequestBody DriverSafeMeetingRelationIdRequestModel requestModel) {
        return Result.success(driverSafeMeetingBiz.appletSafeMeetingDetail(requestModel));
    }

    /**
     * 小程序提交学习
     * @param requestModel
     * @return
     */
    @ApiOperation(("小程序提交学习"))
    @PostMapping(value = "/appletConfirmLeaning")
    public Result appletConfirmLeaning(@RequestBody AppletConfirmLeaningRequestModel requestModel) {
        driverSafeMeetingBiz.appletConfirmLeaning(requestModel);
        return Result.success(true);
    }
}
