package com.logistics.appapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class ArrivePickUpV2RequestDto {

    @ApiModelProperty(value = "运单Id", required = true)
    @NotBlank(message = "运单Id不能为空")
    private String carrierOrderId;

    @ApiModelProperty(value = "1.1.6修改字段名；触达定位经度", required = true)
    @NotBlank(message = "触达定位不能为空")
    private String reachLongitude;

    @ApiModelProperty(value = "1.1.6修改字段名；触达定位纬度", required = true)
    @NotBlank(message = "触达定位不能为空")
    private String reachLatitude;

    @ApiModelProperty(value = "触达详细地址", required = true)
    @NotBlank(message = "触达详细地址不能为空")
    private String reachAddressDetail;

    @ApiModelProperty(value = "触达抬头")
    private String terminalHead;

    @ApiModelProperty(value = "触达地址备注")
    private String reachAddressRemark;

    @ApiModelProperty(value = "联系人类型: 0、无误 1、有误", required = true)
    @NotBlank(message = "请选择联系人类型")
    private String checkReachContact;

    @ApiModelProperty(value = "触达联系人")
    private String reachContactor;

    @ApiModelProperty(value = "触达联系方式")
    private String reachTelephone;

    @ApiModelProperty(value = "门头图片临时路径")
    private List<String> terminalTmpUrl;

}
