package com.logistics.tms.controller.vehiclesafecheck.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 列表
 * @Author: sj
 * @Date: 2019/11/6 13:05
 */
@Data
public class SearchSafeCheckListResponseModel {
    @ApiModelProperty("车辆检车ID")
    private Long safeCheckVehicleId;
    @ApiModelProperty("检查年月")
    private String period;
    @ApiModelProperty("状态：0未检查，10待确认，20待整改，30已整改，40检查完成")
    private Integer status;
    @ApiModelProperty("整改项")
    private Integer reformCount;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆机构：1 自主 2 外部 3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("挂车车牌号")
    private String trailerVehicleNo;
    @ApiModelProperty("司机名称")
    private String staffName;
    @ApiModelProperty("司机电话")
    private String staffMobile;
    @ApiModelProperty("检查人")
    private String checkUserName;
    @ApiModelProperty("检查时间")
    private Date checkTime;
}
