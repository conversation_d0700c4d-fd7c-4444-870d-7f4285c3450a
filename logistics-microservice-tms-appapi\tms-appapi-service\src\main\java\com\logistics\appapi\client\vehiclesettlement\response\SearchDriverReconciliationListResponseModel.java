package com.logistics.appapi.client.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author：wjf
 * @date：2021/4/12 13:09
 */
@Data
public class SearchDriverReconciliationListResponseModel {
    @ApiModelProperty("结算id")
    private Long vehicleSettlementId;
    @ApiModelProperty("结算状态：空 全部，2 待确认，3 待处理，4 待结清,5 部分结清，6 已结清")
    private Integer status;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("月份（yyyy-MM）")
    private String settlementMonth;
    @ApiModelProperty("运单数量")
    private Integer carrierOrderCount;
    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;
    @ApiModelProperty("应收费用")
    private BigDecimal actualExpensesPayable;
}
