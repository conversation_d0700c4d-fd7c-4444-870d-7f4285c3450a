<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderGoodsMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCarrierOrderGoods">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="carrier_order_id" jdbcType="BIGINT" property="carrierOrderId" />
    <result column="demand_order_goods_id" jdbcType="BIGINT" property="demandOrderGoodsId" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="length" jdbcType="INTEGER" property="length" />
    <result column="width" jdbcType="INTEGER" property="width" />
    <result column="height" jdbcType="INTEGER" property="height" />
    <result column="goods_size" jdbcType="VARCHAR" property="goodsSize" />
    <result column="expect_amount" jdbcType="DECIMAL" property="expectAmount" />
    <result column="load_amount" jdbcType="DECIMAL" property="loadAmount" />
    <result column="unload_amount" jdbcType="DECIMAL" property="unloadAmount" />
    <result column="sign_amount" jdbcType="DECIMAL" property="signAmount" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, carrier_order_id, demand_order_goods_id, sku_code, goods_name, category_name, 
    length, width, height, goods_size, expect_amount, load_amount, unload_amount, sign_amount, 
    created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_carrier_order_goods
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_carrier_order_goods
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCarrierOrderGoods">
    insert into t_carrier_order_goods (id, carrier_order_id, demand_order_goods_id, 
      sku_code, goods_name, category_name, 
      length, width, height, 
      goods_size, expect_amount, load_amount, 
      unload_amount, sign_amount, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{carrierOrderId,jdbcType=BIGINT}, #{demandOrderGoodsId,jdbcType=BIGINT}, 
      #{skuCode,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, #{categoryName,jdbcType=VARCHAR}, 
      #{length,jdbcType=INTEGER}, #{width,jdbcType=INTEGER}, #{height,jdbcType=INTEGER}, 
      #{goodsSize,jdbcType=VARCHAR}, #{expectAmount,jdbcType=DECIMAL}, #{loadAmount,jdbcType=DECIMAL}, 
      #{unloadAmount,jdbcType=DECIMAL}, #{signAmount,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCarrierOrderGoods">
    insert into t_carrier_order_goods
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="carrierOrderId != null">
        carrier_order_id,
      </if>
      <if test="demandOrderGoodsId != null">
        demand_order_goods_id,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="length != null">
        length,
      </if>
      <if test="width != null">
        width,
      </if>
      <if test="height != null">
        height,
      </if>
      <if test="goodsSize != null">
        goods_size,
      </if>
      <if test="expectAmount != null">
        expect_amount,
      </if>
      <if test="loadAmount != null">
        load_amount,
      </if>
      <if test="unloadAmount != null">
        unload_amount,
      </if>
      <if test="signAmount != null">
        sign_amount,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderId != null">
        #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderGoodsId != null">
        #{demandOrderGoodsId,jdbcType=BIGINT},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="length != null">
        #{length,jdbcType=INTEGER},
      </if>
      <if test="width != null">
        #{width,jdbcType=INTEGER},
      </if>
      <if test="height != null">
        #{height,jdbcType=INTEGER},
      </if>
      <if test="goodsSize != null">
        #{goodsSize,jdbcType=VARCHAR},
      </if>
      <if test="expectAmount != null">
        #{expectAmount,jdbcType=DECIMAL},
      </if>
      <if test="loadAmount != null">
        #{loadAmount,jdbcType=DECIMAL},
      </if>
      <if test="unloadAmount != null">
        #{unloadAmount,jdbcType=DECIMAL},
      </if>
      <if test="signAmount != null">
        #{signAmount,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCarrierOrderGoods">
    update t_carrier_order_goods
    <set>
      <if test="carrierOrderId != null">
        carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderGoodsId != null">
        demand_order_goods_id = #{demandOrderGoodsId,jdbcType=BIGINT},
      </if>
      <if test="skuCode != null">
        sku_code = #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="length != null">
        length = #{length,jdbcType=INTEGER},
      </if>
      <if test="width != null">
        width = #{width,jdbcType=INTEGER},
      </if>
      <if test="height != null">
        height = #{height,jdbcType=INTEGER},
      </if>
      <if test="goodsSize != null">
        goods_size = #{goodsSize,jdbcType=VARCHAR},
      </if>
      <if test="expectAmount != null">
        expect_amount = #{expectAmount,jdbcType=DECIMAL},
      </if>
      <if test="loadAmount != null">
        load_amount = #{loadAmount,jdbcType=DECIMAL},
      </if>
      <if test="unloadAmount != null">
        unload_amount = #{unloadAmount,jdbcType=DECIMAL},
      </if>
      <if test="signAmount != null">
        sign_amount = #{signAmount,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCarrierOrderGoods">
    update t_carrier_order_goods
    set carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      demand_order_goods_id = #{demandOrderGoodsId,jdbcType=BIGINT},
      sku_code = #{skuCode,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      category_name = #{categoryName,jdbcType=VARCHAR},
      length = #{length,jdbcType=INTEGER},
      width = #{width,jdbcType=INTEGER},
      height = #{height,jdbcType=INTEGER},
      goods_size = #{goodsSize,jdbcType=VARCHAR},
      expect_amount = #{expectAmount,jdbcType=DECIMAL},
      load_amount = #{loadAmount,jdbcType=DECIMAL},
      unload_amount = #{unloadAmount,jdbcType=DECIMAL},
      sign_amount = #{signAmount,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>