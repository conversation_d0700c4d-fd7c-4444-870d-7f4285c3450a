package com.logistics.tms.mapper;

import com.logistics.tms.controller.carrierorder.response.CarrierOrderOrdersResponseModel;
import com.logistics.tms.entity.TCarrierOrderOrderRel;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TCarrierOrderOrderRelMapper extends BaseMapper<TCarrierOrderOrderRel> {

    int batchInsertSelective(@Param("list") List<TCarrierOrderOrderRel> list);

    List<TCarrierOrderOrderRel> selectCarrierOrderRelsByCarrierOrderIds(@Param("carrierOrderIds") String carrierOrderIds);

    int batchUpdateSelective(@Param("list")List<TCarrierOrderOrderRel> tCarrierOrderOrderRels);

    List<CarrierOrderOrdersResponseModel> getCarrierOrderOrdersByCarrierCode(@Param("carrierOrderId") Long carrierOrderId);
}