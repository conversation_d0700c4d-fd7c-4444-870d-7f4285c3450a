package com.logistics.management.webapi.controller.invoicingmanagement.packaging.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2024/3/20 9:11
 */
@Data
public class GetAddSettleStatementListRequestDto extends AbstractPageForm<GetAddSettleStatementListRequestDto> {
    @ApiModelProperty(value = "承运商id",required = true)
    @NotBlank(message = "承运商id不能为空")
    private String companyCarrierId;

    @ApiModelProperty("对账月份起")
    private String settleStatementMonthStart;
    @ApiModelProperty("对账月份止")
    private String settleStatementMonthEnd;
}
