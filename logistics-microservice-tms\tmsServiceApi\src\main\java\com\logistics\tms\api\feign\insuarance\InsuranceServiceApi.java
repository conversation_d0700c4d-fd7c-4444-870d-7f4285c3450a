package com.logistics.tms.api.feign.insuarance;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.insuarance.hystrix.InsuranceServiceApiHystrix;
import com.logistics.tms.api.feign.insuarance.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/6/4 19:13
 */
@Api(value = "API-InsuranceServiceApi-保险管理")
@FeignClient(name = "logistics-tms-services", fallback = InsuranceServiceApiHystrix.class)
public interface InsuranceServiceApi {

    @ApiOperation(value = "保险管理列表")
    @PostMapping(value = "/service/insurance/searchInsuranceList")
    Result<PageInfo<SearchInsuranceListResponseModel>> searchInsuranceList(@RequestBody SearchInsuranceListRequestModel requestModel);

    @ApiOperation(value = "保险详情")
    @PostMapping(value = "/service/insurance/getInsuranceDetail")
    Result<GetInsuranceDetailResponseModel> getInsuranceDetail(@RequestBody InsuranceIdRequestModel requestModel);

    @ApiOperation(value = "新增/修改保险")
    @PostMapping(value = "/service/insurance/addOrModifyInsurance")
    Result addOrModifyInsurance(@RequestBody AddOrModifyInsuranceRequestModel requestModel);

    @ApiOperation(value = "作废保险")
    @PostMapping(value = "/service/insurance/cancelInsurance")
    Result cancelInsurance(@RequestBody CancelInsuranceRequestModel requestModel);

    @ApiOperation(value = "导出保险")
    @PostMapping(value = "/service/insurance/exportInsurance")
    Result<List<SearchInsuranceListResponseModel>> exportInsurance(@RequestBody SearchInsuranceListRequestModel requestModel);

    @ApiOperation(value = "导入保险")
    @PostMapping(value = "/service/insurance/importInsurance")
    Result<ImportInsuranceResponseModel> importInsurance(@RequestBody ImportInsuranceRequestModel requestModel);

    @ApiOperation(value = "导入保险证件信息")
    @PostMapping(value = "/service/insurance/importInsuranceCertificateInfo")
    Result importInsuranceCertificateInfo(@RequestBody ImportInsuranceCertificateRequestModel requestModel);

    @ApiOperation(value = "根据车辆id查询保险信息（车辆退保页面）")
    @PostMapping(value = "/service/insurance/getInsuranceInfoByVehicleId")
    Result<GetInsuranceInfoByVehicleIdResponseModel> getInsuranceInfoByVehicleId(@RequestBody GetInsuranceInfoByVehicleIdRequestModel requestModel);

    @ApiOperation(value = "确认退保")
    @PostMapping(value = "/service/insurance/confirmRefund")
    Result confirmRefund(@RequestBody ConfirmRefundRequestModel requestModel);
}
