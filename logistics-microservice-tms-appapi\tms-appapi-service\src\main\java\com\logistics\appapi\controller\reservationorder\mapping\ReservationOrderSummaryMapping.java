package com.logistics.appapi.controller.reservationorder.mapping;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.ReservationTypeEnum;
import com.logistics.appapi.client.reservationorder.response.ReservationOrderSummaryListResponseModel;
import com.logistics.appapi.client.reservationorder.response.ReservationOrderSummaryResponseModel;
import com.logistics.appapi.controller.reservationorder.response.ReservationOrderSummaryListResponseDto;
import com.logistics.appapi.controller.reservationorder.response.ReservationOrderSummaryResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/22 13:17
 */
public class ReservationOrderSummaryMapping extends MapperMapping<ReservationOrderSummaryResponseModel, ReservationOrderSummaryResponseDto> {
    @Override
    public void configure() {
        ReservationOrderSummaryResponseModel source = getSource();
        ReservationOrderSummaryResponseDto destination = getDestination();

        ReservationOrderSummaryListResponseDto dto;
        List<ReservationOrderSummaryListResponseDto> summaryList = new ArrayList<>();
        for (ReservationOrderSummaryListResponseModel model : source.getSummaryList()) {
            dto = MapperUtils.mapper(model, ReservationOrderSummaryListResponseDto.class);

            //预约类型
            dto.setReservationTypeLabel(ReservationTypeEnum.getEnumByKey(model.getReservationType()).getValue());

            //预约时间
            String reservationDate = DateUtils.dateToString(model.getReservationStartTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN);
            String reservationStartTime = DateUtils.dateToString(model.getReservationStartTime(), CommonConstant.DATE_TO_STRING_HM_PATTERN);
            String reservationEndTime = DateUtils.dateToString(model.getReservationEndTime(), CommonConstant.DATE_TO_STRING_HM_PATTERN);
            dto.setReservationTime(reservationDate+" "+reservationStartTime+CommonConstant.MINUS+reservationEndTime);

            //地址
            StringBuilder addressBuilder = new StringBuilder();
            if (StringUtils.isNotBlank(model.getWarehouse())){
                addressBuilder.append("【");
                addressBuilder.append(model.getWarehouse());
                addressBuilder.append("】");
            }
            addressBuilder.append(model.getProvinceName());
            addressBuilder.append(model.getCityName());
            addressBuilder.append(model.getAreaName());
            addressBuilder.append(model.getDetailAddress());
            dto.setAddress(addressBuilder.toString());

            //数量
            dto.setExpectAmount(model.getExpectAmount().stripTrailingZeros().toPlainString());

            summaryList.add(dto);
        }
        destination.setSummaryList(summaryList);

    }
}
