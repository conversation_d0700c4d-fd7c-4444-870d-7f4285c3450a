package com.logistics.management.webapi.api.feign.entrustsettlement.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class GetSettlementDetailRequestDto {
    @ApiModelProperty("结算ID，逗号分隔")
    @NotBlank(message = "结算ID不能为空")
    private String settlementIds;
    @ApiModelProperty("接口类型：1 收款按钮，2 回退按钮")
    @NotBlank(message = "接口类型不能为空，1 收款按钮，2 回退按钮")
    private String type;
}
