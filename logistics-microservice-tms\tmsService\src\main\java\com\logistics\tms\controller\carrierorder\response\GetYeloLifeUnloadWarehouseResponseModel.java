package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/25
 */
@Data
public class GetYeloLifeUnloadWarehouseResponseModel {

	@ApiModelProperty("仓库code")
	private String warehouseCode;

	@ApiModelProperty("卸货省份id")
	private Long unloadProvinceId;

	@ApiModelProperty("卸货省份名字")
	private String unloadProvinceName;

	@ApiModelProperty("卸货城市id")
	private Long unloadCityId;

	@ApiModelProperty("卸货城市名字")
	private String unloadCityName;

	@ApiModelProperty("卸货县区id")
	private Long unloadAreaId;

	@ApiModelProperty("卸货县区名字")
	private String unloadAreaName;

	@ApiModelProperty("卸货详细地址")
	private String unloadDetailAddress;

	@ApiModelProperty("卸货仓库")
	private String unloadWarehouse;

	@ApiModelProperty("卸货人姓名")
	private String receiverName;

	@ApiModelProperty("卸货人手机号")
	private String receiverMobile;
}
