package com.logistics.management.webapi.api.impl.carriercontact.mapping;

import com.logistics.management.webapi.api.feign.carriercontact.dto.SearchCarrierContactResponseDto;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.EnabledEnum;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.carriercontact.dto.SearchCarrierContactResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

public class CarrierContactListMapping extends MapperMapping<SearchCarrierContactResponseModel, SearchCarrierContactResponseDto> {
    @Override
    public void configure() {
        SearchCarrierContactResponseModel source = getSource();
        SearchCarrierContactResponseDto destination = getDestination();

        destination.setTypeLabel(CompanyTypeEnum.getEnum(source.getType()).getValue());
        destination.setCarrierContactStatusLabel(EnabledEnum.getEnum(source.getCarrierContactStatus()).getValue());
        destination.setContactPhone(FrequentMethodUtils.encryptionData(source.getContactPhone(), EncodeTypeEnum.MOBILE_PHONE));
        destination.setExportContactPhone(source.getContactPhone());
    }
}
