package com.logistics.tms.controller.settlestatement.packaging.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/16
 */
@Data
public class CarrierSettleStatementListResponseModel {

	@ApiModelProperty("对账单id")
	private Long settleStatementId;

	@ApiModelProperty("对账单状态,-2:已撤销 -1:待提交 0:待业务审核 1:待财务审核 2:已对账 3:已驳回")
	private Integer settleStatementStatus;

	@ApiModelProperty("对账月份")
	private String settleStatementMonth;

	@ApiModelProperty("对账单号")
	private String settleStatementCode;

	@ApiModelProperty("结算主体")
	private Long platformCompanyId;
	private String platformCompanyName;

	@ApiModelProperty("运费费点")
	private BigDecimal freightTaxPoint;

	@ApiModelProperty("临时费用费点")
	private BigDecimal otherFeeTaxPoint;

	@ApiModelProperty("差异调整费用")
	private BigDecimal adjustFee;
	@ApiModelProperty("调整理由")
	private String adjustRemark;

	@ApiModelProperty("对账单名称")
	private String settleStatementName;

	@ApiModelProperty("合同号")
	private String contractCode;

	@ApiModelProperty("关联发票：0 否，1 是")
	private Integer ifInvoice;

	@ApiModelProperty("备注")
	private String remark;

	@ApiModelProperty("创建时间")
	private Date createdTime;

	@ApiModelProperty("操作人")
	private String lastModifiedBy;

	@ApiModelProperty("操作时间")
	private Date lastModifiedTime;

	@ApiModelProperty("对账单明细")
	private List<CarrierSettleStatementItemModel> itemList;
}
