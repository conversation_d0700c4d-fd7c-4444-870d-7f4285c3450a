package com.logistics.appapi.controller.renewableaudit.mapping;

import com.logistics.appapi.base.enums.CompanyTypeEnum;
import com.logistics.appapi.base.enums.RenewableAuditStatusEnum;
import com.logistics.appapi.base.enums.RenewableTicketUploadStatusEnum;
import com.logistics.appapi.base.enums.YeloLifeGoodsUnitEnum;
import com.logistics.appapi.client.renewableaudit.response.RenewableOrderDetailResponseModel;
import com.logistics.appapi.client.renewableaudit.response.RenewableOrderGoodModel;
import com.logistics.appapi.controller.renewableaudit.response.RenewableOrderDetailResponseDto;
import com.logistics.appapi.controller.renewableaudit.response.RenewableOrderGoodDto;
import com.logistics.appapi.controller.renewableaudit.response.RenewableOrderTicketDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/19 16:17
 */
public class RenewableOrderDetailMapping extends MapperMapping<RenewableOrderDetailResponseModel, RenewableOrderDetailResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;

    public RenewableOrderDetailMapping(String imagePrefix, Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap = imageMap;
    }

    @Override
    public void configure() {
        RenewableOrderDetailResponseModel source = getSource();
        RenewableOrderDetailResponseDto destination = getDestination();

        if (ListUtils.isNotEmpty(source.getRenewableOrderTickets())) {
            for (RenewableOrderTicketDto ticket : destination.getRenewableOrderTickets()) {
                ticket.setSrc(imagePrefix + imageMap.get(ticket.getRelativePath()));
            }
        }
        if (source.getStatus() != null) {
            destination.setStatusLabel(RenewableAuditStatusEnum.getEnum(source.getStatus()).getValue());
        }
        destination.setGoodsVerifyStatusLabel(RenewableTicketUploadStatusEnum.getEnum(source.getGoodsVerifyStatus()).getValue());
        destination.setTicketUploadStatusLabel(RenewableTicketUploadStatusEnum.getEnum(source.getTicketUploadStatus()).getValue());

        List<RenewableOrderGoodDto> renewableOrderGoodDtoList = new ArrayList<>();
        List<RenewableOrderGoodModel> renewableOrderGoods = source.getRenewableOrderGoods();
        for (RenewableOrderGoodModel renewableOrderGood : renewableOrderGoods) {
            RenewableOrderGoodDto dto = MapperUtils.mapper(renewableOrderGood, RenewableOrderGoodDto.class);
            dto.setGoodsUnit(YeloLifeGoodsUnitEnum.getEnum(renewableOrderGood.getGoodsUnit()).getUnit());
            dto.setGoodsAmount(renewableOrderGood.getGoodsAmount().stripTrailingZeros().toPlainString());
            renewableOrderGoodDtoList.add(dto);
        }
        destination.setRenewableOrderGoods(renewableOrderGoodDtoList);

        //转换确认货物数量
        destination.setVerifiedGoodsAmountTotal(source.getVerifiedGoodsAmountTotal().stripTrailingZeros().toPlainString());

        //个人类型
        if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getBusinessType())){
            destination.setCustomerName(source.getCustomerUserName()+" "+source.getCustomerUserMobile());
        }
    }
}
