package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/5/16 8:56
 */
@Data
public class SaveDemandOrderRequestDto {
    @ApiModelProperty("客户单号")
    private String customerOrderCode;

    //货主信息
    @ApiModelProperty(value="货主id",required = true)
    @NotBlank(message = "请填写委托方")
    private String companyEntrustId;
    @ApiModelProperty(value = "货主名称",required = true)
    private String companyEntrustName;
    @ApiModelProperty("货主联系人id")
    private String entrustContactId;

    //地址信息
    //发货信息
    @ApiModelProperty(value = "发货地址",required = true)
    @NotBlank(message = "请填写发货地址")
    private String loadProvinceId;
    @NotBlank(message = "请填写发货地址")
    private String loadProvinceName;
    @ApiModelProperty(value = "发货地址",required = true)
    @NotBlank(message = "请填写发货地址")
    private String loadCityId;
    @NotBlank(message = "请填写发货地址")
    private String loadCityName;
    @ApiModelProperty(value = "发货地址",required = true)
    @NotBlank(message = "请填写发货地址")
    private String loadAreaId;
    @NotBlank(message = "请填写发货地址")
    private String loadAreaName;
    @Size( max = 20, message = "最多输入20字")
    private String loadWarehouse;
    @Size( max = 50, message = "最多输入50字")
    private String loadDetailAddress;
    @Size( max = 20, message = "最多输入20字")
    private String loadContactName;
    @ApiModelProperty(value = "发货地址联系方式")
    private String loadContactMobile;
    @ApiModelProperty("期望提货时间")
    private String expectedLoadTime;
    //收货信息
    @ApiModelProperty(value = "收货地址",required = true)
    @NotBlank(message = "请填写收货地址")
    private String unloadProvinceId;
    @NotBlank(message = "请填写收货地址")
    private String unloadProvinceName;
    @ApiModelProperty(value = "收货地址",required = true)
    @NotBlank(message = "请填写收货地址")
    private String unloadCityId;
    @NotBlank(message = "请填写收货地址")
    private String unloadCityName;
    @ApiModelProperty(value = "收货地址",required = true)
    @NotBlank(message = "请填写收货地址")
    private String unloadAreaId;
    @NotBlank(message = "请填写收货地址")
    private String unloadAreaName;
    @Size( max = 20, message = "最多输入20字")
    private String unloadWarehouse;
    @Size( max = 50, message = "最多输入50字")
    private String unloadDetailAddress;
    @Size( max = 20, message = "最多输入20字")
    private String unloadContactName;
    @ApiModelProperty(value = "收货地址联系方式",required = true)
    @Pattern(regexp = "(^\\d{11}$)|(^(0[0-9]{2,3}-)?([2-9][0-9]{6,7})$)", message = "请维护正确的收货地址联系方式")
    private String unloadContactMobile;
    @ApiModelProperty("期望卸货时间")
    private String expectedUnloadTime;

    //货主费用信息
    @ApiModelProperty(value = "货主结算费用类型：0 空 1 单价(元/吨，元/件)，2 一口价(元)")
    private String contractPriceType;
    @ApiModelProperty(value = "合同价：0 空")
    private String contractPrice;

    //车主信息
    @ApiModelProperty("是否我司 1:我司,2:其他车主")
    @NotBlank(message = "请选择接单车主类型")
    @Range(min = 1, max = 2, message = "接单车主类型请填写1或2")
    private String isOurCompany;
    @ApiModelProperty("车主ID,非我司时填写")
    private String companyCarrierId;
    @ApiModelProperty(value = "车主价格：0 空，1 单价(元/吨，元/件)，2 一口价(元)")
    private String carrierPriceType;
    @ApiModelProperty(value = "车主价格：0 空")
    private String carrierPrice;
    @ApiModelProperty("备注")
    private String remark;

    //货物信息
    @Valid
    @ApiModelProperty("委托单货物信息")
    @NotEmpty(message = "货物信息不能为空")
    private List<SaveDemandOrderGoodsRequestDto> demandOrderGoodsList;
}
