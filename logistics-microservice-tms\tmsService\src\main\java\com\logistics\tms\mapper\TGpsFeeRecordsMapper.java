package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.gpsfee.model.GpsFeeRecordsListResponseModel;
import com.logistics.tms.entity.TGpsFeeRecords;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TGpsFeeRecordsMapper extends BaseMapper<TGpsFeeRecords> {

    List<GpsFeeRecordsListResponseModel> getGpsFeeRecords(@Param("gpsFeeId")Long gpsFeeId);
}