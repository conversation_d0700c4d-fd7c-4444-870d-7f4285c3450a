package com.logistics.appapi.client.website.vehicle;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.website.vehicle.hystrix.VehicleSourceClientHystrix;
import com.logistics.appapi.client.website.vehicle.request.PublishVehicleSourceRequestModel;
import com.logistics.appapi.client.website.vehicle.request.VehicleSourceListRequestModel;
import com.logistics.appapi.client.website.vehicle.response.VehicleSourceListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2024/3/15 10:56
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        fallback = VehicleSourceClientHystrix.class)
public interface VehicleSourceClient {

    @ApiOperation("发布车源")
    @PostMapping("/service/vehicleSource/publishVehicleSource")
    Result publishVehicleSource(@RequestBody PublishVehicleSourceRequestModel requestModel);

    @ApiOperation("车源列表")
    @PostMapping("/service/vehicleSource/vehicleSourceList")
    Result<PageInfo<VehicleSourceListResponseModel>> vehicleSourceList(@RequestBody VehicleSourceListRequestModel requestModel);

}
