package com.logistics.management.webapi.client.routeenquiry.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2024/7/9 17:16
 */
@Data
public class GetRouteEnquiryDetailAddressListResponseModel {

    /**
     * 路线询价单地址表id
     */
    private Long routeEnquiryAddressId;

    //发货地
    /**
     * 发货地-仓库
     */
    private String fromWarehouse;

    /**
     * 发货地-省
     */
    private String fromProvinceName;

    /**
     * 发货地-市
     */
    private String fromCityName;

    /**
     * 发货地-区
     */
    private String fromAreaName;


    //收货地
    /**
     * 收货地-省
     */
    private String toProvinceName;

    /**
     * 收货地-市
     */
    private String toCityName;

    /**
     * 收货地-区
     */
    private String toAreaName;

    /**
     * 运距
     */
    private BigDecimal distance;

    /**
     * 结算价
     */
    private BigDecimal quotePrice;

    /**
     * 结算模式：1 单价，2 一口价
     */
    private Integer quotePriceType;

    /**
     * 备注
     */
    private String quoteRemark;

}
