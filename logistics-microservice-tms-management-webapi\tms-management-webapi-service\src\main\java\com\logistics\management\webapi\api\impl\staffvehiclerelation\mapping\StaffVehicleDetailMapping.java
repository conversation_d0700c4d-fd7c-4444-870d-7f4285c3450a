package com.logistics.management.webapi.api.impl.staffvehiclerelation.mapping;

import com.logistics.management.webapi.api.feign.staffvehiclerelation.dto.StaffVehicleDetailResponseDto;
import com.logistics.management.webapi.base.enums.VehicleCategoryEnum;
import com.logistics.management.webapi.base.enums.VehiclePropertyEnum;
import com.logistics.tms.api.feign.staffvehiclerelation.model.StaffVehicleDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

/**
 * @Author: sj
 * @Date: 2019/10/31 11:10
 */
public class StaffVehicleDetailMapping extends MapperMapping<StaffVehicleDetailResponseModel,StaffVehicleDetailResponseDto> {
    @Override
    public void configure() {
        StaffVehicleDetailResponseModel source = this.getSource();
        StaffVehicleDetailResponseDto dto = this.getDestination();

        if(source!=null){
            dto.setVehicleCategoryLabel(VehicleCategoryEnum.getEnum(source.getVehicleCategory()).getValue());
            dto.setTypeLabel(VehiclePropertyEnum.getEnum(source.getType()).getValue());
            if(VehicleCategoryEnum.WHOLE.getKey().equals(source.getVehicleCategory())){
                dto.setOneVehicleId(dto.getTractorVehicleId());
                dto.setOneVehicleNo(dto.getTractorVehicleNo());
                dto.setTractorVehicleId("");
                dto.setTractorVehicleNo("");
            }

            if(StringUtils.isBlank(dto.getTrailerVehicleNo())){
                dto.setTrailerVehicleId("");
                dto.setTrailerVehicleNo("");
            }
        }

    }
}
