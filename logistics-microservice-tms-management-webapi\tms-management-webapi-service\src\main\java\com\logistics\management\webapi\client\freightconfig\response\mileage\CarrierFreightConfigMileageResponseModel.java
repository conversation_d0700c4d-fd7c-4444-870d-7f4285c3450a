package com.logistics.management.webapi.client.freightconfig.response.mileage;

import com.logistics.management.webapi.client.freightconfig.response.ladder.CarrierFreightConfigPriceDesignResponseModel;
import com.logistics.management.webapi.client.freightconfig.response.scheme.CarrierFreightConfigSchemeResponseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigMileageResponseModel extends CarrierFreightConfigSchemeResponseModel {

    @ApiModelProperty(value = "价格设计")
    private CarrierFreightConfigPriceDesignResponseModel priceDesign;
}
