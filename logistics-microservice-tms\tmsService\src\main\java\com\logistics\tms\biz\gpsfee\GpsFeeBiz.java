package com.logistics.tms.biz.gpsfee;

import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tray.core.exception.BizException;
import com.logistics.tms.api.feign.gpsfee.model.*;
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/8 10:52
 */
@Service
@Slf4j
public class GpsFeeBiz {

    @Autowired
    private TGpsFeeMapper gpsFeeMapper;
    @Autowired
    private TGpsFeeRecordsMapper gpsFeeRecordsMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TDeductingHistoryMapper deductingHistoryMapper;
    @Autowired
    private TStaffBasicMapper staffBasicMapper;
    @Autowired
    private TVehicleBasicMapper vehicleBasicMapper;
    @Autowired
    private TVehicleSettlementRelationMapper tVehicleSettlementRelationMapper;

    /**
     * 查询gps费用列表
     *
     * @param requestModel
     * @return
     */
    public List<SearchGpsFeeListResponseModel> searchGpsFeeList(SearchGpsFeeListRequestModel requestModel) {
        return gpsFeeMapper.searchGpsFeeList(requestModel);
    }

    /**
     * 统计gps费用列表各状态数量
     *
     * @param requestModel
     * @return
     */
    public SearchGpsFeeListCountResponseModel searchGpsFeeListCount(SearchGpsFeeListRequestModel requestModel) {
        return gpsFeeMapper.searchGpsFeeListCount(requestModel);
    }

    /**
     * 查看详情
     *
     * @param requestModel
     * @return
     */
    public GpsFeeDetailResponseModel getGpsFeeDetail(GpsFeeIdRequestModel requestModel) {
        GpsFeeDetailResponseModel responseModel = gpsFeeMapper.getGpsFeeDetail(requestModel.getGpsFeeId());
        //判断是有已经生成了结算数据
        List<TVehicleSettlementRelation> tVehicleSettlementRelationList = tVehicleSettlementRelationMapper.getByObjectIdAndType(VehicleSettlementTypeEnum.GPS.getKey(), responseModel.getGpsFeeId());
        if (ListUtils.isNotEmpty(tVehicleSettlementRelationList)) {
            responseModel.setIfSettlement(CommonConstant.INTEGER_ONE);
        }
        return responseModel;
    }

    /**
     * 新增/修改gps费用
     *
     * @param requestModel
     */
    @Transactional
    public void addOrModifyGpsFee(AddOrModifyGpsFeeRequestModel requestModel) {
        if (requestModel.getStartDate().getTime() > requestModel.getEndDate().getTime()) {
            throw new BizException(CarrierDataExceptionEnum.GPS_FEE_START_DATE_ERROR);
        }
        Date now = new Date();
        //转化程时分秒
        Date currentNow = DateUtils.stringToDate(DateUtils.dateToString(now, DateUtils.DATE_TO_STRING_SHORT_PATTERN), DateUtils.DATE_TO_STRING_SHORT_PATTERN);
        //新增的时候截止时间不能少于当前时间
        if ((requestModel.getGpsFeeId() == null || CommonConstant.LONG_ZERO.equals(requestModel.getGpsFeeId())) && requestModel.getEndDate().before(currentNow)) {
            throw new BizException(CarrierDataExceptionEnum.GPS_FEE_END_TIME_NOT_ALLOW_BEFORE_NOW);
        }
        VehicleBasicPropertyModel vehicleBasic = vehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getVehicleId());
        if (vehicleBasic == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_NOT_EXIST);
        }
        TStaffBasic tStaffBasic = staffBasicMapper.selectByPrimaryKey(requestModel.getStaffId());
        if (tStaffBasic == null || tStaffBasic.getValid().equals(CommonConstant.INTEGER_ZERO)) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }
        Long gpsFeeId = requestModel.getGpsFeeId();
        //新增gps费用的判断
        TGpsFee tGpsFee = null;
        if (gpsFeeId != null && gpsFeeId > CommonConstant.LONG_ZERO) {//修改gps费用的判断
            tGpsFee = gpsFeeMapper.selectByPrimaryKey(gpsFeeId);
            if (tGpsFee == null || tGpsFee.getValid().equals(CommonConstant.INTEGER_ZERO)) {
                throw new BizException(CarrierDataExceptionEnum.GPS_FEE_NOT_EXIST);
            }
            if (CooperationStatusEnum.TERMINAL.getKey().equals(tGpsFee.getCooperationStatus())) {
                throw new BizException(CarrierDataExceptionEnum.GPS_FEE_TERMINATION);
            }
            if (!SettlementStatusEnum.WAIT.getKey().equals(tGpsFee.getStatus())) {
                throw new BizException(CarrierDataExceptionEnum.SETTLEMENT_STATUS_ERROR_NOT_UPDATE);
            }
            List<TDeductingHistory> historyList = deductingHistoryMapper.getByObjectTypeId(DeductingHistoryObjectTypeEnum.T_GPS_FEE.getKey(), gpsFeeId);
            if (ListUtils.isNotEmpty(historyList) && requestModel.getStartDate().getTime() > new Date().getTime()) {
                throw new BizException(CarrierDataExceptionEnum.GPS_FEE_HISTORY_EXIST);
            }
            //修改了车辆则判断车辆是否符合条件
            if (!tGpsFee.getVehicleId().equals(requestModel.getVehicleId())) {
                if (!VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehicleBasic.getVehicleProperty()) && !VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehicleBasic.getVehicleProperty())) {
                    throw new BizException(CarrierDataExceptionEnum.VEHICLE_PROPERTY_ERROR);
                }
                if (!VehicleOutageStatus.IN_OPERATION.getKey().equals(vehicleBasic.getOperatingState())) {
                    throw new BizException(CarrierDataExceptionEnum.VEHICLE_OUTAGE_ERROR);
                }
                if (!VehicleCategoryEnum.TRACTOR.getKey().equals(vehicleBasic.getVehicleCategory()) && !VehicleCategoryEnum.WHOLE.getKey().equals(vehicleBasic.getVehicleCategory())) {
                    throw new BizException(CarrierDataExceptionEnum.VEHICLE_CATEGORY_ERROR);
                }
            }
            //修改了司机则判断司机是否符合条件
            if (!tGpsFee.getStaffId().equals(requestModel.getStaffId()) && !StaffPropertyEnum.OWN_STAFF.getKey().equals(tStaffBasic.getStaffProperty()) && !StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(tStaffBasic.getStaffProperty())) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_PROPERTY_ERROR);
            }
        } else { //新增gps费用的判断
            //判断车辆司机是否符合条件
            if (!VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehicleBasic.getVehicleProperty()) && !VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehicleBasic.getVehicleProperty())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_PROPERTY_ERROR);
            }
            if (!VehicleOutageStatus.IN_OPERATION.getKey().equals(vehicleBasic.getOperatingState())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_OUTAGE_ERROR);
            }
            if (!VehicleCategoryEnum.TRACTOR.getKey().equals(vehicleBasic.getVehicleCategory()) && !VehicleCategoryEnum.WHOLE.getKey().equals(vehicleBasic.getVehicleCategory())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_CATEGORY_ERROR);
            }
            if (!StaffPropertyEnum.OWN_STAFF.getKey().equals(tStaffBasic.getStaffProperty()) && !StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(tStaffBasic.getStaffProperty())) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_PROPERTY_ERROR);
            }
        }
        TGpsFee gpsFee = new TGpsFee();
        gpsFee.setVehicleId(requestModel.getVehicleId());
        gpsFee.setVehicleNo(vehicleBasic.getVehicleNo());
        gpsFee.setStaffId(requestModel.getStaffId());
        gpsFee.setName(tStaffBasic.getName());
        gpsFee.setMobile(tStaffBasic.getMobile());
        gpsFee.setGpsServiceProvider(requestModel.getGpsServiceProvider());
        gpsFee.setTerminalType(requestModel.getTerminalType());
        gpsFee.setServiceFee(requestModel.getServiceFee());
        gpsFee.setCooperationPeriod(requestModel.getCooperationPeriod());
        gpsFee.setSimNumber(requestModel.getSimNumber());
        gpsFee.setInstallTime(requestModel.getInstallTime());
        gpsFee.setStartDate(requestModel.getStartDate());
        gpsFee.setEndDate(requestModel.getEndDate());
        gpsFee.setFinishDate(requestModel.getEndDate());
        gpsFee.setCooperationStatus(getCooperationStatus(requestModel.getStartDate(), requestModel.getEndDate()));
        gpsFee.setRemark(requestModel.getRemark());
        String userName = BaseContextHandler.getUserName();
        List<TGpsFee> tGpsFeeList = gpsFeeMapper.getByVehicleStartDate(requestModel.getVehicleId(), requestModel.getStartDate(), requestModel.getEndDate());

        //修改gps费用记录
        if (gpsFeeId != null && gpsFeeId > CommonConstant.LONG_ZERO) {
            //判断当前gps费用是否存在账单数据
            List<TVehicleSettlementRelation> tVehicleSettlementRelationList = tVehicleSettlementRelationMapper.getByObjectIdAndType(VehicleSettlementTypeEnum.GPS.getKey(), gpsFeeId);
            if (ListUtils.isEmpty(tVehicleSettlementRelationList) && requestModel.getEndDate().before(currentNow)) {
                throw new BizException(CarrierDataExceptionEnum.GPS_FEE_END_TIME_NOT_ALLOW_BEFORE_NOW);
            }
            if (ListUtils.isNotEmpty(tGpsFeeList) && (tGpsFeeList.stream().filter(o -> o != null && !o.getId().equals(requestModel.getGpsFeeId())).findFirst().orElse(null) != null)) {
                throw new BizException(CarrierDataExceptionEnum.GPS_FEE_EXIST);
            }
            //如果存在车辆结算费用，则以下字段不允许修改
            if (ListUtils.isNotEmpty(tVehicleSettlementRelationList)) {
                gpsFee.setVehicleId(null);
                gpsFee.setVehicleNo(null);
                gpsFee.setStartDate(null);
                gpsFee.setCooperationPeriod(null);
                gpsFee.setEndDate(null);
                gpsFee.setServiceFee(null);
                gpsFee.setFinishDate(null);
            } else {
                //更换了车辆后才更换机构
                if (!tGpsFee.getVehicleId().equals(requestModel.getVehicleId())) {
                    gpsFee.setVehicleProperty(vehicleBasic.getVehicleProperty());
                }
            }
            gpsFee.setId(gpsFeeId);
            commonBiz.setBaseEntityModify(gpsFee, userName);
            gpsFeeMapper.updateByPrimaryKeySelective(gpsFee);
        } else {
            //新增gps费用记录
            if (ListUtils.isNotEmpty(tGpsFeeList)) {
                throw new BizException(CarrierDataExceptionEnum.GPS_FEE_EXIST);
            }
            //设置车辆机构
            gpsFee.setVehicleProperty(vehicleBasic.getVehicleProperty());
            commonBiz.setBaseEntityAdd(gpsFee, userName);
            gpsFeeMapper.insertSelective(gpsFee);
            gpsFeeId = gpsFee.getId();
        }

        TGpsFeeRecords gpsFeeRecords = new TGpsFeeRecords();
        gpsFeeRecords.setGpsFeeId(gpsFeeId);
        gpsFeeRecords.setVehicleNo(vehicleBasic.getVehicleNo());
        gpsFeeRecords.setGpsServiceProvider(requestModel.getGpsServiceProvider());
        gpsFeeRecords.setServiceFee(requestModel.getServiceFee());
        gpsFeeRecords.setCooperationPeriod(requestModel.getCooperationPeriod());
        gpsFeeRecords.setStartDate(requestModel.getStartDate());
        gpsFeeRecords.setEndDate(requestModel.getEndDate());
        gpsFeeRecords.setFinishDate(requestModel.getEndDate());
        gpsFeeRecords.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityAdd(gpsFeeRecords, userName);
        gpsFeeRecordsMapper.insertSelective(gpsFeeRecords);
    }

    public Integer getCooperationStatus(Date startDate, Date endDate) {
        Date now = new Date();
        SimpleDateFormat nowSdf = new SimpleDateFormat(DateUtils.DATE_TO_STRING_SHORT_PATTERN);
        String nowStr = nowSdf.format(now);
        Date nowTime = null;
        try {
            nowTime = nowSdf.parse(nowStr);
        } catch (ParseException e) {
            log.info(e.getMessage(), e);
        }
        Integer cooperationStatus = null;
        if (nowTime != null) {
            if (startDate.getTime() > nowTime.getTime()) {
                cooperationStatus = CooperationStatusEnum.NO_START.getKey();
            } else if (nowTime.getTime() > endDate.getTime()) {
                cooperationStatus = CooperationStatusEnum.TERMINAL.getKey();
            } else {
                cooperationStatus = CooperationStatusEnum.VALID.getKey();
            }
        }
        return cooperationStatus;
    }

    /**
     * 终止gps费用（修改终止时间）
     *
     * @param requestModel
     */
    @Transactional
    public void terminationGpsFee(TerminationGpsFeeRequestModel requestModel) {
        TGpsFee tGpsFee = gpsFeeMapper.selectByPrimaryKey(requestModel.getGpsFeeId());
        if (tGpsFee == null || tGpsFee.getValid().equals(CommonConstant.INTEGER_ZERO)) {
            throw new BizException(CarrierDataExceptionEnum.GPS_FEE_NOT_EXIST);
        }
        if (CooperationStatusEnum.TERMINAL.getKey().equals(tGpsFee.getCooperationStatus())) {
            throw new BizException(CarrierDataExceptionEnum.GPS_FEE_TERMINATION);
        }
        if (CooperationStatusEnum.NO_START.getKey().equals(tGpsFee.getCooperationStatus()) && SettlementStatusEnum.WAIT.getKey().equals(tGpsFee.getStatus())) {
            throw new BizException(CarrierDataExceptionEnum.GPS_FEE_NOT_TERMINATION);
        }
        if (requestModel.getFinishDate().getTime() > tGpsFee.getEndDate().getTime() || requestModel.getFinishDate().getTime() < tGpsFee.getStartDate().getTime()) {
            throw new BizException(CarrierDataExceptionEnum.GPS_FEE_FINISH_DATE_ERROR);
        }
        TGpsFee gpsFee = new TGpsFee();
        gpsFee.setId(requestModel.getGpsFeeId());
        gpsFee.setFinishDate(requestModel.getFinishDate());
        gpsFee.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityModify(gpsFee, BaseContextHandler.getUserName());
        gpsFeeMapper.updateByPrimaryKeySelective(gpsFee);

        TGpsFeeRecords gpsFeeRecords = new TGpsFeeRecords();
        gpsFeeRecords.setGpsFeeId(requestModel.getGpsFeeId());
        gpsFeeRecords.setVehicleNo(tGpsFee.getVehicleNo());
        gpsFeeRecords.setGpsServiceProvider(tGpsFee.getGpsServiceProvider());
        gpsFeeRecords.setServiceFee(tGpsFee.getServiceFee());
        gpsFeeRecords.setCooperationPeriod(tGpsFee.getCooperationPeriod());
        gpsFeeRecords.setStartDate(tGpsFee.getStartDate());
        gpsFeeRecords.setEndDate(tGpsFee.getEndDate());
        gpsFeeRecords.setFinishDate(requestModel.getFinishDate());
        gpsFeeRecords.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityAdd(gpsFeeRecords, BaseContextHandler.getUserName());
        gpsFeeRecordsMapper.insertSelective(gpsFeeRecords);
    }

    /**
     * 查询gps费用操作记录
     *
     * @param requestModel
     * @return
     */
    public List<GpsFeeRecordsListResponseModel> getGpsFeeRecords(GpsFeeIdRequestModel requestModel) {
        return gpsFeeRecordsMapper.getGpsFeeRecords(requestModel.getGpsFeeId());
    }

    /**
     * 查询gps费用扣减历史
     *
     * @param requestModel
     * @return
     */
    public GetDeductingHistoryByGpsFeeIdResponseModel getGpsFeeDeductingHistory(GpsFeeIdRequestModel requestModel) {
        TGpsFee tGpsFee = gpsFeeMapper.selectByPrimaryKey(requestModel.getGpsFeeId());
        if (tGpsFee == null || tGpsFee.getValid().equals(CommonConstant.INTEGER_ZERO)) {
            throw new BizException(CarrierDataExceptionEnum.GPS_FEE_NOT_EXIST);
        }
        GetDeductingHistoryByGpsFeeIdResponseModel responseModel = new GetDeductingHistoryByGpsFeeIdResponseModel();
        responseModel.setVehicleNo(tGpsFee.getVehicleNo());
        List<TDeductingHistory> historyList = deductingHistoryMapper.getByObjectTypeId(DeductingHistoryObjectTypeEnum.T_GPS_FEE.getKey(), requestModel.getGpsFeeId());
        responseModel.setHistoryList(MapperUtils.mapper(historyList, GetDeductingHistoryResponseModel.class));
        return responseModel;
    }
}
