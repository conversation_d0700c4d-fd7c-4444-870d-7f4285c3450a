package com.logistics.management.webapi.base.constant;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class ConfigKeyConstant {
    //临时文件访问地址
    @Value("${images.fileAccessAddressTemp}")
    public String fileAccessAddressTemp;
    //正式文件访问地址
    @Value("${images.fileAccessAddress}")
    public String fileAccessAddress;
    //文件服务器域名
    @Value("${images.fileServerDomainName}")
    public String fileServerDomainName;
    //临时文件目录
    @Value("${images.tempImageUploadCatalog}")
    public String tempImageUploadCatalog;
    //正式文件目录
    @Value("${images.imageUploadCatalog}")
    public String imageUploadCatalog;

    @Value("${images.pickUpGoodTempCatalog}")
    public String pickUpGoodTempCatalog;

    @Value("${images.vehicleBasicInfoCatalog}")
    public String vehicleBasicInfoCatalog;


    // 二维码跳转通用前缀
    @Value("${qrcode.commonPrefix}")
    public String qrcodeCommonPrefix;

}


