package com.logistics.appapi.client.drivercostapply.hystrix;

import com.logistics.appapi.client.drivercostapply.DriverCostApplyClient;
import com.logistics.appapi.client.drivercostapply.request.AddDriverCostApplyRequestModel;
import com.logistics.appapi.client.drivercostapply.request.DriverCostApplyDetailRequestModel;
import com.logistics.appapi.client.drivercostapply.request.SearchCostApplyListForAppletRequestModel;
import com.logistics.appapi.client.drivercostapply.request.UndoDriverCostApplyRequestModel;
import com.logistics.appapi.client.drivercostapply.response.DriverCostApplyDetailResponseModel;
import com.logistics.appapi.client.drivercostapply.response.SearchCostApplyListCountForAppletResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

@Component
public class DriverCostApplyClientHystrix implements DriverCostApplyClient {

    @Override
    public Result<DriverCostApplyDetailResponseModel> driverCostApplyDetail(DriverCostApplyDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> undoDriverCostApply(UndoDriverCostApplyRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SearchCostApplyListCountForAppletResponseModel> searchCostApplyListForApplet(SearchCostApplyListForAppletRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addDriverCostApply(AddDriverCostApplyRequestModel requestModel) {
        return Result.timeout();
    }
}
