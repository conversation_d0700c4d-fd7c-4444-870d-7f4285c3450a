package com.logistics.appapi.controller.login.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/17
 */
@Data
public class UpdatePhoneByVerifyPhoneRequestDto {

	@ApiModelProperty(value = "手机号",required = true)
	@NotBlank(message = "手机号不能为空")
	private String userAccount;

	@ApiModelProperty(value = "短信验证码",required = true)
	@NotBlank(message = "短信验证码不能为空")
	private String verificationCode;
}
