package com.logistics.tms.api.feign.carrierfreight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 车主运价禁用启用
 *
 * <AUTHOR>
 * @date 2022/9/2 15:57
 */
@Data
public class CarrierFreightEnableRequestModel {

    @ApiModelProperty(value = "车主运价id", required = true)
    private Long carrierFreightId;

    @ApiModelProperty("禁用/启用。1：启用，0：禁用")
    private Integer enabled;
}
