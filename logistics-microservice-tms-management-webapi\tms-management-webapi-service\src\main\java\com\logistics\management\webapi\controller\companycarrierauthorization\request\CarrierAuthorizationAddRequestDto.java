package com.logistics.management.webapi.controller.companycarrierauthorization.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/3
 */
@Data
public class CarrierAuthorizationAddRequestDto {

	@ApiModelProperty(value = "车主id", required = true)
	@NotBlank(message = "请选择车主")
	private String companyCarrierId;

	@ApiModelProperty(value = "车主id", required = true)
	@NotEmpty(message = "请上传授权书")
	private List<String> authorizationImageList;

	@ApiModelProperty(value = "是否归档, 0:否 1:是", required = true)
	@NotBlank(message = "请选择是否归档")
	@Range(min = 0, max = 1, message = "请选择是否归档")
	private String isArchived;

	@ApiModelProperty(value = "备注")
	@Length(max = 100, message = "备注最多填写100个字符")
	private String remark;
}
