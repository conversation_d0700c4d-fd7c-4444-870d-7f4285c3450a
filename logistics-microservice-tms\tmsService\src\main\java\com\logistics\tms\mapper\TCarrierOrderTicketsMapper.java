package com.logistics.tms.mapper;

import com.logistics.tms.controller.carrierorder.response.TicketsModel;
import com.logistics.tms.controller.carrierorderapplet.response.CarrierOrderBillResponseModel;
import com.logistics.tms.biz.carrierorder.model.GetTicketsAmountByCarrierOrderIdsModel;
import com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailTicketsModel;
import com.logistics.tms.controller.carrierorder.response.CarrierOrderTicketsResponseModel;
import com.logistics.tms.controller.carrierorder.response.GetTicketsResponseModel;
import com.logistics.tms.entity.TCarrierOrderTickets;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TCarrierOrderTicketsMapper extends BaseMapper<TCarrierOrderTickets> {

    List<GetTicketsResponseModel> getTicketsByCarrierOrderId(@Param("carrierOrderId") Long id, @Param("imageType") Integer imageType);

    int batchInsertTickets(@Param("list") List<TCarrierOrderTickets> list);

    int batchUpdate(@Param("list") List<TCarrierOrderTickets> list);

    List<GetTicketsResponseModel> getTicketsByCarrierOrderIdAndType(@Param("carrierOrderId") Long id, @Param("type") Integer type);

    //app
    List<CarrierOrderBillResponseModel> getTicketsByCarrierOrderIdByApp(@Param("carrierOrderId") Long carrierOrderId);

    List<GetTicketsAmountByCarrierOrderIdsModel> getTicketsAmountByCarrierOrderIds(@Param("carrierOrderIds") String carrierOrderIds);

    List<TicketsModel> getTicketsByCarrierOrderIds(@Param("carrierOrderIds") String carrierOrderIds);

    CarrierOrderDetailTicketsModel getTicketsCountByCarrierOrderIdForWeb(@Param("carrierOrderId") Long carrierOrderId);

    List<CarrierOrderTicketsResponseModel> getTicketsByCarrierOrderIdByWeb(@Param("carrierOrderId") Long carrierOrderId);

    List<Long> selectCarrierOrderIdByTicketUploadTimeAndType(@Param("uploadStartTime") String uploadStartTime,
                                                             @Param("uploadEndTime") String uploadEndTime,
                                                             @Param("type") Integer type);

    List<TCarrierOrderTickets> selectTicketsByCarrierOrderIdsAndType(@Param("carrierOrderIds") String carrierOrderIds, @Param("type") Integer type);
}