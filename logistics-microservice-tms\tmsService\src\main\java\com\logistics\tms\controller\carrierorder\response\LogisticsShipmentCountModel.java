package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author:lei.zhu
 * @date:2021/12/28 13:30:30
 */
@Data
public class LogisticsShipmentCountModel {

    @ApiModelProperty("需求单编号")
    private String logisticsDemandCode;

    @ApiModelProperty("预提数量")
    private Integer shipmentCount;

    @ApiModelProperty("sku code")
    private String productTypeCode;

    @ApiModelProperty(value = "大类名称")
    private String categoryName;

    @ApiModelProperty(value = "货物名")
    private String sortName;

    @ApiModelProperty(value = "长")
    private String length;

    @ApiModelProperty(value = "宽")
    private String width;

    @ApiModelProperty(value = "高")
    private String height;
}
