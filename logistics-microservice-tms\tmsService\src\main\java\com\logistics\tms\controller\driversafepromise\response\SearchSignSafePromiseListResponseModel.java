package com.logistics.tms.controller.driversafepromise.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 签订承诺书列表
 * @Author: sj
 * @Date: 2019/11/4 10:19
 */
@Data
public class SearchSignSafePromiseListResponseModel {
    @ApiModelProperty("承诺书ID")
    private Long safePromiseId;
    @ApiModelProperty("签订承诺书ID")
    private Long signSafePromiseId;
    @ApiModelProperty("签订状态: 0 待签订、1 已签订")
    private Integer status;
    @ApiModelProperty("司机ID")
    private Long staffId;
    @ApiModelProperty("司机名称")
    private String staffName;
    @ApiModelProperty("司机电话")
    private String staffMobile;
    @ApiModelProperty("签订时间")
    private Date signTime;
    @ApiModelProperty("人员机构 1 自主 2外部 3 自营")
    private Integer staffProperty;

    @ApiModelProperty("手持承诺书图片地址-相对路径")
    private String handPromiseUrl;
    @ApiModelProperty("签字责任书图片地址-相对路径")
    private String signResponsibilityUrl;
}
