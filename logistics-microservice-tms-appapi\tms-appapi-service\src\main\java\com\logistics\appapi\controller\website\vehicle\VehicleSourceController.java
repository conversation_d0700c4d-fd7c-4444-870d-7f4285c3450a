package com.logistics.appapi.controller.website.vehicle;

import cn.dev33.satoken.annotation.SaIgnore;
import com.github.pagehelper.PageInfo;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.AppApiExceptionEnum;
import com.logistics.appapi.base.utils.RegExpValidatorUtil;
import com.logistics.appapi.client.thirdparty.basicdata.BasicServiceClient;
import com.logistics.appapi.client.thirdparty.basicdata.baidu.request.CheckSensitiveWordRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.baidu.response.CheckSensitiveWordResponseModel;
import com.logistics.appapi.client.website.vehicle.VehicleSourceClient;
import com.logistics.appapi.client.website.vehicle.request.PublishVehicleSourceRequestModel;
import com.logistics.appapi.client.website.vehicle.request.VehicleSourceListRequestModel;
import com.logistics.appapi.client.website.vehicle.response.VehicleSourceListResponseModel;
import com.logistics.appapi.controller.website.vehicle.mapping.VehicleSourceListMapping;
import com.logistics.appapi.controller.website.vehicle.request.PublishVehicleSourceRequestDto;
import com.logistics.appapi.controller.website.vehicle.request.VehicleSourceListRequestDto;
import com.logistics.appapi.controller.website.vehicle.response.VehicleSourceListResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.utils.RedisUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.ExtFrequency;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.annocation.LimitedExtFrequency;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/15 9:52
 */
@Api(value = "云途官网-车源")
@RestController
public class VehicleSourceController {

    @Resource
    private VehicleSourceClient vehicleSourceClient;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private BasicServiceClient basicServiceClient;

    /**
     * 发布车源
     * @param requestDto
     * @return
     */
    @ApiOperation("发布车源")
    @PostMapping("/api/vehicleSource/publishVehicleSource")
    @SaIgnore
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @LimitedExtFrequency(limiteds = {@ExtFrequency(params = "sourceIp", count = 100, type = ExtFrequency.TYPE_DAY_DAWN, message = "非法访问,访问超过限定次数!")})
    public Result publishVehicleSource(@RequestBody @Valid PublishVehicleSourceRequestDto requestDto) {
        String verPic = (String)redisUtils.get(CommonConstant.TMS_API_PICTURE_VERIFICATION_CODE_PREFIX + "_" + requestDto.getUuid());
        if(StringUtils.isBlank(verPic) || !verPic.equalsIgnoreCase(requestDto.getPictureVerificationCode())){
            throw new BizException(AppApiExceptionEnum.USER_PIC_VERIFICATION_ERROR);
        }
        if (!RegExpValidatorUtil.isVehicleNo(requestDto.getVehicleNo())){
            throw new BizException(AppApiExceptionEnum.VEHICLE_NO_ERROR);
        }
        CheckSensitiveWordRequestModel checkSensitiveWordRequestModel = new CheckSensitiveWordRequestModel();
        checkSensitiveWordRequestModel.setContent(requestDto.getContactName());
        CheckSensitiveWordResponseModel sensitiveWordModel = basicServiceClient.checkSensitiveWord(checkSensitiveWordRequestModel);
        if (sensitiveWordModel == null || CommonConstant.INTEGER_ONE.equals(sensitiveWordModel.getIfSensitive())) {
            throw new BizException(AppApiExceptionEnum.NOT_ALLOW_SUBMIT_SENSITIVE_WORD);
        }
        Result result = vehicleSourceClient.publishVehicleSource(MapperUtils.mapper(requestDto, PublishVehicleSourceRequestModel.class));
        result.throwException();
        redisUtils.delete(CommonConstant.TMS_API_PICTURE_VERIFICATION_CODE_PREFIX + "_" + requestDto.getUuid());
        return Result.success(true);
    }

    /**
     * 车源列表
     * @param requestDto
     * @return
     */
    @ApiOperation("车源列表")
    @PostMapping("/api/vehicleSource/vehicleSourceList")
    @SaIgnore
    public Result<PageInfo<VehicleSourceListResponseDto>> vehicleSourceList(@RequestBody VehicleSourceListRequestDto requestDto) {
        Result<PageInfo<VehicleSourceListResponseModel>> result = vehicleSourceClient.vehicleSourceList(MapperUtils.mapper(requestDto, VehicleSourceListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<VehicleSourceListResponseDto> list = MapperUtils.mapper(pageInfo.getList(),VehicleSourceListResponseDto.class,new VehicleSourceListMapping());
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }
}
