package com.logistics.appapi.controller.reservationorder.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2024/8/14 14:52
 */
@Data
public class ReservationOrderSignDetail45HRequestDto {



    /**
     * 预约单id
     */
    private Long reservationOrderId;


    /**
     * 运单code
     */
    @NotBlank(message = "运单code不能为空")
    private String carrierOrderCode;

    /**
     * 定位-经度
     */
    @NotBlank(message = "请获取定位")
    private String longitude;
    /**
     * 定位-纬度
     */
    @NotBlank(message = "请获取定位")
    private String latitude;

}
