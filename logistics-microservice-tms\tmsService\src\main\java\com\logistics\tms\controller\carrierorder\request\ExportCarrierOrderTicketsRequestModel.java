package com.logistics.tms.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author：wjf
 * @date：2021/3/16 13:03
 */
@Data
public class ExportCarrierOrderTicketsRequestModel {
    @ApiModelProperty("运单ids")
    private List<String> carrierOrderIdList;
    @ApiModelProperty("命名方式：1 运单号，2 客户单号，3 车牌号（多个用逗号分隔）")
    private String fileNameType;
}
