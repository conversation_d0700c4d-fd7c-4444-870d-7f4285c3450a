package com.logistics.tms.biz.carrierorderticketsaudit.model;

import com.yelo.tools.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class SearchCarrierOrderIdsForLeYiReceiptAuditBoModel {

    @ApiModelProperty(value = "运单号")
    private String carrierOrderCode;

    @ApiModelProperty("需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;

    @ApiModelProperty(value = "发货地")
    private String loadAddress;

    @ApiModelProperty(value = "收货地")
    private String unloadAddress;

    @ApiModelProperty(value = "卸货日期; 起始日期")
    private String unloadStartDate;

    @ApiModelProperty(value = "卸货日期; 终止日期")
    private String unloadEndDate;

    @ApiModelProperty(value = "单据上传日期; 起始日期")
    private String ticketUploadStartDate;

    @ApiModelProperty(value = "单据上传日期; 终止日期")
    private String ticketUploadEndDate;

    @ApiModelProperty(value = "运单ID")
    private List<Long> carrierOrderIdList;

    public void setCarrierOrderCode(String carrierOrderCode) {
        this.carrierOrderCode = StringUtils.isBlank(carrierOrderCode) ? null : carrierOrderCode;
    }

    public void setLoadAddress(String loadAddress) {
        this.loadAddress = StringUtils.isBlank(loadAddress) ? null : loadAddress;
    }

    public void setUnloadAddress(String unloadAddress) {
        this.unloadAddress = StringUtils.isBlank(unloadAddress) ? null : unloadAddress;
    }

    public void setUnloadStartDate(String unloadStartDate) {
        this.unloadStartDate = StringUtils.isBlank(unloadStartDate) ? null : unloadStartDate;
    }

    public void setUnloadEndDate(String unloadEndDate) {
        this.unloadEndDate = StringUtils.isBlank(unloadEndDate) ? null : unloadEndDate;
    }

    public void setTicketUploadStartDate(String ticketUploadStartDate) {
        this.ticketUploadStartDate = StringUtils.isBlank(ticketUploadStartDate) ? null : ticketUploadStartDate;
    }

    public void setTicketUploadEndDate(String ticketUploadEndDate) {
        this.ticketUploadEndDate = StringUtils.isBlank(ticketUploadEndDate) ? null : ticketUploadEndDate;
    }
}
