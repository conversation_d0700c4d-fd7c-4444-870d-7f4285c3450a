package com.logistics.tms.biz.drivercostdeduction;

import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.drivercostdeduction.model.DriverCostDeductionQueryModel;
import com.logistics.tms.controller.drivercostapply.request.DriverCostDeductionRequestModel;
import com.logistics.tms.entity.TDriverCostDeduction;
import com.logistics.tms.mapper.TDriverCostDeductionMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ListUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DriverCostDeductionBiz {

    private final CommonBiz commonBiz;
    private final TDriverCostDeductionMapper driverCostDeductionMapper;

    /**
     * 查询费用冲销列表
     * @param queryModel
     * @return List<TDriverCostDeduction>
     */
    public List<TDriverCostDeduction> getCostDeduction(DriverCostDeductionQueryModel queryModel) {
        return driverCostDeductionMapper.selectAll(queryModel);
    }

    /**
     * 费用申请批量新增
     * @param costDeduction
     * @param driverCostApplyId
     */
    public void batchAddCostDeduction(Long driverCostApplyId, List<DriverCostDeductionRequestModel> costDeduction) {
        if (ListUtils.isEmpty(costDeduction)) {
            return;
        }
        String userName = BaseContextHandler.getUserName();
        List<TDriverCostDeduction> entities = costDeduction.stream()
                .map(s -> {
                    TDriverCostDeduction entity = new TDriverCostDeduction();
                    entity.setDriverCostApplyId(driverCostApplyId);
                    entity.setBalanceAmount(s.getBalance());
                    entity.setReserveApplyCode(s.getReserveCode());
                    entity.setVerificationAmount(s.getWriteOffAmount());
                    entity.setReserveType(s.getReserveType());
                    commonBiz.setBaseEntityAdd(entity, userName);
                    return entity;
                }).collect(Collectors.toList());
        driverCostDeductionMapper.batchInsert(entities);
    }
}
