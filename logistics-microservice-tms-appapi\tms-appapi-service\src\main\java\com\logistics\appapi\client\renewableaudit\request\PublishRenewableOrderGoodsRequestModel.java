package com.logistics.appapi.client.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2022/8/19 14:14
 */
@Data
public class PublishRenewableOrderGoodsRequestModel {
    @ApiModelProperty(value = "sku编码")
    private String skuCode;

    @ApiModelProperty(value = "货物名(sku名)")
    private String goodsName;

    @ApiModelProperty(value = "确认货物数量(Kg) 0<KG<=10000Kg")
    private BigDecimal goodsAmount;

    @ApiModelProperty(value = "收货单价 0<单价<=10000")
    private BigDecimal goodsPrice;
}
