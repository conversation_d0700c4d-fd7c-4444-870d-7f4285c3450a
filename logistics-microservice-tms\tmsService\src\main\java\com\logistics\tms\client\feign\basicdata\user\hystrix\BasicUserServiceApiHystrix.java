package com.logistics.tms.client.feign.basicdata.user.hystrix;

import com.logistics.tms.client.feign.basicdata.user.UserServiceApi;
import com.logistics.tms.client.feign.basicdata.user.request.GetUserByIdsRequestModel;
import com.logistics.tms.client.feign.basicdata.user.request.GetUserInfoModelRequestModel;
import com.logistics.tms.client.feign.basicdata.user.request.UserIdRequestModel;
import com.logistics.tms.client.feign.basicdata.user.response.GetUserByIdsResponseModel;
import com.logistics.tms.client.feign.basicdata.user.response.UserDetailResponseModel;
import com.logistics.tms.client.feign.basicdata.user.response.UserInfoModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/22 15:16
 */
@Component
public class BasicUserServiceApiHystrix implements UserServiceApi {
    @Override
    public Result<UserDetailResponseModel> getUserInfo(UserIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<UserInfoModel> getUserInfoModel(GetUserInfoModelRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetUserByIdsResponseModel>> getUserByIds(GetUserByIdsRequestModel requestModel) {
        return Result.timeout();
    }
}
