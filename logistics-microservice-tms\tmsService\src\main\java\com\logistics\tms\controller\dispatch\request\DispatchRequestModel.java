package com.logistics.tms.controller.dispatch.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DispatchRequestModel {

    private List<DispatchGoodsModel> vehicleRequestModels;

    @ApiModelProperty("预计提货/到货时间（回收类型是预计提货时间，其他类型是预计到货时间）")
    private Date expectArrivalTime;

    @ApiModelProperty("司机ID")
    private Long driverId;

    @ApiModelProperty("车辆ID")
    private Long vehicleId;

    @ApiModelProperty("挂车车辆ID")
    private Long trailerVehicleId;

    @ApiModelProperty("司机结算运费价格类型 1 单价 2 一口价")
    private Integer dispatchFreightFeeType;

    @ApiModelProperty("司机结算运费")
    private BigDecimal dispatchFreightFee;

    @ApiModelProperty("调度单备注")
    private String remark;

    @ApiModelProperty("多装多卸（装货数）")
    private Integer loadPointAmount;

    @ApiModelProperty("多装多卸（卸货数）")
    private Integer unloadPointAmount;

    @ApiModelProperty("多装多卸加价费用")
    private BigDecimal markupFee;

    /**
     * 临时定价：0 否，1 是
     */
    @ApiModelProperty("临时定价：0 否，1 是")
    private Integer ifProvisionalPricing;

    /**
     * 车主运费类型：1 单价，2 一口价
     */
    @ApiModelProperty("车主运费类型：1 单价，2 一口价")
    private Integer carrierPriceType;

    /**
     * 车主运费
     */
    @ApiModelProperty("车主运费")
    private BigDecimal carrierPrice;

    /**
     * 零担模式：0 否，1 是
     */
    private Integer ifLessThanTruckload;

    /**
     * 车长（选择“零担”时传-1，其他选择什么传什么）（零担模式时有值）
     */
    private BigDecimal vehicleLength;

    /**
     * 串点费用（零担模式时有值）
     */
    private BigDecimal crossPointFee;

    /**
     * 整车运费（零担模式时有值）
     */
    private BigDecimal carrierFreight;

    private Integer source;//来源：1 后台，2 前台



    /**
     * 是否匹配了车长和串点 0：否 1：是   v2.42
     */
    private Integer ifMatch;
    /**
     * 是否加入新的零担标识 有就是加入旧的  v2.42
     */
    private String shippingOrderCode;
}
