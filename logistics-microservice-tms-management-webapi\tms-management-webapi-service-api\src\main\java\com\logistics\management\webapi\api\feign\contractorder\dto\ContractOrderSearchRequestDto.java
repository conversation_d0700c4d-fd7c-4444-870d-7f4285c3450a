package com.logistics.management.webapi.api.feign.contractorder.dto;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/4/4 15:38
 */
@Data
public class ContractOrderSearchRequestDto extends AbstractPageForm<ContractOrderSearchRequestDto> {
    @ApiModelProperty("内部合同号")
    private String contractNoInternal;
    @ApiModelProperty("外部合同号")
    private String contractNoExternal;
    @ApiModelProperty("合同状态 1 待执行，2 执行中，3 已终止，4 已作废")
    private String contractStatus;
    @ApiModelProperty("合同类型 1 框架合同，2 单次合同")
    private String contractType;
    @ApiModelProperty("合同性质 1 货源合同，2 车源合同 3 租赁合同")
    private String contractNature;
    @ApiModelProperty("客户")
    private String customerName;
}
