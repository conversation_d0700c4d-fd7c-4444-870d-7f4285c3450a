package com.logistics.tms.api.feign.safegroupmeeting.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AddSafeGroupMeetingRequestModel {

    @ApiModelProperty(value = "年")
    private String meetingYear;

    @ApiModelProperty(value = "季度")
    private Integer meetingSeason;

    @ApiModelProperty(value = "会议标题")
    private String meetingTitle;

    @ApiModelProperty(value = "会议时间")
    private Date meetingTime;

    @ApiModelProperty(value = "会议地点")
    private String meetingPlace;

    @ApiModelProperty(value = "主持人")
    private String anchor;

    @ApiModelProperty(value = "记录人")
    private String recordPerson;

    @ApiModelProperty(value = "会议内容")
    private String content;

    @ApiModelProperty(value = "会议参与人员")
    private List<SafeGroupMeetingRelationModel> safeGroupMeetingRelationList;


    @ApiModelProperty(value = "会议图片")
    private List<SafetyGroupMeetingAttachmentModel> safetyGroupMeetingAttachmentList;
}
