package com.logistics.management.webapi.controller.companycarrierauthorization.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/3
 */
@Data
public class CarrierAuthorizationListResponseDto {

	@ApiModelProperty("车主授权信息id")
	private String carrierAuthorizationId = "";

	@ApiModelProperty("车主名")
	private String companyCarrierName = "";

	@ApiModelProperty("车主公司类型, 1:企业 2:个人")
	private String companyCarrierType = "";

	@ApiModelProperty("车主公司类型展示文本")
	private String companyCarrierTypeLabel = "";

	@ApiModelProperty("是否归档, 0:否 1:是")
	private String isArchived = "";

	@ApiModelProperty("是否归档展示文本")
	private String isArchivedLabel = "";

	@ApiModelProperty("授权状态, 0:待授权 1:待审核 2:已驳回 3:已授权")
	private String authorizationStatus = "";

	@ApiModelProperty("授权状态展示文本")
	private String authorizationStatusLabel = "";

	@ApiModelProperty("创建人")
	private String createdBy = "";

	@ApiModelProperty("创建时间")
	private String createdTime = "";

	@ApiModelProperty("归档人")
	private String archivedUsername = "";

	@ApiModelProperty("归档时间")
	private String archivedTime = "";
}
