package com.logistics.management.webapi.api.feign.freight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
@Data
public class DriverFreightByDemandOrderIdsAndVehicleResponseDto {
    @ApiModelProperty("价格类型 1 单价 2 整车价")
    private String priceType = "";
    @ApiModelProperty("价格")
    private String price ="";
    @ApiModelProperty("多装多卸车加价")
    private String multipleMarkupPrice = "";
}
