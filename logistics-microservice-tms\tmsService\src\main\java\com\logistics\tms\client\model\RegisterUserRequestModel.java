package com.logistics.tms.client.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/3/16 16:50
 */
@Data
public class RegisterUserRequestModel {

    @ApiModelProperty("用户账号，唯一（个人类型是手机号，企业类型是公司code）")
    private String account;

    @ApiModelProperty("用户/企业名称，必须和证件上登记的名称一致")
    private String name;

    @ApiModelProperty("用户类型，1表示个人 2表示企业")
    private String userType= "2";

    @ApiModelProperty("是否申请证书：0 不申请，1 申请")
    private String applyCert = "1";

    @ApiModelProperty("个人用户证件信息对象，个人类型传此对象")
    private PersonalCredentialInfoModel personalCredential;

    @ApiModelProperty("企业用户证件信息对象，企业类型传此对象")
    private CompanyCredentialInfoModel companyCredential;
}
