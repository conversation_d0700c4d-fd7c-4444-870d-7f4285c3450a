<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TMessageNoticeMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TMessageNotice">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="message_type" jdbcType="INTEGER" property="messageType" />
    <result column="message_module" jdbcType="INTEGER" property="messageModule" />
    <result column="object_type" jdbcType="INTEGER" property="objectType" />
    <result column="object_id" jdbcType="BIGINT" property="objectId" />
    <result column="object_code" jdbcType="VARCHAR" property="objectCode" />
    <result column="message_body" jdbcType="VARCHAR" property="messageBody" />
    <result column="message_pusher" jdbcType="VARCHAR" property="messagePusher" />
    <result column="message_push_time" jdbcType="TIMESTAMP" property="messagePushTime" />
    <result column="message_receiver" jdbcType="BIGINT" property="messageReceiver" />
    <result column="if_read" jdbcType="INTEGER" property="ifRead" />
    <result column="read_operator" jdbcType="VARCHAR" property="readOperator" />
    <result column="read_time" jdbcType="TIMESTAMP" property="readTime" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, message_id, message_type, message_module, object_type, object_id, object_code, 
    message_body, message_pusher, message_push_time, message_receiver, if_read, read_operator, 
    read_time, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_message_notice
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_message_notice
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TMessageNotice">
    insert into t_message_notice (id, message_id, message_type, 
      message_module, object_type, object_id, 
      object_code, message_body, message_pusher, 
      message_push_time, message_receiver, if_read, 
      read_operator, read_time, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{messageId,jdbcType=VARCHAR}, #{messageType,jdbcType=INTEGER}, 
      #{messageModule,jdbcType=INTEGER}, #{objectType,jdbcType=INTEGER}, #{objectId,jdbcType=BIGINT}, 
      #{objectCode,jdbcType=VARCHAR}, #{messageBody,jdbcType=VARCHAR}, #{messagePusher,jdbcType=VARCHAR}, 
      #{messagePushTime,jdbcType=TIMESTAMP}, #{messageReceiver,jdbcType=BIGINT}, #{ifRead,jdbcType=INTEGER}, 
      #{readOperator,jdbcType=VARCHAR}, #{readTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TMessageNotice">
    insert into t_message_notice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="messageId != null">
        message_id,
      </if>
      <if test="messageType != null">
        message_type,
      </if>
      <if test="messageModule != null">
        message_module,
      </if>
      <if test="objectType != null">
        object_type,
      </if>
      <if test="objectId != null">
        object_id,
      </if>
      <if test="objectCode != null">
        object_code,
      </if>
      <if test="messageBody != null">
        message_body,
      </if>
      <if test="messagePusher != null">
        message_pusher,
      </if>
      <if test="messagePushTime != null">
        message_push_time,
      </if>
      <if test="messageReceiver != null">
        message_receiver,
      </if>
      <if test="ifRead != null">
        if_read,
      </if>
      <if test="readOperator != null">
        read_operator,
      </if>
      <if test="readTime != null">
        read_time,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="messageType != null">
        #{messageType,jdbcType=INTEGER},
      </if>
      <if test="messageModule != null">
        #{messageModule,jdbcType=INTEGER},
      </if>
      <if test="objectType != null">
        #{objectType,jdbcType=INTEGER},
      </if>
      <if test="objectId != null">
        #{objectId,jdbcType=BIGINT},
      </if>
      <if test="objectCode != null">
        #{objectCode,jdbcType=VARCHAR},
      </if>
      <if test="messageBody != null">
        #{messageBody,jdbcType=VARCHAR},
      </if>
      <if test="messagePusher != null">
        #{messagePusher,jdbcType=VARCHAR},
      </if>
      <if test="messagePushTime != null">
        #{messagePushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="messageReceiver != null">
        #{messageReceiver,jdbcType=BIGINT},
      </if>
      <if test="ifRead != null">
        #{ifRead,jdbcType=INTEGER},
      </if>
      <if test="readOperator != null">
        #{readOperator,jdbcType=VARCHAR},
      </if>
      <if test="readTime != null">
        #{readTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TMessageNotice">
    update t_message_notice
    <set>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="messageType != null">
        message_type = #{messageType,jdbcType=INTEGER},
      </if>
      <if test="messageModule != null">
        message_module = #{messageModule,jdbcType=INTEGER},
      </if>
      <if test="objectType != null">
        object_type = #{objectType,jdbcType=INTEGER},
      </if>
      <if test="objectId != null">
        object_id = #{objectId,jdbcType=BIGINT},
      </if>
      <if test="objectCode != null">
        object_code = #{objectCode,jdbcType=VARCHAR},
      </if>
      <if test="messageBody != null">
        message_body = #{messageBody,jdbcType=VARCHAR},
      </if>
      <if test="messagePusher != null">
        message_pusher = #{messagePusher,jdbcType=VARCHAR},
      </if>
      <if test="messagePushTime != null">
        message_push_time = #{messagePushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="messageReceiver != null">
        message_receiver = #{messageReceiver,jdbcType=BIGINT},
      </if>
      <if test="ifRead != null">
        if_read = #{ifRead,jdbcType=INTEGER},
      </if>
      <if test="readOperator != null">
        read_operator = #{readOperator,jdbcType=VARCHAR},
      </if>
      <if test="readTime != null">
        read_time = #{readTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TMessageNotice">
    update t_message_notice
    set message_id = #{messageId,jdbcType=VARCHAR},
      message_type = #{messageType,jdbcType=INTEGER},
      message_module = #{messageModule,jdbcType=INTEGER},
      object_type = #{objectType,jdbcType=INTEGER},
      object_id = #{objectId,jdbcType=BIGINT},
      object_code = #{objectCode,jdbcType=VARCHAR},
      message_body = #{messageBody,jdbcType=VARCHAR},
      message_pusher = #{messagePusher,jdbcType=VARCHAR},
      message_push_time = #{messagePushTime,jdbcType=TIMESTAMP},
      message_receiver = #{messageReceiver,jdbcType=BIGINT},
      if_read = #{ifRead,jdbcType=INTEGER},
      read_operator = #{readOperator,jdbcType=VARCHAR},
      read_time = #{readTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>