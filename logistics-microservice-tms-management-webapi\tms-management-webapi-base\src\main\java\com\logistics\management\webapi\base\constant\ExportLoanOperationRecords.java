package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/10/8 19:43
 */
public class ExportLoanOperationRecords {
    private ExportLoanOperationRecords() {

    }

    private static final Map<String, String> LOAN_OPERATION_RECORD_INFO;

    static {
        LOAN_OPERATION_RECORD_INFO = new LinkedHashMap<>();
        LOAN_OPERATION_RECORD_INFO.put("操作", "operateTypeLabel");
        LOAN_OPERATION_RECORD_INFO.put("操作人", "operateUserName");
        LOAN_OPERATION_RECORD_INFO.put("操作时间", "operateTime");
    }

    public static Map<String, String> getOperationMap() {
        return LOAN_OPERATION_RECORD_INFO;
    }
}
