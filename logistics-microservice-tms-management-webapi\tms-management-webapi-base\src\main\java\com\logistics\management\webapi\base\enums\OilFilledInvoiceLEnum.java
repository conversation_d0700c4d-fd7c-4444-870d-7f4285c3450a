package com.logistics.management.webapi.base.enums;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/10/9
 * @description:
 */
public enum OilFilledInvoiceLEnum {

    NO_INVOICE("0", "无票"),
    HAVE_INVOICE("1", "有票"),
    ;


    private String key;
    private String value;

    OilFilledInvoiceLEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static OilFilledInvoiceLEnum getEnum(String key) {
        for (OilFilledInvoiceLEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return NO_INVOICE;
    }
}
