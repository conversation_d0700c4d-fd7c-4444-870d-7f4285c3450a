package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2024/07/09
*/
@Data
public class TRouteEnquiry extends BaseEntity {
    /**
    * 单号
    */
    @ApiModelProperty("单号")
    private String orderCode;

    /**
    * 状态：1 待业务审核，2 待车主确认，3 待结算审核，4 完成竞价
    */
    @ApiModelProperty("状态：1 待业务审核，2 待车主确认，3 待结算审核，4 完成竞价")
    private Integer status;

    /**
    * 是否取消：0 否，1 是
    */
    @ApiModelProperty("是否取消：0 否，1 是")
    private Integer ifCancel;

    /**
    * 货物名称
    */
    @ApiModelProperty("货物名称")
    private String goodsName;

    /**
    * 报价生效开始时间
    */
    @ApiModelProperty("报价生效开始时间")
    private Date quoteStartTime;

    /**
    * 报价生效结束时间
    */
    @ApiModelProperty("报价生效结束时间")
    private Date quoteEndTime;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 是否归档：0 否，1 是
    */
    @ApiModelProperty("是否归档：0 否，1 是")
    private Integer ifArchive;

    /**
    * 关联合同号
    */
    @ApiModelProperty("关联合同号")
    private String contractCode;

    /**
    * 业务审核人
    */
    @ApiModelProperty("业务审核人")
    private String auditorNameOne;

    /**
    * 业务审核时间
    */
    @ApiModelProperty("业务审核时间")
    private Date auditTimeOne;

    /**
    * 业务审核备注
    */
    @ApiModelProperty("业务审核备注")
    private String auditRemarkOne;

    /**
    * 结算审核人
    */
    @ApiModelProperty("结算审核人")
    private String auditorNameTwo;

    /**
    * 结算审核时间
    */
    @ApiModelProperty("结算审核时间")
    private Date auditTimeTwo;

    /**
    * 结算审核备注
    */
    @ApiModelProperty("结算审核备注")
    private String auditRemarkTwo;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}