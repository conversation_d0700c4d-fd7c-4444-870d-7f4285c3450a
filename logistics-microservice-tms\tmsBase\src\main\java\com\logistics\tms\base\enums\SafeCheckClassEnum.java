package com.logistics.tms.base.enums;

/**
 * @Author: sj
 * @Date: 2019/11/8 9:33
 */
public enum SafeCheckClassEnum {
    NULL(-1,""),
    SAFETY_PRF(1,"安全性能"),
    SAFETY_EQP(2, "安全器材"),
    SAFETY_OTHER(3, "车辆其他性能"),
    ;
    private Integer key;
    private String value;

    SafeCheckClassEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
