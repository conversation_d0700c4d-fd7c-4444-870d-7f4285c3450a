<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDriverAppointAddressMapper">
    <sql id="Base_Column_List_Decrypt">
        id,
        driver_appoint_id,
        load_address_code,
        load_province_id,
        load_province_name,
        load_city_id,
        load_city_name,
        load_area_id,
        load_area_name,
        load_detail_address,
        load_warehouse,
        load_company,
        consignor_name,
        AES_DECRYPT(UNHEX(consignor_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as consignor_mobile,
        expected_load_time,
        unload_address_code,
        unload_province_id,
        unload_province_name,
        unload_city_id,
        unload_city_name,
        unload_area_id,
        unload_area_name,
        unload_detail_address,
        unload_warehouse,
        unload_company,
        receiver_name,
        AES_DECRYPT(UNHEX(receiver_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as receiver_mobile,
        expected_unload_time,
        created_by,
        created_time,
        last_modified_by,
        last_modified_time,
        valid
    </sql>

    <select id="selectByPrimaryKeyDecrypt" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_driver_appoint_address
        where id = #{id,jdbcType=BIGINT}
        and valid = 1
    </select>
    <select id="queryAddressListByDriverAppointIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_driver_appoint_address
        where valid = 1 and driver_appoint_id in
        <foreach collection="driverAppointIds" item="driverAppointId" open="(" close=")" separator=",">
            #{driverAppointId,jdbcType=BIGINT}
        </foreach>
    </select>

    <insert id="insertSelectiveEncrypt">
        insert into t_driver_appoint_address
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="driverAppointId != null">
                driver_appoint_id,
            </if>
            <if test="loadAddressCode != null">
                load_address_code,
            </if>
            <if test="loadProvinceId != null">
                load_province_id,
            </if>
            <if test="loadProvinceName != null">
                load_province_name,
            </if>
            <if test="loadCityId != null">
                load_city_id,
            </if>
            <if test="loadCityName != null">
                load_city_name,
            </if>
            <if test="loadAreaId != null">
                load_area_id,
            </if>
            <if test="loadAreaName != null">
                load_area_name,
            </if>
            <if test="loadDetailAddress != null">
                load_detail_address,
            </if>
            <if test="loadWarehouse != null">
                load_warehouse,
            </if>
            <if test="loadCompany != null">
                load_company,
            </if>
            <if test="consignorName != null">
                consignor_name,
            </if>
            <if test="consignorMobile != null">
                consignor_mobile,
            </if>
            <if test="expectedLoadTime != null">
                expected_load_time,
            </if>
            <if test="unloadAddressCode != null">
                unload_address_code,
            </if>
            <if test="unloadProvinceId != null">
                unload_province_id,
            </if>
            <if test="unloadProvinceName != null">
                unload_province_name,
            </if>
            <if test="unloadCityId != null">
                unload_city_id,
            </if>
            <if test="unloadCityName != null">
                unload_city_name,
            </if>
            <if test="unloadAreaId != null">
                unload_area_id,
            </if>
            <if test="unloadAreaName != null">
                unload_area_name,
            </if>
            <if test="unloadDetailAddress != null">
                unload_detail_address,
            </if>
            <if test="unloadWarehouse != null">
                unload_warehouse,
            </if>
            <if test="unloadCompany != null">
                unload_company,
            </if>
            <if test="receiverName != null">
                receiver_name,
            </if>
            <if test="receiverMobile != null">
                receiver_mobile,
            </if>
            <if test="expectedUnloadTime != null">
                expected_unload_time,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by,
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time,
            </if>
            <if test="valid != null">
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="driverAppointId != null">
                #{driverAppointId,jdbcType=BIGINT},
            </if>
            <if test="loadAddressCode != null">
                #{loadAddressCode,jdbcType=VARCHAR},
            </if>
            <if test="loadProvinceId != null">
                #{loadProvinceId,jdbcType=BIGINT},
            </if>
            <if test="loadProvinceName != null">
                #{loadProvinceName,jdbcType=VARCHAR},
            </if>
            <if test="loadCityId != null">
                #{loadCityId,jdbcType=BIGINT},
            </if>
            <if test="loadCityName != null">
                #{loadCityName,jdbcType=VARCHAR},
            </if>
            <if test="loadAreaId != null">
                #{loadAreaId,jdbcType=BIGINT},
            </if>
            <if test="loadAreaName != null">
                #{loadAreaName,jdbcType=VARCHAR},
            </if>
            <if test="loadDetailAddress != null">
                #{loadDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="loadWarehouse != null">
                #{loadWarehouse,jdbcType=VARCHAR},
            </if>
            <if test="loadCompany != null">
                #{loadCompany,jdbcType=VARCHAR},
            </if>
            <if test="consignorName != null">
                #{consignorName,jdbcType=VARCHAR},
            </if>
            <if test="consignorMobile != null">
                HEX(AES_ENCRYPT(#{consignorMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="expectedLoadTime != null">
                #{expectedLoadTime,jdbcType=TIMESTAMP},
            </if>
            <if test="unloadAddressCode != null">
                #{unloadAddressCode,jdbcType=VARCHAR},
            </if>
            <if test="unloadProvinceId != null">
                #{unloadProvinceId,jdbcType=BIGINT},
            </if>
            <if test="unloadProvinceName != null">
                #{unloadProvinceName,jdbcType=VARCHAR},
            </if>
            <if test="unloadCityId != null">
                #{unloadCityId,jdbcType=BIGINT},
            </if>
            <if test="unloadCityName != null">
                #{unloadCityName,jdbcType=VARCHAR},
            </if>
            <if test="unloadAreaId != null">
                #{unloadAreaId,jdbcType=BIGINT},
            </if>
            <if test="unloadAreaName != null">
                #{unloadAreaName,jdbcType=VARCHAR},
            </if>
            <if test="unloadDetailAddress != null">
                #{unloadDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="unloadWarehouse != null">
                #{unloadWarehouse,jdbcType=VARCHAR},
            </if>
            <if test="unloadCompany != null">
                #{unloadCompany,jdbcType=VARCHAR},
            </if>
            <if test="receiverName != null">
                #{receiverName,jdbcType=VARCHAR},
            </if>
            <if test="receiverMobile != null">
                HEX(AES_ENCRYPT(#{receiverMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="expectedUnloadTime != null">
                #{expectedUnloadTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelectiveEncrypt">
        update t_driver_appoint_address
        <set>
            <if test="driverAppointId != null">
                driver_appoint_id = #{driverAppointId,jdbcType=BIGINT},
            </if>
            <if test="loadAddressCode != null">
                load_address_code = #{loadAddressCode,jdbcType=VARCHAR},
            </if>
            <if test="loadProvinceId != null">
                load_province_id = #{loadProvinceId,jdbcType=BIGINT},
            </if>
            <if test="loadProvinceName != null">
                load_province_name = #{loadProvinceName,jdbcType=VARCHAR},
            </if>
            <if test="loadCityId != null">
                load_city_id = #{loadCityId,jdbcType=BIGINT},
            </if>
            <if test="loadCityName != null">
                load_city_name = #{loadCityName,jdbcType=VARCHAR},
            </if>
            <if test="loadAreaId != null">
                load_area_id = #{loadAreaId,jdbcType=BIGINT},
            </if>
            <if test="loadAreaName != null">
                load_area_name = #{loadAreaName,jdbcType=VARCHAR},
            </if>
            <if test="loadDetailAddress != null">
                load_detail_address = #{loadDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="loadWarehouse != null">
                load_warehouse = #{loadWarehouse,jdbcType=VARCHAR},
            </if>
            <if test="loadCompany != null">
                load_company = #{loadCompany,jdbcType=VARCHAR},
            </if>
            <if test="consignorName != null">
                consignor_name = #{consignorName,jdbcType=VARCHAR},
            </if>
            <if test="consignorMobile != null">
                consignor_mobile = HEX(AES_ENCRYPT(#{consignorMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="expectedLoadTime != null">
                expected_load_time = #{expectedLoadTime,jdbcType=TIMESTAMP},
            </if>
            <if test="unloadAddressCode != null">
                unload_address_code = #{unloadAddressCode,jdbcType=VARCHAR},
            </if>
            <if test="unloadProvinceId != null">
                unload_province_id = #{unloadProvinceId,jdbcType=BIGINT},
            </if>
            <if test="unloadProvinceName != null">
                unload_province_name = #{unloadProvinceName,jdbcType=VARCHAR},
            </if>
            <if test="unloadCityId != null">
                unload_city_id = #{unloadCityId,jdbcType=BIGINT},
            </if>
            <if test="unloadCityName != null">
                unload_city_name = #{unloadCityName,jdbcType=VARCHAR},
            </if>
            <if test="unloadAreaId != null">
                unload_area_id = #{unloadAreaId,jdbcType=BIGINT},
            </if>
            <if test="unloadAreaName != null">
                unload_area_name = #{unloadAreaName,jdbcType=VARCHAR},
            </if>
            <if test="unloadDetailAddress != null">
                unload_detail_address = #{unloadDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="unloadWarehouse != null">
                unload_warehouse = #{unloadWarehouse,jdbcType=VARCHAR},
            </if>
            <if test="unloadCompany != null">
                unload_company = #{unloadCompany,jdbcType=VARCHAR},
            </if>
            <if test="receiverName != null">
                receiver_name = #{receiverName,jdbcType=VARCHAR},
            </if>
            <if test="receiverMobile != null">
                receiver_mobile = HEX(AES_ENCRYPT(#{receiverMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="expectedUnloadTime != null">
                expected_unload_time = #{expectedUnloadTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>