package com.logistics.tms.controller.routeenquiry.response;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/7/9 14:01
 */
@Data
public class GetCreateQuotationInfoResponseModel {

    /**
     * 竞价单号
     */
    private String orderCode;

    /**
     * 关联合同号
     */
    private String contractCode;

    /**
     * 车主公司类型：1 公司，2 个人
     */
    private Integer companyCarrierType;

    /**
     * 车主公司名称
     */
    private String companyCarrierName;

    /**
     * 车主账号名称
     */
    private String carrierContactName;

    /**
     * 车主账号手机号
     */
    private String carrierContactPhone;

    /**
     * 报价人
     */
    private String quoteOperator;

    /**
     * 报价人电话
     */
    private String quoteOperatorPhone;

    /**
     * 报价时间
     */
    private Date quoteTime;

    /**
     * 货物名称
     */
    private String goodsName;

    /**
     * 报价生效开始时间
     */
    private Date quoteStartTime;

    /**
     * 报价生效结束时间
     */
    private Date quoteEndTime;


    /**
     * 地址列表
     */
    private List<GetRouteEnquiryDetailAddressListResponseModel> addressList;


    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 业务审核人
     */
    private String auditorNameOne;

    /**
     * 业务审核时间
     */
    private Date auditTimeOne;

}
