package com.logistics.tms.controller.reservationorder.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/19 09:48
 */
@Data
public class ReservationOrderSignDetailResponseModel {
    /**
     * 预约单id
     */
    private Long reservationOrderId;

    /**
     * 预约单号
     */
    private String reservationOrderCode;

    /**
     * 预约类型：1 提货，2 卸货
     */
    private Integer reservationType;

    /**
     * 省
     */
    private String provinceName;
    /**
     * 市
     */
    private String cityName;
    /**
     * 区
     */
    private String areaName;
    /**
     * 详细地址
     */
    private String detailAddress;
    /**
     * 仓库名称
     */
    private String warehouse;

    /**
     * 运单列表
     */
    private List<ReservationCarrierOrderListResponseModel> orderList=new ArrayList<>();

    /**
     * 预计里程数（公里）
     */
    private BigDecimal expectMileage;

    /**
     * 预约开始时间
     */
    private Date reservationStartTime;
    /**
     * 预约结束时间
     */
    private Date reservationEndTime;


    /**
     * 委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨；100 新生回收，101 新生销售
     */
    private Integer demandOrderEntrustType;

    /**
     * 预约类型
     */
    private String demandOrderEntrustTypeLabel="";

}
