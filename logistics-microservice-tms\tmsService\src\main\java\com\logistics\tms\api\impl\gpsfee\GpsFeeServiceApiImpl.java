package com.logistics.tms.api.impl.gpsfee;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.gpsfee.GpsFeeServiceApi;
import com.logistics.tms.api.feign.gpsfee.model.*;
import com.logistics.tms.biz.gpsfee.GpsFeeBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/8 10:51
 */
@RestController
public class GpsFeeServiceApiImpl implements GpsFeeServiceApi {

    @Autowired
    private GpsFeeBiz gpsFeeBiz;

    /**
     * 查询gps费用列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<SearchGpsFeeListResponseModel>> searchGpsFeeList(@RequestBody SearchGpsFeeListRequestModel requestModel) {
        List<SearchGpsFeeListResponseModel> list = gpsFeeBiz.searchGpsFeeList(requestModel.enablePaging());
        return Result.success(new PageInfo<>(list));
    }

    /**
     * 统计gps费用列表各状态数量
     * @param requestModel
     * @return
     */
    @Override
    public Result<SearchGpsFeeListCountResponseModel> searchGpsFeeListCount(@RequestBody SearchGpsFeeListRequestModel requestModel) {
        return Result.success(gpsFeeBiz.searchGpsFeeListCount(requestModel));
    }

    /**
     * 查看详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<GpsFeeDetailResponseModel> getGpsFeeDetail(@RequestBody GpsFeeIdRequestModel requestModel) {
        return Result.success(gpsFeeBiz.getGpsFeeDetail(requestModel));
    }

    /**
     * 新增/修改gps费用
     * @param requestModel
     * @return
     */
    @Override
    public Result addOrModifyGpsFee(@RequestBody AddOrModifyGpsFeeRequestModel requestModel) {
        gpsFeeBiz.addOrModifyGpsFee(requestModel);
        return Result.success(true);
    }

    /**
     * 导出gps费用
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchGpsFeeListResponseModel>> exportGpsFee(@RequestBody SearchGpsFeeListRequestModel requestModel) {
        return Result.success(gpsFeeBiz.searchGpsFeeList(requestModel));
    }

    /**
     * 终止gps费用（修改终止时间）
     * @param requestModel
     * @return
     */
    @Override
    public Result terminationGpsFee(@RequestBody TerminationGpsFeeRequestModel requestModel) {
        gpsFeeBiz.terminationGpsFee(requestModel);
        return Result.success(true);
    }

    /**
     * 查询gps费用操作记录
     * @param requestModel
     * @return
     */
    @Override
    public Result<List<GpsFeeRecordsListResponseModel>> getGpsFeeRecords(@RequestBody GpsFeeIdRequestModel requestModel) {
        return Result.success(gpsFeeBiz.getGpsFeeRecords(requestModel));
    }

    /**
     * 查询gps费用扣减历史
     * @param requestModel
     * @return
     */
    @Override
    public Result<GetDeductingHistoryByGpsFeeIdResponseModel> getGpsFeeDeductingHistory(@RequestBody GpsFeeIdRequestModel requestModel) {
        return Result.success(gpsFeeBiz.getGpsFeeDeductingHistory(requestModel));
    }
}
