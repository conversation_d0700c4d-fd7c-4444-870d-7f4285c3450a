package com.logistics.tms.controller.biddingorder;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.biddingorder.BiddingOrderBiz;
import com.logistics.tms.controller.biddingorder.request.*;
import com.logistics.tms.controller.biddingorder.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 竞价管理
 * <AUTHOR>
 * @date 2024/04/26
 */
@RestController
@RequestMapping(value = "/service/biddingOrder")
public class BiddingOrderController {
    @Resource
    private BiddingOrderBiz biddingOrderBiz;


    /**
     * 后台查询列表
     *
     * @param requestModel 请求参数
     * @return {@link Result}<{@link PageInfo}<{@link SearchBiddingOrderListResponseModel}>>
     */
    @PostMapping(value = "/searchBiddingOrderListByManager")
    public Result<PageInfo<SearchBiddingOrderListResponseModel>> searchBiddingOrderListByManager(
            @RequestBody @Valid SearchBiddingOrderListRequestModel requestModel){
        return Result.success(biddingOrderBiz.searchBiddingOrderListByManager(requestModel));
    }

    /**
     * 前台查询列表
     *
     * @param requestModel 请求参数
     * @return {@link Result}<{@link PageInfo}<{@link SearchBiddingOrderListResponseModel}>>
     */
    @PostMapping(value = "/searchBiddingOrderListByCustomer")
    public Result<PageInfo<SearchBiddingOrderListResponseModel>> searchBiddingOrderListByCustomer(
            @RequestBody @Valid SearchBiddingOrderListRequestModel requestModel){
        return Result.success(biddingOrderBiz.searchBiddingOrderListByCustomer(requestModel));
    }

    /**
     * 前台竞价单详情
     *
     * @param requestModel 请求参数
     * @return {@link Result}<{@link BiddingOrderDetailResponseModel}>
     */
    @PostMapping(value = "/biddingOrderDetailByCustomer")
    public Result<BiddingOrderDetailResponseModel> biddingOrderDetailByCustomer(
            @RequestBody @Valid BiddingOrderDetailRequestModel requestModel){
        return Result.success(biddingOrderBiz.biddingOrderDetailByCustomer(requestModel));
    }

    /**
     * 竞价单详情
     * @param requestModel 请求参数
     * @return 竞价单详情
     */
    @PostMapping(value = "/biddingOrderDetailByManager")
    public Result<BiddingOrderDetailByManagerResponseModel> biddingOrderDetailByManager(
            @RequestBody @Valid BiddingOrderDetailRequestModel requestModel){
        return Result.success(biddingOrderBiz.biddingOrderDetailByManager(requestModel));
    }

    /**
     * 获取最低价 v3.20.0
     * @param requestModel 请求参数
     * @return 最低价
     */
    @PostMapping(value = "/bottomPrice")
    public Result<BottomPriceResponseModel> bottomPrice(
            @RequestBody BottomPriceRequestModel requestModel){
        return Result.success(biddingOrderBiz.bottomPrice(requestModel));
    }

    /**
     * 选择车主报价 v3.20.0
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/confirmQuote")
    public Result<Boolean> confirmQuote(@RequestBody ConfirmQuoteRequestModel requestModel){
        biddingOrderBiz.confirmQuote(requestModel);
        return Result.success(true);
    }

    /**
     * 修改报价 v3.20.0
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/modifyQuote")
    public Result<Boolean> modifyQuote(@RequestBody ModifyQuoteRequestModel requestModel){
        biddingOrderBiz.modifyQuote(requestModel);
        return Result.success(true);
    }




    /**
     * 前台报价
     *
     * @param requestModel 请求参数
     * @return {@link Result}<{@link Boolean}>
     */
    @PostMapping(value = "/quoteByCustomer")
    public Result<Boolean> quoteByCustomer(
            @RequestBody @Valid QuoteRequestModel requestModel){
        biddingOrderBiz.quote(requestModel);
        return Result.success(true);
    }


    /**
     * 前台取消报价
     *
     * @param requestModel 请求参数
     * @return {@link Result}<{@link Boolean}>
     */
    @PostMapping(value = "/cancelBiddingByCustomer")
    public Result<Boolean> cancelBiddingByCustomer(
            @RequestBody @Valid CancelBiddingRequestModel requestModel){
        biddingOrderBiz.cancelBidding(requestModel);
        return Result.success(true);
    }

    /**
     * 暂停报价
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/stopBiddingByManager")
    public Result<Boolean> stopBiddingByManager(
            @RequestBody @Valid StopBiddingRequestModel requestModel){
        biddingOrderBiz.stopBiddingByManager(requestModel);
        return Result.success(true);
    }

    /**
     * 取消报价
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/cancelBiddingByManager")
    public Result<Boolean> cancelBiddingByManager(
            @RequestBody @Valid CancelBiddingRequestModel requestModel){
        biddingOrderBiz.cancelBiddingByManager(requestModel);
        return Result.success(true);
    }

    /**
     * 重新报价 v3.20.0
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/rebiddingQuote")
    public Result<Boolean> rebiddingQuote(@RequestBody RebiddingRequestModel requestModel){
        biddingOrderBiz.rebiddingQuote(requestModel);
        return Result.success(true);
    }


    /**
     * 删除需求单 v3.20.0
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/delDemand")
    public Result<Boolean> delDemand(
            @RequestBody DelDemandRequestModel requestModel){
        biddingOrderBiz.delDemand(requestModel);
        return Result.success(true);
    }

    /**
     * 竞价查询需求单 v3.20.0
     * @param requestModel 请求参数
     * @return SearchBiddingDemandResponseModel
     */
    @PostMapping(value = "/searchBiddingDemand")
    public Result<List<SearchBiddingDemandResponseModel>> searchBiddingDemand(
            @RequestBody SearchBiddingDemandRequestModel requestModel){
        return Result.success(biddingOrderBiz.searchBiddingDemand(requestModel));
    }


    /**
     * 车主报价详情 v3.20.0
     * @param requestModel 请求参数
     * @return 车主报价详情
     */
    @PostMapping(value = "/biddingOrderQuoteDetail")
    public Result<BiddingOrderQuoteDetailResponseModel> biddingOrderQuoteDetail(
            @RequestBody BiddingOrderQuoteDetailRequestModel requestModel){
        return Result.success(biddingOrderBiz.biddingOrderQuoteDetail(requestModel));
    }


    /**
     * 新增需求单 v3.20.0
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/addDemand")
    public Result<Boolean> addDemand(
            @RequestBody @Valid AddDemandRequestModel requestModel){
        biddingOrderBiz.addDemand(requestModel);
        return Result.success(true);
    }
}
