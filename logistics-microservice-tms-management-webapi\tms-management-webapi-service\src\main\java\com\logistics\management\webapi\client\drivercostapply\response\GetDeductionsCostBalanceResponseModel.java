package com.logistics.management.webapi.client.drivercostapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class GetDeductionsCostBalanceResponseModel {

    @ApiModelProperty("司机ID")
    private Long driverId;

    @ApiModelProperty("司机姓名")
    private String driverName;

    @ApiModelProperty("司机电话")
    private String driverPhone;

    @ApiModelProperty("备用金余额")
    private BigDecimal balance;
}
