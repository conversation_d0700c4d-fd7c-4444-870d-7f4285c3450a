package com.logistics.tms.mapper;

import com.logistics.tms.controller.carrierorder.response.CarrierOrderOtherFeeItemModel;
import com.logistics.tms.controller.carrierorderotherfee.response.GetOtherFeeItemByCarrierOrderIdResponseModel;
import com.logistics.tms.entity.TCarrierOrderOtherFeeItem;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Mybatis Generator on 2022/09/02
 */
@Mapper
public interface TCarrierOrderOtherFeeItemMapper extends BaseMapper<TCarrierOrderOtherFeeItem> {

	/**
	 * 查询已审核的运单其他费用
	 *
	 * @param carrierOrderIds 运单id
	 * @return 运单其他费用
	 */
	List<CarrierOrderOtherFeeItemModel> selectOtherFeeItemByCarrierIds(@Param("carrierOrderIds") String carrierOrderIds);

	/**
	 * 批量查询临时费用
	 *
	 * @param carrierOrderOtherFeeId 临时费用表id
	 * @return
	 */
	List<TCarrierOrderOtherFeeItem> getByCarrierOrderOtherFeeId(@Param("carrierOrderOtherFeeId") Long carrierOrderOtherFeeId);

	List<GetOtherFeeItemByCarrierOrderIdResponseModel> getItemByCarrierOrderOtherFeeId(@Param("carrierOrderOtherFeeId") Long carrierOrderOtherFeeId);
}