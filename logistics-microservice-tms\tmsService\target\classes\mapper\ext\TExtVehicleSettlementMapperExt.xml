<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TExtVehicleSettlementMapper" >
    <select id="searchExtVehicleSettlementIdList" resultType="java.lang.Long">
        select
        DISTINCT tevs.id
        from t_ext_vehicle_settlement tevs
        left join t_carrier_order tco on tco.id = tevs.carrier_order_id and tco.valid = 1
        left join t_carrier_order_address tcoa on tcoa.carrier_order_id = tevs.carrier_order_id and tcoa.valid = 1
        left join t_carrier_order_goods tcog on tcog.carrier_order_id = tevs.carrier_order_id and tcog.valid = 1
        where tevs.valid = 1
        <if test="params.payStatus != null">
            and tevs.status = #{params.payStatus,jdbcType=INTEGER}
        </if>
        <if test="params.status != null">
            <choose>
                <when test="params.status==2">
                    and tco.if_empty=1
                </when>
                <otherwise>
                    and tco.status = #{params.status,jdbcType=INTEGER}
                </otherwise>
            </choose>
        </if>
        <if test="params.carrierOrderCode != null and params.carrierOrderCode != ''">
            and (INSTR(tco.carrier_order_code,#{params.carrierOrderCode,jdbcType=VARCHAR})
            or INSTR(REPLACE(tco.carrier_order_code,'-',''),#{params.carrierOrderCode,jdbcType=VARCHAR}))
        </if>
        <if test="params.demandOrderCode != null and params.demandOrderCode != ''">
            and INSTR(tco.demand_order_code,#{params.demandOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="params.loadAddress != null and params.loadAddress != ''">
            and (instr(tcoa.load_province_name,#{params.loadAddress,jdbcType=VARCHAR})
            OR instr(tcoa.load_city_name,#{params.loadAddress,jdbcType=VARCHAR})
            OR instr(tcoa.load_area_name,#{params.loadAddress,jdbcType=VARCHAR})
            OR instr(tcoa.load_detail_address,#{params.loadAddress,jdbcType=VARCHAR})
            OR instr(tcoa.load_warehouse,#{params.loadAddress,jdbcType=VARCHAR})
            or instr(concat(tcoa.load_province_name,tcoa.load_city_name,tcoa.load_area_name,tcoa.load_detail_address,tcoa.load_warehouse),#{params.loadAddress,jdbcType=VARCHAR}))
        </if>
        <if test="params.unloadAddress != null and params.unloadAddress != ''">
            and (instr(tcoa.unload_province_name,#{params.unloadAddress,jdbcType=VARCHAR})
            OR instr(tcoa.unload_city_name,#{params.unloadAddress,jdbcType=VARCHAR})
            OR instr(tcoa.unload_area_name,#{params.unloadAddress,jdbcType=VARCHAR})
            OR instr(tcoa.unload_detail_address,#{params.unloadAddress,jdbcType=VARCHAR})
            OR instr(tcoa.unload_warehouse,#{params.unloadAddress,jdbcType=VARCHAR})
            or instr(concat(tcoa.unload_province_name,tcoa.unload_city_name,tcoa.unload_area_name,tcoa.unload_detail_address,tcoa.unload_warehouse),#{params.unloadAddress,jdbcType=VARCHAR}))
        </if>
        <if test="params.vehicleNo != null and params.vehicleNo != ''">
            and instr(tevs.vehicle_no,#{params.vehicleNo,jdbcType=VARCHAR})
        </if>
        <if test="params.driver != null and params.driver != ''">
            and (instr(concat(tevs.staff_name,tevs.staff_mobile),#{params.driver,jdbcType=VARCHAR})
            or instr(concat(tevs.staff_name,'-',tevs.staff_mobile),#{params.driver,jdbcType=VARCHAR}))
        </if>
        <if test="params.dispatchUser != null and params.dispatchUser != ''">
            and instr(tco.dispatch_user_name,#{params.dispatchUser,jdbcType=VARCHAR})
        </if>
        <if test="params.goodsName != null and params.goodsName != ''">
            and instr(tcog.goods_name,#{params.goodsName,jdbcType=VARCHAR})
        </if>
        <if test="params.goodsSize != null and params.goodsSize != ''">
            and (instr(concat(tcog.length,'*',tcog.width,'*',tcog.height),#{params.goodsSize,jdbcType=VARCHAR})
            or instr(tcog.goods_size,#{params.goodsSize,jdbcType=VARCHAR}))
        </if>
        <if test="params.dispatchTimeFrom != null and params.dispatchTimeFrom != ''">
            and tco.dispatch_time &gt;= DATE_FORMAT(#{params.dispatchTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="params.dispatchTimeTo != null and params.dispatchTimeTo != ''">
            and tco.dispatch_time &lt;= DATE_FORMAT(#{params.dispatchTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.companyEntrustIds != null and params.companyEntrustIds != ''">
            and tco.company_entrust_id in (${params.companyEntrustIds})
        </if>
        <if test="params.ids != null and params.ids != ''">
            and tevs.id in (${params.ids})
        </if>
        order by tevs.last_modified_time desc,tevs.id desc
    </select>

    <resultMap id="searchExtVehicleSettlementList_Map" type="com.logistics.tms.api.feign.extvehiclesettlement.model.SearchExtVehicleSettlementListResponseModel">
        <id column="id" property="extVehicleSettlementId" jdbcType="BIGINT"/>
        <result column="payStatus" property="payStatus" jdbcType="INTEGER"/>
        <result column="carrierOrderId" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="staff_name" property="driverName" jdbcType="VARCHAR"/>
        <result column="staff_mobile" property="driverMobile" jdbcType="VARCHAR"/>
        <result column="driver_total_fee" property="driverTotalFee" jdbcType="DECIMAL"/>
        <result column="settlement_amount" property="settlementAmount" jdbcType="DECIMAL"/>
        <result column="sign_time" property="signTime" jdbcType="TIMESTAMP"/>
        <result column="dispatch_user_name" property="dispatchUserName" jdbcType="VARCHAR"/>
        <result column="company_entrust_name" property="companyEntrustName" jdbcType="VARCHAR"/>
        <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT"/>
        <result column="company_carrier_name" property="companyCarrierName" jdbcType="VARCHAR"/>
        <result column="company_carrier_type" property="companyCarrierType" jdbcType="INTEGER"/>
        <result column="carrier_contact_name" property="carrierContactName" jdbcType="VARCHAR"/>
        <result column="carrier_contact_phone" property="carrierContactPhone" jdbcType="VARCHAR"/>
        <result column="dispatch_time" property="dispatchTime" jdbcType="TIMESTAMP"/>
        <result column="settlement_unit" property="settlementUnit" jdbcType="INTEGER"/>
        <result column="if_empty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="demand_order_source" property="demandOrderSource" jdbcType="INTEGER"/>
        <collection property="goodsInfoList" ofType="com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="g_expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="g_load_amount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="g_unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="g_sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>
    <select id="searchExtVehicleSettlementList" resultMap="searchExtVehicleSettlementList_Map">
        select
        tevs.id,
        tevs.vehicle_no,
        tevs.staff_name,
        tevs.staff_mobile,
        tevs.status as payStatus,
        tevs.driver_total_fee,
        tevs.settlement_amount,
        tevs.settlement_unit,

        tco.id as carrierOrderId,
        tco.carrier_order_code,
        tco.status,
        tco.sign_time,
        tco.demand_order_id,
        tco.demand_order_code,
        tco.dispatch_user_name,
        tco.dispatch_time,
        tco.company_entrust_name,
        tco.company_carrier_id,
        tco.company_carrier_name,
        tco.company_carrier_type,
        tco.carrier_contact_name,
        AES_DECRYPT(UNHEX(tco.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrier_contact_phone,
        tco.if_empty,
        tco.demand_order_source,

        tcoa.load_city_name,
        tcoa.unload_city_name,

        tcog.id as goodsId,
        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount as g_expect_amount,
        tcog.load_amount as g_load_amount,
        tcog.unload_amount as g_unload_amount,
        tcog.sign_amount as g_sign_amount

        from t_ext_vehicle_settlement tevs
        left join t_carrier_order tco on tco.id = tevs.carrier_order_id and tco.valid = 1
        left join t_carrier_order_address tcoa on tcoa.carrier_order_id = tevs.carrier_order_id and tcoa.valid = 1
        left join t_carrier_order_goods tcog on tcog.carrier_order_id = tevs.carrier_order_id and tcog.valid = 1
        where tevs.valid = 1
        and tevs.id in (${ids})
        order by tevs.last_modified_time desc,tevs.id desc
    </select>

    <resultMap id="extVehicleSettlementDetail_Map" type="com.logistics.tms.api.feign.extvehiclesettlement.model.ExtVehicleSettlementDetailResponseModel">
        <id column="extVehicleSettlementId" property="extVehicleSettlementId" jdbcType="BIGINT"/>
        <result column="vehicleNo" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="driverTotalFee" property="driverTotalFee" jdbcType="DECIMAL"/>
        <result column="settlementAmount" property="settlementAmount" jdbcType="DECIMAL"/>
        <result column="settlementUnit" property="settlementUnit" jdbcType="INTEGER"/>
        <result column="paymentChannel" property="paymentChannel" jdbcType="VARCHAR"/>
        <result column="paymentNo" property="paymentNo" jdbcType="VARCHAR"/>
        <result column="paymentFee" property="paymentFee" jdbcType="DECIMAL"/>
        <result column="reimburseFee" property="reimburseFee" jdbcType="DECIMAL"/>
        <result column="totalFee" property="totalFee" jdbcType="DECIMAL"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="carrierOrderCode" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="ifEmpty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="companyCarrierId" property="companyCarrierId" jdbcType="BIGINT"/>
        <collection property="goodsInfoList" ofType="com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel">
            <id column="goodsId" property="goodsId" jdbcType="BIGINT"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goods_size" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="g_expect_amount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="g_load_amount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="g_unload_amount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="g_sign_amount" property="signAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>
    <select id="extVehicleSettlementDetail" resultMap="extVehicleSettlementDetail_Map">
        select
        tevs.id as extVehicleSettlementId,
        tevs.vehicle_no as vehicleNo,
        tevs.driver_total_fee as driverTotalFee,
        tevs.settlement_amount as settlementAmount,
        tevs.settlement_unit as settlementUnit,

        tevsi.payment_channel as paymentChannel,
        tevsi.payment_no as paymentNo,
        tevsi.payment_fee as paymentFee,
        tevsi.reimburse_fee as reimburseFee,
        tevsi.total_fee as totalFee,
        tevsi.remark,

        tco.carrier_order_code as carrierOrderCode,
        tco.if_empty as ifEmpty,
        tco.company_carrier_id as companyCarrierId,
        tcog.id as goodsId,
        tcog.goods_name,
        tcog.length,
        tcog.width,
        tcog.height,
        tcog.goods_size,
        tcog.expect_amount as g_expect_amount,
        tcog.load_amount as g_load_amount,
        tcog.unload_amount as g_unload_amount,
        tcog.sign_amount as g_sign_amount
        from t_ext_vehicle_settlement tevs
        left join t_ext_vehicle_settlement_item tevsi on tevsi.ext_vehicle_settlement_id = tevs.id and tevsi.if_fallback = 0 and tevsi.valid = 1
        left join t_carrier_order tco on tco.id = tevs.carrier_order_id and tco.valid = 1
        left join t_carrier_order_goods tcog on tcog.carrier_order_id = tevs.carrier_order_id and tcog.valid = 1
        where tevs.valid = 1
        and tevs.id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TExtVehicleSettlement" >
        <foreach collection="list" item="item" separator=";">
            insert into t_ext_vehicle_settlement
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    id,
                </if>
                <if test="item.carrierOrderId != null" >
                    carrier_order_id,
                </if>
                <if test="item.vehicleId != null" >
                    vehicle_id,
                </if>
                <if test="item.vehicleNo != null" >
                    vehicle_no,
                </if>
                <if test="item.staffId != null" >
                    staff_id,
                </if>
                <if test="item.staffName != null" >
                    staff_name,
                </if>
                <if test="item.staffMobile != null" >
                    staff_mobile,
                </if>
                <if test="item.driverTotalFee != null" >
                    driver_total_fee,
                </if>
                <if test="item.settlementUnit != null">
                    settlement_unit,
                </if>
                <if test="item.settlementAmount != null">
                    settlement_amount,
                </if>
                <if test="item.status != null" >
                    status,
                </if>
                <if test="item.remark != null" >
                    remark,
                </if>
                <if test="item.createdBy != null" >
                    created_by,
                </if>
                <if test="item.createdTime != null" >
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="item.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.carrierOrderId != null" >
                    #{item.carrierOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleId != null" >
                    #{item.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleNo != null" >
                    #{item.vehicleNo,jdbcType=VARCHAR},
                </if>
                <if test="item.staffId != null" >
                    #{item.staffId,jdbcType=BIGINT},
                </if>
                <if test="item.staffName != null" >
                    #{item.staffName,jdbcType=VARCHAR},
                </if>
                <if test="item.staffMobile != null" >
                    #{item.staffMobile,jdbcType=VARCHAR},
                </if>
                <if test="item.driverTotalFee != null" >
                    #{item.driverTotalFee,jdbcType=DECIMAL},
                </if>
                <if test="item.settlementUnit != null">
                    #{item.settlementUnit,jdbcType=INTEGER},
                </if>
                <if test="item.settlementAmount != null">
                    #{item.settlementAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.status != null" >
                    #{item.status,jdbcType=INTEGER},
                </if>
                <if test="item.remark != null" >
                    #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null" >
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <update id="batchUpdateSelective" parameterType="com.logistics.tms.entity.TExtVehicleSettlement">
        <foreach collection="recordList" item="item" separator=";">
            update t_ext_vehicle_settlement
            <set>
                <if test="item.carrierOrderId != null">
                    carrier_order_id = #{item.carrierOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleId != null">
                    vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleNo != null">
                    vehicle_no = #{item.vehicleNo,jdbcType=VARCHAR},
                </if>
                <if test="item.staffId != null">
                    staff_id = #{item.staffId,jdbcType=BIGINT},
                </if>
                <if test="item.staffName != null">
                    staff_name = #{item.staffName,jdbcType=VARCHAR},
                </if>
                <if test="item.staffMobile != null">
                    staff_mobile = #{item.staffMobile,jdbcType=VARCHAR},
                </if>
                <if test="item.driverTotalFee != null">
                    driver_total_fee = #{item.driverTotalFee,jdbcType=DECIMAL},
                </if>
                <if test="item.settlementUnit != null">
                    settlement_unit = #{item.settlementUnit,jdbcType=INTEGER},
                </if>
                <if test="item.settlementAmount != null">
                    settlement_amount = #{item.settlementAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.status != null">
                    status = #{item.status,jdbcType=INTEGER},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getByCarrierOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_ext_vehicle_settlement
        where valid = 1
        and carrier_order_id in (${carrierOrderIds})
    </select>

    <select id="getByCarrierOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_ext_vehicle_settlement
        where valid = 1
          and carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
    </select>
</mapper>