package com.logistics.tms.controller.freightconfig.response;

import lombok.Data;

import java.util.Date;

@Data
public class AssociateCarrierListRespModel {

    /**
     * 主键id
     */
    private Long shippingFreightId ;

    /**
     * 车主id
     */
    private Long companyCarrierId;

    /**
     * 承运商名称
     */
    private String companyCarrierName;

    /**
     * 零担运价新增人
     */
    private String shippingFreightAddUser;

    /**
     * 零担运价新增时间
     */
    private Date shippingFreightAddTime;

}
