package com.logistics.management.webapi.base.enums;

public enum FreightRuleAdjustRuleEnum {
    DEFAULT(0,"未设置"),
    UP(1,"上调"),
    DOWN(2,"下调");

    private Integer key;
    private String value;

    FreightRuleAdjustRuleEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static FreightRuleAdjustRuleEnum getEnum(Integer key) {
        for (FreightRuleAdjustRuleEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return null;
    }
}
