package com.logistics.management.webapi.client.carrierorderotherfee;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.carrierorderotherfee.hystrix.CarrierOrderOtherFeeClientHystrix;
import com.logistics.management.webapi.client.carrierorderotherfee.request.*;
import com.logistics.management.webapi.client.carrierorderotherfee.response.CarrierOrderOtherFeeDetailResponseModel;
import com.logistics.management.webapi.client.carrierorderotherfee.response.GetOtherFeeRecordResponseModel;
import com.logistics.management.webapi.client.carrierorderotherfee.response.SearchOtherFeeListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/19 13:20
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/carrierOrderOtherFee",
        fallback = CarrierOrderOtherFeeClientHystrix.class)
public interface CarrierOrderOtherFeeClient {

    /**
     * 临时费用列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "临时费用列表")
    @PostMapping(value = "/searchList")
    Result<PageInfo<SearchOtherFeeListResponseModel>> searchList(@RequestBody SearchOtherFeeListRequestModel requestModel);

    /**
     * 新增临时费用
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "新增临时费用")
    @PostMapping(value = "/addCarrierOrderOtherFee")
    Result<Boolean> addCarrierOrderOtherFee(@RequestBody AddCarrierOrderOtherFeeRequestModel requestModel);

    /**
     * 临时费用详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "临时费用详情")
    @PostMapping(value = "/getCarrierOrderOtherFeeDetail")
    Result<CarrierOrderOtherFeeDetailResponseModel> getCarrierOrderOtherFeeDetail(@RequestBody CarrierOrderOtherFeeDetailRequestModel requestModel);

    /**
     * 审核/提交临时费用
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "审核/提交临时费用 v2.44(2)")
    @PostMapping(value = "/commitCarrierOrderOtherFee")
    Result<Boolean> commitCarrierOrderOtherFee(@RequestBody CommitCarrierOrderOtherFeeRequestModel requestModel);

    /**
     * 驳回临时费用
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "审核驳回临时费用")
    @PostMapping(value = "/rejectOtherFee")
    Result<Boolean> rejectOtherFee(@RequestBody RejectCarrierOrderOtherFeeRequestModel requestModel);

    /**
     * 撤销
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "撤销")
    @PostMapping(value = "/cancelOtherFee")
    Result<Boolean> cancelOtherFee(@RequestBody CarrierOrderOtherFeeDetailRequestModel requestModel);

    /**
     * 操作日志查询
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查看操作日志")
    @PostMapping(value = "/getOtherFeeRecord")
    Result<List<GetOtherFeeRecordResponseModel>> getOtherFeeRecord(@RequestBody CarrierOrderOtherFeeDetailRequestModel requestModel);

    /**
     * 回退
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "回退")
    @PostMapping(value = "/rollbackOtherFee")
    Result<Boolean> rollbackOtherFee(@RequestBody CarrierOrderOtherFeeDetailRequestModel requestModel);
}
