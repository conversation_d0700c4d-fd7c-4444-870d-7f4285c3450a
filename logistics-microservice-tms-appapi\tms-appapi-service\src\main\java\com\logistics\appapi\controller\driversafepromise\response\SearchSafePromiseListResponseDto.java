package com.logistics.appapi.controller.driversafepromise.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/11/18 15:05
 */
@Data
public class SearchSafePromiseListResponseDto {
    @ApiModelProperty("安全承诺书关联司机ID")
    private String relationId = "";
    @ApiModelProperty("所属年份")
    private String period = "";
    @ApiModelProperty("承诺书名称如,2019年驾驶员安全承诺书")
    private String periodLabel = "";
    @ApiModelProperty("签订状态: 0待签订、1已签订")
    private String status = "";
    @ApiModelProperty("签订状态文本")
    private String statusLabel = "";
    @ApiModelProperty("发布时间")
    private String publishTime = "";
    @ApiModelProperty("经办人")
    private String agent = "";
}
