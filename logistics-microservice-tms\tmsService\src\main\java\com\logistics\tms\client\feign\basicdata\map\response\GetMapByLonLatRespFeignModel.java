package com.logistics.tms.client.feign.basicdata.map.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetMapByLonLatRespFeignModel {

    @ApiModelProperty("省Id")
    private Long provinceId;
    @ApiModelProperty("省名称")
    private String provinceName;
    @ApiModelProperty("城市Id")
    private Long cityId;
    @ApiModelProperty("城市名称 (当城市是直辖市时城市名称展示为省名称如 上海市)")
    private String cityName;
    @ApiModelProperty("区Id (当城市为直辖县时该字段为空)")
    private Long areaId;
    @ApiModelProperty("区名称  (当城市为直辖县时该字段为空)")
    private String areaName;

    @ApiModelProperty("详细地址")
    private String addressDetail;


}
