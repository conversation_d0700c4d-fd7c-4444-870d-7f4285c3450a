package com.logistics.tms.biz.traycost;

import com.github.pagehelper.PageInfo;
import com.yelo.tools.context.BaseContextHandler;
import com.logistics.tms.api.feign.traycost.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.EntrustDataExceptionEnum;
import com.logistics.tms.base.enums.IfValidEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TTrayCost;
import com.logistics.tms.mapper.TTrayCostMapper;
import com.yelo.tools.redis.utils.RedisUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/20 19:21
 */
@Service
public class TrayCostBiz {

    @Autowired
    private TTrayCostMapper tTrayCostMapper;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private CommonBiz commonBiz;

    private static final String MODIFY_TRAY_COST_COUNT_KEY = "MODIFY_TRAY_COST_COUNT_KEY_";

    /**
     * 托盘费用列表
     * @return
     */
    public List<SearchTrayCostListResponseModel> searchTrayCostList() {
        return tTrayCostMapper.searchTrayCostList();
    }

    /**
     * 托盘费用详情
     * @param requestModel
     * @return
     */
    public TrayCostDetailResponseModel getDetail(TrayCostIdRequestModel requestModel) {
        TTrayCost tTrayCost = tTrayCostMapper.selectByPrimaryKey(requestModel.getTrayCostId());
        if (tTrayCost == null || tTrayCost.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(EntrustDataExceptionEnum.TRAY_COST_EMPTY);
        }
        TrayCostDetailResponseModel detail = new TrayCostDetailResponseModel();
        detail.setTrayCostId(tTrayCost.getId());
        detail.setEntrustType(tTrayCost.getEntrustType());
        detail.setGoodsUnit(tTrayCost.getGoodsUnit());
        detail.setUnitPrice(tTrayCost.getUnitPrice());
        Object object = redisUtils.get(MODIFY_TRAY_COST_COUNT_KEY + tTrayCost.getEntrustType() + tTrayCost.getGoodsUnit());
        if (object != null){
            detail.setModifyCount(ConverterUtils.toInteger(object));
        }else{
            detail.setModifyCount(CommonConstant.INTEGER_ZERO);
        }
        return detail;
    }

    /**
     * 修改费用
     * @param requestModel
     */
    @Transactional
    public void modifyPrice(ModifyPriceRequestModel requestModel) {
        TTrayCost tTrayCost = tTrayCostMapper.selectByPrimaryKey(requestModel.getTrayCostId());
        if (tTrayCost == null || tTrayCost.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(EntrustDataExceptionEnum.TRAY_COST_EMPTY);
        }
        Integer modifyCount = CommonConstant.INTEGER_ZERO;
        String modifyTrayCostCountKey = MODIFY_TRAY_COST_COUNT_KEY + tTrayCost.getEntrustType() + tTrayCost.getGoodsUnit();
        Object object = redisUtils.get(modifyTrayCostCountKey);
        if (object != null){
            modifyCount = ConverterUtils.toInteger(object);
        }
        if (modifyCount >= CommonConstant.INTEGER_FIVE){//每月只能修改5次
            throw new BizException(EntrustDataExceptionEnum.TRAY_COST_MODIFY_COUNT_MAX);
        }
        //将修改次数存入redis
        modifyCount++;
        redisUtils.set(modifyTrayCostCountKey, modifyCount, getNextMonthNoeDay());
        //修改当前有效数据的截止时间
        TTrayCost upTrayCost = new TTrayCost();
        upTrayCost.setId(tTrayCost.getId());
        upTrayCost.setEndTime(DateUtils.add(requestModel.getStartTime(), Calendar.SECOND, CommonConstant.NEGATIVE_INTEGER_ONE));//修改费用生效时间的前一秒时间作为当前有效数据的截止时间
        commonBiz.setBaseEntityModify(upTrayCost, BaseContextHandler.getUserName());
        tTrayCostMapper.updateByPrimaryKeySelective(upTrayCost);
        //查询该类型和单位大于当前时间的数据
        TTrayCost trayCostTop = tTrayCostMapper.getTopByEntrustTypeGoodsUnit(tTrayCost.getEntrustType(),tTrayCost.getGoodsUnit());
        if (trayCostTop == null){//不存在，新增
            TTrayCost addTrayCost = new TTrayCost();
            addTrayCost.setEntrustType(tTrayCost.getEntrustType());
            addTrayCost.setGoodsUnit(tTrayCost.getGoodsUnit());
            addTrayCost.setUnitPrice(requestModel.getUnitPrice());
            addTrayCost.setStartTime(requestModel.getStartTime());
            commonBiz.setBaseEntityAdd(addTrayCost, BaseContextHandler.getUserName());
            tTrayCostMapper.insertSelective(addTrayCost);
        }else{//存在，修改费用和时间
            upTrayCost = new TTrayCost();
            upTrayCost.setId(trayCostTop.getId());
            upTrayCost.setUnitPrice(requestModel.getUnitPrice());
            upTrayCost.setStartTime(requestModel.getStartTime());
            commonBiz.setBaseEntityModify(upTrayCost, BaseContextHandler.getUserName());
            tTrayCostMapper.updateByPrimaryKeySelective(upTrayCost);
        }
    }
    //获取下月1号0点0分0秒的时间长度
    public static Long getNextMonthNoeDay(){
        Calendar cl = Calendar.getInstance();
        cl.add(Calendar.MONTH,1);
        cl.set(Calendar.DAY_OF_MONTH, 1);
        cl.set(Calendar.HOUR_OF_DAY, 0);
        cl.set(Calendar.MINUTE, 0);
        cl.set(Calendar.SECOND, 0);
        cl.set(Calendar.MILLISECOND, 0);
        return (cl.getTimeInMillis() - System.currentTimeMillis()) / CommonConstant.INTEGER_ONE_THOUSAND_HUNDRED;
    }

    /**
     * 费用记录列表
     * @param requestModel
     * @return
     */
    public PageInfo<SearchCostRecordsListResponseModel> searchCostRecordsList(SearchCostRecordsListRequestModel requestModel) {
        TTrayCost tTrayCost = tTrayCostMapper.selectByPrimaryKey(requestModel.getTrayCostId());
        if (tTrayCost == null || tTrayCost.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(EntrustDataExceptionEnum.TRAY_COST_EMPTY);
        }
        requestModel.setEntrustType(tTrayCost.getEntrustType());
        requestModel.setGoodsUnit(tTrayCost.getGoodsUnit());
        requestModel.enablePaging();
        List<SearchCostRecordsListResponseModel> list = tTrayCostMapper.searchCostRecordsList(requestModel);
        return new PageInfo<>(list);
    }
}
