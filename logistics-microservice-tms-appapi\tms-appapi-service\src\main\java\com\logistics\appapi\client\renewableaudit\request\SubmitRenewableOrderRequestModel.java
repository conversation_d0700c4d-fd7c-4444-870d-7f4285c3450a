package com.logistics.appapi.client.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class SubmitRenewableOrderRequestModel {

	@ApiModelProperty(value = "乐橘新生订单审核表id",required = true)
	private Long renewableAuditId;

	@ApiModelProperty(value = "收货地址code（云仓仓库地址code）", required = true)
	private String unloadAddressCode;

	@ApiModelProperty(value = "收货省id",required = true)
	private Long unloadProvinceId;

	@ApiModelProperty(value = "收货省",required = true)
	private String unloadProvinceName;

	@ApiModelProperty(value = "收货市id",required = true)
	private Long unloadCityId;

	@ApiModelProperty(value = "收货市",required = true)
	private String unloadCityName;

	@ApiModelProperty(value = "收货区id",required = true)
	private Long unloadAreaId;

	@ApiModelProperty(value = "收货区",required = true)
	private String unloadAreaName;

	@ApiModelProperty("收货地址详细")
	private String unloadDetailAddress;

	@ApiModelProperty(value = "收货仓库",required = true)
	private String unloadWarehouse;

	@ApiModelProperty(value = "收货人",required = true)
	private String receiverName;

	@ApiModelProperty(value = "收货人手机号",required = true)
	private String receiverMobile;

	@ApiModelProperty("卸货经度")
	private String unloadLongitude;

	@ApiModelProperty("卸货纬度")
	private String unloadLatitude;
}
