<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCompanyAccountMapper">
    <sql id="Base_Column_List_Decrypt" >
        id, AES_DECRYPT(UNHEX(bank_account),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as bank_account, bank_account_name, bra_bank_name, bank_code, remark, enabled, created_by,
        created_time, last_modified_by, last_modified_time, valid
    </sql>

    <select id="searchList" resultType="com.logistics.tms.controller.companyaccount.response.SearchCompanyAccountResponseModel">
        select
        id                                                                                                                as companyAccountId,
        enabled,
        AES_DECRYPT(UNHEX(bank_account), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as bankAccount,
        bank_code                                                                                                         as bankCode,
        bank_account_name                                                                                                 as bankAccountName,
        bra_bank_name                                                                                                     as braBankName,
        remark,
        created_by                                                                                                        as createdBy,
        last_modified_by                                                                                                  as operateBy,
        last_modified_time                                                                                                as operateDateTime
        from t_company_account tca
        where valid = 1
        <if test="enabled != null">
            and tca.enabled = #{enabled,jdbcType=INTEGER}
        </if>
        <if test="bankAccount != null and bankAccount != ''">
            and instr(AES_DECRYPT(UNHEX(bank_account), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'), #{bankAccount,jdbcType=VARCHAR})
        </if>
        <if test="createdStartTime != '' and createdStartTime != null">
            and tca.created_time &gt;= DATE_FORMAT(#{createdStartTime,jdbcType=VARCHAR}, '%Y-%m-%d %k:%i:%S')
        </if>
        <if test="createdEndTime != '' and createdEndTime != null">
            and tca.created_time &lt;= DATE_FORMAT(#{createdEndTime,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="operateStartTime != '' and operateStartTime != null">
            and tca.last_modified_time &gt;= DATE_FORMAT(#{operateStartTime,jdbcType=VARCHAR}, '%Y-%m-%d %k:%i:%S')
        </if>
        <if test="operateEndTime != '' and operateEndTime != null">
            and tca.last_modified_time &lt;= DATE_FORMAT(#{operateEndTime,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        order by last_modified_time desc, id desc
    </select>

    <select id="selectByBankAccount" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_company_account
        where valid = 1
        and bank_account = HEX(AES_ENCRYPT(#{bankAccount,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'))
    </select>

    <insert id="insertSelectiveEncrypt" useGeneratedKeys="true" keyProperty="id">
        insert into t_company_account
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="bankAccount != null" >
                bank_account,
            </if>
            <if test="bankAccountName != null" >
                bank_account_name,
            </if>
            <if test="braBankName != null" >
                bra_bank_name,
            </if>
            <if test="bankCode != null" >
                bank_code,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="enabled != null" >
                enabled,
            </if>
            <if test="createdBy != null" >
                created_by,
            </if>
            <if test="createdTime != null" >
                created_time,
            </if>
            <if test="lastModifiedBy != null" >
                last_modified_by,
            </if>
            <if test="lastModifiedTime != null" >
                last_modified_time,
            </if>
            <if test="valid != null" >
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="bankAccount != null" >
                HEX(AES_ENCRYPT(#{bankAccount,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')) ,
            </if>
            <if test="bankAccountName != null" >
                #{bankAccountName,jdbcType=VARCHAR},
            </if>
            <if test="braBankName != null" >
                #{braBankName,jdbcType=VARCHAR},
            </if>
            <if test="bankCode != null" >
                #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="enabled != null" >
                #{enabled,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null" >
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null" >
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null" >
                #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null" >
                #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null" >
                #{valid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
</mapper>