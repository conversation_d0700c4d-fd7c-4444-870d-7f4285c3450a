package com.logistics.tms.biz.carrierfreight;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.carrierfreight.model.*;
import com.logistics.tms.controller.companycarrier.request.FuzzySearchCompanyCarrierRequestModel;
import com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.EnabledEnum;
import com.logistics.tms.base.enums.EntrustDataExceptionEnum;
import com.logistics.tms.base.enums.IfValidEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TCarrierFreight;
import com.logistics.tms.entity.TCompanyCarrier;
import com.logistics.tms.mapper.TCarrierFreightMapper;
import com.logistics.tms.mapper.TCompanyCarrierMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.MapUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 车主运价service
 *
 * <AUTHOR>
 * @date 2022/9/2 15:01
 */
@Service
public class CarrierFreightBiz {

    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private TCarrierFreightMapper tCarrierFreightMapper;

    /**
     * 新增车主运价
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public Boolean addCarrierFreight(CarrierFreightAddRequestModel requestModel) {
        // 校验车主
        TCompanyCarrier companyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(requestModel.getCompanyCarrierId());
        if (companyCarrier == null || companyCarrier.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }
        // 校验是否已存在该车主运价
        TCarrierFreight existCarrierFreight = tCarrierFreightMapper.getFreightByCarrierCompanyId(companyCarrier.getId());
        if (existCarrierFreight != null) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_FREIGHT_ALREADY_EXIST);
        }
        // 保存车主运价
        TCarrierFreight carrierFreight = new TCarrierFreight();
        carrierFreight.setCompanyCarrierId(companyCarrier.getId());
        carrierFreight.setEnabled(EnabledEnum.DISABLED.getKey());
        commonBiz.setBaseEntityAdd(carrierFreight, BaseContextHandler.getUserName());
        tCarrierFreightMapper.insertSelective(carrierFreight);
        return true;
    }

    /**
     * 车主运价禁用启用
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public Boolean enable(CarrierFreightEnableRequestModel requestModel) {
        // 校验车主运价
        TCarrierFreight carrierFreight = tCarrierFreightMapper.selectByPrimaryKey(requestModel.getCarrierFreightId());
        if (carrierFreight == null || carrierFreight.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_FREIGHT_NOT_EXIST);
        }
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getEnabled()) && CommonConstant.INTEGER_ONE.equals(carrierFreight.getEnabled())) {
            throw new BizException(EntrustDataExceptionEnum.FREIGHT_ALREADY_ENABLED);
        }
        if (CommonConstant.INTEGER_ZERO.equals(requestModel.getEnabled()) && CommonConstant.INTEGER_ZERO.equals(carrierFreight.getEnabled())) {
            throw new BizException(EntrustDataExceptionEnum.FREIGHT_ALREADY_DISABLED);
        }
        TCarrierFreight enableCarrierFreight = new TCarrierFreight();
        enableCarrierFreight.setId(carrierFreight.getId());
        enableCarrierFreight.setEnabled(requestModel.getEnabled());
        commonBiz.setBaseEntityModify(enableCarrierFreight, BaseContextHandler.getUserName());
        tCarrierFreightMapper.updateByPrimaryKeySelective(enableCarrierFreight);

        return true;
    }

    /**
     * 查询车主运价列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<SearchCarrierFreightResponseModel> searchList(SearchCarrierFreightRequestModel requestModel) {
        List<TCarrierFreight> carrierFreights;
        Map<Long, FuzzySearchCompanyCarrierResponseModel> companyCarrierMap;

        // 公司名称或个人姓名手机号为空时，直接查询车主运价表。不为空时，先去查车主表，在查车主运价表
        if (StringUtils.isBlank(requestModel.getCompanyCarrierName())) {
            requestModel.enablePaging();
            carrierFreights = tCarrierFreightMapper.selectFreightList(new TCarrierFreight());
            if (ListUtils.isEmpty(carrierFreights)) {
                return new PageInfo<>(new ArrayList<>());
            }
            List<Long> companyCarrierIds = carrierFreights.stream().map(TCarrierFreight::getCompanyCarrierId)
                    .collect(Collectors.toList());
            companyCarrierMap = tCompanyCarrierMapper.selectCompanyCarrierInfoByIds(companyCarrierIds)
                    .stream().collect(Collectors.toMap(FuzzySearchCompanyCarrierResponseModel::getCompanyId, Function.identity(), (o1, o2) -> o2));
        } else {
            // 先根据公司名称、司机姓名、手机号模糊查询车主信息
            FuzzySearchCompanyCarrierRequestModel fuzzyRequestModel = new FuzzySearchCompanyCarrierRequestModel();
            fuzzyRequestModel.setCompanyName(requestModel.getCompanyCarrierName());
            companyCarrierMap = tCompanyCarrierMapper.fuzzyQueryCompanyCarrierInfo(fuzzyRequestModel)
                    .stream().collect(Collectors.toMap(FuzzySearchCompanyCarrierResponseModel::getCompanyId, Function.identity(), (o1, o2) -> o2));
            if (MapUtils.isEmpty(companyCarrierMap)) {
                return new PageInfo<>(new ArrayList<>());
            }
            // 分页查询车主价格列表
            Set<Long> companyCarrierIds = companyCarrierMap.keySet();
            requestModel.enablePaging();
            carrierFreights = tCarrierFreightMapper.selectFreightByCarrierCompanyIds(new ArrayList<>(companyCarrierIds));
        }

        // 封装车主价格列表
        List<SearchCarrierFreightResponseModel> carrierFreightList = new ArrayList<>();
        SearchCarrierFreightResponseModel responseModel;
        for (TCarrierFreight carrierFreight : carrierFreights) {
            responseModel = new SearchCarrierFreightResponseModel();
            responseModel.setCarrierFreightId(carrierFreight.getId());
            responseModel.setEnabled(carrierFreight.getEnabled());
            if (companyCarrierMap.containsKey(carrierFreight.getCompanyCarrierId())) {
                FuzzySearchCompanyCarrierResponseModel companyCarrier = companyCarrierMap.get(carrierFreight.getCompanyCarrierId());
                responseModel.setCompanyCarrierType(companyCarrier.getCompanyType());
                responseModel.setCarrierContactName(companyCarrier.getContactName());
                responseModel.setCarrierContactPhone(companyCarrier.getContactPhone());
                responseModel.setCompanyCarrierName(companyCarrier.getCompanyName());
            }
            carrierFreightList.add(responseModel);
        }

        PageInfo pageInfo = new PageInfo(carrierFreights);
        pageInfo.setList(carrierFreightList);
        return pageInfo;
    }

    public TCarrierFreight getCarrierFreightByScheme(Long schemeId) {
        TCarrierFreight tCarrierFreight = tCarrierFreightMapper.selectCarrierFreightBySchemeId(schemeId);
        if (tCarrierFreight == null || IfValidEnum.INVALID.getKey().equals(tCarrierFreight.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_FREIGHT_NOT_EXIST);
        }
        return tCarrierFreight;
    }

    /**
     * 校验车主运价表是否存在
     * @param carrierFreightId 主键id
     * @return 车主运价
     */
    public TCarrierFreight checkCarrierFreightExist(Long carrierFreightId){
        TCarrierFreight tCarrierFreight = tCarrierFreightMapper.selectByPrimaryKey(carrierFreightId);
        if(Objects.isNull(tCarrierFreight)){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_FREIGHT_NOT_EXIST);
        }
        return tCarrierFreight;
    }
}
