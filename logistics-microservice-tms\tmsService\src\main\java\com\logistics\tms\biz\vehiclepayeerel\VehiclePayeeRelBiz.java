package com.logistics.tms.biz.vehiclepayeerel;

import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tray.core.exception.BizException;
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel;
import com.logistics.tms.api.feign.vehiclepayeerel.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.AuditStatusEnum;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.VehicleCategoryEnum;
import com.logistics.tms.base.enums.VehicleOutageStatus;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TDriverPayee;
import com.logistics.tms.entity.TVehicleDrivingLicense;
import com.logistics.tms.entity.TVehiclePayeeRel;
import com.logistics.tms.mapper.TDriverPayeeMapper;
import com.logistics.tms.mapper.TVehicleBasicMapper;
import com.logistics.tms.mapper.TVehicleDrivingLicenseMapper;
import com.logistics.tms.mapper.TVehiclePayeeRelMapper;
import com.yelo.tools.utils.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/7/11 14:30
 */
@Service
public class VehiclePayeeRelBiz {

    @Autowired
    private TVehiclePayeeRelMapper vehiclePayeeRelMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TVehicleBasicMapper vehicleBasicMapper;
    @Autowired
    private TDriverPayeeMapper driverPayeeMapper;
    @Autowired
    private TVehicleDrivingLicenseMapper vehicleDrivingLicenseMapper;

    /**
     * 车辆收款账户关联关系列表
     * @param requestModel
     * @return
     */
    public List<SearchVehiclePayeeRelListResponseModel> searchVehiclePayeeRelList(SearchVehiclePayeeRelListRequestModel requestModel) {
        List<SearchVehiclePayeeRelListResponseModel> list = vehiclePayeeRelMapper.searchVehiclePayeeRelList(requestModel);
        if (ListUtils.isEmpty(list)){
            list = new ArrayList<>();
        }
        return list;
    }

    /**
     * 车辆收款账户关联关系详情
     * @param requestModel
     * @return
     */
    public VehiclePayeeRelDetailResponseModel getVehiclePayeeRelDetail(VehiclePayeeRelIdRequestModel requestModel) {
        return vehiclePayeeRelMapper.getVehiclePayeeRelDetail(requestModel.getVehiclePayeeRelId());
    }

    /**
     * 新增/修改车辆收款账户关联关系
     * @param requestModel
     */
    @Transactional
    public void addOrModifyVehiclePayeeRel(AddOrModifyVehiclePayeeRelRequestModel requestModel) {
        VehicleBasicPropertyModel tqVehicleBasic =  vehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getVehicleId());
        if (tqVehicleBasic == null){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_BASIC_IS_EMPTY);
        }
        if(!VehicleCategoryEnum.TRACTOR.getKey().equals(tqVehicleBasic.getVehicleCategory())
                && !VehicleCategoryEnum.WHOLE.getKey().equals(tqVehicleBasic.getVehicleCategory())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_CATEGORY_ERROR);
        }
        if(!VehicleOutageStatus.IN_OPERATION.getKey().equals(tqVehicleBasic.getOperatingState())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLES_ARE_OUT_OF_SERVICE);
        }

        TDriverPayee tqDriverPayee = driverPayeeMapper.selectByPrimaryKey(requestModel.getDriverPayeeId());
        if (tqDriverPayee == null){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_PAYEE_EMPTY);
        }
        TVehiclePayeeRel vehiclePayeeRel = new TVehiclePayeeRel();
        vehiclePayeeRel.setVehicleId(requestModel.getVehicleId());
        vehiclePayeeRel.setDriverPayeeId(requestModel.getDriverPayeeId());
        vehiclePayeeRel.setRemark(requestModel.getRemark());

        TVehiclePayeeRel tqVehiclePayeeRel = vehiclePayeeRelMapper.getByVehicleId(requestModel.getVehicleId());
        if (requestModel.getVehiclePayeeRelId() != null && requestModel.getVehiclePayeeRelId() > CommonConstant.LONG_ZERO){
            if (tqVehiclePayeeRel != null && !tqVehiclePayeeRel.getId().equals(requestModel.getVehiclePayeeRelId())){
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_PAYEE_REL_EXIST);
            }
            vehiclePayeeRel.setId(requestModel.getVehiclePayeeRelId());
            commonBiz.setBaseEntityModify(vehiclePayeeRel,BaseContextHandler.getUserName());
            vehiclePayeeRelMapper.updateByPrimaryKeySelective(vehiclePayeeRel);
        }else{
            if (tqVehiclePayeeRel != null){
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_PAYEE_REL_EXIST);
            }
            commonBiz.setBaseEntityAdd(vehiclePayeeRel,BaseContextHandler.getUserName());
            vehiclePayeeRelMapper.insertSelective(vehiclePayeeRel);
        }
    }

    /**
     * 删除车辆收款账户关联关系
     * @param requestModel
     */
    @Transactional
    public void delVehiclePayeeRel(VehiclePayeeRelIdsRequestModel requestModel) {
        List<TVehiclePayeeRel> list = vehiclePayeeRelMapper.getByIds(requestModel.getVehiclePayeeRelIds());
        if (ListUtils.isEmpty(list)){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_PAYEE_REL_EMPTY);
        }
        String userName = BaseContextHandler.getUserName();
        TVehiclePayeeRel vehiclePayeeRel;
        List<TVehiclePayeeRel> upList = new ArrayList<>();
        for (TVehiclePayeeRel rel:list) {
            vehiclePayeeRel = new TVehiclePayeeRel();
            vehiclePayeeRel.setId(rel.getId());
            vehiclePayeeRel.setValid(CommonConstant.INTEGER_ZERO);
            commonBiz.setBaseEntityModify(vehiclePayeeRel,userName);
            upList.add(vehiclePayeeRel);
        }
        if (ListUtils.isNotEmpty(upList)){
            vehiclePayeeRelMapper.batchUpdate(upList);
        }
    }

    /**
     * 导入车辆收款账户关联关系
     * @param requestModel
     * @return
     */
    @Transactional
    public ImportVehiclePayeeRelResponseModel importVehiclePayeeRel(ImportVehiclePayeeRelRequestModel requestModel) {
        ImportVehiclePayeeRelResponseModel responseModel = new ImportVehiclePayeeRelResponseModel();
        Integer numberFailures = requestModel.getNumberFailures();
        Integer numberSuccessful = 0;
        String userName = BaseContextHandler.getUserName();
        TVehicleDrivingLicense tqVehicleDrivingLicense;
        TDriverPayee tqDriverPayee;
        TVehiclePayeeRel vehiclePayeeRel;
        List<TVehiclePayeeRel> addList = new ArrayList<>();
        List<TVehiclePayeeRel> upList = new ArrayList<>();
        for (ImportVehiclePayeeRelListRequestModel model:requestModel.getRelList()) {
            tqVehicleDrivingLicense = vehicleDrivingLicenseMapper.getByVehicleNo(model.getVehicleNo());
            if (tqVehicleDrivingLicense == null){
                numberFailures++;
                continue;
            }
            VehicleBasicPropertyModel tqVehicleBasic =  vehicleBasicMapper.getVehicleBasicPropertyById(tqVehicleDrivingLicense.getVehicleId());
            if (tqVehicleBasic == null){
                numberFailures++;
                continue;
            }
            if(!VehicleCategoryEnum.TRACTOR.getKey().equals(tqVehicleBasic.getVehicleCategory())
                    && !VehicleCategoryEnum.WHOLE.getKey().equals(tqVehicleBasic.getVehicleCategory())){
                numberFailures++;
                continue;
            }
            if(!VehicleOutageStatus.IN_OPERATION.getKey().equals(tqVehicleBasic.getOperatingState())){
                numberFailures++;
                continue;
            }

            tqDriverPayee = driverPayeeMapper.getByNameMobileIdentity(model.getName(),model.getBankCardNo(),model.getIdentityNo(),model.getBankName());
            if (tqDriverPayee == null|| !AuditStatusEnum.AUDIT_THROUGH.getKey().equals(tqDriverPayee.getAuditStatus())){
                numberFailures++;
                continue;
            }
            TVehiclePayeeRel tqVehiclePayeeRel = vehiclePayeeRelMapper.getByVehicleId(tqVehicleDrivingLicense.getVehicleId());
            vehiclePayeeRel = new TVehiclePayeeRel();
            if(tqVehiclePayeeRel!=null){
                vehiclePayeeRel.setId(tqVehiclePayeeRel.getId());
                vehiclePayeeRel.setDriverPayeeId(tqDriverPayee.getId());
                vehiclePayeeRel.setRemark(model.getRemark());
                commonBiz.setBaseEntityModify(vehiclePayeeRel,userName);
                upList.add(vehiclePayeeRel);
            }else{
                vehiclePayeeRel = new TVehiclePayeeRel();
                vehiclePayeeRel.setVehicleId(tqVehicleDrivingLicense.getVehicleId());
                vehiclePayeeRel.setDriverPayeeId(tqDriverPayee.getId());
                vehiclePayeeRel.setRemark(model.getRemark());
                commonBiz.setBaseEntityAdd(vehiclePayeeRel,userName);
                addList.add(vehiclePayeeRel);
            }
            numberSuccessful++;
        }
        if (ListUtils.isNotEmpty(addList)){
            vehiclePayeeRelMapper.batchInsert(addList);
        }
        if(ListUtils.isNotEmpty(upList)){
            vehiclePayeeRelMapper.batchUpdate(upList);
        }
        responseModel.setNumberSuccessful(numberSuccessful);
        responseModel.setNumberFailures(numberFailures);
        return responseModel;
    }
}
