package com.logistics.tms.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2021/9/17 17:23
 */
@Data
public class CarrierOrderCorrectConfirmRequestModel {

    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    @ApiModelProperty("实际实提数量")
    private BigDecimal loadAmount;

    @ApiModelProperty("纠错原因类型: 1 系统数量错误，2  实物损耗，3 提错托盘  4 仓库入错")
    private Integer correctType;

    @ApiModelProperty(value = "车主运费类型：1 单价，2 一口价")
    private Integer carrierFreightType;

    @ApiModelProperty(value = "车主运费(元)")
    private BigDecimal carrierFreight;

    @ApiModelProperty("提错托盘数量")
    private BigDecimal loadErrorAmount;

    @ApiModelProperty("遗失托盘数量")
    private BigDecimal loseErrorAmount;

    @ApiModelProperty("运单回单路径")
    private List<String> signTicketsList;

}
