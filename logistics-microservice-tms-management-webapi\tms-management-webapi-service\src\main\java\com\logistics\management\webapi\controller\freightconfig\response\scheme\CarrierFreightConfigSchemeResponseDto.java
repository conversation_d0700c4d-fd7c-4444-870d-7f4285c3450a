package com.logistics.management.webapi.controller.freightconfig.response.scheme;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierFreightConfigSchemeResponseDto {

    @ApiModelProperty(value = "运价配置方案ID")
    private String freightConfigSchemeId = "";

    @ApiModelProperty(value = "方案类型; 100: 路线配置; 200:同区-跨区价格; 201: 同区价格; 202: 跨区价格; 301:系统计算预计距离; 302:系统配置距离")
    private String schemeType = "";
}
