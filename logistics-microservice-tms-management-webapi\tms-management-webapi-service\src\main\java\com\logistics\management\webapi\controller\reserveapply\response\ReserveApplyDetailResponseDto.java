package com.logistics.management.webapi.controller.reserveapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Data
public class ReserveApplyDetailResponseDto {

	@ApiModelProperty("申请记录id")
	private String applyId = "";

	@ApiModelProperty("申请单号")
	private String applyCode = "";

	@ApiModelProperty("状态：-1 已撤销，0 待业务审核，1 待财务审核，2 已驳回，3 待打款，4 已打款")
	private String status = "";

	@ApiModelProperty("状态展示文本")
	private String statusLabel = "";

	@ApiModelProperty("收款账号")
	private String receiveAccount = "";

	@ApiModelProperty("司机")
	private String driver = "";

	@ApiModelProperty("申请金额")
	private String applyAmount = "";

	@ApiModelProperty("余额")
	private String balanceAmount = "";

	@ApiModelProperty("待核销金额")
	private String awaitVerificationAmount = "";

	@ApiModelProperty("备注")
	private String remark = "";

	@ApiModelProperty("业务审核人")
	private String businessAuditor = "";

	@ApiModelProperty("业务审核时间")
	private String businessAuditTime = "";

	@ApiModelProperty("业务批准金额")
	private String approveAmount = "";

	@ApiModelProperty("业务审核备注")
	private String businessRemark = "";

	@ApiModelProperty("财务审核人")
	private String financeAuditor = "";

	@ApiModelProperty("财务审核时间")
	private String financeAuditTime = "";
	@ApiModelProperty("财务审核备注")
	private String financeRemark = "";

	@ApiModelProperty(value = "打款编号 ")
	private String remitCode;

	@ApiModelProperty(value = "打款日期")
	private String remitDate;

	@ApiModelProperty(value = "打款日期")
	private String remitOperateTime;

	@ApiModelProperty(value = "打款凭据")
	private List<String> remitTickets;

	@ApiModelProperty(value = "打款备注")
	private String remitRemark;
}
