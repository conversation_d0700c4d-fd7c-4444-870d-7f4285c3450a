package com.logistics.tms.biz.biddingorder.bo;

import lombok.Data;

import java.util.List;

@Data
public class SearchBiddingOrderListByCustomerReqBo {

    /**
     *车主id
     */
    private Long companyCarrierId;

    /**
     *车主是否加入黑名单：0 否，1 是
     */
    private Integer companyCarrierIfAddBlacklist;

    /**
     *竞价单ids 地址过滤竞价单
     */
    private List<Long> biddingOrderIdsByAddress;

    /**
     * 竞价状态
     */
    private Integer biddingStatus;

    /**
     * 装卸方式 1一装一卸、2多装一卸
     */
    private Integer handlingMode;

    /**
     * 车长
     */
    private Long vehicleLength;
}
