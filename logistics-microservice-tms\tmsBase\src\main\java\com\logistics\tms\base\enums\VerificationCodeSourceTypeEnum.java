package com.logistics.tms.base.enums;

/**
 * <AUTHOR>
 * @createDate 2018-08-01 19:35
 */
public enum VerificationCodeSourceTypeEnum {
    WEB(1,"运营平台"),
    CUSTOMER_WEB(2,"客户前台"),
    APP(3,"客户app"),
    CARRIER_WEB(4,"承运商网站"),
    DRIVER_APP(5,"司机APP"),
    QIYA_WEIXIN(6,"云途微信"),
    TMS_DRIVER_APPLET(7,"tms司机小程序"),
    TMS_CUSTOMER_WEB(8, "tms客户前台"),
    ;
    private Integer key;
    private String value;

    VerificationCodeSourceTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
