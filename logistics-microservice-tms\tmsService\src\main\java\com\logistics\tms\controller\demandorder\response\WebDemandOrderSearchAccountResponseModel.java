package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2018/9/26 17:56
 */
@Data
public class WebDemandOrderSearchAccountResponseModel {

    @ApiModelProperty("全部")
    private Integer allAccount;

    @ApiModelProperty("待调度")
    private Integer waitAccount;

    @ApiModelProperty("部分调度")
    private Integer partAccount;

    @ApiModelProperty("完成调度")
    private Integer completeAccount;

    @ApiModelProperty("已取消")
    private Integer cancelAccount;
}
