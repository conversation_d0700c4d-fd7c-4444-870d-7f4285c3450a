package com.logistics.tms.biz.carriervehiclerel.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierVehicleRelAndInfoModel {
    /**
     * 承运商id
     */
    @ApiModelProperty("承运商车辆关联关系id")
    private Long carrierVehicleRelId;

    /**
     * 车辆id
     */
    @ApiModelProperty("车辆id")
    private Long vehicleId;

    /**
     * 承运商id
     */
    @ApiModelProperty("承运商id")
    private Long companyCarrierId;

    /**
     * 装载量（可装载托盘数）
     */
    @ApiModelProperty("装载量（可装载托盘数）")
    private Integer loadingCapacity;

    /**
     * 车辆机构：1 自主，2 外部，3 自营
     */
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;
}
