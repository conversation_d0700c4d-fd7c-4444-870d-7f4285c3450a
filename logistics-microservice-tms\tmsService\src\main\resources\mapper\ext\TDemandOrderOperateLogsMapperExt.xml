<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandOrderOperateLogsMapper" >
    <insert id="batchInsertSelective" parameterType="com.logistics.tms.entity.TDemandOrderOperateLogs" >
        <foreach collection="list" item="item" separator=";">
            insert into t_demand_order_operate_logs
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    id,
                </if>
                <if test="item.demandOrderId != null" >
                    demand_order_id,
                </if>
                <if test="item.operationType != null" >
                    operation_type,
                </if>
                <if test="item.operationContent != null" >
                    operation_content,
                </if>
                <if test="item.remark != null" >
                    remark,
                </if>
                <if test="item.operatorName != null" >
                    operator_name,
                </if>
                <if test="item.operateTime != null" >
                    operate_time,
                </if>
                <if test="item.createdBy != null" >
                    created_by,
                </if>
                <if test="item.createdTime != null" >
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="item.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.demandOrderId != null" >
                    #{item.demandOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.operationType != null" >
                    #{item.operationType,jdbcType=INTEGER},
                </if>
                <if test="item.operationContent != null" >
                    #{item.operationContent,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null" >
                    #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.operatorName != null" >
                    #{item.operatorName,jdbcType=VARCHAR},
                </if>
                <if test="item.operateTime != null" >
                    #{item.operateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createdBy != null" >
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <resultMap id="Logs_Map" type="com.logistics.tms.controller.demandorder.response.GetDemandOrderLogsResponseModel" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT" />
        <result column="operation_type" property="operationType" jdbcType="INTEGER" />
        <result column="operation_content" property="operationContent" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="operate_time" property="operateTime" jdbcType="TIMESTAMP" />
        <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
        <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
        <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
        <result column="valid" property="valid" jdbcType="INTEGER" />
    </resultMap>
    <select id="getDemandOrderLogs" resultMap="Logs_Map">
        select
        <include refid="Base_Column_List"/>
        from t_demand_order_operate_logs
        where valid = 1
        and demand_order_id = #{demandOrderId,jdbcType=BIGINT}
        order by operate_time desc,id desc
    </select>

    <select id="getInvalidByDemandOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_demand_order_operate_logs
        where demand_order_id = #{demandOrderId,jdbcType=BIGINT}
    </select>
</mapper>