package com.logistics.management.webapi.client.oilfilled.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ImportOilFilledCardListRequestModel {

    @ApiModelProperty(value = "油卡卡号")
    private String cardNumber;
    @ApiModelProperty(value = "副卡卡号")
    private String subCardNumber;
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;
    @ApiModelProperty(value = "司机")
    private String driverName;
    @ApiModelProperty(value = "司机手机号")
    private String driverPhone;
    @ApiModelProperty(value = "充值金额")
    private BigDecimal applyAmount;
    @ApiModelProperty(value = "充值时间")
    private Date oilFilledDate;
    @ApiModelProperty(value = "备注")
    private String remark;

}
