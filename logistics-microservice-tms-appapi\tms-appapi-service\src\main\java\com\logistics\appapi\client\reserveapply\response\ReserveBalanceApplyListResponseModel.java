package com.logistics.appapi.client.reserveapply.response;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ReserveBalanceApplyListResponseModel {

    @ApiModelProperty("申请次数")
    private Integer applyCount;

    @ApiModelProperty("批准次数")
    private Integer approveCount;

    @ApiModelProperty("批准金额")
    private BigDecimal approveAmount;

    @ApiModelProperty("申请记录")
    private PageInfo<ReserveBalanceApplyListItemModel> applyPageInfo;
}
