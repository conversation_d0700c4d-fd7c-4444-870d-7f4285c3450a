package com.logistics.management.webapi.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/10/14 9:35
 */
@Data
public class GetOilFilledByVehicleIdResponseDto {
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty(value = "充油金额")
    private String oilFilledFee="";
    @ApiModelProperty(value = "充值时间")
    private String oilFilledDate="";
    @ApiModelProperty(value = "充油方式")
    private String oilFilledType="";
    @ApiModelProperty(value = "升数")
    private String liter="";
    @ApiModelProperty(value = "充值积分")
    private String topUpIntegral="";
    @ApiModelProperty(value = "奖励积分")
    private String rewardIntegral="";
}
