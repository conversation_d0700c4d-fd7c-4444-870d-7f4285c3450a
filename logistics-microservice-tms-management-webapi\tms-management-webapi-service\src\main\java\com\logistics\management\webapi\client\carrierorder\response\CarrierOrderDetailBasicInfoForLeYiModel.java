package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CarrierOrderDetailBasicInfoForLeYiModel {

    //地址信息
    //提货信息
    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    @ApiModelProperty("提货人")
    private String consignorName;
    @ApiModelProperty("提货人")
    private String consignorMobile;
    //提货定位信息
    @ApiModelProperty("提货定位位置")
    private String loadLocation;
    //卸货信息
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;
    @ApiModelProperty("卸货人")
    private String receiverName;
    @ApiModelProperty("卸货人")
    private String receiverMobile;
    //卸货定位信息
    @ApiModelProperty("卸货定位位置")
    private String unloadLocation;

    @ApiModelProperty("实际提货时间")
    private Date loadTime;
    @ApiModelProperty("实际卸货时间")
    private Date unloadTime;
    @ApiModelProperty("卸货地址是否后补")
    private Integer unloadAddressIsAmend;


}
