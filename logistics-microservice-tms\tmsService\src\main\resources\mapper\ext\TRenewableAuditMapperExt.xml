<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRenewableAuditMapper">
    <sql id="Base_Column_List_Decrypt">
        id, demand_order_id, demand_order_code, renewable_order_code, status, business_type,
    source, goods_amount_total, verified_goods_amount_total, vehicle_id, vehicle_no,
    staff_id, staff_property, staff_name,
    AES_DECRYPT(UNHEX(staff_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as staff_mobile,
    publish_time, customer_name, customer_user_name,
    AES_DECRYPT(UNHEX(customer_user_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as customer_user_mobile,
    publish_user_name, AES_DECRYPT(UNHEX(publish_user_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as publish_user_mobile,
    audit_time, auditor_name, remark, created_by, created_time, last_modified_by, last_modified_time, valid
    </sql>

    <select id="queryStatus" resultType="java.lang.Integer">
        select status
        from t_renewable_audit
        <where>
            valid =1 and id in (${renewableOrderIds})
        </where>
    </select>

    <select id="selectByPrimaryKeyDecrypt" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt" />
        from t_renewable_audit
        where id = #{id,jdbcType=BIGINT}
        and valid = 1
    </select>

    <insert id="insertSelectiveEncrypt" parameterType="com.logistics.tms.entity.TRenewableAudit" keyProperty="id" useGeneratedKeys="true">
        insert into t_renewable_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="demandOrderId != null">
                demand_order_id,
            </if>
            <if test="demandOrderCode != null">
                demand_order_code,
            </if>
            <if test="renewableOrderCode != null">
                renewable_order_code,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="source != null">
                source,
            </if>
            <if test="goodsAmountTotal != null">
                goods_amount_total,
            </if>
            <if test="verifiedGoodsAmountTotal != null">
                verified_goods_amount_total,
            </if>
            <if test="vehicleId != null">
                vehicle_id,
            </if>
            <if test="vehicleNo != null">
                vehicle_no,
            </if>
            <if test="staffId != null">
                staff_id,
            </if>
            <if test="staffProperty != null">
                staff_property,
            </if>
            <if test="staffName != null">
                staff_name,
            </if>
            <if test="staffMobile != null">
                staff_mobile,
            </if>
            <if test="publishTime != null">
                publish_time,
            </if>
            <if test="customerName != null">
                customer_name,
            </if>
            <if test="customerUserName != null">
                customer_user_name,
            </if>
            <if test="customerUserMobile != null">
                customer_user_mobile,
            </if>
            <if test="publishUserName != null">
                publish_user_name,
            </if>
            <if test="publishUserMobile != null">
                publish_user_mobile,
            </if>
            <if test="auditTime != null">
                audit_time,
            </if>
            <if test="auditorName != null">
                auditor_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by,
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time,
            </if>
            <if test="valid != null">
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="demandOrderId != null">
                #{demandOrderId,jdbcType=BIGINT},
            </if>
            <if test="demandOrderCode != null">
                #{demandOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="renewableOrderCode != null">
                #{renewableOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                #{source,jdbcType=INTEGER},
            </if>
            <if test="goodsAmountTotal != null">
                #{goodsAmountTotal,jdbcType=DECIMAL},
            </if>
            <if test="verifiedGoodsAmountTotal != null">
                #{verifiedGoodsAmountTotal,jdbcType=DECIMAL},
            </if>
            <if test="vehicleId != null">
                #{vehicleId,jdbcType=BIGINT},
            </if>
            <if test="vehicleNo != null">
                #{vehicleNo,jdbcType=VARCHAR},
            </if>
            <if test="staffId != null">
                #{staffId,jdbcType=BIGINT},
            </if>
            <if test="staffProperty != null">
                #{staffProperty,jdbcType=INTEGER},
            </if>
            <if test="staffName != null">
                #{staffName,jdbcType=VARCHAR},
            </if>
            <if test="staffMobile != null">
                HEX(AES_ENCRYPT(#{staffMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="publishTime != null">
                #{publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="customerName != null">
                #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="customerUserName != null">
                #{customerUserName,jdbcType=VARCHAR},
            </if>
            <if test="customerUserMobile != null">
                HEX(AES_ENCRYPT(#{customerUserMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="publishUserName != null">
                #{publishUserName,jdbcType=VARCHAR},
            </if>
            <if test="publishUserMobile != null">
                HEX(AES_ENCRYPT(#{publishUserMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="auditTime != null">
                #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditorName != null">
                #{auditorName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelectiveEncrypt" parameterType="com.logistics.tms.entity.TRenewableAudit">
        update t_renewable_audit
        <set>
            <if test="demandOrderId != null">
                demand_order_id = #{demandOrderId,jdbcType=BIGINT},
            </if>
            <if test="demandOrderCode != null">
                demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="renewableOrderCode != null">
                renewable_order_code = #{renewableOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="businessType != null">
                business_type = #{businessType,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                source = #{source,jdbcType=INTEGER},
            </if>
            <if test="goodsAmountTotal != null">
                goods_amount_total = #{goodsAmountTotal,jdbcType=DECIMAL},
            </if>
            <if test="verifiedGoodsAmountTotal != null">
                verified_goods_amount_total = #{verifiedGoodsAmountTotal,jdbcType=DECIMAL},
            </if>
            <if test="vehicleId != null">
                vehicle_id = #{vehicleId,jdbcType=BIGINT},
            </if>
            <if test="vehicleNo != null">
                vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
            </if>
            <if test="staffId != null">
                staff_id = #{staffId,jdbcType=BIGINT},
            </if>
            <if test="staffProperty != null">
                staff_property = #{staffProperty,jdbcType=INTEGER},
            </if>
            <if test="staffName != null">
                staff_name = #{staffName,jdbcType=VARCHAR},
            </if>
            <if test="staffMobile != null">
                staff_mobile = HEX(AES_ENCRYPT(#{staffMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="publishTime != null">
                publish_time = #{publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="customerName != null">
                customer_name = #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="customerUserName != null">
                customer_user_name = #{customerUserName,jdbcType=VARCHAR},
            </if>
            <if test="customerUserMobile != null">
                customer_user_mobile = HEX(AES_ENCRYPT(#{customerUserMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="publishUserName != null">
                publish_user_name = #{publishUserName,jdbcType=VARCHAR},
            </if>
            <if test="publishUserMobile != null">
                publish_user_mobile = HEX(AES_ENCRYPT(#{publishUserMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditorName != null">
                auditor_name = #{auditorName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="renewableAuditListCountIds" parameterType="com.logistics.tms.controller.renewableaudit.request.RenewableAuditRequestModel" resultType="java.lang.Long">
        select DISTINCT
        tra.id renewableOrderId
        from t_renewable_audit tra
        left join t_renewable_audit_address traa on tra.id=traa.renewable_order_id and traa.valid = 1
        <where>
            tra.valid = 1
            <if test="renewableOrderCode !=null and renewableOrderCode !=''">
                and instr(tra.renewable_order_code, #{renewableOrderCode,jdbcType=VARCHAR})
            </if>
            <if test="customerName != null and customerName != ''">
                and (instr(tra.customer_name, #{customerName,jdbcType=VARCHAR})
                or instr(tra.customer_user_name,#{customerName,jdbcType=VARCHAR})
                or instr(AES_DECRYPT(UNHEX(tra.customer_user_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'),#{customerName,jdbcType=VARCHAR}))
            </if>
            <if test="businessType !=null and businessType !=''">
                and tra.business_type = #{businessType,jdbcType=INTEGER}
            </if>
            <if test="loadDetailAddress !=null and loadDetailAddress !=''">
                and (instr(traa.load_province_name,#{loadDetailAddress,jdbcType=VARCHAR}) or
                instr(traa.load_city_name,#{loadDetailAddress,jdbcType=VARCHAR}) or
                instr(traa.load_detail_address,#{loadDetailAddress,jdbcType=VARCHAR}) or
                instr(traa.load_area_name,#{loadDetailAddress,jdbcType=VARCHAR}) or
                instr(CONCAT(traa.load_province_name,traa.load_city_name,traa.load_area_name,traa.load_detail_address),#{loadDetailAddress,jdbcType=VARCHAR}))
            </if>
            <if test="consignor !=null and consignor !=''">
                and (instr(traa.consignor_name, #{consignor,jdbcType=VARCHAR}) or instr(AES_DECRYPT(UNHEX(traa.consignor_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'), #{consignor,jdbcType=VARCHAR}))
            </if>
            <if test="vehicleNo !=null and vehicleNo !=''">
                and instr(tra.vehicle_no , #{vehicleNo,jdbcType=VARCHAR})
            </if>
            <if test="driver !=null and driver !=''">
                and (instr(tra.staff_name , #{driver,jdbcType=VARCHAR}) or instr(AES_DECRYPT(UNHEX(tra.staff_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'), #{driver,jdbcType=VARCHAR}))
            </if>
            <if test="unloadWarehouse !=null and unloadWarehouse !=''">
                and instr(traa.unload_warehouse,#{unloadWarehouse,jdbcType=VARCHAR})
            </if>
            <if test="unloadDetailAddress !=null and unloadDetailAddress !=''">
                and (instr(traa.unload_province_name,#{unloadDetailAddress,jdbcType=VARCHAR}) or
                instr(traa.unload_city_name,#{unloadDetailAddress,jdbcType=VARCHAR}) or
                instr(traa.unload_area_name,#{unloadDetailAddress,jdbcType=VARCHAR}) or
                instr(traa.unload_detail_address,#{unloadDetailAddress,jdbcType=VARCHAR}) or
                instr(traa.unload_warehouse,#{unloadDetailAddress,jdbcType=VARCHAR}) or
                instr(CONCAT(traa.unload_province_name,traa.unload_city_name,traa.unload_area_name,traa.unload_detail_address,traa.unload_warehouse),#{unloadDetailAddress,jdbcType=VARCHAR}))
            </if>
            <if test="receiver !=null and receiver !=''">
                and (instr(traa.receiver_name,#{receiver,jdbcType=VARCHAR}) or instr(AES_DECRYPT(UNHEX(traa.receiver_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'),#{receiver,jdbcType=VARCHAR}))
            </if>
            <if test="publishStartTime !=null and publishStartTime !=''">
                and tra.publish_time &gt;= DATE_FORMAT(#{publishStartTime,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
            </if>
            <if test="publishEndTime !=null and publishEndTime !=''">
                and tra.publish_time &lt;= DATE_FORMAT(#{publishEndTime,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
            </if>
            <if test="auditStartTime !=null and auditStartTime !=''">
                and tra.audit_time &gt;= DATE_FORMAT(#{auditStartTime,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
            </if>
            <if test="auditEndTime !=null and auditEndTime !=''">
                and tra.audit_time &lt;= DATE_FORMAT(#{auditEndTime,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
            </if>
            <if test="status != null">
                and tra.status = #{status,jdbcType=INTEGER}
            </if>
            <if test="renewableOrderIds!=null and renewableOrderIds.size()>0">
                and tra.id in
                <foreach collection="renewableOrderIds" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        order by tra.last_modified_time desc,tra.id desc
    </select>

  <select id="renewableAuditList" resultType="com.logistics.tms.controller.renewableaudit.response.RenewableAuditResponseModel">
    select
       tra.id renewableOrderId,
       tra.renewable_order_code renewableOrderCode,
       tra.status,
       tra.customer_name customerName,
       tra.customer_user_name customerUserName,
       tra.goods_amount_total goodsAmountTotal,
       tra.verified_goods_amount_total verifiedGoodsAmountTotal,
       tra.business_type businessType,
       traa.load_province_name loadProvinceName,
       traa.load_city_name loadCityName,
       traa.load_area_name loadAreaName,
       traa.load_detail_address loadDetailAddress,
       traa.consignor_name consignorName,
       tra.staff_name staffName,
       tra.vehicle_no vehicleNo,
       traa.unload_warehouse unloadWarehouse,
       traa.unload_province_name unloadProvinceName,
       traa.unload_city_name unloadCityName,
       traa.unload_area_name unloadAreaName,
       traa.unload_detail_address unloadDetailAddress,
       traa.receiver_name receiverName,
       AES_DECRYPT(UNHEX(traa.consignor_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as  consignorMobile,
       AES_DECRYPT(UNHEX(tra.staff_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as  staffMobile,
       AES_DECRYPT(UNHEX(tra.customer_user_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as  customerUserMobile,
       AES_DECRYPT(UNHEX(traa.receiver_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as  receiverMobile
    from t_renewable_audit tra
    left join t_renewable_audit_address traa on tra.id=traa.renewable_order_id and traa.valid = 1
    where
        tra.valid = 1 and tra.id in (${renewableAuditIdLists}) order by tra.last_modified_time desc,tra.id desc
  </select>

    <resultMap id="getRenewableAuditDetailByIdMap" type="com.logistics.tms.controller.renewableaudit.response.RenewableAuditDetailResponseModel">
        <id column="id" property="renewableOrderId" jdbcType="BIGINT"/>
        <result column="renewable_order_code" property="renewableOrderCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="source" property="source" jdbcType="INTEGER"/>
        <result column="business_type" property="businessType" jdbcType="INTEGER"/>
        <result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
        <result column="customer_user_name" property="customerUserName" jdbcType="VARCHAR"/>
        <result column="customer_user_mobile" property="customerUserMobile" jdbcType="VARCHAR"/>
        <result column="publish_user_name" property="publishUserName" jdbcType="VARCHAR"/>
        <result column="publish_user_mobile" property="publishUserMobile" jdbcType="VARCHAR"/>
        <result column="publish_time" property="publishTime" jdbcType="TIMESTAMP"/>
        <result column="staff_name" property="staffName" jdbcType="VARCHAR"/>
        <result column="staff_mobile" property="staffMobile" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="expected_load_time" property="expectedLoadTime" jdbcType="TIMESTAMP"/>
        <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR"/>
        <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR"/>
        <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR"/>
        <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR"/>
        <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR"/>
        <result column="consignor_name" property="consignorName" jdbcType="VARCHAR"/>
        <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR"/>
        <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR"/>
        <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR"/>
        <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR"/>
        <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR"/>
        <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR"/>
        <result column="receiver_name" property="receiverName" jdbcType="VARCHAR"/>
        <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR"/>
        <collection property="goodsResponseModelList" ofType="com.logistics.tms.controller.renewableaudit.response.RenewableAuditGoodsResponseModel">
            <result column="renewable_sku_code" property="skuCode" jdbcType="VARCHAR"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="goods_amount" property="goodsAmount" jdbcType="DECIMAL"/>
            <result column="goods_price" property="goodsPrice" jdbcType="DECIMAL"/>
            <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
            <result column="goods_source_type" property="goodsSourceType" jdbcType="INTEGER"/>
        </collection>
    </resultMap>

    <select id="getRenewableAuditDetail" resultMap="getRenewableAuditDetailByIdMap">
        select
           tra.id,
           tra.renewable_order_code ,
           tra.status ,
           tra.source ,
           tra.business_type,
           tra.customer_name ,
           tra.customer_user_name,
           tra.publish_user_name ,
           tra.staff_name,
           AES_DECRYPT(UNHEX(tra.staff_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as staff_mobile,
           AES_DECRYPT(UNHEX(tra.publish_user_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as  publish_user_mobile,
           AES_DECRYPT(UNHEX(tra.customer_user_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as  customer_user_mobile,
           tra.publish_time ,
           tra.remark ,


           traa.expected_load_time,
           traa.load_warehouse,
           traa.load_province_name,
           traa.load_city_name,
           traa.load_area_name ,
           traa.load_detail_address,
           traa.consignor_name ,
           AES_DECRYPT(UNHEX(traa.consignor_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as  consignor_mobile,
           traa.unload_warehouse ,
           traa.unload_province_name ,
           traa.unload_city_name ,
           traa.unload_area_name ,
           traa.unload_detail_address,
           traa.receiver_name ,
           AES_DECRYPT(UNHEX(traa.receiver_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as  receiver_mobile,

           trag.renewable_sku_code,
           trag.goods_name ,
           trag.goods_amount ,
           trag.goods_price ,
           trag.goods_unit ,
           trag.goods_source_type

        from t_renewable_audit tra
        left join t_renewable_audit_address traa on tra.id=traa.renewable_order_id and traa.valid=1
        left join t_renewable_audit_goods trag on tra.id=trag.renewable_order_id and trag.valid=1
        <where>
            tra.valid = 1
            and
            tra.id = #{requestModel.renewableOrderId,jdbcType=BIGINT}
        </where>
    </select>

    <select id="getAssignDriverDetail" parameterType="com.logistics.tms.controller.renewableaudit.request.RenewableAssignDriverDetailRequestModel" resultType="com.logistics.tms.controller.renewableaudit.response.RenewableAssignDriverDetailResponseModel">
        select
         tra.id                                                                                                                           renewableOrderId,
         tra.renewable_order_code                                                                                                         renewableOrderCode,
         tra.customer_name                                                                                                                customerName,
         tra.goods_amount_total                                                                                                           goodsAmountTotal,
         tra.staff_name                                                                                                                   staffName,
         tra.customer_user_name                                                                                                            customerUserName,
         tra.business_type                                                                                                                businessType,
         AES_DECRYPT(UNHEX(tra.customer_user_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as  customerUserMobile,
         AES_DECRYPT(UNHEX(tra.staff_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as         staffMobile,
         traa.load_warehouse                                                                                                              loadWarehouse,
         traa.load_province_name                                                                                                          loadProvinceName,
         traa.load_city_name                                                                                                              loadCityName,
         traa.load_area_name                                                                                                              loadAreaName
        from t_renewable_audit tra
        left join t_renewable_audit_address traa on tra.id = traa.renewable_order_id and traa.valid = 1
        <where>
            tra.valid = 1
            <if test="renewableOrderIds != null and renewableOrderIds.size() > 0">
                and tra.id in
                <foreach collection="renewableOrderIds" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        order by tra.publish_time desc, tra.id desc
    </select>

    <resultMap id="renewableConfirmGoodsMap" type="com.logistics.tms.controller.renewableaudit.response.RenewableConfirmGoodsResponseModel">
        <id column="id" property="renewableOrderId" jdbcType="BIGINT"/>
        <result  column="renewable_order_code" property="renewableOrderCode" jdbcType="VARCHAR"/>
        <result  column="staff_name" property="staffName" jdbcType="VARCHAR"/>
        <result  column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
        <collection property="confirmGoodsList" ofType="com.logistics.tms.controller.renewableaudit.response.RenewableAuditGoodsResponseModel">
            <result column="renewable_goods_id" property="renewableGoodsId" jdbcType="BIGINT"/>
            <result column="renewable_sku_code" property="skuCode" jdbcType="VARCHAR"/>
            <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
            <result column="goods_amount" property="goodsAmount" jdbcType="DECIMAL"/>
            <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER"/>
            <result column="goods_price" property="goodsPrice" jdbcType="DECIMAL"/>
            <result column="goods_source_type" property="goodsSourceType" jdbcType="INTEGER"/>
        </collection>
        <collection property="photosList" ofType="com.logistics.tms.controller.renewableaudit.response.RenewableTicketsUrlResponseModel">
            <result column="file_type" property="fileType" jdbcType="INTEGER"/>
            <result column="file_type_name" property="fileTypeName" jdbcType="VARCHAR"/>
            <result column="file_path" property="filePath" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>
    <select id="confirmNewsDetail" resultMap="renewableConfirmGoodsMap">
        select
        tra.id,
        tra.renewable_order_code,
        tra.staff_name,
        tra.vehicle_no,
        trag.id renewable_goods_id,
        trag.renewable_sku_code,
        trag.goods_name,
        trag.goods_amount,
        trag.goods_unit,
        trag.goods_price,
        trag.goods_source_type,
        tcp.file_type_name,
        tcp.file_path
        from t_renewable_audit tra
        left join t_renewable_audit_goods trag on tra.id = trag.renewable_order_id and trag.valid = 1
        left join t_certification_pictures tcp on tra.id = tcp.object_id and tcp.object_type = #{objectType} and tcp.valid = 1
        where tra.valid = 1
        and tra.id = #{renewableOrderId}
    </select>


      <select id="getCountByStaffId" resultType="int">
        select
        count(*)
        from t_renewable_audit
        where valid = 1 and staff_id = #{staffId}
        <if test="status!=null">
          and status = #{status}
        </if>
      </select>

    <select id="queryRenewableOrderListByStaffId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt" />
        from t_renewable_audit
        where valid = 1 and staff_id = #{staffId}
        <if test="status!=null">
            and status = #{status}
        </if>
        order by last_modified_time desc, id desc
    </select>

    <select id="renewableOrderListStatistic" resultType="com.logistics.tms.controller.renewableaudit.response.RenewableOrderListStatisticResponseModel">
        select ifnull(sum(if(status = 1 or status = 2 or status = 3, 1, 0)), 0) as allCount,
               ifnull(sum(if(status = 1, 1, 0)), 0) as waitConfirm,
               ifnull(sum(if(status = 2, 1, 0)), 0) as waitAudit,
               ifnull(sum(if(status = 3, 1, 0)), 0) as audited
            from t_renewable_audit
        where valid = 1 and staff_id = #{staffId}
    </select>
    <select id="getRenewableAuditListStatistics" resultType="com.logistics.tms.controller.renewableaudit.response.RenewableAuditListStatisticsResponseModel">
        select
        ifnull(count(*), 0)                                                                    as allCount,
        ifnull(sum(if(status =0,1,0)), 0)                                                      as waitAssignedCount,
        ifnull(sum(if(status =1,1,0)), 0)                                                      as waitConfirmCount,
        ifnull(sum(if(status =2,1,0)), 0)                                                      as waitAuditCount,
        ifnull(sum(if(status =3,1,0)), 0)                                                      as alreadyAuditCount,
        ifnull(sum(if(status =4,1,0)), 0)                                                      as alreadyCancelCount
        from t_renewable_audit
        where valid = 1
        <if test="renewableOrderIds != null and renewableOrderIds != ''">
            and id in (${renewableOrderIds})
        </if>
    </select>

    <select id="getByRenewableOrderCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt" />
        from t_renewable_audit
        where valid = 1
        and renewable_order_code = #{renewableOrderCode,jdbcType=VARCHAR}
    </select>

    <select id="selectByDemandOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_renewable_audit
        where valid = 1
        <if test="demandOrderId != null">
            and demand_order_id = #{demandOrderId}
        </if>
    </select>

    <update id="batchUpdateSelectiveEncrypt">
        <foreach collection="list" item="item" separator=";">
        update t_renewable_audit
        <set>
            <if test="item.demandOrderId != null">
                demand_order_id = #{item.demandOrderId,jdbcType=BIGINT},
            </if>
            <if test="item.demandOrderCode != null">
                demand_order_code = #{item.demandOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="item.renewableOrderCode != null">
                renewable_order_code = #{item.renewableOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="item.status != null">
                status = #{item.status,jdbcType=INTEGER},
            </if>
            <if test="item.businessType != null">
                business_type = #{item.businessType,jdbcType=INTEGER},
            </if>
            <if test="item.source != null">
                source = #{item.source,jdbcType=INTEGER},
            </if>
            <if test="item.goodsAmountTotal != null">
                goods_amount_total = #{item.goodsAmountTotal,jdbcType=DECIMAL},
            </if>
            <if test="item.verifiedGoodsAmountTotal != null">
                verified_goods_amount_total = #{item.verifiedGoodsAmountTotal,jdbcType=DECIMAL},
            </if>
            <if test="item.vehicleId != null">
                vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
            </if>
            <if test="item.vehicleNo != null">
                vehicle_no = #{item.vehicleNo,jdbcType=VARCHAR},
            </if>
            <if test="item.staffId != null">
                staff_id = #{item.staffId,jdbcType=BIGINT},
            </if>
            <if test="item.staffProperty != null">
                staff_property = #{item.staffProperty,jdbcType=INTEGER},
            </if>
            <if test="item.staffName != null">
                staff_name = #{item.staffName,jdbcType=VARCHAR},
            </if>
            <if test="item.staffMobile != null">
                staff_mobile = HEX(AES_ENCRYPT(#{item.staffMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="item.publishTime != null">
                publish_time = #{item.publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.customerName != null">
                customer_name = #{item.customerName,jdbcType=VARCHAR},
            </if>
            <if test="item.customerUserName != null">
                customer_user_name = #{item.customerUserName,jdbcType=VARCHAR},
            </if>
            <if test="item.customerUserMobile != null">
                customer_user_mobile = HEX(AES_ENCRYPT(#{item.customerUserMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="item.publishUserName != null">
                publish_user_name = #{item.publishUserName,jdbcType=VARCHAR},
            </if>
            <if test="item.publishUserMobile != null">
                publish_user_mobile = HEX(AES_ENCRYPT(#{item.publishUserMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="item.auditTime != null">
                audit_time = #{item.auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.auditorName != null">
                auditor_name = #{item.auditorName,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.createdBy != null">
                created_by = #{item.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="item.createdTime != null">
                created_time = #{item.createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastModifiedBy != null">
                last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="item.lastModifiedTime != null">
                last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.valid != null">
                valid = #{item.valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>