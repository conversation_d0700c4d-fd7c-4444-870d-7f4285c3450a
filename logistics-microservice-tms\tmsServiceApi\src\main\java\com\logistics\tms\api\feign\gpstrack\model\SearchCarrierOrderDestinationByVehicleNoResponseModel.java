package com.logistics.tms.api.feign.gpstrack.model;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SearchCarrierOrderDestinationByVehicleNoResponseModel {
    private Long gpsId;

    private Long dispatchOrderId;

    private String dispatchOrderCode;

    private String gpsVehicleNo;

    private Long vehicleId;

    private Date uploadTime;

    private VehicleLatestPositionModel vehicleLatestPosition;

    private List<CarrierOrderDestinationModel> carrierOrderDestinations;
}
