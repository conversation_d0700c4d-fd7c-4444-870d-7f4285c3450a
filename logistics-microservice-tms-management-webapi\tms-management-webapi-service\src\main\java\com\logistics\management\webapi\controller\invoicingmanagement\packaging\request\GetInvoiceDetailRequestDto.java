package com.logistics.management.webapi.controller.invoicingmanagement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2024/3/19 17:23
 */
@Data
public class GetInvoiceDetailRequestDto {

    @ApiModelProperty(value = "发票id",required = true)
    @NotBlank(message = "发票id不能为空")
    private String invoiceId;

}
