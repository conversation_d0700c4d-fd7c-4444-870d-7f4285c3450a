package com.logistics.tms.api.impl.driverfreight;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.driverfreight.DriverFreightServiceApi;
import com.logistics.tms.api.feign.driverfreight.model.DriverFreightListSearchRequestModel;
import com.logistics.tms.api.feign.driverfreight.model.DriverFreightListSearchResponseModel;
import com.logistics.tms.biz.driverfreight.DriverFreightBiz;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(value = "API-DriverPayeeServiceApiImpl-司机运费管理")
public class DriverFreightServiceApiImpl implements DriverFreightServiceApi {

    @Autowired
    private DriverFreightBiz driverFreightBiz;

    /**
     * 司机运费列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<DriverFreightListSearchResponseModel>> driverFreightList(@RequestBody DriverFreightListSearchRequestModel requestModel) {
        return Result.success(driverFreightBiz.driverFreightList(requestModel));

    }
}
