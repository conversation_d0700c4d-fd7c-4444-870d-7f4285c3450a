package com.logistics.management.webapi.client.dispatch.response;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SearchCanJoinShippingOrderRespModel {

    /**
     * 零担id
     */
    private String shippingOrderId;

    /**
     * 零担单号
     */
    private String shippingOrderCode;

    /**
     * 车牌号
     */
    private String vehicleNo;

    /**
     * 串点费用
     */
    private BigDecimal crossPointFee;

    /**
     * 整车运费
     */
    private BigDecimal carrierFreight;

}
