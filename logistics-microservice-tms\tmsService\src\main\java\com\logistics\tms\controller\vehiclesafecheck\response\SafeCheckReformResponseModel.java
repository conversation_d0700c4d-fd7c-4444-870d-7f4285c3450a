package com.logistics.tms.controller.vehiclesafecheck.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/11/6 15:01
 */
@Data
public class SafeCheckReformResponseModel {
    @ApiModelProperty("整改表ID")
    private Long checkReformId;
    @ApiModelProperty("整改数")
    private Integer reformCount;
    @ApiModelProperty("整改内容")
    private String reformContent;
    @ApiModelProperty("整改结果")
    private String reformResult;
    @ApiModelProperty("添加整改结果时间")
    private Date addReformResultTime;
    @ApiModelProperty("整改事项图片列表")
    private List<SafeCheckFileResponseModel> itemFileList;
    @ApiModelProperty("整改结果图片列表")
    private List<SafeCheckFileResponseModel> resultFileList;
}
