package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.terminalcustomeraddress.model.GetTerminalCustomerAddressDetailResponseModel;
import com.logistics.tms.api.feign.terminalcustomeraddress.model.SearchTerminalCustomerAddressListRequestModel;
import com.logistics.tms.api.feign.terminalcustomeraddress.model.SearchTerminalCustomerAddressListResponseModel;
import com.logistics.tms.entity.TTerminalCustomerAddress;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

@Mapper
public interface TTerminalCustomerAddressMapper extends BaseMapper<TTerminalCustomerAddress> {

    List<SearchTerminalCustomerAddressListResponseModel> searchTerminalCustomerAddressList(@Param("params") SearchTerminalCustomerAddressListRequestModel requestModel);

    GetTerminalCustomerAddressDetailResponseModel getTerminalCustomerAddressDetail(@Param("id")Long id);

    List<TTerminalCustomerAddress> getByIds(@Param("ids")String ids);

    void delTerminalCustomerAddress(@Param("ids")String ids,@Param("operator")String operator,@Param("operateTime") Date operateTime);

}