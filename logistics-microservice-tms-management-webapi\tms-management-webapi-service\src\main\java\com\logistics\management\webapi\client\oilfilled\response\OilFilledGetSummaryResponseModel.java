package com.logistics.management.webapi.client.oilfilled.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/10/9
 * @description:
 */
@Data
public class OilFilledGetSummaryResponseModel {

    @ApiModelProperty(value = "待结算数量")
    private Integer waitSettleCount;
    @ApiModelProperty(value = "已结算数量")
    private Integer haveSettleCount;

}
