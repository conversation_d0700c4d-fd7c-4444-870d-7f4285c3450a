package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TCarrierOrderTickets extends BaseEntity {
    /**
    * 运单ID
    */
    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    /**
    * 图片类型：1 提货单，2 出库单，3 签收单，4 其他，5 到达提货地凭证，6 到达卸货地凭证，7 入库单，8 提货现场图片，9 卸货现场图片
    */
    @ApiModelProperty("图片类型：1 提货单，2 出库单，3 签收单，4 其他，5 到达提货地凭证，6 到达卸货地凭证，7 入库单，8 提货现场图片，9 卸货现场图片")
    private Integer imageType;

    /**
    * 图片名
    */
    @ApiModelProperty("图片名")
    private String imageName;

    /**
    * 图片路径
    */
    @ApiModelProperty("图片路径")
    private String imagePath;

    /**
    * 上传人姓名
    */
    @ApiModelProperty("上传人姓名")
    private String uploadUserName;

    /**
    * 上传时间
    */
    @ApiModelProperty("上传时间")
    private Date uploadTime;
}