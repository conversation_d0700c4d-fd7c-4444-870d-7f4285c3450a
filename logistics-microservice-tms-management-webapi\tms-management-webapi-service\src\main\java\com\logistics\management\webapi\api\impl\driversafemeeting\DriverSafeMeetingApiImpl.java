package com.logistics.management.webapi.api.impl.driversafemeeting;

import cn.dev33.satoken.annotation.SaIgnore;
import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.driversafemeeting.DriverSafeMeetingApi;
import com.logistics.management.webapi.api.feign.driversafemeeting.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.driversafemeeting.mapping.DriverSafeMeetingContentDetailMapping;
import com.logistics.management.webapi.api.impl.driversafemeeting.mapping.DriverSafeMeetingDetailListMapping;
import com.logistics.management.webapi.api.impl.driversafemeeting.mapping.DriverSafeMeetingKanBanMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.constant.ExportExcelDriverSafeMeeting;
import com.logistics.tms.api.feign.driversafemeeting.DriverSafeMeetingServiceApi;
import com.logistics.tms.api.feign.driversafemeeting.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;

/**
 * @author: wjf
 * @date: 2019/11/4 10:03
 */
@RestController
@Slf4j
public class DriverSafeMeetingApiImpl implements DriverSafeMeetingApi {

    @Autowired
    private DriverSafeMeetingServiceApi driverSafeMeetingServiceApi;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 例会看板（列表）
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<DriverSafeMeetingKanBanResponseDto>> driverSafeMeetingKanBan(@RequestBody @Valid DriverSafeMeetingKanBanRequestDto requestDto) {
        Result<List<DriverSafeMeetingKanBanResponseModel>> result = driverSafeMeetingServiceApi.driverSafeMeetingKanBan(MapperUtils.mapper(requestDto, DriverSafeMeetingKanBanRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), DriverSafeMeetingKanBanResponseDto.class, new DriverSafeMeetingKanBanMapping()));
    }

    /**
     * 新增例会
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result addDriverSafeMeeting(@RequestBody @Valid AddDriverSafeMeetingRequestDto requestDto) {
        return driverSafeMeetingServiceApi.addDriverSafeMeeting(MapperUtils.mapper(requestDto, AddDriverSafeMeetingRequestModel.class));
    }

    /**
     * 重新编辑例会
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result modifyDriverSafeMeeting(@RequestBody @Valid ModifyDriverSafeMeetingRequestDto requestDto) {
        return driverSafeMeetingServiceApi.modifyDriverSafeMeeting(MapperUtils.mapper(requestDto, ModifyDriverSafeMeetingRequestModel.class));
    }

    /**
     * 补发例会
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result replacementDriverSafeMeeting(@RequestBody @Valid ReplacementDriverSafeMeetingRequestDto requestDto) {
        return driverSafeMeetingServiceApi.replacementDriverSafeMeeting(MapperUtils.mapper(requestDto, ReplacementDriverSafeMeetingRequestModel.class));
    }

    /**
     * 例会内容详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<DriverSafeMeetingContentDetailResponseDto> driverSafeMeetingContentDetail(@RequestBody @Valid DriverSafeMeetingIdRequestDto requestDto) {
        Result<DriverSafeMeetingContentDetailResponseModel> result = driverSafeMeetingServiceApi.driverSafeMeetingContentDetail(MapperUtils.mapper(requestDto, DriverSafeMeetingIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), DriverSafeMeetingContentDetailResponseDto.class, new DriverSafeMeetingContentDetailMapping()));
    }

    /**
     * 导出例会内容详情（pdf）
     * @param requestDto
     * @param response
     * @param request
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @SaIgnore
    public void exportDriverSafeMeetingContentDetail(DriverSafeMeetingIdRequestDto requestDto, HttpServletResponse response, HttpServletRequest request) {
        Result<DriverSafeMeetingContentDetailResponseModel> result = driverSafeMeetingServiceApi.driverSafeMeetingContentDetail(MapperUtils.mapper(requestDto, DriverSafeMeetingIdRequestModel.class));
        result.throwException();
        DriverSafeMeetingContentDetailResponseDto detail = MapperUtils.mapper(result.getData(), DriverSafeMeetingContentDetailResponseDto.class, new DriverSafeMeetingContentDetailMapping());
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("driverCount", detail.getDriverCount());
        dataMap.put("period", detail.getPeriod());
        dataMap.put("title", detail.getTitle());
        dataMap.put("introduction", detail.getIntroduction());
        dataMap.put("createDate", detail.getCreatedTime());
        dataMap.put("content", detail.getContent());

        String pdfBasePath = "/tmp/htmlfont";//模板、字体和生成的html、pdf存放目录
        String fileName = "/driver_safe_meeting.html";
        boolean notExistFlag = commonBiz.dirIfExist(pdfBasePath);
        List<String> fileList = Arrays.asList("/template/driver_safe_meeting.html", "/font/msyh.ttc", "/font/msyhbd.ttc", "/font/msyhl.ttc");
        if (notExistFlag) {
            for (String filePath : fileList) {
                try (InputStream is = this.getClass().getResourceAsStream(filePath)) {
                    if (is != null) {
                        String tempFileName = filePath.substring(filePath.lastIndexOf('/'));
                        commonBiz.writeToLocal(is, pdfBasePath + tempFileName);
                    } else {
                        return;
                    }
                } catch (Exception e) {
                    log.info(e.getMessage());
                }
            }
        }
        String temPathBasePath = pdfBasePath + "/";
        List<String> fontNameList = Arrays.asList(pdfBasePath + "/msyh.ttc", pdfBasePath + "/msyhbd.ttc", pdfBasePath + "/msyhl.ttc");
        List<String> list = PDFUtils.createPdf(temPathBasePath, fileName, dataMap, temPathBasePath, fontNameList);
        if (ListUtils.isNotEmpty(list) && list.size() == CommonConstant.INTEGER_TWO) {
            String pdfName = detail.getPeriod() + "安全例会";
            try (InputStream inputStream = new FileInputStream(new File(list.get(CommonConstant.INTEGER_ONE)))) {
                commonBiz.downLoadFile(pdfName, "pdf", inputStream, response);
            } catch (Exception e) {
                log.info("downLoadFile error", e);
            } finally {
                for (String path : list) {
                    File file = new File(path);
                    if (file.isFile() && file.exists()) {
                        if (!file.delete()) {
                            log.info("安全例会临时删除失败 ");
                        }
                    }
                }
            }
        }
    }

    /**
     * 学习详情（司机学习列表）
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<DriverSafeMeetingDetailResponseDto>> driverSafeMeetingDetailList(@RequestBody DriverSafeMeetingDetailRequestDto requestDto) {
        Result<PageInfo<DriverSafeMeetingDetailResponseModel>> result = driverSafeMeetingServiceApi.driverSafeMeetingDetailList(MapperUtils.mapper(requestDto, DriverSafeMeetingDetailRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<String> sourceSrcList=new ArrayList<>();
        for (DriverSafeMeetingDetailResponseModel ticketsModel : result.getData().getList()) {
            sourceSrcList.add(ticketsModel.getSignImageUrl());
            sourceSrcList.add(ticketsModel.getStaffDriverImageUrl());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        List<DriverSafeMeetingDetailResponseDto> list = MapperUtils.mapper(pageInfo.getList(), DriverSafeMeetingDetailResponseDto.class, new DriverSafeMeetingDetailListMapping(configKeyConstant.fileAccessAddress,imageMap));
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    /**
     * 学习详情（司机学习列表统计人数）
     * @param requestDto
     * @return
     */
    @Override
    public Result<DriverSafeMeetingListCountResponseDto> driverSafeMeetingListCount(@RequestBody DriverSafeMeetingDetailRequestDto requestDto) {
        Result<DriverSafeMeetingListCountResponseModel> result = driverSafeMeetingServiceApi.driverSafeMeetingListCount(MapperUtils.mapper(requestDto, DriverSafeMeetingDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), DriverSafeMeetingListCountResponseDto.class));
    }

    /**
     * 导出学习详情（司机学习列表）
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportDriverSafeMeetingDetailList(DriverSafeMeetingDetailRequestDto requestDto, HttpServletResponse response) {
        DriverSafeMeetingDetailRequestModel model = MapperUtils.mapper(requestDto, DriverSafeMeetingDetailRequestModel.class);
        model.setPageNum(CommonConstant.INTEGER_ZERO);
        model.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<DriverSafeMeetingDetailResponseModel>> result = driverSafeMeetingServiceApi.driverSafeMeetingDetailList(model);
        result.throwException();
        PageInfo pageInfo = result.getData();
        //获取图片访问路径
        List<String> sourceSrcList=new ArrayList<>();
        for (DriverSafeMeetingDetailResponseModel ticketsModel : result.getData().getList()) {
            sourceSrcList.add(ticketsModel.getSignImageUrl());
            sourceSrcList.add(ticketsModel.getStaffDriverImageUrl());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        List<DriverSafeMeetingDetailResponseDto> list = MapperUtils.mapper(pageInfo.getList(), DriverSafeMeetingDetailResponseDto.class, new DriverSafeMeetingDetailListMapping(configKeyConstant.fileAccessAddress,imageMap));
        String fileName = "安全例会" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String, String> exportMap = ExportExcelDriverSafeMeeting.getExcelDriverSafeMeeting();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return list;
            }
        });
    }

    /**
     * 删除学习详情
     * @param requestDto
     * @return
     */
    @Override
    public Result delDriverSafeMeetingRelation(@RequestBody @Valid DriverSafeMeetingRelationIdRequestDto requestDto) {
        return Result.success(driverSafeMeetingServiceApi.delDriverSafeMeetingRelation(MapperUtils.mapper(requestDto, DriverSafeMeetingRelationIdRequestModel.class)));
    }

    /**
     * 获取年份（2019到当前年份）
     * @return
     */
    @Override
    public Result<List<String>> getYear() {
        String currentYear = DateUtils.dateToString(new Date(), CommonConstant.DATE_FORMAT_ACCURATE_YEAR);
        List<String> list = new ArrayList<>();
        for (int i = ConverterUtils.toInt(currentYear); i >= CommonConstant.DRIVER_SAFE_MEETING_START_YEAR; i--) {
            list.add(ConverterUtils.toString(i));
        }
        return Result.success(list);
    }
}
