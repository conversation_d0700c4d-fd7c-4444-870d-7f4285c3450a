package com.logistics.tms.controller.carrierorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/23
 */
@Data
public class SearchYeloLifeCarrierOrderRequestModel extends AbstractPageForm<SearchYeloLifeCarrierOrderRequestModel> {

	@ApiModelProperty("运单状态 10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0 已取消")
	private Integer status;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("需求单号")
	private String demandOrderCode;

	@ApiModelProperty("回收申请单号")
	private String recycleOrderCode;

	@ApiModelProperty("下单人")
	private String publishName;

	@ApiModelProperty("车牌号")
	private String vehicleNo;

	@ApiModelProperty("取件地址")
	private String loadAddress;

	@ApiModelProperty("卸货仓库")
	private String unloadWareHouse;

	@ApiModelProperty("提货时间from")
	private String loadTimeFrom;

	@ApiModelProperty("提货时间to")
	private String loadTimeTo;

	private List<Long> carrierOrderIdList;//运单筛选条件借用字段
	/**
	 * 拥有地址权限的省份idList
	 */
	private List<Long> provinceIdList;
}
