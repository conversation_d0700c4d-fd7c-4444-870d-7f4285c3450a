package com.logistics.management.webapi.controller.biddingorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class BiddingOrderQuoteDetailResponseDto {
    /**
     * 报价单id
     */
    @ApiModelProperty("报价单id")
    private String biddingOrderQuoteId="";

    /**
     * 车主
     */
    @ApiModelProperty("车主")
    private String carrierContactName="";

    /**
     * 报价金额
     */
    @ApiModelProperty("报价金额")
    private String quotePrice="";

    /**
     * 报价单价
     */
    @ApiModelProperty("报价金额")
    private String unitPrice="";

    /**
     * 车长id
     */
    @ApiModelProperty("车长id")
    private String vehicleLengthId;

    /**
     * 车长
     */
    @ApiModelProperty("车长")
    private String vehicleLength="";

    /**
     * 报价金额类型：1 单价，2 一口价
     */
    @ApiModelProperty("报价金额类型：1 单价，2 一口价")
    private String quotePriceType="";

    /**
     * 需求单列表
     */
    @ApiModelProperty("需求单列表")
    private List<BiddingDemandDto> demandDtoList = new ArrayList<>();

}
