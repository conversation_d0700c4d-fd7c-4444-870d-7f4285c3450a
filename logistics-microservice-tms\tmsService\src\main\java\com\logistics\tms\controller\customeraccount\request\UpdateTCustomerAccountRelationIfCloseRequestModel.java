package com.logistics.tms.controller.customeraccount.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author：wjf
 * @date：2021/5/13 17:57
 */
@Data
public class UpdateTCustomerAccountRelationIfCloseRequestModel {
    @ApiModelProperty(value = "userId集合",required = true)
    private String userIds;
    @ApiModelProperty(value = "用户角色：1 tms司机",required = true)
    private Integer userRole;
    @ApiModelProperty(value = "启用 1，禁用 0",required = true)
    private Integer enabled;
    @ApiModelProperty(value = "是否关闭账号：0否，1是",required = true)
    private Integer ifClose;
}
