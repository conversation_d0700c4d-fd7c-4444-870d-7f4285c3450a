package com.logistics.management.webapi.client.thirdparty.basicdata.user.hystrix;

import com.logistics.management.webapi.client.thirdparty.basicdata.user.UserClient;
import com.logistics.management.webapi.client.thirdparty.basicdata.user.request.GetUserInfoListRequestModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.user.response.GetUserInfoListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/22 15:16
 */
@Component
public class UserClientHystrix implements UserClient {
    @Override
    public Result<List<GetUserInfoListResponseModel>> getUserInfoList(GetUserInfoListRequestModel requestModel) {
        return Result.timeout();
    }
}
