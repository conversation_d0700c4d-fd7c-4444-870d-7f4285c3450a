package com.logistics.tms.controller.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class SearchConsignorRequestModel {

	@ApiModelProperty("当前经度")
	private String longitude;

	@ApiModelProperty("当前纬度")
	private String latitude;

	@ApiModelProperty("搜索方式：1 距离优先，2 综合")
	private String searchType;

	@ApiModelProperty("支持根据发货仓库、发货省市区、发货详细地址，发货联系人姓名以及联系方式模糊查询发货地址")
	private String consignorCondition;
}
