package com.logistics.tms.controller.demandorder.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CompleteDemandOrderBackAmountModel {
    private String demandOrderCode;
    private BigDecimal completeBackAmount;
    private String lastModifiedBy;
    private List<CompleteDemandOrderCarrierOrderBackAmountModel> models;

    private String demandCode;
    private Integer demandState;
}