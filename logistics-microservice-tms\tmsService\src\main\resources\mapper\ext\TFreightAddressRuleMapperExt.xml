<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TFreightAddressRuleMapper" >
    <select id="getListByFreightAddressIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_freight_address_rule
        where valid = 1
        and freight_address_id in (${freightAddressIds})
    </select>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            update t_freight_address_rule
            <set >
                <if test="item.freightAddressId != null" >
                    freight_address_id = #{item.freightAddressId,jdbcType=BIGINT},
                </if>
                <if test="item.ruleIndex != null" >
                    rule_index = #{item.ruleIndex,jdbcType=INTEGER},
                </if>
                <if test="item.amountFrom != null" >
                    amount_from = #{item.amountFrom,jdbcType=DECIMAL},
                </if>
                <if test="item.fromSymbol != null" >
                    from_symbol = #{item.fromSymbol,jdbcType=INTEGER},
                </if>
                <if test="item.amountTo != null" >
                    amount_to = #{item.amountTo,jdbcType=DECIMAL},
                </if>
                <if test="item.toSymbol != null" >
                    to_symbol = #{item.toSymbol,jdbcType=INTEGER},
                </if>
                <if test="item.freightType != null" >
                    freight_type = #{item.freightType,jdbcType=INTEGER},
                </if>
                <if test="item.freightFee != null" >
                    freight_fee = #{item.freightFee,jdbcType=DECIMAL},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>