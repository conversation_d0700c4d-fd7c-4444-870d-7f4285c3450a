package com.logistics.management.webapi.api.impl.parkingfee.mapping;

import com.logistics.management.webapi.api.feign.parkingfee.dto.ParkingFeeDeductingHistoryResponseDto;
import com.logistics.management.webapi.api.feign.parkingfee.dto.ParkingFeeDetailResponseDto;
import com.logistics.management.webapi.base.enums.SettlementStatusEnum;
import com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeDeductingHistoryResponseModel;
import com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;

import java.util.Optional;

/**
 * @Author: sj
 * @Date: 2019/10/9 16:32
 */
public class ParkingFeeDetailMapping extends MapperMapping<ParkingFeeDetailResponseModel,ParkingFeeDetailResponseDto> {
    @Override
    public void configure() {
        ParkingFeeDetailResponseModel source = this.getSource();
        ParkingFeeDetailResponseDto target = this.getDestination();
        if(source != null){
            target.setStatusLabel(SettlementStatusEnum.getEnum(source.getStatus()).getValue());
            if(source.getStartDate()!=null){
                target.setStartDate(DateUtils.dateToString(source.getStartDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getStartDate()!=null){
                target.setEndDate(DateUtils.dateToString(source.getEndDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getFinishDate()!=null){
                target.setFinishDate(DateUtils.dateToString(source.getFinishDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            target.setDriverLabel(Optional.ofNullable(target.getName()).orElse("")+" "+ Optional.ofNullable(target.getMobile()).orElse("") );

            if(ListUtils.isNotEmpty(source.getDeductingHistoryList())){
                for (ParkingFeeDeductingHistoryResponseModel tempModel : source.getDeductingHistoryList()) {
                    for (ParkingFeeDeductingHistoryResponseDto tmpDto : target.getDeductingHistoryList()) {
                        if(tmpDto.getParkingFeeDeductingId().equals(ConverterUtils.toString(tempModel.getParkingFeeDeductingId()))){
                            tmpDto.setRemainingDeductingFeeTotal(ConverterUtils.toString(tempModel.getDeductingFee().add(tempModel.getRemainingDeductingFee())));
                        }
                    }
                }
            }
        }
    }
}
