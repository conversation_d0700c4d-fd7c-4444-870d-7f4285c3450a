package com.logistics.tms.biz.dispatch.event;

import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz;
import com.logistics.tms.biz.dispatch.model.UpdateCarrierExpectMileageModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 更新运单预计里程数
 */
@Component
public class CarrierOrderExpectMileageEventListener {

    @Autowired
    private CarrierOrderCommonBiz carrierOrderCommonBiz;

    /**
     * 更新运单预计里程数
     *
     * @param event 运单id集合
     */
    @TransactionalEventListener
    public void updateExpectMileageEvent(UpdateCarrierExpectMileageModel event) {
        AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.carrierOrderMileage(event.getCarrierOrderIds(), null));
    }
}
