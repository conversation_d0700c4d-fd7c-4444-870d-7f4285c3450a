package com.logistics.tms.api.feign.gpsfee;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.gpsfee.hystrix.GpsFeeServiceApiHystrix;
import com.logistics.tms.api.feign.gpsfee.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/8 10:26
 */
@Api(value = "API-GpsFeeServiceApi-gps费用管理")
@FeignClient(name = "logistics-tms-services", fallback = GpsFeeServiceApiHystrix.class)
public interface GpsFeeServiceApi {

    @ApiOperation("查询gps费用列表")
    @PostMapping(value = "/service/gpsFee/searchGpsFeeList")
    Result<PageInfo<SearchGpsFeeListResponseModel>> searchGpsFeeList(@RequestBody SearchGpsFeeListRequestModel requestModel);

    @ApiOperation("统计gps费用列表各状态数量")
    @PostMapping(value = "/service/gpsFee/searchGpsFeeListCount")
    Result<SearchGpsFeeListCountResponseModel> searchGpsFeeListCount(@RequestBody SearchGpsFeeListRequestModel requestModel);

    @ApiOperation("查看详情")
    @PostMapping(value = "/service/gpsFee/getGpsFeeDetail")
    Result<GpsFeeDetailResponseModel> getGpsFeeDetail(@RequestBody GpsFeeIdRequestModel requestModel);

    @ApiOperation("新增/修改gps费用")
    @PostMapping(value = "/service/gpsFee/addOrModifyGpsFee")
    Result addOrModifyGpsFee(@RequestBody AddOrModifyGpsFeeRequestModel requestModel);

    @ApiOperation("导出gps费用")
    @PostMapping(value = "/service/gpsFee/exportGpsFee")
    Result<List<SearchGpsFeeListResponseModel>> exportGpsFee(@RequestBody SearchGpsFeeListRequestModel requestModel);

    @ApiOperation("终止gps费用（修改终止时间）")
    @PostMapping(value = "/service/gpsFee/terminationGpsFee")
    Result terminationGpsFee(@RequestBody TerminationGpsFeeRequestModel requestModel);

    @ApiOperation("查询gps费用操作记录")
    @PostMapping(value = "/service/gpsFee/getGpsFeeRecords")
    Result<List<GpsFeeRecordsListResponseModel>> getGpsFeeRecords(@RequestBody GpsFeeIdRequestModel requestModel);

    @ApiOperation("查询gps费用扣减历史")
    @PostMapping(value = "/service/gpsFee/getGpsFeeDeductingHistory")
    Result<GetDeductingHistoryByGpsFeeIdResponseModel> getGpsFeeDeductingHistory(@RequestBody GpsFeeIdRequestModel requestModel);
}
