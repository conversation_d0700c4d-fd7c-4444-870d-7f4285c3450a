package com.logistics.appapi.controller.bankcard.mapping;

import com.logistics.appapi.base.constant.ConfigKeyConstant;
import com.logistics.appapi.client.driveraccount.response.DriverAccountDetailResponseModel;
import com.logistics.appapi.controller.bankcard.response.BankCardDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/29
 */
public class BankCardDetailMapping extends MapperMapping<DriverAccountDetailResponseModel, BankCardDetailResponseDto> {

	private final ConfigKeyConstant configKeyConstant;

	private final Map<String, String> imageMap;

	public BankCardDetailMapping(ConfigKeyConstant configKeyConstant, Map<String, String> imageMap) {
		this.configKeyConstant = configKeyConstant;
		this.imageMap = imageMap;
	}

	@Override
	public void configure() {
		DriverAccountDetailResponseModel source = getSource();
		BankCardDetailResponseDto destination = getDestination();

		//替换图片正式访问路径
		List<String> bankAccountImages = source.getBankAccountImages();
		List<String> imageList = null;
		if (ListUtils.isNotEmpty(bankAccountImages)) {
			imageList = new ArrayList<>();
			for (String bankAccountImage : bankAccountImages) {
				imageList.add(configKeyConstant.fileAccessAddress + imageMap.get(bankAccountImage));
			}
		}
		destination.setBankAccountImage(imageList);
	}
}
