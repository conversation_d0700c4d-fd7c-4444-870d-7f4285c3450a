package com.logistics.tms.controller.dispatch;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.dispatch.DispatchBiz;
import com.logistics.tms.biz.shippingfreight.ShippingFreightBiz;
import com.logistics.tms.biz.shippingorder.ShippingOrderBiz;
import com.logistics.tms.controller.dispatch.request.*;
import com.logistics.tms.controller.dispatch.response.*;
import com.logistics.tms.controller.dispatch.request.SearchCanJoinShippingOrderRequestModel;
import com.logistics.tms.controller.dispatch.request.SearchSpecialDispatchIfMatchFreightReqModel;
import com.logistics.tms.controller.dispatch.response.SearchCanJoinShippingOrderRespModel;
import com.logistics.tms.controller.dispatch.response.SearchSpecialDispatchIfMatchFreightRespModel;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author: dongya.li
 * @Date: 2019/9/12 13:25
 * @Description:
 */
@RestController
@RequestMapping(value = "/service/dispatch")
@Slf4j
public class DispatchController {

    @Autowired
    private DispatchBiz dispatchBiz;
    @Autowired
    private ShippingOrderBiz shippingOrderBiz;
    @Autowired
    private ShippingFreightBiz shippingFreightBiz;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 调度车辆-搜索司机&车牌
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "调度车辆-搜索司机&车牌", notes = "")
    @PostMapping(value = "/searchDriverAndVehicle")
    public Result<List<DriverAndVehicleSearchResponseModel>> searchDriverAndVehicle(@RequestBody DriverAndVehicleSearchRequestModel requestModel) {
        return Result.success(dispatchBiz.getDriverAndVehicleByVehicleNumber(requestModel));
    }

    /**
     * 调度车辆-调度车辆完成调度查看详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "调度车辆-调度车辆完成调度查看详情")
    @PostMapping(value = "/getDispatchDetail")
    public Result<List<DemandOrderDispatchResponseModel>> getDispatchDetail(@RequestBody DemandOrderDispatchRequestModel requestModel) {
        return Result.success(dispatchBiz.getDispatchDetail(requestModel));
    }

    /**
     * 调度车辆-完成调度
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "调度车辆-完成调度")
    @PostMapping(value = "/saveCompleteDispatch")
    public Result<Boolean> saveCompleteDispatch(@RequestBody CompleteDemandOrderRequestModel requestModel) {
        dispatchBiz.saveCompleteDispatch(requestModel);
        return Result.success(true);
    }
    /**
     * 调度车辆-分页搜索司机
     * @param
     * @return
     */
    @ApiOperation(value = "调度车辆-分页搜索司机")
    @PostMapping(value = "/searchDriver")
    public Result<PageInfo<DriverSearchResponseModel>> searchDriver(@RequestBody DriverSearchRequestModel requestModel) {
        return Result.success(dispatchBiz.searchDriver(requestModel));
    }

    /**
     * 调度车辆-分页搜索车辆
     * @param
     * @return
     */
    @ApiOperation(value = "调度车辆-分页搜索车辆")
    @PostMapping(value = "/searchVehicle")
    public Result<PageInfo<VehicleSearchResponseModel>> searchVehicle(@RequestBody VehicleSearchRequestModel requestModel) {
        return Result.success(dispatchBiz.searchVehicle(requestModel));
    }

    @ApiOperation(value = "调度车辆-确认调度")
    @PostMapping(value = "/dispatchVehicle")
    public Result<Boolean> dispatchVehicle(@RequestBody DispatchRequestModel requestModel) {
        RLock[] rLocks = new RLock[requestModel.getVehicleRequestModels().size()];
        for (int i = 0; i < requestModel.getVehicleRequestModels().size(); i++) {
            RLock lock = redissonClient.getLock(CommonConstant.DISPATCH_CAR_LOCK + requestModel.getVehicleRequestModels().get(i).getDemandOrderId());
            rLocks[i] = lock;
        }
        // 批量加锁
        RLock multiLock = redissonClient.getMultiLock(rLocks);
        boolean locked = false;
        try {
            locked = multiLock.tryLock(10L, 20L, TimeUnit.SECONDS);
            if (locked) {
                log.info("加锁成功，调度车辆开始");
                dispatchBiz.dispatchVehicle(requestModel);
            } else {
                log.warn("获取锁失败，请重试");
                throw new BizException("请重试调度车辆");
            }
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        } finally {
            if (locked) {
                try {
                    multiLock.unlock();
                    log.info("调度车辆完成，释放锁");
                } catch (IllegalMonitorStateException e) {
                    log.error("释放锁失败：部分锁未被当前线程持有", e);
                } catch (Exception e) {
                    log.error("释放锁失败", e);
                }
            }
        }
        return Result.success(true);
    }

    /**
     * 零担调度详情
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/specialDispatchDetail")
    public Result<DemandOrderSpecialDispatchDetailResponseModel> specialDispatchDetail(@RequestBody DemandOrderSpecialDispatchDetailRequestModel requestModel) {
        return Result.success(dispatchBiz.specialDispatchDetail(requestModel));
    }

    /**
     * 零担调度车辆
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/specialDispatchVehicle")
    public Result<Boolean> specialDispatchVehicle(@RequestBody SpecialDispatchVehicleRequestModel requestModel) {
        dispatchBiz.specialDispatchVehicle(requestModel);
        return Result.success(true);
    }


    /**
     * 零担调度查询是否有匹配的串点和车长费用 2.42
     */
    @PostMapping(value = "/searchSpecialDispatchIfMatchFreight")
    public Result<SearchSpecialDispatchIfMatchFreightRespModel> searchSpecialDispatchIfMatchFreight(@RequestBody @Valid SearchSpecialDispatchIfMatchFreightReqModel requestModel) {
        return Result.success(shippingFreightBiz.searchSpecialDispatchIfMatchFreight(requestModel));
    }


    /**
     * 零担调度 根据车牌号查询可加入的零担单号 2.42
     */
    @PostMapping(value = "/searchCanJoinShippingOrder")
    public Result<List<SearchCanJoinShippingOrderRespModel>> searchCanJoinShippingOrder(@RequestBody @Valid SearchCanJoinShippingOrderRequestModel requestModel) {
        return Result.success(shippingOrderBiz.searchCanJoinShippingOrder(requestModel));
    }

    @ApiOperation(value = "补单2-关联调度单信息 v2.6.8", tags = "2.6.8")
    @PostMapping(value = "/driverApplet/carrierOrder/associateExtDemandOrder")
    public Result<AssociateExtDemandOrderRespModel> associateExtDemandOrder(@RequestBody @Valid AssociateExtDemandOrderReqModel requestModel) {
        return Result.success(dispatchBiz.associateExtDemandOrder(requestModel));
    }

}
