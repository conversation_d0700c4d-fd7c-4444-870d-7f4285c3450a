package com.logistics.tms.controller.driversafemeeting.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/11/4 9:26
 */
@Data
public class DriverSafeMeetingDetailResponseModel {
    @ApiModelProperty("学习例会关系id")
    private Long safeMeetingRelationId;
    @ApiModelProperty("学习例会id")
    private Long safeMeetingId;
    @ApiModelProperty("学习状态：0未学习，1已学习")
    private Integer status;
    @ApiModelProperty(value = "学习月份")
    private String period;
    @ApiModelProperty("驾驶员姓名")
    private String staffName;
    @ApiModelProperty("驾驶员手机号")
    private String staffMobile;
    @ApiModelProperty("人员机构 1 自主 2外部 3 自营")
    private Integer staffProperty;
    @ApiModelProperty(value = "学习标题")
    private String title;
    @ApiModelProperty(value = "学习时间")
    private Date studyTime;
    @ApiModelProperty(value = "签字时间")
    private Date signTime;
    @ApiModelProperty(value = "驾驶员图片")
    private String staffDriverImageUrl;
    @ApiModelProperty(value = "签字图片")
    private String signImageUrl;
}
