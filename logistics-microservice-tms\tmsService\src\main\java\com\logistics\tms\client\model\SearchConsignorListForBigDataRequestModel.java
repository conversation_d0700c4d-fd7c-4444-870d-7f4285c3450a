package com.logistics.tms.client.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/9/1 16:17
 */
@Data
public class SearchConsignorListForBigDataRequestModel {
    @ApiModelProperty("当前经度")
    private String longitude;

    @ApiModelProperty("当前纬度")
    private String latitude;

    @ApiModelProperty("支持根据发货仓库、发货省市区、发货详细地址，发货联系人姓名以及联系方式模糊查询发货地址")
    private String consignorCondition;
}
