package com.logistics.tms.biz.vehiclesettlement

import com.logistics.tms.controller.vehiclesettlement.response.DriverReconciliationConfirmDetailResponseModel
import com.logistics.tms.controller.vehiclesettlement.request.DriverReconciliationConfirmRequestModel
import com.logistics.tms.controller.vehiclesettlement.response.DriverReconciliationDetailResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.DriverReconciliationListCountResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.GetOilFilledByVehicleIdResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleTireByVehicleIdResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.ReconciliationBillingRecordsResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.ReconciliationCarrierOrderDetailResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.ReconciliationCarrierOrderListResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.ReconciliationOilFilledDetailResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.ReconciliationTireDetailResponseModel
import com.logistics.tms.controller.vehiclesettlement.request.SearchDriverReconciliationListRequestModel
import com.logistics.tms.controller.vehiclesettlement.response.SearchDriverReconciliationListResponseModel
import com.logistics.tms.controller.vehiclesettlement.request.VehicleSettlementIdRequestModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TVehicleSettlementDriverRelation
import com.logistics.tms.entity.TVehicleSettlementPayment
import com.logistics.tms.entity.TVehicleSettlementRelation
import com.logistics.tms.entity.TVehicleTireNo
import com.logistics.tms.mapper.TCarrierOrderMapper
import com.logistics.tms.mapper.TOilFilledMapper
import com.logistics.tms.mapper.TVehicleSettlementDriverRelationMapper
import com.logistics.tms.mapper.TVehicleSettlementEventsMapper
import com.logistics.tms.mapper.TVehicleSettlementMapper
import com.logistics.tms.mapper.TVehicleSettlementPaymentMapper
import com.logistics.tms.mapper.TVehicleSettlementRelationMapper
import com.logistics.tms.mapper.TVehicleTireMapper
import com.logistics.tms.mapper.TVehicleTireNoMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class VehicleSettlementAppletBizTest extends Specification {
    @Mock
    TVehicleSettlementMapper tVehicleSettlementMapper
    @Mock
    TVehicleSettlementRelationMapper tVehicleSettlementRelationMapper
    @Mock
    TVehicleSettlementPaymentMapper tVehicleSettlementPaymentMapper
    @Mock
    TVehicleSettlementDriverRelationMapper tVehicleSettlementDriverRelationMapper
    @Mock
    TCarrierOrderMapper tCarrierOrderMapper
    @Mock
    TVehicleTireMapper tVehicleTireMapper
    @Mock
    TOilFilledMapper tOilFilledMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TVehicleSettlementEventsMapper tVehicleSettlementEventsMapper
    @Mock
    TVehicleTireNoMapper tVehicleTireNoMapper
    @InjectMocks
    VehicleSettlementAppletBiz vehicleSettlementAppletBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Driver Reconciliation List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleSettlementMapper.searchDriverReconciliationList(any())).thenReturn([new SearchDriverReconciliationListResponseModel()])
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)

        expect:
        vehicleSettlementAppletBiz.searchDriverReconciliationList(requestModel) == expectedResult

        where:
        requestModel                                     || expectedResult
        new SearchDriverReconciliationListRequestModel() || null
    }

    @Unroll
    def "driver Reconciliation List Count where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleSettlementMapper.driverReconciliationListCount(any())).thenReturn(new DriverReconciliationListCountResponseModel())
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)

        expect:
        vehicleSettlementAppletBiz.driverReconciliationListCount(requestModel) == expectedResult

        where:
        requestModel                                     || expectedResult
        new SearchDriverReconciliationListRequestModel() || new DriverReconciliationListCountResponseModel()
    }

    @Unroll
    def "driver Reconciliation Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleSettlementMapper.driverReconciliationDetail(anyLong(), anyLong())).thenReturn(new DriverReconciliationDetailResponseModel())
        when(tVehicleSettlementRelationMapper.getByVehicleSettlementId(anyLong())).thenReturn([new TVehicleSettlementRelation(objectType: 0, objectId: 1l)])
        when(tVehicleSettlementPaymentMapper.getByVehicleSettlementId(anyLong())).thenReturn([new TVehicleSettlementPayment(payFee: 0 as BigDecimal)])
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)
        when(tVehicleTireNoMapper.getByTiredIds(anyString())).thenReturn([new TVehicleTireNo(amount: 0)])

        expect:
        vehicleSettlementAppletBiz.driverReconciliationDetail(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new VehicleSettlementIdRequestModel() || new DriverReconciliationDetailResponseModel()
    }

    @Unroll
    def "driver Reconciliation Confirm where requestModel=#requestModel"() {
        given:
        when(tVehicleSettlementDriverRelationMapper.getByVehicleSettlementIdDriverId(anyLong(), anyLong())).thenReturn(new TVehicleSettlementDriverRelation(vehicleSettlementId: 1l, status: 0, commitImageUrl: "commitImageUrl", settlementReasonRemark: "settlementReasonRemark", confirmTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 41).getTime()))
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)

        expect:
        vehicleSettlementAppletBiz.driverReconciliationConfirm(requestModel)
        assert expectedResult == false

        where:
        requestModel                                  || expectedResult
        new DriverReconciliationConfirmRequestModel() || true
    }

    @Unroll
    def "driver Reconciliation Confirm Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleSettlementMapper.driverReconciliationConfirmDetail(anyLong(), anyLong())).thenReturn(new DriverReconciliationConfirmDetailResponseModel())
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)

        expect:
        vehicleSettlementAppletBiz.driverReconciliationConfirmDetail(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new VehicleSettlementIdRequestModel() || new DriverReconciliationConfirmDetailResponseModel()
    }

    @Unroll
    def "driver Reconciliation Carrier Order where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleSettlementMapper.driverReconciliationCarrierOrder(anyLong(), anyLong())).thenReturn(new ReconciliationCarrierOrderDetailResponseModel())
        when(tCarrierOrderMapper.getDriverReconciliationCarrierOrder(anyString())).thenReturn([new ReconciliationCarrierOrderListResponseModel()])
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)

        expect:
        vehicleSettlementAppletBiz.driverReconciliationCarrierOrder(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new VehicleSettlementIdRequestModel() || new ReconciliationCarrierOrderDetailResponseModel()
    }

    @Unroll
    def "driver Reconciliation Billing Records where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleSettlementPaymentMapper.driverReconciliationBillingRecords(anyLong(), anyLong())).thenReturn([new ReconciliationBillingRecordsResponseModel()])
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)

        expect:
        vehicleSettlementAppletBiz.driverReconciliationBillingRecords(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new VehicleSettlementIdRequestModel() || [new ReconciliationBillingRecordsResponseModel()]
    }

    @Unroll
    def "driver Reconciliation Tire where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleSettlementMapper.driverReconciliationTire(anyLong(), anyLong())).thenReturn(new ReconciliationTireDetailResponseModel())
        when(tVehicleTireMapper.getVehicleTireByIdsForSettlement(anyString())).thenReturn([new GetVehicleTireByVehicleIdResponseModel()])
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)

        expect:
        vehicleSettlementAppletBiz.driverReconciliationTire(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new VehicleSettlementIdRequestModel() || new ReconciliationTireDetailResponseModel()
    }

    @Unroll
    def "driver Reconciliation Oil Filled where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleSettlementMapper.driverReconciliationOilFilled(anyLong(), anyLong())).thenReturn(new ReconciliationOilFilledDetailResponseModel())
        when(tOilFilledMapper.getOilFilledByIdsForSettlement(anyString())).thenReturn([new GetOilFilledByVehicleIdResponseModel()])
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)

        expect:
        vehicleSettlementAppletBiz.driverReconciliationOilFilled(requestModel) == expectedResult

        where:
        requestModel                                                                              || expectedResult
        new VehicleSettlementIdRequestModel() || new ReconciliationOilFilledDetailResponseModel()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme