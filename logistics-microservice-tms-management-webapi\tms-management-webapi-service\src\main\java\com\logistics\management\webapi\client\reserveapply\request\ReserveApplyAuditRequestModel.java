package com.logistics.management.webapi.client.reserveapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ReserveApplyAuditRequestModel {

    @ApiModelProperty(value = "申请记录id", required = true)
    private Long applyId;

    @ApiModelProperty(value = "操作人员类型 1:业务审核 2:财务审核", required = true)
    private Integer auditorType;

    @ApiModelProperty(value = "操作类型 1:通过 2:驳回", required = true)
    private Integer auditResult;

    @ApiModelProperty(value = "备用金余额")
    private BigDecimal reserveBalance;

    @ApiModelProperty(value = "审核备注")
    private String remark;

    @ApiModelProperty(value = "业务批准金额", required = true)
    private BigDecimal approveAmount;

}
