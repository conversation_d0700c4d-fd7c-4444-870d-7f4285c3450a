package com.logistics.tms.mapper;

import com.logistics.tms.biz.attendancerecord.model.AttendanceStatisticalModel;
import com.logistics.tms.controller.attendance.request.AttendanceHistoryListRequestModel;
import com.logistics.tms.controller.attendance.request.SearchAttendanceListRequestModel;
import com.logistics.tms.controller.attendance.response.AttendanceDetailResponseModel;
import com.logistics.tms.controller.attendance.response.AttendanceHistoryItemResponseModel;
import com.logistics.tms.controller.attendance.response.SearchAttendanceListResponseModel;
import com.logistics.tms.entity.TAttendanceRecord;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* Created by Mybatis Generator on 2022/11/10
*/
@Mapper
public interface TAttendanceRecordMapper extends BaseMapper<TAttendanceRecord> {

    TAttendanceRecord selectByStaffIdAndDate(@Param("staffId") Long staffId, @Param("date") Date date);

    List<AttendanceHistoryItemResponseModel> selectHistoryByStaffAndAttendanceDate(@Param("staffId") Long staffId, @Param("params") AttendanceHistoryListRequestModel requestModel);

    AttendanceStatisticalModel selectStatistical(@Param("staffId") Long staffId, @Param("attendanceDate") String date);

    int insertOrUpdate(TAttendanceRecord tAttendanceRecord);

    TAttendanceRecord selectOneByIdAndStaffId(@Param("id")Long id, @Param("staffId") Long staffId);

    Long countByIdAndStaffId(@Param("id")Long id,@Param("staffId")Long staffId);

    List<SearchAttendanceListResponseModel> selectSearchAttendanceList(@Param("params") SearchAttendanceListRequestModel requestModel);

    AttendanceDetailResponseModel selectOneDetailById(@Param("id")Long id);

    TAttendanceRecord selectOneById(@Param("id")Long id);

	int updateById(@Param("updated")TAttendanceRecord updated,@Param("id")Long id);

}