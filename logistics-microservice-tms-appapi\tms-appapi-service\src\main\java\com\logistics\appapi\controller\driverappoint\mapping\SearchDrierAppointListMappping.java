package com.logistics.appapi.controller.driverappoint.mapping;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.base.enums.CompanyTypeEnum;
import com.logistics.appapi.client.driverappoint.response.SearchAppointCountResponseModel;
import com.logistics.appapi.client.driverappoint.response.SearchAppointResponseModel;
import com.logistics.appapi.controller.driverappoint.response.SearchDrierAppointCountResponseDto;
import com.logistics.appapi.controller.driverappoint.response.SearchDrierAppointListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;

import java.util.ArrayList;
import java.util.List;

public class SearchDrierAppointListMappping extends MapperMapping<SearchAppointCountResponseModel, SearchDrierAppointCountResponseDto> {
    @Override
    public void configure() {
        SearchAppointCountResponseModel source = getSource();
        SearchDrierAppointCountResponseDto destination = getDestination();
        destination.setAppointAmountTotal(source.getAppointAmountTotal().stripTrailingZeros().toPlainString());
        PageInfo pageInfo = source.getModelList();
        List<SearchAppointResponseModel> list = pageInfo.getList();
        List<SearchDrierAppointListResponseDto> dtoList = new ArrayList<>();
        if(ListUtils.isNotEmpty(list)){
            list.forEach(o->{
                SearchDrierAppointListResponseDto dto = MapperUtils.mapper(o, SearchDrierAppointListResponseDto.class);
                if(CompanyTypeEnum.COMPANY.getKey().equals(o.getBusinessType())){
                    dto.setCustomerName(o.getCustomerName());
                }else if(CompanyTypeEnum.PERSONAL.getKey().equals(o.getBusinessType())){
                    dto.setCustomerName(o.getCustomerUserName()+" "+o.getCustomerUserMobile());
                }
                dto.setGoodsAmountTotal(o.getGoodsAmountTotal().stripTrailingZeros().toPlainString());
                dtoList.add(dto);
            });
            pageInfo.setList(dtoList);
            destination.setModelList(pageInfo);
        }
    }
}
