package com.logistics.tms.controller.freightconfig.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CarrierFreightConfigListResponseModel {

    @ApiModelProperty(value = "运价配置Id")
    private Long freightConfigId;

    @ApiModelProperty(value = "价格类型; 1: 固定路线; 2: 区域设置; 3: 距离阶梯")
    private Integer configType;

    @ApiModelProperty(value = "需求类型")
    private String entrustType;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    @ApiModelProperty(value = "操作人")
    private String lastModifiedBy;
    
    @ApiModelProperty(value = "操作时间")
    private Date lastModifiedTime;
}
