package com.logistics.tms.api.impl.bank;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.bank.BankServiceApi;
import com.logistics.tms.api.feign.bank.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.bank.BankBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/7/10 16:54
 */
@Slf4j
@RestController
public class BankServiceApiImpl implements BankServiceApi{

    @Autowired
    private BankBiz bankBiz;

    /**
     * 银行列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<SearchBankResponseModel>> searchBankList(@RequestBody SearchBankRequestModel requestModel) {
        return Result.success(bankBiz.searchBankList(requestModel));
    }

    /**
     * 银行新增修改
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> saveOrModifyBank(@RequestBody SaveOrModifyBankRequestModel requestModel) {
        bankBiz.saveOrModifyBank(requestModel);
        return Result.success(true);
    }

    /**
     * 查看详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<BankDetailResponseModel> getDetail(@RequestBody BankDetailRequestModel requestModel) {
        return Result.success(bankBiz.getDetail(requestModel));
    }

    /**
     * 启用/禁用银行信息
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> enableOrDisable(@RequestBody EnableBankRequestModel requestModel) {
        bankBiz.enableOrDisable(requestModel);
        return Result.success(true);
    }

    /**
     * 导出
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchBankResponseModel>> export(@RequestBody SearchBankRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        PageInfo<SearchBankResponseModel> pageInfo = bankBiz.searchBankList(requestModel);
        return Result.success(pageInfo.getList());
    }

    /**
     * 导入
     * @param requestModel
     * @return
     */
    @Override
    public Result<ImportBankResponseModel> importBank(@RequestBody ImportBankRequestModel requestModel) {
        return Result.success(bankBiz.batchImportBankInfo(requestModel));
    }

    /**
     * 根据名称模糊匹配银行信息
     * @param requestModel
     * @return
     */
    @Override
    public Result<List<FuzzyQueryBankListResponseModel>> fuzzyQueryBank(@RequestBody FuzzyQueryBankRequestModel requestModel) {
        return Result.success(bankBiz.fuzzyQueryInsuranceCompany(requestModel));
    }
}
