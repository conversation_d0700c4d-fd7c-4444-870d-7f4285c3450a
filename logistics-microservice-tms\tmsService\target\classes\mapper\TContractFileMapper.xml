<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TContractFileMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TContractFile" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="contract_id" property="contractId" jdbcType="BIGINT" />
    <result column="contract_file_path" property="contractFilePath" jdbcType="VARCHAR" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, contract_id, contract_file_path, valid, created_by, created_time, last_modified_by, 
    last_modified_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_contract_file
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_contract_file
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TContractFile" >
    insert into t_contract_file (id, contract_id, contract_file_path, 
      valid, created_by, created_time, 
      last_modified_by, last_modified_time)
    values (#{id,jdbcType=BIGINT}, #{contractId,jdbcType=BIGINT}, #{contractFilePath,jdbcType=VARCHAR}, 
      #{valid,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TContractFile" useGeneratedKeys="true" keyProperty="id">
    insert into t_contract_file
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="contractId != null" >
        contract_id,
      </if>
      <if test="contractFilePath != null" >
        contract_file_path,
      </if>
      <if test="valid != null" >
        valid,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="contractId != null" >
        #{contractId,jdbcType=BIGINT},
      </if>
      <if test="contractFilePath != null" >
        #{contractFilePath,jdbcType=VARCHAR},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TContractFile" >
    update t_contract_file
    <set >
      <if test="contractId != null" >
        contract_id = #{contractId,jdbcType=BIGINT},
      </if>
      <if test="contractFilePath != null" >
        contract_file_path = #{contractFilePath,jdbcType=VARCHAR},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TContractFile" >
    update t_contract_file
    set contract_id = #{contractId,jdbcType=BIGINT},
      contract_file_path = #{contractFilePath,jdbcType=VARCHAR},
      valid = #{valid,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>