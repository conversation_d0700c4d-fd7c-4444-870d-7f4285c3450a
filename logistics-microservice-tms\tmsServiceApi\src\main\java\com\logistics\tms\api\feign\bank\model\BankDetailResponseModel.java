package com.logistics.tms.api.feign.bank.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/7/10 14:07
 */
@Data
public class BankDetailResponseModel {
    @ApiModelProperty("银行名称ID")
    private Long bankId;
    @ApiModelProperty("银行名称")
    private String bankName;
    @ApiModelProperty("支行名称")
    private String branchName;
    @ApiModelProperty("是否禁用 0 禁用 1 启用 ")
    private Integer enable;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("添加人")
    private String createdBy;
    @ApiModelProperty("创建时间")
    private Date createdTime;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;
}
