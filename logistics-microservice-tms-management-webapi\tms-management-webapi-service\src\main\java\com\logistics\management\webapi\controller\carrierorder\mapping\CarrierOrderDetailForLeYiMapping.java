package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.client.carrierorder.response.*;
import com.logistics.management.webapi.controller.carrierorder.response.*;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Optional;
import java.util.stream.Collectors;


public class CarrierOrderDetailForLeYiMapping extends MapperMapping<CarrierOrderDetailForLeYiResponseModel, CarrierOrderDetailForLeYiResponseDto> {
    @Override
    public void configure() {
        CarrierOrderDetailForLeYiResponseModel source = getSource();
        CarrierOrderDetailForLeYiResponseDto destination = getDestination();

        if (CommonConstant.INTEGER_ONE.equals(source.getIfCancel()) ) {
            destination.setStatus(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getKey().toString());
            destination.setStatusLabel(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getValue());
        }else if (CommonConstant.INTEGER_ONE.equals(source.getIfEmpty()) ) {
            destination.setStatus(CarrierOrderStatusEnum.EMPTY.getKey().toString());
            destination.setStatusLabel(CarrierOrderStatusEnum.EMPTY.getValue());
        }else{
            destination.setStatusLabel(CarrierOrderStatusEnum.getEnum(source.getStatus()).getValue());
        }
        if (source.getDispatchTime() != null) {
            destination.setDispatchTime(DateUtils.dateToString(source.getDispatchTime(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
        }
        destination.setGoodsUnit(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());

        //项目标签
        if (StringUtils.isNotBlank(source.getProjectLabel())){
            String projectLabel = Arrays.stream(source.getProjectLabel().split(","))
                    .map(s -> ProjectLabelEnum.getEnum(Integer.valueOf(s))
                            .getValue()).collect(Collectors.joining("、"));
            destination.setProjectLabel(projectLabel);
        }

        //货主
        String companyEntrustName = source.getEntrustCompany();
        if (EntrustTypeEnum.BOOKING.getKey().equals(source.getEntrustType())){
            companyEntrustName = CommonConstant.LEYI_POINTS_FOR_LOGISTICS;
        }
        destination.setEntrustCompany(companyEntrustName);

        CarrierOrderDetailBasicInfoForLeYiModel carrierOrderDetailBasicInfoModel = source.getCarrierOrderDetailBasicInfo();
        if (carrierOrderDetailBasicInfoModel != null) {
            CarrierOrderDetailBasicInfoForLeYiDto basicInfoDto = new CarrierOrderDetailBasicInfoForLeYiDto();
            basicInfoDto.setConsignor((carrierOrderDetailBasicInfoModel.getConsignorName() == null ? "" : carrierOrderDetailBasicInfoModel.getConsignorName()) + "  " + (carrierOrderDetailBasicInfoModel.getConsignorMobile() == null ? "" : carrierOrderDetailBasicInfoModel.getConsignorMobile()));
            basicInfoDto.setReceiver((carrierOrderDetailBasicInfoModel.getReceiverName() == null ? "" : carrierOrderDetailBasicInfoModel.getReceiverName()) + "  " + (carrierOrderDetailBasicInfoModel.getReceiverMobile() == null ? "" : carrierOrderDetailBasicInfoModel.getReceiverMobile()));
            if (carrierOrderDetailBasicInfoModel.getLoadTime() != null) {
                basicInfoDto.setLoadTime(DateUtils.dateToString(carrierOrderDetailBasicInfoModel.getLoadTime(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
            }
            if (carrierOrderDetailBasicInfoModel.getUnloadTime() != null) {
                basicInfoDto.setUnloadTime(DateUtils.dateToString(carrierOrderDetailBasicInfoModel.getUnloadTime(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
            }
            basicInfoDto.setLoadAddress(carrierOrderDetailBasicInfoModel.getLoadProvinceName() + carrierOrderDetailBasicInfoModel.getLoadCityName() + carrierOrderDetailBasicInfoModel.getLoadAreaName());
            basicInfoDto.setLoadAddress(StringUtils.isEmpty(carrierOrderDetailBasicInfoModel.getLoadWarehouse()) ? basicInfoDto.getLoadAddress() : basicInfoDto.getLoadAddress() + " 【" + carrierOrderDetailBasicInfoModel.getLoadWarehouse() + "】 ");
            basicInfoDto.setLoadAddress(basicInfoDto.getLoadAddress() + carrierOrderDetailBasicInfoModel.getLoadDetailAddress());

            //实际发货地址：触达抬头+地址备注+省市区详细地址
            String terminalHead = "";
            String reachAddressRemark = "";
            String reachAddressDetail = "";
            ReachManagementInfoModel reachManagementInfoModel = source.getReachManagementInfoModel();
            if (reachManagementInfoModel != null) {
                terminalHead = StringUtils.isEmpty(reachManagementInfoModel.getTerminalHead()) ? "" : reachManagementInfoModel.getTerminalHead() + " ";
                reachAddressRemark = StringUtils.isEmpty(reachManagementInfoModel.getReachAddressRemark()) ? "" : reachManagementInfoModel.getReachAddressRemark() + " ";
                reachAddressDetail = reachManagementInfoModel.getReachProvinceName() + reachManagementInfoModel.getReachCityName()
                        + reachManagementInfoModel.getReachAreaName() + reachManagementInfoModel.getReachAddressDetail();
            }
            //提货定位
            String loadLocation;
            if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(source.getEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(source.getEntrustType())){
                loadLocation = reachAddressDetail;
            }else {
                loadLocation = StringUtils.isEmpty(carrierOrderDetailBasicInfoModel.getLoadLocation()) ? "" : carrierOrderDetailBasicInfoModel.getLoadLocation();
            }
            basicInfoDto.setLoadLocation(terminalHead + reachAddressRemark + loadLocation);

            basicInfoDto.setUnloadAddress(carrierOrderDetailBasicInfoModel.getUnloadProvinceName() + carrierOrderDetailBasicInfoModel.getUnloadCityName() + carrierOrderDetailBasicInfoModel.getUnloadAreaName());
            basicInfoDto.setUnloadAddress(StringUtils.isEmpty(carrierOrderDetailBasicInfoModel.getUnloadWarehouse()) ? basicInfoDto.getUnloadAddress() : basicInfoDto.getUnloadAddress() + " 【" + carrierOrderDetailBasicInfoModel.getUnloadWarehouse() + "】 ");
            basicInfoDto.setUnloadAddress(basicInfoDto.getUnloadAddress() + carrierOrderDetailBasicInfoModel.getUnloadDetailAddress());
            basicInfoDto.setUnloadAddressIsAmend(IfRemindEnum.getEnum(carrierOrderDetailBasicInfoModel.getUnloadAddressIsAmend()).getValue());
            basicInfoDto.setUnloadLocation(ConverterUtils.toString(carrierOrderDetailBasicInfoModel.getUnloadLocation()));
            destination.setCarrierOrderDetailBasicInfo(basicInfoDto);

        }
        String unit = GoodsUnitEnum.getEnum(source.getGoodsUnit()).getPriceUnit();
        //货主实际结算数量
        BigDecimal entrustSettlementAmount = BigDecimal.ZERO;
        String entrustSettlementTonnage = "";
        if (SettlementTonnageEnum.LOAD.getKey().equals(source.getEntrustSettlementTonnage())) {
            entrustSettlementAmount = source.getLoadAmount();
            entrustSettlementTonnage = SettlementTonnageEnum.LOAD.getValue();
        } else if (SettlementTonnageEnum.UNLOAD.getKey().equals(source.getEntrustSettlementTonnage())) {
            entrustSettlementAmount = source.getUnloadAmount();
            entrustSettlementTonnage = SettlementTonnageEnum.UNLOAD.getValue();
        } else if (SettlementTonnageEnum.SIGN.getKey().equals(source.getEntrustSettlementTonnage())) {
            entrustSettlementAmount = source.getSignAmount();
            entrustSettlementTonnage = SettlementTonnageEnum.SIGN.getValue();
        } else if (SettlementTonnageEnum.EXPECT.getKey().equals(source.getEntrustSettlementTonnage())) {
            entrustSettlementAmount = source.getExpectAmount();
            entrustSettlementTonnage = SettlementTonnageEnum.EXPECT.getValue();
        }
        if(CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())){
            entrustSettlementAmount = source.getExpectAmount();
            entrustSettlementTonnage = SettlementTonnageEnum.EXPECT.getValue();
        }
        destination.setEntrustSettlementTonnage(entrustSettlementTonnage);

        CarrierOrderDetailFreightFeeInfoModel carrierOrderDetailFreightFeeInfoModel = source.getCarrierOrderDetailFreightFeeInfo();
        CarrierOrderDetailFreightFeeInfoDto freightFeeDto = new CarrierOrderDetailFreightFeeInfoDto();

        if (carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFeeType() != null && carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFeeType() > CommonConstant.INTEGER_ZERO) {
            freightFeeDto.setDispatchFreightFeeType(ConverterUtils.toString(carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFeeType()));
            freightFeeDto.setDispatchFreightFeeTypeLabel(PriceTypeEnum.getEnum(carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFeeType()).getValue());
        }

        //预计货主费用
        BigDecimal exceptContractPrice = carrierOrderDetailFreightFeeInfoModel.getExpectEntrustFreight();
        if (PriceTypeEnum.UNIT_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getExpectEntrustFreightType())) {
            freightFeeDto.setEntrustFreight(ConverterUtils.toString(exceptContractPrice) + unit);
            freightFeeDto.setEntrustFreightTotal(ConverterUtils.toString(exceptContractPrice.multiply(source.getExpectAmount()).setScale(2, BigDecimal.ROUND_HALF_UP)) + CommonConstant.YUAN);
        } else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getExpectEntrustFreightType())) {
            freightFeeDto.setEntrustFreight(ConverterUtils.toString(exceptContractPrice) + CommonConstant.YUAN);
            freightFeeDto.setEntrustFreightTotal(ConverterUtils.toString(exceptContractPrice) + CommonConstant.YUAN);
        }
        freightFeeDto.setEntrustFreightType(PriceTypeEnum.getEnum(carrierOrderDetailFreightFeeInfoModel.getExpectEntrustFreightType()).getValue());

        //实际货主费用
        if (CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(source.getStatus()) || CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())) {
            //乐医单子有签收费用,取签收费用
            BigDecimal signFreightFee = Optional.ofNullable(carrierOrderDetailFreightFeeInfoModel.getSignFreightFee()).orElse(BigDecimal.ZERO);
            if (CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())) {
                if (carrierOrderDetailFreightFeeInfoModel.getEntrustFreightType().equals(PriceTypeEnum.UNIT_PRICE.getKey())) {
                    BigDecimal settleUnitPrice = signFreightFee.divide(source.getExpectAmount(), 2, RoundingMode.HALF_UP);
                    freightFeeDto.setSignEntrustFreight(ConverterUtils.toString(settleUnitPrice) + unit);
                } else if (carrierOrderDetailFreightFeeInfoModel.getEntrustFreightType().equals(PriceTypeEnum.FIXED_PRICE.getKey())) {
                    freightFeeDto.setSignEntrustFreight(ConverterUtils.toString(signFreightFee) + CommonConstant.YUAN);
                }
            } else {
                if (carrierOrderDetailFreightFeeInfoModel.getEntrustFreightType().equals(PriceTypeEnum.UNIT_PRICE.getKey())) {
                    if (entrustSettlementAmount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                        freightFeeDto.setSignEntrustFreight(ConverterUtils.toString(signFreightFee.divide(entrustSettlementAmount, 2, RoundingMode.HALF_UP)) + unit);
                    }
                } else if (carrierOrderDetailFreightFeeInfoModel.getEntrustFreightType().equals(PriceTypeEnum.FIXED_PRICE.getKey())) {
                    freightFeeDto.setSignEntrustFreight(ConverterUtils.toString(signFreightFee) + CommonConstant.YUAN);
                }
            }
            freightFeeDto.setSignEntrustFreightTotal(ConverterUtils.toString(signFreightFee) + CommonConstant.YUAN);
            freightFeeDto.setActualEntrustFreightType(PriceTypeEnum.getEnum(carrierOrderDetailFreightFeeInfoModel.getEntrustFreightType()).getValue());

            //已生成结算数据
            BigDecimal entrustSettlementCostTotal = carrierOrderDetailFreightFeeInfoModel.getEntrustSettlementCostTotal();
            if(entrustSettlementCostTotal != null && carrierOrderDetailFreightFeeInfoModel.getEntrustPriceType() != null) {
                freightFeeDto.setActualEntrustFreightType(PriceTypeEnum.getEnum(carrierOrderDetailFreightFeeInfoModel.getEntrustPriceType()).getValue());
                if (PriceTypeEnum.UNIT_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getEntrustPriceType())) {
                    if (entrustSettlementAmount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                        BigDecimal settleUnitPrice = entrustSettlementCostTotal.divide(entrustSettlementAmount, 2, RoundingMode.HALF_UP);
                        freightFeeDto.setSignEntrustFreight(ConverterUtils.toString(settleUnitPrice) + unit);
                    }
                } else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getEntrustPriceType())) {
                    freightFeeDto.setSignEntrustFreight(ConverterUtils.toString(entrustSettlementCostTotal) + CommonConstant.YUAN);
                }
                freightFeeDto.setSignEntrustFreightTotal(ConverterUtils.toString(entrustSettlementCostTotal) + CommonConstant.YUAN);
            }
        }

        //实际结算数量
        BigDecimal carrierSettlementAmount = BigDecimal.ZERO;
        String carrierSettlementTonnage = "";
        if (SettlementTonnageEnum.LOAD.getKey().equals(source.getCarrierSettlementTonnage())) {
            carrierSettlementAmount = source.getLoadAmount();
            carrierSettlementTonnage = SettlementTonnageEnum.LOAD.getValue();
        } else if (SettlementTonnageEnum.UNLOAD.getKey().equals(source.getCarrierSettlementTonnage())) {
            carrierSettlementAmount = source.getUnloadAmount();
            carrierSettlementTonnage = SettlementTonnageEnum.UNLOAD.getValue();
        } else if (SettlementTonnageEnum.SIGN.getKey().equals(source.getCarrierSettlementTonnage())) {
            carrierSettlementAmount = source.getSignAmount();
            carrierSettlementTonnage = SettlementTonnageEnum.SIGN.getValue();
        } else if (SettlementTonnageEnum.EXPECT.getKey().equals(source.getCarrierSettlementTonnage())) {
            carrierSettlementAmount = source.getExpectAmount();
            carrierSettlementTonnage = SettlementTonnageEnum.EXPECT.getValue();
        }
        if(CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())){
            carrierSettlementAmount = source.getExpectAmount();
            carrierSettlementTonnage = SettlementTonnageEnum.EXPECT.getValue();
        }
        destination.setCarrierSettlementTonnage(carrierSettlementTonnage);

        destination.setIsOurCompany(String.valueOf(source.getLevel()));

        //车主费用
        BigDecimal carrierFreightFee = carrierOrderDetailFreightFeeInfoModel.getCarrierFreight();

        //预计车主费用 单价：单价*预提数量；一口价：预提/一口价*总委托
        freightFeeDto.setCarrierFreightType(PriceTypeEnum.getEnum(carrierOrderDetailFreightFeeInfoModel.getCarrierFreightType()).getValue());
        if (PriceTypeEnum.UNIT_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getCarrierFreightType())) {
            freightFeeDto.setCarrierFreight(carrierFreightFee + unit);
            freightFeeDto.setCarrierFreightTotal(ConverterUtils.toString(source.getExpectAmount().multiply(carrierFreightFee).setScale(2, BigDecimal.ROUND_HALF_UP)) + CommonConstant.YUAN);
        } else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getCarrierFreightType())) {
            freightFeeDto.setCarrierFreight(ConverterUtils.toString(carrierFreightFee.setScale(2, RoundingMode.HALF_UP)) + CommonConstant.YUAN);
            freightFeeDto.setCarrierFreightTotal(ConverterUtils.toString(carrierFreightFee.setScale(2, RoundingMode.HALF_UP)) + CommonConstant.YUAN);
        }

        //实际车主费用 单价：单价*签收数量；一口价：预提/一口价*总委托
        if (CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(source.getStatus()) || CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())) {
            //有结算数据计算实际费用
            if (PriceTypeEnum.UNIT_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getCarrierFreightType())) {
                freightFeeDto.setSignCarrierFreight(ConverterUtils.toString(carrierFreightFee) + unit);
                BigDecimal signCarrierFreightFeeTotal = carrierFreightFee.multiply(carrierSettlementAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                freightFeeDto.setSignCarrierFreightTotal(ConverterUtils.toString(signCarrierFreightFeeTotal.add(carrierOrderDetailFreightFeeInfoModel.getOtherFee())) + CommonConstant.YUAN);
            } else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getCarrierFreightType())) {
                freightFeeDto.setSignCarrierFreight(ConverterUtils.toString(carrierFreightFee) + CommonConstant.YUAN);
                freightFeeDto.setSignCarrierFreightTotal(ConverterUtils.toString(carrierFreightFee.add(carrierOrderDetailFreightFeeInfoModel.getOtherFee())) + CommonConstant.YUAN);
            }
            freightFeeDto.setActualCarrierFreightType(PriceTypeEnum.getEnum(carrierOrderDetailFreightFeeInfoModel.getCarrierFreightType()).getValue());

            //已生成结算数据
            if(carrierOrderDetailFreightFeeInfoModel.getCarrierSettlementCostTotal() != null && carrierOrderDetailFreightFeeInfoModel.getCarrierPriceType() != null){
                BigDecimal carrierSettlementCost = Optional.ofNullable(carrierOrderDetailFreightFeeInfoModel.getCarrierSettlementCostTotal()).orElse(CommonConstant.BIG_DECIMAL_ZERO_NET_ZERO);
                BigDecimal signCarrierFreightFeeTotal = Optional.ofNullable(carrierOrderDetailFreightFeeInfoModel.getCarrierSettlementCostTotal()).orElse(CommonConstant.BIG_DECIMAL_ZERO_NET_ZERO);
                if (PriceTypeEnum.UNIT_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getCarrierPriceType())) {
                    if (carrierSettlementAmount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                        freightFeeDto.setSignCarrierFreight(ConverterUtils.toString(carrierSettlementCost.divide(carrierSettlementAmount, 2, BigDecimal.ROUND_HALF_UP)) + unit);
                    }
                } else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getCarrierPriceType())) {
                    freightFeeDto.setSignCarrierFreight(ConverterUtils.toString(signCarrierFreightFeeTotal) + CommonConstant.YUAN);
                }
                freightFeeDto.setSignCarrierFreightTotal(ConverterUtils.toString(signCarrierFreightFeeTotal.add(carrierOrderDetailFreightFeeInfoModel.getOtherFee())) + CommonConstant.YUAN);
                freightFeeDto.setActualCarrierFreightType(PriceTypeEnum.getEnum(carrierOrderDetailFreightFeeInfoModel.getCarrierPriceType()).getValue());
            }
        }
        //车主临时费用
        freightFeeDto.setCarrierOtherFee(ConverterUtils.toString(carrierOrderDetailFreightFeeInfoModel.getOtherFee()));

        //我司
        if (CommonConstant.INTEGER_ONE.equals(source.getLevel())){
            //预计司机费用
            BigDecimal dispatchFreightFeeTotal = BigDecimal.ZERO;
            Integer dispatchFreightFeeType = carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFeeType();
            BigDecimal dispatchFreightFee = carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFee();
            if (dispatchFreightFeeType.equals(PriceTypeEnum.UNIT_PRICE.getKey())) {
                freightFeeDto.setDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee));
                freightFeeDto.setDispatchFreightFeeUnit(unit);
                dispatchFreightFeeTotal = dispatchFreightFee.multiply(source.getExpectAmount()).setScale(2, BigDecimal.ROUND_HALF_UP);
            } else if (dispatchFreightFeeType.equals(PriceTypeEnum.FIXED_PRICE.getKey())) {
                freightFeeDto.setDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee));
                freightFeeDto.setDispatchFreightFeeUnit(CommonConstant.YUAN);
                dispatchFreightFeeTotal = dispatchFreightFee;
            }
            String adjustFee = ConverterUtils.toString(carrierOrderDetailFreightFeeInfoModel.getAdjustFee());
            if (carrierOrderDetailFreightFeeInfoModel.getAdjustFee().compareTo(CommonConstant.BIG_DECIMAL_ZERO) > CommonConstant.INTEGER_ZERO) {
                adjustFee = "+" + ConverterUtils.toString(carrierOrderDetailFreightFeeInfoModel.getAdjustFee());
            }
            freightFeeDto.setDispatchFreightFeeType(ConverterUtils.toString(dispatchFreightFeeType));
            freightFeeDto.setDispatchFreightFeeTypeLabel(PriceTypeEnum.getEnum(dispatchFreightFeeType).getValue());
            freightFeeDto.setAdjustFee(adjustFee + CommonConstant.YUAN);
            freightFeeDto.setMarkupFee(ConverterUtils.toString(carrierOrderDetailFreightFeeInfoModel.getMarkupFee()) + CommonConstant.YUAN);
            freightFeeDto.setDispatchFreightFeeTotal(ConverterUtils.toString(dispatchFreightFeeTotal.add(carrierOrderDetailFreightFeeInfoModel.getAdjustFee()).add(carrierOrderDetailFreightFeeInfoModel.getMarkupFee())) + CommonConstant.YUAN);

            //实际司机费用=货主费用+临时费用（所以单价时用货主结算数量结算）
            if (CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(source.getStatus()) || CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())) {
                BigDecimal signDispatchFreightFeeTotal = BigDecimal.ZERO;
                if (dispatchFreightFeeType.equals(PriceTypeEnum.UNIT_PRICE.getKey())) {
                    freightFeeDto.setSignDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee) + unit);
                    signDispatchFreightFeeTotal = dispatchFreightFee.multiply(entrustSettlementAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (dispatchFreightFeeType.equals(PriceTypeEnum.FIXED_PRICE.getKey())) {
                    freightFeeDto.setSignDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee) + CommonConstant.YUAN);
                    signDispatchFreightFeeTotal = dispatchFreightFee;
                }
                freightFeeDto.setSignDispatchFreightFeeType(ConverterUtils.toString(dispatchFreightFeeType));
                freightFeeDto.setSignDispatchFreightFeeTypeLabel(PriceTypeEnum.getEnum(dispatchFreightFeeType).getValue());
                freightFeeDto.setSignDispatchFreightFeeTotal(ConverterUtils.toString(signDispatchFreightFeeTotal.add(carrierOrderDetailFreightFeeInfoModel.getOtherFee()).setScale(2,BigDecimal.ROUND_HALF_UP) + CommonConstant.YUAN));
                freightFeeDto.setSignAdjustFee(adjustFee + CommonConstant.YUAN);
                freightFeeDto.setSignMarkupFee(ConverterUtils.toString(carrierOrderDetailFreightFeeInfoModel.getMarkupFee()) + CommonConstant.YUAN);
            }
            //司机临时费用
            freightFeeDto.setDriverOtherFee(ConverterUtils.toString(carrierOrderDetailFreightFeeInfoModel.getOtherFee()) + CommonConstant.YUAN);
        }

        destination.setCarrierOrderDetailFreightFeeInfo(freightFeeDto);

        //货物信息
        if (source.getCarrierOrderDetailGoodsInfo() != null) {
            destination.setCarrierOrderDetailGoodsInfo(new ArrayList<>());
            for (CarrierOrderDetailGoodsInfoModel tmp : source.getCarrierOrderDetailGoodsInfo()) {
                CarrierOrderDetailGoodsInfoDto dto = new CarrierOrderDetailGoodsInfoDto();
                dto.setGoodsName(tmp.getGoodsName());
                dto.setSize(tmp.getGoodsSize());
                dto.setExpectAmount(tmp.getExpectAmount().stripTrailingZeros().toPlainString());
                if (source.getStatus()>CarrierOrderStatusEnum.WAIT_LOAD.getKey()) {
                    dto.setLoadAmount(tmp.getLoadAmount().stripTrailingZeros().toPlainString());
                }
                if (source.getStatus()>CarrierOrderStatusEnum.WAIT_UNLOAD.getKey()) {
                    dto.setUnloadAmount(tmp.getUnloadAmount().stripTrailingZeros().toPlainString());
                }
                if (source.getStatus()>CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey()) {
                    dto.setSignAmount(tmp.getSignAmount().stripTrailingZeros().toPlainString());
                }
                if (source.getGoodsUnit().equals(GoodsUnitEnum.BY_VOLUME.getKey())) {
                    BigDecimal capacity = ConverterUtils.toBigDecimal(tmp.getLength()).multiply(ConverterUtils.toBigDecimal(tmp.getWidth())).multiply(ConverterUtils.toBigDecimal(tmp.getHeight()));
                    dto.setSize(tmp.getLength() + "*" + tmp.getWidth() + "*" + tmp.getHeight() + " " + tmp.getGoodsSize() + "mm");
                    dto.setExpectCapacity(calcCapacity(tmp.getExpectAmount(), capacity));
                    if (source.getStatus()>CarrierOrderStatusEnum.WAIT_LOAD.getKey()) {
                        dto.setLoadCapacity(calcCapacity(tmp.getLoadAmount(), capacity));
                    }
                    if (source.getStatus()>CarrierOrderStatusEnum.WAIT_UNLOAD.getKey()) {
                        dto.setUnloadCapacity(calcCapacity(tmp.getUnloadAmount(), capacity));
                    }
                    if (source.getStatus()>CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey()) {
                        dto.setSignLoadCapacity(calcCapacity(tmp.getSignAmount(), capacity));
                    }
                }
                destination.getCarrierOrderDetailGoodsInfo().add(dto);
            }
        }
        //车辆信息
        if (source.getCarrierOrderDetailVehicleDriverInfo() != null) {
            destination.setCarrierOrderDetailVehicleDriverInfo(new ArrayList<>());
            for (CarrierOrderDetailVehicleDriverInfoModel tmp : source.getCarrierOrderDetailVehicleDriverInfo()) {
                CarrierOrderDetailVehicleDriverInfoDto dto = MapperUtils.mapper(tmp, CarrierOrderDetailVehicleDriverInfoDto.class);
                if (CommonConstant.INTEGER_ONE.equals(tmp.getIfInvalid())) {
                    dto.setStatus(ValidStatusEnum.VALID.getValue());
                } else {
                    if (VehicleHistoryAuditStatusEnum.REJECT.getKey().equals(tmp.getAuditStatus())) {
                        dto.setStatus(VehicleHistoryAuditStatusEnum.REJECT.getValue());
                    } else if (VehicleHistoryAuditStatusEnum.NOT_NEED_AUDIT.getKey().equals(tmp.getAuditStatus()) || VehicleHistoryAuditStatusEnum.AUDIT.getKey().equals(tmp.getAuditStatus())) {
                        dto.setStatus(ValidStatusEnum.INVALID.getValue());
                    } else if (VehicleHistoryAuditStatusEnum.WAIT_AUDIT.getKey().equals(tmp.getAuditStatus())) {
                        dto.setStatus(VehicleHistoryAuditStatusEnum.WAIT_AUDIT.getValue());
                    }
                }
                dto.setDriver((tmp.getDriverName() == null ? "" : tmp.getDriverName()) + " " + (tmp.getDriverMobile() == null ? "" : tmp.getDriverMobile()));
                destination.getCarrierOrderDetailVehicleDriverInfo().add(dto);
            }
        }

    }

    private String calcCapacity(BigDecimal amount, BigDecimal capacity) {
        BigDecimal sumAmount = amount.multiply(capacity).divide(new BigDecimal(1000).pow(3),3, BigDecimal.ROUND_HALF_UP);
        return sumAmount.stripTrailingZeros().toPlainString();
    }

}
