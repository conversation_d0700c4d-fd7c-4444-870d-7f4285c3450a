package com.logistics.tms.biz.carrierorder;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.constant.ConfigKeyConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.base.utils.LocalStringUtil;
import com.logistics.tms.base.utils.RegExpValidatorUtil;
import com.logistics.tms.base.utils.StripTrailingZerosUtils;
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz;
import com.logistics.tms.biz.carrierorder.model.GetTicketsAmountByCarrierOrderIdsModel;
import com.logistics.tms.biz.carrierorder.model.SearchCarrierOrderVehicleHistoryModel;
import com.logistics.tms.biz.carrierorder.model.SearchListStatisticsForYeloLifeModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.demandorder.DemandOrderBiz;
import com.logistics.tms.biz.extvehiclesettlement.ExtVehicleSettlementBiz;
import com.logistics.tms.client.WarehouseLifeClient;
import com.logistics.tms.client.feign.warehouse.lift.reponse.GetWarehouseDetailForLifeResponseModel;
import com.logistics.tms.client.feign.warehouse.lift.request.GetWarehouseDetailForLifeRequestModel;
import com.logistics.tms.client.feign.yelolife.basicdata.YeloLifeBasicDataServiceApi;
import com.logistics.tms.controller.carrierorder.request.*;
import com.logistics.tms.controller.carrierorder.response.*;
import com.logistics.tms.controller.demandorder.request.UpdateDemandOrderStatusByIdsRequestModel;
import com.logistics.tms.controller.demandorder.response.GetDemandOrderInfoByIdsModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.logistics.tms.rabbitmq.consumer.model.YeloLifeCarrierOrderGoodsStockOutModel;
import com.logistics.tms.rabbitmq.consumer.model.YeloLifeCarrierOrderInStatusModel;
import com.logistics.tms.rabbitmq.consumer.model.YeloLifeCarrierOrderStockOutModel;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.logistics.tms.rabbitmq.publisher.model.CarrierOrderGoodsToYeloLifeModel;
import com.logistics.tms.rabbitmq.publisher.model.CarrierOrderLoadToYeloLifeModel;
import com.logistics.tms.rabbitmq.publisher.model.SyncCarrierOrderToYeloLifeModel;
import com.logistics.tms.rabbitmq.publisher.model.UpdateLifeCarrierOrderAddressMessage;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 乐橘新生运单biz
 *
 * <AUTHOR>
 * @date ：Created in 2022/8/19
 */
@Slf4j
@Service
public class CarrierOrderForYloLifeBiz {

	@Autowired
	private TCarrierOrderMapper tCarrierOrderMapper;
	@Autowired
	private TCarrierOrderTicketsMapper tCarrierOrderTicketsMapper;
	@Autowired
	private TCarrierOrderVehicleHistoryMapper tCarrierOrderVehicleHistoryMapper;
	@Autowired
	private TDemandOrderMapper tDemandOrderMapper;
	@Autowired
	private TPaymentMapper tPaymentMapper;
	@Autowired
	private TReceivementMapper tReceivementMapper;
	@Autowired
	private CarrierOrderCommonBiz carrierOrderCommonBiz;
	@Autowired
	private TCarrierOrderGoodsMapper tCarrierOrderGoodsMapper;
	@Autowired
	private CommonBiz commonBiz;
	@Autowired
	private TCarrierOrderEventsMapper tCarrierOrderEventsMapper;
	@Autowired
	private TCarrierOrderOperateLogsMapper tCarrierOrderOperateLogsMapper;
	@Autowired
	private RabbitMqPublishBiz rabbitMqPublishBiz;
	@Autowired
	private DemandOrderBiz demandOrderBiz;
	@Autowired
	private WarehouseLifeClient warehouseLifeClient;
	@Autowired
	private TCarrierOrderAddressMapper tCarrierOrderAddressMapper;
	@Autowired
	private ConfigKeyConstant configKeyConstant;
	@Autowired
	private TCompanyCarrierMapper tCompanyCarrierMapper;
	@Autowired
	private ExtVehicleSettlementBiz extVehicleSettlementBiz;
	@Resource
	private TCarrierOrderGoodsCodeMapper tCarrierOrderGoodsCodeMapper;
	@Resource
	private YeloLifeBasicDataServiceApi yeloLifeBasicDataServiceApi;

	/**
	 * 查询新生运单列表
	 *
	 * @param requestModel 筛选条件
	 * @return 运单信息
	 */
	public PageInfo<SearchCarrierOrderListForYeloLifeResponseModel> searchCarrierOrderListForYeloLife(SearchCarrierOrderListForYeloLifeRequestModel requestModel) {

		//批量查询校验
		if (ListUtils.isNotEmpty(requestModel.getCarrierOrderCodeList()) && requestModel.getCarrierOrderCodeList().size() > CommonConstant.INTEGER_TWENTY_FIVE) {
			throw new BizException(CarrierDataExceptionEnum.SEARCH_CARRIER_ORDER_AMOUNT_MAX);
		}
		if (ListUtils.isNotEmpty(requestModel.getDemandOrderCodeList()) && requestModel.getDemandOrderCodeList().size() > CommonConstant.INTEGER_TWENTY_FIVE) {
			throw new BizException(CarrierDataExceptionEnum.SEARCH_CARRIER_ORDER_AMOUNT_MAX);
		}

		//运单车辆信息map
		Map<Long, TCarrierOrderVehicleHistory> carrierOrderVehicleHistoryMap = new HashMap<>();

		//条件查询返回运单id（带分页）
		List<Long> carrierOrderIdList = searchCarrierOrderIdListForYeloLife(requestModel, carrierOrderVehicleHistoryMap, true);
		PageInfo carrierOrderList = new PageInfo(carrierOrderIdList);

		if (ListUtils.isNotEmpty(carrierOrderIdList)) {
			String carrierOrderIds = StringUtils.listToString(carrierOrderIdList, ',');
			//根据运单id查询运单信息
			List<SearchCarrierOrderListForYeloLifeResponseModel> searchCarrierOrderListResponseModels = tCarrierOrderMapper.searchCarrierOrderForYeloLife(carrierOrderIds);

			//根据运单id查询票据信息
			List<GetTicketsAmountByCarrierOrderIdsModel> ticketsAmountList = tCarrierOrderTicketsMapper.getTicketsAmountByCarrierOrderIds(carrierOrderIds);
			Map<Long, Integer> carrierOrderTicketsAmountMap = ticketsAmountList.stream().collect(
					Collectors.toMap(GetTicketsAmountByCarrierOrderIdsModel::getCarrierOrderId, GetTicketsAmountByCarrierOrderIdsModel::getAmount));

			//不存在车辆相关筛选条件，则根据运单id查询运单司机车辆信息
			if (MapUtils.isEmpty(carrierOrderVehicleHistoryMap)) {
				List<TCarrierOrderVehicleHistory> carrierOrderVehicleHistoryList = tCarrierOrderVehicleHistoryMapper.getValidByCarrierOrderIds(carrierOrderIds);
				carrierOrderVehicleHistoryList.forEach(item -> carrierOrderVehicleHistoryMap.put(item.getCarrierOrderId(), item));
			}

			//拼接数据
			TCarrierOrderVehicleHistory tCarrierOrderVehicleHistory;
			for (SearchCarrierOrderListForYeloLifeResponseModel responseModel : searchCarrierOrderListResponseModels) {
				//票据数量
				responseModel.setCarrierOrderTicketsAmount(Optional.ofNullable(carrierOrderTicketsAmountMap.get(responseModel.getCarrierOrderId())).orElse(CommonConstant.INTEGER_ZERO));

				//车辆司机信息
				tCarrierOrderVehicleHistory = carrierOrderVehicleHistoryMap.get(responseModel.getCarrierOrderId());
				if (tCarrierOrderVehicleHistory != null) {
					responseModel.setVehicleNo(tCarrierOrderVehicleHistory.getVehicleNo());
					responseModel.setDriverName(tCarrierOrderVehicleHistory.getDriverName());
					responseModel.setDriverMobile(tCarrierOrderVehicleHistory.getDriverMobile());
				}

			}
			carrierOrderList.setList(searchCarrierOrderListResponseModels);
		}
		return carrierOrderList;
	}

	public List<Long> searchCarrierOrderIdListForYeloLife(SearchCarrierOrderListForYeloLifeRequestModel requestModel, Map<Long, TCarrierOrderVehicleHistory> carrierOrderVehicleHistoryMap, boolean enablePaging){
		//查询条件含有司机车辆信息
		if (StringUtils.isBlank(requestModel.getCarrierOrderIds())
				&& (StringUtils.isNotBlank(requestModel.getVehicleNo())
				|| StringUtils.isNotBlank(requestModel.getDriver()))){
			//查询地址信息
			SearchCarrierOrderVehicleHistoryModel vehicleHistoryModel = MapperUtils.mapper(requestModel, SearchCarrierOrderVehicleHistoryModel.class);
			List<TCarrierOrderVehicleHistory> vehicleHistoryList = tCarrierOrderVehicleHistoryMapper.searchListByCarrierOrderParams(vehicleHistoryModel);
			//不存在，直接返回
			if (ListUtils.isEmpty(vehicleHistoryList)) {
				return new ArrayList<>();
			}
			//存在，数据聚合处理
			List<Long> vehicleCarrierOrderIdList = new ArrayList<>();
			for (TCarrierOrderVehicleHistory vehicleHistory : vehicleHistoryList) {
				carrierOrderVehicleHistoryMap.put(vehicleHistory.getCarrierOrderId(), vehicleHistory);
				if (!vehicleCarrierOrderIdList.contains(vehicleHistory.getCarrierOrderId())) {
					vehicleCarrierOrderIdList.add(vehicleHistory.getCarrierOrderId());
				}
			}
			requestModel.setCarrierOrderIdList(vehicleCarrierOrderIdList);
		}

		//分页查询获取运单Id
		if (enablePaging) {
			requestModel.enablePaging();
		}
		return tCarrierOrderMapper.searchCarrierOrderIdsForYeloLife(requestModel);
	}

	/**
	 * 运单列表统计 TAB
	 *
	 * @param requestModel 筛选条件
	 * @return 运单TAB信息
	 */
	public SearchCarrierListStatisticsResponseModel searchListStatisticsForYeloLife(SearchCarrierOrderListForYeloLifeRequestModel requestModel) {
		requestModel.setStatus(null);
		//条件查询返回运单id
		List<Long> carrierOrderIdList = searchCarrierOrderIdListForYeloLife(requestModel, new HashMap<>(), false);

		//累加各状态数量
		int allCount = 0;
		int waitArriveLoadSiteCount = 0;
		int waitLoadCount = 0;
		int waitArriveUnloadSiteCount = 0;
		int waitUnloadCount = 0;
		int waitSignedAccount = 0;
		int signedAccount = 0;
		int cancelCount = 0;

		if (ListUtils.isNotEmpty(carrierOrderIdList)) {
			//查询统计数据
			List<SearchListStatisticsForYeloLifeModel> statisticsList = tCarrierOrderMapper.searchCarrierOrderIdsStatisticsForYeloLife(carrierOrderIdList);
			for (SearchListStatisticsForYeloLifeModel statistics : statisticsList) {
				allCount++;
				//归类各状态数量
				Integer status = statistics.getStatus();
				if (CommonConstant.INTEGER_ONE.equals(statistics.getIfCancel())) {
					//已取消
					cancelCount++;
				} else {
					if (CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey().equals(status)) {
						//待到达提货地
						waitArriveLoadSiteCount++;
					} else if (CarrierOrderStatusEnum.WAIT_LOAD.getKey().equals(status)) {
						//待提货
						waitLoadCount++;
					} else if (CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey().equals(status)) {
						//待到达卸货地
						waitArriveUnloadSiteCount++;
					} else if (CarrierOrderStatusEnum.WAIT_UNLOAD.getKey().equals(status)) {
						//待卸货
						waitUnloadCount++;
					} else if (CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey().equals(status)) {
						//待签收
						waitSignedAccount++;
					} else if (CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(status)) {
						//已签收
						signedAccount++;
					}
				}
			}
		}

		//赋值
		SearchCarrierListStatisticsResponseModel responseModel = new SearchCarrierListStatisticsResponseModel();
		responseModel.setAllCount(allCount);
		responseModel.setWaitArriveLoadSiteCount(waitArriveLoadSiteCount);
		responseModel.setWaitLoadCount(waitLoadCount);
		responseModel.setWaitArriveUnloadSiteCount(waitArriveUnloadSiteCount);
		responseModel.setWaitUnloadCount(waitUnloadCount);
		responseModel.setWaitSignedAccount(waitSignedAccount);
		responseModel.setSignedAccount(signedAccount);
		responseModel.setCancelCount(cancelCount);
		return responseModel;
	}

	/**
	 * 查询新生运单详情
	 *
	 * @param requestModel 运单ID
	 * @return 运单详情
	 */
	public CarrierOrderDetailForYeloLifeResponseModel carrierOrderDetailForYeloLife(CarrierOrderIdRequestModel requestModel) {
		if (requestModel == null) {
			throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
		}

		//查询运单
		TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
		if (tCarrierOrder == null) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
		}
		//查询运单货物明细
		List<TCarrierOrderGoods> tCarrierOrderGoods = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(tCarrierOrder.getId().toString());
		String tCarrierOrderGoodIds = tCarrierOrderGoods.stream().map(orderGoods -> orderGoods.getId().toString()).collect(Collectors.joining(","));

		//查询运单货物编码明细
		List<TCarrierOrderGoodsCode> tCarrierOrderGoodsCodes = tCarrierOrderGoodsCodeMapper.selectGoodsCodeByGoodsIds(tCarrierOrderGoodIds);
		Map<Long, List<TCarrierOrderGoodsCode>> tCarrierOrderGoodsCodesMap = tCarrierOrderGoodsCodes.stream().collect(Collectors.groupingBy(TCarrierOrderGoodsCode::getCarrierOrderGoodsId));

		//查询运单车辆历史
		List<TCarrierOrderVehicleHistory> tCarrierOrderVehicleHistories = tCarrierOrderVehicleHistoryMapper.getAllByCarrierOrderIds(tCarrierOrder.getId().toString());

		//查询运单地址
		TCarrierOrderAddress tCarrierOrderAddress = tCarrierOrderAddressMapper.getByCarrierOrderId(tCarrierOrder.getId());


		CarrierOrderDetailForYeloLifeResponseModel carrierOrderDetailResponseModel = new CarrierOrderDetailForYeloLifeResponseModel();
		carrierOrderDetailResponseModel.setCarrierOrderId(tCarrierOrder.getId());
		carrierOrderDetailResponseModel.setDemandOrderId(tCarrierOrder.getDemandOrderId());
		carrierOrderDetailResponseModel.setDemandOrderCode(tCarrierOrder.getDemandOrderCode());
		carrierOrderDetailResponseModel.setCarrierOrderCode(tCarrierOrder.getCarrierOrderCode());
		carrierOrderDetailResponseModel.setCustomerOrderCode(tCarrierOrder.getCustomerOrderCode());
		carrierOrderDetailResponseModel.setCustomerName(tCarrierOrder.getCustomerName());
		carrierOrderDetailResponseModel.setStatus(tCarrierOrder.getStatus());
		carrierOrderDetailResponseModel.setExpectAmount(tCarrierOrder.getExpectAmount());
		carrierOrderDetailResponseModel.setIfCancel(tCarrierOrder.getIfCancel());
		carrierOrderDetailResponseModel.setDispatchUserName(tCarrierOrder.getDispatchUserName());
		carrierOrderDetailResponseModel.setRemark(tCarrierOrder.getRemark());
		carrierOrderDetailResponseModel.setDispatchTime(tCarrierOrder.getDispatchTime());
		carrierOrderDetailResponseModel.setGoodsUnit(tCarrierOrder.getGoodsUnit());
		carrierOrderDetailResponseModel.setLoadAmount(tCarrierOrder.getLoadAmount());
		carrierOrderDetailResponseModel.setUnloadAmount(tCarrierOrder.getUnloadAmount());
		carrierOrderDetailResponseModel.setSignAmount(tCarrierOrder.getSignAmount());
		carrierOrderDetailResponseModel.setCompanyCarrierId(tCarrierOrder.getCompanyCarrierId());
		carrierOrderDetailResponseModel.setCompanyCarrierName(tCarrierOrder.getCompanyCarrierName());
		carrierOrderDetailResponseModel.setCompanyCarrierType(tCarrierOrder.getCompanyCarrierType());
		carrierOrderDetailResponseModel.setCarrierContactName(tCarrierOrder.getCarrierContactName());
		carrierOrderDetailResponseModel.setCarrierContactPhone(tCarrierOrder.getCarrierContactPhone());
		carrierOrderDetailResponseModel.setBusinessType (tCarrierOrder.getBusinessType());
		carrierOrderDetailResponseModel.setPublishName(tCarrierOrder.getPublishName());
		carrierOrderDetailResponseModel.setPublishMobile(tCarrierOrder.getPublishMobile());
		carrierOrderDetailResponseModel.setCustomerUserName(tCarrierOrder.getCustomerUserName());
		carrierOrderDetailResponseModel.setCustomerUserMobile(tCarrierOrder.getCustomerUserMobile());
		carrierOrderDetailResponseModel.setEntrustSettlementTonnage(tCarrierOrder.getSettlementTonnage());
		carrierOrderDetailResponseModel.setCarrierSettlementTonnage(tCarrierOrder.getCarrierSettlement());
		carrierOrderDetailResponseModel.setCarrierSettleStatementStatus(tCarrierOrder.getCarrierSettleStatementStatus());

		//地址相关
		CarrierOrderDetailBasicInfoModel carrierOrderDetailBasicInfo = new CarrierOrderDetailBasicInfoModel();
		carrierOrderDetailBasicInfo.setLoadProvinceName(tCarrierOrderAddress.getLoadProvinceName());
		carrierOrderDetailBasicInfo.setLoadCityName(tCarrierOrderAddress.getLoadCityName());
		carrierOrderDetailBasicInfo.setLoadAreaName(tCarrierOrderAddress.getLoadAreaName());
		carrierOrderDetailBasicInfo.setLoadDetailAddress(tCarrierOrderAddress.getLoadDetailAddress());
		carrierOrderDetailBasicInfo.setLoadWarehouse(tCarrierOrderAddress.getLoadWarehouse());
		carrierOrderDetailBasicInfo.setConsignorName(tCarrierOrderAddress.getConsignorName());
		carrierOrderDetailBasicInfo.setConsignorMobile(tCarrierOrderAddress.getConsignorMobile());
		carrierOrderDetailBasicInfo.setUnloadProvinceName(tCarrierOrderAddress.getUnloadProvinceName());
		carrierOrderDetailBasicInfo.setUnloadCityName(tCarrierOrderAddress.getUnloadCityName());
		carrierOrderDetailBasicInfo.setUnloadAreaName(tCarrierOrderAddress.getUnloadAreaName());
		carrierOrderDetailBasicInfo.setUnloadDetailAddress(tCarrierOrderAddress.getUnloadDetailAddress());
		carrierOrderDetailBasicInfo.setUnloadWarehouse(tCarrierOrderAddress.getUnloadWarehouse());
		carrierOrderDetailBasicInfo.setReceiverName(tCarrierOrderAddress.getReceiverName());
		carrierOrderDetailBasicInfo.setReceiverMobile(tCarrierOrderAddress.getReceiverMobile());
		carrierOrderDetailBasicInfo.setLoadTime(tCarrierOrder.getLoadTime());
		carrierOrderDetailBasicInfo.setUnloadTime(tCarrierOrder.getUnloadTime());
		carrierOrderDetailResponseModel.setCarrierOrderDetailBasicInfo(carrierOrderDetailBasicInfo);

		//费用相关
		CarrierOrderDetailFreightFeeInfoModel carrierOrderDetailFreightFeeInfo = new CarrierOrderDetailFreightFeeInfoModel();
		carrierOrderDetailFreightFeeInfo.setDispatchFreightFeeType(tCarrierOrder.getDispatchFreightFeeType());
		carrierOrderDetailFreightFeeInfo.setDispatchFreightFee(tCarrierOrder.getDispatchFreightFee());
		carrierOrderDetailFreightFeeInfo.setSignFreightFee(tCarrierOrder.getSignFreightFee());
		carrierOrderDetailFreightFeeInfo.setAdjustFee(tCarrierOrder.getAdjustFee());
		carrierOrderDetailFreightFeeInfo.setMarkupFee(tCarrierOrder.getMarkupFee());
		carrierOrderDetailFreightFeeInfo.setEntrustFreightType(tCarrierOrder.getEntrustFreightType());
		carrierOrderDetailFreightFeeInfo.setEntrustFreight(tCarrierOrder.getEntrustFreight());
		carrierOrderDetailFreightFeeInfo.setExpectEntrustFreightType(tCarrierOrder.getExpectEntrustFreightType());
		carrierOrderDetailFreightFeeInfo.setExpectEntrustFreight(tCarrierOrder.getExpectEntrustFreight());
		carrierOrderDetailFreightFeeInfo.setCarrierFreightType(tCarrierOrder.getCarrierPriceType());
		carrierOrderDetailFreightFeeInfo.setCarrierFreight(tCarrierOrder.getCarrierPrice());
		carrierOrderDetailResponseModel.setCarrierOrderDetailFreightFeeInfo(carrierOrderDetailFreightFeeInfo);

		List<CarrierOrderDetailGoodsInfoModel> carrierOrderDetailGoodsInfo = new ArrayList<>();
		for (TCarrierOrderGoods tCarrierOrderGood : tCarrierOrderGoods) {

			if (CarrierOrderIfRecycleByCodeEnum.RECYCLE_BY_CODE.getCode().equals(tCarrierOrder.getIfRecycleByCode())
					&& CarrierOrderStatusEnum.WAIT_LOAD.getKey().compareTo(tCarrierOrder.getStatus()) < 0) {
				List<TCarrierOrderGoodsCode> carrierOrderGoodsCodes = tCarrierOrderGoodsCodesMap.getOrDefault(tCarrierOrderGood.getId(), new ArrayList<>());

				for (TCarrierOrderGoodsCode carrierOrderGoodsCode : carrierOrderGoodsCodes) {
					CarrierOrderDetailGoodsInfoModel carrierOrderDetailGoodsInfoModel = new CarrierOrderDetailGoodsInfoModel();
					carrierOrderDetailGoodsInfoModel.setYeloCode(carrierOrderGoodsCode.getYeloGoodCode());
					carrierOrderDetailGoodsInfoModel.setGoodsId(tCarrierOrderGood.getId());
					carrierOrderDetailGoodsInfoModel.setGoodsName(tCarrierOrderGood.getGoodsName());
					carrierOrderDetailGoodsInfoModel.setLength(tCarrierOrderGood.getLength());
					carrierOrderDetailGoodsInfoModel.setWidth(tCarrierOrderGood.getWidth());
					carrierOrderDetailGoodsInfoModel.setHeight(tCarrierOrderGood.getHeight());
					carrierOrderDetailGoodsInfoModel.setGoodsSize(tCarrierOrderGood.getGoodsSize());
                    carrierOrderDetailGoodsInfoModel.setExpectAmount(tCarrierOrderGood.getExpectAmount());
					carrierOrderDetailGoodsInfoModel.setLoadAmount(carrierOrderGoodsCode.getLoadAmount());
					carrierOrderDetailGoodsInfoModel.setUnloadAmount(carrierOrderGoodsCode.getUnloadAmount());
					carrierOrderDetailGoodsInfoModel.setSignAmount(carrierOrderGoodsCode.getSignAmount());
					carrierOrderDetailGoodsInfo.add(carrierOrderDetailGoodsInfoModel);
				}
			}else {
				CarrierOrderDetailGoodsInfoModel carrierOrderDetailGoodsInfoModel = new CarrierOrderDetailGoodsInfoModel();
				carrierOrderDetailGoodsInfoModel.setGoodsId(tCarrierOrderGood.getId());
				carrierOrderDetailGoodsInfoModel.setGoodsName(tCarrierOrderGood.getGoodsName());
				carrierOrderDetailGoodsInfoModel.setLength(tCarrierOrderGood.getLength());
				carrierOrderDetailGoodsInfoModel.setWidth(tCarrierOrderGood.getWidth());
				carrierOrderDetailGoodsInfoModel.setHeight(tCarrierOrderGood.getHeight());
				carrierOrderDetailGoodsInfoModel.setGoodsSize(tCarrierOrderGood.getGoodsSize());
				carrierOrderDetailGoodsInfoModel.setExpectAmount(tCarrierOrderGood.getExpectAmount());
				carrierOrderDetailGoodsInfoModel.setLoadAmount(tCarrierOrderGood.getLoadAmount());
				carrierOrderDetailGoodsInfoModel.setUnloadAmount(tCarrierOrderGood.getUnloadAmount());
				carrierOrderDetailGoodsInfoModel.setSignAmount(tCarrierOrderGood.getSignAmount());
				carrierOrderDetailGoodsInfo.add(carrierOrderDetailGoodsInfoModel);

			}
		}
		carrierOrderDetailResponseModel.setCarrierOrderDetailGoodsInfo(carrierOrderDetailGoodsInfo);

		//车辆信息
		List<CarrierOrderDetailVehicleDriverInfoModel> carrierOrderDetailVehicleDriverInfo = new ArrayList<>();
		for (TCarrierOrderVehicleHistory tCarrierOrderVehicleHistory : tCarrierOrderVehicleHistories) {
			CarrierOrderDetailVehicleDriverInfoModel driverInfoModel = new CarrierOrderDetailVehicleDriverInfoModel();
			driverInfoModel.setVehicleHistoryId(tCarrierOrderVehicleHistory.getId().toString());
			driverInfoModel.setAuditStatus(tCarrierOrderVehicleHistory.getAuditStatus());
			driverInfoModel.setIfInvalid(tCarrierOrderVehicleHistory.getIfInvalid());
			driverInfoModel.setVehicleNo(tCarrierOrderVehicleHistory.getVehicleNo());
			driverInfoModel.setDriverName(tCarrierOrderVehicleHistory.getDriverName());
			driverInfoModel.setDriverMobile(tCarrierOrderVehicleHistory.getDriverMobile());
			driverInfoModel.setRejectReason(tCarrierOrderVehicleHistory.getRejectReason());
			driverInfoModel.setRemark(tCarrierOrderVehicleHistory.getRemark());
			driverInfoModel.setCreatedTime(tCarrierOrderVehicleHistory.getCreatedTime());
			carrierOrderDetailVehicleDriverInfo.add(driverInfoModel);
		}
		carrierOrderDetailResponseModel.setCarrierOrderDetailVehicleDriverInfo(carrierOrderDetailVehicleDriverInfo);
//        carrierOrderDetailResponseModel.setCarrierOrderDetailEvents();


//		//查询运单详情
//		CarrierOrderDetailForYeloLifeResponseModel carrierOrderDetailResponseModel = tCarrierOrderMapper.getCarrierOrderDetailForYeloLifeById(requestModel.getCarrierOrderId());
//		if (carrierOrderDetailResponseModel == null) {
//			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
//		}
		//查询车主结算费用
		TPayment tPayment = tPaymentMapper.getByCarrierOrderId(carrierOrderDetailResponseModel.getCarrierOrderId());
		if (tPayment != null) {
			carrierOrderDetailResponseModel.getCarrierOrderDetailFreightFeeInfo().setCarrierPriceType(tPayment.getPriceType());
			carrierOrderDetailResponseModel.getCarrierOrderDetailFreightFeeInfo().setCarrierSettlementCostTotal(tPayment.getSettlementCostTotal());
		}
		//查询货主结算费用
		TReceivement tReceivement = tReceivementMapper.getByCarrierOrderId(carrierOrderDetailResponseModel.getCarrierOrderId());
		if (tReceivement != null) {
			carrierOrderDetailResponseModel.getCarrierOrderDetailFreightFeeInfo().setEntrustPriceType(tReceivement.getPriceType());
			carrierOrderDetailResponseModel.getCarrierOrderDetailFreightFeeInfo().setEntrustSettlementCostTotal(tReceivement.getSettlementCostTotal());
		}
        //查询车主信息
		TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(carrierOrderDetailResponseModel.getCompanyCarrierId());
		if (tCompanyCarrier != null && tCompanyCarrier.getValid().equals(IfValidEnum.VALID.getKey())){
			carrierOrderDetailResponseModel.setIsOurCompany(tCompanyCarrier.getLevel());
		}

		//车辆司机排序
		carrierOrderDetailResponseModel.getCarrierOrderDetailVehicleDriverInfo().sort((o1, o2) -> o2.getCreatedTime().compareTo(o1.getCreatedTime()));
		return carrierOrderDetailResponseModel;
	}

	/**
	 * 查询新生运单票据信息
	 *
	 * @param requestModel 运单ID
	 * @return 运单票据
	 */
	public List<TicketsModel> getTicketsForYeloLife(CarrierOrderIdRequestModel requestModel) {
		return tCarrierOrderTicketsMapper.getTicketsByCarrierOrderIds(requestModel.getCarrierOrderId() + "");
	}

	/**
	 * 提卸货详情
	 *
	 * @param requestModel 运单did
	 * @return 提卸货详情
	 */
	public LoadDetailForYeloLifeResponseModel getLoadDetailForYeloLife(LoadDetailForYeloLifeRequestModel requestModel) {
		//节点操作提示
		List<TCarrierOrder> dbTCarrierOrderList = carrierOrderCommonBiz.checkCarrierOrderIfExist(Collections.singletonList(requestModel.getCarrierOrderId()));
		TCarrierOrder dbTCarrierOrder = dbTCarrierOrderList.get(CommonConstant.INTEGER_ZERO);
		//非新生单子不能操作
		if (!DemandOrderSourceEnum.YELO_LIFE.getKey().equals(dbTCarrierOrder.getDemandOrderSource())){
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
		}
		//提货操作
		if (CommonConstant.INTEGER_ONE.equals(requestModel.getNodeType())) {
			if (!dbTCarrierOrder.getStatus().equals(CarrierOrderStatusEnum.WAIT_LOAD.getKey())) {
				throw new BizException(CarrierDataExceptionEnum.ONLY_WAIT_LOAD);
			}
			//新生销售-货物完成出库才能提货
			if (EntrustTypeEnum.LIFE_SALE.getKey().equals(dbTCarrierOrder.getDemandOrderEntrustType()) && !CarrierOrderOutStatusEnum.FINISH_OUT.getKey().equals(dbTCarrierOrder.getOutStatus())) {
				throw new BizException(CarrierDataExceptionEnum.NOT_ALLOWED_PICKUP);
			}
		}
		//卸货操作
		else if (CommonConstant.INTEGER_TWO.equals(requestModel.getNodeType())) {
			if (!dbTCarrierOrder.getStatus().equals(CarrierOrderStatusEnum.WAIT_UNLOAD.getKey())) {
				throw new BizException(CarrierDataExceptionEnum.ONLY_WAIT_UNLOAD);
			}
		}

		LoadDetailForYeloLifeResponseModel loadDetailList = tCarrierOrderMapper.getLoadDetailForYeloLifeByIds(requestModel.getCarrierOrderId());
		boolean crossNodeOperation = true;
		for (GetLoadDetailForYeloLifeGoodModel tempGoods : loadDetailList.getGoodsList()) {
			if (Optional.ofNullable(tempGoods.getLoadAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
				crossNodeOperation = false;
				break;
			}
		}
		for (GetLoadDetailForYeloLifeGoodModel tempGoods : loadDetailList.getGoodsList()) {
			tempGoods.setLoadAmount(BigDecimal.ZERO.compareTo(tempGoods.getLoadAmount()) == 0 && crossNodeOperation ? tempGoods.getExpectAmount() : Optional.ofNullable(tempGoods.getLoadAmount()).orElse(BigDecimal.ZERO));
		}
		return loadDetailList;
	}

	/**
	 * 新生运单提货
	 *
	 * @param requestModel 提货信息
	 */
	@Transactional
	public  Result loadForYeloLife(LoadForYeloLifeRequestModel requestModel) {
		Map<Long, BigDecimal> pageLoadGoodsAmountMap = new HashMap<>();
		//校验入参数量，并统计提货数量
		if (requestModel.getCarrierOrderId() == null || ListUtils.isEmpty(requestModel.getGoodsList())) {
			throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
		}

		BigDecimal pageLoadAmount = BigDecimal.ZERO;
		for (LoadGoodsForYeloLifeRequestModel itemGoods : requestModel.getGoodsList()) {
			BigDecimal tempGoodsCount = Optional.ofNullable(itemGoods.getLoadAmount()).orElse(BigDecimal.ZERO);
			if (tempGoodsCount.compareTo(BigDecimal.ZERO) < CommonConstant.INTEGER_ZERO
					|| tempGoodsCount.compareTo(CommonConstant.BIG_DECIMAL_FIVE_THOUSAND_HUNDRED) > CommonConstant.INTEGER_ZERO) {
				throw new BizException(CarrierDataExceptionEnum.EXPECT_LOAD_AMOUNT_ERROR);
			}

			if (!RegExpValidatorUtil.isFloatNumber(tempGoodsCount.stripTrailingZeros().toPlainString())) {
				throw new BizException(CarrierDataExceptionEnum.EXPECT_AMOUNT_NOT_WEIGHT);
			}

			pageLoadGoodsAmountMap.put(itemGoods.getGoodsId(), itemGoods.getLoadAmount());
			pageLoadAmount = pageLoadAmount.add(itemGoods.getLoadAmount());
		}

		//总量校验： 0<提货数量<5000
		if (pageLoadAmount.compareTo(BigDecimal.ZERO) <= CommonConstant.INTEGER_ZERO) {
			throw new BizException(CarrierDataExceptionEnum.EXPECT_LOAD_AMOUNT_ERROR);
		}

		//做校验用
		TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());

		boolean ifYeloLifeRecycleAndByCode = tCarrierOrder.getIfRecycleByCode().equals(CommonConstant.INTEGER_ONE) &&
				EntrustTypeEnum.LIFE_TRANSFER.getKey().equals(tCarrierOrder.getDemandOrderEntrustType());
		Map<Long,List<LoadGoodsForYeloLifeRequestCodeModel>> goodCodeMap = new HashMap<>();
		List<String> code = new ArrayList<>();
		if (ifYeloLifeRecycleAndByCode){
			requestModel.getGoodsList().forEach(e->{
				if (ListUtils.isNotEmpty(e.getCodeDtoList())){
					goodCodeMap.put(e.getGoodsId(),e.getCodeDtoList());
					code.addAll(e.getCodeDtoList().stream().map(LoadGoodsForYeloLifeRequestCodeModel::getYeloCode).collect(Collectors.toList()));
				}
			});
			if (CollectionUtil.isNotEmpty(code)){
				if (code.size() != new HashSet<>(code).size()) {
					throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_GOODS_CODE_CANNOT_DEP);
				}

				Result<List<String>> listResult = yeloLifeBasicDataServiceApi.verifyGoodsCodeList(code);
				listResult.throwException();
				List<String> codes = listResult.getData();
				if (code.size() != codes.size()) {
					code.removeAll(codes);
					return new Result(CommonConstant.CODE_NOT_EXIST,code,CarrierDataExceptionEnum.CARRIER_ORDER_GOODS_CODE_NOT_STANDARD.getMsg());
				}
			}
		}
		if (tCarrierOrder == null || IfValidEnum.INVALID.getKey().equals(tCarrierOrder.getValid())) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
		}
		//非新生单子不能操作
		if (!DemandOrderSourceEnum.YELO_LIFE.getKey().equals(tCarrierOrder.getDemandOrderSource())){
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
		}
		//不是待提货状态不能操作
		if (!CarrierOrderStatusEnum.WAIT_LOAD.getKey().equals(tCarrierOrder.getStatus())) {
			throw new BizException(CarrierDataExceptionEnum.ONLY_WAIT_LOAD);
		}
		//已取消不能操作
		if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfCancel())) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL);
		}
		//运单放空不能操作
		if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfEmpty())) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EMPTY);
		}

		//提货数量大于预计数量 0<货物数量<=预提数量+1
		if (pageLoadAmount.compareTo(tCarrierOrder.getExpectAmount().add(BigDecimal.ONE)) > CommonConstant.INTEGER_ZERO) {
			throw new BizException(CarrierDataExceptionEnum.LOAD_UNLOAD_AMOUNT_ERROR);
		}

		//查询运单货物
		List<TCarrierOrderGoods> dbTCarrierOrderGoods = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(ConverterUtils.toString(tCarrierOrder.getId()));

		//新生销售
		if (EntrustTypeEnum.LIFE_SALE.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())) {
			//货物完成出库才能提货
			if (!CarrierOrderOutStatusEnum.FINISH_OUT.getKey().equals(tCarrierOrder.getOutStatus())) {
				throw new BizException(CarrierDataExceptionEnum.NOT_ALLOWED_PICKUP);
			}

			// 提货总数量与出库总数量必须相等
			if (BigDecimal.ZERO.compareTo(tCarrierOrder.getLoadAmount()) != CommonConstant.INTEGER_ZERO
					&& tCarrierOrder.getLoadAmount().compareTo(pageLoadAmount) != CommonConstant.INTEGER_ZERO){
				StringBuilder loadAmountNumber = new StringBuilder();
				loadAmountNumber
						.append(tCarrierOrder.getLoadAmount().stripTrailingZeros().toPlainString())
						.append(GoodsUnitEnum.getEnum(tCarrierOrder.getGoodsUnit()).getUnit());
				String errorMsg = MessageFormat.format(CarrierDataExceptionEnum.CARRIER_ORDER_LOAD_AMOUNT_ERROR.getMsg(),
						tCarrierOrder.getCarrierOrderCode(), loadAmountNumber);
				throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_LOAD_AMOUNT_ERROR.getCode(), errorMsg);
			}

			//每个货物对应的数量必须跟出库数量一致
			for (TCarrierOrderGoods good : dbTCarrierOrderGoods) {
				if (good.getLoadAmount().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO
						&& good.getLoadAmount().compareTo(pageLoadGoodsAmountMap.get(good.getId())) != CommonConstant.INTEGER_ZERO){
					throw new BizException(CarrierDataExceptionEnum.LOAD_AMOUNT_ERROR_FOR_LE_YI);
				}
			}
		}

		carrierOrderCommonBiz.checkIfCarrierOrdersStatusCanChange(Collections.singletonList(tCarrierOrder.getId()), CarrierDataExceptionEnum.VEHICLE_NEED_AUDIT_TIP2);

		//查询运单车辆记录信息
		TCarrierOrderVehicleHistory dbCarrierOrderVehicleHistory = tCarrierOrderVehicleHistoryMapper.getValidTopByCarrierOrderId(tCarrierOrder.getId());

		Date now = new Date();
		TCarrierOrder upOrder;
		List<TCarrierOrder> upCarrierOrders = new ArrayList<>();
		TCarrierOrderTickets addTickets;
		List<TCarrierOrderTickets> addTicketsList = new ArrayList<>();
		TCarrierOrderGoods upOrderGood;
		List<TCarrierOrderGoods> upCarrierOrderGoods = new ArrayList<>();
		List<TCarrierOrderEvents> insertEvents = new ArrayList<>();
		List<TCarrierOrderOperateLogs> insertLogs = new ArrayList<>();

		//更新运单信息
		upOrder = new TCarrierOrder();
		upOrder.setId(tCarrierOrder.getId());
		upOrder.setStatus(CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey());
		upOrder.setStatusUpdateTime(now);
		upOrder.setLoadAmount(pageLoadAmount);
		upOrder.setLoadAmountExpect(pageLoadAmount);
		upOrder.setLoadTime(new Date());
		upOrder.setDeliveryMethod(CarrierOrderDeliverMethodEnum.BILL_PICKUP.getKey());
		commonBiz.setBaseEntityModify(upOrder, BaseContextHandler.getUserName());


		//处理现场图片
		if (ListUtils.isNotEmpty(requestModel.getSiteImgList())) {
			for (String imageUrl : requestModel.getSiteImgList()) {
				addTickets = new TCarrierOrderTickets();
				addTickets.setCarrierOrderId(tCarrierOrder.getId());
				addTickets.setImageName(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_LOAD_SCENE_PIC.getValue());
				addTickets.setImagePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getKey(), tCarrierOrder.getCarrierOrderCode(), imageUrl, null));
				addTickets.setImageType(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_LOAD_SCENE_PIC.getKey());
				addTickets.setUploadTime(now);
				addTickets.setUploadUserName(BaseContextHandler.getUserName());
				commonBiz.setBaseEntityAdd(addTickets, BaseContextHandler.getUserName());
				addTicketsList.add(addTickets);
			}
		}

		//处理提货单据
		if (ListUtils.isNotEmpty(requestModel.getOutImgList())) {
			for (String imageUrl : requestModel.getOutImgList()) {
				addTickets = new TCarrierOrderTickets();
				addTickets.setCarrierOrderId(tCarrierOrder.getId());
				addTickets.setImageName(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getValue());
				addTickets.setImagePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getKey(), tCarrierOrder.getCarrierOrderCode(), imageUrl, null));
				addTickets.setImageType(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getKey());
				addTickets.setUploadTime(now);
				addTickets.setUploadUserName(BaseContextHandler.getUserName());
				commonBiz.setBaseEntityAdd(addTickets, BaseContextHandler.getUserName());
				addTicketsList.add(addTickets);
			}
		}

		CarrierOrderGoodsToYeloLifeModel carrierOrderGoodsToYeloLifeModel;
		List<CarrierOrderGoodsToYeloLifeModel> carrierOrderGoodsToYeloLifeModelList = new ArrayList<>();
		//更新货物表信息，并计算同步到entrust的总提货数(货物提货数大于预提数 取 预提数)
		// 特殊逻辑 如果是新生回收的话，并且是按编码回收，那么需要下落编码code表和 good做关联，并且good的所有数量都是code表的综合 ----jiang 2.44
		List<TCarrierOrderGoodsCode> insertGoodsCodeList = new ArrayList<>();
		BigDecimal carrierOrderLoad = new BigDecimal("0.00");
		for (TCarrierOrderGoods good : dbTCarrierOrderGoods) {
			BigDecimal loadAmount = pageLoadGoodsAmountMap.get(good.getId());
			upOrderGood = new TCarrierOrderGoods();
			upOrderGood.setId(good.getId());
			upOrderGood.setLoadAmount(loadAmount);
			if (ifYeloLifeRecycleAndByCode){
				loadAmount = new BigDecimal("0.00");
				if (goodCodeMap.get(good.getId()) != null) {
					for (LoadGoodsForYeloLifeRequestCodeModel  e : goodCodeMap.get(good.getId())){
						TCarrierOrderGoodsCode tCarrierOrderGoodsCode = new TCarrierOrderGoodsCode();
						tCarrierOrderGoodsCode.setCarrierOrderGoodsId(good.getId());
						tCarrierOrderGoodsCode.setLoadAmount(e.getWeight());
						tCarrierOrderGoodsCode.setYeloGoodCode(e.getYeloCode());
						tCarrierOrderGoodsCode.setUnit(e.getUnit());
						commonBiz.setBaseEntityAdd(tCarrierOrderGoodsCode, BaseContextHandler.getUserName());
						insertGoodsCodeList.add(tCarrierOrderGoodsCode);
						loadAmount = loadAmount.add(e.getWeight());
						carrierOrderGoodsToYeloLifeModel = new CarrierOrderGoodsToYeloLifeModel();
						carrierOrderGoodsToYeloLifeModel.setSkuCode(good.getSkuCode());
						carrierOrderGoodsToYeloLifeModel.setCount(e.getWeight());
						carrierOrderGoodsToYeloLifeModel.setRecycleBagCode(e.getYeloCode());
						carrierOrderGoodsToYeloLifeModel.setUnit(e.getUnit());
						carrierOrderGoodsToYeloLifeModelList.add(carrierOrderGoodsToYeloLifeModel);
					}
				}
				upOrderGood.setLoadAmount(loadAmount);
				carrierOrderLoad = carrierOrderLoad.add(loadAmount);
			}else {
				//同步新生
				carrierOrderGoodsToYeloLifeModel = new CarrierOrderGoodsToYeloLifeModel();
				carrierOrderGoodsToYeloLifeModel.setSkuCode(good.getSkuCode());
				carrierOrderGoodsToYeloLifeModel.setCount(loadAmount);
				Integer goodsUnit = tCarrierOrder.getGoodsUnit();
				if (GoodsUnitEnum.BY_PACKAGE.getKey().equals(goodsUnit)){
					carrierOrderGoodsToYeloLifeModel.setUnit(CommonConstant.INTEGER_TWO);
				}
				if (GoodsUnitEnum.BY_WEIGHT.getKey().equals(goodsUnit)){
					carrierOrderGoodsToYeloLifeModel.setUnit(CommonConstant.INTEGER_ONE);
				}
				carrierOrderGoodsToYeloLifeModelList.add(carrierOrderGoodsToYeloLifeModel);
			}
			commonBiz.setBaseEntityModify(upOrderGood, BaseContextHandler.getUserName());
			upCarrierOrderGoods.add(upOrderGood);

		}
		if(ifYeloLifeRecycleAndByCode){
			upOrder.setLoadAmount(carrierOrderLoad);
			upOrder.setLoadAmountExpect(carrierOrderLoad);
		}
		upCarrierOrders.add(upOrder);
		//单位
		String unit = GoodsUnitEnum.getEnum(tCarrierOrder.getGoodsUnit()).getUnit();
		//生成运单事件
		TCarrierOrderEvents carrierOrderEvents = carrierOrderCommonBiz.getCarrierOrderEvent(tCarrierOrder.getId(), CarrierOrderEventsTypeEnum.PICK_UP, BaseContextHandler.getUserName(), CarrierOrderEventsTypeEnum.PICK_UP.format(StripTrailingZerosUtils.stripTrailingZerosToString(upOrder.getLoadAmount()) + unit));
		insertEvents.add(carrierOrderEvents);

		//生成操作日志
		TCarrierOrderOperateLogs carrierOrderOperateLogs = carrierOrderCommonBiz.getCarrierOrderOperateLogs(tCarrierOrder.getId(), CarrierOrderOperateLogsTypeEnum.PICK_UP, BaseContextHandler.getUserName(), CarrierOrderOperateLogsTypeEnum.PICK_UP.format(StripTrailingZerosUtils.stripTrailingZerosToString(upOrder.getLoadAmount()) + unit));
		insertLogs.add(carrierOrderOperateLogs);

		if (ListUtils.isNotEmpty(upCarrierOrders)) {
			tCarrierOrderMapper.batchUpdateCarrierOrders(upCarrierOrders);
		}
		if (ListUtils.isNotEmpty(upCarrierOrderGoods)) {
			tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(upCarrierOrderGoods);
		}
		if (ListUtils.isNotEmpty(insertGoodsCodeList)) {
			tCarrierOrderGoodsCodeMapper.batchInsertSelective(insertGoodsCodeList);
		}
		if (ListUtils.isNotEmpty(insertEvents)) {
			tCarrierOrderEventsMapper.batchInsertSelective(insertEvents);
		}
		if (ListUtils.isNotEmpty(insertLogs)) {
			tCarrierOrderOperateLogsMapper.batchInsertSelective(insertLogs);
		}
		if (ListUtils.isNotEmpty(addTicketsList)) {
			tCarrierOrderTicketsMapper.batchInsertTickets(addTicketsList);
		}

		//同步新生
		CarrierOrderLoadToYeloLifeModel carrierOrderLoadToYeloLifeModel = new CarrierOrderLoadToYeloLifeModel();
		carrierOrderLoadToYeloLifeModel.setDemandOrderCode(tCarrierOrder.getDemandOrderCode());
		carrierOrderLoadToYeloLifeModel.setCarrierOrderCode(tCarrierOrder.getCarrierOrderCode());
		carrierOrderLoadToYeloLifeModel.setCustomerOrderCode(tCarrierOrder.getCustomerOrderCode());
		carrierOrderLoadToYeloLifeModel.setVehicleNo(dbCarrierOrderVehicleHistory.getVehicleNo());
		carrierOrderLoadToYeloLifeModel.setRemark(tCarrierOrder.getRemark());
		carrierOrderLoadToYeloLifeModel.setUserName(BaseContextHandler.getUserName());
		carrierOrderLoadToYeloLifeModel.setLifeGoodsModels(carrierOrderGoodsToYeloLifeModelList);
		//发mq给新生系统
		SyncCarrierOrderToYeloLifeModel<Object> syncCarrierOrderToYeloLifeModel = new SyncCarrierOrderToYeloLifeModel<>();
		syncCarrierOrderToYeloLifeModel.setType(SyncCarrierOrderToYeloLifeModel.SyncTypeEnum.LOAD.getKey());
		syncCarrierOrderToYeloLifeModel.setMsgData(List.of(carrierOrderLoadToYeloLifeModel));
		rabbitMqPublishBiz.syncCarrierOrderToYeloLife(syncCarrierOrderToYeloLifeModel);

		//异步方法放在最后执行
		//节点信息推送微信公众号
		AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.batchWxPush(upCarrierOrders));
		return Result.success(true);

	}

	/**
	 * 新生运单卸货
	 *
	 * @param requestModel 卸货信息
	 */
	@Transactional
	public void unloadForYeloLife(UnLoadForYeloLifeRequestModel requestModel) {
		Map<Long, BigDecimal> pageUnLoadGoodsAmountMap = new HashMap<>();

		//校验入参数量，并统计提货数量
		if (requestModel.getCarrierOrderId() == null || ListUtils.isEmpty(requestModel.getGoodsList())) {
			throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
		}
		BigDecimal pageUnLoadAmount = BigDecimal.ZERO;
		for (UnloadGoodsForYeloLifeRequestModel itemGoods : requestModel.getGoodsList()) {
			BigDecimal tempGoodsCount = Optional.ofNullable(itemGoods.getUnloadAmount()).orElse(BigDecimal.ZERO);

			if (!RegExpValidatorUtil.isFloatNumber(tempGoodsCount.stripTrailingZeros().toPlainString())) {
				throw new BizException(CarrierDataExceptionEnum.EXPECT_AMOUNT_NOT_WEIGHT);
			}

			pageUnLoadGoodsAmountMap.put(itemGoods.getGoodsId(), itemGoods.getUnloadAmount());
			pageUnLoadAmount = pageUnLoadAmount.add(itemGoods.getUnloadAmount());
		}
		if (pageUnLoadAmount.compareTo(BigDecimal.ZERO) <= CommonConstant.INTEGER_ZERO) {
			throw new BizException(CarrierDataExceptionEnum.AMOUNT_ERROR);
		}

		//运单状态检查
		TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
		if (tCarrierOrder == null || IfValidEnum.INVALID.getKey().equals(tCarrierOrder.getValid())) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
		}
		//非新生单子不能操作
		if (!DemandOrderSourceEnum.YELO_LIFE.getKey().equals(tCarrierOrder.getDemandOrderSource())){
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
		}
		//不是待卸货状态不能操作
		if (!CarrierOrderStatusEnum.WAIT_UNLOAD.getKey().equals(tCarrierOrder.getStatus())) {
			throw new BizException(CarrierDataExceptionEnum.ONLY_WAIT_UNLOAD);
		}
		//已取消不能操作
		if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfCancel())) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL);
		}
		//运单放空不能操作
		if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfEmpty())) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EMPTY);
		}
		//卸货数量不能大于提货数量
		if (pageUnLoadAmount.compareTo(tCarrierOrder.getLoadAmount()) > 0) {
			throw new BizException(CarrierDataExceptionEnum.UNLOADING_AMOUNT_CANT_GT_LOAD_AMOUNT);
		}

		//校验车辆审核状态
		carrierOrderCommonBiz.checkIfCarrierOrdersStatusCanChange(Collections.singletonList(tCarrierOrder.getId()), CarrierDataExceptionEnum.VEHICLE_NEED_AUDIT_TIP2);

		//运单货物、销售单查询分组
		Map<Long, List<TCarrierOrderGoods>> dbTCarrierOrderGoodsMap = new HashMap<>();
		List<TCarrierOrderGoods> dbTCarrierOrderGoods = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(LocalStringUtil.listTostring(Collections.singletonList(tCarrierOrder.getId()), ','));
		if (ListUtils.isNotEmpty(dbTCarrierOrderGoods)) {
			dbTCarrierOrderGoodsMap = dbTCarrierOrderGoods.stream().collect(Collectors.groupingBy(TCarrierOrderGoods::getCarrierOrderId, Collectors.toList()));
		}
		//卸货数量不能大于提货数量
		List<TCarrierOrderGoods> dbGoodsList = dbTCarrierOrderGoodsMap.get(tCarrierOrder.getId());
		for (TCarrierOrderGoods goods : dbGoodsList) {
			if (pageUnLoadGoodsAmountMap.get(goods.getId()).compareTo(goods.getLoadAmount()) > 0) {
				throw new BizException(CarrierDataExceptionEnum.UNLOADING_AMOUNT_CANT_GT_LOAD_AMOUNT);
			}
		}
		List<Long> tCarrierOrderGoodIds = dbTCarrierOrderGoods.stream().map(TCarrierOrderGoods::getId).collect(Collectors.toList());

		//运单货物code
		Map<String, TCarrierOrderGoodsCode> tCarrierOrderGoodsCodesMap = new HashMap<>();
		if (CarrierOrderIfRecycleByCodeEnum.RECYCLE_BY_CODE.getCode().equals(tCarrierOrder.getIfRecycleByCode())) {
			List<TCarrierOrderGoodsCode> tCarrierOrderGoodsCodes = tCarrierOrderGoodsCodeMapper.selectGoodsCodeByGoodsIds(tCarrierOrderGoodIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
			tCarrierOrderGoodsCodesMap = tCarrierOrderGoodsCodes.stream().collect(Collectors.toMap(tCarrierOrderGoodsCode -> tCarrierOrderGoodsCode.getCarrierOrderGoodsId() + "-" + tCarrierOrderGoodsCode.getYeloGoodCode(), Function.identity()));
		}

		Date now = new Date();
		List<TCarrierOrder> upCarrierOrders = new ArrayList<>();
		List<TCarrierOrderGoods> upCarrierOrderGoods = new ArrayList<>();
		List<TCarrierOrderEvents> insertEvents = new ArrayList<>();
		List<TCarrierOrderOperateLogs> insertLogs = new ArrayList<>();
		List<TCarrierOrderTickets> addTicketsList = new ArrayList<>();

		TCarrierOrder upOrder = new TCarrierOrder();
		upOrder.setId(tCarrierOrder.getId());
		upOrder.setStatus(CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey());
		upOrder.setStatusUpdateTime(now);
		upOrder.setUnloadTime(now);
		upOrder.setUnloadAmount(pageUnLoadAmount);
		upOrder.setUnloadAmountExpect(pageUnLoadAmount);
		commonBiz.setBaseEntityModify(upOrder, BaseContextHandler.getUserName());
		upCarrierOrders.add(upOrder);


		TCarrierOrderGoods upOrderGood;
		List<TCarrierOrderGoods> tCarrierOrderGoods = dbTCarrierOrderGoodsMap.get(tCarrierOrder.getId());
		for (TCarrierOrderGoods good : tCarrierOrderGoods) {
			BigDecimal unloadAmount = pageUnLoadGoodsAmountMap.get(good.getId());
			//卸货数量大于提货数量
			if (unloadAmount.compareTo(good.getLoadAmount()) > CommonConstant.INTEGER_ZERO) {
				throw new BizException(CarrierDataExceptionEnum.LOAD_UNLOAD_AMOUNT_ERROR);
			}
			upOrderGood = new TCarrierOrderGoods();
			upOrderGood.setId(good.getId());
			upOrderGood.setUnloadAmount(unloadAmount);
			commonBiz.setBaseEntityModify(upOrderGood, BaseContextHandler.getUserName());
			upCarrierOrderGoods.add(upOrderGood);
		}

		//更新运单货物编码表卸货数量
		List<TCarrierOrderGoodsCode> updateCarrierOrderGoodsCodes = new ArrayList<>();
		for (UnloadGoodsForYeloLifeRequestModel goodsModel : requestModel.getGoodsList()) {
			if (CollectionUtil.isEmpty(goodsModel.getCodeDtoList())) {
				continue;
			}
			if (CarrierOrderIfRecycleByCodeEnum.RECYCLE_BY_CODE.getCode().equals(tCarrierOrder.getIfRecycleByCode())) {
				for (LoadGoodsForYeloLifeRequestCodeModel codeModel : goodsModel.getCodeDtoList()) {
					TCarrierOrderGoodsCode tCarrierOrderGoodsCode = tCarrierOrderGoodsCodesMap.get(goodsModel.getGoodsId() + "-" + codeModel.getYeloCode());
					if (tCarrierOrderGoodsCode == null) {
						throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_GOODS_CODE_NOT_EXIST);
					}
					TCarrierOrderGoodsCode updateCarrierOrderGoodsCode = new TCarrierOrderGoodsCode();
					updateCarrierOrderGoodsCode.setId(tCarrierOrderGoodsCode.getId());
					updateCarrierOrderGoodsCode.setUnloadAmount(codeModel.getWeight());
					updateCarrierOrderGoodsCode.setUnit(codeModel.getUnit());
					updateCarrierOrderGoodsCodes.add(updateCarrierOrderGoodsCode);
				}
			}
		}

		//处理现场图片
		TCarrierOrderTickets addTickets;
		if (ListUtils.isNotEmpty(requestModel.getSiteImgList())) {
			for (String imageUrl : requestModel.getSiteImgList()) {
				addTickets = new TCarrierOrderTickets();
				addTickets.setCarrierOrderId(tCarrierOrder.getId());
				addTickets.setImageName(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_SCENE_PIC.getValue());
				addTickets.setImagePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey(), tCarrierOrder.getCarrierOrderCode(), imageUrl, null));
				addTickets.setImageType(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_SCENE_PIC.getKey());
				addTickets.setUploadTime(now);
				addTickets.setUploadUserName(BaseContextHandler.getUserName());
				commonBiz.setBaseEntityAdd(addTickets, BaseContextHandler.getUserName());
				addTicketsList.add(addTickets);
			}
		}

		//处理签收单
		if (ListUtils.isNotEmpty(requestModel.getOutImgList())) {
			for (String imageUrl : requestModel.getOutImgList()) {
				addTickets = new TCarrierOrderTickets();
				addTickets.setCarrierOrderId(tCarrierOrder.getId());
				addTickets.setImageName(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getValue());
				addTickets.setImagePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey(), tCarrierOrder.getCarrierOrderCode(), imageUrl, null));
				addTickets.setImageType(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey());
				addTickets.setUploadTime(now);
				addTickets.setUploadUserName(BaseContextHandler.getUserName());
				commonBiz.setBaseEntityAdd(addTickets, BaseContextHandler.getUserName());
				addTicketsList.add(addTickets);
			}
		}

		//单位
		String unit = GoodsUnitEnum.getEnum(tCarrierOrder.getGoodsUnit()).getUnit();
		//生成运单事件
		TCarrierOrderEvents carrierOrderEvents = carrierOrderCommonBiz.getCarrierOrderEvent(upOrder.getId(), CarrierOrderEventsTypeEnum.UNLOADING, BaseContextHandler.getUserName(), CarrierOrderEventsTypeEnum.UNLOADING.format(StripTrailingZerosUtils.stripTrailingZerosToString(upOrder.getUnloadAmount()) + unit));
		insertEvents.add(carrierOrderEvents);
		//生成操作日志
		TCarrierOrderOperateLogs carrierOrderOperateLogs = carrierOrderCommonBiz.getCarrierOrderOperateLogs(upOrder.getId(), CarrierOrderOperateLogsTypeEnum.UNLOADING, BaseContextHandler.getUserName(), CarrierOrderOperateLogsTypeEnum.UNLOADING.format(StripTrailingZerosUtils.stripTrailingZerosToString(upOrder.getUnloadAmount()) + unit));
		insertLogs.add(carrierOrderOperateLogs);


		if (ListUtils.isNotEmpty(upCarrierOrders)) {
			tCarrierOrderMapper.batchUpdateCarrierOrders(upCarrierOrders);
		}
		if (ListUtils.isNotEmpty(upCarrierOrderGoods)) {
			tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(upCarrierOrderGoods);
		}
		if (CollectionUtil.isNotEmpty(updateCarrierOrderGoodsCodes)) {
			tCarrierOrderGoodsCodeMapper.batchUpdate(updateCarrierOrderGoodsCodes);
		}
		if (ListUtils.isNotEmpty(insertEvents)) {
			tCarrierOrderEventsMapper.batchInsertSelective(insertEvents);
		}
		if (ListUtils.isNotEmpty(insertLogs)) {
			tCarrierOrderOperateLogsMapper.batchInsertSelective(insertLogs);
		}
		if (ListUtils.isNotEmpty(addTicketsList)) {
			tCarrierOrderTicketsMapper.batchInsertTickets(addTicketsList);
		}

		//生成外部车辆结算数据
		extVehicleSettlementBiz.createExtVehicleSettlement(List.of(tCarrierOrder.getId()), false, false);

		//异步方法放在最后执行
		//节点信息推送微信公众号
		AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.batchWxPush(upCarrierOrders));
	}

	/**
	 * 查询运单列表(新生调用)
	 *
	 * @param requestModel 筛选条件
	 * @return 运单列表
	 */
	public PageInfo<SearchYeloLifeCarrierOrderResponseModel> searchYeloLifeCarrierOrder(SearchYeloLifeCarrierOrderRequestModel requestModel) {
		//查询条件含有司机车辆信息
		Map<Long, TCarrierOrderVehicleHistory> carrierOrderVehicleHistoryMap = new HashMap<>();
		if (StringUtils.isNotBlank(requestModel.getVehicleNo())){
			//查询地址信息
			SearchCarrierOrderVehicleHistoryModel vehicleHistoryModel = MapperUtils.mapper(requestModel, SearchCarrierOrderVehicleHistoryModel.class);
			List<TCarrierOrderVehicleHistory> vehicleHistoryList = tCarrierOrderVehicleHistoryMapper.searchListByCarrierOrderParams(vehicleHistoryModel);
			//不存在，直接返回
			if (ListUtils.isEmpty(vehicleHistoryList)) {
				return new PageInfo<>(new ArrayList<>());
			}
			//存在，数据聚合处理
			List<Long> vehicleCarrierOrderIdList = new ArrayList<>();
			for (TCarrierOrderVehicleHistory vehicleHistory : vehicleHistoryList) {
				carrierOrderVehicleHistoryMap.put(vehicleHistory.getCarrierOrderId(), vehicleHistory);
				if (!vehicleCarrierOrderIdList.contains(vehicleHistory.getCarrierOrderId())) {
					vehicleCarrierOrderIdList.add(vehicleHistory.getCarrierOrderId());
				}
			}
			requestModel.setCarrierOrderIdList(vehicleCarrierOrderIdList);
		}

		//分页查询运单信息
		requestModel.enablePaging();
		List<Long> carrierOrderIdList = tCarrierOrderMapper.searchYeloLifeCarrierOrderId(requestModel);
		PageInfo carrierOrderList = new PageInfo(carrierOrderIdList);

		if (ListUtils.isNotEmpty(carrierOrderIdList)) {
			String carrierOrderIds = StringUtils.listToString(carrierOrderIdList, ',');
			//根据运单id查询运单信息
			List<SearchYeloLifeCarrierOrderResponseModel> searchCarrierOrderListResponseModels = tCarrierOrderMapper.searchYeloLifeCarrierOrder(carrierOrderIds);

			//不存在车辆相关筛选条件，则根据运单id查询运单司机车辆信息
			if (MapUtils.isEmpty(carrierOrderVehicleHistoryMap)) {
				List<TCarrierOrderVehicleHistory> carrierOrderVehicleHistoryList = tCarrierOrderVehicleHistoryMapper.getValidByCarrierOrderIds(carrierOrderIds);
				carrierOrderVehicleHistoryList.forEach(item -> carrierOrderVehicleHistoryMap.put(item.getCarrierOrderId(), item));
			}

			//查询运单签收单
			List<TCarrierOrderTickets> tCarrierOrderTicketsList = tCarrierOrderTicketsMapper.selectTicketsByCarrierOrderIdsAndType(carrierOrderIds, CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey());
			Map<Long, List<String>> signTicketsMap = tCarrierOrderTicketsList.stream().collect(Collectors.groupingBy(TCarrierOrderTickets::getCarrierOrderId, Collectors.mapping(TCarrierOrderTickets::getImagePath, Collectors.toList())));

			//获取oss图片访问路径
			Map<String, String> ossFileUrlMap = commonBiz.batchGetOSSFileUrl(tCarrierOrderTicketsList.stream().map(TCarrierOrderTickets::getImagePath).collect(Collectors.toList()));

			//拼接数据
			TCarrierOrderVehicleHistory tCarrierOrderVehicleHistory;
			for (SearchYeloLifeCarrierOrderResponseModel modelItem : searchCarrierOrderListResponseModels) {
				//车辆司机信息
				tCarrierOrderVehicleHistory = carrierOrderVehicleHistoryMap.get(modelItem.getCarrierOrderId());
				if (tCarrierOrderVehicleHistory != null) {
					modelItem.setVehicleNo(tCarrierOrderVehicleHistory.getVehicleNo());
					modelItem.setDriverName(tCarrierOrderVehicleHistory.getDriverName());
					modelItem.setDriverMobile(tCarrierOrderVehicleHistory.getDriverMobile());
				}

				//取消状态转换
				if (CommonConstant.INTEGER_ONE.equals(modelItem.getIfCancel())) {
					modelItem.setStatus(CommonConstant.INTEGER_ZERO);
				}

				//计算货主费用
				if (PriceTypeEnum.UNIT_PRICE.getKey().equals(modelItem.getEntrustFreightType())){
					modelItem.setEntrustFreightFee(modelItem.getEntrustFreight().multiply(this.getGoodsAmount(modelItem.getEntrustSettlementTonnage(), modelItem)).setScale(2, RoundingMode.HALF_UP));
				}else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(modelItem.getEntrustFreightType())) {
					modelItem.setEntrustFreightFee(modelItem.getEntrustFreight());
				}

				//签收单
				List<String> signPic = new ArrayList<>();
				List<String> signTickets = signTicketsMap.get(modelItem.getCarrierOrderId());
				if (ListUtils.isNotEmpty(signTickets)){
					for (String path : signTickets) {
						signPic.add(configKeyConstant.fileAccessAddress + ossFileUrlMap.get(path));
					}
				}
				modelItem.setSignPic(signPic);
			}
			carrierOrderList.setList(searchCarrierOrderListResponseModels);
		}
		return carrierOrderList;
	}
	/**
	 * 根据不同结算方式获取货物数量
	 *
	 * @param settlementTonnage 结算方式
	 * @param modelItem     运单信息
	 * @return 货物数量
	 */
	private BigDecimal getGoodsAmount(Integer settlementTonnage, SearchYeloLifeCarrierOrderResponseModel modelItem) {
		BigDecimal resultAmount = BigDecimal.ZERO;
		if (SettlementTonnageEnum.LOAD.getKey().equals(settlementTonnage)) {
			resultAmount = modelItem.getLoadAmount();
		} else if (SettlementTonnageEnum.UNLOAD.getKey().equals(settlementTonnage)) {
			resultAmount = modelItem.getUnloadAmount();
		} else if (SettlementTonnageEnum.SIGN.getKey().equals(settlementTonnage)) {
			resultAmount = modelItem.getSignAmount();
		} else if (SettlementTonnageEnum.EXPECT.getKey().equals(settlementTonnage)) {
			resultAmount = modelItem.getExpectAmount();
		}
		if (resultAmount.compareTo(BigDecimal.ZERO) == CommonConstant.INTEGER_ZERO) {
			resultAmount = modelItem.getExpectAmount();
		}
		return resultAmount;
	}

	/**
	 * 查询运单详情(新生调用)
	 *
	 * @param requestModel 运单id
	 * @return 运单详情
	 */
	public SearchYeloLifeCarrierOrderDetailResponseModel searchYeloLifeCarrierOrderDetail(SearchYeloLifeCarrierOrderDetailRequestModel requestModel) {
		//根据运单id查询运单详情
		SearchYeloLifeCarrierOrderDetailResponseModel carrierOrderDetail = tCarrierOrderMapper.searchYeloLifeCarrierOrderDetail(requestModel);
		if (carrierOrderDetail == null) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
		}
		//查询当前有效的运单车辆信息
		TCarrierOrderVehicleHistory carrierOrderVehicleHistory = tCarrierOrderVehicleHistoryMapper.getValidTopByCarrierOrderId(carrierOrderDetail.getCarrierOrderId());
		//设置车辆和司机信息
		carrierOrderDetail.setDriverName(carrierOrderVehicleHistory.getDriverName());
		carrierOrderDetail.setDriverMobile(carrierOrderVehicleHistory.getDriverMobile());
		carrierOrderDetail.setVehicleNo(carrierOrderVehicleHistory.getVehicleNo());

		List<TicketsModel> ticketsModelsList = new ArrayList<>();
		//查询提货票据
		List<GetTicketsResponseModel> carrierOrderTicketList = tCarrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(carrierOrderDetail.getCarrierOrderId(), CarrierOrderTicketsTypeEnum.CARRIER_ORDER_UNLOAD_TICKETS.getKey());
		if (ListUtils.isNotEmpty(carrierOrderTicketList)) {
			//获取oss图片访问路径
			Map<String, String> ossFileUrlMap = commonBiz.batchGetOSSFileUrl(carrierOrderTicketList.stream().map(GetTicketsResponseModel::getImagePath).collect(Collectors.toList()));

			//数据转换
			TicketsModel ticketsModel;
			for (GetTicketsResponseModel getTicketsResponseModel : carrierOrderTicketList) {
				ticketsModel = new TicketsModel();
				ticketsModel.setTicketsId(getTicketsResponseModel.getImageId());
				ticketsModel.setCarrierOrderId(carrierOrderDetail.getCarrierOrderId());
				ticketsModel.setImagePath(configKeyConstant.fileAccessAddress + ossFileUrlMap.get(getTicketsResponseModel.getImagePath()));
				ticketsModel.setImageName(getTicketsResponseModel.getImageName());
				ticketsModel.setUploadUserName(getTicketsResponseModel.getUploadUserName());
				ticketsModel.setUploadTime(getTicketsResponseModel.getUploadTime());
				ticketsModel.setImageType(getTicketsResponseModel.getImageType());
				ticketsModelsList.add(ticketsModel);
			}
		}
		carrierOrderDetail.setTicketList(ticketsModelsList);

		//已取消
		if (CommonConstant.INTEGER_ONE.equals(carrierOrderDetail.getIfCancel())) {
			carrierOrderDetail.setStatus(CommonConstant.INTEGER_ZERO);
		}


		List<Long> goodIds = carrierOrderDetail.getGoodsList().stream().map(SearchYeloLifeCarrierOrderGoodsModel::getGoodId).collect(Collectors.toList());
		Map<Long, SearchYeloLifeCarrierOrderGoodsModel> maps = carrierOrderDetail.getGoodsList()
				.stream().collect(Collectors.toMap(SearchYeloLifeCarrierOrderGoodsModel::getGoodId, Function.identity(), (key1, key2) -> key2));


		List<TCarrierOrderGoodsCode> tCarrierOrderGoodsCodes = tCarrierOrderGoodsCodeMapper.selectGoodsCodeByGoodsIds(StringUtils.listToString(goodIds, ','));
		// 重新构造goodList
		if (ListUtils.isNotEmpty(tCarrierOrderGoodsCodes)){
			List<SearchYeloLifeCarrierOrderGoodsModel> goodsModelList = new ArrayList<>();
			tCarrierOrderGoodsCodes.forEach(e->{
				if (maps.get(e.getCarrierOrderGoodsId()) != null){
					SearchYeloLifeCarrierOrderGoodsModel mapper = new SearchYeloLifeCarrierOrderGoodsModel();
					mapper.setGoodId(maps.get(e.getCarrierOrderGoodsId()).getGoodId());
					mapper.setSkuName(maps.get(e.getCarrierOrderGoodsId()).getSkuName());
					mapper.setExpectAmount(maps.get(e.getCarrierOrderGoodsId()).getExpectAmount());
					mapper.setRecycleBagCode(e.getYeloGoodCode());
					mapper.setLoadAmount(e.getLoadAmount());
					mapper.setUnloadAmount(e.getUnloadAmount());
					mapper.setSignAmount(e.getSignAmount());
					mapper.setUnit(e.getUnit());
					goodsModelList.add(mapper);
				}
			});
			carrierOrderDetail.setGoodsList(goodsModelList);
		}
		return carrierOrderDetail;
	}

	/**
	 * 查询签收确认详情-回收类型
	 *
	 * @param requestModel 运单ID
	 * @return 签收确认详情
	 */
	public SignDetailForYeloLifeResponseModel carrierOrderListBeforeSignUpForYeloLife(CarrierOrderIdRequestModel requestModel) {
		SignDetailForYeloLifeResponseModel responseModel = tCarrierOrderMapper.carrierOrderListBeforeSignUpForYeloLife(requestModel.getCarrierOrderId());
		if (responseModel == null) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
		}
		//非待签收不能操作
		if (!CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey().equals(responseModel.getStatus())) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_NOT_ALLOW_SIGN);
		}
		//运单取消不能操作
		if (CommonConstant.INTEGER_ONE.equals(responseModel.getIfCancel())) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_NOT_ALLOW_SIGN);
		}
		//非新生回收不能操作
		if (!EntrustTypeEnum.LIFE_TRANSFER.getKey().equals(responseModel.getEntrustType())) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_NOT_ALLOW_SIGN);
		}

		//查询当前有效的运单车辆信息
		TCarrierOrderVehicleHistory carrierOrderVehicleHistory = tCarrierOrderVehicleHistoryMapper.getValidTopByCarrierOrderId(responseModel.getCarrierOrderId());
		//设置车辆和司机信息
		responseModel.setDriverName(carrierOrderVehicleHistory.getDriverName());
		responseModel.setDriverMobile(carrierOrderVehicleHistory.getDriverMobile());
		responseModel.setVehicleNo(carrierOrderVehicleHistory.getVehicleNo());

		//查询车主信息
		TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(responseModel.getCompanyCarrierId());
		responseModel.setIsOurCompany(tCompanyCarrier.getLevel());


		//编码重量置为2位小数
		for (CarrierOrderGoodsResponseModel coodModel : responseModel.getGoodsList()) {
			for (CarrierOrderGoodsCodeResponseModel carrierOrderGoodsCodeResponseModel : coodModel.getCodeDtoList()) {
				carrierOrderGoodsCodeResponseModel.setWeight(carrierOrderGoodsCodeResponseModel.getWeight().setScale(2, RoundingMode.HALF_UP));
			}
		}


		return responseModel;
	}

	/**
	 * 签收-回收类型
	 *
	 * @param requestModel 签收信息
	 */
	public void carrierOrderSignUpForYeloLife(CarrierOrderSignUpForYeloLifeRequestModel requestModel) {
		//判断运单是否存在
		TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
		if (tCarrierOrder == null || IfValidEnum.INVALID.getKey().equals(tCarrierOrder.getValid())) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
		}
		//非新生单子不能操作
		if (!DemandOrderSourceEnum.YELO_LIFE.getKey().equals(tCarrierOrder.getDemandOrderSource())){
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
		}
		//非待签收不能操作
		if (!CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey().equals(tCarrierOrder.getStatus())) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_NOT_ALLOW_SIGN);
		}
		//运单取消不能操作
		if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfCancel())) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_NOT_ALLOW_SIGN);
		}
		//非新生回收不能操作
		if (!EntrustTypeEnum.LIFE_TRANSFER.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_NOT_ALLOW_SIGN);
		}

		TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(tCarrierOrder.getCompanyCarrierId());
		if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(tCompanyCarrier.getLevel())){
			if (requestModel.getDispatchFreightFee() == null){
				throw new BizException(CarrierDataExceptionEnum.DRIVER_FEE_EMPTY);
			}
		}else{
			requestModel.setDispatchFreightFee(null);
		}

		//查询运单货物信息
		Map<Long, TCarrierOrderGoods> tCarrierOrderGoodsMap = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderId(tCarrierOrder.getId()).stream().collect(Collectors.toMap(TCarrierOrderGoods::getId, item -> item));
		String TCarrierOrderGoodIdsJoin = tCarrierOrderGoodsMap.keySet().stream().map(Objects::toString).collect(Collectors.joining(","));

		//查询运单货物编码信息
		Map<String, TCarrierOrderGoodsCode> tCarrierOrderGoodsCodesMap = new HashMap<>();
		if (CarrierOrderIfRecycleByCodeEnum.RECYCLE_BY_CODE.getCode().equals(tCarrierOrder.getIfRecycleByCode())) {
			List<TCarrierOrderGoodsCode> tCarrierOrderGoodsCodes = tCarrierOrderGoodsCodeMapper.selectGoodsCodeByGoodsIds(TCarrierOrderGoodIdsJoin);
			tCarrierOrderGoodsCodesMap = tCarrierOrderGoodsCodes.stream().collect(Collectors.toMap(tCarrierOrderGoodsCode -> tCarrierOrderGoodsCode.getCarrierOrderGoodsId()+"-"+tCarrierOrderGoodsCode.getYeloGoodCode(),Function.identity() ));
		}

		Date now = new Date();
		TCarrierOrder upCarrierOrder;
		List<TCarrierOrder> batchUpdateCarrierOrders = new ArrayList<>();
		TCarrierOrderGoods upGoods;
		List<TCarrierOrderGoods> batchUpdateCarrierGoods = new ArrayList<>();
		List<TCarrierOrderGoodsCode> batchUpdateCarrierGoodsCodes = new ArrayList<>();
		TCarrierOrderEvents carrierOrderEvents;
		List<TCarrierOrderEvents> insertEvents = new ArrayList<>();
		TCarrierOrderOperateLogs carrierOrderOperateLogs;
		List<TCarrierOrderOperateLogs> insertLogs = new ArrayList<>();
		List<Long> upExtDriverFeeCarrierOrderIdList = new ArrayList<>();
		Map<Long, BigDecimal> upExtDriverFeeMap = new HashMap<>();//我司时司机费用：运单id-》司机费用

		//签收备注
		String signRemark = "，修改签收时间为" + DateUtils.dateToString(now, "yyyy年MM月dd日");
		//签收总数量
		BigDecimal signAmountTotal = CommonConstant.BIG_DECIMAL_ZERO;
		//更新货物
		for (SignGoodsForYeloLifeRequestModel goods : requestModel.getGoodsList()) {
			TCarrierOrderGoods tCarrierOrderGoods = tCarrierOrderGoodsMap.get(goods.getGoodsId());
			if (tCarrierOrderGoods == null) {
				throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_GOODS_EMPTY);
			}
			//签收数量大于卸货数量
			if (goods.getSignAmount().compareTo(tCarrierOrderGoods.getUnloadAmount()) > CommonConstant.INTEGER_ZERO) {
				throw new BizException(CarrierDataExceptionEnum.LOAD_UNLOAD_AMOUNT_ERROR);
			}
			upGoods = new TCarrierOrderGoods();
			upGoods.setId(goods.getGoodsId());
			upGoods.setSignAmount(goods.getSignAmount());
			commonBiz.setBaseEntityModify(upGoods, BaseContextHandler.getUserName());
			batchUpdateCarrierGoods.add(upGoods);

			//更新货物编码
			for (CarrierOrderSignUpForYeloLifeCodeModel codeModel : Optional.ofNullable(goods.getCodeDtoList()).orElse(new ArrayList<>())) {
				TCarrierOrderGoodsCode tCarrierOrderGoodsCode = tCarrierOrderGoodsCodesMap.get(tCarrierOrderGoods.getId() + "-" + codeModel.getYeloCode());
				if (tCarrierOrderGoodsCode == null) {
					throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_GOODS_CODE_NOT_EXIST);
				}
				TCarrierOrderGoodsCode updateCarrierOrderGoodsCode = new TCarrierOrderGoodsCode();
				updateCarrierOrderGoodsCode.setId(tCarrierOrderGoodsCode.getId());
				updateCarrierOrderGoodsCode.setSignAmount(codeModel.getWeight());
				updateCarrierOrderGoodsCode.setUnit(codeModel.getUnit());
				batchUpdateCarrierGoodsCodes.add(updateCarrierOrderGoodsCode);
			}

			signAmountTotal = signAmountTotal.add(goods.getSignAmount());
		}

		//更新运单
		upCarrierOrder = new TCarrierOrder();
		upCarrierOrder.setId(tCarrierOrder.getId());
		upCarrierOrder.setStatus(CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey());
		upCarrierOrder.setStatusUpdateTime(now);
		upCarrierOrder.setSignTime(now);
		upCarrierOrder.setSignAmount(signAmountTotal);
		//货主价格,一口价
		upCarrierOrder.setEntrustFreightType(FreightTypeEnum.FIXED_PRICE.getKey());
		upCarrierOrder.setEntrustFreight(requestModel.getEntrustFreightFee());
		//司机价格,一口价
		if (requestModel.getDispatchFreightFee() != null) {
			upCarrierOrder.setDispatchFreightFeeType(FreightTypeEnum.FIXED_PRICE.getKey());
			upCarrierOrder.setDispatchFreightFee(requestModel.getDispatchFreightFee());

			upExtDriverFeeCarrierOrderIdList.add(tCarrierOrder.getId());
			upExtDriverFeeMap.put(tCarrierOrder.getId(), upCarrierOrder.getDispatchFreightFee());
		}
		//车主价格,一口价
		upCarrierOrder.setCarrierPriceType(FreightTypeEnum.FIXED_PRICE.getKey());
		upCarrierOrder.setCarrierPrice(requestModel.getCarrierFreight());
		commonBiz.setBaseEntityModify(upCarrierOrder, BaseContextHandler.getUserName());
		batchUpdateCarrierOrders.add(upCarrierOrder);

		//单位
		String unit = GoodsUnitEnum.getEnum(tCarrierOrder.getGoodsUnit()).getUnit();
		//生成运单事件
		carrierOrderEvents = new TCarrierOrderEvents();
		carrierOrderEvents.setCarrierOrderId(tCarrierOrder.getId());
		carrierOrderEvents.setEvent(CarrierOrderEventsTypeEnum.SIGN_IN.getKey());
		carrierOrderEvents.setEventDesc(CarrierOrderEventsTypeEnum.SIGN_IN.getValue());
		carrierOrderEvents.setRemark(CarrierOrderEventsTypeEnum.SIGN_IN.format(StripTrailingZerosUtils.stripTrailingZerosToString(upCarrierOrder.getSignAmount()) + unit));
		carrierOrderEvents.setEventTime(now);
		carrierOrderEvents.setOperatorName(BaseContextHandler.getUserName());
		carrierOrderEvents.setOperateTime(now);
		commonBiz.setBaseEntityAdd(carrierOrderEvents, BaseContextHandler.getUserName());
		insertEvents.add(carrierOrderEvents);

		//生成操作日志
		carrierOrderOperateLogs = new TCarrierOrderOperateLogs();
		carrierOrderOperateLogs.setCarrierOrderId(tCarrierOrder.getId());
		carrierOrderOperateLogs.setOperationType(CarrierOrderOperateLogsTypeEnum.SIGN_IN.getKey());
		carrierOrderOperateLogs.setOperationContent(CarrierOrderOperateLogsTypeEnum.SIGN_IN.getValue());
		carrierOrderOperateLogs.setOperatorName(BaseContextHandler.getUserName());
		carrierOrderOperateLogs.setOperateTime(now);
		carrierOrderOperateLogs.setRemark(CarrierOrderOperateLogsTypeEnum.SIGN_IN.format(StripTrailingZerosUtils.stripTrailingZerosToString(upCarrierOrder.getSignAmount()) + unit, ConverterUtils.toString(requestModel.getEntrustFreightFee())) + signRemark);
		commonBiz.setBaseEntityAdd(carrierOrderOperateLogs, BaseContextHandler.getUserName());
		insertLogs.add(carrierOrderOperateLogs);

		if (ListUtils.isNotEmpty(batchUpdateCarrierOrders)) {
			tCarrierOrderMapper.batchUpdateCarrierOrders(batchUpdateCarrierOrders);
		}
		if (ListUtils.isNotEmpty(batchUpdateCarrierGoods)) {
			tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(batchUpdateCarrierGoods);
		}
		if (ListUtils.isNotEmpty(batchUpdateCarrierGoodsCodes)) {
			tCarrierOrderGoodsCodeMapper.batchUpdate(batchUpdateCarrierGoodsCodes);
		}
		if (ListUtils.isNotEmpty(insertEvents)) {
			tCarrierOrderEventsMapper.batchInsertSelective(insertEvents);
		}
		if (ListUtils.isNotEmpty(insertLogs)) {
			tCarrierOrderOperateLogsMapper.batchInsertSelective(insertLogs);
		}

		//更新外部车辆结算司机费用
		if (ListUtils.isNotEmpty(upExtDriverFeeCarrierOrderIdList)){
			extVehicleSettlementBiz.updateDriverFee(upExtDriverFeeCarrierOrderIdList, upExtDriverFeeMap);
		}

		//根据运单所属需求单Id查询需求单状态
		List<Long> demandOrderIdList = Collections.singletonList(tCarrierOrder.getDemandOrderId());
		List<GetDemandOrderInfoByIdsModel> demandOrderInfoList = tDemandOrderMapper.getDemandOrderInfoByIds(StringUtils.listToString(demandOrderIdList, ','));
		List<Long> demandOrderIds = new ArrayList<>();
		demandOrderInfoList.forEach(demandOrder -> {
			if (demandOrder.getEntrustStatus() < DemandOrderStatusEnum.SIGN_DISPATCH.getKey()) {
				demandOrderIds.add(demandOrder.getDemandOrderId());
			}
		});
		if (ListUtils.isNotEmpty(demandOrderIds)) {
			//如果需求单下所有运单（除已取消的）均为已签收状态，则判断是否将需求单置为已签收状态
			List<DemandCarrierOrderRecursiveModel> demandCarrierOrderList = tCarrierOrderMapper.getNotCancelByDemandOrderIds(StringUtils.listToString(demandOrderIds, ','));
			List<Long> demandIds = new ArrayList<>();
			demandCarrierOrderList.forEach(order -> {
				DemandCarrierOrderModel demandCarrierOrderModel = order.getCarrierOrderList().stream().filter(carrierOrder -> !CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(carrierOrder.getStatus())).findFirst().orElse(null);
				if (demandCarrierOrderModel == null) {
					demandIds.add(order.getDemandOrderId());
				}
			});

			if (ListUtils.isNotEmpty(demandIds)) {
				//修改需求单状态
				UpdateDemandOrderStatusByIdsRequestModel updateDemandOrderStatusByIdsRequestModel = new UpdateDemandOrderStatusByIdsRequestModel();
				updateDemandOrderStatusByIdsRequestModel.setDemandOrderIdList(demandIds);
				updateDemandOrderStatusByIdsRequestModel.setEntrustStatus(DemandOrderStatusEnum.SIGN_DISPATCH.getKey());
				updateDemandOrderStatusByIdsRequestModel.setOperatorName(BaseContextHandler.getUserName());
				demandOrderBiz.updateDemandOrderStatusByIds(updateDemandOrderStatusByIdsRequestModel);
			}
		}
		//没有预计里程数,更新预计里程数
		if (!(tCarrierOrder.getExpectMileage() != null
				&& tCarrierOrder.getExpectMileage().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO)) {
			AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.carrierOrderMileage(Collections.singletonList(tCarrierOrder.getId()), null));
		}
	}

	/**
	 * 查询签收详情-销售类型
	 *
	 * @param requestModel 运单id
	 * @return 签收详情
	 */
	public List<CarrierOrderBeforeSignUpForYeloLifeResponseModel> carrierOrderBeforeSignUpForYeloLife(CarrierOrderBeforeSignUpForYeloLifeRequestModel requestModel) {
		List<Long> carrierOrderIdList = requestModel.getCarrierOrderIdList();
		//判断车辆是否处于未审核状态
		carrierOrderCommonBiz.checkIfCarrierOrdersStatusCanChange(carrierOrderIdList, CarrierDataExceptionEnum.VEHICLE_NEED_AUDIT_TIP1);

		//查询运单信息
		String carrierOrderIds = StringUtils.listToString(carrierOrderIdList, ',');
		List<TCarrierOrder> dbCarrierOrderList = tCarrierOrderMapper.selectCarrierOrdersByIds(StringUtils.listToString(carrierOrderIdList, ','));
		//校验是否能签收
		checkBySignUp(dbCarrierOrderList);

		//查询车主信息
		List<Long> companyCarrierIdList = dbCarrierOrderList.stream().map(TCarrierOrder::getCompanyCarrierId).distinct().collect(Collectors.toList());
		Map<Long, Integer> companyLevelMap = tCompanyCarrierMapper.getByIds(StringUtils.listToString(companyCarrierIdList, ',')).stream().collect(Collectors.toMap(TCompanyCarrier::getId, TCompanyCarrier::getLevel));

		//查询签收详情
		List<CarrierOrderBeforeSignUpForYeloLifeResponseModel> carrierOrderList = tCarrierOrderMapper.selectCarrierOrderSignDetailForYeloLife(carrierOrderIds);
		//组装信息
		for (CarrierOrderBeforeSignUpForYeloLifeResponseModel signDetails : carrierOrderList) {
			//是否是我司
			signDetails.setIsOurCompany(companyLevelMap.get(signDetails.getCompanyCarrierId()));
		}
		return carrierOrderList;
	}
	private void checkBySignUp(List<TCarrierOrder> tCarrierOrderList){
		List<Integer> goodsUnitList = new ArrayList<>();
		for (TCarrierOrder tCarrierOrder : tCarrierOrderList) {
			//新生的运单才能操作
			if (!DemandOrderSourceEnum.YELO_LIFE.getKey().equals(tCarrierOrder.getDemandOrderSource())) {
				throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
			}
			if (!CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey().equals(tCarrierOrder.getStatus())) {
				throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_NOT_ALLOW_SIGN);
			}
			//运单取消不能操作
			if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfCancel())) {
				throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_NOT_ALLOW_SIGN);
			}
			//非新生销售不能操作
			if (!EntrustTypeEnum.LIFE_SALE.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())) {
				throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_NOT_ALLOW_SIGN);
			}

			//货物单位
			if (!goodsUnitList.contains(tCarrierOrder.getGoodsUnit())) {
				goodsUnitList.add(tCarrierOrder.getGoodsUnit());
			}
		}
		//不同单位的不能一起操作
		if (ListUtils.isNotEmpty(goodsUnitList) && goodsUnitList.size() > CommonConstant.INTEGER_ONE) {
			throw new BizException(CarrierDataExceptionEnum.GOODS_UNIT_DIFFERENT);
		}
	}

	/**
	 * 运单签收-销售类型
	 *
	 * @param requestModel 运单id,签收信息
	 * @return 操作结果
	 */
	@Transactional
	public void carrierOrderConfirmSignUpForYeloLife(CarrierOrderConfirmSignUpForYeloLifeRequestModel requestModel) {
		List<Long> carrierOrderIdList = requestModel.getSignList().stream().map(CarrierOrderConfirmSignUpListForYeloLifeRequestModel::getCarrierOrderId).collect(Collectors.toList());
		//查询运单信息
		String carrierOrderIds = StringUtils.listToString(carrierOrderIdList, ',');
		List<TCarrierOrder> dbCarrierOrderList = tCarrierOrderMapper.selectCarrierOrdersByIds(StringUtils.listToString(carrierOrderIdList, ','));
		//校验是否能签收
		checkBySignUp(dbCarrierOrderList);

		Map<Long, TCarrierOrder> carrierOrderSignMap = new HashMap<>();
		List<Long> demandOrderIdList = new ArrayList<>();
		for (TCarrierOrder tCarrierOrder : dbCarrierOrderList) {
			carrierOrderSignMap.put(tCarrierOrder.getId(), tCarrierOrder);
			if (!demandOrderIdList.contains(tCarrierOrder.getDemandOrderId())){
				demandOrderIdList.add(tCarrierOrder.getDemandOrderId());
			}
		}
		//查询运单货物
		Map<Long, List<TCarrierOrderGoods>> carrierOrderGoodsSignMap = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(carrierOrderIds).stream().collect(Collectors.groupingBy(TCarrierOrderGoods::getCarrierOrderId));

		Date now = new Date();
		TCarrierOrder upCarrierOrder;
		List<TCarrierOrder> batchUpdateCarrierOrders = new ArrayList<>();
		TCarrierOrderGoods upGoods;
		List<TCarrierOrderGoods> batchUpdateCarrierGoods = new ArrayList<>();
		TCarrierOrderEvents carrierOrderEvents;
		List<TCarrierOrderEvents> insertEvents = new ArrayList<>();
		TCarrierOrderOperateLogs carrierOrderOperateLogs;
		List<TCarrierOrderOperateLogs> insertLogs = new ArrayList<>();
		String unit;
		//签收时间
		Date signTime = requestModel.getSignTime() == null ? now : requestModel.getSignTime();
		//签收备注
		String signRemark = "";
		if (signTime != null) {
			signRemark = "，修改签收时间为" + DateUtils.dateToString(signTime, "yyyy年MM月dd日");
		}
		List<Long> updateCarrierOrderMileageIdList = new ArrayList<>();

		for (CarrierOrderConfirmSignUpListForYeloLifeRequestModel carrierOrderSignInfo : requestModel.getSignList()) {
			TCarrierOrder carrierOrderSignModel = carrierOrderSignMap.get(carrierOrderSignInfo.getCarrierOrderId());
			if (carrierOrderSignModel != null) {
				//没有预计里程数,更新预计里程数
				if (!(carrierOrderSignModel.getExpectMileage() != null
						&& carrierOrderSignModel.getExpectMileage().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO)) {
					updateCarrierOrderMileageIdList.add(carrierOrderSignInfo.getCarrierOrderId());
				}

				List<TCarrierOrderGoods> tCarrierOrderGoods = carrierOrderGoodsSignMap.get(carrierOrderSignInfo.getCarrierOrderId());
				//更新货物
				for (TCarrierOrderGoods goods : tCarrierOrderGoods) {
					upGoods = new TCarrierOrderGoods();
					upGoods.setId(goods.getId());
					upGoods.setSignAmount(goods.getUnloadAmount());
					commonBiz.setBaseEntityModify(upGoods, BaseContextHandler.getUserName());
					batchUpdateCarrierGoods.add(upGoods);
				}

				//更新运单
				upCarrierOrder = new TCarrierOrder();
				upCarrierOrder.setId(carrierOrderSignInfo.getCarrierOrderId());
				upCarrierOrder.setStatus(CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey());
				upCarrierOrder.setStatusUpdateTime(now);
				upCarrierOrder.setSignTime(signTime);
				upCarrierOrder.setSignAmount(carrierOrderSignModel.getUnloadAmount());
				upCarrierOrder.setCarrierPriceType(FreightTypeEnum.FIXED_PRICE.getKey());
				upCarrierOrder.setCarrierPrice(carrierOrderSignInfo.getSignCarrierFreight());
				commonBiz.setBaseEntityModify(upCarrierOrder, BaseContextHandler.getUserName());
				batchUpdateCarrierOrders.add(upCarrierOrder);

				unit = GoodsUnitEnum.getEnum(carrierOrderSignModel.getGoodsUnit()).getUnit();
				//生成运单事件
				carrierOrderEvents = new TCarrierOrderEvents();
				carrierOrderEvents.setCarrierOrderId(carrierOrderSignInfo.getCarrierOrderId());
				carrierOrderEvents.setEvent(CarrierOrderEventsTypeEnum.SIGN_IN.getKey());
				carrierOrderEvents.setEventDesc(CarrierOrderEventsTypeEnum.SIGN_IN.getValue());
				carrierOrderEvents.setRemark(CarrierOrderEventsTypeEnum.SIGN_IN.format(StripTrailingZerosUtils.stripTrailingZerosToString(upCarrierOrder.getSignAmount()) + unit));
				carrierOrderEvents.setEventTime(now);
				carrierOrderEvents.setOperatorName(BaseContextHandler.getUserName());
				carrierOrderEvents.setOperateTime(now);
				commonBiz.setBaseEntityAdd(carrierOrderEvents, BaseContextHandler.getUserName());
				insertEvents.add(carrierOrderEvents);

				//生成操作日志
				carrierOrderOperateLogs = new TCarrierOrderOperateLogs();
				carrierOrderOperateLogs.setCarrierOrderId(carrierOrderSignInfo.getCarrierOrderId());
				carrierOrderOperateLogs.setOperationType(CarrierOrderOperateLogsTypeEnum.SIGN_IN.getKey());
				carrierOrderOperateLogs.setOperationContent(CarrierOrderOperateLogsTypeEnum.SIGN_IN.getValue());
				carrierOrderOperateLogs.setOperatorName(BaseContextHandler.getUserName());
				carrierOrderOperateLogs.setOperateTime(now);
				carrierOrderOperateLogs.setRemark(CarrierOrderOperateLogsTypeEnum.SIGN_IN.format(StripTrailingZerosUtils.stripTrailingZerosToString(upCarrierOrder.getSignAmount()) + unit, ConverterUtils.toString(carrierOrderSignInfo.getActualEntrustFee())) + signRemark);
				commonBiz.setBaseEntityAdd(carrierOrderOperateLogs, BaseContextHandler.getUserName());
				insertLogs.add(carrierOrderOperateLogs);
			}
		}
		if (ListUtils.isNotEmpty(batchUpdateCarrierOrders)) {
			tCarrierOrderMapper.batchUpdateCarrierOrders(batchUpdateCarrierOrders);
		}
		if (ListUtils.isNotEmpty(batchUpdateCarrierGoods)) {
			tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(batchUpdateCarrierGoods);
		}
		if (ListUtils.isNotEmpty(insertEvents)) {
			tCarrierOrderEventsMapper.batchInsertSelective(insertEvents);
		}
		if (ListUtils.isNotEmpty(insertLogs)) {
			tCarrierOrderOperateLogsMapper.batchInsertSelective(insertLogs);
		}

		//根据运单所属需求单Id查询需求单状态
		List<GetDemandOrderInfoByIdsModel> demandOrderInfoList = tDemandOrderMapper.getDemandOrderInfoByIds(StringUtils.listToString(demandOrderIdList, ','));
		List<Long> demandOrderIds = new ArrayList<>();
		//需求单货物数量map
		demandOrderInfoList.forEach(demandOrder -> {
			if (demandOrder.getEntrustStatus() < DemandOrderStatusEnum.SIGN_DISPATCH.getKey()) {
				demandOrderIds.add(demandOrder.getDemandOrderId());
			}
		});
		if (ListUtils.isNotEmpty(demandOrderIds)) {
			//如果需求单下所有运单（除已取消的）均为已签收状态，则判断是否将需求单置为已签收状态
			List<DemandCarrierOrderRecursiveModel> demandCarrierOrderList = tCarrierOrderMapper.getNotCancelByDemandOrderIds(StringUtils.listToString(demandOrderIds, ','));
			List<Long> demandIds = new ArrayList<>();
			for (DemandCarrierOrderRecursiveModel demandOrderInfo : demandCarrierOrderList) {
				DemandCarrierOrderModel demandCarrierOrderModel = demandOrderInfo.getCarrierOrderList().stream().filter(carrierOrder -> !CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(carrierOrder.getStatus())).findFirst().orElse(null);
				if (demandCarrierOrderModel == null) {
					demandIds.add(demandOrderInfo.getDemandOrderId());
				}
			}
			if (ListUtils.isNotEmpty(demandIds)) {
				//修改需求单状态
				UpdateDemandOrderStatusByIdsRequestModel updateDemandOrderStatusByIdsRequestModel = new UpdateDemandOrderStatusByIdsRequestModel();
				updateDemandOrderStatusByIdsRequestModel.setDemandOrderIdList(demandIds);
				updateDemandOrderStatusByIdsRequestModel.setEntrustStatus(DemandOrderStatusEnum.SIGN_DISPATCH.getKey());
				updateDemandOrderStatusByIdsRequestModel.setOperatorName(BaseContextHandler.getUserName());
				demandOrderBiz.updateDemandOrderStatusByIds(updateDemandOrderStatusByIdsRequestModel);
			}
		}
		if (ListUtils.isNotEmpty(updateCarrierOrderMileageIdList)) {
			AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.carrierOrderMileage(updateCarrierOrderMileageIdList, null));
		}
	}

	/**
	 * 修改卸货地址-查询新生仓库
	 *
	 * @param requestModel 筛选条件
	 * @return 新生仓库列表
	 */
	public List<GetYeloLifeUnloadWarehouseResponseModel> getYeloLifeUnloadWarehouse(GetYeloLifeUnloadWarehouseRequestModel requestModel) {
		//查询新生仓库
		GetWarehouseDetailForLifeRequestModel lifeRequestModel = new GetWarehouseDetailForLifeRequestModel();
		lifeRequestModel.setEnabled(EnabledEnum.ENABLED.getKey());
		lifeRequestModel.setWarehouseName(requestModel.getUnloadSearchName());
		List<GetWarehouseDetailForLifeResponseModel> warehouseDetailForLife = warehouseLifeClient.getWarehouseDetailForLife(lifeRequestModel);
		List<GetYeloLifeUnloadWarehouseResponseModel> responseModelList = new ArrayList<>();
		GetYeloLifeUnloadWarehouseResponseModel responseModel;
		for (GetWarehouseDetailForLifeResponseModel model : warehouseDetailForLife) {
			responseModel = MapperUtils.mapper(model, GetYeloLifeUnloadWarehouseResponseModel.class);
			responseModel.setUnloadWarehouse(model.getWarehouseName());
			responseModelList.add(responseModel);
		}
		return responseModelList;
	}

	/**
	 * 修改卸货地址详情
	 *
	 * @param requestModel 运单ID
	 * @return 现地址信息
	 */
	public UpdateUnloadAddressDetailResponseModel getUnloadAddressDetailForYeloLife(CarrierOrderIdRequestModel requestModel) {
		//查询运单数据
		List<UpdateUnloadAddressDetailResponseModel> detailList = tCarrierOrderMapper.updateCarrierOrderUnloadAddressDetail(requestModel.getCarrierOrderId().toString());
		if (ListUtils.isEmpty(detailList)) {
			throw new BizException(CarrierDataExceptionEnum.UPDATE_UNLOAD_ADDRESS_ERROR_LIFE_ORDER);
		}
		UpdateUnloadAddressDetailResponseModel detail = detailList.get(CommonConstant.INTEGER_ZERO);

		//非新生单子不能操作
		if (!DemandOrderSourceEnum.YELO_LIFE.getKey().equals(detail.getDemandOrderSource())) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
		}
		//不是待入库不能修改
		if (!StockInStateEnum.WAIT_STOCK_IN.getKey().equals(detail.getStockInState())) {
			throw new BizException(CarrierDataExceptionEnum.UPDATE_UNLOAD_ADDRESS_ERROR_LIFE_ORDER);
		}
		return detail;
	}

	/**
	 * 确认修改卸货地址
	 *
	 * @param requestModel 新地址信息
	 */
	@Transactional
	public void updateUnloadAddressConfirmForYeloLife(UpdateCarrierOrderUnloadAddressForLifeRequestModel requestModel) {
		CarrierOrderIdRequestModel detailRequestModel = new CarrierOrderIdRequestModel();
		detailRequestModel.setCarrierOrderId(requestModel.getCarrierOrderId());
		UpdateUnloadAddressDetailResponseModel detail = getUnloadAddressDetailForYeloLife(detailRequestModel);

		//修改卸货地址
		TCarrierOrderAddress carrierOrderAddress = new TCarrierOrderAddress();
		carrierOrderAddress.setId(detail.getCarrierOrderAddressId());
		carrierOrderAddress.setUnloadAddressCode(requestModel.getUnloadWarehouseCode());
		carrierOrderAddress.setUnloadProvinceId(requestModel.getUnloadProvinceId());
		carrierOrderAddress.setUnloadProvinceName(requestModel.getUnloadProvinceName());
		carrierOrderAddress.setUnloadCityId(requestModel.getUnloadCityId());
		carrierOrderAddress.setUnloadCityName(requestModel.getUnloadCityName());
		carrierOrderAddress.setUnloadAreaId(requestModel.getUnloadAreaId());
		carrierOrderAddress.setUnloadAreaName(requestModel.getUnloadAreaName());
		carrierOrderAddress.setUnloadDetailAddress(requestModel.getUnloadDetailAddress());
		carrierOrderAddress.setUnloadWarehouse(requestModel.getUnloadWarehouse());
		carrierOrderAddress.setReceiverName(requestModel.getReceiverName());
		carrierOrderAddress.setReceiverMobile(requestModel.getReceiverMobile());
		carrierOrderAddress.setUnloadAddressUpdateType(requestModel.getUnloadAddressUpdateType());
		commonBiz.setBaseEntityModify(carrierOrderAddress, BaseContextHandler.getUserName());
		tCarrierOrderAddressMapper.updateByPrimaryKeySelective(carrierOrderAddress);

		//记录操作日志
		String remark = (StringUtils.isBlank(detail.getUnloadWarehouse()) ? "" : "【" + detail.getUnloadWarehouse() + "】") +
				detail.getUnloadProvinceName() +
				detail.getUnloadCityName() +
				detail.getUnloadAreaName() +
				(StringUtils.isBlank(detail.getUnloadDetailAddress()) ? "" : detail.getUnloadDetailAddress());
		TCarrierOrderOperateLogs carrierOrderOperateLogs = carrierOrderCommonBiz.getCarrierOrderOperateLogs(detail.getCarrierOrderId(), CarrierOrderOperateLogsTypeEnum.UPDATE_UNLOAD_ADDRESS, BaseContextHandler.getUserName(), remark);
		tCarrierOrderOperateLogsMapper.insertSelective(carrierOrderOperateLogs);

		//异步修改卸货地址经纬度
		AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.updateCarrierOrderAddressLonAndLat(Collections.singletonList(carrierOrderAddress), false));

		//同步新生
		UpdateLifeCarrierOrderAddressMessage message = new UpdateLifeCarrierOrderAddressMessage();
		message.setCarrierOrderCode(detail.getCarrierOrderCode());
		message.setCustomerOrderCode(detail.getCustomerOrderCode());
		message.setToAddressCode(requestModel.getUnloadWarehouseCode());
		message.setUserName(BaseContextHandler.getUserName());
		SyncCarrierOrderToYeloLifeModel<Object> syncCarrierOrderToYeloLifeModel = new SyncCarrierOrderToYeloLifeModel<>();
		syncCarrierOrderToYeloLifeModel.setType(SyncCarrierOrderToYeloLifeModel.SyncTypeEnum.UPDATE_UNLOAD_ADDRESS.getKey());
		syncCarrierOrderToYeloLifeModel.setMsgData(Collections.singletonList(message));
		rabbitMqPublishBiz.syncCarrierOrderToYeloLife(syncCarrierOrderToYeloLifeModel);
	}

	/**
	 * 云仓同步新生运单入库状态
	 *
	 * @param requestModel 入库状态
	 */
	@Transactional
	public void updateLifeCarrierCodeStockInState(YeloLifeCarrierOrderInStatusModel requestModel) {
		//查询运单
		TCarrierOrder tCarrierOrder = tCarrierOrderMapper.getByCode(requestModel.getCarrierOrderCode());
		if (tCarrierOrder == null || IfValidEnum.INVALID.getKey().equals(tCarrierOrder.getValid())) {
			log.info(requestModel.getCarrierOrderCode() + " " + CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST.getMsg());
			return;
		}

		//非新生单子不能操作
		if (!DemandOrderSourceEnum.YELO_LIFE.getKey().equals(tCarrierOrder.getDemandOrderSource())) {
			log.info(requestModel.getCarrierOrderCode() + " " + CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST.getMsg());
			return;
		}

		//运单入库状态是已经入库了
		if (StockInStateEnum.STOCK_IN.getKey().equals(tCarrierOrder.getStockInState())) {
			log.info(requestModel.getCarrierOrderCode() + " " + CarrierDataExceptionEnum.CARRIER_ORDER_IS_STOCK_IN.getMsg());
			return;
		}

		//运单是否取消
		if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfCancel())) {
			log.info(requestModel.getCarrierOrderCode() + " " + CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL.getMsg());
			return;
		}

		//更新运单入库状态,(新生运单只有入库状态)
		if (StockInStateEnum.STOCK_IN.getKey().equals(requestModel.getStockInState())) {
			TCarrierOrder upCarrierOrder = new TCarrierOrder();
			upCarrierOrder.setId(tCarrierOrder.getId());
			upCarrierOrder.setStockInState(StockInStateEnum.STOCK_IN.getKey());
			commonBiz.setBaseEntityModify(upCarrierOrder, requestModel.getOperatorName());
			tCarrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(upCarrierOrder);
		}
	}

	/**
	 * 新生系统同步-新生运单出库同步到物流系统
	 * @param requestModel
	 */
	@Transactional
	public void updateLifeCarrierCodeStockOutState(YeloLifeCarrierOrderStockOutModel requestModel){
		//查询运单
		TCarrierOrder tCarrierOrder = tCarrierOrderMapper.getByCode(requestModel.getCarrierOrderCode());
		if (tCarrierOrder == null) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
		}

		//非新生单子不能操作
		if (!DemandOrderSourceEnum.YELO_LIFE.getKey().equals(tCarrierOrder.getDemandOrderSource())) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
		}

		//运单取消不能操作
		if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfCancel())) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL);
		}

		//运单出库状态为已出库不能操作
		if (CarrierOrderOutStatusEnum.FINISH_OUT.getKey().equals(tCarrierOrder.getOutStatus())) {
			throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_STOCK_OUT);
		}

		//查询运单货物的的信息
		List<TCarrierOrderGoods> tCarrierOrderGoods = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(tCarrierOrder.getId().toString());

		Date now = new Date();
		BigDecimal loadAmount = CommonConstant.BIG_DECIMAL_ZERO;
		//根据sku code更新货物信息
		TCarrierOrderGoods upTCarrierOrderGoods;
		List<TCarrierOrderGoods> upTCarrierOrderGoodsList = new ArrayList<>();
		for (TCarrierOrderGoods tCarrierOrderGood : tCarrierOrderGoods) {
			for (YeloLifeCarrierOrderGoodsStockOutModel goodsModel : requestModel.getGoodsModels()) {
				if (StringUtils.isNotBlank(goodsModel.getSkuCode()) && goodsModel.getSkuCode().equals(tCarrierOrderGood.getSkuCode())) {
					upTCarrierOrderGoods = new TCarrierOrderGoods();
					upTCarrierOrderGoods.setId(tCarrierOrderGood.getId());
					upTCarrierOrderGoods.setLoadAmount(goodsModel.getLoadAmount());
					upTCarrierOrderGoods.setLastModifiedBy(requestModel.getOperatorName());
					upTCarrierOrderGoods.setLastModifiedTime(now);
					upTCarrierOrderGoodsList.add(upTCarrierOrderGoods);

					loadAmount = loadAmount.add(goodsModel.getLoadAmount());
				}
			}
		}
		//新生会少出库，导致没有匹配到我们这边货物信息
		if (ListUtils.isEmpty(upTCarrierOrderGoodsList)){
			throw new BizException(CarrierDataExceptionEnum.GOODS_LIST_ERROR);
		}

		//更新运单提货数量
		TCarrierOrder upCarrierOrder = new TCarrierOrder();
		upCarrierOrder.setId(tCarrierOrder.getId());
		upCarrierOrder.setLoadAmount(loadAmount);
		upCarrierOrder.setOutStatus(requestModel.getOutStatus());
		upCarrierOrder.setLastModifiedBy(requestModel.getOperatorName());
		upCarrierOrder.setLastModifiedTime(now);
		tCarrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(upCarrierOrder);
		//更新货物
		tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(upTCarrierOrderGoodsList);
	}
}
