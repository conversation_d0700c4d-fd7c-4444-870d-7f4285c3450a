package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/20 14:15
 */
@Data
public class InsertBillOrderLogisticsMessage {
    @ApiModelProperty("账单总金额")
    private BigDecimal totalPrice;

    @ApiModelProperty("收款人姓名")
    private String receiverName;

    @ApiModelProperty("收款人手机号")
    private String receiverPhone;

    @ApiModelProperty("物流确认单据的相对路径")
    private List<String> billOrderAttachment;

    @ApiModelProperty("操作人")
    private String operator;
}
