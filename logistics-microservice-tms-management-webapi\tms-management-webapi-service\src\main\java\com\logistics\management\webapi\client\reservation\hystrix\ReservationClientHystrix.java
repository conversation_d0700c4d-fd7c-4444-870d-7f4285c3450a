package com.logistics.management.webapi.client.reservation.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.reservation.ReservationClient;
import com.logistics.management.webapi.client.reservation.request.ReservationOrderSearchListForManagementWebReqModel;
import com.logistics.management.webapi.client.reservation.response.ReservationOrderSearchListForManagementWebResModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;


@Component
public class ReservationClientHystrix implements ReservationClient {


    @Override
    public Result<PageInfo<ReservationOrderSearchListForManagementWebResModel>> searchListForManagementWeb(ReservationOrderSearchListForManagementWebReqModel requestModel) {
        return Result.timeout();
    }
}
