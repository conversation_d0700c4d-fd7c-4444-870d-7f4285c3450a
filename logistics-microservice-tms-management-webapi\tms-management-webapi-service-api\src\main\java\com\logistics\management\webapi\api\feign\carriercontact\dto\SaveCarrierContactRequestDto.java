package com.logistics.management.webapi.api.feign.carriercontact.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * liang current user system login name
 * 2018/9/18 current system date
 */
@Data
public class SaveCarrierContactRequestDto {

    @ApiModelProperty("车主账号id（编辑时必填）")
    private String carrierContactId;

    @ApiModelProperty(value = "公司id",required = true)
    @NotBlank(message = "请选择车主")
    private String companyCarrierId;

    @ApiModelProperty(value = "姓名",required = true)
    @Pattern(regexp = "[\\u4e00-\\u9fa5]{2,50}",message = "姓名2-50汉字")
    private String contactName;

    @ApiModelProperty(value = "手机号",required = true)
    @Pattern(regexp = "^\\d{11}$", message = "请输入正确的手机号")
    private String contactPhone;

    @ApiModelProperty(value = "身份证号",required = true)
    @Pattern(regexp = "(^\\d{18}$)|(^\\d{17}(\\d|X)$)",message = "请输入正确的身份证号")
    private String identityNumber;

}
