package com.logistics.management.webapi.controller.vehiclesettlement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @author:lei.zhu
 * @date:2021/4/9 11:35
 */
@Data
public class CancelVehicleSettlementRequestDto {
    @ApiModelProperty(value = "车辆账单id",required = true)
    @NotBlank(message = "id不能为空")
    private String vehicleSettlementId;
    @ApiModelProperty(value = "操作类型  1 无需确认  2 撤回",required = true)
    @NotBlank(message = "操作类型不能为空")
    private String operatorType;
    @ApiModelProperty(value = "无需确认/撤回理由",required = true)
    @NotBlank(message = "理由备注不能为空")
    @Size(min = 1,max = 50,message = "不能超出50备注")
    private String reason;
}
