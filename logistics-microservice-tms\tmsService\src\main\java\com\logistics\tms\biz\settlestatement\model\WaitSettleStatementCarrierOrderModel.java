package com.logistics.tms.biz.settlestatement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/15
 */
@Data
public class WaitSettleStatementCarrierOrderModel {

	//运单信息
	@ApiModelProperty("运单id")
	private Long carrierOrderId;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("提货时间")
	private Date loadTime;

	@ApiModelProperty("签收时间")
	private Date signTime;

	@ApiModelProperty("货物单位")
	private Integer goodsUnit;

	@ApiModelProperty("预提数量")
	private BigDecimal expectAmount;

	@ApiModelProperty("提货数量")
	private BigDecimal loadAmount;

	@ApiModelProperty("卸货数量")
	private BigDecimal unloadAmount;

	@ApiModelProperty("签收数量")
	private BigDecimal signAmount;

	@ApiModelProperty("报价类型：1 单价  2 一口价")
	private Integer carrierPriceType;

	@ApiModelProperty("车主价格")
	private BigDecimal carrierPrice;

	@ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
	private Integer carrierSettlement;

	//货主信息
	@ApiModelProperty("货主id")
	private Long companyEntrustId;

	@ApiModelProperty("货主名")
	private String companyEntrustName;

	//车主信息
	@ApiModelProperty("车主id")
	private Long companyCarrierId;

	@ApiModelProperty("车主名")
	private String companyCarrierName;

	@ApiModelProperty("车主联系人姓名")
	private String carrierContactName;

	@ApiModelProperty("车主联系人手机号")
	private String carrierContactPhone;

	@ApiModelProperty("车主类型 1:公司 2:个人")
	private Integer companyCarrierType;

	//结算信息
	@ApiModelProperty("结算数量(结算表无数据时通过车主费用计算)")
	private BigDecimal settlementAmount;

	@ApiModelProperty("结算金额(结算表无数据时通过车主费用计算)")
	private BigDecimal settlementCost;

	//结算表结算信息
	@ApiModelProperty("结算表结算数量")
	private BigDecimal dbSettlementAmount;

	@ApiModelProperty("结算表结算金额")
	private BigDecimal dbSettlementCost;

	@ApiModelProperty("结算表价格类型 1 单价  2 一口价")
	private Integer dbCarrierPriceType;

	@ApiModelProperty("是否放空：0 否，1 是")
	private Integer ifEmpty;

	//临时费用
	@ApiModelProperty("临时费用")
	private BigDecimal otherFees;
}
