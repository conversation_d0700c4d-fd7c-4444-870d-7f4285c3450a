package com.logistics.management.webapi.controller.vehiclesettlement.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.vehiclesettlement.response.SettlementStatementHandleDetailResponseModel;
import com.logistics.management.webapi.controller.vehiclesettlement.response.SettlementStatementHandleDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * @author:lei.zhu
 * @date:2021/4/12 17:58
 */
public class SettlementStatementHandleDetailMapping extends MapperMapping<SettlementStatementHandleDetailResponseModel, SettlementStatementHandleDetailResponseDto> {
    @Override
    public void configure() {
        SettlementStatementHandleDetailResponseDto destination = getDestination();
        SettlementStatementHandleDetailResponseModel source = getSource();
        destination.setDriverName(Optional.ofNullable(source.getDriverName()).orElse("")+" "+Optional.ofNullable(source.getDriverPhone()).orElse(""));

        if(CommonConstant.INTEGER_ONE.equals(source.getAdjustCostSymbol())){
            destination.setOriginalFee(Optional.ofNullable(source.getActualExpensesPayable()).orElse(BigDecimal.ZERO)
                    .subtract(Optional.ofNullable(source.getAdjustFee()).orElse(BigDecimal.ZERO)).stripTrailingZeros().setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
        }else if(CommonConstant.INTEGER_TWO.equals(source.getAdjustCostSymbol())){
            destination.setOriginalFee(Optional.ofNullable(source.getActualExpensesPayable()).orElse(BigDecimal.ZERO)
                    .add(Optional.ofNullable(source.getAdjustFee()).orElse(BigDecimal.ZERO)).stripTrailingZeros().setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
        }else{
            destination.setOriginalFee(Optional.ofNullable(source.getActualExpensesPayable()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP).toPlainString());
        }
    }
}
