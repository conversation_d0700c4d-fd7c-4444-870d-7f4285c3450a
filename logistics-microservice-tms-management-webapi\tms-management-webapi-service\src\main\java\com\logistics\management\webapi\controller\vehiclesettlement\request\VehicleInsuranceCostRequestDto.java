package com.logistics.management.webapi.controller.vehiclesettlement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/6/5 16:45
 */
@Data
public class VehicleInsuranceCostRequestDto {
    @ApiModelProperty(value = "保单Id")
    private String insuranceId;
    @ApiModelProperty(value = "险种：1 商业险，2 交强险，4 货物险，5 危货承运人险")
    private String insuranceType;
    @ApiModelProperty(value = "应扣减费用")
    private String payCost;
    @ApiModelProperty(value = "未扣减费用")
    private String unPaidCost;
}
