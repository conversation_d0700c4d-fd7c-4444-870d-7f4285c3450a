package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2024/01/29
*/
@Data
public class TWorkGroup extends BaseEntity {
    /**
    * 群名
    */
    @ApiModelProperty("群名")
    private String groupName;

    /**
    * 群简介
    */
    @ApiModelProperty("群简介")
    private String groupDesc;

    /**
    * 负责人id
    */
    @ApiModelProperty("负责人id")
    private Long groupOwnerId;

    /**
    * 负责人手机号
    */
    @ApiModelProperty("负责人手机号")
    private String groupOwnerMobile;

    /**
    * 负责人姓名
    */
    @ApiModelProperty("负责人姓名")
    private String groupOwnerUsername;

    /**
    * 参与人id
    */
    @ApiModelProperty("参与人id")
    private Long participantId;

    /**
    * 参与人手机号
    */
    @ApiModelProperty("参与人手机号")
    private String participantMobile;

    /**
    * 参与人姓名
    */
    @ApiModelProperty("参与人姓名")
    private String participantUsername;

    /**
    * 群聊来源：1 新建，2 已有
    */
    @ApiModelProperty("群聊来源：1 新建，2 已有")
    private Integer workGroupSource;

    /**
    * 新建：企业微信群id；已有：机器人url
    */
    @ApiModelProperty("新建：企业微信群id；已有：机器人url")
    private String workGroupCode;

    /**
    * 匹配地点（多个,拼接）：1 发货信息，2 收货信息
    */
    @ApiModelProperty("匹配地点（多个,拼接）：1 发货信息，2 收货信息")
    private String matchingLocation;

    /**
    * 匹配字段：1 区域，2 仓库
    */
    @ApiModelProperty("匹配字段：1 区域，2 仓库")
    private Integer matchingField;

    /**
    * 配置仓库
    */
    @ApiModelProperty("配置仓库")
    private String configWarehouse;

    /**
    * 委托类型合集：1.回收业务(回收入库、回收出库) 2.采购业务（供应商直配、采购) 3.仓库业务（发货、调拨、退货、退货仓库送、退货调拨、其他）
    */
    @ApiModelProperty("委托类型合集：1.回收业务(回收入库、回收出库) 2.采购业务（供应商直配、采购) 3.仓库业务（发货、调拨、退货、退货仓库送、退货调拨、其他）")
    private String entrustTypeGroup;

    /**
    * 项目标签（多个标签,拼接）：0 标签为空，1 石化板块，2 轮胎板块，3 涂料板块，4 其他板块
    */
    @ApiModelProperty("项目标签（多个标签,拼接）：0 标签为空，1 石化板块，2 轮胎板块，3 涂料板块，4 其他板块")
    private String projectLabel;

    /**
    * 0禁用1启用
    */
    @ApiModelProperty("0禁用1启用")
    private Integer enabled;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}