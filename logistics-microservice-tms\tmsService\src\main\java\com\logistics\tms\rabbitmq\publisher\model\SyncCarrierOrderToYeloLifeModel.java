package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/6/20 13:08
 */
@Data
public class SyncCarrierOrderToYeloLifeModel<T> {

    /**
     * 同步类型 {@link SyncTypeEnum#key}
     */
    private Integer type;

    @ApiModelProperty("内容")
    private T msgData;

    /**
     * 新生运单节点操作枚举
     */
    @Getter
    @AllArgsConstructor
    public enum SyncTypeEnum {

        /**
         * 运单提货节点：内容{@link List<CarrierOrderLoadToYeloLifeModel>}
         */
        LOAD(1, "运单提货"),

        /**
         * 调度车辆生成运单节点：内容{@link CarrierOrderCreateToYeloLifeModel}
         */
        CREATE(2, "生成运单"),

        /**
         * 取消运单节点：内容{@link List<CarrierOrderCancelToYeloLifeModel>}
         */
        CANCEL(3, "取消运单"),

        /**
         * 修改卸货地址节点：内容{@link List<UpdateLifeCarrierOrderAddressMessage>}
         */
        UPDATE_UNLOAD_ADDRESS(4, "修改卸货地址"),

        /**
         * 修改车辆节点：内容{@link List<UpdateLifeCarrierOrderVehicleMessage>}
         */
        UPDATE_VEHICLE(5, "修改车辆"),

        ;
        private final Integer key;
        private final String value;

    }

}
