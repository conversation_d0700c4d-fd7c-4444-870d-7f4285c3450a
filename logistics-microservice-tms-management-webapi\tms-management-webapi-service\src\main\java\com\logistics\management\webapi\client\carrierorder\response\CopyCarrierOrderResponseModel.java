package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/11 13:16
 */
@Data
public class CopyCarrierOrderResponseModel {
    //运单基础信息
    @ApiModelProperty("运单号")
    private String carrierOrderCode;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("预计承运数量")
    private BigDecimal expectAmount;

    //运单地址信息
    //发货地址
    @ApiModelProperty("发货地址")
    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    @ApiModelProperty("发货人")
    private String consignorName;
    private String consignorMobile;
    //收货地址
    @ApiModelProperty("收货地址")
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;
    @ApiModelProperty("收货人")
    private String receiverName;
    private String receiverMobile;

    //运单货物信息
    @ApiModelProperty("品名")
    private List<String> goodsNameList;

    //运单车辆司机信息
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机")
    private String driverName;
    @ApiModelProperty("手机号")
    private String driverMobile;

    @ApiModelProperty("预计提货时间")
    private Date expectLoadTime;
}
