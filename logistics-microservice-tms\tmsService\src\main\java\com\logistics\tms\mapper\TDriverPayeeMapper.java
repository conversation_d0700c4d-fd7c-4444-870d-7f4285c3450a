package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.driverpayee.model.*;
import com.logistics.tms.entity.TDriverPayee;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TDriverPayeeMapper extends BaseMapper<TDriverPayee> {

    TDriverPayee getByNameMobileIdentity(@Param("name") String name, @Param("bankCardNo") String bankCardNo, @Param("identityNo") String identityNo, @Param("bankName") String bankName);

    List<Long> selectDriverPayeeListIds(@Param("param") DriverPayeeListRequestModel driverPayeeListRequestModel);

    List<DriverPayeeListResponseModel> selectDriverPayeeListByIds(@Param("ids") String ids);

    DriverPayeeDetailResponseModel getDriverPayeeDetail(@Param("driverPayeeId") Long driverPayeeId);

    TDriverPayee getByIdentity(@Param("identityNo") String identityNo);

    List<TDriverPayee> selectDriverPayeeByIds(@Param("driverPayeeIds") String ids);

    int batchUpdate(@Param("list") List<TDriverPayee> tqDriverPayees);

    List<TDriverPayee> findByName(@Param("name") String name);

    List<ExportDriverPayeeListResponseModel> exportDriverPayeeList(@Param("param") DriverPayeeListRequestModel driverPayeeListRequestModel);

    List<SearchDriverPayeesResponseModel> searchAuditedDriverPayees(@Param("driverPayee") String driverPayee);



}