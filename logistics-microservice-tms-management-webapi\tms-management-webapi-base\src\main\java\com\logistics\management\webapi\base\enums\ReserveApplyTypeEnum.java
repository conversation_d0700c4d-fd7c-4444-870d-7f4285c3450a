package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum ReserveApplyTypeEnum {

    DEFAULT(-1, ""),
    RECHARGE_TYPE(1, "充值"),
    ADVANCE_TYPE(2, "垫付"),
    RED_CHARGE_REFUND_TYPE(3, "红冲退款"),
    ;
    private final Integer key;
    private final String value;

    public static ReserveApplyTypeEnum getEnumByKey(Integer key) {
        return Optional.ofNullable(key)
                .map(k -> Stream.of(values())
                        .filter(f -> Objects.equals(k, f.getKey()))
                        .findFirst()
                        .orElse(DEFAULT))
                .orElse(DEFAULT);
    }
}
