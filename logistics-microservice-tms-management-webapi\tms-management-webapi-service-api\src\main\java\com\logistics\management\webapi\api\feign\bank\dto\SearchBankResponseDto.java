package com.logistics.management.webapi.api.feign.bank.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @Author: sj
 * @Date: 2019/7/10 13:58
 */
@Data
public class SearchBankResponseDto{
    @ApiModelProperty("银行ID")
    private Long bankId;
    @ApiModelProperty("银行名称")
    private String bankName = "";
    @ApiModelProperty("支行名字")
    private String branchName;
    @ApiModelProperty("是否禁用 0 禁用 1 启用 ")
    private String enable = "";
    @ApiModelProperty("是否禁用文本")
    private String enableLabel = "";
    @ApiModelProperty("备注")
    private String remark = "";
    @ApiModelProperty("添加人")
    private String createdBy = "";
    @ApiModelProperty("操作人")
    private String lastModifiedBy = "";
    @ApiModelProperty("操作时间")
    private String lastModifiedTime = "";
}
