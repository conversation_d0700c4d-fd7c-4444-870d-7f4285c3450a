package com.logistics.tms.mapper;

import com.logistics.tms.api.feign.violationregulation.model.FuzzyQueryDriverInfoRequestModel;
import com.logistics.tms.api.feign.violationregulation.model.FuzzyQueryDriverInfoResponseModel;
import com.logistics.tms.biz.dispatch.model.DriverByVehiclesModel;
import com.logistics.tms.biz.staff.model.GetStaffIfCompleteByIdsModel;
import com.logistics.tms.biz.staff.model.StaffIdDriverCredentialIdModel;
import com.logistics.tms.controller.baiscinfo.applet.response.DriverBasicInfoResponseModel;
import com.logistics.tms.controller.dispatch.request.DriverSearchRequestModel;
import com.logistics.tms.controller.dispatch.response.DriverSearchResponseModel;
import com.logistics.tms.controller.staff.request.SearchStaffManagementListRequestModel;
import com.logistics.tms.controller.staff.response.ExportStaffManagementListResponseModel;
import com.logistics.tms.controller.staff.response.GetStaffDetailResponseModel;
import com.logistics.tms.controller.staff.response.SearchStaffManagementListResponseModel;
import com.logistics.tms.controller.staffvehiclerelation.request.GetFuzzyQueryDriverInfoRequestModel;
import com.logistics.tms.controller.staffvehiclerelation.response.GetFuzzyQueryDriverInfoResponseModel;
import com.logistics.tms.controller.vehicleassetmanagement.response.AssetsBoardResponseModel;
import com.logistics.tms.controller.vehiclesettlement.response.GetSettlementDriverResponseModel;
import com.logistics.tms.entity.TStaffBasic;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface TStaffBasicMapper extends BaseMapper<TStaffBasic> {

    int batchUpdate(@Param("list") List<TStaffBasic> list);

    TStaffBasic getByMobile(@Param("mobile") String mobile);

    List<StaffIdDriverCredentialIdModel> getByIds(@Param("ids") String ids);

    GetStaffDetailResponseModel getStaffDetail(@Param("staffId") Long staffId);

    List<Long> searchStaffRelIdList(@Param("params") SearchStaffManagementListRequestModel requestModel, @Param("companyIds") List<Long> companyIds);

    List<SearchStaffManagementListResponseModel> searchStaffList(@Param("ids") String ids);

    List<ExportStaffManagementListResponseModel> exportStaffList(@Param("ids") String ids);

    AssetsBoardResponseModel getDriverStatics(@Param("companyCarrierId") Long companyCarrierId);

    Map<String, String> getDueIdentityCount(@Param("companyCarrierId") Long companyCarrierId,@Param("remindDays") Integer remindDays);

    TStaffBasic getByName(@Param("name") String name);

    int getCountByName(@Param("name") String name);

    List<FuzzyQueryDriverInfoResponseModel> fuzzyQueryDriverInfo(@Param("fuzzyDriverField") String fuzzyDriverField);

    List<FuzzyQueryDriverInfoResponseModel> fuzzyQueryDriverMessage(@Param("params") FuzzyQueryDriverInfoRequestModel requestModel);

    TStaffBasic getStaffByMobile(@Param("mobile") String mobile);

    List<TStaffBasic> getStaffByIds(@Param("ids") String ids);

    List<TStaffBasic> getStaffByMobiles(@Param("mobiles") String mobiles);

    int getInternalDriverCount();

    List<TStaffBasic> getInternalDriverByIds(@Param("ids") String ids);

    List<GetSettlementDriverResponseModel> getDriverInfoForVehicleSettlement(@Param("driverName") String driverName);


    List<GetStaffIfCompleteByIdsModel> getStaffContinueLearningComplete(@Param("ids") String staffIds);

    List<GetStaffIfCompleteByIdsModel> getStaffIntegrityExaminationComplete(@Param("ids") String staffIds);

    List<GetStaffIfCompleteByIdsModel> getStaffOccupationalComplete(@Param("ids") String staffIds);

    List<GetFuzzyQueryDriverInfoResponseModel> getFuzzyQueryDriverInfo(@Param("params") GetFuzzyQueryDriverInfoRequestModel requestModel);

    List<DriverSearchResponseModel> searchDriver(@Param("params") DriverSearchRequestModel requestModel);

    List<DriverByVehiclesModel> selectDriverByVehicles(@Param("driverIds") String driverIds, @Param("companyCarrierId") Long companyCarrierId, @Param("isEnable") Integer isEnable);

    TStaffBasic getByCarrierDriverId(@Param("carrierDriverId")Long carrierDriverId);

    DriverBasicInfoResponseModel selectDriverBasicInfo(@Param("driverId") Long driverId);
}
