<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRegionItemMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TRegionItem" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="region_id" property="regionId" jdbcType="BIGINT" />
    <result column="province_id" property="provinceId" jdbcType="BIGINT" />
    <result column="province_name" property="provinceName" jdbcType="VARCHAR" />
    <result column="city_id" property="cityId" jdbcType="BIGINT" />
    <result column="city_name" property="cityName" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, region_id, province_id, province_name, city_id, city_name, created_by, created_time, 
    last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_region_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_region_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TRegionItem" >
    insert into t_region_item (id, region_id, province_id, 
      province_name, city_id, city_name, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{regionId,jdbcType=BIGINT}, #{provinceId,jdbcType=BIGINT}, 
      #{provinceName,jdbcType=VARCHAR}, #{cityId,jdbcType=BIGINT}, #{cityName,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TRegionItem" >
    insert into t_region_item
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="regionId != null" >
        region_id,
      </if>
      <if test="provinceId != null" >
        province_id,
      </if>
      <if test="provinceName != null" >
        province_name,
      </if>
      <if test="cityId != null" >
        city_id,
      </if>
      <if test="cityName != null" >
        city_name,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="regionId != null" >
        #{regionId,jdbcType=BIGINT},
      </if>
      <if test="provinceId != null" >
        #{provinceId,jdbcType=BIGINT},
      </if>
      <if test="provinceName != null" >
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null" >
        #{cityId,jdbcType=BIGINT},
      </if>
      <if test="cityName != null" >
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TRegionItem" >
    update t_region_item
    <set >
      <if test="regionId != null" >
        region_id = #{regionId,jdbcType=BIGINT},
      </if>
      <if test="provinceId != null" >
        province_id = #{provinceId,jdbcType=BIGINT},
      </if>
      <if test="provinceName != null" >
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null" >
        city_id = #{cityId,jdbcType=BIGINT},
      </if>
      <if test="cityName != null" >
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TRegionItem" >
    update t_region_item
    set region_id = #{regionId,jdbcType=BIGINT},
      province_id = #{provinceId,jdbcType=BIGINT},
      province_name = #{provinceName,jdbcType=VARCHAR},
      city_id = #{cityId,jdbcType=BIGINT},
      city_name = #{cityName,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>