package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2021/9/24 17:54
 */
@Data
public class BatchPublishDemandDto {
    @ApiModelProperty("需求单id")
    @NotBlank(message = "需求单id不能为空")
    private String demandOrderId;

    @ApiModelProperty("收货省份id")
    private String unloadProvinceId;
    @ApiModelProperty("收货省份名字")
    private String unloadProvinceName;
    @ApiModelProperty("收货城市id")
    private String unloadCityId;
    @ApiModelProperty("收货城市名字")
    private String unloadCityName;
    @ApiModelProperty("收货县区id")
    private String unloadAreaId;
    @ApiModelProperty("收货县区名字")
    private String unloadAreaName;
    @ApiModelProperty("收货详细地址")
    private String unloadDetailAddress;
    @ApiModelProperty("收货仓库")
    private String unloadWarehouse;
    @ApiModelProperty("收货人姓名")
    private String receiverName;
    @ApiModelProperty("收货人手机号")
    private String receiverMobile;
    @ApiModelProperty("云盘仓库外部id")
    private String warehouseId;

}
