package com.logistics.appapi.client.website.vehicle.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.website.vehicle.VehicleSourceClient;
import com.logistics.appapi.client.website.vehicle.request.PublishVehicleSourceRequestModel;
import com.logistics.appapi.client.website.vehicle.request.VehicleSourceListRequestModel;
import com.logistics.appapi.client.website.vehicle.response.VehicleSourceListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/3/15 10:56
 */
@Component
public class VehicleSourceClientHystrix implements VehicleSourceClient {
    @Override
    public Result publishVehicleSource(PublishVehicleSourceRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<VehicleSourceListResponseModel>> vehicleSourceList(VehicleSourceListRequestModel requestModel) {
        return Result.timeout();
    }
}
