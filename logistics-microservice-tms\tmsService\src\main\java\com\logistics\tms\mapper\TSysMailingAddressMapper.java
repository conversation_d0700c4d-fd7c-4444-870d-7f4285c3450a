package com.logistics.tms.mapper;

import com.logistics.tms.api.feign.mailinginfo.model.response.MailingInfoListResponseModel;
import com.logistics.tms.entity.TSysMailingAddress;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2023/01/09
*/
@Mapper
public interface TSysMailingAddressMapper extends BaseMapper<TSysMailingAddress> {

    List<MailingInfoListResponseModel> selectAllMailingInfo();

    TSysMailingAddress selectOneById(@Param("mailingId") Long mailingId);

    int updateMailingInfo(TSysMailingAddress sysMailingAddress);
}