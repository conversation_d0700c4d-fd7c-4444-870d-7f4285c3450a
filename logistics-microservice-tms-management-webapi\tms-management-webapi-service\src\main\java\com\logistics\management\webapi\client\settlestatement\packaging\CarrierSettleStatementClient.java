package com.logistics.management.webapi.client.settlestatement.packaging;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.settlestatement.packaging.hystrix.CarrierSettleStatementClientHystrix;
import com.logistics.management.webapi.client.settlestatement.packaging.request.*;
import com.logistics.management.webapi.client.settlestatement.packaging.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/20 9:45
 */
@FeignClient(name = FeignClientName.TMS_SERVICES, 
        path = "/service/carrierStatementManage",
        fallback = CarrierSettleStatementClientHystrix.class)
public interface CarrierSettleStatementClient {

    /*
    待对账单
    * */
    @ApiOperation(value = "待对账运单列表")
    @PostMapping(value = "/waitSettleStatementList")
    Result<PageInfo<CarrierWaitSettleStatementListResponseModel>> waitSettleStatementList(@RequestBody CarrierWaitSettleStatementListRequestModel requestModel);

    @ApiOperation(value = "导出待对账运单列表")
    @PostMapping(value = "/exportWaitSettleStatementList")
    Result<PageInfo<CarrierWaitSettleStatementListResponseModel>> exportWaitSettleStatementList(@RequestBody CarrierWaitSettleStatementListRequestModel requestModel);

    @ApiOperation(value = "生成对账单")
    @PostMapping(value = "/createSettleStatement")
    Result<Boolean> createSettleStatement(@RequestBody CarrierCreateSettleStatementRequestModel requestModel);

    @ApiOperation(value = "查询车主税点")
    @PostMapping(value = "/queryTaxPoint")
    Result<CarrierTaxPointResponseModel> queryTaxPoint(@RequestBody CarrierTaxPointRequestModel requestModel);

    /**
     * 对账单功能
     */
    @ApiOperation(value = "对账单列表")
    @PostMapping(value = "/settleStatementList")
    Result<PageInfo<CarrierSettleStatementListResponseModel>> settleStatementList(@RequestBody CarrierSettleStatementListRequestModel requestModel);

    @ApiOperation(value = "关联运单号")
    @PostMapping(value = "/associationCarrierOrder")
    Result<CarrierAssociationCarrierOrderResponseModel> associationCarrierOrder(@RequestBody CarrierAssociationCarrierOrderRequestModel requestModel);

    @ApiOperation(value = "编辑对账月份")
    @PostMapping(value = "/modifySettleStatementMonth")
    Result<Boolean> modifySettleStatementMonth(@RequestBody ModifySettleStatementMonthRequestModel requestModel);

    @ApiOperation(value = "编辑结算主体")
    @PostMapping(value = "/modifyPlatformCompany")
    Result<Boolean> modifyPlatformCompany(@RequestBody ModifyPlatformCompanyRequestModel requestModel);

    @ApiOperation(value = "修改对账单费点")
    @PostMapping(value = "/modifyTaxPoint")
    Result<Boolean> modifyTaxPoint(@RequestBody CarrierModifyTaxPointRequestModel requestModel);

    @ApiOperation(value = "修改对账单名称")
    @PostMapping(value = "/renameStatement")
    Result<Boolean> renameStatement(@RequestBody CarrierEditSettleStatementNameRequestModel requestModel);

    @ApiOperation(value = "差异调整-回显")
    @PostMapping(value = "/queryAdjustCost")
    Result<CarrierAdjustCostResponseModel> queryAdjustCost(@RequestBody CarrierQueryAdjustCostRequestModel requestModel);

    @ApiOperation(value = "差异调整-发起")
    @PostMapping(value = "/AdjustCost")
    Result<Boolean> adjustCost(@RequestBody CarrierAdjustRequestModel requestModel);

    @ApiOperation(value = "申请开票")
    @PostMapping(value = "/applyInvoicing")
    Result<Boolean> applyInvoicing(@RequestBody SettleStatementApplyInvoicingRequestModel requestModel);

    @ApiOperation(value = "撤销对账单")
    @PostMapping(value = "/cancel")
    Result<Boolean> cancel(@RequestBody CarrierCancelRequestModel requestModel);

    /**
     * 对账单下运单归档
     */
    @ApiOperation(value = "对账单归档列表")
    @PostMapping(value = "/statementArchiveList")
    Result<PageInfo<StatementArchiveListResponseModel>> statementArchiveList(@RequestBody StatementArchiveListRequestModel requestModel);

    @ApiOperation(value = "对账单归档/编辑")
    @PostMapping(value = "/statementArchive")
    Result<Boolean> statementArchive(@RequestBody StatementArchiveRequestModel requestModel);

    @ApiOperation(value = "查看归档图片")
    @PostMapping(value = "/archiveTicketList")
    Result<List<String>> archiveTicketList(@RequestBody StatementArchiveTicketListRequestModel requestModel);

    @ApiOperation(value = "查看归档详情")
    @PostMapping(value = "/statementArchiveDetail")
    Result<StatementArchiveDetailResponseModel> statementArchiveDetail(@RequestBody StatementArchiveDetailRequestModel requestModel);

    @ApiOperation(value = "查询对账单下待归档运单")
    @PostMapping(value = "/statementWaitArchiveList")
    Result<List<StatementWaitArchiveListResponseModel>> statementWaitArchiveList(@RequestBody StatementWaitArchiveListRequestModel requestModel);

    /*对账单详情*/
    @ApiOperation(value = "对账单详情-提交")
    @PostMapping(value = "/applicationCheck")
    Result<Boolean> submitSettleStatement(@RequestBody CarrierSettleStatementIdRequestModel requestModel);

    @ApiOperation(value = "对账单详情-审核/驳回")
    @PostMapping(value = "/auditOrReject")
    Result<Boolean> auditOrReject(@RequestBody @Valid CarrierChangeSettleStatementStatsRequestModel requestModel);

    @ApiOperation(value = "对账单详情-合计")
    @PostMapping(value = "/settleStatementDetailTotal")
    Result<CarrierSettleStatementDetailTotalResponseModel> settleStatementDetailTotal(@RequestBody CarrierSettleStatementIdRequestModel requestModel);

    @ApiOperation(value = "对账单详情-对账单运单列表")
    @PostMapping(value = "/settleStatementDetailList")
    Result<PageInfo<CarrierSettleStatementDetailListResponseModel>> settleStatementDetailList(@RequestBody CarrierSettleStatementDetailListRequestModel requestModel);

    @ApiOperation(value = "导出对账单详情-对账单运单列表")
    @PostMapping(value = "/exportSettleStatementDetailList")
    Result<PageInfo<CarrierSettleStatementDetailListResponseModel>> exportSettleStatementDetailList(@RequestBody CarrierSettleStatementDetailListRequestModel requestModel);

    @ApiOperation(value = "对账单详情-查询添加运单列表")
    @PostMapping(value = "/addCarrierOrderList")
    Result<PageInfo<CarrierWaitSettleStatementListResponseModel>> addCarrierOrderList(@RequestBody CarrierAddCarrierOrderListRequestModel requestModel);

    @ApiOperation(value = "对账单详情-添加运单")
    @PostMapping(value = "/addCarrierOrder")
    Result<Boolean> addCarrierOrderConfirm(@RequestBody CarrierAddCarrierOrderConfirmRequestModel requestModel);

    @ApiOperation(value = "对账单详情-撤销运单")
    @PostMapping(value = "/cancelCarrierOrder")
    Result<Boolean> cancelCarrierOrder(@RequestBody CarrierUndoCarrierOrderRequestModel requestModel);

    @ApiOperation(value = "对账单详情-已对账驳回")
    @PostMapping(value = "/rejectCompleteSettle")
    Result<Boolean> rejectCompleteSettle(@RequestBody CarrierSettleStatementIdRequestModel requestModel);
    
}
