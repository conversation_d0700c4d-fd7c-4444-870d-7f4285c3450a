package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TFreightAddressRule;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TFreightAddressRuleMapper extends BaseMapper<TFreightAddressRule>{

    List<TFreightAddressRule> getListByFreightAddressIds(@Param("freightAddressIds") String freightAddressIds);

    void batchUpdate(@Param("list") List<TFreightAddressRule> list);

}