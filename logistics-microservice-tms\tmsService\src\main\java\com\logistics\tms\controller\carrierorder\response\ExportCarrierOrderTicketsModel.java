package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author：wjf
 * @date：2021/3/16 13:13
 */
@Data
public class ExportCarrierOrderTicketsModel {
    @ApiModelProperty("运单id")
    private Long carrierOrderId;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("签收单/回单")
    private List<String> signTicketsList;
}
