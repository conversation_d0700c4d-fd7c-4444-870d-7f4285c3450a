package com.logistics.management.webapi.controller.biddingorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class ConfirmQuoteRequestDto {

    /**
     * 报价单id
     */
    @ApiModelProperty("报价单id")
    @NotBlank(message = "报价单id不能为空")
    private String biddingOrderQuoteId;

    /**
     * 竞价金额类型：1 单价，2 一口价
     */
    @ApiModelProperty("竞价金额类型：1 单价，2 一口价")
    @NotBlank(message = "竞价金额类型不能为空")
    private String biddingPriceType;


    /**
     * (3.22.0)备注
     */
    @ApiModelProperty("备注")
    @Size(max = 100, message = "备注不超过100字")
    private String remark;
}
