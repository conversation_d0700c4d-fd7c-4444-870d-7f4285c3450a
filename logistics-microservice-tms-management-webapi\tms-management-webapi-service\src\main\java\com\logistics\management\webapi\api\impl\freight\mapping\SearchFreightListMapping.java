package com.logistics.management.webapi.api.impl.freight.mapping;

import com.logistics.management.webapi.api.feign.freight.dto.SearchFreightListResponseDto;
import com.logistics.management.webapi.base.enums.EnabledEnum;
import com.logistics.tms.api.feign.freight.model.SearchFreightListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @Author: sj
 * @Date: 2019/12/25 19:17
 */
public class SearchFreightListMapping extends MapperMapping<SearchFreightListResponseModel,SearchFreightListResponseDto> {
    @Override
    public void configure() {
        SearchFreightListResponseModel model = this.getSource();
        SearchFreightListResponseDto dto = this.getDestination();
        if(model != null){
            dto.setEnabledLabel(EnabledEnum.getEnum(model.getEnabled()).getValue());
        }
    }
}
