<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRegionItemMapper">
    <select id="getByNotRegionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_region_item
        where valid =1
        <if test="regionId !=null">
            and region_id != #{regionId,jdbcType = VARCHAR}
        </if>
    </select>

    <select id="selectByRegionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_region_item
        where valid =1
        <if test="regionId !=null">
            and region_id = #{regionId,jdbcType = VARCHAR}
        </if>
    </select>

    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TRegionItem">
        <foreach collection="list" separator=";" item="item">
            insert into t_region_item
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.regionId != null">
                    region_id,
                </if>
                <if test="item.provinceId != null">
                    province_id,
                </if>
                <if test="item.provinceName != null">
                    province_name,
                </if>
                <if test="item.cityId != null">
                    city_id,
                </if>
                <if test="item.cityName != null">
                    city_name,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.regionId != null">
                    #{item.regionId,jdbcType=BIGINT},
                </if>
                <if test="item.provinceId != null">
                    #{item.provinceId,jdbcType=BIGINT},
                </if>
                <if test="item.provinceName != null">
                    #{item.provinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.cityId != null">
                    #{item.cityId,jdbcType=BIGINT},
                </if>
                <if test="item.cityName != null">
                    #{item.cityName,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="com.logistics.tms.entity.TRegionItem" >
        <foreach collection="list" item="item" separator=";">
            update t_region_item
            <set >
                <if test="item.regionId != null" >
                    region_id = #{item.regionId,jdbcType=BIGINT},
                </if>
                <if test="item.provinceId != null" >
                    province_id = #{item.provinceId,jdbcType=BIGINT},
                </if>
                <if test="item.provinceName != null" >
                    province_name = #{item.provinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.cityId != null" >
                    city_id = #{item.cityId,jdbcType=BIGINT},
                </if>
                <if test="item.cityName != null" >
                    city_name = #{item.cityName,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="searchDetailList" resultType="com.logistics.tms.controller.region.response.SearchRegionDetailResponseModel">
        select
        tr.region_name as regionName,

        tri.id as regionItemId,
        tri.province_name as provinceName,
        tri.city_name as cityName
        from t_region_item tri
        left join t_region tr on tr.id = tri.region_id and tr.valid = 1
        where tri.valid = 1
        and tri.region_id = #{params.regionId,jdbcType=BIGINT}
        <if test="params.provinceName != null and params.provinceName != ''">
            and instr(tri.province_name, #{params.provinceName})
        </if>
        <if test="params.cityName != null and params.cityName != ''">
            and instr(tri.city_name, #{params.cityName})
        </if>
        order by tri.last_modified_time desc,tri.id desc
    </select>
</mapper>