<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TLoanRecordsMapper" >
  <select id="getLoanRecordsByVehicleId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_loan_records
    where  valid = 1
    and vehicle_id = #{vehicleId,jdbcType=BIGINT}
  </select>

  <select id="getLoanRecordList" resultType="com.logistics.tms.api.feign.loanrecord.model.LoanRecordListResponseModel" >
      select
        tlr.id as loanRecordId,
        tlr.status as status,
        tlr.vehicle_id as vehicleId,
        tlr.vehicle_no as vehicleNo,
        tlr.brand as brand,
        tlr.model as model,
        tlr.vehicle_identification_number as vehicleIdentificationNumber,
        tlr.engine_number as engineNumber,
        tlr.body_color as bodyColor,
        tlr.producer as producer,
        tlr.manufacturers as manufacturers,
        tlr.production_date as productionDate,
        tlr.staff_id as staffId,
        tlr.name as name,
        tlr.mobile as mobile,
        tlr.identity_number as identityNumber,
        tlr.naked_car_price as nakedCarPrice,
        tlr.insurance_premium as insurancePremium,
        tlr.purchase_tax as purchaseTax,
        tlr.car_price as carPrice,
        tlr.driver_expense as driverExpense,
        tlr.loan_fee as loanFee,
        tlr.loan_periods as loanPeriods,
        tlr.loan_start_time as loanStartTime,
        tlr.loan_rate as loanRate,
        tlr.loan_commission as loanCommission ,
        tlr.loan_interest as loanInterest,
        tlr.remark as remark,
        tlr.created_by as createdBy,
        tlr.created_time as createdTime,
        tlr.last_modified_by as lastModifiedBy,
        tlr.last_modified_time as lastModifiedTime,

        min(tlsr.remaining_repayment_fee) as remainingRepaymentFee
      from t_loan_records tlr
      left join t_loan_settlement_record tlsr on tlsr.loan_records_id = tlr.id and tlsr.valid = 1
      where tlr.valid = 1
      <if test="condition.status!=null">
        and tlr.status = #{condition.status,jdbcType = INTEGER}
      </if>
      <if test="condition.vehicleNo!=null and condition.vehicleNo!=''">
         and instr(tlr.vehicle_no,#{condition.vehicleNo,jdbcType = VARCHAR})
      </if>
      <if test="condition.driverInfo!=null and condition.driverInfo!=''">
         and (instr(tlr.name,#{condition.driverInfo,jdbcType = VARCHAR})
              or instr(tlr.mobile,#{condition.driverInfo,jdbcType = VARCHAR})    )
      </if>
      <if test="condition.loanRecordIds!=null and condition.loanRecordIds!=''">
        and tlr.id in (${condition.loanRecordIds})
      </if>
      group by tlr.id
      order by tlr.created_time desc,tlr.id desc

  </select>

  <select id="getSummaryLoanRecordInfo" resultType="com.logistics.tms.api.feign.loanrecord.model.SummaryLoanRecordResponseModel">
    select
      ifnull(count(0),0) as allCount,
      ifnull(sum(if(tlr.status = 0,1,0)),0) as waitCount,
      ifnull(sum(if(tlr.status = 1,1,0)),0) as partCount,
      ifnull(sum(if(tlr.status = 2,1,0)),0) as hasCount
    from t_loan_records tlr
    where 1=1 and tlr.valid = 1
    <if test="condition.vehicleNo!=null and condition.vehicleNo!=''">
      and instr(tlr.vehicle_no,#{condition.vehicleNo,jdbcType = VARCHAR})
    </if>
    <if test="condition.driverInfo!=null and condition.driverInfo!=''">
      and (instr(tlr.name,#{condition.driverInfo,jdbcType = VARCHAR})
      or instr(tlr.mobile,#{condition.driverInfo,jdbcType = VARCHAR})    )
    </if>
  </select>

    <select id="getSettlementRecordsByIdForSettlement" resultType="com.logistics.tms.controller.vehiclesettlement.response.GetLoanFeeByVehicleIdResponseModel">
        select
        tlr.id as loanRecordsId,
        tlr.status,
        tlr.loan_fee as loanFee,
        tlr.loan_periods as loanPeriods,
        tlr.load_finish_time as loadFinishTime,
        tlsr.deducting_month as deductingMonth,
        tlsr.settlement_fee as deductingFee,
        tlsr.remaining_repayment_fee as RemainingDeductingFee
        from t_loan_records tlr
        left join (select loan_records_id,deducting_month,settlement_fee,remaining_repayment_fee from t_loan_settlement_record
        where valid = 1 and loan_records_id = #{id,jdbcType=BIGINT} and deducting_month &lt;= #{deductingMonth,jdbcType=VARCHAR}
        order by deducting_month desc,id desc limit 1) tlsr on tlsr.loan_records_id = tlr.id
        where tlr.valid = 1
        and tlr.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="getVehicleBySettlementMonth" resultType="com.logistics.tms.controller.vehiclesettlement.response.GetVehicleBySettlementMonthModel">
        select
        vehicle_id as vehicleId,
        max(id) as objectId
        from t_loan_records
        where valid = 1
        and status != 2
        and date_format(loan_start_time,'%Y-%m') &lt;= #{settlementMonth,jdbcType=VARCHAR}
        and date_format(load_finish_time,'%Y-%m') >= #{settlementMonth,jdbcType=VARCHAR}
        and date_format(created_time,'%Y-%m') &lt;= #{settlementMonth,jdbcType=VARCHAR}
        group by vehicle_id
    </select>

    <select id="getById" resultType="com.logistics.tms.controller.vehiclesettlement.response.GetLoanFeeByVehicleIdResponseModel">
        select
            tlr.id as loanRecordsId,
            tlr.status,
            tlr.loan_fee as loanFee,
            tlr.loan_periods as loanPeriods,
            tlr.load_finish_time as loadFinishTime,
            tlsr.deducting_month as deductingMonth,
            tlsr.settlement_fee as deductingFee,
            tlsr.remaining_repayment_fee as RemainingDeductingFee
        from t_loan_records tlr
        left join t_loan_settlement_record tlsr on tlsr.loan_records_id = tlr.id  and tlsr.valid = 1
        where tlr.valid = 1
        and tlr.id = #{id,jdbcType=BIGINT}
        order by tlsr.created_time  desc
    </select>

</mapper>