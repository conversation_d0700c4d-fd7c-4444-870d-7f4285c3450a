package com.logistics.management.webapi.controller.routeconfig.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class RouteDistanceConfigRecommendRequestDto {

    @ApiModelProperty(value = "发货区", required = true)
    @NotBlank(message = "区id不允许为空")
    private String fromAreaId;

    @ApiModelProperty(value = "收货区", required = true)
    @NotBlank(message = "区id不允许为空")
    private String toAreaId;
}
