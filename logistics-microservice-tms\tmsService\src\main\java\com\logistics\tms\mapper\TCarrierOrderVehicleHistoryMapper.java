package com.logistics.tms.mapper;

import com.logistics.tms.biz.carrierorder.model.SearchCarrierOrderVehicleHistoryModel;
import com.logistics.tms.controller.carrierorder.request.GetDriverVehicleInfoByCodeRequestModel;
import com.logistics.tms.controller.carrierorder.response.DriverAndVehicleResponseModel;
import com.logistics.tms.controller.carrierorder.response.GetDriverVehicleInfoByCodeResponseModel;
import com.logistics.tms.controller.dispatch.response.DriverIdVehicleIdRelationIdModel;
import com.logistics.tms.entity.TCarrierOrderVehicleHistory;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TCarrierOrderVehicleHistoryMapper extends BaseMapper<TCarrierOrderVehicleHistory>{

    int batchInsertSelective(@Param("list") List<TCarrierOrderVehicleHistory> list);

    DriverAndVehicleResponseModel getDriverVehicleInfoByCarrierOrderId(@Param("carrierOrderId") Long carrierOrderId);

    TCarrierOrderVehicleHistory getTopByCarrierOrderId(@Param("params") DriverIdVehicleIdRelationIdModel params);

    int batchUpdateByPrimaryKeySelective(@Param("list") List<TCarrierOrderVehicleHistory> list);

    List<String> selectWaitAuditCarrierOrders(@Param("carrierOrders") List<Long> carrierOrderList);

    TCarrierOrderVehicleHistory getInvalidTopByCarrierOrderId(@Param("carrierOrderId") Long carrierOrderId);

    TCarrierOrderVehicleHistory getValidTopByCarrierOrderId(@Param("carrierOrderId") Long carrierOrderId);

    Integer getCarrierDriverByHistoryId(@Param("historyId") Long historyId, @Param("companyCarrierId") Long companyCarrierId);

    List<TCarrierOrderVehicleHistory> getValidByCarrierOrderIds(@Param("carrierOrderIds")String carrierOrderIds);

    List<TCarrierOrderVehicleHistory> getAllByCarrierOrderIds(@Param("carrierOrderIds")String carrierOrderIds);

    List<TCarrierOrderVehicleHistory> searchListByCarrierOrderParams(@Param("condition") SearchCarrierOrderVehicleHistoryModel model);

    List<GetDriverVehicleInfoByCodeResponseModel> getDriverVehicleInfoByCode(@Param("params") GetDriverVehicleInfoByCodeRequestModel requestModel);

    List<TCarrierOrderVehicleHistory> listValidByVehicleNo(String vehicleNo);
}