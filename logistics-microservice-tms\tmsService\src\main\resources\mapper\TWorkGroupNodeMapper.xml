<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TWorkGroupNodeMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TWorkGroupNode" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="work_group_id" property="workGroupId" jdbcType="BIGINT" />
    <result column="order_type" property="orderType" jdbcType="INTEGER" />
    <result column="order_node" property="orderNode" jdbcType="INTEGER" />
    <result column="time_require" property="timeRequire" jdbcType="INTEGER" />
    <result column="amount_type" property="amountType" jdbcType="INTEGER" />
    <result column="amount_symbol" property="amountSymbol" jdbcType="INTEGER" />
    <result column="amount_require" property="amountRequire" jdbcType="DECIMAL" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, work_group_id, order_type, order_node, time_require, amount_type, amount_symbol, 
    amount_require, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_work_group_node
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_work_group_node
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TWorkGroupNode" >
    insert into t_work_group_node (id, work_group_id, order_type, 
      order_node, time_require, amount_type, 
      amount_symbol, amount_require, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{workGroupId,jdbcType=BIGINT}, #{orderType,jdbcType=INTEGER}, 
      #{orderNode,jdbcType=INTEGER}, #{timeRequire,jdbcType=INTEGER}, #{amountType,jdbcType=INTEGER}, 
      #{amountSymbol,jdbcType=INTEGER}, #{amountRequire,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TWorkGroupNode" >
    insert into t_work_group_node
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="workGroupId != null" >
        work_group_id,
      </if>
      <if test="orderType != null" >
        order_type,
      </if>
      <if test="orderNode != null" >
        order_node,
      </if>
      <if test="timeRequire != null" >
        time_require,
      </if>
      <if test="amountType != null" >
        amount_type,
      </if>
      <if test="amountSymbol != null" >
        amount_symbol,
      </if>
      <if test="amountRequire != null" >
        amount_require,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="workGroupId != null" >
        #{workGroupId,jdbcType=BIGINT},
      </if>
      <if test="orderType != null" >
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderNode != null" >
        #{orderNode,jdbcType=INTEGER},
      </if>
      <if test="timeRequire != null" >
        #{timeRequire,jdbcType=INTEGER},
      </if>
      <if test="amountType != null" >
        #{amountType,jdbcType=INTEGER},
      </if>
      <if test="amountSymbol != null" >
        #{amountSymbol,jdbcType=INTEGER},
      </if>
      <if test="amountRequire != null" >
        #{amountRequire,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TWorkGroupNode" >
    update t_work_group_node
    <set >
      <if test="workGroupId != null" >
        work_group_id = #{workGroupId,jdbcType=BIGINT},
      </if>
      <if test="orderType != null" >
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderNode != null" >
        order_node = #{orderNode,jdbcType=INTEGER},
      </if>
      <if test="timeRequire != null" >
        time_require = #{timeRequire,jdbcType=INTEGER},
      </if>
      <if test="amountType != null" >
        amount_type = #{amountType,jdbcType=INTEGER},
      </if>
      <if test="amountSymbol != null" >
        amount_symbol = #{amountSymbol,jdbcType=INTEGER},
      </if>
      <if test="amountRequire != null" >
        amount_require = #{amountRequire,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TWorkGroupNode" >
    update t_work_group_node
    set work_group_id = #{workGroupId,jdbcType=BIGINT},
      order_type = #{orderType,jdbcType=INTEGER},
      order_node = #{orderNode,jdbcType=INTEGER},
      time_require = #{timeRequire,jdbcType=INTEGER},
      amount_type = #{amountType,jdbcType=INTEGER},
      amount_symbol = #{amountSymbol,jdbcType=INTEGER},
      amount_require = #{amountRequire,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>