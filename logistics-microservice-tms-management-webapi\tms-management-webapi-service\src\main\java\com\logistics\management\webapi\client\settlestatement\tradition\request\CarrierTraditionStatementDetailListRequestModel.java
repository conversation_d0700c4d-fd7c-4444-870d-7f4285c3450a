package com.logistics.management.webapi.client.settlestatement.tradition.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/17
 */
@Data
public class CarrierTraditionStatementDetailListRequestModel extends AbstractPageForm<CarrierTraditionStatementDetailListRequestModel> {

	@ApiModelProperty(value = "对账单id")
	private Long settleStatementId;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("需求单号")
	private String demandOrderCode;

	@ApiModelProperty("车牌号")
	private String vehicleNumber;

	@ApiModelProperty("司机名,司机姓名+手机号")
	private String driverName;

	@ApiModelProperty("报价类型：1 单价，2 一口价")
	private Integer carrierPriceType;

	@ApiModelProperty("发货仓库")
	private String loadWarehouse;

	@ApiModelProperty("发货地址")
	private String loadAddress;

	@ApiModelProperty("收货仓库")
	private String unloadWareHouse;

	@ApiModelProperty("收货地址")
	private String unloadAddress;

	@ApiModelProperty("调度单号")
	private String dispatchOrderCode;

	@ApiModelProperty("提货时间起")
	private String loadTimeStart;

	@ApiModelProperty("提货时间始")
	private String loadTimeEnd;

	@ApiModelProperty("卸货时间起")
	private String unloadTimeStart;

	@ApiModelProperty("卸货时间止")
	private String unloadTimeEnd;

	@ApiModelProperty("签收时间起")
	private String signTimeStart;

	@ApiModelProperty("签收时间始")
	private String signTimeEnd;

	@ApiModelProperty("拼单助手运单号")
	private List<String> carrierOrderCodeList;

	@ApiModelProperty("拼单助手需求单号")
	private List<String> demandOrderCodeList;

	@ApiModelProperty("对账单明细Ids")
	private String settleStatementItemIds;

	@ApiModelProperty("请求来源：1 后台，2 前台")
	private Integer source;
}
