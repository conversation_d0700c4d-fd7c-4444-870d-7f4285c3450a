package com.logistics.tms.controller.staff.response;

import com.logistics.tms.api.feign.common.model.CertificatePictureModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class OccupationalListResponseModel {

    @ApiModelProperty("人员ID")
    private Long staffId;
    @ApiModelProperty("从业资格记录Id")
    private Long occupationalRecordId;
    @ApiModelProperty("发证日期")
    private Date issueDate;
    @ApiModelProperty("继续教育有效期")
    private Date validDate;
    @ApiModelProperty("凭证地址")
    private List<CertificatePictureModel> imageList;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;
}
