package com.logistics.tms.controller.drivercostapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DriverCostApplyInvoiceRequestModel {

    @ApiModelProperty(value = "发票id,新增不传")
    private Long invoiceId;

    @ApiModelProperty("1 增值税，2 出租车，3 火车票，4 定额，5 卷票，6 机打，7 过路")
    private Integer type;

    @ApiModelProperty("发票名称")
    private String invoiceName;

    @ApiModelProperty("票据类型")
    private String invoiceType;

    @ApiModelProperty("发票代码")
    private String invoiceCode;

    @ApiModelProperty("发票号码")
    private String invoiceNum;

    @ApiModelProperty("合计金额")
    private BigDecimal totalPrice;

    @ApiModelProperty("合计税额")
    private BigDecimal totalTax;

    @ApiModelProperty("价税合计")
    private BigDecimal totalTaxAndPrice;

    @ApiModelProperty(value = "图片相对路径")
    private String imagePath;
}
