package com.logistics.appapi.controller.reservationorder.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/14 14:03
 */
@Data
public class ReservationOrderSummaryResponseDto {

    /**
     * 待预约-提货订单数量
     */
    private String loadOrderCount="";

    /**
     * 待预约-卸货订单数量
     */
    private String unloadOrderCount="";

    /**
     * 已预约-列表
     */
    private List<ReservationOrderSummaryListResponseDto> summaryList=new ArrayList<>();

}
