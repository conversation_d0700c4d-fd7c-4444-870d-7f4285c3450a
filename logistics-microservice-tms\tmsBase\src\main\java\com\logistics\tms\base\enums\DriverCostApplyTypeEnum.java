package com.logistics.tms.base.enums;

/**
 * 司机费用申请类型枚举
 * <p>
 * 费用类型：100 住宿费，101 装卸费，102 其他费用，103 加班费，104 劳保费，105 电话费，106 交通费；107 打印费，108 叉车费，109 盖雨布，110 破包赔偿，111 交通罚款
 * 200 维修费，201 车辆保养费，202 过路过桥费，203 停车费，204 尿素费，205 加油费
 * 300 核算检测费，301 医疗防护用品
 * 400 扣款
 */
public enum DriverCostApplyTypeEnum {
    DEFAULT(-1, ""),
    ACCOMMODATION_FEE(100, "住宿费"),
    LOAD_AND_UNLOAD_FEE(101, "装卸费"),
    OTHER_FEE(102, "其他费用"),
    OVERTIME_FEE(103, "加班费"),
    LABOUR_PROTECTION_FEE(104, "劳保费"),
    TELEPHONE_BILL(105, "电话费"),
    TRAFFIC_FEE(106, "交通费"),
    PRINTING_FEE(107, "打印费"),
    FORKLIFT_FEE(108, "叉车费"),
    COVER_TARPAULIN(109, "盖雨布"),
    DAMAGE_WRAP_INDEMNIFY(110, "破包赔偿"),
    TRAFFIC_FINE(111, "交通罚款"),

    MAINTAIN_FEE(200, "维修费"),
    VEHICLE_MAINTENANCE_FEE(201, "车辆保养费"),
    ROAD_TOLL(202, "过路过桥费"),
    PARKING_FEE(203, "停车费"),
    UREA_FEE(204, "尿素费"),
    OIL_FEE(205, "加油费"),

    NAT_FEE(300, "核算检测费"),
    MEDICAL_SUPPLIES_FEE(301, "医疗防护用品"),

    DEDUCTIONS(400, "扣款"),
    ;

    private final Integer key;
    private final String value;

    DriverCostApplyTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static DriverCostApplyTypeEnum getEnum(Integer key) {
        for (DriverCostApplyTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
