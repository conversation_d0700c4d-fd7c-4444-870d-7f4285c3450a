package com.logistics.tms.controller.reserveapply;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.reserveapply.ReserveApplyBiz;
import com.logistics.tms.controller.reserveapply.request.*;
import com.logistics.tms.controller.reserveapply.response.ReserveApplyDetailResponseModel;
import com.logistics.tms.controller.reserveapply.response.ReserveApplyListResponseModel;
import com.logistics.tms.controller.reserveapply.response.ReserveBalanceApplyDetailResponseModel;
import com.logistics.tms.controller.reserveapply.response.ReserveBalanceApplyListResponseModel;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/service/DriverReserveApply")
public class DriverReserveApplyController {

    @Resource
    private ReserveApplyBiz reserveApplyBiz;


    /**
     * 后台 - 备用金申请列表
     *
     * @param requestModel 请求Model
     * @return PageInfo<ReserveApplyListResponseModel>
     */
    @PostMapping("/reserveApplyList")
    public Result<PageInfo<ReserveApplyListResponseModel>> reserveApplyList(@RequestBody ReserveApplyListRequestModel requestModel) {
        return Result.success(reserveApplyBiz.reserveApplyList(requestModel));
    }

    /**
     * 后台 - 备用金申请列表导出查询
     *
     * @param requestModel 请求Model
     * @return List<ReserveApplyListResponseModel>
     */
    @PostMapping("/reserveApplyListExport")
    @IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<ReserveApplyListResponseModel>> reserveApplyListExport(@RequestBody ReserveApplyListRequestModel requestModel) {
        return Result.success(reserveApplyBiz.reserveApplyListExport(requestModel));
    }

    /**
     * 后台 - 备用金申请详情
     *
     * @param requestModel 请求Model
     * @return ReserveApplyDetailResponseModel
     */
    @PostMapping("/reserveApplyDetail")
    public Result<ReserveApplyDetailResponseModel> reserveApplyDetail(@RequestBody ReserveApplyDetailRequestModel requestModel) {
        return Result.success(reserveApplyBiz.reserveApplyDetail(requestModel));
    }

    /**
     * 后台 -备用金申请撤销
     *
     * @param requestModel 请求Model
     * @return Boolean
     */
    @PostMapping("/reserveApplyCancel")
    public Result<Boolean> reserveApplyCancel(@RequestBody ReserveApplyCancelRequestModel requestModel) {
        return Result.success(reserveApplyBiz.reserveApplyCancel(requestModel));
    }

    /**
     * 后台 - 备用金申请审核
     *
     * @param requestModel 请求Model
     * @return Boolean
     */
    @PostMapping("/reserveApplyAudit")
    public Result<Boolean> reserveApplyAudit(@RequestBody ReserveApplyAuditRequestModel requestModel) {
        return Result.success(reserveApplyBiz.reserveApplyAudit(requestModel));
    }

    /**
     * 后台 - 备用金申请打款
     *
     * @param requestModel 请求Model
     * @return Boolean
     */
    @PostMapping("/driverReserveRemit")
    public Result<Boolean> driverReserveRemit(@RequestBody ReserveApplyRemitRequestModel requestModel) {
        return Result.success(reserveApplyBiz.driverReserveRemit(requestModel));
    }

    /**
     * 小程序 - 备用金申请 / 重新提交
     *
     * @param requestModel 请求Model
     * @return boolean
     */
    @PostMapping(value = "/applet/applyReserveBalance")
    public Result<Boolean> applyReserveBalance(@RequestBody ApplyReserveBalanceRequestModel requestModel) {
        return Result.success(reserveApplyBiz.applyReserveBalance(requestModel));
    }

    /**
     * 小程序 - 备用金申请列表
     *
     * @param requestModel 请求Model
     * @return ReserveBalanceApplyListResponseModel
     */
    @PostMapping(value = "/applet/reserveBalanceApplyList")
    public Result<ReserveBalanceApplyListResponseModel> reserveBalanceApplyList(@RequestBody ReserveBalanceApplyListRequestModel requestModel) {
        return Result.success(reserveApplyBiz.reserveBalanceApplyList(requestModel));
    }

    /**
     * 小程序 - 备用金申请详情
     *
     * @param requestModel 请求Model
     * @return ReserveBalanceApplyDetailResponseModel
     */
    @PostMapping(value = "/applet/reserveBalanceApplyDetail")
    public Result<ReserveBalanceApplyDetailResponseModel> reserveBalanceApplyDetail(@RequestBody ReserveBalanceApplyDetailRequestModel requestModel) {
        return Result.success(reserveApplyBiz.reserveBalanceApplyDetail(requestModel));
    }

    /**
     * 小程序 - 备用金申请撤销
     *
     * @param requestModel 请求Model
     * @return boolean
     */
    @PostMapping(value = "/applet/cancel")
    public Result<Boolean> cancel(@RequestBody ReserveBalanceApplyCancelRequestModel requestModel) {
        return Result.success(reserveApplyBiz.cancel(requestModel));
    }
}
