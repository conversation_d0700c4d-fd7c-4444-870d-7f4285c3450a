<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TWorkOrderProcessMapper" >
    <select id="searchProcessListByOrderId" resultType="com.logistics.tms.controller.workordercenter.response.WorkOrderProcessResponseModel">
        select
        twop.status,
        twop.solve_source    as solveSource,
        twop.solve_time      as solveTime,
        twop.solve_desc as solveDesc,
        twop.solve_remark as solveRemark
        from t_work_order_process twop
        where twop.valid = 1
          and twop.work_order_id = #{workOrderId,jdbcType=BIGINT}
        order by twop.solve_time desc
    </select>
</mapper>