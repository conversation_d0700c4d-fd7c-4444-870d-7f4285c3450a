package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/25
 */
@Data
public class DriverVehicleInfoByCarrierOrderIdResponseModel {

	@ApiModelProperty("运单ID")
	private Long carrierOrderId;

	@ApiModelProperty("司机id")
	private Long driverId;

	@ApiModelProperty("司机名字")
	private String driverName;

	@ApiModelProperty("司机手机号")
	private String driverPhone;

	@ApiModelProperty("司机身份证")
	private String driverIdentityNumber;

	@ApiModelProperty("车辆id")
	private Long vehicleId;

	@ApiModelProperty("车牌号")
	private String vehicleNo;

	@ApiModelProperty("挂车车辆ID")
	private Long trailerVehicleId;

	@ApiModelProperty("挂车车牌号")
	private String trailerVehicleNo;

	@ApiModelProperty("承运商id")
	private Long companyCarrierId;

	@ApiModelProperty("需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
	private Integer entrustType;
}
