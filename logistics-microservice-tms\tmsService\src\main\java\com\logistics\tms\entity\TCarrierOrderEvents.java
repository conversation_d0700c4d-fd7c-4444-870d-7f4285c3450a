package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TCarrierOrderEvents extends BaseEntity {
    /**
    * 运单ID
    */
    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    /**
    * 事件 10 承接订单 20 调度车辆 30 已取消 35 修改车辆 40 修改车辆（待审核）45 审核车辆 50 修改车辆（已审核）55 修改车辆 (已驳回) 60 到达提货地 70 提货 80 到达卸货地 90 卸货 100 签收
    */
    @ApiModelProperty("事件 10 承接订单 20 调度车辆 30 已取消 35 修改车辆 40 修改车辆（待审核）45 审核车辆 50 修改车辆（已审核）55 修改车辆 (已驳回) 60 到达提货地 70 提货 80 到达卸货地 90 卸货 100 签收")
    private Integer event;

    /**
    * 事件描述
    */
    @ApiModelProperty("事件描述")
    private String eventDesc;

    /**
    * 事件内容
    */
    @ApiModelProperty("事件内容")
    private String remark;

    /**
    * 事件时间
    */
    @ApiModelProperty("事件时间")
    private Date eventTime;

    /**
    * 事件图片数量
    */
    @ApiModelProperty("事件图片数量")
    private Integer ticketsCount;

    /**
    * 操作人名称
    */
    @ApiModelProperty("操作人名称")
    private String operatorName;

    /**
    * 操作时间
    */
    @ApiModelProperty("操作时间")
    private Date operateTime;
}