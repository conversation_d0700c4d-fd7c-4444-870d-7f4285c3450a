package com.logistics.appapi.client.reservationorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetReservationInfo4H5RespModel {

    /**
     * 预约单id
     */
    @ApiModelProperty("预约单id")
    private String reservationOrderId;


    @ApiModelProperty("运单id")
    private String carrierOrderId;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("状态 0.可预约 1待签到 2已签到 3已失效4.异常 5.已完成")
    private Integer state;

    @ApiModelProperty("状态文本  0.可预约  1待签到 2已签到 3已失效4.异常 5.已完成")
    private String stateLabel = "";

    @ApiModelProperty("司机名称")
    private String driverName ;

    @ApiModelProperty("车辆")
    private String vehicleNumber;

    @ApiModelProperty("提货省名称")
    private String loadProvinceName;

    @ApiModelProperty("提货城市名字")
    private String loadCityName;

    @ApiModelProperty("提货县区名字")
    private String loadAreaName;

    @ApiModelProperty("卸货省名称")
    private String unLoadProvinceName;

    @ApiModelProperty("卸货城市名字")
    private String unLoadCityName;
    @ApiModelProperty("卸货县区名字")
    private String unLoadAreaName;


    @ApiModelProperty("提货仓库")
    private String loadWarehouse;


    @ApiModelProperty("卸货仓库")
    private String unLoadWarehouse ;

    @ApiModelProperty("0 否 1：是")
    private Integer ifNeedVerifyDriver;


    private Integer orderEntrustType ;


}
