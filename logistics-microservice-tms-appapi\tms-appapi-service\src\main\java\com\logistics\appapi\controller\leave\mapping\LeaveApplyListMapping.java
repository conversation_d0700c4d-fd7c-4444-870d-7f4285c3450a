package com.logistics.appapi.controller.leave.mapping;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.LeaveApplyAuditStatusEnum;
import com.logistics.appapi.base.enums.LeaveApplyTimeTypeEnum;
import com.logistics.appapi.base.enums.LeaveApplyTypeEnum;
import com.logistics.appapi.client.leave.response.DriverLeaveApplyListItemModel;
import com.logistics.appapi.client.leave.response.DriverLeaveApplyListResponseModel;
import com.logistics.appapi.controller.leave.response.LeaveApplyListItem;
import com.logistics.appapi.controller.leave.response.LeaveApplyListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;

import java.util.Date;
import java.util.List;

public class LeaveApplyListMapping extends MapperMapping<DriverLeaveApplyListResponseModel, LeaveApplyListResponseDto> {

    @Override
    public void configure() {

        DriverLeaveApplyListResponseModel source = getSource();
        LeaveApplyListResponseDto destination = getDestination();
        PageInfo leaveApplyPageInfo = source.getLeaveApplyListItem();

        List<DriverLeaveApplyListItemModel> leaveApplyListItemModels = leaveApplyPageInfo.getList();
        if (ListUtils.isNotEmpty(leaveApplyListItemModels)) {
            List<LeaveApplyListItem> leaveApplyListItems = MapperUtils.mapper(leaveApplyListItemModels, LeaveApplyListItem.class,
                    new LeaveApplyListItemMapping());
            leaveApplyPageInfo.setList(leaveApplyListItems);
        }
        destination.setLeaveApplyListItem(leaveApplyPageInfo);
    }

    /**
     * Item转换mapping
     */
    class LeaveApplyListItemMapping extends MapperMapping<DriverLeaveApplyListItemModel, LeaveApplyListItem> {

        @Override
        public void configure() {

            DriverLeaveApplyListItemModel source = getSource();
            LeaveApplyListItem destination = getDestination();

            // 名字转换
            String leaveApplyStaff = String.format(CommonConstant.NAME_MOBILE_FORMAT, source.getStaffName(), source.getStaffMobile());
            destination.setLeaveApplyStaff(leaveApplyStaff);

            // 审核状态转换
            destination.setLeaveAuditStatusLabel(LeaveApplyAuditStatusEnum.getEnumByKey(source.getLeaveAuditStatus()).getValue());

            // 请假类型转换
            destination.setLeaveTypeLabel(LeaveApplyTypeEnum.getEnumByKey(source.getLeaveType()).getValue());

            // 请假时间转换
            destination.setLeaveStartTime(leaveTimeConversion(source.getLeaveStartTime(), source.getLeaveStartTimeType()));
            destination.setLeaveEndTime(leaveTimeConversion(source.getLeaveEndTime(), source.getLeaveEndTimeType()));
        }
    }

    // 请假时间转换
    private String leaveTimeConversion(Date leaveDate, Integer leaveDateType) {
        String leaveDateLabel = DateUtils.dateToString(leaveDate, DateUtils.DATE_TO_STRING_SHORT_PATTERN);
        String leaveDateTypeLabel = LeaveApplyTimeTypeEnum.getEnumByKey(leaveDateType).getValue();
        return String.join(" ", leaveDateLabel, leaveDateTypeLabel);
    }
}
