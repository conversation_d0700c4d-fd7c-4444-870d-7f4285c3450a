<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TFreightMapper" >
    <select id="getByCompanyIdAndRoleType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_freight
        where valid = 1
        and object_id = #{companyId,jdbcType = BIGINT}
        and object_type = #{roleType,jdbcType = INTEGER}
    </select>
    
    <select id="searchEntrustFreightList" resultType="com.logistics.tms.api.feign.freight.model.SearchFreightListResponseModel">
        select
            tf.id as freightId,
            tf.enabled as enabled,
            tcc.id as companyId,
            tc.company_name as companyName
        from t_freight tf
            left join t_company_entrust tcc on tcc.id = tf.object_id and tcc.valid = 1
            left join t_company tc on tc.id = tcc.company_id and tc.valid = 1
        where tf.valid = 1
        <if test="params.roleType!=null">
            and tf.object_type = #{params.roleType,jdbcType = INTEGER}
        </if>
        <if test="params.companyName!=null">
            and instr(tc.company_name,#{params.companyName,jdbcType = VARCHAR})>0
        </if>
        order by tf.created_time desc
    </select>


    <select id="searchCarrierFreightList" resultType="com.logistics.tms.api.feign.freight.model.SearchFreightListResponseModel">
        select
            tf.id as freightId,
            tf.enabled as enabled,
            tce.id as companyId,
            tc.company_name as companyName
        from t_freight tf
            left join t_company_carrier tce on tce.id = tf.object_id and tce.valid = 1
            left join t_company tc on tc.id = tce.company_id and tc.valid = 1
        where tf.valid = 1
        <if test="params.roleType!=null">
            and tf.object_type = #{params.roleType,jdbcType = INTEGER}
        </if>
        <if test="params.companyName!=null">
            and instr(tc.company_name,#{params.companyName,jdbcType = VARCHAR})>0
        </if>
        order by tf.created_time desc
    </select>

    <select id="getEntrustFreightCompanyInfo" resultType="com.logistics.tms.api.feign.freight.model.FreightCompanyInfoResponseModel">
        select
        tf.id as freightId,
        tf.enabled as enabled,
        tce.id as companyId,
        tc.company_name as companyName
        from t_freight tf
        left join t_company_entrust tce on tce.id = tf.object_id and tce.valid = 1
        left join t_company tc on tc.id = tce.company_id and tc.valid = 1
        where tf.valid = 1
        and tf.id = #{freightId,jdbcType = BIGINT}
    </select>

    <select id="getCarrierFreightCompanyInfo" resultType="com.logistics.tms.api.feign.freight.model.FreightCompanyInfoResponseModel">
        select
        tf.id as freightId,
        tf.enabled as enabled,
        tce.id as companyId,
        tc.company_name as companyName
        from t_freight tf
        left join t_company_carrier tce on tce.id = tf.object_id and tce.valid = 1
        left join t_company tc on tc.id = tce.company_id and tc.valid = 1
        where tf.valid = 1
        and tf.id = #{freightId,jdbcType = BIGINT}
    </select>

    <select id="getDriverFreightInfo" resultType="com.logistics.tms.api.feign.freight.model.CompanyDriverFreightResponseModel">
        select
        tf.id as freightId,
        tfar.id as freightAddressRuleId,
        tfar.freight_type as freightType,
        tfar.freight_fee as  freightFee
        from t_freight tf
        left join t_freight_address tfa on tfa.freight_id = tf.id and tfa.valid = 1
        left join t_freight_address_rule tfar on tfar.freight_address_id = tfa.id and tfar.valid = 1
        where tf.valid = 1
        and tf.object_type = 2
        <if test="list != null and list.size > 0">
            <trim prefix="and(" suffix=")" prefixOverrides="or">
                <foreach collection="list" index="index" item="item" separator=" ">
                    or ( tf.object_id = #{item.companyCarrierId,jdbcType = BIGINT}
                    and tfa.from_province_id = #{item.fromProvinceId,jdbcType = BIGINT}
                    and tfa.from_city_id = #{item.fromCityId,jdbcType = BIGINT}
                    and tfa.from_area_id in (${item.fromAreaId})
                    and tfa.to_province_id = #{item.toProvinceId,jdbcType = BIGINT}
                    and tfa.to_city_id = #{item.toCityId,jdbcType = BIGINT}
                    and tfa.to_area_id in (${item.toAreaId})
                    and tfar.amount_from &lt; #{item.totalAmount,jdbcType = DECIMAL}
                    and tfar.amount_to &gt;= #{item.totalAmount,jdbcType = DECIMAL})
                </foreach>
            </trim>
        </if>
    </select>

    <select id="getEntrustFreightInfo" resultType="com.logistics.tms.api.feign.freight.model.GetFreightForEntrustResponseModel">
        select
        tf.id as freightId,
        tfar.freight_type as freightType,
        tfar.freight_fee as  freightFee
        from t_freight tf
        left join t_freight_address tfa on tfa.freight_id = tf.id and tfa.valid = 1
        left join t_freight_address_rule tfar on tfar.freight_address_id = tfa.id and tfar.valid = 1
        where tf.valid = 1
        and tf.object_type = 1
        and tf.object_id = #{params.companyEntrustId,jdbcType = BIGINT}
        and tfa.calc_type = #{params.calcType,jdbcType = INTEGER}
        <choose>
            <when test="params.warehouseId != null">
                and tfa.warehouse_id = #{params.warehouseId,jdbcType = BIGINT}
            </when>
            <otherwise>
                and tfa.warehouse_id = 0
            </otherwise>
        </choose>
        and tfa.from_province_id = #{params.fromProvinceId,jdbcType = BIGINT}
        and tfa.from_city_id = #{params.fromCityId,jdbcType = BIGINT}
        and tfa.from_area_id = #{params.fromAreaId,jdbcType = BIGINT}
        and tfa.to_province_id = #{params.toProvinceId,jdbcType = BIGINT}
        and tfa.to_city_id = #{params.toCityId,jdbcType = BIGINT}
        and tfa.to_area_id = #{params.toAreaId,jdbcType = BIGINT}
        and tfar.amount_from &lt; #{params.goodsAmount,jdbcType = DECIMAL}
        and tfar.amount_to &gt;= #{params.goodsAmount,jdbcType = DECIMAL}
    </select>

</mapper>