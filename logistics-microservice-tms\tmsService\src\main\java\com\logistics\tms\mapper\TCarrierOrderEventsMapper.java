package com.logistics.tms.mapper;

import com.logistics.tms.controller.carrierorderapplet.response.CarrierOrderLogisticsEventResponseModel;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TCarrierOrderEvents;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TCarrierOrderEventsMapper extends BaseMapper<TCarrierOrderEvents>{

    int batchInsertSelective(@Param("list") List<TCarrierOrderEvents> list);

    TCarrierOrderEvents selectEventByEventTypeAndCarrieriOrderId(@Param("carrierOrderId") Long carrierOrderId, @Param("eventType") Integer type);

    List<CarrierOrderLogisticsEventResponseModel> getEvent4App(@Param("carrierOrderId") Long carrierOrderId);

    List<TCarrierOrderEvents> selectByCarrierOrderId(@Param("carrierOrderId") Long carrierOrderId);

}