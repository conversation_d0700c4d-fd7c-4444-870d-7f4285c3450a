package com.logistics.tms.api.feign.vehicletire.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class VehicleTireListRequestModel extends AbstractPageForm<VehicleTireListRequestModel> {
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆机构：1 自主 2 外部 3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("轮胎牌号")
    private String tireBrand;
    @ApiModelProperty("更换日期上限")
    private String replaceDateFrom;
    @ApiModelProperty("更换日期下限")
    private String replaceDateTo;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("最后操作时间上限")
    private String lastModifiedTimeFrom;
    @ApiModelProperty("最后操作时间下限")
    private String lastModifiedTimeTo;
    @ApiModelProperty("结算状态：空全部，0 待结算，1 已结算")
    private Integer settlementStatus;
}
