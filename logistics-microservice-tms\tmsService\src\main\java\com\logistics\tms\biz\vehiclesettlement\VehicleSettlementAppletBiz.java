package com.logistics.tms.biz.vehiclesettlement;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.common.model.WaterMarkModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.carrierorderotherfee.CarrierOrderOtherFeeCommonBiz;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.vehiclesettlement.request.DriverReconciliationConfirmRequestModel;
import com.logistics.tms.controller.vehiclesettlement.request.SearchDriverReconciliationListRequestModel;
import com.logistics.tms.controller.vehiclesettlement.request.VehicleSettlementIdRequestModel;
import com.logistics.tms.controller.vehiclesettlement.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.MapUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author：wjf
 * @date：2021/4/12 13:38
 */
@Service
public class VehicleSettlementAppletBiz {

    @Autowired
    private TVehicleSettlementMapper tVehicleSettlementMapper;
    @Autowired
    private TVehicleSettlementRelationMapper tVehicleSettlementRelationMapper;
    @Autowired
    private TVehicleSettlementPaymentMapper tVehicleSettlementPaymentMapper;
    @Autowired
    private TVehicleSettlementDriverRelationMapper tVehicleSettlementDriverRelationMapper;
    @Autowired
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Autowired
    private TVehicleTireMapper tVehicleTireMapper;
    @Autowired
    private TOilFilledMapper tOilFilledMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TVehicleSettlementEventsMapper tVehicleSettlementEventsMapper;
    @Autowired
    private TVehicleTireNoMapper tVehicleTireNoMapper;
    @Autowired
    private CarrierOrderOtherFeeCommonBiz carrierOrderOtherFeeCommonBiz;

    /**
     * 司机账单列表
     * @param requestModel
     * @return
     */
    public PageInfo<SearchDriverReconciliationListResponseModel> searchDriverReconciliationList(SearchDriverReconciliationListRequestModel requestModel) {
        Long driverId = commonBiz.getLoginDriverAppletUserId();
        if (driverId == null || driverId.equals(CommonConstant.LONG_ZERO)){
            return new PageInfo<>(new ArrayList<>());
        }
        requestModel.setDriverId(driverId);
        requestModel.enablePaging();
        List<SearchDriverReconciliationListResponseModel> list = tVehicleSettlementMapper.searchDriverReconciliationList(requestModel);
        return new PageInfo<>(list);
    }

    /**
     * 司机账单列表数量统计
     * @param requestModel
     * @return
     */
    public DriverReconciliationListCountResponseModel driverReconciliationListCount(SearchDriverReconciliationListRequestModel requestModel) {
        Long driverId = commonBiz.getLoginDriverAppletUserId();
        if (driverId == null || driverId.equals(CommonConstant.LONG_ZERO)){
            return new DriverReconciliationListCountResponseModel();
        }
        requestModel.setDriverId(driverId);
        return tVehicleSettlementMapper.driverReconciliationListCount(requestModel);
    }

    /**
     * 司机账单详情
     * @param requestModel
     * @return
     */
    public DriverReconciliationDetailResponseModel driverReconciliationDetail(VehicleSettlementIdRequestModel requestModel) {
        Long driverId = commonBiz.getLoginDriverAppletUserId();
        if (driverId == null || driverId.equals(CommonConstant.LONG_ZERO)){
            return new DriverReconciliationDetailResponseModel();
        }
        DriverReconciliationDetailResponseModel detail = tVehicleSettlementMapper.driverReconciliationDetail(requestModel.getVehicleSettlementId(), driverId);
        if (detail == null){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLEMENT_EMPTY);
        }
        //保险费用合计=商业险+交强险+货物险应扣除费用合计+个人意外险扣除费用合计
        detail.setInsuranceFee(detail.getInsuranceFee().add(detail.getAccidentInsuranceFee()).setScale(2,BigDecimal.ROUND_HALF_UP));
        //理赔费用合计
        detail.setClaimFee(detail.getAccidentInsuranceClaimFee().add(detail.getVehicleClaimFee()).setScale(2,BigDecimal.ROUND_HALF_UP));
        //查询轮胎费用数量
        List<TVehicleSettlementRelation> relationList = tVehicleSettlementRelationMapper.getByVehicleSettlementId(detail.getVehicleSettlementId());
        if (ListUtils.isNotEmpty(relationList)){
            List<TVehicleSettlementRelation> tireRelationList = relationList.stream().filter(item -> item.getObjectType().equals(VehicleSettlementTypeEnum.TIRE.getKey())).collect(Collectors.toList());
            List<Long> tireIdList=new ArrayList<>();
            for (TVehicleSettlementRelation tireRelation : tireRelationList) {
                tireIdList.add(tireRelation.getObjectId());
            }
            List<TVehicleTireNo> tireNoList=null;
            if(ListUtils.isNotEmpty(tireIdList)){
                tireNoList=tVehicleTireNoMapper.getByTiredIds(StringUtils.listToString(tireIdList,','));
            }
            Integer tireNum= CommonConstant.INTEGER_ZERO;
            if(ListUtils.isNotEmpty(tireNoList)) {
                for (TVehicleTireNo tVehicleTireNo : tireNoList) {
                    tireNum+=tVehicleTireNo.getAmount();
                }
            }
            detail.setTireCount(tireNum);
        }
        //开始结算后查询结算信息
        if (detail.getStatus() > VehicleSettlementStatementStatusEnum.WAIT_SETTLE.getKey()){
            List<TVehicleSettlementPayment> paymentList = tVehicleSettlementPaymentMapper.getByVehicleSettlementId(detail.getVehicleSettlementId());
            BigDecimal completeSettlePayable = BigDecimal.ZERO;
            if (ListUtils.isNotEmpty(paymentList)){
                for (TVehicleSettlementPayment payment : paymentList) {
                    completeSettlePayable = completeSettlePayable.add(payment.getPayFee());
                }
            }
            // 结清费用小于等于0，并且是已结清 且没有结算记录 结清是运费,未结清是0
            if (ListUtils.isEmpty(paymentList) && detail.getActualExpensesPayable().compareTo(CommonConstant.BIG_DECIMAL_ZERO) <=CommonConstant.INTEGER_ZERO
                    && VehicleSettlementStatementStatusEnum.SETTLED.getKey().equals(detail.getStatus())){
                detail.setCompleteSettlePayable(detail.getActualExpensesPayable());
                detail.setWaitSettlePayable(CommonConstant.BIG_DECIMAL_ZERO);
            }else {
                detail.setCompleteSettlePayable(completeSettlePayable);
                detail.setWaitSettlePayable(detail.getActualExpensesPayable().subtract(completeSettlePayable).setScale(2, BigDecimal.ROUND_HALF_UP));
            }
            detail.setBillingRecordsCount(paymentList.size());
        }
        return detail;
    }

    /**
     * 司机确认账单
     * @param requestModel
     */
    @Transactional
    public void driverReconciliationConfirm(DriverReconciliationConfirmRequestModel requestModel) {
        Long driverId = commonBiz.getLoginDriverAppletUserId();
        if (driverId == null || driverId.equals(CommonConstant.LONG_ZERO)){
            return;
        }
        TVehicleSettlementDriverRelation tVehicleSettlementDriverRelation = tVehicleSettlementDriverRelationMapper.getByVehicleSettlementIdDriverId(requestModel.getVehicleSettlementId(), driverId);
        if (tVehicleSettlementDriverRelation == null){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLEMENT_EMPTY);
        }
        TVehicleSettlement tVehicleSettlement = tVehicleSettlementMapper.selectByPrimaryKey(tVehicleSettlementDriverRelation.getVehicleSettlementId());
        if (tVehicleSettlement == null || tVehicleSettlement.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLEMENT_EMPTY);
        }
        if (!tVehicleSettlement.getStatus().equals(VehicleSettlementStatementStatusEnum.WAIT_CONFIRM.getKey())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLE_STATEMENT_ERROR);
        }

        Date now = new Date();
        Integer driverRelationStatus;
        Date confirmTime = null;
        Integer vehicleSettlementStatus;
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getConfirmReconciliationType())){
            driverRelationStatus = VehicleSettlementDriverStatusEnum.CONFIRM.getKey();
            confirmTime = now;
            vehicleSettlementStatus = VehicleSettlementStatementStatusEnum.WAIT_SETTLE.getKey();
        }else{
            driverRelationStatus = VehicleSettlementDriverStatusEnum.REJECT.getKey();
            vehicleSettlementStatus = VehicleSettlementStatementStatusEnum.WAIT_HANDLE.getKey();
        }

        //对司机确认对账的图片加水印
        String confirmImageUrl = null;
        if (StringUtils.isNotBlank(requestModel.getCommitImageUrl())){
            WaterMarkModel waterMarkModel = new WaterMarkModel();
            waterMarkModel.setWaterMark(DateUtils.dateToString(now,DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
            confirmImageUrl = commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.APPLET_DRIVER_SETTLE_IMAGE.getKey(),"",requestModel.getCommitImageUrl(),waterMarkModel);
        }

        //修改账单司机关系表状态和确认信息
        TVehicleSettlementDriverRelation vehicleSettlementDriverRelation = new TVehicleSettlementDriverRelation();
        vehicleSettlementDriverRelation.setId(tVehicleSettlementDriverRelation.getId());
        vehicleSettlementDriverRelation.setStatus(driverRelationStatus);
        vehicleSettlementDriverRelation.setCommitImageUrl(confirmImageUrl);
        vehicleSettlementDriverRelation.setConfirmTime(confirmTime);
        vehicleSettlementDriverRelation.setSettlementReasonRemark(requestModel.getSettlementReasonRemark());
        commonBiz.setBaseEntityModify(vehicleSettlementDriverRelation, BaseContextHandler.getUserName());
        tVehicleSettlementDriverRelationMapper.updateByPrimaryKeySelective(vehicleSettlementDriverRelation);

        //修改车辆结算表状态
        TVehicleSettlement vehicleSettlement = new TVehicleSettlement();
        vehicleSettlement.setId(tVehicleSettlementDriverRelation.getVehicleSettlementId());
        vehicleSettlement.setStatus(vehicleSettlementStatus);
        commonBiz.setBaseEntityModify(vehicleSettlement, BaseContextHandler.getUserName());
        tVehicleSettlementMapper.updateByPrimaryKeySelective(vehicleSettlement);
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getConfirmReconciliationType())) {
            TVehicleSettlementEvents event = new TVehicleSettlementEvents();
            event.setVehicleSettlementId(tVehicleSettlementDriverRelation.getVehicleSettlementId());
            event.setEvent(VehicleSettlementEventsEnum.CONFIRM.getKey());
            event.setEventDesc(VehicleSettlementEventsEnum.CONFIRM.getValue());
            event.setEventTime(now);
            event.setOperateTime(now);
            event.setOperatorName(BaseContextHandler.getUserName());
            commonBiz.setBaseEntityAdd(event, BaseContextHandler.getUserName());
            tVehicleSettlementEventsMapper.insertSelective(event);
        }
    }

    /**
     * 司机确认详情
     * @param requestModel
     * @return
     */
    public DriverReconciliationConfirmDetailResponseModel driverReconciliationConfirmDetail(VehicleSettlementIdRequestModel requestModel) {
        Long driverId = commonBiz.getLoginDriverAppletUserId();
        if (driverId == null || driverId.equals(CommonConstant.LONG_ZERO)){
            return new DriverReconciliationConfirmDetailResponseModel();
        }
        return tVehicleSettlementMapper.driverReconciliationConfirmDetail(requestModel.getVehicleSettlementId(), driverId);
    }

    /**
     * 司机账单运单列表
     * @param requestModel
     * @return
     */
    public ReconciliationCarrierOrderDetailResponseModel driverReconciliationCarrierOrder(VehicleSettlementIdRequestModel requestModel) {
        Long driverId = commonBiz.getLoginDriverAppletUserId();
        if (driverId == null || driverId.equals(CommonConstant.LONG_ZERO)){
            return new ReconciliationCarrierOrderDetailResponseModel();
        }
        ReconciliationCarrierOrderDetailResponseModel detail = tVehicleSettlementMapper.driverReconciliationCarrierOrder(requestModel.getVehicleSettlementId(), driverId);
        if (detail == null || ListUtils.isEmpty(detail.getCarrierOrderIdList())){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //查询运单信息
        String carrierOrderIds = StringUtils.listToString(detail.getCarrierOrderIdList(), ',');
        List<ReconciliationCarrierOrderListResponseModel> carrierOrderList = tCarrierOrderMapper.getDriverReconciliationCarrierOrder(carrierOrderIds);
        //查询运单临时费用信息
        Map<Long, BigDecimal> driverOtherFeeMap = carrierOrderOtherFeeCommonBiz.getAuditFee(carrierOrderIds);
        //赋值临时费用
        if (!MapUtils.isEmpty(driverOtherFeeMap)){
            for (ReconciliationCarrierOrderListResponseModel order : carrierOrderList) {
                order.setDriverOtherFee(driverOtherFeeMap.get(order.getCarrierOrderId()));
            }
        }
        detail.setCarrierOrderList(carrierOrderList);
        return detail;
    }

    /**
     * 司机账单收款记录
     * @param requestModel
     * @return
     */
    public List<ReconciliationBillingRecordsResponseModel> driverReconciliationBillingRecords(VehicleSettlementIdRequestModel requestModel) {
        Long driverId = commonBiz.getLoginDriverAppletUserId();
        if (driverId == null || driverId.equals(CommonConstant.LONG_ZERO)){
            return new ArrayList<>();
        }
        return tVehicleSettlementPaymentMapper.driverReconciliationBillingRecords(requestModel.getVehicleSettlementId(), driverId);
    }

    /**
     * 司机账单轮胎费用列表
     * @param requestModel
     * @return
     */
    public ReconciliationTireDetailResponseModel driverReconciliationTire(VehicleSettlementIdRequestModel requestModel) {
        Long driverId = commonBiz.getLoginDriverAppletUserId();
        if (driverId == null || driverId.equals(CommonConstant.LONG_ZERO)){
            return new ReconciliationTireDetailResponseModel();
        }
        ReconciliationTireDetailResponseModel detail = tVehicleSettlementMapper.driverReconciliationTire(requestModel.getVehicleSettlementId(), driverId);
        if (detail == null || ListUtils.isEmpty(detail.getTireIdList())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_TIRE_IS_EMPTY);
        }
        List<GetVehicleTireByVehicleIdResponseModel> vehicleTireList = tVehicleTireMapper.getVehicleTireByIdsForSettlement(StringUtils.listToString(detail.getTireIdList(), ','));
        detail.setVehicleTireList(MapperUtils.mapper(vehicleTireList, ReconciliationTireListResponseModel.class));
        return detail;
    }

    /**
     * 司机账单充油费用列表
     * @param requestModel
     * @return
     */
    public ReconciliationOilFilledDetailResponseModel driverReconciliationOilFilled(VehicleSettlementIdRequestModel requestModel) {
        Long driverId = commonBiz.getLoginDriverAppletUserId();
        if (driverId == null || driverId.equals(CommonConstant.LONG_ZERO)){
            return new ReconciliationOilFilledDetailResponseModel();
        }
        ReconciliationOilFilledDetailResponseModel detail = tVehicleSettlementMapper.driverReconciliationOilFilled(requestModel.getVehicleSettlementId(), driverId);
        if (detail == null || ListUtils.isEmpty(detail.getOilFilledIdList())){
            throw new BizException(CarrierDataExceptionEnum.OIL_FILLED_NOT_EXIST);
        }
        List<GetOilFilledByVehicleIdResponseModel> oilFilledList = tOilFilledMapper.getOilFilledByIdsForSettlement(StringUtils.listToString(detail.getOilFilledIdList(), ','));
        detail.setOilFilledList(MapperUtils.mapper(oilFilledList, ReconciliationOilFilledListResponseModel.class));
        return detail;
    }
}
