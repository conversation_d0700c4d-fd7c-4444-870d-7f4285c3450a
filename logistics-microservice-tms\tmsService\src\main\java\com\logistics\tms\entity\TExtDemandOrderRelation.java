package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2025/07/22
*/
@Data
public class TExtDemandOrderRelation extends BaseEntity {
    /**
    * 运单id
    */
    @ApiModelProperty("运单id")
    private Long carrierOrderId;

    /**
    * 运单code
    */
    @ApiModelProperty("运单code")
    private String carrierOrderCode;

    /**
    * 运单对应的需求单id
    */
    @ApiModelProperty("运单对应的需求单id")
    private Long demandOrderId;

    /**
    * 需求单code
    */
    @ApiModelProperty("需求单code")
    private String demandOrderCode;

    /**
    * 补的需求单id
    */
    @ApiModelProperty("补的需求单id")
    private Long extDemandOrderId;

    /**
    * 补的需求单code
    */
    @ApiModelProperty("补的需求单code")
    private String extDemandOrderCode;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}