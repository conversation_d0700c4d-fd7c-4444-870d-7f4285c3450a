package com.logistics.appapi.client.driversafepromise.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 上传承诺书
 * @Author: sj
 * @Date: 2019/11/4 14:38
 */
@Data
public class UploadSafePromiseRequestModel {
    @ApiModelProperty("签订承诺书ID")
    private Long signSafePromiseId;
    @ApiModelProperty("手持承诺书图片地址")
    private String handPromiseUrl;
    @ApiModelProperty("签字责任书图片地址")
    private String signResponsibilityUrl;
}
