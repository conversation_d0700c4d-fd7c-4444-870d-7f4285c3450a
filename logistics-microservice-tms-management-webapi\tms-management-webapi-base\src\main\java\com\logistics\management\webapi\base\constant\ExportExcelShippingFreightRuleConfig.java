package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/7/11 19:51
 */
public class ExportExcelShippingFreightRuleConfig {
    private ExportExcelShippingFreightRuleConfig() {

    }

    private static final Map<String, String> VEHICLE_ASSET_INFO;

    static {
        VEHICLE_ASSET_INFO = new LinkedHashMap<>();
        VEHICLE_ASSET_INFO.put("状态","enabledStr");
        VEHICLE_ASSET_INFO.put("发货市","fromCityName");
        VEHICLE_ASSET_INFO.put("发货区","fromAreaName");
        VEHICLE_ASSET_INFO.put("收货市","toCityName");
        VEHICLE_ASSET_INFO.put("收货区","toAreaName");
        VEHICLE_ASSET_INFO.put("运距","carrierDistance");
        VEHICLE_ASSET_INFO.put("零担数量","startEndCount");
        VEHICLE_ASSET_INFO.put("零担价格","price");
        VEHICLE_ASSET_INFO.put("4.2车长报价","fourPointTwo");
        VEHICLE_ASSET_INFO.put("6.8车长报价","sixPointEight");
        VEHICLE_ASSET_INFO.put("9.6车长报价","ninePointSix");
        VEHICLE_ASSET_INFO.put("13.7车长报价","thirteenPointSevenFive");
        VEHICLE_ASSET_INFO.put("17.5车长报价","seventeenPointFive");
        VEHICLE_ASSET_INFO.put("操作人","lastModifiedBy");
        VEHICLE_ASSET_INFO.put("操作时间","lastModifiedTime");

    }

    public static Map<String, String> getHeadMap() {
        return VEHICLE_ASSET_INFO;
    }
}
