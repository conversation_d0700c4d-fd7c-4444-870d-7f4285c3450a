package com.logistics.tms.biz.demandpayment;


import cn.hutool.core.collection.CollectionUtil;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.DemandOrderStatusEnum;
import com.logistics.tms.base.enums.EntrustDataExceptionEnum;
import com.logistics.tms.base.enums.FreightTypeEnum;
import com.logistics.tms.base.enums.PriceTypeEnum;
import com.logistics.tms.biz.carrierorder.CarrierOrderForLeYiBiz;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.payment.PaymentBiz;
import com.logistics.tms.controller.carrierorder.response.DemandCarrierOrderModel;
import com.logistics.tms.controller.carrierorder.response.DemandCarrierOrderRecursiveModel;
import com.logistics.tms.entity.TCarrierOrder;
import com.logistics.tms.entity.TDemandOrder;
import com.logistics.tms.entity.TDemandPayment;
import com.logistics.tms.entity.TPayment;
import com.logistics.tms.mapper.TCarrierOrderMapper;
import com.logistics.tms.mapper.TDemandOrderMapper;
import com.logistics.tms.mapper.TDemandPaymentMapper;
import com.logistics.tms.mapper.TPaymentMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/23
 */
@Slf4j
@Service
public class DemandPaymentBiz {

	@Resource
	private TDemandPaymentMapper tDemandPaymentMapper;
	@Resource
	private TPaymentMapper tPaymentMapper;
	@Resource
	private TCarrierOrderMapper tCarrierOrderMapper;
	@Resource
	private CommonBiz commonBiz;
	@Resource
	private PaymentBiz paymentBiz;
	@Resource
	private TDemandOrderMapper tDemandOrderMapper;


	/**
	 * 刷新 需求单应付承运商费用结算表 的费用 一口价的
	 * @param demandOrderIds 需求单ids
	 */
	@Transactional
	public void refreshDemandCarrierPriceForShippingOrder(List<Long> demandOrderIds) {
		if (CollectionUtil.isEmpty(demandOrderIds)) {
			return;
		}
		List<TDemandOrder> tDemandOrders = tDemandOrderMapper.getByIds(StringUtils.listToString(demandOrderIds, ','));
		if (CollectionUtil.isEmpty(tDemandOrders)) {
			throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
		}

		//查询需求单费用
		List<TDemandPayment> tDemandPayments = tDemandPaymentMapper.getByDemandOrderIds(StringUtils.listToString(demandOrderIds, ','));
		if (CollectionUtil.isEmpty(tDemandPayments)) {
			return;
		}
		Map<Long, TDemandPayment> tDemandPaymentsMap = tDemandPayments.stream().collect(Collectors.toMap(TDemandPayment::getDemandOrderId, Function.identity()));

		//查询运单
		List<TCarrierOrder> tCarrierOrders = tCarrierOrderMapper.selectByDemandOrderIds(demandOrderIds);
		Map<Long, List<TCarrierOrder>> tCarrierOrdersMap = tCarrierOrders.stream().collect(Collectors.groupingBy(TCarrierOrder::getDemandOrderId));
		List<Long> carrierOrderIds = tCarrierOrders.stream().map(TCarrierOrder::getId).collect(Collectors.toList());

		//查询运单费用明细
		List<TPayment> tPayments = tPaymentMapper.getByCarrierOrderIds(StringUtils.listToString(carrierOrderIds, ','));
		Map<Long, TPayment> tPaymentsMap = tPayments.stream().collect(Collectors.toMap(TPayment::getCarrierOrderId, Function.identity()));

		//重新计算需求单费用
		List<TDemandPayment> updateDemandPayments = new ArrayList<>();
		List<TDemandOrder> updateDemandOrders = new ArrayList<>();
		for (TDemandOrder tDemandOrder : tDemandOrders) {
			//需求单结算
			TDemandPayment tDemandPayment = tDemandPaymentsMap.get(tDemandOrder.getId());

			List<TCarrierOrder> carrierOrders = tCarrierOrdersMap.getOrDefault(tDemandOrder.getId(), new ArrayList<>());

			BigDecimal carrierPriceTotal = BigDecimal.ZERO;
			BigDecimal settlementCostTotal = BigDecimal.ZERO;
			for (TCarrierOrder tCarrierOrder : carrierOrders) {

				TPayment tPayment = tPaymentsMap.get(tCarrierOrder.getId());

				//累加运单费用
				carrierPriceTotal = carrierPriceTotal.add(paymentBiz.calculateCarrierOrderCarrierPrice(tCarrierOrder));
				//累加运单结算费用
				if (tPayment != null) {
					settlementCostTotal = settlementCostTotal.add(tPayment.getSettlementCostTotal());
				}

			}


			//需求单是否已经触发签收或已放空
			boolean isSing = DemandOrderStatusEnum.SIGN_DISPATCH.getKey().equals(tDemandOrder.getEntrustStatus()) || CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfEmpty());

			//汇总运单的车主费用到需求单
			TDemandOrder updateDemandOrder = new TDemandOrder();
			updateDemandOrder.setId(tDemandOrder.getId());
			updateDemandOrder.setCarrierPrice(carrierPriceTotal);
			updateDemandOrder.setCarrierPriceType(PriceTypeEnum.FIXED_PRICE.getKey());
			commonBiz.setBaseEntityModify(updateDemandOrder, BaseContextHandler.getUserName());
			updateDemandOrders.add(updateDemandOrder);

			//汇总运单结算的车主费用到需求单结算
			if (isSing && tDemandPayment != null) {
				TDemandPayment updateDemandPayment = new TDemandPayment();
				updateDemandPayment.setId(tDemandPayment.getId());
				updateDemandPayment.setSettlementCostTotal(settlementCostTotal);
				updateDemandPayment.setPriceType(PriceTypeEnum.FIXED_PRICE.getKey());
				commonBiz.setBaseEntityModify(updateDemandPayment, BaseContextHandler.getUserName());
				updateDemandPayments.add(updateDemandPayment);
			}

		}

		if (CollectionUtil.isNotEmpty(updateDemandOrders)) {
			tDemandOrderMapper.batchUpdateByPrimaryKeySelective(updateDemandOrders);
		}
		if (CollectionUtil.isNotEmpty(updateDemandPayments)) {
			tDemandPaymentMapper.batchUpdate(updateDemandPayments);
		}


	}


}
