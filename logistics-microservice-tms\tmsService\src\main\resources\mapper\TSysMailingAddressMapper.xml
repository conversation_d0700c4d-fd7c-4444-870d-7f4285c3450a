<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TSysMailingAddressMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TSysMailingAddress">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="addressee" jdbcType="VARCHAR" property="addressee" />
    <result column="addressee_mobile" jdbcType="VARCHAR" property="addresseeMobile" />
    <result column="apply_scope" jdbcType="VARCHAR" property="applyScope" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, address, addressee, addressee_mobile, apply_scope, created_by, created_time, 
    last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_sys_mailing_address
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_sys_mailing_address
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TSysMailingAddress">
    insert into t_sys_mailing_address (id, address, addressee, 
      addressee_mobile, apply_scope, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{address,jdbcType=VARCHAR}, #{addressee,jdbcType=VARCHAR}, 
      #{addresseeMobile,jdbcType=VARCHAR}, #{applyScope,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TSysMailingAddress">
    insert into t_sys_mailing_address
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="addressee != null">
        addressee,
      </if>
      <if test="addresseeMobile != null">
        addressee_mobile,
      </if>
      <if test="applyScope != null">
        apply_scope,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="addressee != null">
        #{addressee,jdbcType=VARCHAR},
      </if>
      <if test="addresseeMobile != null">
        #{addresseeMobile,jdbcType=VARCHAR},
      </if>
      <if test="applyScope != null">
        #{applyScope,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TSysMailingAddress">
    update t_sys_mailing_address
    <set>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="addressee != null">
        addressee = #{addressee,jdbcType=VARCHAR},
      </if>
      <if test="addresseeMobile != null">
        addressee_mobile = #{addresseeMobile,jdbcType=VARCHAR},
      </if>
      <if test="applyScope != null">
        apply_scope = #{applyScope,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TSysMailingAddress">
    update t_sys_mailing_address
    set address = #{address,jdbcType=VARCHAR},
      addressee = #{addressee,jdbcType=VARCHAR},
      addressee_mobile = #{addresseeMobile,jdbcType=VARCHAR},
      apply_scope = #{applyScope,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>