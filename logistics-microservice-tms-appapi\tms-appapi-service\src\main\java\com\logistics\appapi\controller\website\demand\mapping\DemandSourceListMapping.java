package com.logistics.appapi.controller.website.demand.mapping;

import com.logistics.appapi.base.utils.FrequentMethodUtils;
import com.logistics.appapi.client.website.demand.response.DemandSourceListResponseModel;
import com.logistics.appapi.controller.website.demand.response.DemandSourceListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/10/31
 * @description:
 */
public class DemandSourceListMapping extends MapperMapping<DemandSourceListResponseModel, DemandSourceListResponseDto> {
    @Override
    public void configure() {
        DemandSourceListResponseModel source = getSource();
        DemandSourceListResponseDto destination = getDestination();
        if (StringUtils.isNotBlank(source.getContactName())) {
            destination.setContactName(FrequentMethodUtils.encodeUserName(source.getContactName()));
        }
        if(StringUtils.isNotBlank(source.getContactMobile())){
            destination.setContactMobile(FrequentMethodUtils.encodePhone(source.getContactMobile()));
        }
        if(StringUtils.isNotBlank(destination.getGoodsPrice())){
            destination.setGoodsPrice(FrequentMethodUtils.encodeString(destination.getGoodsPrice()));
        }
    }
}
