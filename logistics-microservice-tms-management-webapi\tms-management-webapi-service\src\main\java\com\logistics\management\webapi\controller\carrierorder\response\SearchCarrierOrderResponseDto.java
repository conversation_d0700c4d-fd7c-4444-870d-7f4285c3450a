package com.logistics.management.webapi.controller.carrierorder.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class SearchCarrierOrderResponseDto {

    @ApiModelProperty("运单ID")
    private String carrierOrderId = "";

    @ApiModelProperty("10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0 已取消")
    private String status = "";
    @ApiModelProperty("运单状态描述")
    private String statusDesc = "";
    @ExcelProperty("状态")
    @ApiModelProperty("运单状态导出用")
    private String exportStatusDesc="";

    @ExcelProperty("运单号")
    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";

    @ExcelProperty("客户单号")
    @ApiModelProperty("客户单号")
    private String customerOrderCode = "";

    @ApiModelProperty("需求单ID")
    private String demandOrderId = "";
    @ExcelProperty("需求单号")
    @ApiModelProperty("需求单号")
    private String demandOrderCode = "";

    @ExcelProperty("车牌")
    @ApiModelProperty("车牌号")
    private String vehicleNo = "";

    @ApiModelProperty("司机")
    private String driver = "";
    @ExcelProperty("司机姓名")
    private String exportDriverName="";
    @ExcelProperty("司机手机号")
    private String exportDriverMobile="";

    @ExcelProperty("司机运费合计(元)")
    @ApiModelProperty("司机运费")
    private String dispatchFreightFee="";

    @ExcelProperty("单位")
    @ApiModelProperty("货物单位")
    private String goodsUnit="";


    //发货地址
    @ExcelProperty("发货仓库")
    @ApiModelProperty("发货仓库")
    private String loadWarehouse = "";
    @ExcelProperty("发货省")
    @ApiModelProperty("发货地址")
    private String loadProvinceName = "";
    @ExcelProperty("发货市")
    @ApiModelProperty("发货地址")
    private String loadCityName = "";
    @ExcelProperty("发货区")
    @ApiModelProperty("发货地址")
    private String loadAreaName = "";
    @ExcelProperty("发货详细地址")
    @ApiModelProperty("发货地址")
    private String loadDetailAddress = "";
    private String loadAddress = "";//拼接后的数据，列表展示
    @ExcelProperty("发货人姓名")
    @ApiModelProperty("发货人姓名")
    private String consignorName = "";
    @ExcelProperty("发货人联系方式")
    @ApiModelProperty("发货人联系方式")
    private String consignorMobile = "";


    @ExcelProperty("预计承运")
    @ApiModelProperty("预计承运件数")
    private String expectAmount = "";

    @ExcelProperty("委托运费合计(元)")
    @ApiModelProperty("委托运费")
    private String freightFee = "";

    @ExcelProperty("预计车主运费(元)")
    @ApiModelProperty("预计车主费用合计")
    private String expectedCarrierPriceTotal = "";

    @ExcelProperty("实际车主运费(元)")
    @ApiModelProperty("车主费用合计")
    private String carrierPriceTotal = "";

    @ExcelProperty("实际提货")
    @ApiModelProperty("实际提货件数")
    private String loadAmount = "";

    @ExcelProperty("实际卸货")
    @ApiModelProperty("实际卸货件数")
    private String unloadAmount = "";

    @ExcelProperty("预计到货时间")
    @ApiModelProperty("预计到货时间")
    private String expectArrivalTime = "";


    //收货地址
    @ExcelProperty("收货仓库")
    @ApiModelProperty("收货仓库")
    private String unloadWarehouse = "";
    @ExcelProperty("收货省")
    @ApiModelProperty("收货省")
    private String unloadProvinceName = "";
    @ExcelProperty("收货市")
    @ApiModelProperty("收货市")
    private String unloadCityName = "";
    @ExcelProperty("收货区")
    @ApiModelProperty("收货区")
    private String unloadAreaName = "";
    @ExcelProperty("收货详细地址")
    @ApiModelProperty("收货详细地址")
    private String unloadDetailAddress = "";
    private String unloadAddress = "";//拼接后的数据，列表展示
    @ExcelProperty("收货人姓名")
    @ApiModelProperty("收货人姓名")
    private String receiverName = "";
    @ExcelProperty("收货人联系方式")
    @ApiModelProperty("收货人联系方式")
    private String receiverMobile = "";


    @ExcelProperty("里程数")
    @ApiModelProperty("里程数")
    private String expectMileage="";

    @ExcelProperty("实际到货时间")
    @ApiModelProperty("实际到货时间")
    private String unloadTime = "";

    @ExcelProperty("实际签收时间")
    @ApiModelProperty("实际签收时间")
    private String signTime = "";

    @ExcelProperty("品名")
    @ApiModelProperty("品名")
    private String goodsName = "";

    @ExcelProperty("规格")
    @ApiModelProperty("规格; 1.2.9 新增")
    private String goodsSize = "";

    @ExcelProperty("备注")
    @ApiModelProperty("备注")
    private String remark = "";

    @ExcelProperty("调度人")
    @ApiModelProperty("调度人")
    private String dispatchUserName = "";

    @ExcelProperty("车主")
    @ApiModelProperty("车主")
    private String carrierCompany = "";

    @ExcelProperty("委托客户")
    @ApiModelProperty("货主")
    private String entrustCompany = "";

    @ExcelProperty("运单生成时间")
    @ApiModelProperty("运单生成时间")
    private String dispatchTime = "";

    @ExcelProperty("回单数")
    @ApiModelProperty("回单数")
    private String carrierOrderTicketsAmount="";


    @ApiModelProperty("是否异常：0 否，1 是")
    private String ifObjection="";


    @ApiModelProperty("是否是云盘运单 1是 2不是")
    private String ifTrayOrder = "";
}
