package com.logistics.management.webapi.api.feign.dateremind.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Author: sj
 * @Date: 2019/5/31 8:55
 */
@Data
public class SaveOrModifyDateRemindRequestDto implements Serializable{
    @ApiModelProperty("日期提醒ID")
    private String dateRemindId;
    @ApiModelProperty("日期提醒名称")
    @NotBlank(message = "日期提醒名称必填")
    private String dateName;
    @ApiModelProperty("是否提醒：0 否，1 是")
    @NotBlank(message = "是否提醒必填")
    private String ifRemind;
    @ApiModelProperty("提醒天数")
    private String remindDays;
    @ApiModelProperty("备注")
    private String remark;
}
