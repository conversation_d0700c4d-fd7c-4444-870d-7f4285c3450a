package com.logistics.management.webapi.api.feign.insuarance.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2019/6/4 19:07
 */
@Data
public class CancelInsuranceRequestDto {
    @ApiModelProperty("保险id")
    @NotBlank(message = "id不能为空")
    private String insuranceId;
    @ApiModelProperty("作废原因")
    @NotBlank(message = "作废原因不能为空")
    private String cancelReason;
}
