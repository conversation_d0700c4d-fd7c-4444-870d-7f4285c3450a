package com.logistics.management.webapi.base.enums;

/**
 * 车牌颜色枚举
 * @Author: sj
 * @Date: 2019/6/10 17:41
 */
public enum PlateColorEnum {
    DEFAULT(0, ""),
    YELLOW(1,"黄色"),
    BLUE(2,"蓝色"),
    ;

    private Integer key;
    private String value;

    PlateColorEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static PlateColorEnum getEnum(Integer key) {
        for (PlateColorEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }

    public static PlateColorEnum getEnumByValue(String value) {
        for (PlateColorEnum t : values()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        return null;
    }
}
