package com.logistics.appapi.controller.drivercostapply.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2022/8/2 17:12
 */
@Data
public class SearchCostApplyListRequestDto extends AbstractPageForm<SearchCostApplyListRequestDto> {

    @ApiModelProperty(value = "申请时间（yyyy-MM）",required = true)
    @NotBlank(message = "请选择日期")
    private String applyTime;
}
