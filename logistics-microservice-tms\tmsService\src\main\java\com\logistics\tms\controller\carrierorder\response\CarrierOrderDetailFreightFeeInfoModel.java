package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CarrierOrderDetailFreightFeeInfoModel {

    //司机费用信息
    @ApiModelProperty("司机运费价格类型 1 单价 2 一口价")
    private Integer dispatchFreightFeeType;

    @ApiModelProperty("司机运费")
    private BigDecimal dispatchFreightFee;

    @ApiModelProperty("签收运费")
    private BigDecimal signFreightFee;

    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;

    @ApiModelProperty("多装多卸加价费用")
    private BigDecimal markupFee;

    //货主费用信息
    @ApiModelProperty("货主费用类型 1 单价 2 一口价")
    private Integer entrustFreightType;

    @ApiModelProperty("货主运费")
    private BigDecimal entrustFreight;

    @ApiModelProperty("货主结算报价类型 1 单价 2 整车价")
    private Integer entrustPriceType;

    @ApiModelProperty("货主结算金额合计")
    private BigDecimal entrustSettlementCostTotal;

    @ApiModelProperty(value = "预计货主费用类型，1单价，2一口价")
    private Integer expectEntrustFreightType;

    @ApiModelProperty(value = "预计货主费用")
    private BigDecimal expectEntrustFreight;

    //车主费用信息
    @ApiModelProperty("车主费用类型 1 单价 2 一口价")
    private Integer carrierFreightType;

    @ApiModelProperty("车主运费")
    private BigDecimal carrierFreight;

    @ApiModelProperty("车主结算报价类型 1 单价 2 整车价")
    private Integer carrierPriceType;

    @ApiModelProperty("车主结算金额合计")
    private BigDecimal carrierSettlementCostTotal;

    @ApiModelProperty("运单临时费用")
    private BigDecimal otherFee;
}
