package com.logistics.management.webapi.controller.settlestatement.tradition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TraditionCarrierAdjustCostResponseDto {

    @ApiModelProperty("对账单id")
    private String settleStatementId = "";

    @ApiModelProperty("车主运费")
    private String carrierFreight = "";

    @ApiModelProperty("运费费点")
    private String freightTaxPoint = "";

    @ApiModelProperty("费额合计")
    private String carrierFreightTotal = "";

    @ApiModelProperty("申请费用总额")
    private String applyFeeTotal = "";

    @ApiModelProperty("对账费用")
    private String reconciliationFee = "";

    @ApiModelProperty("调整费用")
    private String adjustCost = "";

    @ApiModelProperty("调整费用符号：1 ‘+’， 2 ‘-’")
    private String adjustCostSymbol = "";
}
