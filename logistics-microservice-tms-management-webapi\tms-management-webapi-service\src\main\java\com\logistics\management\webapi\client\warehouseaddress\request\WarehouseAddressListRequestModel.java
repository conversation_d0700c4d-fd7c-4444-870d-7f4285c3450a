package com.logistics.management.webapi.client.warehouseaddress.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WarehouseAddressListRequestModel extends AbstractPageForm<WarehouseAddressListRequestModel> {
    @ApiModelProperty("仓库名称")
    private String warehouseName;
    @ApiModelProperty("状态  空 全部  0 禁用 1 启用  ")
    private Integer enabled;
    @ApiModelProperty("货主")
    private String companyEntrustName;
    @ApiModelProperty("省市区")
    private String address;
    @ApiModelProperty("仓库Ids拼接")
    private String warehouseAddressIds;
    @ApiModelProperty("货主公司ID")
    private Long companyEntrustId;
}
