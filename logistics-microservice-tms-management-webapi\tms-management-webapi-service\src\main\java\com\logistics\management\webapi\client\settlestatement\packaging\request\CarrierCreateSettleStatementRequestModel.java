package com.logistics.management.webapi.client.settlestatement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/15
 */
@Data
public class CarrierCreateSettleStatementRequestModel {

	@ApiModelProperty(value = "对账月份 yyyy-MM-dd")
	private String settleStatementMonth;

	@ApiModelProperty("对账单名称")
	private String settleStatementName;

	@ApiModelProperty(value = "临时费用费点,0<=数值范围<=20")
	private BigDecimal otherFeeTaxPoint;

	@ApiModelProperty(value = "运费费点,0<=数值范围<=20")
	private BigDecimal freightTaxPoint;

	/*查询条件*/
	@ApiModelProperty("车主名模糊查询")
	private String companyCarrierName;

	/**
	 * 对账状态：-3 待完结，-2 未关联
	 */
	private Integer carrierSettleStatementStatus;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("需求单号")
	private String demandOrderCode;

	@ApiModelProperty("调度单号")
	private String dispatchOrderCode;

	@ApiModelProperty("车牌号")
	private String vehicleNo;

	@ApiModelProperty("司机（支持模糊搜索司机姓名或手机号）")
	private String driver;

	@ApiModelProperty("报价类型：1 单价，2 一口价")
	private Integer carrierPriceType;

	@ApiModelProperty("需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
	private Integer entrustType;

	@ApiModelProperty("发货地址，发货省市区+详细地址")
	private String loadAddress;

	@ApiModelProperty("收货地址，发货省市区+详细地址")
	private String unloadAddress;

	@ApiModelProperty("发货仓库")
	private String loadWarehouse;

	@ApiModelProperty("收货仓库")
	private String unloadWarehouse;

	@ApiModelProperty("提货时间起")
	private String loadTimeStart;

	@ApiModelProperty("提货时间止")
	private String loadTimeEnd;

	@ApiModelProperty("卸货时间起")
	private String unloadTimeStart;

	@ApiModelProperty("卸货时间始")
	private String unloadTimeEnd;

	@ApiModelProperty("签收时间起")
	private String signTimeStart;

	@ApiModelProperty("签收时间止")
	private String signTimeEnd;

	@ApiModelProperty("项目标签：1 石化板块，2 轮胎板块，3 涂料板块，4 其他")
	private Integer projectLabel;

	@ApiModelProperty("拼单助手运单号")
	private List<String> carrierOrderCodeList;

	@ApiModelProperty("拼单助手需求单号")
	private List<String> demandOrderCodeList;

	@ApiModelProperty("若有勾选导出则传此字段，运单ids  ','拼接")
	private String carrierOrderIds;

	@ApiModelProperty("来源 1 后台 2 前台")
	private Integer source;

	@ApiModelProperty("车主id")
	private Long companyCarrierId;

	private Long qiyaCompanyCarrierId;
}
