<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierContactMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCarrierContact" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT" />
    <result column="contact_name" property="contactName" jdbcType="VARCHAR" />
    <result column="contact_phone" property="contactPhone" jdbcType="VARCHAR" />
    <result column="identity_number" property="identityNumber" jdbcType="VARCHAR" />
    <result column="identity_face_file" property="identityFaceFile" jdbcType="VARCHAR" />
    <result column="identity_face_file_is_amend" property="identityFaceFileIsAmend" jdbcType="INTEGER" />
    <result column="identity_national_file" property="identityNationalFile" jdbcType="VARCHAR" />
    <result column="identity_national_file_is_amend" property="identityNationalFileIsAmend" jdbcType="INTEGER" />
    <result column="identity_validity" property="identityValidity" jdbcType="TIMESTAMP" />
    <result column="identity_is_forever" property="identityIsForever" jdbcType="INTEGER" />
    <result column="province_id" property="provinceId" jdbcType="BIGINT" />
    <result column="province_name" property="provinceName" jdbcType="VARCHAR" />
    <result column="city_id" property="cityId" jdbcType="BIGINT" />
    <result column="city_name" property="cityName" jdbcType="VARCHAR" />
    <result column="area_id" property="areaId" jdbcType="BIGINT" />
    <result column="area_name" property="areaName" jdbcType="VARCHAR" />
    <result column="certification_department_detail" property="certificationDepartmentDetail" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="enabled" property="enabled" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, company_carrier_id, contact_name, contact_phone, identity_number, identity_face_file, 
    identity_face_file_is_amend, identity_national_file, identity_national_file_is_amend, 
    identity_validity, identity_is_forever, province_id, province_name, city_id, city_name, 
    area_id, area_name, certification_department_detail, remark, enabled, created_by, 
    created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_carrier_contact
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_carrier_contact
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCarrierContact" >
    insert into t_carrier_contact (id, company_carrier_id, contact_name, 
      contact_phone, identity_number, identity_face_file, 
      identity_face_file_is_amend, identity_national_file, 
      identity_national_file_is_amend, identity_validity, 
      identity_is_forever, province_id, province_name, 
      city_id, city_name, area_id, 
      area_name, certification_department_detail, 
      remark, enabled, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{companyCarrierId,jdbcType=BIGINT}, #{contactName,jdbcType=VARCHAR}, 
      #{contactPhone,jdbcType=VARCHAR}, #{identityNumber,jdbcType=VARCHAR}, #{identityFaceFile,jdbcType=VARCHAR}, 
      #{identityFaceFileIsAmend,jdbcType=INTEGER}, #{identityNationalFile,jdbcType=VARCHAR}, 
      #{identityNationalFileIsAmend,jdbcType=INTEGER}, #{identityValidity,jdbcType=TIMESTAMP}, 
      #{identityIsForever,jdbcType=INTEGER}, #{provinceId,jdbcType=BIGINT}, #{provinceName,jdbcType=VARCHAR}, 
      #{cityId,jdbcType=BIGINT}, #{cityName,jdbcType=VARCHAR}, #{areaId,jdbcType=BIGINT}, 
      #{areaName,jdbcType=VARCHAR}, #{certificationDepartmentDetail,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{enabled,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCarrierContact" >
    insert into t_carrier_contact
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="companyCarrierId != null" >
        company_carrier_id,
      </if>
      <if test="contactName != null" >
        contact_name,
      </if>
      <if test="contactPhone != null" >
        contact_phone,
      </if>
      <if test="identityNumber != null" >
        identity_number,
      </if>
      <if test="identityFaceFile != null" >
        identity_face_file,
      </if>
      <if test="identityFaceFileIsAmend != null" >
        identity_face_file_is_amend,
      </if>
      <if test="identityNationalFile != null" >
        identity_national_file,
      </if>
      <if test="identityNationalFileIsAmend != null" >
        identity_national_file_is_amend,
      </if>
      <if test="identityValidity != null" >
        identity_validity,
      </if>
      <if test="identityIsForever != null" >
        identity_is_forever,
      </if>
      <if test="provinceId != null" >
        province_id,
      </if>
      <if test="provinceName != null" >
        province_name,
      </if>
      <if test="cityId != null" >
        city_id,
      </if>
      <if test="cityName != null" >
        city_name,
      </if>
      <if test="areaId != null" >
        area_id,
      </if>
      <if test="areaName != null" >
        area_name,
      </if>
      <if test="certificationDepartmentDetail != null" >
        certification_department_detail,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="enabled != null" >
        enabled,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierId != null" >
        #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="contactName != null" >
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null" >
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="identityNumber != null" >
        #{identityNumber,jdbcType=VARCHAR},
      </if>
      <if test="identityFaceFile != null" >
        #{identityFaceFile,jdbcType=VARCHAR},
      </if>
      <if test="identityFaceFileIsAmend != null" >
        #{identityFaceFileIsAmend,jdbcType=INTEGER},
      </if>
      <if test="identityNationalFile != null" >
        #{identityNationalFile,jdbcType=VARCHAR},
      </if>
      <if test="identityNationalFileIsAmend != null" >
        #{identityNationalFileIsAmend,jdbcType=INTEGER},
      </if>
      <if test="identityValidity != null" >
        #{identityValidity,jdbcType=TIMESTAMP},
      </if>
      <if test="identityIsForever != null" >
        #{identityIsForever,jdbcType=INTEGER},
      </if>
      <if test="provinceId != null" >
        #{provinceId,jdbcType=BIGINT},
      </if>
      <if test="provinceName != null" >
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null" >
        #{cityId,jdbcType=BIGINT},
      </if>
      <if test="cityName != null" >
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null" >
        #{areaId,jdbcType=BIGINT},
      </if>
      <if test="areaName != null" >
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="certificationDepartmentDetail != null" >
        #{certificationDepartmentDetail,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null" >
        #{enabled,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCarrierContact" >
    update t_carrier_contact
    <set >
      <if test="companyCarrierId != null" >
        company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="contactName != null" >
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null" >
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="identityNumber != null" >
        identity_number = #{identityNumber,jdbcType=VARCHAR},
      </if>
      <if test="identityFaceFile != null" >
        identity_face_file = #{identityFaceFile,jdbcType=VARCHAR},
      </if>
      <if test="identityFaceFileIsAmend != null" >
        identity_face_file_is_amend = #{identityFaceFileIsAmend,jdbcType=INTEGER},
      </if>
      <if test="identityNationalFile != null" >
        identity_national_file = #{identityNationalFile,jdbcType=VARCHAR},
      </if>
      <if test="identityNationalFileIsAmend != null" >
        identity_national_file_is_amend = #{identityNationalFileIsAmend,jdbcType=INTEGER},
      </if>
      <if test="identityValidity != null" >
        identity_validity = #{identityValidity,jdbcType=TIMESTAMP},
      </if>
      <if test="identityIsForever != null" >
        identity_is_forever = #{identityIsForever,jdbcType=INTEGER},
      </if>
      <if test="provinceId != null" >
        province_id = #{provinceId,jdbcType=BIGINT},
      </if>
      <if test="provinceName != null" >
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null" >
        city_id = #{cityId,jdbcType=BIGINT},
      </if>
      <if test="cityName != null" >
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null" >
        area_id = #{areaId,jdbcType=BIGINT},
      </if>
      <if test="areaName != null" >
        area_name = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="certificationDepartmentDetail != null" >
        certification_department_detail = #{certificationDepartmentDetail,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null" >
        enabled = #{enabled,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCarrierContact" >
    update t_carrier_contact
    set company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      contact_name = #{contactName,jdbcType=VARCHAR},
      contact_phone = #{contactPhone,jdbcType=VARCHAR},
      identity_number = #{identityNumber,jdbcType=VARCHAR},
      identity_face_file = #{identityFaceFile,jdbcType=VARCHAR},
      identity_face_file_is_amend = #{identityFaceFileIsAmend,jdbcType=INTEGER},
      identity_national_file = #{identityNationalFile,jdbcType=VARCHAR},
      identity_national_file_is_amend = #{identityNationalFileIsAmend,jdbcType=INTEGER},
      identity_validity = #{identityValidity,jdbcType=TIMESTAMP},
      identity_is_forever = #{identityIsForever,jdbcType=INTEGER},
      province_id = #{provinceId,jdbcType=BIGINT},
      province_name = #{provinceName,jdbcType=VARCHAR},
      city_id = #{cityId,jdbcType=BIGINT},
      city_name = #{cityName,jdbcType=VARCHAR},
      area_id = #{areaId,jdbcType=BIGINT},
      area_name = #{areaName,jdbcType=VARCHAR},
      certification_department_detail = #{certificationDepartmentDetail,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      enabled = #{enabled,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>