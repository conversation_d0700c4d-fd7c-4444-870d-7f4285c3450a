package com.logistics.management.webapi.controller.invoicingmanagement.tradition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/3/19 17:10
 */
@Data
public class TraditionGetInvoiceListResponseDto {
    /**
     * 发票id
     */
    @ApiModelProperty("发票id")
    private String invoiceId="";

    /**
     * 发票类型
     */
    @ApiModelProperty("发票类型")
    private String invoiceType="";

    /**
     * 发票类型文本
     */
    @ApiModelProperty("发票类型文本")
    private String invoiceTypeLabel="";

    /**
     * 发票代码
     */
    @ApiModelProperty("发票代码")
    private String invoiceCode="";

    /**
     * 发票号码
     */
    @ApiModelProperty("发票号码")
    private String invoiceNum="";

    /**
     * 开票日期
     */
    @ApiModelProperty("开票日期")
    private String invoiceDate="";

    /**
     * 发票金额
     */
    @ApiModelProperty("发票金额")
    private String invoiceAmount="";

    /**
     * 税率
     */
    @ApiModelProperty("税率")
    private String taxRate="";

    /**
     * 税额合计
     */
    @ApiModelProperty("税额合计")
    private String totalTaxAndPrice="";

    /**
     * 发票图片
     */
    @ApiModelProperty("发票图片")
    private String invoicePictureUrl="";

    /**
     * 操作人
     */
    @ApiModelProperty("操作人")
    private String lastModifiedBy="";

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    private String lastModifiedTime="";
}
