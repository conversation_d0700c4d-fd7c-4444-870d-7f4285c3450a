package com.logistics.tms.client.feign.basicdata.wechat.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CreateGroupChatRequestModel {

    @ApiModelProperty(value = "群聊Id")
    private String chatId;

    @ApiModelProperty(value = "群聊名称")
    private String name;

    @ApiModelProperty(value = "群主userId")
    private Long ownerId;

    @ApiModelProperty(value = "群成员userId")
    private List<Long> userList;
}