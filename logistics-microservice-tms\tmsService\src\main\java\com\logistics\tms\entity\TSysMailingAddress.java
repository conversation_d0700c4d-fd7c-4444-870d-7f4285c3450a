package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2023/01/09
*/
@Data
public class TSysMailingAddress extends BaseEntity {
    /**
    * 收件地址
    */
    @ApiModelProperty("收件地址")
    private String address;

    /**
    * 收件人
    */
    @ApiModelProperty("收件人")
    private String addressee;

    /**
    * 收件人
    */
    @ApiModelProperty("收件人")
    private String addresseeMobile;

    /**
    * 使用范围
    */
    @ApiModelProperty("使用范围")
    private String applyScope;
}