package com.logistics.management.webapi.api.impl.vehicleassetmanagement.mapper;

import com.logistics.management.webapi.api.feign.vehicleassetmanagement.dto.GetGpsInfoByVehicleNoResponseDto;
import com.logistics.tms.api.feign.vehicleassetmanagement.model.GetGpsInfoByVehicleNoResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @author: wjf
 * @date: 2019/10/9 13:51
 */
public class GetGpsInfoByVehicleNoMapping extends MapperMapping<GetGpsInfoByVehicleNoResponseModel,GetGpsInfoByVehicleNoResponseDto> {
    @Override
    public void configure() {
        GetGpsInfoByVehicleNoResponseModel source = getSource();
        GetGpsInfoByVehicleNoResponseDto destination = getDestination();
        if (source != null && source.getInstallTime() != null){
            destination.setInstallTime(DateUtils.dateToString(source.getInstallTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
    }
}
