package com.logistics.appapi.controller.reservationorder.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/14 14:37
 */
@Data
public class WaitReservationDetail4H5ResponseDto {

    /**
     * 预约类型：1 提货，2 卸货
     */
    private String reservationType="";

    /**
     * 预约类型
     */
    private String reservationTypeLabel="";

    /**
     * 发货地/收货地
     */
    private String address="";

    /**
     * 仓库名称
     */
    private String warehouse="";

    /**
     * 运单列表
     */
    private List<ReservationCarrierOrderListH5ResponseDto> orderList=new ArrayList<>();

    /**
     * 预计里程数（公里）
     */
    private String expectMileage="";

}
