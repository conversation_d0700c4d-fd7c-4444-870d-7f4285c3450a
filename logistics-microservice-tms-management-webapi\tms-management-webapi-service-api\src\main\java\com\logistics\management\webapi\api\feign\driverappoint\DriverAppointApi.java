package com.logistics.management.webapi.api.feign.driverappoint;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.driverappoint.dto.SearchDriverAppointRequestDto;
import com.logistics.management.webapi.api.feign.driverappoint.dto.SearchDriverAppointResponseDto;
import com.logistics.management.webapi.api.feign.driverappoint.hystrix.DriverAppointApiHystrix;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;

/**
 * 驾驶员预约记录
 *
 * <AUTHOR>
 * @date 2022/8/16 17:41
 */
@Api(value = "API - DriverAppointApiImpl-驾驶员预约记录管理")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = DriverAppointApiHystrix.class)
public interface DriverAppointApi {

    @ApiOperation(value = "查询驾驶员预约列表 v1.2.1")
    @PostMapping(value = "/api/driverAppointManagement/searchDriverAppointList")
    Result<PageInfo<SearchDriverAppointResponseDto>> searchDriverAppointList(@RequestBody SearchDriverAppointRequestDto requestDto);

    @ApiOperation(value = "导出驾驶员预约列表 v1.2.1")
    @GetMapping(value = "/api/driverAppointManagement/exportDriverAppoint")
    void exportDriverAppoint(SearchDriverAppointRequestDto requestDto, HttpServletResponse response);
}
