<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TSinopecOriginalTransitPortLocationDataMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TSinopecOriginalTransitPortLocationData" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="sinopec_original_data_id" property="sinopecOriginalDataId" jdbcType="BIGINT" />
    <result column="location_type" property="locationType" jdbcType="INTEGER" />
    <result column="location_code" property="locationCode" jdbcType="VARCHAR" />
    <result column="location_name" property="locationName" jdbcType="VARCHAR" />
    <result column="province_code" property="provinceCode" jdbcType="VARCHAR" />
    <result column="province_name" property="provinceName" jdbcType="VARCHAR" />
    <result column="city_code" property="cityCode" jdbcType="VARCHAR" />
    <result column="city_name" property="cityName" jdbcType="VARCHAR" />
    <result column="county_code" property="countyCode" jdbcType="VARCHAR" />
    <result column="county_name" property="countyName" jdbcType="VARCHAR" />
    <result column="town_code" property="townCode" jdbcType="VARCHAR" />
    <result column="town_name" property="townName" jdbcType="VARCHAR" />
    <result column="detail_address" property="detailAddress" jdbcType="VARCHAR" />
    <result column="latitude" property="latitude" jdbcType="VARCHAR" />
    <result column="longitude" property="longitude" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, sinopec_original_data_id, location_type, location_code, location_name, province_code, 
    province_name, city_code, city_name, county_code, county_name, town_code, town_name, 
    detail_address, latitude, longitude, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_sinopec_original_transit_port_location_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_sinopec_original_transit_port_location_data
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TSinopecOriginalTransitPortLocationData" >
    insert into t_sinopec_original_transit_port_location_data (id, sinopec_original_data_id, location_type, 
      location_code, location_name, province_code, 
      province_name, city_code, city_name, 
      county_code, county_name, town_code, 
      town_name, detail_address, latitude, 
      longitude, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{sinopecOriginalDataId,jdbcType=BIGINT}, #{locationType,jdbcType=INTEGER}, 
      #{locationCode,jdbcType=VARCHAR}, #{locationName,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, 
      #{provinceName,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, 
      #{countyCode,jdbcType=VARCHAR}, #{countyName,jdbcType=VARCHAR}, #{townCode,jdbcType=VARCHAR}, 
      #{townName,jdbcType=VARCHAR}, #{detailAddress,jdbcType=VARCHAR}, #{latitude,jdbcType=VARCHAR}, 
      #{longitude,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TSinopecOriginalTransitPortLocationData" >
    insert into t_sinopec_original_transit_port_location_data
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="sinopecOriginalDataId != null" >
        sinopec_original_data_id,
      </if>
      <if test="locationType != null" >
        location_type,
      </if>
      <if test="locationCode != null" >
        location_code,
      </if>
      <if test="locationName != null" >
        location_name,
      </if>
      <if test="provinceCode != null" >
        province_code,
      </if>
      <if test="provinceName != null" >
        province_name,
      </if>
      <if test="cityCode != null" >
        city_code,
      </if>
      <if test="cityName != null" >
        city_name,
      </if>
      <if test="countyCode != null" >
        county_code,
      </if>
      <if test="countyName != null" >
        county_name,
      </if>
      <if test="townCode != null" >
        town_code,
      </if>
      <if test="townName != null" >
        town_name,
      </if>
      <if test="detailAddress != null" >
        detail_address,
      </if>
      <if test="latitude != null" >
        latitude,
      </if>
      <if test="longitude != null" >
        longitude,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sinopecOriginalDataId != null" >
        #{sinopecOriginalDataId,jdbcType=BIGINT},
      </if>
      <if test="locationType != null" >
        #{locationType,jdbcType=INTEGER},
      </if>
      <if test="locationCode != null" >
        #{locationCode,jdbcType=VARCHAR},
      </if>
      <if test="locationName != null" >
        #{locationName,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null" >
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null" >
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null" >
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null" >
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="countyCode != null" >
        #{countyCode,jdbcType=VARCHAR},
      </if>
      <if test="countyName != null" >
        #{countyName,jdbcType=VARCHAR},
      </if>
      <if test="townCode != null" >
        #{townCode,jdbcType=VARCHAR},
      </if>
      <if test="townName != null" >
        #{townName,jdbcType=VARCHAR},
      </if>
      <if test="detailAddress != null" >
        #{detailAddress,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null" >
        #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null" >
        #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TSinopecOriginalTransitPortLocationData" >
    update t_sinopec_original_transit_port_location_data
    <set >
      <if test="sinopecOriginalDataId != null" >
        sinopec_original_data_id = #{sinopecOriginalDataId,jdbcType=BIGINT},
      </if>
      <if test="locationType != null" >
        location_type = #{locationType,jdbcType=INTEGER},
      </if>
      <if test="locationCode != null" >
        location_code = #{locationCode,jdbcType=VARCHAR},
      </if>
      <if test="locationName != null" >
        location_name = #{locationName,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null" >
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null" >
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null" >
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null" >
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="countyCode != null" >
        county_code = #{countyCode,jdbcType=VARCHAR},
      </if>
      <if test="countyName != null" >
        county_name = #{countyName,jdbcType=VARCHAR},
      </if>
      <if test="townCode != null" >
        town_code = #{townCode,jdbcType=VARCHAR},
      </if>
      <if test="townName != null" >
        town_name = #{townName,jdbcType=VARCHAR},
      </if>
      <if test="detailAddress != null" >
        detail_address = #{detailAddress,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null" >
        latitude = #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null" >
        longitude = #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TSinopecOriginalTransitPortLocationData" >
    update t_sinopec_original_transit_port_location_data
    set sinopec_original_data_id = #{sinopecOriginalDataId,jdbcType=BIGINT},
      location_type = #{locationType,jdbcType=INTEGER},
      location_code = #{locationCode,jdbcType=VARCHAR},
      location_name = #{locationName,jdbcType=VARCHAR},
      province_code = #{provinceCode,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      county_code = #{countyCode,jdbcType=VARCHAR},
      county_name = #{countyName,jdbcType=VARCHAR},
      town_code = #{townCode,jdbcType=VARCHAR},
      town_name = #{townName,jdbcType=VARCHAR},
      detail_address = #{detailAddress,jdbcType=VARCHAR},
      latitude = #{latitude,jdbcType=VARCHAR},
      longitude = #{longitude,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>