package com.logistics.management.webapi.api.impl.driversafemeeting.mapping;

import com.logistics.management.webapi.api.feign.driversafemeeting.dto.DriverSafeMeetingDetailResponseDto;
import com.logistics.management.webapi.base.enums.StaffPropertyEnum;
import com.logistics.management.webapi.base.enums.StudyStatusEnum;
import com.logistics.tms.api.feign.driversafemeeting.model.DriverSafeMeetingDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/11/4 17:34
 */
public class DriverSafeMeetingDetailListMapping extends MapperMapping<DriverSafeMeetingDetailResponseModel,DriverSafeMeetingDetailResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;
    public DriverSafeMeetingDetailListMapping(String imagePrefix , Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        DriverSafeMeetingDetailResponseModel source = getSource();
        DriverSafeMeetingDetailResponseDto destination = getDestination();
        if (source != null){
            destination.setStatusDesc(StudyStatusEnum.getEnum(source.getStatus()).getValue());
            if (StringUtils.isNotBlank(source.getSignImageUrl())){
                destination.setSignImageUrl(imagePrefix + imageMap.get(source.getSignImageUrl()));
            }
            if (StringUtils.isNotBlank(source.getStaffDriverImageUrl())){
                destination.setStaffDriverImageUrl(imagePrefix + imageMap.get(source.getStaffDriverImageUrl()));
            }
            destination.setStaffPropertyLabel(StaffPropertyEnum.getEnum(source.getStaffProperty()).getValue());
        }
    }
}
