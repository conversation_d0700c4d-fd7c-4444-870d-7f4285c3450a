package com.logistics.tms.mapper;


import com.logistics.tms.api.feign.driverfreight.model.DriverFreightListSearchRequestModel;
import com.logistics.tms.api.feign.driverfreight.model.DriverFreightListSearchResponseModel;
import com.logistics.tms.api.feign.extvehiclesettlement.model.ExtVehicleCarrierOrderModel;
import com.logistics.tms.biz.biddingorder.bo.BottomPriceRequestBo;
import com.logistics.tms.biz.carrierorder.model.SearchListStatisticsForYeloLifeModel;
import com.logistics.tms.biz.carrierorderticketsaudit.model.SearchCarrierOrderIdsForLeYiReceiptAuditBoModel;
import com.logistics.tms.biz.settlestatement.model.WaitSettleStatementCarrierOrderModel;
import com.logistics.tms.controller.carrierorder.request.*;
import com.logistics.tms.controller.carrierorder.response.*;
import com.logistics.tms.controller.carrierorderapplet.request.CarrierDriverRelationForAppletModel;
import com.logistics.tms.controller.carrierorderapplet.request.SearchCarrierOrderListAppRequestModel;
import com.logistics.tms.controller.carrierorderapplet.response.CarrierOrderDetailAppResponseModel;
import com.logistics.tms.controller.carrierorderapplet.response.PrintBillDetailResponseModel;
import com.logistics.tms.controller.carrierorderapplet.response.SearchCarrierOrderCountResponseModel;
import com.logistics.tms.controller.carrierorderapplet.response.SearchCarrierOrderListAppResponseModel;
import com.logistics.tms.controller.demandorder.request.WebDemandOrderDispatchVehicleRequestModel;
import com.logistics.tms.controller.demandorder.response.CorrectCarrierOrderDataStatisticsResponseModel;
import com.logistics.tms.controller.demandorder.response.MapDataStatisticsResponseModel;
import com.logistics.tms.controller.dispatchorder.request.DispatchOrderCarrierRequestModel;
import com.logistics.tms.controller.dispatchorder.response.DispatchOrderCarrierChildResponseModel;
import com.logistics.tms.controller.reservationorder.request.SearchCarrierReservationOrderReqModel;
import com.logistics.tms.controller.reservationorder.response.GetReservationInfo4H5RespModel;
import com.logistics.tms.controller.reservationorder.response.WaitReservationCarrierOrderListResponseModel;
import com.logistics.tms.controller.settlestatement.packaging.request.CarrierSettleStatementDetailListRequestModel;
import com.logistics.tms.controller.settlestatement.packaging.request.CarrierWaitSettleStatementListRequestModel;
import com.logistics.tms.controller.settlestatement.packaging.response.CarrierSettleStatementDetailListResponseModel;
import com.logistics.tms.controller.settlestatement.packaging.response.CarrierWaitSettleStatementListResponseModel;
import com.logistics.tms.controller.settlestatement.tradition.request.TraditionWaitSettleStatementListRequestModel;
import com.logistics.tms.controller.settlestatement.tradition.response.TraditionWaitSettleStatementListResponseModel;
import com.logistics.tms.controller.vehiclesettlement.response.GetCarrierOrderByVehicleIdResponseModel;
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleBySettlementMonthModel;
import com.logistics.tms.controller.vehiclesettlement.response.ReconciliationCarrierOrderListResponseModel;
import com.logistics.tms.entity.TCarrierOrder;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
@Mapper
public interface TCarrierOrderMapper extends BaseMapper<TCarrierOrder> {

    TCarrierOrder selectByPrimaryKeyDecrypt(Long id);

    int insertSelectiveEncrypt(TCarrierOrder tCarrierOrder);

    int updateByPrimaryKeySelectiveEncrypt(TCarrierOrder tCarrierOrder);

    int batchUpdateCarrierOrders(@Param("carrierOrders") List<TCarrierOrder> list);

    List<Long> searchCarrierOrderIdsForManagement(@Param("condition") SearchCarrierOrderListRequestModel searchCarrierOrderListRequestModel);

    List<SearchCarrierOrderListResponseModel> searchCarrierOrderForManagement(@Param("ids") String ids);

    List<Long> searchCarrierOrderIdsForLeYiManagement(@Param("condition") SearchCarrierOrderListForLeYiRequestModel requestModel);

    List<SearchCarrierOrderListForLeYiResponseModel> searchCarrierOrderForLeYiManagement(@Param("ids") String carrierOrderIds);

    int getWaitAuditVehicleCount(@Param("condition") WaitAuditVehicleInfoRequestModel requestModel);

    int getWaitAuditVehicleCountForLeYi(@Param("condition") WaitAuditVehicleInfoRequestModel requestModel);

    List<DispatchOrderCarrierChildResponseModel> getCarrierOrderByDispatchOrderId(@Param("params") DispatchOrderCarrierRequestModel requestModel);

    List<DemandOrderCarrierDetailResponseModel> getCarrierOrderInfoByDemandId(@Param("demandId") Long demandId);

    List<DemandOrderCarrierDetailResponseModel> getEmptyCarrierOrderInfoByDemandIds(@Param("demandOrderIds") String demandOrderIds);

    CarrierOrderDetailResponseModel getCarrierOrderDetailForManagementById(@Param("orderId") Long carrierOrderId);

    CarrierOrderDetailForLeYiResponseModel getCarrierOrderDetailForLeYiManagementById(@Param("orderId") Long carrierOrderId);

    List<TCarrierOrder> selectCarrierOrdersByIds(@Param("ids") String ids);

    List<TCarrierOrder> selectYunPanCarrierOrdersByIds(@Param("ids") String ids);

    List<CarrierOrderListBeforeSignUpResponseModel> selectCarrierOrderSignDetail(@Param("carrierOrdersIds") String carrierOrdersIds, @Param("demandOrderIds") String demandOrderIds);

    DownloadLadingBillResponseModel downloadLadingBill(@Param("carrierOrderId") Long carrierOrderId);
    DownloadLadingBillByCarrierCodeResponseModel downloadLadingBillByCarrierCode(@Param("carrierOrderCode") String carrierOrderCode);

    List<GetCarrierOrderResponseModel> getCarrierOrderByDemandOrderIds(@Param("demandOrderIds") String demandOrderIds, @Param("ifEmpty") Integer ifEmpty);

    List<DemandCarrierOrderRecursiveModel> getNotCancelByDemandOrderIds(@Param("demandOrderIds") String demandOrderIds);

    List<DemandCarrierOrderRecursiveModel> getByDemandOrderIds(@Param("demandOrderIds") String demandOrderIds);

    List<TCarrierOrder> getNotCancelByDemandOrderId(@Param("demandOrderId") Long demandOrderId);

    List<TCarrierOrder> getNotCancelEmptyByDemandOrderId(@Param("demandOrderId") Long demandOrderId);

    CarrierOrderStatusModel getCarrierStatus(@Param("carrierOrderId") Long carrierOrderId);

    List<Long> findOrderListByHistoryVehicleNo(@Param("vehicleNo") String vehicleNo, @Param("companyCarrierId") Long companyCarrierId);

    List<GetCarrierOrderByVehicleIdResponseModel> getCarrierOrderByIdsForSettlement(@Param("ids") String ids);

    List<ReconciliationCarrierOrderListResponseModel> getDriverReconciliationCarrierOrder(@Param("ids") String ids);

    List<GetVehicleBySettlementMonthModel> getVehicleBySettlementMonth(@Param("settlementMonth") String settlementMonth, @Param("companyCarrierId") Long companyCarrierId);

    //司机运费相关
    List<Long> searchDriverFreightOrdersIds(@Param("condition") DriverFreightListSearchRequestModel driverFreightListSearchRequestModel);

    List<DriverFreightListSearchResponseModel> searchDriverFreightOrdersByIds(@Param("carrierOrderIds") String carrierOrderIds);

    //applet
    List<Long> searchCarrierOrderIdsForApp(@Param("params") SearchCarrierOrderListAppRequestModel params);

    List<SearchCarrierOrderListAppResponseModel> searchCarrierOrderForApp(@Param("ids") String ids);

    SearchCarrierOrderCountResponseModel searchListAccountByApp(@Param("params") SearchCarrierOrderListAppRequestModel params);

    CarrierOrderDetailAppResponseModel carrierOrderDetailByApp(@Param("carrierOrderId") Long carrierOrderId);

    List<ExtVehicleCarrierOrderModel> getExtVehicleCarrierOrder(@Param("ids") String ids);

    List<LoadDetailResponseModel> getLoadDetailByIds(@Param("carrierOrderIds") String carrierOrderIds);

    TCarrierOrder getByCode(@Param("carrierOrderCode") String carrierOrderCode);

    List<TCarrierOrder> getCarrierOrderByCodes(@Param("carrierOrderCodeList") List<String> carrierOrderCodeList);

    List<ExportCarrierOrderTicketsModel> exportCarrierOrderTickets(@Param("carrierOrderIds") String carrierOrderIds);

    List<UpdateUnloadAddressDetailResponseModel> updateCarrierOrderUnloadAddressDetail(@Param("carrierOrderIds") String carrierOrderIds);

    CarrierOrderCorrectDetailResponseModel carrierOrderCorrectDetail(@Param("carrierOrderId") Long carrierOrderId);

    CarrierOrderInfoForAutoModel getCarrierOrderInfoForAuto(@Param("carrierOrderId") Long carrierOrderId);

    List<MapDataStatisticsResponseModel> mapDataStatistics();

    List<LogisticsCostStatisticsModel> logisticsCostStatistics(@Param("signTimeStart") String signTimeStart, @Param("signTimeEnd") String signTimeEnd);

    List<LogisticsLoadValidityStatisticsModel> logisticsLoadValidityStatistics(@Param("signTimeStart") String signTimeStart, @Param("signTimeEnd") String signTimeEnd);

    List<WaitCorrectStatisticsModel> waitCorrectStatistics();

    List<WaitLoadStatisticsModel> waitLoadStatistics();

    CorrectCarrierOrderDataStatisticsResponseModel selectCorrectCarrierOrderCount();

    List<WebDemandOrderDispatchVehicleRequestModel> selectCarrierOrdersCountVehicleByDemandOrderIds(@Param("demandOrderIds") String demandOrderIds);

    List<Long> searchCarrierOrderIdsForWeb(@Param("condition") SearchCarrierOrderListForWebRequestModel requestModel, @Param("companyCarrierId") Long loginUserCompanyCarrierId);

    List<SearchCarrierOrderListForWebResponseModel> searchCarrierOrderForWeb(@Param("condition") SearchCarrierOrderListForWebRequestModel requestModel, @Param("ids") String carrierOrderIds);

    CarrierOrderStatisticsResponseModel statistics(@Param("carrierOrderIds") String carrierOrderIds, @Param("companyCarrierId") Long companyCarrierId);

	CarrierOrderDetailForWebResponseModel getCarrierOrderDetailForWebById(@Param("carrierOrderId") Long carrierOrderId, @Param("companyCarrierId") Long loginUserCompanyCarrierId);

	ViewCarrierOrderTicketsResponseModel selectVehicleHistoryByCarrierOrderId(@Param("carrierOrderId") Long carrierOrderId);

    List<DemandCarrierOrderRecursiveModel> getCarrierOrderStatusByDemandOrderIds(@Param("demandOrderIds") String demandOrderIds);

    /**
     * 根据条件查询运单相关信息（不分页）
     */
    List<SearchCarrierOrderForParamsResponseModel> searchCarrierOrderForParams(@Param("params") SearchCarrierOrderForParamsRequestModel requestModel);


    /**
     * 根据运单id查询运单相关信息
     */
    GetValidCarrierOrderResponseModel searchCarrierOrderForById(@Param("carrierOrderId") Long carrierOrderId);

    List<CarrierOrderListBeforeSignUpForLeyiResponseModel> selectCarrierOrderSignDetailForLeyi(@Param("carrierOrderIds") String carrierOrderIds);

    List<CarrierOrderBeforeSignUpForYeloLifeResponseModel> selectCarrierOrderSignDetailForYeloLife(@Param("carrierOrderIds") String carrierOrderIds);

    /**
     * 根据车主id查询有效的云盘运单（排除已取消、已放空）
     */
    List<GetValidCarrierOrderResponseModel> getValidCarrierOrder(@Param("params") GetValidCarrierOrderRequestModel requestModel);

    /**
     * 统计司机运单数量
     *
     * @param driverId             司机id
     * @param status               运单状态 10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收，null时不根据此条件过滤
     * @param needExcludedOrderIds 要过滤的运单
     * @return 待提货数量
     */
    int getCountByDriverId(@Param("driverId") Long driverId, @Param("companyCarrierIds") String companyCarrierIds, @Param("status") List<Integer> status, @Param("needExcludedOrderIds") List<Long> needExcludedOrderIds);

    List<Long> searchCarrierOrderIdsForYeloLife(@Param("condition") SearchCarrierOrderListForYeloLifeRequestModel requestModel);

    List<SearchCarrierOrderListForYeloLifeResponseModel> searchCarrierOrderForYeloLife(@Param("carrierOrderIds") String carrierOrderIds);

    List<SearchListStatisticsForYeloLifeModel> searchCarrierOrderIdsStatisticsForYeloLife(@Param("carrierOrderIdList") List<Long> carrierOrderIdList);

    CarrierOrderDetailForYeloLifeResponseModel getCarrierOrderDetailForYeloLifeById(@Param("carrierOrderId") Long carrierOrderId);

    LoadDetailForYeloLifeResponseModel getLoadDetailForYeloLifeByIds(@Param("carrierOrderId") Long carrierOrderId);

    List<Long> searchYeloLifeCarrierOrderId(@Param("condition") SearchYeloLifeCarrierOrderRequestModel condition);

    List<SearchYeloLifeCarrierOrderResponseModel> searchYeloLifeCarrierOrder(@Param("carrierOrderIds") String carrierOrderIds);

    SearchYeloLifeCarrierOrderDetailResponseModel searchYeloLifeCarrierOrderDetail(SearchYeloLifeCarrierOrderDetailRequestModel requestModel);

    SignDetailForYeloLifeResponseModel carrierOrderListBeforeSignUpForYeloLife(@Param("carrierOrderId") Long carrierOrderId);

    int getSpecifiedStateCount(@Param("driverIds") String driverIds, @Param("vehicleIds") String vehicleIds, @Param("companyCarrierId") Long companyCarrierId);

    Long selectCarrierOrderIdByCode(@Param("carrierOrderCode") String carrierOrderCode, @Param("driverId") Long driverId);

    List<Long> selectCarrierWaitSettleStatementListIds(CarrierWaitSettleStatementListRequestModel requestModel);

    List<CarrierWaitSettleStatementListResponseModel> selectCarrierWaitSettleStatementList(@Param("ids") String ids);

    List<WaitSettleStatementCarrierOrderModel> selectCarrierWaitSettleStatementCarrierOrdersByIds(@Param("ids") String ids, @Param("companyCarrierId") Long companyCarrierId);

    List<CarrierSettleStatementDetailListResponseModel> selectSettleStatementCarrierOrder(@Param("requestModel") CarrierSettleStatementDetailListRequestModel requestModel, @Param("carrierOrderIdsStr") String carrierOrderIdsStr);

    List<Long> selectCarrierTraditionWaitSettleStatementListIds(TraditionWaitSettleStatementListRequestModel requestModel);

    List<TraditionWaitSettleStatementListResponseModel> selectCarrierTraditionWaitSettleStatementList(@Param("ids") String ids);

    /**
     * 批量更新运单车主对账状态
     *
     * @param ids      运单id集合
     * @param status   对账状态
     * @param operator 操作人
     */
    void batchUpdateCarrierSettleStatus(@Param("ids") List<Long> ids, @Param("status") Integer status, @Param("operator") String operator, @Param("updateWrapper") TCarrierOrder updateWrapper);

    /**
     * 根据回单审核筛选云盘 (发货、供应商直配)
     * @param boModel 查询Bo Model
     * @return Id 列表
     */
    List<Long> searchCarrierOrderIdsForLeYiReceiptAudit(@Param("condition") SearchCarrierOrderIdsForLeYiReceiptAuditBoModel boModel);

    /**
     * 根据Id查询回单审核运单信息
     * @param carrierOrderIds 运单ID
     * @return 运单信息
     */
    List<SearchCarrierOrderListForLeYiResponseModel> searchCarrierOrderForLeYiReceiptAudit(@Param("carrierOrderIds") List<Long> carrierOrderIds);

    /**
     * 小程序司机打印详情
     * @param carrierOrderId 运单ID
     * @return 司机打印详情
     */
    PrintBillDetailResponseModel printBillDetail(@Param("carrierOrderId") Long carrierOrderId);

    List<TCarrierOrder> selectByDemandOrderId(Long demandOrderId);

    BigDecimal selectMinCarrierPrice(BottomPriceRequestBo bottomPrice);

    List<TCarrierOrder> selectByDemandOrderIds(@Param("list") List<Long> demandOrderIds);

    List<Long> waitReservationOrderId(@Param("driverModel") List<CarrierDriverRelationForAppletModel> driverModel, @Param("reservationType") Integer reservationType);

    List<WaitReservationCarrierOrderListResponseModel> loadWaitReservationCarrierOrder(@Param("driverModel") List<CarrierDriverRelationForAppletModel> driverModel,
                                                                                       @Param("carrierOrderIdList") List<Long> carrierOrderIdList,
                                                                                       @Param("loadDemandOrderEntrustTypeList") List<Integer> loadDemandOrderEntrustTypeList);

    List<WaitReservationCarrierOrderListResponseModel> unloadWaitReservationCarrierOrder(@Param("driverModel") List<CarrierDriverRelationForAppletModel> driverModel,
                                                                                         @Param("carrierOrderIdList") List<Long> carrierOrderIdList,
                                                                                         @Param("unLoadDemandOrderEntrustTypeList") List<Integer> unLoadDemandOrderEntrustTypeList);

    GetReservationInfo4H5RespModel selectCarrierOrderReservationInfoByCode(@Param("carrierOrderCode") String carrierOrderCode);


    List<TCarrierOrder> searchCarrierReservationOrder(@Param("loginUserCompanyCarrierId") Long loginUserCompanyCarrierId, @Param("requestModel") SearchCarrierReservationOrderReqModel requestModel, @Param("carrierOrderIds") Collection<Long> carrierOrderIds);


    int selectExtCarrierOrderCountByDemandOrderCode(String demandOrderCode);

}