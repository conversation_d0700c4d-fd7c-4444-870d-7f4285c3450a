package com.logistics.tms.client.feign.basicdata.map.hystrix;

import com.logistics.tms.client.feign.basicdata.map.MapServiceApi;
import com.logistics.tms.client.feign.basicdata.map.request.GetMapByLonLatReqFeignModel;
import com.logistics.tms.client.feign.basicdata.map.response.GetMapByLonLatRespFeignModel;
import com.yelo.basicdata.api.feign.datamap.model.GetMapByLonLatRequestModel;
import com.yelo.basicdata.api.feign.datamap.model.GetMapByLonLatResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/22 15:16
 */
@Component
public class BasicMapServiceApiHystrix implements MapServiceApi {


    @Override
    public Result<GetMapByLonLatRespFeignModel> getMapByLonLat(GetMapByLonLatReqFeignModel requestModel) {
        return Result.timeout();
    }
}
