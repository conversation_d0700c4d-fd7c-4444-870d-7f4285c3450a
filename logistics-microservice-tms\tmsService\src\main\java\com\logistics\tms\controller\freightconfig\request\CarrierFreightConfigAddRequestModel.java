package com.logistics.tms.controller.freightconfig.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CarrierFreightConfigAddRequestModel {

    @ApiModelProperty(value = "需求类型", required = true)
    private List<String> entrustTypes;

    @ApiModelProperty(value = "价格模式", required = true)
    private Integer configType;

    @ApiModelProperty(value = "车主运价ID", required = true)
    private Long carrierFreightId;
}
