<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleTransportLineMapper" >
    <select id="getLineByVehicleId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_vehicle_transport_line
        where valid = 1
        and vehicle_no = #{vehicleNo,jdbcType=VARCHAR}
        and vehicle_id = #{vehicleId,jdbcType=BIGINT}
        and load_city_id = #{loadCityId,jdbcType=BIGINT}
        and load_area_id = #{loadAreaId,jdbcType=BIGINT}
        and unload_city_id = #{unloadCityId,jdbcType=BIGINT}
        and unload_area_id = #{unloadAreaId,jdbcType=BIGINT}
    </select>
</mapper>