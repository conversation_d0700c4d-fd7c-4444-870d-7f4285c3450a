package com.logistics.tms.controller.reservebalance.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DriverReserveBalanceListResponseModel {

    @ApiModelProperty(value = "余额ID")
    private Long reserveBalanceId;

    @ApiModelProperty(value = "司机ID")
    private Long driverId;

    @ApiModelProperty(value = "机构, 1 自主 2 外包 3 自营")
    private Integer driverProperty;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "司机手机号")
    private String driverMobile;

    @ApiModelProperty(value = "收款账户")
    private String bankAccount;

    @ApiModelProperty(value = "余额")
    private BigDecimal balanceAmount;

    @ApiModelProperty(value = "待核销金额")
    private BigDecimal awaitVerificationAmount;

    @ApiModelProperty(value = "已冲销金额")
    private BigDecimal verificationAmount;
}
