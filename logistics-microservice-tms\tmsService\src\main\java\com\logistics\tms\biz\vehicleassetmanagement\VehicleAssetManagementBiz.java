package com.logistics.tms.biz.vehicleassetmanagement;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.controller.companycarrier.request.FuzzySearchCompanyCarrierRequestModel;
import com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.tms.api.feign.personalaccidentinsurance.model.PersonalAccidentInsuranceListResponseModel;
import com.logistics.tms.controller.staffvehiclerelation.request.SearchStaffVehicleListRequestModel;
import com.logistics.tms.controller.staffvehiclerelation.response.SearchStaffVehicleListResponseModel;
import com.logistics.tms.biz.staffvehiclerelation.model.TStaffVehicleRelationModel;
import com.logistics.tms.controller.vehicleassetmanagement.request.FuzzyQueryVehicleInfoRequestModel;
import com.logistics.tms.controller.vehicleassetmanagement.response.FuzzyQueryVehicleInfoResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.ListJoinResultUtil;
import com.logistics.tms.base.utils.LocalStringUtil;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.vehicleassetmanagement.model.*;
import com.logistics.tms.client.BasicDataClient;
import com.logistics.tms.controller.vehicleassetmanagement.request.*;
import com.logistics.tms.controller.vehicleassetmanagement.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.basicdata.api.feign.file.model.FileUploadRequestModel;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.redisdistributedlock.DistributedLock;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.MapUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.entity.BaseEntity;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date:2019/6/3 10:15
 */

@Service
@Slf4j
public class VehicleAssetManagementBiz {
    @Autowired
    private TVehicleBasicMapper tqVehicleBasicMapper;
    @Autowired
    private TVehicleDrivingLicenseAnnualReviewMapper tqVehicleDrivingLicenseAnnualReviewMapper;
    @Autowired
    private TVehicleRoadTransportCertificateAnnualReviewMapper tqVehicleRoadTransportCertificateAnnualReviewMapper;
    @Autowired
    private TVehicleGpsRecordMapper tqVehicleGpsRecordMapper;
    @Autowired
    private TVehicleGradeEstimationRecordMapper tqVehicleGradeEstimationRecordMapper;
    @Autowired
    private TCertificationPicturesMapper tqCertificationPicturesMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TVehicleDrivingLicenseMapper tqVehicleDrivingLicenseMapper;
    @Autowired
    private TVehicleRoadTransportCertificateMapper tqVehicleRoadTransportCertificateMapper;
    @Autowired
    private TStaffBasicMapper tqStaffBasicMapper;
    @Autowired
    private TStaffDriverOccupationalRecordMapper tqStaffDriverOccupationalRecordMapper;
    @Autowired
    private TStaffDriverIntegrityExaminationRecordMapper tqStaffDriverIntegrityExaminationRecordMapper;
    @Autowired
    private TStaffDriverContinueLearningRecordMapper tqStaffDriverContinueLearningRecordMapper;
    @Autowired
    private BasicDataClient basicDataClient;
    @Autowired
    private TStaffDriverCredentialMapper tqStaffDriverCredentialMapper;
    @Autowired
    private TDateRemindMapper tqDateRemindMapper;
    @Autowired
    private TInsuranceMapper tqInsuranceMapper;
    @Autowired
    private TPersonalAccidentInsuranceMapper tqPersonalAccidentInsuranceMapper;
    @Autowired
    private TViolationRegulationsMapper tQViolationRegulationsMapper;
    @Autowired
    private TVehicleTypeMapper tQVehicleTypeMapper;
    @Autowired
    private TCarrierOrderMapper tcarrierOrderMapper;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private TCarrierVehicleRelationMapper tCarrierVehicleRelationMapper;
    @Autowired
    private TStaffVehicleRelationMapper tStaffVehicleRelationMapper;
    @Autowired
    private TOperateLogsMapper tOperateLogsMapper;

    /**
     * 模糊查询车辆信息
     *
     * @param requestModel
     * @return
     */
    public List<FuzzyQueryVehicleInfoResponseModel> fuzzyQueryVehicleInfo(FuzzyQueryVehicleInfoRequestModel requestModel) {
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getIsWebRequest())) {
            /*前台请求*/
            Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
                return new ArrayList<>();
            }
            requestModel.setCompanyCarrierId(loginUserCompanyCarrierId);
        }
        //默认查我司的车辆
        if (requestModel.getCompanyCarrierId() == null || CommonConstant.LONG_ZERO.equals(requestModel.getCompanyCarrierId())) {
            /*我司*/
            requestModel.setCompanyCarrierId(commonBiz.getQiyaCompanyCarrierId());
            //我司车辆才有是否运营中标识
            requestModel.setOperatingState(CommonConstant.INTEGER_ONE);
        }

        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            //排除当前车主已关联过的车辆
            requestModel.setNotInVehicleIds(tCarrierVehicleRelationMapper.getByCompanyCarrierId(requestModel.getCompanyCarrierId()).stream().map(TCarrierVehicleRelation::getVehicleId).collect(Collectors.toList()));
        }
        List<FuzzyQueryVehicleInfoResponseModel> vehicleInfoList = tqVehicleBasicMapper.fuzzyQueryVehicleInfo(requestModel);
        if (ListUtils.isNotEmpty(vehicleInfoList)) {
            List<Long> vehicleIdList = vehicleInfoList.stream().map(FuzzyQueryVehicleInfoResponseModel::getVehicleId).collect(Collectors.toList());
            SearchStaffVehicleListRequestModel model = new SearchStaffVehicleListRequestModel();
            model.setVehicleIds(StringUtils.listToString(vehicleIdList, ','));
            List<SearchStaffVehicleListResponseModel> relList = tStaffVehicleRelationMapper.searchStaffVehicleList(model);
            if (ListUtils.isNotEmpty(relList)) {
                ListJoinResultUtil.joinList(vehicleInfoList, relList,
                        (s, d) -> s.getVehicleId().equals(d.getVehicleId()) && d.getTrailerVehicleNo() != null,
                        (s, d) -> s.setTrailerVehicleNo(d.getTrailerVehicleNo()));
            }
        }
        return vehicleInfoList;
    }

    /**
     * 查询车辆资产管理列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<VehicleAssetManagementListResponseModel> searchVehicleBasicList(VehicleAssetManagementListRequestModel requestModel) {
        //车辆类型支持以"/"隔开多类型查询
        if (StringUtils.isNotBlank(requestModel.getVehicleType())) {
            String[] vehicleTypes = requestModel.getVehicleType().split("/");
            requestModel.setVehicleTypeList(Arrays.asList(vehicleTypes));
        }

        Long qiyaCompanyCarrierId = commonBiz.getQiyaCompanyCarrierId();
        //车主查询
        List<Long> companyIdList = null;
        if (StringUtils.isNotBlank(requestModel.getCompanyCarrierName())) {
            // 先根据公司名称、司机姓名、手机号模糊查询车主id
            FuzzySearchCompanyCarrierRequestModel fuzzyRequestModel = new FuzzySearchCompanyCarrierRequestModel();
            fuzzyRequestModel.setCompanyName(requestModel.getCompanyCarrierName());
            companyIdList = tCompanyCarrierMapper.fuzzyQueryCompanyCarrierInfo(fuzzyRequestModel)
                    .stream()
                    .map(FuzzySearchCompanyCarrierResponseModel::getCompanyId).collect(Collectors.toList());
            if (ListUtils.isEmpty(companyIdList)) {
                return new PageInfo<>(new ArrayList<>());
            }
        } else {
            //资产管理面板跳转只查询我司数据
            if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(requestModel.getIsOurCompany())) {
                companyIdList = Collections.singletonList(qiyaCompanyCarrierId);
            }
        }

        //获取我司id
        requestModel.setCompanyCarrierId(qiyaCompanyCarrierId);
        //开启分页
        requestModel.enablePaging();
        //分页查询车辆信息-以车主车辆关系表为维度
        List<VehicleAssetManagementListResponseModel> vehicleList = tCarrierVehicleRelationMapper.getVehicleList(requestModel, companyIdList);
        PageInfo pageInfo = new PageInfo(vehicleList);
        //车辆数据完整性
        getVehicleIfComplete(vehicleList);
        pageInfo.setList(vehicleList);
        return pageInfo;
    }
    /**
     * 车辆数据完整性
     *
     * @param  vehicleList
     */
    private void getVehicleIfComplete(List<VehicleAssetManagementListResponseModel> vehicleList) {
        if(ListUtils.isEmpty(vehicleList)){
            return;
        }
        List<Long> vehicleIdList = new ArrayList<>();//车辆id
        List<Long> companyIds = new ArrayList<>();//车主id
        for (VehicleAssetManagementListResponseModel model : vehicleList) {
            if (!vehicleIdList.contains(model.getVehicleBasicId())){
                vehicleIdList.add(model.getVehicleBasicId());
            }
            if (!companyIds.contains(model.getCompanyCarrierId())){
                companyIds.add(model.getCompanyCarrierId());
            }
        }
        String vehicleIds=StringUtils.listToString(vehicleIdList,',');

        Map<Long,Integer> vehicleLicenseCompleteMap=new HashMap<>();
        Map<Long,Integer> vehicleGradeEstimationCompleteMap=new HashMap<>();
        Map<Long,Integer> vehicleTransportCertificateCompleteMap=new HashMap<>();

        //查询车辆行驶证证件完整性
        List<GetVehicleIfCompleteByIdsModel> vehicleLicenseComplete = tqVehicleBasicMapper.getVehicleLicenseComplete(vehicleIds);
        if(ListUtils.isNotEmpty(vehicleLicenseComplete)){
            vehicleLicenseComplete.forEach(item->{
                vehicleLicenseCompleteMap.put(item.getVehicleId(),item.getIfComplete());
            });
        }
        //查询车辆等级评定证件完整性
        List<GetVehicleIfCompleteByIdsModel> vehicleGradeEstimationComplete = tqVehicleBasicMapper.getVehicleGradeEstimationComplete(vehicleIds);
        if(ListUtils.isNotEmpty(vehicleGradeEstimationComplete)){
            vehicleGradeEstimationComplete.forEach(item->{
                vehicleGradeEstimationCompleteMap.put(item.getVehicleId(),item.getIfComplete());
            });
        }
        //查询车辆道路运输证证件完整性
        List<GetVehicleIfCompleteByIdsModel> vehicleTransportCertificateComplete = tqVehicleBasicMapper.getVehicleTransportCertificateComplete(vehicleIds);
        if(ListUtils.isNotEmpty(vehicleTransportCertificateComplete)){
            vehicleTransportCertificateComplete.forEach(item->{
                vehicleTransportCertificateCompleteMap.put(item.getVehicleId(),item.getIfComplete());
            });
        }

        //车主信息
        Map<Long, FuzzySearchCompanyCarrierResponseModel> companyCarrierMap = tCompanyCarrierMapper.selectCompanyCarrierInfoByIds(companyIds)
                .stream().collect(Collectors.toMap(FuzzySearchCompanyCarrierResponseModel::getCompanyId, Function.identity(), (o1, o2) -> o2));

        //拼接信息
        for (VehicleAssetManagementListResponseModel model : vehicleList) {
            Integer licenseComplete = Optional.ofNullable(vehicleLicenseCompleteMap.get(model.getVehicleBasicId())).orElse(CommonConstant.INTEGER_ZERO);
            Integer gradeEstimationComplete = Optional.ofNullable(vehicleGradeEstimationCompleteMap.get(model.getVehicleBasicId())).orElse(CommonConstant.INTEGER_ZERO);
            Integer transportCertificateComplete = Optional.ofNullable(vehicleTransportCertificateCompleteMap.get(model.getVehicleBasicId())).orElse(CommonConstant.INTEGER_ZERO);
            //只要有一个全，则车辆证件信息完成性为不完整
            if(CommonConstant.INTEGER_ZERO.equals(licenseComplete) || CommonConstant.INTEGER_ZERO.equals(gradeEstimationComplete) || CommonConstant.INTEGER_ZERO.equals(transportCertificateComplete)){
                model.setIfComplete(CommonConstant.INTEGER_ZERO);
            }else{
                model.setIfComplete(CommonConstant.INTEGER_ONE);
            }
            //车主信息
            if (!MapUtils.isEmpty(companyCarrierMap) && companyCarrierMap.containsKey(model.getCompanyCarrierId())) {
                FuzzySearchCompanyCarrierResponseModel carrierResponseModel = companyCarrierMap.get(model.getCompanyCarrierId());
                if (carrierResponseModel != null) {
                    model.setIsOurCompany(carrierResponseModel.getIsOurCompany());
                    model.setCompanyCarrierType(carrierResponseModel.getCompanyType());
                    model.setCompanyCarrierName(carrierResponseModel.getCompanyName());
                    model.setCarrierContactName(carrierResponseModel.getContactName());
                    model.setCarrierContactPhone(carrierResponseModel.getContactPhone());
                }
            }
        }
    }

    /**
     * 导出
     * @param requestModel
     * @return
     */
    public List<ExportVehicleBasicInfoResponseModel> searchExportVehicleInfo(VehicleAssetManagementListRequestModel requestModel) {
        //车辆类型支持以"/"隔开多类型查询
        if (StringUtils.isNotBlank(requestModel.getVehicleType())) {
            String[] vehicleTypes = requestModel.getVehicleType().split("/");
            requestModel.setVehicleTypeList(Arrays.asList(vehicleTypes));
        }

        Long qiyaCompanyCarrierId = commonBiz.getQiyaCompanyCarrierId();
        //车主查询
        List<Long> companyIdList = null;
        if (StringUtils.isNotBlank(requestModel.getCompanyCarrierName())) {
            // 先根据公司名称、司机姓名、手机号模糊查询车主id
            FuzzySearchCompanyCarrierRequestModel fuzzyRequestModel = new FuzzySearchCompanyCarrierRequestModel();
            fuzzyRequestModel.setCompanyName(requestModel.getCompanyCarrierName());
            companyIdList = tCompanyCarrierMapper.fuzzyQueryCompanyCarrierInfo(fuzzyRequestModel)
                    .stream()
                    .map(FuzzySearchCompanyCarrierResponseModel::getCompanyId).collect(Collectors.toList());
            if (ListUtils.isEmpty(companyIdList)) {
                return new ArrayList<>();
            }
        } else {
            //资产管理面板跳转只查询我司数据
            if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(requestModel.getIsOurCompany())) {
                companyIdList = Collections.singletonList(qiyaCompanyCarrierId);
            }
        }

        //获取我司id
        requestModel.setCompanyCarrierId(qiyaCompanyCarrierId);
        //查询需要导出的数据
        List<ExportVehicleBasicInfoResponseModel> exportInfoList = tCarrierVehicleRelationMapper.getExportVehicleList(requestModel, companyIdList);
        if (ListUtils.isNotEmpty(exportInfoList)) {
            List<Long> vehicleIdList = new ArrayList<>();
            List<Long> drivingLicenseIdList = new ArrayList<>();
            List<Long> companyIds = new ArrayList<>();//车主id
            for (ExportVehicleBasicInfoResponseModel model : exportInfoList) {
                if (!vehicleIdList.contains(model.getVehicleBasicId())){
                    vehicleIdList.add(model.getVehicleBasicId());
                }
                if (!drivingLicenseIdList.contains(model.getVehicleDrivingLicenseId())){
                    drivingLicenseIdList.add(model.getVehicleDrivingLicenseId());
                }
                if (!companyIds.contains(model.getCompanyCarrierId())){
                    companyIds.add(model.getCompanyCarrierId());
                }
            }
            String vehicleIds = StringUtils.listToString(vehicleIdList, ',');

            //查询登记评定
            List<ExportGradeEstimationModel> exportGradeEstimationList = tqVehicleBasicMapper.getExportGradeEstimationByVehicleIds(vehicleIds);
            Map<Long, Date> gradeEstimationMap = new HashMap<>();
            exportGradeEstimationList.forEach(item -> gradeEstimationMap.put(item.getVehicleId(), item.getEstimationDate()));

            //查询车辆行驶证检查记录
            List<ExportDrivingLicenseReviewModel> exportDrivingLicenseList = tqVehicleBasicMapper.getExportDrivingLicenseReviewByDrivingIds(StringUtils.listToString(drivingLicenseIdList, ','));
            Map<Long, Date> drivingLicenseMap = new HashMap<>();
            exportDrivingLicenseList.forEach(item -> drivingLicenseMap.put(item.getDrivingLicenseId(), item.getCheckValidDate()));

            //查询道路运输证信息
            List<Long> vehicleRoadTransportCertificateIdList = new ArrayList<>();
            List<TVehicleRoadTransportCertificate> vehicleRoadTransportCertificateList = tqVehicleRoadTransportCertificateMapper.getByVehicleIds(vehicleIds);
            Map<Long, TVehicleRoadTransportCertificate> vehicleRoadTransportCertificateMap = new HashMap<>();
            for (TVehicleRoadTransportCertificate certificate : vehicleRoadTransportCertificateList) {
                vehicleRoadTransportCertificateMap.put(certificate.getVehicleId(), certificate);
                vehicleRoadTransportCertificateIdList.add(certificate.getId());
            }

            //查询道路运输证年审记录
            List<ExportRoadAnnualModel> exportRoadAnnualList = tqVehicleBasicMapper.getExportRoadAnnualByRoadIds(StringUtils.listToString(vehicleRoadTransportCertificateIdList, ','));
            Map<Long, Date> roadAnnualMap = new HashMap<>();
            exportRoadAnnualList.forEach(item -> roadAnnualMap.put(item.getVehicleRoadId(), item.getCheckValidDate()));

            //查询车辆GPS安装记录
            List<ExportGpsRecordModel> exportGpsRecordList = tqVehicleBasicMapper.findNewlyInstallTimeGpsRecord(vehicleIds);
            Map<Long, ExportGpsRecordModel> gpsRecordMap = new HashMap<>();
            exportGpsRecordList.forEach(item -> gpsRecordMap.put(item.getVehicleId(), item));

            // 车主信息
            Map<Long, FuzzySearchCompanyCarrierResponseModel> companyCarrierMap = tCompanyCarrierMapper.selectCompanyCarrierInfoByIds(companyIds)
                    .stream().collect(Collectors.toMap(FuzzySearchCompanyCarrierResponseModel::getCompanyId, Function.identity(), (o1, o2) -> o2));

            //拼接数据
            ExportGpsRecordModel gpsRecordModel;
            TVehicleRoadTransportCertificate tVehicleRoadTransportCertificate;
            for (ExportVehicleBasicInfoResponseModel model : exportInfoList) {
                model.setEstimationDate(gradeEstimationMap.get(model.getVehicleBasicId()));
                model.setCheckVehicleValidDate(drivingLicenseMap.get(model.getVehicleDrivingLicenseId()));

                //gps信息
                gpsRecordModel = gpsRecordMap.get(model.getVehicleBasicId());
                if (gpsRecordModel != null){
                    model.setInstallTime(gpsRecordModel.getInstallTime());
                    model.setTerminalType(gpsRecordModel.getTerminalType());
                    model.setSimNumber(gpsRecordModel.getSimNumber());
                    model.setGpsServiceProvider(gpsRecordModel.getGpsServiceProvider());
                }

                //道路运输证信息
                tVehicleRoadTransportCertificate = vehicleRoadTransportCertificateMap.get(model.getVehicleBasicId());
                if (tVehicleRoadTransportCertificate != null){
                    model.setVehicleRoadTransportCertificateId(tVehicleRoadTransportCertificate.getId());
                    model.setCertificationSign(tVehicleRoadTransportCertificate.getCertificationSign());
                    model.setBusinessLicenseNumber(tVehicleRoadTransportCertificate.getBusinessLicenseNumber());
                    model.setEconomicType(tVehicleRoadTransportCertificate.getEconomicType());
                    model.setTransportTonnage(tVehicleRoadTransportCertificate.getTransportTonnage());
                    model.setBusinessScope(tVehicleRoadTransportCertificate.getBusinessScope());
                    model.setRoadTransportCertificationDepartment(tVehicleRoadTransportCertificate.getCertificationDepartment());
                    model.setIssueDate(tVehicleRoadTransportCertificate.getIssueDate());
                    model.setObtainDate(tVehicleRoadTransportCertificate.getObtainDate());
                }

                if (model.getVehicleRoadTransportCertificateId() != null) {
                    model.setCheckRoadValidDate(roadAnnualMap.get(model.getVehicleRoadTransportCertificateId()));
                }

                // 封装车主信息
                if (!MapUtils.isEmpty(companyCarrierMap) && companyCarrierMap.containsKey(model.getCompanyCarrierId())) {
                    FuzzySearchCompanyCarrierResponseModel carrierResponseModel = companyCarrierMap.get(model.getCompanyCarrierId());
                    if (carrierResponseModel != null) {
                        model.setCompanyCarrierType(carrierResponseModel.getCompanyType());
                        model.setCompanyCarrierName(carrierResponseModel.getCompanyName());
                        model.setCarrierContactName(carrierResponseModel.getContactName());
                        model.setCarrierContactPhone(carrierResponseModel.getContactPhone());
                    }
                }
            }
        }
        return exportInfoList;
    }


    /**
     * 查询车辆资产详情
     *
     * @param requestModel
     * @return
     */
    public VehicleAssetManagementDetailResponseModel getDetailListById(VehicleAssetManagementDetailRequestModel requestModel) {
        if (requestModel.getCarrierVehicleId() == null || requestModel.getCarrierVehicleId() <= CommonConstant.INTEGER_ZERO) {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }
        //查询车辆是否存在
        TCarrierVehicleRelation dbCarrierVehicleRelation = tCarrierVehicleRelationMapper.selectByPrimaryKey(requestModel.getCarrierVehicleId());
        if (dbCarrierVehicleRelation == null || dbCarrierVehicleRelation.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }
        VehicleAssetManagementDetailResponseModel responseModel = tqVehicleBasicMapper.getVehicleBasicDetailByCarrierVehicleId(requestModel.getCarrierVehicleId());
        responseModel.setVehicleGpsRecordList(tqVehicleGpsRecordMapper.getVehicleGpsRecordByVehicleIds(ConverterUtils.toString(responseModel.getVehicleBasicId())));
        responseModel.setVehicleGradeEstimationList(tqVehicleGradeEstimationRecordMapper.getVehicleGradeEstimationRecordByVehicleIds(ConverterUtils.toString(responseModel.getVehicleBasicId())));
        responseModel.setVehicleDrivingLicensePageList(tqVehicleDrivingLicenseAnnualReviewMapper.getDrivingLicenseAnnualReviewByVehicleIds(ConverterUtils.toString(responseModel.getVehicleBasicId())));
        responseModel.setVehicleRoadTransportCertificateList(tqVehicleRoadTransportCertificateAnnualReviewMapper.getVehicleRoadTransportCertificateAnnualReviewByVehicleIds(ConverterUtils.toString(responseModel.getVehicleBasicId())));
        for (CertificationPicturesRecordModel tmp : responseModel.getOtherDocumentsRecord()) {
            tmp.setPageFileType(CertificationFileMappingEnum.getEnumByDbType(CertificationPicturesObjectTypeEnum.getEnum(tmp.getObjectType()), tmp.getFileType()).getPageFileType());
        }

        //车主信息
        Map<Long, FuzzySearchCompanyCarrierResponseModel> companyCarrierMap = tCompanyCarrierMapper.selectCompanyCarrierInfoByIds(Arrays.asList(dbCarrierVehicleRelation.getCompanyCarrierId()))
                .stream().collect(Collectors.toMap(FuzzySearchCompanyCarrierResponseModel::getCompanyId, Function.identity(), (o1, o2) -> o2));
        FuzzySearchCompanyCarrierResponseModel carrierResponseModel = companyCarrierMap.get(dbCarrierVehicleRelation.getCompanyCarrierId());
        if (carrierResponseModel != null) {
            responseModel.setIsOurCompany(carrierResponseModel.getIsOurCompany());
            responseModel.setCompanyCarrierType(carrierResponseModel.getCompanyType());
            responseModel.setCompanyCarrierName(carrierResponseModel.getCompanyName());
            responseModel.setCarrierContactName(carrierResponseModel.getContactName());
            responseModel.setCarrierContactPhone(carrierResponseModel.getContactPhone());
        }
        return responseModel;
    }

    /**
     * 删除车辆
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public void delVehicle(DeleteVehicleAssetManagementRequest requestModel) {
        TCarrierVehicleRelation dbCarrierVehicleRelation = tCarrierVehicleRelationMapper.selectByPrimaryKey(requestModel.getCarrierVehicleId());
        if (dbCarrierVehicleRelation == null || dbCarrierVehicleRelation.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            /*前台车主请求*/
            Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (!loginUserCompanyCarrierId.equals(dbCarrierVehicleRelation.getCompanyCarrierId())){
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
            }
        }

        //查询车主信息
        TCompanyCarrier dbCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(dbCarrierVehicleRelation.getCompanyCarrierId());
        if (dbCompanyCarrier == null || dbCompanyCarrier.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }
        //其他车主才有删除功能
        if (!IsOurCompanyEnum.OTHER_COMPANY.getKey().equals(dbCompanyCarrier.getLevel())) {
            throw new BizException(CarrierDataExceptionEnum.OTHER_COMPANY_DEL_PERMISSION);
        }

        //判断这个车辆是否存在未卸货的运单
        if (tcarrierOrderMapper.getSpecifiedStateCount(null, ConverterUtils.toString(dbCarrierVehicleRelation.getVehicleId()), dbCarrierVehicleRelation.getCompanyCarrierId()) > CommonConstant.INTEGER_ZERO) {
            throw new BizException(CarrierDataExceptionEnum.THE_VEHICLE_IS_CARRYING_GOODS);
        }

        String vehicleIds = dbCarrierVehicleRelation.getVehicleId().toString();

        //查询车辆是否有绑定其他车主
        List<TCarrierVehicleRelation> dbCarrierVehicleRelationList = tCarrierVehicleRelationMapper.getByVehicleIds(vehicleIds);
        //车辆除了当前车主外，没有绑定其他车主，则删除车辆信息
        if (dbCarrierVehicleRelationList.size() == CommonConstant.INTEGER_ONE){
            Date now = new Date();
            //删除车辆
            tqVehicleBasicMapper.deleteVehicleBasicInfo(vehicleIds, BaseContextHandler.getUserName(), now);
            //删除驾驶证相关信息
            tqVehicleDrivingLicenseMapper.deleteDrivingLicenseInfo(vehicleIds, BaseContextHandler.getUserName(), now);
            //删除行驶证相关信息
            tqVehicleRoadTransportCertificateMapper.deleteRoadTransportCertificateInfo(vehicleIds, BaseContextHandler.getUserName(), now);
        }
        //删除车主车辆关系
        TCarrierVehicleRelation carrierVehicleRelation = new TCarrierVehicleRelation();
        carrierVehicleRelation.setId(dbCarrierVehicleRelation.getId());
        carrierVehicleRelation.setValid(IfValidEnum.INVALID.getKey());
        commonBiz.setBaseEntityModify(carrierVehicleRelation, BaseContextHandler.getUserName());
        tCarrierVehicleRelationMapper.updateByPrimaryKeySelective(carrierVehicleRelation);

        //查询车辆司机关系
        List<TStaffVehicleRelation> dbStaffVehicleRelationList = tStaffVehicleRelationMapper.selectRelationsByVehicleIdDriverId(null, ConverterUtils.toString(dbCarrierVehicleRelation.getVehicleId()), dbCarrierVehicleRelation.getCompanyCarrierId());
        //存在车辆司机关系，则删除
        if (ListUtils.isNotEmpty(dbStaffVehicleRelationList)){
            TStaffVehicleRelation staffVehicleRelation;
            List<TStaffVehicleRelation> delRelationList = new ArrayList<>();
            for (TStaffVehicleRelation relation : dbStaffVehicleRelationList) {
                staffVehicleRelation = new TStaffVehicleRelation();
                staffVehicleRelation.setId(relation.getId());
                staffVehicleRelation.setValid(IfValidEnum.INVALID.getKey());
                commonBiz.setBaseEntityModify(staffVehicleRelation, BaseContextHandler.getUserName());
                delRelationList.add(staffVehicleRelation);
            }
            tStaffVehicleRelationMapper.batchUpdateSelective(delRelationList);
        }
    }


    /**
     * 车辆资产基础信息表 - 新增/修改
     *
     * @param requestModel
     */
    @Transactional
    @DistributedLock(prefix = CommonConstant.TMS_ADD_VEHICLE_LOCK,
            keys = "#requestModel.vehicleNo",
            waitTime = 3)
    public void saveVehicleInfo(AddOrModifyVehicleBasicInfoRequestModel requestModel) {

        //车辆基础信息
        TVehicleBasic vehicleBasic = new TVehicleBasic();
        vehicleBasic.setUsageProperty(requestModel.getUsageProperty());
        vehicleBasic.setIfInstallGps(Optional.ofNullable(requestModel.getIfInstallGps()).orElse(CommonConstant.INTEGER_ZERO));
        vehicleBasic.setIfAccessSinopec(Optional.ofNullable(requestModel.getIfAccessSinopec()).orElse(CommonConstant.INTEGER_ZERO));
        vehicleBasic.setVehicleProperty(Optional.ofNullable(requestModel.getVehicleProperty()).orElse(CommonConstant.INTEGER_ZERO));
        vehicleBasic.setVehicleOwner(requestModel.getVehicleOwner());
        vehicleBasic.setConnectTime(requestModel.getConnectTime());
        vehicleBasic.setConnectWay(Optional.ofNullable(requestModel.getConnectWay()).orElse(CommonConstant.INTEGER_ZERO));
        vehicleBasic.setAuthenticationStartTime(requestModel.getAuthenticationStartTime());
        vehicleBasic.setAuthenticationExpireTime(requestModel.getAuthenticationExpireTime());
        vehicleBasic.setRegistrationCertificationNumber(requestModel.getRegistrationCertificationNumber());
        vehicleBasic.setEmissionStandardType(requestModel.getEmissionStandardType());
        vehicleBasic.setLoadingCapacity(requestModel.getLoadingCapacity());

        //驾驶证信息
        TVehicleDrivingLicense vehicleDrivingLicense = new TVehicleDrivingLicense();
        vehicleDrivingLicense.setVehicleType(requestModel.getVehicleType());
        vehicleDrivingLicense.setAddress(requestModel.getAddress());
        vehicleDrivingLicense.setOwner(requestModel.getOwner());
        vehicleDrivingLicense.setBrand(requestModel.getBrand());
        vehicleDrivingLicense.setModel(requestModel.getModel());
        vehicleDrivingLicense.setVehicleIdentificationNumber(requestModel.getVehicleIdentificationNumber());
        vehicleDrivingLicense.setEngineNumber(requestModel.getEngineNumber());
        vehicleDrivingLicense.setCertificationDepartment(requestModel.getDrivingCertificationDepartmentOne());
        vehicleDrivingLicense.setRegistrationDate(requestModel.getRegistrationDate());
        vehicleDrivingLicense.setIssueDate(requestModel.getDrivingIssueDate());
        vehicleDrivingLicense.setFilingNumber(requestModel.getFilingNumber());
        vehicleDrivingLicense.setAuthorizedCarryingCapacity(Optional.ofNullable(requestModel.getAuthorizedCarryingCapacity()).orElse(CommonConstant.INTEGER_ZERO));
        vehicleDrivingLicense.setTotalWeight(Optional.ofNullable(requestModel.getTotalWeight()).orElse(BigDecimal.ZERO));
        vehicleDrivingLicense.setCurbWeight(requestModel.getCurbWeight());
        vehicleDrivingLicense.setTractionMassWeight(Optional.ofNullable(requestModel.getTractionMassWeight()).orElse(BigDecimal.ZERO));
        vehicleDrivingLicense.setApprovedLoadWeight(Optional.ofNullable(requestModel.getApprovedLoadWeight()).orElse(BigDecimal.ZERO));
        vehicleDrivingLicense.setLength(requestModel.getLength());
        vehicleDrivingLicense.setWidth(requestModel.getWidth());
        vehicleDrivingLicense.setHeight(requestModel.getHeight());
        vehicleDrivingLicense.setObsolescenceDate(requestModel.getObsolescenceDate());
        vehicleDrivingLicense.setAxleNumber(Optional.ofNullable(requestModel.getAxleNumber()).orElse(CommonConstant.INTEGER_ZERO));
        vehicleDrivingLicense.setDriveShaftNumber(Optional.ofNullable(requestModel.getDriveShaftNumber()).orElse(CommonConstant.INTEGER_ZERO));
        vehicleDrivingLicense.setPlateColor(requestModel.getPlateColor());
        vehicleDrivingLicense.setBodyColor(requestModel.getBodyColor());
        vehicleDrivingLicense.setTiresNumber(requestModel.getTiresNumber());

        //道路许可证信息
        TVehicleRoadTransportCertificate vehicleRoadTransportCertificate = new TVehicleRoadTransportCertificate();
        vehicleRoadTransportCertificate.setCertificationSign(requestModel.getCertificationSign());
        vehicleRoadTransportCertificate.setBusinessLicenseNumber(requestModel.getBusinessLicenseNumber());
        vehicleRoadTransportCertificate.setEconomicType(requestModel.getEconomicType());
        vehicleRoadTransportCertificate.setTransportTonnage(requestModel.getTransportTonnage());
        vehicleRoadTransportCertificate.setBusinessScope(requestModel.getBusinessScope());
        vehicleRoadTransportCertificate.setCertificationDepartment(requestModel.getRoadTransportCertificationDepartment());
        vehicleRoadTransportCertificate.setIssueDate(requestModel.getIssueDate());
        vehicleRoadTransportCertificate.setObtainDate(requestModel.getObtainDate());
        vehicleRoadTransportCertificate.setRemark(requestModel.getRemark());
        List<TCertificationPictures> addFileList = new ArrayList<>();

        //根据ID查询车辆类型
        TVehicleType vehicleTypeEntity = tQVehicleTypeMapper.selectByPrimaryKey(requestModel.getVehicleType());
        requestModel.setVehicleTypeLabel(vehicleTypeEntity == null ? "" : vehicleTypeEntity.getVehicleType());
        //内部车辆(自主,自营) 需要判断车辆类型是否存在
        if (vehicleTypeEntity == null
                && (CommonConstant.INTEGER_ONE.equals(requestModel.getVehicleProperty()) && CommonConstant.INTEGER_THREE.equals(requestModel.getVehicleProperty()))) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_TYPE_IS_EMPTY);
        }

        //requestModel 过滤图片为空的相对路径
        List<CertificationPicturesRequestModel> newList = new ArrayList<>();
        if (ListUtils.isNotEmpty(requestModel.getFileList())) {
            for (CertificationPicturesRequestModel tempRequestFile : requestModel.getFileList()) {
                if (StringUtils.isNotBlank(tempRequestFile.getRelativeFilepath())) {
                    newList.add(tempRequestFile);
                }
            }
            requestModel.setFileList(newList);
        }

        //查询我司id
        Long qiyaCompanyCarrierId = commonBiz.getQiyaCompanyCarrierId();
        if (requestModel.getCarrierVehicleId() == null || requestModel.getCarrierVehicleId() <= CommonConstant.LONG_ZERO) {
            /*新增车辆逻辑*/
            long vehicleId;
            TVehicleBasic vehicleInfo = tqVehicleBasicMapper.getInfoByVehicleNo(requestModel.getVehicleNo());
            if (vehicleInfo != null) {
                vehicleId = vehicleInfo.getId();
                //查询我司与车辆关联关系
                List<TCarrierVehicleRelation> tCarrierVehicleRelationList = tCarrierVehicleRelationMapper.getByVehicleIds(ConverterUtils.toString(vehicleInfo.getId()));

                //只有一条关联关系
                if (tCarrierVehicleRelationList.size() == CommonConstant.INTEGER_ONE) {
                    TCarrierVehicleRelation tCarrierVehicleRelation = tCarrierVehicleRelationList.get(CommonConstant.INTEGER_ZERO);
                    if (qiyaCompanyCarrierId.equals(tCarrierVehicleRelation.getCompanyCarrierId())) {
                        /*已经和我司关联*/
                        throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_HAS_EXIST);
                    } else {
                        /*增加自主自营的人员,判断司机是否关联其他车主*/
                        if (!VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(requestModel.getVehicleProperty())) {
                            throw new BizException(CarrierDataExceptionEnum.ONLY_OUR_COMPANY_EDIT_VEHICLE_PROPERTY);
                        }
                    }
                }

                //有多条关联关系
                if (!VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(requestModel.getVehicleProperty())) {
                    /*增加自主自营的人员,判断司机是否关联其他车主*/
                    List<Long> carrierIdList = tCarrierVehicleRelationList.stream().map(TCarrierVehicleRelation::getCompanyCarrierId).distinct().collect(Collectors.toList());
                    if (carrierIdList.size() > CommonConstant.INTEGER_ONE) {
                        throw new BizException(CarrierDataExceptionEnum.ONLY_OUR_COMPANY_EDIT_VEHICLE_PROPERTY);
                    }
                } else {
                    //判断和我司是否已经存在关系
                    for (TCarrierVehicleRelation carrierVehicleRelation : tCarrierVehicleRelationList) {
                        if (qiyaCompanyCarrierId.equals(carrierVehicleRelation.getCompanyCarrierId())) {
                            /*已经和我司关联*/
                            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_HAS_EXIST);
                        }
                    }
                }

                //查询车辆已存在证件信息
                VehicleBasicModel dbVehicleBasicModel = tqVehicleBasicMapper.getVehicleBasicModelById(vehicleId);
                if (dbVehicleBasicModel != null) {
                    //行驶证
                    VehicleDrivingLicenseModel vehicleDrivingLicenseModel = tqVehicleBasicMapper.getVehicleDrivingLicenseModelById(vehicleId);
                    //道路许可证信息
                    VehicleRoadTransportCertificateModel vehicleRoadTransportCertificateModel = tqVehicleBasicMapper.getVehicleRoadTransportCertificateModelById(vehicleId);
                    dbVehicleBasicModel.setBasicVehicleNo(vehicleDrivingLicenseModel.getVehicleNo());
                    dbVehicleBasicModel.setVehicleDrivingLicenseModel(vehicleDrivingLicenseModel);
                    dbVehicleBasicModel.setVehicleRoadTransportCertificateModel(vehicleRoadTransportCertificateModel);
                    //驾驶证信息
                    VehicleDrivingLicenseModel dbVehicleDrivingLicenseModel = dbVehicleBasicModel.getVehicleDrivingLicenseModel();
                    //道路许可证信息
                    VehicleRoadTransportCertificateModel dbVehicleRoadTransportCertificateModel = dbVehicleBasicModel.getVehicleRoadTransportCertificateModel();
                    //行驶证年审记录
                    List<VehicleDrivingLicenseAnnualReviewModel> dbVehicleDrivingLicenseAnnualReviewList = dbVehicleDrivingLicenseModel.getVehicleDrivingLicenseAnnualReviewList();
                    //营运证年审
                    List<VehicleRoadTransportCertificateAnnualReviewModel> dbVehicleRoadTransportCertificateAnnualReviewList = dbVehicleRoadTransportCertificateModel.getVehicleRoadTransportCertificateAnnualReviewList();
                    //GPS记录
                    List<VehicleGpsRecordModel> dbVehicleGpsRecordModelList = dbVehicleBasicModel.getVehicleGpsRecordModelList();
                    if (dbVehicleGpsRecordModelList == null) {
                        dbVehicleGpsRecordModelList = new ArrayList<>();
                    }
                    //等级评定
                    List<VehicleGradeEstimationRecordModel> dbVehicleGradeEstimationRecordModelList = dbVehicleBasicModel.getVehicleGradeEstimationRecordModelList();
                    if (dbVehicleGradeEstimationRecordModelList == null) {
                        dbVehicleGradeEstimationRecordModelList = new ArrayList<>();
                    }
                    //行驶证
                    TVehicleDrivingLicense drivingLicense = tqVehicleDrivingLicenseMapper.getByVehicleId(vehicleId);
                    //道路许可证
                    TVehicleRoadTransportCertificate roadTransportCertificate = tqVehicleRoadTransportCertificateMapper.getByVehicleId(dbVehicleBasicModel.getVehicleBasicId());

                    //更新已存在车辆信息
                    vehicleBasic.setId(vehicleInfo.getId());
                    vehicleBasic.setVehicleOwner("");
                    commonBiz.setBaseEntityModify(vehicleBasic, BaseContextHandler.getUserName());
                    tqVehicleBasicMapper.updateByPrimaryKeySelectiveExt(vehicleBasic);

                    vehicleDrivingLicense.setId(drivingLicense.getId());
                    vehicleDrivingLicense.setVehicleId(vehicleBasic.getId());
                    commonBiz.setBaseEntityModify(vehicleDrivingLicense, BaseContextHandler.getUserName());
                    tqVehicleDrivingLicenseMapper.updateByPrimaryKeySelectiveExt(vehicleDrivingLicense);

                    vehicleRoadTransportCertificate.setId(roadTransportCertificate.getId());
                    vehicleRoadTransportCertificate.setVehicleId(vehicleBasic.getId());
                    commonBiz.setBaseEntityModify(vehicleRoadTransportCertificate, BaseContextHandler.getUserName());
                    tqVehicleRoadTransportCertificateMapper.updateByPrimaryKeySelectiveExt(vehicleRoadTransportCertificate);

                    List<CertificationPicturesRequestModel> pageAllFileList = new ArrayList<>();

                    //基础图片信息
                    addFileList.addAll(this.targetAddFileList(vehicleBasic.getId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, targetFileList(requestModel.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC)));
                    addFileList.addAll(this.targetAddFileList(vehicleDrivingLicense.getId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, targetFileList(requestModel.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_DRIVING_LICENSE)));
                    addFileList.addAll(this.targetAddFileList(vehicleRoadTransportCertificate.getId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, targetFileList(requestModel.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE)));

                    if (ListUtils.isNotEmpty(requestModel.getFileList())) {
                        pageAllFileList.addAll(requestModel.getFileList());
                    }

                    //GPS信息列表信息
                    List<TVehicleGpsRecord> upVehicleGpsRecordList = new ArrayList<>();
                    processVehicleGpsRecord(requestModel, vehicleBasic, addFileList, dbVehicleGpsRecordModelList, pageAllFileList, upVehicleGpsRecordList);
                    if (ListUtils.isNotEmpty(upVehicleGpsRecordList)) {
                        tqVehicleGpsRecordMapper.batchUpdate(upVehicleGpsRecordList);
                    }

                    //等级评定列表
                    List<TVehicleGradeEstimationRecord> upGradeEstimationRecordList = new ArrayList<>();
                    processVehicleGradeEstimationRecord(requestModel, vehicleBasic, addFileList, dbVehicleGradeEstimationRecordModelList, pageAllFileList, upGradeEstimationRecordList);
                    if (ListUtils.isNotEmpty(upGradeEstimationRecordList)) {
                        tqVehicleGradeEstimationRecordMapper.batchUpdate(upGradeEstimationRecordList);
                    }

                    //行驶证年审记录列表
                    List<TVehicleDrivingLicenseAnnualReview> upDrivingLicenseAnnualReviewList = new ArrayList<>();
                    processVehicleDrivingLicenseAnnualReview(requestModel, addFileList, drivingLicense, dbVehicleDrivingLicenseAnnualReviewList, pageAllFileList, upDrivingLicenseAnnualReviewList);
                    if (ListUtils.isNotEmpty(upDrivingLicenseAnnualReviewList)) {
                        tqVehicleDrivingLicenseAnnualReviewMapper.batchUpdate(upDrivingLicenseAnnualReviewList);
                    }

                    //车辆营运证年审列表
                    List<TVehicleRoadTransportCertificateAnnualReview> upVehicleRoadAnnualReviewList = new ArrayList<>();
                    processVehicleRoadTransportCertificateAnnualReview(requestModel, addFileList, roadTransportCertificate, dbVehicleRoadTransportCertificateAnnualReviewList, pageAllFileList, upVehicleRoadAnnualReviewList);
                    if (ListUtils.isNotEmpty(upVehicleRoadAnnualReviewList)) {
                        tqVehicleRoadTransportCertificateAnnualReviewMapper.batchUpdate(upVehicleRoadAnnualReviewList);
                    }

                    //查询待修改所有图片信息
                    List<SearchCertificationPicturesModel> unionSearchList = new ArrayList<>();
                    unionSearchList.addAll(this.getFileSearchCondition(vehicleInfo.getId(), CertificationFileMappingEnum.getFileTypeEnumList(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC)));
                    unionSearchList.addAll(this.getFileSearchCondition(drivingLicense.getId(), CertificationFileMappingEnum.getFileTypeEnumList(CertificationPicturesObjectTypeEnum.T_VEHICLE_DRIVING_LICENSE)));
                    unionSearchList.addAll(this.getFileSearchCondition(roadTransportCertificate.getId(), CertificationFileMappingEnum.getFileTypeEnumList(CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE)));

                    //车辆GPS安装记录凭证
                    processCertificationPic(upVehicleGpsRecordList, upGradeEstimationRecordList, upDrivingLicenseAnnualReviewList, upVehicleRoadAnnualReviewList, unionSearchList);
                    //查询凭证图片
                    List<TCertificationPictures> dbFileList = tqCertificationPicturesMapper.getUnionFiles(unionSearchList);
                    //凭证图片修改列表
                    List<TCertificationPictures> upFileList = new ArrayList<>();
                    //检查凭证图片是新增还是修改
                    checkCertificationPic(pageAllFileList, dbFileList, upFileList);

                    if (ListUtils.isNotEmpty(upFileList)) {
                        tqCertificationPicturesMapper.batchUpdate(upFileList);
                    }

                    if (ListUtils.isNotEmpty(addFileList)) {
                        tqCertificationPicturesMapper.batchInsert(addFileList);
                    }
                }
            } else {
                commonBiz.setBaseEntityAdd(vehicleBasic, BaseContextHandler.getUserName());
                tqVehicleBasicMapper.insertSelective(vehicleBasic);
                vehicleId = vehicleBasic.getId();

                List<CertificationPicturesRequestModel> vehicleBasicFileList = this.targetFileList(requestModel.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC);
                addFileList.addAll(this.targetAddFileList(vehicleBasic.getId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, vehicleBasicFileList));

                //GPS信息列表
                TVehicleGpsRecord vehicleGpsRecord;
                if (ListUtils.isNotEmpty(requestModel.getVehicleGpsRecordList())) {
                    for (AddOrModifyVehicleGpsRecordRequestModel tempGpsRecordModel : requestModel.getVehicleGpsRecordList()) {
                        vehicleGpsRecord = new TVehicleGpsRecord();
                        vehicleGpsRecord.setVehicleId(vehicleBasic.getId());
                        vehicleGpsRecord.setGpsServiceProvider(tempGpsRecordModel.getGpsServiceProvider());
                        vehicleGpsRecord.setSimNumber(tempGpsRecordModel.getSimNumber());
                        vehicleGpsRecord.setTerminalType(tempGpsRecordModel.getTerminalType());
                        vehicleGpsRecord.setInstallTime(tempGpsRecordModel.getInstallTime());
                        vehicleGpsRecord.setRemark(tempGpsRecordModel.getRemark());
                        commonBiz.setBaseEntityAdd(vehicleGpsRecord, BaseContextHandler.getUserName());
                        tqVehicleGpsRecordMapper.insertSelective(vehicleGpsRecord);
                        List<CertificationPicturesRequestModel> vehicleGpsRecordFileList = this.targetFileList(tempGpsRecordModel.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_GPS_RECORD);
                        addFileList.addAll(this.targetAddFileList(vehicleGpsRecord.getId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, vehicleGpsRecordFileList));
                    }
                }

                //等级评定列表
                TVehicleGradeEstimationRecord vehicleGradeEstimationRecord;
                if (ListUtils.isNotEmpty(requestModel.getVehicleGradeEstimationRecordList())) {
                    for (AddOrModifyVehicleGradeEstimationRecordRequestModel tempVehicleGradeEstimationRecord : requestModel.getVehicleGradeEstimationRecordList()) {
                        vehicleGradeEstimationRecord = new TVehicleGradeEstimationRecord();
                        vehicleGradeEstimationRecord.setVehicleId(vehicleBasic.getId());
                        vehicleGradeEstimationRecord.setEstimationDate(tempVehicleGradeEstimationRecord.getEstimationDate());
                        vehicleGradeEstimationRecord.setGrade(tempVehicleGradeEstimationRecord.getGrade());
                        vehicleGradeEstimationRecord.setRemark(tempVehicleGradeEstimationRecord.getRemark());
                        commonBiz.setBaseEntityAdd(vehicleGradeEstimationRecord, BaseContextHandler.getUserName());
                        tqVehicleGradeEstimationRecordMapper.insertSelective(vehicleGradeEstimationRecord);
                        List<CertificationPicturesRequestModel> vehicleGradeEstimationRecordFileList = this.targetFileList(tempVehicleGradeEstimationRecord.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_GRADE_ESTIMATION_RECORD);
                        addFileList.addAll(this.targetAddFileList(vehicleGradeEstimationRecord.getId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, vehicleGradeEstimationRecordFileList));
                    }
                }
                vehicleDrivingLicense.setVehicleNo(requestModel.getVehicleNo());
                vehicleDrivingLicense.setVehicleId(vehicleBasic.getId());
                commonBiz.setBaseEntityAdd(vehicleDrivingLicense, BaseContextHandler.getUserName());
                tqVehicleDrivingLicenseMapper.insertSelective(vehicleDrivingLicense);

                List<CertificationPicturesRequestModel> vehicleDrivingLicenseFileList = this.targetFileList(requestModel.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_DRIVING_LICENSE);
                List<TCertificationPictures> vehicleDrivingLicenseFileAddList = this.targetAddFileList(vehicleDrivingLicense.getId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, vehicleDrivingLicenseFileList);
                addFileList.addAll(vehicleDrivingLicenseFileAddList);

                //行驶证年审记录列表
                if (ListUtils.isNotEmpty(requestModel.getDrivingLicenseAnnualReviewList())) {
                    TVehicleDrivingLicenseAnnualReview vehicleDrivingLicenseAnnualReview;
                    for (AddOrModifyDrivingLicenseAnnualReviewRequestModel tempDrivingLicenseAnnualReview : requestModel.getDrivingLicenseAnnualReviewList()) {
                        vehicleDrivingLicenseAnnualReview = new TVehicleDrivingLicenseAnnualReview();
                        vehicleDrivingLicenseAnnualReview.setDrivingLicenseId(vehicleDrivingLicense.getId());
                        vehicleDrivingLicenseAnnualReview.setCheckValidDate(commonBiz.getLastDayOfMonth(tempDrivingLicenseAnnualReview.getCheckValidDate()));
                        vehicleDrivingLicenseAnnualReview.setRemark(tempDrivingLicenseAnnualReview.getRemark());
                        commonBiz.setBaseEntityAdd(vehicleDrivingLicenseAnnualReview, BaseContextHandler.getUserName());
                        tqVehicleDrivingLicenseAnnualReviewMapper.insertSelective(vehicleDrivingLicenseAnnualReview);
                        List<CertificationPicturesRequestModel> vehicleDrivingLicenseAnnualReviewFileList = this.targetFileList(tempDrivingLicenseAnnualReview.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_DRIVING_LICENSE_ANNUAL_REVIEW);
                        addFileList.addAll(this.targetAddFileList(vehicleDrivingLicenseAnnualReview.getId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, vehicleDrivingLicenseAnnualReviewFileList));
                    }
                }

                vehicleRoadTransportCertificate.setVehicleId(vehicleBasic.getId());
                vehicleRoadTransportCertificate.setValid(CommonConstant.INTEGER_ONE);
                commonBiz.setBaseEntityAdd(vehicleRoadTransportCertificate, BaseContextHandler.getUserName());
                tqVehicleRoadTransportCertificateMapper.insertSelective(vehicleRoadTransportCertificate);

                List<CertificationPicturesRequestModel> vehicleRoadTransportCertificateFileList = this.targetFileList(requestModel.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE);
                addFileList.addAll(this.targetAddFileList(vehicleRoadTransportCertificate.getId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, vehicleRoadTransportCertificateFileList));

                //车辆营运证年审列表
                if (ListUtils.isNotEmpty(requestModel.getVehicleRoadTransportCertificateAnnualReviewList())) {
                    TVehicleRoadTransportCertificateAnnualReview vehicleRoadTransportCertificateAnnualReview;
                    for (AddOrModifyVehicleRoadTransportCertificateAnnualReviewRequestModel tempVehicleRoadTransportCertificateAnnualReview : requestModel.getVehicleRoadTransportCertificateAnnualReviewList()) {
                        vehicleRoadTransportCertificateAnnualReview = new TVehicleRoadTransportCertificateAnnualReview();
                        vehicleRoadTransportCertificateAnnualReview.setRoadTransportCetificationId(vehicleRoadTransportCertificate.getId());
                        vehicleRoadTransportCertificateAnnualReview.setCheckValidDate(commonBiz.getLastDayOfMonth(tempVehicleRoadTransportCertificateAnnualReview.getCheckValidDate()));
                        vehicleRoadTransportCertificateAnnualReview.setRemark(tempVehicleRoadTransportCertificateAnnualReview.getRemark());
                        vehicleRoadTransportCertificateAnnualReview.setRoadTransportCetificationId(vehicleRoadTransportCertificate.getId());
                        commonBiz.setBaseEntityAdd(vehicleRoadTransportCertificateAnnualReview, BaseContextHandler.getUserName());
                        tqVehicleRoadTransportCertificateAnnualReviewMapper.insertSelective(vehicleRoadTransportCertificateAnnualReview);
                        List<CertificationPicturesRequestModel> vehicleRoadTransportCertificateAnnualReviewFileList = this.targetFileList(tempVehicleRoadTransportCertificateAnnualReview.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE_ANNUAL_REVIEW);
                        addFileList.addAll(this.targetAddFileList(vehicleRoadTransportCertificateAnnualReview.getId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, vehicleRoadTransportCertificateAnnualReviewFileList));
                    }
                }

                //新增修改列表
                if (ListUtils.isNotEmpty(addFileList)) {
                    tqCertificationPicturesMapper.batchInsert(addFileList);
                }
            }

            //新增我司与车辆关系
            TCarrierVehicleRelation tCarrierVehicleRelation = new TCarrierVehicleRelation();
            tCarrierVehicleRelation.setVehicleId(vehicleId);
            tCarrierVehicleRelation.setCompanyCarrierId(qiyaCompanyCarrierId);
            commonBiz.setBaseEntityAdd(tCarrierVehicleRelation, BaseContextHandler.getUserName());
            tCarrierVehicleRelationMapper.insertSelective(tCarrierVehicleRelation);
        } else {
            /*修改车辆逻辑*/
            //查询车辆关联信息
            TCarrierVehicleRelation tCarrierVehicleRelation = tCarrierVehicleRelationMapper.selectByPrimaryKey(requestModel.getCarrierVehicleId());
            if (tCarrierVehicleRelation == null || IfValidEnum.INVALID.getKey().equals(tCarrierVehicleRelation.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.BASIC_INFORMATION_OF_VEHICLE_ASSETS_NOT_EXIST);
            }
            //查询车辆
            VehicleBasicModel dbVehicleBasicModel = tqVehicleBasicMapper.getVehicleBasicModelById(tCarrierVehicleRelation.getVehicleId());
            if (dbVehicleBasicModel == null) {
                throw new BizException(CarrierDataExceptionEnum.BASIC_INFORMATION_OF_VEHICLE_ASSETS_NOT_EXIST);
            }

            TVehicleBasic vehicleInfo = tqVehicleBasicMapper.getInfoByVehicleNo(requestModel.getVehicleNo());
            if (vehicleInfo == null) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_CANNOT_CHANGE);
            }
            if (!tCarrierVehicleRelation.getVehicleId().equals(vehicleInfo.getId())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_HAS_EXIST);
            }

            /*修改车辆逻辑*/
            this.checkVehicleInfo(vehicleInfo.getId(), requestModel.getVehicleProperty(), requestModel.getVehicleType());

            //查询车主信息
            FuzzySearchCompanyCarrierResponseModel companyCarrierInfo = tCompanyCarrierMapper.getCompanyCarrierInfoById(tCarrierVehicleRelation.getCompanyCarrierId());

            //修改我司数据还是其他车主数据
            if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(companyCarrierInfo.getIsOurCompany())) {
                /*我司*/

                //外部修改为自主/自营
                if (StaffPropertyEnum.EXTERNAL_STAFF.getKey().equals(vehicleInfo.getVehicleProperty())
                        && !StaffPropertyEnum.EXTERNAL_STAFF.getKey().equals(requestModel.getVehicleProperty())) {
                    //查询当前司机是否关联其他车主
                    List<TCarrierVehicleRelation> tCarrierVehicleRelationList = tCarrierVehicleRelationMapper.getByVehicleIds(ConverterUtils.toString(tCarrierVehicleRelation.getVehicleId()));
                    if (tCarrierVehicleRelationList.size() > CommonConstant.INTEGER_ONE) {
                        throw new BizException(CarrierDataExceptionEnum.ONLY_OUR_COMPANY_EDIT_VEHICLE_PROPERTY);
                    }
                }
            } else {
                /*其他车主*/
                if (!vehicleInfo.getVehicleProperty().equals(requestModel.getVehicleProperty())) {
                    throw new BizException(CarrierDataExceptionEnum.ONLY_OUR_COMPANY_EDIT_VEHICLE_PROPERTY);
                }
            }

            //行驶证
            VehicleDrivingLicenseModel vehicleDrivingLicenseModel = tqVehicleBasicMapper.getVehicleDrivingLicenseModelById(tCarrierVehicleRelation.getVehicleId());
            //道路许可证信息
            VehicleRoadTransportCertificateModel vehicleRoadTransportCertificateModel = tqVehicleBasicMapper.getVehicleRoadTransportCertificateModelById(tCarrierVehicleRelation.getVehicleId());

            dbVehicleBasicModel.setBasicVehicleNo(vehicleDrivingLicenseModel.getVehicleNo());
            dbVehicleBasicModel.setVehicleDrivingLicenseModel(vehicleDrivingLicenseModel);
            dbVehicleBasicModel.setVehicleRoadTransportCertificateModel(vehicleRoadTransportCertificateModel);

            //行驶证
            TVehicleDrivingLicense drivingLicense = tqVehicleDrivingLicenseMapper.getByVehicleId(tCarrierVehicleRelation.getVehicleId());
            if (drivingLicense == null) {
                throw new BizException(CarrierDataExceptionEnum.DRIVING_LICENSE_NOT_EXIST);
            }

            TVehicleRoadTransportCertificate roadTransportCertificate = tqVehicleRoadTransportCertificateMapper.getByVehicleId(dbVehicleBasicModel.getVehicleBasicId());

            if (roadTransportCertificate == null) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_ROAD_TRANSPORT_CERTIFICATE_NOT_EXIST);
            }
            if (dbVehicleBasicModel.getVehicleDrivingLicenseModel() == null) {
                throw new BizException(CarrierDataExceptionEnum.DRIVING_LICENSE_NOT_EXIST);
            }
            if (dbVehicleBasicModel.getVehicleRoadTransportCertificateModel() == null) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_ROAD_TRANSPORT_CERTIFICATE_NOT_EXIST);
            }

            VehicleDrivingLicenseModel dbVehicleDrivingLicenseModel = dbVehicleBasicModel.getVehicleDrivingLicenseModel();
            VehicleRoadTransportCertificateModel dbVehicleRoadTransportCertificateModel = dbVehicleBasicModel.getVehicleRoadTransportCertificateModel();
            List<VehicleDrivingLicenseAnnualReviewModel> dbVehicleDrivingLicenseAnnualReviewList = dbVehicleDrivingLicenseModel.getVehicleDrivingLicenseAnnualReviewList();
            List<VehicleRoadTransportCertificateAnnualReviewModel> dbVehicleRoadTransportCertificateAnnualReviewList = dbVehicleRoadTransportCertificateModel.getVehicleRoadTransportCertificateAnnualReviewList();

            List<VehicleGpsRecordModel> dbVehicleGpsRecordModelList = dbVehicleBasicModel.getVehicleGpsRecordModelList();
            if (dbVehicleGpsRecordModelList == null) {
                dbVehicleGpsRecordModelList = new ArrayList<>();
            }
            List<VehicleGradeEstimationRecordModel> dbVehicleGradeEstimationRecordModelList = dbVehicleBasicModel.getVehicleGradeEstimationRecordModelList();
            if (dbVehicleGradeEstimationRecordModelList == null) {
                dbVehicleGradeEstimationRecordModelList = new ArrayList<>();
            }

            vehicleBasic.setId(vehicleInfo.getId());
            commonBiz.setBaseEntityModify(vehicleBasic, BaseContextHandler.getUserName());
            tqVehicleBasicMapper.updateByPrimaryKeySelectiveExtTwo(vehicleBasic);

            vehicleDrivingLicense.setId(drivingLicense.getId());
            vehicleDrivingLicense.setVehicleId(vehicleBasic.getId());
            commonBiz.setBaseEntityModify(vehicleDrivingLicense, BaseContextHandler.getUserName());
            tqVehicleDrivingLicenseMapper.updateByPrimaryKeySelectiveExt(vehicleDrivingLicense);

            vehicleRoadTransportCertificate.setId(roadTransportCertificate.getId());
            vehicleRoadTransportCertificate.setVehicleId(vehicleBasic.getId());
            commonBiz.setBaseEntityModify(vehicleRoadTransportCertificate, BaseContextHandler.getUserName());
            tqVehicleRoadTransportCertificateMapper.updateByPrimaryKeySelectiveExt(vehicleRoadTransportCertificate);

            List<CertificationPicturesRequestModel> pageAllFileList = new ArrayList<>();

            //基础图片信息
            addFileList.addAll(this.targetAddFileList(vehicleBasic.getId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, targetFileList(requestModel.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC)));
            addFileList.addAll(this.targetAddFileList(vehicleDrivingLicense.getId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, targetFileList(requestModel.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_DRIVING_LICENSE)));
            addFileList.addAll(this.targetAddFileList(vehicleRoadTransportCertificate.getId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, targetFileList(requestModel.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE)));

            if (ListUtils.isNotEmpty(requestModel.getFileList())) {
                pageAllFileList.addAll(requestModel.getFileList());
            }

            //GPS信息列表信息
            List<TVehicleGpsRecord> upVehicleGpsRecordList = new ArrayList<>();
            processVehicleGpsRecord(requestModel, vehicleBasic, addFileList, dbVehicleGpsRecordModelList, pageAllFileList, upVehicleGpsRecordList);
            if (ListUtils.isNotEmpty(upVehicleGpsRecordList)) {
                tqVehicleGpsRecordMapper.batchUpdate(upVehicleGpsRecordList);
            }

            //等级评定列表
            List<TVehicleGradeEstimationRecord> upGradeEstimationRecordList = new ArrayList<>();
            processVehicleGradeEstimationRecord(requestModel, vehicleBasic, addFileList, dbVehicleGradeEstimationRecordModelList, pageAllFileList, upGradeEstimationRecordList);
            if (ListUtils.isNotEmpty(upGradeEstimationRecordList)) {
                tqVehicleGradeEstimationRecordMapper.batchUpdate(upGradeEstimationRecordList);
            }

            //行驶证年审记录列表
            List<TVehicleDrivingLicenseAnnualReview> upDrivingLicenseAnnualReviewList = new ArrayList<>();
            processVehicleDrivingLicenseAnnualReview(requestModel, addFileList, drivingLicense, dbVehicleDrivingLicenseAnnualReviewList, pageAllFileList, upDrivingLicenseAnnualReviewList);
            if (ListUtils.isNotEmpty(upDrivingLicenseAnnualReviewList)) {
                tqVehicleDrivingLicenseAnnualReviewMapper.batchUpdate(upDrivingLicenseAnnualReviewList);
            }

            //车辆营运证年审列表
            List<TVehicleRoadTransportCertificateAnnualReview> upVehicleRoadAnnualReviewList = new ArrayList<>();
            processVehicleRoadTransportCertificateAnnualReview(requestModel, addFileList, roadTransportCertificate, dbVehicleRoadTransportCertificateAnnualReviewList, pageAllFileList, upVehicleRoadAnnualReviewList);
            if (ListUtils.isNotEmpty(upVehicleRoadAnnualReviewList)) {
                tqVehicleRoadTransportCertificateAnnualReviewMapper.batchUpdate(upVehicleRoadAnnualReviewList);
            }

            //查询待修改所有图片信息
            List<SearchCertificationPicturesModel> unionSearchList = new ArrayList<>();
            unionSearchList.addAll(this.getFileSearchCondition(vehicleInfo.getId(), CertificationFileMappingEnum.getFileTypeEnumList(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC)));
            unionSearchList.addAll(this.getFileSearchCondition(drivingLicense.getId(), CertificationFileMappingEnum.getFileTypeEnumList(CertificationPicturesObjectTypeEnum.T_VEHICLE_DRIVING_LICENSE)));
            unionSearchList.addAll(this.getFileSearchCondition(roadTransportCertificate.getId(), CertificationFileMappingEnum.getFileTypeEnumList(CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE)));

            //车辆GPS安装记录凭证
            processCertificationPic(upVehicleGpsRecordList, upGradeEstimationRecordList, upDrivingLicenseAnnualReviewList, upVehicleRoadAnnualReviewList, unionSearchList);
            //查询凭证图片
            List<TCertificationPictures> dbFileList = tqCertificationPicturesMapper.getUnionFiles(unionSearchList);
            //凭证图片修改列表
            List<TCertificationPictures> upFileList = new ArrayList<>();
            //检查凭证图片是新增还是修改
            checkCertificationPic(pageAllFileList, dbFileList, upFileList);

            if (ListUtils.isNotEmpty(upFileList)) {
                tqCertificationPicturesMapper.batchUpdate(upFileList);
            }

            if (ListUtils.isNotEmpty(addFileList)) {
                tqCertificationPicturesMapper.batchInsert(addFileList);
            }
        }
    }

    //检查凭证图片是新增还是修改
    private void checkCertificationPic(List<CertificationPicturesRequestModel> pageAllFileList, List<TCertificationPictures> dbFileList, List<TCertificationPictures> upFileList) {
        Map<Long, String> pageFileMap = new HashMap<>();
        if (ListUtils.isNotEmpty(pageAllFileList)) {
            for (CertificationPicturesRequestModel tempFile : pageAllFileList) {
                if (tempFile.getFileId() == null || tempFile.getFileId() <= CommonConstant.LONG_ZERO) continue;
                pageFileMap.put(tempFile.getFileId(), tempFile.getRelativeFilepath());
            }
        }
        TCertificationPictures upCertificationPictures;
        for (TCertificationPictures tempFile : dbFileList) {
            upCertificationPictures = new TCertificationPictures();
            //删除
            if (StringUtils.isBlank(pageFileMap.get(tempFile.getId()))) {
                upCertificationPictures.setId(tempFile.getId());
                upCertificationPictures.setValid(CommonConstant.INTEGER_ZERO);
                commonBiz.setBaseEntityModify(upCertificationPictures, BaseContextHandler.getUserName());
                upFileList.add(upCertificationPictures);
            }
            //更新
            if (StringUtils.isNotBlank(pageFileMap.get(tempFile.getId())) && !pageFileMap.get(tempFile.getId()).equals(tempFile.getFilePath())) {
                upCertificationPictures.setId(tempFile.getId());
                upCertificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.VEHICLE_BASIC_INFO.getKey(), "", pageFileMap.get(tempFile.getId()), null));
                upCertificationPictures.setSuffix(upCertificationPictures.getFilePath().substring(upCertificationPictures.getFilePath().lastIndexOf('.')));
                commonBiz.setBaseEntityModify(upCertificationPictures, BaseContextHandler.getUserName());
                upFileList.add(upCertificationPictures);
            }
        }
    }

    //处理车辆凭证
    private void processCertificationPic(List<TVehicleGpsRecord> upVehicleGpsRecordList, List<TVehicleGradeEstimationRecord> upGradeEstimationRecordList, List<TVehicleDrivingLicenseAnnualReview> upDrivingLicenseAnnualReviewList, List<TVehicleRoadTransportCertificateAnnualReview> upVehicleRoadAnnualReviewList, List<SearchCertificationPicturesModel> unionSearchList) {
        if (ListUtils.isNotEmpty(upVehicleGpsRecordList)) {
            for (TVehicleGpsRecord tempGpsRecord : upVehicleGpsRecordList) {
                unionSearchList.add(this.getSingleFileCondition(tempGpsRecord.getId(), CertificationFileMappingEnum.VEHICLE_GPS_RECORD));
            }
        }
        //等级凭证
        if (ListUtils.isNotEmpty(upGradeEstimationRecordList)) {
            for (TVehicleGradeEstimationRecord tempGradeEstimationRecord : upGradeEstimationRecordList) {
                unionSearchList.add(this.getSingleFileCondition(tempGradeEstimationRecord.getId(), CertificationFileMappingEnum.VEHICLE_GRADE_ESTIMATION_RECORD));
            }
        }
        //车辆行驶证检查记录凭证
        if (ListUtils.isNotEmpty(upDrivingLicenseAnnualReviewList)) {
            for (TVehicleDrivingLicenseAnnualReview tempDrivingLicenseAnnualReview : upDrivingLicenseAnnualReviewList) {
                unionSearchList.add(this.getSingleFileCondition(tempDrivingLicenseAnnualReview.getId(), CertificationFileMappingEnum.DRIVING_LICENSE_ANNUAL_REVIEW));
            }
        }
        //车辆营运证年审列表
        if (ListUtils.isNotEmpty(upVehicleRoadAnnualReviewList)) {
            for (TVehicleRoadTransportCertificateAnnualReview tempRoadAnnualReview : upVehicleRoadAnnualReviewList) {
                unionSearchList.add(this.getSingleFileCondition(tempRoadAnnualReview.getId(), CertificationFileMappingEnum.VEHICLE_ROAD_TRANSPORT_CERTIFICATE_ANNUAL_REVIEW));
            }

        }
    }

    //处理车辆营运证年审
    @Transactional
    public void processVehicleRoadTransportCertificateAnnualReview(AddOrModifyVehicleBasicInfoRequestModel requestModel, List<TCertificationPictures> addFileList, TVehicleRoadTransportCertificate roadTransportCertificate, List<VehicleRoadTransportCertificateAnnualReviewModel> dbVehicleRoadTransportCertificateAnnualReviewList, List<CertificationPicturesRequestModel> pageAllFileList, List<TVehicleRoadTransportCertificateAnnualReview> upVehicleRoadAnnualReviewList) {
        Map<Long, AddOrModifyVehicleRoadTransportCertificateAnnualReviewRequestModel> vehicleRoadAnnualReviewMap = new HashMap<>();
        TVehicleRoadTransportCertificateAnnualReview vehicleRoadAnnualReview;
        for (AddOrModifyVehicleRoadTransportCertificateAnnualReviewRequestModel tempVehicleRoadAnnualReviewModel : requestModel.getVehicleRoadTransportCertificateAnnualReviewList()) {
            if (tempVehicleRoadAnnualReviewModel.getVehicleRoadTransportCertificateAnnualReviewId() == null || tempVehicleRoadAnnualReviewModel.getVehicleRoadTransportCertificateAnnualReviewId() <= CommonConstant.LONG_ZERO) {
                vehicleRoadAnnualReview = new TVehicleRoadTransportCertificateAnnualReview();
                vehicleRoadAnnualReview.setCheckValidDate(commonBiz.getLastDayOfMonth(tempVehicleRoadAnnualReviewModel.getCheckValidDate()));
                vehicleRoadAnnualReview.setRoadTransportCetificationId(roadTransportCertificate.getId());
                vehicleRoadAnnualReview.setRemark(tempVehicleRoadAnnualReviewModel.getRemark());
                commonBiz.setBaseEntityAdd(vehicleRoadAnnualReview, BaseContextHandler.getUserName());
                tqVehicleRoadTransportCertificateAnnualReviewMapper.insertSelective(vehicleRoadAnnualReview);
                List<CertificationPicturesRequestModel> vehicleRoadAnnualReviewFileList = this.targetFileList(tempVehicleRoadAnnualReviewModel.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE_ANNUAL_REVIEW);
                addFileList.addAll(this.targetAddFileList(vehicleRoadAnnualReview.getId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, vehicleRoadAnnualReviewFileList));
                continue;
            }
            vehicleRoadAnnualReviewMap.put(tempVehicleRoadAnnualReviewModel.getVehicleRoadTransportCertificateAnnualReviewId(), tempVehicleRoadAnnualReviewModel);
            if (ListUtils.isNotEmpty(tempVehicleRoadAnnualReviewModel.getFileList())) {
                List<CertificationPicturesRequestModel> targetFileList = this.targetFileList(tempVehicleRoadAnnualReviewModel.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_ROAD_TRANSPORT_CERTIFICATE_ANNUAL_REVIEW);
                addFileList.addAll(this.targetAddFileList(tempVehicleRoadAnnualReviewModel.getVehicleRoadTransportCertificateAnnualReviewId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, targetFileList));
                pageAllFileList.addAll(tempVehicleRoadAnnualReviewModel.getFileList());
            }
        }

        for (VehicleRoadTransportCertificateAnnualReviewModel tempVehicleRoadAnnualReviewModel : dbVehicleRoadTransportCertificateAnnualReviewList) {
            vehicleRoadAnnualReview = new TVehicleRoadTransportCertificateAnnualReview();
            if (vehicleRoadAnnualReviewMap.get(tempVehicleRoadAnnualReviewModel.getVehicleRoadTransportCertificateAnnualReviewId()) == null) {
                vehicleRoadAnnualReview.setValid(IfValidEnum.INVALID.getKey());
            } else {
                AddOrModifyVehicleRoadTransportCertificateAnnualReviewRequestModel vehicleRoadAnnualReviewRequestModel = vehicleRoadAnnualReviewMap.get(tempVehicleRoadAnnualReviewModel.getVehicleRoadTransportCertificateAnnualReviewId());
                vehicleRoadAnnualReview.setCheckValidDate(commonBiz.getLastDayOfMonth(vehicleRoadAnnualReviewRequestModel.getCheckValidDate()));
                vehicleRoadAnnualReview.setRemark(vehicleRoadAnnualReviewRequestModel.getRemark());
            }
            vehicleRoadAnnualReview.setId(tempVehicleRoadAnnualReviewModel.getVehicleRoadTransportCertificateAnnualReviewId());
            commonBiz.setBaseEntityModify(vehicleRoadAnnualReview, BaseContextHandler.getUserName());
            upVehicleRoadAnnualReviewList.add(vehicleRoadAnnualReview);
        }
    }

    //处理车辆行驶证年审记录
    @Transactional
    public void processVehicleDrivingLicenseAnnualReview(AddOrModifyVehicleBasicInfoRequestModel requestModel, List<TCertificationPictures> addFileList, TVehicleDrivingLicense drivingLicense, List<VehicleDrivingLicenseAnnualReviewModel> dbVehicleDrivingLicenseAnnualReviewList, List<CertificationPicturesRequestModel> pageAllFileList, List<TVehicleDrivingLicenseAnnualReview> upDrivingLicenseAnnualReviewList) {
        Map<Long, AddOrModifyDrivingLicenseAnnualReviewRequestModel> drivingLicenseAnnualReviewMap = new HashMap<>();
        TVehicleDrivingLicenseAnnualReview vehicleDrivingLicenseAnnualReview;
        for (AddOrModifyDrivingLicenseAnnualReviewRequestModel tempDrivingLicenseAnnualReview : requestModel.getDrivingLicenseAnnualReviewList()) {
            if (tempDrivingLicenseAnnualReview.getDrivingLicenseAnnualReviewId() == null || tempDrivingLicenseAnnualReview.getDrivingLicenseAnnualReviewId() <= CommonConstant.LONG_ZERO) {
                vehicleDrivingLicenseAnnualReview = new TVehicleDrivingLicenseAnnualReview();
                vehicleDrivingLicenseAnnualReview.setCheckValidDate(commonBiz.getLastDayOfMonth(tempDrivingLicenseAnnualReview.getCheckValidDate()));
                vehicleDrivingLicenseAnnualReview.setDrivingLicenseId(drivingLicense.getId());
                vehicleDrivingLicenseAnnualReview.setRemark(tempDrivingLicenseAnnualReview.getRemark());
                commonBiz.setBaseEntityAdd(vehicleDrivingLicenseAnnualReview, BaseContextHandler.getUserName());
                tqVehicleDrivingLicenseAnnualReviewMapper.insertSelective(vehicleDrivingLicenseAnnualReview);
                List<CertificationPicturesRequestModel> vehicleDrivingLicenseAnnualReviewFileList = this.targetFileList(tempDrivingLicenseAnnualReview.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_DRIVING_LICENSE_ANNUAL_REVIEW);
                addFileList.addAll(this.targetAddFileList(vehicleDrivingLicenseAnnualReview.getId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, vehicleDrivingLicenseAnnualReviewFileList));
                continue;
            }
            drivingLicenseAnnualReviewMap.put(tempDrivingLicenseAnnualReview.getDrivingLicenseAnnualReviewId(), tempDrivingLicenseAnnualReview);
            if (ListUtils.isNotEmpty(tempDrivingLicenseAnnualReview.getFileList())) {
                List<CertificationPicturesRequestModel> targetFileList = this.targetFileList(tempDrivingLicenseAnnualReview.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_DRIVING_LICENSE_ANNUAL_REVIEW);
                addFileList.addAll(this.targetAddFileList(tempDrivingLicenseAnnualReview.getDrivingLicenseAnnualReviewId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, targetFileList));
                pageAllFileList.addAll(tempDrivingLicenseAnnualReview.getFileList());
            }
        }

        for (VehicleDrivingLicenseAnnualReviewModel tempDrivingLicenseAnnualReviewModel : dbVehicleDrivingLicenseAnnualReviewList) {
            vehicleDrivingLicenseAnnualReview = new TVehicleDrivingLicenseAnnualReview();
            if (drivingLicenseAnnualReviewMap.get(tempDrivingLicenseAnnualReviewModel.getVehicleDrivingLicenseAnnualReviewId()) == null) {
                vehicleDrivingLicenseAnnualReview.setValid(IfValidEnum.INVALID.getKey());
            } else {
                AddOrModifyDrivingLicenseAnnualReviewRequestModel drivingLicenseAnnualReviewRequestModel = drivingLicenseAnnualReviewMap.get(tempDrivingLicenseAnnualReviewModel.getVehicleDrivingLicenseAnnualReviewId());
                vehicleDrivingLicenseAnnualReview.setId(drivingLicenseAnnualReviewRequestModel.getDrivingLicenseAnnualReviewId());
                vehicleDrivingLicenseAnnualReview.setCheckValidDate(commonBiz.getLastDayOfMonth(drivingLicenseAnnualReviewRequestModel.getCheckValidDate()));
                vehicleDrivingLicenseAnnualReview.setRemark(drivingLicenseAnnualReviewRequestModel.getRemark());
            }
            vehicleDrivingLicenseAnnualReview.setId(tempDrivingLicenseAnnualReviewModel.getVehicleDrivingLicenseAnnualReviewId());
            commonBiz.setBaseEntityModify(vehicleDrivingLicenseAnnualReview, BaseContextHandler.getUserName());
            upDrivingLicenseAnnualReviewList.add(vehicleDrivingLicenseAnnualReview);
        }
    }

    //处理车辆等级评定信息
    @Transactional
    public void processVehicleGradeEstimationRecord(AddOrModifyVehicleBasicInfoRequestModel requestModel, TVehicleBasic vehicleBasic, List<TCertificationPictures> addFileList, List<VehicleGradeEstimationRecordModel> dbVehicleGradeEstimationRecordModelList, List<CertificationPicturesRequestModel> pageAllFileList, List<TVehicleGradeEstimationRecord> upGradeEstimationRecordList) {
        Map<Long, AddOrModifyVehicleGradeEstimationRecordRequestModel> vehicleGradeEstimationRecordMap = new HashMap<>();
        TVehicleGradeEstimationRecord upGradeEstimationRecord;
        for (AddOrModifyVehicleGradeEstimationRecordRequestModel tempGradeEstimationRecord : requestModel.getVehicleGradeEstimationRecordList()) {
            if (tempGradeEstimationRecord.getVehicleGradeEstimationRecordId() == null
                    || tempGradeEstimationRecord.getVehicleGradeEstimationRecordId() <= CommonConstant.LONG_ZERO) {
                upGradeEstimationRecord = new TVehicleGradeEstimationRecord();
                upGradeEstimationRecord.setGrade(tempGradeEstimationRecord.getGrade());
                upGradeEstimationRecord.setEstimationDate(tempGradeEstimationRecord.getEstimationDate());
                upGradeEstimationRecord.setVehicleId(vehicleBasic.getId());
                upGradeEstimationRecord.setRemark(tempGradeEstimationRecord.getRemark());
                commonBiz.setBaseEntityAdd(upGradeEstimationRecord, BaseContextHandler.getUserName());
                tqVehicleGradeEstimationRecordMapper.insertSelective(upGradeEstimationRecord);
                List<CertificationPicturesRequestModel> vehicleGpsRecordFileList = this.targetFileList(tempGradeEstimationRecord.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_GRADE_ESTIMATION_RECORD);
                addFileList.addAll(this.targetAddFileList(upGradeEstimationRecord.getId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, vehicleGpsRecordFileList));
                continue;
            }
            vehicleGradeEstimationRecordMap.put(tempGradeEstimationRecord.getVehicleGradeEstimationRecordId(), tempGradeEstimationRecord);
            if (ListUtils.isNotEmpty(tempGradeEstimationRecord.getFileList())) {
                List<CertificationPicturesRequestModel> targetFileList = this.targetFileList(tempGradeEstimationRecord.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_GRADE_ESTIMATION_RECORD);
                pageAllFileList.addAll(tempGradeEstimationRecord.getFileList());
                addFileList.addAll(this.targetAddFileList(tempGradeEstimationRecord.getVehicleGradeEstimationRecordId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, targetFileList));
            }
        }

        for (VehicleGradeEstimationRecordModel tempGradeEstimationRecordModel : dbVehicleGradeEstimationRecordModelList) {
            upGradeEstimationRecord = new TVehicleGradeEstimationRecord();
            if (vehicleGradeEstimationRecordMap.get(tempGradeEstimationRecordModel.getVehicleGradeEstimationRecordId()) == null) {
                upGradeEstimationRecord.setValid(IfValidEnum.INVALID.getKey());
            } else {
                AddOrModifyVehicleGradeEstimationRecordRequestModel gradeEstimationRecordRequestModel = vehicleGradeEstimationRecordMap.get(tempGradeEstimationRecordModel.getVehicleGradeEstimationRecordId());
                upGradeEstimationRecord.setEstimationDate(gradeEstimationRecordRequestModel.getEstimationDate());
                upGradeEstimationRecord.setGrade(gradeEstimationRecordRequestModel.getGrade());
                upGradeEstimationRecord.setRemark(gradeEstimationRecordRequestModel.getRemark());

            }
            upGradeEstimationRecord.setId(tempGradeEstimationRecordModel.getVehicleGradeEstimationRecordId());
            upGradeEstimationRecord.setVehicleId(vehicleBasic.getId());
            commonBiz.setBaseEntityModify(upGradeEstimationRecord, BaseContextHandler.getUserName());
            upGradeEstimationRecordList.add(upGradeEstimationRecord);
        }
    }

    //处理车辆gps信息
    @Transactional
    public void processVehicleGpsRecord(AddOrModifyVehicleBasicInfoRequestModel requestModel, TVehicleBasic vehicleBasic, List<TCertificationPictures> addFileList, List<VehicleGpsRecordModel> dbVehicleGpsRecordModelList, List<CertificationPicturesRequestModel> pageAllFileList, List<TVehicleGpsRecord> upVehicleGpsRecordList) {
        Map<Long, AddOrModifyVehicleGpsRecordRequestModel> vehicleGpsRecordMap = new HashMap<>();
        TVehicleGpsRecord vehicleGpsRecord;

        for (AddOrModifyVehicleGpsRecordRequestModel tempVehicleGpsRecord : requestModel.getVehicleGpsRecordList()) {
            if (tempVehicleGpsRecord.getVehicleGpsRecordId() == null
                    || tempVehicleGpsRecord.getVehicleGpsRecordId() <= CommonConstant.LONG_ZERO) {
                vehicleGpsRecord = new TVehicleGpsRecord();
                vehicleGpsRecord.setVehicleId(vehicleBasic.getId());
                vehicleGpsRecord.setInstallTime(tempVehicleGpsRecord.getInstallTime());
                vehicleGpsRecord.setTerminalType(tempVehicleGpsRecord.getTerminalType());
                vehicleGpsRecord.setSimNumber(tempVehicleGpsRecord.getSimNumber());
                vehicleGpsRecord.setGpsServiceProvider(tempVehicleGpsRecord.getGpsServiceProvider());
                vehicleGpsRecord.setRemark(tempVehicleGpsRecord.getRemark());
                commonBiz.setBaseEntityAdd(vehicleGpsRecord, BaseContextHandler.getUserName());
                tqVehicleGpsRecordMapper.insertSelective(vehicleGpsRecord);
                List<CertificationPicturesRequestModel> vehicleGpsRecordFileList = this.targetFileList(tempVehicleGpsRecord.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_GPS_RECORD);
                addFileList.addAll(this.targetAddFileList(vehicleGpsRecord.getId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, vehicleGpsRecordFileList));
                continue;
            }
            vehicleGpsRecordMap.put(tempVehicleGpsRecord.getVehicleGpsRecordId(), tempVehicleGpsRecord);
            if (ListUtils.isNotEmpty(tempVehicleGpsRecord.getFileList())) {
                List<CertificationPicturesRequestModel> targetFileList = this.targetFileList(tempVehicleGpsRecord.getFileList(), CertificationPicturesObjectTypeEnum.T_VEHICLE_GPS_RECORD);
                pageAllFileList.addAll(tempVehicleGpsRecord.getFileList());
                addFileList.addAll(this.targetAddFileList(tempVehicleGpsRecord.getVehicleGpsRecordId(), CopyFileTypeEnum.VEHICLE_BASIC_INFO, targetFileList));
            }
        }

        for (VehicleGpsRecordModel tempGpsRecordModel : dbVehicleGpsRecordModelList) {
            vehicleGpsRecord = new TVehicleGpsRecord();
            if (vehicleGpsRecordMap.get(tempGpsRecordModel.getVehicleGpsRecordId()) == null) {
                vehicleGpsRecord.setValid(IfValidEnum.INVALID.getKey());
            } else {
                AddOrModifyVehicleGpsRecordRequestModel gpsRecordRequestModel = vehicleGpsRecordMap.get(tempGpsRecordModel.getVehicleGpsRecordId());
                vehicleGpsRecord.setGpsServiceProvider(gpsRecordRequestModel.getGpsServiceProvider());
                vehicleGpsRecord.setSimNumber(gpsRecordRequestModel.getSimNumber());
                vehicleGpsRecord.setTerminalType(gpsRecordRequestModel.getTerminalType());
                vehicleGpsRecord.setInstallTime(gpsRecordRequestModel.getInstallTime());
                vehicleGpsRecord.setVehicleId(vehicleBasic.getId());
                vehicleGpsRecord.setRemark(gpsRecordRequestModel.getRemark());
            }
            vehicleGpsRecord.setId(tempGpsRecordModel.getVehicleGpsRecordId());
            commonBiz.setBaseEntityModify(vehicleGpsRecord, BaseContextHandler.getUserName());
            upVehicleGpsRecordList.add(vehicleGpsRecord);
        }
    }

    /**
     * 新增外部车辆资产信息
     *
     * @param requestModel
     */
    @Transactional
    @DistributedLock(prefix = CommonConstant.TMS_ADD_VEHICLE_LOCK,
            keys = "#requestModel.vehicleNo",
            waitTime = 3)
    public void saveOrModifyExternalVehicle(SaveOrModifyExternalVehicleRequestModel requestModel) {
        // 校验车主是否存在
        Long companyCarrierId = requestModel.getCompanyCarrierId();
        TCompanyCarrier companyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(companyCarrierId);
        if (companyCarrier == null || IfValidEnum.INVALID.getKey().equals(companyCarrier.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }
        //只有其他车主才能新增
        if (!IsOurCompanyEnum.OTHER_COMPANY.getKey().equals(companyCarrier.getLevel())) {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }
        //车主授权或实名后才能新增
        if (CompanyTypeEnum.COMPANY.getKey().equals(companyCarrier.getType())) {
            //企业类型判断是否授权
            if (!CompanyCarrierAuthAuditStatusEnum.BE_AUTH.getKey().equals(companyCarrier.getAuthorizationStatus())) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_NOT_AUTH);
            }
        } else {
            //个人车主判断是否实名
            if (!RealNameAuthenticationStatusEnum.REAL_NAME.getKey().equals(companyCarrier.getRealNameAuthenticationStatus())) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_NOT_AUTH);
            }
        }

        // 校验车辆类型
        if (requestModel.getVehicleType() != null) {
            TVehicleType tVehicleType = tQVehicleTypeMapper.selectByPrimaryKey(requestModel.getVehicleType());
            if (tVehicleType == null || IfValidEnum.INVALID.getKey().equals(tVehicleType.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_TYPE_IS_EMPTY);
            }
        }

        //根据车牌号查询车辆信息
        List<FuzzyQueryVehicleInfoResponseModel> vehicleInfoByVehicleNo = tqVehicleBasicMapper.queryVehicleInfoByVehicleNos(LocalStringUtil.listTostring(Collections.singletonList(requestModel.getVehicleNo()), ','));
        if (ListUtils.isNotEmpty(vehicleInfoByVehicleNo)) {//车辆信息已存在
            //车牌号不能重复,所以只有一条我司的车辆记录
            FuzzyQueryVehicleInfoResponseModel vehicleInfo = vehicleInfoByVehicleNo.get(CommonConstant.INTEGER_ZERO);

            //我司的车辆是外部的才能继续添加
            if (!VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(vehicleInfo.getVehicleProperty())) {
                throw new BizException(CarrierDataExceptionEnum.BASIC_DATA_ERROR);
            }

            //查询当前车主和车辆的关联关系
            TCarrierVehicleRelation carrierVehicleRelation = tCarrierVehicleRelationMapper.getByCompanyCarrierIdAndVehicleId(companyCarrierId, vehicleInfo.getVehicleId());
            if (carrierVehicleRelation != null) {
                //此车主已经存在这辆车了
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_HAS_EXIST);
            }

            //装载量（可装载托盘数）不同，则更新
            if (requestModel.getLoadingCapacity() != null && !requestModel.getLoadingCapacity().equals(vehicleInfo.getLoadingCapacity())){
                //更新车辆信息
                TVehicleBasic tVehicleBasic = new TVehicleBasic();
                tVehicleBasic.setId(vehicleInfo.getVehicleId());
                tVehicleBasic.setLoadingCapacity(requestModel.getLoadingCapacity());
                commonBiz.setBaseEntityModify(tVehicleBasic, BaseContextHandler.getUserName());
                tqVehicleBasicMapper.updateByPrimaryKeySelective(tVehicleBasic);
            }

            //车辆类型不同，则更新
            if (requestModel.getVehicleType() != null && !requestModel.getVehicleType().equals(vehicleInfo.getVehicleType())) {
                TVehicleDrivingLicense upVehicleDrivingLicense = new TVehicleDrivingLicense();
                upVehicleDrivingLicense.setId(vehicleInfo.getDrivingLicenseId());
                upVehicleDrivingLicense.setVehicleType(requestModel.getVehicleType());
                commonBiz.setBaseEntityModify(upVehicleDrivingLicense, BaseContextHandler.getUserName());
                tqVehicleDrivingLicenseMapper.updateByPrimaryKeySelective(upVehicleDrivingLicense);
            }

            //不存在关联关系则新增关联关系和操作记录
            addCarrierVehicleRelation(companyCarrierId, vehicleInfo.getVehicleId(), vehicleInfo.getVehicleNo());
        } else {//车辆信息不存在
            //新增车辆信息
            TVehicleBasic tVehicleBasic = new TVehicleBasic();
            tVehicleBasic.setVehicleProperty(VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey());
            tVehicleBasic.setLoadingCapacity(requestModel.getLoadingCapacity());
            commonBiz.setBaseEntityAdd(tVehicleBasic, BaseContextHandler.getUserName());
            tqVehicleBasicMapper.insertSelective(tVehicleBasic);

            //车辆行驶证信息
            TVehicleDrivingLicense tVehicleDrivingLicense = new TVehicleDrivingLicense();
            tVehicleDrivingLicense.setVehicleNo(requestModel.getVehicleNo());
            tVehicleDrivingLicense.setVehicleId(tVehicleBasic.getId());
            tVehicleDrivingLicense.setVehicleType(requestModel.getVehicleType());
            commonBiz.setBaseEntityAdd(tVehicleDrivingLicense, BaseContextHandler.getUserName());
            tqVehicleDrivingLicenseMapper.insertSelective(tVehicleDrivingLicense);

            //新增道路运输证表信息
            TVehicleRoadTransportCertificate vehicleRoadTransportCertificate = new TVehicleRoadTransportCertificate();
            vehicleRoadTransportCertificate.setVehicleId(tVehicleBasic.getId());
            commonBiz.setBaseEntityAdd(vehicleRoadTransportCertificate, BaseContextHandler.getUserName());
            tqVehicleRoadTransportCertificateMapper.insertSelective(vehicleRoadTransportCertificate);

            //添加车主车辆关联记录并记录日志
            addCarrierVehicleRelation(companyCarrierId, tVehicleBasic.getId(), requestModel.getVehicleNo());
        }
    }

    /**
     * 添加车辆车主关联记录并记录日志
     *
     * @param companyCarrierId 车主id
     * @param vehicleId        车辆id
     * @param vehicleNo        车辆车牌号
     */
    @Transactional
    public void addCarrierVehicleRelation(Long companyCarrierId, Long vehicleId, String vehicleNo) {
        //新增车主车辆关联关系
        TCarrierVehicleRelation tCarrierVehicleRelationAdd = new TCarrierVehicleRelation();
        tCarrierVehicleRelationAdd.setCompanyCarrierId(companyCarrierId);
        tCarrierVehicleRelationAdd.setVehicleId(vehicleId);
        commonBiz.setBaseEntityAdd(tCarrierVehicleRelationAdd, BaseContextHandler.getUserName());
        tCarrierVehicleRelationMapper.insertSelective(tCarrierVehicleRelationAdd);

        //添加操作日志
        tOperateLogsMapper.insertSelective(commonBiz.addOperateLogs(companyCarrierId, OperateLogsOperateTypeEnum.CARRIER_VEHICLE_ADD, vehicleNo, BaseContextHandler.getUserName()));
    }

    /**
     * 外部车辆资产详情
     * @param requestModel
     * @return
     */
    public ExternalVehicleDetailResponseModel getExternalVehicle(VehicleAssetManagementDetailRequestModel requestModel) {
        //查询车辆是否存在
        TCarrierVehicleRelation dbCarrierVehicleRelation = tCarrierVehicleRelationMapper.selectByPrimaryKey(requestModel.getCarrierVehicleId());
        if (dbCarrierVehicleRelation == null || dbCarrierVehicleRelation.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }

        //查询车辆基础信息
        TVehicleBasic dbVehicleBasic = tqVehicleBasicMapper.selectByPrimaryKey(dbCarrierVehicleRelation.getVehicleId());
        if (dbVehicleBasic == null || dbVehicleBasic.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }

        //查询车辆行驶证表
        TVehicleDrivingLicense dbVehicleDrivingLicense = tqVehicleDrivingLicenseMapper.getByVehicleId(dbCarrierVehicleRelation.getVehicleId());
        if (dbVehicleDrivingLicense == null || dbVehicleDrivingLicense.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }

        //车辆类型
        TVehicleType dbVehicleType = null;
        if (dbVehicleDrivingLicense.getVehicleType() != null && dbVehicleDrivingLicense.getVehicleType() > CommonConstant.LONG_ZERO) {
            dbVehicleType = tQVehicleTypeMapper.selectByPrimaryKey(dbVehicleDrivingLicense.getVehicleType());
        }

        //车主信息
        Map<Long, FuzzySearchCompanyCarrierResponseModel> companyCarrierMap = tCompanyCarrierMapper.selectCompanyCarrierInfoByIds(Arrays.asList(dbCarrierVehicleRelation.getCompanyCarrierId()))
                .stream().collect(Collectors.toMap(FuzzySearchCompanyCarrierResponseModel::getCompanyId, Function.identity(), (o1, o2) -> o2));


        //组装信息
        ExternalVehicleDetailResponseModel responseModel = new ExternalVehicleDetailResponseModel();
        responseModel.setCarrierVehicleId(dbCarrierVehicleRelation.getId());
        responseModel.setCompanyCarrierId(dbCarrierVehicleRelation.getCompanyCarrierId());
        responseModel.setVehicleBasicId(dbCarrierVehicleRelation.getVehicleId());
        responseModel.setVehicleNo(dbVehicleDrivingLicense.getVehicleNo());
        responseModel.setVehicleType(dbVehicleDrivingLicense.getVehicleType());
        responseModel.setLoadingCapacity(dbVehicleBasic.getLoadingCapacity());
        //车辆类型
        if (dbVehicleType != null){
            responseModel.setVehicleTypeLabel(dbVehicleType.getVehicleType());
        }
        //车主
        FuzzySearchCompanyCarrierResponseModel carrierResponseModel = companyCarrierMap.get(dbCarrierVehicleRelation.getCompanyCarrierId());
        if (carrierResponseModel != null) {
            responseModel.setCompanyCarrierType(carrierResponseModel.getCompanyType());
            responseModel.setCompanyCarrierName(carrierResponseModel.getCompanyName());
            responseModel.setCarrierContactName(carrierResponseModel.getContactName());
            responseModel.setCarrierContactPhone(carrierResponseModel.getContactPhone());
        }
        return responseModel;
    }

    /**
     * 检车是否更改车辆机构与类型 是否与车辆司机关联关系冲突
     *
     * @param vehicleId
     * @param upVehicleProperty
     * @param upVehicleType
     */
    public void checkVehicleInfo(Long vehicleId, Integer upVehicleProperty, Long upVehicleType) {
        if (vehicleId == null
                || upVehicleProperty == null
                || upVehicleType == null) {
            return;
        }

        TVehicleType vehicleType = tQVehicleTypeMapper.selectByPrimaryKey(upVehicleType);
        if (vehicleType == null || vehicleType.getVehicleCategory() == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_TYPE_IS_EMPTY);
        }

        List<TStaffVehicleRelationModel> dbVehicleInfo = new ArrayList<>();
        List<TStaffVehicleRelationModel> relationListOne = tStaffVehicleRelationMapper.getRelByVehicleId(vehicleId);
        List<TStaffVehicleRelationModel> relationListTwo = tStaffVehicleRelationMapper.getRelByTrailerVehicleId(vehicleId);

        dbVehicleInfo.addAll(relationListOne);
        dbVehicleInfo.addAll(relationListTwo);
        if (ListUtils.isEmpty(dbVehicleInfo)) {
            return;
        }

        TStaffVehicleRelationModel tempModel = dbVehicleInfo.stream().filter(o -> !vehicleType.getVehicleCategory().equals(o.getVehicleCategory()) || !upVehicleProperty.equals(o.getVehicleProperty())).findFirst().orElse(null);
        if (tempModel != null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_CATEGORY_OR_PROPERTY_CONFLICT);
        }
    }

    /**
     * 图片查询条件-list
     *
     * @param objectId
     * @param fileMappingEnumList
     * @return
     */
    private List<SearchCertificationPicturesModel> getFileSearchCondition(Long objectId, List<CertificationFileMappingEnum> fileMappingEnumList) {
        List<SearchCertificationPicturesModel> searchModelList = new ArrayList<>();
        if (ListUtils.isNotEmpty(fileMappingEnumList)) {
            SearchCertificationPicturesModel searchModel;
            for (CertificationFileMappingEnum tempMappingEnum : fileMappingEnumList) {
                searchModel = new SearchCertificationPicturesModel();
                searchModel.setObjectId(objectId);
                searchModel.setObjectType(tempMappingEnum.getObjectType().getObjectType());
                searchModel.setFileType(tempMappingEnum.getDbFileType());
                searchModelList.add(searchModel);
            }
        }
        return searchModelList;
    }

    /**
     * 图片查询单个条件
     *
     * @param objectId
     * @param fileMappingEnum
     * @return
     */
    private SearchCertificationPicturesModel getSingleFileCondition(Long objectId, CertificationFileMappingEnum fileMappingEnum) {
        if (objectId == null || fileMappingEnum == null) {
            return null;
        }
        SearchCertificationPicturesModel searchModel = new SearchCertificationPicturesModel();
        searchModel.setObjectId(objectId);
        searchModel.setObjectType(fileMappingEnum.getObjectType().getObjectType());
        searchModel.setFileType(fileMappingEnum.getDbFileType());
        return searchModel;
    }

    /**
     * 获取目标图片信息, 相同list不可重复遍历
     *
     * @param sourceFileList
     */
    private List<CertificationPicturesRequestModel> targetFileList(List<CertificationPicturesRequestModel> sourceFileList, CertificationPicturesObjectTypeEnum enumObj) {
        List<CertificationPicturesRequestModel> retList = new ArrayList<>();
        List<CertificationFileMappingEnum> mappingEnumList = CertificationFileMappingEnum.getFileTypeEnumList(enumObj);
        if (ListUtils.isNotEmpty(mappingEnumList) && ListUtils.isNotEmpty(sourceFileList)) {
            CertificationPicturesRequestModel certificationPictures;
            for (CertificationFileMappingEnum tempEnum : mappingEnumList) {
                for (CertificationPicturesRequestModel tempFile : sourceFileList) {
                    if ((tempFile.getFileId() == null || tempFile.getFileId() <= CommonConstant.LONG_ZERO) && tempEnum.getPageFileType().equals(tempFile.getFileType())) {
                        certificationPictures = new CertificationPicturesRequestModel();
                        certificationPictures.setFileId(tempFile.getFileId());
                        certificationPictures.setAbsoluteFilePath(tempFile.getAbsoluteFilePath());
                        certificationPictures.setRelativeFilepath(tempFile.getRelativeFilepath());
                        certificationPictures.setFileObjectType(enumObj.getObjectType());
                        certificationPictures.setFileType(tempEnum.getDbFileType());
                        certificationPictures.setFileName(StringUtils.isBlank(tempFile.getFileName()) ? tempEnum.getFileName() : tempFile.getFileName());
                        retList.add(certificationPictures);
                    }
                }
            }
        }
        return retList;
    }

    /**
     * 获取新增图片集合
     *
     * @param objectId
     * @param copyFileEnum
     * @param fileList
     * @return
     */
    private List<TCertificationPictures> targetAddFileList(Long objectId, CopyFileTypeEnum copyFileEnum, List<CertificationPicturesRequestModel> fileList) {
        List<TCertificationPictures> addFileList = new ArrayList<>();
        if (ListUtils.isEmpty(fileList)) {
            return addFileList;
        }
        TCertificationPictures addCertificationPictures;
        for (CertificationPicturesRequestModel tempFile : fileList) {
            if (tempFile.getFileId() == null || tempFile.getFileId() <= CommonConstant.LONG_ZERO) {
                addCertificationPictures = new TCertificationPictures();
                addCertificationPictures.setObjectId(objectId);
                addCertificationPictures.setObjectType(tempFile.getFileObjectType());
                addCertificationPictures.setFileType(tempFile.getFileType());
                addCertificationPictures.setFileTypeName(tempFile.getFileName());
                addCertificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(copyFileEnum.getKey(), "", tempFile.getRelativeFilepath(), null));
                addCertificationPictures.setUploadUserName(BaseContextHandler.getUserName());
                addCertificationPictures.setUploadTime(new Date());
                addCertificationPictures.setSuffix(addCertificationPictures.getFilePath().substring(addCertificationPictures.getFilePath().lastIndexOf('.')));
                commonBiz.setBaseEntityAdd(addCertificationPictures, BaseContextHandler.getUserName());
                addFileList.add(addCertificationPictures);
            }
        }
        return addFileList;
    }

    /**
     * 车辆资产基础信息导入
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public ImportVehicleBasicInfoResponseModel importVehicleBasicInfo(ImportVehicleBasicInfoRequestModel requestModel) {
        ImportVehicleBasicInfoResponseModel responseModel = new ImportVehicleBasicInfoResponseModel();
        responseModel.initNumber(requestModel.getNumberFailures());
        List<ImportVehicleBasicInfoListRequestModel> importList = requestModel.getImportList();
        if (ListUtils.isEmpty(importList)) {
            return responseModel;
        }

        //车辆类型校验
        Map<String, Long> vehicleTypeMap = new HashMap<>();
        List<TVehicleType> list = tQVehicleTypeMapper.findListByType("");
        if (ListUtils.isNotEmpty(list)) {
            for (TVehicleType tempVehicleType : list) {
                vehicleTypeMap.put(tempVehicleType.getVehicleType(), tempVehicleType.getId());
            }
        }

        //用于判断是否已存在车辆信息
        Map<String, VehicleBasicModel> vehicleInfoMap = new HashMap<>();
        List<String> vehicleNoList = importList.stream().map(ImportVehicleBasicInfoListRequestModel::getVehicleNo).distinct().collect(Collectors.toList());
        List<FuzzyQueryVehicleInfoResponseModel> vehicleModelList = tqVehicleBasicMapper.queryVehicleInfoByVehicleNos(LocalStringUtil.listTostring(vehicleNoList, ','));
        if (ListUtils.isNotEmpty(vehicleModelList)) {
            List<Long> vehicleIdList = vehicleModelList.stream().map(FuzzyQueryVehicleInfoResponseModel::getVehicleId).distinct().collect(Collectors.toList());
            List<VehicleBasicModel> vehicleBasicList = tqVehicleBasicMapper.getVehicleBasicModelByIds(StringUtils.listToString(vehicleIdList, ','));
            if (vehicleBasicList == null) {
                vehicleBasicList = new ArrayList<>();
            }
            List<VehicleDrivingLicenseModel> vehicleDrivingLicenseList = tqVehicleBasicMapper.getVehicleDrivingLicenseModelByIds(StringUtils.listToString(vehicleIdList, ','));
            List<VehicleRoadTransportCertificateModel> vehicleRoadTransportCertificateList = tqVehicleBasicMapper.getVehicleRoadTransportCertificateModelByIds(StringUtils.listToString(vehicleIdList, ','));
            Map<Long,VehicleDrivingLicenseModel> drivingLicenseMap=new HashMap<>();
            Map<Long,VehicleRoadTransportCertificateModel> roadTransportCertificateMap=new HashMap<>();

            if(ListUtils.isNotEmpty(vehicleDrivingLicenseList)){
                vehicleDrivingLicenseList.forEach(item->drivingLicenseMap.put(item.getVehicleBasicId(),item));
            }
            if(ListUtils.isNotEmpty(vehicleRoadTransportCertificateList)){
                vehicleRoadTransportCertificateList.forEach(item->roadTransportCertificateMap.put(item.getVehicleBasicId(),item));
            }
            for (VehicleBasicModel tempVehicleModel : vehicleBasicList) {
                VehicleDrivingLicenseModel vehicleDrivingLicenseModel = drivingLicenseMap.get(tempVehicleModel.getVehicleBasicId());
                VehicleRoadTransportCertificateModel vehicleRoadTransportCertificateModel = roadTransportCertificateMap.get(tempVehicleModel.getVehicleBasicId());

                tempVehicleModel.setBasicVehicleNo(vehicleDrivingLicenseModel.getVehicleNo());
                tempVehicleModel.setVehicleDrivingLicenseModel(vehicleDrivingLicenseModel);
                tempVehicleModel.setVehicleRoadTransportCertificateModel(vehicleRoadTransportCertificateModel);

                vehicleInfoMap.put(tempVehicleModel.getBasicVehicleNo(), tempVehicleModel);
            }
        }

        Long qiyaCompanyCarrierId = commonBiz.getQiyaCompanyCarrierId();
        for (ImportVehicleBasicInfoListRequestModel requestVehicleBasicInfo : requestModel.getImportList()) {

            //车辆类型校验
            if (StringUtils.isNotBlank(requestVehicleBasicInfo.getVehicleTypeLabel())) {
                Long vehicleValue = vehicleTypeMap.get(requestVehicleBasicInfo.getVehicleTypeLabel());
                if (vehicleValue == null) {
                    responseModel.addFailures();
                    continue;
                } else {
                    requestVehicleBasicInfo.setVehicleType(vehicleValue);
                }
            }


            Long addVehicleId = 0L;
            VehicleBasicModel dbVehicleBasicModel = vehicleInfoMap.get(requestVehicleBasicInfo.getVehicleNo());
            if (dbVehicleBasicModel != null) {
                if (dbVehicleBasicModel.getVehicleBasicId() == null
                        || dbVehicleBasicModel.getVehicleDrivingLicenseModel().getVehicleDrivingLicenseId() == null) {
                    responseModel.addFailures();
                    continue;
                }
                TCarrierVehicleRelation tCarrierVehicleRelation = tCarrierVehicleRelationMapper.getByCompanyCarrierIdAndVehicleId(qiyaCompanyCarrierId, dbVehicleBasicModel.getVehicleBasicId());
                if (tCarrierVehicleRelation != null) {
                    /*和我司存在关系*/
                    //外部修改为自主/自营
                    if (VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(dbVehicleBasicModel.getVehicleProperty())
                            && !VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(requestVehicleBasicInfo.getVehicleProperty())) {
                        //查询当前车辆是否关联其他车主
                        List<TCarrierVehicleRelation> tCarrierDriverRelations = tCarrierVehicleRelationMapper.getByVehicleIds(ConverterUtils.toString(dbVehicleBasicModel.getVehicleBasicId()));
                        if (tCarrierDriverRelations.size() > CommonConstant.INTEGER_ONE) {
                            responseModel.addFailures();
                            continue;
                        }
                    }
                } else {
                    /*和我司不存在关系*/
                    //判断车辆是否已被其他车主添加
                    if (!StaffPropertyEnum.EXTERNAL_STAFF.getKey().equals(requestVehicleBasicInfo.getVehicleProperty())) {
                        //查询当前司机是否关联其他车主
                        List<TCarrierVehicleRelation> tCarrierDriverRelations = tCarrierVehicleRelationMapper.getByVehicleIds(ConverterUtils.toString(dbVehicleBasicModel.getVehicleBasicId()));
                        if (ListUtils.isNotEmpty(tCarrierDriverRelations)) {
                            responseModel.addFailures();
                            continue;
                        }
                    }
                }

                try {
                    addVehicleId = updateImportInfo(requestVehicleBasicInfo, dbVehicleBasicModel);
                    responseModel.addSuccessful();
                } catch (Exception e) {
                    responseModel.addFailures();
                    log.info("", e);
                }
            } else {
                /*车辆不存在直接新增*/
                try {
                    addVehicleId = addImportInfo(requestVehicleBasicInfo);
                    responseModel.addSuccessful();
                } catch (Exception e) {
                    responseModel.addFailures();
                    log.info("", e);
                }
            }

            //新增车辆关联关系
            TCarrierVehicleRelation tCarrierVehicleRelationTwo = tCarrierVehicleRelationMapper.getByCompanyCarrierIdAndVehicleId(qiyaCompanyCarrierId, addVehicleId);
            if (tCarrierVehicleRelationTwo == null) {
                TCarrierVehicleRelation addTCarrierVehicleRelation = new TCarrierVehicleRelation();
                addTCarrierVehicleRelation.setVehicleId(addVehicleId);
                addTCarrierVehicleRelation.setCompanyCarrierId(qiyaCompanyCarrierId);
                commonBiz.setBaseEntityAdd(addTCarrierVehicleRelation, BaseContextHandler.getUserName());
                tCarrierVehicleRelationMapper.insertSelective(addTCarrierVehicleRelation);
            }
        }

        return responseModel;
    }

    /**
     * 导入Excel 每一行数据信息
     *
     * @param tempVehicleBasicInfo
     */
    private Long addImportInfo(ImportVehicleBasicInfoListRequestModel tempVehicleBasicInfo) {
        TVehicleBasic vehicleBasic = new TVehicleBasic();
        TVehicleDrivingLicense vehicleDrivingLicense = new TVehicleDrivingLicense();
        TVehicleRoadTransportCertificate vehicleRoadTransportCertificate = new TVehicleRoadTransportCertificate();

        this.initImportModelField(tempVehicleBasicInfo, vehicleBasic, vehicleDrivingLicense, vehicleRoadTransportCertificate);

        TVehicleGpsRecord vehicleGpsRecord = new TVehicleGpsRecord();
        vehicleGpsRecord.setInstallTime(tempVehicleBasicInfo.getInstallTime());
        vehicleGpsRecord.setTerminalType(tempVehicleBasicInfo.getTerminalType());
        vehicleGpsRecord.setSimNumber(tempVehicleBasicInfo.getSimNumber());
        vehicleGpsRecord.setGpsServiceProvider(tempVehicleBasicInfo.getGpsServiceProvider());
        TVehicleGradeEstimationRecord vehicleGradeEstimationRecord = new TVehicleGradeEstimationRecord();
        vehicleGradeEstimationRecord.setEstimationDate(tempVehicleBasicInfo.getEstimationDate());

        //车辆基础信息
        commonBiz.setBaseEntityAdd(vehicleBasic, BaseContextHandler.getUserName());
        tqVehicleBasicMapper.insertSelective(vehicleBasic);

        //行驶证检车记录
        if (vehicleGpsRecord.getInstallTime() != null) {
            vehicleGpsRecord.setVehicleId(vehicleBasic.getId());
            commonBiz.setBaseEntityAdd(vehicleGpsRecord, BaseContextHandler.getUserName());
            tqVehicleGpsRecordMapper.insertSelective(vehicleGpsRecord);
        }

        //营运证年审记录
        if (vehicleGradeEstimationRecord.getEstimationDate() != null) {
            vehicleGradeEstimationRecord.setVehicleId(vehicleBasic.getId());
            commonBiz.setBaseEntityAdd(vehicleGradeEstimationRecord, BaseContextHandler.getUserName());
            tqVehicleGradeEstimationRecordMapper.insertSelective(vehicleGradeEstimationRecord);
        }

        //机动车行驶证信息
        vehicleDrivingLicense.setVehicleId(vehicleBasic.getId());
        commonBiz.setBaseEntityAdd(vehicleDrivingLicense, BaseContextHandler.getUserName());
        tqVehicleDrivingLicenseMapper.insertSelective(vehicleDrivingLicense);

        if (tempVehicleBasicInfo.getCheckVehicleValidDate() != null) {
            TVehicleDrivingLicenseAnnualReview vehicleDrivingLicenseAnnualReview = new TVehicleDrivingLicenseAnnualReview();
            vehicleDrivingLicenseAnnualReview.setCheckValidDate(tempVehicleBasicInfo.getCheckVehicleValidDate());
            vehicleDrivingLicenseAnnualReview.setDrivingLicenseId(vehicleDrivingLicense.getId());
            commonBiz.setBaseEntityAdd(vehicleDrivingLicenseAnnualReview, BaseContextHandler.getUserName());
            tqVehicleDrivingLicenseAnnualReviewMapper.insertSelective(vehicleDrivingLicenseAnnualReview);
        }

        //道路运输证信息
        vehicleRoadTransportCertificate.setVehicleId(vehicleBasic.getId());
        commonBiz.setBaseEntityAdd(vehicleRoadTransportCertificate, BaseContextHandler.getUserName());
        tqVehicleRoadTransportCertificateMapper.insertSelective(vehicleRoadTransportCertificate);

        if (tempVehicleBasicInfo.getCheckRoadValidDate() != null) {
            TVehicleRoadTransportCertificateAnnualReview vehicleRoadTransportCertificateAnnualReview = new TVehicleRoadTransportCertificateAnnualReview();
            vehicleRoadTransportCertificateAnnualReview.setCheckValidDate(tempVehicleBasicInfo.getCheckRoadValidDate());
            vehicleRoadTransportCertificateAnnualReview.setRoadTransportCetificationId(vehicleRoadTransportCertificate.getId());
            commonBiz.setBaseEntityAdd(vehicleRoadTransportCertificateAnnualReview, BaseContextHandler.getUserName());
            tqVehicleRoadTransportCertificateAnnualReviewMapper.insertSelective(vehicleRoadTransportCertificateAnnualReview);
        }

        return vehicleBasic.getId();
    }

    /**
     * 导入Excel 每一行数据信息
     *
     * @param tempVehicleBasicInfo
     */
    private Long updateImportInfo(ImportVehicleBasicInfoListRequestModel tempVehicleBasicInfo, VehicleBasicModel upVehicleModel) {
        TVehicleBasic vehicleBasic = new TVehicleBasic();
        TVehicleDrivingLicense vehicleDrivingLicense = new TVehicleDrivingLicense();
        TVehicleRoadTransportCertificate vehicleRoadTransportCertificate = new TVehicleRoadTransportCertificate();

        /*校验车辆司机关联关系*/
        this.checkVehicleInfo(upVehicleModel.getVehicleBasicId(), tempVehicleBasicInfo.getVehicleProperty(), tempVehicleBasicInfo.getVehicleType());

        this.initImportModelField(tempVehicleBasicInfo, vehicleBasic, vehicleDrivingLicense, vehicleRoadTransportCertificate);
        vehicleBasic.setId(upVehicleModel.getVehicleBasicId());
        vehicleDrivingLicense.setId(upVehicleModel.getVehicleDrivingLicenseModel().getVehicleDrivingLicenseId());
        vehicleRoadTransportCertificate.setId(upVehicleModel.getVehicleRoadTransportCertificateModel().getVehicleRoadTransportCertificateId());

        TVehicleGpsRecord vehicleGpsRecord = new TVehicleGpsRecord();
        vehicleGpsRecord.setInstallTime(tempVehicleBasicInfo.getInstallTime());
        vehicleGpsRecord.setTerminalType(tempVehicleBasicInfo.getTerminalType());
        vehicleGpsRecord.setSimNumber(tempVehicleBasicInfo.getSimNumber());
        vehicleGpsRecord.setGpsServiceProvider(tempVehicleBasicInfo.getGpsServiceProvider());

        TVehicleGradeEstimationRecord vehicleGradeEstimationRecord = new TVehicleGradeEstimationRecord();
        vehicleGradeEstimationRecord.setEstimationDate(tempVehicleBasicInfo.getEstimationDate());

        commonBiz.setBaseEntityModify(vehicleBasic, BaseContextHandler.getUserName());
        tqVehicleBasicMapper.updateByPrimaryKeySelective(vehicleBasic);

        if (vehicleGpsRecord.getInstallTime() != null
                && CommonConstant.INTEGER_ZERO.equals(tqVehicleGpsRecordMapper.countGpsRecordByDate(vehicleBasic.getId(), vehicleGpsRecord.getInstallTime()))) {
            vehicleGpsRecord.setVehicleId(vehicleBasic.getId());
            commonBiz.setBaseEntityAdd(vehicleGpsRecord, BaseContextHandler.getUserName());
            tqVehicleGpsRecordMapper.insertSelective(vehicleGpsRecord);
        }

        if (vehicleGradeEstimationRecord.getEstimationDate() != null
                && CommonConstant.INTEGER_ZERO.equals(tqVehicleGradeEstimationRecordMapper.countGradeEstimationByDate(vehicleBasic.getId(), vehicleGradeEstimationRecord.getEstimationDate()))) {
            vehicleGradeEstimationRecord.setVehicleId(vehicleBasic.getId());
            commonBiz.setBaseEntityAdd(vehicleGradeEstimationRecord, BaseContextHandler.getUserName());
            tqVehicleGradeEstimationRecordMapper.insertSelective(vehicleGradeEstimationRecord);
        }

        vehicleDrivingLicense.setVehicleId(vehicleBasic.getId());
        commonBiz.setBaseEntityModify(vehicleDrivingLicense, BaseContextHandler.getUserName());
        tqVehicleDrivingLicenseMapper.updateByPrimaryKeySelective(vehicleDrivingLicense);
        if (tempVehicleBasicInfo.getCheckVehicleValidDate() != null
                && CommonConstant.INTEGER_ZERO.equals(tqVehicleDrivingLicenseAnnualReviewMapper.countDrivingLicenseByDate(vehicleDrivingLicense.getId(), tempVehicleBasicInfo.getCheckVehicleValidDate()))) {
            TVehicleDrivingLicenseAnnualReview vehicleDrivingLicenseAnnualReview = new TVehicleDrivingLicenseAnnualReview();
            vehicleDrivingLicenseAnnualReview.setCheckValidDate(tempVehicleBasicInfo.getCheckVehicleValidDate());
            vehicleDrivingLicenseAnnualReview.setDrivingLicenseId(vehicleDrivingLicense.getId());
            commonBiz.setBaseEntityAdd(vehicleDrivingLicenseAnnualReview, BaseContextHandler.getUserName());
            tqVehicleDrivingLicenseAnnualReviewMapper.insertSelective(vehicleDrivingLicenseAnnualReview);
        }


        if (vehicleRoadTransportCertificate.getId() == null
                || vehicleRoadTransportCertificate.getId() <= CommonConstant.LONG_ZERO) {//新增
            vehicleRoadTransportCertificate.setVehicleId(vehicleBasic.getId());
            commonBiz.setBaseEntityAdd(vehicleRoadTransportCertificate, BaseContextHandler.getUserName());
            tqVehicleRoadTransportCertificateMapper.insertSelective(vehicleRoadTransportCertificate);
        } else {//修改
            vehicleRoadTransportCertificate.setVehicleId(vehicleBasic.getId());
            commonBiz.setBaseEntityModify(vehicleRoadTransportCertificate, BaseContextHandler.getUserName());
            tqVehicleRoadTransportCertificateMapper.updateByPrimaryKeySelective(vehicleRoadTransportCertificate);
        }

        if (tempVehicleBasicInfo.getCheckRoadValidDate() != null
                && CommonConstant.INTEGER_ZERO.equals(tqVehicleRoadTransportCertificateAnnualReviewMapper.countRoadTransportReviewByDate(vehicleRoadTransportCertificate.getId(), tempVehicleBasicInfo.getCheckRoadValidDate()))) {
            TVehicleRoadTransportCertificateAnnualReview vehicleRoadTransportCertificateAnnualReview = new TVehicleRoadTransportCertificateAnnualReview();
            vehicleRoadTransportCertificateAnnualReview.setCheckValidDate(tempVehicleBasicInfo.getCheckRoadValidDate());
            vehicleRoadTransportCertificateAnnualReview.setRoadTransportCetificationId(vehicleRoadTransportCertificate.getId());
            commonBiz.setBaseEntityAdd(vehicleRoadTransportCertificateAnnualReview, BaseContextHandler.getUserName());
            tqVehicleRoadTransportCertificateAnnualReviewMapper.insertSelective(vehicleRoadTransportCertificateAnnualReview);
        }

        return vehicleBasic.getId();
    }

    private void initImportModelField(ImportVehicleBasicInfoListRequestModel tempVehicleBasicInfo,
                                      TVehicleBasic vehicleBasic,
                                      TVehicleDrivingLicense vehicleDrivingLicense,
                                      TVehicleRoadTransportCertificate vehicleRoadTransportCertificate) {
        //车辆基础信息
        vehicleBasic.setUsageProperty(tempVehicleBasicInfo.getUsageProperty());
        vehicleBasic.setIfInstallGps(tempVehicleBasicInfo.getIfInstallGps());
        vehicleBasic.setIfAccessSinopec(tempVehicleBasicInfo.getIfAccessSinopec());
        vehicleBasic.setVehicleProperty(tempVehicleBasicInfo.getVehicleProperty());
        vehicleBasic.setAuthenticationStartTime(tempVehicleBasicInfo.getAuthenticationStartTime());
        vehicleBasic.setAuthenticationExpireTime(tempVehicleBasicInfo.getAuthenticationExpireTime());
        vehicleBasic.setSource(CommonConstant.INTEGER_TWO);
        vehicleBasic.setEmissionStandardType(tempVehicleBasicInfo.getEmissionStandardType());
        if (StringUtils.isNotBlank(tempVehicleBasicInfo.getVehicleOwner())) {
            vehicleBasic.setVehicleOwner(tempVehicleBasicInfo.getVehicleOwner());
        }
        if (StringUtils.isNotBlank(tempVehicleBasicInfo.getRegistrationCertificationNumber())) {
            vehicleBasic.setRegistrationCertificationNumber(tempVehicleBasicInfo.getRegistrationCertificationNumber());
        }


        //车辆行驶证信息
        vehicleDrivingLicense.setVehicleNo(tempVehicleBasicInfo.getVehicleNo());
        vehicleDrivingLicense.setVehicleType(tempVehicleBasicInfo.getVehicleType());
        if (StringUtils.isNotBlank(tempVehicleBasicInfo.getAddress())) {
            vehicleDrivingLicense.setAddress(tempVehicleBasicInfo.getAddress());
        }
        if (StringUtils.isNotBlank(tempVehicleBasicInfo.getOwner())) {
            vehicleDrivingLicense.setOwner(tempVehicleBasicInfo.getOwner());
        }
        if (StringUtils.isNotBlank(tempVehicleBasicInfo.getBrand())) {
            vehicleDrivingLicense.setBrand(tempVehicleBasicInfo.getBrand());
        }
        if (StringUtils.isNotBlank(tempVehicleBasicInfo.getModel())) {
            vehicleDrivingLicense.setModel(tempVehicleBasicInfo.getModel());
        }
        if (StringUtils.isNotBlank(tempVehicleBasicInfo.getVehicleIdentificationNumber())) {
            vehicleDrivingLicense.setVehicleIdentificationNumber(tempVehicleBasicInfo.getVehicleIdentificationNumber());
        }
        if (StringUtils.isNotBlank(tempVehicleBasicInfo.getEngineNumber())) {
            vehicleDrivingLicense.setEngineNumber(tempVehicleBasicInfo.getEngineNumber());
        }
        if (StringUtils.isNotBlank(tempVehicleBasicInfo.getDrivingCertificationDepartmentOne())) {
            vehicleDrivingLicense.setCertificationDepartment(tempVehicleBasicInfo.getDrivingCertificationDepartmentOne());
        }
        vehicleDrivingLicense.setRegistrationDate(tempVehicleBasicInfo.getRegistrationDate());
        vehicleDrivingLicense.setIssueDate(tempVehicleBasicInfo.getDrivingIssueDate());
        if (StringUtils.isNotBlank(tempVehicleBasicInfo.getFilingNumber())) {
            vehicleDrivingLicense.setFilingNumber(tempVehicleBasicInfo.getFilingNumber());
        }
        vehicleDrivingLicense.setAuthorizedCarryingCapacity(tempVehicleBasicInfo.getAuthorizedCarryingCapacity());
        vehicleDrivingLicense.setTotalWeight(tempVehicleBasicInfo.getTotalWeight());
        vehicleDrivingLicense.setCurbWeight(tempVehicleBasicInfo.getCurbWeight());
        vehicleDrivingLicense.setTractionMassWeight(tempVehicleBasicInfo.getTractionMassWeight());
        vehicleDrivingLicense.setApprovedLoadWeight(tempVehicleBasicInfo.getApprovedLoadWeight());
        vehicleDrivingLicense.setLength(tempVehicleBasicInfo.getLength());
        vehicleDrivingLicense.setWidth(tempVehicleBasicInfo.getWidth());
        vehicleDrivingLicense.setHeight(tempVehicleBasicInfo.getHeight());
        vehicleDrivingLicense.setObsolescenceDate(tempVehicleBasicInfo.getObsolescenceDate());
        vehicleDrivingLicense.setAxleNumber(tempVehicleBasicInfo.getAxleNumber());
        vehicleDrivingLicense.setDriveShaftNumber(tempVehicleBasicInfo.getDriveShaftNumber());
        vehicleDrivingLicense.setPlateColor(tempVehicleBasicInfo.getPlateColor());

        if (StringUtils.isNotBlank(tempVehicleBasicInfo.getBodyColor())) {
            vehicleDrivingLicense.setBodyColor(tempVehicleBasicInfo.getBodyColor());
        }
        vehicleDrivingLicense.setTiresNumber(tempVehicleBasicInfo.getTiresNumber());

        //车辆道路许可证信息
        if (StringUtils.isNotBlank(tempVehicleBasicInfo.getCertificationSign())) {
            vehicleRoadTransportCertificate.setCertificationSign(tempVehicleBasicInfo.getCertificationSign());
        }
        if (StringUtils.isNotBlank(tempVehicleBasicInfo.getBusinessLicenseNumber())) {
            vehicleRoadTransportCertificate.setBusinessLicenseNumber(tempVehicleBasicInfo.getBusinessLicenseNumber());
        }
        if (StringUtils.isNotBlank(tempVehicleBasicInfo.getEconomicType())) {
            vehicleRoadTransportCertificate.setEconomicType(tempVehicleBasicInfo.getEconomicType());
        }
        vehicleRoadTransportCertificate.setTransportTonnage(tempVehicleBasicInfo.getTransportTonnage());
        if (StringUtils.isNotBlank(tempVehicleBasicInfo.getBusinessScope())) {
            vehicleRoadTransportCertificate.setBusinessScope(tempVehicleBasicInfo.getBusinessScope());
        }
        if (StringUtils.isNotBlank(tempVehicleBasicInfo.getRoadTransportCertificationDepartment())) {
            vehicleRoadTransportCertificate.setCertificationDepartment(tempVehicleBasicInfo.getRoadTransportCertificationDepartment());
        }
        vehicleRoadTransportCertificate.setIssueDate(tempVehicleBasicInfo.getIssueDate());
        vehicleRoadTransportCertificate.setObtainDate(tempVehicleBasicInfo.getObtainDate());
    }

    /**
     * 导入车辆证件信息
     *
     * @param importModel
     */
    @Transactional
    public void importVehicleCertificateInfo(ImportVehicleCertificateRequestModel importModel) {
        List<VehicleRelationInfoModel> vehicleList = tqVehicleBasicMapper.getVehicleRelationInfoByVehicleNo(importModel.getVehicleNO());
        if (ListUtils.isEmpty(vehicleList)) {
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }
        Long objectId = this.getFileObjectId(importModel.getFileType(), vehicleList.get(0));
        if (objectId == null || CommonConstant.LONG_ZERO.equals(objectId)) {
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }

        CertificationFileMappingEnum mappingEnum = CertificationFileMappingEnum.getEnumByPageType(importModel.getFileType());
        if (mappingEnum.getObjectType() == null) {
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }

        List<SearchCertificationPicturesModel> unionSearchList = new ArrayList<>();
        SearchCertificationPicturesModel picturesModel = new SearchCertificationPicturesModel();
        picturesModel.setObjectId(objectId);
        picturesModel.setFileType(mappingEnum.getDbFileType());
        picturesModel.setObjectType(mappingEnum.getObjectType().getObjectType());
        unionSearchList.add(picturesModel);
        List<TCertificationPictures> dbFileList = tqCertificationPicturesMapper.getUnionFiles(unionSearchList);

        FileUploadRequestModel uploadModel = new FileUploadRequestModel();
        MapperUtils.mapper(importModel.getUploadModel(), uploadModel);
        basicDataClient.uploadFileOSS(uploadModel);

        if (ListUtils.isEmpty(dbFileList)) {//新增
            TCertificationPictures addCertificationPictures = new TCertificationPictures();
            addCertificationPictures.setObjectId(objectId);
            addCertificationPictures.setObjectType(mappingEnum.getObjectType().getObjectType());
            addCertificationPictures.setFileType(mappingEnum.getDbFileType());
            addCertificationPictures.setFileTypeName(mappingEnum.getFileName());
            addCertificationPictures.setFilePath(importModel.getUploadModel().getFullPath());
            addCertificationPictures.setUploadUserName(BaseContextHandler.getUserName());
            addCertificationPictures.setUploadTime(new Date());
            addCertificationPictures.setSuffix(addCertificationPictures.getFilePath().substring(addCertificationPictures.getFilePath().lastIndexOf('.')));
            commonBiz.setBaseEntityAdd(addCertificationPictures, BaseContextHandler.getUserName());
            tqCertificationPicturesMapper.insertSelective(addCertificationPictures);
        } else {//修改
            TCertificationPictures certificationPictures = dbFileList.get(CommonConstant.INTEGER_ZERO);
            TCertificationPictures addCertificationPictures = new TCertificationPictures();
            addCertificationPictures.setId(certificationPictures.getId());
            addCertificationPictures.setFilePath(importModel.getUploadModel().getFullPath());
            addCertificationPictures.setSuffix(addCertificationPictures.getFilePath().substring(addCertificationPictures.getFilePath().lastIndexOf('.')));
            commonBiz.setBaseEntityModify(addCertificationPictures, BaseContextHandler.getUserName());
            tqCertificationPicturesMapper.updateByPrimaryKeySelective(addCertificationPictures);
        }
    }

    /**
     * 根据前台类型获取文件objectId
     *
     * @param fileType
     * @param infoModel
     * @return
     */
    private Long getFileObjectId(Integer fileType, VehicleRelationInfoModel infoModel) {
        if (CommonConstant.INTEGER_ONE.equals(fileType)
                || CommonConstant.INTEGER_TWO.equals(fileType)) {
            return infoModel.getVehicleDrivingLicenseId();
        } else if (CommonConstant.INTEGER_THREE.equals(fileType)
                || CommonConstant.INTEGER_FOUR.equals(fileType)
                || CommonConstant.INTEGER_FIVE.equals(fileType)
                || CommonConstant.INTEGER_SIX.equals(fileType)) {
            return infoModel.getVehicleRoadTransportCertificateId();
        } else if (CommonConstant.INTEGER_SEVEN.equals(fileType)
                || CommonConstant.INTEGER_EIGHT.equals(fileType)
                || CommonConstant.INTEGER_NINE.equals(fileType)
                || CommonConstant.INTEGER_TEN.equals(fileType)
                || CommonConstant.INTEGER_ELVEN.equals(fileType)
                || CommonConstant.INTEGER_TWELVE.equals(fileType)
                || CommonConstant.INTEGER_THIRTEEN.equals(fileType)
                || CommonConstant.INTEGER_FOURTEEN.equals(fileType)) {
            return infoModel.getVehicleId();
        } else {
            return 0L;
        }
    }

    private AssetsBoardRiskRecordResponseModel getAssetsBoardRecord(Map<String, String> templateDataMap, RiskWarningTypeEnum enumt, Integer remindDays) {
        Map<String, Object> stringObjectMap = enumt.getPlaceHolder();
        Map<String, AssetsBoardRiskRecordAmountResponseModel> stringAssetsBoardRiskRecordAmountResponseModelMap = new HashMap<>();

        AssetsBoardRiskRecordResponseModel assetsBoardRiskRecordResponseModel = new AssetsBoardRiskRecordResponseModel();
        assetsBoardRiskRecordResponseModel.setText(enumt.getText());
        for (Map.Entry<String, Object> tmp : stringObjectMap.entrySet()) {
            AssetsBoardRiskRecordAmountResponseModel assetsBoardRiskRecordAmountResponseModel = new AssetsBoardRiskRecordAmountResponseModel();
            if ("expire".equals(tmp.getKey())) {
                assetsBoardRiskRecordAmountResponseModel.setAmount(ConverterUtils.toString(ConverterUtils.toInt(remindDays)));
            } else if (templateDataMap.get(tmp.getKey()) != null) {
                assetsBoardRiskRecordAmountResponseModel.setAmount(ConverterUtils.toString(templateDataMap.get(tmp.getKey())));
            } else {
                assetsBoardRiskRecordAmountResponseModel.setAmount(ConverterUtils.toString(ConverterUtils.toInt(tmp.getValue())));
            }
            assetsBoardRiskRecordAmountResponseModel.setLink(templateDataMap.get(tmp.getKey() + "Ids"));
            stringAssetsBoardRiskRecordAmountResponseModelMap.put(tmp.getKey(), assetsBoardRiskRecordAmountResponseModel);
        }
        assetsBoardRiskRecordResponseModel.setAmountProperty((stringAssetsBoardRiskRecordAmountResponseModelMap));
        assetsBoardRiskRecordResponseModel.setType(enumt.getDateRemindTypeEnum().getValue());
        return assetsBoardRiskRecordResponseModel;
    }

    /**
     * 资产管理看板
     * @return
     */
    public AssetsBoardResponseModel assetBoard() {
        Long qiyaCompanyCarrierId = commonBiz.getQiyaCompanyCarrierId();

        AssetsBoardResponseModel assetsBoardResponseModel = tqVehicleBasicMapper.getVehicleStatics(qiyaCompanyCarrierId);
        AssetsBoardResponseModel assetsBoardResponseModel1 = tqStaffBasicMapper.getDriverStatics(qiyaCompanyCarrierId);
        assetsBoardResponseModel.setDriverCount(assetsBoardResponseModel1.getDriverCount());
        assetsBoardResponseModel.setSuperCargoCount(assetsBoardResponseModel1.getSuperCargoCount());
        assetsBoardResponseModel.setExtDriverCount(assetsBoardResponseModel1.getExtDriverCount());
        assetsBoardResponseModel.setDriverAndSuperCargoCount(assetsBoardResponseModel1.getDriverAndSuperCargoCount());
        if (assetsBoardResponseModel.getApprovedLoadWeight() != null){
            assetsBoardResponseModel.setApprovedLoadWeight(assetsBoardResponseModel.getApprovedLoadWeight().divide(CommonConstant.BIG_DECIMAL_ONE_THOUSAND_HUNDRED, 3, RoundingMode.HALF_UP));
        }
        if (assetsBoardResponseModel.getTotalWeight() != null){
            assetsBoardResponseModel.setTotalWeight(assetsBoardResponseModel.getTotalWeight().divide(CommonConstant.BIG_DECIMAL_ONE_THOUSAND_HUNDRED, 3, RoundingMode.HALF_UP));
        }

        List<TDateRemind> tqDateReminds = tqDateRemindMapper.getAllNeedRemindDate();
        Map<String, Integer> dateRemindMap = new HashMap<>();
        tqDateReminds.forEach(tmp -> dateRemindMap.put(tmp.getDateName(), tmp.getRemindDays()));
        tqDateReminds.sort(Comparator.comparing(BaseEntity::getLastModifiedTime));
        Map<String, Integer> dateOrderMap = new HashMap<>();
        for (int idx = 0; idx < tqDateReminds.size(); idx++) {
            dateOrderMap.put(tqDateReminds.get(idx).getDateName(), idx);
        }

        //查询车辆行驶证到期
        if (dateRemindMap.get(DateRemindTypeEnum.VEHICLE_LICENSE_DATE_VALIDATE.getValue()) != null) {
            Map<String, String> stringIntegerMap = tqVehicleDrivingLicenseAnnualReviewMapper.getDueDrivingLicenseCount(qiyaCompanyCarrierId,dateRemindMap.get(DateRemindTypeEnum.VEHICLE_LICENSE_DATE_VALIDATE.getValue()));
            boolean ifAdd = false;
            for (Object integer : stringIntegerMap.values()) {
                if (integer instanceof BigDecimal && !CommonConstant.INTEGER_ZERO.equals(ConverterUtils.toInteger(integer))) {
                    ifAdd = true;
                    break;
                }
            }
            if (ifAdd) {
                assetsBoardResponseModel.getVehicleCertificateList().add(getAssetsBoardRecord(stringIntegerMap, RiskWarningTypeEnum.VEHICLE_LICENSE_DATE_VALIDATE, dateRemindMap.get(DateRemindTypeEnum.VEHICLE_LICENSE_DATE_VALIDATE.getValue())));
            }
        }

        //查询营运证到期  年月
        if (dateRemindMap.get(DateRemindTypeEnum.ROAD_TRANSPORT_DATE_VALIDATE.getValue()) != null) {
            Map<String, String> stringIntegerMap2 = tqVehicleRoadTransportCertificateAnnualReviewMapper.getDueRoadTransportCertificateCount(qiyaCompanyCarrierId,dateRemindMap.get(DateRemindTypeEnum.ROAD_TRANSPORT_DATE_VALIDATE.getValue()));
            boolean ifAdd = false;
            for (Object integer : stringIntegerMap2.values()) {
                if (integer instanceof BigDecimal && !CommonConstant.INTEGER_ZERO.equals(ConverterUtils.toInteger(integer))) {
                    ifAdd = true;
                    break;
                }
            }
            if (ifAdd) {
                assetsBoardResponseModel.getVehicleCertificateList().add(getAssetsBoardRecord(stringIntegerMap2, RiskWarningTypeEnum.ROAD_TRANSPORT_DATE_VALIDATE, dateRemindMap.get(DateRemindTypeEnum.ROAD_TRANSPORT_DATE_VALIDATE.getValue())));
            }
        }

        //查询等级评定检查
        if (dateRemindMap.get(DateRemindTypeEnum.GRADE_ESTIMATION_DATE_VALIDATE.getValue()) != null) {
            Map<String, String> stringIntegerMap3 = tqVehicleGradeEstimationRecordMapper.getDueGradeEstimationCount(qiyaCompanyCarrierId,dateRemindMap.get(DateRemindTypeEnum.GRADE_ESTIMATION_DATE_VALIDATE.getValue()));
            boolean ifAdd = false;
            for (Object integer : stringIntegerMap3.values()) {
                if (integer instanceof BigDecimal && !CommonConstant.INTEGER_ZERO.equals(ConverterUtils.toInteger(integer))) {
                    ifAdd = true;
                    break;
                }
            }
            if (ifAdd) {
                assetsBoardResponseModel.getVehicleCertificateList().add(getAssetsBoardRecord(stringIntegerMap3, RiskWarningTypeEnum.GRADE_ESTIMATION_DATE_VALIDATE, dateRemindMap.get(DateRemindTypeEnum.GRADE_ESTIMATION_DATE_VALIDATE.getValue())));
            }
        }
        //车辆强制报废期
        if (dateRemindMap.get(DateRemindTypeEnum.VEHICLE_OBSOLESCENSE_VALIDATE.getValue()) != null) {
            Map<String, String> stringIntegerMap3 = tqVehicleDrivingLicenseMapper.getDueObsolescenceCount(qiyaCompanyCarrierId,dateRemindMap.get(DateRemindTypeEnum.VEHICLE_OBSOLESCENSE_VALIDATE.getValue()));
            boolean ifAdd = false;
            for (Object integer : stringIntegerMap3.values()) {
                if (integer instanceof BigDecimal && !CommonConstant.INTEGER_ZERO.equals(ConverterUtils.toInteger(integer))) {
                    ifAdd = true;
                    break;
                }
            }
            if (ifAdd) {
                assetsBoardResponseModel.getVehicleCertificateList().add(getAssetsBoardRecord(stringIntegerMap3, RiskWarningTypeEnum.DRIVING_LICENSE_OBSOLESCENSE_DATE_VALIDATE, dateRemindMap.get(DateRemindTypeEnum.VEHICLE_OBSOLESCENSE_VALIDATE.getValue())));
            }
        }

        //身份证到期
        if (dateRemindMap.get(DateRemindTypeEnum.IDENTITY_NUMBER_VALID_DATE.getValue()) != null) {
            Map<String, String> stringIntegerMap7 = tqStaffBasicMapper.getDueIdentityCount(qiyaCompanyCarrierId,dateRemindMap.get(DateRemindTypeEnum.IDENTITY_NUMBER_VALID_DATE.getValue()));
            boolean ifAdd = false;
            for (Object integer : stringIntegerMap7.values()) {
                if (integer instanceof BigDecimal && !CommonConstant.INTEGER_ZERO.equals(ConverterUtils.toInteger(integer))) {
                    ifAdd = true;
                    break;
                }
            }
            if (ifAdd) {
                assetsBoardResponseModel.getStaffCertificateList().add(getAssetsBoardRecord(stringIntegerMap7, RiskWarningTypeEnum.IDENTITY_DATE_VALIDATE, dateRemindMap.get(DateRemindTypeEnum.IDENTITY_NUMBER_VALID_DATE.getValue())));
            }
        }

        //驾驶证到期
        if (dateRemindMap.get(DateRemindTypeEnum.DRIVING_LICENSE_DATE_VALIDATE.getValue()) != null) {
            Map<String, String> stringIntegerMap8 = tqStaffDriverCredentialMapper.getDueDriverCredentialCount(qiyaCompanyCarrierId,dateRemindMap.get(DateRemindTypeEnum.DRIVING_LICENSE_DATE_VALIDATE.getValue()));
            boolean ifAdd = false;
            for (Object integer : stringIntegerMap8.values()) {
                if (integer instanceof BigDecimal && !CommonConstant.INTEGER_ZERO.equals(ConverterUtils.toInteger(integer))) {
                    ifAdd = true;
                    break;
                }
            }
            if (ifAdd) {
                assetsBoardResponseModel.getStaffCertificateList().add(getAssetsBoardRecord(stringIntegerMap8, RiskWarningTypeEnum.DRIVING_LICENSE_DATE_VALIDATE, dateRemindMap.get(DateRemindTypeEnum.DRIVING_LICENSE_DATE_VALIDATE.getValue())));
            }
        }

        //从业资格证到期
        if (dateRemindMap.get(DateRemindTypeEnum.DRIVER_OCCUPATIONAL_DATE_VALIDATE.getValue()) != null) {
            Map<String, String> stringIntegerMap = tqStaffDriverOccupationalRecordMapper.getDueOccupationalCount(qiyaCompanyCarrierId,dateRemindMap.get(DateRemindTypeEnum.DRIVER_OCCUPATIONAL_DATE_VALIDATE.getValue()));
            boolean ifAdd = false;
            for (Object integer : stringIntegerMap.values()) {
                if (integer instanceof BigDecimal && !CommonConstant.INTEGER_ZERO.equals(ConverterUtils.toInteger(integer))) {
                    ifAdd = true;
                    break;
                }
            }
            if (ifAdd) {
                assetsBoardResponseModel.getStaffCertificateList().add(getAssetsBoardRecord(stringIntegerMap, RiskWarningTypeEnum.DRIVER_OCCUPATIONAL_DATE_VALIDATE, dateRemindMap.get(DateRemindTypeEnum.DRIVER_OCCUPATIONAL_DATE_VALIDATE.getValue())));
            }
        }


        //诚信考核到期  年月
        if (dateRemindMap.get(DateRemindTypeEnum.INTEGRITY_EXAMINATION_DATE_VALIDATE.getValue()) != null) {
            Map<String, String> stringIntegerMap5 = tqStaffDriverIntegrityExaminationRecordMapper.getDueIntegrityExaminationCount(qiyaCompanyCarrierId,dateRemindMap.get(DateRemindTypeEnum.INTEGRITY_EXAMINATION_DATE_VALIDATE.getValue()));
            boolean ifAdd = false;
            for (Object integer : stringIntegerMap5.values()) {
                if (integer instanceof BigDecimal && !CommonConstant.INTEGER_ZERO.equals(ConverterUtils.toInteger(integer))) {
                    ifAdd = true;
                    break;
                }
            }
            if (ifAdd) {
                assetsBoardResponseModel.getStaffCertificateList().add(getAssetsBoardRecord(stringIntegerMap5, RiskWarningTypeEnum.INTEGRITY_EXAMINATION_DATE_VALIDATE, dateRemindMap.get(DateRemindTypeEnum.INTEGRITY_EXAMINATION_DATE_VALIDATE.getValue())));
            }
        }

        //继续教育到期
        if (dateRemindMap.get(DateRemindTypeEnum.CONTINUE_LEARNING_DATE_VALIDATE.getValue()) != null) {
            Map<String, String> stringIntegerMap6 = tqStaffDriverContinueLearningRecordMapper.getDueContinueLearningCount(qiyaCompanyCarrierId,dateRemindMap.get(DateRemindTypeEnum.CONTINUE_LEARNING_DATE_VALIDATE.getValue()));
            boolean ifAdd = false;
            for (Object integer : stringIntegerMap6.values()) {
                if (integer instanceof BigDecimal && !CommonConstant.INTEGER_ZERO.equals(ConverterUtils.toInteger(integer))) {
                    ifAdd = true;
                    break;
                }
            }
            if (ifAdd) {
                assetsBoardResponseModel.getStaffCertificateList().add(getAssetsBoardRecord(stringIntegerMap6, RiskWarningTypeEnum.CONTINUE_LEARNING_DATE_VALIDATE, dateRemindMap.get(DateRemindTypeEnum.CONTINUE_LEARNING_DATE_VALIDATE.getValue())));
            }
        }

        assetsBoardResponseModel.getVehicleCertificateList().sort(Comparator.comparingInt(o -> dateOrderMap.get(o.getType())));
        assetsBoardResponseModel.getStaffCertificateList().sort(Comparator.comparingInt(o -> dateOrderMap.get(o.getType())));

        //保险到期风险
        List<AssetsBoardRiskRecordResponseModel> insuranceDueList = new ArrayList<>();
        assetsBoardResponseModel.setInsuranceDueList(insuranceDueList);
        Date now = new Date();

        if (dateRemindMap.get(DateRemindTypeEnum.INSURANCE_DATE_VALIDATE.getValue()) != null) {
            Calendar today = Calendar.getInstance();
            String currentMonthTag = today.get(Calendar.YEAR) + "年" + (today.get(Calendar.MONTH) + 1) + "月";
            List<TInsurance> aboutToOverDueInsurances = tqInsuranceMapper.getAboutToOverDueInsurance(now, dateRemindMap.get(DateRemindTypeEnum.INSURANCE_DATE_VALIDATE.getValue()));
            List<TInsurance> hasExpiredInsurances = removeAboutToOverDue(getLastOverDueInsurances(tqInsuranceMapper.getOverDueInsurance(today.getTime())), aboutToOverDueInsurances);
            Map<String, List<TInsurance>> willExpireAmount = new LinkedHashMap<>();
            willExpireAmount.put(currentMonthTag, new ArrayList<>());
            aboutToOverDueInsurances.stream().forEach(tmp -> {
                Calendar endCalendar = Calendar.getInstance();
                endCalendar.setTime(tmp.getEndTime());
                Integer thisMonth = endCalendar.get(Calendar.MONTH) + 1;
                String yearMonthTag = endCalendar.get(Calendar.YEAR) + "年" + thisMonth + "月";
                if (willExpireAmount.get(yearMonthTag) == null) {
                    willExpireAmount.put(yearMonthTag, new ArrayList<>(Arrays.asList(tmp)));
                } else {
                    willExpireAmount.get(yearMonthTag).add(tmp);
                }
            });
            String currentMonthText = ":{expired}车保险已过期，{willExpired}车保险将到期，去年缴纳保险金额合计：{totalMoney}万，其中{vioVehiCount}辆车保险期内发生违章事故{vioAcciCount}起,预计保险费用会{grow};";
            String nextMonthText = ":{willExpired}车保险将到期，去年缴纳保险金额合计：{totalMoney}万，其中{vioVehiCount}辆车保险期内发生违章事故{vioAcciCount}起,预计保险费用会{grow};";


            for (Map.Entry<String, List<TInsurance>> entry : willExpireAmount.entrySet()) {
                AssetsBoardRiskRecordResponseModel assetsBoardRiskRecordResponseModel = new AssetsBoardRiskRecordResponseModel();
                Map<String, AssetsBoardRiskRecordAmountResponseModel> amountProperty = new HashMap<>();
                AssetsBoardRiskRecordAmountResponseModel amountProp = null;
                assetsBoardRiskRecordResponseModel.setAmountProperty(amountProperty);
                StringBuilder text = new StringBuilder();
                int count = 0;
                if (currentMonthTag.equals(entry.getKey())) {
                    text.append(entry.getKey() + currentMonthText);
                    amountProp = new AssetsBoardRiskRecordAmountResponseModel();
                    amountProp.setAmount(ConverterUtils.toString(hasExpiredInsurances.stream().map(TInsurance::getVehicleId).distinct().toArray().length));
                    count += ConverterUtils.toInt(amountProp.getAmount());
                    amountProp.setLink(StringUtils.listToString(hasExpiredInsurances.stream().distinct().map(TInsurance::getId).collect(Collectors.toList()), ','));
                    amountProperty.put("expired", amountProp);
                } else {
                    text.append(entry.getKey() + nextMonthText);
                }
                assetsBoardRiskRecordResponseModel.setText(text.toString());

                amountProp = new AssetsBoardRiskRecordAmountResponseModel();
                if (entry.getValue() != null) {
                    amountProp.setAmount(ConverterUtils.toString(entry.getValue().stream().map(TInsurance::getVehicleId).distinct().toArray().length));
                    count += ConverterUtils.toInt(amountProp.getAmount());
                    amountProp.setLink(StringUtils.listToString(entry.getValue().stream().distinct().map(TInsurance::getId).collect(Collectors.toList()), ','));
                }
                if (count == 0) {
                    continue;
                }
                amountProperty.put("willExpired", amountProp);
                amountProp = new AssetsBoardRiskRecordAmountResponseModel();
                BigDecimal totalMoney = BigDecimal.ZERO;
                //保险车辆id set
                Set<Long> vehicleIdSet = new HashSet<>();
                if (currentMonthTag.equals(entry.getKey())) {
                    for (TInsurance tmp : hasExpiredInsurances) {
                        totalMoney = totalMoney.add(tmp.getPremium());
                        vehicleIdSet.add(tmp.getVehicleId());
                    }
                }
                for (TInsurance tmp : entry.getValue()) {
                    totalMoney = totalMoney.add(tmp.getPremium());
                    vehicleIdSet.add(tmp.getVehicleId());
                }
                amountProp.setAmount(ConverterUtils.toString(totalMoney.divide(BigDecimal.valueOf(10000), 6, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
                amountProperty.put("totalMoney", amountProp);

                amountProp = new AssetsBoardRiskRecordAmountResponseModel();
                //违章车辆id set
                Set<Long> vehicleIdS = new HashSet<>();
                int vioAcciCount = CommonConstant.INTEGER_ZERO;
                List<TViolationRegulations> tqViolationRegulations = new ArrayList<>();
                if (ListUtils.isNotEmpty(new ArrayList<>(vehicleIdSet))) {
                    tqViolationRegulations = tQViolationRegulationsMapper.getViolationRegulations(StringUtils.listToString(new ArrayList<>(vehicleIdSet), ','));
                }
                for (TViolationRegulations tqViolationRegulations1 : tqViolationRegulations) {
                    boolean flag = false;//是否违章标志位
                    if (currentMonthTag.equals(entry.getKey())) {
                        for (TInsurance tqInsurance : hasExpiredInsurances) {
                            if (tqInsurance.getVehicleId().equals(tqViolationRegulations1.getVehicleId()) && tqViolationRegulations1.getOccuranceTime().compareTo(tqInsurance.getStartTime()) >= 0) {
                                flag = true;
                                break;
                            }
                        }
                    }
                    if (!flag) {
                        for (TInsurance tqInsurance : entry.getValue()) {
                            if (tqInsurance.getVehicleId().equals(tqViolationRegulations1.getVehicleId()) && tqViolationRegulations1.getOccuranceTime().compareTo(tqInsurance.getStartTime()) >= 0) {
                                flag = true;
                                break;
                            }
                        }
                    }
                    if (flag) {
                        vehicleIdS.add(tqViolationRegulations1.getVehicleId());
                        vioAcciCount = vioAcciCount + 1;
                    }
                }
                amountProp.setAmount(ConverterUtils.toString(vehicleIdS.size()));
                amountProperty.put("vioVehiCount", amountProp);
                amountProp = new AssetsBoardRiskRecordAmountResponseModel();
                amountProp.setAmount(ConverterUtils.toString(vioAcciCount));
                amountProperty.put("vioAcciCount", amountProp);

                amountProp = new AssetsBoardRiskRecordAmountResponseModel();
                if (vioAcciCount > 0) {
                    amountProp.setAmount("增加");
                } else {
                    amountProp.setAmount("持平");
                    amountProp.setColor("green");
                }
                amountProperty.put("grow", amountProp);
                insuranceDueList.add(assetsBoardRiskRecordResponseModel);
            }
        }
        return assetsBoardResponseModel;
    }

    private List<TInsurance> getLastOverDueInsurances(List<TInsurance> allInsurances) {
        //key vehicleId+insuranceType
        Map<String, TInsurance> vehicleInsTypeInsuranceType = new HashMap<>();
        if (ListUtils.isNotEmpty(allInsurances)) {
            for (TInsurance tmp : allInsurances) {
                String key = tmp.getVehicleId() + "_" + tmp.getInsuranceType();
                TInsurance existOne = vehicleInsTypeInsuranceType.get(key);
                if (existOne == null) {
                    vehicleInsTypeInsuranceType.put(key, tmp);
                } else {
                    if (existOne.getEndTime().compareTo(tmp.getEndTime()) <= 0) {
                        vehicleInsTypeInsuranceType.put(key, tmp);
                    }
                }
            }
        }
        return new ArrayList<>(vehicleInsTypeInsuranceType.values());

    }

    private List<TInsurance> removeAboutToOverDue(List<TInsurance> overdueList, List<TInsurance> aboutOverdueList) {

        //在已到期中排除将要到期的险种的保险
        for (Iterator<TInsurance> iterator = overdueList.iterator(); iterator.hasNext(); ) {
            TInsurance overDueInsurance = iterator.next();
            for (Iterator<TInsurance> iterator2 = aboutOverdueList.iterator(); iterator2.hasNext(); ) {
                TInsurance aboutOverDueInsurance = iterator2.next();
                if (overDueInsurance.getVehicleId().equals(aboutOverDueInsurance.getVehicleId()) && overDueInsurance.getInsuranceType().equals(aboutOverDueInsurance.getInsuranceType())) {
                    iterator.remove();
                    break;
                }
            }
        }
        return overdueList;
    }

    /**
     * 停运
     *
     * @param requestModel
     */
    @Transactional
    public void vehicleOutage(VehicleAssertOutageRequestModel requestModel) {
        //查询车主车辆关联关系
        TCarrierVehicleRelation tCarrierVehicleRelation = tCarrierVehicleRelationMapper.selectByPrimaryKey(requestModel.getCarrierVehicleId());
        if (tCarrierVehicleRelation == null || IfValidEnum.INVALID.getKey().equals(tCarrierVehicleRelation.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_BASIC_IS_EMPTY);
        }

        TVehicleBasic vehicleBasic = tqVehicleBasicMapper.selectByPrimaryKey(tCarrierVehicleRelation.getVehicleId());
        if (vehicleBasic == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_BASIC_IS_EMPTY);
        }
        if (VehicleOutageStatus.OUT_SERVICE.getKey().equals(requestModel.getOutageType()) && VehicleOutageStatus.OUT_SERVICE.getKey().equals(vehicleBasic.getOperatingState())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLES_ARE_OUT_OF_SERVICE);
        }
        if (VehicleOutageStatus.SCRAP.getKey().equals(requestModel.getOutageType()) && VehicleOutageStatus.SCRAP.getKey().equals(vehicleBasic.getOperatingState())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLES_ARE_SCRAP);
        }
        if (VehicleOutageStatus.TRANSFER.getKey().equals(requestModel.getOutageType()) && VehicleOutageStatus.TRANSFER.getKey().equals(vehicleBasic.getOperatingState())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLES_ARE_TRANSFER);
        }
        Long vehicleBasicId = tCarrierVehicleRelation.getVehicleId();
        VehicleAssertOutageCheckRequestModel outageDetectionRequest = new VehicleAssertOutageCheckRequestModel();
        outageDetectionRequest.setCarrierVehicleId(requestModel.getCarrierVehicleId());
        VehicleAssertOutageCheckResponseModel outageDetectionResult = this.getOutageCheckInfo(outageDetectionRequest);
        //停运需要判断运单以及保险
        if (CommonConstant.INTEGER_ZERO.equals(outageDetectionResult.getInsuranceCheckState())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLES_OUT_OF_SERVICE_ERROR);
        }
        List<String> fileList = requestModel.getPathList();
        if(VehicleOutageStatus.SCRAP.getKey().equals(requestModel.getOutageType()) || VehicleOutageStatus.TRANSFER.getKey().equals(requestModel.getOutageType())){
            if(ListUtils.isEmpty(fileList)){
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_OUTAGE_FILE_ERROR);
            }
        }
        TVehicleBasic upVehicleBasic = new TVehicleBasic();
        upVehicleBasic.setId(vehicleBasicId);
        upVehicleBasic.setOperatingState(requestModel.getOutageType());
        upVehicleBasic.setShutDownReason(requestModel.getOutageInfo());

        if (ListUtils.isNotEmpty(fileList)) {
            List<TCertificationPictures> addFileList = new ArrayList<>();
            TCertificationPictures addCertificationPictures;
            for (String filePath : fileList) {
                addCertificationPictures = new TCertificationPictures();
                addCertificationPictures.setObjectId(vehicleBasicId);
                addCertificationPictures.setObjectType(CertificationPicturesObjectTypeEnum.T_VEHICLE_BASIC.getObjectType());
                addCertificationPictures.setFileType(CertificationPicturesFileTypeEnum.VEHICLE_BASIC_OUTAGE_FILE.getFileType());
                addCertificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.VEHICLE_OUTAGE.getKey(), "", filePath, null));
                addCertificationPictures.setFileTypeName(CertificationPicturesFileTypeEnum.VEHICLE_BASIC_OUTAGE_FILE.getFileName());
                addCertificationPictures.setUploadUserName(BaseContextHandler.getUserName());
                addCertificationPictures.setUploadTime(new Date());
                addCertificationPictures.setSuffix(addCertificationPictures.getFilePath().substring(addCertificationPictures.getFilePath().lastIndexOf('.')));
                commonBiz.setBaseEntityAdd(addCertificationPictures, BaseContextHandler.getUserName());
                addFileList.add(addCertificationPictures);
            }
            tqCertificationPicturesMapper.batchInsert(addFileList);
        }
        commonBiz.setBaseEntityModify(upVehicleBasic, BaseContextHandler.getUserName());
        tqVehicleBasicMapper.updateByPrimaryKeySelective(upVehicleBasic);
    }


    /**
     * 停运检测结果
     *
     * @param requestModel
     * @return
     */
    public VehicleAssertOutageCheckResponseModel getOutageCheckInfo(VehicleAssertOutageCheckRequestModel requestModel) {
        Integer initSafeguardCount = CommonConstant.INTEGER_ZERO;
        Integer initNotStartCount = CommonConstant.INTEGER_ZERO;

        //查询车主车辆关联关系
        TCarrierVehicleRelation tCarrierVehicleRelation = tCarrierVehicleRelationMapper.selectByPrimaryKey(requestModel.getCarrierVehicleId());
        if (tCarrierVehicleRelation == null || IfValidEnum.INVALID.getKey().equals(tCarrierVehicleRelation.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_BASIC_IS_EMPTY);
        }
        Long qiyaCompanyCarrierId = commonBiz.getQiyaCompanyCarrierId();
        if (!tCarrierVehicleRelation.getCompanyCarrierId().equals(qiyaCompanyCarrierId)) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_BASIC_IS_EMPTY);
        }

        TVehicleDrivingLicense vehicleDrivingLicense = tqVehicleDrivingLicenseMapper.getByVehicleId(tCarrierVehicleRelation.getVehicleId());
        if (vehicleDrivingLicense == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_BASIC_IS_EMPTY);
        }

        //运单
        VehicleAssertOutageCheckResponseModel responseModel = new VehicleAssertOutageCheckResponseModel();
        responseModel.setCarrierVehicleId(tCarrierVehicleRelation.getId());
        responseModel.setVehicleNo(vehicleDrivingLicense.getVehicleNo());
        //查询运单信息
        List<Long> carrierIdList = tcarrierOrderMapper.findOrderListByHistoryVehicleNo(vehicleDrivingLicense.getVehicleNo(), tCarrierVehicleRelation.getCompanyCarrierId());
        if (ListUtils.isNotEmpty(carrierIdList)) {
            responseModel.setCarrierOrderIds(StringUtils.listToString(carrierIdList, ','));
            responseModel.setCarrierOrderCheckState(CommonConstant.INTEGER_ZERO);
        }

        //保险
        Date now = new Date();
        List<Long> insuranceIdList = new ArrayList<>();
        List<TInsurance> insuranceList = tqInsuranceMapper.getByVehicleBasicId(ConverterUtils.toString(tCarrierVehicleRelation.getVehicleId()));
        if (ListUtils.isNotEmpty(insuranceList)) {

            //个人意外险
            List<TInsurance> accidentInsuranceIdList = insuranceList.stream()
                    .filter(item -> item.getPersonalAccidentInsuranceId() > CommonConstant.LONG_ZERO && item.getStatusType().equals(InsuranceStatusTypeEnum.NORMAL.getKey()))
                    .collect(Collectors.toList());
            if (ListUtils.isNotEmpty(accidentInsuranceIdList)) {
                List<Long> accidentIdList = accidentInsuranceIdList.stream().map(TInsurance::getPersonalAccidentInsuranceId).collect(Collectors.toList());
                List<PersonalAccidentInsuranceListResponseModel> accidentInsuranceList = tqPersonalAccidentInsuranceMapper.searchPersonalAccidentInsuranceList(StringUtils.listToString(accidentIdList, ','));

                List<Long> personAccidentList = new ArrayList<>();

                for (PersonalAccidentInsuranceListResponseModel item : accidentInsuranceList) {
                    if (item.getStartTime().getTime() <= now.getTime() && item.getEndTime().getTime() >= now.getTime()) {//保障中
                        initSafeguardCount++;
                        personAccidentList.add(item.getPersonalAccidentInsuranceId());
                    } else if (item.getStartTime().getTime() > now.getTime()) { //未开始
                        initNotStartCount++;
                        personAccidentList.add(item.getPersonalAccidentInsuranceId());
                    }
                }

                if (ListUtils.isNotEmpty(personAccidentList)) {
                    List<Long> tempList = accidentInsuranceIdList.stream().filter(o -> personAccidentList.contains(o.getPersonalAccidentInsuranceId())).map(TInsurance::getId).collect(Collectors.toList());
                    insuranceIdList.addAll(tempList);
                }
            }

            //其他保险
            List<Long> elseInsuranceIdList = new ArrayList<>();
            for (TInsurance item : insuranceList) {
                if (InsuranceStatusTypeEnum.NORMAL.getKey().equals(item.getStatusType()) && CommonConstant.LONG_ZERO.equals(item.getPersonalAccidentInsuranceId())) {
                    if (item.getStartTime().getTime() <= now.getTime() && item.getEndTime().getTime() >= now.getTime()) {//保障中
                        initSafeguardCount++;
                        elseInsuranceIdList.add(item.getId());
                    } else if (item.getStartTime().getTime() > now.getTime()) {//未开始
                        initNotStartCount++;
                        elseInsuranceIdList.add(item.getId());
                    }
                }
            }

            if (ListUtils.isNotEmpty(elseInsuranceIdList)) {
                insuranceIdList.addAll(elseInsuranceIdList);
            }

            if (ListUtils.isNotEmpty(insuranceIdList)) {
                responseModel.setInsuranceIds(StringUtils.listToString(insuranceIdList, ','));
                responseModel.setInsuranceCheckState(CommonConstant.INTEGER_ZERO);
                responseModel.setNotStartCount(initNotStartCount);
                responseModel.setSafeguardCount(initSafeguardCount);
            }
        }
        return responseModel;
    }

    /**
     * 根据车牌号模糊查询车辆司机GPS信息（牵引车和一体车）
     *
     * @param requestModel
     * @return
     */
    public List<GetGpsInfoByVehicleNoResponseModel> getGpsInfoByVehicleNo(VehicleNoRequestModel requestModel) {
        List<GetGpsInfoByVehicleNoResponseModel> list = tqVehicleDrivingLicenseMapper.getGpsInfoByVehicleNo(requestModel.getVehicleNo());
        if (ListUtils.isNotEmpty(list)) {
            GetGpsInfoByVehicleNoModel gps;
            for (GetGpsInfoByVehicleNoResponseModel vehicle : list) {
                if (ListUtils.isNotEmpty(vehicle.getGpsList())) {
                    gps = vehicle.getGpsList().get(0);
                    if (gps != null) {
                        vehicle.setInstallTime(gps.getInstallTime());
                        vehicle.setTerminalType(gps.getTerminalType());
                        vehicle.setSimNumber(gps.getSimNumber());
                        vehicle.setGpsServiceProvider(gps.getGpsServiceProvider());
                        vehicle.setGpsList(new ArrayList<>());
                    }
                }
            }
        }
        return list;
    }

    /**
     * 车辆报废详情
     *
     * @param  requestModel
     */
    public VehicleAssertScrapDetailResponseModel vehicleScrapDetail(VehicleAssertScrapDetailRequestModel requestModel) {
        VehicleAssertScrapDetailResponseModel responseModel=  tqVehicleBasicMapper.getVehicleScrapDetail(requestModel.getVehicleBasicId());
        if(responseModel==null){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }
        return responseModel;
    }

    /**
     * 恢复营运
     *
     * @param  requestModel
     */
    @Transactional
    public void vehicleRestoration(VehicleAssertRestorationRequestModel requestModel) {

        //查询车主车辆关联关系
        TCarrierVehicleRelation tCarrierVehicleRelation = tCarrierVehicleRelationMapper.selectByPrimaryKey(requestModel.getCarrierVehicleId());
        if (tCarrierVehicleRelation == null || IfValidEnum.INVALID.getKey().equals(tCarrierVehicleRelation.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_BASIC_IS_EMPTY);
        }

        //查询车辆基础信息
        TVehicleBasic vehicleBasic = tqVehicleBasicMapper.selectByPrimaryKey(tCarrierVehicleRelation.getVehicleId());
        if (vehicleBasic == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_BASIC_IS_EMPTY);
        }

        //停运的车辆才能操作
        if (VehicleOutageStatus.IN_OPERATION.getKey().equals(vehicleBasic.getOperatingState())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLES_ARE_IN_OF_SERVICE);
        }
        //检查车辆是否停运
        VehicleAssertOutageCheckRequestModel outageDetectionRequest = new VehicleAssertOutageCheckRequestModel();
        outageDetectionRequest.setCarrierVehicleId(tCarrierVehicleRelation.getId());
        VehicleAssertOutageCheckResponseModel outageDetectionResult = this.getOutageCheckInfo(outageDetectionRequest);
        if (CommonConstant.INTEGER_ZERO.equals(outageDetectionResult.getCarrierOrderCheckState())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLES_OUT_OF_SERVICE_ERROR);
        }

        //更新车辆基础信息
        TVehicleBasic upVehicleBasic = new TVehicleBasic();
        upVehicleBasic.setId(tCarrierVehicleRelation.getVehicleId());
        upVehicleBasic.setOperatingState(VehicleOutageStatus.IN_OPERATION.getKey());
        //启运把停运原因置空
        upVehicleBasic.setShutDownReason("");
        commonBiz.setBaseEntityModify(upVehicleBasic, BaseContextHandler.getUserName());

        //删除停运票据
        tqCertificationPicturesMapper.delByObjectTypeFileTypeId(CertificationPicturesFileTypeEnum.VEHICLE_BASIC_OUTAGE_FILE.getObjectType().getObjectType()
                , CertificationPicturesFileTypeEnum.VEHICLE_BASIC_OUTAGE_FILE.getFileType(), tCarrierVehicleRelation.getVehicleId(), BaseContextHandler.getUserName());

        tqVehicleBasicMapper.updateByPrimaryKeySelective(upVehicleBasic);
    }

    /**
     * 根据车牌号模糊搜索、车辆机构查询车辆信息（分页）
     *
     * @param requestModel 筛选条件
     * @return 车辆信息
     */
    public PageInfo<SearchVehicleByPropertyResponseModel> searchVehicleByProperty(SearchVehicleByPropertyRequestModel requestModel) {
        //开启分页查询车辆信息
        requestModel.enablePaging();
        return new PageInfo<>(tqVehicleBasicMapper.searchVehicleByProperty(requestModel.getVehicleNo(), requestModel.getVehicleProperty(), requestModel.getVehicleCategory()));
    }

    /**
     * 分页搜索挂车信息
     *
     * @param requestModel
     * @return
     */
    public PageInfo<SearchTrailerVehicleResponseModel> searchTrailerVehicle(SearchTrailerVehicleRequestModel requestModel) {
        //前台请求
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getRequestSource())) {
            //查询登录人所属车主id
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (companyCarrierId == null || companyCarrierId.equals(CommonConstant.LONG_ZERO)) {
                return new PageInfo<>(new ArrayList<>());
            }
            requestModel.setCompanyCarrierId(companyCarrierId);
            requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
            requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        }
        requestModel.enablePaging();
        return new PageInfo<>(tqVehicleBasicMapper.searchTrailerVehicle(requestModel));
    }
}