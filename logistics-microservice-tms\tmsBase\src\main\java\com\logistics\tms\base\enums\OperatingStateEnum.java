package com.logistics.tms.base.enums;

/**
 * 车辆停运-枚举
 * @Author: sj
 * @Date: 2019/9/5 19:06
 */
public enum OperatingStateEnum {
    IN_OPERATION(1, "运营中"),
    OUT_OPERATION(2, "停运中"),
            ;

    private Integer key;
    private String value;

    OperatingStateEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
