package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleTire extends BaseEntity {
    /**
    * 车辆外键
    */
    @ApiModelProperty("车辆外键")
    private Long vehicleId;

    /**
    * 车辆机构：1 自主，2 外部，3 自营
    */
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;

    /**
    * 挂车Id
    */
    @ApiModelProperty("挂车Id")
    private Long trailerVehicleId;

    /**
    * 司机外键
    */
    @ApiModelProperty("司机外键")
    private Long staffId;

    /**
    * 更换日期
    */
    @ApiModelProperty("更换日期")
    private Date replaceDate;

    /**
    * 轮胎企业
    */
    @ApiModelProperty("轮胎企业")
    private String tireCompany;

    /**
    * 结算状态：0 待结算，1 已结算
    */
    @ApiModelProperty("结算状态：0 待结算，1 已结算")
    private Integer settlementStatus;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}