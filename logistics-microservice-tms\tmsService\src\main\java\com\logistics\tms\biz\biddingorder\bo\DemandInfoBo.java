package com.logistics.tms.biz.biddingorder.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/04/28
 */
@Data
public class DemandInfoBo {

    /**
     *竞价单id
     */
    private Long biddingOrderId;

    /**
     *需求单id
     */
    private Long demandOrderId;

    /**
     *需求单号
     */
    private String demandOrderCode;

    /**
     *单位
     */
    private Integer goodsUnit;

    /**
     *发货地
     */
    private String loadAddress;

    /**
     *收货地
     */
    private String unloadAddress;

    /**
     *委托数
     */
    private BigDecimal goodsCount;

    /**
     * 货物
     */
    private String goodsName;

    /**
     * 去掉无效0
     * @return {@link BigDecimal}
     */
    public BigDecimal getGoodsCount() {
        return null!=goodsCount?goodsCount.stripTrailingZeros():null;
    }
}
