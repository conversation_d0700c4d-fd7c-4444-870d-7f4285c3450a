package com.logistics.management.webapi.api.impl.vehicletype;


import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.vehicletype.VehicleTypeApi;
import com.logistics.management.webapi.api.feign.vehicletype.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.vehicletype.mapper.VehicleTypeListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportExcelVehicleTypeInfo;
import com.logistics.management.webapi.base.constant.ImportExcelHeaders;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.enums.VehicleCategoryEnum;
import com.logistics.tms.api.feign.vehicletype.VehicleTypeServiceApi;
import com.logistics.tms.api.feign.vehicletype.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


@RestController
@Slf4j
public class VehicleTypeServiceApiImpl implements VehicleTypeApi {

    @Autowired
    private VehicleTypeServiceApi vehicleTypeServiceApi;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 模糊匹配已有的车辆类型
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<GetVehicleTypeSearchByNameResponseDto>> fuzzyVehicleType(@RequestBody GetVehicleTypeSearchByNameRequestDto requestDto) {
        Result<List<GetVehicleTypeSearchByNameResponseModel>> result = vehicleTypeServiceApi.fuzzyVehicleType(MapperUtils.mapper(requestDto, GetVehicleTypeSearchByNameRequestModel.class));
        result.throwException();
        if (ListUtils.isNotEmpty(result.getData())) {
            return Result.success(MapperUtils.mapper(result.getData(), GetVehicleTypeSearchByNameResponseDto.class));
        } else {
            return Result.success(new ArrayList<>());
        }
    }

    /**
     * 获取车辆类型列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<VehicleTypeListResponseDto>> searchVehicleTypeList(@RequestBody VehicleTypeListRequestDto requestDto) {
        Result<PageInfo<VehicleTypeListResponseModel>> result = vehicleTypeServiceApi.searchVehicleTypeList(MapperUtils.mapper(requestDto, VehicleTypeListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        pageInfo.setList(MapperUtils.mapper(pageInfo.getList(), VehicleTypeListResponseDto.class, new VehicleTypeListMapping()));
        return Result.success(pageInfo);
    }

    /**
     * 查看详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<VehicleTypeDetailResponseDto> getDetail(@RequestBody VehicleTypeDetailRequestDto requestDto) {
        Result<VehicleTypeDetailResponseModel> detail = vehicleTypeServiceApi.getDetail(MapperUtils.mapper(requestDto, VehicleTypeDetailRequestModel.class));
        detail.throwException();
        return Result.success(MapperUtils.mapper(detail.getData(), VehicleTypeDetailResponseDto.class));

    }

    /**
     * 启动/禁用
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> enableOrDisable(@RequestBody @Valid EnableVehicleTypeRequestDto requestDto) {
        Result<Boolean> result = vehicleTypeServiceApi.enableOrDisable(MapperUtils.mapper(requestDto, EnableVehicleTypeModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 新增/修改车辆类型
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addOrModifyVehicleType(@RequestBody @Valid AddOrModifyVehicleTypeRequestDto requestDto) {
        Result<Boolean> result = vehicleTypeServiceApi.addOrModifyVehicleType(MapperUtils.mapper(requestDto, AddOrModifyVehicleTypeRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 导出
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void export(VehicleTypeListRequestDto requestDto, HttpServletResponse response) {
        Result<List<VehicleTypeListResponseModel>> result = vehicleTypeServiceApi.exportVehicleType(MapperUtils.mapper(requestDto, VehicleTypeListRequestModel.class));
        result.throwException();
        String fileName = "车辆类型基础数据" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        List<VehicleTypeListResponseDto> resultList = MapperUtils.mapper(result.getData(), VehicleTypeListResponseDto.class, new VehicleTypeListMapping());
        Map<String, String> exportTypeMap = ExportExcelVehicleTypeInfo.getVehicleTypeInfo();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportTypeMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return resultList;
            }
        });
    }

    /**
     * 导入
     * @param file
     * @param request
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<ImportVehicleTypeResponseDto> importVehicleType(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        if (null == file) {
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_VEHICLE_TYPE_FILE_IS_EMPTY);
        }
        InputStream in;
        try {
            in = file.getInputStream();
        } catch (IOException e) {
            log.error("导入车辆类型失败，",e);
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_VEHICLE_TYPE_FILE_IS_EMPTY);
        }

        List<List<Object>> excelList = new ImportExcelUtil().getDataListByExcel(in, file.getOriginalFilename(), ImportExcelHeaders.getImportVehicleType());//JSON处理类
        ImportVehicleTypeRequestDto responseDto = this.initImportRepeatData(excelList);
        ImportVehicleTypeRequestModel importVehicleTypeRequestModel = MapperUtils.mapper(responseDto, ImportVehicleTypeRequestModel.class);
        commonBiz.convertObjectListToNullIfIsEmpty(importVehicleTypeRequestModel.getImportList());
        Result<ImportVehicleTypeResponseModel> result = vehicleTypeServiceApi.importVehicleType(importVehicleTypeRequestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), ImportVehicleTypeResponseDto.class));
    }
    //入参校验及转换
    public ImportVehicleTypeRequestDto initImportRepeatData(List<List<Object>> excelList) {
        ImportVehicleTypeRequestDto requestDto = new ImportVehicleTypeRequestDto();
        if (ListUtils.isEmpty(excelList)) {
            return requestDto;
        }
        Integer failureNumber = CommonConstant.INTEGER_ZERO;
        ArrayList<ImportListVehicleTypeRequestDto> importList = new ArrayList<>();
        ArrayList<String> importVehicleTypeFilterContent = new ArrayList<>();
        ImportListVehicleTypeRequestDto listVehicleTypeRequestDto;
        for (int i = 0; i < excelList.size(); i++) {
            List<Object> objects = excelList.get(i);
            if (objects != null) {
                String vehicleType = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ZERO));
                String vehicleCategoryLabel = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ONE));
                String remark = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_TWO));
                if (StringUtils.isBlank(vehicleType) && StringUtils.isBlank(remark) && StringUtils.isBlank(vehicleCategoryLabel)) {
                    continue;
                }
                if (StringUtils.isBlank(vehicleType)) {
                    failureNumber++;
                    continue;
                }
                if(CommonConstant.INTEGER_ZERO.equals(VehicleCategoryEnum.getKeyByValue(vehicleCategoryLabel))){
                    failureNumber++;
                    continue;
                }
                listVehicleTypeRequestDto = new ImportListVehicleTypeRequestDto();
                listVehicleTypeRequestDto.setVehicleType(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ZERO)));
                listVehicleTypeRequestDto.setVehicleCategory(ConverterUtils.toString(VehicleCategoryEnum.getKeyByValue(vehicleCategoryLabel)));
                listVehicleTypeRequestDto.setRemark(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_TWO)));
                if (ListUtils.isEmpty(importVehicleTypeFilterContent) || !importVehicleTypeFilterContent.contains(listVehicleTypeRequestDto.getVehicleType())) {
                    importVehicleTypeFilterContent.add(listVehicleTypeRequestDto.getVehicleType());
                    importList.add(listVehicleTypeRequestDto);
                } else {
                    failureNumber++;
                }
            }
        }
        requestDto.setImportList(importList);
        requestDto.setNumberFailure(ConverterUtils.toString(failureNumber));
        return requestDto;
    }

}



