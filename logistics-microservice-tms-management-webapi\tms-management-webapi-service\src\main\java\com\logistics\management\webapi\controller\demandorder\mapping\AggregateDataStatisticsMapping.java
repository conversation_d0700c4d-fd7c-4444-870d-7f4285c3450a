package com.logistics.management.webapi.controller.demandorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.DemandOrderStatusEnum;
import com.logistics.management.webapi.client.demandorder.response.AggregateDataStatisticsResponseModel;
import com.logistics.management.webapi.client.demandorder.response.CorrectCarrierOrderDataStatisticsResponseModel;
import com.logistics.management.webapi.client.demandorder.response.DemandOrderDataStatisticsResponseModel;
import com.logistics.management.webapi.controller.demandorder.response.AggregateDataStatisticsResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: fjh
 * @date: 202w/1/10
 */
public class AggregateDataStatisticsMapping extends MapperMapping<AggregateDataStatisticsResponseModel, AggregateDataStatisticsResponseDto> {
    @Override
    public void configure() {
        AggregateDataStatisticsResponseModel source = getSource();
        AggregateDataStatisticsResponseDto destination = getDestination();
        //获取待纠错数据
        CorrectCarrierOrderDataStatisticsResponseModel correctCarrierOrder = source.getCorrectCarrierOrder();
        //设置待纠错单数
        destination.setWaitCorrectCount(String.valueOf(correctCarrierOrder.getWaitCorrectCount()));
        //设置待纠错货物数量
        destination.setWaitCorrectAmount(correctCarrierOrder.getWaitCorrectAmount() == null ? CommonConstant.ZERO : correctCarrierOrder.getWaitCorrectAmount().stripTrailingZeros().toPlainString());
        //获取需求单集合
        List<DemandOrderDataStatisticsResponseModel> demandOrders = source.getDemandOrders();
        //全部（单数）
        Integer allCount= CommonConstant.INTEGER_ZERO;
        //待调度（单数）
        Integer waitDispatchCount= CommonConstant.INTEGER_ZERO;
        //已调度（单数）
        Integer dispatchCount= CommonConstant.INTEGER_ZERO;

        //全部（数量）
        BigDecimal allAmount = CommonConstant.BIG_DECIMAL_ZERO;
        //待调度（数量）
        BigDecimal waitDispatchAmount = CommonConstant.BIG_DECIMAL_ZERO;
        //已调度（数量）
        BigDecimal dispatchAmount = CommonConstant.BIG_DECIMAL_ZERO;
        if (ListUtils.isNotEmpty(demandOrders)){

            for (DemandOrderDataStatisticsResponseModel demandOrder : demandOrders) {
                //待调度数据统计
                if (DemandOrderStatusEnum.WAIT_DISPATCH.getKey().equals(demandOrder.getStatus()) || DemandOrderStatusEnum.PART_DISPATCH.getKey().equals(demandOrder.getStatus())){
                    //待调度货物数据统计
                    waitDispatchAmount=waitDispatchAmount.add(demandOrder.getGoodsAmount());
                    //待调度单数数据统计
                    waitDispatchCount++;
                }
                //已调度数据统计
                if (DemandOrderStatusEnum.COMPLETE_DISPATCH.getKey().equals(demandOrder.getStatus())){
                    //已调度单数数据统计
                    dispatchAmount=dispatchAmount.add(demandOrder.getGoodsAmount());
                    //已调度货物数据统计
                    dispatchCount++;
                }
                //全部单数统计
                allAmount=allAmount.add(demandOrder.getGoodsAmount());
                //全部货物数量统计
                allCount++;
            }
        }
        destination.setAllCount(String.valueOf(allCount));
        destination.setAllAmount(allAmount.stripTrailingZeros().toPlainString());
        destination.setWaitDispatchCount(String.valueOf(waitDispatchCount));
        destination.setWaitDispatchAmount(waitDispatchAmount.stripTrailingZeros().toPlainString());
        destination.setDispatchCount(String.valueOf(dispatchCount));
        destination.setDispatchAmount(dispatchAmount.stripTrailingZeros().toPlainString());

    }
}