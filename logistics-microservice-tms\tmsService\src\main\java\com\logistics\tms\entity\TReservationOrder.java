package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2024/11/07
*/
@Data
public class TReservationOrder extends BaseEntity {
    /**
    * 预约单号
    */
    @ApiModelProperty("预约单号")
    private String reservationOrderCode;

    /**
    * 预约单状态：10 待签到，20 已签到，30 已失效
    */
    @ApiModelProperty("预约单状态：10 待签到，20 已签到，30 已失效")
    private Integer status;

    /**
    * 预约类型：1 提货，2 卸货
    */
    @ApiModelProperty("预约类型：1 提货，2 卸货")
    private Integer reservationType;

    /**
    * 司机ID
    */
    @ApiModelProperty("司机ID")
    private Long driverId;

    /**
    * 司机姓名
    */
    @ApiModelProperty("司机姓名")
    private String driverName;

    /**
    * 司机手机号
    */
    @ApiModelProperty("司机手机号")
    private String driverMobile;

    /**
    * 司机身份证号码
    */
    @ApiModelProperty("司机身份证号码")
    private String driverIdentity;

    /**
    * 车辆ID
    */
    @ApiModelProperty("车辆ID")
    private Long vehicleId;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 省份ID
    */
    @ApiModelProperty("省份ID")
    private Long provinceId;

    /**
    * 省份名字
    */
    @ApiModelProperty("省份名字")
    private String provinceName;

    /**
    * 城市ID
    */
    @ApiModelProperty("城市ID")
    private Long cityId;

    /**
    * 城市名字
    */
    @ApiModelProperty("城市名字")
    private String cityName;

    /**
    * 县区id
    */
    @ApiModelProperty("县区id")
    private Long areaId;

    /**
    * 县区名字
    */
    @ApiModelProperty("县区名字")
    private String areaName;

    /**
    * 详细地址
    */
    @ApiModelProperty("详细地址")
    private String detailAddress;

    /**
    * 仓库名称
    */
    @ApiModelProperty("仓库名称")
    private String warehouse;

    /**
    * 经度
    */
    @ApiModelProperty("经度")
    private String longitude;

    /**
    * 纬度
    */
    @ApiModelProperty("纬度")
    private String latitude;

    /**
    * 预约开始时间
    */
    @ApiModelProperty("预约开始时间")
    private Date reservationStartTime;

    /**
    * 预约结束时间
    */
    @ApiModelProperty("预约结束时间")
    private Date reservationEndTime;

    /**
    * 签到时间
    */
    @ApiModelProperty("签到时间")
    private Date signDate;

    /**
    * 预约角色0.司机 1.访客 2.车主
    */
    @ApiModelProperty("预约角色0.司机 1.访客 2.车主")
    private Integer reservationRole;

    /**
    * 当且仅当访客得时候 该字段有值，为客户填写得手机号
    */
    @ApiModelProperty("当且仅当访客得时候 该字段有值，为客户填写得手机号")
    private String reservationPerson;


    /**
    * 预约来源 0.小程序 1.微信 2.车主
    */
    @ApiModelProperty("预约来源 0.小程序 1.微信 2.车主")
    private Integer reservationSource;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}