package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 上报详情,请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/5/30
 */
@Data
public class SinopecReportAbnormalDetailRequestDto {

	@ApiModelProperty(value = "需求单id",required = true)
	@NotBlank(message = "请选中数据")
	private String demandId;
}
