package com.logistics.management.webapi.controller.companycarrierauthorization.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/3
 */
@Data
public class CarrierAuthorizationAuditRequestDto {

	@ApiModelProperty(value = "车主授权信息id", required = true)
	@NotBlank(message = "车主授权信息id不能为空")
	private String carrierAuthorizationId;

	@ApiModelProperty(value = "审核类型: 1:通过 ,2:驳回", required = true)
	@NotBlank(message = "请选择审核类型")
	@Range(min = 1, max = 2, message = "请选择审核类型")
	private String auditModel;

	@ApiModelProperty(value = "是否归档, 0:否 1:是", required = true)
	@NotBlank(message = "请选择是否归档")
	@Range(min = 0, max = 1, message = "请选择是否归档")
	private String isArchived;

	@ApiModelProperty(value = "备注")
	@Length(max = 100, message = "备注最多填写100个字符")
	private String remark;
}
