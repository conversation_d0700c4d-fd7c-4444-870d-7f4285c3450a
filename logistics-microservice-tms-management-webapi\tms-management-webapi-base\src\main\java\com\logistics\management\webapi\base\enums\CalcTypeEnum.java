package com.logistics.management.webapi.base.enums;

/**
 * 计价类型 1 基价 2 一日游
 * @Author: sj
 * @Date: 2019/12/25 19:26
 */
public enum CalcTypeEnum {
    DEFAULT_VALUE(-1,""),
    BASE_PRICE(1,"基价"),
    ONE_DAY_PRICE(2,"一日游"),;

    private Integer key;
    private String value;

    CalcTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }
    public String getValue() {
        return value;
    }

    public static CalcTypeEnum getEnum(Integer key) {
        for (CalcTypeEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return DEFAULT_VALUE;
    }

}
