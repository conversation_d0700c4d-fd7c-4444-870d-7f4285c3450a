package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TStaffDriverCredential extends BaseEntity {
    /**
    * 人员ID
    */
    @ApiModelProperty("人员ID")
    private Long staffId;

    /**
    * 从业资格证件号
    */
    @ApiModelProperty("从业资格证件号")
    private String occupationalRequirementsCredentialNo;

    /**
    * 初次发证日期
    */
    @ApiModelProperty("初次发证日期")
    private Date initialIssuanceDate;

    /**
    * 驾驶证号码
    */
    @ApiModelProperty("驾驶证号码")
    private String driversLicenseNo;

    /**
    * 准假车型
    */
    @ApiModelProperty("准假车型")
    private String permittedType;

    /**
    * 驾照期限开始
    */
    @ApiModelProperty("驾照期限开始")
    private Date driversLicenseDateFrom;

    /**
    * 驾照期限结束
    */
    @ApiModelProperty("驾照期限结束")
    private Date driversLicenseDateTo;
}