package com.logistics.management.webapi.controller.dispatch.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/6 10:13
 */
@Data
public class SpecialDispatchVehicleRequestDto {

    /**
     * 需求单货物信息
     */
    @Valid
    @NotEmpty(message = "调度车辆需求单列表不能为空")
    private List<SpecialDispatchVehicleListRequestDto> vehicleRequestModels;

    /**
     * 预计提货/到货时间
     */
//    @NotBlank(message = "预计到货时间不能为空")
    private String expectArrivalTime;

    /**
     * 车辆ID
     */
    @NotBlank(message = "请选择车辆")
    private String vehicleId;

    /**
     * 挂车车辆ID
     */
    private String trailerVehicleId;

    /**
     * 司机ID
     */
//    @NotBlank(message = "请选择司机")
    private String driverId;

    /**
     * 装卸数-装
     */
    @NotBlank(message = "请填写装卸数")
    private String loadPointAmount;

    /**
     * 装卸数-卸
     */
    @NotBlank(message = "请填写装卸数")
    private String unloadPointAmount;

    /**
     * 车长（选择“零担”时传-1，其他选择什么传什么）
     */
//    @NotBlank(message = "请选择车长")
    private String vehicleLength;

    /**
     * 串点费用
     */
    @NotBlank(message = "请维护串点费用")
    @DecimalMin(value = "0", message = "请维护串点费用，0<=费用<=1000000元")
    @DecimalMax(value = "1000000", message = "请维护串点费用，0<=费用<=1000000元")
    private String crossPointFee;

    /**
     * 整车运费
     */
    @NotBlank(message = "请维护整车运费")
    @DecimalMin(value = "0.01", message = "请维护整车运费，0<费用<=1000000元")
    @DecimalMax(value = "1000000", message = "请维护整车运费，0<费用<=1000000元")
    private String carrierFreight;

    /**
     * 备注
     */
    @Size(max = 80, message = "备注不能超过80字")
    private String remark;

    /**
     * 是否匹配了车长和串点 0：否 1：是   v2.42
     */
    private String ifMatch;


    /**
     * 是否加入新的零担标识 有就是加入旧的  v2.42
     */
    private String shippingOrderCode;



}
