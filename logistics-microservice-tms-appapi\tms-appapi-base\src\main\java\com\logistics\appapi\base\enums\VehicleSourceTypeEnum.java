package com.logistics.appapi.base.enums;

/**
 * @author: wjf
 * @date: 2019/10/31 18:55
 */
public enum VehicleSourceTypeEnum {
    DEFAULT(0, ""),
    BARN_GRID_SEMI_TRAILER(1, "重型仓栅式半挂车"),
    ORDINARY_SEMI_TRAILER(2, "重型普通半挂车"),
    VAR(3, "重型厢式货车"),
    ORDINARY_TRUCK(4, "重型普通货车"),
    SEMITRAILER_TRACTOR(5, "重型半挂牵引车"),
    ;

    private Integer key;
    private String value;

    VehicleSourceTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static VehicleSourceTypeEnum getEnum(int key) {
        for (VehicleSourceTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
