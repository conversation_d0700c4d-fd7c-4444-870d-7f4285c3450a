<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCarrierOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dispatch_order_id" jdbcType="BIGINT" property="dispatchOrderId" />
    <result column="dispatch_order_code" jdbcType="VARCHAR" property="dispatchOrderCode" />
    <result column="demand_order_id" jdbcType="BIGINT" property="demandOrderId" />
    <result column="demand_order_code" jdbcType="VARCHAR" property="demandOrderCode" />
    <result column="carrier_order_code" jdbcType="VARCHAR" property="carrierOrderCode" />
    <result column="customer_order_code" jdbcType="VARCHAR" property="customerOrderCode" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="status_update_time" jdbcType="TIMESTAMP" property="statusUpdateTime" />
    <result column="if_cancel" jdbcType="INTEGER" property="ifCancel" />
    <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason" />
    <result column="cancel_operator_name" jdbcType="VARCHAR" property="cancelOperatorName" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_user_name" jdbcType="VARCHAR" property="customerUserName" />
    <result column="customer_user_mobile" jdbcType="VARCHAR" property="customerUserMobile" />
    <result column="customer_order_source" jdbcType="INTEGER" property="customerOrderSource" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="dispatch_user_id" jdbcType="BIGINT" property="dispatchUserId" />
    <result column="dispatch_user_name" jdbcType="VARCHAR" property="dispatchUserName" />
    <result column="dispatch_time" jdbcType="TIMESTAMP" property="dispatchTime" />
    <result column="company_carrier_type" jdbcType="INTEGER" property="companyCarrierType" />
    <result column="company_carrier_id" jdbcType="BIGINT" property="companyCarrierId" />
    <result column="company_carrier_name" jdbcType="VARCHAR" property="companyCarrierName" />
    <result column="carrier_contact_id" jdbcType="BIGINT" property="carrierContactId" />
    <result column="carrier_contact_name" jdbcType="VARCHAR" property="carrierContactName" />
    <result column="carrier_contact_phone" jdbcType="VARCHAR" property="carrierContactPhone" />
    <result column="company_carrier_level" jdbcType="INTEGER" property="companyCarrierLevel" />
    <result column="company_entrust_id" jdbcType="BIGINT" property="companyEntrustId" />
    <result column="company_entrust_name" jdbcType="VARCHAR" property="companyEntrustName" />
    <result column="upstream_customer" jdbcType="VARCHAR" property="upstreamCustomer" />
    <result column="load_time" jdbcType="TIMESTAMP" property="loadTime" />
    <result column="unload_time" jdbcType="TIMESTAMP" property="unloadTime" />
    <result column="sign_time" jdbcType="TIMESTAMP" property="signTime" />
    <result column="expect_amount" jdbcType="DECIMAL" property="expectAmount" />
    <result column="load_amount_expect" jdbcType="DECIMAL" property="loadAmountExpect" />
    <result column="load_amount" jdbcType="DECIMAL" property="loadAmount" />
    <result column="unload_amount_expect" jdbcType="DECIMAL" property="unloadAmountExpect" />
    <result column="unload_amount" jdbcType="DECIMAL" property="unloadAmount" />
    <result column="sign_amount" jdbcType="DECIMAL" property="signAmount" />
    <result column="expect_entrust_freight" jdbcType="DECIMAL" property="expectEntrustFreight" />
    <result column="expect_entrust_freight_type" jdbcType="INTEGER" property="expectEntrustFreightType" />
    <result column="entrust_freight_type" jdbcType="INTEGER" property="entrustFreightType" />
    <result column="entrust_freight" jdbcType="DECIMAL" property="entrustFreight" />
    <result column="sign_freight_fee" jdbcType="DECIMAL" property="signFreightFee" />
    <result column="dispatch_freight_fee_type" jdbcType="INTEGER" property="dispatchFreightFeeType" />
    <result column="dispatch_freight_fee" jdbcType="DECIMAL" property="dispatchFreightFee" />
    <result column="adjust_fee" jdbcType="DECIMAL" property="adjustFee" />
    <result column="markup_fee" jdbcType="DECIMAL" property="markupFee" />
    <result column="goods_unit" jdbcType="INTEGER" property="goodsUnit" />
    <result column="demand_order_source" jdbcType="INTEGER" property="demandOrderSource" />
    <result column="demand_order_entrust_type" jdbcType="INTEGER" property="demandOrderEntrustType" />
    <result column="settlement_tonnage" jdbcType="INTEGER" property="settlementTonnage" />
    <result column="carrier_settlement" jdbcType="INTEGER" property="carrierSettlement" />
    <result column="expect_mileage" jdbcType="DECIMAL" property="expectMileage" />
    <result column="config_distance" jdbcType="DECIMAL" property="configDistance" />
    <result column="load_validity" jdbcType="INTEGER" property="loadValidity" />
    <result column="abnormal_amount" jdbcType="DECIMAL" property="abnormalAmount" />
    <result column="if_empty" jdbcType="INTEGER" property="ifEmpty" />
    <result column="empty_time" jdbcType="TIMESTAMP" property="emptyTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="dispatch_remark" jdbcType="VARCHAR" property="dispatchRemark" />
    <result column="carrier_price_type" jdbcType="INTEGER" property="carrierPriceType" />
    <result column="carrier_price" jdbcType="DECIMAL" property="carrierPrice" />
    <result column="print_count" jdbcType="INTEGER" property="printCount" />
    <result column="if_objection" jdbcType="INTEGER" property="ifObjection" />
    <result column="out_status" jdbcType="INTEGER" property="outStatus" />
    <result column="publish_name" jdbcType="VARCHAR" property="publishName" />
    <result column="publish_mobile" jdbcType="VARCHAR" property="publishMobile" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
    <result column="publish_org_code" jdbcType="VARCHAR" property="publishOrgCode" />
    <result column="publish_org_name" jdbcType="VARCHAR" property="publishOrgName" />
    <result column="if_urgent" jdbcType="INTEGER" property="ifUrgent" />
    <result column="available_on_weekends" jdbcType="INTEGER" property="availableOnWeekends" />
    <result column="loading_unloading_part" jdbcType="INTEGER" property="loadingUnloadingPart" />
    <result column="loading_unloading_charge" jdbcType="DECIMAL" property="loadingUnloadingCharge" />
    <result column="recycle_task_type" jdbcType="INTEGER" property="recycleTaskType" />
    <result column="project_label" jdbcType="VARCHAR" property="projectLabel" />
    <result column="if_wait_audit_vehicle" jdbcType="INTEGER" property="ifWaitAuditVehicle" />
    <result column="correct_status" jdbcType="INTEGER" property="correctStatus" />
    <result column="stock_in_state" jdbcType="INTEGER" property="stockInState" />
    <result column="carrier_settle_statement_status" jdbcType="INTEGER" property="carrierSettleStatementStatus" />
    <result column="qr_code_pic_path" jdbcType="VARCHAR" property="qrCodePicPath" />
    <result column="delivery_method" jdbcType="INTEGER" property="deliveryMethod" />
    <result column="order_mode" jdbcType="INTEGER" property="orderMode" />
    <result column="vehicle_length_id" jdbcType="BIGINT" property="vehicleLengthId" />
    <result column="vehicle_length" jdbcType="DECIMAL" property="vehicleLength" />
    <result column="bargaining_mode" jdbcType="INTEGER" property="bargainingMode" />
    <result column="if_recycle_by_code" jdbcType="INTEGER" property="ifRecycleByCode" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dispatch_order_id, dispatch_order_code, demand_order_id, demand_order_code, carrier_order_code, 
    customer_order_code, status, status_update_time, if_cancel, cancel_reason, cancel_operator_name, 
    cancel_time, business_type, customer_name, customer_user_name, customer_user_mobile, 
    customer_order_source, source, dispatch_user_id, dispatch_user_name, dispatch_time, 
    company_carrier_type, company_carrier_id, company_carrier_name, carrier_contact_id, 
    carrier_contact_name, carrier_contact_phone, company_carrier_level, company_entrust_id, 
    company_entrust_name, upstream_customer, load_time, unload_time, sign_time, expect_amount, 
    load_amount_expect, load_amount, unload_amount_expect, unload_amount, sign_amount, 
    expect_entrust_freight, expect_entrust_freight_type, entrust_freight_type, entrust_freight, 
    sign_freight_fee, dispatch_freight_fee_type, dispatch_freight_fee, adjust_fee, markup_fee, 
    goods_unit, demand_order_source, demand_order_entrust_type, settlement_tonnage, carrier_settlement, 
    expect_mileage, config_distance, load_validity, abnormal_amount, if_empty, empty_time, 
    remark, dispatch_remark, carrier_price_type, carrier_price, print_count, if_objection, 
    out_status, publish_name, publish_mobile, publish_time, publish_org_code, publish_org_name, 
    if_urgent, available_on_weekends, loading_unloading_part, loading_unloading_charge, 
    recycle_task_type, project_label, if_wait_audit_vehicle, correct_status, stock_in_state, 
    carrier_settle_statement_status, qr_code_pic_path, delivery_method, order_mode, vehicle_length_id, 
    vehicle_length, bargaining_mode, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_carrier_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_carrier_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCarrierOrder">
    insert into t_carrier_order (id, dispatch_order_id, dispatch_order_code, 
      demand_order_id, demand_order_code, carrier_order_code, 
      customer_order_code, status, status_update_time, 
      if_cancel, cancel_reason, cancel_operator_name, 
      cancel_time, business_type, customer_name, 
      customer_user_name, customer_user_mobile, customer_order_source, 
      source, dispatch_user_id, dispatch_user_name, 
      dispatch_time, company_carrier_type, company_carrier_id, 
      company_carrier_name, carrier_contact_id, carrier_contact_name, 
      carrier_contact_phone, company_carrier_level, 
      company_entrust_id, company_entrust_name, upstream_customer, 
      load_time, unload_time, sign_time, 
      expect_amount, load_amount_expect, load_amount, 
      unload_amount_expect, unload_amount, sign_amount, 
      expect_entrust_freight, expect_entrust_freight_type, 
      entrust_freight_type, entrust_freight, sign_freight_fee, 
      dispatch_freight_fee_type, dispatch_freight_fee, 
      adjust_fee, markup_fee, goods_unit, 
      demand_order_source, demand_order_entrust_type, 
      settlement_tonnage, carrier_settlement, expect_mileage, 
      config_distance, load_validity, abnormal_amount, 
      if_empty, empty_time, remark, 
      dispatch_remark, carrier_price_type, carrier_price, 
      print_count, if_objection, out_status, 
      publish_name, publish_mobile, publish_time, 
      publish_org_code, publish_org_name, if_urgent, 
      available_on_weekends, loading_unloading_part, 
      loading_unloading_charge, recycle_task_type, 
      project_label, if_wait_audit_vehicle, correct_status, 
      stock_in_state, carrier_settle_statement_status, 
      qr_code_pic_path, delivery_method, order_mode, 
      vehicle_length_id, vehicle_length, bargaining_mode, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{dispatchOrderId,jdbcType=BIGINT}, #{dispatchOrderCode,jdbcType=VARCHAR}, 
      #{demandOrderId,jdbcType=BIGINT}, #{demandOrderCode,jdbcType=VARCHAR}, #{carrierOrderCode,jdbcType=VARCHAR}, 
      #{customerOrderCode,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{statusUpdateTime,jdbcType=TIMESTAMP}, 
      #{ifCancel,jdbcType=INTEGER}, #{cancelReason,jdbcType=VARCHAR}, #{cancelOperatorName,jdbcType=VARCHAR}, 
      #{cancelTime,jdbcType=TIMESTAMP}, #{businessType,jdbcType=INTEGER}, #{customerName,jdbcType=VARCHAR}, 
      #{customerUserName,jdbcType=VARCHAR}, #{customerUserMobile,jdbcType=VARCHAR}, #{customerOrderSource,jdbcType=INTEGER}, 
      #{source,jdbcType=INTEGER}, #{dispatchUserId,jdbcType=BIGINT}, #{dispatchUserName,jdbcType=VARCHAR}, 
      #{dispatchTime,jdbcType=TIMESTAMP}, #{companyCarrierType,jdbcType=INTEGER}, #{companyCarrierId,jdbcType=BIGINT}, 
      #{companyCarrierName,jdbcType=VARCHAR}, #{carrierContactId,jdbcType=BIGINT}, #{carrierContactName,jdbcType=VARCHAR}, 
      #{carrierContactPhone,jdbcType=VARCHAR}, #{companyCarrierLevel,jdbcType=INTEGER}, 
      #{companyEntrustId,jdbcType=BIGINT}, #{companyEntrustName,jdbcType=VARCHAR}, #{upstreamCustomer,jdbcType=VARCHAR}, 
      #{loadTime,jdbcType=TIMESTAMP}, #{unloadTime,jdbcType=TIMESTAMP}, #{signTime,jdbcType=TIMESTAMP}, 
      #{expectAmount,jdbcType=DECIMAL}, #{loadAmountExpect,jdbcType=DECIMAL}, #{loadAmount,jdbcType=DECIMAL}, 
      #{unloadAmountExpect,jdbcType=DECIMAL}, #{unloadAmount,jdbcType=DECIMAL}, #{signAmount,jdbcType=DECIMAL}, 
      #{expectEntrustFreight,jdbcType=DECIMAL}, #{expectEntrustFreightType,jdbcType=INTEGER}, 
      #{entrustFreightType,jdbcType=INTEGER}, #{entrustFreight,jdbcType=DECIMAL}, #{signFreightFee,jdbcType=DECIMAL}, 
      #{dispatchFreightFeeType,jdbcType=INTEGER}, #{dispatchFreightFee,jdbcType=DECIMAL}, 
      #{adjustFee,jdbcType=DECIMAL}, #{markupFee,jdbcType=DECIMAL}, #{goodsUnit,jdbcType=INTEGER}, 
      #{demandOrderSource,jdbcType=INTEGER}, #{demandOrderEntrustType,jdbcType=INTEGER}, 
      #{settlementTonnage,jdbcType=INTEGER}, #{carrierSettlement,jdbcType=INTEGER}, #{expectMileage,jdbcType=DECIMAL}, 
      #{configDistance,jdbcType=DECIMAL}, #{loadValidity,jdbcType=INTEGER}, #{abnormalAmount,jdbcType=DECIMAL}, 
      #{ifEmpty,jdbcType=INTEGER}, #{emptyTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{dispatchRemark,jdbcType=VARCHAR}, #{carrierPriceType,jdbcType=INTEGER}, #{carrierPrice,jdbcType=DECIMAL}, 
      #{printCount,jdbcType=INTEGER}, #{ifObjection,jdbcType=INTEGER}, #{outStatus,jdbcType=INTEGER}, 
      #{publishName,jdbcType=VARCHAR}, #{publishMobile,jdbcType=VARCHAR}, #{publishTime,jdbcType=TIMESTAMP}, 
      #{publishOrgCode,jdbcType=VARCHAR}, #{publishOrgName,jdbcType=VARCHAR}, #{ifUrgent,jdbcType=INTEGER}, 
      #{availableOnWeekends,jdbcType=INTEGER}, #{loadingUnloadingPart,jdbcType=INTEGER}, 
      #{loadingUnloadingCharge,jdbcType=DECIMAL}, #{recycleTaskType,jdbcType=INTEGER}, 
      #{projectLabel,jdbcType=VARCHAR}, #{ifWaitAuditVehicle,jdbcType=INTEGER}, #{correctStatus,jdbcType=INTEGER}, 
      #{stockInState,jdbcType=INTEGER}, #{carrierSettleStatementStatus,jdbcType=INTEGER}, 
      #{qrCodePicPath,jdbcType=VARCHAR}, #{deliveryMethod,jdbcType=INTEGER}, #{orderMode,jdbcType=INTEGER}, 
      #{vehicleLengthId,jdbcType=BIGINT}, #{vehicleLength,jdbcType=DECIMAL}, #{bargainingMode,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCarrierOrder">
    insert into t_carrier_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="dispatchOrderId != null">
        dispatch_order_id,
      </if>
      <if test="dispatchOrderCode != null">
        dispatch_order_code,
      </if>
      <if test="demandOrderId != null">
        demand_order_id,
      </if>
      <if test="demandOrderCode != null">
        demand_order_code,
      </if>
      <if test="carrierOrderCode != null">
        carrier_order_code,
      </if>
      <if test="customerOrderCode != null">
        customer_order_code,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="statusUpdateTime != null">
        status_update_time,
      </if>
      <if test="ifCancel != null">
        if_cancel,
      </if>
      <if test="cancelReason != null">
        cancel_reason,
      </if>
      <if test="cancelOperatorName != null">
        cancel_operator_name,
      </if>
      <if test="cancelTime != null">
        cancel_time,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerUserName != null">
        customer_user_name,
      </if>
      <if test="customerUserMobile != null">
        customer_user_mobile,
      </if>
      <if test="customerOrderSource != null">
        customer_order_source,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="dispatchUserId != null">
        dispatch_user_id,
      </if>
      <if test="dispatchUserName != null">
        dispatch_user_name,
      </if>
      <if test="dispatchTime != null">
        dispatch_time,
      </if>
      <if test="companyCarrierType != null">
        company_carrier_type,
      </if>
      <if test="companyCarrierId != null">
        company_carrier_id,
      </if>
      <if test="companyCarrierName != null">
        company_carrier_name,
      </if>
      <if test="carrierContactId != null">
        carrier_contact_id,
      </if>
      <if test="carrierContactName != null">
        carrier_contact_name,
      </if>
      <if test="carrierContactPhone != null">
        carrier_contact_phone,
      </if>
      <if test="companyCarrierLevel != null">
        company_carrier_level,
      </if>
      <if test="companyEntrustId != null">
        company_entrust_id,
      </if>
      <if test="companyEntrustName != null">
        company_entrust_name,
      </if>
      <if test="upstreamCustomer != null">
        upstream_customer,
      </if>
      <if test="loadTime != null">
        load_time,
      </if>
      <if test="unloadTime != null">
        unload_time,
      </if>
      <if test="signTime != null">
        sign_time,
      </if>
      <if test="expectAmount != null">
        expect_amount,
      </if>
      <if test="loadAmountExpect != null">
        load_amount_expect,
      </if>
      <if test="loadAmount != null">
        load_amount,
      </if>
      <if test="unloadAmountExpect != null">
        unload_amount_expect,
      </if>
      <if test="unloadAmount != null">
        unload_amount,
      </if>
      <if test="signAmount != null">
        sign_amount,
      </if>
      <if test="expectEntrustFreight != null">
        expect_entrust_freight,
      </if>
      <if test="expectEntrustFreightType != null">
        expect_entrust_freight_type,
      </if>
      <if test="entrustFreightType != null">
        entrust_freight_type,
      </if>
      <if test="entrustFreight != null">
        entrust_freight,
      </if>
      <if test="signFreightFee != null">
        sign_freight_fee,
      </if>
      <if test="dispatchFreightFeeType != null">
        dispatch_freight_fee_type,
      </if>
      <if test="dispatchFreightFee != null">
        dispatch_freight_fee,
      </if>
      <if test="adjustFee != null">
        adjust_fee,
      </if>
      <if test="markupFee != null">
        markup_fee,
      </if>
      <if test="goodsUnit != null">
        goods_unit,
      </if>
      <if test="demandOrderSource != null">
        demand_order_source,
      </if>
      <if test="demandOrderEntrustType != null">
        demand_order_entrust_type,
      </if>
      <if test="settlementTonnage != null">
        settlement_tonnage,
      </if>
      <if test="carrierSettlement != null">
        carrier_settlement,
      </if>
      <if test="expectMileage != null">
        expect_mileage,
      </if>
      <if test="configDistance != null">
        config_distance,
      </if>
      <if test="loadValidity != null">
        load_validity,
      </if>
      <if test="abnormalAmount != null">
        abnormal_amount,
      </if>
      <if test="ifEmpty != null">
        if_empty,
      </if>
      <if test="emptyTime != null">
        empty_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="dispatchRemark != null">
        dispatch_remark,
      </if>
      <if test="carrierPriceType != null">
        carrier_price_type,
      </if>
      <if test="carrierPrice != null">
        carrier_price,
      </if>
      <if test="printCount != null">
        print_count,
      </if>
      <if test="ifObjection != null">
        if_objection,
      </if>
      <if test="outStatus != null">
        out_status,
      </if>
      <if test="publishName != null">
        publish_name,
      </if>
      <if test="publishMobile != null">
        publish_mobile,
      </if>
      <if test="publishTime != null">
        publish_time,
      </if>
      <if test="publishOrgCode != null">
        publish_org_code,
      </if>
      <if test="publishOrgName != null">
        publish_org_name,
      </if>
      <if test="ifUrgent != null">
        if_urgent,
      </if>
      <if test="availableOnWeekends != null">
        available_on_weekends,
      </if>
      <if test="loadingUnloadingPart != null">
        loading_unloading_part,
      </if>
      <if test="loadingUnloadingCharge != null">
        loading_unloading_charge,
      </if>
      <if test="recycleTaskType != null">
        recycle_task_type,
      </if>
      <if test="projectLabel != null">
        project_label,
      </if>
      <if test="ifWaitAuditVehicle != null">
        if_wait_audit_vehicle,
      </if>
      <if test="correctStatus != null">
        correct_status,
      </if>
      <if test="stockInState != null">
        stock_in_state,
      </if>
      <if test="carrierSettleStatementStatus != null">
        carrier_settle_statement_status,
      </if>
      <if test="qrCodePicPath != null">
        qr_code_pic_path,
      </if>
      <if test="deliveryMethod != null">
        delivery_method,
      </if>
      <if test="orderMode != null">
        order_mode,
      </if>
      <if test="vehicleLengthId != null">
        vehicle_length_id,
      </if>
      <if test="vehicleLength != null">
        vehicle_length,
      </if>
      <if test="bargainingMode != null">
        bargaining_mode,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="dispatchOrderId != null">
        #{dispatchOrderId,jdbcType=BIGINT},
      </if>
      <if test="dispatchOrderCode != null">
        #{dispatchOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="demandOrderId != null">
        #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderCode != null">
        #{demandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="carrierOrderCode != null">
        #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderCode != null">
        #{customerOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="statusUpdateTime != null">
        #{statusUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ifCancel != null">
        #{ifCancel,jdbcType=INTEGER},
      </if>
      <if test="cancelReason != null">
        #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="cancelOperatorName != null">
        #{cancelOperatorName,jdbcType=VARCHAR},
      </if>
      <if test="cancelTime != null">
        #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerUserName != null">
        #{customerUserName,jdbcType=VARCHAR},
      </if>
      <if test="customerUserMobile != null">
        #{customerUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderSource != null">
        #{customerOrderSource,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="dispatchUserId != null">
        #{dispatchUserId,jdbcType=BIGINT},
      </if>
      <if test="dispatchUserName != null">
        #{dispatchUserName,jdbcType=VARCHAR},
      </if>
      <if test="dispatchTime != null">
        #{dispatchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyCarrierType != null">
        #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null">
        #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null">
        #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactId != null">
        #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactName != null">
        #{carrierContactName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactPhone != null">
        #{carrierContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="companyCarrierLevel != null">
        #{companyCarrierLevel,jdbcType=INTEGER},
      </if>
      <if test="companyEntrustId != null">
        #{companyEntrustId,jdbcType=BIGINT},
      </if>
      <if test="companyEntrustName != null">
        #{companyEntrustName,jdbcType=VARCHAR},
      </if>
      <if test="upstreamCustomer != null">
        #{upstreamCustomer,jdbcType=VARCHAR},
      </if>
      <if test="loadTime != null">
        #{loadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="unloadTime != null">
        #{unloadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="signTime != null">
        #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectAmount != null">
        #{expectAmount,jdbcType=DECIMAL},
      </if>
      <if test="loadAmountExpect != null">
        #{loadAmountExpect,jdbcType=DECIMAL},
      </if>
      <if test="loadAmount != null">
        #{loadAmount,jdbcType=DECIMAL},
      </if>
      <if test="unloadAmountExpect != null">
        #{unloadAmountExpect,jdbcType=DECIMAL},
      </if>
      <if test="unloadAmount != null">
        #{unloadAmount,jdbcType=DECIMAL},
      </if>
      <if test="signAmount != null">
        #{signAmount,jdbcType=DECIMAL},
      </if>
      <if test="expectEntrustFreight != null">
        #{expectEntrustFreight,jdbcType=DECIMAL},
      </if>
      <if test="expectEntrustFreightType != null">
        #{expectEntrustFreightType,jdbcType=INTEGER},
      </if>
      <if test="entrustFreightType != null">
        #{entrustFreightType,jdbcType=INTEGER},
      </if>
      <if test="entrustFreight != null">
        #{entrustFreight,jdbcType=DECIMAL},
      </if>
      <if test="signFreightFee != null">
        #{signFreightFee,jdbcType=DECIMAL},
      </if>
      <if test="dispatchFreightFeeType != null">
        #{dispatchFreightFeeType,jdbcType=INTEGER},
      </if>
      <if test="dispatchFreightFee != null">
        #{dispatchFreightFee,jdbcType=DECIMAL},
      </if>
      <if test="adjustFee != null">
        #{adjustFee,jdbcType=DECIMAL},
      </if>
      <if test="markupFee != null">
        #{markupFee,jdbcType=DECIMAL},
      </if>
      <if test="goodsUnit != null">
        #{goodsUnit,jdbcType=INTEGER},
      </if>
      <if test="demandOrderSource != null">
        #{demandOrderSource,jdbcType=INTEGER},
      </if>
      <if test="demandOrderEntrustType != null">
        #{demandOrderEntrustType,jdbcType=INTEGER},
      </if>
      <if test="settlementTonnage != null">
        #{settlementTonnage,jdbcType=INTEGER},
      </if>
      <if test="carrierSettlement != null">
        #{carrierSettlement,jdbcType=INTEGER},
      </if>
      <if test="expectMileage != null">
        #{expectMileage,jdbcType=DECIMAL},
      </if>
      <if test="configDistance != null">
        #{configDistance,jdbcType=DECIMAL},
      </if>
      <if test="loadValidity != null">
        #{loadValidity,jdbcType=INTEGER},
      </if>
      <if test="abnormalAmount != null">
        #{abnormalAmount,jdbcType=DECIMAL},
      </if>
      <if test="ifEmpty != null">
        #{ifEmpty,jdbcType=INTEGER},
      </if>
      <if test="emptyTime != null">
        #{emptyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="dispatchRemark != null">
        #{dispatchRemark,jdbcType=VARCHAR},
      </if>
      <if test="carrierPriceType != null">
        #{carrierPriceType,jdbcType=INTEGER},
      </if>
      <if test="carrierPrice != null">
        #{carrierPrice,jdbcType=DECIMAL},
      </if>
      <if test="printCount != null">
        #{printCount,jdbcType=INTEGER},
      </if>
      <if test="ifObjection != null">
        #{ifObjection,jdbcType=INTEGER},
      </if>
      <if test="outStatus != null">
        #{outStatus,jdbcType=INTEGER},
      </if>
      <if test="publishName != null">
        #{publishName,jdbcType=VARCHAR},
      </if>
      <if test="publishMobile != null">
        #{publishMobile,jdbcType=VARCHAR},
      </if>
      <if test="publishTime != null">
        #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="publishOrgCode != null">
        #{publishOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="publishOrgName != null">
        #{publishOrgName,jdbcType=VARCHAR},
      </if>
      <if test="ifUrgent != null">
        #{ifUrgent,jdbcType=INTEGER},
      </if>
      <if test="availableOnWeekends != null">
        #{availableOnWeekends,jdbcType=INTEGER},
      </if>
      <if test="loadingUnloadingPart != null">
        #{loadingUnloadingPart,jdbcType=INTEGER},
      </if>
      <if test="loadingUnloadingCharge != null">
        #{loadingUnloadingCharge,jdbcType=DECIMAL},
      </if>
      <if test="recycleTaskType != null">
        #{recycleTaskType,jdbcType=INTEGER},
      </if>
      <if test="projectLabel != null">
        #{projectLabel,jdbcType=VARCHAR},
      </if>
      <if test="ifWaitAuditVehicle != null">
        #{ifWaitAuditVehicle,jdbcType=INTEGER},
      </if>
      <if test="correctStatus != null">
        #{correctStatus,jdbcType=INTEGER},
      </if>
      <if test="stockInState != null">
        #{stockInState,jdbcType=INTEGER},
      </if>
      <if test="carrierSettleStatementStatus != null">
        #{carrierSettleStatementStatus,jdbcType=INTEGER},
      </if>
      <if test="qrCodePicPath != null">
        #{qrCodePicPath,jdbcType=VARCHAR},
      </if>
      <if test="deliveryMethod != null">
        #{deliveryMethod,jdbcType=INTEGER},
      </if>
      <if test="orderMode != null">
        #{orderMode,jdbcType=INTEGER},
      </if>
      <if test="vehicleLengthId != null">
        #{vehicleLengthId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLength != null">
        #{vehicleLength,jdbcType=DECIMAL},
      </if>
      <if test="bargainingMode != null">
        #{bargainingMode,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCarrierOrder">
    update t_carrier_order
    <set>
      <if test="dispatchOrderId != null">
        dispatch_order_id = #{dispatchOrderId,jdbcType=BIGINT},
      </if>
      <if test="dispatchOrderCode != null">
        dispatch_order_code = #{dispatchOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="demandOrderId != null">
        demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderCode != null">
        demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="carrierOrderCode != null">
        carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderCode != null">
        customer_order_code = #{customerOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="statusUpdateTime != null">
        status_update_time = #{statusUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ifCancel != null">
        if_cancel = #{ifCancel,jdbcType=INTEGER},
      </if>
      <if test="cancelReason != null">
        cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="cancelOperatorName != null">
        cancel_operator_name = #{cancelOperatorName,jdbcType=VARCHAR},
      </if>
      <if test="cancelTime != null">
        cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerUserName != null">
        customer_user_name = #{customerUserName,jdbcType=VARCHAR},
      </if>
      <if test="customerUserMobile != null">
        customer_user_mobile = #{customerUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderSource != null">
        customer_order_source = #{customerOrderSource,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="dispatchUserId != null">
        dispatch_user_id = #{dispatchUserId,jdbcType=BIGINT},
      </if>
      <if test="dispatchUserName != null">
        dispatch_user_name = #{dispatchUserName,jdbcType=VARCHAR},
      </if>
      <if test="dispatchTime != null">
        dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyCarrierType != null">
        company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null">
        company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null">
        company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactId != null">
        carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactName != null">
        carrier_contact_name = #{carrierContactName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactPhone != null">
        carrier_contact_phone = #{carrierContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="companyCarrierLevel != null">
        company_carrier_level = #{companyCarrierLevel,jdbcType=INTEGER},
      </if>
      <if test="companyEntrustId != null">
        company_entrust_id = #{companyEntrustId,jdbcType=BIGINT},
      </if>
      <if test="companyEntrustName != null">
        company_entrust_name = #{companyEntrustName,jdbcType=VARCHAR},
      </if>
      <if test="upstreamCustomer != null">
        upstream_customer = #{upstreamCustomer,jdbcType=VARCHAR},
      </if>
      <if test="loadTime != null">
        load_time = #{loadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="unloadTime != null">
        unload_time = #{unloadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="signTime != null">
        sign_time = #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectAmount != null">
        expect_amount = #{expectAmount,jdbcType=DECIMAL},
      </if>
      <if test="loadAmountExpect != null">
        load_amount_expect = #{loadAmountExpect,jdbcType=DECIMAL},
      </if>
      <if test="loadAmount != null">
        load_amount = #{loadAmount,jdbcType=DECIMAL},
      </if>
      <if test="unloadAmountExpect != null">
        unload_amount_expect = #{unloadAmountExpect,jdbcType=DECIMAL},
      </if>
      <if test="unloadAmount != null">
        unload_amount = #{unloadAmount,jdbcType=DECIMAL},
      </if>
      <if test="signAmount != null">
        sign_amount = #{signAmount,jdbcType=DECIMAL},
      </if>
      <if test="expectEntrustFreight != null">
        expect_entrust_freight = #{expectEntrustFreight,jdbcType=DECIMAL},
      </if>
      <if test="expectEntrustFreightType != null">
        expect_entrust_freight_type = #{expectEntrustFreightType,jdbcType=INTEGER},
      </if>
      <if test="entrustFreightType != null">
        entrust_freight_type = #{entrustFreightType,jdbcType=INTEGER},
      </if>
      <if test="entrustFreight != null">
        entrust_freight = #{entrustFreight,jdbcType=DECIMAL},
      </if>
      <if test="signFreightFee != null">
        sign_freight_fee = #{signFreightFee,jdbcType=DECIMAL},
      </if>
      <if test="dispatchFreightFeeType != null">
        dispatch_freight_fee_type = #{dispatchFreightFeeType,jdbcType=INTEGER},
      </if>
      <if test="dispatchFreightFee != null">
        dispatch_freight_fee = #{dispatchFreightFee,jdbcType=DECIMAL},
      </if>
      <if test="adjustFee != null">
        adjust_fee = #{adjustFee,jdbcType=DECIMAL},
      </if>
      <if test="markupFee != null">
        markup_fee = #{markupFee,jdbcType=DECIMAL},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=INTEGER},
      </if>
      <if test="demandOrderSource != null">
        demand_order_source = #{demandOrderSource,jdbcType=INTEGER},
      </if>
      <if test="demandOrderEntrustType != null">
        demand_order_entrust_type = #{demandOrderEntrustType,jdbcType=INTEGER},
      </if>
      <if test="settlementTonnage != null">
        settlement_tonnage = #{settlementTonnage,jdbcType=INTEGER},
      </if>
      <if test="carrierSettlement != null">
        carrier_settlement = #{carrierSettlement,jdbcType=INTEGER},
      </if>
      <if test="expectMileage != null">
        expect_mileage = #{expectMileage,jdbcType=DECIMAL},
      </if>
      <if test="configDistance != null">
        config_distance = #{configDistance,jdbcType=DECIMAL},
      </if>
      <if test="loadValidity != null">
        load_validity = #{loadValidity,jdbcType=INTEGER},
      </if>
      <if test="abnormalAmount != null">
        abnormal_amount = #{abnormalAmount,jdbcType=DECIMAL},
      </if>
      <if test="ifEmpty != null">
        if_empty = #{ifEmpty,jdbcType=INTEGER},
      </if>
      <if test="emptyTime != null">
        empty_time = #{emptyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="dispatchRemark != null">
        dispatch_remark = #{dispatchRemark,jdbcType=VARCHAR},
      </if>
      <if test="carrierPriceType != null">
        carrier_price_type = #{carrierPriceType,jdbcType=INTEGER},
      </if>
      <if test="carrierPrice != null">
        carrier_price = #{carrierPrice,jdbcType=DECIMAL},
      </if>
      <if test="printCount != null">
        print_count = #{printCount,jdbcType=INTEGER},
      </if>
      <if test="ifObjection != null">
        if_objection = #{ifObjection,jdbcType=INTEGER},
      </if>
      <if test="outStatus != null">
        out_status = #{outStatus,jdbcType=INTEGER},
      </if>
      <if test="publishName != null">
        publish_name = #{publishName,jdbcType=VARCHAR},
      </if>
      <if test="publishMobile != null">
        publish_mobile = #{publishMobile,jdbcType=VARCHAR},
      </if>
      <if test="publishTime != null">
        publish_time = #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="publishOrgCode != null">
        publish_org_code = #{publishOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="publishOrgName != null">
        publish_org_name = #{publishOrgName,jdbcType=VARCHAR},
      </if>
      <if test="ifUrgent != null">
        if_urgent = #{ifUrgent,jdbcType=INTEGER},
      </if>
      <if test="availableOnWeekends != null">
        available_on_weekends = #{availableOnWeekends,jdbcType=INTEGER},
      </if>
      <if test="loadingUnloadingPart != null">
        loading_unloading_part = #{loadingUnloadingPart,jdbcType=INTEGER},
      </if>
      <if test="loadingUnloadingCharge != null">
        loading_unloading_charge = #{loadingUnloadingCharge,jdbcType=DECIMAL},
      </if>
      <if test="recycleTaskType != null">
        recycle_task_type = #{recycleTaskType,jdbcType=INTEGER},
      </if>
      <if test="projectLabel != null">
        project_label = #{projectLabel,jdbcType=VARCHAR},
      </if>
      <if test="ifWaitAuditVehicle != null">
        if_wait_audit_vehicle = #{ifWaitAuditVehicle,jdbcType=INTEGER},
      </if>
      <if test="correctStatus != null">
        correct_status = #{correctStatus,jdbcType=INTEGER},
      </if>
      <if test="stockInState != null">
        stock_in_state = #{stockInState,jdbcType=INTEGER},
      </if>
      <if test="carrierSettleStatementStatus != null">
        carrier_settle_statement_status = #{carrierSettleStatementStatus,jdbcType=INTEGER},
      </if>
      <if test="qrCodePicPath != null">
        qr_code_pic_path = #{qrCodePicPath,jdbcType=VARCHAR},
      </if>
      <if test="deliveryMethod != null">
        delivery_method = #{deliveryMethod,jdbcType=INTEGER},
      </if>
      <if test="orderMode != null">
        order_mode = #{orderMode,jdbcType=INTEGER},
      </if>
      <if test="vehicleLengthId != null">
        vehicle_length_id = #{vehicleLengthId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLength != null">
        vehicle_length = #{vehicleLength,jdbcType=DECIMAL},
      </if>
      <if test="bargainingMode != null">
        bargaining_mode = #{bargainingMode,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCarrierOrder">
    update t_carrier_order
    set dispatch_order_id = #{dispatchOrderId,jdbcType=BIGINT},
      dispatch_order_code = #{dispatchOrderCode,jdbcType=VARCHAR},
      demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
      carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      customer_order_code = #{customerOrderCode,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      status_update_time = #{statusUpdateTime,jdbcType=TIMESTAMP},
      if_cancel = #{ifCancel,jdbcType=INTEGER},
      cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      cancel_operator_name = #{cancelOperatorName,jdbcType=VARCHAR},
      cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      business_type = #{businessType,jdbcType=INTEGER},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_user_name = #{customerUserName,jdbcType=VARCHAR},
      customer_user_mobile = #{customerUserMobile,jdbcType=VARCHAR},
      customer_order_source = #{customerOrderSource,jdbcType=INTEGER},
      source = #{source,jdbcType=INTEGER},
      dispatch_user_id = #{dispatchUserId,jdbcType=BIGINT},
      dispatch_user_name = #{dispatchUserName,jdbcType=VARCHAR},
      dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
      company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
      company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
      carrier_contact_name = #{carrierContactName,jdbcType=VARCHAR},
      carrier_contact_phone = #{carrierContactPhone,jdbcType=VARCHAR},
      company_carrier_level = #{companyCarrierLevel,jdbcType=INTEGER},
      company_entrust_id = #{companyEntrustId,jdbcType=BIGINT},
      company_entrust_name = #{companyEntrustName,jdbcType=VARCHAR},
      upstream_customer = #{upstreamCustomer,jdbcType=VARCHAR},
      load_time = #{loadTime,jdbcType=TIMESTAMP},
      unload_time = #{unloadTime,jdbcType=TIMESTAMP},
      sign_time = #{signTime,jdbcType=TIMESTAMP},
      expect_amount = #{expectAmount,jdbcType=DECIMAL},
      load_amount_expect = #{loadAmountExpect,jdbcType=DECIMAL},
      load_amount = #{loadAmount,jdbcType=DECIMAL},
      unload_amount_expect = #{unloadAmountExpect,jdbcType=DECIMAL},
      unload_amount = #{unloadAmount,jdbcType=DECIMAL},
      sign_amount = #{signAmount,jdbcType=DECIMAL},
      expect_entrust_freight = #{expectEntrustFreight,jdbcType=DECIMAL},
      expect_entrust_freight_type = #{expectEntrustFreightType,jdbcType=INTEGER},
      entrust_freight_type = #{entrustFreightType,jdbcType=INTEGER},
      entrust_freight = #{entrustFreight,jdbcType=DECIMAL},
      sign_freight_fee = #{signFreightFee,jdbcType=DECIMAL},
      dispatch_freight_fee_type = #{dispatchFreightFeeType,jdbcType=INTEGER},
      dispatch_freight_fee = #{dispatchFreightFee,jdbcType=DECIMAL},
      adjust_fee = #{adjustFee,jdbcType=DECIMAL},
      markup_fee = #{markupFee,jdbcType=DECIMAL},
      goods_unit = #{goodsUnit,jdbcType=INTEGER},
      demand_order_source = #{demandOrderSource,jdbcType=INTEGER},
      demand_order_entrust_type = #{demandOrderEntrustType,jdbcType=INTEGER},
      settlement_tonnage = #{settlementTonnage,jdbcType=INTEGER},
      carrier_settlement = #{carrierSettlement,jdbcType=INTEGER},
      expect_mileage = #{expectMileage,jdbcType=DECIMAL},
      config_distance = #{configDistance,jdbcType=DECIMAL},
      load_validity = #{loadValidity,jdbcType=INTEGER},
      abnormal_amount = #{abnormalAmount,jdbcType=DECIMAL},
      if_empty = #{ifEmpty,jdbcType=INTEGER},
      empty_time = #{emptyTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      dispatch_remark = #{dispatchRemark,jdbcType=VARCHAR},
      carrier_price_type = #{carrierPriceType,jdbcType=INTEGER},
      carrier_price = #{carrierPrice,jdbcType=DECIMAL},
      print_count = #{printCount,jdbcType=INTEGER},
      if_objection = #{ifObjection,jdbcType=INTEGER},
      out_status = #{outStatus,jdbcType=INTEGER},
      publish_name = #{publishName,jdbcType=VARCHAR},
      publish_mobile = #{publishMobile,jdbcType=VARCHAR},
      publish_time = #{publishTime,jdbcType=TIMESTAMP},
      publish_org_code = #{publishOrgCode,jdbcType=VARCHAR},
      publish_org_name = #{publishOrgName,jdbcType=VARCHAR},
      if_urgent = #{ifUrgent,jdbcType=INTEGER},
      available_on_weekends = #{availableOnWeekends,jdbcType=INTEGER},
      loading_unloading_part = #{loadingUnloadingPart,jdbcType=INTEGER},
      loading_unloading_charge = #{loadingUnloadingCharge,jdbcType=DECIMAL},
      recycle_task_type = #{recycleTaskType,jdbcType=INTEGER},
      project_label = #{projectLabel,jdbcType=VARCHAR},
      if_wait_audit_vehicle = #{ifWaitAuditVehicle,jdbcType=INTEGER},
      correct_status = #{correctStatus,jdbcType=INTEGER},
      stock_in_state = #{stockInState,jdbcType=INTEGER},
      carrier_settle_statement_status = #{carrierSettleStatementStatus,jdbcType=INTEGER},
      qr_code_pic_path = #{qrCodePicPath,jdbcType=VARCHAR},
      delivery_method = #{deliveryMethod,jdbcType=INTEGER},
      order_mode = #{orderMode,jdbcType=INTEGER},
      vehicle_length_id = #{vehicleLengthId,jdbcType=BIGINT},
      vehicle_length = #{vehicleLength,jdbcType=DECIMAL},
      bargaining_mode = #{bargainingMode,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>