package com.logistics.management.webapi.api.feign.attendance.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 撤销考勤申请请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/15
 */
@Data
public class CancelAttendanceChangeApplyRequestDto {

	//请假申请ID
	@ApiModelProperty(value = "考勤变更申请ID", required = true)
	@NotBlank(message = "请选择要撤销的记录")
	private String attendanceChangeApplyId;

	//撤销说明
	@ApiModelProperty(value = "撤销说明,1-100个字符", required = true)
	@Length(min = 1, max = 100, message = "请填写撤销说明,1-100字")
	private String remark;
}
