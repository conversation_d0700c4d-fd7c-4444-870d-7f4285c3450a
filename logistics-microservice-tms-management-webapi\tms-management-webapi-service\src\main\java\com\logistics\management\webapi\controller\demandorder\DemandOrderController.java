package com.logistics.management.webapi.controller.demandorder;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.enums.PriceTypeEnum;
import com.logistics.management.webapi.base.utils.ApiParamsValidatorUtil;
import com.logistics.management.webapi.base.utils.ExportUtils;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.demandorder.DemandOrderClient;
import com.logistics.management.webapi.client.demandorder.SinopecDemandOrderClient;
import com.logistics.management.webapi.client.demandorder.request.*;
import com.logistics.management.webapi.client.demandorder.response.*;
import com.logistics.management.webapi.controller.demandorder.mapping.*;
import com.logistics.management.webapi.controller.demandorder.request.*;
import com.logistics.management.webapi.controller.demandorder.response.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2024/3/27 9:17
 */
@Api(value = "需求单管理", tags = "需求单管理")
@RestController
public class DemandOrderController {

    @Resource
    private DemandOrderClient demandOrderClient;
    @Resource
    private SinopecDemandOrderClient sinopecDemandOrderClient;

    /**
     * 获取需求单列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "获取需求单列表 v1.1.9")
    @PostMapping(value = "/api/demandOrder/searchList")
    public Result<PageInfo<DemandOrderResponseDto>> searchList(@RequestBody DemandOrderSearchRequestDto requestDto) {
        DemandOrderSearchRequestModel mapper = MapperUtils.mapper(requestDto, DemandOrderSearchRequestModel.class);
        Result<PageInfo<DemandOrderResponseModel>> pageInfoResult = demandOrderClient.searchList(mapper);
        pageInfoResult.throwException();
        PageInfo pageInfo = pageInfoResult.getData();
        if (pageInfoResult.getData() != null && ListUtils.isNotEmpty(pageInfoResult.getData().getList())) {
            List<DemandOrderResponseDto> demandOrderListResponseDtos = MapperUtils.mapper(pageInfoResult.getData().getList(), DemandOrderResponseDto.class, new ListDemandOrderMapping());
            pageInfo.setList(demandOrderListResponseDtos);
        }
        return Result.success(pageInfo);
    }

    /**
     * 需求单列表统计
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "需求单列表统计v1.1.9")
    @PostMapping(value = "/api/demandOrder/searchListStatistics")
    public Result<SearchListStatisticsResponseDto> searchListStatistics(@RequestBody DemandOrderSearchRequestDto requestDto) {
        Result<SearchListStatisticsResponseModel> searchListStatisticsResponseModelResult = demandOrderClient.searchListStatistics(MapperUtils.mapper(requestDto,DemandOrderSearchRequestModel.class));
        searchListStatisticsResponseModelResult.throwException();
        return Result.success(MapperUtils.mapper(searchListStatisticsResponseModelResult.getData(),SearchListStatisticsResponseDto.class));
    }

    /**
     * 取消需求单
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "取消需求单")
    @PostMapping(value = "/api/demandOrder/cancelReason")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> cancelDemandOrder(@RequestBody @Valid DemandOrderCancelRequestDto requestDto) {
        return demandOrderClient.cancelDemandOrder(MapperUtils.mapper(requestDto, DemandOrderCancelRequestModel.class));
    }

    /**
     * 获取需求单详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "获取需求单详情v1.1.9")
    @PostMapping(value = "/api/demandOrder/getDetail")
    public Result<DemandOrderDetailResponseDto> getDetail(@RequestBody @Valid DemandOrderDetailRequestDto requestDto) {
        DemandOrderDetailRequestModel demandOrderDetailRequestModel = MapperUtils.mapper(requestDto, DemandOrderDetailRequestModel.class);
        Result<DemandOrderDetailResponseModel> detail = demandOrderClient.getDetail(demandOrderDetailRequestModel);
        detail.throwException();
        return Result.success(MapperUtils.mapper(detail.getData(), DemandOrderDetailResponseDto.class, new DemandOrderDetailMapping())) ;
    }

    /**
     * 需求单日志
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "需求单日志")
    @PostMapping(value = "/api/demandOrder/getDemandOrderLogs")
    public Result<List<GetDemandOrderLogsResponseDto>> getDemandOrderLogs(@RequestBody @Valid DemandOrderDetailRequestDto requestDto) {
        Result<List<GetDemandOrderLogsResponseModel>> listResult = demandOrderClient.getDemandOrderLogs(MapperUtils.mapper(requestDto,DemandOrderDetailRequestModel.class));
        if (!listResult.isSuccess()){
            throw new BizException(listResult.getErrcode(),listResult.getErrmsg());
        }
        List<GetDemandOrderLogsResponseDto> list = new ArrayList<>();
        if (ListUtils.isNotEmpty(listResult.getData())){
            list = MapperUtils.mapper(listResult.getData(),GetDemandOrderLogsResponseDto.class, new DemandOrderLogsMapping());
        }
        return Result.success(list);
    }

    /**
     * 导出需求单
     * @param requestDto
     * @param response
     * @return
     */
    @ApiOperation(value = "导出需求单v1.2.3")
    @PostMapping(value = "/api/demandOrder/exportDemandOrder")
    public void exportDemandOrder(@RequestBody DemandOrderSearchRequestDto requestDto, HttpServletResponse response) {
        Result<List<DemandOrderResponseModel>> listResult = demandOrderClient.exportDemandOrder(MapperUtils.mapper(requestDto,DemandOrderSearchRequestModel.class));
        if (!listResult.isSuccess()){
            throw new BizException(listResult.getErrcode(),listResult.getErrmsg());
        }
        List<DemandOrderResponseDto> list = MapperUtils.mapper(listResult.getData(),DemandOrderResponseDto.class, new ListDemandOrderMapping());
        String fileName = "需求单列表" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM);
        ExportUtils.exportByYeloExcel(response, list, DemandOrderResponseDto.class, fileName);
    }

    /**
     * 委托单发布
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "委托单发布v1.1.9")
    @PostMapping(value = "/api/demandOrder/saveDemandOrder")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> saveDemandOrder(@RequestBody @Valid SaveDemandOrderRequestDto requestDto) {
        //判断货物单位是否都相同
        List<String> goodsUnitList = requestDto.getDemandOrderGoodsList().stream().map(SaveDemandOrderGoodsRequestDto::getGoodsUnit).distinct().collect(Collectors.toList());
        if (ListUtils.isEmpty(goodsUnitList) || goodsUnitList.size() > CommonConstant.INTEGER_ONE) {
            throw new BizException(ManagementWebApiExceptionEnum.DEMAND_ORDER_GOODS_UNIT_DIFFERENT);
        }
        //判断货物是否在指定范围内
        String goodsUnit = goodsUnitList.get(0);
        if (!goodsUnit.equals(GoodsUnitEnum.BY_PACKAGE.getKey().toString()) &&
                !goodsUnit.equals(GoodsUnitEnum.BY_WEIGHT.getKey().toString()) &&
                !goodsUnit.equals(GoodsUnitEnum.BY_PIECE.getKey().toString())) {
            throw new BizException(ManagementWebApiExceptionEnum.DEMAND_ORDER_GOODS_UNIT_DIFFERENT);
        }
        //发货人联系方式校验
        if (StringUtils.isNotBlank(requestDto.getLoadContactMobile())) {
            if (!FrequentMethodUtils.checkReg(requestDto.getLoadContactMobile(), "(^\\d{11}$)|(^(0[0-9]{2,3}-)?([2-9][0-9]{6,7})$)")) {
                throw new BizException(ManagementWebApiExceptionEnum.LOAD_CONTACT_MOBILE_CHECK_FAIL);
            }
        }

        //校验货主价格
        if (requestDto.getContractPriceType() != null && requestDto.getContractPrice() != null) {
            //校验货主价格是否正确
            if (PriceTypeEnum.UNIT_PRICE.getKey().toString().equals(requestDto.getContractPriceType())) {
                //验证单价
                if (!ApiParamsValidatorUtil.verifyUnitPrice(requestDto.getContractPrice())) {
                    throw new BizException(ManagementWebApiExceptionEnum.CARRIER_UNIT_PRICE_ERROR);
                }
            } else if (PriceTypeEnum.FIXED_PRICE.getKey().toString().equals(requestDto.getContractPriceType())) {
                //验证一口价
                if (!ApiParamsValidatorUtil.verifyFixedPrice(requestDto.getContractPrice())) {
                    throw new BizException(ManagementWebApiExceptionEnum.CARRIER_FIXED_PRICE_ERROR);
                }
            } else {
                //所选价格类型不正确
                throw new BizException(ManagementWebApiExceptionEnum.CARRIER_PRICE_TYPE_ERROR);
            }
        }

        //校验是否填写车主价格(选择我司可为空)
        if (CommonConstant.TWO.equals(requestDto.getIsOurCompany())) {//其他车主
            //车主ID校验
            if (StringUtils.isBlank(requestDto.getCompanyCarrierId())) {
                throw new BizException(ManagementWebApiExceptionEnum.COMPANY_CARRIER_ID_IS_NULL);
            }
            //车主价格类型校验
            if (StringUtils.isBlank(requestDto.getCarrierPriceType())) {
                throw new BizException(ManagementWebApiExceptionEnum.FREIGHT_FEE_TYPE_ERROR);
            }
            //车主价格校验
            if (StringUtils.isBlank(requestDto.getCarrierPrice())) {
                throw new BizException(ManagementWebApiExceptionEnum.FREIGHT_FEE_IS_NULL);
            }

            //校验车主价格是否正确
            if (PriceTypeEnum.UNIT_PRICE.getKey().toString().equals(requestDto.getCarrierPriceType())) {
                //验证单价
                if (!ApiParamsValidatorUtil.verifyUnitPrice(requestDto.getCarrierPrice())) {
                    throw new BizException(ManagementWebApiExceptionEnum.CARRIER_UNIT_PRICE_ERROR);
                }
            } else if (PriceTypeEnum.FIXED_PRICE.getKey().toString().equals(requestDto.getCarrierPriceType())) {
                //验证一口价
                if (!ApiParamsValidatorUtil.verifyFixedPrice(requestDto.getCarrierPrice())) {
                    throw new BizException(ManagementWebApiExceptionEnum.CARRIER_FIXED_PRICE_ERROR);
                }
            } else {
                //所选价格类型不正确
                throw new BizException(ManagementWebApiExceptionEnum.CARRIER_PRICE_TYPE_ERROR);
            }
        }else {
            //车主价格置空
            requestDto.setCompanyCarrierId(null);
            requestDto.setCarrierPriceType(null);
            requestDto.setCarrierPrice(null);
        }

        return demandOrderClient.saveDemandOrder(MapperUtils.mapperNoDefault(requestDto, SaveDemandOrderRequestModel.class));
    }

    /**
     * 复制发布详情接口
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "复制发布详情接口")
    @PostMapping(value = "/api/entrustDemandOrder/getPublishDemandOrderDetail")
    public Result<GetPublishDemandOrderDetailResponseDto> getPublishDemandOrderDetail(@RequestBody @Valid DemandOrderIdRequestDto requestDto) {
        Result<GetPublishDemandOrderDetailResponseModel> getPublishDemandOrderDetailResponseModelResult = demandOrderClient.getPublishDemandOrderDetail(MapperUtils.mapper(requestDto, DemandOrderIdRequestModel.class));
        getPublishDemandOrderDetailResponseModelResult.throwException();
        return Result.success(MapperUtils.mapper(getPublishDemandOrderDetailResponseModelResult.getData(),GetPublishDemandOrderDetailResponseDto.class,new GetPublishDemandOrderDetailMapping()));
    }

    /**
     * 确认同步网络货运平台
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "确认同步网络货运平台v1.0.0")
    @PostMapping(value = "/api/demandOrder/confirmSynNetworkFreight")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result confirmSynNetworkFreight(@RequestBody @Valid DemandOrderIdListRequestDto requestDto) {
        throw new BizException("网络货运已下线,无法同步");
    }

    /**
     * 接参数直接返参数-前端解决重复调用无id的情况
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "接参数直接返参数-前端解决重复调用无id的情况")
    @PostMapping(value = "/api/demandOrder/getRequestParam")
    public Result<DemandOrderParamResponseDto> getRequestParam(@RequestBody DemandOrderParamRequestDto requestDto) {
        return Result.success(MapperUtils.mapper(requestDto, DemandOrderParamResponseDto.class));
    }

    /**
     * 获取需求单单个发布详情(中石化)
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "获取需求单单个发布详情(中石化) V1.1.2")
    @PostMapping(value = "/api/demandOrder/publishSinopecDetail")
    public Result<PublishSinopecResponseDto> publishSinopecDetail(@RequestBody @Valid PublishSinopecDetailRequestDto requestDto) {
        Result<PublishSinopecResponseModel> result = sinopecDemandOrderClient.publishSinopecDetail(MapperUtils.mapper(requestDto, PublishSinopecDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), PublishSinopecResponseDto.class, new PublishSinopecDetailMapping()));
    }

    /**
     * 获取需求单批量发布详情(中石化)
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "获取需求单批量发布详情(中石化) V1.1.2")
    @PostMapping(value = "/api/demandOrder/batchPublishSinopecDetail")
    public Result<BatchPublishSinopecResponseDto> batchPublishSinopecDetail(@RequestBody @Valid BatchPublishSinopecDetailRequestDto requestDto) {
        Result<BatchPublishSinopecResponseModel> result = sinopecDemandOrderClient.batchPublishSinopecDetail(MapperUtils.mapper(requestDto, BatchPublishSinopecDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), BatchPublishSinopecResponseDto.class, new BatchPublishSinopecDetailMapping()));
    }

    /**
     * 需求单-批量发布(中石化)
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "需求单-批量发布(中石化) v1.1.9")
    @PostMapping(value = "/api/demandOrder/batchPublishSinopecDemandOrder")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> batchPublishSinopecDemandOrder(@RequestBody @Valid BatchPublishSinopecDemandRequestDto requestDto) {
        //校验是否填写车主价格(选择我司可为空)
        if (CommonConstant.TWO.equals(requestDto.getIsOurCompany())) {//其他车主
            //车主ID校验
            if (StringUtils.isBlank(requestDto.getCompanyCarrierId())) {
                throw new BizException(ManagementWebApiExceptionEnum.COMPANY_CARRIER_ID_IS_NULL);
            }
            //验证单价
            if (!ApiParamsValidatorUtil.verifyUnitPrice(requestDto.getCarrierPrice())) {
                throw new BizException(ManagementWebApiExceptionEnum.CARRIER_UNIT_PRICE_ERROR);
            }
        }else{//我司
            requestDto.setCompanyCarrierId(null);
            requestDto.setCarrierPrice(null);
        }
        return sinopecDemandOrderClient.batchPublishSinopecDemandOrder(MapperUtils.mapper(requestDto, BatchPublishSinopecDemandRequestModel.class));
    }

    /**
     * 需求单-单个发布(中石化)
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "需求单-单个发布(中石化) v1.1.9")
    @PostMapping(value = "/api/demandOrder/publishSinopecDemandOrder")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> publishSinopecDemandOrder(@RequestBody @Valid PublishSinopecDemandRequestDto requestDto) {
        //校验货主价格
        if (PriceTypeEnum.UNIT_PRICE.getKey().toString().equals(requestDto.getContractPriceType())) {
            //验证单价
            if (!ApiParamsValidatorUtil.verifyUnitPrice(requestDto.getContractPrice())) {
                throw new BizException(ManagementWebApiExceptionEnum.CARRIER_UNIT_PRICE_ERROR);
            }
        } else if (PriceTypeEnum.FIXED_PRICE.getKey().toString().equals(requestDto.getContractPriceType())) {
            //验证一口价
            if (!ApiParamsValidatorUtil.verifyFixedPrice(requestDto.getContractPrice())) {
                throw new BizException(ManagementWebApiExceptionEnum.CARRIER_FIXED_PRICE_ERROR);
            }
        } else {
            //所选价格类型不正确
            throw new BizException(ManagementWebApiExceptionEnum.CARRIER_PRICE_TYPE_ERROR);
        }

        //校验是否填写车主价格(选择我司可为空)
        if (CommonConstant.TWO.equals(requestDto.getIsOurCompany())) {//其他车主
            //车主ID校验
            if (StringUtils.isBlank(requestDto.getCompanyCarrierId())) {
                throw new BizException(ManagementWebApiExceptionEnum.COMPANY_CARRIER_ID_IS_NULL);
            }
            //校验车主价格是否正确
            if (PriceTypeEnum.UNIT_PRICE.getKey().toString().equals(requestDto.getCarrierPriceType())) {
                //验证单价
                if (!ApiParamsValidatorUtil.verifyUnitPrice(requestDto.getCarrierPrice())) {
                    throw new BizException(ManagementWebApiExceptionEnum.CARRIER_UNIT_PRICE_ERROR);
                }
            } else if (PriceTypeEnum.FIXED_PRICE.getKey().toString().equals(requestDto.getCarrierPriceType())) {
                //验证一口价
                if (!ApiParamsValidatorUtil.verifyFixedPrice(requestDto.getCarrierPrice())) {
                    throw new BizException(ManagementWebApiExceptionEnum.CARRIER_FIXED_PRICE_ERROR);
                }
            } else {
                //所选价格类型不正确
                throw new BizException(ManagementWebApiExceptionEnum.CARRIER_PRICE_TYPE_ERROR);
            }
        }else{//我司
            requestDto.setCompanyCarrierId(null);
            requestDto.setCarrierPriceType(null);
            requestDto.setCarrierPrice(null);
        }
        return sinopecDemandOrderClient.publishSinopecDemandOrder(MapperUtils.mapper(requestDto, PublishSinopecDemandRequestModel.class));
    }


    /**
     * 中石化需求单取消,可批量(中石化)
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "取消需求单(中石化),可批量 V1.1.2")
    @PostMapping(value = "/api/demandOrder/cancelSinopecDemandOrder")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> cancelSinopecDemandOrder(@RequestBody @Valid SinopecDemandOrderCancelRequestDto requestDto) {
        return sinopecDemandOrderClient.cancelSinopecDemandOrder(MapperUtils.mapper(requestDto, SinopecDemandOrderCancelRequestModel.class));
    }

    /**
     * 中石化需求单上报异常详情查询
     *
     * @param requestDto 需求单id
     * @return 需求单详情列表
     */
    @ApiOperation(value = "中石化需求单上报异常详情查询 V1.1.6")
    @PostMapping(value = "/api/demandOrder/sinopecReportAbnormalDetail")
    public Result<SinopecReportAbnormalDetailResponseDto> sinopecReportAbnormalDetail(@RequestBody @Valid SinopecReportAbnormalDetailRequestDto requestDto) {
        Result<SinopecReportAbnormalDetailResponseModel> result = sinopecDemandOrderClient.sinopecReportAbnormalDetail(MapperUtils.mapper(requestDto, SinopecReportAbnormalDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SinopecReportAbnormalDetailResponseDto.class,new SinopecReportAbnormalDetailMapping()));
    }

    /**
     * 中石化需求单异常上报
     *
     * @param requestDto 异常上报信息
     * @return 操作结果
     */
    @ApiOperation(value = "中石化需求单异常上报 V1.1.6")
    @PostMapping(value = "/api/demandOrder/saveSinopecReportAbnormal")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> saveSinopecReportAbnormal(@RequestBody @Valid  SaveSinopecReportAbnormalRequestDto requestDto) {
        return sinopecDemandOrderClient.saveSinopecReportAbnormal(MapperUtils.mapper(requestDto, SaveSinopecReportAbnormalRequestModel.class));
    }

    /**
     * 修改车主
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "需求单修改车主v1.1.9")
    @PostMapping(value = "/api/demandOrder/modifyCarrier")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> modifyCarrier(@RequestBody @Valid ModifyCarrierRequestDto requestDto) {
        //校验是否填写车主价格(选择我司可为空)
        if (CommonConstant.TWO.equals(requestDto.getIsOurCompany())) {//其他车主
            //车主ID校验
            if (StringUtils.isBlank(requestDto.getCompanyCarrierId())) {
                throw new BizException(ManagementWebApiExceptionEnum.COMPANY_CARRIER_ID_IS_NULL);
            }

            if (StringUtils.isNotBlank(requestDto.getCarrierPriceType()) || StringUtils.isNotBlank(requestDto.getCarrierPrice())) {
                //校验车主价格是否正确
                if (PriceTypeEnum.UNIT_PRICE.getKey().toString().equals(requestDto.getCarrierPriceType())) {
                    //验证单价
                    if (!ApiParamsValidatorUtil.verifyUnitPrice(requestDto.getCarrierPrice())) {
                        throw new BizException(ManagementWebApiExceptionEnum.CARRIER_UNIT_PRICE_ERROR);
                    }
                } else if (PriceTypeEnum.FIXED_PRICE.getKey().toString().equals(requestDto.getCarrierPriceType())) {
                    //验证一口价
                    if (!ApiParamsValidatorUtil.verifyFixedPrice(requestDto.getCarrierPrice())) {
                        throw new BizException(ManagementWebApiExceptionEnum.CARRIER_FIXED_PRICE_ERROR);
                    }
                } else {
                    //所选价格类型不正确
                    throw new BizException(ManagementWebApiExceptionEnum.CARRIER_PRICE_TYPE_ERROR);
                }
            }
        }else {
            //车主价格置空
            requestDto.setCompanyCarrierId(null);
            requestDto.setCarrierPriceType(null);
            requestDto.setCarrierPrice(null);
        }
        return demandOrderClient.modifyCarrier(MapperUtils.mapper(requestDto,ModifyCarrierRequestModel.class));
    }

    /**
     * 修改车主详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "需求单修改车主详情查询v1.1.9")
    @PostMapping(value = "/api/demandOrder/modifyCarrierDetail")
    public Result<ModifyCarrierDetailResponseDto> modifyCarrierDetail(@RequestBody @Valid ModifyCarrierDetailRequestDto requestDto) {
        Result<ModifyCarrierDetailResponseModel> modelResult = demandOrderClient.modifyCarrierDetail(MapperUtils.mapper(requestDto, ModifyCarrierDetailRequestModel.class));
        modelResult.throwException();
        return Result.success(MapperUtils.mapper(modelResult.getData(),ModifyCarrierDetailResponseDto.class,new ModifyCarrierDetailMapping()));
    }

}
