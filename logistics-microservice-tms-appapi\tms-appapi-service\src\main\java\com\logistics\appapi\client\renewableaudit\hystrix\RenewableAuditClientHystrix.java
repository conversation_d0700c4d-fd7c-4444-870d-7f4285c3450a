package com.logistics.appapi.client.renewableaudit.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.renewableaudit.RenewableAuditClient;
import com.logistics.appapi.client.renewableaudit.request.*;
import com.logistics.appapi.client.renewableaudit.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/14 14:50
 */
@Component
public class RenewableAuditClientHystrix implements RenewableAuditClient {
    @Override
    public Result<RenewableOrderListStatisticResponseModel> renewableOrderListStatistic() {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<RenewableOrderListResponseModel>> renewableOrderList(RenewableOrderListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<RenewableOrderDetailResponseModel> renewableOrderDetail(RenewableOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> submitGoods(RenewableOrderSubmitGoodsRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> submitTicket(RenewableOrderSubmitTicketRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> submitRenewableOrder(SubmitRenewableOrderRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchWarehouseResponseModel>> searchWarehouse(SearchWarehouseRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchConsignorResponseModel>> searchConsignor(SearchConsignorRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PublishRenewableOrderResponseModel> publishRenewableOrder(PublishRenewableOrderRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchLifeSkuResponseModel>> searchLifeSku(SearchLifeSkuRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SearchSkuDetailResponseModel> searchSkuDetail(SearchSkuDetailRequestModel requestModel) {
        return Result.timeout();
    }
}
