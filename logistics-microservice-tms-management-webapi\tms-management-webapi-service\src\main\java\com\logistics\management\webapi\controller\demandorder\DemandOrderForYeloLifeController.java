package com.logistics.management.webapi.controller.demandorder;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.ExportUtils;
import com.logistics.management.webapi.client.demandorder.DemandOrderForYeloLifeClient;
import com.logistics.management.webapi.client.demandorder.request.*;
import com.logistics.management.webapi.client.demandorder.response.*;
import com.logistics.management.webapi.controller.demandorder.mapping.LifePublishDetailMapping;
import com.logistics.management.webapi.controller.demandorder.mapping.RenewableDemandOrderDetailMapping;
import com.logistics.management.webapi.controller.demandorder.mapping.RenewableDemandOrderListMapping;
import com.logistics.management.webapi.controller.demandorder.request.*;
import com.logistics.management.webapi.controller.demandorder.response.LifeBatchPublishDetailResponseDto;
import com.logistics.management.webapi.controller.demandorder.response.RenewableDemandListStatisticsResponseDto;
import com.logistics.management.webapi.controller.demandorder.response.RenewableDemandOrderDetailResponseDto;
import com.logistics.management.webapi.controller.demandorder.response.RenewableDemandOrderResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 新生需求单管理
 * @author: wjf
 * @date: 2024/3/27 9:23
 */
@Api(value = "新生需求单管理")
@RestController
public class DemandOrderForYeloLifeController {

    @Resource
    private DemandOrderForYeloLifeClient demandOrderForYeloLifeClient;

    /**
     * 查询新生需求单列表 3.23.0
     *
     * @param requestDto 筛选条件
     * @return 新生需求单列表
     */
    @ApiOperation("新生需求单列表")
    @PostMapping(value = "/api/renewableDemandOrder/renewableDemandOrderList")
    public Result<PageInfo<RenewableDemandOrderResponseDto>> renewableDemandOrderList(@RequestBody RenewableDemandOrderRequestDto requestDto) {
        Result<PageInfo<RenewableDemandOrderResponseModel>> result = demandOrderForYeloLifeClient.renewableDemandOrderList(MapperUtils.mapper(requestDto, RenewableDemandOrderRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        pageInfo.setList(MapperUtils.mapper(pageInfo.getList(), RenewableDemandOrderResponseDto.class, new RenewableDemandOrderListMapping()));
        return Result.success(pageInfo);
    }

    /**
     * 导出新生需求单
     *
     * @param requestDto 筛选条件
     */
    @ApiOperation("新生需求单列表导出 v1.2.1")
    @GetMapping(value = "/api/renewableDemandOrder/exportRenewableDemandOrderList")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportRenewableDemandOrderList(RenewableDemandOrderRequestDto requestDto, HttpServletResponse response) {
        requestDto.setPageNum(CommonConstant.INTEGER_ZERO);
        requestDto.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<List<RenewableDemandOrderResponseModel>> result = demandOrderForYeloLifeClient.renewableDemandOrderListExport(MapperUtils.mapper(requestDto, RenewableDemandOrderRequestModel.class));
        result.throwException();
        List<RenewableDemandOrderResponseModel> data = result.getData();
        List<RenewableDemandOrderResponseDto> list = MapperUtils.mapper(data, RenewableDemandOrderResponseDto.class, new RenewableDemandOrderListMapping());

        String fileName = "需求单列表" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        ExportUtils.exportByYeloExcel(response, list, RenewableDemandOrderResponseDto.class, fileName);
    }

    /**
     * 新生需求单详情
     *
     * @param requestDto 需求单ID
     * @return 需求单详情
     */
    @ApiOperation("新生需求单详情 v1.2.1")
    @PostMapping(value = "/api/renewableDemandOrder/renewableDemandOrderDetail")
    public Result<RenewableDemandOrderDetailResponseDto> renewableDemandOrderDetail(@RequestBody @Valid DemandOrderDetailRequestDto requestDto) {
        Result<RenewableDemandOrderDetailResponseModel> result = demandOrderForYeloLifeClient.renewableDemandOrderDetail(MapperUtils.mapper(requestDto, DemandOrderDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), RenewableDemandOrderDetailResponseDto.class, new RenewableDemandOrderDetailMapping()));
    }

    /**
     * 新生需求单列表统计
     *
     * @param requestDto 筛选条件
     * @return 需求单列表统计
     */
    @ApiOperation(value = "新生需求单列表统计")
    @PostMapping(value = "/api/renewableDemandOrder/getRenewableDemandOrderListStatistics")
    public Result<RenewableDemandListStatisticsResponseDto> getRenewableDemandOrderListStatistics(@RequestBody RenewableDemandOrderRequestDto requestDto) {
        Result<RenewableDemandListStatisticsResponseModel> result = demandOrderForYeloLifeClient.getRenewableDemandOrderListStatistics(MapperUtils.mapper(requestDto, RenewableDemandOrderRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), RenewableDemandListStatisticsResponseDto.class));
    }

    /**
     * 新生需求单批量发布详情 3.23.0
     */
    @PostMapping(value = "/api/renewableDemandOrder/publishDetail")
    public Result<List<LifeBatchPublishDetailResponseDto>> publishDetail(@RequestBody @Valid LifeBatchPublishDetailRequestDto requestDto) {
        Result<List<LifeBatchPublishDetailResponseModel>> result = demandOrderForYeloLifeClient.publishDetail(MapperUtils.mapper(requestDto, LifeBatchPublishDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), LifeBatchPublishDetailResponseDto.class, new LifePublishDetailMapping()));
    }

    /**
     * 新生需求单批量发布 3.23.0
     */
    @PostMapping(value = "/api/renewableDemandOrder/confirmPublish")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> confirmPublish(@RequestBody @Valid LifeBatchPublishRequestDto requestDto) {

        //是否我司
        if (!CommonConstant.ONE.equals(requestDto.getIsOurCompany()) && !CommonConstant.TWO.equals(requestDto.getIsOurCompany())){
            throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
        }
        if (CommonConstant.TWO.equals(requestDto.getIsOurCompany())) {//其他车主
            //车主ID校验
            if (StringUtils.isBlank(requestDto.getCompanyCarrierId())) {
                throw new BizException(ManagementWebApiExceptionEnum.COMPANY_CARRIER_ID_IS_NULL);
            }
        }else{
            requestDto.setCompanyCarrierId(null);
        }

        LifeBatchPublishRequestModel requestModel = MapperUtils.mapper(requestDto, LifeBatchPublishRequestModel.class);
        Result<Boolean> result = demandOrderForYeloLifeClient.confirmPublish(requestModel);
        result.throwException();
        return Result.success(true);
    }

    /**
     * 新生取消需求单 3.23.0
     */
    @PostMapping(value = "/api/renewableDemandOrder/cancelDemandOrder")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> cancelDemandOrder(@RequestBody @Valid LifeDemandOrderCancelRequestDto requestDto) {
        LifeDemandOrderCancelRequestModel requestModel = MapperUtils.mapperNoDefault(requestDto, LifeDemandOrderCancelRequestModel.class);
        Result<Boolean> result = demandOrderForYeloLifeClient.cancelDemandOrder(requestModel);
        result.throwException();
        return Result.success(true);
    }

    /**
     * 新生需求单修改车主 3.23.0
     */
    @ApiOperation(value = "新生需求单修改车主")
    @PostMapping(value = "/api/renewableDemandOrder/modifyCarrier")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> modifyCarrier(@RequestBody @Valid ModifyCarrierForLifeRequestDto requestDto) {
        ModifyCarrierForLifeRequestModel requestModel = MapperUtils.mapperNoDefault(requestDto, ModifyCarrierForLifeRequestModel.class);
        Result<Boolean> result = demandOrderForYeloLifeClient.modifyCarrier(requestModel);
        result.throwException();
        return Result.success(true);
    }

}
