package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * 异常类型（二级）：" +
 * "101 电话空号 102 无人接听 103 联系方式错误;" +
 * "201 客户占用 202 客户不协助装车 203 贸易商流向错误 204 装车费用确认;" +
 * "301 暴风暴雨 302 当地修路;" +
 * "401 重复下单;
 */
@Getter
@AllArgsConstructor
public enum WorkOrderAnomalyTypeTwoEnum {

    DEFAULT(-1, ""),

    VACANT_NUMBER(101, "电话空号"),
    NO_ANSWER(102, "无人接听"),
    WRONG_NUMBER(103, "联系方式错误"),

    OCCUPY(201, "客户占用"),
    NOT_ASSIST(202, "客户不协助装车"),
    FLOW_ERROR(203, "贸易商流向错误"),
    LOADING_CHARGE(204, "装车费用确认"),

    WEATHER_REASON(301, "暴风暴雨"),
    ROAD_CONSTRUCTION(302, "当地修路"),

    REPETITION(401, "重复下单");

    private final Integer key;
    private final String value;

    public static WorkOrderAnomalyTypeTwoEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
