package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/1/4 14:23
 */
@Data
public class WaitCorrectStatisticsResponseDto {
    @ApiModelProperty("待提货")
    private List<WaitLoadStatisticsDto> waitLoadList;

    @ApiModelProperty("待纠错")
    private List<WaitCorrectStatisticsDto> waitCorrectList;

    @ApiModelProperty("待纠错总单数")
    private String waitCorrectOrderCount="0";
}
