package com.logistics.tms.controller.workordercenter.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: wjf
 * @date: 2023/4/14 17:05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkOrderListRequestModel extends AbstractPageForm<WorkOrderListRequestModel> {

    @ApiModelProperty("状态：0 待处理，10 处理中，20 已处理，30 已关闭，40 已撤销")
    private Integer status;

    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("车主")
    private String companyCarrierName;

    @ApiModelProperty("提报人")
    private String reportUserName;

    @ApiModelProperty("提报时间-开始")
    private String reportTimeStart;

    @ApiModelProperty("提报时间-结束")
    private String reportTimeEnd;

    @ApiModelProperty("处理时间-开始")
    private String solveTimeStart;

    @ApiModelProperty("处理时间-结束")
    private String solveTimeEnd;

    @ApiModelProperty("请求来源 1 后台，2 前台")
    private Integer source;//1 后台，2 前台

    @ApiModelProperty("提报来源：1 后台，2 前台，3 小程序")
    private Integer reportSource;

    private Long companyCarrierId;
}
