package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class GetCarrierOrderResponseModel {
    private Long demandOrderId;
    private Long carrierOrderId;
    private String carrierOrderCode;

    //货主费用
    private Integer entrustFreightType;
    private BigDecimal entrustFreight;
    private BigDecimal signFreightFee;

    @ApiModelProperty("车主价格：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer carrierPriceType;
    @ApiModelProperty("车主价格")
    private BigDecimal carrierPrice;

    private Integer goodsUnit;
    private BigDecimal expectAmount;
    private BigDecimal loadAmount;
    private BigDecimal unloadAmount;
    private BigDecimal signAmount;
    private Long companyCarrierId;
    private Long companyEntrustId;
    private Integer status;
    private Date loadTime;
    @ApiModelProperty("是否放空")
    private Integer ifEmpty;

    @ApiModelProperty("货主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer settlementTonnage;
    @ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位'")
    private Integer carrierSettlement;
    @ApiModelProperty("委托单来源：1 乐医托盘委托单，2 委托方发单，3 中石化系统，4 扬巴邮件，5 乐橘新生")
    private Integer demandOrderSource;

    //货物信息
    private List<GetCarrierOrderGoodsResponseModel> goodsList;

}
