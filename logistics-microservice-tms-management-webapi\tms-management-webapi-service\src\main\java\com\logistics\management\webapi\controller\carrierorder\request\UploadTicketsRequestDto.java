package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class UploadTicketsRequestDto {
    @ApiModelProperty("运单ID")
    private String carrierOrderId;
    @ApiModelProperty("票据类型 1 提货单 2 出库单 3 签收单 4 其他")
    private String ticketType;
    @ApiModelProperty("上传图片临时路径")
    private List<String> tmpUrl;
}
