<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDriverSafeMeetingMapper" >
    <select id="driverSafeMeetingKanBan" resultType="com.logistics.tms.controller.driversafemeeting.response.DriverSafeMeetingKanBanResponseModel" parameterType="com.logistics.tms.controller.driversafemeeting.request.DriverSafeMeetingKanBanRequestModel">
        select
        tdsm.id as safeMeetingId,
        tdsm.period as period,
        tdsm.type,
        tdsm.title
        from t_driver_safe_meeting tdsm
        where tdsm.valid = 1
        <if test="meetingYearStart!= null and meetingYearStart != ''">
            and tdsm.period &gt;= #{meetingYearStart,jdbcType=VARCHAR}
        </if>
        <if test="meetingYearEnd!= null and meetingYearEnd != ''">
            and tdsm.period &lt;= #{meetingYearEnd,jdbcType=VARCHAR}
        </if>
        <if test="type!= null">
            and tdsm.type = #{type,jdbcType=INTEGER}
        </if>
        group by tdsm.id
        order by tdsm.period desc,tdsm.created_time desc
    </select>

    <select id="getByPeriod" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_driver_safe_meeting
        where valid = 1
        and period = #{period,jdbcType=VARCHAR}
    </select>

    <resultMap id="driverSafeMeetingContentDetail_Map" type="com.logistics.tms.controller.driversafemeeting.response.DriverSafeMeetingContentDetailResponseModel">
        <id column="safeMeetingId" property="safeMeetingId" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="period" property="period" jdbcType="VARCHAR"/>
        <result column="introduction" property="introduction" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="createdTime" property="createdTime" jdbcType="TIMESTAMP"/>
        <collection property="driverIdList" ofType="java.lang.Long" javaType="list">
            <result column="staff_id"/>
        </collection>
    </resultMap>
    <select id="driverSafeMeetingContentDetail" resultMap="driverSafeMeetingContentDetail_Map">
        select
        tdsm.id as safeMeetingId,
        tdsm.title,
        tdsm.period,
        tdsm.introduction,
        tdsm.content,
        tdsm.created_time as createdTime,
        tdsmr.staff_id
        from t_driver_safe_meeting tdsm
        left join t_driver_safe_meeting_relation tdsmr on tdsmr.safe_meeting_id = tdsm.id and tdsmr.valid = 1
        where tdsm.valid = 1
        and tdsm.id = #{id,jdbcType=BIGINT}
    </select>
</mapper>