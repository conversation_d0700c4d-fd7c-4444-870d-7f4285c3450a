package com.logistics.appapi.controller.driverappoint.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/17
 */
@Data
public class SearchDrierAppointListResponseDto {

	@ApiModelProperty("司机预约记录id")
	private String driverAppointId = "";

	@ApiModelProperty("需求单号")
	private String demandOrderCode = "";

	@ApiModelProperty("乐橘新生客户名,个人展示手机号 姓名")
	private String customerName = "";

	@ApiModelProperty("下单时间 yyyy-MM-dd HH:mm:ss")
	private String publishTime = "";

	@ApiModelProperty("货物数量(单位吨)")
	private String goodsAmountTotal = "";
}
