<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TDriverCostApplyInvoiceMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDriverCostApplyInvoice">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="driver_cost_apply_id" jdbcType="BIGINT" property="driverCostApplyId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="invoice_name" jdbcType="VARCHAR" property="invoiceName" />
    <result column="invoice_type" jdbcType="VARCHAR" property="invoiceType" />
    <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="invoice_num" jdbcType="VARCHAR" property="invoiceNum" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="total_tax" jdbcType="DECIMAL" property="totalTax" />
    <result column="total_tax_and_price" jdbcType="DECIMAL" property="totalTaxAndPrice" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, driver_cost_apply_id, type, invoice_name, invoice_type, invoice_code, invoice_num, 
    total_price, total_tax, total_tax_and_price, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_driver_cost_apply_invoice
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_driver_cost_apply_invoice
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDriverCostApplyInvoice">
    insert into t_driver_cost_apply_invoice (id, driver_cost_apply_id, type, 
      invoice_name, invoice_type, invoice_code, 
      invoice_num, total_price, total_tax, 
      total_tax_and_price, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{driverCostApplyId,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, 
      #{invoiceName,jdbcType=VARCHAR}, #{invoiceType,jdbcType=VARCHAR}, #{invoiceCode,jdbcType=VARCHAR}, 
      #{invoiceNum,jdbcType=VARCHAR}, #{totalPrice,jdbcType=DECIMAL}, #{totalTax,jdbcType=DECIMAL}, 
      #{totalTaxAndPrice,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDriverCostApplyInvoice">
    insert into t_driver_cost_apply_invoice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="driverCostApplyId != null">
        driver_cost_apply_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="invoiceName != null">
        invoice_name,
      </if>
      <if test="invoiceType != null">
        invoice_type,
      </if>
      <if test="invoiceCode != null">
        invoice_code,
      </if>
      <if test="invoiceNum != null">
        invoice_num,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="totalTax != null">
        total_tax,
      </if>
      <if test="totalTaxAndPrice != null">
        total_tax_and_price,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="driverCostApplyId != null">
        #{driverCostApplyId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="invoiceName != null">
        #{invoiceName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=VARCHAR},
      </if>
      <if test="invoiceCode != null">
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNum != null">
        #{invoiceNum,jdbcType=VARCHAR},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="totalTax != null">
        #{totalTax,jdbcType=DECIMAL},
      </if>
      <if test="totalTaxAndPrice != null">
        #{totalTaxAndPrice,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDriverCostApplyInvoice">
    update t_driver_cost_apply_invoice
    <set>
      <if test="driverCostApplyId != null">
        driver_cost_apply_id = #{driverCostApplyId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="invoiceName != null">
        invoice_name = #{invoiceName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        invoice_type = #{invoiceType,jdbcType=VARCHAR},
      </if>
      <if test="invoiceCode != null">
        invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNum != null">
        invoice_num = #{invoiceNum,jdbcType=VARCHAR},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="totalTax != null">
        total_tax = #{totalTax,jdbcType=DECIMAL},
      </if>
      <if test="totalTaxAndPrice != null">
        total_tax_and_price = #{totalTaxAndPrice,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDriverCostApplyInvoice">
    update t_driver_cost_apply_invoice
    set driver_cost_apply_id = #{driverCostApplyId,jdbcType=BIGINT},
      type = #{type,jdbcType=INTEGER},
      invoice_name = #{invoiceName,jdbcType=VARCHAR},
      invoice_type = #{invoiceType,jdbcType=VARCHAR},
      invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      invoice_num = #{invoiceNum,jdbcType=VARCHAR},
      total_price = #{totalPrice,jdbcType=DECIMAL},
      total_tax = #{totalTax,jdbcType=DECIMAL},
      total_tax_and_price = #{totalTaxAndPrice,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="batchUpdateSelective" parameterType="java.util.List">
    <foreach collection="recordList" index="index" item="item" separator=";">
      update t_driver_cost_apply_invoice
      <set>
        <if test="item.driverCostApplyId != null">
          driver_cost_apply_id = #{item.driverCostApplyId, jdbcType=BIGINT},
        </if>
        <if test="item.type != null">
          type = #{item.type, jdbcType=INTEGER},
        </if>
        <if test="item.invoiceName != null">
          invoice_name = #{item.invoiceName, jdbcType=VARCHAR},
        </if>
        <if test="item.invoiceType != null">
          invoice_type = #{item.invoiceType, jdbcType=VARCHAR},
        </if>
        <if test="item.invoiceCode != null">
          invoice_code = #{item.invoiceCode, jdbcType=VARCHAR},
        </if>
        <if test="item.invoiceNum != null">
          invoice_num = #{item.invoiceNum, jdbcType=VARCHAR},
        </if>
        <if test="item.totalPrice != null">
          total_price = #{item.totalPrice, jdbcType=DECIMAL},
        </if>
        <if test="item.totalTax != null">
          total_tax = #{item.totalTax, jdbcType=DECIMAL},
        </if>
        <if test="item.totalTaxAndPrice != null">
          total_tax_and_price = #{item.totalTaxAndPrice, jdbcType=DECIMAL},
        </if>
        <if test="item.createdBy != null">
          created_by = #{item.createdBy, jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          created_time = #{item.createdTime, jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by = #{item.lastModifiedBy, jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time = #{item.lastModifiedTime, jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          valid = #{item.valid, jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id, jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="java.util.List">
    <foreach collection="recordList" index="index" item="item" separator=";">
      insert into t_driver_cost_apply_invoice 
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          id,
        </if>
        <if test="item.driverCostApplyId != null">
          driver_cost_apply_id,
        </if>
        <if test="item.type != null">
          type,
        </if>
        <if test="item.invoiceName != null">
          invoice_name,
        </if>
        <if test="item.invoiceType != null">
          invoice_type,
        </if>
        <if test="item.invoiceCode != null">
          invoice_code,
        </if>
        <if test="item.invoiceNum != null">
          invoice_num,
        </if>
        <if test="item.totalPrice != null">
          total_price,
        </if>
        <if test="item.totalTax != null">
          total_tax,
        </if>
        <if test="item.totalTaxAndPrice != null">
          total_tax_and_price,
        </if>
        <if test="item.createdBy != null">
          created_by,
        </if>
        <if test="item.createdTime != null">
          created_time,
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time,
        </if>
        <if test="item.valid != null">
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          #{item.id, jdbcType=BIGINT},
        </if>
        <if test="item.driverCostApplyId != null">
          #{item.driverCostApplyId, jdbcType=BIGINT},
        </if>
        <if test="item.type != null">
          #{item.type, jdbcType=INTEGER},
        </if>
        <if test="item.invoiceName != null">
          #{item.invoiceName, jdbcType=VARCHAR},
        </if>
        <if test="item.invoiceType != null">
          #{item.invoiceType, jdbcType=VARCHAR},
        </if>
        <if test="item.invoiceCode != null">
          #{item.invoiceCode, jdbcType=VARCHAR},
        </if>
        <if test="item.invoiceNum != null">
          #{item.invoiceNum, jdbcType=VARCHAR},
        </if>
        <if test="item.totalPrice != null">
          #{item.totalPrice, jdbcType=DECIMAL},
        </if>
        <if test="item.totalTax != null">
          #{item.totalTax, jdbcType=DECIMAL},
        </if>
        <if test="item.totalTaxAndPrice != null">
          #{item.totalTaxAndPrice, jdbcType=DECIMAL},
        </if>
        <if test="item.createdBy != null">
          #{item.createdBy, jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          #{item.createdTime, jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          #{item.lastModifiedBy, jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          #{item.lastModifiedTime, jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          #{item.valid, jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>
</mapper>