package com.logistics.management.webapi.controller.invoicingmanagement.tradition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/3/19 16:28
 */
@Data
public class TraditionSearchInvoicingManagementListResponseDto {
    /**
     * 发票管理id
     */
    @ApiModelProperty("发票管理id")
    private String invoicingId="";

    /**
     * 业务名称
     */
    @ApiModelProperty("业务名称")
    private String businessName="";

    /**
     * 开票月份
     */
    @ApiModelProperty("开票月份")
    private String invoicingMonth="";

    /**
     * 承运商名称
     */
    @ApiModelProperty("承运商名称")
    private String companyCarrierName="";

    /**
     * 发票金额
     */
    @ApiModelProperty("发票金额")
    private String invoiceAmount="";

    /**
     * 对账金额
     */
    @ApiModelProperty("对账金额")
    private String reconciliationFee = "";

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark="";

    /**
     * 操作人
     */
    @ApiModelProperty("操作人")
    private String lastModifiedBy="";

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    private String lastModifiedTime="";
}
