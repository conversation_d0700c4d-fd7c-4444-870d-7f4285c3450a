package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleType extends BaseEntity {
    /**
    * 车辆类型
    */
    @ApiModelProperty("车辆类型")
    private String vehicleType;

    /**
    * 车辆类别 1 牵引车 2 挂车 3 一体车 
    */
    @ApiModelProperty("车辆类别 1 牵引车 2 挂车 3 一体车 ")
    private Integer vehicleCategory;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 添加人id
    */
    @ApiModelProperty("添加人id")
    private Long addUserId;

    /**
    * 添加人名字
    */
    @ApiModelProperty("添加人名字")
    private String addUserName;

    /**
    * 车辆添加来源：1新增 2导入
    */
    @ApiModelProperty("车辆添加来源：1新增 2导入")
    private Integer source;

    /**
    * 0禁用 1启用
    */
    @ApiModelProperty("0禁用 1启用")
    private Integer enabled;
}