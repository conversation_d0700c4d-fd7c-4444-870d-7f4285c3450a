package com.logistics.tms.client.feign.tray.order.customerinorder;

import com.logistics.tms.client.feign.FeignClientName;
import com.logistics.tms.client.feign.tray.order.customerinorder.hystrix.CustomerInOrderServiceApiHystrix;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.RollbackDemandRequestModel;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.request.*;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.response.GetCarrierOrderDetailResponseModel;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.response.GetCarrierOrderQRCodeResponseModel;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.response.GetCheckAddressCodeResponse;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.response.GetCustomerInOrderStateResponseModel;
import com.logistics.tms.client.model.TmsLockReplenishDemandFeignRequest;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Optional;

@FeignClient(name = FeignClientName.TRAY_ORDER_SERVICES, fallback = CustomerInOrderServiceApiHystrix.class)
public interface CustomerInOrderServiceApi {

    /**
     * 根据运单号查询入库状态
     *
     * @param requestModel request model param
     * @return customer in order state
     */
    @ApiOperation(value = "批量获取入库状态")
    @PostMapping(value = "/service/customerInOrder/getCustomerStateByCarrierOrderCodes")
    Result<List<GetCustomerInOrderStateResponseModel>> getCustomerStateByCarrierOrderCodes(@RequestBody GetCustomerInOrderStateRequestModel requestModel);

    @ApiOperation(value = "根据运单号查询二维码信息")
    @PostMapping(value = "/service/carrier/order/getCarrierOrderQRCode")
    Result<GetCarrierOrderQRCodeResponseModel> getCarrierOrderQRCode(@RequestBody GetCarrierOrderQRCodeRequestModel requestModel);

    @ApiModelProperty(value = "根据运单号查询运单信息")
    @PostMapping(value = "/service/carrier/order/getCarrierOrderDetail")
    Result<GetCarrierOrderDetailResponseModel> getCarrierOrderDetail(@RequestBody GetCarrierOrderDetailRequestModel requestModel);

    /**
     * 司机驳回提货数
     *
     * @param requestModel request model param
     * @return customer in order state
     */
    @ApiOperation(value = "司机驳回提货数")
    @PostMapping(value = "/service/carrier/order/reject")
    Result<Boolean> rejectOrderLoadAmount(@RequestBody RejectOrderLoadAmountRequestModel requestModel);

    default Optional<GetCarrierOrderDetailResponseModel> getCarrierOrderDetail(String carrierOrderCode) {
        Result<GetCarrierOrderDetailResponseModel> result = getCarrierOrderDetail(new GetCarrierOrderDetailRequestModel()
                .setCarrierOrderCode(carrierOrderCode));
        result.throwException();
        return Optional.ofNullable(result.getData());
    }


    @PostMapping(value = "/service/logisticsDemand/rollBackDemandOrder")
    Result<Boolean> rollBackDemandOrder(@RequestBody RollbackDemandRequestModel requestModel);


    /**
     * 物流纠错补单预锁定
     * tms-service调用
     */
    @ApiOperation(value = "物流取消补单预锁定")
    @PostMapping(value = "/service/logistics/tmsLockReplenishDemand")
    Result<Void> tmsLockReplenishDemand(TmsLockReplenishDemandFeignRequest request);

    @ApiOperation("获取核销专用地址code")
    @PostMapping("/service/logistics/getCheckAddressCode")
    Result<GetCheckAddressCodeResponse> getCheckAddressCode();


    /**
     * 物流补充需求单增加实际地址
     */
    @ApiOperation(value = "物流补充需求单增加实际地址")
    @PostMapping(value = "/service/logistics/logisticsAddOrUpdateActualAddress")
    Result<Boolean> logisticsAddOrUpdateActualAddress(@RequestBody LogisticsAddOrUpdateActualAddressRequest request);


    @ApiOperation(value = "物流同步补充需求单")
    @PostMapping(value = "/service/logistics/syncSupplementDemand")
    Result<Boolean> syncSupplementDemand(@RequestBody SyncSupplementDemandRequest request);

}
