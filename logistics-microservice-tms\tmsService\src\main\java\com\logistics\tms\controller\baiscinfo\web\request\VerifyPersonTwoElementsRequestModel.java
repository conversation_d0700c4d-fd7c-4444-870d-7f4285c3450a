package com.logistics.tms.controller.baiscinfo.web.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/2
 */
@Data
public class VerifyPersonTwoElementsRequestModel {

	@ApiModelProperty("姓名")
	private String name;

	@ApiModelProperty("默认0，表示身份证。目前只支持身份证")
	private String identityType = "0";

	@ApiModelProperty("证件号")
	private String identity;
}
