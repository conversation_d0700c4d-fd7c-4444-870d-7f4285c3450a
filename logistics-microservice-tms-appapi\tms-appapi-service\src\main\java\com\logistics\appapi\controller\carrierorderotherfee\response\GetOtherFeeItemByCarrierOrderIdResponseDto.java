package com.logistics.appapi.controller.carrierorderotherfee.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2022/9/2 11:23
 */
@Data
public class GetOtherFeeItemByCarrierOrderIdResponseDto {
    @ApiModelProperty("临时费用明细id")
    private String carrierOrderOtherFeeItemId="";

    @ApiModelProperty("费用名称：1 短驳费，2 保管费，3 装卸费，4 压车费，5 质量处罚费，6 其他杂费")
    private String feeType="";
    @ApiModelProperty("费用名称")
    private String feeTypeLabel="";

    @ApiModelProperty("调整费用符号：1 ‘+’， 2 ‘-’")
    private String feeAmountSymbol="";
    @ApiModelProperty("费用金额")
    private String feeAmount="";

    /**
     * 单据图片 v2.43
     */
    @ApiModelProperty("单据-绝对路径 v2.43")
    private List<BillPicDto> picList= new ArrayList<>();
}
