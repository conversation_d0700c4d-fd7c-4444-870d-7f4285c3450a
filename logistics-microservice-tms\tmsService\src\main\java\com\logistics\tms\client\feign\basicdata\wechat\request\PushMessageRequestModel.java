package com.logistics.tms.client.feign.basicdata.wechat.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PushMessageRequestModel {

    @ApiModelProperty(value = "群聊id（有值就推）")
    private String chatId;
    @ApiModelProperty(value = "机器人的连接地址（有值就推）")
    private String rbId;
    @ApiModelProperty(value = "消息类型 text:文本，image：图片，voice：语音，video：视频，file：文件")
    private String msgType;
    @ApiModelProperty(value = "消息内容")
    private String message;

    public enum PushMessageMsgType {
        text,
        image,
        voice,
        video,
        file
    }
}
