package com.logistics.tms.controller.verificationcode;

import com.logistics.tms.biz.verificationcode.VerificationCodeBiz;
import com.logistics.tms.controller.verificationcode.request.VerificationCodeRequestModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: wjf
 * @date: 2023/10/26 11:10
 */
@RestController
@Api(value = "获取验证码")
@RequestMapping(value = "/service/verificationCode")
public class VerificationCodeController {

    @Resource
    private VerificationCodeBiz verificationCodeBiz;

    /**
     * 获取短信验证码
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取短信验证码")
    @PostMapping(value = "/getVerifyCode")
    public Result<Boolean> getVerifyCode(@RequestBody VerificationCodeRequestModel requestModel) {
        verificationCodeBiz.getVerifyCode(requestModel);
        return Result.success(true);
    }
}
