<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TRealNameAuthenticationMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TRealNameAuthentication">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bestsign_account" jdbcType="VARCHAR" property="bestsignAccount" />
    <result column="authentication_type" jdbcType="INTEGER" property="authenticationType" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="identity_number" jdbcType="VARCHAR" property="identityNumber" />
    <result column="certification_status" jdbcType="INTEGER" property="certificationStatus" />
    <result column="auth_mode" jdbcType="INTEGER" property="authMode" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, bestsign_account, authentication_type, name, mobile, identity_number, certification_status, 
    auth_mode, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_real_name_authentication
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_real_name_authentication
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TRealNameAuthentication">
    insert into t_real_name_authentication (id, bestsign_account, authentication_type, 
      name, mobile, identity_number, 
      certification_status, auth_mode, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{bestsignAccount,jdbcType=VARCHAR}, #{authenticationType,jdbcType=INTEGER}, 
      #{name,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{identityNumber,jdbcType=VARCHAR}, 
      #{certificationStatus,jdbcType=INTEGER}, #{authMode,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TRealNameAuthentication">
    insert into t_real_name_authentication
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bestsignAccount != null">
        bestsign_account,
      </if>
      <if test="authenticationType != null">
        authentication_type,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="identityNumber != null">
        identity_number,
      </if>
      <if test="certificationStatus != null">
        certification_status,
      </if>
      <if test="authMode != null">
        auth_mode,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bestsignAccount != null">
        #{bestsignAccount,jdbcType=VARCHAR},
      </if>
      <if test="authenticationType != null">
        #{authenticationType,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="identityNumber != null">
        #{identityNumber,jdbcType=VARCHAR},
      </if>
      <if test="certificationStatus != null">
        #{certificationStatus,jdbcType=INTEGER},
      </if>
      <if test="authMode != null">
        #{authMode,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TRealNameAuthentication">
    update t_real_name_authentication
    <set>
      <if test="bestsignAccount != null">
        bestsign_account = #{bestsignAccount,jdbcType=VARCHAR},
      </if>
      <if test="authenticationType != null">
        authentication_type = #{authenticationType,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="identityNumber != null">
        identity_number = #{identityNumber,jdbcType=VARCHAR},
      </if>
      <if test="certificationStatus != null">
        certification_status = #{certificationStatus,jdbcType=INTEGER},
      </if>
      <if test="authMode != null">
        auth_mode = #{authMode,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TRealNameAuthentication">
    update t_real_name_authentication
    set bestsign_account = #{bestsignAccount,jdbcType=VARCHAR},
      authentication_type = #{authenticationType,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      identity_number = #{identityNumber,jdbcType=VARCHAR},
      certification_status = #{certificationStatus,jdbcType=INTEGER},
      auth_mode = #{authMode,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>