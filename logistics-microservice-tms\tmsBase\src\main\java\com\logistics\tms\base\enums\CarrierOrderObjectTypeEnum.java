package com.logistics.tms.base.enums;


public enum CarrierOrderObjectTypeEnum {
    DEFAULT(0,""),
    AMOUNT(10,"数量问题"),
    REPEAT(20,"重报问题"),
    ASSOCIATION(30,"联系问题"),
    ADDRESS(40,"地址问题"),
    LOAD(50,"装车问题"),
    WAIT(60,"等待问题"),
    <PERSON><PERSON><PERSON>(70,"其他问题"),
    ;

    private Integer key;
    private String value;

    CarrierOrderObjectTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
    public static CarrierOrderObjectTypeEnum getEnum(Integer key) {
        for (CarrierOrderObjectTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
