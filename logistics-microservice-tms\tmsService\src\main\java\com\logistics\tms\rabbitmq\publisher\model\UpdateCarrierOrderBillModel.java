package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class UpdateCarrierOrderBillModel {

    @ApiModelProperty("运单code")
    private String carrierOrderCode;
    @ApiModelProperty("标识：1、新增  2、删除")
    private Integer type;
    @ApiModelProperty("操作人")
    private String  operation;
    @ApiModelProperty("票据集合")
    private List<CarrierOrderBill> carrierOrderBillList;

}
