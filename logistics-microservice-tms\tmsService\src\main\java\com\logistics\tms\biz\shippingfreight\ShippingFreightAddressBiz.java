package com.logistics.tms.biz.shippingfreight;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.EnabledEnum;
import com.logistics.tms.base.enums.PriceTypeEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.shippingfreight.model.ShippingFreightRuleSqlConditionModel;
import com.logistics.tms.biz.shippingfreight.model.ShippingFreightRuleVehicleLengthSqlConditionModel;
import com.logistics.tms.controller.freightconfig.request.shipping.AddShippingFreightRuleAddressReqModel;
import com.logistics.tms.controller.freightconfig.request.shipping.AddShippingFreightRuleReqModel;
import com.logistics.tms.controller.freightconfig.request.shipping.ListShippingFreightRuleConfigReqModel;
import com.logistics.tms.controller.freightconfig.response.shipping.ListShippingFreightRuleListRespModel;
import com.logistics.tms.entity.TShippingFreightAddress;
import com.logistics.tms.entity.TShippingFreightRuleVehicleLength;
import com.logistics.tms.mapper.*;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ShippingFreightAddressBiz {

    @Resource
    private TShippingFreightAddressMapper tShippingFreightAddressMapper;
    @Resource
    private TShippingFreightRuleVehicleLengthMapper tShippingFreightRuleVehicleLengthMapper;

    @Resource
    private CommonBiz commonBiz;


    /**
     * 零担规则配置列表
     */
    public PageInfo<ListShippingFreightRuleListRespModel> getList(ListShippingFreightRuleConfigReqModel reqModel) {

        //开启分页
        if (CommonConstant.INTEGER_ZERO.equals(reqModel.getIfExport())) {
            reqModel.enablePaging();
        }
        //查询零担费用规则
        ShippingFreightRuleSqlConditionModel configReqModel = new ShippingFreightRuleSqlConditionModel();
        if (reqModel.getShippingFreightId() != null) {
            configReqModel.setShippingFreightIds(Collections.singletonList(reqModel.getShippingFreightId()));
        }
        if (CollectionUtil.isNotEmpty(reqModel.getShippingFreightRuleIds())) {
            configReqModel.setIds(reqModel.getShippingFreightRuleIds());
        }
        if (StringUtils.isNotBlank(reqModel.getLoadAddress())) {
            configReqModel.setLoadAddress(reqModel.getLoadAddress());
        }
        if (StringUtils.isNotBlank(reqModel.getUnloadAddress())) {
            configReqModel.setUnloadAddress(reqModel.getUnloadAddress());
        }
        List<TShippingFreightAddress> tShippingFreightAddresses = tShippingFreightAddressMapper.listByCondition(configReqModel);
        List<Long> tShippingFreightRuleIds = tShippingFreightAddresses.stream().map(TShippingFreightAddress::getId).collect(Collectors.toList());
        PageInfo pageInfo = new PageInfo<>(tShippingFreightAddresses);

        //查询零担费用车长运价规则
        ShippingFreightRuleVehicleLengthSqlConditionModel shippingFreightRuleVehicleLengthSqlConditionModel = new ShippingFreightRuleVehicleLengthSqlConditionModel();
        shippingFreightRuleVehicleLengthSqlConditionModel.setShippingFreightRuleIds(tShippingFreightRuleIds);
        List<TShippingFreightRuleVehicleLength> tShippingFreightRuleVehicleLengths = tShippingFreightRuleVehicleLengthMapper.selectListByCondition(shippingFreightRuleVehicleLengthSqlConditionModel);
        Map<Long, List<TShippingFreightRuleVehicleLength>> tShippingFreightRuleVehicleLengthsMap = tShippingFreightRuleVehicleLengths.stream().collect(Collectors.groupingBy(TShippingFreightRuleVehicleLength::getShippingFreightAddressId));

        //组装请求参数
        List<ListShippingFreightRuleListRespModel> respModels = new ArrayList<>();
        for (TShippingFreightAddress tShippingFreightAddress : tShippingFreightAddresses) {

            List<TShippingFreightRuleVehicleLength> tShippingFreightRuleVehicleLengthList = tShippingFreightRuleVehicleLengthsMap.getOrDefault(tShippingFreightAddress.getId(), new ArrayList<>());

            Map<BigDecimal, List<TShippingFreightRuleVehicleLength>> tShippingFreightRuleVehicleLengthMap = tShippingFreightRuleVehicleLengthList.stream().collect(Collectors.groupingBy(TShippingFreightRuleVehicleLength::getVehicleLength));

            TShippingFreightRuleVehicleLength lessThanConfig = Optional.ofNullable(tShippingFreightRuleVehicleLengthMap.get(new BigDecimal("-1.00"))).orElse(new ArrayList<>()).stream().max(Comparator.comparing(TShippingFreightRuleVehicleLength::getSort)).orElse(null);
            TShippingFreightRuleVehicleLength fourPointTwoConfig = Optional.ofNullable(tShippingFreightRuleVehicleLengthMap.get(new BigDecimal("4.20"))).orElse(new ArrayList<>()).stream().max(Comparator.comparing(TShippingFreightRuleVehicleLength::getSort)).orElse(null);
            TShippingFreightRuleVehicleLength sixPointEightConfig = Optional.ofNullable(tShippingFreightRuleVehicleLengthMap.get(new BigDecimal("6.80"))).orElse(new ArrayList<>()).stream().max(Comparator.comparing(TShippingFreightRuleVehicleLength::getSort)).orElse(null);
            TShippingFreightRuleVehicleLength ninePointSixConfig = Optional.ofNullable(tShippingFreightRuleVehicleLengthMap.get(new BigDecimal("9.60"))).orElse(new ArrayList<>()).stream().max(Comparator.comparing(TShippingFreightRuleVehicleLength::getSort)).orElse(null);
            TShippingFreightRuleVehicleLength thirteenPointSevenFiveConfig = Optional.ofNullable(tShippingFreightRuleVehicleLengthMap.get(new BigDecimal("13.75"))).orElse(new ArrayList<>()).stream().max(Comparator.comparing(TShippingFreightRuleVehicleLength::getSort)).orElse(null);
            TShippingFreightRuleVehicleLength seventeenPointFiveConfig = Optional.ofNullable(tShippingFreightRuleVehicleLengthMap.get(new BigDecimal("17.50"))).orElse(new ArrayList<>()).stream().max(Comparator.comparing(TShippingFreightRuleVehicleLength::getSort)).orElse(null);


            ListShippingFreightRuleListRespModel respModel = new ListShippingFreightRuleListRespModel();
            respModel.setEnabled(tShippingFreightAddress.getEnabled());
            respModel.setEnabledStr(com.yelo.tools.enums.EnabledEnum.getEnum(tShippingFreightAddress.getEnabled()).getValue());

            respModel.setShippingFreightRuleId(tShippingFreightAddress.getId());
            respModel.setCarrierDistance(tShippingFreightAddress.getCarrierDistance().setScale(2, RoundingMode.HALF_UP).toString());
            respModel.setFromAreaName(tShippingFreightAddress.getFromAreaName());
            respModel.setFromCityName(tShippingFreightAddress.getFromProvinceName() + tShippingFreightAddress.getFromCityName());
//            respModel.setFromProvinceName(tShippingFreightAddress.getFromProvinceName());
            respModel.setLastModifiedBy(tShippingFreightAddress.getLastModifiedBy());
            if (tShippingFreightAddress.getLastModifiedTime() != null) {
                respModel.setLastModifiedTime(DateUtil.format(tShippingFreightAddress.getLastModifiedTime(), CommonConstant.YYYY_MM_DD_HH_MM_SS));
            }
            respModel.setToAreaName(tShippingFreightAddress.getToAreaName());
            respModel.setToCityName(tShippingFreightAddress.getToProvinceName() + tShippingFreightAddress.getToCityName());
//            respModel.setToProvinceName(tShippingFreightAddress.getToProvinceName());
            if (lessThanConfig != null) {
                respModel.setPrice(lessThanConfig.getPrice() + getUnitByPriceType(lessThanConfig.getPriceType()));
                respModel.setStartEndCount("<=" +lessThanConfig.getCountEnd().stripTrailingZeros().toPlainString());
            }
            if (fourPointTwoConfig != null) {
                respModel.setFourPointTwo(fourPointTwoConfig.getPrice() + getUnitByPriceType(fourPointTwoConfig.getPriceType()));
            }
            if (sixPointEightConfig != null) {
                respModel.setSixPointEight(sixPointEightConfig.getPrice() + getUnitByPriceType(sixPointEightConfig.getPriceType()));
            }
            if (ninePointSixConfig != null) {
                respModel.setNinePointSix(ninePointSixConfig.getPrice() + getUnitByPriceType(ninePointSixConfig.getPriceType()));
            }
            if (thirteenPointSevenFiveConfig != null) {
                respModel.setThirteenPointSevenFive(thirteenPointSevenFiveConfig.getPrice() + getUnitByPriceType(thirteenPointSevenFiveConfig.getPriceType()));
            }
            if (seventeenPointFiveConfig != null) {
                respModel.setSeventeenPointFive(seventeenPointFiveConfig.getPrice() + getUnitByPriceType(seventeenPointFiveConfig.getPriceType()));
            }
            respModels.add(respModel);
        }

        pageInfo.setList(respModels);
        return pageInfo;
    }

    private String getUnitByPriceType(Integer priceType) {
        String unit = "";
        if (PriceTypeEnum.UNIT_PRICE.getKey().equals(priceType)) {
            unit = "元/件";
        } else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(priceType)) {
            unit = "元";
        }
        return unit;
    }

    /**
     * 新增配置运价规则  v2.42
     */
    @Transactional
    public void add(AddShippingFreightRuleReqModel reqModel) {

        //校验地址不能重复
        this.checkAddressRepeat(reqModel);

        List<AddShippingFreightRuleAddressReqModel> fromAddressItems = reqModel.getFromAddressItems();
        List<TShippingFreightAddress> addShippingFreightAddresses = new ArrayList<>();
        for (AddShippingFreightRuleAddressReqModel fromAddressItem : fromAddressItems) {

            TShippingFreightAddress addShippingFreightAddress = new TShippingFreightAddress();
            addShippingFreightAddress.setEnabled(EnabledEnum.ENABLED.getKey());
            addShippingFreightAddress.setShippingFreightId(reqModel.getShippingFreightId());

            addShippingFreightAddress.setToAreaId(reqModel.getToAreaId());
            addShippingFreightAddress.setToAreaName(reqModel.getToAreaName());
            addShippingFreightAddress.setToCityId(reqModel.getToCityId());
            addShippingFreightAddress.setToCityName(reqModel.getToCityName());
            addShippingFreightAddress.setToProvinceId(reqModel.getToProvinceId());
            addShippingFreightAddress.setToProvinceName(reqModel.getToProvinceName());

            addShippingFreightAddress.setFromAreaId(fromAddressItem.getFromAreaId());
            addShippingFreightAddress.setFromAreaName(fromAddressItem.getFromAreaName());
            addShippingFreightAddress.setFromCityId(fromAddressItem.getFromCityId());
            addShippingFreightAddress.setFromCityName(fromAddressItem.getFromCityName());
            addShippingFreightAddress.setFromProvinceId(fromAddressItem.getFromProvinceId());
            addShippingFreightAddress.setFromProvinceName(fromAddressItem.getFromProvinceName());
            addShippingFreightAddress.setCarrierDistance(fromAddressItem.getCarrierDistance());

            addShippingFreightAddresses.add(addShippingFreightAddress);
        }

        if (CollectionUtil.isNotEmpty(addShippingFreightAddresses)) {
            tShippingFreightAddressMapper.batchInsertSelective(addShippingFreightAddresses);
        }


    }

    /**
     * 校验地址不能重复
     */
    private void checkAddressRepeat(AddShippingFreightRuleReqModel reqModel) {
        List<AddShippingFreightRuleAddressReqModel> fromAddressItems = reqModel.getFromAddressItems();

        //校验发货地不能重复
        LinkedHashSet<String> set = new LinkedHashSet<>();
        for (AddShippingFreightRuleAddressReqModel fromAddressItem : fromAddressItems) {
            if (!set.add(fromAddressItem.getFromProvinceId() + "-" + fromAddressItem.getFromCityId() + "-" + fromAddressItem.getFromAreaId()
                    +reqModel.getToProvinceId() + "-" + reqModel.getToCityId() + "-" + reqModel.getToAreaId())) {
                throw new BizException(CarrierDataExceptionEnum.REPEAT_ADDRESS_EXIST);
            }
        }
        //查询历史地址 校验不能重复
        List<TShippingFreightAddress> tShippingFreightAddresses = tShippingFreightAddressMapper.selectByShippingFreightId(reqModel.getShippingFreightId());
        for (TShippingFreightAddress tShippingFreightAddress : tShippingFreightAddresses) {
            if (!set.add(tShippingFreightAddress.getFromProvinceId() + "-" + tShippingFreightAddress.getFromCityId() + "-" + tShippingFreightAddress.getFromAreaId()
                    +tShippingFreightAddress.getToProvinceId() + "-" + tShippingFreightAddress.getToCityId() + "-" + tShippingFreightAddress.getToAreaId())) {
                throw new BizException(CarrierDataExceptionEnum.REPEAT_ADDRESS_EXIST);
            }
        }
    }


}
