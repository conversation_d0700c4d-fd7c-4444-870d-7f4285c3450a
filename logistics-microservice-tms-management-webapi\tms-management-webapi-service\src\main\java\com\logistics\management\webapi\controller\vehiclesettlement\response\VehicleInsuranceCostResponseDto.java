package com.logistics.management.webapi.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/6/5 17:04
 */
@Data
public class VehicleInsuranceCostResponseDto {
    @ApiModelProperty(value = "保单Id")
    private String insuranceId="";
    @ApiModelProperty(value = "险种：1 商业险，2 交强险，4 货物险，5 危货承运人险")
    private String insuranceType="";
    private String insuranceTypeLabel="";
    @ApiModelProperty(value = "保单号")
    private String policyNo="";
    @ApiModelProperty(value = "保费合计")
    private String totalCost="";
    @ApiModelProperty(value = "剩余未扣除金额")
    private String remainNotDeductionCost="";
    @ApiModelProperty(value = "应扣减费用")
    private String payCost="";
    @ApiModelProperty(value = "未扣减费用")
    private String unPaidCost="";
}
