package com.logistics.management.webapi.controller.freightconfig.response.address;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class CarrierFreightConfigAddressItemResponseDto {

    @ApiModelProperty(value = "路线计价配置Id")
    private String freightConfigAddressId = "";

    @ApiModelProperty(value = "状态; 1 启用 0 禁用")
    private String enabled = "";

    @ExcelProperty("状态")
    @ApiModelProperty(value = "状态展示文本")
    private String enabledLabel = "";

    @ExcelProperty("发货市")
    @ApiModelProperty(value = "发货城市名字")
    private String fromCityName = "";

    @ExcelProperty("发货区")
    @ApiModelProperty(value = "发货县区名字")
    private String fromAreaName = "";

    @ExcelProperty("收货市")
    @ApiModelProperty(value = "卸货城市名字")
    private String toCityName = "";

    @ExcelProperty("收货区")
    @ApiModelProperty(value = "卸货县区名字")
    private String toAreaName = "";

    @ExcelProperty("操作人")
    @ApiModelProperty(value = "操作人")
    private String lastModifiedBy = "";

    @ExcelProperty("操作时间")
    @ApiModelProperty(value = "操作时间")
    private String lastModifiedTime = "";
}
