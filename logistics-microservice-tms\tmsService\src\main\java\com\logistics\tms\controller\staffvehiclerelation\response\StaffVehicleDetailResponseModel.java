package com.logistics.tms.controller.staffvehiclerelation.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/7/26 13:31
 */
@Data
public class StaffVehicleDetailResponseModel {
    @ApiModelProperty("车辆司机ID")
    private Long staffVehicleRelationId;
    @ApiModelProperty("类型 1 内部（自有） 2 外部")
    private Integer type;
    @ApiModelProperty("车辆类别: 1 牵引车 2 挂车 3 一体车")
    private Integer vehicleCategory;
    @ApiModelProperty("牵引车车牌号")
    private String tractorVehicleNo;
    @ApiModelProperty("牵引车ID")
    private Long tractorVehicleId;
    @ApiModelProperty("挂车车牌号")
    private String trailerVehicleNo;
    @ApiModelProperty("挂车ID")
    private Long trailerVehicleId;
    @ApiModelProperty("司机ID")
    private Long staffId;
    @ApiModelProperty("司机名称")
    private String staffName;
    @ApiModelProperty("司机电话")
    private String staffPhoneNumber;
    @ApiModelProperty("备注")
    private String remark;
}
