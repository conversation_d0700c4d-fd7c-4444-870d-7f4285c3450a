package com.logistics.management.webapi.client.vehicleassetmanagement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/6/3 11:16
 */
@Data
public class FuzzyQueryVehicleInfoRequestModel {

    @ApiModelProperty("类型 1 自主，2 外部，3 自营 多个以,分割")
    private String type;

    @ApiModelProperty("车辆类别 1 牵引车 2 挂车 3 一体车,默认为空")
    private String vehicleCategory;

    @ApiModelProperty("模糊匹配字段-车牌号")
    private String fuzzyVehicleField;

    @ApiModelProperty("请求来源： 1 贷款模块, 2 二级承运商关联司机模块 ,未特殊说明默认为空")
    private Integer source;

    @ApiModelProperty("车主ID")
    private Long companyCarrierId;

    @ApiModelProperty("需要排除的车辆id")
    private List<Long> notInVehicleIds;

    //车辆运营状态：1 运营中，2 已停运，3 过户，4 报废 我司车辆才有用
    private Integer operatingState;

    //是否前台请求 1:是
    private Integer isWebRequest;
}
