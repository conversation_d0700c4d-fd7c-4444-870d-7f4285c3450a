package com.logistics.management.webapi.api.impl.freight.mapping;

import com.logistics.management.webapi.api.feign.freight.dto.SearchFreightAddressResponseDto;
import com.logistics.management.webapi.base.enums.CalcTypeEnum;
import com.logistics.tms.api.feign.freight.model.SearchFreightAddressResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.util.Optional;

/**
 * @Author: sj
 * @Date: 2019/12/26 17:46
 */
public class SearchFreightAddressMapping extends MapperMapping<SearchFreightAddressResponseModel,SearchFreightAddressResponseDto> {
    @Override
    public void configure() {
        SearchFreightAddressResponseModel model = this.getSource();
        SearchFreightAddressResponseDto dto = this.getDestination();
        if(model!=null){
            dto.setCalcTypeLabel(CalcTypeEnum.getEnum(model.getCalcType()).getValue());
            dto.setFromProvinceAndCityLabel(Optional.ofNullable(model.getFromProvinceName()).orElse("") + Optional.ofNullable(model.getFromCityName()).orElse(""));
            dto.setToProvinceAndCityLabel(Optional.ofNullable(model.getToProvinceName()).orElse("") + Optional.ofNullable(model.getToCityName()).orElse(""));
        }
    }
}
