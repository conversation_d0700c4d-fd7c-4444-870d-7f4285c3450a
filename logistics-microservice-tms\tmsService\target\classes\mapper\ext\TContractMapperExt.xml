<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TContractMapper" >
    <select id="getContractDetail" resultMap="getContractDetailMap">
      select
        tc.id,
        tc.contract_no_external,
        tc.contract_no_internal,
        tc.contract_header,
        tc.contract_nature,
        tc.contract_object_id,
        tc.contract_start_time,
        tc.contract_end_time,
        tc.contract_status,
        tc.contract_type,
        tc.remark,
        tc.customer_company_name as contractObject,
        tcf.id as contractFileId,
        tcf.contract_file_path,
        tol.id as operateLogsId,
        tol.operate_type,
        tol.operate_contents,
        tol.operate_user_name,
        tol.operate_time,
        tol.remark as logRemark
      from t_contract tc
      left join t_contract_file tcf on tc.id = tcf.contract_id and tcf.valid = 1
      left join t_operate_logs tol on tc.id = tol.object_id and tol.valid = 1 and tol.object_type = 6
      where tc.valid = 1
      and tc.id =#{contractId,jdbcType=BIGINT}
      order by tcf.id desc,tol.operate_time desc,tol.id desc
    </select>

    <resultMap id="getContractDetailMap"
               type="com.logistics.tms.api.feign.contractorder.model.ContractOrderDetailResponseModel">
        <id column="id" property="contractId" jdbcType="BIGINT"/>
        <result column="contract_no_internal" property="contractNoInternal" jdbcType="VARCHAR"/>
        <result column="contract_no_external" property="contractNoExternal" jdbcType="VARCHAR"/>
        <result column="contract_nature" property="contractNature" jdbcType="INTEGER"/>
        <result column="contract_status" property="contractStatus" jdbcType="INTEGER"/>
        <result column="contract_type" property="contractType" jdbcType="INTEGER"/>
        <result column="contract_object_id" property="contractObjectId" jdbcType="BIGINT"/>
        <result column="contractObject" property="contractObject" jdbcType="BIGINT"/>
        <result column="contract_header" property="contractHeader" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="contractObject" property="contractObject" jdbcType="VARCHAR"/>
        <result column="contract_start_time" property="contractStartTime" jdbcType="TIMESTAMP"/>
        <result column="contract_end_time" property="contractEndTime" jdbcType="TIMESTAMP"/>

        <collection property="contractLogs"
                    ofType="com.logistics.tms.api.feign.contractorder.model.ContractLogsResponseModel"
                    javaType="ArrayList">
            <id column="operateLogsId" property="operateLogsId" jdbcType="BIGINT"/>
            <result column="operate_user_name" property="operateUserName" jdbcType="VARCHAR"/>
            <result column="operate_time" property="operateTime" jdbcType="TIMESTAMP"/>
            <result column="operate_type" property="operateType" jdbcType="INTEGER"/>
            <result column="operate_contents" property="operateContents" jdbcType="VARCHAR"/>
            <result column="logRemark" property="remark" jdbcType="VARCHAR"/>
        </collection>

        <collection property="contractFiles" ofType="com.logistics.tms.api.feign.contractorder.model.ContractFileResponseModel"
                    javaType="ArrayList">
            <id column="contractFileId" property="contractFileId" jdbcType="BIGINT"/>
            <result column="contract_file_path" property="contractFilePath" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>
    <select id="searchContractOrderList" resultType="com.logistics.tms.api.feign.contractorder.model.ContractOrderSearchResponseModel">
        SELECT
        tc.id as contractId,
        tc.contract_no_internal as contractNoInternal,
        tc.contract_no_external as contractNoExternal,
        tc.contract_status as contractStatus,
        tc.contract_type as contractType,
        tc.contract_nature as contractNature,
        tc.contract_start_time as contractStartTime,
        tc.contract_end_time as contractEndTime,
        tc.last_modified_by as lastModifiedBy,
        tc.last_modified_time as lastModifiedTime,
        tc.customer_company_name as customerName
        FROM
        t_contract tc
        WHERE tc.valid = 1
        <if test="params.contractNoInternal!=null and params.contractNoInternal!=''">
            and instr(tc.contract_no_internal,#{params.contractNoInternal,jdbcType=VARCHAR})
        </if>
        <if test="params.contractNoExternal!=null and params.contractNoExternal!=''">
            and instr(tc.contract_no_external,#{params.contractNoExternal,jdbcType=VARCHAR})
        </if>
        <if test="params.contractStatus!=null">
            and tc.contract_status = #{params.contractStatus,jdbcType=INTEGER}
        </if>
        <if test="params.contractType!=null">
            and tc.contract_type = #{params.contractType,jdbcType=INTEGER}
        </if>
        <if test="params.contractNature!=null and params.contractNature!=''">
            and tc.contract_nature = #{params.contractNature,jdbcType=INTEGER}
        </if>
        <if test="params.customerName!=null and params.customerName!=''">
            and (instr(tc.customer_company_name,#{params.customerName,jdbcType=VARCHAR}))
        </if>
        order by tc.last_modified_time desc,tc.id desc
    </select>
    
    <select id="getValidContract" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_contract
        where valid = 1
        and contract_status in (1,2)
    </select>

    <update id="batchUpdate" parameterType="com.logistics.tms.entity.TContract">
        <foreach collection="list" item="item" separator=";">
            update t_contract
            <set>
                <if test="item.contractNoInternal != null">
                    contract_no_internal = #{item.contractNoInternal,jdbcType=VARCHAR},
                </if>
                <if test="item.contractNoExternal != null">
                    contract_no_external = #{item.contractNoExternal,jdbcType=VARCHAR},
                </if>
                <if test="item.contractStatus != null">
                    contract_status = #{item.contractStatus,jdbcType=INTEGER},
                </if>
                <if test="item.contractType != null">
                    contract_type = #{item.contractType,jdbcType=INTEGER},
                </if>
                <if test="item.contractNature != null">
                    contract_nature = #{item.contractNature,jdbcType=INTEGER},
                </if>
                <if test="item.contractObjectId != null">
                    contract_object_id = #{item.contractObjectId,jdbcType=BIGINT},
                </if>
                <if test="item.contractHeader != null">
                    contract_header = #{item.contractHeader,jdbcType=VARCHAR},
                </if>
                <if test="item.contractStartTime != null">
                    contract_start_time = #{item.contractStartTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.contractEndTime != null">
                    contract_end_time = #{item.contractEndTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.endingCancelBy != null">
                    ending_cancel_by = #{item.endingCancelBy,jdbcType=VARCHAR},
                </if>
                <if test="item.endingCancelRemark != null">
                    ending_cancel_remark = #{item.endingCancelRemark,jdbcType=VARCHAR},
                </if>
                <if test="item.endingCancelDatetime != null">
                    ending_cancel_datetime = #{item.endingCancelDatetime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>