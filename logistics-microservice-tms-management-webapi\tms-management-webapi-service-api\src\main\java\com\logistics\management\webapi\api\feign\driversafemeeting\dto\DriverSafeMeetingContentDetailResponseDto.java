package com.logistics.management.webapi.api.feign.driversafemeeting.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/11/4 9:26
 */
@Data
public class DriverSafeMeetingContentDetailResponseDto {
    @ApiModelProperty("学习例会id")
    private String safeMeetingId="";
    @ApiModelProperty(value = "驾驶员数量")
    private String driverCount="";
    @ApiModelProperty(value = "学习月份")
    private String period="";
    @ApiModelProperty(value = "学习标题")
    private String title="";
    @ApiModelProperty(value = "学习简介")
    private String introduction="";
    @ApiModelProperty(value = "例会发布时间")
    private String createdTime="";
    @ApiModelProperty(value = "学习内容")
    private String content="";
    @ApiModelProperty(value = "驾驶员")
    private List<String> driverIdList;
    @ApiModelProperty(value = "是否可补发：0 否，1 是")
    private String ifReissue="0";
}
