package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleSettlementRelation extends BaseEntity {
    /**
    * 结算id
    */
    @ApiModelProperty("结算id")
    private Long vehicleSettlementId;

    /**
    * 费用类型：10 gps，20 停车，30 贷款，40 轮胎，50 充油，51 油费退款，60 保险，61 保险退保，70 运费
    */
    @ApiModelProperty("费用类型：10 gps，20 停车，30 贷款，40 轮胎，50 充油，51 油费退款，60 保险，61 保险退保，70 运费")
    private Integer objectType;

    /**
    * 费用id
    */
    @ApiModelProperty("费用id")
    private Long objectId;
}