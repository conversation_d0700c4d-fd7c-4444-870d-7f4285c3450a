package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2023/04/14
*/
@Data
public class TWorkOrder extends BaseEntity {
    /**
    * 状态：0 待处理，10 处理中，20 已处理，30 已关闭，40 已撤销
    */
    @ApiModelProperty("状态：0 待处理，10 处理中，20 已处理，30 已关闭，40 已撤销")
    private Integer status;

    /**
    * 工单优先级：1,2,3,4,5（数字越小，优先级越高）
    */
    @ApiModelProperty("工单优先级：1,2,3,4,5（数字越小，优先级越高）")
    private Integer workOrderPriority;

    /**
    * 工单编号（uuid）
    */
    @ApiModelProperty("工单编号（uuid）")
    private String workOrderCode;

    /**
    * 工单类型：10 需求单，20 运单
    */
    @ApiModelProperty("工单类型：10 需求单，20 运单")
    private Integer workOrderType;

    /**
    * 需求单id
    */
    @ApiModelProperty("需求单id")
    private Long demandOrderId;

    /**
    * 需求单号
    */
    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    /**
    * 运单id
    */
    @ApiModelProperty("运单id")
    private Long carrierOrderId;

    /**
    * 运单号
    */
    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    /**
    * 异常类型（一级）：10 联系不上客户，20 不想还盘，30 不可抗力，40 重复下单
    */
    @ApiModelProperty("异常类型（一级）：10 联系不上客户，20 不想还盘，30 不可抗力，40 重复下单")
    private Integer anomalyTypeOne;

    /**
    * 异常类型（二级）：一级下的选项，详见枚举
    */
    @ApiModelProperty("异常类型（二级）：一级下的选项，详见枚举")
    private Integer anomalyTypeTwo;

    /**
    * 联系人姓名
    */
    @ApiModelProperty("联系人姓名")
    private String contactName;

    /**
    * 联系人电话
    */
    @ApiModelProperty("联系人电话")
    private String contactTelephone;

    /**
    * 核验联系方式: 1 无误，2 有误
    */
    @ApiModelProperty("核验联系方式: 1 无误，2 有误")
    private Integer checkContact;

    /**
    * 司机是否到达现场：0 否，1 是
    */
    @ApiModelProperty("司机是否到达现场：0 否，1 是")
    private Integer isArriveScene;

    /**
    * 到达现场图片
    */
    @ApiModelProperty("到达现场图片")
    private String arriveScenePicture;

    /**
    * 地址抬头
    */
    @ApiModelProperty("地址抬头")
    private String addressHead;

    /**
    * 地址详情
    */
    @ApiModelProperty("地址详情")
    private String addressDetail;

    /**
    * 提报来源：1 后台，2 前台，3 小程序
    */
    @ApiModelProperty("提报来源：1 后台，2 前台，3 小程序")
    private Integer reportSource;

    /**
    * 提报人
    */
    @ApiModelProperty("提报人")
    private String reportUserName;

    /**
    * 提报时间
    */
    @ApiModelProperty("提报时间")
    private Date reportTime;

    /**
    * 处理人
    */
    @ApiModelProperty("处理人")
    private String solveUserName;

    /**
    * 处理时间
    */
    @ApiModelProperty("处理时间")
    private Date solveTime;

    /**
    * 车主公司类型：1 公司，2 个人
    */
    @ApiModelProperty("车主公司类型：1 公司，2 个人")
    private Integer companyCarrierType;

    /**
    * 车主公司ID
    */
    @ApiModelProperty("车主公司ID")
    private Long companyCarrierId;

    /**
    * 车主公司名称
    */
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName;

    /**
    * 车主联系人/账号id
    */
    @ApiModelProperty("车主联系人/账号id")
    private Long carrierContactId;

    /**
    * 车主账号名称
    */
    @ApiModelProperty("车主账号名称")
    private String carrierContactName;

    /**
    * 车主账号手机号（原长度50）
    */
    @ApiModelProperty("车主账号手机号（原长度50）")
    private String carrierContactPhone;

    /**
    * 司机ID
    */
    @ApiModelProperty("司机ID")
    private Long driverId;

    /**
    * 司机姓名
    */
    @ApiModelProperty("司机姓名")
    private String driverName;

    /**
    * 司机手机号
    */
    @ApiModelProperty("司机手机号")
    private String driverMobile;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}