package com.logistics.management.webapi.controller.companyentrust.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/9/27 14:24
 */
@Data
public class CompanyEntrustDetailResponseDto {

    @ApiModelProperty("货主类型: 1 公司 2 个人")
    private String type="";
    @ApiModelProperty("货主类型文本")
    private String typeLabel="";
    @ApiModelProperty("公司id")
    private String companyEntrustId="";
    @ApiModelProperty("公司名字")
    private String companyName="";
    @ApiModelProperty("简称")
    private String companyShortName="";
    @ApiModelProperty("营业执照图片相对")
    private String fileSrcPathTradingCertificateImage="";
    @ApiModelProperty("营业执照图片绝对")
    private String fileTargetPathTradingCertificateImage="";
    @ApiModelProperty("营业执照有效期")
    private String tradingCertificateValidityTime="";
    @ApiModelProperty("营业执照是否永久 0否1是")
    private String tradingCertificateIsForever="";
    @ApiModelProperty("营业执照是否后补 0否1是")
    private String tradingCertificateIsAmend="";
    @ApiModelProperty("结算吨位")
    private String settlementTonnage="";
    @ApiModelProperty("修改车辆审核")
    private String ifAudit = "";
    private String ifAuditDesc = "";
    @ApiModelProperty("签收模式")
    private String signMode="";
    private String signModeDesc="";
    @ApiModelProperty("备注")
    private String remark="";
}
