package com.logistics.tms.api.feign.organization.hystrix;

import com.logistics.tms.api.feign.organization.IOrganizationServiceApi;
import com.logistics.tms.api.feign.organization.model.IOrganizationNameResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class IOrganizationServiceApiHystrix implements IOrganizationServiceApi {

    @Override
    public Result<List<IOrganizationNameResponseModel>> getAllOrgForHierarchy() {
        return Result.timeout();
    }
}
