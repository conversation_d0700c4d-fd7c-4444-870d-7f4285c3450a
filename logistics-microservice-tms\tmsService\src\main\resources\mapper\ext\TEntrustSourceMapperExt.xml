<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TEntrustSourceMapper" >
    <select id="getByMobile" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_entrust_source
        where valid = 1
        and contact_mobile = #{mobile,jdbcType=VARCHAR}
    </select>
</mapper>