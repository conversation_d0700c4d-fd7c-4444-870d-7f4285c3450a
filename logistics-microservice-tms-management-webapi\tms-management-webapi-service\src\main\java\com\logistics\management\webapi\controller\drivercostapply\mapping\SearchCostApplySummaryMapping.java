package com.logistics.management.webapi.controller.drivercostapply.mapping;

import com.logistics.management.webapi.base.enums.DriverCostApplyTypeEnum;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.enums.StaffPropertyEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.drivercostapply.response.SearchCostApplySummaryListResponseModel;
import com.logistics.management.webapi.client.drivercostapply.response.SearchCostApplySummaryResponseModel;
import com.logistics.management.webapi.controller.drivercostapply.response.SearchCostApplySummaryResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2022/8/11 14:07
 */
public class SearchCostApplySummaryMapping extends MapperMapping<SearchCostApplySummaryResponseModel, SearchCostApplySummaryResponseDto> {
    @Override
    public void configure() {
        SearchCostApplySummaryResponseModel source = getSource();
        SearchCostApplySummaryResponseDto destination = getDestination();

        destination.setStaffName(source.getStaffName() + "_" + (FrequentMethodUtils.encryptionData(source.getStaffMobile(), EncodeTypeEnum.MOBILE_PHONE)));
        destination.setExportStaffName(source.getStaffName() + "_" + source.getStaffMobile());
        destination.setStaffPropertyLabel(StaffPropertyEnum.getEnum(source.getStaffProperty()).getValue());

        BigDecimal costTotal = BigDecimal.ZERO;
        Map<Integer, BigDecimal> costMap = new HashMap<>();
        for (SearchCostApplySummaryListResponseModel model : source.getCostList()) {
            costTotal = costTotal.add(model.getApplyCost());

            //累加各种费用的和
            BigDecimal bigDecimal = costMap.get(model.getCostType());
            if (bigDecimal == null) {
                costMap.put(model.getCostType(), model.getApplyCost());
            } else {
                costMap.put(model.getCostType(), bigDecimal.add(model.getApplyCost()));
            }
        }

        BigDecimal accommodationFee = costMap.get(DriverCostApplyTypeEnum.ACCOMMODATION_FEE.getKey());
        BigDecimal loadingFee = costMap.get(DriverCostApplyTypeEnum.LOAD_AND_UNLOAD_FEE.getKey());
        BigDecimal otherFee = costMap.get(DriverCostApplyTypeEnum.OTHER_FEE.getKey());
        BigDecimal overtimePay = costMap.get(DriverCostApplyTypeEnum.OVERTIME_FEE.getKey());
        BigDecimal laborSecurityExpense = costMap.get(DriverCostApplyTypeEnum.LABOUR_PROTECTION_FEE.getKey());
        BigDecimal telephoneCharges = costMap.get(DriverCostApplyTypeEnum.TELEPHONE_BILL.getKey());
        BigDecimal transportationFee = costMap.get(DriverCostApplyTypeEnum.TRAFFIC_FEE.getKey());
        BigDecimal maintenanceCost = costMap.get(DriverCostApplyTypeEnum.MAINTAIN_FEE.getKey());
        BigDecimal vehicleMaintenanceCost = costMap.get(DriverCostApplyTypeEnum.VEHICLE_MAINTENANCE_FEE.getKey());
        BigDecimal tollFee = costMap.get(DriverCostApplyTypeEnum.ROAD_TOLL.getKey());
        BigDecimal parkingFee = costMap.get(DriverCostApplyTypeEnum.PARKING_FEE.getKey());
        BigDecimal ureaFee = costMap.get(DriverCostApplyTypeEnum.UREA_FEE.getKey());
        BigDecimal oilFee = costMap.get(DriverCostApplyTypeEnum.OIL_FEE.getKey());
        BigDecimal nucleicAcidTestingFee = costMap.get(DriverCostApplyTypeEnum.NAT_FEE.getKey());
        BigDecimal medicalSuppliesFee = costMap.get(DriverCostApplyTypeEnum.MEDICAL_SUPPLIES_FEE.getKey());
        BigDecimal printFee = costMap.get(DriverCostApplyTypeEnum.PRINTING_FEE.getKey());
        BigDecimal forkliftsFee = costMap.get(DriverCostApplyTypeEnum.FORKLIFT_FEE.getKey());
        BigDecimal waterproofFee = costMap.get(DriverCostApplyTypeEnum.COVER_TARPAULIN.getKey());
        BigDecimal brokenPackageFee = costMap.get(DriverCostApplyTypeEnum.DAMAGE_WRAP_INDEMNIFY.getKey());
        BigDecimal deductions = costMap.get(DriverCostApplyTypeEnum.DEDUCTIONS.getKey());
        BigDecimal TrafficFine = costMap.get(DriverCostApplyTypeEnum.TRAFFIC_FINE.getKey());

        destination.setCostTotal(ConverterUtils.toString(costTotal.setScale(2, RoundingMode.HALF_UP)));
        destination.setAccommodationFee(ConverterUtils.toString(accommodationFee != null ? accommodationFee.setScale(2, RoundingMode.HALF_UP):""));
        destination.setLoadingFee(ConverterUtils.toString(loadingFee != null ? loadingFee.setScale(2, RoundingMode.HALF_UP):""));
        destination.setOtherFee(ConverterUtils.toString(otherFee != null ? otherFee.setScale(2, RoundingMode.HALF_UP):""));
        destination.setOvertimePay(ConverterUtils.toString(overtimePay != null ? overtimePay.setScale(2, RoundingMode.HALF_UP):""));
        destination.setLaborSecurityExpense(ConverterUtils.toString(laborSecurityExpense != null ? laborSecurityExpense.setScale(2, RoundingMode.HALF_UP):""));
        destination.setTelephoneCharges(ConverterUtils.toString(telephoneCharges != null ? telephoneCharges.setScale(2, RoundingMode.HALF_UP):""));
        destination.setTransportationFee(ConverterUtils.toString(transportationFee != null ? transportationFee.setScale(2, RoundingMode.HALF_UP):""));
        destination.setMaintenanceCost(ConverterUtils.toString(maintenanceCost != null ? maintenanceCost.setScale(2, RoundingMode.HALF_UP):""));
        destination.setVehicleMaintenanceCost(ConverterUtils.toString(vehicleMaintenanceCost != null ? vehicleMaintenanceCost.setScale(2, RoundingMode.HALF_UP):""));
        destination.setTollFee(ConverterUtils.toString(tollFee != null ? tollFee.setScale(2, RoundingMode.HALF_UP):""));
        destination.setParkingFee(ConverterUtils.toString(parkingFee != null ? parkingFee.setScale(2, RoundingMode.HALF_UP):""));
        destination.setUreaFee(ConverterUtils.toString(ureaFee != null ? ureaFee.setScale(2, RoundingMode.HALF_UP):""));
        destination.setOilFee(ConverterUtils.toString(oilFee != null ? oilFee.setScale(2, RoundingMode.HALF_UP):""));
        destination.setNucleicAcidTestingFee(ConverterUtils.toString(nucleicAcidTestingFee != null ? nucleicAcidTestingFee.setScale(2, RoundingMode.HALF_UP):""));
        destination.setMedicalSuppliesFee(ConverterUtils.toString(medicalSuppliesFee != null ? medicalSuppliesFee.setScale(2, RoundingMode.HALF_UP):""));

        destination.setPrintingFee(ConverterUtils.toString(printFee != null ? printFee.setScale(2, RoundingMode.HALF_UP):""));
        destination.setForkliftFee(ConverterUtils.toString(forkliftsFee != null ? forkliftsFee.setScale(2, RoundingMode.HALF_UP):""));
        destination.setCoverTarpaulin(ConverterUtils.toString(waterproofFee != null ? waterproofFee.setScale(2, RoundingMode.HALF_UP):""));
        destination.setDamageWrapIndemnify(ConverterUtils.toString(brokenPackageFee != null ? brokenPackageFee.setScale(2, RoundingMode.HALF_UP):""));

        destination.setDeductions(ConverterUtils.toString(deductions != null ? deductions.setScale(2, RoundingMode.HALF_UP):""));
        destination.setTrafficFine(ConverterUtils.toString(TrafficFine != null ? TrafficFine.setScale(2, RoundingMode.HALF_UP):""));
    }
}
