package com.logistics.management.webapi.controller.routeenquiry.response;

import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/7/8 15:18
 */
@Data
public class SearchRouteEnquiryListResponseDto {

    /**
     * 路线询价单表id
     */
    private String routeEnquiryId="";

    /**
     * 竞价单号
     */
    private String orderCode="";

    /**
     * 竞价状态：1 待业务审核，2 待车主确认，3 待结算审核，4 完成竞价，5 竞价取消
     */
    private String status="";
    /**
     * 竞价状态
     */
    private String statusLabel="";

    /**
     * 归档
     */
    private String ifArchiveLabel="";

    /**
     * 路线数
     */
    private String addressCount="";

    /**
     * 报价承运商数
     */
    private String quotedCarrierCount="";

    /**
     * 中标承运商
     */
    private String companyCarrierName="";

    /**
     * 报价生效开始时间
     */
    private String quoteStartTime="";

    /**
     * 报价生效结束时间
     */
    private String quoteEndTime="";

    /**
     * 关联合同号
     */
    private String contractCode="";

    /**
     * 审核人
     */
    private String auditorNameOne="";

    /**
     * 审核时间
     */
    private String auditTimeOne="";

    /**
     * 结算审核人
     */
    private String auditorNameTwo="";

    /**
     * 结算审核时间
     */
    private String auditTimeTwo="";

}
