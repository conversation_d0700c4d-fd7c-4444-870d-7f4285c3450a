<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRealNameAuthenticationMapper">
    <sql id="Base_Column_List_Decrypt">
        id, bestsign_account, authentication_type, name,
        AES_DECRYPT(UNHEX(mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as mobile,
        AES_DECRYPT(UNHEX(identity_number), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as identity_number
        ,certification_status,auth_mode, created_by, created_time, last_modified_by, last_modified_time, valid
    </sql>

    <insert id="insertSelectiveEncrypt" useGeneratedKeys="true" keyProperty="id">
        insert into t_real_name_authentication
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="bestsignAccount != null">
                bestsign_account,
            </if>
            <if test="authenticationType != null">
                authentication_type,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="identityNumber != null">
                identity_number,
            </if>
            <if test="certificationStatus != null">
                certification_status,
            </if>
            <if test="authMode != null">
                auth_mode,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by,
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time,
            </if>
            <if test="valid != null">
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="bestsignAccount != null">
                #{bestsignAccount,jdbcType=VARCHAR},
            </if>
            <if test="authenticationType != null">
                #{authenticationType,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                HEX(AES_ENCRYPT(#{mobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="identityNumber != null">
                HEX(AES_ENCRYPT(#{identityNumber,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="certificationStatus != null">
                #{certificationStatus,jdbcType=INTEGER},
            </if>
            <if test="authMode != null">
                #{authMode,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <select id="selectRealNameAuthenticationByMobile" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_real_name_authentication
        where valid = 1
        and AES_DECRYPT(UNHEX(mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') = #{mobile,jdbcType=VARCHAR}
    </select>

    <select id="selectRealNameByMobiles" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_real_name_authentication
        where valid = 1
        <choose>
            <when test="mobiles != null and mobiles.size() != 0">
                and AES_DECRYPT(UNHEX(mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') in
                <foreach collection="mobiles" open="(" separator="," close=")" item="item">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_real_name_authentication
        where valid = 1
        <choose>
            <when test="ids != null and ids.size() != 0">
                and id in
                <foreach collection="ids" open="(" separator="," close=")" item="item">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </select>

    <update id="updateByPrimaryKeySelectiveEncrypt">
        update t_real_name_authentication
        <set>
            <if test="bestsignAccount != null">
                bestsign_account = #{bestsignAccount,jdbcType=VARCHAR},
            </if>
            <if test="authenticationType != null">
                authentication_type = #{authenticationType,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                mobile = HEX(AES_ENCRYPT(#{mobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="identityNumber != null">
                identity_number = HEX(AES_ENCRYPT(#{identityNumber,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="certificationStatus != null">
                certification_status = #{certificationStatus,jdbcType=INTEGER},
            </if>
            <if test="authMode != null">
                auth_mode = #{authMode,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateCertificationStatus">
        update t_real_name_authentication
        <set>
            <if test="entity.certificationStatus != null">
                certification_status = #{entity.certificationStatus},
            </if>
            <if test="entity.authMode != null">
                auth_mode = #{entity.authMode},
            </if>
            <if test="entity.lastModifiedBy != null">
                last_modified_by = #{entity.lastModifiedBy},
            </if>
            <if test="entity.lastModifiedTime != null">
                last_modified_time = #{entity.lastModifiedTime},
            </if>
        </set>
        where id = #{entity.id} and certification_status = #{currentStatus}
    </update>

    <update id="delRealNameAuthenticationByMobile">
        update t_real_name_authentication
        set valid =0
        where valid = 1
        <choose>
            <when test="mobiles != null and mobiles.size() != 0">
                and AES_DECRYPT(UNHEX(mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') in
                <foreach collection="mobiles" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </update>
</mapper>