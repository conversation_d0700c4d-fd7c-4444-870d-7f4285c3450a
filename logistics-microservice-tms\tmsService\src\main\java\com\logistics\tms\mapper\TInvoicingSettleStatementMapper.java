package com.logistics.tms.mapper;

import com.logistics.tms.biz.invoicingmanagement.model.GetReconciliationFeeModel;
import com.logistics.tms.controller.invoicingmanagement.request.GetSettleStatementListRequestModel;
import com.logistics.tms.controller.invoicingmanagement.response.GetSettleStatementListResponseModel;
import com.logistics.tms.entity.TInvoicingSettleStatement;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
* Created by Mybatis Generator on 2024/03/20
*/
@Mapper
public interface TInvoicingSettleStatementMapper extends BaseMapper<TInvoicingSettleStatement> {

    @MapKey("invoicingId")
    Map<Long, GetReconciliationFeeModel> getReconciliationFeeByInvoicingIds(@Param("invoicingIds") Collection<Long> invoicingIds);

    List<TInvoicingSettleStatement> getByInvoicingIds(@Param("invoicingIds") Collection<Long> invoicingIds);

    List<Long> getSettleStatementIdList(@Param("params") GetSettleStatementListRequestModel requestModel);

    List<GetSettleStatementListResponseModel> getSettleStatementList(@Param("ids")List<Long> ids);

}