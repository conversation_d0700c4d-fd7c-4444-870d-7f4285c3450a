package com.logistics.appapi.controller.bankcard;

import com.logistics.appapi.controller.common.CommonBiz;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.constant.ConfigKeyConstant;
import com.logistics.appapi.base.enums.VerificationCodeSourceTypeEnum;
import com.logistics.appapi.base.enums.VerificationCodeTypeEnum;
import com.logistics.appapi.client.driveraccount.DriverAccountClient;
import com.logistics.appapi.client.driveraccount.request.AddBankCardAppletRequestModel;
import com.logistics.appapi.client.driveraccount.request.DriverAccountDetailRequestModel;
import com.logistics.appapi.client.driveraccount.response.BankCardInfoResponseModel;
import com.logistics.appapi.client.driveraccount.response.DriverAccountDetailResponseModel;
import com.logistics.appapi.controller.bankcard.mapping.BankCardDetailMapping;
import com.logistics.appapi.controller.bankcard.request.AddBankCardRequestDto;
import com.logistics.appapi.controller.bankcard.request.BankCardDetailRequestDto;
import com.logistics.appapi.controller.bankcard.response.BankCardDetailResponseDto;
import com.logistics.appapi.controller.bankcard.response.BankCardInfoResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2024/3/11 9:29
 */
@Api(value = "银行卡管理", tags = "银行卡管理")
@RestController
@RequestMapping(value = "/api/driverApplet/bankCard")
public class BankCardController {

    @Resource
    private DriverAccountClient driverAccountClient;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private ConfigKeyConstant configKeyConstant;

    /**
     * 获取当前绑定的银行卡
     *
     * @return 银行卡信息
     */
    @ApiOperation(value = "当前绑定银行卡信息查询", tags = "1.0.8")
    @PostMapping(value = "/currBankCardInfo")
    public Result<BankCardInfoResponseDto> currBankCardInfo() {
        Result<BankCardInfoResponseModel> result = driverAccountClient.currBankCardInfo();
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), BankCardInfoResponseDto.class));
    }

    /**
     * 添加/更换银行卡
     *
     * @return 操作结果
     */
    @ApiOperation(value = "添加/修改银行卡", tags = "1.0.8")
    @PostMapping(value = "/addBankCard")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addBankCard(@RequestBody @Valid AddBankCardRequestDto requestDto) {
        AddBankCardAppletRequestModel requestModel = MapperUtils.mapper(requestDto, AddBankCardAppletRequestModel.class);
        requestModel.setCodeSource(VerificationCodeSourceTypeEnum.TMS_DRIVER_APPLET.getKey());
        requestModel.setCodeType(VerificationCodeTypeEnum.TMS_DRIVER_APPLET_MODIFY_BANKCARD.getKey());
        Result<Boolean> result = driverAccountClient.addBankCardForApplet(requestModel);
        result.throwException();
        return result;
    }

    /**
     * 司机银行卡账户信息
     *
     * @return 银行卡账户信息
     */
    @ApiOperation(value = "银行卡详情查询", tags = "1.0.8")
    @PostMapping(value = "/bankCardDetail")
    public Result<BankCardDetailResponseDto> bankCardDetail(@RequestBody @Valid BankCardDetailRequestDto requestDto) {
        Result<DriverAccountDetailResponseModel> result = driverAccountClient.getDetail(MapperUtils.mapper(requestDto, DriverAccountDetailRequestModel.class));
        result.throwException();
        Map<String, String> imageMap = null;
        if (result.getData() != null) {
            List<String> bankAccountImages = result.getData().getBankAccountImages();
            if (ListUtils.isNotEmpty(bankAccountImages)) {
                imageMap = commonBiz.batchGetOSSFileUrl(bankAccountImages);
            }
        }
        return Result.success(MapperUtils.mapper(result.getData(), BankCardDetailResponseDto.class, new BankCardDetailMapping(configKeyConstant, imageMap)));
    }
}
