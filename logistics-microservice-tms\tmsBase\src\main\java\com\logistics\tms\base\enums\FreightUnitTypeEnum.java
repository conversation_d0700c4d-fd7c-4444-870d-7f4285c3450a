package com.logistics.tms.base.enums;

/**
 * @Author: sj
 * @Date: 2019/12/27 18:58
 */
public enum FreightUnitTypeEnum {
    PERCENT(1, "百分比"),
    YUAN(2, "元"),;

    private Integer key;
    private String value;

    FreightUnitTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static FreightUnitTypeEnum getEnum(Integer key) {
        for (FreightUnitTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
