package com.logistics.management.webapi.client.settlestatement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CarrierModifyTaxPointRequestModel {

    @ApiModelProperty(value = "对账单id")
    private Long settleStatementId;

    @ApiModelProperty("临时费用费点")
    private BigDecimal otherFeeTaxPoint;

    @ApiModelProperty("运费费点")
    private BigDecimal freightTaxPoint;
}
