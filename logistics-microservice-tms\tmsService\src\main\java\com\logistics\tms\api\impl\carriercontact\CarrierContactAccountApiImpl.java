package com.logistics.tms.api.impl.carriercontact;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.carriercontact.CarrierContactAccountApi;
import com.logistics.tms.api.feign.carriercontact.dto.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.carriercontact.CarrierContactBiz;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class CarrierContactAccountApiImpl implements CarrierContactAccountApi {

    @Autowired
    private CarrierContactBiz carrierContactBiz;

    /**
     * 车主账号列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<SearchCarrierContactResponseModel>> searchList(@RequestBody SearchCarrierContactRequestModel requestModel) {
        List<SearchCarrierContactResponseModel> responseModelList = carrierContactBiz.searchList(requestModel);
        PageInfo<SearchCarrierContactResponseModel> pageInfo = new PageInfo<>(responseModelList);
        return Result.success(pageInfo);
    }

    /**
     * 导出车主账号
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchCarrierContactResponseModel>> exportCarrierContact(SearchCarrierContactRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        return Result.success(carrierContactBiz.searchList(requestModel));
    }

    /**
     * 车主账号查看详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<CarrierContactDetailResponseModel> getDetail(@RequestBody CarrierContactDetailRequestModel requestModel) {
        return Result.success( carrierContactBiz.getDetail(requestModel));
    }

    /**
     * 车主账号新增编辑
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> saveAccount(@RequestBody SaveCarrierContactRequestModel requestModel) {
        carrierContactBiz.saveAccount(requestModel);
        return Result.success(true);
    }

    /**
     * 禁用启用账号
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> enableDisableClosed(@RequestBody CarrierContactEnableRequestModel requestModel) {
        carrierContactBiz.enableDisableClosed(requestModel);
        return Result.success(true);
    }

    /**
     * 车主账号删除
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> delCarrierAccount(@RequestBody DelCarrierContactRequestModel requestModel) {
        carrierContactBiz.delCarrierAccount(requestModel);
        return Result.success(true);
    }
}
