package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2024/07/09
*/
@Data
public class TRouteEnquiryAttachment extends BaseEntity {
    /**
    * 路线询价单表id
    */
    @ApiModelProperty("路线询价单表id")
    private Long routeEnquiryId;

    /**
    * 附件类型：1 报价单，2 归档文件
    */
    @ApiModelProperty("附件类型：1 报价单，2 归档文件")
    private Integer attachmentType;

    /**
    * 附件路径
    */
    @ApiModelProperty("附件路径")
    private String attachmentPath;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}