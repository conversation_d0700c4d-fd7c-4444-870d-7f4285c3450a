package com.logistics.tms.api.feign.forthirdparty.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class WorkOrderProcessSyncRequestModel {

    @ApiModelProperty(value = "处理来源：1 后台，2 前台，3 小程序，4 任务中心")
    private Integer solveSource;

    @ApiModelProperty(value = "工单编号")
    private String code;

    @ApiModelProperty(value = "状态; 10 处理中，20 已处理，30 已关闭")
    private Integer status;

    @ApiModelProperty(value = "处理人")
    private String solveUserName;

    @ApiModelProperty(value = "处理时间")
    private Date solveTime;

    @ApiModelProperty(value = "处理备注")
    private String solveRemark;
}
