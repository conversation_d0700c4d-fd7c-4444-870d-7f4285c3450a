package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2022/5/12 17:52
 */
@Data
public class CompleteWarehouseModel {
    @ApiModelProperty("手动完成入库的运单号")
    private List<String> carrierOrderCodeList;
    @ApiModelProperty("请求时间戳")
    private Date currentDateTime;
    @ApiModelProperty("操作人姓名")
    private String operateUserName;
}
