package com.logistics.management.webapi.client.biddingorder.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/04/26
 */
@Data
public class BiddingOrderQuoteListResponseModel {
    /**
     * 报价单id
     */
    private Long biddingOrderQuoteId;

    /**
     * 车主
     */
    private String carrierContactName;

    /**
     * 车主 报价状态 0 未选择，1 已选择，2 已取消
     */
    private Integer quoteStatus;

    /**
     * 车主 报价状态
     */
    private String quoteStatusLabel;

    /**
     * 报价金额
     */
    private BigDecimal quotePrice;

    /**
     * 车长
     */
    private String vehicleLength;

    /**
     * 报价时间
     */
    private Date quoteTime;

}
