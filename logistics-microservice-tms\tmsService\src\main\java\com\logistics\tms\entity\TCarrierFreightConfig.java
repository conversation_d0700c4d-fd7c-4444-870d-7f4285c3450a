package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2023/06/30
*/
@Data
public class TCarrierFreightConfig extends BaseEntity {
    /**
    * 车主运价ID
    */
    @ApiModelProperty("车主运价ID")
    private Long carrierFreightId;

    /**
    * 配置类型; 1: 固定路线; 2: 区域设置; 3: 距离阶梯
    */
    @ApiModelProperty("配置类型; 1: 固定路线; 2: 区域设置; 3: 距离阶梯")
    private Integer configType;

    /**
    * 需求类型，多种类型‘,’拼接;
    */
    @ApiModelProperty("需求类型，多种类型‘,’拼接;")
    private String entrustType;
}