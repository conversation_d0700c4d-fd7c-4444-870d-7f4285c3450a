package com.logistics.management.webapi.controller.settlestatement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/9
 */
@Data
public class StatementWaitArchiveListRequestDto {

	@ApiModelProperty(value = "对账单id", required = true)
	@NotBlank(message = "id不能为空")
	private String settleStatementId;

	@ApiModelProperty(value = "运单号(支持模糊搜索)",required = true)
	@NotBlank(message = "请输入运单号")
	private String carrierOrderCode;
}
