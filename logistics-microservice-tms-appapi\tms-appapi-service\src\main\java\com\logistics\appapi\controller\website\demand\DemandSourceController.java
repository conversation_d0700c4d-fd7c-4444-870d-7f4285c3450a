package com.logistics.appapi.controller.website.demand;

import cn.dev33.satoken.annotation.SaIgnore;
import com.github.pagehelper.PageInfo;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.AppApiExceptionEnum;
import com.logistics.appapi.client.thirdparty.basicdata.BasicServiceClient;
import com.logistics.appapi.client.thirdparty.basicdata.baidu.request.CheckSensitiveWordRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.baidu.response.CheckSensitiveWordResponseModel;
import com.logistics.appapi.client.website.demand.DemandSourceClient;
import com.logistics.appapi.client.website.demand.request.AddDemandSourceRequestModel;
import com.logistics.appapi.client.website.demand.request.DemandSourceListRequestModel;
import com.logistics.appapi.client.website.demand.response.DemandSourceListResponseModel;
import com.logistics.appapi.controller.website.demand.mapping.DemandSourceListMapping;
import com.logistics.appapi.controller.website.demand.request.AddDemandSourceRequestDto;
import com.logistics.appapi.controller.website.demand.request.DemandSourceListRequestDto;
import com.logistics.appapi.controller.website.demand.response.DemandSourceListResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.utils.RedisUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.ExtFrequency;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.annocation.LimitedExtFrequency;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/15 9:47
 */
@Api(value = "云途官网-需求单")
@RestController
public class DemandSourceController {

    @Resource
    private DemandSourceClient demandSourceClient;
    @Resource
    private BasicServiceClient basicServiceClient;
    @Resource
    private RedisUtils redisUtils;

    /**
     * 货源列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "货源列表接口")
    @PostMapping(value = "/api/demandSource/searchList")
    @SaIgnore
    public Result<PageInfo<DemandSourceListResponseDto>> searchList(@RequestBody DemandSourceListRequestDto requestDto) {
        Result<PageInfo<DemandSourceListResponseModel>> pageInfoResult = demandSourceClient.searchList(MapperUtils.mapper(requestDto, DemandSourceListRequestModel.class));
        pageInfoResult.throwException();
        PageInfo pageInfoResultData = pageInfoResult.getData();
        List<DemandSourceListResponseDto> responseDtoList = MapperUtils.mapper(pageInfoResultData.getList(), DemandSourceListResponseDto.class, new DemandSourceListMapping());
        pageInfoResultData.setList(responseDtoList);
        return Result.success(pageInfoResultData);
    }

    /**
     * 发布货源
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发布货源")
    @PostMapping(value = "/api/demandSource/add")
    @SaIgnore
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @LimitedExtFrequency(limiteds = {@ExtFrequency(params = "sourceIp", count = 100, type = ExtFrequency.TYPE_DAY_DAWN, message = "非法访问,访问超过限定次数!")})
    public Result<Boolean> add(@RequestBody @Valid AddDemandSourceRequestDto requestDto) {
        String verPic = (String) redisUtils.get(CommonConstant.TMS_API_PICTURE_VERIFICATION_CODE_PREFIX + "_" + requestDto.getUuid());
        if (StringUtils.isBlank(verPic) || !verPic.equalsIgnoreCase(requestDto.getPictureVerificationCode())) {
            throw new BizException(AppApiExceptionEnum.USER_PIC_VERIFICATION_ERROR);
        }
        CheckSensitiveWordRequestModel checkSensitiveWordRequestModel = new CheckSensitiveWordRequestModel();
        checkSensitiveWordRequestModel.setContent(requestDto.getGoodsName() + "&" + requestDto.getContactName() +"&"+ requestDto.getLoadAddress() + requestDto.getUnloadAddress());
        CheckSensitiveWordResponseModel sensitiveWordResponseModelResultData = basicServiceClient.checkSensitiveWord(checkSensitiveWordRequestModel);
        if (sensitiveWordResponseModelResultData != null && CommonConstant.INTEGER_ONE.equals(sensitiveWordResponseModelResultData.getIfSensitive())) {
            throw new BizException(AppApiExceptionEnum.NOT_ALLOW_SUBMIT_SENSITIVE_WORD);
        }
        Result<Boolean> addResult = demandSourceClient.add(MapperUtils.mapper(requestDto, AddDemandSourceRequestModel.class));
        addResult.throwException();
        redisUtils.delete(CommonConstant.TMS_API_PICTURE_VERIFICATION_CODE_PREFIX + "_" + requestDto.getUuid());
        return Result.success(true);
    }

}
