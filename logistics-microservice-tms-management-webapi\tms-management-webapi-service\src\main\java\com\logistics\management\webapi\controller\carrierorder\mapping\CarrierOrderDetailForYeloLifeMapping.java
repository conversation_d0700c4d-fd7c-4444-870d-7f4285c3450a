package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.client.carrierorder.response.*;
import com.logistics.management.webapi.controller.carrierorder.response.*;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/19
 */
public class CarrierOrderDetailForYeloLifeMapping extends MapperMapping<CarrierOrderDetailForYeloLifeResponseModel, CarrierOrderDetailForYeloLifeResponseDto> {

	@Override
	public void configure() {
		CarrierOrderDetailForYeloLifeResponseModel source = getSource();
		CarrierOrderDetailForYeloLifeResponseDto destination = getDestination();

		if (source != null) {
			if (LifeBusinessTypeEnum.PERSONAGE.getKey().equals(source.getBusinessType())) {
				destination.setCustomName(source.getCustomerUserName() + " " + source.getCustomerUserMobile());
			} else {
				destination.setCustomName(source.getCustomerName());
			}

			//个人车主展示姓名+手机号
			if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())){
				destination.setCompanyCarrierName(source.getCarrierContactName()+" "+source.getCarrierContactPhone());
			}

			if (CommonConstant.INTEGER_ONE.equals(source.getIfCancel())) {
				destination.setStatus(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getKey().toString());
				destination.setStatusLabel(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getValue());
			} else {
				destination.setStatusLabel(CarrierOrderStatusEnum.getEnum(source.getStatus()).getValue());
			}
			if (source.getDispatchTime() != null) {
				destination.setDispatchTime(DateUtils.dateToString(source.getDispatchTime(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
			}
			destination.setGoodsUnit(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());

			//运单地址信息
			CarrierOrderDetailBasicInfoModel carrierOrderDetailBasicInfoModel = source.getCarrierOrderDetailBasicInfo();
			if (carrierOrderDetailBasicInfoModel != null) {
				CarrierOrderDetailBasicInfoForYeloLifeDto basicInfoDto = new CarrierOrderDetailBasicInfoForYeloLifeDto();
				basicInfoDto.setConsignor((carrierOrderDetailBasicInfoModel.getConsignorName() == null ? "" : carrierOrderDetailBasicInfoModel.getConsignorName()) + "  " + (carrierOrderDetailBasicInfoModel.getConsignorMobile() == null ? "" : carrierOrderDetailBasicInfoModel.getConsignorMobile()));
				basicInfoDto.setReceiver((carrierOrderDetailBasicInfoModel.getReceiverName() == null ? "" : carrierOrderDetailBasicInfoModel.getReceiverName()) + "  " + (carrierOrderDetailBasicInfoModel.getReceiverMobile() == null ? "" : carrierOrderDetailBasicInfoModel.getReceiverMobile()));
				basicInfoDto.setLoadAddress(carrierOrderDetailBasicInfoModel.getLoadProvinceName() + carrierOrderDetailBasicInfoModel.getLoadCityName() + carrierOrderDetailBasicInfoModel.getLoadAreaName());
				basicInfoDto.setLoadAddress(StringUtils.isEmpty(carrierOrderDetailBasicInfoModel.getLoadWarehouse()) ? basicInfoDto.getLoadAddress() : basicInfoDto.getLoadAddress() + " 【" + carrierOrderDetailBasicInfoModel.getLoadWarehouse() + "】 ");
				basicInfoDto.setLoadAddress(basicInfoDto.getLoadAddress() + carrierOrderDetailBasicInfoModel.getLoadDetailAddress());
				basicInfoDto.setUnloadAddress(carrierOrderDetailBasicInfoModel.getUnloadProvinceName() + carrierOrderDetailBasicInfoModel.getUnloadCityName() + carrierOrderDetailBasicInfoModel.getUnloadAreaName());
				basicInfoDto.setUnloadAddress(StringUtils.isEmpty(carrierOrderDetailBasicInfoModel.getUnloadWarehouse()) ? basicInfoDto.getUnloadAddress() : basicInfoDto.getUnloadAddress() + " 【" + carrierOrderDetailBasicInfoModel.getUnloadWarehouse() + "】 ");
				basicInfoDto.setUnloadAddress(basicInfoDto.getUnloadAddress() + carrierOrderDetailBasicInfoModel.getUnloadDetailAddress());
				destination.setCarrierOrderDetailBasicInfo(basicInfoDto);

			}

			String unit = GoodsUnitEnum.getEnum(source.getGoodsUnit()).getPriceUnit();
			//货主实际结算数量
			BigDecimal entrustSettlementAmount = BigDecimal.ZERO;
			if (SettlementTonnageEnum.LOAD.getKey().equals(source.getEntrustSettlementTonnage())) {
				entrustSettlementAmount = source.getLoadAmount();
			} else if (SettlementTonnageEnum.UNLOAD.getKey().equals(source.getEntrustSettlementTonnage())) {
				entrustSettlementAmount = source.getUnloadAmount();
			} else if (SettlementTonnageEnum.SIGN.getKey().equals(source.getEntrustSettlementTonnage())) {
				entrustSettlementAmount = source.getSignAmount();
			} else if (SettlementTonnageEnum.EXPECT.getKey().equals(source.getEntrustSettlementTonnage())) {
				entrustSettlementAmount = source.getExpectAmount();
			}

			CarrierOrderDetailFreightFeeInfoModel carrierOrderDetailFreightFeeInfoModel = source.getCarrierOrderDetailFreightFeeInfo();
			CarrierOrderDetailFreightFeeInfoForYeloLifeDto freightFeeDto = new CarrierOrderDetailFreightFeeInfoForYeloLifeDto();

			if (carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFeeType() != null && carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFeeType() > CommonConstant.INTEGER_ZERO) {
				freightFeeDto.setDispatchFreightFeeType(ConverterUtils.toString(carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFeeType()));
				freightFeeDto.setDispatchFreightFeeTypeLabel(PriceTypeEnum.getEnum(carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFeeType()).getValue());
			}

			//预计货主费用
			BigDecimal exceptContractPrice = carrierOrderDetailFreightFeeInfoModel.getExpectEntrustFreight();
			if (PriceTypeEnum.UNIT_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getExpectEntrustFreightType())) {
				freightFeeDto.setEntrustFreight(ConverterUtils.toString(exceptContractPrice) + unit);
				freightFeeDto.setEntrustFreightTotal(ConverterUtils.toString(exceptContractPrice.multiply(source.getExpectAmount()).setScale(2, RoundingMode.HALF_UP)) + CommonConstant.YUAN);
			} else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getExpectEntrustFreightType())) {
				freightFeeDto.setEntrustFreight(ConverterUtils.toString(exceptContractPrice) + CommonConstant.YUAN);
				freightFeeDto.setEntrustFreightTotal(ConverterUtils.toString(exceptContractPrice) + CommonConstant.YUAN);
			}
			freightFeeDto.setEntrustFreightType(PriceTypeEnum.getEnum(carrierOrderDetailFreightFeeInfoModel.getExpectEntrustFreightType()).getValue());

			//实际货主费用
			BigDecimal entrustFreightFee = carrierOrderDetailFreightFeeInfoModel.getEntrustFreight();
			BigDecimal signEntrustFreightTotal = Optional.ofNullable(carrierOrderDetailFreightFeeInfoModel.getEntrustSettlementCostTotal()).orElse(BigDecimal.ZERO);

			//按照正常结算数据计算
			if (entrustSettlementAmount != null &&
					entrustSettlementAmount.compareTo(BigDecimal.ZERO) > 0 &&
					entrustFreightFee.compareTo(BigDecimal.ZERO) > 0) {
				freightFeeDto.setActualEntrustFreightType(PriceTypeEnum.getEnum(carrierOrderDetailFreightFeeInfoModel.getEntrustFreightType()).getValue());
				if (carrierOrderDetailFreightFeeInfoModel.getEntrustFreightType().equals(PriceTypeEnum.UNIT_PRICE.getKey())) {
					freightFeeDto.setSignEntrustFreight(ConverterUtils.toString(entrustFreightFee) + unit);
					BigDecimal signEntrustFreightFeeTotal = entrustFreightFee.multiply(entrustSettlementAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
					freightFeeDto.setSignEntrustFreightTotal(ConverterUtils.toString(signEntrustFreightFeeTotal) + CommonConstant.YUAN);
				} else if (carrierOrderDetailFreightFeeInfoModel.getEntrustFreightType().equals(PriceTypeEnum.FIXED_PRICE.getKey())) {
					freightFeeDto.setSignEntrustFreight(ConverterUtils.toString(entrustFreightFee) + CommonConstant.YUAN);
					freightFeeDto.setSignEntrustFreightTotal(ConverterUtils.toString(entrustFreightFee) + CommonConstant.YUAN);
				}
			}

			//已生成结算数据，取结算数据
			if (CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(source.getStatus()) && carrierOrderDetailFreightFeeInfoModel.getEntrustSettlementCostTotal() != null) {
				freightFeeDto.setActualEntrustFreightType(PriceTypeEnum.getEnum(carrierOrderDetailFreightFeeInfoModel.getEntrustPriceType()).getValue());
				if (carrierOrderDetailFreightFeeInfoModel.getEntrustPriceType() > 0) {
					if (PriceTypeEnum.UNIT_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getEntrustPriceType())) {
						BigDecimal settleUnitPrice = signEntrustFreightTotal.divide(entrustSettlementAmount, 2, BigDecimal.ROUND_HALF_UP);
						freightFeeDto.setSignEntrustFreight(ConverterUtils.toString(settleUnitPrice) + unit);
					} else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getEntrustPriceType())) {
						freightFeeDto.setSignEntrustFreight(ConverterUtils.toString(signEntrustFreightTotal) + CommonConstant.YUAN);
					}
					freightFeeDto.setSignEntrustFreightTotal(ConverterUtils.toString(signEntrustFreightTotal) + CommonConstant.YUAN);
				}
			}




			//实际结算数量
			BigDecimal carrierSettlementAmount = BigDecimal.ZERO;
			if (SettlementTonnageEnum.LOAD.getKey().equals(source.getCarrierSettlementTonnage())) {
				carrierSettlementAmount = source.getLoadAmount();
			} else if (SettlementTonnageEnum.UNLOAD.getKey().equals(source.getCarrierSettlementTonnage())) {
				carrierSettlementAmount = source.getUnloadAmount();
			} else if (SettlementTonnageEnum.SIGN.getKey().equals(source.getCarrierSettlementTonnage())) {
				carrierSettlementAmount = source.getSignAmount();
			} else if (SettlementTonnageEnum.EXPECT.getKey().equals(source.getCarrierSettlementTonnage())) {
				carrierSettlementAmount = source.getExpectAmount();
			}



			//车主费用
			BigDecimal carrierFreightFee = carrierOrderDetailFreightFeeInfoModel.getCarrierFreight();

			//预计车主费用 单价：单价*预提数量；一口价：预提/一口价*总委托
			freightFeeDto.setCarrierFreightType(PriceTypeEnum.getEnum(carrierOrderDetailFreightFeeInfoModel.getCarrierFreightType()).getValue());
			if (PriceTypeEnum.UNIT_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getCarrierFreightType())) {
				freightFeeDto.setCarrierFreight(carrierFreightFee + unit);
				freightFeeDto.setCarrierFreightTotal(ConverterUtils.toString(source.getExpectAmount().multiply(carrierFreightFee).setScale(2, BigDecimal.ROUND_HALF_UP)) + CommonConstant.YUAN);
			} else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getCarrierFreightType())) {
				freightFeeDto.setCarrierFreight(ConverterUtils.toString(carrierFreightFee.setScale(2, RoundingMode.HALF_UP)) + CommonConstant.YUAN);
				freightFeeDto.setCarrierFreightTotal(ConverterUtils.toString(carrierFreightFee.setScale(2, RoundingMode.HALF_UP)) + CommonConstant.YUAN);
			}

			//实际车主费用 单价：单价*签收数量；一口价：预提/一口价*总委托
			if (CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(source.getStatus()) ) {
				//有结算数据计算实际费用
				if (PriceTypeEnum.UNIT_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getCarrierFreightType())) {
					freightFeeDto.setSignCarrierFreight(ConverterUtils.toString(carrierFreightFee) + unit);
					BigDecimal signCarrierFreightFeeTotal = carrierFreightFee.multiply(carrierSettlementAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
					freightFeeDto.setSignCarrierFreightTotal(ConverterUtils.toString(signCarrierFreightFeeTotal) + CommonConstant.YUAN);
				} else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getCarrierFreightType())) {
					freightFeeDto.setSignCarrierFreight(ConverterUtils.toString(carrierFreightFee) + CommonConstant.YUAN);
					freightFeeDto.setSignCarrierFreightTotal(ConverterUtils.toString(carrierFreightFee) + CommonConstant.YUAN);
				}
				freightFeeDto.setActualCarrierFreightType(PriceTypeEnum.getEnum(carrierOrderDetailFreightFeeInfoModel.getCarrierFreightType()).getValue());

				//已生成结算数据
				if(carrierOrderDetailFreightFeeInfoModel.getCarrierSettlementCostTotal() != null && carrierOrderDetailFreightFeeInfoModel.getCarrierPriceType() != null){
					BigDecimal carrierSettlementCost = Optional.ofNullable(carrierOrderDetailFreightFeeInfoModel.getCarrierSettlementCostTotal()).orElse(CommonConstant.BIG_DECIMAL_ZERO_NET_ZERO);
					BigDecimal signCarrierFreightFeeTotal = Optional.ofNullable(carrierOrderDetailFreightFeeInfoModel.getCarrierSettlementCostTotal()).orElse(CommonConstant.BIG_DECIMAL_ZERO_NET_ZERO);
					if (PriceTypeEnum.UNIT_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getCarrierPriceType())) {
						if (carrierSettlementAmount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
							freightFeeDto.setSignCarrierFreight(ConverterUtils.toString(carrierSettlementCost.divide(carrierSettlementAmount, 2, BigDecimal.ROUND_HALF_UP)) + unit);
						}
					} else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(carrierOrderDetailFreightFeeInfoModel.getCarrierPriceType())) {
						freightFeeDto.setSignCarrierFreight(ConverterUtils.toString(signCarrierFreightFeeTotal) + CommonConstant.YUAN);
					}
					freightFeeDto.setSignCarrierFreightTotal(ConverterUtils.toString(signCarrierFreightFeeTotal) + CommonConstant.YUAN);
					freightFeeDto.setActualCarrierFreightType(PriceTypeEnum.getEnum(carrierOrderDetailFreightFeeInfoModel.getCarrierPriceType()).getValue());
				}
			}




			//我司,新生运单只有我司
			if (CommonConstant.INTEGER_ONE.equals(source.getIsOurCompany())) {
				//司机费用
				BigDecimal dispatchFreightFeeTotal = BigDecimal.ZERO;
				BigDecimal dispatchFreightFee = carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFee();
				if (carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFeeType().equals(PriceTypeEnum.UNIT_PRICE.getKey())) {
					freightFeeDto.setDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee));
					freightFeeDto.setDispatchFreightFeeUnit(unit);
					dispatchFreightFeeTotal = dispatchFreightFee.multiply(source.getExpectAmount()).setScale(2, BigDecimal.ROUND_HALF_UP);
				} else if (carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFeeType().equals(PriceTypeEnum.FIXED_PRICE.getKey())) {
					freightFeeDto.setDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee));
					freightFeeDto.setDispatchFreightFeeUnit(CommonConstant.YUAN);
					dispatchFreightFeeTotal = dispatchFreightFee;
				}
				String adjustFee = ConverterUtils.toString(carrierOrderDetailFreightFeeInfoModel.getAdjustFee());
				if (carrierOrderDetailFreightFeeInfoModel.getAdjustFee().compareTo(CommonConstant.BIG_DECIMAL_ZERO) > CommonConstant.INTEGER_ZERO) {
					adjustFee = "+" + ConverterUtils.toString(carrierOrderDetailFreightFeeInfoModel.getAdjustFee());
				}
				if (source.getStatus() > CarrierOrderStatusEnum.WAIT_UNLOAD.getKey()) {
					//司机费用
					BigDecimal signDispatchFreightFeeTotal = BigDecimal.ZERO;
					if (carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFeeType().equals(PriceTypeEnum.UNIT_PRICE.getKey())) {
						freightFeeDto.setSignDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee) + unit);
						signDispatchFreightFeeTotal = dispatchFreightFee.multiply(source.getUnloadAmount()).setScale(2, BigDecimal.ROUND_HALF_UP);
					} else if (carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFeeType().equals(PriceTypeEnum.FIXED_PRICE.getKey())) {
						freightFeeDto.setSignDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee) + CommonConstant.YUAN);
						signDispatchFreightFeeTotal = dispatchFreightFee;
					}
					freightFeeDto.setSignAdjustFee(adjustFee + CommonConstant.YUAN);
					freightFeeDto.setSignMarkupFee(ConverterUtils.toString(carrierOrderDetailFreightFeeInfoModel.getMarkupFee()) + CommonConstant.YUAN);
					freightFeeDto.setSignDispatchFreightFeeTotal(ConverterUtils.toString(signDispatchFreightFeeTotal.add(carrierOrderDetailFreightFeeInfoModel.getAdjustFee()).add(carrierOrderDetailFreightFeeInfoModel.getMarkupFee())) + CommonConstant.YUAN);
				}
				freightFeeDto.setAdjustFee(adjustFee + CommonConstant.YUAN);
				freightFeeDto.setMarkupFee(carrierOrderDetailFreightFeeInfoModel.getMarkupFee() == null || carrierOrderDetailFreightFeeInfoModel.getMarkupFee().compareTo(BigDecimal.ZERO) <= CommonConstant.INTEGER_ZERO ?
						"" : ConverterUtils.toString(carrierOrderDetailFreightFeeInfoModel.getMarkupFee()) + CommonConstant.YUAN);
				freightFeeDto.setDispatchFreightFeeTotal((dispatchFreightFeeTotal.add(carrierOrderDetailFreightFeeInfoModel.getAdjustFee()).add(carrierOrderDetailFreightFeeInfoModel.getMarkupFee())).compareTo(BigDecimal.ZERO) <= CommonConstant.INTEGER_ZERO ?
						"" : ConverterUtils.toString(dispatchFreightFeeTotal.add(carrierOrderDetailFreightFeeInfoModel.getAdjustFee()).add(carrierOrderDetailFreightFeeInfoModel.getMarkupFee())) + CommonConstant.YUAN);
			}
			destination.setCarrierOrderDetailFreightFeeInfo(freightFeeDto);

			//货物信息
			if (source.getCarrierOrderDetailGoodsInfo() != null) {
				destination.setCarrierOrderDetailGoodsInfo(new ArrayList<>());
				for (CarrierOrderDetailGoodsForYeloLifeModel tmp : source.getCarrierOrderDetailGoodsInfo()) {
					CarrierOrderDetailGoodsInfoForYeloLifeDto dto = new CarrierOrderDetailGoodsInfoForYeloLifeDto();
					dto.setYeloCode(tmp.getYeloCode());
					dto.setGoodsName(tmp.getGoodsName());
					dto.setSize(tmp.getGoodsSize());
					dto.setExpectAmount(tmp.getExpectAmount().stripTrailingZeros().toPlainString());
					if (source.getStatus() > CarrierOrderStatusEnum.WAIT_LOAD.getKey()) {
						dto.setLoadAmount(tmp.getLoadAmount().stripTrailingZeros().toPlainString());
					}
					if (source.getStatus() > CarrierOrderStatusEnum.WAIT_UNLOAD.getKey()) {
						dto.setUnloadAmount(tmp.getUnloadAmount().stripTrailingZeros().toPlainString());
					}
					if (source.getStatus() > CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey()) {
						dto.setSignAmount(tmp.getSignAmount().stripTrailingZeros().toPlainString());
					}
					destination.getCarrierOrderDetailGoodsInfo().add(dto);
				}
			}
			//车辆信息
			if (source.getCarrierOrderDetailVehicleDriverInfo() != null) {
				destination.setCarrierOrderDetailVehicleDriverInfo(new ArrayList<>());
				for (CarrierOrderDetailVehicleDriverInfoModel tmp : source.getCarrierOrderDetailVehicleDriverInfo()) {
					CarrierOrderDetailVehicleDriverInfoDto dto = MapperUtils.mapper(tmp, CarrierOrderDetailVehicleDriverInfoDto.class);
					if (CommonConstant.INTEGER_ONE.equals(tmp.getIfInvalid())) {
						dto.setStatus(ValidStatusEnum.VALID.getValue());
					} else {
						if (VehicleHistoryAuditStatusEnum.REJECT.getKey().equals(tmp.getAuditStatus())) {
							dto.setStatus(VehicleHistoryAuditStatusEnum.REJECT.getValue());
						} else if (VehicleHistoryAuditStatusEnum.NOT_NEED_AUDIT.getKey().equals(tmp.getAuditStatus()) || VehicleHistoryAuditStatusEnum.AUDIT.getKey().equals(tmp.getAuditStatus())) {
							dto.setStatus(ValidStatusEnum.INVALID.getValue());
						} else if (VehicleHistoryAuditStatusEnum.WAIT_AUDIT.getKey().equals(tmp.getAuditStatus())) {
							dto.setStatus(VehicleHistoryAuditStatusEnum.WAIT_AUDIT.getValue());
						}
					}
					dto.setDriver((tmp.getDriverName() == null ? "" : tmp.getDriverName()) + " " + (tmp.getDriverMobile() == null ? "" : tmp.getDriverMobile()));
					destination.getCarrierOrderDetailVehicleDriverInfo().add(dto);
				}
			}
		}
	}
}
