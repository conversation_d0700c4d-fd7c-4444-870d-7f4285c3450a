/**
 * Created by yun<PERSON>zhou on 2017/12/12.
 */
package com.logistics.tms.base.enums;

import java.util.stream.Stream;

public enum ReservationSourceEnum {

    DRIVER_APP(0, "司机小程序"),
    H5(1, "H5"),
    CARRIER_WEB(2, "车主前台页面"),
    DEFAULT(-1, "");

    private Integer key;
    private String value;

    ReservationSourceEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static ReservationSourceEnum getEnum(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
