package com.logistics.appapi.controller.driverappoint.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2022/8/25 19:08
 */
@Data
public class DriverAppointAssociatedVehicleRequestDto {
    @ApiModelProperty(value = "司机预约记录id", required = true)
    @NotBlank(message = "请选择要查看的记录")
    private String driverAppointId;

    @ApiModelProperty(value = "车辆id", required = true)
    @NotBlank(message = "请选择车辆")
    private String vehicleId;
}
