package com.logistics.tms.client;

import com.logistics.tms.base.enums.WarehouseTypeEnum;
import com.logistics.tms.client.feign.warehouse.lift.WarehouseLifeServiceApi;
import com.logistics.tms.client.feign.warehouse.lift.reponse.GetWarehouseDetailForLifeResponseModel;
import com.logistics.tms.client.feign.warehouse.lift.request.GetWarehouseDetailForLifeRequestModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/24 9:54
 */
@Service
public class WarehouseLifeClient {


    @Resource
    private WarehouseLifeServiceApi warehouseLifeServiceApi;

    /**
     * 新生仓库卸货列表
     *
     * @param requestModel
     * @return
     */
    public List<GetWarehouseDetailForLifeResponseModel> getWarehouseDetailForLife(GetWarehouseDetailForLifeRequestModel requestModel) {
        requestModel.setTypes(new ArrayList<>(Arrays.asList(WarehouseTypeEnum.OWN.getKey(), WarehouseTypeEnum.THIRD_PARTY.getKey())));
        Result<List<GetWarehouseDetailForLifeResponseModel>> responseModel = warehouseLifeServiceApi.getWarehouseDetailForLife(requestModel);
        responseModel.throwException();
        return responseModel.getData();
    }

}
