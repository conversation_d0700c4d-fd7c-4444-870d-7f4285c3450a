<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TDispatchOrderMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDispatchOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dispatch_order_code" jdbcType="VARCHAR" property="dispatchOrderCode" />
    <result column="carrier_order_count" jdbcType="INTEGER" property="carrierOrderCount" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="trailer_vehicle_id" jdbcType="BIGINT" property="trailerVehicleId" />
    <result column="trailer_vehicle_no" jdbcType="VARCHAR" property="trailerVehicleNo" />
    <result column="driver_id" jdbcType="BIGINT" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="driver_mobile" jdbcType="VARCHAR" property="driverMobile" />
    <result column="driver_identity" jdbcType="VARCHAR" property="driverIdentity" />
    <result column="expect_arrival_time" jdbcType="TIMESTAMP" property="expectArrivalTime" />
    <result column="entrust_freight_type" jdbcType="INTEGER" property="entrustFreightType" />
    <result column="entrust_freight" jdbcType="DECIMAL" property="entrustFreight" />
    <result column="dispatch_freight_fee_type" jdbcType="INTEGER" property="dispatchFreightFeeType" />
    <result column="dispatch_freight_fee" jdbcType="DECIMAL" property="dispatchFreightFee" />
    <result column="load_point_amount" jdbcType="INTEGER" property="loadPointAmount" />
    <result column="unload_point_amount" jdbcType="INTEGER" property="unloadPointAmount" />
    <result column="markup_fee" jdbcType="DECIMAL" property="markupFee" />
    <result column="if_adjust" jdbcType="INTEGER" property="ifAdjust" />
    <result column="adjust_fee_type" jdbcType="INTEGER" property="adjustFeeType" />
    <result column="adjust_fee" jdbcType="DECIMAL" property="adjustFee" />
    <result column="adjust_remark" jdbcType="VARCHAR" property="adjustRemark" />
    <result column="goods_unit" jdbcType="INTEGER" property="goodsUnit" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="dispatch_user_id" jdbcType="BIGINT" property="dispatchUserId" />
    <result column="dispatch_user_name" jdbcType="VARCHAR" property="dispatchUserName" />
    <result column="dispatch_time" jdbcType="TIMESTAMP" property="dispatchTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="company_carrier_type" jdbcType="INTEGER" property="companyCarrierType" />
    <result column="company_carrier_id" jdbcType="BIGINT" property="companyCarrierId" />
    <result column="company_carrier_name" jdbcType="VARCHAR" property="companyCarrierName" />
    <result column="carrier_contact_id" jdbcType="BIGINT" property="carrierContactId" />
    <result column="carrier_contact_name" jdbcType="VARCHAR" property="carrierContactName" />
    <result column="carrier_contact_phone" jdbcType="VARCHAR" property="carrierContactPhone" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dispatch_order_code, carrier_order_count, vehicle_id, vehicle_no, trailer_vehicle_id, 
    trailer_vehicle_no, driver_id, driver_name, driver_mobile, driver_identity, expect_arrival_time, 
    entrust_freight_type, entrust_freight, dispatch_freight_fee_type, dispatch_freight_fee, 
    load_point_amount, unload_point_amount, markup_fee, if_adjust, adjust_fee_type, adjust_fee, 
    adjust_remark, goods_unit, source, dispatch_user_id, dispatch_user_name, dispatch_time, 
    remark, company_carrier_type, company_carrier_id, company_carrier_name, carrier_contact_id, 
    carrier_contact_name, carrier_contact_phone, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_dispatch_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_dispatch_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDispatchOrder">
    insert into t_dispatch_order (id, dispatch_order_code, carrier_order_count, 
      vehicle_id, vehicle_no, trailer_vehicle_id, 
      trailer_vehicle_no, driver_id, driver_name, 
      driver_mobile, driver_identity, expect_arrival_time, 
      entrust_freight_type, entrust_freight, dispatch_freight_fee_type, 
      dispatch_freight_fee, load_point_amount, unload_point_amount, 
      markup_fee, if_adjust, adjust_fee_type, 
      adjust_fee, adjust_remark, goods_unit, 
      source, dispatch_user_id, dispatch_user_name, 
      dispatch_time, remark, company_carrier_type, 
      company_carrier_id, company_carrier_name, carrier_contact_id, 
      carrier_contact_name, carrier_contact_phone, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{dispatchOrderCode,jdbcType=VARCHAR}, #{carrierOrderCount,jdbcType=INTEGER}, 
      #{vehicleId,jdbcType=BIGINT}, #{vehicleNo,jdbcType=VARCHAR}, #{trailerVehicleId,jdbcType=BIGINT}, 
      #{trailerVehicleNo,jdbcType=VARCHAR}, #{driverId,jdbcType=BIGINT}, #{driverName,jdbcType=VARCHAR}, 
      #{driverMobile,jdbcType=VARCHAR}, #{driverIdentity,jdbcType=VARCHAR}, #{expectArrivalTime,jdbcType=TIMESTAMP}, 
      #{entrustFreightType,jdbcType=INTEGER}, #{entrustFreight,jdbcType=DECIMAL}, #{dispatchFreightFeeType,jdbcType=INTEGER}, 
      #{dispatchFreightFee,jdbcType=DECIMAL}, #{loadPointAmount,jdbcType=INTEGER}, #{unloadPointAmount,jdbcType=INTEGER}, 
      #{markupFee,jdbcType=DECIMAL}, #{ifAdjust,jdbcType=INTEGER}, #{adjustFeeType,jdbcType=INTEGER}, 
      #{adjustFee,jdbcType=DECIMAL}, #{adjustRemark,jdbcType=VARCHAR}, #{goodsUnit,jdbcType=INTEGER}, 
      #{source,jdbcType=INTEGER}, #{dispatchUserId,jdbcType=BIGINT}, #{dispatchUserName,jdbcType=VARCHAR}, 
      #{dispatchTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{companyCarrierType,jdbcType=INTEGER}, 
      #{companyCarrierId,jdbcType=BIGINT}, #{companyCarrierName,jdbcType=VARCHAR}, #{carrierContactId,jdbcType=BIGINT}, 
      #{carrierContactName,jdbcType=VARCHAR}, #{carrierContactPhone,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDispatchOrder">
    insert into t_dispatch_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="dispatchOrderCode != null">
        dispatch_order_code,
      </if>
      <if test="carrierOrderCount != null">
        carrier_order_count,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleNo != null">
        vehicle_no,
      </if>
      <if test="trailerVehicleId != null">
        trailer_vehicle_id,
      </if>
      <if test="trailerVehicleNo != null">
        trailer_vehicle_no,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="driverName != null">
        driver_name,
      </if>
      <if test="driverMobile != null">
        driver_mobile,
      </if>
      <if test="driverIdentity != null">
        driver_identity,
      </if>
      <if test="expectArrivalTime != null">
        expect_arrival_time,
      </if>
      <if test="entrustFreightType != null">
        entrust_freight_type,
      </if>
      <if test="entrustFreight != null">
        entrust_freight,
      </if>
      <if test="dispatchFreightFeeType != null">
        dispatch_freight_fee_type,
      </if>
      <if test="dispatchFreightFee != null">
        dispatch_freight_fee,
      </if>
      <if test="loadPointAmount != null">
        load_point_amount,
      </if>
      <if test="unloadPointAmount != null">
        unload_point_amount,
      </if>
      <if test="markupFee != null">
        markup_fee,
      </if>
      <if test="ifAdjust != null">
        if_adjust,
      </if>
      <if test="adjustFeeType != null">
        adjust_fee_type,
      </if>
      <if test="adjustFee != null">
        adjust_fee,
      </if>
      <if test="adjustRemark != null">
        adjust_remark,
      </if>
      <if test="goodsUnit != null">
        goods_unit,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="dispatchUserId != null">
        dispatch_user_id,
      </if>
      <if test="dispatchUserName != null">
        dispatch_user_name,
      </if>
      <if test="dispatchTime != null">
        dispatch_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="companyCarrierType != null">
        company_carrier_type,
      </if>
      <if test="companyCarrierId != null">
        company_carrier_id,
      </if>
      <if test="companyCarrierName != null">
        company_carrier_name,
      </if>
      <if test="carrierContactId != null">
        carrier_contact_id,
      </if>
      <if test="carrierContactName != null">
        carrier_contact_name,
      </if>
      <if test="carrierContactPhone != null">
        carrier_contact_phone,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="dispatchOrderCode != null">
        #{dispatchOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="carrierOrderCount != null">
        #{carrierOrderCount,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="trailerVehicleId != null">
        #{trailerVehicleId,jdbcType=BIGINT},
      </if>
      <if test="trailerVehicleNo != null">
        #{trailerVehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null">
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null">
        #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="driverIdentity != null">
        #{driverIdentity,jdbcType=VARCHAR},
      </if>
      <if test="expectArrivalTime != null">
        #{expectArrivalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="entrustFreightType != null">
        #{entrustFreightType,jdbcType=INTEGER},
      </if>
      <if test="entrustFreight != null">
        #{entrustFreight,jdbcType=DECIMAL},
      </if>
      <if test="dispatchFreightFeeType != null">
        #{dispatchFreightFeeType,jdbcType=INTEGER},
      </if>
      <if test="dispatchFreightFee != null">
        #{dispatchFreightFee,jdbcType=DECIMAL},
      </if>
      <if test="loadPointAmount != null">
        #{loadPointAmount,jdbcType=INTEGER},
      </if>
      <if test="unloadPointAmount != null">
        #{unloadPointAmount,jdbcType=INTEGER},
      </if>
      <if test="markupFee != null">
        #{markupFee,jdbcType=DECIMAL},
      </if>
      <if test="ifAdjust != null">
        #{ifAdjust,jdbcType=INTEGER},
      </if>
      <if test="adjustFeeType != null">
        #{adjustFeeType,jdbcType=INTEGER},
      </if>
      <if test="adjustFee != null">
        #{adjustFee,jdbcType=DECIMAL},
      </if>
      <if test="adjustRemark != null">
        #{adjustRemark,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        #{goodsUnit,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="dispatchUserId != null">
        #{dispatchUserId,jdbcType=BIGINT},
      </if>
      <if test="dispatchUserName != null">
        #{dispatchUserName,jdbcType=VARCHAR},
      </if>
      <if test="dispatchTime != null">
        #{dispatchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="companyCarrierType != null">
        #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null">
        #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null">
        #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactId != null">
        #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactName != null">
        #{carrierContactName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactPhone != null">
        #{carrierContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDispatchOrder">
    update t_dispatch_order
    <set>
      <if test="dispatchOrderCode != null">
        dispatch_order_code = #{dispatchOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="carrierOrderCount != null">
        carrier_order_count = #{carrierOrderCount,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="trailerVehicleId != null">
        trailer_vehicle_id = #{trailerVehicleId,jdbcType=BIGINT},
      </if>
      <if test="trailerVehicleNo != null">
        trailer_vehicle_no = #{trailerVehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null">
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null">
        driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="driverIdentity != null">
        driver_identity = #{driverIdentity,jdbcType=VARCHAR},
      </if>
      <if test="expectArrivalTime != null">
        expect_arrival_time = #{expectArrivalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="entrustFreightType != null">
        entrust_freight_type = #{entrustFreightType,jdbcType=INTEGER},
      </if>
      <if test="entrustFreight != null">
        entrust_freight = #{entrustFreight,jdbcType=DECIMAL},
      </if>
      <if test="dispatchFreightFeeType != null">
        dispatch_freight_fee_type = #{dispatchFreightFeeType,jdbcType=INTEGER},
      </if>
      <if test="dispatchFreightFee != null">
        dispatch_freight_fee = #{dispatchFreightFee,jdbcType=DECIMAL},
      </if>
      <if test="loadPointAmount != null">
        load_point_amount = #{loadPointAmount,jdbcType=INTEGER},
      </if>
      <if test="unloadPointAmount != null">
        unload_point_amount = #{unloadPointAmount,jdbcType=INTEGER},
      </if>
      <if test="markupFee != null">
        markup_fee = #{markupFee,jdbcType=DECIMAL},
      </if>
      <if test="ifAdjust != null">
        if_adjust = #{ifAdjust,jdbcType=INTEGER},
      </if>
      <if test="adjustFeeType != null">
        adjust_fee_type = #{adjustFeeType,jdbcType=INTEGER},
      </if>
      <if test="adjustFee != null">
        adjust_fee = #{adjustFee,jdbcType=DECIMAL},
      </if>
      <if test="adjustRemark != null">
        adjust_remark = #{adjustRemark,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="dispatchUserId != null">
        dispatch_user_id = #{dispatchUserId,jdbcType=BIGINT},
      </if>
      <if test="dispatchUserName != null">
        dispatch_user_name = #{dispatchUserName,jdbcType=VARCHAR},
      </if>
      <if test="dispatchTime != null">
        dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="companyCarrierType != null">
        company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null">
        company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null">
        company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactId != null">
        carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactName != null">
        carrier_contact_name = #{carrierContactName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactPhone != null">
        carrier_contact_phone = #{carrierContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDispatchOrder">
    update t_dispatch_order
    set dispatch_order_code = #{dispatchOrderCode,jdbcType=VARCHAR},
      carrier_order_count = #{carrierOrderCount,jdbcType=INTEGER},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      trailer_vehicle_id = #{trailerVehicleId,jdbcType=BIGINT},
      trailer_vehicle_no = #{trailerVehicleNo,jdbcType=VARCHAR},
      driver_id = #{driverId,jdbcType=BIGINT},
      driver_name = #{driverName,jdbcType=VARCHAR},
      driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      driver_identity = #{driverIdentity,jdbcType=VARCHAR},
      expect_arrival_time = #{expectArrivalTime,jdbcType=TIMESTAMP},
      entrust_freight_type = #{entrustFreightType,jdbcType=INTEGER},
      entrust_freight = #{entrustFreight,jdbcType=DECIMAL},
      dispatch_freight_fee_type = #{dispatchFreightFeeType,jdbcType=INTEGER},
      dispatch_freight_fee = #{dispatchFreightFee,jdbcType=DECIMAL},
      load_point_amount = #{loadPointAmount,jdbcType=INTEGER},
      unload_point_amount = #{unloadPointAmount,jdbcType=INTEGER},
      markup_fee = #{markupFee,jdbcType=DECIMAL},
      if_adjust = #{ifAdjust,jdbcType=INTEGER},
      adjust_fee_type = #{adjustFeeType,jdbcType=INTEGER},
      adjust_fee = #{adjustFee,jdbcType=DECIMAL},
      adjust_remark = #{adjustRemark,jdbcType=VARCHAR},
      goods_unit = #{goodsUnit,jdbcType=INTEGER},
      source = #{source,jdbcType=INTEGER},
      dispatch_user_id = #{dispatchUserId,jdbcType=BIGINT},
      dispatch_user_name = #{dispatchUserName,jdbcType=VARCHAR},
      dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
      company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
      carrier_contact_name = #{carrierContactName,jdbcType=VARCHAR},
      carrier_contact_phone = #{carrierContactPhone,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByDispatchOrderCode" resultMap="BaseResultMap" parameterType="java.lang.String">
    select
    <include refid="Base_Column_List" />
    from t_dispatch_order
    where dispatch_order_code = #{dispatchOrderCode,jdbcType=VARCHAR}
    and valid = 1
    limit 1
  </select>
</mapper>