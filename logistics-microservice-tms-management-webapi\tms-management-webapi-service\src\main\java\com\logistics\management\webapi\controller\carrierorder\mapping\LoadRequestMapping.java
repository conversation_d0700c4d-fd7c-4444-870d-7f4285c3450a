
package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.controller.carrierorder.request.LoadRequestDto;
import com.logistics.management.webapi.client.carrierorder.request.LoadRequestModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

import java.util.Collections;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/4
 */
public class LoadRequestMapping extends MapperMapping<LoadRequestDto, LoadRequestModel> {
	@Override
	public void configure() {
		LoadRequestDto source = getSource();
		LoadRequestModel destination = getDestination();

		if (source != null) {
			destination.setTmpUrl(StringUtils.isBlank(source.getTmpUrl()) ? null : Collections.singletonList(source.getTmpUrl()));
		}
	}
}
