<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleLengthMapper">
    <select id="searchVehicleLengthList" resultType="com.logistics.tms.controller.vehiclelength.response.SearchVehicleLengthListResponseModel">
        select
        id as vehicleLengthId,
        vehicle_length as vehicleLength,
        carriage_scope_min as carriageScopeMin,
        carriage_scope_max as carriageScopeMax,
        last_modified_by as lastModifiedBy,
        last_modified_time as lastModifiedTime
        from t_vehicle_length
        where valid = 1
        order by last_modified_time desc, id desc
    </select>

    <select id="vehicleLengthDetail" resultType="com.logistics.tms.controller.vehiclelength.response.VehicleLengthDetailResponseModel">
        select
        id as vehicleLengthId,
        vehicle_length as vehicleLength,
        carriage_scope_min as carriageScopeMin,
        carriage_scope_max as carriageScopeMax
        from t_vehicle_length
        where valid = 1
        and id = #{vehicleLengthId,jdbcType=BIGINT}
    </select>

    <select id="selectVehicleLengthList" resultType="com.logistics.tms.controller.vehiclelength.response.SelectVehicleLengthListResponseModel">
        select
        id as vehicleLengthId,
        vehicle_length as vehicleLength
        from t_vehicle_length
        where valid = 1
        order by last_modified_time desc, id desc
    </select>

    <select id="selectRangeGoodsCount" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from t_vehicle_length
        where valid = 1
        and carriage_scope_min &lt;=#{goodsCount,jdbcType=DECIMAL}
        and carriage_scope_max &gt;=#{goodsCount,jdbcType=DECIMAL}
    </select>
</mapper>