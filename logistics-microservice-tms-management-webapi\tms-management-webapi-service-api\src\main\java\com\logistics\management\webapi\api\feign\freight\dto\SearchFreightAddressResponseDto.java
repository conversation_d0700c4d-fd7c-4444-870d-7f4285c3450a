package com.logistics.management.webapi.api.feign.freight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 运价地址
 * @Author: sj
 * @Date: 2019/12/24 13:11
 */
@Data
public class SearchFreightAddressResponseDto {
    @ApiModelProperty("运价地址ID")
    private String freightAddressId = "";
    @ApiModelProperty("计价类型: 计价类型 1 基价 2 一日游")
    private String calcType = "";
    @ApiModelProperty("计价类型文本")
    private String calcTypeLabel;
    @ApiModelProperty("仓库名称")
    private String warehouseName = "";
    @ApiModelProperty("发货省名称")
    private String fromProvinceName = "";
    @ApiModelProperty("发货市名称")
    private String fromCityName = "";
    @ApiModelProperty("发货区名称")
    private String fromAreaName = "";
    @ApiModelProperty("省市拼接")
    private String fromProvinceAndCityLabel;

    @ApiModelProperty("卸货省名称")
    private String toProvinceName = "";
    @ApiModelProperty("卸货市名称")
    private String toCityName = "";
    @ApiModelProperty("卸货区名称")
    private String toAreaName = "";
    @ApiModelProperty("省市拼接")
    private String toProvinceAndCityLabel;
    @ApiModelProperty("操作人")
    private String lastModifiedBy = "";
    @ApiModelProperty("操作时间")
    private String lastModifiedTime = "";
}
