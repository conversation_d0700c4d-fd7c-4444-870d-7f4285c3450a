package com.logistics.management.webapi.controller.shippingorder;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.utils.ConvertPageInfoUtil;
import com.logistics.management.webapi.client.shippingorder.ShippingOrderClient;
import com.logistics.management.webapi.client.shippingorder.request.*;
import com.logistics.management.webapi.client.shippingorder.response.GetShippingOrderDetailResponseModel;
import com.logistics.management.webapi.client.shippingorder.response.GetShippingOrderRoutePlanResponseModel;
import com.logistics.management.webapi.client.shippingorder.response.SearchShippingOrderListResponseModel;
import com.logistics.management.webapi.controller.shippingorder.mapping.ShippingOrderGetDetailMapping;
import com.logistics.management.webapi.controller.shippingorder.mapping.ShippingOrderSearchListMapping;
import com.logistics.management.webapi.controller.shippingorder.request.*;
import com.logistics.management.webapi.controller.shippingorder.response.GetShippingOrderDetailResponseDto;
import com.logistics.management.webapi.controller.shippingorder.response.GetShippingOrderRoutePlanResponseDto;
import com.logistics.management.webapi.controller.shippingorder.response.SearchShippingOrderListResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.YeloExcelUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 零担运输单
 * @author: wjf
 * @date: 2024/8/6 10:01
 */
@Slf4j
@Api(tags = "零担运输单")
@RestController
@RequestMapping(value = "/api/shippingOrder")
public class ShippingOrderController {

    @Resource
    private ShippingOrderClient shippingOrderClient;

    /**
     * 列表 3.27.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/searchList")
    public Result<PageInfo<SearchShippingOrderListResponseDto>> searchList(@RequestBody SearchShippingOrderListRequestDto requestDto){
        SearchShippingOrderListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchShippingOrderListRequestModel.class);
        requestModel.setSource(CommonConstant.ONE);
        Result<PageInfo<SearchShippingOrderListResponseModel>> pageInfoResult = shippingOrderClient.searchList(requestModel);
        pageInfoResult.throwException();
        PageInfo<SearchShippingOrderListResponseDto> searchShippingOrderListResponseDtoPageInfo = ConvertPageInfoUtil.convertPageInfo(pageInfoResult.getData(), SearchShippingOrderListResponseDto.class, new ShippingOrderSearchListMapping());
        return Result.success(searchShippingOrderListResponseDtoPageInfo);
    }

    /**
     * 列表导出 3.27.0
     *
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/export")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void export(@RequestBody SearchShippingOrderListRequestDto requestDto, HttpServletResponse response) {
        SearchShippingOrderListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchShippingOrderListRequestModel.class);
        requestModel.setSource(CommonConstant.ONE);
        Result<List<SearchShippingOrderListResponseModel>> listResult = shippingOrderClient.export(requestModel);
        listResult.throwException();
        List<SearchShippingOrderListResponseDto> list = MapperUtils.mapper(listResult.getData(), SearchShippingOrderListResponseDto.class, new ShippingOrderSearchListMapping());
        String fileName = "零担运输单管理" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        try {
            YeloExcelUtils.doExportMoreThreadByOneSheet(response, list, false, SearchShippingOrderListResponseDto.class, fileName);
        } catch (Exception e) {
            return;
        }
    }

    /**
     * 查询详情 3.27.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/getDetail")
    public Result<GetShippingOrderDetailResponseDto> getDetail(@RequestBody @Valid GetShippingOrderDetailRequestDto requestDto){
        GetShippingOrderDetailRequestModel requestModel = MapperUtils.mapper(requestDto, GetShippingOrderDetailRequestModel.class);
        requestModel.setSource(CommonConstant.ONE);
        Result<GetShippingOrderDetailResponseModel> result = shippingOrderClient.getDetail(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), GetShippingOrderDetailResponseDto.class, new ShippingOrderGetDetailMapping()));
    }

    /**
     * 审核 3.27.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/audit")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> audit(@RequestBody @Valid ShippingOrderAuditRequestDto requestDto){
        return shippingOrderClient.audit(MapperUtils.mapper(requestDto, ShippingOrderAuditRequestModel.class));
    }



    /**
     * 重新报价 3.27.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/reQuote")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> reQuote(@RequestBody @Valid ShippingOrderReQuoteRequestDto requestDto){
        ShippingOrderReQuoteRequestModel requestModel = MapperUtils.mapper(requestDto, ShippingOrderReQuoteRequestModel.class);
        requestModel.setSource(CommonConstant.ONE);
        return shippingOrderClient.reQuote(requestModel);
    }

    /**
     * 查看路径规划 3.27.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/getRoutePlan")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<List<GetShippingOrderRoutePlanResponseDto>> getRoutePlan(@RequestBody @Valid GetShippingOrderDetailRequestDto requestDto){
        GetShippingOrderDetailRequestModel requestModel = MapperUtils.mapper(requestDto, GetShippingOrderDetailRequestModel.class);
        requestModel.setSource(CommonConstant.ONE);
        Result<List<GetShippingOrderRoutePlanResponseModel>> result = shippingOrderClient.getRoutePlan(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), GetShippingOrderRoutePlanResponseDto.class));
    }
}
