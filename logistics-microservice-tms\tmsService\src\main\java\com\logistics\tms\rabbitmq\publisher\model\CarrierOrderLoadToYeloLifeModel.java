package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/6/20 13:09
 */
@Data
public class CarrierOrderLoadToYeloLifeModel {

    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("客户单号")
    private String customerOrderCode;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty(value = "入库单sku明细")
    private List<CarrierOrderGoodsToYeloLifeModel> lifeGoodsModels;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "操作人")
    private String userName;


    @ApiModelProperty(value = "操作时间")
    private Date date = new Date();



}
