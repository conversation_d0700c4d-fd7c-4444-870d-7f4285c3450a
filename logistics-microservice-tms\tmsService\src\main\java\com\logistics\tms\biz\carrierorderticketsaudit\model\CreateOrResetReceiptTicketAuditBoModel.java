package com.logistics.tms.biz.carrierorderticketsaudit.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class CreateOrResetReceiptTicketAuditBoModel {

    /**
     * 委托来源
     */
    private Integer source;

    /**
     * 委托类型
     */
    private Integer entrustType;

    /**
     * 票据类型
     */
    private Integer ticketType;

    /**
     * 运单Id
     */
    private Long carrierOrderId;

    /**
     * 运单号
     */
    private String carrierOrderCode;

    /**
     * 运单状态
     */
    private Integer carrierOrderStatus;

    /**
     * 签收单相对路径
     */
    private List<String> ticketsPathList;
}
