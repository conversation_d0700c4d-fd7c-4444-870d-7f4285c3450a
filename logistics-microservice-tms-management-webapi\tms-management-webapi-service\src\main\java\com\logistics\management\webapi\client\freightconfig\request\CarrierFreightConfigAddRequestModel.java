package com.logistics.management.webapi.client.freightconfig.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class CarrierFreightConfigAddRequestModel {

    @ApiModelProperty(value = "需求类型", required = true)
    @NotEmpty(message = "请选择需求类型")
    private List<String> entrustTypes;

    @ApiModelProperty(value = "价格模式", required = true)
    @NotBlank(message = "请选择价格模式")
    private Integer configType;

    @ApiModelProperty(value = "车主运价ID", required = true)
    @NotBlank(message = "车主运价ID不能为空")
    private Long carrierFreightId;
}
