package com.logistics.management.webapi.client.staff.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FuzzyQueryDriverInfoFeignRequestModel extends AbstractPageForm<FuzzyQueryDriverInfoFeignRequestModel> {
    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private String type;
    @ApiModelProperty("模糊匹配-可根据司机名称/手机号")
    private String fuzzyDriverField;
    @ApiModelProperty("启用 1，禁用 0")
    private Integer enabled;
}
