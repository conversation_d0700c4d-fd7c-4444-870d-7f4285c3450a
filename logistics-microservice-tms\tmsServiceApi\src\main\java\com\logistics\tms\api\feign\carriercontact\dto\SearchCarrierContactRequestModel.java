package com.logistics.tms.api.feign.carriercontact.dto;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class SearchCarrierContactRequestModel extends AbstractPageForm<SearchCarrierContactRequestModel> {

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("车主类型：1 企业 2 个人")
    private Integer type;

    @ApiModelProperty("公司名字")
    private String companyCarrierName;

    @ApiModelProperty("账号状态 全部为空 禁用0 启用1")
    private Integer carrierContactStatus;

    private String companyIds;//主表id
}
