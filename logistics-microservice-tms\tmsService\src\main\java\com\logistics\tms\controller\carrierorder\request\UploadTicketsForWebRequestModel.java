package com.logistics.tms.controller.carrierorder.request;

import com.logistics.tms.controller.carrierorder.response.TicketsForWebModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/2
 */
@Data
public class UploadTicketsForWebRequestModel {

	@ApiModelProperty(value = "运单ID")
	private Long carrierOrderId;

	//前台目前仅能编辑签收单
	@ApiModelProperty(value = "图片类型：3 签收单")
	private Integer imageType;

	@ApiModelProperty(value = "图片信息")
	private List<TicketsForWebModel> imageInfoList;
}
