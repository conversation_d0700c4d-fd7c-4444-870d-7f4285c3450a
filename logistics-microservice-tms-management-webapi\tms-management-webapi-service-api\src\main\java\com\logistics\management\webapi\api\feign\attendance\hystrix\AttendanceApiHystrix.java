package com.logistics.management.webapi.api.feign.attendance.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.attendance.AttendanceApi;
import com.logistics.management.webapi.api.feign.attendance.dto.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/14
 */
@Component
public class AttendanceApiHystrix implements AttendanceApi {

	@Override
	public Result<PageInfo<SearchAttendanceListResponseDto>> searchAttendanceList(SearchAttendanceListRequestDto requestDto) {
		return Result.timeout();
	}

	@Override
	public void exportAttendanceList(SearchAttendanceListRequestDto requestDto, HttpServletResponse response) {
		Result.timeout();
	}

	@Override
	public Result<AttendanceDetailResponseDto> attendanceDetail(AttendanceDetailRequestDto requestDto) {
		return Result.timeout();
	}

	@Override
	public Result<PageInfo<SearchAttendanceChangeListResponseDto>> searchAttendanceChangeList(SearchAttendanceChangeListRequestDto requestDto) {
		return Result.timeout();
	}

	@Override
	public void exportAttendanceChangeList(SearchAttendanceChangeListRequestDto requestDto, HttpServletResponse response) {
		Result.timeout();
	}

	@Override
	public Result<AttendanceChangeDetailResponseDto> attendanceChangeDetail(AttendanceChangeDetailRequestDto requestDto) {
		return Result.timeout();
	}

	@Override
	public Result<Boolean> auditAttendanceChangeApply(AuditAttendanceChangeApplyRequestDto requestDto) {
		return Result.timeout();
	}

	@Override
	public Result<Boolean> cancelAttendanceChangeApply(CancelAttendanceChangeApplyRequestDto requestDto) {
		return Result.timeout();
	}
}
