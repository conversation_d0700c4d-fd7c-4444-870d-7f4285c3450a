package com.logistics.tms.api.feign.mailinginfo.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class MailingInfoModifyRequestModel {

    @ApiModelProperty(value = "邮寄地址", required = true)
    @NotBlank(message = "邮寄地址id不能为空")
    private String mailingInfoId;

    @ApiModelProperty(value = "收货地址,1-100字符", required = true)
    private String address;

    @ApiModelProperty(value = "收货联系人,1-20个字符", required = true)
    private String addressee;

    @ApiModelProperty(value = "收货联系人手机号,11-20个字符", required = true)
    private String addresseeMobile;

    @ApiModelProperty(value = "应用场景,1-50个字符", required = true)
    private String applyScope;
}
