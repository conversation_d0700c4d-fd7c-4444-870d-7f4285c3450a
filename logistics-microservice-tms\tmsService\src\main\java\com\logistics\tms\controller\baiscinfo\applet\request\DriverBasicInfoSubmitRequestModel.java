package com.logistics.tms.controller.baiscinfo.applet.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/11
 */
@Data
public class DriverBasicInfoSubmitRequestModel {

	@ApiModelProperty(value = "姓名,2-50个汉字")
	private String name;

	@ApiModelProperty(value = "身份证号,18-21位")
	private String identityNumber;

	@ApiModelProperty(value = "身份证头像面")
	private String identityFaceFile;

	@ApiModelProperty(value = "身份证国徽面")
	private String identityNationalFile;

	@ApiModelProperty(value = "认证类型,1:手机号认证 2:人脸识别")
	private Integer authModel;

	@ApiModelProperty("人脸识别传入唯一字符串(人脸识别认证传)")
	private String orderNo;

	@ApiModelProperty(value = "短信验证码")
	private String verificationCode;
}
