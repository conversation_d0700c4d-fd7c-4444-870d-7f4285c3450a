package com.logistics.management.webapi.base.enums;

/**
 * 乐橘新生业务类型枚举
 * Created by yuhong.lin on 2019/1/18
 */
public enum LifeBusinessTypeEnum {
    DEFAULT(-1, ""),
    COMPANY(1, "公司"),
    PERSONAGE(2, "个人");

    private final Integer key;
    private final String value;

    LifeBusinessTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static LifeBusinessTypeEnum getEnum(Integer key) {
        for (LifeBusinessTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
