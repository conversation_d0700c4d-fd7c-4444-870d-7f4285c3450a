package com.logistics.management.webapi.client.reserveapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class ReserveApplyDetailResponseModel {

    @ApiModelProperty("司机Id")
    private Long driverId;

    @ApiModelProperty("申请记录id")
    private Long applyId;

    @ApiModelProperty("申请单号")
    private String applyCode;

    @ApiModelProperty("状态：-1 已撤销，0 待业务审核，1 待财务审核，2 已驳回，3 待打款，4 已打款")
    private Integer status;

    @ApiModelProperty("收款账号")
    private String receiveAccount;

    @ApiModelProperty("收款银行名称")
    private String receiveAccountName;

    @ApiModelProperty("收款支行名称")
    private String receiveBraBankName;

    @ApiModelProperty("司机姓名")
    private String driverName;

    @ApiModelProperty("司机手机号")
    private String driverMobile;

    @ApiModelProperty("余额")
    private BigDecimal balanceAmount;

    @ApiModelProperty("待核销金额")
    private BigDecimal awaitVerificationAmount;

    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("业务审核人")
    private String businessAuditor;

    @ApiModelProperty("业务审核时间")
    private Date businessAuditTime;

    @ApiModelProperty("业务批准金额")
    private BigDecimal approveAmount;

    @ApiModelProperty("业务审核备注")
    private String businessRemark;

    @ApiModelProperty("财务审核人")
    private String financeAuditor;

    @ApiModelProperty("财务审核时间")
    private Date financeAuditTime;

    @ApiModelProperty("财务审核备注")
    private String financeRemark;

    @ApiModelProperty(value = "打款编号 ")
    private String remitCode;

    @ApiModelProperty(value = "打款日期")
    private Date remitDate;

    @ApiModelProperty(value = "打款凭据")
    private List<String> remitTickets;

    @ApiModelProperty(value = "打款备注")
    private String remitRemark;

    @ApiModelProperty(value = "操作时间")
    private Date remitOperateTime;
}
