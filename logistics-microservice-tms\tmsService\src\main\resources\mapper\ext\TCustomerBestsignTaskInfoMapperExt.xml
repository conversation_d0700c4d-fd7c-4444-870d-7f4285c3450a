<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCustomerBestsignTaskInfoMapper">
    <select id="getCustomerBestSignTaskList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_customer_bestsign_task_info
        where valid = 1
        <if test="certApplyStatus != null and certApplyStatus != ''">
            and cert_apply_status in (${certApplyStatus})
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
    </select>

    <update id="batchUpdate" parameterType="com.logistics.tms.entity.TCustomerBestsignTaskInfo">
        <foreach collection="list" item="item" separator=";">
            update t_customer_bestsign_task_info
            <set>
                <if test="item.realNameAuthId != null">
                    real_name_auth_id = #{item.realNameAuthId,jdbcType=BIGINT},
                </if>
                <if test="item.bestsignAccount != null">
                    bestsign_account = #{item.bestsignAccount,jdbcType=VARCHAR},
                </if>
                <if test="item.taskId != null">
                    task_id = #{item.taskId,jdbcType=VARCHAR},
                </if>
                <if test="item.taskExpireTime != null">
                    task_expire_time = #{item.taskExpireTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.certApplyStatus != null">
                    cert_apply_status = #{item.certApplyStatus,jdbcType=INTEGER},
                </if>
                <if test="item.message != null">
                    message = #{item.message,jdbcType=VARCHAR},
                </if>
                <if test="item.status != null">
                    status = #{item.status,jdbcType=INTEGER},
                </if>
                <if test="item.applyErrorCount != null">
                    apply_error_count = #{item.applyErrorCount,jdbcType=INTEGER},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectByRealNameIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_customer_bestsign_task_info
        where valid = 1
        <choose>
            <when test="ids != null and ids.size() != 0">
                and id in
                <foreach collection="ids" open="(" separator="," close=")" item="item">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </select>
</mapper>