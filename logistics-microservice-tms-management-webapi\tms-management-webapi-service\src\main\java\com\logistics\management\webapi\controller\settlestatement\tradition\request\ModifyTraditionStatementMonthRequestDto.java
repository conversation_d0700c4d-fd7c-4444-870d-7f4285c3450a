package com.logistics.management.webapi.controller.settlestatement.tradition.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/9
 */
@Data
public class ModifyTraditionStatementMonthRequestDto {

	@ApiModelProperty(value = "对账单id", required = true)
	@NotBlank(message = "id不能为空")
	private String settleStatementId;

	@ApiModelProperty(value = "对账月份 yyyy-MM",required = true)
	@NotBlank(message = "请填写对账月份")
	private String settleStatementMonth;
}
