package com.logistics.tms.api.impl.driverpayee;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.common.SrcUrlModel;
import com.logistics.tms.api.feign.driverpayee.DriverPayeeServiceApi;
import com.logistics.tms.api.feign.driverpayee.model.*;
import com.logistics.tms.biz.driverpayee.DriverPayeeBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class DriverPayeeServiceApiImpl implements DriverPayeeServiceApi {

    @Autowired
    private DriverPayeeBiz driverPayeeBiz;

    /**
     * 司机收款人账户列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<DriverPayeeListResponseModel>> driverPayeeList(@RequestBody DriverPayeeListRequestModel requestModel) {
        return Result.success(driverPayeeBiz.driverPayeeList(requestModel));
    }

    /**
     * 导出司机收款人账户列表
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<ExportDriverPayeeListResponseModel>> exportDriverPayeeList(@RequestBody DriverPayeeListRequestModel requestModel) {
        return Result.success(driverPayeeBiz.exportDriverPayeeList(requestModel));
    }

    /**
     * 司机收款人账户新增/修改
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> addOrModifyDriverPayee(@RequestBody AddOrModifyDriverPayeeRequestModel requestModel) {
        driverPayeeBiz.addOrModifyDriverPayee(requestModel);
        return Result.success(true);
    }

    /**
     * 司机收款人账户详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<DriverPayeeDetailResponseModel> driverPayeeDetail(@RequestBody DriverPayeeDetailRequestModel requestModel) {
        return Result.success(driverPayeeBiz.driverPayeeDetail(requestModel));
    }

    /**
     * 司机收款人审核驳回作废
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> auditOrReject(@RequestBody AuditRejectDriverPayeeRequestModel requestModel) {
        driverPayeeBiz.auditOrReject(requestModel);
        return Result.success(true);
    }

    /**
     * 导入
     * @param importDriverPayeeRequestModel
     * @return
     */
    @Override
    public Result<ImportDriverPayeeResponseModel> importDriverPayee(@RequestBody ImportDriverPayeeRequestModel importDriverPayeeRequestModel) {
        return Result.success(driverPayeeBiz.importDriverPayee(importDriverPayeeRequestModel));
    }

    /**
     * 导入证件信息
     * @param srcUrlModel
     * @return
     */
    @Override
    public Result<Boolean> importDriverPayeeCertificate(@RequestBody SrcUrlModel srcUrlModel) {
        driverPayeeBiz.importDriverPayeeCertificate(srcUrlModel);
        return Result.success(true);
    }

    /**
     * 查看日志
     * @param requestModel
     * @return
     */
    @Override
    public Result<List<ViewLogResponseModel>> driverPayeeLogs(@RequestBody DriverPayeeDetailRequestModel requestModel) {
        return Result.success(driverPayeeBiz.driverPayeeLogs(requestModel));
    }

    /**
     * 查询已审核的收款账户信息
     * @param requestModel
     * @return
     */
    @Override
    public Result<List<SearchDriverPayeesResponseModel>> searchDriverPayees(@RequestBody SearchDriverPayeesRequestModel requestModel) {
        return Result.success(driverPayeeBiz.searchDriverPayees(requestModel));
    }
}
