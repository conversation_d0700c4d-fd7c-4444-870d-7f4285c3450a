package com.logistics.tms.biz.companyentrust

import com.logistics.tms.controller.companyentrust.request.AddOrModifyCompanyEntrustRequestModel
import com.logistics.tms.controller.companyentrust.response.CompanyEntrustDetailResponseModel
import com.logistics.tms.controller.companyentrust.request.CompanyEntrustIdRequestModel
import com.logistics.tms.controller.companyentrust.request.SearchCompanyEntrustByNameRequestModel
import com.logistics.tms.controller.companyentrust.response.SearchCompanyEntrustByNameResponseModel
import com.logistics.tms.controller.companyentrust.request.SearchCompanyEntrustRequestModel
import com.logistics.tms.controller.companyentrust.response.SearchCompanyEntrustResponseModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TCompany
import com.logistics.tms.mapper.TCompanyEntrustMapper
import com.logistics.tms.mapper.TCompanyMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class CompanyEntrustBizTest extends Specification {
    @Mock
    TCompanyEntrustMapper companyEntrustMapper
    @Mock
    TCompanyMapper companyMapper
    @Mock
    CommonBiz commonBiz
    @InjectMocks
    CompanyEntrustBiz companyEntrustBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(companyEntrustMapper.searchList(any())).thenReturn([new SearchCompanyEntrustResponseModel()])

        expect:
        companyEntrustBiz.searchList(requestModel) == expectedResult

        where:
        requestModel                           || expectedResult
        new SearchCompanyEntrustRequestModel() || [new SearchCompanyEntrustResponseModel()]
    }

    @Unroll
    def "get Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(companyEntrustMapper.getDetail(anyLong())).thenReturn(new CompanyEntrustDetailResponseModel())

        expect:
        companyEntrustBiz.getDetail(requestModel) == expectedResult

        where:
        requestModel                       || expectedResult
        new CompanyEntrustIdRequestModel() || new CompanyEntrustDetailResponseModel()
    }

    @Unroll
    def "save Company where requestModel=#requestModel"() {
        given:
        when(companyMapper.getByName(anyString())).thenReturn(new TCompany(companyName: "companyName", tradingCertificateImage: "tradingCertificateImage", tradingCertificateValidityTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 11).getTime(), tradingCertificateIsForever: 0, tradingCertificateIsAmend: 0))
        when(companyMapper.updateByPrimaryKeySelectiveForTime(any())).thenReturn(0)
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")

        expect:
        companyEntrustBiz.saveCompany(requestModel)
        assert expectedResult == false

        where:
        requestModel                                || expectedResult
        new AddOrModifyCompanyEntrustRequestModel() || true
    }

    @Unroll
    def "search Company Entrust By Name where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(companyEntrustMapper.searchCompanyEntrustByName(anyString())).thenReturn([new SearchCompanyEntrustByNameResponseModel()])

        expect:
        companyEntrustBiz.searchCompanyEntrustByName(requestModel) == expectedResult

        where:
        requestModel                                 || expectedResult
        new SearchCompanyEntrustByNameRequestModel() || [new SearchCompanyEntrustByNameResponseModel()]
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme