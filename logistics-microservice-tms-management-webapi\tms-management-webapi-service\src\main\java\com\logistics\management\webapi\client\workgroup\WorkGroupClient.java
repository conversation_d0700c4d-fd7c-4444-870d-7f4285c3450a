package com.logistics.management.webapi.client.workgroup;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.workgroup.hystrix.WorkGroupClientHystrix;
import com.logistics.management.webapi.client.workgroup.request.*;
import com.logistics.management.webapi.client.workgroup.response.SearchWorkGroupListResponseModel;
import com.logistics.management.webapi.client.workgroup.response.WorkGroupDetailResponseModel;
import com.logistics.management.webapi.client.workgroup.response.WorkGroupNodeResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2023/12/22 16:03
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/workGroup",
        fallback = WorkGroupClientHystrix.class)
public interface WorkGroupClient {

    @PostMapping(value = "/searchList")
    @ApiOperation(value = "列表")
    Result<PageInfo<SearchWorkGroupListResponseModel>> searchList(@RequestBody SearchWorkGroupListRequestModel requestModel);

    @PostMapping(value = "/workGroupEnable")
    @ApiOperation(value = "列表-禁用/启用")
    Result<Boolean> workGroupEnable(@RequestBody WorkGroupEnableRequestModel requestModel);

    @PostMapping(value = "/delWorkGroup")
    @ApiOperation(value = "列表-删除")
    Result<Boolean> delWorkGroup(@RequestBody WorkGroupIdRequestModel requestModel);

    @PostMapping(value = "/getDetail")
    @ApiOperation(value = "推送配置详情")
    Result<WorkGroupDetailResponseModel> getDetail(@RequestBody WorkGroupIdRequestModel requestModel);

    @PostMapping(value = "/addEditWorkGroup")
    @ApiOperation(value = "新增/编辑推送配置")
    Result<Boolean> addEditWorkGroup(@RequestBody AddEditWorkGroupRequestModel requestModel);

    @PostMapping(value = "/addEditNode")
    @ApiOperation(value = "新增/编辑配置节点信息")
    Result<Boolean> addEditNode(@RequestBody AddEditWorkGroupNodeRequestModel requestModel);

    @PostMapping(value = "/getNodeInfo")
    @ApiOperation(value = "配置节点信息")
    Result<WorkGroupNodeResponseModel> getNodeInfo(@RequestBody WorkGroupIdRequestModel requestModel);
}
