package com.logistics.tms.controller.invoicingmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2024/3/20 9:13
 */
@Data
public class GetAddSettleStatementListResponseModel {
    @ApiModelProperty("对账单id")
    private Long settleStatementId;

    @ApiModelProperty("对账月份")
    private String settleStatementMonth;

    @ApiModelProperty("对账单号")
    private String settleStatementCode;

    @ApiModelProperty("对账费用")
    private BigDecimal reconciliationFee;

    @ApiModelProperty("对账单名称")
    private String settleStatementName;
}
