<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleLengthMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TVehicleLength" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="vehicle_length" property="vehicleLength" jdbcType="DECIMAL" />
    <result column="carriage_scope_min" property="carriageScopeMin" jdbcType="DECIMAL" />
    <result column="carriage_scope_max" property="carriageScopeMax" jdbcType="DECIMAL" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, vehicle_length, carriage_scope_min, carriage_scope_max, created_by, created_time, 
    last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_vehicle_length
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_vehicle_length
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TVehicleLength" >
    insert into t_vehicle_length (id, vehicle_length, carriage_scope_min, 
      carriage_scope_max, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{vehicleLength,jdbcType=DECIMAL}, #{carriageScopeMin,jdbcType=DECIMAL}, 
      #{carriageScopeMax,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TVehicleLength" >
    insert into t_vehicle_length
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="vehicleLength != null" >
        vehicle_length,
      </if>
      <if test="carriageScopeMin != null" >
        carriage_scope_min,
      </if>
      <if test="carriageScopeMax != null" >
        carriage_scope_max,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vehicleLength != null" >
        #{vehicleLength,jdbcType=DECIMAL},
      </if>
      <if test="carriageScopeMin != null" >
        #{carriageScopeMin,jdbcType=DECIMAL},
      </if>
      <if test="carriageScopeMax != null" >
        #{carriageScopeMax,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TVehicleLength" >
    update t_vehicle_length
    <set >
      <if test="vehicleLength != null" >
        vehicle_length = #{vehicleLength,jdbcType=DECIMAL},
      </if>
      <if test="carriageScopeMin != null" >
        carriage_scope_min = #{carriageScopeMin,jdbcType=DECIMAL},
      </if>
      <if test="carriageScopeMax != null" >
        carriage_scope_max = #{carriageScopeMax,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TVehicleLength" >
    update t_vehicle_length
    set vehicle_length = #{vehicleLength,jdbcType=DECIMAL},
      carriage_scope_min = #{carriageScopeMin,jdbcType=DECIMAL},
      carriage_scope_max = #{carriageScopeMax,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>