package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2024/03/20
*/
@Data
public class TInvoicingSettleStatement extends BaseEntity {
    /**
    * 发票管理id
    */
    @ApiModelProperty("发票管理id")
    private Long invoicingId;

    /**
    * 对账单表Id
    */
    @ApiModelProperty("对账单表Id")
    private Long settleStatementId;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}