package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/10/27 14:39
 */
@Data
public class LogisticsLoadValidityStatisticsResponseModel {
    @ApiModelProperty("统计年月")
    private String yearMonth;
    @ApiModelProperty("发货类型：单/天（提货运单：提货时效/提货数量）")
    private BigDecimal deliverCount = BigDecimal.ZERO;
    @ApiModelProperty("回收类型：单/天（提货运单：提货时效/提货数量）")
    private BigDecimal recycleCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨类型：单/天（提货运单：提货时效/提货数量）")
    private BigDecimal transfersCount = BigDecimal.ZERO;
}
