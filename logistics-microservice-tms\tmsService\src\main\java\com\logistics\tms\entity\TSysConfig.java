package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2023/02/03
*/
@Data
public class TSysConfig extends BaseEntity {
    /**
    * 配置分组Code
    */
    @ApiModelProperty("配置分组Code")
    private String groupCode;

    /**
    * 配置分组描述
    */
    @ApiModelProperty("配置分组描述")
    private String groupDesc;

    /**
    * 配置Key
    */
    @ApiModelProperty("配置Key")
    private String configKey;

    /**
    * 配置Value
    */
    @ApiModelProperty("配置Value")
    private String configValue;

    /**
    * 配置描述
    */
    @ApiModelProperty("配置描述")
    private String configDesc;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}