package com.logistics.management.webapi.api.feign.attendance.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 考勤打卡详情查询响应dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/15
 */
@Data
public class AttendanceDetailResponseDto {

	@ApiModelProperty("考勤用户,【机构】姓名_手机号")
	private String attendanceUser = "";

	@ApiModelProperty("考勤日期")
	private String attendanceDate = "";

	@ApiModelProperty("上班打卡日期")
	private String onDutyPunchDate = "";

	@ApiModelProperty("上班打卡时间")
	private String onDutyPunchTime = "";

	@ApiModelProperty("下班打卡日期")
	private String offDutyPunchDate = "";

	@ApiModelProperty("下班打卡时间")
	private String offDutyPunchTime = "";

	@ApiModelProperty("上班打卡图片")
	private String onDutyPunchPic = "";

	@ApiModelProperty("下班打卡图片")
	private String offDutyPunchPic = "";
}
