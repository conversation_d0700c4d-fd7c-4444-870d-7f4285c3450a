/**
 * Created by yun<PERSON>zhou on 2017/10/23.
 */
package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ConfigKeyEnum {

    QIYA_COMPANY_NAME(ConfigGroupCodeEnum.COMPANY_NAME.getCode(), "qiya_company_name", "江苏乐橘云途公司名"),

    ;
    private final String groupCode;
    private final String value;
    private final String remark;

    public enum ConfigGroupCodeEnum {

        DEFAULT,
        COMPANY_NAME,
        ;

        public String getCode() {
            return name().toLowerCase();
        }
    }
}
