package com.logistics.tms.base.enums;


public enum ShippingOrderItemSourceEnum {
    INITIAL(1,"初始新增"),
    LATER_ADD(2,"后期加入"),

    ;

    private Integer key;
    private String value;

    ShippingOrderItemSourceEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
