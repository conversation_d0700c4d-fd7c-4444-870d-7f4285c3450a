<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TSysMailingAddressMapper">
    <select id="selectAllMailingInfo" resultType="com.logistics.tms.api.feign.mailinginfo.model.response.MailingInfoListResponseModel">
        select id               as mailingInfoId,
             address            as address,
             addressee          as addressee,
             addressee_mobile   as addresseeMobile,
             apply_scope        as applyScope,
             last_modified_by   as lastModifiedBy,
             last_modified_time as lastModifiedTime,
             valid
        from t_sys_mailing_address
        where valid = 1
        order by last_modified_time desc, id desc
    </select>

    <select id="selectOneById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_sys_mailing_address
        where valid = 1
        and id = #{mailingId}
    </select>

    <update id="updateMailingInfo" parameterType="com.logistics.tms.entity.TSysMailingAddress">
        update t_sys_mailing_address
        <set>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="addressee != null">
                addressee = #{addressee,jdbcType=VARCHAR},
            </if>
            <if test="addresseeMobile != null">
                addressee_mobile = #{addresseeMobile,jdbcType=VARCHAR},
            </if>
            <if test="applyScope != null">
                apply_scope = #{applyScope,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>