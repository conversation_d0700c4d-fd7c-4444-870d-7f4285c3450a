package com.logistics.appapi.client.thirdparty.basicdata.file.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/5/8 16:32
 */
@Data
public class FileUploadForAIResponseModel {
    @ApiModelProperty("图片全路径")
    private String src="";
    @ApiModelProperty("图片相对路径")
    private String relativePath="";
    @ApiModelProperty("身份证头像面文字信息")
    private IDCardFrontResponseModel cardFront;
    @ApiModelProperty("身份证非头像面文字信息")
    private IDCardBackResponseModel cardBack;
    @ApiModelProperty("营业执照文字信息")
    private BusinessLicenseResponseModel businessLicense;
    @ApiModelProperty("车牌号文字信息")
    private PlateLicenseResponseModel plateLicense;
    @ApiModelProperty("驾驶证文字信息")
    private DrivingLicenseResponseModel drivingLicense;
    @ApiModelProperty("行驶证主页文字信息")
    private VehicleLicenseResponseModel vehicleLicense;
    @ApiModelProperty("行驶证副页文字信息")
    private VehicleLicenseBackResponseModel vehicleLicenseBack;
    @ApiModelProperty("银行卡文字信息")
    private BankCardResponseModel bankCard;
    @ApiModelProperty("护照文字信息")
    private PassPortResponseModel passPort;
    @ApiModelProperty("车牌文字信息")
    private TrainTicketResponseModel trainTicket;
}
