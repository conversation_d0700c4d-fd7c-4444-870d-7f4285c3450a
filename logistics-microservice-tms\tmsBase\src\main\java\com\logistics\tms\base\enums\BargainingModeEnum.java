package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: wjf
 * @date: 2024/8/6 11:13
 */
@Getter
@AllArgsConstructor
public enum BargainingModeEnum {

    DEFAULT(0, ""),
    ASSIGN_CARRIER(1, "指定车主"),
    BIDDING_PRICE(2, "竞价抢单"),
    PROVISIONAL_PRICING(3, "临时定价"),
    LESS_THAN_TRUCKLOAD_PRICING(4, "零担定价"),
    ;

    private final Integer key;
    private final String value;

}
