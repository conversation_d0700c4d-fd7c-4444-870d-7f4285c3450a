package com.logistics.tms.biz.dispatchorder;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CompanyTypeEnum;
import com.logistics.tms.base.enums.DemandOrderSourceEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.dispatchorder.request.DispatchOrderCarrierRequestModel;
import com.logistics.tms.controller.dispatchorder.request.DispatchOrderSearchRequestModel;
import com.logistics.tms.controller.dispatchorder.request.VehicleDriverRelationQueryRequestModel;
import com.logistics.tms.controller.dispatchorder.response.*;
import com.logistics.tms.mapper.TCarrierDriverRelationMapper;
import com.logistics.tms.mapper.TCarrierOrderGoodsMapper;
import com.logistics.tms.mapper.TCarrierOrderMapper;
import com.logistics.tms.mapper.TDispatchOrderMapper;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Slf4j
@Service
public class DispatchOrderBiz {

    @Autowired
    private TDispatchOrderMapper dispatchOrderMapper;
    @Autowired
    private TCarrierOrderMapper carrierOrderMapper;
    @Autowired
    private TCarrierOrderGoodsMapper carrierOrderGoodsMapper;
    @Autowired
    private TCarrierDriverRelationMapper tCarrierDriverRelationMapper;
    @Autowired
    private CommonBiz commonBiz;
    /**
     * 调度单列表
     * @param requestModel
     * @return
     */
    public PageInfo<DispatchOrderSearchResponseModel> searchList(DispatchOrderSearchRequestModel requestModel) {
        Long companyCarrierId = null;
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        }
        requestModel.enablePaging();
        List<Long> idList = dispatchOrderMapper.searchIdList(requestModel,companyCarrierId);

        PageInfo pageInfo = new PageInfo(idList);
        List<DispatchOrderSearchResponseModel> list = new ArrayList<>();
        if(ListUtils.isNotEmpty(idList)){
            list = dispatchOrderMapper.searchList(StringUtils.listToString(idList,','));
        }
        pageInfo.setList(list == null ? new ArrayList() : list);
        return pageInfo;
    }


    /**
     * 根据调度单id查询运单列表
     * @param requestModel
     * @return
     */
    public List<DispatchOrderCarrierChildResponseModel> getCarrierOrder(DispatchOrderCarrierRequestModel requestModel){
        return carrierOrderMapper.getCarrierOrderByDispatchOrderId(requestModel);
    }

    /**
     * 调度单详情
     * @param requestModel
     * @return
     */
    public DispatchOrderDetailResponseModel getDetail(DispatchOrderCarrierRequestModel requestModel) {
        DispatchOrderDetailResponseModel detail = dispatchOrderMapper.getDetail(requestModel.getDispatchOrderId());
        if (detail==null){
            return new DispatchOrderDetailResponseModel();
        }
        if (CommonConstant.TWO.equals(requestModel.getSource())) {
            commonBiz.checkAppCarrierPermission();
        }

        List<CarrierOrderGoodsResponseModel> carrierOrderGoodsList = carrierOrderGoodsMapper.getByDispatchOrderId(requestModel.getDispatchOrderId());
        if (ListUtils.isNotEmpty(carrierOrderGoodsList)) {
            detail.setEntrustType(carrierOrderGoodsList.get(0).getEntrustType());
            //个人车主展示姓名+手机号
            CarrierOrderGoodsResponseModel carrierOrderGoodsResponseModel = carrierOrderGoodsList.get(0);
            if (CompanyTypeEnum.PERSON.getKey().equals(carrierOrderGoodsResponseModel.getCompanyCarrierType())) {
                //个人车主
                detail.setCompanyCarrierName(carrierOrderGoodsResponseModel.getCarrierContactName() + " " + carrierOrderGoodsResponseModel.getCarrierContactPhone());
            } else {
                detail.setCompanyCarrierName(carrierOrderGoodsResponseModel.getCompanyCarrierName());
            }
            for (CarrierOrderGoodsResponseModel goodTemp : carrierOrderGoodsList) {
                if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(goodTemp.getSource())) {
                    goodTemp.setCustomerOrderCode("");
                }
            }
        }

        detail.setCarrierOrderGoodsList(carrierOrderGoodsList == null ? new ArrayList<>() : carrierOrderGoodsList);
        return detail;
    }

    /**
     * 根据司机姓名联系方式查询
     * @param requestModel
     * @return
     */
    public List<DriverByNameAndPhoneResponseModel> getDriverByNameAndPhone(VehicleDriverRelationQueryRequestModel requestModel) {
        Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        List<DriverByNameAndPhoneResponseModel> driverSearchResponseModels = tCarrierDriverRelationMapper.getDriverByNameAndPhone(companyCarrierId,requestModel.getDriverNameAndPhone());
        if (ListUtils.isNotEmpty(driverSearchResponseModels)){
            return driverSearchResponseModels;
        }
        return new ArrayList<>();
    }
}
