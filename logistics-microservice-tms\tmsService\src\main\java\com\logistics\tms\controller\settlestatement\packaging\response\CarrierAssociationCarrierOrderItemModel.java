package com.logistics.tms.controller.settlestatement.packaging.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CarrierAssociationCarrierOrderItemModel {

	@ApiModelProperty("对账单明细Id")
	private Long settleStatementItemId;
	@ApiModelProperty("运单id")
	private Long carrierOrderId;
	@ApiModelProperty("运单号")
	private String carrierOrderCode;
	@ApiModelProperty("货主名称")
	private String companyEntrustName;
	@ApiModelProperty("车主类型：1 公司，2 个人")
	private Integer companyCarrierType;
	@ApiModelProperty("车主公司名称")
	private String companyCarrierName;
	@ApiModelProperty("车主联系人姓名")
	private String contactName;
	@ApiModelProperty("车主联系人手机号")
	private String contactPhone;
	@ApiModelProperty("结算数量")
	private BigDecimal settlementAmount;
	@ApiModelProperty("单位：1 件，2 吨，3 件（方），4 块")
	private Integer goodsUnit;
	@ApiModelProperty("委托费用")
	private BigDecimal entrustFreight;
	@ApiModelProperty("临时费用")
	private BigDecimal otherFees;
	@ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
	private Integer demandOrderEntrustType;
}
