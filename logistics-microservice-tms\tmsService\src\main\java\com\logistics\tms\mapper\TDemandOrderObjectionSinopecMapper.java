package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.demandorderobjectionsinopec.model.GetSinopecObjectionDetailResponseModel;
import com.logistics.tms.api.feign.demandorderobjectionsinopec.model.SearchDemandOrderObjectionSinopecRequestModel;
import com.logistics.tms.api.feign.demandorderobjectionsinopec.model.SearchDemandOrderObjectionSinopecResponseModel;
import com.logistics.tms.entity.TDemandOrderObjectionSinopec;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TDemandOrderObjectionSinopecMapper extends BaseMapper<TDemandOrderObjectionSinopec> {

    List<SearchDemandOrderObjectionSinopecResponseModel> searchSinopecObjection(@Param(("params")) SearchDemandOrderObjectionSinopecRequestModel requestModel);

    GetSinopecObjectionDetailResponseModel getSinopecObjectionDetail(@Param("id")Long id);

    TDemandOrderObjectionSinopec selectByDemandOrderId(@Param("demandOrderId")Long demandOrderId);
}