package com.logistics.management.webapi.client.freightconfig.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigEditRequestModel extends CarrierFreightConfigRequestModel {

    @ApiModelProperty(value = "需求类型", required = true)
    @NotEmpty(message = "请选择需求类型")
    private List<String> entrustTypes;
}
