package com.logistics.appapi.base.constant;

import com.google.common.base.Joiner;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Map;
import java.util.function.BiFunction;

/**
 * Created by yun.z<PERSON> on 2017/10/23.
 */
public class CommonConstant {
    private CommonConstant() {
    }

    public static final String COMMA = ",";
    public static final String SEMICOLON = "；";
    public static final String DATE_TO_STRING_YMD_PATTERN = "yyyy-MM-dd";

    public static final String BLANK_TEXT = "";
    public static final String RETURN_SUCCESS = "1";
    public static final String RETURN_FAILURE = "-1";

    public static final String ONE = "1";
    public static final String TWO = "2";
    public static final String THREE = "3";
    public static final String ZERO = "0";
    public static final String FOUR = "4";
    public static final String FIVE = "5";
    public static final String SIX = "6";
    public static final String SEVEN = "7";
    public static final String EIGHT = "8";
    public static final String NINE = "9";
    public static final String TEN = "10";
    public static final String LIMITED_IDEMPOTENT_MESSAGE = "请勿重复请求，稍后再试";
    public static final BigDecimal BIG_DECIMAL_ZERO = new BigDecimal(0);
    public static final BigDecimal NEGATIVE_ONE = new BigDecimal(-1);
    public static final BigDecimal BIG_DECIMAL_ONE = new BigDecimal(1);
    public static final BigDecimal BIG_DECIMAL_TWO = new BigDecimal(2);
    public static final BigDecimal BIG_DECIMAL_THREE = new BigDecimal(3);
    public static final BigDecimal BIG_DECIMAL_FOUR = new BigDecimal(4);
    public static final BigDecimal BIG_DECIMAL_ZERO_NET_ZERO = new BigDecimal("0.00");
    public static final BigDecimal BIG_DECIMAL_TWO_HUNDRED_NET_ZERO = new BigDecimal("200.000");
    public static final BigDecimal BIG_DECIMAL_ELEVEN = new BigDecimal(11);
    public static final BigDecimal BIG_DECIMAL_FIVE_THOUSAND = new BigDecimal(5000);
    public static final BigDecimal BIG_DECIMAL_HUNDRED_THOUSAND = new BigDecimal(100000);
    public static final Integer NEGATIVE_INTEGER_ONE = -1;

    public static final String DOUBLE_ZERO = "0.00";

    public static final Integer INTEGER_ZERO = 0;
    public static final Integer INTEGER_ONE = 1;
    public static final Integer INTEGER_TWO = 2;
    public static final Integer INTEGER_THREE = 3;
    public static final Integer INTEGER_FOUR = 4;
    public static final Integer INTEGER_FIVE = 5;
    public static final Integer INTEGER_SIX = 6;
    public static final Integer INTEGER_SEVEN = 7;
    public static final Integer INTEGER_EIGHT = 8;
    public static final Integer INTEGER_NINE = 9;
    public static final Integer INT_TEN = 10;
    public static final Integer INT_ELEVEN = 11;
    public static final Integer INT_TWELVE = 12;
    public static final Integer INT_THIRTEEN = 13;
    public static final Integer INT_FOURTEEN = 14;
    public static final Integer INT_FIFTEEN = 15;
    public static final Integer INT_SIXTEEN = 16;
    public static final Integer INT_SEVENTEEN = 17;
    public static final Integer INT_EIGHTEEN = 18;
    public static final Integer INT_NINETEEN = 19;
    public static final Integer INT_TWENTY = 20;
    public static final Integer INT_TWENTY_ONE = 21;
    public static final Integer INT_TWENTY_TWO = 22;
    public static final Integer INT_TWENTY_THREE = 23;
    public static final Integer INT_TWENTY_FOUR = 24;
    public static final Integer INT_TWENTY_FIVE = 25;
    public static final Integer INT_TWENTY_SIX = 26;
    public static final Integer INT_TWENTY_SEVEN = 27;
    public static final Integer INT_TWENTY_EIGHT = 28;
    public static final Integer INT_TWENTY_NIGHT = 29;
    public static final Integer INT_THIRTY = 30;
    public static final Integer INT_THIRTY_ONE = 31;
    public static final Integer INT_THIRTY_TWO = 32;
    public static final Integer INT_THIRTY_THREE = 33;
    public static final Integer INT_THIRTY_FOUR = 34;
    public static final Integer INT_THIRTY_FIVE = 35;
    public static final Integer INT_THIRTY_SIX = 36;
    public static final Integer INT_THIRTY_SEVEN = 37;
    public static final Integer INT_THIRTY_EIGHT = 38;
    public static final Integer INT_THIRTY_NIGHT = 39;
    public static final Integer INT_FORTY = 40;
    public static final Integer INT_FORTY_ONE = 41;
    public static final Integer INT_FORTY_TWO = 42;
    public static final Integer INT_FORTY_THREE = 43;
    public static final Integer INT_FORTY_FOUR = 44;
    public static final Integer INT_FORTY_FIVE = 45;
    public static final Integer INT_FORTY_SIX = 46;
    public static final Integer INT_FORTY_SEVEN = 47;
    public static final Integer INT_FORTY_EIGHT = 48;
    public static final Integer INT_FORTY_NIGHT = 49;
    public static final Integer INT_FIFTY = 50;
    public static final Integer INT_FIFTY_ONE = 51;
    public static final Integer INT_FIFTY_TWO = 52;
    public static final Integer INTEGER_THREE_HUNDRED = 300;
    public static final Integer INTEGER_TEN_THOUSAND = 10000;

    public static final Long LONG_ZERO = 0L;
    public static final Long LONG_ONE = 1L;
    public static final boolean BOOLTRUE = true;
    public static final boolean BOOLFALES = false;

    public static final String SYSTEM_ADMIN_OPERATOR = "systemAdmin";

    public static final String TRUE = "true";
    public static final String FALSE = "false";

    public static final Long USERINFO_REDIS_EXPDATE = 86400L;

    public static final String CARRIER_ORDER_BILL_DIR_FORMAT = "yyyyMMdd";
    public static final String DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES = "yyyy-MM-dd HH:mm";
    public static final String DATE_TO_STRING_YM_PATTERN = "yyyy-MM";

    public static final String DATE_TO_STRING_HM_PATTERN = "HH:mm";

    public static final Integer ADDRESS_NUMBER = 20;
    public static final BigDecimal CUBIC_MILLIMETER_TO_CUBIC_METER = new BigDecimal(1000000000);

    public static final String OTHER = "其他";
    public static final String PACKAGE = "件";
    public static final String SQUARE = "方";
    public static final String WEIGHT = "吨";
    public static final String YUAN = "元";
    public static final String PACKAGE_UNIT = "元/件";
    public static final String VOLUME_UNIT = "元/方";
    public static final String WEIGHT_UNIT = "元/吨";
    public static final String TOKEN = "token";

    public static final String TMS_API_PICTURE_VERIFICATION_CODE_PREFIX = "TMS.API.PICTURE.VERIFICATION.CODE.PREFIX";

    public static final String PROMISE_PERIOD = "年驾驶员安全承诺书";
    public static final String VEHICLE_CHECK_PERIOD_FORMAT = "yyyy年M月安全检查";

    public static final String PLUS="+";
    public static final String MINUS="-";

    public static final String PIC_RID = "rId6";//非回收单

    public static final String PIC_RID_R = "rId6";//回收单

    public static final String TABLE_KEYWORD_1 = "*货物信息1";//二部签收单&提货单 表格关键字
    public static final String TABLE_KEYWORD_2 = "*货物信息2";//二部签收单&提货单 表格关键字

    public static final String DATE_TODAY = "今日";
    public static final String DATE_TO_STRING_MD_PATTERN_TEXT = "MM月dd日";
    public static final String NAME_MOBILE_FORMAT = "%s_%s";
    public static final String DAY_TEXT = "天";
    public static final String TAXI = "出租车";

    public static final String TRAFFIC = "交通";

    public static final String TRAIN_TICKETS = "火车票";

    public static final String PASSING_BY = "过路过桥费";
    public static final int CODE_NOT_EXIST =-3000;

    public static final String KEY_TOKEN_REDIS = "TMS_APPAPI_VISITOR:";


    public static final String SHORT_URL_HASH_COLLISION_SUFFIX = "#HASH_COLLISTION";

    // 链接参数拼接
    public static final BiFunction<String, Map<String, Object>, String> linkParamsJoinFunction = (l, m) -> {
        StringBuilder builder = new StringBuilder(l);
        if (!CollectionUtils.isEmpty(m)) {
            builder.append("?");
            builder.append(Joiner.on("&").useForNull("").withKeyValueSeparator("=").join(m));
        }
        return builder.toString();
    };
}
