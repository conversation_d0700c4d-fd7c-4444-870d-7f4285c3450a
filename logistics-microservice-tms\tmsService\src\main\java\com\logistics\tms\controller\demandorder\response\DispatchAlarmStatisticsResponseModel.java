package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/10/25 9:55
 */
@Data
public class DispatchAlarmStatisticsResponseModel {
    @ApiModelProperty("发货省份")
    private String loadProvinceName;
    @ApiModelProperty("发货市")
    private String loadCityName;
    @ApiModelProperty("负责人")
    private String loadRegionContactName;
    @ApiModelProperty("数量（需求单货物数量）")
    private BigDecimal goodsAmount;
    @ApiModelProperty("下单数（需求单数）")
    private Integer demandOrderCount;

    private Long loadCityId;//提货市id，用于查询最新单子存在的负责人
}
