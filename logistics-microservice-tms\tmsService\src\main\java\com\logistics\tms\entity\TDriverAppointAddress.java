package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/08/20
*/
@Data
public class TDriverAppointAddress extends BaseEntity {
    /**
    * 司机预约表id
    */
    @ApiModelProperty("司机预约表id")
    private Long driverAppointId;

    /**
    * 提货地址code
    */
    @ApiModelProperty("提货地址code")
    private String loadAddressCode;

    /**
    * 省份ID
    */
    @ApiModelProperty("省份ID")
    private Long loadProvinceId;

    /**
    * 省份名字
    */
    @ApiModelProperty("省份名字")
    private String loadProvinceName;

    /**
    * 城市ID
    */
    @ApiModelProperty("城市ID")
    private Long loadCityId;

    /**
    * 城市名字
    */
    @ApiModelProperty("城市名字")
    private String loadCityName;

    /**
    * 县区id
    */
    @ApiModelProperty("县区id")
    private Long loadAreaId;

    /**
    * 县区名字
    */
    @ApiModelProperty("县区名字")
    private String loadAreaName;

    /**
    * 详细地址
    */
    @ApiModelProperty("详细地址")
    private String loadDetailAddress;

    /**
    * 发货仓库
    */
    @ApiModelProperty("发货仓库")
    private String loadWarehouse;

    /**
    * 装货公司
    */
    @ApiModelProperty("装货公司")
    private String loadCompany;

    /**
    * 发货人姓名
    */
    @ApiModelProperty("发货人姓名")
    private String consignorName;

    /**
    * 发货人手机号(加密)
    */
    @ApiModelProperty("发货人手机号(加密)")
    private String consignorMobile;

    /**
    * 期望提货时间
    */
    @ApiModelProperty("期望提货时间")
    private Date expectedLoadTime;

    /**
    * 卸货地址code
    */
    @ApiModelProperty("卸货地址code")
    private String unloadAddressCode;

    /**
    * 省份ID
    */
    @ApiModelProperty("省份ID")
    private Long unloadProvinceId;

    /**
    * 省份名字
    */
    @ApiModelProperty("省份名字")
    private String unloadProvinceName;

    /**
    * 城市ID
    */
    @ApiModelProperty("城市ID")
    private Long unloadCityId;

    /**
    * 城市名字
    */
    @ApiModelProperty("城市名字")
    private String unloadCityName;

    /**
    * 县区id
    */
    @ApiModelProperty("县区id")
    private Long unloadAreaId;

    /**
    * 县区名字
    */
    @ApiModelProperty("县区名字")
    private String unloadAreaName;

    /**
    * 详细地址
    */
    @ApiModelProperty("详细地址")
    private String unloadDetailAddress;

    /**
    * 收货仓库
    */
    @ApiModelProperty("收货仓库")
    private String unloadWarehouse;

    /**
    * 卸货公司
    */
    @ApiModelProperty("卸货公司")
    private String unloadCompany;

    /**
    * 收货人姓名
    */
    @ApiModelProperty("收货人姓名")
    private String receiverName;

    /**
    * 收货人手机号(加密)
    */
    @ApiModelProperty("收货人手机号(加密)")
    private String receiverMobile;

    /**
    * 期望卸货时间
    */
    @ApiModelProperty("期望卸货时间")
    private Date expectedUnloadTime;
}