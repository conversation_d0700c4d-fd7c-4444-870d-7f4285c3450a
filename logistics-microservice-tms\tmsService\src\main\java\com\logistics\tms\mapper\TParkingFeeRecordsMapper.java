package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TParkingFeeRecords;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface TParkingFeeRecordsMapper extends BaseMapper<TParkingFeeRecords> {
    List<TParkingFeeRecords> getByParkingFeeId(@Param("parkingFeeId") Long parkingFeeId);
}