package com.logistics.appapi.base.enums;

import com.yelo.tray.core.base.enums.BaseExceptionEnum;
import lombok.Getter;

/**
 * @author: wjf
 * @date: 2019/10/22 16:10
 */
@Getter
public enum AppApiExceptionEnum implements BaseExceptionEnum {
    USER_VERIFICATION_CANT_BE_NULL(40001,"验证码不能为空"),
    USER_PASSWORD_CANT_BE_NULL(40002,"密码不能为空"),
    TICKET_COUNT_MAX(40003,"单据数量不能超过6张"),
    CARRIER_ORDER_GOODS_CANNOT_BE_EMPTY(40004,"货物不能为空"),
    CARRIER_ORDER_GOODS_UNIT_CAN_NOT_BE_EMPTY(40005,"货物单位不能为空"),
    START_TIME_END_TIME_ERROR(40006,"起始日期不能大于截止日期"),
    BASE_DATA_SERVICE_ERROR(40007, "基础数据服务请求失败"),
    COMMON_IO_EXCEPTION(40008, "IO异常"),
    AI_UPLOAD_SIZE_TO_LONG(40009, "文件大于4M"),
    USER_PIC_VERIFICATION_ERROR(40010, "图片验证码错误"),
    NOT_ALLOW_SUBMIT_SENSITIVE_WORD(40011, "请勿提交敏感词汇、恶意信息等"),
    VEHICLE_NO_ERROR(40012, "请输入正确的车牌号"),
    DRIVER_RECONCILIATION_CONFIRM_IMAGE_URL_EMPTY(40013, "请拍照确认"),
    DRIVER_RECONCILIATION_CONFIRM_REMARK_EMPTY(40014, "请维护对账问题，1-50字"),
    DRIVER_RECONCILIATION_IF_CONFIRM_EMPTY(40015, "只有待确认账单，才能提交信息"),
    OPEN_ID_EMPTY(40016, "授权失败"),
    COST_TYPE_ERROR(40017, "请选择正确的费用申请类型"),
    PARAMS_ERROR(40018, "请求参数错误"),
    SKU_CODE_NOT_UNIQUE(40019, "货物不可重复"),
    LOCALE_TICKET_COUNT_MAX(40020, "现场图片最多2张"),
    TICKET_COUNT_EMPTY(40021, "请上传单据"),
    OTHER_FEE_NOT_DUPLICATE(40022, "临时费用申请不可重复"),
    LOAD_TICKET_COUNT_MAX(40023, "出库单数量不能超过6张"),
    UNLOAD_TICKET_COUNT_MAX(40024, "请上传签收单，且不能超过6张"),
    IMAGE_VERIFICATION_AUTH_DEFEATED(40025, "图片验证码验证失败,请重新验证"),
    DECRYPTION_FAILURE(40026, "解密失败"),
    MOBILE_PHONE_ERROR(40027, "请输入正确的手机号"),
    PASSWORD_ERROR(40028, "请输入正确的密码"),
    OLE_PASSWORD_NOT_EQUALS_NEW_PASSWORD(40029, "新旧密码一致，请重新输入"),
    NO_REAL_NAME(40030, "请先完成实名认证"),
    REAL_NAME_PARAMS_ERROR(40031, "实名认证参数错误"),
    REACH_TRY_PIC(40032, "请上传带料共享托盘图片，且不超过3张"),
    TRAYS_AMOUNT_MAX(40033, "请维护正确的共享托盘数量：1<=数量<=10000"),
    TELEPHONE_ERROR(40034, "请输入正确的电话"),
    RE_REPORT_WORK_NOT_EMPTY(40035, "工单ID不允许为空"),
    LOCATION_IS_NLL(40036, "请选择定位地址"),

    MISSING_REQUIRED_PIC(40037, "现场图片、支付图片必须存在"),
    MISSING_INVOICE_PIC_CONTAINS(40038, "发票图片必须存在"),

    INVOICE_INFO_DELETION(40039, "发票信息缺失"),

    ERROR_FEE_LIMIT(40040, "请维护正确费用金额，0<=费用<=100000元"),

    INVOICE_CODE_PARAMS_ERROR(40040, "发票代码为1-12位数字以及大小写字母"),

    OCR_TYPE_CANNOT_BLANK(40041, "票据识别类型不能为空"),

    ERROR_OCR_INVOICE(40042, "发票有误，请重新上传"),
    INVOICE_REPETITION(40043, "发票重复，请重新上传"),
    FAIL_GET_TOKEN(40044, "获取权限失败，请重新扫码"),
    SIGN_UP_BILL_3_ONLY_FOR_YR(40045, "该类型不支持查看预览"),
    ;

    @Override
    public int getCode() {
        return key;
    }

    @Override
    public String getMsg() {
        return value;
    }

    private Integer key;
    private String value;

    AppApiExceptionEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

}
