<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TFreightAddressRuleMarkupMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TFreightAddressRuleMarkup" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="freight_address_rule_id" property="freightAddressRuleId" jdbcType="BIGINT" />
    <result column="mark_index" property="markIndex" jdbcType="INTEGER" />
    <result column="load_amount" property="loadAmount" jdbcType="INTEGER" />
    <result column="unload_amount" property="unloadAmount" jdbcType="INTEGER" />
    <result column="markup_freight_fee" property="markupFreightFee" jdbcType="DECIMAL" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, freight_address_rule_id, mark_index, load_amount, unload_amount, markup_freight_fee,
    created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_freight_address_rule_markup
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_freight_address_rule_markup
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TFreightAddressRuleMarkup" >
    insert into t_freight_address_rule_markup (id, freight_address_rule_id, mark_index,
      load_amount, unload_amount, markup_freight_fee, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{freightAddressRuleId,jdbcType=BIGINT}, #{markIndex,jdbcType=INTEGER},
      #{loadAmount,jdbcType=INTEGER}, #{unloadAmount,jdbcType=INTEGER}, #{markupFreightFee,jdbcType=DECIMAL}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TFreightAddressRuleMarkup" keyProperty="id" useGeneratedKeys="true">
    insert into t_freight_address_rule_markup
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="freightAddressRuleId != null" >
        freight_address_rule_id,
      </if>
      <if test="markIndex != null" >
        mark_index,
      </if>
      <if test="loadAmount != null" >
        load_amount,
      </if>
      <if test="unloadAmount != null" >
        unload_amount,
      </if>
      <if test="markupFreightFee != null" >
        markup_freight_fee,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="freightAddressRuleId != null" >
        #{freightAddressRuleId,jdbcType=BIGINT},
      </if>
      <if test="markIndex != null" >
        #{markIndex,jdbcType=INTEGER},
      </if>
      <if test="loadAmount != null" >
        #{loadAmount,jdbcType=INTEGER},
      </if>
      <if test="unloadAmount != null" >
        #{unloadAmount,jdbcType=INTEGER},
      </if>
      <if test="markupFreightFee != null" >
        #{markupFreightFee,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TFreightAddressRuleMarkup" >
    update t_freight_address_rule_markup
    <set >
      <if test="freightAddressRuleId != null" >
        freight_address_rule_id = #{freightAddressRuleId,jdbcType=BIGINT},
      </if>
      <if test="markIndex != null" >
        mark_index = #{markIndex,jdbcType=INTEGER},
      </if>
      <if test="loadAmount != null" >
        load_amount = #{loadAmount,jdbcType=INTEGER},
      </if>
      <if test="unloadAmount != null" >
        unload_amount = #{unloadAmount,jdbcType=INTEGER},
      </if>
      <if test="markupFreightFee != null" >
        markup_freight_fee = #{markupFreightFee,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TFreightAddressRuleMarkup" >
    update t_freight_address_rule_markup
    set freight_address_rule_id = #{freightAddressRuleId,jdbcType=BIGINT},
      mark_index = #{markIndex,jdbcType=INTEGER},
      load_amount = #{loadAmount,jdbcType=INTEGER},
      unload_amount = #{unloadAmount,jdbcType=INTEGER},
      markup_freight_fee = #{markupFreightFee,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>