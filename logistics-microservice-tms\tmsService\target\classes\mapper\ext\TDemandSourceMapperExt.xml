<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandSourceMapper">
    <select id="searchList" resultType="com.logistics.tms.controller.website.demand.response.DemandSourceListResponseModel">
        select id             as demandSourceId,
               goods_name     as goodsName,
               goods_amount   as goodsAmount,
               goods_price    as goodsPrice,
               load_address   as loadAddress,
               unload_address as unloadAddress,
               contact_name   as contactName,
               contact_mobile as contactMobile
        from t_demand_source
        where valid = 1
        order by created_time desc
    </select>
</mapper>