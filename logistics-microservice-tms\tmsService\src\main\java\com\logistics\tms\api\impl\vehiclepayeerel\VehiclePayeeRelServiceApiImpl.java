package com.logistics.tms.api.impl.vehiclepayeerel;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.vehiclepayeerel.VehiclePayeeRelServiceApi;
import com.logistics.tms.api.feign.vehiclepayeerel.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.vehiclepayeerel.VehiclePayeeRelBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/7/11 14:26
 */
@RestController
public class VehiclePayeeRelServiceApiImpl implements VehiclePayeeRelServiceApi {

    @Autowired
    private VehiclePayeeRelBiz vehiclePayeeRelBiz;

    /**
     * 车辆收款账户关联关系列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<SearchVehiclePayeeRelListResponseModel>> searchVehiclePayeeRelList(@RequestBody SearchVehiclePayeeRelListRequestModel requestModel) {
        List<SearchVehiclePayeeRelListResponseModel> list = vehiclePayeeRelBiz.searchVehiclePayeeRelList(requestModel.enablePaging());
        return Result.success(new PageInfo<>(list));
    }

    /**
     * 车辆收款账户关联关系详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<VehiclePayeeRelDetailResponseModel> getVehiclePayeeRelDetail(@RequestBody VehiclePayeeRelIdRequestModel requestModel) {
        return Result.success(vehiclePayeeRelBiz.getVehiclePayeeRelDetail(requestModel));
    }

    /**
     * 新增/修改车辆收款账户关联关系
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> addOrModifyVehiclePayeeRel(@RequestBody AddOrModifyVehiclePayeeRelRequestModel requestModel) {
        vehiclePayeeRelBiz.addOrModifyVehiclePayeeRel(requestModel);
        return Result.success(true);
    }

    /**
     * 删除车辆收款账户关联关系
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> delVehiclePayeeRel(@RequestBody VehiclePayeeRelIdsRequestModel requestModel) {
        vehiclePayeeRelBiz.delVehiclePayeeRel(requestModel);
        return Result.success(true);
    }

    /**
     * 导出车辆收款账户关联关系列表
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchVehiclePayeeRelListResponseModel>> exportVehiclePayeeRel(@RequestBody SearchVehiclePayeeRelListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        return Result.success(vehiclePayeeRelBiz.searchVehiclePayeeRelList(requestModel));
    }

    /**
     * 导入车辆收款账户关联关系
     * @param requestModel
     * @return
     */
    @Override
    public Result<ImportVehiclePayeeRelResponseModel> importVehiclePayeeRel(@RequestBody ImportVehiclePayeeRelRequestModel requestModel) {
        return Result.success(vehiclePayeeRelBiz.importVehiclePayeeRel(requestModel));
    }
}
