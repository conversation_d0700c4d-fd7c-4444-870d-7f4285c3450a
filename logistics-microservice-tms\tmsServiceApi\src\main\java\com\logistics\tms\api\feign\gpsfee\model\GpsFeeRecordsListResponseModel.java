package com.logistics.tms.api.feign.gpsfee.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/10/9 9:53
 */
@Data
public class GpsFeeRecordsListResponseModel {
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("服务商")
    private String gpsServiceProvider;
    @ApiModelProperty("服务费")
    private BigDecimal serviceFee;
    @ApiModelProperty("合作周期（月）")
    private Integer cooperationPeriod;
    @ApiModelProperty("起始日期")
    private Date startDate;
    @ApiModelProperty("截止时间")
    private Date endDate;
    @ApiModelProperty("终止时间")
    private Date finishDate;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("最后修改人")
    private String lastModifiedBy;
    @ApiModelProperty("最后修改时间")
    private Date lastModifiedTime;
}
