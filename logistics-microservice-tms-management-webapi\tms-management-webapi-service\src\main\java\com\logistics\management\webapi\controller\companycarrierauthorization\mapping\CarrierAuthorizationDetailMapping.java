package com.logistics.management.webapi.controller.companycarrierauthorization.mapping;

import com.logistics.management.webapi.base.enums.CompanyAuthorizationAuditStatusEnum;
import com.logistics.management.webapi.base.enums.CompanyAuthorizationIsArchivedEnum;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.client.companycarrierauthorization.response.CarrierAuthorizationDetailResponseModel;
import com.logistics.management.webapi.controller.companycarrierauthorization.response.CarrierAuthorizationDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import lombok.AllArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@AllArgsConstructor
public class CarrierAuthorizationDetailMapping extends MapperMapping<CarrierAuthorizationDetailResponseModel, CarrierAuthorizationDetailResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;

    @Override
    public void configure() {
        CarrierAuthorizationDetailResponseModel source = getSource();
        CarrierAuthorizationDetailResponseDto destination = getDestination();

        // 车主公司类型展示文本
        destination.setCompanyCarrierTypeLabel(CompanyTypeEnum.getEnum(source.getCompanyCarrierType()).getValue());
        // 是否归档展示文本
        destination.setIsArchivedLabel(CompanyAuthorizationIsArchivedEnum.getEnumByKey(source.getIsArchived()).getValue());
        // 授权状态展示文本
        destination.setAuthorizationStatus(CompanyAuthorizationAuditStatusEnum.getEnumByKey(source.getAuthorizationStatus()).getValue());
        // 授权书
        destination.setAuthorizationImageList(picConversion());
    }

    // 图片访问路径转换
    private List<CarrierAuthorizationDetailResponseDto.AuthorizationImage> picConversion() {
        if (CollectionUtils.isEmpty(imageMap)) {
            return Collections.emptyList();
        }
        return imageMap.entrySet()
                .stream()
                .filter(f -> Objects.nonNull(f.getValue()))
                .map(s -> {
                    var authorizationImage = new CarrierAuthorizationDetailResponseDto.AuthorizationImage();
                    authorizationImage.setSrc(imagePrefix.concat(s.getValue()));
                    authorizationImage.setRelativePath(s.getKey());
                    return authorizationImage;
                })
                .collect(Collectors.toList());
    }
}
