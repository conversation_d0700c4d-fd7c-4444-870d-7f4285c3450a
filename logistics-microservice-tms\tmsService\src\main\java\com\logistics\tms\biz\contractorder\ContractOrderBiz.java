package com.logistics.tms.biz.contractorder;

import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tray.core.exception.BizException;
import com.logistics.tms.api.feign.contractorder.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.constant.ConfigKeyConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.LocalStringUtil;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TContract;
import com.logistics.tms.entity.TContractFile;
import com.logistics.tms.entity.TOperateLogs;
import com.logistics.tms.mapper.TContractFileMapper;
import com.logistics.tms.mapper.TContractMapper;
import com.logistics.tms.mapper.TOperateLogsMapper;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Slf4j
@Service
public class ContractOrderBiz {

    @Autowired
    private TContractMapper tContractMapper;
    @Autowired
    private TContractFileMapper tContractFileMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TOperateLogsMapper tOperateLogsMapper;
    @Autowired
    private ConfigKeyConstant configKeyConstant;

    /**
     * 查询合同列表
     * @param requestModel
     * @return
     */
    public List<ContractOrderSearchResponseModel> searchContractOrderList(ContractOrderSearchRequestModel requestModel) {
        requestModel.enablePaging();
        return tContractMapper.searchContractOrderList(requestModel);
    }

    /**
     * 查询合同详情
     * @param requestModel
     * @return
     */
    public ContractOrderDetailResponseModel geDetail(ContractOrderDetailRequestModel requestModel){
        return tContractMapper.getContractDetail(requestModel.getContractId());
    }

    /**
     * 新增或修改合同信息
     * @param requestModel
     */
    @Transactional
    public void saveContract(AddOrModifyContractOrderRequestModel requestModel){
        String contractHeader = requestModel.getContractHeader();
        if (StringUtils.isBlank(requestModel.getContractHeader())) {
            contractHeader = commonBiz.getQiyaCompanyName();
        }
        Date startTime = requestModel.getContractStartTime();
        Date endTime = requestModel.getContractEndTime();
        Date now = new Date();
        Integer contractStatus = getContractStatus(now,startTime,endTime,CommonConstant.ONE);
        TContract contractInfo = new TContract();
        contractInfo.setContractStatus(contractStatus);
        contractInfo.setContractNoExternal(requestModel.getContractNoExternal());
        contractInfo.setContractType(requestModel.getContractType());
        contractInfo.setContractNature(requestModel.getContractNature());
        contractInfo.setCustomerCompanyName(requestModel.getCustomerCompanyName());
        if(ContractOrderNatureEnum.LEASE_CONTRACT.getKey().equals(requestModel.getContractNature())){
            contractInfo.setContractObjectId(0L);
        }else {
            contractInfo.setContractObjectId(requestModel.getContractObjectId());
        }
        contractInfo.setContractStartTime(startTime);
        contractInfo.setContractEndTime(endTime);
        contractInfo.setContractHeader(contractHeader);
        contractInfo.setRemark(requestModel.getRemark());
        Long contractId = requestModel.getContractId();
        String contractNoInternal;
        OperateLogsOperateTypeEnum logsEnum;
        OperateLogsOperateTypeEnum terminateLogsEnum = null;
        String terminateRemark = "";
        if (ContractOrderStatusEnum.TERMINATION.getKey().equals(contractStatus)){
            contractInfo.setEndingCancelBy(BaseContextHandler.getUserName());
            contractInfo.setEndingCancelDatetime(now);
            terminateLogsEnum = OperateLogsOperateTypeEnum.TERMINATE_CONTRACT;
        }
        BusinessCodeTypeEnum businessCodeTypeEnum = null;
        if(ContractOrderNatureEnum.GOODS_CONTRACT.getKey().equals(requestModel.getContractNature()) && ContractOrderTypeEnum.FRAME_CONTRACT.getKey().equals(requestModel.getContractType())){
            businessCodeTypeEnum = BusinessCodeTypeEnum.CONTRACT_ORDER_HK;
        }else if(ContractOrderNatureEnum.GOODS_CONTRACT.getKey().equals(requestModel.getContractNature()) && ContractOrderTypeEnum.SINGLE_CONTRACT.getKey().equals(requestModel.getContractType())){
            businessCodeTypeEnum = BusinessCodeTypeEnum.CONTRACT_ORDER_HD;
        }else if(ContractOrderNatureEnum.VEHICLE_CONTRACT.getKey().equals(requestModel.getContractNature()) && ContractOrderTypeEnum.FRAME_CONTRACT.getKey().equals(requestModel.getContractType())){
            businessCodeTypeEnum = BusinessCodeTypeEnum.CONTRACT_ORDER_CK;
        }else if(ContractOrderNatureEnum.VEHICLE_CONTRACT.getKey().equals(requestModel.getContractNature()) && ContractOrderTypeEnum.SINGLE_CONTRACT.getKey().equals(requestModel.getContractType())){
            businessCodeTypeEnum = BusinessCodeTypeEnum.CONTRACT_ORDER_CD;
        }else if(ContractOrderNatureEnum.LEASE_CONTRACT.getKey().equals(requestModel.getContractNature()) && ContractOrderTypeEnum.FRAME_CONTRACT.getKey().equals(requestModel.getContractType())){
            businessCodeTypeEnum=BusinessCodeTypeEnum.CONTRACT_ORDER_ZK;
        }else if(ContractOrderNatureEnum.LEASE_CONTRACT.getKey().equals(requestModel.getContractNature()) && ContractOrderTypeEnum.SINGLE_CONTRACT.getKey().equals(requestModel.getContractType())){
            businessCodeTypeEnum=BusinessCodeTypeEnum.CONTRACT_ORDER_ZD;
        }
        if(contractId != null && !contractId.equals(CommonConstant.LONG_ZERO)) {//修改合同信息
            TContract tContract = tContractMapper.selectByPrimaryKey(contractId);
            if (tContract == null){
                throw new BizException(CarrierDataExceptionEnum.CONTRACT_NOT_EXIST);
            }
            if(ContractOrderStatusEnum.TERMINATION.getKey().equals(tContract.getContractStatus()) || ContractOrderStatusEnum.CANCEL.getKey().equals(tContract.getContractStatus())){
                throw new BizException(CarrierDataExceptionEnum.CONTRACT_NOT_MODIFY);
            }
            if (ContractOrderStatusEnum.TERMINATION.getKey().equals(contractStatus)){
                terminateRemark = CommonConstant.MODIFY_CONTRACT_TERMINATE;
                contractInfo.setEndingCancelRemark(terminateRemark);
            }
            //如果修改了合同类型或性质，则内部合同号重新生成
            if (!tContract.getContractType().equals(requestModel.getContractType()) || !tContract.getContractNature().equals(requestModel.getContractNature())){
                contractNoInternal = commonBiz.getBusinessTypeCode(businessCodeTypeEnum,"",BaseContextHandler.getUserName());
                contractInfo.setContractNoInternal(contractNoInternal);
            }else{
                contractNoInternal = tContract.getContractNoInternal();
            }
            contractInfo.setId(contractId);
            commonBiz.setBaseEntityModify(contractInfo,BaseContextHandler.getUserName());
            tContractMapper.updateByPrimaryKeySelective(contractInfo);

            tContractFileMapper.updateContractFileByContractId(contractId, LocalStringUtil.listTostring(requestModel.getContractFiles(), ','));
            List<String> addImageList = new ArrayList<>();
            for (String filePath : requestModel.getContractFiles()) {
                if(!filePath.contains(configKeyConstant.contractImageCatalog)){
                    addImageList.add(filePath);
                }
            }
            requestModel.setContractFiles(addImageList);
            logsEnum = OperateLogsOperateTypeEnum.MODIFY_CONTRACT;
        }else{//新增合同信息
            if (ContractOrderStatusEnum.TERMINATION.getKey().equals(contractStatus)){
                terminateRemark = CommonConstant.ADD_CONTRACT_TERMINATE;
                contractInfo.setEndingCancelRemark(terminateRemark);
            }
            contractNoInternal = commonBiz.getBusinessTypeCode(businessCodeTypeEnum,"",BaseContextHandler.getUserName());
            contractInfo.setContractNoInternal(contractNoInternal);
            commonBiz.setBaseEntityAdd(contractInfo,BaseContextHandler.getUserName());
            tContractMapper.insertSelective(contractInfo);
            contractId = contractInfo.getId();
            logsEnum = OperateLogsOperateTypeEnum.ADD_CONTRACT;
        }

        if(ListUtils.isNotEmpty(requestModel.getContractFiles())){
            List<TContractFile> addContractFileList = new ArrayList<>();
            TContractFile addContractFile;
            for (String filePath : requestModel.getContractFiles()) {
                addContractFile = new TContractFile();
                addContractFile.setContractId(contractId);
                addContractFile.setContractFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.COMPANY_CONTRACT_ORDER_FILE.getKey(),contractNoInternal,filePath,null));
                commonBiz.setBaseEntityAdd(addContractFile,BaseContextHandler.getUserName());
                addContractFileList.add(addContractFile);
            }
            tContractFileMapper.batchInsert(addContractFileList);
        }

        List<TOperateLogs> logsList = new ArrayList<>();
        TOperateLogs operateLogs = addLog(contractId,logsEnum,requestModel.getRemark(),BaseContextHandler.getUserName());
        logsList.add(operateLogs);
        if (ContractOrderStatusEnum.TERMINATION.getKey().equals(contractStatus)){
            operateLogs = addLog(contractId,terminateLogsEnum,terminateRemark,BaseContextHandler.getUserName());
            logsList.add(operateLogs);
        }
        tOperateLogsMapper.batchInsert(logsList);
    }
    //获取合同状态
    public static Integer getContractStatus(Date now, Date beginTime, Date endTime, String source) {
        SimpleDateFormat nowSdf = new SimpleDateFormat(DateUtils.DATE_TO_STRING_SHORT_PATTERN);
        String nowStr = nowSdf.format(now);
        Date nowTime = null;
        try {
            nowTime = nowSdf.parse(nowStr);
        } catch (ParseException e) {
            log.info(e.getMessage(),e);
        }
        Integer contractStatus = null;
        if (source != null){
            contractStatus = ContractOrderStatusEnum.WAIT_EXECUTE.getKey();
        }
        if(nowTime!=null){
            if (nowTime.getTime() >= beginTime.getTime() && nowTime.getTime() <= endTime.getTime()) {
                contractStatus = ContractOrderStatusEnum.EXECUTING.getKey();
            } else if (nowTime.getTime() > endTime.getTime()){
                contractStatus = ContractOrderStatusEnum.TERMINATION.getKey();
            }
        }

        return contractStatus;
    }

    /**
     * 终止或作废合同
     * @param requestModel
     */
    @Transactional
    public void terminateOrCancelContract(TerminateOrCancelContractRequestModel requestModel) {
        TContract tContract = tContractMapper.selectByPrimaryKey(requestModel.getContractId());
        if (tContract == null) {
            throw new BizException(CarrierDataExceptionEnum.CONTRACT_NOT_EXIST);
        }
        TContract contract = new TContract();
        contract.setId(requestModel.getContractId());
        contract.setEndingCancelDatetime(new Date());
        contract.setEndingCancelBy(BaseContextHandler.getUserName());
        contract.setEndingCancelRemark(requestModel.getRemark());
        commonBiz.setBaseEntityModify(contract,BaseContextHandler.getUserName());
        Integer contractStatus;
        OperateLogsOperateTypeEnum logsEnum;
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getType())) {//终止
            if (!tContract.getContractStatus().equals(ContractOrderStatusEnum.EXECUTING.getKey())) {
                throw new BizException(CarrierDataExceptionEnum.NOT_EXECUTING_NOT_ENDING);
            }
            contractStatus = ContractOrderStatusEnum.TERMINATION.getKey();
            logsEnum = OperateLogsOperateTypeEnum.TERMINATE_CONTRACT;
        } else {//作废
            if (!tContract.getContractStatus().equals(ContractOrderStatusEnum.WAIT_EXECUTE.getKey())) {
                throw new BizException(CarrierDataExceptionEnum.NOT_WAIT_EXECUTE_NOT_CANCEL);
            }
            contractStatus = ContractOrderStatusEnum.CANCEL.getKey();
            logsEnum = OperateLogsOperateTypeEnum.CANCEL_CONTRACT;
        }
        contract.setContractStatus(contractStatus);
        tContractMapper.updateByPrimaryKeySelective(contract);
        //记录操作日志
        TOperateLogs operateLogs = addLog(requestModel.getContractId(),logsEnum,requestModel.getRemark(),BaseContextHandler.getUserName());
        tOperateLogsMapper.insertSelective(operateLogs);
    }
    //新增操作日志
    public TOperateLogs addLog(Long objectId,OperateLogsOperateTypeEnum logsEnum,String remark,String userName){
        TOperateLogs tOperateLogs = new TOperateLogs();
        tOperateLogs.setObjectType(logsEnum.getObjectType().getKey());
        tOperateLogs.setObjectId(objectId);
        tOperateLogs.setOperateType(logsEnum.getOperateType());
        tOperateLogs.setOperateContents(logsEnum.getOperateContents());
        tOperateLogs.setOperateUserName(userName);
        tOperateLogs.setOperateTime(new Date());
        tOperateLogs.setRemark(remark);
        commonBiz.setBaseEntityAdd(tOperateLogs,userName);
        return tOperateLogs;
    }

    /**
     * 定时任务：合同有效期到期后自动终止
     */
    @Transactional
    public void updateStatusFromJob(){
        List<TContract> validContractList = tContractMapper.getValidContract();
        if (ListUtils.isNotEmpty(validContractList)){
            List<TContract> terminateList = new ArrayList<>();
            TContract tContract;
            List<TOperateLogs> logsList = new ArrayList<>();
            TOperateLogs operateLogs;
            Date now = new Date();
            Integer contractStatus;
            for (TContract contract:validContractList) {
                contractStatus = getContractStatus(now,contract.getContractStartTime(),contract.getContractEndTime(),null);
                if (contractStatus != null && !contractStatus.equals(contract.getContractStatus())){
                    tContract = new TContract();
                    tContract.setId(contract.getId());
                    tContract.setContractStatus(contractStatus);
                    if (ContractOrderStatusEnum.TERMINATION.getKey().equals(contractStatus)) {
                        tContract.setEndingCancelDatetime(now);
                        tContract.setEndingCancelBy(CommonConstant.TIMING_TASK);
                        tContract.setEndingCancelRemark(CommonConstant.EXPIRE_CONTRACT_TERMINATE);
                    }
                    commonBiz.setBaseEntityModify(tContract,CommonConstant.TIMING_TASK);
                    terminateList.add(tContract);

                    if (ContractOrderStatusEnum.TERMINATION.getKey().equals(contractStatus)) {
                        operateLogs = addLog(contract.getId(), OperateLogsOperateTypeEnum.TERMINATE_CONTRACT, CommonConstant.EXPIRE_CONTRACT_TERMINATE, CommonConstant.TIMING_TASK);
                        logsList.add(operateLogs);
                    }else if (ContractOrderStatusEnum.EXECUTING.getKey().equals(contractStatus)) {
                        operateLogs = addLog(contract.getId(), OperateLogsOperateTypeEnum.MODIFY_CONTRACT, CommonConstant.EXPIRE_CONTRACT_EXECUTING, CommonConstant.TIMING_TASK);
                        logsList.add(operateLogs);
                    }
                }
            }
            if (ListUtils.isNotEmpty(terminateList)){
                tContractMapper.batchUpdate(terminateList);
            }
            if (ListUtils.isNotEmpty(logsList)){
                tOperateLogsMapper.batchInsert(logsList);
            }
        }
    }
}
