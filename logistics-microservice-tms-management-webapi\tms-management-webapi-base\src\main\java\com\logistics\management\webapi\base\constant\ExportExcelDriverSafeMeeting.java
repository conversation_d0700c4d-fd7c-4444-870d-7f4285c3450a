package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/11/4 17:42
 */
public class ExportExcelDriverSafeMeeting {
    private ExportExcelDriverSafeMeeting(){}
    private static final Map<String, String> EXCEL_DRIVER_SAFE_MEETING;

    static{
        EXCEL_DRIVER_SAFE_MEETING = new LinkedHashMap<>();
        EXCEL_DRIVER_SAFE_MEETING.put("学习状态", "statusDesc");
        EXCEL_DRIVER_SAFE_MEETING.put("学习月份","period");
        EXCEL_DRIVER_SAFE_MEETING.put("司机","staffName");
        EXCEL_DRIVER_SAFE_MEETING.put("人员机构","staffPropertyLabel");
        EXCEL_DRIVER_SAFE_MEETING.put("手机号","staffMobile" );
        EXCEL_DRIVER_SAFE_MEETING.put("学习标题","title" );
        EXCEL_DRIVER_SAFE_MEETING.put("学习时间","studyTime" );
    }

    public static Map<String, String> getExcelDriverSafeMeeting() {
        return EXCEL_DRIVER_SAFE_MEETING;
    }
}
