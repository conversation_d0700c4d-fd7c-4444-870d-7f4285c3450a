package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchListStatisticsResponseModel {

    @ApiModelProperty("全部")
    private Integer allCount = 0;
    @ApiModelProperty("待发布")
    private Integer waitPublishCount = 0;
    @ApiModelProperty("待调度")
    private Integer waitDispatchCount = 0;
    @ApiModelProperty("部分调度")
    private Integer partDispatchCount = 0;
    @ApiModelProperty("调度完成")
    private Integer completeDispatchCount = 0;
    @ApiModelProperty("待签收数量")
    private Integer waitSignedAccount= 0;
    @ApiModelProperty("已签收数量")
    private Integer signedAccount= 0;
    @ApiModelProperty("已取消")
    private Integer cancelCount = 0;
}
