package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/3/27 11:06
 */
public class ExportExcelEntrustSettlement {
    private ExportExcelEntrustSettlement() {
    }

    private static final Map<String, String> EXPORT_ENTRUST_SETTLEMENT;

    static {
        EXPORT_ENTRUST_SETTLEMENT = new LinkedHashMap<>();
        EXPORT_ENTRUST_SETTLEMENT.put("结算状态 ", "settlementStatusDesc");
        EXPORT_ENTRUST_SETTLEMENT.put("委托方", "companyEntrust");
        EXPORT_ENTRUST_SETTLEMENT.put("需求单号", "demandOrderCode");
        EXPORT_ENTRUST_SETTLEMENT.put("客户单号", "customerOrderCode");
        EXPORT_ENTRUST_SETTLEMENT.put("起点市", "loadCityName");
        EXPORT_ENTRUST_SETTLEMENT.put("起点区", "loadAreaName");
        EXPORT_ENTRUST_SETTLEMENT.put("起点详细地址", "loadDetailAddress");
        EXPORT_ENTRUST_SETTLEMENT.put("起点联系人", "consignorName");
        EXPORT_ENTRUST_SETTLEMENT.put("起点联系方式", "consignorMobile");
        EXPORT_ENTRUST_SETTLEMENT.put("卸点市", "unloadCityName");
        EXPORT_ENTRUST_SETTLEMENT.put("卸点区", "unloadAreaName");
        EXPORT_ENTRUST_SETTLEMENT.put("卸点详细地址", "unloadDetailAddress");
        EXPORT_ENTRUST_SETTLEMENT.put("卸点联系人", "receiverName");
        EXPORT_ENTRUST_SETTLEMENT.put("卸点联系方式 ", "receiverMobile");
        EXPORT_ENTRUST_SETTLEMENT.put("品名", "goodsName");
        EXPORT_ENTRUST_SETTLEMENT.put("规格", "goodsSize");
        EXPORT_ENTRUST_SETTLEMENT.put("结算数据", "settlementAmount");
        EXPORT_ENTRUST_SETTLEMENT.put("结算费用", "settlementCostTotal");
        EXPORT_ENTRUST_SETTLEMENT.put("下单时间", "publishTime");
        EXPORT_ENTRUST_SETTLEMENT.put("结算时间", "settlementTime");
    }

    public static Map<String, String> getExportEntrustSettlement() {
        return EXPORT_ENTRUST_SETTLEMENT;
    }
}
