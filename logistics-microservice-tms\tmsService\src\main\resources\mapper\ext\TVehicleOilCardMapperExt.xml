<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleOilCardMapper" >
    <select id="searchVehicleOilCardList" resultType="com.logistics.tms.api.feign.vehicleoilcard.model.SearchVehicleOilCardListResponseModel">
        select
        id as vehicleOilCardId,
        card_number as cardNumber,
        status,
        vehicle_property as vehicleProperty,
        vehicle_no as vehicleNo,
        remark,
        last_modified_time as lastModifiedTime,
        last_modified_by as lastModifiedBy
        from t_vehicle_oil_card
        where valid = 1
        <if test="params.vehicleNo != null and params.vehicleNo != ''">
            and instr(vehicle_no, #{params.vehicleNo,jdbcType=VARCHAR})
        </if>
        <if test="params.cardNumber != null and params.cardNumber != ''">
            and instr(card_number, #{params.cardNumber,jdbcType=VARCHAR})
        </if>
        <if test="params.lastModifiedTimeStart != null and params.lastModifiedTimeStart != ''">
            and last_modified_time &gt;= DATE_FORMAT(#{params.lastModifiedTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="params.lastModifiedTimeEnd != null and params.lastModifiedTimeEnd != ''">
            and last_modified_time &lt;= DATE_FORMAT(#{params.lastModifiedTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.vehicleOilCardIds != null and params.vehicleOilCardIds != ''">
            and id in(${params.vehicleOilCardIds})
        </if>
        order by last_modified_time desc, id desc
    </select>

    <select id="getByCardNoOrVehicleId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_vehicle_oil_card
        where valid = 1
        <if test="cardNumber != null and cardNumber != ''">
            and card_number = #{cardNumber, jdbcType=VARCHAR}
        </if>
        <if test="vehicleId != null and vehicleId != ''">
            and vehicle_id = #{vehicleId, jdbcType=BIGINT}
        </if>
    </select>
</mapper>