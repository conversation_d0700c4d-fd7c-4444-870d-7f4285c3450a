package com.logistics.tms.api.feign.safegroupmeeting.hystrix;

import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.safegroupmeeting.SafeGroupMeetingServiceApi;
import com.logistics.tms.api.feign.safegroupmeeting.model.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2021/4/9 09:00
 */
@Component
public class SafeGroupMeetingServiceApiHystrix implements SafeGroupMeetingServiceApi {


    @Override
    public Result<Boolean> addSafeGroupMeeting(AddSafeGroupMeetingRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SafeGroupMeetingResponseModel>> safeGroupMeetingData(SafeGroupMeetingRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SafeGroupMeetingDetailResponseModel> safeGroupMeetingDetail(SafeGroupMeetingDetailRequestModel requestModel) {
        return Result.timeout();
    }
}
