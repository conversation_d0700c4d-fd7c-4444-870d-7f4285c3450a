package com.logistics.tms.mapper;

import com.logistics.tms.entity.TReservationVisitorRecord;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* Created by Mybatis Generator on 2024/11/07
*/
@Mapper
public interface TReservationVisitorRecordMapper extends BaseMapper<TReservationVisitorRecord> {
    int deleteByPrimaryKey(Long id);

    int insert(TReservationVisitorRecord record);

    int insertSelective(TReservationVisitorRecord record);

    TReservationVisitorRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TReservationVisitorRecord record);

    int updateByPrimaryKey(TReservationVisitorRecord record);
}