package com.logistics.tms.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 需求单-批量发布(中石化) - 请求实体类
 *
 * @author: wei.wang
 * @date: 2021/12/4
 */
@Data
public class BatchPublishSinopecDemandRequestModel {

	@ApiModelProperty("调度人员姓名")
	private String dispatcherName;
	@ApiModelProperty("调度人员电话")
	private String dispatcherPhone;
	@ApiModelProperty("货主价格（单价）")
	private BigDecimal contractPrice;//固定是单价

	@ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
	private Integer isOurCompany;
	@ApiModelProperty(value = "车主ID")
	private Long companyCarrierId;
	@ApiModelProperty(value = "车主价格（单价）")
	private BigDecimal carrierPrice;

	@ApiModelProperty("需求单详情")
	private List<BatchPublishSinopecDemandDetailModel> sinopecDemands;
}
