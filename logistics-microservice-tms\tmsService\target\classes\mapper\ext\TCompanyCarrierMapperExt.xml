<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCompanyCarrierMapper">
    <select id="getCompanyCarrierList" resultType="com.logistics.tms.controller.companycarrier.response.SearchCompanyCarrierListResponseModel">
        select
        tcc.id                                                                                                                 as companyCarrierId,
        tcc.road_transport_certificate_is_amend                                                                                as roadTransportCertificateIsAmend,
        tcc.type                                                                                                               as type,
        tcc.source                                                                                                             as source,
        tcc.remark                                                                                                             as remark,
        tcc.authorization_status                                                                                               as authorizationStatus,
        tcc.real_name_authentication_status                                                                                    as realNameAuthenticationStatus,
        tcc.created_time                                                                                                       as createdTime,
        tcc.last_modified_by                                                                                                   as lastModifiedBy,
        tcc.last_modified_time                                                                                                 as lastModifiedTime,
        tcc.if_add_blacklist                                                                                                   as ifAddBlacklist,
        tcc.if_less_than_truckload                                                                                             as ifLessThanTruckload,
        tcc.shipping_freight_add_user                                                                                          as shippingFreightAddUser,
        tcc.shipping_freight_add_time                                                                                          as shippingFreightAddTime,
        tc.company_name                                                                                                        as companyCarrierName,
        tc.trading_certificate_is_amend                                                                                        as tradingCertificateIsAmend,

        tac.contact_name                                                                                                       as carrierContactName,
        AES_DECRYPT(UNHEX(tac.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactPhone
        from t_company_carrier tcc
        left join t_company tc on tc.id = tcc.company_id and tc.valid = 1
        LEFT JOIN t_carrier_contact tac ON tcc.id = tac.company_carrier_id and tcc.type = 2 AND tac.valid = 1
        where tcc.valid = 1
        <if test="params.ifAllLevel != null">
            <choose>
                <when test="params.ifAllLevel == 1">
                    and tcc.level in (1,2)
                </when>
                <otherwise>
                    and tcc.level = 2 -- 指定二级承运商
                </otherwise>
            </choose>
        </if>
        <if test="params.source != null">
            and tcc.source = #{params.source,jdbcType = INTEGER}
        </if>
        <if test="params.authorizationStatus != null">
            and tcc.authorization_status = #{params.authorizationStatus,jdbcType = INTEGER}
            and tcc.type = 1
        </if>
        <if test="params.realNameAuthenticationStatus != null">
            and tcc.real_name_authentication_status = #{params.realNameAuthenticationStatus,jdbcType = INTEGER}
            and tcc.type = 2
        </if>
        <if test="params.companyCarrierName != null and params.companyCarrierName !=''">
            and ((tcc.type = 1 and instr(tc.company_name,#{params.companyCarrierName,jdbcType=VARCHAR}))
            or (tcc.type = 2 and (instr(tac.contact_name,#{params.companyCarrierName,jdbcType=VARCHAR}) or instr(AES_DECRYPT(UNHEX(tac.contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') ,#{params.companyCarrierName,jdbcType=VARCHAR}))))
        </if>
        <if test="params.certificateSupplement!=null and params.certificateSupplement!=''">
            <if test='params.certificateSupplement == "1"'>
                and ( tcc.road_transport_certificate_is_amend = 1
                or tc.trading_certificate_is_amend = 1 )
            </if>
            <if test='params.certificateSupplement == "2"'>
                and (CASE tcc.type
                       WHEN 2 THEN tcc.road_transport_certificate_is_amend = 0
                       WHEN 1 THEN tcc.road_transport_certificate_is_amend = 0 AND tc.trading_certificate_is_amend = 0
                       END)
            </if>
        </if>
        <if test="params.type!=null">
            and tcc.type = #{params.type,jdbcType = INTEGER}
        </if>
        <if test="params.createdTimeStart !='' and params.createdTimeStart !=null">
            and tcc.created_time &gt;= DATE_FORMAT(#{params.createdTimeStart,jdbcType=VARCHAR},'%Y-%m-%d
            %k:%i:%S')
        </if>
        <if test="params.createdTimeEnd !='' and params.createdTimeEnd !=null">
            and tcc.created_time &lt;= DATE_FORMAT(#{params.createdTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.lastModifiedTimeStart !='' and params.lastModifiedTimeStart !=null">
            and tcc.last_modified_time &gt;= DATE_FORMAT(#{params.lastModifiedTimeStart,jdbcType=VARCHAR},'%Y-%m-%d
            %k:%i:%S')
        </if>
        <if test="params.lastModifiedTimeEnd !='' and params.lastModifiedTimeEnd !=null">
            and tcc.last_modified_time &lt;= DATE_FORMAT(#{params.lastModifiedTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d
            23:59:59')
        </if>
        <if test="params.lastModifiedBy!=null and params.lastModifiedBy!=''">
            and instr(tcc.last_modified_by,#{params.lastModifiedBy,jdbcType=VARCHAR})
        </if>
        <if test="params.ifAddBlacklist != null">
            and tcc.if_add_blacklist = #{params.ifAddBlacklist,jdbcType = INTEGER}
        </if>
        <if test="params.shippingFreightId != null">
            and tcc.shipping_freight_id = #{params.shippingFreightId,jdbcType = BIGINT}
        </if>
        <if test="params.shippingFreightAddTimeDesc != null">
            <choose>
                <when test="params.shippingFreightAddTimeDesc == 1">
                    order by tcc.shipping_freight_add_time desc
                </when>
                <otherwise>
                    order by tcc.id desc
                </otherwise>
            </choose>
        </if>
    </select>


    <select id="getCompanyCarrierDetailById" resultType="com.logistics.tms.controller.companycarrier.response.CompanyCarrierDetailResponseModel">
        select
        tcc.id                                       as companyCarrierId,
        tcc.company_water_mark                       as companyWaterMark,
        tcc.road_transport_certificate_number        as roadTransportCertificateNumber,
        tcc.road_transport_certificate_image         as roadTransportCertificateImage,
        tcc.road_transport_certificate_validity_time as roadTransportCertificateValidityTime,
        tcc.road_transport_certificate_is_forever    as roadTransportCertificateIsForever,
        tcc.road_transport_certificate_is_amend      as roadTransportCertificateIsAmend,
        tcc.type                                     as type,
        tcc.commit_other_fee                         as commitOtherFee,
        tcc.remark                                   as remark,
        tcc.level                                    as level,
        tcc.freight_tax_point                        as freightTaxPoint,
        tcc.other_fee_tax_point                      as otherFeeTaxPoint,

        tc.company_name                              as companyCarrierName,
        tc.trading_certificate_image                 as tradingCertificateImage,
        tc.trading_certificate_validity_time         as tradingCertificateValidityTime,
        tc.trading_certificate_is_forever            as tradingCertificateIsForever,
        tc.trading_certificate_is_amend              as tradingCertificateIsAmend
        from t_company_carrier tcc
        left join t_company tc on tc.id = tcc.company_id and tc.valid = 1
        where tcc.valid = 1
        and tcc.id = #{companyCarrierId,jdbcType = BIGINT}
    </select>

    <select id="fuzzyQueryCompanyCarrierInfo" resultType="com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel">
        select
        tcc.id                                                                                                                  as companyId,
        tcc.type                                                                                                                as companyType,
        tc.company_name                                                                                                         as companyName,
        tcct.contact_name                                                                                                       as contactName,
        AES_DECRYPT(UNHEX(tcct.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as contactPhone
        from t_company_carrier tcc
        left join t_company tc on tc.id = tcc.company_id and tc.valid = 1
        left join t_carrier_contact tcct on tcct.company_carrier_id = tcc.id and tcc.type = 2 and tcct.valid = 1
        where tcc.valid = 1
        <if test="params.companyName != null and params.companyName !=''">
            and ((tcc.type = 1 and instr(tc.company_name,#{params.companyName,jdbcType=VARCHAR}))
            or (tcc.type = 2 and (instr(tcct.contact_name,#{params.companyName,jdbcType=VARCHAR}) or instr(AES_DECRYPT(UNHEX(tcct.contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') ,#{params.companyName,jdbcType=VARCHAR}))))
        </if>
        <if test="params.companyType != null">
            and tcc.type = #{params.companyType,jdbcType=INTEGER}
        </if>
        <if test="params.level != null">
            and tcc.level = #{params.level,jdbcType=INTEGER}
        </if>
        <if test="params.ifAddBlacklist != null">
            and tcc.if_add_blacklist = #{params.ifAddBlacklist,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectCompanyCarrierInfoByIds" resultType="com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel">
        select
        tcc.id                                                                                                                  as companyId,
        tcc.type                                                                                                                as companyType,
        tc.company_name                                                                                                         as companyName,
        tcct.id                                                                                                                 as contactId,
        tcct.contact_name                                                                                                       as contactName,
        AES_DECRYPT(UNHEX(tcct.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as contactPhone,
        tcc.level                                                                                                               as isOurCompany,
        tcc.if_add_blacklist as ifAddBlacklist
        from t_company_carrier tcc
        left join t_company tc on tc.id = tcc.company_id and tc.valid = 1
        left join t_carrier_contact tcct on tcct.company_carrier_id = tcc.id and tcc.type =2 and tcct.valid = 1
        where tcc.valid = 1
          and tcc.id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="getByName" resultMap="BaseResultMap">
        select
        tcc.*
        from t_company_carrier tcc
        left join t_company tc on tc.id = tcc.company_id
        where tcc.valid = 1 and tc.valid = 1
        and tc.company_name = #{companyName,jdbcType=VARCHAR}
    </select>

    <update id="updateByPrimaryKeySelectiveForTime" parameterType="com.logistics.tms.entity.TCompanyCarrier">
        update t_company_carrier
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="companyWaterMark != null">
                company_water_mark = #{companyWaterMark,jdbcType=VARCHAR},
            </if>
            <if test="roadTransportCertificateNumber != null">
                road_transport_certificate_number = #{roadTransportCertificateNumber,jdbcType=VARCHAR},
            </if>
            <if test="roadTransportCertificateImage != null">
                road_transport_certificate_image = #{roadTransportCertificateImage,jdbcType=VARCHAR},
            </if>
                road_transport_certificate_validity_time = #{roadTransportCertificateValidityTime,jdbcType=TIMESTAMP},
            <if test="roadTransportCertificateIsForever != null">
                road_transport_certificate_is_forever = #{roadTransportCertificateIsForever,jdbcType=INTEGER},
            </if>
            <if test="roadTransportCertificateIsAmend != null">
                road_transport_certificate_is_amend = #{roadTransportCertificateIsAmend,jdbcType=INTEGER},
            </if>
            <if test="enabled != null">
                enabled = #{enabled,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="level != null">
                level = #{level,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                source = #{source,jdbcType=INTEGER},
            </if>
            <if test="commitOtherFee != null">
                commit_other_fee = #{commitOtherFee,jdbcType=INTEGER},
            </if>
            <if test="otherFeeTaxPoint != null">
                other_fee_tax_point = #{otherFeeTaxPoint,jdbcType=DECIMAL},
            </if>
            <if test="freightTaxPoint != null">
                freight_tax_point = #{freightTaxPoint,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="authorizationStatus != null">
                authorization_status = #{authorizationStatus,jdbcType=INTEGER},
            </if>
            <if test="realNameAuthenticationStatus != null">
                real_name_authentication_status = #{realNameAuthenticationStatus,jdbcType=INTEGER},
            </if>
            <if test="ifAddBlacklist != null">
                if_add_blacklist = #{ifAddBlacklist,jdbcType=INTEGER},
            </if>
            <if test="ifLessThanTruckload != null">
                if_less_than_truckload = #{ifLessThanTruckload,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectNoOurCompanyCarrierById" resultType="com.logistics.tms.biz.demandorder.model.CompanyCarrierByIdModel">
        select
        tcc.id                                                                                                                  as companyCarrierId,
        tcc.type                                                                                                                as companyCarrierType,
        tcc.if_add_blacklist                                                                                                    as ifAddBlacklist,
        tc.company_name                                                                                                         as companyCarrierName,
        tcct.id                                                                                                                 as carrierContactId,
        tcct.contact_name                                                                                                       as carrierContactName,
        AES_DECRYPT(UNHEX(tcct.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactPhone
        from t_company_carrier tcc
        left join t_company tc on tc.id = tcc.company_id and tcc.type = 1 and tc.valid = 1
        left join t_carrier_contact tcct on tcc.id = tcct.company_carrier_id and tcc.type = 2 and tcct.valid = 1
        where tcc.valid = 1
        and level = 2
        and tcc.id = #{companyCarrierId,jdbcType=BIGINT}
    </select>

    <select id="selectCarrierById" resultType="com.logistics.tms.biz.demandorder.model.CompanyCarrierByIdModel">
        select
        tcc.id                                                                                                                  as companyCarrierId,
        tcc.type                                                                                                                as companyCarrierType,
        tc.company_name                                                                                                         as companyCarrierName,
        tcct.id                                                                                                                 as carrierContactId,
        tcct.contact_name                                                                                                       as carrierContactName,
        AES_DECRYPT(UNHEX(tcct.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactPhone
        from t_company_carrier tcc
        left join t_company tc on tc.id = tcc.company_id and tcc.type = 1 and tc.valid = 1
        left join t_carrier_contact tcct on tcc.id = tcct.company_carrier_id and tcc.type = 2 and tcct.valid = 1
        where tcc.valid = 1
        and tcc.id = #{companyCarrierId,jdbcType=BIGINT}
    </select>

    <select id="getByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_company_carrier
        where valid = 1
        <if test="ids != null and ids != ''">
            and id in (${ids})
        </if>
    </select>

    <select id="getCompanyCarrierInfoById" resultType="com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel">
        select
        tcc.id                                                                                                                    as companyId,
        tcc.type                                                                                                                  as companyType,
        tc.company_name                                                                                                           as companyName,
        tcct.id                                                                                                                   as contactId,
        tcct.contact_name                                                                                                         as contactName,
        AES_DECRYPT(UNHEX(tcct.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')   as contactPhone,
        AES_DECRYPT(UNHEX(tcct.identity_number), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as identityNumber,
        tcc.level                                                                                                                 as isOurCompany,
        tcc.authorization_status                                                                                                  as authorizationStatus,
        tcc.real_name_authentication_status                                                                                       as realNameAuthenticationStatus
        from t_company_carrier tcc
        left join t_company tc on tc.id = tcc.company_id and tc.valid = 1
        left join t_carrier_contact tcct on tcct.company_carrier_id = tcc.id and tcc.type = 2 and tcct.valid = 1
        where tcc.valid = 1
        and tcc.id = #{id}
    </select>

    <select id="selectTaxPointById" resultType="com.logistics.tms.controller.settlestatement.packaging.response.CarrierTaxPointResponseModel">
        select
        other_fee_tax_point as otherFeeTaxPoint,
        freight_tax_point   as freightTaxPoint
        from t_company_carrier
        where valid = 1
        and id = #{companyCarrierId,jdbcType=BIGINT}
    </select>

    <select id="selectCarrierBasicInfo" resultType="com.logistics.tms.controller.baiscinfo.web.response.CarrierBasicInfoResponseModel">
        select
        tcc.type                                                                                                                  as companyCarrierType,
        tc.id                                                                                                                     as companyId,
        tc.company_name                                                                                                           as companyCarrierName,
        tcct.contact_name                                                                                                         as carrierContactName,
        AES_DECRYPT(UNHEX(tcct.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')   as carrierContactPhone,
        AES_DECRYPT(UNHEX(tcct.identity_number), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactIdentityNumber,
        tcc.`authorization_status`                                                                                                as authorizationStatus,
        tcc.`real_name_authentication_status`                                                                                     as realNameAuthenticationStatus,
        tcc.created_time                                                                                                          as companyCarrierCreateTime
        from t_company_carrier tcc
        left join t_company tc on tc.id = tcc.company_id and tc.valid = 1
        left join t_carrier_contact tcct on tcct.company_carrier_id = tcc.id and tcc.type = 2 and tcct.valid = 1
        where tcc.valid = 1
        and tcc.id = #{companyCarrierId,jdbcType=BIGINT}
    </select>

    <select id="selectPersonCarrierByMobile" resultType="com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel">
        select
        tcc.id                                                                                                                    as companyId,
        tcc.type                                                                                                                  as companyType,
        tcct.id                                                                                                                   as contactId,
        tcct.contact_name                                                                                                         as contactName,
        AES_DECRYPT(UNHEX(tcct.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')   as contactPhone,
        AES_DECRYPT(UNHEX(tcct.identity_number), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as identityNumber,
        tcc.`authorization_status`                                                                                                as authorizationStatus,
        tcc.`real_name_authentication_status`                                                                                     as realNameAuthenticationStatus,
        tcct.identity_face_file                                                                                                   as identityFaceFile,
        tcct.identity_national_file                                                                                               as identityNationalFile
        from t_company_carrier tcc
        left join t_carrier_contact tcct on tcct.company_carrier_id = tcc.id and tcct.valid = 1
        where tcc.valid = 1
          and tcc.type = 2
          and AES_DECRYPT(UNHEX(tcct.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') = #{mobile,jdbcType=VARCHAR}
    </select>

    <select id="getByCompanyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_company_carrier
        where valid = 1
        and company_id = #{companyId}
    </select>

    <select id="selectPersonCarrierByMobiles" resultType="com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel">
        select
        tcc.id                                                                                                                    as companyId,
        tcc.type                                                                                                                  as companyType,
        tcct.id                                                                                                                   as contactId,
        tcct.contact_name                                                                                                         as contactName,
        AES_DECRYPT(UNHEX(tcct.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')   as contactPhone,
        AES_DECRYPT(UNHEX(tcct.identity_number), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as identityNumber,
        tcc.`authorization_status`                                                                                                as authorizationStatus,
        tcc.`real_name_authentication_status`                                                                                     as realNameAuthenticationStatus,
        tcct.identity_face_file                                                                                                   as identityFaceFile,
        tcct.identity_national_file                                                                                               as identityNationalFile
        from t_company_carrier tcc
        left join t_carrier_contact tcct on tcct.company_carrier_id = tcc.id and tcct.valid = 1
        where tcc.valid = 1
          and tcc.type = 2
        <choose>
            <when test="mobiles != null and mobiles.size() != 0">
                and AES_DECRYPT(UNHEX(tcct.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') in
                <foreach collection="mobiles" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </select>

    <update id="batchUpdateSelective" parameterType="com.logistics.tms.entity.TCompanyCarrier">
        <foreach collection="recordList" item="item" separator=";">
            update t_company_carrier
            <set>
                <if test="item.companyId != null">
                    company_id = #{item.companyId,jdbcType=BIGINT},
                </if>
                <if test="item.companyWaterMark != null">
                    company_water_mark = #{item.companyWaterMark,jdbcType=VARCHAR},
                </if>
                <if test="item.roadTransportCertificateNumber != null">
                    road_transport_certificate_number = #{item.roadTransportCertificateNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.roadTransportCertificateImage != null">
                    road_transport_certificate_image = #{item.roadTransportCertificateImage,jdbcType=VARCHAR},
                </if>
                <if test="item.roadTransportCertificateValidityTime != null">
                    road_transport_certificate_validity_time = #{item.roadTransportCertificateValidityTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.roadTransportCertificateIsForever != null">
                    road_transport_certificate_is_forever = #{item.roadTransportCertificateIsForever,jdbcType=INTEGER},
                </if>
                <if test="item.roadTransportCertificateIsAmend != null">
                    road_transport_certificate_is_amend = #{item.roadTransportCertificateIsAmend,jdbcType=INTEGER},
                </if>
                <if test="item.enabled != null">
                    enabled = #{item.enabled,jdbcType=INTEGER},
                </if>
                <if test="item.type != null">
                    type = #{item.type,jdbcType=INTEGER},
                </if>
                <if test="item.level != null">
                    level = #{item.level,jdbcType=INTEGER},
                </if>
                <if test="item.source != null">
                    source = #{item.source,jdbcType=INTEGER},
                </if>
                <if test="item.commitOtherFee != null">
                    commit_other_fee = #{item.commitOtherFee,jdbcType=INTEGER},
                </if>
                <if test="item.otherFeeTaxPoint != null">
                    other_fee_tax_point = #{item.otherFeeTaxPoint,jdbcType=DECIMAL},
                </if>
                <if test="item.freightTaxPoint != null">
                    freight_tax_point = #{item.freightTaxPoint,jdbcType=DECIMAL},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.authorizationStatus != null">
                    authorization_status = #{item.authorizationStatus,jdbcType=INTEGER},
                </if>
                <if test="item.realNameAuthenticationStatus != null">
                    real_name_authentication_status = #{item.realNameAuthenticationStatus,jdbcType=INTEGER},
                </if>
                <if test="item.ifAddBlacklist != null">
                    if_add_blacklist = #{item.ifAddBlacklist,jdbcType=INTEGER},
                </if>
                <if test="item.ifLessThanTruckload != null">
                    if_less_than_truckload = #{item.ifLessThanTruckload,jdbcType=INTEGER},
                </if>
                <if test="item.shippingFreightId != null">
                    shipping_freight_id = #{item.shippingFreightId,jdbcType=BIGINT},
                </if>
                <if test="item.shippingFreightAddUser != null">
                    shipping_freight_add_user = #{item.shippingFreightAddUser,jdbcType=VARCHAR},
                </if>
                <if test="item.shippingFreightAddTime != null">
                    shipping_freight_add_time = #{item.shippingFreightAddTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getCountCarrierByShippingFreightIds" resultType="com.logistics.tms.biz.shippingfreight.model.GetCountCarrierByShippingFreightIdModel">
        select
        shipping_freight_id as shippingFreightId,
        count(id) as carrierCount,
        GROUP_CONCAT(id) as carrierIds
        from
        t_company_carrier
        where
        valid = 1
        and shipping_freight_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
        group by shipping_freight_id
    </select>

    <update id="updateNoAuthCompanyAndMore31Day">
      update
      t_company_carrier
      set if_add_blacklist = 1
      where
      valid = 1
      and authorization_status != 3 and if_add_blacklist = 0 and type = 1
      and DATE_SUB(CURDATE(), INTERVAL 31 DAY) &gt;= created_time
      and id != #{carrierCompanyId,jdbcType=BIGINT}
    </update>

</mapper>