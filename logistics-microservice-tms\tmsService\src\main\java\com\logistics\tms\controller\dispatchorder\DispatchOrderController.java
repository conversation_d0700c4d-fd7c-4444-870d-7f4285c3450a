package com.logistics.tms.controller.dispatchorder;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.dispatchorder.DispatchOrderBiz;
import com.logistics.tms.biz.shippingfreight.ShippingFreightBiz;
import com.logistics.tms.biz.shippingorder.ShippingOrderBiz;
import com.logistics.tms.controller.dispatchorder.request.*;
import com.logistics.tms.controller.dispatchorder.response.*;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@RestController
@RequestMapping(value = "/service/dispatchOrder")
public class DispatchOrderController {

    @Autowired
    private DispatchOrderBiz dispatchOrderBiz;


    /**
     * 调度单列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取调度单列表")
    @PostMapping(value = "/searchList")
    public Result<PageInfo<DispatchOrderSearchResponseModel>> searchList(@RequestBody DispatchOrderSearchRequestModel requestModel) {
        PageInfo<DispatchOrderSearchResponseModel> pageInfo = dispatchOrderBiz.searchList(requestModel);
        return Result.success(pageInfo);
    }

    /**
     * 根据调度单id查询运单列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取调度单子列表")
    @PostMapping(value = "/getCarrierOrder")
    public Result<List<DispatchOrderCarrierChildResponseModel>> getCarrierOrder(@RequestBody DispatchOrderCarrierRequestModel requestModel) {
        List<DispatchOrderCarrierChildResponseModel> list = dispatchOrderBiz.getCarrierOrder(requestModel);
        if (ListUtils.isEmpty(list)){
            list = new ArrayList<>();
        }
        return Result.success(list);
    }

    /**
     * 导出调度单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出调度单")
    @PostMapping(value = "/exportDispatchOrder")
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<DispatchOrderSearchResponseModel>> exportDispatchOrder(@RequestBody DispatchOrderSearchRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        PageInfo<DispatchOrderSearchResponseModel> pageInfo = dispatchOrderBiz.searchList(requestModel);
        List<DispatchOrderSearchResponseModel> list = new ArrayList<>();
        if (ListUtils.isNotEmpty(pageInfo.getList())){
            list = pageInfo.getList();
        }
        return Result.success(list);
    }

    /**
     * 运费审核管理-查看/修改/审核/驳回详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "调度单详情")
    @PostMapping(value = "/getDetail")
    public Result<DispatchOrderDetailResponseModel> getDetail(@RequestBody DispatchOrderCarrierRequestModel requestModel) {
        DispatchOrderDetailResponseModel responseModel = dispatchOrderBiz.getDetail(requestModel);
        return Result.success(responseModel);
    }
    /**
     * 根据司机姓名联系方式查询
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/getDriverByPhone")
    @ApiOperation(value = "根据司机姓名联系方式查询")
    public Result<List<DriverByNameAndPhoneResponseModel>> getDriverByNameAndPhone(@RequestBody VehicleDriverRelationQueryRequestModel requestModel) {
        List<DriverByNameAndPhoneResponseModel> list = dispatchOrderBiz.getDriverByNameAndPhone(requestModel);
        if (ListUtils.isEmpty(list)){
            list = new ArrayList<>();
        }
        return Result.success(list);
    }



}
