package com.logistics.tms.biz.carrierorder.model;


import com.logistics.tms.biz.demandorder.model.CancelCarrierOrderChangeDemandStateSynchronizeModel;
import lombok.Data;

import java.util.List;

@Data
public class CancelCarrierOrderChangeDemandAndCarrierStataSynchronizeModel {

    private List<CancelCarrierOrderChangeDemandStateSynchronizeModel> cancelCarrierOrderChangeDemandStateSynchronizeModels;

    private List<CarrierOrderSynchronizeModel> synchronizeModels;
}
