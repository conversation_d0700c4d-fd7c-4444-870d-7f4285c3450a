package com.logistics.tms.controller.vehiclelength;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.vehiclelength.VehicleLengthBiz;
import com.logistics.tms.controller.vehiclelength.request.AddOrUpdateVehicleLengthRequestModel;
import com.logistics.tms.controller.vehiclelength.request.SearchVehicleLengthListRequestModel;
import com.logistics.tms.controller.vehiclelength.request.VehicleLengthDetailRequestModel;
import com.logistics.tms.controller.vehiclelength.response.SearchVehicleLengthListResponseModel;
import com.logistics.tms.controller.vehiclelength.response.SelectVehicleLengthListResponseModel;
import com.logistics.tms.controller.vehiclelength.response.VehicleLengthDetailResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 车长配置管理
 * @author: wjf
 * @date: 2024/4/29 13:11
 */
@RestController
@RequestMapping(value = "/service/vehicleLength")
public class VehicleLengthController {

    @Resource
    private VehicleLengthBiz vehicleLengthBiz;

    /**
     * 分页搜索车长配置
     * @param requestModel 请求参数
     * @return 车长配置
     */
    @PostMapping(value = "/searchVehicleLengthList")
    @ApiOperation(value = "分页搜索车长配置")
    public Result<PageInfo<SearchVehicleLengthListResponseModel>> searchVehicleLengthList(@RequestBody SearchVehicleLengthListRequestModel requestModel){
        return Result.success(vehicleLengthBiz.searchVehicleLengthList(requestModel));
    }


    /**
     * 车长配置详情
     * @param requestModel 请求参数
     * @return 车长配置详情
     */
    @PostMapping(value = "/vehicleLengthDetail")
    @ApiOperation(value = "车长配置详情")
    public Result<VehicleLengthDetailResponseModel> vehicleLengthDetail(@RequestBody VehicleLengthDetailRequestModel requestModel){
        return Result.success(vehicleLengthBiz.vehicleLengthDetail(requestModel));
    }

    /**
     * 新增编辑车长
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/addOrUpdateVehicleLength")
    @ApiOperation(value = "新增编辑车长")
    public Result<Boolean> addOrUpdateVehicleLength(@RequestBody AddOrUpdateVehicleLengthRequestModel requestModel){
        vehicleLengthBiz.addOrUpdateVehicleLength(requestModel);
        return Result.success(true);
    }

    /**
     * 删除车长
     * @param requestModel 请求参数
     * @return boolean
     */
    @PostMapping(value = "/delVehicleLength")
    @ApiOperation(value = "删除车长")
    public Result<Boolean> delVehicleLength(@RequestBody VehicleLengthDetailRequestModel requestModel){
        vehicleLengthBiz.delVehicleLength(requestModel);
        return Result.success(true);
    }

    /**
     * 下拉选择车长
     * @return 下拉选择车长
     */
    @PostMapping(value = "/selectVehicleLengthList")
    @ApiOperation(value = "下拉选择车长")
    public Result<List<SelectVehicleLengthListResponseModel>> selectVehicleLengthList(){
        return Result.success(vehicleLengthBiz.selectVehicleLengthList());
    }
}
