package com.logistics.tms.mapper;

import com.logistics.tms.api.feign.dateremind.model.DateRemindListRequestModel;
import com.logistics.tms.api.feign.dateremind.model.DateRemindListResponseModel;
import com.logistics.tms.entity.TDateRemind;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TDateRemindMapper extends BaseMapper<TDateRemind> {

    List<DateRemindListResponseModel> searchDateRemindList(@Param("params") DateRemindListRequestModel requestModel);

    List<TDateRemind> getDateRemindByIds(@Param("ids") String dateRemindIds);

    void batchModifyDateRemind(@Param("list") List<TDateRemind> batchList);

    TDateRemind getDateRemindByName(@Param("dateRemindName") String dateRemindName);

    List<TDateRemind> getAllNeedRemindDate();

    int updateByPrimaryKeySelectiveExt(@Param("params") TDateRemind dateRemind);

}