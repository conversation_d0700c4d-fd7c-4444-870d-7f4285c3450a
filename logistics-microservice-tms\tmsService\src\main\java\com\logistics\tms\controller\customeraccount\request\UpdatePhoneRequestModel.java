package com.logistics.tms.controller.customeraccount.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/7/13 15:56
 */
@Data
public class UpdatePhoneRequestModel {

    @ApiModelProperty(value = "用户账号")
    private String userAccount;

    @ApiModelProperty(value = "手机验证码")
    private String verificationCode;

    @ApiModelProperty(value = "验证码来源")
    private Integer codeSource;

    @ApiModelProperty(value = "验证码类型")
    private Integer codeType;

    @ApiModelProperty("更换手机号令牌")
    private String updatePhoneToken;

    @ApiModelProperty("人脸识别传入唯一字符串(人脸识别认证传)")
    private String orderNo;

    @ApiModelProperty(value = "请求来源：1 司机（小程序），2 车主（前台）")
    private Integer requestSource = 2;

    @ApiModelProperty(value = "是否需要验证 实名信息")
    private boolean checkRealName = true;
}
