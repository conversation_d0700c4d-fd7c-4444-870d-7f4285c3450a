package com.logistics.management.webapi.controller.biddingorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchBiddingOrderListResponseDto {
    /**
     * 竞价单id
     */
    @ApiModelProperty("竞价单id")
    private String biddingOrderId="";

    /**
     * 竞价单号
     */
    @ApiModelProperty("竞价单号")
    private String biddingOrderCode="";

    /**
     * 竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价 5报价取消
     */
    @ApiModelProperty("竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价 5报价取消")
    private String biddingStatus="";

    /**
     * 竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价 5报价取消
     */
    @ApiModelProperty("竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价 5报价取消")
    private String biddingStatusLabel="";

    /**
     * 装卸方式 1一装一卸、2多装一卸
     */
    @ApiModelProperty("装卸方式 1一装一卸、2多装一卸")
    private String handlingMode="";

    /**
     * 装卸方式 1一装一卸、2多装一卸
     */
    @ApiModelProperty("装卸方式 1一装一卸、2多装一卸")
    private String handlingModeLabel="";

    /**
     * 关联单据
     */
    @ApiModelProperty("关联单据")
    private String demandCount="";

    /**
     * 报价承运商
     */
    @ApiModelProperty("报价承运商")
    private String carrierCount="";

    /**
     * 途径点
     */
    @ApiModelProperty("途径点")
    private String pathwayCount="";

    /**
     * 发货地址
     */
    @ApiModelProperty("发货地址")
    private String loadAddress = "";

    /**
     * 收货地址
     */
    @ApiModelProperty("收货地址")
    private String unloadAddress = "";

    /**
     * 总价
     */
    @ApiModelProperty("总价")
    private String quotePrice = "";

    /**
     * 车长
     */
    @ApiModelProperty("车长")
    private String vehicleLength = "";

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private String goodsCount = "";

    /**
     * 货物单位 1 件 2 吨
     */
    @ApiModelProperty("货物单位 1 件 2 吨")
    private String goodsUnit="";

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy = "";

    /**
     * 报价开始时间
     */
    @ApiModelProperty("报价开始时间")
    private String quoteStartTime = "";

    /**
     * 报价结束时间
     */
    @ApiModelProperty("报价结束时间")
    private String quoteEndTime = "";
}
