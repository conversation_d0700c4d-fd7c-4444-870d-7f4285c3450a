package com.logistics.tms.client.feign.tray.order.customerinorder.model.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class GetCarrierOrderDetailResponseModel {

    @ApiModelProperty(value = "运单编号")
    private String carrierOrderCode;

    @ApiModelProperty(value = "运单状态 0 待还盘、1 待确认、2 已还盘、3 已驳回，-1 已取消")
    private Integer customerOutState;

    @ApiModelProperty(value = "提货数量")
    private BigDecimal loadingCount;
}
