package com.logistics.management.webapi.controller.invoicingmanagement.packaging;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.SettleStatementTypeEnum;
import com.logistics.management.webapi.base.utils.ConvertPageInfoUtil;
import com.logistics.management.webapi.client.invoicingmanagement.InvoicingManagementClient;
import com.logistics.management.webapi.client.invoicingmanagement.request.*;
import com.logistics.management.webapi.client.invoicingmanagement.response.*;
import com.logistics.management.webapi.controller.invoicingmanagement.packaging.mapping.*;
import com.logistics.management.webapi.controller.invoicingmanagement.packaging.request.*;
import com.logistics.management.webapi.controller.invoicingmanagement.packaging.response.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 包装业务发票管理
 * @author: wjf
 * @date: 2024/3/19 15:07
 */
@Api(value = "包装业务发票管理",tags = "包装业务发票管理")
@RestController
@RequestMapping(value = "/api/invoicingManagement")
public class InvoicingManagementController {

    @Resource
    private InvoicingManagementClient invoicingManagementClient;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 发票管理-列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-列表 v3.18.0")
    @PostMapping(value = "/searchList")
    public Result<PageInfo<SearchInvoicingManagementListResponseDto>> searchList(@RequestBody SearchInvoicingManagementListRequestDto requestDto){
        SearchInvoicingManagementListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchInvoicingManagementListRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.PACKAGING_BUSINESS.getKey());
        Result<PageInfo<SearchInvoicingManagementListResponseModel>> result = invoicingManagementClient.searchList(requestModel);
        result.throwException();
        PageInfo<SearchInvoicingManagementListResponseDto> pageInfo = ConvertPageInfoUtil.convertPageInfo(result.getData(), SearchInvoicingManagementListResponseDto.class, new SearchInvoicingManagementListMapping());
        return Result.success(pageInfo);
    }

    /**
     * 发票管理-查看发票
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-查看发票 v3.18.0")
    @PostMapping(value = "/getInvoicePictures")
    public Result<List<GetInvoicePicturesResponseDto>> getInvoicePictures(@RequestBody @Valid InvoicingManagementDetailRequestDto requestDto){
        InvoicingManagementDetailRequestModel requestModel = MapperUtils.mapper(requestDto, InvoicingManagementDetailRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.PACKAGING_BUSINESS.getKey());
        Result<List<GetInvoicePicturesResponseModel>> result = invoicingManagementClient.getInvoicePictures(requestModel);
        List<String> sourceSrcList = new ArrayList<>();
        if (ListUtils.isNotEmpty(result.getData())){
            sourceSrcList = result.getData().stream().map(GetInvoicePicturesResponseModel::getInvoicePicture).collect(Collectors.toList());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(), GetInvoicePicturesResponseDto.class, new GetInvoicePicturesMapping(configKeyConstant.fileAccessAddress, imageMap)));
    }

    /**
     * 发票管理-修改业务名称 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-修改业务名称")
    @PostMapping(value = "/updateBusinessName")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> updateBusinessName(@RequestBody @Valid UpdateBusinessNameRequestDto requestDto){
        UpdateBusinessNameRequestModel requestModel = MapperUtils.mapper(requestDto, UpdateBusinessNameRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.PACKAGING_BUSINESS.getKey());
        return invoicingManagementClient.updateBusinessName(requestModel);
    }

    /**
     * 发票管理-详情-头部信息
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-头部信息 v3.18.0")
    @PostMapping(value = "/getDetail")
    public Result<InvoicingManagementDetailResponseDto> getDetail(@RequestBody @Valid InvoicingManagementDetailRequestDto requestDto){
        InvoicingManagementDetailRequestModel requestModel = MapperUtils.mapper(requestDto, InvoicingManagementDetailRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.PACKAGING_BUSINESS.getKey());
        Result<InvoicingManagementDetailResponseModel> result = invoicingManagementClient.getDetail(requestModel);
        return Result.success(MapperUtils.mapper(result.getData(), InvoicingManagementDetailResponseDto.class, new InvoicingManagementDetailMapping()));
    }

    /**
     * 发票管理-详情-发票列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-发票列表 v3.18.0")
    @PostMapping(value = "/getInvoiceList")
    public Result<List<GetInvoiceListResponseDto>> getInvoiceList(@RequestBody @Valid InvoicingManagementDetailRequestDto requestDto){
        InvoicingManagementDetailRequestModel requestModel = MapperUtils.mapper(requestDto, InvoicingManagementDetailRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.PACKAGING_BUSINESS.getKey());
        Result<List<GetInvoiceListResponseModel>> result = invoicingManagementClient.getInvoiceList(requestModel);
        return Result.success(MapperUtils.mapper(result.getData(), GetInvoiceListResponseDto.class,new GetInvoiceListMapping()));
    }

    /**
     * 发票管理-详情-发票列表-查看详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-发票列表-查看详情 v3.18.0")
    @PostMapping(value = "/getInvoiceDetail")
    public Result<GetInvoiceDetailResponseDto> getInvoiceDetail(@RequestBody @Valid GetInvoiceDetailRequestDto requestDto){
        GetInvoiceDetailRequestModel requestModel = MapperUtils.mapper(requestDto, GetInvoiceDetailRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.PACKAGING_BUSINESS.getKey());
        Result<GetInvoiceDetailResponseModel> result = invoicingManagementClient.getInvoiceDetail(requestModel);
        return Result.success(MapperUtils.mapper(result.getData(), GetInvoiceDetailResponseDto.class));
    }

    /**
     * 发票管理-详情-发票列表-新增/编辑发票
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-发票列表-新增/编辑发票 v3.18.0")
    @PostMapping(value = "/addOrModifyInvoice")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addOrModifyInvoice(@RequestBody @Valid AddOrModifyInvoiceRequestDto requestDto){
        AddOrModifyInvoiceRequestModel requestModel = MapperUtils.mapper(requestDto, AddOrModifyInvoiceRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.PACKAGING_BUSINESS.getKey());
        return invoicingManagementClient.addOrModifyInvoice(requestModel);
    }

    /**
     * 发票管理-详情-发票列表-删除发票
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-发票列表-删除发票 v3.18.0")
    @PostMapping(value = "/delInvoice")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> delInvoice(@RequestBody @Valid GetInvoiceDetailRequestDto requestDto){
        GetInvoiceDetailRequestModel requestModel = MapperUtils.mapper(requestDto, GetInvoiceDetailRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.PACKAGING_BUSINESS.getKey());
        return invoicingManagementClient.delInvoice(requestModel);
    }

    /**
     * 发票管理-详情-对账单列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-对账单列表 v3.18.0")
    @PostMapping(value = "/getSettleStatementList")
    public Result<PageInfo<GetSettleStatementListResponseDto>> getSettleStatementList(@RequestBody @Valid GetSettleStatementListRequestDto requestDto){
        GetSettleStatementListRequestModel requestModel = MapperUtils.mapper(requestDto, GetSettleStatementListRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.PACKAGING_BUSINESS.getKey());
        Result<PageInfo<GetSettleStatementListResponseModel>> result = invoicingManagementClient.getSettleStatementList(requestModel);
        result.throwException();
        PageInfo<GetSettleStatementListResponseDto> pageInfo = ConvertPageInfoUtil.convertPageInfo(result.getData(), GetSettleStatementListResponseDto.class, new GetSettleStatementListMapping());
        return Result.success(pageInfo);
    }

    /**
     * 发票管理-详情-对账单-添加对账单列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-对账单-添加对账单列表 v3.18.0")
    @PostMapping(value = "/getAddSettleStatementList")
    public Result<PageInfo<GetAddSettleStatementListResponseDto>> getAddSettleStatementList(@RequestBody @Valid GetAddSettleStatementListRequestDto requestDto){
        GetAddSettleStatementListRequestModel requestModel = MapperUtils.mapper(requestDto, GetAddSettleStatementListRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.PACKAGING_BUSINESS.getKey());
        Result<PageInfo<GetAddSettleStatementListResponseModel>> result = invoicingManagementClient.getAddSettleStatementList(requestModel);
        result.throwException();
        PageInfo<GetAddSettleStatementListResponseDto> pageInfo = ConvertPageInfoUtil.convertPageInfo(result.getData(), GetAddSettleStatementListResponseDto.class);
        return Result.success(pageInfo);
    }

    /**
     * 发票管理-详情-对账单-添加对账单
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-对账单-添加对账单 v3.18.0")
    @PostMapping(value = "/addSettleStatement")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addSettleStatement(@RequestBody @Valid AddInvoicingSettleStatementRequestDto requestDto){
        AddInvoicingSettleStatementRequestModel requestModel = MapperUtils.mapper(requestDto, AddInvoicingSettleStatementRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.PACKAGING_BUSINESS.getKey());
        return invoicingManagementClient.addSettleStatement(requestModel);
    }

    /**
     * 发票管理-详情-对账单-移除对账单
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-对账单-移除对账单 v3.18.0")
    @PostMapping(value = "/delSettleStatement")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> delSettleStatement(@RequestBody @Valid DelSettleStatementRequestDto requestDto){
        DelSettleStatementRequestModel requestModel = MapperUtils.mapper(requestDto, DelSettleStatementRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.PACKAGING_BUSINESS.getKey());
        return invoicingManagementClient.delSettleStatement(requestModel);
    }

    /**
     * 发票管理-详情-归档列表 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-归档列表")
    @PostMapping(value = "/getInvoicingArchiveList")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<List<GetInvoicingArchiveListResponseDto>> getInvoicingArchiveList(@RequestBody @Valid InvoicingManagementDetailRequestDto requestDto){
        InvoicingManagementDetailRequestModel requestModel = MapperUtils.mapper(requestDto, InvoicingManagementDetailRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.PACKAGING_BUSINESS.getKey());
        Result<List<GetInvoicingArchiveListResponseModel>> result = invoicingManagementClient.getInvoicingArchiveList(requestModel);
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        if (ListUtils.isNotEmpty(result.getData())){
            sourceSrcList = result.getData().stream().map(GetInvoicingArchiveListResponseModel::getRelativePath).collect(Collectors.toList());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(), GetInvoicingArchiveListResponseDto.class, new GetInvoicingArchiveListMapping(configKeyConstant.fileAccessAddress, imageMap)));
    }

    /**
     * 发票管理-详情-确认归档 3.24.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "发票管理-详情-确认归档")
    @PostMapping(value = "/invoicingArchive")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> invoicingArchive(@RequestBody @Valid InvoicingArchiveRequestDto requestDto){
        InvoicingArchiveRequestModel requestModel = MapperUtils.mapper(requestDto, InvoicingArchiveRequestModel.class);
        requestModel.setBusinessType(SettleStatementTypeEnum.PACKAGING_BUSINESS.getKey());
        return invoicingManagementClient.invoicingArchive(requestModel);
    }

}
