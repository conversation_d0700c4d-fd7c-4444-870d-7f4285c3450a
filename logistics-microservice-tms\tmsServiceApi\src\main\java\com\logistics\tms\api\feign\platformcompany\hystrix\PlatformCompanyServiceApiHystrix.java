package com.logistics.tms.api.feign.platformcompany.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.platformcompany.PlatformCompanyServiceApi;
import com.logistics.tms.api.feign.platformcompany.model.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/11/11 17:40
 */
@Component("tmsPlatformCompanyServiceApiHystrix")
public class PlatformCompanyServiceApiHystrix implements PlatformCompanyServiceApi {
    @Override
    public Result<PageInfo<SearchPlatformCompanyListResponseModel>> searchPlatformCompanyList(SearchPlatformCompanyListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addPlatformCompany(AddPlatformCompanyRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> delPlatformCompany(DelPlatformCompanyRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<PlatformCompanySelectListResponseModel>> platformCompanySelectList(PlatformCompanySelectListRequestModel requestModel) {
        return Result.timeout();
    }
}
