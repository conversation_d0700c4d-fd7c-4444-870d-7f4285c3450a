<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TExtVehicleSettlementItemMapper">
  <select id="getValidBySettlementId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_ext_vehicle_settlement_item
    where valid = 1
    and ext_vehicle_settlement_id = #{settlementId,jdbcType=BIGINT}
    and if_fallback = 0
  </select>

  <select id="getValidBySettlementIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_ext_vehicle_settlement_item
    where valid = 1
    and ext_vehicle_settlement_id in (${settlementIds})
    and if_fallback = 0
  </select>
</mapper>