package com.logistics.tms.controller.carrierorderloadcode.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class SaveCorrectLoadCodeResponseModel {

    @ApiModelProperty(value = "运单提货编码id")
    private Long carrierOrderLoadCodeId;

    @ApiModelProperty("云盘客户名称 (state=2 该字段为空)")
    private String trayCustomerCompanyName;
}
