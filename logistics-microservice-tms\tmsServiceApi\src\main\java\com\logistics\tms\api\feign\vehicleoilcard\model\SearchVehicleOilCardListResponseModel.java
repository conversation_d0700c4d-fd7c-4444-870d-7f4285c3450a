package com.logistics.tms.api.feign.vehicleoilcard.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2022/8/3 10:59
 */
@Data
public class SearchVehicleOilCardListResponseModel {
    @ApiModelProperty("车辆油卡表id")
    private Long vehicleOilCardId;
    @ApiModelProperty("卡号")
    private String cardNumber;
    @ApiModelProperty("绑定状态：0 未绑定，1 已绑定")
    private Integer status;
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
}
