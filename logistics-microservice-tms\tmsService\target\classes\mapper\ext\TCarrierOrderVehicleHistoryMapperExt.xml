<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderVehicleHistoryMapper" >
    <insert id="batchInsertSelective" parameterType="com.logistics.tms.entity.TCarrierOrderVehicleHistory" >
        <foreach collection="list" separator=";" item="item">
            insert into t_carrier_order_vehicle_history
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    id,
                </if>
                <if test="item.carrierOrderId != null" >
                    carrier_order_id,
                </if>
                <if test="item.vehicleId != null" >
                    vehicle_id,
                </if>
                <if test="item.vehicleNo != null" >
                    vehicle_no,
                </if>
                <if test="item.trailerVehicleId != null">
                    trailer_vehicle_id,
                </if>
                <if test="item.trailerVehicleNo != null">
                    trailer_vehicle_no,
                </if>
                <if test="item.driverId != null" >
                    driver_id,
                </if>
                <if test="item.driverName != null" >
                    driver_name,
                </if>
                <if test="item.driverMobile != null" >
                    driver_mobile,
                </if>
                <if test="item.driverIdentity != null" >
                    driver_identity,
                </if>
                <if test="item.expectLoadTime != null" >
                    expect_load_time,
                </if>
                <if test="item.expectArrivalTime != null" >
                    expect_arrival_time,
                </if>
                <if test="item.remark != null" >
                    remark,
                </if>
                <if test="item.vehicleHistoryId != null">
                    vehicle_history_id,
                </if>
                <if test="item.ifInvalid != null" >
                    if_invalid,
                </if>
                <if test="item.auditStatus != null" >
                    audit_status,
                </if>
                <if test="item.auditTime != null" >
                    audit_time,
                </if>
                <if test="item.auditUserId != null" >
                    audit_user_id,
                </if>
                <if test="item.auditUserName != null" >
                    audit_user_name,
                </if>
                <if test="item.rejectReason != null" >
                    reject_reason,
                </if>
                <if test="item.createdBy != null" >
                    created_by,
                </if>
                <if test="item.createdTime != null" >
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="item.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.carrierOrderId != null" >
                    #{item.carrierOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleId != null" >
                    #{item.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleNo != null" >
                    #{item.vehicleNo,jdbcType=VARCHAR},
                </if>
                <if test="item.trailerVehicleId != null">
                    #{item.trailerVehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.trailerVehicleNo != null">
                    #{item.trailerVehicleNo,jdbcType=VARCHAR},
                </if>
                <if test="item.driverId != null" >
                    #{item.driverId,jdbcType=BIGINT},
                </if>
                <if test="item.driverName != null" >
                    #{item.driverName,jdbcType=VARCHAR},
                </if>
                <if test="item.driverMobile != null" >
                    #{item.driverMobile,jdbcType=VARCHAR},
                </if>
                <if test="item.driverIdentity != null" >
                    #{item.driverIdentity,jdbcType=VARCHAR},
                </if>
                <if test="item.expectLoadTime != null" >
                    #{item.expectLoadTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.expectArrivalTime != null" >
                    #{item.expectArrivalTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null" >
                    #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleHistoryId != null">
                    #{item.vehicleHistoryId,jdbcType=BIGINT},
                </if>
                <if test="item.ifInvalid != null" >
                    #{item.ifInvalid,jdbcType=INTEGER},
                </if>
                <if test="item.auditStatus != null" >
                    #{item.auditStatus,jdbcType=INTEGER},
                </if>
                <if test="item.auditTime != null" >
                    #{item.auditTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.auditUserId != null" >
                    #{item.auditUserId,jdbcType=BIGINT},
                </if>
                <if test="item.auditUserName != null" >
                    #{item.auditUserName,jdbcType=VARCHAR},
                </if>
                <if test="item.rejectReason != null" >
                    #{item.rejectReason,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null" >
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <select id="getDriverVehicleInfoByCarrierOrderId" resultType="com.logistics.tms.controller.carrierorder.response.DriverAndVehicleResponseModel">
        select
        tcovh.id                 as vehicleHistoryId,
        tcovh.carrier_order_id   as carrierOrderId,
        tcovh.driver_id          as driverId,
        tcovh.driver_name        as driverName,
        tcovh.driver_mobile      as driverPhone,
        tcovh.driver_identity    as driverIdentityNumber,
        tcovh.vehicle_id         as vehicleId,
        tcovh.vehicle_no         as vehicleNo,
        tcovh.trailer_vehicle_id as trailerVehicleId,
        tcovh.trailer_vehicle_no as trailerVehicleNo,
        tvb.vehicle_property     as vehicleProperty
        from t_carrier_order_vehicle_history tcovh
        left join t_vehicle_basic tvb on tvb.id = tcovh.vehicle_id and tvb.valid = 1
        where tcovh.valid = 1
          and tcovh.if_invalid = 1
          and tcovh.carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
    </select>

    <select id="getTopByCarrierOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_order_vehicle_history
        where valid = 1
        <if test="params.carrierOrderId != null">
            and carrier_order_id = #{params.carrierOrderId,jdbcType=BIGINT}
        </if>
        <if test="params.driverId != null">
            and driver_id = #{params.driverId,jdbcType=BIGINT}
        </if>
        <if test="params.vehicleId != null">
            and vehicle_id = #{params.vehicleId,jdbcType=BIGINT}
        </if>
        order by id desc limit 1
    </select>

    <update id="batchUpdateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCarrierOrderVehicleHistory" >
        <foreach collection="list" item="item" separator=";">
            update t_carrier_order_vehicle_history
            <set >
                <if test="item.carrierOrderId != null" >
                    carrier_order_id = #{item.carrierOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleId != null" >
                    vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleNo != null" >
                    vehicle_no = #{item.vehicleNo,jdbcType=VARCHAR},
                </if>
                <if test="item.trailerVehicleId != null">
                    trailer_vehicle_id = #{item.trailerVehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.trailerVehicleNo != null">
                    trailer_vehicle_no = #{item.trailerVehicleNo,jdbcType=VARCHAR},
                </if>
                <if test="item.driverId != null" >
                    driver_id = #{item.driverId,jdbcType=BIGINT},
                </if>
                <if test="item.driverName != null" >
                    driver_name = #{item.driverName,jdbcType=VARCHAR},
                </if>
                <if test="item.driverMobile != null" >
                    driver_mobile = #{item.driverMobile,jdbcType=VARCHAR},
                </if>
                <if test="item.driverIdentity != null" >
                    driver_identity = #{item.driverIdentity,jdbcType=VARCHAR},
                </if>
                <if test="item.expectLoadTime != null" >
                    expect_load_time = #{item.expectLoadTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.expectArrivalTime != null" >
                    expect_arrival_time = #{item.expectArrivalTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null" >
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleHistoryId != null">
                    vehicle_history_id = #{item.vehicleHistoryId,jdbcType=BIGINT},
                </if>
                <if test="item.ifInvalid != null" >
                    if_invalid = #{item.ifInvalid,jdbcType=INTEGER},
                </if>
                <if test="item.auditStatus != null" >
                    audit_status = #{item.auditStatus,jdbcType=INTEGER},
                </if>
                <if test="item.auditTime != null" >
                    audit_time = #{item.auditTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.auditUserId != null" >
                    audit_user_id = #{item.auditUserId,jdbcType=BIGINT},
                </if>
                <if test="item.auditUserName != null" >
                    audit_user_name = #{item.auditUserName,jdbcType=VARCHAR},
                </if>
                <if test="item.rejectReason != null" >
                    reject_reason = #{item.rejectReason,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectWaitAuditCarrierOrders" resultType="java.lang.String">
        select
         DISTINCT  tco.carrier_order_code
        from t_carrier_order_vehicle_history tcovh
        left join t_carrier_order tco on tco.valid = 1 and tco.id  = tcovh.carrier_order_id
        where tcovh.carrier_order_id in 
        <foreach collection="carrierOrders" item="item" open="(" close=")" separator=",">
            ${item}
        </foreach>
        and tcovh.if_invalid = 0 and tcovh.audit_status = 0 and tcovh.valid = 1
    </select>

    <select id="getCarrierDriverByHistoryId" resultType="java.lang.Integer">
        select
        tcdr.enabled
        from t_carrier_order_vehicle_history tcovh
        left join t_carrier_driver_relation tcdr on tcovh.driver_id = tcdr.driver_id and tcdr.valid = 1
        where tcovh.valid = 1
        and tcovh.id = #{historyId,jdbcType=BIGINT}
        and tcdr.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
    </select>

    <select id="getInvalidTopByCarrierOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_order_vehicle_history
        where valid = 1
        and if_invalid = 0
        and carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
        order by id desc limit 1
    </select>
    <select id="getValidTopByCarrierOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_order_vehicle_history
        where valid = 1
        and if_invalid = 1
        and carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
    </select>

    <select id="getValidByCarrierOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_order_vehicle_history
        where valid = 1
        and if_invalid = 1
        and carrier_order_id in (${carrierOrderIds})
    </select>

    <select id="getAllByCarrierOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_order_vehicle_history
        where valid = 1
        and carrier_order_id in (${carrierOrderIds})
    </select>

    <select id="searchListByCarrierOrderParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_order_vehicle_history
        where valid = 1 and if_invalid = 1
        <if test="condition.vehicleNo!=null and condition.vehicleNo!=''">
            and instr(vehicle_no,#{condition.vehicleNo,jdbcType=VARCHAR})>0
        </if>
        <if test="condition.driver!=null and condition.driver!=''">
            and (instr(concat(driver_name,driver_mobile),#{condition.driver,jdbcType=VARCHAR})>0
            or instr(concat(driver_name,'-',driver_mobile),#{condition.driver,jdbcType=VARCHAR})>0)
        </if>
        <if test="condition.expectArrivalTimeFrom!=null and condition.expectArrivalTimeFrom!=''">
            and expect_arrival_time &gt;= DATE_FORMAT(#{condition.expectArrivalTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d
            %H:%i:%S')
        </if>
        <if test="condition.expectArrivalTimeTo!=null and condition.expectArrivalTimeTo!=''">
            and expect_arrival_time &lt;= DATE_FORMAT(#{condition.expectArrivalTimeTo,jdbcType=VARCHAR},'%Y-%m-%d
            23:59:59')
        </if>
        <if test="condition.expectLoadTimeFrom!=null and condition.expectLoadTimeFrom!=''">
            and expect_load_time &gt;= DATE_FORMAT(#{condition.expectLoadTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d
            %H:%i:%S')
        </if>
        <if test="condition.expectLoadTimeTo!=null and condition.expectLoadTimeTo!=''">
            and expect_load_time &lt;= DATE_FORMAT(#{condition.expectLoadTimeTo,jdbcType=VARCHAR},'%Y-%m-%d
            23:59:59')
        </if>
    </select>

    <select id="getDriverVehicleInfoByCode" resultType="com.logistics.tms.controller.carrierorder.response.GetDriverVehicleInfoByCodeResponseModel">
        select
        tco.carrier_order_code as carrierOrderCode,

        tcovh.vehicle_no as vehicleNo,
        tcovh.trailer_vehicle_no as trailerVehicleNo,
        tcovh.driver_name as driverName,
        tcovh.driver_mobile as driverMobile,
        tcovh.driver_identity as driverIdentity
        from t_carrier_order_vehicle_history tcovh
        left join t_carrier_order tco on tco.id = tcovh.carrier_order_id and tco.valid = 1
        where tcovh.valid = 1
        and tcovh.if_invalid = 1
        <choose>
            <when test="params.carrierOrderCodeList != null and params.carrierOrderCodeList.size > 0">
                and tco.carrier_order_code in
                <foreach collection="params.carrierOrderCodeList" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </select>


    <select id="listValidByVehicleNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_carrier_order_vehicle_history
    where valid = 1  and if_invalid = 1
    and instr(vehicle_no,#{condition.vehicleNo,jdbcType=VARCHAR})>0
</select>


</mapper>