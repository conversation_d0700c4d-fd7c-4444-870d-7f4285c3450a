package com.logistics.management.webapi.api.impl.attendance;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.attendance.AttendanceApi;
import com.logistics.management.webapi.api.feign.attendance.dto.*;
import com.logistics.management.webapi.api.impl.attendance.mapping.AttendanceChangeDetailMapping;
import com.logistics.management.webapi.api.impl.attendance.mapping.AttendanceDetailMapping;
import com.logistics.management.webapi.api.impl.attendance.mapping.SearchAttendanceChangeListMapping;
import com.logistics.management.webapi.api.impl.attendance.mapping.SearchAttendanceListMapping;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.AttendanceChangeAuditStatusEnum;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.ExportUtils;
import com.logistics.tms.api.feign.attendance.AttendanceServiceApi;
import com.logistics.tms.api.feign.attendance.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;

/**
 * 考勤管理
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/15
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class AttendanceApiImpl implements AttendanceApi {

	private final CommonBiz commonBiz;
	private final ConfigKeyConstant configKeyConstant;
	private final AttendanceServiceApi attendanceServiceApi;

	/**
	 * 查询考勤列表 (下班打卡倒叙)
	 * @param requestDto
	 * @return PageInfo<SearchAttendanceListResponseDto>
	 */
	@Override
	public Result<PageInfo<SearchAttendanceListResponseDto>> searchAttendanceList(@RequestBody SearchAttendanceListRequestDto requestDto) {
		SearchAttendanceListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchAttendanceListRequestModel.class);
		Result<PageInfo<SearchAttendanceListResponseModel>> pageInfoResult = attendanceServiceApi.searchAttendanceList(requestModel);
		pageInfoResult.throwException();

		PageInfo pageInfo = pageInfoResult.getData();
		List<SearchAttendanceListResponseModel> responseModelList = pageInfo.getList();
		if (ListUtils.isNotEmpty(responseModelList)) {
			List<SearchAttendanceListResponseDto> responseDtoList = MapperUtils.mapper(responseModelList, SearchAttendanceListResponseDto.class,
					new SearchAttendanceListMapping());
			pageInfo.setList(responseDtoList);
		}
		return Result.success(pageInfo);
	}

	/**
	 * 查询考勤列表导出(下班打卡倒序)
	 * @param requestDto
	 * @param response
	 */
	@Override
	@IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
	public void exportAttendanceList(SearchAttendanceListRequestDto requestDto, HttpServletResponse response) {
		SearchAttendanceListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchAttendanceListRequestModel.class);
		Result<List<SearchAttendanceListResponseModel>> responseModelResult = attendanceServiceApi.exportAttendanceList(requestModel);
		responseModelResult.throwException();

		String fileName = "司机考勤列表" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
		List<SearchAttendanceListResponseDto> responseDtoList = MapperUtils.mapper(responseModelResult.getData(), SearchAttendanceListResponseDto.class,
				new SearchAttendanceListMapping());
		ExportUtils.exportByYeloExcel(response, responseDtoList, SearchAttendanceListResponseDto.class, fileName);
	}

	/**
	 * 考勤记录详情
	 * @param requestDto
	 * @return AttendanceDetailResponseDto
	 */
	@Override
	public Result<AttendanceDetailResponseDto> attendanceDetail(@RequestBody @Valid AttendanceDetailRequestDto requestDto) {
		AttendanceDetailRequestModel requestModel = MapperUtils.mapper(requestDto, AttendanceDetailRequestModel.class);
		Result<AttendanceDetailResponseModel> responseModelResult = attendanceServiceApi.attendanceDetail(requestModel);
		responseModelResult.throwException();

		Map<String, String> picMap = Optional.ofNullable(responseModelResult.getData())
				.map(d -> {
					return commonBiz.batchGetOSSFileUrl(Lists.newArrayList(d.getOnDutyPunchPic(), d.getOffDutyPunchPic()));
				})
				.orElse(new HashMap<>());
		AttendanceDetailResponseDto responseDto = MapperUtils.mapper(responseModelResult.getData(), AttendanceDetailResponseDto.class,
				new AttendanceDetailMapping(configKeyConstant.fileAccessAddress, picMap));
		return Result.success(responseDto);
	}

	/**
	 * 查询考勤变更申请列表
	 * @param requestDto
	 * @return PageInfo<SearchAttendanceChangeListResponseDto>
	 */
	@Override
	public Result<PageInfo<SearchAttendanceChangeListResponseDto>> searchAttendanceChangeList(@RequestBody SearchAttendanceChangeListRequestDto requestDto) {
		SearchAttendanceChangeListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchAttendanceChangeListRequestModel.class);
		Result<PageInfo<SearchAttendanceChangeListResponseModel>> pageInfoResult = attendanceServiceApi.searchAttendanceChangeList(requestModel);
		pageInfoResult.throwException();

		PageInfo pageInfo = pageInfoResult.getData();
		List<SearchAttendanceChangeListResponseModel> responseModelList = pageInfo.getList();
		if (ListUtils.isNotEmpty(responseModelList)) {
			List<SearchAttendanceChangeListResponseDto> responseDtoList = MapperUtils.mapper(responseModelList, SearchAttendanceChangeListResponseDto.class,
					new SearchAttendanceChangeListMapping());
			pageInfo.setList(responseDtoList);
		}
		return Result.success(pageInfo);
	}

	/**
	 * 导出考勤审核变更列表
	 * @param requestDto
	 * @param response
	 */
	@Override
	@IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
	public void exportAttendanceChangeList(SearchAttendanceChangeListRequestDto requestDto, HttpServletResponse response) {
		SearchAttendanceChangeListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchAttendanceChangeListRequestModel.class);
		Result<List<SearchAttendanceChangeListResponseModel>> responseModelResult = attendanceServiceApi.exportAttendanceChangeList(requestModel);
		responseModelResult.throwException();

		String fileName = "司机考勤列表" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
		List<SearchAttendanceChangeListResponseDto> responseDtoList = MapperUtils.mapper(responseModelResult.getData(),
				SearchAttendanceChangeListResponseDto.class, new SearchAttendanceChangeListMapping());
		ExportUtils.exportByYeloExcel(response, responseDtoList, SearchAttendanceChangeListResponseDto.class, fileName);
	}

	/**
	 * 审核变更申请详情
	 * @param requestDto
	 * @return AttendanceChangeDetailResponseDto
	 */
	@Override
	public Result<AttendanceChangeDetailResponseDto> attendanceChangeDetail(@RequestBody @Valid AttendanceChangeDetailRequestDto requestDto) {
		AttendanceChangeDetailRequestModel requestModel = MapperUtils.mapper(requestDto, AttendanceChangeDetailRequestModel.class);
		Result<AttendanceChangeDetailResponseModel> responseModelResult = attendanceServiceApi.attendanceChangeDetail(requestModel);
		responseModelResult.throwException();

		Map<String, String> picMap = Optional.ofNullable(responseModelResult.getData())
				.map(d -> {
					return commonBiz.batchGetOSSFileUrl(Lists.newArrayList(d.getOnDutyPunchPic(), d.getOffDutyPunchPic()));
				})
				.orElse(new HashMap<>());
		AttendanceChangeDetailResponseDto responseDto = MapperUtils.mapper(responseModelResult.getData(), AttendanceChangeDetailResponseDto.class,
				new AttendanceChangeDetailMapping(configKeyConstant.fileAccessAddress, picMap));
		return Result.success(responseDto);
	}

	/**
	 * 考勤变更申请审核
	 * @param requestDto
	 * @return boolean
	 */
	@Override
	@IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
	public Result<Boolean> auditAttendanceChangeApply(@RequestBody @Valid AuditAttendanceChangeApplyRequestDto requestDto) {
		Integer auditType = Integer.valueOf(requestDto.getAuditType());
		if (AttendanceChangeAuditStatusEnum.AUDIT_REJECT.getKey().equals(auditType)) {
			if (StringUtils.isBlank(requestDto.getRemark()) || requestDto.getRemark().length() > CommonConstant.INTEGER_ONE_HUNDRED) {
				throw new BizException(ManagementWebApiExceptionEnum.REMARKS_VERIFICATION_MESSAGE);
			}
		}
		AuditAttendanceChangeApplyRequestModel requestModel = MapperUtils.mapper(requestDto, AuditAttendanceChangeApplyRequestModel.class);
		return attendanceServiceApi.auditAttendanceChangeApply(requestModel);
	}

	/**
	 * 考勤变更申请撤销
	 * @param requestDto
	 * @return boolean
	 */
	@Override
	@IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
	public Result<Boolean> cancelAttendanceChangeApply(@RequestBody @Valid CancelAttendanceChangeApplyRequestDto requestDto) {
		CancelAttendanceChangeApplyRequestModel requestModel = MapperUtils.mapper(requestDto, CancelAttendanceChangeApplyRequestModel.class);
		return attendanceServiceApi.cancelAttendanceChangeApply(requestModel);
	}
}
