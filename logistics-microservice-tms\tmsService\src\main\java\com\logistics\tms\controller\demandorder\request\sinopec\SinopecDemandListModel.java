package com.logistics.tms.controller.demandorder.request.sinopec;

import com.yelo.tools.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/12
 */
@Data
public class SinopecDemandListModel {

	@ApiModelProperty("物流订单号 C26146961894163")
	private String logisticsOrderNumber;

	@ApiModelProperty("商流订单号 2614696189")
	private String businessOrderNumber;

	@ApiModelProperty("订单有效期 ")
	private Long businessOrderValidEnd;

	@ApiModelProperty("凭证日期 ")
	private Long certificateTime;

	@ApiModelProperty("物料形态 1-固体 2-液体 3-气体")
	private Integer existState;

	@ApiModelProperty("物料性质 1-危化品 2-普通")
	private Integer materialAttribute;

	@ApiModelProperty("是否易制毒 0-否，1-是")
	private Integer makeDrug;

	@ApiModelProperty("是否支持提货身份验证 0-否，1-是")
	private Integer supportAuthOnline;

	@ApiModelProperty("包装方式（物料运输组） 共享托盘")
	private String packingName;

	@ApiModelProperty("包装规格 1.5吨/托")
	private String packingSpecification;

	@ApiModelProperty("生产企业")
	private SinopecManufacturerModel manufacturer;

	@ApiModelProperty("批次号")
	private String batch;

	@ApiModelProperty("物料大类 合成树脂")
	private String materialMainCategory;

	@ApiModelProperty("物料大类编码 H11")
	private String materialMainCategoryCode;

	@ApiModelProperty("物料小类 高密度聚乙烯")
	private String materialCategory;

	@ApiModelProperty("物料小类编码 C12")
	private String materialCategoryCode;

	@ApiModelProperty("物料名称 高密度聚乙烯(HD5502)")
	private String materialName;

	@ApiModelProperty("物料编码 001201060060508331")
	private String materialCode;

	@ApiModelProperty("委托数量")
	private BigDecimal associatedCount;

	@ApiModelProperty("单位ID")
	private Integer unitId;

	@ApiModelProperty("单位名称 吨")
	private String unitName;

	@ApiModelProperty("客户编码")
	private String clientCode;

	@ApiModelProperty("客户名称")
	private String clientName;

	@ApiModelProperty("运输单价")
	private BigDecimal transportPrice;

	@ApiModelProperty("预估运费")
	private BigDecimal estimateTransportFee;

	@ApiModelProperty("装货人类型 1-生产企业 2-仓库")
	private Integer freighterType;

	@ApiModelProperty("装货人编码")
	private String freighterCode;

	@ApiModelProperty("装货人 镇海炼化")
	private String freighterName;

	@ApiModelProperty("起运地/港/站信息")
	private SinopecLocationModel originPortLocation;

	@ApiModelProperty("送达地/港/站信息")
	private SinopecLocationModel destinationPortLocation;

	@ApiModelProperty("收货人编码")
	private String receiverCode;

	@ApiModelProperty("收货人名称")
	private String receiverName;

	@ApiModelProperty("收货人类型")
	private Integer receiverType;

	@ApiModelProperty("收货联系方式")
	private String receiverPhone;

	@ApiModelProperty("其他费用")
	private BigDecimal otherFee;

	@ApiModelProperty("预留字段")
	private String remark1;

	@ApiModelProperty("预留字段")
	private String remark2;

	@ApiModelProperty("预留字段")
	private String remark3;

	@ApiModelProperty("预留字段")
	private String remark4;

	@ApiModelProperty("预留字段")
	private String remark5;

	@ApiModelProperty("预留字段")
	private String remark6;

	@ApiModelProperty("预留字段")
	private String remark7;

	@ApiModelProperty("预留字段")
	private String remark8;

	@ApiModelProperty("预留字段")
	private String remark9;

	@ApiModelProperty("预留字段")
	private String remark10;

	/**
	 * 校验参数
	 *
	 * @return 校验结果
	 */
	public String check() {
		if (StringUtils.isBlank(this.logisticsOrderNumber)) {
			return "物流订单号不能为空";
		}
		if (StringUtils.isBlank(this.businessOrderNumber)) {
			return "商流订单号不能为空";
		}
		if (this.businessOrderValidEnd == null) {
			return "订单有效期不能为空";
		}
		if (this.certificateTime == null) {
			return "凭证日期不能为空";
		}
		if (this.existState == null) {
			return "物料形态不能为空";
		}
		if (this.materialAttribute == null) {
			return "物料性质不能为空";
		}
		if (this.makeDrug == null) {
			return "是否易制毒不能为空";
		}
		if (this.supportAuthOnline == null) {
			return "是否支持提货身份验证不能为空";
		}

		if (this.manufacturer == null) {
			return "生产企业不能为空";
		} else {
			SinopecManufacturerModel manufacturerScope = this.manufacturer;
			String checkMsg = manufacturerScope.check();
			if (checkMsg != null) {
				return checkMsg;
			}
		}

		if (StringUtils.isBlank(this.materialMainCategory)) {
			return "物料大类不能为空";
		}
		if (StringUtils.isBlank(this.materialMainCategoryCode)) {
			return "物料大类编码不能为空";
		}
		if (StringUtils.isBlank(this.materialCategory)) {
			return "物料小类不能为空";
		}
		if (StringUtils.isBlank(this.materialCategoryCode)) {
			return "物料小类编码不能为空";
		}
		if (StringUtils.isBlank(this.materialName)) {
			return "物料名称不能为空";
		}
		if (StringUtils.isBlank(this.materialCode)) {
			return "物料编码不能为空";
		}
		if (this.associatedCount == null) {
			return "委托数量不能为空";
		}
		if (this.unitId == null) {
			return "单位ID不能为空";
		}
		if (StringUtils.isBlank(this.unitName)) {
			return "单位名称不能为空";
		}
		if (this.freighterType == null) {
			return "装货人类型不能为空";
		}
		if (StringUtils.isBlank(this.freighterCode)) {
			return "装货人编码不能为空";
		}
		if (StringUtils.isBlank(this.freighterName)) {
			return "装货人编码不能为空";
		}
		if (StringUtils.isBlank(this.receiverCode)) {
			return "收货人编码不能为空";
		}
		if (StringUtils.isBlank(this.receiverName)) {
			return "收货人名称不能为空";
		}
		if (receiverType == null) {
			return "收货人类型不能为空";
		}

		//长度校验
		if (this.logisticsOrderNumber.length() > 64) {
			return "物流订单号长度不能大于64个字符";
		}
		if (this.businessOrderNumber.length() > 64) {
			return "商流订单号长度不能大于64个字符";
		}
		if (this.packingName != null && this.packingName.length() > 64) {
			return "包装方式长度不能大于64个字符";
		}
		if (this.packingSpecification != null && this.packingSpecification.length() > 255) {
			return "包装规格长度不能大于255个字符";
		}
		if (this.batch != null && this.batch.length() > 64) {
			return "批次号长度不能大于64个字符";
		}
		if (this.materialMainCategory.length() > 64) {
			return "物料大类长度不能大于64个字符";
		}
		if (this.materialMainCategoryCode.length() > 32) {
			return "物料大类编码长度不能大于32个字符";
		}
		if (this.materialCategory.length() > 64) {
			return "物料小类长度不能大于64个字符";
		}
		if (this.materialCategoryCode.length() > 32) {
			return "物料小类编码长度不能大于32个字符";
		}
		if (this.materialName.length() > 255) {
			return "物料名称长度不能大于255个字符";
		}
		if (this.materialCode.length() > 255) {
			return "物料编码长度不能大于255个字符";
		}
		if (this.unitName.length() > 16) {
			return "单位名称长度不能大于16个字符";
		}
		if (this.clientCode != null && this.clientCode.length() > 16) {
			return "客户编码长度不能大于16个字符";
		}
		if (this.clientName != null && this.clientName.length() > 64) {
			return "客户名称长度不能大于64个字符";
		}
		if (this.freighterCode != null && this.freighterCode.length() > 64) {
			return "装货人编码长度不能大于64个字符";
		}
		if (this.freighterName != null && this.freighterName.length() > 64) {
			return "装货人长度不能大于64个字符";
		}
		if (this.receiverCode != null && this.receiverCode.length() > 64) {
			return "收货人编码长度不能大于64个字符";
		}
		if (this.receiverName != null && this.receiverName.length() > 64) {
			return "收货人名称长度不能大于64个字符";
		}
		if (this.receiverPhone != null && this.receiverPhone.length() > 32) {
			return "收货联系方式长度不能大于32个字符";
		}
		if (this.originPortLocation != null) {
			if (this.originPortLocation.getLocationCode() != null && this.originPortLocation.getLocationCode().length() > 64) {
				return "起运地/港/站位置编码长度不能大于64个字符";
			}
			if (this.originPortLocation.getLocationName() != null && this.originPortLocation.getLocationName().length() > 200) {
				return "起运地/港/站位置名称长度不能大于200个字符";
			}
			if (this.originPortLocation.getDetailAddress() != null && this.originPortLocation.getDetailAddress().length() > 200) {
				return "起运地/港/站详细地址长度不能大于200个字符";
			}
		}
		if (this.destinationPortLocation != null) {
			if (this.destinationPortLocation.getLocationCode() != null && this.destinationPortLocation.getLocationCode().length() > 64) {
				return "送达地/港/站位置编码长度不能大于64个字符";
			}
			if (this.destinationPortLocation.getLocationName() != null && this.destinationPortLocation.getLocationName().length() > 200) {
				return "送达地/港/站位置名称长度不能大于200个字符";
			}
			if (this.destinationPortLocation.getDetailAddress() != null && this.destinationPortLocation.getDetailAddress().length() > 200) {
				return "送达地/港/站详细地址长度不能大于200个字符";
			}
		}
		return null;
	}
}
