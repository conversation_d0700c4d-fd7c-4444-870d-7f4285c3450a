package com.logistics.management.webapi.controller.demandorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.demandorder.response.GetPublishDemandOrderDetailResponseModel;
import com.logistics.management.webapi.controller.demandorder.response.DemandOrderGoodsBaseInfoResponseDto;
import com.logistics.management.webapi.controller.demandorder.response.GetPublishDemandOrderDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;


public class GetPublishDemandOrderDetailMapping extends MapperMapping<GetPublishDemandOrderDetailResponseModel,GetPublishDemandOrderDetailResponseDto> {
    @Override
    public void configure() {
        GetPublishDemandOrderDetailResponseModel source = getSource();
        GetPublishDemandOrderDetailResponseDto destination = getDestination();
        if (source == null) {
            return;
        }
        if (ListUtils.isNotEmpty(destination.getDemandOrderGoodsList())) {
            for (DemandOrderGoodsBaseInfoResponseDto demandOrderGoodsResponseDto : destination.getDemandOrderGoodsList()) {
                if (ConverterUtils.toInt(demandOrderGoodsResponseDto.getLength()) > 0
                        || ConverterUtils.toInt(demandOrderGoodsResponseDto.getWidth()) > 0
                        || ConverterUtils.toInt(demandOrderGoodsResponseDto.getHeight()) > 0) {
                    demandOrderGoodsResponseDto.setGoodsSize(demandOrderGoodsResponseDto.getLength() + "*" + demandOrderGoodsResponseDto.getWidth() + "*" + demandOrderGoodsResponseDto.getHeight() + "mm");
                }
                demandOrderGoodsResponseDto.setGoodsAmount(ConverterUtils.toBigDecimal(demandOrderGoodsResponseDto.getGoodsAmount()).stripTrailingZeros().toPlainString());
            }
        }

        //我司情况下
        if (CommonConstant.INTEGER_ONE.equals(source.getIsOurCompany())) {
            destination.setCarrierPrice("");
            destination.setCarrierPriceType("");
            destination.setCompanyCarrierId("");
            destination.setCompanyCarrierType("");
            destination.setCompanyCarrierName("");
            destination.setCompanyCarrierContactName("");
            destination.setCompanyCarrierContactPhone("");
            destination.setCarrierContactId("");
        }
    }
}
