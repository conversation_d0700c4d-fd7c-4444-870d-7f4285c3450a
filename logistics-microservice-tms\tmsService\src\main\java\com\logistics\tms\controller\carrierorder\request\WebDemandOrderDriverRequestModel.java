package com.logistics.tms.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class WebDemandOrderDriverRequestModel {
    @ApiModelProperty("司机名字")
    private String driverName;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机联系方式")
    private String drivePhone;
    @ApiModelProperty("公司id")
    private Long companyCarrierId;
}
