package com.logistics.tms.controller.biddingorder.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/04/26
 */
@Data
public class QuoteRequestModel {

    /**
     *竞价单id
     */
    private Long biddingOrderId;

    /**
     *竞价金额类型：1 单价，2 一口价
     */
    private Integer biddingPriceType;

    /**
     *报价金额
     */
    private BigDecimal biddingPrice;


    /**
     *车长id
     */
    private Long vehicleLengthId;

    /**
     *车长
     */
    private BigDecimal vehicleLength;
}
