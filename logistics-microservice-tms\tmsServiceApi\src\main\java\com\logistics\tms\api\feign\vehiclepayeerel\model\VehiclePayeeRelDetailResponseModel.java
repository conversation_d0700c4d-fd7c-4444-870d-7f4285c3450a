package com.logistics.tms.api.feign.vehiclepayeerel.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/7/11 14:10
 */
@Data
public class VehiclePayeeRelDetailResponseModel {
    @ApiModelProperty("id")
    private Long vehiclePayeeRelId;
    @ApiModelProperty("车辆id")
    private Long vehicleId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机收款账号id")
    private Long driverPayeeId;
    @ApiModelProperty("关联账户：姓名")
    private String name;
    @ApiModelProperty("关联账户：手机号")
    private String mobile;
    @ApiModelProperty("关联账户：银行名称")
    private String bankName;
    @ApiModelProperty("关联账户：银行账号")
    private String bankCardNo;
    @ApiModelProperty("备注")
    private String remark;
}
