package com.logistics.tms.controller.drivercostapply.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/7
 */
@Data
public class SearchCostApplyListForAppletRequestModel extends AbstractPageForm<SearchCostApplyListForAppletRequestModel> {

	@ApiModelProperty(value = "司机ID")
	private Long staffId;

	@ApiModelProperty(value = "申请时间（yyyy-MM）")
	private String applyTime;
}
