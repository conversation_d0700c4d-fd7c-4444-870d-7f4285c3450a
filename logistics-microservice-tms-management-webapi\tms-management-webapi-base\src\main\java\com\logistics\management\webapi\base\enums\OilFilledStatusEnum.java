package com.logistics.management.webapi.base.enums;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/10/9
 * @description:
 */
public enum OilFilledStatusEnum {

    WAIT_SETTLE(0, "待结算"),
    HAVE_SETTLE(1, "已结算"),

    DEFAULT(-999,""),

    ;


    private Integer key;
    private String value;

    OilFilledStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static OilFilledStatusEnum getEnum(Integer key) {
        for (OilFilledStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }


}
