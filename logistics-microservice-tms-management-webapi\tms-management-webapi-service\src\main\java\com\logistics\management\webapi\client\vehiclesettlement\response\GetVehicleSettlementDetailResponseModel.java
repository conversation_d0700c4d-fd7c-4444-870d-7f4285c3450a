package com.logistics.management.webapi.client.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/14 9:41
 */
@Data
public class GetVehicleSettlementDetailResponseModel {
    @ApiModelProperty("对账单事件列表")
    private List<VehicleSettlementEventModel> eventList;

    private Long vehicleSettlementId;
    @ApiModelProperty("结算状态：0 待结算，1 已结算")
    private Integer status;
    private String settlementMonth;
    @ApiModelProperty("车牌号")
    private Long vehicleId;
    private String vehicleNo;

    @ApiModelProperty("运单")
    private List<GetCarrierOrderByVehicleIdResponseModel> carrierOrderList;
    @ApiModelProperty("是否调整费用：0 否，1 是")
    private Integer ifAdjustFee;
    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;
    @ApiModelProperty("调整原因")
    private String adjustRemark;

    @ApiModelProperty("轮胎")
    private List<GetVehicleTireByVehicleIdResponseModel> vehicleTireList;

    @ApiModelProperty("充油")
    private List<GetOilFilledByVehicleIdResponseModel> oilFilledList;


    @ApiModelProperty("gps")
    private GetGpsFeeByVehicleIdResponseModel gpsFeeModel;

    @ApiModelProperty("停车")
    private GetParkingFeeByVehicleIdResponseModel parkingFeeModel;

    @ApiModelProperty("保险")
    private List<VehicleInsuranceCostResponseModel> insuranceCostList;
    @ApiModelProperty("车辆理赔费用")
    private BigDecimal vehicleClaimFee;


    @ApiModelProperty("个人意外险费用合计")
    private BigDecimal accidentInsuranceExpenseTotal;
    @ApiModelProperty("应扣个人意外险费")
    private BigDecimal accidentInsuranceFee;
    @ApiModelProperty("个人意外险费月理赔费用")
    private BigDecimal accidentInsuranceClaimFee;

    @ApiModelProperty("贷款")
    private GetLoanFeeByVehicleIdResponseModel loanFeeModel;

    @ApiModelProperty("扣减费用合计")
    private BigDecimal deductingFeeTotal;
    @ApiModelProperty("剩余未扣费用合计")
    private BigDecimal remainingDeductingFeeTotal;

    @ApiModelProperty("月实际应付运费")
    private BigDecimal actualExpensesPayable;
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("附件相对路径")
    private String attachment;
}
