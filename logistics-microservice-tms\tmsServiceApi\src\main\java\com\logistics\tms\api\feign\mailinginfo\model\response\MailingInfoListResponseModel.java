package com.logistics.tms.api.feign.mailinginfo.model.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class MailingInfoListResponseModel {

    @ApiModelProperty(value = "邮寄地址")
    private String mailingInfoId;

    @ApiModelProperty("收货地址")
    private String address;

    @ApiModelProperty("收货联系人")
    private String addressee;

    @ApiModelProperty("收货联系人手机号")
    private String addresseeMobile;

    @ApiModelProperty("应用场景")
    private String applyScope;

    @ApiModelProperty("操作人")
    private String lastModifiedBy;

    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;
}
