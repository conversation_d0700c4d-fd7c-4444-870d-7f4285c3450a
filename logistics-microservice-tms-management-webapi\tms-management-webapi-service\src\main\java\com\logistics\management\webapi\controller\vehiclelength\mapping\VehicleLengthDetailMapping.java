package com.logistics.management.webapi.controller.vehiclelength.mapping;

import com.logistics.management.webapi.client.vehiclelength.response.VehicleLengthDetailResponseModel;
import com.logistics.management.webapi.controller.vehiclelength.response.VehicleLengthDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2024/4/29 9:25
 */
public class VehicleLengthDetailMapping extends MapperMapping<VehicleLengthDetailResponseModel, VehicleLengthDetailResponseDto> {
    @Override
    public void configure() {
        VehicleLengthDetailResponseModel source = getSource();
        VehicleLengthDetailResponseDto destination = getDestination();

        destination.setVehicleLength(source.getVehicleLength().stripTrailingZeros().toPlainString());
        destination.setCarriageScopeMin(source.getCarriageScopeMin().stripTrailingZeros().toPlainString());
        destination.setCarriageScopeMax(source.getCarriageScopeMax().stripTrailingZeros().toPlainString());
    }
}
