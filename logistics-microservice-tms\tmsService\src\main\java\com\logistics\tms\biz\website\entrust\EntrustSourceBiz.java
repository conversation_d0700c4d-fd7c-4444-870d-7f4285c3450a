package com.logistics.tms.biz.website.entrust;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.website.entrust.request.AddEntrustSourceRequestModel;
import com.logistics.tms.entity.TEntrustSource;
import com.logistics.tms.mapper.TEntrustSourceMapper;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * @author: wjf
 * @date: 2019/10/31 16:40
 */
@Service
public class EntrustSourceBiz {

    @Resource
    private TEntrustSourceMapper entrustSourceMapper;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 成为货主
     * @param requestModel
     */
    @Transactional
    public void addEntrustSource(AddEntrustSourceRequestModel requestModel){
        TEntrustSource tEntrustSource = entrustSourceMapper.getByMobile(requestModel.getContactMobile());
        if (tEntrustSource != null){
            throw new BizException(CarrierDataExceptionEnum.ENTRUST_SOURCE_EXIST);
        }
        TEntrustSource entrustSource = MapperUtils.mapper(requestModel,TEntrustSource.class);
        commonBiz.setBaseEntityAdd(entrustSource, CommonConstant.WEBSITE);
        entrustSourceMapper.insertSelective(entrustSource);
    }
}
