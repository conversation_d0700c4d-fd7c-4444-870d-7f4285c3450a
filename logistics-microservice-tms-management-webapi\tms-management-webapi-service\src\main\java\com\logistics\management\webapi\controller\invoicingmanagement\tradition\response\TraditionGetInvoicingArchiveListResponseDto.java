package com.logistics.management.webapi.controller.invoicingmanagement.tradition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/6/25 11:28
 */
@Data
public class TraditionGetInvoicingArchiveListResponseDto {
    /**
     * 发票归档文件id
     */
    @ApiModelProperty("发票归档文件id")
    private String invoicingArchiveId="";

    /**
     * 图片绝对路径
     */
    @ApiModelProperty("图片绝对路径")
    private String absolutePath="";

    /**
     * 图片相对路径
     */
    @ApiModelProperty("图片相对路径")
    private String relativePath="";
}
