package com.logistics.management.webapi.api.impl.dispatch;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.dispatch.DispatchApi;
import com.logistics.management.webapi.api.feign.dispatch.dto.*;
import com.logistics.management.webapi.api.impl.dispatch.mapping.DispatchDetailMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.tms.api.feign.dispatch.DispatchServiceApi;
import com.logistics.tms.api.feign.dispatch.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;


@Slf4j
@RestController
public class DispatchApiImpl implements DispatchApi {

    @Autowired
    private DispatchServiceApi dispatchServiceApi;

    /**
     * 调度车辆-搜索司机&车牌
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<DriverAndVehicleSearchResponseDto>> searchDriverAndVehicle(@RequestBody @Valid DriverAndVehicleSearchRequestDto requestDto) {
        DriverAndVehicleSearchRequestModel model = MapperUtils.mapper(requestDto, DriverAndVehicleSearchRequestModel.class);
        Result<List<DriverAndVehicleSearchResponseModel>> listResult = dispatchServiceApi.searchDriverAndVehicle(model);
        if (listResult.isSuccess()) {
            return Result.success(MapperUtils.mapper(listResult.getData(), DriverAndVehicleSearchResponseDto.class));
        } else {
            return Result.success(new ArrayList<>());
        }
    }

    /**
     * 调度车辆-调度车辆完成调度查看详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<DemandOrderDispatchResponseDto>> getDispatchDetail(@RequestBody @Valid DemandOrderDispatchRequestDto requestDto) {
        DemandOrderDispatchRequestModel webDemandOrderDispatchRequestModel = MapperUtils.mapper(requestDto, DemandOrderDispatchRequestModel.class);
        Result<List<DemandOrderDispatchResponseModel>> listResult = dispatchServiceApi.getDispatchDetail(webDemandOrderDispatchRequestModel);
        listResult.throwException();
        List<DemandOrderDispatchResponseDto> dtoList = new ArrayList();
        if (ListUtils.isNotEmpty(listResult.getData())) {
            dtoList = MapperUtils.mapper(listResult.getData(), DemandOrderDispatchResponseDto.class, new DispatchDetailMapping());
        }
        return Result.success(dtoList);
    }

    /**
     * 调度车辆-完成调度
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> saveCompleteDispatch(@RequestBody @Valid CompleteDemandOrderRequestDto requestDto) {
        CompleteDemandOrderRequestModel requestModel = MapperUtils.mapper(requestDto, CompleteDemandOrderRequestModel.class);
        Result<Boolean> booleanResult = dispatchServiceApi.saveCompleteDispatch(requestModel);
        booleanResult.throwException();
        return Result.success(true);
    }
    /**
     * 调度车辆-分页搜索司机
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<DriverSearchResponseDto>> searchDriver(@RequestBody @Valid DriverSearchRequestDto requestDto) {
        Result<PageInfo<DriverSearchResponseModel>> result = dispatchServiceApi.searchDriver(MapperUtils.mapper(requestDto, DriverSearchRequestModel.class));
        result.throwException();
        PageInfo dtoPageInfo = result.getData();
        List<DriverSearchResponseDto> dtoList =MapperUtils.mapper(result.getData().getList(),DriverSearchResponseDto.class);
        dtoPageInfo.setList(dtoList == null ? new ArrayList() : dtoList);
        return Result.success(dtoPageInfo);
    }
    /**
     * 调度车辆-分页搜索车辆
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<VehicleSearchResponseDto>> searchVehicle(VehicleSearchRequestDto requestDto) {
        Result<PageInfo<VehicleSearchResponseModel>> result = dispatchServiceApi.searchVehicle(MapperUtils.mapper(requestDto, VehicleSearchRequestModel.class));
        result.throwException();
        PageInfo dtoPageInfo = result.getData();
        List<VehicleSearchResponseDto> dtoList =MapperUtils.mapper(result.getData().getList(),VehicleSearchResponseDto.class);
        dtoPageInfo.setList(dtoList == null ? new ArrayList() : dtoList);
        return Result.success(dtoPageInfo);
    }
}
