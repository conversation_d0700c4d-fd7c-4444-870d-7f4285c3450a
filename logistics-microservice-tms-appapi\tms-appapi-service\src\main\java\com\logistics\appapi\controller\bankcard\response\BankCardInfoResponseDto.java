package com.logistics.appapi.controller.bankcard.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Data
public class BankCardInfoResponseDto {

	@ApiModelProperty(value = "司机账户ID")
	private String driverAccountId;

	@ApiModelProperty(value = "银行名称")
	private String bankAccountName = "";

	@ApiModelProperty(value = "银行账号")
	private String bankAccount = "";
}
