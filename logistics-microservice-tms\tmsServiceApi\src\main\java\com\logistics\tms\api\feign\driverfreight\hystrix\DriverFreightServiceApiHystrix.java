package com.logistics.tms.api.feign.driverfreight.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.driverfreight.DriverFreightServiceApi;
import com.logistics.tms.api.feign.driverfreight.model.DriverFreightListSearchRequestModel;
import com.logistics.tms.api.feign.driverfreight.model.DriverFreightListSearchResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;


@Component
public class DriverFreightServiceApiHystrix implements DriverFreightServiceApi {


    @Override
    public Result<PageInfo<DriverFreightListSearchResponseModel>> driverFreightList(DriverFreightListSearchRequestModel requestDto) {
        return Result.timeout();
    }
}
