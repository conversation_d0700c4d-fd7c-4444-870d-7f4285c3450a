package com.logistics.appapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2023/5/5 13:28
 */
@Data
public class PrintBillDetailResponseDto {

    //运单信息
    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";

    @ApiModelProperty("预提数(带单位)")
    private String expectAmount = "";

    @ApiModelProperty("实体数(带单位)")
    private String loadAmount = "";


    //运单地址信息
    //提货信息（提货地址前端拿当前定位）
    @ApiModelProperty("发货联系人")
    private String loadPerson = "";
    @ApiModelProperty("发货联系方式")
    private String loadMobile = "";

    //卸货信息
    @ApiModelProperty("收货联系人")
    private String unloadPerson = "";
    @ApiModelProperty("收货联系方式")
    private String unloadMobile = "";

    @ApiModelProperty("发货仓库")
    private String loadWarehouse = "";

    @ApiModelProperty("收货地址")
    private String unloadAddress = "";

    //司机信息
    @ApiModelProperty("车牌号")
    private String vehicleNo = "";
    @ApiModelProperty("司机名称")
    private String driverName = "";
    @ApiModelProperty("司机电话")
    private String driverMobile = "";


    @ApiModelProperty("二维码内容")
    private String qrCodeContent = "";
}
