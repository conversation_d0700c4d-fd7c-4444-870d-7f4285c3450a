package com.logistics.tms.api.feign.traycost.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/4/20 17:19
 */
@Data
public class SearchTrayCostListResponseModel {
    @ApiModelProperty("托盘费用id")
    private Long trayCostId;
    @ApiModelProperty("委托类型：1 发货，2 回收，3 采购，4 调拨")
    private Integer entrustType;
    @ApiModelProperty("单位：1 件，2 吨，3 件（方），4 块")
    private Integer goodsUnit;
    @ApiModelProperty("价格")
    private BigDecimal unitPrice;
}
