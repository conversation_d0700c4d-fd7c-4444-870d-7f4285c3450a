package com.logistics.tms.controller.vehiclesafecheck.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/11/13 18:39
 */
@Data
public class SafeCheckBoardResponseModel {
    @ApiModelProperty("检查周期")
    private String period;
    @ApiModelProperty("车辆数")
    private Integer vehicleCount;
    @ApiModelProperty("自主车辆数(此次检查的)")
    private Integer ownVehicleCount;
    @ApiModelProperty("自营车辆数(此次检查的)")
    private Integer affiliationVehicleCount;
    @ApiModelProperty("检查车数量")
    private Integer checkVehicleCount;
    @ApiModelProperty("检查项数量")
    private Integer checkItemCount;
    @ApiModelProperty("整改项数量")
    private Integer reformCount;
}
