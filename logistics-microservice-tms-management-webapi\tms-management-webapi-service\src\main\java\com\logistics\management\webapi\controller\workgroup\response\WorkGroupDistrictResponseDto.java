package com.logistics.management.webapi.controller.workgroup.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WorkGroupDistrictResponseDto {

    @ApiModelProperty("区域配置id")
    private String workGroupDistrictId = "";

    @ApiModelProperty(value = "省份id")
    private String provinceId = "";

    @ApiModelProperty(value = "省份名")
    private String provinceName = "";

    @ApiModelProperty(value = "市id")
    private String cityId = "";

    @ApiModelProperty(value = "市名")
    private String cityName = "";
}
