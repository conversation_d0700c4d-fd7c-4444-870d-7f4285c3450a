package com.logistics.tms.biz.demandorder


import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.biz.demandorder.model.GetRegionInfoByCityIdModel
import com.logistics.tms.client.BasicDataClient
import com.logistics.tms.client.WarehouseStockClient
import com.logistics.tms.controller.carrierorder.response.DemandCarrierOrderRecursiveModel
import com.logistics.tms.controller.demandorder.request.*
import com.logistics.tms.controller.demandorder.response.*
import com.logistics.tms.entity.*
import com.logistics.tms.mapper.*
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz
import com.yelo.tools.rabbitmq.producer.RabbitMqSender
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class DemandOrderForLeYiBizTest extends Specification {
    @Mock
    TDemandOrderMapper tDemandOrderMapper
    @Mock
    TDemandOrderAddressMapper tDemandOrderAddressMapper
    @Mock
    TDemandOrderOperateLogsMapper tDemandOrderOperateLogsMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TCompanyCarrierMapper tCompanyCarrierMapper
    @Mock
    DemandOrderCommonBiz demandOrderCommonBiz
    @Mock
    TDemandOrderGoodsMapper tDemandOrderGoodsMapper
    @Mock
    TDemandOrderOrderRelMapper tDemandOrderOrderRelMapper
    @Mock
    CarrierOrderCommonBiz carrierOrderCommonBiz
    @Mock
    TCarrierOrderMapper tCarrierOrderMapper
    @Mock
    DemandOrderBiz demandOrderBiz
    @Mock
    RabbitMqPublishBiz rabbitMqPublishBiz
    @Mock
    WarehouseStockClient warehouseStockClient
    @Mock
    TDemandOrderObjectionMapper tDemandOrderObjectionMapper
    @Mock
    TRegionMapper tRegionMapper
    @Mock
    BasicDataClient basicDataClient
    @Mock
    TDemandOrderEventsMapper tDemandOrderEventsMapper
    @Mock
    RabbitMqSender rabbitMqSender
    @Mock
    Logger log
    @InjectMocks
    DemandOrderForLeYiBiz demandOrderForLeYiBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "publish Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tDemandOrderMapper.publishDetail(anyString())).thenReturn([new BatchPublishDetailResponseModel()])

        expect:
        demandOrderForLeYiBiz.publishDetail(requestModel) == expectedResult

        where:
        requestModel                         || expectedResult
        new BatchPublishDetailRequestModel() || [new BatchPublishDetailResponseModel()]
    }

    @Unroll
    def "confirm Publish where requestModel=#requestModel"() {
        given:
        when(tDemandOrderMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(tDemandOrderMapper.publishDetail(anyString())).thenReturn([new BatchPublishDetailResponseModel()])
        when(tDemandOrderAddressMapper.batchUpdateSelective(any())).thenReturn(0)
        when(tDemandOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(commonBiz.getQiyaCompanyName()).thenReturn("getQiyaCompanyNameResponse")
        when(tCompanyCarrierMapper.getByName(anyString())).thenReturn(new TCompanyCarrier())
        when(tRegionMapper.getEnableRegionInfoByDemandIds(anyString())).thenReturn([new GetRegionInfoByCityIdModel()])

        expect:
        demandOrderForLeYiBiz.confirmPublish(requestModel)
        assert expectedResult == false

        where:
        requestModel                   || expectedResult
        new BatchPublishRequestModel() || true
    }

    @Unroll
    def "search List For Le Yi where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tDemandOrderMapper.searchListForLeYiManageAddressGoodsDemand(any(), anyString())).thenReturn([new DemandOrderForLeYiResponseModel()])
        when(tDemandOrderMapper.searchListForLeYiManageIds(any())).thenReturn([1l])
        when(tDemandOrderOrderRelMapper.getDemandOrderOrderRelByDemandIds(anyString())).thenReturn([new TDemandOrderOrderRel(demandOrderId: 1l, remark: "remark")])
        when(demandOrderBiz.getDemandActualFee(any())).thenReturn([(1l): 0 as BigDecimal])

        expect:
        demandOrderForLeYiBiz.searchListForLeYi(requestModel) == expectedResult

        where:
        requestModel                               || expectedResult
        new DemandOrderSearchForLeYiRequestModel() || null
    }

    @Unroll
    def "search YP Warehouse where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(warehouseStockClient.listWarehouseByName(anyString())).thenReturn([null])

        expect:
        demandOrderForLeYiBiz.searchYPWarehouse(requestModel) == expectedResult

        where:
        requestModel                                || expectedResult
        new SearchDemandUnLoadAddressRequestModel() || [new SearchDemandUnLoadAddressResponseModel()]
    }

    @Unroll
    def "confirm Empty where requestModel=#requestModel"() {
        given:
        when(tDemandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(entrustStatus: 0, ifCancel: 0, demandOrderCode: "demandOrderCode", notArrangedAmount: 0 as BigDecimal, backAmount: 0 as BigDecimal, entrustType: 0, ifEmpty: 0)])
        when(tDemandOrderMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(tDemandOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(tDemandOrderGoodsMapper.getDemandOrderGoodsByDemandOrderIds(anyString())).thenReturn([new TDemandOrderGoods(notArrangedAmount: 0 as BigDecimal, backAmount: 0 as BigDecimal)])
        when(tDemandOrderGoodsMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(tDemandOrderOrderRelMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(tDemandOrderOrderRelMapper.getDemandOrderOrderRelByDemandIds(anyString())).thenReturn([new TDemandOrderOrderRel(demandOrderId: 1l, orderId: 1l, totalAmount: 0 as BigDecimal, arrangedAmount: 0 as BigDecimal, backAmount: 0 as BigDecimal)])
        when(tCarrierOrderMapper.getByDemandOrderIds(anyString())).thenReturn([new DemandCarrierOrderRecursiveModel()])
        when(tDemandOrderObjectionMapper.batchInsert(any())).thenReturn(0)
        when(tDemandOrderObjectionMapper.batchUpdate(any())).thenReturn(0)
        when(tDemandOrderObjectionMapper.getByDemandOrderIds(anyString())).thenReturn([new TDemandOrderObjection(demandOrderId: 1l, customerName: "customerName", objectionType: 0, objectionReason: "objectionReason", reportContactName: "reportContactName", reportTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 13).getTime())])
        when(tDemandOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)

        expect:
        demandOrderForLeYiBiz.confirmEmpty(requestModel)
        assert expectedResult == false

        where:
        requestModel                       || expectedResult
        new DemandOrderEmptyRequestModel() || true
    }

    @Unroll
    def "dispatch Alarm Statistics"() {
        given:
        when(tDemandOrderMapper.dispatchAlarmStatistics()).thenReturn([new DispatchAlarmStatisticsResponseModel()])
        when(tDemandOrderAddressMapper.dispatchAlarmStatisticsAddress(any())).thenReturn([1l])
        when(tDemandOrderAddressMapper.getByIds(anyString())).thenReturn([new TDemandOrderAddress(loadCityId: 1l, loadRegionContactName: "loadRegionContactName")])

        expect:
        demandOrderForLeYiBiz.dispatchAlarmStatistics() == expectedResult

        where:
        expectedResult << [new DispatchAlarmStatisticsResponseModel()]
    }

    @Unroll
    def "wait Dispatch Statistics"() {
        given:
        when(tDemandOrderMapper.waitDispatchStatistics()).thenReturn([new WaitDispatchStatisticsModel()])

        expect:
        demandOrderForLeYiBiz.waitDispatchStatistics() == expectedResult

        where:
        expectedResult << new WaitDispatchStatisticsResponseModel()
    }

    @Unroll
    def "aggregate Data Statistics"() {
        given:
        when(tDemandOrderMapper.selectDemandOrders()).thenReturn([new DemandOrderDataStatisticsResponseModel()])
        when(tCarrierOrderMapper.selectCorrectCarrierOrderCount()).thenReturn(new CorrectCarrierOrderDataStatisticsResponseModel())

        expect:
        demandOrderForLeYiBiz.aggregateDataStatistics() == expectedResult

        where:
        expectedResult << new AggregateDataStatisticsResponseModel()
    }

    @Unroll
    def "map Data Statistics"() {
        given:
        when(tDemandOrderMapper.mapDataStatistics()).thenReturn([new MapDataStatisticsResponseModel()])
        when(tCarrierOrderMapper.mapDataStatistics()).thenReturn([new MapDataStatisticsResponseModel()])
        when(basicDataClient.getLonLatByMapIds(any())).thenReturn([null])

        expect:
        demandOrderForLeYiBiz.mapDataStatistics() == expectedResult

        where:
        expectedResult << [new MapDataStatisticsResponseModel()]
    }

    @Unroll
    def "sync Additional Order From Leyi Tray where model=#model"() {
        given:
        when(tDemandOrderMapper.getByCode(anyString())).thenReturn(new TDemandOrder(entrustStatus: 0, ifCancel: 0, goodsAmount: 0 as BigDecimal, notArrangedAmount: 0 as BigDecimal, ifEmpty: 0))
        when(tDemandOrderGoodsMapper.getTDemandOrderGoodsByDemandId(anyLong())).thenReturn([new DemandOrderGoodsResponseModel()])
        when(tDemandOrderOrderRelMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(tDemandOrderOrderRelMapper.batchInsertDemandOrderOrderRelSelective(any())).thenReturn(0)
        when(tDemandOrderOrderRelMapper.getDemandOrderOrderRelByDemandIds(anyString())).thenReturn([new com.logistics.tms.entity.TDemandOrderOrderRel(demandOrderId: 1l, orderId: 1l, orderCode: "orderCode", totalAmount: 0 as java.math.BigDecimal, relType: 0, remark: "remark")])

        expect:
        demandOrderForLeYiBiz.syncAdditionalOrderFromLeyiTray(model)
        assert expectedResult == false

        where:
        model                                                                       || expectedResult
        new com.logistics.tms.rabbitmq.consumer.model.TmsSyncAdditionalOrderModel() || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme