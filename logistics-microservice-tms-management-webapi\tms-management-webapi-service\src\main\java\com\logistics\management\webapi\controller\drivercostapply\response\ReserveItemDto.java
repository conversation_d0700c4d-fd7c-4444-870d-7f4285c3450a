package com.logistics.management.webapi.controller.drivercostapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Data
public class ReserveItemDto {

	@ApiModelProperty("备用金单号")
	private String reserveCode;

	@ApiModelProperty("余额")
	private String balance;

	@ApiModelProperty("冲销金额")
	private String writeOffAmount;

	@ApiModelProperty("备用金类型: 1 申请，2 垫付，3 红冲退款; 1.3.6 新增")
	private String reserveType;

	@ApiModelProperty("备用金类型文本")
	private String reserveTypeLabel;
}
