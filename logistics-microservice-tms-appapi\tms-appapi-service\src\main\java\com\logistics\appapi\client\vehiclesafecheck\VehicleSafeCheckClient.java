package com.logistics.appapi.client.vehiclesafecheck;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.vehiclesafecheck.hystrix.VehicleSafeCheckClientHystrix;
import com.logistics.appapi.client.vehiclesafecheck.request.AppletAddWaitCheckRequestModel;
import com.logistics.appapi.client.vehiclesafecheck.request.AppletAddWaitReformRequestModel;
import com.logistics.appapi.client.vehiclesafecheck.request.AppletSafeCheckListRequestModel;
import com.logistics.appapi.client.vehiclesafecheck.request.SafeCheckDetailRequestModel;
import com.logistics.appapi.client.vehiclesafecheck.response.AppletSafeCheckListResponseModel;
import com.logistics.appapi.client.vehiclesafecheck.response.AppletSafeCheckSummaryResponseModel;
import com.logistics.appapi.client.vehiclesafecheck.response.SafeCheckDetailResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2024/3/15 9:28
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        fallback = VehicleSafeCheckClientHystrix.class)
public interface VehicleSafeCheckClient {

    @ApiOperation("列表")
    @PostMapping(value = "/service/applet/safeCheck/searchAppletList")
    Result<PageInfo<AppletSafeCheckListResponseModel>> searchAppletList(@RequestBody AppletSafeCheckListRequestModel requestModel);

    @ApiOperation("列表汇总")
    @PostMapping(value = "/service/applet/safeCheck/getAppletSummary")
    Result<AppletSafeCheckSummaryResponseModel> getAppletSummary(@RequestBody AppletSafeCheckListRequestModel requestModel);

    @ApiOperation("详情")
    @PostMapping(value = "/service/safeCheck/getDetail")
    Result<SafeCheckDetailResponseModel> getDetail(@RequestBody SafeCheckDetailRequestModel requestModel);

    @ApiOperation("待检查-提交")
    @PostMapping(value = "/service/applet/safeCheck/submitWaitCheck")
    Result<Boolean> submitWaitCheck(@RequestBody AppletAddWaitCheckRequestModel requestModel);

    @ApiOperation("待整改-提交")
    @PostMapping(value = "/service/applet/safeCheck/submitWaitReform")
    Result<Boolean> submitWaitReform(@RequestBody AppletAddWaitReformRequestModel requestModel);

}
