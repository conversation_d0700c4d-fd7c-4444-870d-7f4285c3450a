package com.logistics.tms.client;

import com.logistics.tms.controller.renewableaudit.request.SearchLifeSkuRequestModel;
import com.logistics.tms.controller.renewableaudit.response.SearchLifeSkuResponseModel;
import com.logistics.tms.controller.renewableaudit.request.SearchSkuDetailRequestModel;
import com.logistics.tms.controller.renewableaudit.response.SearchSkuDetailResponseModel;
import com.yelo.life.basicdata.api.fegin.address.CustomerAddressServiceApi;
import com.yelo.life.basicdata.api.fegin.address.model.GetCustomerAddressRequestModel;
import com.yelo.life.basicdata.api.fegin.address.model.GetCustomerAddressResponseModel;
import com.yelo.life.basicdata.api.fegin.customeraccount.CustomerAccountServiceApi;
import com.yelo.life.basicdata.api.fegin.customeraccount.model.GetCustomerAccountAddressRequestModel;
import com.yelo.life.basicdata.api.fegin.customeraccount.model.GetCustomerAccountAddressResponseModel;
import com.yelo.life.basicdata.api.fegin.sku.SkuServiceApi;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/23 17:54
 */
@Service
public class YeloLifeBasicDataClient {

    @Autowired
    private CustomerAccountServiceApi customerAccountServiceApi;
    @Autowired
    private CustomerAddressServiceApi customerAddressServiceApi;
    @Autowired
    private SkuServiceApi skuServiceApi;

    /**
     * 查询司机手机号在新生系统是否存在账号
     * @param requestModel
     * @return
     */
    public GetCustomerAccountAddressResponseModel getCustomerAccountAndAddress(GetCustomerAccountAddressRequestModel requestModel){
        Result<GetCustomerAccountAddressResponseModel> result = customerAccountServiceApi.getCustomerAccountAndAddress(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 模糊搜索新生客户地址
     * @param requestModel
     * @return
     */
    public List<GetCustomerAddressResponseModel> getCustomerAddress(GetCustomerAddressRequestModel requestModel){
        Result<List<GetCustomerAddressResponseModel>> result = customerAddressServiceApi.getCustomerAddress(requestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 获取sku详情数据，不分页
     *
     * @param requestModel
     * @return
     */
    public SearchSkuDetailResponseModel getLifeSkuDetail(SearchSkuDetailRequestModel requestModel) {
        com.yelo.life.basicdata.api.fegin.sku.model.SearchSkuDetailRequestModel searchSkuDetailRequestModel = MapperUtils.mapper(requestModel,
                com.yelo.life.basicdata.api.fegin.sku.model.SearchSkuDetailRequestModel.class);
        Result<com.yelo.life.basicdata.api.fegin.sku.model.SearchSkuDetailResponseModel> lifeSkuDetail = skuServiceApi.getLifeSkuDetail(searchSkuDetailRequestModel);
        lifeSkuDetail.throwException();
        return MapperUtils.mapper(lifeSkuDetail.getData(), SearchSkuDetailResponseModel.class);
    }

    /**
     * 获取sku列表数据，不分页
     *
     * @param requestModel
     * @return
     */
    public List<SearchLifeSkuResponseModel> getLifeSkuList(SearchLifeSkuRequestModel requestModel) {
        com.yelo.life.basicdata.api.fegin.sku.model.SearchLifeSkuRequestModel skuRequestModel = MapperUtils.mapper(requestModel,
                com.yelo.life.basicdata.api.fegin.sku.model.SearchLifeSkuRequestModel.class);
        Result<List<com.yelo.life.basicdata.api.fegin.sku.model.SearchLifeSkuResponseModel>> lifeSkuList = skuServiceApi.getLifeSkuList(skuRequestModel);
        lifeSkuList.throwException();
        return MapperUtils.mapper(lifeSkuList.getData(), SearchLifeSkuResponseModel.class);
    }
}
