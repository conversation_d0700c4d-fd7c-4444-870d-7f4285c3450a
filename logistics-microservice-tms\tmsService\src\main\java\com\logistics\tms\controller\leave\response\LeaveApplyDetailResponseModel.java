package com.logistics.tms.controller.leave.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class LeaveApplyDetailResponseModel {

    @ApiModelProperty("请假申请ID")
    private Long leaveApplyId;

    @ApiModelProperty("申请人姓名")
    private String staffName;

    @ApiModelProperty("申请人手机号")
    private String staffMobile;

    @ApiModelProperty("请假类型")
    private Integer leaveType;

    @ApiModelProperty("请假申请开始时间 (年-月-日)")
    private Date leaveStartTime;

    @ApiModelProperty("请假申请开始时间类型 1 上午 2 下午")
    private Integer leaveStartTimeType;

    @ApiModelProperty("请假申请结束时间 (年-月-日)")
    private Date leaveEndTime;

    @ApiModelProperty("请假申请结束时间类型 1 上午 2 下午")
    private Integer leaveEndTimeType;

    @ApiModelProperty("请假时长")
    private BigDecimal leaveDuration;

    @ApiModelProperty("请假原由")
    private String leaveReason;

    @ApiModelProperty("请假申请审核状态,审核状态: 1 已审核，2 已驳回，3 已撤销")
    private Integer leaveAuditStatus;

    @ApiModelProperty("审核人")
    private String auditorName;

    @ApiModelProperty("审核时间")
    private Date auditTime;

    @ApiModelProperty("审核备注/撤销备注")
    private String remark;
}
