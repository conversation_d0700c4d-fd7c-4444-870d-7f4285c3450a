package com.logistics.management.webapi.client.invoicingmanagement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/7/2 9:23
 */
@Data
public class UpdateBusinessNameRequestModel {

    /**
     * 发票管理id
     */
    @ApiModelProperty(value = "发票管理id")
    private Long invoicingId;

    /**
     * 业务名称
     */
    @ApiModelProperty(value = "业务名称")
    private String businessName;

    @ApiModelProperty("业务类型：1 包装业务，2 自营业务")
    private Integer businessType;

}
