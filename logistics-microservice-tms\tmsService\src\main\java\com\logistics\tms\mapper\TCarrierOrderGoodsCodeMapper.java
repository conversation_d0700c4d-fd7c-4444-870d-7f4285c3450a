package com.logistics.tms.mapper;

import com.logistics.tms.entity.TCarrierOrderGoodsCode;
import com.logistics.tms.entity.TCarrierOrderOperateLogs;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/10/24
*/
@Mapper
public interface TCarrierOrderGoodsCodeMapper extends BaseMapper<TCarrierOrderGoodsCode> {

    List<TCarrierOrderGoodsCode> selectGoodsCodeByGoodsIds(@Param("ids") String goodIds);

    int batchUpdate(@Param("list") List<TCarrierOrderGoodsCode> tCarrierOrderGoodsCodes);


    int batchInsertSelective(@Param("list") List<TCarrierOrderGoodsCode> list);
}