<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandOrderObjectionMapper" >
    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TDemandOrderObjection" >
        <foreach collection="list" item="item" separator=";">
            insert into t_demand_order_objection
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    id,
                </if>
                <if test="item.demandOrderId != null" >
                    demand_order_id,
                </if>
                <if test="item.customerName != null" >
                    customer_name,
                </if>
                <if test="item.objectionType != null" >
                    objection_type,
                </if>
                <if test="item.objectionReason != null" >
                    objection_reason,
                </if>
                <if test="item.reportContactName != null" >
                    report_contact_name,
                </if>
                <if test="item.reportTime != null" >
                    report_time,
                </if>
                <if test="item.createdBy != null" >
                    created_by,
                </if>
                <if test="item.createdTime != null" >
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="item.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.demandOrderId != null" >
                    #{item.demandOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.customerName != null" >
                    #{item.customerName,jdbcType=VARCHAR},
                </if>
                <if test="item.objectionType != null" >
                    #{item.objectionType,jdbcType=INTEGER},
                </if>
                <if test="item.objectionReason != null" >
                    #{item.objectionReason,jdbcType=VARCHAR},
                </if>
                <if test="item.reportContactName != null" >
                    #{item.reportContactName,jdbcType=VARCHAR},
                </if>
                <if test="item.reportTime != null" >
                    #{item.reportTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createdBy != null" >
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="com.logistics.tms.entity.TDemandOrderObjection" >
        <foreach collection="list" item="item" separator=";">
            update t_demand_order_objection
            <set >
                <if test="item.demandOrderId != null" >
                    demand_order_id = #{item.demandOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.customerName != null" >
                    customer_name = #{item.customerName,jdbcType=VARCHAR},
                </if>
                <if test="item.objectionType != null" >
                    objection_type = #{item.objectionType,jdbcType=INTEGER},
                </if>
                <if test="item.objectionReason != null" >
                    objection_reason = #{item.objectionReason,jdbcType=VARCHAR},
                </if>
                <if test="item.reportContactName != null" >
                    report_contact_name = #{item.reportContactName,jdbcType=VARCHAR},
                </if>
                <if test="item.reportTime != null" >
                    report_time = #{item.reportTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="searchDemandOrderObjectionIds" resultType="java.lang.Long">
        select distinct
        tdoo.id
        from t_demand_order_objection tdoo
        left join t_demand_order tdo on tdoo.demand_order_id=tdo.id and tdo.valid=1
        left join t_demand_order_address tdoa on tdoa.demand_order_id=tdoo.demand_order_id and tdoa.valid=1
        where tdoo.valid=1
        <if test="condition.demandStatus != null">
            <choose>
                <when test="condition.demandStatus==1">
                    and tdo.if_cancel = 1
                </when>
                <when test="condition.demandStatus==2">
                    and tdo.if_empty = 1
                </when>
                <when test="condition.demandStatus==3">
                    and tdo.if_rollback = 1 and tdo.if_cancel = 0
                </when>
                <when test="condition.demandStatus==3000">
                    and tdo.status = 3000
                </when>
                <when test="condition.demandStatus==2000">
                    and tdo.status = 2000
                </when>
                <when test="condition.demandStatus==1000">
                    and tdo.status = 1000 and if_empty=0 and if_cancel = 0 and tdo.if_rollback = 0
                </when>
            </choose>
        </if>
        <if test="condition.demandOrderCode != null and condition.demandOrderCode !=''">
            and instr(tdo.demand_order_code,#{condition.demandOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="condition.loadRegionName != null and condition.loadRegionName !=''">
            and instr(tdoa.load_region_name,#{condition.loadRegionName,jdbcType=VARCHAR})
        </if>
        <if test="condition.publishTimeStart!=null and condition.publishTimeStart!=''">
            and tdo.publish_time &gt;= DATE_FORMAT(#{condition.publishTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="condition.publishTimeEnd!=null and condition.publishTimeEnd!=''">
            and tdo.publish_time &lt;= DATE_FORMAT(#{condition.publishTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="condition.reportTimeStart!=null and condition.reportTimeStart!=''">
            and tdoo.report_time &gt;= DATE_FORMAT(#{condition.reportTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="condition.reportTimeEnd!=null and condition.reportTimeEnd!=''">
            and tdoo.report_time &lt;= DATE_FORMAT(#{condition.reportTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        order by tdoo.last_modified_time desc,tdoo.id
    </select>

    <select id="searchDemandOrderObjection" resultType="com.logistics.tms.api.feign.demandorderobjection.model.SearchDemandOrderObjectionResponseModel">
        select
        tdo.id as demandId,
        tdo.status as status,
        tdo.if_cancel as ifCancel,
        tdo.if_empty as ifEmpty,
        tdo.if_rollback as ifRollback,
        tdo.demand_order_code as demandOrderCode,
        tdo.entrust_type as entrustType,
        tdo.source as demandOrderSource,

        tdoo.customer_name as customerName,
        tdoo.report_time as reportTime,
        tdoo.report_contact_name as reportContactName,
        tdoo.objection_type as objectionType,
        tdoo.objection_reason as objectionReason,

        tdoa.load_region_name as loadRegionName
        from t_demand_order_objection tdoo
        left join t_demand_order tdo on tdo.id=tdoo.demand_order_id and tdo.valid=1
        left join t_demand_order_address tdoa on tdoa.demand_order_id=tdoo.demand_order_id and tdoa.valid=1
        where tdoo.valid=1
        and tdoo.id in (${ids})
        order by tdoo.last_modified_time desc,tdoo.id
    </select>

    <select id="getByDemandOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_demand_order_objection
        where valid = 1
        and demand_order_id in (${demandOrderIds})
    </select>
</mapper>