package com.logistics.tms.mapper;

import com.logistics.tms.controller.customeraccount.response.AccountInfoResponseModel;
import com.logistics.tms.entity.TCustomerAccount;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TCustomerAccountMapper extends BaseMapper<TCustomerAccount> {
    TCustomerAccount selectByPrimaryKeyDecrypt(Long id);

    int insertSelectiveEncrypt(TCustomerAccount tAccount);

    int updateByPrimaryKeySelectiveEncrypt(TCustomerAccount tAccount);

    AccountInfoResponseModel selectByMobileAndRole(@Param("mobile") String mobile, @Param("openId") String openId, @Param("userRole") Integer userRole);

    TCustomerAccount getByOpenId(@Param("openId")String openId);

    TCustomerAccount selectByMobile(@Param("mobile") String mobile);

    AccountInfoResponseModel getAccountInfoBy(@Param("mobile") String mobile, @Param("accountId") Long accountId, @Param("userRole") Integer userRole);
}