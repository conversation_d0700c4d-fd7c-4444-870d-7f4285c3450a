package com.logistics.management.webapi.controller.oilfilled.mapping;

import com.logistics.management.webapi.client.oilfilled.response.OilFilledOperationRecordResponseModel;
import com.logistics.management.webapi.controller.oilfilled.response.OilRefundRecordResponseDto;
import com.logistics.management.webapi.base.enums.OilFilledTypeEnum;
import com.logistics.management.webapi.base.enums.OilRefundReasonTypeEnum;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @author: wjf
 * @date: 2019/12/23 16:46
 */
public class OilRefundRecordMapping extends MapperMapping<OilFilledOperationRecordResponseModel,OilRefundRecordResponseDto> {
    @Override
    public void configure() {
        OilFilledOperationRecordResponseModel source = getSource();
        OilRefundRecordResponseDto destination = getDestination();
        if (source!= null){
            if (source.getOilFilledType() != null){
                destination.setOilFilledTypeLabel(OilFilledTypeEnum.getEnum(source.getOilFilledType()).getValue());
            }
            if (source.getOilFilledDate() != null) {
                destination.setOilFilledDate(DateUtils.dateToString(source.getOilFilledDate(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if (source.getRefundReasonType() != null) {
                destination.setRefundReasonTypeDesc(OilRefundReasonTypeEnum.getEnum(source.getRefundReasonType()).getValue());
            }
        }
    }
}
