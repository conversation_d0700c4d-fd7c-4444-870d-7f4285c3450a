package com.logistics.tms.controller.settlestatement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/21
 */
@Data
public class CarrierChangeSettleStatementStatsRequestModel {

	@ApiModelProperty(value = "对账单id")
	private Long settleStatementId;

	@ApiModelProperty(value = "1:业务人员操作,2:财务人员操作")
	private Integer operationType;

	@ApiModelProperty(value = "通过：1 驳回：2 ")
	private Integer settleStatementStatus;

	@ApiModelProperty(value = "备注原因")
	private String remark;
}
