package com.logistics.tms.controller.driversafepromise.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/11/6 16:52
 */
@Data
public class SignSafePromiseDetailResponseModel {
    @ApiModelProperty("承诺书ID")
    private Long safePromiseId;
    @ApiModelProperty("签订承诺书ID")
    private Long signSafePromiseId;
    @ApiModelProperty("司机ID")
    private Long staffId;
    @ApiModelProperty("司机名称")
    private String staffName;
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("手持承诺书图片地址-相对路径")
    private String handPromiseUrl = "";
    @ApiModelProperty("签字责任书图片地址-相对路径")
    private String signResponsibilityUrl = "";
}
