package com.logistics.tms.mapper;

import com.logistics.tms.entity.TRealNameAuthentication;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * Created by Mybatis Generator on 2023/01/09
 */
@Mapper
public interface TRealNameAuthenticationMapper extends BaseMapper<TRealNameAuthentication> {

    int insertSelectiveEncrypt(TRealNameAuthentication tRealNameAuthentication);

    int updateByPrimaryKeySelectiveEncrypt(TRealNameAuthentication tRealNameAuthentication);

    TRealNameAuthentication selectRealNameAuthenticationByMobile(@Param("mobile") String mobile);

    List<TRealNameAuthentication> selectRealNameByMobiles(@Param("mobiles") Collection<String> mobiles);

    List<TRealNameAuthentication> selectByIds(@Param("ids") List<Long> ids);

    int updateCertificationStatus(@Param("entity") TRealNameAuthentication entity, @Param("currentStatus") Integer currentStatus);

    int delRealNameAuthenticationByMobile(@Param("mobiles") List<String> mobiles);
}