package com.logistics.tms.biz.attendancerecord.handle;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.attendancerecord.model.AttendanceClockModel;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

public interface AttendancePunchHandle {

    boolean filter(Integer dutyPunchType);

    boolean handle(AttendanceClockModel boModel);

    default BigDecimal calculatingManHour(Date onDutyPunchTime, Date offDutyPunchTime) {
        LocalDateTime onDutyPunchLocalDateTime = LocalDateTime.ofInstant(onDutyPunchTime.toInstant(), ZoneId.systemDefault());
        LocalDateTime offDutyPunchLocalDateTime = LocalDateTime.ofInstant(offDutyPunchTime.toInstant(), ZoneId.systemDefault());
        Duration duration = Duration.between(onDutyPunchLocalDateTime, offDutyPunchLocalDateTime);
        BigDecimal[] bigDecimals = BigDecimal.valueOf(duration.toMinutes()).divideAndRemainder(CommonConstant.BIG_DECIMAL_SIXTY);
        return bigDecimals[CommonConstant.INTEGER_ONE].compareTo(CommonConstant.BIG_DECIMAL_THIRTY) > CommonConstant.NEGATIVE_INTEGER_ONE ?
                bigDecimals[CommonConstant.INTEGER_ZERO].add(CommonConstant.ZERO_POINT_FIVE) : bigDecimals[CommonConstant.INTEGER_ZERO];
    }
}
