package com.logistics.tms.api.feign.vehicleoilcard.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/8/3 10:56
 */
@Data
public class SearchVehicleOilCardListRequestModel extends AbstractPageForm<SearchVehicleOilCardListRequestModel> {
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("卡号")
    private String cardNumber;
    @ApiModelProperty("操作时间-起始")
    private String lastModifiedTimeStart;
    @ApiModelProperty("操作时间-结束")
    private String lastModifiedTimeEnd;
    @ApiModelProperty("车辆油卡表ids（选择导出用）")
    private String vehicleOilCardIds;
}
