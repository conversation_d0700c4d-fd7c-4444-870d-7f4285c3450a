package com.logistics.management.webapi.client.warehouseaddress.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.warehouseaddress.WarehouseAddressClient;
import com.logistics.management.webapi.client.warehouseaddress.request.*;
import com.logistics.management.webapi.client.warehouseaddress.response.SearchWarehouseAddressResponseModel;
import com.logistics.management.webapi.client.warehouseaddress.response.WarehouseAddressDetailResponseModel;
import com.logistics.management.webapi.client.warehouseaddress.response.WarehouseAddressListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/7/15 9:26
 */
@Component
public class WarehouseAddressClientHystrix implements WarehouseAddressClient {
    @Override
    public Result<PageInfo<WarehouseAddressListResponseModel>> warehouseAddressList(WarehouseAddressListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> warehouseAddressAddOrModifyOrDel(AddWarehouseAddressRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<WarehouseAddressDetailResponseModel> warehouseAddressDetail(WarehouseAddressDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<WarehouseAddressListResponseModel>> export(WarehouseAddressListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> enable(WarehouseAddressEnableRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<SearchWarehouseAddressResponseModel>> searchWarehouseAddress(SearchWarehouseAddressRequestModel requestModel) {
        return Result.timeout();
    }
}
