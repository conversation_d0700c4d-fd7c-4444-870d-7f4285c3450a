<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDriverAccountMapper">
    <sql id="Base_Column_List_Decrypt">
        id, driver_id, AES_DECRYPT(UNHEX(bank_account),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as bank_account
        , bank_account_name, bra_bank_name, bank_code,created_by,
        created_time, last_modified_by, last_modified_time, valid
    </sql>

    <select id="selectByBankAccountDriverId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_driver_account
        where valid = 1
        and AES_DECRYPT(UNHEX(bank_account), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') = #{bankAccount,jdbcType=VARCHAR}
        and driver_id = #{driverId,jdbcType=BIGINT}
    </select>

    <select id="searchList" resultType="com.logistics.tms.controller.driveraccount.response.SearchDriverAccountResponseModel">
        select
        tda.id                                                                                                                as driverAccountId,
        tsb.name                                                                                                              as driverName,
        tsb.mobile                                                                                                            as driverMobile,
        tsb.staff_property                                                                                                    as driverProperty,
        AES_DECRYPT(UNHEX(tda.bank_account), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as bankAccount,
        tda.bank_account_name                                                                                                 as bankAccountName,
        tda.bra_bank_name                                                                                                     as braBankName,
        tda.created_by                                                                                                        as createdBy,
        tda.last_modified_by                                                                                                  as operateBy,
        tda.last_modified_time                                                                                                as operateDateTime
        from t_driver_account tda
        left join t_staff_basic tsb on tsb.staff_property in (1, 3) and tda.driver_id = tsb.id and tsb.valid = 1
        where tda.valid = 1
        <if test="driverName != null and driverName != ''">
            and (instr(tsb.name, #{driverName,jdbcType=VARCHAR}) or
                 instr(tsb.mobile, #{driverName,jdbcType=VARCHAR}))
        </if>
        <if test="driverProperty != null">
            and tsb.staff_property = #{driverProperty,jdbcType=INTEGER}
        </if>
        <if test="bankAccount != null and bankAccount != ''">
            and instr(AES_DECRYPT(UNHEX(tda.bank_account), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'), #{bankAccount,jdbcType=VARCHAR})
        </if>
        <if test="createdStartTime != '' and createdStartTime != null">
            and tda.created_time &gt;= DATE_FORMAT(#{createdStartTime,jdbcType=VARCHAR}, '%Y-%m-%d %k:%i:%S')
        </if>
        <if test="createdEndTime != '' and createdEndTime != null">
            and tda.created_time &lt;= DATE_FORMAT(#{createdEndTime,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="operateStartTime != '' and operateStartTime != null">
            and tda.last_modified_time &gt;= DATE_FORMAT(#{operateStartTime,jdbcType=VARCHAR}, '%Y-%m-%d %k:%i:%S')
        </if>
        <if test="operateEndTime != '' and operateEndTime != null">
            and tda.last_modified_time &lt;= DATE_FORMAT(#{operateEndTime,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        order by tda.last_modified_time desc, tda.id desc
    </select>

    <select id="getDetail" resultType="com.logistics.tms.controller.driveraccount.response.DriverAccountDetailResponseModel">
        select
        tda.id                                                                                                                as driverAccountId,
        tsb.id                                                                                                                as driverId,
        tsb.name                                                                                                              as driverName,
        tsb.mobile                                                                                                            as driverMobile,
        AES_DECRYPT(UNHEX(tda.bank_account), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as bankAccount,
        tda.bank_account_name                                                                                                 as bankAccountName,
        tda.bra_bank_name                                                                                                     as braBankName,
        tda.bank_code                                                                                                         as bankCode
        from t_driver_account tda
        left join t_staff_basic tsb on tsb.staff_property in (1, 3) and tda.driver_id = tsb.id and tsb.valid = 1
        where tda.id = #{driverAccountId,jdbcType=BIGINT}
    </select>

    <select id="selectOneByDriverId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_driver_account
        where valid = 1
        and driver_id = #{driverId}
    </select>

    <insert id="insertSelectiveEncrypt" useGeneratedKeys="true" keyProperty="id">
        insert into t_driver_account
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="driverId != null" >
                driver_id,
            </if>
            <if test="bankAccount != null" >
                bank_account,
            </if>
            <if test="bankAccountName != null" >
                bank_account_name,
            </if>
            <if test="braBankName != null" >
                bra_bank_name,
            </if>
            <if test="bankCode != null" >
                bank_code,
            </if>
            <if test="createdBy != null" >
                created_by,
            </if>
            <if test="createdTime != null" >
                created_time,
            </if>
            <if test="lastModifiedBy != null" >
                last_modified_by,
            </if>
            <if test="lastModifiedTime != null" >
                last_modified_time,
            </if>
            <if test="valid != null" >
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="driverId != null" >
                #{driverId,jdbcType=BIGINT},
            </if>
            <if test="bankAccount != null" >
                HEX(AES_ENCRYPT(#{bankAccount,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')) ,
            </if>
            <if test="bankAccountName != null" >
                #{bankAccountName,jdbcType=VARCHAR},
            </if>
            <if test="braBankName != null" >
                #{braBankName,jdbcType=VARCHAR},
            </if>
            <if test="bankCode != null" >
                #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null" >
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null" >
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null" >
                #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null" >
                #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null" >
                #{valid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelectiveEncrypt">
        update t_driver_account
        <set >
            <if test="driverId != null" >
                driver_id = #{driverId,jdbcType=BIGINT},
            </if>
            <if test="bankAccount != null" >
                bank_account = HEX(AES_ENCRYPT(#{bankAccount,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')) ,
            </if>
            <if test="bankAccountName != null" >
                bank_account_name = #{bankAccountName,jdbcType=VARCHAR},
            </if>
            <if test="braBankName != null" >
                bra_bank_name = #{braBankName,jdbcType=VARCHAR},
            </if>
            <if test="bankCode != null" >
                bank_code = #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null" >
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null" >
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null" >
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null" >
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null" >
                valid = #{valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectDetailByDriverId" resultType="com.logistics.tms.controller.driveraccount.response.BankCardInfoResponseModel">
        select
        tda.id                                                                                                                as driverAccountId,
        AES_DECRYPT(UNHEX(tda.bank_account), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as bankAccount,
        tda.bank_account_name                                                                                                 as bankAccountName
        from t_driver_account tda
        where valid = 1
        and tda.driver_id = #{driverId,jdbcType=BIGINT}
        limit 1
    </select>

    <select id="selectByPrimaryKeyDecrypt" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_driver_account
        where valid = 1
        and id =#{id,jdbcType=BIGINT}
    </select>

    <select id="selectOneByDriverAccount" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_driver_account
        where valid = 1
        and AES_DECRYPT(UNHEX(bank_account), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') = #{bankAccount,jdbcType=VARCHAR}
    </select>
</mapper>