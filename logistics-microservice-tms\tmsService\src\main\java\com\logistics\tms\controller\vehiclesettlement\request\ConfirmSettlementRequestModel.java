package com.logistics.tms.controller.vehiclesettlement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/12 19:40
 */
@Data
public class ConfirmSettlementRequestModel {
    private Long vehicleSettlementId;
    @ApiModelProperty("月运单数量")
    private Integer carrierOrderCount;
    @ApiModelProperty("车辆月运单费用")
    private BigDecimal dispatchFreightFeeTotal;
    @ApiModelProperty("是否调整费用：0 否，1 是")
    private Integer ifAdjustFee;
    @ApiModelProperty("调整费用符号：1 +，2 -")
    private Integer adjustFeeSymbol;
    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;
    @ApiModelProperty("调整原因")
    private String adjustRemark;

    @ApiModelProperty("月应扣轮胎费")
    private BigDecimal tireCostTotal;
    @ApiModelProperty("月应扣充油费")
    private BigDecimal oilFilledFeeTotal;
    @ApiModelProperty("月应扣gps费")
    private BigDecimal gpsDeductingFee;
    @ApiModelProperty("月应扣停车费")
    private BigDecimal parkingDeductingFee;

    @ApiModelProperty("保险未扣除费用")
    private List<VehicleInsuranceCostRequestModel> insuranceCostList;
    @ApiModelProperty("应扣保险费")
    private BigDecimal insuranceFee;
    @ApiModelProperty("车辆理赔费用")
    private BigDecimal vehicleClaimFee;
    @ApiModelProperty("个人意外险费用合计")
    private BigDecimal accidentInsuranceExpenseTotal;
    @ApiModelProperty("月应扣个人意外险费")
    private BigDecimal accidentInsuranceFee;
    @ApiModelProperty("个人意外险费月理赔费用")
    private BigDecimal accidentInsuranceClaimFee;

    @ApiModelProperty("月应扣贷款费")
    private BigDecimal loanFee;
    @ApiModelProperty("月实际应付运费")
    private BigDecimal actualExpensesPayable;
    @ApiModelProperty("扣减费用合计")
    private BigDecimal deductingFeeTotal;
    @ApiModelProperty("剩余未扣费用合计")
    private BigDecimal remainingDeductingFeeTotal;
    @ApiModelProperty("附件")
    private String attachment;
    @ApiModelProperty("备注")
    private String remark;
}
