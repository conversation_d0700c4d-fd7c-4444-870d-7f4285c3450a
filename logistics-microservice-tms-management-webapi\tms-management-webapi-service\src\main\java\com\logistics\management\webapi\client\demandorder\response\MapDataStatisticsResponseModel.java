package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/10/27 14:38
 */
@Data
public class MapDataStatisticsResponseModel {
    @ApiModelProperty("市名称")
    private String cityName ;
    @ApiModelProperty("市经度")
    private String longitude ;
    @ApiModelProperty("市维度")
    private String latitude;
    @ApiModelProperty("待提货运单数")
    private Integer waitLoadCount = 0;
    @ApiModelProperty("待纠错运单数")
    private Integer waitCorrectCount = 0;
    @ApiModelProperty("待调度需求单数")
    private Integer waitDispatchCount = 0;

    private Long loadCityId;//提货市id
}
