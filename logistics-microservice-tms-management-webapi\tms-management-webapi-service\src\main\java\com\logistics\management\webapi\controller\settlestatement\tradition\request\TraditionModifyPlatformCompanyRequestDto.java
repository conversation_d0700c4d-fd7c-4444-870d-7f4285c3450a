package com.logistics.management.webapi.controller.settlestatement.tradition.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/9
 */
@Data
public class TraditionModifyPlatformCompanyRequestDto {

	@ApiModelProperty(value = "对账单id", required = true)
	@NotBlank(message = "id不能为空")
	private String settleStatementId;

	@ApiModelProperty(value = "结算主体", required = true)
	@NotBlank(message = "请选择结算主体")
	private String platformCompanyId;

	@ApiModelProperty(value = "合同号")
	@Length(max = 30, message = "合同号长度不能超过30个字符")
	private String contractCode;
}
