package com.logistics.management.webapi.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/10/12 19:23
 */
@Data
public class SearchVehicleSettlementListResponseDto {
    private String vehicleSettlementId="";
    @ApiModelProperty("结算状态：0 待对账，1 待发送，2 待确认，3 待处理，4 待结清，5 部分结清，6 已结清")
    private String status="";
    @ApiModelProperty("结算状态文本")
    private String statusLabel="";
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private String vehicleProperty = "";
    @ApiModelProperty("车辆机构展示文本")
    private String vehiclePropertyLabel = "";
    @ApiModelProperty("车辆id")
    private String vehicleId="";
    @ApiModelProperty("司机")
    private String driverName="";
    @ApiModelProperty("账单时间")
    private String settlementMonth="";
    @ApiModelProperty("月运单数量")
    private String carrierOrderCount="";
    @ApiModelProperty("运费")
    private String carrierFreight="";
    @ApiModelProperty("月实付运费")
    private String actualExpensesPayable="";
    @ApiModelProperty("调整费用")
    private String adjustFee="";
    @ApiModelProperty("扣减费用合计")
    private String deductingFeeTotal="";
    @ApiModelProperty("备注")
    private String remark="";
    @ApiModelProperty("操作人")
    private String lastModifiedBy="";
    @ApiModelProperty("操作时间")
    private String lastModifiedTime="";

}
