package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.controller.vehicleassetmanagement.response.VehicleGpsRecordListResponseModel;
import com.logistics.tms.entity.TVehicleGpsRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

@Mapper
public interface TVehicleGpsRecordMapper extends BaseMapper<TVehicleGpsRecord>{

    int batchInsert(@Param("list") List<TVehicleGpsRecord> picLists);

    int batchUpdate(@Param("list") List<TVehicleGpsRecord> picLists);

    List<VehicleGpsRecordListResponseModel> getVehicleGpsRecordByVehicleIds(@Param("vehicleIds") String vehicleIds);

    int countGpsRecordByDate(@Param("vehicleId") Long vehicleId, @Param("installTime") Date installTime);
}