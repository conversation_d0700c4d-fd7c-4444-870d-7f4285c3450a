package com.logistics.management.webapi.api.feign.freight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 启用/禁用
 * @Author: sj
 * @Date: 2019/12/24 13:05
 */
@Data
public class EnableFreightRequestDto {
    @NotBlank(message = "运价ID为空")
    @ApiModelProperty("运价ID")
    private String freightId;
    @NotBlank(message = "请选择禁用/启用")
    @ApiModelProperty("禁用启用 1 启用 0 禁用")
    private String enabled;
}
