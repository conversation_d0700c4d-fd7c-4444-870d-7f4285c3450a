package com.logistics.management.webapi.base.enums;

public enum ContractStatusEnum {
    DEFAULT_VALUE(0,""),
    WAIT_EXECUTE(1, "待执行"),
    EXECUTING(2, "执行中"),
    TERMINATION(3, "已终止"),
    CANCEL(4, "已作废"),
    ;
    private Integer key;
    private String value;

    ContractStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static ContractStatusEnum getEnum(Integer key) {
        for (ContractStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT_VALUE;
    }
}
