package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class DemandOrderDetailForLeYiResponseModel {

    private Long demandId;
    private String demandOrderCode;
    private String customerOrderCode;
    private Integer entrustStatus;
    private Integer ifCancel;
    private Integer ifEmpty;
    @ApiModelProperty("是否回退")
    private Integer ifRollback;
    private Integer status;
    private String cancelReason;
    private String customerCompanyName;
    private String customerCompanyPhone;
    private Integer ifObjection;
    private String publishName;
    private Date publishTime;
    private String remark;

    //地址信息
    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    private String consignorName;
    private String consignorMobile;
    @ApiModelProperty("提货经度")
    private String loadLongitude;
    @ApiModelProperty("提货纬度")
    private String loadLatitude;
    private Date expectedLoadTime;
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;
    private String receiverName;
    private String receiverMobile;
    private Date expectedUnloadTime;
    @ApiModelProperty("卸货经度")
    private String unloadLongitude;
    @ApiModelProperty("卸货纬度")
    private String unloadLatitude;

    private Long companyEntrustId;
    private String companyEntrustName;
    private Integer goodsUnit;
    private String upstreamCustomer;

    @ApiModelProperty("车主id")
    private Long companyCarrierId;
    @ApiModelProperty("车主公司类型：1 公司，2 个人")
    private Integer companyCarrierType;
    @ApiModelProperty("主公司名称")
    private String companyCarrierName;
    @ApiModelProperty("车主账号名称")
    private String carrierContactName;
    @ApiModelProperty("车主账号手机号（原长度50）")
    private String carrierContactPhone;

    private Integer settlementTonnage;
    private BigDecimal goodsAmount;
    private Integer source;
    private Integer entrustType;

    @ApiModelProperty("未安排数量")
    private BigDecimal notArrangedAmount;

    @ApiModelProperty("货主结算费用")
    private BigDecimal entrustSettlementCostTotal;
    @ApiModelProperty("货主结算数量")
    private BigDecimal entrustSettlementAmount;
    @ApiModelProperty("货主结算费用类型")
    private Integer entrustPriceType;
    @ApiModelProperty("车主结算费用")
    private BigDecimal carrierSettlementCostTotal;
    @ApiModelProperty("车主结算数量")
    private BigDecimal carrierSettlementAmount;
    @ApiModelProperty("车主结算费用类型")
    private Integer carrierPriceType;

    private Integer orderMode;

    private Date ticketTime;

    private Integer carrierSettlement;

    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;

    //需求单上费用信息
    @ApiModelProperty("需求单货主预计费用类型 1 单价 2 一口价")
    private Integer exceptContractPriceType;
    @ApiModelProperty("需求单货主预计运费")
    private BigDecimal exceptContractPrice;
    @ApiModelProperty("需求单车主费用类型 1 单价 2 一口价")
    private Integer carrierFreightType;
    @ApiModelProperty("需求单车主运费")
    private BigDecimal carrierFreight;

    @ApiModelProperty("运单临时费用合计")
    private BigDecimal carrierOtherFeeTotal;

    @ApiModelProperty("周末是否可上门：0 空，1 是，2 否")
    private Integer availableOnWeekends;
    @ApiModelProperty("装卸方: 0 空，1 我司装卸，2 客户装卸")
    private Integer loadingUnloadingPart;
    @ApiModelProperty("装卸费用")
    private BigDecimal loadingUnloadingCharge;

    @ApiModelProperty("项目标签（多个标签,拼接）：1 石化板块，2 轮胎板块，3 涂料板块，4 其他")
    private String projectLabel;

    private List<DemandOrderGoodsResponseModel> goodsResponseModel;
    private List<DemandOrderCarrierResponseModel> carrierResponseModel;
}
