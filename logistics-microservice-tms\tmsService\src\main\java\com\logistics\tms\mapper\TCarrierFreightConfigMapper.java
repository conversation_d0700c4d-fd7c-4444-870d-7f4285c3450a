package com.logistics.tms.mapper;

import com.logistics.tms.controller.freightconfig.response.CarrierFreightConfigDetailResponseModel;
import com.logistics.tms.controller.freightconfig.response.CarrierFreightConfigListResponseModel;
import com.logistics.tms.biz.carrierfreight.model.CarrierFreightCarrierModel;
import com.logistics.tms.entity.TCarrierFreightConfig;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Mybatis Generator on 2023/06/30
 */
@Mapper
public interface TCarrierFreightConfigMapper extends BaseMapper<TCarrierFreightConfig> {
    List<CarrierFreightConfigListResponseModel> selectByFreightId(@Param("freightId") Long freightId);

    CarrierFreightConfigDetailResponseModel selectById(@Param("freightConfigId") Long freightConfigId);

    List<String> selectEntrustTypeByFreightId(@Param("freightId") Long freightId);

    List<CarrierFreightCarrierModel> selectCarrierFreightByCompanyCarrier(@Param("list") List<Long> companyCarrierIds);
}