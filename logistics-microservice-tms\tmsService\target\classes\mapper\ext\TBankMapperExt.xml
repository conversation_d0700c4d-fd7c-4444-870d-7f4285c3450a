<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TBankMapper" >
    <select id="searchBankList" resultType="com.logistics.tms.api.feign.bank.model.SearchBankResponseModel">
        select
            tb.id as bankId,
            tb.bank_name as bankName,
            tb.branch_name as branchName,
            tb.enabled as enable,
            tb.remark as remark,
            tb.created_by as createdBy,
            tb.last_modified_by as lastModifiedBy,
            tb.created_time as createdTime,
            tb.last_modified_time as lastModifiedTime
        from t_bank tb
        where tb.valid = 1
        order by tb.last_modified_time desc,tb.id desc
    </select>
    
    <select id="queryBankListByName" resultType="com.logistics.tms.api.feign.bank.model.FuzzyQueryBankListResponseModel">
        select
            tb.id as bankId,
            tb.bank_name as bankName
        from t_bank tb
        where tb.valid = 1 and tb.enabled = 1
        <if test="params.fuzzyBankName!=null and params.fuzzyBankName!=''">
            and (
              instr(tb.bank_name,#{params.fuzzyBankName,jdbcType = VARCHAR})
              or instr(tb.branch_name,#{params.fuzzyBankName,jdbcType = VARCHAR})
            )
        </if>
        order by tb.last_modified_time desc
    </select>

    <select id="findBankByName" resultType="com.logistics.tms.entity.TBank">
        select
        <include refid="Base_Column_List"/>
        from t_bank
        where valid =1
        and bank_name = #{bankName,jdbcType = VARCHAR}
        limit 1
    </select>

    <select id="findByName" resultType="com.logistics.tms.entity.TBank">
        select
        <include refid="Base_Column_List"/>
        from t_bank
        where valid =1
        and bank_name = #{bankName,jdbcType = VARCHAR}
        and branch_name = #{branchName,jdbcType = VARCHAR}
    </select>

    <select id="getBankDetailById" resultType="com.logistics.tms.api.feign.bank.model.BankDetailResponseModel" parameterType="java.lang.Long" >
         select
            tb.id as bankId,
            tb.bank_name as bankName,
            tb.branch_name as branchName,
            tb.enabled as enable,
            tb.remark as remark,
            tb.created_by as createdBy,
            tb.created_time as createdTime,
            tb.last_modified_by as lastModifiedBy,
            tb.last_modified_time as lastModifiedTime
        from t_bank tb
        where tb.valid = 1
        and  tb.id = #{bankId,jdbcType = BIGINT}
        order by tb.last_modified_time desc
    </select>

    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TBank">
        <foreach collection="list" item="item" index="index" separator=";">
            insert into t_bank
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    id,
                </if>
                <if test="item.bankName != null" >
                    bank_name,
                </if>
                <if test="item.branchName != null" >
                    branch_name,
                </if>
                <if test="item.enabled != null" >
                    enabled,
                </if>
                <if test="item.remark != null" >
                    remark,
                </if>
                <if test="item.createdBy != null" >
                    created_by,
                </if>
                <if test="item.createdTime != null" >
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="item.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.bankName != null" >
                    #{item.bankName,jdbcType=VARCHAR},
                </if>
                <if test="item.branchName != null" >
                    #{item.branchName,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null" >
                    #{item.enabled,jdbcType=INTEGER},
                </if>
                <if test="item.remark != null" >
                    #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null" >
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <update id="batchUpdateByName" parameterType="com.logistics.tms.entity.TBank" >
        <foreach collection="list" item="item" index="index" separator=";">
            update t_bank
            <set >
                <if test="item.bankName != null" >
                    bank_name = #{item.bankName,jdbcType=VARCHAR},
                </if>
                <if test="item.branchName != null" >
                    branch_name = #{item.branchName,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null" >
                    enabled = #{item.enabled,jdbcType=INTEGER},
                </if>
                <if test="item.remark != null" >
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where bank_name = #{item.bankName,jdbcType=VARCHAR}
        </foreach>
    </update>


    <update id="batchUpdate" parameterType="com.logistics.tms.entity.TBank" >
        <foreach collection="list" item="item" index="index" separator=";">
            update t_bank
            <set >
                <if test="item.bankName != null" >
                    bank_name = #{item.bankName,jdbcType=VARCHAR},
                </if>
                <if test="item.branchName != null" >
                    branch_name = #{item.branchName,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null" >
                    enabled = #{item.enabled,jdbcType=INTEGER},
                </if>
                <if test="item.remark != null" >
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>