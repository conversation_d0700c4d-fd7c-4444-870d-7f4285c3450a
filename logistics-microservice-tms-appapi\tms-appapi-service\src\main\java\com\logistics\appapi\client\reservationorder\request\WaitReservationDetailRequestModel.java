package com.logistics.appapi.client.reservationorder.request;

import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/14 14:37
 */
@Data
public class WaitReservationDetailRequestModel {

    /**
     * 预约类型：1 提货，2 卸货
     */
    private Integer reservationType;

    /**
     * 运单id
     */
    private List<Long> carrierOrderIdList;

    /**
     * 定位-经度
     */
    private String longitude;
    /**
     * 定位-纬度
     */
    private String latitude;

    /**
     * 来源 0:司机小程序 1.H5
     */
    private Integer source;
}
