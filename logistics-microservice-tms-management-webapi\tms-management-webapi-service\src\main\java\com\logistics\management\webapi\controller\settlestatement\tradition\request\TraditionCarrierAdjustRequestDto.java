package com.logistics.management.webapi.controller.settlestatement.tradition.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class TraditionCarrierAdjustRequestDto {

    @ApiModelProperty(value = "对账单id", required = true)
    @NotBlank(message = "对账单id不能为空")
    private String settleStatementId;

    @ApiModelProperty(value = "调整费用符号：1 ‘+’， 2 ‘-’ ", required = true)
    @NotBlank(message = "请维护调整费用")
    private String adjustCostSymbol;

    @ApiModelProperty(value = "调整费用", required = true)
    @NotBlank(message = "请维护调整费用，请输入0.01~1000的数字")
    @DecimalMin(value = "0", message = "请维护调整费用，请输入0.01~1000的数字")
    @DecimalMax(value = "1000", message = "请维护调整费用，请输入0.01~1000的数字")
    private String adjustCost;

    @ApiModelProperty(value = "调整理由1-100字符", required = true)
    @Size(min = 1, max = 100, message = "请输入1-100字符")
    @NotBlank(message = "请输入调整理由")
    private String adjustReason;
}
