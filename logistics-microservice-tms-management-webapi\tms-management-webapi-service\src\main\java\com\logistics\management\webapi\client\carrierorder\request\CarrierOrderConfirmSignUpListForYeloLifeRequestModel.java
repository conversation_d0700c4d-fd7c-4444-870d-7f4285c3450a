package com.logistics.management.webapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2024/7/22 13:33
 */
@Data
public class CarrierOrderConfirmSignUpListForYeloLifeRequestModel {

    @ApiModelProperty(value = "运单ID")
    private Long carrierOrderId;

    @ApiModelProperty(value = "实际货主费用(元)")
    private BigDecimal actualEntrustFee;

    @ApiModelProperty(value = "实际车主运费(元)")
    private BigDecimal signCarrierFreight;

}
