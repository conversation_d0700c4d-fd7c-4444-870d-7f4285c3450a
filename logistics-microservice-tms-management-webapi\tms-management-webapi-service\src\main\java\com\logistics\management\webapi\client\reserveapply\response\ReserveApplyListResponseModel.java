package com.logistics.management.webapi.client.reserveapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ReserveApplyListResponseModel {

    @ApiModelProperty("申请记录id")
    private Long applyId;

    @ApiModelProperty("申请单号")
    private String applyCode;

    @ApiModelProperty("状态：-1 已撤销，0 待业务审核，1 待财务审核，2 已驳回，3 待打款，4 已打款")
    private Integer status;

    @ApiModelProperty("类型: 1 充值 2 垫付")
    private Integer type;

    @ApiModelProperty("收款账号")
    private String receiveAccount;

    @ApiModelProperty("收款账号银行名称")
    private String receiveBankAccountName;

    @ApiModelProperty("收款账号支行名称")
    private String receiveBraBankName;

    @ApiModelProperty("司机姓名")
    private String driverName;

    @ApiModelProperty("司机手机号")
    private String driverMobile;

    @ApiModelProperty("申请金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("批准金额")
    private BigDecimal approveAmount;

    @ApiModelProperty("备注")
    private String remark;
}
