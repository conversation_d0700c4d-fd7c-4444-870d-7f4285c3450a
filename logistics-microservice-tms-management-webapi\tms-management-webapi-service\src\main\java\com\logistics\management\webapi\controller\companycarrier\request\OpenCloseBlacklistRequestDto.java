package com.logistics.management.webapi.controller.companycarrier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/4/26 10:01
 */
@Data
public class OpenCloseBlacklistRequestDto {

    /**
     * 车主id
     */
    @ApiModelProperty(value = "车主id",required = true)
    @NotEmpty(message = "请选择车主")
    private List<String> companyCarrierIdList;

    /**
     * 操作类型：1 开启，2 关闭
     */
    @ApiModelProperty(value = "操作类型：1 开启，2 关闭",required = true)
    @NotBlank(message = "操作类型不能为空")
    @Pattern(regexp = "^[12]$", message = "操作类型错误")
    private String operateType;
}
