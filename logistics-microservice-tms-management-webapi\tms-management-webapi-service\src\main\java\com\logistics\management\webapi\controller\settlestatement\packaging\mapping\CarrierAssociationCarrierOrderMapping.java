package com.logistics.management.webapi.controller.settlestatement.packaging.mapping;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.EntrustTypeEnum;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.management.webapi.client.settlestatement.packaging.response.CarrierAssociationCarrierOrderItemModel;
import com.logistics.management.webapi.client.settlestatement.packaging.response.CarrierAssociationCarrierOrderResponseModel;
import com.logistics.management.webapi.controller.settlestatement.packaging.response.CarrierAssociationCarrierOrderItem;
import com.logistics.management.webapi.controller.settlestatement.packaging.response.CarrierAssociationCarrierOrderResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class CarrierAssociationCarrierOrderMapping extends MapperMapping<CarrierAssociationCarrierOrderResponseModel, CarrierAssociationCarrierOrderResponseDto> {
    @Override
    public void configure() {
        CarrierAssociationCarrierOrderResponseModel source = getSource();
        CarrierAssociationCarrierOrderResponseDto destination = getDestination();

        if (source != null) {
            //车主运费合计
            destination.setSettlementCost(ConverterUtils.toString(source.getOtherFees().add(source.getEntrustFreight()).setScale(2,BigDecimal.ROUND_HALF_UP)));

            //数量去0
            destination.setTotalPackageSettlementAmount(source.getTotalPackageSettlementAmount().stripTrailingZeros().toPlainString());
            destination.setTotalWeightSettlementAmount(source.getTotalWeightSettlementAmount().stripTrailingZeros().toPlainString());
            destination.setTotalPieceSettlementAmount(source.getTotalPieceSettlementAmount().stripTrailingZeros().toPlainString());

            //运单数量转换
            List<CarrierAssociationCarrierOrderItem> carrierOrderItemList = new ArrayList<>();
            List<CarrierAssociationCarrierOrderItemModel> carrierOrderItemListModel = source.getCarrierOrderItemList().getList();
            if (ListUtils.isNotEmpty(carrierOrderItemListModel)) {
                CarrierAssociationCarrierOrderItem dtoItem;
                for (CarrierAssociationCarrierOrderItemModel model : carrierOrderItemListModel) {
                    //数据转换
                    dtoItem = MapperUtils.mapper(model, CarrierAssociationCarrierOrderItem.class);

                    //个人车主：姓名+手机号
                    if (CompanyTypeEnum.PERSONAL.getKey().equals(model.getCompanyCarrierType())) {
                        dtoItem.setCompanyCarrierName(model.getContactName() + "_" + model.getContactPhone());
                    }

                    //预约类型，货主名称显示
                    if (EntrustTypeEnum.BOOKING.getKey().equals(model.getDemandOrderEntrustType())) {
                        dtoItem.setCompanyEntrustName(CommonConstant.LEYI_POINTS_FOR_LOGISTICS);
                    }

                    //费用计算
                    dtoItem.setSetSettlementCost(ConverterUtils.toString(model.getOtherFees().add(model.getEntrustFreight())));
                    //货物单位
                    String unit = GoodsUnitEnum.getEnum(model.getGoodsUnit()).getUnit();
                    //数量去0
                    dtoItem.setSettlementAmount(model.getSettlementAmount().stripTrailingZeros().toPlainString() + unit);
                    //单位转换
                    dtoItem.setGoodsUnit(unit);

                    carrierOrderItemList.add(dtoItem);
                }
            }
            PageInfo pageInfo = MapperUtils.mapper(source.getCarrierOrderItemList(), PageInfo.class);
            pageInfo.setList(carrierOrderItemList);
            destination.setCarrierOrderItemList(pageInfo);
        }
    }
}

