package com.logistics.management.webapi.api.feign.driveraccount.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DriverAccountOperateLogResponseDto {

    @ApiModelProperty(value = "时间, yyyy-MM-dd HH:mm:ss")
    private String operateTime = "";

    @ApiModelProperty(value = "操作人")
    private String operateName = "";

    @ApiModelProperty(value = "操作内容")
    private String operateContent = "";

    @ApiModelProperty(value = "备注")
    private String remark = "";
}
