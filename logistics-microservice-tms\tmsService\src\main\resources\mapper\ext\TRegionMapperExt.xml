<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRegionMapper">
    <select id="findByName" resultType="com.logistics.tms.entity.TRegion">
        select
        <include refid="Base_Column_List"/>
        from t_region
        where valid =1
        and region_name = #{regionName,jdbcType = VARCHAR}
    </select>

    <resultMap id="getDetail_map" type="com.logistics.tms.controller.region.response.RegionDetailResponseModel">
        <id column="regionId" property="regionId" jdbcType="BIGINT"/>
        <result column="region_name" property="regionName" jdbcType="VARCHAR"/>
        <result column="contact_name" property="regionContactName" jdbcType="VARCHAR"/>
        <result column="contact_phone" property="regionContactPhone" jdbcType="VARCHAR"/>
        <collection property="provinceResponseDtoList" ofType="com.logistics.tms.controller.region.response.ProvinceResponseModel">
            <result column="province_id" property="provinceId" jdbcType="BIGINT"/>
            <result column="province_name" property="provinceName" jdbcType="VARCHAR"/>
            <result column="city_id" property="cityId" jdbcType="BIGINT"/>
            <result column="city_name" property="cityName" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>
    <select id="getDetail" resultMap="getDetail_map">
        select
        tr.id as regionId,
        tr.region_name,
        tr.contact_name,
        tr.contact_phone,

        tri.province_id,
        tri.province_name,
        tri.city_id,
        tri.city_name
        from t_region tr
        left join t_region_item tri on tri.region_id = tr.id and tri.valid = 1
        where tr.id = #{regionId,jdbcType=BIGINT}
        and tr.valid = 1
    </select>

    <select id="searchListIds" resultType="java.lang.Long">
        select DISTINCT
        tr.id
        from t_region tr
        left join t_region_item tri ON tr.id = tri.region_id and tri.valid = 1
        where tr.valid = 1
        <if test="params.provinceName != null and params.provinceName != ''">
            and instr(tri.province_name, #{params.provinceName})
        </if>
        <if test="params.cityName != null and params.cityName != ''">
            and instr(tri.city_name, #{params.cityName})
        </if>
        <if test="params.regionIds != null and params.regionIds != ''">
            and tr.id in (${params.regionIds})
        </if>
        order by tr.last_modified_time desc,tr.id desc
    </select>

    <resultMap id="searchList_map" type="com.logistics.tms.controller.region.response.SearchRegionResponseModel">
        <id column="regionId" property="regionId" jdbcType="BIGINT"/>
        <result column="enabled" property="enabled" jdbcType="INTEGER"/>
        <result column="regionName" property="regionName" jdbcType="VARCHAR"/>
        <result column="contactName" property="contactName" jdbcType="VARCHAR"/>
        <result column="contactPhone" property="contactPhone" jdbcType="VARCHAR"/>
        <result column="lastModifiedBy" property="lastModifiedBy" jdbcType="VARCHAR"/>
        <result column="lastModifiedTime" property="lastModifiedTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <select id="searchList" resultMap="searchList_map">
        select
        tr.id as regionId,
        tr.enabled as enabled,
        tr.region_name as regionName,
        tr.contact_name as contactName,
        tr.contact_phone as contactPhone,
        tr.last_modified_by as lastModifiedBy,
        tr.last_modified_time as lastModifiedTime
        FROM t_region tr
        where tr.valid = 1
        and tr.id in (${ids})
        order by tr.last_modified_time desc,tr.id desc
    </select>

    <select id="getEnableRegionInfoByDemandIds" resultType="com.logistics.tms.biz.demandorder.model.GetRegionInfoByCityIdModel">
        select
        tdoa.demand_order_id as demandId,
        tdoa.load_city_id as cityId,
        tr.id as regionId,
        tr.region_name as regionName,
        tr.contact_name as regionContactName,
        tr.contact_phone as regionContactPhone
        from t_demand_order_address tdoa
        left join t_region_item tri on tri.city_id=tdoa.load_city_id and tri.valid=1
        left join t_region tr on tri.region_id=tr.id and tr.valid=1
        where tdoa.demand_order_id in (${demandIds}) and tdoa.valid=1
        and tr.enabled=1
    </select>

    <select id="getByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_region
        where valid = 1
        and id in (${ids})
    </select>

    <update id="batchUpdate" parameterType="com.logistics.tms.entity.TRegion" >
        <foreach collection="list" item="item" separator=";">
            update t_region
            <set >
                <if test="item.regionName != null" >
                    region_name = #{item.regionName,jdbcType=VARCHAR},
                </if>
                <if test="item.contactName != null" >
                    contact_name = #{item.contactName,jdbcType=VARCHAR},
                </if>
                <if test="item.contactPhone != null" >
                    contact_phone = #{item.contactPhone,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null" >
                    enabled = #{item.enabled,jdbcType=INTEGER},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="delRegionById">
        update t_region tr
        left join t_region_item tri on tri.region_id = tr.id and tri.valid = 1
        left join t_region_company trc on trc.region_id = tr.id and trc.valid = 1
        set
        tr.valid = 0,
        tr.last_modified_by = #{userName,jdbcType=VARCHAR},
        tr.last_modified_time = #{updateTime,jdbcType=TIMESTAMP},

        tri.valid = 0,
        tri.last_modified_by = #{userName,jdbcType=VARCHAR},
        tri.last_modified_time = #{updateTime,jdbcType=TIMESTAMP},

        trc.valid = 0,
        trc.last_modified_by = #{userName,jdbcType=VARCHAR},
        trc.last_modified_time = #{updateTime,jdbcType=TIMESTAMP}
        where tr.valid = 1
        and tr.id = #{id,jdbcType=BIGINT}
    </update>
</mapper>