package com.logistics.management.webapi.controller.invoicingmanagement.packaging.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/3/19 17:06
 */
@Data
public class InvoicingManagementDetailResponseDto {
    @ApiModelProperty("发票管理id")
    private String invoicingId="";

    @ApiModelProperty("业务名称")
    private String businessName="";

    @ApiModelProperty("开票月份")
    private String invoicingMonth="";

    @ApiModelProperty("承运商id")
    private String companyCarrierId="";
    @ApiModelProperty("承运商名称")
    private String companyCarrierName="";

    @ApiModelProperty("备注")
    private String remark="";
}
