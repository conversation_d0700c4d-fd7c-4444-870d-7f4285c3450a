package com.logistics.tms.client.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/9/1 16:14
 */
@Data
public class SearchWarehouseForBigDataRequestModel {
    @ApiModelProperty("发货人地址-发货省")
    private String loadProvinceName;

    @ApiModelProperty("发货人地址-发货市")
    private String loadCityName;

    @ApiModelProperty("发货人地址-发货区")
    private String loadAreaName;

    @ApiModelProperty("发货人地址-发货详细地址")
    private String loadDetailAddress;

    @ApiModelProperty("仓库名称，仓库地址，仓库联系人 搜索条件")
    private String warehouseCondition;
}
