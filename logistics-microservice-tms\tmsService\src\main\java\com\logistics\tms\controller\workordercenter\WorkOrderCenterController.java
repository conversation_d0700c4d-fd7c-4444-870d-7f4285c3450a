package com.logistics.tms.controller.workordercenter;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.workordercenter.WorkOrderBiz;
import com.logistics.tms.controller.workordercenter.request.*;
import com.logistics.tms.controller.workordercenter.response.WorkOrderDetailResponseModel;
import com.logistics.tms.controller.workordercenter.response.WorkOrderListResponseModel;
import com.logistics.tms.controller.workordercenter.response.WorkOrderProcessResponseModel;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/17
 */
@Api(value = "API - WorkOrderCenterApi- 工单中心", tags = "工单中心")
@RequestMapping("/service/workOrderCenter")
@RestController
public class WorkOrderCenterController{

	@Autowired
	private WorkOrderBiz workOrderBiz;

	/**
	 * 工单列表
	 *
	 * @param requestModel
	 * @return
	 */
	@ApiOperation(value = "工单列表")
	@PostMapping(value = "/workOrderList")
	public Result<PageInfo<WorkOrderListResponseModel>> workOrderList(@RequestBody WorkOrderListRequestModel requestModel) {
		return Result.success(workOrderBiz.workOrderList(requestModel));
	}

	/**
	 * 工单列表导出
	 *
	 * @param requestModel
	 * @return
	 */
	@IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.RESPONSE)
	@ApiOperation(value = "工单列表导出")
	@PostMapping(value = "/exportWorkOrderList")
	public Result<List<WorkOrderListResponseModel>> exportWorkOrderList(@RequestBody WorkOrderListRequestModel requestModel) {
		requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
		requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
		return Result.success(workOrderBiz.workOrderList(requestModel).getList());
	}

	/**
	 * 工单详情
	 *
	 * @param requestModel
	 * @return
	 */
	@ApiOperation(value = "工单详情")
	@PostMapping(value = "/workOrderDetail")
	public Result<WorkOrderDetailResponseModel> workOrderDetail(@RequestBody WorkOrderDetailRequestModel requestModel) {
		return Result.success(workOrderBiz.workOrderDetail(requestModel));
	}

	/**
	 * 工单详情-处理过程列表
	 *
	 * @param requestModel
	 * @return
	 */
	@ApiOperation(value = "工单处理过程列表")
	@PostMapping(value = "/workOrderProcess")
	public Result<List<WorkOrderProcessResponseModel>> workOrderProcess(@RequestBody WorkOrderDetailRequestModel requestModel) {
		return Result.success(workOrderBiz.workOrderProcessList(requestModel));
	}

	/**
	 * 撤销工单
	 *
	 * @param requestModel
	 * @return
	 */
	@ApiOperation(value = "撤销工单")
	@PostMapping(value = "/cancelWorkOrder")
	public Result<Boolean> cancelWorkOrder(@RequestBody CancelWorkOrderRequestModel requestModel) {
		workOrderBiz.cancelWorkOrder(requestModel);
		return Result.success(true);
	}

	/**
	 * 上报任务中心异常工单
	 *
	 * @param requestModel 请求Model
	 * @return boolean
	 */
	@ApiOperation(value = "上报任务中心异常工单")
	@PostMapping(value = "/reportWorkException")
	public Result<Boolean> reportWorkException(@RequestBody ReportWorkExceptionRequestModel requestModel) {
		return Result.success(workOrderBiz.reportWorkException(requestModel));
	}

	/**
	 * 重新上报异常
	 * @param requestModel 请求Model
	 * @return boolean
	 */
	@ApiOperation(value = "重新上报任务中心异常工单")
	@PostMapping(value = "/reReportWorkException")
	public Result<Boolean> reReportWorkException(@RequestBody ReReportWorkExceptionRequestModel requestModel) {
		return Result.success(workOrderBiz.reReportWorkException(requestModel));
	}
}
