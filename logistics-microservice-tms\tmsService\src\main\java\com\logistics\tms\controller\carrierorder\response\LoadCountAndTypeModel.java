package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author:lei.zhu
 * @date:2021/12/28 15:01:37
 */
@Data
public class LoadCountAndTypeModel {
    @ApiModelProperty(value = "预计提货数量")
    private Integer loadAmount;

    @ApiModelProperty("sku code")
    private String productTypeCode;

    @ApiModelProperty(value = "大类名称")
    private String categoryName;

    @ApiModelProperty(value = "货物名")
    private String sortName;

    @ApiModelProperty(value = "长")
    private String length;

    @ApiModelProperty(value = "宽")
    private String width;

    @ApiModelProperty(value = "高")
    private String height;
}
