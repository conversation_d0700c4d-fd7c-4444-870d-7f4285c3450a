package com.logistics.tms.mapper;

import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleSettlementRelationByInsuranceIdsModel;
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleTireListByVehicleSettlementIdResponseModel;
import com.logistics.tms.entity.TVehicleSettlementRelation;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TVehicleSettlementRelationMapper extends BaseMapper<TVehicleSettlementRelation> {

    int batchInsert(@Param("list")List<TVehicleSettlementRelation> list);

    List<TVehicleSettlementRelation> getByVehicleSettlementId(@Param("vehicleSettlementId")Long vehicleSettlementId);

    List<TVehicleSettlementRelation> getByObjectIdAndType(@Param("objectType") Integer objectType, @Param("objectId") Long objectId);

    List<TVehicleSettlementRelation> getByObjectIdsAndType(@Param("objectType") Integer objectType, @Param("objectIds") String objectIds);

    List<TVehicleSettlementRelation> getByTypeSettlementId(@Param("objectType") Integer objectType, @Param("vehicleSettlementId")Long vehicleSettlementId);

    List<GetVehicleSettlementRelationByInsuranceIdsModel> getByInsuranceIds(@Param("insuranceIds") String insuranceIds);

    List<GetVehicleTireListByVehicleSettlementIdResponseModel> getVehicleTireListBySettlementId(@Param("vehicleSettlementId") Long vehicleSettlementId);

    void delByIds(@Param("ids") String listToString, @Param("userName") String userName);

    List<GetVehicleTireListByVehicleSettlementIdResponseModel> getNotAssociateTireList(@Param("vehicleId") Long vehicleId);

    List<Long> getObjectIdByObjectType(@Param("objectType")Integer objectType);
}