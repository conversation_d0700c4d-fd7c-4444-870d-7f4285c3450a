package com.logistics.tms.api.impl.entrustsettlement;

import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.entrustsettlement.EntrustSettlementServiceApi;
import com.logistics.tms.api.feign.entrustsettlement.model.*;
import com.logistics.tms.biz.entrustsettlement.EntrustSettlementBiz;
import com.yelo.tools.utils.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/11 19:05
 */
@RestController
public class EntrustSettlementServiceApiImpl implements EntrustSettlementServiceApi {

    @Autowired
    private EntrustSettlementBiz settlementBiz;

    /**
     * 查询委托方结算列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<EntrustSettlementListResponseModel> entrustSettlementList(@RequestBody EntrustSettlementListRequestModel requestModel) {
        return Result.success(settlementBiz.entrustSettlementList(requestModel,true));
    }

    /**
     * 导出委托方结算列表
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<EntrustSettlementRowModel>> exportEntrustSettlementList(@RequestBody EntrustSettlementListRequestModel requestModel) {
        List<EntrustSettlementRowModel> list = new ArrayList<>();
        EntrustSettlementListResponseModel model = settlementBiz.entrustSettlementList(requestModel,false);
        if (model != null && ListUtils.isNotEmpty(model.getPageInfo().getList())){
            list = model.getPageInfo().getList();
        }
        return Result.success(list);
    }

    /**
     * 修改费用
     * @param requestModel
     * @return
     */
    @Override
    public Result modifyCost(@RequestBody ModifyCostRequestModel requestModel) {
        settlementBiz.modifyCost(requestModel);
        return Result.success(true);
    }

    /**
     * 结算详情，确认收款，回退界面
     * @param requestModel
     * @return
     */
    @Override
    public Result<GetSettlementDetailResponseModel> getSettlementDetail(@RequestBody GetSettlementDetailRequestModel requestModel) {
        return Result.success(settlementBiz.getSettlementDetail(requestModel));
    }

    /**
     * 结算详情，修改费用
     * @param requestModel
     * @return
     */
    @Override
    public Result<GetDetailResponseModel> getDetail(@RequestBody GetDetailRequestModel requestModel) {
        return Result.success(settlementBiz.getDetail(requestModel));
    }

    /**
     * 已收款
     * @param requestModel
     * @return
     */
    @Override
    public Result receiveMoney(@RequestBody GetSettlementDetailRequestModel requestModel) {
        settlementBiz.receiveMoney(requestModel);
        return Result.success(true);
    }

    /**
     * 退款
     * @param requestModel
     * @return
     */
    @Override
    public Result refund(@RequestBody RefundRequestModel requestModel) {
        settlementBiz.refund(requestModel);
        return Result.success(true);
    }
}
