package com.logistics.tms.controller.warehouseaddress.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/7/15 9:17
 */
@Data
public class SearchWarehouseAddressResponseModel {

    @ApiModelProperty("Id")
    private Long warehouseAddressId;

    @ApiModelProperty("仓库")
    private String warehouse;

    @ApiModelProperty("省ID")
    private Long provinceId;
    @ApiModelProperty("省")
    private String provinceName;

    @ApiModelProperty("市ID")
    private Long cityId;
    @ApiModelProperty("市")
    private String cityName;

    @ApiModelProperty("区ID")
    private Long areaId;
    @ApiModelProperty("区")
    private String areaName;

}
