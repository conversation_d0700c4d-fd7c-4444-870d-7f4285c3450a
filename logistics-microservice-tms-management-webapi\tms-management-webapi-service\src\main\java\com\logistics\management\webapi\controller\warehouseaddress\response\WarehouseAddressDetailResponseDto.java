package com.logistics.management.webapi.controller.warehouseaddress.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WarehouseAddressDetailResponseDto {
    @ApiModelProperty("货主名")
    private String companyEntrustName;
    @ApiModelProperty("ID")
    private String warehouseAddressId;
    @ApiModelProperty("仓库")
    private String warehouse;
    @ApiModelProperty("省ID")
    private String provinceId;
    @ApiModelProperty("市ID")
    private String cityId;
    @ApiModelProperty("区ID")
    private String areaId;
    @ApiModelProperty("省")
    private String provinceName;
    @ApiModelProperty("市")
    private String cityName;
    @ApiModelProperty("区")
    private String areaName;
}
