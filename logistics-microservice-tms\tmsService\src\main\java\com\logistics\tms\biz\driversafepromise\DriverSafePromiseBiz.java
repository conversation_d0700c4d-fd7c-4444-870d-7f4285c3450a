package com.logistics.tms.biz.driversafepromise;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.CopyFileTypeEnum;
import com.logistics.tms.base.enums.SafePromiseStatusEnum;
import com.logistics.tms.base.enums.StaffPropertyEnum;
import com.logistics.tms.base.utils.ListJoinResultUtil;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.driversafepromise.request.*;
import com.logistics.tms.controller.driversafepromise.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sj
 * @Date: 2019/11/4 15:26
 */
@Service
public class DriverSafePromiseBiz {

    @Resource
    private CommonBiz commonBiz;
    @Resource
    private TDriverSafePromiseMapper tDriverSafePromiseMapper;
    @Resource
    private TDriverSafePromiseRelationMapper tDriverSafePromiseRelationMapper;
    @Resource
    private TStaffBasicMapper tStaffBasicMapper;
    @Resource
    private TVehicleDrivingLicenseMapper tVehicleDrivingLicenseMapper;
    @Resource
    private TStaffVehicleRelationMapper tqStaffVehicleRelationMapper;

    /**
     * 列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<SearchSafePromiseListResponseModel> searchList(SearchSafePromiseListRequestModel requestModel) {
        requestModel.enablePaging();
        List<SearchSafePromiseListResponseModel> safePromiseList = tDriverSafePromiseMapper.searchList(requestModel);

        if (ListUtils.isNotEmpty(safePromiseList)) {
            List<Long> safePromiseIdList = safePromiseList.stream().map(SearchSafePromiseListResponseModel::getSafePromiseId).collect(Collectors.toList());
            List<SummarySavePromiseInfoModel> summaryList = tDriverSafePromiseRelationMapper.getSummaryBySafePromiseIds(StringUtils.listToString(safePromiseIdList, ','));
            ListJoinResultUtil.joinList(safePromiseList, summaryList,
                    (s, d) -> s.getSafePromiseId().equals(d.getSafePromiseId()),
                    (s, d) -> {
                        s.setHasSignCount(d.getHasSignCount());
                        s.setNotSignCount(d.getNotSignCount());
                    });
        }
        return new PageInfo<>(safePromiseList);
    }

    /**
     * 新增
     *
     * @param requestModel
     */
    @Transactional
    public void addSafePromise(AddSafePromiseRequestModel requestModel) {
        if (tDriverSafePromiseMapper.getCountByPeriod(requestModel.getPeriod()) > CommonConstant.INTEGER_ZERO) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_PROMISE_INFO_EXIST);
        }
        if (ListUtils.isEmpty(requestModel.getDriverList())) {
            throw new BizException(CarrierDataExceptionEnum.PLEASE_SELECT_DRIVER);
        }

        TDriverSafePromise addTDriverSafePromise = new TDriverSafePromise();
        addTDriverSafePromise.setPeriod(requestModel.getPeriod());
        addTDriverSafePromise.setContent(commonBiz.processReplaceTempPicture(requestModel.getContent(), CopyFileTypeEnum.DRIVER_SAFE_PROMISE_FILE, requestModel.getPeriod()));
        addTDriverSafePromise.setTitle(requestModel.getTitle());
        addTDriverSafePromise.setAgent(requestModel.getAgent());
        addTDriverSafePromise.setAttachmentUrl(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DRIVER_SAFE_PROMISE_FILE.getKey(), requestModel.getPeriod(), requestModel.getAttachmentUrl(), null));
        commonBiz.setBaseEntityAdd(addTDriverSafePromise, BaseContextHandler.getUserName());
        tDriverSafePromiseMapper.insertSelective(addTDriverSafePromise);

        List<TStaffBasic> staffList = tStaffBasicMapper.getInternalDriverByIds(StringUtils.listToString(requestModel.getDriverList(), ','));
        if (ListUtils.isEmpty(staffList)) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }

        List<TDriverSafePromiseRelation> batchAddList = new ArrayList<>();
        TDriverSafePromiseRelation addTDriverSafePromiseRelation;
        for (TStaffBasic tempStaffBasic : staffList) {
            if (!tempStaffBasic.getStaffProperty().equals(StaffPropertyEnum.OWN_STAFF.getKey()) && !tempStaffBasic.getStaffProperty().equals(StaffPropertyEnum.AFFILIATION_STAFF.getKey())) {
                throw new BizException(CarrierDataExceptionEnum.INTERNAL_DRIVER_ERROR);
            }
            addTDriverSafePromiseRelation = new TDriverSafePromiseRelation();
            addTDriverSafePromiseRelation.setSafePromiseId(addTDriverSafePromise.getId());
            addTDriverSafePromiseRelation.setStaffId(tempStaffBasic.getId());
            addTDriverSafePromiseRelation.setStaffName(tempStaffBasic.getName());
            addTDriverSafePromiseRelation.setStaffMobile(tempStaffBasic.getMobile());
            addTDriverSafePromiseRelation.setStaffProperty(tempStaffBasic.getStaffProperty());
            addTDriverSafePromiseRelation.setStatus(SafePromiseStatusEnum.WAIT.getKey());
            commonBiz.setBaseEntityAdd(addTDriverSafePromiseRelation, BaseContextHandler.getUserName());
            batchAddList.add(addTDriverSafePromiseRelation);
        }
        if (ListUtils.isNotEmpty(batchAddList)) {
            tDriverSafePromiseRelationMapper.batchInsert(batchAddList);
        }
    }


    /**
     * 详情
     *
     * @param requestModel
     * @return
     */
    public SafePromiseDetailResponseModel getDetail(SafePromiseDetailRequestModel requestModel) {
        TDriverSafePromise dbDriverSafePromise = tDriverSafePromiseMapper.selectByPrimaryKey(requestModel.getSafePromiseId());
        if (dbDriverSafePromise == null) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_PROMISE_INFO_NOT_EXIST);
        }

        SafePromiseDetailResponseModel responseModel = new SafePromiseDetailResponseModel();
        responseModel.setSafePromiseId(dbDriverSafePromise.getId());
        responseModel.setPeriod(dbDriverSafePromise.getPeriod());
        responseModel.setTitle(dbDriverSafePromise.getTitle());
        responseModel.setAgent(dbDriverSafePromise.getAgent());
        responseModel.setUploadTime(dbDriverSafePromise.getCreatedTime());
        responseModel.setContent(commonBiz.addRealPath(dbDriverSafePromise.getContent()));
        responseModel.setAttachmentUrl(dbDriverSafePromise.getAttachmentUrl());
        responseModel.setDriverList(new ArrayList<>());
        responseModel.setAllDriverList(new ArrayList<>());

        //详情中已签订列表,指定查询状态
        List<TDriverSafePromiseRelation> dbRelList = tDriverSafePromiseRelationMapper.getBySafePromiseIdAndStatus(dbDriverSafePromise.getId(), null);
        if (ListUtils.isNotEmpty(dbRelList)) {
            List<Long> staffIdList = dbRelList.stream().filter(o-> o.getStatus().equals(CommonConstant.INTEGER_ONE)).map(TDriverSafePromiseRelation::getStaffId).collect(Collectors.toList());
            responseModel.setDriverList(staffIdList);
            responseModel.setHasSignCount(ListUtils.isNotEmpty(dbRelList) ? dbRelList.size() : CommonConstant.INTEGER_ZERO);

            List<Long> allStaffIdList = dbRelList.stream().map(TDriverSafePromiseRelation::getStaffId).collect(Collectors.toList());
            responseModel.setAllDriverList(allStaffIdList);
        }

        return responseModel;
    }

    /**
     * 删除
     *
     * @param requestModel
     */
    @Transactional
    public void delSafePromise(DeleteSafePromiseRequestModel requestModel) {
        tDriverSafePromiseMapper.deleteSafePromiseById(requestModel.getSafePromiseId(), BaseContextHandler.getUserName(), new Date());
    }

    /**
     * 补发
     *
     * @param requestModel
     */
    @Transactional
    public void reissueSafePromise(ReissueSavePromiseRequestModel requestModel) {
        if (tDriverSafePromiseMapper.getCountById(requestModel.getSafePromiseId()) < CommonConstant.INTEGER_ONE) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_PROMISE_INFO_NOT_EXIST);
        }

        Map<Long, Long> dbStaffMap = new HashMap<>();
        List<TDriverSafePromiseRelation> relList = tDriverSafePromiseRelationMapper.getBySafePromiseIdAndStatus(requestModel.getSafePromiseId(), null);
        for (TDriverSafePromiseRelation tempTDriverSafePromiseRelation : relList) {
            dbStaffMap.put(tempTDriverSafePromiseRelation.getStaffId(), tempTDriverSafePromiseRelation.getId());
        }

        List<Long> driverIdList = new ArrayList<>();
        for (Long driverId : requestModel.getDriverInfoList()) {
            if (dbStaffMap.get(driverId) == null) {
                driverIdList.add(driverId);
            }
        }

        if (ListUtils.isNotEmpty(driverIdList)) {
            List<TStaffBasic> staffList = tStaffBasicMapper.getInternalDriverByIds(StringUtils.listToString(driverIdList, ','));
            if (ListUtils.isEmpty(staffList) || staffList.size() != driverIdList.size()) {
                throw new BizException(CarrierDataExceptionEnum.INTERNAL_DRIVER_ERROR);
            }

            List<TDriverSafePromiseRelation> batchAddList = new ArrayList<>();
            TDriverSafePromiseRelation driverSafePromiseRelation;
            String operatorName = BaseContextHandler.getUserName();
            Date now = new Date();
            for (TStaffBasic tStaffBasic : staffList) {
                driverSafePromiseRelation = new TDriverSafePromiseRelation();
                driverSafePromiseRelation.setSafePromiseId(requestModel.getSafePromiseId());
                driverSafePromiseRelation.setStaffId(tStaffBasic.getId());
                driverSafePromiseRelation.setStaffName(tStaffBasic.getName());
                driverSafePromiseRelation.setStaffMobile(tStaffBasic.getMobile());
                driverSafePromiseRelation.setStaffProperty(tStaffBasic.getStaffProperty());
                driverSafePromiseRelation.setStatus(SafePromiseStatusEnum.WAIT.getKey());
                driverSafePromiseRelation.setCreatedBy(operatorName);
                driverSafePromiseRelation.setCreatedTime(now);
                driverSafePromiseRelation.setLastModifiedBy(operatorName);
                driverSafePromiseRelation.setLastModifiedTime(now);
                batchAddList.add(driverSafePromiseRelation);
            }
            tDriverSafePromiseRelationMapper.batchInsert(batchAddList);
        }
    }

    /**
     * 签订列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<SearchSignSafePromiseListResponseModel> searchSignList(SearchSignSafePromiseListRequestModel requestModel) {
        requestModel.enablePaging();
        List<SearchSignSafePromiseListResponseModel> signSafePromiseList = tDriverSafePromiseRelationMapper.searchSignList(requestModel);
        return new PageInfo<>(signSafePromiseList);
    }

    /**
     * 签订详情
     *
     * @param requestModel
     * @return
     */
    public SignSafePromiseDetailResponseModel getSignDetail(SignSafePromiseDetailRequestModel requestModel) {
        TDriverSafePromise driverSafePromise = tDriverSafePromiseMapper.selectByPrimaryKey(requestModel.getSafePromiseId());
        if (driverSafePromise == null) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_PROMISE_INFO_NOT_EXIST);
        }

        TDriverSafePromiseRelation rel = tDriverSafePromiseRelationMapper.selectByPrimaryKey(requestModel.getSignSafePromiseId());
        if (rel == null) {
            throw new BizException(CarrierDataExceptionEnum.INTERNAL_DRIVER_ERROR);
        }

        SignSafePromiseDetailResponseModel responseModel = new SignSafePromiseDetailResponseModel();
        responseModel.setSafePromiseId(driverSafePromise.getId());
        responseModel.setSignSafePromiseId(rel.getId());
        responseModel.setHandPromiseUrl(rel.getHandPromiseUrl());
        responseModel.setSignResponsibilityUrl(rel.getSignResponsibilityUrl());
        responseModel.setStaffId(rel.getStaffId());
        responseModel.setStaffName(rel.getStaffName());
        responseModel.setVehicleNo(rel.getVehicleNo());

        if (SafePromiseStatusEnum.WAIT.getKey().equals(rel.getStatus())) {
            List<TStaffVehicleRelation> staffRelList = tqStaffVehicleRelationMapper.findRelationByStaffId(rel.getStaffId());
            if (ListUtils.isNotEmpty(staffRelList)) {
                List<Long> vehicleIdList = new ArrayList<>();
                for (TStaffVehicleRelation tempTStaffVehicleRelation : staffRelList) {
                    vehicleIdList.add(tempTStaffVehicleRelation.getVehicleId());
                }

                List<TVehicleDrivingLicense> drivingLicenseList = tVehicleDrivingLicenseMapper.getListByVehicleIds(StringUtils.listToString(vehicleIdList, ','));
                if (ListUtils.isNotEmpty(drivingLicenseList)) {
                    List<String> vehicleNoList = drivingLicenseList.stream().map(TVehicleDrivingLicense::getVehicleNo).distinct().collect(Collectors.toList());
                    responseModel.setVehicleNo(StringUtils.listToString(vehicleNoList, ','));
                }
                TStaffBasic dbTStaffBasic = tStaffBasicMapper.selectByPrimaryKey(rel.getStaffId());
                responseModel.setStaffName(dbTStaffBasic.getName());
            }

        }
        return responseModel;
    }

    /**
     * 签订列表-汇总
     *
     * @param responseModel
     * @return
     */
    public SummarySignSafePromiseResponseModel getSignSummary(SearchSignSafePromiseListRequestModel responseModel) {
        return tDriverSafePromiseRelationMapper.getSummarySignCount(responseModel);
    }


    /**
     * 小程序签订列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<SearchSafePromiseAppletListResponseModel> searchAppletList(SearchSafePromiseAppletListRequestModel requestModel) {
        Long staffId = commonBiz.getLoginDriverAppletUserId();
        requestModel.setStaffId(staffId);
        requestModel.enablePaging();
        List<SearchSafePromiseAppletListResponseModel> searchSafePromiseAppletListResponseModels = tDriverSafePromiseMapper.searchAppletList(requestModel);
        return new PageInfo<>(searchSafePromiseAppletListResponseModels);
    }

    /**
     * 小程序签订列表-汇总
     *
     * @param requestModel
     * @return
     */
    public SummarySignSafePromiseAppletResponseModel getAppletSignSummary(SearchSafePromiseAppletListRequestModel requestModel) {
        Long staffId = commonBiz.getLoginDriverAppletUserId();
        requestModel.setStaffId(staffId);
        return tDriverSafePromiseRelationMapper.getAppletSignSummary(requestModel);
    }


    /**
     * 签订列表 - 上传
     *
     * @param requestModel
     */
    @Transactional
    public void uploadSafePromise(UploadSafePromiseRequestModel requestModel) {
        TDriverSafePromiseRelation rel = tDriverSafePromiseRelationMapper.selectByPrimaryKey(requestModel.getSignSafePromiseId());
        if (rel == null) {
            throw new BizException(CarrierDataExceptionEnum.INTERNAL_DRIVER_ERROR);
        }
        TDriverSafePromise driverSafePromise = tDriverSafePromiseMapper.selectByPrimaryKey(rel.getSafePromiseId());
        if (driverSafePromise == null) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_PROMISE_INFO_NOT_EXIST);
        }
        boolean ifSign = false;
        if (SafePromiseStatusEnum.HAVE.getKey().equals(rel.getStatus())) {
            ifSign = true;
        }
        TDriverSafePromiseRelation upRel = new TDriverSafePromiseRelation();
        upRel.setId(requestModel.getSignSafePromiseId());
        //如果是未签订，则需要回填签字时间
        if (!ifSign) {
            upRel.setSignTime(new Date());
            upRel.setStatus(SafePromiseStatusEnum.HAVE.getKey());
            upRel.setStaffId(rel.getStaffId());
            //回填人员信息
            TStaffBasic tStaffBasic = tStaffBasicMapper.selectByPrimaryKey(rel.getStaffId());
            if (tStaffBasic == null) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
            }
            upRel.setStaffName(tStaffBasic.getName());
            upRel.setStaffMobile(tStaffBasic.getMobile());
            upRel.setStaffProperty(tStaffBasic.getStaffProperty());
            //查询人员关联车辆信息
            List<TStaffVehicleRelation> staffRelList = tqStaffVehicleRelationMapper.findRelationByStaffId(rel.getStaffId());
            if (ListUtils.isNotEmpty(staffRelList)) {
                List<Long> vehicleIdList = new ArrayList<>();
                for (TStaffVehicleRelation tempTStaffVehicleRelation : staffRelList) {
                    vehicleIdList.add(tempTStaffVehicleRelation.getVehicleId());
                }
                List<String> vehicleNoList = new ArrayList<>();
                List<TVehicleDrivingLicense> drivingLicenseList = tVehicleDrivingLicenseMapper.getListByVehicleIds(StringUtils.listToString(vehicleIdList, ','));
                if (ListUtils.isNotEmpty(drivingLicenseList)) {
                    for (TVehicleDrivingLicense tVehicleDrivingLicense : drivingLicenseList) {
                        vehicleNoList.add(tVehicleDrivingLicense.getVehicleNo());
                    }
                    upRel.setVehicleNo(StringUtils.listToString(vehicleNoList,','));
                }
            }
        }
        if (StringUtils.isNotBlank(requestModel.getHandPromiseUrl()) && !requestModel.getHandPromiseUrl().equals(rel.getHandPromiseUrl())) {
            upRel.setHandPromiseUrl(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DRIVER_SAFE_PROMISE_FILE.getKey(), driverSafePromise.getPeriod(), requestModel.getHandPromiseUrl(), null));
        }
        if (StringUtils.isNotBlank(requestModel.getSignResponsibilityUrl()) && !requestModel.getSignResponsibilityUrl().equals(rel.getSignResponsibilityUrl())) {
            upRel.setSignResponsibilityUrl(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DRIVER_SAFE_PROMISE_FILE.getKey(), driverSafePromise.getPeriod(), requestModel.getSignResponsibilityUrl(), null));
        }
        commonBiz.setBaseEntityModify(upRel, BaseContextHandler.getUserName());
        tDriverSafePromiseRelationMapper.updateByPrimaryKeySelective(upRel);
    }

    /**
     * 小程序承诺书详情
     * @param requestModel
     * @return
     */
    public SafePromiseAppletDetailResponseModel getAppletDetail(SafePromiseAppletDetailRequestModel requestModel) {
        SafePromiseAppletDetailResponseModel appletDetail = tDriverSafePromiseRelationMapper.getAppletDetail(requestModel.getRelationId());
        if (appletDetail == null) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_SAFE_PROMISE_INFO_NOT_EXIST);
        }
        appletDetail.setContent(commonBiz.addRealPath(appletDetail.getContent()));
        return appletDetail;

    }

}
