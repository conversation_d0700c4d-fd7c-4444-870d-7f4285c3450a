package com.logistics.management.webapi.client.shippingorder.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ExcelIgnoreUnannotated
public class SearchShippingOrderListResponseModel {

    /**
     * 运输单id
     */
    private Long shippingOrderId;

    /**
     * 状态：0 待审核，1 已审核，2 已驳回
     */
    private Integer status;

    /**
     * 运输单号
     */
    @ExcelProperty("零担单号")
    private String shippingOrderCode;

    /**
     * 调度单id
     */
    private Long dispatchOrderId;
    /**
     * 调度单号
     */
    @ExcelProperty("调度单号")
    private String dispatchOrderCode;

    /**
     * 多装
     */
    @ExcelProperty("多装")
    private Integer loadPointAmount;

    /**
     * 发货省市区
     */
    @ExcelProperty("发货省市区")
    private String loadAddress;

    /**
     * 收货省市区
     */
    @ExcelProperty("收货省市区")
    private String unloadAddress;

    /**
     * 预提数
     */
    @ExcelProperty("预提数")
    private BigDecimal expectAmount;

    /**
     * 签收数
     */
    @ExcelProperty("签收数")
    private BigDecimal signAmount;

    /**
     * 车主
     */
    @ExcelProperty("车主")
    private String companyCarrierName;

    /**
     * 车长
     */
    @ExcelProperty("车长")
    private BigDecimal vehicleLength;

    /**
     * 整车费用
     */
    @ExcelProperty("整车运费")
    private BigDecimal carrierFreight;

    /**
     * 串点费用
     */
    @ExcelProperty("串点费用")
    private BigDecimal crossPointFee;

    /**
     * 实际车主费用
     */
    @ExcelProperty("实际车主费用")
    private String actualCarrierFreight;

    /**
     * 车牌号
     */
    @ExcelProperty("车牌号")
    private String vehicleNo;

    /**
     * 司机
     */
    @ExcelProperty("司机")
    private String driver;

    /**
     * 调度人
     */
    @ExcelProperty("调度人")
    private String dispatchUserName;

    /**
     * 调度时间
     */
    @ExcelProperty("调度时间")
    private Date dispatchTime;

}