package com.logistics.management.webapi.controller.carrierorder.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2021/9/17 14:11
 */
@Data
@ExcelIgnoreUnannotated
public class SearchCarrierOrderListForLeYiResponseDto {

    @ApiModelProperty("运单ID")
    private String carrierOrderId = "";

    @ApiModelProperty("10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0 已取消 2已放空")
    private String status = "";

    @ExcelProperty("状态")
    @ApiModelProperty("运单状态描述")
    private String statusDesc = "";

    @ExcelProperty("运单号")
    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";

    @ApiModelProperty("纠错状态：0 待纠错，1 已纠错，2 无需纠错")
    private String correctStatus="";

    @ExcelProperty("纠错状态")
    @ApiModelProperty("纠错状态")
    private String correctStatusDesc="";

    @ApiModelProperty("出库状态：0 待出库，1 部分出库，2 已出库")
    private String outStatus = "";

    @ExcelProperty("出库状态")
    @ApiModelProperty("出库状态展示文本")
    private String outStatusLabel = "";

    @ApiModelProperty("需求单ID")
    private String demandOrderId = "";

    @ExcelProperty("需求单号")
    @ApiModelProperty("需求单号")
    private String demandOrderCode = "";

    @ExcelProperty("车牌")
    @ApiModelProperty("车牌号")
    private String vehicleNo = "";

    @ApiModelProperty("司机")
    private String driver = "";

    @ExcelProperty("司机姓名")
    private String exportDriverName="";

    @ExcelProperty("司机联系方式")
    private String exportDriverMobile="";

    @ApiModelProperty("1.3.2新增；时效要求：1 日常回收，2 加急或节假日回收")
    private String recycleTaskType = "";
    @ExcelProperty("时效要求")
    @ApiModelProperty("1.3.2新增；时效要求")
    private String recycleTaskTypeLabel = "";

    @ExcelProperty("司机运费合计(元)")
    @ApiModelProperty("司机运费")
    private String dispatchFreightFee="";

    @ExcelProperty("单位")
    @ApiModelProperty("货物单位")
    private String goodsUnit="";

    //发货地址
    @ExcelProperty("发货仓库")
    @ApiModelProperty("发货仓库")
    private String loadWarehouse = "";
    @ExcelProperty("发货省")
    @ApiModelProperty("发货地址")
    private String loadProvinceName = "";
    @ExcelProperty("发货市")
    @ApiModelProperty("发货地址")
    private String loadCityName = "";
    @ExcelProperty("发货区")
    @ApiModelProperty("发货地址")
    private String loadAreaName = "";
    @ExcelProperty("发货详细地址")
    @ApiModelProperty("发货地址")
    private String loadDetailAddress = "";
    private String loadAddress = "";//拼接后的数据，列表展示
    @ExcelProperty("实际发货地址")
    @ApiModelProperty("提货定位")
    private String loadLocation = "";
    @ExcelProperty("发货人姓名")
    @ApiModelProperty("发货人姓名")
    private String consignorName = "";
    @ExcelProperty("发货人联系方式")
    @ApiModelProperty("发货人联系方式")
    private String consignorMobile = "";

    @ExcelProperty("预计承运")
    @ApiModelProperty("预计承运数量")
    private String expectAmount = "";

    @ExcelProperty("实际提货")
    @ApiModelProperty("实际提货数量")
    private String loadAmount = "";

    @ExcelProperty("实际卸货")
    @ApiModelProperty("实际卸货数量")
    private String unloadAmount = "";

    @ExcelProperty("实际签收")
    @ApiModelProperty("实际签收数量")
    private String signAmount = "";

    @ExcelProperty("临时费用（元）")
    @ApiModelProperty("临时费用")
    private String otherFee = "";


    @ExcelProperty("临时费用费额（元）")
    @ApiModelProperty("临时费用费额 v2.44(2)")
    private String otherFeeFreight = "";



    @ExcelProperty("实际货主费用（元）")
    @ApiModelProperty("实际货主运费")
    private String entrustFreight = "";

    @ExcelProperty("车主运费金额(元)")
    @ApiModelProperty("车主费用合计")
    private String carrierPriceTotal = "";


    @ExcelProperty("运费费额(元)")
    @ApiModelProperty("运费费额 v2.44(2)")
    private String carrierPriceTotalFreight = "";


    @ExcelProperty("实际车主费用(元)")
    @ApiModelProperty("实际车主费用合计(加上临时费用的)")
    private String actualCarrierPriceTotal = "";

    @ExcelProperty("差异数")
    @ApiModelProperty("差异数量（实际提货数量-预计数量）")
    private String differenceAmount = "";

    @ExcelProperty("云仓异常数")
    @ApiModelProperty("云仓异常数")
    private String abnormalAmount = "";

    @ExcelProperty("回退数")
    @ApiModelProperty("回退数量（实际提货数量-预计提货数量）")
    private String backAmount = "";

    @ExcelProperty("预计提货时间")
    @ApiModelProperty("预计提货时间")
    private String expectLoadTime = "";

    @ExcelProperty("预计到货时间")
    @ApiModelProperty("预计到货时间")
    private String expectArrivalTime = "";


    //收货地址
    @ExcelProperty("收货仓库")
    @ApiModelProperty("收货仓库")
    private String unloadWarehouse = "";
    @ExcelProperty("收货省")
    @ApiModelProperty("收货省")
    private String unloadProvinceName = "";
    @ExcelProperty("收货市")
    @ApiModelProperty("收货市")
    private String unloadCityName = "";
    @ExcelProperty("收货区")
    @ApiModelProperty("收货区")
    private String unloadAreaName = "";
    @ExcelProperty("收货详细地址")
    @ApiModelProperty("收货详细地址")
    private String unloadDetailAddress = "";
    private String unloadAddress = "";//拼接后的数据，列表展示
    @ExcelProperty("实际收货地址")
    @ApiModelProperty("卸货定位")
    private String unloadLocation = "";
    @ExcelProperty("收货人姓名")
    @ApiModelProperty("收货人姓名")
    private String receiverName = "";
    @ExcelProperty("收货人联系方式")
    @ApiModelProperty("收货人联系方式")
    private String receiverMobile = "";

    @ExcelProperty("预计里程数")
    @ApiModelProperty("预计里程数")
    private String expectMileage="";

    @ExcelProperty("实际提货时间")
    @ApiModelProperty("实际提货时间")
    private String loadTime = "";

    @ExcelProperty("实际到货时间")
    @ApiModelProperty("实际到货时间")
    private String unloadTime = "";

    @ExcelProperty("实际签收时间")
    @ApiModelProperty("实际签收时间")
    private String signTime = "";

    @ExcelProperty("品名")
    @ApiModelProperty("品名")
    private String goodsName = "";

    @ExcelProperty("备注")
    @ApiModelProperty("备注")
    private String remark = "";

    @ExcelProperty("需求类型")
    @ApiModelProperty("需求类型")
    private String entrustType="";

    @ExcelProperty("下单部门")
    @ApiModelProperty("下单部门")
    private String publishOrgName = "";

    @ExcelProperty("调度人")
    @ApiModelProperty("调度人")
    private String dispatchUserName = "";

    @ExcelProperty("车主")
    @ApiModelProperty("车主")
    private String carrierCompany = "";

    @ExcelProperty("委托客户")
    @ApiModelProperty("货主")
    private String entrustCompany = "";

    @ExcelProperty("客户单号")
    @ApiModelProperty("客户单号")
    private String customerOrderCode = "";

    @ExcelProperty("运单生成时间")
    @ApiModelProperty("运单生成时间")
    private String dispatchTime = "";

    @ExcelProperty("调度单号")
    @ApiModelProperty("调度单号")
    private String dispatchOrderCode = "";

    @ExcelProperty("大区")
    @ApiModelProperty("大区")
    private String loadRegionName="";

    @ExcelProperty("负责人")
    @ApiModelProperty("大区负责人")
    private String loadRegionContactName = "";

    @ExcelProperty("提货时效")
    @ApiModelProperty("提货时效")
    private String loadValidity = "";

    @ExcelProperty("回单数")
    @ApiModelProperty("回单数")
    private String carrierOrderTicketsAmount = "";

    @ExcelProperty("配置距离KM")
    @ApiModelProperty("配置距离 1.3.5新增")
    private String configDistance = "";

    @ApiModelProperty("是否加急：0 否，1 是")
    private String ifUrgent = "";

    @ApiModelProperty("是否是额外补的运单  0：否 1：是  V2.6.8")
    private String ifExtCarrierOrder = "";


      @ApiModelProperty("是否显示补单按钮  0：否 1：是  V2.6.8")
    private String enableExtCarrierOrder= "";


}
