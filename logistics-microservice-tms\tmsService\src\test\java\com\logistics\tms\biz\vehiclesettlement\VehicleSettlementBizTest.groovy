package com.logistics.tms.biz.vehiclesettlement


import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel
import com.logistics.tms.base.enums.VehicleSettlementTypeEnum
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.controller.vehiclesettlement.request.CancelVehicleSettlementDetailRequestModel
import com.logistics.tms.controller.vehiclesettlement.request.CancelVehicleSettlementRequestModel
import com.logistics.tms.controller.vehiclesettlement.request.ConfirmSendToDriverRequestModel
import com.logistics.tms.controller.vehiclesettlement.request.ConfirmSettlementRequestModel
import com.logistics.tms.controller.vehiclesettlement.request.GetSettlementDriverRequestModel
import com.logistics.tms.controller.vehiclesettlement.request.SearchVehicleSettlementListRequestModel
import com.logistics.tms.controller.vehiclesettlement.request.SendDriverSettleStatementListRequestModel
import com.logistics.tms.controller.vehiclesettlement.request.SettleFreightRequestModel
import com.logistics.tms.controller.vehiclesettlement.request.SettlementStatementHandleRequestModel
import com.logistics.tms.controller.vehiclesettlement.request.UpdateVehicleSettlementTireRequestModel
import com.logistics.tms.controller.vehiclesettlement.request.VehicleInsuranceCostRequestModel
import com.logistics.tms.controller.vehiclesettlement.request.VehicleSettlementDetailRequestModel
import com.logistics.tms.controller.vehiclesettlement.request.VehicleSettlementIdRequestModel
import com.logistics.tms.controller.vehiclesettlement.request.VehicleSettlementKanBanRequestModel
import com.logistics.tms.controller.vehiclesettlement.response.CancelVehicleSettlementDetailResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.GetCarrierOrderByVehicleIdResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.GetGpsFeeByVehicleIdResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.GetInsuranceCostsByVehicleIdResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.GetLoanFeeByVehicleIdResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.GetOilFilledByVehicleIdResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.GetParkingFeeByVehicleIdResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.GetSettlementDriverModel
import com.logistics.tms.controller.vehiclesettlement.response.GetSettlementDriverResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleBySettlementMonthModel
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleSettlementDetailResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleTireByVehicleIdResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleTireByVehicleSettlementIdResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleTireListByVehicleSettlementIdResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.SearchVehicleSettlementListCountResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.SearchVehicleSettlementListResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.SendDriverSettleStatementListResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.SettleFreightDetailResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.SettlementStatementHandleDetailResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.SettlementStatementHandleModel
import com.logistics.tms.controller.vehiclesettlement.response.SettlementStatementRecordResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.VehicleInsuranceCostResponseModel
import com.logistics.tms.controller.vehiclesettlement.response.VehicleSettlementEventModel
import com.logistics.tms.controller.vehiclesettlement.response.VehicleSettlementKanBanResponseModel
import com.logistics.tms.entity.*
import com.logistics.tms.mapper.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class VehicleSettlementBizTest extends Specification {
    @Mock
    TVehicleSettlementMapper vehicleSettlementMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TCarrierOrderMapper carrierOrderMapper
    @Mock
    TVehicleTireMapper vehicleTireMapper
    @Mock
    TOilFilledMapper oilFilledMapper
    @Mock
    TGpsFeeMapper gpsFeeMapper
    @Mock
    TDeductingHistoryMapper deductingHistoryMapper
    @Mock
    TParkingFeeMapper parkingFeeMapper
    @Mock
    TLoanRecordsMapper loanRecordsMapper
    @Mock
    TInsuranceCostsMapper insuranceCostsMapper
    @Mock
    TInsuranceCostsRelationMapper insuranceCostsRelationMapper
    @Mock
    TCertificationPicturesMapper certificationPicturesMapper
    @Mock
    TLoanSettlementRecordMapper loanSettlementRecordMapper
    @Mock
    TInsuranceMapper insuranceMapper
    @Mock
    TVehicleBasicMapper vehicleBasicMapper
    @Mock
    TVehicleSettlementRelationMapper vehicleSettlementRelationMapper
    @Mock
    TVehicleSettlementDriverRelationMapper tVehicleSettlementDriverRelationMapper
    @Mock
    TStaffBasicMapper tStaffBasicMapper
    @Mock
    TVehicleSettlementPaymentMapper tVehicleSettlementPaymentMapper
    @Mock
    TVehicleSettlementEventsMapper tVehicleSettlementEventsMapper
    @Mock
    Logger log
    @InjectMocks
    VehicleSettlementBiz vehicleSettlementBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Vehicle Settlement List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(vehicleSettlementMapper.searchVehicleSettlementList(any())).thenReturn([new SearchVehicleSettlementListResponseModel()])

        expect:
        vehicleSettlementBiz.searchVehicleSettlementList(requestModel) == expectedResult

        where:
        requestModel                                  || expectedResult
        new SearchVehicleSettlementListRequestModel() || [new SearchVehicleSettlementListResponseModel()]
    }

    @Unroll
    def "search Vehicle Settlement List Count where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(vehicleSettlementMapper.searchVehicleSettlementListCount(any())).thenReturn(new SearchVehicleSettlementListCountResponseModel())

        expect:
        vehicleSettlementBiz.searchVehicleSettlementListCount(requestModel) == expectedResult

        where:
        requestModel                                  || expectedResult
        new SearchVehicleSettlementListRequestModel() || new SearchVehicleSettlementListCountResponseModel()
    }

    @Unroll
    def "get Vehicle Settlement Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(vehicleSettlementMapper.getVehicleSettlementDetail(anyLong())).thenReturn(new GetVehicleSettlementDetailResponseModel())
        when(vehicleSettlementMapper.getFirstNotSettlementByVehicleId(anyLong())).thenReturn(new TVehicleSettlement(status: 0, vehicleId: 1l, settlementMonth: "settlementMonth"))
        when(carrierOrderMapper.getCarrierOrderByIdsForSettlement(anyString())).thenReturn([new GetCarrierOrderByVehicleIdResponseModel()])
        when(vehicleTireMapper.getVehicleTireByIdsForSettlement(anyString())).thenReturn([new GetVehicleTireByVehicleIdResponseModel()])
        when(oilFilledMapper.getOilFilledByIdsForSettlement(anyString())).thenReturn([new GetOilFilledByVehicleIdResponseModel()])
        when(gpsFeeMapper.getCurrentDeductingByIdForSettlement(anyLong(), anyString())).thenReturn(new GetGpsFeeByVehicleIdResponseModel())
        when(parkingFeeMapper.getCurrentDeductingByIdForSettlement(anyLong(), anyString())).thenReturn(new GetParkingFeeByVehicleIdResponseModel())
        when(loanRecordsMapper.getSettlementRecordsByIdForSettlement(anyLong(), anyString())).thenReturn(new GetLoanFeeByVehicleIdResponseModel())
        when(insuranceCostsMapper.getByIdForSettlement(anyLong())).thenReturn(new GetInsuranceCostsByVehicleIdResponseModel())
        when(insuranceCostsMapper.getLastByVehicleId(anyLong())).thenReturn(new TInsuranceCosts(commercialInsuranceCost: 0 as BigDecimal, compulsoryInsuranceCost: 0 as BigDecimal, cargoInsuranceCost: 0 as BigDecimal, carrierInsuranceCost: 0 as BigDecimal))
        when(insuranceCostsRelationMapper.getByInsuranceCostsId(anyLong())).thenReturn([new TInsuranceCostsRelation(insuranceId: 1l)])
        when(insuranceMapper.getByIds(anyString())).thenReturn([new TInsurance(insuranceType: 0, statusType: 0, policyNo: "policyNo", premium: 0 as BigDecimal, unpaidPremium: 0 as BigDecimal, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime(), paymentOfVehicleAndVesselTax: 0 as BigDecimal)])
        when(insuranceMapper.getByVehicleIdAndPeriod(anyLong(), any())).thenReturn([new TInsurance(insuranceType: 0, statusType: 0, policyNo: "policyNo", premium: 0 as BigDecimal, unpaidPremium: 0 as BigDecimal, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime(), paymentOfVehicleAndVesselTax: 0 as BigDecimal)])
        when(vehicleBasicMapper.getVehicleBasicPropertyById(anyLong())).thenReturn(new VehicleBasicPropertyModel())
        when(vehicleSettlementRelationMapper.getByVehicleSettlementId(anyLong())).thenReturn([new TVehicleSettlementRelation(objectType: 0, objectId: 1l)])
        when(tVehicleSettlementEventsMapper.getByVehicleSettlementId(anyLong())).thenReturn([new VehicleSettlementEventModel()])

        expect:
        vehicleSettlementBiz.getVehicleSettlementDetail(requestModel) == expectedResult

        where:
        requestModel                              || expectedResult
        new VehicleSettlementDetailRequestModel() || new GetVehicleSettlementDetailResponseModel()
    }

    @Unroll
    def "get Insurance Info By Vehicle where insuranceCostId=#insuranceCostId and vehicleId=#vehicleId then expect: #expectedResult"() {
        given:
        when(insuranceCostsMapper.getByIdForSettlement(anyLong())).thenReturn(new GetInsuranceCostsByVehicleIdResponseModel())
        when(insuranceCostsMapper.getLastByVehicleId(anyLong())).thenReturn(new TInsuranceCosts(commercialInsuranceCost: 0 as BigDecimal, compulsoryInsuranceCost: 0 as BigDecimal, cargoInsuranceCost: 0 as BigDecimal, carrierInsuranceCost: 0 as BigDecimal))
        when(insuranceCostsRelationMapper.getByInsuranceCostsId(anyLong())).thenReturn([new TInsuranceCostsRelation(insuranceId: 1l)])
        when(insuranceMapper.getByIds(anyString())).thenReturn([new TInsurance(insuranceType: 0, statusType: 0, policyNo: "policyNo", premium: 0 as BigDecimal, unpaidPremium: 0 as BigDecimal, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime(), paymentOfVehicleAndVesselTax: 0 as BigDecimal)])
        when(insuranceMapper.getByVehicleIdAndPeriod(anyLong(), any())).thenReturn([new TInsurance(insuranceType: 0, statusType: 0, policyNo: "policyNo", premium: 0 as BigDecimal, unpaidPremium: 0 as BigDecimal, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime(), paymentOfVehicleAndVesselTax: 0 as BigDecimal)])
        when(vehicleBasicMapper.getVehicleBasicPropertyById(anyLong())).thenReturn(new VehicleBasicPropertyModel())

        expect:
        vehicleSettlementBiz.getInsuranceInfoByVehicle(vehicleId, insuranceCostId) == expectedResult

        where:
        insuranceCostId | vehicleId || expectedResult
        1l              | 1l        || [new VehicleInsuranceCostResponseModel()]
    }

    @Unroll
    def "confirm Settlement where requestModel=#requestModel"() {
        given:
        when(vehicleSettlementMapper.getFirstNotSettlementByVehicleId(anyLong())).thenReturn(new TVehicleSettlement(status: 0, vehicleId: 1l, vehicleNo: "vehicleNo", settlementMonth: "settlementMonth", actualExpensesPayable: 0 as BigDecimal, carrierOrderCount: 0, carrierFreight: 0 as BigDecimal, tireFee: 0 as BigDecimal, gpsFee: 0 as BigDecimal, parkingFee: 0 as BigDecimal, loanFee: 0 as BigDecimal, oilFilledFee: 0 as BigDecimal, vehicleClaimFee: 0 as BigDecimal, insuranceFee: 0 as BigDecimal, deductingFee: 0 as BigDecimal, remainingFee: 0 as BigDecimal, accidentInsuranceFee: 0 as BigDecimal, accidentInsuranceExpenseTotal: 0 as BigDecimal, accidentInsuranceClaimFee: 0 as BigDecimal, ifAdjustFee: 0, adjustFee: 0 as BigDecimal, adjustRemark: "adjustRemark", withdrawRemark: "withdrawRemark", remark: "remark"))
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(vehicleTireMapper.settlementOilFilledByIds(anyString(), anyString())).thenReturn(0)
        when(oilFilledMapper.settlementOilFilledByIds(anyString(), anyString())).thenReturn(0)
        when(gpsFeeMapper.getCurrentDeductingByIdForSettlement(anyLong(), anyString())).thenReturn(new GetGpsFeeByVehicleIdResponseModel())
        when(deductingHistoryMapper.batchInsert(any())).thenReturn(0)
        when(parkingFeeMapper.getCurrentDeductingByIdForSettlement(anyLong(), anyString())).thenReturn(new GetParkingFeeByVehicleIdResponseModel())
        when(loanRecordsMapper.getSettlementRecordsByIdForSettlement(anyLong(), anyString())).thenReturn(new GetLoanFeeByVehicleIdResponseModel())
        when(insuranceCostsRelationMapper.batchInsert(any())).thenReturn(0)
        when(insuranceMapper.getByIds(anyString())).thenReturn([new TInsurance(insuranceType: 0, statusType: 0, vehicleId: 1l, settlementStatus: 0, unpaidPremium: 0 as BigDecimal, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime())])
        when(insuranceMapper.batchUpdate(any())).thenReturn(0)
        when(vehicleBasicMapper.getVehicleBasicPropertyById(anyLong())).thenReturn(new VehicleBasicPropertyModel())
        when(vehicleSettlementRelationMapper.getByVehicleSettlementId(anyLong())).thenReturn([new TVehicleSettlementRelation(vehicleSettlementId: 1l, objectType: 0, objectId: 1l)])

        expect:
        vehicleSettlementBiz.confirmSettlement(requestModel)
        assert expectedResult == false

        where:
        requestModel                        || expectedResult
        new ConfirmSettlementRequestModel() || true
    }

    @Unroll
    def "insurance Cost Settlement where tVehicleSettlement=#tVehicleSettlement and insuranceCostList=#insuranceCostList and insuranceClaimsCost=#insuranceClaimsCost"() {
        given:
        when(insuranceCostsRelationMapper.batchInsert(any())).thenReturn(0)
        when(insuranceMapper.getByIds(anyString())).thenReturn([new TInsurance(insuranceType: 0, statusType: 0, vehicleId: 1l, settlementStatus: 0, unpaidPremium: 0 as BigDecimal, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime())])
        when(insuranceMapper.batchUpdate(any())).thenReturn(0)
        when(vehicleBasicMapper.getVehicleBasicPropertyById(anyLong())).thenReturn(new VehicleBasicPropertyModel())

        expect:
        vehicleSettlementBiz.insuranceCostSettlement(tVehicleSettlement, insuranceCostList, insuranceClaimsCost)
        assert expectedResult == false

        where:
        tVehicleSettlement                                                                                | insuranceCostList                        | insuranceClaimsCost || expectedResult
        new TVehicleSettlement(vehicleId: 1l, vehicleNo: "vehicleNo", settlementMonth: "settlementMonth") | [new VehicleInsuranceCostRequestModel()] | 0 as BigDecimal || true
    }

    @Unroll
    def "vehicle Settlement Kan Ban where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(vehicleSettlementMapper.vehicleSettlementKanBan(anyString())).thenReturn([new VehicleSettlementKanBanResponseModel()])

        expect:
        vehicleSettlementBiz.vehicleSettlementKanBan(requestModel) == expectedResult

        where:
        requestModel                              || expectedResult
        new VehicleSettlementKanBanRequestModel() || [new VehicleSettlementKanBanResponseModel()]
    }

    @Unroll
    def "get Driver where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tStaffBasicMapper.getDriverInfoForVehicleSettlement(anyString())).thenReturn([new GetSettlementDriverResponseModel()])

        expect:
        vehicleSettlementBiz.getDriver(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new GetSettlementDriverRequestModel() || [new GetSettlementDriverResponseModel()]
    }

    @Unroll
    def "confirm Send To Driver where requestModel=#requestModel"() {
        given:
        when(vehicleSettlementMapper.getVehicleSettlementByIds(anyString())).thenReturn([new TVehicleSettlement(status: 0, vehicleNo: "vehicleNo", settlementMonth: "settlementMonth")])
        when(vehicleSettlementMapper.batchUpdateSelective(any())).thenReturn(0)
        when(tVehicleSettlementDriverRelationMapper.batchInsertSelective(any())).thenReturn(0)
        when(tStaffBasicMapper.getStaffByIds(anyString())).thenReturn([new TStaffBasic(name: "name", mobile: "mobile")])

        expect:
        vehicleSettlementBiz.confirmSendToDriver(requestModel)
        assert expectedResult == false

        where:
        requestModel                          || expectedResult
        new ConfirmSendToDriverRequestModel() || true
    }

    @Unroll
    def "settlement Statement Record where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(vehicleSettlementMapper.getSettlementStatementRecordList(anyLong())).thenReturn(new SettlementStatementRecordResponseModel())

        expect:
        vehicleSettlementBiz.settlementStatementRecord(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new VehicleSettlementIdRequestModel() || new SettlementStatementRecordResponseModel()
    }

    @Unroll
    def "generate Vehicle Settlement"() {
        given:
        when(vehicleSettlementMapper.getVehicleByMonth(anyString())).thenReturn([1l])
        when(carrierOrderMapper.getVehicleBySettlementMonth(anyString())).thenReturn([new GetVehicleBySettlementMonthModel()])
        when(oilFilledMapper.getVehicleBySettlementMonth(anyString())).thenReturn([new GetVehicleBySettlementMonthModel()])
        when(gpsFeeMapper.getVehicleBySettlementMonth(anyString())).thenReturn([new GetVehicleBySettlementMonthModel()])
        when(parkingFeeMapper.getVehicleBySettlementMonth(anyString())).thenReturn([new GetVehicleBySettlementMonthModel()])
        when(loanRecordsMapper.getVehicleBySettlementMonth(anyString())).thenReturn([new GetVehicleBySettlementMonthModel()])
        when(vehicleBasicMapper.getVehicleNoByIds(anyString())).thenReturn([new VehicleBasicPropertyModel()])
        when(vehicleSettlementRelationMapper.batchInsert(any())).thenReturn(0)

        expect:
        vehicleSettlementBiz.generateVehicleSettlement()
        assert expectedResult == false

        where:
        expectedResult << true
    }

    @Unroll
    def "create Relation where typeEnum=#typeEnum and addRelationList=#addRelationList and vehicleId=#vehicleId and vehicleList=#vehicleList and vehicleSettlementId=#vehicleSettlementId"() {
        expect:
        vehicleSettlementBiz.createRelation(addRelationList, vehicleList, vehicleId, vehicleSettlementId, typeEnum)
        assert expectedResult == false

        where:
        typeEnum                          | addRelationList                                                                        | vehicleId | vehicleList                              | vehicleSettlementId || expectedResult
        VehicleSettlementTypeEnum.DEFAULT | [new TVehicleSettlementRelation(vehicleSettlementId: 1l, objectType: 0, objectId: 1l)] | 1l        | [new GetVehicleBySettlementMonthModel()] | 1l                  || true
    }

    @Unroll
    def "update Parking Gps Fee Cooperation Status"() {
        given:
        when(gpsFeeMapper.getNotTerminal()).thenReturn([new TGpsFee(startDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime(), finishDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime(), cooperationStatus: 0)])
        when(gpsFeeMapper.batchUpdate(any())).thenReturn(0)
        when(parkingFeeMapper.getNotTerminal()).thenReturn([new TParkingFee(startDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime(), finishDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime(), cooperationStatus: 0)])
        when(parkingFeeMapper.batchUpdate(any())).thenReturn(0)

        expect:
        vehicleSettlementBiz.updateParkingGpsFeeCooperationStatus()
        assert expectedResult == false

        where:
        expectedResult << true
    }

    @Unroll
    def "get Cooperation Status where endDate=#endDate and now=#now and startDate=#startDate then expect: #expectedResult"() {
        expect:
        vehicleSettlementBiz.getCooperationStatus(now, startDate, endDate) == expectedResult

        where:
        endDate                                                          | now                                                              | startDate                                                        || expectedResult
        new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime() | new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime() | new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime() || 0
    }

    @Unroll
    def "cancel Vehicle Settlement Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(vehicleSettlementMapper.getCancelVehicleSettlementDetailById(anyLong())).thenReturn(new CancelVehicleSettlementDetailResponseModel())

        expect:
        vehicleSettlementBiz.cancelVehicleSettlementDetail(requestModel) == expectedResult

        where:
        requestModel                                    || expectedResult
        new CancelVehicleSettlementDetailRequestModel() || new CancelVehicleSettlementDetailResponseModel()
    }

    @Unroll
    def "cancel Vehicle Settlement where requestModel=#requestModel"() {
        given:
        when(vehicleTireMapper.rollbackSettlementOilFilledByIds(anyString(), anyString())).thenReturn(0)
        when(oilFilledMapper.rollbackSettlementOilFilledByIds(anyString(), anyString())).thenReturn(0)
        when(deductingHistoryMapper.batchUpdate(any())).thenReturn(0)
        when(deductingHistoryMapper.getByTypeObjectIdMonth(anyInt(), anyLong(), anyString())).thenReturn(new TDeductingHistory(objectId: 1l, totalFee: 0 as BigDecimal, deductingFee: 0 as BigDecimal, remainingDeductingFee: 0 as BigDecimal))
        when(insuranceCostsMapper.getByIdForSettlement(anyLong())).thenReturn(new GetInsuranceCostsByVehicleIdResponseModel())
        when(insuranceCostsMapper.deleteInsuranceCostAndRelation(anyLong(), anyString())).thenReturn(0)
        when(certificationPicturesMapper.delByObjectTypeId(anyInt(), anyLong(), anyString())).thenReturn(0)
        when(loanSettlementRecordMapper.getByLoanRecordsIdMonth(anyLong(), anyString())).thenReturn(new TLoanSettlementRecord(loanRecordsId: 1l, settlementFee: 0 as BigDecimal, remainingRepaymentFee: 0 as BigDecimal, totalFee: 0 as BigDecimal))
        when(insuranceMapper.getByIds(anyString())).thenReturn([new TInsurance(insuranceType: 0, settlementStatus: 0, premium: 0 as BigDecimal, unpaidPremium: 0 as BigDecimal)])
        when(insuranceMapper.batchUpdate(any())).thenReturn(0)
        when(vehicleSettlementRelationMapper.getByVehicleSettlementId(anyLong())).thenReturn([new TVehicleSettlementRelation(objectType: 0, objectId: 1l)])
        when(tVehicleSettlementDriverRelationMapper.getByVehicleSettlementId(anyLong())).thenReturn(new TVehicleSettlementDriverRelation(status: 0, reason: "reason", confirmTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 42).getTime()))
        when(tVehicleSettlementEventsMapper.rollbackToWaitSettleStatement(anyLong(), anyString())).thenReturn(0)

        expect:
        vehicleSettlementBiz.cancelVehicleSettlement(requestModel)
        assert expectedResult == false

        where:
        requestModel                              || expectedResult
        new CancelVehicleSettlementRequestModel() || true
    }

    @Unroll
    def "settle Freight Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(vehicleSettlementMapper.settleFreightDetailById(anyLong())).thenReturn(new SettleFreightDetailResponseModel())

        expect:
        vehicleSettlementBiz.settleFreightDetail(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new VehicleSettlementIdRequestModel() || new SettleFreightDetailResponseModel()
    }

    @Unroll
    def "settle Freight where requestModel=#requestModel"() {
        given:
        when(vehicleSettlementMapper.settleFreightDetailById(anyLong())).thenReturn(new SettleFreightDetailResponseModel())

        expect:
        vehicleSettlementBiz.settleFreight(requestModel)
        assert expectedResult == false

        where:
        requestModel                    || expectedResult
        new SettleFreightRequestModel() || true
    }

    @Unroll
    def "settlement Statement Handle Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(vehicleSettlementMapper.settlementStatementHandleDetail(anyLong())).thenReturn(new SettlementStatementHandleDetailResponseModel())

        expect:
        vehicleSettlementBiz.settlementStatementHandleDetail(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new VehicleSettlementIdRequestModel() || new SettlementStatementHandleDetailResponseModel()
    }

    @Unroll
    def "settlement Statement Handle where requestModel=#requestModel"() {
        given:
        when(vehicleSettlementMapper.settlementStatementHandleInfo(anyLong())).thenReturn(new SettlementStatementHandleModel())
        when(vehicleTireMapper.rollbackSettlementOilFilledByIds(anyString(), anyString())).thenReturn(0)
        when(oilFilledMapper.rollbackSettlementOilFilledByIds(anyString(), anyString())).thenReturn(0)
        when(deductingHistoryMapper.batchUpdate(any())).thenReturn(0)
        when(deductingHistoryMapper.getByTypeObjectIdMonth(anyInt(), anyLong(), anyString())).thenReturn(new TDeductingHistory(objectId: 1l, totalFee: 0 as BigDecimal, deductingFee: 0 as BigDecimal, remainingDeductingFee: 0 as BigDecimal))
        when(insuranceCostsMapper.getByIdForSettlement(anyLong())).thenReturn(new GetInsuranceCostsByVehicleIdResponseModel())
        when(insuranceCostsMapper.deleteInsuranceCostAndRelation(anyLong(), anyString())).thenReturn(0)
        when(certificationPicturesMapper.delByObjectTypeId(anyInt(), anyLong(), anyString())).thenReturn(0)
        when(loanSettlementRecordMapper.getByLoanRecordsIdMonth(anyLong(), anyString())).thenReturn(new TLoanSettlementRecord(loanRecordsId: 1l, settlementFee: 0 as BigDecimal, remainingRepaymentFee: 0 as BigDecimal, totalFee: 0 as BigDecimal))
        when(insuranceMapper.getByIds(anyString())).thenReturn([new TInsurance(insuranceType: 0, settlementStatus: 0, premium: 0 as BigDecimal, unpaidPremium: 0 as BigDecimal)])
        when(insuranceMapper.batchUpdate(any())).thenReturn(0)
        when(vehicleSettlementRelationMapper.getByVehicleSettlementId(anyLong())).thenReturn([new TVehicleSettlementRelation(objectType: 0, objectId: 1l)])
        when(tVehicleSettlementEventsMapper.rollbackToWaitSettleStatement(anyLong(), anyString())).thenReturn(0)

        expect:
        vehicleSettlementBiz.settlementStatementHandle(requestModel)
        assert expectedResult == false

        where:
        requestModel                                || expectedResult
        new SettlementStatementHandleRequestModel() || true
    }

    @Unroll
    def "send Driver Settle Statement List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(vehicleSettlementMapper.sendDriverSettleStatementList(any())).thenReturn([new SendDriverSettleStatementListResponseModel()])
        when(vehicleSettlementMapper.getSettlementVehicleDriverInfoByIds(anyString())).thenReturn([new GetSettlementDriverModel()])
        when(vehicleSettlementMapper.getSettlementVehicleDriverInfoNoCarrier(anyString())).thenReturn([new GetSettlementDriverModel()])

        expect:
        vehicleSettlementBiz.sendDriverSettleStatementList(requestModel) == expectedResult

        where:
        requestModel                                    || expectedResult
        new SendDriverSettleStatementListRequestModel() || [new SendDriverSettleStatementListResponseModel()]
    }

    @Unroll
    def "update Vehicle Settlement Tire where requestModel=#requestModel"() {
        given:
        when(vehicleTireMapper.getByIds(anyString())).thenReturn([new TVehicleTire(settlementStatus: 0)])
        when(vehicleSettlementRelationMapper.batchInsert(any())).thenReturn(0)
        when(vehicleSettlementRelationMapper.getByTypeSettlementId(anyInt(), anyLong())).thenReturn([new TVehicleSettlementRelation(vehicleSettlementId: 1l, objectType: 0, objectId: 1l)])

        expect:
        vehicleSettlementBiz.updateVehicleSettlementTire(requestModel)
        assert expectedResult == false

        where:
        requestModel                                  || expectedResult
        new UpdateVehicleSettlementTireRequestModel() || true
    }

    @Unroll
    def "get Vehicle Tire By Vehicle Settlement Id where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(vehicleSettlementRelationMapper.getVehicleTireListBySettlementId(anyLong())).thenReturn([new GetVehicleTireListByVehicleSettlementIdResponseModel()])
        when(vehicleSettlementRelationMapper.getNotAssociateTireList(anyLong())).thenReturn([new GetVehicleTireListByVehicleSettlementIdResponseModel()])
        when(tStaffBasicMapper.getStaffByIds(anyString())).thenReturn([new TStaffBasic(name: "name", mobile: "mobile")])

        expect:
        vehicleSettlementBiz.getVehicleTireByVehicleSettlementId(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new VehicleSettlementIdRequestModel() || new GetVehicleTireByVehicleSettlementIdResponseModel()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme