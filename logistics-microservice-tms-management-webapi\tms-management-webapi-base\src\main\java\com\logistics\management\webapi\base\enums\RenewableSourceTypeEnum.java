package com.logistics.management.webapi.base.enums;

public enum RenewableSourceTypeEnum {

    YELOLIFE_SYNC(1,"新生同步"),
    DRIVER_CONFIRM(2,"司机确认");

    private Integer key;
    private String value;

    RenewableSourceTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
