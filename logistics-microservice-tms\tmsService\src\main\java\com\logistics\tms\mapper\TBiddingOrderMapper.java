package com.logistics.tms.mapper;

import com.logistics.tms.biz.biddingorder.bo.*;
import com.logistics.tms.entity.TBiddingOrder;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/04/26
*/
@Mapper
public interface TBiddingOrderMapper extends BaseMapper<TBiddingOrder> {

    int insertSelectiveBackKey(TBiddingOrder tBiddingOrder);

    /**
     * 后台列表
     * @param searchBiddingOrderListReqBo
     * @return {@link List}<{@link SearchBiddingOrderListRespBo}>
     */
    List<SearchBiddingOrderListRespBo> searchBiddingOrderListByManager(SearchBiddingOrderListReqBo searchBiddingOrderListReqBo);

    /**
     * 前台列表
     * @param reqBo
     * @return {@link List}<{@link SearchBiddingOrderListByCustomerRespBo}>
     */
    List<SearchBiddingOrderListByCustomerRespBo> searchBiddingOrderListByCustomer(SearchBiddingOrderListByCustomerReqBo reqBo);

    /**
     * 更新状态BY CAS
     *
     * @param updateBiddingOrderStateBo
     * @return int
     */
    int updateBiddingOrderStateByCAS(UpdateBiddingOrderStateBo updateBiddingOrderStateBo);

}