package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2021/10/18 17:01
 */
@Data
public class LogisticsCostStatisticsRequestDto {
    @ApiModelProperty("日期范围：起始时间（yyyy-MM）")
    @NotBlank(message = "请选择日期")
    private String yearMonthStart;
    @ApiModelProperty("日期范围：结束时间（yyyy-MM）")
    @NotBlank(message = "请选择日期")
    private String yearMonthEnd;
}
