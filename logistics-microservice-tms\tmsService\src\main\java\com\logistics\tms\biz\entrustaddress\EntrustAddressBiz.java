package com.logistics.tms.biz.entrustaddress;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.entrustaddress.model.GetAddressByCompanyNameRequestModel;
import com.logistics.tms.api.feign.entrustaddress.model.GetAddressByCompanyNameResponseModel;
import com.logistics.tms.api.feign.entrustaddress.model.SearchEntrustAddressRequestModel;
import com.logistics.tms.api.feign.entrustaddress.model.SearchEntrustAddressResponseModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.mapper.TEntrustAddressMapper;
import com.yelo.tools.utils.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/9/19 11:53
 */
@Service
public class EntrustAddressBiz {

    @Autowired
    private TEntrustAddressMapper entrustAddressMapper;

    @Autowired
    private CommonBiz commonBiz;

    /**
     * 根据仓库或详细地址模糊搜索带出地址（委托发布）
     *
     * @param requestModel
     * @return
     */
    public PageInfo<GetAddressByCompanyNameResponseModel> getAddressByCompanyNameOrWarehouse(GetAddressByCompanyNameRequestModel requestModel) {
        requestModel.enablePaging();
        return new PageInfo<>(entrustAddressMapper.getAddressByCompanyNameOrWarehouse(requestModel));
    }

    /**
     * 收发货地址列表
     *
     * @param requestModel
     * @return
     */
    public List<SearchEntrustAddressResponseModel> searchList(SearchEntrustAddressRequestModel requestModel) {
        requestModel.enablePaging();
        List<SearchEntrustAddressResponseModel> list = entrustAddressMapper.searchList(requestModel.getAddressType());
        if (ListUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        return list;
    }
}
