package com.logistics.appapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2018/10/16 14:00
 */
@Data
public class CarrierOrderBillResponseDto {
    @ApiModelProperty("单据类型：（1 提货单，2 出库单，3 签收单，4 其他， 5 到库凭证，8 提货现场图片，9 卸货现场图片）")
    private String ticketType = "";
    @ApiModelProperty("单据路径")
    private List<String> ticketPath;
}
