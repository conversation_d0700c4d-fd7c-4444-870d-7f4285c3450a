package com.logistics.management.webapi.api.impl.transmap;

import com.logistics.management.webapi.api.feign.transmap.TransMapApi;
import com.logistics.management.webapi.api.feign.transmap.dto.VehicleTrackInfoItem;
import com.logistics.management.webapi.api.feign.transmap.dto.*;
import com.logistics.management.webapi.api.impl.transmap.mapping.CurrentPositionMapping;
import com.logistics.tms.api.feign.gpstrack.GpsTrackServiceApi;
import com.logistics.tms.api.feign.gpstrack.model.*;
import com.yelo.basicdata.api.feign.thirdpartclient.ZjxlClient;
import com.yelo.basicdata.api.feign.thirdpartclient.entity.zjxl.TransBaseResponseModel;
import com.yelo.basicdata.api.feign.thirdpartclient.entity.zjxl.VehicleRequestModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


@RestController
public class TransMapImpl implements TransMapApi {

    @Autowired
    private ZjxlClient zjxlClient;
    @Autowired
    private GpsTrackServiceApi gpsTrackServiceApi;

    /**
     * 根据货运车牌号，检查车辆注册信息是否存在于运输系统监控网络
     * @param requestDto
     * @return
     */
    @Override
    public Result<TransBaseResponseDto> checkTruckExist(@RequestBody @Valid VehicleRequestDto requestDto) {
        Result<TransBaseResponseModel> result = zjxlClient.checkTruckExist(MapperUtils.mapper(requestDto, VehicleRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), TransBaseResponseDto.class));
    }

    /**
     * 根据货运车牌号以及运行时间段，获取车辆历史定位信息
     * @param requestDto
     * @return
     */
    @Override
    public Result<OpGpHisTrackResponseDto> opGpHisTrack(@RequestBody @Valid OpGpHisTrackRequestDto requestDto) {
        Result<OpGpHisTrackResponseModel> result = gpsTrackServiceApi.getVehicleTrackHistory(MapperUtils.mapper(requestDto, OpGpHisTrackRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), OpGpHisTrackResponseDto.class));
    }

    /**
     * 根据货运车牌号查找最新位置和运单终点信息
     * @param requestDto
     * @return
     */
    @Override
    public Result<SearchCarrierOrderDestinationByVehicleNoResponseDto> currentPosition(@RequestBody @Valid SearchCarrierOrderDestinationByVehicleNoRequestDto requestDto) {
        Result<SearchCarrierOrderDestinationByVehicleNoResponseModel> result = gpsTrackServiceApi.getDestinationByVehicleNo(MapperUtils.mapper(requestDto,SearchCarrierOrderDestinationByVehicleNoRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),SearchCarrierOrderDestinationByVehicleNoResponseDto.class,new CurrentPositionMapping()));
    }

    /**
     * 查询所有车辆信息
     * @param requestDto
     * @return
     */
    @Override
    public Result<AllVehicleTrackInfoResponseDto> getVehicleTrackInfo(@RequestBody AllVehicleTrackInfoRequestDto requestDto) {
        Result<AllVehicleTrackInfoResponseModel> result = gpsTrackServiceApi.getVehicleTrackInfoList(MapperUtils.mapper(requestDto,AllVehicleTrackInfoRequestModel.class));
        result.throwException();
        AllVehicleTrackInfoResponseDto allVehicleTrackInfoResponseDto = MapperUtils.mapper(result.getData(),AllVehicleTrackInfoResponseDto.class);
        allVehicleTrackInfoResponseDto.getVehicleTrackInfoList().setList(MapperUtils.mapper(result.getData().getVehicleTrackInfoList().getList(),VehicleTrackInfoItem.class));
        return Result.success(allVehicleTrackInfoResponseDto);
    }

    /**
     * 刷新本地车辆定位信息
     * @return
     */
    @Override
    public Result refreshLocation() {
        return gpsTrackServiceApi.refreshLocation();
    }
}
