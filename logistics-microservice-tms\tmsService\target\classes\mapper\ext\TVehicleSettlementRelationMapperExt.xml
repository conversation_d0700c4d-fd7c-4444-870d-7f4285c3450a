<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleSettlementRelationMapper" >
  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TVehicleSettlementRelation" >
    <foreach collection="list" item="item" separator=";">
      insert into t_vehicle_settlement_relation
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.vehicleSettlementId != null" >
          vehicle_settlement_id,
        </if>
        <if test="item.objectType != null" >
          object_type,
        </if>
        <if test="item.objectId != null" >
          object_id,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.vehicleSettlementId != null" >
          #{item.vehicleSettlementId,jdbcType=BIGINT},
        </if>
        <if test="item.objectType != null" >
          #{item.objectType,jdbcType=INTEGER},
        </if>
        <if test="item.objectId != null" >
          #{item.objectId,jdbcType=BIGINT},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

  <select id="getByVehicleSettlementId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_vehicle_settlement_relation
    where valid = 1
    and vehicle_settlement_id = #{vehicleSettlementId,jdbcType=BIGINT}
  </select>
    <select id="getByObjectIdAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_vehicle_settlement_relation
        where valid=1
        and object_type = #{objectType,jdbcType=INTEGER}
        and object_id = #{objectId,jdbcType=BIGINT}
    </select>
    <select id="getByObjectIdsAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_vehicle_settlement_relation
        where valid=1
        and object_type = #{objectType,jdbcType=INTEGER}
        and object_id in (${objectIds})
    </select>
    <select id="getByTypeSettlementId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_vehicle_settlement_relation
        where valid=1
        and object_type = #{objectType,jdbcType=INTEGER}
        and vehicle_settlement_id =#{vehicleSettlementId,jdbcType=BIGINT}
    </select>
    <resultMap id="GetByInsuranceIdsMap"
               type="com.logistics.tms.controller.vehiclesettlement.response.GetVehicleSettlementRelationByInsuranceIdsModel">
        <id column="id" property="insuranceId" jdbcType="BIGINT"/>
        <collection property="vehicleSettlementRelationList"
                    ofType="com.logistics.tms.controller.vehiclesettlement.response.GetVehicleSettlementRelationByInsuranceIdsBaseModel">
            <result column="vehicleSettlementRelationId" property="id" jdbcType="BIGINT"/>
            <result column="vehicle_settlement_id" property="vehicleSettlementId" jdbcType="BIGINT"/>
            <result column="object_id" property="objectId" jdbcType="BIGINT"/>
        </collection>
    </resultMap>
    <select id="getByInsuranceIds" resultMap="GetByInsuranceIdsMap">
        select ti.id,
               tvsr.id as vehicleSettlementRelationId,
               tvsr.vehicle_settlement_id,
               tvsr.object_id
        from t_insurance ti
        inner join t_insurance_costs_relation ticr on ticr.insurance_id = ti.id and ticr.valid = 1
        inner join t_vehicle_settlement_relation tvsr on tvsr.object_id = ticr.insurance_costs_id and tvsr.object_type = 60 and tvsr.valid = 1
        where ti.valid = 1
        and ti.id in (${insuranceIds})
    </select>
    <select id="getVehicleTireListBySettlementId" resultMap="getNotAssociateTireListMap">
        select
        tvt.id as vehicleTireId,
        tvt.replace_date ,
        tvt.staff_id ,
        tvt.last_modified_time ,
        tvtn.amount,
        tvtn.unit_price
        from t_vehicle_settlement_relation tvsr
        left join t_vehicle_tire tvt on tvt.id=tvsr.object_id and tvt.valid=1
        left join t_vehicle_tire_no tvtn on tvt.id=tvtn.tire_id and tvtn.valid=1
        where tvsr.valid=1
        and tvsr.object_type=40
        and tvsr.vehicle_settlement_id=#{vehicleSettlementId,jdbcType=BIGINT}
    </select>
    <update id="delByIds">
        update t_vehicle_settlement_relation
        set valid=0,last_modified_time=now(),last_modified_by=#{userName,jdbcType=VARCHAR}
        where id in (${ids})
    </update>
    <resultMap id="getNotAssociateTireListMap" type="com.logistics.tms.controller.vehiclesettlement.response.GetVehicleTireListByVehicleSettlementIdResponseModel">
        <result column="vehicleTireId" property="vehicleTireId"/>
        <result column="replace_date" property="replaceDate"/>
        <result column="staff_id" property="staffId"/>
        <result column="last_modified_time" property="lastModifiedTime"/>
        <collection property="tireNoList" ofType="com.logistics.tms.controller.vehiclesettlement.response.VehicleTireNoModel">
            <result column="amount" property="amount"/>
            <result column="unit_price" property="unitPrice"/>
        </collection>
    </resultMap>
    <select id="getNotAssociateTireList" resultMap="getNotAssociateTireListMap">
        select
        tvt.id as vehicleTireId,
        tvt.staff_id,
        tvt.replace_date,
        tvt.last_modified_time,
        tvtn.amount,
        tvtn.unit_price
        from t_vehicle_tire tvt
        left join t_vehicle_tire_no tvtn on tvtn.tire_id = tvt.id and tvtn.valid = 1
        where tvt.valid = 1
        and tvt.vehicle_id = #{vehicleId,jdbcType=BIGINT}
        and tvt.id not in (select tvsr.object_id from t_vehicle_settlement_relation tvsr where tvsr.object_type=40 and tvsr.valid=1)
    </select>

    <select id="getObjectIdByObjectType" resultType="java.lang.Long">
        select
        object_id
        from t_vehicle_settlement_relation
        where valid = 1
        and object_type = #{objectType,jdbcType=INTEGER}
    </select>
</mapper>