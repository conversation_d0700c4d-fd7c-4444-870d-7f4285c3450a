package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;

/**
 * 中石化需求单确定发布请求实体类
 *
 * @author: wei.wang
 * @date: 2021/12/3
 */
@Data
public class BatchPublishSinopecDemandRequestDto {

	@ApiModelProperty("调度人员姓名")
	@Pattern(regexp = "^[\\u4e00-\\u9fa5]{2,20}$", message = "调度人员姓名不许为空且必须是汉字并且长度控制在2~20字内")
	private String dispatcherName;
	@ApiModelProperty("调度人员电话")
	@Size(min = 1, max = 50, message = "调度人员电话不许为空且长度控制在1~50个字符内")
	private String dispatcherPhone;

	@ApiModelProperty("单价")
	private String contractPrice;//固定是单价

	@ApiModelProperty(value = "是否我司 1:我司,2:其他车主", required = true)
	@NotBlank(message = "请选择接单车主类型")
	private String isOurCompany;
	@ApiModelProperty(value = "车主id")
	private String companyCarrierId;
	@ApiModelProperty(value = "车主价格：单价")
	private String carrierPrice;

	@ApiModelProperty(value = "需求单列表",required = true)
	@NotNull(message = "需求单信息不能为空")
	@Valid
	private List<BatchPublishSinopecDemandDetailDto> sinopecDemands;
}
