package com.logistics.tms.biz.carrierorderapplet


import com.logistics.tms.base.constant.ConfigKeyConstant
import com.logistics.tms.base.enums.CarrierOrderEventsTypeEnum
import com.logistics.tms.base.enums.CarrierOrderOperateLogsTypeEnum
import com.logistics.tms.base.enums.CarrierOrderTicketsTypeEnum
import com.logistics.tms.base.enums.CopyFileTypeEnum
import com.logistics.tms.biz.carrierorder.CarrierOrderBiz
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.biz.extvehiclesettlement.ExtVehicleSettlementBiz
import com.logistics.tms.controller.carrierorder.response.GetTicketsResponseModel
import com.logistics.tms.controller.carrierorderapplet.request.CarrierOrderArriveLoadUnloadRequestModel
import com.logistics.tms.controller.carrierorderapplet.request.CarrierOrderIdRequestModel
import com.logistics.tms.controller.carrierorderapplet.request.CarrierOrderLoadRequestModel
import com.logistics.tms.controller.carrierorderapplet.request.SearchCarrierOrderListAppRequestModel
import com.logistics.tms.controller.carrierorderapplet.response.CarrierOrderBillResponseModel
import com.logistics.tms.controller.carrierorderapplet.response.CarrierOrderDetailAppResponseModel
import com.logistics.tms.controller.carrierorderapplet.response.CarrierOrderLogisticsDetailResponseModel
import com.logistics.tms.controller.carrierorderapplet.response.SearchCarrierOrderCountResponseModel
import com.logistics.tms.controller.carrierorderapplet.response.SearchCarrierOrderListAppResponseModel
import com.logistics.tms.entity.TCarrierOrder
import com.logistics.tms.entity.TCarrierOrderGoods
import com.logistics.tms.entity.TCarrierOrderVehicleHistory
import com.logistics.tms.entity.TDemandOrder
import com.logistics.tms.mapper.*
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz
import com.yelo.basicdata.api.feign.user.UserServiceApi
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class CarrierOrderAppletBizTest extends Specification {
    @Mock
    TCarrierOrderMapper carrierOrderMapper
    @Mock
    TCarrierOrderTicketsMapper carrierOrderTicketsMapper
    @Mock
    TCarrierOrderGoodsMapper carrierOrderGoodsMapper
    @Mock
    TCarrierOrderOperateLogsMapper carrierOrderOperateLogsMapper
    @Mock
    TCarrierOrderEventsMapper carrierOrderEventsMapper
    @Mock
    TCarrierOrderVehicleHistoryMapper carrierOrderVehicleHistoryMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    ConfigKeyConstant configKeyConstant
    @Mock
    CarrierOrderCommonBiz carrierOrderCommonBiz
    @Mock
    UserServiceApi userServiceApi
    @Mock
    TStaffBasicMapper staffBasicMapper
    @Mock
    ExtVehicleSettlementBiz extVehicleSettlementBiz
    @Mock
    TDemandOrderMapper tDemandOrderMapper
    @Mock
    CarrierOrderBiz carrierOrderBiz
    @Mock
    RabbitMqPublishBiz rabbitMqPublishBiz
    @Mock
    Logger log
    @InjectMocks
    CarrierOrderAppletBiz carrierOrderAppletBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(carrierOrderMapper.searchCarrierOrderIdsForApp(any())).thenReturn([1l])
        when(carrierOrderMapper.searchCarrierOrderForApp(anyString())).thenReturn([new SearchCarrierOrderListAppResponseModel()])
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)
        when(tDemandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(source: 0)])

        expect:
        carrierOrderAppletBiz.searchList(requestModel) == expectedResult

        where:
        requestModel                                || expectedResult
        new SearchCarrierOrderListAppRequestModel() || null
    }

    @Unroll
    def "search List Account where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(carrierOrderMapper.searchListAccountByApp(any())).thenReturn(new SearchCarrierOrderCountResponseModel())
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)

        expect:
        carrierOrderAppletBiz.searchListAccount(requestModel) == expectedResult

        where:
        requestModel                                || expectedResult
        new SearchCarrierOrderListAppRequestModel() || new SearchCarrierOrderCountResponseModel()
    }

    @Unroll
    def "carrier Order Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(carrierOrderMapper.carrierOrderDetailByApp(anyLong())).thenReturn(new CarrierOrderDetailAppResponseModel())
        when(carrierOrderVehicleHistoryMapper.getInvalidTopByCarrierOrderId(anyLong())).thenReturn(new TCarrierOrderVehicleHistory(remark: "remark"))
        when(carrierOrderVehicleHistoryMapper.getValidTopByCarrierOrderId(anyLong())).thenReturn(new TCarrierOrderVehicleHistory(remark: "remark"))
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)

        expect:
        carrierOrderAppletBiz.carrierOrderDetail(requestModel) == expectedResult

        where:
        requestModel                     || expectedResult
        new CarrierOrderIdRequestModel() || new CarrierOrderDetailAppResponseModel()
    }

    @Unroll
    def "carrier Order Logistics Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(carrierOrderTicketsMapper.getTicketsByCarrierOrderIdByApp(anyLong())).thenReturn([new CarrierOrderBillResponseModel()])
        when(commonBiz.batchGetOSSFileUrl(any())).thenReturn(["String": "String"])
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)

        expect:
        carrierOrderAppletBiz.carrierOrderLogisticsDetail(requestModel) == expectedResult

        where:
        requestModel                     || expectedResult
        new CarrierOrderIdRequestModel() || new CarrierOrderLogisticsDetailResponseModel()
    }

    @Unroll
    def "pick Up where requestModel=#requestModel"() {
        given:
        when(carrierOrderTicketsMapper.batchInsertTickets(any())).thenReturn(0)
        when(carrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(anyLong(), anyInt())).thenReturn([new GetTicketsResponseModel()])
        when(carrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderGoods(loadAmount: 0 as BigDecimal)])
        when(carrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(any())).thenReturn(0)
        when(carrierOrderVehicleHistoryMapper.getTopByCarrierOrderId(any())).thenReturn(new TCarrierOrderVehicleHistory(driverId: 1l, ifInvalid: 0, auditStatus: 0))
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")

        expect:
        carrierOrderAppletBiz.pickUp(requestModel)
        assert expectedResult == false

        where:
        requestModel                             || expectedResult
        new CarrierOrderLoadRequestModel() || true
    }

    @Unroll
    def "unloading where requestModel=#requestModel"() {
        given:
        when(carrierOrderTicketsMapper.batchInsertTickets(any())).thenReturn(0)
        when(carrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(anyLong(), anyInt())).thenReturn([new GetTicketsResponseModel()])
        when(carrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderGoods(loadAmount: 0 as BigDecimal, unloadAmount: 0 as BigDecimal)])
        when(carrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(any())).thenReturn(0)
        when(carrierOrderVehicleHistoryMapper.getTopByCarrierOrderId(any())).thenReturn(new TCarrierOrderVehicleHistory(driverId: 1l, ifInvalid: 0, auditStatus: 0))
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")

        expect:
        carrierOrderAppletBiz.unloading(requestModel)
        assert expectedResult == false

        where:
        requestModel                             || expectedResult
        new CarrierOrderLoadRequestModel() || true
    }

    @Unroll
    def "arrive Pick Up where requestModel=#requestModel"() {
        given:
        when(carrierOrderTicketsMapper.batchInsertTickets(any())).thenReturn(0)
        when(carrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(anyLong(), anyInt())).thenReturn([new GetTicketsResponseModel()])
        when(carrierOrderVehicleHistoryMapper.getTopByCarrierOrderId(any())).thenReturn(new TCarrierOrderVehicleHistory(driverId: 1l, ifInvalid: 0, auditStatus: 0))
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")

        expect:
        carrierOrderAppletBiz.arrivePickUp(requestModel)
        assert expectedResult == false

        where:
        requestModel                                   || expectedResult
        new CarrierOrderArriveLoadUnloadRequestModel() || true
    }

    @Unroll
    def "arrive Unloading where requestModel=#requestModel"() {
        given:
        when(carrierOrderTicketsMapper.batchInsertTickets(any())).thenReturn(0)
        when(carrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(anyLong(), anyInt())).thenReturn([new GetTicketsResponseModel()])
        when(carrierOrderVehicleHistoryMapper.getTopByCarrierOrderId(any())).thenReturn(new TCarrierOrderVehicleHistory(driverId: 1l, ifInvalid: 0, auditStatus: 0))
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")

        expect:
        carrierOrderAppletBiz.arriveUnloading(requestModel)
        assert expectedResult == false

        where:
        requestModel                                   || expectedResult
        new CarrierOrderArriveLoadUnloadRequestModel() || true
    }

    @Unroll
    def "add Carrier Order Tickets where tCarrierOrder=#tCarrierOrder and ticketsTypeEnum=#ticketsTypeEnum and typeEnum=#typeEnum and imaUrl=#imaUrl then expect: #expectedResult"() {
        given:
        when(carrierOrderTicketsMapper.batchInsertTickets(any())).thenReturn(0)
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")

        expect:
        carrierOrderAppletBiz.addCarrierOrderTickets(imaUrl, tCarrierOrder, typeEnum, ticketsTypeEnum) == expectedResult

        where:
        tCarrierOrder                                           | ticketsTypeEnum                  | typeEnum                                 | imaUrl     || expectedResult
        new TCarrierOrder(carrierOrderCode: "carrierOrderCode") | CarrierOrderTicketsTypeEnum.NULL | CopyFileTypeEnum.COMPANY_CARRIER_TRADING | ["String"] || ["String"]
    }

    @Unroll
    def "add Carrier Order Events where typeEnum=#typeEnum and ticketCount=#ticketCount and carrierOrderId=#carrierOrderId and remark=#remark"() {
        expect:
        carrierOrderAppletBiz.addCarrierOrderEvents(carrierOrderId, typeEnum, ticketCount, remark)
        assert expectedResult == false

        where:
        typeEnum                            | ticketCount | carrierOrderId | remark   || expectedResult
        CarrierOrderEventsTypeEnum.BY_ORDER | 0           | 1l             | "remark" || true
    }

    @Unroll
    def "add Carrier Order Operate Logs where typeEnum=#typeEnum and carrierOrderId=#carrierOrderId and remark=#remark"() {
        expect:
        carrierOrderAppletBiz.addCarrierOrderOperateLogs(carrierOrderId, typeEnum, remark)
        assert expectedResult == false

        where:
        typeEnum                                            | carrierOrderId | remark   || expectedResult
        CarrierOrderOperateLogsTypeEnum.CREATE_DEMAND_ORDER | 1l             | "remark" || true
    }

    @Unroll
    def "check Carrier Driver If Enabled where carrierOrderId=#carrierOrderId then expect: #expectedResult"() {
        given:
        when(carrierOrderVehicleHistoryMapper.getTopByCarrierOrderId(any())).thenReturn(new TCarrierOrderVehicleHistory(driverId: 1l, ifInvalid: 0, auditStatus: 0))

        expect:
        carrierOrderAppletBiz.checkCarrierDriverIfEnabled(carrierOrderId) == expectedResult

        where:
        carrierOrderId || expectedResult
        1l             || new com.logistics.tms.entity.TCarrierOrder()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme