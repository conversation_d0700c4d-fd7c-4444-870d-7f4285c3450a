package com.logistics.management.webapi.controller.vehiclelength.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class VehicleLengthDetailResponseDto {

    /**
     * 车长配置id
     */
    @ApiModelProperty("车长配置id")
    private String vehicleLengthId="";

    /**
     * 车长
     */
    @ApiModelProperty("车长")
    private String vehicleLength="";

    /**
     * 承运范围-低
     */
    @ApiModelProperty("承运范围-低")
    private String carriageScopeMin="";

    /**
     * 承运范围-高
     */
    @ApiModelProperty("承运范围-高")
    private String carriageScopeMax="";

}
