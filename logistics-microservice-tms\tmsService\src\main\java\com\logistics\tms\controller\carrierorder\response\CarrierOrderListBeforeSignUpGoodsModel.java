package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CarrierOrderListBeforeSignUpGoodsModel {
    @ApiModelProperty("货物ID")
    private Long goodsId;
    @ApiModelProperty("货物名")
    private String goodsName;
    @ApiModelProperty("预提件数")
    private BigDecimal expectAmount;
    @ApiModelProperty("装货件数")
    private BigDecimal loadAmount;
    @ApiModelProperty("卸货件数")
    private BigDecimal unloadAmount;
    @ApiModelProperty("签收件数")
    private BigDecimal signAmount;
    @ApiModelProperty("长")
    private Integer length;
    @ApiModelProperty("宽")
    private Integer width;
    @ApiModelProperty("高")
    private Integer height;
    @ApiModelProperty("规格")
    private String goodsSize;
    private Long demandOrderGoodsId;
}
