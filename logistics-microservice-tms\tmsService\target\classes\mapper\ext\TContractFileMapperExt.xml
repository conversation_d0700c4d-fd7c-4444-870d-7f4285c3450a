<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TContractFileMapper" >

  <update id="updateContractFileByContractId" parameterType="java.lang.Long">
    update t_contract_file tcf set tcf.valid = 0
    where tcf.valid = 1
    and tcf.contract_id = #{contractId,jdbcType=BIGINT}
    and tcf.contract_file_path not in (${filePath})
  </update>

  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TContractFile" >
    <foreach collection="list" close="" index="index" item="item" separator=";">
      insert into t_contract_file
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.contractId != null">
          contract_id,
        </if>
        <if test="item.contractFilePath != null">
          contract_file_path,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.contractId != null">
         #{item.contractId,jdbcType=BIGINT},
        </if>
        <if test="item.contractFilePath != null">
          #{item.contractFilePath,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

</mapper>