package com.logistics.tms.client.feign.warehouse.stock.reponse;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetWareHouseByIdsAndKeyWordAndTypeResponseModel {
    @ApiModelProperty("仓库id")
    private Long id;

    @ApiModelProperty("仓库类型 1:自有,2:是第三方,4:虚拟,5:其他,6:粉碎工厂")
    private Integer type;

    @ApiModelProperty("仓库中文名字")
    private String chineseName;

    @ApiModelProperty("仓库英文名字")
    private String englishName;

    @ApiModelProperty("省份ID")
    private Long provinceId;

    @ApiModelProperty("省份名字")
    private String provinceName;

    @ApiModelProperty("城市ID")
    private Long cityId;

    @ApiModelProperty("城市名字")
    private String cityName;

    @ApiModelProperty("县区id")
    private Long areaId;

    @ApiModelProperty("县区名字")
    private String areaName;

    @ApiModelProperty("详细地址")
    private String detailAddress;

    @ApiModelProperty("联系人名字")
    private String contactName;

    @ApiModelProperty("联系座机号码")
    private String contactTelephone;

    @ApiModelProperty("手机号")
    private String contactMobile;

    @ApiModelProperty("传真")
    private String contactFax;

    @ApiModelProperty("是否启用,1启用,0禁用")
    private Integer enabled;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否勾选库点：0 否，1 是")
    private Integer warehouseSite;

    @ApiModelProperty("详细地址（英文）")
    private String abroadAddress;

    @ApiModelProperty("合同id")
    private Long warehouseContractId;

    @ApiModelProperty("区域负责人")
    private Long areaAnagerId;

    @ApiModelProperty("仓库code")
    private String warehouseCode;

    @ApiModelProperty("作业时间")
    private String jobTime;
}
