package com.logistics.tms.biz.vehicleoilcard;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.driverpayee.model.ViewLogResponseModel;
import com.logistics.tms.api.feign.vehicleoilcard.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel;
import com.logistics.tms.entity.TDriverCostApply;
import com.logistics.tms.entity.TOperateLogs;
import com.logistics.tms.entity.TVehicleOilCard;
import com.logistics.tms.mapper.TDriverCostApplyMapper;
import com.logistics.tms.mapper.TOperateLogsMapper;
import com.logistics.tms.mapper.TVehicleBasicMapper;
import com.logistics.tms.mapper.TVehicleOilCardMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/4 15:22
 */
@Service
public class VehicleOilCardBiz {

    @Autowired
    private TVehicleOilCardMapper tVehicleOilCardMapper;
    @Autowired
    private TDriverCostApplyMapper tDriverCostApplyMapper;
    @Autowired
    private TVehicleBasicMapper tVehicleBasicMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TOperateLogsMapper tOperateLogsMapper;

    /**
     * 车辆油卡列表
     * @param requestModel
     * @return
     */
    public PageInfo<SearchVehicleOilCardListResponseModel> searchVehicleOilCardList(SearchVehicleOilCardListRequestModel requestModel) {
        requestModel.enablePaging();
        List<SearchVehicleOilCardListResponseModel> list = tVehicleOilCardMapper.searchVehicleOilCardList(requestModel);
        return new PageInfo<>(list);
    }

    /**
     * 新增车辆油卡
     * @param requestModel
     * @return
     */
    @Transactional
    public void addVehicleOilCard(AddVehicleOilCardRequestModel requestModel) {
        //一辆车只能绑定一张油卡
        TVehicleOilCard vehicleExist = tVehicleOilCardMapper.getByCardNoOrVehicleId(null, requestModel.getVehicleId());
        if (vehicleExist != null){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_VEHICLE_EXIST);
        }

        //一张油卡只能绑定一辆车
        TVehicleOilCard cardNumberExist = tVehicleOilCardMapper.getByCardNoOrVehicleId(requestModel.getCardNumber(), null);
        if (cardNumberExist != null){
            //油卡存在，但未绑定车辆
            if (VehicleOilCardStatusEnum.UNBINDING.getKey().equals(cardNumberExist.getStatus())){
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_CARD_EXIST_VEHICLE_NOT_EXIST);
            }
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_CARD_VEHICLE_EXIST);
        }

        //判断车辆是否满足要求
        VehicleBasicPropertyModel vehicleBasicPropertyModel = tVehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getVehicleId());
        if (vehicleBasicPropertyModel == null){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }
        //判断车辆是否是自主或自营的
        if (!VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehicleBasicPropertyModel.getVehicleProperty())
                && !VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehicleBasicPropertyModel.getVehicleProperty())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_VEHICLE_ERROR);
        }
        //判断车辆是否是牵引车或一体车
        if (!VehicleCategoryEnum.TRACTOR.getKey().equals(vehicleBasicPropertyModel.getVehicleCategory())
                && !VehicleCategoryEnum.WHOLE.getKey().equals(vehicleBasicPropertyModel.getVehicleCategory())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_VEHICLE_ERROR);
        }
        //运营中的车辆
        if (!OperatingStateEnum.IN_OPERATION.getKey().equals(vehicleBasicPropertyModel.getOperatingState())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLES_ARE_SCRAP);
        }

        //新增车辆油卡
        TVehicleOilCard vehicleOilCard = new TVehicleOilCard();
        vehicleOilCard.setCardNumber(requestModel.getCardNumber());
        vehicleOilCard.setVehicleId(requestModel.getVehicleId());
        vehicleOilCard.setVehicleProperty(vehicleBasicPropertyModel.getVehicleProperty());
        vehicleOilCard.setVehicleNo(vehicleBasicPropertyModel.getVehicleNo());
        vehicleOilCard.setStatus(VehicleOilCardStatusEnum.BINDING.getKey());
        vehicleOilCard.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityAdd(vehicleOilCard, BaseContextHandler.getUserName());
        tVehicleOilCardMapper.insertSelective(vehicleOilCard);

        //记录操作日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(vehicleOilCard.getId(), OperateLogsOperateTypeEnum.VEHICLE_OIL_CARD_ADD, "绑定车辆【"+vehicleOilCard.getVehicleNo()+"】", BaseContextHandler.getUserName());
        tOperateLogsMapper.insertSelective(tOperateLogs);
    }

    /**
     * 车辆油卡详情
     * @param requestModel
     * @return
     */
    public VehicleOilCardDetailResponseModel vehicleOilCardDetail(VehicleOilCardIdRequestModel requestModel) {
        //查询车辆油卡信息
        TVehicleOilCard dbVehicleOilCard = tVehicleOilCardMapper.selectByPrimaryKey(requestModel.getVehicleOilCardId());
        //判断车辆油卡信息是否存在
        if (dbVehicleOilCard == null || dbVehicleOilCard.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_NOT_EXIST);
        }

        //数据转换
        VehicleOilCardDetailResponseModel detail = MapperUtils.mapper(dbVehicleOilCard, VehicleOilCardDetailResponseModel.class);
        detail.setVehicleOilCardId(dbVehicleOilCard.getId());
        return detail;
    }

    /**
     * 解绑车辆油卡
     * @param requestModel
     * @return
     */
    @Transactional
    public void unBindVehicleOilCard(VehicleOilCardIdRequestModel requestModel) {
        //查询车辆油卡信息
        TVehicleOilCard dbVehicleOilCard = tVehicleOilCardMapper.selectByPrimaryKey(requestModel.getVehicleOilCardId());
        //判断车辆油卡信息是否存在
        if (dbVehicleOilCard == null || dbVehicleOilCard.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_NOT_EXIST);
        }
        //判断绑定状态
        if (VehicleOilCardStatusEnum.UNBINDING.getKey().equals(dbVehicleOilCard.getStatus())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_STATUS_ERROR);
        }
        //查询费用申请中是否有绑定此油卡的车辆的待审核的记录
        String costApplyStatus = StringUtils.listToString(Arrays.asList(DriverCostAuditEnum.WAIT_BUSINESS_AUDIT.getKey(), DriverCostAuditEnum.WAIT_FINANCIAL_AUDIT.getKey()), ',');
        TDriverCostApply tDriverCostApply = tDriverCostApplyMapper.selectByOilCardNumAndStatus(dbVehicleOilCard.getCardNumber(), costApplyStatus);
        if (tDriverCostApply != null && IfValidEnum.VALID.getKey().equals(tDriverCostApply.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.NOT_UNBIND_OLI_CARD);
        }

        //解除绑定
        TVehicleOilCard vehicleOilCard = new TVehicleOilCard();
        vehicleOilCard.setId(dbVehicleOilCard.getId());
        vehicleOilCard.setVehicleId(CommonConstant.LONG_ZERO);
        vehicleOilCard.setVehicleProperty(VehiclePropertyEnum.PROPERTY_ZERO.getKey());
        vehicleOilCard.setVehicleNo("");
        vehicleOilCard.setStatus(VehicleOilCardStatusEnum.UNBINDING.getKey());
        commonBiz.setBaseEntityModify(vehicleOilCard, BaseContextHandler.getUserName());
        tVehicleOilCardMapper.updateByPrimaryKeySelective(vehicleOilCard);

        //记录操作日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(vehicleOilCard.getId(), OperateLogsOperateTypeEnum.VEHICLE_OIL_CARD_UNBINDING, null, BaseContextHandler.getUserName());
        tOperateLogsMapper.insertSelective(tOperateLogs);
    }

    /**
     * 绑定车辆油卡
     * @param requestModel
     * @return
     */
    @Transactional
    public void bindVehicleOilCard(BindVehicleOilCardRequestModel requestModel) {
        //查询车辆油卡信息
        TVehicleOilCard dbVehicleOilCard = tVehicleOilCardMapper.selectByPrimaryKey(requestModel.getVehicleOilCardId());
        //判断车辆油卡信息是否存在
        if (dbVehicleOilCard == null || dbVehicleOilCard.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_NOT_EXIST);
        }
        //判断绑定状态
        if (VehicleOilCardStatusEnum.BINDING.getKey().equals(dbVehicleOilCard.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_STATUS_ERROR);
        }

        //判断车辆是否满足要求
        VehicleBasicPropertyModel vehicleBasicPropertyModel = tVehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getVehicleId());
        if (vehicleBasicPropertyModel == null){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }
        //判断车辆是否是自主或自营的
        if (!VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehicleBasicPropertyModel.getVehicleProperty())
                && !VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehicleBasicPropertyModel.getVehicleProperty())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_VEHICLE_ERROR);
        }
        //判断车辆是否是牵引车或一体车
        if (!VehicleCategoryEnum.TRACTOR.getKey().equals(vehicleBasicPropertyModel.getVehicleCategory())
                && !VehicleCategoryEnum.WHOLE.getKey().equals(vehicleBasicPropertyModel.getVehicleCategory())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_VEHICLE_ERROR);
        }
        //运营中的车辆
        if (!OperatingStateEnum.IN_OPERATION.getKey().equals(vehicleBasicPropertyModel.getOperatingState())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLES_ARE_SCRAP);
        }

        //一辆车只能绑定一张油卡
        TVehicleOilCard vehicleExist = tVehicleOilCardMapper.getByCardNoOrVehicleId(null, requestModel.getVehicleId());
        if (vehicleExist != null){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_VEHICLE_EXIST);
        }

        //绑定
        TVehicleOilCard vehicleOilCard = new TVehicleOilCard();
        vehicleOilCard.setId(dbVehicleOilCard.getId());
        vehicleOilCard.setVehicleId(vehicleBasicPropertyModel.getVehicleId());
        vehicleOilCard.setVehicleProperty(vehicleBasicPropertyModel.getVehicleProperty());
        vehicleOilCard.setVehicleNo(vehicleBasicPropertyModel.getVehicleNo());
        vehicleOilCard.setStatus(VehicleOilCardStatusEnum.BINDING.getKey());
        vehicleOilCard.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityModify(vehicleOilCard, BaseContextHandler.getUserName());
        tVehicleOilCardMapper.updateByPrimaryKeySelective(vehicleOilCard);

        //记录操作日志
        TOperateLogs tOperateLogs = commonBiz.addOperateLogs(vehicleOilCard.getId(), OperateLogsOperateTypeEnum.VEHICLE_OIL_CARD_BINDING, "绑定车辆【"+vehicleOilCard.getVehicleNo()+"】", BaseContextHandler.getUserName());
        tOperateLogsMapper.insertSelective(tOperateLogs);
    }

    /**
     * 操作记录
     * @param requestModel
     * @return
     */
    public List<GetVehicleOilCardRecordResponseModel> getVehicleOilCardRecord(VehicleOilCardIdRequestModel requestModel) {
        //查询车辆油卡信息
        TVehicleOilCard dbVehicleOilCard = tVehicleOilCardMapper.selectByPrimaryKey(requestModel.getVehicleOilCardId());
        //判断车辆油卡信息是否存在
        if (dbVehicleOilCard == null || dbVehicleOilCard.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OIL_CARD_NOT_EXIST);
        }

        List<GetVehicleOilCardRecordResponseModel> list = new ArrayList<>();

        //查询车辆油卡的操作记录
        List<ViewLogResponseModel> logList = tOperateLogsMapper.selectLogsByCondition(OperateLogsObjectTypeEnum.VEHICLE_OIL_CARD.getKey(), requestModel.getVehicleOilCardId(), null);
        if (ListUtils.isNotEmpty(logList)){
            list = MapperUtils.mapper(logList, GetVehicleOilCardRecordResponseModel.class);
        }
        return list;
    }
}
