package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.insuarance.model.*;
import com.logistics.tms.entity.TInsurance;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

@Mapper
public interface TInsuranceMapper extends BaseMapper<TInsurance> {

    List<SearchInsuranceListResponseModel> searchInsuranceList(@Param("params") SearchInsuranceListRequestModel requestModel);

    GetInsuranceDetailResponseModel getInsuranceDetail(@Param("insuranceId") Long insuranceId);

    List<TInsurance> getByTypeVehicleIdOrDriverId(@Param("params") VerifyInsuranceUniquenessRequestModel requestModel);

    int batchInsert(@Param("list") List<TInsurance> list);

    List<TInsurance> getByDriverIds(@Param("driverIds") String driverIds);

    List<TInsurance> getOverDueInsurance(@Param("now") Date now);

    List<TInsurance> getAboutToOverDueInsurance(@Param("now") Date now, @Param("remindDays") Integer days);

    List<TInsurance> getByVehicleIdAndTime(@Param("insuranceType") Integer insuranceType, @Param("vehicleId") Long vehicleId, @Param("startTime") Integer startTime, @Param("endTime") Integer endTime);

    List<TInsurance> getByVehicleBasicId(@Param("vehicleBasicId") String vehicleBasicId);

    int getAllCount();

    List<TInsurance> getByIds(@Param("ids") String ids);

    List<TInsurance> getByVehicleIdAndPeriod(@Param("vehicleId") Long vehicleId, @Param("currentTime") Date currentTime);

    int batchUpdate(@Param("list") List<TInsurance> list);

    List<InsuranceSettlementCostsRelationListModel> getSettlementCostsRelation(@Param("insuranceIds") String insuranceIds);
}