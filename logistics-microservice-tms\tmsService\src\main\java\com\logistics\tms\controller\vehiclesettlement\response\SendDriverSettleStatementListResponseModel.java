package com.logistics.tms.controller.vehiclesettlement.response;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class SendDriverSettleStatementListResponseModel {
    private Long vehicleSettlementId;
    @ApiModelProperty("车辆id")
    private Long vehicleId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机id")
    private Long driverId;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("司机手机号")
    private String mobile;
    @ApiModelProperty("账单时间")
    private String settlementMonth;
    @ApiModelProperty("月实付运费")
    private BigDecimal actualExpensesPayable;

    private Integer carrierOrderCount;  //运单数量
}
