package com.logistics.management.webapi.controller.region.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2021/10/18 10:34
 */
@Data
public class EnableRegionRequestDto {
    @ApiModelProperty("大区ID")
    @NotBlank(message = "大区名称ID为空")
    private String regionIds;

    @ApiModelProperty("是否禁用 1 启用  0 禁用")
    @NotBlank(message = "操作类型不能为空：1 启用，0 禁用")
    private String enabled;
}
