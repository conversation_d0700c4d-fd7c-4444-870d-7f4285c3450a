package com.logistics.management.webapi.client.vehiclesettlement;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.vehiclesettlement.hystrix.VehicleSettlementClientHystrix;
import com.logistics.management.webapi.client.vehiclesettlement.request.*;
import com.logistics.management.webapi.client.vehiclesettlement.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/2/22 9:37
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/vehicleSettlement",
        fallback = VehicleSettlementClientHystrix.class)
public interface VehicleSettlementClient {

    @ApiOperation(value = "车辆结算列表")
    @PostMapping(value = "/searchVehicleSettlementList")
    Result<PageInfo<SearchVehicleSettlementListResponseModel>> searchVehicleSettlementList(@RequestBody SearchVehicleSettlementListRequestModel requestModel);

    @ApiOperation(value = "车辆结算列表数量")
    @PostMapping(value = "/searchVehicleSettlementListCount")
    Result<SearchVehicleSettlementListCountResponseModel> searchVehicleSettlementListCount(@RequestBody SearchVehicleSettlementListRequestModel requestModel);

    @ApiOperation(value = "车辆结算详情")
    @PostMapping(value = "/getVehicleSettlementDetail")
    Result<GetVehicleSettlementDetailResponseModel> getVehicleSettlementDetail(@RequestBody VehicleSettlementDetailRequestModel requestModel);

    @ApiOperation(value = "导出车辆结算列表")
    @PostMapping(value = "/exportVehicleSettlement")
    Result<List<SearchVehicleSettlementListResponseModel>> exportVehicleSettlement(@RequestBody SearchVehicleSettlementListRequestModel requestModel);

    @ApiOperation(value = "确认结算")
    @PostMapping(value = "/confirmSettlement")
    Result confirmSettlement(@RequestBody ConfirmSettlementRequestModel requestModel);

    @ApiOperation(value = "车辆结算看板")
    @PostMapping(value = "/vehicleSettlementKanBan")
    Result<List<VehicleSettlementKanBanResponseModel>> vehicleSettlementKanBan(@RequestBody VehicleSettlementKanBanRequestModel requestModel);

    @ApiOperation(value = "提供给测试同学触发：生成车辆结算数据（贷款、停车费、GPS费、保险费未结清）")
    @PostMapping(value = "/generateVehicleSettlement")
    Result generateVehicleSettlement();

    @ApiOperation(value = "无需确认/撤回详情")
    @PostMapping(value = "/cancelVehicleSettlementDetail")
    Result<CancelVehicleSettlementDetailResponseModel> cancelVehicleSettlementDetail(@RequestBody CancelVehicleSettlementDetailRequestModel requestModel);

    @ApiOperation(value = "无需确认/撤回")
    @PostMapping(value = "/cancelVehicleSettlement")
    Result<Boolean> cancelVehicleSettlement(@RequestBody CancelVehicleSettlementRequestModel requestModel);

    @ApiOperation(value = "车辆结算列表-结清详情")
    @PostMapping(value = "/settleFreightDetail")
    Result<SettleFreightDetailResponseModel> settleFreightDetail(@RequestBody VehicleSettlementIdRequestModel requestModel);

    @ApiOperation(value = "车辆结算列表-结清")
    @PostMapping(value = "/settleFreight")
    Result<Boolean> settleFreight(@RequestBody SettleFreightRequestModel requestModel);

    @ApiOperation(value = "车辆结算-发送司机-查询司机")
    @PostMapping(value = "/getDriver")
    Result<List<GetSettlementDriverResponseModel>> getDriver(@RequestBody GetSettlementDriverRequestModel requestModel);

    @ApiOperation(value = "发送司机-确认")
    @PostMapping(value = "/confirmSendToDriver")
    Result<Boolean> confirmSendToDriver(@RequestBody  ConfirmSendToDriverRequestModel requestModel);

    @ApiOperation(value = "车辆结算列表-账单记录-查看")
    @PostMapping(value = "/settlementStatementRecord")
    Result<SettlementStatementRecordResponseModel> settlementStatementRecord(@RequestBody VehicleSettlementIdRequestModel requestModel);

    @ApiOperation(value = "车辆结算列表-处理-详情")
    @PostMapping(value = "/settlementStatementHandleDetail")
    Result<SettlementStatementHandleDetailResponseModel> settlementStatementHandleDetail(@RequestBody VehicleSettlementIdRequestModel requestModel);

    @ApiOperation(value = "车辆结算列表-处理")
    @PostMapping(value = "/settlementStatementHandle")
    Result<Boolean> settlementStatementHandle(@RequestBody SettlementStatementHandleRequestModel requestModel);

    @ApiOperation(value = "车辆结算-发送司机-账单列表")
    @PostMapping(value = "/sendDriverSettleStatementList")
    Result<List<SendDriverSettleStatementListResponseModel>> sendDriverSettleStatementList(@RequestBody SendDriverSettleStatementListRequestModel requestModel);

    @ApiOperation(value = "车辆结算详情-修改关联的轮胎（待对账状态操作）")
    @PostMapping(value = "/updateVehicleSettlementTire")
    Result<Boolean> updateVehicleSettlementTire(@RequestBody UpdateVehicleSettlementTireRequestModel requestModel);

    @ApiOperation(value = "车辆结算详情-查询账单上该车辆未关联的轮胎费用")
    @PostMapping(value = "/getVehicleTireByVehicleSettlementId")
    Result<GetVehicleTireByVehicleSettlementIdResponseModel> getVehicleTireByVehicleSettlementId(@RequestBody VehicleSettlementIdRequestModel requestModel);

}
