package com.logistics.tms.base.utils;

import com.yelo.tools.utils.ListUtils;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/4/12 9:22
 */
public class LocalStringUtil {

    private LocalStringUtil(){}

    /**
     * 对String类型的list拼接
     * @param list<String>
     * @param separator
     * @return
     */

    public static String listTostring(List list, char separator) {
        StringBuilder sb = new StringBuilder();

        if(ListUtils.isNotEmpty(list)){
            for(int i = 0; i < list.size(); ++i) {
                sb.append("'");
                sb.append(list.get(i));
                sb.append("'");
                if (i < list.size() - 1) {
                    sb.append(separator);
                }
            }
        }else{
            sb.append("'").append("'");
        }
        return sb.toString();
    }
}
