package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2024/08/06
*/
@Data
public class TShippingOrder extends BaseEntity {
    /**
    * 运输单号
    */
    @ApiModelProperty("运输单号")
    private String shippingOrderCode;

    /**
    * 状态：0 待审核，1 已审核，2 已驳回
    */
    @ApiModelProperty("状态：0 待审核，1 已审核，2 已驳回")
    private Integer status;

    /**
    * 调度单ID
    */
    @ApiModelProperty("调度单ID")
    private Long dispatchOrderId;

    /**
    * 调度单号
    */
    @ApiModelProperty("调度单号")
    private String dispatchOrderCode;

    /**
    * 预计数量
    */
    @ApiModelProperty("预计数量")
    private BigDecimal expectAmount;

    /**
    * 货物单位：1 件，2 吨，3 方，4 块
    */
    @ApiModelProperty("货物单位：1 件，2 吨，3 方，4 块")
    private Integer goodsUnit;

    /**
    * 车长（米）
    */
    @ApiModelProperty("车长（米）")
    private BigDecimal vehicleLength;

    /**
    * 车主整车运费
    */
    @ApiModelProperty("车主整车运费")
    private BigDecimal carrierFreight;

    /**
    * 串点费用
    */
    @ApiModelProperty("串点费用")
    private BigDecimal crossPointFee;

    /**
    * 串点距离
    */
    @ApiModelProperty("串点距离")
    private BigDecimal crossPointDistance;

    /**
    * 审核人
    */
    @ApiModelProperty("审核人")
    private String auditorName;

    /**
    * 审核时间
    */
    @ApiModelProperty("审核时间")
    private Date auditTime;

    /**
    * 审核备注
    */
    @ApiModelProperty("审核备注")
    private String auditRemark;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}