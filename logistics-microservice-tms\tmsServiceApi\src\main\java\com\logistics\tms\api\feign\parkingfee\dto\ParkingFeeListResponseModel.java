package com.logistics.tms.api.feign.parkingfee.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/10/9 15:32
 */
@Data
public class ParkingFeeListResponseModel {
    @ApiModelProperty("物理主键")
    private Long parkingFeeId;
    @ApiModelProperty("结算状态：0 待结算，1 部分结算，2 结算完成")
    private Integer status;
    @ApiModelProperty("车辆id")
    private Long vehicleId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("司机id")
    private Long staffId;
    @ApiModelProperty("司机姓名")
    private String name;
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("合作公司")
    private String cooperationCompany;
    @ApiModelProperty("起始日期")
    private Date startDate;
    @ApiModelProperty("截止时间")
    private Date endDate;
    @ApiModelProperty("终止时间")
    private Date finishDate;
    @ApiModelProperty("停车费")
    private BigDecimal parkingFee;
    @ApiModelProperty("合作周期（月）")
    private Integer cooperationPeriod;
    @ApiModelProperty("合作状态：1 已预付，2 进行中，3 已终止")
    private Integer cooperationStatus;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("创建人")
    private String createdBy;
    @ApiModelProperty("创建时间")
    private Date createdTime;
    @ApiModelProperty("最后修改人")
    private String lastModifiedBy;
    @ApiModelProperty("最后修改时间")
    private Date lastModifiedTime;
}
