package com.logistics.management.webapi.controller.freightconfig.request.ladder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;

@Data
public class CarrierFreightConfigLadderRequestDto {

    @ApiModelProperty(value = "阶梯配置Id, 编辑时使用")
    private String freightConfigLadderId;

    @ApiModelProperty(value = "阶梯层级; 0 为固定单价/总价", required = true)
    @NotBlank(message = "阶梯层级不能为空")
    @Min(value = 0, message = "阶梯层级不能小于0")
    private String ladderLevel;

    @ApiModelProperty(value = "阶梯类型; 1: KM(公里); 2: 数量;")
    private String ladderType;

    @ApiModelProperty(value = "阶梯起始")
    @Max(value = 100000, message = "阶梯最大限制 10000")
    private String ladderFrom;

    @ApiModelProperty(value = "阶梯终止（包含等于）")
    @Max(value = 100000, message = "阶梯最大限制 10000")
    private String ladderTo;

    @ApiModelProperty(value = "单位; 1: 件; 2: 吨; 4: 块")
    private String ladderUnit;

    @ApiModelProperty(value = "价格模式; 1: 单价; 2: 总价", required = true)
    @NotBlank(message = "请选择价格模式")
    @Range(min = 1, max = 2, message = "请选择价格模式")
    private String priceMode;

    @ApiModelProperty(value = "价格(元)", required = true)
    @Digits(integer = 7, fraction = 2, message = "请正确填写阶梯价格，0<金额≤1000000，允许保留2位小数")
    private String unitPrice;

    @Valid
    @ApiModelProperty(value = "多级阶梯")
    @Size(max = 10, message = "请维护 1 ~ 10 条阶梯配置")
    private List<CarrierFreightConfigLadderRequestDto> ladderConfigList;
}
