package com.logistics.management.webapi.api.feign.dispatch.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DemandOrderDispatchRequestDto {

    @ApiModelProperty(value = "需求单id", required = true)
    @NotBlank(message = "请选择需求单")
    private String demandId;

}
