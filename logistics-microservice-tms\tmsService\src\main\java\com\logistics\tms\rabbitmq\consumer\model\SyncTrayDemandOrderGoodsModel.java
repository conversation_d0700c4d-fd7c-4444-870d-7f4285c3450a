package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 托盘同步需求单到 TMS
 * liang current user system login name
 * 2018/10/10 current system date
 */
@Data
public class SyncTrayDemandOrderGoodsModel {

    private Long goodsId;
    @ApiModelProperty("sku code")
    private String productTypeCode;
    private String categoryName;
    private String goodsName;
    private Integer length;
    private Integer width;
    private Integer height;
    private BigDecimal goodsAmount;
}
