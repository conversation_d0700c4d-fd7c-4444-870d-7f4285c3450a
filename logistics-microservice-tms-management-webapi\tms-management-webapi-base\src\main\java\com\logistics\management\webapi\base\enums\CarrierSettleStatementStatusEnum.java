package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: wjf
 * @date: 2024/8/6 13:08
 */
@Getter
@AllArgsConstructor
public enum CarrierSettleStatementStatusEnum {

    DEFAULT(-99, ""),
    WAIT_FINISH(-3, "待完结"),
    NOT_RELATED(-2, "未关联"),
    WAIT_SUBMITTED(-1, "待提交"),
    WAIT_BUSINESS_AUDIT(0, "待业务审核"),
    WAIT_FINANCIAL_AUDIT(1, "待财务审核"),
    ACCOUNT_CHECKED(2, "已对账"),
    REJECT(3, "已驳回"),
    ;

    private final Integer key;
    private final String value;

    public static CarrierSettleStatementStatusEnum getEnum(Integer key) {
        for (CarrierSettleStatementStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }

}
