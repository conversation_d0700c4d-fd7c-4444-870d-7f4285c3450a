package com.logistics.tms.biz.extvehiclesettlement


import com.logistics.tms.api.feign.extvehiclesettlement.model.*
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TCertificationPictures
import com.logistics.tms.entity.TExtVehicleSettlement
import com.logistics.tms.entity.TExtVehicleSettlementItem
import com.logistics.tms.mapper.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class ExtVehicleSettlementBizTest extends Specification {
    @Mock
    TExtVehicleSettlementMapper extVehicleSettlementMapper
    @Mock
    TExtVehicleSettlementItemMapper extVehicleSettlementItemMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TCertificationPicturesMapper certificationPicturesMapper
    @Mock
    TCarrierOrderMapper carrierOrderMapper
    @Mock
    TCarrierOrderOperateLogsMapper carrierOrderOperateLogsMapper
    @InjectMocks
    ExtVehicleSettlementBiz extVehicleSettlementBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "create Ext Vehicle Settlement where carrierOrderIdList=#carrierOrderIdList and ifEmpty=#ifEmpty"() {
        given:
        when(extVehicleSettlementMapper.batchInsert(any())).thenReturn(0)
        when(extVehicleSettlementMapper.getByCarrierOrderIds(anyString())).thenReturn([new TExtVehicleSettlement(carrierOrderId: 1l, vehicleId: 1l, vehicleNo: "vehicleNo", staffId: 1l, staffName: "staffName", staffMobile: "staffMobile", driverTotalFee: 0 as BigDecimal, settlementUnit: 0, settlementAmount: 0 as BigDecimal)])
        when(carrierOrderMapper.getExtVehicleCarrierOrder(anyString())).thenReturn([new ExtVehicleCarrierOrderModel()])

        expect:
        extVehicleSettlementBiz.createExtVehicleSettlement(carrierOrderIdList, ifEmpty)
        assert expectedResult == false

        where:
        carrierOrderIdList | ifEmpty || expectedResult
        [1l]               | true    || true
    }

    @Unroll
    def "search Ext Vehicle Settlement List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(extVehicleSettlementMapper.searchExtVehicleSettlementIdList(any())).thenReturn([1l])
        when(extVehicleSettlementMapper.searchExtVehicleSettlementList(anyString())).thenReturn([new SearchExtVehicleSettlementListResponseModel()])
        when(extVehicleSettlementItemMapper.getValidBySettlementIds(anyString())).thenReturn([new TExtVehicleSettlementItem(extVehicleSettlementId: 1l, totalFee: 0 as BigDecimal)])

        expect:
        extVehicleSettlementBiz.searchExtVehicleSettlementList(requestModel) == expectedResult

        where:
        requestModel                                     || expectedResult
        new SearchExtVehicleSettlementListRequestModel() || null
    }

    @Unroll
    def "ext Vehicle Settlement Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(extVehicleSettlementMapper.extVehicleSettlementDetail(anyLong())).thenReturn(new ExtVehicleSettlementDetailResponseModel())
        when(certificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(filePath: "filePath")])

        expect:
        extVehicleSettlementBiz.extVehicleSettlementDetail(requestModel) == expectedResult

        where:
        requestModel                             || expectedResult
        new ExtVehicleSettlementIdRequestModel() || new ExtVehicleSettlementDetailResponseModel()
    }

    @Unroll
    def "ext Vehicle Settlement Payment where requestModel=#requestModel"() {
        given:
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(certificationPicturesMapper.batchInsert(any())).thenReturn(0)

        expect:
        extVehicleSettlementBiz.extVehicleSettlementPayment(requestModel)
        assert expectedResult == false

        where:
        requestModel                                  || expectedResult
        new ExtVehicleSettlementPaymentRequestModel() || true
    }

    @Unroll
    def "ext Vehicle Settlement Fallback where requestModel=#requestModel"() {
        given:
        when(extVehicleSettlementItemMapper.getValidBySettlementId(anyLong())).thenReturn(new TExtVehicleSettlementItem(totalFee: 0 as BigDecimal, ifFallback: 0, fallbackReason: "fallbackReason"))
        when(certificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures()])
        when(certificationPicturesMapper.batchUpdate(any())).thenReturn(0)

        expect:
        extVehicleSettlementBiz.extVehicleSettlementFallback(requestModel)
        assert expectedResult == false

        where:
        requestModel                                   || expectedResult
        new ExtVehicleSettlementFallbackRequestModel() || true
    }

    @Unroll
    def "create Carrier Logs where carrierOrderId=#carrierOrderId and remark=#remark and type=#type and content=#content"() {
        expect:
        extVehicleSettlementBiz.createCarrierLogs(carrierOrderId, type, content, remark)
        assert expectedResult == false

        where:
        carrierOrderId | remark   | type | content   || expectedResult
        1l             | "remark" | 0    | "content" || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme