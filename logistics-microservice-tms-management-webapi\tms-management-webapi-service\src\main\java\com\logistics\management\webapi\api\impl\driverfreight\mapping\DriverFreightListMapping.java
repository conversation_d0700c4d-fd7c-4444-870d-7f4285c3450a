package com.logistics.management.webapi.api.impl.driverfreight.mapping;
import com.logistics.management.webapi.api.feign.driverfreight.dto.DriverFreightListSearchResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel;
import com.logistics.tms.api.feign.driverfreight.model.DriverFreightListSearchResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.util.Optional;


public class DriverFreightListMapping extends MapperMapping<DriverFreightListSearchResponseModel,DriverFreightListSearchResponseDto> {

    @Override
    public void configure() {
        DriverFreightListSearchResponseModel source = getSource();
        DriverFreightListSearchResponseDto destination = getDestination();

        //状态转换
        if (CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())){
            destination.setStatus(CarrierOrderStatusEnum.EMPTY.getKey().toString());
            destination.setStatusDesc(CarrierOrderStatusEnum.EMPTY.getValue());
        }else{
            destination.setStatusDesc(CarrierOrderStatusEnum.getEnum(source.getStatus()).getValue());
        }

        BigDecimal unloadCapacity = CommonConstant.BIG_DECIMAL_ZERO;
        if (GoodsUnitEnum.BY_VOLUME.getKey().equals(source.getGoodsUnit())){
            BigDecimal unloadCapacityTotal = CommonConstant.BIG_DECIMAL_ZERO;
            for(SearchCarrierOrderListGoodsInfoModel tmp: source.getGoodsInfoList()){
                if(CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())){
                    tmp.setUnloadAmount(tmp.getExpectAmount());
                }
                BigDecimal capacity = ConverterUtils.toBigDecimal(tmp.getLength()).multiply(ConverterUtils.toBigDecimal(tmp.getWidth())).multiply(ConverterUtils.toBigDecimal(tmp.getHeight()));
                unloadCapacityTotal = unloadCapacityTotal.add(tmp.getUnloadAmount().multiply(capacity).divide(new BigDecimal(1000).pow(3),3,BigDecimal.ROUND_HALF_UP));
            }
            unloadCapacity = unloadCapacity.add(unloadCapacityTotal);
        }
        destination.setLoadAddress(source.getLoadCityName());
        destination.setUnloadAddress(source.getUnloadCityName());

        //司机费用
        BigDecimal dispatchFreightFee = BigDecimal.ZERO;
        if (source.getDispatchFreightFeeType().equals(PriceTypeEnum.UNIT_PRICE.getKey())){
            dispatchFreightFee = source.getDispatchFreightFee().multiply(source.getUnloadAmount()).setScale(2, BigDecimal.ROUND_HALF_UP);
        }else if(source.getDispatchFreightFeeType().equals(PriceTypeEnum.FIXED_PRICE.getKey())) {
            dispatchFreightFee = source.getDispatchFreightFee();
        }
        if (source.getDriverOtherFee() != null){
            dispatchFreightFee = dispatchFreightFee.add(source.getDriverOtherFee());
        }
        destination.setDriverFreight(ConverterUtils.toString(dispatchFreightFee));
        destination.setDriverFreightTotal(ConverterUtils.toString(dispatchFreightFee.add(source.getAdjustFee()).add(source.getMarkupFee())));

        destination.setDriver(source.getDriverName() + " " + source.getDriverMobile());
        destination.setVehiclePropertyDesc(VehiclePropertyEnum.getEnum(source.getVehicleProperty()).getValue());
        destination.setGoodsUnitDesc(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());

        if (ListUtils.isNotEmpty(source.getGoodsInfoList())) {
            StringBuilder goodsName = new StringBuilder();
            StringBuilder goodsSize = new StringBuilder();
            for(int index = 0;index<source.getGoodsInfoList().size() ;index++){
                SearchCarrierOrderListGoodsInfoModel tmpGoodsModel = source.getGoodsInfoList().get(index);
                if (source.getGoodsUnit().equals(GoodsUnitEnum.BY_VOLUME.getKey())) {
                    tmpGoodsModel.setGoodsSize(tmpGoodsModel.getGoodsSize() + " " + tmpGoodsModel.getLength() + "*" + tmpGoodsModel.getWidth() + "*" + tmpGoodsModel.getHeight() + "mm");
                }
                if(index !=0){
                    goodsName.append("/");
                }
                if(StringUtils.isNotBlank(goodsSize.toString())&&StringUtils.isNotBlank(tmpGoodsModel.getGoodsSize())){
                    goodsSize.append("/");
                }
                goodsName.append(tmpGoodsModel.getGoodsName());
                if(StringUtils.isNotBlank(tmpGoodsModel.getGoodsSize())){
                    goodsSize.append(tmpGoodsModel.getGoodsSize());
                }
            }

            destination.setGoodsName(goodsName.toString());
            destination.setSize(goodsSize.toString());
        }
        Optional.ofNullable(source.getDispatchTime()).ifPresent(t->
            destination.setDispatchTime(DateUtils.dateToString(source.getDispatchTime(),DateUtils.DATE_TO_STRING_DETAIAL_PATTERN))
        );
        if(CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())){
            //已放空运单，结算数据取预提
            source.setUnloadAmount(source.getExpectAmount());
        }
        destination.setUnloadAmount(source.getUnloadAmount().stripTrailingZeros().toPlainString()+ GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());
        if(GoodsUnitEnum.BY_VOLUME.getKey().equals(source.getGoodsUnit())){
            destination.setUnloadAmount(destination.getUnloadAmount()+"("+unloadCapacity.stripTrailingZeros().toPlainString()+ CommonConstant.SQUARE+")");
        }

        StringBuilder entrust = new StringBuilder();
        if(CompanyTypeEnum.getEnum(source.getCompanyEntrustType())!= CompanyTypeEnum.DEFAULT){
            entrust.append("【").append(CompanyTypeEnum.getEnum(source.getCompanyEntrustType()).getValue()).append("】");
        }
        Optional.ofNullable(source.getCompanyEntrustName()).ifPresent(t -> entrust.append(" " + source.getCompanyEntrustName()));
        Optional.ofNullable(source.getEntrustContactName()).ifPresent(t -> entrust.append(" " + source.getEntrustContactName()));
        Optional.ofNullable(source.getEntrustContactMobile()).ifPresent(t -> entrust.append(" " + source.getEntrustContactMobile()));
        destination.setCompanyEntrustName(entrust.toString());

        StringBuilder carrier = new StringBuilder();
        if (CompanyTypeEnum.getEnum(source.getCompanyCarrierType()) != CompanyTypeEnum.DEFAULT) {
            carrier.append("【").append(CompanyTypeEnum.getEnum(source.getCompanyCarrierType()).getValue()).append("】");
        }
        Optional.ofNullable(source.getCompanyCarrierName()).ifPresent(t -> carrier.append(" " + source.getCompanyCarrierName()));
        Optional.ofNullable(source.getCarrierContactName()).ifPresent(t -> carrier.append(" " + source.getCarrierContactName()));
        Optional.ofNullable(source.getCarrierContactMobile()).ifPresent(t -> carrier.append(" " + source.getCarrierContactMobile()));

        //个人车主展示公司名为 姓名 手机号
        if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())) {
            destination.setCompanyCarrierName(source.getCarrierContactName() + " " + source.getCarrierContactMobile());
        }
    }
}
