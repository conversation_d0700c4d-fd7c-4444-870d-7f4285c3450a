package com.logistics.management.webapi.controller.invoicingmanagement.tradition.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @author: wjf
 * @date: 2024/3/19 17:26
 */
@Data
public class TraditionAddOrModifyInvoiceRequestDto {
    /**
     * 发票管理id
     */
    @ApiModelProperty(value = "发票管理id",required = true)
    @NotBlank(message = "发票管理id")
    private String invoicingId;

    /**
     * 发票id（编辑必填）
     */
    @ApiModelProperty(value = "发票id（编辑必填）")
    private String invoiceId;

    /**
     * 发票类型：1 电子发票，2 纸质发票
     */
    @ApiModelProperty(value = "发票类型：1 电子发票，2 纸质发票",required = true)
    @NotBlank(message = "请选择发票类型")
    @Range(min = 1, max = 2, message = "发票类型只能只1或2")
    private String invoiceType;

    /**
     * 发票图片
     */
    @ApiModelProperty(value = "发票图片",required = true)
    @NotBlank(message = "请选择上传发票图片")
    private String invoicePicture;

    /**
     * 发票代码
     */
    @ApiModelProperty("发票代码")
    @Size(max = 20, message = "发票代码长度不能超过20")
    private String invoiceCode;

    /**
     * 发票号码
     */
    @ApiModelProperty("发票号码")
    @Size(max = 20, message = "发票号码长度不能超过20")
    private String invoiceNum;

    /**
     * 开票日期
     */
    @ApiModelProperty("开票日期")
    private String invoiceDate;

    /**
     * 发票金额
     */
    @ApiModelProperty("发票金额")
    @DecimalMax(value = "1000000",message = "发票金额不能超过1000000")
    private String invoiceAmount;

    /**
     * 税率
     */
    @ApiModelProperty("税率")
    @DecimalMax(value = "100",message = "税率不能超过100")
    private String taxRate;

    /**
     * 税额合计
     */
    @ApiModelProperty("税额合计")
    @DecimalMax(value = "1000000",message = "税额合计不能超过1000000")
    private String totalTaxAndPrice;

}
