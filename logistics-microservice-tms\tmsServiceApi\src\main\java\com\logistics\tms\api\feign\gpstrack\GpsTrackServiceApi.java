package com.logistics.tms.api.feign.gpstrack;

import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.gpstrack.hystrix.GpsTrackServiceApiHystrix;
import com.logistics.tms.api.feign.gpstrack.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@Api(value = "API-GpsTrackServiceApi-车辆管理")
@FeignClient(name = "logistics-tms-services", fallback = GpsTrackServiceApiHystrix.class)
public interface GpsTrackServiceApi {

    @PostMapping(value = "/service/gpstrack/getVehicleTrackInfoList")
    @ApiOperation(value = "获取车辆追踪信息列表")
    Result<AllVehicleTrackInfoResponseModel> getVehicleTrackInfoList(@RequestBody AllVehicleTrackInfoRequestModel requestModel);

    @PostMapping(value = "/service/gpstrack/getDestinationByVehicleNo")
    @ApiOperation(value = "根据车牌号获取调度单和运单地址等详情")
    Result<SearchCarrierOrderDestinationByVehicleNoResponseModel> getDestinationByVehicleNo(@RequestBody SearchCarrierOrderDestinationByVehicleNoRequestModel requestModel);

    @PostMapping(value = "/service/gpstrack/getVehicleTrackHistory")
    @ApiOperation(value = "根据车牌号获取车辆历史轨迹")
    Result<OpGpHisTrackResponseModel> getVehicleTrackHistory(@RequestBody OpGpHisTrackRequestModel requestModel);

    @PostMapping(value = "/service/gpstrack/refreshLocation")
    @ApiOperation(value = "刷新本地车辆定位信息")
    Result refreshLocation();

}
