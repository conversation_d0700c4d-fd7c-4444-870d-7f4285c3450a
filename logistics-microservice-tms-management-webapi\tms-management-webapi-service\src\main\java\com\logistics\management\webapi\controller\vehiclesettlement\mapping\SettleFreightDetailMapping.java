package com.logistics.management.webapi.controller.vehiclesettlement.mapping;

import com.logistics.management.webapi.base.enums.VehicleSettlementStatusEnum;
import com.logistics.management.webapi.client.vehiclesettlement.response.SettleFreightDetailResponseModel;
import com.logistics.management.webapi.controller.vehiclesettlement.response.SettleFreightDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author:lei.zhu
 * @date:2021/4/12 16:16
 */
public class SettleFreightDetailMapping extends MapperMapping<SettleFreightDetailResponseModel, SettleFreightDetailResponseDto> {
    @Override
    public void configure() {
        SettleFreightDetailResponseDto destination = getDestination();
        SettleFreightDetailResponseModel source = getSource();
        destination.setStatusLabel(VehicleSettlementStatusEnum.getEnum(source.getStatus()).getValue());
        destination.setNotPayMoney(source.getNotPayMoney().toPlainString());
    }
}
