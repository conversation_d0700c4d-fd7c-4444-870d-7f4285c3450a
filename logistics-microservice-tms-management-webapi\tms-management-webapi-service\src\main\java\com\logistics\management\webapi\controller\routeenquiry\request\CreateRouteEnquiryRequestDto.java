package com.logistics.management.webapi.controller.routeenquiry.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/7/8 17:34
 */
@Data
public class CreateRouteEnquiryRequestDto {

    /**
     * 基础信息
     */
    @Valid
    @NotEmpty(message = "报价信息不能为空")
    @Size(max = 30, message = "最多支持添加30条路线")
    private List<CreateRouteEnquiryAddressListRequestDto> addressList;

    /**
     * 承运商
     */
    @NotEmpty(message = "请选择承运商")
    private List<String> companyCarrierIdList;

    /**
     * 货物名称
     */
    @Size(max = 50, message = "货物名称不能超过50字")
    private String goodsName;

    /**
     * 报价生效开始时间
     */
    @NotBlank(message = "请选报价生效期限")
    private String quoteStartTime;

    /**
     * 报价生效结束时间
     */
    @NotBlank(message = "请选报价生效期限")
    private String quoteEndTime;

    /**
     * 备注
     */
    @Size(max = 300, message = "备注不能超过300字")
    private String remark;

}
