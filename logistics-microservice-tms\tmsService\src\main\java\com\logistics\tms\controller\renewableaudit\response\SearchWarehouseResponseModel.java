package com.logistics.tms.controller.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class SearchWarehouseResponseModel {
	@ApiModelProperty("收货地址code")
	private String unloadAddressCode;

	@ApiModelProperty("收货省id")
	private Long unloadProvinceId;

	@ApiModelProperty("收货省")
	private String unloadProvinceName;

	@ApiModelProperty("收货市id")
	private Long unloadCityId;

	@ApiModelProperty("收货市")
	private String unloadCityName;

	@ApiModelProperty("收货区id")
	private Long unloadAreaId;

	@ApiModelProperty("收货区")
	private String unloadAreaName;

	@ApiModelProperty("收货地址详细")
	private String unloadDetailAddress;

	@ApiModelProperty("收货仓库")
	private String unloadWarehouse;

	@ApiModelProperty("收货人")
	private String receiverName;

	@ApiModelProperty("收货人手机号")
	private String receiverMobile;

	@ApiModelProperty("目标经度")
	private String longitude;

	@ApiModelProperty("目标纬度")
	private String latitude;

	@ApiModelProperty("距离(KM)")
	private String distance;

	@ApiModelProperty("作业时间")
	private String jobTime;
}
