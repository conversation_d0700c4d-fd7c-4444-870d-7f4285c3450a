<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleSettlementEventsMapper" >
  <update id="rollbackToWaitSettleStatement" >
    update t_vehicle_settlement_events
    set valid=0,
    last_modified_by= #{userName,jdbcType=VARCHAR},
    last_modified_time= now()
    where vehicle_settlement_id=#{vehicleSettlementId,jdbcType=BIGINT}
  </update>
  <select id="getByVehicleSettlementId" resultType="com.logistics.tms.controller.vehiclesettlement.response.VehicleSettlementEventModel">
    select id as eventId,
      event,
      event_desc as eventDesc,
      event_time as eventTime,
      operator_name as operatorName
      from t_vehicle_settlement_events
      where vehicle_settlement_id=#{vehicleSettlementId,jdbcType=BIGINT}
      and valid=1
      order by created_time asc
  </select>
</mapper>