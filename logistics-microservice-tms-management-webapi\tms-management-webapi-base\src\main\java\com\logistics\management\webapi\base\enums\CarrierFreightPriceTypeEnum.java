package com.logistics.management.webapi.base.enums;

public enum CarrierFreightPriceTypeEnum {
    STAIR(1, "阶梯价格"),
    FIXED(2, "固定单价")
    ;
    private final Integer key;
    private final String value;

    public static CarrierFreightPriceTypeEnum getEnum(Integer key) {
        for (CarrierFreightPriceTypeEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return STAIR;
    }

    CarrierFreightPriceTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
