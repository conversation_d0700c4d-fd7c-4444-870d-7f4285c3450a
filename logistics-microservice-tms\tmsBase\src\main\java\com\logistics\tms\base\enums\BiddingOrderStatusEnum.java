package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * @author: wjf
 * @date: 2024/4/28 10:19
 */
@Getter
@AllArgsConstructor
public enum BiddingOrderStatusEnum {

    DEFAULT(0, ""),
    IN_QUOTATION(1, "报价中"),
    SUSPEND_QUOTATION(2, "暂停报价"),
    WAIT_SELECTED(3, "待选择"),
    COMPLETE_QUOTATION(4, "完成报价"),
    QUOTATION_CANCEL(5, "报价取消"),
    ;

    private final Integer key;
    private final String value;

    public static BiddingOrderStatusEnum getEnum(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
