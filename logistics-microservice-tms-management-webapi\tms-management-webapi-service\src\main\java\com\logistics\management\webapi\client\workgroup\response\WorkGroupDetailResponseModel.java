package com.logistics.management.webapi.client.workgroup.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/22 15:44
 */
@Data
public class WorkGroupDetailResponseModel {

    @ApiModelProperty("智能推送配置表id")
    private Long workGroupId;

    @ApiModelProperty(value = "群名")
    private String groupName;

    @ApiModelProperty(value = "负责人id")
    private Long groupOwnerId;

    @ApiModelProperty(value = "负责人姓名")
    private String groupOwnerUsername;

    @ApiModelProperty("负责人手机号")
    private String groupOwnerMobile;

    @ApiModelProperty(value = "参与人id")
    private Long participantId;

    @ApiModelProperty(value = "参与人姓名")
    private String participantUsername;

    @ApiModelProperty("参与人手机号")
    private String participantMobile;

    @ApiModelProperty(value = "推送设置：1 新建群聊，2 已有群聊")
    private Integer workGroupSource;

    @ApiModelProperty(value = "机器人编码")
    private String workGroupCode;

    @ApiModelProperty(value = "匹配地点：1 发货信息，2 收货信息")
    private String matchingLocation;

    @ApiModelProperty(value = "匹配字段：1 区域，2 仓库")
    private Integer matchingField;

    @ApiModelProperty(value = "配置仓库")
    private String configWarehouse;

    @ApiModelProperty(value = "省市信息")
    private List<WorkGroupDistrictResponseModel> districtList;

    @ApiModelProperty(value = "群简介")
    private String groupDesc;
}
