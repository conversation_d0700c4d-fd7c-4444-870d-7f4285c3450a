package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2023/01/10
*/
@Data
public class TCustomerSmsAuthInfo extends BaseEntity {
    /**
    * 账号id
    */
    @ApiModelProperty("账号id")
    private Long accountId;

    /**
    * 个人手机号验证，用于验证key
    */
    @ApiModelProperty("个人手机号验证，用于验证key")
    private String personalIdentityKey;

    /**
    * 过期时间
    */
    @ApiModelProperty("过期时间")
    private Date expireTime;

    /**
    * 是否使用：0否 1是
    */
    @ApiModelProperty("是否使用：0否 1是")
    private Integer whetherUse;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}