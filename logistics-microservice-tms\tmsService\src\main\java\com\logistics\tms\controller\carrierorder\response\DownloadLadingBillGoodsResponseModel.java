package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2018/9/30 11:48
 */
@Data
public class DownloadLadingBillGoodsResponseModel {

    @ApiModelProperty("货物id")
    private Long goodsId;

    @ApiModelProperty("品名")
    private String goodsName;

    @ApiModelProperty("大类名")
    private String categoryName;

    @ApiModelProperty("规格")
    private Integer length;

    @ApiModelProperty("规格")
    private Integer width;

    @ApiModelProperty("规格")
    private Integer height;

    @ApiModelProperty("规格")
    private String goodsSize;

    @ApiModelProperty("预提件数")
    private BigDecimal expectAmount;

    @ApiModelProperty("实提数")
    private BigDecimal loadAmount;

    @ApiModelProperty("实卸数")
    private BigDecimal unloadAmount;
}
