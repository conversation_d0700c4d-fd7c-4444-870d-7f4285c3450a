package com.logistics.tms.client.feign.tray.basicdata.commonaddress.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/5/17 9:27
 */
@Data
public class UpdateAccessibilityVerifyRequestModel {
    @ApiModelProperty("子地址code")
    private String addressCode;

    @ApiModelProperty("地址标签 1:司机可达;2:地推可达;")
    private Integer accessibilityVerify;
}
