package com.logistics.management.webapi.controller.invoicingmanagement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @author: wjf
 * @date: 2024/6/25 11:14
 */
@Data
public class UpdateBusinessNameRequestDto {

    /**
     * 发票管理id
     */
    @ApiModelProperty(value = "发票管理id",required = true)
    @NotBlank(message = "发票管理id不能为空")
    private String invoicingId;

    /**
     * 业务名称
     */
    @ApiModelProperty(value = "业务名称",required = true)
    @NotBlank(message = "业务名称不能为空")
    @Size(min = 1, max = 50, message = "业务名称不能为空")
    private String businessName;
}
