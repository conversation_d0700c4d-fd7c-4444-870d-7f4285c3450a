package com.logistics.management.webapi.client.workgroup.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2023/12/22 15:44
 */
@Data
public class SearchWorkGroupListResponseModel {

    @ApiModelProperty("智能推送配置表id")
    private Long workGroupId;

    @ApiModelProperty("群名")
    private String groupName;

    @ApiModelProperty(value = "负责人姓名")
    private String groupOwnerUsername;

    @ApiModelProperty("负责人手机号")
    private String groupOwnerMobile;

    @ApiModelProperty("群简介")
    private String groupDesc;

    @ApiModelProperty("状态：0禁用，1启用")
    private Integer enabled;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("创建时间")
    private Date createdTime;

}
