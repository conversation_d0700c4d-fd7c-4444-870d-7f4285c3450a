package com.logistics.management.webapi.api.impl.staff.mapping;

import com.logistics.management.webapi.api.feign.staff.dto.ExportStaffManagementListResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.tms.api.feign.staff.model.ExportStaffManagementListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

import java.util.Optional;

/**
 * @author: wjf
 * @date: 2019/6/10 17:26
 */
public class ExportStaffManagementListMapping extends MapperMapping<ExportStaffManagementListResponseModel,ExportStaffManagementListResponseDto> {
    @Override
    public void configure() {
        ExportStaffManagementListResponseModel source = getSource();
        ExportStaffManagementListResponseDto destination = getDestination();
        if (source != null){
            destination.setStaffPropertyLabel(StaffPropertyEnum.getEnum(source.getStaffProperty()).getValue());
            destination.setType(StaffTypeEnum.getEnum(source.getStaffType()).getValue());
            destination.setGender(GenderEnum.getEnum(source.getGender()).getValue());
            destination.setName(source.getStaffName());
            destination.setMobile(source.getStaffMobile());
            Optional.ofNullable(source.getDriversLicenseDateFrom()).ifPresent(t->
                destination.setDriversLicenseDateFrom(DateUtils.dateToString(t,DateUtils.DATE_TO_STRING_SHORT_PATTERN)));
            Optional.ofNullable(source.getDriversLicenseDateTo()).ifPresent(t->
                destination.setDriversLicenseDateTo(DateUtils.dateToString(t,DateUtils.DATE_TO_STRING_SHORT_PATTERN)));
            Optional.ofNullable(source.getInitialIssuanceDate()).ifPresent(t->
                destination.setInitialIssuanceDate(DateUtils.dateToString(t,DateUtils.DATE_TO_STRING_SHORT_PATTERN)));
            Optional.ofNullable(source.getLaborContractValidDate()).ifPresent(t->
                destination.setLaborContractValidDate(DateUtils.dateToString(t,DateUtils.DATE_TO_STRING_SHORT_PATTERN)));


            Optional.ofNullable(source.getContinueLearningValidDate()).ifPresent(t->
                destination.setLearningValidDate(DateUtils.dateToString(t,DateUtils.DATE_TO_STRING_SHORT_PATTERN)));
            Optional.ofNullable(source.getIntegrityExaminationValidDate()).ifPresent(t->
                destination.setExaminationValidDate(DateUtils.dateToString(t,CommonConstant.DATE_TO_STRING_YM_PATTERN)));
            Optional.ofNullable(source.getOccupationalIssueDate()).ifPresent(t->
                destination.setOccupationalIssueDate(DateUtils.dateToString(t,DateUtils.DATE_TO_STRING_SHORT_PATTERN)));
            Optional.ofNullable(source.getOccupationalValidDate()).ifPresent(t->
                destination.setOccupationalValidDate(DateUtils.dateToString(t,DateUtils.DATE_TO_STRING_SHORT_PATTERN)));

            if(CommonConstant.INTEGER_ONE.equals(source.getIdentityIsForever())){
                destination.setIdentityValidity(CommonConstant.FOREVER);
            }else {
                Optional.ofNullable(source.getIdentityValidity()).ifPresent(t->
                    destination.setIdentityValidity(DateUtils.dateToString(source.getIdentityValidity(),DateUtils.DATE_TO_STRING_SHORT_PATTERN))
                );
            }
            destination.setPermittedType(source.getPermittedType());
            destination.setOccupationalRequirementsCredentialNo(source.getOccupationalRequirementsCredentialNo());
            destination.setIdentityNumber(source.getIdentityNumber());
            destination.setDriversLicenseNo(source.getDriversLicenseNo());
            destination.setAge(source.getAge()!=null&&source.getAge()>0?source.getAge().toString():"");

            destination.setCompanyCarrierName(CompanyTypeEnum.COMPANY.getKey().equals(source.getCompanyCarrierType()) ?
                    source.getCompanyCarrierName() :
                    source.getCarrierContactName() + " " + source.getCarrierContactPhone());

            //实名展示文本
            destination.setRealNameAuthenticationStatusLabel(RealNameAuthenticationStatusEnum.getEnum(source.getRealNameAuthenticationStatus()).getValue());
        }
    }
}
