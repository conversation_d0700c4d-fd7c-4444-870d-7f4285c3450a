<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCertificationPicturesMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCertificationPictures" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="object_type" property="objectType" jdbcType="INTEGER" />
    <result column="object_id" property="objectId" jdbcType="BIGINT" />
    <result column="file_type" property="fileType" jdbcType="INTEGER" />
    <result column="file_type_name" property="fileTypeName" jdbcType="VARCHAR" />
    <result column="file_name" property="fileName" jdbcType="VARCHAR" />
    <result column="file_path" property="filePath" jdbcType="VARCHAR" />
    <result column="upload_user_name" property="uploadUserName" jdbcType="VARCHAR" />
    <result column="upload_time" property="uploadTime" jdbcType="TIMESTAMP" />
    <result column="suffix" property="suffix" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, object_type, object_id, file_type, file_type_name, file_name, file_path, upload_user_name, 
    upload_time, suffix, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_certification_pictures
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_certification_pictures
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCertificationPictures" >
    insert into t_certification_pictures (id, object_type, object_id,
      file_type, file_type_name, file_name, 
      file_path, upload_user_name, upload_time, 
      suffix, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{objectType,jdbcType=INTEGER}, #{objectId,jdbcType=BIGINT}, 
      #{fileType,jdbcType=INTEGER}, #{fileTypeName,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, 
      #{filePath,jdbcType=VARCHAR}, #{uploadUserName,jdbcType=VARCHAR}, #{uploadTime,jdbcType=TIMESTAMP}, 
      #{suffix,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCertificationPictures" useGeneratedKeys="true" keyProperty="id" >
    insert into t_certification_pictures
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="objectType != null" >
        object_type,
      </if>
      <if test="objectId != null" >
        object_id,
      </if>
      <if test="fileType != null" >
        file_type,
      </if>
      <if test="fileTypeName != null" >
        file_type_name,
      </if>
      <if test="fileName != null" >
        file_name,
      </if>
      <if test="filePath != null" >
        file_path,
      </if>
      <if test="uploadUserName != null" >
        upload_user_name,
      </if>
      <if test="uploadTime != null" >
        upload_time,
      </if>
      <if test="suffix != null" >
        suffix,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="objectType != null" >
        #{objectType,jdbcType=INTEGER},
      </if>
      <if test="objectId != null" >
        #{objectId,jdbcType=BIGINT},
      </if>
      <if test="fileType != null" >
        #{fileType,jdbcType=INTEGER},
      </if>
      <if test="fileTypeName != null" >
        #{fileTypeName,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null" >
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="uploadUserName != null" >
        #{uploadUserName,jdbcType=VARCHAR},
      </if>
      <if test="uploadTime != null" >
        #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="suffix != null" >
        #{suffix,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCertificationPictures" >
    update t_certification_pictures
    <set >
      <if test="objectType != null" >
        object_type = #{objectType,jdbcType=INTEGER},
      </if>
      <if test="objectId != null" >
        object_id = #{objectId,jdbcType=BIGINT},
      </if>
      <if test="fileType != null" >
        file_type = #{fileType,jdbcType=INTEGER},
      </if>
      <if test="fileTypeName != null" >
        file_type_name = #{fileTypeName,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null" >
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="uploadUserName != null" >
        upload_user_name = #{uploadUserName,jdbcType=VARCHAR},
      </if>
      <if test="uploadTime != null" >
        upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="suffix != null" >
        suffix = #{suffix,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCertificationPictures" >
    update t_certification_pictures
    set object_type = #{objectType,jdbcType=INTEGER},
      object_id = #{objectId,jdbcType=BIGINT},
      file_type = #{fileType,jdbcType=INTEGER},
      file_type_name = #{fileTypeName,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_path = #{filePath,jdbcType=VARCHAR},
      upload_user_name = #{uploadUserName,jdbcType=VARCHAR},
      upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      suffix = #{suffix,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>