package com.logistics.management.webapi.controller.carrierorderticketsaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class GetReceiptAuditDetailResponseDto {

    @ApiModelProperty(value = "审核人")
    private String auditorName = "";

    @ApiModelProperty(value = "审核时间")
    private String auditTime = "";

    @ApiModelProperty(value = "审核状态; 0 待审核，1 已审核，2 已驳回")
    private String auditStatus = "";

    @ApiModelProperty(value = "审核状态文本")
    private String auditStatusLabel = "";

    @ApiModelProperty(value = "回单图片")
    private List<String> ticketImages;

    @ApiModelProperty(value = "备注")
    private String remark = "";
}
