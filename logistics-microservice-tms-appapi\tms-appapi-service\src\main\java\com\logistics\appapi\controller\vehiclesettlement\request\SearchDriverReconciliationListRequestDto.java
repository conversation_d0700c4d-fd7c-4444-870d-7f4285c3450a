package com.logistics.appapi.controller.vehiclesettlement.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author：wjf
 * @date：2021/4/9 14:00
 */
@Data
public class SearchDriverReconciliationListRequestDto extends AbstractPageForm<SearchDriverReconciliationListRequestDto> {
    @ApiModelProperty("结算状态：空 全部，2 待确认，3 待处理，4 待结清（包含部分结清），6 已结清")
    private String status;
    @ApiModelProperty("月份（yyyy-MM）")
    private String settlementMonth;
}
