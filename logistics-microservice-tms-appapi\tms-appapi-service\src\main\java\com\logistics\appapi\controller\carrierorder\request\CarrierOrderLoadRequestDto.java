package com.logistics.appapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: wjf
 * @date: 2018/10/17 16:22
 */
@Data
public class CarrierOrderLoadRequestDto {

    @ApiModelProperty(value = "运单Id",required = true)
    @NotBlank(message = "运单ID不能为空")
    private String carrierOrderId;

    @ApiModelProperty(value = "提货数量",required = true)
    @NotEmpty(message = "货物信息不能为空")
    @Valid
    private List<CarrierOrderGoodsLoadUnloadRequestDto> goodsList;

    @ApiModelProperty(value = "提卸货单据")
    private List<CarrierOrderTicketRequestDto> tickets;

    @ApiModelProperty(value = "货物单位：1 件，2 吨", required = true)
    @NotBlank(message = "货物单位不能为空")
    private String goodsUnit;

    @ApiModelProperty(value = "现场其他托盘信息(提货接口用) v1.1.5")
    private SiteOtherPalletsRequestDto siteOtherPallets;

    @ApiModelProperty(value = "提货方式 1 票据提货，2 二维码提货")
    public String deliveryMethod;
}
