package com.logistics.management.webapi.controller.region;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportExcelRegionDetail;
import com.logistics.management.webapi.base.constant.ExportExcelRegionInfo;
import com.logistics.management.webapi.client.region.RegionClient;
import com.logistics.management.webapi.client.region.request.*;
import com.logistics.management.webapi.client.region.response.*;
import com.logistics.management.webapi.controller.region.mapping.GetCompanyByRegionMapping;
import com.logistics.management.webapi.controller.region.mapping.GetRegionCompanyMapping;
import com.logistics.management.webapi.controller.region.mapping.GetRegionDetailMapping;
import com.logistics.management.webapi.controller.region.mapping.SearchRegionListMapping;
import com.logistics.management.webapi.controller.region.request.*;
import com.logistics.management.webapi.controller.region.response.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ExcelUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 物流区域配置
 * @author: wjf
 * @date: 2024/6/4 11:14
 */
@Api(value = "物流大区配置")
@RestController
@RequestMapping(value = "/api/region")
public class RegionController {

    @Resource
    private RegionClient regionClient;

    /**
     * 物流大区列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "物流大区列表")
    @PostMapping(value = "/searchList")
    public Result<PageInfo<SearchRegionResponseDto>> searchList(@RequestBody SearchRegionRequestDto requestDto) {
        Result<PageInfo<SearchRegionResponseModel>> result = regionClient.searchList(MapperUtils.mapper(requestDto, SearchRegionRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SearchRegionResponseDto> regionDtoList = MapperUtils.mapper(pageInfo.getList(), SearchRegionResponseDto.class, new SearchRegionListMapping());
        pageInfo.setList(regionDtoList);
        return Result.success(pageInfo);
    }

    /**
     * 获取物流大区下车主列表 3.22.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "获取物流大区下车主列表")
    @PostMapping(value = "/getRegionCompany")
    public Result<List<RegionCompanyResponseDto>> getRegionCompany(@RequestBody @Valid RegionDetailRequestDto requestDto) {
        Result<List<RegionCompanyResponseModel>> result = regionClient.getRegionCompany(MapperUtils.mapper(requestDto, RegionDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), RegionCompanyResponseDto.class, new GetRegionCompanyMapping()));
    }

    /**
     * 物流大区新增修改 3.22.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "物流大区新增修改")
    @PostMapping(value = "/saveOrModifyRegion")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> saveOrModifyRegion(@RequestBody @Valid SaveOrModifyRegionRequestDto requestDto) {
        return regionClient.saveOrModifyRegion(MapperUtils.mapper(requestDto, SaveOrModifyRegionRequestModel.class));
    }

    /**
     * 编辑详情 3.22.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "编辑详情")
    @PostMapping(value = "/getDetail")
    public Result<RegionDetailResponseDto> getDetail(@RequestBody @Valid RegionDetailRequestDto requestDto) {
        Result<RegionDetailResponseModel> result = regionClient.getDetail(MapperUtils.mapper(requestDto, RegionDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), RegionDetailResponseDto.class, new GetRegionDetailMapping()));
    }

    /**
     * 启用/禁用大区信息
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "启用/禁用大区信息")
    @PostMapping(value = "/enableRegion")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> enableRegion(@RequestBody @Valid EnableRegionRequestDto requestDto) {
        return regionClient.enableRegion(MapperUtils.mapper(requestDto, EnableRegionRequestModel.class));
    }

    /**
     * 导出
     * @param requestDto
     * @param response
     */
    @ApiOperation(value = "导出")
    @GetMapping(value = "/export")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void export(SearchRegionRequestDto requestDto, HttpServletResponse response) {
        Result<List<SearchRegionResponseModel>> result = regionClient.export(MapperUtils.mapper(requestDto, SearchRegionRequestModel.class));
        result.throwException();
        List<SearchRegionResponseDto> resultList = MapperUtils.mapper(result.getData(), SearchRegionResponseDto.class, new SearchRegionListMapping());
        String fileName = "大区列表" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String, String> exportTypeMap = ExportExcelRegionInfo.getExportRegionInfo();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportTypeMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return resultList;
            }
        });
    }

    /**
     * 移除
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "移除")
    @PostMapping(value = "/removeRegion")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> removeRegion(@RequestBody @Valid RemoveRegionRequestDto requestDto) {
        return regionClient.removeRegion(MapperUtils.mapper(requestDto, RemoveRegionRequestModel.class));
    }

    /**
     * 大区详情列表
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "大区详情列表")
    @PostMapping(value = "/searchDetailList")
    public Result<PageInfo<SearchRegionDetailResponseDto>> searchDetailList(@RequestBody @Valid SearchRegionDetailRequestDto requestDto) {
        Result<PageInfo<SearchRegionDetailResponseModel>> result = regionClient.searchDetailList(MapperUtils.mapper(requestDto, SearchRegionDetailRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SearchRegionDetailResponseDto> list = MapperUtils.mapper(pageInfo.getList(), SearchRegionDetailResponseDto.class);
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    /**
     * 大区详情列表导出
     * @param requestDto
     * @param response
     */
    @ApiOperation(value = "大区详情列表导出")
    @GetMapping(value = "/exportRegionDetail")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportRegionDetail(@Valid SearchRegionDetailRequestDto requestDto, HttpServletResponse response) {
        Result<List<SearchRegionDetailResponseModel>> result = regionClient.exportRegionDetail(MapperUtils.mapper(requestDto, SearchRegionDetailRequestModel.class));
        result.throwException();
        List<SearchRegionDetailResponseDto> list = MapperUtils.mapper(result.getData(), SearchRegionDetailResponseDto.class);
        String regionName = "";
        if (ListUtils.isNotEmpty(list)){
            regionName = list.get(CommonConstant.INTEGER_ZERO).getRegionName();
        }
        String fileName = regionName + "城市列表" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String, String> exportTypeMap = ExportExcelRegionDetail.getExportExcelRegionDetail();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportTypeMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return list;
            }
        });
    }

    /**
     * 根据省市查询区域下的车主（排除加入黑名单的车主） 3.22.0
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "根据省市查询区域下的车主（排除加入黑名单的车主）")
    @PostMapping(value = "/getCompanyByRegion")
    public Result<List<GetCompanyCarrierByRegionResponseDto>> getCompanyByRegion(@RequestBody @Valid GetCompanyCarrierByRegionRequestDto requestDto) {
        Result<List<GetCompanyCarrierByRegionResponseModel>> result = regionClient.getCompanyByRegion(MapperUtils.mapper(requestDto, GetCompanyCarrierByRegionRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), GetCompanyCarrierByRegionResponseDto.class, new GetCompanyByRegionMapping()));
    }
}
