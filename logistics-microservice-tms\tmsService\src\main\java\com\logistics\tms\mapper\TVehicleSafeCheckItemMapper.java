package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TVehicleSafeCheckItem;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TVehicleSafeCheckItemMapper extends BaseMapper<TVehicleSafeCheckItem> {

    int batchInsert(@Param("list") List<TVehicleSafeCheckItem> list);

    int batchUpdate(@Param("list") List<TVehicleSafeCheckItem> list);

    List<TVehicleSafeCheckItem> getListBySafeCheckVehicleId(@Param("safeCheckVehicleId") Long safeCheckVehicleId);

}