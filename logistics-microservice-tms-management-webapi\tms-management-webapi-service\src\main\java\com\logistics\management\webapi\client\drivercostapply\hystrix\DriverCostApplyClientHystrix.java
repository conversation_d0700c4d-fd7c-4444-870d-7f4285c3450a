package com.logistics.management.webapi.client.drivercostapply.hystrix;


import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.drivercostapply.DriverCostApplyClient;
import com.logistics.management.webapi.client.drivercostapply.request.*;
import com.logistics.management.webapi.client.drivercostapply.response.DriverCostApplyDetailResponseModel;
import com.logistics.management.webapi.client.drivercostapply.response.GetDeductionsCostBalanceResponseModel;
import com.logistics.management.webapi.client.drivercostapply.response.SearchCostApplyListResponseModel;
import com.logistics.management.webapi.client.drivercostapply.response.SearchCostApplySummaryResponseModel;
import com.logistics.management.webapi.controller.drivercostapply.request.DeductionsCostApplyRequestModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/3 13:41
 */
@Component
public class DriverCostApplyClientHystrix implements DriverCostApplyClient {

    @Override
    public Result<PageInfo<SearchCostApplyListResponseModel>> searchCostApplyList(SearchCostApplyListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<DriverCostApplyDetailResponseModel> driverCostApplyDetail(DriverCostApplyDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> undoDriverCostApply(UndoDriverCostApplyRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> auditOrRejectCostApply(AuditOrRejectCostApplyRequestModel requestModel) {
        return Result.timeout();
    }

	@Override
	public Result<PageInfo<SearchCostApplySummaryResponseModel>> searchCostApplySummary(SearchCostApplySummaryRequestModel requestModel) {
		return Result.timeout();
	}

    @Override
    public Result<Boolean> deductionsCostApply(DeductionsCostApplyRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetDeductionsCostBalanceResponseModel>> getDeductionsCostBalance(GetDeductionsCostBalanceRequestModel requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> redChargeRefund(RedChargeRefundCostApplyRequestModel requestModel) {
        return Result.timeout();
    }
}
