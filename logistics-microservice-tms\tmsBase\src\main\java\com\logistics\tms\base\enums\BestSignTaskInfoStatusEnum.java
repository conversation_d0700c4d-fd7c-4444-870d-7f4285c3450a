package com.logistics.tms.base.enums;
/**
 * <AUTHOR>
 * @date 2020/3/18 15:15
 */
public enum BestSignTaskInfoStatusEnum {
    WAIT_HANDLE(0, "待处理"),
    FINISH_HANDLE(1, "处理完成"),
    CERT_BACKFILL(2, "证书已回填"),
    ;

    private Integer key;
    private String value;

    BestSignTaskInfoStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
