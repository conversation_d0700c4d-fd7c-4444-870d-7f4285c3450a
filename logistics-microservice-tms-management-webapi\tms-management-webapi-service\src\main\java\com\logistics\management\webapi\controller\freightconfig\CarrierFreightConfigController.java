package com.logistics.management.webapi.controller.freightconfig;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.freightconfig.CarrierFreightConfigClient;
import com.logistics.management.webapi.client.freightconfig.request.CarrierFreightConfigAddRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.CarrierFreightConfigEditRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.CarrierFreightConfigListRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.CarrierFreightConfigRequestModel;
import com.logistics.management.webapi.client.freightconfig.response.CarrierFreightConfigDetailResponseModel;
import com.logistics.management.webapi.client.freightconfig.response.CarrierFreightConfigListResponseModel;
import com.logistics.management.webapi.controller.freightconfig.mapping.CarrierFreightConfigDetailMapping;
import com.logistics.management.webapi.controller.freightconfig.mapping.CarrierFreightConfigListMapping;
import com.logistics.management.webapi.controller.freightconfig.request.CarrierFreightConfigAddRequestDto;
import com.logistics.management.webapi.controller.freightconfig.request.CarrierFreightConfigEditRequestDto;
import com.logistics.management.webapi.controller.freightconfig.request.CarrierFreightConfigListRequestDto;
import com.logistics.management.webapi.controller.freightconfig.request.CarrierFreightConfigRequestDto;
import com.logistics.management.webapi.controller.freightconfig.response.CarrierFreightConfigDetailResponseDto;
import com.logistics.management.webapi.controller.freightconfig.response.CarrierFreightConfigListResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@Api(tags = "运价配置管理")
@RequestMapping(value = "/api/freight/config")
public class CarrierFreightConfigController {

    @Resource
    private CarrierFreightConfigClient carrierFreightConfigClient;

    @PostMapping(value = "/searchList")
    @ApiOperation(value = "车主运价配置管理列表", tags = "1.3.5")
    Result<List<CarrierFreightConfigListResponseDto>> searchList(@Valid @RequestBody CarrierFreightConfigListRequestDto requestDto) {
        Result<List<CarrierFreightConfigListResponseModel>> listResult =
                carrierFreightConfigClient.searchList(MapperUtils.mapper(requestDto, CarrierFreightConfigListRequestModel.class));
        listResult.throwException();
        List<CarrierFreightConfigListResponseDto> result =
                MapperUtils.mapper(listResult.getData(), CarrierFreightConfigListResponseDto.class, new CarrierFreightConfigListMapping());
        return Result.success(result);
    }

    @PostMapping(value = "/detail")
    @ApiOperation(value = "车主运价配置查看", tags = "1.3.5")
    Result<CarrierFreightConfigDetailResponseDto> detail(@Valid @RequestBody CarrierFreightConfigRequestDto requestDto) {
        Result<CarrierFreightConfigDetailResponseModel> detail =
                carrierFreightConfigClient.detail(MapperUtils.mapper(requestDto, CarrierFreightConfigRequestModel.class));
        detail.throwException();
        CarrierFreightConfigDetailResponseDto result =
                MapperUtils.mapper(detail.getData(), CarrierFreightConfigDetailResponseDto.class, new CarrierFreightConfigDetailMapping());
        return Result.success(result);
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "新增车主运价配置", tags = "1.3.5")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    Result<Boolean> add(@Valid @RequestBody CarrierFreightConfigAddRequestDto requestDto) {
        Result<Boolean> add = carrierFreightConfigClient.add(MapperUtils.mapper(requestDto, CarrierFreightConfigAddRequestModel.class));
        add.throwException();
        return Result.success(true);
    }

    @PostMapping(value = "/edit")
    @ApiOperation(value = "编辑车主运价配置", tags = "1.3.5")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    Result<Boolean> edit(@Valid @RequestBody CarrierFreightConfigEditRequestDto requestDto) {
        Result<Boolean> edit = carrierFreightConfigClient.edit(MapperUtils.mapper(requestDto, CarrierFreightConfigEditRequestModel.class));
        edit.throwException();
        return Result.success(true);
    }

    @PostMapping(value = "/delete")
    @ApiOperation(value = "删除车主运价配置", tags = "1.3.5")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    Result<Boolean> delete(@Valid @RequestBody CarrierFreightConfigRequestDto requestDto) {
        Result<Boolean> delete = carrierFreightConfigClient.delete(MapperUtils.mapper(requestDto, CarrierFreightConfigRequestModel.class));
        delete.throwException();
        return Result.success(true);
    }
}
