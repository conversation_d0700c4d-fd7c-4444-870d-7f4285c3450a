package com.logistics.tms.mapper;

import com.logistics.tms.entity.TDemandOrderCustomerRel;
import com.logistics.tms.rabbitmq.publisher.model.SyncSignDemandOrderListToGroundPushModel;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2023/09/06
*/
@Mapper
public interface TDemandOrderCustomerRelMapper extends BaseMapper<TDemandOrderCustomerRel> {

    List<SyncSignDemandOrderListToGroundPushModel> getByDemandOrderIds(@Param("demandOrderIds")String demandOrderIds);

}