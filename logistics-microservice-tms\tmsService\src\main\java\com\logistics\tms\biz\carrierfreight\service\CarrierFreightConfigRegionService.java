package com.logistics.tms.biz.carrierfreight.service;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.CarrierFreightConfigLadderModeEnum;
import com.logistics.tms.base.enums.CarrierFreightConfigSchemeTypeEnum;
import com.logistics.tms.biz.carrierfreight.CarrierFreightConfigLadderBiz;
import com.logistics.tms.biz.carrierfreight.CarrierFreightConfigSchemeBiz;
import com.logistics.tms.controller.freightconfig.request.ladder.CarrierFreightConfigLadderRequestModel;
import com.logistics.tms.controller.freightconfig.request.region.CarrierFreightConfigRegionAddRequestModel;
import com.logistics.tms.controller.freightconfig.request.region.CarrierFreightConfigRegionEditRequestModel;
import com.logistics.tms.controller.freightconfig.request.region.CarrierFreightConfigRegionRequestModel;
import com.logistics.tms.controller.freightconfig.response.ladder.CarrierFreightConfigPriceDesignResponseModel;
import com.logistics.tms.controller.freightconfig.response.region.CarrierFreightConfigRegionResponseModel;
import com.logistics.tms.entity.TCarrierFreightConfigLadder;
import com.logistics.tms.entity.TCarrierFreightConfigScheme;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.exception.BizException;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CarrierFreightConfigRegionService {

    @Resource
    private CarrierFreightConfigSchemeBiz carrierFreightConfigSchemeBiz;

    @Resource
    private CarrierFreightConfigLadderBiz carrierFreightConfigLadderBiz;

    /**
     * 区域计价配置查看
     *
     * @param requestModel 请求Model
     * @return 区域计价配置详情
     */
    public List<CarrierFreightConfigRegionResponseModel> detail(CarrierFreightConfigRegionRequestModel requestModel) {

        // 查询方案配置Id
        List<TCarrierFreightConfigScheme> configScheme = carrierFreightConfigSchemeBiz.getConfigScheme(requestModel.getFreightConfigId());
        if (ListUtils.isEmpty(configScheme)) {
            return Collections.emptyList();
        }
        // 根据方案查询阶梯信息
        List<Long> schemeIds = configScheme
                .stream()
                .map(TCarrierFreightConfigScheme::getId)
                .collect(Collectors.toList());
        Integer ladderMode = CarrierFreightConfigLadderModeEnum.SCHEME_CONFIG.getKey();
        Map<Long, CarrierFreightConfigPriceDesignResponseModel> ladderDetailMap = carrierFreightConfigLadderBiz.getLadderDetail(ladderMode, schemeIds);
        return configScheme.stream()
                .map(s -> {
                    CarrierFreightConfigRegionResponseModel model = new CarrierFreightConfigRegionResponseModel();
                    model.setFreightConfigSchemeId(s.getId());
                    model.setSchemeType(s.getSchemeType());
                    model.setPriceDesign(ladderDetailMap.get(s.getId()));
                    return model;
                })
                .collect(Collectors.toList());
    }

    /**
     * 区域计价配置新增
     *
     * @param requestModel 请求Model
     * @return boolean
     */
    @Transactional
    public boolean add(CarrierFreightConfigRegionAddRequestModel requestModel) {
        if (ListUtils.isNotEmpty(requestModel.getSchemeList())) {
            requestModel.getSchemeList().forEach(f -> {
                // 新增方案
                TCarrierFreightConfigScheme scheme = carrierFreightConfigSchemeBiz.addConfigScheme(requestModel.getFreightConfigId(), f.getSchemeType());
                // 新增阶梯
                CarrierFreightConfigLadderModeEnum ladderModeEnum = CarrierFreightConfigLadderModeEnum.getEnumBySchemeType(scheme.getSchemeType());
                carrierFreightConfigLadderBiz.add(ladderModeEnum.getKey(), scheme.getId(), CommonConstant.LONG_ZERO, f.getLadderConfigList());
            });
        }
        return true;
    }

    /**
     * 区域计价编辑
     *
     * @param requestModel 请求Model
     * @return boolean
     */
    @Transactional
    public boolean edit(CarrierFreightConfigRegionEditRequestModel requestModel) {

        var editOrAddMap =
                requestModel
                        .getSchemeList()
                        .stream()
                        .collect(Collectors.partitioningBy(p -> Objects.nonNull(p.getFreightConfigSchemeId())));
        var editItemRequestModels = editOrAddMap.get(Boolean.TRUE);

        List<Long> existIds = Lists.newArrayList();

        // 处理编辑逻辑
        editItemRequestModels.forEach(f -> {
            // 查询方案配置
            TCarrierFreightConfigScheme configScheme = carrierFreightConfigSchemeBiz.getConfigSchemeById(f.getFreightConfigSchemeId())
                    .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.CARRIER_FREIGHT_CONFIG_SCHEME_NOT_EXIST));

            // 编辑方案
            carrierFreightConfigSchemeBiz.editSchemeType(f.getFreightConfigSchemeId(), f.getSchemeType());

            List<CarrierFreightConfigLadderRequestModel> ladderList = f.getLadderConfigList();
            Map<Boolean, List<CarrierFreightConfigLadderRequestModel>> ladderMap = ladderList
                    .stream()
                    .collect(Collectors.partitioningBy(p -> Objects.nonNull(p.getFreightConfigLadderId())));

            // 新增的阶梯
            CarrierFreightConfigLadderModeEnum ladderModeEnum = CarrierFreightConfigLadderModeEnum.getEnumBySchemeType(f.getSchemeType());
            List<CarrierFreightConfigLadderRequestModel> addLadderList = ladderMap.get(Boolean.FALSE);
            // 编辑的阶梯
            List<CarrierFreightConfigLadderRequestModel> editLadderList = ladderMap.get(Boolean.TRUE);
            // 移除的阶梯
            Integer ladderMode = CarrierFreightConfigLadderModeEnum.SCHEME_CONFIG.getKey();
            List<Long> ladderIds = editLadderList
                    .stream()
                    .map(CarrierFreightConfigLadderRequestModel::getAllLadderId)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());

            List<Long> deleteIds = carrierFreightConfigLadderBiz.getLadderByModeId(ladderMode, configScheme.getId())
                    .stream()
                    .map(TCarrierFreightConfigLadder::getId)
                    .filter(id -> !ladderIds.contains(id))
                    .collect(Collectors.toList());
            carrierFreightConfigLadderBiz.delete(deleteIds);
            // 新增
            carrierFreightConfigLadderBiz.add(ladderModeEnum.getKey(),
                    f.getFreightConfigSchemeId(),
                    CommonConstant.LONG_ZERO,
                    addLadderList);
            // 编辑
            carrierFreightConfigLadderBiz.schemeLadderEdit(f.getFreightConfigSchemeId(), editLadderList);
            // 已存在的方案Id
            existIds.add(f.getFreightConfigSchemeId());
        });

        // 移除删除的方案
        carrierFreightConfigSchemeBiz.removeSchemeNotExistsIdByIds(requestModel.getFreightConfigId(),
                CarrierFreightConfigSchemeTypeEnum.getRegionType(),
                existIds,
                BaseContextHandler.getUserName());

        // 新增
        var addItemRequestModels = editOrAddMap.get(Boolean.FALSE);
        if (ListUtils.isNotEmpty(addItemRequestModels)) {
            CarrierFreightConfigRegionAddRequestModel addRequestModel = new CarrierFreightConfigRegionAddRequestModel();
            addRequestModel.setFreightConfigId(requestModel.getFreightConfigId());
            addRequestModel.setSchemeList(MapperUtils.mapper(addItemRequestModels,
                    CarrierFreightConfigRegionAddRequestModel.CarrierFreightConfigRegionAddItemRequestModel.class));
            this.add(addRequestModel);
        }

        return true;
    }
}
