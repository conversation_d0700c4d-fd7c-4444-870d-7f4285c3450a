package com.logistics.management.webapi.base.enums;

/**
 * @author: wjf
 * @date: 2019/12/23 15:07
 */
public enum OilRefundReasonTypeEnum {
    DEFAULT(-1,""),
    OIL_FILLED(0,"其他"),
    SYB_CARD_LOST(10,"丢失副卡"),
    TRANSFER_OF_VEHICLES(20,"车辆过户"),
    VEHICLE_SCRAPPING(30,"车辆报废"),
    OIL_FILLED_ERROR(40,"充油充错"),
    ;

    private Integer key;
    private String value;

    OilRefundReasonTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static OilRefundReasonTypeEnum getEnum(int key) {
        for (OilRefundReasonTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
