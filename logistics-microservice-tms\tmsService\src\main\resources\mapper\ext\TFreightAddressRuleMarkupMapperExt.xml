<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TFreightAddressRuleMarkupMapper" >


    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TFreightAddressRuleMarkup">
        <foreach collection="list" item="item" separator=";">
            insert into t_freight_address_rule_markup
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    id,
                </if>
                <if test="item.freightAddressRuleId != null" >
                    freight_address_rule_id,
                </if>
                <if test="item.markIndex != null" >
                    mark_index,
                </if>
                <if test="item.loadAmount != null" >
                    load_amount,
                </if>
                <if test="item.unloadAmount != null" >
                    unload_amount,
                </if>
                <if test="item.markupFreightFee != null" >
                    markup_freight_fee,
                </if>
                <if test="item.createdBy != null" >
                    created_by,
                </if>
                <if test="item.createdTime != null" >
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="item.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.freightAddressRuleId != null" >
                    #{item.freightAddressRuleId,jdbcType=BIGINT},
                </if>
                <if test="item.markIndex != null" >
                    #{item.markIndex,jdbcType=INTEGER},
                </if>
                <if test="item.loadAmount != null" >
                    #{item.loadAmount,jdbcType=INTEGER},
                </if>
                <if test="item.unloadAmount != null" >
                    #{item.unloadAmount,jdbcType=INTEGER},
                </if>
                <if test="item.markupFreightFee != null" >
                    #{item.markupFreightFee,jdbcType=DECIMAL},
                </if>
                <if test="item.createdBy != null" >
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <select id="getByFreightAddressRuleIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_freight_address_rule_markup
        where valid = 1
        and freight_address_rule_id in (${freightAddressRuleIds})
    </select>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
        update t_freight_address_rule_markup
        <set >
            <if test="item.freightAddressRuleId != null" >
                freight_address_rule_id = #{item.freightAddressRuleId,jdbcType=BIGINT},
            </if>
            <if test="item.markIndex != null" >
                mark_index = #{item.markIndex,jdbcType=INTEGER},
            </if>
            <if test="item.loadAmount != null" >
                load_amount = #{item.loadAmount,jdbcType=INTEGER},
            </if>
            <if test="item.unloadAmount != null" >
                unload_amount = #{item.unloadAmount,jdbcType=INTEGER},
            </if>
            <if test="item.markupFreightFee != null" >
                markup_freight_fee = #{item.markupFreightFee,jdbcType=DECIMAL},
            </if>
            <if test="item.createdBy != null" >
                created_by = #{item.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="item.createdTime != null" >
                created_time = #{item.createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastModifiedBy != null" >
                last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="item.lastModifiedTime != null" >
                last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.valid != null" >
                valid = #{item.valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getByRuleIdLoadAmount" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_freight_address_rule_markup
        where valid = 1
        and freight_address_rule_id = #{ruleId,jdbcType=BIGINT}
        and load_amount = #{loadAmount,jdbcType = INTEGER}
        and unload_amount = #{unloadAmount,jdbcType = INTEGER}
    </select>
</mapper>