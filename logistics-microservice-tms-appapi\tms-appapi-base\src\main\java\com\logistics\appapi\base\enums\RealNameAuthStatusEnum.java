package com.logistics.appapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum RealNameAuthStatusEnum {

    DEFAULT(-1, ""),
    NO_REAL_NAME(0, "待实名"),
    WAIT_REAL_NAME(1, "实名中"),
    REAL_NAME(2, "已实名"),
    ;

    private Integer key;

    private String value;

    public static RealNameAuthStatusEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
