package com.logistics.tms.api.feign.traycost.model;
import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/4/20 18:35
 */
@Data
public class SearchCostRecordsListRequestModel extends AbstractPageForm<SearchCostRecordsListRequestModel> {
    @ApiModelProperty(value = "托盘费用id")
    private Long trayCostId;

    @ApiModelProperty("id转化为一下2个字段查询")
    private Integer entrustType;//委托类型
    private Integer goodsUnit;//单位
}
