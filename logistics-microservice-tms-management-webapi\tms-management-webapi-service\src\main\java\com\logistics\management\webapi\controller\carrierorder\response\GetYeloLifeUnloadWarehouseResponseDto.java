package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/24
 */
@Data
public class GetYeloLifeUnloadWarehouseResponseDto {

	@ApiModelProperty("卸货地址code")
	private String unloadWarehouseCode = "";

	@ApiModelProperty("卸货省份id")
	private String unloadProvinceId = "";

	@ApiModelProperty("卸货省份名字")
	private String unloadProvinceName = "";

	@ApiModelProperty("卸货城市id")
	private String unloadCityId = "";

	@ApiModelProperty("卸货城市名字")
	private String unloadCityName = "";

	@ApiModelProperty("卸货县区id")
	private String unloadAreaId = "";

	@ApiModelProperty("卸货县区名字")
	private String unloadAreaName = "";

	@ApiModelProperty("卸货详细地址")
	private String unloadDetailAddress = "";

	@ApiModelProperty("卸货仓库")
	private String unloadWarehouse = "";

	@ApiModelProperty("卸货人姓名")
	private String receiverName = "";

	@ApiModelProperty("卸货人手机号")
	private String receiverMobile = "";
}
