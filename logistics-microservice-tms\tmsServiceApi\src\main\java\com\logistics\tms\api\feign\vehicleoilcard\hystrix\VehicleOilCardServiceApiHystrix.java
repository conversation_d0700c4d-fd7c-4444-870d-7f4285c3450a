package com.logistics.tms.api.feign.vehicleoilcard.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.vehicleoilcard.VehicleOilCardServiceApi;
import com.logistics.tms.api.feign.vehicleoilcard.model.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/4 15:13
 */
@Component
public class VehicleOilCardServiceApiHystrix implements VehicleOilCardServiceApi {
    @Override
    public Result<PageInfo<SearchVehicleOilCardListResponseModel>> searchVehicleOilCardList(SearchVehicleOilCardListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addVehicleOilCard(AddVehicleOilCardRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<VehicleOilCardDetailResponseModel> vehicleOilCardDetail(VehicleOilCardIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> unBindVehicleOilCard(VehicleOilCardIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> bindVehicleOilCard(BindVehicleOilCardRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetVehicleOilCardRecordResponseModel>> getVehicleOilCardRecord(VehicleOilCardIdRequestModel requestModel) {
        return Result.timeout();
    }
}
