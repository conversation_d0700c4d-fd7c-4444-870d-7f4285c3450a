package com.logistics.tms.controller.staff.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ExportStaffManagementListResponseModel {

    @ApiModelProperty("人员基本信息表ID")
    private Long staffId;

    @ApiModelProperty("人员类别 1 驾驶员 2 押运员 3 驾驶员&押运员")
    private Integer staffType;

    @ApiModelProperty("人员机构 1 自主，2 外部，3 自营")
    private Integer staffProperty;

    @ApiModelProperty("性别")
    private Integer gender;

    @ApiModelProperty("从业资格证号")
    private String occupationalRequirementsCredentialNo;

    @ApiModelProperty("人员姓名")
    private String staffName;

    @ApiModelProperty("人员手机号")
    private String staffMobile;

    @ApiModelProperty("准驾车型")
    private String permittedType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("新增人")
    private String createdBy;

    @ApiModelProperty("操作人")
    private String lastModifiedBy;

    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;

    private String identityNumber;

    private Integer age;

    private Date identityValidity;

    private Integer identityIsForever;

    private String laborContractNo;

    private Date laborContractValidDate;

    private Date initialIssuanceDate;

    private String driversLicenseNo;

    private Date driversLicenseDateFrom;

    private Date driversLicenseDateTo;

    private Date occupationalIssueDate;

    private Date occupationalValidDate;

    private Date continueLearningValidDate;

    private Date integrityExaminationValidDate;

    @ApiModelProperty(value = "车主ID")
    private Long companyCarrierId;

    @ApiModelProperty(value = "车主名称")
    private String companyCarrierName;

    @ApiModelProperty(value = "车主公司类型：1 公司，2 个人")
    private Integer companyCarrierType;

    @ApiModelProperty("车主账号名称")
    private String carrierContactName;

    @ApiModelProperty("车主账号手机号（原长度50）")
    private String carrierContactPhone;

    @ApiModelProperty("实名认证(个人): 0 待实名 1 实名中 2 已实名")
    private Integer realNameAuthenticationStatus;
}
