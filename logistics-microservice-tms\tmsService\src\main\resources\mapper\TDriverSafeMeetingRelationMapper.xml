<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TDriverSafeMeetingRelationMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDriverSafeMeetingRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="safe_meeting_id" jdbcType="BIGINT" property="safeMeetingId" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
    <result column="staff_mobile" jdbcType="VARCHAR" property="staffMobile" />
    <result column="staff_property" jdbcType="INTEGER" property="staffProperty" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="study_time" jdbcType="TIMESTAMP" property="studyTime" />
    <result column="sign_time" jdbcType="TIMESTAMP" property="signTime" />
    <result column="sign_locate_address" jdbcType="VARCHAR" property="signLocateAddress" />
    <result column="staff_driver_image_url" jdbcType="VARCHAR" property="staffDriverImageUrl" />
    <result column="sign_image_url" jdbcType="VARCHAR" property="signImageUrl" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, safe_meeting_id, staff_id, staff_name, staff_mobile, staff_property, status, 
    study_time, sign_time, sign_locate_address, staff_driver_image_url, sign_image_url, 
    created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_driver_safe_meeting_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_driver_safe_meeting_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDriverSafeMeetingRelation">
    insert into t_driver_safe_meeting_relation (id, safe_meeting_id, staff_id, 
      staff_name, staff_mobile, staff_property, 
      status, study_time, sign_time, 
      sign_locate_address, staff_driver_image_url, 
      sign_image_url, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{safeMeetingId,jdbcType=BIGINT}, #{staffId,jdbcType=BIGINT}, 
      #{staffName,jdbcType=VARCHAR}, #{staffMobile,jdbcType=VARCHAR}, #{staffProperty,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{studyTime,jdbcType=TIMESTAMP}, #{signTime,jdbcType=TIMESTAMP}, 
      #{signLocateAddress,jdbcType=VARCHAR}, #{staffDriverImageUrl,jdbcType=VARCHAR}, 
      #{signImageUrl,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDriverSafeMeetingRelation">
    insert into t_driver_safe_meeting_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="safeMeetingId != null">
        safe_meeting_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="staffName != null">
        staff_name,
      </if>
      <if test="staffMobile != null">
        staff_mobile,
      </if>
      <if test="staffProperty != null">
        staff_property,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="studyTime != null">
        study_time,
      </if>
      <if test="signTime != null">
        sign_time,
      </if>
      <if test="signLocateAddress != null">
        sign_locate_address,
      </if>
      <if test="staffDriverImageUrl != null">
        staff_driver_image_url,
      </if>
      <if test="signImageUrl != null">
        sign_image_url,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="safeMeetingId != null">
        #{safeMeetingId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffName != null">
        #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null">
        #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="staffProperty != null">
        #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="studyTime != null">
        #{studyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="signTime != null">
        #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="signLocateAddress != null">
        #{signLocateAddress,jdbcType=VARCHAR},
      </if>
      <if test="staffDriverImageUrl != null">
        #{staffDriverImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="signImageUrl != null">
        #{signImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDriverSafeMeetingRelation">
    update t_driver_safe_meeting_relation
    <set>
      <if test="safeMeetingId != null">
        safe_meeting_id = #{safeMeetingId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffName != null">
        staff_name = #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null">
        staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="staffProperty != null">
        staff_property = #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="studyTime != null">
        study_time = #{studyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="signTime != null">
        sign_time = #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="signLocateAddress != null">
        sign_locate_address = #{signLocateAddress,jdbcType=VARCHAR},
      </if>
      <if test="staffDriverImageUrl != null">
        staff_driver_image_url = #{staffDriverImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="signImageUrl != null">
        sign_image_url = #{signImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDriverSafeMeetingRelation">
    update t_driver_safe_meeting_relation
    set safe_meeting_id = #{safeMeetingId,jdbcType=BIGINT},
      staff_id = #{staffId,jdbcType=BIGINT},
      staff_name = #{staffName,jdbcType=VARCHAR},
      staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      staff_property = #{staffProperty,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      study_time = #{studyTime,jdbcType=TIMESTAMP},
      sign_time = #{signTime,jdbcType=TIMESTAMP},
      sign_locate_address = #{signLocateAddress,jdbcType=VARCHAR},
      staff_driver_image_url = #{staffDriverImageUrl,jdbcType=VARCHAR},
      sign_image_url = #{signImageUrl,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>