/**
 * @author: wjf
 * @date: 2019/3/27 9:13
 */
package com.logistics.management.webapi.base.enums;

public enum SettlementPaymentStatusEnum {
    DEFAULT(-1, ""),
    NOT_PAY(0, "未付款"),
    PAYED(1, "已付款"),
    ;

    private Integer key;
    private String value;

    SettlementPaymentStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static SettlementPaymentStatusEnum getEnum(Integer key) {
        for (SettlementPaymentStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
