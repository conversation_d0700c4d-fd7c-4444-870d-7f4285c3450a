package com.logistics.management.webapi.api.feign.attendance.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 变更打卡申请列表响应dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/15
 */
@Data
@ExcelIgnoreUnannotated
public class SearchAttendanceChangeListResponseDto {

	@ApiModelProperty("打卡变更申请ID")
	private String attendanceChangeApplyId = "";

	@ApiModelProperty("审核状态: 0 待审核，1 已审核，2 已驳回，3 已撤销")
	private String auditStatus = "";

	@ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
	private String staffProperty = "";

	@ApiModelProperty("变更类型: 1 上班，2 下班")
	private String changeType = "";

	/*
		以下为导出字段（字段顺序导对应表格列顺序）
	 */

	@ExcelProperty(value = "审核状态")
	@ApiModelProperty("审核状态展示文本")
	private String auditStatusLabel = "";

	@ExcelProperty(value = "司机")
	@ApiModelProperty("考勤用户,姓名_手机号")
	private String attendanceUser = "";

	@ExcelProperty(value = "司机机构")
	@ApiModelProperty("人员机构展示文本")
	private String staffPropertyLabel = "";

	@ExcelProperty(value = "考勤月份")
	@ApiModelProperty("考勤月份")
	private String attendanceMonth = "";

	@ExcelProperty(value = "考勤日期")
	@ApiModelProperty("考勤日期")
	private String attendanceDate = "";

	@ExcelProperty(value = "变更类型")
	@ApiModelProperty("变更类型展示文本")
	private String changeTypeLabel = "";

	@ExcelProperty(value = "修改理由")
	@ApiModelProperty("变更原因")
	private String changeReason = "";

	@ExcelProperty(value = "打卡时间")
	@ApiModelProperty("变更的打卡时间")
	private String changePunchTime = "";

	@ExcelProperty(value = "审核时间")
	@ApiModelProperty("审核时间")
	private String auditTime = "";

	@ExcelProperty(value = "审核人")
	@ApiModelProperty("审核人")
	private String auditorName = "";
}
