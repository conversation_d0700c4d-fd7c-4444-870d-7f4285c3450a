<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TInvoicingInvoiceMapper" >
  <select id="getInvoiceAmountByInvoicingIds" resultType="com.logistics.tms.biz.invoicingmanagement.model.GetInvoiceAmountModel">
    select
    invoicing_id as invoicingId,
    sum(ifnull(total_tax_and_price,0)) as invoiceAmount
    from t_invoicing_invoice
    where valid = 1
    and invoicing_id in
    <foreach collection="invoicingIds" item="invoicingId" separator="," open="(" close=")">
      #{invoicingId}
    </foreach>
    group by invoicing_id
  </select>

  <select id="getByInvoicingIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_invoicing_invoice
    where valid = 1
    and invoicing_id in
    <foreach collection="invoicingIds" item="invoicingId" separator="," open="(" close=")">
      #{invoicingId}
    </foreach>
  </select>

    <select id="getOneByInvoiceCodeAndInvoiceNumAndInvoicingId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_invoicing_invoice
        where valid = 1
        <if test="invoiceCode != null">
            and invoice_code= #{invoiceCode,jdbcType=VARCHAR}
        </if>
        <if test="invoiceNum != null">
            and invoice_num =#{invoiceNum,jdbcType=VARCHAR}
        </if>
        <if test="invoicingId != null">
            and invoicing_id= #{invoicingId,jdbcType=BIGINT}
        </if>
    </select>

</mapper>