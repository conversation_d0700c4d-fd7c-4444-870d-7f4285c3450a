package com.logistics.management.webapi.api.impl.dispatchorder.mapper;

import com.logistics.management.webapi.api.feign.dispatchorder.dto.CarrierOrderGoodsResponseDto;
import com.logistics.management.webapi.api.feign.dispatchorder.dto.DispatchOrderDetailResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.tms.api.feign.dispatchorder.model.CarrierOrderGoodsResponseModel;
import com.logistics.tms.api.feign.dispatchorder.model.DispatchOrderDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * Created by yuhong.lin on 2019/1/21
 */
public class DispatchOrderDetailMapping extends MapperMapping<DispatchOrderDetailResponseModel, DispatchOrderDetailResponseDto> {
    @Override
    public void configure() {
        DispatchOrderDetailResponseModel model = getSource();
        DispatchOrderDetailResponseDto dto = getDestination();
        if (null != model) {
            Optional.ofNullable(model.getExpectArrivalTime()).ifPresent(t->dto.setExpectArrivalTime(DateUtils.dateToString(model.getExpectArrivalTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN)));
            if (model.getExpectArrivalTime() != null) {
                dto.setExpectArrivalTime(DateUtils.dateToString(model.getExpectArrivalTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(ListUtils.isEmpty(model.getCarrierOrderGoodsList())){
                return;
            }
            Integer goodsUnit = model.getCarrierOrderGoodsList().get(0).getGoodsUnit();
            dto.setGoodsUnit(ConverterUtils.toString(goodsUnit));
            String unit = GoodsUnitEnum.getEnum(goodsUnit).getUnit();
            BigDecimal countTotal = CommonConstant.BIG_DECIMAL_ZERO;
            BigDecimal volumeTotal = CommonConstant.BIG_DECIMAL_ZERO;
            for (CarrierOrderGoodsResponseModel responseModel:model.getCarrierOrderGoodsList()) {
                StringBuilder load = new StringBuilder();
                StringBuilder unload = new StringBuilder();
                load.append(StringUtils.isNotBlank(responseModel.getLoadCityName())?responseModel.getLoadCityName():"").
                        append(StringUtils.isNotBlank(responseModel.getLoadAreaName())?responseModel.getLoadAreaName():"").
                        append(StringUtils.isNotBlank(responseModel.getLoadDetailAddress())?responseModel.getLoadDetailAddress():"");
                unload.append(StringUtils.isNotBlank(responseModel.getUnloadCityName())?responseModel.getUnloadCityName():"").
                        append(StringUtils.isNotBlank(responseModel.getUnloadAreaName())?responseModel.getUnloadAreaName():"").
                        append(StringUtils.isNotBlank(responseModel.getUnloadDetailAddress())?responseModel.getUnloadDetailAddress():"");

                for (CarrierOrderGoodsResponseDto responseDto:dto.getCarrierOrderGoodsList()) {
                    if (responseDto.getCarrierOrderGoodsId().equals(ConverterUtils.toString(responseModel.getCarrierOrderGoodsId()))){
                        responseDto.setDemandOrderId(ConverterUtils.toString(responseModel.getDemandOrderId()));
                        responseDto.setLoadDetailAddress(ConverterUtils.toString(load));
                        responseDto.setUnloadDetailAddress(ConverterUtils.toString(unload));
                        responseDto.setGoodsSize(responseModel.getGoodsSize());
                        if (responseModel.getGoodsUnit().equals(GoodsUnitEnum.BY_VOLUME.getKey())){
                            responseDto.setGoodsSize(responseModel.getLength() + "*" + responseModel.getWidth() + "*" + responseModel.getHeight()+"mm");
                        }
                        responseDto.setNotArrangedAmount(responseModel.getExpectAmount().stripTrailingZeros().toPlainString()+unit);
                    }
                }
                countTotal = countTotal.add(responseModel.getExpectAmount());
                if (responseModel.getGoodsUnit().equals(GoodsUnitEnum.BY_VOLUME.getKey())){
                    BigDecimal multiply = responseModel.getExpectAmount().multiply(BigDecimal.valueOf(responseModel.getLength()).multiply(BigDecimal.valueOf(responseModel.getWidth()))).multiply(BigDecimal.valueOf(responseModel.getHeight()));
                    volumeTotal = volumeTotal.add(multiply.divide(BigDecimal.valueOf(1000000000)).setScale(3,BigDecimal.ROUND_HALF_UP));
                }
            }
            dto.setNotArrangedAmountTotal(countTotal.stripTrailingZeros().toPlainString());
            if (goodsUnit.equals(GoodsUnitEnum.BY_VOLUME.getKey())) {
                dto.setNotArrangedVolumeTotal(volumeTotal.stripTrailingZeros().toPlainString());
            }
            BigDecimal adjustFee = model.getAdjustFee();
            if (model.getAdjustFeeType().equals(AdjustFeeTypeEnum.MARK_DOWN.getKey())){
                adjustFee = adjustFee.negate();
            }
            if (model.getDispatchFreightFeeType().equals(PriceTypeEnum.UNIT_PRICE.getKey())){
                dto.setDispatchFreightFeeTotal(ConverterUtils.toString(countTotal.multiply(model.getDispatchFreightFee()).setScale(2, BigDecimal.ROUND_HALF_UP).add(adjustFee).add(model.getMarkupFee())));
            }else{
                dto.setDispatchFreightFeeTotal(ConverterUtils.toString(model.getDispatchFreightFee().add(adjustFee).add(model.getMarkupFee())));
            }
            dto.getOperateLogsList().stream().forEach(logsDto ->
                model.getOperateLogsList().stream().forEach(logsModel -> {
                    if (logsDto.getOperateLogsId().equals(ConverterUtils.toString(logsModel.getOperateLogsId()))){
                        logsDto.setOperateType(OperateLogsOperateTypeEnum.getEnum(logsModel.getOperateType()).getValue());
                    }
                })
            );
        }
    }
}
