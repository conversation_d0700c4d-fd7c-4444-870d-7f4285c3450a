<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TInvoicingManagementMapper" >
  <sql id="Base_Column_List_Decrypt" >
    id, business_name, invoicing_month, business_type, company_carrier_id, company_carrier_name, 
    AES_DECRYPT(UNHEX(contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as contact_phone,
    contact_name, company_carrier_type, remark, created_by, created_time,
    last_modified_by, last_modified_time, valid
  </sql>

  <select id="selectByPrimaryKeyDecrypt" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List_Decrypt" />
    from t_invoicing_management
    where id = #{id,jdbcType=BIGINT}
    and valid = 1
  </select>

  <insert id="insertSelectiveEncrypt" parameterType="com.logistics.tms.entity.TInvoicingManagement" keyProperty="id" useGeneratedKeys="true">
    insert into t_invoicing_management
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="businessName != null" >
        business_name,
      </if>
      <if test="invoicingMonth != null" >
        invoicing_month,
      </if>
      <if test="businessType != null" >
        business_type,
      </if>
      <if test="companyCarrierId != null" >
        company_carrier_id,
      </if>
      <if test="companyCarrierName != null" >
        company_carrier_name,
      </if>
      <if test="contactPhone != null" >
        contact_phone,
      </if>
      <if test="contactName != null" >
        contact_name,
      </if>
      <if test="companyCarrierType != null" >
        company_carrier_type,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessName != null" >
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="invoicingMonth != null" >
        #{invoicingMonth,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null" >
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null" >
        #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null" >
        #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null" >
        HEX(AES_ENCRYPT(#{contactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="contactName != null" >
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="companyCarrierType != null" >
        #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelectiveEncrypt" parameterType="com.logistics.tms.entity.TInvoicingManagement" >
    update t_invoicing_management
    <set >
      <if test="businessName != null" >
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="invoicingMonth != null" >
        invoicing_month = #{invoicingMonth,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null" >
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null" >
        company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null" >
        company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null" >
        contact_phone = HEX(AES_ENCRYPT(#{contactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="contactName != null" >
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="companyCarrierType != null" >
        company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="searchList" resultType="com.logistics.tms.controller.invoicingmanagement.response.SearchInvoicingManagementListResponseModel">
    select
    id as invoicingId,
    business_name as businessName,
    invoicing_month as invoicingMonth,
    company_carrier_type as companyCarrierType,
    company_carrier_name as companyCarrierName,
    contact_name as carrierContactName,
    AES_DECRYPT(UNHEX(contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactMobile,
    remark,
    last_modified_time as lastModifiedTime,
    last_modified_by as lastModifiedBy
    from t_invoicing_management
    where valid = 1
    and business_type = #{businessType,jdbcType=INTEGER}
    <if test="params.businessName != null and params.businessName != ''">
      and instr(business_name, #{params.businessName,jdbcType=VARCHAR})
    </if>
    <if test="params.invoicingMonthStart != null and params.invoicingMonthStart != ''">
      and invoicing_month &gt;= #{params.invoicingMonthStart,jdbcType=VARCHAR}
    </if>
    <if test="params.invoicingMonthEnd != null and params.invoicingMonthEnd != ''">
      and invoicing_month &lt;= #{params.invoicingMonthEnd,jdbcType=VARCHAR}
    </if>
    <if test="params.companyCarrierName != null and params.companyCarrierName != ''">
      and (
      (company_carrier_type = 1 and instr(company_carrier_name, #{params.companyCarrierName,jdbcType=VARCHAR}))
      or (company_carrier_type = 2 and (instr(contact_name, #{params.companyCarrierName,jdbcType=VARCHAR})
      or instr(AES_DECRYPT(UNHEX(contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'),
      #{params.companyCarrierName,jdbcType=VARCHAR})))
      )
    </if>
    order by last_modified_time desc, id desc
  </select>

  <select id="getDetail" resultType="com.logistics.tms.controller.invoicingmanagement.response.InvoicingManagementDetailResponseModel">
    select
    id as invoicingId,
    business_name as businessName,
    invoicing_month as invoicingMonth,
    company_carrier_id as companyCarrierId,
    company_carrier_type as companyCarrierType,
    company_carrier_name as companyCarrierName,
    contact_name as carrierContactName,
    AES_DECRYPT(UNHEX(contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactMobile,
    remark
    from t_invoicing_management
    where valid = 1
    and id = #{id,jdbcType=BIGINT}
    and business_type = #{businessType,jdbcType=INTEGER}
  </select>

  <select id="getByIdAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List_Decrypt"/>
    from t_invoicing_management
    where valid = 1
    and id = #{id,jdbcType=BIGINT}
    and business_type = #{businessType,jdbcType=INTEGER}
  </select>
</mapper>