package com.logistics.tms.controller.companycarrier;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.companycarrier.CompanyCarrierBiz;
import com.logistics.tms.controller.companycarrier.request.*;
import com.logistics.tms.controller.companycarrier.response.CompanyCarrierDetailResponseModel;
import com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.tms.controller.companycarrier.response.SearchCompanyCarrierListResponseModel;
import com.logistics.tms.controller.companycarrier.response.UserCompanyCarrierInfoResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/9/27 17:22
 */
@Api(value = "API - CompanyCarrierServiceApi-车主公司管理")
@RestController
@RequestMapping(value = "/service/companyCarrier")
public class CompanyCarrierController {

    @Autowired
    private CompanyCarrierBiz companyCarrierBiz;

    /**
     * 查询车主公司列表
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查询车主公司列表")
    @PostMapping(value = "/searchList")
    public Result<PageInfo<SearchCompanyCarrierListResponseModel>> searchList(@RequestBody SearchCompanyCarrierListRequestModel requestModel) {
        return Result.success(companyCarrierBiz.searchList(requestModel.enablePaging()));
    }

    /**
     * 公司详情
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "公司详情")
    @PostMapping(value = "/detail")
    public Result<CompanyCarrierDetailResponseModel> getDetail(@RequestBody CompanyCarrierDetailRequestModel requestModel) {
        return Result.success(companyCarrierBiz.getDetail(requestModel));
    }

    /**
     * 修改/保存
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "新增/编辑-个人/企业车主")
    @PostMapping(value = "/saveOrUpdate")
    public Result<Boolean> saveOrUpdate(@RequestBody SaveOrModifyCompanyCarrierRequestModel requestModel) {
        companyCarrierBiz.saveOrUpdate(requestModel);
        return Result.success(true);
    }

    /**
     * 模糊查询
     *
     * @param requestModel
     * @return
     */
    @ApiOperation("模糊查询")
    @PostMapping(value = "/fuzzyQuery")
    public Result<List<FuzzySearchCompanyCarrierResponseModel>> fuzzyQuery(@RequestBody FuzzySearchCompanyCarrierRequestModel requestModel) {
        return Result.success(companyCarrierBiz.fuzzyQuery(requestModel));
    }

    /**
     * 根据登录人查询公司信息，界面上部bar
     *
     * @return
     */
    @ApiOperation(value = "根据登录人查询公司信息，界面上部bar")
    @PostMapping(value = "/getUserAndCompanyInfo")
    public Result<UserCompanyCarrierInfoResponseModel> getUserAndCompanyInfo() {
        return Result.success(companyCarrierBiz.getUserAndCompanyInfo());
    }

    /**
     * 开启/关闭黑名单
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "开启/关闭黑名单")
    @PostMapping(value = "/openOrClose")
    public Result<Boolean> openOrClose(@RequestBody OpenCloseBlacklistRequestModel requestModel) {
        companyCarrierBiz.openOrClose(requestModel);
        return Result.success(true);
    }

    /**
     * 开启/关闭零担模式
     *
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/openOrCloseLessThanTruckload")
    public Result<Boolean> openOrCloseLessThanTruckload(@RequestBody OpenCloseLessThanTruckloadRequestModel requestModel){
        companyCarrierBiz.openOrCloseLessThanTruckload(requestModel);
        return Result.success(true);
    }

}
