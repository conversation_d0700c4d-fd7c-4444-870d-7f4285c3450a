package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleTransportLine extends BaseEntity {
    /**
    * 车辆id
    */
    @ApiModelProperty("车辆id")
    private Long vehicleId;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 省份ID
    */
    @ApiModelProperty("省份ID")
    private Long loadProvinceId;

    /**
    * 省份名字
    */
    @ApiModelProperty("省份名字")
    private String loadProvinceName;

    /**
    * 城市ID
    */
    @ApiModelProperty("城市ID")
    private Long loadCityId;

    /**
    * 城市名字
    */
    @ApiModelProperty("城市名字")
    private String loadCityName;

    /**
    * 县区id
    */
    @ApiModelProperty("县区id")
    private Long loadAreaId;

    /**
    * 县区名字
    */
    @ApiModelProperty("县区名字")
    private String loadAreaName;

    /**
    * 详细地址
    */
    @ApiModelProperty("详细地址")
    private String loadDetailAddress;

    /**
    * 发货仓库
    */
    @ApiModelProperty("发货仓库")
    private String loadWarehouse;

    /**
    * 省份ID
    */
    @ApiModelProperty("省份ID")
    private Long unloadProvinceId;

    /**
    * 省份名字
    */
    @ApiModelProperty("省份名字")
    private String unloadProvinceName;

    /**
    * 城市ID
    */
    @ApiModelProperty("城市ID")
    private Long unloadCityId;

    /**
    * 城市名字
    */
    @ApiModelProperty("城市名字")
    private String unloadCityName;

    /**
    * 县区id
    */
    @ApiModelProperty("县区id")
    private Long unloadAreaId;

    /**
    * 县区名字
    */
    @ApiModelProperty("县区名字")
    private String unloadAreaName;

    /**
    * 详细地址
    */
    @ApiModelProperty("详细地址")
    private String unloadDetailAddress;

    /**
    * 收货仓库
    */
    @ApiModelProperty("收货仓库")
    private String unloadWarehouse;
}