package com.logistics.tms.api.feign.carriercontact;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.carriercontact.dto.*;
import com.logistics.tms.api.feign.carriercontact.hystrix.CarrierContactAccountApiHystrix;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Api(value = "API-CarrierContactApi-车主账号管理")
@FeignClient(name = "logistics-tms-services",fallback = CarrierContactAccountApiHystrix.class)
public interface CarrierContactAccountApi {

    @ApiOperation(value = "车主账号列表v1.1.9")
    @PostMapping(value = "/service/carrierContact/searchList")
    Result<PageInfo<SearchCarrierContactResponseModel>> searchList(@RequestBody SearchCarrierContactRequestModel requestModel );

    @ApiOperation(value = "导出车主账号v1.1.9")
    @PostMapping(value = "/service/carrierContact/exportCarrierContact")
    Result<List<SearchCarrierContactResponseModel>> exportCarrierContact(@RequestBody SearchCarrierContactRequestModel requestModel );

    @ApiOperation(value = "查看详情v1.1.9")
    @PostMapping(value = "/service/carrierContact/getDetail")
    Result<CarrierContactDetailResponseModel> getDetail(@RequestBody CarrierContactDetailRequestModel requestModel);

    @ApiOperation(value = "新增编辑车主账号v1.1.9")
    @PostMapping(value = "/service/carrierContact/saveAccount")
    Result<Boolean> saveAccount(@RequestBody SaveCarrierContactRequestModel requestModel);

    @ApiOperation(value = "禁用启用车主账号v1.1.9")
    @PostMapping(value = "/service/carrierContact/enableDisableClosed")
    Result<Boolean> enableDisableClosed(@RequestBody CarrierContactEnableRequestModel requestModel);

    @ApiOperation(value = "删除车主账号v1.1.9")
    @PostMapping(value = "/service/carrierAccount/delCarrierAccount")
    Result<Boolean> delCarrierAccount(@RequestBody DelCarrierContactRequestModel requestModel);

}
