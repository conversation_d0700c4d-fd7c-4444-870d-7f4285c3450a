package com.logistics.management.webapi.api.feign.bank.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/7/10 14:13
 */
@Data
public class ImportBankListRequestDto {
    @ApiModelProperty("银行名称")
    private String bankName;
    @ApiModelProperty("支行名称")
    private String branchName;
    @ApiModelProperty("备注")
    private String remark;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ImportBankListRequestDto)) return false;

        ImportBankListRequestDto that = (ImportBankListRequestDto) o;

        if (getBankName() != null ? !getBankName().equals(that.getBankName()) : that.getBankName() != null)
            return false;
        if (getBranchName() != null ? !getBranchName().equals(that.getBranchName()) : that.getBranchName() != null)
            return false;
        return getRemark() != null ? getRemark().equals(that.getRemark()) : that.getRemark() == null;
    }

    @Override
    public int hashCode() {
        int result = 32;
        result = 31 * result + (getBankName() != null ? getBankName().hashCode() : 0);
        result = 31 * result + (getBranchName() != null ? getBranchName().hashCode() : 0);
        result = 31 * result + (getRemark() != null ? getRemark().hashCode() : 0);
        return result;
    }
}
