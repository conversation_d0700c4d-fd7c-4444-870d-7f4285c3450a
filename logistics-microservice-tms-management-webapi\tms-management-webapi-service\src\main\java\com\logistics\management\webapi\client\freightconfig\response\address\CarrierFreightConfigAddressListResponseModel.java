package com.logistics.management.webapi.client.freightconfig.response.address;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.freightconfig.response.scheme.CarrierFreightConfigSchemeResponseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigAddressListResponseModel extends CarrierFreightConfigSchemeResponseModel {

    @ApiModelProperty(value = "路线配置列表")
    private PageInfo<CarrierFreightConfigAddressItemResponseModel> addressConfigPage;
}
