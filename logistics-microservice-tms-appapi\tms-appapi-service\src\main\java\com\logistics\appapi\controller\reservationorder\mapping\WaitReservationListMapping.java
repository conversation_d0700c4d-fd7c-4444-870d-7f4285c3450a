package com.logistics.appapi.controller.reservationorder.mapping;

import com.logistics.appapi.base.enums.CarrierOrderStatusEnum;
import com.logistics.appapi.base.enums.EntrustTypeEnum;
import com.logistics.appapi.base.enums.ReservationTypeEnum;
import com.logistics.appapi.client.reservationorder.response.WaitReservationCarrierOrderListResponseModel;
import com.logistics.appapi.client.reservationorder.response.WaitReservationResponseModel;
import com.logistics.appapi.controller.reservationorder.response.ReservationCarrierOrderListResponseDto;
import com.logistics.appapi.controller.reservationorder.response.WaitReservationListResponseDto;
import com.logistics.appapi.controller.reservationorder.response.WaitReservationResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2024/8/22 13:34
 */
public class WaitReservationListMapping extends MapperMapping<WaitReservationResponseModel, WaitReservationResponseDto> {
    @Override
    public void configure() {
        WaitReservationResponseModel source = getSource();
        WaitReservationResponseDto destination = getDestination();

        //预约类型
        destination.setReservationTypeLabel(ReservationTypeEnum.getEnumByKey(source.getReservationType()).getValue());

        //数据转换
        ReservationCarrierOrderListResponseDto dto;
        List<ReservationCarrierOrderListResponseDto> carrierOrderDtoList = new ArrayList<>();
        for (WaitReservationCarrierOrderListResponseModel model : source.getOrderList()) {
            dto = MapperUtils.mapper(model, ReservationCarrierOrderListResponseDto.class);

            //地址
            StringBuilder addressBuilder = new StringBuilder();
            if (StringUtils.isNotBlank(model.getWarehouse())){
                addressBuilder.append("【");
                addressBuilder.append(model.getWarehouse());
                addressBuilder.append("】");
            }
            addressBuilder.append(model.getProvinceName());
            addressBuilder.append(model.getCityName());
            addressBuilder.append(model.getAreaName());
            addressBuilder.append(model.getDetailAddress());
            dto.setAddress(addressBuilder.toString());

            //需求类型
            dto.setEntrustTypeLabel(EntrustTypeEnum.getEnum(model.getEntrustType()).getValue());

            //运单状态
            dto.setStatusLabel(CarrierOrderStatusEnum.getEnum(model.getStatus()).getValue());

            //运单预计数量
            dto.setExpectAmount(model.getExpectAmount().stripTrailingZeros().toPlainString());

            if (model.getExpectedTime()!=null){
                dto.setExpectedTime(DateUtils.dateToString(model.getExpectedTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }

            carrierOrderDtoList.add(dto);
        }

        //按地址聚合
        Map<String, List<ReservationCarrierOrderListResponseDto>> map = carrierOrderDtoList.stream().collect(Collectors.groupingBy(ReservationCarrierOrderListResponseDto::getAddress));

        //组装数据
        WaitReservationListResponseDto waitReservationListResponseDto;
        List<WaitReservationListResponseDto> orderList = new ArrayList<>();
        for (Map.Entry<String, List<ReservationCarrierOrderListResponseDto>> entry : map.entrySet()) {
            waitReservationListResponseDto = new WaitReservationListResponseDto();
            waitReservationListResponseDto.setAddress(entry.getKey());
            waitReservationListResponseDto.setOrderList(entry.getValue());
            orderList.add(waitReservationListResponseDto);
        }
        destination.setOrderList(orderList);
    }
}
