package com.logistics.management.webapi.base.enums;


public enum CarrierOrderStatusEnum {
    DEFAULT(-99,""),
    WAIT_REACH_LOAD_ADDRESS(10000,"待到达提货地"),
    WAIT_LOAD(20000,"待提货"),
    WAIT_REACH_UNLOAD_ADDRESS(30000,"待到达卸货地"),
    WAIT_UNLOAD(40000,"待卸货"),
    WAIT_SIGN_UP(50000,"待签收"),
    ALREADY_SIGN_UP(60000,"已签收"),
    CANCEL_CARRIERORDER_STATUS(0,"已取消"),
    WAIT_AUDIT(1,"待审核"),
    EMPTY(2,"已放空"),
    ;

    private Integer key;
    private String value;

    CarrierOrderStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static CarrierOrderStatusEnum getEnum(Integer key) {
        for (CarrierOrderStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
