package com.logistics.tms.controller.driversafemeeting.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/11/4 9:26
 */
@Data
public class DriverSafeMeetingContentDetailResponseModel {
    @ApiModelProperty("学习例会id")
    private Long safeMeetingId;
    @ApiModelProperty(value = "学习月份")
    private String period;
    @ApiModelProperty(value = "学习标题")
    private String title;
    @ApiModelProperty(value = "学习简介")
    private String introduction;
    @ApiModelProperty(value = "例会发布时间")
    private Date createdTime;
    @ApiModelProperty(value = "学习内容")
    private String content;
    @ApiModelProperty(value = "驾驶员")
    private List<Long> driverIdList;
}
