<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TCompanyCarrierMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCompanyCarrier">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="company_water_mark" jdbcType="VARCHAR" property="companyWaterMark" />
    <result column="road_transport_certificate_number" jdbcType="VARCHAR" property="roadTransportCertificateNumber" />
    <result column="road_transport_certificate_image" jdbcType="VARCHAR" property="roadTransportCertificateImage" />
    <result column="road_transport_certificate_validity_time" jdbcType="TIMESTAMP" property="roadTransportCertificateValidityTime" />
    <result column="road_transport_certificate_is_forever" jdbcType="INTEGER" property="roadTransportCertificateIsForever" />
    <result column="road_transport_certificate_is_amend" jdbcType="INTEGER" property="roadTransportCertificateIsAmend" />
    <result column="enabled" jdbcType="INTEGER" property="enabled" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="commit_other_fee" jdbcType="INTEGER" property="commitOtherFee" />
    <result column="other_fee_tax_point" jdbcType="DECIMAL" property="otherFeeTaxPoint" />
    <result column="freight_tax_point" jdbcType="DECIMAL" property="freightTaxPoint" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="authorization_status" jdbcType="INTEGER" property="authorizationStatus" />
    <result column="real_name_authentication_status" jdbcType="INTEGER" property="realNameAuthenticationStatus" />
    <result column="if_add_blacklist" jdbcType="INTEGER" property="ifAddBlacklist" />
    <result column="if_less_than_truckload" jdbcType="INTEGER" property="ifLessThanTruckload" />
    <result column="shipping_freight_id" jdbcType="INTEGER" property="shippingFreightId" />
    <result column="shipping_freight_add_user" jdbcType="VARCHAR" property="shippingFreightAddUser" />
    <result column="shipping_freight_add_time" jdbcType="TIMESTAMP" property="shippingFreightAddTime" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, company_water_mark, road_transport_certificate_number, road_transport_certificate_image, 
    road_transport_certificate_validity_time, road_transport_certificate_is_forever, 
    road_transport_certificate_is_amend, enabled, type, level, source, commit_other_fee, 
    other_fee_tax_point, freight_tax_point, remark, authorization_status, real_name_authentication_status, 
    if_add_blacklist, if_less_than_truckload, created_by, created_time, last_modified_by, 
    last_modified_time, valid,shipping_freight_id,shipping_freight_add_user,shipping_freight_add_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_company_carrier
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_company_carrier
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCompanyCarrier">
    insert into t_company_carrier (id, company_id, company_water_mark, 
      road_transport_certificate_number, road_transport_certificate_image, 
      road_transport_certificate_validity_time, road_transport_certificate_is_forever, 
      road_transport_certificate_is_amend, enabled, 
      type, level, source, 
      commit_other_fee, other_fee_tax_point, freight_tax_point, 
      remark, authorization_status, real_name_authentication_status, 
      if_add_blacklist, if_less_than_truckload, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT}, #{companyWaterMark,jdbcType=VARCHAR}, 
      #{roadTransportCertificateNumber,jdbcType=VARCHAR}, #{roadTransportCertificateImage,jdbcType=VARCHAR}, 
      #{roadTransportCertificateValidityTime,jdbcType=TIMESTAMP}, #{roadTransportCertificateIsForever,jdbcType=INTEGER}, 
      #{roadTransportCertificateIsAmend,jdbcType=INTEGER}, #{enabled,jdbcType=INTEGER}, 
      #{type,jdbcType=INTEGER}, #{level,jdbcType=INTEGER}, #{source,jdbcType=INTEGER}, 
      #{commitOtherFee,jdbcType=INTEGER}, #{otherFeeTaxPoint,jdbcType=DECIMAL}, #{freightTaxPoint,jdbcType=DECIMAL}, 
      #{remark,jdbcType=VARCHAR}, #{authorizationStatus,jdbcType=INTEGER}, #{realNameAuthenticationStatus,jdbcType=INTEGER}, 
      #{ifAddBlacklist,jdbcType=INTEGER}, #{ifLessThanTruckload,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCompanyCarrier" keyProperty="id" useGeneratedKeys="true">
    insert into t_company_carrier
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyWaterMark != null">
        company_water_mark,
      </if>
      <if test="roadTransportCertificateNumber != null">
        road_transport_certificate_number,
      </if>
      <if test="roadTransportCertificateImage != null">
        road_transport_certificate_image,
      </if>
      <if test="roadTransportCertificateValidityTime != null">
        road_transport_certificate_validity_time,
      </if>
      <if test="roadTransportCertificateIsForever != null">
        road_transport_certificate_is_forever,
      </if>
      <if test="roadTransportCertificateIsAmend != null">
        road_transport_certificate_is_amend,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="commitOtherFee != null">
        commit_other_fee,
      </if>
      <if test="otherFeeTaxPoint != null">
        other_fee_tax_point,
      </if>
      <if test="freightTaxPoint != null">
        freight_tax_point,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="authorizationStatus != null">
        authorization_status,
      </if>
      <if test="realNameAuthenticationStatus != null">
        real_name_authentication_status,
      </if>
      <if test="ifAddBlacklist != null">
        if_add_blacklist,
      </if>
      <if test="ifLessThanTruckload != null">
        if_less_than_truckload,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyWaterMark != null">
        #{companyWaterMark,jdbcType=VARCHAR},
      </if>
      <if test="roadTransportCertificateNumber != null">
        #{roadTransportCertificateNumber,jdbcType=VARCHAR},
      </if>
      <if test="roadTransportCertificateImage != null">
        #{roadTransportCertificateImage,jdbcType=VARCHAR},
      </if>
      <if test="roadTransportCertificateValidityTime != null">
        #{roadTransportCertificateValidityTime,jdbcType=TIMESTAMP},
      </if>
      <if test="roadTransportCertificateIsForever != null">
        #{roadTransportCertificateIsForever,jdbcType=INTEGER},
      </if>
      <if test="roadTransportCertificateIsAmend != null">
        #{roadTransportCertificateIsAmend,jdbcType=INTEGER},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="level != null">
        #{level,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="commitOtherFee != null">
        #{commitOtherFee,jdbcType=INTEGER},
      </if>
      <if test="otherFeeTaxPoint != null">
        #{otherFeeTaxPoint,jdbcType=DECIMAL},
      </if>
      <if test="freightTaxPoint != null">
        #{freightTaxPoint,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="authorizationStatus != null">
        #{authorizationStatus,jdbcType=INTEGER},
      </if>
      <if test="realNameAuthenticationStatus != null">
        #{realNameAuthenticationStatus,jdbcType=INTEGER},
      </if>
      <if test="ifAddBlacklist != null">
        #{ifAddBlacklist,jdbcType=INTEGER},
      </if>
      <if test="ifLessThanTruckload != null">
        #{ifLessThanTruckload,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCompanyCarrier">
    update t_company_carrier
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyWaterMark != null">
        company_water_mark = #{companyWaterMark,jdbcType=VARCHAR},
      </if>
      <if test="roadTransportCertificateNumber != null">
        road_transport_certificate_number = #{roadTransportCertificateNumber,jdbcType=VARCHAR},
      </if>
      <if test="roadTransportCertificateImage != null">
        road_transport_certificate_image = #{roadTransportCertificateImage,jdbcType=VARCHAR},
      </if>
      <if test="roadTransportCertificateValidityTime != null">
        road_transport_certificate_validity_time = #{roadTransportCertificateValidityTime,jdbcType=TIMESTAMP},
      </if>
      <if test="roadTransportCertificateIsForever != null">
        road_transport_certificate_is_forever = #{roadTransportCertificateIsForever,jdbcType=INTEGER},
      </if>
      <if test="roadTransportCertificateIsAmend != null">
        road_transport_certificate_is_amend = #{roadTransportCertificateIsAmend,jdbcType=INTEGER},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="commitOtherFee != null">
        commit_other_fee = #{commitOtherFee,jdbcType=INTEGER},
      </if>
      <if test="otherFeeTaxPoint != null">
        other_fee_tax_point = #{otherFeeTaxPoint,jdbcType=DECIMAL},
      </if>
      <if test="freightTaxPoint != null">
        freight_tax_point = #{freightTaxPoint,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="authorizationStatus != null">
        authorization_status = #{authorizationStatus,jdbcType=INTEGER},
      </if>
      <if test="realNameAuthenticationStatus != null">
        real_name_authentication_status = #{realNameAuthenticationStatus,jdbcType=INTEGER},
      </if>
      <if test="ifAddBlacklist != null">
        if_add_blacklist = #{ifAddBlacklist,jdbcType=INTEGER},
      </if>
      <if test="ifLessThanTruckload != null">
        if_less_than_truckload = #{ifLessThanTruckload,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCompanyCarrier">
    update t_company_carrier
    set company_id = #{companyId,jdbcType=BIGINT},
      company_water_mark = #{companyWaterMark,jdbcType=VARCHAR},
      road_transport_certificate_number = #{roadTransportCertificateNumber,jdbcType=VARCHAR},
      road_transport_certificate_image = #{roadTransportCertificateImage,jdbcType=VARCHAR},
      road_transport_certificate_validity_time = #{roadTransportCertificateValidityTime,jdbcType=TIMESTAMP},
      road_transport_certificate_is_forever = #{roadTransportCertificateIsForever,jdbcType=INTEGER},
      road_transport_certificate_is_amend = #{roadTransportCertificateIsAmend,jdbcType=INTEGER},
      enabled = #{enabled,jdbcType=INTEGER},
      type = #{type,jdbcType=INTEGER},
      level = #{level,jdbcType=INTEGER},
      source = #{source,jdbcType=INTEGER},
      commit_other_fee = #{commitOtherFee,jdbcType=INTEGER},
      other_fee_tax_point = #{otherFeeTaxPoint,jdbcType=DECIMAL},
      freight_tax_point = #{freightTaxPoint,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      authorization_status = #{authorizationStatus,jdbcType=INTEGER},
      real_name_authentication_status = #{realNameAuthenticationStatus,jdbcType=INTEGER},
      if_add_blacklist = #{ifAddBlacklist,jdbcType=INTEGER},
      if_less_than_truckload = #{ifLessThanTruckload,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>