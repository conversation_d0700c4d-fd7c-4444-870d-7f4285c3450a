package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2023/03/16
*/
@Data
public class TReachAttachment extends BaseEntity {
    /**
    * 触达id
    */
    @ApiModelProperty("触达id")
    private Long reachManagementId;

    /**
    * 附件类型（1、托盘 2、终端门头）
    */
    @ApiModelProperty("附件类型（1、托盘 2、终端门头）")
    private Integer attachmentType;

    /**
    * 附件路径
    */
    @ApiModelProperty("附件路径")
    private String attachmentPath;

    /**
    * 有效（0无效 1有效）
    */
    @ApiModelProperty("有效（0无效 1有效）")
    private Integer valid;
}