package com.logistics.tms.controller.carrierorder.request;


import com.logistics.tms.controller.carrierorder.response.SyncCarrierOrderLoadAmountModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021 1/7
 * @desd:托盘运单货物出库将数量同步物流系统实体
 */
@Data
public class SyncCarrierOrderLoadAmountRequestModel {

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("实提数量")
    private BigDecimal loadAmount;

    @ApiModelProperty("出库状态：0 待出库，1 部分出库，2 已出库")
    private Integer outStatus;

    @ApiModelProperty("请求时间戳")
    private Date currentDateTime;

    @ApiModelProperty("操作人姓名")
    private String operateUserName;

    @ApiModelProperty("货物对应的数量")
    private List<SyncCarrierOrderLoadAmountModel> typeAndCountModel;

}
