package com.logistics.tms.api.impl.contractorder;


import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.contractorder.ContractOrderServiceApi;
import com.logistics.tms.api.feign.contractorder.model.*;
import com.logistics.tms.biz.contractorder.ContractOrderBiz;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
public class ContractOrderServiceApiImpl implements ContractOrderServiceApi {

    @Autowired
    private ContractOrderBiz contractOrderBiz;

    /**
     * 获取合同列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<ContractOrderSearchResponseModel>> searchContractOrderList(@RequestBody ContractOrderSearchRequestModel requestModel) {
        List<ContractOrderSearchResponseModel> list = contractOrderBiz.searchContractOrderList(requestModel);
        if (ListUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        PageInfo<ContractOrderSearchResponseModel> pageInfo = new PageInfo<>(list);
        return Result.success(pageInfo);
    }

    /**
     * 获取合同详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<ContractOrderDetailResponseModel> getDetail(@RequestBody ContractOrderDetailRequestModel requestModel) {
        return Result.success(contractOrderBiz.geDetail(requestModel));
    }

    /**
     * 新增修改合同信息
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> saveContract(@RequestBody AddOrModifyContractOrderRequestModel requestModel) {
        contractOrderBiz.saveContract(requestModel);
        return Result.success(null);
    }

    /**
     * 作废或终止合同
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> terminateOrCancelContract(@RequestBody TerminateOrCancelContractRequestModel requestModel) {
        contractOrderBiz.terminateOrCancelContract(requestModel);
        return Result.success(null);
    }

    /**
     * 导出合同
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<ContractOrderSearchResponseModel>> exportContractOrder(@RequestBody ContractOrderSearchRequestModel requestModel) {
        requestModel.setPageNum(0);
        requestModel.setPageSize(0);
        return Result.success(contractOrderBiz.searchContractOrderList(requestModel));
    }
}
