package com.logistics.management.webapi.client.shippingorder.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.shippingorder.ShippingOrderClient;
import com.logistics.management.webapi.client.shippingorder.request.*;
import com.logistics.management.webapi.client.shippingorder.response.GetShippingOrderRoutePlanResponseModel;
import com.logistics.management.webapi.client.shippingorder.response.GetShippingOrderDetailResponseModel;
import com.logistics.management.webapi.client.shippingorder.response.SearchShippingOrderListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/6 17:40
 */
@Component
public class ShippingOrderClientHystrix implements ShippingOrderClient {

    @Override
    public Result<PageInfo<SearchShippingOrderListResponseModel>> searchList(SearchShippingOrderListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchShippingOrderListResponseModel>> export(SearchShippingOrderListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> audit(ShippingOrderAuditRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> reQuote(ShippingOrderReQuoteRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetShippingOrderRoutePlanResponseModel>> getRoutePlan(GetShippingOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetShippingOrderDetailResponseModel> getDetail(GetShippingOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }


}
