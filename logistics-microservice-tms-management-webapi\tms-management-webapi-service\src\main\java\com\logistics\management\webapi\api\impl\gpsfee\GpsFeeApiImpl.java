package com.logistics.management.webapi.api.impl.gpsfee;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.gpsfee.GpsFeeApi;
import com.logistics.management.webapi.api.feign.gpsfee.dto.*;
import com.logistics.management.webapi.api.impl.gpsfee.mapping.GetGpsFeeDeductingHistoryMapping;
import com.logistics.management.webapi.api.impl.gpsfee.mapping.GetGpsFeeRecordsMapping;
import com.logistics.management.webapi.api.impl.gpsfee.mapping.GpsFeeDetailMapping;
import com.logistics.management.webapi.api.impl.gpsfee.mapping.SearchGpsFeeListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportExcelDeductingHistory;
import com.logistics.management.webapi.base.constant.ExportExcelGpsFee;
import com.logistics.management.webapi.base.constant.ExportExcelGpsFeeRecords;
import com.logistics.tms.api.feign.gpsfee.GpsFeeServiceApi;
import com.logistics.tms.api.feign.gpsfee.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ExcelUtils;
import com.yelo.tools.utils.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/10/8 14:32
 */
@RestController
public class GpsFeeApiImpl implements GpsFeeApi {

    @Autowired
    private GpsFeeServiceApi gpsFeeServiceApi;

    /**
     * 查询gps费用列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchGpsFeeListResponseDto>> searchGpsFeeList(@RequestBody SearchGpsFeeListRequestDto requestDto) {
        Result<PageInfo<SearchGpsFeeListResponseModel>> result = gpsFeeServiceApi.searchGpsFeeList(MapperUtils.mapper(requestDto, SearchGpsFeeListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SearchGpsFeeListResponseDto> dtoList = MapperUtils.mapper(result.getData().getList(),SearchGpsFeeListResponseDto.class,new SearchGpsFeeListMapping());
        pageInfo.setList(dtoList);
        return Result.success(pageInfo);
    }

    /**
     * 统计gps费用列表各状态数量
     * @param requestDto
     * @return
     */
    @Override
    public Result<SearchGpsFeeListCountResponseDto> searchGpsFeeListCount(@RequestBody SearchGpsFeeListRequestDto requestDto) {
        Result<SearchGpsFeeListCountResponseModel> result = gpsFeeServiceApi.searchGpsFeeListCount(MapperUtils.mapper(requestDto, SearchGpsFeeListRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),SearchGpsFeeListCountResponseDto.class));
    }

    /**
     * 查看详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<GpsFeeDetailResponseDto> getGpsFeeDetail(@RequestBody @Valid GpsFeeIdRequestDto requestDto) {
        Result<GpsFeeDetailResponseModel> result = gpsFeeServiceApi.getGpsFeeDetail(MapperUtils.mapper(requestDto,GpsFeeIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),GpsFeeDetailResponseDto.class,new GpsFeeDetailMapping()));
    }

    /**
     * 新增/修改gps费用
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result addOrModifyGpsFee(@RequestBody @Valid AddOrModifyGpsFeeRequestDto requestDto) {
        Result result = gpsFeeServiceApi.addOrModifyGpsFee(MapperUtils.mapperNoDefault(requestDto, AddOrModifyGpsFeeRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 导出gps费用
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportGpsFee(SearchGpsFeeListRequestDto requestDto, HttpServletResponse response) {
        Result<List<SearchGpsFeeListResponseModel>> result =gpsFeeServiceApi.exportGpsFee(MapperUtils.mapper(requestDto, SearchGpsFeeListRequestModel.class));
        result.throwException();
        List<SearchGpsFeeListResponseDto> dtoList = MapperUtils.mapper(result.getData(),SearchGpsFeeListResponseDto.class,new SearchGpsFeeListMapping());
        String fileName = "GPS费用" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportExcelGpsFee.getExportGpsFee();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return dtoList;
            }
        });
    }

    /**
     * 终止gps费用（修改终止时间）
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result terminationGpsFee(@RequestBody @Valid TerminationGpsFeeRequestDto requestDto) {
        Result result = gpsFeeServiceApi.terminationGpsFee(MapperUtils.mapperNoDefault(requestDto,TerminationGpsFeeRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 查询gps费用操作记录
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<GpsFeeRecordsListResponseDto>> getGpsFeeRecords(@RequestBody @Valid GpsFeeIdRequestDto requestDto) {
        Result<List<GpsFeeRecordsListResponseModel>> result = gpsFeeServiceApi.getGpsFeeRecords(MapperUtils.mapper(requestDto,GpsFeeIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),GpsFeeRecordsListResponseDto.class,new GetGpsFeeRecordsMapping()));
    }

    /**
     * 导出gps费用操作记录
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportGpsFeeRecords(GpsFeeIdRequestDto requestDto, HttpServletResponse response) {
        Result<List<GpsFeeRecordsListResponseModel>> result = gpsFeeServiceApi.getGpsFeeRecords(MapperUtils.mapper(requestDto,GpsFeeIdRequestModel.class));
        result.throwException();
        List<GpsFeeRecordsListResponseDto> list = MapperUtils.mapper(result.getData(),GpsFeeRecordsListResponseDto.class,new GetGpsFeeRecordsMapping());
        String vehicleNo = "";
        if (ListUtils.isNotEmpty(list)){
            vehicleNo = list.get(0).getVehicleNo();
        }
        String fileName = vehicleNo + "GPS费用操作记录" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportExcelGpsFeeRecords.getExportGpsFeeRecords();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return list;
            }
        });
    }

    /**
     * 查询gps费用扣减历史
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<GetDeductingHistoryResponseDto>> getGpsFeeDeductingHistory(@RequestBody @Valid GpsFeeIdRequestDto requestDto) {
        Result<GetDeductingHistoryByGpsFeeIdResponseModel> result = gpsFeeServiceApi.getGpsFeeDeductingHistory(MapperUtils.mapper(requestDto,GpsFeeIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData().getHistoryList(),GetDeductingHistoryResponseDto.class,new GetGpsFeeDeductingHistoryMapping()));
    }

    /**
     * 导出gps费用扣减历史
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportGpsFeeDeductingHistory(GpsFeeIdRequestDto requestDto, HttpServletResponse response) {
        Result<GetDeductingHistoryByGpsFeeIdResponseModel> result = gpsFeeServiceApi.getGpsFeeDeductingHistory(MapperUtils.mapper(requestDto,GpsFeeIdRequestModel.class));
        result.throwException();
        List<GetDeductingHistoryResponseDto> list = MapperUtils.mapper(result.getData().getHistoryList(),GetDeductingHistoryResponseDto.class,new GetGpsFeeDeductingHistoryMapping());
        String fileName = result.getData().getVehicleNo() + "GPS费用扣减记录" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportExcelDeductingHistory.getExportDeductingHistory();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return list;
            }
        });
    }
}
