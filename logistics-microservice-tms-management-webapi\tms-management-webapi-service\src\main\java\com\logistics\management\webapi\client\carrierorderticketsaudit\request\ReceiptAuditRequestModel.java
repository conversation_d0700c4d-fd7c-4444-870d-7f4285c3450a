package com.logistics.management.webapi.client.carrierorderticketsaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReceiptAuditRequestModel {

    @ApiModelProperty(value = "回单审核Id")
    private Long receiptAuditId;

    @ApiModelProperty(value = "审核结果; 1 审核通过，2 驳回")
    private Integer auditStatus;

    @ApiModelProperty(value = "备注")
    private String remark;
}
