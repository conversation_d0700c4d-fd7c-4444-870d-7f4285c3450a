package com.logistics.management.webapi.client.carrierorderotherfee.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class AddCarrierOrderOtherFeeItemRequestModel {

    @ApiModelProperty(value = "费用类型：1 短驳费，2 保管费，3 装卸费，4 压车费，5 质量处罚费，6 其他杂费" ,required = true)
    private Integer feeType;

    @ApiModelProperty("调整费用符号：1 ‘+’， 2 ‘-’")
    private String feeAmountSymbol;

    @ApiModelProperty(value = "金额 :0<费用<=10000元" ,required = true)
    private BigDecimal feeAmount;

    @ApiModelProperty(value = "单据相对路径" ,required = true)
    private List<String> billsPicture;
}
