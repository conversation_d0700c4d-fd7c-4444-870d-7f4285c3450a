package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * sku货物信息
 *
 * <AUTHOR>
 * @date 2022/8/20 13:21
 */
@Data
public class SkuInfoModel {

    @ApiModelProperty("sku唯一标识 查询返回")
    private String skuCode;

    @ApiModelProperty("再生料-定价类型: 1.重量  2.数量")
    private Integer priceType = 1;

    @ApiModelProperty("预计数量/重量")
    private BigDecimal skuAmount;

    @ApiModelProperty("再生料-单价")
    private BigDecimal skuPrice;
}
