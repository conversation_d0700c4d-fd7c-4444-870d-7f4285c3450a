package com.logistics.appapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2018/10/16 13:59
 */
@Data
public class CarrierOrderLogisticsEventResponseDto {

    @ApiModelProperty("物流事件id")
    private String eventId = "";
    @ApiModelProperty("物流事件时间（年月）")
    private String eventDate = "";
    @ApiModelProperty("物流事件时间（时分）")
    private String eventTime = "";
    @ApiModelProperty("物流事件描述")
    private String eventDesc = "";
    @ApiModelProperty("操作人")
    private String operatorName = "";
    @ApiModelProperty("单据类型：1 提货单 2 出库单 3 签收单 4 其他 5 到达装货地 6 到达卸货地")
    private String ticketType = "";
    @ApiModelProperty("单据数量")
    private String ticketCount = "";
}
