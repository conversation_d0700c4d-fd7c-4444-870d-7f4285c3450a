package com.logistics.tms.controller.attendance.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CancelAttendanceChangeApplyRequestModel {

    //请假申请ID
    @ApiModelProperty(value = "考勤变更申请ID", required = true)
    private Long attendanceChangeApplyId;

    //撤销说明
    @ApiModelProperty(value = "撤销说明,1-100个字符", required = true)
    private String remark;
}
