<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderLoadCodeFileMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCarrierOrderLoadCodeFile" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="carrier_order_load_code_id" property="carrierOrderLoadCodeId" jdbcType="BIGINT" />
    <result column="file_path" property="filePath" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, carrier_order_load_code_id, file_path, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_carrier_order_load_code_file
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_carrier_order_load_code_file
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCarrierOrderLoadCodeFile" >
    insert into t_carrier_order_load_code_file (id, carrier_order_load_code_id, file_path, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{carrierOrderLoadCodeId,jdbcType=BIGINT}, #{filePath,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCarrierOrderLoadCodeFile" >
    insert into t_carrier_order_load_code_file
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="carrierOrderLoadCodeId != null" >
        carrier_order_load_code_id,
      </if>
      <if test="filePath != null" >
        file_path,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderLoadCodeId != null" >
        #{carrierOrderLoadCodeId,jdbcType=BIGINT},
      </if>
      <if test="filePath != null" >
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCarrierOrderLoadCodeFile" >
    update t_carrier_order_load_code_file
    <set >
      <if test="carrierOrderLoadCodeId != null" >
        carrier_order_load_code_id = #{carrierOrderLoadCodeId,jdbcType=BIGINT},
      </if>
      <if test="filePath != null" >
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCarrierOrderLoadCodeFile" >
    update t_carrier_order_load_code_file
    set carrier_order_load_code_id = #{carrierOrderLoadCodeId,jdbcType=BIGINT},
      file_path = #{filePath,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
<!--auto generated by MybatisCodeHelper on 2025-01-03-->
  <insert id="insertList">
        INSERT INTO t_carrier_order_load_code_file(
            carrier_order_load_code_id,
            file_path,
            created_by,
            created_time,
            last_modified_by,
            last_modified_time,
            valid
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
        (
            #{element.carrierOrderLoadCodeId,jdbcType=BIGINT},
            #{element.filePath,jdbcType=VARCHAR},
            #{element.createdBy,jdbcType=VARCHAR},
            #{element.createdTime,jdbcType=TIMESTAMP},
            #{element.lastModifiedBy,jdbcType=VARCHAR},
            #{element.lastModifiedTime,jdbcType=TIMESTAMP},
            #{element.valid,jdbcType=INTEGER}
        )
        </foreach>
</insert>


<select id="listByCarrierOrderLoadCodeByCondition" resultMap="BaseResultMap">

    select
        <include refid="Base_Column_List"/>
    from t_carrier_order_load_code_file
    where valid = 1
    <if test="param.carrierOrderLoadCodeIds != null and param.carrierOrderLoadCodeIds.size() != 0">
        and carrier_order_load_code_id in
        <foreach collection="param.carrierOrderLoadCodeIds" open="(" close=")" item="item" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
    </if>

</select>

</mapper>