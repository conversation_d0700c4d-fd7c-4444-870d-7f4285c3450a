package com.logistics.tms.client.feign.basicdata.ocr;

import com.logistics.tms.client.feign.FeignClientName;
import com.logistics.tms.client.feign.basicdata.ocr.request.OCRIdentifyQRCodeRequestDto;
import com.logistics.tms.client.feign.basicdata.ocr.request.OCRCustomizationIdentifyRequestDto;
import com.logistics.tms.client.feign.basicdata.ocr.response.OCRCustomizationIdentifyResponseDto;
import com.logistics.tms.client.feign.basicdata.ocr.hystrix.IOCRServiceApiHystrix;
import com.yelo.tray.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = FeignClientName.BASIC_DATA_SERVICES, fallback = IOCRServiceApiHystrix.class)
public interface IOCRServiceApi {

    /**
     * 自定义模板识别运单号
     *
     * @param requestDto 请求 Dto
     * @return 识别内容
     */
    @PostMapping("/service/ocr/ocrPicture")
    Result<OCRCustomizationIdentifyResponseDto> ocrPicture(@RequestBody OCRCustomizationIdentifyRequestDto requestDto);

    /**
     * 识别二维码  （单个）
     *
     * @param requestDto 请求 Dto
     * @return 二维码内容
     */
    @PostMapping("/service/ocr/qRCodeOSSPath")
    Result<String> qrCodeOSSPath(@RequestBody OCRIdentifyQRCodeRequestDto requestDto);
}
