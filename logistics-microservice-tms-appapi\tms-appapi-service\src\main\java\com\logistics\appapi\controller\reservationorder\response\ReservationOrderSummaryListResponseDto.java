package com.logistics.appapi.controller.reservationorder.response;

import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/8/14 14:06
 */
@Data
public class ReservationOrderSummaryListResponseDto {

    /**
     * 预约单id
     */
    private String reservationOrderId="";

    /**
     * 预约单号
     */
    private String reservationOrderCode="";

    /**
     * 预约类型
     */
    private String reservationTypeLabel="";

    /**
     * 预约时间
     */
    private String reservationTime="";

    /**
     * 地址
     */
    private String address="";

    /**
     * 数量
     */
    private String expectAmount="";

    /**
     * 车牌号
     */
    private String vehicleNo="";
}
