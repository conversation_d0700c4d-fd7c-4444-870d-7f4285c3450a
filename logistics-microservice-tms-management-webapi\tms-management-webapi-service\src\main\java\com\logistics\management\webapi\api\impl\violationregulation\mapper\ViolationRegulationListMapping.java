package com.logistics.management.webapi.api.impl.violationregulation.mapper;

import com.logistics.management.webapi.api.feign.violationregulation.dto.CertificationPicturesResponseDto;
import com.logistics.management.webapi.api.feign.violationregulation.dto.ViolationRegulationListResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.VehiclePropertyEnum;
import com.logistics.tms.api.feign.violationregulation.model.ViolationRegulationListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: sj
 * @Date: 2019/6/4 10:23
 */
public class ViolationRegulationListMapping extends MapperMapping<ViolationRegulationListResponseModel,ViolationRegulationListResponseDto> {
    @Autowired
    private ConfigKeyConstant configKeyConstant;

    @Autowired
    private Map<String, String> imageMap;

    public ViolationRegulationListMapping() {
    }

    public ViolationRegulationListMapping(ConfigKeyConstant configKeyConstant,Map<String, String> imageMap) {
        this.configKeyConstant = configKeyConstant;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        ViolationRegulationListResponseModel source = this.getSource();
        ViolationRegulationListResponseDto destination = this.getDestination();
        if(source!=null){
            if(source.getDeduction()!=null && CommonConstant.INTEGER_ZERO.equals(source.getDeduction())){
                destination.setDeduction("");
            }
            if(source.getFine()!=null){
                String fine = source.getFine().stripTrailingZeros().toPlainString();
                if(CommonConstant.ZERO.equals(fine)){
                    destination.setFine("");
                }else{
                    destination.setFine(fine);
                }
            }
            destination.setDriverLabel(Optional.ofNullable(source.getDriverName()).orElse("")+" "+Optional.ofNullable(source.getDriverPhone()).orElse(""));
            if(source.getOccuranceTime()!=null){
               destination.setOccuranceTime(DateUtils.dateToString(source.getOccuranceTime(),DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
            }
            if(source.getLastModifiedTime()!=null){
                destination.setLastModifiedTime(DateUtils.dateToString(source.getLastModifiedTime(),DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
            }

            if (ListUtils.isNotEmpty(destination.getFileList())) {
                List<CertificationPicturesResponseDto> fileList = destination.getFileList();
                destination.setCountCertificates(ConverterUtils.toString(fileList.size()));
                for (CertificationPicturesResponseDto tempCertificationDto : fileList) {
                    tempCertificationDto.setAbsoluteFilePath(configKeyConstant.fileAccessAddress + imageMap.get(tempCertificationDto.getRelativeFilepath()));
                }
            } else {
                destination.setCountCertificates(CommonConstant.ZERO);
            }

            //车辆机构展示文本
            destination.setVehiclePropertyLabel(VehiclePropertyEnum.getEnum(source.getVehicleProperty()).getValue());
        }
    }
}
