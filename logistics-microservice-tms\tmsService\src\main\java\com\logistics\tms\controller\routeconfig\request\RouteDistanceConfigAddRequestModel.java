package com.logistics.tms.controller.routeconfig.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RouteDistanceConfigAddRequestModel {

    @ApiModelProperty(value = "发货省份ID", required = true)
    private Long fromProvinceId;

    @ApiModelProperty(value = "发货省份名字", required = true)
    private String fromProvinceName;

    @ApiModelProperty(value = "发货城市ID", required = true)
    private Long fromCityId;

    @ApiModelProperty(value = "发货城市名字", required = true)
    private String fromCityName;

    @ApiModelProperty(value = "发货县区ID", required = true)
    private Long fromAreaId;

    @ApiModelProperty(value = "发货县区名字", required = true)
    private String fromAreaName;

    @ApiModelProperty(value = "卸货省份ID", required = true)
    private Long toProvinceId;

    @ApiModelProperty(value = "卸货省份名字", required = true)
    private String toProvinceName;

    @ApiModelProperty(value = "卸货城市ID", required = true)
    private Long toCityId;

    @ApiModelProperty(value = "卸货城市名字", required = true)
    private String toCityName;

    @ApiModelProperty(value = "卸货县区ID", required = true)
    private Long toAreaId;

    @ApiModelProperty(value = "卸货县区名字", required = true)
    private String toAreaName;

    @ApiModelProperty(value = "计费距离", required = true)
    private BigDecimal billingDistance;
}
