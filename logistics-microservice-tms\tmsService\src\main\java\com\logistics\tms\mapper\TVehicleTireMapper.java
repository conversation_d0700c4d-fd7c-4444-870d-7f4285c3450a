package com.logistics.tms.mapper;


import com.logistics.tms.api.feign.vehicletire.model.GetTireByTireIdsModel;
import com.logistics.tms.api.feign.vehicletire.model.VehicleTireDetailResponseModel;
import com.logistics.tms.api.feign.vehicletire.model.VehicleTireListRequestModel;
import com.logistics.tms.api.feign.vehicletire.model.VehicleTireListResponseModel;
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleBySettlementMonthModel;
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleTireByVehicleIdResponseModel;
import com.logistics.tms.entity.TVehicleTire;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface TVehicleTireMapper extends BaseMapper<TVehicleTire> {

    List<Long> searchVehicleTireIdsList(@Param("params") VehicleTireListRequestModel requestModel);

    List<VehicleTireListResponseModel> searchVehicleTireList(@Param("ids") String ids);

    VehicleTireDetailResponseModel getVehicleTireDetailById(@Param("vehicleTireId") Long vehicleTireId);

    GetTireByTireIdsModel getByTireIdOrVehicleId(@Param("id") Long id, @Param("vehicleId") Long vehicleId, @Param("replaceDate") Date replaceDate);

    int batchUpdate(@Param("list") List<TVehicleTire> list);

    List<GetVehicleTireByVehicleIdResponseModel> getVehicleTireByIdsForSettlement(@Param("ids") String ids);

    List<GetVehicleBySettlementMonthModel> getVehicleBySettlementMonth(@Param("settlementMonth") String settlementMonth);

    int settlementOilFilledByIds(@Param("ids")String ids,@Param("userName")String userName);

    int rollbackSettlementOilFilledByIds(@Param("ids")String ids,@Param("userName")String userName);

    List<TVehicleTire> getByIds(@Param("ids") String listToString);
}