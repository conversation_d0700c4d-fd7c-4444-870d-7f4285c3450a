package com.logistics.tms.biz.carrierorder.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/11/23 16:09
 */
@Data
public class SearchCarrierOrderVehicleHistoryModel {

    @ApiModelProperty("车牌号")
    private String  vehicleNo;

    @ApiModelProperty("司机")
    private String  driver;

    @ApiModelProperty("预计到货时间")
    private String expectArrivalTimeFrom;

    @ApiModelProperty("预计到货时间")
    private String  expectArrivalTimeTo;

    @ApiModelProperty("预计提货时间")
    private String  expectLoadTimeFrom;

    @ApiModelProperty("预计提货时间")
    private String  expectLoadTimeTo;

}
