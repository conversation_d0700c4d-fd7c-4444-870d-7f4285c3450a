package com.logistics.appapi.controller.vehiclesafecheck.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/11/18 13:44
 */
@Data
public class SafeCheckDetailResponseDto {
    @ApiModelProperty("安全检查id")
    private String safeCheckVehicleId = "";
    @ApiModelProperty("车牌号")
    private String vehicleNo = "";
    @ApiModelProperty("状态: 0未检查，10待确认，20待整改，30已整改，40检查完成")
    private String status = "";
    @ApiModelProperty("车辆附件信息")
    private List<SafeCheckFileResponseDto> vehicleFileList = new ArrayList<>();
    @ApiModelProperty("检查人名称")
    private String checkUserName = "";
    @ApiModelProperty("检查时间")
    private String checkTime = "";
    @ApiModelProperty("备注")
    private String remark = "";

    @ApiModelProperty("检查项目")
    private List<String> checkItemList = new ArrayList<>();
    @ApiModelProperty("整改事项")
    private SafeCheckReformResponseDto checkReformInfo = new SafeCheckReformResponseDto();
}
