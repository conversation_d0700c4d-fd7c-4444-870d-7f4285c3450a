package com.logistics.tms.biz.demandorder.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class AutoPublishWaitDispatchDemandModel {

    /**
     * 需求单id
     */
    @ApiModelProperty("需求单id")
    private Long demandOrderId;

    /**
     * 需求单号
     */
    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    /**
     * 委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨
     */
    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;

    /**
     * 委托数量
     */
    @ApiModelProperty("委托数量")
    private BigDecimal goodsAmount;

    /**
     * 固定需求唯一code
     */
    @ApiModelProperty("固定需求唯一code")
    private String fixedDemand;

    @ApiModelProperty("是否自动发布：0 否，1 是")
    private Integer autoPublish;

    /**
     * 发布时间
     */
    @ApiModelProperty("发布时间")
    private Date publishTime;
}
