<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TAttendanceRecordMapper" >
  <select id="selectByStaffIdAndDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_attendance_record
    where staff_id = #{staffId}
    and attendance_date = #{date}
    and valid = 1
  </select>

  <insert id="insertOrUpdate" parameterType="com.logistics.tms.entity.TAttendanceRecord" >
    insert into t_attendance_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="staffId != null" >
        staff_id,
      </if>
      <if test="staffName != null" >
        staff_name,
      </if>
      <if test="staffMobile != null" >
        staff_mobile,
      </if>
      <if test="staffProperty != null" >
        staff_property,
      </if>
      <if test="attendanceDate != null" >
        attendance_date,
      </if>
      <if test="manHour != null" >
        man_hour,
      </if>
      <if test="onDutyPunchTime != null" >
        on_duty_punch_time,
      </if>
      <if test="onDutyPunchLocation != null" >
        on_duty_punch_location,
      </if>
      <if test="onDutyPunchPic != null" >
        on_duty_punch_pic,
      </if>
      <if test="offDutyPunchTime != null" >
        off_duty_punch_time,
      </if>
      <if test="offDutyPunchLocation != null" >
        off_duty_punch_location,
      </if>
      <if test="offDutyPunchPic != null" >
        off_duty_punch_pic,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="staffId != null" >
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffName != null" >
        #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null" >
        #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="staffProperty != null" >
        #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="attendanceDate != null" >
        #{attendanceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="manHour != null" >
        #{manHour,jdbcType=DECIMAL},
      </if>
      <if test="onDutyPunchTime != null" >
        #{onDutyPunchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="onDutyPunchLocation != null" >
        #{onDutyPunchLocation,jdbcType=VARCHAR},
      </if>
      <if test="onDutyPunchPic != null" >
        #{onDutyPunchPic,jdbcType=VARCHAR},
      </if>
      <if test="offDutyPunchTime != null" >
        #{offDutyPunchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="offDutyPunchLocation != null" >
        #{offDutyPunchLocation,jdbcType=VARCHAR},
      </if>
      <if test="offDutyPunchPic != null" >
        #{offDutyPunchPic,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
    ON DUPLICATE KEY UPDATE
    <trim prefix="" suffixOverrides=",">
      <if test="manHour != null" >
        man_hour = values(man_hour),
      </if>
      <if test="onDutyPunchTime != null" >
        on_duty_punch_time = values(on_duty_punch_time),
      </if>
      <if test="onDutyPunchLocation != null" >
        on_duty_punch_location = values(on_duty_punch_location),
      </if>
      <if test="onDutyPunchPic != null" >
        on_duty_punch_pic = values(on_duty_punch_pic),
      </if>
      <if test="offDutyPunchTime != null" >
        off_duty_punch_time = values(off_duty_punch_time),
      </if>
      <if test="offDutyPunchLocation != null" >
        off_duty_punch_location = values(off_duty_punch_location),
      </if>
      <if test="offDutyPunchPic != null" >
        off_duty_punch_pic = values(off_duty_punch_pic),
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = values(last_modified_by),
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = values(last_modified_time),
      </if>
    </trim>
  </insert>

  <resultMap id="HistoryResultMap" type="com.logistics.tms.controller.attendance.response.AttendanceHistoryItemResponseModel" >
    <id column="id" property="attendanceRecordId" jdbcType="BIGINT" />
    <result column="attendance_date" property="attendanceDate" jdbcType="TIMESTAMP" />
    <result column="man_hour" property="manHour" jdbcType="DECIMAL" />
    <result column="on_duty_punch_time" property="onDutyPunchTime" jdbcType="TIMESTAMP" />
    <result column="on_duty_punch_location" property="onDutyPunchLocation" jdbcType="VARCHAR" />
    <result column="off_duty_punch_time" property="offDutyPunchTime" jdbcType="TIMESTAMP" />
    <result column="off_duty_punch_location" property="offDutyPunchLocation" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectHistoryByStaffAndAttendanceDate" resultMap="HistoryResultMap">
    select id,
           attendance_date,
           man_hour,
           on_duty_punch_time,
           on_duty_punch_location,
           off_duty_punch_time,
           off_duty_punch_location
    from t_attendance_record
    where staff_id = #{staffId}
    and date_format(attendance_date, '%Y-%m') = #{params.attendanceDate}
    and valid = 1
    order by attendance_date desc
  </select>
  
  <select id="selectStatistical" resultType="com.logistics.tms.biz.attendancerecord.model.AttendanceStatisticalModel">
    SELECT staff_id,
    count(DISTINCT(on_duty_punch_time)) totalOnDutyPunch,
    count(DISTINCT(off_duty_punch_time)) totalOffDutyPunch,
    SUM(man_hour) totalManHour
    FROM t_attendance_record
    WHERE valid = 1
      <if test="staffId != null" >
        AND staff_id = #{staffId}
      </if>
      <if test="attendanceDate != null" >
        AND date_format(attendance_date, '%Y-%m') = #{attendanceDate}
      </if>
    GROUP BY staff_id
  </select>

<!--auto generated by MybatisCodeHelper on 2022-11-15-->
  <select id="selectOneByIdAndStaffId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_attendance_record
    where id=#{id,jdbcType=BIGINT}
    and staff_id = #{staffId}
    and valid = 1
  </select>

<!--auto generated by MybatisCodeHelper on 2022-11-16-->
  <select id="countByIdAndStaffId" resultType="java.lang.Long">
    select count(1)
    from t_attendance_record
    where id=#{id,jdbcType=BIGINT} and staff_id=#{staffId,jdbcType=BIGINT} and valid = 1
  </select>

  <resultMap id="SearchListResultMap" type="com.logistics.tms.controller.attendance.response.SearchAttendanceListResponseModel" >
    <id column="id" property="attendanceRecordId" jdbcType="BIGINT" />
    <result column="staff_name" property="staffName"/>
    <result column="staff_mobile" property="staffMobile"/>
    <result column="staff_property" property="staffProperty"/>
    <result column="attendance_date" property="attendanceDate"/>
    <result column="man_hour" property="manHour"/>
    <result column="on_duty_punch_time" property="onDutyPunchTime"/>
    <result column="on_duty_punch_location" property="onDutyPunchLocation"/>
    <result column="off_duty_punch_time" property="offDutyPunchTime"/>
    <result column="off_duty_punch_location" property="offDutyPunchLocation"/>
  </resultMap>
<!--auto generated by MybatisCodeHelper on 2022-11-17-->
  <select id="selectSearchAttendanceList" resultMap="SearchListResultMap">
    select id,
           staff_name,
           staff_mobile,
           staff_property,
           attendance_date,
           man_hour,
           on_duty_punch_time,
           on_duty_punch_location,
           off_duty_punch_time,
           off_duty_punch_location
    from t_attendance_record
    where valid = 1
    <if test="params.attendanceUser != null and params.attendanceUser != ''">
      and (instr(staff_name, #{params.attendanceUser})
        or instr(staff_mobile, #{params.attendanceUser}))
    </if>
    <if test="params.staffProperty != null">
      and staff_property = #{params.staffProperty}
    </if>
    <if test="(params.attendanceStartTime != null and params.attendanceStartTime != '') and (params.attendanceEndTime != null and params.attendanceEndTime != '')">
      and attendance_date between #{params.attendanceStartTime} and #{params.attendanceEndTime}
    </if>
    order by attendance_date desc,
    off_duty_punch_time is null desc,
    off_duty_punch_time desc,
    on_duty_punch_time desc
  </select>

<!--auto generated by MybatisCodeHelper on 2022-11-17-->
  <select id="selectOneDetailById" resultType="com.logistics.tms.controller.attendance.response.AttendanceDetailResponseModel">
    select
    staff_name staffName,
    staff_mobile staffMobile,
    staff_property staffProperty,
    attendance_date attendanceDate,
    on_duty_punch_time onDutyPunchTime,
    on_duty_punch_pic onDutyPunchPic,
    off_duty_punch_time offDutyPunchTime,
    off_duty_punch_pic offDutyPunchPic
    from t_attendance_record
    where id=#{id} and valid = 1
  </select>

<!--auto generated by MybatisCodeHelper on 2022-11-21-->
  <select id="selectOneById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_attendance_record
    where id=#{id,jdbcType=BIGINT} and valid = 1
  </select>

<!--auto generated by MybatisCodeHelper on 2022-11-21-->
  <update id="updateById">
    update t_attendance_record
    <set>
      <if test="updated.staffId != null">
        staff_id = #{updated.staffId,jdbcType=BIGINT},
      </if>
      <if test="updated.staffName != null">
        staff_name = #{updated.staffName,jdbcType=VARCHAR},
      </if>
      <if test="updated.staffMobile != null">
        staff_mobile = #{updated.staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="updated.staffProperty != null">
        staff_property = #{updated.staffProperty,jdbcType=INTEGER},
      </if>
      <if test="updated.attendanceDate != null">
        attendance_date = #{updated.attendanceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.manHour != null">
        man_hour = #{updated.manHour,jdbcType=DECIMAL},
      </if>
      <if test="updated.onDutyPunchTime != null">
        on_duty_punch_time = #{updated.onDutyPunchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.onDutyPunchLocation != null">
        on_duty_punch_location = #{updated.onDutyPunchLocation,jdbcType=VARCHAR},
      </if>
      <if test="updated.onDutyPunchPic != null">
        on_duty_punch_pic = #{updated.onDutyPunchPic,jdbcType=VARCHAR},
      </if>
      <if test="updated.offDutyPunchTime != null">
        off_duty_punch_time = #{updated.offDutyPunchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.offDutyPunchLocation != null">
        off_duty_punch_location = #{updated.offDutyPunchLocation,jdbcType=VARCHAR},
      </if>
      <if test="updated.offDutyPunchPic != null">
        off_duty_punch_pic = #{updated.offDutyPunchPic,jdbcType=VARCHAR},
      </if>
      <if test="updated.lastModifiedBy != null">
        last_modified_by = #{updated.lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="updated.lastModifiedTime != null">
        last_modified_time = #{updated.lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id=#{id,jdbcType=BIGINT} and valid = 1
  </update>
</mapper>