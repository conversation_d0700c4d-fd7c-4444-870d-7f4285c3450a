package com.logistics.tms.mapper;

import com.logistics.tms.controller.vehiclesettlement.response.VehicleSettlementEventModel;
import com.logistics.tms.entity.TVehicleSettlementEvents;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TVehicleSettlementEventsMapper extends BaseMapper<TVehicleSettlementEvents> {
    int rollbackToWaitSettleStatement(@Param("vehicleSettlementId") Long vehicleSettlementId,@Param("userName") String userName);

    List<VehicleSettlementEventModel> getByVehicleSettlementId(@Param("vehicleSettlementId") Long vehicleSettlementId);
}