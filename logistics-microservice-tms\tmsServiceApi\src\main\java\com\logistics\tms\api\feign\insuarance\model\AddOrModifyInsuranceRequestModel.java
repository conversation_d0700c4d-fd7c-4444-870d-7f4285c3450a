package com.logistics.tms.api.feign.insuarance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/6/4 19:24
 */
@Data
public class AddOrModifyInsuranceRequestModel {
    @ApiModelProperty("保险id")
    private Long insuranceId;
    @ApiModelProperty("险种：1 商业险，2 交强险，3 个人意外险，4 货物险，5 危货承运人险")
    private Integer insuranceType;
    @ApiModelProperty("车牌Id")
    private Long vehicleId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机id")
    private Long driverId;
    @ApiModelProperty("保险公司id")
    private Long insuranceCompanyId;
    @ApiModelProperty("保单号")
    private String policyNumber;
    @ApiModelProperty("保费")
    private BigDecimal premium;
    @ApiModelProperty("保险生效时间")
    private Date startTime;
    @ApiModelProperty("保险截止时间")
    private Date endTime;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("凭证")
    private List<InsuranceTicketsRequestModel> ticketList;

    @ApiModelProperty("代缴车船税")
    private BigDecimal paymentOfVehicleAndVesselTax;

    @ApiModelProperty("个人意外险表id")
    private Long personalAccidentInsuranceId;
    @ApiModelProperty("批单号")
    private String batchNumber;
    @ApiModelProperty("保单类型：1 保单，2 批单")
    private Integer policyType;
    @ApiModelProperty("关联扣费保单id")
    private Long relatedPersonalAccidentInsuranceId;
}
