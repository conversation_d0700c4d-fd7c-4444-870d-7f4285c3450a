package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/22 10:10
 */
public class ExportExcelRegionInfo {
    public ExportExcelRegionInfo() {
    }

    private static final Map<String, String> EXPORT_REGION_INFO;

    static {
        EXPORT_REGION_INFO = new LinkedHashMap<>();
        EXPORT_REGION_INFO.put("状态", "enabledLabel");
        EXPORT_REGION_INFO.put("大区名称", "regionName");
        EXPORT_REGION_INFO.put("大区负责人", "contactName");
        EXPORT_REGION_INFO.put("操作人", "lastModifiedBy");
        EXPORT_REGION_INFO.put("操作时间", "lastModifiedTime");
    }

    public static Map<String, String> getExportRegionInfo() {
        return EXPORT_REGION_INFO;
    }
}
