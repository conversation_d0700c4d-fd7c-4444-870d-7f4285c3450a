<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandOrderCarrierMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDemandOrderCarrier" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT" />
    <result column="company_carrier_id" property="companyCarrierId" jdbcType="BIGINT" />
    <result column="carrier_contact_id" property="carrierContactId" jdbcType="BIGINT" />
    <result column="carrier_price_type" property="carrierPriceType" jdbcType="INTEGER" />
    <result column="carrier_price" property="carrierPrice" jdbcType="DECIMAL" />
    <result column="cancel_reason" property="cancelReason" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, demand_order_id, company_carrier_id, carrier_contact_id, carrier_price_type, 
    carrier_price, cancel_reason, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_demand_order_carrier
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_demand_order_carrier
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDemandOrderCarrier" >
    insert into t_demand_order_carrier (id, demand_order_id, company_carrier_id, 
      carrier_contact_id, carrier_price_type, carrier_price, 
      cancel_reason, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{demandOrderId,jdbcType=BIGINT}, #{companyCarrierId,jdbcType=BIGINT}, 
      #{carrierContactId,jdbcType=BIGINT}, #{carrierPriceType,jdbcType=INTEGER}, #{carrierPrice,jdbcType=DECIMAL}, 
      #{cancelReason,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDemandOrderCarrier" >
    insert into t_demand_order_carrier
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="demandOrderId != null" >
        demand_order_id,
      </if>
      <if test="companyCarrierId != null" >
        company_carrier_id,
      </if>
      <if test="carrierContactId != null" >
        carrier_contact_id,
      </if>
      <if test="carrierPriceType != null" >
        carrier_price_type,
      </if>
      <if test="carrierPrice != null" >
        carrier_price,
      </if>
      <if test="cancelReason != null" >
        cancel_reason,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="demandOrderId != null" >
        #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierId != null" >
        #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactId != null" >
        #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierPriceType != null" >
        #{carrierPriceType,jdbcType=INTEGER},
      </if>
      <if test="carrierPrice != null" >
        #{carrierPrice,jdbcType=DECIMAL},
      </if>
      <if test="cancelReason != null" >
        #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDemandOrderCarrier" >
    update t_demand_order_carrier
    <set >
      <if test="demandOrderId != null" >
        demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierId != null" >
        company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactId != null" >
        carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierPriceType != null" >
        carrier_price_type = #{carrierPriceType,jdbcType=INTEGER},
      </if>
      <if test="carrierPrice != null" >
        carrier_price = #{carrierPrice,jdbcType=DECIMAL},
      </if>
      <if test="cancelReason != null" >
        cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDemandOrderCarrier" >
    update t_demand_order_carrier
    set demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
      carrier_price_type = #{carrierPriceType,jdbcType=INTEGER},
      carrier_price = #{carrierPrice,jdbcType=DECIMAL},
      cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>