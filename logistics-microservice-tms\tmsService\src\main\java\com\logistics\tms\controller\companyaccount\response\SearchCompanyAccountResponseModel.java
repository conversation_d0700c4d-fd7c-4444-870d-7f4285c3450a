package com.logistics.tms.controller.companyaccount.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/6
 */
@Data
public class SearchCompanyAccountResponseModel {

	@ApiModelProperty(value = "公司账户ID")
	private Long companyAccountId;

	@ApiModelProperty(value = "状态: 1 启用 0 禁用")
	private Integer enabled;

	@ApiModelProperty(value = "银行账号")
	private String bankAccount;

	@ApiModelProperty(value = "行号")
	private String bankCode;

	@ApiModelProperty(value = "银行名称 ")
	private String bankAccountName;

	@ApiModelProperty(value = "支行名称 ")
	private String braBankName;

	@ApiModelProperty(value = "银行卡图片数量")
	private Integer bankImageNumber;

	@ApiModelProperty(value = "使用范围")
	private String remark;

	@ApiModelProperty(value = "新增人")
	private String createdBy;

	@ApiModelProperty(value = "操作人")
	private String operateBy;

	@ApiModelProperty(value = "操作时间, yyyy-MM-dd HH:mm:ss")
	private Date operateDateTime;
}
