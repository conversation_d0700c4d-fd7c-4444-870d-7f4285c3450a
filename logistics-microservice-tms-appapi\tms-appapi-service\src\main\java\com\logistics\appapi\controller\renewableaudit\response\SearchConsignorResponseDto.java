package com.logistics.appapi.controller.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class SearchConsignorResponseDto {

	@ApiModelProperty("乐橘新生客户名称（公司名称）")
	private String customerName = "";

	@ApiModelProperty("乐橘新生客户类型: 1 公司，2 个人")
	private String customerType = "";

	@ApiModelProperty("发货地址code（新生客户地址code）")
	private String loadAddressCode="";

	@ApiModelProperty("发货省id")
	private String loadProvinceId = "";

	@ApiModelProperty("发货省")
	private String loadProvinceName = "";

	@ApiModelProperty("发货市id")
	private String loadCityId = "";

	@ApiModelProperty("发货市")
	private String loadCityName = "";

	@ApiModelProperty("发货区ID")
	private String loadAreaId = "";

	@ApiModelProperty("发货区")
	private String loadAreaName = "";

	@ApiModelProperty("发货详细地址")
	private String loadDetailAddress = "";

	@ApiModelProperty("发货仓库")
	private String loadWarehouse = "";

	@ApiModelProperty("目标经度")
	private String longitude = "";

	@ApiModelProperty("目标纬度")
	private String latitude = "";

	@ApiModelProperty("发货人")
	private String consignorName = "";

	@ApiModelProperty("发货人手机号")
	private String consignorMobile = "";

	@ApiModelProperty(value = "发货人所属客户账号（手机号）")
	private String customerAccount = "";

	@ApiModelProperty("距离(KM)")
	private String distance = "";
}
