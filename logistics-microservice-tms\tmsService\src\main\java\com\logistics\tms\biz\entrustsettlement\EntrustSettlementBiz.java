package com.logistics.tms.biz.entrustsettlement;

import com.github.pagehelper.PageInfo;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tray.core.exception.BizException;
import com.logistics.tms.api.feign.entrustsettlement.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TDemandOrder;
import com.logistics.tms.entity.TDemandOrderOperateLogs;
import com.logistics.tms.entity.TDemandPayment;
import com.logistics.tms.entity.TDemandReceivement;
import com.logistics.tms.mapper.TDemandOrderMapper;
import com.logistics.tms.mapper.TDemandOrderOperateLogsMapper;
import com.logistics.tms.mapper.TDemandPaymentMapper;
import com.logistics.tms.mapper.TDemandReceivementMapper;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/11 19:07
 */
@Service
public class EntrustSettlementBiz {

    @Autowired
    private TDemandReceivementMapper tReceivementMapper;
    @Autowired
    private TDemandPaymentMapper paymentMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TDemandOrderOperateLogsMapper demandOrderOperateLogsMapper;
    @Autowired
    private TDemandOrderMapper demandOrderMapper;
    @Autowired
    private CarrierOrderCommonBiz carrierOrderCommonBiz;

    /**
     * 查询委托方结算列表
     * @param requestModel
     * @return
     */
    public EntrustSettlementListResponseModel entrustSettlementList(EntrustSettlementListRequestModel requestModel, boolean paging) {
        if (paging) {
            requestModel.enablePaging();
        }
        List<Long> settlementIdList = tReceivementMapper.searchIdForEntrustSettlementList(requestModel);
        PageInfo pageInfo = new PageInfo(settlementIdList);
        pageInfo.setList(new ArrayList());

        String settlementIds = requestModel.getSettlementIds();
        if (StringUtils.isBlank(settlementIds) && ListUtils.isNotEmpty(settlementIdList)){
            settlementIds = StringUtils.listToString(settlementIdList,',');
        }

        if(StringUtils.isNotBlank(settlementIds)){
            List<EntrustSettlementRowModel> settlementList = tReceivementMapper.entrustSettlementList(settlementIds);
            pageInfo.setList(settlementList);
        }

        EntrustSettlementListResponseModel entrustSettlementListResponseModel = tReceivementMapper.entrustSettlementListCount(requestModel);
        entrustSettlementListResponseModel.setPageInfo(pageInfo);
        return entrustSettlementListResponseModel;
    }

    /**
     * 修改费用
     * @param requestModel
     */
    @Transactional
    public void modifyCost(ModifyCostRequestModel requestModel) {
        TDemandReceivement tReceivement = tReceivementMapper.selectByPrimaryKey(requestModel.getSettlementId());
        if (tReceivement == null || tReceivement.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(EntrustDataExceptionEnum.SETTLEMENT_INFO_EMPTY);
        }
        TDemandPayment tPayment = paymentMapper.getByDemandOrderId(tReceivement.getDemandOrderId());
        if (tPayment == null){
            throw new BizException(EntrustDataExceptionEnum.SETTLEMENT_INFO_EMPTY);
        }
        if (EntrustSettlementStatusEnum.PAYMENT.getKey().equals(tReceivement.getStatus())){
            throw new BizException(EntrustDataExceptionEnum.ENTRUST_PAYMENT_NOT_MODIFY_COST);
        }else if (EntrustSettlementStatusEnum.PAYMENT.getKey().equals(tPayment.getStatus())){
            throw new BizException(EntrustDataExceptionEnum.CARRIER_PAYMENT_NOT_MODIFY_COST);
        }
        TDemandOrder tDemandOrder = demandOrderMapper.selectByPrimaryKeyDecrypt(tReceivement.getDemandOrderId());
        if (tDemandOrder == null || tDemandOrder.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }
        TDemandReceivement receivement = new TDemandReceivement();
        receivement.setId(requestModel.getSettlementId());
        receivement.setPriceType(requestModel.getContractPriceType());
        receivement.setSettlementCostTotal(requestModel.getSettlementCostTotal());
        commonBiz.setBaseEntityModify(receivement, BaseContextHandler.getUserName());
        tReceivementMapper.updateByPrimaryKeySelective(receivement);

        TDemandPayment payment = new TDemandPayment();
        payment.setId(tPayment.getId());
        payment.setPriceType(requestModel.getContractPriceType());
        payment.setSettlementCostTotal(requestModel.getSettlementCostTotal());
        commonBiz.setBaseEntityModify(payment,BaseContextHandler.getUserName());
        paymentMapper.updateByPrimaryKeySelective(payment);

        String priceType;
        if (tReceivement.getPriceType() == null || tReceivement.getPriceType().equals(CommonConstant.INTEGER_ZERO)){
            priceType = "无";
        }else{
            priceType = ContractPriceTypeEnum.getEnum(tReceivement.getPriceType()).getValue();
        }
        TDemandOrderOperateLogs logs = new TDemandOrderOperateLogs();
        logs.setDemandOrderId(tReceivement.getDemandOrderId());
        logs.setOperationType(DemandOrderOperateLogsEnum.ENTRUST_MODIFY_COST.getKey());
        logs.setOperationContent(DemandOrderOperateLogsEnum.ENTRUST_MODIFY_COST.getValue());
        logs.setRemark(String.format(DemandOrderOperateLogsEnum.ENTRUST_MODIFY_COST.getFormat(),priceType,ContractPriceTypeEnum.getEnum(requestModel.getContractPriceType()).getValue(),tReceivement.getSettlementCostTotal().stripTrailingZeros().toPlainString(),requestModel.getSettlementCostTotal().stripTrailingZeros().toPlainString()));
        logs.setOperatorName(BaseContextHandler.getUserName());
        logs.setOperateTime(new Date());
        commonBiz.setBaseEntityAdd(logs,BaseContextHandler.getUserName());
        demandOrderOperateLogsMapper.insertSelective(logs);

        ModifyCarrierSettlementCostModel modifyCarrierSettlementCostModel = new ModifyCarrierSettlementCostModel();
        modifyCarrierSettlementCostModel.setDemandOrderId(tReceivement.getDemandOrderId());
        modifyCarrierSettlementCostModel.setContractPriceType(requestModel.getContractPriceType());
        modifyCarrierSettlementCostModel.setSettlementCostTotal(requestModel.getSettlementCostTotal());
        modifyCarrierSettlementCostModel.setUserName(BaseContextHandler.getUserName());
        modifyCarrierSettlementCostModel.setSettlementTonnage(tDemandOrder.getSettlementTonnage());
        modifyCarrierSettlementCostModel.setCarrierSettlementTonnage(tDemandOrder.getCarrierSettlement());
        modifyCarrierSettlementCostModel.setGoodsUnit(tDemandOrder.getGoodsUnit());
        carrierOrderCommonBiz.modifySettlementCost(modifyCarrierSettlementCostModel);
    }

    /**
     * 结算详情，确认收款，回退界面
     * @param requestModel
     * @return
     */
    public GetSettlementDetailResponseModel getSettlementDetail(GetSettlementDetailRequestModel requestModel) {
        List<TDemandReceivement> receivementList = tReceivementMapper.getByIds(requestModel.getSettlementIds());
        if (ListUtils.isEmpty(receivementList)){
            throw new BizException(EntrustDataExceptionEnum.SETTLEMENT_INFO_EMPTY);
        }
        for (TDemandReceivement tDemandReceivement : receivementList) {
            if (CommonConstant.ONE.equals(requestModel.getType())) {//收款详情
                if (EntrustSettlementStatusEnum.PAYMENT.getKey().equals(tDemandReceivement.getStatus())) {
                    throw new BizException(EntrustDataExceptionEnum.PAYMENT);
                }
                if (CommonConstant.ONE.equals(requestModel.getType()) && CommonConstant.INTEGER_ZERO.equals(tDemandReceivement.getPriceType())) {
                    throw new BizException(EntrustDataExceptionEnum.ENTRUST_PRICE_TYPE_EMPTY);
                }
            } else {//回退详情
                if (EntrustSettlementStatusEnum.NOT_PAYMENT.getKey().equals(tDemandReceivement.getStatus())) {
                    throw new BizException(EntrustDataExceptionEnum.NOT_PAYMENT);
                }
            }
        }
        EntrustSettlementListRequestModel entrustSettlementListRequestModel = new EntrustSettlementListRequestModel();
        entrustSettlementListRequestModel.setSettlementIds(requestModel.getSettlementIds());
        EntrustSettlementListResponseModel settlementTotal = tReceivementMapper.entrustSettlementListCount(entrustSettlementListRequestModel);
        GetSettlementDetailResponseModel getSettlementDetailResponseModel = MapperUtils.mapper(settlementTotal,GetSettlementDetailResponseModel.class);
        List<GetSettlementDetailRowModel> settlementList = tReceivementMapper.getSettlementDetail(requestModel.getSettlementIds());
        getSettlementDetailResponseModel.setSettlementRows(settlementList);
        getSettlementDetailResponseModel.setSettlementIds(requestModel.getSettlementIds());
        return getSettlementDetailResponseModel;
    }

    /**
     * 结算详情，修改费用
     * @param requestModel
     * @return
     */
    public GetDetailResponseModel getDetail(GetDetailRequestModel requestModel) {
        TDemandReceivement tReceivement = tReceivementMapper.selectByPrimaryKey(requestModel.getSettlementId());
        if (tReceivement == null){
            throw new BizException(EntrustDataExceptionEnum.SETTLEMENT_INFO_EMPTY);
        }
        TDemandPayment tPayment = paymentMapper.getByDemandOrderId(tReceivement.getDemandOrderId());
        if (tPayment == null){
            throw new BizException(EntrustDataExceptionEnum.SETTLEMENT_INFO_EMPTY);
        }
        if (EntrustSettlementStatusEnum.PAYMENT.getKey().equals(tReceivement.getStatus())){
            throw new BizException(EntrustDataExceptionEnum.ENTRUST_PAYMENT_NOT_MODIFY_COST);
        }else if (EntrustSettlementStatusEnum.PAYMENT.getKey().equals(tPayment.getStatus())){
            throw new BizException(EntrustDataExceptionEnum.CARRIER_PAYMENT_NOT_MODIFY_COST);
        }
        return tReceivementMapper.getDetailForUpdateCost(requestModel.getSettlementId());
    }

    /**
     * 已收款
     * @param requestModel
     */
    @Transactional
    public void receiveMoney(GetSettlementDetailRequestModel requestModel) {
        List<TDemandReceivement> receivementList = tReceivementMapper.getByIds(requestModel.getSettlementIds());
        if (ListUtils.isEmpty(receivementList)){
            throw new BizException(EntrustDataExceptionEnum.SETTLEMENT_INFO_EMPTY);
        }
        for (TDemandReceivement tDemandReceivement : receivementList) {
            if (EntrustSettlementStatusEnum.PAYMENT.getKey().equals(tDemandReceivement.getStatus())){
                throw new BizException(EntrustDataExceptionEnum.PAYMENT);
            }
            if (CommonConstant.INTEGER_ZERO.equals(tDemandReceivement.getPriceType())){
                throw new BizException(EntrustDataExceptionEnum.ENTRUST_PRICE_TYPE_EMPTY);
            }
        }

        TDemandReceivement upReceivement;
        List<TDemandReceivement> upList = new ArrayList<>();
        TDemandOrderOperateLogs logs;
        List<TDemandOrderOperateLogs> logsList = new ArrayList<>();
        Date now = new Date();
        for (TDemandReceivement item : receivementList) {
            upReceivement = new TDemandReceivement();
            upReceivement.setId(item.getId());
            upReceivement.setStatus(EntrustSettlementStatusEnum.PAYMENT.getKey());
            upReceivement.setSettlementTime(now);
            commonBiz.setBaseEntityModify(upReceivement, BaseContextHandler.getUserName());
            upList.add(upReceivement);

            logs = new TDemandOrderOperateLogs();
            logs.setDemandOrderId(item.getDemandOrderId());
            logs.setOperationType(DemandOrderOperateLogsEnum.ENTRUST_RECEIVEMENT.getKey());
            logs.setOperationContent(DemandOrderOperateLogsEnum.ENTRUST_RECEIVEMENT.getValue());
            logs.setRemark(String.format(DemandOrderOperateLogsEnum.ENTRUST_RECEIVEMENT.getFormat(),BaseContextHandler.getUserName(),item.getSettlementCostTotal()));
            logs.setOperatorName(BaseContextHandler.getUserName());
            logs.setOperateTime(now);
            commonBiz.setBaseEntityAdd(logs,BaseContextHandler.getUserName());
            logsList.add(logs);
        }
        if (ListUtils.isNotEmpty(upList)){
            tReceivementMapper.batchUpdate(upList);
        }
        if (ListUtils.isNotEmpty(logsList)){
            demandOrderOperateLogsMapper.batchInsertSelective(logsList);
        }

    }

    /**
     * 退款
     * @param requestModel
     */
    @Transactional
    public void refund(RefundRequestModel requestModel) {

        List<TDemandReceivement> receivementList = tReceivementMapper.getByIds(requestModel.getSettlementIds());
        if (ListUtils.isEmpty(receivementList)){
            throw new BizException(EntrustDataExceptionEnum.SETTLEMENT_INFO_EMPTY);
        }
        for(TDemandReceivement tReceivement:receivementList){
            if(EntrustSettlementStatusEnum.NOT_PAYMENT.getKey().equals(tReceivement.getStatus())){
                throw new BizException(EntrustDataExceptionEnum.NOT_PAYMENT);
            }
        }
        TDemandReceivement upReceivement;
        List<TDemandReceivement> upList = new ArrayList<>();
        TDemandOrderOperateLogs logs;
        List<TDemandOrderOperateLogs> logsList = new ArrayList<>();
        Date now = new Date();
        for (TDemandReceivement item : receivementList) {
            upReceivement = new TDemandReceivement();
            upReceivement.setId(item.getId());
            upReceivement.setStatus(EntrustSettlementStatusEnum.NOT_PAYMENT.getKey());
            upReceivement.setSettlementTime(null);
            commonBiz.setBaseEntityModify(upReceivement, BaseContextHandler.getUserName());
            upList.add(upReceivement);

            logs = new TDemandOrderOperateLogs();
            logs.setDemandOrderId(item.getDemandOrderId());
            logs.setOperationType(DemandOrderOperateLogsEnum.ENTRUST_REFUND.getKey());
            logs.setOperationContent(DemandOrderOperateLogsEnum.ENTRUST_REFUND.getValue());
            logs.setRemark(String.format(DemandOrderOperateLogsEnum.ENTRUST_REFUND.getFormat(),BaseContextHandler.getUserName(),item.getSettlementCostTotal().stripTrailingZeros().toPlainString(),requestModel.getRefundReason()));
            logs.setOperatorName(BaseContextHandler.getUserName());
            logs.setOperateTime(now);
            commonBiz.setBaseEntityAdd(logs,BaseContextHandler.getUserName());
            logsList.add(logs);
        }
        if (ListUtils.isNotEmpty(upList)){
            tReceivementMapper.batchUpdateForTime(upList);
        }
        if (ListUtils.isNotEmpty(logsList)){
            demandOrderOperateLogsMapper.batchInsertSelective(logsList);
        }
    }
}
