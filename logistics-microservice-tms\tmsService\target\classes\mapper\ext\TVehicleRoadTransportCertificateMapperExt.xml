<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TVehicleRoadTransportCertificateMapper">
  <select id="getByVehicleId" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    from t_vehicle_road_transport_certificate
    where valid = 1 and vehicle_id = #{vehicleId,jdbcType=BIGINT} LIMIT 1
  </select>

  <update id="deleteRoadTransportCertificateInfo">
    update t_vehicle_road_transport_certificate t1
left join t_vehicle_road_transport_certificate_annual_review t2 on t1.id = t2.road_transport_cetification_id and t2.valid = 1
left join t_certification_pictures t3 on ((t3.object_type = 14 and t3.object_id = t1.id) or (t3.object_type = 12 and t3.object_id = t2.id)) and t3.valid = 1
set t1.valid = 0,t2.valid = 0,t3.valid = 0,
t1.last_modified_by = #{modifiedBy,jdbcType=VARCHAR},t1.last_modified_time = #{modifiedTime,jdbcType=TIMESTAMP},
t2.last_modified_by = #{modifiedBy,jdbcType=VARCHAR},t2.last_modified_time = #{modifiedTime,jdbcType=TIMESTAMP},
t3.last_modified_by = #{modifiedBy,jdbcType=VARCHAR},t3.last_modified_time = #{modifiedTime,jdbcType=TIMESTAMP}
where t1.valid = 1 and t1.vehicle_id in (${vehicleIds});
  </update>

  <update id="updateByPrimaryKeySelectiveExt" parameterType="com.logistics.tms.entity.TVehicleRoadTransportCertificate">
    update t_vehicle_road_transport_certificate
    <set>
      <if test="params.vehicleId != null">
        vehicle_id = #{params.vehicleId,jdbcType=BIGINT},
      </if>
        certification_sign = #{params.certificationSign,jdbcType=VARCHAR},
        business_license_number = #{params.businessLicenseNumber,jdbcType=VARCHAR},
        economic_type = #{params.economicType,jdbcType=VARCHAR},
        transport_tonnage = #{params.transportTonnage,jdbcType=DECIMAL},
        business_scope = #{params.businessScope,jdbcType=VARCHAR},
        certification_department = #{params.certificationDepartment,jdbcType=VARCHAR},
        issue_date = #{params.issueDate,jdbcType=TIMESTAMP},
        obtain_date = #{params.obtainDate,jdbcType=TIMESTAMP},
        remark = #{params.remark,jdbcType=VARCHAR},
      <if test="params.createdBy != null">
        created_by = #{params.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="params.createdTime != null">
        created_time = #{params.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="params.lastModifiedBy != null">
        last_modified_by = #{params.lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="params.lastModifiedTime != null">
        last_modified_time = #{params.lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="params.valid != null">
        valid = #{params.valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{params.id,jdbcType=BIGINT}
  </update>


  <update id="updateByPrimaryKeySelectiveForExcel" parameterType="com.logistics.tms.entity.TVehicleRoadTransportCertificate">
    update t_vehicle_road_transport_certificate
    <set>
      <if test="params.vehicleId != null">
        vehicle_id = #{params.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="params.certificationSign != null and params.certificationSign!=''">
        certification_sign = #{params.certificationSign,jdbcType=VARCHAR},
      </if>
      <if test="params.businessLicenseNumber != null and params.businessLicenseNumber != ''">
        business_license_number = #{params.businessLicenseNumber,jdbcType=VARCHAR},
      </if>
      <if test="params.economicType != null and params.economicType != ''">
        economic_type = #{params.economicType,jdbcType=VARCHAR},
      </if>
      <if test="params.transportTonnage != null">
        transport_tonnage = #{params.transportTonnage,jdbcType=DECIMAL},
      </if>
      <if test="params.businessScope != null and params.businessScope != ''">
        business_scope = #{params.businessScope,jdbcType=VARCHAR},
      </if>
      <if test="params.certificationDepartment != null and params.certificationDepartment != ''">
        certification_department = #{params.certificationDepartment,jdbcType=VARCHAR},
      </if>
      <if test="params.issueDate != null">
        issue_date = #{params.issueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="params.obtainDate != null">
        obtain_date = #{params.obtainDate,jdbcType=TIMESTAMP},
      </if>
      <if test="params.remark != null and params.remark != ''">
        remark = #{params.remark,jdbcType=VARCHAR},
      </if>
      <if test="params.createdBy != null">
        created_by = #{params.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="params.createdTime != null">
        created_time = #{params.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="params.lastModifiedBy != null">
        last_modified_by = #{params.lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="params.lastModifiedTime != null">
        last_modified_time = #{params.lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="params.valid != null">
        valid = #{params.valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{params.id,jdbcType=BIGINT}
  </update>

  <select id="getByVehicleIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    from t_vehicle_road_transport_certificate
    where valid = 1
    and vehicle_id in (${vehicleIds})
  </select>
</mapper>