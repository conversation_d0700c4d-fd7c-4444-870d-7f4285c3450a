package com.logistics.management.webapi.client.demandorder;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.demandorder.hystrix.DemandOrderForLeYiClientHystrix;
import com.logistics.management.webapi.client.demandorder.request.*;
import com.logistics.management.webapi.client.demandorder.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/1/9 9:46
 */
@FeignClient(name = FeignClientName.TMS_SERVICES, fallback = DemandOrderForLeYiClientHystrix.class)
public interface DemandOrderForLeYiClient {

    /**
     * 需求单批量发布详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "需求单批量发布详情")
    @PostMapping(value = "/service/demandOrder/publishDetail")
    Result<List<BatchPublishDetailResponseModel>> publishDetail(@RequestBody BatchPublishDetailRequestModel requestModel);

    /**
     * 需求单批量发布
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "需求单批量发布")
    @PostMapping(value = "/service/demandOrder/confirmPublish")
    Result<Boolean> confirmPublish(@RequestBody BatchPublishRequestModel requestModel);

    /**
     * 获取云盘需求单列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取云盘需求单列表")
    @PostMapping(value = "/service/demandOrder/searchListForLeYi")
    Result<PageInfo<DemandOrderForLeYiResponseModel>> searchListForLeYi(@RequestBody DemandOrderSearchForLeYiRequestModel requestModel);

    /**
     * 导出需求单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出需求单")
    @PostMapping(value = "/service/demandOrder/exportDemandOrderForLeYi")
    Result<PageInfo<DemandOrderForLeYiResponseModel>> exportDemandOrderForLeYi(@RequestBody DemandOrderSearchForLeYiRequestModel requestModel);

    /**
     * 获取需求单详情
     *
     * @param requestModel
     */
    @ApiOperation(value = "获取需求单详情")
    @PostMapping(value = "/service/demandOrder/getDetailForLeYi")
    Result<DemandOrderDetailForLeYiResponseModel> getDetailForLeYi(@RequestBody DemandOrderDetailRequestModel requestModel);
    /**
     * 客户单号详情
     *
     * @param requestModel 需求单客户单号详情
     */
    @ApiOperation(value = "获取客户单号详情")
    @PostMapping(value = "/service/demandOrder/getDemandOrderOrders")
    Result<List<DemandOrderOrderRelResponseModel>> getDemandOrderOrders(@RequestBody DemandOrderDetailRequestModel requestModel);

    /**
     * 模糊搜索仓库地址(发布页面调用云盘仓库接口)
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "模糊搜索仓库地址(发布页面调用云盘仓库接口)")
    @PostMapping(value = "/service/demandOrder/searchYPWarehouse")
    Result<List<SearchDemandUnLoadAddressResponseModel>> searchYPWarehouse(@RequestBody SearchDemandUnLoadAddressRequestModel requestModel);

    /**
     * 确认放空（需求单仅存在【已放空】/【已取消】且无其他有效状态运单&【待调度】需求单）
     * @param requestModel
     */
    @ApiOperation(value = "确认放空")
    @PostMapping(value = "/service/demandOrder/confirmEmpty")
    Result<Boolean> confirmEmpty(@RequestBody DemandOrderEmptyRequestModel requestModel);

    /**
     * 云盘物流看板--调度报警
     * @return
     */
    @ApiOperation(value = "云盘物流看板-调度报警")
    @PostMapping(value = "/service/demandOrder/dispatchAlarmStatistics")
    Result<List<DispatchAlarmStatisticsResponseModel>> dispatchAlarmStatistics();

    /**
     * 云盘物流看板-待调度、待发布
     *
     * @return 待发布, 待调度数据
     */
    @ApiOperation(value = "云盘物流看板-待调度、待发布")
    @PostMapping(value = "/service/demandOrder/waitDispatchStatistics")
    Result<WaitDispatchStatisticsResponseModel> waitDispatchStatistics();

    /**
     * 云盘物流看板--合计数据
     *
     * @return 合计数据
     */
    @ApiOperation(value = "云盘物流看板-合计数据")
    @PostMapping(value = "/service/demandOrder/aggregateDataStatistics")
    Result<AggregateDataStatisticsResponseModel> aggregateDataStatistics();

    /**
     * 云盘物流看板-地图数据
     *
     * @return 市维度数据
     */
    @ApiOperation(value = "云盘物流看板-地图数据")
    @PostMapping(value = "/service/demandOrder/mapDataStatistics")
    Result<List<MapDataStatisticsResponseModel>> mapDataStatistics();

    /**
     * 云盘需求单-智能拼单
     *
     * @return
     */
    @ApiOperation(value = "云盘需求单-智能拼单")
    @PostMapping(value = "/service/demandOrder/smartSpellList")
    Result<SmartSpellListResponseModel> smartSpellList();

    /**
     * 云盘需求单修改车主
     *
     * @param requestModel 修改车主信息
     * @return 操作结果
     */
    @ApiOperation(value = "云盘需求单修改车主v1.2.3")
    @PostMapping(value = "/service/demandOrder/modifyCarrierForLeyi")
    Result<Boolean> modifyCarrierForLeyi(@RequestBody ModifyCarrierForLeyiRequestModel requestModel);

    /**
     * 云盘需求单修改车主详情查询
     *
     * @param requestModel 需求单id集合
     * @return 详情信息
     */
    @ApiOperation(value = "云盘需求单修改车主详情查询v1.2.3")
    @PostMapping(value = "/service/demandOrder/modifyCarrierDetailForLeyi")
    Result<List<ModifyCarrierDetailForLeyiResponseModel>> modifyCarrierDetailForLeyi(@RequestBody ModifyCarrierDetailForLeyiRequestModel requestModel);

    /**
     * 云盘需求单回退
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "云盘需求单回退")
    @PostMapping(value = "/service/demandOrder/rollbackDemandOrder")
    Result<Boolean> rollbackDemandOrder(@RequestBody RollbackDemandOrderRequestModel requestModel);

}
