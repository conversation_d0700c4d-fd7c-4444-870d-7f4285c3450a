package com.logistics.management.webapi.api.impl.demandorderobjection;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.demandorderobjection.DemandOrderObjectionApi;
import com.logistics.management.webapi.api.feign.demandorderobjection.dto.SearchDemandOrderObjectionRequestDto;
import com.logistics.management.webapi.api.feign.demandorderobjection.dto.SearchDemandOrderObjectionResponseDto;
import com.logistics.management.webapi.api.impl.demandorderobjection.mapping.SearchDemandOrderObjectionMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportExcelHeaderDemandOrderObjection;
import com.logistics.tms.api.feign.demandorderobjection.DemandOrderObjectionServiceApi;
import com.logistics.tms.api.feign.demandorderobjection.model.SearchDemandOrderObjectionRequestModel;
import com.logistics.tms.api.feign.demandorderobjection.model.SearchDemandOrderObjectionResponseModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.ExcelUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2021/10/18 11:31
 */
@RestController
public class DemandOrderObjectionApiImpl implements DemandOrderObjectionApi {

    @Autowired
    private DemandOrderObjectionServiceApi demandOrderObjectionServiceApi;

    /**
     * 云盘需求单异常列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchDemandOrderObjectionResponseDto>> searchDemandOrderObjection(@RequestBody SearchDemandOrderObjectionRequestDto requestDto) {
        Result<PageInfo<SearchDemandOrderObjectionResponseModel>> result = demandOrderObjectionServiceApi.searchDemandOrderObjection(MapperUtils.mapper(requestDto, SearchDemandOrderObjectionRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        pageInfo.setList(MapperUtils.mapper(result.getData().getList(),SearchDemandOrderObjectionResponseDto.class,new SearchDemandOrderObjectionMapping()));
        return Result.success(pageInfo);
    }

    /**
     * 导出云盘需求单异常列表
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportDemandOrderObjection(SearchDemandOrderObjectionRequestDto requestDto, HttpServletResponse response) {
        Result<PageInfo<SearchDemandOrderObjectionResponseModel>> result = demandOrderObjectionServiceApi.exportDemandOrderObjection(MapperUtils.mapper(requestDto, SearchDemandOrderObjectionRequestModel.class));
        result.throwException();
        List<SearchDemandOrderObjectionResponseDto> list = MapperUtils.mapper(result.getData().getList(), SearchDemandOrderObjectionResponseDto.class, new SearchDemandOrderObjectionMapping());
        String fileName = "云盘需求单异常报表";
        Map<String,String> exportMap = ExportExcelHeaderDemandOrderObjection.getExcelDemandOrderObjection();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return list;
            }
        });
    }
}
