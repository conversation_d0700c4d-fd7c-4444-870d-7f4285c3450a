package com.logistics.management.webapi.api.feign.driversafepromise.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: sj
 * @Date: 2019/11/6 16:43
 */
@Data
public class SignSafePromiseDetailRequestDto {
    @ApiModelProperty("承诺书ID")
    @NotBlank(message = "承诺书ID为空")
    private String safePromiseId;
    @ApiModelProperty("签订承诺书ID")
    private String signSafePromiseId;
    @ApiModelProperty("签订状态: 0 未签订  1 已签订")
    private String status;
}
