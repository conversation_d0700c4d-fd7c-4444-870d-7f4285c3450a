package com.logistics.management.webapi.client.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/10/12 19:23
 */
@Data
public class SearchVehicleSettlementListResponseModel {
    private Long vehicleSettlementId;
    @ApiModelProperty("结算状态：0 待对账，1 待发送，2 待确认，3 待处理，4 待结清，5 部分结清，6 已结清")
    private Integer status;
    @ApiModelProperty("车辆id")
    private Long vehicleId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("司机 姓名")
    private String driverName;
    @ApiModelProperty("账单时间")
    private String settlementMonth;
    @ApiModelProperty("月运单数量")
    private Integer carrierOrderCount;
    @ApiModelProperty("运费")
    private BigDecimal carrierFreight;
    @ApiModelProperty("月实付运费")
    private BigDecimal actualExpensesPayable;
    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;
    @ApiModelProperty("扣减费用合计")
    private BigDecimal deductingFeeTotal;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("撤回备注")
    private String withdrawRemark;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;

}
