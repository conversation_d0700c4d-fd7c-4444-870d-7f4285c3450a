package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2024/03/20
*/
@Data
public class TInvoicingInvoice extends BaseEntity {
    /**
    * 发票管理id
    */
    @ApiModelProperty("发票管理id")
    private Long invoicingId;

    /**
    * 发票类型：1 电子发票，2 纸质发票
    */
    @ApiModelProperty("发票类型：1 电子发票，2 纸质发票")
    private Integer invoiceType;

    /**
    * 发票代码
    */
    @ApiModelProperty("发票代码")
    private String invoiceCode;

    /**
    * 发票号码
    */
    @ApiModelProperty("发票号码")
    private String invoiceNum;

    /**
    * 开票日期
    */
    @ApiModelProperty("开票日期")
    private Date invoiceDate;

    /**
    * 发票金额 
    */
    @ApiModelProperty("发票金额 ")
    private BigDecimal invoiceAmount;

    /**
    * 税率
    */
    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    /**
    * 价税合计
    */
    @ApiModelProperty("价税合计")
    private BigDecimal totalTaxAndPrice;

    /**
    * 发票图片
    */
    @ApiModelProperty("发票图片")
    private String invoicePicture;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}