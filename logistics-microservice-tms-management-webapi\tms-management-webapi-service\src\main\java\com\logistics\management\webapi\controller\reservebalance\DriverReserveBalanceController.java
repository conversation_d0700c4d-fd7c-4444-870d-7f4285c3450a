package com.logistics.management.webapi.controller.reservebalance;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.utils.ConvertPageInfoUtil;
import com.logistics.management.webapi.base.utils.ExportUtils;
import com.logistics.management.webapi.client.reservebalance.DriverReserveBalanceClient;
import com.logistics.management.webapi.client.reservebalance.model.request.DriverReserveBalanceListRequestModel;
import com.logistics.management.webapi.client.reservebalance.model.request.ReserveBalanceDetailRequestModel;
import com.logistics.management.webapi.client.reservebalance.model.response.DriverReserveBalanceListResponseModel;
import com.logistics.management.webapi.client.reservebalance.model.response.ReserveBalanceDetailResponseModel;
import com.logistics.management.webapi.client.reservebalance.model.response.SearchDriverReserveBalanceResponseModel;
import com.logistics.management.webapi.controller.reservebalance.dto.request.DriverReserveBalanceListRequestDto;
import com.logistics.management.webapi.controller.reservebalance.dto.request.ReserveBalanceDetailRequestDto;
import com.logistics.management.webapi.controller.reservebalance.dto.response.DriverReserveBalanceListResponseDto;
import com.logistics.management.webapi.controller.reservebalance.dto.response.ReserveBalanceDetailResponseDto;
import com.logistics.management.webapi.controller.reservebalance.dto.response.SearchDriverReserveBalanceResponseDto;
import com.logistics.management.webapi.controller.reservebalance.mapping.DriverReserveBalanceListMapping;
import com.logistics.management.webapi.controller.reservebalance.mapping.ReserveBalanceDetailMapping;
import com.logistics.management.webapi.controller.reservebalance.mapping.SearchDriverReserveBalanceMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
@Api(tags = "司机备用金管理")
@RestController
@RequestMapping(value = "/api/DriverReserveBalance")
public class DriverReserveBalanceController {

	@Resource
    private DriverReserveBalanceClient driverReserveBalanceClient;

    /**
     * 备用金余额台账搜索
     *
     * @param requestDto 请求Dto
     * @return SearchDriverReserveBalanceResponseDto
     */
    @ApiOperation(value = "备用金余额台账搜索", tags = "1.2.5")
    @PostMapping("/searchList")
    public Result<SearchDriverReserveBalanceResponseDto> searchList(@RequestBody DriverReserveBalanceListRequestDto requestDto) {
        DriverReserveBalanceListRequestModel requestModel = MapperUtils.mapper(requestDto, DriverReserveBalanceListRequestModel.class);
        Result<SearchDriverReserveBalanceResponseModel> responseModelResult = driverReserveBalanceClient.searchList(requestModel);
        responseModelResult.throwException();

        SearchDriverReserveBalanceResponseDto responseDto = MapperUtils.mapper(responseModelResult.getData(), SearchDriverReserveBalanceResponseDto.class,
                new SearchDriverReserveBalanceMapping());
        return Result.success(responseDto);
    }

    /**
     * 导出备用金余额台账
     *
     * @param requestDto 请求Dto
     */
    @ApiOperation(value = "备用金余额台账导出", tags = "1.2.5")
    @GetMapping("/searchListExport")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void searchListExport(DriverReserveBalanceListRequestDto requestDto, HttpServletResponse response) {
        DriverReserveBalanceListRequestModel requestModel = MapperUtils.mapper(requestDto, DriverReserveBalanceListRequestModel.class);
        Result<List<DriverReserveBalanceListResponseModel>> responseModelResult = driverReserveBalanceClient.searchListExport(requestModel);
        responseModelResult.throwException();

        List<DriverReserveBalanceListResponseDto> responseDtoList = MapperUtils.mapper(responseModelResult.getData(), DriverReserveBalanceListResponseDto.class,
                new DriverReserveBalanceListMapping(true));
        ExportUtils.exportByYeloExcel(response, responseDtoList, DriverReserveBalanceListResponseDto.class, "备用金台账");
    }

    /**
     * 备用金余额台账明细
     *
     * @param requestDto 请求Dto
     * @return PageInfo<ReserveBalanceDetailResponseDto>
     */
    @ApiOperation(value = "备用金余额台账明细查询", tags = "1.2.5")
    @PostMapping("/reserveBalanceDetail")
    public Result<PageInfo<ReserveBalanceDetailResponseDto>> reserveBalanceDetail(@RequestBody ReserveBalanceDetailRequestDto requestDto) {
        ReserveBalanceDetailRequestModel requestModel = MapperUtils.mapper(requestDto, ReserveBalanceDetailRequestModel.class);
        Result<PageInfo<ReserveBalanceDetailResponseModel>> responseResult = driverReserveBalanceClient.reserveBalanceDetail(requestModel);
        responseResult.throwException();

        PageInfo<ReserveBalanceDetailResponseDto> pageInfo =
                ConvertPageInfoUtil.convertPageInfo(responseResult.getData(), ReserveBalanceDetailResponseDto.class, new ReserveBalanceDetailMapping());
        return Result.success(pageInfo);
    }

    /**
     * 备用金明细导出
     *
     * @param requestDto 请求Dto
     */
    @ApiOperation(value = "备用金余额台账明细导出", tags = "1.2.5")
    @PostMapping("/reserveBalanceDetailExport")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void reserveBalanceDetailExport(@RequestBody ReserveBalanceDetailRequestDto requestDto, HttpServletResponse response) {
        ReserveBalanceDetailRequestModel requestModel = MapperUtils.mapper(requestDto, ReserveBalanceDetailRequestModel.class);
        var responseModelResult = driverReserveBalanceClient.reserveBalanceDetailExport(requestModel);
        responseModelResult.throwException();

        String fileName = "备用金明细";
        List<ReserveBalanceDetailResponseDto> dtoList = Collections.emptyList();
        var reserveBalanceDetailOptional = Optional.ofNullable(responseModelResult.getData())
                .flatMap(s -> s.entrySet().stream().findFirst());
        if (reserveBalanceDetailOptional.isPresent()) {
            var reserveBalanceDetailMap = reserveBalanceDetailOptional.get();
            dtoList = MapperUtils.mapper(reserveBalanceDetailMap.getValue(),
                    ReserveBalanceDetailResponseDto.class, new ReserveBalanceDetailMapping());
            fileName = reserveBalanceDetailMap.getKey() + fileName;
        }
        ExportUtils.exportByYeloExcel(response, dtoList, ReserveBalanceDetailResponseDto.class, fileName);
    }
}
