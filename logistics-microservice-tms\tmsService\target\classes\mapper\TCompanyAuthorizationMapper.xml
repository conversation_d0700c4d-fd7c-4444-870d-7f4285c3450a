<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TCompanyAuthorizationMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCompanyAuthorization">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="company_type" jdbcType="INTEGER" property="companyType" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="is_archive" jdbcType="INTEGER" property="isArchive" />
    <result column="auditor_name" jdbcType="VARCHAR" property="auditorName" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="archived_name" jdbcType="VARCHAR" property="archivedName" />
    <result column="archived_time" jdbcType="TIMESTAMP" property="archivedTime" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, company_type, audit_status, is_archive, auditor_name, audit_time, 
    remark, archived_name, archived_time, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_company_authorization
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_company_authorization
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCompanyAuthorization">
    insert into t_company_authorization (id, company_id, company_type, 
      audit_status, is_archive, auditor_name, 
      audit_time, remark, archived_name, 
      archived_time, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT}, #{companyType,jdbcType=INTEGER}, 
      #{auditStatus,jdbcType=INTEGER}, #{isArchive,jdbcType=INTEGER}, #{auditorName,jdbcType=VARCHAR}, 
      #{auditTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{archivedName,jdbcType=VARCHAR}, 
      #{archivedTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCompanyAuthorization">
    insert into t_company_authorization
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyType != null">
        company_type,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="isArchive != null">
        is_archive,
      </if>
      <if test="auditorName != null">
        auditor_name,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="archivedName != null">
        archived_name,
      </if>
      <if test="archivedTime != null">
        archived_time,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyType != null">
        #{companyType,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="isArchive != null">
        #{isArchive,jdbcType=INTEGER},
      </if>
      <if test="auditorName != null">
        #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="archivedName != null">
        #{archivedName,jdbcType=VARCHAR},
      </if>
      <if test="archivedTime != null">
        #{archivedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCompanyAuthorization">
    update t_company_authorization
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyType != null">
        company_type = #{companyType,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="isArchive != null">
        is_archive = #{isArchive,jdbcType=INTEGER},
      </if>
      <if test="auditorName != null">
        auditor_name = #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="archivedName != null">
        archived_name = #{archivedName,jdbcType=VARCHAR},
      </if>
      <if test="archivedTime != null">
        archived_time = #{archivedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCompanyAuthorization">
    update t_company_authorization
    set company_id = #{companyId,jdbcType=BIGINT},
      company_type = #{companyType,jdbcType=INTEGER},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      is_archive = #{isArchive,jdbcType=INTEGER},
      auditor_name = #{auditorName,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      archived_name = #{archivedName,jdbcType=VARCHAR},
      archived_time = #{archivedTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>