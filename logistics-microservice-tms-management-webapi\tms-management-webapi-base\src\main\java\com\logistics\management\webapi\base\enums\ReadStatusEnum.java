package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * @author: wjf
 * @date: 2024/6/5 16:28
 */
@Getter
@AllArgsConstructor
public enum ReadStatusEnum {
    DEFAULT(0, "未读"),
    THIRTY(1, "已读"),
    ;

    private final Integer key;
    private final String value;

    public static ReadStatusEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
