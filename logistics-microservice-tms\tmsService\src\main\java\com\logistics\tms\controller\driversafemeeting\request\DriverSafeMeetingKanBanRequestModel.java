package com.logistics.tms.controller.driversafemeeting.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/11/1 19:55
 */
@Data
public class DriverSafeMeetingKanBanRequestModel {
    @ApiModelProperty(value = "年起")
    private String meetingYearStart;

    @ApiModelProperty(value = "年止")
    private String meetingYearEnd;

    @ApiModelProperty(value = "安全例会类型：1 安全例会，2 紧急培训")
    private Integer type;

}
