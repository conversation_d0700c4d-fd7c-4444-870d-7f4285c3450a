package com.logistics.tms.biz.vehicleassetmanagement

import com.logistics.tms.api.feign.personalaccidentinsurance.model.PersonalAccidentInsuranceListResponseModel
import com.logistics.tms.biz.staffvehiclerelation.model.TStaffVehicleRelationModel
import com.logistics.tms.api.feign.vehicleassetmanagement.model.*
import com.logistics.tms.controller.vehicletype.response.VehicleTypeListResponseModel
import com.logistics.tms.controller.vehicleassetmanagement.request.FuzzyQueryVehicleInfoRequestModel
import com.logistics.tms.controller.vehicleassetmanagement.response.FuzzyQueryVehicleInfoResponseModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.biz.vehicleassetmanagement.model.ExportDrivingLicenseReviewModel
import com.logistics.tms.biz.vehicleassetmanagement.model.ExportGpsRecordModel
import com.logistics.tms.biz.vehicleassetmanagement.model.ExportGradeEstimationModel
import com.logistics.tms.biz.vehicleassetmanagement.model.ExportRoadAnnualModel
import com.logistics.tms.biz.vehicleassetmanagement.model.GetVehicleIfCompleteByIdsModel
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicModel
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleDrivingLicenseModel
import com.logistics.tms.controller.vehicleassetmanagement.request.VehicleNoRequestModel
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleRelationInfoModel
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleRoadTransportCertificateModel
import com.logistics.tms.client.BasicDataClient
import com.logistics.tms.controller.vehicleassetmanagement.request.AddOrModifyVehicleBasicInfoRequestModel
import com.logistics.tms.controller.vehicleassetmanagement.request.DeleteVehicleAssetManagementRequest
import com.logistics.tms.controller.vehicleassetmanagement.request.ImportVehicleBasicInfoRequestModel
import com.logistics.tms.controller.vehicleassetmanagement.request.ImportVehicleCertificateRequestModel
import com.logistics.tms.controller.vehicleassetmanagement.request.VehicleAssertOutageCheckRequestModel
import com.logistics.tms.controller.vehicleassetmanagement.request.VehicleAssertOutageRequestModel
import com.logistics.tms.controller.vehicleassetmanagement.request.VehicleAssertRestorationRequestModel
import com.logistics.tms.controller.vehicleassetmanagement.request.VehicleAssertScrapDetailRequestModel
import com.logistics.tms.controller.vehicleassetmanagement.request.VehicleAssetManagementDetailRequestModel
import com.logistics.tms.controller.vehicleassetmanagement.request.VehicleAssetManagementListRequestModel
import com.logistics.tms.controller.vehicleassetmanagement.response.AssetsBoardResponseModel
import com.logistics.tms.controller.vehicleassetmanagement.response.DrivingLicenseAnnualReviewListResponseModel
import com.logistics.tms.controller.vehicleassetmanagement.response.GetGpsInfoByVehicleNoResponseModel
import com.logistics.tms.controller.vehicleassetmanagement.response.ImportVehicleBasicInfoResponseModel
import com.logistics.tms.controller.vehicleassetmanagement.response.VehicleAssertScrapDetailResponseModel
import com.logistics.tms.controller.vehicleassetmanagement.response.VehicleAssetManagementDetailResponseModel
import com.logistics.tms.controller.vehicleassetmanagement.response.VehicleAssetManagementListResponseModel
import com.logistics.tms.controller.vehicleassetmanagement.response.VehicleGpsRecordListResponseModel
import com.logistics.tms.controller.vehicleassetmanagement.response.VehicleGradeEstimationListResponseModel
import com.logistics.tms.controller.vehicleassetmanagement.response.VehicleRoadTransportCertificateListResponseModel
import com.logistics.tms.entity.*
import com.logistics.tms.mapper.*
import com.yelo.tray.core.exception.BizException
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class VehicleAssetManagementBizTest extends Specification {
    @Mock
    TVehicleBasicMapper tqVehicleBasicMapper
    @Mock
    TVehicleDrivingLicenseAnnualReviewMapper tqVehicleDrivingLicenseAnnualReviewMapper
    @Mock
    TVehicleRoadTransportCertificateAnnualReviewMapper tqVehicleRoadTransportCertificateAnnualReviewMapper
    @Mock
    TVehicleGpsRecordMapper tqVehicleGpsRecordMapper
    @Mock
    TVehicleGradeEstimationRecordMapper tqVehicleGradeEstimationRecordMapper
    @Mock
    TCertificationPicturesMapper tqCertificationPicturesMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TVehicleDrivingLicenseMapper tqVehicleDrivingLicenseMapper
    @Mock
    TVehicleRoadTransportCertificateMapper tqVehicleRoadTransportCertificateMapper
    @Mock
    TStaffBasicMapper tqStaffBasicMapper
    @Mock
    TStaffDriverOccupationalRecordMapper tqStaffDriverOccupationalRecordMapper
    @Mock
    TStaffDriverIntegrityExaminationRecordMapper tqStaffDriverIntegrityExaminationRecordMapper
    @Mock
    TStaffDriverContinueLearningRecordMapper tqStaffDriverContinueLearningRecordMapper
    @Mock
    BasicDataClient basicDataClient
    @Mock
    TStaffDriverCredentialMapper tqStaffDriverCredentialMapper
    @Mock
    TDateRemindMapper tqDateRemindMapper
    @Mock
    TInsuranceMapper tqInsuranceMapper
    @Mock
    TPersonalAccidentInsuranceMapper tqPersonalAccidentInsuranceMapper
    @Mock
    TViolationRegulationsMapper tQViolationRegulationsMapper
    @Mock
    TVehicleTypeMapper tQVehicleTypeMapper
    @Mock
    TCarrierOrderMapper tcarrierOrderMapper
    @Mock
    TCompanyCarrierMapper tCompanyCarrierMapper
    @Mock
    TCarrierVehicleRelationMapper tCarrierVehicleRelationMapper
    @Mock
    TStaffVehicleRelationMapper tStaffVehicleRelationMapper
    @Mock
    Logger log
    @InjectMocks
    VehicleAssetManagementBiz vehicleAssetManagementBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "fuzzy Query Vehicle Info where requestModel=#requestModel then expect: #expectedResult"() {
        given:

        expect:
        vehicleAssetManagementBiz.fuzzyQueryVehicleInfo(requestModel).size() == expectedResult

        where:
        requestModel                            || expectedResult
        new FuzzyQueryVehicleInfoRequestModel() || 0
    }

    @Unroll
    def "search Vehicle Basic List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqVehicleBasicMapper.getVehicleBasicByIds(anyString())).thenReturn([new VehicleAssetManagementListResponseModel()])
        when(tqVehicleBasicMapper.getVehicleLicenseComplete(anyString())).thenReturn([new GetVehicleIfCompleteByIdsModel()])
        when(tqVehicleBasicMapper.getVehicleTransportCertificateComplete(anyString())).thenReturn([new GetVehicleIfCompleteByIdsModel()])
        when(tqVehicleBasicMapper.getVehicleGradeEstimationComplete(anyString())).thenReturn([new GetVehicleIfCompleteByIdsModel()])

        expect:
        vehicleAssetManagementBiz.searchVehicleBasicList(requestModel).getList().size() == expectedResult

        where:
        requestModel                                 || expectedResult
        new VehicleAssetManagementListRequestModel() || 1
    }

    @Unroll
    def "search Export Vehicle Info where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqVehicleBasicMapper.getExportGradeEstimationByVehicleIds(anyString())).thenReturn([new ExportGradeEstimationModel()])
        when(tqVehicleBasicMapper.getExportDrivingLicenseReviewByDrivingIds(anyString())).thenReturn([new ExportDrivingLicenseReviewModel()])
        when(tqVehicleBasicMapper.getExportRoadAnnualByRoadIds(anyString())).thenReturn([new ExportRoadAnnualModel()])
        when(tqVehicleBasicMapper.findNewlyInstallTimeGpsRecord(anyString())).thenReturn([new ExportGpsRecordModel()])
        when(tqVehicleRoadTransportCertificateMapper.getByVehicleIds(anyString())).thenReturn([new TVehicleRoadTransportCertificate(vehicleId: 1l, certificationSign: "certificationSign", businessLicenseNumber: "businessLicenseNumber", economicType: "economicType", transportTonnage: 0 as BigDecimal, businessScope: "businessScope", certificationDepartment: "certificationDepartment", issueDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), obtainDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime())])
        when(tQVehicleTypeMapper.searchVehicleType(anyString())).thenReturn([new VehicleTypeListResponseModel()])

        expect:
        vehicleAssetManagementBiz.searchExportVehicleInfo(requestModel).size() == expectedResult

        where:
        requestModel                                 || expectedResult
        new VehicleAssetManagementListRequestModel() || 1
    }

    @Unroll
    def "get Detail List By Id where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqVehicleBasicMapper.getVehicleBasicDetailByCarrierVehicleId(anyLong())).thenReturn(new VehicleAssetManagementDetailResponseModel())
        when(tqVehicleDrivingLicenseAnnualReviewMapper.getDrivingLicenseAnnualReviewByVehicleIds(anyString())).thenReturn([new DrivingLicenseAnnualReviewListResponseModel()])
        when(tqVehicleRoadTransportCertificateAnnualReviewMapper.getVehicleRoadTransportCertificateAnnualReviewByVehicleIds(anyString())).thenReturn([new VehicleRoadTransportCertificateListResponseModel()])
        when(tqVehicleGpsRecordMapper.getVehicleGpsRecordByVehicleIds(anyString())).thenReturn([new VehicleGpsRecordListResponseModel()])
        when(tqVehicleGradeEstimationRecordMapper.getVehicleGradeEstimationRecordByVehicleIds(anyString())).thenReturn([new VehicleGradeEstimationListResponseModel()])

        when:
        vehicleAssetManagementBiz.getDetailListById(requestModel) == expectedResult

        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where:
        requestModel                                   || expectedResult
        new VehicleAssetManagementDetailRequestModel() || "请求参数错误"
    }

    @Unroll
    def "delete List where requestModel=#requestModel"() {
        given:
        when(tqVehicleBasicMapper.getVehicleBasicByIds(anyString())).thenReturn([new VehicleAssetManagementListResponseModel()])
        when(tqVehicleBasicMapper.deleteVehicleBasicInfo(anyString(), anyString(), any())).thenReturn(0)
        when(tqVehicleDrivingLicenseMapper.deleteDrivingLicenseInfo(anyString(), anyString(), any())).thenReturn(0)
        when(tqVehicleRoadTransportCertificateMapper.deleteRoadTransportCertificateInfo(anyString(), anyString(), any())).thenReturn(0)
        when(tqInsuranceMapper.getByVehicleBasicId(anyString())).thenReturn([new TInsurance(statusType: 0, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), personalAccidentInsuranceId: 1l)])
        when(tqPersonalAccidentInsuranceMapper.searchPersonalAccidentInsuranceList(anyString())).thenReturn([new PersonalAccidentInsuranceListResponseModel()])

        when:
        vehicleAssetManagementBiz.deleteList(requestModel)

        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where:
        requestModel                              || expectedResult
        new DeleteVehicleAssetManagementRequest() || "车辆资产基础信息不存在"
    }

    @Unroll
    def "save Vehicle Info where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqVehicleBasicMapper.getVehicleBasicModelById(anyLong())).thenReturn(new VehicleBasicModel())
        when(tqVehicleBasicMapper.getVehicleDrivingLicenseModelById(anyLong())).thenReturn(new VehicleDrivingLicenseModel())
        when(tqVehicleBasicMapper.getVehicleRoadTransportCertificateModelById(anyLong())).thenReturn(new VehicleRoadTransportCertificateModel())
        when(tqVehicleBasicMapper.updateByPrimaryKeySelectiveExt(any())).thenReturn(0)
        when(tqVehicleBasicMapper.getInfoByVehicleNo(anyString())).thenReturn(new TVehicleBasic(usageProperty: 0, ifInstallGps: 0, ifAccessSinopec: 0, vehicleProperty: 0, vehicleOwner: "vehicleOwner", connectTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), connectWay: 0, authenticationStartTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), authenticationExpireTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), registrationCertificationNumber: "registrationCertificationNumber", emissionStandardType: 0))
        when(tqVehicleDrivingLicenseAnnualReviewMapper.batchUpdate(any())).thenReturn(0)
        when(tqVehicleRoadTransportCertificateAnnualReviewMapper.batchUpdate(any())).thenReturn(0)
        when(tqVehicleGpsRecordMapper.batchUpdate(any())).thenReturn(0)
        when(tqVehicleGradeEstimationRecordMapper.batchUpdate(any())).thenReturn(0)
        when(tqCertificationPicturesMapper.batchInsert(any())).thenReturn(0)
        when(tqCertificationPicturesMapper.batchUpdate(any())).thenReturn(0)
        when(tqCertificationPicturesMapper.getUnionFiles(any())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), suffix: "suffix")])
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(commonBiz.getLastDayOfMonth(any())).thenReturn(new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime())
        when(tqVehicleDrivingLicenseMapper.getByVehicleId(anyLong())).thenReturn(new TVehicleDrivingLicense(vehicleId: 1l, vehicleNo: "vehicleNo", vehicleType: 1l, address: "address", owner: "owner", brand: "brand", model: "model", vehicleIdentificationNumber: "vehicleIdentificationNumber", engineNumber: "engineNumber", certificationDepartment: "certificationDepartment", registrationDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), issueDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), filingNumber: "filingNumber", authorizedCarryingCapacity: 0, totalWeight: 0 as BigDecimal, curbWeight: 0 as BigDecimal, tractionMassWeight: 0 as BigDecimal, approvedLoadWeight: 0 as BigDecimal, length: 0, width: 0, height: 0, obsolescenceDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), axleNumber: 0, driveShaftNumber: 0, tiresNumber: 0, plateColor: 0, bodyColor: "bodyColor"))
        when(tqVehicleDrivingLicenseMapper.updateByPrimaryKeySelectiveExt(any())).thenReturn(0)
        when(tqVehicleRoadTransportCertificateMapper.getByVehicleId(anyLong())).thenReturn(new TVehicleRoadTransportCertificate(vehicleId: 1l, certificationSign: "certificationSign", businessLicenseNumber: "businessLicenseNumber", economicType: "economicType", transportTonnage: 0 as BigDecimal, businessScope: "businessScope", certificationDepartment: "certificationDepartment", issueDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), obtainDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), remark: "remark"))
        when(tqVehicleRoadTransportCertificateMapper.updateByPrimaryKeySelectiveExt(any())).thenReturn(0)
        when(tCompanyCarrierMapper.getByName(anyString())).thenReturn(new TCompanyCarrier())
        when(tCarrierVehicleRelationMapper.getByVehicleId(anyLong())).thenReturn(new TCarrierVehicleRelation(companyCarrierId: 1l, vehicleId: 1l))
        when(tStaffVehicleRelationMapper.getRelByVehicleId(anyLong())).thenReturn([new TStaffVehicleRelationModel()])
        when(tStaffVehicleRelationMapper.getRelByTrailerVehicleId(anyLong())).thenReturn([new TStaffVehicleRelationModel()])

        expect:
        vehicleAssetManagementBiz.saveVehicleInfo(requestModel) == expectedResult

        where:
        requestModel                                  || expectedResult
        new AddOrModifyVehicleBasicInfoRequestModel() || null
    }

    @Unroll
    def "check Vehicle Info where upVehicleProperty=#upVehicleProperty and upVehicleType=#upVehicleType and vehicleId=#vehicleId"() {
        given:
        when(tStaffVehicleRelationMapper.getRelByVehicleId(anyLong())).thenReturn([new TStaffVehicleRelationModel()])
        when(tStaffVehicleRelationMapper.getRelByTrailerVehicleId(anyLong())).thenReturn([new TStaffVehicleRelationModel()])

        when:
        vehicleAssetManagementBiz.checkVehicleInfo(vehicleId, upVehicleProperty, upVehicleType)==expectedResult
        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }
        where:
        upVehicleProperty | upVehicleType | vehicleId || expectedResult
        0                 | 1l            | 1l        || "车辆类型不存在"
    }

    @Unroll
    def "import Vehicle Basic Info where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqVehicleBasicMapper.getVehicleBasicModelByIds(anyString())).thenReturn([new VehicleBasicModel()])
        when(tqVehicleBasicMapper.getVehicleDrivingLicenseModelByIds(anyString())).thenReturn([new VehicleDrivingLicenseModel()])
        when(tqVehicleBasicMapper.getVehicleRoadTransportCertificateModelByIds(anyString())).thenReturn([new VehicleRoadTransportCertificateModel()])
        when(tqVehicleBasicMapper.queryVehicleInfoByVehicleNos(anyString())).thenReturn([new FuzzyQueryVehicleInfoResponseModel()])
        when(tqVehicleDrivingLicenseAnnualReviewMapper.countDrivingLicenseByDate(anyLong(), any())).thenReturn(0)
        when(tqVehicleRoadTransportCertificateAnnualReviewMapper.countRoadTransportReviewByDate(anyLong(), any())).thenReturn(0)
        when(tqVehicleGpsRecordMapper.countGpsRecordByDate(anyLong(), any())).thenReturn(0)
        when(tqVehicleGradeEstimationRecordMapper.countGradeEstimationByDate(anyLong(), any())).thenReturn(0)
        when(commonBiz.getQiyaCompanyCarrierId()).thenReturn(1l)
        when(tQVehicleTypeMapper.findListByType(anyString())).thenReturn([new TVehicleType(vehicleType: "vehicleType")])
        when(tCarrierVehicleRelationMapper.getByVehicleId(anyLong())).thenReturn(new TCarrierVehicleRelation(companyCarrierId: 1l, vehicleId: 1l))

        expect:
        vehicleAssetManagementBiz.importVehicleBasicInfo(requestModel) == expectedResult

        where:
        requestModel                             || expectedResult
        new ImportVehicleBasicInfoRequestModel() || new ImportVehicleBasicInfoResponseModel()
    }

    @Unroll
    def "import Vehicle Certificate Info where importModel=#importModel"() {
        given:
        when(tqVehicleBasicMapper.getVehicleRelationInfoByVehicleNo(anyString())).thenReturn([new VehicleRelationInfoModel()])
        when(tqCertificationPicturesMapper.getUnionFiles(any())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), suffix: "suffix")])

        when:
        vehicleAssetManagementBiz.importVehicleCertificateInfo(importModel)
        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }
        where:
        importModel                                || expectedResult
        new ImportVehicleCertificateRequestModel() || "导入失败"
    }

    @Unroll
    def "asset Board"() {
        given:
        when(tqVehicleBasicMapper.getVehicleStatics()).thenReturn(new AssetsBoardResponseModel())
        when(tqVehicleDrivingLicenseAnnualReviewMapper.getDueDrivingLicenseCount(anyInt())).thenReturn(["String": "String"])
        when(tqVehicleRoadTransportCertificateAnnualReviewMapper.getDueRoadTransportCertificateCount(anyInt())).thenReturn(["String": "String"])
        when(tqVehicleGradeEstimationRecordMapper.getDueGradeEstimationCount(anyInt())).thenReturn(["String": "String"])
        when(tqVehicleDrivingLicenseMapper.getDueObsolescenceCount(anyInt())).thenReturn(["String": "String"])
        when(tqStaffBasicMapper.getDriverStatics()).thenReturn(new AssetsBoardResponseModel())
        when(tqStaffBasicMapper.getDueIdentityCount(anyInt())).thenReturn(["String": "String"])
        when(tqStaffDriverOccupationalRecordMapper.getDueOccupationalCount(anyInt())).thenReturn(["String": "String"])
        when(tqStaffDriverIntegrityExaminationRecordMapper.getDueIntegrityExaminationCount(anyInt())).thenReturn(["String": "String"])
        when(tqStaffDriverContinueLearningRecordMapper.getDueContinueLearningCount(anyInt())).thenReturn(["String": "String"])
        when(tqStaffDriverCredentialMapper.getDueDriverCredentialCount(anyInt())).thenReturn(["String": "String"])
        when(tqDateRemindMapper.getAllNeedRemindDate()).thenReturn([new TDateRemind(dateName: "dateName", remindDays: 0)])
        when(tqInsuranceMapper.getOverDueInsurance(any())).thenReturn([new TInsurance(insuranceType: 0, vehicleId: 1l, premium: 0 as BigDecimal, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime())])
        when(tqInsuranceMapper.getAboutToOverDueInsurance(any(), anyInt())).thenReturn([new TInsurance(insuranceType: 0, vehicleId: 1l, premium: 0 as BigDecimal, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime())])
        when(tQViolationRegulationsMapper.getViolationRegulations(anyString())).thenReturn([new TViolationRegulations(vehicleId: 1l, occuranceTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime())])

        expect:
        vehicleAssetManagementBiz.assetBoard() == expectedResult

        where:
        expectedResult << new AssetsBoardResponseModel()
    }

    @Unroll
    def "vehicle Outage where requestModel=#requestModel"() {
        given:
        when(tqCertificationPicturesMapper.batchInsert(any())).thenReturn(0)
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(tqVehicleDrivingLicenseMapper.getByVehicleId(anyLong())).thenReturn(new TVehicleDrivingLicense(vehicleId: 1l, vehicleNo: "vehicleNo"))
        when(tqInsuranceMapper.getByVehicleBasicId(anyString())).thenReturn([new TInsurance(statusType: 0, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), personalAccidentInsuranceId: 1l)])
        when(tqPersonalAccidentInsuranceMapper.searchPersonalAccidentInsuranceList(anyString())).thenReturn([new PersonalAccidentInsuranceListResponseModel()])
        when(tcarrierOrderMapper.findOrderListByHistoryVehicleNo(anyString(), anyLong())).thenReturn([1l])

        when:
        vehicleAssetManagementBiz.vehicleOutage(requestModel)
        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }
        where:
        requestModel                          || expectedResult
        new VehicleAssertOutageRequestModel() || "车辆资产基本信息不存在"
    }

    @Unroll
    def "get Outage Check Info where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqVehicleDrivingLicenseMapper.getByVehicleId(anyLong())).thenReturn(new TVehicleDrivingLicense(vehicleId: 1l, vehicleNo: "vehicleNo"))
        when(tqInsuranceMapper.getByVehicleBasicId(anyString())).thenReturn([new TInsurance(statusType: 0, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), personalAccidentInsuranceId: 1l)])
        when(tqPersonalAccidentInsuranceMapper.searchPersonalAccidentInsuranceList(anyString())).thenReturn([new PersonalAccidentInsuranceListResponseModel()])
        when(tcarrierOrderMapper.findOrderListByHistoryVehicleNo(anyString(), anyLong())).thenReturn([1l])

        when:
        vehicleAssetManagementBiz.getOutageCheckInfo(requestModel) == expectedResult
        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }
        where:
        requestModel                               || expectedResult
        new VehicleAssertOutageCheckRequestModel() || "车辆资产基本信息不存在"
    }

    @Unroll
    def "get Gps Info By Vehicle No where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqVehicleDrivingLicenseMapper.getGpsInfoByVehicleNo(anyString())).thenReturn([new GetGpsInfoByVehicleNoResponseModel()])

        expect:
        vehicleAssetManagementBiz.getGpsInfoByVehicleNo(requestModel).size() == expectedResult

        where:
        requestModel                || expectedResult
        new VehicleNoRequestModel() || 0
    }

    @Unroll
    def "vehicle Scrap Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqVehicleBasicMapper.getVehicleScrapDetail(anyLong())).thenReturn(new VehicleAssertScrapDetailResponseModel())

        when:
        vehicleAssetManagementBiz.vehicleScrapDetail(requestModel) == expectedResult
        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }
        where:
        requestModel                               || expectedResult
        new VehicleAssertScrapDetailRequestModel() || "车辆信息不存在"
    }

    @Unroll
    def "vehicle Restoration where requestModel=#requestModel"() {
        given:
        when(tqCertificationPicturesMapper.delByObjectTypeFileTypeId(anyInt(), anyInt(), anyLong(), anyString())).thenReturn(0)
        when(tqVehicleDrivingLicenseMapper.getByVehicleId(anyLong())).thenReturn(new TVehicleDrivingLicense(vehicleId: 1l, vehicleNo: "vehicleNo"))
        when(tqInsuranceMapper.getByVehicleBasicId(anyString())).thenReturn([new TInsurance(statusType: 0, startTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), endTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 35).getTime(), personalAccidentInsuranceId: 1l)])
        when(tqPersonalAccidentInsuranceMapper.searchPersonalAccidentInsuranceList(anyString())).thenReturn([new PersonalAccidentInsuranceListResponseModel()])
        when(tcarrierOrderMapper.findOrderListByHistoryVehicleNo(anyString(), anyLong())).thenReturn([1l])

        when:
        vehicleAssetManagementBiz.vehicleRestoration(requestModel)
        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }
        where:
        requestModel                                                                                        || expectedResult
        new VehicleAssertRestorationRequestModel() || "车辆资产基本信息不存在"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme