package com.logistics.tms.biz.gpstrack;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.gpstrack.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.ConfigKeyEnum;
import com.logistics.tms.biz.sysconfig.SysConfigBiz;
import com.logistics.tms.mapper.TVehicleGpsMapper;
import com.yelo.basicdata.api.feign.thirdpartclient.ZjxlClient;
import com.yelo.basicdata.api.feign.thirdpartclient.entity.zjxl.OpGpHisTrackRealResponseModel;
import com.yelo.basicdata.api.feign.thirdpartclient.entity.zjxl.OpsGpHisTrackRealRequestModel;
import com.yelo.basicdata.api.feign.thirdpartclient.entity.zjxl.TransBaseResponseModel;
import com.yelo.basicdata.api.feign.thirdpartclient.entity.zjxl.VehicleRequestModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class GpsTrackBiz {

    @Autowired
    private TVehicleGpsMapper tVehicleGpsMapper;
    @Autowired
    private GpsTrackAsyncBiz gpsTrackAsyncBiz;
    @Autowired
    private ZjxlClient zjxlClient;
    @Resource
    private SysConfigBiz sysConfigBiz;

    /**
     * 获取车辆追踪信息列表
     * @param requestModel
     * @return
     */
    public AllVehicleTrackInfoResponseModel getVehicleTrackInfoList(AllVehicleTrackInfoRequestModel requestModel) {
        AllVehicleTrackInfoResponseModel responseModel = tVehicleGpsMapper.getVehicleGpsStatistics();
        requestModel.enablePaging();
        List<VehicleTrackInfoItem> vehicleTrackInfoItemList = tVehicleGpsMapper.getVehicleInfoByCondition(requestModel);
        responseModel.setVehicleTrackInfoList(new PageInfo<>(vehicleTrackInfoItemList));
        return responseModel;
    }

    //check检查车辆是否入网
    public void checkTruckExist(String searchVehicleNo) {
        VehicleRequestModel vehicleRequestModel = new VehicleRequestModel();
        vehicleRequestModel.setVclNo(searchVehicleNo);
        Result<TransBaseResponseModel> result = zjxlClient.checkTruckExist(vehicleRequestModel);
        result.throwException();
        if (CommonConstant.ZERO.equals(result.getData().getResult())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NO_NOT_NET_IN);
        }
    }

    /**
     * 根据车牌号获取调度单和运单地址等详情
     * @param requestModel
     * @return
     */
    public SearchCarrierOrderDestinationByVehicleNoResponseModel getDestinationByVehicleNo(SearchCarrierOrderDestinationByVehicleNoRequestModel requestModel) {
        checkTruckExist(requestModel.getVehicleNo());
        List<SearchCarrierOrderDestinationByVehicleNoResponseModel> carrierOrdersByVehicleNoList = tVehicleGpsMapper.getCarrierOrdersByVehicleNo(requestModel.getVehicleNo());
        SearchCarrierOrderDestinationByVehicleNoResponseModel responseModel;
        if (ListUtils.isNotEmpty(carrierOrdersByVehicleNoList)) {
            responseModel = carrierOrdersByVehicleNoList.get(CommonConstant.INTEGER_ZERO);
        } else {
            responseModel = new SearchCarrierOrderDestinationByVehicleNoResponseModel();
            responseModel.setGpsVehicleNo(requestModel.getVehicleNo());
            responseModel.setCarrierOrderDestinations(new ArrayList<>());
        }
        VehicleLatestPositionModel vehicleLatestPositionAndUpdateLocal = gpsTrackAsyncBiz.getVehicleLatestPositionAndUpdateLocal(requestModel.getVehicleNo());
        responseModel.setVehicleLatestPosition(vehicleLatestPositionAndUpdateLocal);
        if (vehicleLatestPositionAndUpdateLocal != null) {
            responseModel.setUploadTime(new Date(ConverterUtils.toLong(vehicleLatestPositionAndUpdateLocal.getUtc())));
        }
        return responseModel;
    }

    /**
     * 根据车牌号获取车辆历史轨迹
     * @param requestModel
     * @return
     */
    public OpGpHisTrackResponseModel getVehicleTrackHistory(OpGpHisTrackRequestModel requestModel) {
        checkTruckExist(requestModel.getVclNo());
        OpsGpHisTrackRealRequestModel opGpHisTrackMapRequestModel = MapperUtils.mapper(requestModel, OpsGpHisTrackRealRequestModel.class);
        if (opGpHisTrackMapRequestModel.getStopTime() == 0) {
            String configValue = sysConfigBiz.getSysConfig(ConfigKeyEnum.MAP_STOP_TIME_THRESHOLD).orElse("");
            if (StringUtils.isNotBlank(configValue)) {
                opGpHisTrackMapRequestModel.setStopTime(ConverterUtils.toLong(configValue, 5L));
            }
        }
        Result<OpGpHisTrackRealResponseModel> result = zjxlClient.opGpHisTrack(opGpHisTrackMapRequestModel);
        result.throwException();
        OpGpHisTrackResponseModel resultModel = MapperUtils.mapper(result.getData(), OpGpHisTrackResponseModel.class);
        SearchCarrierOrderDestinationByVehicleNoRequestModel model = new SearchCarrierOrderDestinationByVehicleNoRequestModel();
        model.setVehicleNo(requestModel.getVclNo());
        resultModel.setCurrentPosition(getDestinationByVehicleNo(model));
        return resultModel;
    }

    /**
     * 根据车牌号获取车辆历史轨迹
     */
    public void refreshVehicleLocation() {
        try {
            gpsTrackAsyncBiz.pullSyncVehicleLatestInfo();
        } catch (Exception e) {
            log.warn("getAllVehicleTrackInfoResponseModel--从中交兴路拉取车辆信息失败");
        }
    }
}
