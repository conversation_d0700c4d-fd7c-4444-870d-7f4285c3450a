package com.logistics.tms.client.feign.tray.basicdata.commonaddress.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 地址信息响应模型
 */
@Data
@ApiModel("地址信息响应模型")
public class AddressInfoResponseModel {

    @ApiModelProperty("地址ID")
    private Long id;

    @ApiModelProperty("地址编码")
    private String addressCode;

    @ApiModelProperty("地址名称")
    private String addressName;
}