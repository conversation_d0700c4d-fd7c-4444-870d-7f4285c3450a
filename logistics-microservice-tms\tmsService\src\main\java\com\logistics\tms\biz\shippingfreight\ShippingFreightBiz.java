package com.logistics.tms.biz.shippingfreight;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.dispatch.DispatchBiz;
import com.logistics.tms.biz.dispatch.model.DemandOrderModel;
import com.logistics.tms.biz.shippingfreight.model.*;
import com.logistics.tms.controller.companycarrier.request.SearchCompanyCarrierListRequestModel;
import com.logistics.tms.controller.companycarrier.response.SearchCompanyCarrierListResponseModel;
import com.logistics.tms.controller.dispatchorder.request.SearchSpecialDispatchIfMatchFreightDemandReqModel;
import com.logistics.tms.controller.dispatch.request.SearchSpecialDispatchIfMatchFreightReqModel;
import com.logistics.tms.controller.dispatch.response.SearchSpecialDispatchIfMatchFreightRespModel;
import com.logistics.tms.controller.freightconfig.request.AddShippingFreightReqModel;
import com.logistics.tms.controller.freightconfig.request.AssociateCarrierDeleteReqModel;
import com.logistics.tms.controller.freightconfig.request.AssociateCarrierReqModel;
import com.logistics.tms.controller.freightconfig.request.ShippingFreightIdReqModel;
import com.logistics.tms.controller.freightconfig.request.ListShippingFreightListReqModel;
import com.logistics.tms.controller.freightconfig.response.AssociateCarrierListRespModel;
import com.logistics.tms.controller.freightconfig.response.ListShippingFreightListRespModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class ShippingFreightBiz {


    @Resource
    private TShippingFreightMapper tShippingFreightMapper;


    @Resource
    private TCompanyCarrierMapper tCompanyCarrierMapper;

    @Resource
    private CommonBiz commonBiz;

    @Resource
    private TShippingFreightRuleVehicleLengthMapper tShippingFreightRuleVehicleLengthMapper;
    @Resource
    private TDemandOrderMapper tDemandOrderMapper;
    @Resource
    private TShippingFreightAddressMapper tShippingFreightAddressMapper;
    @Resource
    private DispatchBiz dispatchBiz;
    @Resource
    private TShippingFreightRuleCrossPointMapper tShippingFreightRuleCrossPointMapper;




    public PageInfo<ListShippingFreightListRespModel> getList(ListShippingFreightListReqModel reqModel) {
        reqModel.enablePaging();

        List<ListShippingFreightListRespModel> list = tShippingFreightMapper.getList(reqModel);
        if (ListUtils.isNotEmpty(list)) {
            //查询多少车主关联了改id
            List<GetCountCarrierByShippingFreightIdModel> countCarrierByShippingFreightIds = tCompanyCarrierMapper.getCountCarrierByShippingFreightIds(list.stream().map(ListShippingFreightListRespModel::getShippingFreightId).collect(Collectors.toList()));
            Map<Long,GetCountCarrierByShippingFreightIdModel> countMap = new HashMap<>();
            if (ListUtils.isNotEmpty(countCarrierByShippingFreightIds)){
                countCarrierByShippingFreightIds.forEach(e-> countMap.put(e.getShippingFreightId(),e));
            }
            list.forEach(e -> {
                String entrustTypeLabel = e.getEntrustTypeLabel();
                if (StringUtils.isNotEmpty(entrustTypeLabel)) {
                    String[] types = entrustTypeLabel.split(",");
                    StringBuilder typeLabel = new StringBuilder();
                    for (String s : types) {
                        typeLabel.append(EntrustTypeEnum.getEnum(Integer.parseInt(s)).getValue()).append(",");
                    }
                    e.setEntrustTypeLabel(typeLabel.substring(0, typeLabel.length() - 1));
                    if (countMap.get(e.getShippingFreightId())!=null){
                        e.setCompanyCarrierCount(countMap.get(e.getShippingFreightId()).getCarrierCount().toString());
                    }
                }
            });
        }
        return new PageInfo<>(list);
    }




    @Transactional
    public void add(AddShippingFreightReqModel reqModel){
        TShippingFreight dbTShippingFreight = tShippingFreightMapper.getByName(reqModel.getCarrierPriceRuleName());
        if (dbTShippingFreight != null){
            throw new BizException(CarrierDataExceptionEnum.SHIPPING_FREIGHT_NAME_EISXT);
        }
        TShippingFreight insertPO = new TShippingFreight();
        insertPO.setCarrierPriceRuleName(reqModel.getCarrierPriceRuleName());
        insertPO.setEntrustType(StringUtils.listToString(reqModel.getEntrustType(), ','));
        insertPO.setRemark(reqModel.getRemark());
        commonBiz.setBaseEntityAdd(insertPO, BaseContextHandler.getUserName());
        tShippingFreightMapper.insertSelective(insertPO);
    }


    @Transactional
    public void associateCarrier(AssociateCarrierReqModel reqModel){
        List<Long> companyCarrierIds = reqModel.getCompanyCarrierIds();
        List<TCompanyCarrier> carrierMapperByIds = tCompanyCarrierMapper.getByIds(StringUtils.listToString(companyCarrierIds, ','));
        if (ListUtils.isNotEmpty(carrierMapperByIds)){
            List<TCompanyCarrier> exitCarrier = carrierMapperByIds.stream().filter(e -> e.getShippingFreightId() != 0).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(exitCarrier)){
                throw new BizException(CarrierDataExceptionEnum.SHIPPING_FREIGHT_CARRIER_COMPANY_IS_EXIST);
            }
        }
        Long shippingFreightId = reqModel.getShippingFreightId();
        Date now = new Date();
        List<TCompanyCarrier> updatePos = new ArrayList<>();
        reqModel.getCompanyCarrierIds().forEach(e->{
            TCompanyCarrier updatePO = new TCompanyCarrier();
            updatePO.setId(e);
            updatePO.setShippingFreightId(shippingFreightId);
            updatePO.setShippingFreightAddTime(now);
            updatePO.setShippingFreightAddUser(BaseContextHandler.getUserName());
            commonBiz.setBaseEntityModify(updatePO, BaseContextHandler.getUserName());
            updatePos.add(updatePO);
        });

        tCompanyCarrierMapper.batchUpdateSelective(updatePos);
    }



    public PageInfo<AssociateCarrierListRespModel> associateCarrierList(ShippingFreightIdReqModel reqModel){
        List<AssociateCarrierListRespModel> respModels = new ArrayList<>();
        SearchCompanyCarrierListRequestModel requestModel = new SearchCompanyCarrierListRequestModel();
        requestModel.setShippingFreightId(reqModel.getShippingFreightId());
        requestModel.setPageNum(reqModel.getPageNum());
        requestModel.setPageSize(reqModel.getPageSize());
        requestModel.enablePaging();
        requestModel.setIfAllLevel(1);
        requestModel.setShippingFreightAddTimeDesc(1);
        List<SearchCompanyCarrierListResponseModel> companyCarrierList = tCompanyCarrierMapper.getCompanyCarrierList(requestModel);
        if (ListUtils.isNotEmpty(companyCarrierList)){
            companyCarrierList.forEach(e->{
                AssociateCarrierListRespModel respModel = new AssociateCarrierListRespModel();
                respModel.setCompanyCarrierId(e.getCompanyCarrierId());
                if (CompanyTypeEnum.PERSON.getKey().equals(e.getType())) {
                    respModel.setCompanyCarrierName(e.getCarrierContactName()+" "+ e.getCarrierContactPhone());
                }else {
                    respModel.setCompanyCarrierName(e.getCompanyCarrierName());
                }
                respModel.setShippingFreightId(reqModel.getShippingFreightId());
                respModel.setShippingFreightAddUser(e.getShippingFreightAddUser());
                respModel.setShippingFreightAddTime(e.getShippingFreightAddTime());
                respModels.add(respModel);

            });
        }
        PageInfo pageInfo = new PageInfo(companyCarrierList);
        pageInfo.setList(respModels);
        return pageInfo;
    }



    @Transactional
    public void  associateCarrierDelete(AssociateCarrierDeleteReqModel reqModel){
        if (ListUtils.isEmpty(reqModel.getCompanyCarrierIds())){
            throw new BizException(CarrierDataExceptionEnum.SHIPPING_FREIGHT_CARRIER_COMPANY_IDS_NULL);
        }
        List<TCompanyCarrier> updatePos = new ArrayList<>();
        reqModel.getCompanyCarrierIds().forEach(e->{
            TCompanyCarrier updatePO = new TCompanyCarrier();
            updatePO.setId(e);
            updatePO.setShippingFreightId(0L);
            updatePO.setShippingFreightAddTime(null);
            updatePO.setShippingFreightAddUser("");
            commonBiz.setBaseEntityModify(updatePO, BaseContextHandler.getUserName());
            updatePos.add(updatePO);
        });
        tCompanyCarrierMapper.batchUpdateSelective(updatePos);
    }





    /**
     * 零担调度查询是否有匹配的串点和车长费用 2.42
     */
    public SearchSpecialDispatchIfMatchFreightRespModel searchSpecialDispatchIfMatchFreight(SearchSpecialDispatchIfMatchFreightReqModel requestModel) {
        List<SearchSpecialDispatchIfMatchFreightDemandReqModel> demandList = requestModel.getDemandIdList();
        if (demandList.size() > 1) {
            List<SearchSpecialDispatchIfMatchFreightDemandReqModel> nullList = demandList.stream().filter(searchSpecialDispatchIfMatchFreightDemandReqModel -> null == searchSpecialDispatchIfMatchFreightDemandReqModel.getOrderNum()).collect(Collectors.toList());
            demandList = demandList.stream()
                    .filter(searchSpecialDispatchIfMatchFreightDemandReqModel -> null != searchSpecialDispatchIfMatchFreightDemandReqModel.getOrderNum())
                    .sorted(Comparator.comparing(SearchSpecialDispatchIfMatchFreightDemandReqModel::getOrderNum))
                    .collect(Collectors.toList());
            demandList.addAll(nullList);
        }
        String demandOrderIds = demandList.stream().map(item->item.getDemandId().toString()).collect(Collectors.joining(","));

        //查询需求单
        List<DemandOrderModel> demandOrderList = tDemandOrderMapper.getDemandOrderGoodsByDemandIds(demandOrderIds);
        if (ListUtils.isEmpty(demandOrderList)) {
            return new SearchSpecialDispatchIfMatchFreightRespModel();
        }

        Long companyCarrierId = demandOrderList.get(0).getCompanyCarrierId();
        String entrustType = String.valueOf(demandOrderList.get(0).getEntrustType());

        //查询车主表
        List<TCompanyCarrier> tCompanyCarriers = tCompanyCarrierMapper.getByIds(companyCarrierId.toString());
        if (CollectionUtil.isEmpty(tCompanyCarriers)) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }
        TCompanyCarrier tCompanyCarrier = tCompanyCarriers.get(0);
        if (CommonConstant.LONG_ZERO.equals(tCompanyCarrier.getShippingFreightId()) || tCompanyCarrier.getShippingFreightId() == null) {
            //匹配不到
            return new SearchSpecialDispatchIfMatchFreightRespModel();
        }

        //查询运价规则
        TShippingFreight tShippingFreight = tShippingFreightMapper.selectByPrimaryKey(tCompanyCarrier.getShippingFreightId());
        if (tShippingFreight == null) {
            throw new BizException(CarrierDataExceptionEnum.SHIPPING_FREIGHT_RULE_NOT_EXIST);
        }
        List<String> existTypes = Arrays.asList("1,2,3,4,6,7,9,10,12,100,101".split(","));
        List<String> configRange = Arrays.asList(tShippingFreight.getEntrustType().split(","));
        if (configRange.contains(entrustType) || (!existTypes.contains(entrustType) && configRange.contains("-99"))) {
            //匹配到了
        } else {
            //匹配不到
            return new SearchSpecialDispatchIfMatchFreightRespModel();
        }


        SearchSpecialDispatchIfMatchFreightDemandReqModel firstModel = demandList.get(0);
        if (firstModel == null) {
            throw new BizException(CarrierDataExceptionEnum.DEMAND_ORDER_EMPTY);
        }


        //查询运价规则地址
        ShippingFreightRuleSqlConditionModel sqlConditionModel = new ShippingFreightRuleSqlConditionModel();
        sqlConditionModel.setShippingFreightIds(Collections.singletonList(tShippingFreight.getId()));
        sqlConditionModel.setEnabled(EnabledEnum.ENABLED.getKey());
        List<TShippingFreightAddress> tShippingFreightAddresses =  tShippingFreightAddressMapper.listByCondition(sqlConditionModel);

        TShippingFreightAddress matchShippingFreightRule = tShippingFreightAddresses.stream().filter(tShippingFreightAddress ->
                tShippingFreightAddress.getFromProvinceName().equals(firstModel.getLoadProvinceName())
                        && tShippingFreightAddress.getFromCityName().equals(firstModel.getLoadCityName())
                        && tShippingFreightAddress.getFromAreaName().equals(firstModel.getLoadAreaName())

                        && tShippingFreightAddress.getToProvinceName().equals(firstModel.getUnloadProvinceName())
                        && tShippingFreightAddress.getToCityName().equals(firstModel.getUnloadCityName())
                        && tShippingFreightAddress.getToAreaName().equals(firstModel.getUnloadAreaName())).findFirst().orElse(null);
        if (matchShippingFreightRule == null) {
            //匹配不到
            return new SearchSpecialDispatchIfMatchFreightRespModel();
        }

        CalculateShippingFeeReturnModel vehicleLengthPriceModel = this.getVehicleLengthPrice(demandList, requestModel.getVehicleLength(), matchShippingFreightRule);

        //查询串点费用 需求单数量大于1才存在串点费用
        CalculateShippingFeeReturnModel crossPointFeeModel = this.getCrossPointFee(demandList, matchShippingFreightRule);

        //返回参数
        SearchSpecialDispatchIfMatchFreightRespModel respModel = new SearchSpecialDispatchIfMatchFreightRespModel();

        if (vehicleLengthPriceModel.getIfMatch() && crossPointFeeModel.getIfMatch()) {
            respModel.setIfMatchShippingFreightRule(CommonConstant.INTEGER_ONE);
        }else {
            respModel.setIfMatchShippingFreightRule(CommonConstant.INTEGER_ZERO);
        }
        respModel.setCarrierFreight(vehicleLengthPriceModel.getFee());
        respModel.setCrossPointFee(crossPointFeeModel.getFee());
        return respModel;
    }

    private CalculateShippingFeeReturnModel getVehicleLengthPrice(List<SearchSpecialDispatchIfMatchFreightDemandReqModel> demandList, BigDecimal vehicleLength, TShippingFreightAddress matchShippingFreightRule) {

        BigDecimal countSum = demandList.stream().map(SearchSpecialDispatchIfMatchFreightDemandReqModel::getCount).reduce(BigDecimal.ZERO, BigDecimal::add);


        //查询车长费用
        ShippingFreightRuleVehicleLengthSqlConditionModel shippingFreightRuleVehicleLengthSqlConditionModel = new ShippingFreightRuleVehicleLengthSqlConditionModel();
        shippingFreightRuleVehicleLengthSqlConditionModel.setShippingFreightRuleIds(Collections.singletonList(matchShippingFreightRule.getId()));
        List<TShippingFreightRuleVehicleLength> tShippingFreightRuleVehicleLengths = tShippingFreightRuleVehicleLengthMapper.selectListByCondition(shippingFreightRuleVehicleLengthSqlConditionModel);

        //过滤当前车长的费用配置
        List<TShippingFreightRuleVehicleLength> matchRuleVehicleLengths = tShippingFreightRuleVehicleLengths.stream()
                .filter(tShippingFreightRuleVehicleLength -> tShippingFreightRuleVehicleLength.getVehicleLength().compareTo(vehicleLength) == 0)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(matchRuleVehicleLengths)) {
            //匹配不到
            return new CalculateShippingFeeReturnModel();
        }
        //获取匹配的费用 如果没有就取排序最后的费用
        TShippingFreightRuleVehicleLength matchRuleVehicleLength = matchRuleVehicleLengths.stream()
                .filter(tShippingFreightRuleVehicleLength -> countSum.compareTo(tShippingFreightRuleVehicleLength.getCountStart()) > 0 && countSum.compareTo(tShippingFreightRuleVehicleLength.getCountEnd()) <= 0)
                .findFirst()
                .orElse(matchRuleVehicleLengths.stream().max(Comparator.comparing(TShippingFreightRuleVehicleLength::getSort)).orElse(null));
        if (matchRuleVehicleLength == null) {
            //匹配不到
            return new CalculateShippingFeeReturnModel();
        }

        BigDecimal vehicleLengthPrice = BigDecimal.ZERO;
        if (PriceTypeEnum.FIXED_PRICE.getKey().equals(matchRuleVehicleLength.getPriceType())) {
            vehicleLengthPrice = matchRuleVehicleLength.getPrice();
        }else {
            vehicleLengthPrice = matchRuleVehicleLength.getPrice().multiply(countSum);
        }

        CalculateShippingFeeReturnModel returnModel = new CalculateShippingFeeReturnModel();
        returnModel.setFee(vehicleLengthPrice);
        returnModel.setIfMatch(true);
        return returnModel;
    }


    private CalculateShippingFeeReturnModel getCrossPointFee(List<SearchSpecialDispatchIfMatchFreightDemandReqModel> demandList, TShippingFreightAddress matchShippingFreightRule) {
        if (demandList.size() <= 1) {
            return new CalculateShippingFeeReturnModel();
        }

        Map<String, List<SearchSpecialDispatchIfMatchFreightDemandReqModel>> demandListMap = demandList.stream()
                .collect(Collectors.groupingBy(searchSpecialDispatchIfMatchFreightDemandReqModel -> searchSpecialDispatchIfMatchFreightDemandReqModel.getLoadProvinceName() +searchSpecialDispatchIfMatchFreightDemandReqModel.getLoadCityName()+searchSpecialDispatchIfMatchFreightDemandReqModel.getLoadCityName()+searchSpecialDispatchIfMatchFreightDemandReqModel.getLoadDetailAddress() ));


        ShippingFreightRuleCrossPointConditionModel crossPointConditionModel = new ShippingFreightRuleCrossPointConditionModel();
        crossPointConditionModel.setShippingFreightRuleId(matchShippingFreightRule.getId());
        List<TShippingFreightRuleCrossPoint> tShippingFreightRuleCrossPoints = tShippingFreightRuleCrossPointMapper.selectListByCondition(crossPointConditionModel);

        BigDecimal crossPointFee = BigDecimal.ZERO;
        for (Map.Entry<String, List<SearchSpecialDispatchIfMatchFreightDemandReqModel>> longListEntry : demandListMap.entrySet()) {
            List<SearchSpecialDispatchIfMatchFreightDemandReqModel> value = longListEntry.getValue();

            BigDecimal distance_next_point = value.get(0).getNextPointDistance();
            if (distance_next_point == null) {
                return new CalculateShippingFeeReturnModel();
            }
            TShippingFreightRuleCrossPoint shippingFreightRuleCrossPoint = tShippingFreightRuleCrossPoints.stream()
                    .filter(tShippingFreightRuleCrossPoint -> distance_next_point.compareTo(tShippingFreightRuleCrossPoint.getCountStart()) > 0 && distance_next_point.compareTo(tShippingFreightRuleCrossPoint.getCountEnd()) <= 0)
                    .findFirst().orElse(tShippingFreightRuleCrossPoints.stream().max(Comparator.comparing(TShippingFreightRuleCrossPoint::getSort)).orElse(null));
            if (shippingFreightRuleCrossPoint == null) {
                return new CalculateShippingFeeReturnModel();
            }
            crossPointFee = crossPointFee.add(shippingFreightRuleCrossPoint.getPrice());

        }
        CalculateShippingFeeReturnModel returnModel = new CalculateShippingFeeReturnModel();
        returnModel.setFee(crossPointFee);
        returnModel.setIfMatch(true);
        return returnModel;
    }


}
