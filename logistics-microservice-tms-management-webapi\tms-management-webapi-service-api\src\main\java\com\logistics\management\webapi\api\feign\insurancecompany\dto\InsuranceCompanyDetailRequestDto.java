package com.logistics.management.webapi.api.feign.insurancecompany.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Author: sj
 * @Date: 2019/5/29 15:16
 */
@Data
public class InsuranceCompanyDetailRequestDto implements Serializable{
    @ApiModelProperty("保险公司ID")
    @NotBlank(message = "保险公司ID为空")
    private String insuranceCompanyId;
}
