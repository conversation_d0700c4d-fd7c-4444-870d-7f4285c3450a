package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/22
 */
@Data
public class ViewCarrierOrderTicketsResponseModel {

	private Long carrierOrderId;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("车牌号")
	private String vehicleNo;

	@ApiModelProperty("司机")
	private String driverName;

	private String driverMobile;

	@ApiModelProperty("单据信息")
	private List<CarrierOrderTicketsResponseModel> ticketsList;
}
