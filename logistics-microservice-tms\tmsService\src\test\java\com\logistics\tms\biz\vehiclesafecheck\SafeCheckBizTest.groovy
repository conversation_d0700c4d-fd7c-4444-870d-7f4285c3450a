package com.logistics.tms.biz.vehiclesafecheck

import com.logistics.tms.controller.staffvehiclerelation.response.SafeCheckStaffRelResponseModel
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel
import com.logistics.tms.controller.vehiclesafecheck.request.AddReformResultRequestModel
import com.logistics.tms.controller.vehiclesafecheck.request.AddSafeCheckDetailRequestModel
import com.logistics.tms.controller.vehiclesafecheck.request.AddSafeCheckRequestModel
import com.logistics.tms.controller.vehiclesafecheck.request.AppletAddWaitCheckRequestModel
import com.logistics.tms.controller.vehiclesafecheck.request.AppletSafeCheckListRequestModel
import com.logistics.tms.controller.vehiclesafecheck.response.AppletSafeCheckListResponseModel
import com.logistics.tms.controller.vehiclesafecheck.response.AppletSafeCheckSummaryResponseModel
import com.logistics.tms.controller.vehiclesafecheck.request.DelSafeCheckRequestModel
import com.logistics.tms.controller.vehiclesafecheck.request.ReformCheckRequestModel
import com.logistics.tms.controller.vehiclesafecheck.request.SafeCheckBoardRequestModel
import com.logistics.tms.controller.vehiclesafecheck.response.SafeCheckBoardResponseModel
import com.logistics.tms.controller.vehiclesafecheck.request.SafeCheckDetailRequestModel
import com.logistics.tms.controller.vehiclesafecheck.response.SafeCheckDetailResponseModel
import com.logistics.tms.controller.vehiclesafecheck.response.SafeCheckFileResponseModel
import com.logistics.tms.controller.vehiclesafecheck.request.SearchSafeCheckListRequestModel
import com.logistics.tms.controller.vehiclesafecheck.response.SearchSafeCheckListResponseModel
import com.logistics.tms.controller.vehiclesafecheck.response.SummarySafeCheckResponseModel
import com.logistics.tms.base.constant.ConfigKeyConstant
import com.logistics.tms.base.enums.CertificationPicturesFileTypeEnum
import com.logistics.tms.base.enums.CertificationPicturesObjectTypeEnum
import com.logistics.tms.base.enums.CopyFileTypeEnum
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.controller.vehiclesafecheck.request.AppletAddWaitReformRequestModel
import com.logistics.tms.entity.TCertificationPictures
import com.logistics.tms.entity.TVehicleSafeCheckItem
import com.logistics.tms.entity.TVehicleSafeCheckReform
import com.logistics.tms.entity.TVehicleSafeCheckVehicle
import com.logistics.tms.mapper.TCertificationPicturesMapper
import com.logistics.tms.mapper.TStaffVehicleRelationMapper
import com.logistics.tms.mapper.TVehicleBasicMapper
import com.logistics.tms.mapper.TVehicleSafeCheckItemMapper
import com.logistics.tms.mapper.TVehicleSafeCheckMapper
import com.logistics.tms.mapper.TVehicleSafeCheckReformMapper
import com.logistics.tms.mapper.TVehicleSafeCheckVehicleMapper
import com.yelo.basicdata.api.feign.user.UserServiceApi
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class SafeCheckBizTest extends Specification {
    @Mock
    CommonBiz commonBiz
    @Mock
    TVehicleSafeCheckMapper tVehicleSafeCheckMapper
    @Mock
    TVehicleSafeCheckVehicleMapper tVehicleSafeCheckVehicleMapper
    @Mock
    TVehicleSafeCheckItemMapper tVehicleSafeCheckItemMapper
    @Mock
    TVehicleSafeCheckReformMapper tVehicleSafeCheckReformMapper
    @Mock
    TVehicleBasicMapper tVehicleBasicMapper
    @Mock
    TStaffVehicleRelationMapper tStaffVehicleRelationMapper
    @Mock
    TCertificationPicturesMapper tCertificationPicturesMapper
    @Mock
    ConfigKeyConstant configKeyConstant
    @Mock
    UserServiceApi userServiceApi
    @InjectMocks
    SafeCheckBiz safeCheckBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleSafeCheckVehicleMapper.searchList(any())).thenReturn([new SearchSafeCheckListResponseModel()])

        expect:
        safeCheckBiz.searchList(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new SearchSafeCheckListRequestModel() || null
    }

    @Unroll
    def "get List Summary where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleSafeCheckVehicleMapper.getListSummary(any())).thenReturn(new SummarySafeCheckResponseModel())

        expect:
        safeCheckBiz.getListSummary(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new SearchSafeCheckListRequestModel() || new SummarySafeCheckResponseModel()
    }

    @Unroll
    def "add Safe Check where requestModel=#requestModel"() {
        given:
        when(tVehicleSafeCheckMapper.getCountBySpecifyPeriod(anyString())).thenReturn(0)
        when(tVehicleSafeCheckItemMapper.batchInsert(any())).thenReturn(0)
        when(tVehicleBasicMapper.getVehicleNoByIds(anyString())).thenReturn([new VehicleBasicPropertyModel()])
        when(tVehicleBasicMapper.getInternalTractorCount()).thenReturn(0)
        when(tStaffVehicleRelationMapper.findRelList4SafeCheck(anyString())).thenReturn([new SafeCheckStaffRelResponseModel()])

        expect:
        safeCheckBiz.addSafeCheck(requestModel)
        assert expectedResult == false

        where:
        requestModel                   || expectedResult
        new AddSafeCheckRequestModel() || true
    }

    @Unroll
    def "del Safe Check where requestModel=#requestModel"() {
        given:
        when(tVehicleSafeCheckVehicleMapper.delSafeCheck(anyLong(), anyString(), any())).thenReturn(0)
        when(tVehicleSafeCheckVehicleMapper.getListByCheckId(anyLong())).thenReturn([new TVehicleSafeCheckVehicle(safeCheckId: 1l)])

        expect:
        safeCheckBiz.delSafeCheck(requestModel)
        assert expectedResult == false

        where:
        requestModel                   || expectedResult
        new DelSafeCheckRequestModel() || true
    }

    @Unroll
    def "get Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleSafeCheckVehicleMapper.getDetail(anyLong(), anyInt())).thenReturn(new SafeCheckDetailResponseModel())
        when(tCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(filePath: "filePath")])

        expect:
        safeCheckBiz.getDetail(requestModel) == expectedResult

        where:
        requestModel                      || expectedResult
        new SafeCheckDetailRequestModel() || new SafeCheckDetailResponseModel()
    }

    @Unroll
    def "get File List where checkReformId=#checkReformId and fileType=#fileType and objectType=#objectType then expect: #expectedResult"() {
        given:
        when(tCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(filePath: "filePath")])

        expect:
        safeCheckBiz.getFileList(checkReformId, objectType, fileType) == expectedResult

        where:
        checkReformId | fileType | objectType || expectedResult
        1l            | 0        | 0          || [new SafeCheckFileResponseModel()]
    }

    @Unroll
    def "add Safe Check Detail where requestModel=#requestModel"() {
        given:
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(tVehicleSafeCheckItemMapper.batchUpdate(any())).thenReturn(0)
        when(tVehicleSafeCheckItemMapper.getListBySafeCheckVehicleId(anyLong())).thenReturn([new TVehicleSafeCheckItem(itemType: 0, status: 0)])
        when(tCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 38).getTime(), suffix: "suffix")])
        when(tCertificationPicturesMapper.batchInsert(any())).thenReturn(0)
        when(tCertificationPicturesMapper.updateFilePath(any())).thenReturn(0)

        expect:
        safeCheckBiz.addSafeCheckDetail(requestModel)
        assert expectedResult == false

        where:
        requestModel                         || expectedResult
        new AddSafeCheckDetailRequestModel() || true
    }

    @Unroll
    def "add Reform Result where requestModel=#requestModel"() {
        given:
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(tVehicleSafeCheckReformMapper.getBySafeCheckVehicleId(anyLong())).thenReturn(new TVehicleSafeCheckReform(safeCheckVehicleId: 1l, reformResult: "reformResult", addReformResultTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 38).getTime()))
        when(tCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 38).getTime(), suffix: "suffix")])
        when(tCertificationPicturesMapper.batchInsert(any())).thenReturn(0)
        when(tCertificationPicturesMapper.updateFilePath(any())).thenReturn(0)

        expect:
        safeCheckBiz.addReformResult(requestModel)
        assert expectedResult == false

        where:
        requestModel                      || expectedResult
        new AddReformResultRequestModel() || true
    }

    @Unroll
    def "reform Check where requestModel=#requestModel"() {
        given:
        when(tVehicleSafeCheckItemMapper.batchUpdate(any())).thenReturn(0)
        when(tVehicleSafeCheckItemMapper.getListBySafeCheckVehicleId(anyLong())).thenReturn([new TVehicleSafeCheckItem(status: 0)])
        when(tVehicleSafeCheckReformMapper.getBySafeCheckVehicleId(anyLong())).thenReturn(new TVehicleSafeCheckReform())
        when(tCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures()])
        when(tCertificationPicturesMapper.batchUpdate(any())).thenReturn(0)

        expect:
        safeCheckBiz.reformCheck(requestModel)
        assert expectedResult == false

        where:
        requestModel                  || expectedResult
        new ReformCheckRequestModel() || true
    }

    @Unroll
    def "add List where operatorTime=#operatorTime and source=#source and operatorName=#operatorName and updateList=#updateList"() {
        expect:
        safeCheckBiz.addList(source, updateList, operatorName, operatorTime)
        assert expectedResult == false

        where:
        operatorTime                                                     | source                         | operatorName   | updateList                     || expectedResult
        new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 38).getTime() | [new TCertificationPictures()] | "operatorName" | [new TCertificationPictures()] || true
    }

    @Unroll
    def "save Or Update File where objectTypeEnum=#objectTypeEnum and pageFileList=#pageFileList and fileTypeEnum=#fileTypeEnum and catalog=#catalog and objectId=#objectId and copyFileTypeEnum=#copyFileTypeEnum"() {
        given:
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(tCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 38).getTime(), suffix: "suffix")])
        when(tCertificationPicturesMapper.batchInsert(any())).thenReturn(0)
        when(tCertificationPicturesMapper.updateFilePath(any())).thenReturn(0)

        expect:
        safeCheckBiz.saveOrUpdateFile(objectId, objectTypeEnum, fileTypeEnum, copyFileTypeEnum, catalog, pageFileList)
        assert expectedResult == false

        where:
        objectTypeEnum                                               | pageFileList | fileTypeEnum                                                       | catalog   | objectId | copyFileTypeEnum                         || expectedResult
        CertificationPicturesObjectTypeEnum.OBJECT_TYPE_DEFAULT_ENUM | ["String"]   | CertificationPicturesFileTypeEnum.CERTIFICATION_PICTURES_NULL_ENUM | "catalog" | 1l       | CopyFileTypeEnum.COMPANY_CARRIER_TRADING || true
    }

    @Unroll
    def "get Safe Check Board Info where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleSafeCheckVehicleMapper.getSafeCheckBoardInfo(any())).thenReturn([new SafeCheckBoardResponseModel()])

        expect:
        safeCheckBiz.getSafeCheckBoardInfo(requestModel) == expectedResult

        where:
        requestModel                     || expectedResult
        new SafeCheckBoardRequestModel() || [new SafeCheckBoardResponseModel()]
    }

    @Unroll
    def "search Applet List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)
        when(tVehicleSafeCheckVehicleMapper.searchAppletList(any())).thenReturn([new AppletSafeCheckListResponseModel()])

        expect:
        safeCheckBiz.searchAppletList(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new AppletSafeCheckListRequestModel() || null
    }

    @Unroll
    def "get Applet Summary where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)
        when(tVehicleSafeCheckVehicleMapper.getAppletSummary(any())).thenReturn(new AppletSafeCheckSummaryResponseModel())

        expect:
        safeCheckBiz.getAppletSummary(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new AppletSafeCheckListRequestModel() || new AppletSafeCheckSummaryResponseModel()
    }

    @Unroll
    def "submit Wait Check where requestModel=#requestModel"() {
        given:
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(tCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 38).getTime(), suffix: "suffix")])
        when(tCertificationPicturesMapper.batchInsert(any())).thenReturn(0)
        when(tCertificationPicturesMapper.updateFilePath(any())).thenReturn(0)

        expect:
        safeCheckBiz.submitWaitCheck(requestModel)
        assert expectedResult == false

        where:
        requestModel                         || expectedResult
        new AppletAddWaitCheckRequestModel() || true
    }

    @Unroll
    def "submit Wait Reform where requestModel=#requestModel"() {
        given:
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(tVehicleSafeCheckReformMapper.getBySafeCheckVehicleId(anyLong())).thenReturn(new TVehicleSafeCheckReform())
        when(tCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 38).getTime(), suffix: "suffix")])
        when(tCertificationPicturesMapper.batchInsert(any())).thenReturn(0)
        when(tCertificationPicturesMapper.updateFilePath(any())).thenReturn(0)

        expect:
        safeCheckBiz.submitWaitReform(requestModel)
        assert expectedResult == false

        where:
        requestModel                                                                             || expectedResult
        new AppletAddWaitReformRequestModel() || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme