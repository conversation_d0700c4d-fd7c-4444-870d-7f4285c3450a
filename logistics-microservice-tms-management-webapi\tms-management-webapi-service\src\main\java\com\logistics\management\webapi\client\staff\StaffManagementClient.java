package com.logistics.management.webapi.client.staff;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.staff.hystrix.StaffManagementClientHystrix;
import com.logistics.management.webapi.client.staff.request.*;
import com.logistics.management.webapi.client.staff.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * @author: wjf
 * @date: 2024/3/20 9:45
 */
@FeignClient(name = FeignClientName.TMS_SERVICES, 
        path = "/service/staffmanagement",
        fallback = StaffManagementClientHystrix.class)
public interface StaffManagementClient {

    /**
     * 模糊查询司机信息
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "模糊查询司机信息")
    @PostMapping(value = "/fuzzyQueryDriverInfo")
    Result<PageInfo<FuzzyQueryDriverInfoFeignResponseModel>> fuzzyQueryDriverInfo(@RequestBody FuzzyQueryDriverInfoFeignRequestModel requestModel);

    
}
