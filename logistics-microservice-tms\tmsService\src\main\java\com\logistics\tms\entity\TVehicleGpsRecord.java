package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleGpsRecord extends BaseEntity {
    /**
    * 车辆ID
    */
    @ApiModelProperty("车辆ID")
    private Long vehicleId;

    /**
    * 安装日期
    */
    @ApiModelProperty("安装日期")
    private Date installTime;

    /**
    * 终端型号
    */
    @ApiModelProperty("终端型号")
    private String terminalType;

    /**
    * SIM卡号
    */
    @ApiModelProperty("SIM卡号")
    private String simNumber;

    /**
    * 服务提供商
    */
    @ApiModelProperty("服务提供商")
    private String gpsServiceProvider;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}