package com.logistics.management.webapi.api.feign.freight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 运价地址详情规则
 * @Author: sj
 * @Date: 2019/12/24 13:59
 */
@Data
public class FreightAddressRuleDetailResponseDto {
    @ApiModelProperty("运价地址ID")
    private String freightAddressId = "";
    @ApiModelProperty("运价Id")
    private String freightId = "";
    @ApiModelProperty("仓库ID")
    private String warehouseId = "";
    @ApiModelProperty("仓库名称")
    private String warehouseName = "";
    @ApiModelProperty("发货省ID")
    private String fromProvinceId = "";
    @ApiModelProperty("发货省名称")
    private String fromProvinceName = "";
    @ApiModelProperty("发货市ID")
    private String fromCityId = "";
    @ApiModelProperty("发货市名称")
    private String fromCityName = "";
    @ApiModelProperty("发货区ID")
    private String fromAreaId = "";
    @ApiModelProperty("发货区名称")
    private String fromAreaName = "";
    @ApiModelProperty("卸货省ID")
    private String toProvinceId = "";
    @ApiModelProperty("卸货省名称")
    private String toProvinceName = "";
    @ApiModelProperty("卸货市ID")
    private String toCityId = "";
    @ApiModelProperty("卸货市名称")
    private String toCityName ="";
    @ApiModelProperty("卸货区ID")
    private String toAreaId = "";
    @ApiModelProperty("卸货区名称")
    private String toAreaName = "";
    @ApiModelProperty("运价规则阶梯")
    private List<FreightAddressRuleDto> freightAddressRuleList = new ArrayList<>();
    @ApiModelProperty("运价多装多卸(车主)")
    private List<FreightAddressRuleMarkupDto> freightAddressRuleMarkupList = new ArrayList<>();
}
