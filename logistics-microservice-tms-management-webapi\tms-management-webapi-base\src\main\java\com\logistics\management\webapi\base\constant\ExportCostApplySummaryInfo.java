package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

@Deprecated
public class ExportCostApplySummaryInfo {
    private ExportCostApplySummaryInfo() {
    }

    private static final Map<String, String> DRIVER_COST_APPLY_SUMMARY_INFO;

    static {
        DRIVER_COST_APPLY_SUMMARY_INFO = new LinkedHashMap<>();
        DRIVER_COST_APPLY_SUMMARY_INFO.put("司机", "exportStaffName");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("发生时间", "occurrenceTime");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("司机机构", "staffPropertyLabel");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("合计费用（元）", "costTotal");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("住宿费（元）", "accommodationFee");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("装卸费（元）", "loadingFee");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("用餐费（元）", "mealFee");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("加班费（元）", "overtimePay");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("劳保费（元）", "laborSecurityExpense");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("电话费（元）", "telephoneCharges");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("交通费（元）", "transportationFee");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("维修费（元）", "maintenanceCost");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("车辆保养费（元）", "vehicleMaintenanceCost");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("过路过桥费（元）", "tollFee");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("停车费（元）", "parkingFee");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("尿素费（元）", "ureaFee");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("加油费（元）", "oilFee");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("核酸检测费（元）", "nucleicAcidTestingFee");
        DRIVER_COST_APPLY_SUMMARY_INFO.put("医疗防护用品（元）", "medicalSuppliesFee");

    }

    public static Map<String, String> getDriverCostApplySummaryInfo() {
        return DRIVER_COST_APPLY_SUMMARY_INFO;
    }
}

