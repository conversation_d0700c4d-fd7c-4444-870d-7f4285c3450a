package com.logistics.tms.controller.reservationorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ReservationOrderSearchListForManagementWebResModel {

    /**
     * 预约单id
     */
    @ApiModelProperty(value = "预约单id")
    private Long id;


    private String companyCarrierName;

    private Integer companyCarrierType;

    private String carrierContactName;

    private String carrierContactMobile;



    /**
     * 运单号
     */
    @ApiModelProperty(value = "运单号")
    private String carrierOrderCode = "";

    /**
     * 预约单号
     */
    @ApiModelProperty(value = "预约单号")
    private String reservationCode = "";

    /**
     * 预约类型：1 提货，2 卸货
     */
    @ApiModelProperty(value = "预约类型：1 提货，2 卸货")
    private Integer reservationType;


    /**
     * 预约类型：1 提货，2 卸货
     */
    @ApiModelProperty(value = "预约类型文本")
    private String reservationTypeLabel = "";

    /**
     * 状态 1待签到 2已签到 3已失效
     */
    @ApiModelProperty(value = "状态 1待签到 2已签到 3已失效")
    private Integer state;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态文本")
    private String stateStr = "";

    /**
     * 车牌号
     */
    @ApiModelProperty("车牌号")
    private String vehicleNo = "";

    /**
     * 仓库
     */
    @ApiModelProperty("仓库")
    private String warehouseName = "";


    /**
     * 仓库地址
     */
    @ApiModelProperty("仓库地址")
    private String warehouseAddress = "";


    /**
     * 预约方式 0.小程序 1.微信 2.车主
     */
    @ApiModelProperty(value = "预约方式 0.小程序 1.微信 2.车主")
    private Integer reservationSource;


    /**
     * 预约方式文本
     */
    @ApiModelProperty(value = "预约方式文本")
    private String reservationSourceLabel = "";


    /**
     * 预约方式 0.小程序 1.微信 2.车主
     */
    @ApiModelProperty(value = "预约角色0.司机 1.访客 2.车主")
    private Integer reservationRole;

    /**
     * 预约角色文本
     */
    @ApiModelProperty(value = "预约角色文本")
    private String reservationRoleLabel = "";

    /**
     * 预约司机  （访客得时候显示 预约填写得手机号）
     */
    @ApiModelProperty(value = "预约司机")
    private String driver = "";


    /**
     * 预约人身份证号
     */
    @ApiModelProperty(value = "预约人身份证号")
    private String identityCardNumber = "";

    /**
     * 预约时间段 start
     */
    @ApiModelProperty(value = "预约时间段 start")
    private Date reservationStartTime ;
    /**
     * 预约时间段 end
     */
    @ApiModelProperty(value = "预约时间段 end")
    private Date reservationEndTime ;
    /**
     /**
     * 预约数量
     */
    @ApiModelProperty(value = "预约数量")
    private BigDecimal reservationCount;
    /**
     * 签到时间
     */
    @ApiModelProperty(value = "签到时间")
    private Date signInTime ;


    /**
     * 预约日期
     */
    @ApiModelProperty(value = "预约日期")
    private Date createdTime ;


}
