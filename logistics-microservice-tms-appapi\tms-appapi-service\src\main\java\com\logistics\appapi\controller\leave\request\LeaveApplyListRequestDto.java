package com.logistics.appapi.controller.leave.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 请假记录列表请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/14
 */
@Data
public class LeaveApplyListRequestDto extends AbstractPageForm<LeaveApplyListRequestDto> {

	@ApiModelProperty(value = "请假日期(月份)", required = true)
	@NotBlank(message = "请选择要查询的月份")
	private String leaveDate;
}
