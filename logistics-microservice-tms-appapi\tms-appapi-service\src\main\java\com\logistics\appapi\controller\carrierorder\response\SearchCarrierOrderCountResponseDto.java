package com.logistics.appapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2018/10/16 14:34
 */
@Data
public class SearchCarrierOrderCountResponseDto {
    @ApiModelProperty("合计数量")
    private String allCount = "";
    @ApiModelProperty("待提货数量")
    private String waitLoadCount = "";
    @ApiModelProperty("待卸货数量")
    private String waitUnloadCount = "";
    @ApiModelProperty("待签收数量")
    private String waitSignUpCount = "";
}
