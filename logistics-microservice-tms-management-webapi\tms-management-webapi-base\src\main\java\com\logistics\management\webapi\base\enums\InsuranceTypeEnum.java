package com.logistics.management.webapi.base.enums;


public enum InsuranceTypeEnum {
    DEFAULT(0,"","0"),
    POLICY(1,"保单","1"),
    BATCH(2,"批单","2"),
    ;

    private Integer key;
    private String value;
    private String keyStr;

    InsuranceTypeEnum(Integer key, String value, String keyStr) {
        this.key = key;
        this.value = value;
        this.keyStr = keyStr;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getKeyStr() {
        return keyStr;
    }

    public static InsuranceTypeEnum getEnum(Integer key) {
        for (InsuranceTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
