package com.logistics.tms.controller.staff.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SearchStaffManagementListResponseModel {
    @ApiModelProperty("车主司机关联id")
    private Long carrierDriverId;
    @ApiModelProperty("人员基本信息表ID")
    private Long staffId;
    @ApiModelProperty("人员机构 1 自主，2 外部，3 自营")
    private Integer staffProperty;
    @ApiModelProperty("司机账号开通状态 0 待开通 1 已开通 2 已关闭")
    private Integer openStatus;
    @ApiModelProperty("人员类别 1 驾驶员 2 押运员 3 驾驶员&押运员")
    private Integer staffType;
    @ApiModelProperty("性别")
    private Integer gender;
    @ApiModelProperty("从业资格证号")
    private String occupationalRequirementsCredentialNo;
    @ApiModelProperty("人员姓名")
    private String staffName;
    @ApiModelProperty("人员手机号")
    private String staffMobile;
    @ApiModelProperty("准驾车型")
    private String permittedType;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("新增人")
    private String createdBy;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;
    @ApiModelProperty("证件是否期权")
    private Integer ifComplete;

    @ApiModelProperty("是否我司: 1:我司 2:其他车主")
    private Integer isOurCompany;

    @ApiModelProperty("车主id")
    private Long companyCarrierId;

    @ApiModelProperty(value = "车主名称")
    private String companyCarrierName;

    @ApiModelProperty(value = "车主公司类型：1 公司，2 个人")
    private Integer companyCarrierType;

    @ApiModelProperty("车主账号名称")
    private String carrierContactName;

    @ApiModelProperty("车主账号手机号（原长度50）")
    private String carrierContactPhone;

    @ApiModelProperty("实名认证(个人): 0 待实名 1 实名中 2 已实名")
    private Integer realNameAuthenticationStatus;

    @ApiModelProperty("仓库权限开关, 0:关 1:开")
    private Integer warehouseSwitch;
}
