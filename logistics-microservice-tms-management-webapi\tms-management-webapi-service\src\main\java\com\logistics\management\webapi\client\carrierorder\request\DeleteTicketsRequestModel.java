package com.logistics.management.webapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2018/11/6 11:12
 */
@Data
public class DeleteTicketsRequestModel {

    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    @ApiModelProperty("图片Id")
    private Long imageId;

    @ApiModelProperty("请求来源：1 后台，2 前台")
    private Integer source;
}
