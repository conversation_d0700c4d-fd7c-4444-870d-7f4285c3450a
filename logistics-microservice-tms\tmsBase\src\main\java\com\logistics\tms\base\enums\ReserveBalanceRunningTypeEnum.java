package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@Getter
@AllArgsConstructor
public enum ReserveBalanceRunningTypeEnum {

    RECHARGE_TYPE(10, "充值"),
    RED_CHARGE_REFUND_TYPE(11, "红冲退款"),
    WRITE_OFF_TYPE(20, "已冲销"),
    ADVANCE_TYPE(21, "垫付"),
    DEDUCTIONS_TYPE(22, "扣款"),
    ;

    private final Integer key;
    private final String value;

    public boolean isRecharge() {
        return isRecharge(this);
    }

    public boolean isDeductions() {
        return !isRecharge();
    }

    public static boolean isRecharge(ReserveBalanceRunningTypeEnum typeEnum) {
        return List.of(RECHARGE_TYPE, RED_CHARGE_REFUND_TYPE).contains(typeEnum);
    }
}
