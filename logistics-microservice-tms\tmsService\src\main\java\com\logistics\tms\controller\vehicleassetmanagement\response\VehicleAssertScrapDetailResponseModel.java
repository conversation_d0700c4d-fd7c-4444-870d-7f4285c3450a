package com.logistics.tms.controller.vehicleassetmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class VehicleAssertScrapDetailResponseModel {
    @ApiModelProperty("车辆基本信息ID")
    private Long vehicleBasicId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty(value = "停运原因类型：2 停运, 3 过户，4 报废")
    private Integer outageType;
    @ApiModelProperty("车辆停运原因")
    private String outageInfo;
    @ApiModelProperty("过户/报废证明")
    private List<String> pathList;
}
