package com.logistics.management.webapi.api.feign.extvehiclesettlement.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/11/20 13:53
 */
@Data
public class ExtVehicleSettlementDetailResponseDto {
    @ApiModelProperty("外部车辆结算ID")
    private String extVehicleSettlementId="";
    @ApiModelProperty("运单号")
    private String carrierOrderCode="";
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty("结算数据")
    private String settlementAmount="";
    @ApiModelProperty("司机合计费用")
    private String driverTotalFee="";
    @ApiModelProperty("付款通道")
    private String paymentChannel="";
    @ApiModelProperty("付款单号")
    private String paymentNo="";
    @ApiModelProperty("应支付费用")
    private String paymentFee="";
    @ApiModelProperty("报销费用")
    private String reimburseFee="";
    @ApiModelProperty("合计总费用")
    private String totalFee="";
    @ApiModelProperty("备注")
    private String remark="";
    @ApiModelProperty("附件")
    private List<String> attachmentList;
}
