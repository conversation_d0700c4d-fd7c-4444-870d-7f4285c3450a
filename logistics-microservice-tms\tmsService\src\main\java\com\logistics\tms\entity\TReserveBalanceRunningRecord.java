package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2023/07/31
*/
@Data
public class TReserveBalanceRunningRecord extends BaseEntity {
    /**
    * 备用金余额id
    */
    @ApiModelProperty("备用金余额id")
    private Long reserveBalanceId;

    /**
    * 流水日期
    */
    @ApiModelProperty("流水日期")
    private Date runningDate;

    /**
    * 流水类型：10 充值，11 红冲退款，20 已冲销，21 垫付, 22 扣款
    */
    @ApiModelProperty("流水类型：10 充值，11 红冲退款，20 已冲销，21 垫付, 22 扣款")
    private Integer runningType;

    /**
    * 金额
    */
    @ApiModelProperty("金额")
    private BigDecimal amount;
}