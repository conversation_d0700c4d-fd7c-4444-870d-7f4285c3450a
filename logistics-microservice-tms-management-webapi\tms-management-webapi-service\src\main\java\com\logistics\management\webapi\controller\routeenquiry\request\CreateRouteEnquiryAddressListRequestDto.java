package com.logistics.management.webapi.controller.routeenquiry.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * @author: wjf
 * @date: 2024/7/8 17:35
 */
@Data
public class CreateRouteEnquiryAddressListRequestDto {

    //发货地
    /**
     * 发货地-类型：1 社会业务，2 生产企业
     */
    @NotBlank(message = "请选择发货地类型")
    @Pattern(regexp = "^([12])$", message = "请选择发货地类型")
    private String fromAddressType;

    /**
     * 发货地-仓库
     */
    private String fromWarehouse;

    /**
     * 发货地-省
     */
    @NotBlank(message = "请选择发货地")
    private String fromProvinceId;
    /**
     * 发货地-省
     */
    @NotBlank(message = "请选择发货地")
    private String fromProvinceName;

    /**
     * 发货地-市
     */
    @NotBlank(message = "请选择发货地")
    private String fromCityId;
    /**
     * 发货地-市
     */
    @NotBlank(message = "请选择发货地")
    private String fromCityName;

    /**
     * 发货地-区
     */
    @NotBlank(message = "请选择发货地")
    private String fromAreaId;
    /**
     * 发货地-区
     */
    @NotBlank(message = "请选择发货地")
    private String fromAreaName;


    //收货地
    /**
     * 收货地-省
     */
    @NotBlank(message = "请选择收货地")
    private String toProvinceId;
    /**
     * 收货地-省
     */
    @NotBlank(message = "请选择收货地")
    private String toProvinceName;

    /**
     * 收货地-市
     */
    @NotBlank(message = "请选择收货地")
    private String toCityId;
    /**
     * 收货地-市
     */
    @NotBlank(message = "请选择收货地")
    private String toCityName;

    /**
     * 收货地-区
     */
    @NotBlank(message = "请选择收货地")
    private String toAreaId;
    /**
     * 收货地-区
     */
    @NotBlank(message = "请选择收货地")
    private String toAreaName;

}
