package com.logistics.tms.controller.reservationorder.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/19 09:48
 */
@Data
public class ReservationOrderSummaryResponseModel {

    /**
     * 待预约-提货订单数量
     */
    private Integer loadOrderCount=0;

    /**
     * 待预约-卸货订单数量
     */
    private Integer unloadOrderCount=0;

    /**
     * 已预约-列表
     */
    private List<ReservationOrderSummaryListResponseModel> summaryList=new ArrayList<>();

}
