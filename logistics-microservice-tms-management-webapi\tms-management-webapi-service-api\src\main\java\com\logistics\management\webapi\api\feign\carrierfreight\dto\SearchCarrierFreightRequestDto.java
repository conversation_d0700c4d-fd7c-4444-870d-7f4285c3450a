package com.logistics.management.webapi.api.feign.carrierfreight.dto;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询车主运价列表
 *
 * <AUTHOR>
 * @date 2022/9/1 14:52
 */
@Data
public class SearchCarrierFreightRequestDto extends AbstractPageForm<SearchCarrierFreightRequestDto> {

    @ApiModelProperty("公司名称或个人姓名手机号")
    private String companyCarrierName;
}
