package com.logistics.tms.biz.workgroup.sendmsg.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2021/9/17 14:11
 */
@Data
public class WorkGroupCarrierOrderForLeYiBoModel {

    @ApiModelProperty("运单状态")
    private String statusDesc = "";

    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";

    @ApiModelProperty("纠错状态")
    private String correctStatusDesc="";

    @ApiModelProperty("出库状态展示文本")
    private String outStatusLabel = "";

    @ApiModelProperty("需求单号")
    private String demandOrderCode = "";

    @ApiModelProperty("车牌号")
    private String vehicleNo = "";

    @ApiModelProperty("司机")
    private String driver = "";

    @ApiModelProperty("司机身份证号")
    private String driverIdentity = "";

    @ApiModelProperty("1.3.2新增；时效要求")
    private String recycleTaskTypeLabel = "";

    @ApiModelProperty("司机运费")
    private String dispatchFreightFee="";

    //发货地址
    @ApiModelProperty("发货地址")
    private String loadAddress = "";
    @ApiModelProperty("发货人")
    private String consignor = "";
    @ApiModelProperty("期望提货时间")
    private String expectedLoadTime = "";

    @ApiModelProperty("预计承运数量")
    private String expectAmount = "";
    @ApiModelProperty("实际提货数量")
    private String loadAmount = "";
    @ApiModelProperty("实际卸货数量")
    private String unloadAmount = "";
    @ApiModelProperty("实际签收数量")
    private String signAmount = "";

    @ApiModelProperty("临时费用")
    private String otherFee = "";

    @ApiModelProperty("实际货主运费")
    private String entrustFreight = "";

    @ApiModelProperty("车主费用合计")
    private String carrierPriceTotal = "";

    @ApiModelProperty("实际车主费用合计(加上临时费用的)")
    private String actualCarrierPriceTotal = "";

    @ApiModelProperty("差异数量（实际提货数量-预计数量）")
    private String differenceAmount = "";

    @ApiModelProperty("云仓异常数")
    private String abnormalAmount = "";

    @ApiModelProperty("回退数量（实际提货数量-预计提货数量）")
    private String backAmount = "";

    @ApiModelProperty("预计提货时间")
    private String expectLoadTime = "";

    @ApiModelProperty("预计到货时间")
    private String expectArrivalTime = "";


    //收货地址
    @ApiModelProperty("收货地址")
    private String unloadAddress = "";
    @ApiModelProperty("收货人")
    private String receiver = "";
    @ApiModelProperty("期望到货时间")
    private String expectedUnloadTime = "";

    @ApiModelProperty("预计里程数")
    private String expectMileage="";

    @ApiModelProperty("实际提货时间")
    private String loadTime = "";

    @ApiModelProperty("实际到货时间")
    private String unloadTime = "";

    @ApiModelProperty("实际签收时间")
    private String signTime = "";

    @ApiModelProperty("品名")
    private String goodsName = "";

    @ApiModelProperty("备注")
    private String remark = "";

    @ApiModelProperty("需求类型")
    private String entrustType="";

    @ApiModelProperty("下单部门")
    private String publishOrgName = "";

    @ApiModelProperty("调度人")
    private String dispatchUserName = "";

    @ApiModelProperty("车主")
    private String carrierCompany = "";

    @ApiModelProperty("货主")
    private String entrustCompany = "";

    @ApiModelProperty("客户单号")
    private String customerOrderCode = "";

    @ApiModelProperty("运单生成时间")
    private String dispatchTime = "";

    @ApiModelProperty("调度单号")
    private String dispatchOrderCode = "";

    @ApiModelProperty("大区")
    private String loadRegionName="";

    @ApiModelProperty("大区负责人")
    private String loadRegionContactName = "";

    @ApiModelProperty("提货时效")
    private String loadValidity = "";

    @ApiModelProperty("回单数")
    private String carrierOrderTicketsAmount = "";

    @ApiModelProperty("配置距离 1.3.5新增")
    private String configDistance = "";

}
