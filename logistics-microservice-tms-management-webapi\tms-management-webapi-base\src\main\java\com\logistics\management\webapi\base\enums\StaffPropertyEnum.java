package com.logistics.management.webapi.base.enums;

/**
 * @Author: sj
 * @Date: 2019/10/12 14:34
 */
public enum StaffPropertyEnum {
    NULL(-1,""),
    OWNER (1,"自主"),
    OUTSIDE(2,"外部"),
    AFFILIATION(3,"自营"),
    ;

    private Integer key;
    private String value;

    StaffPropertyEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static StaffPropertyEnum getEnum(Integer key) {
        for (StaffPropertyEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return NULL;
    }

    public static StaffPropertyEnum getEnumByValue(String value) {
        for (StaffPropertyEnum t : values()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        return NULL;
    }
}
