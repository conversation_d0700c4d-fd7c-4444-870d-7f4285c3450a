/**
 * Created by yun<PERSON>zhou on 2017/10/23.
 */
package com.logistics.tms.base.enums;

public enum CarrierOrderIfRecycleByCodeEnum {
    NOT_RECYCLE_BY_CODE(0,"非按码回收"),
    RECYCLE_BY_CODE(1,"按码回收"),

    ;

    private Integer code;
    private String value;

    CarrierOrderIfRecycleByCodeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
