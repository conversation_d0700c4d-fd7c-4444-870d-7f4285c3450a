package com.logistics.tms.api.feign.dateremind;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.dateremind.hystrix.DateRemindServiceApiHystrix;
import com.logistics.tms.api.feign.dateremind.model.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/5/31 8:46
 */
@FeignClient(name = "logistics-tms-services", fallback = DateRemindServiceApiHystrix.class)
public interface DateRemindServiceApi {

    @ApiOperation(value = "日期提醒列表")
    @PostMapping(value = "/service/dateRemind/list")
    Result<PageInfo<DateRemindListResponseModel>> searchDateRemindList(@RequestBody DateRemindListRequestModel requestModel);

    @ApiOperation(value = "日期提醒详情")
    @PostMapping(value = "/service/dateRemind/detail")
    Result<DateRemindDetailResponseModel> dateRemindDetail(@RequestBody DateRemindDetailRequestModel responseModel);

    @ApiOperation(value = "日期提醒保存/修改")
    @PostMapping(value = "/service/dateRemind/saveOrModify")
    Result<Boolean> saveOrModifyDateRemind(@RequestBody SaveOrModifyDateRemindRequestModel requestModel);

    @ApiOperation(value = "日期统一提醒")
    @PostMapping(value = "/service/dateRemind/unifiedRemind")
    Result<Boolean> unifiedDateRemind(@RequestBody UnifiedDateRemindRequestModel requestModel);

    @ApiOperation(value = "导出")
    @PostMapping(value = "/service/dateRemind/export")
    Result<List<DateRemindListResponseModel>> export(DateRemindListRequestModel requestModel);
}
