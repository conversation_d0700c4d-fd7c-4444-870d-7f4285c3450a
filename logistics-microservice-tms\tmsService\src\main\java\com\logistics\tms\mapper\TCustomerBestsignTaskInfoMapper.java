package com.logistics.tms.mapper;

import com.logistics.tms.entity.TCustomerBestsignTaskInfo;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Mybatis Generator on 2023/01/13
 */
@Mapper
public interface TCustomerBestsignTaskInfoMapper extends BaseMapper<TCustomerBestsignTaskInfo> {

	List<TCustomerBestsignTaskInfo> getCustomerBestSignTaskList(@Param("certApplyStatus") String certApplyStatus, @Param("status") Integer status);

	int batchUpdate(@Param("list") List<TCustomerBestsignTaskInfo> list);

	List<TCustomerBestsignTaskInfo> selectByRealNameIds(@Param("ids") List<Long> ids);
}