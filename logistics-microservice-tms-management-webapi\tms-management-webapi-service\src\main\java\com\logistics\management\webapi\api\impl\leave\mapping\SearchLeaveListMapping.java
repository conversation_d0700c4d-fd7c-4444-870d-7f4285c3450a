package com.logistics.management.webapi.api.impl.leave.mapping;

import com.logistics.management.webapi.api.feign.leave.dto.SearchLeaveListResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.LeaveApplyAuditStatusEnum;
import com.logistics.management.webapi.base.enums.LeaveApplyTimeTypeEnum;
import com.logistics.management.webapi.base.enums.LeaveApplyTypeEnum;
import com.logistics.management.webapi.base.enums.StaffPropertyEnum;
import com.logistics.tms.api.feign.leave.model.LeaveApplySearchListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

import java.util.Date;

public class SearchLeaveListMapping extends MapperMapping<LeaveApplySearchListResponseModel, SearchLeaveListResponseDto> {

    @Override
    public void configure() {

        LeaveApplySearchListResponseModel source = getSource();
        SearchLeaveListResponseDto destination = getDestination();

        // 审核状态转换
        destination.setLeaveAuditStatusLabel(LeaveApplyAuditStatusEnum.getEnumByKey(source.getLeaveAuditStatus()).getValue());

        // 司机姓名转换
        String leaveApplyStaff = String.format(CommonConstant.NAME_MOBILE_FORMAT, source.getStaffName(), source.getStaffMobile());
        destination.setLeaveApplyStaff(leaveApplyStaff);

        // 司机机构
        destination.setStaffPropertyLabel(StaffPropertyEnum.getEnum(source.getStaffProperty()).getValue());

        // 请假类型转换
        destination.setLeaveTypeLabel(LeaveApplyTypeEnum.getEnumByKey(source.getLeaveType()).getValue());

        // 请假时间转换
        destination.setLeaveStartTime(leaveTimeConversion(source.getLeaveStartTime(), source.getLeaveStartTimeType()));
        destination.setLeaveEndTime(leaveTimeConversion(source.getLeaveEndTime(), source.getLeaveEndTimeType()));

        // 请假时长转换
        destination.setLeaveDuration(source.getLeaveDuration().stripTrailingZeros().toPlainString());
    }

    private String leaveTimeConversion(Date leaveDate, Integer leaveDateType) {
        String leaveDateLabel = DateUtils.dateToString(leaveDate, DateUtils.DATE_TO_STRING_SHORT_PATTERN);
        String leaveDateTypeLabel = LeaveApplyTimeTypeEnum.getEnumByKey(leaveDateType).getValue();
        return String.join(" ", leaveDateLabel, leaveDateTypeLabel);
    }
}
