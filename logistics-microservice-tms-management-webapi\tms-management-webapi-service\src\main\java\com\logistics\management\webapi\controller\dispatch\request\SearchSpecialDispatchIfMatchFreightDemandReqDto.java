package com.logistics.management.webapi.controller.dispatch.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

@Data
public class SearchSpecialDispatchIfMatchFreightDemandReqDto {

    /**
     * 需求单id
     */
    private String demandId;


    /**
     * 调度数量
     */
    private String count;



    /**
     * 发货地仓库 2.42
     */
    private String loadProvinceName;
    /**
     * 发货地市 2.42
     */
    private String loadCityName;
    /**
     * 发货地区 2.42
     */
    private String loadAreaName;
    /**
     * 发货地详细地址  2.42
     */
    private String loadDetailAddress;

    /**
     * 收货地省 2.42
     */
    private String unloadProvinceName;
    /**
     * 收货地市 2.42
     */
    private String unloadCityName;
    /**
     * 收货地区 2.42
     */
    private String unloadAreaName;
    /**
     * 收货地详细地址 2.42
     */
    private String unloadDetailAddress;

    /**
     * 排序
     */
    private String orderNum="";
    /**
     * 到下个点位距离
     */
    private String nextPointDistance="";

}
