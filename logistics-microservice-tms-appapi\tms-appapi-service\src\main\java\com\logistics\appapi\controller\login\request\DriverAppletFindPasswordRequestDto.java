package com.logistics.appapi.controller.login.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class DriverAppletFindPasswordRequestDto {

    @ApiModelProperty(value = "用户账号",required = true)
    @NotBlank(message = "用户账号不能为空")
    private String userAccount;
    @ApiModelProperty(value = "手机验证码",required = true)
    @NotBlank(message = "手机验证码不能为空")
    private String verificationCode;
    @ApiModelProperty(value = "密码",required = true)
    @NotBlank(message = "密码不能为空")
    private String password;
}
