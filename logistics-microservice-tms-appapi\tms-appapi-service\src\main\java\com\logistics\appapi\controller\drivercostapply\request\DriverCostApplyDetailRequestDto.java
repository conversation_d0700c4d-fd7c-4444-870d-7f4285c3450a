package com.logistics.appapi.controller.drivercostapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2022/8/3 9:27
 */
@Data
public class DriverCostApplyDetailRequestDto {
    @ApiModelProperty(value = "司机费用申请表id",required = true)
    @NotBlank(message = "id不能为空")
    private String driverCostApplyId;
}
