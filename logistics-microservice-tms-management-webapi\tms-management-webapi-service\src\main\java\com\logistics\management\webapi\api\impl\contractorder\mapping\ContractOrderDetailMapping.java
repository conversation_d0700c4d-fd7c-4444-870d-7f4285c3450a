package com.logistics.management.webapi.api.impl.contractorder.mapping;

import com.logistics.management.webapi.api.feign.contractorder.dto.ContractFileResponseDto;
import com.logistics.management.webapi.api.feign.contractorder.dto.ContractLogsResponseDto;
import com.logistics.management.webapi.api.feign.contractorder.dto.ContractOrderDetailResponseDto;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.ContractLogsTypeEnum;
import com.logistics.management.webapi.base.enums.ContractNatureEnum;
import com.logistics.management.webapi.base.enums.ContractStatusEnum;
import com.logistics.management.webapi.base.enums.ContractTypeEnum;
import com.logistics.tms.api.feign.contractorder.model.ContractFileResponseModel;
import com.logistics.tms.api.feign.contractorder.model.ContractLogsResponseModel;
import com.logistics.tms.api.feign.contractorder.model.ContractOrderDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;

import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/4/8 15:06
 */
public class ContractOrderDetailMapping extends MapperMapping<ContractOrderDetailResponseModel,ContractOrderDetailResponseDto> {

    private ConfigKeyConstant configKeyConstant;

    private Map<String, String> imageMap;

    public ContractOrderDetailMapping(ConfigKeyConstant configKeyConstant,Map<String, String> imageMap) {
        this.configKeyConstant = configKeyConstant;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        ContractOrderDetailResponseModel source = this.getSource();
        ContractOrderDetailResponseDto destination = this.getDestination();
        if(source.getContractNature()!=null){
            destination.setContractNatureLabel(ContractNatureEnum.getEnum(source.getContractNature()).getValue());
        }
        if(source.getContractStatus()!=null){
            destination.setContractStatusLabel(ContractStatusEnum.getEnum(source.getContractStatus()).getValue());
        }
        if(source.getContractType()!=null){
            destination.setContractTypeLabel(ContractTypeEnum.getEnum(source.getContractType()).getValue());
        }
        if(source.getContractStartTime()!=null){
            destination.setContractStartTime(DateUtils.dateToString(source.getContractStartTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if(source.getContractEndTime()!=null){
            destination.setContractEndTime(DateUtils.dateToString(source.getContractEndTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }

        if (ListUtils.isNotEmpty(source.getContractLogs())){
            for (ContractLogsResponseDto dto:destination.getContractLogs()) {
                for (ContractLogsResponseModel model:source.getContractLogs()) {
                    if (dto.getOperateLogsId().equals(model.getOperateLogsId().toString())){
                        dto.setOperateType(ContractLogsTypeEnum.getEnum(model.getOperateType()).getValue());
                    }
                }
            }
        }
        if(ListUtils.isNotEmpty(source.getContractFiles())){
            for (ContractFileResponseDto dto:destination.getContractFiles()) {
                for (ContractFileResponseModel model:source.getContractFiles()) {
                    if (dto.getContractFileId().equals(model.getContractFileId().toString())){
                        dto.setAbsoluteContractFilePath(configKeyConstant.fileAccessAddress+imageMap.get(model.getContractFilePath()));
                    }
                }
            }
        }
    }
}
