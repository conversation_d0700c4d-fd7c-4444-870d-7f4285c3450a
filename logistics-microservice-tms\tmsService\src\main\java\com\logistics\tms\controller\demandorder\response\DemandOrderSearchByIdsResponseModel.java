package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DemandOrderSearchByIdsResponseModel{
    @ApiModelProperty("需求单ID")
    private Long demandId;
    @ApiModelProperty("委托单状态：500待发布 1000待调度 2000部分调度 3000调度完成")
    private Integer status;
    @ApiModelProperty("是否取消 1 是 0 否")
    private Integer ifCancel;
    @ApiModelProperty("是否放空 1 是 0 否")
    private Integer ifEmpty;
    @ApiModelProperty("是否回退：0 否，1 是")
    private Integer ifRollback;
    @ApiModelProperty("是否加急：0 否，1 是")
    private Integer ifUrgent;
    @ApiModelProperty("委托单号")
    private String demandOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("需求生成人")
    private String publishName;
    @ApiModelProperty("下单时间")
    private Date publishTime;
    @ApiModelProperty("下单人部门Code")
    private String publishOrgCode;
    @ApiModelProperty("下单人部门名称")
    private String publishOrgName;
    @ApiModelProperty("已调车数(包含已取消)")
    private Integer dispatchVehicleCount;
    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;
    @ApiModelProperty("委托单来源：1 乐医托盘委托单，2 委托方发单，3 中石化系统，4 扬巴邮件，5 乐橘新生")
    private Integer source;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("货主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer settlementTonnage;
    @ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer carrierSettlement;
    @ApiModelProperty("乐橘新生客户名称（企业）")
    private String customerName;
    @ApiModelProperty("乐橘新生客户姓名（个人）")
    private String customerUserName;
    @ApiModelProperty("乐橘新生客户手机号（个人）")
    private String customerUserMobile;
    @ApiModelProperty("业务类型：1 公司，2 个人")
    private Integer businessType;
    @ApiModelProperty("客户订单来源：1 乐橘新生客户，2 司机")
    private Integer customerOrderSource;
    @ApiModelProperty("下单人手机号")
    private String publishMobile;

    private String userName;


    private Integer ifRecycleByCode;
    /**
     * 是否后补需求单 0.否 1.是
     */
    private Integer ifExtDemandOrder;

    /**
     * 基础地址code
     */
    @ApiModelProperty("基础地址code")
    private String loadYeloAddressCode;
    @ApiModelProperty("提货地址code")
    private String loadAddressCode;
    private String upstreamCustomer;
    private Long loadProvinceId;
    private String loadProvinceName;
    private Long loadCityId;
    private String loadCityName;
    private Long loadAreaId;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    private String loadCompany;
    private String loadLongitude;
    private String loadLatitude;
    private String consignorName;
    private String consignorMobile;
    private Date expectedLoadTime;
    private Long loadRegionId;
    private String loadRegionName;
    private String loadRegionContactName;
    private String loadRegionContactPhone;
    private Long unloadProvinceId;
    private String unloadProvinceName;
    private Long unloadCityId;
    private String unloadCityName;
    private Long unloadAreaId;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadAddressCode;
    private String unloadWarehouse;
    private String unloadCompany;
    private String unloadLongitude;
    private String unloadLatitude;
    private Integer unloadAddressIsAmend;
    private String receiverName;
    private String receiverMobile;
    private Date expectedUnloadTime;

    @ApiModelProperty("委托数量")
    private BigDecimal goodsAmount;
    @ApiModelProperty("已安排数量")
    private BigDecimal arrangedAmount;
    @ApiModelProperty("未安排数量")
    private BigDecimal notArrangedAmount;
    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;
    @ApiModelProperty("中石化预期货主价格单价")
    private BigDecimal expectUnitPrice;
    @ApiModelProperty("合同价类型：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer contractPriceType;
    @ApiModelProperty("合同价")
    private BigDecimal contractPrice;

    @ApiModelProperty("预计合同价类型：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer exceptContractPriceType;
    @ApiModelProperty("预计合同价")
    private BigDecimal exceptContractPrice;

    @ApiModelProperty("车主费用类型：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer carrierPriceType;
    @ApiModelProperty("车主费用")
    private BigDecimal carrierPrice;

    private String cancelReason;

    @ApiModelProperty("货主信息")
    private Long companyEntrustId;
    private String companyEntrustName;

    @ApiModelProperty("车主信息")
    private Long companyCarrierId;
    private Long carrierContactId;
    private Integer companyCarrierType;
    private String companyCarrierName;
    private String carrierContactName;
    private String carrierContactMobile;
    @ApiModelProperty("车主公司级别：1 云途，2 二级承运商")
    private Integer companyCarrierLevel;

    @ApiModelProperty("是否异常（中石化推送单子）：0 否，1 是")
    private Integer ifObjectionSinopec;

    @ApiModelProperty("周末是否可上门：0 空，1 是，2 否")
    private Integer availableOnWeekends;
    @ApiModelProperty("装卸方: 0 空，1 我司装卸，2 客户装卸")
    private Integer loadingUnloadingPart;
    @ApiModelProperty("装卸费用")
    private BigDecimal loadingUnloadingCharge;
    @ApiModelProperty("回收任务类型：1 日常回收，2 加急或节假日回收")
    private Integer recycleTaskType;
    @ApiModelProperty("项目标签（多个标签,拼接）：1 石化板块，2 轮胎板块，3 涂料板块，4 其他")
    private String projectLabel;

    /**
     * 接单模式：1 指定车主，2 竞价抢单
     */
    @ApiModelProperty("接单模式：1 指定车主，2 竞价抢单")
    private Integer orderMode;

    /**
     * 车长id
     */
    @ApiModelProperty("车长id")
    private Long vehicleLengthId;

    /**
     * 车长（米）
     */
    @ApiModelProperty("车长（米）")
    private BigDecimal vehicleLength;


    private List<DemandOrderGoodsResponseModel> goodsResponseModels;
    private List<DemandOrderEventModel> demandOrderEventModels;
    private List<DemandOrderOrderRelModel> demandOrderOrderRelModels;
}
