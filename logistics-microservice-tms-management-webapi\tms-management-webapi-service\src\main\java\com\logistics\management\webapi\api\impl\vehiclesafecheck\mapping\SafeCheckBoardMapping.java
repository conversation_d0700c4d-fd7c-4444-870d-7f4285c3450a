package com.logistics.management.webapi.api.impl.vehiclesafecheck.mapping;

import com.logistics.management.webapi.api.feign.vehiclesafecheck.dto.SafeCheckBoardResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.NumberConversionEnum;
import com.logistics.tms.api.feign.vehiclesafecheck.model.SafeCheckBoardResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;

import java.math.BigDecimal;

/**
 * @Author: sj
 * @Date: 2019/11/14 16:47
 */
public class SafeCheckBoardMapping extends MapperMapping<SafeCheckBoardResponseModel,SafeCheckBoardResponseDto> {
    @Override
    public void configure() {
        SafeCheckBoardResponseModel source = this.getSource();
        SafeCheckBoardResponseDto dto = this.getDestination();

        if(source!=null){
            String period = source.getPeriod();
            dto.setPeriodYear(period.substring(0,period.length()-3));
            dto.setPeriodMonth(NumberConversionEnum.getEnum(period.substring(period.length()-2)).getValue());

            if(source.getReformCount()!=null && source.getVehicleCount()!=null){
                if(source.getCheckVehicleCount() > 0 && source.getVehicleCount() > 0){
                    dto.setCheckRate(ConverterUtils.toString(ConverterUtils.toBigDecimal(source.getCheckVehicleCount()).divide(ConverterUtils.toBigDecimal(source.getVehicleCount()),4, BigDecimal.ROUND_HALF_UP).multiply(CommonConstant.PERCENT)));
                }
            }

            if(source.getReformCount()!=null && source.getCheckItemCount()!=null){
                if(source.getCheckItemCount() > 0){
                    if(source.getReformCount() > 0 ){
                        dto.setQualifiedRate(ConverterUtils.toString(CommonConstant.PERCENT.subtract(ConverterUtils.toBigDecimal(source.getReformCount()).divide(ConverterUtils.toBigDecimal(source.getCheckItemCount()),4, BigDecimal.ROUND_HALF_UP).multiply(CommonConstant.PERCENT))));
                    }else{
                        dto.setQualifiedRate(CommonConstant.PERCENT.toPlainString());
                    }
                }
            }
        }
    }
}
