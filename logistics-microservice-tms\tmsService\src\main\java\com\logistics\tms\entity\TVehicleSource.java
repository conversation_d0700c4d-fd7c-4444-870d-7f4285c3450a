package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleSource extends BaseEntity {
    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 载重吨位
    */
    @ApiModelProperty("载重吨位")
    private Integer approvedLoadWeight;

    /**
    * 车辆类型1重型仓栅式半挂车,2重型普通半挂车,3重型厢式货车,4重型普通货车,5重型半挂牵引车
    */
    @ApiModelProperty("车辆类型1重型仓栅式半挂车,2重型普通半挂车,3重型厢式货车,4重型普通货车,5重型半挂牵引车")
    private Integer type;

    /**
    * 联系人姓名
    */
    @ApiModelProperty("联系人姓名")
    private String contactName;

    /**
    * 联系人手机号
    */
    @ApiModelProperty("联系人手机号")
    private String contactMobile;
}