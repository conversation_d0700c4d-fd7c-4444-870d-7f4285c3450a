package com.logistics.appapi.base.enums;
/**
 * @author：wjf
 * @date：2021/4/12 13:08
 */
public enum VehicleSettlementStatementStatusEnum {
    DEFAULT(-1,""),
    WAIT_SETTLE_STATEMENT(0,"待对账"),
    WAIT_SEND(1,"待发送"),
    WAIT_CONFIRM(2,"待确认"),
    WAIT_HANDLE(3,"待处理"),
    WAIT_SETTLE(4,"待结清"),
    PART_OF_SETTLE(5,"部分结清"),
    SETTLED(6,"已结清")
    ;

    private Integer key;
    private String value;
    VehicleSettlementStatementStatusEnum(Integer key , String value){
        this.key=key;
        this.value=value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static VehicleSettlementStatementStatusEnum getEnum(Integer key) {
        for (VehicleSettlementStatementStatusEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return DEFAULT;
    }

}
