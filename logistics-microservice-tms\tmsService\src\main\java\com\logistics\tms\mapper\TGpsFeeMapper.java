package com.logistics.tms.mapper;

import com.logistics.tms.api.feign.gpsfee.model.GpsFeeDetailResponseModel;
import com.logistics.tms.api.feign.gpsfee.model.SearchGpsFeeListCountResponseModel;
import com.logistics.tms.api.feign.gpsfee.model.SearchGpsFeeListRequestModel;
import com.logistics.tms.api.feign.gpsfee.model.SearchGpsFeeListResponseModel;
import com.logistics.tms.controller.vehiclesettlement.response.GetGpsFeeByVehicleIdResponseModel;
import com.logistics.tms.controller.vehiclesettlement.response.GetVehicleBySettlementMonthModel;
import com.logistics.tms.entity.TGpsFee;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface TGpsFeeMapper extends BaseMapper<TGpsFee> {

    List<SearchGpsFeeListResponseModel> searchGpsFeeList(@Param("params") SearchGpsFeeListRequestModel requestModel);

    List<TGpsFee> getByVehicleStartDate(@Param("vehicleId")Long vehicleId, @Param("startDate")Date startDate, @Param("finishDate")Date finishDate);

    SearchGpsFeeListCountResponseModel searchGpsFeeListCount(@Param("params") SearchGpsFeeListRequestModel requestModel);

    GpsFeeDetailResponseModel getGpsFeeDetail(@Param("gpsFeeId")Long gpsFeeId);

    GetGpsFeeByVehicleIdResponseModel getCurrentDeductingByIdForSettlement(@Param("id") Long id,@Param("deductingMonth")String deductingMonth);

    List<TGpsFee> getNotTerminal();

    int batchUpdate(@Param("list")List<TGpsFee> list);

    List<GetVehicleBySettlementMonthModel> getVehicleBySettlementMonth(@Param("settlementMonth") String settlementMonth);
}