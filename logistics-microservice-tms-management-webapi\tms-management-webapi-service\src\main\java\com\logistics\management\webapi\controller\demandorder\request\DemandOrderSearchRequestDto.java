package com.logistics.management.webapi.controller.demandorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DemandOrderSearchRequestDto extends AbstractPageForm<DemandOrderSearchRequestDto> {
    @ApiModelProperty("需求单状态：空 全部 500待发布 1000待调度 2000部分调度 3000调度完成 4000待签收 5000已签收 1取消")
    private String demandStatus;
    @ApiModelProperty("委托方,逗号分隔")
    private String companyEntrustIds;
    @ApiModelProperty("委托单号")
    private String demandOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("下单时间开始")
    private String publishTimeStart;
    @ApiModelProperty("下单时间结束")
    private String publishTimeEnd;
    @ApiModelProperty("发货地址")
    private String loadDetailAddress;
    @ApiModelProperty("发货人")
    private String consignorMobile;
    @ApiModelProperty("收货地址")
    private String unloadDetailAddress;
    @ApiModelProperty("收货人")
    private String receiverName;
    @ApiModelProperty("显示已取消勾选 是否取消 1 是 0 否")
    private String showIfCancelStatus;
    @ApiModelProperty("凭证日期从")
    private String ticketDateFrom= "";
    @ApiModelProperty("凭证日期到")
    private String ticketDateTo= "";
    @ApiModelProperty("排序字段")
    private String sort;
    @ApiModelProperty("顺序 asc 升序 desc 倒序")
    private String order;

    @ApiModelProperty("拼单助手需求单号")
    private List<String> demandOrderCodeList;
    @ApiModelProperty("拼单助手客户单号")
    private List<String> customerOrderCodeList;

    @ApiModelProperty(value = "选择性导出，传入选择的ids,多个逗号分隔")
    private String demandIds;

    @ApiModelProperty("是否异常：0 否，1 是")
    private String ifObjection;
    @ApiModelProperty("下单类型：10 发布，20 拉取，21 推送")
    private String orderType;

    @ApiModelProperty("车主名")
    private String companyCarrierName;
}
