package com.logistics.management.webapi.client.shipingfreight;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.shipingfreight.hystrix.ShippingFreightClientHystrix;
import com.logistics.management.webapi.client.shipingfreight.request.*;
import com.logistics.management.webapi.client.shipingfreight.response.AssociateCarrierListRespModel;
import com.logistics.management.webapi.client.shipingfreight.response.ListShippingFreightListRespModel;
import com.yelo.tray.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/6 17:39
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/freight/shipping",
        fallback = ShippingFreightClientHystrix.class)
public interface ShippingFreightClient {


    /**
     * 零担运价管理列表  v2.42
     * @return
     */
    @PostMapping(value = "/getList")
    Result<PageInfo<ListShippingFreightListRespModel>> getList(@RequestBody ListShippingFreightListReqModel reqModel);




    /**
     * 零担运价管理新增  v2.42
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/add")
    Result<Boolean> add(@RequestBody @Valid AddShippingFreightReqModel reqModel);



    /**
     * 零担运价管理关联承运商  v2.42
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/associateCarrier")
    Result<Boolean> associateCarrier(@RequestBody @Valid AssociateCarrierReqModel reqModel);



    /**
     * 关联承运商列表  v2.42
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/associateCarrierList")
    Result<PageInfo<AssociateCarrierListRespModel>> associateCarrierList(@RequestBody @Valid ShippingFreightIdReqModel reqModel);


    /**
     * 关联承运商删除  v2.42
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/associateCarrierDelete")
    Result<Boolean> associateCarrierDelete(@RequestBody @Valid AssociateCarrierDeleteReqModel reqModel);

}
