package com.logistics.tms.client;

import com.logistics.tms.client.feign.tray.basicdata.commonaddress.LeyiCommonAddressServiceApi;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.request.UpdateAccessibilityVerifyRequestModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @author: wjf
 *  * @date: 2024/5/17 14:23
 */
@Component
@Slf4j
public class LeyiBasicDataServiceClient {
    
    @Resource
    private LeyiCommonAddressServiceApi leyiCommonAddressServiceApi;

    /**
     * 根据地址code给地址打标(tms->云盘)
     * @param requestModel
     */
    public void updateAccessibilityVerifyFromGroundPush(UpdateAccessibilityVerifyRequestModel requestModel){
        log.info("根据地址code给地址打标(tms->云盘)入参：{}", requestModel);
        leyiCommonAddressServiceApi.updateAccessibilityVerifyFromGroundPush(requestModel).throwException();
    }
}
