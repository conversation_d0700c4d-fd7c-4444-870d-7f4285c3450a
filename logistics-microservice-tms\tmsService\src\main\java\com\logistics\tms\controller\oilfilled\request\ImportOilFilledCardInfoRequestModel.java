package com.logistics.tms.controller.oilfilled.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/6/10 14:52
 */
@Data
public class ImportOilFilledCardInfoRequestModel {
    @ApiModelProperty("导入List")
    private List<ImportOilFilledCardListRequestModel> importList;
    @ApiModelProperty("失败数量")
    private Integer numberFailures=0;

}
