package com.logistics.management.webapi.controller.demandorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.client.demandorder.response.DemandCarrierOrderListGoodsInfoModel;
import com.logistics.management.webapi.client.demandorder.response.DemandOrderCarrierResponseModel;
import com.logistics.management.webapi.client.demandorder.response.DemandOrderDetailResponseModel;
import com.logistics.management.webapi.client.demandorder.response.DemandOrderGoodsResponseModel;
import com.logistics.management.webapi.controller.demandorder.response.DemandOrderCarrierResponseDto;
import com.logistics.management.webapi.controller.demandorder.response.DemandOrderDetailResponseDto;
import com.logistics.management.webapi.controller.demandorder.response.DemandOrderGoodsResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
public class DemandOrderDetailMapping extends MapperMapping<DemandOrderDetailResponseModel, DemandOrderDetailResponseDto> {

    @Override
    public void configure() {
        DemandOrderDetailResponseModel source = getSource();
        DemandOrderDetailResponseDto destination = getDestination();
        destination.setGoodsUnit(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());

        //提货地址信息
        StringBuilder load = new StringBuilder();
        if (StringUtils.isNotBlank(source.getLoadWarehouse())) {
            load.append("【" + source.getLoadWarehouse() + "】");
        }
        load.append(Optional.ofNullable(source.getLoadProvinceName()).orElse("")).
                append(Optional.ofNullable(source.getLoadCityName()).orElse("")).
                append(Optional.ofNullable(source.getLoadAreaName()).orElse("")).
                append(Optional.ofNullable(source.getLoadDetailAddress()).orElse(""));
        destination.setLoadDetailAddress(load.toString());

        //卸货地址信息
        StringBuilder unLoad = new StringBuilder();
        if (StringUtils.isNotBlank(source.getUnloadWarehouse())) {
            unLoad.append("【" + source.getUnloadWarehouse() + "】");
        }
        unLoad.append(Optional.ofNullable(source.getUnloadProvinceName()).orElse("")).
                append(Optional.ofNullable(source.getUnloadCityName()).orElse("")).
                append(Optional.ofNullable(source.getUnloadAreaName()).orElse("")).
                append(Optional.ofNullable(source.getUnloadDetailAddress()).orElse(""));
        destination.setUnloadDetailAddress(unLoad.toString());

        //货物信息
        List<DemandOrderGoodsResponseModel> goodsResponseModel = source.getGoodsResponseModel();
        if (ListUtils.isNotEmpty(goodsResponseModel)) {
            List<DemandOrderGoodsResponseDto> list = new ArrayList<>();
            for (DemandOrderGoodsResponseModel model : goodsResponseModel) {
                DemandOrderGoodsResponseDto responseDto = new DemandOrderGoodsResponseDto();
                responseDto.setGoodsName(model.getGoodsName());
                responseDto.setDemandOrderGoodsId(ConverterUtils.toString(model.getDemandOrderGoodsId()));
                responseDto.setGoodsSize(model.getGoodsSize());
                //委托
                responseDto.setGoodsAmountNumber(model.getGoodsAmountNumber().stripTrailingZeros().toPlainString());
                //已安排
                responseDto.setArrangedAmountNumber(model.getArrangedAmountNumber().stripTrailingZeros().toPlainString());
                //未安排
                responseDto.setNotArrangedAmountNumber(model.getNotArrangedAmountNumber().stripTrailingZeros().toPlainString());
                //已退回
                responseDto.setBackAmountNumber(model.getBackAmountNumber().stripTrailingZeros().toPlainString());

                if (GoodsUnitEnum.BY_VOLUME.getKey().equals(source.getGoodsUnit())) {
                    responseDto.setGoodsSize(model.getLength() + "*" + model.getWidth() + "*" + model.getHeight() + "mm "+responseDto.getGoodsSize());
                    responseDto.setGoodsAmountVolume(ConverterUtils.toString(getVolume(model.getGoodsAmountNumber(), model.getLength(), model.getWidth(), model.getHeight())));
                    responseDto.setArrangedAmountVolume(ConverterUtils.toString(getVolume(model.getArrangedAmountNumber(), model.getLength(), model.getWidth(), model.getHeight())));
                    responseDto.setNotArrangedAmountVolume(ConverterUtils.toString(getVolume(model.getNotArrangedAmountNumber(), model.getLength(), model.getWidth(), model.getHeight())));
                    responseDto.setBackAmountVolume(ConverterUtils.toString(getVolume(model.getBackAmountNumber(), model.getLength(), model.getWidth(), model.getHeight())));
                }
                list.add(responseDto);
            }
            destination.setGoodsResponseModel(list);
        }

        //状态转换
        if (CommonConstant.INTEGER_ONE.equals(source.getIfCancel())) {
            destination.setStatus(DemandOrderStatusEnum.CANCEL_DISPATCH.getKey().toString());
            destination.setStatusLabel(DemandOrderStatusEnum.CANCEL_DISPATCH.getValue());
        }else if (CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())) {
            destination.setStatus(DemandOrderStatusEnum.ORDER_EMPTY.getKey().toString());
            destination.setStatusLabel(DemandOrderStatusEnum.ORDER_EMPTY.getValue());
        }else{
            destination.setStatus(source.getEntrustStatus().toString());
            destination.setStatusLabel(DemandOrderStatusEnum.getEnum(source.getEntrustStatus()).getValue());
        }

        if (source.getExpectedUnloadTime() != null) {
            destination.setExpectedUnloadTime(DateUtils.dateToString(source.getExpectedUnloadTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if (source.getExpectedLoadTime() != null) {
            destination.setExpectedLoadTime(DateUtils.dateToString(source.getExpectedLoadTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if(source.getTicketTime()!=null){
            destination.setTicketDate(DateUtils.dateToString(source.getTicketTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        destination.setConsignorName(source.getConsignorName() + source.getConsignorMobile());
        if (StringUtils.isNotEmpty(source.getReceiverName())) {
            destination.setReceiverName(source.getReceiverName());
        }
        if (StringUtils.isNotEmpty( source.getReceiverMobile())){
            destination.setReceiverName(destination.getReceiverName()+source.getReceiverMobile());
        }

        //货主
        String companyEntrustName = source.getCompanyEntrustName();
        if (EntrustTypeEnum.BOOKING.getKey().equals(source.getEntrustType())){
            companyEntrustName = CommonConstant.LEYI_POINTS_FOR_LOGISTICS;
        }
        destination.setCustomerCompanyName(companyEntrustName);



        List<DemandOrderCarrierResponseDto> carrierOrders = new ArrayList<>();
        //运单信息转换
        if (ListUtils.isNotEmpty(source.getCarrierResponseModel())) {
            for (DemandOrderCarrierResponseModel model : source.getCarrierResponseModel()) {
                DemandOrderCarrierResponseDto carrierOrder = new DemandOrderCarrierResponseDto();
                carrierOrder.setCarrierOrderCode(model.getCarrierOrderCode());
                carrierOrder.setCarrierOrderId(ConverterUtils.toString(model.getCarrierOrderId()));
                if (model.getDispatchTime() != null) {
                    carrierOrder.setDispatchTime(DateUtils.dateToString(model.getDispatchTime(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
                }
                //状态转换
                if (CommonConstant.INTEGER_ONE.equals(model.getIfCancel())) {
                    carrierOrder.setStatus(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getKey().toString());
                    carrierOrder.setStatusLabel(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getValue());
                }else if(CommonConstant.INTEGER_ONE.equals(model.getIfEmpty())){
                    carrierOrder.setStatus(CarrierOrderStatusEnum.EMPTY.getKey().toString());
                    carrierOrder.setStatusLabel(CarrierOrderStatusEnum.EMPTY.getValue());
                }else {
                    carrierOrder.setStatus(model.getStatus().toString());
                    carrierOrder.setStatusLabel(CarrierOrderStatusEnum.getEnum(model.getStatus()).getValue());
                }
                carrierOrder.setVehicleNumber(model.getVehicleNumber());
                carrierOrder.setDriverName(model.getDriverName() == null ? "" : model.getDriverName() + " " + model.getDriverMobile());
                carrierOrder.setDispatchUserName(model.getDispatchUserName());
                carrierOrder.setExpectAmount(model.getExpectAmount().stripTrailingZeros().toPlainString());
                List<DemandCarrierOrderListGoodsInfoModel> goodsInfoList = model.getGoodsInfoList();

                //运单预计体积
                BigDecimal exceptVolume = BigDecimal.ZERO;
                if (ListUtils.isNotEmpty(goodsInfoList)) {
                    for (DemandCarrierOrderListGoodsInfoModel tmp : goodsInfoList) {
                        exceptVolume = exceptVolume.add(getVolume(tmp.getExpectAmount(), tmp.getLength(), tmp.getWidth(), tmp.getHeight()));
                    }
                    if (exceptVolume.compareTo(BigDecimal.ZERO) > 0) {
                        carrierOrder.setAmountVolume(exceptVolume.setScale(3,BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
                    }
                }
                carrierOrders.add(carrierOrder);
            }
        }
        destination.setCarrierResponseModel(carrierOrders);

        if(source.getSettlementTonnage()!=null){
            destination.setSettlementTonnageLabel(SettlementTonnageLabelEnum.getEnum(source.getSettlementTonnage()).getValue());
        }

        String priceUnit = GoodsUnitEnum.getEnum(source.getGoodsUnit()).getPriceUnit();
        
        //调度完成-根据结算吨位计算实际货主费用
        if (ListUtils.isNotEmpty(source.getCarrierResponseModel())){
            Integer settlementTonnage = source.getSettlementTonnage();
            if((source.getEntrustStatus() >= DemandOrderStatusEnum.COMPLETE_DISPATCH.getKey()) && this.carrierStatusAvailable(source.getCarrierResponseModel(),settlementTonnage)){
                Integer actualContractPriceType;
                if(source.getSettlementCostTotal() != null){
                    if(source.getPriceType() != null && !ContractTypeEnum.DEFAULT_VALUE.getKey().equals(source.getPriceType())){
                        destination.setActualContractPriceType(ConverterUtils.toString(source.getPriceType()));
                        destination.setActualContractPriceTotal(source.getSettlementCostTotal() + CommonConstant.YUAN);
                        if(PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getPriceType())){
                            destination.setActualContractPrice(source.getSettlementCostTotal().divide(source.getSettlementAmount(),2,BigDecimal.ROUND_HALF_UP) + priceUnit);
                        }else{
                            destination.setActualContractPrice(source.getSettlementCostTotal() + CommonConstant.YUAN);
                        }
                    }
                }else {
                    actualContractPriceType = source.getContractPriceType();
                    destination.setActualContractPriceType(ConverterUtils.toString(actualContractPriceType));
                    if (PriceTypeEnum.DEFAULT.getKey().equals(actualContractPriceType)) {//如果类型为空则费用合计显示空
                        destination.setActualContractPriceTotal("");
                        destination.setActualContractPrice("");
                    }else {
                        BigDecimal actualEntrustPrice = BigDecimal.ZERO;
                        for (DemandOrderCarrierResponseModel model : source.getCarrierResponseModel()) {
                            //排除取消运单
                            if (CommonConstant.INTEGER_ONE.equals(model.getIfCancel())) {
                                continue;
                            }
                            BigDecimal amount = BigDecimal.ZERO;
                            for (DemandCarrierOrderListGoodsInfoModel goods : model.getGoodsInfoList()) {
                                if (settlementTonnage.equals(SettlementTonnageEnum.EXPECT.getKey())) {
                                    amount = amount.add(goods.getExpectAmount());
                                } else if (settlementTonnage.equals(SettlementTonnageEnum.LOAD.getKey())) {
                                    amount = amount.add(goods.getLoadAmount());
                                } else if (settlementTonnage.equals(SettlementTonnageEnum.UNLOAD.getKey())) {
                                    amount = amount.add(goods.getUnloadAmount());
                                } else if (settlementTonnage.equals(SettlementTonnageEnum.SIGN.getKey())) {
                                    if (goods.getSignAmount() != null && goods.getSignAmount().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                                        amount = amount.add(goods.getSignAmount());
                                    } else {
                                        amount = amount.add(goods.getUnloadAmount());
                                    }
                                }
                            }

                            //乐医托盘
                            if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(source.getSource()) && settlementTonnage.equals(SettlementTonnageEnum.SIGN.getKey())) {
                                actualEntrustPrice = actualEntrustPrice.add(model.getSignFreightFee());
                            } else {
                                if (PriceTypeEnum.UNIT_PRICE.getKey().equals(model.getEntrustFreightType())) {
                                    actualEntrustPrice = actualEntrustPrice.add(model.getEntrustFreight().multiply(amount).setScale(2, BigDecimal.ROUND_HALF_UP));
                                } else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(model.getEntrustFreightType())) {
                                    actualEntrustPrice = actualEntrustPrice.add(model.getEntrustFreight().setScale(2, BigDecimal.ROUND_HALF_UP));
                                }
                            }
                        }
                        if (PriceTypeEnum.UNIT_PRICE.getKey().equals(actualContractPriceType)) {
                            destination.setActualContractPrice(source.getContractPrice().setScale(2, BigDecimal.ROUND_HALF_UP) + priceUnit);
                        } else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(actualContractPriceType)) {
                            destination.setActualContractPrice(actualEntrustPrice + CommonConstant.YUAN);
                        }
                        destination.setActualContractPriceTotal(actualEntrustPrice + CommonConstant.YUAN);
                    }
                }
            }
         }

        //预计数量
        BigDecimal calcAmount = source.getGoodsAmount();
        //预计货主费用 (预计车主费用等于预计货主费用)
        if(PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getExceptContractPriceType())){
            destination.setContractPriceType(ConverterUtils.toString(source.getExceptContractPriceType()));
            destination.setContractPrice(source.getExceptContractPrice()+priceUnit);
            destination.setContractPriceTotal(ConverterUtils.toString(calcAmount.multiply(source.getExceptContractPrice()).setScale(2,BigDecimal.ROUND_HALF_UP))+CommonConstant.YUAN);
         }else if(PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getExceptContractPriceType())){
            destination.setContractPriceType(ConverterUtils.toString(source.getExceptContractPriceType()));
            destination.setContractPrice(source.getExceptContractPrice()+CommonConstant.YUAN);
            destination.setContractPriceTotal(ConverterUtils.toString(source.getExceptContractPrice().setScale(2,BigDecimal.ROUND_HALF_UP))+CommonConstant.YUAN);
        }

        //其他车主
        if (CommonConstant.INTEGER_TWO.equals(source.getIsOurCompany())){
            //预计车主费用
            if (PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getCarrierPriceType())){
                destination.setExpectedCarrierPriceType(ConverterUtils.toString(source.getCarrierPriceType()));
                destination.setExpectedCarrierPrice(source.getCarrierPrice()+priceUnit);
                destination.setExpectedCarrierPriceTotal(ConverterUtils.toString(calcAmount.multiply(source.getCarrierPrice()).setScale(2,BigDecimal.ROUND_HALF_UP))+CommonConstant.YUAN);
            }else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getCarrierPriceType())){
                destination.setExpectedCarrierPriceType(ConverterUtils.toString(source.getCarrierPriceType()));
                destination.setExpectedCarrierPrice(source.getCarrierPrice()+CommonConstant.YUAN);
                destination.setExpectedCarrierPriceTotal(ConverterUtils.toString(source.getCarrierPrice().setScale(2,BigDecimal.ROUND_HALF_UP))+CommonConstant.YUAN);
            }
            //需求单签收展示车主实际费用
            if (ListUtils.isNotEmpty(source.getCarrierResponseModel()) && DemandOrderStatusEnum.COMPLETE_SIGN.getKey().equals(source.getEntrustStatus())){
                if(source.getCarrierSettlementCostTotal() != null){
                    if(source.getCarrierPaymentPriceType() != null && !ContractTypeEnum.DEFAULT_VALUE.getKey().equals(source.getCarrierPaymentPriceType())){
                        destination.setCarrierPriceType(ConverterUtils.toString(source.getCarrierPaymentPriceType()));
                        destination.setCarrierPriceTotal(source.getCarrierSettlementCostTotal() + CommonConstant.YUAN);
                        if(PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getCarrierPaymentPriceType())){
                            destination.setCarrierPrice(source.getCarrierSettlementCostTotal().divide(source.getCarrierSettlementAmount(),2,BigDecimal.ROUND_HALF_UP) + priceUnit);
                        }else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getCarrierPaymentPriceType())){
                            destination.setCarrierPrice(source.getCarrierSettlementCostTotal() + CommonConstant.YUAN);
                        }
                    }
                }else {
                    Integer actualContractPriceType = source.getContractPriceType();
                    destination.setCarrierPriceTotal(ConverterUtils.toString(actualContractPriceType));
                    if (PriceTypeEnum.DEFAULT.getKey().equals(actualContractPriceType)) {//如果类型为空则费用合计显示空
                        destination.setCarrierPriceTotal("");
                        destination.setCarrierPrice("");
                    }
                }
            }else{
                destination.setCarrierPriceType("");
                destination.setCarrierPrice("");
                destination.setCarrierPriceTotal("");
            }

        }else if (CommonConstant.INTEGER_ONE.equals(source.getIsOurCompany())){//我司
            //预计车主费用
            destination.setExpectedCarrierPriceType("");
            destination.setExpectedCarrierPrice("");
            destination.setExpectedCarrierPriceTotal("");
            //司机费用总和
            if (DemandOrderStatusEnum.COMPLETE_SIGN.getKey().equals(source.getEntrustStatus())) {//需求单签收后展示车主实际费用
                BigDecimal signDispatchFreightFeeTotal = BigDecimal.ZERO;
                BigDecimal signDispatchFreightFee = BigDecimal.ZERO;
                if (ListUtils.isNotEmpty(source.getCarrierResponseModel())) {
                    for (DemandOrderCarrierResponseModel carrierOrderDetailFreightFeeInfoModel : source.getCarrierResponseModel()) {
                        //已卸货
                        if (carrierOrderDetailFreightFeeInfoModel.getStatus() > CarrierOrderStatusEnum.WAIT_UNLOAD.getKey()) {
                            BigDecimal dispatchFreightFee = carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFee();
                            if (carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFeeType().equals(PriceTypeEnum.UNIT_PRICE.getKey())) {
                                signDispatchFreightFee = dispatchFreightFee.multiply(carrierOrderDetailFreightFeeInfoModel.getUnloadAmount()).setScale(2, BigDecimal.ROUND_HALF_UP);
                            } else if (carrierOrderDetailFreightFeeInfoModel.getDispatchFreightFeeType().equals(PriceTypeEnum.FIXED_PRICE.getKey())) {
                                signDispatchFreightFee = dispatchFreightFee;
                            }
                            signDispatchFreightFeeTotal = signDispatchFreightFeeTotal.add(signDispatchFreightFee.add(carrierOrderDetailFreightFeeInfoModel.getAdjustFee()).add(carrierOrderDetailFreightFeeInfoModel.getMarkupFee()));
                        }
                    }
                }
                //实际车主费用
                destination.setCarrierPriceType("");
                destination.setCarrierPrice("");
                if (!BigDecimal.ZERO.equals(signDispatchFreightFeeTotal)) {
                    destination.setCarrierPriceTotal(ConverterUtils.toString(signDispatchFreightFeeTotal) + CommonConstant.YUAN);
                } else {
                    destination.setCarrierPriceTotal("");
                }
            }else{
                destination.setCarrierPriceType("");
                destination.setCarrierPrice("");
                destination.setCarrierPriceTotal("");
            }
        }else{//没有车主（待发布状态）
            destination.setCarrierPriceType("");
            destination.setCarrierPrice("");
            destination.setCarrierPriceTotal("");
        }

        //中石化推送的单子客户单号展示为：订单号（委托单号）
        if (DemandOrderSourceEnum.SINOPEC.getKey().equals(source.getSource()) && DemandOrderOrderTypeEnum.PUSH.getKey().equals(source.getOrderType())){
            destination.setCustomerOrderCode(source.getSinopecOrderNo() + "（" + source.getCustomerOrderCode() + "）");
        }
    }

    /**
     * 计算物品体积
     * @param amount
     * @param length
     * @param width
     * @param height
     * @return
     */
    public BigDecimal getVolume(BigDecimal amount, Integer length, Integer width, Integer height) {
        amount = Optional.ofNullable(amount).orElse(CommonConstant.BIG_DECIMAL_ZERO);
        length = Optional.ofNullable(length).orElse(CommonConstant.INTEGER_ZERO);
        width = Optional.ofNullable(width).orElse(CommonConstant.INTEGER_ZERO);
        height = Optional.ofNullable(height).orElse(CommonConstant.INTEGER_ZERO);
        BigDecimal multiply = amount.multiply(BigDecimal.valueOf(length).multiply(BigDecimal.valueOf(width))).multiply(BigDecimal.valueOf(height));
        return multiply.divide(BigDecimal.valueOf(1000000000)).setScale(3, BigDecimal.ROUND_HALF_UP).stripTrailingZeros();
    }

    /**
     * 根据货主结算吨位,判断需求单下所有运单状态，是否可用于计算货主实际结算费用
     * @return
             */
    private boolean carrierStatusAvailable(List<DemandOrderCarrierResponseModel> carrierOrderList, Integer settlementTonnage){
        boolean flag = true;
        if(ListUtils.isEmpty(carrierOrderList)){
            return false;
        }
        for (DemandOrderCarrierResponseModel tempModel: carrierOrderList) {
            //排除取消的运单
            if(CommonConstant.INTEGER_ONE.equals(tempModel.getIfCancel())){
                continue;
            }
            if (settlementTonnage.equals(SettlementTonnageEnum.LOAD.getKey())) {
                if(tempModel.getStatus() < CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey()){
                    flag = false;
                }
            } else if (settlementTonnage.equals(SettlementTonnageEnum.UNLOAD.getKey())) {
                if(tempModel.getStatus() < CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey()){
                    flag = false;
                }
            } else if (settlementTonnage.equals(SettlementTonnageEnum.SIGN.getKey())) {
                if (tempModel.getStatus() < CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey()) {
                    flag = false;
                }
            }else{
                //
            }
        }
        return flag;
    }

}
