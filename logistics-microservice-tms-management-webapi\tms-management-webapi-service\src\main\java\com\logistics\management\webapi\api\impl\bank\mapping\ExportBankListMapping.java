package com.logistics.management.webapi.api.impl.bank.mapping;

import com.logistics.management.webapi.api.feign.bank.dto.SearchBankResponseDto;
import com.logistics.management.webapi.base.enums.EnabledEnum;
import com.logistics.tms.api.feign.bank.model.SearchBankResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @Author: sj
 * @Date: 2019/7/12 15:19
 */
public class ExportBankListMapping extends MapperMapping<SearchBankResponseModel,SearchBankResponseDto> {
    @Override
    public void configure() {
        SearchBankResponseModel source = this.getSource();
        SearchBankResponseDto destination = this.getDestination();
        if(source!=null) {
            destination.setEnableLabel(EnabledEnum.getEnum(source.getEnable()).getValue());
            destination.setLastModifiedTime(DateUtils.dateToString(source.getLastModifiedTime(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
        }
    }
}
