package com.logistics.tms.controller.vehiclesettlement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/6/8 13:02
 */
@Data
public class VehicleInsuranceCostRequestModel {
    @ApiModelProperty(value = "保单Id")
    private Long insuranceId;
    @ApiModelProperty(value = "险种：1 商业险，2 交强险，4 货物险，5 危货承运人险")
    private Integer insuranceType;
    @ApiModelProperty(value = "应扣减费用")
    private BigDecimal payCost;
}
