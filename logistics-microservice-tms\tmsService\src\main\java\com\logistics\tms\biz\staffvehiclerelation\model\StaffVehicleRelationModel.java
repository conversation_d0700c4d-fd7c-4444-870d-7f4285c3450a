package com.logistics.tms.biz.staffvehiclerelation.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/7/29 9:29
 */
@Data
public class StaffVehicleRelationModel {

    @ApiModelProperty("关系ID")
    private Long vehicleRelationId;

    @ApiModelProperty("牵引车或一体车-车牌ID)")
    private Long vehicleId;

    @ApiModelProperty("牵引车或一体车-车牌号")
    private String vehicleNo;

    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;

    @ApiModelProperty("挂车ID")
    private Long trailerVehicleId;

    @ApiModelProperty("挂车车牌号")
    private String trailerVehicleNo;

    @ApiModelProperty("车辆类别 1 牵引车 2 挂车 3 一体车")
    private Integer vehicleCategory;

    @ApiModelProperty("司机Id")
    private Long staffId;

    @ApiModelProperty("人员机构 1 自主，2 外部，3 自营")
    private Integer staffProperty;

    @ApiModelProperty("司机名称")
    private String staffName;

    @ApiModelProperty("司机电话")
    private String staffPhone;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("车主id")
    private Long companyCarrierId;
}
