package com.logistics.management.webapi.client.carrierorderotherfee.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CarrierOrderOtherFeeItemDetailResponseModel {

    @ApiModelProperty(value = "主表id")
    private Long carrierOrderOtherFeeItemId;

    @ApiModelProperty(value = "费用类型：1 短驳费，2 保管费，3 装卸费，4 压车费，5 质量处罚费，6 其他杂费")
    private Integer feeType;

    @ApiModelProperty(value = "金额 :0<费用<=10000元")
    private BigDecimal feeAmount;

    @ApiModelProperty("单据-路径")
    private List<String> billsPicture;

    @ApiModelProperty(value = "费用需求 v2.44(2) 0:我司1.客户" )
    private Integer feeSource;


    @ApiModelProperty(value = "费用需求文本" )
    private String feeSourceLabel;
}
