package com.logistics.appapi.controller.drivercostapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/2 15:57
 */
@Data
public class AddDriverCostApplyRequestDto {

    @ApiModelProperty("司机费用申请表id（重新提交时必填）")
    private String driverCostApplyId;

    @ApiModelProperty(value = "车辆id",required = true)
    @NotBlank(message = "请选择车辆")
    private String vehicleId;

    @ApiModelProperty(value = "(3.11.0)费用类型：100 住宿费，101 装卸费，102 其他费用，103 加班费，104 劳保费，105 电话费，106 交通费， 107 打印费，108 叉车费，109 盖雨布， 110破包赔偿，111 交通罚款；200 维修费，201 车辆保养费，202 过路过桥费，203 停车费，204 尿素费，205 加油费；300 核酸检测费，301 医疗防护用品；400 扣款",required = true)
    @NotBlank(message = "请选择费用类型")
    private String costType;

    @ApiModelProperty(value = "申请费用",required = true)
    @NotBlank(message = "请维护申请费用")
    @DecimalMin(value = "0.01", message = "仅能报销1万以内的费用")
    @DecimalMax(value = "10000", message = "仅能报销1万以内的费用")
    private String applyCost;

    @ApiModelProperty(value = "发生时间",required = true)
    @NotBlank(message = "请选择发生时间")
    private String occurrenceTime;

    @ApiModelProperty(value = "申请说明",required = true)
    @NotBlank(message = "请维护申请说明，1-100字")
    @Size(min = 1, max = 100, message = "请维护申请说明，1-100字")
    private String applyRemark;

    @ApiModelProperty("现场图片 支付图片")
    private List<DriverCostApplyTickDetail> ticketList;

    @Valid
    @ApiModelProperty("发票信息; 1.1.8新增")
    @Size(max = 10, message = "最多只能上传10张发票")
    private List<DriverCostApplyInvoiceRequestDto> invoiceInfoList;

    @Data
    public static class DriverCostApplyTickDetail {

        @ApiModelProperty(value = "图片类型: 1 现场图片, 2 支付图片; 1.1.8删除(3 发票图片)")
        private String type;

        @ApiModelProperty(value = "图片依据路径")
        private List<String> imagePathList;
    }
}
