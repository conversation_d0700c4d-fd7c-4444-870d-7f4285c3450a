package com.logistics.management.webapi.controller.routeenquiry.response;

import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/7/9 9:32
 */
@Data
public class RouteEnquiryQuoteListResponseDto {

    /**
     * 路线询价单车主表id
     */
    private String routeEnquiryCompanyId="";

    /**
     * 车主
     */
    private String companyCarrierName="";

    /**
     * 报价时间
     */
    private String quoteTime="";

    /**
     * 选择承运商：0 否，1 是
     */
    private String selectCarrier="";

}
