package com.logistics.management.webapi.api.feign.gpsfee.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/10/9 9:53
 */
@Data
public class GpsFeeRecordsListResponseDto {
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty("服务商")
    private String gpsServiceProvider="";
    @ApiModelProperty("服务费")
    private String serviceFee="";
    @ApiModelProperty("起始日期")
    private String startDate="";
    @ApiModelProperty("截止时间")
    private String endDate="";
    @ApiModelProperty("终止时间")
    private String finishDate="";
    @ApiModelProperty("备注")
    private String remark="";
    @ApiModelProperty("最后修改人")
    private String lastModifiedBy="";
    @ApiModelProperty("最后修改时间")
    private String lastModifiedTime="";
}
