package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/9/6 10:59
 */
@Data
public class GetValidCarrierOrderResponseDto {
    @ApiModelProperty("运单id")
    private String carrierOrderId="";
    @ApiModelProperty("运单号")
    private String carrierOrderCode="";
    @ApiModelProperty("驾驶员")
    private String staff="";
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty("发货地")
    private String loadAddress="";
    @ApiModelProperty("收货地")
    private String unloadAddress="";
    @ApiModelProperty("数量(带单位)")
    private String amount="";
    @ApiModelProperty("装卸方式")
    private String loadingUnloadingPartLabel= "";
    @ApiModelProperty("装卸费用")
    private String loadingUnloadingCharge= "";
}
