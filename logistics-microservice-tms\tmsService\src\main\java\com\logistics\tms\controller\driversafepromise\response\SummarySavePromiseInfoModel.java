package com.logistics.tms.controller.driversafepromise.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * 承诺书签订人数汇总
 * @Author: sj
 * @Date: 2019/11/4 17:21
 */
@Data
public class SummarySavePromiseInfoModel {
    @ApiModelProperty("承诺书ID")
    private Long safePromiseId;
    @ApiModelProperty("已签订人数")
    private Integer hasSignCount;
    @ApiModelProperty("未签订人数")
    private Integer notSignCount;
}
