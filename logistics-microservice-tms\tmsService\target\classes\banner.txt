${AnsiColor.BRIGHT_YELLOW}

             .__  .__          __                .__
___________  |  | |  |   _____/  |_  ____   ____ |  |__
\____ \__  \ |  | |  | _/ __ \   __\/ __ \_/ ___\|  |  \
|  |_> > __ \|  |_|  |_\  ___/|  | \  ___/\  \___|   Y  \
|   __(____  /____/____/\___  >__|  \___  >\___  >___|  /
|__|       \/               \/          \/     \/     \/
////////////////////////////////////////////////////////////////////////////////////////////////
                    ::  .::                                              ::.  ::
                  .::::::   ::                                        ::   ::::::.
                 :::jjjffjf:  .:                                    :.  :fjffjjj:::
                 :.jjjjjfjjjj:. :                                  : .:jjjjfjjjjj.:
                ::fjtttiitjjjjf: .:                              :. :fjjjjtiiiitjf::
                ::jii;;;;tttijjjt:::                            :::tjjjittt;;;;iij::
                :jti;;;;;;;;titjjj:::                          :::jjjtit;;;;;;;;itj:
                :ji;;;;;;;;;;iiijjj:.:                        :.:jjjiii;;;;;;;;;;ij:
                :fi;;;;;;;;;;;iitijj.:.                      .:.jjitii;;;;;;;;;;;if:
                :.i;;;;;;;;;;;;;titjf:::                    :::fjtit;;;;;;;;;;;;;i.:
                :.tt;;;;;;;;;;;;;tttj.::                    :::jttt;;;;;;;;;;;;;it.:
                ::fti;;;;;;;;;;;;;itjf:::                  :::jjtt;;;;;;;;;;;;;itf::
                 ::ti;;;;;;;;;;;;;;ttjt::.                .::tfti;;;;;;;;;;;;;;it::
                 ::iii;;;;;;;;;;;;;;iij.::                ::.jti;;;;;;;;;;;;;;iii::
                  ::jit;;;;;;;;;;;;;tijf::                ::fjit;;;;;;;;;;;;;iij::
                   ::jtt;;;;;;;;;;;;;iij::.              .::jii;;;;;;;;;;;;;tij::
                    :.fit;;;;;;;;;;;;tijt::              ::tjit;;;;;;;;;;;;tif.:
                     ::jtt;;;;;;;;;;;ittj:::            :::jtt;;;;;;;;;;;;itj::
                      :::tii;;;;;;;;;;tij:::            :::jit;;;;;;;;;;itt:::
                       ::.iti;;;;;;;;;;iff::            ::jji;;;;;;;;;;iti.::
                        .::tiii;;;;;;;;iij:::          :::jii;;;;;;;;iiit::.
                         .:.jiiii;;;;;;itj:::          :::jti;;;;;;iiiij.:.
                          :::fiti;;;;;;;tjt::          ::tjt;;;;;;;iiif:::
                           .:.jttii;;;;;itf::.        :::fti;;;;;iitij.:.
                            .::ftiti;;;;ttj:::        :::jtt;;;;ititf::.
                             .::ftiii;;;;tf:::        ::.fi;;;;tiitf::.
                              .::fiittt;;tij::        ::jii;;tttiif::.
                               .::jiiiii;itj::        ::jtt;iiiiij::.
                                .:.tttiiitij:::      :::jitiiiitt.:.
                                 .::fiiiiiij:::      :::jiiiiiif::.
                                  .::jiiiiit:::      :::tiiiiij::.
                                   ::.ittiitf::      ::ftiiiii:::
                                    .:ftiiiij::      ::jiiiitf:.
                                     ::jiiiij::      ::jiiiij::
                                      :,itiif::      ::fiiii,:
                                      ::ftiii:::    :::iiitf::
                                       .:jiii.::    ::.iiij:.
                                        :,iiii::    ::tiii,:
                                        .:jttf::    ::fttj:.
                                         :jiif::    ::fiij:
                                         ..jij::    ::jij..
                                          :fti::    ::itf:
                                          .:ti::    ::it:.
                                           :ft::    ::tf:
                                           :ij::    ::ji.
                                            :j::    ::j:
                                            :j::    ::j:
                                            :f::    ::f:
                                             :.:    :.:
                                             .::    ::.
                                             :::    :::
                                             :::    :::
                                             :::    :::
                                             :::,,,;:::
                                            ,,:::  .:::,,
                                          ,::.   ...    :,,
                                        .:: ::::::::::::. ::
                                       ,: :::           ::::,
                                      ;:::                ::..
                                     ,:::                   :::
                                     ::.                     :::
                                    .:                        ::
                                    :                          ::
                                   ::                           :
                                   .                             :
                                                                 :
////////////////////////////////////////////////////////////////////////////////////////////////
Application Version: ${application.version}${application.formatted-version}
Spring Boot Version: ${spring-boot.version}${spring-boot.formatted-version}
${AnsiColor.DEFAULT}