package com.logistics.tms.client.feign.tray.order.customerinorder.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class LogisticsAddOrUpdateActualAddressRequest {

    @ApiModelProperty(value = "需求单号")
    private String demandCode;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "省Id")
    private Long provinceId;

    @ApiModelProperty(value = "省名称")
    private String provinceName;

    @ApiModelProperty(value = "市Id")
    private Long cityId;

    @ApiModelProperty(value = "市名称")
    private String cityName;

    @ApiModelProperty(value = "区Id")
    private Long areaId;

    @ApiModelProperty(value = "区名称")
    private String areaName;

    @ApiModelProperty(value = "地址详情")
    private String detailAddress;

    @ApiModelProperty(value = "联系人")
    private String contactName;

    @ApiModelProperty(value = "联系方式")
    private String contactMobile;

    @ApiModelProperty(value = "联系邮箱")
    private String contactEmail;

    @ApiModelProperty(value = "周末可上门: 0.空 1.是 2.否")
    private Integer availableOnWeekends;

    @ApiModelProperty(value = "装卸方: 0.空 1.我司装卸 2.客户装卸")
    private Integer loadingUnloadingPartingPart;


    /**
     * 地址经度 v5.0.0
     */
    private String longitude;


    /**
     * 地址纬度 v5.0.0
     */
    private String latitude;


    /**
     * 创建人
     */
    private String createdBy;
}

