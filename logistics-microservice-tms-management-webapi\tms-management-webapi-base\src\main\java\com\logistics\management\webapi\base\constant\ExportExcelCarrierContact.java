package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

public class ExportExcelCarrierContact {
    private ExportExcelCarrierContact() {
    }
    private static final Map<String, String> EXPORT_CARRIER_ACCOUNT;
    static{
        EXPORT_CARRIER_ACCOUNT = new LinkedHashMap<>();
        EXPORT_CARRIER_ACCOUNT.put("使用状态 ", "carrierContactStatusLabel");
        EXPORT_CARRIER_ACCOUNT.put("车主类型", "typeLabel");
        EXPORT_CARRIER_ACCOUNT.put("车主", "companyCarrierName");
        EXPORT_CARRIER_ACCOUNT.put("姓名", "contactName");
        EXPORT_CARRIER_ACCOUNT.put("手机号", "exportContactPhone");

    }

    public static Map<String, String> getExportCarrierAccount() {
        return EXPORT_CARRIER_ACCOUNT;
    }
}
