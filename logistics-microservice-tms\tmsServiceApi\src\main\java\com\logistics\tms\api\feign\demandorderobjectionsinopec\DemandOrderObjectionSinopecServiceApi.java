package com.logistics.tms.api.feign.demandorderobjectionsinopec;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.demandorderobjectionsinopec.hystrix.DemandOrderObjectionSinopecServiceApiHystrix;
import com.logistics.tms.api.feign.demandorderobjectionsinopec.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2022/5/30 15:16
 */
@Api(value = "API - DemandOrderObjectionSinopecServiceApi-中石化需求单异常管理")
@FeignClient(name = "logistics-tms-services", fallback = DemandOrderObjectionSinopecServiceApiHystrix.class)
public interface DemandOrderObjectionSinopecServiceApi {

    @ApiOperation(value = "中石化需求单异常列表")
    @PostMapping(value = "/service/demandOrderObjectionSinopec/searchSinopecObjection")
    Result<PageInfo<SearchDemandOrderObjectionSinopecResponseModel>> searchSinopecObjection(@RequestBody SearchDemandOrderObjectionSinopecRequestModel requestModel);

    @ApiOperation(value = "中石化需求单异常详情")
    @PostMapping(value = "/service/demandOrderObjectionSinopec/getSinopecObjectionDetail")
    Result<GetSinopecObjectionDetailResponseModel> getSinopecObjectionDetail(@RequestBody GetSinopecObjectionDetailRequestModel requestModel);

    @ApiOperation(value = "中石化需求单异常审核")
    @PostMapping(value = "/service/demandOrderObjectionSinopec/sinopecObjectionAudit")
    Result<Boolean> sinopecObjectionAudit(@RequestBody SinopecObjectionAuditRequestModel requestModel);
}
