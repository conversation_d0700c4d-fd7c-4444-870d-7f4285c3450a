package com.logistics.appapi.controller.vehiclesafecheck.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/11/18 13:21
 */
@Data
public class SafeCheckListRequestDto extends AbstractPageForm<SafeCheckListRequestDto> {
    @ApiModelProperty("状态 0未检查、10待确认、20待整改、30已整改、40检查完成")
    private String status;
}
