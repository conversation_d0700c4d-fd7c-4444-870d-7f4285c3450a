package com.logistics.management.webapi.controller.carrierorderotherfee.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class AddCarrierOrderOtherFeeRequestDto {

    @ApiModelProperty(value = "运单主表ID" ,required = true)
    @NotBlank(message = "请选择一条运单")
    private String carrierOrderId;

    @ApiModelProperty(value = "临时费用明细" ,required = true)
    @NotEmpty(message = "请填写费用")
    @Valid
    private List<AddCarrierOrderOtherFeeItemRequestDto> otherFeeList;

}
