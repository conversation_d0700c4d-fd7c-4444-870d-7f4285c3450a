package com.logistics.tms.biz.carrierorderticketsaudit.event;

import com.logistics.tms.biz.carrierorderticketsaudit.CarrierOrderTicketsAuditBiz;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;

@Component
public class CarrierOrderTicketsAuditEventListener {

    @Resource
    private CarrierOrderTicketsAuditBiz carrierOrderTicketsAuditBiz;

    /**
     * 创建|重置回单审核 事件
     *
     * @param event 事件 Model
     */
    @TransactionalEventListener
    public void createOrResetReceiptTicketAudit(CarrierOrderTicketsAuditEvent event) {
        event.getBoModels().forEach(f -> {
            carrierOrderTicketsAuditBiz.asyncCreateReceiptTicketAudit(f);
        });
    }
}
