package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TViolationRegulations extends BaseEntity {
    /**
    * 车辆ID
    */
    @ApiModelProperty("车辆ID")
    private Long vehicleId;

    /**
    * 司机ID
    */
    @ApiModelProperty("司机ID")
    private Long driverId;

    /**
    * 车辆机构：1 自主，2 外部，3 自营
    */
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;

    /**
    * 扣分
    */
    @ApiModelProperty("扣分")
    private Integer deduction;

    /**
    * 罚款
    */
    @ApiModelProperty("罚款")
    private BigDecimal fine;

    /**
    * 发生时间
    */
    @ApiModelProperty("发生时间")
    private Date occuranceTime;

    /**
    * 发生地点
    */
    @ApiModelProperty("发生地点")
    private String occuranceAddress;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 车辆添加来源：1新增 2导入
    */
    @ApiModelProperty("车辆添加来源：1新增 2导入")
    private Integer source;
}