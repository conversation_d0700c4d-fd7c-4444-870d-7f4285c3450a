<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TSettleStatementMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TSettleStatement">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="settle_statement_code" jdbcType="VARCHAR" property="settleStatementCode" />
    <result column="settle_statement_type" jdbcType="INTEGER" property="settleStatementType" />
    <result column="settle_statement_name" jdbcType="VARCHAR" property="settleStatementName" />
    <result column="settle_statement_month" jdbcType="VARCHAR" property="settleStatementMonth" />
    <result column="company_role" jdbcType="INTEGER" property="companyRole" />
    <result column="other_fee_tax_point" jdbcType="DECIMAL" property="otherFeeTaxPoint" />
    <result column="freight_tax_point" jdbcType="DECIMAL" property="freightTaxPoint" />
    <result column="apply_total_fee" jdbcType="DECIMAL" property="applyTotalFee" />
    <result column="adjust_fee" jdbcType="DECIMAL" property="adjustFee" />
    <result column="adjust_remark" jdbcType="VARCHAR" property="adjustRemark" />
    <result column="settle_statement_status" jdbcType="INTEGER" property="settleStatementStatus" />
    <result column="audit_name" jdbcType="VARCHAR" property="auditName" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="platform_company_id" jdbcType="BIGINT" property="platformCompanyId" />
    <result column="platform_company_name" jdbcType="VARCHAR" property="platformCompanyName" />
    <result column="contract_code" jdbcType="VARCHAR" property="contractCode" />
    <result column="if_invoice" jdbcType="INTEGER" property="ifInvoice" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, settle_statement_code, settle_statement_type, settle_statement_name, settle_statement_month, 
    company_role, other_fee_tax_point, freight_tax_point, apply_total_fee, adjust_fee, 
    adjust_remark, settle_statement_status, audit_name, audit_time, platform_company_id, 
    platform_company_name, contract_code, if_invoice, remark, created_by, created_time, last_modified_by,
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_settle_statement
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_settle_statement
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TSettleStatement">
    insert into t_settle_statement (id, settle_statement_code, settle_statement_type, 
      settle_statement_name, settle_statement_month, 
      company_role, other_fee_tax_point, freight_tax_point, 
      apply_total_fee, adjust_fee, adjust_remark, 
      settle_statement_status, audit_name, audit_time, 
      platform_company_id, platform_company_name, 
      contract_code, if_invoice, remark, created_by,
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{settleStatementCode,jdbcType=VARCHAR}, #{settleStatementType,jdbcType=INTEGER}, 
      #{settleStatementName,jdbcType=VARCHAR}, #{settleStatementMonth,jdbcType=VARCHAR}, 
      #{companyRole,jdbcType=INTEGER}, #{otherFeeTaxPoint,jdbcType=DECIMAL}, #{freightTaxPoint,jdbcType=DECIMAL}, 
      #{applyTotalFee,jdbcType=DECIMAL}, #{adjustFee,jdbcType=DECIMAL}, #{adjustRemark,jdbcType=VARCHAR}, 
      #{settleStatementStatus,jdbcType=INTEGER}, #{auditName,jdbcType=VARCHAR}, #{auditTime,jdbcType=TIMESTAMP}, 
      #{platformCompanyId,jdbcType=BIGINT}, #{platformCompanyName,jdbcType=VARCHAR}, 
      #{contractCode,jdbcType=VARCHAR}, #{ifInvoice,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TSettleStatement" keyProperty="id" useGeneratedKeys="true">
    insert into t_settle_statement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="settleStatementCode != null">
        settle_statement_code,
      </if>
      <if test="settleStatementType != null">
        settle_statement_type,
      </if>
      <if test="settleStatementName != null">
        settle_statement_name,
      </if>
      <if test="settleStatementMonth != null">
        settle_statement_month,
      </if>
      <if test="companyRole != null">
        company_role,
      </if>
      <if test="otherFeeTaxPoint != null">
        other_fee_tax_point,
      </if>
      <if test="freightTaxPoint != null">
        freight_tax_point,
      </if>
      <if test="applyTotalFee != null">
        apply_total_fee,
      </if>
      <if test="adjustFee != null">
        adjust_fee,
      </if>
      <if test="adjustRemark != null">
        adjust_remark,
      </if>
      <if test="settleStatementStatus != null">
        settle_statement_status,
      </if>
      <if test="auditName != null">
        audit_name,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="platformCompanyId != null">
        platform_company_id,
      </if>
      <if test="platformCompanyName != null">
        platform_company_name,
      </if>
      <if test="contractCode != null">
        contract_code,
      </if>
      <if test="ifInvoice != null">
        if_invoice,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="settleStatementCode != null">
        #{settleStatementCode,jdbcType=VARCHAR},
      </if>
      <if test="settleStatementType != null">
        #{settleStatementType,jdbcType=INTEGER},
      </if>
      <if test="settleStatementName != null">
        #{settleStatementName,jdbcType=VARCHAR},
      </if>
      <if test="settleStatementMonth != null">
        #{settleStatementMonth,jdbcType=VARCHAR},
      </if>
      <if test="companyRole != null">
        #{companyRole,jdbcType=INTEGER},
      </if>
      <if test="otherFeeTaxPoint != null">
        #{otherFeeTaxPoint,jdbcType=DECIMAL},
      </if>
      <if test="freightTaxPoint != null">
        #{freightTaxPoint,jdbcType=DECIMAL},
      </if>
      <if test="applyTotalFee != null">
        #{applyTotalFee,jdbcType=DECIMAL},
      </if>
      <if test="adjustFee != null">
        #{adjustFee,jdbcType=DECIMAL},
      </if>
      <if test="adjustRemark != null">
        #{adjustRemark,jdbcType=VARCHAR},
      </if>
      <if test="settleStatementStatus != null">
        #{settleStatementStatus,jdbcType=INTEGER},
      </if>
      <if test="auditName != null">
        #{auditName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="platformCompanyId != null">
        #{platformCompanyId,jdbcType=BIGINT},
      </if>
      <if test="platformCompanyName != null">
        #{platformCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="contractCode != null">
        #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="ifInvoice != null">
        #{ifInvoice,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TSettleStatement">
    update t_settle_statement
    <set>
      <if test="settleStatementCode != null">
        settle_statement_code = #{settleStatementCode,jdbcType=VARCHAR},
      </if>
      <if test="settleStatementType != null">
        settle_statement_type = #{settleStatementType,jdbcType=INTEGER},
      </if>
      <if test="settleStatementName != null">
        settle_statement_name = #{settleStatementName,jdbcType=VARCHAR},
      </if>
      <if test="settleStatementMonth != null">
        settle_statement_month = #{settleStatementMonth,jdbcType=VARCHAR},
      </if>
      <if test="companyRole != null">
        company_role = #{companyRole,jdbcType=INTEGER},
      </if>
      <if test="otherFeeTaxPoint != null">
        other_fee_tax_point = #{otherFeeTaxPoint,jdbcType=DECIMAL},
      </if>
      <if test="freightTaxPoint != null">
        freight_tax_point = #{freightTaxPoint,jdbcType=DECIMAL},
      </if>
      <if test="applyTotalFee != null">
        apply_total_fee = #{applyTotalFee,jdbcType=DECIMAL},
      </if>
      <if test="adjustFee != null">
        adjust_fee = #{adjustFee,jdbcType=DECIMAL},
      </if>
      <if test="adjustRemark != null">
        adjust_remark = #{adjustRemark,jdbcType=VARCHAR},
      </if>
      <if test="settleStatementStatus != null">
        settle_statement_status = #{settleStatementStatus,jdbcType=INTEGER},
      </if>
      <if test="auditName != null">
        audit_name = #{auditName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="platformCompanyId != null">
        platform_company_id = #{platformCompanyId,jdbcType=BIGINT},
      </if>
      <if test="platformCompanyName != null">
        platform_company_name = #{platformCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="contractCode != null">
        contract_code = #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="ifInvoice != null">
        if_invoice = #{ifInvoice,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TSettleStatement">
    update t_settle_statement
    set settle_statement_code = #{settleStatementCode,jdbcType=VARCHAR},
      settle_statement_type = #{settleStatementType,jdbcType=INTEGER},
      settle_statement_name = #{settleStatementName,jdbcType=VARCHAR},
      settle_statement_month = #{settleStatementMonth,jdbcType=VARCHAR},
      company_role = #{companyRole,jdbcType=INTEGER},
      other_fee_tax_point = #{otherFeeTaxPoint,jdbcType=DECIMAL},
      freight_tax_point = #{freightTaxPoint,jdbcType=DECIMAL},
      apply_total_fee = #{applyTotalFee,jdbcType=DECIMAL},
      adjust_fee = #{adjustFee,jdbcType=DECIMAL},
      adjust_remark = #{adjustRemark,jdbcType=VARCHAR},
      settle_statement_status = #{settleStatementStatus,jdbcType=INTEGER},
      audit_name = #{auditName,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      platform_company_id = #{platformCompanyId,jdbcType=BIGINT},
      platform_company_name = #{platformCompanyName,jdbcType=VARCHAR},
      contract_code = #{contractCode,jdbcType=VARCHAR},
      if_invoice = #{ifInvoice,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>