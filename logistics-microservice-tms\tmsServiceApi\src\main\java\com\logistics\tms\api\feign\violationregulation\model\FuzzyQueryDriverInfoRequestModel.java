package com.logistics.tms.api.feign.violationregulation.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/6/3 11:15
 */
@Data
public class FuzzyQueryDriverInfoRequestModel  extends AbstractPageForm<FuzzyQueryDriverInfoRequestModel> {
    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private String type;
    @ApiModelProperty("模糊匹配-可根据司机名称/手机号")
    private String fuzzyDriverField;
    @ApiModelProperty("启用 1，禁用 0")
    private Integer enabled;
}
