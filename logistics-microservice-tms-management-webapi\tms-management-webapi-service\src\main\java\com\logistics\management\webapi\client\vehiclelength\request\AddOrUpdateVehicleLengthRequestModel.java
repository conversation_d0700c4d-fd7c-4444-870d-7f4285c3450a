package com.logistics.management.webapi.client.vehiclelength.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AddOrUpdateVehicleLengthRequestModel {

    /**
     * 车长配置id
     */
    @ApiModelProperty("车长配置id")
    private Long vehicleLengthId;

    /**
     * 车长
     */
    @ApiModelProperty("车长")
    private BigDecimal vehicleLength;

    /**
     * 承运范围-低
     */
    @ApiModelProperty("承运范围-低")
    private BigDecimal carriageScopeMin;

    /**
     * 承运范围-高
     */
    @ApiModelProperty("承运范围-高")
    private BigDecimal carriageScopeMax;

}
