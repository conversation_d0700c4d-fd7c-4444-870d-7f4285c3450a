package com.logistics.management.webapi.client.reservebalance;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.reservebalance.hystrix.DriverReserveBalanceClientHystrix;
import com.logistics.management.webapi.client.reservebalance.model.request.DriverReserveBalanceListRequestModel;
import com.logistics.management.webapi.client.reservebalance.model.request.ReserveBalanceDetailRequestModel;
import com.logistics.management.webapi.client.reservebalance.model.response.DriverReserveBalanceListResponseModel;
import com.logistics.management.webapi.client.reservebalance.model.response.ReserveBalanceDetailResponseModel;
import com.logistics.management.webapi.client.reservebalance.model.response.SearchDriverReserveBalanceResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@Api(value = "司机备用金余额台账")
@FeignClient(name = FeignClientName.TMS_SERVICES,
        fallback = DriverReserveBalanceClientHystrix.class,
        path = "/service/DriverReserveBalance")
public interface DriverReserveBalanceClient {

    @ApiOperation(value = "备用金余额台账搜索")
    @PostMapping("/searchList")
    Result<SearchDriverReserveBalanceResponseModel> searchList(@RequestBody DriverReserveBalanceListRequestModel requestModel);

    @ApiOperation(value = "备用金余额台账搜索导出")
    @PostMapping("/searchListExport")
    Result<List<DriverReserveBalanceListResponseModel>> searchListExport(@RequestBody DriverReserveBalanceListRequestModel requestModel);

    @ApiOperation(value = "备用金余额台账明细查询")
    @PostMapping("/reserveBalanceDetail")
    Result<PageInfo<ReserveBalanceDetailResponseModel>> reserveBalanceDetail(@RequestBody ReserveBalanceDetailRequestModel requestModel);

    @ApiOperation(value = "备用金余额台账明细导出")
    @PostMapping("/reserveBalanceDetailExport")
    Result<Map<String, List<ReserveBalanceDetailResponseModel>>> reserveBalanceDetailExport(@RequestBody ReserveBalanceDetailRequestModel requestModel);
}
