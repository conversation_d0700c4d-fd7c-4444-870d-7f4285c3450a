<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierFreightConfigSchemeMapper">

  <select id="selectConfigSchemeByFreightConfigId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from t_carrier_freight_config_scheme
    where valid = 1
    <if test="freightConfigId != null">
      and freight_config_id = #{freightConfigId}
    </if>
  </select>

  <insert id="insertGeneratedKey" parameterType="com.logistics.tms.entity.TCarrierFreightConfigScheme" keyProperty="id" useGeneratedKeys="true">
    insert into t_carrier_freight_config_scheme
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="freightConfigId != null" >
        freight_config_id,
      </if>
      <if test="schemeType != null" >
        scheme_type,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="freightConfigId != null" >
        #{freightConfigId,jdbcType=BIGINT},
      </if>
      <if test="schemeType != null" >
        #{schemeType,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <update id="deleteNotExistBySchemeTypeAndIds">
    update t_carrier_freight_config_scheme set valid = 0, last_modified_time = now()
    <if test="lastModifiedBy != null" >
      , last_modified_by = #{lastModifiedBy}
    </if>
    where valid = 1
    and freight_config_id = #{freightConfigId}
    <if test="schemeTypes != null and schemeTypes.size > 0">
      and scheme_type in
      <foreach collection="schemeTypes" item="schemeType" open="(" separator="," close=")">
        #{schemeType}
      </foreach>
    </if>
    <if test="ids != null and ids.size > 0">
      and id not in
      <foreach collection="ids" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
  </update>

  <select id="selectByCarrierFreightIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_carrier_freight_config_scheme
    where valid = 1
    <choose>
      <when test="list != null and list.size() != 0">
        and  freight_config_id in
        <foreach collection="list" open="(" separator="," close=")" item="item">
          #{item,jdbcType=BIGINT}
        </foreach>
      </when>
      <otherwise>
        and false
      </otherwise>
    </choose>
  </select>

  <select id="selectByFreightConfigIdAndSchemetype" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_carrier_freight_config_scheme
    where valid = 1
    and freight_config_id = #{freightConfigId,jdbcType=BIGINT}
    and scheme_type = #{schemeType,jdbcType=INTEGER}
  </select>
</mapper>