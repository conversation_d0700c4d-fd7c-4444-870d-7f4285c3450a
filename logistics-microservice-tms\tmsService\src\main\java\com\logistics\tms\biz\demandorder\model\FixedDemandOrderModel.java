package com.logistics.tms.biz.demandorder.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/22
 */
@Data
public class FixedDemandOrderModel {

	@ApiModelProperty("需求单id")
	private Long demandId;

	@ApiModelProperty("需求单code")
	private String demandOrderCode;

	@ApiModelProperty("固定需求唯一code")
	private String fixedDemand;

	//以下数据从大数据接口获取
	@ApiModelProperty("车辆id")
	private Long vehicleId;

	@ApiModelProperty("车主id")
	private Long companyCarrierId;
}
