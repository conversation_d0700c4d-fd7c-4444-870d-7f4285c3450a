package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2019/10/8 14:13
 */
public enum CooperationStatusEnum {
    NO_START(1,"已预付"),
    VALID(2,"进行中"),
    TERMINAL(3,"已终止"),
    ;
    private Integer key;
    private String value;

    CooperationStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
