package com.logistics.management.webapi.api.feign.bank;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.bank.dto.*;
import com.logistics.management.webapi.api.feign.bank.hystirx.BankManagementApiHystrix;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/7/10 13:53
 */
@Api(value = "API - BankManagementApiImpl - 银行名称配置")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = BankManagementApiHystrix.class)
public interface BankManagementApi {

    @ApiOperation(value = "银行列表")
    @PostMapping(value = "/api/bank/bankList")
    Result<PageInfo<SearchBankResponseDto>> searchBankList(@RequestBody SearchBankRequestDto requestDto);

    @ApiOperation(value = "银行新增修改")
    @PostMapping(value = "/api/bank/saveBank")
    Result<Boolean> saveOrModifyBank(@RequestBody @Valid SaveOrModifyBankRequestDto requestDto);

    @ApiOperation(value = "查看详情")
    @PostMapping(value = "/api/bank/getDetail")
    Result<BankDetailResponseDto> getDetail(@RequestBody @Valid BankDetailRequestDto requestDto);

    @ApiOperation(value = "启用/禁用银行信息")
    @PostMapping(value = "/api/bank/enable")
    Result<Boolean> enableOrDisable(@RequestBody @Valid EnableBankRequestDto requestDto);

    @ApiOperation(value = "导出")
    @GetMapping(value = "/api/bank/export")
    void export(SearchBankRequestDto requestDto, HttpServletResponse response);

    @ApiOperation(value = "导入")
    @PostMapping(value = "/api/bank/import")
    Result<ImportBankResponseDto> importBank(@RequestParam("file") MultipartFile file, HttpServletRequest request);

    @ApiOperation(value = "根据名称模糊匹配银行信息")
    @PostMapping(value = "/api/bank/fuzzyQuery")
    Result<List<FuzzyQueryBankListResponseDto>> fuzzyQueryBank(@RequestBody FuzzyQueryBankRequestDto requestDto);

}
