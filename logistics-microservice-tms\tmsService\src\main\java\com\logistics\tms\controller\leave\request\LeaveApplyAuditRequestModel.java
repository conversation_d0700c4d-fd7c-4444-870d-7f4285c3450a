package com.logistics.tms.controller.leave.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class LeaveApplyAuditRequestModel {

    @ApiModelProperty(value = "请假申请ID", required = true)
    private Long leaveApplyId;

    //审核类型: 1:通过 ,2:驳
    @ApiModelProperty(value = "审核类型: 1:通过 ,2:驳回", required = true)
    private Integer auditType;

    //审核备注,驳回时必填
    @ApiModelProperty(value = "审核备注,驳回时必填,1-100个字符")
    private String remark;
}
