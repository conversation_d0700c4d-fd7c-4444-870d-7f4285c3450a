package com.logistics.management.webapi.client.routeenquiry.request;

import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/7/9 17:16
 */
@Data
public class CreateRouteEnquiryAddressListRequestModel {

    //发货地
    /**
     * 发货地-仓库
     */
    private String fromWarehouse;

    /**
     * 发货地-省
     */
    private Long fromProvinceId;
    /**
     * 发货地-省
     */
    private String fromProvinceName;

    /**
     * 发货地-市
     */
    private Long fromCityId;
    /**
     * 发货地-市
     */
    private String fromCityName;

    /**
     * 发货地-区
     */
    private Long fromAreaId;
    /**
     * 发货地-区
     */
    private String fromAreaName;


    //收货地
    /**
     * 收货地-省
     */
    private Long toProvinceId;
    /**
     * 收货地-省
     */
    private String toProvinceName;

    /**
     * 收货地-市
     */
    private Long toCityId;
    /**
     * 收货地-市
     */
    private String toCityName;

    /**
     * 收货地-区
     */
    private Long toAreaId;
    /**
     * 收货地-区
     */
    private String toAreaName;

}
