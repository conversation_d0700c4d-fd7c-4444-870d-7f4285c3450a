package com.logistics.management.webapi.controller.reserveapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Data
public class ReserveApplyAuditRequestDto {

	@ApiModelProperty(value = "申请记录id", required = true)
	@NotBlank(message = "请选择要查看的记录")
	private String applyId;

	@ApiModelProperty(value = "操作人员类型 1:业务审核 2:财务审核", required = true)
	@NotBlank(message = "请选择操作人员类型")
	@Range(min = 1, max = 2, message = "操作人员类型只能为1-2")
	private String auditorType;

	@ApiModelProperty(value = "备用金余额")
	private String reserveBalance;

	@ApiModelProperty(value = "操作类型 1:通过 2:驳回", required = true)
	@NotBlank(message = "请选择审核结果")
	@Range(min = 1, max = 2, message = "审核结果只能为1-2")
	private String auditResult;

	@ApiModelProperty(value = "审核备注,驳回必填")
	@Length(max = 100 , message = "审核备注长度范围在1-100字符")
	private String remark;

	@ApiModelProperty(value = "业务批准金额,业务审核必填", required = true)
	private String approveAmount;
}
