package com.logistics.tms.biz.dispatch

import com.logistics.tms.biz.carrierorder.CarrierOrderBiz
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.biz.demandorder.DemandOrderBiz
import com.logistics.tms.biz.staff.StaffManagementBiz
import com.logistics.tms.biz.staffvehiclerelation.StaffVehicleBiz
import com.logistics.tms.biz.vehicleassetmanagement.VehicleAssetManagementBiz
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel
import com.logistics.tms.controller.carrierorder.response.DemandCarrierOrderRecursiveModel
import com.logistics.tms.controller.companycarrier.response.CompanyCarrierDetailResponseModel
import com.logistics.tms.controller.demandorder.response.DemandOrderSearchByIdsResponseModel
import com.logistics.tms.controller.dispatch.request.CompleteDemandOrderRequestModel
import com.logistics.tms.controller.dispatch.request.DemandOrderDispatchRequestModel
import com.logistics.tms.controller.dispatch.request.DispatchRequestModel
import com.logistics.tms.controller.dispatch.request.DriverAndVehicleSearchRequestModel
import com.logistics.tms.controller.dispatch.response.DemandOrderDispatchResponseModel
import com.logistics.tms.controller.dispatch.response.DriverAndVehicleSearchResponseModel
import com.logistics.tms.controller.dispatch.response.DriverIdVehicleIdRelationIdModel
import com.logistics.tms.controller.staffvehiclerelation.response.StaffAndVehicleSearchResponseModel
import com.logistics.tms.entity.*
import com.logistics.tms.mapper.*
import com.yelo.tools.rabbitmq.producer.RabbitMqSender
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class DispatchBizTest extends Specification {
    @Mock
    TCarrierOrderMapper tCarrierOrderMapper
    @Mock
    TDispatchOrderMapper dispatchOrderMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TCarrierOrderAddressMapper carrierOrderAddressMapper
    @Mock
    TCarrierOrderGoodsMapper carrierOrderGoodsMapper
    @Mock
    TCarrierOrderEventsMapper carrierOrderEventsMapper
    @Mock
    TCarrierOrderOperateLogsMapper carrierOrderOperateLogsMapper
    @Mock
    TCarrierOrderVehicleHistoryMapper carrierOrderVehicleHistoryMapper
    @Mock
    TVehicleTransportLineMapper vehicleTransportLineMapper
    @Mock
    TCarrierOrderOrderRelMapper carrierOrderOrderRelMapper
    @Mock
    TStaffBasicMapper tStaffBasicMapper
    @Mock
    TVehicleBasicMapper tVehicleBasicMapper
    @Mock
    StaffManagementBiz staffManagementBiz
    @Mock
    VehicleAssetManagementBiz vehicleAssetManagementBiz
    @Mock
    DemandOrderBiz demandOrderBiz
    @Mock
    TDemandOrderMapper demandOrderMapper
    @Mock
    TDemandOrderGoodsMapper demandOrderGoodsMapper
    @Mock
    CarrierOrderBiz carrierOrderBiz
    @Mock
    TDemandOrderOperateLogsMapper tDemandOrderOperateLogsMapper
    @Mock
    TDemandOrderEventsMapper tDemandOrderEventsMapper
    @Mock
    StaffVehicleBiz staffVehicleBiz
    @Mock
    TStaffVehicleRelationMapper tStaffVehicleRelationMapper
    @Mock
    TCarrierOrderWxMapper tCarrierOrderWxMapper
    @Mock
    TVehicleTypeMapper tVehicleTypeMapper
    @Mock
    TCarrierVehicleRelationMapper tCarrierVehicleRelationMapper
    @Mock
    RabbitMqSender rabbitMqSender
    @Mock
    TDemandOrderOrderRelMapper tDemandOrderOrderRelMapper
    @Mock
    TDemandOrderGoodsRelMapper tDemandOrderGoodsRelMapper
    @Mock
    TCompanyCarrierMapper tCompanyCarrierMapper
    @Mock
    TDemandOrderObjectionMapper tDemandOrderObjectionMapper
    @Mock
    Logger log
    @InjectMocks
    DispatchBiz dispatchBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "dispatch Vehicle where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(commonBiz.getBusinessTypeCode(any(), anyString(), anyString())).thenReturn("getBusinessTypeCodeResponse")
        when(commonBiz.differentDays(any(), any())).thenReturn(0)
        when(carrierOrderAddressMapper.batchInsertSelective(any())).thenReturn(0)
        when(carrierOrderGoodsMapper.batchInsertSelective(any())).thenReturn(0)
        when(carrierOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)
        when(carrierOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(carrierOrderVehicleHistoryMapper.batchInsertSelective(any())).thenReturn(0)
        when(vehicleTransportLineMapper.getLineByVehicleId(any())).thenReturn(new TVehicleTransportLine(vehicleId: 1l, vehicleNo: "vehicleNo", loadProvinceId: 1l, loadProvinceName: "loadProvinceName", loadCityId: 1l, loadCityName: "loadCityName", loadAreaId: 1l, loadAreaName: "loadAreaName", loadDetailAddress: "loadDetailAddress", loadWarehouse: "loadWarehouse", unloadProvinceId: 1l, unloadProvinceName: "unloadProvinceName", unloadCityId: 1l, unloadCityName: "unloadCityName", unloadAreaId: 1l, unloadAreaName: "unloadAreaName", unloadDetailAddress: "unloadDetailAddress", unloadWarehouse: "unloadWarehouse"))
        when(carrierOrderOrderRelMapper.batchInsertSelective(any())).thenReturn(0)
        when(tStaffBasicMapper.getByMobile(anyString())).thenReturn(new TStaffBasic(staffProperty: 0, name: "name", identityNumber: "identityNumber", openStatus: 0))
        when(tVehicleBasicMapper.getInfoByVehicleNo(anyString())).thenReturn(new TVehicleBasic(vehicleProperty: 0, operatingState: 0))
        when(tVehicleBasicMapper.getVehicleBasicPropertyById(anyLong())).thenReturn(new VehicleBasicPropertyModel())
        when(staffManagementBiz.addOrModifyStaff(any())).thenReturn(1l)
        when(vehicleAssetManagementBiz.saveVehicleInfo(any())).thenReturn(1l)
        when(demandOrderMapper.getDemandOrderGoodsAndRelByIds(any())).thenReturn([new DemandOrderSearchByIdsResponseModel()])
        when(demandOrderMapper.getDemandOrderAddressEventByIds(any())).thenReturn([new DemandOrderSearchByIdsResponseModel()])
        when(tStaffVehicleRelationMapper.getByVehicleIdAndDriverId(anyLong(), anyLong())).thenReturn(new TStaffVehicleRelation(staffId: 1l, vehicleId: 1l))
        when(tCarrierOrderWxMapper.batchInsert(any())).thenReturn(0)
        when(tVehicleTypeMapper.selectListByTypeAndCategory(anyString(), anyInt())).thenReturn(new TVehicleType())
        when(tCarrierVehicleRelationMapper.getByVehicleId(anyLong())).thenReturn(new TCarrierVehicleRelation(companyCarrierId: 1l))
        when(tCompanyCarrierMapper.getCompanyCarrierDetailById(anyLong())).thenReturn(new CompanyCarrierDetailResponseModel())

        expect:
        dispatchBiz.dispatchVehicle(requestModel) == expectedResult

        where:
        requestModel               || expectedResult
    }

    @Unroll
    def "verify Driver And Vehicle where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tStaffBasicMapper.getByMobile(anyString())).thenReturn(new TStaffBasic(staffProperty: 0, name: "name", identityNumber: "identityNumber", openStatus: 0))
        when(tVehicleBasicMapper.getInfoByVehicleNo(anyString())).thenReturn(new TVehicleBasic(vehicleProperty: 0))
        when(tVehicleBasicMapper.getVehicleBasicPropertyById(anyLong())).thenReturn(new VehicleBasicPropertyModel())
        when(staffManagementBiz.addOrModifyStaff(any())).thenReturn(1l)
        when(vehicleAssetManagementBiz.saveVehicleInfo(any())).thenReturn(1l)
        when(tStaffVehicleRelationMapper.getByVehicleIdAndDriverId(anyLong(), anyLong())).thenReturn(new TStaffVehicleRelation(staffId: 1l, vehicleId: 1l))
        when(tVehicleTypeMapper.selectListByTypeAndCategory(anyString(), anyInt())).thenReturn(new TVehicleType())

        expect:
        dispatchBiz.verifyDriverAndVehicle(requestModel) == expectedResult

        where:
        requestModel || expectedResult
        null         || new DriverIdVehicleIdRelationIdModel()
    }

    @Unroll
    def "get Driver And Vehicle By Vehicle Number where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tStaffVehicleRelationMapper.getDispatchVehicleListByVehicleNo(any())).thenReturn([new StaffAndVehicleSearchResponseModel()])

        expect:
        dispatchBiz.getDriverAndVehicleByVehicleNumber(requestModel) == expectedResult

        where:
        requestModel                             || expectedResult
        new DriverAndVehicleSearchRequestModel() || [new DriverAndVehicleSearchResponseModel()]
    }

    @Unroll
    def "get Dispatch Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(demandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(status: 0, ifCancel: 0, demandOrderCode: "demandOrderCode", goodsUnit: 0, entrustType: 0, ifEmpty: 0, ifObjectionSinopec: 0)])
        when(demandOrderGoodsMapper.getDemandOrderGoodsByDemandIds(anyString())).thenReturn([new DemandOrderDispatchResponseModel()])

        expect:
        dispatchBiz.getDispatchDetail(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new DemandOrderDispatchRequestModel() || [new DemandOrderDispatchResponseModel()]
    }

    @Unroll
    def "save Complete Dispatch where requestModel=#requestModel"() {
        given:
        when(commonBiz.differentDays(any(), any())).thenReturn(0)
        when(demandOrderBiz.getDemandOrderOperateLogs(anyLong(), any(), anyString(), anyString())).thenReturn(new TDemandOrderOperateLogs())
        when(demandOrderBiz.getDemandOrderEvent(anyLong(), any(), anyString())).thenReturn(new TDemandOrderEvents())
        when(demandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(entrustStatus: 0, statusUpdateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 16).getTime(), status: 0, ifCancel: 0, source: 0, demandOrderCode: "demandOrderCode", publishTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 16).getTime(), notArrangedAmount: 0 as BigDecimal, backAmount: 0 as BigDecimal, differenceAmount: 0 as BigDecimal, goodsUnit: 0, entrustType: 0, ifUrgent: 0, dispatchValidity: 0, ifOverdue: 0, ifEmpty: 0)])
        when(demandOrderMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(demandOrderGoodsMapper.getDemandOrderGoodsByDemandOrderIds(anyString())).thenReturn([new TDemandOrderGoods(demandOrderId: 1l, goodsName: "goodsName", categoryName: "categoryName", length: 0, width: 0, height: 0, arrangedAmount: 0 as BigDecimal, notArrangedAmount: 0 as BigDecimal, backAmount: 0 as BigDecimal)])
        when(demandOrderGoodsMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(carrierOrderBiz.getByDemandOrderIds(any())).thenReturn([new DemandCarrierOrderRecursiveModel()])
        when(tDemandOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(tDemandOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)
        when(tDemandOrderOrderRelMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(tDemandOrderOrderRelMapper.getDemandOrderOrderRelByDemandIds(anyString())).thenReturn([new TDemandOrderOrderRel(demandOrderId: 1l, orderId: 1l, totalAmount: 0 as java.math.BigDecimal, arrangedAmount: 0 as java.math.BigDecimal, backAmount: 0 as java.math.BigDecimal)])
        when(tDemandOrderGoodsRelMapper.getByDemandOrderIds(anyString())).thenReturn([new com.logistics.tms.entity.TDemandOrderGoodsRel(demandOrderGoodsId: 1l, bookingOrderGoodsId: 1l)])
        when(tDemandOrderObjectionMapper.batchInsert(any())).thenReturn(0)
        when(tDemandOrderObjectionMapper.batchUpdate(any())).thenReturn(0)
        when(tDemandOrderObjectionMapper.getByDemandOrderIds(anyString())).thenReturn([new com.logistics.tms.entity.TDemandOrderObjection(demandOrderId: 1l, customerName: "customerName", objectionType: 0, objectionReason: "objectionReason", reportContactName: "reportContactName", reportTime: new java.util.GregorianCalendar(2022, java.util.Calendar.JUNE, 16, 11, 16).getTime())])

        expect:
        dispatchBiz.saveCompleteDispatch(requestModel)
        assert expectedResult == false

        where:
        requestModel                                                                     || expectedResult
        new CompleteDemandOrderRequestModel() || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme