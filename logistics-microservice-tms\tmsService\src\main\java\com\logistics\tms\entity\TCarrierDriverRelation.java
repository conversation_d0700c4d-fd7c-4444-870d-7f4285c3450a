package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/10/20
*/
@Data
public class TCarrierDriverRelation extends BaseEntity {
    /**
    * 车主id
    */
    @ApiModelProperty("车主id")
    private Long companyCarrierId;

    /**
    * 司机id
    */
    @ApiModelProperty("司机id")
    private Long driverId;

    /**
    * 启用 1 禁用 0
    */
    @ApiModelProperty("启用 1 禁用 0")
    private Integer enabled;
}