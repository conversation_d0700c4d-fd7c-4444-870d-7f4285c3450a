package com.logistics.tms.biz.demandorder.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SyncTMSDemandOrderGoodsModel {
    private Long goodsId;
    @ApiModelProperty("sku code")
    private String skuCode;
    private String categoryName;
    private String goodsName;
    private Integer length;
    private Integer width;
    private Integer height;
    private BigDecimal goodsAmount;
}
