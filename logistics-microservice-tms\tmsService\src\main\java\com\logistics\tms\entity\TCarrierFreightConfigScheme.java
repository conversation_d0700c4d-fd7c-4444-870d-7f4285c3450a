package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2023/06/30
*/
@Data
public class TCarrierFreightConfigScheme extends BaseEntity {
    /**
    * 运价配置Id
    */
    @ApiModelProperty("运价配置Id")
    private Long freightConfigId;

    /**
    * 方案类型; 100: 路线配置; 200:同区-跨区价格; 201: 同区价格; 202: 跨区价格; 301:系统计算预计距离; 302:系统配置距离
    */
    @ApiModelProperty("方案类型; 100: 路线配置; 200:同区-跨区价格; 201: 同区价格; 202: 跨区价格; 301:系统计算预计距离; 302:系统配置距离")
    private Integer schemeType;
}