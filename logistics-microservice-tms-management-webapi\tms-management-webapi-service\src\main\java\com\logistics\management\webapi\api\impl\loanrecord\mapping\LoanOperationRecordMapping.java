package com.logistics.management.webapi.api.impl.loanrecord.mapping;

import com.logistics.management.webapi.api.feign.loanrecord.dto.LoanOperationRecordResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.tms.api.feign.loanrecord.model.LoanOperationRecordResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @Author: sj
 * @Date: 2019/10/8 19:48
 */
public class LoanOperationRecordMapping extends MapperMapping<LoanOperationRecordResponseModel,LoanOperationRecordResponseDto> {
    @Override
    public void configure() {
        LoanOperationRecordResponseModel source = this.getSource();
        LoanOperationRecordResponseDto dto = this.getDestination();
        if(source != null) {
            if(CommonConstant.ONE.equals(dto.getOperateType())){
                dto.setOperateTypeLabel("新增贷款记录");
            }else{
                dto.setOperateTypeLabel("修改贷款记录");
            }
        }
    }
}
