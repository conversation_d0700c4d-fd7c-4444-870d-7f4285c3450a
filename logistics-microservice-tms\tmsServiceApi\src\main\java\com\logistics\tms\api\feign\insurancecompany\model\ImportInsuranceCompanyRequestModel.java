package com.logistics.tms.api.feign.insurancecompany.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/5/29 18:53
 */
@Data
public class ImportInsuranceCompanyRequestModel {
    @ApiModelProperty("导入List")
    private List<InsuranceCompanyRequestModel> importList;
    @ApiModelProperty("失败数量")
    private Integer numberFailures;
    @ApiModelProperty("成功数量")
    private Integer numberSuccessful;
}
