package com.logistics.management.webapi.controller.freightconfig.mapping;

import com.logistics.management.webapi.base.enums.EnabledEnum;
import com.logistics.management.webapi.client.freightconfig.response.address.CarrierFreightConfigAddressItemResponseModel;
import com.logistics.management.webapi.controller.freightconfig.response.address.CarrierFreightConfigAddressItemResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.util.Optional;

public class CarrierFreightConfigAddressListMapping extends MapperMapping<CarrierFreightConfigAddressItemResponseModel, CarrierFreightConfigAddressItemResponseDto> {

    @Override
    public void configure() {
        CarrierFreightConfigAddressItemResponseModel source = getSource();
        CarrierFreightConfigAddressItemResponseDto destination = getDestination();

        destination.setEnabledLabel(EnabledEnum.getEnum(source.getEnabled()).getValue());
        destination.setFromCityName(Optional.ofNullable(source.getFromProvinceName()).orElse("")
                + Optional.ofNullable(source.getFromCityName()).orElse(""));
        destination.setFromAreaName(Optional.ofNullable(source.getFromAreaName()).orElse(""));
        destination.setToCityName(Optional.ofNullable(source.getToProvinceName()).orElse("")
                + Optional.ofNullable(source.getToCityName()).orElse(""));
        destination.setToAreaName(Optional.ofNullable(source.getToAreaName()).orElse(""));
    }
}
