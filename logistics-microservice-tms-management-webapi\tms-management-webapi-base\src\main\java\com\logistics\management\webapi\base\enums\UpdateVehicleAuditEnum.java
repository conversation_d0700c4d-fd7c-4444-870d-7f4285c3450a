package com.logistics.management.webapi.base.enums;

/**
 * <AUTHOR>
 * @createDate 2019-04-04 10:15
 */
public enum UpdateVehicleAuditEnum {
    NOT_AUDIT(0, "无需审核"),
    AUDIT(1, "需审核"),;

    private Integer key;
    private String value;

    UpdateVehicleAuditEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static UpdateVehicleAuditEnum getEnum(Integer key) {
        for (UpdateVehicleAuditEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
