package com.logistics.appapi.controller.workordercenter;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.AppApiExceptionEnum;
import com.logistics.appapi.base.enums.WorkOrderTypeEnum;
import com.logistics.appapi.base.enums.WorkReportSourceEnum;
import com.logistics.appapi.client.workordercenter.WorkOrderCenterClient;
import com.logistics.appapi.client.workordercenter.request.*;
import com.logistics.appapi.client.workordercenter.response.WorkOrderDetailAppletResponseModel;
import com.logistics.appapi.client.workordercenter.response.WorkOrderListAppletResponseModel;
import com.logistics.appapi.client.workordercenter.response.WorkOrderProcessAppletResponseModel;
import com.logistics.appapi.controller.workordercenter.mapping.WorkOrderDetailMapping;
import com.logistics.appapi.controller.workordercenter.mapping.WorkOrderListMapping;
import com.logistics.appapi.controller.workordercenter.mapping.WorkOrderProcessMapping;
import com.logistics.appapi.controller.workordercenter.request.CancelWorkOrderRequestDto;
import com.logistics.appapi.controller.workordercenter.request.ReportWorkExceptionRequestDto;
import com.logistics.appapi.controller.workordercenter.request.WorkOrderDetailRequestDto;
import com.logistics.appapi.controller.workordercenter.request.WorkOrderListRequestDto;
import com.logistics.appapi.controller.workordercenter.response.WorkOrderDetailResponseDto;
import com.logistics.appapi.controller.workordercenter.response.WorkOrderListResponseDto;
import com.logistics.appapi.controller.workordercenter.response.WorkOrderProcessResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/7 13:48
 */
@Api(value = "工单中心", tags = "工单中心")
@RestController
@RequestMapping(value = "/api/applet/workOrderCenter")
public class WorkOrderCenterController {

    @Resource
    private WorkOrderCenterClient workOrderCenterClient;

    /**
     * 工单列表
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "异常工单列表", tags = "1.1.2")
    @PostMapping(value = "/workOrderList")
    public Result<PageInfo<WorkOrderListResponseDto>> workOrderList(@RequestBody WorkOrderListRequestDto requestDto) {
        Result<PageInfo<WorkOrderListAppletResponseModel>> result = workOrderCenterClient.workOrderListForApplet(MapperUtils.mapperNoDefault(requestDto, WorkOrderListAppletRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        pageInfo.setList(MapperUtils.mapper(pageInfo.getList(), WorkOrderListResponseDto.class, new WorkOrderListMapping()));
        return Result.success(pageInfo);
    }

    /**
     * 工单详情
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "异常工单详情", tags = "1.1.2")
    @PostMapping(value = "/workOrderDetail")
    public Result<WorkOrderDetailResponseDto> workOrderDetail(@RequestBody @Valid WorkOrderDetailRequestDto requestDto) {
        Result<WorkOrderDetailAppletResponseModel> result = workOrderCenterClient.workOrderDetailForApplet(MapperUtils.mapperNoDefault(requestDto, WorkOrderDetailAppletRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), WorkOrderDetailResponseDto.class, new WorkOrderDetailMapping()));
    }

    /**
     * 工单处理过程列表
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "异常工单详情处理过程列表", tags = "1.1.2")
    @PostMapping(value = "/workOrderProcess")
    public Result<List<WorkOrderProcessResponseDto>> workOrderProcess(@RequestBody @Valid WorkOrderDetailRequestDto requestDto) {
        Result<List<WorkOrderProcessAppletResponseModel>> result = workOrderCenterClient.workOrderProcessForApplet(MapperUtils.mapperNoDefault(requestDto, WorkOrderDetailAppletRequestModel.class));
        result.throwException();
        List<WorkOrderProcessResponseDto> mapperResult = MapperUtils.mapper(result.getData(), WorkOrderProcessResponseDto.class, new WorkOrderProcessMapping());
        return Result.success(mapperResult);
    }

    /**
     * 上报任务中心异常工单
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "上报任务中心异常工单", tags = "1.1.2")
    @PostMapping(value = "/reportWorkException")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> reportWorkException(@RequestBody @Valid ReportWorkExceptionRequestDto requestDto) {
        //校验定位地址
        if (CommonConstant.ONE.equals(requestDto.getIsArriveScene())){
            if (StringUtils.isBlank(requestDto.getAddressHead()) || StringUtils.isBlank(requestDto.getAddressDetail())) {
                throw new BizException(AppApiExceptionEnum.LOCATION_IS_NLL);
            }
        }
        ReportWorkExceptionRequestModel requestModel = MapperUtils.mapper(requestDto, ReportWorkExceptionRequestModel.class);
        requestModel.setOrderId(Long.valueOf(requestDto.getCarrierOrderId()));
        requestModel.setReportSource(WorkReportSourceEnum.APPLET.getKey());
        requestModel.setWorkOrderType(WorkOrderTypeEnum.CARRIER_ORDER_TYPE.getKey());
        return workOrderCenterClient.reportWorkException(requestModel);
    }

    /**
     * 重新上报任务中心异常工单
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "重新上报任务中心异常工单", tags = "1.1.2")
    @PostMapping(value = "/reReportWorkException")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> reReportWorkException(@RequestBody @Valid ReportWorkExceptionRequestDto requestDto) {
        if (StringUtils.isBlank(requestDto.getWorkOrderId())) {
            throw new BizException(AppApiExceptionEnum.RE_REPORT_WORK_NOT_EMPTY);
        }
        ReReportWorkExceptionRequestModel requestModel = MapperUtils.mapper(requestDto, ReReportWorkExceptionRequestModel.class);
        requestModel.setOrderId(Long.valueOf(requestDto.getCarrierOrderId()));
        requestModel.setReportSource(WorkReportSourceEnum.APPLET.getKey());
        requestModel.setWorkOrderType(WorkOrderTypeEnum.CARRIER_ORDER_TYPE.getKey());
        return workOrderCenterClient.reReportWorkException(requestModel);
    }

    /**
     * 撤销工单
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "撤销工单", tags = "1.1.2")
    @PostMapping(value = "/cancelWorkOrder")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> cancelWorkOrder(@RequestBody @Valid CancelWorkOrderRequestDto requestDto) {
        CancelWorkOrderRequestModel requestModel = MapperUtils.mapperNoDefault(requestDto, CancelWorkOrderRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_THREE);
        Result<Boolean> result = workOrderCenterClient.cancelWorkOrder(requestModel);
        result.throwException();
        return result;
    }
}
