package com.logistics.management.webapi.api.impl.driversafepromise.mapping;

import com.logistics.management.webapi.api.feign.driversafepromise.dto.SignSafePromiseDetailResponseDto;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.tms.api.feign.driversafepromise.model.SignSafePromiseDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/11/6 19:06
 */
public class GetSignDetailMapping extends MapperMapping<SignSafePromiseDetailResponseModel,SignSafePromiseDetailResponseDto> {

    private ConfigKeyConstant configKeyConstant;
    private Map<String, String> imageMap;

    public GetSignDetailMapping(ConfigKeyConstant configKeyConstant,Map<String, String> imageMap){
        this.configKeyConstant =configKeyConstant;
        this.imageMap=imageMap;
    }
    @Override
    public void configure() {
        SignSafePromiseDetailResponseModel source = this.getSource();
        SignSafePromiseDetailResponseDto dto = this.getDestination();

        if(source != null){
          if(source.getHandPromiseUrl()!=null){
              dto.setAbsoluteHandPromiseUrl(configKeyConstant.fileAccessAddress+imageMap.get(source.getHandPromiseUrl()));
          }
          if(source.getSignResponsibilityUrl()!=null){
              dto.setAbsoluteSignResponsibilityUrl(configKeyConstant.fileAccessAddress+imageMap.get(source.getSignResponsibilityUrl()));
          }
        }
    }
}
