package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/10/11 16:52
 */
public class ExportParkingFeeInfo {
    private ExportParkingFeeInfo(){

    }
    private static final Map<String,String> EXPORT_PARKING_FEE_MAP;
    static{
	    EXPORT_PARKING_FEE_MAP = new LinkedHashMap<>();
	    EXPORT_PARKING_FEE_MAP.put("车牌号", "vehicleNo");
	    EXPORT_PARKING_FEE_MAP.put("车辆机构", "vehiclePropertyLabel");
	    EXPORT_PARKING_FEE_MAP.put("司机", "name");
	    EXPORT_PARKING_FEE_MAP.put("手机号", "mobile");
	    EXPORT_PARKING_FEE_MAP.put("停车费用", "parkingFeeLabel");
	    EXPORT_PARKING_FEE_MAP.put("合作公司", "cooperationCompany");
	    EXPORT_PARKING_FEE_MAP.put("合作起始日期", "startDate");
	    EXPORT_PARKING_FEE_MAP.put("合作截止日期", "endDate");
	    EXPORT_PARKING_FEE_MAP.put("备注", "remark");
	    EXPORT_PARKING_FEE_MAP.put("操作人", "lastModifiedBy");
	    EXPORT_PARKING_FEE_MAP.put("操作时间", "lastModifiedTime");
    }

    public static Map<String,String> getExportParkingFeeMap(){
        return EXPORT_PARKING_FEE_MAP;
    }
}
