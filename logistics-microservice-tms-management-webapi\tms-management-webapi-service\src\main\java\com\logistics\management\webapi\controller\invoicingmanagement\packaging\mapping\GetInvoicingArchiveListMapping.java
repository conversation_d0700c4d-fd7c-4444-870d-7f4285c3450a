package com.logistics.management.webapi.controller.invoicingmanagement.packaging.mapping;

import com.logistics.management.webapi.client.invoicingmanagement.response.GetInvoicingArchiveListResponseModel;
import com.logistics.management.webapi.controller.invoicingmanagement.packaging.response.GetInvoicingArchiveListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

import java.util.Map;

/**
 * @author: wjf
 * @date: 2024/7/2 9:17
 */
public class GetInvoicingArchiveListMapping extends MapperMapping<GetInvoicingArchiveListResponseModel, GetInvoicingArchiveListResponseDto> {

    private final String imagePrefix;
    private final Map<String, String> imageMap;

    public GetInvoicingArchiveListMapping(String imagePrefix, Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap = imageMap;
    }

    @Override
    public void configure() {
        GetInvoicingArchiveListResponseModel source = getSource();
        GetInvoicingArchiveListResponseDto destination = getDestination();

        if (StringUtils.isNotBlank(source.getRelativePath())){
            destination.setAbsolutePath(imagePrefix + imageMap.get(source.getRelativePath()));
        }
    }
}
