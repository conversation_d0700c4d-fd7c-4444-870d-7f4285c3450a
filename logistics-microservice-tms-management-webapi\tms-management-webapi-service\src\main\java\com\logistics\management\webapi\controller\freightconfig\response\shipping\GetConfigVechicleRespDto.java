package com.logistics.management.webapi.controller.freightconfig.response.shipping;

import com.logistics.management.webapi.controller.freightconfig.request.shipping.ConfigVehicleItemDto;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


@Data
public class GetConfigVechicleRespDto {


//    /**
//     * 主键id
//     */
//    private String shippingFreightId = "";


    /**
     * 发货地
     */
    private String loadAddress = "";

    /**
     * 收货地
     */
    private String unloadAddress = "";


    private List<GetConfigVechicleDetailRespDto> detailRespDtos = new ArrayList<>();

}
