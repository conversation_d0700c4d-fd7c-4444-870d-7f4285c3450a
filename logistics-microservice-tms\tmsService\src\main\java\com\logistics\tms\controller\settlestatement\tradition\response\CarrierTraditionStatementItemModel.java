package com.logistics.tms.controller.settlestatement.tradition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/17
 */
@Data
public class CarrierTraditionStatementItemModel {

	@ApiModelProperty("对账单表Id")
	private Long settleStatementItemId;

	@ApiModelProperty("对账单id")
	private Long settleStatementId;

	@ApiModelProperty("运单id")
	private Long carrierOrderId;

	@ApiModelProperty("结算数量")
	private BigDecimal settlementAmount;

	@ApiModelProperty("货物单位：1 件，2 吨")
	private Integer goodsUnit;

	@ApiModelProperty("委托费用/车主运费")
	private BigDecimal entrustFreight;

	@ApiModelProperty("应付费用")
	private BigDecimal payableFee;

	@ApiModelProperty("车主id")
	private Long companyCarrierId;

	@ApiModelProperty(value = "车主公司类型：1 公司，2 个人")
	private Integer companyCarrierType;

	@ApiModelProperty("车主公司名称")
	private String companyCarrierName;

	@ApiModelProperty("车主联系人")
	private String carrierContactName;

	@ApiModelProperty("车主联系人电话")
	private String carrierContactMobile;
}
